ControlBeastsPrizeDrawWGData = ControlBeastsPrizeDrawWGData or  BaseClass()

function ControlBeastsPrizeDrawWGData:__init()
	if ControlBeastsPrizeDrawWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[ControlBeastsPrizeDrawWGData] attempt to create singleton twice!")
		return
	end
	ControlBeastsPrizeDrawWGData.Instance = self
	self:InitCfg()
	RemindManager.Instance:Register(RemindName.BeastsPrizeDraw, BindTool.Bind(self.ShowBeastsPrizeDrawRemind, self)) 			-- 总红点

	self.is_play_draw = 0
end

function ControlBeastsPrizeDrawWGData:__delete()
	ControlBeastsPrizeDrawWGData.Instance = nil
	self:DeleteCfg()
	RemindManager.Instance:UnRegister(RemindName.BeastsPrizeDraw)

	self.is_play_draw = nil
end

function ControlBeastsPrizeDrawWGData:ShowBeastsPrizeDrawRemind()
	if not FunOpen.Instance:GetFunIsOpened(FunName.ControlBeastsPrizeDrawWGView) then
		return 0
	end

	if self:GetCurBeastDrawModeRed() or ControlBeastsOADrawWGData.Instance:GetOABeastDrawModeRed() > 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BEASTS_PRIZE_DRAW, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.ControlBeastsPrizeDrawWGView)
            return true
        end)
		return 1
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.BEASTS_PRIZE_DRAW, 0)
	return 0
end

function ControlBeastsPrizeDrawWGData:SortItem(item_list)
	local item_cfg, item_type
	for i, v in ipairs(item_list) do
		local is_beasts_item = ControlBeastsWGData.Instance:IsBeastsItem(v.item_id)
		local beast_cfg = {}

		if is_beasts_item then
			beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(v.item_id)
		else
			local chip_id_to_beasts_id = ControlBeastsWGData.Instance:GetBeastsChipIdToBeastsId(v.item_id)

			if chip_id_to_beasts_id then
				beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(chip_id_to_beasts_id)
			end
		end

		v.beast_color = beast_cfg and beast_cfg.beast_color or 0
		v.beast_star = beast_cfg and beast_cfg.beast_star or 0
		v.beasts_flag = is_beasts_item and 1 or 0
	end
	
	SortTools.SortDesc(item_list, "beast_color", "beast_star", "beasts_flag")
	return item_list
end

------------------------------------------------------------- 配置信息 --------------------------------------------------------
-- 初始化配置表
function ControlBeastsPrizeDrawWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("beasts_cfg_auto")
	if cfg then
		self.draw_type_cfg = cfg.draw_type
		self.draw_type_item_cfg = ListToMap(cfg.draw_type, "cost_item_id")
		self.draw_mode_cfg = ListToMap(cfg.draw_mode, "type", "mode")
		self.draw_convert_cfg = ListToMap(cfg.draw_convert, "type", "grade")
		self.draw_grade_cfg = ListToMapList(cfg.draw_grade, "type")
		self.draw_reward_pool_cfg = ListToMapList(cfg.draw_reward_pool, "type", "grade")
		self.draw_discount_cfg = cfg.draw_discount
	end
end
-- 清理垃圾
function ControlBeastsPrizeDrawWGData:DeleteCfg()
	self.draw_type_cfg = nil
	self.draw_type_item_cfg = nil
	self.draw_mode_cfg = nil
	self.draw_grade_cfg = nil
	self.draw_convert_cfg = nil
	self.draw_reward_pool_cfg = nil
	self.draw_discount_cfg = nil
end

-- 获取当前的红点
function ControlBeastsPrizeDrawWGData:GetCurBeastDrawModeRedByMode(draw_type, mode_id)
	local base_cfg = self:GetDrawTypeCfgByType(draw_type)
	if base_cfg then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id) --拥有的数量
		local mode_cfg = self:GetDrawModeCfgByType(draw_type, mode_id)
		local need_num = mode_cfg and mode_cfg.cost_item_num or 0
		
		if need_num <= 1 then
			return false
		end

		local cur_discount_cfg = self:GetCurDrawDiscountCfg()
		if not IsEmptyTable(cur_discount_cfg) and cur_discount_cfg.discount then
			return has_num >= math.ceil(need_num * cur_discount_cfg.discount / 10)
		else
			return has_num >= need_num
		end
	end

	return false
end

-- 获取当前的红点
function ControlBeastsPrizeDrawWGData:GetCurBeastDrawModeRedByLucky(draw_type)
	local base_cfg = self:GetDrawTypeCfgByType(draw_type)
	if base_cfg then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id) --拥有的数量
		local mode_cfg = self:GetDrawModeCfgByType(draw_type, mode_id)
		local need_num = mode_cfg and mode_cfg.cost_item_num or 0

		return has_num >= need_num
	end

	return false
end

-- 获取当前的总红点
function ControlBeastsPrizeDrawWGData:GetCurBeastDrawModeRed()
	if not FunOpen.Instance:GetFunIsOpened(FunName.ControlBeastsPrizeDrawWGView) then
		return false
	end

	if not self.draw_mode_cfg then
		return false
	end

	local is_red = false
	for draw_type, mode_list in pairs(self.draw_mode_cfg) do
		if self:IsCanShowDrawType(draw_type) then
			for _, mode_data in pairs(mode_list) do
				is_red = is_red or self:GetCurBeastDrawModeRedByMode(mode_data.type, mode_data.mode)
	
				if is_red then
					break
				end
			end
	
			if is_red then
				break
			end
	
			local day_draw_data, last_time = self:GetDrawGradeByType(draw_type)
			local grade = day_draw_data and day_draw_data.grade or 1
			local cfg = self:GetDrawGradeConvertByTypeGrade(draw_type, grade)
			local draw_data = self:GetDrawItemByType(draw_type)
	
			if cfg and draw_data then
				local need_lucky = cfg.need_lucky or 1
				is_red = is_red or draw_data.lucky >= need_lucky 
			end
	
			if is_red then
				break
			end
		end
	end
	
	return is_red
end

-- 获取当前的总红点
function ControlBeastsPrizeDrawWGData:GetCurBeastDrawModeRedByType(draw_type)
	if not self.draw_mode_cfg then
		return false
	end

	-- 未开启处理
	if not self:IsCanShowDrawType(draw_type) then
		return false
	end

	local draw_type_list = self:GetDrawModeCfgByType2(draw_type)

	if not draw_type_list then
		return false
	end

	local is_red = false
	for _, mode_data in pairs(draw_type_list) do
		is_red = is_red or self:GetCurBeastDrawModeRedByMode(mode_data.type, mode_data.mode)

		if is_red then
			break
		end
	end

	local day_draw_data, last_time = self:GetDrawGradeByType(draw_type)
	local grade = day_draw_data and day_draw_data.grade or 1
	local cfg = self:GetDrawGradeConvertByTypeGrade(draw_type, grade)
	local draw_data = self:GetDrawItemByType(draw_type)

	if cfg and draw_data then
		local need_lucky = cfg.need_lucky or 1
		is_red = is_red or draw_data.lucky >= need_lucky 
	end
	
	return is_red
end
------------------------------------------------------------------------
-- 获取抽奖数据根据类型
function ControlBeastsPrizeDrawWGData:GetDrawTypeCfgByType(draw_type)
	local empty = {}
	return (self.draw_type_cfg or empty)[draw_type]
end

-- 获取抽奖数据根据类型
function ControlBeastsPrizeDrawWGData:GetDrawTypeCfglist()
	return self.draw_type_cfg
end

function ControlBeastsPrizeDrawWGData:IsCanShowDrawType(draw_type)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local draw_type_cfg = self:GetDrawTypeCfgByType(draw_type)

	if not IsEmptyTable(draw_type_cfg) then
		return draw_type_cfg.open_day <= open_day
	end

	return false
end

-- 获取抽奖模式根据类型
function ControlBeastsPrizeDrawWGData:GetDrawModeCfgByType(draw_type, mode)
	local empty = {}
	return ((self.draw_mode_cfg or empty)[draw_type] or empty)[mode]
end

-- 获取抽奖模式根据类型
function ControlBeastsPrizeDrawWGData:GetDrawModeCfgByType2(draw_type)
	local empty = {}
	return (self.draw_mode_cfg or empty)[draw_type]
end

-- 获取保底模式根据类型
function ControlBeastsPrizeDrawWGData:IsExtraRewardItem(draw_type, item_id)
	local list = self:GetDrawBaoDiListByType(draw_type)

	for i, v in ipairs(list) do
		if v.item_id and item_id == v.item_id then
			return true
		end
	end

	return false
end

-- 获取是否大奖根据类型
function ControlBeastsPrizeDrawWGData:IsBigRewardItem(draw_type, item_id)
	local day_draw_data, _ = self:GetDrawGradeByType(draw_type)
	local grade = day_draw_data and day_draw_data.grade or 1
	local cfg_list = self:GetRewardPoolListByTypeGrade(draw_type, grade)
	
	if cfg_list and #cfg_list > 0 then
		for i, v in ipairs(cfg_list) do
			if v.item_id and item_id == v.item_id then
				return v.big_reward_type == 1
			end
		end
	end
	return false
end

-- 是否为抽奖物
function ControlBeastsPrizeDrawWGData:IsBeastsPrizeDrawItem(item_id)
	return self.draw_type_item_cfg[item_id] ~= nil
end

-- 获取当期类型的档次列表
function ControlBeastsPrizeDrawWGData:GetDrawGradeListByType(draw_type)
	local empty = {}
	return (self.draw_grade_cfg or empty)[draw_type]
end

-- 获取当期类型的档次列表
function ControlBeastsPrizeDrawWGData:GetDrawGradeConvertByTypeGrade(draw_type, draw_grade)
	local empty = {}
	return ((self.draw_convert_cfg or empty)[draw_type] or empty)[draw_grade]
end

-- 获取奖池根据类型
function ControlBeastsPrizeDrawWGData:GetRewardPoolListByTypeGrade(draw_type, draw_grade)
	local empty = {}
	return ((self.draw_reward_pool_cfg or empty)[draw_type] or empty)[draw_grade]
end

------------------直接获取配置表信息结束----------------------------------
-- 抽奖数据
function ControlBeastsPrizeDrawWGData:SetDrawItemInfo(protocol)
	self.draw_info_data = protocol.draw_item_list
end

-- 抽奖数据
function ControlBeastsPrizeDrawWGData:DrawItemUpdate(protocol)
	if not self.draw_info_data then
		self.draw_info_data = {}
	end

	self.draw_info_data[protocol.type] = protocol.draw_item_data
end

-- 获取当前的抽奖数据
function ControlBeastsPrizeDrawWGData:GetDrawItemByType(draw_type)
	local empty = {}
	return (self.draw_info_data or empty)[draw_type] 
end

-- 打开幻兽抽奖记录返回
function ControlBeastsPrizeDrawWGData:SetDrawRecordInfo(protocol)
	for i, v in ipairs(protocol.record_data_list) do
		self:AddOneRecordInfo(protocol.type, v, false)
	end
end

-- 打开幻兽抽奖记录返回(单个增加)
function ControlBeastsPrizeDrawWGData:UpdateRecordInfo(protocol)
	self:AddOneRecordInfo(protocol.type, protocol.record_data, true)
end

-- 添加一份记录
function ControlBeastsPrizeDrawWGData:AddOneRecordInfo(type, record_info, is_add)
	if not self.draw_record_info then
		self.draw_record_info = {}
	end

	if not self.draw_record_info[type] then
		self.draw_record_info[type] = {}
	end

	-- 封装一下
	local main_role_vo =  GameVoManager.Instance:GetMainRoleVo()
	local info = {}
	info.item_data = {}
	info.item_data.item_id = record_info.beast_id
	info.role_name = main_role_vo and main_role_vo.name or ""
	info.consume_time = record_info.record_time

	if is_add then
		table.insert(self.draw_record_info[type], 1, info)
	else
		table.insert(self.draw_record_info[type], info)
	end
end

-- 获取当前的抽奖记录
function ControlBeastsPrizeDrawWGData:GetDrawRecordByType(draw_type)
	local empty = {}
	return (self.draw_record_info or empty)[draw_type] or {}
end

------------------自定义方法----------------------------------
-- 当前天数具体的档位
function ControlBeastsPrizeDrawWGData:GetDrawGradeByType(draw_type)
	local list = self:GetDrawGradeListByType(draw_type)
	local day_draw_data = nil
	local last_time = 0

	if list == nil or #list <= 0 then
		return day_draw_data, last_time
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local aim_data = nil

	for i, v in ipairs(list) do
		if v and open_day >= v.min_day and open_day <= v.max_day then
			aim_data = v
			break
		end
	end

	if aim_data then
		day_draw_data = aim_data
		local last_day = aim_data.max_day - open_day
		local last_day_time = last_day * 24 * 3600
	    -- 获取当天剩余时间
		local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
		last_time = last_day_time + time
	end

	return day_draw_data, last_time
end

-- 获取当前展示的
function ControlBeastsPrizeDrawWGData:GetDrawShowListByCfg(day_draw_data)
	if not day_draw_data then
		return nil
	end

	local show_id = day_draw_data.show_id
	local show_spine_id = day_draw_data.show_spine_id
	if show_id == nil or show_id == "" or show_spine_id == nil or show_spine_id == "" then
		return nil
	end

	local draw_show_list = {}
	local show_id_list = Split(show_id, "|")
	local show_spine_id_list = Split(show_spine_id, "|")
	for i, v in ipairs(show_id_list) do
		local show_id_info = {}
		show_id_info.item_id = tonumber(v) or 0
		show_id_info.num = 1
		show_id_info.is_bind = 1
		show_id_info.spine_id = tonumber(show_spine_id_list[i] or 0)
		table.insert(draw_show_list, show_id_info)
	end

	return draw_show_list
end

function ControlBeastsPrizeDrawWGData:GetSetIsPlayingDraw(is_playing_draw)
	if not is_playing_draw then
		return self.is_play_draw
	else
		self.is_play_draw = is_playing_draw
	end
end

-- [废弃]获取当前展示的
function ControlBeastsPrizeDrawWGData:GetDrawGradeShowListByCfg(day_draw_data)
	if not day_draw_data then
		return nil
	end

	local show_id = day_draw_data.show_id

	if show_id == nil or show_id == "" then
		return nil
	end

	local draw_show_list = {}
	local show_id_list = Split(show_id, "|")

	for m, show_id_str in ipairs(show_id_list) do
		local show_id_data = Split(show_id_str, ",")
		local show_id_info = {}
		local item_id = tonumber(show_id_data[1]) or 0
		local item_type = show_id_data[2]

		show_id_info.item_id = item_id
		show_id_info.num = 1
		show_id_info.is_bind = 1

		if item_type == nil then
			table.insert(draw_show_list, show_id_info)
		else
			local item_type_num = tonumber(item_type) or 0
			if draw_show_list[item_type_num] then
				table.insert(draw_show_list[item_type_num], show_id_info)
			else
				draw_show_list[item_type_num] = {}
				table.insert(draw_show_list[item_type_num], show_id_info)
			end
		end
	end

	return draw_show_list
end

function ControlBeastsPrizeDrawWGData:GetDrawProListByType(draw_type)
	local day_draw_data, _ = self:GetDrawGradeByType(draw_type)
	local grade = day_draw_data and day_draw_data.grade or 1
	local cfg_list = self:GetRewardPoolListByTypeGrade(draw_type, grade)
	local pro_table = {}

	for i, v in ipairs(cfg_list) do
		local data = {}
		data.number = i
		data.item_id = v.item_id
		data.random_count = v.random_count
		table.insert(pro_table, data)
	end

	return pro_table
end

function ControlBeastsPrizeDrawWGData:GetCurDrawDiscountCfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local discount_cfg = {}

	for k, v in pairs(self.draw_discount_cfg) do
		if open_day >= v.start_day and open_day <= v.end_day then
			discount_cfg = v
			break
		end
	end

	return discount_cfg
end

-- 获取操作保存数据
function ControlBeastsPrizeDrawWGData:GetOperateStatus(contract_select_type)
	local role_id = RoleWGData.Instance:GetOriginUid()
	local curr_draw_anim = 0
	local draw_skip_anim_str = string.format("beast_prize_draw_skip_anim_%s_%s", contract_select_type, role_id)
	curr_draw_anim = PlayerPrefsUtil.GetInt(draw_skip_anim_str, curr_draw_anim)

	local curr_draw_despose = 0
	local draw_draw_despose_str = string.format("beast_prize_draw_despose_%s_%s", contract_select_type, role_id)
	curr_draw_despose = PlayerPrefsUtil.GetInt(draw_draw_despose_str, curr_draw_despose)

	return curr_draw_anim, curr_draw_despose
end

-- 获取操作保存数据
function ControlBeastsPrizeDrawWGData:SaveOperateStatus(draw_anim, draw_despose, contract_select_type)
	local role_id = RoleWGData.Instance:GetOriginUid()

	if draw_anim ~= nil then
		local draw_skip_anim_str = string.format("beast_prize_draw_skip_anim_%s_%s", contract_select_type, role_id)
		PlayerPrefsUtil.SetInt(draw_skip_anim_str, draw_anim)
	end

	if draw_despose ~= nil then
		local draw_draw_despose_str = string.format("beast_prize_draw_despose_%s_%s", contract_select_type, role_id)
		PlayerPrefsUtil.SetInt(draw_draw_despose_str, draw_despose)
	end
end