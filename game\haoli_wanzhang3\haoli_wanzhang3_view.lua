---------------------------------
-- 绝版套装View
---------------------------------
HaoLiWanZhang3View = HaoLiWanZhang3View or BaseClass(SafeBaseView)

local COUNT_DOWN_KEY = "haoli_wanzhang3_end_time"

function HaoLiWanZhang3View:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full

	self:SetMaskBg()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/haoli_wanzhang3_ui_prefab", "layout_haoli_wanzhang3")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function HaoLiWanZhang3View:__delete()

end

function HaoLiWanZhang3View:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3, OA_HAOLI_WANZHANG2_OPERATE_TYPE.OA_HAOLI_WANZHANG2_OPERATE_TYPE_INFO)
end

function HaoLiWanZhang3View:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.HaoLiWanZhang3.ViewName
	self:FlushEndTime()

	self.big_reward_cfg = {}

    if not self.all_reward_list then
        self.all_reward_list = AsyncListView.New(HaoLiWanZhang3Cell, self.node_list.suit_list)
	end

	if not self.big_reward_item_list then
        self.big_reward_item_list = AsyncListView.New(HaoLiWanZhang3ItemCell, self.node_list.reward_item_list)
		self.big_reward_item_list:SetStartZeroIndex(true)
	end

	local other_cfg = HaoLiWanZhang3WGData.Instance:GetTipShowShopCfg()
	local bundle, asset = ResPath.GetRawImagesJPG(other_cfg.bg_res)
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	--加载模型时装
	if nil == self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		self.role_model:SetRenderTexUI3DModel(display_data)
		--self.role_model:SetUI3DModel(self.node_list["ph_display"].transform, nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_model)
	end

	if not self.fz_display then
		self.fz_display = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["fz_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		self.fz_display:SetRenderTexUI3DModel(display_data)
		--self.fz_display:SetUI3DModel(self.node_list["fz_model"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.fz_display)
	end

	self.load_flag = true
	XUI.AddClickEventListener(self.node_list["everyday_gift"], BindTool.Bind(self.OnBtnfreeBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_go_suit"], BindTool.Bind(self.OnClickJumpBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_skill_show"], BindTool.Bind(self.OnBtnShowSkill, self))
	XUI.AddClickEventListener(self.node_list["get_btn"], BindTool.Bind(self.OnClickFinalGetBtn, self))
end

function HaoLiWanZhang3View:ReleaseCallBack()
    if self.all_reward_list then
        self.all_reward_list:DeleteMe()
        self.all_reward_list = nil
    end
	
	if self.big_reward_item_list then
		self.big_reward_item_list:DeleteMe()
		self.big_reward_item_list = nil
	end
	
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.lingchong_model then
		self.lingchong_model:DeleteMe()
		self.lingchong_model = nil
	end

	if self.xianwa_model then
		self.xianwa_model:DeleteMe()
		self.xianwa_model = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	if self.fz_display then
		self.fz_display:DeleteMe()
		self.fz_display = nil
	end

	if CountDownManager.Instance:HasCountDown(COUNT_DOWN_KEY) then
		CountDownManager.Instance:RemoveCountDown(COUNT_DOWN_KEY)
	end

	if self.show_exchange_message_tween then
		self.show_exchange_message_tween:Kill()
		self.show_exchange_message_tween = nil
	end

	--self.footprint_eff_t = nil
	--self.is_foot_view = nil
	self.body_res_id = nil
	self.mount_res_id = nil
	self.have_foot_print = nil
	self.load_flag = nil
    self.next_create_footprint_time = nil
	self.big_reward_cfg = nil
end

function HaoLiWanZhang3View:OnFlush()
    self:FlushSuitModel()
    self:FlushFzModel()
    self:FlushSkillBtn()
    local round_cfg = HaoLiWanZhang3WGData.Instance:GetCurRoundRewardCfg()
    if IsEmptyTable(round_cfg) then
        return
    end

    local cur_cfg_list = {}
    for i, v in ipairs(round_cfg) do
        if v.is_big_reward == 0 then
            table.insert(cur_cfg_list, v)
        else
            self.big_reward_cfg = v
        end
    end

	local sort_table = {}
	local count = 1
	for i, v in ipairs(cur_cfg_list) do
		local is_get = HaoLiWanZhang3WGData.Instance:GetRewardStateBySeq(v.seq)
		if is_get then
			table.insert(sort_table, v)
		else
			table.insert(sort_table, count, v)
			count = count + 1
		end
	end

    self.all_reward_list:SetDataList(sort_table)

    local other_cfg = HaoLiWanZhang3WGData.Instance:GetTipShowShopCfg()
    self.node_list.txt_suit_title.text.text = other_cfg.title_text

    local cur_integral = HaoLiWanZhang3WGData.Instance:GetCurScore()
    local cur_integral_show = cur_integral / RECHARGE_BILI
    self.node_list.cur_integral.text.text = string.format(Language.HaoLiWanZhang3.CurIntegral, cur_integral_show)

    local cur_round = HaoLiWanZhang3WGData.Instance:GetCurRoundNum() + 1
    local total_round = HaoLiWanZhang3WGData.Instance:GetTotalRoundNum() + 1
    self.node_list.text_round.text.text = string.format(Language.HaoLiWanZhang3.RoundNumStr, cur_round, total_round)

    local is_buy_free = HaoLiWanZhang3WGData.Instance:GetShopIsBuyFlag()
    self.node_list.gift_is_get:SetActive(is_buy_free)
    self.node_list.gift_remind:SetActive(not is_buy_free)

    -- local data = HaoLiWanZhang3WGData.Instance:GetCurRoundModelCfg()
    -- if data then
    -- 	self.node_list.suit_name.text.text = string.format(Language.HaoLiWanZhang3.SuitName2, data.name)
    -- end
    -- self.node_list.txt_des.text.text = Language.HaoLiWanZhang3.SuitDes

	--[[
    local next_reward_cfg
    for k, v in ipairs(round_cfg) do
        if cur_integral < v.need_lingyu then
            next_reward_cfg = v
            break
        end
    end
    if not next_reward_cfg then
        next_reward_cfg = round_cfg[#round_cfg]
    end
	
	local need_num = next_reward_cfg.need_lingyu / RECHARGE_BILI / 100
	local color = cur_integral_show < need_num and "#F97878" or "#35e28a"
	local cur_integral_text = string.format("%.2f", cur_integral_show)
	cur_integral_text = ToColorStr(cur_integral_show, color)
	self.node_list.txt_recharge_des.text.text = string.format(Language.HaoLiWanZhang3.RechargeDes, cur_integral_text, need_num)
	]]
	self.node_list.txt_recharge_des.text.text = Language.HaoLiWanZhang3.RechargeTip

	-- 跳转按钮红点
	local is_red = HaoLiWanZhang3WGData.Instance:GetJumpBtnRemind()
	self.node_list["jump_red"]:SetActive(is_red)

	-- 特殊显示的档位
	self:FlushFinalBigReward()
end

function HaoLiWanZhang3View:FlushFinalBigReward()
    if IsEmptyTable(self.big_reward_cfg) then
        return
    end
	local cfg = self.big_reward_cfg
    local need_lingyu = cfg.need_lingyu
    local score = HaoLiWanZhang3WGData.Instance:GetCurScore()
    local cur_recharge = score / RECHARGE_BILI
    local target_recharge = need_lingyu / RECHARGE_BILI

    self.node_list.txt_recharge_tips.text.text = string.format(Language.HaoLiWanZhang3.RechargeStr2, target_recharge)
    local cur_recharge_show_num = string.format("%.2f", cur_recharge)
    self.node_list.txt_cur_recharge.text.text = string.format(Language.HaoLiWanZhang3.RechargeProgress2, cur_recharge_show_num, target_recharge)
    self.node_list["sldr_progress"].slider.value = score / need_lingyu
	

    local is_fetch = HaoLiWanZhang3WGData.Instance:GetRewardStateBySeq(cfg.seq)
	local can_fetch = score >= need_lingyu and not is_fetch
    for key, value in pairs(cfg.reward_item) do
        value.can_fetch = can_fetch
        value.is_fetch = is_fetch
    end
    self.big_reward_item_list:SetDataList(cfg.reward_item)
	self.node_list["get_btn"]:SetActive(can_fetch)
end

function HaoLiWanZhang3View:OnClickFinalGetBtn()
	if not self.big_reward_cfg then
		return
	end

	local is_get = HaoLiWanZhang3WGData.Instance:GetRewardStateBySeq(self.big_reward_cfg.seq)
	local score = HaoLiWanZhang3WGData.Instance:GetCurScore()
	local need_lingyu = self.big_reward_cfg.need_lingyu
	if is_get then
		self.node_list.tianyin_is_get:SetActive(true)
		TipWGCtrl.Instance:ShowSystemMsg(Language.HaoLiWanZhang3.IsGetReward)
		return
	end

	if score >= need_lingyu then
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3, OA_HAOLI_WANZHANG2_OPERATE_TYPE.OA_HAOLI_WANZHANG2_OPERATE_TYPE_REWARD, self.big_reward_cfg.seq)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.HaoLiWanZhang3.ScoreLack)
	end
end

function HaoLiWanZhang3View:FlushEndTime()
	local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3)
	if CountDownManager.Instance:HasCountDown(COUNT_DOWN_KEY) then
		CountDownManager.Instance:RemoveCountDown(COUNT_DOWN_KEY)
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown(COUNT_DOWN_KEY,
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function HaoLiWanZhang3View:UpdateCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["activity_time"].text.text = string.format(Language.HaoLiWanZhang3.ActTime, time_str)
end

function HaoLiWanZhang3View:OnComplete()
	self.node_list.activity_time.text.text = ""
	self:Close()
end

function HaoLiWanZhang3View:OnBtnfreeBtn()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		local is_buy_free = HaoLiWanZhang3WGData.Instance:GetShopIsBuyFlag()
		if is_buy_free then
			TipWGCtrl.Instance:ShowSystemMsg(Language.GoldStoneBuy.AllFreeShopBuy)
			return
		end

		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3, OA_HAOLI_WANZHANG2_OPERATE_TYPE.OA_HAOLI_WANZHANG2_OPERATE_TYPE_EVERYDAY_REWARD)
	end
end

function HaoLiWanZhang3View:OnClickJumpBtn()
	local open_day_cfg = HaoLiWanZhang3WGData.Instance:GetTipShowShopCfg()
    FunOpen.Instance:OpenViewNameByCfg(open_day_cfg.jump_path)
	self.node_list["jump_red"]:SetActive(false)
    HaoLiWanZhang3WGData.Instance:SetJumpBtnRemind()
	RemindManager.Instance:Fire(RemindName.HaoLiWanZhang3)
end

function HaoLiWanZhang3View:OnBtnShowSkill()
	local cfg = HaoLiWanZhang3WGData.Instance:GetCurShowWaistCfg()
	if not cfg then
		return
	end
	local data = SupremeFieldsWGData.Instance:SkillShowCfgList(cfg.waist_type, 1)
	CommonSkillShowCtrl.Instance:SetViewDataAndOpen(data)
end

function HaoLiWanZhang3View:FlushSkillBtn()
	local cfg = HaoLiWanZhang3WGData.Instance:GetCurShowWaistCfg()
	if not cfg then
		return
	end

	local bundle, asset = ResPath.GetSupremeFieldsActTxt(cfg.waist_type)
	local skill_id = SupremeFieldsWGData.Instance:GetSkillIDList(cfg.waist_type, 1)[1] or 0
	local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(skill_id)
	bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon)
	self.node_list.skill_icon.image:LoadSpriteAsync(bundle, asset, function()
		self.node_list.skill_icon.image:SetNativeSize()
	end)
end

function HaoLiWanZhang3View:FlushFzModel()
	if not self.fz_display then
		return
	end

	local cfg = HaoLiWanZhang3WGData.Instance:GetCurShowWaistCfg()
	if not cfg then
		return
	end
	self.fz_display:RemoveAllModel()
	local bundle, asset = ResPath.GetSkillFaZhenModel(cfg.waist_type)
	self.fz_display:SetMainAsset(bundle, asset)

	if cfg.display_pos and cfg.display_pos ~= "" then
		local pos_list = string.split(cfg.display_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.fz_model.rect, pos_x, pos_y)
	end

	if cfg.display_scale and cfg.display_scale ~= "" then
		Transform.SetLocalScaleXYZ(self.node_list.fz_model.transform, cfg.display_scale, cfg.display_scale, cfg.display_scale)
	end

	if cfg.rotation and cfg.rotation ~= "" then
		local rot_x, rot_y, rot_z = 0, 0, 0
		local rot_list = string.split(cfg.rotation, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
		self.node_list.fz_model.rect.rotation = Quaternion.Euler(rot_x, rot_y, rot_z)
	end

	self.role_model:RemoveAllModel()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.role_model:SetModelResInfo(role_vo, nil, function()
		self.role_model:PlayRoleShowAction()
	end)
	self.role_model:FixToOrthographic(self.root_node_transform)
end

function HaoLiWanZhang3View:FlushSuitModel()
	if not self.role_model then
		return
	end

	self.role_model:RemoveAllModel()

	local data_list = HaoLiWanZhang3WGData.Instance:GetActivationPartList()
	if not data_list then
		return
	end

	--清理掉回调
	--self:ClearFootEff()
	self.role_model:RemoveAllModel()

	self.node_list["lc_root"]:SetActive(false)
	self.node_list["xw_root"]:SetActive(false)
	self.node_list["mount_root"]:SetActive(false)

	--self.is_foot_view = false
	self.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	self.mount_res_id = 0
	self.have_foot_print = false
	local has_fashion_show = false
	local res_id, fashion_cfg
	for k, data in pairs(data_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				self.body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
				has_fashion_show = true
			end
		elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 足迹
			fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
			if fashion_cfg then
				self.mount_res_id = fashion_cfg.appe_image_id
			end
		elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
			self.have_foot_print = true
		end
	end

	local d_body_res, d_hair_res, d_face_res
	local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
	local vo = main_role and main_role:GetVo()
	if not has_fashion_show and vo and vo.appearance then
		if vo.appearance.fashion_body == 0 then
			d_body_res = vo.appearance.default_body_res_id
			d_hair_res = vo.appearance.default_hair_res_id
			d_face_res = vo.appearance.default_face_res_id
		end
	end

	local animation_name = self.have_foot_print and SceneObjAnimator.Move or SceneObjAnimator.Idle
	local extra_role_model_data = {
        d_face_res = d_face_res,
        d_hair_res = d_hair_res,
		d_body_res = d_body_res,
		animation_name = animation_name,
    }
	self.role_model:SetRoleResid(self.body_res_id, nil, extra_role_model_data)

	local action_type = MOUNT_RIDING_TYPE[1]
	if self.mount_res_id > 0 then
		self.role_model:SetMountResid(self.mount_res_id)
		local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.mount_res_id)
		if not IsEmptyTable(action_cfg) then
			action_type = MOUNT_RIDING_TYPE[action_cfg.action]
		end
		self.role_model:PlayStartAction(action_type)
	end

	for k, v in pairs(data_list) do
		self:ShowModelByData(v)
	end

	self:ChangeModelShowScale()
end

function HaoLiWanZhang3View:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end
	local res_id, fashion_cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then -- 脸饰
				self.role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then -- 腰饰
				self.role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then -- 尾巴
				self.role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
				self.role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				self.role_model:SetFootTrailModel(res_id)
				self.role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
				self.role_model:PlayRoleAction(SceneObjAnimator.Move)

				-- self.is_foot_view = true
				-- self.foot_effect_id = res_id
				-- if not self.use_update then
				-- 	Runner.Instance:AddRunObj(self, 8)
				-- 	self.use_update = true
				-- end
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then -- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then -- 仙娃
		fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		if fashion_cfg then
			self:SetXianWaModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.active_id)
		end
	end
end

--[[
function HaoLiWanZhang3View:ShowMechaModel(mecha_data)
	local mecha_seq = mecha_data.param1 or 0
	if mecha_seq >= 0 then
		local part_list = {}
		local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(mecha_seq)
		for k, v in pairs(base_part) do
			local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
			part_list[part_cfg.part] = part_cfg.res_id
		end

		if not IsEmptyTable(mecha_part_cfg) then
			part_list[mecha_part_cfg.part] = mecha_part_cfg.res_id
		end

		local part_info = {
			gundam_seq = mecha_seq,
			gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
			gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
			gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
			gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
			gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
			gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
			gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
			gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
		}

		self.role_model:SetGundamModel(part_info)
        self.role_model:PlayStartAction(SceneObjAnimator.UiIdle)
	end
end
]]

function HaoLiWanZhang3View:SetLingChongModelData(type, res_id)
	self.node_list["lc_root"]:SetActive(true)
	if nil == self.lingchong_model then
		self.lingchong_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["lc_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		self.lingchong_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.lingchong_model)
	else
		if self.lingchong_model then
			self.lingchong_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetPetModel(res_id)
	self.lingchong_model:SetMainAsset(bundle, asset, function()
		self.lingchong_model:PlaySoulAction()
	end)

	self.lingchong_model:FixToOrthographic(self.root_node_transform)
end

function HaoLiWanZhang3View:SetXianWaModelData(res_id)
	self.node_list["xw_root"]:SetActive(true)
	if nil == self.xianwa_model then
		self.xianwa_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["xw_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		self.xianwa_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.xianwa_model)
	else
		if self.xianwa_model then
			self.xianwa_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetHaiZiModel(res_id)
	self.xianwa_model:SetMainAsset(bundle, asset, function()
		self.xianwa_model:PlaySoulAction()
	end)

	self.xianwa_model:FixToOrthographic(self.root_node_transform)
end

function HaoLiWanZhang3View:SetMountModelData(res_id)
	self.node_list["mount_root"]:SetActive(true)
	if nil == self.mount_model then
		self.mount_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["mount_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}
		self.mount_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.mount_model)
	else
		if self.mount_model then
			self.mount_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetMountModel(res_id)
	self.mount_model:SetMainAsset(bundle, asset, function()
		self.mount_model:PlayMountAction()
	end)

	self.mount_model:FixToOrthographic(self.root_node_transform)
end

--[[
function HaoLiWanZhang3View:Update(now_time, elapse_time)
	if not self.is_foot_view then
		return
	end

	if self.next_create_footprint_time == 0 then
		self:CreateFootPrint()
		self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
	end

	if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
		self.next_create_footprint_time = 0
	end

	if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
		self.next_create_footprint_time = 0
	end

	self:UpdateFootprintPos()
end

function HaoLiWanZhang3View:CreateFootPrint()
	if nil == self.foot_effect_id then
		return
	end

	if nil == self.footprint_eff_t then
		self.footprint_eff_t = {}
	end

	local pos = self.role_model.draw_obj:GetRoot().transform
	local bundle, asset = ResPath.GetFootUIEffect(self.foot_effect_id)
	EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x, pos.position.y, pos.position.z), nil, pos, nil, function(obj)
		if obj then
			if nil ~= obj then
				if self.role_model then
					obj.transform.localPosition = Vector3.zero
					obj:SetActive(false)
					obj:SetActive(true)
					table.insert(self.footprint_eff_t, { obj = obj, role_model = self.role_model })
					self.role_model:OnAddGameobject(obj)
				else
					ResPoolMgr:Release(obj)
				end
			end
		end
	end)

	if #self.footprint_eff_t > 2 then
		local obj = table.remove(self.footprint_eff_t, 1)
		obj.role_model:OnRemoveGameObject(obj.obj)
		if not IsNil(obj.obj) then
			obj.obj:SetActive(false)
		end
	end
end

function HaoLiWanZhang3View:UpdateFootprintPos()
	if nil == self.footprint_eff_t then
		return
	end

	for k, v in pairs(self.footprint_eff_t) do
		if not IsNil(v.obj) then
			local pos = v.obj.transform.localPosition
			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
		end
	end
end

function HaoLiWanZhang3View:ClearFootEff()
	if self.footprint_eff_t ~= nil then
		for k, v in pairs(self.footprint_eff_t) do
			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
				v.role_model:OnRemoveGameObject(v.obj)
				v.obj:SetActive(false)
			end
		end
	end

	self.footprint_eff_t = {}
end
]]

function HaoLiWanZhang3View:ChangeModelShowScale()
	local data = HaoLiWanZhang3WGData.Instance:GetCurRoundModelCfg()
	if IsEmptyTable(data) then
		return
	end

	local pos_str = data.main_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	end

	local rotate_str = data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.role_model then
			self.role_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
		end
	end

	local scale = data.main_scale
	if scale and scale ~= "" then
		RectTransform.SetLocalScale(self.node_list.ph_display.rect, scale)
	end

	--灵宠
	if self.node_list["lc_root"]:GetActive() then
		pos_str = data.pet_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.lc_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		rotate_str = data.pet_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.lingchong_model then
				self.lingchong_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
		end

		scale = data.lc_scale
		if scale and scale ~= "" then
			RectTransform.SetLocalScale(self.node_list.lc_display.rect, scale)
		end
	end

	--仙娃
	if self.node_list["xw_root"]:GetActive() then
		pos_str = data.xw_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.xw_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		rotate_str = data.xw_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.xianwa_model then
				self.xianwa_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
		end

		scale = data.xw_scale
		if scale and scale ~= "" then
			RectTransform.SetLocalScale(self.node_list.xw_display.rect, scale)
		end
	end

	--坐骑
	if self.node_list["mount_display"]:GetActive() then
		pos_str = data.mount_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.mount_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		rotate_str = data.mount_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.mount_model then
				self.mount_model:SetRotation(u3dpool.vec3(rot[1] or 0, rot[2] or 0, rot[3] or 0))
			end
		end

		scale = data.mount_scale
		if scale and scale ~= "" then
			RectTransform.SetLocalScale(self.node_list.mount_display.rect, scale)
		end
	end
end

--------------------------------------
-- HaoLiWanZhang3Cell
--------------------------------------
HaoLiWanZhang3Cell = HaoLiWanZhang3Cell or BaseClass(BaseRender)

function HaoLiWanZhang3Cell:__init()
	if not self.reward_item_list then
        self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.show_item_list)
		self.reward_item_list:SetStartZeroIndex(true)
	end
	
	self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
	self.node_list["btn_go_recharge"].button:AddClickListener(BindTool.Bind(self.OnClickGoRechargeBtn, self))
end

function HaoLiWanZhang3Cell:__delete()
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function HaoLiWanZhang3Cell:OnFlush()
	if not self.data then
		return
	end

	self.reward_item_list:SetDataList(self.data.reward_item)

    local need_lingyu = self.data.need_lingyu
    local score = HaoLiWanZhang3WGData.Instance:GetCurScore()
	local cur_recharge = score / RECHARGE_BILI
    local target_recharge = need_lingyu / RECHARGE_BILI

	self.node_list.tianyin_integral.text.text = string.format(Language.HaoLiWanZhang3.RechargeStr, target_recharge)
	local cur_recharge_show_num = string.format("%.2f", cur_recharge)
	local cur_recharge_str = score < need_lingyu and cur_recharge_show_num or ToColorStr(cur_recharge_show_num, "#0a7700")
	self.node_list.txt_cur_recharge.text.text = string.format(Language.HaoLiWanZhang3.RechargeProgress, cur_recharge_str, target_recharge)

	local is_get = HaoLiWanZhang3WGData.Instance:GetRewardStateBySeq(self.data.seq)
    self.node_list.tianyin_is_get:SetActive(is_get)
	self.node_list.get_btn:SetActive(not is_get and need_lingyu <= score)
    self.node_list.btn_go_recharge:SetActive(not is_get and need_lingyu > score)
end

function HaoLiWanZhang3Cell:OnClickGetReward()
	if not self.data then
		return
	end

	local is_get = HaoLiWanZhang3WGData.Instance:GetRewardStateBySeq(self.data.seq)
	local score = HaoLiWanZhang3WGData.Instance:GetCurScore()
	local need_lingyu = self.data.need_lingyu
	if is_get then
		self.node_list.tianyin_is_get:SetActive(true)
		TipWGCtrl.Instance:ShowSystemMsg(Language.HaoLiWanZhang3.IsGetReward)
		return
	end

	if score >= need_lingyu then
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3, OA_HAOLI_WANZHANG2_OPERATE_TYPE.OA_HAOLI_WANZHANG2_OPERATE_TYPE_REWARD, self.data.seq)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.HaoLiWanZhang3.ScoreLack)
	end
end

function HaoLiWanZhang3Cell:OnClickGoRechargeBtn()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
end

--------------------------------------
-- HaoLiWanZhang3ItemCell
--------------------------------------
HaoLiWanZhang3ItemCell = HaoLiWanZhang3ItemCell or BaseClass(BaseRender)

function HaoLiWanZhang3ItemCell:__init()
	if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_cell_root)
	end
end

function HaoLiWanZhang3ItemCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function HaoLiWanZhang3ItemCell:OnFlush()
	if not self.data then
		return
	end

	self.item_cell:SetData(self.data)
	self.node_list["can_fetch_flag"]:SetActive(self.data.can_fetch)
	self.node_list["reward_flag"]:SetActive(self.data.is_fetch)
end