FollowBubble = FollowBubble or BaseClass(BaseRender)
FollowBubble.BUBBLE_VIS = false

function FollowBubble:__init()
	self.obj_type = nil
	self.follow_parent = nil
	self.async_loader = nil
	self.bubble_text_str = nil
	self.bubble_vis = false
	self.bubble_callback = nil
	self.bubble_pos = nil
end

function FollowBubble:__delete()
	self.bubble_text_str = nil
	self.follow_parent = nil
	-- self.bubble_callback = nil
	self:DoBubbleCallBack()
	self.bubble_pos = nil
	self:RemoveDelayTime()

	if self.bubble_vis then
		FollowBubble.BUBBLE_VIS = false
	end
	self.bubble_vis = false
end

function FollowBubble:SetFollowParent(obj_type, follow_parent)
	self.obj_type = obj_type
	if follow_parent then
		self.follow_parent = follow_parent.transform
	end
	self:UpdateBubble()
end

function FollowBubble:CreateBubble(text, time)
	FollowBubble.BUBBLE_VIS = true
	self.bubble_vis = true

	self.async_loader = AllocAsyncLoader(self, "root_loader")
	self.async_loader:SetIsUseObjPool(true)
	self.async_loader:SetIsInQueueLoad(true)
	self.async_loader:SetParent(self.follow_parent, false)
	self.async_loader:Load("uis/view/miscpre_load_prefab", "LeisureBubble",
		function (gameobj)
			if IsNil(self.follow_parent) then
				self.async_loader:Destroy()
				return
			end

			if not self.bubble_vis or not FollowBubble.BUBBLE_VIS then
				self.async_loader:Destroy()
				return
			end

			self:SetInstance(gameobj)

			if nil ~= time and time > 0 then
				self:RemoveDelayTime()
				self.delay_time = GlobalTimerQuest:AddDelayTimer(function()
						self:HideBubble()
						self:DoBubbleCallBack()
					end, time)
			end

			self.view:SetActive(true)

			self.node_list.text.text.text = text
		end)
end

function FollowBubble:ChangeBubble(text, time, callback, pos)
	if "" == text or nil == text then
		return
	end

	self.bubble_text_str = text
	self.bubble_time = time
	self.bubble_callback = callback
	self.bubble_pos = pos
	self:UpdateBubble()
end

function FollowBubble:ShowBubble()
	if FollowBubble.BUBBLE_VIS then
		return
	end

	FollowBubble.BUBBLE_VIS = true
	self.bubble_vis = true
	self:UpdateBubble()
end

function FollowBubble:HideBubble()
	FollowBubble.BUBBLE_VIS = false
	self.bubble_vis = false

	if nil ~= self.async_loader then
		self.async_loader:Destroy()
		self.async_loader = nil
	end
end

function FollowBubble:UpdateBubble()
	if nil ~= self.follow_parent and nil ~= self.bubble_text_str then
		self:CreateBubble(self.bubble_text_str, self.bubble_time)
	end
end

function FollowBubble:RemoveDelayTime()
	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
		self.delay_time = nil
	end
end

function FollowBubble:IsBubbleVisible()
	return self.bubble_vis
end

--设置的回调在 气泡框隐藏 或 销毁 的时候会调用一次
function FollowBubble:DoBubbleCallBack()
	if self.bubble_callback ~= nil then
		local call = self.bubble_callback
		self.bubble_callback = nil
		call()
	end
end
