TianShenLingHeDrawRewardView = TianShenLingHeDrawRewardView or BaseClass(SafeBaseView)
local ani_flag_t = {}
local ani_count = 1
local ani_ten_flag_t = {}
local ani_baodi_count = 1
local ani_ten_count = 1
local scroll_verticalNormalizedPosition = 1
local ANI_SPEED = 0.1
local col_num = 10 							 		--每行多少个
local row_num = 1								--显示3行
local lerp = 0.015--1 / (50 - (col_num * row_num)*0)* 0.5		--每次减少多少

local reword_count = 0 				--该次寻宝奖励物品个数

function TianShenLingHeDrawRewardView:__init()
    self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
    self.view_name = "TianShenLingHeDrawRewardView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/tianshen_linghe_ui_prefab", "layout_linghe_draw_result")
end

function TianShenLingHeDrawRewardView:ReleaseCallBack()
	if nil ~= self.zhanshi_grid then
		self.zhanshi_grid:DeleteMe()
		self.zhanshi_grid = nil
	end
	if nil ~= self.zhanshi_ten_grid then
		self.zhanshi_ten_grid:DeleteMe()
		self.zhanshi_ten_grid = nil
	end


    if self.baodi_time_quest then
		GlobalTimerQuest:CancelQuest(self.baodi_time_quest)
		self.baodi_time_quest = nil
	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
	if self.ten_time_quest then
		GlobalTimerQuest:CancelQuest(self.ten_time_quest)
		self.ten_time_quest = nil
	end
	if self.move_scroll_quest then
		GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
		self.move_scroll_quest = nil
	end

	--[[if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end]]
end

function TianShenLingHeDrawRewardView:LoadCallBack()
	--[[if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end]]

	self:InitXunBaoZhanshi()
	self:RegisterEvent()
end

--初始化格子
function TianShenLingHeDrawRewardView:InitXunBaoZhanshi()
    local asset_bundle = "uis/view/tianshen_linghe_ui_prefab"
    local asset_name = "linghe_zhan_shi_item_cell"
	self.zhanshi_grid = LingHeRewardGrid.New()
    self.zhanshi_grid:CreateCells(
        {
            col = col_num,
            change_cells_num = 1,
            assetBundle = asset_bundle,
            assetName = asset_name,
            list_view = self.node_list["ph_zhanshii_cell"],
            itemRender = LingHeRewardCell
        }
)
    self.zhanshi_grid:SetStartZeroIndex(false)

	self.zhanshi_ten_grid = LingHeRewardGrid.New()
    self.zhanshi_ten_grid:CreateCells(
        {
            col = 5,
            change_cells_num = 1,
            assetBundle = asset_bundle,
            assetName = asset_name,
            list_view = self.node_list["ph_zhanshii_ten_cell"],
            itemRender = LingHeRewardCell
        }
    )
    self.zhanshi_ten_grid:SetStartZeroIndex(false)

    --self.baodi_list = LingHeRewardListView.New(BaodiRewardRender, self.node_list.ph_baodi_list)
end

function TianShenLingHeDrawRewardView:CloseCallBack()

end

-- 注册按钮事件
function TianShenLingHeDrawRewardView:RegisterEvent()
	XUI.AddClickEventListener(self.node_list.btn_one_more, BindTool.Bind1(self.OnQuchuAgain, self))		--再来一次
	XUI.AddClickEventListener(self.node_list.skip_anim_toggle, BindTool.Bind(self.OnClinkSkipAnim, self))
end

function TianShenLingHeDrawRewardView:OnClinkSkipAnim(is_on)
    TianShenLingHeWGData.Instance:SetAnimToggleData(not is_on)
end

-- function TianShenLingHeDrawRewardView:SetDataType(cur_count)
--     self.cur_count = cur_count
-- end

-- 再来一次
function TianShenLingHeDrawRewardView:OnQuchuAgain()
    local index = TianShenLingHeWGData.Instance:CacheOrGetDrawIndex()
    TianShenLingHeWGCtrl.Instance:ClickUseLingHeDrawItem(index, function()
        local cfg = TianShenLingHeWGData.Instance:GetDrawConsumeCfg()
        TianShenLingHeWGCtrl.Instance:SendLingHeDrawReq(TianShenLingHeWGData.DRAW_TYPE.LUCK_DRAW, cfg[index].mode)
    end)
end

function TianShenLingHeDrawRewardView:OpenCallBack()

end

function TianShenLingHeDrawRewardView:ChangeState(is_ten)
	self.node_list["ph_zhanshii_cell"]:SetActive(not is_ten)
	self.node_list["ph_zhanshii_ten_cell"]:SetActive(is_ten)
end

--刷新数据
function TianShenLingHeDrawRewardView:OnFlush()
    local btn_index = TianShenLingHeWGData.Instance:CacheOrGetDrawIndex()
    local linghe_draw_cfg = TianShenLingHeWGData.Instance:GetDrawConsumeCfg()
    self.cur_count = btn_index

    self.node_list.btn_again_txt.text.text = Language.TianShenLingHe.DrawRerwadBtnText[btn_index]
    local cur_cfg = linghe_draw_cfg[btn_index]
    if not cur_cfg then
        return
    end
    
    self.node_list.one_more_cosume.text.text = cur_cfg.cost_gold  --再来一次底下的仙玉数目

    local stuff_id = cur_cfg.stuff_id
    if stuff_id > 0 then
        self.node_list["img_zbitem"].image:LoadSprite(ResPath.GetItem(stuff_id))
        local has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id) --拥有的数量
        self.node_list["zbitem_key_num"].text.text = has_num .. "/" .. cur_cfg.stuff_num
        if has_num > 0 then
            self.node_list["zbitem_key"]:SetActive(true)
            self.node_list["xianyu_icon"]:SetActive(has_num < cur_cfg.stuff_num)
        else
            self.node_list["zbitem_key"]:SetActive(false)
            self.node_list["xianyu_icon"]:SetActive(true)
        end
    else
        self.node_list["zbitem_key"]:SetActive(false)
        self.node_list["xianyu_icon"]:SetActive(true)
    end

    --self.baodi_list:SetDataList({}) --数据清一下
    self:AdjustPosition()
	self:ResetItemData()
end


function TianShenLingHeDrawRewardView:AdjustPosition()
    if self.cur_count == 1 then   		--寻宝1次
        self:ChangeState(false)
    elseif self.cur_count == 2 then 		--寻宝10次
        self:ChangeState(true)
    end
    self:ResetTotalValueRootScale()
end

function TianShenLingHeDrawRewardView:ResetItemData()
	local is_not_skip = TianShenLingHeWGData.Instance:GetAnimToggleData()
    self.node_list.skip_anim_toggle.toggle.isOn = not is_not_skip
    self.node_list.ph_zhanshii_cell.scroll_rect.enabled = true

	local linghe_list = {}
	linghe_list = TianShenLingHeWGData.Instance:GetLingHeDrawResultInfo() or {}  --服务器下发的奖励列表

    reword_count = #linghe_list
	if self.cur_count == 2 then
		if nil ~= self.zhanshi_ten_grid then
            ani_ten_flag_t = {}
			self.zhanshi_ten_grid:SetDataList(linghe_list)
			--self.zhanshi_ten_grid:CancleAllSelectCell()
			if self.ten_time_quest then
				GlobalTimerQuest:CancelQuest(self.ten_time_quest)
				self.ten_time_quest = nil
			end
            ani_ten_count = 1
            -- if self.has_baodi and not self.is_mingwen then
            --     self:ShowBaodiAni()
            -- end
			if is_not_skip then
				self.ten_time_quest = GlobalTimerQuest:AddTimesTimer(function()
					self:DoCellAnimTen(true)
					end, ANI_SPEED, reword_count )
			else
				for i = 1, reword_count do
					self:DoCellAnimTen(false)
				end

                self:DoTotalValueRootTween(false)
			end
		end
    else
		if nil ~= self.zhanshi_grid then
            ani_ten_flag_t = {}
			self.zhanshi_grid:SetDataList(linghe_list)
            self.zhanshi_grid:JumptToPrecent(1)
			--self.zhanshi_grid:CancleAllSelectCell()
			if self.time_quest then
				GlobalTimerQuest:CancelQuest(self.time_quest)
				self.time_quest = nil
			end
			ani_count = 1
            scroll_verticalNormalizedPosition = 1
            if is_not_skip then
                self.time_quest = GlobalTimerQuest:AddTimesTimer(function()
                    self:DoCellAnim(true)
                    end, ANI_SPEED, reword_count)
            else
                for i = 1, reword_count do
                    self:DoCellAnim(false)
                end

                self:DoTotalValueRootTween(false)
            end

			if self.move_scroll_quest then
				GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
				self.move_scroll_quest = nil
			end
            self.move_tween_complete = false

			if reword_count > 30 and is_not_skip then
				self.move_scroll_quest = GlobalTimerQuest:AddTimesTimer(function()
					self:MoveScroll()
					if scroll_verticalNormalizedPosition <= 0 then
						scroll_verticalNormalizedPosition = 0
						if self.move_scroll_quest then
							GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
							self.move_scroll_quest = nil
						end
					end
				end, 0.03, 999999)
			end
		end
    end
end


function TianShenLingHeDrawRewardView:ShowAllCellImmediately()
	local grid = self.cur_count == 2 and self.zhanshi_ten_grid or self.zhanshi_grid
	if not grid then
		return
	end
	if not reword_count or reword_count <=0 then
		return
	end
	for i = 1, reword_count do
		local cell = self.zhanshi_grid:GetCell(i)
		if cell ~= nil and cell:GetData() ~= nil then
			cell.view.transform.localScale = Vector3(1, 1, 1)
			cell:SetActive(true)
		end
	end
    reword_count = 0
end


function TianShenLingHeDrawRewardView:DoCellAnimTen(do_tween)
    local cell = self.zhanshi_ten_grid:GetCell(ani_ten_count)

    ani_ten_flag_t[ani_ten_count] = true
	ani_ten_count = ani_ten_count + 1
	if cell ~= nil and cell:GetData() ~= nil then
		if do_tween then
			cell.view.transform.localScale = Vector3(2.5, 2.5, 2.5)
			cell.view.transform:DOScale(Vector3(1, 1, 1), 0.2)
		else
			cell.view.transform.localScale = Vector3(1, 1, 1)
		end
		--特效
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0),nil,nil)
		cell:SetActive(true)
    end

    if do_tween and ani_ten_count == reword_count + 1 then
        self:DoTotalValueRootTween(true)
    end

    -- if ani_ten_count == reword_count + 1 and self.has_baodi and not is_mingwen then
    --     self:ShowBaodiAni()
    -- end
end

function TianShenLingHeDrawRewardView:SetRootNodeActive(value)
	SafeBaseView.SetRootNodeActive(self, value)
	if value and reword_count > 0 then
		self:ShowAllCellImmediately()
	end
end

function TianShenLingHeDrawRewardView:DoCellAnim(do_tween)
    local cell = self.zhanshi_grid:GetCell(ani_count)
    ani_ten_flag_t[ani_count] = true
    ani_count = ani_count + 1
    if cell ~= nil and cell:GetData() ~= nil then
        cell:SetActive(true)
        if do_tween then
            cell.view.transform.localScale = Vector3(2.5, 2.5, 2.5)
            cell.view.transform:DOScale(Vector3(1, 1, 1), 0.15)
        else
            cell.view.transform.localScale = Vector3(1, 1, 1)
        end
        --特效
        local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
        if do_tween or ani_baodi_count <= 31 then
            EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0),nil,nil)
        end
    end

    if do_tween and ani_count == reword_count + 1 then
        self:DoTotalValueRootTween(true)
    end

    -- if ani_count == reword_count + 1 and self.has_baodi and not is_mingwen then
    --     print_error("ani_count",ani_count)
    --     self:ShowBaodiAni()
    -- end
end

-- local total = -70
function TianShenLingHeDrawRewardView:MoveScroll()
	if ani_count > (col_num * row_num) then --and self.move_tween_complete then --
		scroll_verticalNormalizedPosition = scroll_verticalNormalizedPosition - lerp
	else
		scroll_verticalNormalizedPosition = 1
	end
	if self.node_list.ph_zhanshii_cell then
		self.node_list.ph_zhanshii_cell.scroll_rect.verticalNormalizedPosition = scroll_verticalNormalizedPosition
	end
end

function TianShenLingHeDrawRewardView:ResetTotalValueRootScale()
    self.node_list.total_price_root.rect.localScale = Vector3(0, 0, 0)
end

function TianShenLingHeDrawRewardView:DoTotalValueRootTween(is_tween)
    local function set_total_value(is_tween)
        local linghe_list = TianShenLingHeWGData.Instance:GetLingHeDrawResultInfo() or {} --服务器下发的奖励列表

        local total_value = 0
        if not IsEmptyTable(linghe_list) then
            for k, v in pairs(linghe_list) do
                total_value = total_value + ItemWGData.Instance:GetItemValueByItemId(v.item_id)
            end
        end

        if is_tween then
            UITween.DONumberTo(self.node_list.desc_total_price.text, 0, tonumber(total_value), 0.5,
                function(num)
                    self.node_list.desc_total_price.text.text = math.floor(num)
                end, function()
                    self.node_list.desc_total_price.text.text = total_value
                end
            )
        else
            self.node_list.desc_total_price.text.text = total_value
        end
    end

    if is_tween then
        self.node_list.total_price_root.rect.localScale = Vector3(2.5, 2.5, 2.5)
        self.node_list.desc_total_price.text.text = ""
        self.node_list.total_price_root.rect:DOScale(Vector3(1, 1, 1), 0.15):OnComplete(function()
            set_total_value(true)
        end)
    else
        self.node_list.total_price_root.rect.localScale = Vector3(1, 1, 1)
        set_total_value(false)
    end
end


local effect_list = {
    [GameEnum.ITEM_COLOR_ORANGE] = "UI_fuwen_kuang_00", --橙色
    [GameEnum.ITEM_COLOR_RED] = "UI_fuwen_kuang_01", --红色
}

LingHeRewardGrid = LingHeRewardGrid or BaseClass(AsyncBaseGrid)

-- 获得指定的格子
function LingHeRewardGrid:GetCell(index)
    for k, v in pairs(self.cell_list) do
        local row = math.floor((index - 1) / self.columns)
        if row == v:GetRows() and v:GetActive()  then
            for k1, v1 in pairs(v:GetAllCell()) do
                if v1:GetIndex() == index then
                    return v1
                end
            end
        end
    end
	return nil
end


LingHeRewardListView = LingHeRewardListView or BaseClass(AsyncListView)

-- 获得某个索引下的item
function LingHeRewardListView:GetItemAt(cell_index)
    for k, v in pairs(self.cell_list) do
        if v:GetIndex() == cell_index then
            return v
        end
    end
    return nil
end

----------------------------------------------LingHeRewardCell----------------------------------------------
LingHeRewardCell = LingHeRewardCell or BaseClass(BaseRender)

function LingHeRewardCell:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end
end

function LingHeRewardCell:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function LingHeRewardCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self:SetVisible(ani_ten_flag_t[self.index] == true)
    self.item:SetData(self.data)

    self.node_list.desc_value_str.text.text = ItemWGData.Instance:GetItemValueByItemId(self.data.item_id)
end

----------------------------------------TreasureGrid----------------------------------