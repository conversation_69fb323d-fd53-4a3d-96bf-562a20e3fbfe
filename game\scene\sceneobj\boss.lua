Boss = Boss or BaseClass(Monster)

function Boss:__init(boss_vo)
	self.shield_obj_type = ShieldObjType.Boss
	self.followui_class = BossFollow
	-- local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.vo.monster_id]
end

function Boss:__delete()

end

function Boss:CreateFollowUi()
	Monster.CreateFollowUi(self)
	self.follow_ui:SetIsBoss(true)
end

function Boss:AddBuff(buff_type,product_id)
	if buff_type >= 5 and buff_type <= 9 then
		--Boss自带5个免疫buff（免疫眩晕、定身、沉默、迟缓、冰冻）
		--策划需求，这5个自带免疫buff都不需要显示出来
		return
	end
	Monster.AddBuff(self, buff_type,product_id)
end

function Boss:IsBoss()
	return true
end