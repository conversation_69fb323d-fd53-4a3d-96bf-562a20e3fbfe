WorldServerWGData = WorldServerWGData or BaseClass()

-- 深渊boss
WorldServerWGData.SHENYUAN_BOSS_REQ_TYPE =
{
    SHENYUAN_BOSS_REQ_TYPE_INFO = 0,				-- 请求信息
    SHENYUAN_BOSS_REQ_TYPE_ENTER = 1,				--进入场景,param_0 为场景seq
	SHENYUAN_BOSS_REQ_TYPE_CONCERN = 2,             --关注boss param_0 seq  
	SHENYUAN_BOSS_REQ_TYPE_UN_CONCERN = 3,          --取消关注boss param_0 seq  
};

function WorldServerWGData:__init()
	if WorldServerWGData.Instance then
		error("[WorldServerWGData] Attempt to create singleton twice!")
		return
	end

	self.is_pick_state = 0
	WorldServerWGData.Instance = self
end

function WorldServerWGData:__delete()
	WorldServerWGData.Instance = nil
end

function WorldServerWGData:SaveRoleData(protocol)
    self.is_pick_state = protocol.is_pick_state
end

function WorldServerWGData:GetCanPickBox()
	return self.is_pick_state == 0
end

function WorldServerWGData:ClearPickState()
	self.is_pick_state = 0
end

function WorldServerWGData:SetCurFocusViewData(data)
	self.cur_focus_data = data
end

function WorldServerWGData:GetCurFocusViewData()
	return self.cur_focus_data
end