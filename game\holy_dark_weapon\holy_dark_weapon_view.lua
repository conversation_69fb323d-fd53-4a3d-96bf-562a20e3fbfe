HolyDarkWeaponView = HolyDarkWeaponView or BaseClass(SafeBaseView)
function HolyDarkWeaponView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self.is_safe_area_adapter = true


	if self.view_name == GuideModuleName.HolyWeapon then
		self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "layout_holy_bigbg")
		self:AddViewResource(TabIndex.holy_dark_seal, "uis/view/holy_dark_ui_prefab", "layout_holy_seal_view")
		self:AddViewResource({ TabIndex.holy_dark_equip_bag,
			TabIndex.holy_dark_equip_skill,
		}, "uis/view/holy_dark_ui_prefab", "layout_holy_equip_list_panel")
		self:AddViewResource(TabIndex.holy_dark_equip_bag, "uis/view/holy_dark_ui_prefab", "layout_holy_equip_bag_view")
		self:AddViewResource(TabIndex.holy_dark_equip_skill, "uis/view/holy_dark_ui_prefab",
			"layout_holy_equip_skill_view")
		self:AddViewResource(TabIndex.holy_dark_qianghua, "uis/view/holy_dark_ui_prefab", "layout_holy_qianghua_view")
		self:AddViewResource(TabIndex.holy_dark_upstar, "uis/view/holy_dark_ui_prefab", "layout_holy_upstar_view")
		self:AddViewResource(TabIndex.holy_dark_jinjie, "uis/view/holy_dark_ui_prefab", "layout_holy_jinjie_view")
	else
		self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "layout_dark_bigbg")
		self:AddViewResource(TabIndex.holy_dark_seal, "uis/view/holy_dark_ui_prefab", "layout_dark_seal_view")
		self:AddViewResource({ TabIndex.holy_dark_equip_bag,
			TabIndex.holy_dark_equip_skill,
		}, "uis/view/holy_dark_ui_prefab", "layout_dark_equip_list_panel")
		self:AddViewResource(TabIndex.holy_dark_equip_bag, "uis/view/holy_dark_ui_prefab", "layout_dark_equip_bag_view")
		self:AddViewResource(TabIndex.holy_dark_equip_skill, "uis/view/holy_dark_ui_prefab",
			"layout_dark_equip_skill_view")
		self:AddViewResource(TabIndex.holy_dark_qianghua, "uis/view/holy_dark_ui_prefab", "layout_dark_qianghua_view")
		self:AddViewResource(TabIndex.holy_dark_upstar, "uis/view/holy_dark_ui_prefab", "layout_dark_upstar_view")
		self:AddViewResource(TabIndex.holy_dark_jinjie, "uis/view/holy_dark_ui_prefab", "layout_dark_jinjie_view")
	end

	self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "VerticalTabbar")
	self:AddViewResource(0, "uis/view/holy_dark_ui_prefab", "HorizontalTabbar")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")

	self.holy_raw_bg = {
		[TabIndex.holy_dark_seal] = "a2_cbg_holy_fy_bg",
		[TabIndex.holy_dark_equip_bag] = "a2_cbg_holy_zb_bg",
		[TabIndex.holy_dark_equip_skill] = "a2_cbg_holy_zb_bg",
		[TabIndex.holy_dark_qianghua] = "a2_cbg_holy_qh_bg",
		[TabIndex.holy_dark_upstar] = "a2_cbg_holy_qh_bg",
		[TabIndex.holy_dark_jinjie] = "a2_cbg_holy_qh_bg",
	}

	self.dark_raw_bg = {
		[TabIndex.holy_dark_seal] = "a2_cbg_dark_fy_bg",
		[TabIndex.holy_dark_equip_bag] = "a2_cbg_dark_zb_bg",
		[TabIndex.holy_dark_equip_skill] = "a2_cbg_dark_zb_bg",
		[TabIndex.holy_dark_qianghua] = "a2_cbg_dark_qh_bg",
		[TabIndex.holy_dark_upstar] = "a2_cbg_dark_qh_bg",
		[TabIndex.holy_dark_jinjie] = "a2_cbg_dark_qh_bg",
	}
end

function HolyDarkWeaponView:__delete()

end

function HolyDarkWeaponView:ReleaseCallBack()
	if self.weapon_show_list then
		self.weapon_show_list:DeleteMe()
		self.weapon_show_list = nil
	end

	if self.holydark_tabbar then
		self.holydark_tabbar:DeleteMe()
		self.holydark_tabbar = nil
	end

	if self.holy_dark_wear_list ~= nil then
		for k, v in pairs(self.holy_dark_wear_list) do
			v:DeleteMe()
		end
		self.holy_dark_wear_list = nil
	end

	if self.equip_relic_show_model then
		self.equip_relic_show_model:DeleteMe()
		self.equip_relic_show_model = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self.jump_remind = nil
	self.select_relic_seq = nil
	self.cache_equip_seq = nil

	self:DeleteHolyDarkSealView()
	self:DeleteHolyDarkEquipBagView()
	self:DeleteHolyDarkEquipSkillView()
	self:DeleteHolyDarkQiangHuaView()
	self:DeleteHolyDarkUpStarView()
	self:DeleteHolyDarkJinJieView()

	HolyDarkWeaponWGData.Instance:ClearSkillSelectBagParam()
end

function HolyDarkWeaponView:OpenCallBack()
	if self.view_name == GuideModuleName.HolyWeapon then
		HolyDarkWeaponWGData.Instance:SetCurWeaponType(HolyDarkWeaponWGData.Weapon_type.HolyType)
	else
		HolyDarkWeaponWGData.Instance:SetCurWeaponType(HolyDarkWeaponWGData.Weapon_type.DarkType)
	end

	self.jump_remind = true
end

function HolyDarkWeaponView:CloseCallBack()
	self.jump_remind = nil
	self.select_relic_seq = nil
end

function HolyDarkWeaponView:LoadCallBack()
	self.select_relic_seq = -1
	local weapon_type = HolyDarkWeaponWGData.Instance:GetCurWeaponType()

	if not self.weapon_show_list then
		self.weapon_show_list = AsyncListView.New(HolyDarkWeaponRender, self.node_list["weapon_show_list"])
		self.weapon_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectHolyDarkWeaponHandler, self))
		self.weapon_show_list:SetDefaultSelectIndex(nil)
	end

	if not self.holydark_tabbar then
		self.holydark_tabbar = HolyDarkWeaponTabbar.New(self.node_list)
		self.holydark_tabbar:SetSelectCallback(BindTool.Bind(self.ToSelectTabIndex, self))
		if weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType then
			self.holydark_tabbar:InitList(Language.HolyDarkWeapon.VerTabGrop, Language.HolyDarkWeapon.HorTabGrop_Holy)
		else
			self.holydark_tabbar:InitList(Language.HolyDarkWeapon.VerTabGrop, Language.HolyDarkWeapon.HorTabGrop_Dark)
		end
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = { show_bind_gold = true, show_coin = true, show_silver_ticket = true, }
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.node_list.title_view_name.text.text = weapon_type == HolyDarkWeaponWGData.Weapon_type.HolyType and
	Language.HolyDarkWeapon.HolyTitle or Language.HolyDarkWeapon.DarkTitle
end

function HolyDarkWeaponView:LoadIndexCallBack(index)
	-- 装备列表
	if index == TabIndex.holy_dark_equip_bag or
		index == TabIndex.holy_dark_equip_skill then
		if not self.holy_dark_wear_list then
			self.holy_dark_wear_list = {}
			for part = 0, 7 do
				self.holy_dark_wear_list[part] = HolyDarkEquipEquipCell.New(self.node_list["equip_" .. part])
				self.holy_dark_wear_list[part]:SetIndex(part)
				self.holy_dark_wear_list[part]:SetClickCallBack(BindTool.Bind(self.OnClickEquipCallBack, self))
			end

			if nil == self.equip_relic_show_model then
				self.cache_equip_seq = -1
				self.equip_relic_show_model = RoleModel.New()
				local display_data = {
					parent_node = self.node_list["equip_display"],
					camera_type = MODEL_CAMERA_TYPE.BASE,
					-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
					rt_scale_type = ModelRTSCaleType.M,
					can_drag = true,
				}
				
				self.equip_relic_show_model:SetRenderTexUI3DModel(display_data)
				-- self.equip_relic_show_model:SetUI3DModel(self.node_list["equip_display"].transform,
				-- 	self.node_list["equip_EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
				self:AddUiRoleModel(self.equip_relic_show_model)
			end
		end
	end

	if index == TabIndex.holy_dark_seal then
		self:InitHolyDarkSealView()
	elseif index == TabIndex.holy_dark_equip_bag then
		self:InitHolyDarkEquipBagView()
	elseif index == TabIndex.holy_dark_equip_skill then
		self:InitHolyDarkEquipSkillView()
	elseif index == TabIndex.holy_dark_qianghua then
		self:InitHolyDarkQiangHuaView()
	elseif index == TabIndex.holy_dark_upstar then
		self:InitHolyDarkUpStarView()
	elseif index == TabIndex.holy_dark_jinjie then
		self:InitHolyDarkJinJieView()
	end
end

function HolyDarkWeaponView:ShowIndexCallBack(index)
	local bg_res
	if self.view_name == GuideModuleName.HolyWeapon then
		bg_res = self.holy_raw_bg[index] or self.holy_raw_bg[TabIndex.holy_dark_seal]
	else
		bg_res = self.dark_raw_bg[index] or self.dark_raw_bg[TabIndex.holy_dark_seal]
	end

	self.node_list["bg_shenji"].raw_image:LoadSpriteAsync(ResPath.GetRawImagesPNG(bg_res))
	if index == TabIndex.holy_dark_seal then
		self:DoHolyDarkSealViewAnim()
	elseif index == TabIndex.holy_dark_equip_skill then
		HolyDarkWeaponWGData.Instance:ClearSkillSelectBagParam()
	elseif index == TabIndex.holy_dark_qianghua then
		self:DoHolyDarkQiangHuaViewAnim()
	elseif index == TabIndex.holy_dark_upstar then
		self:DoHolyDarkUpStarViewAnim()
	elseif index == TabIndex.holy_dark_jinjie then
		self:DoHolyDarkJinJieViewAnim()
	end
end

function HolyDarkWeaponView:OnFlush(param_t, index)
	local show_info = HolyDarkWeaponWGData.Instance:GetShowWeaponInfo()
	if not IsEmptyTable(show_info) and self.weapon_show_list then
		self.weapon_show_list:SetDataList(show_info)
	end
	for k, v in pairs(param_t) do
		if k == "all" then
			if self.jump_remind then
				local jump_index = HolyDarkWeaponWGData.Instance:GetSelectIndexList()
				self.weapon_show_list:JumpToIndex(jump_index)
				self.jump_remind = nil
			end

			self:FlushViewByIndex()
		end
	end
end

-- 当前选择圣器索引
function HolyDarkWeaponView:GetCurSelectWeaponSeq()
	return self.select_relic_seq
end

function HolyDarkWeaponView:FlushViewByIndex()
	if self.select_relic_seq == nil or self.select_relic_seq == -1 then
		return
	end

	local refresh_ver, refresh_hor = true, true
	self.holydark_tabbar:RefreshAllCellData(refresh_ver, refresh_hor)

	if self.show_index == TabIndex.holy_dark_seal then
		self:FlushHolyDarkSealView()
	elseif self.show_index == TabIndex.holy_dark_equip_bag then
		self:FlushHolyDarkEquipBagView()
		self:FlushHolyEquipWearPanel()
		self:FlushEquipShowModel()
	elseif self.show_index == TabIndex.holy_dark_equip_skill then
		self:FlushHolyDarkEquipSkillView()
		self:FlushHolyEquipWearPanel()
		self:FlushEquipShowModel()
	elseif self.show_index == TabIndex.holy_dark_qianghua then
		self:FlushHolyDarkQiangHuaView()
	elseif self.show_index == TabIndex.holy_dark_upstar then
		self:FlushHolyDarkUpStarView()
	elseif self.show_index == TabIndex.holy_dark_jinjie then
		self:FlushHolyDarkJinJieView()
	end
end

function HolyDarkWeaponView:ToSelectTabIndex(tab_index)
	self:ClearData()
	if self.show_index == tab_index and self:IsLoadedIndex(tab_index) then
		self:ShowIndexCallBack(tab_index)
		self:__MergeFlushParam(tab_index, "all", { "all" }, false)
	end

	self:ChangeToIndex(tab_index)
end

function HolyDarkWeaponView:ClearData()
	self.qianghua_select_data = nil
	self.qianghua_select_index = nil

	self.upstar_select_index = nil
	self.upstar_select_data = nil

	self.jinjie_select_data = nil
	self.jinjie_select_index = nil
end

function HolyDarkWeaponView:OnSelectHolyDarkWeaponHandler(item)
	if nil == item and nil == item.data then
		return
	end

	if self.select_relic_seq and self.select_relic_seq == item.data.seq then
		return
	end

	self.select_relic_seq = item.data.seq
	local hor_bar_index = HolyDarkWeaponWGData.Instance:GetOpenViewJump(self.select_relic_seq)
	self.holydark_tabbar:TryJumpToTabbar(hor_bar_index)
end

function HolyDarkWeaponView:OnClickEquipCallBack(cell)
	if cell == nil or cell.data == nil then
		return
	end

	local data = cell.data
	if data.item_id == nil or data.item_id <= 0 then
		local show_cfg = HolyDarkWeaponWGData.Instance:GetRelicEquipEmptyItemData(self.select_relic_seq, cell.index)
		if not IsEmptyTable(show_cfg) then
			local show_data = { item_id = show_cfg.item_id }
			TipWGCtrl.Instance:OpenItem(show_data)
		end

		return
	end

	TipWGCtrl.Instance:OpenItem(data, ItemTip.FROM_RELIC_EQUIP)
end

-- 刷新穿戴
function HolyDarkWeaponView:FlushHolyEquipWearPanel()
	local wear_list = HolyDarkWeaponWGData.Instance:GetHolyDarkEquipWearList(self.select_relic_seq)
	for i = 0, 7 do
		local cell = self.holy_dark_wear_list[i]
		if cell then
			cell:SetData(wear_list[i])
		end
	end

	local cap = HolyDarkWeaponWGData.Instance:GetRelicCapabilityBySeq(self.select_relic_seq)
	-- self.node_list.equip_cap_value.text.text = cap
	-- self.node_list.equip_capability_pa:SetActive(cap > 0)
end

--刷新裝備模型
function HolyDarkWeaponView:FlushEquipShowModel()
	local relic_cfg = HolyDarkWeaponWGData.Instance:GetWeaponCfgBySeq(self.select_relic_seq)
	if IsEmptyTable(relic_cfg) then
		return
	end

	if self.cache_equip_seq ~= self.select_relic_seq then
		local bundle, asset = ResPath.GetRelicModel(relic_cfg.model_id)
		self.equip_relic_show_model:SetMainAsset(bundle, asset)
		self.equip_relic_show_model:FixToOrthographic(self.root_node_transform)
		self.equip_relic_show_model:PlayJianZhenAction()
		self.cache_equip_seq = self.select_relic_seq
	end
end

-----------------------------
HolyDarkWeaponRender = HolyDarkWeaponRender or BaseClass(BaseRender)
function HolyDarkWeaponRender:__init()

end

function HolyDarkWeaponRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["lock"], BindTool.Bind(self.OnClicNoOpen, self))
end

function HolyDarkWeaponRender:__delete()

end

function HolyDarkWeaponRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.nor_name_text.text.text = self.data.name
	self.node_list.hl_name_text.text.text = self.data.name
	local is_remind = HolyDarkWeaponWGData.Instance:ShowRelicAllRemind(self.data.seq)
	self.node_list.remind:SetActive(is_remind)
	-- local role_level = RoleWGData.Instance:GetRoleLevel()
	-- self.node_list.lock:SetActive(role_level < self.data.open_level)
end

function HolyDarkWeaponRender:OnSelectChange(is_select)
	if not self.data then
		return
	end

	self.node_list.select_hl:SetActive(is_select)
	self.node_list.nor_img:SetActive(not is_select)
end

function HolyDarkWeaponRender:OnClicNoOpen()
	local role_level = RoleWGData.Instance:GetRoleLevel()

	if self.data.open_level > role_level then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.HolyDarkWeapon.OpenLevel, self.data.open_level))
		return
	end
end

--- 圣器装备格子
local equip_icon_map = {
	[0] = "a3_zb_5", --武器
	[1] = "a3_zb_18", --副武
	[2] = "a3_zb_2", --衣服
	[3] = "a3_zb_0", --头盔
	[4] = "a3_zb_9", --腰带
	[5] = "a3_zb_7", --护臂
	[6] = "a3_zb_13", --裤子
	[7] = "a3_zb_4", --鞋子
}
HolyDarkEquipEquipCell = HolyDarkEquipEquipCell or BaseClass(BaseRender)
function HolyDarkEquipEquipCell:LoadCallBack()
	if not self.equip_item then
		self.equip_item = ItemCell.New(self.node_list["item_node"])
	end

	XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClick, self))
	self.play_ani = true
end

function HolyDarkEquipEquipCell:__delete()
	if self.equip_item then
		self.equip_item:DeleteMe()
		self.equip_item = nil
	end

	self.play_ani = nil
end

function HolyDarkEquipEquipCell:OnFlush()
	if self.play_ani then
		self:PlayAni()
		self.play_ani = false
	end

	if self.data == nil or self.data.item_id == nil or self.data.item_id <= 0 then
		self.equip_item:ClearData()
		local bundle, asset = ResPath.GetFairyLandEquipImages("a1_zzlz_tbdi")
		self.equip_item:SetCellBg(bundle, asset)
		bundle = "uis/images/icon_atlas"
		asset = equip_icon_map[self.index]
		self.equip_item:SetItemIcon(bundle, asset)
		self.node_list["remind"].image.enabled = false
		self.node_list["level"].text.text = ""
		return
	end

	self.equip_item:SetData(self.data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.equip_item:SetLeftTopImg(self.data.star_level or 0)
end

function HolyDarkEquipEquipCell:PlayAni()
	if not self.index then
		return
	end

	local time = 0.5 * (self.index + 1)
	local canvas_group = self.node_list["root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 0
	local tween_alpah = canvas_group:DoAlpha(0, 1, time)
end
