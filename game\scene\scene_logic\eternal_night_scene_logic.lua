EternalNightSceneLogic = EternalNightSceneLogic or BaseClass(CommonFbLogic)

local GetMoveObjTime = 10		--获取移动对象的时间间隔

function EternalNightSceneLogic:__init()
	self.guaji_need_stop_time = 0
	self.next_get_all_move_obj_time = 0					-- 下次获取移动对象的时间
end

function EternalNightSceneLogic:__delete()
	self.next_get_all_move_obj_time = 0
end

function EternalNightSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:Open(GuideModuleName.EternalNightTaskView)
	self:SetLeaveFbTip(true)
	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))
    self.obj_dead_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjDead, self))
	self.guaji_change_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.BindGuaJiFireEvent,self))
	self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))

	local self_info = EternalNightWGData.Instance:GetSelfInfo()
    local other_cap = self_info.equip_attr_info and self_info.equip_attr_info.equip_capability or 0
	local main_cap = RoleWGData.Instance:GetOriginCapability()
	RoleWGData.Instance:SetAttr("capability", main_cap + other_cap)

	local view = EternalNightWGCtrl.Instance:GetTaskView()
	ViewManager.Instance:AddMainUIRightTopChangeList(view)
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
end

function EternalNightSceneLogic:Out()
	CommonFbLogic.Out(self)
	GlobalEventSystem:UnBind(self.fly_down_end_event)
	self.fly_down_end_event = nil

	GlobalEventSystem:UnBind(self.obj_dead_event)
	self.obj_dead_event = nil

	GlobalEventSystem:UnBind(self.guaji_change_event)
	self.guaji_change_event = nil

	GlobalEventSystem:UnBind(self.role_enter_event)
	self.role_enter_event = nil

	local view = EternalNightWGCtrl.Instance:GetTaskView()
	local view_manager = ViewManager.Instance
	view_manager:RemoveMainUIRightTopChangeList(view)
	view_manager:RemoveMainUIFuPingChangeList(view)

	view_manager:Close(GuideModuleName.EternalNightTaskView)
	view_manager:Close(GuideModuleName.EternalNightEquipView)
	view_manager:Close(GuideModuleName.EternalNightRankView)
	view_manager:Close(GuideModuleName.EternalNightRewardView)

	local main_cap = RoleWGData.Instance:GetOriginCapability()
	RoleWGData.Instance:SetAttr("capability", main_cap)
	-- EternalNightWGData.Instance:SetCurEnterActType(nil)
	EternalNightWGData.Instance:SetGoToBossFun(nil)
end

function EternalNightSceneLogic:BindGuaJiFireEvent(guaji_type)
	if guaji_type == GuajiType.Auto then
		if Status.NowTime >= self.next_get_all_move_obj_time then
			self.next_get_all_move_obj_time = Status.NowTime + GetMoveObjTime
			Scene.SendGetAllObjMoveInfoReq()
		end
	end
end

-- 是否可以拉取移动对象信息
function EternalNightSceneLogic:CanGetMoveObj()
	-- return true
	return false
end

function EternalNightSceneLogic:OnFlyDownEnd()
	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
	end
	local goto_boss_fun	= EternalNightWGData.Instance:GetGoToBossFun()
	if goto_boss_fun then
		goto_boss_fun()
		EternalNightWGData.Instance:SetGoToBossFun(nil)
	else
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function EternalNightSceneLogic:OnRoleEnter(obj_id)
	local main_role = Scene.Instance:GetMainRole()
	if not main_role:IsFightState() and GuajiCache.guaji_type == GuajiType.Auto then
		local role = Scene.Instance:GetObj(obj_id)
		local role_vo = role:GetVo()
		if role and role:IsRole() and Scene.Instance:IsEnemy(role) then
			main_role:StopMove()
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end
	
end

function EternalNightSceneLogic:OnObjDead(obj)
	if not obj then
        return
    end
    --在诛仙战场 杀死怪物后停留一下
    if obj:IsMonster() then
        self:SetEternalNightTime()
    end
end

-- 诛仙战场 击杀怪物停留等拾取
function EternalNightSceneLogic:ClearEternalNightTime()
    self.guaji_need_stop_time = 0
end

function EternalNightSceneLogic:SetEternalNightTime()
    local main_role = Scene.Instance:GetMainRole()
    if main_role ~= nil and not main_role:IsFightState() and GuajiCache.guaji_type == GuajiType.Auto then
        main_role:StopMove()
        self.guaji_need_stop_time = Status.NowTime + 1
    end
    
end

function EternalNightSceneLogic:IsInEternalNightTime()
    local is_in = false
    if self.guaji_need_stop_time ~= nil then
        is_in = self.guaji_need_stop_time >= Status.NowTime
    end

    return is_in
end

function EternalNightSceneLogic:GetGuajiNeesStopTime()
	return self.guaji_need_stop_time
end

function EternalNightSceneLogic:GetGuajiPos()
	local pos_x,pos_y = self:GetGuiJiMonsterPos()
	return pos_x,pos_y
end

-- 获取挂机打怪的位置
function EternalNightSceneLogic:GetGuiJiMonsterPos()
    local target_distance = 1000 * 1000
    local target_x = nil
    local target_y = nil
    local x, y = Scene.Instance:GetMainRole():GetLogicPos()

    local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
    for k, v in pairs(obj_move_info_list) do
        local vo = v:GetVo()
        local not_block = not AStarFindWay:IsBlock(vo.pos_x, vo.pos_y)
        if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) and self:ConditionScanMonster(vo) and not_block then
            local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
            if distance < target_distance then
                target_x = vo.pos_x
                target_y = vo.pos_y
                target_distance = distance
            end
        end
    end
    return target_x, target_y
end

function EternalNightSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	-- 杀了怪物之后，要原地停留一秒，捡东西
    if self.guaji_need_stop_time >= Status.NowTime then
        local stop_wait_pick = false
        local scene_config = Scene.Instance:GetCurFbSceneCfg()
        if scene_config ~= nil and scene_config.pb_shouhu ~= nil then
            stop_wait_pick = EquipWGData.Instance:CanAutoPick(scene_config.pb_shouhu == 1)
        end
        if GuajiWGCtrl.Instance:CheckCanPick() and not stop_wait_pick then
            self:ClearEternalNightTime()
        end
        return
    end
    self:ClearEternalNightTime()
end

function EternalNightSceneLogic:GetGuajiCharacter()
	if self:IsInEternalNightTime() then
		return nil, nil, true
	end
	local target_obj = nil
	local dis = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local is_need_stop = false

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return target_obj, dis, is_need_stop
	end

	local attack_mode = main_role:GetVo().attack_mode
	if attack_mode == ATTACK_MODE.PEACE then
		local obj = self:GetMonster()
		if obj ~= nil and self:IsEnemy(obj, main_role) then
			target_obj = obj
		end
	elseif attack_mode == ATTACK_MODE.GUILD or attack_mode == ATTACK_MODE.ALL then
		target_obj = self:GetRoleEnemy()
		if target_obj == nil then
			local obj = self:GetMonster()
			if obj ~= nil and self:IsEnemy(obj, main_role) then
				target_obj = obj
			end
		else
			is_need_stop = true
		end
	end

	return target_obj, dis, is_need_stop
end

-- 是否达到指定条件搜索全地图怪
function EternalNightSceneLogic:ConditionScanMonster(vo)
	if self:IsInEternalNightTime() then
		return false
	else
		return true
	end
end
