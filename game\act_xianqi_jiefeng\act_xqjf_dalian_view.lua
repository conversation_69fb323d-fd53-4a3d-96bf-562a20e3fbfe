

XianQiJieFengDaLianView = XianQiJieFengDaLianView or BaseClass(SafeBaseView)

function XianQiJieFengDaLianView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/act_xianqi_jiefeng_ui_prefab", "layout_dalian")
end

function XianQiJieFengDaLianView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("xqjf_dalian_down_time")
end

function XianQiJieFengDaLianView:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])

	local activity_other_cfg = ActXianQiJieFengWGData.Instance:GetActivityThemeOtherCfg()
	if activity_other_cfg and activity_other_cfg[1] then
		local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(activity_other_cfg[1].bz_reward_item)
		self.reward_list:SetDataList(reward_list)
	end
end

function XianQiJieFengDaLianView:CloseCallBack()
	if self.node_list.not_tip_toggle then
		local is_not_tip = self.node_list.not_tip_toggle.toggle.isOn
		if is_not_tip then
			local uuid = RoleWGData.Instance:GetUUid()
			local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "xianqijiefeng_dalian_flag")
			PlayerPrefsUtil.SetInt(key, 1)
		end
	end
end

function XianQiJieFengDaLianView:OnFlush()
	self:RefreshView()
	self:AutoCloseView()
end

function XianQiJieFengDaLianView:RefreshView()
	local end_time, start_time = ActXianQiJieFengWGData.Instance:GetActivityInValidTime()
	local end_time_tab = os.date("*t", end_time)
	local start_time_tab = os.date("*t", start_time)

	local start_str = string.format(Language.XianQiJieFengAct.DaLianStr, start_time_tab.month, start_time_tab.day, start_time_tab.hour)
	local end_str = string.format(Language.XianQiJieFengAct.DaLianStr, end_time_tab.month, end_time_tab.day, end_time_tab.hour)
	self.node_list.activity_time.text.text = string.format(Language.XianQiJieFengAct.DaLianStr2, start_str, end_str)
end

function XianQiJieFengDaLianView:AutoCloseView()
	CountDownManager.Instance:RemoveCountDown("xqjf_dalian_down_time")
	self.node_list.tip.text.text = string.format(Language.TianShenRoad.DaLianStr3, 30)
	CountDownManager.Instance:AddCountDown("xqjf_dalian_down_time", 
		function(elapse_time, total_time)
			self.node_list.tip.text.text = string.format(Language.TianShenRoad.DaLianStr3, math.ceil(total_time - elapse_time))
		end, 
		function()
			self:Close()
		end, nil, 30, 1)
end
