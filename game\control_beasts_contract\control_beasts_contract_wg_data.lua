ControlBeastsContractWGData = ControlBeastsContractWGData or BaseClass()

local REWARD_DISPLAY = {
	BEAST = 1,
	OTHER = 2,
}


function ControlBeastsContractWGData:__init()
	if ControlBeastsContractWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[ControlBeastsWGData] attempt to create singleton twice!")
		return
	end
	ControlBeastsContractWGData.Instance = self

	self:InitCfg()
end

function ControlBeastsContractWGData:__delete()
	ControlBeastsContractWGData.Instance = nil
	self.beast_draw_info = nil
	self:DeleteCfg()
end

------------------------------------------------------------- 配置信息 --------------------------------------------------------
-- 初始化配置表
function ControlBeastsContractWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("beasts_draw_cfg_auto")
	if cfg then
		self.base_cfg = cfg.other[1]
		self.rewards_map_cfg = ListToMapList(cfg.rewards, "deed_level")
		self.deed_level_cfg = cfg.deed_level
		self.draw_mode_cfg = cfg.draw_mode
		self.extra_rewards_map_cfg = cfg.extra_rewards

		self.extra_reward_item_cache = {}
		for k, v in pairs(self.extra_rewards_map_cfg) do
			local item_id = v.item.item_id
			self.extra_reward_item_cache[item_id] = item_id
		end
	end
end

function ControlBeastsContractWGData:IsExtraRewardItem(item_id)
	return nil ~= self.extra_reward_item_cache[item_id]
end

-- 清理垃圾
function ControlBeastsContractWGData:DeleteCfg()
	self.base_cfg = nil
	self.rewards_map_cfg = nil
end

--	设置数据
function ControlBeastsContractWGData:SetBeastDrawInfo(protocol)
	self.beast_draw_info = protocol
end

-- 获取神锲等级
function ControlBeastsContractWGData:GetBeastDrawDeedLevel()
	return self.beast_draw_info and self.beast_draw_info.deed_level or 0
end

-- 获取当前选择的大奖下标
function ControlBeastsContractWGData:GetBeastDrawDeedSelectedIndex()
	return self.beast_draw_info and self.beast_draw_info.selected_index or 0
end

-- 获取当前的大奖抽奖次数
function ControlBeastsContractWGData:GetBeastDrawDeedTimes()
	return self.beast_draw_info and self.beast_draw_info.draw_times or 0
end

-- 获取当前的总抽奖次数
function ControlBeastsContractWGData:GetBeastDrawDeedTotalTimes()
	return self.beast_draw_info and self.beast_draw_info.total_draw_times or 0
end

-- 获取当前的抽奖模式
function ControlBeastsContractWGData:GetCurBeastDrawMode(mode_id)
	local real_index = mode_id - 1
	local enemy = {}
	return (self.draw_mode_cfg or enemy)[real_index] or nil
end

-- 获取当前神锲等级下的所有大奖
function ControlBeastsContractWGData:GetCurBeastDrawExtraRewards(select_index)
	local enemy = {}
	return (self.extra_rewards_map_cfg or enemy)[select_index] or nil
end

----------------------------------------------------------------------
---获取基础表
function ControlBeastsContractWGData:GetBaseCfg()
	return self.base_cfg
end

-- 获取当前的神锲等级对应的奖励ID
function ControlBeastsContractWGData:GetRewardCfgById(deed_level)
	local enemy = {}
	return (self.rewards_map_cfg or enemy)[deed_level] or nil
end

-- 获取当前的神锲等级的条件
function ControlBeastsContractWGData:GetDeedLevelCfg()
	local enemy = {}
	return self.deed_level_cfg 
end

-- 获取当前的神锲等级的条件
function ControlBeastsContractWGData:GetDeedLevelCfgById(deed_level)
	local enemy = {}
	return (self.deed_level_cfg or enemy)[deed_level] or nil
end

-- 获取最大神锲等级
function ControlBeastsContractWGData:GetDeedMaxLevel()
	return self.deed_level_cfg and #self.deed_level_cfg or 0
end

-- 获取当前的神锲等级对应的灵兽奖励和其他奖励
function ControlBeastsContractWGData:GetBeastRewardCfgByType(deed_level, not_split)
	local rewards_cfg = self:GetRewardCfgById(deed_level)
	local beast_list = {}
	local other_list = {}

	if rewards_cfg then
		for _, rewards_data in pairs(rewards_cfg) do
			if rewards_data and rewards_data.item then
				if not_split and rewards_data.weight ~= 0 then
					table.insert(beast_list, rewards_data.item[0])
				else
					if rewards_data.reward_display == REWARD_DISPLAY.BEAST then
						table.insert(beast_list, rewards_data.item[0])
					elseif rewards_data.reward_display == REWARD_DISPLAY.OTHER then
						table.insert(other_list, rewards_data.item[0])
					end
				end
			end
		end
	end

	return beast_list, other_list
end

-- 获取当前的神锲等级对应的灵兽奖励和其他奖励
function ControlBeastsContractWGData:GetLevelConditionCfgByType(deed_level)
	local LevelCfg = self:GetDeedLevelCfgById(deed_level + 1)
	if not LevelCfg then
		return nil
	end

	local condition_list = {}
	local beast_king_level = ControlBeastsWGData.Instance:GetBeastBaseKingLevel()
	local reward_time = self:GetBeastDrawDeedTotalTimes()

	if LevelCfg.beast_king_level == 0 or LevelCfg.reward_times == 0 then
		local temp_data = {}
		temp_data.color = COLOR3B.D_GREEN
		temp_data.desc = Language.ContralBeasts.ContrastCondition3
		table.insert(condition_list, temp_data)
		return condition_list
	end

	local temp_data = {}
	temp_data.is_lock = beast_king_level < LevelCfg.beast_king_level
	temp_data.color = beast_king_level >= LevelCfg.beast_king_level and COLOR3B.D_GREEN or COLOR3B.WHITE
	temp_data.desc = string.format(Language.ContralBeasts.ContrastCondition1, beast_king_level, LevelCfg.beast_king_level) 
	table.insert(condition_list, temp_data)

	local temp_data = {}
	temp_data.is_lock = reward_time < LevelCfg.reward_times
	temp_data.color = reward_time >= LevelCfg.reward_times and COLOR3B.D_GREEN or COLOR3B.WHITE
	temp_data.desc = string.format(Language.ContralBeasts.ContrastCondition2, reward_time, LevelCfg.reward_times) 
	table.insert(condition_list, temp_data)

	return condition_list
end

-- 获取当前全部大奖的列表
function ControlBeastsContractWGData:GetCurBeastExtraRewardsList()
	if not self.extra_rewards_map_cfg then
		return nil
	end
	local best_reward_index = self:GetBeastDrawDeedSelectedIndex()
	local temp_list = {}
	local select_index = 1
	
	for _, extra_rewards in pairs(self.extra_rewards_map_cfg) do
		if extra_rewards and extra_rewards.seq then
			table.insert(temp_list, extra_rewards)
		end
	end

	table.sort(temp_list, function (a, b)
		return a.seq < b.seq
	end)

	for index, extra_rewards in ipairs(temp_list) do
		if extra_rewards.seq == best_reward_index then
			select_index = index
		end
	end

	return temp_list, select_index
end

-- 获取当前全部大奖的列表(根据等级)
function ControlBeastsContractWGData:GetCurLevelExtraRewards()
	if not self.extra_rewards_map_cfg then
		return nil
	end
	local best_deed_level = self:GetBeastDrawDeedLevel()
	local temp_list = {}
	
	for _, extra_rewards in pairs(self.extra_rewards_map_cfg) do
		if extra_rewards and extra_rewards.deed_level and extra_rewards.deed_level == best_deed_level then
			table.insert(temp_list, extra_rewards)
		end
	end

	return temp_list
end

-- 获取当前的红点
function ControlBeastsContractWGData:GetCurBeastDrawModeRedByMode(mode_id)
	local base_cfg = self:GetBaseCfg()
	if base_cfg then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id) --拥有的数量
		local mode_cfg = self:GetCurBeastDrawMode(mode_id)
		local need_num = mode_cfg and mode_cfg.cost_item_num or 0

		return has_num >= need_num
	end

	return false
end

-- 获取当前的总红点
function ControlBeastsContractWGData:GetCurBeastDrawModeRed()
	if not self.draw_mode_cfg then
		return false
	end

	local is_red = false
	for _, mode_data in pairs(self.draw_mode_cfg) do
		is_red = is_red or self:GetCurBeastDrawModeRedByMode(mode_data.mode_seq + 1)
	end
	
	return is_red
end

-- 判断是否是抽奖物品
function ControlBeastsContractWGData:GetCurBeastDrawItem(item_id)
	local base_cfg = self:GetBaseCfg()
	if base_cfg and base_cfg.cost_item_id == item_id then
		return true
	end

	return false
end

