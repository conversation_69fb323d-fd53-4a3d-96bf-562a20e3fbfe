ActicityFoldFunWGData = ActicityFoldFunWGData or BaseClass()

function ActicityFoldFunWGData:__init()
	if ActicityFoldFunWGData.Instance ~= nil then
		ErrorLog("[ActicityFoldFunWGData] attempt to create singleton twice!")
		return
	end

	ActicityFoldFunWGData.Instance = self
    self:InitCfg()

	local rm = RemindManager.Instance
    rm:Register(RemindName.ACTIVITY_FOLD_ONE, BindTool.Bind(self.GetActFoldRemind, self, ACTIVITY_TYPE.ACTIVITY_FOLD_ONE))
	rm:Register(RemindName.ACTIVITY_FOLD_TWO, BindTool.Bind(self.GetActFoldRemind, self, ACTIVITY_TYPE.ACTIVITY_FOLD_TWO))
	rm:Register(RemindName.ACTIVITY_FOLD_THREE, BindTool.Bind(self.GetActFoldRemind, self, ACTIVITY_TYPE.ACTIVITY_FOLD_THREE))
	rm:Register(RemindName.ACTIVITY_FOLD_FOUR, BindTool.Bind(self.GetActFoldRemind, self, ACTIVITY_TYPE.ACTIVITY_FOLD_FOUR))

	self.fold_act_record_list = {}
	self.act_lvlimit_list = {}
end

function ActicityFoldFunWGData:InitCfg()
    local act_list_cfg = ConfigManager.Instance:GetAutoConfig("daily_activity_auto").daily
	self.fold_act_cfg = {}
	for k, v in pairs(act_list_cfg) do
		if v.parent_act_type and v.parent_act_type ~= "" and v.parent_act_type > 0 then
			if self.fold_act_cfg[v.parent_act_type] == nil then
				self.fold_act_cfg[v.parent_act_type] = {}
			end

			table.insert(self.fold_act_cfg[v.parent_act_type], v)
		end
	end
end

function ActicityFoldFunWGData:__delete()
    local rm = RemindManager.Instance
    rm:UnRegister(RemindName.ACTIVITY_FOLD_ONE)
	rm:UnRegister(RemindName.ACTIVITY_FOLD_TWO)
	rm:UnRegister(RemindName.ACTIVITY_FOLD_THREE)
	rm:UnRegister(RemindName.ACTIVITY_FOLD_FOUR)
	ActicityFoldFunWGData.Instance = nil
end

--获取活动的折叠info
function ActicityFoldFunWGData:GetFoldParentActInfo(act_type)
	return self.fold_act_cfg[act_type]
end

--设置记录需要监听的活动
function ActicityFoldFunWGData:SetFoldParentActRecord(parent_act_type, child_act_type)
	if parent_act_type and child_act_type then
		if self.fold_act_record_list[parent_act_type] == nil then
			self.fold_act_record_list[parent_act_type] = {}
		end
		
		self.fold_act_record_list[parent_act_type][child_act_type] = {act_type = child_act_type}
	end
end

function ActicityFoldFunWGData:RemoveFoldParentActRecord(parent_act_type, child_act_type)
	if self.fold_act_record_list[parent_act_type] ~= nil then
		self.fold_act_record_list[parent_act_type][child_act_type] = nil
	end
end

function ActicityFoldFunWGData:GetFoldParentActRecord(parent_act_type)
	return self.fold_act_record_list[parent_act_type]
end

--缓存已开放的活动但是被等级或者天数限制不显示
function ActicityFoldFunWGData:SetActTypeLimit(act_type)
	self.act_lvlimit_list[act_type] = {act_type = act_type}
end

function ActicityFoldFunWGData:RemoveActTypeLimit(act_type)
	self.act_lvlimit_list[act_type] = nil
end

function ActicityFoldFunWGData:GetActTypeLimit()
	return self.act_lvlimit_list
end

---------红点
function ActicityFoldFunWGData:GetActFoldRemind(act_type)
	local record_list = self.fold_act_record_list[act_type]
	if not IsEmptyTable(record_list) then
		for k, v in pairs(record_list) do
			if ActRemindList[v.act_type] then
				local remind_num = RemindManager.Instance:GetRemind(ActRemindList[v.act_type])
				if remind_num > 0 then
					return 1
				end
			end
		end
	end

	return 0
end