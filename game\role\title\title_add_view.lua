------------------------------------------------------------
--人物相关主View
------------------------------------------------------------
TitleAddView = TitleAddView or BaseClass(SafeBaseView)

function TitleAddView:__init()
    self.view_layer = UiLayer.Pop
    self.view_name = "TitleGetView"
	self.view_style = ViewStyle.Half
	self.can_do_fade = false
	self:SetMaskBg(true, true, nil, BindTool.Bind1(self.DoCloseTween, self))
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)

    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/appearance_ui_prefab", "root_gongxi_huode_title_view")
	self.active_close = false
end

function TitleAddView:ReleaseCallBack()
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TitleAddView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
	self.title_id = nil
	self.delay_to_show_model = nil

	if self.open_tween then
        self.open_tween:Kill()
        self.open_tween = nil
    end

    if self.close_tween then
        self.close_tween:Kill()
        self.close_tween = nil
    end
end

function TitleAddView:SetCapability(num)
	--self.node_list.lbl_ml_zhandouli_num.text.text = num
end

function TitleAddView:SetTitleImg(title_id)
	if title_id then
		local b,a = ResPath.GetTitleModel(title_id)
		self.node_list["img_title_item"]:SetActive(false)
        self.node_list["img_title_item"]:SetActive(true)
        self.node_list["img_title_item"]:ChangeAsset(b, a, false, function (obj)
			if IsNil(obj) then
                return
            end
            
            local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
			local title_cfg = TitleWGData.GetTitleConfig(title_id)

            if not diy_cfg or not title_cfg then
                return
            end

            local text_obj = obj.gameObject.transform:Find("Text")
            if text_obj == nil then
                return
            end
            
            local title_text = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
            local title_name = TitleWGData.Instance:GetDiyTitleName(title_id)
            if title_name and title_name ~= "" then
                title_text.text = title_name
            else
                title_text.text = title_cfg.name
            end
		end)
	end
end

function TitleAddView:LoadCallBack(index, loaded_times)
	XUI.AddClickEventListener(self.node_list.btn_get_title, BindTool.Bind1(self.DoCloseTween, self))	--绑定按钮
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TitleAddView, self.get_guide_ui_event)
end


function TitleAddView:OpenCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
end

function TitleAddView:CloseCallBack()
	local use_title_list = TitleWGData.Instance:GetUsedTitleId()
	if #use_title_list > 0 then
		--打开对比
		--print_error("打开对比")
		TitleWGCtrl.Instance:OpenTitleComparisonView(self.title_id)
	else
		--请求穿戴第一个称号
		--print_error("请求穿戴第一个称号")
		TitleWGCtrl.Instance:SendUseTitleReq({self.title_id})
	end
end

function TitleAddView:SetShowTitleId(id)
	self.title_id = id
end

function TitleAddView:ShowIndexCallBack()
	self.delay_to_show_model = true
end

function TitleAddView:DoOpenTween()
	self.node_list["img_title_item"]:SetActive(false)

	self.delay_to_show_model = false
    if self.open_tween then
        self.open_tween:Kill()
        self.open_tween = nil
    end

	local show_tweener = DG.Tweening.DOTween.Sequence()
	local panel = self.node_list.view_panel
	panel.transform.localScale = Vector3(0.6, 0.8, 0.8)

	show_tweener:Append(panel.canvas_group:DoAlpha(0.6, 1, 0.3))
	show_tweener:Join(panel.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.25))
	show_tweener:Append(panel.rect:DOScale(Vector3(1, 1, 1), 0.05))
	show_tweener:SetEase(DG.Tweening.Ease.InOutQuad)
	show_tweener:OnComplete(
	function()
		self:Flush()
	end
	)

	self.open_tween = show_tweener
end

function TitleAddView:DoCloseTween()
	local panel = self.node_list.view_panel
	if not panel then
		self:Close()
		return
	end

    if self.close_tween then
        self.close_tween:Kill()
        self.close_tween = nil
    end

	local show_tweener = DG.Tweening.DOTween.Sequence()
	panel.transform.localScale = Vector3(1, 1, 1)

	show_tweener:Append(panel.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.25))
	show_tweener:Join(panel.canvas_group:DoAlpha(1, 0.1, 0.2))
	show_tweener:Append(panel.rect:DOScale(Vector3(1.1, 1.1, 1.1), 0.05))
	show_tweener:SetEase(DG.Tweening.Ease.InOutQuad)
	show_tweener:OnComplete(
	function()
		self:Close()
	end
	)

	self.close_tween = show_tweener
end

function TitleAddView:OnFlush(param_t, index)
	if self.delay_to_show_model then
		self:DoOpenTween()
		return
	end

	for k, v in pairs(param_t) do
		if "all" == k then
			if self.title_id == nil then
				return
			end
			self:SetTitleImg(self.title_id)
			local title_cfg = TitleWGData.GetTitleConfig(self.title_id)

			if title_cfg and title_cfg.is_use_by_system == 1 then
				local use_title_list = TitleWGData.Instance:GetUsedTitleId()
				if #use_title_list >= 3 then
					use_title_list[3] = self.title_id
				else
					use_title_list[#use_title_list + 1] = self.title_id
				end
				TitleWGCtrl.Instance:SendUseTitleReq(use_title_list)
			end
			local add_attribute = AttributeMgr.GetAttributteByClass(title_cfg)
			local add_military =  AttributeMgr.GetCapability(add_attribute)
			self:SetCapability(add_military)
		end
	end
end

function TitleAddView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.AddTitleBtn then
		return self.node_list.btn_get_title, BindTool.Bind1(self.DoCloseTween, self)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end
