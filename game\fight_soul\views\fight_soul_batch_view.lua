FightSoulBoneBatchView = FightSoulBoneBatchView or BaseClass(SafeBaseView)
function FightSoulBoneBatchView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
                        {sizeDelta = Vector2(828, 450)})
    self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_fight_soul_bone_batch")
end

function FightSoulBoneBatchView:ReleaseCallBack()
    if self.list_view then
        self.list_view:DeleteMe()
        self.list_view = nil
    end

    if self.compose_remind_alert then
        self.compose_remind_alert:DeleteMe()
        self.compose_remind_alert = nil
    end

    self.batch_list = nil
    self.fs_type = nil
end

function FightSoulBoneBatchView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FightSoul.BatchTitle
    self.node_list.gou:SetActive(false)
    self.list_view = AsyncListView.New(FightSoulBoneBatchRender, self.node_list.item_list)
    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.ClickOkBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_select_all, BindTool.Bind(self.SelectAllBatch, self))
end

function FightSoulBoneBatchView:SetDataAndOpen(fs_type)
    self.fs_type = fs_type
    self.batch_list = FightSoulWGData.Instance:GetBatchShowListByType(self.fs_type)
    self:Open()
end

function FightSoulBoneBatchView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushView()
        elseif k == "from_protocol" then
            self.batch_list = FightSoulWGData.Instance:GetBatchShowListByType(self.fs_type)
            self:FlushView()
        elseif k == "flush_all_select" then
            self:FlushAllSelectState()
        end
    end
end

function FightSoulBoneBatchView:FlushView()
    if IsEmptyTable(self.batch_list) then
        self.list_view:SetDataList({})
        self.node_list.no_data:SetActive(true)
        self.node_list.gou:SetActive(false)
        self.node_list.btn_select_all:SetActive(false)
        return
    end

    self.node_list.no_data:SetActive(false)
    self.node_list.btn_select_all:SetActive(true)
    self.list_view:SetDataList(self.batch_list)
    self:FlushAllSelectState()
end

function FightSoulBoneBatchView:FlushAllSelectState()
    local is_all = true
    if not IsEmptyTable(self.batch_list) then
        for k,v in ipairs(self.batch_list) do
            if not v.is_select then
                is_all = false
                break
            end
        end
    else
        is_all = false
    end

    self.node_list.gou:SetActive(is_all)
end

function FightSoulBoneBatchView:SelectAllBatch()
    if IsEmptyTable(self.batch_list) then
        return
    end

    local is_all = self.node_list.gou:GetActive()

    for k,v in ipairs(self.batch_list) do
        v.is_select = not is_all
    end

    if self.list_view then
        local cell_list = self.list_view:GetAllItems()
        for index, cell in pairs(cell_list) do
            cell:FlushSelectBatch()
        end
    end

    self.node_list.gou:SetActive(not is_all)
end

function FightSoulBoneBatchView:ClickOkBtn()
    if IsEmptyTable(self.batch_list) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.BatchLimit1)
        return
    end

    local select_batch_list = {}
    local height_num = 0
    local cur_show_data, next_show_data

    for k,v in ipairs(self.batch_list) do
        if v.is_select then
            local data = {}
            data.main_bag_index = v.cur_data.bag_index
            data.stuff_list = {}
            for i,j in ipairs(v.stuff_list) do
                table.insert(data.stuff_list, j.bag_index)
            end
            table.insert(select_batch_list, data)

            if v.target_data.suit_type > v.cur_data.suit_type then
                height_num = height_num + 1
                cur_show_data = v.cur_data
                next_show_data = v.target_data
            end
        end
    end

    if #select_batch_list == 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.BatchLimit2)
        return
    end

    local function send_pro()
        FightSoulWGCtrl.Instance:ReqFightSoulBoneBatchComposeOp(select_batch_list)
    end

    if height_num == 1 then
        FightSoulWGCtrl.Instance:OpenFightSoulBoneHeightTips(nil, send_pro, cur_show_data, next_show_data)
    elseif height_num > 1 then
        if nil == self.compose_remind_alert then
            self.compose_remind_alert = Alert.New(nil, nil, nil, nil, true)
            self.compose_remind_alert:SetCheckBoxDefaultSelect(false)
        end

        self.compose_remind_alert:SetLableString(Language.FightSoul.BatchRemind)
        self.compose_remind_alert:SetOkFunc(function()
            send_pro()
        end)

        self.compose_remind_alert:Open()
    else
        send_pro()
    end
end

--------------------------------------------------------------------------------
FightSoulBoneBatchRender = FightSoulBoneBatchRender or BaseClass(BaseRender)
function FightSoulBoneBatchRender:__init(instance)

end

function FightSoulBoneBatchRender:__delete()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
    end

    if self.cur_item then
        self.cur_item:DeleteMe()
        self.cur_item = nil
    end

    if self.target_item then
        self.target_item:DeleteMe()
        self.target_item = nil
    end
end

function FightSoulBoneBatchRender:LoadCallBack()
	if self.cell_list == nil then
        self.cell_list = {}
        for i = 1, 6 do
            self.cell_list[i] = ItemCell.New(self.node_list["item_list"])
        end
    end

    self.node_list.gou:SetActive(false)
    self.cur_item = ItemCell.New(self.node_list["cur_item"])
    self.target_item = ItemCell.New(self.node_list["target_item"])
    XUI.AddClickEventListener(self.node_list.btn_select, BindTool.Bind(self.SelectBatch, self))
end

function FightSoulBoneBatchRender:OnFlush()
    if IsEmptyTable(self.data) then
    	return
    end

    local is_wear = FightSoulWGData.Instance:GetBoneBagIndexIsWear(self.data.cur_data.bag_index)
    self.node_list.is_wear:SetActive(is_wear)
    self.cur_item:SetData(self.data.cur_data)
    self.target_item:SetData(self.data.target_data)

    for k,v in ipairs(self.cell_list) do
        local data = self.data.stuff_list[k]
        if data ~= nil then
            v:SetActive(true)
            v:SetData(data)
        else
            v:SetActive(false)
        end
    end

    self:FlushSelectBatch()
end

function FightSoulBoneBatchRender:FlushSelectBatch()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.gou:SetActive(self.data.is_select)
end

function FightSoulBoneBatchRender:SelectBatch()
    if IsEmptyTable(self.data) then
        return
    end

    self.data.is_select = not self.data.is_select
    self:FlushSelectBatch()
    FightSoulWGCtrl.Instance:FlushBoneBatchView("flush_all_select")
end
