﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

using System;
using UnityEngine;

public class UIPrefabLoaderSync : UIPrefabLoader
{
    [SerializeField]
    private GameObject prefab;
    private GameObject instance;

    private Action<GameObject> waitLoad;

    /// <summary>
    /// Wait the instance.
    /// </summary>
    public override void Wait(Action<GameObject> wait)
    {
        if (this.instance == null)
        {
            this.waitLoad = wait;
        }
        else
        {
            wait(this.instance);
        }
    }

    private void OnEnable()
    {
        if (this.instance == null)
        {
            this.instance = GameObject.Instantiate(
                this.prefab, this.transform);
            if (this.waitLoad != null)
            {
                this.waitLoad(this.instance);
                this.waitLoad = null;
            }
        }
    }
}
