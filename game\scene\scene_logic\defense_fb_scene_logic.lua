DefenseFbSceneLogic = DefenseFbSceneLogic or BaseClass(CommonFbLogic)
local DEF_STATE = {
    NONE = 0,
    STATE_DEFAULT_POINT = 1,
    STATE_TO_AUTO_FIGHT = 2,
}
function DefenseFbSceneLogic:__init()
    self.defense_tower_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerList()
    self.defense_tower_list = {}
    self.defense_monster_refresh_time = 0
    self.flush_once = true

    self.range_obj = nil
    self.obj_create_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind1(self.OnObjectCreate, self))

end

function DefenseFbSceneLogic:__delete()
    if self.obj_create_event then
        GlobalEventSystem:UnBind(self.obj_create_event)
    end
    self.obj_create_event = nil

    self.defense_tower_cfg = nil
    self.defense_tower_list = nil
end

function DefenseFbSceneLogic:Enter(old_scene_type, new_scene_type)
    -- print_error("DefenseFbSceneLogic",new_scene_type)
    if old_scene_type ~= new_scene_type then
        CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
        FuBenWGCtrl.Instance:OpenDefenseFuBenView()
        MainuiWGCtrl.Instance:SetFBNameState(true, Language.FbName.LiuDaoLunHui)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
    end
    -- MainuiWGCtrl.Instance:SetTaskActive(false)
    MainuiWGCtrl.Instance:SetOtherContents(true)
    FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.FBCT_TAFANG)
    -- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
    MainuiWGCtrl.Instance:SetTaskContents(false)

    self.defense_pos_list = __TableCopy(FuBenWGCtrl.Instance.defense_fb_data:GetDefensePosList())
    for k, v in ipairs(self.defense_pos_list)do
        local defense_vo = GameVoManager.Instance:CreateVo(DefenseVo)
        defense_vo.pos_x = v.pos_x
        defense_vo.pos_y = v.pos_y
        defense_vo.pos_index = v.pos_index
        defense_vo.obj_id = Scene.Instance:GetSceneClientId()
        defense_vo.action_type = TRIGGER_ACTION_TYPE.DEFENSE_TOWER
        defense_vo.name = "塔基座"
        local obj = Scene.Instance:CreateObj(defense_vo, SceneObjType.DefenseTowerObj)
        self.defense_tower_list[#self.defense_tower_list + 1] = obj
    end

    if not self.select_defense_obj then
        self.select_defense_obj = GlobalEventSystem:Bind(ObjectEventType.SELECT_DEFENSE_OBJ, BindTool.Bind1(self.OnSelectDefenseObj, self))
    end

    self.main_role_pos_change = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_POS_CHANGE,BindTool.Bind(self.OnMainRolePosChange,self))
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    self.main_role = Scene.Instance:GetMainRole()
    self.trigger_tower_obj = nil
end

function DefenseFbSceneLogic:OnMainRolePosChange(role_x,role_y)
    if self.trigger_tower_obj == nil then
        if not IsEmptyTable(self.defense_tower_list) then
            for index,v in pairs(self.defense_tower_list) do
                local distance = GameMath.GetDistance(role_x, role_y, v.vo.pos_x, v.vo.pos_y, false)
                if distance <= 4 then
                    self.trigger_tower_obj = v
                    break
                end
            end
            if self.trigger_tower_obj  ~= nil then
                if self.trigger_tower_obj :IsDefenseTower() and not FuBenWGCtrl.Instance:GetTowerIsBuildByPosIndex(self.trigger_tower_obj.vo.pos_index) then
                    FuBenWGCtrl.Instance:OpenDefenseTowerFuBenView(self.trigger_tower_obj.vo.pos_index,true)
                end
            end
        end
    end
    if self.trigger_tower_obj  ~= nil then
        local distance = GameMath.GetDistance(role_x, role_y, self.trigger_tower_obj.vo.pos_x, self.trigger_tower_obj.vo.pos_y, false)
        if distance > 4 then
            FuBenWGCtrl.Instance:CloseDefenseTowerFuBenView()
            self.trigger_tower_obj = nil
        end
    else
        FuBenWGCtrl.Instance:CloseDefenseTowerFuBenView()
    end
end

function DefenseFbSceneLogic:OnSelectDefenseObj(defense_obj)
    -- print_error("pos_index",pos_index)
   if defense_obj then
       if defense_obj:IsDefenseTower() then
            --创建光圈
          FuBenWGCtrl.Instance:OpenDefenseTowerFuBenView(defense_obj.vo.pos_index,true)
       else
          FuBenWGCtrl.Instance:OpenDefenseTowerFuBenView(self:GetDefenseTowerIndex(defense_obj),false)
       end
   end
end

function DefenseFbSceneLogic:GetDefenseTowerIndex(monster)
    local m_x,m_y = monster:GetLogicPos()
    local d_x,d_y = 1,1
    for i,v in ipairs(self.defense_tower_list) do
        d_x,d_y = v:GetLogicPos()
        if d_x == m_x and d_y == m_y then
            return v.vo.pos_index
        end
    end
end

function DefenseFbSceneLogic:Update(now_time, elapse_time)
    CommonFbLogic.Update(self, now_time, elapse_time)
    if now_time >= self.defense_monster_refresh_time then
        self.defense_monster_refresh_time = now_time + 1
        local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
        if nil == next(defense_data) then
            return
        end
        local can_uplevel = {}
        for tower_type = 0, 3 do
            can_uplevel[tower_type] = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerCanMaxLevel(tower_type, defense_data.douhun)
        end
        if defense_data.cur_wave > -1 then
            if self.flush_once then
                FuBenWGCtrl.Instance:ShowBossBtnAction(true)
                self.flush_once = false
            end
        end
    end
end

function DefenseFbSceneLogic:Out(old_scene_type, new_scene_type)
    if old_scene_type ~= new_scene_type then
        ViewManager.Instance:CloseAll()
        CommonFbLogic.Out(self)

         GlobalTimerQuest:AddDelayTimer(
            function ()
                local drop_info = FuBenWGData.Instance:GetDropInfo(FUBEN_TYPE.FBCT_TAFANG)
                if drop_info then
                    local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
                    local filtrate_drop = FuBenWGData.Instance:FiltrateDefenseDrop(drop_info.drop_item_list) -- 塔防本掉落筛选并排序
                    if defense_data.is_pass == 1 and defense_data.is_finish == 1 and drop_info.is_sweep == 0 then
                        FuBenWGCtrl.Instance:SetCommonWinData(SceneType.DefenseFb, filtrate_drop, defense_data.exp, defense_data.reward_xianhunshi_num, 3, 10)
                    end
                    if defense_data.is_pass == 0 and defense_data.is_finish == 1 then
                        GlobalTimerQuest:AddDelayTimer(
                        function ()
                            FuBenWGCtrl.Instance:OpenFuBenLoseView()
                        end, 1.5)
                    end
                end
            end, 1)

        MainuiWGCtrl.Instance:SetTaskContents(true)
        MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(true)
        -- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
        MainuiWGCtrl.Instance:SetOtherContents(false)
        FuBenWGCtrl.Instance:CloseDefenseFuBenView()

        FuBenWGCtrl.Instance:CloseDefenseTowerFuBenFeformView()
        self.defense_tower_list = nil

    end
    if self.main_role_pos_change then
        GlobalEventSystem:UnBind(self.main_role_pos_change)
        self.main_role_pos_change = nil
    end
     FuBenWGCtrl.Instance:CloseDefenseTowerFuBenView()
    if self.select_defense_obj ~= nil then
        GlobalEventSystem:UnBind(self.select_defense_obj)
    end
    self.select_defense_obj = nil
    self.trigger_tower_obj = nil
    self.main_role = nil
     GuajiWGCtrl.Instance:SetGuajiRange(nil)

    if self.state_machine then
        self.state_machine:DeleteMe()
        self.state_machine = nil
    end
end

function DefenseFbSceneLogic:FlushDefenseObjVisible()
    local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
    local tower_info_list = defense_data.tower_info_list

    for k, v in ipairs(self.defense_tower_list) do
        if v.vo and tower_info_list[v.vo.pos_index] then
            v:ForceSetVisible(tower_info_list[v.vo.pos_index].tower_type == -1)
        end
    end

    self:ShowAllTowerName()
end

-- 判断怪物是不是防御塔
function DefenseFbSceneLogic:IsDefenseTower(target_obj)
    if target_obj == nil then return false end

    if target_obj:GetType() == SceneObjType.DefenseTowerObj then
        return true
    end

    if target_obj:GetType() == SceneObjType.Monster then
    	if target_obj.vo == nil then
    		return false
    	end

    	if self.defense_tower_cfg ~= nil then
	    	for k,v in pairs(self.defense_tower_cfg) do
	    		if v.monster_id == target_obj.vo.monster_id then
	    			return true
	    		end
	    	end
    	end
    end

    return false
end

function DefenseFbSceneLogic:ShowAllTowerName()
    local monster_list = Scene.Instance:GetObjListByType(SceneObjType.Monster)
    for k,target_obj in pairs(monster_list) do
        if target_obj:GetType() == SceneObjType.Monster then
            if target_obj.vo ~= nil and self.defense_tower_cfg ~= nil then
                for k,v in pairs(self.defense_tower_cfg) do
                    if v.monster_id == target_obj.vo.monster_id then
                        local follow_ui = target_obj:GetFollowUi()
                        if follow_ui then
                            follow_ui:ShowName()
                        end
                    end
                end
            end
        end
    end
end

-- 怪物是否是敌人
function DefenseFbSceneLogic:IsMonsterEnemy(target_obj, main_role)
    return not self:IsDefenseTower(target_obj)
end

--获得场景显标的副本图标列表
function DefenseFbSceneLogic:GetFbSceneShowFbIconCfgList()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg == nil then return end
    return fb_scene_cfg.show_fbicon
end

function DefenseFbSceneLogic:RolePickUpFallItem() --捡取最近的采集物 直接捡 不走过去
    if not Scene.Instance:HasOwnFallItem() then return false end
    local my_fall_item_list = Scene.Instance:GetHasOwnFallItem()

    if #my_fall_item_list > 0 then
        local obj_id_list = {}
        for k, v in ipairs(my_fall_item_list)do
            obj_id_list[#obj_id_list + 1] = v:GetObjId()
        end
        Scene.ScenePickItem(obj_id_list)
    end
    return false
end

function DefenseFbSceneLogic:SetGuaiJi(guaji_type, click_obj)
    if click_obj ~= nil and self:IsDefenseTower(click_obj) then
        return
    end
    CommonFbLogic.SetGuaiJi(self, guaji_type)
end

function DefenseFbSceneLogic:GetGuajiCharacter()
    local main_role = Scene.Instance:GetMainRole()
    local x, y = main_role:GetLogicPos()
    local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
    local enemy_obj = self:SelectBossHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
    if enemy_obj ~= nil and enemy_obj:IsBoss() then
        return enemy_obj
    end
    local monster_obj = self:SelectMonsterHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
    if monster_obj ~= nil then
        return monster_obj
    end
end

function DefenseFbSceneLogic:SelectBossHelper(obj_list, x, y, distance_limit, select_type)
    local target_obj = nil
    local target_distance = distance_limit
    --找到一个最近的
    for _, v in pairs(obj_list) do
        if v:IsCharacter() or v:GetModel():IsVisible() then
            local can_select = true
            local target_x, target_y = v:GetLogicPos()
            local distance = GameMath.GetDistance(x, y, target_x, target_y, false)-- - 10
            if distance < target_distance and v:IsBoss() then
                if v:IsInBlock() then
                    if nil == target_obj then
                        target_obj = v
                    end
                else
                    target_obj = v
                    target_distance = distance
                end
            end
        end
    end
    return target_obj, target_distance
end

function DefenseFbSceneLogic:SelectMonsterHelper(obj_list, x, y, distance_limit, select_type)
    local target_obj = nil
    local target_distance = distance_limit
    --找到一个最近的
    for _, v in pairs(obj_list) do
        if v:IsCharacter() or v:GetModel():IsVisible() then
            local can_select = true
            local target_x, target_y = v:GetLogicPos()
            local distance = GameMath.GetDistance(x, y, target_x, target_y, false)-- - 10
            if distance < target_distance and not v:IsTower() then
                if v:IsInBlock() then
                    if nil == target_obj then
                        target_obj = v
                    end
                else
                    target_obj = v

                    target_distance = distance
                end
            end
        end
    end
    return target_obj, target_distance
end

function DefenseFbSceneLogic:GetGuajiPos()
    return FuBenWGCtrl.Instance:GetDefneseGuajiPos()
end

function DefenseFbSceneLogic:OnObjectCreate(obj)
    if obj:IsTower() then
        local follow_ui = obj:GetFollowUi()
        if follow_ui then
            follow_ui:Show()
        end
    end
end
