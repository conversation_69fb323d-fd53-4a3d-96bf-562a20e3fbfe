------------------------------------------------------------
--天神八卦对比tips
------------------------------------------------------------
BaGuaContrastTip = BaGuaContrastTip or BaseClass(SafeBaseView)

function BaGuaContrastTip:__init()
	self.view_layer = UiLayer.Pop
	self.view_name = "BaGuaUi"
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_itemtip")

	self.equip_data_1 = nil
	self.equip_data_2 = nil
end

function BaGuaContrastTip:__delete()

end

function BaGuaContrastTip:ReleaseCallBack()
	if self.bagua_equip_tip_1 then
        self.bagua_equip_tip_1:DeleteMe()
     	self.bagua_equip_tip_1 = nil
   	end

   	if self.bagua_equip_tip_2 then
        self.bagua_equip_tip_2:DeleteMe()
     	self.bagua_equip_tip_2 = nil
   	end
	self.equip_data_list = nil
end

function BaGuaContrastTip:LoadCallBack()
	self.bagua_equip_tip_1 = BaseTip.New(self.node_list["base_tip_root_1"])
	self.item_cell1 = self.bagua_equip_tip_1:GetItemCell()
	self.bagua_equip_tip_1:Reset()
	--self.bagua_equip_tip_1:SetHideScrollBar(true)
	self.bagua_equip_tip_2 = BaseTip.New(self.node_list["base_tip_root_2"])
	self.item_cell2 = self.bagua_equip_tip_2:GetItemCell()
	self.bagua_equip_tip_2:Reset()
	--self.bagua_equip_tip_2:SetHideScrollBar(true)

end

function BaGuaContrastTip:OnFlush()
	-- if self.equip_data_list[1] and self.bagua_equip_tip_1 then
	-- 	self.node_list["base_tip_root_1"]:SetActive(true)
	-- 	local data_list = self.equip_data_list[1]
	-- 	self.bagua_equip_tip_1:SetData(data_list.data, data_list.fromView, data_list.param_t)
	-- else
	-- 	self.node_list["base_tip_root_1"]:SetActive(false)
	-- end
	-- if self.equip_data_list[2] and self.bagua_equip_tip_2 then
	-- 	self.node_list["base_tip_root_2"]:SetActive(true)
	-- 	local data_list = self.equip_data_list[2]
	-- 	self.bagua_equip_tip_2:SetData(data_list.data, data_list.fromView, data_list.param_t)
	-- else
	-- 	self.node_list["base_tip_root_2"]:SetActive(false)
	-- end
	self.my_data = {}
	self.is_show_compare = false
	self.slot_info = {}
	if self.equip_data then
		--self.bagua_equip_tip_2:SetData(self.equip_data, self.fromView, self.param_t, self.item_pos_x, self.btn_callback_event)
		local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.equip_data.item_id) 
		self.my_data = {}
		local my_equip_info_list = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(base_cfg.index)

		if my_equip_info_list and self.fromView == ItemTip.FROM_TIANSHEN_BAGUA_BAG or self.fromView == ItemTip.FROM_TIANSHEN_BAGUA_BAG then
			self.my_data = my_equip_info_list[base_cfg.part + 1]
		end

		local active_part_str = ""
		local part_info = {}
		local is_active = false
		local activity_list = {}
		local color
		for i=1,8 do
			is_active = my_equip_info_list[i] and my_equip_info_list[i].item_id > 0
			color = is_active and COLOR3B.BLUE_TITLE or TIPS_COLOR.ATTR_NAME
			part_info = TianShenBaGuaWGData.Instance:GetAdviseShowCfg(i-1)
			if i >1 and i %2 == 1 then
				active_part_str = active_part_str.."\n"
			elseif i > 1 then
				active_part_str = active_part_str.."                               "
			end
			active_part_str = active_part_str..ToColorStr(part_info[1].name,color)
			activity_list[i] = is_active
		end
		self.slot_info.active_part_str = active_part_str
		self.slot_info.activity_list = activity_list
		self.slot_info.bagua_index = base_cfg.index
		self.slot_info.bagua_part = base_cfg.part
		local skill_info = TianShenBaGuaWGData.Instance:GetTianShenSuitSkillInfo(base_cfg.index)
		local skill_str = ""
		local skill_num = 0
		local active_color
		for k,v in pairs(skill_info) do
			active_color = v.is_active and COLOR3B.GREEN or TIPS_COLOR.ATTR_NAME
			skill_num = skill_num + 1
			if skill_num > 1 then
				skill_str = skill_str.."\n"
			end
			local attr_num = 0
			if v.attr_type_list and tonumber(v.attr_value_list[1]) ~= 0 then
				for t,q in pairs(v.attr_type_list) do
					attr_num = attr_num + 1
					if attr_num == 1 then
						skill_str = skill_str..ToColorStr(string.format(Language.TianShen.TipsBaGuaSlotNum,v.count, v.count_star),active_color)
					end

					if attr_num > 1 then
						skill_str = skill_str .."\n".."          "
					end

					if v.is_pre[t] then
						skill_str = skill_str..ToColorStr(Language.Common.TipsAttrNameList[q].."+"..(tonumber(v.attr_value_list[t])/100).."%",active_color)
					else
						skill_str = skill_str..ToColorStr(Language.Common.TipsAttrNameList[q].."+"..tonumber(v.attr_value_list[t]),active_color)
					end
				end
			end

			if v.skill_type > 0 and v.skill_des and v.param_0 then
				if attr_num == 1 then
					skill_str = skill_str..ToColorStr(string.format(Language.TianShen.TipsBaGuaSlotNum,v.count, v.count_star),active_color)
				else
					skill_str = skill_str .."\n".."          "
				end
				skill_str = skill_str..ToColorStr(string.format(v.skill_des,tonumber(v.param_0)/100).."%",active_color)
			else

			end
		end
		self.slot_info.skill_str = skill_str

		if self.my_data and self.my_data.item_id and self.my_data.item_id >0 then
			self.node_list["base_tip_root_1"]:SetActive(true)
			self.is_show_compare = true
		else
			self.node_list["base_tip_root_1"]:SetActive(false)
		end
	else
		self.node_list["base_tip_root_1"]:SetActive(false)
	end
	self:FlushTipsShow2()
	self:FlushTipsShow1()
end

function BaGuaContrastTip:SetEquipData(data,fromView,param_t,item_pos_x,btn_callback_event)
	self.equip_data = data
	self.fromView = fromView
	self.param_t = param_t
	self.item_pos_x = item_pos_x
	self.btn_callback_event = btn_callback_event
	self:Open()
end
function BaGuaContrastTip:FlushTipsShow1()
	if IsEmptyTable(self.my_data) or not self.my_data.item_id then return end
	 --设置物品cell
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.my_data.item_id)
	local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.my_data.item_id)
	if not item_cfg or not item_cfg.name then return end
	self.item_cell1:SetData(self.my_data)
	self.item_cell1:SetLeftTopImg(base_cfg.star or 0)
	self.item_cell1:SetLeftTopTextVisible(false)
	local str = ""
	str = item_cfg.name
	self.bagua_equip_tip_1:SetItemName(ToColorStr(str, ITEM_COLOR[item_cfg.color]))
	self.bagua_equip_tip_1:SetTopColorBg(item_cfg.color or 0)
	self.bagua_equip_tip_1:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)
	
	self:ParseProp(item_cfg, 1)
	self.bagua_equip_tip_1:SetTopLeftIcon("a1_biaoqian_zbz")
	self:ShowItemDescription(item_cfg,1)
	self:ShowItemGetDesc(item_cfg,1)
	self.bagua_equip_tip_1:SetBaGuaSlotInfo(self.slot_info)
	--self.bagua_equip_tip_1:SetHideScrollBar(true)


end

function BaGuaContrastTip:FlushTipsShow2()
	if not self.equip_data or not self.equip_data.item_id then return end
	self.bagua_equip_tip_2:Reset()
	--设置物品cell
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.equip_data.item_id)
	local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.equip_data.item_id)
	if not self.item_cell2 then
		self.item_cell2 = self.bagua_equip_tip_2:GetItemCell()
	end
	self.item_cell2:SetData(self.equip_data)
	self.item_cell2:SetLeftTopImg(base_cfg.star or 0)
	self.item_cell2:SetLeftTopTextVisible(false)
	

	--设置按钮
	local btn_info_list = {}
	if self.btn_callback_event then
		for i,v in ipairs(self.btn_callback_event) do
			local temp = {}
			temp.btn_name = v.btn_text
			temp.btn_red = v.btn_red or 0
			temp.btn_click = BindTool.Bind2(self.OperationClickHandler, self, i)
			btn_info_list[#btn_info_list + 1] = temp
		end
	end
	if #btn_info_list > 0 then
		self.bagua_equip_tip_2:SetBtnsClick(btn_info_list)
	end

	--设置名称
	local str = ""
	str = item_cfg.name
	self.bagua_equip_tip_2:SetItemName(ToColorStr(str, ITEM_COLOR[item_cfg.color]))
	self.bagua_equip_tip_2:SetTopColorBg(item_cfg.color or 0)
	self.bagua_equip_tip_2:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)
	
	self:ParseProp(item_cfg, 2)
	self:ShowItemGetDesc(item_cfg,2)
	self:ShowItemDescription(item_cfg,2)
	self.bagua_equip_tip_2:SetBaGuaSlotInfo(self.slot_info)
	--self.bagua_equip_tip_2:SetHideScrollBar(true)

	if IsEmptyTable(self.my_data) or not self.my_data.item_id then 
		self:FlushGetWayView()
	end
end 

function BaGuaContrastTip:FlushGetWayView()
	local data_lits = TipWGData.Instance:GetGetWayList(self.equip_data.item_id)
	self.data_lits = data_lits
	local path_list = {}
	for i,data in ipairs(data_lits) do
		if data.ShowType == ItemTipGoods.ShowType.Type2 then
            local temp = {label = data.cfg.open_name, btn_click = BindTool.Bind(self.OnClickGoToOpenPanel, self, i)}
			path_list[#path_list + 1] = temp
		end
	end
	if #path_list > 0 then
		local is_on = true--self.from_view ~= ItemTip.FROM_BAG and self.from_view ~= ItemTip.FROM_LONGHUN_BAG and self.from_view ~= ItemTip.FROM_LONGHUN_EQUIP
		self.bagua_equip_tip_2:SetGetWayPanel({path_list = path_list, is_on = is_on})
	end
end

function BaGuaContrastTip:OnClickGoToOpenPanel(index)
	local data = self.data_lits[index]
	if not data or data.ShowType ~= ItemTipGoods.ShowType.Type2 or not data.cfg then
		return
	end

	local open_panel = data.cfg.open_panel
	local open_panel_table = Split(data.cfg.open_panel,"#")
	if open_panel == GuideModuleName.Compose then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, nil, {is_stuff = false, item_id = data.item_id})
	elseif open_panel == "guaji" then  						--挂机按钮点击去挂机
		TaskWGData.Instance:GuaJiGetMonster()
	elseif open_panel == "GuildJuanXian" then
		local isnot_join_guild = RoleWGData.Instance.role_vo.guild_id == 0
		if isnot_join_guild then
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, TabIndex.guild_guildlist)
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CanNotEnterNoGuild)
		else
			GuildWGCtrl.Instance:OpenGuildJuanXinaView()
		end
	elseif open_panel_table[1] == "shop" then
		local tab_index = 0
		if open_panel_table[2] then
			tab_index = ShopWGData.ShowTypeToIndex[open_panel_table[2]]
		else
			tab_index = ShopWGData.Instance:GetItemSellTabIndex(data.item_id)
		end
		ShopWGCtrl.Instance:ShopJumpToItemByIDAndTabIndex(data.item_id,tab_index)
		--ViewManager.Instance:Open(GuideModuleName.Shop, tab_index, "select_item_id", {item_id = data.item_id})
	elseif open_panel == "boss_xiezhu" then
		BossAssistWGCtrl.Instance:Open()
	else
		FunOpen.Instance:OpenViewNameByCfg(open_panel)
	end

	local close_event = TipWGCtrl.Instance:GetCloseOtherView()
	if nil ~= close_event then
		close_event()
		TipWGCtrl.Instance:CloseOtherView(nil)
	end
	self:Close()
end

--显示描述description
function BaGuaContrastTip:ShowItemDescription(item_cfg,index)
	if item_cfg == nil then
		return
	end
	local info_list = {}
	info_list[1] = {}
	info_list[1].title = Language.TianShen.BaGuaTipsTitle1

	local slot_info = TianShenBaGuaWGData.Instance:GetBaGuaNameInfo(item_cfg.color-1)
	local name = slot_info and slot_info.name or ""
	info_list[1].desc = string.format(Language.TianShen.BaGuaTipsDesc1,name)

	-- local description = ItemWGData.Instance:GetItemConstDesc(item_cfg,1)
	-- if 84 == item_cfg.use_type then               -- 使用类型为84的具体描述读配置
	-- 	description = string.format(description, CommonDataManager.ConverExp(self:GetLeveReward(item_cfg.param1)))
	-- end

	-- description = CommonDataManager.ParseGameName(description)
	-- if description and description ~= "" then
	-- 	info_list[#info_list + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	-- end

	-- local desc = nil
	-- for i = 2, 2 do
	-- 	desc = item_cfg["description" .. i]
	-- 	if desc and desc ~= "" then
	-- 		info_list[#info_list + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
	-- 	elseif i == 2 and item_cfg.search_type == 1500 then
	-- 		desc = ItemWGData.Instance:AutoGetGiftDropDesc(item_cfg.id)
	-- 		info_list[#info_list + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
	-- 	end
	-- end

	local capability = 0
	local need_get_sys, sys_type, replace_idx, show_cap_type = false, 0, 2, TIPS_CAP_SHOW.SHOW_CFG

	local function set_need_sys_attr(show_data, sys_attr_cap_location, ignore_desc)
		local attr_desc = ""
		need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_data.item_id, sys_attr_cap_location)
		if need_get_sys then
			attr_desc, capability = ItemShowWGData.Instance:GetItemAttrDescAndCap(show_data, sys_type)
			if attr_desc ~= "" and not ignore_desc then
				local temp_title = Language.F2Tip.SysAttrTitle[sys_type] or Language.F2Tip.SysAttrTitle[0]
				if self.from_view == ItemTip.FROM_MINGWEN_BAG or self.from_view == ItemTip.FROM_LONGHUN_BAG or self.from_view == ItemTip.FROM_LONGHUN_EQUIP then
					temp_title = Language.F2Tip.SysCurAttrTitle[1]
				end
				local temp_data = {title = temp_title, desc = attr_desc}
				table.insert(info_list, replace_idx, temp_data)
			end
		end
	end
	local data = {}
	data.item_id = item_cfg.id
	set_need_sys_attr(data, item_cfg.sys_attr_cap_location)
	self["bagua_equip_tip_"..index]:SetItemDesc(info_list)
	local have_cfg_cap = item_cfg.capability_show and item_cfg.capability_show > 0
	if need_get_sys and show_cap_type == TIPS_CAP_SHOW.SHOW_CALC and capability > 0 then
		self["bagua_equip_tip_"..index]:SetCapabilityPanel({capability = capability})
	elseif (need_get_sys and show_cap_type == TIPS_CAP_SHOW.SHOW_CALC) or (not need_get_sys or not show_cap_type) then
		if have_cfg_cap then
			self["bagua_equip_tip_"..index]:SetCapabilityPanel({capability = item_cfg.capability_show})
		end
	end
end

--显示获得途径
function BaGuaContrastTip:ShowItemGetDesc(item_cfg,index)
	local info_list = {}
	if (item_cfg.get_msg and 0 < string.len(item_cfg.get_msg)) or not item_cfg.get_way or 0 == string.len(item_cfg.get_way) then
		local get_msg = item_cfg.get_msg
		if not get_msg or 0 == string.len(get_msg) then
			return
		end
		if item_cfg.is_display_role ~= 0 then
			info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
		else
			info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
		end
	elseif (item_cfg.get_way_des and 0 < string.len(item_cfg.get_way_des)) then
		local get_msg = item_cfg.get_way_des
		if not get_msg or 0 == string.len(get_msg) then
			return
		end
		info_list.desc = ToColorStr(string.format(Language.Common.TipsBrackets, get_msg), TIPS_COLOR.GETWAY_VALUE)
	end
	if item_cfg.xiushici and item_cfg.xiushici ~= "" then
		info_list.xiushici = item_cfg.xiushici
	end
	if not IsEmptyTable(info_list) then
		info_list.title = Language.F2Tip.GetWayStr
		self["bagua_equip_tip_"..index]:SetGetWayDesc(info_list)
	end
end

--解析道具tips
function BaGuaContrastTip:ParseProp(item_cfg, index)
	local fun_open_name = item_cfg.funopen_limit or ""
	local show_fun_limit = false
	if fun_open_name ~= "" then
		local is_open = FunOpen.Instance:GetFunIsOpened(fun_open_name)
		show_fun_limit = not is_open
	end

	local rich_level = ""
	-- if not show_fun_limit then
	-- 	rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
	-- 	if item_cfg.is_display_role ~= 0 then
	-- 		rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
	-- 	end
	-- end
	local _,_,zhanli = TianShenBaGuaWGData.Instance:GetBaGuaEquipAttrAndZhanLi(item_cfg.id,true)
	rich_level = string.format(Language.TianShen.BaGuaTipZhanLiName,TIPS_COLOR.SOCRE,zhanli)
	self["bagua_equip_tip_"..index]:SetSyntheticalSocre(rich_level)

	local rich_type = ""
	if item_cfg.goods_type and item_cfg.goods_type ~= 0 then
		rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.ShowItemType[item_cfg.goods_type])
	end
	self["bagua_equip_tip_"..index]:SetEquipSocre(rich_type)
end

function BaGuaContrastTip:OperationClickHandler(index)
	if self.btn_callback_event and self.btn_callback_event[index] then
		self.btn_callback_event[index].callback()
		self:Close()
	end
end