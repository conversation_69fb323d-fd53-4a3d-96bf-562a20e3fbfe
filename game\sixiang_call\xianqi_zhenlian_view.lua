-------------------------------
-- 四象召唤--仙器真炼
-------------------------------
ShenJiTianCiView = ShenJiTianCiView or BaseClass(SafeBaseView)
-- local XQZL_SPINE_ANIM = {
--     NOR = "idle",
--     OPEN = "open",
-- }

function ShenJiTianCiView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.flush_model_time = 10
    self:SetMaskBg(true)
    self.view_name = GuideModuleName.ShenJiTianCiView
	self:AddViewResource(0, "uis/view/shenji_tianci_ui_prefab", "layout_xianqi_zhenlian")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

--初始化入口
function ShenJiTianCiView:LoadCallBack()

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

    if not self.item_data_event then
        self.item_data_event = BindTool.Bind1(self.OnItemDataChange, self)
        ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
    end

    if not self.xqzl_model_display then
        self.xqzl_model_display = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["xqzl_model_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        
        self.xqzl_model_display:SetRenderTexUI3DModel(display_data)
        -- self.xqzl_model_display:SetUI3DModel(self.node_list["xqzl_model_display"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    -- 绑定红点
	self.remind_change = BindTool.Bind(self.ShenJiRemindCallBack, self)
	RemindManager.Instance:Bind(self.remind_change, RemindName.ShenJiEquipSub)

    self:InitXQZLParam()
    self:InitXQZLListener()
    self:InitXQZLItemList()
    self:InitOneXQZLConst()
    self:InitTenXQZLConst()
    self:InItToggleList()
    --self:InitXQZLDanmu()
    --self:InitXQZLStaticText()
    if self.tween_root_bg then
        self.tween_root_bg:Restart()
    else
        self.tween_root_bg = self.node_list["xqzl_bg_root"].transform:DORotate(Vector3(0, 0, -360), 20, DG.Tweening.RotateMode.FastBeyond360)
        self.tween_root_bg:SetEase(DG.Tweening.Ease.Linear)
        self.tween_root_bg:SetLoops(-1)
    end

end

function ShenJiTianCiView:ReleaseCallBack()
    CountDownManager.Instance:RemoveCountDown(self.xqzl_count_down_key)

    if self.xqzl_item_list then
        for k,v in pairs(self.xqzl_item_list) do
            v:DeleteMe()
        end
        self.xqzl_item_list = nil
    end

    if self.sjtc_item_list then
		for k,v in pairs(self.sjtc_item_list) do
			for t,q in pairs(v) do
				q:DeleteMe()
			end
		end
		self.sjtc_item_list = nil
	end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.xqzl_model_display then
        self.xqzl_model_display:DeleteMe()
        self.xqzl_model_display = nil
    end

    if self.item_data_event then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
        self.item_data_event = nil
    end

    if self.remind_change then
		RemindManager.Instance:UnBind(self.remind_change)
	    self.remind_change = nil
	end

    if self.tween_root_bg then
        self.tween_root_bg:Kill()
        self.tween_root_bg = nil
    end

    self:CancelQuest()

	self.cur_select_index = nil

    self:InitXQZLParam()
end

function ShenJiTianCiView:InitXQZLParam()
    self.xqzl_single_item = nil
    self.xqzl_multi_item = nil
    self.sjtc_act_list = nil
    self.xqzl_special_const_id = 0
    self.xqzl_count_down_key = "xianqi_zhenlian_count_down"
    self.xqzl_tedian_is_open = false
    self.xqzl_danmu_index = 1
    self.xqzl_is_draw_cd = false
end

function ShenJiTianCiView:InitXQZLListener()
    --组件事件监听
    --XUI.AddClickEventListener(self.node_list.xqzl_tip_btn, BindTool.Bind1(self.OnClickXQZLTipBtn, self))
    XUI.AddClickEventListener(self.node_list.xqzl_record_btn, BindTool.Bind1(self.OnClickXQZLRecordBtn, self))
    XUI.AddClickEventListener(self.node_list.xqzl_jump_btn, BindTool.Bind1(self.OnClickXQZLSiXiangBtn, self))
    XUI.AddClickEventListener(self.node_list.xqzl_one_call_btn, BindTool.Bind(self.OnClickXQZLBtn, self, MACHINE_DRAW_TYPE.SINGLE))
    XUI.AddClickEventListener(self.node_list.xqzl_ten_call_btn, BindTool.Bind(self.OnClickXQZLBtn, self, MACHINE_DRAW_TYPE.MULTI))
    XUI.AddClickEventListener(self.node_list.xqzl_one_call_const_img, BindTool.Bind(self.OnClickXQZLConstImg, self, MACHINE_DRAW_TYPE.SINGLE))
    XUI.AddClickEventListener(self.node_list.xqzl_ten_call_const_img, BindTool.Bind(self.OnClickXQZLConstImg, self, MACHINE_DRAW_TYPE.MULTI))
    XUI.AddClickEventListener(self.node_list.xqzl_exchange_btn, BindTool.Bind1(self.OnClickXQZLExchangeBtn, self))
    XUI.AddClickEventListener(self.node_list.display_item_btn, BindTool.Bind1(self.OnClickDisPlayItemTipsBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.Close, self))
    XUI.AddClickEventListener(self.node_list["gailv_btn"], BindTool.Bind(self.OnClickGaiLv, self))
end

function ShenJiTianCiView:InitXQZLItemList()
    local res_async_loader = AllocResAsyncLoader(self, "xqzl_item_list")
    res_async_loader:Load("uis/view/shenji_tianci_ui_prefab", "xianqi_zhenlian_render", nil,
        function(new_obj)
            local item_root = self.node_list.xqzl_item_root_list
            local item_list = {}
            for i=1,6 do
                local root = item_root:FindObj("item_root_" .. i)
                local obj = ResMgr:Instantiate(new_obj)
                if root and obj then
                    obj.transform:SetParent(root.transform, false)
                end
                item_list[i] = XianQiZhenLianItem.New(obj)
                item_list[i]:SetIndex(i)
                item_list[i]:SetIsFivePointStar(true)
            end
            self.xqzl_item_list = item_list
            self:FlushXQZLItemList()
            self:FlushModel()
            self:StartQuest()
            self:FlushXQZLItemIcon()

        end)
    self.node_list.title_view_name.text.text = Language.SiXiangCall.XQZLTipTitle
end

function ShenJiTianCiView:FlushXQZLItemList()
    local show_list = SiXiangCallWGData.Instance:GetXQZLShowItemList()
    if IsEmptyTable(show_list) then
        return
    end
    local item_cell_list = self.xqzl_item_list
    for i = 1, #item_cell_list do
        item_cell_list[i]:SetData(show_list[i])
    end
end


function ShenJiTianCiView:OnFlush(param_t)
    for k,v in pairs(param_t) do
        if k == "all" then
            self:FlushOneXQZLConst()
            self:FlushTenXQZLConst()
            self:FlushXQZLTeDianPanel()
        -- elseif k == "sever_log" then
        --     self:FlushXQZLDanmu()
        -- elseif k == "xianqi_reward" then
        --     self:XianQiReturnReward()
        end
    end
end

function ShenJiTianCiView:OnClickXQZLTipBtn()
    RuleTip.Instance:SetContent(Language.SiXiangCall.XQZLTipContent, Language.SiXiangCall.XQZLTipTitle)
end

--抽奖记录
function ShenJiTianCiView:OnClickXQZLRecordBtn()
    SiXiangCallWGCtrl.Instance:OpenXianQiRecordView()
end

function ShenJiTianCiView:OnClickXQZLSiXiangBtn()
    ViewManager.Instance:Open(GuideModuleName.HiddenWeaponView)
end

function ShenJiTianCiView:OnClickXQZLExchangeBtn()
    ViewManager.Instance:Open(GuideModuleName.SiXiangExchange)
end


---[[ 抽奖相关
function ShenJiTianCiView:OnClickXQZLBtn(_type)
    -- if self.xqzl_is_draw_cd then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.SiXiangCall.DrawCdTips)
    --     return
    -- end
    -- self.xqzl_is_draw_cd = true
    ReDelayCall(self, function()
        --self.xqzl_is_draw_cd = false
    end, 1, "OnClickXQZLBtn")

    if _type == MACHINE_DRAW_TYPE.SINGLE then
        if self.xqzl_single_item then
            SiXiangCallWGCtrl.Instance:TryToXianQiZhenLian(self.xqzl_single_item.item_id, self.xqzl_single_item.num, MACHINE_DRAW_TYPE.SINGLE)
        end
    elseif _type == MACHINE_DRAW_TYPE.MULTI then
        if self.xqzl_multi_item then
            --十连道具判断
            local send_type = self.xqzl_multi_item.item_id == self.xqzl_special_const_id and MACHINE_DRAW_TYPE.USE_MULTI_ITEM or MACHINE_DRAW_TYPE.MULTI
            SiXiangCallWGCtrl.Instance:TryToXianQiZhenLian(self.xqzl_multi_item.item_id, self.xqzl_multi_item.num, send_type)
        end
    end
end

function ShenJiTianCiView:OnClickXQZLConstImg(_type)
    local item_id = 0
    if _type == MACHINE_DRAW_TYPE.SINGLE then
        item_id = self.xqzl_single_item and self.xqzl_single_item.item_id
    elseif _type == MACHINE_DRAW_TYPE.MULTI then
        item_id = self.xqzl_multi_item and self.xqzl_multi_item.item_id
    end
    if item_id > 0 then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
    end
end

--神机装备红点
function ShenJiTianCiView:ShenJiRemindCallBack(remind_name, num)
	if remind_name == RemindName.ShenJiEquipSub then
		self.node_list["xqzl_red_jump"]:SetActive(num > 0)
	end
end

function ShenJiTianCiView:InItToggleList()
    local act_list = XianQiTeDianWGData.Instance:GetShowActIdList()
    if IsEmptyTable(act_list) then
        return
    end

    self:CreateXQZLTedianItem(act_list)
end

---[[ 消耗道具显示相关
function ShenJiTianCiView:InitOneXQZLConst()
    local summon_single_item = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("single_lotto_consume")
    if summon_single_item then
        local bundle, asset = ResPath.GetItem(summon_single_item.item_id)
        self.node_list.xqzl_one_call_const_img.image:LoadSprite(bundle, asset)
    end
    self.xqzl_single_item = summon_single_item
end

function ShenJiTianCiView:InitTenXQZLConst()
    ---[[ 特殊的十连召唤符
    local special_item_id = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("multi_lotto_consume_special")
    if special_item_id then
        self.xqzl_special_const_id = special_item_id
        local has_num = ItemWGData.Instance:GetItemNumInBagById(special_item_id)
        if has_num > 0 then
            local bundle, asset = ResPath.GetItem(special_item_id)
            self.node_list.xqzl_ten_call_const_img.image:LoadSprite(bundle, asset)
            self.node_list.xqzl_ten_discount_bg:SetActive(false)

            self.xqzl_multi_item = {item_id = special_item_id, num = 1}
            return
        end
    end
    --]]
    
    local xqzl_multi_item = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("multi_lotto_consume")
    if xqzl_multi_item then
        local bundle, asset = ResPath.GetItem(xqzl_multi_item.item_id)
        self.node_list.xqzl_ten_call_const_img.image:LoadSprite(bundle, asset)

        local multi_lotto = SiXiangCallWGData.Instance:GetXQZLOtherCfgByKey("multi_lotto")
        if multi_lotto then
            local discount = math.floor(xqzl_multi_item.num / multi_lotto * 10)
            self.node_list.xqzl_ten_discount_bg:SetActive(discount < 10)
            self.node_list.xqzl_ten_call_dis_lbl.text.text = string.format(Language.SiXiangCall.DiscountText, discount)
        end
    end
    self.xqzl_multi_item = xqzl_multi_item
end

function ShenJiTianCiView:OnItemDataChange(change_item_id)
    if self.xqzl_multi_item and change_item_id == self.xqzl_multi_item.item_id then
        self:FlushOneXQZLConst()
        self:FlushTenXQZLConst()
    end
    if self.xqzl_single_item and change_item_id == self.xqzl_single_item.item_id then
        self:FlushOneXQZLConst()
        self:FlushTenXQZLConst()
    end
    if change_item_id == self.xqzl_special_const_id then
        self:InitTenXQZLConst()
        self:FlushTenXQZLConst()
    end
end

function ShenJiTianCiView:FlushOneXQZLConst()
    if self.xqzl_single_item then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(self.xqzl_single_item.item_id)
        local need_num = self.xqzl_single_item.num or 0
        local str = string.format("%d/%d", has_num, need_num)
        local str_color = has_num >= need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list.xqzl_one_call_const_lbl.text.text = ToColorStr(str, str_color)
        self.node_list["xqzl_red_one"]:SetActive(has_num >= need_num)
    end
end

function ShenJiTianCiView:FlushTenXQZLConst()
    if self.xqzl_multi_item then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(self.xqzl_multi_item.item_id)
        if self.xqzl_multi_item.item_id == self.xqzl_special_const_id and has_num <= 0 then
            self:InitTenXQZLConst()
        end
        local need_num = self.xqzl_multi_item.num or 0
        local str = string.format("%d/%d", has_num, need_num)
        local str_color = has_num >= need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.node_list.xqzl_ten_call_const_lbl.text.text = ToColorStr(str, str_color)
        self.node_list["xqzl_red_ten"]:SetActive(has_num >= need_num)
    end
end

function ShenJiTianCiView:FlushXQZLItemIcon()
    local show_list = SiXiangCallWGData.Instance:GetXQZLShowItemList()
    for k, v in pairs(show_list) do
        local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
        if item_cfg and self.xqzl_item_list and self.xqzl_item_list[k] then
            local bundle,asset = ResPath.GetSHENJIImgPath(item_cfg.icon_id)
            self.xqzl_item_list[k].node_list.item_icon.image:LoadSprite(bundle, asset)
            self.xqzl_item_list[k].node_list.item_icon.image:SetNativeSize()
        end
    end
end

---[[ 仙器特典
function ShenJiTianCiView:FlushXQZLTeDianPanel()
    local is_open_tedain = XianQiTeDianWGData.Instance:GetIsOpenTeDian()
    self.xqzl_tedian_is_open = is_open_tedain
    self.node_list.tedian_xqzl_list:SetActive(is_open_tedain)
    self.node_list.close_bg:SetActive(not is_open_tedain)
    if is_open_tedain then
        self:FlushXQZLTeDianOpen()
    else
        self:FlushXQZLTeDianClose()
    end
    self:FlushXQZLTeDianTime()
end

--刷新Toogle列表
function ShenJiTianCiView:FlushXQZLTeDianOpen()
    local data_list = XianQiTeDianWGData.Instance:GetActSaleItemListCfg()
    --print_error(data_list)
    if IsEmptyTable(data_list) or IsEmptyTable(self.sjtc_item_list) then
        return
    end
--[[ 
    for k, v in pairs(data_list) do
        local show_img_path = XianQiTeDianWGData.Instance:GetActRelationCfg(v[1].subactivity_id)
        self.sjtc_act_list[k].bg.image:LoadSprite(ResPath.GetNoPackPNG("a1_sjtf_" .. show_img_path.subactivity_show))
        self.sjtc_act_list[k].title.image:LoadSprite(ResPath.GetNoPackPNG("a1_sjtf_gg" .. show_img_path.subactivity_show))
    end ]]

    for k, v in pairs(self.sjtc_item_list) do
        local this_data = data_list[k]
        --print_error("this_data", this_data)
        if this_data ~= nil then
            for k1,v1 in pairs(v) do
                --print_error("v1", v1.data)
                --print_error("this_data[k1]", this_data[k1])
				v1:SetData(this_data[k1])
			end
        end
    end
end

function ShenJiTianCiView:FlushXQZLTeDianClose()
    
end

function ShenJiTianCiView:CreateXQZLTedianItem(need_item_list)
    if self.sjtc_act_list then return end
    self.sjtc_act_list = {}
    local data_list = XianQiTeDianWGData.Instance:GetActSaleItemListCfg()

    for i = 1, 4 do
		self.node_list["SJTC_SelectBtn"..i]:SetActive(i <= #need_item_list)
	end

    for i = 1, #need_item_list do
        self.sjtc_act_list[i] = {}
        self.sjtc_act_list[i].select_btn = self.node_list["SJTC_SelectBtn".. i]
        self.sjtc_act_list[i].list = self.node_list["SJTC_List".. i]
        --self.sjtc_act_list[i].list.canvas_group = 0
        self.sjtc_act_list[i].bg = self.node_list["SJTC_SelectBtnbg_" .. i]
        self.sjtc_act_list[i].title = self.node_list["SJTC_SelectBtnbg_title_" .. i]
        self.sjtc_act_list[i].select_btn_toggle = self.node_list["SJTC_SelectBtn" .. i].gameObject:GetComponent(typeof(UnityEngine.UI.Toggle))
        self.sjtc_act_list[i].select_btn_toggle:AddValueChangedListener(BindTool.Bind(self.AccorBtnClickCallback, self, i))
        self.sjtc_act_list[i].list_item_cell = {}

        local item_data = nil
        if data_list[i] ~= nil then
            item_data = data_list[i]
        end
        self:LoadXQZLTedianItem(i, item_data)
    end

--[[     for k, v in pairs(need_item_list) do
        local show_img_path = XianQiTeDianWGData.Instance:GetActRelationCfg(v)
        self.sjtc_act_list[k].bg.image:LoadSprite(ResPath.GetNoPackPNG("a1_sjtf_" .. show_img_path.subactivity_show))
        self.sjtc_act_list[k].title.image:LoadSprite(ResPath.GetNoPackPNG("a1_sjtf_gg" .. show_img_path.subactivity_show))
    end ]]
 
end

function ShenJiTianCiView:AccorBtnClickCallback(index)
    if self.sjtc_act_list[index].select_btn and self.sjtc_act_list[index].list then
        local is_on = self.sjtc_act_list[index].select_btn.accordion_element.isOn
        local alpha_min = is_on and 0 or 1
        local alpha_max = (not is_on) and 0 or 1
        self.sjtc_act_list[index].list.canvas_group:DoAlpha(alpha_min, alpha_max, 0.2)
    end
end

function ShenJiTianCiView:LoadXQZLTedianItem(index, item_data)
    if nil == item_data then
		return
	end

    local res_async_loader = AllocResAsyncLoader(self, "xianqi_zhenlian_item" .. index)
    --local data_list = XianQiTeDianWGData.Instance:GetActSaleItemListCfg()
    res_async_loader:Load("uis/view/shenji_tianci_ui_prefab", "xianqi_zhenlian_item", nil,
        function(new_obj)
            local item_vo = {}
            --local this_data = data_list[index]
            --local num = 1
            for i = 1, #item_data do
                local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
                obj_transform:SetParent(self.sjtc_act_list[index].list.transform, false)
                self.sjtc_act_list[index].list_item_cell[i] = obj
                local item_render = XianQiZhenLianTeDianItem.New(obj)
                item_render:SetData(item_data[i])
                item_vo[i] = item_render
            end
            -- for k, v in pairs(this_data) do
            --     local obj = ResMgr:Instantiate(new_obj)
			-- 	local obj_transform = obj.transform
            --     obj_transform:SetParent(self.sjtc_act_list[index].list.transform, false)
            --     self.sjtc_act_list[index].list_item_cell[num] = obj
            --     num = num + 1
            --     local item_render = XianQiZhenLianTeDianItem.New(obj)
            --     item_render:SetData(this_data[k])
            --     item_vo[k] = item_render
            -- end

            if nil == self.sjtc_item_list then
				self.sjtc_item_list = {}
			end
            self.sjtc_item_list[index] = item_vo
            self.sjtc_act_list[1].select_btn.accordion_element.isOn = true
        end)
end

function ShenJiTianCiView:FlushXQZLTeDianTime()
    CountDownManager.Instance:RemoveCountDown(self.xqzl_count_down_key)
    local end_time = XianQiTeDianWGData.Instance:GetTeDianEndTimeStamp()
    local sever_time = TimeWGCtrl.Instance:GetServerTime()
    if end_time <= sever_time then
        if self.node_list and self.node_list.tedian_xqzl_time then
            self.node_list.tedian_xqzl_time.text.text = ""
        end
        return
    end

    self:XQZLTeDianUpdateCountDown(sever_time, end_time)

    CountDownManager.Instance:AddCountDown(
        self.xqzl_count_down_key,
        BindTool.Bind(self.XQZLTeDianUpdateCountDown, self),
        BindTool.Bind(self.FlushXQZLTeDianTime, self),
        end_time,
        nil,
        1
    )
end

function ShenJiTianCiView:XQZLTeDianUpdateCountDown(elapse_time, total_time)
    local time_str = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
    time_str = ToColorStr(time_str, COLOR3B.GREEN)
    if self.node_list and self.node_list.tedian_xqzl_time then
        if self.xqzl_tedian_is_open then
            self.node_list.tedian_xqzl_time.text.text = string.format(Language.SiXiangCall.CloseTimeDesc, time_str)
        else
            self.node_list.tedian_xqzl_time.text.text = string.format(Language.SiXiangCall.OpenTimeDesc, time_str)
        end
    end
end


function ShenJiTianCiView:GetZhenLianSkipToggleState()
    if self.node_list.xqzl_skip_btn then
        return self.node_list.xqzl_skip_btn.toggle.isOn
    end

    return true
end

function ShenJiTianCiView:StartQuest()
    if not self.flush_model_timer_quest then
		self.flush_model_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushModel, self), self.flush_model_time)
	end
end

function ShenJiTianCiView:FlushModel()
    if IsEmptyTable(self.xqzl_item_list) then
        return
    end

    local max_num = 6
    if self.cur_select_index then
        self.cur_select_index = self.cur_select_index < max_num and self.cur_select_index + 1 or 1
    else
        self.cur_select_index = 1
    end

    for i = 1, max_num do
        self.xqzl_item_list[i]:OnSelectChange(self.cur_select_index == i)
    end

    local cell_data = self.xqzl_item_list[self.cur_select_index]:GetData()
    if cell_data then
        local cfg = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(cell_data.item_id)
        if cfg then
            self.node_list.show_item_name.text.text = cfg.name

            local model_bundle, model_asset = ResPath.GetArtifactModel(cfg.base_model)
            self.xqzl_model_display:SetMainAsset(model_bundle, model_asset)
        end
    end

    local item_label = SiXiangCallWGData.Instance:GetXQZLShowItemTag()
    local item_tag = SiXiangCallWGData.Instance:GetXQZLShowItemSecTag()
    self.node_list.display_item_label.text.text = item_label[self.cur_select_index]
    self.node_list.display_item_labels.text.text = item_tag[self.cur_select_index]
end

function ShenJiTianCiView:OnClickDisPlayItemTipsBtn()
    local cell_data = self.xqzl_item_list[self.cur_select_index]:GetData()
    if cell_data then
        TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROM_NORMAL, nil)
    end
end

function ShenJiTianCiView:CancelQuest()
    if self.flush_model_timer_quest then
		GlobalTimerQuest:CancelQuest(self.flush_model_timer_quest)
		self.flush_model_timer_quest = nil
	end
end

function ShenJiTianCiView:OnClickGaiLv()
	local info = XianQiTeDianWGData.Instance:GetRandomGaiLvinfo()
	TipWGCtrl.Instance:OpenGaiLvShowView(info)
end

----------------------- 仙器召唤特典 ----------------------------------

XianQiZhenLianTeDianItem = XianQiZhenLianTeDianItem or BaseClass(BaseRender)

function XianQiZhenLianTeDianItem:__delete()

end


function XianQiZhenLianTeDianItem:LoadCallBack()
    self.item_list_view = AsyncListView.New(ItemCell,self.node_list["item_list"])
    XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind1(self.OnClickTeDianItem, self))
end

function XianQiZhenLianTeDianItem:ReleaseCallBack()
    if self.item_list_view then
		self.item_list_view:DeleteMe()
		self.item_list_view = nil
    end
end

function XianQiZhenLianTeDianItem:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    --售卖信息
    local sale_info = XianQiTeDianWGData.Instance:GetSubActSaleInfo(data.subactivity_id, data.product_id)
    self.node_list["title_name"].text.text = string.format(Language.SiXiangCall.SJTCDrawShopItemTitle, sale_info.buy_num, data.limit_buy_times)

    local item_data = SortDataByItemColor(data.item)
	self.item_list_view:SetDataList(item_data)

    self.node_list["xianyv_root"]:SetActive(data.price_type == 1) --仙玉类型
    self.node_list["rmb_price"]:SetActive(data.price_type == 0) --直购类型
    self.node_list["discount"]:SetActive(data.discount > 0 and sale_info.buy_num < data.limit_buy_times) --折扣
    self.node_list["discount_num"].text.text = string.format(Language.SiXiangCall.ZheKou, data.discount)

    if data.price_type == 1 then
        self.node_list["xianyv_price"].text.text = data.special_sale_price
    elseif data.price_type == 0 then
        local flag = data.product_id * 100 + data.subactivity_id
        local price = RoleWGData.GetPayMoneyStr(data.special_sale_price, GET_GOLD_REASON.GET_GOLD_REASON_SHEN_JI_BAI_LIAN, flag)
        self.node_list["rmb_price"].text.text = price
    end

    self.node_list["btn_buy"]:SetActive(sale_info.buy_num < data.limit_buy_times)
    self.node_list["is_buy"]:SetActive(sale_info.buy_num >= data.limit_buy_times)
    
end


function XianQiZhenLianTeDianItem:OnClickTeDianItem()
    local data = self:GetData()
    if not IsEmptyTable(data) then
		XianQiTeDianWGCtrl.Instance:BuyXianQiTeDian(data.subactivity_id, data.product_id)
	end

end
