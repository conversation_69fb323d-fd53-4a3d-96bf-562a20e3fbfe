SubpackageWGData = SubpackageWGData or BaseClass()
function SubpackageWGData:__init()
    if SubpackageWGData.Instance then
        ErrorLog("[SubpackageWGData] Attemp to create a singleton twice !")
    end
    SubpackageWGData.Instance = self

    self.cfg = ConfigManager.Instance:GetAutoConfig("subpackage_download_auto")

    RemindManager.Instance:Register(RemindName.SubPackage_Download, BindTool.Bind(self.IsShowSubPackageIconRemind, self))
end

function SubpackageWGData:__delete()
    SubpackageWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.SubPackage_Download)
end

function SubpackageWGData:SetRewardTab(tab)
    self.reward_tab = tab
end

function SubpackageWGData:GetRewardIsFetch()
    if not self.reward_tab then
        return true
    end

    return self.reward_tab[0] == 1
end

function SubpackageWGData:SetNetWorkState(state, msg)
    self.network_state = state
    self.network_msg = msg
    GlobalEventSystem:Fire(NetWorkStateEvent.NET_WORK_STATE_CHANGE, state, msg)
end

function SubpackageWGData:GetNetWorkState()
    return self.network_state or NETWORK_STATE.MOBILE, self.network_msg or Language.SubPackage.MobileNetWork
end

--当有未下载且未开始下载的分包或者存在可领取的下载奖励时，存在红点
function SubpackageWGData:IsShowSubPackageIconRemind()
    if SubPackageWGCtrl.Instance:GetPackageCount() <= 0 then
        return 0
    end

    local total_size, downloaded_size, is_downloading = SubPackageWGCtrl.Instance:GetPackageInfo(1)
    if not is_downloading and total_size > downloaded_size then
        return 1
    end

    if total_size <= downloaded_size and not self:GetRewardIsFetch() then
        return 1
    end

    return 0
end

function SubpackageWGData:GetRewardItemList()
    local cfg = self.cfg.download_reward[1]
    local data_list = {}
    for i = 0, 9 do
        if cfg.reward_item[i] then
            table.insert(data_list, cfg.reward_item[i])
        else
            break
        end
    end
    return data_list
end

function SubpackageWGData:GetShowBtnPercent()
    return 0.7
end