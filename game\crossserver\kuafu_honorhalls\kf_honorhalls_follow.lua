require("game/task/task_follow_render")

--------------------------------------------------------------
--任务跟踪
--------------------------------------------------------------
HonorhallsFollow = HonorhallsFollow or BaseClass(SafeBaseView)

function HonorhallsFollow:__init()
	self.view_name = "HonorhallsFollow"
	self.active_close = false
	self:AddViewResource(0, "uis/view/kuafu_honorhalls_ui_prefab", "layout_role_info")
	self:AddViewResource(0, "uis/view/kuafu_honorhalls_ui_prefab", "layout_hh_new")
end

function HonorhallsFollow:ReleaseCallBack()
	if self.role_info then
		local obj = self.role_info.gameObject
		ResMgr:Destroy(obj)
		self.role_info = nil
	end
	if self.hh_new then
		local obj = self.hh_new.gameObject
		ResMgr:Destroy(obj)
		self.hh_new = nil
	end
	if self.rank_list then
		self.rank_list = nil
	end
	self.is_load_complete = nil
end

function HonorhallsFollow:ReleaseTaskPanel()
	local mainui_view = MainuiWGCtrl.Instance:GetView()
	-- mainui_view:SetTaskCallBack(nil)
	-- mainui_view:SetTeamCallBack(nil)
	mainui_view:SetTaskContents(true)
end

function HonorhallsFollow:CloseCallBack()
	if self.role_info then
		self.role_info:SetActive(false)
	end
	if self.hh_new then
		self.hh_new:SetActive(false)
	end
	self.change_quest = GlobalTimerQuest:AddDelayTimer(function()
		self:ReleaseTaskPanel()
	end, 0.1)
end

function HonorhallsFollow:OpenCallBack()
	if self.is_load_complete then
		if self.role_info then
			self.role_info:SetActive(true)
		end
		if self.hh_new then
			self.hh_new:SetActive(false)
		end
	end
end

function HonorhallsFollow:ShowIndexCallBack()
	local init_callback = function ()
		self:Init()
	end

	local mainuictrl = MainuiWGCtrl.Instance
	mainuictrl:AddInitCallBack(nil,init_callback)
end

function HonorhallsFollow:Init()
	self.role_info = self.node_list.layout_role_info_root
	self.hh_new = self.node_list.layout_hh_new_root
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()

	local mainui_view = MainuiWGCtrl.Instance:GetView()
	-- mainui_view:SetTaskCallBack(BindTool.Bind2(self.OnHonorhallsFollowType,self,1))
	-- mainui_view:SetTeamCallBack(BindTool.Bind2(self.OnHonorhallsFollowType,self,2))
	mainui_view:SetTaskContents(false)
	-- mainui_view:ChangeTaskBtnName(Language.Task.task_text6)
	mainui_view:ChangeTeamBtnName(Language.Task.task_text7)
	-- mainui_view:SetTaskButtonTrue()

	self.role_info.transform:SetParent(parent.transform)
	self.hh_new.transform:SetParent(parent.transform)
	self.role_info.transform.anchoredPosition = Vector2(0, 0)
	self.hh_new.transform.anchoredPosition = Vector2(0,-28.5)
	self.role_info.transform:SetLocalScale(1, 1, 1)
	self.hh_new.transform:SetLocalScale(1, 1, 1)

	self.node_list.layout_hh_new_root:SetActive(false)
	self.rank_list = AsyncListView.New(HonorhallRankItemRender,self.node_list.ph_rank_list)
	self.rank_list:JumpToTop()
	self.ronyu_item_list = {}
	for i = 1, 5 do
		self.ronyu_item_list[i] = ItemCell.New()
		self.ronyu_item_list[i]:SetInstanceParent(self.node_list["ph_info_item_"..i])
	end

	self.ronyu_rank_list = {}
	for i = 1, 3 do
		self.ronyu_rank_list[i] = ItemCell.New()
		self.ronyu_rank_list[i]:SetInstanceParent(self.node_list["ph_rank_item_"..i])
	end

	self.node_list.check_reward_btn.button:AddClickListener(BindTool.Bind(self.OnCheckRewardClick,self))

	self:FlushHonorhallsFollowView()
	self:OnClickInfo()
	self.is_load_complete = true
end

function HonorhallsFollow:OnFlush(param_t)
	for k,v in pairs(param_t or {"all"}) do
		if k == "FlushRank" then
			self:FlushRank()
		elseif k == "FlushRoleInfo" then
			self:FlushRoleInfo()
		end
	end
end

-- function HonorhallsFollow:OnHonorhallsFollowType(index)
-- 	if index == 1 then
-- 		self.role_info:SetActive(true)
-- 		self.hh_new:SetActive(false)
-- 	else
-- 		self.role_info:SetActive(false)
-- 		self.hh_new:SetActive(true)
-- 	end
-- end

function HonorhallsFollow:OnCheckRewardClick()
	KuafuHonorhallWGCtrl.Instance:OpenRewardView()
end

function HonorhallsFollow:OnClickInfo()
	self.node_list.layout_role_info_root:SetActive(true)
	self.node_list.layout_hh_new_root:SetActive(false)
	self:FlushRoleInfo()
end

function HonorhallsFollow:OnClickRank()
	self.node_list.layout_role_info_root:SetActive(false)
	self.node_list.layout_hh_new_root:SetActive(true)
	self:FlushRank()
end

function HonorhallsFollow:FlushHonorhallsFollowView()
	self:FlushRoleInfo()
	self:FlushRank()
end

function HonorhallsFollow:FlushRoleInfo()
	local role_info = KuafuHonorhallWGData.Instance:GetRoleInfo()
	if not role_info or not next(role_info) then return end
	local max_lay = KuafuHonorhallWGData.Instance:GetMaxLayer()
	self.node_list.label_cur_layer.text.text = role_info.cur_layer + 1
	self.node_list.text_factor.text.text = Language.Honorhalls.Nextfactor

	local reward_item = KuafuHonorhallWGData.Instance:GetRewardItem(role_info.cur_layer + 1)
	if role_info.cur_layer + 1 <= max_lay then
		for i = 1, 5 do
			if reward_item[i - 1] then
				self.node_list["ph_info_item_"..i]:SetActive(true)
				self.ronyu_item_list[i]:SetData(reward_item[i - 1])
			else
				self.node_list["ph_info_item_"..i]:SetActive(false)
				self.ronyu_item_list[i]:ClearData()
			end
		end
	end

	local rank_num = KuafuHonorhallWGData.Instance:GetRankNum()
	local rank_reward = KuafuHonorhallWGData.Instance:GetRankRewardAuto(rank_num)
	for i = 1, 3 do
		if rank_reward[i] then
			self.node_list["ph_rank_item_"..i]:SetActive(true)
			self.ronyu_rank_list[i]:SetData(rank_reward[i])
		else
			self.node_list["ph_rank_item_"..i]:SetActive(false)
			self.ronyu_rank_list[i]:ClearData()
		end
	end
	local need_kill = KuafuHonorhallWGData.Instance:GetUpLayerKills(role_info.cur_layer + 1)
	local color = role_info.cur_layer_kill_count >= need_kill and COLOR3B.D_GLOD or COLOR3B.RED
	local kill_count = role_info.cur_layer_kill_count
	self.node_list.label_curlayer_kill.text.text = "["..kill_count .."/".. need_kill.."]"
	local exp  = CommonDataManager.ConverExp(role_info.exp or 0)
	self.node_list.label_exp.text.text = exp
	if kill_count == 0 then
		local act_statu = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS) or {}
		if act_statu.status == ACTIVITY_STATUS.OPEN then
			local next_time = act_statu.next_time or 0
			local count_time = next_time - TimeWGCtrl.Instance:GetServerTime()
			if count_time > 0 then
				MainuiWGCtrl.Instance:SetFbIconEndCountDown(next_time,false)
			end
		end
	end
end

function HonorhallsFollow:FlushRank()
	if self.rank_list then
		local data = KuafuHonorhallWGData.Instance:GetRankInfo()
		self.rank_list:SetDataList(data)
	end
end


-----------------------------------------------------------------------------
------------------------排行ItemRender---------------------------------------
-----------------------------------------------------------------------------
HonorhallRankItemRender = HonorhallRankItemRender or BaseClass(BaseRender)

function HonorhallRankItemRender:OnFlush()
	if not self.data then
		return
	end
	self.node_list.lbl_rank.text.text = self.index
	local user_name = RoleWGData.ParseCrossServerUserName(self.data.user_name)
	self.node_list.lbl_name.text.text = user_name
	local time_str = string.format(Language.Honorhalls.Layer, self.data.max_layer + 1)
	if self.data.finish_time > 0 then
		time_str = TimeUtil.FormatSecond2MS(self.data.finish_time) or ""
	end
	self.node_list.lbl_time.text.text = time_str

	--设置个人排名
	local uuid = GameVoManager.Instance:GetMainRoleVo().uuid
	if self.data.uuid == uuid then
		KuafuHonorhallWGData.Instance:SetPersonRankInfo(self.index, self.data.finish_time)
	end
end