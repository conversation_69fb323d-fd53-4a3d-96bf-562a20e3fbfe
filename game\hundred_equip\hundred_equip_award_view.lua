function HundredEquipView:LoadAwardCallBack()
    self.scroll_ratio_task_list = AsyncListView.New(HundredEquipAwardRender, self.node_list.scroll_ratio_task_list)
    XUI.AddClickEventListener(self.node_list.btn_task_levelup, BindTool.Bind1(self.OnClickLevelUp, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.ShowEquipAwardPanel, self, false))

	self.ea_up_level_effect_pos = self.node_list.ea_up_level_effect.transform.localPosition
end

function HundredEquipView:ReleaseAwardCallBack()
    self.task_list_data = nil

	if self.scroll_ratio_task_list then
		self.scroll_ratio_task_list:DeleteMe()
		self.scroll_ratio_task_list = nil
	end
	self:StopMoveTween()
end

function HundredEquipView:OnFlushAward()
    self:SetTaskInfo()
    self:SetProgress()
end

function HundredEquipView:SetProgress()
    local level = HundredEquipWGData.Instance:GetLevelValue()
    local exp_per_time = HundredEquipWGData.Instance:GetExpPerTimes()
    local next_task_data =  HundredEquipWGData.Instance:GetLevelCfgByLevel(level + 1)
    local cur_level_cfg = HundredEquipWGData.Instance:GetLevelCfgByLevel(level)

    local now_num = exp_per_time
    local target_num = next_task_data and (next_task_data.exp_per_times_limit or 0) or cur_level_cfg.exp_per_times_limit

    local sum_num = 0--#self.task_list_data
    -- local over_num = 0
    -- for k_1, v_1 in ipairs(self.task_list_data) do
    --     over_num = v_1.state == 1 and over_num + 1 or over_num
    -- end
    local has_max_level = GetTableLen(next_task_data) == 0
    self.node_list.text_ratio_exp_progress.tmp.text = string.format(Language.HundredEquip.ExpText, now_num, target_num) -- string.format("%s/%s", over_num, sum_num)
    self.node_list.slider_ratio_level.slider.value =  now_num / target_num -- over_num / sum_num
    self.node_list.text_now_ratio_level.tmp.text = level
    -- self.node_list.text_next_ratio_level.tmp.text = "Lv." .. (has_max_level and level or level + 1)
    -- self.node_list.text_next_ratio_level:SetActive(not has_max_level)

    self.node_list.btn_task_levelup:SetActive(not has_max_level)
end

function HundredEquipView:SetTaskInfo()
    local level = HundredEquipWGData.Instance:GetLevelValue()
    local next_cfg = HundredEquipWGData.Instance:GetLevelCfgByLevel(level + 1)
	if next_cfg then
    	--self.node_list.award_next_task_name.tmp.text = string.format(Language.HundredEquip.MainTxt1, next_cfg.map_type, next_cfg.percent_bonus)
		self.node_list.award_next_task_name.tmp.text = string.format(Language.HundredEquip.MainTxt14, next_cfg.percent_bonus)
	else
		self.node_list.award_next_task_name.tmp.text = ""
	end

    self.task_list_data = HundredEquipWGData.Instance:GetRatioTaskListData()
    self.scroll_ratio_task_list:SetDataList(self.task_list_data)

    local level_up = HundredEquipWGData.Instance:GetTaskLevelUpRed()
    self.node_list.image_task_levelup_red:SetActive(level_up)
end

function HundredEquipView:OnClickLevelUp()
	if self.playing_effect then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HundredEquip.PlayingUpLevelAnimText)
	else
		local level_up = HundredEquipWGData.Instance:GetTaskLevelUpRed()
		if not level_up then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HundredEquip.MainTxt2)
			return
		end
	
		HundredEquipWGCtrl.Instance:SendHundredEquipRequest(HUNDREDFOLD_DROP_OPERATE_TYPE.HUNDREDFOL_UP_LEVEL)
	end
end

--升级成功
function HundredEquipView:PlayEffect()
	--播放升级成功特效
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 200)})
	
	--self:StopMoveTween()

	--通知render开始播放动画.
	self:SendHundredEquipRenderStartPlayEffect()

	-- --播放升级指引特效.
	-- local target_pos = self.node_list.ea_effect_target_pos.transform.localPosition
	-- self.node_list.ea_up_level_effect:SetActive(true)
	-- self.move_tween = self.node_list.ea_up_level_effect.transform:DOLocalMove(target_pos, 1.5):OnComplete(function()
	-- 	self.node_list.ea_up_level_effect:SetActive(false)
	-- 	self.node_list.ea_up_level_effect.transform.localPosition = self.ea_up_level_effect_pos
		
	-- 	--特效播放结束后，开始播放加成变化动画.
	-- 	self:PlayNumChangeEvent()
	-- end)
	self:PlayNumChangeEvent()
end

function HundredEquipView:StopMoveTween()
	if self.move_tween then
		self.move_tween:Kill()
		self.move_tween = nil
	end
	self.node_list.ea_up_level_effect:SetActive(false)
	self.node_list.ea_up_level_effect.transform.localPosition = self.ea_up_level_effect_pos
end

---------------------------------------HundredEquipAwardRender---------------------------------------
HundredEquipAwardRender = HundredEquipAwardRender or BaseClass(BaseRender)

function HundredEquipAwardRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind1(self.OnClickGoTo, self))
end

function HundredEquipAwardRender:OnFlush()
    if not self.data then
        return
    end

    local show_task_str
	if self.data.cfg.complete_max_times >= 9999 then
		show_task_str = string.format(Language.HundredEquip.WuXian, self.data.cfg.name)
	else
		show_task_str = string.format(Language.HundredEquip.CompleteTime2, self.data.cfg.name, self.data.complete_times, self.data.cfg.complete_max_times)
	end

	self.node_list.text_name.tmp.text = show_task_str
	self.node_list.exp_text.tmp.text = string.format(Language.HundredEquip.Exp, self.data.cfg.exp_per_times)

	self.node_list["image_over"]:SetActive(self.data.state == REWARD_STATE_TYPE.FINISH)
	self.node_list["btn_goto"]:SetActive(self.data.state == REWARD_STATE_TYPE.UNDONE)
end

function HundredEquipAwardRender:OnClickGoTo()
    if not self.data then return end

	if self.data.cfg.is_close == 1 then
		HundredEquipWGCtrl.Instance:CloseHundredView()
	end

	local main_role = Scene.Instance:GetMainRole()
    if main_role:CantPlayerDoMove(true) then
        return
    end

	if self.data.cfg.type == BIZUO_TYPE.RI_CHANG then --日常任务
		local is_finish = TaskWGData.Instance:DailyTaskIsFinsh()
		if is_finish then
			ViewManager.Instance:Open(GuideModuleName.TaskShangJinView)
			return
		end
		TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_RI, true)
	    TaskWGData.Instance:DoShangJinTask()
		return
	elseif self.data.cfg.type == BIZUO_TYPE.HU_SONG then --护送美人
		if NewTeamWGData.Instance:GetIsMatching() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.MatchingCanNotDo)
			return
		end
		if YunbiaoWGData.Instance:GetIsHuShong() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.IsHuSong)
        	return
		end
		TaskGuide.Instance:CanAutoAllTask(false)
		ActIvityHallWGCtrl.Instance:DoHuSong()
		return
	elseif self.data.cfg.type == BIZUO_TYPE.XIAN_MENG then -- 仙盟周任务
		if RoleWGData.Instance.role_vo.guild_id <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoGuild)
			return
		end
		local list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_MENG)
		local task_id = list and list[1]
		if task_id and task_id ~= 0 then
			MainuiWGCtrl.Instance:DoTask(task_id,TaskWGData.Instance:GetTaskStatus(task_id), true)
		end
		return
	elseif self.data.cfg.type == BIZUO_TYPE.GUILD_BUILD_TASK then
		if RoleWGData.Instance.role_vo.guild_id <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NoGuild)
			return
		end
	elseif self.data.cfg.type == BIZUO_TYPE.YEWAI_GUAJI then
		local scene_type = Scene.Instance:GetSceneType()
		local scene_id = Scene.Instance:GetSceneId()
		local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
		if scene_type ~= SceneType.Common or scene_id == bootybay_scene_id then
			TipWGCtrl.Instance:ShowSystemMsg(Language.HundredEquip.NotGiJiDesc)
			return
		end

		local offline_exp_info = BiZuoWGData.Instance:GetOffLineGuaJiInfo()

		if not offline_exp_info then
			return
		end

		MoveCache.SetEndType(MoveEndType.Auto)
		GuajiWGCtrl.Instance:MoveToPos(offline_exp_info.hang_monster_id_scene_id, offline_exp_info.hang_monster_id_pos_x,
			offline_exp_info.hang_monster_id_pos_y, offline_exp_info.pre_radius)
	elseif self.data.cfg.type == BIZUO_TYPE.SIT then
		GuajiWGCtrl.Instance:TryGoToSit()
 	end

	FunOpen.Instance:OpenViewNameByCfg(self.data.cfg.open_panel)
end