
require("game/merge_activity/duobei/merge_duobei_wg_data")

-- 合服活动-多倍有礼 
MergeDuoBeiWGCtrl = MergeDuoBeiWGCtrl or BaseClass(BaseWGCtrl)

function MergeDuoBeiWGCtrl:__init()
	if MergeDuoBeiWGCtrl.Instance then
		ErrorLog(" MergeDuoBeiWGCtrl] Attemp to create a singleton twice !")
	end
 	MergeDuoBeiWGCtrl.Instance = self

	self.data = MergeActDuoBeiWGData.New()
	self:RegisterAllProtocols()
end

function MergeDuoBeiWGCtrl:__delete()
 	MergeDuoBeiWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
end

function MergeDuoBeiWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCSADuoBeiInfo, "OnSCCSADuoBeiInfo")
	self:RegisterProtocol(CSCSADuoBei)
end

--多倍来袭返回
function MergeDuoBeiWGCtrl:OnSCCSADuoBeiInfo(protocol)
	self.data:SetDuoBeiInfo(protocol)
	MergeActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_DUOBEI)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2111, "RefreshDuoBei")
end

function MergeDuoBeiWGCtrl:SendDayDuoBeiReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSADuoBei)
	protocol:EncodeAndSend()
end
