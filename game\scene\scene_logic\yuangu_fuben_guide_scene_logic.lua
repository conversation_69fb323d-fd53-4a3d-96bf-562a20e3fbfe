YuanGuXianDianGuidSceneLogic = YuanGuXianDianGuidSceneLogic or BaseClass(CommonFbLogic)

function YuanGuXianDianGuidSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function YuanGuXianDianGuidSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function YuanGuXianDianGuidSceneLogic:Enter(old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.HIGH_TEAM_EQUIP)
	MainuiWGCtrl.Instance:SetTaskContents(false)
    MainuiWGCtrl.Instance:SetOtherContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(true, Language.FbName.YuanGuXianDian)
	MainuiWGCtrl.Instance:SetTeamBtnState(false)
	FuBenWGCtrl.Instance:SetYuanGuGuidMark(true)
	local time_stamp = TimeWGCtrl.Instance:GetServerTime() + 4
	UiInstanceMgr:DoFBStartDown(time_stamp,function ()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		local mainui_time_stamp_cfg = TeamEquipFbWGData.Instance:GetHighTeamEquipFbOther()
		local mainui_time_stamp = mainui_time_stamp_cfg.fb_time + TimeWGCtrl.Instance:GetServerTime()
		 MainuiWGCtrl.Instance:SetFbIconEndCountDown(mainui_time_stamp)
	end)


	-- self.story_view = StoryView.New(GuideModuleName.StoryView)
	-- self.story_view:SetOpenCallBack(function ()
	-- 	local step_list_cfg = ConfigManager.Instance:GetAutoConfig("story_auto").yuanguxiandian_story_fb
	-- 	if nil ~= step_list_cfg then
	-- 		RobertManager.Instance:Start()
	-- 		self.story = Story.New(step_list_cfg, self.story_view)
	-- 		self.story:SetTrigger(S_STEP_TRIGGER.ENTER_SCENE)
	-- 	end
	-- end)

	-- self.story_view:SetCloseCallBack(function ()
	-- 	if nil ~= self.story then
	-- 		self.story:DeleteMe()
	-- 		print_error("==========================")
	-- 		self.story = nil
	-- 	end
	-- end)

	-- self.story_view:Open()
end
function YuanGuXianDianGuidSceneLogic:Out(old_scene_type, new_scene_type)
	-- if nil ~= self.story then
	-- 	self.story:DeleteMe()
	-- 	self.story = nil
	-- end
	-- if nil ~= self.story_view then
	-- 	self.story_view:Close()
	-- 	self.story_view:DeleteMe()
	-- 	self.story_view = nil
	-- end
	RobertManager.Instance:Stop()
	CommonFbLogic.Out(self)
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:ResetTaskPanel()
	--UiInstanceMgr.Instance:ColseFBStartDown()
end

function YuanGuXianDianGuidSceneLogic:OpenFbSceneCd()

end

