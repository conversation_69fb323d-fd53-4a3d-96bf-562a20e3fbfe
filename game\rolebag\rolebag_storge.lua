RoleBagView = RoleBagView or BaseClass(SafeBaseView)

function RoleBagView:InitStorgeView()
	self.storge_item_data_list = {}
	self.storge_bag_item_data_list = {}

	self.storge_cell = {}
	self.storge_bag_cell = {}
	self.flush_Mode = nil

	XUI.AddClickEventListener(self.node_list["btn_ck_hebing"], BindTool.Bind1(self.OnClickCkHeBingBtnHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_ck_clearup"], BindTool.Bind1(self.OnCkStorgeCleanupHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_storge_clearup"], BindTool.Bind1(self.OnStorgeCleanupHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_storge_quicksell"], BindTool.Bind(self.OpenAutoSell, self))

	if not self.bag_storge_grid then
		self.bag_storge_grid = AsyncBaseGrid.New()
		self.bag_storge_grid:CreateCells({col = 4, cell_count = COMMON_CONSTS.MAX_BAG_COUNT, itemRender = BagCell ,list_view = self.node_list["BageListView1"]})
		self.bag_storge_grid:SetSelectCallBack(BindTool.Bind1(self.SelectStorgeCellCallBack, self))
	end

	if not self.bag_ck_storge_grid then
		self.bag_ck_storge_grid = AsyncBaseGrid.New()
		self.bag_ck_storge_grid:CreateCells({col = 7, cell_count = COMMON_CONSTS.MAX_SRORGE_COUNT, list_view = self.node_list["BageListView2"],itemRender = StorgeCell})
		self.bag_ck_storge_grid:SetSelectCallBack(BindTool.Bind1(self.SelectCKStorgeCellCallBack, self))
	end

	if not self.flush_storge then
		self.flush_storge = GlobalEventSystem:Bind(KnapsackEventType.KNAPSACK_EXTEND_STORAGE,BindTool.Bind(self.FlushStorgeView,self))
	end
end

function RoleBagView:DeleteStorgeView()
	if nil ~= self.bag_storge_grid then
		self.bag_storge_grid:DeleteMe()
		self.bag_storge_grid = nil
	end
	if nil ~= self.flush_storge then
		GlobalEventSystem:UnBind(self.flush_storge)
		self.flush_storge = nil
	end

	if nil ~= self.bag_ck_storge_grid then
		self.bag_ck_storge_grid:DeleteMe()
		self.bag_ck_storge_grid = nil
	end

	self.flush_Mode = nil

	if CountDownManager.Instance:HasCountDown('CKBagCleanup') then
		CountDownManager.Instance:RemoveCountDown('CKBagCleanup')
	end

	if CountDownManager.Instance:HasCountDown('BagBagCleanup') then
		CountDownManager.Instance:RemoveCountDown('BagBagCleanup')
	end

	if nil ~= self.storge_bag_cell then
		self.storge_bag_cell = {}
	end


	self.page_toggle_list = {}
	self.bag_page_toggle_list = {}

	self.storge_item_data_list = {}
	self.storge_bag_item_data_list = {}

	self.storge_list_view = nil
	self.storge_bag_list_view = nil

end

function RoleBagView:GetFirstBagEmptyBoxIndex()
	local bag_list = ItemWGData.Instance:GetBagItemDataList()
	for i = 0, COMMON_CONSTS.MAX_BAG_COUNT do
		if bag_list[i] == nil then
			return i
		end
	end
end

function RoleBagView:GetFirstStorgeEmptyBoxIndex()
	local storge_list = ItemWGData.Instance:GetStorgeItemDataList()
	local count = ItemWGData.Instance:GetCurOpenStorgeNum() - 1
	for i=0,count do
		if storge_list[i] == nil then
			return i
		end
	end
end

function RoleBagView:FlushStorgeView()
	if self.bag_storge_grid and self.bag_ck_storge_grid and self.bag_show_tab == TabIndex.rolebag_storge then
		self.bag_storge_grid:SetDataList(ItemWGData.Instance:GetBagItemDataList(),2)
		self.bag_storge_grid:CancleAllSelectCell()
		self.bag_ck_storge_grid:SetDataList(ItemWGData.Instance:GetStorgeItemDataList(),2)
		self.bag_ck_storge_grid:CancleAllSelectCell()

		self:FlushStorgeCapacity()
	end
end

function RoleBagView:FlushStorgeCapacity()
	if self.node_list.text_storge_capacity then
		self.node_list.text_storge_capacity.text.text = ItemWGData.Instance.hold_knapsack_num .. "/" .. ItemWGData.Instance.max_knapsack_valid_num
	end
end


-- 选择仓库格子回调
function RoleBagView:SelectStorgeCellCallBack(cell)

	if nil == cell then
		return
	end
	if nil == cell.data or not next(cell.data) then return end


	if self:TryOpenCell(cell) then
		return
	end
	if self.delay_bag_tip then
		local to_index = self:GetFirstStorgeEmptyBoxIndex()
		if to_index ~= nil then
			-- print_error('SendMoveItem------------',cell.data.index, COMMON_CONSTS.MAX_BAG_COUNT  + to_index)
			BagWGCtrl.Instance:SendMoveItem(cell.data.index, COMMON_CONSTS.MAX_BAG_COUNT  + to_index)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.StorgeFull)   -- 仓库已满
		end
		GlobalTimerQuest:CancelQuest(self.delay_bag_tip)
		self.delay_bag_tip = nil
		return
	end
	self.delay_bag_tip = GlobalTimerQuest:AddDelayTimer(function ()
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(cell.data.item_id)
		if item_cfg ~= nil then
			TipWGCtrl.Instance:OpenItem(cell.data, ItemTip.FROM_BAG_ON_BAG_STORGE,nil,nil, self.HandleItemTipCallBack)
		end
		GlobalTimerQuest:CancelQuest(self.delay_bag_tip)
		self.delay_bag_tip = nil
	end,0.2)
end

-- 选择仓库格子回调
function RoleBagView:SelectCKStorgeCellCallBack(cell)
	if nil == cell then
		return
	end
	if nil == cell.data or not next(cell.data) then return end


	if self:TryOpenCell(cell) then
		return
	end

	if self.delay_ckbag_tip then
		local to_index = self:GetFirstBagEmptyBoxIndex()
		if to_index ~= nil then
			BagWGCtrl.Instance:SendMoveItem(cell.data.index,to_index)
		end

		GlobalTimerQuest:CancelQuest(self.delay_ckbag_tip)
		self.delay_ckbag_tip = nil
		return
	end
	self.delay_ckbag_tip = GlobalTimerQuest:AddDelayTimer(function ()
		if cell and cell.data and cell.data.item_id and cell.data.item_id > 0 then
			TipWGCtrl.Instance:OpenItem(cell.data, ItemTip.FROM_STORGE_ON_BAG_STORGE,nil,nil, self.HandleItemTipCallBack)
		end
		GlobalTimerQuest:CancelQuest(self.delay_bag_tip)
		self.delay_ckbag_tip = nil
	end,0.2)
end



--操作物品tip回调
function RoleBagView:HandleItemTipCallBack(item_data, handleType, handle_param_t)
	if item_data == nil then return end
	-- local to_cell = nil
	local to_index = nil
	if handleType == ItemTip.HANDLE_STORGE then			--存仓库
		to_index = self:GetFirstStorgeEmptyBoxIndex()
		if to_index ~= nil then
			BagWGCtrl.Instance:SendMoveItem(item_data.index, COMMON_CONSTS.MAX_BAG_COUNT  + to_index)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.StorgeFull)   -- 仓库已满
		end
	elseif handleType == ItemTip.HANDLE_BACK_BAG then 	--从仓库中取回到背包
		to_index = self:GetFirstBagEmptyBoxIndex()
		if to_index ~= nil then
			BagWGCtrl.Instance:SendMoveItem(item_data.index,to_index)
		end
	end
end

--物品变化
function RoleBagView:OnStorgeItemDataChange()
	if self.bag_storge_grid then
		self.bag_storge_grid:CancleAllSelectCell()
		self.bag_storge_grid:SetDataList(ItemWGData.Instance:GetBagItemDataList(),2)

		self:FlushStorgeCapacity()
	end

	if self.bag_ck_storge_grid then
		self.bag_ck_storge_grid:CancleAllSelectCell()
		self.bag_ck_storge_grid:SetDataList(ItemWGData.Instance:GetStorgeItemDataList(),2)
	end
end

-- 玩家背包整理
function RoleBagView:OnStorgeCleanupHandler()
	self.flush_Mode = self.bag_storge_grid

	if self.CKBagCleanup_time then return end

	CountDownManager.Instance:AddCountDown('BagBagCleanup',function (time,total_time)
		time = math.modf(time)
		self.node_list.bag_cleanup_txt.text.text = total_time - time
	end,
	function ()
		self.node_list.bag_cleanup_txt.text.text = Language.Bag.CleanUp_2
		self.CKBagCleanup_time = false
		XUI.SetButtonEnabled(self.node_list.btn_storge_clearup,true)
		CountDownManager.Instance:RemoveCountDown('BagBagCleanup')
	end,
	nil,5,1
	)
	self.node_list.bag_cleanup_txt.text.text = 5
	XUI.SetButtonEnabled(self.node_list.btn_storge_clearup,false)
	self.CKBagCleanup_time = true

	BagWGCtrl.Instance:SendKnapsackStoragePutInOrder(GameEnum.STORAGER_TYPE_BAG, 0)   -- 请求整理
end

-- 仓库整理
function RoleBagView:OnCkStorgeCleanupHandler() --仓库整理
	self.flush_Mode = self.bag_ck_storge_grid

	if self.CKCleanup_time then return end

	CountDownManager.Instance:AddCountDown('CKBagCleanup',function (time,total_time)
		time = math.modf(time)
		self.node_list.ck_cleanup_txt.text.text = total_time - time
	end,
	function ()
		self.node_list.ck_cleanup_txt.text.text = Language.Bag.CleanUp_3
		self.CKCleanup_time = false
		XUI.SetButtonEnabled(self.node_list.btn_ck_clearup,true)
		CountDownManager.Instance:RemoveCountDown('CKBagCleanup')
	end,
	nil,5,1
	)
	self.node_list.ck_cleanup_txt.text.text = 5
	XUI.SetButtonEnabled(self.node_list.btn_ck_clearup,false)
	self.CKCleanup_time = true

	BagWGCtrl.Instance:SendKnapsackStoragePutInOrder(GameEnum.STORAGER_TYPE_STORAGER, 0)
end

function RoleBagView:OnClickCkHeBingBtnHandler() --合并
	self.pop_alert = self.pop_alert or Alert.New()
	self.pop_alert:SetLableString(Language.Role.MergeText)
	self.pop_alert:Open()
	self.pop_alert:SetOkFunc(function ()
		BagWGCtrl.Instance:SendKnapsackStoragePutInOrder(GameEnum.STORAGER_TYPE_STORAGER, 1)
	end)
end

------------------------------------------ StorgeCell ------------------------------------------

StorgeCell = StorgeCell or BaseClass(ItemCell)
function StorgeCell:__init()
	-- self.bg_img.image:LoadSprite(ResPath.GetF2CommonImages('suo_6'))
	-- local rect =  self.bg_img.rect

	-- rect.anchorMin = Vector2(0.5,0.5)
	-- rect.anchorMax = Vector2(0.5,0.5)
	-- rect.sizeDelta = Vector2(44,44)

	self:SetItemTipFrom(ItemTip.FROM_BAG)
	self:SetCanTrade(true)
	self.need_default_eff = false
end

function StorgeCell:OnClick()
	if self:IsIgnoreCKStorgeClick(self.index) and self.data then
		BaseRender.OnClick(self)
		return
	end
	local now_index = ItemWGData.Instance:GetCurOpenStorgeNum()
	local open_num = self.index - now_index
	TipWGCtrl.Instance:OpenAlertIconTips(open_num, 2)
end


function StorgeCell:OnFlush()
	ItemCell.OnFlush(self)
	if not self:IsIgnoreCKStorgeClick(self.index) and self.data and next(self.data) == nil then
		self:SetLockImgEnable(true)
	else
		self:SetLockImgEnable(false)
	end
	self:SetButtonComp(true)
end

function StorgeCell:SetSelectEffect(is_visible)
	-- if self.data and self.data.item_id then
	-- 	self.is_select_effect = is_visible
	-- 	self.select_effect:SetActive(is_visible)
	-- end
end

-- 设置是否选中
function StorgeCell:SetSelect(is_select, item_call_back)
	if self.select_call_back and not item_call_back then
		self.select_call_back(self.index, is_select)
	end

	if not self:CanSelect() then
		if self.is_select then
			return
		end
	end


	self.is_select = is_select
	if self.is_select then
		if self.data and next(self.data) ~= nil then
			-- self.select_effect:SetActive(true)
		end
	else
		self:TrySetActive("select_effect", nil, false)
	end

	self:OnSelectChange(self.is_select)
end

-- 是否忽略点击
function StorgeCell:IsIgnoreCKStorgeClick(index)
	local num = ItemWGData.Instance:GetCurOpenStorgeNum()
	return index <= num
end


------------------------------------------ BagCell ------------------------------------------

BagCell = BagCell or BaseClass(StorgeCell)
-- 是否忽略点击
function BagCell:IsIgnoreCKStorgeClick(index)
	local num = ItemWGData.Instance.max_knapsack_valid_num
	return index <= num
end

function BagCell:__init()
	self.need_default_eff = true
end

function BagCell:OnClick()
	if self:IsIgnoreCKStorgeClick(self.index) and self.data then
		BaseRender.OnClick(self)
		return
	end
	local now_index = ItemWGData.Instance.max_knapsack_valid_num
	local open_num = self.index - now_index
	TipWGCtrl.Instance:OpenAlertIconTips(open_num, 1)
end

------------------------------------------ RoleBagCell ------------------------------------------

RoleBagCell = RoleBagCell or BaseClass(StorgeCell)
-- 是否忽略点击
function RoleBagCell:IsIgnoreCKStorgeClick(index)
	local num = ItemWGData.Instance.max_knapsack_valid_num
	return index <= num
end

function RoleBagCell:__init()
	self.need_default_eff = true
end

function RoleBagCell:OnFlush()
	StorgeCell.OnFlush(self)
	--当有的礼包需要消耗仙玉时，显示的红点 一天只显示一次
	local item_id = self.data and self.data.item_id or 0
	local show_red = ItemWGData.Instance:GetItemGiftShowRed(item_id)
	self:SetCanOperateIconVisible(show_red)
end

function RoleBagCell:OnClick()
	--当有的礼包需要消耗仙玉时，显示的红点 一天只显示一次
	local item_id = self.data and self.data.item_id or 0
	local show_red = ItemWGData.Instance:GetItemGiftShowRed(item_id)
	if show_red then
		self:SetCanOperateIconVisible(false)
		ItemWGData.Instance:SetItemGiftShowRed(item_id,false)
		RemindManager.Instance:Fire(RemindName.BagBag)
	end

	if self:IsIgnoreCKStorgeClick(self.index) and self.data then
		BaseRender.OnClick(self)
		return
	end
	local now_index = ItemWGData.Instance.max_knapsack_valid_num
	local open_num = self.index - now_index
	TipWGCtrl.Instance:OpenAlertIconTips(open_num, 1)
end
