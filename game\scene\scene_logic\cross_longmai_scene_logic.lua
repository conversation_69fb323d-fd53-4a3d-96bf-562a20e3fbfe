CrossLongMaiSceneLogic = CrossLongMaiSceneLogic or BaseClass(CommonFbLogic)

function CrossLongMaiSceneLogic:__init()
	self.update_elaspe_time = 0
	self.role_creat_pos = {x = 0, y = 0}
end

function CrossLongMaiSceneLogic:__delete()
end

function CrossLongMaiSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	local main_role = Scene.Instance:GetMainRole()
	self.update_elaspe_time = 0
	self.lm_scene_enter_complete = true
	self.lm_check_auto_status = true
	self.role_creat_pos = {x = 0, y = 0}
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		self.role_creat_pos = {x = x, y = y}
	end

	FuhuoWGCtrl.Instance:SetFuhuoMustCallback(BindTool.Bind(self.FuHuoCallBack, self))

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(false)
		MainuiWGCtrl.Instance:SetShowTimeTextState(false)
		CrossLongMaiWGCtrl.Instance:OpenSceneView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	end)

	local view = CrossLongMaiWGCtrl.Instance:GetSceneView()
	ViewManager.Instance:AddMainUIRightTopChangeList(view)
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
end

function CrossLongMaiSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

	self.role_wait_time = nil
	self.lm_scene_enter_complete = false
	self.lm_check_auto_status = false

	FuhuoWGCtrl.Instance:ClearFuhuoMustCallback()
	CrossLongMaiWGData.Instance:CleanOwnBossInfo()
	local view = CrossLongMaiWGCtrl.Instance:GetSceneView()
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	CrossLongMaiWGCtrl.Instance:CloseSceneView()

	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	-- MainuiWGCtrl.Instance:SetTaskPanelAndTeamActive(true)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_LONGMAI)
    if not act_is_open then
		ViewManager.Instance:Open(GuideModuleName.LongMaiShop)
    end
end

function CrossLongMaiSceneLogic:FuHuoCallBack(use_type)
	self.lm_check_auto_status = false
	-- use_type = use_type or FuHuoType.Common
	-- if use_type == FuHuoType.Common then
	-- 	self:CommonMoveCallBack()
	-- else
	-- 	self:HereFuHuoCallBack()
	-- end
end

-- :GetGuajiPos()
-- :GetGuajiCharacter()
----[[
function CrossLongMaiSceneLogic:Update(now_time, elapse_time)
	if not self.lm_scene_enter_complete then
		return
	end

	BaseSceneLogic.Update(self, now_time, elapse_time)
	
	if self.update_elaspe_time + 0.5 > now_time then
		return
	end

	self.update_elaspe_time = now_time
	if self.role_wait_time == nil then
		self.role_wait_time = now_time
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role:IsDeleted() then
		return
	end

	-- 玩家进入不进入挂机状态，无动作30s后进入挂机
	if self.lm_check_auto_status and now_time - self.role_wait_time > 30 then
		local x, y = main_role:GetLogicPos()
		if self.role_creat_pos.x == x and self.role_creat_pos.y == y then
			-- print_error("----开始自动挂机----")
			if GuajiCache.guaji_type == GuajiType.None then
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end

			self.lm_check_auto_status = false

			local is_gather_state = main_role:GetIsGatherState()
			-- print_error("---------", end_type, is_gather_state)
			-- 自动打至尊BOSS
			if not is_gather_state and self:CheckCanFightBestBoss() then
				-- print_error("---自动打自尊BOSS------")
				return
			end

			-- 自动打宝箱BOSS
			if not is_gather_state and self:CheckCanFightBoxBoss() then
				-- print_error("---自动打宝箱BOSS------")
				return
			end

			-- 采集宝箱
			local can_gather = self:GetIsCanGather()
			if not is_gather_state and can_gather then
				local gather_obj = CrossLongMaiWGData.Instance:SelectRandGatherObj()
				if gather_obj then
					-- print_error("-----采集----")
					GuajiWGCtrl.Instance:OnSelectObj(gather_obj, SceneTargetSelectType.SCENE)
				end
			end
		else
			self.lm_check_auto_status = false
		end
	end

	--[[
	if GuajiCache.guaji_type == GuajiType.Temporary or GuajiCache.guaji_type == GuajiType.None then
		return
	end

	if GuajiCache.target_obj ~= nil then
		return
	end

	local end_type = MoveCache.GetEndType()
	if end_type ~= MoveEndType.Normal then
		return
	end

	
	--]]
end
--]]

function CrossLongMaiSceneLogic:StopGatherCallBack(stop_reason)
	-- print_error("---采集完成回调---", stop_reason, GuajiCache.guaji_type)
    if stop_reason == STOP_GATHER_REASON.ALREADY_GATHER and GuajiCache.guaji_type == GuajiType.None then
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end
end

function CrossLongMaiSceneLogic:GetIsCanGather()
    local max_num = CrossLongMaiWGData.Instance:GetMaxGatherNum()
    local uesd_num = CrossLongMaiWGData.Instance:GetGatherTimes()
    return uesd_num < max_num
end

-- 至尊BOSS
function CrossLongMaiSceneLogic:CheckCanFightBestBoss()
    local boss_list = CrossLongMaiWGData.Instance:GetBossInfoList()
    local boss_data = boss_list[1]
    if not boss_data then
        return false
    end

	if boss_data.is_live ~= 1 then
		return false
	end

    -- 前往BOSS
    local monster_id = boss_data.monster_id
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

    local sence_id = Scene.Instance:GetSceneId()
    local target_obj = Scene.Instance:GetMonstObjByMonstID(monster_id)
    MoveCache.SetEndType(MoveEndType.FightByMonsterId)
    GuajiCache.monster_id = monster_id
	local range = BossWGData.Instance:GetMonsterRangeByid(monster_id)
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    if target_obj then
        GuajiWGCtrl.Instance:MoveToObj(target_obj, range)
    else
        local point_cfg = CrossLongMaiWGData.Instance:GetBossRefreshPoint(monster_id)
        local pos_x = point_cfg and point_cfg.pos_x or 0
        local pos_y = point_cfg and point_cfg.pos_y or 0
        GuajiWGCtrl.Instance:MoveToPos(sence_id, pos_x, pos_y, range)
    end

	return true
end

-- 宝箱BOSS
function CrossLongMaiSceneLogic:CheckCanFightBoxBoss()
	local target_obj = self:GetLMSceneBoss()
	if target_obj ~= nil then
		GuajiWGCtrl.Instance:MoveToObj(target_obj, COMMON_CONSTS.GUAJI_MAX_RANGE)
		return true
	end

	return false
end

function CrossLongMaiSceneLogic:GetLMSceneBoss()
	local target_obj = nil
	local monster_list = Scene.Instance:GetMonsterList()
	if IsEmptyTable(monster_list) then
		return target_obj
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return target_obj
	end

	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local target_distance_reserved = distance_limit
	local target_obj_reserved = nil

	local target_x, target_y, distance = 0, 0, 0
	local role_x, role_y = main_role:GetLogicPos()

	-- local lm_data = CrossLongMaiWGData.Instance
	for k, v in pairs(monster_list) do
		-- local vo = v:GetVo()
		-- local monster_id = vo.monster_id
		-- if lm_data:GetBossRefreshPoint(monster_id) then
		-- 	target_obj = v
		-- 	break
		-- end

		target_x, target_y = v:GetLogicPos()
		distance = GameMath.GetDistance(role_x, role_y, target_x, target_y, false)
		local isBlock = AStarFindWay:IsBlock(target_x, target_y)
		-- 优先寻找非障碍区的
		if not isBlock then
			if distance < distance_limit then
				target_obj = v
				distance_limit = distance
			end
		else
			if distance < target_distance_reserved then
				target_obj_reserved = v
				target_distance_reserved = distance
			end
		end
	end

	if nil == target_obj then
		return target_obj_reserved
	end

	return target_obj
end

function CrossLongMaiSceneLogic:GetNormalRole()
	local target_obj = nil
	local role_list = Scene.Instance:GetRoleList()
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return target_obj
	end

	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local target_x, target_y, distance = 0, 0, 0
	local role_x, role_y = main_role:GetLogicPos()

	for k, v in pairs(role_list) do
		target_x, target_y = v:GetLogicPos()
		distance = GameMath.GetDistance(role_x, role_y, target_x, target_y, false)
		if distance < distance_limit then
			target_obj = v
			distance_limit = distance
		end
	end

	return target_obj
end

function CrossLongMaiSceneLogic:GetGuajiCharacter()
    local target_obj = self:GetLMSceneBoss()
	if target_obj ~= nil then
		return target_obj
	end

	if target_obj == nil then
		target_obj = self:GetNormalRole()
		return target_obj
	end
end

function CrossLongMaiSceneLogic:OpenFbSceneCd()
	local rest_time, end_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.KF_LONGMAI)
	if rest_time > 0 then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(end_time)
	end
end