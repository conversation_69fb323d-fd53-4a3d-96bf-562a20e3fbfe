SecretAreaTaskView = SecretAreaTaskView or BaseClass(SafeBaseView)

function SecretAreaTaskView:__init()
	self.view_cache_time = 1
	self:AddViewResource(0, "uis/view/country_map_ui/secret_area_prefab", "layout_secret_area_info")
	self.view_layer = UiLayer.MainUIHigh

	self.is_click_medicine = false
end

function SecretAreaTaskView:__delete()

end

function SecretAreaTaskView:ReleaseCallBack()
	self.is_click_medicine = false
	self.is_init = false
	self.old_exp_num = nil

	if CountDownManager.Instance:HasCountDown("start_finals_time") then
		CountDownManager.Instance:RemoveCountDown("start_finals_time")
	end
end

function SecretAreaTaskView:CloseCallBack()
	if self.node_list["btn_efficiency"] then
		self.node_list["btn_efficiency"].gameObject.transform:SetParent(self.root_node_transform, false)
	end
end

function SecretAreaTaskView:LoadCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
		self:InitCallBack()
	end)
end

function SecretAreaTaskView:ShowIndexCallBack(index)
	self:Flush()
	self:FlushTimeCount()
end

function SecretAreaTaskView:OnFlush(param_list, index)
	if not self.is_init then
		return
	end
	local task_info = SecretAreaWGData.Instance:GetSecretAreaTaskInfo()
	local secret_other_cfg = SecretAreaWGData.Instance:GetOtherCfg()

	if IsEmptyTable(task_info) then
		return
	end 
	self.node_list.mijing_lv.text.text = "Lv." .. task_info.scene_lv
	self.node_list.lbl_kill_num.text.text = task_info.kill_count
	self.node_list.lbl_monster_wave.text.text = task_info.wave .. "/" .. secret_other_cfg.wave
	self.node_list.lbl_team_count.text.text = task_info.role_count
	self:GetExp(task_info.fetch_exp)
	local other_info_cfg = SecretAreaWGData.Instance:GetOtherCfg()
	local str  = ""
  	if other_info_cfg.boss_id > 0 then
        local monster_data = BossWGData.Instance:GetMonsterInfo(other_info_cfg.boss_id)
        if monster_data then
            str = monster_data.name
        end
    end
    str = str .. Language.CountrySecret.BossState[task_info.boss_step]
    self.node_list.bossstate.text.text = str

	local add_exp = 0
	local exp_buff = FightWGData.Instance:GetExpBuff()
	if exp_buff then
		add_exp = exp_buff.param_list[3] / 100
	end
	if add_exp > 0 then
		self.node_list.jingYan_bg.gameObject:SetActive(true)
		self.node_list["btn_efficiency_icon"].animator:SetBool("shake",false)
		self.node_list["xiaolv_text"].text.text = string.format(Language.ExpAddition.VipAdd, add_exp)
		self.node_list.can_up_img:SetActive(false)
		self.is_click_medicine = true
	else
		self.node_list.jingYan_bg.gameObject:SetActive(false)
		self.node_list.can_up_img:SetActive(true)
		self.node_list["btn_efficiency_icon"].animator:SetBool("shake",true)
		if not self.is_click_medicine then 	--助战状态不弹经验药水使用
			local list = SecretAreaWGData.Instance:GetMedicineItemList()
			for i, v in ipairs(list) do
				local num = ItemWGData.Instance:GetItemNumInBagById(v)
				if num > 0 then
					self:OnClickOpenEfficiencyView()
					break
				end
			end
		end
		self.is_click_medicine = true
	end
end

function SecretAreaTaskView:GetExp(num)
	if nil == self.old_exp_num then
		self.old_exp_num = 0
	end
	self.save_exp_num = num
	self:PlayExpAni()
end


function SecretAreaTaskView:PlayExpAni()
	if self.is_doing_ani then
		return
	end
	if self.old_exp_num >= self.save_exp_num then return end

	self.is_doing_ani = true

	local next_exp_num = self.save_exp_num
	local text_obj = self.node_list.lbl_get_exp:GetComponent(typeof(TMPro.TextMeshProUGUI))
	local complete_fun = function()
		self.old_exp_num = next_exp_num
		self.is_doing_ani = false
		self:PlayExpAni()
	end

	local update_fun = function(num)
		local value, postfix_name = CommonDataManager.ConverExpFBNum(num)
		if postfix_name == "" then
			text_obj.text = (string.format("%.0f", value)) .. postfix_name
		else
			text_obj.text = (value) .. postfix_name
		end
	end
	UITween.DONumberTo(text_obj, self.old_exp_num, next_exp_num, 0.5, update_fun, complete_fun)
	self.node_list.lbl_get_exp.transform:DOPunchScale(Vector3(0.1, 0.1, 0.1), 0.5)

end

function SecretAreaTaskView:InitCallBack()
	XUI.AddClickEventListener(self.node_list.btn_efficiency, BindTool.Bind1(self.OnClickOpenEfficiencyView, self))

	MainuiWGCtrl.Instance:AddBtnToGuajiLine(self.node_list.btn_efficiency)
	self.is_init = true
end

function SecretAreaTaskView:OnClickOpenEfficiencyView()
	SecretAreaWGCtrl.Instance:OpenSecretAreaExpMdeicineView()
	self.is_click_medicine = true
end

function SecretAreaTaskView:FlushTimeCount()

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_COUNTRY_SECRET_AREA)
	local time = 0
	local info_time = 0
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if  activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
		info_time = activity_info.next_time or 0
	end
	time = info_time - server_time 
	if time > 0 then
		self:ShowTimePanel(true)
		if CountDownManager.Instance:HasCountDown("start_finals_time") then
			CountDownManager.Instance:RemoveCountDown("start_finals_time")
		end
		CountDownManager.Instance:AddCountDown("start_finals_time", BindTool.Bind(self.FinalUpdateTimeCallBack,self), nil, nil, time,1)
	else
		self:FinalCompleteTimeCallBack()
	end
end

function SecretAreaTaskView:FinalUpdateTimeCallBack(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.MSTime(time)
	self.node_list["time_text"].text.text = time_str
end

function SecretAreaTaskView:FinalCompleteTimeCallBack()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_COUNTRY_SECRET_AREA)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		self:ShowTimePanel(false)
		local time, next_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.KF_COUNTRY_SECRET_AREA)
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(next_time,true)
	end
end

function SecretAreaTaskView:ShowTimePanel(bool)
	if self.node_list["time_panel"] then
		self.node_list["time_panel"]:SetActive(bool)
	end
end


--经验药水
SecretAreaMedicineView = SecretAreaMedicineView or BaseClass(SafeBaseView)
function SecretAreaMedicineView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(782, 482)})
	self:AddViewResource(0, "uis/view/country_map_ui/secret_area_prefab", "layout_exp_medicine")
	self.view_layer = UiLayer.Normal
	self.item_data_event = BindTool.Bind(self.ItemDataChangeCallback, self)
	self:SetMaskBg(true)
	self.timer_type = "SecretAreaMedicineViewCountDown"
end

function SecretAreaMedicineView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.CountrySecret.Xiaolv 		
	self.medicine_list_view = AsyncListView.New(SecretAreaMedicineItemRender, self.node_list["medicine_list"])
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	self.effect_change = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.OnEffectChange, self))
end

function SecretAreaMedicineView:ItemDataChangeCallback()
	self:Flush()
end

function SecretAreaMedicineView:OnEffectChange()
	self:Flush()
end

function SecretAreaMedicineView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown(self.timer_type) then
		CountDownManager.Instance:RemoveCountDown(self.timer_type)
	end
	if self.effect_change then
		GlobalEventSystem:UnBind(self.effect_change)
		self.effect_change = nil
	end
	if self.medicine_list_view then
		self.medicine_list_view:DeleteMe()
		self.medicine_list_view = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
end

function SecretAreaMedicineView:CloseCallBack()

end

function SecretAreaMedicineView:OpenCallBack()

end

function SecretAreaMedicineView:ShowIndexCallBack(index)
	self:Flush()
end

function SecretAreaMedicineView:OnFlush(param_list, index)
	local data_list = {}
	local list = SecretAreaWGData.Instance:GetMedicineItemList()
	local seq_list = SecretAreaWGData.Instance:GetMedicineItemSeqList()
	for i, v in ipairs(list) do
		table.insert(data_list, {item_id = v, img_index = i, seq = seq_list[i]})
	end
	self.medicine_list_view:SetDataList(data_list)

	local exp_buff = FightWGData.Instance:GetExpBuff()
	if exp_buff then
		local cur_value = exp_buff.param_list[3] / 100
		local cur_time = TimeUtil.FormatSecondDHM2(math.ceil(exp_buff.param_list[1] / 1000))
		self.node_list.cur_exp_add_text.text.text = string.format(Language.ExpAddition.CurEffect, cur_value, cur_time)
		if CountDownManager.Instance:HasCountDown(self.timer_type) then
			CountDownManager.Instance:RemoveCountDown(self.timer_type)
		end
		local remain_time = math.ceil(exp_buff.param_list[1])
		remain_time = math.max(remain_time / 1000 - (Status.NowTime - exp_buff.recv_time), 0)
		self:UpdataNextTime(0,remain_time)
		CountDownManager.Instance:AddCountDown(self.timer_type, BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind(self.CompleteNextTime, self), nil, remain_time, 0)
	else
		self.node_list.cur_exp_add_text.text.text = string.format(Language.ExpAddition.CurNotEffect, 0, 0)
	end
end

function SecretAreaMedicineView:UpdataNextTime(elapse_time, total_time)
	local on_time = math.ceil(total_time - elapse_time)
	on_time = TimeUtil.FormatSecondDHM3(on_time)
	if self.node_list and self.node_list["cur_exp_add_text"] ~= nil then
		local exp_buff = FightWGData.Instance:GetExpBuff()
		if exp_buff then
			local cur_value = exp_buff.param_list[3] / 100
			self.node_list["cur_exp_add_text"].text.text = string.format(Language.ExpAddition.CurEffect, cur_value, on_time)
		end

	end
end

function SecretAreaMedicineView:CompleteNextTime()
	if self.node_list and self.node_list["cur_exp_add_text"] ~= nil then
		local exp_buff = FightWGData.Instance:GetExpBuff()
		if exp_buff then
			local cur_value = exp_buff.param_list[3] / 100
			self.node_list["cur_exp_add_text"].text.text = string.format(Language.ExpAddition.CurEffect, cur_value, 0)
		end
	end
	CountDownManager.Instance:RemoveCountDown(self.timer_type)
end


--SecretAreaMedicineItemRender
----------------------------------------------------------------------------
SecretAreaMedicineItemRender = SecretAreaMedicineItemRender or BaseClass(BaseRender)
function SecretAreaMedicineItemRender:__init()
	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind1(self.OnClickBuy, self))
	XUI.AddClickEventListener(self.node_list.btn_use, BindTool.Bind1(self.OnClickUse, self))
end
function SecretAreaMedicineItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function SecretAreaMedicineItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.ph_render_item)
end

function SecretAreaMedicineItemRender:OnClickBuy()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	if 0 == item_num then
		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)
		local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
		local gold_num = RoleWGData.Instance.role_info.gold
		--sq说去掉绑玉
		if bind_gold_num >= shop_cfg.price then
			ShopWGCtrl.Instance:SendShopBuy(self.data.item_id, 1, 0, 1, self.data.seq)

		elseif gold_num >= shop_cfg.price then
			local ok_func = function()
				ShopWGCtrl.Instance:SendShopBuy(self.data.item_id, 1, 0, 0, self.data.seq)
			end
			TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
		else
			VipWGCtrl.Instance:OpenTipNoGold()
		end
	end
end

function SecretAreaMedicineItemRender:OnClickUse()
	local enum = {
            [EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1] = 22011,
            [EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2] = 22012,
            [EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3] = 22013
        }

    local str 
	local main_role_effect_list = FightWGData.Instance:GetMainRoleEffectList()

	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	if item_num == 1 then
		local item_index = ItemWGData.Instance:GetItemIndex(self.data.item_id)
		if item_index >= 0 then
			BagWGCtrl.Instance:SendUseItem(item_index, 1)
		end
	elseif item_num == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.CountrySecret.NoExpItem)
	else
		for k, v in pairs(main_role_effect_list) do
			if enum[v.client_effect_type] and enum[v.client_effect_type] < self.data.item_id then
				str = Language.CountrySecret.YouXianShiYong
			end
		end
		OfflineRestWGCtrl.Instance:OpenUserOfflineView(self.data.item_id,1,1, str)
	end
end

function SecretAreaMedicineItemRender:OnFlush()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local role_vo  = GameVoManager.Instance:GetMainRoleVo()
	local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)

	local exp_buff = FightWGData.Instance:GetExpBuff()
	local cur_value = 0
	if exp_buff then
		cur_value = exp_buff.param_list[3] / 100
	end
	local add_num = Language.CountrySecret.ExpAddItemNum[self.data.img_index]

	local data = {}
	data.item_id = self.data.item_id
	data.num = 1
	self.item_cell:SetData(data)
	self.node_list.btn_use:SetActive(item_num > 0 or self.data.img_index <= 2)


	self.node_list.RedPoint:SetActive(item_num > 0 and cur_value < add_num)

	self.node_list.btn_buy:SetActive(item_num <= 0 and self.data.img_index > 2)
	self.node_list.cur_num_text:SetActive(item_num > 0 or self.data.img_index <= 2)
	self.node_list.gold_container:SetActive(item_num <= 0 and self.data.img_index > 2)
	XUI.SetGraphicGrey(self.node_list.btn_use, item_num <= 0)
	if item_num > 0 or self.data.img_index <= 2 then
		self.node_list.cur_num_text.text.text = string.format(Language.CountrySecret.ExpMedicineItemNumStr, item_num)
	end
	if shop_cfg and item_num <= 0 then
		local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
		local gold_num = RoleWGData.Instance.role_info.gold

		local color = (bind_gold_num >= shop_cfg.price or gold_num >= shop_cfg.price) and COLOR3B.GREEN or COLOR3B.RED
		local str = string.format(Language.CountrySecret.ExpMedicineItemGoldStr, shop_cfg.price)
		self.node_list.gold_text.text.text = ToColorStr(str, color)
	end
	
	self.node_list["item_desc"].text.text = string.format(Language.CountrySecret.ExpAddItemType[self.data.img_index],add_num.."%")
end