TianxianTaskView = TianxianTaskView or BaseClass(SafeBaseView)

function TianxianTaskView:__init()
    self:AddViewResource(0, "uis/view/tianxian_pavilion_ui_prefab", "layout_tianxian_task")
    self:SetMaskBg(false, true)
end

function TianxianTaskView:ReleaseCallBack()
    if self.task_list then
		self.task_list:DeleteMe()
		self.task_list = nil
	end
end

function TianxianTaskView:LoadCallBack()
    if not self.task_list then
        self.task_list = AsyncListView.New(TianxianTaskRender, self.node_list["task_list"])
    end
end

function TianxianTaskView:OnFlush()
    local task_cfg = TianxianPavilionWGData.Instance:GetTaskCfg()
    if not IsEmptyTable(task_cfg) then
        self.task_list:SetDataList(task_cfg)
    end
end





----------------------TianxianTaskRender----------------------
TianxianTaskRender = TianxianTaskRender or BaseClass(BaseRender)
function TianxianTaskRender:LoadCallBack()
    self.node_list["open_panel"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
end

function TianxianTaskRender:OnClickGet()
    if not self.data then
        return
    end

    if self.data.activity_id ~= "" then
        local is_open = ActivityWGData.Instance:GetActivityIsOpen(self.data.activity_id)

        if not is_open then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
            return
        end
    end

    if self.data.open_panel ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
    end
end

function TianxianTaskRender:OnFlush()
    if IsEmptyTable(self.data) then
		return
    end

    self.node_list.task_name.text.text = self.data.task_decs
end