﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_CameraClearFlagsWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(UnityEngine.CameraClearFlags));
		<PERSON><PERSON>("Skybox", get_Skybox, null);
		<PERSON><PERSON>("Color", get_Color, null);
		<PERSON><PERSON>("SolidColor", get_SolidColor, null);
		<PERSON><PERSON>("Depth", get_Depth, null);
		<PERSON><PERSON>("Nothing", get_Nothing, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON><PERSON>();
		TypeTraits<UnityEngine.CameraClearFlags>.Check = CheckType;
		StackTraits<UnityEngine.CameraClearFlags>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.CameraClearFlags arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.CameraClearFlags), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Skybox(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.CameraClearFlags.Skybox);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Color(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.CameraClearFlags.Color);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SolidColor(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.CameraClearFlags.SolidColor);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Depth(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.CameraClearFlags.Depth);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Nothing(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.CameraClearFlags.Nothing);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.CameraClearFlags o = (UnityEngine.CameraClearFlags)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

