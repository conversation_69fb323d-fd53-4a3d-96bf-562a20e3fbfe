DayNightManager = DayNightManager or BaseClass()
require("gameui/common/day_night_wg_data")
local VALID_TIME = 5		-- 5秒检测一次

function DayNightManager:__init()
	if nil ~= DayNightManager.Instance then
		print_error("[DayNightManager]:Attempt to create singleton twice!")
	end

	DayNightManager.Instance = self

	self.daynight_data = DayNightWGData.New()
	self.check_time = nil
	self.task_change_handle = GlobalEventSystem:Bind(OtherEventType.TASK_INFO_CHANGE, BindTool.Bind(self.OnCurrTaskChange, self))
	Runner.Instance:AddRunObj(self, 12)
end

function DayNightManager:__delete()
	self.daynight_data:DeleteMe()
	self.daynight_data = nil

	if self.task_change_handle then
		GlobalEventSystem:UnBind(self.task_change_handle)
		self.task_change_handle = nil
	end

	Runner.Instance:RemoveRunObj(self)
	DayNightManager.Instance = nil
end

-- 更新
function DayNightManager:Update(now_time, elapse_time)
	if self.check_time == nil then
		self.check_time = now_time + VALID_TIME
		return
	end

	if now_time > self.check_time then
		self.check_time = now_time + VALID_TIME
		self:CheckNeedChangeDayAndNight()
	end
end

-- 任务发生变动立刻检测一下
function DayNightManager:OnCurrTaskChange(task_id)
	self:CheckNeedChangeDayAndNight()
end

function DayNightManager:CheckNeedChangeDayAndNight()
	-- 检测是否为忽略的场景
	if DayNightWGData.Instance:CheckDayNightChangeForIgnoreScene() then
		return
	end

	-- 先检测任务
	local moment_index = nil
	moment_index = DayNightWGData.Instance:CheckDayNightChangeForTask()
	if moment_index ~= nil then
		Scene.Instance:ChangeSceneDayNightMoment(moment_index)
		return
	end

	-- 检测是否固定
	moment_index =DayNightWGData.Instance:CheckDayNightChangeForRegular()
	if moment_index ~= nil then
		Scene.Instance:ChangeSceneDayNightMoment(moment_index)
		return
	end

	-- 检测时间段
	moment_index =DayNightWGData.Instance:CheckDayNightChangeForTime()
	if moment_index ~= nil then
		Scene.Instance:ChangeSceneDayNightMoment(moment_index)
		return
	end
end

