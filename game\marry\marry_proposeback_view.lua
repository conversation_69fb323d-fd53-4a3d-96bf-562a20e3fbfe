MarryProposeBackView = MarryProposeBackView or BaseClass(SafeBaseView)

function MarryProposeBackView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false)
	self:LoadConfig()
	self.ring_item = nil
end

function MarryProposeBackView:__delete()

end

function MarryProposeBackView:ReleaseCallBack()
	if self.marry_item_slot then
		for k, v in pairs(self.marry_item_slot) do
			v:DeleteMe()
		end
		self.marry_item_slot = nil
	end

	if self.my_head_cell then
		self.my_head_cell:DeleteMe()
		self.my_head_cell = nil
	end

	if self.ta_head_cell then
		self.ta_head_cell:DeleteMe()
		self.ta_head_cell = nil
	end
end

function MarryProposeBackView:LoadConfig()
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_jieshou")
end

function MarryProposeBackView:LoadCallBack()
	self.node_list["btn_close"].button:AddClickListener(BindTool.Bind2(self.Close, self))
	self.node_list["btn_marry_refuse"].button:AddClickListener(BindTool.Bind2(self.Close, self))
	self.node_list["btn_marry_jieshou"].button:AddClickListener(BindTool.Bind2(self.AcceptHandler, self, 1))
end

function MarryProposeBackView:SetItemShow(item_data)
	if nil == self.marry_item_slot then
		self.marry_item_slot = {}
	end
	local item_num = #item_data
	local item_alearday_num = #self.marry_item_slot
	for i=0,item_num do
		if nil == self.marry_item_slot[i] then
			self.marry_item_slot[i] = ItemCell.New(self.node_list["ph_item"])
		end
		self.marry_item_slot[i]:SetData(item_data[i])
	end
	for i=0,item_alearday_num do
		if nil ~= self.marry_item_slot[i] then
			self.marry_item_slot[i]:SetActive(i <= item_num)
		end
	end
end

function MarryProposeBackView:OnFlush()
	self:SetHeadImg()
	local ring_cfg =  MarryWGData.Instance:GetOneMarryCfgByType(MarryWGData.ProposeInfo.marry_type) or {}
	local is_aleardy_tiqin = MarryWGData.ProposeInfo.lover_marry_type_flag[32 - MarryWGData.ProposeInfo.marry_type] or 0
	local reward_data_1 = {}

	if is_aleardy_tiqin == 1 then
		reward_data_1 = ring_cfg.after_reward_item
	else
		reward_data_1 = ring_cfg.reward_item
	end
	
	local marry_get_id = MarryWGData.ProposeInfo.marry_reward_limit_item_id
	local reward_data = MarryWGData.Instance:ExculeRewardInfo(reward_data_1, marry_get_id, false)
	
	if reward_data then
		self:SetItemShow(reward_data)
	end

	self.node_list["rich_tips"].text.text = string.format(Language.Marry.ReplyPropose, MarryWGData.ProposeInfo.GameName, MarryWGData.ProposeInfo.GameName)
end

function MarryProposeBackView:SetHeadImg()
	if not self.my_head_cell then
		self.my_head_cell = BaseHeadCell.New(self.node_list["my_head"])
	end
	--自己的头像数据
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true
	self.my_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.my_head_cell:SetData(data)
	self.my_head_cell:SetBgActive(false)
	--自己的名字
	local role_name = GameVoManager.Instance:GetMainRoleVo().name
	self.node_list["my_name_text"].text.text = role_name

	--对象的头像数据
	if not self.ta_head_cell then
		self.ta_head_cell = BaseHeadCell.New(self.node_list["ta_head"])
    end
	

    local role_vo = MarryWGData.ProposeInfo
	local data = {fashion_photoframe = role_vo.shizhuang_photoframe}
	data.role_id = role_vo.req_uid
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	self.ta_head_cell:SetImgBg(role_vo.shizhuang_photoframe > 0)
	self.ta_head_cell:SetData(data)
	self.ta_head_cell:SetBgActive(false)
	self.node_list["ta_name_text"].text.text = MarryWGData.ProposeInfo.GameName
end

function MarryProposeBackView:AcceptHandler(accept)
	MarryWGCtrl.Instance:SendMarryRet(accept)
	if accept == 1 then
		self.is_accept = true
		--ViewManager.Instance:Open(GuideModuleName.MarryFingerPrintView)
	end
	self:Close()
end

function MarryProposeBackView:CloseCallBack()
	if not self.is_accept then
		MarryWGCtrl.Instance:SendMarryRet(0)
	end
	self.is_accept = nil
end