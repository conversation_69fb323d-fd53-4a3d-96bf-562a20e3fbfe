require("game/operation_activity/task_chain/operation_task_chain_wg_data")
require("game/operation_activity/task_chain/operation_task_chain_reward_view")
require("game/operation_activity/task_chain/operation_task_chain_entrance_view")
require("game/operation_activity/task_chain/operation_task_chain_fb_info_view")
require("game/operation_activity/task_chain/operation_task_chain_accept_view")
require("game/operation_activity/task_chain/operation_task_chain_schedule_view")
require("game/operation_activity/task_chain/operation_task_chain_result_view")
require("game/operation_activity/task_chain/operation_task_chain_skill_view")
require("game/operation_activity/task_chain/operation_task_chain_think_view")
require("game/operation_activity/task_chain/operation_task_chain_fuhuo_view")
require("game/operation_activity/task_chain/operation_task_chain_new_view")

OperationTaskChainWGCtrl = OperationTaskChainWGCtrl or BaseClass(BaseWGCtrl)
function OperationTaskChainWGCtrl:__init()
	if OperationTaskChainWGCtrl.Instance then
		ErrorLog("[OperationTaskChainWGCtrl] Attemp to create a singleton twice !")
	end
	OperationTaskChainWGCtrl.Instance = self
	
	self.data = OperationTaskChainWGData.New()
	self.reward_view = OperationTaskChainRewardView.New(GuideModuleName.OperationTaskChainRewardView)
	self.entrance_view = OperationTaskChainEntranceView.New(GuideModuleName.OperationTaskChainEntranceView)
	self.fb_info = OperationTaskChainFbInfoView.New(GuideModuleName.OperationTaskChainFbInfoView)
	self.accept_view = OperationTaskChainAcceptView.New(GuideModuleName.OperationTaskChainAcceptView)
	self.schedule_view = OperationTaskChainScheduleView.New(GuideModuleName.OperationTaskChainScheduleView)
	self.result_view = OperationTaskChainResultView.New(GuideModuleName.OperationTaskChainResultView)
	self.skill_view = OperationTaskChainSkillView.New(GuideModuleName.OperationTaskChainSkillView)
	self.think_view = OperationTaskChainThinkView.New(GuideModuleName.OperationTaskChainThinkView)
	self.fuhuo_view = OperationTaskChainFuHuoView.New(GuideModuleName.OperationTaskChainFuHuoView)
	self.task_chain_view = OperationTaskChainView.New(GuideModuleName.OperationTaskView)

	self.show_husong_bubble_time = 0
	self.task_over_check_mark = false

	self:RegisterAllProtocols()

	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneChangeComplete, self))

	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/cross_taskchain_auto", BindTool.Bind(self.HotUpdateCommon, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/husong_fb_cfg_auto", BindTool.Bind(self.HotUpdateHuSong, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/caijixunluo_fb_cfg_auto", BindTool.Bind(self.HotUpdateCaiJiXunLuo, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/caiji_fb_cfg_auto", BindTool.Bind(self.HotUpdateCaiJi, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/shiliugonggeduobi_fb_cfg_auto", BindTool.Bind(self.HotUpdateAvoid, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/yunsongduobi_fb_cfg_auto", BindTool.Bind(self.HotUpdateYunSongAvoid, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/shouhu_fb_cfg_auto", BindTool.Bind(self.HotUpdateGround, self))
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/boss_fb_cfg_auto", BindTool.Bind(self.HotUpdateBoss, self))
end

function OperationTaskChainWGCtrl:__delete()
	OperationTaskChainWGCtrl.Instance = nil

	self:RemoveDelay()

	if self.data ~= nil then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.reward_view ~= nil then
		self.reward_view:DeleteMe()
		self.reward_view = nil
	end

	if self.entrance_view ~= nil then
		self.entrance_view:DeleteMe()
		self.entrance_view = nil
	end

	if self.fb_info ~= nil then
		self.fb_info:DeleteMe()
		self.fb_info = nil
	end

	if self.accept_view ~= nil then
		self.accept_view:DeleteMe()
		self.accept_view = nil
	end

	if self.schedule_view ~= nil then
		self.schedule_view:DeleteMe()
		self.schedule_view = nil
	end

	if self.result_view ~= nil then
		self.result_view:DeleteMe()
		self.result_view = nil
	end

	if self.skill_view ~= nil then
		self.skill_view:DeleteMe()
		self.skill_view = nil
	end

	if self.think_view ~= nil then
		self.think_view:DeleteMe()
		self.think_view = nil
	end

	if self.fuhuo_view ~= nil then
		self.fuhuo_view:DeleteMe()
		self.fuhuo_view = nil
	end

	-- if self.scene_change_complete ~= nil then
	-- 	GlobalEventSystem:UnBind(self.scene_change_complete)
	-- 	self.scene_change_complete = nil
	-- end
end

function OperationTaskChainWGCtrl:RegisterAllProtocols()
	-- 请求任务链活动用户信息返回
	self:RegisterProtocol(CrossActivityTCActivityUserInfoRet, "OnCrossActivityTCActivityUserInfoRet")
	-- 请求任务链用户活动信息
	self:RegisterProtocol(CrossActivityTCActivityUserInfoReq)
	-- 任务链名望奖励领取
	self:RegisterProtocol(CrossActivityTCActivityMWRewardFetch)
	-- 任务链场景内信息
	self:RegisterProtocol(SCCrossActivityTCActivityInfo, "OnSCCrossActivityTCActivityInfo")
	-- 玩法（副本）结果
	self:RegisterProtocol(SCCrossActivityTCActivityGamePlayResult, "OnSCCrossActivityTCActivityGamePlayResult")
	-- 跟随状态设置
	self:RegisterProtocol(CSCrossActivityTCActivityFollowStatusSet)
    -- 任务链进入副本
	self:RegisterProtocol(CSCrossActivityTCActivityGoToTaskScene)
	-- 任务链退出任务场景
	self:RegisterProtocol(CSCrossActivityTCActivityExitTaskScene)
	-- 任务链结束信息
	self:RegisterProtocol(SCCrossActivityTCActivityOverInfo, "OnSCCrossActivityTCActivityOverInfo")

	-- 任务链护送玩法信息
	self:RegisterProtocol(SCCrossActivityTCActivityHuSongInfo, "OnSCCrossActivityTCActivityHuSongInfo")

	-- 任务链巡逻采集玩法信息
	self:RegisterProtocol(SCCrossActivityTCActivityXunLuoCaiJiInfo, "OnSCCrossActivityTCActivityXunLuoCaiJiInfo")
	-- 任务链巡逻采集玩法提交道具
	self:RegisterProtocol(CSCrossActivityTCActivityXunLuoCaiJiCommit)
	-- 任务链巡逻采集玩法怪物状态
	self:RegisterProtocol(SCCrossActivityTCActivityXunLuoCaiJiMonsterStatus, "OnSCCrossActivityTCActivityXunLuoCaiJiMonsterStatus")

	-- 任务链采集玩法 提交道具
	self:RegisterProtocol(CSCrossActivityTCActivityCaiJiCommit)
	-- 任务链采集玩法信息
	self:RegisterProtocol(SCCrossActivityTCActivityCaiJiInfo, "OnSCCrossActivityTCActivityCaiJiInfo")
	-- 任务链采集玩法 特殊采集物状态道具
	self:RegisterProtocol(SCCrossActivityTCActivityCaiJiSpecialGather, "OnSCCrossActivityTCActivityCaiJiSpecialGather")

	-- 任务链16宫格躲避场景信息
	self:RegisterProtocol(SCCrossActivityTCActivity16GGDBInfo, "OnSCCrossActivityTCActivity16GGDBInfo")
	-- 任务链16宫格躲避轮次信息
	self:RegisterProtocol(SCCrossActivityTCActivity16GGDBRoundInfo, "OnSCCrossActivityTCActivity16GGDBRoundInfo")
	-- 任务链16宫格机关信息
	self:RegisterProtocol(SCCrossActivityTCActivity16GGDBJiGuanInfo, "OnSCCrossActivityTCActivity16GGDBJiGuanInfo")
	-- 任务链16宫格机关当前轮次结果
	self:RegisterProtocol(SCCrossActivityTCActivity16GGDBRoundResult, "OnSCCrossActivityTCActivity16GGDBRoundResult")

	-- 任务链运送躲避场景信息
	self:RegisterProtocol(SCCrossActivityTCActivityYSDBInfo, "OnSCCrossActivityTCActivityYSDBInfo")
	-- 任务链运送躲避机关信息
	self:RegisterProtocol(SCCrossActivityTCActivityYSDBJiGuanInfo, "OnSCCrossActivityTCActivityYSDBJiGuanInfo")
	-- 任务链运送躲避采集状态信息
	self:RegisterProtocol(SCCrossActivityTCActivityYSDBGatherStatus, "OnSCCrossActivityTCActivityYSDBGatherStatus")

	-- 任务链守护场景信息
	self:RegisterProtocol(SCCrossActivityTCActivityShouHuInfo, "OnSCCrossActivityTCActivityShouHuInfo")
	-- 任务链守护怪物波数信息
	self:RegisterProtocol(SCCrossActivityTCActivityShouHuWaveInfo, "OnSCCrossActivityTCActivityShouHuWaveInfo")

	-- 任务链BOSS场景信息
	self:RegisterProtocol(SCCrossActivityTCActivityBossInfo, "OnSCCrossActivityTCActivityBossInfo")
end

function OperationTaskChainWGCtrl:OnCrossActivityTCActivityUserInfoRet(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		return
	end

	self.data:SetTaskChainInfo(protocol)

	local old_info = self.data:GetTaskChainInfo()
	if old_info == nil or old_info.have_done ~= protocol.have_done then
		local act_info = self.data:GetTaskChainActStatusInfo()
		if act_info ~= nil then
			local status = act_info.status
			if protocol.have_done == 1 then
				status = ACTIVITY_STATUS.CLOSE
			end
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHOW_TASK_CHAIN, status, act_info.next_time, 0, 0, 0)
		end	
	end

	if self.task_chain_view:IsOpen() then
		self.task_chain_view:Flush()
	end

	if self.reward_view ~= nil then
		self.reward_view:Flush()
	end

	if self.entrance_view ~= nil then
		self.entrance_view:Flush()
	end
	
	self:ChangeAcceptNpc()

	RemindManager.Instance:Fire(RemindName.OperationTaskChain)
end

function OperationTaskChainWGCtrl:ChangeAcceptNpc()
	if OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN) then
		local day_index_cfg = self.data:GetCurDayIndexCfg()
		if day_index_cfg ~= nil then
			local task_chain_cfg = self.data:GetTaskChainCfg(day_index_cfg.task_chain_id)
	    	if task_chain_cfg ~= nil then
	    		local obj = Scene.Instance:GetNpcByNpcId(task_chain_cfg.npc_id)
	    		if obj ~= nil and not obj:IsDeleted() then
	    			obj:CheckModeAndName(task_chain_cfg.res_id, task_chain_cfg.npc_name)
	    		end
	    	end
		end
	end
end

-- 活动结束，重置入口NPC模型和名字
function OperationTaskChainWGCtrl:CloseAcceptNpc()
	if OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN) then
		local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
		if day_index_cfg ~= nil then
			local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
	    	if task_chain_cfg ~= nil then
	    		local obj = Scene.Instance:GetNpcByNpcId(task_chain_cfg.npc_id)
	    		if obj ~= nil and not obj:IsDeleted() then
	    			obj:CheckModeAndName(nil, nil, true)
	    		end
	    	end
		end
	end
end

function OperationTaskChainWGCtrl:SendGetTaskChainInfo(taskchain_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CrossActivityTCActivityUserInfoReq)
	protocol.taskchain_type = taskchain_type or TASK_CHAIN_ACTIVITY_TYPE.OPERATION
 	protocol:EncodeAndSend()
end

function OperationTaskChainWGCtrl:SendTaskChainMWRewardFetch(index, taskchain_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CrossActivityTCActivityMWRewardFetch)
	protocol.taskchain_type = taskchain_type or TASK_CHAIN_ACTIVITY_TYPE.OPERATION
 	protocol.index = index
 	protocol:EncodeAndSend()
end

function OperationTaskChainWGCtrl:CheckOpenAcceptView()
	local info = self.data:GetTaskChainActInfo()
	if info == nil then
		return
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic == nil then
		return
	end

	if info.task_idx ~= -1 and scene_logic:GetIsNeedCheck() then
		scene_logic:SetIsNeedCheck(false)
		self.accept_view:Open()
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityInfo(protocol)
		return
	end

	local is_show_accept = false

	-- 特殊情况，服务端因为新需求会下发多次这个协议，会影响客户端逻辑，先特殊处理
	if protocol.reason == 1 then
		ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "exp_change", {add_exp = protocol.add_exp})
		return
	end

	local old_info = self.data:GetTaskChainActInfo()
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil and Scene.Instance:GetSceneType() == SceneType.CROSS_TASK_CHAIN then
		local is_need_check = scene_logic:GetIsNeedCheck()
		-- scene_logic:SetIsNeedCheck(false)

		if old_info ~= nil then
			if old_info.task_idx == -1 and protocol.task_idx ~= -1 then
				is_show_accept = true
			end
		end
		
		if is_need_check then
			scene_logic:SetIsNeedCheck(false)

			if not is_show_accept and protocol.task_idx ~= -1 then
				is_show_accept = true
			end
		end

		local operation_task_chain_flag = self.data:GetTaskChainIsOpen()
		local block_data = nil
		local eff_data = nil
		local is_show_block = protocol.task_idx == -1
		local scene_id = Scene.Instance:GetSceneId()
		if operation_task_chain_flag and is_show_block then
			block_data, eff_data = self.data:GetBlockPosList(scene_id)
		end

		scene_logic:CheckMapBlockIsCreate(scene_id, is_show_block, block_data, eff_data)
	end

	self.data:SetTaskChainActInfo(protocol)

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end

	if self.accept_view:IsOpen() then
		self.accept_view:Flush()
	elseif is_show_accept then
		self.accept_view:Open()
	end

	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityGamePlayResult(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityGamePlayResult(protocol)
		return
	end

	self.data:SetTaskChainFbResult(protocol)
	
	if self.result_view:IsOpen() then
		self.result_view:Flush()
	elseif self.data:IsTaskChainScene() then
		self.result_view:Open()
	end
	
	RemindManager.Instance:Fire(RemindName.OperationTaskChain)
end

function OperationTaskChainWGCtrl:SendTaskChainFollowStatus(follow_status, dis, taskchain_type)
	if follow_status == nil or dis == nil then
		return
	end

	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossActivityTCActivityFollowStatusSet)
	protocol.taskchain_type = taskchain_type or TASK_CHAIN_ACTIVITY_TYPE.OPERATION
 	protocol.follow_status = follow_status
 	protocol.follow_dist = dis
 	protocol:EncodeAndSend()
end

function OperationTaskChainWGCtrl:SetTaskChainActStatus(protocol)
	self.data:SetTaskChainActStatus(protocol)
	if protocol.status ~= ACTIVITY_STATUS.CLOSE then
		OperationTaskChainWGCtrl.Instance:SendGetTaskChainInfo()
	end

	local status = protocol.status
	if not self.data:GetTaskChainIsOpen() then
		status = ACTIVITY_STATUS.CLOSE
	end

	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.SHOW_TASK_CHAIN, status, protocol.next_status_switch_time, 0, 0, 0)

	if self.entrance_view:IsOpen() then
		self.entrance_view:Flush()
	end

	if self.task_chain_view:IsOpen() then
		self.task_chain_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.OperationTaskChain)
end

function OperationTaskChainWGCtrl:SendTaskChainGoToTaskScene(task_idx, taskchain_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossActivityTCActivityGoToTaskScene)
	protocol.taskchain_type = taskchain_type or TASK_CHAIN_ACTIVITY_TYPE.OPERATION
 	protocol.task_idx = task_idx
 	protocol:EncodeAndSend()	
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityHuSongInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityHuSongInfo(protocol)
		return
	end

	local info = self.data:GetHuSongInfo()
	if info == nil or (info ~= nil and info.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY and protocol.task_status ~= OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY) then
		local bundle_str = self.data:GetRandomBubble(protocol.task_status)
		if bundle_str ~= nil then
			self:CheckHuSongBubble(protocol.task_status, protocol.objid, bundle_str)
		end
	end

	local level_change = false
	if info ~= nil and info.level ~= protocol.level then
		level_change = true
	end

	self.data:SetHuSongInfo(protocol)

	local obj = Scene.Instance:GetObj(protocol.objid)
	if obj ~= nil and not obj:IsDeleted() then
		obj:SetIsCheckIdleAni(true)
		obj:SetHpVisiable(false)
		if protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.SUCC then
			obj:PlayMainAni(SceneObjAnimator.Moster_Rest_Show)
		elseif protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.FIGHT then
			local name = obj:CurIdleAni()
			if obj:IsStand() then
				obj:PlayMainAni(name)
			end
		end
	end

	if Scene.Instance:GetSceneType() == SceneType.CROSS_TASK_CHAIN_HU_SONG then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			local operation_task_chain_flag = self.data:GetTaskChainIsOpen()
			local block_data = nil
			local eff_data = nil
			local is_show_block = protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY
			local scene_id = Scene.Instance:GetSceneId()
			if operation_task_chain_flag and is_show_block then
				block_data, eff_data = self.data:GetBlockPosList(scene_id)
			end

			scene_logic:CheckMapBlockIsCreate(scene_id, is_show_block, block_data, eff_data)
		end
	end

	if self.fb_info:IsOpen() then
		self.fb_info:Flush(0, "all", {level_change = level_change})
	end

	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:RemoveDelay()
	if self.delay_time then
		GlobalTimerQuest:CancelQuest(self.delay_time)
		self.delay_time = nil
	end
end

function OperationTaskChainWGCtrl:CheckHuSongBubble(task_status, obj_id, str)
	self:RemoveDelay()
	if task_status == nil or obj_id == nil or str == nil then
		return
	end

	if task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY or task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.SUCC then
		return
	end

	local obj = Scene.Instance:GetObj(obj_id)
	if obj == nil or obj:IsDeleted() then
		return
	end

	local follow_ui = obj:GetFollowUi()
	if follow_ui == nil then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.CROSS_TASK_CHAIN_HUSONG then
		return
	end

	local function callback()
		local info = self.data:GetHuSongInfo()
		if info == nil then
			return
		end

		local bundle_str = self.data:GetRandomBubble()
		if bundle_str == nil then
			return
		end

		self.delay_time = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CheckHuSongBubble, self, info.task_status, info.objid, bundle_str), 2)
		-- self:CheckHuSongBubble(info.task_status, info.objid, bundle_str)
	end

	follow_ui:ForceSetVisible(true)
	follow_ui:ChangeBubble(str, 4, callback)
	follow_ui:ShowBubble()
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityXunLuoCaiJiInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityXunLuoCaiJiInfo(protocol)
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	local old_num = self.data:GetOldCanSubmission()
	local old_commit = self.data:GetOldXunLuoCaiJiSelfCommit()
	local already_submission = false

	if (old_num == nil and protocol.num > 0) or (old_num ~= nil and protocol.num - old_num > 0) then
		local add_num = old_num == nil and protocol.num or protocol.num - old_num
		ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "show_tip", {res = "img_tip_7", show_type = OPERATION_TASK_CHAIN_TIP_TYPE.CHANGE_IMG, str = add_num})
	else
		if old_num ~= nil and protocol.num < old_num and (old_commit == nil or old_commit < protocol.commit_self) then
			local add_num = old_num - protocol.num
			ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "show_tip", {res = "img_tip_3", show_type = OPERATION_TASK_CHAIN_TIP_TYPE.CHANGE_IMG, str = add_num})
		end
	end

	if main_role ~= nil then
		local follow_ui = main_role:GetFollowUi()
		if follow_ui ~= nil then
			if old_num ~= nil and old_num > protocol.num and (old_commit == nil or old_commit < protocol.commit_self) then
	            local cfg = self.data:GetCurTaskCfg()
	            local name = ""
	            if cfg ~= nil then
	                name = cfg.progress_desc
	            end

				-- SysMsgWGCtrl.Instance:ErrorRemind(name .. "+" .. (old_num - protocol.num))
				already_submission = true
				local bundle,asset = ResPath.GetOperationTaskChainF2("taskicon_6")
				ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainFbInfoView, nil, "move", {node = follow_ui:GetFaceInonObj(), bundle = bundle, asset = asset})
			end

			if protocol.num <= 0 then
				main_role:SetEmbraceStatus(false)
			elseif protocol.num > 0 then
				local cfg = self.data:GetCurTaskCfg()
				if cfg ~= nil then
					local dungeon_cfg = self.data:GetXunLuoCaiJiDungeonCfg(cfg.dungeon_id)
					if dungeon_cfg ~= nil then
						main_role:SetEmbraceStatus(true, dungeon_cfg.bundle, dungeon_cfg.asset)
					end
				end
			end
			local bundle,asset = ResPath.GetOperationTaskChainF2("taskicon_6")
			follow_ui:SetFaceIcon(protocol.num > 0, bundle, asset, Language.OpertionAcitvity.TaskChain.NeedSubmissionStr, protocol.num, nil, true)
		end
	end

	local info = self.data:GetXunLuoCaiJiInfo()
	local level_change = false
	if info ~= nil and info.level ~= protocol.level then
		level_change = true
	end

	self.data:SetXunLuoCaiJiInfo(protocol)
	if Scene.Instance:GetSceneType() == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
		local npc_id = self.data:GetXunLuoCaiJiOperaInfo()
		local scene_logic = Scene.Instance:GetSceneLogic()

		if nil ~= self.caiji_task_states and self.caiji_task_states == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY and protocol.task_status ~= OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		elseif nil == self.caiji_task_states and protocol.task_status ~= OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
		self.caiji_task_states = protocol.task_status

		if npc_id ~= nil then
			local obj = Scene.Instance:GetNpcByNpcId(npc_id)
			if obj ~= nil and not obj:IsDeleted() then
	            local cfg = self.data:GetCurTaskCfg()
	            local name = ""
	            if cfg ~= nil then
	                name = cfg.progress_desc
	            end

				local data = {
					show_type = FOLLOW_UI_UNDER_BAR_TYPE.SLIDER,
					param = protocol.progress * 0.01,
					timer_str = string.format(Language.OpertionAcitvity.TaskChain.SubmissionNpcShow, name, protocol.progress),
					call_back = nil,
				}

	            obj:SetUnderShowInfo(data, true)
				-- obj:CheckFlushUnderBar(FOLLOW_UI_UNDER_BAR_TYPE.SLIDER, protocol.progress * 0.01, string.format(Language.OpertionAcitvity.TaskChain.SubmissionNpcShow, name, protocol.progress))
			end

			if scene_logic ~= nil and GuajiCache.guaji_type == GuajiType.None and already_submission then
				local is_need = scene_logic:GetIsNeedGuaJi()
				if is_need then
					GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				end
			end
		end

		if scene_logic ~= nil then
			local operation_task_chain_flag = self.data:GetTaskChainIsOpen()
			local block_data = nil
			local eff_data = nil
			local is_show_block = protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY
			local scene_id = Scene.Instance:GetSceneId()
			if operation_task_chain_flag and is_show_block then
				block_data, eff_data = self.data:GetBlockPosList(scene_id)
			end

			scene_logic:CheckMapBlockIsCreate(scene_id, is_show_block, block_data, eff_data)
			scene_logic:SetIsNeedCheckCommit(true)
		end
	end

	if self.skill_view ~= nil then
		self.skill_view:Flush()
	end

	if self.fb_info:IsOpen() then
		self.fb_info:Flush(0, "all", {level_change = level_change})
	end

	if self.schedule_view ~= nil then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:SendTaskChainHuSongCaiJiCommit(taskchain_type)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:SetIsNeedCheckCommit(false)
		end
	end

	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossActivityTCActivityXunLuoCaiJiCommit)
	protocol.taskchain_type = taskchain_type or TASK_CHAIN_ACTIVITY_TYPE.OPERATION
 	protocol:EncodeAndSend()
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityXunLuoCaiJiMonsterStatus(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityXunLuoCaiJiMonsterStatus(protocol)
		return
	end

	for k,v in pairs(protocol.info) do
		local obj = Scene.Instance:GetObj(v.obj_id)
		if obj ~= nil and not obj:IsDeleted() and obj:IsMonster() then
			obj:SetXunLuoStutes(v.status)
		end
	end
end

function OperationTaskChainWGCtrl:SendTaskChainCaiJiCommit(taskchain_type)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:SetIsNeedCheckCommit(false)
		end
	end

	--[[ 这个扔雪球的动作没有了
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		local npc_id, gather_radius = OperationTaskChainWGData.Instance:GetCaiJiOperaInfo()
		if npc_id ~= nil then
			local target_obj = Scene.Instance:GetNpcByNpcId(npc_id)
			if target_obj ~= nil and not target_obj:IsDeleted() then
				local l_x, l_y = target_obj:GetLogicPos()
				main_role:DoAttack(SkillWGData.Skill_Id_11304, l_x, l_y, target_obj:GetObjId(), target_obj:GetType(), nil)
			end
		end
	end
	--]]

	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossActivityTCActivityCaiJiCommit)
	protocol.taskchain_type = taskchain_type or TASK_CHAIN_ACTIVITY_TYPE.OPERATION
 	protocol:EncodeAndSend()
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityCaiJiInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityCaiJiInfo(protocol)
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	local old_num = self.data:GetOldCanSubmission()
	local already_submission = false

	if (old_num == nil and protocol.num > 0) or (old_num ~= nil and protocol.num - old_num > 0) then
		local add_num = old_num == nil and protocol.num or protocol.num - old_num
		ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "show_tip", {res = "img_tip_6", show_type = OPERATION_TASK_CHAIN_TIP_TYPE.CHANGE_IMG, str = add_num})
	else
		if old_num ~= nil and protocol.num < old_num then
			local add_num = old_num - protocol.num
			ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "show_tip", {res = "img_tip_4", show_type = OPERATION_TASK_CHAIN_TIP_TYPE.FIXED})
		end
	end

	local is_flush_info = true
	local info = self.data:GetCaiJiInfo()
	local level_change = false
	if info ~= nil and info.level ~= protocol.level then
		level_change = true
	end

	if main_role ~= nil then
		local follow_ui = main_role:GetFollowUi()
		if follow_ui ~= nil then
			local bundle,asset = ResPath.GetOperationTaskChainF2("taskicon_3")
			if old_num ~= nil and old_num > protocol.num then
	            local cfg = self.data:GetCurTaskCfg()
	            local name = ""
	            if cfg ~= nil then
	                name = cfg.progress_desc
	            end

				-- SysMsgWGCtrl.Instance:ErrorRemind(name .. "+" .. (old_num - protocol.num))
				already_submission = true
				is_flush_info = false
				ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainFbInfoView, nil, "move", {node = follow_ui:GetFaceInonObj(), bundle = bundle, asset = asset, level_change = level_change})
			end

			follow_ui:SetFaceIcon(protocol.num > 0, bundle, asset, Language.OpertionAcitvity.TaskChain.NeedSubmissionStr, protocol.num, nil, true)
		end

		if protocol.num <= 0 then
			main_role:SetEmbraceStatus(false)
			main_role:FlushStande()
		elseif protocol.num > 0 then
			local cfg = self.data:GetCurTaskCfg()
			if cfg ~= nil then
				local dungeon_cfg = self.data:GetCaiJiDungeonCfg(cfg.dungeon_id, Scene.Instance:GetSceneId())
				if dungeon_cfg ~= nil then
					main_role:SetEmbraceStatus(true, dungeon_cfg.bundle, dungeon_cfg.asset)
				end
			end
		end
	end

	self.data:SetCaiJiInfo(protocol)

	if Scene.Instance:GetSceneType() == SceneType.CROSS_TASK_CHAIN_CAI_JI then
		if nil ~= self.caiji_task_states and self.caiji_task_states == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY and protocol.task_status ~= OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		elseif nil == self.caiji_task_states and protocol.task_status ~= OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end

		self.caiji_task_states = protocol.task_status
		local npc_id = self.data:GetCaiJiOperaInfo()
		if npc_id ~= nil then
			local obj = Scene.Instance:GetNpcByNpcId(npc_id)
			if obj ~= nil and not obj:IsDeleted() then
	            local cfg = self.data:GetCurTaskCfg()
	            local name = ""
	            if cfg ~= nil then
	                name = cfg.progress_desc
	            end

				local data = {
					show_type = FOLLOW_UI_UNDER_BAR_TYPE.SLIDER,
					param = protocol.progress * 0.01,
					timer_str = string.format(Language.OpertionAcitvity.TaskChain.SubmissionNpcShow, name, protocol.progress),
					call_back = nil,
				}

				obj:SetUnderShowInfo(data)
				-- obj:CheckFlushUnderBar(FOLLOW_UI_UNDER_BAR_TYPE.SLIDER, protocol.progress * 0.01, string.format(Language.OpertionAcitvity.TaskChain.SubmissionNpcShow, name, protocol.progress))
			end
		end

		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil and GuajiCache.guaji_type == GuajiType.None and already_submission then
			local is_need = scene_logic:GetIsNeedGuaJi()
			if is_need then
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		end

		if scene_logic ~= nil then
			local operation_task_chain_flag = self.data:GetTaskChainIsOpen()
			local block_data = nil
			local eff_data = nil
			local is_show_block = protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY
			local scene_id = Scene.Instance:GetSceneId()
			if operation_task_chain_flag and is_show_block then
				block_data, eff_data = self.data:GetBlockPosList(scene_id)
			end

			scene_logic:CheckMapBlockIsCreate(scene_id, is_show_block, block_data, eff_data)
			scene_logic:SetIsNeedCheckCommit(true)
		end
	end

	if self.skill_view ~= nil then
		self.skill_view:Flush()
	end

	if self.fb_info ~= nil then
		if is_flush_info then
			if self.fb_info:IsOpen() then
				self.fb_info:Flush(0, "all", {level_change = level_change})
			end
		end
	end

	if self.schedule_view ~= nil then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityCaiJiSpecialGather(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityCaiJiSpecialGather(protocol)
		return
	end

	self.data:SetCaiJiSpecialGatherstatus(protocol)

	if self.schedule_view ~= nil then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:SendTaskChainLeaveTaskScene(taskchain_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossActivityTCActivityExitTaskScene)
	protocol.taskchain_type = taskchain_type or TASK_CHAIN_ACTIVITY_TYPE.OPERATION
 	protocol:EncodeAndSend()
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivity16GGDBInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivity16GGDBInfo(protocol)
		return
	end

	local info = self.data:GetSixteenCellInfo()
	local level_change = false
	if info ~= nil and info.level ~= protocol.level then
		level_change = true
	end

	self.data:SetSixteenCellInfo(protocol)

	if self.fb_info:IsOpen() then
		self.fb_info:Flush(0, "all", {level_change = level_change})
	end

	if self.schedule_view ~= nil then
		self.schedule_view:Flush()
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		local data = nil
		if protocol.cur_status == OPERATION_TASK_CHAIN_SIXTEEN_CELL_ROLE_STATUS.INVINCIBLE then
			local function call_back()
				if main_role ~= nil then
					main_role:SetUnderShowInfo(nil)
				end
			end

			data = {
				show_type = FOLLOW_UI_UNDER_BAR_TYPE.TIMER,
				param = protocol.status_end_timestamp,
				timer_str = Language.OpertionAcitvity.TaskChain.InvincibleTimer,
				call_back = call_back,
			}
		end

		main_role:SetUnderShowInfo(data)
	end

	if Scene.Instance:GetSceneType() == SceneType.CROSS_TASK_CHAIN_AVOID then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			local operation_task_chain_flag = self.data:GetTaskChainIsOpen()
			local block_data = nil
			local eff_data = nil
			local is_show_block = protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY
			local scene_id = Scene.Instance:GetSceneId()
			if operation_task_chain_flag and is_show_block then
				block_data, eff_data = self.data:GetBlockPosList(scene_id)
			end

			scene_logic:CheckMapBlockIsCreate(scene_id, is_show_block, block_data, eff_data)
		end
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivity16GGDBRoundInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivity16GGDBRoundInfo(protocol)
		return
	end

	self.data:SetSixteenCellRoundInfo(protocol)

	if self.schedule_view ~= nil then
		self.schedule_view:Flush()
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		local data = nil
		if protocol.status == OPERATION_TASK_CHAIN_SIXTEEN_CELL_STATUS.WARN then
			local function call_back()
				if main_role ~= nil then
					main_role:SetUnderShowInfo(nil)
				end
			end

			-- data = {
			-- 	show_type = FOLLOW_UI_UNDER_BAR_TYPE.TIMER,
			-- 	param = protocol.status_end_timestamp,
			-- 	timer_str = Language.OpertionAcitvity.TaskChain.InvincibleTimer,
			-- 	call_back = call_back,
			-- }

			local ass, name = ResPath.GetMiscEffect("effect_JingHu_ChuanSongMen")
			local time = math.floor(protocol.status_end_timestamp - TimeWGCtrl.Instance:GetServerTime())
			-- 防止过长
			time = time > 10 and 10 or time
			local map_list = self.data:GetMapCellList()
			local pos = main_role:GetLuaPosition()
			local function call_back(obj)
				table.insert(self.eff_list, obj)
			end

			local hor_tab = bit:d2b_two(protocol.jiguan_hor_flags)
			local ver_tab = bit:d2b_two(protocol.jiguan_ver_flags)

			local hor_rotate = Quaternion.Euler(0, 0, 0)
			local ver_rotate = Quaternion.Euler(0, 90, 0)

			for i = 1, GameEnum.SIXTEEN_CELL_NUM do
				if hor_tab[i - 1] == 1 then
					local start_data = map_list[1][i]
					local start_pos = Vector3(start_data.real_x, pos.y + 2, start_data.real_y)
					local real_x, real_y = GameMapHelper.LogicToWorld(start_data.x - start_data.size * 0.5, start_data.y)
					local start_pos = Vector3(real_x, pos.y + 2, real_y)
					EffectManager.Instance:PlayAtTransform(ass, name, G_EffectLayer, time, start_pos, hor_rotate)
				end

				if ver_tab[i - 1] == 1 then
					local start_data = map_list[i][1]
					local start_pos = Vector3(start_data.real_x, pos.y + 2, start_data.real_y)
					local real_x, real_y = GameMapHelper.LogicToWorld(start_data.x, start_data.y - start_data.size * 0.5)
					local start_pos = Vector3(real_x, pos.y + 2, real_y)
					EffectManager.Instance:PlayAtTransform(ass, name, G_EffectLayer, time, start_pos, ver_rotate)
				end
			end

			--预警表示新的一轮开始去掉显示
			local follow_ui = main_role:GetFollowUi()
			if follow_ui ~= nil then
				local asset,bundle = ResPath.GetOperationTaskChainF2("task_chain_lose_face")
				follow_ui:SetFaceIcon(false, asset, bundle, "", "", nil, false)
			end
		end

		-- main_role:SetUnderShowInfo(data)
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivity16GGDBRoundResult(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivity16GGDBRoundResult(protocol)
		return
	end

	if protocol.succ == 1 then
		ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "show_tip", {res = "img_tip_1", show_type = OPERATION_TASK_CHAIN_TIP_TYPE.CHANGE_IMG, str = protocol.add_score})
	else
		ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "show_tip", {res = "img_tip_5", show_type = OPERATION_TASK_CHAIN_TIP_TYPE.FIXED, str = ""})
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.CROSS_TASK_CHAIN_AVOID then
			local main_role = Scene.Instance:GetMainRole()
			if main_role ~= nil then
				local follow_ui = main_role:GetFollowUi()
				if follow_ui ~= nil then
					local asset,bundle = ResPath.GetOperationTaskChainF2("task_chain_lose_face")
					follow_ui:SetFaceIcon(true, asset, bundle, "", "", nil, false)
				end
			end
		end
	end	
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivity16GGDBJiGuanInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivity16GGDBJiGuanInfo(protocol)
		return
	end

	self.data:SetSixteenCellJiGuanInfo(protocol)

	-- local str = Language.OpertionAcitvity.TaskChain.AvoidFailStr
	-- if protocol.succ == 1 then
	-- 	-- str = string.format(Language.OpertionAcitvity.TaskChain.AvoidSussStr, protocol.add_score)
	-- 	ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "show_tip", {res = "img_tip_1", show_type = OPERATION_TASK_CHAIN_TIP_TYPE.CHANGE_IMG, str = protocol.add_score})
	-- else
	-- 	ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, 0, "show_tip", {res = "img_tip_5", show_type = OPERATION_TASK_CHAIN_TIP_TYPE.FIXED, str = ""})
	-- end

	-- SysMsgWGCtrl.Instance:ErrorRemind(str)

	local map_list = self.data:GetMapCellList()
	local main_role = Scene.Instance:GetMainRole()

	if map_list ~= nil and main_role ~= nil then
		local bundle,asset = ResPath.GetMiscEffect("effect_JingHu_lang")
		local pos = main_role:GetLuaPosition()
		local hor_tab = bit:d2b_two(protocol.jiguan_hor_flags)
		local ver_tab = bit:d2b_two(protocol.jiguan_ver_flags)

		for i = 1, GameEnum.SIXTEEN_CELL_NUM do
			if hor_tab[i - 1] == 1 then
				local start_data = map_list[1][i]
				local real_x, real_y = GameMapHelper.LogicToWorld(start_data.x - start_data.size * 0.5, start_data.y)
				local start_pos = Vector3(real_x, pos.y + 2, real_y)
				local end_data = map_list[4][i]
				local end_pos = Vector3(end_data.real_x, main_role.y, end_data.real_y)
				EffectManager.Instance:PlayControlEffect(self, bundle, asset, start_pos, end_pos)

				-- local direction = end_pos - start_pos
				-- direction.y = 0
				-- local rotate = Quaternion.LookRotation(direction)
				-- EffectManager.Instance:PlayAtTransform(bundle, asset, G_EffectLayer, 1, start_pos, rotate)
			end

			if ver_tab[i - 1] == 1 then
				local start_data = map_list[i][1]
				local real_x, real_y = GameMapHelper.LogicToWorld(start_data.x, start_data.y - start_data.size * 0.5)
				local start_pos = Vector3(real_x, pos.y + 2, real_y)
				local end_data = map_list[i][4]
				local end_pos = Vector3(end_data.real_x, main_role.y, end_data.real_y)
				EffectManager.Instance:PlayControlEffect(self, bundle, asset, start_pos, end_pos)
				
				-- local direction = end_pos - start_pos
				-- direction.y = 0
				-- local rotate = Quaternion.LookRotation(direction)
				-- EffectManager.Instance:PlayAtTransform(bundle, asset, G_EffectLayer, 1, start_pos, rotate)
			end
		end
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityOverInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityOverInfo(protocol)
		return
	end

	self.data:SetShowThinkData(protocol.mingwang)
	local scene_type = Scene.Instance:GetSceneType()

	if scene_type == SceneType.Common then
		self.data:SetShowThinkFlag(false)

		-- if self.scene_change_complete ~= nil then
		-- 	GlobalEventSystem:UnBind(self.scene_change_complete)
		-- 	self.scene_change_complete = nil
		-- end
		self.task_over_check_mark = false

		if self.think_view:IsOpen() then
			self.think_view:Flush()
		else
			self.think_view:Open()
		end
	else
		self.data:SetShowThinkFlag(true)
		-- if self.scene_change_complete == nil then
		-- 	self.scene_change_complete = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneChangeComplete, self))
		-- end
		self.task_over_check_mark = true
	end

	RemindManager.Instance:Fire(RemindName.OperationTaskChain)
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityYSDBInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityYSDBInfo(protocol)
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if protocol.task_status ~= OPERATION_TASK_CHAIN_ACT_STATUS.STANDY and scene_logic ~= nil and scene_logic:GetIsNeedGuaJi() then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			scene_logic:SetIsNeedGuaJi(false)
		end
	end

	local info = self.data:GetYunSongAvoidInfo()
	local level_change = false
	if info ~= nil and info.level ~= protocol.level then
		level_change = true
	end

	self.data:SetYunSongAvoidInfo(protocol)

	if self.skill_view ~= nil then
		self.skill_view:Flush()
	end

	if self.fb_info:IsOpen() then
		self.fb_info:Flush(0, "all", {level_change = level_change})
	end

	if self.schedule_view ~= nil then
		self.schedule_view:Flush()
	end

	if Scene.Instance:GetSceneType() == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			local operation_task_chain_flag = self.data:GetTaskChainIsOpen()
			local block_data = nil
			local eff_data = nil
			local is_show_block = protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY
			local scene_id = Scene.Instance:GetSceneId()
			if operation_task_chain_flag and is_show_block then
				block_data, eff_data = self.data:GetBlockPosList(scene_id)
			end

			scene_logic:CheckMapBlockIsCreate(scene_id, is_show_block, block_data, eff_data)
		end
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityYSDBJiGuanInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityYSDBJiGuanInfo(protocol)
		return
	end

	self.data:SetYunSongAvoidJiGuanInfo(protocol)

	if protocol.jiguan_status == OPERATION_TASK_CHAIN_YUN_SONG_ADOID_JIGUAN_STATUS.WARN then
		local show_list = self.data:GetShowYunSongWarnEffList()
		if show_list ~= nil then
			local time = protocol.continue_time
			time = time > 5 and 5 or time
			for k, v in pairs(show_list) do
				EffectManager.Instance:PlayAtTransform(v.bundle, v.asset, G_EffectLayer, time, v.pos, v.rotation)
			end
		end
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityYSDBGatherStatus(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityYSDBGatherStatus(protocol)
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		if protocol.status == OPERATION_TASK_CHAIN_YUN_SONG_ADOID_GATHER_STATUS.SUCC then
			main_role:SetIsGatherState(false)
			-- 2秒容错
			local time = TimeWGCtrl.Instance:GetServerTime() + 2
			self.data:SetGatherWaitTime(time)

			ViewManager.Instance:FlushView(GuideModuleName.OperationTaskChainScheduleView, nil, "show_tip", {res = "img_yunsong_tip_1", res2 = "img_yunsong_tip_2", str = '1', show_type = OPERATION_TASK_CHAIN_TIP_TYPE.CHANGE_IMG})
		else
			self.data:SetGatherWaitTime(nil)
		end
	end
end


function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityShouHuInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityShouHuInfo(protocol)
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	local scene_logic = Scene.Instance:GetSceneLogic()
	local obj_id = self.data:GetShouHuObjId()

	if obj_id == nil or obj_id ~= protocol.shouhu_objid then
		if scene_logic ~= nil then
			if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and GuajiCache.target_obj:IsMonster() then
				if not scene_logic:IsEnemy(GuajiCache.target_obj) then
					GuajiWGCtrl.Instance:CancelSelect()
				end			
			end
		end
	end

	if scene_logic ~= nil and scene_type == SceneType.CROSS_TASK_CHAIN_GUARD then
		if scene_logic:IsNeedCheckModel() then
			local obj = Scene.Instance:GetObj(protocol.shouhu_objid)
			if obj ~= nil and not obj:IsDeleted() then
				local follow_ui = obj:GetFollowUi()
				if follow_ui ~= nil then
					scene_logic:SetIsNeedCheckModel(false)

					follow_ui:ForceSetVisible(true)
					follow_ui:SetHpVisiable(true)

					local bundle_str = nil
					local vo = obj:GetVo()
					if vo.hp > 0 then
						local max_hp = vo.max_hp == 0 and 1 or vo.max_hp
						local hp = vo.hp / max_hp
						bundle_str = self.data:GetGuardBubbleStr(hp)
					end

					self:CheckGuardBubble(protocol.shouhu_objid, bundle_str)
				end
			end
		end
	end

	local info = self.data:GetGuardInfo()
	local level_change = false
	if info ~= nil and info.level ~= protocol.level then
		level_change = true
	end
	
	self.data:SetGuardInfo(protocol)

	if self.fb_info:IsOpen() then
		self.fb_info:Flush(0, "all", {level_change = level_change})
	end

	if scene_type == SceneType.CROSS_TASK_CHAIN_GUARD then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			local operation_task_chain_flag = self.data:GetTaskChainIsOpen()
			local block_data = nil
			local eff_data = nil
			local is_show_block = protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY
			local scene_id = Scene.Instance:GetSceneId()
			if operation_task_chain_flag and is_show_block then
				block_data, eff_data = self.data:GetBlockPosList(scene_id)
			end

			scene_logic:CheckMapBlockIsCreate(scene_id, is_show_block, block_data, eff_data)
		end
	end
end

function OperationTaskChainWGCtrl:CheckGuardBubble(obj_id, str)
	self:RemoveDelay()
	if obj_id == nil or str == nil then
		return
	end

	local obj = Scene.Instance:GetObj(obj_id)
	if obj == nil or obj:IsDeleted() then
		return
	end

	local follow_ui = obj:GetFollowUi()
	if follow_ui == nil then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.CROSS_TASK_CHAIN_GUARD then
		return
	end

	local function callback()
		local info = self.data and self.data:GetGuardInfo()
		if info == nil then
			return
		end

		local bundle_str = nil
		if obj ~= nil and not obj:IsDeleted() then
			local vo = obj:GetVo()
			if vo.hp > 0 then
				local max_hp = vo.max_hp == 0 and 1 or vo.max_hp
				local hp = vo.hp / max_hp
				bundle_str = self.data:GetGuardBubbleStr(hp)
			end
		end

		if bundle_str == nil then
			return
		end

		self.delay_time = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CheckGuardBubble, self, info.shouhu_objid, bundle_str), 2)
	end
	
	follow_ui:ForceSetVisible(true)
	follow_ui:ChangeBubble(str, 4, callback)
	follow_ui:ShowBubble()
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityShouHuWaveInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityShouHuWaveInfo(protocol)
		return
	end

	self.data:SetGuardWaveInfo(protocol)

	if self.schedule_view ~= nil then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:OnSCCrossActivityTCActivityBossInfo(protocol)
	--开服活动
	if protocol.taskchain_type and protocol.taskchain_type == TASK_CHAIN_ACTIVITY_TYPE.OPENSERVER then
		-- OpenServerTaskChainWGCtrl.Instance:OnSCCrossActivityTCActivityBossInfo(protocol)
		return
	end

	local info = self.data:GetBossInfo()
	local level_change = false
	if info ~= nil and info.level ~= protocol.level then
		level_change = true
	end

	self.data:SetBossInfo(protocol)

	if self.fb_info:IsOpen() then
		self.fb_info:Flush(0, "all", {level_change = level_change})
	end

	-- if self.schedule_view ~= nil then
	-- 	self.schedule_view:Flush()
	-- end

	if Scene.Instance:GetSceneType() == SceneType.CROSS_TASK_CHAIN_BOSS then
		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			local operation_task_chain_flag = self.data:GetTaskChainIsOpen()
			local block_data = nil
			local eff_data = nil
			local is_show_block = protocol.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.STANDY
			local scene_id = Scene.Instance:GetSceneId()
			if operation_task_chain_flag and is_show_block then
				block_data, eff_data = self.data:GetBlockPosList(scene_id)
			end

			scene_logic:CheckMapBlockIsCreate(scene_id, is_show_block, block_data, eff_data)
		end
	end
end

function OperationTaskChainWGCtrl:OperaGo()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_TASK_CHAIN or self.data:GetIsTaskChainTaskScene() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.TaskChain.IsInScene)
		return
	end

	local is_open = self.data:GetTaskChainIsOpen()
	if not is_open then
		local day_index_cfg = self.data:GetCurDayIndexCfg()
		if day_index_cfg ~= nil then
			SysMsgWGCtrl.Instance:ErrorRemind(day_index_cfg.tips1)
		end
		return
	end

	local day_index_cfg = self.data:GetCurDayIndexCfg()
	if day_index_cfg ~= nil then
		local task_chain_cfg = self.data:GetTaskChainCfg(day_index_cfg.task_chain_id)
		if task_chain_cfg ~= nil then
			RoleWGCtrl.Instance:SetJumpAlertCheck(task_chain_cfg.npc_scene, function()
				GuajiWGCtrl.Instance:MoveToNpc(task_chain_cfg.npc_id, nil, task_chain_cfg.npc_scene)
			end, true)
    	end
	end
end

function OperationTaskChainWGCtrl:OnSceneChangeComplete()
	if self.task_over_check_mark then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.Common and self.data:GetShowThinkFlag() then
			self.data:SetShowThinkFlag(false)

			-- if self.scene_change_complete ~= nil then
			-- 	GlobalEventSystem:UnBind(self.scene_change_complete)
			-- 	self.scene_change_complete = nil
			-- end
			self.task_over_check_mark = false

			if self.think_view:IsOpen() then
				self.think_view:Flush()
			else
				self.think_view:Open()
			end
		end
	end
	self:ChangeAcceptNpc() -- 切换国家的时候要改变
end

function OperationTaskChainWGCtrl:ResetInfoView()
	if self.fb_info ~= nil then
		self.fb_info:ResetInfo()
	end
end

function OperationTaskChainWGCtrl:HotUpdateCommon()
	self.data:HotUpdateCommon()

	if self.task_chain_view:IsOpen() then
		self.task_chain_view:Flush()
	end

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end

	if self.accept_view:IsOpen() then
		self.accept_view:Flush()
	end

	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end

	if self.entrance_view:IsOpen() then
		self.entrance_view:Flush()
	end

	if self.result_view:IsOpen() then
		self.result_view:Flush()
	end

	if self.skill_view:IsOpen() then
		self.skill_view:Flush()
	end

	if self.think_view:IsOpen() then
		self.think_view:Flush()
	end
end

function OperationTaskChainWGCtrl:HotUpdateHuSong()
	self.data:HotUpdateHuSong()

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end

	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end	
end

function OperationTaskChainWGCtrl:HotUpdateCaiJiXunLuo()
	self.data:HotUpdateCaiJiXunLuo()

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end
	
	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end

	if self.skill_view:IsOpen() then
		self.skill_view:Flush()
	end
end

function OperationTaskChainWGCtrl:HotUpdateCaiJi()
	self.data:HotUpdateCaiJi()

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end
	
	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end

	if self.skill_view:IsOpen() then
		self.skill_view:Flush()
	end
end

function OperationTaskChainWGCtrl:HotUpdateAvoid()
	self.data:HotUpdateAvoid()

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end
	
	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:HotUpdateYunSongAvoid()
	self.data:HotUpdateYunSongAvoid()

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end
	
	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:HotUpdateGround()
	self.data:HotUpdateGround()

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end
	
	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:HotUpdateBoss()
	self.data:HotUpdateBoss()

	if self.fb_info:IsOpen() then
		self.fb_info:Flush()
	end
	
	if self.schedule_view:IsOpen() then
		self.schedule_view:Flush()
	end
end

function OperationTaskChainWGCtrl:ClearCaiJiTaskStates()
	self.caiji_task_states = nil
end

function OperationTaskChainWGCtrl:CheckActIsOpen()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
	if not act_cfg then
		return
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < act_cfg.level or role_level > act_cfg.level_max then
		return
	end

	self:ChangeAcceptNpc() -- 等级发生变化的时候检测下NPC的变化

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.SHOW_TASK_CHAIN)
	if act_info and act_info.status ~= ACTIVITY_STATUS.CLOSE then
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN, act_info.next_time, act_cfg)
		if act_info.status == ACTIVITY_STATUS.STANDY and not self.data:IsTaskChainScene() then
			if self.data:GetTaskChainIsOpen() and OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN) then
				-- ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
				local day_index_cfg = self.data:GetCurDayIndexCfg()
				local task_chain_cfg = self.data:GetTaskChainCfg(day_index_cfg and day_index_cfg.task_chain_id)
				if task_chain_cfg then
					local cfg_data = ActivityWGData.Instance:GetActivityTipCig(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
					cfg_data = DeepCopy(cfg_data)
					cfg_data.desc = task_chain_cfg.act_notice or ""
					cfg_data.title = task_chain_cfg.task_chain_name or ""
					ActivityWGCtrl.Instance:OpenActNotice(cfg_data)
					ActivityWGCtrl.Instance:OpenActivityTips(cfg_data)
				end
			end
		end
		return
	end

	act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
	if act_info and act_info.status ~= ACTIVITY_STATUS.CLOSE then
		local open_time,hour,min = self.data:GetTodayOpenTime()
		if open_time > 0 then
			local open_time_str = string.format("%d:%02d%s", hour, min, Language.Activity.KaiQi)
			MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN, nil, act_cfg, nil, nil, open_time_str)
		else
			MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN, nil, act_cfg)
		end
		return
	end

	MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN, false)
end