-- 跨服红包天降 开启红包活动界面.
RedPacketRainSendAddActivityView = RedPacketRainSendAddActivityView or BaseClass(SafeBaseView)

function RedPacketRainSendAddActivityView:__init()
	self.is_safe_area_adapter = true
    self.view_style = ViewStyle.Half
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/red_packet_rain_ui_prefab", "layout_red_packet_rain_send_add_activity")
end

function RedPacketRainSendAddActivityView:ReleaseCallBack()

end

function RedPacketRainSendAddActivityView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_send, BindTool.Bind(self.OnClickBtnSend, self))
    XUI.AddClickEventListener(self.node_list.tips_check, BindTool.Bind(self.OnClickCheck, self))
    
end

function RedPacketRainSendAddActivityView:OnClickCheck(is_on)
    local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	-- --是否勾选今日不在展示提示框 :1(勾选) 0(未勾选)
	PlayerPrefsUtil.SetInt("red_packet_rain_send_add_activity" .. role_id .. cur_day, is_on and 1 or 0)
end

function RedPacketRainSendAddActivityView:OnFlush()
    local role_info = RedPacketRainWGData.Instance:GetRoleInfo()
    local add_count = 0

    if role_info and role_info.add_activity then
        add_count = role_info.add_activity
    end
    local role_add_activity_max = RedPacketRainWGData.Instance:GetOtherCfg("role_add_activity_max")
    self.node_list.text_add_count.text.text = string.format(Language.RedPacketRain.ToDayAddNum, add_count, role_add_activity_max)

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	-- --是否勾选今日不在展示提示框 :1(勾选) 0(未勾选)
	local value = PlayerPrefsUtil.GetInt("red_packet_rain_send" .. role_id .. cur_day)

    self.node_list.tips_check.toggle.isOn = value == 1
end

function RedPacketRainSendAddActivityView:OnClickBtnSend()
    local activity_info = RedPacketRainWGData.Instance:GetAcitivityInfo()
    if activity_info and activity_info.round_state ~= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.RedPacketRain.NotAddActivityTips)
        return
    end

    local grade_cfg = RedPacketRainWGData.Instance:GetGradeCfgByItemId(self.add_activity_item_id)

    if grade_cfg then

        local item_num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.item_id)
        if item_num >= grade_cfg.item_num then
            RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_USE_ITEM_ADD_ACTIVITY, grade_cfg.grade)
            RedPacketRainWGCtrl.Instance:RedPacketOperate(CROSS_RED_PAPER_FALLING_OPERATE_TYPE.CROSS_RED_PAPER_FALLING_OPERATE_TYPE_ROLE_INFO)
            self:Close()
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.RedPacketRain.NotItemTips)
        end

    end

end

function RedPacketRainSendAddActivityView:SetAddActivityItemId(item_id)
    self.add_activity_item_id = item_id
end


