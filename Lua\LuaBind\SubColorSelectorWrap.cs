﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class SubColorSelectorWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(SubColorSelector), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("OnPointerDown", OnPointerDown);
		<PERSON><PERSON>RegFunction("GetCanvasFinalPos", GetCanvasFinalPos);
		<PERSON><PERSON>RegFunction("OnDrag", OnDrag);
		<PERSON><PERSON>RegFunction("FulshNowPosFinalColor", FulshNowPosFinalColor);
		<PERSON>.RegFunction("UpdateSoftLocalPos", UpdateSoftLocalPos);
		<PERSON><PERSON>RegFunction("UpdateColor", UpdateColor);
		<PERSON><PERSON>RegFunction("SetSelectColorCB", SetSelectColorCB);
		<PERSON>.RegFunction("GetSelectorColor", GetSelectorColor);
		<PERSON><PERSON>Function("GetSelectorHexColor", GetSelectorHexColor);
		<PERSON><PERSON>un<PERSON>("GetSelectColorHSV", GetSelectColorHSV);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("ColorPlate", get_ColorPlate, set_ColorPlate);
		L.RegVar("FinalColor", get_FinalColor, set_FinalColor);
		L.RegVar("m_ColorPickPanel", get_m_ColorPickPanel, set_m_ColorPickPanel);
		L.RegVar("m_SelectorWheel", get_m_SelectorWheel, set_m_SelectorWheel);
		L.RegVar("RectTrans", get_RectTrans, set_RectTrans);
		L.RegVar("RectRoot", get_RectRoot, set_RectRoot);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerDown(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerDown(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCanvasFinalPos(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			UnityEngine.Vector3 o = obj.GetCanvasFinalPos(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FulshNowPosFinalColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			obj.FulshNowPosFinalColor();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateSoftLocalPos(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.UpdateSoftLocalPos(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateColor(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
				obj.UpdateColor();
				return 0;
			}
			else if (count == 2)
			{
				SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
				UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
				obj.UpdateColor(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: SubColorSelector.UpdateColor");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSelectColorCB(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			LuaFunction arg0 = ToLua.CheckLuaFunction(L, 2);
			obj.SetSelectColorCB(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSelectorColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			UnityEngine.Color o = obj.GetSelectorColor();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSelectorHexColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			string o = obj.GetSelectorHexColor();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSelectColorHSV(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SubColorSelector obj = (SubColorSelector)ToLua.CheckObject<SubColorSelector>(L, 1);
			UnityEngine.Vector3 o = obj.GetSelectColorHSV();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ColorPlate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.UI.Image ret = obj.ColorPlate;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ColorPlate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FinalColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.Color ret = obj.FinalColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FinalColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_ColorPickPanel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.UI.RawImage ret = obj.m_ColorPickPanel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_ColorPickPanel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_SelectorWheel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.RectTransform ret = obj.m_SelectorWheel;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_SelectorWheel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RectTrans(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.RectTransform ret = obj.RectTrans;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RectTrans on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RectRoot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.RectTransform[] ret = obj.RectRoot;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RectRoot on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ColorPlate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.UI.Image arg0 = (UnityEngine.UI.Image)ToLua.CheckObject<UnityEngine.UI.Image>(L, 2);
			obj.ColorPlate = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ColorPlate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_FinalColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.FinalColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FinalColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_ColorPickPanel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.UI.RawImage arg0 = (UnityEngine.UI.RawImage)ToLua.CheckObject<UnityEngine.UI.RawImage>(L, 2);
			obj.m_ColorPickPanel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_ColorPickPanel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_SelectorWheel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
			obj.m_SelectorWheel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_SelectorWheel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RectTrans(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
			obj.RectTrans = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RectTrans on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RectRoot(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SubColorSelector obj = (SubColorSelector)o;
			UnityEngine.RectTransform[] arg0 = ToLua.CheckObjectArray<UnityEngine.RectTransform>(L, 2);
			obj.RectRoot = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RectRoot on a nil value");
		}
	}
}

