require("game/honghuan_classic/honghuang_classic_view")
require("game/honghuan_classic/honghuang_classic_wg_data")

HongHuangClassicWGCtrl = HongHuangClassicWGCtrl or BaseClass(BaseWGCtrl)
function HongHuangClassicWGCtrl:__init()
    if nil ~= HongHuangClassicWGCtrl.Instance then
        ErrorLog("[HongHuangClassicWGCtrl]:Attempt to create singleton twice!")
    end
    HongHuangClassicWGCtrl.Instance = self

	self.view = HongHuangClassicView.New(GuideModuleName.HongHuangClassicView)
	self.data = HongHuangClassicWGData.New()
	self:RegisterAllProtocals()
end

function HongHuangClassicWGCtrl:__delete()
    HongHuangClassicWGCtrl.Instance = nil

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
end

function HongHuangClassicWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCChaoticGiftSpecialInfo, "OnSCChaoticGiftSpecialInfo")
end

function HongHuangClassicWGCtrl:OnSCChaoticGiftSpecialInfo(protocol)
	HongHuangClassicWGData.Instance:SetChaoticGiftSpecialInfo(protocol)
	RemindManager.Instance:Fire(RemindName.HongHuangClassic)
	MainuiWGCtrl.Instance:FlushView(0,"honghuangclassic_over")
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function HongHuangClassicWGCtrl:SendHongHuangClassicReq(opera_type, param_1, param_2, param_3)
	HongHuangGoodCoremonyWGCtrl.Instance:SendHongHuangGoodCoremonyReq(opera_type, param_1, param_2, param_3)
end