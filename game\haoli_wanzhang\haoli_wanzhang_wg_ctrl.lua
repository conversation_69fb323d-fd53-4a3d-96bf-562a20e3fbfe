require("game/haoli_wanzhang/haoli_wanzhang_wg_data")
require("game/haoli_wanzhang/haoli_wanzhang_view")

HaoliWanzhangWGCtrl = HaoliWanzhangWGCtrl or BaseClass(BaseWGCtrl)
function HaoliWanzhangWGCtrl:__init()
    if HaoliWanzhangWGCtrl.Instance then
		error("[HaoliWanzhangWGCtrl]:Attempt to create singleton twice!")
	end

    HaoliWanzhangWGCtrl.Instance = self
    self.data = HaoliWanzhangWGData.New()

    self.view = HaoliWanzhangView.New(GuideModuleName.HaoliWanzhangView)
    self:RegisterAllProtocols()
end

function HaoliWanzhangWGCtrl:__delete()
    HaoliWanzhangWGCtrl.Instance = nil

    self.data:DeleteMe()
	self.data = nil

    self.view:DeleteMe()
	self.view = nil

    if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

function HaoliWanzhangWGCtrl:OpenHaoliWanzhangView()
    self.view:Open()
end

function HaoliWanzhangWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAHaoLiWanZhang, "OnSCOAHaoLiWanZhang")

    self.act_change = BindTool.Bind(self.OnActivityChange, self)
    ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

    -- self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
    -- RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function HaoliWanzhangWGCtrl:OnSCOAHaoLiWanZhang(protocol)
    self.data:SetAllVientianeInfo(protocol)
    MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
    self:FlushView()
    RemindManager.Instance:Fire(RemindName.HaoliWanzhang)
end

function HaoliWanzhangWGCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.HaoliWanzhangView)
end

function HaoliWanzhangWGCtrl:OnActivityChange(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG and (status == ACTIVITY_STATUS.OPEN or status == ACTIVITY_STATUS.CLOSE) then
        MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
    end
end

function HaoliWanzhangWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
    if attr_name == "level" then
        local show_info = self.data:GetOtherCfg()
        local tianyin_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG)
        if (old_value < show_info.open_level and value >= show_info.open_level) and tianyin_is_open then
            MainuiWGCtrl.Instance:FlushView(0, "haoli_wanzhang_tip")
        end
    end
end