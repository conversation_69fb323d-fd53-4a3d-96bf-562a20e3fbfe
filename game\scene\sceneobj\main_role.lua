MainRole = MainRole or BaseClass(Role)
local Time = UnityEngine.Time
local MIN_DIS = 20
local HORIZONTAL_ANGLE = 12
local UP_MULTIPLYING_POWER = 0.1

function MainRole:__init(vo)
	self.obj_type = SceneObjType.MainRole
	self.draw_obj:SetObjType(self.obj_type)
	self.draw_obj:SetMountLoadedAndUpCallBack(BindTool.Bind(self.OnMountLoadedAndUp, self))
	self.shield_obj_type = ShieldObjType.MainRole
	self.followui_class = MainRoleFollow
	--self.projector_shadow_handle = ProjectorShadowHandle.New(self)


	self.is_use_more_quality_mat = true
	self.arrive_func = nil							-- 到达处理
	self.move_oper_cache = nil						-- 移动操作缓存
	self.move_oper_cache2 = nil  					-- 跳跃操作缓存
	self.move_cache_on_chongefeng_end = nil			-- 冲锋操作缓存
	self.is_only_client_move = false				-- 在某些玩法副本中, 全是机器人，又需要动态改变主角速度，移动设计成不通知服务器

	self.last_logic_pos_x = 0
	self.last_logic_pos_y = 0

	self.last_skill_id = 0
	self.last_skill_index = 0
	self.atk_is_hit = {}
	self.last_atk_end_time = 0

	self.path_pos_list = {}
	self.path_pos_index = 1

	self.last_in_safe = false					-- 上一刻是否在安全区
	self.is_auto_move = false					-- 是否自动寻路中

	self.is_specialskil = false
	self.is_special_jump = false

	self.jump_call_back = nil
	self.target_point = nil
	self.next_point = nil
	self.cur_jumping_type = nil
	self.target_jumping_id = nil

	self.target_x = 0
	self.target_y = 0

	self.total_stand_time = 0
	self.is_show_move_trail_effect = false

	self.chongfeng_callback = nil
	self.chongfeng_req_time = 0

	self.character_ghost = nil 					-- 残影组件
	self.is_enter_fight_camera = false
	self._moveSysTime = 0

	self.shadow_level = 0

	self.rotating_call_back = BindTool.Bind(self.RotatingCallBack, self)

	self.follow_npc_change_event = GlobalEventSystem:Bind(ObjectEventType.FOLLOW_NPC_CHANGE, BindTool.Bind(self.ChangeFollowNpc, self)) --刷新

	self.qinggong_index = 0
	self.auto_jump_count = 0
	self.cur_qinggong_state = QingGongState.None
	self.check_fly_task_update_time = 0
	self.last_sync_time = 0
	self.jump_time_stamp = 0
	self.qinggong_obj_time = 0
	self.old_qinggong_enabled = nil
	self.ready_to_ground = false
	self.wait_send_dis = 0
	self.play_run_audio_time = 0
	self.play_run_audio_interval = 0.4
	self.next_pet_atk_time = 0

	self.is_fly_down = false
	self.is_fly_up = false

	self.touch_began_event = GlobalEventSystem:Bind(LayerEventType.TOUCH_BEGAN, BindTool.Bind(self.TouchBegin, self))
	self.touch_end_event = GlobalEventSystem:Bind(LayerEventType.TOUCH_ENDED, BindTool.Bind(self.TouchEnd, self))
	self.touch_move_event = GlobalEventSystem:Bind(LayerEventType.TOUCH_MOVED, BindTool.Bind(self.TouchMove, self))
	self.fight_eff_change_event = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.FightEffectChange, self))

	AStarFindWay:SetBlockChangeCallback(function ()
		self:ClearAstarCacheFindWay()
	end)

	self.auto_do_husong = 0

	self.guaji_check_move_time = 0
	self.role_stand_time = 0
	self.jump_special_param = nil

	self.run_audio_index = 1
	self.follow_move_reason = CLIENT_MOVE_REASON.NONE
	self.send_skill_pos = 0

	self:InitCurDetailLevel()
end

function MainRole:InitCurDetailLevel()
	self.draw_obj:SetCurDetailLevel(SceneObjDetailLevel.High)
end

function MainRole:__delete()
	self:DestroyMoveTrail()

	if not IsNil(MainCameraFollow) then
		MainCameraFollow.Target = nil
		if CAMERA_TYPE == CameraType.Free then
			MainCameraFollow.AutoRotation = false
		end
	end

	-- if self.projector_shadow_handle then
	-- 	self.projector_shadow_handle:DeleteMe()
	-- 	self.projector_shadow_handle = nil
	-- end

	self.shadow_projector = nil

	if self.is_cancel_shuangxiu_alert then
		self.is_cancel_shuangxiu_alert:DeleteMe()
		self.is_cancel_shuangxiu_alert = nil
	end

	if self.follow_npc_change_event then
		GlobalEventSystem:UnBind(self.follow_npc_change_event)
		self.follow_npc_change_event = nil
	end

	self.character_ghost = nil
	self:CancelCameraUpdateTimer()

	if self.touch_began_event then
		GlobalEventSystem:UnBind(self.touch_began_event)
		self.touch_began_event = nil
	end

	if self.touch_end_event then
		GlobalEventSystem:UnBind(self.touch_end_event)
		self.touch_end_event = nil
	end

	if self.touch_move_event then
		GlobalEventSystem:UnBind(self.touch_move_event)
		self.touch_move_event = nil
	end

	if self.fight_eff_change_event then
		GlobalEventSystem:UnBind(self.fight_eff_change_event)
		self.fight_eff_change_event = nil
	end

	if self.follow_npc and self.follow_npc:GetVo() then
        Scene.Instance:DeleteObjByTypeAndKey(SceneObjType.FollowNpc, self.follow_npc:GetObjKey())
        self.follow_npc = nil
    end

	self:CancelResumeVisibleQuest()

	self.is_inter_scene = false
	self.character_ghost = nil
	self.jump_name = nil
	self.jump_normalized_time = nil
	self:DestroyTrail()
	self:ReleaseQingGongMount()
	self.is_fly_down = false
	self.is_fly_up = false

	--防止计时器残留
	self:DelAutoTaskEvent()

	GlobalTimerQuest:CancelQuest(self.qinggong_rush_delay)
	self.qinggong_rush_delay = nil

	self.guaji_check_move_time = 0
	self.role_stand_time = 0
	self.jump_enter_mitsurugi_status = false		-- 是否进入御剑状态（用来做镜头控制）
	self:ForceSetLOD(nil, -1)
end

-- 是否主角
function MainRole:IsMainRole()
	return true
end

function MainRole:GetObjKey()
	return nil
end

-- 获取职业
function MainRole:GetProf()
    if self.vo then
        return self.vo.prof % 10
    else
        return RoleWGData.Instance.GetRolePorf() % 10
    end
end

function MainRole:InitInfo()
	Role.InitInfo(self)
end

-- 属性变化
function MainRole:SetAttr(key, value, param)
	Role.SetAttr(self, key, value, param)

	if key == "level" then
		if not self.old_qinggong_enabled then
			self:CheckQingGong()
		end
	end
end

function MainRole:Update(now_time, elapse_time)
	Role.Update(self, now_time, elapse_time)
	if self.last_logic_pos_x ~= self.logic_pos.x or self.last_logic_pos_y ~= self.logic_pos.y then
		self.last_logic_pos_x = self.logic_pos.x
		self.last_logic_pos_y = self.logic_pos.y
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_POS_CHANGE, self.last_logic_pos_x, self.last_logic_pos_y)

		-- 状态不一样
		if Scene.Instance:GetSceneType() ~= SceneType.KF_HotSpring and Scene.Instance:GetSceneType() ~= SceneType.HotSpring then
			if self.last_in_safe ~= self:IsInSafeArea() then
				self.last_in_safe = self:IsInSafeArea()
				local convertion = SceneConvertionArea.SAFE_TO_WAY
				local str = Language.Common.OutSafeArea
				if self.last_in_safe then
					convertion = SceneConvertionArea.WAY_TO_SAFE
					str = Language.Common.InSafeArea
				end
				TipWGCtrl.Instance:ShowSystemMsg2(convertion,str)
				--SafaAreaWGCtrl.Instance:Open()--
				GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_CHANGE_AREA_TYPE, convertion)

				local role_list = Scene.Instance:GetRoleList()
				for k,v in pairs(role_list) do
					v:UpdateHpVisible()
				end
			end
		end

		if FunctionGuide.GUIDE_JUMP_TARGET and Vector3.Distance(FunctionGuide.GUIDE_JUMP_TARGET, Vector3(self.real_pos.x, 0, self.real_pos.y)) < 4 then
			FunctionGuide.Instance:FinishedOpenFun(false, true)
			UnityEngine.Time.timeScale = 0
			FunctionGuide.GUIDE_JUMP_TARGET = nil
		end

		self:UpdateSitState()
		self:UpdateYunbiaoState()
	end

	if self.add_level_eff_time and now_time - self.add_level_eff_time > 0.5 then
		self:RemoveBuff(BUFF_TYPE.UP_LEVEL)
		self.add_level_eff_time = nil
	end

	if self:IsStand() then
		self.total_stand_time = self.total_stand_time + elapse_time
	else
		self.total_stand_time = 0
	end

	self:UpdateMoveTrailEffect()

	-- 冲锋发起请求，服务器如不通过，一段时间后失效
	if self.chongfeng_req_time > 0 and now_time >= self.chongfeng_req_time + 1 then
		self.chongfeng_req_time = 0
		self.move_cache_on_chongefeng_end = nil
	end

	-- local is_cg_ing = CgManager.Instance:IsCgIng()
	-- 轻功
	-- if not self.is_landed and not is_cg_ing and not self.is_auto_jump then
	-- 	if now_time >= self.last_sync_time + 0.1 then
	-- 		self.last_sync_time = now_time

	-- 		local position = self.draw_obj:GetRoot().gameObject.transform.position
	-- 		self:SetRealPos(position.x, position.z)

	-- 		local forward = self.draw_obj:GetRoot().gameObject.transform.forward
	-- 		local dir = math.atan2(forward.z, forward.x)

	-- 		local height = position.y
	-- 		height = bit:_lshift(height, 4)

	-- 		local percent = 0
	-- 		if self.qinggong_obj_time > 0 then
	-- 			percent = (Status.NowTime - self.jump_time_stamp) / self.qinggong_obj_time
	-- 			percent = percent > 1 and 1 or percent
	-- 			percent = math.floor(percent * 15)
	-- 		end
	-- 		Scene.SendMoveReq(dir, self.logic_pos.x, self.logic_pos.y, 0, height + percent)
	-- 	end
	-- end

	-- 御剑
	-- if self.rider_sword_move_time and not is_cg_ing then
	-- 	if now_time >= self.last_sync_time + 0.1 then
	-- 		self.last_sync_time = now_time

	-- 		local position = self.draw_obj:GetRoot().gameObject.transform.position
	-- 		self:SetRealPos(position.x, position.z)
	-- 	end
	-- end

	-- 修复跳跃卡住的问题
	if self.fixed_jump_stuck_time_data and Status.NowTime >= self.fixed_jump_stuck_time_data.time then
		if self.fixed_jump_stuck_time_data.jump_type == JUMP_POINT_TYPE.JUMP then
			self:OnJumpEnd()
		else
			self.draw_obj:SetMoveCallback(nil)
			self:OnRiderSwordToEnd(true)
		end

		self.fixed_jump_stuck_time_data = nil
	end
end

-- 当进入场景
function MainRole:OnEnterScene()
	Role.OnEnterScene(self)
	self:UpdateCameraFollowTarget(true)
	self:GetFollowUi()
	self:CheckQingGong()
	self:ChangeFollowNpc()
	--self.projector_shadow_handle:CreateShieldHandle()

	if not self.is_fly_down then
		MainuiWGCtrl.Instance:UnLockCameraAutoRotate()
	end

	self:UpdateSitState(true)
end

-- 场景加载完回调
function MainRole:OnLoadSceneComplete()
	Role.OnLoadSceneComplete(self)

	--	策划需求，创角进入场景后要背对摄像机，这里代码写死判断，后面若改了新手创角场景和坐标，记得修改
	-- 	现在不管多少级普通场景有朝向配置直接走一下配置
	-- if self.vo.level == 1 and Scene.Instance:GetSceneId() == 1001 then
		local scene_id = Scene.Instance:GetSceneId()
		local scene_type = Scene.Instance:GetSceneType()
		local fb_angle = FuBenWGData.Instance:GetFbCamearCfg(scene_id, scene_type)
		if fb_angle and fb_angle.rotation_x and fb_angle.rotation_y and not IsNil(MainCameraFollow) then
			if fb_angle.pos_x and fb_angle.pos_y then
				if fb_angle.pos_x ~= 0 and fb_angle.pos_y ~= 0 then
					local main_role = Scene.Instance:GetMainRole()
					if main_role then
						main_role:SetDirectionByXY(fb_angle.pos_x, fb_angle.pos_y)
					end
				end
			end
		end
	-- end
end

-- 当重生
function MainRole:OnRealive()
	if Scene.Instance:GetSceneType() == SceneType.Field1v1 then
		return
	end

	Role.OnRealive(self)
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic ~= nil then
		scene_logic:OnMainRoleRealive()
	end

	self:ChangeFollowNpc()

	--之前的原逻辑，但是要放到我的逻辑之后不然会影响，如果我从出生点跑去打怪他直接挂机在半路遇到怪物就会打，停止向目标点移动了
	local auto_call_back = function()
		--这个挂机会影响他沿路去找怪物打别的怪只能移动个位置调用它
		local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
		if 1 == fb_scene_cfg.is_resurgence then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end

	if GuajiWGCtrl.Instance:TryRecoveryFollowByRealive() then
		self:DelAutoTaskEvent()
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	--是否需要自动去做任务
	--加一个普通场景的判断是因为如果他本来在自动任务，然后突然自己进入某些副本中，这个时候任务标识没清除的话，有可能被抢拉回来做任务
	local cur_task_type = TaskWGData.Instance:GetCurAuToTaskType()
	if -1 == cur_task_type or not NEED_AUTO_TASK_FLAG[cur_task_type] or scene_type ~= SceneType.Common then
		if not REALIVE_NO_GUAJI_SCENE_TYPE[scene_type] then
			auto_call_back()
		end

		return
	end

	--这里获取的任务就是可接和已接任务不需要做状态判断了
	--无可奈何延迟处理
	local task_cfg = TaskWGData.Instance:GetCurAutoTaskInfo(cur_task_type)
	if task_cfg then
		--每次进来先清除计时器，防止他在计时器还没完的时候就又死了
		self:DelAutoTaskEvent()

		self.auto_task_event = GlobalTimerQuest:AddDelayTimer(function()
			TaskWGCtrl.Instance:OperateFollowTask(task_cfg)
			self:DelAutoTaskEvent()
		end, 2)
	else
		auto_call_back()
	end
end

--清除自动任务计时器
function MainRole:DelAutoTaskEvent()
	if nil ~= self.auto_task_event then
		GlobalTimerQuest:CancelQuest(self.auto_task_event)
		self.auto_task_event = nil
	end
end

-- 当死亡
function MainRole:OnDie()
	Role.OnDie(self)
	self:ChangeFollowNpc()
end

-- 死亡状态
function MainRole:EnterStateDead()
	Role.EnterStateDead(self)
	if self:IsJump() then
		self:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
		GlobalEventSystem:Fire(OtherEventType.JUMP_STATE_CHANGE, false)
		self:ClearJumpCache()
	end

	GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_DEAD, self)
end

-- 是否在障碍区
function MainRole:GetIsInBlockArea()
	return AStarFindWay:IsBlock(self.logic_pos.x, self.logic_pos.y)
end

-- 附近是否有任务提交的npc by task_id
function MainRole:IsNearTaskCommitNpc(task_id)
	local limit_range = 25 ^ 2
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
	if task_cfg and task_cfg.commit_npc and task_cfg.commit_npc.id and task_cfg.commit_npc.id > 0 then
		local scene_npc_cfg = task_cfg.commit_npc
		local npc_scene_id = scene_npc_cfg.scene
		local cur_sence_id = Scene.Instance:GetSceneId()
		local role_x, role_y =  self:GetLogicPos()
		if cur_sence_id == npc_scene_id and GameMath.GetDistance(role_x, role_y, scene_npc_cfg.x, scene_npc_cfg.y) < limit_range then
			return true
		end
	end
	return false
end

-- 强制set LOD
function MainRole:ForceSetLOD(obj, lod_level)
	lod_level = lod_level or 0
	local lod_group
	if nil ~= obj and not IsNil(obj.gameObject) then
		lod_group = obj.gameObject:GetComponent(typeof(UnityEngine.LODGroup))
	else
		local main_part = self.draw_obj and self.draw_obj:GetPart(SceneObjPart.Main) or nil
		local obj = main_part and main_part:GetObj() or nil
		lod_group = obj and obj.gameObject:GetComponent(typeof(UnityEngine.LODGroup))
	end

	if not IsNil(lod_group) then
		lod_group:ForceLOD(lod_level)
	end
end

-- 设置真实位置
function MainRole:SetRealPos(pos_x, pos_y)
	local old_real_x = self.real_pos.x
	local old_real_y = self.real_pos.y

	local old_pos_x = self.logic_pos.x
	local old_pos_y = self.logic_pos.y

	Role.SetRealPos(self, pos_x, pos_y)

	if old_pos_x ~= self.logic_pos.x or old_pos_y ~= self.logic_pos.y then
		self.guaji_check_move_time = Status.NowTime
		self.role_stand_time = Status.NowTime
	end

	if old_real_x ~= pos_x or old_real_y ~= pos_y then
		Scene.Instance:UpdateSkillPos()
	end
end

-- 挂机检测移动时间 获取
function MainRole:GetGuaJiCheckMoveTime()
	return self.guaji_check_move_time
end

-- 挂机检测移动时间 重设
function MainRole:ResetGuaJiCheckMoveTime()
	self.guaji_check_move_time = Status.NowTime
end

function MainRole:CreateFollowUi()
    Role.CreateFollowUi(self)
    self.follow_ui:IsMainRole(true)
end


-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    站立     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 站立 更新状态
function MainRole:UpdateStateStand(elapse_time)
	Role.UpdateStateStand(self, elapse_time)

	-- 容错，避免因为某些原因，自动寻路被取消掉，可是玩家又无法移动
	if self.vo ~= nil and self.vo.husong_taskid ~= nil and self.vo.husong_taskid > 0 and self.auto_do_husong < Status.NowTime
	and not ViewManager.Instance:IsOpen(GuideModuleName.TaskDialog) then
		self.auto_do_husong = Status.NowTime + 1
		TaskWGCtrl.Instance:DoTask(self.vo.husong_taskid)
	end
end

-- 站立 进入状态
function MainRole:EnterStateStand()
	Role.EnterStateStand(self)
	self.run_audio_index = 1
end

-- 站立时间 获取
function MainRole:GetRoleStandTime()
	return self.role_stand_time
end

-- 站立时间 重设
function MainRole:ResetRoleStandTime()
	self.role_stand_time = Status.NowTime
end

-- 站立 状态退出
function MainRole:QuitStateStand()
	Role.QuitStateStand(self)
	self:TryLeaveSit(true)
end

-- 站立 获取总站立时间
function MainRole:GetTotalStandTime()
	return self.total_stand_time
end



-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    移动     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 移动 by点击
function MainRole:DoMoveByClick(...)
	--诛仙战场变成幽灵还能移动 特殊处理
	local scene_type = Scene.Instance:GetSceneType()
	local is_ghost_move = self:IsGhost() and (scene_type == SceneType.ETERNAL_NIGHT_FINAL or scene_type == SceneType.TEAM_COMMON_TOWER_FB_1)
	if self:IsRealDead() and not is_ghost_move then
		return
	end

	if MultiMountWGData.Instance:IsMyMultiMountTake() then
		local ok_func = function()
			MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.CANCLE_RIDE)
		end

		TipWGCtrl.Instance:OpenAlertTips(Language.MultiMount.CancelMultiMountTake, ok_func)
		return
	end

	GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
	if self:IsTeamFollowState() then
		local function follow_ok_fun()
			if self ~= nil and not self:IsDeleted() then
				self:SetIsFollowState(false)
				MainuiWGCtrl.Instance:FlushXunLuStates()
			end
		end

		TipWGCtrl.Instance:OpenAlertTips(Language.FollowState.RelieveTip, follow_ok_fun)
		return
	end

	-- print_error('DoMoveByClick---',...)
	if self:IsJump() then
	-- if self.vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP2 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CanNotMoveInJump)
		return
	end

	local logic = Scene.Instance:GetSceneLogic()
	if logic and not logic:CanCancleAutoGuaji() then
		return false
	end

	if self:IsClash() then
		local param_t = {...}
		if param_t ~= nil and #param_t > 0 then
			if self.draw_obj ~= nil and not self.is_set_rotating_call then
				self.draw_obj:SetRotatingCallback(self.rotating_call_back)
				self.is_set_rotating_call = true
			end

			self:SetDirectionByXY(param_t[1], param_t[2])
			self:SetCollideDirPos(param_t[1], param_t[2])
		end
		
		return false
	end

	local param_t = {...}
	if self:IsCircleLimit() then
			local limit_x, limit_y = self:GetMovePosByLimit(param_t[1], param_t[2])
			param_t[1] = limit_x
			param_t[2] = limit_y
	end

	GuajiWGCtrl.Instance:DoMoveByClick(param_t[1], param_t[2], param_t[10])
	local position = self:GetRoot().transform.position
	self:SetRealPos(position.x, position.z)
	self:ClearPathInfo()
	self.attack_skill_id = self.attack_skill_id or 0
	GlobalEventSystem:Fire(OtherEventType.MOVE_BY_CLICK)
	return self:DoMoveOperate(param_t[1], param_t[2], param_t[3], param_t[4], param_t[5], param_t[6], param_t[7], param_t[8], param_t[9])
end

-- 旋转回调
function MainRole:RotatingCallBack(succ)
	Role.RotatingCallBack(self, succ)
end

-- 移动 执行
function MainRole:DoMoveOperate(x, y, range, arrive_func, is_auto_move, ignore_high_area, move_reason, ignore_block, is_comefrom_joystick)
	if GLOBAL_CHECK_NO_MOVE_PRINT then
		print_error("----DoMoveOperate移动 执行111-----", x, y, range)
	end
	
	local limit_x, limit_y = self:GetMovePosByLimit(x, y)

	-- 移动点超出了限制范围
	-- 先清空移动信息还有回调，避免走到边缘的时候触发触发挂机的到达回调，造成循环
	if limit_x ~= x or limit_y ~= y then
		self.arrive_func = nil
		self.move_oper_cache = nil

		x = limit_x
		y = limit_y
		if GLOBAL_CHECK_NO_MOVE_PRINT then
			print_error("----DoMoveOperate移动 ByLimit-----", x, y, range)
		end
	end

	move_reason = move_reason or CLIENT_MOVE_REASON.NONE
	if move_reason ~= CLIENT_MOVE_REASON.FOLLOW then
        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            scene_logic:ResetTrackRoleInfo()
        end

        self:ResetServerFollowState()
		self:SetIsFollowState(false, nil, nil, nil, nil, nil, nil, nil, true)
	end

	-- if move_reason ~= CLIENT_MOVE_REASON.GUAJI then
		--GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
	-- end

	if self:GetIsInSit() then
		Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
	end

	GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)
	TaskGuide.Instance:SideTOStopTask(false)
	ignore_high_area = ignore_high_area and true or false
	local scene_logic = Scene.Instance:GetSceneLogic()
	local can_move = scene_logic:GetIsCanMove(x, y, range, arrive_func, is_auto_move)
	if not can_move then
		return false
	end

	if self:IsJump() then
		return false
	end

	if x == self.logic_pos.x and y == self.logic_pos.y then
		return false
	end

	if not self:CanDoMove() then
		if self:IsAtkPlaying() then
			self.move_oper_cache = {x = x, y = y, range = range, arrive_func = arrive_func, is_auto_move = is_auto_move, ignore_block = ignore_block, move_reason = move_reason}
		end

		if self:IsChongfenging() then
			self.move_cache_on_chongefeng_end = {x = x
												, y = y
												, range = range
												, arrive_func = arrive_func
												, is_auto_move = is_auto_move
												, ignore_high_area = ignore_high_area
												, move_reason = move_reason
												, ignore_block = ignore_block
												}
		end
		return false
	end

	x, y = AStarFindWay:GetAroundVaildXY(x, y, range or 3)
	-- x, y = AStarFindWay:GetLineEndXY2(self.logic_pos.x, self.logic_pos.y, x, y, ignore_high_area, ignore_block)
	if GLOBAL_CHECK_NO_MOVE_PRINT then
		print_error("----DoMoveOperate移动 执行222-----", x, y, range)
	end

	GuajiWGCtrl.Instance:MoveToRide(x, y)
	local move_x, move_y = x, y
	local path_pos_list = nil

	local logic_x, logic_y = self.logic_pos.x, self.logic_pos.y
	if GLOBAL_CHECK_NO_MOVE_PRINT then
		print_error("----DoMoveOperate移动 当前位置-----", logic_x, logic_y)
	end

	if not AStarFindWay:IsWayLine(self.logic_pos.x, self.logic_pos.y, x, y, ignore_high_area, ignore_block) then
		-- 先判断寻路的起始点是否为Block
		-- 如果是block，则从周围2格之内找一个合法的起始点
		if AStarFindWay:IsBlock(logic_x, logic_y, ignore_high_area, ignore_block) then
			logic_x, logic_y = AStarFindWay:FindNearestValidPoint(logic_x, logic_y, 2)
		end
		-- 防止该方法同时调太多，从缓存中取
		path_pos_list = self:TryGetAstarCachePosList(logic_x, logic_y, x, y, ignore_high_area, ignore_block)
		if nil == path_pos_list then
			if not AStarFindWay:FindWay(u3d.vec2(logic_x, logic_y), u3d.vec2(x, y), ignore_high_area, ignore_block) then
				if CLIENT_DEBUG_LOG_STATE then
					print_error("Move Pos Error 	", Scene.Instance:GetSceneId(), x, y, range, logic_x, logic_y)
				end
				
				GlobalEventSystem:Fire(ObjectEventType.CAN_NOT_FIND_THE_WAY)
				if AStarFindWay:IsHighArea(self.logic_pos.x, self.logic_pos.y) then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.QingGong.CanNotFindWay)
				end
		
				return
			end
			path_pos_list = AStarFindWay:GenerateInflexPoint(range)
		end

		self.path_pos_index = 1
		if not path_pos_list or #path_pos_list == 0 then
			if CLIENT_DEBUG_LOG_STATE then
				print_error("Move Pos Error 	", Scene.Instance:GetSceneId(), x, y, range, logic_x, logic_y)
			end
			GlobalEventSystem:Fire(ObjectEventType.CAN_NOT_FIND_THE_WAY)
			return
		end

		self:CacheAstarCahcePosList(logic_x, logic_y, x, y, ignore_high_area, path_pos_list, ignore_block)

		if path_pos_list[1].x == 0 and path_pos_list[1].y == 0 then
			path_pos_list = {{x = x, y = y}}
		end
	else
		self.path_pos_index = 1
		if MoveCache.move_type == MoveType.Obj then
			if GLOBAL_CHECK_NO_MOVE_PRINT then
				print_error("----DoMoveOperate移动 FindOptimalPointNearTarget111-----", self.logic_pos.x, self.logic_pos.y, x, y, range)
			end

			move_x, move_y = AStarFindWay:FindOptimalPointNearTarget(self.logic_pos.x, self.logic_pos.y, x, y, range)
			if GLOBAL_CHECK_NO_MOVE_PRINT then
				print_error("----DoMoveOperate移动 FindOptimalPointNearTarget222-----", move_x, move_y)
			end
		else
			if GLOBAL_CHECK_NO_MOVE_PRINT then
				print_error("----DoMoveOperate移动 GetTargetXY111-----", self.logic_pos.x, self.logic_pos.y, move_x, move_y, range)
			end
			move_x, move_y = AStarFindWay:GetTargetXY(self.logic_pos.x, self.logic_pos.y, move_x, move_y, range)
			if GLOBAL_CHECK_NO_MOVE_PRINT then
				print_error("----DoMoveOperate移动 GetTargetXY222-----", move_x, move_y)
			end
		end

		path_pos_list = {{x = move_x, y = move_y}}
	end

	if not next(path_pos_list) then
		path_pos_list = {{x = self.logic_pos.x, y = self.logic_pos.y}}
	end

	move_x = path_pos_list[1].x
	move_y = path_pos_list[1].y
	if GLOBAL_CHECK_NO_MOVE_PRINT then
		print_error("----DoMoveOperate移动 执行333-----", move_x, move_y, range)
	end

	self.is_auto_move = is_auto_move or false
	self.follow_move_reason = move_reason

	-- 寻路从坐标格子中心那个世界坐标开始寻，地图坐标格子从0.5改成1单位，有时候会原地踏步或者往回走，所以直接从第二个点开始
	if logic_x == move_x and logic_y == move_y then
		move_x = path_pos_list[2] and path_pos_list[2].x or path_pos_list[1].x
		move_y = path_pos_list[2] and path_pos_list[2].y or path_pos_list[1].y
		if path_pos_list[2] then
			self.path_pos_index = 2
		end
	end

	self.path_pos_list = path_pos_list

	if arrive_func then
		self.arrive_func = arrive_func
	end

	self:SetIsMoving(true)
	local height = self:GetMoveHeightDis()

	if GLOBAL_CHECK_NO_MOVE_PRINT then
		print_error("----DoMoveOperate移动 执行444-----", move_x, move_y, range, self.vo.move_speed, self.special_speed, self:IsClash(), self:IsJump(), self:IsFear(), self:IsMitsurugi(), self.dic and self.dic.ignoreCollision == 1)
		print_error("----DoMoveOperate移动 执行555 特殊状态中-----", self:IsClash(), self:IsJump(), self:IsFear(), self:IsMitsurugi())
		print_error("----DoMoveOperate移动 执行666 是否忽略碰撞-----", self.dic and self.dic.ignoreCollision == 1)
	end
	Role.DoMove(self, move_x, move_y, nil, is_comefrom_joystick, height)
	self:SendMoveReq()
end

-- 移动 更新
-- 同位置不冲锋
local cf_pos_x = 0
local cf_pos_y = 0
function MainRole:UpdateMove(now_time, elapse_time)
	if Role.UpdateMove(self, now_time, elapse_time) then
		self._moveSysTime = self._moveSysTime + Time.deltaTime
		if self._moveSysTime > 0.2 then
			local is_move = cf_pos_x ~= self.logic_pos.x or cf_pos_y ~= self.logic_pos.y
			if not self.attack_skill_id or not SkillWGData.Instance:IsSkillToSelf(self.attack_skill_id) and is_move then
				cf_pos_x = self.logic_pos.x
				cf_pos_y = self.logic_pos.y
				FightWGCtrl.Instance:SendChongfengReq(self.logic_pos.x, self.logic_pos.y, SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_ZHENYI)
			end
			self._moveSysTime = 0
		end
	else
		cf_pos_x = self.logic_pos.x
		cf_pos_y = self.logic_pos.y
	end
end

-- 移动 圆形区域限制 设置
function MainRole:SetCircleLimitPos(pos)
	self.circle_limit_pos = pos
end

-- 移动 圆形区域限制 重置
function MainRole:ResetCircleLimitPos()
	self.circle_limit_pos = nil
end

-- 移动 圆形区域限制 获得
function MainRole:GetCircleLimitPos()
	return self.circle_limit_pos
end

-- 移动 获得移动点  圆形区域限制
function MainRole:GetMovePosByLimit(x, y, is_check)
	local go_x = x
	local go_y = y
	local is_limit = false

	if not self:IsCircleLimit() then
		return go_x, go_y, is_limit
	end

	local effect_list = FightWGData.Instance:GetMainRoleEffectList()
	local limit_buff_info = nil
	for _, effect in ipairs(effect_list) do
        if effect and effect.buff_type == BUFF_TYPE.EBT_CIRCLE_LIMIT then
            limit_buff_info = effect
            break
        end
    end

	if not limit_buff_info or not limit_buff_info.param_list or #limit_buff_info.param_list < 7 then
        return go_x, go_y, is_limit
    end

	-- 限制区域的圆形
	local limit_x = limit_buff_info.param_list[3]
	local limit_y = limit_buff_info.param_list[4]
	-- 限制区域的半径
	local limit_rang = limit_buff_info.param_list[5]

	local scene_id = limit_buff_info.param_list[6]
	local scene_key = limit_buff_info.param_list[7]

	local cur_scene_id = Scene.Instance:GetSceneId()
	local cur_scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
	-- 如果不在同一场景，直接返回
    if scene_id ~= cur_scene_id or cur_scene_key ~= scene_key then
        return go_x, go_y, is_limit
    end

	if go_x == nil or go_y == nil or (self.logic_pos.x == go_x and self.logic_pos.y == go_y) then
		return go_x, go_y, is_limit
	end

	local sub_pos = u3dpool.v2Sub({x = go_x, y = go_y}, {x = limit_x, y = limit_y})
	local go_len = u3dpool.v2Length(sub_pos, false)
	-- 当移动后的位置的平方距离超过了半径的平方，则进入限制处理
	if go_len - limit_rang * limit_rang > 0 then
		is_limit = true

		if not is_check then
			if GuajiCache.guaji_type == GuajiType.None or GuajiCache.guaji_type == GuajiType.Temporary then
				local pos = self:GetIntersectPos(limit_x, limit_y, limit_rang, self.logic_pos.x, self.logic_pos.y, x, y)
				if pos ~= nil then
					go_x = pos.x
					go_y = pos.y
				end
			else
				local dir = u3dpool.v2Normalize(sub_pos)
				go_x = math.floor(dir.x * limit_rang - 1) + limit_x
				go_y = math.floor(dir.y * limit_rang - 1) + limit_y
			end
		end
	end

	return go_x, go_y, is_limit
end

-- 获得相交位置
function MainRole:GetIntersectPos(cx, cy, r, stx, sty, edx, edy)
	local numParams = {cx, cy, r, stx, sty, edx, edy}
    for i, v in ipairs(numParams) do
        if type(v) ~= "number" then return nil end
    end

	local vSub = u3dpool.v2Sub
    local vLenSq = function(v) return u3dpool.v2Length(v, false) end	-- false 不开根号
    local vNorm = u3dpool.v2Normalize

	local centerPos = {x = cx, y = cy}
    local startPos = {x = stx, y = sty}
    local endPos = {x = edx, y = edy}

	-- 处理起点在圆外的情况
	-- 如果起点在圆外，沿移动方向投影到圆上
	-- 起点到圆心的向量和平方距离
    local startToCenter = vSub(startPos, centerPos)
    local startDistSq = vLenSq(startToCenter)
    local radiusSq = r * r

    -- 处理起点在圆外的情况（需要调整起点到圆边缘）
    if startDistSq >= radiusSq then
        -- 计算回退方向（朝路径反方向）
		local moveDir = vNorm(vSub(endPos, startPos))
		local backDist = math.sqrt(startDistSq - radiusSq)
		local adjusted = vSub(startPos, {x = moveDir.x * backDist, y = moveDir.y * backDist})

        -- 如果仍然在圆外则向圆心回退
		if vLenSq(vSub(adjusted, centerPos)) >= radiusSq then
			local toCenterDir = vNorm(vSub(centerPos, startPos))
			adjusted = vSub(startPos, {x = toCenterDir.x * backDist, y = toCenterDir.y * backDist})
		end

		startPos = adjusted
    end

    -- 处理零长度路径（起点终点重合）
    local pathVec = vSub(endPos, startPos)
    if vLenSq(pathVec) == 0 then
        return {x = math.floor(startPos.x), y = math.floor(startPos.y)}
    end

	-- 计算最近投影点
    local dotProduct = pathVec.x * (centerPos.x - startPos.x) + pathVec.y * (centerPos.y - startPos.y)
    local projRatio = MathClamp(dotProduct / vLenSq(pathVec), 0, 1)
    local nearest = vSub(startPos, {x = -pathVec.x * projRatio, y = -pathVec.y * projRatio})

	-- 计算交点偏移量（勾股定理）
    local centerToNearest = vSub(nearest, centerPos)
    local offset = math.sqrt(math.max(0, radiusSq - vLenSq(centerToNearest)))
    local pathDir = vNorm(pathVec)

    -- 生成两个候选交点
    local delta = {x = pathDir.x * offset, y = pathDir.y * offset}
    local intersectA = vSub(nearest, delta)
    local intersectB = vSub(nearest, {x = -delta.x, y = -delta.y})

    -- 选择离目标更近的交点（兼容z轴）
    return vLenSq(vSub(intersectA, endPos)) <= vLenSq(vSub(intersectB, endPos))
        and {x = math.floor(intersectA.x), y = math.floor(intersectA.y)}
        or {x = math.floor(intersectB.x), y = math.floor(intersectB.y)}
end

-- 移动 设置到达回调
function MainRole:SetArriveFunc(arrive_func)
	self.arrive_func = arrive_func
end

-- 移动 缓存A*寻路点list
function MainRole:CacheAstarCahcePosList(start_x, start_y, end_x, end_y, ignore_high_area, path_pos_list, ignore_block)
	self.astar_end_x = end_x
	self.astar_end_y = end_y
	self.astar_start_x = start_x
	self.astar_start_y = start_y
	self.astar_ignore_high_area = ignore_high_area
	self.astar_path_pos_list = path_pos_list
	self.astar_ignore_block = ignore_block
end

-- 移动 获取A*缓存寻路点list
function MainRole:TryGetAstarCachePosList(start_x, start_y, end_x, end_y, ignore_high_area, ignore_block)
	if self.astar_end_x == end_x and self.astar_end_y == end_y
		and self.astar_start_x == start_x and self.astar_start_y == start_y
		and self.astar_ignore_high_area == ignore_high_area
		and self.astar_ignore_block == ignore_block
		and nil ~= self.astar_path_pos_list then
		return self.astar_path_pos_list
	end

	return nil
end

-- 移动 清除A*缓存寻路
function MainRole:ClearAstarCacheFindWay()
	self.astar_end_x = nil
	self.astar_end_y = nil
	self.astar_start_x = nil
	self.astar_start_y = nil
	self.astar_ignore_high_area = nil
	self.astar_ignore_block = nil
end

-- 移动 是否可移动
function MainRole:CanDoMove(ignore_list)
	-- skill_can_move = SkillWGData.GetSkillCanMove(self.last_skill_id)
	if 0 ~= self.fight_move_pos.x or 0 ~= self.fight_move_pos.y then-- 普攻冲刺中
		return false
	end

	-- 有些CG是主角可以移动的
	if CgManager.Instance:IsCgIng() and not CgManager.Instance:IsCanMoveCG() then
		return false
	end
	   --结婚巡游状态不能自动做任务，不然会出现任务模型一直被拉出来
    if MarryWGData.Instance:GetOwnIsXunyou() then
   		return false
   	end

	if TaskWGCtrl.Instance:IsFly() then
		return false
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ETERNAL_NIGHT_FINAL or scene_type == SceneType.TEAM_COMMON_TOWER_FB_1 then
		if self:IsRealDead() or self:IsDead() then
			if self:IsGhost() then
				return true
			end
		end
	end

	if self:IsRealDead() or self:IsDead() then
		return false
	end

	if self.is_special_move then
		return false
	end

	--仙盟战变身矿车 播放攻击也能进入移动状态
	if self:IsAtkPlaying() and not self:IsXMZCar() then
		return false
	end

	if self:IsQingGong() then
		return false
	end

	if self:IsInResetPosState() then
		return false
	end

	if self:IsJump() then
		return false
	end

	-- 
	if self:GetIsGatherState() and self.caiji_type == CaijiType.TaskForce then
		return false
	end

	local logic = Scene.Instance:GetSceneLogic()
	if logic then
		if not logic:CanCancleAutoGuaji() then
			return false
		end
		if not logic:GetSceneLogicMoveState() then
			return false
		end
	end

	-- Buff 效果判断
	if (not ignore_list or not ignore_list.buff) and self:HasCantMoveBuff() then
		-- print_log("定身或者眩晕中无法移动. ")
		return false
	end

	-- 打开护送面板禁止移动
	if ViewManager.Instance:IsOpen(GuideModuleName.YunbiaoView) then
		return false
	end

	if self:IsKnockFly() then
		return false
	end

	if self:IsCharge() then
		return false
	end

	if self:GetIsInSpecialState(OBJ_SPECIAL_STATE.TWINKLE) then
		return false
	end

	if self:GetIsInSpecialState(OBJ_SPECIAL_STATE.ACCUMULATION) then
		return false
	end

	-- 观战状态无法移动
	if WorldsNO1WGData.Instance:IsObservationStatus() then
		return false
	end

	return true
end

-- 是否可御龙
function MainRole:CanDoYuLong(ignore_list)
	-- 有些CG是主角可以移动的
	if CgManager.Instance:IsCgIng() and not CgManager.Instance:IsCanMoveCG() then
		return false
	end
		--结婚巡游状态不能自动做任务，不然会出现任务模型一直被拉出来
	if MarryWGData.Instance:GetOwnIsXunyou() then
			return false
		end
	if TaskWGCtrl.Instance:IsFly() then
		return false
	end

	if self:IsRealDead() or self:IsDead() then
		return false
	end

	if self:IsQingGong() then
		return false
	end

	if self:IsMitsurugi() then
		return false
	end

	if self:IsJump() then
		return false
	end

	-- Buff 效果判断
	if (not ignore_list or not ignore_list.buff) and self:HasCantMoveBuff() then
		-- print_log("定身或者眩晕中无法移动. ")
		return false
	end

	-- 打开护送面板禁止移动
	if ViewManager.Instance:IsOpen(GuideModuleName.YunbiaoView) then
		return false
	end

	if self:IsKnockFly() then
		return false
	end

	if self:IsCharge() then
		return false
	end

	if self:GetIsInSpecialState(OBJ_SPECIAL_STATE.ACCUMULATION) then
		return false
	end

	-- 观战状态无法移动
	if WorldsNO1WGData.Instance:IsObservationStatus() then
		return false
	end

	return true
end

-- 移动 不能移动 is_show_msg 是否飘字提示
--特殊状态下不能移动（护送/巡游等）
function MainRole:CantPlayerDoMove(is_show_msg)
	if self.vo.husong_taskid > 0 then  --护送
		if is_show_msg then
			TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		end
		return true
	end

	if self:IsInXunYou() then
		if is_show_msg then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CanNotMoveInXunYou)
		end
		return true
	end

	if self:HasCantMoveBuffButCanShowMove() then
		if is_show_msg then
			if self:IsFear() then
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CanNotMoveInFear)
			elseif self:IsRepelMoving() then
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CanNotMoveInRepelMoving)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CanNotMoveInJump)
			end
		end

		return true
	end

	-- 场景限制操纵杆操作
	local logic = Scene.Instance:GetSceneLogic()
	if logic and not logic:CanPlayJoystickDoMove() then
		return true
	end

	return false
end

-- 移动 能否移动 by task_type
function  MainRole:CantPlayerDoMoveIgnoreHusong(task_type, flag)
	if task_type == GameEnum.TASK_TYPE_HU then
		return false
	end

	return self:CantPlayerDoMove(flag)
end

-- 移动 主角在寻路出的路径如果拐点相距很短时，会出现人物“抖向”问题
function MainRole:IsNeedChangeDirOnDoMove(pos_x, pos_y)
	if #self.path_pos_list > 1 then
		local dis = GameMath.GetDistance(self.logic_pos.x, self.logic_pos.y, pos_x, pos_y, false)
		if dis < 4 then
			return false
		end
	end

	if self:IsClash() then
		return false
	end

	return true
end

-- 移动 结束
function MainRole:MoveEnd()
	local pos = self.path_pos_list[self.path_pos_index + 1]
	if nil ~= pos then
		self.path_pos_index = self.path_pos_index + 1
		Role.DoMove(self, pos.x, pos.y, self.path_pos_index)
		self:SendMoveReq()
		return false
	end

	if self:IsMitsurugi() then
		if self:IsSwordMove() or self:IsSwordSprint() then
			return false
		end
	end

	self.is_auto_move = false
	self.follow_move_reason = CLIENT_MOVE_REASON.NONE
	return true
end

-- 移动 进入状态
function MainRole:EnterStateMove()
	Role.EnterStateMove(self)
	if self:IsJump() and self.vo.move_mode_param > 0 and not self:IsMitsurugi() then
	-- if self.vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP2 and self.vo.move_mode_param > 0 then
		if self.target_x == nil or self.target_y == nil then
			return
		end
		-- if self.timer_quest then
		-- 	GlobalTimerQuest:CancelQuest(self.timer_quest)
		-- 	self.timer_quest = nil
		-- end

		Role.DoMove(self, self.target_x, self.target_y)
		self:SendMoveReq()
	end
	GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_MOVE_START)
end

-- 移动 状态更新
function MainRole:UpdateStateMove(elapse_time)
	-- [[御剑特殊
	local now_time = Status.NowTime
	if self.rider_sword_move_time then
		
		if self.jump_special_param and self.jump_special_param.rw_down_time and now_time >= self.jump_special_param.rw_down_time then
			self:ChangeRiderSwordSate()
			self.jump_special_param.rw_down_time = nil
		end

		-- if now_time >= self.rider_sword_move_time then
		-- 	self.rider_sword_move_time = nil
		-- 	self:ChangeRiderSwordSate()
		-- end
	end

	if self.rider_sword_state ~= nil and self.rider_sword_state ~= ActionStep.End then
		self:JumpChangeHighByMove()
		return
	end
	--]]

	-- 御剑冲刺状态停止状态变更
	if self:IsSwordSprint() then
		return
	end

	Role.UpdateStateMove(self, elapse_time)
	self:PlayRunAudio()

	if self:IsMitsurugi() then
		return
	end
	-- if MainuiWGData.IsSetCameraZoom and not ScreenShotWGCtrl.Instance:IsOpenScreenShotView() then
	-- 	Scene.Instance:UpdateCameraDistance()
	-- end

	if self.wait_send_dis > 0 and self.move_total_distance - self.move_pass_distance <= self.wait_send_dis and not self:IsSpecialJump() then
		local dis = self.move_total_distance - self.move_pass_distance
		self.wait_send_dis = math.max(dis - MIN_DIS, 0)
		dis = math.min(dis, MIN_DIS)
		Scene.SendMoveReq(math.atan2(self.move_dir.y, self.move_dir.x), self.logic_pos.x, self.logic_pos.y, dis, 0)
	-- elseif self.move_total_distance - self.move_pass_distance > 20 then
	end

	self:JumpChangeHighByMove()

	if Scene.Instance ~= nil then
		Scene.Instance:CheckWaitGatherList()
	end
end

-- 移动状态 移动跳跃改变模型高度
function MainRole:JumpChangeHighByMove()
	if self.jump_special_param ~= nil then
		local param_t = self.jump_special_param
		local off_y
		local now_time = Status.NowTime
		-- 结束
		if param_t.end_time and now_time >= param_t.end_time then
			off_y = nil
		-- 上升
		elseif (param_t.ready_up_time == nil or (param_t.ready_up_time and now_time >= param_t.ready_up_time))
			 and (param_t.up_time and param_t.up_time >= now_time) then

			off_y = (1 - (param_t.up_time - now_time) / param_t.up_len) * param_t.y
			off_y = math.min(off_y, param_t.y)
			param_t.cur_y = off_y

		-- 下降
		elseif param_t.down_len and param_t.down_len > 0 and (param_t.down_time and now_time >= param_t.down_time) then
			off_y = param_t.cur_y * (1 - (now_time - param_t.down_time) / param_t.down_len)
			off_y = math.max(off_y, 0)
		end

		if off_y then
			local old_offset_pos = param_t.old_offset_pos
			local draw_obj = self:GetDrawObj()
			if draw_obj ~= nil then
				draw_obj:SetOffset(Vector3(old_offset_pos.x, old_offset_pos.y + off_y, old_offset_pos.z))
			end
		end
	end
end

-- 移动 跑步音效
function MainRole:PlayRunAudio()
	if self.play_run_audio_time > Status.NowTime then
		return
	end
	self.play_run_audio_time = Status.NowTime + self.play_run_audio_interval

	if self:IsJump() then
		return
	end

	local str = nil
	local is_from_mount = false
	if self:IsRiding() then
		str = NewAppearanceWGData.Instance:GetMountAudio(self:GetCurRidingResId())
		is_from_mount = str ~= AudioUrl.HorseshoeSound
	elseif self:IsWaterWay() then
		str = AudioUrl.WetlandRunning
	elseif self:IsWaterRipple() then
		str = AudioUrl.WetlandRunning --踩水面音效，后面换正式的
	else
		str = AudioUrl[(self.vo.sex == GameEnum.MALE and "MoveMale" or "MoveFemale") .. self.run_audio_index]
		self.run_audio_index = self.run_audio_index >= 3 and 1 or (self.run_audio_index + 1)
	end

	if not str then
		return
	end
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(str, is_from_mount))
end

-- 移动 退出移动状态
function MainRole:QuitStateMove()
	self.wait_send_dis = 0
	if not self.is_special_move and not self:IsSpecialJump() then
		-- 如果停止点在阻挡里，前后一格找一个可以站立的点
		if AStarFindWay:IsBlock(self.logic_pos.x, self.logic_pos.y) then
			for _, v in pairs({1, -1}) do
				local mov_dir = u3d.v2Mul(self.move_dir, v)
				local x, y = GameMapHelper.WorldToLogic(self.real_pos.x + mov_dir.x, self.real_pos.y + mov_dir.y)
				if not AStarFindWay:IsBlock(x, y) then
					self:SetLogicPosData(x, y)
					break
				end
			end
		end
		self:SendMoveReq(0)
	end

	Role.QuitStateMove(self)
	if self.arrive_func then
		local arrive_func = self.arrive_func
		self.arrive_func = nil
		arrive_func()
	else
		GuajiWGCtrl.SetMoveValid(false)
	end

	if self:IsJump() and self.vo.move_mode_param > 0 then
	-- if self.vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP2 and self.vo.move_mode_param > 0 then
		if self.jump_call_back then
			self.jump_call_back()
			self.jump_call_back = nil
		end
	end

	GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_MOVE_END)
end

-- 移动 清除路线信息
function MainRole:ClearPathInfo()
	self.path_pos_list = {}
	self.path_pos_index = 0
end

-- 移动 发送移动请求
function MainRole:SendMoveReq(distance)
	if Scene.Instance:IsChangeSceneIng() then return end
	if self.is_only_client_move then return end
	if self:IsJump() then return end
	-- if self:IsMitsurugi() then return end

	local dir = math.atan2(self.move_dir.y, self.move_dir.x)
	distance = distance or self.move_total_distance / Config.SCENE_TILE_WIDTH
	self.wait_send_dis = math.max(distance - MIN_DIS, 0)
	distance = math.min(distance, MIN_DIS)
	local height = self:GetMoveHeightDis(distance)

	-- 这个高度是玩家的相对移动高度需要修正为当前的实际高度发给服务器
	-- local real_pos_y = self.draw_obj.root_transform.localPosition.y
	-- local server_pos_y = real_pos_y + height
	Scene.SendMoveReq(dir, self.logic_pos.x, self.logic_pos.y, distance, height)
end



-- 获取当前的高度信息
function MainRole:GetMoveHeightDis(distance)
	if IsNil(MainCameraFollow) then
		return 0
	end

	if distance == 0 then
		return 0
	end

	if not self:IsMitsurugi() then return 0 end
	local distance = distance or self.move_total_distance / Config.SCENE_TILE_WIDTH
	-- distance = math.min(distance, MIN_DIS)
	local now_angle = MainCameraFollow.gameObject.transform.localRotation.eulerAngles.x
	local now_rot = MainCameraFollow.gameObject.transform.forward
	local role_for = self.draw_obj.root_transform.forward
	local relation = Vector3.Dot(now_rot, role_for)

	if now_angle > 100 then
		now_angle = -360 + now_angle
	end

	local tan_angle = now_angle - HORIZONTAL_ANGLE
	local dir = tan_angle < 0 and 1 or -1
	-- 计算当前长度的高度
	local height = math.abs(tan_angle) * distance * dir * 0.1 * UP_MULTIPLYING_POWER
	-- 拿到目标方向
	local aim_dir = relation < 0 and -1 or 1
	height = height * aim_dir
	-- local dir = math.atan2(self.move_dir.y, self.move_dir.x)
	-- print_error("计算了高度", math.tan(now_angle - HORIZONTAL_ANGLE), distance, UP_MULTIPLYING_POWER, dir, now_angle, HORIZONTAL_ANGLE)
	-- local height = (now_angle - HORIZONTAL_ANGLE) * 0.01 * distance * 0.2 * UP_MULTIPLYING_POWER * -1

	-- 
	return height
end

-- 移动 假移动设置
function MainRole:SetIsOnlyClintMove(is_only_client_move)
	self.is_only_client_move = is_only_client_move
end

-- 移动 获取路线 list
function MainRole:GetPathPosList()
	return self.path_pos_list
end

-- 移动 获取当前路线所在点
function MainRole:GetPathPosIndex()
	return self.path_pos_index
end

-- 移动 停止
function MainRole:StopMove(is_moveing)
	self.arrive_func = nil
	self.move_oper_cache = nil
	self:ClearPathInfo()
	if self:IsMove() and (not is_moveing or self:IsMoving() == false) then
		self.wait_send_dis = 0
		self:ChangeToCommonState()
	end
end

-- 移动 继续路线
function MainRole:ContinuePath()
	self.is_special_jump = false
	local call_back = GuajiWGCtrl.Instance:GetMoveToPosCallBack()
	if self.move_oper_cache2 then
		local cache = self.move_oper_cache2
		self.move_oper_cache2 = nil
		self.jump_call_back = nil
		GlobalTimerQuest:AddDelayTimer(function()
			self:DoMoveOperate(cache.x, cache.y, cache.range, cache.arrive_func, cache.is_auto_move, nil, cache.move_reason, cache.ignore_block)
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
			end, 0.1)
	-- else
	-- 	GuajiWGCtrl.Instance:ClearAllOperate()
	else
		local path_count = #self.path_pos_list
		if path_count > 1 then
			local x = self.path_pos_list[path_count].x
			local y = self.path_pos_list[path_count].y
			GlobalTimerQuest:AddDelayTimer(function()
				self:DoMoveOperate(x, y, 0, self.arrive_func, self.is_auto_move, nil, self.follow_move_reason)
				GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
				end, 0.1)
		else
			local state = MainuiWGCtrl.Instance:GetRoleAutoXunluState()
	        if state ~= nil and state == XunLuStatus.XunLu then
	            GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)
	        end
		end
	end
end

-- 移动 清除自动移动
function MainRole:ClearAutoMove()
	self.is_auto_move = false
end

-- 获取移速
function MainRole:GetMoveSpeed(is_jump, is_ignore_low)
	if self:IsFear() then -- 服务器那边说恐惧时写死速度
        return Scene.ServerSpeedToClient(100)
	end

    if self.vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP1 and self.jump_tong_bu == 0 and self.jump_speed and self.jump_speed > 0 then
		return self.vo.jump_factor * self.jump_speed
	end

	local speed = Scene.ServerSpeedToClient(self.vo.move_speed + self.special_speed)

	if self:IsJump() or is_jump or self:IsMitsurugi() then	--
		if self:IsMitsurugi() then
			speed = 1.4 * speed * self:GetMitsurugiExtraRate()
		else
			if self.vo.jump_factor then
				speed = self.vo.jump_factor * speed
			else
				speed = 1.8 * speed
			end
		end

		return speed
	end

	local is_low_speed = self:GetTotalMoveTime() < Config.SCENE_LOW_SPEED_TIME and not is_ignore_low
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic and scene_logic.GetSceneSpecialSpeed then
		local scene_speed = scene_logic:GetSceneSpecialSpeed(self)
		if scene_speed then
			speed = scene_speed
		end
	end

	speed = is_low_speed and speed * Config.SCENE_LOW_SPEED_FACTOR or speed

	return speed
end

function MainRole:GetMitsurugiExtraRate()
	if self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.EXPEDITE then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
		local sword_expedite_rate = other_cfg and other_cfg.sword_expedite_rate or 1
		return sword_expedite_rate
	end

	return 1
end

-- 是否自动寻路
function MainRole:IsAutoMove()
	return self.is_auto_move
end

-- 移动 模型移动
function MainRole:OnModelRemove(part, obj)
	Role.OnModelRemove(part, obj)
	if part == SceneObjPart.Mount or part == SceneObjPart.FightMount then
		self:CancelUpMountLookAtPointFixedTimer()
		self:UpdateCameraFollowTarget()
		-- 上下坐骑有偏移动画导致误差，需要延迟追加修复
		self.up_mount_look_at_point_fixed_timer = GlobalTimerQuest:AddDelayTimer(function()
			if not self:IsDeleted() then
				self:UpdateCameraFollowTarget()
			end
		end, 0.3)
	elseif part == SceneObjPart.Main then
		self:ForceSetLOD(obj, -1)
	end
end

function MainRole:DestroyMoveTrail()
	if self.move_move_trail_effect_loader then
		self.move_move_trail_effect_loader:Destroy()
		self.move_move_trail_effect_loader = nil
	end
end

-- 移动 拖尾特效
function MainRole:UpdateMoveTrailEffect()
	local is_low_speed = self:GetTotalMoveTime() < Config.SCENE_LOW_SPEED_TIME
	local not_show_trail_status = self:IsJump()
	local show_trail = not is_low_speed and not not_show_trail_status
	if show_trail == self.is_show_move_trail_effect then
		return
	end

	self.is_show_move_trail_effect = show_trail
	if is_low_speed then
		if self.move_move_trail_effect_loader then
			self.move_move_trail_effect_loader:SetActive(false)
		end
	else
		if self.move_move_trail_effect_loader then
			self.move_move_trail_effect_loader:SetActive(true)
		else
			-- 增加拖尾特效
			local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_run_tuowei")
			local trail_point = self.draw_obj:GetAttachPoint(AttachPoint.Hurt)
			if trail_point then
				self.move_move_trail_effect_loader = AllocAsyncLoader(self, "move_trail_effect")
				self.move_move_trail_effect_loader:SetIsUseObjPool(true)
				self.move_move_trail_effect_loader:SetIsInQueueLoad(true)
				self.move_move_trail_effect_loader:SetParent(trail_point)
				self.move_move_trail_effect_loader:Load(bundle_name, asset_name)
			end
		end
	end
end

-- 跟随 操作
function MainRole:OperaRoleFollow(scene_id, uuid, follow_type, client_param)
	if scene_id == nil or uuid == nil or follow_type == nil then
		self:SetIsFollowState(false)
		return
	end

	-- JXW 策划要求，跟随进入范围，如果队长幻化了双人坐骑，需要进入共乘
	MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.TEAM_FOLLOW_RIDE)

	local is_keep = client_param ~= CLIENT_MOVE_REQ_PARAM.NORMAL
	if Scene.Instance:GetSceneId() == scene_id then
		local obj = Scene.Instance:GetRoleByUUID(uuid)
        local scene_logic = Scene.Instance:GetSceneLogic()
		if obj == nil or obj:IsDeleted() then
	        if scene_logic ~= nil then
	            scene_logic:SetTrackRoleData(follow_type, uuid)
	            Scene.SendGetRoleMoveInfo(uuid, follow_type)
	            if follow_type == OBJ_FOLLOW_TYPE.TEAM then
	            	self.sever_follow_state = true
	            else
	            	self.sever_follow_state = false
	            end
	            -- Scene.SendChangeFollowState(OPERATION_TASK_CHAIN_FOLLOW_STATUS.FOLLOW)
	        end

	       	return
	    else
	    	local dis = GameMath.Rand(3, 5)
	    	obj:FollowMe(self, dis, follow_type, nil, nil, nil, nil, is_keep)
		end
	end
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    攻击     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 攻击 当攻击播放结束
function MainRole:OnAttackPlayEnd()
	Role.OnAttackPlayEnd(self)

	if self.move_oper_cache ~= nil then
		local cache = self.move_oper_cache
		self.move_oper_cache = nil
        local call_back = GuajiWGCtrl.Instance:GetMoveToPosCallBack()
		self:DoMoveOperate(cache.x, cache.y, cache.range, cache.arrive_func, cache.is_auto_move, nil, cache.move_reason, cache.ignore_block)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
	end
end

-- 攻击 设置参数（为毛只有特殊技能标记）
function MainRole:SetAttackParam(is_specialskill)
	self.is_specialskill = is_specialskill
end

-- 攻击 发起攻击
-- @awake_level 废弃
function MainRole:DoAttack(skill_id, target_x, target_y, target_obj_id, target_type, awake_level, use_type, skill_type)
	local target_obj = Scene.Instance:GetObj(target_obj_id)
	if target_obj then
		target_type = target_type or target_obj:GetType()
	end

	-- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(skill_id)
	self.normal_skill_awake_level = normal_skill and 1 or nil
	
	skill_id = normal_skill or skill_id
	self.arrive_func = nil
	if not self:CanAttack(skill_id) then
		return
	end

	-- 发起攻击取消跟随
	self:SetIsFollowState(false)

	if self:IsRidingNoFightMount() then
		MountWGCtrl.Instance:SendMountGoonReq(0)
	end

	-- 宠物技能
    -- if SkillWGData.IsPetSkill(skill_id) then
    -- 	self.attack_skill_id = skill_id
    -- 	self.attack_target_pos_x = target_x
    -- 	self.attack_target_pos_y = target_y
    -- 	self:SetAttackTarget(target_obj)
    -- 	self:SendFight()
    --     return
    -- end

	-- 技能前置状态逻辑
	if self:CheckFrontSkill(skill_id, target_x, target_y, target_obj_id, skill_type) then
    	self.attack_skill_id = skill_id
    	self.attack_target_pos_x = target_x
    	self.attack_target_pos_y = target_y
		return
	end

	Role.DoAttack(self, skill_id, target_x, target_y, target_obj_id, target_type, self.normal_skill_awake_level, use_type, skill_type)

	-- self:TryUseChongWuSkill(target_obj_id)
end

-- 客户端请求宠物技能
function MainRole:TryUseChongWuSkill(target_obj_id)
	if self:IsDeleted() then
		return
	end

	if not target_obj_id then
		return
	end

	if not self.vo or self.vo.lingchong_appeid <= 0 then
		return
	end

	local target_obj = GuajiCache.target_obj
	if not target_obj or target_obj:IsDeleted() then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local pet_skill_id = 400 -- 目前只有普攻
	local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(pet_skill_id, 1)
	if not skill_cfg then
		return
	end

	local target_x, target_y = target_obj:GetLogicPos()
	local m_pos_x, m_pos_y = self:GetLogicPos()
	local target_dis = GameMath.GetDistance(m_pos_x, m_pos_y, target_x, target_y)
	local skill_dis = skill_cfg.distance or 1
	if target_obj and target_obj.checks and target_obj.obj_scale then
		local targetModleR = target_obj.checks / 2 - 1
		skill_dis = math.max(targetModleR, skill_dis)
	end

	if target_dis <= skill_dis * skill_dis then
		if self.next_pet_atk_time <= Status.NowTime * 1000 then
			FightWGCtrl.Instance:SendPetSkillReq(target_obj_id)
			self.next_pet_atk_time = Status.NowTime * 1000 + skill_cfg.cd_s
		end
	end
end

-- 攻击 无目标
function MainRole:DoAttackForNoTarget(skill_id, awake_level, x, y, obj)
	if self:IsStand() then
		GuajiWGCtrl.SetMoveValid(false)
	end

	if self:IsAtkPlaying() then
		return
	end

	if self:IsMove() then
		self:StopMove()
	end

	-- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	skill_id = SkillWGData.Instance:GetNormalSkillTransformAwakeSkill(skill_id)
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(skill_id)
	self.normal_skill_awake_level = normal_skill and 1 or nil
	skill_id = normal_skill or skill_id

	-- local awake_level = SkillWGData.Instance:GetSkillAwakeLevel(skill_id)
	Role.DoAttackForNoTarget(self, skill_id, awake_level, x, y, obj)
	local self_pos = u3d.vec2(self:GetLogicPos())
	local dis = SkillWGData.Instance:CanSkillDistance(skill_id)
	local pos = self.draw_obj.root_transform.position + self.draw_obj.root_transform.forward * dis
	if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and Scene.Instance:IsEnemy(GuajiCache.target_obj) then
		local tar_pos = GuajiCache.target_obj:GetLuaPosition()
		local tar_dir = u3dpool.v3Normalize(u3dpool.v3Sub(tar_pos, self.draw_obj.root_transform.position))
		pos = self.draw_obj.root_transform.position + Vector3(tar_dir.x * dis, tar_dir.y * dis, tar_dir.z * dis)
	end
	-- if obj ~= nil and not obj:IsDeleted() and Scene.Instance:IsEnemy(obj) then
	-- 	local tar_pos = obj:GetLuaPosition()
	-- 	local tar_dir = u3dpool.v3Normalize(u3dpool.v3Sub(tar_pos, self.draw_obj.root_transform.position))
	-- 	pos = self.draw_obj.root_transform.position + Vector3(tar_dir.x * dis, tar_dir.y * dis, tar_dir.z * dis)
	-- end

	local attack_target_pos_x, attack_target_pos_y = GameMapHelper.WorldToLogic(pos.x, pos.z)
	local real_skill_index = SkillWGData.Instance:GetRealSkillIndex(skill_id, true) or 0
	-- self.is_specialskill = self.is_specialskill or RoleWGData.Instance.role_vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ
	FightWGCtrl.SendPerformSkillReq(
				real_skill_index,
				self.attack_index,
				attack_target_pos_x,
				attack_target_pos_y,
				65535,
				self.is_specialskill,
				self_pos.x,
				self_pos.y)
end

-- 攻击 是否可攻击
function MainRole:CanAttack(skill_id)
	if self:IsRealDead() or self:IsJump() or self:HasCantAttackBuff(skill_id) or
		CgManager.Instance:IsCgIng() then
		return false
	end

	if self:IsAtkPlaying() then
		return false
	end

	return true
end

function MainRole:OnAnimatorBegin(param, state_info)
	-- print_error("OnAnimatorBegin--------------")
	Role.OnAnimatorBegin(self, param, state_info)
	if self.attack_skill_id ~= SkillWGData.Skill_Id_285
		and self.attack_skill_id ~= HotSpringWGData.XUEQIU_SKILL
		and self.attack_skill_id ~= HotSpringWGData.SHUIQIU_SKILL
		and self.attack_skill_id ~= SkillWGData.Skill_Id_11304 then
		self:SendFight()
	end
end

-- 背刺 check
function MainRole:CheckBlackSkill(skill_id, x, y, skill_type)
	local cfg = SkillWGData.Instance:GetAfterSkillCfgById(skill_id)
	if cfg ~= nil and (skill_type == nil or skill_type == ATTACK_SKILL_TYPE.NONE) then
		if cfg.skill_type == BLACK_SKILL_TYPE.BACKSTAB_BACK then
			local draw_obj = self:GetDrawObj()
			if draw_obj ~= nil then
				local bundle_name = "effects2/prefab/mingjiang/10031_prefab"
				local asset_name = "10031_Mingjiang_attack2_01"
				EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, draw_obj:GetTransfrom().position)
			end	
					
			self:TryBackstab(skill_id, x, y, skill_type, SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_BLACK_BACKSTAB)
		end
	end
end

-- 技能 有些技能有前置流程，先检查一下
function MainRole:CheckFrontSkill(skill_id, x, y, target_obj_id, skill_type)
	local is_front = false
	local cfg = SkillWGData.Instance:GetBeforeSkillCfg(skill_id)
	if cfg ~= nil and skill_type == ATTACK_SKILL_TYPE.NONE then
		if cfg.skill_type == FRONT_SKILL_TYPE.TWINKLE then
			if not self:GetIsInSpecialState(OBJ_SPECIAL_STATE.TWINKLE) then
				-- -- 播放落地特效
				-- local draw_obj = self:GetDrawObj()
				-- if draw_obj ~= nil then
				-- 	local bundle_name, asset_name = ResPath.GeBufftEffect("effect_Buff_shanxian_001")
				-- 	EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, draw_obj:GetTransfrom().position)
				-- end
				
				self:SetSpecialStateInfo(OBJ_SPECIAL_STATE.TWINKLE, 1)
				local limit_x, limit_y = self:GetMovePosByLimit(x, y)

				local target_obj = Scene.Instance:GetObj(target_obj_id)
				if target_obj then
					-- 验证闪现点，随机闪现点
					limit_x, limit_y = self:GetTwinkleRandomPos(skill_id, target_obj, limit_x, limit_y)
				end

				FightWGCtrl.Instance:SendChongfengReq(limit_x, limit_y, SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_SHANSHUO)
			end

			is_front = true
		elseif cfg.skill_type == FRONT_SKILL_TYPE.ACCUMULATION then
			if not self:GetIsInSpecialState(OBJ_SPECIAL_STATE.ACCUMULATION) then
				-- 播放落地特效
				-- self:CrossAction(SceneObjPart.Main, move_action)
				self:StopMove()
				self:SetSpecialStateInfo(OBJ_SPECIAL_STATE.ACCUMULATION, 2)
				local real_skill_index = SkillWGData.Instance:GetRealSkillIndex(skill_id, true) or 0
				FightWGCtrl.Instance:SendPerFormCharge(x, y, real_skill_index)
			end

			is_front = true
		elseif cfg.skill_type == FRONT_SKILL_TYPE.BACKSTAB then
			self:TryBackstab(skill_id, x, y, skill_type, SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_BACKSTAB)
			is_front = true
		end
	end

	return is_front
end

-- 闪现随机点获取
--[[
	前提：默认闪现范围是以玩家为圆心的圆
	技能尝试释放位置：try_pos_x, try_pos_y
]]
function MainRole:GetTwinkleRandomPos(skill_id, target_obj, try_pos_x, try_pos_y)
	if not target_obj then
		-- print_error("---没目标--")
		return try_pos_x, try_pos_y
	end
	
	local target_body_range = 2 -- 目标体积半径
	if target_obj:GetType() == SceneObjType.Monster then
		target_body_range = BossWGData.Instance:GetMonsterRangeByid(target_obj.vo.monster_id)
	end

	-- print_error("--目标体积半径--", target_obj.vo.monster_id, target_body_range, target_obj.vo.obj_type)
	local play_pos_x, play_pos_y = self:GetLogicPos()
	local cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
	if cfg == nil then
		-- print_error("---没技能配置--")
        return play_pos_x, play_pos_y
    end

	local skill_range = cfg.attack_range -- 技能半径
	if target_body_range >= skill_range then
		-- print_error(string.format("怪物%s的体积配置比闪现技能%s的半径%s大", target_obj.vo.monster_id, skill_id, skill_range))
		return play_pos_x, play_pos_y
	end

	local target_pos_x, target_pos_y = target_obj:GetLogicPos()

	local play_pos = {x = play_pos_x, y = play_pos_y}
	local target_pos = {x = target_pos_x, y = target_pos_y}

	local play_to_target_len_sqr = u3d.v2Length(u3d.v2Sub(target_pos, play_pos), false)
	local play_to_target_len = math.ceil(math.sqrt(play_to_target_len_sqr))
	local body_len_sqr = target_body_range * target_body_range
	local skill_len_sqr = skill_range * skill_range

	-- 检查点的合法性
	local function CheckPosVaild(check_x, check_y)
		-- print_error("---检查点--", check_x, check_y)
		-- 检查到玩家点的距离
        local dxA = check_x - play_pos_x
        local dyA = check_y - play_pos_y
        if dxA*dxA + dyA*dyA > skill_len_sqr then
			-- print_error("---超出技能范围---", check_x, check_y)
            return false  -- 闪现超出技能范围
        end

        -- 检查到怪物点的距离
        local dxB = check_x - target_pos_x
        local dyB = check_y - target_pos_y
        if dxB*dxB + dyB*dyB < body_len_sqr then
			-- print_error("---进入怪物体积范围---", check_x, check_y)
            return false  -- 进入怪物体积范围
        end

        if AStarFindWay:IsBlock(check_x, check_y) then
			-- print_error("---是障碍点---", check_x, check_y)
            return false  -- 障碍点
        end

		return true
	end

	if CheckPosVaild(try_pos_x, try_pos_y) then
		-- print_error("---技能点有效--", try_pos_x, try_pos_y)
		return try_pos_x, try_pos_y
	end

	----[[随机点范围，缩小范围，尽可能在玩家的前方取点
	local random_min_x, random_max_x, random_min_y, random_max_y
	local pos_x_sub = play_pos_x - target_pos_x
	local min_x = play_pos_x - skill_range
	local max_x = play_pos_x + skill_range
	if pos_x_sub < 0 then
		random_min_x = target_pos_x - play_to_target_len
		random_max_x = math.min(target_pos_x + play_to_target_len, max_x)
	else
		random_min_x = math.max(target_pos_x - play_to_target_len, min_x)
		random_max_x = target_pos_x + play_to_target_len
	end

	local pos_y_sub = play_pos_y - target_pos_y
	local min_y = play_pos_y - skill_range
	local max_y = play_pos_y + skill_range
	if pos_y_sub < 0 then
		random_min_y = target_pos_y - play_to_target_len
		random_max_y = math.min(target_pos_y + play_to_target_len, max_y)
	else
		random_min_y = math.max(target_pos_y - play_to_target_len, min_y)
		random_max_y = target_pos_y + play_to_target_len
	end
	--]]

	if random_min_x > random_max_x or random_min_y > random_max_y then
		-- print_error("---随机点不对劲--", play_pos_x, play_pos_y)
        return play_pos_x, play_pos_y
    end

	-- 随机点
	local random_pos_list = {}
	for x = random_min_x, random_max_x do
		for y = random_min_y, random_max_y do
			if CheckPosVaild(x, y) then
				table.insert(random_pos_list, {x = x, y = y})
			end
		end
	end

	if #random_pos_list > 0 then
        local tal = random_pos_list[math.random(1, #random_pos_list)]
		-- print_error("---随机点--", #random_pos_list, tal.x, tal.y)
        return tal.x, tal.y
    end

	-- print_error("---非随机点--", play_pos_x, play_pos_y, target_pos_x, target_pos_y, play_to_target_len, target_body_range)
	-- print_error("---非随机点2--", random_min_x, random_max_x, random_min_y, random_max_y)
	return play_pos_x, play_pos_y
end

-- 背刺
function MainRole:TryBackstab(skill_id, x, y, skill_type, send_type)
	local cfg = nil
	if send_type ~= nil then
		if send_type == SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_BACKSTAB then
			cfg = SkillWGData.Instance:GetBeforeSkillCfg(skill_id)
		elseif send_type == SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_BLACK_BACKSTAB then
			cfg = SkillWGData.Instance:GetAfterSkillCfgById(skill_id)
		end
	end

	if cfg == nil then
		return
	end

	if not self:GetIsInSpecialState(OBJ_SPECIAL_STATE.BACKSTAB) then
		-- 播放落地特效
		-- self:CrossAction(SceneObjPart.Main, move_action)
		self:StopMove()
		self:SetSpecialStateInfo(OBJ_SPECIAL_STATE.BACKSTAB, 2)
		local back_x = x
		local back_y = y
		local back_stab_obj_id = -1
		-- local real_skill_index = SkillWGData.Instance:GetRealSkillIndex(skill_id, true) or 0
		if nil ~= GuajiCache.target_obj and not GuajiCache.target_obj:IsDeleted() and Scene.Instance:IsEnemy(GuajiCache.target_obj) then
			local dir = GuajiCache.target_obj:GetLogicDir()
			local back_dir = u3dpool.v2Mul(dir, -1)
			local t_x, t_y = GuajiCache.target_obj:GetLogicPos()
			local calc_x = t_x + back_dir.x * cfg.param
			local calc_y = t_y + back_dir.y * cfg.param

			if not AStarFindWay:IsBlock(calc_x, calc_y) then
				back_x = calc_x
				back_y = calc_y
				back_stab_obj_id = GuajiCache.target_obj:GetObjId()
			end
		else
			local min_hp = 0
			local min_hp_obj = nil
			local role_list = Scene.Instance:GetRoleList()
			if role_list ~= nil then
				for k, v in pairs(role_list) do
					if not v:IsMainRole() and not v:IsDeleted() and Scene.Instance:IsEnemy(v) and Scene.Instance:GetObjIsInSkillRange(skill_id, v, x, y) then
						if min_hp_obj == nil then
							min_hp_obj = v
							min_hp = v:GetHpPercent()
						else
							local cur_hp = v:GetHpPercent()
							if min_hp > cur_hp then
								min_hp_obj = v
								min_hp = cur_hp
							end
						end
					end
				end
			end

			if min_hp_obj == nil then
				local monster_list = Scene.Instance:GetMonsterList()
				for k, v in pairs(monster_list) do
					if not v:IsDeleted() and Scene.Instance:IsEnemy(v) and Scene.Instance:GetObjIsInSkillRange(skill_id, v, x, y) then
						if min_hp_obj == nil then
							min_hp_obj = v
							min_hp = v:GetHpPercent()
						else
							local cur_hp = v:GetHpPercent()
							if min_hp > cur_hp then
								min_hp_obj = v
								min_hp = cur_hp
							end
						end
					end
				end						
			end

			if min_hp_obj ~= nil then
				local dir = min_hp_obj:GetLogicDir()
				local back_dir = u3dpool.v2Mul(dir, -1)
				local t_x, t_y = min_hp_obj:GetLogicPos()
				local calc_x = t_x + back_dir.x * cfg.param
				local calc_y = t_y + back_dir.y * cfg.param
				if not AStarFindWay:IsBlock(calc_x, calc_y) then
					back_x = calc_x
					back_y = calc_y
					back_stab_obj_id = min_hp_obj:GetObjId()
				end	
			
			end
		end
		
		local limit_x, limit_y = self:GetMovePosByLimit(back_x, back_y)
		limit_x = math.floor(limit_x)
		limit_y = math.floor(limit_y)
		send_type = send_type or SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_BACKSTAB
		FightWGCtrl.Instance:SendChongfengReq(limit_x, limit_y, send_type, back_stab_obj_id, skill_id)
	end
end

-- 战斗 发送
function MainRole:SendFight(is_chongci_skill)
	if self:IsAtkPlaying()
		or is_chongci_skill
		or SkillWGData.IsPetSkill(self.attack_skill_id)
		-- or TianShenWGData.Instance:IsJiBanSkill(self.attack_skill_id)
		or self:GetIsIgnoreAnimSkill() then
		if self.attack_use_type == nil then
			print_error("Use Skill Error, use_type is nil", self.attack_skill_id, self.attack_index)
			return
		end

		GuajiWGCtrl.Instance:ClearAtkCache()
		local scene_obj = self.attack_target_obj

		if self.attack_use_type == ATTACK_USE_TYPE.GUAJI then
			if not self.attack_target_obj or not Scene.Instance:IsEnemy(self.attack_target_obj) then
				self:SetAttackTarget()
				return
			end
			-- 如果对方有问题，则找个附近的攻击
			if not scene_obj or scene_obj:IsDeleted() or not scene_obj:IsCharacter() or scene_obj:IsRealDead() then
				-- 直接使用挂机的，会不会选择了太远的目标？
				scene_obj = GuajiWGCtrl.Instance:SelectAtkTarget(true)
			end

			if nil ~= scene_obj and scene_obj:IsCharacter() and not scene_obj:IsRealDead() then
				self.last_skill_id = self.attack_skill_id
				self.atk_is_hit[self.attack_skill_id] = false
				-- local is_team = RoleSkillData.Instance and RoleSkillData.Instance:CheckIsTeamSkill(self.attack_skill_id)
				-- local is_guild_fb = Scene.Instance:GetSceneType() == SceneType.GuideFb
				local target_robert = RobertManager.Instance:GetRobert(scene_obj:GetObjId())
				-- --组队技能是以自己为目标的，可是在一开始，会创建一个主角的机器人，在使用组队技能时会进入下面的逻辑，所以在不是假副本的情况下，组队技能不进入下面的逻辑
				if nil ~= target_robert and not self:IsRobot() then -- 与机器人的战斗不通过服务器
					local attack_robert = RobertManager.Instance:GetRobert(self:GetObjId())
					RobertManager.Instance:ReqFight(attack_robert, target_robert, self.attack_skill_id, self.attack_index)
					self.last_skill_index = self.attack_index
					return
				end
				local self_pos = u3d.vec2(self:GetLogicPos())

				-- self.is_specialskill = self.is_specialskill or RoleWGData.Instance.role_vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ
				self.last_skill_index = self.attack_index
				local real_skill_index = SkillWGData.Instance:GetRealSkillIndex(self.attack_skill_id, true) or 0
				if self:IsRobot() then
					return
				end

				local obj_id = scene_obj:GetObjId()
				local send_x = self.attack_target_pos_x
				local send_y = self.attack_target_pos_y
				local cfg = SkillWGData.Instance:GetAfterSkillCfgById(self.attack_skill_id)
				if cfg ~= nil then
					if cfg.skill_type == BLACK_SKILL_TYPE.BACKSTAB_BACK then
						send_x = self.logic_pos.x
						send_y = self.logic_pos.y
						obj_id = COMMON_CONSTS.INVALID_OBJID
					end
				end

				FightWGCtrl.SendPerformSkillReq(
					real_skill_index,
					self.attack_index,
					send_x,
					send_y,
					obj_id,
					self.is_specialskill,
					self_pos.x,
					self_pos.y)

				self:CheckBlackSkill(self.attack_skill_id, self.attack_target_pos_x, self.attack_target_pos_y, self.attack_skill_type)
			end
		else
			self.last_skill_id = self.attack_skill_id
			self.atk_is_hit[self.attack_skill_id] = false

			local obj_id = COMMON_CONSTS.INVALID_OBJID
			if scene_obj and scene_obj:IsCharacter() and not scene_obj:IsDeleted() and not scene_obj:IsRealDead() then
				local target_robert = RobertManager.Instance:GetRobert(scene_obj:GetObjId())
				if nil ~= target_robert and not self:IsRobot() then -- 与机器人的战斗不通过服务器
					local attack_robert = RobertManager.Instance:GetRobert(self:GetObjId())
					RobertManager.Instance:ReqFight(attack_robert, target_robert, self.attack_skill_id, self.attack_index)
					self.last_skill_index = self.attack_index
					return
				end

				obj_id = scene_obj:GetObjId()
			end

			if obj_id == COMMON_CONSTS.INVALID_OBJID then
				if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and Scene.Instance:IsEnemy(GuajiCache.target_obj) then
					obj_id = GuajiCache.target_obj:GetObjId()
				end
			end

			local self_pos = u3d.vec2(self:GetLogicPos())

			-- self.is_specialskill = self.is_specialskill or RoleWGData.Instance.role_vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ
			self.last_skill_index = self.attack_index
			local real_skill_index = SkillWGData.Instance:GetRealSkillIndex(self.attack_skill_id, true) or 0
			if self:IsRobot() then 
				return
			end

			local send_x = self.attack_target_pos_x
			local send_y = self.attack_target_pos_y
			local cfg = SkillWGData.Instance:GetAfterSkillCfgById(self.attack_skill_id)
			if cfg ~= nil then
				if cfg.skill_type == BLACK_SKILL_TYPE.BACKSTAB_BACK then
					send_x = self.logic_pos.x
					send_y = self.logic_pos.y
					obj_id = COMMON_CONSTS.INVALID_OBJID
				end
			end

			FightWGCtrl.SendPerformSkillReq(
				real_skill_index,
				self.attack_index,
				send_x,
				send_y,
				obj_id,
				self.is_specialskill,
				self_pos.x,
				self_pos.y)

			self:CheckBlackSkill(self.attack_skill_id, self.attack_target_pos_x, self.attack_target_pos_y, self.attack_skill_type)
		end
	end
end

-- 战斗状态 进入
function MainRole:EnterFightState()
	Role.EnterFightState(self)
end

-- 战斗状态 退出
function MainRole:LeaveFightState()
	Role.LeaveFightState(self)
	RoleWGCtrl.Instance:RemoveSuitEquipCoinDelay()
end

-- 技能 处理
function MainRole:OnSkillHandle(skill_id)
	local scene_obj = self.attack_target_obj
	if nil ~= scene_obj and scene_obj:IsCharacter() and not scene_obj:IsRealDead() and not self:IsRobot() then
		FightWGCtrl.SendPerformSkillReq(
		SkillWGData.Instance:GetRealSkillIndex(skill_id, true),
		self.attack_index,
		self.attack_target_pos_x,
		self.attack_target_pos_y,
		scene_obj:GetObjId(),
		self.is_specialskill,
		self.logic_pos.x,
		self.logic_pos.y)
	end
end

-- 触发受击
function MainRole:OnAnimatorHit()
	if self:IsAtk() then
		self.atk_is_hit[self.attack_skill_id] = true

		-- if self:CanAttack() then
			-- local info_cfg = SkillWGData.GetSkillinfoConfig(self.attack_skill_id)
			-- if info_cfg ~= nil then
			-- 	-- if info_cfg.hit_count > 1 then
			-- 	-- 	self.attack_index = self.attack_index + 1
			-- 	-- 	if self.attack_index > info_cfg.hit_count then
			-- 	-- 		self.attack_index = 1
			-- 	-- 	end
			-- 	-- end
			-- end
		-- end
	end
	Role.OnAnimatorHit(self)
end

-- 动画播放结束后
function MainRole:OnAnimatorEnd()
	if self:IsAtk() then
		self.last_atk_end_time = Status.NowTime
	end
	Role.OnAnimatorEnd(self)
end

-- 攻击 当攻击打中
function MainRole:OnAttackHit(attack_skill_id, attack_target_obj)
	Role.OnAttackHit(self, attack_skill_id, attack_target_obj)

	local scene_obj = attack_target_obj
	if nil ~= scene_obj and not scene_obj:IsDeleted() and scene_obj:IsCharacter() and nil ~= self.vo then
		local deliverer = Scene.Instance:GetObj(self.vo.obj_id)
		scene_obj:DoBeHitShow(
			deliverer, attack_skill_id, scene_obj:GetObjId())
	end
end

-- 最后一次技能id
function MainRole:GetLastSkillId()
	return self.last_skill_id
end

-- 攻击 技能是否打中
function MainRole:AtkIsHit(skill_id)
	-- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(skill_id)
	return self.atk_is_hit and self.atk_is_hit[normal_skill or skill_id]
end

-- 最后一次攻击的结束时间
function MainRole:GetLastAtkEndTime()
	return self.last_atk_end_time
end

-- 获取最新技能的index
function MainRole:GetLastSkillIndex()
	return self.last_skill_index
end

-- 冲锋
function MainRole:IsChongfenging()
	return self.chongfeng_req_time > 0 or self.is_special_move
end

-- 冲锋 请求冲锋到目标
function MainRole:ReqChongfengToObj(pos_x, pos_y, end_func, check_dis, target_x, target_y)
	-- print_error("----请求冲锋到目标----", pos_x, pos_y, target_x, target_y)
	local is_on_chongfeng = self:GetIsSpecialMove()
	if is_on_chongfeng or self.chongfeng_req_time > 0 then
		return false
	end

	pos_x, pos_y = self:GetMovePosByLimit(pos_x, pos_y)
	local real_pos = u3d.vec2(pos_x, pos_y)
	check_dis = check_dis or 4

	local real_end_pos = GameMapHelper.GetRealChongFengLogicPos(self.real_pos, real_pos, check_dis * 0.5)

	if nil == real_end_pos or self.logic_pos.x == real_end_pos.x and self.logic_pos.y == real_end_pos.y then
		return false
	end

	if self:IsRidingNoFightMount() then
		MountWGCtrl.Instance:SendMountGoonReq(0)
	end

	self.move_cache_on_chongefeng_end = nil
	self.wait_send_dis = 0
	self:ClearPathInfo()
	self:SendMoveReq()
	self:ChangeToCommonState()

	self.chongfeng_req_time = Status.NowTime
	self.chongfeng_callback = end_func
	local x = real_end_pos.x
	local y = real_end_pos.y

	if check_dis ~= nil and target_x ~= nil and target_y ~= nil then
	    local self_x, self_y = self:GetLogicPos()
	    local targetModleR = check_dis * 0.5

	    if GuajiWGCtrl.CheckRange(x, y, targetModleR - 1, target_x, target_y) then -- 不在目标体内打
	        x, y = AStarFindWay:GetRangeVaildXY(x, y, self_x, self_y, targetModleR)
	    end
	end

	-- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
	local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(AtkCache.skill_id)
	-- 一些技能需要等待冲锋完成再释放技能
	local before_skill_cfg = SkillWGData.Instance:GetBeforeSkillCfg(normal_skill or AtkCache.skill_id)
	if before_skill_cfg and before_skill_cfg.skill_type == FRONT_SKILL_TYPE.CHARGE then
		GuajiWGCtrl.SetAtkValid(false)
	end

	FightWGCtrl.Instance:SendChongfengReq(x, y, SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_CHONGFENG)
	return true
end

-- 冲锋 特殊移动 end
function MainRole:OnSpecialMoveEnd()
	if nil ~= self.chongfeng_callback then
		self.chongfeng_callback(self)
		self.chongfeng_callback = nil
	end

	if self.move_cache_on_chongefeng_end ~= nil then
		self:DoMoveOperate(self.move_cache_on_chongefeng_end.x
						, self.move_cache_on_chongefeng_end.y
						, self.move_cache_on_chongefeng_end.range
						, self.move_cache_on_chongefeng_end.arrive_func
						, self.move_cache_on_chongefeng_end.is_auto_move
						, self.move_cache_on_chongefeng_end.ignore_high_area
						, self.move_cache_on_chongefeng_end.move_reason
						, self.move_cache_on_chongefeng_end.ignore_block)
		self.move_cache_on_chongefeng_end = nil
	end

	Role.OnSpecialMoveEnd(self)
end







-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    跳跃     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
function MainRole:FixBeforeCreateMainRoleSpecialAction()
	local sp_data = LoginWGCtrl.Instance and LoginWGCtrl.Instance:GetMainRoleInSpecialActionData()
	if not sp_data then
		return
	end

	LoginWGCtrl.Instance:ClearMainRoleInSpecialAction()
	if sp_data.is_jump then
		self.cur_jumping_type = sp_data.cur_jumping_type
		self.target_jumping_id = sp_data.target_jumping_id
		self.target_x = sp_data.target_x
		self.target_y = sp_data.target_y
		if self.cur_jumping_type == JUMP_POINT_TYPE.JUMP then
			self:OnJumpEnd()
		elseif self.cur_jumping_type == JUMP_POINT_TYPE.RIDER_SWORD then
			self.draw_obj:SetMoveCallback(nil)
			self:OnRiderSwordToEnd(true)
		end
	end
end

local jump_cost_time
-- 跳跃 开始
function MainRole:OnJumpStart()
	Role.OnJumpStart(self)
	if self:IsDeleted() or self.target_point == nil then
		return
	end
	if self.target_x == nil or self.target_y == nil then
		return
	end

	jump_cost_time = Status.NowTime
	self:RemoveModel(SceneObjPart.Mount)
	-- self:RemoveModel(SceneObjPart.FightMount)

	local x, y = self:GetLogicPos()
	local jump_speed_factor = 1
	local distance = u3d.v2Length({x = self.target_x - x, y = self.target_y - y}, true)

	local speed = self:GetMoveSpeed(true)
	if speed == 0 then
		speed = 0.01
	end

	local time = distance / speed --得到跳跃实际需要的时间
	if time == 0 then
		time = 0.01
	end

	local anim_time_cfg
	if self.vo.jump_act == 2 then
		anim_time_cfg = self:GetActionTimeRecord(SceneObjAnimator.Jump2)
	else
		anim_time_cfg = self:GetActionTimeRecord(SceneObjAnimator.Jump)
	end

	if anim_time_cfg then
		local anim_time = anim_time_cfg.time
		jump_speed_factor = anim_time / time * 0.7 -- 动画截断值，动作里有很多落地收尾的动作
	end
	
	if not self.is_jump then
		self:SetJump(true)
		--[[ 2021/8/12 屏蔽跳跃鬼魂特效
		local interval = 0.1
		local time = distance / self:GetMoveSpeed(true)
		local max_num = math.min(time / interval, 10)
		self:ShowGhost(0, max_num, 7, time / max_num)
		]]
	end

	self.fixed_jump_stuck_time_data = {
		jump_type = JUMP_POINT_TYPE.JUMP,
		time = Status.NowTime + 5,
	}

	self.jump_special_param = nil
	local scene_id = Scene.Instance:GetSceneId()
	-- print_error(string.format("跳跃  场景id=%s  目标跳跃点=%s  预计需要时间=%s", scene_id, self.target_point.vo.id, time))
	-- print_error(string.format("跳跃动作播放速度倍率=%s", jump_speed_factor))
	local point_special_param = (JUMP_SPECAIL_PARAM[scene_id] or {})[self.target_point.vo.id]
	if point_special_param then
		local draw_obj = self:GetDrawObj()
		if draw_obj ~= nil then
			self.jump_special_param = {}
			self.jump_special_param.cur_y = 0
			self.jump_special_param.up_time = Status.NowTime + point_special_param.up_time
			self.jump_special_param.down_time = self.jump_special_param.up_time
			self.jump_special_param.up_len = point_special_param.up_time
			self.jump_special_param.down_len = time - point_special_param.up_time
			self.jump_special_param.old_offset_pos = draw_obj:GetOffset()
			self.jump_special_param.y = point_special_param.y
			self.jump_special_param.total_time = time
			self.jump_special_param.end_time = Status.NowTime + time + 0.1
		end
	end

	Role.DoMove(self, self.target_x, self.target_y)

	if not self:IsSpecialJump() then
		self:SendMoveReq()
	end

	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	local weapon_part = self.draw_obj:GetPart(SceneObjPart.Weapon)
	if self:IsRiding() then
		main_part:SetFloat("jump_speed", jump_speed_factor)
		local mount_part_type = self:GetCurRidingPart()
		local mount_part = self.draw_obj:GetPart(mount_part_type)
		if mount_part then
			mount_part:SetFloat("jump_speed", jump_speed_factor)
		end

		if weapon_part then
			weapon_part:SetFloat("jump_speed", jump_speed_factor)
		end
	else
		local value = 1
		-- 变身状态
		if self.vo.bianshen_param == BIANSHEN_EFEECT_APPEARANCE.APPEARANCE_DATI_XIAOTU then
			value = 2
		elseif self.vo.bianshen_param == BIANSHEN_EFEECT_APPEARANCE.APPEARANCE_DATI_XIAOZHU then
			value = 2.67
		end

		main_part:SetFloat("jump_speed", value * jump_speed_factor)

		if weapon_part then
			weapon_part:SetFloat("jump_speed", value * jump_speed_factor)
		end
	end

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Jump))
end

-- 跳跃 结束
function MainRole:OnJumpEnd()
	if self.jump_special_param ~= nil then
		local old_offset_pos = self.jump_special_param.old_offset_pos
		local draw_obj = self:GetDrawObj()
		if draw_obj ~= nil then
			draw_obj:SetOffset(Vector3(old_offset_pos.x, old_offset_pos.y, old_offset_pos.z))
		end
	end

	self.fixed_jump_stuck_time_data = nil
	if self.target_jumping_id then
		self.vo.jumping_id = self.target_jumping_id
		self.target_jumping_id = nil
	else
		self.vo.jumping_id = -1
	end

	self.cur_jumping_type = nil
	self.jump_special_param = nil
	self.is_special_jump = false
	self:SetJump(false)
	Role.OnJumpEnd(self)

	-- 播放落地特效
	local root = self.draw_obj.root
	local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_yujian_jump")
	EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, root.transform.position)
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Down))

	-- print_error("跳跃结束  耗时=", Status.NowTime - jump_cost_time)
	if self.jump_call_back then
		self.jump_call_back()
		self.jump_call_back = nil
	end
end

-- 跳跃 跳跃到目的地
function MainRole:JumpTo(point_vo, target_point, next_point, call_back)
	if target_point == nil then
		return
	end

	self.vo.jump_factor = 1
	if point_vo.jump_speed and point_vo.jump_speed > 4 then
		point_vo.jump_speed = 4
	end

	self.vo.jumping_id = point_vo.id
	self.vo.jump_factor = point_vo.jump_speed
	-- 同步一下跳跃速度，让跳跃不受玩家移动速度影响
	self.jump_speed = point_vo.jump_speed * 5
	local scene_id = Scene.Instance:GetSceneId()

	local point_special_param = (JUMP_SPECAIL_PARAM[scene_id] or {})[target_point.vo.id]
	if point_special_param then
		if point_special_param.jump_time ~= nil and point_special_param.jump_time ~= 0 then
			local old_speed = self:GetMoveSpeed(false, true)
			local x, y = self:GetLogicPos()
			local distance = u3d.v2Length({x = target_point.vo.pos_x - x, y = target_point.vo.pos_y - y}, true)
			local need_speed = distance / point_special_param.jump_time
			self.vo.jump_factor = need_speed / old_speed
		end
	end

	self.jump_call_back = call_back
	self.target_point = target_point
	self.next_point = next_point
	self.target_x = target_point.vo.pos_x
	self.target_y = target_point.vo.pos_y
	self.target_jumping_id = target_point.vo.id
	self.jump_tong_bu = point_vo.jump_tong_bu

	GlobalEventSystem:Fire(OtherEventType.JUMP_STATE_CHANGE, true)
	self:ToJumpPath(point_vo.range)

	-- 播放CG
	if point_vo.play_cg and point_vo.play_cg == 1 and not IsLowMemSystem and not CgManager.Instance:IsCgIng() then
		for k,v in pairs(point_vo.cgs) do
			local role_sex = self.vo.sex
			local role_prof = self.vo.prof % 10
			if (v.prof % 10) == role_prof and v.sex == role_sex then
				self:RemoveModel(SceneObjPart.Mount)
				-- self:RemoveModel(SceneObjPart.FightMount)
				CgManager.Instance:Play(BaseCg.New(v.bundle_name, v.asset_name), function()
					if self.draw_obj == nil then
						return
					end
					local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
					Scene.SendSyncJump(Scene.Instance:GetSceneId(), v.position.x, v.position.y, scene_key)
					self:SetLogicPos(v.position.x, v.position.y)
					local game_obj = self:GetDrawObj():GetRoot()
					game_obj.transform.localRotation = Quaternion.Euler(0, v.rotation, 0)
					-- 摄象机直接同步到角色，不缓动
					if not IsNil(MainCameraFollow) then
						MainCameraFollow:SyncImmediate()
					end

					self:UpdateMount()
					self:SetJump(false)

					self:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
					-- self.vo.move_mode = MOVE_MODE.MOVE_MODE_NORMAL
					GlobalEventSystem:Fire(OtherEventType.JUMP_STATE_CHANGE, false)
				end, nil, true)

				return
			end
		end
	end

	self.cur_jumping_type = point_vo.jump_type
	-- 跳跃
	if point_vo.jump_type == JUMP_POINT_TYPE.JUMP then
		self.is_special_jump = true
		self:ChangeMoveMode(MOVE_MODE.MOVE_MODE_JUMP1)
		Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_JUMP1)
		local jump_act = point_vo.jump_act
		-- 随机 目前只有2个动作
		if jump_act == 0 then
			jump_act = math.random(1, 2)
		end

		self.vo.jump_act = jump_act
		self:DoJump()
	-- 飞行器
	elseif point_vo.jump_type == JUMP_POINT_TYPE.AIRCRAFT then
		Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_JUMP2, point_vo.air_craft_id)
		self.vo.move_mode_param = point_vo.air_craft_id
		self:DoJump(point_vo.air_craft_id)
	-- 御剑
	elseif point_vo.jump_type == JUMP_POINT_TYPE.RIDER_SWORD then
		self:OnRiderSwordStart()
	end

	if self.move_oper_cache2 then
		for k,v in pairs(self.move_oper_cache2.jumppoint_obj_list) do
			if v.vo.id == point_vo.id then
				self.move_oper_cache2 = nil
				self:ClearPathInfo()
				break
			end
		end
	end
end

-- 跳跃时的路线
function MainRole:ToJumpPath(jump_range)
	local check_range = jump_range or 5
	local path_count = #self.path_pos_list
	if path_count > 1 then
		local x = self.path_pos_list[path_count].x
		local y = self.path_pos_list[path_count].y
		if GameMath.GetDistance(self.logic_pos.x, self.logic_pos.y, x, y) > check_range * check_range then
			local jumppoint_obj_list = Scene.Instance:FindJumpPoint(x, y)
			self.move_oper_cache2 = {x = x
									, y = y
									, range = 0
									, arrive_func = self.arrive_func
									, jumppoint_obj_list = jumppoint_obj_list
									, is_auto_move = self.is_auto_move
									, ignore_block = self.astar_ignore_block
									, move_reason = self.follow_move_reason}
		end
	end

	self.arrive_func = nil
	self:ClearPathInfo()
end

-- 跳跃 清除缓存
function MainRole:ClearJumpCache()
	self.jump_call_back = nil
	self.move_oper_cache2 = nil
end

-- 特殊跳跃
function MainRole:IsSpecialJump()
	return self.is_special_jump
end


function MainRole:ChangeRiderSwordSate()
	-- elseif self.rider_sword_state == ActionStep.Start then
	-- 	self:OnRiderSwordToKeeping()
	if self.rider_sword_state == ActionStep.Keep then
		self:OnRiderSwordToReadyEnd()
	-- elseif self.rider_sword_state == ActionStep.ReadyToEnd then
	-- 	self:OnRiderSwordToEnd()
	end
end

-- 御剑开始
function MainRole:OnRiderSwordStart()
	if self.rider_sword_state == ActionStep.Start then
		return
	end

	-- print_error("---御剑开始---")
	if self:IsDeleted() or self.target_point == nil then
		return
	end

	if self.target_x == nil or self.target_y == nil then
		return
	end

	if not self.is_jump then
		self:SetJump(true)
	end

	if not self:IsMove() then
		self:ChangeState(SceneObjState.Move)
	end

	self:UpdateMount()
	self.rider_sword_state = ActionStep.Start
	self:RemoveModel(SceneObjPart.Mount)
	-- self:RemoveModel(SceneObjPart.FightMount)
	self:ChangeMoveMode(MOVE_MODE.MOVE_MODE_FLY)

	local camera_move_dis = 16
	if Scene.Instance:GetCameraDis() < camera_move_dis then
		-- main_role_jump_rider_sword_move_camera_dis_flag
		self.mr_jump_rs_mc_dis_flag = true
		Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.UP_MOUNT, nil, nil, camera_move_dis)
	end
	

	self.fixed_jump_stuck_time_data = {
		jump_type = JUMP_POINT_TYPE.RIDER_SWORD,
		time = Status.NowTime + 4,
	}

	local start_move_distance = 8
	local now_time = Status.NowTime
	local move_speed = 11
	local move_time = start_move_distance / move_speed

	self.rider_sword_move_time = nil
	self.add_rider_sword_carrier_time = nil
	self.jump_special_param = nil
	local scene_id = Scene.Instance:GetSceneId()
	local point_special_param = ((RIDER_SWORD_SPECAIL_PARAM[scene_id] or {})[self.target_point.vo.id]) or RIDER_SWORD_BASE_PARAM
	if point_special_param then
		start_move_distance = point_special_param.rw_pre_move_distance or start_move_distance
		move_speed = point_special_param.rw_pre_move_speed or move_speed
		move_time = start_move_distance / move_speed
		if move_time == 0 then
			move_time = 0.01
		end

		if point_special_param.add_carrier_time then
			self.add_rider_sword_carrier_time = now_time + point_special_param.add_carrier_time
		end

		move_time = point_special_param.rw_pre_total_do_time or 0.97
		self.rider_sword_move_time = now_time + move_time
		local draw_obj = self:GetDrawObj()
		if draw_obj ~= nil then
			local ready_up_time = point_special_param.ready_up_time or 0
			local up_time = point_special_param.up_time or 0
			self.jump_special_param = {}
			self.jump_special_param.cur_y = 0
			self.jump_special_param.y = point_special_param.y
			self.jump_special_param.up_len = up_time
			self.jump_special_param.up_time = now_time + up_time + ready_up_time
			self.jump_special_param.ready_up_time = now_time + ready_up_time
			-- self.jump_special_param.total_time = move_time
			-- self.jump_special_param.down_len = move_time - up_time
			-- self.jump_special_param.end_time = self.rider_sword_move_time
			self.jump_special_param.old_offset_pos = draw_obj:GetOffset()
		end
	end

	self:AddRiderSwordCarrier()
	self.move_end_pos.x, self.move_end_pos.y = GameMapHelper.LogicToWorld(self.target_x, self.target_y)
	local move_end_logic_pos = {x = self.target_x, y = self.target_y}
	local target_delta_pos = u3d.v2Sub(move_end_logic_pos, self.logic_pos)
	local target_dir = u3d.v2Normalize(target_delta_pos)
	local mov_dir = u3d.v2Mul(target_dir, start_move_distance)
	local new_pos = u3d.v2Add(self.logic_pos, mov_dir)
	self:SetDirectionByXY(new_pos.x, new_pos.y)
	local real_end_pos_x, real_end_pos_y = GameMapHelper.LogicToWorld(new_pos.x, new_pos.y)
	self.draw_obj:MoveTo(real_end_pos_x, real_end_pos_y, move_speed)
	self.draw_obj:SetMoveCallback(function ()
		self.rider_sword_move_time = Status.NowTime
		local position = self.draw_obj:GetRoot().gameObject.transform.position
		self:SetRealPos(position.x, position.z)
		self.draw_obj:SetMoveCallback(nil)
		self:OnRiderSwordToKeeping()
	end)

	-- local forward = self.draw_obj:GetRoot().gameObject.transform.forward
	-- local dir = math.atan2(forward.z, forward.x)
	-- Scene.SendMoveReq(dir, new_pos.x, new_pos.y, 0, 0)
	-- Role.DoMove(self, new_pos.x, new_pos.y)
	self:CrossAction(SceneObjPart.Main, SceneObjAnimator.RiderSwordUp, nil, 0)
end

-- 御剑中
function MainRole:OnRiderSwordToKeeping()
	if self.rider_sword_state == ActionStep.Keep then
		return
	end

	-- print_error("---御剑中---")
	if self:IsDeleted() or self.target_point == nil then
		return
	end

	if self.target_x == nil or self.target_y == nil then
		return
	end

	local cur_logic_pos_x, cur_logic_pos_y = self:GetLogicPos()
	local move_distance = u3d.v2Length({x = self.target_x - cur_logic_pos_x, y = self.target_y - cur_logic_pos_y}, true)
	local now_time = Status.NowTime
	local move_speed = 40
	local move_time = move_distance / move_speed
	self.rider_sword_move_time = now_time + move_time

	local scene_id = Scene.Instance:GetSceneId()
	local point_special_param = ((RIDER_SWORD_SPECAIL_PARAM[scene_id] or {})[self.target_point.vo.id]) or RIDER_SWORD_BASE_PARAM
	if point_special_param then
		local draw_obj = self:GetDrawObj()
		if draw_obj ~= nil then
			local cur_offest_pos = draw_obj:GetOffset()
			local old_pos = self.jump_special_param and self.jump_special_param.old_offset_pos or cur_offest_pos
			self.jump_special_param = nil
			self.jump_special_param = {}
			self.jump_special_param.cur_y = cur_offest_pos.y - old_pos.y
			self.jump_special_param.y = cur_offest_pos.y - old_pos.y

			move_speed = point_special_param.rw_keep_move_speed or move_speed
			move_time = move_distance / move_speed
			self.rider_sword_move_time = now_time + move_time

			local down_time = point_special_param.rw_down_time or 0
			self.jump_special_param.down_len = down_time
			self.jump_special_param.rw_down_time = self.rider_sword_move_time - down_time
			self.jump_special_param.down_time =self.rider_sword_move_time - down_time
			self.jump_special_param.end_time = self.rider_sword_move_time
			self.jump_special_param.old_offset_pos = old_pos
		end
	end

	self.rider_sword_state = ActionStep.Keep
	self:SetDirectionByXY(self.target_x, self.target_y)
	local real_end_pos_x, real_end_pos_y = GameMapHelper.LogicToWorld(self.target_x, self.target_y)
	if self.draw_obj then
		self.draw_obj:MoveTo(real_end_pos_x, real_end_pos_y, move_speed)
		self.draw_obj:SetMoveCallback(function ()
			self.rider_sword_move_time = Status.NowTime
			local position = self.draw_obj:GetRoot().gameObject.transform.position
			self:SetRealPos(position.x, position.z)
			self:OnRiderSwordToEnd()
			self.draw_obj:SetMoveCallback(nil)
		end)
		
		-- local forward = self.draw_obj:GetRoot().gameObject.transform.forward
		-- local dir = math.atan2(forward.z, forward.x)
		-- Scene.SendMoveReq(dir, self.target_x, self.target_y, 0, 0)
		local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
		main_part:ResetTrigger(AnimatorParameters.RiderSwordDown)
		main_part:SetTrigger(AnimatorParameters.RiderSwordIng)
	end

	Scene.Instance:UpdateVolumeMotionBlur(MotionBlurStage.Slight)
end

-- 御剑收剑
function MainRole:OnRiderSwordToReadyEnd()
	if self.rider_sword_state == ActionStep.ReadyToEnd then
		return
	end

	-- print_error("---御剑收剑---")
	self.rider_sword_state = ActionStep.ReadyToEnd
	if self.draw_obj then
		local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
		main_part:ResetTrigger(AnimatorParameters.RiderSwordIng)
		main_part:SetTrigger(AnimatorParameters.RiderSwordDown)
	end

	Scene.Instance:UpdateVolumeMotionBlur(MotionBlurStage.SpeedCut)
end

-- 御剑结束
function MainRole:OnRiderSwordToEnd(is_force)
	if not is_force and self.rider_sword_state == ActionStep.End then
		return
	end

	-- print_error("---御剑结束---", is_force)
	if self.target_x == nil or self.target_y == nil then
		return
	end

	if self.target_jumping_id then
		self.vo.jumping_id = self.target_jumping_id
		self.target_jumping_id = nil
	else
		self.vo.jumping_id = -1
	end

	self.cur_jumping_type = nil
	self.fixed_jump_stuck_time_data = nil
	Scene.Instance:UpdateVolumeMotionBlur(MotionBlurStage.None)
	self:ReleaseRiderSwordCarrier()
	self.rider_sword_state = ActionStep.End
	if self.jump_special_param ~= nil then
		local old_offset_pos = self.jump_special_param.old_offset_pos
		local draw_obj = self:GetDrawObj()
		if draw_obj ~= nil then
			draw_obj:SetOffset(Vector3(old_offset_pos.x, old_offset_pos.y, old_offset_pos.z))
		end
	end

	self:ChangeMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
	
	if self.mr_jump_rs_mc_dis_flag then
		Scene.Instance:RecoverCamera()
		self.mr_jump_rs_mc_dis_flag = nil
	end
	
	-- 同步位置
	-- local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
	-- Scene.SendSyncJump(Scene.Instance:GetSceneId(), self.logic_pos.x, self.logic_pos.y, scene_key)

	-- local forward = self.draw_obj:GetRoot().gameObject.transform.forward
	-- local dir = math.atan2(forward.z, forward.x)
	-- Scene.SendMoveReq(dir, self.logic_pos.x, self.logic_pos.y, 0)

	self.rider_sword_move_time = nil
	self.add_rider_sword_carrier_time = nil
	self.jump_special_param = nil
	self.is_special_jump = false
	self:SetJump(false)
	self:SetIsMoveOverPos(true)

	-- 播放落地特效
	local root = self.draw_obj.root
	local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_jump_luodi")
	EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, root.transform.position)
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Down))

	if self.jump_call_back then
		self.jump_call_back()
		self.jump_call_back = nil
	end
end

-- 御剑 释放御剑的载体
function MainRole:ReleaseRiderSwordCarrier()
    if not IsNil(self.rider_sword_carrier) then
        self.rider_sword_carrier_async_loader:Destroy()
        self.rider_sword_carrier = nil
    end
end

-- 御剑 添加御剑的载体
function MainRole:AddRiderSwordCarrier()
    self:ReleaseRiderSwordCarrier()
	local sex = self.vo.sex
    local role_foot_point = self.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
    if role_foot_point then
    	local bundle, asset = ResPath.GetRiderSwordCarrier(sex)
        self.rider_sword_carrier_async_loader = AllocAsyncLoader(self, "RiderSwordCarrier")
        self.rider_sword_carrier_async_loader:SetParent(role_foot_point.transform)
        self.rider_sword_carrier_async_loader:Load(bundle, asset,
            function(obj)
                if IsNil(obj) then
                    return
                end

                if not self.draw_obj or self.rider_sword_state == ActionStep.End then
                    self.rider_sword_carrier_async_loader:Destroy()
                    return
                end

                self.rider_sword_carrier = obj
                if IsNil(role_foot_point.gameObject) then
                    self:ReleaseRiderSwordCarrier()
                end
        	end
		)
    end
end















-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    轻功     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
function MainRole:TouchBegin()
	self.is_joystick_touched = true
	local enabled = self:IsCanUseQingGong() and self:IsUsableQingGong()
	self:QingGongEnable(enabled)
end

function MainRole:TouchEnd()
	self.is_joystick_touched = false
	if not self.is_landed then
		self.draw_obj:AdjustMoveMent(0, 0)
	end

	if not self:IsQingGong() then
		self:QingGongEnable(false)
	end
end

function MainRole:TouchMove(fx, fy)
	if not self.is_landed then
		self.draw_obj:AdjustMoveMent(fx, fy)
	end
end

-------------------------------------------------轻功-御剑--------------------------------------------------
----[[轻功-御剑
-- 轻功 跳
function MainRole:Jump()
	if self.qinggong_index >= COMMON_CONSTS.MAX_QING_GONG_COUNT then
		return
	end

	-- 落地之前不允许触发第一段跳跃
	-- 快要落地之后不能再触发连跳
	if not self.is_landed and (self.qinggong_index == 0 or self.ready_to_ground) then
		return
	end

	-- 快速降落之后不能再使用轻功
	if self.is_force_landing then
		return
	end

	if self.jump_time_stamp + 1 > Status.NowTime then
		return
	end

	if not self:IsUsableQingGong() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.QingGong.StateNoQingGong)
		return
	end

	if self:IsFightState() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.QingGong.CanNotJumpInFight)
		return
	end

	if not self:IsCanUseQingGong() then
		return
	end

	-- 轻功整体改成御剑，动作采用之前的轻功三
	Role.Jump(self)
	-- 这里发一条移动协议，用来同步方向
	self:ClearPathInfo()
	self.jump_time_stamp = Status.NowTime
	self.jump_old_camera_type = CAMERA_TYPE
	self.jump_enter_mitsurugi_status = false
	Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_SWORD)
	MainuiWGCtrl.Instance:FlushView(0, "JumpState", {self.qinggong_index})
	GlobalEventSystem:Fire(OtherEventType.POWER_CHANGE_VIEW_OPEN, true)
end

-- 轻功 停止
function MainRole:StopQinggong()
	Role.StopQinggong(self)
end

-- 轻功 状态改变
function MainRole:QingGongStateChange(state)
	Role.QingGongStateChange(self, state)

	if state == QingGongState.Stop then
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_EXIT_JUMP_STATE)
	elseif state == QingGongState.OnGround then
		-- 检查是否落在非法区
		local logic_x, logic_y = self.logic_pos.x, self.logic_pos.y
		local is_block = AStarFindWay:IsBlock(logic_x, logic_y, true)
		local is_force_reset = false
		local position = Vector3(0, 0, 0)
		if is_block then
			self.auto_jump_count = self.auto_jump_count + 1
			logic_x, logic_y = AStarFindWay:FindNearestValidPoint(logic_x, logic_y, 500)
			-- 如果没有找到有效的点，则回到出生点
			if self.logic_pos.x == logic_x and self.logic_pos.y == logic_y then
				logic_x, logic_y = Scene.Instance:GetSceneTownPos()
			end

			local x, y = GameMapHelper.LogicToWorld(logic_x, logic_y)
			position.x = x
			position.z = y

			local height = self.draw_obj:GetHeight(MASK_LAYER.WALKABLE, position)
			position.y = height

			if u3d.v3Length(u3d.v3Sub(self:GetLuaPosition(), position), false) > 30 * 30 then
				is_force_reset = true
			end
		end

		-- 如果落在非法区，则尝试跳到合法区
		if is_block and self.auto_jump_count <= 3 and not is_force_reset then
			self:QingGongEnable(true)
			local dir = u3d.v3Normalize(u3d.v3Sub(position, self:GetLuaPosition()))
			self.draw_obj:SimpleJump(ResPreload.QingGongObject_back, u3d.v3Add(self:GetLuaPosition(), u3d.v3Mul(dir, 1000)), true)
			self:SetActionTrigger(SceneObjPart.Main, "qinggong")
			self.qinggong_index = 0
			self.has_play_qinggong_land = false
			self.is_landed = false
		-- 超过3次，或者距离太远，则直接重置坐标
		else
			if self.auto_jump_count > 3 or is_force_reset then
				self:SetLogicPos(logic_x, logic_y)
			end
			self.auto_jump_count = 0

			-- 这里发一条移动协议，用来同步方向
			local forward = self.draw_obj:GetRoot().gameObject.transform.forward
			local dir = math.atan2(forward.z, forward.x)

			-- 同步位置
			local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
			Scene.SendSyncJump(Scene.Instance:GetSceneId(), logic_x, logic_y, scene_key)
			if self.qinggong_index < COMMON_CONSTS.MAX_QING_GONG_COUNT and not self.has_play_qinggong_land then
				if not self:IsMove() then
					self:SetActionTrigger(SceneObjPart.Main, "mitsurugi_land")
				else
					self:QingGongLandExit()
				end
			end

			Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
			Scene.SendMoveReq(dir, logic_x, logic_y, 0, 0)
			self.qinggong_index = 0

			-- 恢复摄像机
			self.jump_enter_mitsurugi_status = false
			Scene.Instance:SetCameraMode(self.jump_old_camera_type or CameraType.Free)
			GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_EXIT_JUMP_STATE)
		end
	elseif state == QingGongState.Mitsurugi then
		self:SendMoveReq(0)
	elseif state == QingGongState.Up then
		if not self.jump_enter_mitsurugi_status then
			self.jump_enter_mitsurugi_status = true
			self:ChangeCameraDistance()
		end
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_ENTER_JUMP_STATE)
	elseif state == QingGongState.Down then
		Scene.Instance:RecoverCamera(false, true)
	end
end

-- 切换摄像机距离
function MainRole:ChangeCameraDistance()
	if not IsNil(MainCameraFollow) then
		Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.UP_MOUNT, nil, nil, nil, {is_only_cache = true})
		local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
		local sprint_camera_distance = other_cfg and other_cfg.sprint_camera_distance or 18
		MainCameraFollow:DOCameraDistanceTwoStageTween(6, 0.2, sprint_camera_distance, 0.7)
	end
end

-- 轻功 着陆
function MainRole:Landing()
	Role.Landing(self)
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_MOVE_SWORD_DOWN)
end

-- 轻功
function MainRole:CheckQingGong()
	local enabled = self:IsCanUseQingGong()
	if self.old_qinggong_enabled ~= enabled then
		GlobalEventSystem:Fire(OtherEventType.ENABLE_QING_GONG_CHANGE, enabled)
		self.old_qinggong_enabled = enabled
	end
end

-- 轻功 是否可使用
function MainRole:IsCanUseQingGong()
	return Scene.Instance:IsQingGongScene()
		and FunOpen.Instance:GetFunIsOpened("JumpOpen")
		and self.vo.husong_taskid == 0
		and self:IsFightState() == false
		and self.vo.special_appearance == 0
		and self:IsRidingFightMount() == false
end

-- 轻功 当前状态是否可用
-- 为毛会有两个
function MainRole:IsUsableQingGong()
	return self.special_res_id == 0
		and not self:GetIsFlying()
		and not self:IsJump()
		and not CgManager.Instance:IsCgIng()
		and self.vo.husong_taskid == 0
		and self.vo.special_appearance == 0
		and not self:IsDead()
		and not self.is_gather_state
		and not MarryWGData.Instance:GetOwnIsXunyou()
end

-- 轻功御剑
function MainRole:JumpUp()
	local trans = self.draw_obj:GetTransfrom()
	local pos_z = trans.position.y + 0.45
	if pos_z <= Config.SCENE_ROLE_JUMP_UP_MAX_HIGHT then
		trans.position = Vector3(trans.position.x, pos_z, trans.position.z)
	end
end

-- 获取冲刺后的位置
function MainRole:JumpSprint(callback)
	local now_angle = MainCameraFollow.gameObject.transform.localRotation.eulerAngles.x
	local now_rot = MainCameraFollow.gameObject.transform.forward
	local role_for = self.draw_obj.root_transform.forward
	local relation = Vector3.Dot(now_rot, role_for)

	if now_angle > 100 then
		now_angle = -360 + now_angle
	end

	-- 计算出摄像机方向和角度
	local tan_angle = now_angle - HORIZONTAL_ANGLE

	local dir = tan_angle < 0 and 1 or -1
	local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	local move_sword_sprint_distance = other_cfg and other_cfg.move_sword_sprint_distance or 12
	local min_move_sword_sprint_distance = other_cfg and other_cfg.min_move_sword_sprint_distance or 12

	-- 拿到玩家当前自身的方向
	local aim_dir = relation < 0 and -1 or 1
	local height = math.abs(tan_angle * 0.05 * (move_sword_sprint_distance + min_move_sword_sprint_distance) * 0.5)
	local forward = self.draw_obj:GetRoot().gameObject.transform.forward
	local up = self.draw_obj:GetRoot().gameObject.transform.up
	local trans = self.draw_obj:GetTransfrom()
	local aim_pos = trans.position + forward * (move_sword_sprint_distance + min_move_sword_sprint_distance)
	local aim_pos_up = trans.position + up * dir * aim_dir * height
	local logic_pos_x, logic_pos_y = GameMapHelper.WorldToLogic(aim_pos.x, aim_pos.z)
	local is_not_in_grid = AStarFindWay:IsNotInGrid(logic_pos_x, logic_pos_y)
	if is_not_in_grid then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotCanMoveToPos)
		return false
	end

	-- 发送玩家冲刺
	-- self:UpdateMovePathPosList(logic_pos_x, logic_pos_y, self:IsCanUseQingGong(), true)
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_MOVE_SWORD_SPRINT, logic_pos_x, logic_pos_y, aim_pos_up.y)
	self:DoSwordSprint(false, aim_pos.x, aim_pos.z, aim_pos_up.y)
	return true
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    护送\运镖     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 护送\运镖 状态更新
function MainRole:UpdateYunbiaoState()
	local old_state = self.is_in_yunbiao_area
	self:CheckIsInYunbiaoArea()

	if old_state ~= self.is_in_yunbiao_area and self.is_in_yunbiao_area and (not self.lingqu_cishu or  self.lingqu_cishu ~= YunbiaoWGData.Instance:GetLingQuCishu()) then
		YunbiaoWGCtrl.Instance:OpenQuestionView()
		self.lingqu_cishu = YunbiaoWGData.Instance:GetLingQuCishu()
	end
end

-- 护送 是否在护送区域
function MainRole:CheckIsInYunbiaoArea()
	-- 是否是运镖状态
	if not YunbiaoWGData.Instance:GetIsHuShong() then
		return
	end
	self:TryLeaveSit(true)
	self.is_in_yunbiao_area = false
	local info = YunbiaoWGData.Instance:GetAreaInfo()
	if info == nil then
		return
	end

	if not GameMath.IsInRectInPos(self.logic_pos.x, self.logic_pos.y, info.pos_1.x, info.pos_1.y, info.pos_2.x, info.pos_2.y) then
		return
	end

	self.is_in_yunbiao_area = AStarFindWay:IsInSafeArea(self.logic_pos.x, self.logic_pos.y)
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    打坐     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 打坐 状态更新
function MainRole:UpdateSitState(is_init, is_change)
	local old_state = self.is_in_sit_area
	local sit_open_change = self.role_sit_open
	self:CheckIsInSitArea()

	if not self.role_sit_open then
		self:CheckSitOpen()
	end

	if old_state ~= self.is_in_sit_area or is_init or sit_open_change ~= self.role_sit_open or is_change then
		MainuiWGCtrl.Instance:FlushSitState()
		if is_change then
			self:TryLeaveSit(true)
		end
	end
end

-- 打坐 是否开启
function MainRole:CheckSitOpen()
	self.role_sit_open = FunOpen.Instance:GetFunIsOpened(FunName.RoleSit)
end

-- 打坐 是否在打坐区域
function MainRole:CheckIsInSitArea()
	self.is_in_sit_area = false
	local info = OfflineRestWGData.Instance:GetSitAreaInfo()
	if info == nil then
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	if scene_id ~= info.scene_id then
		return
	end

	if not GameMath.IsInRectInPos(self.logic_pos.x, self.logic_pos.y, info.pos_1.x, info.pos_1.y, info.pos_3.x, info.pos_3.y) then
		return
	end

	self.is_in_sit_area = AStarFindWay:IsInSafeArea(self.logic_pos.x, self.logic_pos.y)
end

-- 打坐
function MainRole:TrySit()
	if self:IsDeleted() then
		return
	end

	if self:IsJump() or self:IsQingGong() or YunbiaoWGData.Instance:GetIsHuShong() or self:IsMitsurugi() then
		return
	end

	if not self:GetIsInSit() then
		Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_SIT)
	end
end


-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    摄像机     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 坐骑加载完成,设置了乘骑pos
function MainRole:OnMountLoadedAndUp()
	self:CancelUpMountLookAtPointFixedTimer()
	self:UpdateCameraFollowTarget()
	-- 上下坐骑有偏移动画导致误差，需要延迟追加修复
	self.up_mount_look_at_point_fixed_timer = GlobalTimerQuest:AddDelayTimer(function()
		if not self:IsDeleted() then
			self:UpdateCameraFollowTarget()
		end
	end, 0.3)
end


-- 摄像机 更新跟随目标
function MainRole:UpdateCameraFollowTarget(immediate)
	if IsNil(MainCameraFollow) then
		return
	end

	if UN_UPDATE_CAMERA_FOLLOW_TARGET then
		return
	end

	if self:IsInXunYou()
	or (WorldsNO1WGData.Instance and WorldsNO1WGData.Instance:IsObservationStatus()) then
		return
	end

	local _, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(self.vo.obj_id)
	if multi_mount_state == MULTI_MOUNT_STATE.TAKE then
		return
	end
	
	self:CancelCameraUpdateTimer()
	self.delay_update_camera_target_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if WorldsNO1WGData.Instance and WorldsNO1WGData.Instance:IsObservationStatus() then
			return
		end

		if not IsNil(MainCameraFollow) and not self:IsDeleted() then
			local target_point = self:GetRoot() and self:GetRoot().transform or nil
			local x, y, z = self:GetLookAtPointPos()
			local point = self.draw_obj:GetLookAtPoint(x, y, z, immediate)
			target_point = point or target_point

			--MainCameraFollow.targetOffset = Vector3(0, -0.1, 0)
			MainCameraFollow.Target = target_point
			BossCamera.Instance:SetBossFollowCameraTarget(self, target_point)
			if immediate then
				MainCameraFollow:SyncImmediate()
			end

			SceneOptimizeMgr.SetCenterPoint(self:GetRoot().transform)
		end
	end, 0.1)
end

-- 摄像机 清除更新目标计时器
function MainRole:CancelCameraUpdateTimer()
	if self.delay_update_camera_target_timer then
		GlobalTimerQuest:CancelQuest(self.delay_update_camera_target_timer)
		self.delay_update_camera_target_timer = nil
	end
end

-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    影子     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 投影仪的影子 可见性
function MainRole:SetProjectorShadowVisible(visible)
	if visible then
		self:CreateProjectorShadow()
	elseif nil ~= self.projector_shadow_loader then
		self.projector_shadow_loader:Destroy()
		self.shadow_projector = nil
	end
end

-- 投影仪的影子 创建
function MainRole:CreateProjectorShadow()
	self.projector_shadow_loader = self.projector_shadow_loader or AllocAsyncLoader(self, "projector_shadow")
	self.projector_shadow_loader:SetParent(G_SceneObjLayer)
	self.projector_shadow_loader:SetIsInQueueLoad(true)
 	self.projector_shadow_loader:Load("misc/projector_prefab", "HardShadowProjector", function(obj)
	 		if nil ~= obj then
	 			self.shadow_projector = obj:GetComponent(typeof(ShadowProjector))
	 			self.shadow_projector:SetTarget(self.draw_obj:GetRoot():GetComponent(typeof(UnityEngine.Transform)))
	 		end
		end)

	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	if main_part then
		main_part:SetIsCastShadow(true)
	end
	
	local mount_part_type = self:GetCurRidingPart()
	if mount_part_type then
		local mount_part = self.draw_obj:GetPart(mount_part_type)
		if mount_part then
			mount_part:SetIsCastShadow(true)
		end
	end

	local wing_part = self.draw_obj:GetPart(SceneObjPart.Wing)
	if wing_part then
		wing_part:SetIsCastShadow(true)
	end

	local weapon_part = self.draw_obj:GetPart(SceneObjPart.Weapon)
	if weapon_part then
		weapon_part:SetIsCastShadow(true)
	end
end

-- 投影仪的影子 设置影子灯光朝向
function MainRole:SetShadowProjectorLightForward(light_forward)
	if self.shadow_projector then
		self.shadow_projector:SetLightForward(light_forward)
	end
end


function MainRole:CreateShadow()
	Role.CreateShadow(self)
end-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    模型     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 模型 移除
function MainRole:RemoveModel(part, real_remove)
    Role.RemoveModel(self, part, real_remove)

    if part ~= nil and part == SceneObjPart.Main and TaskWGCtrl.Instance ~= nil and not TaskWGCtrl.Instance:IsFly() then
    	print_error("MianRole MainPart Is Remove !", self.special_res_id, self.role_res_id)
    end
end

-- 模型加载完
function MainRole:OnModelLoaded(part, obj, obj_class)
	Role.OnModelLoaded(self, part, obj, obj_class)
	if part == SceneObjPart.Main then
		--[[ 2021/8/12 屏蔽跳跃鬼魂特效
		if nil ~= CharacterGhost then
			self.character_ghost = CharacterGhost.Bind(obj.gameObject)
			if self.character_ghost then
				self.character_ghost.Root = SceneObjLayer.transform
				local material = ResPreload["role_ghost_" .. self:GetProf()]
				self.character_ghost.Material = material
				self.character_ghost:SetSpeedFactor(3)
			end
		end
		]]
		self:UpdateCameraFollowTarget()
		if CgManager.Instance:IsCgIng() then
			CgManager.Instance:ModifyTrack()
		end

		-- 主角模型强制使用LOD 0
		self:ForceSetLOD(obj, 0)
	end
end

-- 坐骑 更新
function MainRole:UpdateMount(...)
	-- CG的时候更新坐骑可能导致人物位置改变，CG出现异常
    if CgManager.Instance:IsCgIng() then
		CgManager.Instance:SetCgEndCallBack(BindTool.Bind2(self.UpdateMount, self, ...))
		return
    end

	Role.UpdateMount(self, ...)
end

function MainRole:CancelUpMountLookAtPointFixedTimer()
	if self.up_mount_look_at_point_fixed_timer then
		GlobalTimerQuest:CancelQuest(self.up_mount_look_at_point_fixed_timer)
		self.up_mount_look_at_point_fixed_timer = nil
	end
end


-- 跟随的npc change
function MainRole:ChangeFollowNpc()
    if TaskWGData.FOLLOW_NPC and Scene.Instance:GetSceneType() == SceneType.Common and not self:IsRealDead() then
        if not self.follow_npc then
            self.follow_npc = Scene.Instance:CreateFollowNpcByRole(self, TaskWGData.FOLLOW_NPC)
        end
    else
        if self.follow_npc then
            Scene.Instance:DeleteObjByTypeAndKey(SceneObjType.FollowNpc, self.follow_npc:GetObjKey())
            self.follow_npc = nil
        end
    end
end

-- 跟随的npc get
function MainRole:GetFollowNpc()
	return self.follow_npc
end


-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    特效 Buff     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- Buff 战斗Buff变更
function MainRole:FightEffectChange(is_main_role)
	if is_main_role then
		self:SetBuffList(FightWGData.Instance:GetMainRoleShowEffect(0))
	end
end

-- Buff list set
function MainRole:SetBuffList(data)
	if self.follow_ui then
		self.follow_ui:SetBuffList(data)
	end
end


-- 特效 飞天特效
function MainRole:PlayFlyUpEffect(callback)
    if self.is_fly_up then
        return
    end
    self.is_fly_up = true

    -- self:ChangeMoveMode(MOVE_MODE.MOVE_MODE_JUMP2)

    local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_chuansong_up")
    local async_loader = AllocAsyncLoader(self, "player_fly_up_effect")
	if async_loader then
		async_loader:SetParent(self:GetRoot().transform or G_EffectLayer)
		async_loader:SetObjAliveTime(5)
		async_loader:SetIsUseObjPool(true)
		async_loader:Load(bundle_name, asset_name)
	end

    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ChuanSong))

    GlobalTimerQuest:AddDelayTimer(function()
        self.is_fly_up = false
        if self.follow_ui then
			self.follow_ui:ForceSetVisible(false)
		end

		if self.under_follow_ui then
			self.under_follow_ui:ForceSetVisible(false)
		end
        self:ForceSetVisible(false)

        if callback then
            callback()
        end
    end, 1)
end

-- 特效 下落时的光束
function MainRole:PlayFlyDownEffect(callback)
    if self.is_fly_down then
        return
    end
    self.is_fly_down = true

    -- 在角色信息没返回的时候，屏蔽管理会报错，因为没有经过Init
    self:CreateShieldHandle()

    if self.follow_ui then
		self.follow_ui:ForceSetVisible(false)
	end

	if self.under_follow_ui then
		self.under_follow_ui:ForceSetVisible(false)
	end

    self:ForceSetVisible(false)
    self:CheckFollowObjShadowVisiable(false)

    local session = {flag = false, callback = callback}
    self:StartResumeVisibleQuest(session)

    local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_chuansong_down")
    local async_loader = AllocAsyncLoader(self, "player_fly_down_effect")
	if async_loader then
		async_loader:SetParent(self:GetRoot().transform or G_EffectLayer)
		async_loader:SetObjAliveTime(8)
		async_loader:SetIsUseObjPool(true)

		-- fly_down的时候不要旋转摄像机
		if not IsNil(MainCameraFollow) then
			MainCameraFollow.AutoRotation = false
		end

    	async_loader:Load(bundle_name, asset_name, function(obj)
			self:CancelResumeVisibleQuest()
			GlobalTimerQuest:AddDelayTimer(function()
				if not session.flag then
					session.flag = true
					if session.callback then
						session.callback()
					end
				end

				if self.follow_ui and not self:IsInXunYou() then
					self.follow_ui:CancelForceSetVisible()
				end

				if self.under_follow_ui then
					self.under_follow_ui:CancelForceSetVisible()
				end
				self:CancelForceSetVisible()
				self.is_fly_down = false
				if not self.role_first_game then
					MainuiWGCtrl.Instance:ChangeZuoQiState(self:IsRiding() and 1 or 0)
					self.role_first_game = true
				end

				if not IsNil(MainCameraFollow) then
					local last_auto_state = MainuiWGCtrl.Instance:GetRoleAutoXunluState()
					if last_auto_state ~= nil and last_auto_state == XunLuStatus.XunLu then
						MainCameraFollow.AutoRotation = true
					else
						MainCameraFollow.AutoRotation = false
					end
				end

				self:CheckFollowObjShadowVisiable(true)
				GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_FLY_DOWN_END)
			end, 1.13)
		end)
	end
end

-- 特效 下落时的光束 3秒之后强制恢复
function MainRole:StartResumeVisibleQuest(session)
	self:CancelResumeVisibleQuest()
	self.resume_visible_quest = GlobalTimerQuest:AddDelayTimer(function ()
		self.is_fly_up = false
		self.is_fly_down = false

		if not session.flag then
			session.flag = true

			if session.callback then
			    session.callback()
			end
		end

		if self.follow_ui and not self:IsInXunYou() then
			self.follow_ui:CancelForceSetVisible()
		end

		self:CancelForceSetVisible()
		self:CheckFollowObjShadowVisiable(true)
	end, 3)
end

function MainRole:CancelResumeVisibleQuest()
	if self.resume_visible_quest then
		GlobalTimerQuest:CancelQuest(self.resume_visible_quest)
		self.resume_visible_quest = nil
	end
end

-- 特效 人物残影 显示
function MainRole:ShowGhost(_type, maxGhostNum, maxConcurrentGhostNum, timeInterval)
	if CgManager.Instance:IsCgIng() then
		return
	end
	_type = _type or 0
	maxGhostNum = maxGhostNum or 10
	maxConcurrentGhostNum = maxConcurrentGhostNum or 8
	timeInterval = timeInterval or 0.1
	if self.character_ghost then
		self.character_ghost:ShowGhost(_type, maxGhostNum, maxConcurrentGhostNum, timeInterval)
	end
end

-- 特效 人物残影 停止
function MainRole:StopGhost(time)
	time = time or 0
	if self.character_ghost then
		self.character_ghost:Stop(time)
	end
end

-- 特效 选中连线
function MainRole:AddSelectObjLine(side2_objid)
	local buff_type = OBJ_LINR_SPECIAL_KEY.SELECT
	local side1_objid = self:GetObjId()

	local old_line_data = nil
	for k,v in pairs(self.line_effect_cache) do
        if buff_type == v.line_key then
			if (v.side1_objid == side1_objid and v.side2_objid == side2_objid) then
            	return
			end

			old_line_data = self.line_effect_cache[k]
        end
    end

	local side1_obj = Scene.Instance:GetMainRole()
	if side1_obj == nil or side1_obj:IsDeleted() then
		return
	end

	local side2_obj = Scene.Instance:GetObjectByObjId(side2_objid)
    if side2_obj == nil or side2_obj:IsDeleted() or side2_obj:GetType() == SceneObjType.MainRole then
		return
	end
	
	if old_line_data then
		-- 变更连接对象
		old_line_data.side2_objid = side2_objid
		old_line_data.side2_obj = side2_obj
	else
		local link_data = {}
		link_data.side1_objid = side1_objid
		link_data.side2_objid = side2_objid
		link_data.side1_obj = side1_obj
		link_data.side2_obj = side2_obj
		link_data.line_key = buff_type
		link_data.is_select_line = true
		self:CreateLineEffectLoder(link_data)
	end
end

function MainRole:ClearSelectObjLine()
	self:ClearAllLineEffect(OBJ_LINR_SPECIAL_KEY.SELECT)
end


-- 是否是怒气变身(请求中)
function MainRole:IsXiuWeiBianShenSeq()
    return self.xiuwei_bianshen_seq or false
end

-- 是否是怒气变身(请求中)
function MainRole:SetXiuWeiBianShenSeq(value)
    self.xiuwei_bianshen_seq = value
end

-- 播放被炸特效
function MainRole:PlayGatherBombEffect()
	local bundle_name, asset_name = ResPath.GetFallItemZhuEffect("eff_caijibaozha")
    local async_loader = AllocAsyncLoader(self, "player_gather_bomb_effect")
	if async_loader then
		async_loader:SetParent(self:GetRoot().transform or G_EffectLayer)
		async_loader:SetObjAliveTime(5)
		async_loader:SetIsUseObjPool(true)
		async_loader:Load(bundle_name, asset_name)
	end
end
------------------------------ProjectorShadowHandle-----------------------------------------------

ProjectorShadowHandle = ProjectorShadowHandle or BaseClass(VisibleObj)
function ProjectorShadowHandle:__init(main_role)
	self.main_role = main_role
	self.shield_obj_type = ShieldObjType.ProjectorShadow
end

function ProjectorShadowHandle:__delete()
	self.main_role = nil
end

function ProjectorShadowHandle:VisibleChanged(visible)
	if self.main_role then
		self.main_role:SetProjectorShadowVisible(visible)
	end
end
