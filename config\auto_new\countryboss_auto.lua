-- G-国家BOSS.xls
local item_table={
[1]={item_id=17211,num=1,is_bind=1},
[2]={item_id=17210,num=1,is_bind=1},
[3]={item_id=17209,num=1,is_bind=1},
[4]={item_id=17208,num=1,is_bind=1},
[5]={item_id=17207,num=1,is_bind=1},
[6]={item_id=17206,num=1,is_bind=1},
[7]={item_id=50008,num=1,is_bind=1},
[8]={item_id=50025,num=1,is_bind=1},
[9]={item_id=17205,num=1,is_bind=1},
[10]={item_id=17204,num=1,is_bind=1},
[11]={item_id=17203,num=1,is_bind=1},
[12]={item_id=17202,num=1,is_bind=1},
[13]={item_id=17201,num=1,is_bind=1},
[14]={item_id=17311,num=1,is_bind=1},
[15]={item_id=17310,num=1,is_bind=1},
[16]={item_id=17309,num=1,is_bind=1},
[17]={item_id=17308,num=1,is_bind=1},
[18]={item_id=17307,num=1,is_bind=1},
[19]={item_id=17306,num=1,is_bind=1},
[20]={item_id=50009,num=1,is_bind=1},
[21]={item_id=50027,num=1,is_bind=1},
[22]={item_id=17305,num=1,is_bind=1},
[23]={item_id=17304,num=1,is_bind=1},
[24]={item_id=17303,num=1,is_bind=1},
[25]={item_id=17302,num=1,is_bind=1},
[26]={item_id=17301,num=1,is_bind=1},
[27]={item_id=17411,num=1,is_bind=1},
[28]={item_id=17410,num=1,is_bind=1},
[29]={item_id=17409,num=1,is_bind=1},
[30]={item_id=17408,num=1,is_bind=1},
[31]={item_id=17407,num=1,is_bind=1},
[32]={item_id=17406,num=1,is_bind=1},
[33]={item_id=50010,num=1,is_bind=1},
[34]={item_id=50029,num=1,is_bind=1},
[35]={item_id=17405,num=1,is_bind=1},
[36]={item_id=17404,num=1,is_bind=1},
[37]={item_id=17403,num=1,is_bind=1},
[38]={item_id=17402,num=1,is_bind=1},
[39]={item_id=17401,num=1,is_bind=1},
[40]={item_id=17111,num=1,is_bind=1},
[41]={item_id=50007,num=1,is_bind=1},
[42]={item_id=50023,num=1,is_bind=1},
[43]={item_id=17110,num=1,is_bind=1},
[44]={item_id=17109,num=1,is_bind=1},
[45]={item_id=17108,num=1,is_bind=1},
[46]={item_id=17107,num=1,is_bind=1},
[47]={item_id=17106,num=1,is_bind=1},
[48]={item_id=17105,num=1,is_bind=1},
[49]={item_id=17104,num=1,is_bind=1},
[50]={item_id=17103,num=1,is_bind=1},
[51]={item_id=17102,num=1,is_bind=1},
[52]={item_id=17101,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
boss_hurt={
{},
{}
},

boss_hurt_meta_table_map={
},
boss={
{},
{index=2,boss_id=23001,box_name="寒冰幽魂",},
{index=3,boss_id=24001,box_name="噬魂魔王",},
{index=4,boss_id=25001,box_name="雷天狮王",}
},

boss_meta_table_map={
},
reward_cfg={
{},
{level_min=390,level_max=399,},
{level_min=400,level_max=409,},
{level_min=410,level_max=419,},
{level_min=420,level_max=429,},
{level_min=430,level_max=439,},
{level_min=440,level_max=449,},
{level_min=450,level_max=459,},
{level_min=460,level_max=469,},
{level_min=470,level_max=479,},
{level_min=480,level_max=489,},
{level_min=490,level_max=499,},
{level_min=500,level_max=509,},
{level_min=510,level_max=519,},
{level_min=520,level_max=529,},
{level_min=530,level_max=539,},
{level_min=540,level_max=549,},
{level_min=550,level_max=559,},
{level_min=560,level_max=569,},
{level_min=570,level_max=579,},
{level_min=580,level_max=589,},
{level_min=590,level_max=599,},
{level_min=600,level_max=609,},
{level_min=610,level_max=619,},
{level_min=620,level_max=629,},
{level_min=630,level_max=639,},
{level_min=640,level_max=649,},
{level_min=650,level_max=659,},
{level_min=660,level_max=669,},
{level_min=670,level_max=679,},
{level_min=680,level_max=689,},
{level_min=690,level_max=699,},
{level_min=700,level_max=709,},
{level_min=710,level_max=719,},
{level_min=720,level_max=729,},
{level_min=730,level_max=739,},
{level_min=740,level_max=749,},
{level_min=750,level_max=759,},
{level_min=760,level_max=769,},
{level_min=770,level_max=779,},
{level_min=780,level_max=789,},
{level_min=790,level_max=799,},
{level_min=800,level_max=809,},
{level_min=810,level_max=819,},
{level_min=820,level_max=829,},
{level_min=830,level_max=839,},
{level_min=840,level_max=849,},
{level_min=850,level_max=859,},
{level_min=860,level_max=869,},
{level_min=870,level_max=879,},
{level_min=880,level_max=889,},
{level_min=890,level_max=899,},
{level_min=900,level_max=909,},
{level_min=910,level_max=919,},
{level_min=920,level_max=929,},
{level_min=930,level_max=939,},
{level_min=940,level_max=949,},
{level_min=950,level_max=959,},
{level_min=960,level_max=2000,},
{boss_id=23001,show_reward_item_1={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7],[7]=item_table[8]},join_reward_item={[0]=item_table[2],[1]=item_table[3],[2]=item_table[4],[3]=item_table[5],[4]=item_table[6],[5]=item_table[9],[6]=item_table[10],[7]=item_table[11],[8]=item_table[12],[9]=item_table[13],[10]=item_table[7],[11]=item_table[8]},},
{level_min=390,level_max=399,},
{level_min=400,level_max=409,},
{level_min=410,level_max=419,},
{level_min=420,level_max=429,},
{level_min=430,level_max=439,},
{level_min=440,level_max=449,},
{level_min=450,level_max=459,},
{level_min=460,level_max=469,},
{level_min=470,level_max=479,},
{level_min=480,level_max=489,},
{level_min=490,level_max=499,},
{level_min=500,level_max=509,},
{level_min=510,level_max=519,},
{level_min=520,level_max=529,},
{level_min=530,level_max=539,},
{level_min=540,level_max=549,},
{level_min=550,level_max=559,},
{level_min=560,level_max=569,},
{level_min=570,level_max=579,},
{level_min=580,level_max=589,},
{level_min=590,level_max=599,},
{level_min=600,level_max=609,},
{level_min=610,level_max=619,},
{level_min=620,level_max=629,},
{level_min=630,level_max=639,},
{level_min=640,level_max=649,},
{level_min=650,level_max=659,},
{level_min=660,level_max=669,},
{level_min=670,level_max=679,},
{level_min=680,level_max=689,},
{level_min=690,level_max=699,},
{level_min=700,level_max=709,},
{level_min=710,level_max=719,},
{level_min=720,level_max=729,},
{level_min=730,level_max=739,},
{level_min=740,level_max=749,},
{level_min=750,level_max=759,},
{level_min=760,level_max=769,},
{level_min=770,level_max=779,},
{level_min=780,level_max=789,},
{level_min=790,level_max=799,},
{level_min=800,level_max=809,},
{level_min=810,level_max=819,},
{level_min=820,level_max=829,},
{level_min=830,level_max=839,},
{level_min=840,level_max=849,},
{level_min=850,level_max=859,},
{level_min=860,level_max=869,},
{level_min=870,level_max=879,},
{level_min=880,level_max=889,},
{level_min=890,level_max=899,},
{level_min=900,level_max=909,},
{level_min=910,level_max=919,},
{level_min=920,level_max=929,},
{level_min=930,level_max=939,},
{level_min=940,level_max=949,},
{level_min=950,level_max=959,},
{level_min=960,level_max=2000,},
{boss_id=24001,show_reward_item_1={[0]=item_table[14],[1]=item_table[15],[2]=item_table[16],[3]=item_table[17],[4]=item_table[18],[5]=item_table[19],[6]=item_table[20],[7]=item_table[21]},join_reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[19],[5]=item_table[22],[6]=item_table[23],[7]=item_table[24],[8]=item_table[25],[9]=item_table[26],[10]=item_table[20],[11]=item_table[21]},},
{level_min=390,level_max=399,},
{level_min=400,level_max=409,},
{level_min=410,level_max=419,},
{level_min=420,level_max=429,},
{level_min=430,level_max=439,},
{level_min=440,level_max=449,},
{level_min=450,level_max=459,},
{level_min=460,level_max=469,},
{level_min=470,level_max=479,},
{level_min=480,level_max=489,},
{level_min=490,level_max=499,},
{level_min=500,level_max=509,},
{level_min=510,level_max=519,},
{level_min=520,level_max=529,},
{level_min=530,level_max=539,},
{level_min=540,level_max=549,},
{level_min=550,level_max=559,},
{level_min=560,level_max=569,},
{level_min=570,level_max=579,},
{level_min=580,level_max=589,},
{level_min=590,level_max=599,},
{level_min=600,level_max=609,},
{level_min=610,level_max=619,},
{level_min=620,level_max=629,},
{level_min=630,level_max=639,},
{level_min=640,level_max=649,},
{level_min=650,level_max=659,},
{level_min=660,level_max=669,},
{level_min=670,level_max=679,},
{level_min=680,level_max=689,},
{level_min=690,level_max=699,},
{level_min=700,level_max=709,},
{level_min=710,level_max=719,},
{level_min=720,level_max=729,},
{level_min=730,level_max=739,},
{level_min=740,level_max=749,},
{level_min=750,level_max=759,},
{level_min=760,level_max=769,},
{level_min=770,level_max=779,},
{level_min=780,level_max=789,},
{level_min=790,level_max=799,},
{level_min=800,level_max=809,},
{level_min=810,level_max=819,},
{level_min=820,level_max=829,},
{level_min=830,level_max=839,},
{level_min=840,level_max=849,},
{level_min=850,level_max=859,},
{level_min=860,level_max=869,},
{level_min=870,level_max=879,},
{level_min=880,level_max=889,},
{level_min=890,level_max=899,},
{level_min=900,level_max=909,},
{level_min=910,level_max=919,},
{level_min=920,level_max=929,},
{level_min=930,level_max=939,},
{level_min=940,level_max=949,},
{level_min=950,level_max=959,},
{level_min=960,level_max=2000,},
{boss_id=25001,show_reward_item_1={[0]=item_table[27],[1]=item_table[28],[2]=item_table[29],[3]=item_table[30],[4]=item_table[31],[5]=item_table[32],[6]=item_table[33],[7]=item_table[34]},join_reward_item={[0]=item_table[28],[1]=item_table[29],[2]=item_table[30],[3]=item_table[31],[4]=item_table[32],[5]=item_table[35],[6]=item_table[36],[7]=item_table[37],[8]=item_table[38],[9]=item_table[39],[10]=item_table[33],[11]=item_table[34]},},
{level_min=390,level_max=399,},
{level_min=400,level_max=409,},
{level_min=410,level_max=419,},
{level_min=420,level_max=429,},
{level_min=430,level_max=439,},
{level_min=440,level_max=449,},
{level_min=450,level_max=459,},
{level_min=460,level_max=469,},
{level_min=470,level_max=479,},
{level_min=480,level_max=489,},
{level_min=490,level_max=499,},
{level_min=500,level_max=509,},
{level_min=510,level_max=519,},
{level_min=520,level_max=529,},
{level_min=530,level_max=539,},
{level_min=540,level_max=549,},
{level_min=550,level_max=559,},
{level_min=560,level_max=569,},
{level_min=570,level_max=579,},
{level_min=580,level_max=589,},
{level_min=590,level_max=599,},
{level_min=600,level_max=609,},
{level_min=610,level_max=619,},
{level_min=620,level_max=629,},
{level_min=630,level_max=639,},
{level_min=640,level_max=649,},
{level_min=650,level_max=659,},
{level_min=660,level_max=669,},
{level_min=670,level_max=679,},
{level_min=680,level_max=689,},
{level_min=690,level_max=699,},
{level_min=700,level_max=709,},
{level_min=710,level_max=719,},
{level_min=720,level_max=729,},
{level_min=730,level_max=739,},
{level_min=740,level_max=749,},
{level_min=750,level_max=759,},
{level_min=760,level_max=769,},
{level_min=770,level_max=779,},
{level_min=780,level_max=789,},
{level_min=790,level_max=799,},
{level_min=800,level_max=809,},
{level_min=810,level_max=819,},
{level_min=820,level_max=829,},
{level_min=830,level_max=839,},
{level_min=840,level_max=849,},
{level_min=850,level_max=859,},
{level_min=860,level_max=869,},
{level_min=870,level_max=879,},
{level_min=880,level_max=889,},
{level_min=890,level_max=899,},
{level_min=900,level_max=909,},
{level_min=910,level_max=919,},
{level_min=920,level_max=929,},
{level_min=930,level_max=939,},
{level_min=940,level_max=949,},
{level_min=950,level_max=959,},
{level_min=960,level_max=2000,}
},

reward_cfg_meta_table_map={
[174]=119,	-- depth:1
[173]=174,	-- depth:2
[179]=178,	-- depth:1
[175]=174,	-- depth:2
[176]=174,	-- depth:2
[177]=174,	-- depth:2
[180]=179,	-- depth:2
[186]=179,	-- depth:2
[182]=179,	-- depth:2
[183]=179,	-- depth:2
[184]=179,	-- depth:2
[185]=179,	-- depth:2
[187]=179,	-- depth:2
[188]=179,	-- depth:2
[172]=174,	-- depth:2
[189]=179,	-- depth:2
[181]=179,	-- depth:2
[171]=174,	-- depth:2
[156]=174,	-- depth:2
[169]=174,	-- depth:2
[190]=179,	-- depth:2
[151]=174,	-- depth:2
[152]=174,	-- depth:2
[153]=174,	-- depth:2
[154]=174,	-- depth:2
[155]=174,	-- depth:2
[157]=174,	-- depth:2
[158]=174,	-- depth:2
[170]=174,	-- depth:2
[159]=174,	-- depth:2
[161]=174,	-- depth:2
[162]=174,	-- depth:2
[163]=174,	-- depth:2
[164]=174,	-- depth:2
[165]=174,	-- depth:2
[166]=174,	-- depth:2
[167]=174,	-- depth:2
[168]=174,	-- depth:2
[160]=174,	-- depth:2
[191]=179,	-- depth:2
[221]=179,	-- depth:2
[193]=179,	-- depth:2
[217]=179,	-- depth:2
[218]=179,	-- depth:2
[219]=179,	-- depth:2
[220]=179,	-- depth:2
[150]=174,	-- depth:2
[222]=179,	-- depth:2
[223]=179,	-- depth:2
[224]=179,	-- depth:2
[216]=179,	-- depth:2
[225]=179,	-- depth:2
[227]=179,	-- depth:2
[228]=179,	-- depth:2
[229]=179,	-- depth:2
[230]=179,	-- depth:2
[231]=179,	-- depth:2
[232]=179,	-- depth:2
[233]=179,	-- depth:2
[234]=179,	-- depth:2
[226]=179,	-- depth:2
[215]=179,	-- depth:2
[214]=179,	-- depth:2
[213]=179,	-- depth:2
[194]=179,	-- depth:2
[195]=179,	-- depth:2
[196]=179,	-- depth:2
[197]=179,	-- depth:2
[198]=179,	-- depth:2
[199]=179,	-- depth:2
[200]=179,	-- depth:2
[201]=179,	-- depth:2
[202]=179,	-- depth:2
[203]=179,	-- depth:2
[204]=179,	-- depth:2
[205]=179,	-- depth:2
[206]=179,	-- depth:2
[207]=179,	-- depth:2
[208]=179,	-- depth:2
[209]=179,	-- depth:2
[210]=179,	-- depth:2
[211]=179,	-- depth:2
[212]=179,	-- depth:2
[192]=179,	-- depth:2
[149]=174,	-- depth:2
[118]=60,	-- depth:1
[147]=174,	-- depth:2
[83]=118,	-- depth:2
[84]=118,	-- depth:2
[85]=118,	-- depth:2
[86]=118,	-- depth:2
[87]=118,	-- depth:2
[88]=118,	-- depth:2
[89]=118,	-- depth:2
[90]=118,	-- depth:2
[82]=118,	-- depth:2
[91]=118,	-- depth:2
[93]=118,	-- depth:2
[94]=118,	-- depth:2
[95]=118,	-- depth:2
[96]=118,	-- depth:2
[97]=118,	-- depth:2
[98]=118,	-- depth:2
[99]=118,	-- depth:2
[100]=118,	-- depth:2
[92]=118,	-- depth:2
[101]=118,	-- depth:2
[81]=118,	-- depth:2
[79]=118,	-- depth:2
[61]=118,	-- depth:2
[62]=118,	-- depth:2
[63]=118,	-- depth:2
[64]=118,	-- depth:2
[65]=118,	-- depth:2
[66]=118,	-- depth:2
[67]=118,	-- depth:2
[68]=118,	-- depth:2
[80]=118,	-- depth:2
[69]=118,	-- depth:2
[71]=118,	-- depth:2
[72]=118,	-- depth:2
[73]=118,	-- depth:2
[74]=118,	-- depth:2
[75]=118,	-- depth:2
[76]=118,	-- depth:2
[77]=118,	-- depth:2
[78]=118,	-- depth:2
[70]=118,	-- depth:2
[102]=118,	-- depth:2
[103]=118,	-- depth:2
[104]=118,	-- depth:2
[129]=174,	-- depth:2
[130]=174,	-- depth:2
[131]=174,	-- depth:2
[132]=174,	-- depth:2
[133]=174,	-- depth:2
[134]=174,	-- depth:2
[135]=174,	-- depth:2
[136]=174,	-- depth:2
[128]=174,	-- depth:2
[137]=174,	-- depth:2
[139]=174,	-- depth:2
[140]=174,	-- depth:2
[141]=174,	-- depth:2
[142]=174,	-- depth:2
[143]=174,	-- depth:2
[144]=174,	-- depth:2
[145]=174,	-- depth:2
[146]=174,	-- depth:2
[138]=174,	-- depth:2
[127]=174,	-- depth:2
[126]=174,	-- depth:2
[125]=174,	-- depth:2
[105]=118,	-- depth:2
[106]=118,	-- depth:2
[107]=118,	-- depth:2
[108]=118,	-- depth:2
[109]=118,	-- depth:2
[110]=118,	-- depth:2
[111]=118,	-- depth:2
[112]=118,	-- depth:2
[113]=118,	-- depth:2
[114]=118,	-- depth:2
[115]=118,	-- depth:2
[116]=118,	-- depth:2
[117]=118,	-- depth:2
[235]=179,	-- depth:2
[120]=174,	-- depth:2
[121]=174,	-- depth:2
[122]=174,	-- depth:2
[123]=174,	-- depth:2
[124]=174,	-- depth:2
[148]=174,	-- depth:2
[236]=179,	-- depth:2
},
hurt_cap={
{},
{index=1,min_world_level=301,max_world_level=400,},
{index=2,min_world_level=401,max_world_level=500,},
{index=3,min_world_level=501,max_world_level=550,},
{index=4,min_world_level=551,max_world_level=600,},
{index=5,min_world_level=601,max_world_level=650,},
{index=6,min_world_level=651,max_world_level=700,},
{index=7,min_world_level=701,max_world_level=750,},
{index=8,min_world_level=751,max_world_level=800,},
{index=9,min_world_level=801,max_world_level=850,},
{index=10,min_world_level=851,max_world_level=900,},
{index=11,min_world_level=901,max_world_level=950,},
{index=12,min_world_level=951,max_world_level=1000,},
{index=13,min_world_level=1001,max_world_level=1050,},
{index=14,min_world_level=1051,max_world_level=1100,},
{index=15,min_world_level=1101,max_world_level=1150,},
{index=16,min_world_level=1151,max_world_level=1200,},
{index=17,min_world_level=1201,max_world_level=99999,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=1,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=2,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=3,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=4,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=5,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=6,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=7,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,},
{seq=8,}
},

hurt_cap_meta_table_map={
[112]=4,	-- depth:1
[113]=5,	-- depth:1
[114]=6,	-- depth:1
[116]=8,	-- depth:1
[117]=9,	-- depth:1
[118]=10,	-- depth:1
[119]=11,	-- depth:1
[120]=12,	-- depth:1
[121]=13,	-- depth:1
[122]=14,	-- depth:1
[123]=15,	-- depth:1
[115]=7,	-- depth:1
[111]=3,	-- depth:1
[107]=17,	-- depth:1
[108]=18,	-- depth:1
[106]=16,	-- depth:1
[105]=123,	-- depth:2
[104]=122,	-- depth:2
[103]=121,	-- depth:2
[102]=120,	-- depth:2
[101]=119,	-- depth:2
[100]=118,	-- depth:2
[99]=117,	-- depth:2
[98]=116,	-- depth:2
[97]=115,	-- depth:2
[96]=114,	-- depth:2
[95]=113,	-- depth:2
[94]=112,	-- depth:2
[110]=2,	-- depth:1
[124]=106,	-- depth:2
[131]=95,	-- depth:3
[126]=108,	-- depth:2
[160]=124,	-- depth:3
[159]=105,	-- depth:3
[158]=104,	-- depth:3
[157]=103,	-- depth:3
[156]=102,	-- depth:3
[155]=101,	-- depth:3
[154]=100,	-- depth:3
[153]=99,	-- depth:3
[152]=98,	-- depth:3
[151]=97,	-- depth:3
[150]=96,	-- depth:3
[149]=131,	-- depth:4
[148]=94,	-- depth:3
[147]=111,	-- depth:2
[146]=110,	-- depth:2
[144]=126,	-- depth:3
[143]=107,	-- depth:2
[128]=146,	-- depth:3
[129]=147,	-- depth:3
[130]=148,	-- depth:4
[132]=150,	-- depth:4
[133]=151,	-- depth:4
[134]=152,	-- depth:4
[125]=143,	-- depth:3
[93]=129,	-- depth:4
[137]=155,	-- depth:4
[138]=156,	-- depth:4
[139]=157,	-- depth:4
[140]=158,	-- depth:4
[141]=159,	-- depth:4
[142]=160,	-- depth:4
[136]=154,	-- depth:4
[135]=153,	-- depth:4
[81]=135,	-- depth:5
[90]=144,	-- depth:4
[51]=141,	-- depth:5
[50]=140,	-- depth:5
[49]=139,	-- depth:5
[48]=138,	-- depth:5
[47]=137,	-- depth:5
[46]=136,	-- depth:5
[45]=81,	-- depth:6
[44]=134,	-- depth:5
[43]=133,	-- depth:5
[42]=132,	-- depth:5
[41]=149,	-- depth:5
[40]=130,	-- depth:5
[39]=93,	-- depth:5
[38]=128,	-- depth:4
[36]=90,	-- depth:5
[35]=125,	-- depth:4
[34]=142,	-- depth:5
[20]=38,	-- depth:5
[21]=39,	-- depth:6
[22]=40,	-- depth:6
[23]=41,	-- depth:6
[24]=42,	-- depth:6
[25]=43,	-- depth:6
[52]=34,	-- depth:6
[26]=44,	-- depth:6
[28]=46,	-- depth:6
[29]=47,	-- depth:6
[30]=48,	-- depth:6
[31]=49,	-- depth:6
[32]=50,	-- depth:6
[33]=51,	-- depth:6
[27]=45,	-- depth:7
[53]=35,	-- depth:5
[54]=36,	-- depth:6
[56]=20,	-- depth:6
[76]=22,	-- depth:7
[77]=23,	-- depth:7
[78]=24,	-- depth:7
[79]=25,	-- depth:7
[80]=26,	-- depth:7
[161]=53,	-- depth:6
[75]=21,	-- depth:7
[82]=28,	-- depth:7
[84]=30,	-- depth:7
[85]=31,	-- depth:7
[86]=32,	-- depth:7
[87]=33,	-- depth:7
[88]=52,	-- depth:7
[89]=161,	-- depth:7
[83]=29,	-- depth:7
[92]=56,	-- depth:7
[74]=92,	-- depth:8
[71]=89,	-- depth:8
[57]=75,	-- depth:8
[58]=76,	-- depth:8
[59]=77,	-- depth:8
[60]=78,	-- depth:8
[61]=79,	-- depth:8
[62]=80,	-- depth:8
[72]=54,	-- depth:7
[63]=27,	-- depth:8
[65]=83,	-- depth:8
[66]=84,	-- depth:8
[67]=85,	-- depth:8
[68]=86,	-- depth:8
[69]=87,	-- depth:8
[70]=88,	-- depth:8
[64]=82,	-- depth:8
[162]=72,	-- depth:8
},
other_default_table={open_level=300,pop_view_area="367,212|325,212|325,178|367,178",boss_scene_id=1003,world_level_limit=9999,show_reward={[0]=item_table[40],[1]=item_table[1],[2]=item_table[14],[3]=item_table[27],[4]=item_table[41],[5]=item_table[42]},alert_reward={[0]=item_table[40],[1]=item_table[1],[2]=item_table[14],[3]=item_table[27],[4]=item_table[41],[5]=item_table[42]},reward_rule="<color=#99ffbb>伤害榜</color>上的玩家参与击杀首领：伤害第一的仙境成员获得<color=#99ffbb>归属奖励</color>。其他仙境成员获得<color=#99ffbb>参与奖励</color>。参与的玩家可开启宝箱,打开宝箱有概率获得高品质道具！",send_timer=5,boss_reward_tips_des="1.仙图首领死亡时，个人伤害榜上的玩家在首领死亡后可以获得奖励：仙境伤害排名第一的获得<color=#99ffbb>归属奖励</color>跟其他仙境获得<color=#99ffbb>参与奖励</color>\n2.可前往其他仙境抢夺首领获得奖励",},

boss_hurt_default_table={},

boss_default_table={index=1,scene_id=1003,boss_id=22001,born_pos_x=403,born_pos_y=236,box_consume=288,box_name="花妖仙子",guishu_id="",},

reward_cfg_default_table={boss_id=22001,level_min=1,level_max=389,show_reward_item_1={[0]=item_table[40],[1]=item_table[43],[2]=item_table[44],[3]=item_table[45],[4]=item_table[46],[5]=item_table[47],[6]=item_table[41],[7]=item_table[42]},join_reward_item={[0]=item_table[43],[1]=item_table[44],[2]=item_table[45],[3]=item_table[46],[4]=item_table[47],[5]=item_table[48],[6]=item_table[49],[7]=item_table[50],[8]=item_table[51],[9]=item_table[52],[10]=item_table[41],[11]=item_table[42]},rate_id="",guishu_id="",},

hurt_cap_default_table={seq=0,index=0,min_world_level=1,max_world_level=300,}

}

