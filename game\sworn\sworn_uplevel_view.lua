function SwornView:InitSwornUplevelView()
    self.up_select_hole_index = -1
    self.up_select_hole_data = nil
    self.do_up_level_ani = false

    XUI.AddClickEventListener(self.node_list["btn_uplevel"], BindTool.Bind(self.OnClickSuitUplevel, self))
    XUI.AddClickEventListener(self.node_list["btn_ul_select_item"], BindTool.Bind(self.OnClickUpLevelShowItem, self))

    if self.uplevel_equip_list == nil then
        self.uplevel_equip_list = {}
        local node_num = self.node_list["uplevel_equip_list"].transform.childCount
        for i = 0, node_num - 1 do
            self.uplevel_equip_list[i] = JlpUpEquipRender.New(self.node_list["uplevel_equip_list"]:FindObj("equip_" .. i + 1))
            self.uplevel_equip_list[i]:SetIndex(i)
            self.uplevel_equip_list[i]:SetCellClickCallBack(BindTool.Bind(self.OnSelectUplevelCallBack, self))
        end
    end

    if self.up_need_item == nil then
        self.up_need_item = ItemCell.New(self.node_list["uplevel_need_item"])
    end
end

function SwornView:SwornUplevelViewReleaseCallBack()
    if self.uplevel_equip_list then
        for k, v in pairs(self.uplevel_equip_list) do
            v:DeleteMe()
        end
        self.uplevel_equip_list = nil
    end

    if self.up_need_item then
        self.up_need_item:DeleteMe()
        self.up_need_item = nil
    end

    self.up_select_hole_index = nil
    self.up_select_hole_data = nil

    self:SwornUpLevelOneKeyStop()
    self.do_up_level_ani = nil
end

function SwornView:OnSelectUplevelCallBack(cell, ignore_stop_ani)
    if cell == nil then
        return
    end

    local data = cell:GetData()
    if data == nil then
        return
    end

    local hole = data.hole
    if hole == self.up_select_hole_index and self.up_select_hole_data == data then
        return
    end

    self.up_select_hole_index = hole
    for k,v in pairs(self.uplevel_equip_list) do
        v:FlushSelectHL(k == hole)
    end

    self:FlushUpLevelItem()

    local ignore_stop_ani = ignore_stop_ani or false
    if not ignore_stop_ani then
        self:SwornUpLevelOneKeyStop()
    else
        if self.do_up_level_ani then
            self:SwornUpLevelUpAni()
        end
    end
end

function SwornView:FlushUpLevelView(need_jump_hole)
    local suit_index = self:GetSelectSuitIndex()
    local equip_list = SwornWGData.Instance:GetSuitHoleList(suit_index)
    for k,v in pairs(self.uplevel_equip_list) do
        v:SetData(equip_list[k])
    end

    -- if need_jump_hole then
        local jump_hole = SwornWGData.Instance:GetSuitUpLevelJumpHole(suit_index, self.up_select_hole_index)
        if jump_hole ~= self.up_select_hole_index then
            local cell = self.uplevel_equip_list[jump_hole]
            if cell then
                self:OnSelectUplevelCallBack(cell, true)
                return
            end
        end
    -- end

    self:FlushUpLevelItem()

    if self.do_up_level_ani then
        self:SwornUpLevelUpAni()
    end
end

function SwornView:FlushUpLevelItem()
    local cell = self.uplevel_equip_list[self.up_select_hole_index]
    if cell and cell.data ~= nil then
        self.up_select_hole_data = cell.data
    else
        self.up_select_hole_data = nil
        return
    end

    local suit_index = self:GetSelectSuitIndex()
    local level = self.up_select_hole_data.level
    local max_level = SwornWGData.Instance:GetMaxLevel(suit_index)
    local level_cfg = SwornWGData.Instance:GetEquipUpLevelCfg(suit_index, level)
    if level_cfg == nil then
        return
    end

    local is_can_uplevel = false
    if level >= max_level then
        XUI.SetButtonEnabled(self.node_list["btn_uplevel"], false)
        self.up_need_item:SetFlushCallBack(nil)
    else
        local has_num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.cost_item_id)
        is_can_uplevel = has_num >= level_cfg.cost_item_num
        local color = (not is_can_uplevel) and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
        local str = has_num .. "/" .. level_cfg.cost_item_num
        self.up_need_item:SetFlushCallBack(function ()
            self.up_need_item:SetRightBottomColorText(str, color)
            self.up_need_item:SetRightBottomTextVisible(true)
        end)

        XUI.SetButtonEnabled(self.node_list["btn_uplevel"], true)
    end

    self.up_need_item:SetData({item_id = level_cfg.cost_item_id})
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.up_select_hole_data.item_id)
    if item_cfg ~= nil then
        self.node_list["ul_cur_item_bg"].image:LoadSpriteAsync(ResPath.GetCommon("a2_sc_btn_bg_" .. item_cfg.color))
        self.node_list["ul_select_item_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
        
        local star_str = CommonDataManager.GetAncientNumber(self.up_select_hole_data.star_level)
        local item_name = string.format(Language.Sworn.EquipStarName, star_str, item_cfg.name)
        local next_str = item_name .. string.format(Language.Sworn.EquipLevel, level)
        self.node_list["ul_cur_item_name"].text.text = next_str
    end

    self.node_list["btn_uplevel_red"]:SetActive(is_can_uplevel)
end

--装备升级
function SwornView:OnClickSuitUplevel()
    -- if self.up_select_hole_data == nil then
    --     return
    -- end

    -- local level = self.up_select_hole_data.level
    -- local suit_index = self:GetSelectSuitIndex()
    -- local level_cfg = SwornWGData.Instance:GetEquipUpLevelCfg(suit_index, level)
    -- if level_cfg == nil then
    --     return
    -- end


    -- local has_num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.cost_item_id)
    -- if has_num < level_cfg.cost_item_num then
    --     TipWGCtrl.Instance:OpenItemTipGetWay({item_id = level_cfg.cost_item_id})
    --     return
    -- end

    -- SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_EQUIP_LEVEL_UP, self:GetSelectSuitIndex(), self.up_select_hole_index)
    -- self:UpSucessEffect(self.node_list["uplevel_effect_node"], UIEffectName.s_shengji)

    if self:IsSwornUpLevelCanUp(true) then
        if self.do_up_level_ani then
            self:SwornUpLevelOneKeyStop()
        else
            self.do_up_level_ani = true
            SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_EQUIP_LEVEL_UP, self:GetSelectSuitIndex(), self.up_select_hole_index)
            self:UpSucessEffect(self.node_list["uplevel_effect_node"], UIEffectName.s_shengji)
            self.node_list.btn_uplevel_text.text.text = Language.Sworn.UpLevelOneKeyStop
        end
    end
end

function SwornView:OnClickUpLevelShowItem()
    if self.up_select_hole_data == nil then
        return
    end

    local is_act = self.up_select_hole_data.star_level > 0
    TipWGCtrl.Instance:OpenItem({item_id = self.up_select_hole_data.item_id}, is_act and ItemTip.FROM_SWORN_EQUIP or ItemTip.FROM_NORMAL)
end

----------------------------------一键升级-------------------------------------
function SwornView:SwornUpLevelUpAni()
    if self:IsSwornUpLevelCanUp(false) then
        if self.do_up_level_ani then
            ReDelayCall(self, function()
                if self.do_up_level_ani then
                    SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.JIEYI_OPERATE_TYPE_EQUIP_LEVEL_UP, self:GetSelectSuitIndex(), self.up_select_hole_index)
                    self:UpSucessEffect(self.node_list["uplevel_effect_node"], UIEffectName.s_shengji)
                end
             end, 0.2, "sworn_auto_up_level")
        end
    else
        self:SwornUpLevelOneKeyStop()
    end
end

function SwornView:IsSwornUpLevelCanUp(need_show_tip)
    if self.up_select_hole_data == nil then
        return
    end

    local level = self.up_select_hole_data.level
    local suit_index = self:GetSelectSuitIndex()
    local level_cfg = SwornWGData.Instance:GetEquipUpLevelCfg(suit_index, level)
    if level_cfg == nil then
        return false
    end

    local max_level = SwornWGData.Instance:GetMaxLevel(suit_index)
    if level >= max_level then
        return false
    end

    local has_num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.cost_item_id)
    if has_num < level_cfg.cost_item_num then
        if need_show_tip then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = level_cfg.cost_item_id})
        end

        return false
    end

    return true
end

function SwornView:SwornUpLevelOneKeyStop()
    if self.node_list.btn_uplevel_text then
        self.node_list.btn_uplevel_text.text.text = Language.Sworn.UpLevelOneKey
    end

    self.do_up_level_ani = false
end

-------------------------------JlpUpEquipRender-------------------------------
JlpUpEquipRender = JlpUpEquipRender or BaseClass(BaseRender)
function JlpUpEquipRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_cell_click"], BindTool.Bind(self.OnClickUpCellBtn, self))
end

function JlpUpEquipRender:__delete()
    self.up_click_callback = nil
end

function JlpUpEquipRender:SetCellClickCallBack(call_back)
    self.up_click_callback = call_back
end

function JlpUpEquipRender:OnClickUpCellBtn()
    if self.data == nil then
        return
    end
    
    if self.data.star_level < 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Sworn.TabErrorDesc)
    else
        if self.up_click_callback then
            self.up_click_callback(self)
        end
    end
end

function JlpUpEquipRender:OnFlush()
    if self.data == nil then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg ~= nil then
        self.node_list["up_cell_bg"].image:LoadSpriteAsync(ResPath.GetCommon("a2_sc_btn_bg_" .. item_cfg.color))
        self.node_list["icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    self.node_list["equip_level"].text.text = string.format(Language.Sworn.EquipLevel, self.data.level)
    self.node_list["equip_star"].text.text = string.format(Language.Sworn.EquipStar, CommonDataManager.GetAncientNumber(self.data.star_level))

    XUI.SetGraphicGrey(self.node_list["icon"], self.data.star_level < 1)
    XUI.SetGraphicGrey(self.node_list["up_cell_bg"], self.data.star_level < 1)

    local is_can_uplevel = SwornWGData.Instance:GetHoleUpLevelRemind(self.data.suit, self.data.hole)
    self.node_list["remind"]:SetActive(is_can_uplevel)
end

function JlpUpEquipRender:FlushSelectHL(is_select)
    self.node_list["img_hl"]:SetActive(is_select)
end