WushuangFbLogic = WushuangFbLogic or BaseClass(CommonFbLogic)

function WushuangFbLogic:__init()
	self.open_view = false
end

function WushuangFbLogic:__delete()

end

function WushuangFbLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- XuiBaseView.CloseAllView()
end

function WushuangFbLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function WushuangFbLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseFuBenNextView()
	if RoleWGData.Instance.role_vo.level > 1 then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Daily, "daily_wushuang")
	end
end


function WushuangFbLogic:IsRoleEnemy(target_obj, main_role)
	if target_obj:GetType() == SceneObjType.Role then			-- 同一边
		return false, Language.Fight.Side
	end
	return true
end

-- 是否是挂机打怪的敌人
function WushuangFbLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end

function WushuangFbLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
end