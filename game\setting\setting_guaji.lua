local guaji_card_1 = 22537
local guaji_card_2 = 22538
local guajika_seq = 1008

function SettingView:InitGuaJi()
	local data = SkillWGData.Instance:GetSkillListByType(1)
	for i = 1, 7 do
		if data[i] then
			self.node_list["ph_skill_icon"..i].image:LoadSprite(ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(data[i].skill_id)))
		else
			self.node_list["ph_skill_icon"..i]:SetActive(false)
		end
		self.node_list["ph_skill_itemrender"..i].button:AddClickListener(BindTool.Bind1(self.SelectSkillCellCallBack, self))
	end
	for i = 1, self.GUAJI_OPTION_COUNT do
		if self.node_list["layout_guaji_option"..i] then
			XUI.AddClickEventListener(self.node_list["layout_guaji_option"..i], BindTool.Bind(self.OnClickGuijiSetting, self, i))
		end
	end

	self:RefreshCheckGuaJi()

	self.pop_alert = Alert.New()

	-- XUI.AddClickEventListener(self.node_list["btn_add_time"], BindTool.Bind(self.OnClickAddTime, self))
	local open_form = SettingWGData.Instance:GetViewOpenForm()
	if open_form ~= nil and open_form == 1 then
		self:OnClickAddTime()
		SettingWGData.Instance:SetViewOpenForm(nil)
	end

	local coin_cost = FuhuoWGCtrl.Instance:GetFuhuoGold()
	local free_fuhuo_count = RechargeWGData.Instance:GetTZJHOtherCfg("free_relive_count") or 0
	self.node_list["vip_tz_free_fuhuo"].text.text = string.format(Language.Setting.GuajiFreeFuhuo, free_fuhuo_count)
	self.node_list["guaji_option_txt11"].text.text = string.format(Language.Setting.GuajiOptionTxt11, coin_cost)

	XUI.AddClickEventListener(self.node_list["vip_tz_free_fuhuo"], BindTool.Bind(self.OnClickVipTzFreeFuHuo, self))

	-- local lilian_is_open = FunOpen.Instance:GetFunIsOpened(FunName.TianShenLiLianView)
	-- local desc_index = lilian_is_open and 2 or 1
	-- self.node_list["guaji_time_desc"].text.text = Language.OfflineRest.GuaJiTimeDesc[desc_index]
end

function SettingView:OnClickVipTzFreeFuHuo()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_month_card)
end

function SettingView:OnClickAddTime()
	local num1 = ItemWGData.Instance:GetItemNumInBagById(guaji_card_1)
	local num2 = ItemWGData.Instance:GetItemNumInBagById(guaji_card_2)
	if num1 == 0 and num2 == 0 then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = guaji_card_2})
		-- self.pop_alert:Open()
		-- local is_bind = 0
		-- -- local pay_money = 0
		-- if ShopWGData.Instance:IsHaveBindItem(guaji_card_1) then
		-- 	local price_info = ShopWGData.GetItemPrice(guaji_card_1)
		-- 	if RoleWGData.Instance.role_info.bind_gold >= price_info.bind_gold then
		-- 		is_bind = 1
		-- 		-- pay_money = price_info.bind_gold
		-- 	else
		-- 		-- pay_money = price_info.gold
		-- 	end
		-- 	local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(guajika_seq)
			
		-- 	self.pop_alert:SetLableString(string.format(Language.Setting.BuyGuaJiKa, shop_cfg.price))
		-- 	self.pop_alert:SetOkFunc(BindTool.Bind(self.OnClickBuyGuaJiKa, self, is_bind))
		-- 	return
		-- end
	end

	if OfflineRestWGData.Instance:GetRemainOfflineRestTime() >= OfflineRestWGData.OfflineTotalTime then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OfflineRest.OfflineTimeFull)
		return
	end

	if num2 == 1 and OfflineRestWGData.Instance:IsOverstep(guaji_card_2, 1) then
		OfflineRestWGCtrl.Instance:OpenOfflineOverstepView(guaji_card_2, 1)
		return
	elseif num2 == 1 and not OfflineRestWGData.Instance:IsOverstep(guaji_card_2, 1) then
		local index = ItemWGData.Instance:GetItemIndex(guaji_card_2)
		BagWGCtrl.Instance:SendUseItem(index, 1, 0, 0)
		return
	elseif num2 > 1 then 
		OfflineRestWGCtrl.Instance:OpenUserOfflineView(guaji_card_2)
		return
	end

	if num1 == 1 and OfflineRestWGData.Instance:IsOverstep(guaji_card_1, 1) then
		OfflineRestWGCtrl.Instance:OpenOfflineOverstepView(guaji_card_1, 1)	
	elseif num1 == 1 and not OfflineRestWGData.Instance:IsOverstep(self.item_id, 1) then
		local index = ItemWGData.Instance:GetItemIndex(guaji_card_1)
		BagWGCtrl.Instance:SendUseItem(index, 1, 0, 0)
	elseif num1 > 1 then
		OfflineRestWGCtrl.Instance:OpenUserOfflineView(guaji_card_1)
	end
end

function SettingView:OnClickBuyGuaJiKa(is_bind)
	ShopWGCtrl.Instance:SendShopBuy(guaji_card_2, 1, 0, is_bind, guajika_seq)
end

-- 挂机设置项
function SettingView:OnClickGuijiSetting(index)
	local img_hook =  self.node_list["img_guaji_hook" .. index]
	local flag = not img_hook:GetActive()
	if index >= 8 and index <= self.GUAJI_OPTION_COUNT then
		self.node_list["img_guaji_check" .. index]:SetActive(not flag)
	end
	-- img_hook:setVisible(flag)
	img_hook:SetActive(flag)
	-- self.guaji_set_flag[33 - index] = flag and 1 or 0
	-- for _,v in pairs(FixBugSettting) do
	-- 	if v == SettingPanel2[index] then
	-- 		SettingWGData.Instance:SetBugFixRecordValue(SettingPanel2[index], flag)
	-- 	end
	-- end
	-- local data = bit:b2d(self.guaji_set_flag)
	-- SettingWGData.Instance:SetDataByIndex(HOT_KEY.SYS_SETTING_2, data)
	SettingWGData.Instance:SetSettingData1(SettingPanel2[index], flag, true)
	-- SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.SYS_SETTING_2, data)
	-- GlobalEventSystem:Fire(SettingEventType.GUAJI_SETTING_CHANGE, index, flag)
end

function SettingView:SelectSkillCellCallBack(cell)
	SettingWGCtrl.Instance:OpenGuaJiSkillList()
end

function SettingView:RefreshGuaJi()
	self:RefreshCheckGuaJi()
end

function SettingView:FlushSkill()
	local data = SkillWGData.Instance:GetSkillListByType(1)
	for i=1,7 do
		if data[i] then
			self.node_list["ph_skill_icon"..i].image:LoadSprite(ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(data[i].skill_id)))
			self.node_list["ph_skill_icon"..i]:SetActive(true)
			local flag = SettingWGData.Instance:GetSettingData(SettingPanel3[i])
			XUI.SetGraphicGrey(self.node_list["ph_skill_icon"..i], not flag)
		else
			self.node_list["ph_skill_icon"..i]:SetActive(false)
		end
	end
end

function SettingView:RefreshCheckGuaJi()
	for i,v in pairs(SettingPanel2) do
		local flag = SettingWGData.Instance:GetSettingData(v)
		if i >= 8 and i <= self.GUAJI_OPTION_COUNT then
			self.node_list["img_guaji_hook"..i]:SetActive(flag)
			self.node_list["img_guaji_check" .. i]:SetActive(not flag)
		else
			self.node_list["img_guaji_hook"..i]:SetActive(flag or false)
		end
	end

	-- local remain_offline_rest_time = OfflineRestWGData.Instance.remain_offline_rest_time
	-- local time = OfflineRestWGData.Instance:GetTimeStr(remain_offline_rest_time)
	-- local hour = math.floor(remain_offline_rest_time / 3600)
	-- local time_str
	-- if hour >= 2 then
	-- 	time_str = ToColorStr(time, COLOR3B.DEFAULT_NUM)
	-- else
	-- 	time_str = ToColorStr(time, COLOR3B.D_RED)
	-- end 
	-- self.node_list["lbl_off_line_time"].text.text = time_str

	self:FlushSkill()
	self:FlushGroupActive()
end

function SettingView:FlushGroupActive()
	local sx_is_open = FunOpen.Instance:GetFunIsOpened(FunName.FightSoulView)
	local longzhu_is_open = FunOpen.Instance:GetFunIsOpened(FunName.LongZhuView)
	local wuxing_is_open = FunOpen.Instance:GetFunIsOpened(FunName.FiveElementsView)
	local wuxing_cangming_is_open = FunOpen.Instance:GetFunIsOpened(FunName.five_elements_cangming)
	local new_fight_mount_open = FunOpen.Instance:GetFunIsOpened(FunName.NewFightMountView)
	local tianshen_heji_open = FunOpen.Instance:GetFunIsOpened(FunName.tianshen_heji)
	local customized_suit_open = FunOpen.Instance:GetFunIsOpened(FunName.CustomizedSuitView)
	local gundam_is_open = FunOpen.Instance:GetFunIsOpened(FunName.MechaView)
	local esoterica_is_open = FunOpen.Instance:GetFunIsOpened(FunName.esoterica)
	local beasts_is_open = FunOpen.Instance:GetFunIsOpened(FunName.ControlBeastsView)

	local is_show_group_8 = sx_is_open or longzhu_is_open or wuxing_is_open or wuxing_cangming_is_open or new_fight_mount_open or tianshen_heji_open
							or customized_suit_open or esoterica_is_open or beasts_is_open
	self.node_list.group_8:SetActive(is_show_group_8)
	self.node_list.layout_guaji_option13:SetActive(sx_is_open)
	self.node_list.layout_guaji_option14:SetActive(longzhu_is_open)
	self.node_list.layout_guaji_option15:SetActive(wuxing_is_open)
	self.node_list.layout_guaji_option16:SetActive(wuxing_cangming_is_open)
	self.node_list.layout_guaji_option17:SetActive(new_fight_mount_open)
	self.node_list.layout_guaji_option19:SetActive(new_fight_mount_open)
	self.node_list.layout_guaji_option18:SetActive(tianshen_heji_open)
	self.node_list.layout_guaji_option20:SetActive(customized_suit_open)
	self.node_list.layout_guaji_option21:SetActive(gundam_is_open)
	self.node_list.layout_guaji_option22:SetActive(esoterica_is_open)
	self.node_list.layout_guaji_option24:SetActive(beasts_is_open)
end

function SettingView:DeleteSettingGuaji()
	if nil ~= self.grid_skill then
		self.grid_skill:DeleteMe()
		self.grid_skill = nil
	end

	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end
end


GuaJiSkillItemRender = GuaJiSkillItemRender or BaseClass(BaseRender)
function GuaJiSkillItemRender:__init()

end

function GuaJiSkillItemRender:__delete()

end

function GuaJiSkillItemRender:OnFlush()
	if self.data == nil then
		return
	end
	local path = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.skill_id))
	self.skill_icon:loadTexture(path, XUI.IS_PLIST)

	local data = SettingWGData.Instance:GetDataByIndex(HOT_KEY.GUAJI_SETTING_TYPE)
	local guaji_set_flag = bit:d2b(data)
	self.skill_icon:setGrey(guaji_set_flag[33 - (self.index + 10)] == 0)
end

function GuaJiSkillItemRender:ClickHandler()
	if self.click_callback then
		self.click_callback(self)
	end
end

function GuaJiSkillItemRender:CreateSelectEffect()

end