ZhuoGuiBossLogic = ZhuoGuiBossLogic or BaseClass(CommonFbLogic)

function ZhuoGuiBossLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function ZhuoGuiBossLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function ZhuoGuiBossLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.ZhuoGuiFuBen.BossTitle)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		FuBenPanelWGCtrl.Instance:OpenZhuoGuiBossScenceView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)
end

function ZhuoGuiBossLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

    FuBenPanelWGCtrl.Instance:CloseZhuoGuiBossScenceView()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
end