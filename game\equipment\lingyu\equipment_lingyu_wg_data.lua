EquipmentLingYuWGData = EquipmentLingYuWGData or BaseClass()

function EquipmentLingYuWGData:__init()
	if EquipmentLingYuWGData.Instance ~= nil then
		ErrorLog("[EquipmentLingYuWGData] attempt to create singleton twice!")
		return
	end

	EquipmentLingYuWGData.Instance = self

	-- 灵玉配置
    local lingyu_cfg = ConfigManager.Instance:GetAutoConfig("lingyuconfig_auto")

    self.lingyu_add_cfg = lingyu_cfg.lingyu_level
    self.lingyu_level_up_cfg = lingyu_cfg.lingyu_compose
    self.lingyu_menu_cfg = lingyu_cfg.lingyu_menu
    --self.stone_refine_stuff = ListToMap(lingyu_cfg.refine_stuff, "part_index", "seq")


    self.lingyu_cfg = lingyu_cfg.lingyu
    self.list_to_map_lingyu_cfg = ListToMap(lingyu_cfg.lingyu, "item_id")
    self.lingyu_equip_part_cfg = ListToMap(lingyu_cfg.lingyu_equip_part, "equip_part")
    self.old_lingyu_level_up_cfg = ListToMap(lingyu_cfg.lingyu_compose,"old_item_id")
    self.new_lingyu_level_up_cfg = ListToMap(lingyu_cfg.lingyu_compose,"new_item_id")
    self.lingyu_slot_open_limit = ListToMap(lingyu_cfg.lingyu_slot_open_limit, "part_index", "slot_index")
    self.first_lev_lingyu_price = lingyu_cfg.lingyu_compose[1].price

    self.total_lingyu_level = {}
	self.lingyu_active_star_level = {}
	self.lingyu_infos = {}
	self.refine_level = {}
	self.slot_remind_infos = {}

	self:CtreLingYuConversionTable()

	RemindManager.Instance:Register(RemindName.Equipment_LingYu_Inlay, BindTool.Bind(self.GetEquipLingYuRemindNum, self))
	self:RegisterLingYuRemindInBag(RemindName.Equipment_LingYu_Inlay)
end

function EquipmentLingYuWGData:__delete()
	EquipmentLingYuWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.Equipment_LingYu_Inlay)
end

function EquipmentLingYuWGData:CtreLingYuConversionTable()
	self.lingyu_table = {}
	for i,v in ipairs(self.lingyu_cfg) do
		local need_num = 0
		local item_id = v.item_id
		self.lingyu_table[item_id] = {}
		local lingyu_level_cfg = self:GetLingYuLevelUpCfg(item_id)
        local price = self.first_lev_lingyu_price
		if lingyu_level_cfg then
			if v.level <= 1 then
				price = need_num * self.first_lev_lingyu_price
			else
				need_num = self:LingYuConversion(item_id)
				price = need_num * self.first_lev_lingyu_price
			end

			self.lingyu_table[item_id].num = need_num
			self.lingyu_table[item_id].price = price
		else
			if v.level <= 1 then
				self.lingyu_table[item_id].num = 0
				self.lingyu_table[item_id].price = price
			end
		end
	end
end

-----------------------------------------------REMIND_START--------------------------------------------
function EquipmentLingYuWGData:RegisterLingYuRemindInBag(remind_name)
	local map = {}

	for _, v in pairs(self.list_to_map_lingyu_cfg) do
		map[v.item_id] = true
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

-- 灵玉总红点
function EquipmentLingYuWGData:GetEquipLingYuRemindNum()
	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZXJXQEquipBodyDataList()

	if IsEmptyTable(total_equip_body_data_list) then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_LINGYU, 0)
		return 0
	end

	for k, v in pairs(total_equip_body_data_list) do
		if self:GetEquipBodyLingYuRemind(v.seq) > 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_LINGYU, 1, function ()
				FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_lingyu)
				return true
			end)

			return 1
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_LINGYU, 0)
	return 0
end

function EquipmentLingYuWGData:GetEquipBodyLingYuRemind(equip_body_seq)
	local equip_list = self:GetEquipLingYuShowList(equip_body_seq)
	if IsEmptyTable(equip_list) then
		return 0
	end

	for k, v in pairs(equip_list) do
		if self:GetEquipLingYuRemind(v.index) > 0 then
			return 1
		end
	end


	if self:GetEquipLingYuActiveRemind(equip_body_seq) > 0 then
		return 1
	end

	return 0
end

-- 镶嵌加成红点
function EquipmentLingYuWGData:GetEquipLingYuActiveRemind(equip_body_seq)
	local is_open = FunOpen.Instance:GetFunIsOpened("equipment_lingyu")
	
	if not is_open then 
		return 0 
	end

	local lingyu_total_level = self:GetTotalLingYuLevel(equip_body_seq)
	local active_level = self:GetLingYuActiveLevel(equip_body_seq)
	local next_total_cfg = self:GetLingYuTotalStoneCfg(equip_body_seq, active_level, true)

	if next_total_cfg then
        if  next_total_cfg.lingyu_level <= lingyu_total_level
            and active_level <= next_total_cfg.lingyu_level then
		    return 1
        end
	end

	return 0
end

-- 灵玉单个装备的红点
function EquipmentLingYuWGData:GetEquipLingYuRemind(equip_body_index)
	for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
		local is_have_batter = self:GetLingYuSlotCanAddRemind(equip_body_index, slot_index)
		if is_have_batter then
			return 1
		end

		local is_can_upgrade = self:GetLingYuSlotCanUpGrade(equip_body_index, slot_index)
		if is_can_upgrade then
			return 1
		end
	end

	return 0
end

function EquipmentLingYuWGData:UpdateEquipLingYuSlotRemind()
    if IsEmptyTable(self.lingyu_infos) then
        return
    end

    for equip_body_index = 0, GameEnum.MAX_ROLE_EQUIP_NUM - 1 do
		self:UpdateEquipLingYuSlotInfo(equip_body_index)
    end
end

function EquipmentLingYuWGData:UpdateEquipLingYuSlotInfo(equip_body_index)
	self.slot_remind_infos[equip_body_index] = {}

	for index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
		self.slot_remind_infos[equip_body_index][index] = {}
		self.slot_remind_infos[equip_body_index][index].is_have_batter = self:GetLingYuSlotCanAddRemind(equip_body_index, index)
		self.slot_remind_infos[equip_body_index][index].is_can_upgrade = self:GetLingYuSlotCanUpGrade(equip_body_index, index)
	end
end

-- 更新共鸣主界面提醒
function EquipmentLingYuWGData:UpdateEquipLingYuAttrTip()
	local is_open = FunOpen.Instance:GetFunIsOpened("equipment_lingyu")

	if not is_open then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LINGYU_ATTR_ACTIVE, 0)
		return
	end

	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZXJXQEquipBodyDataList()

	if IsEmptyTable(total_equip_body_data_list) then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LINGYU_ATTR_ACTIVE, 0)
		return
	end

	for k, v in pairs(total_equip_body_data_list) do
		local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(v.seq)

		if unlock then
			local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(v.seq)

			if is_wear_equip then
				if self:GetEquipLingYuActiveRemind(v.seq) then
					local open_func = function ()
						FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_lingyu)
						RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.LINGYU_TIP, v.seq)
						RoleWGCtrl.Instance:OpenEquipAttr()
					end
		
					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LINGYU_ATTR_ACTIVE , 1, open_func)
					return
				end
			end
		end
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LINGYU_ATTR_ACTIVE, 0)
end
------------------------------------------------REMIND_END---------------------------------------------

-----------------------------------------------PROTOCOL_START------------------------------------------
function EquipmentLingYuWGData:SetLingYuInfo(protocol)
	self.lingyu_infos = protocol.lingyu_infos
	self.refine_level = protocol.refine_level

    self:UpdateEquipLingYuSlotRemind()
end

function EquipmentLingYuWGData:SetLingYuBaseInfo(protocol)
	self.total_lingyu_level = protocol.total_lingyu_level
	self.lingyu_active_star_level = protocol.lingyu_active_star_level
	self:UpdateEquipLingYuAttrTip()
end

function EquipmentLingYuWGData:SetLingYuUpdate(protocol)
	local equip_body_index = protocol.equip_body_index
	self.refine_level[equip_body_index] = protocol.refine_level
	self.lingyu_infos[equip_body_index] = protocol.lingyu_info
	self:UpdateEquipLingYuSlotInfo(equip_body_index)
end

-- 获取灵玉总等级
function EquipmentLingYuWGData:GetTotalLingYuLevel(equip_body_index)
	return self.total_lingyu_level[equip_body_index] or 0
end

function EquipmentLingYuWGData:GetLingYuActiveLevel(equip_body_index)
	return self.lingyu_active_star_level[equip_body_index] or 0
end

function EquipmentLingYuWGData:GetLingYuActiveLevelData()
	return self.lingyu_active_star_level
end

--获取身上某个灵玉孔中的灵玉data
function EquipmentLingYuWGData:GetLingYuDataBySelectIndex(equip_body_index, select_index)
	return (self.lingyu_infos[equip_body_index] or {})[select_index] or {}
end

function EquipmentLingYuWGData:GetLingYuInfoListByIndex(equip_body_index)
	return self.lingyu_infos[equip_body_index] or {}
end

-- -- 获取已经镶嵌的灵玉的列表数据
-- function EquipmentLingYuWGData:GetLingYuInfo()
-- 	return self.lingyu_infos
-- end
------------------------------------------------PROTOCOL_END-------------------------------------------

-----------------------------------------------CFG_GET_START-------------------------------------------
-- 根据升级的物品ID获取灵玉升级配置
function EquipmentLingYuWGData:GetLingYuLevelUpCfg(new_item_id)
    return self.new_lingyu_level_up_cfg[new_item_id]
end

-- 根据装备部位索引获得灵玉类型
function EquipmentLingYuWGData:GetLingYuTypeByEquipPart(equip_body_index)
	local part_index = equip_body_index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    return (self.lingyu_equip_part_cfg[part_index] or {}).lingyu_type or 1
end

-- 获取限制灵玉配置
function EquipmentLingYuWGData:GetLimitLingYuOpenCfg(equip_body_index ,slot_index)
	local part_index = equip_body_index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
	return (self.lingyu_slot_open_limit[part_index] or {})[slot_index] or {}
end

-- 根据物品ID获取灵玉配置
function EquipmentLingYuWGData:GetLingYuCfgByItemId(item_id)
	return self.list_to_map_lingyu_cfg[item_id]
end
------------------------------------------------CFG_GET_END--------------------------------------------

-----------------------------------------------CAL_START-----------------------------------------------
-- 获取人物身上的装备灵玉数据列表(带remind功能的)
function EquipmentLingYuWGData:GetEquipLingYuShowList(equip_body_seq, not_need_sort)
	local data_list = EquipWGData.Instance:GetDataList()		--身上穿戴的装备列表

	local equip_data = {}

	if not data_list then
		return equip_data
	end

	local seq = 1
	local start_equip_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
    local end_equip_body_index = equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + GameEnum.EQUIP_INDEX_XIANZHUO

	for k, v in pairs(data_list) do
		if v.index >= start_equip_index and v.index <= end_equip_body_index then
			equip_data[seq] = v
			equip_data[seq].lingyu = self.lingyu_infos[k]
			seq = seq + 1
		end
	end

	if not_need_sort then
		return equip_data
	end

	return EquipmentLingYuWGData.SortEquipTabForAwake(equip_data)
end

--灵玉等级，每级需要多少个
--根据等级换算成1级灵玉
function EquipmentLingYuWGData:LingYuConversion(item_id)
	local need_num = 1
    local function calc_num(stone_id)
        local new_cfg = self:GetLingYuLevelUpCfg(stone_id)
        if new_cfg then
            need_num = need_num * new_cfg.need_num
            calc_num(new_cfg.old_item_id)
        end
    end

    calc_num(item_id)
    return need_num > 1 and need_num or 0
end

function EquipmentLingYuWGData:GetLingYuTotalStoneCfg(equip_body_seq, level, is_next)
	local curr_cfg = nil
	local next_cfg = nil

	local max_level_limit = EquipBodyWGData.Instance:GetLingYuLevelLimit(equip_body_seq)
    local max_level = math.min(level, max_level_limit)

	for k, v in pairs(self.lingyu_add_cfg) do
		if v.lingyu_level > max_level then
			if v.lingyu_level <= max_level_limit then
				next_cfg = v
			end

			break
		end

		curr_cfg = v
	end

	-- for k, v in pairs(self.lingyu_add_cfg) do
	-- 	if v.lingyu_level <= level then
	-- 		curr_cfg = v
	-- 		next_cfg = self.lingyu_add_cfg[k + 1]
	-- 	end
	-- end

	if is_next then
		if nil == curr_cfg then
			return self.lingyu_add_cfg[1]
		else
			return next_cfg
		end
	end

	return curr_cfg
end

function EquipmentLingYuWGData:GetLingYuSlotCanAddRemind(equip_body_index, slot_index)
    if not equip_body_index or not slot_index then
        return false
    end

    local is_open = self:LingYuSlotIsOpen(equip_body_index, slot_index)
    if not is_open then
        return false
    end

    local list = self:GetLingYuInlayAttrType(equip_body_index, slot_index)
    for k, v in pairs(list) do
        if v.is_show_remind then
            return true
        end
    end

    return false
end

-- 灵玉槽是否开启
function EquipmentLingYuWGData:LingYuSlotIsOpen(equip_body_index ,slot_index)
    local slot_data = self:GetLingYuDataBySelectIndex(equip_body_index, slot_index)
    return slot_data and slot_data.is_open == 1
end

--【修改标记】
-- 根据装备部位索引,装备孔索引 获取宝贝中的灵玉
function EquipmentLingYuWGData:GetLingYuInlayAttrType(equip_body_index, slot_index)
	local item_list = {}
    local lingyu_type = self:GetLingYuTypeByEquipPart(equip_body_index)
	local cur_item_data = self:GetLingYuDataBySelectIndex(equip_body_index, slot_index) 	--当前镶嵌的灵玉
	if cur_item_data == nil then
		return {}
	end

	local cur_lingyu_cfg = self:GetLingYuCfgByItemId(cur_item_data.item_id)
	local lingyu_item_info = ItemWGData.Instance:GetEquipLingYuItemList(lingyu_type)
	for k, v in pairs(lingyu_item_info) do
		local item_cfg = self:GetLingYuCfgByItemId(v.item_id)
		if item_cfg then
			v.level = item_cfg.level
			v.sort_type = item_cfg.sort_type
			v.lingyu_type = item_cfg.lingyu_type
			v.goto_shop = false

			if cur_lingyu_cfg then
				v.is_show_remind = cur_lingyu_cfg.level < v.level
			elseif cur_item_data.is_open and cur_item_data.item_id == 0 then
				v.is_show_remind = true
			else
				v.is_show_remind = false
			end

			item_list[#item_list + 1] = v
		end
	end
	return item_list
end

function EquipmentLingYuWGData:GetLingYuSlotCanUpGrade(equip_body_index, slot_index)
    local is_open = self:LingYuSlotIsOpen(equip_body_index, slot_index)
    if not is_open then
        return false
    end

    local is_inlay = self:LingYuSlotIsInlay(equip_body_index ,slot_index)
    if not is_inlay then
        return false
    end

    local need_price = self:GetLingYuUpgradePrice(equip_body_index, slot_index)

    return need_price <= 0
end

-- 灵玉槽是否镶嵌
function EquipmentLingYuWGData:LingYuSlotIsInlay(equip_body_index ,slot_index)
    local slot_data = self:GetLingYuDataBySelectIndex(equip_body_index, slot_index)
    return slot_data and slot_data.is_inlay
end

function EquipmentLingYuWGData:GetLingYuUpgradePrice(equip_body_index, select_index)
	local old_item_id = self:GetLingYuItemIdBySelectIndex(equip_body_index, select_index)  --当前灵玉id
	return self:ComputLingYuUpgradePrice(old_item_id)
end

--获取身上某个灵玉孔中的灵玉id
function EquipmentLingYuWGData:GetLingYuItemIdBySelectIndex(equip_body_index, select_index)
	local lingyu_slot_info = self:GetLingYuDataBySelectIndex(equip_body_index, select_index)
	return lingyu_slot_info.item_id
end

--计算升级灵玉花费的价格
function EquipmentLingYuWGData:ComputLingYuUpgradePrice(old_item_id)
	if not old_item_id or old_item_id <= 0 then
		return 999999999
	end

	local cfg = self:GetLingYuLevelUpCfgByOldId(old_item_id)
	--已经满级
	if cfg == nil then
		return 999999999
	end

	--目标需要的换算数量
	local new_item_id = cfg.new_item_id 			          --新灵玉id
	local new_conversion_list = self:GetLingYuConversionTableByid(new_item_id)
	if IsEmptyTable(new_conversion_list) then
		return 999999999
	end

	--当前灵玉孔的换算数量
	local cur_conversion_list = self:GetLingYuConversionTableByid(old_item_id)
	local cur_lingyu_cfg = self:GetLingYuCfgByItemId(old_item_id)
	local cur_num, cur_price = 0, 0

	cur_num = cur_conversion_list.num
	cur_price = cur_conversion_list.price

	--背包所有灵玉的换算数量
	local bag_conversion_list = ItemWGData.Instance:GetEquipLingYuNumAndValueByType(cur_lingyu_cfg.lingyu_type)
	if not new_conversion_list or IsEmptyTable(new_conversion_list) then
		return 999999999
	end

	local new_lingyu_item_price = 0
	local have_price = cur_price + bag_conversion_list.price
	if new_conversion_list.price > have_price then
		new_lingyu_item_price = new_conversion_list.price - have_price
	end

	return new_lingyu_item_price
end

--背包中装备索引index，灵玉孔
--注意，每个灵玉孔镶嵌的灵玉类型需要做筛选
function EquipmentLingYuWGData:GetLingYuListByLingYu(equip_body_index, slot_index)
    local lingyu_info_list = {}
    local lingyu_type = self:GetLingYuTypeByEquipPart(equip_body_index)
	local cur_item_data = self:GetLingYuDataBySelectIndex(equip_body_index, slot_index) 	--当前镶嵌的灵玉
	if IsEmptyTable(cur_item_data) or cur_item_data.is_open ~= 1 then
		return lingyu_info_list
	end

	local cur_lingyu_cfg = self:GetLingYuCfgByItemId(cur_item_data.item_id)
	local lingyu_item_info = ItemWGData.Instance:GetEquipLingYuItemList(lingyu_type)
	for k, data in pairs(lingyu_item_info) do
        local data_lingyu_cfg = self:GetLingYuCfgByItemId(data.item_id)
        if data_lingyu_cfg then
            if cur_item_data.is_inlay and cur_lingyu_cfg then
                if data_lingyu_cfg.level > cur_lingyu_cfg.level then
                    lingyu_info_list[#lingyu_info_list + 1] = data
                end
            else
                lingyu_info_list[#lingyu_info_list + 1] = data
            end
        end
    end

    if not IsEmptyTable(lingyu_info_list) then
        table.sort(lingyu_info_list, SortTools.KeyUpperSorter("item_id"))
    end

	return lingyu_info_list
end

function EquipmentLingYuWGData:GetLingYuMenu(equip_body_index)
	local lingyu_type = self:GetLingYuTypeByEquipPart(equip_body_index)
	local lingyu_menu_cfg = ConfigManager.Instance:GetAutoConfig("lingyuconfig_auto").lingyu_menu
	if IsEmptyTable(lingyu_menu_cfg) then
        return {}
    end

	for k, v in ipairs(lingyu_menu_cfg) do
		if lingyu_type == v.lingyu_type then
			return v
		end
	end

	return {}
end

function EquipmentLingYuWGData:GetLingYuActiveLevelChange()
	local active_level = self:GetLingYuActiveLevelData()

	if nil == self.old_lingyu_active_star_level then
		self.old_lingyu_active_star_level = active_level
		return false
	end

	local level_change = false
	if active_level then
        for k, v in pairs(active_level) do
            local old_lv = self.old_lingyu_active_star_level[k]

            if old_lv and old_lv < v then
                level_change = true 
            end
        end
    end

	self.old_lingyu_active_star_level = active_level

	-- if active_level and self.old_lingyu_active_star_level < self.lingyu_active_star_level then
	-- 	self.old_lingyu_active_star_level = active_level
	-- 	return true
	-- end

	return level_change
end

-- 一键镶嵌
function EquipmentLingYuWGData:GetAllLingYuOneKeyInlay(equip_body_seq, select_order)
    local temp_baoshi_list = {}
    local all_one_key_list = {}
    local equip_list = EquipWGData.Instance:GetDataList()
    for k, v in pairs(equip_list) do
		local order = EquipmentWGData.EquipBodySeqAndPartToEquipBodyIndex(equip_body_seq, v.index)
        if order == select_order then
            local list = self:GetLingYuOneKeyInlay(v.index, temp_baoshi_list)
            for _, data in pairs(list) do
                table.insert(all_one_key_list, data)
            end
        end
    end

    return all_one_key_list
end

function EquipmentLingYuWGData:GetLingYuOneKeyInlay(part_index, temp_use_baoshi_list)
    local one_key_list = {}
    local lingyu_type = self:GetLingYuTypeByEquipPart(part_index)
    local bag_lingyu_list = ItemWGData.Instance:GetEquipLingYuItemList(lingyu_type)
	if not temp_use_baoshi_list[lingyu_type] then
        temp_use_baoshi_list[lingyu_type] = {}
    end

	if IsEmptyTable(bag_lingyu_list) then
        return one_key_list
    end

    -- 镶嵌空槽
    local had_inlay_itemid_list = {}
    local temp_item_data = {item_id = 0, index = -1}
    local is_open, is_inlay
	for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
        local slot_data = self:GetLingYuDataBySelectIndex(part_index, slot_index)
        is_open = slot_data.is_open == 1
        is_inlay = slot_data.is_inlay
        -- 缓存
        if is_open and is_inlay then
            table.insert(had_inlay_itemid_list, {slot_index = slot_index, item_id = slot_data.item_id})
        elseif is_open and not is_inlay then
            temp_item_data = {item_id = 0, index = -1}
            for k,v in pairs(bag_lingyu_list) do
                local use_num = temp_use_baoshi_list[lingyu_type][v.index] or 0
                if v.num > use_num and v.item_id > temp_item_data.item_id then
                    temp_item_data = {item_id = v.item_id, index = v.index}
                end
            end

            if temp_item_data.item_id > 0 then
                local index = temp_item_data.index
                temp_use_baoshi_list[lingyu_type][index] = temp_use_baoshi_list[lingyu_type][index] and temp_use_baoshi_list[lingyu_type][index] + 1 or 1
                table.insert(one_key_list, {part_index = part_index, slot_index = slot_index, bag_index = index})
            end
        end
    end

    -- 排序
    SortTools.SortAsc(had_inlay_itemid_list, "item_id", "slot_index")

    local item_id = 0
    -- 替换上更高级
    for k,v in ipairs(had_inlay_itemid_list) do
        item_id = v.item_id
        temp_item_data = {item_id = 0, index = -1}
        for item_k, item_data in pairs(bag_lingyu_list) do
			local use_num = temp_use_baoshi_list[lingyu_type][v.index] or 0
            if item_data.num > use_num and item_data.item_id > item_id and item_data.item_id > temp_item_data.item_id then
                temp_item_data = {item_id = item_data.item_id, index = item_data.index}
            end
        end

        if temp_item_data.item_id > 0 then
            local index = temp_item_data.index
			temp_use_baoshi_list[lingyu_type][index] = temp_use_baoshi_list[lingyu_type][index] and temp_use_baoshi_list[lingyu_type][index] + 1 or 1
            table.insert(one_key_list, {part_index = part_index, slot_index = v.slot_index, bag_index = index})
        end
    end

    return one_key_list
end

-- 装备是否镶嵌灵石
function EquipmentLingYuWGData:GetEquipPartIsInlayLingYu(equip_body_index)
    for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
        if self:LingYuSlotIsInlay(equip_body_index ,slot_index) then
            return true
        end
    end

    return false
end

function EquipmentLingYuWGData:GetEquipPartIsAllInlayLingYu(equip_part)
    local open_num = 0
    local inlay_num = 0
    for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
        local slot_data = self:GetLingYuDataBySelectIndex(equip_part, slot_index)
        if slot_data and slot_data.is_open == 1 then
            open_num = open_num + 1
        end

        if slot_data and slot_data.is_inlay then
            inlay_num = inlay_num + 1
        end
    end

    return open_num == inlay_num
end

function EquipmentLingYuWGData:GetEquipPartMinLevelLingYu(equip_part)
    local min_level_index = -1
    local min_level = -1
    for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
        local slot_data = self:GetLingYuDataBySelectIndex(equip_part, slot_index)
        local next_stone = nil
        local stone_cfg = nil
        if slot_data.item_id and slot_data.item_id > 0 then
            next_stone = self:GetLingYuLevelUpCfgByOldId(slot_data.item_id)
            stone_cfg = self:GetLingYuCfgByItemId(slot_data.item_id)
        end

        if next_stone and stone_cfg then
            local cur_level = stone_cfg.level
            if min_level_index == -1 or (cur_level < min_level) then
                min_level_index = slot_index
                min_level = cur_level
            end
        end
    end

    return min_level_index
end

------------------------------------------------CAL_END------------------------------------------------




















































































function EquipmentLingYuWGData:GetLingYuSlotHaveBatterState(equip_part, slot_index)
    return (((self.slot_remind_infos or {})[equip_part] or {})[slot_index] or {}).is_have_batter
end

function EquipmentLingYuWGData:GetLingYuSlotCanUpgradeState(equip_part, slot_index)
    return (((self.slot_remind_infos or {})[equip_part] or {})[slot_index] or {}).is_can_upgrade
end
















-- 是否灵玉物品
function EquipmentLingYuWGData:IsLingYuItem(item_id)
	return self.list_to_map_lingyu_cfg[item_id] ~= nil
end

function EquipmentLingYuWGData:GetLingYuCfgByID(item_id)
    return self.list_to_map_lingyu_cfg[item_id]
end













function EquipmentLingYuWGData:GetLingYuConversionTableByid(item_id)
	return self.lingyu_table[item_id] or {}
end

-- 在背包内的灵玉转换成一级的总数 和 总价值
function EquipmentLingYuWGData:GetLingYuConversionTableInBag(lingyu_type)
	local conversion_list = {}
	local need_num = 0
	local price = self.first_lev_lingyu_price

	local bag_list = ItemWGData.Instance:GetEquipLingYuItemList(lingyu_type)
	for k,v in pairs(bag_list) do
		local lingyu_cfg = self:GetLingYuCfgByItemId(v.item_id)
		local cfg = self:GetLingYuConversionTableByid(v.item_id)
		need_num = need_num + (cfg.num * v.num)
		if lingyu_cfg.level <= 1 then
			need_num = (need_num + 1 * v.num)
		end
	end

	conversion_list.num = need_num
	conversion_list.price = price * need_num
	return conversion_list
end





-- 获取升级灵玉需要低级灵玉描述
function EquipmentLingYuWGData:CalcUpgradeNeedLingYuStr(old_item_id)
    if not old_item_id then
		return nil
	end

	local cfg = self:GetLingYuLevelUpCfgByOldId(old_item_id)
    if IsEmptyTable(cfg) then
        return nil
    end

    local new_item_id = cfg.new_item_id 			            --新灵玉id
    local new_lingyu_item_price = (cfg.price * cfg.need_num) - cfg.price		--新灵玉价格
    local expend_tab = {}

    while cfg ~= nil do
        local have_count = ItemWGData.Instance:GetItemNumInBagById(old_item_id)
        if have_count > 0 then
            local need_count = new_lingyu_item_price / cfg.price
            local offest_count = have_count - need_count
            local add_count = offest_count > 0 and need_count or have_count
            if add_count > 0 then
                table.insert(expend_tab, {item_id = old_item_id, count = add_count})
            end

            if offest_count > 0 then
                break
            end
            new_lingyu_item_price = new_lingyu_item_price - cfg.price * have_count
        end

        local temp_cfg = self:GetLingYuLevelUpCfg(old_item_id)
        if temp_cfg == nil then
            break
        end

        old_item_id = temp_cfg.old_item_id
        cfg = self:GetLingYuLevelUpCfgByOldId(old_item_id)
    end

    if IsEmptyTable(expend_tab) then
        return nil
    else
        local str = ""
        for i, v in ipairs(expend_tab) do
            local name = ItemWGData.Instance:GetItemName(v.item_id, nil, true)
            if i < #expend_tab then
                str = str .. name .. "*" .. v.count .. "，"
            else
                str = str .. name .. "*" .. v.count
            end
        end

        return str
    end

    return nil
end



function EquipmentLingYuWGData.SortEquipTabForAwake(data_list)
	if data_list == nil or IsEmptyTable(data_list) then
		return {}
	end

	local tmp = {}
	local index = 1
	for k,v in pairs(data_list) do
		tmp[index] = v
		index = index + 1
	end

	local sort_func = function ( a,b )
			if a == nil or b == nil or a == b then
				return false
			end
			return EquipmentWGData.GetSortIndex(a.index) < EquipmentWGData.GetSortIndex(b.index)
		end

	table.sort(tmp, sort_func)
	return tmp
end












-- function EquipmentLingYuWGData:GetOneRemindLingYuInfo()
-- 	local list_data = self:GetEquipLingYuShowList()
-- 	if IsEmptyTable(list_data) then
-- 		return -1
-- 	end

-- 	-- 优先提醒可替换镶嵌
-- 	for k, equip_data in ipairs(list_data) do
-- 		for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
-- 			local is_have_batter = self:GetLingYuSlotHaveBatterState(equip_data.index, slot_index)
-- 			if is_have_batter then
-- 				return equip_data.index
-- 			end
-- 		end
-- 	end

-- 	-- 可升级
-- 	for k, equip_data in ipairs(list_data) do
-- 		for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
-- 			local is_can_upgrade = self:GetLingYuSlotCanUpgradeState(equip_data.index, slot_index)
-- 			if is_can_upgrade then
-- 				return equip_data.index
-- 			end
-- 		end
-- 	end

-- 	return -1
-- end

function EquipmentLingYuWGData:GetEquipPartCanUpLingYu(equip_part)
    for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
		local is_can_upgrade = self:GetLingYuSlotCanUpgradeState(equip_part, slot_index)
		if is_can_upgrade then
			return true
		end
	end

    return false
end

function EquipmentLingYuWGData:GetEquipPartCanInlayLingYu(equip_part)
    for slot_index = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
        local is_have_batter = self:GetLingYuSlotHaveBatterState(equip_part, slot_index)
        if is_have_batter then
            return true
        end
    end

    return false
end



-- 拥有的同类型灵玉列表
function EquipmentLingYuWGData:GetInBagSameTypeLingYuList(equip_indx, slot_index)
    local lingyu_info_list = {}
    local lingyu_type = self:GetLingYuTypeByEquipPart(equip_indx)
	local lingyu_item_info = ItemWGData.Instance:GetEquipLingYuItemList(lingyu_type)

	for k, data in pairs(lingyu_item_info) do
        local data_lingyu_cfg = self:GetLingYuCfgByItemId(data.item_id)
        if data_lingyu_cfg then
            lingyu_info_list[#lingyu_info_list + 1] = data
        end
    end

    if not IsEmptyTable(lingyu_info_list) then
        table.sort(lingyu_info_list, SortTools.KeyUpperSorter("item_id"))
    end

	return lingyu_info_list
end







--根据灵玉ID获取灵玉属性
function EquipmentLingYuWGData:GetLingYuNatrue(item_id)
	local lingyu_cfg = self:GetLingYuCfgByItemId(item_id)
	if nil == lingyu_cfg then
		return "", ""
	end

	local attr_num = GameEnum.EQUIP_LINGYU_ATTR_NUM
	local name_str = ItemWGData.Instance:GetItemName(item_id, nil, true)
	local attr_str = ""
	for i = 1, attr_num do
		local type = lingyu_cfg["attr_type" .. i]
		local value = lingyu_cfg["attr_val" .. i]
		if value and value > 0 then
			local name = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
			--name = DeleteStrSpace(name)
			local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
			value = is_per and (value * 0.01 .. "%") or value
			local attr_desc = name .. "  " .. ToColorStr(value, COLOR3B.GREEN)
			local huan_hang = 1 < i and "\n" or ""
			attr_str = attr_str .. huan_hang .. attr_desc
		end
	end

	return name_str, attr_str
end

function EquipmentLingYuWGData:GetSingleLingYuAttrList(item_id)
    local attr_list = {}
    local lingyu_cfg = self:GetLingYuCfgByItemId(item_id)

    if IsEmptyTable(lingyu_cfg) then
        return attr_list
    end

    for j = 1, GameEnum.EQUIP_LINGYU_ATTR_NUM do
        local attr_id = lingyu_cfg["attr_type" .. j]
        local value = lingyu_cfg["attr_val" .. j]
        if value > 0 then
            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
            if not attr_list[attr_str] then
                attr_list[attr_str] = value
            else
                attr_list[attr_str] = attr_list[attr_str] + value
            end
        end
    end

    return attr_list
end



-- 根据现在物品ID获取灵玉升级配置
function EquipmentLingYuWGData:GetLingYuLevelUpCfgByOldId(old_item_id)
	return self.old_lingyu_level_up_cfg[old_item_id]
end