YinJiTurnTableRecordTip = YinJiTurnTableRecordTip or BaseClass(SafeBaseView)

function YinJiTurnTableRecordTip:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(742, 491)})
    self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "layout_yinji_turntable_record")
end

function YinJiTurnTableRecordTip:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function YinJiTurnTableRecordTip:LoadCallBack()
    self.toggle_index = 1
    self.node_list.title_view_name.text.text = Language.YinJiTurnTable.TitleName
    self.node_list.btn_all.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 1))
    self.node_list.btn_self.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 2))
    self.record_list = AsyncListView.New(YinJiTurnTableRecordItem, self.node_list["role_list"])
    -- if self.node_list.ph_xunbao_show_list then
    --     self.record_list_view = AsyncListView.New(RecordItem, self.node_list.ph_xunbao_show_list)
    -- end
    -- if self.node_list.ph_xunbao_person_list then
    --     self.record_person_list_view = AsyncListView.New(YinJiTurnTableRecordItem, self.node_list.ph_xunbao_person_list)
    -- end
end

function YinJiTurnTableRecordTip:OnClickSwitch(state)
    self.toggle_index = state
    self:FlushRecordList()
end

function YinJiTurnTableRecordTip:OnFlush(param_t)
    self:FlushRecordList()
end

function YinJiTurnTableRecordTip:FlushRecordList()
    local data_list = {}
    if self.toggle_index == 2 then
        data_list = EveryDayYinJiTurnTableWGData.Instance:GetPerSonalRecord()
    else
        data_list = EveryDayYinJiTurnTableWGData.Instance:GetServerRecord()
    end

    local is_show_list = not IsEmptyTable(data_list)
    if is_show_list then
        self.record_list:SetDataList(data_list)
    end
    self.node_list["role_list"]:SetActive(is_show_list)
    self.node_list["no_invite"]:SetActive(not is_show_list)
end


YinJiTurnTableRecordItem = YinJiTurnTableRecordItem or BaseClass(BaseRender)
function YinJiTurnTableRecordItem:__init()
    self.node_list["txt_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRecordItem,self))
end

function YinJiTurnTableRecordItem:__delete()
end

function YinJiTurnTableRecordItem:OnFlush()
	local index = self:GetIndex()
    local num = index % 2
    if(num == 0) then
        self.node_list["root_bg"].image:LoadSprite(ResPath.GetCommonImages("a2_zudui_lbdi"))
    else
        self.node_list["root_bg"].image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_5"))
    end
	if not self.data or not self.data.item_id then
		return
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if not item_cfg then
        print_error("Item is nil:",self.data.item_id)
        return 
    end
    self.node_list["time"].text.text = os.date("%m-%d  %X", self.data.draw_time)

    local color = ITEM_COLOR[item_cfg.color]
    
    --self.node_list["root_bg"].image.enabled = mark
    if self.data.role_name and self.data.role_name ~= "" then
        local role_name = self.data.role_name
        local str1 = string.format(Language.SiXiangCall.TxtRecord5, role_name)
        self.node_list["desc"].text.text = str1
    else
        local role_name = Language.Society.You
        local str1 = role_name..Language.SiXiangCall.TxtRecord4
        self.node_list["desc"].text.text = str1
    end

    local name = string.format(Language.SiXiangCall.TxtRecord1_2, color, item_cfg.name)
    local num = string.format(Language.SiXiangCall.TxtRecord1_3, color, self.data.num or 1)
    self.node_list["txt_btn"].text.text = name
    self.node_list["num"].text.text = num
end

function YinJiTurnTableRecordItem:OnClickRecordItem()
    if not self.data then
        return
    end
    TipWGCtrl.Instance:OpenItem({item_id = self.data.item_id})
end