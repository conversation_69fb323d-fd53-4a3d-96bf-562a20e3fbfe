

function ActXianQiJieFengView:InitXingTianLaiXiView()
	XUI.AddClickEventListener(self.node_list.jl_btn_shilian, BindTool.Bind(self.OnBtnShi<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,self))
	--XUI.AddClickEventListener(self.node_list.jl_btn_tip, BindTool.Bind(self.OnJLBtnTipClickHnadler,self))
	
	local theme_cfg = ActXianQiJieFengWGData.Instance:GetActivityThemeCfg(TabIndex.xianqi_jiefeng_laixi)
	if theme_cfg ~= nil then
		self.node_list.jl_tip_label.text.text = theme_cfg.rule_desc
	end

	self.jl_dl_reward_list = AsyncListView.New(XQJFCommonBiaoQianItemRender, self.node_list["jl_yi_reward_list"])
	self.jl_sl_reward_list = AsyncListView.New(XQJFCommonBiaoQianItemRender, self.node_list["jl_sl_reward_list"])

	self:FlushJiangLinView()
end

function ActXianQiJieFengView:ReleaseXingTianLaiXiView()
	if self.jl_sl_reward_list then
		self.jl_sl_reward_list:DeleteMe()
		self.jl_sl_reward_list = nil
	end

	if self.jl_dl_reward_list then
		self.jl_dl_reward_list:DeleteMe()
		self.jl_dl_reward_list = nil
	end

	-- if self.boss_model then
	-- 	self.boss_model:DeleteMe()
	-- 	self.boss_model = nil
	-- end

	if self.jianglin_count_down and CountDownManager.Instance:HasCountDown(self.jianglin_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.jianglin_count_down)
	end
	if self.boss_jianglin_time and CountDownManager.Instance:HasCountDown(self.boss_jianglin_time) then
		CountDownManager.Instance:RemoveCountDown(self.boss_jianglin_time)
	end
	if CountDownManager.Instance:HasCountDown("BeiZhan_common_count_douwn") then
        CountDownManager.Instance:RemoveCountDown("BeiZhan_common_count_douwn")
    end
end

-- function ActXianQiJieFengView:InitFlushDesc()
-- 	local cfg = ActXianQiJieFengWGData.Instance:GetJiangLinFlushTimeCfg()
-- 	local time_pramstr = {}
-- 	local index = 1
-- 	if cfg ~= nil then
-- 		for k,v in pairs(cfg) do
-- 			time_pramstr[index] = string.format("%s:%s", string.sub(v.refresh_time, 1, 2), string.sub(v.refresh_time, -2))
-- 			index = index + 1
-- 		end
-- 		self.node_list.jl_title_text.text.text = string.format(Language.XianQiJieFengAct.JianLinTip, time_pramstr[1])
-- 	end
-- end

function ActXianQiJieFengView:FlushJiangLinView()
	--local monster_cfg = ActXianQiJieFengWGData.Instance:GetJiangLinMonster_cfg()
	--local monster_info = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_cfg[#monster_cfg].monster_id]
	--self.node_list.jl_kill_boss_text.text.text = string.format(Language.XianQiJieFengAct.LaiXiStr10, monster_info.name,ActXianQiJieFengWGData.Instance:GetDayPassTimes())

	XUI.SetGraphicGrey(self.node_list.jl_btn_shilian, not ActXianQiJieFengWGData.Instance:IsInXingTianLaiXiActivity())
	self.node_list["jl_btn_name"].text.text = Language.TianShenRoad.GoShiLian
	self.node_list.jl_btn_redpoint:SetActive(ActXianQiJieFengWGData.Instance:IsShowLaiXiRedPoint() > 0)

	self:JLTimeCountDown()
	--self:InitFlushDesc()
	self:InitJiangLinRewardList()
	--self:InitJLBossModel()
	--self:FlushBossJlState()
end

-- function ActXianQiJieFengView:InitJLBossModel()
-- 	local other_cfg = ActXianQiJieFengWGData.Instance:GetJiangLinOtherCfg()
-- 	if self.boss_model == nil and other_cfg ~= nil and other_cfg[1] ~= nil then
-- 		self.boss_model = RoleModel.New()
-- 		local display_data = {
-- 			parent_node = self.node_list["BossRoleDisplay"],
-- 			camera_type = MODEL_CAMERA_TYPE.BASE,
-- 			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
-- 			rt_scale_type = ModelRTSCaleType.M,
-- 			can_drag = true,
-- 		}

-- 		self.boss_model:SetRenderTexUI3DModel(display_data)
-- 		-- self.boss_model:SetUI3DModel(self.node_list.BossRoleDisplay.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
-- 		self:AddUiRoleModel(self.boss_model)
-- 		self.boss_model:SetMainAsset(ResPath.GetMonsterModel(other_cfg[1].model_id))
-- 	end
-- end

function ActXianQiJieFengView:InitJiangLinRewardList()
	local cfg = ActXianQiJieFengWGData.Instance:GetJiangLinReward()
	if not cfg then
		return
	end

	local canyu_biaoqian = string.split(cfg.canyu_biaoqian, "|")
	local drop_biaoqian = string.split(cfg.drop_biaoqian, "|")

	local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(cfg.act_reward_item)
	local sl_list = {}
	for i=1, #reward_list do
		local item = {}
		item.reward = reward_list[i]
		if canyu_biaoqian[i + 1] and tonumber(canyu_biaoqian[i+1]) == 1 then
			item.biaoqian_name = "qfbz_shouci"
		end
		sl_list[#sl_list + 1] = item
	end
	self.jl_sl_reward_list:SetDataList(sl_list)

	local drop_reward_list = OperationActivityWGData.Instance:SortDataByItemColor(cfg.drop_reward_item)
	local dl_list = {}
	for i=1, #drop_reward_list do
		local item = {}
		item.reward = drop_reward_list[i]
		if drop_biaoqian[i + 1] and tonumber(drop_biaoqian[i+1]) == 1 then
			item.biaoqian_name = "qfbz_xiyou"
		end
		dl_list[#dl_list + 1] = item
	end
	self.jl_dl_reward_list:SetDataList(dl_list)
end

-- function ActXianQiJieFengView:OnJLBtnTipClickHnadler()
-- 	local role_tip = RuleTip.Instance
-- 	if role_tip then
-- 		local title,desc = ActXianQiJieFengWGData.Instance:GetActivityTip(TabIndex.xianqi_jiefeng_laixi)
-- 		if title ~= nil and desc ~= nil then
-- 			role_tip:SetTitle(title)
-- 			role_tip:SetContent(desc)
-- 		end
-- 	end
-- end

function ActXianQiJieFengView:OnBtnShiLianClickHnadler()
	if ActXianQiJieFengWGData.Instance:IsInXingTianLaiXiActivity() then
		ActXianQiJieFengWGData.Instance:SetLaiXiRedPoint()
		RemindManager.Instance:Fire(RemindName.XianQiJieFeng_LaiXi)
		RemindManager.Instance:Fire(RemindName.XianQiJieFeng)
		self.node_list.jl_btn_redpoint:SetActive(false)
		ActXianQiJieFengWGCtrl.Instance:GotoShiLian()
		self:Close()
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.XianQiJieFengAct.LaiXiStr11)
	end
end

--有效时间倒计时
function ActXianQiJieFengView:JLTimeCountDown()
	if self.jianglin_count_down and CountDownManager.Instance:HasCountDown(self.jianglin_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.jianglin_count_down)
	end
	
	self.jianglin_count_down = "jianglin_count_down"
	local invalid_time = ActXianQiJieFengWGData.Instance:GetActivityInValidTime(TabIndex.xianqi_jiefeng_laixi)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.jl_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.jianglin_count_down, BindTool.Bind1(self.UpdateJLCountDown, self), BindTool.Bind1(self.OnJLComplete, self), invalid_time, nil, 1)
	end
end

function ActXianQiJieFengView:UpdateJLCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.jl_time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function ActXianQiJieFengView:OnJLComplete()
	self.node_list.jl_time_label.text.text = Language.XianQiJieFengAct.ActivityStr1
end

--boss来领时间倒计时
-- function ActXianQiJieFengView:BossJLTime(goal_time)
-- 	if self.boss_jianglin_time and CountDownManager.Instance:HasCountDown(self.boss_jianglin_time) then
-- 		CountDownManager.Instance:RemoveCountDown(self.boss_jianglin_time)
-- 	end
	
-- 	self.boss_jianglin_time = "boss_jianglin_time"
-- 	if goal_time > TimeWGCtrl.Instance:GetServerTime() then
-- 		self.node_list.zhuangtai_time.text.text = TimeUtil.FormatSecondDHM2(goal_time - TimeWGCtrl.Instance:GetServerTime())
-- 		CountDownManager.Instance:AddCountDown(
-- 			self.boss_jianglin_time,
-- 			BindTool.Bind1(self.UpdateBossJLDown, self), 
-- 			BindTool.Bind1(self.OnBossJLComplete, self), 
-- 			goal_time, nil, 1)
-- 	else
-- 		self.node_list.zhuangtai_time.text.text = ""
-- 	end
-- end

-- function ActXianQiJieFengView:UpdateBossJLDown(elapse_time, total_time)
-- 	local valid_time = total_time - elapse_time
-- 	if valid_time > 0 then
-- 		self.node_list.zhuangtai_time.text.text = TimeUtil.FormatSecondDHM2(valid_time)
-- 	end
-- end

-- function ActXianQiJieFengView:OnBossJLComplete()
-- 	self.node_list.zhuangtai_time.text.text = ""
-- end

-- function ActXianQiJieFengView:BossJlAlltime()
-- 	local start_time_cfg = ActXianQiJieFengWGData.Instance:GetJiangLinFlushTimeCfg()
-- 	local hold_time_cfg = ActXianQiJieFengWGData.Instance:GetJiangLinOtherCfg() -- 读配置表时间
-- 	local boos_one_starttime, boos_one_lasttime, boos_two_starttime, boos_two_lasttime ----第一次入侵開始时间，结束时间，第二次开始时间,结束时间
-- 	local zero_time = TimeWGCtrl.Instance:NowDayTimeStart(TimeWGCtrl.Instance:GetServerTime())
-- 	local clear_monster_time = hold_time_cfg[1].clear_monster -- 活动结束强制清怪时间
-- 	local time_tab = {}
-- 	if start_time_cfg ~= nil then
-- 		for k,v in pairs(start_time_cfg) do
-- 			local sub_time = {}
-- 			sub_time.hour = string.sub(v.refresh_time,1,2)
-- 			sub_time.min  = string.sub(v.refresh_time,-2)
-- 		 	table.insert(time_tab,sub_time)
-- 		end
-- 	end

-- 	boos_one_starttime = time_tab[1].hour * 3600 + time_tab[1].min * 60 + zero_time
-- 	boos_one_lasttime = boos_one_starttime + clear_monster_time
-- 	-- boos_two_starttime = time_tab[2].hour * 3600 + time_tab[2].min * 60 + zero_time
-- 	-- boos_two_lasttime = boos_two_starttime + clear_monster_time
-- 	return boos_one_starttime, boos_one_lasttime --, boos_two_starttime, boos_two_lasttime
-- end

-- function ActXianQiJieFengView:FlushBossJlState()
-- 	local time_1, time_2 = self:BossJlAlltime()  
-- 	local cur_time = TimeWGCtrl.Instance:GetServerTime()
-- 	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
-- 	local is_close = act_info and act_info.status == ACTIVITY_STATUS.CLOSE -- 打完所有怪提早结束

-- 	if cur_time < time_1 then
-- 		self.node_list.zhuangtai_desc.text.text = Language.XianQiJieFengAct.BoosJlStr1
-- 		self:BossJLTime(time_1)
-- 	elseif cur_time < time_2 and not is_close then
-- 		self.node_list.zhuangtai_desc.text.text = Language.XianQiJieFengAct.BoosJlStr2
-- 		self:BossJLTime(time_2)
-- 	-- elseif cur_time < time_3 then
-- 	-- 	self.node_list.zhuangtai_desc.text.text = Language.XianQiJieFengAct.BoosJlStr1
-- 	-- 	self:BossJLTime(time_3)
-- 	-- elseif cur_time < time_4 and not is_close then
-- 	-- 	self.node_list.zhuangtai_desc.text.text = Language.XianQiJieFengAct.BoosJlStr2
-- 	-- 	self:BossJLTime(time_4)
-- 	-- elseif cur_time >= time_4 then
-- 	-- 	self.node_list.zhuangtai_desc.text.text = Language.XianQiJieFengAct.BoosJlStr3
-- 	-- 	self:BossJLTime(time_4)
-- 	end
-- end