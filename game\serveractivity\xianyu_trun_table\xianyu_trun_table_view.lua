
XianyuTrunTableView = XianyuTrunTableView or BaseClass(SafeBaseView)
function XianyuTrunTableView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/xianyu_trun_table_prefab", "layout_xianyu_trun_table")
	self.cur_result_index = 1
end

function XianyuTrunTableView:__delete()

end

function XianyuTrunTableView:ReleaseCallBack()
	if self.show_item then
		for k,v in pairs(self.show_item) do
			v:DeleteMe()
		end
		self.show_item = nil
	end

	if self.world_record_list then
		self.world_record_list:DeleteMe()
		self.world_record_list = nil
	end

	if self.person_record_list then
		self.person_record_list:DeleteMe()
		self.person_record_list = nil
	end

	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end
	self:CancleHLQuest()

	if CountDownManager.Instance:HasCountDown("xianyu_trun_table_count_down") then
		CountDownManager.Instance:RemoveCountDown("xianyu_trun_table_count_down")
	end

	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	XianyuTrunTableWGData.Instance:SetCurIsTurning(false)
end

function XianyuTrunTableView:OpenCallBack()
	
end

function XianyuTrunTableView:LoadCallBack()
	XianyuTrunTableWGData.Instance:SetFirstLoginFlushRemind(true)
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
	end

	local open_type = CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE.CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE_INFO
	XianyuTrunTableWGCtrl.Instance:SendYunChengRollReq(open_type)

	self.show_item = {}
	for i= 1, 12 do
		-- self.show_item[i] = ItemCell.New(self.node_list["item_pos"..i])
		local group_obj = self.node_list["item_pos"..i]
	    local obj = ResMgr:Instantiate(self.node_list["item_pos_prefab"].gameObject)
	    local obj_transform = obj.transform
		obj_transform:SetParent(group_obj.transform, false)
		self.show_item[i] = XianyuTrunTableCell.New(obj)
		self.show_item[i]:SetIndex(i)
	end

	if nil == self.world_record_list then
		self.world_record_list = AsyncListView.New(XianyuTrunTableRecordCell, self.node_list.record_list1)
	end

	if nil == self.person_record_list then
		self.person_record_list = AsyncListView.New(XianyuTrunTableRecordCell, self.node_list.record_list2)
	end

	self.node_list["jump_ani"].button:AddClickListener(BindTool.Bind(self.OnClickJump, self))
	--self.node_list["change"].button:AddClickListener(BindTool.Bind(self.OnClickChangeRecord, self))   --更换道具
	self.node_list["roll_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRoll, self,XianyuTrunTableWGData.DrawMode.One))
	self.node_list["close_btn"].button:AddClickListener(BindTool.Bind(self.OnClickClose, self))
	self.node_list["tips_btn"].button:AddClickListener(BindTool.Bind(self.OnClickTipsBtn, self))
	self.node_list["ten_btn"].button:AddClickListener(BindTool.Bind(self.OnClickRoll, self,XianyuTrunTableWGData.DrawMode.Ten))

	for i = 1 ,2 do
		self.node_list["toggle_btn"..i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickRecordChange,self,i))
	end
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_GOLD_ZHUANPAN)
	if not IsEmptyTable(act_info) then
		local end_time = act_info.end_time
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if end_time > server_time then
			self:ActUpdeteTimer(server_time, end_time)
			if CountDownManager.Instance:HasCountDown("xianyu_trun_table_count_down") then
				CountDownManager.Instance:RemoveCountDown("xianyu_trun_table_count_down")
			end
			CountDownManager.Instance:AddCountDown("xianyu_trun_table_count_down", BindTool.Bind(self.ActUpdeteTimer,self), BindTool.Bind(self.ActCompleteTimer,self),end_time,nil,1)
		else
			self.node_list["act_time_text"].text.text = Language.XianyuTrunTable.ActivityEnd
		end
	end
	local other_cfg = XianyuTrunTableWGData.Instance:GetOtherCfg()
	self.node_list["add_reward_text"].text.text = other_cfg and other_cfg.add_glod_desc or ""
	local draw_cfg = XianyuTrunTableWGData.Instance:GetDrawModeCfg(XianyuTrunTableWGData.DrawMode.One)
	self.node_list["one_gold"].text.text = draw_cfg and draw_cfg.cost_gold or 50
end

function XianyuTrunTableView:ActUpdeteTimer(now_time, elapse_time)
	local time_str = TimeUtil.FormatSecondDHM(elapse_time - now_time)
	self.node_list["act_time_text"].text.text = string.format(Language.XianyuTrunTable.ActivityTime,time_str)
end

function XianyuTrunTableView:ActCompleteTimer()
	self.node_list["act_time_text"].text.text = Language.XianyuTrunTable.ActivityEnd
end

function XianyuTrunTableView:ShowIndexCallBack()
	self:ViewAnimation()
	self.node_list["toggle_btn1"].toggle.isOn = true
end

function XianyuTrunTableView:ViewAnimation()
	-- local tween_info = UITween_CONSTS.XianyuTrunTableCarnival
	-- local tween_root_1 = self.node_list.content
	-- UITween.CleanAllTween(GuideModuleName.XianyuTrunTableView)
	-- UITween.FakeHideShow(tween_root_1)
	-- RectTransform.SetAnchoredPositionXY(self.node_list.muban_l.rect, -900, -40)
	-- RectTransform.SetAnchoredPositionXY(self.node_list.muban_r.rect, 1000, -40)

	-- self.node_list.muban_l.rect:DOAnchorPos(Vector2(-406, -40), tween_info.MoveTime)
	-- self.node_list.muban_r.rect:DOAnchorPos(Vector2(473, -40), tween_info.MoveTime)

	-- ReDelayCall(self, function()
	-- 	UITween.AlphaShow(GuideModuleName.XianyuTrunTableView,tween_root_1, 0, 1, tween_info.AlphaTime)
	-- end, tween_info.MoveTime, "xianyu_trun_table_carnival_1")
end


function XianyuTrunTableView:FlushAniStatus()
	self.is_jump_ani = XianyuTrunTableWGData.Instance:GetJumpAni()
	self.node_list.jump_toggle:SetActive(self.is_jump_ani)
end


function XianyuTrunTableView:OnClickJump()
	XianyuTrunTableWGData.Instance:SetJumpAni()
	self:FlushAniStatus()
end

-- function XianyuTrunTableView:OnClickChangeRecord()
-- 	local is_turning = XianyuTrunTableWGData.Instance:GetCurIsTurning()
-- 	if is_turning then
-- 		TipWGCtrl.Instance:ShowSystemMsg(Language.XianyuTrunTable.IsWorkIng)
-- 		return
-- 	end
-- 	XianyuTrunTableWGCtrl.Instance:OpenRewardSelectView()
-- end

function XianyuTrunTableView:OnClickRoll(draw_type)
	local is_turning = XianyuTrunTableWGData.Instance:GetCurIsTurning()
	if is_turning then
		TipWGCtrl.Instance:ShowSystemMsg(Language.XianyuTrunTable.IsWorkIng)
		return
	end
	XianyuTrunTableWGData.Instance:SetCurRollDrawType(draw_type)
	XianyuTrunTableWGCtrl.Instance:OnSendRoll(draw_type)

	-- local other_cfg = XianyuTrunTableWGData.Instance:GetOtherCfg()
	-- local mode_cfg = XianyuTrunTableWGData.Instance:GetDrawModeCfg(draw_type)
	-- if IsEmptyTable(mode_cfg) then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind("检查抽奖配置:",draw_type)
	-- 	return
	-- end
	-- local cost_gold = mode_cfg and mode_cfg.cost_gold or 0

	-- local func = function ()
	-- 	if self.is_turning then return end
	-- 	self.is_turning = true
	-- 	local is_enough = RoleWGData.Instance:GetIsEnoughUseGold(cost_gold)
	-- 	if is_enough then
	-- 		local op_type = CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE.CS_CROSS_CHANNEL_ACTIVITY_GOLD_ZHUANPAN_OP_TYPE_BUY
	-- 		local param_1 = mode_cfg.draw_type
	-- 		XianyuTrunTableWGCtrl.Instance:SendYunChengRollReq(op_type,param_1)
	-- 	else
	-- 		UiInstanceMgr.Instance:ShowChongZhiView()
	-- 	end
	-- end

	-- if not self.alert then
 --        self.alert = Alert.New()
 --    end
 --    self.alert:ClearCheckHook()
 --    self.alert:SetShowCheckBox(true, "xianyu_trun_table")
 --    self.alert:SetCheckBoxDefaultSelect(false)
 --    local str = string.format(Language.XianyuTrunTable.ChouJiangStr, cost_gold, mode_cfg.count)

 --    self.alert:SetLableString(str)
 --    self.alert:SetOkFunc(func)
 --    self.alert:Open()

	
end

function XianyuTrunTableView:OnClickClose()
	self:Close()
end

function XianyuTrunTableView:PlayRollAnimal()
	local item_cfg,index = XianyuTrunTableWGData.Instance:GetRollResult()
	local is_turning = XianyuTrunTableWGData.Instance:GetCurIsTurning()
	if index <= 0 then
		XianyuTrunTableWGData.Instance:SetCurIsTurning(false)
		return
	end
	local time = 2

	if self.is_jump_ani then
		time = 0
	end

	self.tween = self.node_list.roll_father.transform:DORotate(Vector3(0, 0, - 30 * (index - 1)),time,
		DG.Tweening.RotateMode.FastBeyond360)
	-- self.tween = self.node_list.roll_father.transform:DORotate(Vector3(0, 0, -360 * time - 30 * (index - 1)),time,
	-- 	DG.Tweening.RotateMode.FastBeyond360)

	self.tween:SetEase(DG.Tweening.Ease.OutQuart)
	self.tween:OnComplete(function()
		XianyuTrunTableWGData.Instance:SetCurIsTurning(false)
		self:CancleHLQuest()
		self:ShowRollHightLight()
	end)

	self:CancleHLQuest()

	if self.is_jump_ani then
		return
	end

	self.show_hl_quest = GlobalTimerQuest:AddRunQuest(function ()
		self:ShowRollHightLight()
	end,0.02)
	self:ShowRollHightLight()
end

function XianyuTrunTableView:CancleHLQuest()
	if self.show_hl_quest then
		GlobalTimerQuest:CancelQuest(self.show_hl_quest)
		self.show_hl_quest = nil
	end
end

function XianyuTrunTableView:ShowRollHightLight()
	local father_rotation = self.node_list.roll_father.transform.localEulerAngles.z
	local index = (12 - math.floor((father_rotation + 15) / 30)) % 12 + 1
	for i =1,12 do
		self.node_list["highlight"..i]:SetActive(i == index)
	end
end

function XianyuTrunTableView:OnFlush()
	local main_role_vo = RoleWGData.Instance:GetRoleInfo()
	self.node_list["times"].text.text = CommonDataManager.ConverNumValue(main_role_vo["gold"])
	-- local jiangchi_gold = XianyuTrunTableWGData.Instance:GetJingchiGlod()
	-- self.node_list["desc"].text.text = jiangchi_gold
	self:FlushJingChiGlod()
	self:FlushAniStatus()

	local all_draw_cfg = XianyuTrunTableWGData.Instance:GetAllDrawCfg()
	if not IsEmptyTable(all_draw_cfg) then
		for i = 1, 12 do
			if all_draw_cfg[i] then
				local exchange_data = {}
				local reward_cfg = XianyuTrunTableWGData.Instance:GetRewardCfgById(all_draw_cfg[i].reward_id)
				self.show_item[i]:SetData(reward_cfg)
			end
		end
	end
	self:FlushWorld()
	self:FlushPersonal()
	-- if XianyuTrunTableWGData.Instance:IsAvalibaleTime() then
	-- 	self.node_list["effectpos"]:SetActive(true)
	-- else
	-- 	self.node_list["effectpos"]:SetActive(false)
	-- end
end

function XianyuTrunTableView:FlushJingChiGlod()
	local get_gold = XianyuTrunTableWGData.Instance:GetJingchiGlod()
	local old_value = self.node_list["desc"].text.text
    local text_obj = self.node_list["desc"].text
    local complete_fun = function()
        if text_obj then
            text_obj.text = get_gold
        end
    end
    local update_fun = function(num)
        if text_obj then
            text_obj.text = math.ceil(num)
        end
    end
    UITween.DONumberTo(text_obj, old_value, get_gold, 1, update_fun, complete_fun)
end

function XianyuTrunTableView:OnClickRecordChange(index,is_on)
	if is_on then
		if index == 1 and self.cur_result_index ~= index then
			self.cur_result_index = index
			self:FlushWorld()
		elseif index == 2 and self.cur_result_index ~= index then
			self.cur_result_index = index
			self:FlushPersonal()
		end
	end
end

function XianyuTrunTableView:FlushWorld()
	if self.cur_result_index ~= 1 then return end
	local world_data = XianyuTrunTableWGData.Instance:GetWorldRecord()
	self:SetInfoBg(world_data)
	if nil == self.world_record_list then return end
	self.world_record_list:SetDataList(world_data)
end

function XianyuTrunTableView:FlushPersonal()
	if self.cur_result_index ~= 2 then return end
	local personal_data = XianyuTrunTableWGData.Instance:GetPersonalRecord()
	self:SetInfoBg(personal_data)
	if nil == self.person_record_list then return end
	self.person_record_list:SetDataList(personal_data)
end

function XianyuTrunTableView:SetInfoBg(data)
	local is_have_info = not IsEmptyTable(data)
	self.node_list.not_info:SetActive(not is_have_info)
end

function XianyuTrunTableView:OnClickTipsBtn()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.XianyuTrunTable.RuleTitle)
	role_tip:SetContent(Language.XianyuTrunTable.RuleDesc)
end


--------------------------------------------------------------------


XianyuTrunTableRecordCell = XianyuTrunTableRecordCell or BaseClass(BaseRender)

function XianyuTrunTableRecordCell:__init()
end

function XianyuTrunTableRecordCell:__delete()
end

function XianyuTrunTableRecordCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local reward_id = self.data.reward_id
		local reward_cfg = XianyuTrunTableWGData.Instance:GetRewardCfgById(reward_id)
		if self.node_list["show_bg"] then
			self.node_list["show_bg"]:SetActive(reward_cfg.show == 1)
		end
		local item_cfg = ItemWGData.Instance:GetItemConfig(reward_cfg.item_id)
		local rewared_name = reward_cfg.show_name
		if self.data.rolename then
			local rolename = string.format(Language.XianyuTrunTable.RoleNameStr,self.data.rolename)
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			if self.data.get_gold > 0 then
				item_name = string.format(Language.XianyuTrunTable.GetGoldStr,self.data.get_gold)
			end
			self.node_list["contant"].text.text = rolename..Language.XianyuTrunTable.Congratulations1..rewared_name..item_name
		else
			local time_tab = os.date("*t", self.data.timestamp)
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			if self.data.get_gold > 0 then
				item_name = string.format(Language.XianyuTrunTable.GetGoldStr,self.data.get_gold)
			end
			self.node_list["contant"].text.text = Language.XianyuTrunTable.Congratulations2.. "：" .. item_name
		end
	else
		self.node_list["contant"].text.text = ""
	end
end

------------------------------------------------
XianyuTrunTableCell = XianyuTrunTableCell or BaseClass(BaseRender)

function XianyuTrunTableCell:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function XianyuTrunTableCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function XianyuTrunTableCell:OnFlush()
	if IsEmptyTable(self.data) then return end
	local item_data = {}
	item_data.item_id = tonumber(self.data.item_id)
	item_data.num = 1
	--策划让特殊处理  因为绑定标识挡住策划的图标文字了，策划让丢掉绑定标
	item_data.is_bind = self.data.show > 0 and 0 or 1
	self.item_cell:SetData(item_data)
	self.node_list["show_kuang"]:SetActive(self.data.show > 0)
	self.node_list["show_reward"]:SetActive(self.data.show > 0)
	if self.data.show > 0 then
		local bunlle,asset = ResPath.GetXianyuTrunTableImg("a1_xyly_jldj"..self.data.show)
		self.node_list["show_reward"].image:LoadSprite(bunlle,asset,function ()
			self.node_list["show_reward"].image:SetNativeSize()
		end)
	end
end
