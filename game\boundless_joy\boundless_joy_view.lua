BoundlessJoyView = BoundlessJoyView or BaseClass(SafeBaseView)

function BoundlessJoyView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Half
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/boundless_joy_ui_prefab", "layout_boundless_joy")
end

function BoundlessJoyView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["free_draw_btn"], BindTool.Bind1(self.OnClickGetFreeReward, self))
    XUI.AddClickEventListener(self.node_list["all_cost_btn"], BindTool.Bind1(self.OnClickGetAllReward, self))

    self.boundless_reward_list = {}
    for i = 0, 2 do
        self.boundless_reward_list[i] = BoundlessJoyRewardRender.New(self.node_list["boundlessjoy_reward_show_list"]:FindObj("boundless_joy_item" .. i))
    end
end

function BoundlessJoyView:ReleaseCallBack()
    if self.boundless_reward_list then
        for k,v in pairs(self.boundless_reward_list) do
			v:DeleteMe()
		end
	    self.boundless_reward_list = nil
	end
end

function BoundlessJoyView:OnFlush()
    local is_buy_free = BoundlessJoyWGData.Instance:GetShopIsBuyFlag()
    local other_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
    local str = string.format(Language.BoundlessJoy.buy_btn_text, other_cfg.all_buy_price)
    self.node_list.all_cost_btn_text.text.text = str
    XUI.SetGraphicGrey(self.node_list.free_draw_btn, not is_buy_free)
    self.node_list.free_get_reward_remind:SetActive(is_buy_free)
    local all_cost_btn_flag = BoundlessJoyWGData.Instance:GetAllCostBtnIsBuyFlag()
    local reward_list_cfg = BoundlessJoyWGData.Instance:GetALLRewardPoolCfg()
    self.node_list.all_cost_btn:SetActive(all_cost_btn_flag)
    self.node_list.tips_text.text.text = Language.BoundlessJoy.tips_text
    self.node_list.tips_text:SetActive(all_cost_btn_flag)

    for i = 0, #reward_list_cfg do
        self.boundless_reward_list[i]:SetData(reward_list_cfg[i])
    end
end

--领取免费奖励
function BoundlessJoyView:OnClickGetFreeReward() 
    local is_buy_free = BoundlessJoyWGData.Instance:GetShopIsBuyFlag()
    if is_buy_free then
        BoundlessJoyWGCtrl.Instance:SendHappyForeverReq(HAPPY_FOREVER_TYPE.HAPPY_FOREVER_OPERATE_TYPE_GET_DAILY_FREE_REWARDS)
    end
end

--一键购买
function BoundlessJoyView:OnClickGetAllReward()
    for i = 0, 2 do
        local choose_reward_list = BoundlessJoyWGData.Instance:GetChooseRewardPool(i)
        local other_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
        if #choose_reward_list < other_cfg.need_select_count then
            local ok_func = function()
                BoundlessJoyWGCtrl.Instance:OpenSelectRewardView()
            end

            BoundlessJoyWGCtrl.Instance:OpenChooseRewardAlertView(ok_func)
            return
        end
    end

    local free_shop_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
    RechargeWGCtrl.Instance:Recharge(free_shop_cfg.all_buy_price, free_shop_cfg.all_rmb_type)
end







BoundlessJoyRewardRender = BoundlessJoyRewardRender or BaseClass(BaseRender)
function BoundlessJoyRewardRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind1(self.OnClickBuyGiftBtn, self))
    XUI.AddClickEventListener(self.node_list["get_free_reward_btn"], BindTool.Bind1(self.OnClickGetFreeGiftBtn, self))   --领取免费礼包
    XUI.AddClickEventListener(self.node_list["go_choose_reward_btn"], BindTool.Bind1(self.OnClickGoRewardChooseBtn, self))   --打开奖励选择界面

    self.free_show_item = ItemCell.New(self.node_list["specil_reward_show_item"])
    self.free_show_item:SetCellBgEnabled(false)

    if self.reward_list == nil then
        self.reward_list = {}
        for i = 1, 6 do
            self.reward_list[i] = BoundlessJoyRewardCellRender.New(self.node_list["reward_pos_" .. i])
        end
    end
end

function BoundlessJoyRewardRender:__delete()
    if self.free_show_item then
		self.free_show_item:DeleteMe()
		self.free_show_item = nil
	end

    if self.reward_list then
        for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
	    self.reward_list = nil
	end
end

function BoundlessJoyRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local data = BoundlessJoyWGData.Instance:GetChooseRewardPool(self.data.seq, true)
    for i = 1, 6 do
        self.reward_list[i]:SetData(data[i])
    end

    self.free_show_item:SetData(self.data.reward_item[0])
    self.node_list.specil_reward_show_item_name.text.text = self.data.name
    self.node_list.need_cost_number.text.text = string.format(Language.BoundlessJoy.yuan, self.data.rmb_price)
    local buytimes = BoundlessJoyWGData.Instance:GetGiftBuyTimes(self.data.seq)
    local free_shop_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
    local free_acquire_begin_times = free_shop_cfg.free_acquire_begin_times
    local color =  buytimes >= free_acquire_begin_times and COLOR3B.L_GREEN or COLOR3B.RED
    local value_str = string.format(Language.BoundlessJoy.cost_num, ToColorStr(buytimes, color), free_acquire_begin_times)
    local free_shop_flag = BoundlessJoyWGData.Instance:GetFreeShopFlag(self.data.seq)
    self.node_list.cost_number.text.text = value_str
    self.node_list.buy_btn:SetActive(buytimes < free_acquire_begin_times)
    self.node_list.get_free_reward_btn:SetActive(buytimes == free_acquire_begin_times and free_shop_flag == 0)
    self.node_list.get_reward_text:SetActive(buytimes >= free_acquire_begin_times)
end

--购买礼包
function BoundlessJoyRewardRender:OnClickBuyGiftBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local data = BoundlessJoyWGData.Instance:GetChooseRewardPool(self.data.seq)
    local other_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
    local reward_num = #data
    if reward_num < other_cfg.need_select_count then
        local ok_func = function()
            BoundlessJoyWGCtrl.Instance:OpenSelectRewardView()
        end
        BoundlessJoyWGCtrl.Instance:OpenChooseRewardAlertView(ok_func)
    else
        RechargeWGCtrl.Instance:Recharge(self.data.rmb_price, self.data.rmb_type, self.data.rmb_seq)
    end
end

--领取免费礼包
function BoundlessJoyRewardRender:OnClickGetFreeGiftBtn()
    if IsEmptyTable(self.data) then
        return
    end

    local data = BoundlessJoyWGData.Instance:GetChooseRewardPool(self.data.seq)
    local other_cfg = BoundlessJoyWGData.Instance:GetCurFreeShopCfg()
    local reward_num = #data
    if reward_num < other_cfg.need_select_count then
        local ok_func = function()
            BoundlessJoyWGCtrl.Instance:OpenSelectRewardView()
        end
        BoundlessJoyWGCtrl.Instance:OpenChooseRewardAlertView(ok_func)
    else
        BoundlessJoyWGCtrl.Instance:SendHappyForeverReq(HAPPY_FOREVER_TYPE.HAPPY_FOREVER_OPERATE_TYPE_GET_DAILY_REWARDS, self.data.seq)
    end
end

--打开奖励选择界面
function BoundlessJoyRewardRender:OnClickGoRewardChooseBtn()
    BoundlessJoyWGCtrl.Instance:OpenSelectRewardView()
end







BoundlessJoyRewardCellRender = BoundlessJoyRewardCellRender or BaseClass(BaseRender)

function BoundlessJoyRewardCellRender:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list["item"])
    self.item_cell:SetCellBgEnabled(false)

    self.node_list["go_custom_btn"].button:AddClickListener(BindTool.Bind(self.OpenSelestRewardViewBtn, self))
end

function BoundlessJoyRewardCellRender:__delete()
    if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function BoundlessJoyRewardCellRender:OnFlush()
    if not self.data then
        self.node_list["go_custom_btn"]:SetActive(true)
		return
	end

    self.node_list["go_custom_btn"]:SetActive(false)
	self.item_cell:SetData(self.data)
end

function BoundlessJoyRewardCellRender:OpenSelestRewardViewBtn()
    BoundlessJoyWGCtrl.Instance:OpenSelectRewardView()
end