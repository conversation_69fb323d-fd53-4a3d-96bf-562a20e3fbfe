SpecialPersonalBossLogic = SpecialPersonalBossLogic or BaseClass(CommonFbLogic)

function SpecialPersonalBossLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function SpecialPersonalBossLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function SpecialPersonalBossLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.Boss.SpecialPersonalBossTitle)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		BossWGCtrl.Instance:OpenPersonSpecialBossSceneView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)
end

function SpecialPersonalBossLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

	BossWGCtrl.Instance:ClosePersonSpecialBossSceneView()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)

	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
end