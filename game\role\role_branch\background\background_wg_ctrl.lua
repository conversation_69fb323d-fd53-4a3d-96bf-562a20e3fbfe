require("game/role/role_branch/background/background_wg_data")          --数据层
----------------------
require("game/role/role_branch/background/background_sky_curtain_view") ---模块界面
require("game/role/role_branch/background/background_tips_view")        ---属性提示
--------------------------------------------------------------
--角色背景（奇境）
--------------------------------------------------------------
BackgroundWGCtrl = BackgroundWGCtrl or BaseClass(BaseWGCtrl)
function BackgroundWGCtrl:__init()
	if BackgroundWGCtrl.Instance then
		ErrorLog("[BackgroundWGCtrl] Attemp to create a singleton twice !")
	end
	self.data = BackgroundWGData.New() ---数据层实例
	self.back_tips_view = BackgroundTipsView.New()
	---注册协议
	self:RegisterAllProtocols()
	self.item_change_call_back = BindTool.Bind(self.OnItemChangeCallBack, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_call_back)

	BackgroundWGCtrl.Instance = self
end

function BackgroundWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.back_tips_view:DeleteMe()
	self.back_tips_view = nil

	-- 注销功能引导
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_call_back)
	BackgroundWGCtrl.Instance = nil
end

---物品发生改变
function BackgroundWGCtrl:OnItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.data:CheckIsBackgroundItemId(change_item_id) then
		BackgroundWGData.Instance:RefreshSubRemind()
		self:FlushView()
	end
end

-- 注册协议
function BackgroundWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSBackOperate)
	self:RegisterProtocol(SCBackInfo, "SCBackInfo")
	self:RegisterProtocol(SCBackUse, "SCBackUse")
	self:RegisterProtocol(SCActiveNewBackInfo, "SCActiveNewBackInfo")
end

---操作背景
function BackgroundWGCtrl:SendBackgroundOperate(oper_type, back_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBackOperate)
	protocol.oper_type = oper_type
	protocol.back_id = back_id
	protocol:EncodeAndSend()
end

---请求背景信息
function BackgroundWGCtrl:BackInfoReq(back_id)
	self:SendBackgroundOperate(BACK_OPERTYPE.BACK_REQ_INFO, back_id)
end

---请求使用背景
function BackgroundWGCtrl:BackUseReq(back_id)
	self:SendBackgroundOperate(BACK_OPERTYPE.BACK_REQ_USE, back_id)
end

---请求背景信息
function BackgroundWGCtrl:BackUnuseReq(back_id)
	self:SendBackgroundOperate(BACK_OPERTYPE.BACK_REQ_UNUSE, back_id)
end

---请求背景信息
function BackgroundWGCtrl:BackUpLevelReq(back_id)
	self:SendBackgroundOperate(BACK_OPERTYPE.BACK_REQ_UP_LEVEL, back_id)
end

-- 背景信息
function BackgroundWGCtrl:SCBackInfo(protocol)
	self.data:InitServerDta(protocol)
	self:FlushView()
	RemindManager.Instance:Fire(RemindName.Role_Background)
end

-- 同步背景的当前使用id
function BackgroundWGCtrl:SCBackUse(protocol)
	self.data:RefreshUseBack(protocol)
	self:FlushView()
	RemindManager.Instance:Fire(RemindName.Role_Background)
end

-- 激活新背景
function BackgroundWGCtrl:SCActiveNewBackInfo(protocol)
	self.data:ActiveNewBackInfo(protocol)
	self:FlushView()
	RemindManager.Instance:Fire(RemindName.Role_Background)
end

---升星结果
function BackgroundWGCtrl:OnUpLevelResult(result, param1, param2)
	if result == 1 then
		self.data:RefreshUpLevel(param1, param2)
		self:FlushView()
		RemindManager.Instance:Fire(RemindName.Role_Background)
	end
end

-- 刷新界面
function BackgroundWGCtrl:FlushView(index, key, param_t)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.RoleBranchView, TabIndex.sky_curtain) then
		ViewManager.Instance:FlushView(GuideModuleName.RoleBranchView, TabIndex.sky_curtain)
	end
end

---打开总属性
function BackgroundWGCtrl:OnOpenTitleTips()
	if self.back_tips_view then
		self.back_tips_view:Open()
	end
end
