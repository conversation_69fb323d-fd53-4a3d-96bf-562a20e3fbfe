------------------------战力飙升礼包start------------------------
SCCapabilityUpGiftInfo = SCCapabilityUpGiftInfo or BaseClass(BaseProtocolStruct)
function SCCapabilityUpGiftInfo:__init()
    self.msg_type = 16301
end

function SCCapabilityUpGiftInfo:Decode()
	self.seq = MsgAdapter.ReadInt()
	self.end_second = MsgAdapter.ReadUInt()					--结束时间
	self.gift_buy_flag = {}									--购买标记
	for i = 0, 15 do
		self.gift_buy_flag[i] = MsgAdapter.ReadUChar()
	end
end
------------------------战力飙升礼包end------------------------

------------------------------------------限时直购礼包Start---------------------------
-- 操作请求
CSPopupGiftClientReq =  CSPopupGiftClientReq or BaseClass(BaseProtocolStruct)
function CSPopupGiftClientReq:__init()
	self.msg_type = 16302
end

function CSPopupGiftClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

--限时直购礼包信息
SPopupGiftAllInfo =  SPopupGiftAllInfo or BaseClass(BaseProtocolStruct)
function  SPopupGiftAllInfo:__init()
	self.msg_type = 16303
end

function SPopupGiftAllInfo:Decode()
	self.popupgift_list = {}
	self.open_day_grade_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)
	for i = 1, 30 do
		self.popupgift_list[i] = ProtocolStruct.ReadPopGift()
	end
end
------------------------------------------限时直购礼包end---------------------------

-----------------进阶属性丹扩展start-----------------
CSAttributeStoneOperate = CSAttributeStoneOperate or BaseClass(BaseProtocolStruct)

function CSAttributeStoneOperate:__init()
	self.msg_type = 16304
end

function CSAttributeStoneOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCAttributeStoneInfo = SCAttributeStoneInfo or BaseClass(BaseProtocolStruct)

function SCAttributeStoneInfo:__init()
    self.msg_type = 16305
end

function SCAttributeStoneInfo:Decode()
	local upgrade_stone_list = {}
	for i = 0, 47 do
		upgrade_stone_list[i] = MsgAdapter.ReadInt()
	end
	self.upgrade_stone_list = upgrade_stone_list

	local pet_stone_list = {}
	for i = 0, 11 do
		pet_stone_list[i] = MsgAdapter.ReadInt()
	end
	self.pet_stone_list = pet_stone_list

	local mount_stone_list = {}
	for i = 0, 11 do
		mount_stone_list[i] = MsgAdapter.ReadInt()
	end
	self.mount_stone_list = mount_stone_list
end
-----------------进阶属性丹扩展end-----------------

-----------------本服战力榜Start----------------
local function GetCapabilityRankInfoItem()
	local data = {}
	data.uid = MsgAdapter.ReadInt()
	data.name = MsgAdapter.ReadName()
	data.level = MsgAdapter.ReadInt()
	data.vip_level = MsgAdapter.ReadShort()
	data.sex = MsgAdapter.ReadChar()
	data.is_hide_vip = MsgAdapter.ReadChar()
	data.rank_value = MsgAdapter.ReadLL()
	data.rank = MsgAdapter.ReadInt()
	return data
end

SCCapabilityRankInfo = SCCapabilityRankInfo or BaseClass(BaseProtocolStruct)

function SCCapabilityRankInfo:__init()
	self.msg_type = 16306
end

function SCCapabilityRankInfo:Decode()
	self.self_rank_value = MsgAdapter.ReadLL()
	self.total_capability = MsgAdapter.ReadLL()
	self.count = MsgAdapter.ReadInt()

	local rank_list = {}
	for i = 1, self.count do
		local data = GetCapabilityRankInfoItem()
		rank_list[data.rank] = data
	end

	self.rank_item_list = rank_list
end

CSCapabilityRank = CSCapabilityRank or BaseClass(BaseProtocolStruct)

function CSCapabilityRank:__init()
	self.msg_type = 16307
end

function CSCapabilityRank:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.opera_type)
end
-----------------本服战力榜End---------------------

---------------------------天仙宝阁Start---------------------------
CSTianXianBaoGeClientReq = CSTianXianBaoGeClientReq or BaseClass(BaseProtocolStruct)
function CSTianXianBaoGeClientReq:__init()
	self.msg_type = 16309
end

function CSTianXianBaoGeClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

--天仙宝阁所有信息
SCTianXianBaoGeAllInfo = SCTianXianBaoGeAllInfo or BaseClass(BaseProtocolStruct)
function SCTianXianBaoGeAllInfo:__init()
	self.msg_type = 16310
end

function SCTianXianBaoGeAllInfo:Decode()
	self.round = MsgAdapter.ReadInt()
	self.end_time = MsgAdapter.ReadInt()

	self.convert = {}
	for i = 0, 31 do
		self.convert[i] = MsgAdapter.ReadShort()
	end
end

---------------------------天仙宝阁End---------------------------

-----------------跨服属性宝石排行榜Start----------------
CSCrossAttributeStoneRankClient = CSCrossAttributeStoneRankClient or BaseClass(BaseProtocolStruct)
function CSCrossAttributeStoneRankClient:__init()
	self.msg_type = 16308
end

function CSCrossAttributeStoneRankClient:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCCrossAttributeStoneRankInfo = SCCrossAttributeStoneRankInfo or BaseClass(BaseProtocolStruct)
local function GetAttributeStoneInfoItem()
	local data = {}
	data.uuid = MsgAdapter.ReadLL()
	data.usid = MsgAdapter.ReadLL()
	data.name = MsgAdapter.ReadName()
	data.level = MsgAdapter.ReadInt()
	data.vip_level = MsgAdapter.ReadShort()
	data.sex = MsgAdapter.ReadChar()
	data.is_hide_vip = MsgAdapter.ReadChar()
	data.rank_value = MsgAdapter.ReadLL()
	data.rank = MsgAdapter.ReadInt()
	return data
end

function SCCrossAttributeStoneRankInfo:__init()
	self.msg_type = 16311
end

function SCCrossAttributeStoneRankInfo:Decode()
	self.self_score = MsgAdapter.ReadLL()
	self.self_rank = MsgAdapter.ReadLL()
	self.count = MsgAdapter.ReadInt()
	local rank_list = {}
	for i = 1, self.count do
		local data = GetAttributeStoneInfoItem()
		rank_list[data.rank] = data
	end

	self.rank_item_list = rank_list
end

SCCrossAttributeStoneRankPersonScoreReward = SCCrossAttributeStoneRankPersonScoreReward or BaseClass(BaseProtocolStruct)

function SCCrossAttributeStoneRankPersonScoreReward:__init()
	self.msg_type = 16312
end

function SCCrossAttributeStoneRankPersonScoreReward:Decode()
	self.person_score_reward_flag = MsgAdapter.ReadInt()
	self.self_score = MsgAdapter.ReadLL()
end

-----------------跨服属性宝石排行榜End----------------

--------------------------- 自定义动作 Start---------------------------
local function MsgRoleActionObj()
	local data = {}
	data.level = MsgAdapter.ReadInt()
	return data
end

-- 更新单个动作信息
SCRoleActionInfo = SCRoleActionInfo or BaseClass(BaseProtocolStruct)
function SCRoleActionInfo:__init()
	self.msg_type = 16313
end

function SCRoleActionInfo:Decode()
	self.action_id = MsgAdapter.ReadInt()
	self.action_data = MsgRoleActionObj()
end

-- 全部动作信息
SCRoleActionAllInfo = SCRoleActionAllInfo or BaseClass(BaseProtocolStruct)
function SCRoleActionAllInfo:__init()
	self.msg_type = 16314
end

function SCRoleActionAllInfo:Decode()
	self.action_list = {}
	for action_id = 0, 40 - 1 do
		self.action_list[action_id] = MsgRoleActionObj()
	end
end

-- 动作范围广播
SCRoleActionNotify = SCRoleActionNotify or BaseClass(BaseProtocolStruct)
function SCRoleActionNotify:__init()
	self.msg_type = 16315
end

function SCRoleActionNotify:Decode()
	self.obj_id = MsgAdapter.ReadObjId()
	MsgAdapter.ReadUShort()
	self.action_id = MsgAdapter.ReadInt()
end

-- 操作请求
CSRoleActionReq = CSRoleActionReq or BaseClass(BaseProtocolStruct)
function CSRoleActionReq:__init()
	self.msg_type = 16316
end

function CSRoleActionReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
end

--------------------------- 自定义动作 end ---------------------------

--------------------------- Boss死亡广播 Start---------------------------
SCBOSSDeadNotice = SCBOSSDeadNotice or BaseClass(BaseProtocolStruct)
function SCBOSSDeadNotice:__init()
	self.msg_type = 16317
end

function SCBOSSDeadNotice:Decode()
	self.boss_type = MsgAdapter.ReadChar()			--boss类型.
	self.monster_type = MsgAdapter.ReadChar()		--怪物类型.
	self.boss_id = MsgAdapter.ReadUShort()			--怪物ID.
end

--------------------------- Boss死亡广播 end ---------------------------

-----------------一折弹窗礼包Start---------------------------
-- 操作请求
CSPopupGift2ClientReq =  CSPopupGift2ClientReq or BaseClass(BaseProtocolStruct)
function CSPopupGift2ClientReq:__init()
	self.msg_type = 16318
end

function CSPopupGift2ClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

--一折弹窗礼包信息
SPopupGift2AllInfo =  SPopupGift2AllInfo or BaseClass(BaseProtocolStruct)
function  SPopupGift2AllInfo:__init()
	self.msg_type = 16319
end

function SPopupGift2AllInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.end_time = MsgAdapter.ReadInt()

	self.gift = {}
	for i = 1, 4 do
		self.gift[i] = MsgAdapter.ReadChar()
	end
end
-----------------一折弹窗礼包End----------------

---------------------------镜花水月Start---------------------------
CSJingHuaShuiYueOperate = CSJingHuaShuiYueOperate or BaseClass(BaseProtocolStruct)
function CSJingHuaShuiYueOperate:__init()
	self.msg_type = 16320
end

function CSJingHuaShuiYueOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

--全部信息
SCJingHuaShuiYueInfo = SCJingHuaShuiYueInfo or BaseClass(BaseProtocolStruct)
function SCJingHuaShuiYueInfo:__init()
	self.msg_type = 16321
end

function SCJingHuaShuiYueInfo:Decode()
	local count = MsgAdapter.ReadInt()
	local suit_list = {}
	for i = 0, count - 1 do
		local temp = {}
		temp.pos_count = MsgAdapter.ReadShort()					--激活进度
		temp.star_level = MsgAdapter.ReadShort()				--星级
		temp.pos_info = {}
		for index = 0, 8 do
			temp.pos_info[index] = MsgAdapter.ReadChar()		--套装内部位单独的信息 -1未激活 0-10为星级
		end

		local idle = MsgAdapter.ReadChar()
		local idle2 = MsgAdapter.ReadShort()

		suit_list[i] = temp
	end

	self.suit_list = suit_list
end

--激活信息
SCJingHuaShuiYueActiveInfo = SCJingHuaShuiYueActiveInfo or BaseClass(BaseProtocolStruct)
function SCJingHuaShuiYueActiveInfo:__init()
	self.msg_type = 16322
end

function SCJingHuaShuiYueActiveInfo:Decode()
	self.suit_id = MsgAdapter.ReadShort()
	self.pos_count = MsgAdapter.ReadShort()
end

--升星信息
SCJingHuaShuiYueUpgradeInfo = SCJingHuaShuiYueUpgradeInfo or BaseClass(BaseProtocolStruct)
function SCJingHuaShuiYueUpgradeInfo:__init()
	self.msg_type = 16323
end

function SCJingHuaShuiYueUpgradeInfo:Decode()
	self.suit_id = MsgAdapter.ReadShort()
	self.star_level = MsgAdapter.ReadShort()
end

--部位信息
SCJingHuaShuiYuePosInfo = SCJingHuaShuiYuePosInfo or BaseClass(BaseProtocolStruct)
function SCJingHuaShuiYuePosInfo:__init()
	self.msg_type = 16324
end

function SCJingHuaShuiYuePosInfo:Decode()
	local count = MsgAdapter.ReadInt()
	local data_list = {}
	for i = 1, count do
		data_list[i] = {}
		data_list[i].suit_id = MsgAdapter.ReadShort()
		data_list[i].pos = MsgAdapter.ReadChar()
		data_list[i].pos_level = MsgAdapter.ReadChar()
	end

	self.data_list = data_list
end

---------------------------镜花水月End---------------------------

---------------------------攒福特权Start---------------------------

CSZanfutequanClientReq = CSZanfutequanClientReq or BaseClass(BaseProtocolStruct)
function CSZanfutequanClientReq:__init()
	self.msg_type = 16325
end

function CSZanfutequanClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

--攒福特权所有信息
SCZanfutequanAllInfo = SCZanfutequanAllInfo or BaseClass(BaseProtocolStruct)
function SCZanfutequanAllInfo:__init()
	self.msg_type = 16326
end

function SCZanfutequanAllInfo:Decode()
	self.level = MsgAdapter.ReadInt()								-- 特权等级
	self.final_reward_flag = MsgAdapter.ReadInt()					-- 终生奖励领取标识
	self.online_reward_flag = MsgAdapter.ReadInt()					-- 每日在线奖励领取标识
	self.mail_back_num = MsgAdapter.ReadInt()						-- 可领取或者跨天邮件返利领取灵玉
	self.today_back_num = MsgAdapter.ReadInt()						-- 当日累计返利可领取灵玉数量
	self.all_life_get_back_num = MsgAdapter.ReadLL()				-- 玩家这辈子可领取的特权返利灵玉数量
	self.today_online_time = MsgAdapter.ReadInt()					-- 今日在线时长
end
---------------------------攒福特权End---------------------------

-------------------------结义建设拓展Start---------------------------

SCJieYiTeamBuildAllInfo = SCJieYiTeamBuildAllInfo or BaseClass(BaseProtocolStruct)
function SCJieYiTeamBuildAllInfo:__init()
	self.msg_type = 16327
end

function SCJieYiTeamBuildAllInfo:Decode()
    self.build_level = MsgAdapter.ReadInt()
    self.build_exp = MsgAdapter.ReadInt()
    self.build_get_back_num = MsgAdapter.ReadInt()
    self.build_active_sum = MsgAdapter.ReadInt()
    local build_get_active_reward_flag = MsgAdapter.ReadInt()
    self.build_get_active_reward_flag = bit:d2b_l2h(build_get_active_reward_flag, nil, true)
    self.build_task_list = {}
    for i = 0, 63 do
        local task_seq = MsgAdapter.ReadInt()
        self.build_task_list[task_seq] = {}
        self.build_task_list[task_seq].task_seq = task_seq
        self.build_task_list[task_seq].task_progress = MsgAdapter.ReadInt()
        self.build_task_list[task_seq].task_status = MsgAdapter.ReadInt()
    end
end

SCJieYiTeamBuildTaskUpdate = SCJieYiTeamBuildTaskUpdate or BaseClass(BaseProtocolStruct)
function SCJieYiTeamBuildTaskUpdate:__init()
	self.msg_type = 16328
end

function SCJieYiTeamBuildTaskUpdate:Decode()
    local data = {}
    data.task_seq = MsgAdapter.ReadInt()
    data.task_progress = MsgAdapter.ReadInt()
    data.task_status = MsgAdapter.ReadInt() --建设任务状态 0:未领取 1:已领取
    self.change_data = data
end

SCJieYiTeamBuildLevelUpdate = SCJieYiTeamBuildLevelUpdate or BaseClass(BaseProtocolStruct)
function SCJieYiTeamBuildLevelUpdate:__init()
	self.msg_type = 16329
end

function SCJieYiTeamBuildLevelUpdate:Decode()
    self.build_level = MsgAdapter.ReadInt()
    self.build_exp = MsgAdapter.ReadInt()
end

SCJieYiTeamBuildFanliRewardUpdate = SCJieYiTeamBuildFanliRewardUpdate or BaseClass(BaseProtocolStruct)
function SCJieYiTeamBuildFanliRewardUpdate:__init()
	self.msg_type = 16330
end

function SCJieYiTeamBuildFanliRewardUpdate:Decode()
    self.build_get_back_num = MsgAdapter.ReadInt()
end

SCJieYiTeamBuildActiveUpdate = SCJieYiTeamBuildActiveUpdate or BaseClass(BaseProtocolStruct)
function SCJieYiTeamBuildActiveUpdate:__init()
	self.msg_type = 16331
end

function SCJieYiTeamBuildActiveUpdate:Decode()
    self.build_active_sum = MsgAdapter.ReadInt()
    local build_get_active_reward_flag = MsgAdapter.ReadInt()
    self.build_get_active_reward_flag = bit:d2b_l2h(build_get_active_reward_flag, nil, true)
end

-------------------------结义建设拓展End-------------------------------

-------------------------超级锦鲤Start---------------------------
SCLingYuPoolActiveAllInfo = SCLingYuPoolActiveAllInfo or BaseClass(BaseProtocolStruct)
function SCLingYuPoolActiveAllInfo:__init()
	self.msg_type = 16333
end

function SCLingYuPoolActiveAllInfo:Decode()
	self.totalGoldValue = MsgAdapter.ReadLL()
	self.playerRechargeValue = MsgAdapter.ReadInt()
	self.activeEndTime = MsgAdapter.ReadInt()
end
-------------------------超级锦鲤End-------------------------------

-------------------------王者特权Start---------------------------
SCKingPrivilegeInfo = SCKingPrivilegeInfo or BaseClass(BaseProtocolStruct)
function SCKingPrivilegeInfo:__init()
	self.msg_type = 16342
end

function SCKingPrivilegeInfo:Decode()
	self.end_time = MsgAdapter.ReadUInt()				--特权失效时间.
	self.is_fetch_daily_rewards = MsgAdapter.ReadChar()		--是否领取每日奖励 0:false, 1:true

	for i = 1, 3 do
		MsgAdapter.ReadChar()
	end
end

CSKingPrivilegeInfoReq = CSKingPrivilegeInfoReq or BaseClass(BaseProtocolStruct)
function CSKingPrivilegeInfoReq:__init()
	self.msg_type = 16343
end

function CSKingPrivilegeInfoReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.operate_type)
	MsgAdapter.WriteShort(self.param1)
end
-------------------------王者特权End-------------------------------

-------------------------盲盒卡包Start-------------------------------
SCBlindBoxCardAllInfo = SCBlindBoxCardAllInfo or BaseClass(BaseProtocolStruct)
function SCBlindBoxCardAllInfo:__init()
	self.msg_type = 16344
end

function SCBlindBoxCardAllInfo:Decode()
	local count_reward_flag = MsgAdapter.ReadUInt()
    self.draw_task_flag_list = bit:d2b_l2h(count_reward_flag, nil, true)
	
	local handbook_task_reward_flag = MsgAdapter.ReadUInt()
	self.handbook_task_flag_list = bit:d2b_l2h(handbook_task_reward_flag, nil, true)
	
	self.draw_num_list = {}
	for i = 0, MysteryBoxWGData.CARD_PACKAGE_NUM - 1 do
		self.draw_num_list[i] = MsgAdapter.ReadUInt()
	end

	self.convert_score = MsgAdapter.ReadUInt()

	self.shop_covert_num_list = {}--兑换商店已兑换次数
	for i = 0, MysteryBoxWGData.MAX_CONVERT_NUM - 1 do
		self.shop_covert_num_list[i] = MsgAdapter.ReadUChar()
	end

	self.draw_reward_num_list = {}--大奖道具已经抽到次数列表
	local info_list, reward_id, reward_count
	for i = 0, MysteryBoxWGData.CARD_PACKAGE_NUM - 1 do
		info_list = {}
		for j = 1, MysteryBoxWGData.MAX_CARD_REWARD_NUM do
			reward_id = MsgAdapter.ReadUShort()
			reward_count = MsgAdapter.ReadUShort()
			info_list[reward_id] = reward_count
		end
		self.draw_reward_num_list[i] = info_list
	end
end

SCBlindBoxCardDrawInfo = SCBlindBoxCardDrawInfo or BaseClass(BaseProtocolStruct)
function SCBlindBoxCardDrawInfo:__init()
	self.msg_type = 16345
end

function SCBlindBoxCardDrawInfo:Decode()

	self.convert_score = MsgAdapter.ReadUInt()
	self.card_package_id = MsgAdapter.ReadUInt()

	self.draw_num = MsgAdapter.ReadUInt()

	self.draw_rewards = {}--大奖道具已经抽到次数列表
	local reward_id, reward_count
	for j = 1, MysteryBoxWGData.MAX_CARD_REWARD_NUM do
		reward_id = MsgAdapter.ReadUShort()
		reward_count = MsgAdapter.ReadUShort()
		self.draw_rewards[reward_id] = reward_count
	end

	self.draw_result_list = {}
	local count = MsgAdapter.ReadInt()
	for i = 1, count do
		self.draw_result_list[i] = MsgAdapter.ReadItemId()
	end
	-- print_error(count, self.draw_rewards, self.draw_result_list, self.card_package_id)
end

SCBlindBoxCardCountRewardInfo = SCBlindBoxCardCountRewardInfo or BaseClass(BaseProtocolStruct)
function SCBlindBoxCardCountRewardInfo:__init()
	self.msg_type = 16346
end

function SCBlindBoxCardCountRewardInfo:Decode()
	local count_reward_flag = MsgAdapter.ReadUInt()
    self.draw_task_flag_list = bit:d2b_l2h(count_reward_flag, nil, true)
end

SCBlindBoxCardHandbookTaskInfo = SCBlindBoxCardHandbookTaskInfo or BaseClass(BaseProtocolStruct)
function SCBlindBoxCardHandbookTaskInfo:__init()
	self.msg_type = 16347
end

function SCBlindBoxCardHandbookTaskInfo:Decode()
	local handbook_task_reward_flag = MsgAdapter.ReadUInt()
	self.handbook_task_flag_list = bit:d2b_l2h(handbook_task_reward_flag, nil, true)
end

SCBlindBoxCardConvertInfo = SCBlindBoxCardConvertInfo or BaseClass(BaseProtocolStruct)
function SCBlindBoxCardConvertInfo:__init()
	self.msg_type = 16348
end

function SCBlindBoxCardConvertInfo:Decode()
	self.convert_score = MsgAdapter.ReadUInt()

	self.shop_covert_num_list = {}
	for i = 0, MysteryBoxWGData.MAX_CONVERT_NUM - 1 do
		self.shop_covert_num_list[i] = MsgAdapter.ReadUChar()
	end
end

SCBlindBoxCardRecordInfo = SCBlindBoxCardRecordInfo or BaseClass(BaseProtocolStruct)
function SCBlindBoxCardRecordInfo:__init()
	self.msg_type = 16349
end

function SCBlindBoxCardRecordInfo:Decode()
	local count = MsgAdapter.ReadInt()

	self.log_list = {}
	for i = 1, count do
		self.log_list[i] = {}
		self.log_list[i].role_name = MsgAdapter.ReadName()
		self.log_list[i].item_id = MsgAdapter.ReadItemId()
		self.log_list[i].item_num  = MsgAdapter.ReadUShort()
		self.log_list[i].time_stamp  = MsgAdapter.ReadUInt()
	end
end
-------------------------盲盒卡包End-------------------------------

-------------------------奇闻异录Start---------------------------
CSHandbookClientReq = CSHandbookClientReq or BaseClass(BaseProtocolStruct)
function CSHandbookClientReq:__init()
	self.msg_type = 16350
end

function CSHandbookClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

local function GetQiWenTuJianItem(seq)
	local tujian_item = {}
	tujian_item.seq = seq
	tujian_item.level = MsgAdapter.ReadShort()
	tujian_item.zhuling_level = MsgAdapter.ReadChar()
	tujian_item.zhuling_exp = MsgAdapter.ReadChar()

	return tujian_item
end

-- 图鉴 所有信息
SCHandbookAllInfo = SCHandbookAllInfo or BaseClass(BaseProtocolStruct)
function SCHandbookAllInfo:__init()
	self.msg_type = 16351
end

function SCHandbookAllInfo:Decode()
	local tujian_list = {}
	for i = 0, 1023 do
		local tujian_item = GetQiWenTuJianItem(i)
		tujian_list[i] = tujian_item
	end

	self.tujian_list = tujian_list

	local jiban_level_list = {}
	for i = 0, 99 do
		jiban_level_list[i] = MsgAdapter.ReadChar()
	end

	self.jiban_level_list = jiban_level_list
end

-- 图鉴 单个信息
SCHandbookDataOneUpdate = SCHandbookDataOneUpdate or BaseClass(BaseProtocolStruct)
function SCHandbookDataOneUpdate:__init()
	self.msg_type = 16352
end

function SCHandbookDataOneUpdate:Decode()
	self.seq = MsgAdapter.ReadInt()
	self.level = MsgAdapter.ReadShort()
	self.zhuling_level = MsgAdapter.ReadChar()
	self.zhuling_exp = MsgAdapter.ReadChar()
end

-- 图鉴羁绊 单个信息
SCHandbookJibanOneUpdate = SCHandbookJibanOneUpdate or BaseClass(BaseProtocolStruct)
function SCHandbookJibanOneUpdate:__init()
	self.msg_type = 16353
end

function SCHandbookJibanOneUpdate:Decode()
	self.jiban_seq = MsgAdapter.ReadInt()
	self.jiban_level = MsgAdapter.ReadInt()
end

local function GetQiWenBagGridItem()
	local grid_item = {}
	grid_item.index = MsgAdapter.ReadInt()
	grid_item.item_id = MsgAdapter.ReadUShort()
	MsgAdapter.ReadShort()
	grid_item.num = MsgAdapter.ReadInt()
	grid_item.is_bind = MsgAdapter.ReadChar()
	grid_item.param1 = MsgAdapter.ReadChar()
	grid_item.param2 = MsgAdapter.ReadChar()
	grid_item.param3 = MsgAdapter.ReadChar()

	return grid_item
end

-- 图鉴 - 背包列表
SCHandbookBagList = SCHandbookBagList or BaseClass(BaseProtocolStruct)
function SCHandbookBagList:__init()
	self.msg_type = 16354
end

function SCHandbookBagList:Decode()
	self.grid_count = MsgAdapter.ReadInt()
	local grid_list = {}
	for i = 0, self.grid_count - 1 do
		local grid_item = GetQiWenBagGridItem()
		grid_list[grid_item.index] = grid_item
	end

	self.grid_list = grid_list
end

-- 图鉴 - 背包信息变化
SCHandbookBagUpdateInfo = SCHandbookBagUpdateInfo or BaseClass(BaseProtocolStruct)
function SCHandbookBagUpdateInfo:__init()
    self.msg_type = 16355
end

function SCHandbookBagUpdateInfo:Decode()
	self.equip_item = GetQiWenBagGridItem()
end

-- 图鉴 - 装备分解请求
CSHandbookDecompos = CSHandbookDecompos or BaseClass(BaseProtocolStruct)
function CSHandbookDecompos:__init()
    self.msg_type = 16356
	self.decompos_list = {}
end

function CSHandbookDecompos:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(#self.decompos_list)

	for i = 1, #self.decompos_list do
		local data = self.decompos_list[i]
		MsgAdapter.WriteItemId(data.item_id)
		MsgAdapter.WriteShort(data.index)
		MsgAdapter.WriteInt(data.num)
	end
end
-------------------------奇闻异录End---------------------------

----------------------------------------魂阵相关  开始-----------------------------------------
CSHunZhenOperate = CSHunZhenOperate or BaseClass(BaseProtocolStruct)
function CSHunZhenOperate:__init()
	self.msg_type = 16334
end

function CSHunZhenOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)

	for k, v in ipairs(self.param5) do
		MsgAdapter.WriteUShort(v)
	end
end


--下发所有信息
SCHunZhenAllInfo = SCHunZhenAllInfo or BaseClass(BaseProtocolStruct)
function SCHunZhenAllInfo:__init()
	self.msg_type = 16335
end

--魂阵的所有结构
local function GetWuHunStructInfoItem()
	local data = {}
	data.wuhun_id = MsgAdapter.ReadShort()                     --武魂id -1 武魂没激活
	data.front_use_index = MsgAdapter.ReadShort()              --幻化的魂阵index

	data.front_list = {}
	for i = 0, COMMON_GAME_ENUM.EIGHT - 1 do --索引从0开始
		data.front_list[i] = {}
		local front_list = data.front_list[i]

		front_list.wuhun_front_index = i						--魂阵索引
		front_list.gem_level_index = MsgAdapter.ReadChar()      --魂石等级激活属性index
		front_list.engrave_level_index = MsgAdapter.ReadChar()  --刻印等级激活属性index
		front_list.gem_grade_index = MsgAdapter.ReadChar()      --魂石品阶激活属性index
		MsgAdapter.ReadChar()

		front_list.gem_list = {}
		for j = 0, COMMON_GAME_ENUM.NINE - 1 do  --索引从0开始
			front_list.gem_list[j] = {}
			local gem_list = front_list.gem_list[j]

			gem_list.front_gem_index = j                             --魂石索引
			gem_list.gem_star = MsgAdapter.ReadShort()               -- 魂石星级
			gem_list.gem_level = MsgAdapter.ReadShort()              -- 魂石等级
			gem_list.gem_grade = MsgAdapter.ReadShort()              -- 魂石品级
			MsgAdapter.ReadShort()

			gem_list.engrave_list = {}
			for m = 1, COMMON_GAME_ENUM.SIX do
				gem_list.engrave_list[m] = MsgAdapter.ReadUShort()  --刻印物品id
			end
		end
	end

	return data
end

function SCHunZhenAllInfo:Decode()
	self.count = MsgAdapter.ReadInt()
	self.front_gem_list = {}

	for i = 1, self.count do
		local data = GetWuHunStructInfoItem()
		self.front_gem_list[data.wuhun_id] = data
	end
end


--更新魂阵信息
SCHunZhenSoulFrontInfo = SCHunZhenSoulFrontInfo or BaseClass(BaseProtocolStruct)
function SCHunZhenSoulFrontInfo:__init()
	self.msg_type = 16336
end

function SCHunZhenSoulFrontInfo:Decode()
	self.wuhun_id = MsgAdapter.ReadShort()            --武魂id
	self.wuhun_front_index = MsgAdapter.ReadShort()   --魂阵索引
	self.gem_level_index = MsgAdapter.ReadChar()      --魂石等级激活属性index
	self.engrave_level_index = MsgAdapter.ReadChar()   --刻印等级激活属性index
	self.gem_grade_index = MsgAdapter.ReadChar()  --魂石品阶激活属性index
	MsgAdapter.ReadChar()
end


--更新魂石信息
SCHunZhenSoulStoneInfo = SCHunZhenSoulStoneInfo or BaseClass(BaseProtocolStruct)
function SCHunZhenSoulStoneInfo:__init()
	self.msg_type = 16337
end

function SCHunZhenSoulStoneInfo:Decode()
	self.wuhun_id = MsgAdapter.ReadShort()            --武魂id
	self.wuhun_front_index = MsgAdapter.ReadShort()   --魂阵索引
	self.front_gem_index = MsgAdapter.ReadShort()     --魂石索引
	self.gem_star = MsgAdapter.ReadShort()            --魂石星级
	self.gem_level = MsgAdapter.ReadShort()           --魂石等级
	self.gem_grade = MsgAdapter.ReadShort()           --魂石品级
end


--更新刻印信息
SCHunZhenEngraveInfo = SCHunZhenEngraveInfo or BaseClass(BaseProtocolStruct)
function SCHunZhenEngraveInfo:__init()
	self.msg_type = 16338
end

function SCHunZhenEngraveInfo:Decode()
	self.wuhun_id = MsgAdapter.ReadShort()              --武魂id
	self.wuhun_front_index = MsgAdapter.ReadShort()     --魂阵索引
	self.front_gem_index = MsgAdapter.ReadShort()       --魂石索引
	MsgAdapter.ReadShort()                              --对齐
	self.engrave_list = {}
	for i = 1, COMMON_GAME_ENUM.SIX do
		self.engrave_list[i] = MsgAdapter.ReadShort()
	end
end


--更新魂阵幻化信息
SCHunZhenSoulFrontUse = SCHunZhenSoulFrontUse or BaseClass(BaseProtocolStruct)
function SCHunZhenSoulFrontUse:__init()
	self.msg_type = 16339
end

function SCHunZhenSoulFrontUse:Decode()
	self.wuhun_id = MsgAdapter.ReadShort()            --武魂id
	self.front_use_index = MsgAdapter.ReadShort()     --幻化的魂阵index
end


-- 武魂 - 背包信息
SCWuHunBag = SCWuHunBag or BaseClass(BaseProtocolStruct)

local function GetWuHunFrontBagGridItem()
	local grid_item = {}
	grid_item.index = MsgAdapter.ReadInt()
	grid_item.item_id = MsgAdapter.ReadUShort()
	MsgAdapter.ReadShort()
	grid_item.num = MsgAdapter.ReadInt()
	grid_item.is_bind = MsgAdapter.ReadChar()
	grid_item.param1 = MsgAdapter.ReadChar()
	grid_item.param2 = MsgAdapter.ReadChar()
	grid_item.param3 = MsgAdapter.ReadChar()

	if grid_item.item_id > 0 then
		local item_cfg = ItemWGData.Instance:GetItemConfig(grid_item.item_id)
		grid_item.color = item_cfg and item_cfg.color or 0
	else
		grid_item.color = GameEnum.ITEM_COLOR_WHITE
	end

	local cfg = WuHunFrontWGData.Instance:GetEngraveCfgByItemId(grid_item.item_id)
	if cfg then
		grid_item.engrave_type = cfg.type
		grid_item.level = cfg.level
	else
		grid_item.engrave_type = 0
		grid_item.level = 0
	end

	return grid_item
end

function SCWuHunBag:__init()
    self.msg_type = 16340
end

function SCWuHunBag:Decode()
	self.grid_count = MsgAdapter.ReadInt()
	self.grid_list = {}
	for i = 1, self.grid_count do
		local grid_item = GetWuHunFrontBagGridItem()
		self.grid_list[grid_item.index] = grid_item
	end
end

-- 武魂 - 背包信息变化
SCWuHunBagGrid = SCWuHunBagGrid or BaseClass(BaseProtocolStruct)
function SCWuHunBagGrid:__init()
    self.msg_type = 16341
end

function SCWuHunBagGrid:Decode()
	self.grid_info = GetWuHunFrontBagGridItem()
end
----------------------------------------------魂阵相关 结束------------------------------------------

--------------------------------------跨服冲榜 昨日排行榜 start--------------------------------------
SCCrossYesterdayRankInfo = SCCrossYesterdayRankInfo or BaseClass(BaseProtocolStruct)
local function GetAttributeStoneLastInfoItem()
	local data = {}
	data.uuid = MsgAdapter.ReadUUID()
	data.name = MsgAdapter.ReadStrN(32)
	data.rank_value = MsgAdapter.ReadLL()
	data.rank = MsgAdapter.ReadInt()
	return data
end

function SCCrossYesterdayRankInfo:__init()
    self.msg_type = 16357
end

function SCCrossYesterdayRankInfo:Decode()
	self.count = MsgAdapter.ReadInt()
	local rank_list = {}
	for i = 1, self.count do
		local data = GetAttributeStoneLastInfoItem()
		rank_list[data.rank] = data
	end

	self.last_rank_item_list = rank_list
end

--------------------------------------跨服冲榜 昨日排行榜 end--------------------------------------

--------------------------------------沧溟拓展 道魂 start--------------------------------------
SCWaistSoulActiviate = SCWaistSoulActiviate or BaseClass(BaseProtocolStruct)
function SCWaistSoulActiviate:__init()
    self.msg_type = 16361
end

function SCWaistSoulActiviate:Decode()
	self.cangming_id = MsgAdapter.ReadInt()
end

SCWaistSoulLevelInfo = SCWaistSoulLevelInfo or BaseClass(BaseProtocolStruct)
function SCWaistSoulLevelInfo:__init()
    self.msg_type = 16362
end

function SCWaistSoulLevelInfo:Decode()
	self.cangming_id = MsgAdapter.ReadInt()
	self.daohun_level = MsgAdapter.ReadInt()
end

--------------------------------------沧溟拓展 道魂 end--------------------------------------

--------------------------------------沧溟拓展 乾天录 start--------------------------------------
SCWaistAchievementInfo = SCWaistAchievementInfo or BaseClass(BaseProtocolStruct)
function SCWaistAchievementInfo:__init()
    self.msg_type = 16363
end

function SCWaistAchievementInfo:Decode()
	self.task_id = MsgAdapter.ReadInt()
end

SCWaistAllAchievementInfo = SCWaistAllAchievementInfo or BaseClass(BaseProtocolStruct)
function SCWaistAllAchievementInfo:__init()
    self.msg_type = 16364
end

function SCWaistAllAchievementInfo:Decode()
	local max_count = 159
	self.active_list = {}
	for i = 0, max_count do
		self.active_list[i] = MsgAdapter.ReadUChar()
	end
end
--------------------------------------沧溟拓展 乾天录 end--------------------------------------

--------------------------------------独尊领域-五行通天 start--------------------------------------
--全部领域的星级.
SCFootLightAllStarAttrInfo = SCFootLightAllStarAttrInfo or BaseClass(BaseProtocolStruct)
function SCFootLightAllStarAttrInfo:__init()
	self.msg_type = 16365 
	self.info = {}
end

function SCFootLightAllStarAttrInfo:Decode()
	local list = {}
	for i = 0, 39 do
		list[i] = MsgAdapter.ReadShort()
	end
	
	self.info = list
end

--单个总属性的信息.
SCFootLightSingleTotalStarAttrInfo = SCFootLightSingleTotalStarAttrInfo or BaseClass(BaseProtocolStruct)
function SCFootLightSingleTotalStarAttrInfo:__init()
	self.msg_type = 16368
end

function SCFootLightSingleTotalStarAttrInfo:Decode()
	self.foot_light_total_star = MsgAdapter.ReadInt()
end
--------------------------------------独尊领域-五行通天 end----------------------------------------


---------------------------------------战神特权 start--------------------------------------
-- 战神特权请求
CSRoleZhanshenPrivilegeReq = CSRoleZhanshenPrivilegeReq or BaseClass(BaseProtocolStruct)
function CSRoleZhanshenPrivilegeReq:__init()
	self.msg_type = 16369
end

function CSRoleZhanshenPrivilegeReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.operate_type)
	MsgAdapter.WriteShort(self.param1)
end

--战神特权属性
SCRoleZhanshenPrivilegeInfo = SCRoleZhanshenPrivilegeInfo or BaseClass(BaseProtocolStruct)
function SCRoleZhanshenPrivilegeInfo:__init()
	self.msg_type = 16370
end

function SCRoleZhanshenPrivilegeInfo:Decode()
	self.total_count = MsgAdapter.ReadInt()
	self.level = MsgAdapter.ReadInt()
	self.open_status = MsgAdapter.ReadChar()
	self.is_fetch_daily_rewards = MsgAdapter.ReadChar()
	for i = 1, 2 do
		MsgAdapter.ReadChar()
	end
end
---------------------------------------战神特权 end--------------------------------------

---------------------------------------武魂材料直购 start--------------------------------------
SCRoleWuHunRmbBuyInfo = SCRoleWuHunRmbBuyInfo or BaseClass(BaseProtocolStruct)
function SCRoleWuHunRmbBuyInfo:__init()
	self.msg_type = 16373
end

function SCRoleWuHunRmbBuyInfo:Decode()
	self.wuhun_id = MsgAdapter.ReadInt()
	self.grade = MsgAdapter.ReadInt()
	self.discount_finish_time = MsgAdapter.ReadInt()
end
---------------------------------------武魂材料直购 end--------------------------------------

---------------------------------------武魂特权 start--------------------------------------
SCWuHunDraw2Result = SCWuHunDraw2Result or BaseClass(BaseProtocolStruct)
function SCWuHunDraw2Result:__init()
	self.msg_type = 16374
end

function SCWuHunDraw2Result:Decode()
	self.mode = MsgAdapter.ReadInt() --抽奖模式.
	self.count = MsgAdapter.ReadInt() --数组长度.

	self.result_item_list = {}
	for i = 1, self.count do
		local data = {
			item_id = MsgAdapter.ReadUShort(),
			reversh = MsgAdapter.ReadChar(),
			is_bind = MsgAdapter.ReadChar(),
			num = MsgAdapter.ReadInt(),
		}
		self.result_item_list[i] = data
	end
end

SCWuHunRightUpdate = SCWuHunRightUpdate or BaseClass(BaseProtocolStruct)
function SCWuHunRightUpdate:__init()
	self.msg_type = 16375
end

function SCWuHunRightUpdate:Decode()
	self.wuhun_prerogative_level = MsgAdapter.ReadInt() --武魂特权等级.
	self.wuhun_prerogative_flag = MsgAdapter.ReadInt() --武魂特权领取标识,0:未领取,1:已领取.
end
---------------------------------------武魂特权 end--------------------------------------

---------------------------------------寻缘 start--------------------------------------
CSQingYuanZhengHunReq = CSQingYuanZhengHunReq or BaseClass(BaseProtocolStruct)
function CSQingYuanZhengHunReq:__init()
	self.msg_type = 16376
end

function CSQingYuanZhengHunReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.msg_length)
	MsgAdapter.WriteStrN(self.content, self.msg_length)
end

SCQingYuanZhengHunInfo = SCQingYuanZhengHunInfo or BaseClass(BaseProtocolStruct)
function SCQingYuanZhengHunInfo:__init()
	self.msg_type = 16377
end

function SCQingYuanZhengHunInfo:Decode()
	self.page_count = MsgAdapter.ReadInt() --总页数
	self.zhenghun_info_flag = MsgAdapter.ReadInt() --发布寻缘信息标记，0：未发布 1：已发布
	self.info_count = MsgAdapter.ReadInt()
	self.show_zhenghun_info = {}
	for i = 1, self.info_count do
		local stu = XunYuanStruct.XunYuanItemRead()
		table.insert(self.show_zhenghun_info, stu)
	end
end

XunYuanStruct = XunYuanStruct or {}

function XunYuanStruct.XunYuanItemRead()
	local stu = {}
	stu.role_name = MsgAdapter.ReadStrN(32)
	stu.role_uid = MsgAdapter.ReadInt()
	stu.role_level = MsgAdapter.ReadInt()
	stu.role_sex = MsgAdapter.ReadInt()
	stu.prof = MsgAdapter.ReadShort()		-- 角色职业
	stu.is_online = MsgAdapter.ReadShort()		-- 是否在线
	stu.zhenghun_info_time =  MsgAdapter.ReadUInt()  -- 寻缘信息发布时间
	stu.zhenghun_info = MsgAdapter.ReadStrN(128)		-- 寻缘信息 
	return stu
end
---------------------------------------寻缘 end--------------------------------------

---------------------------------------幻兽分解和技能相关 start--------------------------------------
CSBeastBreakListReq = CSBeastBreakListReq or BaseClass(BaseProtocolStruct)
function CSBeastBreakListReq:__init()
	self.msg_type = 16378
end

function CSBeastBreakListReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.list_len)

	for i = 1, self.list_len do
		if self.operate_list[i] then
			MsgAdapter.WriteShort(self.operate_list[i])
		end
	end
end

CSBeastResetListReq = CSBeastResetListReq or BaseClass(BaseProtocolStruct)
function CSBeastResetListReq:__init()
	self.msg_type = 16379
end

function CSBeastResetListReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.list_len)
	
	for i = 1, self.list_len do
		if self.operate_list[i] then
			MsgAdapter.WriteShort(self.operate_list[i])
		end
	end
end

CSBeastUseSkill = CSBeastUseSkill or BaseClass(BaseProtocolStruct)
function CSBeastUseSkill:__init()
	self.msg_type = 16380
end

function CSBeastUseSkill:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.skill_id)
	MsgAdapter.WriteInt(self.target_id)
	MsgAdapter.WriteInt(self.pos_x)
	MsgAdapter.WriteInt(self.pos_y)
end

SCBeastFightInfo = SCBeastFightInfo or BaseClass(BaseProtocolStruct)
function SCBeastFightInfo:__init()
	self.msg_type = 16381
end

function SCBeastFightInfo:Decode()
	self.beast_fight_info = {}
	for i = 1, BEAST_DEFINE.BEAST_MAIN_BATTLE_COUNT do
		self.beast_fight_info[i] = {}
		self.beast_fight_info[i].bag_id = MsgAdapter.ReadInt() 				--驭兽背包id
		self.beast_fight_info[i].beast_id = MsgAdapter.ReadInt() 			--驭兽id
		self.beast_fight_info[i].skill_id = MsgAdapter.ReadInt() 			--技能id
		self.beast_fight_info[i].cd_change_time = MsgAdapter.ReadLL() 		--冷却时间
		self.beast_fight_info[i].cd_finish_time = MsgAdapter.ReadLL() 		--冷却时间
	end
end

CSBeastFightChange = CSBeastFightChange or BaseClass(BaseProtocolStruct)
function CSBeastFightChange:__init()
	self.msg_type = 16382
end

function CSBeastFightChange:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.battle_index)
end
---------------------------------------幻兽分解和技能相关 end--------------------------------------

---------------------------------------紫云秘录 start--------------------------------------
CSZiYunMiLuClientReq = CSZiYunMiLuClientReq or BaseClass(BaseProtocolStruct)
function CSZiYunMiLuClientReq:__init()
	self.msg_type = 16383
end

function CSZiYunMiLuClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
end

SCZiYunMiLuInfo = SCZiYunMiLuInfo or BaseClass(BaseProtocolStruct)
function SCZiYunMiLuInfo:__init()
	self.msg_type = 16384
end

function SCZiYunMiLuInfo:Decode()
	self.show_total_capability = 0
	self.show_capability_list = {}
	for i = 1, 16 do
		local capability = MsgAdapter.ReadLL()
		if capability >= 0 then
			self.show_total_capability = self.show_total_capability + capability
			table.insert(self.show_capability_list, capability)
		end
	end

	self.show_item_list = {}
	for i = 1, 20 do
		local item = {
			item_id = MsgAdapter.ReadUShort(),
			is_bind = MsgAdapter.ReadUShort(),
		}

		if item.item_id > 0 then
			table.insert(self.show_item_list, item)
		else
			break
		end
	end
end
---------------------------------------紫云秘录 end--------------------------------------

---------------------------------------每日累充 start--------------------------------------
--每日累充--秒杀.
SCRADailyLeiChongRapidlyGiftInfo = SCRADailyLeiChongRapidlyGiftInfo or BaseClass(BaseProtocolStruct)
function SCRADailyLeiChongRapidlyGiftInfo:__init()
	self.msg_type = 16385
end

function SCRADailyLeiChongRapidlyGiftInfo:Decode()
	self.free_reward_flag = MsgAdapter.ReadChar()		--免费奖励领取标志，0：未领取，1：已领取.
	self.super_reward_flag = MsgAdapter.ReadChar()		--超级返利奖励领取标志，0：未领取，1：已领取.
	self.ten_day_remain = MsgAdapter.ReadChar()			--购买10天的剩余可领取天数.
	self.rapidly_gift_flag = MsgAdapter.ReadChar()		--秒杀礼包奖励领取标志.
end

--每日累充--秒杀礼包记录信息.
SCRapidlyGiftRecordInfo = SCRapidlyGiftRecordInfo or BaseClass(BaseProtocolStruct)
function SCRapidlyGiftRecordInfo:__init()
	self.msg_type = 16386
end

local function ReadListInfo()
	local count = 50
	local list_data = {}
	for i = 1, count do
		local data = {
			role_name = MsgAdapter.ReadName(),		--角色名字.
			item_id = MsgAdapter.ReadUShort(),		--道具ID或秒杀礼包ID.
			re_sh = MsgAdapter.ReadUShort(),		--对齐.
			timestamp = MsgAdapter.ReadUInt(),		--时间.
		}
		list_data[i] = data
	end

	return list_data
end

function SCRapidlyGiftRecordInfo:Decode()
	self.buy_record_list = ReadListInfo()		--购买记录列表.
	self.draw_record_list = ReadListInfo()		--抽奖记录列表.
end

---------------------------------------每日累充 end--------------------------------------

--------------------------------------每日累充-每日礼包 start---------------------------
SCRADailyLeiChongDailyGiftInfo = SCRADailyLeiChongDailyGiftInfo or BaseClass(BaseProtocolStruct)
function SCRADailyLeiChongDailyGiftInfo:__init()
	self.msg_type = 16387
end

function SCRADailyLeiChongDailyGiftInfo:Decode()
	self.grade = MsgAdapter.ReadShort()
	self.re_ch = MsgAdapter.ReadChar()
	self.free_reward_flag = MsgAdapter.ReadChar()
	self.daily_gift_count = {}
	for i = 1, 12 do
        self.daily_gift_count[i] = MsgAdapter.ReadChar()
    end
end
---------------------------------------每日累充-每日礼包 end--------------------------------------

---------------------------------------装备洗练新 start--------------------------------------
-- 装备洗练 单个信息
SCEquipBaptizeOneInfo = SCEquipBaptizeOneInfo or BaseClass(BaseProtocolStruct)
function SCEquipBaptizeOneInfo:__init()
	self.msg_type = 16388
end

function SCEquipBaptizeOneInfo:Decode()
	self.equip_part = MsgAdapter.ReadInt()								-- 部位  0 - 375  装备equip_index
	self.today_free_baptize_num = MsgAdapter.ReadInt()					-- 今日剩余免费鉴定次数
	self.baptize_part = ProtocolStruct.ReadEquipBaptizePart()			-- 洗练部位
end
---------------------------------------装备洗练新 end--------------------------------------

---------------------------------------竞技场1v1排行榜 start--------------------------------------
--竞技场1v1操作.
CSChallengeFieldOperate = CSChallengeFieldOperate or BaseClass(BaseProtocolStruct)
function CSChallengeFieldOperate:__init()
	self.msg_type = 16389
end

function CSChallengeFieldOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
end

--竞技场1v1排行.
SCChallengeRankListInfo = SCChallengeRankListInfo or BaseClass(BaseProtocolStruct)
function SCChallengeRankListInfo:__init()
	self.msg_type = 16390
end

function SCChallengeRankListInfo:Decode()
	self.challenger_info = {}
	for i = 1, 11 do
		local data = {}
		data.uid = MsgAdapter.ReadInt()					--角色UID.
		data.rank_pos = MsgAdapter.ReadInt()			--排名，0是第一名.
		data.have_like_count = MsgAdapter.ReadInt()		--被点赞数.
		data.capatility = MsgAdapter.ReadLL()			--战力.
		data.role_name = MsgAdapter.ReadName()			--角色名称.
		table.insert(self.challenger_info, data)		--排行榜信息，第11个是玩家自己.
	end

	self.daily_like_flag = MsgAdapter.ReadShort()		--每天点赞标记 bit.
	self.re_sh = MsgAdapter.ReadShort()		--对齐.
end

--竞技场1v1奖励信息.
SCChallengeFieldRewardInfo = SCChallengeFieldRewardInfo or BaseClass(BaseProtocolStruct)
function SCChallengeFieldRewardInfo:__init()
	self.msg_type = 16391
end

function SCChallengeFieldRewardInfo:Decode()
	self.count_reward_flag = MsgAdapter.ReadInt()		--次数奖励标记 bit.
	self.rank_reward_flag = MsgAdapter.ReadInt()		--排名奖励标记 bit.
end

--竞技场1v1信息.
SCChallengeFieldInfo = SCChallengeFieldInfo or BaseClass(BaseProtocolStruct)
function SCChallengeFieldInfo:__init()
	self.msg_type = 16392
end

function SCChallengeFieldInfo:Decode()
	self.grade = MsgAdapter.ReadInt()				--档位.
end

--最强仙尊排行数据
SCZuiQiangXianZunInfo = SCZuiQiangXianZunInfo or BaseClass(BaseProtocolStruct)
function SCZuiQiangXianZunInfo:__init()
	self.msg_type = 16393
	self.rank_list = {}
end

function SCZuiQiangXianZunInfo:Decode()
	local list = {}
	for i = 1, 11 do
		list[i] = ProtocolStruct.ReadRankListInfo()
	end
	self.rank_list = list
end
---------------------------------------竞技场1v1排行榜 end--------------------------------------

---------------------------------------Boss战令 start--------------------------------------
CSHuntMonsterOrderClientReq = CSHuntMonsterOrderClientReq or BaseClass(BaseProtocolStruct)
function CSHuntMonsterOrderClientReq:__init()
	self.msg_type = 16394
end

function CSHuntMonsterOrderClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
end

SCHuntMonsterOrderInfo = SCHuntMonsterOrderInfo or BaseClass(BaseProtocolStruct)
function SCHuntMonsterOrderInfo:__init()
	self.msg_type = 16395
end

function SCHuntMonsterOrderInfo:Decode()
	self.score = MsgAdapter.ReadInt()
	self.grade = MsgAdapter.ReadInt()
	self.senior_order_flag = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
	self.next_grade_time = MsgAdapter.ReadUInt()

	self.free_reward_flag = {}
	for i = 1, 64 do
		self.free_reward_flag[i] = MsgAdapter.ReadUChar()
	end

	self.added_reward_flag = {}
	for i = 1, 64 do
		self.added_reward_flag[i] = MsgAdapter.ReadUChar()
	end
end
---------------------------------------Boss战令 end--------------------------------------

---------------------------------------技能突破直购 start--------------------------------------
--最强仙尊排行数据
SCSkillBreakRmbBuyInfo = SCSkillBreakRmbBuyInfo or BaseClass(BaseProtocolStruct)
function SCSkillBreakRmbBuyInfo:__init()
	self.msg_type = 16396
end

function SCSkillBreakRmbBuyInfo:Decode()
	self.reward_flat = MsgAdapter.ReadChar()			-- 奖励购买标记
	self.re_ch = MsgAdapter.ReadChar()					-- 对齐
	self.grade = MsgAdapter.ReadUShort()				-- 档位
	self.discount_end_time = MsgAdapter.ReadUInt()		-- 折扣结束时间
end
---------------------------------------技能突破直购 end--------------------------------------