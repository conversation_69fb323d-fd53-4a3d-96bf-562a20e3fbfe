TianShenSelectView = TianShenSelectView or BaseClass(SafeBaseView)

function TianShenSelectView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(900,568)})
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_select_tianshen")

	self.cell_list = {}
	local is_fight = false
	self.data = nil
	self.index = -1
end

function TianShenSelectView:__delete()

end

function TianShenSelectView:ReleaseCallBack()
	self.list_view = nil
	for k, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
	self.cell_list = {}
	self.image_list = nil
	local is_fight = false
	self.data = nil
	self.index = -1

	if self.select_list_view then
		self.select_list_view:DeleteMe()
		self.select_list_view = nil
	end
end

function TianShenSelectView:LoadCallBack()
	self.node_list["th_tips"]:SetActive(false)
	self.node_list.title_view_name.text.text = Language.TianShen.TSSelect
	local has_list = TianShenWGData.Instance:GetFightTisnShen(false)
	if #has_list <= 0  then
		self.node_list["th_tips"]:SetActive(true)
	else
		self.node_list["th_tips"]:SetActive(false)
	end

	if nil == self.select_list_view then
        self.select_list_view = AsyncBaseGrid.New()
        local bundle = "uis/view/tianshen_prefab"
        local asset = "ph_select_tianshen_itemrender"
        self.select_list_view:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_select_baoshi_list"],
            assetBundle = bundle, assetName = asset, itemRender = BattleItem2})
		self.select_list_view:SetSelectCallBack(BindTool.Bind1(self.OnClickRenderCalllBack, self))
		self.select_list_view:SetStartZeroIndex(false)
	end
end

function TianShenSelectView:OnClickRenderCalllBack()
	print_error("OnClickRenderCalllBack")
end

function TianShenSelectView:OnXieXiaClick()
	if not self.data or not self.is_fight then return end
	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type3, self.data.index, self.index)
	self:Close()
end

function TianShenSelectView:OpenCallBack()
	self.image_list = TianShenWGData.Instance:GetFightTisnShen(false)
end

function TianShenSelectView:OnFlush(param_t)
	if not param_t or not param_t.all then return end
	self.data = param_t.all.data
	self.index = param_t.all.index
	self.is_fight = false ~= param_t.all.data
	local data = {}
	for i = 1, #self.image_list do
		data[i] = {}
		data[i].info = self.image_list[i]
		data[i].index = self.index
	end

	self.select_list_view:SetDataList(data)
end

BattleItem2 = BattleItem2  or BaseClass(BaseRender)

function BattleItem2:LoadCallBack()
	self.is_first = true
	XUI.AddClickEventListener(self.node_list["btn_chuzhan"], BindTool.Bind(self.OnClick, self))
	XUI.AddClickEventListener(self.node_list["skill_btn"], BindTool.Bind(self.OnClick, self))

	self.zhu_skill = {}

	self.appearance_zhu_skill_list = {}
	self.zhu_skill_list_view = self.node_list["zhu_skill_list"]
	local list_delegate = self.zhu_skill_list_view.list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetSkillOfCells, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshSkillCell, self)
end

function BattleItem2:GetSkillOfCells()
	return #self.zhu_skill
end

function BattleItem2:OnClickBtnWuXingTip()
	ViewManager.Instance:Open(GuideModuleName.WuXingTip)
end

function BattleItem2:RefreshSkillCell(cell, cell_index)
	local item_cell = self.appearance_zhu_skill_list[cell]
	if item_cell == nil then
		item_cell = TianShenSkillItem.New(cell.gameObject, self)
		item_cell:SetIndex(cell_index)
		item_cell:SetDefaultFlag(true)
		self.appearance_zhu_skill_list[cell] = item_cell
	end

	local skill_id = tonumber(self.zhu_skill[cell_index + 1])
	item_cell:SetData({skill_id = skill_id, is_tianshen_select_view = true,
			is_open_skill = self.select_data ~= nil and TianShenWGData.Instance:IsActivation(self.select_data.index),
		show_bottom = 0,show_next_desc = 0})

	if self.select_skill == skill_id then
		self.select_skill = 0
		item_cell:OnClick()
	end
end

function BattleItem2:OnClick()
	if self.is_first == false then return end
	TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type3, self.data.info.index, self.data.index)
	ViewManager.Instance:Close(GuideModuleName.TianShenSelectView)
	self.is_first = false
end

function BattleItem2:OnFlush()
	if not self.data then return end
	self.is_first = true
	self.zhu_skill = TianShenWGData.Instance:GetTianShenZhuSkill(self.data.info.index)
	local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(self.data.info.index)
	local tianshen_item = TianShenWGData.Instance:GetTianshenItemCfgByIndex(self.data.info.index)
	local tianshen_color = ItemWGData.Instance:GetItemConfig(tianshen_item.act_item_id)
	self.node_list["level_Text"].text.text = "Lv." .. tianshen_info.level
	self.node_list["lbl_name"].text.text = ToColorStr(self.data.info.bianshen_name, ITEM_COLOR[tianshen_color.color])
	self.node_list["cap_value"].text.text = TianShenWGData.Instance:GetActivationZhanLi(self.data.info.index, true)
	if self.zhu_skill_list_view then
		self.zhu_skill_list_view.scroller:ReloadData(0)
	end
	self:SetItemIcon()
	local bundle, asset = ResPath.GetCommonImages(TianShenWGData.TianShenQualityImg[self.data.info.series])
	self.node_list.img_quality.image:LoadSprite(bundle,asset)-- (asset .. ".png"))
end

function BattleItem2:SetItemIcon()
	local data = self:GetData()
	local tianshen_item_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(data.info.index)
	local item_cfg = ItemWGData.Instance:GetItemConfig(tianshen_item_cfg.act_item_id)
	self.node_list.item_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
end
