YunBiaoIconView = YunBiaoIconView or BaseClass(SafeBaseView)

function YunBiaoIconView:__init()
	--self.default_index = YunBiaoType.Icon
	self:AddViewResource(0, "uis/view/yunbiao_ui_prefab", "layout_icon")
end

function YunBiaoIconView:__delete()
end

function YunBiaoIconView:ReleaseCallBack()
	if CountDown.Instance:HasCountDown(self.countdown_key) then
		CountDown.Instance:RemoveCountDown(self.countdown_key)
	end
end

function YunBiaoIconView:CloseCallBack()
	if CountDown.Instance:HasCountDown(self.countdown_key) then
		CountDown.Instance:RemoveCountDown(self.countdown_key)
	end
end

function YunBiaoIconView:LoadCallBack()
	for i = 0, 2 do
		XUI.AddClickEventListener(self.node_list["img_skill_" .. i], BindTool.Bind2(self.OnIconBtnHandler, self, i))
	end
end

function YunBiaoIconView:FlushView()
	self:Flush()
end

function YunBiaoIconView:OnFlush()
	local is_gray = YunbiaoWGData.Instance:GetHasProtectSkill() == 1 and true or false
	XUI.SetButtonEnabled(self.node_list.img_skill_2, not is_gray)
	XUI.SetGraphicGrey(self.node_list.img_skill_2, is_gray)

	self:FlushHusongTaskTimer()
end

function YunBiaoIconView:OnIconBtnHandler(param)
	if param == 0 then
		YunbiaoWGData.Instance:MoveToHuShongNpc()
	elseif param == 1 then
		YunbiaoWGCtrl.Instance:QiuJiuHandler()
	elseif param == 2 then
		YunbiaoWGCtrl.Instance:SendHusongUseProtectSkill()
	end
end

function YunBiaoIconView:FlushHusongTaskTimer()
	local task_info = TaskWGData.Instance:GetTaskInfo(24001)
	if nil ~= task_info then
		local function update_fun(elapse_time, total_time)
			local time_str = TimeUtil.FormatSecond2MS(math.ceil(total_time - elapse_time))
			self.node_list.lbl_icon_time.text.text = time_str
		end

		if self.countdown_key~=nil and CountDown.Instance:HasCountDown(self.countdown_key) then
			CountDown.Instance:RemoveCountDown(self.countdown_key)
		end
		local remain_time = COMMON_CONSTS.HusongTaskTotalTime - (TimeWGCtrl.Instance:GetServerTime() - task_info.accept_time)
		self.countdown_key = CountDown.Instance:AddCountDown(remain_time,  0.5, update_fun)
	end
end