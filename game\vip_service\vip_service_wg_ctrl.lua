require("game/vip_service/vip_service_wg_data")
require("game/vip_service/vip_service_view")

VipServiceWindowWGCtrl = VipServiceWindowWGCtrl or BaseClass(BaseWGCtrl)
function VipServiceWindowWGCtrl:__init()
    if VipServiceWindowWGCtrl.Instance ~= nil then
		print_error("[VipServiceWindowWGCtrl] attempt to create singleton twice!")
		return
	end

	VipServiceWindowWGCtrl.Instance = self
    self.data = VipServiceWindowWGData.New()
    self.view = VipServiceWindowView.New(GuideModuleName.VipServiceWindowView)
    self:RegisterAllProtocals()

    self.get_vip_service_req_list = {}					-- 下载请求
end

function VipServiceWindowWGCtrl:__delete()
    VipServiceWindowWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if  self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    self.get_vip_service_req_list = {}
    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end
end

function VipServiceWindowWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCPopSeviceInfo, "OnSCPopSeviceInfo")

    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function VipServiceWindowWGCtrl:OnSCPopSeviceInfo(protocol)
	self.data:SetAllInfo(protocol)
	if self.view:IsOpen() then
        self.view:Flush()
    end

	MainuiWGCtrl.Instance:FlushView(0, "vip_service")
end

function VipServiceWindowWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
    if attr_name == "level" then
        local is_open = self.data:GetServiceIsOpen()
        if not is_open then
            return
        end

        local cfg = self.data:GetOpenViewLevelCfg()
        local level_cfg = string.split(cfg, "|")
        for key, level in pairs(level_cfg) do
            if tonumber(level) == value then
                self:OpenVipServiceView(true)
                return
            end
        end
	end
end

function VipServiceWindowWGCtrl:OpenVipServiceView(is_auto_close)
    self.view:SetIsAutoClose(is_auto_close)
    if not self.view:IsOpen() then
        self.view:Open()
    end
end

function VipServiceWindowWGCtrl.GetFilePath()
	local path = "%s/cache/vipservice/%s"
    local str = "vip_service_img.jpg"
	return string.format(path,
		UnityEngine.Application.persistentDataPath,
		str)
end

function VipServiceWindowWGCtrl:SetVipService(raw_image_obj)
    -- 下载失败回调
    local load_failing_callback = function ()
        if raw_image_obj and not IsNil(raw_image_obj.gameObject) then
            self:CancelSetVipService(raw_image_obj)
            raw_image_obj.gameObject:SetActive(false)
        end
    end

    local callback = function (path)
        if nil == self.get_vip_service_req_list[raw_image_obj] then
            return
        end

        if nil == raw_image_obj or IsNil(raw_image_obj.gameObject) then
            return
        end

        local vip_service_path = path or VipServiceWindowWGCtrl.GetFilePath()
        raw_image_obj.raw_image:LoadURLSprite(vip_service_path, function()
            if nil == self.get_vip_service_req_list[raw_image_obj] then
                return
            end
            
            if raw_image_obj and not IsNil(raw_image_obj.gameObject) then
                raw_image_obj.gameObject:SetActive(true)
            end
            self:CancelSetVipService(raw_image_obj)
        end)
    end

    self.get_vip_service_req_list[raw_image_obj] = callback
    self:GetVipService(self.get_vip_service_req_list[raw_image_obj], load_failing_callback)
end

function VipServiceWindowWGCtrl:CancelSetVipService(raw_image_obj)
	if nil ~= self.get_vip_service_req_list[raw_image_obj] then
		self.get_vip_service_req_list[raw_image_obj] = nil
	end
end

-- 获取图片
-- callback(path, is_plist)
function VipServiceWindowWGCtrl:GetVipService(callback, load_failing_callback)
	local url = (((GLOBAL_CONFIG.param_list.resources or {}).vip_service or {})[1] or {}).download
    if not url then
        return
    end

    local path = VipServiceWindowWGCtrl.GetFilePath()
	local function load_callback(url2, path2, is_succ)
		if is_succ then
			callback(path)
		else
            if load_failing_callback then
                load_failing_callback()
            end
		end
	end

	-- 通过http下载
	if not HttpClient:Download(url, path, load_callback) then
		return nil
	end
end

function VipServiceWindowWGCtrl:OpenWebView()
    local url = GLOBAL_CONFIG.param_list.vip_service_url or ""
	if url then
		--WebView.Open(url)
        -- UnityEngine.Application.OpenURL(url)
        if UnityEngine.Application.platform == UnityEngine.RuntimePlatform.IPhonePlayer then
			WebView.Open(url)
		else
			UnityEngine.Application.OpenURL(url)
		end
	end
end
