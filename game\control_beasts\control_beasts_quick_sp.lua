-- 灵兽快捷升品
ControlBeastsQuickSp = ControlBeastsQuickSp or BaseClass(SafeBaseView)

function ControlBeastsQuickSp:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(706, 486)})
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_quick_compose")
    self:SetMaskBg(true)
end

function ControlBeastsQuickSp:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    if nil ~= self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    self.is_click_sure = false
    self.curr_list = nil
    self.select_all = true
end

function ControlBeastsQuickSp:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.ComposeTips7
    self.node_list.tip_text.text.text = Language.ContralBeasts.ComposeTips8
    self.node_list.lbl_tips.text.text = Language.ContralBeasts.ComposeTips9

    self.item_list = AsyncListView.New(BeastShengPinRender, self.node_list.ph_item_list)
    -- self.item_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectShengPinCB, self))
    XUI.AddClickEventListener(self.node_list["sure_btn"], BindTool.Bind(self.OnClickSure, self))
    XUI.AddClickEventListener(self.node_list["all_mark"], BindTool.Bind(self.OnClickAllSelect, self))
end

function ControlBeastsQuickSp:OnSelectShengPinCB(beasts_item)
    if not beasts_item.data then
        return
    end

    self:SetSelectStatusByIndex(beasts_item.index, beasts_item.data.select)
    self:FlushList()
end

function ControlBeastsQuickSp:OnClickAllSelect()
    local select_all = not self.select_all
    self:SetSelectStatus(select_all)
    self:FlushList()
end

function ControlBeastsQuickSp:OnFlush()
    self.curr_list = ControlBeastsWGData.Instance:GetAllBeastComposeList()
    self:FlushList()
end

function ControlBeastsQuickSp:FlushList()
    if not self.curr_list then
        return
    end
    
    self.item_list:SetDataList(self.curr_list)
    self.node_list.img_no_record:SetActive(#self.curr_list == 0)
    self.node_list.all_mark:SetActive(#self.curr_list > 0)

    self.select_all = self:GetIsSelectFull()
    self.node_list.gou:SetActive(self.select_all)
end

function ControlBeastsQuickSp:SetSelectStatus(is_select)
    if not self.curr_list then
        return
    end
    
    for index, data in ipairs(self.curr_list) do
        data.select = is_select
    end
end

function ControlBeastsQuickSp:SetSelectStatusByIndex(select_index, select)
    if not self.curr_list then
        return
    end
    
    for index, data in ipairs(self.curr_list) do
        if select_index == index then
            data.select = select
        end
    end
end

function ControlBeastsQuickSp:GetIsSelectFull()
    if not self.curr_list then
        return true
    end
    
    for _, data in ipairs(self.curr_list) do
        if data and (not data.select) then
            return false
        end
    end

    return true
end


function ControlBeastsQuickSp:OnClickSure()
    if not self.curr_list then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ComposeTips9)
        return
    end

    local batch_compose_list = nil
    for _, compose_data in ipairs(self.curr_list) do
        if compose_data and compose_data.select then
            if batch_compose_list == nil then
                batch_compose_list = {}
            end

            local temp_data = {
                is_egg = compose_data.is_egg,
                bag_id = compose_data.bag_id
            }
            local starup_beast_ids = ControlBeastsWGData.Instance:GetBeastStarupCfgById(compose_data.beast_id)
            local server_data = ControlBeastsWGData.Instance:CreateComposeData(temp_data, compose_data.beast_compose_list, starup_beast_ids)
            table.insert(batch_compose_list, server_data)
        end
    end

    if batch_compose_list == nil or IsEmptyTable(batch_compose_list) then
        return
    end

    ControlBeastsWGCtrl.Instance:SendCSRoleBatchBeastCompose(batch_compose_list)
end

function ControlBeastsQuickSp:GetCurrFlairScore(data)
    local flair_score = 0

    if (not data.is_egg) and data.bag_id then
        local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(data.bag_id)

		if beast_data and beast_data.server_data and beast_data.is_have_beast then
            local server_data = beast_data.server_data

            if server_data.flair_values then
                for _, flair_value in ipairs(server_data.flair_values) do
                    flair_score = flair_score + flair_value
                end
            end
            
            flair_score = flair_score * (server_data.effort_value / 10000)
        end
    end
    
    return flair_score
end

-------------------------------------------------------------------------------------------
BeastShengPinRender = BeastShengPinRender or BaseClass(BaseRender)

function BeastShengPinRender:__delete()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
    end

    if self.target_item then
        self.target_item:DeleteMe()
        self.target_item = nil
    end

    if self.must_item then
        self.must_item:DeleteMe()
        self.must_item = nil
    end
end

function BeastShengPinRender:LoadCallBack()
	self.cell_list = {}
	for i = 1, 4 do
		self.cell_list[i] = ItemCell.New(self.node_list["content"])
	end
    self.target_item = ItemCell.New(self.node_list["target_item"])
    self.must_item = ItemCell.New(self.node_list["must_item"])

    XUI.AddClickEventListener(self.node_list["check_mark"], BindTool.Bind(self.ClickMark, self))
end

function BeastShengPinRender:ClickMark()
    self.data.select = not self.data.select
    self.node_list.gou:SetActive(self.data.select)

    if ControlBeastsWGCtrl.Instance.control_beasts_quick_sp then
        ControlBeastsWGCtrl.Instance.control_beasts_quick_sp:OnSelectShengPinCB(self)
    end
end

function BeastShengPinRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.gou:SetActive(self.data.select)
    local must = {}
    must.item_id = self.data.beast_id
    must.bag_id = self.data.bag_id
    must.is_egg = self.data.is_egg
    must.is_beast = not self.data.is_egg
    self.must_item:SetData(must)

    local target_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.beast_id)
    local starup_beast_ids = ControlBeastsWGData.Instance:GetBeastStarupCfgById(self.data.beast_id)

    if target_cfg and target_cfg.starup_beast_id then
        local starup_beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(target_cfg.starup_beast_id)
        if starup_beast_cfg then
            local target = {}
            target.item_id = starup_beast_cfg.beast_id
            self.target_item:SetData(target)
        end
    end

    if self.data.beast_compose_list then
        local com_list = self.data.beast_compose_list
        local stuff_table = {}
        if com_list.special_list then
            for _, special_beast in ipairs(com_list.special_list) do
                local stuff_data = {}
                stuff_data.item_id = special_beast.beast_id
                stuff_data.bag_id = special_beast.bag_id
                stuff_data.is_egg = special_beast.is_egg
                table.insert(stuff_table, stuff_data)
            end
        end


        if com_list and com_list.same_list then
            for _, same_data in pairs(com_list.same_list) do
                for element, star_data in pairs(same_data) do
                    for satr, star_list in pairs(star_data) do
                        for _, beasts_data in pairs(star_list) do
                            local stuff_data = {}
                            stuff_data.item_id = beasts_data.beast_id
                            stuff_data.bag_id = beasts_data.bag_id
                            stuff_data.is_egg = beasts_data.is_egg
                            table.insert(stuff_table, stuff_data)
                        end
                    end
                end
            end
        end
    
        -- 加入碎片
        if starup_beast_ids then
            for i, starup_beast_data in ipairs(starup_beast_ids) do
                if starup_beast_data.chip_id ~= nil then
                    local stuff_data = {}
                    stuff_data.item_id = starup_beast_data.chip_id
                    stuff_data.num =starup_beast_data.num
                    table.insert(stuff_table, stuff_data)
                end
            end
        end
    

        for i = 1, 4 do
            if stuff_table[i] then
                self.cell_list[i]:SetActive(true) 
                self.cell_list[i]:SetData(stuff_table[i])
            else
                self.cell_list[i]:SetActive(false) 
            end
        end
    end
end

function BeastShengPinRender:SetBeastFlairScoreIcon(item_cell, data)
    if (not data.is_egg) and data.bag_id then
        local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(data.bag_id)

		if beast_data and beast_data.server_data and beast_data.is_have_beast then
            local server_data = beast_data.server_data
            local flair_score, score_index = ControlBeastsWGData.Instance:GetFlairScoreByServerData(server_data)
            item_cell:SetBeastFlairScoreIcon(score_index)
        end
    end
end