﻿using DG.Tweening;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;

public static class ShadowExtensions
{
    public static Tweener DoEffectDistance(
this Shadow shadow, Vector2 from, Vector2 to, float duration, Action complete)
    {
        Tweener t = DOTween.To(() => from, x => from = x, to, duration);
        t.OnUpdate(() =>
        {
            if (null != shadow)
            {
                shadow.effectDistance = from;
            }
        });
        t.OnComplete(() =>
        {
            if (null != complete)
            {
                complete();
            }
        });
        return t;
    }

    public static Tweener DoEffectColor(
this Shadow shadow, Color from, Color to, float duration, Action complete)
    {
        Tweener t = DOTween.To(() => from, x => from = x, to, duration);
        t.OnUpdate(() =>
        {
            if (null != shadow)
            {
                shadow.effectColor = from;
            }
        });
        t.OnComplete(() =>
        {
            if (null != complete)
            {
                complete();
            }
        });
        return t;
    } 
}
