﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_TexturePoolWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(Nirvana.TexturePool), typeof(Nirvana.Singleton<Nirvana.TexturePool>));
		<PERSON><PERSON>Function("SetMaxLoadingCount", SetMaxLoadingCount);
		<PERSON><PERSON>unction("Free", Free);
		<PERSON><PERSON>unction("ClearAllUnused", ClearAllUnused);
		L<PERSON>RegFunction("Clear", Clear);
		<PERSON><PERSON>RegFunction("Load", Load);
		L.RegFunction("New", _CreateNirvana_TexturePool);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("DefaultReleaseAfterFree", get_DefaultReleaseAfterFree, set_DefaultReleaseAfterFree);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_TexturePool(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.TexturePool obj = new Nirvana.TexturePool();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.TexturePool.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMaxLoadingCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.TexturePool obj = (Nirvana.TexturePool)ToLua.CheckObject(L, 1, typeof(Nirvana.TexturePool));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetMaxLoadingCount(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Free(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				Nirvana.TexturePool obj = (Nirvana.TexturePool)ToLua.CheckObject(L, 1, typeof(Nirvana.TexturePool));
				UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
				obj.Free(arg0);
				return 0;
			}
			else if (count == 3)
			{
				Nirvana.TexturePool obj = (Nirvana.TexturePool)ToLua.CheckObject(L, 1, typeof(Nirvana.TexturePool));
				UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				obj.Free(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.TexturePool.Free");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearAllUnused(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.TexturePool obj = (Nirvana.TexturePool)ToLua.CheckObject(L, 1, typeof(Nirvana.TexturePool));
			obj.ClearAllUnused();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Clear(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.TexturePool obj = (Nirvana.TexturePool)ToLua.CheckObject(L, 1, typeof(Nirvana.TexturePool));
			obj.Clear();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Load(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				Nirvana.TexturePool obj = (Nirvana.TexturePool)ToLua.CheckObject(L, 1, typeof(Nirvana.TexturePool));
				Nirvana.AssetID arg0 = StackTraits<Nirvana.AssetID>.Check(L, 2);
				System.Action<UnityEngine.Texture> arg1 = (System.Action<UnityEngine.Texture>)ToLua.CheckDelegate<System.Action<UnityEngine.Texture>>(L, 3);
				obj.Load(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				Nirvana.TexturePool obj = (Nirvana.TexturePool)ToLua.CheckObject(L, 1, typeof(Nirvana.TexturePool));
				Nirvana.AssetID arg0 = StackTraits<Nirvana.AssetID>.Check(L, 2);
				System.Action<UnityEngine.Texture> arg1 = (System.Action<UnityEngine.Texture>)ToLua.CheckDelegate<System.Action<UnityEngine.Texture>>(L, 3);
				bool arg2 = LuaDLL.luaL_checkboolean(L, 4);
				obj.Load(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.TexturePool.Load");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DefaultReleaseAfterFree(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.TexturePool obj = (Nirvana.TexturePool)o;
			float ret = obj.DefaultReleaseAfterFree;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DefaultReleaseAfterFree on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DefaultReleaseAfterFree(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.TexturePool obj = (Nirvana.TexturePool)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.DefaultReleaseAfterFree = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DefaultReleaseAfterFree on a nil value");
		}
	}
}

