WorldTreasureLimitBuyTipView = WorldTreasureLimitBuyTipView or BaseClass(SafeBaseView)

function WorldTreasureLimitBuyTipView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(700, 446)})
	self:AddViewResource(0, "uis/view/world_treasure_ui_prefab", "layout_world_treasure_limit_buy_tip")

    self.data = {}
end

function WorldTreasureLimitBuyTipView:LoadCallBack()
    self.ph_item = ItemCell.New(self.node_list["ph_item"])

    XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_OK, BindTool.Bind(self.OnClickBtnOk, self))
end

function WorldTreasureLimitBuyTipView:ReleaseCallBack()
	if self.ph_item then
        self.ph_item:DeleteMe()
        self.ph_item = nil
    end
end

function WorldTreasureLimitBuyTipView:SetData(data)
	self.data = data
end

function WorldTreasureLimitBuyTipView:ShowIndexCallBack()
end

function WorldTreasureLimitBuyTipView:OnFlush()
    local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
    local item_id = self.data.reward_item[0].item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if item_cfg then
        self.node_list.desc1.text.text = string.format(Language.WorldTreasure.LimitBuyTxt7, ITEM_COLOR[item_cfg.color], item_cfg.name)
    end

    self.node_list.desc2.text.text = Language.WorldTreasure.LimitBuyTxt8[cur_grade]
    self.ph_item:SetData({item_id = item_id})
end

function WorldTreasureLimitBuyTipView:OnClickBtnOk()
    local discount_time = WorldTreasureWGData.Instance:GetLimitBuyDiscountTime()
    local rmb_type = discount_time > 0 and self.data.discount_rmb_type or self.data.rmb_type
    local price = discount_time > 0 and self.data.discount_price or self.data.price
    RechargeWGCtrl.Instance:Recharge(price, rmb_type, self.data.rmb_seq)
    self:Close()
end