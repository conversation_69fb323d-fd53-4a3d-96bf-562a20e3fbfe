HotSpringProgressView = HotSpringProgressView or BaseClass(SafeBaseView)
local pro_val = 0.9
local HOT_SPRING_ANMO_CD_TIME_S = 5
function HotSpringProgressView:__init()
    self:AddViewResource(0, "uis/view/hot_spring_ui_prefab", "layout_pass_progess")
end

function HotSpringProgressView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("hotspring_pass_progress") then
        CountDownManager.Instance:RemoveCountDown("hotspring_pass_progress")
    end
    GlobalTimerQuest:CancelQuest(self.quest)
end

function HotSpringProgressView:ShowIndexCallBack()
    --因为下发开始按摩的协议跟按摩剩余时间协议不是同一个
    --所以这里要用定时器去检测时间
    self.node_list["Progress"].slider.value = 0
    self.node_list["txt_pro"].text.text = string.format(Language.HotSpring.AnmoProgress, 0)
    if not self.quest then
        self.quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateCallBack, self), 0.1)
    end
end

function HotSpringProgressView:UpdateCallBack(elapse_time, total_time)
    local _, _, time = HotSpringWGData.Instance:GetHotspringPlayerInfo()
    if time <= 0 then
        return
    end
    CountDownManager.Instance:AddCountDown("hotspring_pass_progress", BindTool.Bind(self.UpdateView, self),
        BindTool.Bind(self.CompleteView, self), time, nil, 0.05)
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
    GlobalTimerQuest:CancelQuest(self.quest)
    self.quest = nil
end

function HotSpringProgressView:UpdateView(elapse_time, total_time)
    local num = string.format("%.2f", elapse_time/total_time)
    local amount1 = num / pro_val
    self.node_list["Progress"].slider.value = amount1
    -- if amount1 > pro_val then
    --     self.node_list["icon_result_pro"].image.fillAmount = (num - pro_val) / (1 - pro_val)
    -- end
    self.node_list["txt_pro"].text.text = string.format(Language.HotSpring.AnmoProgress, num*100)
end

function HotSpringProgressView:CompleteView()
    self:Close()
end
