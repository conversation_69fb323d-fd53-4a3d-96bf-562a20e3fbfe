function NewAppearanceWGView:AdvancedInitView()
    if self.is_load_advanced then
        return
    end

    if not self.ad_attr_list then
        self.ad_attr_list = {}
        local parent_node = self.node_list["ad_attr_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = QiChongAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            self.ad_attr_list[i] = cell
        end
    end

    self.ad_skill_item_list = {}
    for i = 1, 5 do
        self.ad_skill_item_list[i] = QiChongSkillRender.New(self.node_list["ad_skill_" .. i])
        self.ad_skill_item_list[i]:SetPosflag(true)
        self.ad_skill_item_list[i]:SetIndex(i)
    end

    self.ad_stuff_item_list = {}
    for i = 1, 3 do
        self.ad_stuff_item_list[i] = ItemCell.New(self.node_list["ad_stuff_item" .. i])
    end

    if not self.ad_sxd_list then
        self.ad_sxd_list = {}
        local parent_node = self.node_list["ad_sxd_list"]
        local count = parent_node.transform.childCount
        for i = 1, count do
            local cell = NewAppearanceSXDRender.New(parent_node:FindObj("sxd_" .. i))
            cell:SetIndex(i)
            self.ad_sxd_list[i] = cell
        end
    end


    XUI.AddClickEventListener(self.node_list["ad_btn_use"], BindTool.Bind(self.OnClickADUse, self))
    XUI.AddClickEventListener(self.node_list["ad_btn_uplevel"], BindTool.Bind(self.OnClickADUpLevel, self))
    XUI.AddClickEventListener(self.node_list["ad_hide_btn"], BindTool.Bind(self.OnClickADHide, self))
    XUI.AddClickEventListener(self.node_list["ad_btn_lingzhi"], BindTool.Bind(self.OnClickLingZhi, self))
    XUI.AddClickEventListener(self.node_list["ad_sxd_extend"], BindTool.Bind(self.OnClickSXDExtend, self))
    XUI.AddClickEventListener(self.node_list["king_vip_btn"], BindTool.Bind(self.OnClickKingVipBtn, self))
    XUI.AddClickEventListener(self.node_list["ad_btn_reset"], BindTool.Bind(self.OnClickADResetBtn, self))
    self.is_load_advanced = true
end

function NewAppearanceWGView:AdvancedReleaseCallBack()
    self:ClearAdvancedSliderTween()

    if self.ad_attr_list then
        for k,v in pairs(self.ad_attr_list) do
            v:DeleteMe()
        end
        self.ad_attr_list = nil
    end

    if self.ad_skill_item_list then
        for k,v in pairs(self.ad_skill_item_list) do
            v:DeleteMe()
        end
        self.ad_skill_item_list = nil
    end

    if self.ad_stuff_item_list then
        for k,v in pairs(self.ad_stuff_item_list) do
            v:DeleteMe()
        end
        self.ad_stuff_item_list = nil
    end

    if self.ad_sxd_list then
        for k,v in pairs(self.ad_sxd_list) do
            v:DeleteMe()
        end
        self.ad_sxd_list = nil
    end

    self.is_load_advanced = nil
end

function NewAppearanceWGView:AdvancedShowIndexCallBack()
    self:ClearAdvancedSliderTween()
    self:StopAdvancedAutoUpLevel()
    local tab_data = NEW_APPEARANCE_TAB_DATA[self.show_index]
    if not tab_data then
        return
    end

    self.select_advanced_type = tab_data.ad_type
    self:ShowAdvancedModel()

    -- 注灵部分
    local show_lingzhi_byn = false
    local fun_name = LingZhiSkillWGData.FunName[tab_data.lingzhi_type]
    local fun_is_open = FunOpen.Instance:GetFunIsOpened(fun_name)
    local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(tab_data.lingzhi_type)
    if not IsEmptyTable(server_info) then
        show_lingzhi_byn = fun_is_open or server_info.is_buy == 1
    end
    self.node_list["ad_btn_lingzhi"]:CustomSetActive(show_lingzhi_byn)
    self:FlushAdvancedLingZhiRemind()
end

function NewAppearanceWGView:AdvancedFlushView()
    local info = NewAppearanceWGData.Instance:GetAdvancedInfo(self.select_advanced_type)
    if not info then
        return
    end

    local image_cfg = NewAppearanceWGData.Instance:GetAdvancedImageCfgByType(self.select_advanced_type)
    if not image_cfg then
        return
    end

    local level = info.level
    local use_image_id = info.use_image_id

    local next_uplevel_cfg = NewAppearanceWGData.Instance:GetAdvancedUplevelCfg(self.select_advanced_type, level + 1)
    local is_act = level >= image_cfg.active_level
    local is_max = next_uplevel_cfg == nil

    -- 技能
    local skill_list = NewAppearanceWGData.Instance:GetAdvancedSkillList(self.select_advanced_type)
    for k,v in pairs(skill_list) do
        if self.ad_skill_item_list[k] then
            self.ad_skill_item_list[k]:SetData(v)
        end
    end

    -- 属性丹
    local sxd_list = NewAppearanceWGData.Instance:GetAdvancedSXDlist(self.select_advanced_type)
    for k,v in pairs(self.ad_sxd_list) do
        v:SetData(sxd_list[k])
    end

    local fun_name = NewAppearanceWGData.Instance:GetAdvancedFunnameBuType(self.select_advanced_type)
    if fun_name then
        local is_open = FunOpen.Instance:GetFunIsOpened(fun_name)

        if is_open then
            -- 属性宝石红点
            local is_remind = NewAppearanceWGData.Instance:GetAdvancedSXDExtendRemind(self.select_advanced_type)
            self.node_list["ad_sxd_extend_remind"]:CustomSetActive(is_remind)
        end

        self.node_list["ad_sxd_extend"]:CustomSetActive(is_open)
    end

    -- 消耗
    local stuff_list = NewAppearanceWGData.Instance:GetAdvancedUplevelStuffList(self.select_advanced_type)
    for k,v in pairs(self.ad_stuff_item_list) do
        local data = stuff_list[k]
        if data then
            self.node_list["ad_stuff_item" .. k]:CustomSetActive(true)
            local item_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
            v:SetFlushCallBack(function ()
                v:SetRightBottomColorText(item_num)
                v:SetRightBottomTextVisible(true)
            end)

            v:SetData({item_id = data.item_id})
        else
            self.node_list["ad_stuff_item" .. k]:CustomSetActive(false)
        end
    end

    local is_remind = NewAppearanceWGData.Instance:GetAdvancedUpLevelRemind(self.select_advanced_type)
    self.node_list["ad_btn_uplevel_remind"]:CustomSetActive(is_remind)

    -- 按钮
    self.node_list["ad_max_level_flag"]:CustomSetActive(is_max)
    self.node_list["ad_btn_uplevel"]:CustomSetActive(not is_max)

    -- 幻化
    self.node_list["ad_no_act_flag"]:CustomSetActive(not is_act)
    local cfg_appe_id = image_cfg.appe_image_id
    self.node_list["ad_btn_use"]:CustomSetActive(use_image_id ~= cfg_appe_id and is_act)
    self.node_list["ad_btn_reset"]:CustomSetActive(use_image_id == cfg_appe_id and is_act)

    -- 隐藏
    local is_hide = NewAppearanceWGData.Instance:GetAppearanceIsHide(self.select_advanced_type)
    self.node_list["ad_hide_btn"]:CustomSetActive(is_act)
    -- self.node_list["ad_hide_btn_hl"]:CustomSetActive(is_hide)
    self.node_list.ad_hide_btn_text.text.text = is_hide and Language.NewAppearance.AdHideBtnStr or Language.NewAppearance.AdShowBtnStr

    -- 进度
    local cur_uplevel_cfg = NewAppearanceWGData.Instance:GetAdvancedUplevelCfg(self.select_advanced_type, level)
    if not cur_uplevel_cfg then
        return
    end

    local uplevel_exp_val = info.uplevel_exp_val
    local need_exp_val = cur_uplevel_cfg.need_exp
    if is_max then
        if self.is_advanced_auto_uplevel then
            self:PlayAdvancedSliderTween(0, uplevel_exp_val, need_exp_val)
        else
            self.node_list["ad_progress"].slider.value = 1
        end

        self.node_list["ad_progress_text"].text.text = "-/-"
    else
        if self.is_advanced_auto_uplevel then
            local add_level = level - self.advanced_old_level
            self:PlayAdvancedSliderTween(add_level, uplevel_exp_val, need_exp_val)
        else
            self.node_list["ad_progress_text"].text.text = uplevel_exp_val .. "/" .. need_exp_val
            self.node_list["ad_progress"].slider.value = uplevel_exp_val / need_exp_val
            self.node_list["ad_btn_uplevel_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[5]
        end
    end

    if not self.is_advanced_auto_uplevel then
        self:UGAdvacnedLevelChangeFlush(level, false)
    end
    
    self:SetKingVipBtnInfo(1)
end

function NewAppearanceWGView:UGAdvacnedLevelChangeFlush(level, need_check_show_effect)
    self.node_list["ad_level_text"].text.text = string.format(Language.NewAppearance.LevelText, level)
    local attr_list, capability = NewAppearanceWGData.Instance:GetAdvancedAttrListAndCap(self.select_advanced_type, level)
    local need_show_up_effect = false

    if need_check_show_effect then
        if nil ~= self.select_advanced_type_cache and nil ~= self.select_advanced_type_level_cache then
            if self.select_advanced_type_cache == self.select_advanced_type and (level - self.select_advanced_type_level_cache == 1) then
                need_show_up_effect = true
            end
        end
    end

    for k,v in pairs(self.ad_attr_list) do
        v:SetData(attr_list[k])

        if need_show_up_effect then
            v:PlayAttrValueUpEffect()
        end
    end
    self.node_list["ad_cap_value"].text.text = capability

    self.select_advanced_type_cache = self.select_advanced_type
    self.select_advanced_type_level_cache = level
end

-- 一键升级
function NewAppearanceWGView:OnClickADUpLevel()
    if not self.is_advanced_auto_uplevel then
        self:StartAdvancedAutoUpLevel()
    else
        self:StopAdvancedAutoUpLevel()
    end
end

-- 升级
function NewAppearanceWGView:StartAdvancedAutoUpLevel(no_tips)
    -- if self.is_advanced_auto_uplevel then
    --     return
    -- end

    local level = NewAppearanceWGData.Instance:GetAdvancedLevel(self.select_advanced_type)
    local next_uplevel_cfg = NewAppearanceWGData.Instance:GetAdvancedUplevelCfg(self.select_advanced_type, level + 1)
    if not next_uplevel_cfg then
        self:StopAdvancedAutoUpLevel()
        return
    end

    -- 升阶材料
    local stuff_list = NewAppearanceWGData.Instance:GetAdvancedUplevelStuffList(self.select_advanced_type)
    local stuff_id, had_stuff = 0, false
    for i, v in ipairs(stuff_list) do
        if stuff_id == 0 then
            stuff_id = v.item_id
        end
        
		local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if item_num > 0 then
            stuff_id = v.item_id
			had_stuff = true
            break
		end
	end

    if not had_stuff then
        if not no_tips then
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_id})
        end

        self:StopAdvancedAutoUpLevel()
        return
    end

    self.advanced_old_level = level
    self.is_advanced_auto_uplevel = true
    self.node_list["ad_btn_uplevel_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[3]

    MainuiWGCtrl.Instance:CreateCacheTable()
    NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.UPLEVEL, self.select_advanced_type)
end

-- 停止
function NewAppearanceWGView:StopAdvancedAutoUpLevel()
    if not self.is_load_advanced then
        return
    end

    self.is_advanced_auto_uplevel = nil
    self.node_list["ad_btn_uplevel_text"].text.text = Language.NewAppearance.UpGradeBtnDesc[5]
end

function NewAppearanceWGView:ClearAdvancedSliderTween()
    if self.advanced_slider_tween then
        self.advanced_slider_tween:Kill()
        self.advanced_slider_tween = nil
    end
end

function NewAppearanceWGView:PlayAdvancedSliderTween(add_level, exp_val, need_exp_val)
    if add_level == 0 and exp_val == need_exp_val then
        self:ClearAdvancedSliderTween()
        local slider = self.node_list["ad_progress"].slider
        local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.1))
        self.advanced_slider_tween = slider:DOValue(1, time)
        self.advanced_slider_tween:OnComplete(function ()
			MainuiWGCtrl.Instance:DelayShowCachePower(0) --延时调用
            self:PlayUpLevelEffect()
		end)

        self:StopAdvancedAutoUpLevel()
        return
    end

    local info = NewAppearanceWGData.Instance:GetAdvancedInfo(self.select_advanced_type)
    if not info then
        return
    end

    local real_level = info.level
    self.advanced_slider_tween_func = function (progress)
        self:ClearAdvancedSliderTween()

        local before_uplevel_cfg = NewAppearanceWGData.Instance:GetAdvancedUplevelCfg(self.select_advanced_type, self.advanced_old_level)
        if not before_uplevel_cfg then
            return
        end
        
        if progress <= 0 then
            if self.is_advanced_auto_uplevel then
                if self.advanced_old_level ~= real_level then
                    self.advanced_old_level = real_level
                    self:UGAdvacnedLevelChangeFlush(real_level, true)
                end

                self:StartAdvancedAutoUpLevel(true)
            end

            return
        end

        local is_up_one_level = false
        local slider = self.node_list["ad_progress"].slider
		if progress > 1 then
			local time = tonumber(string.format("%.2f", (1 - slider.value) * 0.1))
			self.advanced_slider_tween = slider:DOValue(1, time)
            is_up_one_level = true
            self.node_list["ad_progress_text"].text.text = before_uplevel_cfg.need_exp .. "/" .. before_uplevel_cfg.need_exp
		else
			local time = tonumber(string.format("%.2f", (progress - slider.value) * 0.1))
			self.advanced_slider_tween = slider:DOValue(progress, time)
            self.node_list["ad_progress_text"].text.text = info.uplevel_exp_val .. "/" .. before_uplevel_cfg.need_exp
		end

        progress = progress - 1
        self.advanced_slider_tween:OnComplete(function ()
			if progress >= 0 then
				slider.value = 0
                if is_up_one_level then
                    self.advanced_old_level = self.advanced_old_level + 1
                    self:UGAdvacnedLevelChangeFlush(self.advanced_old_level, true)
                    self:PlayUpLevelEffect()
                end
			end
			
			if progress < 1 then
				MainuiWGCtrl.Instance:DelayShowCachePower(0)
			end

			self.advanced_slider_tween_func(progress)
		end)
    end

    local total_progress = add_level + exp_val / need_exp_val
    self.advanced_slider_tween_func(total_progress)
end

-- 模型
function NewAppearanceWGView:ShowAdvancedModel()
    local tab_data = NEW_APPEARANCE_TAB_DATA[self.show_index]
    if not tab_data then
        return
    end

    local image_cfg = NewAppearanceWGData.Instance:GetAdvancedImageCfgByType(self.select_advanced_type)
    if image_cfg then
        self.node_list["ad_select_name"].text.text = image_cfg.weapon_name
        self:FlushFashionModel(tab_data.fashion_type, image_cfg.appe_image_id)
    end
end

-- 幻化
function NewAppearanceWGView:OnClickADUse()
	local image_cfg = NewAppearanceWGData.Instance:GetAdvancedImageCfgByType(self.select_advanced_type)
    if not image_cfg then
        return
    end

	NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.USE_IMAGE, self.select_advanced_type, image_cfg.appe_image_id, 0)
end

-- 隐藏
function NewAppearanceWGView:OnClickADHide()
    NewAppearanceWGCtrl.Instance:SendUpgradeReq(UPGRADE_REQ_TYPE.APPEARANCE_HIDE, self.select_advanced_type)
end

-- 注灵 - 红点
function NewAppearanceWGView:FlushAdvancedLingZhiRemind()
    if not self.is_load_advanced then
        return
    end

    local tab_data = NEW_APPEARANCE_TAB_DATA[self.show_index]
    if not tab_data then
        return
    end

    local lz_remind_num = RemindManager.Instance:GetRemind(tab_data.lz_remind_name)
    local lz_act_remind_num = RemindManager.Instance:GetRemind(tab_data.lz_act_remind_name)
    self.node_list["ad_btn_lingzhi_remind"]:CustomSetActive(lz_remind_num >= 1 or lz_act_remind_num >= 1)
end

-- 注灵
function NewAppearanceWGView:OnClickLingZhi()
    local tab_data = NEW_APPEARANCE_TAB_DATA[self.show_index]
    if not tab_data then
        return
    end

	local server_info = LingZhiSkillWGData.Instance:GetSingleServerInfo(tab_data.lingzhi_type)
	if IsEmptyTable(server_info) then
		return
	end

	local is_open = server_info.is_open == 1
	if not is_open then
		LingZhiWGCtrl.Instance:OpenLingZhiZhiGouTip(server_info)
		return
	end

    local ad_type = self.select_advanced_type
	local lz_ctrl = LingZhiWGCtrl.Instance
	if ad_type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING then
		lz_ctrl.lingzhi_wing_view:Open()
	elseif ad_type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO then
		lz_ctrl.lingzhi_fabao_view:Open()
	elseif ad_type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN then
		lz_ctrl.lingzhi_jianzhen_view:Open()
	elseif ad_type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING then
		lz_ctrl.lingzhi_shenbing_view:Open()
	end
end

function NewAppearanceWGView:OnClickSXDExtend()
    NewAppearanceWGCtrl.Instance:OpenAppearanceAttrStoreView(self.select_advanced_type, -1)
end

function NewAppearanceWGView:OnClickADResetBtn()
    SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.ResetErrorTip)
end