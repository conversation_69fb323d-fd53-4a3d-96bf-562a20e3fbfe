--深渊boss宝箱奖励预览面板
ShenyuanBossBoxReward = ShenyuanBossBoxReward or BaseClass(SafeBaseView)
local ATTR_COUNT = 10
function ShenyuanBossBoxReward:__init()
    self.view_name = "ShenyuanBossBoxReward"
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_shenyuan_boss_reward")
	self:SetMaskBg(true)
end

function ShenyuanBossBoxReward:OpenCallBack()

end

function ShenyuanBossBoxReward:LoadCallBack()
    self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.Close, self))
    if not self.reward_grid then
        self.reward_grid = AsyncBaseGrid.New()
		local t = {}
		t.col = 4
		t.change_cells_num = 1
		t.itemRender = ItemCell
		t.list_view = self.node_list["ph_grid"]
		self.reward_grid:CreateCells(t)
        self.reward_grid:SetStartZeroIndex(false)
    end
    self.des_list = {}
    for i = 1, ATTR_COUNT do
		self.des_list[i] = self.node_list.des_list:FindObj("text" .. i)
    end
    self.node_list.title_view_name.text.text = Language.Boss.ShenyuanBox
end

function ShenyuanBossBoxReward:ReleaseCallBack()
    if nil ~= self.reward_grid then
        self.reward_grid:DeleteMe()
        self.reward_grid = nil
    end

    self.des_list = {}
end

function ShenyuanBossBoxReward:SetRewardList(list)
    self.list = list
end

function ShenyuanBossBoxReward:OnFlush()
    if not self.list then
        return
    end
    local list = self.list

    self.reward_grid:SetDataList(list)
    local FirstKillDes = Language.Boss.ShenyuanBoxDes

    local reward_tips_des = Split(FirstKillDes, "\n") 
    local index = #reward_tips_des
    for i = 1, ATTR_COUNT do
        self.des_list[i]:SetActive(i <= index)
        if i <= index then
            self.des_list[i].text.text = reward_tips_des[i]
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.des_list[i].rect)
        end
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.des_list.rect)
   
end



