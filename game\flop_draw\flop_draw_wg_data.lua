FlopDrawWGData = FlopDrawWGData or BaseClass()
FlopDrawWGData.MAX_STEP_REWARD_COUNT = 21 -- 步数奖励最大值

function FlopDrawWGData:__init()
	if FlopDrawWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[FlopDrawWGData] attempt to create singleton twice!")
		return
	end

	FlopDrawWGData.Instance = self

	self:InitCfg()
	self.grade = 1 -- 档次
	self.step_reward_flag = {}
	self.task_list = {}
	self.step_flag = {} --步数状态

	RemindManager.Instance:Register(RemindName.FlopDraw, BindTool.Bind(self.ShowFlopDrawRemind, self))
end

function FlopDrawWGData:__delete()
	FlopDrawWGData.Instance = nil
	self.grade = nil
	self.step_reward_flag = nil
	self.task_list = nil
	self.pro_flag = nil
	self.step_flag = nil

	RemindManager.Instance:UnRegister(RemindName.FlopDraw)
end

function FlopDrawWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_cat_venture2_auto")
	self.reward_cfg = ListToMapList(cfg.reward, "grade")
	self.consume_cfg = ListToMapList(cfg.consume, "grade")
	self.task_cfg = ListToMapList(cfg.task, "grade", "task_type")
    self.item_random_desc = ListToMapList(cfg.item_random_desc, "grade")
	self.other_cfg = cfg.other[1]
end

--全部数据
function FlopDrawWGData:SetAllFlopDrawInfo(protocol)
	self.grade = protocol.grade
	self.step_reward_flag = protocol.step_reward_flag
	self.task_list = protocol.task_list
	self.step_flag = protocol.step_flag
end

-- 单个任务信息改变
function FlopDrawWGData:SetSingleTaskInfo(protocol)
  	local data = protocol.change_data
  	for i, v in pairs(self.task_list ) do
  		if v.task_id == data.task_id then
  			self.task_list[v.task_id] = data
  			return
  		end
  	end
end

function FlopDrawWGData:GetCurStep()  --获取当前步数
	local step = 0
	for k, v in pairs(self.step_flag) do
		if v == 1 then
			step = step + 1
		end
	end

	return step
end

function FlopDrawWGData:GetStepState(step) -- 是否已经抽到
	return self.step_flag[step] and self.step_flag[step] == 1
end

function FlopDrawWGData:GetAllStepState()
	return self.step_flag
end

function FlopDrawWGData:SetInfoFlag()  --设置是否获取过一次协议数据
	self.pro_flag = true
end

function FlopDrawWGData:GetInfoFlag()  --
	return self.pro_flag
end

function FlopDrawWGData:GetStepCostNum(step) -- 根据步数获取消耗数量
	local cur_grade_consume_cfg = self.consume_cfg[self.grade] or {}
	local count = 0
	for i, v in ipairs(cur_grade_consume_cfg) do
		if step == v.step then
			count = v.cost_item_num
		end
	end

	return count
end

function FlopDrawWGData:GetRewardStateByStep(step) -- 获取奖励状态
	return self.step_reward_flag[step] and self.step_reward_flag[step] == 1
end

function FlopDrawWGData:GetAllRewardState()
	return self.step_reward_flag
end

function FlopDrawWGData:GetOtherCfg()
	return self.other_cfg
end

function FlopDrawWGData:GetAllStepRewardCfg()
	return self.reward_cfg[self.grade]
end

function FlopDrawWGData:GetShowRewardCfg()
	local reward_cfg = self:GetAllStepRewardCfg()
	local show_list = {}
	if reward_cfg then
		for k, v in pairs(reward_cfg) do
			if v.is_show == 1 then
				table.insert(show_list, v.item)
			end
		end
	end

	return show_list
end

function FlopDrawWGData:GetAllTaskList()
	local task_data_list = {}
	local cur_grade_task_cfg = self.task_cfg[self.grade]
	if not IsEmptyTable(cur_grade_task_cfg) then
		for k, v in pairs(cur_grade_task_cfg) do
			local cfg = v[#v]
			if #v > 1 then
				for k1, v1 in ipairs(v) do
					if self.task_list[v1.task_id] and self.task_list[v1.task_id].status ~= REWARD_STATE_TYPE.FINISH then
						cfg = v1
						break
					end
				end
			end
	
			local task_type = cfg.task_type
			local task_id = cfg.task_id
			task_data_list[task_type] = {}
			task_data_list[task_type].task_type = cfg.task_type
			task_data_list[task_type].task_id = cfg.task_id
			task_data_list[task_type].item_list = cfg.item_list
			task_data_list[task_type].des = cfg.des
			task_data_list[task_type].target = cfg.target
			task_data_list[task_type].open_panel = cfg.open_panel
			local status = self.task_list[cfg.task_id] and self.task_list[cfg.task_id].status or REWARD_STATE_TYPE.UNDONE
			task_data_list[task_type].status = status
			task_data_list[task_type].progress_num_flag = self.task_list[cfg.task_id] and self.task_list[cfg.task_id].progress_num_flag or 0
	
			local sort_index = 0
			if status == REWARD_STATE_TYPE.UNDONE then
				task_data_list[task_type].sort_index = 100
			elseif status == REWARD_STATE_TYPE.CAN_FETCH then
				task_data_list[task_type].sort_index = 10
			elseif status == REWARD_STATE_TYPE.FINISH then
				task_data_list[task_type].sort_index = 1000
			end
		end
	end

	table.sort(task_data_list, SortTools.KeyLowerSorters("sort_index", "task_id"))
	return task_data_list
end


---红点--
function FlopDrawWGData:ShowFlopDrawRemind()
	if self:ShowStepBtnRemind() then
		return 1
	end

	if self:ShowTaskRemind() then
		return 1
	end

	if self:ShowStepRewardRemind() then
		return 1
	end

	return 0
end

---按钮红点--
function FlopDrawWGData:ShowStepBtnRemind()
	local remind = false
	local cur_step = self:GetCurStep()
	if cur_step < FlopDrawWGData.MAX_STEP_REWARD_COUNT then
		local other_cfg = self:GetOtherCfg()
	    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
	    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
	    local cost_num = self:GetStepCostNum(cur_step + 1)
	    if item_num >= cost_num then
	    	remind = true
	    end
	end

	return remind
end

--任务红点
function FlopDrawWGData:ShowTaskRemind()
	local remind = false
	for i, v in pairs(self.task_list) do
		if v.status == REWARD_STATE_TYPE.CAN_FETCH then
			remind = true
			break
		end
	end

	return remind
end

---步进红点--
function FlopDrawWGData:ShowStepRewardRemind()
	local remind = false
	local reward_cfg = self:GetAllStepRewardCfg()
	if reward_cfg then
		for k, v in pairs(reward_cfg) do
			local is_get = self:GetRewardStateByStep(v.step)
			local is_flop = self:GetStepState(v.step)
			if not is_get and is_flop then
				remind = true
			end
		end
	end

	return remind
end

function FlopDrawWGData:GetGaiLvInfo()
	return self.item_random_desc[self.grade] or {}
end