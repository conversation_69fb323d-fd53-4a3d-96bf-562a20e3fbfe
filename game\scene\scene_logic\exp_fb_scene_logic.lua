ExpFbSceneLogic = ExpFbSceneLogic or BaseClass(CommonFbLogic)

function ExpFbSceneLogic:__init()

end

function ExpFbSceneLogic:__delete()
	
end

function ExpFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenWGCtrl.Instance:OpenTaskFollow()

	-- XuiBaseView.CloseAllView()
	-- UiInstanceMgr.Instance:OpenRewardAction(ResPath.GetScene("exp_reward"))
end

function ExpFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	FuBenWGCtrl.Instance:UpdataTaskFollow()
end

function ExpFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	-- UiInstanceMgr.Instance:CloseRewardAction()
end
