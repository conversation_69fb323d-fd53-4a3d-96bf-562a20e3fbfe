HotSpringRoleListView = HotSpringRoleListView or BaseClass(SafeBaseView)

function HotSpringRoleListView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
    self:SetMaskBgAlpha(0)
    self.view_name = "HotSpringRoleListView"
	self:AddViewResource(0, "uis/view/hot_spring_ui_prefab", "layout_role_list")
end

function HotSpringRoleListView:__delete()

end

function HotSpringRoleListView:ReleaseCallBack()
	
end

function HotSpringRoleListView:CloseCallBack()
	
end

function HotSpringRoleListView:LoadCallBack()
    self.role_list = AsyncListView.New(HotSpringRoleRender, self.node_list.ph_role_list)
    self.role_list:SetSelectCallBack(BindTool.Bind(self.SelectRole, self))
end

function HotSpringRoleListView:OnFlush()
    local role_list = Scene.Instance:GetRoleList()
    local data_list = {}
    if not IsEmptyTable(role_list) then
        for k,v in pairs(role_list) do
            local is_not_in_boat = Scene.Instance:GetBoatByRole(v.vo.obj_id) == nil
            if is_not_in_boat then
                local vo = {}
                vo.name = v.vo.name
                vo.role_id = v.vo.role_id
                vo.obj_id = v.vo.obj_id
                vo.uuid = v.vo.uuid
                vo.server_id = v.vo.merge_server_id or v.vo.origin_server_id
                vo.plat_type = v.vo.merge_plat_type or v.vo.origin_plat_type
                data_list[#data_list + 1] = vo
            end
        end
    end
    self.role_list:SetDataList(data_list)
    self.node_list.role_num_txt.text.text = string.format(Language.HotSpring.NearByRoleNum, #data_list)
    self.node_list.img_zanwu:SetActive(#data_list == 0)
end

function HotSpringRoleListView:SelectRole(item, cell_index, is_default, is_click)
    if not item or not item:GetData() or is_default or not is_click then
        return
    end
    local main_role = Scene.Instance:GetMainRole()
    local param = HotSpringWGData.Instance:GetCurOperType()
    local target
    if not IS_ON_CROSSSERVER then
        target = Scene.Instance:GetRoleByRoleId(item:GetData().role_id)
    else
        target = Scene.Instance:GetRoleByUUID(item:GetData().uuid)
    end
    if target then
        if not main_role:IsWaterWay() or not target:IsWaterWay() then
            local act_txt = Language.HotSpring.ActiveTxt[param]
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.HotSpring.NeedInWater, act_txt))
            self:Flush()
            return
        end

        if target.vo.shuangxiu_state == SHUANGXIU_TYPE.IS_SHUANGXIU then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.TargetIsShuangxiu)
            self:Flush()
            return
        elseif target.vo.anmo_state == ANMO_TYPE.IS_ANMO then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.TargetIsAnmo)
            self:Flush()
            return
        end

        -- local is_main_role_in_water = main_role:IsWaterWay()
        -- local is_target_in_water = target:IsWaterWay()
        local target_x, target_y = target:GetLogicPos()
        local tab = {target_obj = target, main_role = main_role, param = param}
        local target_x, target_y = target:GetLogicPos()
        local x, y = main_role:GetLogicPos()
        local distance = GameMath.GetDistance(x, y, target_x, target_y, true)

        local juli = 0
        if param ~= 0 and param ~= 1 then
            juli = 14
        end
        if math.floor(distance) > juli then
                local sence_id = Scene.Instance:GetSceneId()
                local ctrl = GuajiWGCtrl.Instance
                ctrl:SetMoveToPosCallBack(
                    function ()
                        self:OnMainRoleMoveEnd(tab)
                    end)
                ctrl:MoveToPos(sence_id,target_x, target_y, juli)
        else
            self:OnMainRoleMoveEnd(tab)
        end
        self:Close()
    end
    
end

function HotSpringRoleListView:OnMainRoleMoveEnd(tab)
	if nil == tab or nil == tab.param or nil == tab.main_role or nil == tab.target_obj then
		return
	end
	local param = tab.param
	local main_role = tab.main_role
	local target_obj = tab.target_obj

	if nil == target_obj or target_obj.vo == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.SelectTarget)
		return
	end

	if main_role.vo.is_in_snow or main_role.vo.is_in_hot_water then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.StateYunXuan)
		return
	end

	if target_obj.vo and (target_obj.vo.is_in_snow or target_obj.vo.is_in_hot_water) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.StateYunXuanTarget)
		return
	end
	--
	if param == 0 then
		if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip1_2)
			return
		end
		HotSpringWGCtrl.Instance:SendHotspringAction(target_obj.vo.obj_id, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_SHUANGXIU)
		
	elseif param == 1 then
		if main_role.vo.shuangxiu_state == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack)
			return
		end
		if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip1_2)
			return
		end

		if target_obj.vo and target_obj.vo.is_in_hot_spring and main_role.vo.is_in_hot_spring then
			HotSpringWGCtrl.Instance:SendHotspringAction(target_obj.vo.obj_id, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_ANMO)
		elseif not main_role.vo.is_in_hot_spring then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.AnmoTip)
		else
			--SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotThrowBallTargetInSpring)
		end
	elseif param == 2 then
		if main_role.vo.shuangxiu_state == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack)
			return
		end
		if main_role.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack2)
			return
		end
		if target_obj.vo and  target_obj.vo.anmo_state == ANMO_TYPE.IS_ANMO then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack4)
			return
		end
		if target_obj.vo and  target_obj.vo.shuangxiu_state == SHUANGXIU_TYPE.IS_SHUANGXIU then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotAttack3)
			return
		end

		if target_obj.vo and target_obj.vo.is_in_hot_spring and main_role.vo.is_in_hot_spring then
			HotSpringWGCtrl.Instance:SendHotspringAction(target_obj.vo.obj_id, HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_INTIMIDATION)
		elseif not main_role.vo.is_in_hot_spring then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.MainRoleNotInHotSpring)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.HotSpring.CanNotSplashHotWater)
		end
	end
	main_role:ChangeToCommonState()
end


HotSpringRoleRender = HotSpringRoleRender or BaseClass(BaseRender)
function HotSpringRoleRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.lbl_name.text.text = self.data.name
end