SwornRechargeTeamTipsView = SwornRechargeTeamTipsView or BaseClass(SafeBaseView)
function SwornRechargeTeamTipsView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ sizeDelta = Vector2(968, 498) })
	self:AddViewResource(0, "uis/view/sworn_recharge_prefab", "layout_recharge_teamtip")
end

function SwornRechargeTeamTipsView:ReleaseCallBack()
	if self.player_item_list then
		for key, value in pairs(self.player_item_list) do
			value:DeleteMe()
			value = nil
		end
		self.player_item_list = nil
	end
end

function SwornRechargeTeamTipsView:LoadCallBack()
	if not self.player_item_list then
		self.player_item_list = {}
		for idx = 1, 5 do
			self.player_item_list[idx] = SwornRechargePlayerItem.New(self.node_list["Node_PalyerItem" .. idx])
		end
	end
end

function SwornRechargeTeamTipsView:OnFlush()
	self:SetPanel()
	self:SetMyTeam()
end

function SwornRechargeTeamTipsView:SetPanel()
	local my_rank = SwornRechargeWGData.Instance:GetMyRank()

	self.node_list.text_rank.text.text = my_rank

	local my_rank_value = SwornRechargeWGData.Instance:GetMyRankValue()

	self.node_list.text_teamRecharge.text.text = my_rank_value

	local team_data = SwornRechargeWGData.Instance:GetSelfTeam()
	if IsEmptyTable(team_data) then
		return
	end

	local my_chongzhi = 0
	for key, value in pairs(team_data) do
		if value.uuid == RoleWGData.Instance:GetUUid() then
			my_chongzhi = value.chongzhi_num
			break
		end
	end
	self.node_list.text_playerRecharge.text.text = my_chongzhi
end

function SwornRechargeTeamTipsView:SetMyTeam()
	local team_data = SwornRechargeWGData.Instance:GetSelfTeam()
	if IsEmptyTable(team_data) then
		return
	end

	for key, value in pairs(self.player_item_list) do
		if team_data[key] then
			team_data[key].is_show_top_team = false
			value:SetData(team_data[key])
		end
	end
end
