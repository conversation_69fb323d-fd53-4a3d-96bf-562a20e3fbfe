ZhuXieTaskFinishView = ZhuXieTaskFinishView or BaseClass(SafeBaseView)
local CLOSE_TIME = 10
function ZhuXieTaskFinishView:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/zhuxie_ui_prefab", "zhuxie_task_finish_view")
end

function ZhuXieTaskFinishView:__delete()
end

function ZhuXieTaskFinishView:LoadCallBack()
	self.node_list.comfirm_btn.button:AddClickListener(BindTool.Bind(self.OnClickComfirmBtn,self))
	self.item_cell = ItemCell.New(self.node_list.reward_item)
end

-- 切换标签调用
function ZhuXieTaskFinishView:ShowIndexCallBack(index)
	self:Flush()
end

function ZhuXieTaskFinishView:SetData(data)
	self.data = data
end

function ZhuXieTaskFinishView:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	if CountDownManager.Instance:HasCountDown("zhuxie_task_finish_countdown") then
		CountDownManager.Instance:RemoveCountDown("zhuxie_task_finish_countdown")
	end
end

function ZhuXieTaskFinishView:OnFlush()
	if not self.data then return end
	local task_reward_data
	if IS_ON_CROSSSERVER then
		task_reward_data = ActivityWGData.Instance:GetKFZhuxieTaskReward(self.data.cfg.task_id)
	else
		task_reward_data = ActivityWGData.Instance:GetZhuxieTaskReward(self.data.cfg.task_id)
	end

	if not task_reward_data then return end

	self.item_cell:SetData(task_reward_data.item[0])
	local time = TimeWGCtrl.Instance:GetServerTime()
	self.node_list.double_flag:SetActive(self.data.is_double == 1 and self.data.double_task_end_time > time)
	self.node_list.task_name.text.text = string.format(Language.ZhuXie.FinishTask,self.data.cfg.task_name)

	if CountDownManager.Instance:HasCountDown("zhuxie_task_finish_countdown") then
		CountDownManager.Instance:RemoveCountDown("zhuxie_task_finish_countdown")
	end

	CountDownManager.Instance:AddCountDown("zhuxie_task_finish_countdown", BindTool.Bind1(self.ChangeTime, self), BindTool.Bind1(self.CompleteTime, self), nil, CLOSE_TIME, 1)
end

function ZhuXieTaskFinishView:ChangeTime()
	-- body
end

function ZhuXieTaskFinishView:CompleteTime()
	self:Close()
end

function ZhuXieTaskFinishView:OnClickComfirmBtn()
	self:Close()
end
