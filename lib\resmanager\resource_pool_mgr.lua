-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local ResUtil = require "lib/resmanager/res_util"
local GameObjectPool = require "lib/resmanager/gameobject_pool"
local ResPool = require "lib/resmanager/res_pool"
local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()

local TypeUnityTexture = typeof(UnityEngine.Texture)
local TypeUnitySprite = typeof(UnityEngine.Sprite)
local TypeUnityMaterial = typeof(UnityEngine.Material)
local TypeUnityPrefab = typeof(UnityEngine.GameObject)
local TypeAudioItem = typeof(AudioItem)
local TypeAudioMixer = typeof(UnityEngine.Audio.AudioMixer)
local TypeActorQingGongObject = typeof(ActorQingGongObject)
local TypeQualityConfig = typeof(QualityConfig)
local TypeTextAsset = typeof(UnityEngine.TextAsset)
local TypeRuntimeAnimatorController = typeof(UnityEngine.RuntimeAnimatorController)
local TypeShaderVariant = typeof(UnityEngine.ShaderVariantCollection)
local TypeUnityAudioClip = typeof(UnityEngine.AudioClip)
local TypeVideoClip = typeof(UnityEngine.Video.VideoClip)
local TypeRenderTexture = typeof(UnityEngine.RenderTexture)
local TypeCharacterData = typeof(SceneEnvironment.CharacterData)
local TypeTMPSpriteAsset = typeof(TMPro.TMP_SpriteAsset)
local TypeActorDyeItem = typeof(ActorDyeItem)

local is_debug = UnityEngine.Debug.isDebugBuild
local _slower = string.lower

local M = ResUtil.create_class()
function M:_init()
    self.v_used_pools = {}

    self.v_poots_root = ResMgr:CreateEmptyGameObj("Pools", true)
    self.v_poots_root_transform = self.v_poots_root.transform

    -- 添加原因看TryOptimizeEffect注释
    local type_drag = typeof(UIDrag)
    self.v_poots_root:GetOrAddComponent(type_drag)

    self.v_root = ResMgr:CreateEmptyGameObj("GameObjectPool", true)
    self.v_root_transform = self.v_root.transform
    self.v_root_transform:SetParent(self.v_poots_root_transform)
    self.v_root:SetActive(false)

    self.v_root_act = ResMgr:CreateEmptyGameObj("GameObjectPoolAct", true)
    self.v_root_act_transform = self.v_root_act.transform
    self.v_root_act_transform:SetParent( self.v_poots_root_transform)
    self.v_root_act_transform.localPosition = Vector3(-100000, -100000, -100000)

    self.v_res_pools = {}
    self.v_gameobj_pools = {}
    self.v_get_gameobj_session = 0
    self.v_priority_map = {}

    -- 初始化不同优先级下的加载列表
    self.priority_type_list = {ResLoadPriority.mid, ResLoadPriority.low}
    for i,v in ipairs(self.priority_type_list) do
        self.v_priority_map[v] = {}
        self.v_priority_map[v].get_gameobj_map = {}
        self.v_priority_map[v].get_gameobj_index = 1
    end

    self.next_check_pool_release_time = 0
end

function M:OnGameStop()
    if nil ~= self.v_poots_root then
        ResMgr:Destroy(self.v_poots_root)
        self.v_poots_root = nil
        self.v_root = nil
        self.v_root_act = nil
    end
end

function M:GetRoot()
    return self.v_root
end

function M:Update(now_time, elapse_time)
    self:QueueGetGameObject()
    self:UpdateAllPool(now_time)
    -- self:ResumeInvalidTransform(self.v_root_transform)
    -- self:ResumeInvalidTransform(self.v_root_act_transform)
end

function M:GetSprite(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == string.find(asset_name, ".png") then
        asset_name = asset_name .. ".png"
    end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeUnitySprite, callback, is_async, load_priority, cbdata)
end

function M:GetTexture(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    if nil == string.find(asset_name, ".png") and nil == string.find(asset_name, ".jpg") and nil == string.find(asset_name, ".tga")  then
        print_error("[ResPoolMgr] if you want to load texture, you must specified .jpg or .png!!", bundle_name, asset_name)
        callback(nil, cbdata)
        return
    end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeUnityTexture, callback, is_async, load_priority, cbdata)
end

function M:GetQualityConfig(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeQualityConfig, callback, is_async, load_priority, cbdata)
end

function M:GetTextAsset(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeTextAsset, callback, is_async, load_priority, cbdata)
end

function M:GetQingGongObj(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeActorQingGongObject, callback, is_async, load_priority, cbdata)
end

function M:GetAudioMixer(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeAudioMixer, callback, is_async, load_priority, cbdata)
end

function M:GetAudio(bundle_name, asset_name, callback, is_async, cbdata)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    if nil == string.find(asset_name, ".asset") then
        asset_name = asset_name .. ".asset"
    end
    self:_GetRes(bundle_name, asset_name, TypeAudioItem, callback, is_async, ResLoadPriority.high, cbdata)
end

function M:GetCharacterData(bundle_name, asset_name, callback, is_async, cbdata)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    if nil == string.find(asset_name, ".asset") then
        asset_name = asset_name .. ".asset"
    end
    self:_GetRes(bundle_name, asset_name, TypeCharacterData, callback, is_async, ResLoadPriority.high, cbdata)
end


function M:GetTMPSpriteAsset(bundle_name, asset_name, callback, is_async, cbdata)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    if nil == string.find(asset_name, ".asset") then
        asset_name = asset_name .. ".asset"
    end

    self:_GetRes(bundle_name, asset_name, TypeTMPSpriteAsset, callback, is_async, ResLoadPriority.high, cbdata)
end

function M:GetAnimatorController(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeRuntimeAnimatorController, callback, is_async, load_priority, cbdata)
end

function M:GetShaderVariant(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeShaderVariant, callback, is_async, load_priority, cbdata)
end

function M:GetMaterial(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    if nil == string.find(asset_name, ".mat") then
        asset_name = asset_name .. ".mat"
    end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeUnityMaterial, callback, is_async, load_priority, cbdata)
end

function M:GetAudioClip(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeUnityAudioClip, callback, is_async, load_priority, cbdata)
end

function M:GetResInType(bundle_name, asset_name, callback, is_async, res_type, cbdata, load_priority)
   if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, res_type, callback, is_async, load_priority, cbdata)
end

function M:TryGetMaterial(bundle_name, asset_name)
    if nil == bundle_name or nil == asset_name then return end

    if nil == string.find(asset_name, ".mat") then
        asset_name = asset_name .. ".mat"
    end

    asset_name = _slower(asset_name)
    local pool = self.v_res_pools[bundle_name]
    return pool ~= nil and pool:GetRes(asset_name) or nil
end

function M:GetPrefab(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    if nil == string.find(asset_name, ".prefab") then
        asset_name = asset_name .. ".prefab"
    end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeUnityPrefab, callback, is_async, load_priority, cbdata)
end

-- 加载视频片段
function M:GetVideoClip(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeVideoClip, callback, is_async, load_priority, cbdata)
end

-- 加载材质方案
function M:GetActorDyeItem(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeActorDyeItem, callback, is_async, load_priority, cbdata)
end

-- 加载Rt图
function M:GetRenderTexture(bundle_name, asset_name, callback, is_async, cbdata, load_priority)
    if nil == bundle_name or nil == asset_name then return end

    asset_name = _slower(asset_name)
    self:_GetRes(bundle_name, asset_name, TypeRenderTexture, callback, is_async, load_priority, cbdata)
end

function M:TryGetPrefab(bundle_name, asset_name)
    if nil == string.find(asset_name, ".prefab") then
        asset_name = asset_name .. ".prefab"
    end

    asset_name = _slower(asset_name)
    local pool = self.v_res_pools[bundle_name]
    return pool ~= nil and pool:GetRes(asset_name) or nil
end

function M:ScanRes(bundle_name, asset_name)
    if nil ~= self.v_res_pools[bundle_name] then
        return self.v_res_pools[bundle_name]:ScanRes(asset_name)
    end

    return nil
end

function M:GetResPoolAssetCount(bundle_name)
    if nil ~= self.v_res_pools[bundle_name] then
        return self.v_res_pools[bundle_name].v_asset_count
    else
        return 0
    end
end

function M:GetOrCreateResPool(bundle_name)
    local pool = self.v_res_pools[bundle_name]
    if nil == pool then
        pool = ResPool:new(bundle_name)
        self.v_res_pools[bundle_name] = pool
    end

    return pool
end

function M:_GetRes(bundle_name, asset_name, asset_type, callback, is_async, load_priority, cbdata)
    local asset = self:_TryGetRes(bundle_name, asset_name)
    if nil ~= asset then
        callback(asset, cbdata)
        return
    end

    self:_LoadRes(bundle_name, asset_name, asset_type, callback, is_async, load_priority, cbdata)
end

function M:_TryGetRes(bundle_name, asset_name)
    local pool = self:GetOrCreateResPool(bundle_name)
    local asset = pool:GetRes(asset_name)
    if nil == asset then
        return nil
    end

    local instance_id = asset:GetInstanceID()
    if nil ~= self.v_used_pools[instance_id] and self.v_used_pools[instance_id] ~= pool then
        print_error("[ResPoolMgr] _TryGetRes big bug!!!", bundle_name, asset_name)
    end
    self.v_used_pools[instance_id] = pool

    return asset
end

local function LoadObjectCallBack(asset, cbdata)
    local self = cbdata[1]
    local bundle_name = cbdata[2]
    local asset_name = cbdata[3]
    local callback = cbdata[4]
    local up_cbdata = cbdata[5]
    self:_ReleaseLoadObjectCBData(cbdata)

    if nil == asset then
        if callback then
            callback(nil, up_cbdata)
        end
        return
    end

    local old_asset = self:_TryGetRes(bundle_name, asset_name)
    if nil ~= old_asset then
        if old_asset ~= asset then
            print_error("[ResPoolMgr] _LoadRes big bug, old_asset is not same!!!", bundle_name, asset_name)
        end

        if callback then
            callback(old_asset, up_cbdata)
        end
        return
    end

    if IS_LOCLA_WINDOWS_DEBUG_EXE then
        RuntimeGUIMgr.Instance:OnLoadObject(bundle_name, asset_name, asset)
    end

    local pool = self:GetOrCreateResPool(bundle_name)
    pool:CacheRes(asset_name, asset)
    if callback then
        callback(self:_TryGetRes(bundle_name, asset_name), up_cbdata)
    end
end

function M:_LoadRes(bundle_name, asset_name, asset_type, callback, is_async, load_priority, up_cbdata)
    if is_develop and not EditorResourceMgr.IsExistsAsset(bundle_name, asset_name) then
        print_error("加载不存在资源，马上检查!!!", bundle_name, asset_name)
        return
    end

    local load_fun = nil
    if is_async then
        load_fun = ResMgr.__LoadObjectAsync
    else
        load_fun = ResMgr.__LoadObjectSync
    end

    local cbdata = self:_GetLoadObjectCBData()
    cbdata[1] = self
    cbdata[2] = bundle_name
    cbdata[3] = asset_name
    cbdata[4] = callback
    cbdata[5] = up_cbdata
    load_fun(ResMgr, bundle_name, asset_name, asset_type, LoadObjectCallBack, cbdata, load_priority or ResLoadPriority.high)
end

function M:QueueGetGameObject()
    local remain_get_count = 2 -- 最多同时获取N个, 按照优先级
    for _, v in ipairs(self.priority_type_list) do
        if remain_get_count <= 0 then
            break
        end

        local priority_t = self.v_priority_map[v]
        for i = priority_t.get_gameobj_index, self.v_get_gameobj_session do
            local t = priority_t.get_gameobj_map[i]
            priority_t.get_gameobj_index = priority_t.get_gameobj_index + 1
            if nil ~= t then
                priority_t.get_gameobj_map[i] = nil
                remain_get_count = remain_get_count - 1
                self:_GetGameObject(t[1], t[2], t[3], t[4], t[6], t[5], t[7])
                self:_ReleaseDynamicObjCBData(t)

                if remain_get_count <= 0 then
                    break
                end
            end
        end
    end
end

function M:GetOrCreateGameObjPool(bundle_name, asset_name)
	local full_path = ResUtil.GetAssetFullPath(bundle_name, asset_name)
    local pool = self.v_gameobj_pools[full_path]
    if nil == pool then
        pool = GameObjectPool:new(self.v_root_transform, self.v_root_act_transform, full_path)
        self.v_gameobj_pools[full_path] = pool
    end

    return pool
end

function M:_GetGameObject(bundle_name, asset_name, callback, is_async, parent, load_priority, cbdata)
    if nil == string.find(asset_name, ".prefab") then
        asset_name = asset_name .. ".prefab"
    end

    asset_name = _slower(asset_name)
    local gameobj = self:_TryGetGameObject(bundle_name, asset_name, parent)
    if nil ~= gameobj then
        callback(gameobj, cbdata)
        return true
    end

    self:_LoadGameObject(bundle_name, asset_name, callback, is_async, parent, load_priority, cbdata)
    return false
end

function M:_TryGetGameObject(bundle_name, asset_name, parent)
    local pool = self:GetOrCreateGameObjPool(bundle_name, asset_name)
    local gameobj = pool:TryPop()
    if nil == gameobj then
        return nil
    end

    local instance_id = gameobj:GetInstanceID()
    if nil ~= self.v_used_pools[instance_id] and self.v_used_pools[instance_id] ~= pool then
        print_error("[ResPoolMgr] _TryGetGameObject big bug!!!", bundle_name, asset_name)
    end

    self.v_used_pools[instance_id] = pool

    if not IsNil(parent) then
        gameobj.transform:SetParent(parent, false)
    end

    return gameobj
end

local function LoadGameObjectCallBack(gameobj, cbdata)
    local self = cbdata[1]
    local bundle_name = cbdata[2]
    local asset_name = cbdata[3]
    local callback = cbdata[4]
    local up_cbdata = cbdata[5]
    self:_ReleaseLoadGameObjectCBData(cbdata)

    if nil == gameobj then
        callback(nil, up_cbdata)
        return
    end

    local pool = self:GetOrCreateGameObjPool(bundle_name, asset_name)
    local instance_id = gameobj:GetInstanceID()
    if nil ~= self.v_used_pools[instance_id] and self.v_used_pools[instance_id] ~= pool then
        print_error("[ResPoolMgr] _LoadGameObject big bug!!!", bundle_name, asset_name)
    end

    self.v_used_pools[instance_id] = pool
    pool:CacheOrginalTransformInfo(ResMgr:GetPrefab(instance_id))
    callback(gameobj, up_cbdata)
end

function M:_LoadGameObject(bundle_name, asset_name, callback, is_async, parent, load_priority, up_cbdata)
    local cbdata = self:_GetLoadGameObjectCBData()
    cbdata[1] = self
    cbdata[2] = bundle_name
    cbdata[3] = asset_name
    cbdata[4] = callback
    cbdata[5] = up_cbdata

    local load_fun = nil
    if is_async then
        load_fun = ResMgr.LoadGameobjAsync
    else
        load_fun = ResMgr.LoadGameobjSync
    end

    load_fun(ResMgr, bundle_name, asset_name,
        LoadGameObjectCallBack, parent, cbdata, load_priority)
end

function M:_GetGameObjectInPrefab(prefab, parent)
    if nil == prefab then
        return nil
    end

    local pool = self.v_gameobj_pools[prefab]
    if nil == pool then
        pool = GameObjectPool:new(self.v_root_transform, self.v_root_act_transform)
        self.v_gameobj_pools[prefab] = pool

        if is_develop then
            develop_mode:OnLuaCall("cahce_asset_res_pool", pool, prefab.name, prefab)
        end
    end

    local gameobj = pool:TryPop()
    if nil ~= gameobj then
        local instance_id = gameobj:GetInstanceID()
        if nil ~= self.v_used_pools[instance_id] and self.v_used_pools[instance_id] ~= pool then
            print_error("[ResPoolMgr] _GetGameObjectInPrefab big bug!!!", prefab.name)
        end
        self.v_used_pools[instance_id] = pool

        if not IsNil(parent) then
            gameobj.transform:SetParent(parent, false)
        end
        return gameobj
    end

    local gameobj2 = ResMgr:Instantiate(prefab, false, parent)
    local instance_id = gameobj2:GetInstanceID()
    self.v_used_pools[instance_id] = pool
    pool:CacheOrginalTransformInfo(prefab)

    return gameobj2
end

function M:GetEffectAsync(bundle_name, asset_name, callback, parent)
    if nil == bundle_name or nil == asset_name then
        print_error("[ResPoolMgr] GetEffectAsync param is ivalid", bundle_name, asset_name)
        return
    end

    if is_develop then
        develop_mode:OnLuaCall("call_obsolete", "不要再在外部直接使用GetEffectAsync方法。写逻辑的人经常写出内存泄漏，请使用loader，马上修改!!!!", bundle_name, asset_name)
    end

    self:_GetGameObject(bundle_name, asset_name, callback, true, parent, ResLoadPriority.low)
end

function M:__GetDynamicObjAsync(bundle_name, asset_name, callback, parent, load_priority, cbdata)
    if nil == bundle_name or nil == asset_name then
        print_error("[ResPoolMgr] __GetDynamicObjAsync param is ivalid", bundle_name, asset_name)
        return
    end

    if ResLoadPriority.low == load_priority or ResLoadPriority.mid == load_priority then
        self.v_get_gameobj_session = self.v_get_gameobj_session + 1

        local t = self:_GetDynamicObjCBData()
        t[1] = bundle_name
        t[2] = asset_name
        t[3] = callback
        t[4] = true
        t[5] = load_priority
        t[6] = parent
        t[7] = cbdata
        local load_priority_t = self.v_priority_map[load_priority]
        load_priority_t.get_gameobj_map[self.v_get_gameobj_session] = t
        return self.v_get_gameobj_session
    else
        self:_GetGameObject(bundle_name, asset_name, callback, true, parent, load_priority, cbdata)
    end
end

function M:GetDynamicObjSync(bundle_name, asset_name, callback, parent)
    if is_develop then
        develop_mode:OnLuaCall("call_obsolete", "不要再在外部直接使用GetDynamicObjSync方法。写逻辑的人经常写出内存泄漏，请使用loader，马上修改!!!!", bundle_name, asset_name)
    end
    self:__GetDynamicObjSync(bundle_name, asset_name, callback, parent)
end

function M:__GetDynamicObjSync(bundle_name, asset_name, callback, parent, cbdata)
    self:_GetGameObject(bundle_name, asset_name, callback, false, parent, ResLoadPriority.sync, cbdata)
end

function M:__CancleGetInQueue(session)
   for k,v in pairs(self.v_priority_map) do
        local t = v.get_gameobj_map[session]
        v.get_gameobj_map[session] = nil
        if nil ~= t then
            self:_ReleaseDynamicObjCBData(t)
        end
   end
end

function M:TryGetGameObject(bundle_name, asset_name, parent)
    local prefab = self:TryGetPrefab(bundle_name, asset_name)
    return self:_GetGameObjectInPrefab(prefab, parent)
end

function M:TryGetGameObjectInPrefab(prefab, parent)
     return self:_GetGameObjectInPrefab(prefab, parent)
end

function M:IsPoolObj(obj)
    if IsNil(obj) then return false end

    local cid = obj:GetInstanceID()
    return nil ~= self.v_used_pools[cid]
end

function M:Release(obj, release_policy)
    if IsNil(obj) then return end

    local cid = obj:GetInstanceID()
    local pool = self.v_used_pools[cid]
    if nil == pool then
        print_error("[ResMgr] Release 释放一个没有pooled的 ", obj.name, cid)
        return
    end

    if pool:Release(obj, release_policy) then
        self.v_used_pools[cid] = nil
    end
end

function M:ReleaseInObjId(cid)
    local pool = self.v_used_pools[cid]
    if nil == pool then
        -- print_error("[ResMgr] ReleaseInObjId 释放一个没有pooled的 ", cid)
        return
    end

    if pool:ReleaseInObjId(cid) then
        self.v_used_pools[cid] = nil
    end
end

function M:OnGameObjIllegalDestroy(cid)
    self.v_used_pools[cid] = nil
end

function M:IsInGameObjPool(cid, gameobj)
    local pool = self.v_used_pools[cid]
    return nil ~= pool and pool:GetGameObjIsCache(gameobj)
end

function M:Clear()
    self:ClearPools(self.v_gameobj_pools)
    self:ClearPools(self.v_res_pools)
end

function M:ClearPools(pools)
    local del_pool_list = {}
    for k, pool in pairs(pools) do
        if pool:Clear() then
            table.insert(del_pool_list, k)
        end
    end

    for _, v in ipairs(del_pool_list) do
        pools[v] = nil
    end
end

function M:UpdateAllPool(now_time)
    if now_time < self.next_check_pool_release_time then
        return
    end

    self.next_check_pool_release_time = now_time + 0.1

    self:UpdatePool(self.v_gameobj_pools, now_time)
    self:UpdatePool(self.v_res_pools, now_time)
end

function M:UpdatePool(pools, now_time)
    for k, pool in pairs(pools) do
        if pool:Update(now_time) then
            pools[k] = nil
            break
        end
    end
end

-- 检查对象池节transform属性是否为0，被修改将非常严重
function M:ResumeInvalidTransform(transform)
    if is_debug then
        return
    end

    if 0 ~= transform.localPosition.x or 0 ~= transform.localPosition.y or 0 ~= transform.localPosition.z then
        transform:SetLocalPosition(0, 0, 0)
        print_error("[ResourcePool] big bug!!!!, object pool be modified!!!!")
    end

    if 0 ~= transform.rotation.x or 0 ~= transform.rotation.y or 0 ~= transform.rotation.z then
        transform.localRotation = Quaternion.Euler(0, 0, 0)
        print_error("[ResourcePool] big bug!!!!, object pool be modified!!!!")
    end

    if 1 ~= transform.localScale.x or 1 ~= transform.localScale.y or 1 ~= transform.localScale.z then
        transform:SetLocalScale(1, 1, 1)
        print_error("[ResourcePool] big bug!!!!, object pool be modified!!!!")
    end
end

local DynamicObjCBDataList = {}
function M:_GetDynamicObjCBData()
    local t = table.remove(DynamicObjCBDataList)
    if nil == t then
        -- [1]bundle_name[2]asset_name[3]callback[4]is_async[5]load_priority[6]parent[7]cbdata
        t = {true, true, true, true, true, true, true}
    end

    return t
end

function M:_ReleaseDynamicObjCBData(t)
    t[3] = true
    t[6] = true
    t[7] = true
    table.insert(DynamicObjCBDataList, t)
end

local LoadGameObjectCBDataList = {}
function M:_GetLoadGameObjectCBData()
    local t = table.remove(LoadGameObjectCBDataList)
    if nil == t then
        -- [1]self[2]bundle_name[3]asset_name[4]callback[5]up_cbdata
        t = {true, true, true, true, true}
    end

    return t
end

function M:_ReleaseLoadGameObjectCBData(t)
    t[4] = nil
    table.insert(LoadGameObjectCBDataList, t)
end

local LoadObjectCBDataList = {}
function M:_GetLoadObjectCBData()
    local t = table.remove(LoadObjectCBDataList)
    if nil == t then
        -- [1]self[2]bundle_name[3]asset_name[4]callback[5]up_cbdata
        t = {true, true, true, true, true}
    end

    return t
end

function M:_ReleaseLoadObjectCBData(t)
    t[4] = true
    t[5] = true
    table.insert(LoadObjectCBDataList, t)
end

function M:GetPoolDebugInfo(t)
    t.res_count = 0
    t.res_pool_count = 0

    for k,v in pairs(self.v_res_pools) do
        t.res_pool_count = t.res_pool_count + 1
        t.res_count =  t.res_count + v:GetAssetCount()
    end

    t.gameobj_cache_count = 0
    t.gameobj_pool_count = 0
    for k,v in pairs( self.v_gameobj_pools) do
        t.gameobj_pool_count = t.gameobj_pool_count + 1
        t.gameobj_cache_count = t.gameobj_cache_count + v:GetCacheCount()
    end
end

-- debug
function M:CheckLeak()
    local res_pools = {
        self.v_res_pools,
        self.v_gameobj_pools,
    }

    local debug_str = ""
    for k,pools in ipairs(res_pools) do
        local tbl = {}
        debug_str = debug_str .. "=================" .. k .. "=================\n"
        for _, v in pairs(pools) do
            table.insert(tbl, v:GetDebugStr())
        end
        debug_str = debug_str .. table.concat(tbl)
    end
  
    return debug_str
end

function M:GetAllResInstanceID()
    local all_list = {}
    for k, pool in pairs(self.v_res_pools) do
        local list = pool:GetResInstanceIDs()
        all_list[pool.v_bundle_name] = list
    end

    return all_list
end

function M:GetResRefCount(bundle_name, instance_id)
    local res_pool = self.v_res_pools[bundle_name]
    if res_pool then
        if nil == res_pool.GetResRefCount then
            print_error("[GetResRefCount]有bug")
            return 0
        end

        local count, asset_name, asset_count = res_pool:GetResRefCount(instance_id)
        return count, asset_name, asset_count
    end

    return 0
end

function M:GetAllResInfoInResPool(bundle_name)
    local res_pool = self.v_res_pools[bundle_name]
    if res_pool then
        if nil == res_pool.GetAllRes then
            print_error("[GetAllResInfoInResPool]有bug")
            return
        end

        return res_pool:GetAllRes()
    end
end

return M

