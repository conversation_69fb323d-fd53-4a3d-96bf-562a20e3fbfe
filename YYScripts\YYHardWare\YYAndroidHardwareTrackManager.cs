﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using System.IO;

using System;

using YYGot.OffLine;

public class YYAndroidHardwareTrackManager : YYHardwareTrackManager {

    private string tempPathFormat = "/sys/class/thermal/thermal_zone{0}/temp";

    private string tempPathFormat2 = "/sys/devices/virtual/thermal/thermal_zone{0}/temp";

    public float interTime = 1.0f;
    float updateTime = 0.0f;
    private void Update()
    {
        updateTime += Time.deltaTime;

        if (updateTime >= interTime)
        {
            //Debug.Log("time is qqqqqq");
            UpdateTemperature();
            updateTime = 0.0f;
        }
    }

    private void OnGUI()
    {
        //GUILayout.Label("温度是" + _runtimeInfo.Temperature);
        //GUI.Label(new Rect(0, 0, 1000, 50), "温度是" + _runtimeInfo.Temperature);

        int w = Screen.width, h = Screen.height;

        GUIStyle style = new GUIStyle();

        Rect rect = new Rect(0, 0, w, h * 2 / 100);
        style.alignment = TextAnchor.UpperLeft;
        style.fontSize = h * 2 / 30;
        style.normal.textColor = new Color(0.0f, 0.0f, 0.5f, 1.0f);
        float msec = 1.0f;// deltaTime * 1000.0f;
        float fps = _runtimeInfo.Temperature;// 1.0f / deltaTime;
        string text = string.Format("温度 ({0:0.00} 摄氏度)", fps);

        //string text = "温度是" + _runtimeInfo.Temperature;
        GUI.Label(rect, text, style);
    }

    private void UpdateTemperature()
    {
        int num = 0;
        string path = string.Format(tempPathFormat, num);
        List<float> list = new List<float>();
        _runtimeInfo.CpuTemp.Clear();

        while (File.Exists(path))
        {
            _runtimeInfo.CpuTemp.Add(-1f);
            int index = _runtimeInfo.CpuTemp.Count - 1;

            try
            {
                string[] array = YYCoreUtils.ReadAllLinesFromFile(path);
                if (array.Length == 1 && !string.IsNullOrEmpty(array[0]))
                {
                    float num2 = float.Parse(array[0]);
                    if (num2 < 100f && num2 > 0f)
                    {
                        list.Add(num2);
                    }
                    if (num2 < 1000f && num2 > 100f)
                    {
                        list.Add(num2 / 10f);
                    }
                    if (num2 > 1000f)
                    {
                        list.Add(num2 / 1000f);
                    }
                    _runtimeInfo.CpuTemp[index] = num2;
                }
            }
            catch (Exception)
            {
            }
            num++;
            path = string.Format(tempPathFormat, num);
        }
        if (num == 0)
        {
            _runtimeInfo.CpuTemp.Clear();
            path = string.Format(tempPathFormat2, num);
            while (File.Exists(path))
            {
                _runtimeInfo.CpuTemp.Add(-1f);
                int index2 = _runtimeInfo.CpuTemp.Count - 1;
                try
                {
                    string[] array2 = YYCoreUtils.ReadAllLinesFromFile(path);
                    if (array2.Length == 1 && !string.IsNullOrEmpty(array2[0]))
                    {
                        float num3 = float.Parse(array2[0]);
                        if (num3 < 100f && num3 > 0f)
                        {
                            list.Add(num3);
                        }
                        if (num3 < 1000f && num3 > 100f)
                        {
                            list.Add(num3 / 10f);
                        }
                        if (num3 > 1000f)
                        {
                            list.Add(num3 / 1000f);
                        }
                        _runtimeInfo.CpuTemp[index2] = num3;
                    }
                }
                catch (Exception)
                {
                }
                num++;
                path = string.Format(tempPathFormat, num);
            }
        }
        if (num != 0 && list.Count != 0)
        {
            float num4 = 0f;
            for (int i = 0; i < list.Count; i++)
            {
                num4 += list[i];
            }
            _runtimeInfo.Temperature = num4 / (float)list.Count;
        }
    }
}
