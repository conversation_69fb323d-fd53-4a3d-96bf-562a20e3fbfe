KFOneVOneResultView = KFOneVOneResultView or BaseClass(SafeBaseView)
function KFOneVOneResultView:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_onevone_result")
	self:SetMaskBg(false)
	self.active_close = false
end

function KFOneVOneResultView:__delete()
end

function KFOneVOneResultView:LoadCallBack()
	self.list_view = AsyncListView.New(OneVOneResultItem, self.node_list["list_view"])
	XUI.AddClickEventListener(self.node_list["close_btn"], BindTool.Bind(self.ClickCloseBtn, self))
	self.item_list = AsyncListView.New(ItemCell, self.node_list["item_list"])
	self.winner_item_list = AsyncListView.New(ItemCell, self.node_list["winner_item_list"])

	self.winner_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["role_model"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}
	
	self.winner_model:SetRenderTexUI3DModel(display_data)

	-- self.winner_model:SetUI3DModel(self.node_list["role_model"].transform,
	-- 	self.node_list["role_model"].event_trigger_listener,
	-- 	1, false, MODEL_CAMERA_TYPE.BASE)
end

function KFOneVOneResultView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end

	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	if self.winner_item_list then
		self.winner_item_list:DeleteMe()
		self.winner_item_list = nil
	end

	if self.winner_model then
		self.winner_model:DeleteMe()
		self.winner_model = nil
	end

	if CountDownManager.Instance:HasCountDown("onevone_result_close") then
		CountDownManager.Instance:RemoveCountDown("onevone_result_close")
	end
end

function KFOneVOneResultView:CloseCallBack()

end

function KFOneVOneResultView:ShowIndexCallBack()
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_WINNER_INFO)

	local time = 20
	if time > 0 then
		if CountDownManager.Instance:HasCountDown("onevone_result_close") then
			CountDownManager.Instance:RemoveCountDown("onevone_result_close")
		end
		CountDownManager.Instance:AddCountDown("onevone_result_close", BindTool.Bind(self.UpdataTime, self),
			BindTool.Bind(self.TimeCompleteCallBack, self), nil, time, 1)
	end
end

function KFOneVOneResultView:OnFlush()
	local result_data = KFOneVOneWGData.Instance:GetOneVOneResultData()
	self.list_view:SetDataList(result_data)

	local my_knockout_index = KFOneVOneWGData.Instance:GetMyKnockoutIndex()
	-- self.node_list["my_msg"]:SetActive(my_knockout_index ~= nil)
	self.node_list["not_rank"]:SetActive(my_knockout_index == nil)

	if my_knockout_index ~= nil then
		local my_data = {}
		for k, v in pairs(result_data) do
			if v.index == my_knockout_index then
				my_data = v
			end
		end
		if not IsEmptyTable(my_data) then
			local rank = my_data and my_data.rank or 0
			local rank_seq = my_data and my_data.rank_seq or 0
			local info = KFOneVOneWGData.Instance:GetKnockoutItemInfo(my_data.index)
			local name = info and info.name or ""
			self.node_list["my_name"].text.text = name
			local rank_reward_cfg = KFOneVOneWGData.Instance:GetRankRewardCfgBySeq(rank_seq)
			if rank_reward_cfg then
				self.node_list["rank_num"].text.text = rank_reward_cfg.title_des
				local item_data = {}
				for i = 0, #rank_reward_cfg.reward_item do
					table.insert(item_data, rank_reward_cfg.reward_item[i])
				end
				if not IsEmptyTable(item_data) then
					self.item_list:SetDataList(item_data)
				end
			end
		end
	else
		self.node_list["rank_num"].text.text = Language.Kuafu1V1.NoInRank
		local role_vo = RoleWGData.Instance:GetRoleVo()
		self.node_list["my_name"].text.text = role_vo.name
	end

	self:FlushWinnerModel()
end

function KFOneVOneResultView:FlushWinnerModel()
	local role_id, plat_type = KFOneVOneWGData.Instance:GetCross1V1Winner(1)
	if role_id and plat_type and role_id > 0 and plat_type >= 0 then
		self.node_list["not_winner"]:SetActive(false)
		self.node_list["winner_panel"]:SetActive(true)
		BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(data_info)
			if self:IsOpen() then
				if self.winner_model then
					self.winner_model:SetModelResInfo(data_info)
				end
				if self.node_list["winner_name"] then
					self.node_list["winner_name"].text.text = data_info.role_name
				end
			end
		end, plat_type)
		local rank_reward_cfg = KFOneVOneWGData.Instance:GetRankRewardCfgBySeq(0)
		if rank_reward_cfg then
			local item_data = {}
			for i = 0, #rank_reward_cfg.reward_item do
				table.insert(item_data, rank_reward_cfg.reward_item[i])
			end
			if not IsEmptyTable(item_data) then
				self.winner_item_list:SetDataList(item_data)
			end
			local title_id = rank_reward_cfg.title_id
			local title_cfg = TitleWGData.Instance:GetConfig(title_id)
			local b, a = ResPath.GetTitleModel(title_id)
			self.node_list["image_title"]:SetActive(false)
			self.node_list["image_title"]:SetActive(true)
			self.node_list["image_title"]:ChangeAsset(b, a, false)
		end
	else
		self.node_list["not_winner"]:SetActive(true)
		self.node_list["winner_panel"]:SetActive(false)
	end
end

function KFOneVOneResultView:UpdataTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	if self.node_list["close_text"] then
		self.node_list["close_text"].text.text = string.format(Language.Common.AutoCloseTimerTxt, time)
	end
end

function KFOneVOneResultView:TimeCompleteCallBack()
	self:Close()
end

function KFOneVOneResultView:ClickCloseBtn()
	self:Close()
end

-----------------ItemRender-------------
OneVOneResultItem = OneVOneResultItem or BaseClass(BaseRender)

function OneVOneResultItem:__init()
	self.item_list = AsyncListView.New(ItemCell, self.node_list["item_list"])
end

function OneVOneResultItem:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function OneVOneResultItem:OnFlush()
	if not self.data then return end
	local name = ""
	if self.data.index > 0 then
		local my_knockout_index = KFOneVOneWGData.Instance:GetMyKnockoutIndex()
		local info = KFOneVOneWGData.Instance:GetKnockoutItemInfo(self.data.index)
		name = info.name or ""
		if my_knockout_index and my_knockout_index == self.data.index then
			name = ToColorStr(name, COLOR3B.D_GREEN)
		end
	else
		name = Language.Kuafu1V1.XuWeiYiDai
	end

	self.node_list["name"].text.text = name

	local item_data = {}
	local rank_reward_cfg = KFOneVOneWGData.Instance:GetRankRewardCfgBySeq(self.data.rank_seq)
	if rank_reward_cfg then
		-- self.node_list["rank_num"]:SetActive(true)
		self.node_list["rank_num"].text.text = rank_reward_cfg.title_des
		for i = 0, #rank_reward_cfg.reward_item do
			table.insert(item_data, rank_reward_cfg.reward_item[i])
		end
	end

	if not IsEmptyTable(item_data) then
		self.item_list:SetDataList(item_data)
	end
end
