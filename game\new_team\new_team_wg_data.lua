-- /jy_gm TeamFlagGmOperate: 1  -- GM开启分身功能
NewTeamWGData = NewTeamWGData or BaseClass()
-- 组队目标
TeamDataConst = {
	GoalType = {
	None = 0,
	GuaJi = 0,
	TeamExpFb = 1,
	TeamEquipFb = 3,
	HighTeamEquipFb = 4,
	ZhuShenTa = 5, 			--诛神塔
	}
}

GoalTeamType = {
	YeWaiGuaJi = 0, 		-- 野外挂机
	Exp_DuJieXianZhou = 6, --1  -- 渡劫仙舟
	QingYuanFb = 2, 		-- 情缘副本
	YuanGuXianDian = 4, 	-- 远古仙殿
	ZhuShenTa = 5, 			-- 诛神塔
	Exp_FuMoZhanChuan = 6, 	-- 伏魔战船
	PengLaiTanBao = 7, 		-- 蓬莱探宝
    ManHuangGuDian = 8, 	-- 蛮荒古殿
	FbControlBeastsType = 10, --幻兽副本
	FbBeautyType = 11,      -- 女神副本
	FbWuHunType = 12,       -- 武魂副本
	FbRuneTowerType = 13,   -- 符文塔副本
}

GoalQingYuanFbMaxCount = {
	[GoalTeamType.QingYuanFb] = 2,
}

QingYuanLimitCount = 2

--最大队伍成员数量
MaxTeamCount = 5

--不显示到界面的类型，这类玩法不可匹配，所以不显示在界面上
GoalNotShowInViewType = {
	--[GoalTeamType.PengLaiTanBao] = true,
}

--拥有协助次数的玩法目标
GoalHasXieZhuType = {
	[GoalTeamType.QingYuanFb] = true,
	[GoalTeamType.YuanGuXianDian] = true,
	[GoalTeamType.ZhuShenTa] = true,
	[GoalTeamType.Exp_DuJieXianZhou] = true,
	[GoalTeamType.Exp_FuMoZhanChuan] = true,
	[GoalTeamType.ManHuangGuDian] = true,
	[GoalTeamType.FbControlBeastsType] = true,
	[GoalTeamType.FbBeautyType] = true,
	[GoalTeamType.FbWuHunType] = true,
	[GoalTeamType.FbRuneTowerType] = true,
}
--可以匹配跨服的玩法目标
GoalCrossType = {
	[GoalTeamType.QingYuanFb] = true,
	[GoalTeamType.YuanGuXianDian] = true,
	[GoalTeamType.ZhuShenTa] = true,
	[GoalTeamType.Exp_DuJieXianZhou] = true,
	[GoalTeamType.Exp_FuMoZhanChuan] = true,
	[GoalTeamType.ManHuangGuDian] = true,
	[GoalTeamType.FbControlBeastsType] = true,
	[GoalTeamType.FbBeautyType] = true,
	[GoalTeamType.FbWuHunType] = true,
	[GoalTeamType.FbRuneTowerType] = true,
}

--可以购买次数的玩法目标
GoalCanButTimesType = {
	[GoalTeamType.QingYuanFb] = true,
	-- [GoalTeamType.YuanGuXianDian] = true,
	[GoalTeamType.Exp_DuJieXianZhou] = true,
	[GoalTeamType.Exp_FuMoZhanChuan] = true,
	[GoalTeamType.ManHuangGuDian] = true,
}

--需要消耗门票的玩法目标
ConsumeTicketsTeamType = {
	[GoalTeamType.Exp_DuJieXianZhou] = 26427,
	[GoalTeamType.Exp_FuMoZhanChuan] = 26427,
}

--需要消耗门票的玩法目标
XieZhuRewardTeamType = {
	[GoalTeamType.QingYuanFb] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR,
	[GoalTeamType.YuanGuXianDian] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR,
	[GoalTeamType.ZhuShenTa] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR,
	[GoalTeamType.Exp_DuJieXianZhou] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR,
	[GoalTeamType.Exp_FuMoZhanChuan] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR,
	[GoalTeamType.ManHuangGuDian] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR,
}

RoomSceneType = {
	[SceneType.QingYuanFB] = true,
	[SceneType.Wujinjitan] = true,
	[SceneType.LingHunGuangChang] = true,
	[SceneType.HIGH_TEAM_EQUIP_FB] = true,
	[SceneType.ZHUSHENTA_FB] = true,
	[SceneType.ManHuangGuDian_FB] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_1] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_2] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_3] = true,
	[SceneType.TEAM_COMMON_TOWER_FB_1] = true,
}

--不显示进入副本按钮的
NotShowBtnEnter ={
	[GoalTeamType.PengLaiTanBao] = true,
}


function NewTeamWGData:__init()
	if nil ~= NewTeamWGData.Instance then
		ErrorLog("[NewTeamWGData]:Attempt to create singleton twice!")
	end
	NewTeamWGData.Instance = self

	local big_cfg = ConfigManager.Instance:GetAutoConfig("teamtarget_auto")
 	self.teamt_arget_info = big_cfg.team_target
	self.match_time_cfg = big_cfg.match_time
	self.other_cfg = big_cfg.other[1]
	self.barrage_cfg = ListToMapList(big_cfg.barrage_cfg, "team_type")
	self.team_target_limit_cfg = ListToMapList(big_cfg.team_target_limit, "team_type")
	self:RecordTargetCountList() 	-- 记录每个类型有多少个数量
	self:InitMenuBtnList() 			-- 初始化按钮菜单列表

	self.now_goal_info = {}

	self.is_matching = false        -- 是否匹配中
	self.near_team_list = {}        -- 附近队伍
	self.near_team_list.team_list = {}
	self.near_role_list = {}        -- 附近玩家信息(同场景)

	self.team_type = 0      -- 队伍目标(仅用于筛选队伍)
	self.fb_mode = 0

	self.pingtai_team_type = 0      -- 队伍目标(仅用于筛选队伍)
	self.pingtai_fb_mode = 0
	self.min_level = 1
	self.max_level = RoleWGData.GetRoleMaxLevel()

	self.team_index = -1    -- 队伍索引

 	self.to_team_type = 0          --队伍进入的目标
 	self.to_teamfb_mode = 0
 	self.role_appearance = {}
 	self.old_team_info = {}

	self.cur_xiezhu_times = 0 --今日已经协助的次数
	self.cur_xiezhu_times_max = 0 --总协助次数

	self.room_info = {}
	self.room_info.room_member_list = {}

	self.team_open_callback = {}
	--合并消息列表
	self.hebing_list = {}

	--快速请求入队消息队列
	self.quick_invite_team_list = {}

	self.quick_invite_match_info = {}
	--指定邀请队列
	self.specific_invite_team_list = {}

	self.fenshen_open_flag = 0
	self.fenshen_mail_count = {}
	self.member_share_item_list = {}
	self.can_use_item_info = {}

	RemindManager.Instance:Register(RemindName.NewTeam_MyTeam, BindTool.Bind(self.IsShowMyTeamRedPoint, self))
end

function NewTeamWGData:__delete()
	NewTeamWGData.Instance = nil
	self.now_goal_info = {}
    if CountDownManager.Instance:HasCountDown("new_team_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("new_team_invite_cd_time")
    end
	RemindManager.Instance:UnRegister(RemindName.NewTeam_MyTeam)
end

function NewTeamWGData:RecordTargetCountList()
	if not self.record_target_goal_count_list then
		self.record_target_goal_count_list = {}
    end

    for i, v in pairs(self.teamt_arget_info) do
		if not self.record_target_goal_count_list[v.team_type] then
			self.record_target_goal_count_list[v.team_type] = 0
		end
		self.record_target_goal_count_list[v.team_type] = self.record_target_goal_count_list[v.team_type] + 1
	end
end

function NewTeamWGData:GetIsZheDieBtn(team_type)
	if not self.record_target_goal_count_list[team_type] then
		return false, 1
	end
	return self.record_target_goal_count_list[team_type] > 1, self.record_target_goal_count_list[team_type]
end

function NewTeamWGData:InitMenuBtnList()
	if not self.menu_btn_list then
		self.menu_btn_list = {}
	end
	if not self.menu_btn_open_param then
		self.menu_btn_open_param = {}
	end
	local flag_list = {} --过滤掉重复的team_type
	for i, v in ipairs(self.teamt_arget_info) do
		if not flag_list[v.team_type] then
			flag_list[v.team_type] = true
			table.insert(self.menu_btn_list, v)
			if v.open_param and v.open_param ~= "" then
				local str = Split(v.open_param, "#")
				self.menu_btn_open_param[v.team_type] = {view_name = str[2], tab_index = str[1]}
			end
		end
	end
	flag_list = nil
end

--获取按钮列表，这里的列表是过滤掉重复的
--如果是平台，需要显示全部队伍
function NewTeamWGData:GetMenuBtnList(is_pingtai)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local goal_list = {}
	if is_pingtai then
		--手动构造一个全部队伍
		local all_goal = {}
		all_goal.team_type = -1
		all_goal.fb_mode = -1
		all_goal.fb_type = 0
		all_goal.role_min_level = COMMON_CONSTS.NoalGoalLimitMinLevel
		local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
		all_goal.role_max_level = top_user_level
		all_goal.scene_id = 0
		all_goal.team_type_name = Language.NewTeam.AllTeam
		table.insert(goal_list, 1, all_goal)

		--附近队伍
		-- local cur_map_goal = {}
		-- cur_map_goal.team_type = -2
		-- cur_map_goal.fb_mode = -2
		-- cur_map_goal.fb_type = 0
		-- cur_map_goal.role_min_level = COMMON_CONSTS.NoalGoalLimitMinLevel
		-- local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
		-- cur_map_goal.role_max_level = top_user_level
		-- cur_map_goal.scene_id = 0
		-- cur_map_goal.team_type_name = Language.NewTeam.CurMapTeam
		-- table.insert(goal_list, 2, cur_map_goal)
	end

	for i,v in ipairs(self.menu_btn_list) do
		local is_open = true
        if self.menu_btn_open_param[v.team_type] then
            is_open = FunOpen.Instance:GetFunIsOpened(self.menu_btn_open_param[v.team_type].tab_index)
		end

        if v.role_min_level <= role_level and is_open then
            if not GoalNotShowInViewType[v.team_type] then
				table.insert(goal_list, v)
			end
		end
	end

	return goal_list
end

function NewTeamWGData:GetListByTeamType(team_type)
	local ret_team_list = {}
	for i, v in ipairs(self.teamt_arget_info) do
		if v.team_type == team_type then
			table.insert(ret_team_list, v)
		end
	end
	return ret_team_list
end


function NewTeamWGData:GetTeamTargetCfg()
	return self.teamt_arget_info
end

function NewTeamWGData:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	for i,v in ipairs(self.teamt_arget_info) do
		if v.team_type == team_type and v.fb_mode == fb_mode then
			return v
		end
    end
    print_log("GetTeamTargetInfoByTypeAndMode 获取不到组队目标，team_type =", team_type, "fb_mode =", fb_mode)
	return {}
end


function NewTeamWGData:SetNowGoalInfoByIndex(index)
	if 0 == SocietyWGData.Instance:GetIsInTeam() then
		self.now_goal_info = self.teamt_arget_info[index]
        self:SetTeamTypeAndMode(self.now_goal_info.team_type, self.now_goal_info.fb_mode)
        local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
        local top_lv = self.now_goal_info.role_max_level or top_user_level
		self:SetTeamLimitLevel(self.now_goal_info.role_min_level, top_lv)
	end
end

function NewTeamWGData:SetNowGoalInfoByTypeAndMode(team_type, fb_mode)
	for i,v in ipairs(self.teamt_arget_info) do
		if v.team_type == team_type and v.fb_mode == fb_mode then
            self:SetTeamTypeAndMode(v.team_type, v.fb_mode)
            local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
            local top_lv = v.role_max_level or top_user_level
			self:SetTeamLimitLevel(v.role_min_level, top_lv)
		end
	end
end

function NewTeamWGData:GetJumpViewNameAndIndex(team_type, fb_mode)
	local goal_info = self:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	local view_name, tab_index
	if not IsEmptyTable(goal_info) then
		local str = Split(goal_info.open_param, "#")
		view_name = str[2]
		tab_index = str[1]
	end
	return view_name, tab_index
end

function NewTeamWGData:SetPingTaiJumpYuanGuIndex(fb_mode)
	self.jump_yuan_gu_index = fb_mode
end

function NewTeamWGData:GetPingTaiJumpYuanGuIndex()
	return self.jump_yuan_gu_index
end

function NewTeamWGData:GetNowGoalInfo()
	return self.now_goal_info
end

function NewTeamWGData:SetIsMatching(flag)
	self.is_matching = flag == 1
end

function NewTeamWGData:GetIsMatching()
	return self.is_matching
end

function NewTeamWGData:IsGuideMatching(guide_id)
	if self.is_matching and (guide_id == 7 and (self.matching_team_type == GoalTeamType.Exp_DuJieXianZhou or
		self.matching_team_type == GoalTeamType.Exp_FuMoZhanChuan) or
		guide_id == 22 and self.matching_team_type == GoalTeamType.YuanGuXianDian) then
	return true
	end
	return false
end

-- 附近队伍
function NewTeamWGData:SetNearTeamList(protocol)
	self.near_team_list = {}
	self.near_team_list.team_type = protocol.team_type
	self.near_team_list.teamfb_mode = protocol.teamfb_mode
	self.near_team_list.count = protocol.count
	self.near_team_list.team_list = protocol.team_list
	--print_error("设置附近队伍 ",self.near_team_list.team_list)
end

-- function NewTeamWGData:GetNearTeamList()
-- 	return self.near_team_list
-- end

--获取目标队伍(平台Index)
function NewTeamWGData:GetTargetTeamList()
	local my_level = RoleWGData.Instance.role_vo.level
	local is_in_team = SocietyWGData.Instance:GetIsInTeam() 	--是否在队伍里
    local my_team_data = nil 								--自己队伍的数量
	local ret_team_list = {}								--返回的队伍列表
	local min_fit_level, max_fit_level = self:GetCurFiltrateLevelLimit()
	local my_team_index = SocietyWGData.Instance:GetTeamIndex()
	if not self.near_team_list or not self.near_team_list.team_list then return ret_team_list end

	for k,v in pairs(self.near_team_list.team_list) do
		if v.team_index == my_team_index then
			if is_in_team then
				my_team_data = v  --table.remove(self.near_team_list.team_list,k)
			end
		else
			if min_fit_level and max_fit_level then
				--只显示队伍人数小于MaxTeamCount的队伍, 和等級筛选下的队伍
				if v.cur_member_num < MaxTeamCount and 
						--(my_level >= v.min_limit and my_level <= v.max_limit) and 
						(my_level >= min_fit_level and my_level <= max_fit_level) and --这个条件目前没影响
						(min_fit_level <= v.min_limit and max_fit_level >= v.max_limit) then
					table.insert(ret_team_list, v)
				end
			else
				--只显示队伍人数小于MaxTeamCount的队伍, 和等級筛选下的队伍
				if v.cur_member_num < MaxTeamCount and (my_level >= v.min_limit and my_level <= v.max_limit) then
					table.insert(ret_team_list, v)
				end
			end

		end
	end

	local sortfunction = function(a,b)
		local order_a = 100000
		local order_b = 100000
		--1.队伍人数
		if a.cur_member_num < MaxTeamCount then
			order_a = order_a + 10000
		end
		if b.cur_member_num < MaxTeamCount then
			order_b = order_b + 10000
		end
		--2.可加入情况
			--(1)等级限制
			--if my_level > a.min_limit and my_level < a.min_limit then
			--	order_a = order_a + 1000
			--end
			--if my_level > b.max_limit and my_level < b.max_limit then
			--	order_b = order_b + 1000
			--end
			--(2)是直接进入还是申请入队
			if a.is_must_check == 1 then
				order_a = order_a + 100
			end
			if b.is_must_check == 1 then
				order_b = order_b + 100
			end
		--3.列表顺序
			--(1)team_type
		if a.team_type > b.team_type then
			order_b = order_b + 10
		elseif a.team_type < b.team_type then
			order_a = order_a + 10
		else
				--(2)fb_mode
			if a.team_fb_mode > b.team_fb_mode then
				order_b = order_b + 1
			elseif a.team_fb_mode < b.team_fb_mode then
				order_a = order_a + 1
			end
		end
		return order_a > order_b
	end

	table.sort( ret_team_list, sortfunction )
    if my_team_data ~= nil and my_team_data.cur_member_num < MaxTeamCount then --如果自己有队伍并且队伍成员没满，加到第一 --and self:GetIsMatching()
        table.insert(ret_team_list, 1, my_team_data)
    end
	return ret_team_list
end

--获取附近队伍(附近队伍Index)
function NewTeamWGData:GetNearTeamList()
	--local team_list = __TableCopy(self.near_team_list)
	local my_level = RoleWGData.Instance.role_vo.level
	local is_in_team = SocietyWGData.Instance:GetIsInTeam() 	--是否在队伍里
    local my_team_data = nil 								--自己队伍的数量
	local ret_team_list = {}								--返回的队伍列表

	local my_team_index = SocietyWGData.Instance:GetTeamIndex()
	for k,v in pairs(self.near_team_list.team_list) do
		if v.team_index == my_team_index then
			if is_in_team then
				if v.cur_member_num < MaxTeamCount then
					my_team_data = v--table.remove(self.near_team_list.team_list,k)
				end
			end
		else
			--附近队伍，显示全，直接插入,筛选组队未满的队伍
			if v.cur_member_num < MaxTeamCount then
				table.insert(ret_team_list, v)
			end
		end
	end

	local sortfunction = function(a,b)
		local order_a = 100000
		local order_b = 100000
		--1.队伍人数
		if a.cur_member_num < MaxTeamCount then
			order_a = order_a + 10000
		end
		if b.cur_member_num < MaxTeamCount then
			order_b = order_b + 10000
		end
		--2.可加入情况
			--(1)等级限制
			if my_level > a.min_limit and my_level < a.max_limit then
				order_a = order_a + 1000
			end
			if my_level > b.min_limit and my_level < b.max_limit then
				order_b = order_b + 1000
			end
			--(2)是直接进入还是申请入队
			if a.is_must_check == 1 then
				order_a = order_a + 100
			end
			if b.is_must_check == 1 then
				order_b = order_b + 100
			end
		--3.列表顺序
			--(1)team_type
		if a.team_type > b.team_type then
			order_b = order_b + 10
		elseif a.team_type < b.team_type then
			order_a = order_a + 10
		else
			--(2)fb_mode
			if a.team_fb_mode > b.team_fb_mode then
				order_b = order_b + 1
			elseif a.team_fb_mode < b.team_fb_mode then
				order_a = order_a + 1
			end
		end
		return order_a > order_b
	end

	table.sort( ret_team_list, sortfunction )

	--策划说附近队伍，不显示自己的队伍了 2019/9/17
	--2019/10/18 又要显示
    if my_team_data ~= nil then --如果自己有队伍并且队伍成员没满，加到第一
        table.insert(ret_team_list, 1, my_team_data)
    end
	return ret_team_list
end


function NewTeamWGData:GetCurTeamLimitLevel()
	local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
	for k,v in pairs(self.teamt_arget_info) do
        if v.team_type == self.team_type and v.fb_mode == self.fb_mode then
            local top_lv = v.role_max_level or top_user_level
			return v.role_min_level, top_lv
		end
	end

	return COMMON_CONSTS.NoalGoalLimitMinLevel, top_user_level
end

function NewTeamWGData:GetPingTaiCurTeamLimitLevel()
    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
	for k,v in pairs(self.teamt_arget_info) do
        if v.team_type == self.pingtai_team_type and v.fb_mode == self.pingtai_fb_mode then
            local top_lv = v.role_max_level or top_user_level
			return v.role_min_level, top_lv
		end
	end
	return COMMON_CONSTS.NoalGoalLimitMinLevel, top_user_level
end
-- 设置队伍信息
function NewTeamWGData:SetData(protocol)
    --print_error("protocol-9100测试打印",protocol.team_type, protocol.teamfb_mode)
	self:SetTeamLimitLevel(protocol.limit_level, protocol.max_level)
	self.team_index = protocol.team_index
 	self.to_team_type = protocol.to_team_type
    self.to_teamfb_mode = protocol.to_teamfb_mode
    self:SetTeamTypeAndMode(protocol.team_type, protocol.teamfb_mode)
end

function NewTeamWGData:GetTeamIndex()
	return self.team_index
end

function NewTeamWGData:SetNearRoleList(protocol)
	local role_list = __TableCopy(protocol.role_list)
	local member_list = SocietyWGData.Instance:GetTeamMemberList()
	self.near_role_list = {}
	local my_role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if IsEmptyTable(member_list) then
		for k,v in pairs(protocol.role_list) do
			if v.orig_uid == my_role_id then
				table.remove(role_list,k)
				break
			end
		end
		self.near_role_list = role_list
		return
	end

	for k = #protocol.role_list,1,-1 do
		for i,j in pairs(member_list) do
			if j.orgin_role_id == protocol.role_list[k].orig_uid then
				table.remove(role_list,k)
			end
		end
	end

	self.near_role_list = role_list
end

function NewTeamWGData:GetFriendList()
	local list = __TableCopy(SocietyWGData.Instance:GetFriendList2())
	local member_list = __TableCopy(SocietyWGData.Instance:GetTeamMemberList())
	for k,v in pairs(list) do
		for i,j in ipairs(member_list) do
			if v.user_id == j.role_id then
				table.remove(list,k)
			end
		end
	end
	return list
end

function NewTeamWGData:GetNearRoleList()
	return self.near_role_list
end

function NewTeamWGData:GetReplyInfo()
	return self.reply_list
end

function NewTeamWGData:GetTeamTypeAndMode()
	return self.team_type, self.fb_mode
end

function NewTeamWGData:GetPingTaiTeamTypeAndMode()
	return self.pingtai_team_type , self.pingtai_fb_mode
end

--设置队伍类型和模式
function NewTeamWGData:SetTeamTypeAndMode(team_type, fb_mode)
	if self.team_type ~= team_type or self.fb_mode ~= fb_mode then
		self.team_type = team_type
        self.fb_mode = fb_mode
		GlobalEventSystem:Fire(TeamEventType.TEAM_TYPE_CHANGE)
	end
end

--设置平台队伍类型和模式, 不可影响我的队伍里面的类型和模式， 反过来我的队伍可以影响它
function NewTeamWGData:SetPingTaiTeamTypeAndMode(team_type, fb_mode)
	if self.pingtai_team_type ~= team_type or self.pingtai_fb_mode ~= fb_mode then
		self.pingtai_team_type = team_type
		self.pingtai_fb_mode = fb_mode
		--GlobalEventSystem:Fire(TeamEventType.TEAM_TYPE_CHANGE)
	end
end

function NewTeamWGData:SetMatchingTeamTypeAndMode(team_type, fb_mode)
	if self.matching_team_type ~= team_type or self.matching_fb_mode ~= fb_mode then
		self.matching_team_type = team_type
		self.matching_fb_mode = fb_mode
	end
end

function NewTeamWGData:GetMatchingTeamTypeAndMode()
	return self.matching_team_type, self.matching_fb_mode
end

function NewTeamWGData:GetTeamLimitLevel()
	return self.min_level, self.max_level
end

function NewTeamWGData:SetTeamLimitLevel(min_level, max_level)
	self.min_level = min_level
	self.max_level = max_level
end

function NewTeamWGData:GetTeamFbName()
	if 0 == self.team_type then
		local scene_cfg_name = Scene.Instance:GetSceneName()
		return scene_cfg_name
	end
	for i,v in ipairs(self.teamt_arget_info) do
		if v.team_type == self.team_type and v.fb_mode == self.fb_mode then
			return v.team_type_name
		end
	end
	return ""
end

function NewTeamWGData:GetExpAdd()
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        local count = CrossTeamWGData.Instance:GetTeamMemberCount()
        return count - 1 > 0 and count - 1 or 0
    end

	local num = 0
	local index_list = {}
	local temp_data = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()
	if temp_data and temp_data.has_get_first_rward == 1 then
		return 2
	end
	local data_list = SocietyWGData.Instance:GetTeamMemberList() or {}
	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	local scene_id = Scene.Instance:GetSceneId()
    for i,v in ipairs(data_list) do
		if v.orgin_role_id ~= my_uid and v.is_robert ~= 1 and not v.is_in_different_country then --是队友，不是机器人，在同一个国
			if v.scene_id == scene_id and (1 == v.is_online or 1 == v.is_hang) then
				num = num + 1
				index_list[i] = true
			else
				index_list[i] = false
			end
		else
			index_list[i] = true
		end
	end
	return num, index_list
end


--获取经验加成，不判断场景，用于组队准备面板
function NewTeamWGData:GetExpAddIgnoreScene()
	local num = 0
	local index_list = {}
	local temp_data = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()
	if temp_data and temp_data.has_get_first_rward == 1 then
		return 2
	end
	local data_list = SocietyWGData.Instance:GetTeamMemberList() or {}
	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	local scene_id = Scene.Instance:GetSceneId()
	for i,v in ipairs(data_list) do
		if v.orgin_role_id ~= my_uid and v.is_robert ~= 1 then
			if 1 == v.is_online or 1 == v.is_hang then
				num = num + 1
				index_list[i] = true
			else
				index_list[i] = false
			end
		else
			index_list[i] = true
		end
	end
	return num, index_list
end


function NewTeamWGData:SetExpFirstFirstEnter(mark)
	self.exp_enter_first = mark
end

function NewTeamWGData:GetExpFirstFirstEnter()
	return self.exp_enter_first
end

-- 真正进入的副本名称
function NewTeamWGData:GetTeamFbNameByToTeamTypeAndMode()
	local target_info = self:GetTeamTargetInfoByTypeAndMode(self.to_team_type, self.to_teamfb_mode)
	return target_info.team_type_name and target_info.team_type_name or ""
end

function NewTeamWGData:SetRoleInfo( protocol )
	self.role_appearance.role_id = protocol.role_id
	self.role_appearance.appearance = protocol.appearance
	self.role_appearance.role_diy_appearance = protocol.role_diy_appearance
	self.role_appearance.sex = protocol.sex
	self.role_appearance.prof = protocol.prof
	self.role_appearance.updateattr = protocol.updateattr
end

function NewTeamWGData:GetRoleInfo()
	return self.role_appearance
end

function NewTeamWGData:GetTeamMemberList()
	local member_list = __TableCopy(SocietyWGData.Instance:GetTeamMemberList())
	local role_vo = RoleWGData.Instance.role_vo
	if 0 == #member_list then
		member_list[1] = {name = role_vo.name, level = role_vo.level, role_id = role_vo.role_id, prof = role_vo.prof, is_match = false, is_leader = 0, vip_level = role_vo.vip_level}
	end

	if self:GetIsMatching() then
		for i = #member_list + 1, MaxTeamCount do
			if i >= #member_list + 1 then
				member_list[i] = {name = "", level = 0, role_id = 0, prof = 0, is_match = true, is_leader = 0}
			else
				member_list[i].is_match = false
			end
		end
	end

	local sort_team = function (a, b)
		local order_a = 100000
		local order_b = 100000
		if 1 == a.is_leader then
			order_a = order_a + 10000
		elseif 1 == b.is_leader then
			order_b = order_b + 10000
		end

		if a.role_id == RoleWGData.Instance:InCrossGetOriginUid() then
			order_a = order_a + 1000
		elseif b.role_id == RoleWGData.Instance:InCrossGetOriginUid() then
			order_b = order_b + 1000
		end

		return order_a > order_b
	end

	table.sort(member_list, sort_team)

	local change_index = {1,2,3,4,5}
	self.old_team_info = __TableCopy(member_list)
	return change_index,member_list
end

function NewTeamWGData:ClearTeamInfo()
	self.old_team_info = {}
end

function NewTeamWGData:ClearTarget( id )
	local count = 0
	for k,v in pairs(self.old_team_info) do
		if v and v.role_id == id then
			count = k
			break
		end
	end
	if count ~= 0 then
		table.remove(self.old_team_info,count)
	end
end

function NewTeamWGData:GetNameList()
	local role_vo = RoleWGData.Instance.role_vo
	local list = __TableCopy(self.old_team_info)
	for i=#list,1,-1 do
		if list[i].role_id == role_vo.role_id then
			table.remove(list, i)
		end
	end
	return list
end

function NewTeamWGData:GetTeamGoalSpecial(is_need_all)
	local goal_list = {}
	local specail_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()

	if is_need_all then
		--手动构造一个全部队伍
		local all_goal = {}
		all_goal.team_type = -1
		all_goal.fb_mode = -1
		all_goal.fb_type = 0
		all_goal.role_min_level = COMMON_CONSTS.NoalGoalLimitMinLevel
		local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
		all_goal.role_max_level = top_user_level
		all_goal.scene_id = 0
		all_goal.team_type_name = Language.NewTeam.AllTeam
		table.insert(goal_list, 1, all_goal)
	end

    for i,v in ipairs(self.teamt_arget_info) do
		if v.role_min_level <= role_level then
			if v.fb_type ~= FUBEN_TYPE.HIGH_TEAM_EQUIP then
				if v.fb_type ~= FUBEN_TYPE.FBCT_WUJINJITAN or not WuJinJiTanWGData.Instance:IsLingHunGuangChang() then
				    table.insert(goal_list, v)
                end
			else
				table.insert(specail_list,v)
			end
		end
	end
	
	return goal_list, specail_list
end

function NewTeamWGData:GetSetCurSelectTarget( cell )
	if cell then
		self.cur_select_target = cell
		return
	end
	return self.cur_select_target
end

function NewTeamWGData:GetMatchStateInfo()
	local t = {}
	t.team_type = self.team_type
	t.teamfb_mode = self.fb_mode
	t.is_matching = self.is_matching and 1
	return t
end

function NewTeamWGData:GetIsNoGoal(team_type)
	return team_type == 0
end

function NewTeamWGData:GetTeamMatchPreTime(cur_value)
	local cfg = self.match_time_cfg
	local is_max = false
	local ret_value = cfg[1].match_time
	for i = #cfg, 1, -1 do
		if cur_value > cfg[i].match_time then
			ret_value = cfg[i + 1 > #cfg and #cfg or i + 1].match_time
			break
		end
	end
	if cur_value > cfg[#cfg].match_time then
		is_max = true
	end
	return ret_value, is_max
end

function NewTeamWGData:GetTimesByTeamType(team_type)
    local enter_times = 0
    local xiezhu_times = 0
    local total_reward_count = 0
	local total_xiezhu_count = 0
    if team_type == GoalTeamType.Exp_DuJieXianZhou then 			-- 渡劫仙舟
        enter_times = FuBenPanelWGCtrl.Instance.view:GetFBEnterTimes()
        total_reward_count = FuBenPanelWGCtrl.Instance.view:GetExpTotalCount()
	elseif team_type == GoalTeamType.QingYuanFb then				-- 情缘副本
		enter_times = MarryWGData.Instance:GetEnterTimes()
		total_reward_count = MarryWGData.Instance:GetTotalTimes()
	elseif team_type == GoalTeamType.YuanGuXianDian then			-- 远古仙殿
        enter_times = TeamEquipFbWGData.Instance:GetHTEFbEnteredTimes()
        total_reward_count = TeamEquipFbWGData.Instance:GetHTEFbTotalTimes()
	elseif team_type == GoalTeamType.ZhuShenTa then 				-- 诛神塔
        enter_times = FuBenPanelWGData.Instance:GetZhuShenTaFbDayEnterTimes()
        total_reward_count = FuBenPanelWGData.Instance:GetZhuShenTaFbDayTotalCount()
	elseif team_type == GoalTeamType.Exp_FuMoZhanChuan then 		-- 伏魔战船
        enter_times = FuBenPanelWGCtrl.Instance.view:GetFBEnterTimes()
        total_reward_count = FuBenPanelWGCtrl.Instance.view:GetExpTotalCount()
	elseif team_type == GoalTeamType.PengLaiTanBao then 			-- 蓬莱探宝
		local wabao_times = BootyBayWGData.Instance:GettDailyWaBaoRemainCount()
		local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
		local is_accepted = BootyBayWGData.Instance:GetIsAcceptWaBaoTask()
		local times = is_accepted and other_cfg.task_wabao_times - wabao_times or other_cfg.task_wabao_times
		enter_times = other_cfg.task_wabao_times - times
		total_reward_count = other_cfg.task_wabao_times
	elseif team_type == GoalTeamType.ManHuangGuDian then 			-- 蛮荒古殿
		enter_times = ManHuangGuDianWGData.Instance:GetManHuangEnterTimes()
		total_reward_count = ManHuangGuDianWGData.Instance:GetTotalTimes()
	elseif team_type == GoalTeamType.FbControlBeastsType then --幻兽副本
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbControlBeastsType)
		enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbControlBeastsType)
		total_reward_count = team_type_cfg.max_times or 0
	elseif team_type == GoalTeamType.FbBeautyType then       --女神副本
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbBeautyType)
		enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbBeautyType)
		total_reward_count = team_type_cfg.max_times or 0
	elseif team_type == GoalTeamType.FbWuHunType then       --武魂副本
		local team_type_cfg =  FuBenTeamCommonBossWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbWuHunType)
		enter_times = FuBenTeamCommonBossWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbWuHunType)
		total_reward_count = team_type_cfg.max_times or 0
	elseif team_type == GoalTeamType.FbRuneTowerType then       --武魂副本
		local team_type_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenTeamTypeCfg(GoalTeamType.FbRuneTowerType)
		enter_times = FuBenTeamCommonTowerWGData.Instance:GetFuBenEnterTimes(GoalTeamType.FbRuneTowerType)
		total_reward_count = team_type_cfg.max_times or 0
	end

	return enter_times, total_reward_count
end

function NewTeamWGData:SetHasXieZhuTimesMax(times)
	self.cur_xiezhu_times_max = times
end

--设置已经协助的次数
function NewTeamWGData:SetHasXieZhuTimes(times)
	self.cur_xiezhu_times = times
end

--获取结算面板目标协助次数
function NewTeamWGData:GetXieZhuTimesByTeamWinType(team_type)
	local total_xiezhu_times = 0
	local cur_xiezhu_times_max = 0
	if not GoalHasXieZhuType[team_type] then
		return 0, 0
	end

	total_xiezhu_times = self.other_cfg.help_times
	cur_xiezhu_times_max = self.cur_xiezhu_times_max
	return cur_xiezhu_times_max, total_xiezhu_times
end

--获取目标协助次数
function NewTeamWGData:GetXieZhuTimesByTeamType(team_type)
    local xiezhu_times = 0
	local total_xiezhu_times = 0
	if not GoalHasXieZhuType[team_type] then
		return 0, 0
	end

	xiezhu_times = self.cur_xiezhu_times
	total_xiezhu_times = self.other_cfg.help_times
	return xiezhu_times, total_xiezhu_times
end

--获取目标协助奖励
function NewTeamWGData:GetXieZhuRewardValue(team_type)
	if not GoalHasXieZhuType[team_type] then
		return 0
	end
	return self.other_cfg.help_honor
end

function NewTeamWGData:SetRoomInfo(protocol)
	self.room_info = {}
	self.room_info.room_index = protocol.room_index
	self.room_info.team_type = protocol.team_type
	self.room_info.teamfb_mode = protocol.teamfb_mode
	self.room_info.teamfb_enter_timestamp = protocol.teamfb_enter_timestamp
	self.room_info.member_count = protocol.member_count
	self.room_info.room_member_list = protocol.room_member_list
end

function NewTeamWGData:ClearRoomInfo()
	self.room_info = {}
end

function NewTeamWGData:GetRoomIndex()
	if not self.room_info or not self.room_info.room_index then
		return 0
	end
	return self.room_info.room_index
end

function NewTeamWGData:GetRoomMemberList()
	local member_list = {}
	if not self.room_info or not self.room_info.room_member_list then
		return member_list
	end
	for i, v in pairs(self.room_info.room_member_list) do
		table.insert(member_list , v)
	end
	local leader_data = {}
	for i,v in ipairs(member_list) do
		if v.is_leader == 1 then
			leader_data = v
			table.remove(member_list, i)
			break
		end
	end
	if not IsEmptyTable(leader_data) then
		table.insert(member_list, 1, leader_data)
	end
	return member_list
end

function NewTeamWGData:GetRoomExpAdd()
	local num = 0
	local data_list = self:GetRoomMemberList() or {}
	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	local scene_id = Scene.Instance:GetSceneId()
    for i,v in ipairs(data_list) do
		if v.role_original_id ~= my_uid or v.is_robert == 1 then --机器人也算经验加成
			num = num + 1
		end
	end
	return num
end

function NewTeamWGData:IsRoomMemberAllRobot()
    local robot_num = 0
    local room_list = self:GetRoomMemberList()
    local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
    for k, v in pairs(room_list) do
        if v.role_original_id ~= role_id then
            if v.is_robert == 1 then
                robot_num = robot_num + 1
            end
        end
    end
	return robot_num == 2
end

function NewTeamWGData:GetPrepareTimeStamp()
	if self.room_info and self.room_info.teamfb_enter_timestamp then
		return self.room_info.teamfb_enter_timestamp
	end
	return nil
end

--获取自己的准备状态
function NewTeamWGData:GetSelfPrepareState()
	if nil == self.room_info or IsEmptyTable(self.room_info.room_member_list) then return false end
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if nil == role_id then return false end
	for i, v in pairs(self.room_info.room_member_list) do
		if v.role_original_id == role_id then
			return v.fbroom_ready == 1
		end
	end

	return false
end

-- 获取队员是否都已准备
function NewTeamWGData:GetMemberPrepareState()
	if nil == self.room_info or IsEmptyTable(self.room_info.room_member_list) then return false end
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if nil == role_id then return false end
	for i, v in pairs(self.room_info.room_member_list) do
		if v.role_original_id ~= role_id and v.fbroom_ready == 0 then
			return false
		end
	end
	return true
end

--设置平台筛选等级
function NewTeamWGData:SetCurFiltrateLevelLimit(min_level , max_level, is_fire)
	self.min_fit_level = min_level
	self.max_fit_level = max_level
	if is_fire then
		GlobalEventSystem:Fire(OtherEventType.SetTeamFiltrateLevel)
	end
end

--获取平台筛选等级
function NewTeamWGData:GetCurFiltrateLevelLimit()
	return self.min_fit_level, self.max_fit_level
end

function NewTeamWGData:GetDefaultGoalByTeamType(team_type)
	if team_type == GoalTeamType.YuanGuXianDian then
		local max_layer = TeamEquipFbWGData.Instance:GetEquipFbMaxCount()
		local cur_layer = TeamEquipFbWGData.Instance:GetCurFbIndex()
		local jump_layer = MathClamp(cur_layer, 1, max_layer)
		return jump_layer
	end
	return 1
end

function NewTeamWGData:GetIsInRoomScene(scene_type)
	local target_type = scene_type or Scene.Instance:GetSceneType()
	return RoomSceneType[target_type] or false
end

--设置成员所在场景信息
function NewTeamWGData:SetTeamMemberSceneInfo(protocol)
	self.team_member_scene_info = {}
	for i = 1, #protocol.member_list do
		self.team_member_scene_info[i] = protocol.member_list[i]
	end
end

function NewTeamWGData:GetTeamMemberSceneInfo()
	return self.team_member_scene_info
end
function NewTeamWGData:GetOtherTeamMemberSceneInfo()
	local list = {}
	if not self.team_member_scene_info then return list end
	local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
	for i, v in ipairs(self.team_member_scene_info) do
		if v.uid > 0 and v.uid ~= my_uid then
			table.insert(list, v)
		end
	end
	return list
end

function NewTeamWGData:GetLevelStr(level_param)
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(level_param)
	if is_vis then
		return string.format(Language.Common.LevelFeiXian, level)
	else
		return string.format(Language.Common.LevelNormal, level)
	end
end

function NewTeamWGData:GetTargetIsRoomMember(orgin_role_id)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	for i, v in pairs(self.room_info.room_member_list) do
		if v.role_original_id ~= role_id and v.role_original_id == orgin_role_id then
			return true
		end
	end
	return false
end

----合并
--获取合并列表
function NewTeamWGData:GetHeBingList()
	return self.hebing_list
end

--获取合并数量
function NewTeamWGData:GetHeBingCount()
	return #self.hebing_list
end

--添加合并消息
function NewTeamWGData:AddHeBingInfo(protocol)
	local hebing_item = {}
	hebing_item.req_role_id = protocol.req_role_id
	hebing_item.req_role_name = protocol.req_role_name
	hebing_item.req_role_vip_level = protocol.req_role_vip_level
	hebing_item.req_role_level = protocol.req_role_level
	hebing_item.req_role_sex = protocol.req_role_sex
	hebing_item.req_role_prof = protocol.req_role_prof
	hebing_item.req_role_photoframe = protocol.req_role_photoframe
	hebing_item.avatar_key_big = protocol.avatar_key_big
	hebing_item.avatar_key_small = protocol.avatar_key_small
	hebing_item.req_role_capability = protocol.req_role_capability
	hebing_item.relation_flag = protocol.relation_flag

	AvatarManager.Instance:SetAvatarKey(hebing_item.req_role_id, hebing_item.avatar_key_big, hebing_item.avatar_key_small)

	self:RemoveHeBingItem(hebing_item.req_role_id)
	table.insert(self.hebing_list, hebing_item)
	if #self.hebing_list > COMMON_CONSTS.TEAM_HEBING_COUNT then
		table.remove(self.hebing_list, 1)
	end
end

--移除合并消息
function NewTeamWGData:RemoveHeBingItem(uid)
	local remove_success = false
	for i, v in ipairs(self.hebing_list) do
		if v.req_role_id == uid then
			table.remove(self.hebing_list, i)
			remove_success = true
			break
		end
	end

	return remove_success
end

--清空合并消息
function NewTeamWGData:ClearHeBing()
	self.hebing_list = {}
end

function NewTeamWGData:RestHeBing()
	self:ClearHeBing()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, 0)
	NewTeamWGCtrl.Instance:CloseHeBingView()
end

--showindex的时候执行的callback，执行1次就清除
function NewTeamWGData:SetOpenTeamCallBack(callback)
	table.insert(self.team_open_callback, callback)
end

function NewTeamWGData:GetOpenTeamCallBack()
	local callback_list = self.team_open_callback
	self.team_open_callback = {}
	return callback_list
end


function NewTeamWGData:GetIsTeamRobort(uid)
	if self:GetIsInRoomScene() and uid == 0 then
		return true
	end
	return false
end

function NewTeamWGData:IsShowMyTeamRedPoint()
	local is_leader = SocietyWGData.Instance:GetIsTeamLeader()
	if not is_leader then
		return 0
	end
	local req_count = SocietyWGData.Instance:GetReqTeamListSize()
	return req_count > 0 and 1 or 0
end

-- 获取服务器最高世界等级 + 额外的等级限制
function NewTeamWGData:GetTopWordLevel()
	local top_user_level = RankWGData.Instance:GetTopWordLevel()
	local limit_extra_level = self.other_cfg.limit_extra_level
	local level = top_user_level + limit_extra_level
	return level
end

-- 获取配置的等级
function NewTeamWGData:GetTopLevelByTeamType(team_type)
    team_type = team_type or 0
	for i,v in ipairs(self.teamt_arget_info) do
		if v.team_type == team_type then
			return v.role_max_level
		end
	end
	return COMMON_CONSTS.MaxRoleLevel
end

-- 缓存邀请人员的cd列表
function NewTeamWGData:AddCacheCDList(role_id, time)
    if not self.cache_invite_cd_list then
        self.cache_invite_cd_list = {}
    end
    if CountDownManager.Instance:HasCountDown("new_team_invite_cd_time") then
        CountDownManager.Instance:RemoveCountDown("new_team_invite_cd_time")
    end
    CountDownManager.Instance:AddCountDown("new_team_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
    BindTool.Bind(self.CheckInviteTime, self), 40 + TimeWGCtrl.Instance:GetServerTime())
    time = time or 30
    self.cache_invite_cd_list[role_id] = time
    NewTeamWGCtrl.Instance:FlushTextInvite()
end

function NewTeamWGData:GetCacheCDByRoleid(role_id)
    if not self.cache_invite_cd_list then
        return 0
    end
    return self.cache_invite_cd_list[role_id] or 0
end

function NewTeamWGData:CheckInviteTime()
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("new_team_invite_cd_time")
    else
        CountDownManager.Instance:AddCountDown("new_team_invite_cd_time", BindTool.Bind(self.UpdateInviteTime,self), 
        BindTool.Bind(self.CheckInviteTime,self), 40 + TimeWGCtrl.Instance:GetServerTime())
    end
end

-- 缓存邀请人员的cd列表
function NewTeamWGData:UpdateInviteTime(elapse_time, total_time)
    if IsEmptyTable(self.cache_invite_cd_list) then
        CountDownManager.Instance:RemoveCountDown("new_team_invite_cd_time")
        return
    end

    for k, v in pairs(self.cache_invite_cd_list) do
        self.cache_invite_cd_list[k] = v - 1
        if v <= 0 then
            self.cache_invite_cd_list[k] = nil
            table.remove(self.cache_invite_cd_list, k)
        end
        NewTeamWGCtrl.Instance:FlushTextInvite()
    end
end

function NewTeamWGData:OnSCTeamInviteChannelChat(protocol)
    if Scene.Instance:GetIsOpenCrossViewByScene() then --跨服组队中，屏蔽原服邀请
        return
    end

	-- 2022.5.27 拒绝所有人组队邀请（原 拒绝陌生人）
	if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.REFUSE_STRANGER_TEAM) then
		-- local is_friend = SocietyWGData.Instance:CheckIsFriend(protocol.inviter)
		-- if not is_friend then
			return
		-- end
	end

	if SocietyWGData.Instance:GetIsInTeam() == 1 or #self.quick_invite_team_list > 20 then
		return
	end

	local enter_times, total_reward_count = NewTeamWGData.Instance:GetTimesByTeamType(protocol.team_type)
	local remain_times = total_reward_count - enter_times
	--同性别邀请进入情缘副本屏蔽
	-- if GoalTeamType.QingYuanFb == protocol.team_type then
	-- 	local main_role = Scene.Instance:GetMainRole()
	-- 	local leader_info = protocol.leader_info
	-- 	if main_role.vo.sex == leader_info.sex then
	-- 		return
	-- 	end
	-- end
	if remain_times > 0 or GoalTeamType.PengLaiTanBao == protocol.team_type then
		table.insert(self.quick_invite_team_list, NewTeamWGData.GetQuickInviteTeamInfo(protocol))
		NewTeamWGCtrl.Instance:OpenQuickInviteTeam()
	end
end

function NewTeamWGData:SetTeamInviteMatch(protocol)
    if Scene.Instance:GetIsOpenCrossViewByScene() then --跨服组队中，屏蔽原服邀请
        return
    end

	local invite_match_info = protocol.invite_match_info
	local enter_times, total_reward_count = NewTeamWGData.Instance:GetTimesByTeamType(invite_match_info.team_type)
	local remain_times = total_reward_count - enter_times
	if remain_times > 0 then
		self.quick_invite_match_info = invite_match_info
		NewTeamWGCtrl.Instance:OpenQuickInviteMatch()
	end
end

-- 修改，指定邀请
function NewTeamWGData:GetQuickInviteFromUserTransmit(team_info)
    if Scene.Instance:GetIsOpenCrossViewByScene() then  --跨服组队中，屏蔽原服邀请
        return
    end
	
	-- 2022.5.27 拒绝所有人组队邀请（原 拒绝陌生人）
	if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.REFUSE_STRANGER_TEAM) then
		-- local is_friend = SocietyWGData.Instance:CheckIsFriend(protocol.inviter)
		-- if not is_friend then
			return
		-- end
	end

	if SocietyWGData.Instance:GetIsInTeam() == 1 or #self.specific_invite_team_list > 20 then
		return
	end

	local info = NewTeamWGData.GetQuickInviteTeamInfo(team_info)
	--同性别邀请进入情缘副本屏蔽
	-- if GoalTeamType.QingYuanFb == info.team_type then
	-- 	local main_role = Scene.Instance:GetMainRole()
	-- 	local sex = team_info.leader_info and team_info.leader_info.sex or team_info.inviter_sex or 0
	-- 	if main_role.vo.sex == sex then
	-- 		return
	-- 	end
	-- end

	table.insert(self.specific_invite_team_list, info)
	NewTeamWGCtrl.Instance:OpenSpecificInviteTeam()
end

function NewTeamWGData.GetQuickInviteTeamInfo(msg_info)
	local t = {}
	t.inviter = msg_info.leader_info and msg_info.leader_info.uid or msg_info.inviter or 0
	t.inviter_name = msg_info.leader_info and msg_info.leader_info.name or msg_info.inviter_name or ""
	t.team_type = msg_info.team_type or 0
    t.team_fb_mode = msg_info.team_fb_mode or msg_info.teamfb_mode or 0
    t.leader_scene_id = msg_info.leader_scene_id
	return t
end

function NewTeamWGData:GetQuickInviteTeamTopTeamInfo()
	return not IsEmptyTable(self.quick_invite_team_list) and table.remove(self.quick_invite_team_list, 1) or nil
end

function NewTeamWGData:GetSpecificInviteTeamTopTeamInfo()
	return not IsEmptyTable(self.specific_invite_team_list) and table.remove(self.specific_invite_team_list, 1) or nil
end

function NewTeamWGData:ClearQuickInviteTeamList()
	self.quick_invite_team_list = {}
	self.specific_invite_team_list = {}
end

function NewTeamWGData:GetQuickInviteMatchTeamInfo()
	return self.quick_invite_match_info
end

-- 设置召集CD结束时间
function NewTeamWGData:SetZhaoJiCdEndTime()
	local cd_time = 20
	self.zhaoji_cd_end_time = Status.NowTime + cd_time
end

-- 获取召集CD结束时间
function NewTeamWGData:GetZhaoJiCdEndTime()
	return self.zhaoji_cd_end_time or Status.NowTime
end

------------------------------------分身--------------------------------
function NewTeamWGData:SetTeamFenshenInfo(protocol)
	self.fenshen_open_flag = protocol.fenshen_open_flag
	self.fenshen_mail_count = protocol.fenshen_mail_count
end

function NewTeamWGData:GetFenshenFuncIsOpen()
	return self.fenshen_open_flag == 1
end

function NewTeamWGData:GetFenShenMailCountBySeq(seq)
	return self.fenshen_mail_count[seq] or 0
end

----------------------------------召唤卡--------------------------------
function NewTeamWGData:GetShareCardInfo()
	if not self.share_card_info then
		self.share_card_info = {}
		local share_card_item_list = string.split(self.other_cfg.share_item_id, "|")
		for k, v in pairs(share_card_item_list) do
			self.share_card_info[k] = tonumber(v)
		end
	end

	return self.share_card_info
end


function NewTeamWGData:SetTeamShareItemInfo(protocol)
	self.member_share_item_list = protocol.member_item_list
end

function NewTeamWGData:SetSingleShareItemInfo(protocol)
	local data = protocol.change_data
	local index = protocol.index

	self.member_share_item_list[index] = data
end

function NewTeamWGData:GetAllTeamShareItemInfo()
	return self.member_share_item_list
end

function NewTeamWGData:UpdataTeamShareItemInfo()
	local share_card_info = self:GetShareCardInfo()
	local main_role_id = RoleWGData.Instance:GetMainRoleId()
	local team_list = SocietyWGData.Instance:GetTeamMemberList()
	local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
	local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
	if not is_in_team or not is_leader then
		return
	end

	for i, v in pairs(share_card_info) do
		local item_id = v
		local can_use_item_info = {}
		local all_item_num = 0
		local member_item_list_info = {}
		for k1, v1 in pairs(self.member_share_item_list) do
			local role_id = v1.role_id
			if role_id > 0 then
				member_item_list_info[role_id] = v1.item_num_list[item_id] or 0
			end
		end

		for k1, v1 in pairs(team_list) do
			if main_role_id == v1.role_id then
				all_item_num = all_item_num + (member_item_list_info[v1.role_id] or 0)
				local data = {}
				data.is_me = true
				data.role_id = v1.role_id
				data.item_id = item_id
				data.item_num = member_item_list_info[v1.role_id] or 0
				table.insert(can_use_item_info, data)
			else
				local share_card_flag = v1.share_card_flag == 1
				if v1.scene_id == Scene.Instance:GetSceneId() and share_card_flag then
					all_item_num = all_item_num + (member_item_list_info[v1.role_id] or 0)
					local data = {}
					data.is_me = false
					data.role_id = v1.role_id
					data.item_id = item_id
					data.item_num = member_item_list_info[v1.role_id] or 0
					table.insert(can_use_item_info, data)
				end
			end
		end

		if not IsEmptyTable(can_use_item_info) then
			if not self.can_use_item_info then
				self.can_use_item_info = {}
			end
	
			local data = {}
			data.can_use_item_info = can_use_item_info
			data.all_item_num = all_item_num
			self.can_use_item_info[item_id] = data
		end
	end
end

function NewTeamWGData:GetSelectUseShareItemInfo(item_id)
	local main_role_id = RoleWGData.Instance:GetMainRoleId()
	local item_num = 0
	local use_role_id = main_role_id

	local cur_select_role_id = main_role_id
	local cur_select_item_num = 0

	local is_in_team = SocietyWGData.Instance:GetIsInTeam() == 1
	local is_leader = SocietyWGData.Instance:GetIsTeamLeader() == 1
	if not item_id then
		return item_num, use_role_id
	end

	local item_info = self.can_use_item_info[item_id]
	if not item_info then
		return item_num, use_role_id
	end

	if is_in_team and is_leader then
		item_num = item_info.all_item_num or 0
		for k, v in pairs(item_info.can_use_item_info) do
			if v.item_num > 0 and v.role_id == main_role_id then
				cur_select_item_num = v.item_num
				cur_select_role_id = main_role_id
				break
			elseif v.item_num > cur_select_item_num then
				cur_select_item_num = v.item_num
				cur_select_role_id = v.role_id
			end
		end
		
		item_num = item_info.all_item_num
		use_role_id = cur_select_role_id
	else
		item_num = item_info.all_item_num
	end

	return item_num, use_role_id
end

--跨服回到本服需要
function NewTeamWGData:GetSetFollowLeaderFlag(flag)
	if flag ~= nil then
		self.need_follow_leader = flag
		return
	end

	return self.need_follow_leader
end

function NewTeamWGData:GetBarrageCfgBYTeamType(team_type)
	return self.barrage_cfg[team_type]
end

function NewTeamWGData:GetTeamTargetLimitCfgByTeamType(team_type)
	return self.team_target_limit_cfg[team_type]
end

function NewTeamWGData:CheckIsOpenDayLimit(team_type)
	local team_target_limit_cfg = self:GetTeamTargetLimitCfgByTeamType(team_type)
	if not team_target_limit_cfg then
		return true, {}
	end

	local open_day_tab = {}
	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
    local day_start_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)
	local week_num = TimeUtil.FormatSecond3MYHM1(day_start_time)
	week_num = week_num > 6 and 0 or week_num
	for i, v in pairs(team_target_limit_cfg) do
		if v.min_open_day_limit <= cur_open_day and cur_open_day <= v.max_open_day_limit then
			local split_list = string.split(v.open_week_limit, "|")
			for key, day_num in pairs(split_list) do
				open_day_tab[tonumber(day_num)] = true
			end	
		end
	end

	return open_day_tab[week_num], open_day_tab
end