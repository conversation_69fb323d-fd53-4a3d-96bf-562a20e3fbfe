EffectObj = EffectObj or BaseClass(SceneObj)

local BOSS_GOST_KING_SCENE_ID = 151
function EffectObj:__init()
	self.obj_type = SceneObjType.EffectObj
	self.draw_obj:SetObjType(self.obj_type)
	self.effect_root = nil
	self.parent_obj = nil
	self.target_rotate = 0
	self.rotate_speed = 0
	self.rotate_interval = 0

	self.shield_obj_type = ShieldObjType.SceneEffectObj
	if self.vo ~= nil and Scene ~= nil and Scene.Instance ~= nil 
	and (self:IsSiXiangSkillEffect(self.vo.product_method)
	or self:IsWuXingSkillEffect(self.vo.product_method)
	or SkillWGData.Instance:GetIsHaloSkill(self.vo.product_id)
	or SkillWGData.Instance:GetIsTianShenHeJiSkill(self.vo.product_id)) then
		local main_role = Scene.Instance:GetMainRole()
		if main_role ~= nil and not main_role:IsDeleted() then
			local obj_id = main_role:GetObjId()
			if self.vo.param1 ~= nil and obj_id == self.vo.param1 then
				self.shield_obj_type = ShieldObjType.MainRoleSkillEffect
			elseif self.vo.param1 ~= nil and obj_id ~= self.vo.param1 then
				self.shield_obj_type = ShieldObjType.RoleSkillEffect
			end
		end
	end

	self.is_limit_show = false
end

function EffectObj:__delete()
	self:ReleaseEffect()
	self.parent_obj = nil
	self.line_renderer = nil

	GlobalTimerQuest:CancelQuest(self.release_delay_timer)
	self.release_delay_timer = nil
	self.target_rotate = 0
	self.rotate_speed = 0
	self.rotate_interval = 0
end

function EffectObj:IsEffect()
	return true
end

function EffectObj:VisibleChanged(visible)
	if self.vo ~= nil
	and (self:IsSiXiangSkillEffect(self.vo.product_method)
	or self:IsWuXingSkillEffect(self.vo.product_method)
	or SkillWGData.Instance:GetIsHaloSkill(self.vo.product_id)
	or SkillWGData.Instance:GetIsTianShenHeJiSkill(self.vo.product_id)) then

		if not visible then
			self:ReleaseEffect()
		end

		self.is_limit_show = not visible
	end
end

function EffectObj:InitAppearance()
    if PRODUCT_METHOD.SKILL_READDING == self.vo.product_method
		or PRODUCT_METHOD.SKILL_READDING_CHARGE == self.vo.product_method
     	then
		self:CreateFazhen()
	else
		self:CreateEffect()
	end
end

function EffectObj:GetLineRenderer()
	if self.line_renderer == nil and self.effect_root then
		self.line_renderer = self.effect_root:GetComponent(typeof(UnityEngine.LineRenderer))
	end
	return self.line_renderer
end

function EffectObj:CreateFazhen()
	local skill_cfg = nil
	local server_angle = -(self.vo.dir * 180 / math.pi)

	if PRODUCT_METHOD.SKILL_READDING_CHARGE == self.vo.product_method then
		local cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(self.vo.product_id, 1)
		if cfg ~= nil then
			skill_cfg = {
				RangeType = AOE_RANGE_TYPE.SELF_BEGINNING_RECT,
				RectWidth = cfg.attack_range,
				RectLength = cfg.attack_range2,
				DirType = SKILL_DIR_TYPE.NONE,
				Range2 = cfg.attack_range2,
			}
		end
	else
		skill_cfg = SkillWGData.GetMonsterSkillConfig(self.vo.product_id)
	end

	if nil == skill_cfg then
		return
	end

	local effect = ""
	local size = nil
	local angle_y = 0
	size = Vector2(self.vo.param2, self.vo.param2)

	local special_angle = 0

	if Scene.Instance:GetSceneType() == SceneType.FBCT_NEWPLAYERFB and BOSS_GOST_KING_SCENE_ID == Scene.Instance:GetSceneId() then
		--新手-火鬼王技能预警特效特殊处理
		--因为此怪物是固定在一个地方无法移动，策划需求就是预警特效也固定
		angle_y = 186
		special_angle = 180
		effect = "eff_yujing_fang"
		self.vo.src_pos_x = 104
        self.vo.src_pos_y = 242
	else
		if AOE_RANGE_TYPE.SELF_BEGINNING_RECT == skill_cfg.RangeType
			or AOE_RANGE_TYPE.SELF_CENTERED_QUADRATE == skill_cfg.RangeType
			or AOE_RANGE_TYPE.TARGET_CENTERED_QUADRATE == skill_cfg.RangeType
		then
			angle_y = 90 - server_angle
			effect = "eff_yujing_fang"
			if skill_cfg.DirType ~= SKILL_DIR_TYPE.NONE then
				size = Vector2(skill_cfg.RectWidth, skill_cfg.RectLength)
				if skill_cfg.RectLength > skill_cfg.RectWidth then
					angle_y = server_angle
					size = Vector2(skill_cfg.RectLength, skill_cfg.RectWidth)
				end
			else
				if AOE_RANGE_TYPE.SELF_BEGINNING_RECT == skill_cfg.RangeType then
					size.y = self.vo.param3 or self.vo.param2

					if skill_cfg.DirType == SKILL_DIR_TYPE.NONE then
						angle_y = 90 + server_angle
						size.x = skill_cfg.Range2 or self.vo.param2
					end
				end
			end

			if skill_cfg.DirType == SKILL_DIR_TYPE.TWO then
				angle_y = server_angle
				if skill_cfg.RectLength > skill_cfg.RectWidth then
					angle_y = 90 + server_angle
				end
				effect = "yujing_shuangjuxing" -- 旧
			elseif skill_cfg.DirType == SKILL_DIR_TYPE.CROSS then
				angle_y = server_angle
				effect = "yujing_shuangjuxing_shizi" -- 旧
				special_angle = 180
			elseif skill_cfg.DirType == SKILL_DIR_TYPE.TOW_CROSS then
				angle_y = server_angle
				effect = "yujing_shuangjuxing_mixi" -- 旧
				special_angle = 180
			end
		elseif AOE_RANGE_TYPE.SELF_BEGINNING_SECTOR == skill_cfg.RangeType then
			effect = "eff_yujing_shanxing"
			angle_y = server_angle + 90
		else
			effect = "eff_yujing_yuan"
		end
	end

	self:SetLogicPos(self.vo.src_pos_x, self.vo.src_pos_y)

	local bunble, asset = ResPath.GetEnvironmentCommonEffect(effect)
	if not self.async_loader then
		self.async_loader = AllocAsyncLoader(self, "EffectObj" .. self.vo.product_id)
		self.async_loader:SetParent(self.draw_obj.root.transform)
		self.async_loader:SetIsUseObjPool(true)
		self.draw_obj.root.transform.localRotation = Quaternion.identity
	end

	self.async_loader:Load(bunble, asset, function(obj)

	if nil == obj then
		return
	end
	-- local fazhen = obj:GetComponent(typeof(Fazhen))
	local fazhen_list = obj:GetComponentsInChildren(typeof(Fazhen))
	local moveobj = obj:GetComponent(typeof(MoveableObject))
	if nil == fazhen_list or fazhen_list.Length == 0 or nil == moveobj then
		self:ReleaseEffect()
		return
	end

	if Scene.Instance:GetSceneType() == SceneType.CROSS_TASK_CHAIN_BOSS then
		moveobj:SetOffset(Vector3(0,0.2,0))
	else
		moveobj:SetOffset(Vector3(0,0.1,0)) -- 提高0.1防止被地形遮挡
	end

	obj.transform:SetParent(self.draw_obj.root.transform)
	self.effect_root = obj
	moveobj:SetPosition(self.real_pos.x, 0, self.real_pos.y)

	local total_time =  self.vo.disappear_time - self.vo.birth_time
	local elease_time = TimeWGCtrl.Instance:GetServerTime() - self.vo.birth_time + 0.5
	special_angle = special_angle / fazhen_list.Length
	for i = 0, fazhen_list.Length - 1 do
		fazhen_list[i]:SetRotateY(angle_y + special_angle * i)
		if size ~= nil then
			fazhen_list[i]:SetSize(size)
		end
		fazhen_list[i]:Play(elease_time, total_time)
	end

	GlobalTimerQuest:AddDelayTimer(function()
		self:ReleaseEffect()
		end, total_time * 2)
	end)
end

function EffectObj:CreateEffect()
	local effect_name = ""
	local product_id = self.vo.product_id
	local product_method = self.vo.product_method
	local res_func = nil
	local def = 3
	local need_set_pos = false
	if self.is_limit_show then
		return
	end

	if self:IsLine() then
		if -1 ~= self.vo.main_deliverer_obj_id then
			effect_name = self.vo.res or "Effect_shenbing_lszn_01"
			res_func = ResPath.GetMiscEffect
		else
			effect_name = self.vo.res or "Strengthen_buff_01"
			res_func = ResPath.GeBufftEffect
		end
	else
		local skill_cfg = SkillWGData.GetMonsterSkillConfig(product_id)
		-- 怪物技能
		if nil ~= skill_cfg then
			effect_name = skill_cfg.fazhen_effect
		end

		res_func = ResPath.GetEffect
	end

	local bunble, asset
	local cfg = SkillWGData.Instance:GetSceneBuffEffectCfg(product_method, product_id)
	if product_method == PRODUCT_METHOD.PRODUCT_METHOD_GUILD_BATTLE_GATHER_CLIENT then   --仙盟战-采集物出场特效
		local gather_id = self.vo.param1
		local gather_cfg = GuildBattleRankedWGData.Instance:GetLingShiCfgById(gather_id)
		if not IsEmptyTable(gather_cfg) then
			bunble = gather_cfg.effect_bunble
			asset = gather_cfg.effect_asset
		end
		def = 10
		need_set_pos = true
	elseif cfg ~= nil then
		bunble = cfg.bunble
		asset = cfg.asset
		def = cfg.alive_time ~= "" and cfg.alive_time or def
		need_set_pos = cfg.set_pos == 1
	else
		if "" == effect_name or "none" == effect_name then
			return
		end

		bunble, asset = res_func(effect_name)
	end

	if bunble == nil or asset == nil then
		return
	end

	if need_set_pos then
		self:SetLogicPos(self.vo.src_pos_x, self.vo.src_pos_y)
	end

	if not self.async_loader then
		self.async_loader = AllocAsyncLoader(self, "EffectObj" .. product_id)
		self.async_loader:SetParent(self.draw_obj.root.transform)
		self.async_loader:SetIsUseObjPool(true)


	end

	self.async_loader:Load(bunble, asset, function(obj)
		if not obj then
			return
		end

		self.effect_root = obj
		self:SetPoint()
		if product_method == PRODUCT_METHOD.DUJIE_YUN then -- 渡劫雷云特效
			local moveableObject = obj:GetOrAddComponent(typeof(MoveableObject))
			moveableObject:SetOffset(Vector3(0,9,0))
		end
		obj:GetOrAddComponent(typeof(MoveableObject)):SetPosition(self.real_pos.x, 0, self.real_pos.y)
		if not self:IsLine()  then
			GlobalTimerQuest:CancelQuest(self.release_delay_timer)
			self.release_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
			--如果服务端没有释放，自动释放
				self:ReleaseEffect()
			end, def)
		end
	end)
end

function EffectObj:IsLine()
	return self.vo.product_id == PRODUCT_ID_TRIGGER.CLIENT_SHANDIANXIAN_LINE or
		self.vo.product_id == PRODUCT_ID_TRIGGER.CLIENT_HUIXUE_LINE
end

function EffectObj:SetPoint()
	-- body
	if self:IsLine() then
		if self.vo.summoner_obj_id < 0 or 10000 < self.vo.summoner_obj_id then
			return
		end

		local line_renderer = self:GetLineRenderer()
		if not line_renderer or IsNil(line_renderer) then return end
		local boss = Scene.Instance:GetObj(self.vo.summoner_obj_id)
		local monster = Scene.Instance:GetObj(self.vo.deliverer_obj_id)
		if not boss or not monster then
			return
		end
		local pos = boss.draw_obj:GetRootPosition()
		local point = boss.draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
		if point then
			pos = point.position
        end
        if pos and pos.x then
            line_renderer:SetPosition(0, Vector3(pos.x, pos.y, pos.z))
        end
		pos = monster.draw_obj:GetRootPosition()
		point = monster.draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
		if point then
			pos = point.position
        end
        if pos and pos.x then
            line_renderer:SetPosition(1, Vector3(pos.x, pos.y, pos.z))
        end
	end
end

function EffectObj:SetLinePos()
	if self.vo == nil then
		return
	end

	if self.vo.product_method == PRODUCT_METHOD.PRODUCT_METHOD_PLAN_LINT_EFFECT then
		local begin_obj_id = self.vo.param1
		local end_obj_id = self.vo.param2
		local begin_obj = Scene.Instance:GetObj(begin_obj_id)
		local end_obj = Scene.Instance:GetObj(end_obj_id)
		if begin_obj ~= nil and not begin_obj:IsDeleted() and end_obj ~= nil and not end_obj:IsDeleted() then
			local begin_draw_obj = begin_obj:GetDrawObj()
			local end_draw_obj = end_obj:GetDrawObj()
			local line_renderer = self:GetLineRenderer()
			if begin_draw_obj ~= nil and end_draw_obj ~= nil and line_renderer ~= nil and not IsNil(line_renderer)  then
			    local pos = begin_draw_obj:GetRootPosition()
			    local point = begin_draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
			    if point then
			        pos = point.position
                end
                if pos and pos.x then
                    line_renderer:SetPosition(0, Vector3(pos.x, pos.y, pos.z))
                end

			    pos = end_draw_obj:GetRootPosition()
			    point = end_draw_obj:GetAttachPoint(AttachPoint.BuffMiddle)
			    if point then
			        pos = point.position
			    end
                if pos and pos.x then
                    line_renderer:SetPosition(1, Vector3(pos.x, pos.y, pos.z))
                end
			end
		end
	end
end

function EffectObj:ReleaseEffect()
	-- body
	-- if self.effect_root then
	-- 	ResPoolMgr:Release(self.effect_root)
	-- 	self.effect_root = nil
	-- end
	if self.effect_root then
		-- ResPoolMgr:Release(self.effect_root)
		self.effect_root = nil
	end
	if self.async_loader then
		self.async_loader:Destroy()
		self.async_loader = nil
	end
end

function EffectObj:Update(now_time, elapse_time)
	SceneObj.Update(self, now_time, elapse_time)
	self:SetPoint()
	self:SetLinePos()
	self:CheckAutoRotate(now_time, elapse_time)
end

function EffectObj:CheckAutoRotate(now_time, elapse_time)
	if self:IsDeleted() then
		return
	end

	if self.vo.product_method ~= PRODUCT_METHOD.PRODUCT_METHOD_FIRE_OBJ then
		return
	end

	if self.rotate_speed == 0 then
		return
	end

	local draw_obj = self:GetDrawObj()
	if draw_obj ~= nil then
		local transform = draw_obj:GetTransfrom()
		local cur_angle = transform.rotation.eulerAngles
		local next_angle = elapse_time * self.rotate_speed  + cur_angle.y
		next_angle = next_angle % 360
		if math.abs(next_angle - self.target_rotate) <= 1 then
			return
		end

		self:SetRotation(Quaternion.Euler(cur_angle.x, next_angle, cur_angle.z))
	end
end

function EffectObj:SetAutoRotateInfo(x, y, speed, interval)
	local draw_obj = self:GetDrawObj()
	if draw_obj ~= nil then
		local transform = draw_obj:GetTransfrom()
		local pos = self:GetLuaPosition()
		local t_x, t_y = GameMapHelper.LogicToWorld(x, y)
		local m_dir = u3dpool.v3Normalize({x = t_x - pos.x, y = pos.y, z = t_y - pos.z})
		local cur_angle = transform.rotation.eulerAngles
		local angle = Quaternion.LookRotation(Vector3(m_dir.x, m_dir.y, m_dir.z))
		local b = Quaternion.ToEulerAngles(angle)
		local r = b.y
		if cur_angle.y <= 90 and r >= 270 then
			self.rotate_speed = ((r - 360) - cur_angle.y) / interval
		else
			self.rotate_speed = (r - cur_angle.y) / interval
		end

		self.target_rotate = r
	end	
end

function EffectObj:IsSiXiangSkillEffect(product_method)
	if product_method == nil then
		return false
	end

	return product_method >= PRODUCT_METHOD.PRODUCT_METHOD_SIXIANG_1 and product_method <= PRODUCT_METHOD.PRODUCT_METHOD_SIXIANG_4
end

function EffectObj:IsWuXingSkillEffect(product_method)
	if product_method == nil then
		return false
	end
	
	return product_method >= PRODUCT_METHOD.SKILL_WUXING_1 and product_method <= PRODUCT_METHOD.SKILL_WUXING_7
end