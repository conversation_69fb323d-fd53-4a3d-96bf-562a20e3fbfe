function MechaView:LoadEquipCallBack()
    if not self.me_mecha_list then
        self.me_mecha_list = AsyncListView.New(MEMechaListRender, self.node_list.me_mecha_list)
        self.me_mecha_list:SetDefaultSelectIndex(nil)
        self.me_mecha_list:SetStartZeroIndex(true)
        self.me_mecha_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMEMechaHandler, self))
    end

    self.me_sp_item_list = {}
    for i = 1, 8 do
        self.me_sp_item_list[i] = MeMechaSPItemRender.New(self.node_list["me_eq_item" .. i])
        self.me_sp_item_list[i]:SetData({111})
        self.node_list["me_eq_item" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickMeEquipToggle, self, i))
    end

    if not self.me_gundam_model then
        self.me_gundam_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["me_modle_root"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.me_gundam_model:SetRenderTexUI3DModel(display_data)
        -- self.me_gundam_model:SetUI3DModel(self.node_list["me_modle_root"].transform, self.node_list["me_modle_root"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    self.me_mecha_seq = -1
    self.me_right_tog_select_id = 1

    self.me_show_action = false
end

function MechaView:ShowEquipCallBack()
    self:RightPanleShowTween(self.node_list.me_right_tween_root, self.node_list.me_right_tween_root)

    if self.me_gundam_model then
        self.me_gundam_model:PlayRoleShowAction()
    end
end

function MechaView:ChangeEquipCallBack()
    
end

function MechaView:ReleaseEquipCallBack()
    if self.me_mecha_list then
        self.me_mecha_list:DeleteMe()
        self.me_mecha_list = nil
    end

    if self.me_sp_item_list then
        for k, v in pairs(self.me_sp_item_list) do
            v:DeleteMe()
        end

        self.me_sp_item_list = nil
    end

    if self.me_gundam_model then
        self.me_gundam_model:DeleteMe()
        self.me_gundam_model = nil
    end

    self.me_model_cache = nil
end

function MechaView:OnFlushEquipCallBack()
    local mecha_show_data = MechaWGData.Instance:GetMechaShowDataList()
    self.me_mecha_list:SetDataList(mecha_show_data)
    self.me_mecha_list:JumpToIndex(self:GetMESelectMecha(mecha_show_data))
end

function MechaView:FlushMechaEquipRightPanel()
    local tog_select_id = self:GetMERightTogSelect()

    if self.me_right_tog_select_id == tog_select_id then
    
    else
        self.node_list["me_right_tog" .. tog_select_id].toggle.isOn = true
    end


    -- if self.me_right_tog_select_id == tog_select_id then
    --     if tog_select_id == 1 then
    --         self:FlushFighterPlaneAttrInfoPanel()
    --     else
    --         self:FlushFighterPlaneUplevelPanel()
    --     end
    -- else
    --     if tog_select_id == 1 then
    --         self.node_list.mfp_attr_tog.toggle.isOn = true
    --     else
    --         self.node_list.mfp_uplevel_tog.toggle.isOn = true
    --     end
    -- end

    -- self.me_right_tog_select_id = tog_select_id
    
end

function MechaView:FlushMechaEquipModel()
    local part_list = {}
    local base_part = MechaWGData.Instance:GetMechaBasePartListByMechaSeq(self.me_mecha_seq)
    for k, v in pairs(base_part) do
        local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
        part_list[part_cfg.part] = part_cfg.res_id
    end

    local putton_list = MechaWGData.Instance:GetMechaPartWearPartList(self.me_mecha_seq)
    if not IsEmptyTable(putton_list) then
        for k, v in pairs(putton_list) do
            if v >= 0 then
                local part_cfg = MechaWGData.Instance:GetPartCfgBySeq(v)
                part_list[part_cfg.part] = part_cfg.res_id
            end
        end
    end

    local part_info = {
		gundam_seq = self.me_mecha_seq,
		gundam_body_res = part_list[MECHA_PART_TYPE.BODY] or 0,
        gundam_weapon_res = part_list[MECHA_PART_TYPE.WEAPON] or 0,
		gundam_left_arm_res = part_list[MECHA_PART_TYPE.LEFT_HAND] or 0,
        gundam_right_arm_res = part_list[MECHA_PART_TYPE.RIGHT_HAND] or 0,
		gundam_left_leg_res = part_list[MECHA_PART_TYPE.LEFT_FOOT] or 0,
        gundam_right_leg_res = part_list[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
		gundam_left_wing_res = part_list[MECHA_PART_TYPE.LEFT_WING] or 0,
        gundam_right_wing_res = part_list[MECHA_PART_TYPE.RIGHT_WING] or 0,
	}

    if self:IsMEModelChange(part_info) then
        self.me_model_cache = part_info
        self.me_gundam_model:SetGundamModel(part_info)

        if not self.me_show_action then
            self.me_show_action = true
            self.me_gundam_model:PlayRoleShowAction()
        end
    end
end

function MechaView:IsMEModelChange(part_info)
    if IsEmptyTable(self.me_model_cache) then
        return true
    end

    for k, v in pairs(part_info) do
        if self.me_model_cache[k] ~= v then
            return true
        end
    end

    return false
end

function MechaView:GetMERightTogSelect()
    -- local remind = MechaWGData.Instance:GetMFPMechaPartCellRemind(self.mfb_mecha_seq, self.mfb_mecha_part , self.mfb_mecha_part_cell_sort)
    -- if remind then
    --     return 2
    -- end

    -- return self.me_right_tog_select_id >= 1 and self.me_right_tog_select_id or 1

    return 2
end

------------------------------------选择回调------------------------------
function MechaView:OnSelectMEMechaHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    self.me_mecha_seq = data.seq
    -- print_error("选择大机甲啊")

    local select_equip = self:GetMESelectMechaEquip()
    if self.node_list["me_eq_item" .. select_equip].toggle.isOn then
        self:OnClickMeEquipToggle(select_equip)
    else
        self.node_list["me_eq_item" .. select_equip].toggle.isOn = true
    end
 
    -- self:OnClickMeEquipToggle(select_equip)
    self:FlushMechaEquipModel()
end

function MechaView:OnClickMeEquipToggle(index)
    -- print_error("index=" , index)
    self:FlushMechaEquipRightPanel()
end


------------------------------------计算选择------------------------------
function MechaView:GetMESelectMecha(mecha_show_data)
    -- if self.me_mecha_seq >= 0 then
    --     local cur_remind = MechaWGData.Instance:GetMFPMechaRemind(self.me_mecha_seq)

    --     if cur_remind then
    --         return self.me_mecha_seq
    --     end
    -- end

    -- if not IsEmptyTable(mecha_show_data) then
    --     for k, v in pairs(mecha_show_data) do
    --         if self.me_mecha_seq ~= v.seq then
    --             local remind = MechaWGData.Instance:GetMFPMechaRemind(v.seq)
    --             if remind then
    --                 return v.seq
    --             end
    --         end
    --     end 
    -- end

    return  self.me_mecha_seq >= 0 and  self.me_mecha_seq or 0
end

function MechaView:GetMESelectMechaEquip()
    return 3
end

------------------------------------MEMechaListRender------------------------------
MEMechaListRender = MEMechaListRender or BaseClass(BaseRender)

function MEMechaListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local icon_bundle, icon_asset = ResPath.GetMechaImg(self.data.icon)
    self.node_list.mecha_icon.image:LoadSprite(icon_bundle, icon_asset, function ()
        self.node_list.mecha_icon.image:SetNativeSize()
    end)

    self.node_list.desc_name.text.text = self.data.name
    local remind = MechaWGData.Instance:GetMFPMechaRemind(self.data.seq)
    self.node_list.remind:CustomSetActive(remind)

    local is_wear_complete = MechaWGData.Instance:IsMechaWearComplete(self.data.seq)
    self.node_list.flag_wear_complete:CustomSetActive(is_wear_complete)
end

function MEMechaListRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_nor:CustomSetActive(not is_select)
    self.node_list.bg_select:CustomSetActive(is_select)
    self.node_list.select_hl:CustomSetActive(is_select)
end

-------------------------------------MeMechaSPItemRender-----------------------------------
MeMechaSPItemRender = MeMechaSPItemRender or BaseClass(BaseRender)

function MeMechaSPItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
    end
end

function MeMechaSPItemRender:__delete()
   if self.item then
        self.item:DeleteMe()
        self.item = nil
   end
end

function MeMechaSPItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item:SetData({item_id = 26042})
end