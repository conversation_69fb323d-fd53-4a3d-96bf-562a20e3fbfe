require("game/xuyuan_freshpool/xuyuan_freshpool_wg_data")
require("game/xuyuan_freshpool/xuyuan_freshpool_view")
require("game/xuyuan_freshpool/xuyuan_freshpool_reward")
require("game/xuyuan_freshpool/xuyuan_freshpool_draw_record_view")
require("game/xuyuan_freshpool/xuyuan_freshpool_pop_view")
require("game/xuyuan_freshpool/xuyuan_freshpool_pop_view2")
require("game/xuyuan_freshpool/xuyuan_freshpool_shop")

XuYuanFreshPoolWGCtrl = XuYuanFreshPoolWGCtrl or BaseClass(BaseWGCtrl)
function XuYuanFreshPoolWGCtrl:__init()
	if XuYuanFreshPoolWGCtrl.Instance then
		ErrorLog("[XuYuanFreshPoolWGCtrl] Attemp to create a singleton twice !")
	end
	XuYuanFreshPoolWGCtrl.Instance = self
    self.view = XuYuanFreshPoolView.New(GuideModuleName.XuYuanFreshPoolView)
    self.draw_record_view = XuYuanFreshPoolDrawRecordView.New()
	self.data = XuYuanFreshPoolWGData.New()
    self.reward_view = XuYuanFreshPoolReward.New()
    self.shop_view = XunYuanFreshPoolShop.New(GuideModuleName.XunYuanFreshPoolShop)
    self.pop_view = XunYuanFreshPoolPopView.New(GuideModuleName.XunYuanFreshPoolPopView)
    self.pop_view2 = XunYuanFreshPoolPopView2.New(GuideModuleName.XunYuanFreshPoolPopView2)

    self:RegisterAllProtocols()

    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function XuYuanFreshPoolWGCtrl:__delete()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
    end
    
    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end
        
    if self.draw_record_view then
		self.draw_record_view:DeleteMe()
		self.draw_record_view = nil
	end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.reward_view then
        self.reward_view:DeleteMe()
        self.reward_view = nil
    end
        
    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    XuYuanFreshPoolWGCtrl.Instance = nil
end

function XuYuanFreshPoolWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCRAYanHuaShengDian3Info,'OnSCRAYanHuaShengDian3Info')
    self:RegisterProtocol(SCRAYanHuaShengDian3RecordListInfo,'OnSCRAYanHuaShengDian3RecordListInfo')
    self:RegisterProtocol(SCRAYanHuaShengDian3DrawRewardInfo,'OnSCRAYanHuaShengDian3DrawRewardInfo')
    self:RegisterProtocol(SCRAYanHuaShengDian3BaoDiRewardDrawInfo,'OnSCRAYanHuaShengDian3BaoDiRewardDrawInfo')
    self:RegisterProtocol(SCRAYanHuaShengDian3ExchangeShopInfo,'OnSCRAYanHuaShengDian3ExchangeShopInfo')
end

--请求
function XuYuanFreshPoolWGCtrl:SendReq(opera_type, param1, param2)
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_YANHUA_SHENGDIAN_3,
		opera_type = opera_type,
        param_1 = param1,
        param_2 = param2,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function XuYuanFreshPoolWGCtrl:MainuiOpenCreate()
    self.main_ui_load = true
    if self.data:GetActIsOpen() then
        self:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.INFO)

        -- 拍脸图
        local role_id = RoleWGData.Instance:InCrossGetOriginUid()
        local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        local set_key = "dudi_fushi_day_no_open" .. role_id
        if day ~= PlayerPrefsUtil.GetInt(set_key) then
            local open_panel = self.data:GetPopPanel()
            ViewManager.Instance:Open(open_panel)
        end
    end
end


function XuYuanFreshPoolWGCtrl:OnSCRAYanHuaShengDian3Info(protocol)
    -- print_error("抽奖信息", protocol)
    local old_suit_index = self.data:GetModelSuiIndex()
    self.data:SetInfo(protocol)
    local new_suit_index = self.data:GetModelSuiIndex()
    local need_flush_model = old_suit_index ~= new_suit_index

	if self.view:IsOpen() then
        if need_flush_model then
            self.view:Flush(0, "flush_model")
        else
            self.view:Flush()
        end
    end

    --self.reward_view:Flush()
    RemindManager.Instance:Fire(RemindName.XuYuanFreshPool)
end

function XuYuanFreshPoolWGCtrl:OnSCRAYanHuaShengDian3RecordListInfo(protocol)
    --print_error("抽奖日志", protocol)
    self.data:SetRecord(protocol.record_list)
    if self.view:IsOpen() then
        self.view:Flush()
    end
end

function XuYuanFreshPoolWGCtrl:OnSCRAYanHuaShengDian3DrawRewardInfo(protocol)
    --print_error("抽奖结果返回", protocol)
    local btn_index = self.data:CacheOrGetDrawIndex()
    if not btn_index then
        return
    end

    local consume_cfg = self.data:GetConsumeCfg()
    local btn_cfg = consume_cfg[btn_index]
    if not btn_cfg then
        return
    end

    local discount = btn_cfg.discount_text ~= "" and btn_cfg.discount_text or 10
    local zhekou_num = tonumber(discount) / 10
    local consume_num = btn_cfg.onekey_lotto_num * zhekou_num
    local str = string.format(Language.XuYuanFreshPool.BtnStr, btn_cfg.onekey_lotto_num)
    local ok_func = function ()
        if self.view:IsOpen() then
            self.view:Flush()
        end

        XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.BUY, btn_cfg.onekey_lotto_num, 1)
    end

    local data_list, item_ids = self.data:CalDrawRewardList(protocol)
    -- self:OpenRewardView(data_list, btn_cfg.yanhua_item.item_id, consume_num, str, function ()
    --     ok_func()
    -- end, btn_cfg.discount_text)
	local price = btn_cfg.consume_count / btn_cfg.yanhua_item.num 	-- 计算单价
    local other_info = {}
    other_info.again_text = str
    other_info.stuff_id = btn_cfg.yanhua_item.item_id
    other_info.times = btn_cfg.yanhua_item.num
    other_info.spend = price
    local best_data = {}
    if IsEmptyTable(item_ids) then
        best_data = nil
    else
        best_data.item_list = item_ids
    end
    other_info.best_data = best_data

    other_info.get_skip_anim_func = function ()
		return XuYuanFreshPoolWGData.Instance:GetSkipComic()
    end
    other_info.set_skip_anim_func = function (is_skip)
        local flag = is_skip and 1 or 0
		XuYuanFreshPoolWGCtrl.Instance:SendReq(RA_YANHUA_SHENGDIAN3_OP_TYPE.ANIM_FLAG, flag)
    end

    TipWGCtrl.Instance:ShowGetCommonReward(data_list, ok_func, other_info)
end

--奖励弹窗
function XuYuanFreshPoolWGCtrl:OpenRewardView(data, stuff_id, stuff_num, btn_text, ok_func, discount_text)
    self.reward_view:SetData(data, stuff_id, stuff_num, btn_text, ok_func, discount_text)
    self.reward_view:Open()
end

function XuYuanFreshPoolWGCtrl:OnSCRAYanHuaShengDian3BaoDiRewardDrawInfo(protocol)
    --print_error("抽奖保底信息", protocol)
    self.data:SaveDrawInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    RemindManager.Instance:Fire(RemindName.XuYuanFreshPool)
end

-- 兑换商店
function XuYuanFreshPoolWGCtrl:OnSCRAYanHuaShengDian3ExchangeShopInfo(protocol)
    self.data:SetGoodsBuyNum(protocol)

    if self.shop_view and self.shop_view:IsOpen() then
        self.shop_view:Flush()
    end
end

function XuYuanFreshPoolWGCtrl:ClickUse(index, func)
    --数量检测
    local cfg = self.data:GetConsumeCfg()
    if not cfg then
        return
    end

    local lotto_cfg = cfg[index]
    if not lotto_cfg then
        return
    end

    local cost_item_id = lotto_cfg.yanhua_item.item_id
    local cost_item_num = lotto_cfg.yanhua_item.num
    local num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    local single_price = lotto_cfg.consume_count / cost_item_num

   --不足弹窗
   if num < cost_item_num then
        if not self.alert then
            self.alert = Alert.New()
        end

        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "xuyuan_fresh_pool")
        self.alert:SetCheckBoxDefaultSelect(false)
        local item_cfg = ItemWGData.Instance:GetItemConfig(cost_item_id)
        local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])

       local cost = single_price * (cost_item_num - num)
       local str = string.format(Language.MergeFireworks.CostStr, name, cost)

       self.alert:SetLableString(str)
       self.alert:SetOkFunc(func)
       self.alert:Open()
    else
       --使用
       func()
   end
end

--通用寻宝记录 包含全服和个人
-- all_list 全服记录list,  title_str 标题文本
function XuYuanFreshPoolWGCtrl:OpenDrawRecordView(all_list, title_str)
	self.draw_record_view:SetData(all_list, title_str)
	if self.draw_record_view:IsLoaded() and self.draw_record_view:IsOpen() then
		self.draw_record_view:Flush()
	else
		self.draw_record_view:Open()
	end
end