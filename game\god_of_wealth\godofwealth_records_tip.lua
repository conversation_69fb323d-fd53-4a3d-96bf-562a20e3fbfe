GodOfWealthRecordsTipView = GodOfWealthRecordsTipView or BaseClass(SafeBaseView)
function GodOfWealthRecordsTipView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, "uis/view/god_of_wealth_ui_prefab", "layout_godofwealth_records_tip")
end

function GodOfWealthRecordsTipView:LoadCallBack()
    if not self.records_list then
        self.records_list = AsyncListView.New(GodOfWealthRecordItemRender, self.node_list.records_list)
    end
end

function GodOfWealthRecordsTipView:ReleaseCallBack()
    if self.records_list then
        self.records_list:DeleteMe()
        self.records_list = nil
    end
end

function GodOfWealthRecordsTipView:OnFlush()
    local count, rank_item_list = GodOfWealthWGData.Instance:GetWealthGodRankInfo()
    self.node_list["records_list"]:SetActive(count > 0)
    self.node_list["no_record_list"]:SetActive(count <= 0)
    if count > 0 and not IsEmptyTable(rank_item_list) then
        self.records_list:SetDataList(rank_item_list)
    end
end