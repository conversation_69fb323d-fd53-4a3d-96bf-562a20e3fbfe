-- 离线卡使用
----------------------------------
UserOffLineView = UserOffLineView or BaseClass(SafeBaseView)

function UserOffLineView:__init()
	self:LoadConfig()
	self.default_num = nil
	self.cur_num = 0
end

function UserOffLineView:LoadConfig()
	-- self.default_index = 1
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 440)})
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_use_offline")
	self.offline_id = {
		[22536] = 3 * 3600,
		[22537] = 6 * 3600,
		[22538] = 5 * 3600,
	}
end

function UserOffLineView:ReleaseCallBack()
	if self.offline_item then
		self.offline_item:DeleteMe()
		self.offline_item = nil
	end
	self.default_num = nil
	self.cur_num = 0
	self.default_show_num = 0
	self.str = nil
end

function UserOffLineView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Bag.PiLiangUse
	self.node_list.slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSoundValueChange, self))
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind1(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancel, self))
	self.offline_item = ItemCell.New(self.node_list.ph_offline_cell)
end

function UserOffLineView:SetData(item_id, num, show_num, str)
	self.item_id = item_id
	self.default_num = num
	self.default_show_num = show_num
	self.str = str
	self:Open()
end

function UserOffLineView:ShowIndexCallBack()
	self:Flush()
end

function UserOffLineView:OnFlush()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.item_id)
	self.offline_item:SetData({item_id = self.item_id, num = item_num, is_bind = 1})

	local item_cfg, _ = ItemWGData.Instance:GetItemConfig(self.item_id)
	local item_data = ItemWGData.Instance:GetItem(self.item_id)
	local remain_offline_rest_time = OfflineRestWGData.Instance:GetRemainOfflineRestTime() or 0
	local cur_show_num = 0
	if item_cfg.use_daytimes and item_cfg.use_daytimes ~= 0 then
		local can_user_num = item_cfg.use_daytimes - ItemWGData.Instance:GetItemUseTimes(self.item_id)
		self.max = item_num <= can_user_num and item_num or can_user_num
		if item_cfg.use_type ~= Item_Use_Type.XianYu and self.max >= 999 then
			self.max = 999
		end

		if self.item_id == SPECIAL_GUAJICARD.IDONE or self.item_id == SPECIAL_GUAJICARD.IDTWO then
			local can_add_time = 20*3600 - remain_offline_rest_time
			local cur_item_add_time = self.offline_id[self.item_id]
			local can_add_num = math.floor(can_add_time/cur_item_add_time)
			
			-- self.max = self.max > use_max_num and use_max_num or self.max
			if can_add_num > self.max then
				can_add_num = self.max
			end
			self.default_num = can_add_num
			cur_show_num = can_add_num
		else
			cur_show_num = self.max
		end
		if self.default_show_num and self.default_show_num > 0 then
			cur_show_num = self.default_show_num
		end
		self.node_list.slider.slider.maxValue = self.max
		self.node_list.slider.slider.minValue = 1
		self.node_list.slider.slider.value = cur_show_num
	else
		self.max = item_num
		if item_cfg.use_type ~= Item_Use_Type.XianYu and self.max >= 999 then
			self.max = 999
		end

		if self.item_id == SPECIAL_GUAJICARD.IDONE or self.item_id == SPECIAL_GUAJICARD.IDTWO then
			local can_add_time = 20*3600 - remain_offline_rest_time
			local cur_item_add_time = self.offline_id[self.item_id]
			local can_add_num = math.floor(can_add_time/cur_item_add_time)
			can_add_num = can_add_num > 0 and can_add_num or 1
			-- local use_max_num = 20*3600 / cur_item_add_time
			-- self.max = self.max > can_add_num and can_add_num or self.max
			if can_add_num > self.max then
				can_add_num = self.max
			end

			self.default_num = can_add_num
			cur_show_num = can_add_num
		else
			cur_show_num = self.max
		end

		if self.default_show_num and self.default_show_num > 0 then
			cur_show_num = self.default_show_num
		end
		self.node_list.slider.slider.maxValue = self.max
		self.node_list.slider.slider.minValue = 1
		self.node_list.slider.slider.value = cur_show_num
	end

	if self.default_num then
		self.num = self.default_num
		self.cur_num = self.num
		self.node_list.lbl_num.text.text = self.default_num
	else
		self.num = item_num
		self.cur_num = self.num
		self.node_list.lbl_num.text.text = self.max
	end

	local name = ItemWGData.Instance:GetItemName(self.item_id)
	local color = ItemWGData.Instance:GetItemColor(self.item_id)
	self.node_list.lbl_item_name.text.text = ToColorStr(name,color)
end

function UserOffLineView:OnClickConfirm()
	--如果溢出，显示溢出界面
	local num = tonumber(self.node_list.lbl_num.text.text)
	if OfflineRestWGData.Instance:IsOverstep(self.item_id, GameMath.Round(num)) then
		OfflineRestWGCtrl.Instance:OpenOfflineOverstepView(self.item_id, GameMath.Round(num))		
		self:Close()
	else
		local item_data = ItemWGData.Instance:GetItem(self.item_id)
		local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(self.item_id)
		if not item_data or not item_cfg then return end
		local num = tonumber(self.node_list.lbl_num.text.text)
		num = num > self.max and self.max or num


		if item_data.num >= num then
			BagWGCtrl.Instance:SendUseItem(item_data.index, GameMath.Round(num), 0, item_cfg.need_gold)
			if self.str then
				SysMsgWGCtrl.Instance:ErrorRemind(self.str)
			end
		else
			local tab = ItemWGData.Instance:GetContinueUseItem(self.item_id, num)
			if nil ~= tab then
				for i = 1, #tab do
					BagWGCtrl.Instance:SendUseItem(tab[i].index, GameMath.Round(tab[i].num), 0, item_cfg.need_gold)
					if self.str then
						SysMsgWGCtrl.Instance:ErrorRemind(self.str)
					end
				end
			else
				print_error("没有找到该配置", self.item_id)
			end
		end
		if OfflineRestWGData.Instance:IsOverstep(self.item_id, GameMath.Round(num)) then
			OfflineRestWGCtrl.Instance:OpenOfflineOverstepView(self.item_id, GameMath.Round(num))	
		else
			if item_cfg.open_panel ~= "" then
				FunOpen.Instance:OpenViewNameByCfg(item_cfg.open_panel, self.item_id)
	 		end
		end
		
		self:Close()
	end
end

function UserOffLineView:OnClickCancel()
	self:Close()
end

function UserOffLineView:OnClickSub()
	if self.max == 0 then return end
	self.cur_num = self.node_list.lbl_num.text.text - 1
	if self.cur_num <= 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MinValue1)
	end
	self.cur_num = self.cur_num <= 1 and 1 or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function UserOffLineView:OnClickAdd()
	if self.max == 0 then return end
	self.cur_num = self.node_list.lbl_num.text.text + 1
	if self.cur_num >= self.max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
	end
	self.cur_num = self.cur_num >= self.max and self.max or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function UserOffLineView:OnSoundValueChange(float_param)
	self.node_list.lbl_num.text.text = float_param

end

