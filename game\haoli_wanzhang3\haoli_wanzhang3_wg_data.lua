HaoLiWanZhang3WGData = HaoLiWanZhang3WGData or BaseClass()
HaoLiWanZhang3WGData.MAX_REWARD_COUNT = 5 -- 一轮奖励
HaoLiWanZhang3WGData.JUMP_REMIND_KEY = "haoli_wanzhang3_jump_remind" -- 红点储存数据key


function HaoLiWanZhang3WGData:__init()
    if HaoLiWanZhang3WGData.Instance then
		error("[HaoLiWanZhang3WGData] Attempt to create singleton twice!")
		return
	end

    HaoLiWanZhang3WGData.Instance = self
    self:InitConfig()
    self.reward_flag = {}
    self.every_day_flag = false
    self.show_tip = true

    RemindManager.Instance:Register(RemindName.HaoLiWanZhang3, BindTool.Bind(self.GetRemind, self))
end

function HaoLiWanZhang3WGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.HaoLiWanZhang3)
    HaoLiWanZhang3WGData.Instance = nil
    self.reward_flag = nil
    self.every_day_flag = nil
end

function HaoLiWanZhang3WGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_haoli_wanzhang3_auto")
    if not cfg then
        return
    end
    self.cur_reward_cfg = ListToMapList(cfg.reward, "grade", "round")
    self.cur_model_cfg = ListToMap(cfg.display_model, "grade", "round")
    self.show_waist_cfg = ListToMap(cfg.show_waist, "grade", "round")
    self.suit_act_map_cfg = ListToMap(cfg.activation, "grade", "round", "part")
    
    self.open_day_cfg = cfg.open_day
    self.other_cfg = cfg.other[1]
end

function HaoLiWanZhang3WGData:SetAllVientianeInfo(protocol)
    self.score = protocol.score
    self.grade = protocol.grade
	self.round_num = protocol.round_num
	self.reward_flag = protocol.reward_flag
    self.every_day_flag = protocol.every_day_flag == 1
end

-- 获取累充额度
function HaoLiWanZhang3WGData:GetCurScore()
    return self.score or 0
end

-- 获取每日领取标识
function HaoLiWanZhang3WGData:GetShopIsBuyFlag()
	return self.every_day_flag
end

-- 获取所有奖励领取状态
function HaoLiWanZhang3WGData:GetAllRewardState()
	return self.reward_flag[self.round_num] or {}
end

-- 获取当前轮次
function HaoLiWanZhang3WGData:GetCurRoundNum()
    return self.round_num or 0
end

-- 获取当前档次总轮次
function HaoLiWanZhang3WGData:GetTotalRoundNum()
    return #(self.cur_reward_cfg[self.grade] or {})
end

-- 红点是否可领取
function HaoLiWanZhang3WGData:GetRemind()
    local is_buy_free = self:GetShopIsBuyFlag()
    if not is_buy_free then
        return 1
    end
    if self:GetReceiveRemind() then
        return 1
    end
    
    -- if self:GetJumpBtnRemind() then
    --     return 1
    -- end

	return 0
end


function HaoLiWanZhang3WGData:GetReceiveRemind()
    local remind = false
    local score = self:GetCurScore()
    local flag_num = self:GetAllRewardState()
    local cur_cfg = self:GetCurRoundRewardCfg()
    if not IsEmptyTable(cur_cfg) and not IsEmptyTable(flag_num) then
        for i = 1, #cur_cfg do
            if score >= cur_cfg[i].need_lingyu then
                if flag_num[i - 1] == 0 then
                    remind = true
                end
            end
        end
    end

    return remind
end

-- 跳转按钮的红点
function HaoLiWanZhang3WGData:GetJumpBtnRemind()
    local value = RoleWGData.GetRolePlayerPrefsInt(HaoLiWanZhang3WGData.JUMP_REMIND_KEY)
    return value ~= 1
end

-- 设置跳转按钮红点
function HaoLiWanZhang3WGData:SetJumpBtnRemind()
    RoleWGData.SetRolePlayerPrefsInt(HaoLiWanZhang3WGData.JUMP_REMIND_KEY, 1)
end

function HaoLiWanZhang3WGData:GetCurLocation()
    local score = self:GetCurScore()
    local cur_cfg = self:GetCurRoundRewardCfg()
    local location = -1
    if not IsEmptyTable(cur_cfg) then
        for i = 1, #cur_cfg do
            if score < cur_cfg[i].need_lingyu then
                location = i
                break
            elseif score >= cur_cfg[#cur_cfg].need_lingyu then
                location = #cur_cfg + 1
            end
        end
    end

    return location
end

function HaoLiWanZhang3WGData:GetTipShowShopCfg()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg = {}
	for i, v in ipairs(self.open_day_cfg) do
		if server_day >= v.start_day and server_day <= v.end_day then
			cfg = v
		end
	end

	return cfg
end

function HaoLiWanZhang3WGData:GetIsShowTip()
	local is_show = false
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    if self.show_tip then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HAOLI_WANZAHNG3)
		if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
            if role_level >= self.other_cfg.open_level then
                is_show = true
            end
		end
	end

	return is_show
end

function HaoLiWanZhang3WGData:SetIsShowTip(state)
	self.show_tip = state
end


function HaoLiWanZhang3WGData:GetRewardStateBySeq(seq) -- 获取奖励状态
    if not IsEmptyTable(self.reward_flag) then
        local flag_num = self:GetAllRewardState()
        return flag_num and flag_num[seq] == 1
    end
end

function HaoLiWanZhang3WGData:GetCurRoundRewardCfg()
    return (self.cur_reward_cfg[self.grade] or {})[self.round_num]
end

function HaoLiWanZhang3WGData:GetCurRoundModelCfg()
    return (self.cur_model_cfg[self.grade] or {})[self.round_num]
end

function HaoLiWanZhang3WGData:GetCurShowWaistCfg()
    return (self.show_waist_cfg[self.grade] or {})[self.round_num]
end



function HaoLiWanZhang3WGData:GetActivationPartList()
	return (self.suit_act_map_cfg[self.grade] or {})[self.round_num]
end

function HaoLiWanZhang3WGData:GetOtherCfg()
	return self.other_cfg
end
