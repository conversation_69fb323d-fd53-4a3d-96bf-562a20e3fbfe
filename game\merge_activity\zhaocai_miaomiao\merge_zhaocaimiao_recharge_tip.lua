MergeZhaoCaiMiaoMiaoRechargeTipsPanel = MergeZhaoCaiMiaoMiaoRechargeTipsPanel or BaseClass(SafeBaseView)

function MergeZhaoCaiMiaoMiaoRechargeTipsPanel:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/operation_activity_ui/zhaocai_miaomiao_prefab", "layout_recharge_miaomiao_tip")
end

function MergeZhaoCaiMiaoMiaoRechargeTipsPanel:LoadIndexCallBack()
	
 	XUI.AddClickEventListener(self.node_list.go_look_look,BindTool.Bind1(self.JumpActMiaoMiaoPanel, self))

end

function MergeZhaoCaiMiaoMiaoRechargeTipsPanel:ShowIndexCallBack()
	-- local interface_cfg = MergeZhaoCaiMiaoWGData.Instance:GetPanelUiInterface()
	-- if not interface_cfg then
	-- 	return 
	-- end
	-- local b, a = ResPath.GetMiaoMiaoRawImg(interface_cfg.pic_14)
	-- self.node_list.big_image.raw_image:LoadSpriteAsync(b, a, function()
	-- 			self.node_list.big_image.raw_image:SetNativeSize()
	-- 		end)
	-- b, a = ResPath.GetMiaoMiaoRawImg(interface_cfg.pic_15)
	-- self.node_list.big_cat.raw_image:LoadSpriteAsync(b, a, function()
	-- 			self.node_list.big_cat.raw_image:SetNativeSize()
	-- 		end)
	-- b, a = ResPath.GetMiaoMiaoImg(interface_cfg.pic_16)
	-- self.node_list.lingdang.image:LoadSpriteAsync(b, a, function()
	-- 			self.node_list.lingdang.image:SetNativeSize()
	-- 		end)
	-- b, a = ResPath.GetMiaoMiaoImg(interface_cfg.pic_18)
	-- self.node_list.tip_paopao.image:LoadSpriteAsync(b, a)
end

function MergeZhaoCaiMiaoMiaoRechargeTipsPanel:OnFlush()
	local item_id = MergeZhaoCaiMiaoWGData.Instance:GetMiaoMiaoRechargeTips(self.recharge_num)
	if item_id > 0 then
		local str = string.format(Language.Activity.MiaoMiaoRechargeDesc, item_id)
		local emoji_text =self.node_list.content_text.emoji_text
		EmojiTextUtil.ParseRichText(emoji_text, str, 20, "#A5492D", false ,nil)
	end
	local act_name = MergeZhaoCaiMiaoWGData.Instance:GetActName()
	self.node_list.act_name.text.text = act_name
end

function MergeZhaoCaiMiaoMiaoRechargeTipsPanel:JumpActMiaoMiaoPanel()
	if MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.MERGE_ACT_ZHAOCAIMAO) then
		ViewManager.Instance:Open(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2112)
	else
		ViewManager.Instance:Open(GuideModuleName.OperationActivityView, TabIndex.operation_act_zhaocaimao)
	end
	self:Close()
end

function MergeZhaoCaiMiaoMiaoRechargeTipsPanel:SetContentType(tip_type, recharge_num, oa_index)
	self.tip_type = tip_type
	self.recharge_num = recharge_num
	self.oa_index = oa_index
	if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end

function MergeZhaoCaiMiaoMiaoRechargeTipsPanel:ReleaseCallBack()
	self.tip_type = nil
	self.recharge_num = nil
	self.oa_index = nil
end







