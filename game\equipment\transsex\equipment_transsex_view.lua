--装备转化

local BAG_COL = 5
local BAG_MAX_COUNT = 500

-- 道具转性别
function EquipmentView:InitEquipTranssexView()
	self.transsex_stuff_item = EquipmentTranssexStuffSlot.New(self.node_list["transsex_stuff_slot"]) 		-- 材料孔
	self.transsex_stuff_item:SetData(nil)

	self.transsex_product_item = EquipmentTranssexGridItem.New(self.node_list["transsex_product"]) 		-- 产品
	self.transsex_product_item:SetData(nil)

	XUI.AddClickEventListener(self.node_list["transsex_amount_plus_btn"], BindTool.Bind1(self.OnClickTranssexPlusBtn, self)) 		-- 加号
	XUI.AddClickEventListener(self.node_list["transsex_amount_minus_btn"], BindTool.Bind1(self.OnClickTranssexMinusBtn, self)) 		-- 减号
	XUI.AddClickEventListener(self.node_list["btn_transsex"], BindTool.Bind1(self.OnClickTranssex, self)) 							-- 转化按钮
	XUI.AddClickEventListener(self.node_list["transsex_amount_btn"], BindTool.Bind1(self.OnClickTranssexAmountBtn, self)) 			-- 数字

	self:CreateTranssexGrid()

	self.transsex_amount = 1
	self:CalTranssexGridAllItem()
end

function EquipmentView:DeleteEquipTranssexView()	
	if self.transsex_stuff_item then
		self.transsex_stuff_item:DeleteMe()
		self.transsex_stuff_item = nil
	end

	if self.transsex_product_item then
		self.transsex_product_item:DeleteMe()
		self.transsex_product_item = nil
	end

	if self.transsex_grid then
		self.transsex_grid:DeleteMe()
		self.transsex_grid = nil
	end
end

-- 创建格子
function EquipmentView:CreateTranssexGrid()
	self.transsex_grid = ScrollGrid.New()
	self.transsex_grid:CreateCells({col = BAG_COL, cell_count = BAG_MAX_COUNT, itemRender = EquipmentTranssexGridRowItem, list_view = self.node_list["transsex_grid"]})
	self.transsex_grid:SetSelectCallBack(BindTool.Bind1(self.OnClickEquipTranssexGridItem, self))
end

function EquipmentView:ShowIndexTranssexView()
	if self.all_transsex_grid_item and self.transsex_grid then
		self.transsex_grid:SetDataList(self.all_transsex_grid_item, 0)
	end
end

function EquipmentView:FlushEquipTranssexView(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushEquipTranssexAllView()
		elseif k == "flush_product" then
			self:FlushTranssexProduct()
			self:FlushEquipTranssexOther()
		elseif k == "clear_stuff_slot" then
			if self.transsex_stuff_item and self:GetStuffDataFunc() then
				self.transsex_stuff_item:SetData(nil)
			end
		end
	end
end

function EquipmentView:OnTranssexItemChange()
	self:CalTranssexGridAllItem()
	self:FlushEquipTranssexGrid()
end

-- 刷新全部面板
function EquipmentView:FlushEquipTranssexAllView()
	self:FlushEquipTranssexGrid()
	self:FlushEquipTranssexOther()
	self:FlushTranssexProduct()
end

function EquipmentView:FlushEquipTranssexOther()
	local stuff_data = self:GetStuffDataFunc()
	self.node_list["transsex_cost"].text.text = "-/-" 
	self.node_list["transsex_amount"].text.text = "-/-"
	if stuff_data then
		local cfg_data = EquipmentWGData.Instance:GetTranssexData(stuff_data.item_id)
		self.node_list["transsex_cost_icon"].image:LoadSprite(ResPath.GetCommonIcon(ResPath.GetMoneyIcon(cfg_data.cfg.cost_type)))
		self.node_list["transsex_cost_icon"].image:SetNativeSize()
		self.node_list["transsex_amount"].text.text = self.transsex_amount
		local cost = EquipmentWGData.Instance:GetTranssexIsFree() and 0 or EquipmentWGData.Instance:GetTranssexCostPrice(stuff_data) * self.transsex_amount
		self.node_list["transsex_cost"].text.text = cost
	else
		self.node_list["transsex_cost_icon"].image:LoadSprite(ResPath.GetCommonIcon(ResPath.GetMoneyIcon(1)))
	end

	-- 首次免费标签
	self.node_list["transsex_free_label"]:SetActive(EquipmentWGData.Instance:GetTranssexIsFree())
end

-- 计算格子数据
function EquipmentView:CalTranssexGridAllItem()
	self.all_transsex_grid_item = {}
	local i = 0
	for _, v in pairs(ItemWGData.Instance:GetBagItemDataList()) do 		-- 普通背包
		if EquipmentWGData.Instance:CanTranssex(v) then
			local data = {}
			data.bag_data = v
			data.get_stuff_slot_data_func = BindTool.Bind(self.GetStuffDataFunc, self)
			self.all_transsex_grid_item[i] = data
			i = i + 1
		end
	end
	for _, v in pairs(ItemWGData.Instance:GetStuffStorgeItemData()) do 	-- 材料背包
		if EquipmentWGData.Instance:CanTranssex(v) then
			local data = {}
			data.bag_data = v
			data.get_stuff_slot_data_func = BindTool.Bind(self.GetStuffDataFunc, self)
			self.all_transsex_grid_item[i] = data
			i = i + 1
		end
	end
end

-- 刷新格子
function EquipmentView:FlushEquipTranssexGrid()
	if not self:IsLoaded() or not self:IsLoadedIndex(TabIndex.equipment_zhuanhua) then
		return
	end

	if nil ~= self.transsex_grid then
		self.transsex_grid:SetDataList(self.all_transsex_grid_item, 2)
	end
end

function EquipmentView:FlushTranssexProduct()
	-- 产品孔
	local stuff_data = self:GetStuffDataFunc()
	local product_item = self.transsex_product_item
	product_item:SetData(nil)
	if stuff_data then
		local product_item_id = EquipmentWGData.Instance:GetTranssexProductItemId(stuff_data.item_id)
		if product_item_id then
			local product_item_data = {}
			product_item_data.item_id = product_item_id
			product_item_data.num = 1
			product_item_data.is_bind = stuff_data.is_bind
			product_item_data.param = stuff_data.param
			product_item:SetData(product_item_data)
		end
	end
end

-- 获取材料孔数据
function EquipmentView:GetStuffDataFunc()
	local stuff_item = self.transsex_stuff_item
	return stuff_item:GetData()
end

-- 点击物品回调（item_index 从1开始）
function EquipmentView:OnClickEquipTranssexGridItem(item_index, cell)
	item_index = item_index - 1 									-- 因为item_index从1开始，所以这里减1
	local bag_item_data = self.all_transsex_grid_item[item_index]	
	if not bag_item_data or bag_item_data.bag_data.num <= 0 then
		return
	end

	-- 清空产品孔
	self.transsex_product_item:SetData(nil)

	-- 材料孔
	local stuff_item = self.transsex_stuff_item
	local stuff_data = {}
	stuff_data.item_id = bag_item_data.bag_data.item_id
	stuff_data.is_bind = bag_item_data.bag_data.is_bind
	stuff_data.param = bag_item_data.bag_data.param
	stuff_data.num = 1
	self.transsex_amount = 1
	stuff_data.index = bag_item_data.bag_data.index
	local from_world_pos = nil
	if cell then
		from_world_pos = cell:GetView().transform.position
	end
	stuff_item:SetData(stuff_data, from_world_pos)
end

-- 点击转性
function EquipmentView:OnClickTranssex()
	local slot_stuff_data = self:GetStuffDataFunc()
	if slot_stuff_data then
		if not EquipmentWGData.Instance:GetTranssexIsFree() then
			local cfg_data = EquipmentWGData.Instance:GetTranssexData(slot_stuff_data.item_id)
			local need_gold = EquipmentWGData.Instance:GetTranssexCostPrice(slot_stuff_data) * self.transsex_amount

			-- 仙玉
			if cfg_data.cfg.cost_type == Shop_Money_Type.Type1 then
				-- 仙玉不足
				local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
				if role_gold < need_gold then
					VipWGCtrl.Instance:OpenTipNoGold()
					return
				end
			end

			-- 绑玉
			if cfg_data.cfg.cost_type == Shop_Money_Type.Type2 then
				-- 绑玉不足
				local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
				if bind_gold_num < need_gold then
					-- 检查仙玉是否足够，如果足够询问是否用仙玉代替绑玉
					local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
					if bind_gold_num + role_gold >= need_gold then
						TipWGCtrl.Instance:OpenAlertByNotBindYu(function() 
							EquipmentWGCtrl.Instance:SendTranssexItem(slot_stuff_data.index, self.transsex_amount)
						end)
					else
						TipWGCtrl.Instance:ShowSystemMsg(Language.Bag.NoEnoughBindGold)
					end
					return 
				end
			end
		end

		EquipmentWGCtrl.Instance:SendTranssexItem(slot_stuff_data.index, self.transsex_amount)
	end
end

-- 点击数目，弹出小键盘
function EquipmentView:OnClickTranssexAmountBtn()
	local slot_stuff_data = self:GetStuffDataFunc()
	if not slot_stuff_data then
		return
	end

	local function callback(input_num)
		self.transsex_amount = input_num
		self:FlushEquipTranssexOther()
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.transsex_amount)
	num_keypad:SetMaxValue(ItemWGData.Instance:GetItemNumInBagByIndex(slot_stuff_data.index))
	num_keypad:SetMinValue(1)
	num_keypad:SetOkCallBack(callback)
end

-- 点击加号
function EquipmentView:OnClickTranssexPlusBtn()
	local stuff_data = self.transsex_stuff_item:GetData()
	if stuff_data then
		local max_item_amount = ItemWGData.Instance:GetItemNumInBagByIndex(stuff_data.index)
		if self.transsex_amount >= max_item_amount then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
		else
			self.transsex_amount = self.transsex_amount + 1
			self:FlushEquipTranssexOther()
		end
	end
end

-- 点击减号
function EquipmentView:OnClickTranssexMinusBtn()
	local stuff_data = self.transsex_stuff_item:GetData()
	if stuff_data then
		if self.transsex_amount <= 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MinValue1)
		else
			self.transsex_amount = self.transsex_amount - 1
			self:FlushEquipTranssexOther()
		end
	end
end
----------------------------------------------------
-- 背包一横格子item
----------------------------------------------------
EquipmentTranssexGridRowItem = EquipmentTranssexGridRowItem or BaseClass(BaseGridRender)
function EquipmentTranssexGridRowItem:__init()
	self.item_list = {}
	for i = 1, 5 do
		local cell = EquipTranssexGridItem.New(self.node_list["ItemRender_"..i])
		cell:SetClickCallBack(BindTool.Bind(self.ClickCell, self, i))
		table.insert(self.item_list, cell)
	end
end

function EquipmentTranssexGridRowItem:__delete()
	for i,v in ipairs(self.item_list) do
		v:DeleteMe()
	end
	self.item_list = nil
end

function EquipmentTranssexGridRowItem:ClickCell(i)
	self:GridItemOnCLick(i)
end

function EquipmentTranssexGridRowItem:OnFlush()
	if self.data == nil then return end

	for i = 1, 5 do
		self.item_list[i]:SetData(self.data[i])
	end
end

function EquipmentTranssexGridRowItem:GetCell(i)
	return self.item_list[i]
end
----------------------------------------------------
-- 背包格子item
----------------------------------------------------
EquipTranssexGridItem = EquipTranssexGridItem or BaseClass(BaseRender)
function EquipTranssexGridItem:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	self.item_cell:SetItemTipFrom(ItemTip.FROM_ROLE_BAG_TRANSSEX)
end

function EquipTranssexGridItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function EquipTranssexGridItem:OnFlush()
	if self.data then 
		if self.data.bag_data.num == 0 then
			self.item_cell:SetData(nil)
			self.item_cell:SetActive(false)
		else
			local old_data = self.item_cell:GetData()

			if old_data == nil or self.old_data_item_id ~= self.data.bag_data.item_id or self.old_data_num ~= self.data.bag_data.num then
				self.item_cell:SetData(self.data.bag_data)
			end
			self.item_cell:SetActive(true)
		end

		self.old_data_item_id = self.data.bag_data.item_id
		self.old_data_num = self.data.bag_data.num

		local stuff_slot_data = self.data.get_stuff_slot_data_func() 
		self.item_cell:SetSelectEffect(stuff_slot_data and stuff_slot_data.index == self.data.bag_data.index)
	else
		self.item_cell:SetData(nil)
		self.item_cell:SetSelectEffect(false)
	end
end

----------------------------------------------------
-- 材料孔
----------------------------------------------------
EquipmentTranssexStuffSlot = EquipmentTranssexStuffSlot or BaseClass(BaseRender)
function EquipmentTranssexStuffSlot:__init()
	self.item_cell = ItemCell.New(self.node_list["stuff_item"])
	self.item_cell:SetItemTipFrom(ItemTip.FROM_ROLE_BAG_TRANSSEX)
	self.item_orginal_pos = self.node_list["stuff_item"].transform.localPosition
	self.item_cell:SetActive(false)
	XUI.AddClickEventListener(self.node_list["click_btn"], BindTool.Bind(self.OnClick, self))
	self.domove_tween = nil
end

function EquipmentTranssexStuffSlot:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	self.item_orginal_pos = nil
end

function EquipmentTranssexStuffSlot:SetData(data, from_world_pos)
	if self.domove_tween then
		self.domove_tween:Kill()
	end
	-- 如果有点击来源的坐标，则播放一个动画
	if from_world_pos and self.item_cell:GetView() then
		self.node_list["stuff_item"].transform.position = from_world_pos
		self.domove_tween = self.node_list["stuff_item"].transform:DOLocalMove(self.item_orginal_pos, 0.5):OnComplete(function()
			ViewManager.Instance:FlushView(GuideModuleName.Equipment, TabIndex.equipment_zhuanhua)
		end)
	else
		ViewManager.Instance:FlushView(GuideModuleName.Equipment, TabIndex.equipment_zhuanhua)
	end

	BaseRender.SetData(self, data)
end

function EquipmentTranssexStuffSlot:OnClick()
	if self.data ~= nil then
		self:SetData(nil)
	end
end

function EquipmentTranssexStuffSlot:OnFlush()
	self.item_cell:SetActive(self.data and self.data.item_id > 0)
	self.item_cell:SetData(self.data)
	-- self.item_cell:SetCellBgEnabled(false)
	if self.data and self.data.item_id > 0 then
		self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)
	else
		self.node_list["item_name"].text.text = ""
	end
end
----------------------------------------------------
-- 产品孔
----------------------------------------------------
EquipmentTranssexGridItem = EquipmentTranssexGridItem or BaseClass(BaseRender)
function EquipmentTranssexGridItem:__init()
	self.item_cell = ItemCell.New(self.node_list["product_item"])
	self.item_cell:SetActive(false)
	self.item_cell:SetItemTipFrom(ItemTip.FROM_ROLE_BAG_TRANSSEX)
end

function EquipmentTranssexGridItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function EquipmentTranssexGridItem:OnFlush()
	self.item_cell:SetActive(self.data and self.data.item_id > 0)
	self.item_cell:SetData(self.data)
	if self.data and self.data.item_id > 0 then
		self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)
	else
		self.node_list["item_name"].text.text = ""
	end
end