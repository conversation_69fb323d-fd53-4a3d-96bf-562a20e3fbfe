﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class SafeAreaAdpaterWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(SafeAreaAdpater), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("AdjustLayout", AdjustLayout);
		<PERSON><PERSON>Function("SetSafeAreaChangeCallBack", SetSafeAreaChangeCallBack);
		<PERSON><PERSON>unction("Bind", Bind);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AdjustLayout(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SafeAreaAdpater obj = (SafeAreaAdpater)ToLua.CheckObject<SafeAreaAdpater>(L, 1);
			obj.AdjustLayout();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSafeAreaChangeCallBack(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 1);
			SafeAreaAdpater.SetSafeAreaChangeCallBack(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Bind(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			SafeAreaAdpater o = SafeAreaAdpater.Bind(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

