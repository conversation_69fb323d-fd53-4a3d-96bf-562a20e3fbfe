TeamEquipFbSceneLogic = TeamEquipFbSceneLogic or BaseClass(CommonFbLogic)

function TeamEquipFbSceneLogic:__init()

end

function TeamEquipFbSceneLogic:__delete()
	
end

function TeamEquipFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- XuiBaseView.CloseAllView()
	FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.TEAM_EQUIP)
end

function TeamEquipFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end
function TeamEquipFbSceneLogic:Out()
	CommonFbLogic.Out(self)
end