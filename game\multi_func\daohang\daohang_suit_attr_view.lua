DaoHangSuitAttrView = DaoHangSuitAttrView or BaseClass(SafeBaseView)

function DaoHangSuitAttrView:__init()
    self:SetMaskBg(true)
	local daohang_bundle_name = "uis/view/multi_function_ui/daohang_prefab"
	self:AddViewResource(0, daohang_bundle_name, "layout_daohang_suit_attr")
end

function DaoHangSuitAttrView:LoadCallBack()
	if not self.attr_list then
        self.attr_list = AsyncListView.New(DaoHangSuitAttrItemRender, self.node_list.attr_list)
    end

	if not self.suit_attr_get_item then
		self.suit_attr_get_item = ItemCell.New(self.node_list.suit_attr_get_item)
		self.suit_attr_get_item:SetTipClickCallBack(BindTool.Bind(self.OnClickItem, self))
	end

	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model_root)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	self.can_receive = false
	self.suit_item_id = -1
end

function DaoHangSuitAttrView:ReleaseCallBack()
	if self.attr_list then
		self.attr_list:DeleteMe()
		self.attr_list = nil
	end

	if self.suit_attr_get_item then
		self.suit_attr_get_item:DeleteMe()
		self.suit_attr_get_item = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	self.can_receive = nil
	self.suit_item_id = nil
end

function DaoHangSuitAttrView:OnClickItem(data)
	if self.can_receive and self.suit_item_id > 0 then
		MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SUIT_ITEM, self.suit_item_id)
		return true
	end
end

function DaoHangSuitAttrView:OnFlush()
	local data_list = MultiFunctionWGData.Instance:GetDaoHangEquipSuitAttr()
	self.attr_list:SetDataList(data_list)

	local receive_cfg, is_get, can_get = MultiFunctionWGData.Instance:GetDaoHangEquipSuitShowItemData()
	if not IsEmptyTable(receive_cfg) then
		self.can_receive = not is_get and can_get
		self.suit_item_id = receive_cfg.suit_item_id
		self.suit_attr_get_item:SetData({item_id = receive_cfg.item_id})
		self.node_list.suit_attr_item_flag:CustomSetActive(self.can_receive)

		local color_str = Language.Common.ColorName[receive_cfg.quality] or ""
		local item_cfg = ItemWGData.Instance:GetItemConfig(receive_cfg.item_id)
		local name = item_cfg and item_cfg.name or ""
		self.node_list.suit_attr_get_desc.text.text = string.format(Language.Charm.DaoHangSuitActDesc, color_str, receive_cfg.star_level, name)

		self:FlushDaoHangSuitAttrModel(receive_cfg)
	end
end

function DaoHangSuitAttrView:FlushDaoHangSuitAttrModel(model_cfg)
	if IsEmptyTable(model_cfg) then
		return
	end

    local display_data = {}
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1

	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	display_data.render_type = model_show_type - 1
	display_data.model_click_func = function ()
		TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	end

	self.model_display:SetData(display_data)

	local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.model_root.rect, pos_x, pos_y)

	if model_cfg["display_scale"] then
		local scale = model_cfg["display_scale"]
		Transform.SetLocalScaleXYZ(self.node_list.model_root.transform, scale, scale, scale)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rotation_tab = string.split(model_cfg.rotation,"|")
		self.node_list.model_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

---------------------------------------------render------------------------------------------
DaoHangSuitAttrItemRender = DaoHangSuitAttrItemRender or BaseClass(BaseRender)

function DaoHangSuitAttrItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ResPath.GetMultiFunctionImg("a2_dh_suit_num" .. self.data.num)
    self.node_list.suit_num.image:LoadSprite(bundle, asset, function ()
        self.node_list.suit_num.image:SetNativeSize()
    end)

	local max_act_suit = self.data.max_act_suit or false
	self.node_list.max_act_suit:CustomSetActive(max_act_suit)
	self.node_list.current_title.text.text = self.data.current_title or ""
	self.node_list.next_title.text.text = self.data.next_title or ""

	for i = 1, 4 do
		local data = (self.data.attr_list or {})[i]
		local has_data = not IsEmptyTable(data)

		if has_data then
			self.node_list["attr_name" .. i].text.text = data.attr_name or ""
			self.node_list["attr_value" .. i].text.text = data.value_str or ""
			self.node_list["attr_add_value" .. i].text.text = data.add_value_str or ""
		end

		self.node_list["attr" .. i]:CustomSetActive(has_data)
	end
end