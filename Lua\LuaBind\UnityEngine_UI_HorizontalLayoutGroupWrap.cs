﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_UI_HorizontalLayoutGroupWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.UI.HorizontalLayoutGroup), typeof(UnityEngine.UI.HorizontalOrVerticalLayoutGroup));
		<PERSON><PERSON>unction("CalculateLayoutInputHorizontal", CalculateLayoutInputHorizontal);
		<PERSON><PERSON>unction("CalculateLayoutInputVertical", CalculateLayoutInputVertical);
		<PERSON>.RegFunction("SetLayoutHorizontal", SetLayoutHorizontal);
		<PERSON><PERSON>RegFunction("SetLayoutVertical", SetLayoutVertical);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CalculateLayoutInputHorizontal(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.HorizontalLayoutGroup obj = (UnityEngine.UI.HorizontalLayoutGroup)ToLua.CheckObject<UnityEngine.UI.HorizontalLayoutGroup>(L, 1);
			obj.CalculateLayoutInputHorizontal();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CalculateLayoutInputVertical(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.HorizontalLayoutGroup obj = (UnityEngine.UI.HorizontalLayoutGroup)ToLua.CheckObject<UnityEngine.UI.HorizontalLayoutGroup>(L, 1);
			obj.CalculateLayoutInputVertical();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLayoutHorizontal(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.HorizontalLayoutGroup obj = (UnityEngine.UI.HorizontalLayoutGroup)ToLua.CheckObject<UnityEngine.UI.HorizontalLayoutGroup>(L, 1);
			obj.SetLayoutHorizontal();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLayoutVertical(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.HorizontalLayoutGroup obj = (UnityEngine.UI.HorizontalLayoutGroup)ToLua.CheckObject<UnityEngine.UI.HorizontalLayoutGroup>(L, 1);
			obj.SetLayoutVertical();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

