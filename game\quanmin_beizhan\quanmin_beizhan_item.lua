
QuanMinBeiZhanDayItem = QuanMinBeiZhanDayItem or BaseClass(BaseRender)
function QuanMinBeiZhanDayItem:__init(instance)
	-- self.is_use_objpool = false
	if nil == self.root_node then
		local bundle, asset = ResPath.GetWidgets("day_item")
        local u3dobj = U3DObject(ResPoolMgr:TryGetGameObject(bundle, asset))
        if self.instance_parent then
            u3dobj.transform:SetParent(self.instance_parent)
        end

		self:SetInstance(u3dobj)
		self.is_use_objpool = true
	end

	if instance then
		self:SetInstanceParent(instance)
	end

	self.num_str = Language.QuanMinBeiZhan.LoginDayNum
end

function QuanMinBeiZhanDayItem:__delete()
	if self.is_use_objpool and not self:IsNil() then
		self.btn_yilingqu = nil
		self.select_bg = nil
		self.day_num = nil
		self.btn_item = nil
		self.cur_day_num = nil
		self.vip_tip = nil
		ResPoolMgr:Release(self.view.gameObject)
	end
end

function QuanMinBeiZhanDayItem:SetData(data,is_select)
	BaseRender.SetData(self, data)
	self.btn_yilingqu = self.node_list.btn_yilingqu
	self.select_bg = self.node_list.select_bg
	self.day_num = self.node_list.day_num
	self.btn_item = self.node_list.btn_item
	self.vip_tip = self.node_list.vip_tip
	self.cur_day_num = self.node_list.cur_day_num

	self.day_num.text.text = string.format(Language.QuanMinBeiZhan.LoginStr1, self.num_str[data.index])
	self.cur_day_num.text.text = string.format(Language.QuanMinBeiZhan.LoginStr1, self.num_str[data.index])

	local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
	if data.vip_level > 0 then
		self.btn_yilingqu:SetActive(data.common_gift_state == ActivityRewardState.YLQ and data.special_gift_state == ActivityRewardState.YLQ)
		self.vip_tip:SetActive(data.common_gift_state == ActivityRewardState.YLQ and cur_vip_level < data.vip_level)
		self.vip_tip.text.text = string.format(Language.QuanMinBeiZhan.LoginStr2, data.vip_level)
	else
		self.btn_yilingqu:SetActive(data.common_gift_state == ActivityRewardState.YLQ)
	end

	self.btn_item.button:AddClickListener(BindTool.Bind1(self.OnBtnItemClickHnadler, self))

	if is_select then
		self.select_bg:SetActive(true)
		self.btn_item.transform.localScale = Vector3(1.2,1.2,1.2)
		
		QuanMinBeiZhanWGData.Instance:SetSelectDayItem(self.btn_item, self.select_bg)
	else
		self.btn_item.transform.localScale = Vector3(1,1,1)
		self.select_bg:SetActive(false)
	end

	local cur_login_day = QuanMinBeiZhanWGData.Instance:GetLoginDyaIndex()
	self.day_num:SetActive(cur_login_day ~= data.index)
	self.cur_day_num:SetActive(cur_login_day == data.index)
	self.node_list.cur_reward_bg:SetActive((data.special_gift_state == ActivityRewardState.KLQ or data.common_gift_state == ActivityRewardState.KLQ))
end

function QuanMinBeiZhanDayItem:SetSelectBg(is_select)
	if is_select then
		self.select_bg:SetActive(true)
		self.btn_item.transform.localScale = Vector3(1.2,1.2,1.2)
		QuanMinBeiZhanWGData.Instance:SetSelectDayItem(self.btn_item, self.select_bg)
	else
		self.btn_item.transform.localScale = Vector3(1,1,1)
		self.select_bg:SetActive(false)
	end
end

function QuanMinBeiZhanDayItem:OnBtnItemClickHnadler(sender)
	local old_item, old_select_bg = QuanMinBeiZhanWGData.Instance:GetDyaItemSelect()
	if old_item ~= nil and old_select_bg ~= nil then
		old_select_bg:SetActive(false)
		old_item.transform.localScale = Vector3(1,1,1)
	end

	self.select_bg:SetActive(true)
	self.btn_item.transform.localScale = Vector3(1.2,1.2,1.2)
	QuanMinBeiZhanWGData.Instance:SetSelectDayItem(self.btn_item, self.select_bg)
	QuanMinBeiZhanWGCtrl.Instance:ShowLoginDayReward(self.data.index)
end

QuanMinCommonItemRender = QuanMinCommonItemRender or BaseClass(BaseRender)
function QuanMinCommonItemRender:__init()
	
end

function QuanMinCommonItemRender:LoadCallBack()
	self.cell = nil
end

function QuanMinCommonItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function QuanMinCommonItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function QuanMinCommonItemRender:OnFlush()
	if not self.data then
		return
	end 
	if self.cell == nil then
		self.cell = ItemCell.New(self.node_list["pivot"])
	end
	
	self.cell:SetData(self.data)
end

QuanMinCommonBiaoQianItemRender = QuanMinCommonBiaoQianItemRender or BaseClass(BaseRender)
function QuanMinCommonBiaoQianItemRender:__init()
	
end

function QuanMinCommonBiaoQianItemRender:LoadCallBack()
	self.cell = nil
end

function QuanMinCommonBiaoQianItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function QuanMinCommonBiaoQianItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function QuanMinCommonBiaoQianItemRender:OnFlush()
	if not self.data then
		return
	end 
	if self.cell == nil then
		self.cell = ItemCell.New(self.node_list["pivot"])
	end
	
	self.cell:SetData(self.data.reward)
	if self.node_list.biaoqian ~= nil then
		if self.data.biaoqian_name then
			self.node_list.biaoqian:SetActive(true)
			--self.node_list.biaoqian_name.text.text = self.data.biaoqian_name
			self.node_list.biaoqian_name.image:LoadSprite(ResPath.GetQuanMinBeiZhanImagePath(self.data.biaoqian_name))
			self.node_list.biaoqian_name.image:SetNativeSize()
		else
			self.node_list.biaoqian:SetActive(false)
		end
	end
end

QuanMinLoadBXItem = QuanMinLoadBXItem or BaseClass(BaseRender)

function QuanMinLoadBXItem:__init(instance)
	local bundle, asset = "uis/view/quanmin_beizhan_ui_prefab", "bx_list_item"
	self:LoadAsset(bundle, asset, instance.transform)
end

function QuanMinLoadBXItem:LoadCallBack()
	self:AddClickEventListener(BindTool.Bind(self.OnClickoHnadler,self))
end

function QuanMinLoadBXItem:OnFlush()
	if not self.data then 
		return 
	end

	self.node_list.baoxaing_open.image:LoadSprite(ResPath.GetQuanMinBeiZhanImagePath("a1_lh_sqrw" .. self.data.ID))
	self.node_list.baoxaing_close.image:LoadSprite(ResPath.GetQuanMinBeiZhanImagePath("a1_lh_sqrw" .. self.data.ID))

	self.node_list.score_label.text.text = self.data.jifen .. "点"
	local cur_state = QuanMinBeiZhanWGData.Instance:GetBXState(self.data.ID)
	-- 0 未完成 1 已完成 2 已领取
	self.node_list.red_point:SetActive(cur_state == ActivityRewardState.KLQ)
	self.node_list.baoxaing_close:SetActive(cur_state == ActivityRewardState.BKL)
	self.node_list.baoxaing_open:SetActive(cur_state == ActivityRewardState.KLQ or cur_state == ActivityRewardState.YLQ)
	self.node_list.baoxaing_ylq:SetActive(cur_state ~= ActivityRewardState.YLQ)
end

function QuanMinLoadBXItem:OnClickoHnadler()
	local state = QuanMinBeiZhanWGData.Instance:GetBXState(self.data.ID)
	if state == ActivityRewardState.KLQ then
		QuanMinBeiZhanWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_BEIZHAN_WOYAOLONGHUN, WOYAOSHENQI_OP_TYPE.DANGWEI_REWARD, self.data.ID)
	else
		QuanMinBeiZhanWGCtrl.Instance:OpenLongHunRewardTips()
	end
end