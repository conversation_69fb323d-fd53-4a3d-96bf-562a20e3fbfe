M = {}

UnityMaterial = typeof(UnityEngine.Material)
local UnityTexture = typeof(UnityEngine.Texture)
local TypeUnityPrefab = typeof(UnityEngine.GameObject)
local TypeOfActorQingGongObject = typeof(ActorQingGongObject)
local RuntimeAnimatorController = typeof(UnityEngine.RuntimeAnimatorController)
local TypeOfVolumeProfile = typeof(UnityEngine.Rendering.VolumeProfile)
local loader

M._class_type = true
function M.init()
	-- 需要将shader材质球预加载，解决首次显示新shader会闪蓝色问题
	loader = AllocResAsyncLoader(M, "SrpEffecrCf_PreloadMat_loader")
	loader:Load("misc/material", "SrpEffecrCf_PreloadMat", UnityMaterial,
		function(obj)
			M["SrpEffecrCf_PreloadMat"] = obj
		end)

	loader = AllocResAsyncLoader(M, "SrpGrassCf_PreloadMat_loader")
	loader:Load("misc/material", "SrpGrassCf_PreloadMat", UnityMaterial,
		function(obj)
			M["SrpGrassCf_PreloadMat"] = obj
		end)

	loader = AllocResAsyncLoader(M, "SrpMapPbrCf_PreloadMat_loader")
	loader:Load("misc/material", "SrpMapPbrCf_PreloadMat", UnityMaterial,
		function(obj)
			M["SrpMapPbrCf_PreloadMat"] = obj
		end)

	loader = AllocResAsyncLoader(M, "SrpRolePbrCf_PreloadMat_loader")
	loader:Load("misc/material", "SrpRolePbrCf_PreloadMat", UnityMaterial,
		function(obj)
			M["SrpRolePbrCf_PreloadMat"] = obj
		end)

	loader = AllocResAsyncLoader(M, "SrpDistortMask_PreloadMat_loader")
	loader:Load("misc/material", "SrpDistortMask_PreloadMat", UnityMaterial,
		function(obj)
			M["SrpDistortMask_PreloadMat"] = obj
		end)

	loader = AllocResAsyncLoader(M, "RoleOcclusion")
	loader:Load("misc/material", "RoleOcclusion", UnityMaterial,
		function(obj)
			M["role_occlusion"] = obj
		end)
	
	for i = 1, 4 do
		for j = 1, 4 do
			loader = AllocResAsyncLoader(M, string.format("QingGongObject%s_%s", i, j))
			loader:Load("misc/qinggong", string.format("QingGongObject%s_%s", i, j), TypeOfActorQingGongObject,
				function(obj)
					M[string.format("QingGongObject%s_%s", i, j)] = obj
				end)
		end
	end

	loader = AllocResAsyncLoader(M, "QingGongObject_back")
	loader:Load("misc/qinggong", "QingGongObject_back", TypeOfActorQingGongObject,
		function(obj)
			M["QingGongObject_back"] = obj
		end)

	loader = AllocResAsyncLoader(M, "QingGongObject_guide")
	loader:Load("misc/qinggong", "QingGongObject_guide", TypeOfActorQingGongObject,
		function(obj)
			M["QingGongObject_guide"] = obj
		end)

    loader = AllocResAsyncLoader(M, "PostEffects")
    loader:Load("scenes/posteffects", "common_Profile", TypeOfVolumeProfile,
        function(obj)
            if nil ~= obj and not IsNil(obj) then
                M["common_Profile"] = obj
            end
        end)

	loader = AllocResAsyncLoader(M, "PostEffects_UI")
    loader:Load("scenes/posteffects", "UI_Profile", TypeOfVolumeProfile,
        function(obj)
            if nil ~= obj and not IsNil(obj) then
                M["UI_Profile"] = obj
            end
        end)

    
	loader = AllocResAsyncLoader(M, "ScreenEffect")
    loader:Load("misc_prefab", "ScreenEffect", TypeUnityPrefab,
        function(obj)
            if nil ~= obj and not IsNil(obj) then
            	local clone = GameObject.Instantiate(obj)
            	clone.name = obj.name
                clone.transform:SetParent(GameRoot.Instance.transform)
                GameRoot.Instance:GetComponent(typeof(SpiritChangePlayer)).enabled = true
            end
        end)

    -- loader = AllocResAsyncLoader(M, "NoiseTex")
	-- loader:Load("misc/texture", "NoiseTex.png", UnityTexture,
	-- 	function(obj)
	-- 		M["NoiseTex"] = obj
	-- 	end)
end

return M