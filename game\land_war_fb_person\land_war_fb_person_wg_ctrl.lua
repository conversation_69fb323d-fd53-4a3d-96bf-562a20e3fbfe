require("game/land_war_fb_person/land_war_fb_person_view")
require("game/land_war_fb_person/land_war_fb_person_wg_data")
require("game/land_war_fb_person/land_war_fb_person_description_view")
require("game/land_war_fb_person/land_war_fb_person_boss_info_view")
require("game/land_war_fb_person/land_war_fb_person_task_view")


LandWarFbPersonWGCtrl = LandWarFbPersonWGCtrl or BaseClass(BaseWGCtrl)

function LandWarFbPersonWGCtrl:__init()
	if LandWarFbPersonWGCtrl.Instance ~= nil then
		ErrorLog("[LandWarFbPersonWGCtrl] attempt to create singleton twice!")
		return
	end

	LandWarFbPersonWGCtrl.Instance = self

    self.view = LandWarFbPersonView.New(GuideModuleName.LandWarFbPersonView)
    self.data = LandWarFbPersonWGData.New()
	self.boss_info_view = LandWarFbPersonBossInfoView.New(GuideModuleName.LandWarFbPersonBossInfoView)
	self.description_view = LandWarFbPersonDescriptionView.New()
	self.task_view = LandWarFbPersonTaskView.New()
	self:RegisterAllProtocols()
end

function LandWarFbPersonWGCtrl:__delete()
	LandWarFbPersonWGCtrl.Instance = nil

    if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

    if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	
    if self.boss_info_view then
		self.boss_info_view:DeleteMe()
		self.boss_info_view = nil
	end

	if self.description_view then
		self.description_view:DeleteMe()
		self.description_view = nil
	end

	if self.task_view then
		self.task_view:DeleteMe()
		self.task_view = nil
	end
end

function LandWarFbPersonWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSLandWarFBOperate)
    self:RegisterProtocol(SCLandWarFBBaseInfo, "OnSCLandWarFBBaseInfo")
end

function LandWarFbPersonWGCtrl:SendLandWarFBOperate(opera_type, param1, param2, param3, param4)
    local protocol = ProtocolPool.Instance:GetProtocol(CSLandWarFBOperate)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol:EncodeAndSend()
end

function LandWarFbPersonWGCtrl:OnSCLandWarFBBaseInfo(protocol)
    --print_error("----------OnSCLandWarFBBaseInfo----------", protocol)
	local old_stage = self.data:GetCurStage()
	-- local rewrd_flag = self.data:GetPassRewardIsGet()
	local old_get_all_paa_reward = self.data:IsGetAllPassReward()
	self.data:SetBaseInfo(protocol)
	local new_get_all_paa_reward = self.data:IsGetAllPassReward()
	
	if self.view and self.view:IsOpen() then
        self.view:Flush()
    end

	if self.task_view and self.task_view:IsOpen() then
        self.task_view:Flush()
    end
	
	local scene_type = Scene.Instance:GetSceneType()
	if old_stage < protocol.stage and scene_type == SceneType.SCENE_TYPE_LAND_WAR_FB then
		ViewManager.Instance:Open(GuideModuleName.LandWarFbPersonView)
		FunctionGuide.Instance:TriggerLandWarFbPersonGoGuide(protocol.stage)
	-- elseif not rewrd_flag and protocol.pass_reward_flag == 1 and self.view:IsOpen() then
	elseif not old_get_all_paa_reward and new_get_all_paa_reward and self.view:IsOpen() then
		FunctionGuide.Instance:TriggerLandWarFbPersonGoGuide(protocol.stage, GuideTriggerType.LandWarFbPersonStageFour)
	end

	RemindManager.Instance:Fire(RemindName.LandWarFbPerson)
end

function LandWarFbPersonWGCtrl:OpenGamePlayDescriptionView()
    if self.description_view then
        self.description_view:Open()
    end
end

function LandWarFbPersonWGCtrl:OpenBossInfonView(data)
    if self.boss_info_view then
        self.boss_info_view:SetDataAndOpen(data)
    end
end

function LandWarFbPersonWGCtrl:GetFBTaskView()
    return self.task_view
end

function LandWarFbPersonWGCtrl:OpenFBTaskView()
    if self.task_view then
		self.task_view:Open()
	end
end

function LandWarFbPersonWGCtrl:CloseFBTaskView()
    if self.task_view and self.task_view:IsOpen() then
		self.task_view:Close()
	end
end

function LandWarFbPersonWGCtrl:OnBossHurtInfo(protocol)
    if self.task_view and self.task_view:IsOpen() then
        self.task_view:Flush(0, "rank_info", protocol)
    end
end
