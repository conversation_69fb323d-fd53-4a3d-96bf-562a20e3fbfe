EquipmentBagResloveView = EquipmentBagResloveView or BaseClass(SafeBaseView)
function EquipmentBagResloveView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
    self.view_cache_time = 0

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(1000, 640)})
	self:AddViewResource(0, "uis/view/rolebag_ui/equip_target_prefab", "layout_equipment_reslove_view")
end

function EquipmentBagResloveView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.EquipTarget.ResloveTitle

    if not self.item_grid then
        self.item_grid = EquipmentBagResloveGrid.New()
        self.item_grid:SetStartZeroIndex(false)
        self.item_grid:SetIsMultiSelect(true)
        self.item_grid:CreateCells({
            col = 5,
            cell_count = 40,
            list_view = self.node_list["item_grid"],
            itemRender = MeltEquipCell,
            change_cells_num = 2,
        })
        self.item_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectItemCB, self))
    end

    if not self.get_list_view then
        self.get_list_view = AsyncListView.New(EquipmentBagResloveGetItem, self.node_list["get_list_view"])
    end

    self.color_select_list = {}
    for i = 1, 4 do
        XUI.AddClickEventListener(self.node_list["quality_toggle_" .. i], BindTool.Bind(self.OnSelectColor, self, i + 3)) --开始是橙色
    end
    XUI.AddClickEventListener(self.node_list["btn_reslove"], BindTool.Bind(self.OnClickReslove, self))
    self.is_on = false
    XUI.AddClickEventListener(self.node_list.btn_select_color, BindTool.Bind(self.OnClickSelectColor, self))

    self.is_first_load = true
    self.node_list["quality_toggle_1"].toggle.isOn = true
    self.node_list.cur_color_text.text.text = Language.Bag.NameList3[1] -- 开始默认显示橙色
end

function EquipmentBagResloveView:ReleaseCallBack()
    if self.item_grid then
        self.item_grid:DeleteMe()
        self.item_grid = nil
    end

    if self.get_list_view then
        self.get_list_view:DeleteMe()
        self.get_list_view = nil
    end

    self.is_first_load = nil
    self.grid_select_list = nil
    self.color_select_list = {}
end

function EquipmentBagResloveView:OnBagSelectItemCB(cell)
    self:FlushGetItemView()
end

function EquipmentBagResloveView:OnFlush()
    local bag_reslove_list = EquipTargetWGData.Instance:GetResloveBagList()
    local is_have_data = not IsEmptyTable(bag_reslove_list)
    self.node_list.img_no_data:SetActive(not is_have_data)
    self.node_list.item_grid:SetActive(is_have_data)
    self.item_grid:SetDataList(bag_reslove_list)
    self.item_grid:SetColorSelcet(self.color_select_list)
    self:FlushGetItemView()
end

function EquipmentBagResloveView:FlushGetItemView()
    self.grid_select_list = self.item_grid:GetAllSelectCell()
    local reslove_cfg = ConfigManager.Instance:GetAutoConfig("equip_jicheng_cfg_auto").decompose
    local get_item_list = {}
    local item_id

    for k,v in pairs(self.grid_select_list) do
        local cfg = reslove_cfg[v.item_id]
        if cfg then
            for i, j in pairs(cfg.decompos_item) do
                item_id = j.item_id
                get_item_list[item_id] = get_item_list[item_id] or 0
                get_item_list[item_id] = get_item_list[item_id] + j.num * v.num
            end
        end
    end

    local show_list = {}
    for k,v in pairs(get_item_list) do
        table.insert(show_list, {item_id = k, get_num = v})
    end

    if not IsEmptyTable(show_list) then
        SortTools.SortAsc(show_list, "item_id")
    end

    self.node_list["no_get_tips"]:SetActive(#show_list == 0)
    self.get_list_view:SetDataList(show_list)
end

function EquipmentBagResloveView:OnClickSelectColor()
    self.is_on = not self.is_on
    self.node_list.color_arrow_down:SetActive(self.is_on)
    self.node_list.color_arrow_up:SetActive(not self.is_on)
    self.node_list.color_list_part:SetActive(self.is_on) -- 打开颜色选中列表
end


function EquipmentBagResloveView:OnSelectColor(color, is_on)
    if type(color) == "number" then
        self.color_select_list[color] = is_on
        self.node_list.cur_color_text.text.text = Language.Bag.NameList3[color - 3]
    elseif type(color) == "table" then
        for k,v in pairs(color) do
            self.color_select_list[v] = is_on
        end
    end

    if self.is_first_load then
        self.is_first_load = nil
        return
    end

    if self.item_grid then
        self.item_grid:SetColorSelcet(self.color_select_list)
    end

    -- 选中后的处理
    self.is_on = false
    self.node_list.color_list_part:SetActive(self.is_on)
    self.node_list.color_arrow_down:SetActive(self.is_on)
    self.node_list.color_arrow_up:SetActive(not self.is_on)

    self:FlushGetItemView()
end

function EquipmentBagResloveView:OnClickReslove()
	local select_list = self.item_grid:GetAllSelectCell()
	if IsEmptyTable(select_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.EquipTarget.ResloveError)
		return
	end

	local reslove_list = {}
	for k,v in pairs(select_list) do
        local data = {
            item_id = v.item_id,
            bag_index = v.index,
            num = v.num or 1,
        }
		table.insert(reslove_list, data)
	end

    self.item_grid:CancleAllSelectCell()
    EquipTargetWGCtrl.Instance:SendEIResloveItemList(reslove_list)
end

-------------------------
-- EquipmentBagResloveGrid
---------------------------------------------------------
EquipmentBagResloveGrid = EquipmentBagResloveGrid or BaseClass(AsyncBaseGrid)
function EquipmentBagResloveGrid:IsSelectMultiNumLimit(cell_index)
    if not self.select_tab[1][cell_index] then
        if self.cur_multi_select_num >= 50 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.EquipTarget.ResloveLimit)
            return true
        end
    end

    return false
end

function EquipmentBagResloveGrid:SetColorSelcet(select_color_list)
    if IsEmptyTable(select_color_list) then
        return
    end

    local color
    self.select_tab[1] = {}
    self.cur_multi_select_num = 0
    local data = self.cell_data_list
    for i = 1, self.has_data_max_index do
        color = 0
        if data[i] then
            local item_cfg = ItemWGData.Instance:GetItemConfig(data[i].item_id)
            color = item_cfg and item_cfg.color
            if select_color_list[color] then
                if self.cur_multi_select_num < 50 then
                    self.cur_multi_select_num = self.cur_multi_select_num + 1
                    self.select_tab[1][i] = true
                else
                    break
                end
            end
        end
    end

    self:__DoRefreshSelectState()
end


---EquipmentBagResloveGetItem
EquipmentBagResloveGetItem = EquipmentBagResloveGetItem or BaseClass(BaseRender)
function EquipmentBagResloveGetItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.can_get_item)
end

function EquipmentBagResloveGetItem:__delete()
    if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function EquipmentBagResloveGetItem:OnFlush()
    if self.data == nil then
        return
    end

    self.item_cell:SetData({item_id = self.data.item_id})
    self.node_list["can_get_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)
    self.node_list["can_get_num"].text.text = self.data.get_num
end