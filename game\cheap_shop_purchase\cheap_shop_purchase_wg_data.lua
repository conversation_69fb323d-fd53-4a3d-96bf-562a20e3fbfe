CheapShopPurchaseWGData = CheapShopPurchaseWGData or BaseClass()

function CheapShopPurchaseWGData:__init()
	if CheapShopPurchaseWGData.Instance then
		print_error("[CheapShopPurchaseWGData] Attempt to create singleton twice!")
		return
	end

	CheapShopPurchaseWGData.Instance = self
    self:InitConfig()
    self.grade = 0 -- 档次
	self.rmb_buy_times_list = {}
end

function CheapShopPurchaseWGData:__delete()
    self.grade = nil
	self.rmb_buy_times_list = nil

    CheapShopPurchaseWGData.Instance = nil
end

function CheapShopPurchaseWGData:InitConfig()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_cheap_shop_auto")
	self.rmb_buy_seq_cfg = ListToMapList(cfg.rmb_buy, "grade", "activity_day")
end

function CheapShopPurchaseWGData:SetAllBuyInfo(protocol)
	self.grade = protocol.grade
	self.rmb_buy_times_list = protocol.rmb_buy_times_list
end

function CheapShopPurchaseWGData:GetBuyTimeBySeq(seq)
    for k, v in pairs(self.rmb_buy_times_list) do
        if seq == k then
            return v
        end
    end
    
    return 0
end

function CheapShopPurchaseWGData:GetCurDayBuyList()
    local act_day = 1               -- 2023/11/24 经协商 要求活动天数只配置为1
    local cur_grade_cfg = self.rmb_buy_seq_cfg[self.grade]

    if IsEmptyTable(cur_grade_cfg) then
        print("===========配置档次不存在self.grade=", self.grade)
		return {}
    end

    if not cur_grade_cfg[act_day] then
        print("===========配置天数不存在day", act_day)
        return {}
    end

    return cur_grade_cfg[act_day]
end

function CheapShopPurchaseWGData:GetGradeRewardList()
	local data_list = {}
    local all_buy_cfg = {}
	local cur_grade_cfg = self:GetCurDayBuyList()
	if IsEmptyTable(cur_grade_cfg) then
		return data_list, all_buy_cfg
	end

    local fun = function (cfg)
        local temp = {}
        temp.seq = cfg.seq
        temp.rmb_type = cfg.rmb_type
        temp.rmb_seq = cfg.rmb_seq
        temp.rmb_price = cfg.rmb_price
        temp.discount = cfg.discount
        temp.buy_times = cfg.buy_times
        temp.is_all_buy = cfg.is_all_buy
        temp.reward_item = cfg.reward_item
        temp.name = cfg.name
        temp.count_color = cfg.count_color
        temp.show_icon_ground = cfg.show_icon_ground
        temp.cur_buy_times = self:GetBuyTimeBySeq(cfg.seq)
        return temp
    end

    for k, v in pairs(cur_grade_cfg) do
		if v.is_all_buy == 0 then
           local info = fun(v)
           data_list[#data_list + 1] = info
        elseif v.is_all_buy == 1 then
            all_buy_cfg = fun(v)
		end
	end

    if not IsEmptyTable(data_list) then   
        for k, v in pairs(data_list) do
            v.is_buy_all = (v.buy_times - v.cur_buy_times > 0) and 0 or 1
        end

        table.sort(data_list, SortTools.KeyLowerSorters("is_buy_all", "seq"))
    end

	return data_list, all_buy_cfg
end