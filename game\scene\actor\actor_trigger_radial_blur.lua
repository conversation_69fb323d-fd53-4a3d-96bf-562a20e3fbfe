ActorTriggerRadialBlur = ActorTriggerRadialBlur or BaseClass(ActorTriggerBase)

function ActorTriggerRadialBlur:__init()
	self.transform = nil
	self.enabled = true

	self.radial_blur_data = nil
end

function ActorTriggerRadialBlur:InitData(anima_name)
	self.enabled = true

	self.radial_blur_data = nil
	self:Reset()
end

function ActorTriggerRadialBlur:Reset()
	self.radial_blur_data = nil
	ActorTriggerBase.Reset(self)
end

function ActorTriggerRadialBlur:__delete()
	self.radial_blur_data = nil
end


function ActorTriggerRadialBlur:Init(radial_blur_data)
	self.radial_blur_data = radial_blur_data
    self.delay = radial_blur_data.delay
	self.anima_name = radial_blur_data.eventName

end

function ActorTriggerRadialBlur:Enalbed(value)
	if value == nil then
		return self.enabled
	end

	self.enabled = value
end

function ActorTriggerRadialBlur:OnEventTriggered(source, target, stateInfo)
    if not self.enabled or not self.radial_blur_data then
        return
    end

    local data = self.radial_blur_data
    local playTransform = nil

    if data.isRole then
        if source ~= nil then
            if data.referenceNodeHierarchyPath ~= nil and data.referenceNodeHierarchyPath ~= "" then
                playTransform = source.transform:Find(data.referenceNodeHierarchyPath)
            else
                playTransform = source.transform.transform
            end
        end
    else
        if target ~= nil then
            playTransform = target.transform.transform
        end
    end

    if not playTransform then
        return
    end

    if Scene ~= nil and Scene.Instance ~= nil then
        Scene.Instance:DoRadialBlurByWorldPos(playTransform.position, data.riseTime, data.holdTime, data.fallTime, data.strength)
    end
end
