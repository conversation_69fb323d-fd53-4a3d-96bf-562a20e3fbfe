CatExploreWGData = CatExploreWGData or BaseClass()
CatExploreWGData.MAX_STEP_REWARD_COUNT = 21 -- 步数奖励最大值

function CatExploreWGData:__init()
	if CatExploreWGData.Instance ~= nil then
		<PERSON>rrorLog("[CatExploreWGData] attempt to create singleton twice!")
		return
	end

	CatExploreWGData.Instance = self

	self:InitCfg()
	self.grade = 1 -- 档次
	self.step = 0 --步数
	self.step_reward_flag = {} -- 步数奖励领取状态
	self.task_list = {}        -- 服务端下发得任务状态

	RemindManager.Instance:Register(RemindName.CatExplore, BindTool.Bind(self.ShowCatExploreRemind, self))
end

function CatExploreWGData:__delete()
	CatExploreWGData.Instance = nil
	self.grade = nil
	self.step = nil
	self.step_reward_flag = nil
	self.task_list = nil
	self.grade = 1
	self.step = 0
	RemindManager.Instance:UnRegister(RemindName.CatExplore)
end

function CatExploreWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_cat_venture_auto")
	self.reward_cfg = ListToMapList(cfg.reward, "grade")
	self.consume_cfg = ListToMapList(cfg.consume, "grade")
	self.task_cfg = ListToMapList(cfg.task, "grade", "task_type")
	self.other_cfg = cfg.other[1]
end


--全部数据
function CatExploreWGData:SetAllCatExploreInfo(protocol)
	self.grade = protocol.grade
	self.step = protocol.step
	self.step_reward_flag = protocol.step_reward_flag
	self.task_list = protocol.task_list
end

-- 单个任务信息改变
function CatExploreWGData:SetSingleTaskInfo(protocol)
  	local data = protocol.change_data
  	for i, v in pairs(self.task_list ) do
  		if v.task_id == data.task_id then
  			self.task_list[v.task_id] = data
  			return
  		end
  	end
end

function CatExploreWGData:GetAllTaskList()
	local task_data_list = {}
	local cur_grade_task_cfg = self.task_cfg[self.grade]
	if not IsEmptyTable(cur_grade_task_cfg) then
		for k, v in pairs(cur_grade_task_cfg) do
			local cfg = v[#v]
			if #v > 1 then
				for k1, v1 in ipairs(v) do
					if self.task_list[v1.task_id] and self.task_list[v1.task_id].status ~= REWARD_STATE_TYPE.FINISH then
						cfg = v1
						break
					end
				end
			end
	
			local task_type = cfg.task_type
			local task_id = cfg.task_id
			task_data_list[task_type] = {}
			task_data_list[task_type].task_type = cfg.task_type
			task_data_list[task_type].task_id = cfg.task_id
			task_data_list[task_type].item_list = cfg.item_list
			task_data_list[task_type].des = cfg.des
			task_data_list[task_type].target = cfg.target
			task_data_list[task_type].open_panel = cfg.open_panel
			local status = self.task_list[cfg.task_id] and self.task_list[cfg.task_id].status or REWARD_STATE_TYPE.UNDONE
			task_data_list[task_type].status = status
			task_data_list[task_type].progress_num_flag = self.task_list[cfg.task_id] and self.task_list[cfg.task_id].progress_num_flag or 0
	
			local sort_index = 0
			if status == REWARD_STATE_TYPE.UNDONE then
				task_data_list[task_type].sort_index = 100
			elseif status == REWARD_STATE_TYPE.CAN_FETCH then
				task_data_list[task_type].sort_index = 10
			elseif status == REWARD_STATE_TYPE.FINISH then
				task_data_list[task_type].sort_index = 1000
			end
		end
	end

	table.sort(task_data_list, SortTools.KeyLowerSorters("sort_index", "task_id"))
	return task_data_list
end

function CatExploreWGData:GetOtherCfg()
	return self.other_cfg
end

function CatExploreWGData:GetAllStepRewardCfg()
	return self.reward_cfg[self.grade] or {}
end

function CatExploreWGData:GetCurStep()  --获取当前步数
	return self.step
end

function CatExploreWGData:SetInfoFlag()  --设置是否获取过一次协议数据
	self.pro_flag = true
end

function CatExploreWGData:GetInfoFlag()  --
	return self.pro_flag
end

function CatExploreWGData:GetStepCostNum(step) -- 根据步数获取消耗数量
	local cur_grade_consume_cfg = self.consume_cfg[self.grade] or {}
	local count = 0
	for i, v in ipairs(cur_grade_consume_cfg) do
		if step == v.step then
			count = v.cost_item_num
		end
	end

	return count
end

function CatExploreWGData:GetRewardStateBystep(step) -- 获取奖励状态
	return self.step_reward_flag[step] and self.step_reward_flag[step] == 1
end

function CatExploreWGData:GetAllRewardState()
	return self.step_reward_flag
end

---红点--
function CatExploreWGData:ShowCatExploreRemind()
	if self:ShowStepBtnRemind() then
		return 1
	end

	if self:ShowTaskRemind() then
		return 1
	end

	return 0
end

---按钮红点--
function CatExploreWGData:ShowStepBtnRemind()
	local remind = false
	local cur_step = self:GetCurStep()
	if cur_step < CatExploreWGData.MAX_STEP_REWARD_COUNT then
		local other_cfg = self:GetOtherCfg()
	    local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg.cost_item_id)
	    local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
	    local cost_num = self:GetStepCostNum(cur_step + 1)
	    if item_num >= cost_num then
	    	remind = true
	    end
	end

	return remind
end

--任务红点
function CatExploreWGData:ShowTaskRemind()
	local remind = false
	for i, v in ipairs(self.task_list) do
		if v.status == REWARD_STATE_TYPE.CAN_FETCH then
			remind = true
			break
		end
	end

	return remind
end

