require("game/today_special/today_special_wg_data")
require("game/today_special/today_special_view")

TodaySpecialWGCtrl = TodaySpecialWGCtrl or BaseClass(BaseWGCtrl)
function TodaySpecialWGCtrl:__init()
	if TodaySpecialWGCtrl.Instance then
		<PERSON>rror<PERSON><PERSON>("[TodaySpecialWGCtrl] attempt to create singleton twice!")
		return
	end

	TodaySpecialWGCtrl.Instance = self
	self.data = TodaySpecialWGData.New()
    self.view = TodaySpecialView.New(GuideModuleName.TodaySpecialView)
  
    self:RegisterAllProtocols()
    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function TodaySpecialWGCtrl:__delete()
	TodaySpecialWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil
end

function TodaySpecialWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOALimitRmbBuy2Info,"OnSCOALimitRmbBuy2Info")
end

function TodaySpecialWGCtrl:OnSCOALimitRmbBuy2Info(protocol)
	--print_error("===========数据============", protocol)
	self.data:SetAllBuyInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TodaySpecialView)
end

function TodaySpecialWGCtrl:ReqActivityLimitBuy2Info(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY2
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function TodaySpecialWGCtrl:OnPassDay()
    GlobalTimerQuest:AddDelayTimer(function()
		self:ReqActivityLimitBuy2Info(OA_LIMIT_RMB_BUY2_OPERATE_TYPE.INFO)
	end, 2)
end
