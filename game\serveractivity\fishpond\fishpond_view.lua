function SocietyView:__InitFishPondView()
	self.change_callback = BindTool.Bind1(self.RoleDataAttrChangeCallBack, self)
end

function SocietyView:InitFishPondView()
	self.enter_time = 0
	self.create_cd = 0.5
	self.fish_list = {}
	self.protect_fish_list = {} --守卫鱼
	self.all_fish_list = {}  --全部的鱼
	self.bullet_list = {}
	self.bullet_x, self.bullet_y = 540, 50
	self.success_fishing = nil 		--捕鱼特效
	self.acc = 1   --加速度
	self.extend_success_eff = nil   --鱼塘扩展成功特效
	self.button_fire = self.node_list["Buttonfire"]
	self.bullet_num = -1 		--发射子弹数

	self.click_point = nil 		--点击池塘
	self.is_shoot = false
	self.bubble_list = {3108,3109,3110,3114}

	self.add_fish_position = nil



	self.node_list["layout_fish_shop"]:SetActive(false)
	self.layout_fishpond_down = self.node_list["layout_fishpond_down"]
	self.layout_fish_shop = self.node_list["layout_fish_shop"]
	self.layout_fishing_tip = self.node_list["layout_fishing_tip"]
	self:InitShopView()
	--事件注册
	self.node_list["ClickRange"].event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.FireBullet, self))
	XUI.AddClickEventListener(self.node_list["btn_fishpond_attr"], BindTool.Bind1(self.OpenFishpondAttr, self)) 	--打开鱼塘信息面板
	XUI.AddClickEventListener(self.node_list["btn_backhome"], BindTool.Bind1(self.ClickBackBtnHandler, self)) 
	XUI.AddClickEventListener(self.node_list["btn_fishpond_bullet"], BindTool.Bind1(self.CliskBulletHandler, self)) --点击子弹
	XUI.AddClickEventListener(self.node_list["btn_friend_fishpond"], BindTool.Bind1(self.OpenFriendList, self)) --好友列表
	XUI.AddClickEventListener(self.button_fire, BindTool.Bind1(self.CanNotCatchFishTip, self)) --在自己家无法捕鱼提醒
	--self.node_list.layout_pool:addTouchEventListener(BindTool.Bind1(self.ClickPoolHandler, self)) 
	XUI.AddClickEventListener(self.node_list["btn_close_window"], BindTool.Bind1(self.Close, self))
	self:UpdataBulletNum()
	self.bullet_delete_call_back = BindTool.Bind(self.BulletDeleteCallBack, self)
	self.touch_call_back = BindTool.Bind(self.TouchCallBack, self)
	self.img_shou_wang_remind = self.node_list["img_shou_wang_remind"]
	
end
function SocietyView:DeleteFishPondView()
	self:RemoveAllFishAndBullet()
	if nil ~= self.close_timer_quest then
		GlobalTimerQuest:EndQuest(self.close_timer_quest)
		self.close_timer_quest = nil
	end
	
	self.button_fire = nil
	self.protect_fish_list = {}
	if nil ~= self.bubble_eff then
		for k, v in pairs(self.bubble_eff) do
			v = nil
		end
		self.bubble_eff = nil
	end
	if nil ~= self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end

	if nil ~= self.page_fishshop_list then
		self.page_fishshop_list:DeleteMe()
		self.page_fishshop_list = nil
	end

	if nil ~= self.ui_drag then
		self.ui_drag:DeleteMe()
		self.ui_drag = nil
	end

	self.rich_content = nil
	if nil ~= self.bubble_eff then
		for k,v in pairs(self.fish_list) do
			self.fish_list[v] = nil
		end
	end
	self.layout_fishpond_down = nil
	self.layout_fish_shop = nil
	self.layout_fishing_tip = nil
	self.img_shou_wang_remind = nil
end


function SocietyView:BulletDeleteCallBack(bullet)
	if self.bullet_list[bullet] then
		self.bullet_list[bullet]:DeleteMe()
		self.bullet_list[bullet] = nil
	end
end

function SocietyView:TouchCallBack(bullet, obj)
	--print_error(bullet, obj.name,obj)
	--判断是否打到了守卫鱼
	for _, v in ipairs(self.protect_fish_list) do
		--print_error("--判断是否打到了守卫鱼"..v)
		if v:GetObj() == obj then
			--创建特效
			local position = bullet:GetPosition()
			--EffectManager.Instance:PlayAtTransform("uis/view/fishpond_ui/fishanimatepic_prefab", "UI_buyuzidan_sj", self.node_list["ClickRange"].transform, 2, position)

			--删除子弹
			self.bullet_list[bullet]:DeleteMe()
			self.bullet_list[bullet] = nil

			--发送偷鱼的协议
			local uid = SocietyWGData.Instance:GetShowPond()
			--local now_fish_list = FishingData.Instance:GetNowFishList()
			-- if nil == now_fish_list then
			-- 	print_error("now_fish_list is nil!!!!!!!!!!!!!!!!")
			-- 	return
			-- end
			SocietyWGCtrl.Instance:SendFishPoolStealFish(uid, v:GetVo().fish_objid, v:GetVo().raise_timestamp)
			return
		end
	end

	for k, v in ipairs(self.fish_list) do
		if v:GetObj() == obj then
			--鱼已经死亡
			if v:IsDead() then
				return
			end

			--创建特效
			local position = bullet:GetPosition()
			--EffectManager.Instance:PlayAtTransform("effects/prefab/ui_x_prefab", "UI_buyuzidan_sj", self.node_list["ClickRange"].transform, 2, position)

			--删除子弹
			self.bullet_list[bullet]:DeleteMe()
			self.bullet_list[bullet] = nil

			--发送偷鱼的协议
			local uid = SocietyWGData.Instance:GetShowPond()

			SocietyWGCtrl.Instance:SendFishPoolStealFish(uid, v:GetVo().fish_objid, v:GetVo().raise_timestamp)
			return
		end
	end
end
-- 气泡
function SocietyView:CreateBubble(bubble_type, loop)
	
end
--设置UI拖动
function SocietyView:SetUiDrag()
	self.ui_drag:SetUi(self.node_list["layout_buyfish_on"], nil, "pool")

	local function onhit(hitter, hitter_source, from_area_name, touch_pos)
		if from_area_name == "fish" then
			SocietyWGCtrl.Instance:SendFishPoolRaiseReq(hitter_source.fish_type or 1)
			self.add_fish_position = touch_pos
		end
	end

	self.ui_drag:BindOnHit(onhit)


end
--刷新捕鱼视图
function SocietyView:FlushFishPondView(param_t)
	
	local data = SocietyWGData.Instance
	--print_error(data)
	if self.button_fire then
		self.button_fire:SetActive(false)
		if data:CurIsOwnPond() then
			self.button_fire:SetActive(true)
		end
	end
	self.node_list["btn_backhome"]:SetActive(not data:CurIsOwnPond())
	self.node_list["btn_fish_shop"]:SetActive(data:CurIsOwnPond())
	self.node_list["btn_backhome"]:SetActive(not data:CurIsOwnPond())
	self.node_list["layout_fishpond_top"]:SetActive(data:CurIsOwnPond())
	local cur_uid = data:GetShowPond()
	local all_info = data:GetFishpondAllInfo(cur_uid)
	--print_error(cur_uid,all_info,"+++++++++++++",all_info.normal_info,"++++++++",all_info.raise_list)
	if all_info and all_info.normal_info and all_info.raise_list then
		local normal_info = all_info.normal_info
		--print_error(string.format(Language.Fishpond.FishpondLv, normal_info.pool_level))
		self.node_list["rich_fishpond_level"].text.text = string.format(Language.Society.BuYuLevel, normal_info.pool_level)
		self.node_list["lbl_role_name"].text.text = normal_info.owner_name

		local role_vo = RoleWGData.Instance.role_vo
		
		local bundle,asset = ResPath.GetRoleHeadIconSociety(role_vo.prof) 
		self.node_list["icon"].image:LoadSprite(bundle,asset)

		local lv_cfg = data:GetFishpondCfgByLv(normal_info.pool_level) or {}
		local common_fish_capacity = lv_cfg.common_fish_capacity or 0
		local max_capacity = common_fish_capacity + normal_info.extend_capacity
		local fish_num = data:GetRewardFishNum(normal_info.owner_uid) or 0
		local content = string.format(Language.Society.FishNum, fish_num, max_capacity)
		self.node_list["rich_fishnum"].text.text = content	

		--local my_id = role_vo.role_id

		local fishs_info = all_info.raise_list
		
		if param_t.allfish then 	--是否刷新所有鱼
			self:RemoveAllFishAndBullet()
			self.all_fish_list = {}
				--print_error(#fishs_info,fishs_info"0000000000000
			for k, v in pairs(fishs_info) do
				self:CreateFish(v)
				--print_error(v)
			end
			local ind = data:CurIsOwnPond() and 1 or 2
			self.node_list["btn_fish_shop_text"].text.text = Language.Fishpond.FishShopBtnTxt[ind]
		else
			self:CheckFishsChange(fishs_info)
		end
	end

	self:UpdataBulletNum()
	self:FlushFriendBtnEffect()
	self.node_list["btn_fishpond_attr"]:SetActive(SocietyWGData.Instance:CurIsOwnPond())

	local is_Own_Pond = data:CurIsOwnPond()
	self.img_shou_wang_remind:SetActive(false) --收网红点
	local fish_list = data:GetFishpondFishInfoNoGuard(RoleWGData.Instance.role_vo.role_id)
	for i,v in ipairs(fish_list) do
		local fish_time = data:GetCanHarvestTimeByType(v.fish_type)
		if math.floor(TimeWGCtrl.Instance:GetServerTime() - v.raise_timestamp) >= fish_time then
			self.img_shou_wang_remind:SetActive(is_Own_Pond and true)
			break
		else
			self.img_shou_wang_remind:SetActive(false)
		end
	end
	if data:GetFishExtendRemind() then
		self.img_shou_wang_remind:SetActive(data:GetFishExtendRemind() > 0 )
	end
	self.node_list["img_fish_shop_remind"]:SetActive(is_Own_Pond and data:GetFramFishRemind() == 1) --鱼苗商店提醒
end

-- 设置好友按钮特效
function SocietyView:FlushFriendBtnEffect()
	local is_remind = SocietyWGData.Instance:GetFramBulletRemind() > 0
	if self.layout_fishing_tip then
		if is_remind then
			self.layout_fishing_tip:SetActive(self.layout_fishpond_down:isVisible())
		else
			self.layout_fishing_tip:SetActive(false)
		end
	end

end

-- 刷新子弹数量
function SocietyView:UpdataBulletNum()
	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	local all_info = SocietyWGData.Instance:GetFishpondAllInfo(role_id)
	if all_info and all_info.normal_info then
		local normal_info = all_info.normal_info
		local other_cfg = SocietyWGData.Instance.other_cfg
		local bullet_num = other_cfg.base_bullet_num + normal_info.bullet_buy_num - normal_info.bullet_consume_num
		local cur_bullet = self:CurBulletNum()
		self.bullet_num = bullet_num - cur_bullet
		self.node_list["lbl_bullet_num"].text.text = math.max(self.bullet_num, 0)
		self.node_list["img_steal_remind"]:SetActive(self.bullet_num > 0) --偷鱼红点提醒
		
	end
end

function SocietyView:CheckFishsChange(info)

end
--创建鱼
function SocietyView:CreateFish(fish_info)

	if nil == fish_info then
		return
	end
	
	if fish_info.fish_type == 1 then
		local fish = Fish.New(fish_info, true)
		fish:SetParent(self.node_list["layout_pool"])
		table.insert(self.protect_fish_list, fish)
		
	else
		local fish = Fish.New(fish_info, false)
		fish:SetParent(self.node_list["layout_pool"])
		table.insert(self.fish_list, fish)
	end
	
	
	
end
function SocietyView:FishsChange(info, is_del)
	if is_del then

		--fish:PlayToBeTake()
			for k, v in ipairs(self.fish_list) do
				if info.fish_objid == v:GetVo().fish_objid then
					v:PlayToBeTake()
					table.remove(self.fish_list, k)
					self:RewardIconMove(v:GetVo())
					break
				end
			end

		self:UpdataBulletNum()
	else
		self:CreateFish(info)
		
	end
end



-- 点击子弹
function SocietyView:CliskBulletHandler()
	local role_id = RoleWGData.Instance.role_vo.role_id or 0
	local normal_info = SocietyWGData.Instance:GetFishpondNormalInfo(role_id)
	if normal_info and normal_info.bullet_buy_times >= SocietyWGData.Instance.other_cfg.day_buy_bullet_limit_times then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fishpond.BuyBulletLimitTip)
		return
	end
	if nil == self.alert_window then
		self.alert_window = Alert.New(nil, nil, nil, nil, true)
	end
	local gold = SocietyWGData.Instance.other_cfg.buy_bullet_price or 0
	local bullet_num = SocietyWGData.Instance.other_cfg.give_bullet_per_buy or 0
	local content = string.format(Language.Fishpond.BuyBulletTips, gold, bullet_num)
	self.alert_window:SetLableString(content)
	self.alert_window:SetOkFunc(function() 
		SocietyWGCtrl.Instance:SendFishPoolBuyBulletReq()
	 end)
	self.alert_window:Open()
end

-- 好友列表
function SocietyView:OpenFriendList()
	
	SocietyWGCtrl.Instance:OpenFriendView(SocietyWGData.GetInfoType.ServerAllInfo)
end
-- 打开鱼塘信息面板
function SocietyView:OpenFishpondAttr()
	SocietyWGCtrl.Instance:OpenAllInfoView()
end
-- 撒网特效
function SocietyView:ShowFishingEffect(x, y)
	
end

-- 点击鱼塘
function SocietyView:ClickPoolHandler(sender, touch_event, touch)
	

end

function SocietyView:OnTouchTimer()
	if nil == self.click_point then return end

	if not SocietyWGData.Instance:CurIsOwnPond() then 
		self:CreateBullet()
	end
end

-- 当前存在的子弹数
function SocietyView:CurBulletNum()
	local num = 0
	if self.bullet_list then
	for k, v in pairs(self.bullet_list) do
		if v then
			num = num + 1
		end
	end
end
	return num
end

-- 点击返回按钮
function SocietyView:ClickBackBtnHandler()
	SocietyWGCtrl.Instance:BackHandler()
end


--打开捕鱼视图
function SocietyView:OpenFishView(fish)
	if fish and fish:GetVo() then
		SocietyWGCtrl.Instance:OpenFishInfoView(fish:GetVo())
	end
end
--移除所有鱼和子弹

function SocietyView:RemoveAllFishAndBullet()
	--print_error(#self.fish_list,self.fish_list)

	if self.fish_list then
	for _, v in pairs(self.fish_list) do
		if v then
		    v:DeleteMe()
	    end
	end
end
	self.fish_list = {}
	if self.protect_fish_list then
	if self.protect_fish_list then
	for _, v in pairs(self.protect_fish_list) do
		if v then
		v:DeleteMe()
	end
	end
end
end
	self.protect_fish_list = {}
	
	if self.bullet_list then 
	if self.bullet_list then 
		for k, v in pairs(self.bullet_list) do
			if v and v[1] then
				v[1]:removeFromParent()
			end
		end
		self.bullet_list = {}
	end
end
end


function SocietyView:FireBullet()
	--self:RewardIconMove()
		--是自己的鱼塘无法捕获
	if SocietyWGData.Instance:CurIsOwnPond() then
		self:CanNotCatchFishTip()
		self.node_list["img_gishpond_gun"].rect.localRotation = Quaternion.Euler(0, 0, 0)
		--print_error("自家鱼塘")
		return
	end
	
	--判断是否有足够子弹
	local bullet_num = self.bullet_num
	--print_error("子弹数",bullet_num)
	if bullet_num <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Fishpond.NoBulletTips)
		self.node_list["img_gishpond_gun"].rect.localRotation = Quaternion.Euler(0, 0, 0)
		return
	end
	local mousePosition = Vector3(UnityEngine.Input.mousePosition.x,UnityEngine.Input.mousePosition.y,0)

	local rect = self.node_list["ClickRange"].rect
	local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(rect, mousePosition, UICamera, Vector2(0, 0))

	local abs_x = math.abs(local_pos_tbl.x)
	local abs_y = math.abs(local_pos_tbl.y)
	local angle = math.deg(math.atan2(abs_x, abs_y))
	if local_pos_tbl.x > 0 then
		angle = -angle
	end
	--print_error("angle"..angle,mousePosition,local_pos_tbl)
	local rotation = Quaternion.Euler(0, 0, angle)
	-- print_error(_,abs_x,abs_y,angle,rect,local_pos_tbl,rotation,UnityEngine.Input.mousePosition)
	self.node_list["img_gishpond_gun"].rect.localRotation = rotation
	

	-- --获取指引按钮的屏幕坐标
	 local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.node_list["fire_pos"].rect.position)

	--转换屏幕坐标为本地坐标
	rect = self.node_list["ClickRange"].rect
	local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(rect, screen_pos_tbl, UICamera, Vector2(0, 0))

	--开始发射子弹
	local bullet = Bullet.New()
	bullet:SetParent(self.node_list["ClickRange"].transform)
	bullet:SetRange(self.node_list["ClickRange"].rect.sizeDelta.x/2, self.node_list["ClickRange"].rect.sizeDelta.y)
	bullet:SetStartPosTbl(local_bullet_start_pos_tbl)
	bullet:SetLocalRotation(rotation)
	bullet:SetDeleteCallBack(self.bullet_delete_call_back)
	bullet:SetTouchCallBack(self.touch_call_back)
	for _, v in ipairs(self.protect_fish_list) do
		local bind_func = BindTool.Bind(v.BulletPositionChange, v)
		bullet:AddPositionChangeListen(bind_func)
	end
	for _, v in ipairs(self.fish_list) do
		local bind_func = BindTool.Bind(v.BulletPositionChange, v)
		bullet:AddPositionChangeListen(bind_func)
	end
	bullet:CreateBulletObj()
	self.bullet_list[bullet] = bullet
end




function SocietyView:RewardIconMove(info)
	if nil == info and info.fish_type ~= 3 and  info.fish_type ~= 4 then return end  ----26346  26349
	local item_id = 0
	if info.fish_type == 3 then 
		item_id = 26346 
	else
		item_id = 26349 
	end 

	local bundle,asset = ResPath.GetItem(item_id)
	self.node_list["move_fish_icon"].image:LoadSprite(bundle,asset)
	self.node_list["move_fish_icon"]:SetActive(true)
	self.node_list["move_fish_icon"].rect.anchoredPosition = self.node_list["reward_pos"].rect.anchoredPosition

	local end_pos_x,end_pos_y = self.node_list["reward_end_pos"].rect.anchoredPosition.x,self.node_list["reward_end_pos"].rect.anchoredPosition.y

		local tweenX = self.node_list["move_fish_icon"].rect:DOAnchorPosX(end_pos_x, 0.5)
		local tweenY = self.node_list["move_fish_icon"].rect:DOAnchorPosY(end_pos_y, 0.5)
		tweenX:SetEase(DG.Tweening.Ease.Linear)
		tweenY:SetEase(DG.Tweening.Ease.Linear)
		self.close_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.SetFishIconEnd, self), 0.5)

end

function SocietyView:SetFishIconEnd()
	self.node_list["move_fish_icon"]:SetActive(false)
end








function SocietyView:CanNotCatchFishTip()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Fishpond.CanNotCatchFishTip)
end
--打开提示
function SocietyView:OpenHowToPlayTips()
	-- DescTip.Instance:Open()
	-- DescTip.Instance:SetContent(Language.Fishpond.HowToPlayDesc, Language.Dungeon.HowToPlay)
end