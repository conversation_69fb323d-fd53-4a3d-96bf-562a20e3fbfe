-- C-宠物.xls
local item_table={
[1]={item_id=27700,num=2,is_bind=1},
[2]={item_id=27700,num=4,is_bind=1},
[3]={item_id=27700,num=8,is_bind=1},
[4]={item_id=27701,num=4,is_bind=1},
[5]={item_id=27702,num=2,is_bind=1},
[6]={item_id=27703,num=2,is_bind=1},
[7]={item_id=27718,num=2,is_bind=1},
[8]={item_id=27700,num=20,is_bind=1},
[9]={item_id=27701,num=10,is_bind=1},
[10]={item_id=27702,num=5,is_bind=1},
[11]={item_id=27703,num=4,is_bind=1},
[12]={item_id=27704,num=2,is_bind=1},
[13]={item_id=27718,num=3,is_bind=1},
[14]={item_id=27719,num=2,is_bind=1},
[15]={item_id=27700,num=1,is_bind=1},
[16]={item_id=27701,num=1,is_bind=1},
[17]={item_id=27702,num=1,is_bind=1},
[18]={item_id=27703,num=1,is_bind=1},
[19]={item_id=27704,num=1,is_bind=1},
[20]={item_id=27705,num=1,is_bind=1},
[21]={item_id=27718,num=1,is_bind=1},
[22]={item_id=27719,num=1,is_bind=1},
[23]={item_id=27720,num=1,is_bind=1},
[24]={item_id=27708,num=1,is_bind=1},
[25]={item_id=27709,num=1,is_bind=1},
[26]={item_id=27710,num=1,is_bind=1},
[27]={item_id=27711,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
ai={
{}
},

ai_meta_table_map={
},
pet_base={
{color=0,gongji=75,fangyu=50,maxhp=1875,pojia=25,yuansu_sh=50,yuansu_hj=25,grow_rate_lower=100,grow_rate_upper=112,get_msg_type=-1,main_pos="-0.1|-0.8|0",other_scale=110,other_pos="0|-1|0",},
{id=2,name="神灵草",active_skill=411,passive_skill_id=1,color=0,gongji=75,fangyu=50,maxhp=1875,pojia=25,yuansu_sh=50,yuansu_hj=25,grow_rate_lower=100,grow_rate_upper=112,get_msg_type=-1,pet_head_res=2,pet_res_id=10002001,main_pos="-0.1|-0.82|0",item_icon=90776,},
{id=3,name="咕噜熊",active_skill=412,passive_skill_id=3,color=100,gongji=100,fangyu=100,maxhp=100,pojia=100,yuansu_sh=100,yuansu_hj=100,grow_rate_lower=140,grow_rate_upper=168,get_msg_type=-1,pet_head_res=3,pet_res_id=10003001,main_pos="-0.1|-0.8|0",other_pos="0|-1.08|0",item_icon=90777,},
{id=4,name="萌萌鸡",active_skill=413,passive_skill_id=4,pet_head_res=4,pet_res_id=10004001,main_pos="-0.1|-0.74|0",other_pos="0|-1.06|0",item_icon=90778,},
{id=5,name="长耳兔",active_skill=414,passive_skill_id=5,pet_head_res=5,pet_res_id=10005001,main_pos="-0.1|-0.79|0",other_pos="0|-1.04|0",item_icon=90779,},
{id=6,name="咕咕鸟",active_skill=415,passive_skill_id=6,color=100,gongji=100,fangyu=100,maxhp=100,pojia=100,yuansu_sh=100,yuansu_hj=100,grow_rate_lower=140,grow_rate_upper=168,get_msg_type=-1,pet_head_res=6,pet_res_id=10006001,ui_pos="0|-1.2|0",other_pos="0|-1|0",item_icon=90780,},
{id=7,name="金刚狼",active_skill=416,passive_skill_id="7|8",pet_head_res=7,pet_res_id=10007001,ui_pos="0.1|-1.04|0",main_pos="0|-0.77|0",other_pos="0.1|-1.11|0",item_icon=90781,},
{id=8,name="龟龟兔兔",active_skill=417,passive_skill_id="9|10",pet_head_res=8,pet_res_id=10008001,main_pos="-0.1|-0.85|0",other_scale=100,other_pos="0|-1.31|0",item_icon=90782,},
{id=9,name="狂扁猴",active_skill=418,passive_skill_id="11|12",pet_head_res=9,pet_res_id=10009001,ui_scale=180,main_scale=180,main_pos="-0.1|-0.89|0",other_scale=100,other_pos="0|-1.35|0",item_icon=90783,},
{id=10,name="爱心狐",active_skill=421,passive_skill_id="17|18",pet_head_res=10,pet_res_id=10010001,ui_scale=180,ui_pos="0|-1.2|0",main_scale=180,main_pos="-0.1|-1|0",other_pos="0.03|-1.2|0",item_icon=90784,},
{id=11,name="蓝魔灵",active_skill=420,passive_skill_id="15|16",pet_head_res=11,pet_res_id=10011001,ui_pos="0|-1.21|0",main_pos="-0.1|-0.95|0",other_pos="0|-1|0",item_icon=90785,},
{id=12,type=0,name="月兔",active_skill=419,passive_skill_id="13|14",grow_rate_lower=180,grow_rate_upper=224,pet_head_res=12,pet_res_id=10012001,ui_pos="0|-1.28|0",main_pos="-0.1|-0.97|0",other_pos="0|-1.17|0",item_icon=90786,},
{id=13,type=0,name="忍者喵",common_skill_id=401,active_skill=422,passive_skill_id="19|20",grow_rate_lower=180,grow_rate_upper=224,pet_head_res=13,pet_res_id=10013001,main_pos="-0.1|-0.81|0",other_pos="0|-1.11|0",item_icon=90787,},
{id=14,name="葫芦兔仙",active_skill=423,passive_skill_id="21|22",pet_head_res=14,pet_res_id=10014001,ui_pos="0|-1|0",main_pos="-0.1|-0.7|0",item_icon=90788,},
{id=15,name="霸虎",active_skill=424,passive_skill_id="23|24|25",zs_skill_id=1,pet_head_res=15,pet_res_id=10015001,ui_pos="0|-1.09|0",main_pos="-0.1|-0.75|0",item_icon=90789,},
{id=16,name="白泽",active_skill=425,passive_skill_id="26|27|28",zs_skill_id=2,pet_head_res=16,pet_res_id=10016001,item_icon=90790,},
{id=17,name="小丑杰克",common_skill_id=401,active_skill=426,passive_skill_id="29|30|31",zs_skill_id=3,pet_head_res=17,pet_res_id=10017001,ui_pos="0|-1.08|0",main_pos="-0.1|-0.76|0",item_icon=90791,},
{id=18,name="扶灯玉兔",active_skill=427,passive_skill_id="32|33|34",zs_skill_id=4,pet_head_res=18,pet_res_id=10018001,item_icon=90792,},
{id=19,name="狐火",active_skill=428,passive_skill_id="35|36|37",zs_skill_id=5,pet_head_res=19,pet_res_id=10019001,ui_pos="0|-1.08|0",main_pos="-0.1|-0.76|0",item_icon=90793,},
{id=20,name="朋克白熊",common_skill_id=401,active_skill=429,passive_skill_id="38|39|40",zs_skill_id=6,pet_head_res=20,pet_res_id=10020001,ui_pos="0|-1.09|0",main_pos="0|-0.77|0",item_icon=90794,},
{id=21,name="小兔娘",active_skill=430,passive_skill_id="41|42|43",zs_skill_id=7,pet_head_res=21,pet_res_id=10021001,ui_scale=180,ui_pos="0|-1|0",main_scale=180,main_pos="-0.1|-0.92|0",other_scale=110,other_pos="0|-1.2|0",item_icon=90795,},
{id=22,name="嫦小娥",active_skill=431,passive_skill_id="44|45|46",zs_skill_id=8,pet_head_res=22,pet_res_id=10022001,main_pos="-0.1|-0.8|0",other_pos="0|-1.11|0",item_icon=90796,},
{id=23,name="猫耳小缘",active_skill=432,passive_skill_id="47|48|49",zs_skill_id=9,pet_head_res=23,pet_res_id=10023001,ui_pos="0.2|-1.11|0",main_pos="0.1|-0.8|0",other_pos="0.15|-1.09|0",item_icon=90797,},
{id=24,name="画魂",active_skill=433,passive_skill_id="50|51|52",zs_skill_id=10,pet_head_res=24,pet_res_id=10024001,ui_pos="0|-0.8|0",main_pos="0|-0.7|0",other_scale=100,item_icon=90798,},
{id=25,name="邀月公主",active_skill=436,passive_skill_id="59|60|61",zs_skill_id=13,pet_head_res=25,pet_res_id=10025001,ui_scale=180,ui_pos="0|-1.3|0",main_scale=180,main_pos="-0.1|-1|0",other_scale=100,other_pos="0|-1.2|0",item_icon=90799,},
{id=26,name="扇舞仙子",active_skill=435,passive_skill_id="56|57|58",zs_skill_id=12,pet_head_res=26,pet_res_id=10026001,ui_pos="0|-1.4|0",main_pos="-0.1|-1.1|0",other_scale=100,other_pos="0|-1.3|0",item_icon=90800,},
{id=27,name="夜巡狐仙",active_skill=437,passive_skill_id="62|63|64",zs_skill_id=14,pet_head_res=27,pet_res_id=10027001,ui_pos="0|-1.27|0",main_pos="-0.1|-0.99|0",other_scale=100,other_pos="-0.1|-1.24|0",item_icon=90801,},
{id=28,name="哪吒",active_skill=434,passive_skill_id="53|54|55",zs_skill_id=11,color=4,gongji=900,fangyu=600,maxhp=22500,pojia=300,yuansu_sh=600,yuansu_hj=300,grow_rate_lower=250,grow_rate_upper=420,pet_head_res=28,pet_res_id=10028001,ui_scale=210,ui_pos="0|-1.2|0",main_scale=210,main_pos="-0.05|-0.94|0",other_pos="0|-1.27|0",item_icon=90802,pet_audio="NPC_chongwu_nezha",},
{id=29,name="灭世魔丸",active_skill=438,passive_skill_id="65|66|67|68",zs_skill_id=15,pet_head_res=29,pet_res_id=10029001,ui_scale=160,ui_pos="0|-1.52|0",main_scale=160,main_pos="-0.1|-1.12|0",other_pos="0|-1.44|0",item_icon=90803,pet_audio="NPC_chongwu_mieshi",},
{id=30,name="通臂猿猴",active_skill=441,passive_skill_id="77|78|79|80",zs_skill_id=18,pet_head_res=30,pet_res_id=10030001,ui_scale=140,ui_pos="0|-1.45|0",main_scale=140,main_pos="-0.1|-0.96|0",other_scale=70,other_pos="0|-1.64|0",item_icon=90804,pet_audio="NPC_chongwu_tongbi",},
{id=31,name="黑曜炎虎",active_skill=440,passive_skill_id="73|74|75|76",zs_skill_id=17,pet_head_res=31,pet_res_id=10031001,ui_pos="0|-1.05|0",main_pos="-0.1|-0.73|0",other_scale=80,other_pos="0|-1.55|0",item_icon=90805,pet_audio="NPC_chongwu_heiyao",},
{id=32,type=0,name="敖丙",active_skill=439,passive_skill_id="69|70|71|72",zs_skill_id=16,color=4,gongji=900,fangyu=600,maxhp=22500,pojia=300,yuansu_sh=600,yuansu_hj=300,grow_rate_lower=250,grow_rate_upper=420,pet_head_res=32,pet_res_id=10032001,ui_scale=180,ui_pos="0|-1.21|0",main_scale=180,other_scale=100,other_pos="0|-1.3|0",item_icon=90806,pet_audio="NPC_chongwu_aobing",},
{id=33,name="怒焰",active_skill=442,passive_skill_id="81|82|83|84",zs_skill_id=19,pet_head_res=33,pet_res_id=10033001,item_icon=90807,pet_audio="NPC_chongwu_rongyan",},
{id=34,name="安倍晴明",active_skill=443,passive_skill_id="85|86|87|88",zs_skill_id=20,pet_head_res=34,pet_res_id=10034001,item_icon=90808,pet_audio="NPC_chongwu_anbei",},
{id=35,name="女娲",active_skill=444,passive_skill_id="89|90|91|92",zs_skill_id="21|22",color=5,gongji=1725,fangyu=1150,maxhp=43125,pojia=575,yuansu_sh=1150,yuansu_hj=575,grow_rate_lower=500,grow_rate_upper=600,pet_head_res=35,pet_res_id=10035001,ui_scale=150,ui_pos="0|-1.61|0",main_scale=150,main_pos="-0.1|-1.19|0",other_scale=90,other_pos="0|-1.64|0",item_icon=90809,pet_audio="NPC_chongwu_nvwa",},
{id=36,name="伏羲",active_skill=445,passive_skill_id="93|94|95|96",zs_skill_id="23|24",pet_head_res=36,pet_res_id=10036001,ui_pos="0|-1.3|0",main_pos="-0.1|-1.1|0",other_scale=80,other_pos="0|-1.6|0",item_icon=90810,open_day_visible=15,pet_audio="NPC_chongwu_fuxi",},
{id=37,name="盘古",active_skill=446,passive_skill_id="97|98|99|100",zs_skill_id="25|26",pet_head_res=37,pet_res_id=10037001,ui_pos="0|-1.46|0",other_scale=90,other_pos="0.09|-1.5|0",item_icon=90811,pet_audio="NPC_chongwu_pangu",}
},

pet_base_meta_table_map={
[18]=19,	-- depth:1
[16]=19,	-- depth:1
[4]=3,	-- depth:1
[5]=3,	-- depth:1
[12]=6,	-- depth:1
[13]=3,	-- depth:1
[11]=12,	-- depth:2
[14]=11,	-- depth:3
[7]=12,	-- depth:2
[9]=12,	-- depth:2
[10]=13,	-- depth:2
[33]=32,	-- depth:1
[34]=32,	-- depth:1
[36]=35,	-- depth:1
[8]=10,	-- depth:3
[29]=32,	-- depth:1
[30]=32,	-- depth:1
[31]=32,	-- depth:1
[37]=36,	-- depth:2
},
pet_egg={
{is_special=0,need_time_s=900,pet_pool_weight="245|245|350|350|350|350|20|20|20|20|20|10",special_pet_pool="1|2|3|4|5|6|7|8|9|10|13|17",special_pet_pool_weight="245|245|350|350|350|350|20|20|20|20|20|10",pet_res_id=10,open_get_list_1="17|7|8|9|10|13",open_get_list_2="3|4|5|6",open_get_list_3="1|2",},
{item_id=27712,is_special=0,need_time_s=3600,pet_pool="1|2|3|4|5|6|7|8|9|10|13|17|24|29",pet_pool_weight="700|700|1375|1375|1375|1375|2400|2400|2400|2400|2400|500|500|50",special_pet_pool="1|2|3|4|5|6|7|8|9|10|13|17|24|29",special_pet_pool_weight="700|700|1375|1375|1375|1375|2400|2400|2400|2400|2400|500|500|50",pet_res_id=20,open_get_list_2="7|8|9|10|13",open_get_list_3="3|4|5|6|1|2",},
{item_id=27713,is_special=0,need_time_s=7200,pet_pool="15|17|21|22|29",pet_pool_weight="245|245|245|245|20",special_pet_pool="15|17|21|22|29",special_pet_pool_weight="245|245|245|245|20",pet_res_id=30,open_get_list_1=29,open_get_list_3="15|17|21|22",},
{item_id=27729,pet_pool="19|23|24|25|31",special_pet_pool="19|23|24|25|31",open_get_list_1=31,open_get_list_3="19|23|24|25",},
{item_id=27714,need_time_s=18000,pet_pool="32|35",pet_pool_weight="95|5",special_pet_pool="32|35",special_pet_pool_weight="95|5",pet_res_id=40,open_get_list_1="35|32",},
{item_id=27726,pet_pool=28,special_pet_pool=28,open_get_list_1=28,bind_grow_rate=350,},
{item_id=27727,pet_pool=4,special_pet_pool=4,pet_res_id=10,open_get_list_1=4,bind_grow_rate=140,},
{item_id=27728,pet_pool=12,special_pet_pool=12,pet_res_id=21,open_get_list_1=12,bind_grow_rate=224,},
{item_id=27730,pet_pool=33,special_pet_pool=33,open_get_list_1=33,},
{item_id=27731,pet_pool=34,special_pet_pool=34,open_get_list_1=34,},
{item_id=27732,pet_pool=11,special_pet_pool=11,pet_res_id=21,open_get_list_1=11,bind_grow_rate=210,},
{item_id=27733,pet_pool=26,special_pet_pool=26,pet_res_id=31,open_get_list_1=26,bind_grow_rate=260,},
{item_id=27734,pet_pool=30,open_get_list_1=30,bind_grow_rate=400,},
{item_id=27735,is_special=0,pet_pool="9|13|14",pet_pool_weight="1|1|1",pet_res_id=20,open_get_list_2="7|8|9|10|13",open_get_list_3="3|4|5|6|1|2",bind_grow_rate=180,},
{item_id=27736,pet_pool=32,special_pet_pool=32,open_get_list_1=32,bind_grow_rate=420,}
},

pet_egg_meta_table_map={
[9]=6,	-- depth:1
[10]=13,	-- depth:1
[4]=3,	-- depth:1
},
pet_egg_slot={
{},
{slot=1,unlock_bind_gold=200,},
{slot=2,unlock_gold=68,},
{slot=3,unlock_gold=128,},
{slot=4,unlock_gold=188,}
},

pet_egg_slot_meta_table_map={
},
pet_level={
{},
{level=2,need_exp=110,},
{level=3,need_exp=120,},
{level=4,need_exp=130,},
{level=5,need_exp=140,},
{level=6,need_exp=150,},
{level=7,need_exp=160,},
{level=8,need_exp=170,},
{level=9,need_exp=180,},
{level=10,need_exp=200,},
{level=11,need_exp=240,},
{level=12,need_exp=280,},
{level=13,need_exp=320,},
{level=14,need_exp=360,},
{level=15,need_exp=400,},
{level=16,need_exp=440,},
{level=17,need_exp=480,},
{level=18,need_exp=520,},
{level=19,need_exp=560,},
{level=20,need_exp=600,},
{level=21,need_exp=640,},
{level=22,need_exp=680,},
{level=23,need_exp=720,},
{level=24,need_exp=760,},
{level=25,need_exp=800,},
{level=26,need_exp=840,},
{level=27,need_exp=880,},
{level=28,need_exp=920,},
{level=29,need_exp=960,},
{level=30,need_exp=1000,},
{level=31,need_exp=1020,},
{level=32,need_exp=1040,},
{level=33,need_exp=1060,},
{level=34,need_exp=1080,},
{level=35,need_exp=1100,},
{level=36,need_exp=1120,},
{level=37,need_exp=1140,},
{level=38,need_exp=1160,},
{level=39,need_exp=1180,},
{level=40,need_exp=1200,},
{level=41,need_exp=1220,},
{level=42,need_exp=1240,},
{level=43,need_exp=1260,},
{level=44,need_exp=1280,},
{level=45,need_exp=1300,},
{level=46,need_exp=1320,},
{level=47,need_exp=1340,},
{level=48,need_exp=1360,},
{level=49,need_exp=1380,},
{level=50,need_exp=1400,},
{level=51,need_exp=1420,},
{level=52,need_exp=1440,},
{level=53,need_exp=1460,},
{level=54,need_exp=1480,},
{level=55,need_exp=1500,},
{level=56,need_exp=1520,},
{level=57,need_exp=1540,},
{level=58,need_exp=1560,},
{level=59,need_exp=1580,},
{level=60,need_exp=1600,},
{level=61,need_exp=1640,},
{level=62,need_exp=1680,},
{level=63,need_exp=1720,},
{level=64,need_exp=1760,},
{level=65,need_exp=1800,},
{level=66,need_exp=1840,},
{level=67,need_exp=1880,},
{level=68,need_exp=1920,},
{level=69,need_exp=1960,},
{level=70,need_exp=2000,},
{level=71,need_exp=2100,},
{level=72,need_exp=2200,},
{level=73,need_exp=2300,},
{level=74,need_exp=2400,},
{level=75,need_exp=2500,},
{level=76,need_exp=2600,},
{level=77,need_exp=2700,},
{level=78,need_exp=2800,},
{level=79,need_exp=2900,},
{level=80,need_exp=3000,},
{level=81,need_exp=3100,},
{level=82,need_exp=3200,},
{level=83,need_exp=3300,},
{level=84,need_exp=3400,},
{level=85,need_exp=3500,},
{level=86,need_exp=3600,},
{level=87,need_exp=3700,},
{level=88,need_exp=3800,},
{level=89,need_exp=3900,},
{level=90,need_exp=4000,},
{level=91,need_exp=4100,},
{level=92,need_exp=4200,},
{level=93,need_exp=4300,},
{level=94,need_exp=4400,},
{level=95,need_exp=4500,},
{level=96,need_exp=4600,},
{level=97,need_exp=4700,},
{level=98,need_exp=4800,},
{level=99,need_exp=4900,},
{level=100,need_exp=5000,},
{level=101,need_exp=5100,},
{level=102,need_exp=5200,},
{level=103,need_exp=5300,},
{level=104,need_exp=5400,},
{level=105,need_exp=5500,},
{level=106,need_exp=5600,},
{level=107,need_exp=5700,},
{level=108,need_exp=5800,},
{level=109,need_exp=5900,},
{level=110,need_exp=6000,},
{level=111,need_exp=6100,},
{level=112,need_exp=6200,},
{level=113,need_exp=6300,},
{level=114,need_exp=6400,},
{level=115,need_exp=6500,},
{level=116,need_exp=6600,},
{level=117,need_exp=6700,},
{level=118,need_exp=6800,},
{level=119,need_exp=6900,},
{level=120,need_exp=7000,},
{level=121,need_exp=7100,},
{level=122,need_exp=7200,},
{level=123,need_exp=7300,},
{level=124,need_exp=7400,},
{level=125,need_exp=7500,},
{level=126,need_exp=7600,},
{level=127,need_exp=7700,},
{level=128,need_exp=7800,},
{level=129,need_exp=7900,},
{level=130,need_exp=8000,},
{level=131,need_exp=8100,},
{level=132,need_exp=8200,},
{level=133,need_exp=8300,},
{level=134,need_exp=8400,},
{level=135,need_exp=8500,},
{level=136,need_exp=8600,},
{level=137,need_exp=8700,},
{level=138,need_exp=8800,},
{level=139,need_exp=8900,},
{level=140,need_exp=9000,},
{level=141,need_exp=9100,},
{level=142,need_exp=9200,},
{level=143,need_exp=9300,},
{level=144,need_exp=9400,},
{level=145,need_exp=9500,},
{level=146,need_exp=9600,},
{level=147,need_exp=9700,},
{level=148,need_exp=9800,},
{level=149,need_exp=9900,},
{level=150,need_exp=10000,},
{level=151,need_exp=10100,},
{level=152,need_exp=10200,},
{level=153,need_exp=10300,},
{level=154,need_exp=10400,},
{level=155,need_exp=10500,},
{level=156,need_exp=10600,},
{level=157,need_exp=10700,},
{level=158,need_exp=10800,},
{level=159,need_exp=10900,},
{level=160,need_exp=11000,},
{level=161,need_exp=11100,},
{level=162,need_exp=11200,},
{level=163,need_exp=11300,},
{level=164,need_exp=11400,},
{level=165,need_exp=11500,},
{level=166,need_exp=11600,},
{level=167,need_exp=11700,},
{level=168,need_exp=11800,},
{level=169,need_exp=11900,},
{level=170,need_exp=12000,},
{level=171,need_exp=12100,},
{level=172,need_exp=12200,},
{level=173,need_exp=12300,},
{level=174,need_exp=12400,},
{level=175,need_exp=12500,},
{level=176,need_exp=12600,},
{level=177,need_exp=12700,},
{level=178,need_exp=12800,},
{level=179,need_exp=12900,},
{level=180,need_exp=13000,},
{level=181,need_exp=13100,},
{level=182,need_exp=13200,},
{level=183,need_exp=13300,},
{level=184,need_exp=13400,},
{level=185,need_exp=13500,},
{level=186,need_exp=13600,},
{level=187,need_exp=13700,},
{level=188,need_exp=13800,},
{level=189,need_exp=13900,},
{level=190,need_exp=14000,},
{level=191,need_exp=14100,},
{level=192,need_exp=14200,},
{level=193,need_exp=14300,},
{level=194,need_exp=14400,},
{level=195,need_exp=14500,},
{level=196,need_exp=14600,},
{level=197,need_exp=14700,},
{level=198,need_exp=14800,},
{level=199,need_exp=14900,},
{level=200,need_exp=15000,},
{level=201,need_exp=15100,},
{level=202,need_exp=15200,},
{level=203,need_exp=15300,},
{level=204,need_exp=15400,},
{level=205,need_exp=15500,},
{level=206,need_exp=15600,},
{level=207,need_exp=15700,},
{level=208,need_exp=15800,},
{level=209,need_exp=15900,},
{level=210,need_exp=16000,},
{level=211,need_exp=16100,},
{level=212,need_exp=16200,},
{level=213,need_exp=16300,},
{level=214,need_exp=16400,},
{level=215,need_exp=16500,},
{level=216,need_exp=16600,},
{level=217,need_exp=16700,},
{level=218,need_exp=16800,},
{level=219,need_exp=16900,},
{level=220,need_exp=17000,},
{level=221,need_exp=17100,},
{level=222,need_exp=17200,},
{level=223,need_exp=17300,},
{level=224,need_exp=17400,},
{level=225,need_exp=17500,},
{level=226,need_exp=17600,},
{level=227,need_exp=17700,},
{level=228,need_exp=17800,},
{level=229,need_exp=17900,},
{level=230,need_exp=18000,},
{level=231,need_exp=18100,},
{level=232,need_exp=18200,},
{level=233,need_exp=18300,},
{level=234,need_exp=18400,},
{level=235,need_exp=18500,},
{level=236,need_exp=18600,},
{level=237,need_exp=18700,},
{level=238,need_exp=18800,},
{level=239,need_exp=18900,},
{level=240,need_exp=19000,},
{level=241,need_exp=19100,},
{level=242,need_exp=19200,},
{level=243,need_exp=19300,},
{level=244,need_exp=19400,},
{level=245,need_exp=19500,},
{level=246,need_exp=19600,},
{level=247,need_exp=19700,},
{level=248,need_exp=19800,},
{level=249,need_exp=19900,},
{level=250,need_exp=20000,},
{level=251,need_exp=20100,},
{level=252,need_exp=20200,},
{level=253,need_exp=20300,},
{level=254,need_exp=20400,},
{level=255,need_exp=20500,msg_type=6,},
{level=256,need_exp=20600,},
{level=257,need_exp=20700,},
{level=258,need_exp=20800,},
{level=259,need_exp=20900,},
{level=260,need_exp=21000,},
{level=261,need_exp=21100,},
{level=262,need_exp=21200,},
{level=263,need_exp=21300,},
{level=264,need_exp=21400,},
{level=265,need_exp=21500,},
{level=266,need_exp=21600,},
{level=267,need_exp=21700,},
{level=268,need_exp=21800,},
{level=269,need_exp=21900,},
{level=270,need_exp=22000,},
{level=271,need_exp=22100,},
{level=272,need_exp=22200,},
{level=273,need_exp=22300,},
{level=274,need_exp=22400,},
{level=275,need_exp=22500,},
{level=276,need_exp=22600,},
{level=277,need_exp=22700,},
{level=278,need_exp=22800,},
{level=279,need_exp=22900,},
{level=280,need_exp=23000,},
{level=281,need_exp=23100,},
{level=282,need_exp=23200,},
{level=283,need_exp=23300,},
{level=284,need_exp=23400,},
{level=285,need_exp=23500,},
{level=286,need_exp=23600,},
{level=287,need_exp=23700,},
{level=288,need_exp=23800,},
{level=289,need_exp=23900,},
{level=290,need_exp=24000,},
{level=291,need_exp=24100,},
{level=292,need_exp=24200,},
{level=293,need_exp=24300,},
{level=294,need_exp=24400,},
{level=295,need_exp=24500,},
{level=296,need_exp=24600,},
{level=297,need_exp=24700,},
{level=298,need_exp=24800,},
{level=299,need_exp=24900,},
{level=300,need_exp=25000,}
},

pet_level_meta_table_map={
[270]=255,	-- depth:1
[269]=255,	-- depth:1
[268]=255,	-- depth:1
[267]=255,	-- depth:1
[247]=255,	-- depth:1
[248]=255,	-- depth:1
[266]=255,	-- depth:1
[265]=255,	-- depth:1
[249]=255,	-- depth:1
[250]=255,	-- depth:1
[264]=255,	-- depth:1
[263]=255,	-- depth:1
[262]=255,	-- depth:1
[261]=255,	-- depth:1
[260]=255,	-- depth:1
[259]=255,	-- depth:1
[271]=255,	-- depth:1
[258]=255,	-- depth:1
[251]=255,	-- depth:1
[252]=255,	-- depth:1
[257]=255,	-- depth:1
[256]=255,	-- depth:1
[253]=255,	-- depth:1
[254]=255,	-- depth:1
[272]=255,	-- depth:1
[286]=255,	-- depth:1
[274]=255,	-- depth:1
[289]=255,	-- depth:1
[296]=255,	-- depth:1
[281]=255,	-- depth:1
[282]=255,	-- depth:1
[292]=255,	-- depth:1
[291]=255,	-- depth:1
[246]=255,	-- depth:1
[295]=255,	-- depth:1
[287]=255,	-- depth:1
[297]=255,	-- depth:1
[280]=255,	-- depth:1
[273]=255,	-- depth:1
[285]=255,	-- depth:1
[284]=255,	-- depth:1
[290]=255,	-- depth:1
[288]=255,	-- depth:1
[279]=255,	-- depth:1
[298]=255,	-- depth:1
[294]=255,	-- depth:1
[293]=255,	-- depth:1
[278]=255,	-- depth:1
[277]=255,	-- depth:1
[276]=255,	-- depth:1
[275]=255,	-- depth:1
[283]=255,	-- depth:1
[245]=255,	-- depth:1
[150]=255,	-- depth:1
[243]=255,	-- depth:1
[213]=255,	-- depth:1
[212]=255,	-- depth:1
[211]=255,	-- depth:1
[210]=255,	-- depth:1
[200]=255,	-- depth:1
[190]=255,	-- depth:1
[180]=255,	-- depth:1
[170]=255,	-- depth:1
[160]=255,	-- depth:1
[299]=255,	-- depth:1
[140]=255,	-- depth:1
[214]=255,	-- depth:1
[130]=255,	-- depth:1
[110]=255,	-- depth:1
[100]=255,	-- depth:1
[90]=255,	-- depth:1
[80]=255,	-- depth:1
[70]=255,	-- depth:1
[60]=255,	-- depth:1
[50]=255,	-- depth:1
[40]=255,	-- depth:1
[30]=255,	-- depth:1
[20]=255,	-- depth:1
[10]=255,	-- depth:1
[120]=255,	-- depth:1
[215]=255,	-- depth:1
[216]=255,	-- depth:1
[217]=255,	-- depth:1
[242]=255,	-- depth:1
[241]=255,	-- depth:1
[240]=255,	-- depth:1
[239]=255,	-- depth:1
[238]=255,	-- depth:1
[237]=255,	-- depth:1
[236]=255,	-- depth:1
[235]=255,	-- depth:1
[234]=255,	-- depth:1
[233]=255,	-- depth:1
[232]=255,	-- depth:1
[231]=255,	-- depth:1
[230]=255,	-- depth:1
[229]=255,	-- depth:1
[228]=255,	-- depth:1
[227]=255,	-- depth:1
[226]=255,	-- depth:1
[225]=255,	-- depth:1
[224]=255,	-- depth:1
[223]=255,	-- depth:1
[222]=255,	-- depth:1
[221]=255,	-- depth:1
[220]=255,	-- depth:1
[219]=255,	-- depth:1
[218]=255,	-- depth:1
[244]=255,	-- depth:1
[300]=255,	-- depth:1
},
pet_level_base_attr={
{},
{type=1,}
},

pet_level_base_attr_meta_table_map={
},
grow_rate={
{},
{grow_rate=2,attr_add_per=20,},
{grow_rate=3,attr_add_per=30,},
{grow_rate=4,attr_add_per=40,},
{grow_rate=5,attr_add_per=50,},
{grow_rate=6,attr_add_per=60,},
{grow_rate=7,attr_add_per=70,},
{grow_rate=8,attr_add_per=80,},
{grow_rate=9,attr_add_per=90,},
{grow_rate=10,attr_add_per=100,},
{grow_rate=11,attr_add_per=110,},
{grow_rate=12,attr_add_per=120,},
{grow_rate=13,attr_add_per=130,},
{grow_rate=14,attr_add_per=140,},
{grow_rate=15,attr_add_per=150,},
{grow_rate=16,attr_add_per=160,},
{grow_rate=17,attr_add_per=170,},
{grow_rate=18,attr_add_per=180,},
{grow_rate=19,attr_add_per=190,},
{grow_rate=20,attr_add_per=200,},
{grow_rate=21,attr_add_per=210,},
{grow_rate=22,attr_add_per=220,},
{grow_rate=23,attr_add_per=230,},
{grow_rate=24,attr_add_per=240,},
{grow_rate=25,attr_add_per=250,},
{grow_rate=26,attr_add_per=260,},
{grow_rate=27,attr_add_per=270,},
{grow_rate=28,attr_add_per=280,},
{grow_rate=29,attr_add_per=290,},
{grow_rate=30,attr_add_per=300,}
},

grow_rate_meta_table_map={
},
pet_upgrade={
{need_item_id=27703,need_item_num=1,active_passive_skill_index=-1,},
{grade=1,need_item_id=27703,active_passive_skill_index=-1,gongji=141,fangyu=71,maxhp=1412,pojia=47,},
{grade=2,need_item_id=27703,need_item_num=3,active_passive_skill_index=-1,gongji=156,fangyu=78,maxhp=1562,pojia=52,},
{grade=3,need_item_id=27703,need_item_num=4,active_passive_skill_index=-1,gongji=172,fangyu=86,maxhp=1724,pojia=57,},
{grade=4,need_item_id=27703,need_item_num=5,active_passive_skill_index=-1,gongji=189,fangyu=95,maxhp=1892,pojia=63,},
{grade=5,need_item_id=27703,need_item_num=6,gongji=207,fangyu=104,maxhp=2072,pojia=69,},
{grade=6,need_item_id=27703,need_item_num=7,gongji=226,fangyu=113,maxhp=2258,pojia=75,},
{grade=7,need_item_id=27703,need_item_num=8,gongji=246,fangyu=123,maxhp=2456,pojia=82,},
{grade=8,need_item_id=27703,need_item_num=9,gongji=266,fangyu=133,maxhp=2660,pojia=89,},
{grade=9,need_item_id=27703,need_item_num=9,gongji=288,fangyu=144,maxhp=2876,pojia=96,},
{grade=10,need_item_id=27704,msg_type=6,gongji=482,fangyu=241,maxhp=4820,pojia=161,},
{grade=11,need_item_id=27704,gongji=499,fangyu=250,maxhp=4994,pojia=166,},
{grade=12,need_item_id=27704,gongji=517,fangyu=259,maxhp=5174,pojia=172,},
{grade=13,need_item_id=27704,need_item_num=3,gongji=537,fangyu=268,maxhp=5366,pojia=179,},
{grade=14,need_item_id=27704,need_item_num=3,gongji=557,fangyu=278,maxhp=5570,pojia=186,},
{grade=15,need_item_id=27704,need_item_num=4,gongji=579,fangyu=289,maxhp=5786,pojia=193,},
{grade=16,need_item_id=27704,need_item_num=4,gongji=601,fangyu=301,maxhp=6014,pojia=200,},
{grade=17,need_item_id=27704,need_item_num=4,gongji=625,fangyu=312,maxhp=6248,pojia=208,},
{grade=18,need_item_num=1,gongji=649,fangyu=325,maxhp=6494,pojia=216,},
{grade=19,gongji=675,fangyu=338,maxhp=6752,pojia=225,},
{grade=20,need_item_num=3,msg_type=6,gongji=909,fangyu=454,maxhp=9086,pojia=303,},
{grade=21,need_item_num=4,gongji=928,fangyu=464,maxhp=9284,pojia=309,},
{grade=22,need_item_num=5,gongji=949,fangyu=475,maxhp=9494,pojia=316,},
{grade=23,need_item_num=6,gongji=972,fangyu=486,maxhp=9722,pojia=324,},
{grade=24,need_item_num=7,gongji=996,fangyu=498,maxhp=9962,pojia=332,},
{grade=25,need_item_num=8,gongji=1021,fangyu=511,maxhp=10214,pojia=340,},
{grade=26,need_item_num=9,gongji=1048,fangyu=524,maxhp=10478,pojia=349,},
{grade=27,need_item_num=10,gongji=1075,fangyu=538,maxhp=10754,pojia=358,},
{grade=28,need_item_num=11,gongji=1104,fangyu=552,maxhp=11042,pojia=368,},
{grade=29,need_item_num=12,gongji=1134,fangyu=567,maxhp=11342,pojia=378,},
{grade=30,need_item_num=13,msg_type=6,gongji=1406,fangyu=703,maxhp=14060,pojia=469,},
{grade=31,need_item_num=14,gongji=1429,fangyu=714,maxhp=14288,pojia=476,},
{grade=32,need_item_num=15,gongji=1453,fangyu=726,maxhp=14528,pojia=484,},
{grade=33,need_item_num=16,gongji=1479,fangyu=739,maxhp=14786,pojia=493,},
{grade=34,need_item_num=17,gongji=1506,fangyu=753,maxhp=15056,pojia=502,},
{grade=35,need_item_num=18,gongji=1534,fangyu=767,maxhp=15344,pojia=511,},
{grade=36,need_item_num=19,gongji=1564,fangyu=782,maxhp=15644,pojia=521,},
{grade=37,need_item_num=20,gongji=1596,fangyu=798,maxhp=15956,pojia=532,},
{grade=38,need_item_num=21,gongji=1629,fangyu=814,maxhp=16286,pojia=543,},
{grade=39,need_item_num=22,gongji=1663,fangyu=831,maxhp=16628,pojia=554,},
{grade=40,need_item_num=23,msg_type=6,gongji=1974,fangyu=987,maxhp=19736,pojia=658,},
{grade=41,need_item_num=24,gongji=1999,fangyu=1000,maxhp=19994,pojia=666,},
{grade=42,need_item_num=25,gongji=2026,fangyu=1013,maxhp=20264,pojia=675,},
{grade=43,need_item_num=26,gongji=2055,fangyu=1028,maxhp=20552,pojia=685,},
{grade=44,need_item_num=27,gongji=2086,fangyu=1043,maxhp=20858,pojia=695,},
{grade=45,need_item_num=28,gongji=2118,fangyu=1059,maxhp=21176,pojia=706,},
{grade=46,need_item_num=29,gongji=2151,fangyu=1076,maxhp=21512,pojia=717,},
{grade=47,need_item_num=30,gongji=2187,fangyu=1093,maxhp=21866,pojia=729,},
{grade=48,need_item_num=31,gongji=2223,fangyu=1112,maxhp=22232,pojia=741,},
{grade=49,need_item_num=32,gongji=2262,fangyu=1131,maxhp=22616,pojia=754,},
{grade=50,need_item_num=1,msg_type=6,gongji=2611,fangyu=1306,maxhp=26114,pojia=870,},
{color=1,need_item_id=27703,need_item_num=1,active_passive_skill_index=-1,gongji=2640,fangyu=1320,maxhp=26402,pojia=880,},
{color=1,grade=1,need_item_id=27703,active_passive_skill_index=-1,gongji=2670,fangyu=1335,maxhp=26702,pojia=890,},
{color=1,gongji=2702,fangyu=1351,maxhp=27020,pojia=901,},
{color=1,gongji=2736,fangyu=1368,maxhp=27356,pojia=912,},
{color=1,gongji=2771,fangyu=1385,maxhp=27710,pojia=924,},
{color=1,grade=5,need_item_id=27703,need_item_num=6,gongji=2808,fangyu=1404,maxhp=28082,pojia=936,},
{color=1,grade=6,need_item_id=27703,need_item_num=7,gongji=2847,fangyu=1424,maxhp=28472,pojia=949,},
{color=1,grade=7,need_item_id=27703,need_item_num=8,gongji=2888,fangyu=1444,maxhp=28880,pojia=963,},
{color=1,grade=8,need_item_id=27703,need_item_num=9,gongji=2931,fangyu=1465,maxhp=29306,pojia=977,},
{color=1,grade=9,need_item_id=27703,need_item_num=9,gongji=3319,fangyu=1660,maxhp=33194,pojia=1106,},
{color=1,grade=10,need_item_id=27704,msg_type=6,gongji=3351,fangyu=1675,maxhp=33506,pojia=1117,},
{color=1,grade=11,need_item_id=27704,gongji=3384,fangyu=1692,maxhp=33836,pojia=1128,},
{color=1,grade=12,need_item_id=27704,gongji=3419,fangyu=1709,maxhp=34190,pojia=1140,},
{color=1,grade=13,need_item_id=27704,need_item_num=3,gongji=3456,fangyu=1728,maxhp=34562,pojia=1152,},
{color=1,grade=14,need_item_id=27704,need_item_num=3,gongji=3495,fangyu=1748,maxhp=34952,pojia=1165,},
{color=1,grade=15,need_item_id=27704,need_item_num=4,gongji=3536,fangyu=1768,maxhp=35360,pojia=1179,},
{color=1,grade=16,need_item_id=27704,need_item_num=4,gongji=3579,fangyu=1790,maxhp=35792,pojia=1193,},
{color=1,grade=17,need_item_id=27704,need_item_num=4,gongji=3624,fangyu=1812,maxhp=36242,pojia=1208,},
{color=1,grade=18,need_item_num=1,gongji=3671,fangyu=1835,maxhp=36710,pojia=1224,},
{color=1,grade=19,gongji=4098,fangyu=2049,maxhp=40982,pojia=1366,},
{color=1,grade=20,need_item_num=3,msg_type=6,gongji=4132,fangyu=2066,maxhp=41324,pojia=1377,},
{color=1,grade=21,need_item_num=4,gongji=4168,fangyu=2084,maxhp=41684,pojia=1389,},
{color=1,grade=22,need_item_num=5,gongji=4207,fangyu=2103,maxhp=42068,pojia=1402,},
{color=1,grade=23,need_item_num=6,gongji=4248,fangyu=2124,maxhp=42476,pojia=1416,},
{color=1,grade=24,need_item_num=7,gongji=4290,fangyu=2145,maxhp=42902,pojia=1430,},
{color=1,grade=25,need_item_num=8,gongji=4335,fangyu=2168,maxhp=43352,pojia=1445,},
{color=1,grade=26,need_item_num=9,gongji=4382,fangyu=2191,maxhp=43820,pojia=1461,},
{color=1,grade=27,need_item_num=10,gongji=4431,fangyu=2216,maxhp=44312,pojia=1477,},
{color=1,grade=28,need_item_num=11,gongji=4482,fangyu=2241,maxhp=44822,pojia=1494,},
{color=1,grade=29,need_item_num=12,gongji=4948,fangyu=2474,maxhp=49484,pojia=1649,},
{color=1,grade=30,need_item_num=13,msg_type=6,gongji=4986,fangyu=2493,maxhp=49856,pojia=1662,},
{color=1,grade=31,need_item_num=14,gongji=5025,fangyu=2512,maxhp=50246,pojia=1675,},
{color=1,grade=32,need_item_num=15,gongji=5066,fangyu=2533,maxhp=50660,pojia=1689,},
{color=1,grade=33,need_item_num=16,gongji=5110,fangyu=2555,maxhp=51098,pojia=1703,},
{color=1,grade=34,need_item_num=17,gongji=5156,fangyu=2578,maxhp=51560,pojia=1719,},
{color=1,grade=35,need_item_num=18,gongji=5205,fangyu=2602,maxhp=52046,pojia=1735,},
{color=1,grade=36,need_item_num=19,gongji=5256,fangyu=2628,maxhp=52556,pojia=1752,},
{color=1,grade=37,need_item_num=20,gongji=5308,fangyu=2654,maxhp=53084,pojia=1769,},
{color=1,grade=38,need_item_num=21,gongji=5364,fangyu=2682,maxhp=53636,pojia=1788,},
{color=1,grade=39,need_item_num=22,gongji=5869,fangyu=2934,maxhp=58688,pojia=1956,},
{color=1,grade=40,need_item_num=23,msg_type=6,gongji=5908,fangyu=2954,maxhp=59084,pojia=1969,},
{color=1,grade=41,need_item_num=24,gongji=5950,fangyu=2975,maxhp=59504,pojia=1983,},
{color=1,grade=42,need_item_num=25,gongji=5995,fangyu=2998,maxhp=59954,pojia=1998,},
{color=1,grade=43,need_item_num=26,gongji=6043,fangyu=3021,maxhp=60428,pojia=2014,},
{color=1,grade=44,need_item_num=27,gongji=6093,fangyu=3046,maxhp=60926,pojia=2031,},
{color=1,grade=45,need_item_num=28,gongji=6145,fangyu=3072,maxhp=61448,pojia=2048,},
{color=1,grade=46,need_item_num=29,gongji=6199,fangyu=3100,maxhp=61994,pojia=2066,},
{color=1,grade=47,need_item_num=30,gongji=6256,fangyu=3128,maxhp=62564,pojia=2085,},
{color=1,grade=48,need_item_num=31,gongji=6316,fangyu=3158,maxhp=63158,pojia=2105,},
{color=1,grade=49,need_item_num=32,gongji=6859,fangyu=3430,maxhp=68594,pojia=2286,},
{color=1,grade=50,need_item_num=1,msg_type=6,gongji=6902,fangyu=3451,maxhp=69020,pojia=2301,},
{color=2,need_item_id=27703,need_item_num=1,active_passive_skill_index=-1,gongji=6947,fangyu=3473,maxhp=69470,pojia=2316,},
{color=2,grade=1,need_item_id=27703,active_passive_skill_index=-1,gongji=6995,fangyu=3497,maxhp=69950,pojia=2332,},
{color=2,gongji=7045,fangyu=3523,maxhp=70454,pojia=2348,},
{color=2,gongji=7099,fangyu=3549,maxhp=70988,pojia=2366,},
{color=2,gongji=7155,fangyu=3577,maxhp=71546,pojia=2385,},
{color=2,grade=5,need_item_id=27703,need_item_num=6,gongji=7213,fangyu=3607,maxhp=72134,pojia=2404,},
{color=2,grade=6,need_item_id=27703,need_item_num=7,gongji=7275,fangyu=3637,maxhp=72746,pojia=2425,},
{color=2,grade=7,need_item_id=27703,need_item_num=8,gongji=7338,fangyu=3669,maxhp=73382,pojia=2446,},
{color=2,grade=8,need_item_id=27703,need_item_num=9,gongji=7921,fangyu=3960,maxhp=79208,pojia=2640,},
{color=2,grade=9,need_item_id=27703,need_item_num=9,gongji=7966,fangyu=3983,maxhp=79664,pojia=2655,},
{color=2,gongji=8014,fangyu=4007,maxhp=80144,pojia=2671,},
{color=2,grade=11,need_item_id=27704,active_passive_skill_index=1,gongji=8065,fangyu=4033,maxhp=80654,pojia=2688,},
{color=2,grade=12,need_item_id=27704,active_passive_skill_index=1,gongji=8119,fangyu=4060,maxhp=81194,pojia=2706,},
{color=2,gongji=8176,fangyu=4088,maxhp=81764,pojia=2725,},
{color=2,gongji=8236,fangyu=4118,maxhp=82358,pojia=2745,},
{grade=15,gongji=8298,fangyu=4149,maxhp=82982,pojia=2766,},
{grade=16,gongji=8364,fangyu=4182,maxhp=83636,pojia=2788,},
{grade=17,need_item_id=27704,gongji=8431,fangyu=4216,maxhp=84314,pojia=2810,},
{color=2,grade=18,need_item_num=1,active_passive_skill_index=1,gongji=9053,fangyu=4526,maxhp=90530,pojia=3018,},
{color=2,grade=19,active_passive_skill_index=1,gongji=9101,fangyu=4550,maxhp=91010,pojia=3034,},
{color=2,active_passive_skill_index=1,gongji=9152,fangyu=4576,maxhp=91520,pojia=3051,},
{color=2,grade=21,need_item_num=4,active_passive_skill_index=1,gongji=9206,fangyu=4603,maxhp=92060,pojia=3069,},
{color=2,grade=22,need_item_num=5,active_passive_skill_index=1,gongji=9263,fangyu=4631,maxhp=92630,pojia=3088,},
{color=2,grade=23,need_item_num=6,active_passive_skill_index=1,gongji=9323,fangyu=4661,maxhp=93230,pojia=3108,},
{color=2,grade=24,need_item_num=7,active_passive_skill_index=1,gongji=9386,fangyu=4693,maxhp=93860,pojia=3129,},
{color=2,grade=25,need_item_num=8,active_passive_skill_index=1,gongji=9452,fangyu=4726,maxhp=94520,pojia=3151,},
{color=2,grade=26,need_item_num=9,active_passive_skill_index=1,gongji=9521,fangyu=4760,maxhp=95210,pojia=3174,},
{color=2,grade=27,need_item_num=10,active_passive_skill_index=1,gongji=9593,fangyu=4796,maxhp=95930,pojia=3198,},
{color=2,grade=28,need_item_num=11,active_passive_skill_index=1,gongji=10253,fangyu=5126,maxhp=102530,pojia=3418,},
{color=2,grade=29,need_item_num=12,active_passive_skill_index=1,gongji=10304,fangyu=5152,maxhp=103040,pojia=3435,},
{color=2,active_passive_skill_index=1,gongji=10358,fangyu=5179,maxhp=103580,pojia=3453,},
{color=2,grade=31,need_item_num=14,active_passive_skill_index=1,gongji=10416,fangyu=5208,maxhp=104156,pojia=3472,},
{color=2,grade=32,need_item_num=15,active_passive_skill_index=1,gongji=10476,fangyu=5238,maxhp=104762,pojia=3492,},
{color=2,grade=33,need_item_num=16,active_passive_skill_index=1,gongji=10540,fangyu=5270,maxhp=105398,pojia=3513,},
{color=2,grade=34,need_item_num=17,active_passive_skill_index=1,gongji=10607,fangyu=5303,maxhp=106070,pojia=3536,},
{color=2,grade=35,need_item_num=18,active_passive_skill_index=1,gongji=10677,fangyu=5339,maxhp=106772,pojia=3559,},
{color=2,grade=36,need_item_num=19,active_passive_skill_index=1,gongji=10750,fangyu=5375,maxhp=107504,pojia=3583,},
{color=2,grade=37,need_item_num=20,active_passive_skill_index=1,gongji=10827,fangyu=5414,maxhp=108272,pojia=3609,},
{color=2,grade=38,need_item_num=21,active_passive_skill_index=1,gongji=11526,fangyu=5763,maxhp=115262,pojia=3842,},
{color=2,grade=39,need_item_num=22,active_passive_skill_index=1,gongji=11580,fangyu=5790,maxhp=115802,pojia=3860,},
{color=2,active_passive_skill_index=1,gongji=11637,fangyu=5819,maxhp=116372,pojia=3879,},
{color=2,grade=41,need_item_num=24,active_passive_skill_index=1,gongji=11698,fangyu=5849,maxhp=116978,pojia=3899,},
{color=2,grade=42,need_item_num=25,active_passive_skill_index=1,gongji=11762,fangyu=5881,maxhp=117620,pojia=3921,},
{color=2,grade=43,need_item_num=26,active_passive_skill_index=1,gongji=11829,fangyu=5915,maxhp=118292,pojia=3943,},
{color=2,grade=44,need_item_num=27,active_passive_skill_index=1,gongji=11900,fangyu=5950,maxhp=119000,pojia=3967,},
{color=2,grade=45,need_item_num=28,active_passive_skill_index=1,gongji=11974,fangyu=5987,maxhp=119738,pojia=3991,},
{color=2,grade=46,need_item_num=29,active_passive_skill_index=1,gongji=12051,fangyu=6026,maxhp=120512,pojia=4017,},
{color=2,grade=47,need_item_num=30,active_passive_skill_index=1,gongji=12132,fangyu=6066,maxhp=121322,pojia=4044,},
{color=2,grade=48,need_item_num=31,active_passive_skill_index=1,gongji=12870,fangyu=6435,maxhp=128702,pojia=4290,},
{color=2,grade=49,need_item_num=32,active_passive_skill_index=1,gongji=7832,fangyu=5221,maxhp=195800,pojia=2611,},
{grade=50,msg_type=6,gongji=8090,fangyu=5393,maxhp=202250,pojia=2697,},
{color=3,},
{color=3,grade=1,need_item_id=27703,active_passive_skill_index=-1,gongji=100,fangyu=67,maxhp=2500,pojia=33,},
{color=3,gongji=203,fangyu=135,maxhp=5063,pojia=68,},
{color=3,gongji=308,fangyu=205,maxhp=7688,pojia=103,},
{color=3,gongji=415,fangyu=277,maxhp=10375,pojia=138,},
{color=3,grade=5,need_item_id=27703,need_item_num=6,gongji=525,fangyu=350,maxhp=13125,pojia=175,},
{color=3,grade=6,need_item_id=27703,need_item_num=7,gongji=638,fangyu=425,maxhp=15938,pojia=213,},
{color=3,grade=7,need_item_id=27703,need_item_num=8,gongji=753,fangyu=502,maxhp=18813,pojia=251,},
{color=3,grade=8,need_item_id=27703,need_item_num=9,gongji=870,fangyu=580,maxhp=21750,pojia=290,},
{color=3,grade=9,need_item_id=27703,need_item_num=9,gongji=990,fangyu=660,maxhp=24750,pojia=330,},
{color=3,gongji=1113,fangyu=742,maxhp=27813,pojia=371,},
{color=3,grade=11,need_item_id=27704,active_passive_skill_index=1,gongji=1240,fangyu=827,maxhp=31000,pojia=413,},
{color=3,grade=12,need_item_id=27704,active_passive_skill_index=1,gongji=1373,fangyu=915,maxhp=34313,pojia=458,},
{color=3,gongji=1510,fangyu=1007,maxhp=37750,pojia=503,},
{grade=14,gongji=1653,fangyu=1102,maxhp=41313,pojia=551,},
{grade=15,gongji=1800,fangyu=1200,maxhp=45000,pojia=600,},
{color=3,gongji=1953,fangyu=1302,maxhp=48813,pojia=651,},
{grade=17,gongji=2110,fangyu=1407,maxhp=52750,pojia=703,},
{color=3,grade=18,need_item_num=1,active_passive_skill_index=2,gongji=2273,fangyu=1515,maxhp=56813,pojia=758,},
{color=3,grade=19,active_passive_skill_index=2,gongji=2440,fangyu=1627,maxhp=61000,pojia=813,},
{color=3,active_passive_skill_index=2,gongji=2613,fangyu=1742,maxhp=65313,pojia=871,},
{color=3,grade=21,need_item_num=4,active_passive_skill_index=2,gongji=2790,fangyu=1860,maxhp=69750,pojia=930,},
{color=3,grade=22,need_item_num=5,active_passive_skill_index=2,gongji=2973,fangyu=1982,maxhp=74313,pojia=991,},
{color=3,grade=23,need_item_num=6,active_passive_skill_index=2,gongji=3160,fangyu=2107,maxhp=79000,pojia=1053,},
{color=3,grade=24,need_item_num=7,active_passive_skill_index=2,gongji=3353,fangyu=2235,maxhp=83813,pojia=1118,},
{color=3,grade=25,need_item_num=8,active_passive_skill_index=2,gongji=3550,fangyu=2367,maxhp=88750,pojia=1183,},
{color=3,grade=26,need_item_num=9,active_passive_skill_index=2,gongji=3753,fangyu=2502,maxhp=93813,pojia=1251,},
{color=3,grade=27,need_item_num=10,active_passive_skill_index=2,gongji=3960,fangyu=2640,maxhp=99000,pojia=1320,},
{color=3,grade=28,need_item_num=11,active_passive_skill_index=2,gongji=4173,fangyu=2782,maxhp=104313,pojia=1391,},
{color=3,grade=29,need_item_num=12,active_passive_skill_index=2,gongji=4390,fangyu=2927,maxhp=109750,pojia=1463,},
{color=3,active_passive_skill_index=2,gongji=4613,fangyu=3075,maxhp=115313,pojia=1538,},
{color=3,grade=31,need_item_num=14,active_passive_skill_index=2,gongji=4840,fangyu=3227,maxhp=121000,pojia=1613,},
{color=3,grade=32,need_item_num=15,active_passive_skill_index=2,gongji=5073,fangyu=3382,maxhp=126813,pojia=1691,},
{color=3,grade=33,need_item_num=16,active_passive_skill_index=2,gongji=5310,fangyu=3540,maxhp=132750,pojia=1770,},
{color=3,grade=34,need_item_num=17,active_passive_skill_index=2,gongji=5553,fangyu=3702,maxhp=138813,pojia=1851,},
{color=3,grade=35,need_item_num=18,active_passive_skill_index=2,gongji=5800,fangyu=3867,maxhp=145000,pojia=1933,},
{color=3,grade=36,need_item_num=19,active_passive_skill_index=2,gongji=6053,fangyu=4035,maxhp=151313,pojia=2018,},
{color=3,grade=37,need_item_num=20,active_passive_skill_index=2,gongji=6310,fangyu=4207,maxhp=157750,pojia=2103,},
{color=3,grade=38,need_item_num=21,active_passive_skill_index=2,gongji=6573,fangyu=4382,maxhp=164313,pojia=2191,},
{color=3,grade=39,need_item_num=22,active_passive_skill_index=2,gongji=6840,fangyu=4560,maxhp=171000,pojia=2280,},
{color=3,active_passive_skill_index=2,gongji=7113,fangyu=4742,maxhp=177813,pojia=2371,},
{color=3,grade=41,need_item_num=24,active_passive_skill_index=2,gongji=7390,fangyu=4927,maxhp=184750,pojia=2463,},
{color=3,grade=42,need_item_num=25,active_passive_skill_index=2,gongji=7673,fangyu=5115,maxhp=191813,pojia=2558,},
{color=3,grade=43,need_item_num=26,active_passive_skill_index=2,gongji=7960,fangyu=5307,maxhp=199000,pojia=2653,},
{color=3,grade=44,need_item_num=27,active_passive_skill_index=2,gongji=8253,fangyu=5502,maxhp=206313,pojia=2751,},
{color=3,grade=45,need_item_num=28,active_passive_skill_index=2,gongji=8550,fangyu=5700,maxhp=213750,pojia=2850,},
{color=3,grade=46,need_item_num=29,active_passive_skill_index=2,gongji=8853,fangyu=5902,maxhp=221313,pojia=2951,},
{color=3,grade=47,need_item_num=30,active_passive_skill_index=2,gongji=9160,fangyu=6107,maxhp=229000,pojia=3053,},
{color=3,grade=48,need_item_num=31,active_passive_skill_index=2,gongji=9473,fangyu=6315,maxhp=236813,pojia=3158,},
{color=3,grade=49,need_item_num=32,active_passive_skill_index=2,gongji=9790,fangyu=6527,maxhp=244750,pojia=3263,},
{color=3,active_passive_skill_index=2,gongji=10113,fangyu=6742,maxhp=252813,pojia=3371,},
{color=4,},
{color=4,grade=1,need_item_id=27703,active_passive_skill_index=-1,gongji=150,fangyu=100,maxhp=3750,pojia=50,},
{color=4,gongji=304,fangyu=203,maxhp=7594,pojia=101,},
{color=4,gongji=461,fangyu=308,maxhp=11531,pojia=154,},
{color=4,gongji=623,fangyu=415,maxhp=15563,pojia=208,},
{color=4,grade=5,need_item_id=27703,need_item_num=6,gongji=788,fangyu=525,maxhp=19688,pojia=263,},
{color=4,grade=6,need_item_id=27703,need_item_num=7,gongji=956,fangyu=638,maxhp=23906,pojia=319,},
{color=4,grade=7,need_item_id=27703,need_item_num=8,gongji=1129,fangyu=753,maxhp=28219,pojia=376,},
{color=4,grade=8,need_item_id=27703,need_item_num=9,gongji=1305,fangyu=870,maxhp=32625,pojia=435,},
{color=4,grade=9,need_item_id=27703,need_item_num=9,gongji=1485,fangyu=990,maxhp=37125,pojia=495,},
{color=4,gongji=1669,fangyu=1113,maxhp=41719,pojia=556,},
{color=4,grade=11,need_item_id=27704,active_passive_skill_index=1,gongji=1860,fangyu=1240,maxhp=46500,pojia=620,},
{color=4,grade=12,need_item_id=27704,active_passive_skill_index=1,gongji=2059,fangyu=1373,maxhp=51469,pojia=686,},
{color=4,gongji=2265,fangyu=1510,maxhp=56625,pojia=755,},
{color=4,gongji=2479,fangyu=1653,maxhp=61969,pojia=826,},
{grade=15,gongji=2700,fangyu=1800,maxhp=67500,pojia=900,},
{color=4,gongji=2929,fangyu=1953,maxhp=73219,pojia=976,},
{color=4,gongji=3165,fangyu=2110,maxhp=79125,pojia=1055,},
{color=4,grade=18,need_item_num=1,active_passive_skill_index=2,gongji=3409,fangyu=2273,maxhp=85219,pojia=1136,},
{color=4,grade=19,active_passive_skill_index=2,gongji=3660,fangyu=2440,maxhp=91500,pojia=1220,},
{color=4,gongji=3919,fangyu=2613,maxhp=97969,pojia=1306,},
{color=4,grade=21,need_item_num=4,active_passive_skill_index=3,gongji=4185,fangyu=2790,maxhp=104625,pojia=1395,},
{color=4,grade=22,need_item_num=5,active_passive_skill_index=3,gongji=4459,fangyu=2973,maxhp=111469,pojia=1486,},
{color=4,grade=23,need_item_num=6,active_passive_skill_index=3,gongji=4740,fangyu=3160,maxhp=118500,pojia=1580,},
{color=4,grade=24,need_item_num=7,active_passive_skill_index=3,gongji=5029,fangyu=3353,maxhp=125719,pojia=1676,},
{color=4,grade=25,need_item_num=8,active_passive_skill_index=3,gongji=5325,fangyu=3550,maxhp=133125,pojia=1775,},
{color=4,grade=26,need_item_num=9,active_passive_skill_index=3,gongji=5629,fangyu=3753,maxhp=140719,pojia=1876,},
{color=4,grade=27,need_item_num=10,active_passive_skill_index=3,gongji=5940,fangyu=3960,maxhp=148500,pojia=1980,},
{color=4,grade=28,need_item_num=11,active_passive_skill_index=3,gongji=6259,fangyu=4173,maxhp=156469,pojia=2086,},
{color=4,grade=29,need_item_num=12,active_passive_skill_index=3,gongji=6585,fangyu=4390,maxhp=164625,pojia=2195,},
{color=4,gongji=6919,fangyu=4613,maxhp=172969,pojia=2306,},
{color=4,grade=31,need_item_num=14,active_passive_skill_index=3,gongji=7260,fangyu=4840,maxhp=181500,pojia=2420,},
{color=4,grade=32,need_item_num=15,active_passive_skill_index=3,gongji=7609,fangyu=5073,maxhp=190219,pojia=2536,},
{color=4,grade=33,need_item_num=16,active_passive_skill_index=3,gongji=7965,fangyu=5310,maxhp=199125,pojia=2655,},
{color=4,grade=34,need_item_num=17,active_passive_skill_index=3,gongji=8329,fangyu=5553,maxhp=208219,pojia=2776,},
{color=4,grade=35,need_item_num=18,active_passive_skill_index=3,gongji=8700,fangyu=5800,maxhp=217500,pojia=2900,},
{color=4,grade=36,need_item_num=19,active_passive_skill_index=3,gongji=9079,fangyu=6053,maxhp=226969,pojia=3026,},
{color=4,grade=37,need_item_num=20,active_passive_skill_index=3,gongji=9465,fangyu=6310,maxhp=236625,pojia=3155,},
{color=4,grade=38,need_item_num=21,active_passive_skill_index=3,gongji=9859,fangyu=6573,maxhp=246469,pojia=3286,},
{color=4,grade=39,need_item_num=22,active_passive_skill_index=3,gongji=10260,fangyu=6840,maxhp=256500,pojia=3420,},
{color=4,gongji=10669,fangyu=7113,maxhp=266719,pojia=3556,},
{color=4,grade=41,need_item_num=24,active_passive_skill_index=3,gongji=11085,fangyu=7390,maxhp=277125,pojia=3695,},
{color=4,grade=42,need_item_num=25,active_passive_skill_index=3,gongji=11509,fangyu=7673,maxhp=287719,pojia=3836,},
{color=4,grade=43,need_item_num=26,active_passive_skill_index=3,gongji=11940,fangyu=7960,maxhp=298500,pojia=3980,},
{color=4,grade=44,need_item_num=27,active_passive_skill_index=3,gongji=12379,fangyu=8253,maxhp=309469,pojia=4126,},
{color=4,grade=45,need_item_num=28,active_passive_skill_index=3,gongji=12825,fangyu=8550,maxhp=320625,pojia=4275,},
{color=4,grade=46,need_item_num=29,active_passive_skill_index=3,gongji=13279,fangyu=8853,maxhp=331969,pojia=4426,},
{color=4,grade=47,need_item_num=30,active_passive_skill_index=3,gongji=13740,fangyu=9160,maxhp=343500,pojia=4580,},
{color=4,grade=48,need_item_num=31,active_passive_skill_index=3,gongji=14209,fangyu=9473,maxhp=355219,pojia=4736,},
{color=4,grade=49,need_item_num=32,active_passive_skill_index=3,gongji=14685,fangyu=9790,maxhp=367125,pojia=4895,},
{color=4,active_passive_skill_index=3,gongji=15169,fangyu=10113,maxhp=379219,pojia=5056,},
{color=5,},
{color=5,grade=1,need_item_id=27703,active_passive_skill_index=-1,gongji=200,fangyu=133,maxhp=5000,pojia=67,},
{color=5,gongji=405,fangyu=270,maxhp=10125,pojia=135,},
{color=5,gongji=615,fangyu=410,maxhp=15375,pojia=205,},
{color=5,gongji=830,fangyu=553,maxhp=20750,pojia=277,},
{color=5,grade=5,need_item_id=27703,need_item_num=6,gongji=1050,fangyu=700,maxhp=26250,pojia=350,},
{color=5,grade=6,need_item_id=27703,need_item_num=7,gongji=1275,fangyu=850,maxhp=31875,pojia=425,},
{color=5,grade=7,need_item_id=27703,need_item_num=8,gongji=1505,fangyu=1003,maxhp=37625,pojia=502,},
{color=5,grade=8,need_item_id=27703,need_item_num=9,gongji=1740,fangyu=1160,maxhp=43500,pojia=580,},
{color=5,grade=9,need_item_id=27703,need_item_num=9,gongji=1980,fangyu=1320,maxhp=49500,pojia=660,},
{color=5,active_passive_skill_index=1,gongji=2225,fangyu=1483,maxhp=55625,pojia=742,},
{color=5,grade=11,need_item_id=27704,active_passive_skill_index=1,gongji=2480,fangyu=1653,maxhp=62000,pojia=827,},
{color=5,grade=12,need_item_id=27704,active_passive_skill_index=1,gongji=2745,fangyu=1830,maxhp=68625,pojia=915,},
{color=5,active_passive_skill_index=1,gongji=3020,fangyu=2013,maxhp=75500,pojia=1007,},
{grade=14,gongji=3305,fangyu=2203,maxhp=82625,pojia=1102,},
{grade=15,gongji=3600,fangyu=2400,maxhp=90000,pojia=1200,},
{color=5,active_passive_skill_index=2,gongji=3905,fangyu=2603,maxhp=97625,pojia=1302,},
{grade=17,gongji=4220,fangyu=2813,maxhp=105500,pojia=1407,},
{color=5,grade=18,need_item_num=1,active_passive_skill_index=2,gongji=4545,fangyu=3030,maxhp=113625,pojia=1515,},
{color=5,grade=19,active_passive_skill_index=2,gongji=4880,fangyu=3253,maxhp=122000,pojia=1627,},
{color=5,active_passive_skill_index=3,gongji=5225,fangyu=3483,maxhp=130625,pojia=1742,},
{color=5,grade=21,need_item_num=4,active_passive_skill_index=3,gongji=5580,fangyu=3720,maxhp=139500,pojia=1860,},
{color=5,grade=22,need_item_num=5,active_passive_skill_index=3,gongji=5945,fangyu=3963,maxhp=148625,pojia=1982,},
{color=5,grade=23,need_item_num=6,active_passive_skill_index=3,gongji=6320,fangyu=4213,maxhp=158000,pojia=2107,},
{color=5,grade=24,need_item_num=7,active_passive_skill_index=3,gongji=6705,fangyu=4470,maxhp=167625,pojia=2235,},
{color=5,grade=25,need_item_num=8,active_passive_skill_index=3,gongji=7100,fangyu=4733,maxhp=177500,pojia=2367,},
{color=5,grade=26,need_item_num=9,active_passive_skill_index=3,gongji=7505,fangyu=5003,maxhp=187625,pojia=2502,},
{color=5,grade=27,need_item_num=10,active_passive_skill_index=3,gongji=7920,fangyu=5280,maxhp=198000,pojia=2640,},
{color=5,grade=28,need_item_num=11,active_passive_skill_index=3,gongji=8345,fangyu=5563,maxhp=208625,pojia=2782,},
{color=5,grade=29,need_item_num=12,active_passive_skill_index=3,gongji=8780,fangyu=5853,maxhp=219500,pojia=2927,},
{color=5,active_passive_skill_index=3,gongji=9225,fangyu=6150,maxhp=230625,pojia=3075,},
{color=5,grade=31,need_item_num=14,active_passive_skill_index=3,gongji=9680,fangyu=6453,maxhp=242000,pojia=3227,},
{color=5,grade=32,need_item_num=15,active_passive_skill_index=3,gongji=10145,fangyu=6763,maxhp=253625,pojia=3382,},
{color=5,grade=33,need_item_num=16,active_passive_skill_index=3,gongji=10620,fangyu=7080,maxhp=265500,pojia=3540,},
{color=5,grade=34,need_item_num=17,active_passive_skill_index=3,gongji=11105,fangyu=7403,maxhp=277625,pojia=3702,},
{color=5,grade=35,need_item_num=18,active_passive_skill_index=3,gongji=11600,fangyu=7733,maxhp=290000,pojia=3867,},
{color=5,grade=36,need_item_num=19,active_passive_skill_index=3,gongji=12105,fangyu=8070,maxhp=302625,pojia=4035,},
{color=5,grade=37,need_item_num=20,active_passive_skill_index=3,gongji=12620,fangyu=8413,maxhp=315500,pojia=4207,},
{color=5,grade=38,need_item_num=21,active_passive_skill_index=3,gongji=13145,fangyu=8763,maxhp=328625,pojia=4382,},
{color=5,grade=39,need_item_num=22,active_passive_skill_index=3,gongji=13680,fangyu=9120,maxhp=342000,pojia=4560,},
{color=5,active_passive_skill_index=3,gongji=14225,fangyu=9483,maxhp=355625,pojia=4742,},
{color=5,grade=41,need_item_num=24,active_passive_skill_index=3,gongji=14780,fangyu=9853,maxhp=369500,pojia=4927,},
{color=5,grade=42,need_item_num=25,active_passive_skill_index=3,gongji=15345,fangyu=10230,maxhp=383625,pojia=5115,},
{color=5,grade=43,need_item_num=26,active_passive_skill_index=3,gongji=15920,fangyu=10613,maxhp=398000,pojia=5307,},
{color=5,grade=44,need_item_num=27,active_passive_skill_index=3,gongji=16505,fangyu=11003,maxhp=412625,pojia=5502,},
{color=5,grade=45,need_item_num=28,active_passive_skill_index=3,gongji=17100,fangyu=11400,maxhp=427500,pojia=5700,},
{color=5,grade=46,need_item_num=29,active_passive_skill_index=3,gongji=17705,fangyu=11803,maxhp=442625,pojia=5902,},
{color=5,grade=47,need_item_num=30,active_passive_skill_index=3,gongji=18320,fangyu=12213,maxhp=458000,pojia=6107,},
{color=5,grade=48,need_item_num=31,active_passive_skill_index=3,gongji=18945,fangyu=12630,maxhp=473625,pojia=6315,},
{color=5,grade=49,need_item_num=32,active_passive_skill_index=3,gongji=19580,fangyu=13053,maxhp=489500,pojia=6527,},
{color=5,gongji=20225,fangyu=13483,maxhp=505625,pojia=6742,}
},

pet_upgrade_meta_table_map={
[154]=1,	-- depth:1
[205]=154,	-- depth:2
[256]=205,	-- depth:3
[286]=82,	-- depth:1
[276]=72,	-- depth:1
[269]=65,	-- depth:1
[296]=92,	-- depth:1
[272]=68,	-- depth:1
[271]=272,	-- depth:2
[270]=269,	-- depth:2
[273]=272,	-- depth:2
[266]=62,	-- depth:1
[153]=121,	-- depth:1
[259]=4,	-- depth:1
[164]=266,	-- depth:2
[158]=5,	-- depth:1
[157]=4,	-- depth:1
[156]=3,	-- depth:1
[143]=92,	-- depth:1
[133]=82,	-- depth:1
[123]=72,	-- depth:1
[120]=124,	-- depth:1
[119]=120,	-- depth:2
[118]=120,	-- depth:2
[117]=270,	-- depth:3
[116]=269,	-- depth:2
[113]=266,	-- depth:2
[107]=5,	-- depth:1
[106]=4,	-- depth:1
[105]=3,	-- depth:1
[56]=5,	-- depth:1
[55]=4,	-- depth:1
[54]=3,	-- depth:1
[167]=269,	-- depth:2
[260]=5,	-- depth:1
[168]=167,	-- depth:3
[170]=272,	-- depth:2
[258]=3,	-- depth:1
[255]=102,	-- depth:1
[245]=296,	-- depth:2
[235]=286,	-- depth:2
[225]=276,	-- depth:2
[222]=273,	-- depth:3
[221]=272,	-- depth:2
[220]=221,	-- depth:3
[219]=270,	-- depth:3
[218]=269,	-- depth:2
[215]=266,	-- depth:2
[209]=5,	-- depth:1
[208]=4,	-- depth:1
[207]=3,	-- depth:1
[204]=102,	-- depth:1
[194]=92,	-- depth:1
[184]=82,	-- depth:1
[174]=72,	-- depth:1
[171]=170,	-- depth:3
[169]=170,	-- depth:3
[306]=255,	-- depth:2
},
pet_upstar={
{need_num=999,need_item_id=27706,msg_type=-1,},
{star=1,maxhp=50,gongji=50,fangyu=50,pojia=50,yuansu_sh=50,yuansu_hj=50,},
{star=2,maxhp=100,gongji=100,fangyu=100,pojia=100,yuansu_sh=100,yuansu_hj=100,},
{star=3,maxhp=150,gongji=150,fangyu=150,pojia=150,yuansu_sh=150,yuansu_hj=150,},
{star=4,maxhp=200,gongji=200,fangyu=200,pojia=200,yuansu_sh=200,yuansu_hj=200,},
{star=5,maxhp=250,gongji=250,fangyu=250,pojia=250,yuansu_sh=250,yuansu_hj=250,},
{star=6,need_num=999,need_item_id=27706,maxhp=300,gongji=300,fangyu=300,pojia=300,yuansu_sh=300,yuansu_hj=300,},
{star=7,maxhp=350,gongji=350,fangyu=350,pojia=350,yuansu_sh=350,yuansu_hj=350,},
{star=8,maxhp=400,gongji=400,fangyu=400,pojia=400,yuansu_sh=400,yuansu_hj=400,},
{star=9,maxhp=450,gongji=450,fangyu=450,pojia=450,yuansu_sh=450,yuansu_hj=450,},
{star=10,maxhp=500,gongji=500,fangyu=500,pojia=500,yuansu_sh=500,yuansu_hj=500,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=2,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=3,},
{pet_id=4,},
{pet_id=4,},
{pet_id=4,},
{pet_id=4,},
{pet_id=4,star=4,need_item_id=27707,},
{pet_id=4,star=5,need_item_id=27707,},
{star=6,maxhp=600,gongji=600,fangyu=600,pojia=600,yuansu_sh=600,yuansu_hj=600,},
{star=7,maxhp=700,gongji=700,fangyu=700,pojia=700,yuansu_sh=700,yuansu_hj=700,},
{star=8,maxhp=800,gongji=800,fangyu=800,pojia=800,yuansu_sh=800,yuansu_hj=800,},
{star=9,maxhp=900,gongji=900,fangyu=900,pojia=900,yuansu_sh=900,yuansu_hj=900,},
{pet_id=4,star=10,maxhp=1000,gongji=1000,fangyu=1000,pojia=1000,yuansu_sh=1000,yuansu_hj=1000,},
{pet_id=5,need_item_id=27707,},
{pet_id=5,star=1,need_item_id=27707,},
{pet_id=5,star=2,need_item_id=27707,},
{pet_id=5,star=3,need_item_id=27707,},
{pet_id=5,},
{pet_id=5,},
{pet_id=5,},
{pet_id=5,},
{pet_id=5,},
{pet_id=5,},
{pet_id=5,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=6,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=7,},
{pet_id=8,need_item_id=27708,},
{pet_id=8,},
{pet_id=8,},
{pet_id=8,},
{pet_id=8,},
{pet_id=8,},
{pet_id=8,},
{pet_id=8,},
{pet_id=8,},
{pet_id=8,},
{pet_id=8,star=10,zs_skill_level=11,need_item_id=27708,extra_grow_rate=60,maxhp=36406,gongji=1456,fangyu=971,pojia=485,yuansu_sh=971,yuansu_hj=485,},
{pet_id=9,},
{pet_id=9,},
{pet_id=9,},
{pet_id=9,star=3,zs_skill_level=4,need_item_id=27708,extra_grow_rate=18,maxhp=10922,gongji=437,fangyu=291,pojia=146,yuansu_sh=291,yuansu_hj=146,},
{pet_id=9,star=4,zs_skill_level=5,need_item_id=27708,extra_grow_rate=24,maxhp=14563,gongji=583,fangyu=388,pojia=194,yuansu_sh=388,yuansu_hj=194,},
{pet_id=9,},
{pet_id=9,},
{pet_id=9,},
{pet_id=9,},
{pet_id=9,},
{pet_id=9,},
{pet_id=10,},
{pet_id=10,},
{pet_id=10,},
{pet_id=10,},
{pet_id=10,},
{pet_id=10,star=5,zs_skill_level=6,need_item_id=27708,extra_grow_rate=30,maxhp=18203,gongji=728,fangyu=485,pojia=243,yuansu_sh=485,yuansu_hj=243,},
{pet_id=10,star=6,zs_skill_level=7,need_num=2,need_item_id=27708,extra_grow_rate=36,maxhp=21844,gongji=874,fangyu=583,pojia=291,yuansu_sh=583,yuansu_hj=291,},
{pet_id=10,star=7,zs_skill_level=8,need_num=3,need_item_id=27708,extra_grow_rate=42,maxhp=25484,gongji=1019,fangyu=680,pojia=340,yuansu_sh=680,yuansu_hj=340,},
{pet_id=10,star=8,zs_skill_level=9,need_num=4,need_item_id=27708,extra_grow_rate=48,maxhp=29125,gongji=1165,fangyu=777,pojia=388,yuansu_sh=777,yuansu_hj=388,},
{pet_id=10,star=9,zs_skill_level=10,need_num=5,need_item_id=27708,extra_grow_rate=54,maxhp=32766,gongji=1311,fangyu=874,pojia=437,yuansu_sh=874,yuansu_hj=437,},
{pet_id=10,},
{pet_id=11,},
{pet_id=11,star=1,zs_skill_level=2,need_item_id=27708,extra_grow_rate=6,maxhp=3641,gongji=146,fangyu=97,pojia=49,yuansu_sh=97,yuansu_hj=49,},
{pet_id=11,star=2,zs_skill_level=3,need_item_id=27708,extra_grow_rate=12,maxhp=7281,gongji=291,fangyu=194,pojia=97,yuansu_sh=194,yuansu_hj=97,},
{pet_id=11,},
{pet_id=11,},
{pet_id=11,},
{pet_id=11,},
{pet_id=11,},
{pet_id=11,},
{pet_id=11,},
{pet_id=11,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=12,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=13,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=14,},
{pet_id=15,},
{pet_id=15,},
{pet_id=15,star=2,zs_skill_level=3,extra_grow_rate=16,maxhp=11175,gongji=447,fangyu=298,pojia=149,yuansu_sh=298,yuansu_hj=149,},
{pet_id=15,},
{pet_id=15,star=4,zs_skill_level=5,extra_grow_rate=32,maxhp=22350,gongji=894,fangyu=596,pojia=298,yuansu_sh=596,yuansu_hj=298,},
{pet_id=15,},
{pet_id=15,},
{pet_id=15,},
{pet_id=15,},
{pet_id=15,},
{pet_id=15,},
{pet_id=16,},
{pet_id=16,},
{pet_id=16,},
{pet_id=16,},
{pet_id=16,},
{pet_id=16,},
{pet_id=16,star=6,active_skill_level=2,zs_skill_level=7,need_num=2,extra_grow_rate=48,maxhp=33525,gongji=1341,fangyu=894,pojia=447,yuansu_sh=894,yuansu_hj=447,},
{pet_id=16,},
{pet_id=16,},
{pet_id=16,},
{pet_id=16,},
{pet_id=17,},
{pet_id=17,star=1,zs_skill_level=2,extra_grow_rate=8,maxhp=5588,gongji=224,fangyu=149,pojia=75,yuansu_sh=149,yuansu_hj=75,},
{pet_id=17,},
{pet_id=17,},
{pet_id=17,},
{pet_id=17,},
{pet_id=17,},
{pet_id=17,},
{pet_id=17,},
{pet_id=17,},
{pet_id=17,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=18,},
{pet_id=19,},
{pet_id=19,},
{pet_id=19,},
{pet_id=19,},
{pet_id=19,},
{pet_id=19,},
{pet_id=19,},
{pet_id=19,},
{pet_id=19,star=8,active_skill_level=2,zs_skill_level=9,need_num=4,extra_grow_rate=64,maxhp=44700,gongji=1788,fangyu=1192,pojia=596,yuansu_sh=1192,yuansu_hj=596,},
{pet_id=19,star=9,active_skill_level=2,zs_skill_level=10,need_num=5,extra_grow_rate=72,maxhp=50288,gongji=2012,fangyu=1341,pojia=671,yuansu_sh=1341,yuansu_hj=671,},
{pet_id=19,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=20,},
{pet_id=21,},
{pet_id=21,},
{pet_id=21,},
{pet_id=21,star=3,zs_skill_level=4,extra_grow_rate=24,maxhp=16763,gongji=671,fangyu=447,pojia=224,yuansu_sh=447,yuansu_hj=224,},
{pet_id=21,},
{pet_id=21,},
{pet_id=21,},
{pet_id=21,},
{pet_id=21,},
{pet_id=21,},
{pet_id=21,star=10,active_skill_level=3,zs_skill_level=11,extra_grow_rate=80,maxhp=55875,gongji=2235,fangyu=1490,pojia=745,yuansu_sh=1490,yuansu_hj=745,},
{pet_id=22,},
{pet_id=22,},
{pet_id=22,},
{pet_id=22,},
{pet_id=22,},
{pet_id=22,star=5,active_skill_level=2,zs_skill_level=6,extra_grow_rate=40,maxhp=27938,gongji=1118,fangyu=745,pojia=373,yuansu_sh=745,yuansu_hj=373,},
{pet_id=22,},
{pet_id=22,star=7,active_skill_level=2,zs_skill_level=8,need_num=3,extra_grow_rate=56,maxhp=39113,gongji=1565,fangyu=1043,pojia=522,yuansu_sh=1043,yuansu_hj=522,},
{pet_id=22,},
{pet_id=22,},
{pet_id=22,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=23,},
{pet_id=24,zs_skill_level=1,msg_type=-1,},
{pet_id=24,},
{pet_id=24,},
{pet_id=24,},
{pet_id=24,},
{pet_id=24,},
{pet_id=24,},
{pet_id=24,},
{pet_id=24,},
{pet_id=24,},
{pet_id=24,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=25,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=26,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=27,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=28,},
{pet_id=29,need_item_id=27710,},
{pet_id=29,},
{pet_id=29,},
{pet_id=29,},
{pet_id=29,},
{pet_id=29,star=5,active_skill_level=2,zs_skill_level=6,need_item_id=27710,extra_grow_rate=55,maxhp=63840,gongji=2554,fangyu=1702,pojia=851,yuansu_sh=1702,yuansu_hj=851,},
{pet_id=29,},
{pet_id=29,},
{pet_id=29,},
{pet_id=29,},
{pet_id=29,star=10,active_skill_level=3,zs_skill_level=11,need_item_id=27710,extra_grow_rate=110,maxhp=127680,gongji=5107,fangyu=3405,pojia=1702,yuansu_sh=3405,yuansu_hj=1702,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=30,},
{pet_id=31,},
{pet_id=31,},
{pet_id=31,star=2,zs_skill_level=3,need_item_id=27710,extra_grow_rate=22,maxhp=25536,gongji=1021,fangyu=681,pojia=340,yuansu_sh=681,yuansu_hj=340,},
{pet_id=31,},
{pet_id=31,},
{pet_id=31,},
{pet_id=31,},
{pet_id=31,},
{pet_id=31,star=8,active_skill_level=2,zs_skill_level=9,need_num=4,need_item_id=27710,extra_grow_rate=88,maxhp=102144,gongji=4086,fangyu=2724,pojia=1362,yuansu_sh=2724,yuansu_hj=1362,},
{pet_id=31,},
{pet_id=31,},
{pet_id=32,},
{pet_id=32,},
{pet_id=32,},
{pet_id=32,},
{pet_id=32,star=4,zs_skill_level=5,need_item_id=27710,extra_grow_rate=44,maxhp=51072,gongji=2043,fangyu=1362,pojia=681,yuansu_sh=1362,yuansu_hj=681,},
{pet_id=32,},
{pet_id=32,star=6,active_skill_level=2,zs_skill_level=7,need_num=2,need_item_id=27710,extra_grow_rate=66,maxhp=76608,gongji=3064,fangyu=2043,pojia=1021,yuansu_sh=2043,yuansu_hj=1021,},
{pet_id=32,star=7,active_skill_level=2,zs_skill_level=8,need_num=3,need_item_id=27710,extra_grow_rate=77,maxhp=89376,gongji=3575,fangyu=2383,pojia=1192,yuansu_sh=2383,yuansu_hj=1192,},
{pet_id=32,},
{pet_id=32,},
{pet_id=32,},
{pet_id=33,},
{pet_id=33,star=1,zs_skill_level=2,need_item_id=27710,extra_grow_rate=11,maxhp=12768,gongji=511,fangyu=340,pojia=170,yuansu_sh=340,yuansu_hj=170,},
{pet_id=33,},
{pet_id=33,star=3,zs_skill_level=4,need_item_id=27710,extra_grow_rate=33,maxhp=38304,gongji=1532,fangyu=1021,pojia=511,yuansu_sh=1021,yuansu_hj=511,},
{pet_id=33,},
{pet_id=33,},
{pet_id=33,},
{pet_id=33,},
{pet_id=33,},
{pet_id=33,star=9,active_skill_level=2,zs_skill_level=10,need_num=5,need_item_id=27710,extra_grow_rate=99,maxhp=114912,gongji=4596,fangyu=3064,pojia=1532,yuansu_sh=3064,yuansu_hj=1532,},
{pet_id=33,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=34,},
{pet_id=35,},
{pet_id=35,star=1,zs_skill_level="2|2",need_item_id=27711,extra_grow_rate=16,maxhp=20443,gongji=818,fangyu=545,pojia=273,yuansu_sh=545,yuansu_hj=273,},
{pet_id=35,star=2,zs_skill_level="3|3",need_item_id=27711,extra_grow_rate=32,maxhp=40885,gongji=1635,fangyu=1090,pojia=545,yuansu_sh=1090,yuansu_hj=545,},
{pet_id=35,star=3,zs_skill_level="4|4",need_item_id=27711,extra_grow_rate=48,maxhp=61328,gongji=2453,fangyu=1635,pojia=818,yuansu_sh=1635,yuansu_hj=818,},
{pet_id=35,star=4,zs_skill_level="5|5",need_item_id=27711,extra_grow_rate=64,maxhp=81770,gongji=3271,fangyu=2181,pojia=1090,yuansu_sh=2181,yuansu_hj=1090,},
{pet_id=35,},
{pet_id=35,},
{pet_id=35,},
{pet_id=35,},
{pet_id=35,},
{pet_id=35,},
{pet_id=36,},
{pet_id=36,},
{pet_id=36,},
{pet_id=36,},
{pet_id=36,},
{pet_id=36,},
{pet_id=36,star=6,active_skill_level=2,zs_skill_level="7|7",need_num=2,need_item_id=27711,extra_grow_rate=96,maxhp=122655,gongji=4906,fangyu=3271,pojia=1635,yuansu_sh=3271,yuansu_hj=1635,},
{pet_id=36,star=7,active_skill_level=2,zs_skill_level="8|8",need_num=3,need_item_id=27711,extra_grow_rate=112,maxhp=143098,gongji=5724,fangyu=3816,pojia=1908,yuansu_sh=3816,yuansu_hj=1908,},
{pet_id=36,star=8,active_skill_level=2,zs_skill_level="9|9",need_num=4,need_item_id=27711,extra_grow_rate=128,maxhp=163540,gongji=6542,fangyu=4361,pojia=2181,yuansu_sh=4361,yuansu_hj=2181,},
{pet_id=36,star=9,active_skill_level=2,zs_skill_level="10|10",need_num=5,need_item_id=27711,extra_grow_rate=144,maxhp=183983,gongji=7359,fangyu=4906,pojia=2453,yuansu_sh=4906,yuansu_hj=2453,},
{pet_id=36,star=10,active_skill_level=3,zs_skill_level="11|11",need_item_id=27711,extra_grow_rate=160,maxhp=204425,gongji=8177,fangyu=5451,pojia=2726,yuansu_sh=5451,yuansu_hj=2726,},
{pet_id=37,zs_skill_level="1|1",need_item_id=27711,msg_type=-1,},
{pet_id=37,},
{pet_id=37,},
{pet_id=37,},
{pet_id=37,},
{pet_id=37,star=5,active_skill_level=2,zs_skill_level="6|6",need_item_id=27711,extra_grow_rate=80,maxhp=102213,gongji=4089,fangyu=2726,pojia=1363,yuansu_sh=2726,yuansu_hj=1363,},
{pet_id=37,},
{pet_id=37,},
{pet_id=37,},
{pet_id=37,},
{pet_id=37,}
},

pet_upstar_meta_table_map={
[265]=254,	-- depth:1
[276]=265,	-- depth:2
[287]=276,	-- depth:3
[243]=287,	-- depth:4
[166]=243,	-- depth:5
[232]=166,	-- depth:6
[177]=232,	-- depth:7
[210]=177,	-- depth:8
[188]=210,	-- depth:9
[221]=188,	-- depth:10
[199]=221,	-- depth:11
[155]=199,	-- depth:12
[78]=254,	-- depth:1
[45]=1,	-- depth:1
[122]=78,	-- depth:2
[89]=122,	-- depth:3
[309]=254,	-- depth:1
[56]=45,	-- depth:2
[111]=89,	-- depth:4
[320]=309,	-- depth:2
[133]=111,	-- depth:5
[331]=320,	-- depth:3
[100]=133,	-- depth:6
[342]=331,	-- depth:4
[386]=397,	-- depth:1
[353]=342,	-- depth:5
[12]=1,	-- depth:1
[375]=386,	-- depth:2
[67]=100,	-- depth:7
[34]=56,	-- depth:3
[298]=353,	-- depth:6
[144]=67,	-- depth:8
[23]=34,	-- depth:4
[364]=298,	-- depth:7
[3]=7,	-- depth:1
[4]=7,	-- depth:1
[10]=7,	-- depth:1
[9]=7,	-- depth:1
[6]=7,	-- depth:1
[8]=7,	-- depth:1
[11]=7,	-- depth:1
[5]=7,	-- depth:1
[2]=7,	-- depth:1
[278]=157,	-- depth:1
[179]=278,	-- depth:2
[180]=224,	-- depth:1
[158]=180,	-- depth:2
[258]=159,	-- depth:1
[257]=158,	-- depth:3
[181]=258,	-- depth:2
[225]=181,	-- depth:3
[222]=178,	-- depth:1
[256]=179,	-- depth:3
[255]=222,	-- depth:2
[223]=256,	-- depth:4
[246]=257,	-- depth:4
[234]=223,	-- depth:5
[279]=246,	-- depth:5
[280]=225,	-- depth:4
[245]=234,	-- depth:6
[244]=255,	-- depth:3
[288]=244,	-- depth:4
[289]=245,	-- depth:7
[247]=280,	-- depth:5
[290]=279,	-- depth:6
[291]=247,	-- depth:6
[233]=288,	-- depth:5
[269]=291,	-- depth:7
[167]=233,	-- depth:6
[168]=289,	-- depth:8
[169]=290,	-- depth:7
[267]=168,	-- depth:9
[266]=167,	-- depth:7
[170]=269,	-- depth:8
[236]=170,	-- depth:9
[235]=169,	-- depth:8
[268]=235,	-- depth:9
[277]=266,	-- depth:8
[191]=268,	-- depth:10
[190]=267,	-- depth:10
[48]=7,	-- depth:1
[47]=5,	-- depth:2
[46]=3,	-- depth:2
[44]=48,	-- depth:2
[43]=44,	-- depth:3
[42]=44,	-- depth:3
[41]=44,	-- depth:3
[40]=44,	-- depth:3
[39]=11,	-- depth:2
[38]=9,	-- depth:2
[37]=48,	-- depth:2
[36]=47,	-- depth:3
[35]=46,	-- depth:3
[33]=44,	-- depth:3
[49]=38,	-- depth:3
[32]=43,	-- depth:4
[30]=41,	-- depth:4
[29]=40,	-- depth:4
[28]=39,	-- depth:3
[27]=49,	-- depth:4
[26]=37,	-- depth:3
[25]=36,	-- depth:4
[24]=35,	-- depth:4
[22]=11,	-- depth:2
[21]=10,	-- depth:2
[20]=9,	-- depth:2
[19]=8,	-- depth:2
[18]=7,	-- depth:1
[17]=6,	-- depth:2
[16]=5,	-- depth:2
[31]=42,	-- depth:4
[189]=277,	-- depth:9
[50]=28,	-- depth:4
[52]=30,	-- depth:5
[14]=3,	-- depth:2
[192]=236,	-- depth:10
[214]=192,	-- depth:11
[156]=189,	-- depth:10
[213]=191,	-- depth:11
[212]=190,	-- depth:11
[13]=2,	-- depth:2
[200]=156,	-- depth:11
[201]=212,	-- depth:12
[202]=213,	-- depth:12
[203]=214,	-- depth:12
[51]=29,	-- depth:5
[66]=33,	-- depth:4
[211]=200,	-- depth:12
[64]=31,	-- depth:5
[53]=64,	-- depth:6
[54]=32,	-- depth:5
[55]=66,	-- depth:5
[57]=24,	-- depth:5
[65]=54,	-- depth:6
[15]=4,	-- depth:2
[59]=26,	-- depth:4
[60]=27,	-- depth:5
[61]=50,	-- depth:5
[62]=51,	-- depth:6
[63]=52,	-- depth:6
[58]=25,	-- depth:5
[215]=237,	-- depth:1
[220]=231,	-- depth:1
[242]=220,	-- depth:2
[226]=215,	-- depth:2
[253]=242,	-- depth:3
[355]=333,	-- depth:1
[357]=346,	-- depth:1
[365]=354,	-- depth:1
[366]=355,	-- depth:2
[367]=356,	-- depth:1
[368]=357,	-- depth:2
[387]=376,	-- depth:1
[388]=377,	-- depth:1
[389]=378,	-- depth:1
[390]=379,	-- depth:1
[398]=387,	-- depth:2
[399]=388,	-- depth:2
[400]=389,	-- depth:2
[401]=390,	-- depth:2
[345]=367,	-- depth:2
[344]=366,	-- depth:3
[343]=365,	-- depth:2
[335]=368,	-- depth:3
[259]=226,	-- depth:3
[264]=253,	-- depth:4
[270]=259,	-- depth:4
[275]=264,	-- depth:5
[281]=270,	-- depth:5
[286]=275,	-- depth:6
[292]=281,	-- depth:6
[297]=286,	-- depth:7
[299]=343,	-- depth:3
[300]=344,	-- depth:4
[248]=292,	-- depth:7
[301]=345,	-- depth:3
[310]=299,	-- depth:4
[311]=300,	-- depth:5
[312]=301,	-- depth:4
[313]=335,	-- depth:4
[321]=310,	-- depth:5
[322]=311,	-- depth:6
[323]=312,	-- depth:5
[324]=313,	-- depth:5
[332]=321,	-- depth:6
[334]=323,	-- depth:6
[302]=324,	-- depth:6
[209]=297,	-- depth:8
[204]=248,	-- depth:8
[102]=113,	-- depth:1
[165]=209,	-- depth:9
[110]=88,	-- depth:1
[101]=112,	-- depth:1
[90]=101,	-- depth:2
[91]=102,	-- depth:2
[160]=204,	-- depth:9
[154]=110,	-- depth:2
[94]=105,	-- depth:1
[143]=154,	-- depth:3
[145]=90,	-- depth:3
[132]=143,	-- depth:4
[134]=145,	-- depth:4
[135]=91,	-- depth:3
[149]=94,	-- depth:2
[148]=93,	-- depth:1
[147]=92,	-- depth:1
[136]=147,	-- depth:2
[99]=132,	-- depth:5
[137]=148,	-- depth:2
[171]=160,	-- depth:10
[138]=149,	-- depth:3
[146]=135,	-- depth:4
[126]=137,	-- depth:3
[104]=126,	-- depth:4
[68]=134,	-- depth:5
[69]=146,	-- depth:5
[198]=165,	-- depth:10
[70]=136,	-- depth:3
[71]=104,	-- depth:5
[72]=138,	-- depth:4
[121]=99,	-- depth:6
[123]=68,	-- depth:6
[103]=70,	-- depth:4
[116]=72,	-- depth:5
[115]=71,	-- depth:6
[193]=171,	-- depth:11
[187]=198,	-- depth:11
[114]=103,	-- depth:5
[77]=121,	-- depth:7
[124]=69,	-- depth:6
[125]=114,	-- depth:6
[79]=123,	-- depth:7
[80]=124,	-- depth:7
[81]=125,	-- depth:7
[82]=115,	-- depth:7
[182]=193,	-- depth:12
[176]=187,	-- depth:12
[83]=116,	-- depth:6
[127]=83,	-- depth:7
[308]=319,	-- depth:1
[95]=106,	-- depth:1
[330]=308,	-- depth:2
[391]=402,	-- depth:1
[73]=95,	-- depth:2
[74]=107,	-- depth:1
[75]=108,	-- depth:1
[76]=109,	-- depth:1
[385]=396,	-- depth:1
[380]=391,	-- depth:2
[374]=330,	-- depth:3
[369]=314,	-- depth:1
[84]=73,	-- depth:3
[85]=74,	-- depth:2
[86]=75,	-- depth:2
[87]=76,	-- depth:2
[363]=374,	-- depth:4
[358]=369,	-- depth:2
[352]=363,	-- depth:5
[347]=358,	-- depth:3
[303]=347,	-- depth:4
[96]=85,	-- depth:3
[97]=86,	-- depth:3
[98]=87,	-- depth:3
[341]=352,	-- depth:6
[336]=303,	-- depth:5
[325]=336,	-- depth:6
[117]=84,	-- depth:4
[407]=385,	-- depth:2
[119]=97,	-- depth:4
[238]=172,	-- depth:1
[240]=207,	-- depth:1
[241]=208,	-- depth:1
[164]=241,	-- depth:2
[163]=240,	-- depth:2
[173]=239,	-- depth:1
[162]=173,	-- depth:2
[118]=96,	-- depth:4
[250]=162,	-- depth:3
[251]=163,	-- depth:3
[252]=164,	-- depth:3
[153]=98,	-- depth:4
[152]=119,	-- depth:5
[151]=118,	-- depth:5
[161]=238,	-- depth:2
[174]=251,	-- depth:4
[175]=252,	-- depth:4
[230]=175,	-- depth:5
[206]=250,	-- depth:4
[205]=161,	-- depth:3
[197]=230,	-- depth:6
[196]=174,	-- depth:5
[195]=206,	-- depth:5
[216]=205,	-- depth:4
[217]=195,	-- depth:6
[218]=196,	-- depth:6
[219]=197,	-- depth:7
[194]=216,	-- depth:5
[186]=219,	-- depth:8
[185]=218,	-- depth:7
[184]=217,	-- depth:7
[183]=194,	-- depth:6
[227]=183,	-- depth:7
[228]=184,	-- depth:8
[229]=185,	-- depth:8
[150]=117,	-- depth:5
[260]=227,	-- depth:8
[249]=260,	-- depth:9
[262]=229,	-- depth:9
[274]=186,	-- depth:9
[131]=153,	-- depth:5
[261]=228,	-- depth:9
[283]=261,	-- depth:10
[284]=262,	-- depth:10
[285]=274,	-- depth:10
[273]=284,	-- depth:11
[130]=152,	-- depth:6
[128]=150,	-- depth:6
[293]=249,	-- depth:10
[294]=283,	-- depth:11
[295]=273,	-- depth:12
[296]=285,	-- depth:11
[120]=131,	-- depth:6
[129]=151,	-- depth:6
[272]=294,	-- depth:12
[282]=293,	-- depth:11
[139]=128,	-- depth:7
[271]=282,	-- depth:12
[263]=296,	-- depth:12
[142]=120,	-- depth:7
[141]=130,	-- depth:7
[140]=129,	-- depth:7
[338]=349,	-- depth:1
[340]=362,	-- depth:1
[306]=339,	-- depth:1
[406]=395,	-- depth:1
[305]=338,	-- depth:2
[304]=348,	-- depth:1
[403]=392,	-- depth:1
[404]=393,	-- depth:1
[405]=394,	-- depth:1
[350]=306,	-- depth:2
[351]=340,	-- depth:2
[384]=406,	-- depth:2
[315]=304,	-- depth:2
[360]=305,	-- depth:3
[329]=351,	-- depth:3
[328]=350,	-- depth:3
[327]=360,	-- depth:4
[326]=315,	-- depth:3
[359]=326,	-- depth:4
[370]=359,	-- depth:5
[371]=327,	-- depth:5
[372]=328,	-- depth:4
[373]=329,	-- depth:4
[318]=373,	-- depth:5
[317]=372,	-- depth:5
[381]=403,	-- depth:2
[382]=404,	-- depth:2
[383]=405,	-- depth:2
[361]=317,	-- depth:6
[316]=371,	-- depth:6
[337]=370,	-- depth:6
[307]=318,	-- depth:6
},
passive_skill={
{skill_name="同心·初",consume_item_num=0,param1="shengming_max",},
{level=1,param2=7500,skill_des="宠物生命提高<color=#35ab6c>7500</color>点",},
{level=2,consume_item_num=1,param2=10000,skill_des="宠物生命提高<color=#35ab6c>10000</color>点",},
{level=3,param2=12500,skill_des="宠物生命提高<color=#35ab6c>12500</color>点",},
{level=4,consume_item_num=2,param2=15000,skill_des="宠物生命提高<color=#35ab6c>15000</color>点",},
{level=5,consume_item_num=3,param2=17500,skill_des="宠物生命提高<color=#35ab6c>17500</color>点",},
{level=6,param2=20000,skill_des="宠物生命提高<color=#35ab6c>20000</color>点",},
{level=7,consume_item_num=4,param2=22500,skill_des="宠物生命提高<color=#35ab6c>22500</color>点",},
{level=8,param2=25000,skill_des="宠物生命提高<color=#35ab6c>25000</color>点",},
{level=9,consume_item_num=5,param2=27500,skill_des="宠物生命提高<color=#35ab6c>27500</color>点",},
{level=10,consume_item_num=99,param2=30000,skill_des="宠物生命提高<color=#35ab6c>30000</color>点",},
{skill_id=2,},
{level=1,param2=300,skill_des="宠物攻击提高<color=#35ab6c>300</color>点",},
{level=2,consume_item_num=1,param2=400,skill_des="宠物攻击提高<color=#35ab6c>400</color>点",},
{level=3,consume_item_num=2,param2=500,skill_des="宠物攻击提高<color=#35ab6c>500</color>点",},
{skill_id=2,skill_name="协力·初",level=4,},
{level=5,param2=700,skill_des="宠物攻击提高<color=#35ab6c>700</color>点",},
{level=6,consume_item_num=3,param2=800,skill_des="宠物攻击提高<color=#35ab6c>800</color>点",},
{level=7,consume_item_num=4,param2=900,skill_des="宠物攻击提高<color=#35ab6c>900</color>点",},
{level=8,param2=1000,skill_des="宠物攻击提高<color=#35ab6c>1000</color>点",},
{skill_id=2,},
{skill_id=2,},
{skill_id=3,skill_name="协力·初",},
{skill_id=3,},
{skill_id=3,},
{skill_id=3,},
{skill_id=3,},
{skill_id=3,},
{skill_id=3,},
{skill_id=3,},
{skill_id=3,},
{level=9,consume_item_num=5,param2=1100,skill_des="宠物攻击提高<color=#35ab6c>1100</color>点",},
{skill_id=3,skill_name="协力·初",param2=1200,skill_des="宠物攻击提高<color=#35ab6c>1200</color>点",},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=4,},
{skill_id=5,skill_name="破躯·初",},
{level=1,consume_item_num=1,param2=300,skill_des="宠物破甲提高<color=#35ab6c>300</color>点",},
{level=2,param2=400,skill_des="宠物破甲提高<color=#35ab6c>400</color>点",},
{level=3,consume_item_num=2,param2=500,skill_des="宠物破甲提高<color=#35ab6c>500</color>点",},
{skill_id=5,skill_name="破躯·初",level=4,},
{level=5,consume_item_num=3,param2=700,skill_des="宠物破甲提高<color=#35ab6c>700</color>点",},
{level=6,param2=800,skill_des="宠物破甲提高<color=#35ab6c>800</color>点",},
{skill_id=5,skill_name="破躯·初",level=7,consume_item_num=4,},
{skill_id=5,skill_name="破躯·初",level=8,},
{level=9,consume_item_num=5,param2=1100,skill_des="宠物破甲提高<color=#35ab6c>1100</color>点",},
{skill_id=5,skill_name="破躯·初",param2=1200,skill_des="宠物破甲提高<color=#35ab6c>1200</color>点",},
{skill_id=6,skill_name="守护·初",},
{level=1,consume_item_num=1,param2=300,skill_des="宠物防御提高<color=#35ab6c>300</color>点",},
{level=2,param2=400,skill_des="宠物防御提高<color=#35ab6c>400</color>点",},
{level=3,param2=500,skill_des="宠物防御提高<color=#35ab6c>500</color>点",},
{skill_id=6,skill_name="守护·初",level=4,},
{level=5,param2=700,skill_des="宠物防御提高<color=#35ab6c>700</color>点",},
{skill_id=6,skill_name="守护·初",level=6,consume_item_num=3,},
{level=7,consume_item_num=4,param2=900,skill_des="宠物防御提高<color=#35ab6c>900</color>点",},
{level=8,param2=1000,skill_des="宠物防御提高<color=#35ab6c>1000</color>点",},
{level=9,consume_item_num=5,param2=1100,skill_des="宠物防御提高<color=#35ab6c>1100</color>点",},
{level=10,consume_item_num=99,param2=1200,skill_des="宠物防御提高<color=#35ab6c>1200</color>点",},
{skill_id=7,skill_name="协力·良",consume_item_num=2,skill_type=0,},
{level=1,consume_item_num=2,param2=600,skill_des="宠物攻击提高<color=#35ab6c>600</color>点",},
{level=2,param2=800,skill_des="宠物攻击提高<color=#35ab6c>800</color>点",},
{skill_id=7,skill_name="协力·良",level=3,},
{level=4,param2=1200,skill_des="宠物攻击提高<color=#35ab6c>1200</color>点",},
{level=5,param2=1400,skill_des="宠物攻击提高<color=#35ab6c>1400</color>点",},
{skill_id=7,skill_name="协力·良",level=6,param2=1600,skill_icon=981,skill_des="宠物攻击提高<color=#35ab6c>1600</color>点",},
{level=7,consume_item_num=8,param2=1800,skill_des="宠物攻击提高<color=#35ab6c>1800</color>点",},
{level=8,param2=2000,skill_des="宠物攻击提高<color=#35ab6c>2000</color>点",},
{level=9,consume_item_num=10,param2=2200,skill_des="宠物攻击提高<color=#35ab6c>2200</color>点",},
{level=10,consume_item_num=99,param2=2400,skill_des="宠物攻击提高<color=#35ab6c>2400</color>点",},
{skill_id=8,skill_name="玄法精进·良",consume_item_num=0,skill_type=2,param1="yuansu_sh",skill_icon=984,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>0</color>点",},
{level=1,param2=6,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>6</color>点",},
{level=2,consume_item_num=2,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>8</color>点",},
{level=3,consume_item_num=4,param2=10,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>10</color>点",},
{level=4,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>12</color>点",},
{skill_id=8,skill_name="玄法精进·良",level=5,skill_type=2,param1="yuansu_sh",param2=14,skill_icon=984,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>14</color>点",},
{level=6,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>16</color>点",},
{level=7,consume_item_num=8,param2=18,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>18</color>点",},
{level=8,param2=20,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>20</color>点",},
{level=9,consume_item_num=10,param2=22,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>22</color>点",},
{level=10,consume_item_num=99,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>24</color>点",},
{skill_id=9,skill_name="守护·良",},
{level=1,param2=600,skill_des="宠物防御提高<color=#35ab6c>600</color>点",},
{level=2,consume_item_num=2,param2=800,skill_des="宠物防御提高<color=#35ab6c>800</color>点",},
{level=3,param2=1000,skill_des="宠物防御提高<color=#35ab6c>1000</color>点",},
{level=4,consume_item_num=4,param2=1200,skill_des="宠物防御提高<color=#35ab6c>1200</color>点",},
{skill_id=9,skill_name="守护·良",level=5,param1="fangyu",param2=1400,skill_icon=983,skill_des="宠物防御提高<color=#35ab6c>1400</color>点",},
{level=6,param2=1600,skill_des="宠物防御提高<color=#35ab6c>1600</color>点",},
{level=7,param2=1800,skill_des="宠物防御提高<color=#35ab6c>1800</color>点",},
{level=8,consume_item_num=8,param2=2000,skill_des="宠物防御提高<color=#35ab6c>2000</color>点",},
{level=9,consume_item_num=10,param2=2200,skill_des="宠物防御提高<color=#35ab6c>2200</color>点",},
{level=10,consume_item_num=99,param2=2400,skill_des="宠物防御提高<color=#35ab6c>2400</color>点",},
{skill_id=10,skill_name="共生·良",},
{level=1,consume_item_num=2,param2=170,skill_des="宠物生命提高<color=#35ab6c>1.7%</color>",},
{level=2,param2=220,skill_des="宠物生命提高<color=#35ab6c>2.2%</color>",},
{level=3,param2=280,skill_des="宠物生命提高<color=#35ab6c>2.8%</color>",},
{level=4,consume_item_num=4,param2=330,skill_des="宠物生命提高<color=#35ab6c>3.3%</color>",},
{level=5,param2=390,skill_des="宠物生命提高<color=#35ab6c>3.9%</color>",},
{skill_id=10,skill_name="共生·良",level=6,skill_type=31,param1="shengming_max",param2=440,skill_icon=985,skill_des="宠物生命提高<color=#35ab6c>4.4%</color>",},
{level=7,consume_item_num=8,param2=500,skill_des="宠物生命提高<color=#35ab6c>5%</color>",},
{level=8,param2=560,skill_des="宠物生命提高<color=#35ab6c>5.6%</color>",},
{level=9,consume_item_num=10,param2=610,skill_des="宠物生命提高<color=#35ab6c>6.1%</color>",},
{level=10,consume_item_num=99,param2=670,skill_des="宠物生命提高<color=#35ab6c>6.7%</color>",},
{skill_id=11,skill_name="玄法·良",consume_item_num=0,param1="yuansu_sh",skill_icon=986,skill_des="宠物道法伤害提高<color=#35ab6c>0</color>点",},
{skill_id=11,},
{skill_id=11,},
{skill_id=11,},
{skill_id=11,},
{skill_id=11,},
{skill_id=11,},
{skill_id=11,},
{skill_id=11,},
{skill_id=11,},
{skill_id=11,},
{skill_id=12,skill_name="协力精进·良",consume_item_num=0,skill_type=2,skill_icon=987,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>0</color>点",},
{level=1,param2=6,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>6</color>点",},
{level=2,consume_item_num=2,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>8</color>点",},
{level=3,param2=10,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>10</color>点",},
{level=4,consume_item_num=4,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>12</color>点",},
{skill_id=12,skill_name="协力精进·良",level=5,skill_type=2,param2=14,skill_icon=987,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>14</color>点",},
{level=6,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>16</color>点",},
{level=7,param2=18,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>18</color>点",},
{level=8,consume_item_num=8,param2=20,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>20</color>点",},
{level=9,consume_item_num=10,param2=22,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>22</color>点",},
{level=10,consume_item_num=99,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>24</color>点",},
{skill_id=13,skill_name="破躯·良",},
{level=1,consume_item_num=2,param2=600,skill_des="宠物破甲提高<color=#35ab6c>600</color>点",},
{level=2,param2=800,skill_des="宠物破甲提高<color=#35ab6c>800</color>点",},
{level=3,consume_item_num=4,param2=1000,skill_des="宠物破甲提高<color=#35ab6c>1000</color>点",},
{level=4,param2=1200,skill_des="宠物破甲提高<color=#35ab6c>1200</color>点",},
{level=5,param2=1400,skill_des="宠物破甲提高<color=#35ab6c>1400</color>点",},
{skill_id=13,skill_name="破躯·良",level=6,param1="pojia",param2=1600,skill_icon=982,skill_des="宠物破甲提高<color=#35ab6c>1600</color>点",},
{level=7,consume_item_num=8,param2=1800,skill_des="宠物破甲提高<color=#35ab6c>1800</color>点",},
{level=8,param2=2000,skill_des="宠物破甲提高<color=#35ab6c>2000</color>点",},
{level=9,consume_item_num=10,param2=2200,skill_des="宠物破甲提高<color=#35ab6c>2200</color>点",},
{level=10,consume_item_num=99,param2=2400,skill_des="宠物破甲提高<color=#35ab6c>2400</color>点",},
{skill_id=14,skill_name="合纵·良",},
{level=1,consume_item_num=2,param2=500,skill_des="宠物攻击提高<color=#35ab6c>5%</color>",},
{level=2,param2=670,skill_des="宠物攻击提高<color=#35ab6c>6.7%</color>",},
{level=3,consume_item_num=4,param2=830,skill_des="宠物攻击提高<color=#35ab6c>8.3%</color>",},
{level=4,param2=1000,skill_des="宠物攻击提高<color=#35ab6c>10%</color>",},
{skill_id=14,skill_name="合纵·良",level=5,skill_type=31,param2=1170,skill_icon=988,skill_des="宠物攻击提高<color=#35ab6c>11.7%</color>",},
{level=6,param2=1330,skill_des="宠物攻击提高<color=#35ab6c>13.3%</color>",},
{level=7,consume_item_num=8,param2=1500,skill_des="宠物攻击提高<color=#35ab6c>15%</color>",},
{level=8,param2=1670,skill_des="宠物攻击提高<color=#35ab6c>16.7%</color>",},
{level=9,consume_item_num=10,param2=1830,skill_des="宠物攻击提高<color=#35ab6c>18.3%</color>",},
{level=10,consume_item_num=99,param2=2000,skill_des="宠物攻击提高<color=#35ab6c>20%</color>",},
{skill_id=15,},
{level=1,consume_item_num=2,param2=600,skill_des="宠物道法护甲提高<color=#35ab6c>600</color>点",},
{level=2,param2=800,skill_des="宠物道法护甲提高<color=#35ab6c>800</color>点",},
{level=3,param2=1000,skill_des="宠物道法护甲提高<color=#35ab6c>1000</color>点",},
{skill_id=15,skill_name="玄防·良",level=4,},
{skill_id=15,},
{skill_id=15,},
{level=7,consume_item_num=8,param2=1800,skill_des="宠物道法护甲提高<color=#35ab6c>1800</color>点",},
{level=8,param2=2000,skill_des="宠物道法护甲提高<color=#35ab6c>2000</color>点",},
{level=9,consume_item_num=10,param2=2200,skill_des="宠物道法护甲提高<color=#35ab6c>2200</color>点",},
{skill_id=15,skill_name="玄防·良",level=10,consume_item_num=99,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=16,},
{skill_id=17,skill_name="玄防·良",},
{skill_id=17,},
{skill_id=17,},
{skill_id=17,},
{skill_id=17,},
{level=5,param2=1400,skill_des="宠物道法护甲提高<color=#35ab6c>1400</color>点",},
{skill_id=17,skill_name="玄防·良",level=6,param1="yuansu_hj",param2=1600,skill_icon=989,skill_des="宠物道法护甲提高<color=#35ab6c>1600</color>点",},
{skill_id=17,},
{skill_id=17,},
{skill_id=17,},
{skill_id=17,},
{skill_id=18,skill_name="同心精进·良",consume_item_num=0,skill_type=2,param1="shengming_max",skill_icon=990,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>0</color>点",},
{level=1,param2=150,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>150</color>点",},
{level=2,consume_item_num=2,param2=200,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>200</color>点",},
{level=3,consume_item_num=4,param2=250,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>250</color>点",},
{level=4,param2=300,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>300</color>点",},
{level=5,param2=350,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>350</color>点",},
{skill_id=18,skill_name="同心精进·良",level=6,skill_type=2,param1="shengming_max",param2=400,skill_icon=990,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>400</color>点",},
{level=7,param2=450,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>450</color>点",},
{level=8,consume_item_num=8,param2=500,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>500</color>点",},
{level=9,consume_item_num=10,param2=550,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>550</color>点",},
{level=10,consume_item_num=99,param2=600,skill_des="宠物每升<color=#35ab6c>1</color>级，生命提升<color=#35ab6c>600</color>点",},
{skill_id=19,skill_name="同心·良",},
{skill_id=19,skill_name="同心·良",level=1,},
{level=2,param2=20000,skill_des="宠物生命提高<color=#35ab6c>20000</color>点",},
{skill_id=19,skill_name="同心·良",level=3,},
{skill_id=19,skill_name="同心·良",level=4,},
{skill_id=19,skill_name="同心·良",level=5,param1="shengming_max",param2=35000,skill_des="宠物生命提高<color=#35ab6c>35000</color>点",},
{level=6,param2=40000,skill_des="宠物生命提高<color=#35ab6c>40000</color>点",},
{level=7,consume_item_num=8,param2=45000,skill_des="宠物生命提高<color=#35ab6c>45000</color>点",},
{level=8,param2=50000,skill_des="宠物生命提高<color=#35ab6c>50000</color>点",},
{level=9,consume_item_num=10,param2=55000,skill_des="宠物生命提高<color=#35ab6c>55000</color>点",},
{level=10,consume_item_num=99,param2=60000,skill_des="宠物生命提高<color=#35ab6c>60000</color>点",},
{skill_id=20,skill_name="守护精进·良",},
{level=1,param2=6,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>6</color>点",},
{level=2,consume_item_num=2,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>8</color>点",},
{level=3,param2=10,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>10</color>点",},
{level=4,consume_item_num=4,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>12</color>点",},
{skill_id=20,skill_name="守护精进·良",level=5,skill_type=2,param1="fangyu",param2=14,skill_icon=991,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>14</color>点",},
{level=6,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>16</color>点",},
{level=7,consume_item_num=8,param2=18,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>18</color>点",},
{level=8,param2=20,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>20</color>点",},
{level=9,consume_item_num=10,param2=22,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>22</color>点",},
{level=10,consume_item_num=99,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>24</color>点",},
{skill_id=21,},
{level=1,consume_item_num=2,param2=600,skill_des="宠物道法伤害提高<color=#35ab6c>600</color>点",},
{level=2,param2=800,skill_des="宠物道法伤害提高<color=#35ab6c>800</color>点",},
{level=3,consume_item_num=4,param2=1000,skill_des="宠物道法伤害提高<color=#35ab6c>1000</color>点",},
{level=4,param2=1200,skill_des="宠物道法伤害提高<color=#35ab6c>1200</color>点",},
{skill_id=21,skill_name="玄法·良",level=5,param1="yuansu_sh",param2=1400,skill_icon=986,skill_des="宠物道法伤害提高<color=#35ab6c>1400</color>点",},
{level=6,param2=1600,skill_des="宠物道法伤害提高<color=#35ab6c>1600</color>点",},
{skill_id=21,skill_name="玄法·良",level=7,consume_item_num=8,},
{level=8,param2=2000,skill_des="宠物道法伤害提高<color=#35ab6c>2000</color>点",},
{level=9,consume_item_num=10,param2=2200,skill_des="宠物道法伤害提高<color=#35ab6c>2200</color>点",},
{level=10,consume_item_num=99,param2=2400,skill_des="宠物道法伤害提高<color=#35ab6c>2400</color>点",},
{skill_id=22,skill_name="破躯精进·良",consume_item_num=0,skill_type=2,param1="pojia",skill_icon=918,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>0</color>点",},
{level=1,consume_item_num=2,param2=6,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>6</color>点",},
{level=2,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>8</color>点",},
{level=3,param2=10,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>10</color>点",},
{level=4,consume_item_num=4,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>12</color>点",},
{skill_id=22,skill_name="破躯精进·良",level=5,skill_type=2,param1="pojia",param2=14,skill_icon=918,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>14</color>点",},
{level=6,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>16</color>点",},
{level=7,consume_item_num=8,param2=18,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>18</color>点",},
{level=8,param2=20,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>20</color>点",},
{level=9,consume_item_num=10,param2=22,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>22</color>点",},
{level=10,consume_item_num=99,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，破甲提升<color=#35ab6c>24</color>点",},
{skill_id=23,consume_item_num=0,param1="fangyu",skill_icon=983,skill_des="宠物防御提高<color=#35ab6c>0</color>点",},
{skill_id=23,},
{skill_id=23,},
{skill_id=23,},
{level=4,param2=1800,skill_des="宠物防御提高<color=#35ab6c>1800</color>点",},
{skill_id=23,},
{skill_id=23,},
{skill_id=23,},
{skill_id=23,},
{skill_id=23,},
{skill_id=23,},
{skill_id=24,skill_name="掠夺精进·绝",consume_item_num=0,skill_type=2,param1="shengming_qq",skill_icon=993,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>0</color>点",},
{level=1,param2=3,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>3</color>点",},
{level=2,consume_item_num=3,param2=4,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>4</color>点",},
{level=3,param2=5,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>5</color>点",},
{skill_id=24,skill_name="掠夺精进·绝",level=4,skill_type=2,param1="shengming_qq",param2=6,skill_icon=993,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>6</color>点",},
{level=5,param2=7,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>7</color>点",},
{level=6,consume_item_num=9,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>8</color>点",},
{level=7,consume_item_num=12,param2=9,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>9</color>点",},
{level=8,param2=10,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>10</color>点",},
{level=9,consume_item_num=15,param2=11,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>11</color>点",},
{level=10,consume_item_num=99,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>12</color>点",},
{skill_id=25,skill_name="抗击·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="jianshang_per",skill_icon=994,skill_des="主人玩家减伤提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=2,param2=100,skill_des="主人玩家减伤提高<color=#35ab6c>1%</color>",},
{level=2,param2=130,skill_des="主人玩家减伤提高<color=#35ab6c>1.3%</color>",},
{level=3,consume_item_num=4,param2=170,skill_des="主人玩家减伤提高<color=#35ab6c>1.7%</color>",},
{level=4,param2=200,skill_des="主人玩家减伤提高<color=#35ab6c>2%</color>",},
{skill_id=25,skill_name="抗击·绝",param1="jianshang_per",skill_icon=994,skill_des="主人玩家减伤提高<color=#35ab6c>2.3%</color>",},
{level=6,param2=270,skill_des="主人玩家减伤提高<color=#35ab6c>2.7%</color>",},
{level=7,param2=300,skill_des="主人玩家减伤提高<color=#35ab6c>3%</color>",},
{level=8,consume_item_num=8,param2=330,skill_des="主人玩家减伤提高<color=#35ab6c>3.3%</color>",},
{level=9,consume_item_num=10,param2=370,skill_des="主人玩家减伤提高<color=#35ab6c>3.7%</color>",},
{level=10,consume_item_num=99,param2=400,skill_des="主人玩家减伤提高<color=#35ab6c>4%</color>",},
{skill_id=26,skill_name="同心·绝",},
{level=1,consume_item_num=3,param2=22500,skill_des="宠物生命提高<color=#35ab6c>22500</color>点",},
{level=2,param2=30000,skill_des="宠物生命提高<color=#35ab6c>30000</color>点",},
{skill_id=26,skill_name="同心·绝",level=3,param1="shengming_max",param2=37500,skill_des="宠物生命提高<color=#35ab6c>37500</color>点",},
{level=4,param2=45000,skill_des="宠物生命提高<color=#35ab6c>45000</color>点",},
{skill_id=26,},
{level=6,param2=60000,skill_des="宠物生命提高<color=#35ab6c>60000</color>点",},
{level=7,param2=67500,skill_des="宠物生命提高<color=#35ab6c>67500</color>点",},
{skill_id=26,},
{level=9,consume_item_num=15,param2=82500,skill_des="宠物生命提高<color=#35ab6c>82500</color>点",},
{skill_id=26,},
{skill_id=27,skill_name="铁躯·绝",consume_item_num=0,skill_type=31,param1="fangyu",skill_icon=995,skill_des="宠物防御提高<color=#35ab6c>0%</color>",},
{level=1,param2=1130,skill_des="宠物防御提高<color=#35ab6c>11.3%</color>",},
{level=2,consume_item_num=3,param2=1500,skill_des="宠物防御提高<color=#35ab6c>15%</color>",},
{level=3,param2=1880,skill_des="宠物防御提高<color=#35ab6c>18.8%</color>",},
{skill_id=27,skill_name="铁躯·绝",level=4,skill_type=31,param1="fangyu",param2=2250,skill_icon=995,skill_des="宠物防御提高<color=#35ab6c>22.5%</color>",},
{level=5,consume_item_num=9,param2=2630,skill_des="宠物防御提高<color=#35ab6c>26.3%</color>",},
{level=6,param2=3000,skill_des="宠物防御提高<color=#35ab6c>30%</color>",},
{level=7,param2=3380,skill_des="宠物防御提高<color=#35ab6c>33.8%</color>",},
{level=8,consume_item_num=12,param2=3750,skill_des="宠物防御提高<color=#35ab6c>37.5%</color>",},
{level=9,consume_item_num=15,param2=4130,skill_des="宠物防御提高<color=#35ab6c>41.3%</color>",},
{level=10,consume_item_num=99,param2=4500,skill_des="宠物防御提高<color=#35ab6c>45%</color>",},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=28,},
{skill_id=29,skill_name="协力·绝",consume_item_num=0,skill_icon=981,skill_des="宠物攻击提高<color=#35ab6c>0</color>点",},
{skill_id=29,},
{skill_id=29,},
{skill_id=29,},
{skill_id=29,skill_name="协力·绝",level=4,param2=1800,skill_icon=981,skill_des="宠物攻击提高<color=#35ab6c>1800</color>点",},
{skill_id=29,},
{skill_id=29,},
{skill_id=29,},
{skill_id=29,},
{skill_id=29,},
{skill_id=29,},
{skill_id=30,skill_name="反伤精进·绝",consume_item_num=0,skill_type=2,param1="fangtan",skill_icon=902,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>0</color>点",},
{level=1,param2=3,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>3</color>点",},
{level=2,consume_item_num=3,param2=4,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>4</color>点",},
{level=3,param2=5,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>5</color>点",},
{skill_id=30,skill_name="反伤精进·绝",level=4,skill_type=2,param1="fangtan",param2=6,skill_icon=902,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>6</color>点",},
{level=5,consume_item_num=9,param2=7,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>7</color>点",},
{level=6,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>8</color>点",},
{level=7,consume_item_num=12,param2=9,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>9</color>点",},
{level=8,param2=10,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>10</color>点",},
{level=9,consume_item_num=15,param2=11,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>11</color>点",},
{level=10,consume_item_num=99,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>12</color>点",},
{skill_id=31,consume_item_num=2,skill_type=0,skill_icon=997,},
{skill_id=31,skill_icon=997,},
{skill_id=31,skill_icon=997,},
{skill_id=31,skill_icon=997,},
{skill_id=31,skill_icon=997,},
{skill_id=31,skill_name="穿杨·绝",level=5,param1="mingzhong_per",param2=120,skill_icon=997,skill_des="主人命中率提高<color=#35ab6c>1.2%</color>",},
{level=6,param2=130,skill_des="主人命中率提高<color=#35ab6c>1.3%</color>",},
{skill_id=31,skill_icon=997,},
{skill_id=31,skill_icon=997,},
{skill_id=31,skill_icon=997,},
{skill_id=31,skill_icon=997,},
{skill_id=32,},
{skill_id=32,},
{skill_id=32,},
{skill_id=32,level=3,param2=1500,skill_des="宠物攻击提高<color=#35ab6c>1500</color>点",},
{skill_id=32,},
{skill_id=32,},
{skill_id=32,},
{skill_id=32,},
{skill_id=32,},
{skill_id=32,},
{skill_id=32,},
{skill_id=33,skill_name="均衡·绝",},
{skill_id=33,},
{skill_id=33,},
{skill_id=33,skill_name="均衡·绝",level=3,skill_type=32,param1=210,skill_icon=500,skill_des="宠物全属性提高<color=#35ab6c>2.1%</color>",},
{level=4,param1=250,skill_des="宠物全属性提高<color=#35ab6c>2.5%</color>",},
{skill_id=33,},
{skill_id=33,},
{skill_id=33,},
{skill_id=33,},
{skill_id=33,},
{skill_id=33,},
{skill_id=34,skill_name="克钢·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="kangbao_per",skill_icon=996,skill_des="主人抗暴率提高<color=#35ab6c>0%</color>",},
{level=1,param2=100,skill_des="主人抗暴率提高<color=#35ab6c>1%</color>",},
{level=2,consume_item_num=2,param2=130,skill_des="主人抗暴率提高<color=#35ab6c>1.3%</color>",},
{level=3,consume_item_num=4,param2=170,skill_des="主人抗暴率提高<color=#35ab6c>1.7%</color>",},
{level=4,param2=200,skill_des="主人抗暴率提高<color=#35ab6c>2%</color>",},
{skill_id=34,skill_name="克钢·绝",param1="kangbao_per",skill_icon=996,skill_des="主人抗暴率提高<color=#35ab6c>2.3%</color>",},
{level=6,param2=270,skill_des="主人抗暴率提高<color=#35ab6c>2.7%</color>",},
{level=7,consume_item_num=8,param2=300,skill_des="主人抗暴率提高<color=#35ab6c>3%</color>",},
{level=8,param2=330,skill_des="主人抗暴率提高<color=#35ab6c>3.3%</color>",},
{level=9,consume_item_num=10,param2=370,skill_des="主人抗暴率提高<color=#35ab6c>3.7%</color>",},
{level=10,consume_item_num=99,param2=400,skill_des="主人抗暴率提高<color=#35ab6c>4%</color>",},
{skill_id=35,skill_name="玄防·绝",consume_item_num=0,param1="yuansu_hj",skill_icon=989,skill_des="宠物道法护甲提高<color=#35ab6c>0</color>点",},
{level=1,param2=900,skill_des="宠物道法护甲提高<color=#35ab6c>900</color>点",},
{level=2,consume_item_num=3,param2=1200,skill_des="宠物道法护甲提高<color=#35ab6c>1200</color>点",},
{skill_id=35,skill_name="玄防·绝",level=3,param1="yuansu_hj",param2=1500,skill_icon=989,skill_des="宠物道法护甲提高<color=#35ab6c>1500</color>点",},
{level=4,param2=1800,skill_des="宠物道法护甲提高<color=#35ab6c>1800</color>点",},
{level=5,param2=2100,skill_des="宠物道法护甲提高<color=#35ab6c>2100</color>点",},
{level=6,consume_item_num=9,param2=2400,skill_des="宠物道法护甲提高<color=#35ab6c>2400</color>点",},
{level=7,param2=2700,skill_des="宠物道法护甲提高<color=#35ab6c>2700</color>点",},
{level=8,consume_item_num=12,param2=3000,skill_des="宠物道法护甲提高<color=#35ab6c>3000</color>点",},
{level=9,consume_item_num=15,param2=3300,skill_des="宠物道法护甲提高<color=#35ab6c>3300</color>点",},
{level=10,consume_item_num=99,param2=3600,skill_des="宠物道法护甲提高<color=#35ab6c>3600</color>点",},
{skill_id=36,},
{level=1,consume_item_num=3,param2=9,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>9</color>点",},
{level=2,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>12</color>点",},
{level=3,param2=15,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>15</color>点",},
{skill_id=36,skill_name="守护精进·绝",level=4,skill_type=2,param1="fangyu",param2=18,skill_icon=991,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>18</color>点",},
{level=5,consume_item_num=9,param2=21,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>21</color>点",},
{skill_id=36,},
{level=7,consume_item_num=12,param2=27,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>27</color>点",},
{skill_id=36,},
{skill_id=36,},
{skill_id=36,},
{skill_id=37,},
{level=1,param2=100,skill_des="主人连击抵抗率提高<color=#35ab6c>1%</color>",},
{level=2,consume_item_num=2,param2=130,skill_des="主人连击抵抗率提高<color=#35ab6c>1.3%</color>",},
{level=3,param2=170,skill_des="主人连击抵抗率提高<color=#35ab6c>1.7%</color>",},
{level=4,consume_item_num=4,param2=200,skill_des="主人连击抵抗率提高<color=#35ab6c>2%</color>",},
{skill_id=37,},
{skill_id=37,},
{level=7,consume_item_num=8,param2=300,skill_des="主人连击抵抗率提高<color=#35ab6c>3%</color>",},
{level=8,param2=330,skill_des="主人连击抵抗率提高<color=#35ab6c>3.3%</color>",},
{level=9,consume_item_num=10,param2=370,skill_des="主人连击抵抗率提高<color=#35ab6c>3.7%</color>",},
{level=10,consume_item_num=99,param2=400,skill_des="主人连击抵抗率提高<color=#35ab6c>4%</color>",},
{skill_id=38,},
{skill_id=38,},
{skill_id=38,},
{skill_id=38,level=3,param1="fangyu",param2=1500,skill_icon=983,skill_des="宠物防御提高<color=#35ab6c>1500</color>点",},
{skill_id=38,},
{skill_id=38,},
{skill_id=38,},
{skill_id=38,},
{skill_id=38,},
{skill_id=38,},
{skill_id=38,},
{skill_id=39,skill_name="共生·绝",consume_item_num=0,skill_type=31,param1="shengming_max",skill_icon=985,skill_des="宠物生命提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=3,param2=250,skill_des="宠物生命提高<color=#35ab6c>2.5%</color>",},
{level=2,param2=330,skill_des="宠物生命提高<color=#35ab6c>3.3%</color>",},
{skill_id=39,skill_name="共生·绝",level=3,skill_type=31,param1="shengming_max",param2=420,skill_icon=985,skill_des="宠物生命提高<color=#35ab6c>4.2%</color>",},
{level=4,param2=500,skill_des="宠物生命提高<color=#35ab6c>5%</color>",},
{level=5,consume_item_num=9,param2=580,skill_des="宠物生命提高<color=#35ab6c>5.8%</color>",},
{level=6,param2=670,skill_des="宠物生命提高<color=#35ab6c>6.7%</color>",},
{level=7,consume_item_num=12,param2=750,skill_des="宠物生命提高<color=#35ab6c>7.5%</color>",},
{level=8,param2=830,skill_des="宠物生命提高<color=#35ab6c>8.3%</color>",},
{level=9,consume_item_num=15,param2=920,skill_des="宠物生命提高<color=#35ab6c>9.2%</color>",},
{level=10,consume_item_num=99,param2=1000,skill_des="宠物生命提高<color=#35ab6c>10%</color>",},
{skill_id=40,skill_name="化瘀·绝",skill_icon=994,},
{level=1,consume_item_num=2,param2=80,skill_des="主人伤害减免提高<color=#35ab6c>0.8%</color>",},
{level=2,param2=100,skill_des="主人伤害减免提高<color=#35ab6c>1%</color>",},
{level=3,consume_item_num=4,param2=130,skill_des="主人伤害减免提高<color=#35ab6c>1.3%</color>",},
{level=4,param2=150,skill_des="主人伤害减免提高<color=#35ab6c>1.5%</color>",},
{level=5,param2=180,skill_des="主人伤害减免提高<color=#35ab6c>1.8%</color>",},
{skill_id=40,skill_name="化瘀·绝",param1="shanghai_jm_per",skill_icon=994,skill_des="主人伤害减免提高<color=#35ab6c>2%</color>",},
{level=7,param2=230,skill_des="主人伤害减免提高<color=#35ab6c>2.3%</color>",},
{level=8,consume_item_num=8,param2=250,skill_des="主人伤害减免提高<color=#35ab6c>2.5%</color>",},
{level=9,consume_item_num=10,param2=2800,skill_des="主人伤害减免提高<color=#35ab6c>28%</color>",},
{level=10,consume_item_num=99,param2=300,skill_des="主人伤害减免提高<color=#35ab6c>3%</color>",},
{skill_id=41,},
{skill_id=41,},
{skill_id=41,},
{skill_id=41,},
{skill_id=41,},
{level=5,consume_item_num=9,param2=52500,skill_des="宠物生命提高<color=#35ab6c>52500</color>点",},
{skill_id=41,},
{skill_id=41,},
{level=8,consume_item_num=12,param2=75000,skill_des="宠物生命提高<color=#35ab6c>75000</color>点",},
{skill_id=41,},
{level=10,consume_item_num=99,param2=90000,skill_des="宠物生命提高<color=#35ab6c>90000</color>点",},
{skill_id=42,skill_name="守护精进·绝",consume_item_num=0,skill_type=2,param1="fangyu",skill_icon=991,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>0</color>点",},
{skill_id=42,},
{skill_id=42,},
{skill_id=42,},
{skill_id=42,},
{skill_id=42,},
{level=6,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>24</color>点",},
{skill_id=42,},
{level=8,param2=30,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>30</color>点",},
{level=9,consume_item_num=15,param2=33,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>33</color>点",},
{level=10,consume_item_num=99,param2=36,skill_des="宠物每升<color=#35ab6c>1</color>级，防御提升<color=#35ab6c>36</color>点",},
{skill_id=43,skill_name="蔑神·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="jianshang_bs_per",skill_icon=999,skill_des="主人变身伤害减免提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=2,param2=100,skill_des="主人变身伤害减免提高<color=#35ab6c>1%</color>",},
{level=2,param2=130,skill_des="主人变身伤害减免提高<color=#35ab6c>1.3%</color>",},
{level=3,consume_item_num=4,param2=170,skill_des="主人变身伤害减免提高<color=#35ab6c>1.7%</color>",},
{level=4,param2=200,skill_des="主人变身伤害减免提高<color=#35ab6c>2%</color>",},
{level=5,param2=230,skill_des="主人变身伤害减免提高<color=#35ab6c>2.3%</color>",},
{skill_id=43,skill_name="蔑神·绝",param1="jianshang_bs_per",skill_icon=999,skill_des="主人变身伤害减免提高<color=#35ab6c>2.7%</color>",},
{level=7,param2=300,skill_des="主人变身伤害减免提高<color=#35ab6c>3%</color>",},
{level=8,consume_item_num=8,param2=330,skill_des="主人变身伤害减免提高<color=#35ab6c>3.3%</color>",},
{level=9,consume_item_num=10,param2=370,skill_des="主人变身伤害减免提高<color=#35ab6c>3.7%</color>",},
{level=10,consume_item_num=99,param2=400,skill_des="主人变身伤害减免提高<color=#35ab6c>4%</color>",},
{skill_id=44,},
{level=1,param2=900,skill_des="宠物防御提高<color=#35ab6c>900</color>点",},
{level=2,consume_item_num=3,param2=1200,skill_des="宠物防御提高<color=#35ab6c>1200</color>点",},
{skill_id=44,},
{skill_id=44,},
{level=5,consume_item_num=9,param2=2100,skill_des="宠物防御提高<color=#35ab6c>2100</color>点",},
{level=6,param2=2400,skill_des="宠物防御提高<color=#35ab6c>2400</color>点",},
{level=7,consume_item_num=12,param2=2700,skill_des="宠物防御提高<color=#35ab6c>2700</color>点",},
{level=8,param2=3000,skill_des="宠物防御提高<color=#35ab6c>3000</color>点",},
{level=9,consume_item_num=15,param2=3300,skill_des="宠物防御提高<color=#35ab6c>3300</color>点",},
{level=10,consume_item_num=99,param2=3600,skill_des="宠物防御提高<color=#35ab6c>3600</color>点",},
{skill_id=45,skill_name="真防精进·绝",consume_item_num=0,skill_type=2,param1="fangyu_zs",skill_icon=1000,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>0</color>点",},
{level=1,param2=6,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>6</color>点",},
{level=2,consume_item_num=3,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>8</color>点",},
{level=3,param2=10,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>10</color>点",},
{skill_id=45,skill_name="真防精进·绝",level=4,skill_type=2,param1="fangyu_zs",param2=12,skill_icon=1000,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>12</color>点",},
{level=5,param2=14,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>14</color>点",},
{level=6,consume_item_num=9,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>16</color>点",},
{level=7,param2=18,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>18</color>点",},
{level=8,consume_item_num=12,param2=20,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>20</color>点",},
{level=9,consume_item_num=15,param2=22,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>22</color>点",},
{level=10,consume_item_num=99,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，真实防御提升<color=#35ab6c>24</color>点",},
{skill_id=46,skill_name="提气·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="jichuankang_per",skill_icon=505,skill_des="主人击穿抵抗提高<color=#35ab6c>0%</color>",},
{level=1,param2=100,skill_des="主人击穿抵抗提高<color=#35ab6c>1%</color>",},
{skill_id=46,},
{level=3,param2=170,skill_des="主人击穿抵抗提高<color=#35ab6c>1.7%</color>",},
{level=4,consume_item_num=4,param2=200,skill_des="主人击穿抵抗提高<color=#35ab6c>2%</color>",},
{skill_id=46,skill_name="提气·绝",param1="jichuankang_per",skill_icon=505,skill_des="主人击穿抵抗提高<color=#35ab6c>2.3%</color>",},
{skill_id=46,},
{level=7,param2=300,skill_des="主人击穿抵抗提高<color=#35ab6c>3%</color>",},
{level=8,consume_item_num=8,param2=330,skill_des="主人击穿抵抗提高<color=#35ab6c>3.3%</color>",},
{skill_id=46,},
{level=10,consume_item_num=99,param2=400,skill_des="主人击穿抵抗提高<color=#35ab6c>4%</color>",},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=47,},
{skill_id=48,},
{level=1,consume_item_num=3,param1=130,skill_des="宠物全属性提高<color=#35ab6c>1.3%</color>",},
{level=2,param1=170,skill_des="宠物全属性提高<color=#35ab6c>1.7%</color>",},
{skill_id=48,},
{skill_id=48,},
{level=5,consume_item_num=9,param1=290,skill_des="宠物全属性提高<color=#35ab6c>2.9%</color>",},
{level=6,param1=330,skill_des="宠物全属性提高<color=#35ab6c>3.3%</color>",},
{level=7,consume_item_num=12,param1=380,skill_des="宠物全属性提高<color=#35ab6c>3.8%</color>",},
{level=8,param1=420,skill_des="宠物全属性提高<color=#35ab6c>4.2%</color>",},
{level=9,consume_item_num=15,param1=460,skill_des="宠物全属性提高<color=#35ab6c>4.6%</color>",},
{level=10,consume_item_num=99,param1=500,skill_des="宠物全属性提高<color=#35ab6c>5%</color>",},
{skill_id=49,},
{skill_id=49,},
{level=2,consume_item_num=2,param2=130,skill_des="主人击穿抵抗提高<color=#35ab6c>1.3%</color>",},
{skill_id=49,},
{skill_id=49,},
{skill_id=49,},
{skill_id=49,level=6,param2=270,skill_des="主人击穿抵抗提高<color=#35ab6c>2.7%</color>",},
{skill_id=49,},
{skill_id=49,},
{level=9,consume_item_num=10,param2=370,skill_des="主人击穿抵抗提高<color=#35ab6c>3.7%</color>",},
{skill_id=49,},
{skill_id=50,},
{level=1,consume_item_num=3,param2=900,skill_des="宠物道法伤害提高<color=#35ab6c>900</color>点",},
{level=2,param2=1200,skill_des="宠物道法伤害提高<color=#35ab6c>1200</color>点",},
{level=3,param2=1500,skill_des="宠物道法伤害提高<color=#35ab6c>1500</color>点",},
{skill_id=50,skill_name="玄法·绝",level=4,param1="yuansu_sh",param2=1800,skill_icon=986,skill_des="宠物道法伤害提高<color=#35ab6c>1800</color>点",},
{level=5,consume_item_num=9,param2=2100,skill_des="宠物道法伤害提高<color=#35ab6c>2100</color>点",},
{level=6,param2=2400,skill_des="宠物道法伤害提高<color=#35ab6c>2400</color>点",},
{level=7,param2=2700,skill_des="宠物道法伤害提高<color=#35ab6c>2700</color>点",},
{level=8,consume_item_num=12,param2=3000,skill_des="宠物道法伤害提高<color=#35ab6c>3000</color>点",},
{level=9,consume_item_num=15,param2=3300,skill_des="宠物道法伤害提高<color=#35ab6c>3300</color>点",},
{skill_id=50,},
{skill_id=51,},
{level=1,param2=9,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>9</color>点",},
{level=2,consume_item_num=3,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>12</color>点",},
{level=3,param2=15,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>15</color>点",},
{skill_id=51,skill_name="协力精进·绝",level=4,skill_type=2,param2=18,skill_icon=987,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>18</color>点",},
{level=5,consume_item_num=9,param2=21,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>21</color>点",},
{level=6,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>24</color>点",},
{level=7,param2=27,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>27</color>点",},
{level=8,consume_item_num=12,param2=30,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>30</color>点",},
{level=9,consume_item_num=15,param2=33,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>33</color>点",},
{level=10,consume_item_num=99,param2=36,skill_des="宠物每升<color=#35ab6c>1</color>级，攻击提升<color=#35ab6c>36</color>点",},
{skill_id=52,skill_name="刺髓·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="shanghai_jc_per",skill_icon=1001,skill_des="主人伤害加成提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=2,param2=80,skill_des="主人伤害加成提高<color=#35ab6c>0.8%</color>",},
{level=2,param2=100,skill_des="主人伤害加成提高<color=#35ab6c>1%</color>",},
{level=3,param2=130,skill_des="主人伤害加成提高<color=#35ab6c>1.3%</color>",},
{level=4,consume_item_num=4,param2=150,skill_des="主人伤害加成提高<color=#35ab6c>1.5%</color>",},
{level=5,param2=180,skill_des="主人伤害加成提高<color=#35ab6c>1.8%</color>",},
{skill_id=52,skill_name="刺髓·绝",param1="shanghai_jc_per",param2=200,skill_icon=1001,skill_des="主人伤害加成提高<color=#35ab6c>2%</color>",},
{level=7,param2=230,skill_des="主人伤害加成提高<color=#35ab6c>2.3%</color>",},
{level=8,consume_item_num=8,param2=250,skill_des="主人伤害加成提高<color=#35ab6c>2.5%</color>",},
{level=9,consume_item_num=10,param2=280,skill_des="主人伤害加成提高<color=#35ab6c>2.8%</color>",},
{level=10,consume_item_num=99,param2=300,skill_des="主人伤害加成提高<color=#35ab6c>3%</color>",},
{skill_id=53,skill_name="破躯·绝",consume_item_num=0,param1="pojia",skill_icon=982,skill_des="宠物破甲提高<color=#35ab6c>0</color>点",},
{level=1,consume_item_num=3,param2=900,skill_des="宠物破甲提高<color=#35ab6c>900</color>点",},
{level=2,param2=1200,skill_des="宠物破甲提高<color=#35ab6c>1200</color>点",},
{level=3,param2=1500,skill_des="宠物破甲提高<color=#35ab6c>1500</color>点",},
{skill_id=53,skill_name="破躯·绝",level=4,param1="pojia",param2=1800,skill_icon=982,skill_des="宠物破甲提高<color=#35ab6c>1800</color>点",},
{level=5,param2=2100,skill_des="宠物破甲提高<color=#35ab6c>2100</color>点",},
{level=6,consume_item_num=9,param2=2400,skill_des="宠物破甲提高<color=#35ab6c>2400</color>点",},
{level=7,param2=2700,skill_des="宠物破甲提高<color=#35ab6c>2700</color>点",},
{level=8,consume_item_num=12,param2=3000,skill_des="宠物破甲提高<color=#35ab6c>3000</color>点",},
{level=9,consume_item_num=15,param2=3300,skill_des="宠物破甲提高<color=#35ab6c>3300</color>点",},
{level=10,consume_item_num=99,param2=3600,skill_des="宠物破甲提高<color=#35ab6c>3600</color>点",},
{skill_id=54,skill_name="合纵·绝",consume_item_num=0,skill_type=31,skill_icon=988,skill_des="宠物攻击提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=3,param2=750,skill_des="宠物攻击提高<color=#35ab6c>7.5%</color>",},
{level=2,param2=1000,skill_des="宠物攻击提高<color=#35ab6c>10%</color>",},
{level=3,param2=1250,skill_des="宠物攻击提高<color=#35ab6c>12.5%</color>",},
{skill_id=54,skill_name="合纵·绝",level=4,skill_type=31,param2=1500,skill_icon=988,skill_des="宠物攻击提高<color=#35ab6c>15%</color>",},
{level=5,param2=1750,skill_des="宠物攻击提高<color=#35ab6c>17.5%</color>",},
{level=6,consume_item_num=9,param2=2000,skill_des="宠物攻击提高<color=#35ab6c>20%</color>",},
{level=7,param2=2250,skill_des="宠物攻击提高<color=#35ab6c>22.5%</color>",},
{level=8,consume_item_num=12,param2=2500,skill_des="宠物攻击提高<color=#35ab6c>25%</color>",},
{level=9,consume_item_num=15,param2=2750,skill_des="宠物攻击提高<color=#35ab6c>27.5%</color>",},
{level=10,consume_item_num=99,param2=3000,skill_des="宠物攻击提高<color=#35ab6c>30%</color>",},
{skill_id=55,skill_name="迂回·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="lianjikang_per",skill_icon=923,skill_des="主人连击抵抗率提高<color=#35ab6c>0%</color>",},
{skill_id=55,},
{skill_id=55,},
{skill_id=55,},
{skill_id=55,},
{level=5,param2=230,skill_des="主人连击抵抗率提高<color=#35ab6c>2.3%</color>",},
{skill_id=55,skill_name="迂回·绝",level=6,param1="lianjikang_per",param2=270,skill_icon=923,skill_des="主人连击抵抗率提高<color=#35ab6c>2.7%</color>",},
{skill_id=55,},
{skill_id=55,},
{skill_id=55,},
{skill_id=55,},
{skill_id=56,skill_name="玄法·绝",},
{skill_id=56,},
{skill_id=56,},
{skill_id=56,},
{skill_id=56,},
{skill_id=56,},
{skill_id=56,},
{skill_id=56,},
{skill_id=56,},
{skill_id=56,},
{skill_id=56,},
{skill_id=57,skill_name="协力精进·绝",},
{skill_id=57,},
{skill_id=57,},
{skill_id=57,},
{skill_id=57,},
{skill_id=57,},
{skill_id=57,},
{skill_id=57,},
{skill_id=57,},
{skill_id=57,},
{skill_id=57,},
{skill_id=58,skill_name="屠戮·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="zengshang_boss_per",skill_icon=1002,skill_des="主人首领增伤提高<color=#35ab6c>0%</color>",},
{level=1,param2=100,skill_des="主人首领增伤提高<color=#35ab6c>1%</color>",},
{level=2,consume_item_num=2,param2=130,skill_des="主人首领增伤提高<color=#35ab6c>1.3%</color>",},
{level=3,param2=170,skill_des="主人首领增伤提高<color=#35ab6c>1.7%</color>",},
{level=4,consume_item_num=4,param2=200,skill_des="主人首领增伤提高<color=#35ab6c>2%</color>",},
{level=5,param2=230,skill_des="主人首领增伤提高<color=#35ab6c>2.3%</color>",},
{skill_id=58,skill_name="屠戮·绝",param1="zengshang_boss_per",skill_icon=1002,skill_des="主人首领增伤提高<color=#35ab6c>2.7%</color>",},
{level=7,param2=300,skill_des="主人首领增伤提高<color=#35ab6c>3%</color>",},
{level=8,consume_item_num=8,param2=330,skill_des="主人首领增伤提高<color=#35ab6c>3.3%</color>",},
{level=9,consume_item_num=10,param2=370,skill_des="主人首领增伤提高<color=#35ab6c>3.7%</color>",},
{level=10,consume_item_num=99,param2=400,skill_des="主人首领增伤提高<color=#35ab6c>4%</color>",},
{skill_id=59,},
{skill_id=59,},
{skill_id=59,},
{skill_id=59,},
{skill_id=59,},
{skill_id=59,},
{skill_id=59,},
{skill_id=59,},
{skill_id=59,},
{skill_id=59,},
{level=10,consume_item_num=99,param2=3600,skill_des="宠物道法伤害提高<color=#35ab6c>3600</color>点",},
{skill_id=60,skill_name="弑弱·绝",consume_item_num=0,skill_type=31,param1="pojia",skill_icon=1003,skill_des="宠物破甲提高<color=#35ab6c>0%</color>",},
{level=1,param2=2250,skill_des="宠物破甲提高<color=#35ab6c>22.5%</color>",},
{level=2,consume_item_num=3,param2=3000,skill_des="宠物破甲提高<color=#35ab6c>30%</color>",},
{level=3,param2=3750,skill_des="宠物破甲提高<color=#35ab6c>37.5%</color>",},
{skill_id=60,skill_name="弑弱·绝",level=4,skill_type=31,param1="pojia",param2=4500,skill_icon=1003,skill_des="宠物破甲提高<color=#35ab6c>45%</color>",},
{level=5,param2=5250,skill_des="宠物破甲提高<color=#35ab6c>52.5%</color>",},
{level=6,consume_item_num=9,param2=6000,skill_des="宠物破甲提高<color=#35ab6c>60%</color>",},
{level=7,param2=6750,skill_des="宠物破甲提高<color=#35ab6c>67.5%</color>",},
{level=8,consume_item_num=12,param2=7500,skill_des="宠物破甲提高<color=#35ab6c>75%</color>",},
{level=9,consume_item_num=15,param2=8250,skill_des="宠物破甲提高<color=#35ab6c>82.5%</color>",},
{level=10,consume_item_num=99,param2=9000,skill_des="宠物破甲提高<color=#35ab6c>90%</color>",},
{skill_id=61,skill_name="诛仙·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="zengshang_bs_per",skill_icon=512,skill_des="主人变身增伤提高<color=#35ab6c>0%</color>",},
{level=1,param2=100,skill_des="主人变身增伤提高<color=#35ab6c>1%</color>",},
{level=2,consume_item_num=2,param2=130,skill_des="主人变身增伤提高<color=#35ab6c>1.3%</color>",},
{level=3,param2=170,skill_des="主人变身增伤提高<color=#35ab6c>1.7%</color>",},
{level=4,consume_item_num=4,param2=200,skill_des="主人变身增伤提高<color=#35ab6c>2%</color>",},
{skill_id=61,skill_name="诛仙·绝",param1="zengshang_bs_per",skill_icon=512,skill_des="主人变身增伤提高<color=#35ab6c>2.3%</color>",},
{level=6,param2=270,skill_des="主人变身增伤提高<color=#35ab6c>2.7%</color>",},
{level=7,param2=300,skill_des="主人变身增伤提高<color=#35ab6c>3%</color>",},
{level=8,consume_item_num=8,param2=330,skill_des="主人变身增伤提高<color=#35ab6c>3.3%</color>",},
{level=9,consume_item_num=10,param2=370,skill_des="主人变身增伤提高<color=#35ab6c>3.7%</color>",},
{level=10,consume_item_num=99,param2=400,skill_des="主人变身增伤提高<color=#35ab6c>4%</color>",},
{skill_id=62,},
{level=1,param2=900,skill_des="宠物攻击提高<color=#35ab6c>900</color>点",},
{level=2,consume_item_num=3,param2=1200,skill_des="宠物攻击提高<color=#35ab6c>1200</color>点",},
{skill_id=62,},
{skill_id=62,},
{level=5,param2=2100,skill_des="宠物攻击提高<color=#35ab6c>2100</color>点",},
{level=6,consume_item_num=9,param2=2400,skill_des="宠物攻击提高<color=#35ab6c>2400</color>点",},
{level=7,param2=2700,skill_des="宠物攻击提高<color=#35ab6c>2700</color>点",},
{level=8,consume_item_num=12,param2=3000,skill_des="宠物攻击提高<color=#35ab6c>3000</color>点",},
{level=9,consume_item_num=15,param2=3300,skill_des="宠物攻击提高<color=#35ab6c>3300</color>点",},
{level=10,consume_item_num=99,param2=3600,skill_des="宠物攻击提高<color=#35ab6c>3600</color>点",},
{skill_id=63,skill_name="真伤精进·绝",consume_item_num=0,skill_type=2,param1="shanghai_zs",skill_icon=1004,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>0</color>点",},
{level=1,consume_item_num=3,param2=6,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>6</color>点",},
{level=2,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>8</color>点",},
{level=3,param2=10,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>10</color>点",},
{skill_id=63,skill_name="真伤精进·绝",level=4,skill_type=2,param1="shanghai_zs",param2=12,skill_icon=1004,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>12</color>点",},
{level=5,param2=14,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>14</color>点",},
{level=6,consume_item_num=9,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>16</color>点",},
{level=7,consume_item_num=12,param2=18,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>18</color>点",},
{level=8,param2=20,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>20</color>点",},
{level=9,consume_item_num=15,param2=22,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>22</color>点",},
{level=10,consume_item_num=99,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，真实伤害提升<color=#35ab6c>24</color>点",},
{skill_id=64,skill_name="穿杨·绝",consume_item=27719,consume_item_num=0,skill_type=3,param1="mingzhong_per",skill_icon=1005,skill_des="主人命中率提高<color=#35ab6c>0%</color>",},
{level=1,param2=50,skill_des="主人命中率提高<color=#35ab6c>0.5%</color>",},
{level=2,consume_item_num=2,param2=70,skill_des="主人命中率提高<color=#35ab6c>0.7%</color>",},
{level=3,consume_item_num=4,param2=80,skill_des="主人命中率提高<color=#35ab6c>0.8%</color>",},
{level=4,param2=100,skill_des="主人命中率提高<color=#35ab6c>1%</color>",},
{skill_id=64,skill_icon=1005,},
{skill_id=64,skill_icon=1005,},
{level=7,consume_item_num=8,param2=150,skill_des="主人命中率提高<color=#35ab6c>1.5%</color>",},
{level=8,param2=170,skill_des="主人命中率提高<color=#35ab6c>1.7%</color>",},
{level=9,consume_item_num=10,param2=180,skill_des="主人命中率提高<color=#35ab6c>1.8%</color>",},
{level=10,consume_item_num=99,param2=200,skill_des="主人命中率提高<color=#35ab6c>2%</color>",},
{skill_id=65,},
{level=1,param2=30000,skill_des="宠物生命提高<color=#35ab6c>30000</color>点",},
{skill_id=65,skill_name="同心·极",level=2,consume_item_num=4,},
{level=3,param2=50000,skill_des="宠物生命提高<color=#35ab6c>50000</color>点",},
{level=4,consume_item_num=8,param2=60000,skill_des="宠物生命提高<color=#35ab6c>60000</color>点",},
{level=5,consume_item_num=12,param2=70000,skill_des="宠物生命提高<color=#35ab6c>70000</color>点",},
{level=6,param2=80000,skill_des="宠物生命提高<color=#35ab6c>80000</color>点",},
{level=7,param2=90000,skill_des="宠物生命提高<color=#35ab6c>90000</color>点",},
{level=8,consume_item_num=16,param2=100000,skill_des="宠物生命提高<color=#35ab6c>100000</color>点",},
{level=9,consume_item_num=20,param2=110000,skill_des="宠物生命提高<color=#35ab6c>110000</color>点",},
{skill_id=65,skill_name="同心·极",param2=120000,skill_des="宠物生命提高<color=#35ab6c>120000</color>点",},
{skill_id=66,skill_name="抗法·极",consume_item_num=0,skill_type=31,param1="yuansu_hj",skill_icon=1006,skill_des="宠物道法护甲提高<color=#35ab6c>0%</color>",},
{level=1,param2=3000,skill_des="宠物道法护甲提高<color=#35ab6c>30%</color>",},
{level=2,consume_item_num=4,param2=4000,skill_des="宠物道法护甲提高<color=#35ab6c>40%</color>",},
{level=3,param2=5000,skill_des="宠物道法护甲提高<color=#35ab6c>50%</color>",},
{level=4,consume_item_num=8,param2=6000,skill_des="宠物道法护甲提高<color=#35ab6c>60%</color>",},
{level=5,param2=7000,skill_des="宠物道法护甲提高<color=#35ab6c>70%</color>",},
{level=6,consume_item_num=12,param2=8000,skill_des="宠物道法护甲提高<color=#35ab6c>80%</color>",},
{level=7,param2=9000,skill_des="宠物道法护甲提高<color=#35ab6c>90%</color>",},
{level=8,consume_item_num=16,param2=10000,skill_des="宠物道法护甲提高<color=#35ab6c>100%</color>",},
{level=9,consume_item_num=20,param2=11000,skill_des="宠物道法护甲提高<color=#35ab6c>110%</color>",},
{level=10,consume_item_num=99,param2=12000,skill_des="宠物道法护甲提高<color=#35ab6c>120%</color>",},
{skill_id=67,skill_name="抗兽·极",consume_item=27719,consume_item_num=0,skill_type=3,param1="jianshang_boss_per",skill_icon=1007,skill_des="主人首领减伤提高<color=#35ab6c>0%</color>",},
{level=1,param2=150,skill_des="主人首领减伤提高<color=#35ab6c>1.5%</color>",},
{level=2,consume_item_num=3,param2=200,skill_des="主人首领减伤提高<color=#35ab6c>2%</color>",},
{level=3,param2=250,skill_des="主人首领减伤提高<color=#35ab6c>2.5%</color>",},
{skill_id=67,skill_name="抗兽·极",param1="jianshang_boss_per",skill_icon=1007,skill_des="主人首领减伤提高<color=#35ab6c>3%</color>",},
{level=5,param2=350,skill_des="主人首领减伤提高<color=#35ab6c>3.5%</color>",},
{level=6,consume_item_num=9,param2=400,skill_des="主人首领减伤提高<color=#35ab6c>4%</color>",},
{level=7,param2=450,skill_des="主人首领减伤提高<color=#35ab6c>4.5%</color>",},
{level=8,consume_item_num=12,param2=500,skill_des="主人首领减伤提高<color=#35ab6c>5%</color>",},
{level=9,consume_item_num=15,param2=550,skill_des="主人首领减伤提高<color=#35ab6c>5.5%</color>",},
{level=10,consume_item_num=99,param2=600,skill_des="主人首领减伤提高<color=#35ab6c>6%</color>",},
{skill_id=68,skill_name="狩猎",consume_item=27720,consume_item_num=0,skill_type=10,param1=4000,skill_icon=1008,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=2,param2=250,attack_power=3400,defence_power=3400,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>2.5%</color>",},
{level=2,param2=330,attack_power=4500,defence_power=4500,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>3.3%</color>",},
{level=3,param2=420,attack_power=5600,defence_power=5600,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>4.2%</color>",},
{level=4,consume_item_num=4,param2=500,attack_power=6700,defence_power=6700,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>5%</color>",},
{level=5,param2=580,attack_power=7800,defence_power=7800,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>5.8%</color>",},
{skill_id=68,skill_name="狩猎",skill_type=10,param1=4000,param2=670,skill_icon=1008,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>6.7%</color>",},
{level=7,param2=750,attack_power=10000,defence_power=10000,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>7.5%</color>",},
{level=8,consume_item_num=8,param2=830,attack_power=11100,defence_power=11100,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>8.3%</color>",},
{level=9,consume_item_num=10,param2=920,attack_power=12200,defence_power=12200,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>9.2%</color>",},
{level=10,consume_item_num=99,param2=1000,attack_power=13400,defence_power=13400,skill_des="主人攻击首领怪物时，主人的首领增伤有<color=#35ab6c>40%</color>的概率提高<color=#35ab6c>10%</color>",},
{skill_id=69,skill_name="玄防·极",},
{level=1,consume_item_num=4,param2=1200,skill_des="宠物道法护甲提高<color=#35ab6c>1200</color>点",},
{level=2,param2=1600,skill_des="宠物道法护甲提高<color=#35ab6c>1600</color>点",},
{level=3,param2=2000,skill_des="宠物道法护甲提高<color=#35ab6c>2000</color>点",},
{level=4,consume_item_num=8,param2=2400,skill_des="宠物道法护甲提高<color=#35ab6c>2400</color>点",},
{level=5,param2=2800,skill_des="宠物道法护甲提高<color=#35ab6c>2800</color>点",},
{level=6,consume_item_num=12,param2=3200,skill_des="宠物道法护甲提高<color=#35ab6c>3200</color>点",},
{level=7,param2=3600,skill_des="宠物道法护甲提高<color=#35ab6c>3600</color>点",},
{level=8,consume_item_num=16,param2=4000,skill_des="宠物道法护甲提高<color=#35ab6c>4000</color>点",},
{level=9,consume_item_num=20,param2=4400,skill_des="宠物道法护甲提高<color=#35ab6c>4400</color>点",},
{level=10,consume_item_num=99,param2=4800,skill_des="宠物道法护甲提高<color=#35ab6c>4800</color>点",},
{skill_id=70,skill_name="均衡·极",consume_item_num=0,skill_type=32,param1=0,skill_icon=500,skill_des="宠物全属性提高<color=#35ab6c>0%</color>",},
{level=1,param1=170,skill_des="宠物全属性提高<color=#35ab6c>1.7%</color>",},
{level=2,consume_item_num=4,param1=220,skill_des="宠物全属性提高<color=#35ab6c>2.2%</color>",},
{level=3,param1=280,skill_des="宠物全属性提高<color=#35ab6c>2.8%</color>",},
{level=4,consume_item_num=8,param1=330,skill_des="宠物全属性提高<color=#35ab6c>3.3%</color>",},
{level=5,param1=390,skill_des="宠物全属性提高<color=#35ab6c>3.9%</color>",},
{level=6,consume_item_num=12,param1=440,skill_des="宠物全属性提高<color=#35ab6c>4.4%</color>",},
{level=7,param1=500,skill_des="宠物全属性提高<color=#35ab6c>5%</color>",},
{level=8,consume_item_num=16,param1=560,skill_des="宠物全属性提高<color=#35ab6c>5.6%</color>",},
{level=9,consume_item_num=20,param1=610,skill_des="宠物全属性提高<color=#35ab6c>6.1%</color>",},
{level=10,consume_item_num=99,param1=670,skill_des="宠物全属性提高<color=#35ab6c>6.7%</color>",},
{skill_id=71,skill_name="化瘀·极",consume_item=27719,consume_item_num=0,skill_type=3,param1="shanghai_jm_per",skill_icon=1009,skill_des="主人伤害减免提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=3,param2=110,skill_des="主人伤害减免提高<color=#35ab6c>1.1%</color>",},
{level=2,param2=150,skill_des="主人伤害减免提高<color=#35ab6c>1.5%</color>",},
{skill_id=71,skill_name="化瘀·极",param1="shanghai_jm_per",param2=190,skill_icon=1009,skill_des="主人伤害减免提高<color=#35ab6c>1.9%</color>",},
{level=4,param2=230,skill_des="主人伤害减免提高<color=#35ab6c>2.3%</color>",},
{level=5,consume_item_num=9,param2=260,skill_des="主人伤害减免提高<color=#35ab6c>2.6%</color>",},
{level=6,param2=300,skill_des="主人伤害减免提高<color=#35ab6c>3%</color>",},
{level=7,param2=340,skill_des="主人伤害减免提高<color=#35ab6c>3.4%</color>",},
{level=8,consume_item_num=12,param2=380,skill_des="主人伤害减免提高<color=#35ab6c>3.8%</color>",},
{level=9,consume_item_num=15,param2=410,skill_des="主人伤害减免提高<color=#35ab6c>4.1%</color>",},
{level=10,consume_item_num=99,param2=450,skill_des="主人伤害减免提高<color=#35ab6c>4.5%</color>",},
{skill_id=72,skill_name="乘胜追击",skill_type=20,param1=5000,param3=5000,param4=30000,skill_icon=1010,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>0%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=1,consume_item_num=2,param2=300,attack_power=3400,defence_power=3400,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>3%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=2,param2=400,attack_power=4500,defence_power=4500,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>4%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=3,param2=500,attack_power=5600,defence_power=5600,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=4,consume_item_num=4,param2=600,attack_power=6700,defence_power=6700,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>6%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=5,param2=700,attack_power=7800,defence_power=7800,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>7%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{skill_id=72,skill_name="乘胜追击",skill_type=20,param3=5000,param4=30000,skill_icon=1010,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>8%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=7,consume_item_num=8,param2=900,attack_power=10000,defence_power=10000,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>9%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=8,param2=1000,attack_power=11100,defence_power=11100,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>10%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=9,consume_item_num=10,param2=1100,attack_power=12200,defence_power=12200,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>11%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=10,consume_item_num=99,param2=1200,attack_power=13400,defence_power=13400,skill_des="主人攻击玩家时，有<color=#35ab6c>50%</color>的概率增加主人玩家增伤<color=#35ab6c>12%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{skill_id=73,skill_name="协力·极",},
{skill_id=73,},
{skill_id=73,},
{skill_id=73,},
{level=4,param2=2400,skill_des="宠物攻击提高<color=#35ab6c>2400</color>点",},
{skill_id=73,},
{level=6,consume_item_num=12,param2=3200,skill_des="宠物攻击提高<color=#35ab6c>3200</color>点",},
{level=7,consume_item_num=16,param2=3600,skill_des="宠物攻击提高<color=#35ab6c>3600</color>点",},
{level=8,param2=4000,skill_des="宠物攻击提高<color=#35ab6c>4000</color>点",},
{level=9,consume_item_num=20,param2=4400,skill_des="宠物攻击提高<color=#35ab6c>4400</color>点",},
{skill_id=73,skill_name="协力·极",param2=4800,skill_des="宠物攻击提高<color=#35ab6c>4800</color>点",},
{skill_id=74,skill_name="炼法·极",consume_item_num=0,skill_type=31,param1="yuansu_sh",skill_icon=1011,skill_des="宠物道法伤害提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=4,param2=1500,skill_des="宠物道法伤害提高<color=#35ab6c>15%</color>",},
{level=2,param2=2000,skill_des="宠物道法伤害提高<color=#35ab6c>20%</color>",},
{level=3,consume_item_num=8,param2=2500,skill_des="宠物道法伤害提高<color=#35ab6c>25%</color>",},
{level=4,param2=3000,skill_des="宠物道法伤害提高<color=#35ab6c>30%</color>",},
{level=5,consume_item_num=12,param2=3500,skill_des="宠物道法伤害提高<color=#35ab6c>35%</color>",},
{level=6,param2=4000,skill_des="宠物道法伤害提高<color=#35ab6c>40%</color>",},
{level=7,consume_item_num=16,param2=4500,skill_des="宠物道法伤害提高<color=#35ab6c>45%</color>",},
{level=8,param2=5000,skill_des="宠物道法伤害提高<color=#35ab6c>50%</color>",},
{level=9,consume_item_num=20,param2=5500,skill_des="宠物道法伤害提高<color=#35ab6c>55%</color>",},
{level=10,consume_item_num=99,param2=6000,skill_des="宠物道法伤害提高<color=#35ab6c>60%</color>",},
{skill_id=75,skill_name="蓄势·极",consume_item=27719,consume_item_num=0,skill_type=3,param1="jichuan_per",skill_icon=1012,skill_des="主人击穿率提高<color=#35ab6c>0%</color>",},
{level=1,param2=150,skill_des="主人击穿率提高<color=#35ab6c>1.5%</color>",},
{level=2,consume_item_num=3,param2=200,skill_des="主人击穿率提高<color=#35ab6c>2%</color>",},
{skill_id=75,skill_name="蓄势·极",consume_item=27719,skill_type=3,param1="jichuan_per",param2=250,skill_icon=1012,skill_des="主人击穿率提高<color=#35ab6c>2.5%</color>",},
{level=4,param2=300,skill_des="主人击穿率提高<color=#35ab6c>3%</color>",},
{level=5,consume_item_num=9,param2=350,skill_des="主人击穿率提高<color=#35ab6c>3.5%</color>",},
{level=6,param2=400,skill_des="主人击穿率提高<color=#35ab6c>4%</color>",},
{level=7,consume_item_num=12,param2=450,skill_des="主人击穿率提高<color=#35ab6c>4.5%</color>",},
{level=8,param2=500,skill_des="主人击穿率提高<color=#35ab6c>5%</color>",},
{level=9,consume_item_num=15,param2=550,skill_des="主人击穿率提高<color=#35ab6c>5.5%</color>",},
{level=10,consume_item_num=99,param2=600,skill_des="主人击穿率提高<color=#35ab6c>6%</color>",},
{skill_id=76,skill_name="弱点洞悉",consume_item=27720,consume_item_num=0,skill_type=11,param1=0,skill_icon=1013,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>0%</color>的玩家增伤",},
{level=1,param1=200,attack_power=3400,defence_power=3400,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>2%</color>的玩家增伤",},
{level=2,consume_item_num=2,param1=270,attack_power=4500,defence_power=4500,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>2.7%</color>的玩家增伤",},
{level=3,consume_item_num=4,param1=330,attack_power=5600,defence_power=5600,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>3.3%</color>的玩家增伤",},
{level=4,param1=400,attack_power=6700,defence_power=6700,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>4%</color>的玩家增伤",},
{skill_id=76,skill_name="弱点洞悉",skill_type=11,param1=470,skill_icon=1013,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>4.7%</color>的玩家增伤",},
{level=6,param1=530,attack_power=8900,defence_power=8900,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>5.3%</color>的玩家增伤",},
{level=7,consume_item_num=8,param1=600,attack_power=10000,defence_power=10000,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>6%</color>的玩家增伤",},
{level=8,param1=670,attack_power=11100,defence_power=11100,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>6.7%</color>的玩家增伤",},
{level=9,consume_item_num=10,param1=730,attack_power=12200,defence_power=12200,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>7.3%</color>的玩家增伤",},
{level=10,consume_item_num=99,param1=800,attack_power=13400,defence_power=13400,skill_des="主人<color=#35ab6c>触发击穿</color>，且目标为玩家时，提升主人<color=#35ab6c>8%</color>的玩家增伤",},
{skill_id=77,},
{skill_id=77,skill_name="协力·极",level=1,},
{level=2,param2=1600,skill_des="宠物攻击提高<color=#35ab6c>1600</color>点",},
{skill_id=77,skill_name="协力·极",level=3,},
{skill_id=77,},
{level=5,param2=2800,skill_des="宠物攻击提高<color=#35ab6c>2800</color>点",},
{skill_id=77,},
{skill_id=77,},
{skill_id=77,},
{skill_id=77,},
{skill_id=77,},
{skill_id=78,skill_name="玄法精进·极",},
{skill_id=78,skill_name="玄法精进·极",level=1,},
{level=2,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>16</color>点",},
{skill_id=78,skill_name="玄法精进·极",level=3,},
{level=4,param2=24,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>24</color>点",},
{level=5,consume_item_num=12,param2=28,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>28</color>点",},
{level=6,param2=32,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>32</color>点",},
{level=7,consume_item_num=16,param2=36,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>36</color>点",},
{level=8,param2=40,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>40</color>点",},
{level=9,consume_item_num=20,param2=44,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>44</color>点",},
{level=10,consume_item_num=99,param2=48,skill_des="宠物每升<color=#35ab6c>1</color>级，道法伤害提升<color=#35ab6c>48</color>点",},
{skill_id=79,skill_name="狂怒·极",},
{level=1,consume_item_num=3,param2=150,skill_des="主人暴击率提高<color=#35ab6c>1.5%</color>",},
{level=2,param2=200,skill_des="主人暴击率提高<color=#35ab6c>2%</color>",},
{level=3,param2=250,skill_des="主人暴击率提高<color=#35ab6c>2.5%</color>",},
{skill_id=79,skill_name="狂怒·极",param1="baoji_per",skill_icon=1014,skill_des="主人暴击率提高<color=#35ab6c>3%</color>",},
{level=5,consume_item_num=9,param2=350,skill_des="主人暴击率提高<color=#35ab6c>3.5%</color>",},
{level=6,param2=400,skill_des="主人暴击率提高<color=#35ab6c>4%</color>",},
{level=7,consume_item_num=12,param2=450,skill_des="主人暴击率提高<color=#35ab6c>4.5%</color>",},
{level=8,param2=500,skill_des="主人暴击率提高<color=#35ab6c>5%</color>",},
{level=9,consume_item_num=15,param2=550,skill_des="主人暴击率提高<color=#35ab6c>5.5%</color>",},
{level=10,consume_item_num=99,param2=600,skill_des="主人暴击率提高<color=#35ab6c>6%</color>",},
{skill_id=80,skill_name="金刚怒目",consume_item=27720,consume_item_num=0,skill_type=12,param1=0,skill_icon=418,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>0%</color>的伤害",},
{level=1,consume_item_num=2,param1=190,attack_power=3400,defence_power=3400,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>1.9%</color>的伤害",},
{level=2,param1=250,attack_power=4500,defence_power=4500,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>2.5%</color>的伤害",},
{level=3,param1=310,attack_power=5600,defence_power=5600,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>3.1%</color>的伤害",},
{level=4,consume_item_num=4,param1=380,attack_power=6700,defence_power=6700,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>3.8%</color>的伤害",},
{skill_id=80,skill_name="金刚怒目",level=5,consume_item=27720,skill_type=12,param1=440,attack_power=7800,defence_power=7800,skill_icon=418,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>4.4%</color>的伤害",},
{level=6,param1=500,attack_power=8900,defence_power=8900,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>5%</color>的伤害",},
{level=7,param1=560,attack_power=10000,defence_power=10000,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>5.6%</color>的伤害",},
{level=8,consume_item_num=8,param1=630,attack_power=11100,defence_power=11100,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>6.3%</color>的伤害",},
{level=9,consume_item_num=10,param1=690,attack_power=12200,defence_power=12200,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>6.9%</color>的伤害",},
{level=10,consume_item_num=99,param1=750,attack_power=13400,defence_power=13400,skill_des="主人<color=#35ab6c>触发暴击</color>，且主人血量低于<color=#35ab6c>50%</color>时，提升主人<color=#35ab6c>7.5%</color>的伤害",},
{skill_id=81,skill_name="玄法·极",},
{level=1,consume_item_num=4,param2=1200,skill_des="宠物道法伤害提高<color=#35ab6c>1200</color>点",},
{level=2,param2=1600,skill_des="宠物道法伤害提高<color=#35ab6c>1600</color>点",},
{level=3,consume_item_num=8,param2=2000,skill_des="宠物道法伤害提高<color=#35ab6c>2000</color>点",},
{level=4,param2=2400,skill_des="宠物道法伤害提高<color=#35ab6c>2400</color>点",},
{level=5,consume_item_num=12,param2=2800,skill_des="宠物道法伤害提高<color=#35ab6c>2800</color>点",},
{level=6,param2=3200,skill_des="宠物道法伤害提高<color=#35ab6c>3200</color>点",},
{level=7,param2=3600,skill_des="宠物道法伤害提高<color=#35ab6c>3600</color>点",},
{level=8,consume_item_num=16,param2=4000,skill_des="宠物道法伤害提高<color=#35ab6c>4000</color>点",},
{level=9,consume_item_num=20,param2=4400,skill_des="宠物道法伤害提高<color=#35ab6c>4400</color>点",},
{level=10,consume_item_num=99,param2=4800,skill_des="宠物道法伤害提高<color=#35ab6c>4800</color>点",},
{skill_id=82,skill_name="反伤精进·极",},
{level=1,param2=4,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>4</color>点",},
{level=2,consume_item_num=4,param2=5.3,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>5.3</color>点",},
{level=3,param2=6.7,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>6.7</color>点",},
{level=4,consume_item_num=8,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>8</color>点",},
{level=5,param2=9.3,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>9.3</color>点",},
{level=6,consume_item_num=12,param2=10.7,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>10.7</color>点",},
{level=7,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>12</color>点",},
{level=8,consume_item_num=16,param2=13.3,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>13.3</color>点",},
{level=9,consume_item_num=20,param2=14.7,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>14.7</color>点",},
{level=10,consume_item_num=99,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，反弹伤害提升<color=#35ab6c>16</color>点",},
{skill_id=83,skill_name="借机·极",consume_item=27719,consume_item_num=0,skill_type=3,param1="lianji_per",skill_icon=1015,skill_des="主人连击率提高<color=#35ab6c>0%</color>",},
{level=1,param2=150,skill_des="主人连击率提高<color=#35ab6c>1.5%</color>",},
{level=2,consume_item_num=3,param2=200,skill_des="主人连击率提高<color=#35ab6c>2%</color>",},
{skill_id=83,skill_name="借机·极",param1="lianji_per",skill_icon=1015,skill_des="主人连击率提高<color=#35ab6c>2.5%</color>",},
{level=4,param2=300,skill_des="主人连击率提高<color=#35ab6c>3%</color>",},
{level=5,consume_item_num=9,param2=350,skill_des="主人连击率提高<color=#35ab6c>3.5%</color>",},
{level=6,param2=400,skill_des="主人连击率提高<color=#35ab6c>4%</color>",},
{level=7,consume_item_num=12,param2=450,skill_des="主人连击率提高<color=#35ab6c>4.5%</color>",},
{level=8,param2=500,skill_des="主人连击率提高<color=#35ab6c>5%</color>",},
{level=9,consume_item_num=15,param2=550,skill_des="主人连击率提高<color=#35ab6c>5.5%</color>",},
{level=10,consume_item_num=99,param2=600,skill_des="主人连击率提高<color=#35ab6c>6%</color>",},
{skill_id=84,skill_name="穷追猛击",consume_item=27720,consume_item_num=0,skill_type=13,param1=0,skill_icon=1016,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>0%</color>的伤害",},
{level=1,param2=300,attack_power=3400,defence_power=3400,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>3%</color>的伤害",},
{level=2,consume_item_num=2,param2=400,attack_power=4500,defence_power=4500,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>4%</color>的伤害",},
{level=3,param2=500,attack_power=5600,defence_power=5600,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>5%</color>的伤害",},
{level=4,consume_item_num=4,param2=600,attack_power=6700,defence_power=6700,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>6%</color>的伤害",},
{skill_id=84,skill_name="穷追猛击",skill_type=13,param1=5000,param2=700,skill_icon=1016,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>7%</color>的伤害",},
{level=6,param2=800,attack_power=8900,defence_power=8900,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>8%</color>的伤害",},
{level=7,param2=900,attack_power=10000,defence_power=10000,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>9%</color>的伤害",},
{level=8,consume_item_num=8,param2=1000,attack_power=11100,defence_power=11100,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>10%</color>的伤害",},
{level=9,consume_item_num=10,param2=1100,attack_power=12200,defence_power=12200,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>11%</color>的伤害",},
{level=10,consume_item_num=99,param2=1200,attack_power=13400,defence_power=13400,skill_des="主人<color=#35ab6c>触发连击</color>时，有<color=#35ab6c>50%</color>的概率提升主人<color=#35ab6c>12%</color>的伤害",},
{skill_id=85,skill_name="同心·极",},
{skill_id=85,},
{skill_id=85,},
{skill_id=85,},
{skill_id=85,},
{skill_id=85,},
{skill_id=85,},
{skill_id=85,},
{skill_id=85,},
{skill_id=85,},
{skill_id=85,},
{skill_id=86,skill_name="掠夺精进·极",},
{level=1,consume_item_num=4,param2=4,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>4</color>点",},
{level=2,param2=5.3,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>5.3</color>点",},
{level=3,param2=6.7,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>6.7</color>点",},
{level=4,consume_item_num=8,param2=8,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>8</color>点",},
{level=5,param2=9.3,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>9.3</color>点",},
{level=6,consume_item_num=12,param2=10.7,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>10.7</color>点",},
{level=7,param2=12,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>12</color>点",},
{level=8,consume_item_num=16,param2=13.3,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>13.3</color>点",},
{level=9,consume_item_num=20,param2=14.7,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>14.7</color>点",},
{level=10,consume_item_num=99,param2=16,skill_des="宠物每升<color=#35ab6c>1</color>级，生命窃取提升<color=#35ab6c>16</color>点",},
{skill_id=87,skill_name="巧体·极",consume_item=27719,consume_item_num=0,skill_type=3,param1="shanbi_per",skill_icon=1017,skill_des="主人闪避率提高<color=#35ab6c>0%</color>",},
{level=1,consume_item_num=3,param2=80,skill_des="主人闪避率提高<color=#35ab6c>0.8%</color>",},
{level=2,param2=100,skill_des="主人闪避率提高<color=#35ab6c>1%</color>",},
{level=3,param2=130,skill_des="主人闪避率提高<color=#35ab6c>1.3%</color>",},
{skill_id=87,skill_name="巧体·极",param1="shanbi_per",param2=150,skill_icon=1017,skill_des="主人闪避率提高<color=#35ab6c>1.5%</color>",},
{level=5,consume_item_num=9,param2=180,skill_des="主人闪避率提高<color=#35ab6c>1.8%</color>",},
{level=6,param2=200,skill_des="主人闪避率提高<color=#35ab6c>2%</color>",},
{level=7,consume_item_num=12,param2=230,skill_des="主人闪避率提高<color=#35ab6c>2.3%</color>",},
{level=8,param2=250,skill_des="主人闪避率提高<color=#35ab6c>2.5%</color>",},
{level=9,consume_item_num=15,param2=280,skill_des="主人闪避率提高<color=#35ab6c>2.8%</color>",},
{level=10,consume_item_num=99,param2=300,skill_des="主人闪避率提高<color=#35ab6c>3%</color>",},
{skill_id=88,skill_name="阴阳道法",skill_type=21,param1=300,param3=60000,skill_icon=1018,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>3%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=1,consume_item_num=2,param2=15000,attack_power=3400,defence_power=3400,},
{level=2,consume_item_num=2,param1=400,attack_power=4500,defence_power=4500,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>4%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=3,consume_item_num=4,param1=500,attack_power=5600,defence_power=5600,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>5%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=4,param1=600,attack_power=6700,defence_power=6700,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>6%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=5,param1=700,attack_power=7800,defence_power=7800,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>7%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{skill_id=88,skill_name="阴阳道法",skill_type=21,param1=800,param2=15000,param3=60000,skill_icon=1018,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>8%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=7,consume_item_num=8,param1=900,attack_power=10000,defence_power=10000,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>9%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=8,param1=1000,attack_power=11100,defence_power=11100,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>10%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=9,consume_item_num=10,param1=1100,attack_power=12200,defence_power=12200,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>11%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=10,consume_item_num=99,param1=1200,attack_power=13400,defence_power=13400,skill_des="主人<color=#35ab6c>触发闪避</color>时，主人生命回复比例提升<color=#35ab6c>12%</color>，持续<color=#35ab6c>15</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{skill_id=89,skill_name="同心·仙",},
{skill_id=89,skill_name="同心·仙",level=1,consume_item_num=5,},
{level=2,param2=50000,skill_des="宠物生命提高<color=#35ab6c>50000</color>点",},
{level=3,consume_item_num=10,param2=62500,skill_des="宠物生命提高<color=#35ab6c>62500</color>点",},
{level=4,param2=75000,skill_des="宠物生命提高<color=#35ab6c>75000</color>点",},
{level=5,param2=87500,skill_des="宠物生命提高<color=#35ab6c>87500</color>点",},
{level=6,consume_item_num=15,param2=100000,skill_des="宠物生命提高<color=#35ab6c>100000</color>点",},
{level=7,param2=112500,skill_des="宠物生命提高<color=#35ab6c>112500</color>点",},
{level=8,consume_item_num=20,param2=125000,skill_des="宠物生命提高<color=#35ab6c>125000</color>点",},
{level=9,consume_item_num=25,param2=137500,skill_des="宠物生命提高<color=#35ab6c>137500</color>点",},
{level=10,consume_item_num=99,param2=150000,skill_des="宠物生命提高<color=#35ab6c>150000</color>点",},
{skill_id=90,skill_name="共生·仙",},
{skill_id=90,skill_name="共生·仙",level=1,consume_item_num=5,},
{level=2,param2=560,skill_des="宠物生命提高<color=#35ab6c>5.6%</color>",},
{level=3,param2=690,skill_des="宠物生命提高<color=#35ab6c>6.9%</color>",},
{level=4,consume_item_num=10,param2=830,skill_des="宠物生命提高<color=#35ab6c>8.3%</color>",},
{level=5,param2=970,skill_des="宠物生命提高<color=#35ab6c>9.7%</color>",},
{level=6,consume_item_num=15,param2=1110,skill_des="宠物生命提高<color=#35ab6c>11.1%</color>",},
{level=7,param2=1250,skill_des="宠物生命提高<color=#35ab6c>12.5%</color>",},
{level=8,consume_item_num=20,param2=1390,skill_des="宠物生命提高<color=#35ab6c>13.9%</color>",},
{level=9,consume_item_num=25,param2=1530,skill_des="宠物生命提高<color=#35ab6c>15.3%</color>",},
{skill_id=90,skill_name="共生·仙",param2=1670,skill_des="宠物生命提高<color=#35ab6c>16.7%</color>",},
{skill_id=91,skill_name="巧体·仙",},
{level=1,consume_item_num=4,param2=100,skill_des="主人闪避率提高<color=#35ab6c>1%</color>",},
{level=2,param2=130,skill_des="主人闪避率提高<color=#35ab6c>1.3%</color>",},
{level=3,consume_item_num=8,param2=170,skill_des="主人闪避率提高<color=#35ab6c>1.7%</color>",},
{level=4,param2=200,skill_des="主人闪避率提高<color=#35ab6c>2%</color>",},
{skill_id=91,skill_name="巧体·仙",level=5,},
{level=6,param2=270,skill_des="主人闪避率提高<color=#35ab6c>2.7%</color>",},
{skill_id=91,skill_name="巧体·仙",level=7,consume_item_num=16,},
{level=8,param2=330,skill_des="主人闪避率提高<color=#35ab6c>3.3%</color>",},
{level=9,consume_item_num=20,param2=370,skill_des="主人闪避率提高<color=#35ab6c>3.7%</color>",},
{skill_id=91,skill_name="巧体·仙",param2=400,skill_des="主人闪避率提高<color=#35ab6c>4%</color>",},
{skill_id=92,skill_name="回溯",consume_item=27720,consume_item_num=0,skill_type=30,param1=0,skill_icon=1019,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>0%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=1,consume_item_num=3,param1=15000,attack_power=5000,defence_power=5000,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>150%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=2,param1=20000,attack_power=6700,defence_power=6700,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>200%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{skill_id=92,skill_name="回溯",skill_type=30,param1=25000,param2=150000,param3=2500,skill_icon=1019,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>250%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=4,param1=30000,attack_power=10000,defence_power=10000,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>300%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=5,consume_item_num=9,param1=35000,attack_power=11700,defence_power=11700,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>350%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=6,param1=40000,attack_power=13400,defence_power=13400,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>400%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=7,consume_item_num=12,param1=45000,attack_power=15000,defence_power=15000,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>450%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=8,param1=50000,attack_power=16700,defence_power=16700,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>500%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=9,consume_item_num=15,param1=55000,attack_power=18400,defence_power=18400,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>550%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{level=10,consume_item_num=99,param1=60000,attack_power=20000,defence_power=20000,skill_des="主人生命低于<color=#35ab6c>75%</color>且<color=#35ab6c>触发闪避</color>时，给主人回复生命值，回复量相当于宠物面板攻击的<color=#35ab6c>600%</color>，回复量最多不超过主人生命上限的<color=#35ab6c>25%</color>，每隔<color=#35ab6c>150</color>秒最多触发1次",},
{skill_id=93,skill_name="玄法·仙",},
{level=1,param2=1500,skill_des="宠物道法伤害提高<color=#35ab6c>1500</color>点",},
{level=2,consume_item_num=5,param2=2000,skill_des="宠物道法伤害提高<color=#35ab6c>2000</color>点",},
{level=3,param2=2500,skill_des="宠物道法伤害提高<color=#35ab6c>2500</color>点",},
{skill_id=93,skill_name="玄法·仙",level=4,consume_item_num=10,},
{level=5,consume_item_num=15,param2=3500,skill_des="宠物道法伤害提高<color=#35ab6c>3500</color>点",},
{level=6,param2=4000,skill_des="宠物道法伤害提高<color=#35ab6c>4000</color>点",},
{level=7,consume_item_num=20,param2=4500,skill_des="宠物道法伤害提高<color=#35ab6c>4500</color>点",},
{level=8,param2=5000,skill_des="宠物道法伤害提高<color=#35ab6c>5000</color>点",},
{level=9,consume_item_num=25,param2=5500,skill_des="宠物道法伤害提高<color=#35ab6c>5500</color>点",},
{level=10,consume_item_num=99,param2=6000,skill_des="宠物道法伤害提高<color=#35ab6c>6000</color>点",},
{skill_id=94,skill_name="均衡·仙",},
{skill_id=94,skill_name="均衡·仙",level=1,consume_item_num=5,},
{level=2,param1=280,skill_des="宠物全属性提高<color=#35ab6c>2.8%</color>",},
{level=3,consume_item_num=10,param1=350,skill_des="宠物全属性提高<color=#35ab6c>3.5%</color>",},
{level=4,param1=420,skill_des="宠物全属性提高<color=#35ab6c>4.2%</color>",},
{level=5,consume_item_num=15,param1=490,skill_des="宠物全属性提高<color=#35ab6c>4.9%</color>",},
{level=6,param1=560,skill_des="宠物全属性提高<color=#35ab6c>5.6%</color>",},
{level=7,param1=630,skill_des="宠物全属性提高<color=#35ab6c>6.3%</color>",},
{level=8,consume_item_num=20,param1=690,skill_des="宠物全属性提高<color=#35ab6c>6.9%</color>",},
{level=9,consume_item_num=25,param1=760,skill_des="宠物全属性提高<color=#35ab6c>7.6%</color>",},
{level=10,consume_item_num=99,param1=830,skill_des="宠物全属性提高<color=#35ab6c>8.3%</color>",},
{skill_id=95,skill_name="寂灭·仙",consume_item_num=0,skill_type=3,param1="shanghai_quan_jc_per",skill_icon=1020,skill_des="主人全能增伤提高<color=#35ab6c>0%</color>",},
{level=1,param2=100,skill_des="主人全能增伤提高<color=#35ab6c>1%</color>",},
{level=2,consume_item_num=5,param2=130,skill_des="主人全能增伤提高<color=#35ab6c>1.3%</color>",},
{level=3,param2=170,skill_des="主人全能增伤提高<color=#35ab6c>1.7%</color>",},
{level=4,consume_item_num=10,param2=200,skill_des="主人全能增伤提高<color=#35ab6c>2%</color>",},
{level=5,param2=230,skill_des="主人全能增伤提高<color=#35ab6c>2.3%</color>",},
{level=6,consume_item_num=15,param2=270,skill_des="主人全能增伤提高<color=#35ab6c>2.7%</color>",},
{level=7,param2=300,skill_des="主人全能增伤提高<color=#35ab6c>3%</color>",},
{level=8,consume_item_num=20,param2=330,skill_des="主人全能增伤提高<color=#35ab6c>3.3%</color>",},
{level=9,consume_item_num=25,param2=370,skill_des="主人全能增伤提高<color=#35ab6c>3.7%</color>",},
{level=10,consume_item_num=99,param2=400,skill_des="主人全能增伤提高<color=#35ab6c>4%</color>",},
{skill_id=96,skill_name="道术·庇护",consume_item=27720,consume_item_num=0,skill_type=22,param1=10,skill_icon=1021,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>0%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=1,consume_item_num=3,param2=230,attack_power=5000,defence_power=5000,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>2.3%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=2,param2=300,attack_power=6700,defence_power=6700,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>3%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{skill_id=96,skill_name="道术·庇护",level=3,consume_item=27720,skill_type=22,param1=10,param2=380,param3=5000,param4=30000,attack_power=8400,defence_power=8400,skill_icon=1021,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>3.8%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=4,param2=450,attack_power=10000,defence_power=10000,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>4.5%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=5,consume_item_num=9,param2=530,attack_power=11700,defence_power=11700,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>5.3%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=6,param2=600,attack_power=13400,defence_power=13400,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>6%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=7,consume_item_num=12,param2=680,attack_power=15000,defence_power=15000,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>6.8%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=8,param2=750,attack_power=16700,defence_power=16700,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>7.5%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=9,consume_item_num=15,param2=830,attack_power=18400,defence_power=18400,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>8.3%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=10,consume_item_num=99,param2=900,attack_power=20000,defence_power=20000,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，给主人提升<color=#35ab6c>9%</color>全能减免，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{skill_id=97,skill_name="协力·仙",},
{skill_id=97,skill_name="协力·仙",level=1,consume_item_num=5,},
{level=2,param2=2000,skill_des="宠物攻击提高<color=#35ab6c>2000</color>点",},
{level=3,param2=2500,skill_des="宠物攻击提高<color=#35ab6c>2500</color>点",},
{level=4,consume_item_num=10,param2=3000,skill_des="宠物攻击提高<color=#35ab6c>3000</color>点",},
{level=5,consume_item_num=15,param2=3500,skill_des="宠物攻击提高<color=#35ab6c>3500</color>点",},
{level=6,param2=4000,skill_des="宠物攻击提高<color=#35ab6c>4000</color>点",},
{level=7,consume_item_num=20,param2=4500,skill_des="宠物攻击提高<color=#35ab6c>4500</color>点",},
{level=8,param2=5000,skill_des="宠物攻击提高<color=#35ab6c>5000</color>点",},
{level=9,consume_item_num=25,param2=5500,skill_des="宠物攻击提高<color=#35ab6c>5500</color>点",},
{level=10,consume_item_num=99,param2=6000,skill_des="宠物攻击提高<color=#35ab6c>6000</color>点",},
{skill_id=98,skill_name="合纵·仙",skill_icon=914,},
{level=1,param2=1250,skill_des="宠物攻击提高<color=#35ab6c>12.5%</color>",},
{level=2,consume_item_num=5,param2=1670,skill_des="宠物攻击提高<color=#35ab6c>16.7%</color>",},
{level=3,param2=2080,skill_des="宠物攻击提高<color=#35ab6c>20.8%</color>",},
{level=4,consume_item_num=10,param2=2500,skill_des="宠物攻击提高<color=#35ab6c>25%</color>",},
{level=5,param2=2920,skill_des="宠物攻击提高<color=#35ab6c>29.2%</color>",},
{level=6,consume_item_num=15,param2=3330,skill_des="宠物攻击提高<color=#35ab6c>33.3%</color>",},
{level=7,param2=3750,skill_des="宠物攻击提高<color=#35ab6c>37.5%</color>",},
{level=8,consume_item_num=20,param2=4170,skill_des="宠物攻击提高<color=#35ab6c>41.7%</color>",},
{level=9,consume_item_num=25,param2=4580,skill_des="宠物攻击提高<color=#35ab6c>45.8%</color>",},
{level=10,consume_item_num=99,param2=5000,skill_des="宠物攻击提高<color=#35ab6c>50%</color>",},
{skill_id=99,skill_name="狂怒·仙",consume_item=27719,consume_item_num=0,skill_type=3,param1="baoji_per",skill_icon=1014,skill_des="主人暴击率提高<color=#35ab6c>0%</color>",},
{level=1,param2=200,skill_des="主人暴击率提高<color=#35ab6c>2%</color>",},
{level=2,consume_item_num=4,param2=270,skill_des="主人暴击率提高<color=#35ab6c>2.7%</color>",},
{level=3,param2=330,skill_des="主人暴击率提高<color=#35ab6c>3.3%</color>",},
{level=4,consume_item_num=8,param2=400,skill_des="主人暴击率提高<color=#35ab6c>4%</color>",},
{level=5,consume_item_num=12,param2=470,skill_des="主人暴击率提高<color=#35ab6c>4.7%</color>",},
{level=6,param2=530,skill_des="主人暴击率提高<color=#35ab6c>5.3%</color>",},
{level=7,param2=600,skill_des="主人暴击率提高<color=#35ab6c>6%</color>",},
{level=8,consume_item_num=16,param2=670,skill_des="主人暴击率提高<color=#35ab6c>6.7%</color>",},
{level=9,consume_item_num=20,param2=730,skill_des="主人暴击率提高<color=#35ab6c>7.3%</color>",},
{level=10,consume_item_num=99,param2=800,skill_des="主人暴击率提高<color=#35ab6c>8%</color>",},
{skill_id=100,skill_name="怒意决",consume_item=27720,consume_item_num=0,skill_type=23,param1=0,skill_icon=1022,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>0%</color>暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=1,consume_item_num=3,param1=450,attack_power=5000,defence_power=5000,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>4.5%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=2,param1=600,attack_power=6700,defence_power=6700,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>6%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{skill_id=100,skill_name="怒意决",level=3,consume_item=27720,skill_type=23,param1=750,param2=5000,param3=30000,attack_power=8400,defence_power=8400,skill_icon=1022,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>7.5%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=4,param1=900,attack_power=10000,defence_power=10000,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>9%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=5,param1=1050,attack_power=11700,defence_power=11700,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>10.5%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=6,consume_item_num=9,param1=1200,attack_power=13400,defence_power=13400,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>12%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=7,param1=1350,attack_power=15000,defence_power=15000,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>13.5%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=8,consume_item_num=12,param1=1500,attack_power=16700,defence_power=16700,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>15%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=9,consume_item_num=15,param1=1650,attack_power=18400,defence_power=18400,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>16.5%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",},
{level=10,consume_item_num=99,param1=1800,attack_power=20000,defence_power=20000,skill_des="主人<color=#35ab6c>触发暴击</color>时，额外给主人提升<color=#35ab6c>18%</color>的暴击伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>30</color>秒最多触发1次",}
},

passive_skill_meta_table_map={
[969]=1,	-- depth:1
[925]=1,	-- depth:1
[199]=1,	-- depth:1
[34]=1,	-- depth:1
[276]=1,	-- depth:1
[705]=925,	-- depth:2
[507]=276,	-- depth:2
[441]=507,	-- depth:3
[342]=309,	-- depth:1
[793]=309,	-- depth:1
[408]=243,	-- depth:1
[837]=793,	-- depth:2
[1057]=309,	-- depth:1
[672]=342,	-- depth:2
[23]=309,	-- depth:1
[474]=408,	-- depth:2
[12]=23,	-- depth:2
[10]=1,	-- depth:1
[617]=122,	-- depth:1
[606]=111,	-- depth:1
[5]=1,	-- depth:1
[345]=313,	-- depth:1
[346]=313,	-- depth:1
[89]=243,	-- depth:1
[551]=617,	-- depth:2
[540]=606,	-- depth:2
[4]=5,	-- depth:2
[72]=73,	-- depth:1
[312]=345,	-- depth:2
[6]=1,	-- depth:1
[639]=540,	-- depth:3
[7]=6,	-- depth:2
[205]=204,	-- depth:1
[11]=1,	-- depth:1
[221]=111,	-- depth:1
[749]=375,	-- depth:1
[246]=411,	-- depth:1
[177]=375,	-- depth:1
[247]=246,	-- depth:2
[412]=247,	-- depth:3
[881]=111,	-- depth:1
[155]=177,	-- depth:2
[676]=346,	-- depth:2
[8]=1,	-- depth:1
[144]=584,	-- depth:1
[280]=279,	-- depth:1
[675]=312,	-- depth:3
[133]=573,	-- depth:1
[9]=8,	-- depth:2
[67]=309,	-- depth:1
[1013]=111,	-- depth:1
[444]=279,	-- depth:1
[445]=280,	-- depth:2
[511]=445,	-- depth:3
[510]=444,	-- depth:2
[3]=1,	-- depth:1
[56]=243,	-- depth:1
[2]=3,	-- depth:2
[1068]=584,	-- depth:1
[45]=573,	-- depth:1
[477]=246,	-- depth:2
[478]=412,	-- depth:4
[446]=444,	-- depth:2
[285]=279,	-- depth:1
[709]=705,	-- depth:3
[708]=709,	-- depth:4
[707]=205,	-- depth:2
[706]=707,	-- depth:3
[682]=676,	-- depth:3
[451]=444,	-- depth:2
[681]=676,	-- depth:3
[281]=446,	-- depth:3
[680]=676,	-- depth:3
[678]=676,	-- depth:3
[677]=678,	-- depth:4
[450]=285,	-- depth:2
[710]=707,	-- depth:3
[449]=444,	-- depth:2
[284]=449,	-- depth:3
[277]=279,	-- depth:1
[278]=277,	-- depth:2
[283]=284,	-- depth:4
[282]=281,	-- depth:4
[448]=283,	-- depth:5
[447]=282,	-- depth:5
[679]=680,	-- depth:4
[711]=710,	-- depth:4
[713]=707,	-- depth:3
[476]=477,	-- depth:3
[206]=204,	-- depth:1
[207]=206,	-- depth:2
[208]=204,	-- depth:1
[209]=204,	-- depth:1
[210]=452,	-- depth:1
[479]=477,	-- depth:3
[480]=479,	-- depth:4
[227]=226,	-- depth:1
[475]=476,	-- depth:4
[481]=477,	-- depth:3
[712]=713,	-- depth:4
[19]=12,	-- depth:3
[244]=475,	-- depth:5
[245]=476,	-- depth:4
[482]=481,	-- depth:4
[483]=477,	-- depth:3
[484]=477,	-- depth:3
[248]=479,	-- depth:4
[249]=480,	-- depth:5
[250]=481,	-- depth:4
[251]=482,	-- depth:5
[252]=483,	-- depth:4
[253]=484,	-- depth:4
[714]=707,	-- depth:3
[715]=209,	-- depth:2
[286]=451,	-- depth:3
[414]=249,	-- depth:6
[674]=676,	-- depth:3
[352]=682,	-- depth:4
[353]=760,	-- depth:1
[357]=356,	-- depth:1
[587]=588,	-- depth:1
[515]=284,	-- depth:4
[516]=450,	-- depth:3
[517]=286,	-- depth:4
[576]=577,	-- depth:1
[379]=378,	-- depth:1
[351]=681,	-- depth:4
[518]=353,	-- depth:2
[554]=555,	-- depth:1
[543]=544,	-- depth:1
[521]=356,	-- depth:1
[522]=357,	-- depth:2
[409]=244,	-- depth:6
[410]=245,	-- depth:5
[418]=253,	-- depth:5
[417]=252,	-- depth:5
[416]=251,	-- depth:6
[413]=248,	-- depth:5
[386]=452,	-- depth:1
[350]=680,	-- depth:4
[349]=679,	-- depth:5
[348]=678,	-- depth:4
[415]=250,	-- depth:5
[673]=674,	-- depth:4
[443]=278,	-- depth:3
[643]=544,	-- depth:1
[642]=543,	-- depth:2
[442]=277,	-- depth:2
[310]=673,	-- depth:5
[311]=674,	-- depth:4
[508]=442,	-- depth:3
[314]=677,	-- depth:5
[315]=348,	-- depth:5
[316]=349,	-- depth:6
[317]=350,	-- depth:5
[318]=351,	-- depth:5
[319]=352,	-- depth:5
[621]=555,	-- depth:1
[620]=554,	-- depth:2
[509]=443,	-- depth:4
[203]=706,	-- depth:4
[610]=643,	-- depth:2
[609]=642,	-- depth:3
[512]=281,	-- depth:4
[343]=310,	-- depth:6
[344]=311,	-- depth:5
[513]=447,	-- depth:6
[514]=448,	-- depth:6
[347]=314,	-- depth:6
[202]=9,	-- depth:3
[20]=19,	-- depth:4
[200]=5,	-- depth:2
[68]=73,	-- depth:1
[1061]=1057,	-- depth:2
[128]=127,	-- depth:1
[970]=279,	-- depth:1
[1062]=1057,	-- depth:2
[1063]=1062,	-- depth:3
[1064]=1057,	-- depth:2
[1065]=1064,	-- depth:3
[1066]=1057,	-- depth:2
[1067]=1057,	-- depth:2
[15]=12,	-- depth:3
[69]=68,	-- depth:2
[936]=254,	-- depth:1
[934]=714,	-- depth:4
[933]=713,	-- depth:4
[932]=712,	-- depth:5
[931]=711,	-- depth:5
[930]=710,	-- depth:4
[929]=709,	-- depth:4
[928]=708,	-- depth:5
[927]=707,	-- depth:3
[926]=706,	-- depth:4
[14]=12,	-- depth:3
[935]=715,	-- depth:3
[150]=149,	-- depth:1
[70]=20,	-- depth:5
[972]=970,	-- depth:2
[16]=68,	-- depth:2
[95]=94,	-- depth:1
[1024]=760,	-- depth:1
[100]=419,	-- depth:1
[1058]=345,	-- depth:2
[1059]=1058,	-- depth:3
[1060]=1061,	-- depth:3
[201]=200,	-- depth:3
[77]=73,	-- depth:1
[971]=970,	-- depth:2
[76]=73,	-- depth:1
[74]=73,	-- depth:1
[71]=70,	-- depth:6
[980]=419,	-- depth:1
[979]=970,	-- depth:2
[116]=226,	-- depth:1
[117]=227,	-- depth:2
[978]=970,	-- depth:2
[977]=970,	-- depth:2
[976]=977,	-- depth:3
[975]=970,	-- depth:2
[974]=975,	-- depth:3
[973]=972,	-- depth:3
[75]=74,	-- depth:2
[892]=320,	-- depth:1
[138]=139,	-- depth:1
[182]=183,	-- depth:1
[18]=16,	-- depth:3
[42]=9,	-- depth:3
[41]=8,	-- depth:2
[13]=14,	-- depth:4
[40]=7,	-- depth:3
[39]=6,	-- depth:2
[38]=5,	-- depth:2
[37]=4,	-- depth:3
[36]=3,	-- depth:2
[35]=2,	-- depth:3
[17]=18,	-- depth:4
[33]=77,	-- depth:2
[32]=23,	-- depth:2
[838]=71,	-- depth:7
[31]=20,	-- depth:5
[160]=182,	-- depth:2
[803]=77,	-- depth:2
[30]=19,	-- depth:4
[802]=793,	-- depth:2
[29]=18,	-- depth:4
[28]=17,	-- depth:5
[27]=16,	-- depth:3
[800]=793,	-- depth:2
[26]=15,	-- depth:4
[801]=800,	-- depth:3
[25]=14,	-- depth:4
[24]=13,	-- depth:5
[21]=32,	-- depth:3
[839]=838,	-- depth:8
[22]=33,	-- depth:3
[799]=793,	-- depth:2
[161]=183,	-- depth:1
[794]=838,	-- depth:8
[840]=75,	-- depth:3
[795]=839,	-- depth:9
[796]=840,	-- depth:4
[44]=11,	-- depth:2
[166]=188,	-- depth:1
[43]=10,	-- depth:2
[848]=78,	-- depth:1
[847]=803,	-- depth:3
[797]=796,	-- depth:5
[845]=801,	-- depth:4
[844]=800,	-- depth:3
[843]=799,	-- depth:3
[842]=843,	-- depth:4
[841]=797,	-- depth:6
[798]=842,	-- depth:5
[846]=802,	-- depth:3
[581]=577,	-- depth:1
[1032]=1024,	-- depth:2
[553]=555,	-- depth:1
[580]=581,	-- depth:2
[579]=577,	-- depth:1
[552]=553,	-- depth:2
[578]=579,	-- depth:2
[556]=555,	-- depth:1
[557]=556,	-- depth:2
[574]=577,	-- depth:1
[1033]=1024,	-- depth:2
[1034]=1024,	-- depth:2
[575]=574,	-- depth:2
[559]=555,	-- depth:1
[561]=555,	-- depth:1
[560]=555,	-- depth:1
[558]=559,	-- depth:2
[1077]=1068,	-- depth:2
[548]=544,	-- depth:1
[1076]=1068,	-- depth:2
[1075]=1076,	-- depth:3
[1074]=1068,	-- depth:2
[1073]=1074,	-- depth:3
[1072]=1068,	-- depth:2
[1071]=1072,	-- depth:3
[519]=521,	-- depth:2
[520]=519,	-- depth:3
[1070]=1068,	-- depth:2
[1078]=1068,	-- depth:2
[1069]=1070,	-- depth:3
[549]=544,	-- depth:1
[523]=521,	-- depth:2
[524]=523,	-- depth:3
[525]=521,	-- depth:2
[526]=525,	-- depth:3
[488]=489,	-- depth:1
[527]=521,	-- depth:2
[528]=521,	-- depth:2
[529]=496,	-- depth:1
[541]=544,	-- depth:1
[542]=541,	-- depth:2
[545]=544,	-- depth:1
[546]=545,	-- depth:2
[547]=548,	-- depth:2
[582]=577,	-- depth:1
[583]=577,	-- depth:1
[613]=547,	-- depth:3
[585]=588,	-- depth:1
[882]=881,	-- depth:2
[883]=882,	-- depth:3
[884]=881,	-- depth:2
[885]=884,	-- depth:3
[886]=881,	-- depth:2
[887]=886,	-- depth:3
[859]=1079,	-- depth:1
[686]=687,	-- depth:1
[889]=881,	-- depth:2
[890]=881,	-- depth:2
[891]=881,	-- depth:2
[653]=654,	-- depth:1
[888]=889,	-- depth:3
[750]=749,	-- depth:2
[770]=760,	-- depth:1
[769]=760,	-- depth:1
[768]=760,	-- depth:1
[767]=768,	-- depth:2
[766]=760,	-- depth:1
[765]=766,	-- depth:2
[764]=760,	-- depth:1
[763]=764,	-- depth:2
[762]=760,	-- depth:1
[761]=762,	-- depth:2
[759]=749,	-- depth:2
[758]=749,	-- depth:2
[757]=749,	-- depth:2
[756]=757,	-- depth:3
[755]=749,	-- depth:2
[753]=749,	-- depth:2
[752]=753,	-- depth:3
[751]=750,	-- depth:3
[649]=643,	-- depth:2
[648]=549,	-- depth:2
[647]=548,	-- depth:2
[646]=613,	-- depth:4
[1017]=548,	-- depth:2
[1018]=1013,	-- depth:2
[1019]=1018,	-- depth:3
[1020]=1013,	-- depth:2
[1021]=1020,	-- depth:3
[1022]=1013,	-- depth:2
[1023]=1013,	-- depth:2
[1025]=356,	-- depth:1
[1026]=1025,	-- depth:2
[594]=588,	-- depth:1
[1027]=1025,	-- depth:2
[1028]=1027,	-- depth:3
[593]=588,	-- depth:1
[592]=588,	-- depth:1
[591]=592,	-- depth:2
[590]=588,	-- depth:1
[589]=590,	-- depth:2
[1029]=1025,	-- depth:2
[1030]=1029,	-- depth:3
[586]=585,	-- depth:2
[607]=541,	-- depth:2
[1031]=1032,	-- depth:3
[608]=542,	-- depth:3
[1015]=1013,	-- depth:2
[645]=546,	-- depth:3
[644]=545,	-- depth:2
[641]=608,	-- depth:4
[640]=607,	-- depth:3
[627]=561,	-- depth:2
[626]=560,	-- depth:2
[625]=559,	-- depth:2
[624]=558,	-- depth:3
[623]=557,	-- depth:3
[622]=556,	-- depth:2
[991]=947,	-- depth:1
[619]=553,	-- depth:2
[618]=552,	-- depth:3
[1014]=1015,	-- depth:3
[616]=649,	-- depth:3
[615]=648,	-- depth:3
[614]=647,	-- depth:3
[612]=645,	-- depth:4
[611]=644,	-- depth:3
[1016]=1017,	-- depth:3
[754]=755,	-- depth:3
[550]=616,	-- depth:4
[65]=56,	-- depth:2
[363]=528,	-- depth:3
[362]=527,	-- depth:3
[361]=526,	-- depth:4
[360]=525,	-- depth:3
[359]=524,	-- depth:4
[358]=523,	-- depth:3
[354]=519,	-- depth:3
[143]=139,	-- depth:1
[145]=149,	-- depth:1
[146]=145,	-- depth:2
[147]=149,	-- depth:1
[148]=147,	-- depth:2
[151]=149,	-- depth:1
[152]=151,	-- depth:2
[153]=149,	-- depth:1
[331]=694,	-- depth:1
[154]=149,	-- depth:1
[156]=161,	-- depth:2
[142]=139,	-- depth:1
[140]=139,	-- depth:1
[130]=127,	-- depth:1
[389]=390,	-- depth:1
[385]=378,	-- depth:1
[384]=378,	-- depth:1
[383]=378,	-- depth:1
[382]=383,	-- depth:2
[131]=127,	-- depth:1
[381]=378,	-- depth:1
[380]=381,	-- depth:2
[377]=378,	-- depth:1
[376]=377,	-- depth:2
[132]=127,	-- depth:1
[134]=139,	-- depth:1
[135]=134,	-- depth:2
[136]=139,	-- depth:1
[137]=136,	-- depth:2
[141]=140,	-- depth:2
[129]=130,	-- depth:2
[157]=156,	-- depth:3
[159]=750,	-- depth:3
[46]=45,	-- depth:2
[216]=215,	-- depth:1
[66]=56,	-- depth:2
[222]=226,	-- depth:1
[223]=222,	-- depth:2
[224]=226,	-- depth:1
[225]=224,	-- depth:2
[228]=544,	-- depth:1
[229]=228,	-- depth:2
[230]=226,	-- depth:1
[231]=226,	-- depth:1
[238]=237,	-- depth:1
[257]=258,	-- depth:1
[158]=159,	-- depth:4
[193]=194,	-- depth:1
[162]=161,	-- depth:2
[323]=324,	-- depth:1
[163]=162,	-- depth:3
[164]=161,	-- depth:2
[165]=381,	-- depth:2
[171]=193,	-- depth:2
[172]=194,	-- depth:1
[298]=364,	-- depth:1
[178]=156,	-- depth:3
[179]=157,	-- depth:4
[180]=158,	-- depth:5
[181]=159,	-- depth:4
[184]=162,	-- depth:3
[185]=163,	-- depth:4
[186]=164,	-- depth:3
[187]=165,	-- depth:3
[290]=291,	-- depth:1
[126]=127,	-- depth:1
[355]=520,	-- depth:4
[124]=127,	-- depth:1
[112]=222,	-- depth:2
[47]=46,	-- depth:3
[48]=45,	-- depth:2
[49]=134,	-- depth:2
[50]=45,	-- depth:2
[51]=50,	-- depth:3
[423]=422,	-- depth:1
[52]=574,	-- depth:2
[53]=136,	-- depth:2
[57]=56,	-- depth:2
[55]=143,	-- depth:2
[430]=771,	-- depth:1
[456]=390,	-- depth:1
[455]=389,	-- depth:2
[105]=106,	-- depth:1
[99]=94,	-- depth:1
[98]=94,	-- depth:1
[97]=94,	-- depth:1
[96]=97,	-- depth:2
[93]=94,	-- depth:1
[92]=93,	-- depth:2
[91]=94,	-- depth:1
[90]=91,	-- depth:2
[125]=126,	-- depth:2
[113]=223,	-- depth:3
[58]=57,	-- depth:3
[54]=45,	-- depth:2
[60]=90,	-- depth:3
[123]=124,	-- depth:2
[121]=231,	-- depth:2
[397]=595,	-- depth:1
[120]=230,	-- depth:2
[119]=229,	-- depth:3
[118]=228,	-- depth:2
[115]=225,	-- depth:3
[59]=60,	-- depth:4
[114]=224,	-- depth:2
[84]=83,	-- depth:1
[63]=56,	-- depth:2
[62]=91,	-- depth:2
[61]=62,	-- depth:3
[64]=63,	-- depth:3
[191]=194,	-- depth:1
[190]=194,	-- depth:1
[1044]=1035,	-- depth:1
[1043]=1035,	-- depth:1
[189]=190,	-- depth:2
[1042]=1043,	-- depth:2
[805]=804,	-- depth:1
[806]=805,	-- depth:2
[807]=804,	-- depth:1
[220]=215,	-- depth:1
[809]=804,	-- depth:1
[810]=809,	-- depth:2
[811]=804,	-- depth:1
[812]=811,	-- depth:2
[813]=804,	-- depth:1
[814]=804,	-- depth:1
[1041]=1035,	-- depth:1
[818]=422,	-- depth:1
[819]=818,	-- depth:2
[1040]=1041,	-- depth:2
[1039]=1035,	-- depth:1
[1038]=1039,	-- depth:2
[808]=807,	-- depth:2
[724]=716,	-- depth:1
[725]=716,	-- depth:1
[233]=237,	-- depth:1
[81]=83,	-- depth:1
[219]=215,	-- depth:1
[80]=83,	-- depth:1
[79]=80,	-- depth:2
[217]=215,	-- depth:1
[239]=237,	-- depth:1
[1037]=1035,	-- depth:1
[731]=819,	-- depth:3
[730]=731,	-- depth:4
[214]=215,	-- depth:1
[213]=214,	-- depth:2
[212]=215,	-- depth:1
[774]=818,	-- depth:2
[775]=774,	-- depth:3
[240]=239,	-- depth:2
[211]=212,	-- depth:2
[198]=194,	-- depth:1
[197]=194,	-- depth:1
[196]=194,	-- depth:1
[726]=716,	-- depth:1
[195]=196,	-- depth:2
[236]=237,	-- depth:1
[235]=236,	-- depth:2
[192]=191,	-- depth:2
[234]=233,	-- depth:2
[82]=81,	-- depth:2
[1045]=1035,	-- depth:1
[855]=848,	-- depth:2
[176]=198,	-- depth:2
[902]=892,	-- depth:2
[906]=818,	-- depth:2
[907]=906,	-- depth:3
[107]=106,	-- depth:1
[108]=107,	-- depth:2
[109]=106,	-- depth:1
[110]=106,	-- depth:1
[723]=724,	-- depth:2
[990]=110,	-- depth:2
[989]=980,	-- depth:2
[988]=980,	-- depth:2
[987]=988,	-- depth:3
[986]=980,	-- depth:2
[937]=936,	-- depth:2
[938]=937,	-- depth:3
[985]=986,	-- depth:3
[984]=980,	-- depth:2
[958]=1002,	-- depth:1
[951]=819,	-- depth:3
[950]=951,	-- depth:4
[981]=422,	-- depth:1
[946]=936,	-- depth:2
[945]=936,	-- depth:2
[901]=892,	-- depth:2
[944]=936,	-- depth:2
[942]=936,	-- depth:2
[941]=942,	-- depth:3
[940]=936,	-- depth:2
[939]=940,	-- depth:3
[982]=981,	-- depth:2
[983]=984,	-- depth:3
[943]=944,	-- depth:3
[1036]=1037,	-- depth:2
[900]=892,	-- depth:2
[898]=892,	-- depth:2
[175]=197,	-- depth:2
[174]=196,	-- depth:2
[85]=83,	-- depth:1
[173]=195,	-- depth:3
[86]=85,	-- depth:2
[87]=83,	-- depth:1
[170]=192,	-- depth:3
[849]=82,	-- depth:3
[850]=849,	-- depth:4
[851]=86,	-- depth:3
[852]=851,	-- depth:4
[853]=848,	-- depth:2
[854]=853,	-- depth:3
[856]=855,	-- depth:3
[857]=848,	-- depth:2
[858]=848,	-- depth:2
[169]=191,	-- depth:2
[897]=898,	-- depth:3
[896]=892,	-- depth:2
[895]=896,	-- depth:3
[894]=892,	-- depth:2
[893]=894,	-- depth:3
[104]=106,	-- depth:1
[899]=900,	-- depth:3
[103]=104,	-- depth:2
[101]=106,	-- depth:1
[88]=83,	-- depth:1
[167]=189,	-- depth:3
[168]=190,	-- depth:2
[863]=819,	-- depth:3
[862]=863,	-- depth:4
[102]=101,	-- depth:2
[722]=716,	-- depth:1
[218]=217,	-- depth:2
[720]=716,	-- depth:1
[325]=324,	-- depth:1
[326]=325,	-- depth:2
[327]=324,	-- depth:1
[328]=327,	-- depth:2
[329]=324,	-- depth:1
[330]=324,	-- depth:1
[721]=722,	-- depth:2
[322]=324,	-- depth:1
[336]=818,	-- depth:2
[601]=818,	-- depth:2
[600]=601,	-- depth:3
[369]=600,	-- depth:4
[370]=369,	-- depth:5
[568]=601,	-- depth:3
[567]=568,	-- depth:4
[387]=390,	-- depth:1
[337]=336,	-- depth:3
[388]=387,	-- depth:2
[321]=322,	-- depth:2
[634]=601,	-- depth:3
[292]=291,	-- depth:1
[660]=654,	-- depth:1
[659]=654,	-- depth:1
[658]=654,	-- depth:1
[657]=658,	-- depth:2
[656]=654,	-- depth:1
[655]=656,	-- depth:2
[633]=634,	-- depth:4
[293]=292,	-- depth:2
[652]=654,	-- depth:1
[651]=652,	-- depth:2
[295]=291,	-- depth:1
[296]=291,	-- depth:1
[297]=291,	-- depth:1
[303]=369,	-- depth:5
[304]=370,	-- depth:6
[294]=295,	-- depth:2
[666]=600,	-- depth:4
[391]=390,	-- depth:1
[393]=390,	-- depth:1
[494]=489,	-- depth:1
[493]=489,	-- depth:1
[492]=493,	-- depth:2
[491]=489,	-- depth:1
[490]=491,	-- depth:2
[453]=387,	-- depth:2
[454]=388,	-- depth:3
[495]=489,	-- depth:1
[487]=489,	-- depth:1
[457]=391,	-- depth:2
[458]=457,	-- depth:3
[459]=393,	-- depth:2
[460]=459,	-- depth:3
[461]=452,	-- depth:1
[462]=452,	-- depth:1
[469]=601,	-- depth:3
[486]=487,	-- depth:2
[392]=458,	-- depth:4
[501]=600,	-- depth:4
[436]=568,	-- depth:4
[394]=460,	-- depth:4
[395]=461,	-- depth:2
[396]=462,	-- depth:2
[402]=600,	-- depth:4
[403]=601,	-- depth:3
[535]=501,	-- depth:5
[534]=501,	-- depth:5
[502]=535,	-- depth:6
[420]=422,	-- depth:1
[424]=422,	-- depth:1
[425]=424,	-- depth:2
[426]=422,	-- depth:1
[427]=426,	-- depth:2
[428]=422,	-- depth:1
[429]=422,	-- depth:1
[435]=436,	-- depth:5
[421]=420,	-- depth:2
[667]=666,	-- depth:5
[468]=469,	-- depth:4
[689]=687,	-- depth:1
[261]=258,	-- depth:1
[262]=261,	-- depth:2
[684]=687,	-- depth:1
[685]=684,	-- depth:2
[688]=689,	-- depth:2
[699]=336,	-- depth:3
[700]=337,	-- depth:4
[256]=258,	-- depth:1
[693]=687,	-- depth:1
[692]=687,	-- depth:1
[260]=258,	-- depth:1
[255]=256,	-- depth:2
[242]=237,	-- depth:1
[270]=600,	-- depth:4
[259]=260,	-- depth:2
[690]=687,	-- depth:1
[271]=270,	-- depth:5
[289]=291,	-- depth:1
[691]=690,	-- depth:2
[264]=258,	-- depth:1
[263]=258,	-- depth:1
[718]=716,	-- depth:1
[288]=289,	-- depth:2
[717]=718,	-- depth:2
[241]=237,	-- depth:1
[719]=720,	-- depth:2
[1083]=1079,	-- depth:1
[1087]=1079,	-- depth:1
[703]=694,	-- depth:1
[1084]=1079,	-- depth:1
[1086]=1087,	-- depth:2
[1085]=1084,	-- depth:2
[399]=397,	-- depth:2
[1081]=1079,	-- depth:1
[697]=694,	-- depth:1
[698]=697,	-- depth:2
[401]=397,	-- depth:2
[701]=694,	-- depth:1
[702]=701,	-- depth:2
[398]=399,	-- depth:3
[400]=401,	-- depth:3
[1082]=1083,	-- depth:2
[671]=661,	-- depth:1
[952]=947,	-- depth:1
[696]=694,	-- depth:1
[367]=364,	-- depth:1
[371]=364,	-- depth:1
[372]=371,	-- depth:2
[373]=364,	-- depth:1
[374]=364,	-- depth:1
[572]=562,	-- depth:1
[571]=562,	-- depth:1
[570]=562,	-- depth:1
[569]=570,	-- depth:2
[566]=562,	-- depth:1
[1088]=1079,	-- depth:1
[565]=566,	-- depth:2
[948]=947,	-- depth:1
[949]=948,	-- depth:2
[563]=562,	-- depth:1
[695]=696,	-- depth:2
[704]=694,	-- depth:1
[953]=952,	-- depth:2
[954]=947,	-- depth:1
[955]=954,	-- depth:2
[956]=947,	-- depth:1
[957]=947,	-- depth:1
[564]=563,	-- depth:2
[404]=397,	-- depth:2
[531]=529,	-- depth:2
[538]=529,	-- depth:2
[505]=538,	-- depth:3
[504]=496,	-- depth:1
[503]=504,	-- depth:2
[500]=496,	-- depth:1
[499]=500,	-- depth:2
[498]=531,	-- depth:3
[497]=498,	-- depth:4
[440]=430,	-- depth:2
[737]=727,	-- depth:1
[736]=727,	-- depth:1
[735]=727,	-- depth:1
[734]=735,	-- depth:2
[733]=727,	-- depth:1
[732]=733,	-- depth:2
[729]=727,	-- depth:1
[728]=729,	-- depth:2
[464]=463,	-- depth:1
[465]=464,	-- depth:2
[466]=463,	-- depth:1
[473]=463,	-- depth:1
[472]=463,	-- depth:1
[471]=463,	-- depth:1
[470]=471,	-- depth:2
[506]=496,	-- depth:1
[439]=430,	-- depth:2
[438]=430,	-- depth:2
[437]=438,	-- depth:3
[537]=504,	-- depth:2
[536]=503,	-- depth:3
[533]=500,	-- depth:2
[532]=499,	-- depth:3
[366]=364,	-- depth:1
[992]=991,	-- depth:2
[993]=992,	-- depth:3
[994]=991,	-- depth:2
[995]=994,	-- depth:3
[996]=954,	-- depth:2
[997]=996,	-- depth:3
[539]=506,	-- depth:2
[998]=957,	-- depth:2
[1000]=991,	-- depth:2
[1001]=957,	-- depth:2
[530]=497,	-- depth:5
[1080]=1081,	-- depth:2
[405]=404,	-- depth:3
[406]=397,	-- depth:2
[407]=397,	-- depth:2
[431]=430,	-- depth:2
[432]=431,	-- depth:3
[433]=430,	-- depth:2
[434]=433,	-- depth:3
[999]=998,	-- depth:3
[365]=366,	-- depth:2
[368]=367,	-- depth:2
[597]=399,	-- depth:3
[301]=367,	-- depth:2
[302]=368,	-- depth:3
[467]=466,	-- depth:2
[305]=371,	-- depth:2
[306]=372,	-- depth:3
[638]=628,	-- depth:1
[637]=628,	-- depth:1
[636]=628,	-- depth:1
[635]=636,	-- depth:2
[782]=1002,	-- depth:1
[300]=366,	-- depth:2
[632]=628,	-- depth:1
[596]=398,	-- depth:4
[631]=632,	-- depth:2
[630]=628,	-- depth:1
[864]=859,	-- depth:2
[865]=864,	-- depth:3
[866]=859,	-- depth:2
[867]=866,	-- depth:3
[868]=859,	-- depth:2
[869]=859,	-- depth:2
[629]=630,	-- depth:2
[860]=859,	-- depth:2
[299]=365,	-- depth:3
[268]=265,	-- depth:1
[670]=661,	-- depth:1
[669]=661,	-- depth:1
[668]=669,	-- depth:2
[275]=265,	-- depth:1
[665]=661,	-- depth:1
[664]=665,	-- depth:2
[663]=661,	-- depth:1
[662]=663,	-- depth:2
[274]=265,	-- depth:1
[273]=265,	-- depth:1
[1089]=1079,	-- depth:1
[272]=273,	-- depth:2
[817]=815,	-- depth:1
[820]=815,	-- depth:1
[821]=820,	-- depth:2
[822]=815,	-- depth:1
[823]=822,	-- depth:2
[824]=815,	-- depth:1
[825]=815,	-- depth:1
[269]=268,	-- depth:2
[831]=875,	-- depth:1
[832]=831,	-- depth:2
[816]=817,	-- depth:2
[876]=875,	-- depth:1
[861]=860,	-- depth:3
[333]=696,	-- depth:2
[908]=903,	-- depth:1
[604]=406,	-- depth:3
[334]=697,	-- depth:2
[335]=698,	-- depth:3
[602]=404,	-- depth:3
[905]=903,	-- depth:1
[909]=908,	-- depth:2
[772]=771,	-- depth:1
[338]=701,	-- depth:2
[339]=702,	-- depth:3
[340]=703,	-- depth:2
[904]=905,	-- depth:2
[341]=704,	-- depth:2
[603]=405,	-- depth:4
[266]=265,	-- depth:1
[910]=903,	-- depth:1
[773]=772,	-- depth:2
[267]=266,	-- depth:2
[598]=400,	-- depth:4
[599]=401,	-- depth:3
[332]=695,	-- depth:3
[912]=903,	-- depth:1
[307]=373,	-- depth:2
[308]=374,	-- depth:2
[913]=903,	-- depth:1
[781]=771,	-- depth:1
[776]=771,	-- depth:1
[777]=776,	-- depth:2
[911]=910,	-- depth:2
[605]=407,	-- depth:3
[779]=771,	-- depth:1
[780]=771,	-- depth:1
[778]=779,	-- depth:2
[919]=875,	-- depth:1
[828]=826,	-- depth:1
[829]=826,	-- depth:1
[920]=919,	-- depth:2
[827]=828,	-- depth:2
[833]=826,	-- depth:1
[834]=833,	-- depth:2
[835]=826,	-- depth:1
[836]=826,	-- depth:1
[830]=829,	-- depth:2
[744]=876,	-- depth:2
[878]=875,	-- depth:1
[871]=875,	-- depth:1
[872]=871,	-- depth:2
[879]=875,	-- depth:1
[880]=875,	-- depth:1
[874]=875,	-- depth:1
[877]=878,	-- depth:2
[743]=744,	-- depth:3
[873]=874,	-- depth:2
[924]=919,	-- depth:2
[923]=919,	-- depth:2
[922]=919,	-- depth:2
[918]=919,	-- depth:2
[917]=918,	-- depth:3
[916]=919,	-- depth:2
[915]=916,	-- depth:3
[921]=922,	-- depth:3
[964]=876,	-- depth:2
[747]=738,	-- depth:1
[742]=738,	-- depth:1
[1005]=1093,	-- depth:1
[1006]=1005,	-- depth:2
[741]=742,	-- depth:2
[748]=738,	-- depth:1
[739]=738,	-- depth:1
[740]=739,	-- depth:2
[963]=964,	-- depth:3
[746]=738,	-- depth:1
[745]=746,	-- depth:2
[1094]=1093,	-- depth:1
[1091]=1093,	-- depth:1
[1096]=1093,	-- depth:1
[1098]=1093,	-- depth:1
[1097]=1098,	-- depth:2
[1095]=1096,	-- depth:2
[1092]=1091,	-- depth:2
[1100]=1093,	-- depth:1
[967]=964,	-- depth:3
[960]=964,	-- depth:3
[961]=964,	-- depth:3
[962]=961,	-- depth:4
[965]=964,	-- depth:3
[966]=965,	-- depth:4
[968]=964,	-- depth:3
[1003]=1005,	-- depth:2
[1004]=1003,	-- depth:3
[1007]=1005,	-- depth:2
[1008]=1007,	-- depth:3
[959]=958,	-- depth:2
[1009]=1005,	-- depth:2
[1011]=1005,	-- depth:2
[1012]=1005,	-- depth:2
[788]=920,	-- depth:3
[787]=788,	-- depth:4
[1050]=1049,	-- depth:1
[1099]=1093,	-- depth:1
[1010]=1009,	-- depth:3
[789]=782,	-- depth:2
[786]=782,	-- depth:2
[785]=786,	-- depth:3
[783]=782,	-- depth:2
[790]=789,	-- depth:3
[1055]=1049,	-- depth:1
[792]=782,	-- depth:2
[1056]=1049,	-- depth:1
[1047]=1049,	-- depth:1
[1048]=1047,	-- depth:2
[1051]=1049,	-- depth:1
[1052]=1051,	-- depth:2
[1053]=1049,	-- depth:1
[1054]=1053,	-- depth:2
[791]=782,	-- depth:2
[784]=783,	-- depth:3
},
zs_skill={
{trigger_param1=10,handle_param1=501,skill_icon=252,},
{level=1,},
{level=2,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>257.5%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=500,defence_power=500,},
{level=3,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>265%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=1000,defence_power=1000,},
{level=4,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>272.5%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=1500,defence_power=1500,},
{level=5,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>280%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=2000,defence_power=2000,},
{level=6,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>287.5%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=2500,defence_power=2500,},
{level=7,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>295%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=3000,defence_power=3000,},
{level=8,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>302.5%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=3500,defence_power=3500,},
{level=9,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>310%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=4000,defence_power=4000,},
{level=10,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>317.5%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=4500,defence_power=4500,},
{level=11,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>325%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",attack_power=5000,defence_power=5000,},
{skill_id=2,skill_name="大难临头",trigger_param1=425,handle_param1=5000,handle_param2=20000,skill_icon=420,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>200%</color>的伤害",},
{level=1,},
{level=2,handle_param2=21000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>210%</color>的伤害",attack_power=500,defence_power=500,},
{level=3,handle_param2=22000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>220%</color>的伤害",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=23000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>230%</color>的伤害",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=24000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>240%</color>的伤害",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=25000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>250%</color>的伤害",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=26000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>260%</color>的伤害",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=27000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>270%</color>的伤害",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=28000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>280%</color>的伤害",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=29000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>290%</color>的伤害",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=30000,skill_des="释放天罚时，有<color=#35ab6c>50%</color>概率额外追加<color=#35ab6c>300%</color>的伤害",attack_power=5000,defence_power=5000,},
{skill_id=3,skill_name="鸿运当头",cd=60000,trigger_type=3,handle_type=3,handle_param1=2000,handle_param2="shanghai_quan_jc_per",handle_param3=800,handle_param4=5000,skill_icon=450,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>8%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=1,},
{level=2,handle_param3=840,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>8.4%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=500,defence_power=500,},
{level=3,handle_param3=880,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>8.8%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=1000,defence_power=1000,},
{level=4,handle_param3=920,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>9.2%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=1500,defence_power=1500,},
{level=5,handle_param3=960,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>9.6%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=2000,defence_power=2000,},
{level=6,handle_param3=1000,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>10%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=2500,defence_power=2500,},
{level=7,handle_param3=1040,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>10.4%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=3000,defence_power=3000,},
{level=8,handle_param3=1080,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>10.8%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=3500,defence_power=3500,},
{level=9,handle_param3=1120,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>11.2%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=4000,defence_power=4000,},
{level=10,handle_param3=1160,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>11.6%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=4500,defence_power=4500,},
{level=11,handle_param3=1200,skill_des="主人攻击时，有<color=#35ab6c>20%</color>增加主人<color=#35ab6c>12%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=5000,defence_power=5000,},
{skill_id=4,skill_name="暴击免疫",cd=60000,trigger_type=4,handle_type=4,handle_param1=5000,handle_param2="kangbao_per",handle_param3=500,handle_param4=3000,skill_icon=912,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>5%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",},
{level=1,},
{level=2,handle_param3=540,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>5.4%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=500,defence_power=500,},
{level=3,handle_param3=580,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>5.8%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=1000,defence_power=1000,},
{level=4,handle_param3=620,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>6.2%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=1500,defence_power=1500,},
{level=5,handle_param3=660,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>6.6%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=2000,defence_power=2000,},
{level=6,handle_param3=700,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>7%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=2500,defence_power=2500,},
{level=7,handle_param3=740,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>7.4%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=3000,defence_power=3000,},
{level=8,handle_param3=780,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>7.8%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=3500,defence_power=3500,},
{level=9,handle_param3=820,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>8.2%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=4000,defence_power=4000,},
{level=10,handle_param3=860,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>8.6%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=4500,defence_power=4500,},
{level=11,handle_param3=900,skill_des="主人受到暴击时，有<color=#35ab6c>50%</color>概率免除此次伤害，并给主人附加<color=#35ab6c>9%</color>的抗暴率，持续<color=#35ab6c>3</color>秒",attack_power=5000,defence_power=5000,},
{skill_id=5,skill_name="恩泽",trigger_type=5,handle_param2="shanghai_quan_jm_per",handle_param3=800,skill_icon=9,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>8</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=1,},
{level=2,handle_param3=840,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>8.4</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=500,defence_power=500,},
{level=3,handle_param3=880,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>8.8</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=1000,defence_power=1000,},
{level=4,handle_param3=920,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>9.2</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=1500,defence_power=1500,},
{level=5,handle_param3=960,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>9.6</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=2000,defence_power=2000,},
{level=6,handle_param3=1000,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>10</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=2500,defence_power=2500,},
{level=7,handle_param3=1040,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>10.4</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=3000,defence_power=3000,},
{level=8,handle_param3=1080,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>10.8</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=3500,defence_power=3500,},
{level=9,handle_param3=1120,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>11.2</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=4000,defence_power=4000,},
{level=10,handle_param3=1160,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>11.6</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=4500,defence_power=4500,},
{level=11,handle_param3=1200,skill_des="主人每受到<color=#35ab6c>10</color>下攻击时，会给主人附加免伤效果，使主人受到的伤害减少<color=#35ab6c>12</color>%，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=5000,defence_power=5000,},
{skill_id=6,skill_name="极寒",trigger_type=2,trigger_param1=429,handle_type=11,handle_param2=2000,skill_icon=414,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>20%</color>",},
{level=1,},
{level=2,handle_param2=2200,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>22%</color>",attack_power=500,defence_power=500,},
{level=3,handle_param2=2400,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>24%</color>",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=2600,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>26%</color>",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=2800,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>28%</color>",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=3000,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>30%</color>",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=3200,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>32%</color>",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=3400,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>34%</color>",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=3600,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>36%</color>",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=3800,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>38%</color>",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=4000,skill_des="释放北极摇滚时，触发冰冻的概率额外提升<color=#35ab6c>40%</color>",attack_power=5000,defence_power=5000,},
{skill_id=7,skill_name="追猎",trigger_param1=10,handle_param1=502,skill_icon=405,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>240%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",},
{level=1,},
{level=2,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>247%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=500,defence_power=500,},
{level=3,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>254%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=1000,defence_power=1000,},
{level=4,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>261%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=1500,defence_power=1500,},
{level=5,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>268%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=2000,defence_power=2000,},
{level=6,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>275%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=2500,defence_power=2500,},
{level=7,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>282%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=3000,defence_power=3000,},
{level=8,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>289%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=3500,defence_power=3500,},
{level=9,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>296%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=4000,defence_power=4000,},
{level=10,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>303%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=4500,defence_power=4500,},
{level=11,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>310%</color>的伤害，如果目标为首领怪物，则伤害额外提升<color=#35ab6c>20%</color>",attack_power=5000,defence_power=5000,},
{skill_id=8,skill_name="月之庇护",trigger_type=7,handle_param1=1000,handle_param2="shanghai_quan_jm_per",handle_param3=500,skill_icon=1079,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",},
{level=1,},
{level=2,handle_param3=530,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>5.3%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=500,defence_power=500,},
{level=3,handle_param3=560,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>5.6%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=1000,defence_power=1000,},
{level=4,handle_param3=590,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>5.9%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=1500,defence_power=1500,},
{level=5,handle_param3=620,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>6.2%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=2000,defence_power=2000,},
{level=6,handle_param3=650,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>6.5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=2500,defence_power=2500,},
{level=7,handle_param3=680,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>6.8%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=3000,defence_power=3000,},
{level=8,handle_param3=710,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>7.1%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=3500,defence_power=3500,},
{level=9,handle_param3=740,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>7.4%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=4000,defence_power=4000,},
{level=10,handle_param3=770,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>7.7%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=4500,defence_power=4500,},
{level=11,handle_param3=800,skill_des="宠物攻击时，有<color=#35ab6c>10%</color>概率使主人受到的伤害减少<color=#35ab6c>8%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多释放1次",attack_power=5000,defence_power=5000,},
{skill_id=9,skill_name="古灵精怪",trigger_type=2,trigger_param1=432,handle_type=11,handle_param2=2000,skill_icon=1055,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>20%</color>",},
{level=1,},
{level=2,handle_param2=2200,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>22%</color>",attack_power=500,defence_power=500,},
{level=3,handle_param2=2400,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>24%</color>",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=2600,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>26%</color>",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=2800,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>28%</color>",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=3000,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>30%</color>",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=3200,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>32%</color>",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=3400,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>34%</color>",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=3600,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>36%</color>",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=3800,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>38%</color>",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=4000,skill_des="释放喵喵连击时，触发眩晕的概率额外提升<color=#35ab6c>40%</color>",attack_power=5000,defence_power=5000,},
{skill_id=10,skill_name="圣驱",trigger_type=8,handle_type=5,handle_param1=1000,handle_param2="shanghai_quan_jm_per",handle_param3=500,handle_param4=3000,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>10%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",},
{level=1,},
{level=2,handle_param1=1100,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>11%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=500,defence_power=500,},
{level=3,handle_param1=1200,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>12%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=1000,defence_power=1000,},
{level=4,handle_param1=1300,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>13%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=1500,defence_power=1500,},
{level=5,handle_param1=1400,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>14%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=2000,defence_power=2000,},
{level=6,handle_param1=1500,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>15%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=2500,defence_power=2500,},
{level=7,handle_param1=1600,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>16%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=3000,defence_power=3000,},
{level=8,handle_param1=1700,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>17%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=3500,defence_power=3500,},
{level=9,handle_param1=1800,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>18%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=4000,defence_power=4000,},
{level=10,handle_param1=1900,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>19%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=4500,defence_power=4500,},
{level=11,handle_param1=2000,skill_des="主人受到宠物主动技伤害时，有<color=#35ab6c>20%</color>的概率免疫此次受到的伤害及效果，并且使主人受到的伤害减少<color=#35ab6c>5%</color>，持续<color=#35ab6c>3</color>秒",attack_power=5000,defence_power=5000,},
{skill_id=11,skill_name="鼓舞",trigger_type=15,handle_type=13,handle_param1="gongji",handle_param2=1500,skill_icon=1025,skill_des="主战宠物的攻击力提高<color=#35ab6c>15%</color>",},
{level=1,},
{level=2,handle_param2=1575,skill_des="主战宠物的攻击力提高<color=#35ab6c>15.75%</color>",attack_power=500,defence_power=500,},
{level=3,handle_param2=1650,skill_des="主战宠物的攻击力提高<color=#35ab6c>16.5%</color>",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=1725,skill_des="主战宠物的攻击力提高<color=#35ab6c>17.25%</color>",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=1800,skill_des="主战宠物的攻击力提高<color=#35ab6c>18%</color>",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=1875,skill_des="主战宠物的攻击力提高<color=#35ab6c>18.75%</color>",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=1950,skill_des="主战宠物的攻击力提高<color=#35ab6c>19.5%</color>",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=2025,skill_des="主战宠物的攻击力提高<color=#35ab6c>20.25%</color>",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=2100,skill_des="主战宠物的攻击力提高<color=#35ab6c>21%</color>",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=2175,skill_des="主战宠物的攻击力提高<color=#35ab6c>21.75%</color>",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=2250,skill_des="主战宠物的攻击力提高<color=#35ab6c>22.5%</color>",attack_power=5000,defence_power=5000,},
{skill_id=12,skill_name="扇舞",trigger_type=9,trigger_param1=435,handle_type=2,handle_param2=12000,skill_icon=1040,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>120%</color>的伤害",},
{level=1,},
{level=2,handle_param2=12600,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>126%</color>的伤害",attack_power=500,defence_power=500,},
{level=3,handle_param2=13200,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>132%</color>的伤害",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=13800,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>138%</color>的伤害",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=14400,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>144%</color>的伤害",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=15000,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>150%</color>的伤害",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=15600,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>156%</color>的伤害",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=16200,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>162%</color>的伤害",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=16800,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>168%</color>的伤害",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=17400,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>174%</color>的伤害",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=18000,skill_des="释放飞花摘叶时，如果目标为首领怪物，则额外追加<color=#35ab6c>180%</color>的伤害",attack_power=5000,defence_power=5000,},
{skill_id=13,skill_name="月色",trigger_type=2,trigger_param1=436,handle_type=2,handle_param2=12000,skill_icon=1080,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>120%</color>",},
{level=1,},
{level=2,handle_param2=12600,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>126%</color>",attack_power=500,defence_power=500,},
{level=3,handle_param2=13200,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>132%</color>",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=13800,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>138%</color>",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=14400,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>144%</color>",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=15000,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>150%</color>",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=15600,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>156%</color>",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=16200,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>162%</color>",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=16800,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>168%</color>",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=17400,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>174%</color>",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=18000,skill_des="释放月蚀时，变身增伤提高<color=#35ab6c>180%</color>",attack_power=5000,defence_power=5000,},
{skill_id=14,skill_name="狐媚",trigger_param1=10,handle_param1=505,skill_icon=1061,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>260%</color>的伤害",},
{level=1,},
{level=2,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>268%</color>的伤害",attack_power=500,defence_power=500,},
{level=3,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>276%</color>的伤害",attack_power=1000,defence_power=1000,},
{level=4,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>284%</color>的伤害",attack_power=1500,defence_power=1500,},
{level=5,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>292%</color>的伤害",attack_power=2000,defence_power=2000,},
{level=6,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>300%</color>的伤害",attack_power=2500,defence_power=2500,},
{level=7,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>308%</color>的伤害",attack_power=3000,defence_power=3000,},
{level=8,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>316%</color>的伤害",attack_power=3500,defence_power=3500,},
{level=9,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>324%</color>的伤害",attack_power=4000,defence_power=4000,},
{level=10,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>332%</color>的伤害",attack_power=4500,defence_power=4500,},
{level=11,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标单体造成宠物攻击<color=#35ab6c>340%</color>的伤害",attack_power=5000,defence_power=5000,},
{skill_id=15,skill_name="激励",trigger_type=15,handle_type=13,handle_param1="gongji",handle_param2=2000,skill_icon=2031,skill_des="主战宠物的攻击力提高<color=#35ab6c>20%</color>",},
{level=1,},
{level=2,handle_param2=2100,skill_des="主战宠物的攻击力提高<color=#35ab6c>21%</color>",attack_power=500,defence_power=500,},
{level=3,handle_param2=2200,skill_des="主战宠物的攻击力提高<color=#35ab6c>22%</color>",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=2300,skill_des="主战宠物的攻击力提高<color=#35ab6c>23%</color>",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=2400,skill_des="主战宠物的攻击力提高<color=#35ab6c>24%</color>",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=2500,skill_des="主战宠物的攻击力提高<color=#35ab6c>25%</color>",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=2600,skill_des="主战宠物的攻击力提高<color=#35ab6c>26%</color>",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=2700,skill_des="主战宠物的攻击力提高<color=#35ab6c>27%</color>",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=2800,skill_des="主战宠物的攻击力提高<color=#35ab6c>28%</color>",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=2900,skill_des="主战宠物的攻击力提高<color=#35ab6c>29%</color>",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=3000,skill_des="主战宠物的攻击力提高<color=#35ab6c>30%</color>",attack_power=5000,defence_power=5000,},
{skill_id=16,skill_name="冰瀑",trigger_param1=10,handle_param1=507,skill_icon=1046,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>255%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",},
{level=1,},
{level=2,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>262.8%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=500,defence_power=500,},
{level=3,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>270.6%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=1000,defence_power=1000,},
{level=4,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>278.4%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=1500,defence_power=1500,},
{level=5,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>286.2%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=2000,defence_power=2000,},
{level=6,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>294%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=2500,defence_power=2500,},
{level=7,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>301.8%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=3000,defence_power=3000,},
{level=8,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>309.6%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=3500,defence_power=3500,},
{level=9,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>317.4%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=4000,defence_power=4000,},
{level=10,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>325.2%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=4500,defence_power=4500,},
{level=11,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>333%</color>的伤害，并有<color=#35ab6c>20%</color>的概率使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=5000,defence_power=5000,},
{skill_id=17,skill_name="净化",cd=60000,trigger_type=14,handle_type=6,handle_param1=2000,skill_icon=505,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>2</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=1,},
{level=2,handle_param1=2500,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>2.5</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=500,defence_power=500,},
{level=3,handle_param1=3000,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>3</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=1000,defence_power=1000,},
{level=4,handle_param1=3500,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>3.5</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=1500,defence_power=1500,},
{level=5,handle_param1=4000,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>4</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=2000,defence_power=2000,},
{level=6,handle_param1=4500,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>4.5</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=2500,defence_power=2500,},
{level=7,handle_param1=5000,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>5</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=3000,defence_power=3000,},
{level=8,handle_param1=5500,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>5.5</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=3500,defence_power=3500,},
{level=9,handle_param1=6000,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>6</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=4000,defence_power=4000,},
{level=10,handle_param1=6500,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>6.5</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=4500,defence_power=4500,},
{level=11,handle_param1=7000,skill_des="主人受到眩晕，冰冻，定身的影响时，立即解除该状态，并在<color=#35ab6c>7</color>秒内免疫同类效果，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=5000,defence_power=5000,},
{skill_id=18,skill_name="怒意加深",trigger_type=10,handle_type=2,handle_param2=15000,skill_icon=913,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>150%</color>的伤害",},
{level=1,},
{level=2,handle_param2=15750,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>157.5%</color>的伤害",attack_power=500,defence_power=500,},
{level=3,handle_param2=16500,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>165%</color>的伤害",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=17250,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>172.5%</color>的伤害",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=18000,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>180%</color>的伤害",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=18750,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>187.5%</color>的伤害",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=19500,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>195%</color>的伤害",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=20250,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>202.5%</color>的伤害",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=21000,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>210%</color>的伤害",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=21750,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>217.5%</color>的伤害",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=22500,skill_des="主人获得<color=#35ab6c>狂怒标记</color>时，使用宠物主动技能，额外追加<color=#35ab6c>225%</color>的伤害",attack_power=5000,defence_power=5000,},
{skill_id=19,skill_name="庇护",trigger_type=11,handle_param1=600,handle_param2=5000,skill_icon=531,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>6%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",},
{level=1,},
{level=2,handle_param1=630,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>6.3%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=500,defence_power=500,},
{level=3,handle_param1=660,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>6.6%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=1000,defence_power=1000,},
{level=4,handle_param1=690,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>6.9%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=1500,defence_power=1500,},
{level=5,handle_param1=720,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>7.2%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=2000,defence_power=2000,},
{level=6,handle_param1=750,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>7.5%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=2500,defence_power=2500,},
{level=7,handle_param1=780,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>7.8%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=3000,defence_power=3000,},
{level=8,handle_param1=810,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>8.1%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=3500,defence_power=3500,},
{level=9,handle_param1=840,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>8.4%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=4000,defence_power=4000,},
{level=10,handle_param1=870,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>8.7%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=4500,defence_power=4500,},
{level=11,handle_param1=900,skill_des="主人生命低于<color=#35ab6c>50%</color>时，会获得一个生命护盾，最大可吸收主人生命上限<color=#35ab6c>9%</color>的伤害，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=5000,defence_power=5000,},
{skill_id=20,skill_name="驱魔",trigger_param1=10,handle_param1=503,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>260%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",},
{level=1,},
{level=2,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>268%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=500,defence_power=500,},
{level=3,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>276%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=1000,defence_power=1000,},
{level=4,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>284%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=1500,defence_power=1500,},
{level=5,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>292%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=2000,defence_power=2000,},
{level=6,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>300%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=2500,defence_power=2500,},
{level=7,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>308%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=3000,defence_power=3000,},
{level=8,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>316%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=3500,defence_power=3500,},
{level=9,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>324%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=4000,defence_power=4000,},
{level=10,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>332%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=4500,defence_power=4500,},
{level=11,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对目标扇形范围区域造成宠物攻击<color=#35ab6c>340%</color>的伤害，如果目标为玩家，伤害额外提升<color=#35ab6c>30%</color>",attack_power=5000,defence_power=5000,},
{skill_id=21,skill_name="忘情",cd=60000,trigger_param1=10,handle_type=3,handle_param2="shanghai_quan_jc_per",handle_param3=1000,handle_param4=5000,skill_icon=907,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>10%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",},
{level=1,},
{level=2,handle_param3=1050,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>10.5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=500,defence_power=500,},
{level=3,handle_param3=1100,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>11%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=1000,defence_power=1000,},
{level=4,handle_param3=1150,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>11.5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=1500,defence_power=1500,},
{level=5,handle_param3=1200,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>12%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=2000,defence_power=2000,},
{level=6,handle_param3=1250,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>12.5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=2500,defence_power=2500,},
{level=7,handle_param3=1300,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>13%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=3000,defence_power=3000,},
{level=8,handle_param3=1350,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>13.5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=3500,defence_power=3500,},
{level=9,handle_param3=1400,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>14%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=4000,defence_power=4000,},
{level=10,handle_param3=1450,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>14.5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=4500,defence_power=4500,},
{level=11,handle_param3=1500,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，给主人附加伤害增强状态，使主人的伤害提升<color=#35ab6c>15%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>60</color>秒最多触发1次",attack_power=5000,defence_power=5000,},
{skill_id=22,skill_name="补天",cd=300000,trigger_type=12,handle_type=8,handle_param1=100,handle_param2=5000,skill_icon=1051,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",},
{level=1,},
{level=2,handle_param1=110,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.1%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=500,defence_power=500,},
{level=3,handle_param1=120,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.2%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=1000,defence_power=1000,},
{level=4,handle_param1=130,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.3%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=1500,defence_power=1500,},
{level=5,handle_param1=140,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.4%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=2000,defence_power=2000,},
{level=6,handle_param1=150,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.5%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=2500,defence_power=2500,},
{level=7,handle_param1=160,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.6%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=3000,defence_power=3000,},
{level=8,handle_param1=170,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.7%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=3500,defence_power=3500,},
{level=9,handle_param1=180,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.8%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=4000,defence_power=4000,},
{level=10,handle_param1=190,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>1.9%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=4500,defence_power=4500,},
{level=11,handle_param1=200,skill_des="主人生命低于<color=#35ab6c>30%</color>时，会给主人附加治疗效果，每秒恢复主人生命上限的<color=#35ab6c>2%</color>，持续<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>300</color>秒最多触发1次",attack_power=5000,defence_power=5000,},
{skill_id=23,skill_name="敌意",trigger_type=2,trigger_param1=445,handle_type=2,handle_param2=18000,skill_icon=1005,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>180%</color>的伤害",},
{level=1,},
{level=2,handle_param2=18900,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>189%</color>的伤害",attack_power=500,defence_power=500,},
{level=3,handle_param2=19800,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>198%</color>的伤害",attack_power=1000,defence_power=1000,},
{level=4,handle_param2=20700,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>207%</color>的伤害",attack_power=1500,defence_power=1500,},
{level=5,handle_param2=21600,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>216%</color>的伤害",attack_power=2000,defence_power=2000,},
{level=6,handle_param2=22500,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>225%</color>的伤害",attack_power=2500,defence_power=2500,},
{level=7,handle_param2=23400,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>234%</color>的伤害",attack_power=3000,defence_power=3000,},
{level=8,handle_param2=24300,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>243%</color>的伤害",attack_power=3500,defence_power=3500,},
{level=9,handle_param2=25200,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>252%</color>的伤害",attack_power=4000,defence_power=4000,},
{level=10,handle_param2=26100,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>261%</color>的伤害",attack_power=4500,defence_power=4500,},
{level=11,handle_param2=27000,skill_des="释放六爻八卦印时，额外追加<color=#35ab6c>270%</color>的伤害",attack_power=5000,defence_power=5000,},
{skill_id=24,skill_name="寒意",handle_type=9,handle_param1=1000,handle_param2=5000,skill_icon=997,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>10%</color>的伤害，持续<color=#35ab6c>5</color>秒",},
{level=1,},
{level=2,handle_param1=1050,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>10.5%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=500,defence_power=500,},
{level=3,handle_param1=1100,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>11%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=1000,defence_power=1000,},
{level=4,handle_param1=1150,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>11.5%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=1500,defence_power=1500,},
{level=5,handle_param1=1200,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>12%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=2000,defence_power=2000,},
{level=6,handle_param1=1250,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>12.5%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=2500,defence_power=2500,},
{level=7,handle_param1=1300,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>13%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=3000,defence_power=3000,},
{level=8,handle_param1=1350,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>13.5%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=3500,defence_power=3500,},
{level=9,handle_param1=1400,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>14%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=4000,defence_power=4000,},
{level=10,handle_param1=1450,attack_power=4500,defence_power=4500,},
{level=11,handle_param1=1500,skill_des="释放六爻八卦印时，额外降低目标<color=#35ab6c>14.5%</color>的伤害，持续<color=#35ab6c>5</color>秒",attack_power=5000,defence_power=5000,},
{skill_id=25,skill_name="混沌",trigger_param1=10,handle_param1=508,skill_icon=1064,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>260%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",},
{level=1,},
{level=2,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>268%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=500,defence_power=500,},
{level=3,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>276%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=1000,defence_power=1000,},
{level=4,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>284%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=1500,defence_power=1500,},
{level=5,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>292%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=2000,defence_power=2000,},
{level=6,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>300%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=2500,defence_power=2500,},
{level=7,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>308%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=3000,defence_power=3000,},
{level=8,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>316%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=3500,defence_power=3500,},
{level=9,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>324%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=4000,defence_power=4000,},
{level=10,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>332%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=4500,defence_power=4500,},
{level=11,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围造成宠物攻击<color=#35ab6c>340%</color>的伤害，并使目标眩晕，持续<color=#35ab6c>1</color>秒",attack_power=5000,defence_power=5000,},
{skill_id=26,skill_name="天成",cd=120000,trigger_type=13,handle_type=7,handle_param1=0,skill_icon=999,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>8%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",},
{level=1,handle_param1=800,handle_param2=5000,},
{level=2,handle_param1=840,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>8.4%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=500,defence_power=500,},
{level=3,handle_param1=880,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>8.8%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=1000,defence_power=1000,},
{level=4,handle_param1=920,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>9.2%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=1500,defence_power=1500,},
{level=5,handle_param1=960,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>9.6%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=2000,defence_power=2000,},
{level=6,handle_param1=1000,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>10%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=2500,defence_power=2500,},
{level=7,handle_param1=1040,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>10.4%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=3000,defence_power=3000,},
{level=8,handle_param1=1080,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>10.8%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=3500,defence_power=3500,},
{level=9,handle_param1=1120,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>11.2%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=4000,defence_power=4000,},
{level=10,handle_param1=1160,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>11.6%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=4500,defence_power=4500,},
{level=11,handle_param1=1200,skill_des="主人生命上限低于<color=#35ab6c>20%</color>时，宠物会给主人释放生命护盾，生命护盾的血量相当于主人血量的<color=#35ab6c>12%</color>，持续时间<color=#35ab6c>5</color>秒，每隔<color=#35ab6c>120</color>秒最多触发1次",attack_power=5000,defence_power=5000,}
},

zs_skill_meta_table_map={
[2]=1,	-- depth:1
[230]=229,	-- depth:1
[182]=181,	-- depth:1
[74]=73,	-- depth:1
[158]=157,	-- depth:1
[290]=289,	-- depth:1
[6]=2,	-- depth:2
[12]=2,	-- depth:2
[11]=2,	-- depth:2
[10]=2,	-- depth:2
[9]=2,	-- depth:2
[4]=2,	-- depth:2
[3]=2,	-- depth:2
[7]=2,	-- depth:2
[5]=2,	-- depth:2
[8]=2,	-- depth:2
[231]=229,	-- depth:1
[233]=229,	-- depth:1
[240]=229,	-- depth:1
[239]=229,	-- depth:1
[238]=229,	-- depth:1
[237]=229,	-- depth:1
[236]=229,	-- depth:1
[232]=229,	-- depth:1
[235]=229,	-- depth:1
[206]=205,	-- depth:1
[234]=229,	-- depth:1
[194]=193,	-- depth:1
[190]=181,	-- depth:1
[191]=181,	-- depth:1
[84]=73,	-- depth:1
[83]=73,	-- depth:1
[82]=73,	-- depth:1
[81]=73,	-- depth:1
[80]=73,	-- depth:1
[98]=97,	-- depth:1
[192]=181,	-- depth:1
[184]=181,	-- depth:1
[188]=181,	-- depth:1
[146]=145,	-- depth:1
[159]=157,	-- depth:1
[160]=157,	-- depth:1
[161]=157,	-- depth:1
[162]=157,	-- depth:1
[163]=157,	-- depth:1
[164]=157,	-- depth:1
[134]=133,	-- depth:1
[165]=157,	-- depth:1
[189]=181,	-- depth:1
[166]=157,	-- depth:1
[168]=157,	-- depth:1
[170]=169,	-- depth:1
[122]=121,	-- depth:1
[183]=181,	-- depth:1
[79]=73,	-- depth:1
[185]=181,	-- depth:1
[186]=181,	-- depth:1
[187]=181,	-- depth:1
[167]=157,	-- depth:1
[78]=73,	-- depth:1
[75]=73,	-- depth:1
[76]=73,	-- depth:1
[295]=289,	-- depth:1
[296]=289,	-- depth:1
[297]=289,	-- depth:1
[298]=289,	-- depth:1
[299]=289,	-- depth:1
[300]=289,	-- depth:1
[217]=301,	-- depth:1
[266]=265,	-- depth:1
[62]=61,	-- depth:1
[13]=145,	-- depth:1
[293]=289,	-- depth:1
[292]=289,	-- depth:1
[291]=289,	-- depth:1
[77]=73,	-- depth:1
[277]=265,	-- depth:1
[294]=289,	-- depth:1
[110]=109,	-- depth:1
[278]=277,	-- depth:2
[218]=217,	-- depth:2
[254]=253,	-- depth:1
[216]=205,	-- depth:1
[215]=205,	-- depth:1
[207]=205,	-- depth:1
[208]=205,	-- depth:1
[209]=205,	-- depth:1
[211]=205,	-- depth:1
[212]=205,	-- depth:1
[210]=205,	-- depth:1
[302]=301,	-- depth:1
[213]=205,	-- depth:1
[214]=205,	-- depth:1
[14]=13,	-- depth:2
[154]=145,	-- depth:1
[155]=145,	-- depth:1
[172]=169,	-- depth:1
[153]=145,	-- depth:1
[276]=265,	-- depth:1
[275]=265,	-- depth:1
[274]=265,	-- depth:1
[171]=169,	-- depth:1
[173]=169,	-- depth:1
[271]=265,	-- depth:1
[273]=265,	-- depth:1
[152]=145,	-- depth:1
[204]=193,	-- depth:1
[203]=193,	-- depth:1
[202]=193,	-- depth:1
[201]=193,	-- depth:1
[200]=193,	-- depth:1
[199]=193,	-- depth:1
[198]=193,	-- depth:1
[197]=193,	-- depth:1
[196]=193,	-- depth:1
[195]=193,	-- depth:1
[242]=241,	-- depth:1
[267]=265,	-- depth:1
[268]=265,	-- depth:1
[269]=265,	-- depth:1
[270]=265,	-- depth:1
[180]=169,	-- depth:1
[179]=169,	-- depth:1
[178]=169,	-- depth:1
[177]=169,	-- depth:1
[176]=169,	-- depth:1
[175]=169,	-- depth:1
[272]=265,	-- depth:1
[174]=169,	-- depth:1
[151]=145,	-- depth:1
[156]=145,	-- depth:1
[149]=145,	-- depth:1
[106]=97,	-- depth:1
[105]=97,	-- depth:1
[104]=97,	-- depth:1
[103]=97,	-- depth:1
[102]=97,	-- depth:1
[101]=97,	-- depth:1
[100]=97,	-- depth:1
[85]=25,	-- depth:1
[72]=61,	-- depth:1
[71]=61,	-- depth:1
[70]=61,	-- depth:1
[69]=61,	-- depth:1
[68]=61,	-- depth:1
[67]=61,	-- depth:1
[66]=61,	-- depth:1
[65]=61,	-- depth:1
[64]=61,	-- depth:1
[63]=61,	-- depth:1
[150]=145,	-- depth:1
[107]=97,	-- depth:1
[108]=97,	-- depth:1
[99]=97,	-- depth:1
[136]=133,	-- depth:1
[148]=145,	-- depth:1
[147]=145,	-- depth:1
[144]=133,	-- depth:1
[143]=133,	-- depth:1
[142]=133,	-- depth:1
[141]=133,	-- depth:1
[140]=133,	-- depth:1
[139]=133,	-- depth:1
[138]=133,	-- depth:1
[137]=133,	-- depth:1
[135]=133,	-- depth:1
[132]=121,	-- depth:1
[131]=121,	-- depth:1
[130]=121,	-- depth:1
[49]=241,	-- depth:1
[128]=121,	-- depth:1
[127]=121,	-- depth:1
[129]=121,	-- depth:1
[123]=121,	-- depth:1
[124]=121,	-- depth:1
[126]=121,	-- depth:1
[125]=121,	-- depth:1
[283]=277,	-- depth:2
[282]=277,	-- depth:2
[281]=277,	-- depth:2
[280]=277,	-- depth:2
[279]=277,	-- depth:2
[306]=302,	-- depth:2
[264]=253,	-- depth:1
[284]=277,	-- depth:2
[263]=253,	-- depth:1
[262]=253,	-- depth:1
[261]=253,	-- depth:1
[307]=302,	-- depth:2
[260]=253,	-- depth:1
[259]=253,	-- depth:1
[258]=253,	-- depth:1
[257]=253,	-- depth:1
[256]=253,	-- depth:1
[38]=37,	-- depth:1
[285]=277,	-- depth:2
[304]=302,	-- depth:2
[305]=302,	-- depth:2
[255]=253,	-- depth:1
[18]=13,	-- depth:2
[19]=13,	-- depth:2
[20]=13,	-- depth:2
[21]=13,	-- depth:2
[22]=13,	-- depth:2
[23]=13,	-- depth:2
[24]=13,	-- depth:2
[26]=25,	-- depth:1
[288]=277,	-- depth:2
[287]=288,	-- depth:3
[309]=302,	-- depth:2
[17]=13,	-- depth:2
[16]=13,	-- depth:2
[15]=13,	-- depth:2
[303]=302,	-- depth:2
[310]=302,	-- depth:2
[286]=277,	-- depth:2
[308]=302,	-- depth:2
[312]=302,	-- depth:2
[111]=109,	-- depth:1
[220]=217,	-- depth:2
[221]=217,	-- depth:2
[222]=217,	-- depth:2
[223]=217,	-- depth:2
[224]=217,	-- depth:2
[225]=217,	-- depth:2
[226]=217,	-- depth:2
[227]=217,	-- depth:2
[228]=217,	-- depth:2
[219]=217,	-- depth:2
[112]=109,	-- depth:1
[113]=109,	-- depth:1
[114]=109,	-- depth:1
[115]=109,	-- depth:1
[116]=109,	-- depth:1
[86]=85,	-- depth:2
[50]=49,	-- depth:2
[117]=109,	-- depth:1
[311]=302,	-- depth:2
[120]=109,	-- depth:1
[119]=109,	-- depth:1
[118]=109,	-- depth:1
[251]=241,	-- depth:1
[250]=241,	-- depth:1
[249]=241,	-- depth:1
[248]=241,	-- depth:1
[243]=241,	-- depth:1
[244]=241,	-- depth:1
[247]=241,	-- depth:1
[246]=241,	-- depth:1
[245]=241,	-- depth:1
[252]=241,	-- depth:1
[28]=25,	-- depth:1
[87]=85,	-- depth:2
[27]=25,	-- depth:1
[92]=85,	-- depth:2
[89]=85,	-- depth:2
[90]=85,	-- depth:2
[91]=85,	-- depth:2
[93]=85,	-- depth:2
[94]=85,	-- depth:2
[95]=85,	-- depth:2
[96]=85,	-- depth:2
[88]=85,	-- depth:2
[47]=37,	-- depth:1
[31]=25,	-- depth:1
[59]=49,	-- depth:2
[46]=37,	-- depth:1
[45]=37,	-- depth:1
[44]=37,	-- depth:1
[43]=37,	-- depth:1
[51]=49,	-- depth:2
[52]=49,	-- depth:2
[53]=49,	-- depth:2
[54]=49,	-- depth:2
[55]=49,	-- depth:2
[57]=49,	-- depth:2
[58]=49,	-- depth:2
[42]=37,	-- depth:1
[41]=37,	-- depth:1
[40]=37,	-- depth:1
[39]=37,	-- depth:1
[36]=25,	-- depth:1
[35]=25,	-- depth:1
[34]=25,	-- depth:1
[33]=25,	-- depth:1
[32]=25,	-- depth:1
[48]=37,	-- depth:1
[30]=25,	-- depth:1
[29]=25,	-- depth:1
[60]=49,	-- depth:2
[56]=49,	-- depth:2
},
pet_call={
{pos_des="与主人并肩作战\n所有技能生效\n主角继承宠物属性",},
{index=1,open_level=80,},
{index=2,auto_open_need_zhuanzhi=1,},
{index=3,auto_open_need_zhuanzhi=2,pos_des="被动、专属技能生效\n主角继承宠物属性",},
{index=4,auto_open_need_zhuanzhi=3,}
},

pet_call_meta_table_map={
[5]=4,	-- depth:1
},
pet_call_other={
{}
},

pet_call_other_meta_table_map={
},
pet_break={
{item5_reward_per=500,item8_reward_per=0,},
{color=1,item2_reward_per=5000,item5_reward_per=1000,item8_reward_per=500,},
{color=2,item1=item_table[1],item5_reward_per=2500,item6_reward_per=500,item8_reward_per=2000,item9_reward_per=500,item11_reward_per=5000,},
{color=3,item1=item_table[2],item3_reward_per=10000,item6_reward_per=5000,item7_reward_per=500,item9_reward_per=5000,item12_reward_per=5000,},
{color=4,item1=item_table[3],item3=item_table[4],item3_reward_per=10000,item4=item_table[5],item4_reward_per=10000,item5=item_table[6],item6_reward_per=10000,item7_reward_per=3000,item8=item_table[7],item9_reward_per=10000,item10_reward_per=5000,item13_reward_per=5000,},
{color=5,item1=item_table[8],item3=item_table[9],item3_reward_per=10000,item4=item_table[10],item4_reward_per=10000,item5=item_table[11],item6=item_table[12],item6_reward_per=10000,item7_reward_per=10000,item8=item_table[13],item9=item_table[14],item9_reward_per=10000,item10_reward_per=10000,item14_reward_per=5000,}
},

pet_break_meta_table_map={
},
role_attach={
{},
{role_level=2,},
{role_level=3,},
{role_level=4,},
{role_level=5,},
{role_level=6,},
{role_level=7,},
{role_level=8,},
{role_level=9,},
{role_level=10,},
{role_level=11,},
{role_level=12,},
{role_level=13,},
{role_level=14,},
{role_level=15,},
{role_level=16,},
{role_level=17,},
{role_level=18,},
{role_level=19,},
{role_level=20,},
{role_level=21,},
{role_level=22,},
{role_level=23,},
{role_level=24,},
{role_level=25,},
{role_level=26,},
{role_level=27,},
{role_level=28,},
{role_level=29,},
{role_level=30,},
{role_level=31,},
{role_level=32,},
{role_level=33,},
{role_level=34,},
{role_level=35,},
{role_level=36,},
{role_level=37,},
{role_level=38,},
{role_level=39,},
{role_level=40,},
{role_level=41,},
{role_level=42,},
{role_level=43,},
{role_level=44,},
{role_level=45,},
{role_level=46,},
{role_level=47,},
{role_level=48,},
{role_level=49,},
{role_level=50,},
{role_level=51,},
{role_level=52,},
{role_level=53,},
{role_level=54,},
{role_level=55,},
{role_level=56,},
{role_level=57,},
{role_level=58,},
{role_level=59,},
{role_level=60,},
{role_level=61,},
{role_level=62,},
{role_level=63,},
{role_level=64,},
{role_level=65,},
{role_level=66,},
{role_level=67,},
{role_level=68,},
{role_level=69,},
{role_level=70,},
{role_level=71,},
{role_level=72,},
{role_level=73,},
{role_level=74,},
{role_level=75,},
{role_level=76,},
{role_level=77,},
{role_level=78,},
{role_level=79,},
{role_level=80,},
{role_level=81,},
{role_level=82,},
{role_level=83,},
{role_level=84,},
{role_level=85,},
{role_level=86,},
{role_level=87,},
{role_level=88,},
{role_level=89,},
{role_level=90,},
{role_level=91,},
{role_level=92,},
{role_level=93,},
{role_level=94,},
{role_level=95,},
{role_level=96,},
{role_level=97,},
{role_level=98,},
{role_level=99,},
{role_level=100,},
{role_level=101,},
{role_level=102,},
{role_level=103,},
{role_level=104,},
{role_level=105,},
{role_level=106,},
{role_level=107,},
{role_level=108,},
{role_level=109,},
{role_level=110,},
{role_level=111,},
{role_level=112,},
{role_level=113,},
{role_level=114,},
{role_level=115,},
{role_level=116,},
{role_level=117,},
{role_level=118,},
{role_level=119,},
{role_level=120,},
{role_level=121,},
{role_level=122,},
{role_level=123,},
{role_level=124,},
{role_level=125,},
{role_level=126,},
{role_level=127,},
{role_level=128,},
{role_level=129,},
{role_level=130,},
{role_level=131,},
{role_level=132,},
{role_level=133,},
{role_level=134,},
{role_level=135,},
{role_level=136,},
{role_level=137,},
{role_level=138,},
{role_level=139,},
{role_level=140,},
{role_level=141,},
{role_level=142,},
{role_level=143,},
{role_level=144,},
{role_level=145,},
{role_level=146,},
{role_level=147,},
{role_level=148,},
{role_level=149,},
{role_level=150,},
{role_level=151,},
{role_level=152,},
{role_level=153,},
{role_level=154,},
{role_level=155,},
{role_level=156,},
{role_level=157,},
{role_level=158,},
{role_level=159,},
{role_level=160,},
{role_level=161,},
{role_level=162,},
{role_level=163,},
{role_level=164,},
{role_level=165,},
{role_level=166,},
{role_level=167,},
{role_level=168,},
{role_level=169,},
{role_level=170,},
{role_level=171,},
{role_level=172,},
{role_level=173,},
{role_level=174,},
{role_level=175,},
{role_level=176,},
{role_level=177,},
{role_level=178,},
{role_level=179,},
{role_level=180,},
{role_level=181,},
{role_level=182,},
{role_level=183,},
{role_level=184,},
{role_level=185,},
{role_level=186,},
{role_level=187,},
{role_level=188,},
{role_level=189,},
{role_level=190,},
{role_level=191,},
{role_level=192,},
{role_level=193,},
{role_level=194,},
{role_level=195,},
{role_level=196,},
{role_level=197,},
{role_level=198,},
{role_level=199,},
{role_level=200,},
{role_level=201,},
{role_level=202,},
{role_level=203,},
{role_level=204,},
{role_level=205,},
{role_level=206,},
{role_level=207,},
{role_level=208,},
{role_level=209,},
{role_level=210,},
{role_level=211,},
{role_level=212,},
{role_level=213,},
{role_level=214,},
{role_level=215,},
{role_level=216,},
{role_level=217,},
{role_level=218,},
{role_level=219,},
{role_level=220,},
{role_level=221,},
{role_level=222,},
{role_level=223,},
{role_level=224,},
{role_level=225,},
{role_level=226,},
{role_level=227,},
{role_level=228,},
{role_level=229,},
{role_level=230,},
{role_level=231,},
{role_level=232,},
{role_level=233,},
{role_level=234,},
{role_level=235,},
{role_level=236,},
{role_level=237,},
{role_level=238,},
{role_level=239,},
{role_level=240,},
{role_level=241,},
{role_level=242,},
{role_level=243,},
{role_level=244,},
{role_level=245,},
{role_level=246,},
{role_level=247,},
{role_level=248,},
{role_level=249,},
{role_level=250,},
{role_level=251,},
{role_level=252,},
{role_level=253,},
{role_level=254,},
{role_level=255,},
{role_level=256,},
{role_level=257,},
{role_level=258,},
{role_level=259,},
{role_level=260,},
{role_level=261,},
{role_level=262,},
{role_level=263,},
{role_level=264,},
{role_level=265,},
{role_level=266,},
{role_level=267,},
{role_level=268,},
{role_level=269,},
{role_level=270,},
{role_level=271,},
{role_level=272,},
{role_level=273,},
{role_level=274,},
{role_level=275,},
{role_level=276,},
{role_level=277,},
{role_level=278,},
{role_level=279,},
{role_level=280,},
{role_level=281,},
{role_level=282,},
{role_level=283,},
{role_level=284,},
{role_level=285,},
{role_level=286,},
{role_level=287,},
{role_level=288,},
{role_level=289,},
{role_level=290,},
{role_level=291,},
{role_level=292,},
{role_level=293,},
{role_level=294,},
{role_level=295,},
{role_level=296,},
{role_level=297,},
{role_level=298,},
{role_level=299,},
{role_level=300,},
{role_level=301,},
{role_level=302,},
{role_level=303,},
{role_level=304,},
{role_level=305,},
{role_level=306,},
{role_level=307,},
{role_level=308,},
{role_level=309,},
{role_level=310,},
{role_level=311,},
{role_level=312,},
{role_level=313,},
{role_level=314,},
{role_level=315,},
{role_level=316,},
{role_level=317,},
{role_level=318,},
{role_level=319,},
{role_level=320,},
{role_level=321,},
{role_level=322,},
{role_level=323,},
{role_level=324,},
{role_level=325,},
{role_level=326,},
{role_level=327,},
{role_level=328,},
{role_level=329,},
{role_level=330,},
{role_level=331,},
{role_level=332,},
{role_level=333,},
{role_level=334,},
{role_level=335,},
{role_level=336,},
{role_level=337,},
{role_level=338,},
{role_level=339,},
{role_level=340,},
{role_level=341,},
{role_level=342,},
{role_level=343,},
{role_level=344,},
{role_level=345,},
{role_level=346,},
{role_level=347,},
{role_level=348,},
{role_level=349,},
{role_level=350,},
{role_level=351,},
{role_level=352,},
{role_level=353,},
{role_level=354,},
{role_level=355,},
{role_level=356,},
{role_level=357,},
{role_level=358,},
{role_level=359,},
{role_level=360,},
{role_level=361,},
{role_level=362,},
{role_level=363,},
{role_level=364,},
{role_level=365,},
{role_level=366,},
{role_level=367,},
{role_level=368,},
{role_level=369,},
{role_level=370,},
{role_level=371,},
{role_level=372,},
{role_level=373,},
{role_level=374,},
{role_level=375,},
{role_level=376,},
{role_level=377,},
{role_level=378,},
{role_level=379,},
{role_level=380,},
{role_level=381,},
{role_level=382,},
{role_level=383,},
{role_level=384,},
{role_level=385,},
{role_level=386,},
{role_level=387,},
{role_level=388,},
{role_level=389,},
{role_level=390,},
{role_level=391,},
{role_level=392,},
{role_level=393,},
{role_level=394,},
{role_level=395,},
{role_level=396,},
{role_level=397,},
{role_level=398,},
{role_level=399,},
{role_level=400,},
{role_level=401,},
{role_level=402,},
{role_level=403,},
{role_level=404,},
{role_level=405,},
{role_level=406,},
{role_level=407,},
{role_level=408,},
{role_level=409,},
{role_level=410,},
{role_level=411,},
{role_level=412,},
{role_level=413,},
{role_level=414,},
{role_level=415,},
{role_level=416,},
{role_level=417,},
{role_level=418,},
{role_level=419,},
{role_level=420,},
{role_level=421,},
{role_level=422,},
{role_level=423,},
{role_level=424,},
{role_level=425,},
{role_level=426,},
{role_level=427,},
{role_level=428,},
{role_level=429,},
{role_level=430,},
{role_level=431,},
{role_level=432,},
{role_level=433,},
{role_level=434,},
{role_level=435,},
{role_level=436,},
{role_level=437,},
{role_level=438,},
{role_level=439,},
{role_level=440,},
{role_level=441,},
{role_level=442,},
{role_level=443,},
{role_level=444,},
{role_level=445,},
{role_level=446,},
{role_level=447,},
{role_level=448,},
{role_level=449,},
{role_level=450,},
{role_level=451,},
{role_level=452,},
{role_level=453,},
{role_level=454,},
{role_level=455,},
{role_level=456,},
{role_level=457,},
{role_level=458,},
{role_level=459,},
{role_level=460,},
{role_level=461,},
{role_level=462,},
{role_level=463,},
{role_level=464,},
{role_level=465,},
{role_level=466,},
{role_level=467,},
{role_level=468,},
{role_level=469,},
{role_level=470,},
{role_level=471,},
{role_level=472,},
{role_level=473,},
{role_level=474,},
{role_level=475,},
{role_level=476,},
{role_level=477,},
{role_level=478,},
{role_level=479,},
{role_level=480,},
{role_level=481,},
{role_level=482,},
{role_level=483,},
{role_level=484,},
{role_level=485,},
{role_level=486,},
{role_level=487,},
{role_level=488,},
{role_level=489,},
{role_level=490,},
{role_level=491,},
{role_level=492,},
{role_level=493,},
{role_level=494,},
{role_level=495,},
{role_level=496,},
{role_level=497,},
{role_level=498,},
{role_level=499,},
{role_level=500,},
{role_level=501,},
{role_level=502,},
{role_level=503,},
{role_level=504,},
{role_level=505,},
{role_level=506,},
{role_level=507,},
{role_level=508,},
{role_level=509,},
{role_level=510,},
{role_level=511,},
{role_level=512,},
{role_level=513,},
{role_level=514,},
{role_level=515,},
{role_level=516,},
{role_level=517,},
{role_level=518,},
{role_level=519,},
{role_level=520,},
{role_level=521,},
{role_level=522,},
{role_level=523,},
{role_level=524,},
{role_level=525,},
{role_level=526,},
{role_level=527,},
{role_level=528,},
{role_level=529,},
{role_level=530,},
{role_level=531,},
{role_level=532,},
{role_level=533,},
{role_level=534,},
{role_level=535,},
{role_level=536,},
{role_level=537,},
{role_level=538,},
{role_level=539,},
{role_level=540,},
{role_level=541,},
{role_level=542,},
{role_level=543,},
{role_level=544,},
{role_level=545,},
{role_level=546,},
{role_level=547,},
{role_level=548,},
{role_level=549,},
{role_level=550,},
{role_level=551,},
{role_level=552,},
{role_level=553,},
{role_level=554,},
{role_level=555,},
{role_level=556,},
{role_level=557,},
{role_level=558,},
{role_level=559,},
{role_level=560,},
{role_level=561,},
{role_level=562,},
{role_level=563,},
{role_level=564,},
{role_level=565,},
{role_level=566,},
{role_level=567,},
{role_level=568,},
{role_level=569,},
{role_level=570,},
{role_level=571,},
{role_level=572,},
{role_level=573,},
{role_level=574,},
{role_level=575,},
{role_level=576,},
{role_level=577,},
{role_level=578,},
{role_level=579,},
{role_level=580,},
{role_level=581,},
{role_level=582,},
{role_level=583,},
{role_level=584,},
{role_level=585,},
{role_level=586,},
{role_level=587,},
{role_level=588,},
{role_level=589,},
{role_level=590,},
{role_level=591,},
{role_level=592,},
{role_level=593,},
{role_level=594,},
{role_level=595,},
{role_level=596,},
{role_level=597,},
{role_level=598,},
{role_level=599,},
{role_level=600,},
{role_level=601,},
{role_level=602,},
{role_level=603,},
{role_level=604,},
{role_level=605,},
{role_level=606,},
{role_level=607,},
{role_level=608,},
{role_level=609,},
{role_level=610,},
{role_level=611,},
{role_level=612,},
{role_level=613,},
{role_level=614,},
{role_level=615,},
{role_level=616,},
{role_level=617,},
{role_level=618,},
{role_level=619,},
{role_level=620,},
{role_level=621,},
{role_level=622,},
{role_level=623,},
{role_level=624,},
{role_level=625,},
{role_level=626,},
{role_level=627,},
{role_level=628,},
{role_level=629,},
{role_level=630,},
{role_level=631,},
{role_level=632,},
{role_level=633,},
{role_level=634,},
{role_level=635,},
{role_level=636,},
{role_level=637,},
{role_level=638,},
{role_level=639,},
{role_level=640,},
{role_level=641,},
{role_level=642,},
{role_level=643,},
{role_level=644,},
{role_level=645,},
{role_level=646,},
{role_level=647,},
{role_level=648,},
{role_level=649,},
{role_level=650,},
{role_level=651,},
{role_level=652,},
{role_level=653,},
{role_level=654,},
{role_level=655,},
{role_level=656,},
{role_level=657,},
{role_level=658,},
{role_level=659,},
{role_level=660,},
{role_level=661,},
{role_level=662,},
{role_level=663,},
{role_level=664,},
{role_level=665,},
{role_level=666,},
{role_level=667,},
{role_level=668,},
{role_level=669,},
{role_level=670,},
{role_level=671,},
{role_level=672,},
{role_level=673,},
{role_level=674,},
{role_level=675,},
{role_level=676,},
{role_level=677,},
{role_level=678,},
{role_level=679,},
{role_level=680,},
{role_level=681,},
{role_level=682,},
{role_level=683,},
{role_level=684,},
{role_level=685,},
{role_level=686,},
{role_level=687,},
{role_level=688,},
{role_level=689,},
{role_level=690,},
{role_level=691,},
{role_level=692,},
{role_level=693,},
{role_level=694,},
{role_level=695,},
{role_level=696,},
{role_level=697,},
{role_level=698,},
{role_level=699,},
{role_level=700,},
{role_level=701,},
{role_level=702,},
{role_level=703,},
{role_level=704,},
{role_level=705,},
{role_level=706,},
{role_level=707,},
{role_level=708,},
{role_level=709,},
{role_level=710,},
{role_level=711,},
{role_level=712,},
{role_level=713,},
{role_level=714,},
{role_level=715,},
{role_level=716,},
{role_level=717,},
{role_level=718,},
{role_level=719,},
{role_level=720,},
{role_level=721,},
{role_level=722,},
{role_level=723,},
{role_level=724,},
{role_level=725,},
{role_level=726,},
{role_level=727,},
{role_level=728,},
{role_level=729,},
{role_level=730,},
{role_level=731,},
{role_level=732,},
{role_level=733,},
{role_level=734,},
{role_level=735,},
{role_level=736,},
{role_level=737,},
{role_level=738,},
{role_level=739,},
{role_level=740,},
{role_level=741,},
{role_level=742,},
{role_level=743,},
{role_level=744,},
{role_level=745,},
{role_level=746,},
{role_level=747,},
{role_level=748,},
{role_level=749,},
{role_level=750,},
{role_level=751,},
{role_level=752,},
{role_level=753,},
{role_level=754,},
{role_level=755,},
{role_level=756,},
{role_level=757,},
{role_level=758,},
{role_level=759,},
{role_level=760,},
{role_level=761,},
{role_level=762,},
{role_level=763,},
{role_level=764,},
{role_level=765,},
{role_level=766,},
{role_level=767,},
{role_level=768,},
{role_level=769,},
{role_level=770,},
{role_level=771,},
{role_level=772,},
{role_level=773,},
{role_level=774,},
{role_level=775,},
{role_level=776,},
{role_level=777,},
{role_level=778,},
{role_level=779,},
{role_level=780,},
{role_level=781,},
{role_level=782,},
{role_level=783,},
{role_level=784,},
{role_level=785,},
{role_level=786,},
{role_level=787,},
{role_level=788,},
{role_level=789,},
{role_level=790,},
{role_level=791,},
{role_level=792,},
{role_level=793,},
{role_level=794,},
{role_level=795,},
{role_level=796,},
{role_level=797,},
{role_level=798,},
{role_level=799,},
{role_level=800,},
{role_level=801,},
{role_level=802,},
{role_level=803,},
{role_level=804,},
{role_level=805,},
{role_level=806,},
{role_level=807,},
{role_level=808,},
{role_level=809,},
{role_level=810,},
{role_level=811,},
{role_level=812,},
{role_level=813,},
{role_level=814,},
{role_level=815,},
{role_level=816,},
{role_level=817,},
{role_level=818,},
{role_level=819,},
{role_level=820,},
{role_level=821,},
{role_level=822,},
{role_level=823,},
{role_level=824,},
{role_level=825,},
{role_level=826,},
{role_level=827,},
{role_level=828,},
{role_level=829,},
{role_level=830,},
{role_level=831,},
{role_level=832,},
{role_level=833,},
{role_level=834,},
{role_level=835,},
{role_level=836,},
{role_level=837,},
{role_level=838,},
{role_level=839,},
{role_level=840,},
{role_level=841,},
{role_level=842,},
{role_level=843,},
{role_level=844,},
{role_level=845,},
{role_level=846,},
{role_level=847,},
{role_level=848,},
{role_level=849,},
{role_level=850,},
{role_level=851,},
{role_level=852,},
{role_level=853,},
{role_level=854,},
{role_level=855,},
{role_level=856,},
{role_level=857,},
{role_level=858,},
{role_level=859,},
{role_level=860,},
{role_level=861,},
{role_level=862,},
{role_level=863,},
{role_level=864,},
{role_level=865,},
{role_level=866,},
{role_level=867,},
{role_level=868,},
{role_level=869,},
{role_level=870,},
{role_level=871,},
{role_level=872,},
{role_level=873,},
{role_level=874,},
{role_level=875,},
{role_level=876,},
{role_level=877,},
{role_level=878,},
{role_level=879,},
{role_level=880,},
{role_level=881,},
{role_level=882,},
{role_level=883,},
{role_level=884,},
{role_level=885,},
{role_level=886,},
{role_level=887,},
{role_level=888,},
{role_level=889,},
{role_level=890,},
{role_level=891,},
{role_level=892,},
{role_level=893,},
{role_level=894,},
{role_level=895,},
{role_level=896,},
{role_level=897,},
{role_level=898,},
{role_level=899,},
{role_level=900,},
{role_level=901,},
{role_level=902,},
{role_level=903,},
{role_level=904,},
{role_level=905,},
{role_level=906,},
{role_level=907,},
{role_level=908,},
{role_level=909,},
{role_level=910,},
{role_level=911,},
{role_level=912,},
{role_level=913,},
{role_level=914,},
{role_level=915,},
{role_level=916,},
{role_level=917,},
{role_level=918,},
{role_level=919,},
{role_level=920,},
{role_level=921,},
{role_level=922,},
{role_level=923,},
{role_level=924,},
{role_level=925,},
{role_level=926,},
{role_level=927,},
{role_level=928,},
{role_level=929,},
{role_level=930,},
{role_level=931,},
{role_level=932,},
{role_level=933,},
{role_level=934,},
{role_level=935,},
{role_level=936,},
{role_level=937,},
{role_level=938,},
{role_level=939,},
{role_level=940,},
{role_level=941,},
{role_level=942,},
{role_level=943,},
{role_level=944,},
{role_level=945,},
{role_level=946,},
{role_level=947,},
{role_level=948,},
{role_level=949,},
{role_level=950,},
{role_level=951,},
{role_level=952,},
{role_level=953,},
{role_level=954,},
{role_level=955,},
{role_level=956,},
{role_level=957,},
{role_level=958,},
{role_level=959,},
{role_level=960,},
{role_level=961,},
{role_level=962,},
{role_level=963,},
{role_level=964,},
{role_level=965,},
{role_level=966,},
{role_level=967,},
{role_level=968,},
{role_level=969,},
{role_level=970,},
{role_level=971,},
{role_level=972,},
{role_level=973,},
{role_level=974,},
{role_level=975,},
{role_level=976,},
{role_level=977,},
{role_level=978,},
{role_level=979,},
{role_level=980,},
{role_level=981,},
{role_level=982,},
{role_level=983,},
{role_level=984,},
{role_level=985,},
{role_level=986,},
{role_level=987,},
{role_level=988,},
{role_level=989,},
{role_level=990,},
{role_level=991,},
{role_level=992,},
{role_level=993,},
{role_level=994,},
{role_level=995,},
{role_level=996,},
{role_level=997,},
{role_level=998,},
{role_level=999,},
{role_level=1000,}
},

role_attach_meta_table_map={
},
pet_pingji={
{},
{type=2,grow_rate_lower=180,grow_rate_upper=200,},
{type=3,grow_rate_lower=200,grow_rate_upper=250,},
{type=4,grow_rate_lower=250,grow_rate_upper=421,},
{type=5,grow_rate_lower=421,grow_rate_upper=500,},
{type=6,grow_rate_lower=500,grow_rate_upper=9999,}
},

pet_pingji_meta_table_map={
},
pet_jump={
{},
{jump_type=2,param_1="xunBao#equipxunbao_xunbao",jump_icon="btn_xunbao",icon_name="寻宝",icon_describe="装备寻宝",},
{jump_type=3,param_1="boss#boss_world",jump_icon="btn_boss",icon_name="魔王",icon_describe="购买守护",},
{jump_type=4,},
{jump_type=5,},
{jump_type=6,}
},

pet_jump_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
},
other_default_table={pet_exp_item="27700|27701|27702",pet_exp_item_add_exp="100|600|3000",egg_quick_finish_item_id=27717,egg_quick_finish_time_s=900,jump_way="1|2|3",seq=20011,newer_pet_id=4,},

ai_default_table={},

pet_base_default_table={id=1,type=1,name="狐小妖",common_skill_id=400,active_skill=410,passive_skill_id=2,zs_skill_id=0,color=3,gongji=450,fangyu=300,maxhp=11250,pojia=150,yuansu_sh=300,yuansu_hj=150,grow_rate_lower=200,grow_rate_upper=280,get_msg_type=6,pet_head_res=1,pet_res_id=10001001,ui_scale=200,ui_pos="0|-1.12|0",scene_scale=1,main_scale=200,main_pos="-0.1|-0.87|0",other_scale=120,other_pos="0|-1.09|0",zhudong_des="主动技能描述",zhuanshu_des="专属技能描述",beidong_des="被动技能描述",jump_way="4|5|6",item_icon=90775,open_day_visible=0,pet_audio="",},

pet_egg_default_table={item_id=27721,is_special=1,need_time_s=1,pet_pool="1|2|3|4|5|6|7|8|9|10|13|17",pet_pool_weight=1,special_open_times=0,special_pet_pool=30,special_pet_pool_weight=1,pet_res_id=41,ui_scale=200,ui_pos="0|-1.34|0",open_get_list_1="29|17|24",open_get_list_2=0,open_get_list_3=0,bind_grow_rate=0,},

pet_egg_slot_default_table={slot=0,open_level=0,unlock_item_id=0,unlock_gold=0,unlock_bind_gold=0,},

pet_level_default_table={level=1,need_role_level=1,need_exp=100,msg_type=-1,call_attr_per=20000,shanghai_jm_per=3500,buff_name_1="英勇奋战",buff_des_1="宠物出战时，自身攻击提高100%",buff_icon_1=252,buff_name_2="伺机待发",buff_des_2="宠物受到的伤害减少35%",buff_icon_2=251,buff_name_3="亲密灵剑",buff_des_3="主人攻击力的20%，将共享给宠物",buff_icon_3=250,},

pet_level_base_attr_default_table={type=0,gongji=12,fangyu=8,maxhp=300,pojia=4,yuansu_sh=8,yuansu_hj=4,},

grow_rate_default_table={grow_rate=1,attr_add_per=10,},

pet_upgrade_default_table={color=0,grade=0,need_pet_level=0,need_item_id=27705,need_item_num=2,msg_type=-1,active_passive_skill_index=0,gongji=0,fangyu=0,maxhp=0,pojia=0,yuansu_sh=0,yuansu_hj=0,},

pet_upstar_default_table={pet_id=1,star=0,active_skill_level=1,zs_skill_level=0,need_pet_level=0,need_num=1,need_item_id=27709,msg_type=6,extra_grow_rate=0,maxhp=0,gongji=0,fangyu=0,pojia=0,yuansu_sh=0,yuansu_hj=0,},

passive_skill_default_table={skill_id=1,skill_name="守护·绝",level=0,consume_item=27718,consume_item_num=6,skill_type=1,param1="gongji",param2=0,param3=0,param4=0,attack_power=0,defence_power=0,capability_inc=0,skill_icon=152,skill_des="宠物生命提高<color=#35ab6c>0</color>点",zhen_flag=0,},

zs_skill_default_table={skill_id=1,skill_name="追击",level=0,cd=0,trigger_type=1,trigger_param1=0,handle_type=1,handle_param1=10000,handle_param2=0,handle_param3=0,handle_param4=0,skill_icon=1066,skill_des="宠物每攻击<color=#35ab6c>10</color>下时，对前方扇形范围区域<color=#35ab6c>4</color>个目标造成宠物攻击<color=#35ab6c>250%</color>的伤害，并降低目标移动速度<color=#35ab6c>20%</color>，持续<color=#35ab6c>3</color>秒",zhen_flag=0,attack_power=0,defence_power=0,capability_inc=0,},

pet_call_default_table={index=0,open_level=0,auto_open_need_zhuanzhi=0,unlock_item_id=0,unlock_gold=0,pos_des="所有技能生效\n主角继承宠物属性",},

pet_call_other_default_table={common_skill_id=400,realive_cd_s=3,},

pet_break_default_table={color=0,item1=item_table[15],item1_reward_per=10000,item2=item_table[15],item2_reward_per=0,item3=item_table[16],item3_reward_per=0,item4=item_table[17],item4_reward_per=0,item5=item_table[18],item5_reward_per=10000,item6=item_table[19],item6_reward_per=0,item7=item_table[20],item7_reward_per=0,item8=item_table[21],item8_reward_per=10000,item9=item_table[22],item9_reward_per=0,item10=item_table[23],item10_reward_per=0,item11=item_table[24],item11_reward_per=0,item12=item_table[25],item12_reward_per=0,item13=item_table[26],item13_reward_per=0,item14=item_table[27],item14_reward_per=0,},

role_attach_default_table={role_level=1,gongji_attach_per=2000,},

pet_pingji_default_table={type=1,grow_rate_lower=1,grow_rate_upper=180,},

pet_jump_default_table={jump_type=1,execute=2,param_1="shop",remind_id="",jump_icon="btn_shop",icon_name="商城",activity_id="",icon_describe="元宝购买",icon_scale=1.2,}

}

