require("game/serveractivity/crazy_money_tree/crazy_moneytree_view")
require("game/serveractivity/crazy_money_tree/crazy_moneytree_wg_data")

CrazyMoneyTreeWGCtrl = CrazyMoneyTreeWGCtrl or BaseClass(BaseWGCtrl)

function CrazyMoneyTreeWGCtrl:__init()
	if CrazyMoneyTreeWGCtrl.Instance ~= nil then
		print("[CrazyMoneyTreeWGCtrl]error:create a singleton twice")
	end

	CrazyMoneyTreeWGCtrl.Instance = self
	self.view = CrazyMoneyTreeView.New(GuideModuleName.CrazyMoneyTreeView)
	self.data = CrazyMoneyTreeWGData.New()
	self:RegisterAllProtocols()  --注册协议
	self.activity_call_back = BindTool.Bind(self.ActivityCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_call_back)	
end

function CrazyMoneyTreeWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	if nil ~= self.nomoney_view then
		self.nomoney_view:DeleteMe()
		self.nomoney_view = nil
	end
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if self.activity_call_back then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_call_back)
		self.activity_call_back = nil
	end	
	CrazyMoneyTreeWGCtrl.Instance = nil
end

function CrazyMoneyTreeWGCtrl:RegisterAllProtocols()
	-- 注册接收到的协议
	self:RegisterProtocol(SCRAShakeMoneyInfo, "OnRAShakeMoneyInfo")
	self:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.SendAllInfoReq, self))
end

function CrazyMoneyTreeWGCtrl:SendAllInfoReq() 
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY ) then
		return
	end
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY, opera_type = RA_SHAKEMONEY_OPERA_TYPE.RA_SHAKEMONEY_OPERA_TYPE_QUERY_INFO})
end

function CrazyMoneyTreeWGCtrl:OnRAShakeMoneyInfo(protocol)
	self.data:SetRAShakeMoneyInfo(protocol)
	RemindManager.Instance:Fire(RemindName.crazy_money_tree)
	-- self:CanGetMoneyNum()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function CrazyMoneyTreeWGCtrl:CanGetMoneyNum()
	local chongzhi = self.data:GetTotalGold() or 0
	local max_chongzhi_num = self.data:GetMaxChongZhiNum()
	local gold = self.data:GetMoney()
	if max_chongzhi_num == gold and not self.view:IsOpen() then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY, ACTIVITY_STATUS.CLOSE)
	end
end

function CrazyMoneyTreeWGCtrl:ActivityCallBack(activity_type, status)
	if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY and status == ACTIVITY_STATUS.OPEN then
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY, opera_type = RA_SHAKEMONEY_OPERA_TYPE.RA_SHAKEMONEY_OPERA_TYPE_QUERY_INFO})
	end 
end