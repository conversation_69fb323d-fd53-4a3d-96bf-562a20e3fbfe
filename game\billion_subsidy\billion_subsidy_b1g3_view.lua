-----------------------------------
-- 百亿补贴-付一买三
-----------------------------------

function BillionSubsidyView:B1G3LoadIndexCallBack()
	if not self.b1g3_shop_item_grid then
		self.b1g3_shop_item_grid = B1G3SelectShopGrid.New()
		self.b1g3_shop_item_grid:SetStartZeroIndex(false)
		self.b1g3_shop_item_grid:SetIsMultiSelect(true)
		self.b1g3_shop_item_grid:CreateCells({
			col = 4,
			assetName = "shop_item_render_b1g3",
			assetBundle = "uis/view/billion_subsidy_ui_prefab",
			list_view = self.node_list["b1g3_goods_list"],
			itemRender = B1G3ShopItemRender,
			change_cells_num = 1,
		})
		self.b1g3_shop_item_grid:SetSelectCallBack(BindTool.Bind(self.OnB1G3ItemSelectCB, self))
	end

	if not self.b1g3_sel_result_list then
		self.b1g3_sel_result_list = {}
		for i = 1, 3 do
			self.b1g3_sel_result_list[i] = B1G3ShopSelResultRender.New(self.node_list["selected_result_item_" .. i])
		end
	end

	XUI.AddClickEventListener(self.node_list["btn_b1g3_buy"], BindTool.Bind(self.OnClickB1G3BuyBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_b1g3_refresh"], BindTool.Bind(self.OnClickB1G3RefreshBtn, self))


end

function BillionSubsidyView:B1G3ReleaseCallBack()
	if self.b1g3_shop_item_grid then
		self.b1g3_shop_item_grid:DeleteMe()
		self.b1g3_shop_item_grid = nil
	end

	if self.b1g3_sel_result_list then
		for k, v in pairs(self.b1g3_sel_result_list) do
			v:DeleteMe()
		end
		self.b1g3_sel_result_list = nil
	end

	if self.b1g3_refresh_alert then
		self.b1g3_refresh_alert:DeleteMe()
		self.b1g3_refresh_alert = nil
	end
end

function BillionSubsidyView:B1G3ShowIndexCallBack()

end

function BillionSubsidyView:B1G3CloseCallBack()

end

function BillionSubsidyView:B1G3OnFlush(param_t, index)
	self.b1g3_shop_item_grid:CancleAllSelectCell()
	self.select_data_list = nil
	local shop_cfg_list = BillionSubsidyWGData.Instance:GetB1G3ShopList()
	self.b1g3_shop_item_grid:SetDataList(shop_cfg_list)

	local limited_type = BillionSubsidyWGData.Instance:GetB1G3TodayBuyIsLimited()
	self.node_list["btn_b1g3_buy"]:SetActive(limited_type ~= 2)
	self.node_list["btn_b1g3_refresh"]:SetActive(limited_type ~= 2)
	self.node_list["b1g3_buy_flag"]:SetActive(limited_type == 2)

	if limited_type ~= 2 then
		local cur_times, free_times, refresh_cost = BillionSubsidyWGData.Instance:GetB1G3TodayRefreshTimes()
		local btn_str = Language.BillionSubsidy.B1G3RefreshBtn
		if cur_times < free_times then
			btn_str = string.format(Language.BillionSubsidy.B1G3RefreshFreeBtn, free_times - cur_times, free_times)
		end
		self.node_list["txt_refresh_btn_text"].text.text = btn_str
	end

	self:FlushSelectItemsPart()
end

function BillionSubsidyView:OnB1G3ItemSelectCB(cell)
	local data = cell:GetData()
	if data then
		local count_in_cart = BillionSubsidyWGData.Instance:GetShopNumInCart(BillionSubsidyWGData.ShopType.FYMS, data.item_seq)
		if count_in_cart == 0 then
			BillionSubsidyWGCtrl.Instance:AddItemToShopCart(BillionSubsidyWGData.ShopType.FYMS, data.item_seq, 1, 1)
		else
			BillionSubsidyWGCtrl.Instance:RemoveItemToShopCart(BillionSubsidyWGData.ShopType.FYMS,data.item_seq, 1, 1)
		end
	end
end

function BillionSubsidyView:GetDataIndex(data_list)
	local need_select_list = {}
	local shop_cfg_list = BillionSubsidyWGData.Instance:GetB1G3ShopList()
	for k, cfg in ipairs(shop_cfg_list) do
		for i, data in ipairs(data_list) do
			if cfg.item_seq == data.item_seq then
				table.insert(need_select_list,k)
			end
		end
	end
	return need_select_list
end

function BillionSubsidyView:FlushSelectItemsPart()
	local count,data_list = BillionSubsidyWGData.Instance:GetShopNumInCartByType(BillionSubsidyWGData.ShopType.FYMS)
	local need_select_list = self:GetDataIndex(data_list)
	for i, v in ipairs(need_select_list) do
		self.b1g3_shop_item_grid:SetSelectCellIndexMore(v)
	end
	self.select_data_list = self.b1g3_shop_item_grid:GetAllSelectCell()

	for i = 1, 3 do
		self.b1g3_sel_result_list[i]:SetData(self.select_data_list[i])
	end

	local can_select_num = BillionSubsidyWGData.Instance:GetB1G3CanSelectNum()
	XUI.SetButtonEnabled(self.node_list["btn_b1g3_buy"], #self.select_data_list >= can_select_num)

end

function BillionSubsidyView:OnClickB1G3BuyBtn()
	local limited_type = BillionSubsidyWGData.Instance:GetB1G3TodayBuyIsLimited()
	if limited_type == 1 then
		BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
		return
	elseif limited_type == 2 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.B1G3TodayLimited)
		return
	end
	local can_select_num = BillionSubsidyWGData.Instance:GetB1G3CanSelectNum()
	if IsEmptyTable(self.select_data_list) or #self.select_data_list < can_select_num then
		return
	end

	table.sort(self.select_data_list, SortTools.KeyUpperSorter("price"))
	local max_price_item = self.select_data_list[1]
	local other_seq_list = {}
	for i = 2, #self.select_data_list do
		table.insert(other_seq_list, self.select_data_list[i].item_seq)
	end
	local other_param = self:GetB1G3RechargeParam(other_seq_list)
	BillionSubsidyWGCtrl.Instance:OpenBuyTipView(BillionSubsidyWGData.ShopType.FYMS, max_price_item.item_seq, other_param)
	--RechargeWGCtrl.Instance:Recharge(max_price_item.price, max_price_item.rmb_type, max_price_item.rmb_seq, nil, other_param)
end

function BillionSubsidyView:GetB1G3RechargeParam(rmb_seq_list)
	local recharge_param = 0
	for i, v in ipairs(rmb_seq_list) do
		recharge_param = recharge_param + v * 1000 ^ (i - 1)
	end
	return recharge_param
end

function BillionSubsidyView:OnClickB1G3RefreshBtn()
	local limited_type = BillionSubsidyWGData.Instance:GetB1G3TodayBuyIsLimited()
	if limited_type == 1 then
		BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
		return
	elseif limited_type == 2 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.B1G3TodayLimited)
		return
	end
	local cur_times, free_times, refresh_cost = BillionSubsidyWGData.Instance:GetB1G3TodayRefreshTimes()
	if cur_times < free_times then
		local count,data_list = BillionSubsidyWGData.Instance:GetShopNumInCartByType(BillionSubsidyWGData.ShopType.FYMS)
		for i, v in ipairs(data_list) do
			BillionSubsidyWGCtrl.Instance:RemoveItemToShopCart(v.shop_type, v.item_seq, 9999)
		end
		BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.PAY_ONE_GET_MORE_SHOP_REFRESH)
	else
		--确认消耗
		if not self.b1g3_refresh_alert then
			self.b1g3_refresh_alert = Alert.New()
		end
		local func = function ()
			local enough = RoleWGData.Instance:GetIsEnoughUseGold(refresh_cost)
			if enough then
				BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.PAY_ONE_GET_MORE_SHOP_REFRESH)
			else
				VipWGCtrl.Instance:OpenTipNoGold()
			end 
		end
		local str = string.format(Language.BillionSubsidy.B1G3RefreshAlert, refresh_cost)
		self.b1g3_refresh_alert:SetLableString(str)
		self.b1g3_refresh_alert:SetOkFunc(func)
		self.b1g3_refresh_alert:Open()
	end
end

-------------------------------------------
-- 付一买三 商品格子
-------------------------------------------
B1G3ShopItemRender = B1G3ShopItemRender or BaseClass(BaseRender)
function B1G3ShopItemRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_cell_pos"])
	end

	XUI.AddClickEventListener(self.node_list.btn_add_shop_cart, BindTool.Bind(self.OnClickAddShopCartBtn, self))
end

function B1G3ShopItemRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function B1G3ShopItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	local cfg = self.data.cfg
	local show_item = cfg.reward[0]
	self.item_cell:SetData(show_item)
	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(show_item.item_id)

	local cur_price = cfg.price
	self.node_list["price_text"].text.text = string.format(Language.BillionSubsidy.Price, cur_price)

	if cfg.discount and cfg.discount ~= "" then
		self.node_list["discount"]:SetActive(true)
		local discount_str = string.format(Language.BillionSubsidy.Discount, NumberToChinaNumber(cfg.discount))
		self.node_list["txt_discount"].text.text = discount_str
	else
		self.node_list["discount"]:SetActive(false)
	end

	local is_buy = self.data.buy_count > 0
	self.node_list["select_tag"]:SetActive(not is_buy)
	self.node_list["buy_tag"]:SetActive(is_buy)
	XUI.SetGraphicGrey(self.node_list["buy_tag"], is_buy)

	local limited_type = BillionSubsidyWGData.Instance:GetB1G3TodayBuyIsLimited()
	if limited_type == 1 then
		local level = BillionSubsidyWGData.Instance:GetB1G3TodayBuyNotLimitedVIPLevel()
		local str = Language.BillionSubsidy.VipNameList[level] or Language.BillionSubsidy.ShopItemLockDesc
		self.node_list["txt_select_state"].text.text = string.format(Language.BillionSubsidy.B1G3ShopItemLockDesc, str)
	end
end

function B1G3ShopItemRender:SetSelect(is_select)
	self.node_list.btn_add_shop_cart:SetActive(not is_select)
	XUI.SetGraphicGrey(self.node_list["select_tag"], is_select)
	local limited_type = BillionSubsidyWGData.Instance:GetB1G3TodayBuyIsLimited()
	if limited_type ~= 1 then
		self.node_list["txt_select_state"].text.text = is_select and Language.BillionSubsidy.Selected or Language.BillionSubsidy.UnSelect
	end
end

function B1G3ShopItemRender:OnClickAddShopCartBtn()
	local limited_type = BillionSubsidyWGData.Instance:GetB1G3TodayBuyIsLimited()
	if limited_type == 1 then
		BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
		return
	end
    if IsEmptyTable(self.data) then
        return
    end
    
    local is_add = BillionSubsidyWGCtrl.Instance:AddItemToShopCart(BillionSubsidyWGData.ShopType.FYMS, self.data.item_seq, 1, self.data.cfg.buy_limit, true)

    if is_add then
        local pos, target_pos = BillionSubsidyWGCtrl.Instance:GetNodeInScreenPos(self.node_list.item_cell_pos)
        BillionSubsidyWGCtrl.Instance:PlaySCAddItemAnim(self.data.cfg.reward[0].item_id, pos, target_pos)
    end

end

-------------------------------------------
-- 付一买三 选择预览格子
-------------------------------------------
B1G3ShopSelResultRender = B1G3ShopSelResultRender or BaseClass(BaseRender)
function B1G3ShopSelResultRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_cell_pos"])
	end
end

function B1G3ShopSelResultRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function B1G3ShopSelResultRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.node_list["item_cell_pos"]:SetActive(false)
		self.node_list["img_item_bg"]:SetActive(true)
		self.node_list["img_jia"]:SetActive(true)
		self.node_list["img_gou"]:SetActive(false)
		return
	end
	self.node_list["item_cell_pos"]:SetActive(true)
	self.node_list["img_jia"]:SetActive(false)
	
	local cfg = self.data.cfg
	local today_is_buy = BillionSubsidyWGData.Instance:GetB1G3TodayBuyIsLimited() == 2
	self.node_list["img_item_bg"]:SetActive(today_is_buy)
	self.node_list["img_gou"]:SetActive(today_is_buy)
	
	self.item_cell:SetData(cfg.reward[0])
end

---------------------------------------------------------
-- B1G3多选列表
---------------------------------------------------------
B1G3SelectShopGrid = B1G3SelectShopGrid or BaseClass(AsyncBaseGrid)
function B1G3SelectShopGrid:IsSelectMultiNumLimit(cell_index)
	if not self.select_tab[1][cell_index] then
		local limited_type = BillionSubsidyWGData.Instance:GetB1G3TodayBuyIsLimited()
		if limited_type == 1 then
			BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
			return true
		elseif limited_type == 2 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.B1G3TodayLimited)
			return true
		end
		local can_select_num = BillionSubsidyWGData.Instance:GetB1G3CanSelectNum()
		if self.cur_multi_select_num >= can_select_num then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.MergeFireworks.SelectLimit)
			return true
		end
	end
	return false
end
