local List_Vlaue = {
    [1] = 0,
    [2] = 0.35,
    [3] = 0.6,
    [4] = 0.9,
    [5] = 0.9,
    [6] = 0.9,
    [7] = 0.9,
    [8] = 0.9,
}

local model_sign = {
    left = 0,
    right = 1,
}

TianShiGodownHillView = TianShiGodownHillView or BaseClass(SafeBaseView)
function TianShiGodownHillView:__init()
    self.view_layer = UiLayer.Normal
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/tianshi_godownhill_ui_prefab", "layout_tianshi_godownhill_view")
end

function TianShiGodownHillView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_givelike"], BindTool.Bind(self.OnClickGiveLike, self))
    XUI.AddClickEventListener(self.node_list["btn_jump"], BindTool.Bind(self.OnClickJump, self))
    XUI.AddClickEventListener(self.node_list["tips"], BindTool.Bind(self.OnClickTips, self))

    self.is_load_complete = true
    self:LoginTimeCountDown()

    self.nested_scroll_rect = SNestedScrollRect.New(self.node_list["reward_parent"])
    self.left_model_index = 0
    self.right_model_index = 0

    if self.reward_list == nil then
        self.reward_list = {}
    end

    for i = 1, 8 do
        if self.node_list["reward_".. i] then
            self.reward_list[i] = TianShiGodownHillRewardItem.New(self.node_list["reward_"..i])
        end
    end

    if not self.require_info_delay then
		self.require_info_delay = GlobalTimerQuest:AddRunQuest(function ()
			TianShiGodownHillWGCtrl.Instance:ReqTianShiGodownHillInfo(OA_LIKE_OPERATE_TYPE.INFO)
		end, 10)
	end
    --self:SetParentScrollRect(self.node_list["reward_scroll"].scroll_rect)
    self.node_list["desc"].text.text = Language.TianShiGodownHill.DescTxt
end

function TianShiGodownHillView:ReleaseCallBack()

    if CountDownManager.Instance:HasCountDown("tianshi_godownhill_time") then
        CountDownManager.Instance:RemoveCountDown("tianshi_godownhill_time")
    end

    if self.model_display_left then
		self.model_display_left:DeleteMe()
		self.model_display_left = nil
	end

    if self.model_display_right then
		self.model_display_right:DeleteMe()
		self.model_display_right = nil
	end

    if self.nested_scroll_rect then
        self.nested_scroll_rect:DeleteMe()
        self.nested_scroll_rect = nil
    end

    if self.reward_list then
        for k,v in ipairs(self.reward_list) do
            if v then
                v:DeleteMe()
            end
        end
        self.reward_list = nil
    end

    if self.require_info_delay then
		GlobalTimerQuest:CancelQuest(self.require_info_delay)
		self.require_info_delay = nil
	end

    self.is_load_complete = false
    self.parent_scroll_rect = nil
    self.left_model_index = nil
    self.right_model_index = nil
end

function TianShiGodownHillView:OpenCallBack()
    TianShiGodownHillWGCtrl.Instance:ReqTianShiGodownHillInfo(OA_LIKE_OPERATE_TYPE.INFO)
end

function TianShiGodownHillView:OnFlush()
    local times_str = TianShiGodownHillWGData.Instance:GetTodayDolikeTimesStr()
    self.node_list["givelike_times"].text.text = times_str

    local all_times = TianShiGodownHillWGData.Instance:GetAllServerTimes()
    self.node_list["all_server_times"].text.text = string.format(Language.FIFAWorldCar.AllServerTimes, all_times)

    local is_dolike = TianShiGodownHillWGData.Instance:GetTodayIsDolike()
    XUI.SetButtonEnabled(self.node_list["btn_givelike"], is_dolike)

    local hotlevel_list = TianShiGodownHillWGData.Instance:GetHotLevelList()
    for k, v in ipairs(self.reward_list) do
        v:SetData(hotlevel_list[k])
    end

    local hotlevel_value = TianShiGodownHillWGData.Instance:GetAllServerTimes()
    local cur_Progress = TianShiGodownHillWGData.Instance:GetRewardCurProgress(hotlevel_value)
    self.node_list["reward_slider"].slider.value = cur_Progress

    local cur_index = 1
    for i, v in ipairs(hotlevel_list) do
        if TianShiGodownHillWGData.Instance:GetHotLevelRed(v.seq) then
            cur_index = i
            break
        end
    end

    self.node_list["reward_scroll"].scroll_rect.horizontalNormalizedPosition = List_Vlaue[cur_index]
end

function TianShiGodownHillView:ShowIndexCallBack()
    --self:FlushModel()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.TianShiGodownHillView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIKE)
end

function TianShiGodownHillView:FlushModel()
	if not self.model_display_left then
		self.model_display_left = OperationActRender.New(self.node_list.man_display)
		self.model_display_left:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.model_display_right then
		self.model_display_right = OperationActRender.New(self.node_list.woman_display)
		self.model_display_right:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    local left_cfg = TianShiGodownHillWGData.Instance:GetModelShowCfgByIndexAndSeq(model_sign.left, self.left_model_index)
    local rigth_cfg = TianShiGodownHillWGData.Instance:GetModelShowCfgByIndexAndSeq(model_sign.right, self.right_model_index)
    self:SetModelData(left_cfg, self.model_display_left, self.node_list.man_display, model_sign.left)
    self:SetModelData(rigth_cfg, self.model_display_right, self.node_list.woman_display, model_sign.right)
end

function TianShiGodownHillView:SetModelData(model_cfg, model_render, model_root, model_pos_id)
	if IsEmptyTable(model_cfg) or nil == model_render or nil == model_root then
		return
	end

    local display_data = {}
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1

	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	display_data.render_type = model_show_type - 1
    display_data.hide_model_block = true
	-- display_data.model_click_func = function ()
	-- 	TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	-- end

	model_render:SetData(display_data)

    -- 切换模型闪烁 
    ReDelayCall(self, function ()
        model_root:CustomSetActive(true)
    end, 0.15, "TianShiGodownHillVieModelDelayShow" .. model_pos_id)

	local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(model_root.rect, pos_x, pos_y)

	if model_cfg["display_scale"] then
		local scale = model_cfg["display_scale"]
		Transform.SetLocalScaleXYZ(model_root.transform, scale, scale, scale)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rotation_tab = string.split(model_cfg.rotation,"|")
		model_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end

    local change_model = function ()
        local seq = 0

        if model_sign.left == model_pos_id then
            self.left_model_index = self.left_model_index + 1
            seq = self.left_model_index
        else
            self.right_model_index = self.right_model_index + 1
            seq = self.right_model_index
        end

        seq = seq % 4
        local cfg = TianShiGodownHillWGData.Instance:GetModelShowCfgByIndexAndSeq(model_pos_id, seq)

        model_root:CustomSetActive(false)
        self:SetModelData(cfg, model_render, model_root, model_pos_id)
    end

    local time = model_cfg.time or 0

    if time > 0 then
        ReDelayCall(self, change_model, time, "TianShiGodownHillView" .. model_pos_id)
    end
end

function TianShiGodownHillView:OnClickGiveLike()
    local is_dolike = TianShiGodownHillWGData.Instance:GetTodayIsDolike()
    if not is_dolike then
        return
    end

    local bundle_name, asset_name = ResPath.GetEffectUi("UI_zhendizhan_shuibowen")
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_shuibowen"].transform)
    TianShiGodownHillWGCtrl.Instance:ReqTianShiGodownHillInfo(OA_LIKE_OPERATE_TYPE.DO_LIKE)
end

-- function TianShiGodownHillView:OnClickJump()
--     if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_SHOP) then
--         ViewManager.Instance:Open(GuideModuleName.CheapShopPurchaseView)
--     else
--         SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
--     end
-- end

function TianShiGodownHillView:OnClickJump()
    --PositionalWarfareWGCtrl.Instance:OpenGamePlayDescriptionView()
    ViewManager.Instance:Open(GuideModuleName.LandWarFbPersonView)
end

function TianShiGodownHillView:OnClickTips()
    RuleTip.Instance:SetTitle(Language.FIFAWorldCar.TipTitle)
	RuleTip.Instance:SetContent(Language.FIFAWorldCar.TipContent, nil, nil, nil, true)
end

function TianShiGodownHillView:SetParentScrollRect(scroll_rect)
    self.parent_scroll_rect = scroll_rect

    if self.is_load_complete then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end
end

function TianShiGodownHillView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIKE)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            CountDownManager.Instance:AddCountDown("tianshi_godownhill_time", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end
   
function TianShiGodownHillView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        self.node_list["act_time_txt"].text.text = string.format(Language.TianShiGodownHill.ActTimeTxt, TimeUtil.FormatSecondDHM7(valid_time))
    end
end

function TianShiGodownHillView:OnComplete()
    self.node_list["act_time_txt"].text.text = ""
    self:Close()
end


TianShiGodownHillRewardItem = TianShiGodownHillRewardItem or BaseClass(BaseRender)

function TianShiGodownHillRewardItem:LoadCallBack()
    self.node_list.button.button:AddClickListener(BindTool.Bind1(self.OnClickItem, self))
end


function TianShiGodownHillRewardItem:OnFlush()
	if not self.data then
		return
    end

    self.node_list.times.text.text = string.format(Language.TianShiGodownHill.ReawrdTimes, self.data.hot_value)

    local is_red = TianShiGodownHillWGData.Instance:GetHotLevelRed(self.data.seq)
    local is_get = TianShiGodownHillWGData.Instance:GetRewardFlag(self.data.seq)
    self.node_list["had_get"]:SetActive(is_get == 1)
    self.node_list["red"]:SetActive(is_red)
    self.node_list["effect_root"]:SetActive(is_red)
    --self.node_list["hl_img"]:SetActive(is_red)

    self.node_list["cell_pos"].image:LoadSprite(ResPath.GetTianShiGodownHillImg("a3_zdyy_qp_" .. self.data.color))
    self.node_list["box_icon"].image:LoadSprite(ResPath.GetItem(self.data.item_icon))

    local bundle_name, asset_name = ResPath.GetA2Effect("UI_zhendizhan_qipao_" .. self.data.color)
	self.node_list["effect_root1"]:ChangeAsset(bundle_name, asset_name)
end

function TianShiGodownHillRewardItem:OnClickItem()
    if not self.data then
		return
    end

    local is_red = TianShiGodownHillWGData.Instance:GetHotLevelRed(self.data.seq)
    if is_red then --领取
        TianShiGodownHillWGCtrl.Instance:ReqTianShiGodownHillInfo(OA_LIKE_OPERATE_TYPE.HOT_VALUE_REWARD, self.data.seq)
    else --查看里面物品
        local seq_cfg = TianShiGodownHillWGData.Instance:GetHotLevelSeqCfg(self.data.seq)
        if seq_cfg and seq_cfg.itemlist then
            local data_list =
			{
				view_type = RewardShowViewType.Normal,
				reward_item_list = seq_cfg.itemlist
			}
            RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
            --TianShiGodownHillWGCtrl.Instance:ShowGiftView(seq_cfg.itemlist)
        end
    end
end