local ATTR_COUNT = 6

function RoleBranchView:LoadBackgroundCallBack()
    if not self.bg_item then
        self.bg_item = ItemCell.New(self.node_list.bg_item)
    end

    if self.fs_star_list == nil then
        self.fs_star_list = {}
        for i = 1, 5 do
            self.fs_star_list[i] = self.node_list["fs_star_" .. i]
        end
    end

    -- 基础属性
    if self.attr_list == nil then
        self.attr_list = {}
        for i = 1, ATTR_COUNT do
            local attr_obj = self.node_list.attr_list:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = CommonAddAttrRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetAttrNameNeedSpace(true)
                self.attr_list[i] = cell
            end
        end
    end

    -- 初始化已孵化列表
    if not self.background_list then
        self.background_list = AsyncListView.New(SkyCurtainItemRender, self.node_list.background_list)
        self.background_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectBackGroundCB, self))
    end

    XUI.AddClickEventListener(self.node_list.btn_wear, BindTool.Bind(self.OnClickSaveBack, self))
    XUI.AddClickEventListener(self.node_list.btn_wear_down, BindTool.Bind(self.OnClickUnSaveBack, self))
    XUI.AddClickEventListener(self.node_list.btn_active, BindTool.Bind(self.OnClickActiveBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_up_level, BindTool.Bind2(self.OnClickUpLevelBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_background_attr, BindTool.Bind2(self.OnClickAddition, self))

    -- self:InitParam()
    -- self.item_change_call_back = BindTool.Bind(self.OnItemChangeCallBack, self)
    -- ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_call_back)

    -- self:CreatCPToggleList()
    self:FlushModel()
end

function RoleBranchView:OnSelectBackGroundCB(back_item, cell_index, is_default, is_click)
    if nil == back_item or nil == back_item.data then
        return
    end

    --拿到当前选中的格子下标
    if self.back_index == cell_index and (not self.is_force_refresh) then
        return
    end

    self.is_force_refresh = false
    self.back_data = back_item.data
    self.back_index = cell_index

    self:FlushBackGroundSelectModel()
    self:FlushBackGroundAttrMessage()
    self:FlushSelectItemView()
end

function RoleBranchView:OpenBackgroundCallBack()

end

function RoleBranchView:CloseBackgroundCallBack()
    RoleWGCtrl.Instance:FlushView(TabIndex.role_intro)
end

function RoleBranchView:ReleaseBackgroundCallBack()
    if self.attr_list and #self.attr_list > 0 then
        for _, attr_cell in ipairs(self.attr_list) do
            attr_cell:DeleteMe()
            attr_cell = nil
        end

        self.attr_list = nil
    end

    if self.bg_item then
        self.bg_item:DeleteMe()
        self.bg_item = nil
    end

    if self.background_list then
        self.background_list:DeleteMe()
        self.background_list = nil
    end

    if self.role_sky_curtain_model then
        self.role_sky_curtain_model:DeleteMe()
        self.role_sky_curtain_model = nil
    end

    self.fs_star_list = nil
    self.back_data = nil
    self.back_index = nil
    self.is_force_refresh = false
    self.curr_select_back_id = nil
    self.background_loader = nil
end

-- 之前的逻辑一直走刷新，试着尝试别的方式刷新
function RoleBranchView:OnFlushBackground(param_t, index)
    for k, v in pairs(param_t) do
        if "all" == k then
            self:FlushBackGroundAllMessage()

            if v.background_id then
                self:JumpFlushBackList(v.background_id)
            elseif v.open_param then
                local background_id = tonumber(v.open_param) or 0
                if background_id ~= 0 then
                    self:JumpFlushBackList(background_id)
                end
            end
        end
    end

    if self.role_sky_curtain_model then
		self.role_sky_curtain_model:PlayLastAction()
	end
end

---模型改变
function RoleBranchView:FlushModel()
    if nil == self.role_sky_curtain_model then
        local node = self.node_list.RoleDisplay
        self.role_sky_curtain_model = RoleModel.New()
        local display_data = {
            parent_node = node,
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.role_sky_curtain_model:SetRenderTexUI3DModel(display_data)
        -- self.role_sky_curtain_model:SetUI3DModel(node.transform, node.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.role_sky_curtain_model)
    end

    if self.role_sky_curtain_model then
        local role_vo = GameVoManager.Instance:GetMainRoleVo()
        local ignore_table = { ignore_wing = true, ignore_jianzhen = true, ignore_halo = true }
        self.role_sky_curtain_model:SetModelResInfo(role_vo, ignore_table)
    end
end

function RoleBranchView:FlushBackGroundAllMessage()
    -- 获取列表
    local data_list = BackgroundWGData.Instance:GetBackgroundList()
    if data_list and self.background_list then
        self.background_list:SetDataList(data_list)
    end

    local select_index = 1
    if data_list and self.back_index == nil then
        for index, back_data in ipairs(data_list) do
            if back_data and back_data.is_remind then
                select_index = index
            end
        end
    end

    if self.back_index == nil and self.background_list then
        self.background_list:JumpToIndex(select_index, 5)
    else
        self.is_force_refresh = true
        self.background_list:JumpToIndex(self.back_index, 5)
    end
end

-- 跳转刷新
function RoleBranchView:JumpFlushBackList(background_id)
    -- 获取列表
    local jump_index = 1
    local refresh_data = nil
    local data_list = BackgroundWGData.Instance:GetBackgroundList()
    for index, data in ipairs(data_list) do
        if background_id == data.background_id then
            jump_index = index
            refresh_data = data
            break
        end
    end

    if self.background_list and refresh_data then
        local cell = self.background_list:GetItemAt(jump_index)
        if nil ~= cell then
            cell:SetData(refresh_data)
        end

        self.is_force_refresh = true
        self.background_list:JumpToIndex(jump_index, 5)
    end
end

-- 刷新天幕模型
function RoleBranchView:FlushBackGroundSelectModel()
    if not self.back_data then
        return
    end

    local small_data = self.back_data
    local need_refresh_model = self.curr_select_back_id ~= self.back_data.background_id
    if need_refresh_model then
        self.curr_select_back_id = small_data.background_id
        local asset, bundle = ResPath.BackgroundShow(small_data.item_id)

        if not self.background_loader then
            local background_loader = AllocAsyncLoader(self, "base_tip_back_cell")
            background_loader:SetIsUseObjPool(true)
            background_loader:SetParent(self.node_list["background_root"].transform)
            self.background_loader = background_loader
        end
        self.background_loader:Load(asset, bundle)
    end
end

-- 刷新天幕属性
function RoleBranchView:FlushBackGroundAttrMessage()
    if not self.back_data then
        return
    end

    local small_data = self.back_data
    local small_attr_data = BackgroundWGData.Instance:GetBackgroundAttr(small_data.background_id, small_data.level) ---属性
    if self.attr_list and small_attr_data then
        for i, attr_cell in ipairs(self.attr_list) do
            attr_cell:SetVisible(small_attr_data[i] ~= nil)

            if small_attr_data[i] ~= nil then
                attr_cell:SetData(small_attr_data[i])
            end
        end
    end
end

---刷新UI
function RoleBranchView:FlushSelectItemView()
    if not self.back_data then
        return
    end

    local small_data = self.back_data
    ---获得方式
    self.node_list.label_get_chengjiu.text.text = small_data.desc
    ---展示物品
    self.bg_item:SetData({ item_id = small_data.item_id })
    self.node_list.cap_value.text.text = BackgroundWGData.Instance:GetBackgroundAttrCap(small_data.background_id,
        small_data.level)
    ---状态显示
    local item_num = ItemWGData.Instance:GetItemNumInBagById(small_data.item_id)
    local level_data = BackgroundWGData.Instance:GetBackgroundLevelData(small_data.background_id, small_data.level, false)
    local next_level_data = BackgroundWGData.Instance:GetBackgroundLevelData(small_data.background_id, small_data.level,
        true)
    if level_data then
        self.node_list.common_flag_red:SetActive(small_data.lock and item_num < level_data.stuff_num) ---未解锁
        self.node_list.btn_active:SetActive(small_data.lock and item_num >= level_data.stuff_num)     ---可激活
        local is_full_star = false
        if (not small_data.lock) and not next_level_data then                                         ---满星
            is_full_star = true
        end

        self.node_list.is_active:SetActive(false)                                           ---已激活
        self.node_list.is_max_level:SetActive(is_full_star)                                 ---已满星
        self.node_list.btn_up_level:SetActive((not small_data.lock) and (not is_full_star)) ---可升星
        local is_up_red = (not small_data.lock) and (not is_full_star) and next_level_data and item_num >= next_level_data.stuff_num
        self.node_list.background_up_level_remind:SetActive(is_up_red)
        if (not small_data.lock) and (not is_full_star) and next_level_data then
            local color = item_num >= next_level_data.stuff_num and COLOR3B.D_GREEN or COLOR3B.D_RED
            self.bg_item:SetRightBottomTextVisible(true)
            self.bg_item:SetRightBottomColorText(item_num .. '/' .. next_level_data.stuff_num, color)
        else
            self.bg_item:SetRightBottomTextVisible(false)
        end

        self.node_list.fs_stars_list:SetActive((not small_data.lock))
        -- 升级
        if (not small_data.lock) then
            local star_res_list = GetStarImgResByStar(small_data.level)
            for k, v in ipairs(self.fs_star_list) do
                v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
            end
        end
    end

    ---刷新穿戴数据
    self.node_list.btn_wear:SetActive(BackgroundWGData.Instance.curr_use_back_id ~= small_data.background_id and
    (not small_data.lock))
    self.node_list.btn_wear_down:SetActive(BackgroundWGData.Instance.curr_use_back_id == small_data.background_id and
    (not small_data.lock))
end

----------------------------------------------------------------------------------------------------------------------------------------------
---激活
function RoleBranchView:OnClickActiveBtn()
    if not self.back_data then
        return
    end

    local small_data = self.back_data
    if small_data then
        local bag_index = ItemWGData.Instance:GetItemIndex(small_data.item_id)
        BagWGCtrl.Instance:SendUseItem(bag_index, 1, 0, 0)
    end
end

---升级
function RoleBranchView:OnClickUpLevelBtn()
    if not self.back_data then
        return
    end

    local small_data = self.back_data

    if small_data and (not small_data.lock) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(small_data.item_id)
        local next_level_data = BackgroundWGData.Instance:GetBackgroundLevelData(small_data.background_id,
            small_data.level, true)
        if next_level_data and item_num >= next_level_data.stuff_num then
            BackgroundWGCtrl.Instance:BackUpLevelReq(small_data.background_id)
        else
            TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = small_data.item_id })
        end
    end
end

---穿戴
function RoleBranchView:OnClickSaveBack()
    if not self.back_data then
        return
    end

    local small_data = self.back_data
    if not IsEmptyTable(small_data) and (not small_data.lock) then
        BackgroundWGCtrl.Instance:BackUseReq(small_data.background_id)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.BackgroundNotActive)
    end
end

---卸下
function RoleBranchView:OnClickUnSaveBack()
    if not self.back_data then
        return
    end

    local small_data = self.back_data
    BackgroundWGCtrl.Instance:BackUnuseReq(small_data.background_id)
end

--点击背景总属性
function RoleBranchView:OnClickAddition()
    if not BackgroundWGData.Instance:ChechkHaveBackgroundAttr() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.BackgroundErrorInfo)
        return
    end

    BackgroundWGCtrl.Instance:OnOpenTitleTips()
end

----------------------------------灵兽已孵化培养item-----------------------
SkyCurtainItemRender = SkyCurtainItemRender or BaseClass(BaseRender)
function SkyCurtainItemRender:OnFlush()
    if not self.data then return end

    if self.node_list.remind then
        self.node_list.remind:CustomSetActive(self.data.is_remind)
    end
    self.node_list.bg.image:LoadSprite(ResPath.GetChengHaoImg(string.format("a3_tm_btn_tm%d", self.data.background_id)))
end

function SkyCurtainItemRender:OnSelectChange(is_select)
    self.node_list.img_hl:CustomSetActive(is_select)
end
