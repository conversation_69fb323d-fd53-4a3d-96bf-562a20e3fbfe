
EquipmentWGData = EquipmentWGData or BaseClass()

function EquipmentWGData:InitImperialSpiritCfg()
	self.equip_yuling_cfg = ConfigManager.Instance:GetAutoConfig("equip_yuling_cfg_auto")
	self.yuling_hole_cfg = ListToMapList(self.equip_yuling_cfg.hole, "equip_index", "hole")
	self.yuling_forge_cfg = ListToMapList(self.equip_yuling_cfg.forge, "equip_index", "hole")
	self.yuling_show_icon_cfg = self.equip_yuling_cfg.show_icon

	self.yuling_datalist = {}
	self.yuling_set_remind_list = {}
	self.yuling_strength_remind_list = {}
	self.equip_yuling_data_list = {}
	self.show_equip_list_id = {}
	self.show_equip_data_list= {}

	self.show_qinghua_equip_list_id = {}
	self.equip_active_per_data = {}
	self.equip_cap_Attr_list = {}

	self.yuling_set_cost_list = {}
	self.yuling_strength_cost_list = {}

	RemindManager.Instance:Register(RemindName.Equipment_Imperial_Spirit_Set, BindTool.Bind(self.GetEquipYuLingSetRemind, self))
	RemindManager.Instance:Register(RemindName.Equipment_Imperial_Spirit_Strength, BindTool.Bind(self.GetEquipYuLingStrengthRemind, self))
end

function EquipmentWGData:DeleteImperialSpiritCfg()
	RemindManager.Instance:UnRegister(RemindName.Equipment_Imperial_Spirit_Set)
	RemindManager.Instance:UnRegister(RemindName.Equipment_Imperial_Spirit_Strength)
end

function EquipmentWGData:GetEquipYuLingSetRemind()
	return self.yuling_set_remind_list.remind and 1 or 0
end

function EquipmentWGData:GetEquipYuLingStrengthRemind()
	return self.yuling_strength_remind_list.remind and 1 or 0
end

function EquipmentWGData:SetEquipYuLingPartInfo(protocol)
	self.yuling_datalist = self:CalculationImperialSpritDatalist(protocol.part_item_list)
end

function EquipmentWGData:EquipYuLingPartUpdate(protocol)
	local equip_index = protocol.equip_index

	if equip_index >= GameEnum.EQUIP_INDEX_TOUKUI and equip_index <= GameEnum.EQUIP_INDEX_XIANZHUO then
		local new_data = self:CalculationYuLingItemDatalist(protocol.part_item, equip_index)
		self.yuling_datalist[equip_index] = new_data
		self:CalculationEquipYuLingRemind()
		self:CalculationYuLingCostItemList()
	end

	self:UpdateEquipYuLingData(equip_index)
end

function EquipmentWGData:CalculationImperialSpritDatalist(yuling_datalist)
	local data_list = {}
	for i = 0, 9 do
		data_list[i] = self:CalculationYuLingItemDatalist(yuling_datalist[i], i)
    end	

	return data_list
end

function EquipmentWGData:CalculationYuLingItemDatalist(imperial_sprit_item_datalist, index)
	local equip_part_data = {}
	local equip_part_add_per = 0
	local can_show_yuling_icon = false
	local equip_yuling_icon = 0
	local show_yuling_icon_index = 0
	local active_per_data = {}
	local base_attribute = {}

	for j = 0, 9 do
		local data = {}
		local target_data = imperial_sprit_item_datalist[j]
		data.equip_index = index
		data.hole = j
		data.is_unlock = target_data.is_unlock
    	data.add_per = target_data.add_per
		local target_hole_cfg = self:GetImperialSpiritHoleCfg(index, j)
		data.max_per = target_hole_cfg.max_attr_per or 0
		data.name = target_hole_cfg.name or ""

		if data.is_unlock == 1 and data.add_per > 0 then
			data.show_icon = self:CalculationImperialSpritItemShowIcon(data.add_per, data.max_per)
		else
			data.show_icon = 1
		end

		equip_part_data[j] = data

		if data.is_unlock == 1 then
			can_show_yuling_icon = true
			show_yuling_icon_index = j
			equip_part_add_per = equip_part_add_per + (data.add_per / 100)

			local add_data_list = target_hole_cfg or {}
			if not IsEmptyTable(add_data_list) then
				base_attribute = self:AddYuLingAttrInfo(base_attribute, add_data_list)
			end

			table.insert(active_per_data, data)
		end
	end

	-- 传入item_data 所需数据
	equip_yuling_icon = self:GetImperialSpiritHoleCfg(index, show_yuling_icon_index).icon_show or 0
	equip_part_data.equip_index = index
	equip_part_data.can_show_yuling_icon = can_show_yuling_icon
	equip_part_data.equip_yuling_icon = equip_yuling_icon
	equip_part_data.equip_part_add_per = equip_part_add_per
	--

	--新增 策划要求战力加上孔位加成计算
	local addtion = equip_part_add_per / 100
	if addtion > 0 then
		self:AddAdditionToYuLingAttrInfo(base_attribute, addtion)
	end

	local attr_list, capability = self:OutStrAttrListAndCapalityByAttrId(base_attribute, "attr_id", "attr_value")

	if nil == self.equip_cap_Attr_list[index] then
		self.equip_cap_Attr_list[index] = {}
	end

	self.equip_cap_Attr_list[index].capability = capability
	self.equip_cap_Attr_list[index].attr_list = attr_list
	self.equip_active_per_data[index] = active_per_data

	return equip_part_data
end

function EquipmentWGData:CalculationEquipYuLingRemind()
	if IsEmptyTable(self.yuling_datalist) then
		self.yuling_strength_remind_list = {}
		self.yuling_set_remind_list = {}
		return 0
	end

	local bind_gold = RoleWGData.Instance.role_info.bind_gold or 0
	if bind_gold <= 0 then
		self.yuling_strength_remind_list = {}
		self.yuling_set_remind_list = {}
		return 0
	end

	local data_list = EquipWGData.Instance:GetDataList()
	if IsEmptyTable(data_list) then
		self.yuling_strength_remind_list = {}
		self.yuling_set_remind_list = {}
		return 0
	end

	self.yuling_set_remind_list.remind = false
	self.yuling_strength_remind_list.remind = false
	for k, v in pairs(data_list) do
		if v.index <= GameEnum.EQUIP_INDEX_XIANZHUO then
			local hole_id = #self:GetEquipActivePerData(v.index)

			if hole_id >=0 and hole_id < 10 then
				-- local cfg = self.yuling_hole_cfg[v.index][hole_id][1]
				local cfg = self:GetImperialSpiritHoleCfg(v.index, hole_id)
				if not IsEmptyTable(cfg) then
					local cost_item_id = cfg.cost_item_id
					local need_num = cfg.cost_item_num
					local cost_gold = cfg.cost_coin
					local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
	
					if need_num <= item_num and cost_gold <= bind_gold then
						self.yuling_set_remind_list[v.index] = true
						self.yuling_set_remind_list.remind = true
					else
						self.yuling_set_remind_list[v.index] = false
					end
				end
			else
				self.yuling_set_remind_list[v.index] = false
			end

			if not self.yuling_strength_remind_list[v.index] then
				self.yuling_strength_remind_list[v.index] = {}
			end
			self.yuling_strength_remind_list[v.index].remind = false

			local active_per_data_list = self:GetEquipActivePerData(v.index)
			for i, k in pairs(active_per_data_list) do
				local hole_data = self.yuling_datalist[v.index][k.hole]
				
				if not self.yuling_strength_remind_list[v.index][k.hole] then
					self.yuling_strength_remind_list[v.index][k.hole] = {}
				end
				self.yuling_strength_remind_list[v.index][k.hole].remind = false
				
				local cfg = self:GetImperialSpiritForgeCfg(v.index, k.hole)
				if not IsEmptyTable(cfg) and hole_data.add_per < hole_data.max_per then
					-- local cfg = self.yuling_forge_cfg[v.index][k.hole][1]
					local cost_item_id = cfg.cost_item_id
					local need_num = cfg.cost_item_num
					local cost_gold = cfg.cost_coin
					local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)

					if need_num <= item_num and cost_gold <= bind_gold then
						self.yuling_strength_remind_list.remind = true
						self.yuling_strength_remind_list[v.index].remind = true
						self.yuling_strength_remind_list[v.index][k.hole].remind = true
					end
				end
			end
		end
	end
end

function EquipmentWGData:CalculationYuLingCostItemList()
	self.yuling_set_cost_list = {}
	self.yuling_strength_cost_list = {}
	local data_list = EquipWGData.Instance:GetDataList()
	if IsEmptyTable(data_list) then
		return
	end

	for k, v in pairs(data_list) do
		if v.index <= GameEnum.EQUIP_INDEX_XIANZHUO then
			local hole_id = #self:GetEquipActivePerData(v.index)
			if hole_id >= 0 and hole_id < 10 then
				-- local cfg = self.yuling_hole_cfg[v.index][hole_id][1]

				local cfg = self:GetImperialSpiritHoleCfg(v.index, hole_id)
				if not IsEmptyTable(cfg) and not self.yuling_set_cost_list[cfg.cost_item_id] then
					self.yuling_set_cost_list[cfg.cost_item_id] = cfg.cost_item_id
				end
			end

			local active_data_list = self:GetEquipActivePerData(v.index)
			for i, k in pairs(active_data_list) do
				local not_max_per = k.add_per < k.max_per
				-- local cfg = self.yuling_forge_cfg[v.index][k.hole][1]
				local cfg = self:GetImperialSpiritForgeCfg(v.index, k.hole)

				if not IsEmptyTable(cfg) and not_max_per and not self.yuling_strength_cost_list[cfg.cost_item_id] then
					self.yuling_strength_cost_list[cfg.cost_item_id] = cfg.cost_item_id
				end
			end
		end
	end
end

function EquipmentWGData:CalculationImperialSpritItemShowIcon(add_per, max_per)
	table.sort(self.yuling_show_icon_cfg, SortTools.KeyLowerSorter("process"))
	local per = add_per / max_per
	local show_icon = -1
	local max_show_icon = 1

	for k, v in pairs(self.yuling_show_icon_cfg) do
		if per <= v.process / 100 then
			show_icon = v.icon
			break
		end

		max_show_icon = v.icon
	end
	
	return show_icon > 0 and show_icon or max_show_icon
end

function EquipmentWGData:AddYuLingAttrInfo(base_data, new_data)
	local attr_id, attr_value = 0, 0
	local data_len = 5

	for i = 1, data_len do
		attr_id = new_data["attr_id" .. i]
		attr_value = new_data["attr_value" .. i]

		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			if base_data["attr_id" .. i] then
				base_data["attr_value" .. i] = base_data["attr_value" .. i] + attr_value
			else
				base_data["attr_id" .. i] = attr_id
				base_data["attr_value" .. i] = attr_value + attr_value
			end
		end
	end

	return base_data
end

function EquipmentWGData:AddAdditionToYuLingAttrInfo(base_data, addition)
	local attr_id, attr_value = 0, 0
	local data_len = 5

	for i = 1, data_len do
		attr_id = base_data["attr_id" .. i]
		attr_value = base_data["attr_value" .. i]

		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			base_data["attr_value" .. i] = attr_value + math.ceil(attr_value * addition)
		end
	end

	return base_data
end

function EquipmentWGData:GetEquipImperialSpiritList(not_need_sort)
	local data_list = EquipWGData.Instance:GetDataList()

	local equip_data = {}
	local seq = 1
	for k, v in pairs(data_list) do
		if v.index <= GameEnum.EQUIP_INDEX_XIANZHUO then
			equip_data[seq] = v
			seq = seq + 1
		end
	end

	if not_need_sort then
		self.show_equip_data_list = equip_data
		return equip_data
	end

	local target_data_list = EquipmentWGData.SortEquipTabForAwake(equip_data)
	self.show_equip_data_list = target_data_list

	for k, v in pairs(target_data_list) do
		self.show_equip_list_id[v.index] = k
	end

	return target_data_list
end


function EquipmentWGData:GetYuLingDataByEquipIndex(index)
	return (self.yuling_datalist or {})[index] or {}
end

function EquipmentWGData:GetYuLingHoleData(index, hole)
	return ((self.yuling_datalist or {})[index] or {})[hole] or {}
end

function EquipmentWGData:GetImperialSpiritForgeCfg(equip_index, hole)
	return ((self.yuling_forge_cfg[equip_index] or {})[hole] or {})[1] or {}
end

function EquipmentWGData:GetImperialSpiritForgeAddPerWeight(equip_index, hole)
	local forge_cfg = self:GetImperialSpiritForgeCfg(equip_index, hole)
	local per_str = forge_cfg.add_per_weight or ""
	local min_per = 0
	local max_per = 0
	
	if per_str == "" then
		return string.format(Language.EquipmentImperialSpirit.ForgeAddPerWeight, min_per / 100, max_per / 100)
	end

	local add_per_weight = string.split(per_str, "|")
	for k, v in pairs(add_per_weight) do
		local per = string.split(v, ":")[1]
		if min_per == 0 or per < min_per then
			min_per = per
		end
	
		if max_per == 0 or per > max_per then
			max_per = per
		end
	end

	return string.format(Language.EquipmentImperialSpirit.ForgeAddPerWeight, min_per / 100, max_per / 100)
end

function EquipmentWGData:GetImperialSpiritHoleCfg(equip_index, hole)
	return ((self.yuling_hole_cfg[equip_index] or {})[hole] or {})[1] or {}
end

function EquipmentWGData:GetQingHuaImperialSpiritList()
	local qinghua_list = {}
	local target_data_list = self:GetEquipImperialSpiritList()
	if not IsEmptyTable(target_data_list) then
		for k, v in pairs(target_data_list) do
			if not IsEmptyTable(self:GetEquipActivePerData(v.index)) then
				table.insert(qinghua_list, v)
			end
		end
	end
	
	if not IsEmptyTable(qinghua_list) then
		qinghua_list = EquipmentWGData.SortEquipTabForAwake(qinghua_list)
		for k, v in ipairs(qinghua_list) do
			self.show_qinghua_equip_list_id[v.index] = k
		end
	end

	return qinghua_list
end

function EquipmentWGData:CheckIsImperialSpiritSetItem(change_item_id)
	if IsEmptyTable(self.yuling_set_cost_list) then
		return false
	end
	
	return nil ~= self.yuling_set_cost_list[change_item_id]
end

function EquipmentWGData:CheckIsImperialSpiritStrengthItem(change_item_id)
	if IsEmptyTable(self.yuling_strength_cost_list) then
		return false
	end

	return nil ~= self.yuling_strength_cost_list[change_item_id]
end

function EquipmentWGData:GetImperialSpiritSetRemind(index)
	return (self.yuling_set_remind_list or {})[index] or false
end

function EquipmentWGData:GetImperialSpiritStrengthRemind(index)
	return ((self.yuling_strength_remind_list or {})[index] or {}).remind or false
end

function EquipmentWGData:GetImperialSpiritStrengthCellRemind(index, hole)
	return (((self.yuling_strength_remind_list or {})[index] or {})[hole] or {}).remind or false
end

function EquipmentWGData:SetEquipYuLingData()
	local data_list = EquipWGData.Instance:GetDataList()

	local seq = 1
	for k, v in pairs(data_list) do
		if v.index <= GameEnum.EQUIP_INDEX_XIANZHUO then
			self.equip_yuling_data_list[seq] = v
			self.equip_yuling_data_list[seq].yuling = self.yuling_datalist[v.index]
			seq = seq + 1
		end
	end

	self:CalculationEquipYuLingRemind()
	self:CalculationYuLingCostItemList()
end

function EquipmentWGData:UpdateEquipYuLingData(equip_index)
	local data_list = EquipWGData.Instance:GetDataList()

	for k, v in pairs(data_list) do
		if v.index == equip_index and v.index <= GameEnum.EQUIP_INDEX_XIANZHUO then
			v.yuling = self.yuling_datalist[v.index]
			return
		end
	end
end

function EquipmentWGData:GetEquipActivePerData(equip_index)
	local active_per_data = {}
	if equip_index >= GameEnum.EQUIP_INDEX_TOUKUI and equip_index <= GameEnum.EQUIP_INDEX_XIANZHUO then
		if not IsEmptyTable(self.equip_active_per_data[equip_index])then
			active_per_data = self.equip_active_per_data[equip_index]
		end
	end

	return active_per_data
end

function EquipmentWGData:GetEquipCapAndAttrList(equip_index)
	local equip_cap_Attr_list = {}
	if equip_index >= GameEnum.EQUIP_INDEX_TOUKUI and equip_index <= GameEnum.EQUIP_INDEX_XIANZHUO then
		if not IsEmptyTable(self.equip_cap_Attr_list[equip_index]) then
			equip_cap_Attr_list = self.equip_cap_Attr_list[equip_index]
		end
	end

	return equip_cap_Attr_list
end

function EquipmentWGData:ShowEquipSetYuLingAttrList(equip_index)
	local current_active_num = #self:GetEquipActivePerData(equip_index)
	local cur_attr_cfg = {}
	local next_attr_cfg = {}
	local yuling_info = self:GetYuLingDataByEquipIndex(equip_index)
	local equip_part_add_per = yuling_info.equip_part_add_per or 0
	local addtion = equip_part_add_per / 100

	local attr_fun = function(attr_list_cfg, num)
		for i = 0, num do
			local cfg = self:GetImperialSpiritHoleCfg(equip_index, i)
			if not IsEmptyTable(cfg) then
				attr_list_cfg = self:AddYuLingAttrInfo(attr_list_cfg, cfg)
			end
		end
	end

	attr_fun(cur_attr_cfg, current_active_num - 1)
	attr_fun(next_attr_cfg, current_active_num)

	--加上百分比属性
	if addtion > 0 then
		self:AddAdditionToYuLingAttrInfo(cur_attr_cfg, addtion)
		self:AddAdditionToYuLingAttrInfo(next_attr_cfg, addtion)
	end

 	local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_attr_cfg, next_attr_cfg, "attr_id", "attr_value", 1, 5)
	return attr_list, current_active_num
end

---------------------------------------------------------------------------------
function EquipmentWGData:GetEquipYuLingJumpIndex(equip_index, is_yuling_set)
	if equip_index >= 0 then
		if is_yuling_set then
			if is_yuling_set and self:GetImperialSpiritSetRemind(equip_index) then
				return self.show_equip_list_id[equip_index] or 1
			end
		else
			if self:GetImperialSpiritStrengthRemind(equip_index) then
				return self.show_qinghua_equip_list_id[equip_index] or 1
			end
		end
	end

	local jump_index = -1
	for k, v in pairs(self.show_equip_data_list) do
		if is_yuling_set then
			if self:GetImperialSpiritSetRemind(v.index) then
				jump_index = self.show_equip_list_id[v.index] or 0
				break
			end
		else
			if self:GetImperialSpiritStrengthRemind(v.index) then
				jump_index = self.show_qinghua_equip_list_id[v.index] or 0
				break
			end
		end
	end

	if jump_index < 1 then
		if is_yuling_set then
			jump_index = self.show_equip_list_id[equip_index] or 1
		else
			jump_index = self.show_qinghua_equip_list_id[equip_index] or 1
		end
	end

	return jump_index
end

function EquipmentWGData:GetEquipYuLingStrengthCellJumpIndex(equip_index, hole)
	local data_list = self:GetEquipActivePerData(equip_index)
	local jump_index = -1

	local function GetHoleRemind()
		local jump_index = -1
		local data = self.yuling_strength_remind_list[equip_index]
		
		if not IsEmptyTable(data) then
			for k, v in pairs(data) do
				if type(v) == "table" and v.remind then
					jump_index = k + 1
					break
				end
			end
		end

		if hole < 0 then
			jump_index = jump_index >= 1 and jump_index or 1
		else
			jump_index = jump_index >= 1 and jump_index or hole + 1

		end

		return jump_index
	end

	if hole < 0 then
		jump_index = GetHoleRemind()
	else
		local has_remind = self:GetImperialSpiritStrengthCellRemind(equip_index, hole)

		if has_remind then
			jump_index = hole + 1
		else
			jump_index = GetHoleRemind()
		end
	end

	return jump_index
end