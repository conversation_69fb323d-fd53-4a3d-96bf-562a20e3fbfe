-- 天玑迷城 (原紫荆之巅)
function ConquestWarView:LoadIndexCallBackTJMCView()
	self:FlushFGBEnterBtnState()

	if not self.tjmc_reward_list then
		self.tjmc_reward_list = AsyncListView.New(ItemCell, self.node_list.tjmc_reward_list)
		self.tjmc_reward_list:SetStartZeroIndex(true)
	end

	local data = ConquestWarWGData.Instance:GetConquestWarActivityInfoById(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
	if not IsEmptyTable(data) then
		self.tjmc_reward_list:SetDataList(data.activity_item)

		self.node_list["txt_tjmc_title"].text.text = data.activity_title
		self.node_list["txt_tjmc_open_time"].text.text = string.format(Language.FlagGrabbingBattlefield.ActivityTime1, data.time_1, data.time_2)
		self.node_list["txt_tjmc_desc"].text.text = data.activity_illustrate
	end
	
	XUI.AddClickEventListener(self.node_list.go_tjmc_btn, BindTool.Bind1(self.OnClickGoToTJMC, self))
	XUI.AddClickEventListener(self.node_list.btn_tjmc_reward_show, BindTool.Bind(self.OnClickTJMCRewardShow, self))

	self.tjmc_activity_change_callback = BindTool.Bind(self.OnTJMCActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.tjmc_activity_change_callback)
	
	self.tjmc_remind_callback = BindTool.Bind(self.TJMCRemindCallBack, self)
	RemindManager.Instance:Bind(self.tjmc_remind_callback, RemindName.YeZhanWangCheng)
end

function ConquestWarView:ReleaseTJMCView()
	if self.tjmc_reward_list then
		self.tjmc_reward_list:DeleteMe()
		self.tjmc_reward_list = nil
	end

	if self.tjmc_activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.tjmc_activity_change_callback)
		self.tjmc_activity_change_callback = nil
	end
	
	RemindManager.Instance:UnBind(self.tjmc_remind_callback)
end

function ConquestWarView:TJMCShowIndexCallBack()

end

-- 红点变化回调
function ConquestWarView:TJMCRemindCallBack(remind_name, num)
	if remind_name == RemindName.YeZhanWangCheng then
		self.node_list.tjmc_reward_red_point:SetActive(num > 0)
	end
end

function ConquestWarView:OnClickTJMCRewardShow()
	KuafuYeZhanWangChengWGCtrl.Instance:OpenSeeGiftView()
end

function ConquestWarView:OnFlushTJMCView(param_t, index)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
	if not activity_info then return end

	self:FlushTJMCBtnState(activity_info.status)
end

function ConquestWarView:OnClickGoToTJMC()
	local info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
	if not info then return end

	if info.status ~= ACTIVITY_STATUS.OPEN and info.status ~= ACTIVITY_STATUS.STANDY then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.NotOpenAct))
		return
	end

	local open_level = KuafuYeZhanWangChengWGData.Instance:GetLimitLevel()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < open_level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FlagGrabbingBattlefield.LevelLimitJoin, RoleWGData.GetLevelString2(open_level)))
		return
	end

    KuafuYeZhanWangChengWGCtrl.Instance:SendNightFightEnterReq()
end

function ConquestWarView:OnTJMCActChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG and self:IsOpen() then
		self:FlushTJMCBtnState(status)
	end
end

function ConquestWarView:FlushTJMCBtnState(status)
	local is_finish =  ActivityWGData.Instance:GetActDoubleSideFBIsFinish(ACTIVITY_TYPE.KUAFUYEZHANWANGCHENG)
	if status == ACTIVITY_STATUS.CLOSE then
		self.node_list["go_tjmc_btn"]:CustomSetActive(false)
		self.node_list["go_jtmc_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.NotOpenDesc
	elseif status == ACTIVITY_STATUS.STANDY then
		self.node_list["go_tjmc_btn"]:CustomSetActive(true)
		self.node_list["go_jtmc_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Standy
	elseif is_finish then
		self.node_list["go_tjmc_btn"]:CustomSetActive(true)
		self.node_list["go_jtmc_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Finish
	elseif status == ACTIVITY_STATUS.OPEN then
		self.node_list["go_tjmc_btn"]:CustomSetActive(true)
		self.node_list["go_jtmc_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Doing
	end
end
