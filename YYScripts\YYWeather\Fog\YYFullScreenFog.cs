﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;

public class YYFullScreenFog : YYFog<YYFullScreenFogProfile>
{

    private YYFullScreenEffect effect;

    private const string commandBufferName = "WeatherMakerFullScreenFogScript";


    [Header("Full screen fog - rendering")]
    [Tooltip("Material to render the fog full screen after it has been calculated")]
    public Material FogFullScreenMaterial;

    [Tooltip("Fog Blur Material.")]
    public Material FogBlurMaterial;





    public CameraEvent FogRenderQueue = CameraEvent.BeforeForwardAlpha;

    // ///
    private System.Action<WeatherCommandBuffer> updateShaderPropertiesAction;

    private void Awake()
    {
        effect = new YYFullScreenEffectFog// new YYFullScreenEffect
        {
            CommandBufferName = commandBufferName,
            RenderQueue = FogRenderQueue

        };
    }

    protected override void OnEnable()
    {
        base.OnEnable();
        if(YYCommandBufferManager.Instance!=null)
        {
            YYCommandBufferManager.Instance.RegisterPreCull(CameraPreCull, this);
            YYCommandBufferManager.Instance.RegisterPreRender(CameraPreRender, this);

        }
    }

    protected override void OnDisable()
    {
        base.OnDisable();
        if (effect != null)
        {
            effect.Dispose();
        }
    }


    private void CameraPreRender(Camera camera)
    {
        if (effect != null)// && !WeatherMakerScript.ShouldIgnoreCamera(this, camera, !AllowReflections))
        {
            effect.PreRenderCamera(camera);
        }
    }

    private void CameraPreCull(Camera camera)
    {
        if (effect != null)
        {
            UpdateFogProperties(camera);
            effect.PreCullCamera(camera);
        }
    }

    private void UpdateFogProperties(Camera camera)
    {
        updateShaderPropertiesAction = (updateShaderPropertiesAction ?? UpdateShaderProperties);

        //effect.SetupEffect(FogMaterial,true,null, updateShaderPropertiesAction,true);
        effect.SetupEffect(FogMaterial,FogBlurMaterial, updateShaderPropertiesAction, true, null);
    }

    private void UpdateShaderProperties(WeatherCommandBuffer b)
    {
        //
        if (FogProfile == null)
        {
            return;
        }

        FogProfile.UpdateMaterialProperties(b.Material, b.Camera, true);
    }
}
