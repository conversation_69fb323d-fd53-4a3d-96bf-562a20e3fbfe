------------------------------------------------------------
--小游戏任务
------------------------------------------------------------
TaskDice = TaskDice or BaseClass(SafeBaseView)

local HAND_TYPE = {
	PAPER = 1,  					-- 包
	SCISSORS = 2,  					-- 剪
	ROCK = 3, 						-- 锤
}

local TO_WIN_HAND_TYPE = {
	[HAND_TYPE.PAPER] = HAND_TYPE.SCISSORS, 
	[HAND_TYPE.SCISSORS] = HAND_TYPE.ROCK, 
	[HAND_TYPE.ROCK] = HAND_TYPE.PAPER, 
}

local TO_LOSE_HAND_TYPE = {
	[HAND_TYPE.SCISSORS] = HAND_TYPE.PAPER, 
	[HAND_TYPE.ROCK] = HAND_TYPE.SCISSORS, 
	[HAND_TYPE.PAPER] = HAND_TYPE.ROCK, 
}

function TaskDice:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/task_dice_prefab", "layout_dice")
	self.is_success = true
	self.dice_cfg_t = ListToMap(ConfigManager.Instance:GetAutoConfig("tasklist_auto").dice_task, "id", "rounds")
	self.round = 1
	self.id = 2
end

function TaskDice:__delete()

end

function TaskDice:ReleaseCallBack()
	self:CancelCloseTimer()
	self:CancelSelectTimer()
	self:CancelShowNextTimer()
	self:CancelResultTimer()
	self:CancelFixTimer()
	self.animater = nil
	if self.role then
		self.role:DeleteMe()
		self.role = nil
	end

	if self.npc then
		self.npc:DeleteMe()
		self.npc = nil
	end
end

function TaskDice:ShowView(task_cfg)
	self.task_cfg = task_cfg
	self.round = 1
	self.id = task_cfg and task_cfg.c_param5 or 2
	self.dice_cfg = self.dice_cfg_t[self.id] and self.dice_cfg_t[self.id][1]
	if self.dice_cfg then
		self:Open()
	end
end

function TaskDice:ShowViewByDiceId(dice_id)
	self.round = 1
	self.id = dice_id
	self.dice_cfg = self.dice_cfg_t[self.id] and self.dice_cfg_t[self.id][1]
	if self.dice_cfg then
		self:Open()
	end
end

function TaskWGData:OpenCallBack()
	TaskGuide.Instance:CanAutoAllTask(false)
end

function TaskDice:CloseCallBack()
	TaskGuide.Instance:CanAutoAllTask(true)
	if self.task_cfg then
		TaskWGCtrl.Instance:SendTaskCommit(self.task_cfg.task_id)
	end
end

function TaskDice:LoadCallBack()
	self.animater = self.node_list.dice_panel.animator

	-- 主角模型
	self.role = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["right_model"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = false,
	}
	
	self.role:SetRenderTexUI3DModel(display_data)
    -- self.role:SetUI3DModel(self.node_list["right_model"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_jianzhen = true}
	self.role:SetModelResInfo(role_vo, special_status_table)
	-- self.role:FixToOrthographic(self.root_node_transform)

	-- NPC模型
	self.npc = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["left_model"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.S,
		can_drag = false,
	}
	
	self.npc:SetRenderTexUI3DModel(display_data)
    -- self.npc:SetUI3DModel(self.node_list["left_model"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)

    XUI.AddClickEventListener(self.node_list["btn_hand_1"], BindTool.Bind(self.OnClickHandBtn, self, HAND_TYPE.PAPER))
    XUI.AddClickEventListener(self.node_list["btn_hand_2"], BindTool.Bind(self.OnClickHandBtn, self, HAND_TYPE.SCISSORS))
    XUI.AddClickEventListener(self.node_list["btn_hand_3"], BindTool.Bind(self.OnClickHandBtn, self, HAND_TYPE.ROCK))
    XUI.AddClickEventListener(self.node_list["btn_cheats"], BindTool.Bind(self.OnClickCheats, self))
    XUI.AddClickEventListener(self.node_list["close_btn"], BindTool.Bind(self.Close, self))
    self.fix_timer = GlobalTimerQuest:AddRunQuest(function()
    	self:FixModelToOrthographic()
    end, 0.1)
end

-- 修正模型旋转, 模型世界坐标改了之后需要修正旋转
function TaskDice:FixModelToOrthographic()
	-- if self.role then
		-- self.role:FixToOrthographic(self.root_node_transform)
	-- end
	-- if self.npc then
		-- self.npc:FixToOrthographic(self.root_node_transform)
	-- end
end

function TaskDice:CancelFixTimer()
	if self.fix_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.fix_timer)
		self.fix_timer = nil
	end
end

function TaskDice:ShowIndexCallBack()
	self.round = 0
	self.is_win = false
	self:ShowNextRound()
	self.node_list.close_tips:SetActive(false)
end

-- 进入下一回合
function TaskDice:ShowNextRound()
	self.is_cheats = false
	self.player_hand_type = HAND_TYPE.PAPER
	self.npc_hand_type = HAND_TYPE.ROCK
	self.round = self.round + 1
	self.dice_cfg = self.dice_cfg_t[self.id][self.round]

	if not self.dice_cfg then
		print_error("骰子任务没取到配置，请检查配置表！, id:", self.id, "round:", self.round)
		self:Close()
	end

	self.node_list["left_hand"].raw_image:LoadSpriteAsync(ResPath.GetDiceRawImage("hand_" .. HAND_TYPE.ROCK))
	self.node_list["right_hand"].raw_image:LoadSpriteAsync(ResPath.GetDiceRawImage("hand_" .. HAND_TYPE.ROCK))
	self.node_list["success"]:SetActive(false)
	self.node_list["fail"]:SetActive(false)

	self:FlushNPCImg()
	self:FlushStartText()
	self:ShowHandBtn()
	self:ShowAutoSelectTips()
end

function TaskDice:ShowAnim()
	self.node_list.auto_select_tips:SetActive(false)

	-- 计算结果
	self:CalResult()

	self.node_list["anim_hand_panel"]:SetActive(true)
	self.node_list["hand_panel"]:SetActive(false)

	self:CancelResultTimer()
	self.show_result_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if not self.node_list["anim_hand_panel"] then
			return
		end
		self.node_list["anim_hand_panel"]:SetActive(false)
		self.node_list["hand_panel"]:SetActive(true)
		self:ShowResult()
	end, 1.85)

	self.show_result_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if not self.node_list["success"] then
			return
		end
		self:ShowResultEffect()
	end, 3.85)
end

-- 展示本回合结果
function TaskDice:ShowResult()
	self.node_list["left_hand"].raw_image:LoadSpriteAsync(ResPath.GetDiceRawImage("hand_" .. self.npc_hand_type))
	self.node_list["right_hand"].raw_image:LoadSpriteAsync(ResPath.GetDiceRawImage("hand_" .. self.player_hand_type))
end

function TaskDice:ShowResultEffect()
	self.node_list["hand_panel"]:SetActive(false)
	self.node_list["success"]:SetActive(self.is_win)
	self.node_list["fail"]:SetActive(not self.is_win)

	if self.is_win then
		self.npc:SetTrigger("attack1") -- npc生气动画
	else
		self.npc:SetTrigger("attack2") -- npc拍手动画
	end

	if self.is_win then
		self:ShowCloseTips()
	else
		self:DelayShowNextRound()
	end
	self:FlushResultText()
end

-- 延迟进入下一回合
function TaskDice:DelayShowNextRound()
	self:CancelShowNextTimer()
	self.show_next_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if not self.node_list["left_hand"] then
			return
		end
		self:ShowNextRound()
	end, 2.5)
end

-- 刷新NPC形象
function TaskDice:FlushNPCImg()
	self.node_list.npc_img:SetActive(self.dice_cfg.pic_id ~= "")
	self.node_list.left_model:SetActive(self.dice_cfg.modle_id ~= "")
	if self.dice_cfg.modle_id ~= "" then
		local bundle, asset = ResPath.GetNpcModel(self.dice_cfg.modle_id)
		local last_bundle, last_asset = self.npc:GetCurAssetPath()
		if bundle ~= last_bundle or asset ~= last_asset then
			self.npc:SetMainAsset(bundle, asset, function()
				self.npc:SetTrigger("attack3")
			end)
			-- self.npc:FixToOrthographic(self.root_node_transform)
		end
	end
	if self.dice_cfg.pic_id ~= "" then
		local asset_name, bundle_name = ResPath.GetF2RawImagesPNG(self.dice_cfg.pic_id)
		self.node_list.npc_img.raw_image:LoadSprite(asset_name, bundle_name, function ()
			self.node_list.npc_img.raw_image:SetNativeSize()
		end)
	end
end

-- 刷新本轮开始文字
function TaskDice:FlushStartText()
	self.node_list["npc_talk_dec"].emoji_text.Padding = Vector2(0, 10)
	self.node_list["role_talk_dec"].emoji_text.Padding = Vector2(0, 10)
	
	EmojiTextUtil.ParseRichText(self.node_list["npc_talk_dec"].emoji_text, self.dice_cfg.opp_start_dec, 24)
	self.node_list["npc_talk"]:SetActive(false)
	self.node_list["npc_talk"]:SetActive(true)

	self.node_list["role_talk"]:SetActive(false)
	GlobalTimerQuest:AddDelayTimer(function()  
		if self.node_list["role_talk_dec"] then
			EmojiTextUtil.ParseRichText(self.node_list["role_talk_dec"].emoji_text, self.dice_cfg.my_pre_dec, 24)
			self.node_list["role_talk"]:SetActive(true)
		end
	end, self.dice_cfg.param)

	self.node_list["npc_name"].text.text = self.dice_cfg.opponent_name
	local role_vo = RoleWGData.Instance:GetRoleVo()
	self.node_list["role_name"].text.text = role_vo.name
end

-- 刷新本轮结果文字
function TaskDice:FlushResultText()
	if self.is_win then
		EmojiTextUtil.ParseRichText(self.node_list["npc_talk_dec"].emoji_text, self.dice_cfg.opp_end_lose_dec, 24)
		EmojiTextUtil.ParseRichText(self.node_list["role_talk_dec"].emoji_text, self.dice_cfg.my_win_dec, 24)
	else
		EmojiTextUtil.ParseRichText(self.node_list["npc_talk_dec"].emoji_text, self.dice_cfg.opp_end_win_dec, 24)
		EmojiTextUtil.ParseRichText(self.node_list["role_talk_dec"].emoji_text, self.dice_cfg.my_fail_dec, 24)
	end
	self.node_list["npc_talk"]:SetActive(false)
	self.node_list["npc_talk"]:SetActive(true)
	self.node_list["role_talk"]:SetActive(false)
	self.node_list["role_talk"]:SetActive(true)
end

-- 计算结果
function TaskDice:CalResult()
	-- 如果不是出猫
	if not self.is_cheats then
		self.is_win = math.random(1, 100) <= self.dice_cfg.win_rate
		-- 如果本回合玩家赢了
		if self.is_win then
			self.npc_hand_type = TO_LOSE_HAND_TYPE[self.player_hand_type]
		else
			self.npc_hand_type = TO_WIN_HAND_TYPE[self.player_hand_type]
		end
	else
		self.is_win = true
		self.npc_hand_type = math.random(1, 3)
		self.player_hand_type = TO_WIN_HAND_TYPE[self.npc_hand_type]
	end
end

-- 选中手势
function TaskDice:OnClickHandBtn(hand_type)
	self:CancelSelectTimer()
	self.player_hand_type = hand_type
	self:HideOtherHandBtn()
	self:ShowAnim()
end

-- 点击出猫
function TaskDice:OnClickCheats(hand_type)
	self:CancelSelectTimer()
	self.is_cheats = true
	self:HideOtherHandBtn()
	self:ShowAnim()
end

-- 隐藏自己选中按钮以外的按钮
function TaskDice:HideOtherHandBtn()
	for hand_type = 1, 3 do
		if self.is_cheats or hand_type ~= self.player_hand_type then
			self.node_list["btn_hand_" .. hand_type]:SetActive(false)
		end
		self.node_list["btn_hand_" .. hand_type].button.interactable = false
		self.node_list["btn_hand_" .. hand_type].image:LoadSpriteAsync(ResPath.GetTaskDiceImg("dice_btn_bg_hl"))
		self.node_list["btn_icon_" .. hand_type].image:LoadSpriteAsync(ResPath.GetTaskDiceImg("hand_" .. hand_type .. "_small_hl"))
	end
	self.node_list["btn_cheats"]:SetActive(self.is_cheats)
	self.node_list["btn_cheats"].button.interactable = false
end

-- 显示按钮
function TaskDice:ShowHandBtn()
	for hand_type = 1, 3 do
		self.node_list["btn_hand_" .. hand_type]:SetActive(true)
		self.node_list["btn_hand_" .. hand_type].button.interactable = true
		self.node_list["btn_hand_" .. hand_type].image:LoadSpriteAsync(ResPath.GetTaskDiceImg("dice_btn_bg"))
		self.node_list["btn_icon_" .. hand_type].image:LoadSpriteAsync(ResPath.GetTaskDiceImg("hand_" .. hand_type .. "_small"))
	end
	self.node_list["btn_cheats"]:SetActive(not (self.dice_cfg.show_cheats == 0))
	self.node_list["btn_cheats"].button.interactable = true
end

-- 自动关闭提示
function TaskDice:ShowCloseTips()
	self:CancelCloseTimer()

	self.node_list.close_tips:SetActive(true)
	self.node_list.close_time.text.text = string.format(Language.Task.SecondStr, 3)
	self.time = 3
	self.close_timer_quest = GlobalTimerQuest:AddTimesTimer(function ()
		self.time = self.time - 1
		self.node_list.close_time.text.text = string.format(Language.Task.SecondStr, self.time)
		if self.time <= 0 then
			self.time = nil
			self:Close()
			self:CancelCloseTimer()
		end
	end, 1, 3)
end

-- 自动选中提示
function TaskDice:ShowAutoSelectTips()
	self:CancelSelectTimer()

	self.node_list.auto_select_tips:SetActive(true)
	self.node_list.select_tips_time.text.text = string.format(Language.Task.SecondStr, 10)
	local select_time = 10
	self.auto_select_timer_quest = GlobalTimerQuest:AddTimesTimer(function ()
		select_time = select_time - 1
		self.node_list.select_tips_time.text.text = string.format(Language.Task.SecondStr, select_time)
		if select_time <= 0 then
			self:OnClickHandBtn(HAND_TYPE.PAPER)
		end
	end, 1, 10)
end

function TaskDice:CancelResultTimer()
	if self.show_result_timer then
		GlobalTimerQuest:CancelQuest(self.show_result_timer)
		self.show_result_timer = nil
	end
	if self.show_result_effect_timer then
		GlobalTimerQuest:CancelQuest(self.show_result_effect_timer)
		self.show_result_effect_timer = nil
	end
end

function TaskDice:CancelShowNextTimer()
	if self.show_next_delay_timer then
		GlobalTimerQuest:CancelQuest(self.show_next_delay_timer)
		self.show_next_delay_timer = nil
	end
end

function TaskDice:CancelCloseTimer()
	if self.close_timer_quest then
		GlobalTimerQuest:CancelQuest(self.close_timer_quest)
		self.close_timer_quest = nil
	end
end

function TaskDice:CancelSelectTimer()
	if self.auto_select_timer_quest then
		GlobalTimerQuest:CancelQuest(self.auto_select_timer_quest)
		self.auto_select_timer_quest = nil
	end
end