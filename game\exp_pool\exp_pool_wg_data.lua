ExpPoolWGData = ExpPoolWGData or BaseClass()

function ExpPoolWGData:__init()
	if ExpPoolWGData.Instance then
		ErrorLog("[ExpPoolWGData]:Attempt to create singleton twice!")
	end

	ExpPoolWGData.Instance = self
    RemindManager.Instance:Register(RemindName.ExpPoolView, BindTool.Bind(self.IsCanGetVipExtraRoleExp, self))
	self.is_get_exp_flag = 1
end

function ExpPoolWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.ExpPoolView)
	ExpPoolWGData.Instance = nil
end

--服务端下发,当前经验池内经验值
function ExpPoolWGData:PoolExp()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	return vo.vip_extra_role_exp or 0
end

function ExpPoolWGData:SetSCVipRoleExpInfo(protocol)
	self.is_get_exp_flag = protocol.get_exp_flag
end

function ExpPoolWGData:IsGetVipExtraRoleExp()
	return self.is_get_exp_flag == 1
end

function ExpPoolWGData:IsCanGetVipExtraRoleExp()
	local is_get = self:IsGetVipExtraRoleExp()

	if is_get then
		return 0
	end

	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local is_vip = VipWGData.Instance:IsVip()

	if is_vip and role_vip >= 6 then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local cur_exp = role_vo.vip_extra_role_exp
		
		if cur_exp > 0 then
			return 1
		end

		return 0
	end
	
	return 0
end

--读配置读配置
-- function ExpPoolWGData:PoolMaxExp()
-- 	local vo_level = GameVoManager.Instance:GetMainRoleVo().level
-- 	local exp_limit_cfg = ConfigManager.Instance:GetAutoConfig("vip_role_exp_pool_auto").exp_limit_cfg
-- 	for k,v in pairs(exp_limit_cfg) do
-- 		if vo_level == v.lv then
-- 			return v.exp_limit
-- 		end
-- 	end
-- end

-- --读配置读配置
-- function ExpPoolWGData:PoolOtherCfgExp()
-- 	local other_cfg = ConfigManager.Instance:GetAutoConfig("vip_role_exp_pool_auto").other[1]
-- 	return other_cfg
-- end