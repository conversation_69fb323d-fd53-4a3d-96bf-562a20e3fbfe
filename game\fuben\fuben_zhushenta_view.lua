--废弃的
--诛神塔场景面板
ZhuShenTaView = ZhuShenTaView or BaseClass(SafeBaseView)

function ZhuShenTaView:__init()
	self:AddViewResource(0, "uis/view/team_equip_fb_prefab", "layout_team_fuben_info")
	self.order = 1
	self.fb_star = -1 --开始星数
	self.load_info = false
	
	self.active_close = false
end

function ZhuShenTaView:__delete()

end

function ZhuShenTaView:ReleaseCallBack()
	self.end_time_bar = nil
	if CountDownManager.Instance:HasCountDown("star_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("star_time_countdown")
	end
	if CountDownManager.Instance:HasCountDown("kill_one_countdown") then
		CountDownManager.Instance:RemoveCountDown("kill_one_countdown")
	end
	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
	end

	if self.info then
		local obj = self.info.node_list.layout_fuben_zhushenta_info.gameObject
		self.info:DeleteMe()
		ResMgr:Destroy(obj)
		self.info = nil
	end
	self.fuben_data = nil
	self.start_fight_event = nil
	self.layout_fuben_info = nil
	self.layout_tower_end_time = nil
	self.layout_star = nil
	self.load_info = false
	self.is_boss_time = nil
	self.level = nil
end

function ZhuShenTaView:CloseCallBack()
	self.fb_star = -1
	MainuiWGCtrl.Instance:SetAutoGuaJi(false)
	MainuiWGCtrl.Instance:ResetTaskPanel()
	MainuiWGCtrl.Instance:SetFBNameState(false)
	if self.info then
		local obj = self.info.node_list.layout_fuben_zhushenta_info.gameObject
		self.info:DeleteMe()
		ResMgr:Destroy(obj)
		self.info = nil
	end
end

function ZhuShenTaView:LoadCallBack()
	self.start_fight_event = BindTool.Bind(self.StartFight,self)
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
	self.star_move_pos_1 = self.node_list.pos_1.transform.position.x
	self.star_move_pos_2 = self.node_list.pos_2.transform.position.x
	self.star_move_pos_3 = self.node_list.pos_3.transform.position.x
	self.node_list.layout_star.transform.position = self.node_list.pos_3.transform.position
end

--第一段位移
function ZhuShenTaView:FirstMoveAni()
	if not self:IsLoaded() then
		GlobalTimerQuest:AddDelayTimer(function()
				self:FirstMoveAni()
			end,0.5)
		return
	end

	self.node_list.layout_star.transform:DOMoveX(self.star_move_pos_2, 2):OnComplete(function()
		-- self:WaitForMonment()
	end):SetEase(DG.Tweening.Ease.Linear)
end

function ZhuShenTaView:WaitForMonment()
	GlobalTimerQuest:AddDelayTimer(function()
				self:SecondMoveAni()
			end,1.5)
	
end

--第二段位移
function ZhuShenTaView:SecondMoveAni()
	if nil == self.node_list.layout_star then return end
	self.node_list.layout_star.transform:DOMoveX(self.star_move_pos_1, 2):OnComplete(function()
		
	end):SetEase(DG.Tweening.Ease.Linear)
end

function ZhuShenTaView:ShowIndexCallBack()
	self:InitStartPosition()
end

function ZhuShenTaView:InitStartPosition()
	if nil == self.node_list.pos_1 then return end
	self.star_move_pos_1 = self.node_list.pos_1.transform.position.x
	self.star_move_pos_2 = self.node_list.pos_2.transform.position.x
	self.star_move_pos_3 = self.node_list.pos_3.transform.position.x
	self.node_list.layout_star.transform.position = self.node_list.pos_3.transform.position
end

function ZhuShenTaView:InitCallBack()
	MainuiWGCtrl.Instance:SetTaskPanel(false,152,-200)

	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text3)

	if not self.load_info then
		ResMgr:LoadGameobjSync("uis/view/team_equip_fb_prefab","layout_fuben_zhushenta_info",
			function (obj)
				local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
				obj.transform:SetParent(parent.transform,false)
				obj.transform.localPosition = Vector3.zero
				obj = U3DObject(obj)
				self.info = ZhuShenTaInfo.New(obj)
				self.load_info = true
				if self.need_flush then
					self.need_flush = false
					self:Flush()
				end
			end)
		MainuiWGCtrl.Instance:SetTaskPanel(false,152,-179)
	end
	self.fuben_data = FuBenWGData.Instance

	self.layout_fuben_info = self.node_list.layout_fuben_info
	self.layout_tower_end_time = self.node_list.layout_tower_end_time_node
	self.layout_star = self.node_list.layout_star

	--self.layout_star:SetActive(false)

	self.end_time_bar = self.node_list.down_time

	self:Flush()
end

--开始战斗
function ZhuShenTaView:StartFight()
	MainuiWGCtrl.Instance:SetAutoGuaJi(true)
	self:FirstMoveAni()
end

function ZhuShenTaView:OnFlush()
	if not self.load_info then
		self.need_flush = true
		return
	end
	local copper_scence_info = self.fuben_data:GetZhushenTaFbInfo()
	-- self.info:SetLevel(string.format(Language.FbWushuang.RankValue,copper_scence_info.level + 1))
	MainuiWGCtrl.Instance:SetFBNameState(true, string.format(Language.FuBenPanel.ZhuShenTaRankValue, copper_scence_info.level + 1))
	if 0 ~= copper_scence_info.prepare_end_timestamp and copper_scence_info.prepare_end_timestamp > TimeWGCtrl.Instance:GetServerTime() then
		-- print_error("诛神塔时间",copper_scence_info.prepare_end_timestamp - TimeWGCtrl.Instance:GetServerTime())
		self:FlushPrepareTime()
	end
	if not self.level or self.level ~= copper_scence_info.level + 1 then
		FuBenWGCtrl.Instance:OpenFuBenLayerView(copper_scence_info.level + 1)
	end
	self.level = copper_scence_info.level + 1

	 if copper_scence_info then
	 	if copper_scence_info.cur_star_num <= 0 then
	 		self:CompleteTime()
	 	elseif copper_scence_info.cur_star_num > 0 then --self.fb_star ~= copper_scence_info.cur_star_num and
			local time = copper_scence_info.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime()

			self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), copper_scence_info.next_star_timestamp)
			if CountDownManager.Instance:HasCountDown("star_time_countdown") then
				CountDownManager.Instance:RemoveCountDown("star_time_countdown")
			end
			CountDownManager.Instance:AddCountDown("star_time_countdown", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), nil, time, 1)
	 	end
	 	if self.fb_star ~= copper_scence_info.cur_star_num and copper_scence_info.cur_star_num ~= 3 then
	 		self:InitStartPosition()
			self:FirstMoveAni()
	 	end

		self.fb_star = copper_scence_info.cur_star_num

		--self.node_list["img_star_" .. 1].image:LoadSprite(ResPath.GetCommon("start_small"))

		self.node_list.img_wu:SetActive(false)
		self.node_list.layout_star_1:SetActive(copper_scence_info.cur_star_num > 0)
		if copper_scence_info.cur_star_num > 0 then
			for i = 1, copper_scence_info.cur_star_num do
		   		--self.node_list["img_star_" .. i]:SetActive(true)
			end
			if 1 == copper_scence_info.cur_star_num then
				--self.node_list["img_star_1"].image:LoadSprite(ResPath.GetCommon("start_big"))
			end
		else
			self.node_list.img_wu:SetActive(true)
		end
	end


	local cur_level_cfg = self.fuben_data:GetMonsterTotalCountBylayout(copper_scence_info.level)
	local str = string.format(Language.FuBen.Label2,0,cur_level_cfg.com_monster_num)
	if  copper_scence_info.boss_count ~= 0 or self.is_boss_time then --copper_scence_info.monster_count == 0 or     ---copper_scence_info.monster_count == 0 and
		self.is_boss_time = true
		str = string.format(Language.FuBen.Label4, cur_level_cfg.boss_count-copper_scence_info.boss_count,cur_level_cfg.boss_count)
		self.info:SetBoSui(str)
	elseif copper_scence_info.monster_count > 0 then
		local kill_monster_count = cur_level_cfg.com_monster_num - copper_scence_info.monster_count
		str = string.format(Language.FuBen.Label2,kill_monster_count,cur_level_cfg.com_monster_num)
		self.info:SetBoSui(str)
	end

	self.info:SetRichtxt(cur_level_cfg.fb_des)

	if CountDownManager.Instance:HasCountDown("kill_one_countdown") then
		CountDownManager.Instance:RemoveCountDown("kill_one_countdown")
	end
	if FuBenWGData.Instance:GetIsCountDownKillBoss() then
		self.node_list["boss_fuhuo"]:SetActive(true)
		local time_stamp = FuBenWGData.Instance:GetKillBossTimeStamp()
		local time = time_stamp - TimeWGCtrl.Instance:GetServerTime()
		self:UpdateKillBossCountDown(TimeWGCtrl.Instance:GetServerTime(),time_stamp)
		CountDownManager.Instance:AddCountDown("kill_one_countdown", BindTool.Bind1(self.UpdateKillBossCountDown, self), BindTool.Bind1(self.CompleteTimeKillBoss, self), nil, time, 1)
	else
		self.node_list["boss_fuhuo"]:SetActive(false)
	end
end

function ZhuShenTaView:SetBossFuHuoImage(is_active)
	if self.node_list["boss_fuhuo"] then
		self.node_list["boss_fuhuo"]:SetActive(is_active)
	end
end

function ZhuShenTaView:UpdateKillBossCountDown(elapse_time, total_time)
	-- print_error(elapse_time, total_time)
	if not self.info then return end
	local time = math.floor(total_time - elapse_time)
	--self.info:SetRichtxt(string.format(Language.FuBenPanel.KillBoss,time))
	self.node_list["boss_fuhuo"].text.text = time--string.format(Language.FuBenPanel.KillBoss,time)
end

function ZhuShenTaView:CompleteTimeKillBoss()
	FuBenWGData.Instance:SetIsCountDownKillBoss(false)
	local copper_scence_info = self.fuben_data:GetZhushenTaFbInfo()
	local cur_level_cfg = self.fuben_data:GetMonsterTotalCountBylayout(copper_scence_info.level)
	-- self.info:SetRichtxt(cur_level_cfg.fb_des)

	self.node_list["boss_fuhuo"]:SetActive(false)
end
--副本更新时间回调（星数）
function ZhuShenTaView:UpdateTime(elapse_time, total_time)
	local copper_scence_info = self.fuben_data:GetZhushenTaFbInfo()
	if total_time - elapse_time > 0 then
		local str = ""
		if copper_scence_info.cur_star_num > 0 then
			str = string.format(Language.FuBenPanel.CopperStarTask, TimeUtil.FormatSecond((total_time - elapse_time), 2), copper_scence_info.cur_star_num - 1)
		else
			str = string.format(Language.FuBenPanel.CopperStarTask1, TimeUtil.FormatSecond((total_time - elapse_time), 2))
		end
		self.node_list.rich_star_tips.text.text = str
	end
	-- local str = ""
	-- local cur_level_cfg = self.fuben_data:GetMonsterTotalCountBylayout(copper_scence_info.level)
	-- if copper_scence_info.monster_count == 0 and copper_scence_info.boss_count ~= 0 then
	-- 	str = string.format(Language.FuBen.Label4,copper_scence_info.boss_count,cur_level_cfg.boss_count)
	-- elseif copper_scence_info.monster_count > 0 then
	-- 	local kill_monster_count = cur_level_cfg.com_monster_num - copper_scence_info.monster_count
	-- 	str = string.format(Language.FuBen.Label2,kill_monster_count,cur_level_cfg.com_monster_num)
	-- elseif copper_scence_info.monster_count == 0 then
	-- 	local kill_monster_count = cur_level_cfg.com_monster_num - copper_scence_info.monster_count
	-- 	str = string.format(Language.FuBen.Label2,0,cur_level_cfg.com_monster_num)
	-- end
	--local drop_per = FuBenWGData.Instance:GetZSTDropPer(copper_scence_info.cur_star_num)
	--self.node_list["rich_drop_per"].text.text = string.format(Language.FuBenPanel.RewordMoLi,copper_scence_info.cur_star_num,drop_per)
	-- if self.info then
	-- 	self.info:SetBoSui(str)
	-- end
end

--星数时间结束回调
function ZhuShenTaView:CompleteTime()
	--self.layout_star:SetActive(false)
	local copper_scence_info = self.fuben_data:GetZhushenTaFbInfo()
	if copper_scence_info.cur_star_num > 0 then return end

	local temp_time = FuBenPanelWGData.Instance:GetOutTimer()
	local time = temp_time - TimeWGCtrl.Instance:GetServerTime()
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(temp_time)
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), temp_time)
	if CountDownManager.Instance:HasCountDown("star_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("star_time_countdown")
	end
	CountDownManager.Instance:AddCountDown("star_time_countdown", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), nil, time, 1)

end
function ZhuShenTaView:CompleteTime111()

end

---------------------副本进入的准备时间 ------------------------------------------
-- 刷新准备倒计时 战斗开始
function ZhuShenTaView:FlushPrepareTime()
	local copper_scence_info = self.fuben_data:GetZhushenTaFbInfo()
	local cur_level_cfg = self.fuben_data:GetMonsterTotalCountBylayout(copper_scence_info.level)
	local pre_time = copper_scence_info.prepare_end_timestamp + 1
	local str = string.format(Language.FuBen.Label2,0,cur_level_cfg.com_monster_num)
	self.info:SetBoSui(str)
	self.is_boss_time = false
	-- UiInstanceMgr.Instance:ShowFBStartDown2(pre_time,self.start_fight_event)
	if CountDownManager.Instance:HasCountDown("kill_one_countdown") then
		CountDownManager.Instance:RemoveCountDown("kill_one_countdown")
	end
	self:CompleteTimeKillBoss()
	self.node_list.layout_star.transform.position = self.node_list.pos_3.transform.position
	UiInstanceMgr.Instance:DoFBStartDown(pre_time,self.start_fight_event)
end

-----------------------------进入下一层------------------------------------
--准备倒计时回调
function ZhuShenTaView:ShowNextTime(time)
	self:HideNextTime(true)
	self.node_list.next_time_txt.text.text = time
end

function ZhuShenTaView:HideNextTime(enable)
	self.node_list.next_time_txt:SetActive(enable or false)
end


--------------------------------------------------------------------------------------
ZhuShenTaInfo = ZhuShenTaInfo or BaseClass(BaseRender)

function ZhuShenTaInfo:__init()

end

function ZhuShenTaInfo:__delete()

end

--设置当前层数
function ZhuShenTaInfo:SetLevel( str )
	-- self.node_list.lbl_layout_level.text.text = str or ""
end

--设置当前目标
function ZhuShenTaInfo:SetBoSui( str )
	self.node_list.rich_boshu.text.text = str or ""
end

--设置副本说明
function ZhuShenTaInfo:SetRichtxt( str )
	self.node_list.rich_txt_desc.text.text = str or ""
end