  -- 封装 role_info_view 中有关暗器软甲的内容

HiddenWeaponRoleInfoHandler = HiddenWeaponRoleInfoHandler or BaseClass()

local HiddenWeaponRoleInfoHandler_OpenTime = "HiddenWeaponRoleInfoHandler_OpenTime"

function HiddenWeaponRoleInfoHandler:__init(parent_view)          
    self.node_list = parent_view.node_list
    self.item_cell_list = {}
    self.main_btn_list = {} --主要点击区域
    self.right_bottom_btn_list = {} --右下角点击
    self.mask_list = {}
    self.open_time_text_list = {}
    self.red_point_list = {}

    self:SetupViews()
    self.hidden_weapon_change_fun = BindTool.Bind1(self.SetWeaponData, self)
    HiddenWeaponWGCtrl.Instance:AddEquipGridChangeListener(self.hidden_weapon_change_fun)
end

function HiddenWeaponRoleInfoHandler:__delete()
    if self.hidden_weapon_change_fun and HiddenWeaponWGCtrl and HiddenWeaponWGCtrl.Instance then
        HiddenWeaponWGCtrl.Instance:RemoveEquipGridChangeListener(self.hidden_weapon_change_fun)
        self.hidden_weapon_change_fun = nil
    end

    if self.remind_change then
        RemindManager.Instance:UnBind(self.remind_change)
        self.remind_change = nil
    end

    if self.listen_fun_open_event then
        FunOpen.Instance:UnNotifyFunOpen(self.listen_fun_open_event)
        self.listen_fun_open_event = nil
    end

    if self.shenji_task_event then
        GlobalEventSystem:UnBind(self.shenji_task_event)
        self.shenji_task_event = nil
    end

    self:CancelAllCountdown()

    for k, v in pairs(self.item_cell_list) do
        v:DeleteMe()
    end

    self.item_cell_list = nil
    self.main_btn_list = nil
    self.right_bottom_btn_list = nil
    self.mask_list = nil
    self.open_time_text_list = nil
    self.red_point_list = nil
end

function HiddenWeaponRoleInfoHandler:SetupViews()
    local color = Color.New(0, 0, 0, 0)
    if self.node_list["hw_equip_cell_" .. 1] == nil then
        return
    end

    for i = 1, 2 do
        local bg_main = self.node_list["hw_equip_cell_" .. i]
        self.mask_list[i] = bg_main.transform:Find("mask")
        self.main_btn_list[i] = self.node_list["img_hw_add_" .. i]
        self.right_bottom_btn_list[i] = self.node_list["btn_hw_right_" .. i]
        self.open_time_text_list[i] = self.node_list["hw_equip_open_time_" .. i]
        self.red_point_list[i] = self.node_list["hw_equip_red_point_" .. i]
        self.red_point_list[i]:SetActive(false)
        -- 前往获取
        self.main_btn_list[i].button:SetClickListener(BindTool.Bind(self.JumpToHiddenWeaponView, self, i))
        if IS_AUDIT_VERSION then
            self.main_btn_list[i]:SetActive(false)
            bg_main:SetActive(false)
        end

        local item_cell = HiddenWeaponItemCell.New(self.node_list["shenqi_item_cell" .. i])
        self.item_cell_list[i] = item_cell
        -- item_cell:SetItemTipFrom(ItemTip.FROM_PUTON_XIAOGUI)
        item_cell:SetData(nil)
    end
end

-- 弹出获取来源
function HiddenWeaponRoleInfoHandler:ShowSourceTip(index)
    HiddenWeaponWGCtrl.Instance:OpenWnd(1, index)
end

-- 打开页面
function HiddenWeaponRoleInfoHandler:JumpToHiddenWeaponView(index)
    HiddenWeaponWGCtrl.Instance:OpenWnd(1, index)
end

-- 暗器属性详情
function HiddenWeaponRoleInfoHandler:ShowWeaponDetail(index)
    self.item_cell_list[index]:OnShowTip()
end

-- 打开页面
function HiddenWeaponRoleInfoHandler:WeaponViewNotOpen()
    -- SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FunOpenTip)
    --神机未开启，打开预告
    ShenJiNoticeWGCtrl.Instance:BagEquipCellClickOpenView()
    -- FunOpen.Instance:GetFunIsOpened(FunName.ShenJi)
end

function HiddenWeaponRoleInfoHandler:CancelAllCountdown()
    for index = 1, 2 do
        self:TryCancelCountdown(index)
    end
end

function HiddenWeaponRoleInfoHandler:TryCancelCountdown(index)
    if CountDownManager.Instance:HasCountDown(HiddenWeaponRoleInfoHandler_OpenTime .. index) then
        CountDownManager.Instance:RemoveCountDown(HiddenWeaponRoleInfoHandler_OpenTime .. index)
    end
end

function HiddenWeaponRoleInfoHandler:SetWeaponData()
    -- 判断功能开启
    local is_fun_open = FunOpen.Instance:GetFunIsOpened(FunName.ShenJi)
    -- is_fun_open = false
    if self.node_list["hw_equip_cell_" .. 1] == nil then
        return
    end

    if not self.remind_change then
        self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
        RemindManager.Instance:Bind(self.remind_change, RemindName.ShenJiEquip)
    end

    if not self.shenji_task_event then
        self.shenji_task_event = GlobalEventSystem:Bind(ShenJiEventType.ShenJiNotice_TaskInfoChange, BindTool.Bind1(self.ShenJiTaskInfoChange, self))
    end

    if not is_fun_open then
        local open_time = ShenJiNoticeWGData.Instance:GetNowSpecialSaleStartTime()
        local remain_time = open_time - TimeWGCtrl.Instance:GetServerTime()
        -- local remain_time = 0

        for index, item_cell in ipairs(self.item_cell_list) do
            -- 黑色背景，锁图标，隐藏右下按钮
            self.mask_list[index].gameObject:SetActive(true)
            item_cell:SetData(nil)

            self.right_bottom_btn_list[index].gameObject:SetActive(true)
            self.right_bottom_btn_list[index].button:SetClickListener(
                function()
                end
            )
            self.right_bottom_btn_list[index].gameObject:SetActive(false)

            --实时刷新剩余时间
            self:TryCancelCountdown(index)

            if remain_time > 0 then
                self.open_time_text_list[index]:SetActive(true)
                self:UpdateTime(index, 0, remain_time)
                CountDownManager.Instance:AddCountDown(
                    HiddenWeaponRoleInfoHandler_OpenTime .. index,
                    BindTool.Bind(self.UpdateTime, self, index),
                    BindTool.Bind(self.CompleteTime, self, index),
                    nil,
                    remain_time,
                    1
                )
            else
                self.open_time_text_list[index]:SetActive(false)
            end

            self.main_btn_list[index].button:SetClickListener(BindTool.Bind(self.WeaponViewNotOpen, self))
        end

        -- -- --监听神机功能开启。
        if not self.listen_fun_open_event then
            self.listen_fun_open_event = BindTool.Bind(self.OnShenJiFunOpen, self)
            FunOpen.Instance:NotifyFunOpen(self.listen_fun_open_event)
        end
        return
    end

    local datas = HiddenWeaponWGData.Instance:GetSCShenJiEquipGrid()
    for index, data in ipairs(datas) do
        self:TryCancelCountdown(index)
        self.mask_list[index].gameObject:SetActive(false)
        self.open_time_text_list[index]:SetActive(false)
        self.right_bottom_btn_list[index].gameObject:SetActive(true)

        if data and data.equip then
            self.item_cell_list[index]:SetData(data)
            self:SetRightBottomIcon(index, "bt_btn_sousuo2")
            self.main_btn_list[index].button:SetClickListener(BindTool.Bind(self.JumpToHiddenWeaponView, self, index))
            self.right_bottom_btn_list[index].button:SetClickListener(BindTool.Bind(self.ShowWeaponDetail, self, index))
        else
            self.item_cell_list[index]:SetData(nil)
            self:SetRightBottomIcon(index, "bt_btn_jiahao_2")
            self.right_bottom_btn_list[index].button:SetClickListener(BindTool.Bind(self.ShowSourceTip, self, index))
            self.main_btn_list[index].button:SetClickListener(BindTool.Bind(self.ShowSourceTip, self, index))
        end
    end
end

-- 设置物品图标
function HiddenWeaponRoleInfoHandler:SetRightBottomIcon(index, icon_url, bundle)
    bundle = bundle or ResPath.GetF2CommonButtonToggle(icon_url)
    if self.right_bottom_btn_list[index] then
        local img = self.right_bottom_btn_list[index].image
        if img then
            if icon_url then
                self.right_bottom_btn_list[index]:SetActive(true)
                img:LoadSprite(bundle, icon_url)
            else
                self.right_bottom_btn_list[index]:SetActive(false)
            end
        end
    end
end

function HiddenWeaponRoleInfoHandler:ClacTimeShow(time)
    if time > 0 then
        local time_tab = TimeUtil.Format2TableDHM2(time)
        if time_tab.day >= 1 then
            return string.format(Language.ShenJiNotice.XianShiEndTimeDay, time_tab.day)
        elseif time_tab.day < 1 and time_tab.hour >= 1 then
            -- return string.format(Language.ShenJiNotice.EndTimeHour, time_tab.hour)
            return Language.ShenJiNotice.NextDayOpen
        elseif time_tab.hour < 1 then
            -- return string.format("%02d:%02d", time_tab.min, time_tab.sec)
            return Language.ShenJiNotice.NextDayOpen
        end
    else
        return string.format("%02d:%02d:%02d", 0, 0, 0)
    end
end

function HiddenWeaponRoleInfoHandler:UpdateTime(index, elapse_time, total_time)
    if self.open_time_text_list[index] then
        local temp_seconds = GameMath.Round(total_time - elapse_time)
        local calc_time = self:ClacTimeShow(temp_seconds)
        local str = string.format(Language.ShenJiNotice.BagEquipCellLockTime, calc_time)
        self.open_time_text_list[index].text.text = calc_time
    end
end
function HiddenWeaponRoleInfoHandler:CompleteTime(index, elapse_time, total_time)
end

function HiddenWeaponRoleInfoHandler:OnShenJiFunOpen(fun_name)
    if fun_name == FunName.ShenJi then
        self:SetWeaponData()
        HiddenWeaponWGData.Instance.remind_manager:FireAllRemind()
    end
end

function HiddenWeaponRoleInfoHandler:RemindChangeCallBack(remind_name, num)
    self:FlushRemind()
end

function HiddenWeaponRoleInfoHandler:FlushRemind()
    if self.red_point_list == nil or self.red_point_list[1] == nil then
        return
    end
    
    local xianshi_remind = ShenJiNoticeWGData.Instance:IsShowShenJiXianShiRedPoint()
    if xianshi_remind > 0 then
        -- 神机现世是两边都亮
        self.red_point_list[1]:SetActive(true)
        self.red_point_list[2]:SetActive(true)
    else
        local red1 = HiddenWeaponWGData.Instance.remind_manager:IsWeaponTypeRed(1)
        local red2 = HiddenWeaponWGData.Instance.remind_manager:IsWeaponTypeRed(2)

        self.red_point_list[1]:SetActive(red1 == true)
        self.red_point_list[2]:SetActive(red2 == true)
    end
end

function HiddenWeaponRoleInfoHandler:ShenJiTaskInfoChange()
    self:FlushRemind()
end

---------------------
---模型格子
---------------------

local UI_COLOR_FX = {
    [7] = "UI_effect_anqi_saoguang_cai_GJ",
    [6] = "UI_effect_anqi_saoguang_fen_GJ",
    [5] = "UI_effect_anqi_saoguang_hong_GJ",
    [4] = "UI_effect_anqi_saoguang_cheng"
}

HiddenWeaponItemCell = HiddenWeaponItemCell or BaseClass(BaseRender)
function HiddenWeaponItemCell:__init(node)
    self.color_bg = self.node_list["color_bg"]
    self.model_node = self.node_list["model"]
    self.awaken_bg = self.node_list["awaken_bg"]
    self.effect_color_node = self.node_list["effect_color_node"]
    self.tips_btn = self.node_list["tips_btn"]

    local star_area = self.node_list["star_area"]
    self.node_list = {
        star_area = star_area
    }

    self.equip_model = RoleModel.New()
    local display_data = {
        parent_node = self.model_node,
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.S,
        can_drag = false,
    }
    
    self.equip_model:SetRenderTexUI3DModel(display_data)
    -- self.equip_model:SetUI3DModel(self.model_node.transform, nil, 0, false, MODEL_CAMERA_TYPE.BASE)

    self.effect = nil

    if self.tips_btn then
        XUI.AddClickEventListener(self.tips_btn, BindTool.Bind(self.OnShowTip, self))
    end
end

function HiddenWeaponItemCell:CreateEffect(color)
    local asset_name = UI_COLOR_FX[color]
    if asset_name then
        if not self.effect then
            local effect = AllocAsyncLoader(self, "effect_ui")
            effect:SetIsUseObjPool(true)
            effect:SetParent(self.effect_color_node.transform)
            self.effect = effect
        end

        local asset_bundle, name = ResPath.GetEffectUi(asset_name)
        self.effect:Load(asset_bundle, name)
        self.effect:SetActive(true)
    end
end

function HiddenWeaponItemCell:HideAllEffect()
    if self.effect then
        self.effect:SetActive(false)
    end
end

function HiddenWeaponItemCell:__delete()
    if self.equip_model then
        self.equip_model:DeleteMe()
        self.equip_model = nil
    end

    if self.effect then
        self.effect:Destroy()
        self.effect:DeleteMe()
    end

    if self.model_tween then
        self.model_tween:Kill()
        self.model_tween = nil
    end

    self:PlayModelAction(false)
    self.effect = nil
end

function HiddenWeaponItemCell:OnShowTip()
    if self.data then
        TipWGCtrl.Instance:OpenItem(self.data, self.item_tip_from or ItemTip.FROM_NORMAL, self.index)
    end
end

function HiddenWeaponItemCell:SetItemTipFrom(item_tip_from)
    self.item_tip_from = item_tip_from
end

function HiddenWeaponItemCell:SetData(data)
    self.data = data
    self:OnFlush()
end

function HiddenWeaponItemCell:SetTipBtnActive(b)
    if self.tips_btn then
        self.tips_btn:SetActive(b)
    end
end

function HiddenWeaponItemCell:OnFlush()
    self:HideAllEffect()
    self:SetTipBtnActive(false)
    if not self.data then
        self.color_bg:SetActive(false)
        self.awaken_bg:SetActive(false)

        if self.equip_model then
            self.equip_model:ClearModel()
        end
        self:PlayModelAction(false)

        HiddenWeaponWGCtrl.Instance:RefreshShowStar(0, self)
    else
        self.color_bg:SetActive(true)
        self.awaken_bg:SetActive(true)

        local equip = self.data.equip
        if equip then
            self:PlayModelAction(true)
            local bundle, asset = ResPath.GetShenJiModel(equip.up_model)
            self.equip_model:SetMainAsset(
                bundle,
                asset,
                function(obj)
                    -- --去掉影子
                    local ui_layer = UnityEngine.LayerMask.NameToLayer("UI")
                    obj.gameObject:SetLayerRecursively(ui_layer)
                    if obj and obj.SetActive then
                        obj:SetActive(true)
                    end
                end
            )

            self.equip_model:SetModelScale(Vector3(0.35, 0.35, 0.35))
            -- local sz_model = tostring(equip.up_model)
            -- local add_pos = UI_SPECIAL_POS[sz_model] or {x = 0, y = 20}
            -- local scale = UI_SPECIAL_SCALE[sz_model] or 1

            self.model_node.transform.localPosition = Vector2(0, 0)
            if equip.big_type == 2 then
                self.model_node.transform.localPosition = Vector2(0, HW_CONST_PARAM.OutUIModelOffY)
            end

            -- self.model_node.transform.localScale = Vector3(scale, scale, scale)

            self:SetTipBtnActive(true)

            self:CreateEffect(equip.base_color)
            HiddenWeaponWGCtrl.Instance:RefreshShowStar(equip.base_star, self)
            self.color_bg.image:LoadSprite(ResPath.GetShenJiGridColor(equip.base_color))
        else
            self.color_bg:SetActive(false)
            if self.equip_model then
                self.equip_model:ClearModel()
            end
            HiddenWeaponWGCtrl.Instance:RefreshShowStar(0, self)
            self:PlayModelAction(false)
        end

        if self.data.protocol_equip_item.special_effect_level > 0 then
            local asset,bundle = ResPath.GetShenJiAwakenBg(self.data.protocol_equip_item.special_effect_level)
            self.awaken_bg.image:LoadSprite(asset,bundle,function ()
                self.awaken_bg.image:SetNativeSize()
            end)

        else
            self.awaken_bg:SetActive(false)
        end
    end
end

function HiddenWeaponItemCell:PlayModelAction(b)
    local obj = self.model_node
    if obj then
        if not b then
            if self.model_tween then
                self.model_tween:Kill()
                self.model_tween = nil
            end
            return
        end

        if self.model_tween then
            self.model_tween:Kill()
            self.model_tween = nil
        end

        if not self.model_start_pos then
            self.model_start_pos = obj.transform.anchoredPosition
        end

        obj.transform.anchoredPosition = Vector3(self.model_start_pos.x, self.model_start_pos.y, self.model_start_pos.z)
        obj.transform.localRotation = Quaternion.Euler(0, 15, 0)

        local tween1 = obj.transform:DOLocalRotate(Vector3(0, -15, 0), 4)
        local tween2 = obj.transform:DOLocalRotate(Vector3(0, 15, 0), 4)
        self.model_tween = DG.Tweening.DOTween.Sequence()
        self.model_tween:Append(tween1)
        self.model_tween:Append(tween2)
        self.model_tween:SetEase(DG.Tweening.Ease.Linear)
        self.model_tween:SetLoops(-1)
    end
end
