SocietySendGoodsView = SocietySendGoodsView or BaseClass(SafeBaseView)
SPECIALGIFTID = {
	[26120] = true,
	[26121] = true,
	[26122] = true,
	[26123] = true,

}

local flower_seq_list = {588, 587, 509, 508}
function SocietySendGoodsView:__init()
	self.view_name = GuideModuleName.SocietySendGoods
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_send_goods")
	self.order = 1
	self.is_refrech_friend_list = false
	--self.select_btn_index = 1
	self.itemdata_change_callback = BindTool.Bind1(self.RefreshGoodsCellViews, self)
end

function SocietySendGoodsView:ReleaseCallBack()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.itemdata_change_callback)
	if self.friend_list then
		self.friend_list:DeleteMe()
		self.friend_list = nil
	end
	if self.society_send_material_bag then
		self.society_send_material_bag:DeleteMe()
		self.society_send_material_bag = nil
	end
	self.select_btn_index = nil
end

function SocietySendGoodsView:LoadCallBack()
	self.select_friend_data = {} --选择朋友信息

	ItemWGData.Instance:NotifyDataChangeCallBack(self.itemdata_change_callback)
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(1060,651)
	self.friend_list = AsyncListView.New(SocietySendGoodsRender, self.node_list.friend_list_view)
	self.friend_list:SetSelectCallBack(BindTool.Bind1(self.SelectfriendCellCallBack,self))

	-- local data = SocietyWGData.Instance:GetFriendList2()
	-- self.friend_list:SetDefaultSelectIndex(1)
	-- self.friend_list:SetData(data,0)

	self.society_send_material_bag = AsyncBaseGrid.New() --ShenShouGrid.New() --
	self.society_send_material_bag:CreateCells({
	col = 5,
	cell_count = 10,
	change_cells_num = 2,
	--assetName = asset,
	--assetBundle = bundle,
	itemRender = SocietySendBagRender,
	list_view = self.node_list["material_list_view"]
	 })
	self.society_send_material_bag:SetStartZeroIndex(false)
	--self.society_send_material_bag:SetSelectCallBack(BindTool.Bind1(self.SelectMaterialCellCallBack, self))
	-- local data = {}
	-- self.society_send_material_bag:SetDataList(data,0)
	for i=1,3 do
		XUI.AddClickEventListener(self.node_list["btn_material_"..i], BindTool.Bind(self.SelectSendType, self,i))
	end

	self.has_select_goods_list = {}
	for i=1,5 do
		local item_cell = ItemCell.New(self.node_list["material_send_"..i])
		local u_obj = U3DObject(item_cell.view.gameObject, item_cell.view.gameObject.transform, _G)
		u_obj.rect.anchorMax = Vector2(0.5,0.5)
		u_obj.rect.anchorMin = Vector2(0.5,0.5)
		u_obj.rect.anchoredPosition = Vector2(0,0)
		table.insert(self.has_select_goods_list,item_cell)
		XUI.AddClickEventListener(self.node_list["Btn_reduce_"..i], BindTool.Bind(self.ReduceMaterialBtn, self,i))
	end
	XUI.AddClickEventListener(self.node_list["btn_send"], BindTool.Bind(self.SendSendMaterial, self))
	XUI.AddClickEventListener(self.node_list["btn_send_record"], BindTool.Bind(self.OpenSendRecordPanel, self))
	self:FlushFriendList(0)
	self:SelectSendType(self.select_btn_index == nil and 1 or self.select_btn_index)

end

function SocietySendGoodsView:SetDataByTipOpen(item_data)
	local data = {}
	local cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)

	if SPECIALGIFTID[item_data.item_id] then
		self.select_btn_index = 3
	else
		self.select_btn_index = cfg.goods_type
	end
	self.default_item_id = item_data
	self:Open()
end

function SocietySendGoodsView:OpenSendRecordPanel()
	SocietyWGCtrl.Instance:PresentHistoryReq(0)
	SocietyWGCtrl.Instance:PresentHistoryReq(1)
	SocietyWGCtrl.Instance:OpenSendRecordView()

end

function SocietySendGoodsView:ReduceMaterialBtn(index)
	local list_material = SocietyWGCtrl.Instance:GetSelectItemCellSetData()
	local item_id = list_material[index].item_id
	local index = list_material[index].index
	SocietyWGCtrl.Instance:ClickNumOK(item_id,-1,index)

end

function SocietySendGoodsView:SendSendMaterial()
	local data = SocietyWGData.Instance:GetFriendList2()
	if self.select_friend_data then
		if self.select_friend_data.is_kuafu == 3 or IS_ON_CROSSSERVER then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.KuaFuSongHua)
			return
		end
		local is_online = nil
		for k,v in pairs(data) do
			if v.user_id == self.select_friend_data.user_id then
			is_online = v.is_online
			--print_error(self.select_friend_data.user_id,v.user_id)
			end
		end
		if self.select_btn_index ~= 3 then
			if nil == is_online then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NotMyFriend)
				return
			else
				SocietyWGCtrl.Instance:SendSendMaterial(self.select_btn_index)
			end
			self.society_send_material_bag:CancleAllSelectCell()
		else
			if is_online == 1  then
				SocietyWGCtrl.Instance:SendSendSpecialMaterial(self.select_btn_index)
			elseif is_online == 0 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.FriendIsNotOnline)
				return
			else
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.NotMyFriend)
				return
			end
		end
	end
end

function SocietySendGoodsView:SelectfriendCellCallBack(cell)
	if cell.data then
		SocietyWGData.Instance:SetSelectFriendSendId(cell.data.user_id)
		SocietyWGCtrl.Instance:SetSendFriendData(cell.data)
		self.select_friend_data = cell.data

	end
end

function SocietySendGoodsView:Close()
	self.select_btn_index = nil
	for i=1,3 do
		self.node_list["btn_material_select_"..i]:SetActive(false)
	end
	SafeBaseView.Close(self)
end

function SocietySendGoodsView:SelectSendType(index)
	self.select_btn_index = index
	local  vas = self.node_list["btn_material_select_"..index]:GetActive()
	if vas then
		return
	else
		SocietyWGCtrl.Instance:ClickNumOK(nil,nil,nil,true)
	end
	for i=1,3 do
		self.node_list["btn_material_select_"..i]:SetActive(index == i)
	end
	local bag_list = ItemWGData.Instance:GetBagItemDataList()
	local temp_select_list = {}
	local gift_list = {}
	local count = 1
	for i = 26120,26123 do
		local data = {}
		data.item_id = i
		data.num = 0
		data.index = 0
		data.seq = flower_seq_list[count]
		count = count + 1
		table.insert(gift_list,data)
	end
	for k,v in pairs(bag_list) do
		local cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

		if cfg then
			if index == 3 then

				if cfg.largess_type == 1 and v.is_bind == 0 then
					if SPECIALGIFTID[v.item_id] then
						for m,n in pairs(gift_list) do
							if v.item_id == n.item_id then
								n.num = n.num + v.num
								n.index = v.index
							end
						end
						--table.insert(temp_select_list,v)
					end
				end
			else
				--index物品类型 1道具 2 材料  largess_type 1可赠送 0不可赠送  isbind 是否绑定  0 非绑定
				if cfg.goods_type == index and cfg.largess_type == 1 and v.is_bind == 0 then
					--print_error(cfg.goods_type,cfg.largess_type)
					if not SPECIALGIFTID[v.item_id] then
						table.insert(temp_select_list,v)
					end
				end
			end

		end
	end
	--print_error(temp_select_list)
	if self.select_btn_index == 3 then
		temp_select_list = gift_list
	end
	local selecc_dex = 0
	local defult_num = 0
	if self.default_item_id then
		for k,v in pairs(temp_select_list) do
			if v.item_id == self.default_item_id.item_id and v.index == self.default_item_id.index then
				selecc_dex = k
				defult_num = v.num
				break
			end
		end
		if self.select_btn_index == 3 then
			for k,v in pairs(temp_select_list) do
			if v.item_id == self.default_item_id.item_id then
				selecc_dex = k
				defult_num = v.num
				break
			end
		end
		end
	end
	self.society_send_material_bag:CancleAllSelectCell()
	if selecc_dex > 0 then
		self.society_send_material_bag.select_tab[1][selecc_dex] = true
	end
	if #temp_select_list > 10 then
		local yushu = #temp_select_list % 5
		local need_count = 5 - yushu
		for i=1,need_count do
			local data = {}
			data.item_id = nil
			table.insert(temp_select_list,data)
		end

	end

	self.society_send_material_bag:SetDataList(temp_select_list,0)
	if self.default_item_id then
		SocietyWGCtrl.Instance:OpenSendNumView(self.default_item_id.item_id, defult_num,self.default_item_id.index)
	end
	self.default_item_id = nil
end

function SocietySendGoodsView:HasSelectItemCellSetData(set_cell_data_list,is_refrech)
	for k,v in pairs(self.has_select_goods_list) do
		v:SetData(set_cell_data_list[k])
		if set_cell_data_list[k] then
			v:SetRightBottomTextVisible(true)
			v:SetRightBottomText(set_cell_data_list[k].num)
			self.node_list["Btn_reduce_"..k]:SetActive(true)
		else
			self.node_list["Btn_reduce_"..k]:SetActive(false)
		end
	end
	if not is_refrech then
		self.node_list["material_list_view"].scroller:RefreshActiveCellViews()
	end
end

function SocietySendGoodsView:RefreshGoodsCellViews()
	-- local temp_select_btn_index = 1
	-- for i=1,3 do
	-- 	local vas = self.node_list["btn_material_select_"..i]:GetActive()
	-- 	if vas then
	-- 		temp_select_btn_index = i
	-- 	end
	-- end
	local bag_list = ItemWGData.Instance:GetBagItemDataList()
	local temp_select_list = {}
	local gift_list = {}
	local count = 1
	for i = 26120,26123 do
		local data = {}
		data.item_id = i
		data.num = 0
		data.index = 0
		data.seq = flower_seq_list[count]
		count = count + 1
		table.insert(gift_list,data)
	end
	for k,v in pairs(bag_list) do
		local cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

		if cfg then
			if self.select_btn_index == 3 then

				if cfg.largess_type == 1 and v.is_bind == 0 then
					if SPECIALGIFTID[v.item_id] then
						for m,n in pairs(gift_list) do
							if v.item_id == n.item_id then
								n.num = n.num + v.num
								n.index = v.index
							end
						end
						--table.insert(temp_select_list,v)
					end
				end
			else
				if cfg.goods_type == self.select_btn_index and cfg.largess_type == 1 and v.is_bind == 0 then  --index物品类型 1道具 2 材料  largess_type 1可赠送 0不可赠送  isbind 是否绑定  0 非绑定

					if not SPECIALGIFTID[v.item_id] then
						table.insert(temp_select_list,v)
					end
				end
			end

		end
	end
	if self.select_btn_index == 3 then
		temp_select_list = gift_list
	end
	if #temp_select_list > 10 then
		local yushu = #temp_select_list % 5
		local need_count = 5 - yushu
		for i=1,need_count do
			local data = {}
			data.item_id = nil
			table.insert(temp_select_list,data)
		end

	end
	self.society_send_material_bag:SetDataList(temp_select_list,0)

	--self.node_list["material_list_view"].scroller:RefreshActiveCellViews()
end

function SocietySendGoodsView:OpenCallBack()
	self:FlushFriendList(0)
end

function SocietySendGoodsView:Close()
	self.is_refrech_friend_list = false
	SafeBaseView.Close(self)
end

function SocietySendGoodsView:FlushFriendList(index)
	if self.friend_list then
		if self.is_refrech_friend_list then
			self.node_list["friend_list_view"].scroller:RefreshActiveCellViews()
		else
			local data = SocietyWGData.Instance:GetFriendList2()
			if index == 0 then
				local index,friend_count = SocietyWGData.Instance:GetShouldSelectIndex()
				self.friend_list:SetDefaultSelectIndex(index)
				if index > 4 then
				local hua_dong_float = (index - 4 ) /(friend_count - 4)
 					self.node_list.friend_list_view.scroller:ReloadData(hua_dong_float)
 				end
			end

			self.friend_list:SetDataList(data,index)
			self.is_refrech_friend_list = true
			self:SelectSendType(self.select_btn_index == nil and 1 or self.select_btn_index)
		end
	end
end

function SocietySendGoodsView:SelectMaterialCellCallBack(item)
	--print_error("选择了物品")
end



--赠送好友列表显示
SocietySendGoodsRender = SocietySendGoodsRender or BaseClass(BaseRender)
function SocietySendGoodsRender:__init()
end

function SocietySendGoodsRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["img_heart"], BindTool.Bind1(self.OnClickOpenQinMi, self))
	--XUI.AddClickEventListener(self.node_list["img_cell"], BindTool.Bind1(self.RoleInfoList, self))
end

function SocietySendGoodsRender:RoleInfoList()
end

function SocietySendGoodsRender:__delete()
end

function SocietySendGoodsRender:OnSelectChange(is_select)
	if next(self.node_list) == nil then return end
	if is_select then
		self.node_list.img_choose:SetActive(true)
	else
		self.node_list.img_choose:SetActive(false)
	end
end

function SocietySendGoodsRender:OnFlush()
	if self.data == nil then return end
	local temp_friend_list = SocietyWGData.Instance:GetFriendList2()
	for k,v in pairs(temp_friend_list) do
		if v.user_id == self.data.user_id then
			self.data = v
			break
		end
	end
	self.node_list["lbl_camp_username"].text.text = self.data.gamename
	XUI.UpdateRoleHead(self.node_list["icon"], self.node_list["custom_icon"], self.data.user_id,self.data.sex,self.data.prof,false)

	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.img9_fire:SetActive(is_vis)
	self.node_list.label_nodeixian_level:SetActive(not is_vis)
	if is_vis then
		self.node_list["label_level"].text.text = role_level
	else
	self.node_list["label_nodeixian_level"].text.text = "Lv."..role_level--RoleWGData.GetLevelString(self.data.level)
	end
		-- 亲密度
	local need_intimacy = 1
	local level = 0
	local intimacy_cfg = ConfigManager.Instance:GetAutoConfig("friendcfg_auto").team_intimacy_buff
	for k,v in pairs(intimacy_cfg) do
	 	if self.data.intimacy < v.intimacy then
	 		need_intimacy = v.intimacy
	 		break
	 	else
	 		need_intimacy = intimacy_cfg[#intimacy_cfg].intimacy
	 	end
	end
	local middle_intimacy = intimacy_cfg[6].intimacy
	local max_middle_intimacy = intimacy_cfg[#intimacy_cfg].intimacy
	local intimacy_level_cfg = MarryWGData.Instance:GetIntimacyLevelByIntimacy(self.data.intimacy)
	if intimacy_level_cfg then
		if intimacy_level_cfg.buff_level <= 5 then
			self.node_list.img_heart_list2:SetActive(false)
			self.node_list.img_heart_list1:SetActive(true)
			self.node_list.img_heart_list1.image.fillAmount = self.data.intimacy/middle_intimacy
		else
			self.node_list.img_heart_list1.image.fillAmount = 1
			self.node_list.img_heart_list1:SetActive(true)
			self.node_list.img_heart_list2:SetActive(true)
			self.node_list.img_heart_list2.image.fillAmount = (self.data.intimacy - middle_intimacy)/max_middle_intimacy

		end
	end

end

function SocietySendGoodsRender:OnClickOpenQinMi()
	MarryWGCtrl.Instance:SetIntimacyData(self.data.intimacy, self.data.gamename)
end

--赠送背包列表显示
SocietySendBagRender = SocietySendBagRender or BaseClass(ItemCell)
function SocietySendBagRender:OnFlush()
	ItemCell.OnFlush(self)
	if nil == self.data.item_id then
		return
	end

	local show_num = self:GetItemNum()
	self:SetRightBottomTextVisible(true)
	if show_num > 999 then
		show_num = "999+"
	end
	self:SetRightBottomText(show_num)
end

function SocietySendBagRender:__delete()
	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
end

function SocietySendBagRender:OnClick()
	local num = self:GetItemNum()
	BaseRender.OnClick(self)
	local material_list = SocietyWGCtrl.Instance:GetSelectItemCellSetData()
	local is_full = false
	if SPECIALGIFTID[self.data.item_id] then
		for k,v in pairs(material_list) do
			if v.item_id == self.data.item_id then
				if v.num >= 999 then
					is_full = true
					break
				end
			end
		end
	end

	if num <= 0 then
		if nil == self.alert_window then
			self.alert_window = Alert.New(nil, nil, nil, nil, false)
		end

		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)
		local shop_gold = shop_cfg.price
		local item_data = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if item_data then
			local grid_tem_num = 1
			local color_txt = ""
			local price = shop_gold * grid_tem_num
			if RoleWGData.Instance:GetIsEnoughUseGold(item_data.sellprice * grid_tem_num) then
				color_txt = "<color=#7cffb7>".. price .."</color>"
			else
				color_txt = "<color=#ff0000ff>".. price .. "</color>"
			end
			self.alert_window:SetLableString(string.format(Language.Flower.NotNumYips, item_data.name, color_txt))
			self.alert_window:SetOkFunc(function()
				ShopWGCtrl.Instance:SendShopBuy(self.data.item_id, grid_tem_num, 0, nil, self.data.seq)
			end)
			self.alert_window:Open()
		end
		return
	end

	if is_full then
		return
	end

	SocietyWGCtrl.Instance:OpenSendNumView(self.data.item_id, num,self.data.index)
end

function SocietySendBagRender:GetItemNum()
	local material_list = SocietyWGCtrl.Instance:GetSelectItemCellSetData()
	local show_num = self.data.num
	for k,v in pairs(material_list) do
		if v.item_id == self.data.item_id and v.index == self.data.index then
			show_num = self.data.num - v.num
		end
	end

	return show_num or 0
end
