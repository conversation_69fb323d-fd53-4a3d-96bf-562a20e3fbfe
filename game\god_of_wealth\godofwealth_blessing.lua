GodOfWealthBlessingView = GodOfWealthBlessingView or BaseClass(SafeBaseView)
function GodOfWealthBlessingView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(false, true)

    self:AddViewResource(0, "uis/view/god_of_wealth_ui_prefab", "layout_god_of_wealth_blessing")
end

function GodOfWealthBlessingView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_lq"], BindTool.Bind1(self.OnClickBlessingLQ, self))
    XUI.AddClickEventListener(self.node_list["btn_up"], BindTool.Bind1(self.OnClickBlessingUp, self))

    if not self.alert then
        self.alert = Alert.New()
    end
end

function GodOfWealthBlessingView:ReleaseCallBack()
    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function GodOfWealthBlessingView:OnFlush()
    local reward_level = GodOfWealthWGData.Instance:GetBlessingLevel()
    XUI.SetGraphicGrey(self.node_list["blessing_lv_img"], reward_level < 1)
    self.node_list["blessing_lv"].text.text = string.format(Language.GodOfWealth.BlessingLevelTxT, reward_level)
    self.node_list["up_text"].text.text = reward_level > 0 and Language.GodOfWealth.BlessingBtnTxT2 or Language.GodOfWealth.BlessingBtnTxT1

    local img_lv = reward_level < 1 and 1 or reward_level
    local bundle, asset = ResPath.GetRawImagesPNG("a1_cscf_bx" .. img_lv)
    self.node_list["blessing_lv_img"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["blessing_lv_img"].raw_image:SetNativeSize()
    end)

    local cur_level_cfg = GodOfWealthWGData.Instance:GetBlessingLevelCfg(reward_level)
    local buy_times = GodOfWealthWGData.Instance:GetBlessingBuyTimes()
    local times_desc = string.format(Language.GodOfWealth.BlessingUpTimesTxt, cur_level_cfg.rmb_buy_time - buy_times)
    local times_str = (buy_times >= cur_level_cfg.rmb_buy_time) and Language.GodOfWealth.BlessingCanUpTxt or times_desc
    if cur_level_cfg then
        self.node_list["up_times_txt"].text.text = times_str
    end

    local is_max = GodOfWealthWGData.Instance:GetBlessingIsMaxLevel()
    self.node_list["up_times_txt"]:SetActive(not is_max)
    self.node_list["btn_up"]:SetActive(not is_max)

    local rule_desc = GodOfWealthWGData.Instance:GetBlessingRuleDesc()
    self.node_list["blessing_desc"].text.text = rule_desc

    local lq_red = GodOfWealthWGData.Instance:GetBlessingLQRed()
    local up_red = GodOfWealthWGData.Instance:GetBlessingUpRed()
    self.node_list["lq_redmind"]:SetActive(lq_red)
    self.node_list["up_redmind"]:SetActive(up_red)
end


function GodOfWealthBlessingView:OnClickBlessingLQ()
    local is_get = GodOfWealthWGData.Instance:GetBlessingRewardIsGet()
    local reward_level = GodOfWealthWGData.Instance:GetBlessingLevel()
    if reward_level < 1 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.GodOfWealth.BlessingLQError)
        return
    end

    if is_get then
        TipWGCtrl.Instance:ShowSystemMsg(Language.GodOfWealth.BlessingError)
        return
    end

    GodOfWealthWGCtrl.Instance:SendOperaReq(WEALTH_GOD_OPERATE_TYPE.REWARD)
end

function GodOfWealthBlessingView:OnClickBlessingUp()
    local reward_level = GodOfWealthWGData.Instance:GetBlessingLevel()
    local cur_level_cfg = GodOfWealthWGData.Instance:GetBlessingLevelCfg(reward_level)
    local buy_times = GodOfWealthWGData.Instance:GetBlessingBuyTimes()
    if cur_level_cfg then
        if buy_times < cur_level_cfg.rmb_buy_time then
            TipWGCtrl.Instance:ShowSystemMsg(Language.GodOfWealth.BlessingUpError)
            return
        end
    end
    
    if reward_level < 1 then
        GodOfWealthWGCtrl.Instance:SendOperaReq(WEALTH_GOD_OPERATE_TYPE.UPLEVEL)
        return
    end

    local function func()
        GodOfWealthWGCtrl.Instance:SendOperaReq(WEALTH_GOD_OPERATE_TYPE.UPLEVEL)
    end

    self.alert:ClearCheckHook()
    self.alert:SetShowCheckBox(true, "godofwealth_blessing")
    self.alert:SetCheckBoxDefaultSelect(false)
    self.alert:SetLableString(Language.GodOfWealth.BlessingUpTxT)
    self.alert:SetOkFunc(func)
    self.alert:Open()
end