--动态运营活动 风筝夺宝
-- 所有图片动态
-- NameTable 里面的名字都要加上前缀,否则可能会和天神夺宝冲突
function OperationActivityView:InitFengZhengView()
	self.reward_list = {}
	-- if nil == self.item_data_change_callback then
    --     self.item_data_change_callback = BindTool.Bind1(self.OnLianHuaItemChangeCallBack, self)
    --     ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
    -- end
end

function OperationActivityView:__delete()
    -- if self.item_data_change_callback then
    --     ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
    --     self.item_data_change_callback = nil
    -- end
end

--物品变化
function OperationActivityView:OnLianHuaItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local other_cfg = FZGetRewardWGData.Instance:GetChangeFlushItemList()
	if other_cfg[1] == change_item_id and self:IsLoadedIndex(TabIndex.operation_act_fengzheng) then
    	self:GetTxtTime()
		self:FlushRed()
		self:FlushItemNum()
    end
end


function OperationActivityView:ReleaseFengZhengView()
	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	for i, v in pairs(self.reward_list) do
		v:DeleteMe()
	end

	if CountDownManager.Instance:HasCountDown("fengzheng_draw") then
		CountDownManager.Instance:RemoveCountDown("fengzheng_draw")
	end

	if self.talk_bubble_show then
		GlobalTimerQuest:CancelQuest(self.talk_bubble_show)
        self.talk_bubble_show = nil
	end

 --    if self.close_bubble then
	-- 	GlobalTimerQuest:CancelQuest(self.close_bubble)
 --        self.close_bubble = nil
	-- end

	if self.img_arrow_timequest then
		GlobalTimerQuest:CancelQuest(self.img_arrow_timequest)
		self.img_arrow_timequest = nil
	end

	if self.img_arrow_timequest1 then
		GlobalTimerQuest:CancelQuest(self.img_arrow_timequest1)
		self.img_arrow_timequest1 = nil
	end

	if self.click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.click_draw_timequest)
		self.click_draw_timequest = nil
	end

	if self.tween_timequest then
		GlobalTimerQuest:CancelQuest(self.tween_timequest)
		self.tween_timequest = nil
	end

	if self.arrow_anim_timequest then
		GlobalTimerQuest:CancelQuest(self.arrow_anim_timequest)
		self.arrow_anim_timequest = nil
	end

	if self.play_drop_anim_timequest then
		GlobalTimerQuest:CancelQuest(self.play_drop_anim_timequest)
		self.play_drop_anim_timequest = nil
	end
	self.reward_list = {}
	self.select_layer = nil
	self.ignore_anim = nil
	self.is_animing = nil
	self.on_click_draw = nil
	self.btn_tip = nil
	self.reward_info = nil
	self.ske_graphic = nil
	if self.model then
		self.model:DeleteMe()
		self.model = nil
	end
	self.layer_list = nil
end

function OperationActivityView:LoadCallBackFengZhengView()
    self.node_list["fz_btn_draw"].button:AddClickListener(BindTool.Bind(self.OnClickBtnDraw, self))
    self.node_list["fz_btn_draw_all"].button:AddClickListener(BindTool.Bind(self.OnClickBtnDrawAll, self))
    self.node_list["fz_btn_check"].button:AddClickListener(BindTool.Bind(self.OnClickBtnCheck, self))
    self.node_list["fz_btn_record"].button:AddClickListener(BindTool.Bind(self.OnClickBtnRecoed, self))
    -- self.node_list["fz_btn_tip"].button:AddClickListener(BindTool.Bind(self.OnClickBtnTip, self))
    self.node_list["fz_reset_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBtnReset, self))
    local have_layer = FZGetRewardWGData.Instance:GetDangWeiLayerNum()
    for i = 1, 3 do
    	self.node_list["fz_layer_btn" .. i]:SetActive(i <= have_layer)
    end
    if have_layer == 1 then
		self.node_list["fz_layer_btn1"]:SetActive(false)
	end

    for i = 1, 2 do
    	XUI.AddClickEventListener(self.node_list['fz_item_icon_' .. i],BindTool.Bind(self.OnClickGodGetItemBtn, self))
    end
    self.select_layer = 1
    FZGetRewardWGData.Instance:SetCurLayer(self.select_layer)
    self:SetFZViewShowInfo()
	self:CreateLayerList()
	self:CreateMoneyBar()
	self.def_pos = self.node_list["fz_arrow_anim"].rect.anchoredPosition
end

--  创建页签list
function OperationActivityView:CreateLayerList()
	if not self.layer_list then
		self.layer_list = {}
		for i = 1 , 3 do
			self.layer_list[i] = self.node_list["fz_layer_btn" .. i]
			self.layer_list[i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickBtnLayer, self, i))
		end
		self:OnClickBtnLayer(self.select_layer,true)
	end
end

function OperationActivityView:OnClickBtnLayer(index, is_on)
	if self.is_animing then
		return
	end
	if is_on then
		self.node_list["fz_select_image1"]:SetActive(index == OPERA_ACT_FENGZHENG.LAYER1)
		self.node_list["fz_select_image2"]:SetActive(index == OPERA_ACT_FENGZHENG.LAYER2)
		self.node_list["fz_select_image3"]:SetActive(index == OPERA_ACT_FENGZHENG.LAYER3)
		if self.select_layer == index then
			self:FlushLayerImg(index)
			return
		end
		self.select_layer = index
		FZGetRewardWGData.Instance:SetCurLayer(self.select_layer)
		local cur_draw_info = FZGetRewardWGData.Instance:GetCurDrawInfoByLayer(index)
		local is_empty = FZGetRewardWGData.Instance:GetIsDrawEmptyByLayer(index)
		--刷新界面
		self:FlushLayerImg(index)
		self:FlushLayerShow()
		self:GetTxtTime(is_empty)
		self:FlushItemNum(cur_draw_info, is_empty)
		self:FlushRed()
		self:FlushBigRewardAnim()
	end
end

-- 创建MoneyBar
function OperationActivityView:CreateMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["fz_money_bar"].transform)
	end
end

function OperationActivityView:ShowIndexCallBackFengZhengView()
	--存储
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid
	local img_yes_state = PlayerPrefsUtil.GetInt("fengzheng_draw" .. role_id) == 1
	self.node_list["fz_img_yes"]:SetActive(img_yes_state)
	self.ignore_anim = img_yes_state
	self:CheckNeedResetFengZheng()

	local view_info_cfg = FZGetRewardWGData.Instance:GetViewCfgByInterface()
	if nil == view_info_cfg or IsEmptyTable(view_info_cfg) then
		return
	end
	local title = OperationActivityWGData.Instance:GetActivityNameByActivityNum(ACTIVITY_TYPE.OPERA_ACT_FENGZHENG)
	self:SetRuleInfo(view_info_cfg.rule_content, title)
    self:SetOutsideRuleTips(view_info_cfg.default_content)
   	self:SetRightTimeShow()
end

-- 是否需要重置
function OperationActivityView:CheckNeedResetFengZheng()
	local layer_num = FZGetRewardWGData.Instance:GetDangWeiLayerNum()
	for i = 1, layer_num do
		local is_empty = FZGetRewardWGData.Instance:GetIsDrawEmptyByLayer(i)
		local has_next = FZGetRewardWGData.Instance:HasNextPool(i)
		if is_empty and has_next then
			FZGetRewardWGCtrl.Instance:SendOpera(FZ_XUNBAO_OPERA_TYPE.RESET_LAYRE, i)
		end
	end
end

function OperationActivityView:FengZhengCloseCallBack()
	self:CheckNeedResetFengZheng()
end

------------------------------------ 刷新 -----------------------------------------------------------------------------

function OperationActivityView:OnFlushFengZhengView()
	if not FZGetRewardWGData.Instance:IsHasData(self.select_layer) then
		return
	end
	self:FlushLayerShow()
	self:FlushBigRewardAnim()
	self:GetTxtTime()
	self:FlushRed()
	self:FlushItemNum()
	self:FZFlushRecordNum()
	self:SetRightTimeShow()
end

-- 刷新 奖励 和 重置按钮
function OperationActivityView:FlushLayerShow()
	local reward_id_list = FZGetRewardWGData.Instance:GetCurRewardListByLayer(self.select_layer)
	if not reward_id_list then
		return
	end
	local bundle, asset = "uis/view/operation_activity_ui/fengzheng_ui_prefab", "fz_kite_render"
	for i, v in pairs(reward_id_list) do
		local state = FZGetRewardWGData.Instance:GetRewardIsShowByLayerAndId(self.select_layer, i)
		if not self.reward_list[i] then
			self.reward_list[i] = FZKiteRender.New()
			self.reward_list[i]:LoadAsset(bundle, asset, self.node_list["fz_kite_content"].rect)
		end
		if state then
			self.reward_list[i]:SetData(v)
		end
		self.reward_list[i]:SetKiteState(state)
	end

	for i = #reward_id_list + 1, #self.reward_list do
		if self.reward_list[i] then
			self.reward_list[i]:SetKiteState(false)
		end
	end

	-- 重置按鈕显隐
	--reward_id_list 当前奖池
	--有大奖就是false
	local state = FZGetRewardWGData.Instance:GetBestRewardHasGet(self.select_layer, reward_id_list)
	local has_next = FZGetRewardWGData.Instance:HasNextPool(self.select_layer)
	self.node_list["fz_reset_btn"]:SetActive(state and has_next)
end

-- 刷新蝴蝶龙骨动画
function OperationActivityView:FlushBigRewardAnim()
	local cur_layer = self.select_layer
	local has_big_reward = FZGetRewardWGData.Instance:GetBigRewardExistByLayer(self.select_layer)
	self.node_list["ske_pos"]:SetActive(has_big_reward)
	self.node_list["ske_pos"].canvas_group.alpha = 1

end

--刷新下一期时间
function OperationActivityView:GetTxtTime(is_empty)
	local last_time, cur_round = FZGetRewardWGData.Instance:GetActRoundTimeByLayer(self.select_layer)
	is_empty = is_empty or FZGetRewardWGData.Instance:GetIsDrawEmptyByLayer(self.select_layer)
	self:FlushButtonState(cur_round, is_empty)
	self:FlushRed()
	self.node_list["fz_text_group"]:SetActive(true)
	if CountDownManager.Instance:HasCountDown("fengzheng_draw") then
		CountDownManager.Instance:RemoveCountDown("fengzheng_draw")
	end

	local has_big_reward = FZGetRewardWGData.Instance:GetBigRewardExistByLayer(self.select_layer)

	self.node_list["fz_special_fengzheng"]:SetActive(not has_big_reward)
	-- has_big_reward 还有大奖
	--射了没射完
	if not is_empty and has_big_reward then
		--本批风筝刷新还剩
		self.node_list["fz_special_desc1"].text.text = Language.TSXunBao.special_desc3
		CountDownManager.Instance:AddCountDown("fengzheng_draw", BindTool.Bind(self.UpdateTxtTime, self, is_empty),
					BindTool.Bind(self.CompleteTxtTime, self), nil, last_time, 0.3)
	else
		local select_round_max = FZGetRewardWGData.Instance:GetCurLayerRound(self.select_layer)
		local no_next = cur_round == select_round_max
		local bundle, asset = ResPath.GetF2RawImagesPNG("ts_db_hudie" .. (self.select_layer))
		self.node_list["fz_special_fengzheng"].raw_image:LoadSprite(bundle, asset, function ()
			self.node_list["fz_special_fengzheng"].raw_image:SetNativeSize()
		end)

		local special_desc = no_next and Language.TSXunBao.special_desc1 or Language.TSXunBao.special_desc2
		self.node_list["fz_special_desc1"].text.text = special_desc
		self.node_list["fz_special_desc2"]:SetActive(not no_next)

		if FZGetRewardWGData.Instance:HasNextPool(self.select_layer) then
			self.node_list["fz_text_group"]:SetActive(false)
			CountDownManager.Instance:AddCountDown("fengzheng_draw", BindTool.Bind(self.UpdateTxtTime, self, is_empty),
					BindTool.Bind(self.CompleteTxtTime, self), nil, last_time, 0.3)
		else
			-- 没有下一周期
			if no_next then
				if is_empty then
					self.node_list["fz_txt_time"].text.text = Language.TSXunBao.TxtTime .. "        "
				else
					self.node_list["fz_text_group"]:SetActive(false)
					CountDownManager.Instance:AddCountDown("fengzheng_draw", BindTool.Bind(self.UpdateTxtTime, self, is_empty),
						BindTool.Bind(self.CompleteTxtTime, self), nil, last_time, 0.3)
				end
			else
				CountDownManager.Instance:AddCountDown("fengzheng_draw", BindTool.Bind(self.UpdateTxtTime, self, is_empty),
						BindTool.Bind(self.CompleteTxtTime, self), nil, last_time, 0.3)
			end
		end

	end
end

function OperationActivityView:FlushRed()
	self.node_list["fz_btn_draw_red"]:SetActive(FZGetRewardWGData.Instance:IsShowBtnDrawRedByLayerView(self.select_layer))
	for i = 1 , 3 do
		self.node_list["fz_red" .. i]:SetActive(FZGetRewardWGData.Instance:IsShowBtnDrawRedByLayerView(i))
	end
end

--刷新按钮状态 ( 一键射完 和 小射一只 ) 1 
function OperationActivityView:FlushButtonState(cur_round, is_empty)
	if cur_round == nil then
		local _, cur_round = FZGetRewardWGData.Instance:GetActRoundTimeByLayer(self.select_layer)
	end
	is_empty = is_empty or FZGetRewardWGData.Instance:GetIsDrawEmptyByLayer(self.select_layer)
	--期数
	local select_round_max = FZGetRewardWGData.Instance:GetCurLayerRound(self.select_layer)

	if not is_empty then
		self:FlushButtonTxt(Language.TSXunBao.TxtBtnDraw1)
		self:FlushButtonGray(false)
		self.node_list["fz_item_1"]:SetActive(true)
	else
		if FZGetRewardWGData.Instance:HasNextPool(self.select_layer) then
			self:FlushButtonTxt(Language.TSXunBao.TxtBtnDraw1)
			self:FlushButtonGray(true)
			self.node_list["fz_item_1"]:SetActive(false)
			self.node_list["fz_item_2"]:SetActive(false)
		else
			self:FlushButtonGray(true)
			--已射完
			if cur_round == select_round_max then
				self:FlushButtonTxt(Language.TSXunBao.TxtBtnDraw4)
				self.btn_tip = Language.TSXunBao.BtnDrawTip4
				self.node_list["fz_item_1"]:SetActive(false)
				self.node_list["fz_item_2"]:SetActive(false)
			else
				self:FlushButtonTxt(Language.TSXunBao.TxtBtnDraw1)
				self.btn_tip = Language.TSXunBao.BtnDrawTip3
			end
		end
	end
end

--刷新按钮状态 ( 一键射完 和 小射一只 ) 2 
function OperationActivityView:FlushItemNum(cur_draw_info, is_empty)
	cur_draw_info = cur_draw_info or FZGetRewardWGData.Instance:GetCurDrawInfoByLayer(self.select_layer)
	is_empty = is_empty or FZGetRewardWGData.Instance:GetIsDrawEmptyByLayer(self.select_layer)
	if cur_draw_info == nil or is_empty then
		self.node_list["fz_item_1"]:SetActive(false)
		self.node_list["fz_item_2"]:SetActive(false)
		self.node_list["fz_btn_draw_all_red"]:SetActive(false)
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local color = num < cur_draw_info.draw_consume_item_count and COLOR3B.RED or COLOR3B.D_GREEN
	self.node_list["fz_txt_num_1"].text.text = ToColorStr(num .. "/" .. cur_draw_info.draw_consume_item_count, color)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["fz_txt_num_1"].rect)

    if not is_empty then
		local bundle, asset = ResPath.GetItem(cur_draw_info.draw_consume_item_id)
		self:SetCostItemIcon(bundle, asset)
    end

    -- 一键抽完按钮显隐
    self.node_list["fz_item_2"]:SetActive(cur_draw_info.one_key == 1)
    self.node_list["fz_btn_draw_all"]:SetActive(cur_draw_info.one_key == 1)
    if cur_draw_info.one_key == 1 then
    	-- 刷新一键数量
		local color = num < cur_draw_info.draw_all_consume_item_count and COLOR3B.RED or COLOR3B.D_GREEN
		self.node_list["fz_txt_num_2"].text.text = ToColorStr(num .. "/" .. cur_draw_info.draw_all_consume_item_count, color)
        UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["fz_txt_num_2"].rect)
		local lerp_num = cur_draw_info.draw_all_consume_item_count - num
    	self.node_list["fz_btn_draw_all_red"]:SetActive(lerp_num <= 0)
    end
end


--刷新左下角大奖记录数量
function OperationActivityView:FZFlushRecordNum()
    local num = FZGetRewardWGData.Instance:GetNewRecordCount()
	if num > 0 then
		self.node_list["fz_ts_record_show"]:SetActive(true)
		self.node_list["fz_ts_record_num"].text.text = num
	else
		self.node_list["fz_ts_record_show"]:SetActive(false)
	end
end


--创建哪吒说话气泡
-- function OperationActivityView:StartBubble()
-- 	self:CloseTalkBubble()
-- 	self.talk_bubble_show = GlobalTimerQuest:AddRunQuest(function()
-- 		self.node_list["fz_talk_bubble"].rect.localScale = Vector3(0,0,1)
-- 		self.node_list["fz_talk_bubble"].rect:DOScale(Vector3(1,1,1), 0.3)
-- 		self.close_bubble = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.CloseTalkBubble, self), 3)
-- 	end, 6)
-- end

-- --关闭气泡
-- function OperationActivityView:CloseTalkBubble()
-- 	if self:IsOpen() then
-- 		self.node_list["fz_talk_bubble"].rect:DOScale(Vector3(0,0,0), 0.3)
-- 	end
-- end

function OperationActivityView:SetCostItemIcon(bundle, asset)
	if bundle == nil or asset == nil then return end

	for i=1, 2 do
		if self.node_list["fz_item_icon_" .. i] then
			self.node_list["fz_item_icon_" .. i].image:LoadSprite(bundle, asset, function()
				self.node_list["fz_item_icon_" .. i]:SetActive(true)
				self.node_list["fz_item_icon_" .. i].image:SetNativeSize()
			end)
		end
	end
end


function OperationActivityView:OnClickGodGetItemBtn()
	local cur_draw_info = FZGetRewardWGData.Instance:GetCurDrawInfoByLayer(self.select_layer)
	local item_id = cur_draw_info and cur_draw_info.draw_consume_item_id
    if not item_id then
        return
    end 	
    TipWGCtrl.Instance:OpenItem({item_id = item_id, num = 1, is_bind = 1}, ItemTip.FROM_NORMAL)
end


function OperationActivityView:GetAlert()
	if not self.alert then
		self.alert = Alert.New()
		self.alert:SetShowCheckBox(true, "ts_xunbao")
		self.alert:SetCheckBoxText(Language.TSXunBao.AlertTip)
	end
end

-------------------------------------------------- 按钮事件 ---------------------------------------------------------

-- 一键抽取
function OperationActivityView:OnClickBtnDrawAll()
	if self.on_click_draw or self.is_animing then
		return
	end

	if self.btn_tip then
		local str = self.btn_tip
		if self.btn_tip == Language.TSXunBao.BtnDrawTip3 then
			str = string.format(str, CountDownManager.Instance:GetRemainSecond2HMS("fengzheng_draw"))
		end
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	end

	local cur_draw_info = FZGetRewardWGData.Instance:GetCurDrawInfoByLayer(self.select_layer)
		if cur_draw_info == nil or cur_draw_info.draw_consume_item_id == nil 
		or cur_draw_info.complement_num == nil or cur_draw_info.draw_consume_item_count == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TSXunBao.TxtTime)
		self.on_click_draw = false
		return
	end

	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_all_consume_item_count - num
	if dif_num > 0 then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cur_draw_info.draw_consume_item_id)
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		local str = string.format(Language.Xunbao.TsNotEnough, dif_num, item_name, cur_draw_info.complement_num * dif_num)
		self:GetAlert()
		self.alert:SetLableString(str)
		self.alert:SetOkFunc(BindTool.Bind(self.SendAllDraw, self))
		self.alert:Open()
	else
		self:SendAllDraw()
	end

end

-- 小抽一下
function OperationActivityView:OnClickBtnDraw()
	if self.on_click_draw or self.is_animing then
		return
	end
	if self.btn_tip then
		local str = self.btn_tip
		if self.btn_tip == Language.TSXunBao.BtnDrawTip3 then
			str = string.format(str, CountDownManager.Instance:GetRemainSecond2HMS("fengzheng_draw"))
		end
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	end

	local cur_draw_info = FZGetRewardWGData.Instance:GetCurDrawInfoByLayer(self.select_layer)
	if cur_draw_info == nil or cur_draw_info.draw_consume_item_id == nil 
		or cur_draw_info.complement_num == nil or cur_draw_info.draw_consume_item_count == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TSXunBao.TxtTime)
		self.on_click_draw = false
		return
	end
	local num = ItemWGData.Instance:GetItemNumInBagById(cur_draw_info.draw_consume_item_id)
	local dif_num = cur_draw_info.draw_consume_item_count - num
	if dif_num > 0 then
		local item_cfg = ItemWGData.Instance:GetItemConfig(cur_draw_info.draw_consume_item_id)
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		local str = string.format(Language.Xunbao.TsNotEnough, dif_num, item_name, cur_draw_info.complement_num * dif_num)
		self:GetAlert()
		self.alert:SetLableString(str)
		self.alert:SetOkFunc(BindTool.Bind(self.SendDraw, self))
		self.alert:Open()
	else
		self:SendDraw()
	end
end

-- 一键抽取发协议
function OperationActivityView:SendAllDraw()
	self.on_click_draw = true
	FZGetRewardWGCtrl.Instance:SendOpera(FZ_XUNBAO_OPERA_TYPE.All_DRAW, self.select_layer, 1)
	self.one_key_reward_list = FZGetRewardWGData.Instance:GetCurGetList(self.select_layer, true)

	if self.click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.click_draw_timequest)
		self.click_draw_timequest = nil
	end
	self.click_draw_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self.on_click_draw = false
	end, 1)
end

-- 小抽一下发协议
function OperationActivityView:SendDraw()
	self.on_click_draw = true
	FZGetRewardWGCtrl.Instance:SendOpera(FZ_XUNBAO_OPERA_TYPE.DRAW, self.select_layer, 1)
	self.one_key_reward_list = nil

	if self.click_draw_timequest then
		GlobalTimerQuest:CancelQuest(self.click_draw_timequest)
		self.click_draw_timequest = nil
	end
	self.click_draw_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self.on_click_draw = false
	end, 1)
end

-- 勾选跳过动画
function OperationActivityView:OnClickBtnCheck()
	self.ignore_anim = not self.ignore_anim
	self.node_list["fz_img_yes"]:SetActive(self.ignore_anim)
	local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt("fengzheng_draw" .. role_id, self.ignore_anim and 1 or 0)
end

--重置
function OperationActivityView:OnClickBtnReset()
	FZGetRewardWGCtrl.Instance:SendOpera(FZ_XUNBAO_OPERA_TYPE.RESET_LAYRE, self.select_layer)
end

--大奖记录
function OperationActivityView:OnClickBtnRecoed()
	FZGetRewardWGCtrl.Instance:OpenRecordView()
end

--tips
-- function OperationActivityView:OnClickBtnTip()
-- 	RuleTip.Instance:SetContent(Language.TSXunBao.TipContent, Language.TSXunBao.TipTitle)
-- end

--------------------------------------------------- 风筝动画 ----------------------------------------------

--开始播放抽取动画,协议来才会播
function OperationActivityView:FZStartDrawAnim()
	self.on_click_draw = false
	--勾选了跳过动画
	if self.ignore_anim then
		self:FlushAnimEnd()
		self:SkipBestKiteDropAnim()
	else

	--没有勾选
		self:ChangeBtnLayerState(true)
		self:FZPrepareAnimTime()
	end
end


function OperationActivityView:FlushAnimEnd()
	local is_empty = FZGetRewardWGData.Instance:GetIsDrawEmptyByLayer(self.select_layer)
	self:ChangeBtnLayerState(false)
	self:FlushLayerShow()
	self:FlushButtonState(nil, is_empty)

	if self.one_key_reward_list then
		TipWGCtrl.Instance:ShowGetReward(nil, self.one_key_reward_list)
		self.one_key_reward_list = nil
	else
		local data_list = FZGetRewardWGData.Instance:GetCurGetList()
		TipWGCtrl.Instance:ShowGetReward(nil, data_list)
	end
	self:ResetAnim()
	self:GetTxtTime()
end


function OperationActivityView:SkipBestKiteDropAnim()
	self.tween_time = 0
	local result = FZGetRewardWGData.Instance:GetResultInfo()
	local reward_info
	
	if result.is_one_key then
		self:FlushBigRewardAnim()
	else
 		reward_info = FZGetRewardWGData.Instance:GetRewardByLayerAndSlot(result.layer, result.slot)
		self:PlayKiteAnim(reward_info.reward_id, true)
	end
end

-- 准备动画
function OperationActivityView:FZPrepareAnimTime()
	self.tween_time = 0
	--扔出eff
	self:PlayRoleAnim()
	--计算旋转
    local reward_info
	local result = FZGetRewardWGData.Instance:GetResultInfo()
	if result.is_one_key then
		reward_info = FZGetRewardWGData.Instance:GetBestRewardInfo(self.select_layer)
	else
	    reward_info = FZGetRewardWGData.Instance:GetRewardByLayerAndSlot(result.layer, result.slot)
	end
	if not reward_info or not next(reward_info) then
		return
	end
	local end_pos = Vector2(reward_info.x_pos, reward_info.y_pos) - self.def_pos
	self:PlayRotAnim(end_pos)
	--位移
	local tween_len = u3d.v2Length(end_pos, self.def_pos)
	self:PlayArrowAnim(tween_len)

	--播放射中风筝动画
	if result.is_one_key then
		self:PlayAllKiteAnim()
	else
		self:PlayKiteAnim(reward_info.reward_id, false)
	end

	if self.tween_timequest then
		GlobalTimerQuest:CancelQuest(self.tween_timequest)
		self.tween_timequest = nil
	end
	self.tween_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self:FlushAnimEnd()
	end, self.tween_time)
end

function OperationActivityView:PlayRoleAnim()
	--哪吒扔出特效fz_effect_feichu
	self.node_list["fz_effect_feichu"]:SetActive(true)
	local tween = self.node_list["fz_effect_feichu"].rect:DOLocalMoveX(83, 0.5)
	tween:SetEase(DG.Tweening.Ease.Linear)
	tween:OnComplete(function ()
		--重置位置
		self.node_list["fz_effect_feichu"].transform.anchoredPosition = Vector2(-209, -181)
		self.node_list["fz_effect_feichu"]:SetActive(false)
	end)

	local role_time = FZGetRewardWGData.Instance:GetAnimRoleTime()
	self.tween_time = self.tween_time + role_time
end

--旋转动画
function OperationActivityView:PlayRotAnim(end_pos)
	local angle_speed = FZGetRewardWGData.Instance:GetAnimAngleSpeed()
	local rot = Vector2.Angle(Vector2.up, end_pos)
	local tween_time = rot/angle_speed
	--左边还是右边
	local dir = end_pos.x > self.def_pos.x and -1 or 1

	if self.arrow_anim_timequest then
		GlobalTimerQuest:CancelQuest(self.arrow_anim_timequest)
		self.arrow_anim_timequest = nil
	end
	self.arrow_anim_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self.node_list["fz_arrow_anim"].rect:DORotate(Vector3(0, 0, dir * rot), tween_time)
	end, self.tween_time)
	self.tween_time = self.tween_time + tween_time
end

function OperationActivityView:PlayArrowAnim(tween_len)
	local arrow_speed = FZGetRewardWGData.Instance:GetAnimArrowSpeed()
	local arrow_time = tween_len/arrow_speed
		local pos_y = self.node_list["fz_img_arrow"].rect.anchoredPosition.y
	if self.img_arrow_timequest then
		GlobalTimerQuest:CancelQuest(self.img_arrow_timequest)
		self.img_arrow_timequest = nil
	end
	self.img_arrow_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self.node_list["fz_img_arrow"].rect:DOAnchorPosY(pos_y + tween_len, arrow_time)
		AudioManager.PlayAndForget(ResPath.UiseRes("effect_kiteshoot"))
	end, self.tween_time)

	if self.img_arrow_timequest1 then
		GlobalTimerQuest:CancelQuest(self.img_arrow_timequest1)
		self.img_arrow_timequest1 = nil
	end
	self.img_arrow_timequest1 = GlobalTimerQuest:AddDelayTimer(function()
		self.node_list["fz_img_arrow"]:SetActive(false)
	end, self.tween_time + 0.7)
	self.tween_time = self.tween_time + arrow_time
end

function OperationActivityView:PlayKiteAnim(reward_id, skip_tween)
	local kite_time = FZGetRewardWGData.Instance:GetAnimKiteTime()
	for i, v in ipairs(self.reward_list) do
		local data = v:GetData()
		if data == reward_id then
			if skip_tween then
				if v.is_best then
					self:PlayDropAnim(kite_time, skip_tween)
				end
			else
				if self.play_drop_anim_timequest then
					GlobalTimerQuest:CancelQuest(self.play_drop_anim_timequest)
					self.play_drop_anim_timequest = nil
				end
				self.play_drop_anim_timequest = GlobalTimerQuest:AddDelayTimer(function()
					if v.is_best then
						self:PlayDropAnim(kite_time, skip_tween)
					end
					v:PlayDropAnim(kite_time)
				end, self.tween_time - 0.2)
			end
			break
		end
	end
	self.tween_time = self.tween_time + kite_time
end

function OperationActivityView:PlayAllKiteAnim()
	local kite_time = FZGetRewardWGData.Instance:GetAnimKiteTime()
	
	if self.play_drop_anim_timequest then
		GlobalTimerQuest:CancelQuest(self.play_drop_anim_timequest)
		self.play_drop_anim_timequest = nil
	end

	self.play_drop_anim_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self:PlayDropAnim(kite_time)
		for i, v in ipairs(self.reward_list) do
			if v:GetKiteState() then
				v:PlayDropAnim(kite_time)
			end
		end
	end, self.tween_time - 0.2)
	self.tween_time = self.tween_time + kite_time
end

function OperationActivityView:PlayDropAnim(time, skip_tween)
	time = skip_tween and 0.1 or time
	self.node_list["ske_pos"].canvas_group:DoAlpha(1, 0, time):SetEase(DG.Tweening.Ease.Linear)
end

function OperationActivityView:ResetAnim()
	self.node_list["fz_arrow_anim"].rect.rotation = Quaternion.Euler(0, 0, 0)
	self.node_list["fz_img_arrow"]:SetActive(true)
	self.node_list["fz_img_arrow"].rect.anchoredPosition = Vector2(3.2, 23.5)
end

function OperationActivityView:FlushButtonTxt(text)
	self.node_list["fz_txt_btn_draw"].text.text = text
end

function OperationActivityView:FlushButtonGray(status)
	XUI.SetGraphicGrey(self.node_list["fz_btn_draw"], status)
	XUI.SetGraphicGrey(self.node_list["fz_btn_draw_all"], status)

	if not status then
		self.btn_tip = nil
	end
end

function OperationActivityView:UpdateTxtTime(is_empty, elapse_time, total_time)
	local str = ""
	local remain_time = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
	if is_empty then
		if FZGetRewardWGData.Instance:HasNextPool(self.select_layer) then
			str = string.format(Language.TSXunBao.TxtTime1, remain_time)
		else
			str = string.format(Language.TSXunBao.TxtTime2, remain_time)
		end
		
	else
		str = string.format(Language.TSXunBao.TxtTime1, remain_time)
	end

	if self.node_list["fz_txt_time"] then
		self.node_list["fz_txt_time"].text.text = str
	end

	if self.node_list["fz_special_desc2"] then
		self.node_list["fz_special_desc2"].text.text = remain_time
	end
end

function OperationActivityView:CompleteTxtTime()
	self:OnClickBtnLayer(self.select_layer, true)

end

--发射的时候不可以切换页签
function OperationActivityView:ChangeBtnLayerState(status)
	self.is_animing = status
	for i = 1, 3 do
		self.node_list["fz_layer_btn" .. i].toggle.interactable = not status
	end
end


function OperationActivityView:SetFZShowView(node_name, cfg, is_raw)
	local node_list = self.node_list[node_name]
	if is_raw then
		local asset1, bundle1 = ResPath.GetF2RawImagesPNG(cfg)
		node_list.raw_image:LoadSprite(asset1, bundle1,function ()
			node_list.raw_image:SetNativeSize()
		end)
	else
		local asset2, bundle2 = ResPath.GetOperationFZduoBaoImagePath(cfg)
		node_list.image:LoadSprite(asset2, bundle2,function ()
			node_list.image:SetNativeSize()
		end)
	end
end

function OperationActivityView:FlushLayerImg(index)
	local view_info_cfg = FZGetRewardWGData.Instance:GetViewCfgByInterface(index)
	local big_info = FZGetRewardWGData.Instance:GetBestRewardInfoByNone()
	if nil == view_info_cfg or IsEmptyTable(view_info_cfg) or nil == big_info or IsEmptyTable(big_info) then
		return
	end
	--蝴蝶龙骨位置
	self.node_list["ske_pos"].transform.anchoredPosition = Vector2(big_info.x_pos, big_info.y_pos - 81)
	--哪吒模型
	if not self.model then
		self.model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["fz_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.model:SetRenderTexUI3DModel(display_data)
	end

	local tab = view_info_cfg.nezha_model_path
	local tab_cfg = Split(tab,"|")
	local bundle = tab_cfg[1]
	local asset = tab_cfg[2]
	self.model:SetMainAsset(bundle, asset)
	self.model:CustomDisplayPositionAndRotation(Vector3(-1, -2.16, 0), Vector3(0, -240, 0), Vector3(1.5, 1.5, 1.5))
	--底座图片
	self.node_list["fz_raw_bg"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(view_info_cfg.fz_raw_bg))
	self.node_list["fz_img_base"].image:LoadSprite(ResPath.GetOperationFZduoBaoImagePath(view_info_cfg.fz_img_base))
	self.node_list["fz_img_arrow"].image:LoadSprite(ResPath.GetOperationFZduoBaoImagePath(view_info_cfg.fz_img_arrow))
	self.node_list["fz_img_launcher"].image:LoadSprite(ResPath.GetOperationFZduoBaoImagePath(view_info_cfg.fz_img_launcher))
	--蝴蝶龙骨
	self.ske_hudie = AllocAsyncLoader(self, "yunying_hudie")
	self.ske_hudie:SetIsUseObjPool(true)
	self.ske_hudie:SetParent(self.node_list["ske_pos"].transform)
	self.ske_hudie:Load("uis/view/operation_activity_ui/fengzheng_ui_prefab", view_info_cfg.ske_hudei)
end


--设置界面的所有显示信息包括底图(只需要赋值一次的)
function OperationActivityView:SetFZViewShowInfo()
	local view_info_cfg = FZGetRewardWGData.Instance:GetViewCfgByInterface()
	if nil == view_info_cfg or IsEmptyTable(view_info_cfg) then
		return
	end
	self:SetFZShowView("btn_bg1", view_info_cfg.btn_hl1)
	self:SetFZShowView("btn_bg2", view_info_cfg.btn_hl2)
	self:SetFZShowView("btn_bg3", view_info_cfg.btn_hl3)
	self:SetFZShowView("mask_bg1", view_info_cfg.btn_nor1)
	self:SetFZShowView("mask_bg2", view_info_cfg.btn_nor2)
	self:SetFZShowView("mask_bg3", view_info_cfg.btn_nor3)
	self:SetFZShowView("decoration_bg", view_info_cfg.decoration_bg)
	self:SetFZShowView("fz_talk_bubble", view_info_cfg.fz_talk_bubble)
	self:SetFZShowView("fz_select_image1", view_info_cfg.fz_select_image)
	self:SetFZShowView("fz_select_image2", view_info_cfg.fz_select_image)
	self:SetFZShowView("fz_select_image3", view_info_cfg.fz_select_image)
end

--设置显示右下角倒计时
function OperationActivityView:SetRightTimeShow()
	--活动信息
	local act_info = FZGetRewardWGData.Instance:GetActOpenTimeCuo()
	--当前周期(服务端周期从0开始,但是切割完的list是从1开始)
	local now_count = FZGetRewardWGData.Instance:GetRoundIndexIdByLayer(self.select_layer) + 1
	--周期时长cfg
	local count_time_cfg = FZGetRewardWGData.Instance:GetNowCountTimeByLayer(self.select_layer)
	--活动已开启时间
	local act_start_time = FZGetRewardWGData.Instance:GetActOpenTime()
	--本批活动结束时间戳
	local now_count_end_time = act_info.start_time + now_count * count_time_cfg[now_count] * 3600
	if now_count_end_time <= act_info.end_time then
		self:SetActRemainTime(TabIndex.operation_act_fengzheng, now_count_end_time)
		self:SetRightTimeTitleText(Language.OpertionAcitvity.ActivityTimeNowCount)
	else
		self:SetActRemainTime(TabIndex.operation_act_fengzheng)
		self:SetRightTimeTitleText(Language.OpertionAcitvity.ActivityTime)
	end
end
