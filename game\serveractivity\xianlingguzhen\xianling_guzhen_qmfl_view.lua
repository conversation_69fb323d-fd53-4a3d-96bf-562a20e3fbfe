XianLingGuZhenQMFLView = XianLingGuZhenQMFLView or BaseClass(SafeBaseView)

function XianLingGuZhenQMFLView:__init()
	self.full_screen = false
	self:SetMaskBg(true)
    self.default_index = TabIndex.xlgz_qmhb
	self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen_qmfl_bg")
    self:AddViewResource(TabIndex.xlgz_qmhb, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen_qmhb")
    self:AddViewResource(TabIndex.xlgz_qmlj, "uis/view/xianling_guzhen_prefab", "layout_xianling_guzhen_qmlj")
    self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "VerticalTabbar")

    self.tab_sub = {}
	self.remind_tab = {
		{RemindName.XianLingGuZhen_QMHB},
		{RemindName.XianLingGuZhen_QMLJ},
	}
end

function XianLingGuZhenQMFLView:LoadCallBack()
    if not self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:Init(Language.XianLingGuZhen.QMFLTable, nil, "uis/view/xianling_guzhen_prefab", nil, self.remind_tab)
        self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
    end

    if not self.time_quest then
        self.time_quest = GlobalTimerQuest:AddRunQuest(function ()
            self:CacularActivityTime()
        end,1)
    
        self:CacularActivityTime()
    end

    XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickTipBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_daily_reward, BindTool.Bind(self.OnClickDailyRewardBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_todraw, BindTool.Bind(self.OnClickToDrawBtn, self))
    
end

function XianLingGuZhenQMFLView:ReleaseCallBack()
    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end

    self:QMLJReleaseCallBack()
    self:QMHBReleaseCallBack()
end

function XianLingGuZhenQMFLView:LoadIndexCallBack(index)
    if index == TabIndex.xlgz_qmhb then
        self:LoadQMHBView()
    elseif index == TabIndex.xlgz_qmlj then
        self:LoadQMLJView()
    end
end

function XianLingGuZhenQMFLView:ShowIndexCallBack(index)
    self.node_list.desc_tip.text.text = Language.XianLingGuZhen.QMFLBOMTipDesc[index] or ""

    if index == TabIndex.xlgz_qmhb then

    elseif index == TabIndex.xlgz_qmlj then
        XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.SERVER_DRAW_TIMES_REWARD_INFO)
    end
end

function XianLingGuZhenQMFLView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
	    if k == "all" then
            if index == TabIndex.xlgz_qmhb then
                self:OnFlushQMHBView()
            elseif index == TabIndex.xlgz_qmlj then
                self:OnFlushQMLJView()
            end
        end
    end

    local get_daily_reward = XianLingGuZhenWGData.Instance:IsGetDailyReward()
    self.node_list.btn_daily_reward:CustomSetActive(not get_daily_reward)
    self.node_list.btn_daily_reward_remind:CustomSetActive(not get_daily_reward)
end

--------------------------------------------------com_start----------------------------------------------
function XianLingGuZhenQMFLView:OnClickTipBtn()
    RuleTip.Instance:SetTitle(Language.XianLingGuZhen.QMFLTipTitleDesc[self.show_index])
    RuleTip.Instance:SetContent(Language.XianLingGuZhen.QMFLTipTitleContentDesc[self.show_index])
end

function XianLingGuZhenQMFLView:OnClickDailyRewardBtn()
    local get_daily_reward = XianLingGuZhenWGData.Instance:IsGetDailyReward()

    if not get_daily_reward then
        XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.FETCH_DAILY_REWARD_INFO)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.YiLingQu)
    end
end

function XianLingGuZhenQMFLView:CacularActivityTime()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN)
	local now_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = activity_status and activity_status.end_time or 0

	if end_time > now_time then
        if self.node_list["act_time"] then
            self.node_list["act_time"].text.text = string.format(Language.Common.ActTimeEnd, TimeUtil.FormatSecondDHM2(end_time - now_time))
        end
	else
		if self.time_quest then
			GlobalTimerQuest:CancelQuest(self.time_quest)
			self.time_quest = nil
		end

        if self.node_list["act_time"] then
            self.node_list["act_time"].text.text =  string.format(Language.Common.ActTimeEnd, Language.XianLingGuZhen.Activity_End)
        end
	end
end

function XianLingGuZhenQMFLView:OnClickToDrawBtn()
    ViewManager.Instance:Open(GuideModuleName.XianLingGuZhen)
    self:Close()
end

---------------------------------------------------com_end-----------------------------------------------

-------------------------------------------------quhb_start----------------------------------------------
function XianLingGuZhenQMFLView:LoadQMHBView()
    if not self.qmhb_list then
        self.qmhb_list = AsyncListView.New(QMHBListItemCellRender, self.node_list.qmhb_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_qmhb_send, BindTool.Bind(self.OnClickQMHBSendBtn, self))
end

function XianLingGuZhenQMFLView:QMHBReleaseCallBack()
    if self.qmhb_list then
        self.qmhb_list:DeleteMe()
        self.qmhb_list = nil
    end
end

function XianLingGuZhenQMFLView:OnFlushQMHBView()
    local rea_paper_data, world_red_paper, wait_data_list = XianLingGuZhenWGData.Instance:GetQMHBRedPaperDataList()
    local has_red_paper = not IsEmptyTable(rea_paper_data)
    self.node_list.qmhb_list:CustomSetActive(has_red_paper)
    self.node_list.qmhb_no_data:CustomSetActive(not has_red_paper)

    if has_red_paper then
        table.sort(rea_paper_data, self.SortQMHBDataList())
        self.qmhb_list:SetDataList(rea_paper_data)
    end

    -- 发红包数据
    local has_wait_red_paper = not IsEmptyTable(wait_data_list)
    self.node_list.qmhb_cost_icon:CustomSetActive(has_wait_red_paper)
    self.node_list.desc_qmhb_send_cost:CustomSetActive(has_wait_red_paper)
    self.node_list.btn_qmhb_send_remind:CustomSetActive(has_wait_red_paper)

    if has_wait_red_paper then
        local wait_red_paper_data = wait_data_list[1]
        local red_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperCfgByTypeAndLevel(wait_red_paper_data.paper_type, wait_red_paper_data.paper_level)

        if not IsEmptyTable(red_cfg) then
            self.node_list.desc_qmhb_send_cost.text.text = red_cfg.gold
            self.node_list.qmhb_cost_icon:CustomSetActive(true)
            self.node_list.desc_qmhb_send_cost:CustomSetActive(true)
        end
    end
end

-- 可领取 可发送 
function XianLingGuZhenQMFLView:SortQMHBDataList()
    local cal_sort_value = function(sort_value, data)
        local red_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperCfgByTypeAndLevel(data.paper_type, data.paper_level)
        local target_value = sort_value

        if data.is_world_paper then
            if not IsEmptyTable(red_cfg) then
                local record_list = data.record_list
                local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
                local is_get = false
                local get_over = #record_list >= red_cfg.num
                
                if not IsEmptyTable(record_list) then
                    for k, v in pairs(record_list) do
                        if v.uid == my_uid then
                            is_get = true
                            break
                        end
                    end
                end
    
                if not get_over and not is_get then
                    target_value = target_value + 10000
                end
            end
        elseif data.is_wait_send then
            target_value = target_value + 100
        end

        return target_value
    end

    return function(a, b)
        local order_a = 1000000
		local order_b = 1000000
		
		if a == nil or b == nil then
			return order_a > order_b
		end

        order_a = cal_sort_value(order_a, a)
        order_b = cal_sort_value(order_b, b)

        return order_a > order_b
    end
end

function XianLingGuZhenQMFLView:OnClickQMHBSendBtn()
    local wait_send_red_data = XianLingGuZhenWGData.Instance:GetQMHBWaitRedPaperDataList()

    if not IsEmptyTable(wait_send_red_data) then
        local wait_red_paper_data = wait_send_red_data[1]
        -- local red_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperCfgByTypeAndLevel(wait_red_paper_data.paper_type, wait_red_paper_data.paper_level)
        WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_DISTRIBUTE_SYSTEM, wait_red_paper_data.info_index)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.XianLingGuZhen.NoCanSendRedPaper)
    end
end

----------------------------------------QMHBListItemCellRender-------------------------------------
QMHBListItemCellRender = QMHBListItemCellRender or BaseClass(BaseRender)

function QMHBListItemCellRender:LoadCallBack()
    if not self.head_cell then
        self.head_cell = BaseHeadCell.New(self.node_list.head_icon)
    end

    XUI.AddClickEventListener(self.node_list.btn_to_get, BindTool.Bind(self.OnClickGetBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_cksq, BindTool.Bind(self.OnClickCKSQBtn, self))
    XUI.AddClickEventListener(self.node_list.wait_send, BindTool.Bind(self.OnClickWaitSendBtn, self))
end

function QMHBListItemCellRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function QMHBListItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local red_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperCfgByTypeAndLevel(self.data.paper_type, self.data.paper_level)
    if IsEmptyTable(red_cfg) then
        return
    end

    self.node_list.hb_name.text.text = red_cfg.name
    local head_data = {}

    if self.data.is_world_paper then
        head_data.role_id = self.data.owner_uid
        head_data.prof = self.data.prof
        head_data.sex = self.data.sex
        self.node_list.name.text.text = self.data.owner_game_name

        self.node_list.wait_send:CustomSetActive(false)
        -- 是否领取

        local record_list = self.data.record_list
        local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
        local is_get = false

        local get_over = #record_list >= red_cfg.num
        if not IsEmptyTable(record_list) then
            for k, v in pairs(record_list) do
                if v.uid == my_uid then
                    is_get = true
                    break
                end
            end
        end

        local can_get = not get_over and not is_get
        
        self.node_list.flag_get:CustomSetActive(is_get)
        self.node_list.btn_cksq:CustomSetActive(not can_get)
        self.node_list.btn_to_get:CustomSetActive(can_get)
        self.node_list.flag_get_over:CustomSetActive(not is_get and get_over)
    else
        local role_vo = GameVoManager.Instance:GetMainRoleVo()
        head_data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
        head_data.prof = role_vo.prof
        head_data.sex = role_vo.sex
        self.node_list.wait_send:CustomSetActive(true)
        self.node_list.name.text.text = RoleWGData.Instance:GetAttr("name")

        self.node_list.flag_get:CustomSetActive(false)
        self.node_list.btn_cksq:CustomSetActive(false)
        self.node_list.btn_to_get:CustomSetActive(false)
        self.node_list.flag_get_over:CustomSetActive(false)
    end

	self.head_cell:SetData(head_data)
end

function QMHBListItemCellRender:OnClickGetBtn()
    WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_RECEIVE_WORLD, self.data.info_index)
    GuildWGCtrl.Instance:OpenGuildRedPacketView(self.data, 0)
end

function QMHBListItemCellRender:OnClickCKSQBtn()
    GuildWGCtrl.Instance:OpenGuildRedPacketView(self.data, 0)
end

function QMHBListItemCellRender:OnClickWaitSendBtn()
    WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_DISTRIBUTE_SYSTEM, self.data.info_index)
end
--------------------------------------------------quhb_end-----------------------------------------------

-------------------------------------------------qulj_start----------------------------------------------
function XianLingGuZhenQMFLView:LoadQMLJView()
    if not self.qmlj_list then
        self.qmlj_list = AsyncListView.New(QMLJListItemCellRender, self.node_list.qmlj_list)
    end
end

function XianLingGuZhenQMFLView:QMLJReleaseCallBack()
    if self.qmlj_list then
        self.qmlj_list:DeleteMe()
        self.qmlj_list = nil
    end
end

function XianLingGuZhenQMFLView:OnFlushQMLJView()
    local data_list = XianLingGuZhenWGData.Instance:GetCurServerDrawTimeRewardCfg()
    data_list = ListIndexFromZeroToOne(data_list)
    table.sort(data_list, self.SortQMLJDataList())
    self.qmlj_list:SetDataList(data_list)
end

-- 能领取排前面
function XianLingGuZhenQMFLView:SortQMLJDataList()
    local cal_sort_value = function(order_value, data)
        local a_can_get, a_is_get = XianLingGuZhenWGData.Instance:IsCanGetQMFLByData(data)
        local target_value = order_value

        if a_can_get then
            target_value = target_value + 1000000 - data.draw_times
        elseif not a_is_get then
            target_value = target_value + 100000 - data.draw_times
        end

        return target_value
    end

    return function(a, b)
        local order_a = 10000000
		local order_b = 10000000
		
		if a == nil or b == nil then
			return order_a > order_b
		end

        order_a = cal_sort_value(order_a, a)
        order_b = cal_sort_value(order_b, b)

        return order_a > order_b
    end
end

----------------------------------------QMLJListItemCellRender----------------------------------------
QMLJListItemCellRender = QMLJListItemCellRender or BaseClass(BaseRender)
function QMLJListItemCellRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function QMLJListItemCellRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickGetBtn, self))
end

function QMLJListItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    
    self.reward_list:SetDataList(self.data.reward_item)
    local draw_time = XianLingGuZhenWGData.Instance:GetNewLingXiMangTuServerDrawTimes()
    local need_time = self.data.draw_times
    local time_enogth = draw_time >= need_time

    self.node_list["desc_info"].text.text = string.format(Language.XianLingGuZhen.QMLJCellDrawInfo)
    local draw_time_str = time_enogth and ToColorStr(draw_time, COLOR3B.L1) or draw_time
    self.node_list["desc_num"].text.text = string.format("%s%s", draw_time_str, ToColorStr("/"..need_time, "#fff45c"))


    local get_flag = XianLingGuZhenWGData.Instance:GetServerDrawTimesRewardFlag(self.data.seq)
    local is_get = get_flag == 1

    self.node_list.flag_get:CustomSetActive(is_get)
    self.node_list.btn_get:CustomSetActive(not is_get and time_enogth)
    self.node_list.btn_get_remind:CustomSetActive(not is_get and time_enogth)
end

function QMLJListItemCellRender:OnClickGetBtn()
    local draw_time = XianLingGuZhenWGData.Instance:GetNewLingXiMangTuServerDrawTimes()
    local need_time = self.data.draw_times
    local time_enogth = draw_time >= need_time
    local get_flag = XianLingGuZhenWGData.Instance:GetServerDrawTimesRewardFlag(self.data.seq)
    local is_get = get_flag == 1

    local str = ""
    if is_get then
        str = Language.Common.YiLingQu
    else
        if time_enogth then
            XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.FETCH_SERVER_DRAW_TIMES_REWARD, self.data.seq)
            return
        else
            str = Language.XianLingGuZhen.QMLJTimeNotEnough
        end
    end

    SysMsgWGCtrl.Instance:ErrorRemind(str)
end

--------------------------------------------------qulj_end-----------------------------------------------