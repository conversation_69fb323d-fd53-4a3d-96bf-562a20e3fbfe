require("game/appearance/appearance_wg_data")
require("game/appearance/appearance_view_get_new_appearance") --激活新的东西的提示
require("game/appearance/first_ts_get_new_view")
require("game/appearance/appearance_remove_view")

AppearanceWGCtrl = AppearanceWGCtrl or BaseClass(BaseWGCtrl)

function AppearanceWGCtrl:__init()
	if nil ~= AppearanceWGCtrl.Instance then
		ErrorLog("[AppearanceWGCtrl]:Attempt to create singleton twice!")
	end
	AppearanceWGCtrl.Instance = self
	self.data = AppearanceWGData.New()
	self.get_new_view = AppearanceGetNew.New(GuideModuleName.AppearanceGetNew)
	self.first_ts_get_new_view = FirstTSGetNewView.New(GuideModuleName.FirstTSGetNewView)
	self.remove_view = AppearanceRemoveView.New(GuideModuleName.AppearanceRemoveView)

	self.getAppearanceCallBack = {}
	self.get_new_appe_info_list = {}
	self.remove_appe_info_list = {}
	self:RegisterAllProtocol()
end

function AppearanceWGCtrl:AddGetAppearanceCallBack(call_back)
	for k,v in pairs(self.getAppearanceCallBack) do
		if k == call_back then
			return
		end
	end
	self.getAppearanceCallBack[call_back] = call_back
end

function AppearanceWGCtrl:RemoveGetAppearanceCallBack(call_back)
	for k,v in pairs(self.getAppearanceCallBack) do
		if k == call_back then
			self.getAppearanceCallBack[call_back] = nil
			return
		end
	end
end

function AppearanceWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	self.getAppearanceCallBack = nil

	if self.get_new_view then
		self.get_new_view:DeleteMe()
		self.get_new_view = nil
	end

	if self.appearance_yuling_image_view then
		self.appearance_yuling_image_view:DeleteMe()
		self.appearance_yuling_image_view = nil
	end

	if self.first_ts_get_new_view then
		self.first_ts_get_new_view:DeleteMe()
		self.first_ts_get_new_view = nil
	end

	if self.remove_view then
		self.remove_view:DeleteMe()
		self.remove_view = nil
	end

	self.remove_first = nil

	AppearanceWGCtrl.Instance = nil
end

-- 注册协议
function AppearanceWGCtrl:RegisterAllProtocol()
	self:RegisterProtocol(SCGetNewAppearance,"OnGetNewAppearance")
	self:RegisterProtocol(SCRemoveActivateAppe,"OnSCRemoveActivateAppe")
end

function AppearanceWGCtrl:OnGetNewAppearance(protocol)
	local info = {}
	info.appe_type = protocol.appe_type
	info.appe_image_id = protocol.appe_image_id
	info.index_param = protocol.index_param
	if protocol.auto_cutdown == true then
		info.auto_cutdown = true
	end

	--print_error("----恭喜获得----", info)
	table.insert(self.get_new_appe_info_list, info)
	self:OpenGetNewView()
end

function AppearanceWGCtrl:GetNewAppeInfoList()
	return self.get_new_appe_info_list
end

--特殊处理 比如说激活了形象后 就激活一个技能 需要先弹形象的恭喜获得面板 再弹获得技能面板
--目前 器魂符文技能激活 有处理
function AppearanceWGCtrl:AddNewAppeInfoListFromSkill(data)
	local info = {}
	info.appe_type = 0
	info.appe_image_id = 0
	info.is_skill = true
	info.skill_data = data
	table.insert(self.get_new_appe_info_list, info)
	self:OpenGetNewView()
end

function AppearanceWGCtrl:AddAndOpenNewAppeInfoList(data)
	if IsEmptyTable(data) then
		return
	end
	table.insert(self.get_new_appe_info_list, data)
	self:OpenGetNewView()
end

function AppearanceWGCtrl:CanOpenGetNewView()
	if self.get_new_view then
		local info = self.get_new_view:GetInfo()
		if not IsEmptyTable(info) then
			return false
		end
	end

	if IsEmptyTable(self.get_new_appe_info_list) then
		return false
	end

	local info = self.get_new_appe_info_list[1]
	if info.is_skill then
		return false
	end

	return true
end

--打开恭喜获得界面
function AppearanceWGCtrl:OpenGetNewView()
	if self.get_new_view then
		local info = self.get_new_view:GetInfo()
		if not IsEmptyTable(info) then
			return
		end
	end

	if IsEmptyTable(self.get_new_appe_info_list) then
		return
	end

	local info = table.remove(self.get_new_appe_info_list,1)
	if info.is_skill then
		TipWGCtrl.Instance:ShowGetNewSkillView2(info.skill_data) 
	else
		if self.get_new_view then
			self.data:SetOpenGetNewViewState(true)
			if MainuiWGCtrl.Instance:IsLoadMainUiView() then
				self.get_new_view:SetContent(info)
				TipWGCtrl.Instance:PlayAniTab(GuideModuleName.AppearanceGetNew, "add")
			else
				MainuiWGCtrl.Instance:AddInitCallBack(nil,function()
						self.get_new_view:SetContent(info)
						TipWGCtrl.Instance:PlayAniTab(GuideModuleName.AppearanceGetNew, "add")
					end)
			end

			for k,v in pairs(self.getAppearanceCallBack) do
				v()
			end
		end
	end
end

--关闭恭喜获得界面
function AppearanceWGCtrl:CloseGetNewView()
	if self.get_new_view and self.get_new_view:IsOpen() then
		self.get_new_view:Close()
	end
end

------------------限时外观过期-------------------
function AppearanceWGCtrl:OnSCRemoveActivateAppe(protocol)
	local info = {}
	info.appe_type = protocol.appe_type
	info.appe_image_id = protocol.appe_image_id
	info.index_param = protocol.index_param

	--print_error("----限时外观过期----", info)
	table.insert(self.remove_appe_info_list, info)
	self:OpenRemoveAppeView()
end

function AppearanceWGCtrl:GetRemoveAppeInfoList()
	return self.remove_appe_info_list
end

function AppearanceWGCtrl:CanOpenRemoveView()
	if self.remove_view then
		local info = self.remove_view:GetInfo()
		if not IsEmptyTable(info) then
			return false
		end
	end

	if IsEmptyTable(self.remove_appe_info_list) then
		return false
	end

	return true
end


--打开物品过期界面
function AppearanceWGCtrl:OpenRemoveAppeView()
	if self.remove_view then
		local info = self.remove_view:GetInfo()
		if not IsEmptyTable(info) then
			return
		end
	end

	if IsEmptyTable(self.remove_appe_info_list) then
		return
	end

	local info
	if self.remove_view then
		if MainuiWGCtrl.Instance:IsLoadMainUiView() then
			info = table.remove(self.remove_appe_info_list, 1)
			self.remove_view:SetContent(info)
			TipWGCtrl.Instance:PlayAniTab(GuideModuleName.AppearanceRemoveView, "add")
		else
			if not self.remove_first then
				info = table.remove(self.remove_appe_info_list, 1)
				MainuiWGCtrl.Instance:AddInitCallBack(nil,function()
					self.remove_view:SetContent(info)
					TipWGCtrl.Instance:PlayAniTab(GuideModuleName.AppearanceRemoveView, "add")
				end)

				self.remove_first = true
			end
		end
	end
end