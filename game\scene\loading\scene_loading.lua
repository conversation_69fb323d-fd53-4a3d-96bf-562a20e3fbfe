require("game/scene/loading/scene_preload")
SceneLoading = SceneLoading or BaseClass(SafeBaseView)

local TypeUnityTexture = typeof(UnityEngine.Texture)

function SceneLoading:__init()
    self.view_style = ViewStyle.Full
    self:AddViewResource(0, "uis/view/miscpre_load_prefab", "SceneLoadingView")
    self.view_layer = UiLayer.SceneLoading
    self.view_name = GuideModuleName.SceneLoading
    self.view_cache_time = 0
    self.notice_str_list = {}
    self.img_bg_list = {}
    self.main_complete_callback = nil
    self.complete_callback = nil
    self.preload_complete = false
    self.cur_precent = 0
    self.bg = nil
    self.notice = nil
    self.progress = nil
    self.delay_close_timer = nil
    self.is_scene_loading = false
    self.is_wait_load = false

    self.start_loading_callback = nil
    self.scene_preload = nil
    self.show_loading = true
    self.active_close = false
    self.old_skip_loading = 0
    self.is_play_gonggu_scene = false
    self.has_put_log = false

    local scene_cfg = ConfigManager.Instance:GetAutoConfig("reminding_config_auto").scene[1]
    local scene_list = scene_cfg and scene_cfg.scene_list or ""
    self.scene_list = Split(scene_list, "|")
    for i=1, #self.scene_list do
        self.scene_list[i] = tonumber(self.scene_list[i])
    end
end

function SceneLoading:__delete()

end

function SceneLoading:ReleaseCallBack()
    self:StopTimer()
    self:StopScenePreload()
    self:StopTweener()

    -- 清理变量和对象
    self.progress = nil
end

function SceneLoading:SetStartLoadingCallback(start_loading_callback)
    self.start_loading_callback = start_loading_callback
end

function SceneLoading:__LoadIndex(index)
    self.bundle_name, self.asset_name, self.bg_img_id = self:GetRandomAsset()
   local loader = AllocResSyncLoader(self, "bg")

    loader:Load(
            self.bundle_name,
            self.asset_name,
            TypeUnityTexture,
            function(texture)
                SafeBaseView.__LoadIndex(self, index)
            end)
end

function SceneLoading:SetShowLoading(scene_id)
    self.show_loading = true
end

function SceneLoading:ShowIndexCallBack()
    local progress_value = 0
    self.progress = self.node_list["Progress"]
    self.progress:SetActive(not IS_AUDIT_VERSION)
    self.progress.slider.value = 0
    if self.show_loading then
        self:StartCheckOverTimeQuest()
        self.node_list["Loading"]:SetActive(true)
        self.node_list["Background"].raw_image:LoadSprite(self.bundle_name, self.asset_name)
        self.node_list["Notice"].tmp.text = self:GetRandomNoticeStr()

        self:CheckStart()
    else
        self.node_list["Loading"]:SetActive(false)
        self.node_list.Fade.image.color = Color.New(0, 0, 0, 0)
        self:CheckStart()
        TaskWGCtrl.Instance:IsFly(false)
        self:ShowWaterEffect()
    end

    if self.node_list["audit_panel"] then
        self.node_list["audit_panel"]:SetActive(IS_AUDIT_VERSION)
    end

    if IS_AUDIT_VERSION then
        self.node_list["Notice"]:SetActive(false)
        self.node_list["tip"]:SetActive(false)
        self.node_list["Progress"]:SetActive(false)
        if self.node_list["rota_icon"] then
            self.node_list["rota_icon"].transform:DORotate(Vector3(0, 0, -360), 1, DG.Tweening.RotateMode.FastBeyond360):SetLoops(-1, DG.Tweening.LoopType.Restart)
        end
    end

    -- local time = 0.4
    -- local total_time = 0
    -- local tween_sequence = DG.Tweening.DOTween.Sequence()
    -- for i = 5, 7 do
    --     tween_sequence:InsertCallback(total_time, function()
    --         self.node_list["progress_"..i].canvas_group.alpha = 1
    --     end)
    --     -- tween_sequence:Insert(total_time ,self.node_list["progress_"..i].image:DOColor(Color.New(102, 102, 102, 255),time))
    --     tween_sequence:Insert(total_time ,self.node_list["progress_"..i].canvas_group:DoAlpha(1,0,time))
    --     total_time = total_time + time
    --     -- tween_sequence:Insert(total_time , self.node_list["progress_"..i].image:DOColor(Color.New(255, 255, 255, 255),time))
    --     tween_sequence:Insert(total_time ,self.node_list["progress_"..i].canvas_group:DoAlpha(0,1,time))

    -- end
	-- tween_sequence:SetEase(DG.Tweening.Ease.Linear)
    -- tween_sequence:SetLoops(-1)
end

function SceneLoading:ShowWaterEffect()
    local wave = Scene.Instance:GetYYWaveEffect()
    if not IsNil(wave) then
        Scene.Instance:UpdateSceneQuality(SCENE_QUALITY_TYPE.WAVE, true)
        local lerp = 0
        local strength = 0.04
        local time_len = 0.03
        local times = 1 / strength
        GlobalTimerQuest:AddTimesTimer(function()
            -- wave.waveStrength = lerp
            lerp = lerp + strength

            if Scene ~= nil and Scene.Instance ~= nil and lerp >= 1 then
                Scene.Instance:UpdateSceneQuality(SCENE_QUALITY_TYPE.WAVE, false)
            end
        end, time_len, times)
    end

    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.XinShouFuBen, nil, true))
end

function SceneLoading:Start(scene_id, main_complete_callback, complete_callback)
    if self.scene_id == scene_id and self.is_scene_loading then
         GlobalEventSystem:Fire(SceneEventType.CLOSE_LOADING_VIEW, false, false)
        return
    end

    self:StopTimer()
    local last_scene_loading = self.is_scene_loading
    self.is_scene_loading = true
    self.scene_id = scene_id
    self.main_complete_callback = main_complete_callback
    self.complete_callback = complete_callback
    self.is_wait_load = true
    self.has_put_log = false

    self:Open()
    if self.show_loading or last_scene_loading then
        self:CheckStart()
    end
end

function SceneLoading:CheckStart()
    if self.check_bundle then
        return
    end

    if self:IsLoaded() and self.is_wait_load then
        self.is_wait_load = false
        self:DoStart()
    end
end

function SceneLoading:PlayLoadingAni()
    self:Open()
end

local old_scene_id = 0
function SceneLoading:DoStart()
    if nil ~= self.start_loading_callback then
        self.start_loading_callback(self.scene_id)
        self.start_loading_callback = nil
    end

    self:StopTimer()
    self:StopTweener()
    self:StopScenePreload()
    self.cur_precent = 0

    -- 把音效音量降为0
    -- AudioService.Instance:SetSFXVolume(0)
    -- TalkCache.StopCurIndexAudio()

    self.scene_preload = ScenePreload.New(self.show_loading)
    self.load_list, self.download_scene_id = ScenePreload.GetLoadList(self.scene_id)
    self.scene_preload:StartLoad(self.scene_id,
        self.load_list,
        self.download_scene_id,
        nil,
        BindTool.Bind(self.OnMainSceneLoadComplete, self, self.scene_id),
        BindTool.Bind(self.OnSceneLoadComplete, self, self.scene_id),
        old_scene_id)

    old_scene_id = self.scene_id

    self.node_list["Notice"].gameObject:SetActive(true and not IS_AUDIT_VERSION)
    self:UpdateMapNotice()
    self:UpdateMapStopTip()
    self.progress.slider.value = 0

    self.tweener = self.progress.slider:DOValue(0.99, 4)
    self.tweener:OnUpdate(function ()
        local percent = self.progress.slider.value
        if self.node_list["progress_1"] then
            self.node_list["progress_1"].tmp.text = math.floor(1)
            self.node_list["progress_2"].tmp.text = math.floor(percent*10%10)
            self.node_list["progress_3"].tmp.text = math.floor(percent*100%10)
        end
        if self.node_list.obj_1 then
            self.node_list.obj_1:SetActive(percent >= 1)
        end
        if self.node_list.obj_2 then
            self.node_list.obj_2:SetActive(percent >= 0.1)
        end
	end)
end

function SceneLoading:UpdateMapNotice()
    self.notice_timer = GlobalTimerQuest:AddRunQuest(function ( ... )
        self.node_list["Notice"].tmp.text = self:GetRandomNoticeStr()
    end, 3)
end

function SceneLoading:UpdateMapStopTip()
    local per = 0
    local max_per = 99
    local old_time = 0
    self.fictitious_timer = GlobalTimerQuest:AddRunQuest(function ( ... )
        per = math.min(per + 1, max_per)
        if per >= max_per then
            if Status.NowTime - old_time >= 3 then
                self.node_list["tip"]:SetActive(true and not IS_AUDIT_VERSION)
                self.node_list["Notice"].gameObject:SetActive(false)
                self.node_list["tip"].tmp.text = Language.Common.MapStop
            else
                self.node_list["tip"]:SetActive(false)
            end
        else
            self.node_list["tip"]:SetActive(false)
            old_time = Status.NowTime
        end
    end, 0.02)
end

function SceneLoading:IsSceneLoading()
    return self.is_scene_loading
end

function SceneLoading:OnMainSceneLoadComplete(scene_id)
    if scene_id ~= nil and self.scene_id ~= scene_id then
        return
    end

    self.is_scene_loading = false
    if not self.show_loading then
        self:DoCloseFunc()
    end

    if nil ~= self.main_complete_callback then
        self.main_complete_callback(self.scene_id)
        self.main_complete_callback = nil
    end
end

function SceneLoading:OnSceneLoadComplete(scene_id)
    if scene_id ~= nil and scene_id ~= self.scene_id then
        return
    end

    local scene_id = self.scene_id
    self.scene_id = 0

    self:StopTimer()
    local delay_close_times = 0
    local function close_view()
        if RoleWGCtrl.Instance:GetIsHasRoleData() then
            self:DoCloseFunc()
        elseif delay_close_times < 10 then
            delay_close_times = delay_close_times + 1
            self.delay_close_timer = GlobalTimerQuest:AddDelayTimer(function ()
                self.delay_close_tiemr = nil
                close_view()
            end, 0.2)
        end
    end

    self:StopTweener()

    if nil ~= self.complete_callback then
        Trycall(function ()
            self.complete_callback(scene_id)
        end)
        self.complete_callback = nil
    end

    self.tweener = self.progress.slider:DOValue(1, 0.05)
    self.tweener:OnUpdate(function ()
        local percent = self.progress.slider.value
        if self.node_list["progress_1"] then
            self.node_list["progress_1"].tmp.text = math.floor(1)
            self.node_list["progress_2"].tmp.text = math.floor(percent * 10 % 10)
            self.node_list["progress_3"].tmp.text = math.floor(percent * 100 % 10)
        end

        if self.node_list.obj_1 then
            self.node_list.obj_1:SetActive(percent >= 1)
        end

        if self.node_list.obj_2 then
            self.node_list.obj_2:SetActive(percent >= 0.1)
        end

	end):OnComplete(close_view)
end

function SceneLoading:DoCloseFunc()
    self:Close()
end

function SceneLoading:CloseCallBack()
    if self.node_list["tip"] then
        self.node_list["tip"]:SetActive(false)
    end
    
    self:CancelCheckOverTimeQuest()
    GlobalEventSystem:Fire(SceneEventType.CLOSE_LOADING_VIEW, true, self.show_loading)
end

function SceneLoading:StopTweener()
    if self.tweener then
        self.tweener:Kill()
        self.tweener = nil
    end
end

function SceneLoading:StopTimer()
    if nil ~= self.delay_close_timer then
        GlobalTimerQuest:CancelQuest(self.delay_close_timer)
        self.delay_close_timer = nil
    end

    if nil ~= self.notice_timer then
        GlobalTimerQuest:CancelQuest(self.notice_timer)
        self.notice_timer = nil
    end

    if nil ~= self.fictitious_timer then
        GlobalTimerQuest:CancelQuest(self.fictitious_timer)
        self.fictitious_timer = nil
    end

    if nil ~= self.show_water_effect_timer then
        GlobalTimerQuest:CancelQuest(self.show_water_effect_timer)
        self.show_water_effect_timer = nil
    end
end

function SceneLoading:StopScenePreload()
    if nil ~= self.scene_preload then
        self.scene_preload:DeleteMe()
        self.scene_preload = nil
    end
end

function SceneLoading:GetRandomNoticeStr()
    local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
    local level = mainrole_vo.level
    if #self.notice_str_list < 1 then
        local temp_cfg = nil
        local word = ConfigManager.Instance:GetAutoConfig("reminding_config_auto").word
        for k, v in pairs(word) do
            if v.level_min <= level and level <= v.level_max then
                temp_cfg = v
            end
        end
        if not temp_cfg then return end
        self.notice_str_list = Split(temp_cfg.tips, "||")
    end
    local index = math.random(1, #self.notice_str_list)
    local str = table.remove(self.notice_str_list, index)

    return str
end

function SceneLoading:GetRandomAsset()
    local img_id = 1
    local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
    local level = math.max(1, mainrole_vo.level)
    if #self.img_bg_list < 1 then
        local temp_cfg = nil
        local loading = ConfigManager.Instance:GetAutoConfig("reminding_config_auto").loading
        for k, v in pairs(loading) do
            if v.level_min <= level and level <= v.level_max then
                temp_cfg = v
                break
            end
        end

        if not temp_cfg then
            img_id = 1
            local bundle_name, asset_name = ResPath.GetRawImagesPNG("sceneloading_bg_" .. img_id)
            return bundle_name, asset_name, img_id
        end

        self.img_bg_list = Split(temp_cfg.img, "||")
    end
    local index = math.random(1, #self.img_bg_list)
    img_id = table.remove(self.img_bg_list, index)

    local bundle_name, asset_name = ResPath.GetRawImagesPNG("sceneloading_bg_" .. img_id)
    return bundle_name, asset_name, tonumber(img_id)
end

function SceneLoading:ResetPostEffects()
    --F2 Camera的PostEffects已去掉
    -- post_effects.BlurSpread = 0.0
    -- post_effects.WaveStrength = 0.0
    -- post_effects.EnableBlur = false

    -- GlobalEventSystem:Fire(SceneEventType.SCENE_WATER_EFFECT_END)
end

function SceneLoading:ShowBubble(str)
    if self:IsLoaded() then
        if self.node_list and self.node_list["Bubble"] and self.node_list["Text"] then
            self.node_list["Bubble"]:SetActive(true)
            -- self.node_list["Text"].emoji_tmp.text = str
            self.node_list["Bubble"].rect.sizeDelta = self.node_list["Text"].rect.sizeDelta
        end
    end
end

function SceneLoading:CloseBubble()
    if self:IsLoaded() then
        if self.node_list and self.node_list["Bubble"] then
            self.node_list["Bubble"]:SetActive(false)
        end
    end
end

-- 检查加载场景是否超时，如果停留在100%超过10秒，则直接关闭进度条
local last_precent = 0
local last_time_stamp = 0
function SceneLoading:CheckLoadingSceneOverTime()
    -- 界面加载完毕并且不是在预下载
    if self:IsLoaded() and not self.check_bundle then
        local cur_precent = self.node_list["Progress"].slider.value
        if last_precent ~= cur_precent then
            last_precent = cur_precent
            last_time_stamp = Status.NowTime
            self:CloseBubble()
        else
            if last_time_stamp > 0 then
                -- 停留在100%超过10秒
                if last_precent >= 1 and Status.NowTime - last_time_stamp > 15 then
                    self:Close()
                    self:CancelCheckOverTimeQuest()
                    -- ReportManager:Step(Report.STEP_CHANGE_SCENE_OVERTIME)
                    -- 没有主角数据
                    if not RoleWGCtrl.Instance:GetIsHasRoleData() then
                        local restart_func = function ()
                            AgentAdapter.Instance:Logout()
                        end
                        TipWGCtrl.Instance:OpenConfirmAlertTips(Language.MapLoading.NoMainRoleData, restart_func)
                        -- ReportManager:Step(Report.STEP_NO_MAIN_ROLE_DATA)
                    end
                elseif last_precent >= 0 and Status.NowTime - last_time_stamp > 3 then
                    self:ShowBubble(Language.MapLoading.Loading)
                    if Status.NowTime - last_time_stamp > 7 and not self.has_put_log then
                        self.has_put_log = true
                        -- self:PrintLog()
                    end
                end
            end
        end
    end
end

function SceneLoading:StartCheckOverTimeQuest()
    self:CancelCheckOverTimeQuest()
    last_precent = 0
    last_time_stamp = 0
    self.check_overtime_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.CheckLoadingSceneOverTime, self), 1)
end

function SceneLoading:CancelCheckOverTimeQuest()
    if self.check_overtime_quest then
        GlobalTimerQuest:CancelQuest(self.check_overtime_quest)
        self.check_overtime_quest = nil
    end
end

function SceneLoading:PrintLog()
    if self.scene_preload then
        local logs = self.scene_preload:GetLoadLogs()
        local msg = ""
        for k,v in ipairs(logs) do
            msg = msg .. v .. "\t"
        end
        print_error("场景加载日志: ", msg)
    end
end

--------------------------------------------预下载相关-------------------------------------------
function SceneLoading:StartCheckBundles(level, scene_id)
    self.check_bundle = true
    PreDownload.Instance:StartStrongUpdate(level, scene_id, BindTool.Bind(self.UpdateBundle, self))
end

function SceneLoading:UpdateBundle(is_complete, percent, str)
    if nil ~= str and self:IsLoaded() then
        self.node_list["Notice"].gameObject:SetActive(false)
        self.node_list["tip"]:SetActive(true and not IS_AUDIT_VERSION)
        self.node_list["tip"].tmp.text = string.format("%s（%d%%）", str, percent * 100)

        if self.node_list["progress_1"] then
            self.node_list["progress_1"].tmp.text = math.floor(1)
            self.node_list["progress_2"].tmp.text = math.floor(percent*10%10)
            self.node_list["progress_3"].tmp.text = math.floor(percent*100%10)
        end
        if self.node_list.obj_1 then
            self.node_list.obj_1:SetActive(percent >= 1)
        end
        if self.node_list.obj_2 then
            self.node_list.obj_2:SetActive(percent >= 0.1)
        end
        
        if nil ~= self.progress then
            self.progress.slider.value = percent
        end

        if nil ~= self.node_list["audit_progress"] then
            -- self.node_list["audit_progress"].tmp.text = math.floor(percent * 100) .. "%"
        end
    end

    if is_complete then
        if self:IsLoaded() then
            self.node_list["tip"]:SetActive(false)
        end
        self.check_bundle = false
        self:CheckStart()
    end
end
