-- 灵兽星阵重置属性
ControlBeastsRefiningReset = ControlBeastsRefiningReset or BaseClass(SafeBaseView)

function ControlBeastsRefiningReset:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(708, 488)})
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_root_refining_reset")
    self:SetMaskBg(true)
end

function ControlBeastsRefiningReset:SetSelectData(show_data)
    self.show_data = show_data
end

function ControlBeastsRefiningReset:ReleaseCallBack()
    self.show_data = nil

    if self.circle_attrlist and #self.circle_attrlist > 0 then
		for _, circle_cell in ipairs(self.circle_attrlist) do
			circle_cell:DeleteMe()
			circle_cell = nil
		end

		self.circle_attrlist = nil
	end

    if self.star_circle_attr_list and #self.star_circle_attr_list > 0 then
		for _, star_circle__cell in ipairs(self.star_circle_attr_list) do
			star_circle__cell:DeleteMe()
			star_circle__cell = nil
		end

		self.star_circle_attr_list = nil
	end

    if self.circle_spend_item then
		self.circle_spend_item:DeleteMe()
		self.circle_spend_item = nil
	end
end

function ControlBeastsRefiningReset:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.AttrWheel8 or ""
    -- 资质属性
    if self.circle_attrlist == nil then
        self.circle_attrlist = {}
        for i = 1, 20 do
            local attr_obj = self.node_list.layout_refining_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststsCircleAttrItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.circle_attrlist[i] = cell
            end
        end
    end

    -- 星阵属性
    if self.star_circle_attr_list == nil then
        self.star_circle_attr_list = {}
        for i = 1, BEAST_DEFINE.BEAST_CHANGE_STAR_CIRCLE_ATTR_COUNT_MAX do
            local attr_obj = self.node_list.refining_attr_root:FindObj(string.format("refining_attr_item_%d", i))
            if attr_obj then
                local cell = BeststsResetRefiningAttrItemRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetOperateCallBack(BindTool.Bind1(self.ChangeCircleResetClickBack, self))
                self.star_circle_attr_list[i] = cell
            end
        end
    end

    if not self.circle_spend_item then
		self.circle_spend_item = ItemCell.New(self.node_list.spend_item)
	end

    XUI.AddClickEventListener(self.node_list.btn_OK, BindTool.Bind2(self.ResetCircleAttr, self))
end

-- 切换当前需要重置的属性
function ControlBeastsRefiningReset:ChangeCircleResetClickBack(circle_cell)
    if not self.show_data then
        return
    end

    if circle_cell.index == self.show_data.circle_index then
        return
    end

    self.show_data.circle_index = circle_cell.index
    self:ChangeCircleResetStatus()
    self:FlushStarCircleAttrList()
end

function ControlBeastsRefiningReset:OnFlush()
    if not self.show_data then
        return
    end

    local attr_data = ControlBeastsWGData.Instance:GetStarCircleAttrDataById(self.show_data.refining_index, self.show_data.circle_index)
    local beast_circle_data = ControlBeastsWGData.Instance:GetStarCircleDataById(self.show_data.refining_index)

    if attr_data and beast_circle_data then
        local refining_data = ControlBeastsWGData.Instance:GetStarCircleDataById(self.show_data.refining_index)

        if refining_data and refining_data.attr_list then
            local attr_list = refining_data.attr_list
            if attr_list and self.star_circle_attr_list then
                for index, circle_attr_cell in ipairs(self.star_circle_attr_list) do
                    -- 设置轮
                    circle_attr_cell:SetIntervalTimes(refining_data.interval_times)
                    circle_attr_cell:SetData(attr_list[index])
                end
            end

            -- 设置当前选中
            self:ChangeCircleResetStatus()
            local refresh_list = ControlBeastsWGData.Instance:GetStarCirclerePreviewAttrCfgList()
            if refresh_list then
                for index, attr_cell in ipairs(self.circle_attrlist) do
                    if attr_cell and refresh_list[index] then
                        attr_cell:SetVisible(true)
                        attr_cell:SetIntervalTimes(refining_data.interval_times)
                        attr_cell:SetData(refresh_list[index])
                    else
                        attr_cell:SetVisible(false)
                    end
                end
            end

            -- 刷新总上限
            self:FlushStarCircleAttrList()
        end
    end

    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    if base_cfg and base_cfg.array_random_item then
        local array_random_item = base_cfg.array_random_item
        self.circle_spend_item:SetData({item_id = array_random_item.item_id})
        local item_num = ItemWGData.Instance:GetItemNumInBagById(array_random_item.item_id)
        local color = item_num >= array_random_item.num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.circle_spend_item:SetRightBottomTextVisible(true)
        self.circle_spend_item:SetRightBottomColorText(item_num .. '/' .. array_random_item.num, color)
    end

    local king_lv = ControlBeastsWGData.Instance:GetBeastBaseInfoKingLevel()
    local king_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(king_lv)
    if king_data and king_data.beast_king_data then
        local color_lv = king_data.beast_king_data.refining_attr_color
        local str = Language.Common.ColorName[color_lv]
        local color = ITEM_COLOR[color_lv]
        self.node_list.best_color_tips.text.text = string.format(Language.ContralBeasts.AttrWhee22, ToColorStr(str, color))
    end
end

-- 刷新上限
function ControlBeastsRefiningReset:FlushStarCircleAttrList()
    for index, circle_attr_cell in ipairs(self.circle_attrlist) do
        circle_attr_cell:SetCurrChooseData(self.circle_data)
    end
end

-- 刷新当前的选中切换
function ControlBeastsRefiningReset:ChangeCircleResetStatus()
    if not self.show_data then
        return
    end

    if self.star_circle_attr_list then
        for index, circle_attr_cell in ipairs(self.star_circle_attr_list) do
            if index == self.show_data.circle_index then
                self.circle_data = circle_attr_cell.data
            end
            circle_attr_cell:ChangeSelect(index == self.show_data.circle_index)
        end
    end
end

-----------------------------------------------------------------
function ControlBeastsRefiningReset:ResetCircleAttr()
    if not self.show_data then
        return
    end

    local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
    if base_cfg and base_cfg.array_random_item then
        local array_random_item = base_cfg.array_random_item
        local item_num = ItemWGData.Instance:GetItemNumInBagById(array_random_item.item_id)

        if item_num >= array_random_item.num then
            ControlBeastsWGCtrl.Instance:SendOperateTypeStarCircleAttrFlush(self.show_data.refining_index, self.show_data.circle_index)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
        end
    end
end

--------------------------------------------------------------------------------------
BeststsCircleAttrItemRender = BeststsCircleAttrItemRender or BaseClass(BaseRender)
function BeststsCircleAttrItemRender:ReleaseCallBack()
	self.circle_data = nil
    self.interval_times = nil
end

-- 设置选择属性
function BeststsCircleAttrItemRender:SetCurrChooseData(circle_data)
    self.circle_data = circle_data
    self:Flush()
end

function BeststsCircleAttrItemRender:SetIntervalTimes(interval_times)
    self.interval_times = interval_times
end

function BeststsCircleAttrItemRender:OnFlush()
	if (not self.data) or (not self.circle_data) then
		return
	end

    self.node_list.pz_image:CustomSetActive(self.data.is_unlock)
    local king_lv = ControlBeastsWGData.Instance:GetBeastBaseInfoKingLevel()
    local king_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(king_lv)
    local color = self.data.is_unlock and COLOR3B.GREEN
    local attr_str = EquipmentWGData.Instance:GetAttrName(self.data.attr_name, true, false)

    if self.data.is_unlock then
        self.node_list.attr_name.text.text = ToColorStr(attr_str, COLOR3B.DEFAULT)

        if king_data and king_data.beast_king_data then
            local color_lv = king_data.beast_king_data.refining_attr_color
            local pz_str = string.format("a3_hs_refining_0%d", color_lv)
            self.node_list.pz_image.image:LoadSprite(ResPath.GetControlBeastsImg(pz_str))

            local best_data = ControlBeastsWGData.Instance:GetCircleAttrDataByIdColor(self.data.attr_name, color_lv)
            if best_data then
                local interval_times = self.interval_times or 0
                local per_desc = is_per and "%" or "" 
                local real_attr_value = best_data.array_attr_base_value * (self.circle_data.star_num + (interval_times * 10))
                self.node_list.attr_value.text.text = ToColorStr(string.format("+%s", real_attr_value), color) 
            end
        end
    else
        self.node_list.attr_name.text.text = attr_str
        self.node_list.attr_value.text.text = string.format(Language.ContralBeasts.AttrWhee20, king_lv, self.data.beast_king_level)
    end
end


-- 属性列表
BeststsResetRefiningAttrItemRender = BeststsResetRefiningAttrItemRender or BaseClass(BaseRender)
function BeststsCircleAttrItemRender:ReleaseCallBack()
	self.circle_data = nil
    self.opeaate_callback = nil
end

function BeststsResetRefiningAttrItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.slect_nor, BindTool.Bind2(self.ChangeSelfAttr, self))
end

function BeststsResetRefiningAttrItemRender:SetIntervalTimes(interval_times)
    self.interval_times = interval_times
end

-- 设置属性
function BeststsResetRefiningAttrItemRender:SetOperateCallBack(callback)
	self.opeaate_callback = callback
end

-- 重置属性
function BeststsResetRefiningAttrItemRender:ChangeSelfAttr()
    if self.opeaate_callback then
        self.opeaate_callback(self)
    end
end

function BeststsResetRefiningAttrItemRender:OnFlush()
	if not self.data then
		return
	end

    local star_circle_attr_cfg = ControlBeastsWGData.Instance:GetStarCircleAttrCfgById(self.data.id)
    if star_circle_attr_cfg then
        local fill_str = string.format("a3_hs_jd_%d", star_circle_attr_cfg.array_attr_color)
        self.node_list.refining_attr_fill.image:LoadSprite(ResPath.GetControlBeastsImg(fill_str))
        self.node_list.attr_name.text.text = EquipmentWGData.Instance:GetAttrName(star_circle_attr_cfg.array_attr_id, false, false)

        local is_per = EquipmentWGData.Instance:GetAttrIsPer(star_circle_attr_cfg.array_attr_id)
        local interval_times = self.interval_times or 0
        local per_desc = is_per and "%" or "" 
        local real_attr_value = star_circle_attr_cfg.array_attr_base_value * (self.data.star_num + (interval_times * 10))
        local value_str = is_per and real_attr_value / 100 or real_attr_value
        self.node_list.attr_value.text.text = string.format("%s%s%s", "+", value_str, per_desc)

        local king_lv = ControlBeastsWGData.Instance:GetBeastBaseInfoKingLevel()
        local king_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(king_lv)
        if king_data and king_data.beast_king_data then
            local color_lv = king_data.beast_king_data.refining_attr_color
            local pz_str = string.format("a3_hs_refining_0%d", color_lv)

            local best_data = ControlBeastsWGData.Instance:GetCircleAttrDataByIdColor(star_circle_attr_cfg.array_attr_id, color_lv)
            if best_data then
                local max_base_value = best_data.array_attr_base_value * (self.data.star_num + (interval_times * 10))
                if max_base_value == 0 then
                    self.node_list.refining_attr_slider.slider.value = 0
                else
                    self.node_list.refining_attr_slider.slider.value = real_attr_value / max_base_value
                end
            end
        end
    end
end

function BeststsResetRefiningAttrItemRender:ChangeSelect(is_select)
    self.node_list.slect_check:CustomSetActive(is_select)
end