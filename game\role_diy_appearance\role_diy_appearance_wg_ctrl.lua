require("game/role_diy_appearance/role_diy_appearance_view")
require("game/role_diy_appearance/role_diy_appearance_wg_data")
require("game/role_diy_appearance/role_diy_appearance_item")

RoleDiyAppearanceWGCtrl = RoleDiyAppearanceWGCtrl or BaseClass(BaseWGCtrl)

function RoleDiyAppearanceWGCtrl:__init()
	if RoleDiyAppearanceWGCtrl.Instance then
		ErrorLog("[RoleDiyAppearanceWGCtrl] attempt to create singleton twice!")
		return
	end

	RoleDiyAppearanceWGCtrl.Instance = self

	self.data = RoleDiyAppearanceWGData.New()
    self.view = RoleDiyAppearanceView.New(GuideModuleName.RoleDiyAppearanceView)
end

function RoleDiyAppearanceWGCtrl:__delete()
	RoleDiyAppearanceWGCtrl.Instance = nil

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end
end

function RoleDiyAppearanceWGCtrl:SetViewDataAndOpen(data)
    self.view:SetShowDataAndOpen(data)
end

function RoleDiyAppearanceWGCtrl:AddDiyAppearaceData(diy_type, diy_data)
    self.data:AddDiyAppearaceData(diy_type, diy_data)
    if self.view:IsOpen() then
        if diy_type == RoleDiyAppearanceWGData.ADD_DIY_TYPE.RESET then
            self.view:Flush(nil, "all", {need_jump = true})
        else
            self.view:Flush()
        end
	end
end

function RoleDiyAppearanceWGCtrl:ChangeCurDiySelectIndex(change_type)
    self.data:ChangeCurDiySelectIndex(change_type)
    if self.view:IsOpen() then
		self.view:Flush(nil, "all", {need_jump = true})
	end
end

function RoleDiyAppearanceWGCtrl:GetShowView()
    return self.view
end