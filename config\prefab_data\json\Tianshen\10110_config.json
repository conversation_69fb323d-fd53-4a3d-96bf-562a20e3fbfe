{"actorController": {"projectiles": [], "hurts": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10110_skill1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10110/10110_skill1_prefab", "AssetName": "10110_skill1", "AssetGUID": "61774311ce90e2e4fb74f753c0ec2add", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10110_skill2", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10110/10110_skill2_prefab", "AssetName": "10110_skill2", "AssetGUID": "90b698f20630f9f4986027167487b3ae", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack3/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10110_skill3", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10110/10110_skill3_prefab", "AssetName": "10110_skill3", "AssetGUID": "f2550b75b558ed645b25fdf0add3d2e1", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack3", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10110_attack1", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10110/10110_attack1_prefab", "AssetName": "10110_attack1", "AssetGUID": "f87a2945e2a532347992557b68e4584b", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10110_rest", "effectAsset": {"BundleName": "effects/prefab/model/tianshen/10110/10110_rest_prefab", "AssetName": "10110_rest", "AssetGUID": "8b875ac4089abf5439159bff004ad416", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": true, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10110", "AssetName": "MingJiangcombo_1_1", "AssetGUID": "e2594ff14bf4bdd47b84b21440fac49a", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_1", "soundBtnName": "combo1", "soundIsMainRole": false}, {"soundEventName": "combo1_2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10110", "AssetName": "MingJiangcombo_1_2", "AssetGUID": "14b32490f40698b46bfab445d8408c49", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_2", "soundBtnName": "combo2", "soundIsMainRole": false}, {"soundEventName": "combo1_3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10110", "AssetName": "MingJiangcombo_1_3", "AssetGUID": "8aca84cd5c8c98a46951577b989f6de0", "IsEmpty": false}, "soundAudioGoName": "MingJiangcombo_1_3", "soundBtnName": "combo3", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10110", "AssetName": "MingJiangattack3", "AssetGUID": "f70bdc9a1d249b948aad600736c206f7", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack3", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10110", "AssetName": "MingJiangattack2", "AssetGUID": "96acfa36c7265624e8403e19e5c959e8", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack2", "soundBtnName": "attack2", "soundIsMainRole": false}, {"soundEventName": "attack3/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/tianshen_10110", "AssetName": "MingJiangattack1", "AssetGUID": "3a81682f520692645a9b9d2132efdf78", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack3", "soundIsMainRole": false}], "cameraShakes": [], "radialBlurs": []}}