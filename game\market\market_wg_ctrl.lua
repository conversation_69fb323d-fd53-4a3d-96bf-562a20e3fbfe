require("game/market/market_wg_data")
require("game/market/market_view")
require("game/market/market_sell_tips")
require("game/market/market_remove_sell_tips")
require("game/market/market_big_type_item")
require("game/market/market_sub_type_item")
require("game/market/market_base_cell")
require("game/market/market_buy_view")
require("game/market/market_sell_view")
require("game/market/market_record_view")
require("game/market/market_my_want_view")
require("game/market/market_want_list_view")

--市场拍卖
require("game/market/auction_comfirm_view")
require("game/market/market_auction_common_render")
require("game/market/market_country_auction")
require("game/market/market_guild_auction")
require("game/market/market_my_auction")
require("game/market/market_auction_record")
require("game/market/market_cross_server")


-- 市场
MarketWGCtrl = MarketWGCtrl or BaseClass(BaseWGCtrl)

function MarketWGCtrl:__init()
	if MarketWGCtrl.Instance ~= nil then
		ErrorLog("[MarketWGCtrl] attempt to create singleton twice!")
		return
	end

	MarketWGCtrl.Instance = self

	self.mianui_load_complete = false
	self.data = MarketWGData.New()
	self.view = MarketView.New(GuideModuleName.Market)
	self.sell_tips = MarketSellTips.New() 						-- 上架面板
	self.remove_sell_tips = MarketRemoveSellTips.New() 			-- 下架面板
	self.auction_comfirm_view = AuctionComfirmView.New() 			-- 市场竞价确认面板

	self:RegisterAllProtocols()
end

function MarketWGCtrl:__delete()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
	self:CancelItemChangeCallbackFlush()

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.sell_tips:DeleteMe()
	self.sell_tips = nil

	self.remove_sell_tips:DeleteMe()
	self.remove_sell_tips = nil

	self.auction_comfirm_view:DeleteMe()
	self.auction_comfirm_view = nil

	if self.auction_goods_add_alert then
		self.auction_goods_add_alert:DeleteMe()
		self.auction_goods_add_alert = nil
	end

	if self.auction_tips_btn_alert then
		self.auction_tips_btn_alert:DeleteMe()
		self.auction_tips_btn_alert = nil
	end

	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end

	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)

	MarketWGCtrl.Instance = nil
end

function MarketWGCtrl:RegisterAllProtocols()
	-- 购买
	self:RegisterProtocol(SCRoleAuctionItem, "OnRoleAuctionItem") 					-- 多个市场物品更变
	self:RegisterProtocol(SCRoleAuctionItemTypeNum, "OnSCRoleAuctionItemTypeNum") 	-- 市场商品各子类数目
	self:RegisterProtocol(SCUserOpAuctionItem, "OnSCUserOpAuctionItem") 			-- 单个市场物品更变
	self:RegisterProtocol(SCAuctionLog, "OnSCAuctionLog") 							-- 日志下发
	self:RegisterProtocol(CSRoleAuctionItem)										-- 请求下发市场物品数据
	self:RegisterProtocol(CSRoleOpAuctionItem) 										-- 请求操作市场物品
	self:RegisterProtocol(CSAuctionLog) 											-- 请求获取买卖日志

	-- 求购
	self:RegisterProtocol(SCRoleAuctionWantedInfo, "OnSCRoleAuctionWantedInfo") 				-- 求购列表下发
	self:RegisterProtocol(SCRoleAuctionWantedInfoUpdate, "OnSCRoleAuctionWantedInfoUpdate") 	-- 单条求购信息更新
	self:RegisterProtocol(CSRoleOpAuctionWanted) 												-- 求购操作

	self:RegisterProtocol(CSNewAuctionOperate) 											--拍卖--拍卖/物品信息请求--9010
	self:RegisterProtocol(SCNewAuctionItemInfo, "OnSCNewAuctionItemInfo") 				--拍卖--所有拍卖商品信息
	self:RegisterProtocol(SCNewAuctionItemInfoUpdate, "OnSCNewAuctionItemInfoUpdate") 	--拍卖--单条有变化的商品信息
	self:RegisterProtocol(SCNewAuctionItemInfoRemove, "OnSCNewAuctionItemInfoRemove") 	--拍卖--单条商品信息移除
	self:RegisterProtocol(SCNewAuctionBidReturn, "OnSCNewAuctionBidReturn") 			--拍卖--竞价信息返还

	self:RegisterProtocol(SCNewAuctionLogInfo, "OnSCNewAuctionLogInfo") 				--竞拍记录信息下发
	self:RegisterProtocol(SCNewAuctionLogAdd, "OnSCNewAuctionLogAdd") 					--单条 竞拍记录信息下发
	self:RegisterProtocol(SCNewAuctionBidRecordInfo, "OnSCNewAuctionBidRecordInfo") 	--当前我的竞拍信息下发
	self:RegisterProtocol(SCNewAuctionBidRecordAdd, "OnSCNewAuctionBidRecordAdd") 		--单条 当前我的竞拍信息下发

	self:RegisterProtocol(SCNewAuctionTypeAdded, "OnSCNewAuctionTypeAdded") 			--版图,仙盟结算,新商品上架
	self:RegisterProtocol(SCRoleAuctionInfo, "OnSCRoleAuctionInfo")                     -- 拍卖元宝信息
	

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
	self.role_data_change_callback =  BindTool.Bind1(self.OnRoleDataChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"guild_id"})

	self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.OnLoadingComplete, self))
end

function MarketWGCtrl:OnLoadingComplete()
	GlobalTimerQuest:AddDelayTimer(function()
		self:SendCSNewAuctionOperate(false, NEW_AUCTION_OPERATE_TYPE.REQ_ITEM_INFO)
		self:SendCSNewAuctionOperate(true, NEW_AUCTION_OPERATE_TYPE.REQ_ITEM_INFO)
		self:SendCSNewAuctionOperate(false, NEW_AUCTION_OPERATE_TYPE.REQ_LOG_INFO)
		self:SendCSNewAuctionOperate(true, NEW_AUCTION_OPERATE_TYPE.REQ_LOG_INFO)
		self:SendCSNewAuctionOperate(false, NEW_AUCTION_OPERATE_TYPE.REQ_BID_RECORD_INFO)
		self:SendCSNewAuctionOperate(true, NEW_AUCTION_OPERATE_TYPE.REQ_BID_RECORD_INFO)
		self.mianui_load_complete = true
	end, 2)
end

function MarketWGCtrl:OnRoleDataChange(attr_name, value, old_value)
	if attr_name == "guild_id" and self.mianui_load_complete then
		--玩家仙盟信息变更(加入/退出),请求一下拍卖信息刷新
		self:SendCSNewAuctionOperate(false, NEW_AUCTION_OPERATE_TYPE.REQ_ITEM_INFO)
	end
end

-- 子类型所有商品(如果数据量大，请求一次，服务端会分批下发)，隆哥说现在已经没有分批下发的骚操作了，商品超出1000个的都不会下发.
function MarketWGCtrl:OnRoleAuctionItem(protocol)
	if protocol.op_type == MARKET_GOODS_TYPE.WORLD then
		MarketWGData.Instance:AddMarketGoodsListInfo(protocol)
		ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_buy, "Part", {flush_buy_goods = true})
		self.sell_tips:Flush()
		self.remove_sell_tips:Flush()
	elseif protocol.op_type == MARKET_GOODS_TYPE.SELF then
		MarketWGData.Instance:SetMyMarketGoodsListInfo(protocol)
		ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_sell)
	end
end

-- 各子类商品数目初始化协议
function MarketWGCtrl:OnSCRoleAuctionItemTypeNum(protocol)
	if protocol.op_type == MARKET_GOODS_TYPE.WORLD then
		MarketWGData.Instance:SetGoodsAmountList(protocol)
		ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_buy, "Part", {flush_sub_type_grid = true})
	end
end

-- 单个商品更变
function MarketWGCtrl:OnSCUserOpAuctionItem(protocol)
	MarketWGData.Instance:UpdateMarketGoodsInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_sell)
	ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_buy, "Part", {flush_buy_goods = true, flush_sub_type_grid = true})
end

--请求的商品列表（如果对应子类没有商品，服务端也会下发空表）
function MarketWGCtrl:SendCSRoleAuctionItem(big_type, sub_type, auction_type)
	auction_type = auction_type or 0
	-- 判断数据是否已经下发
	if auction_type == MARKET_GOODS_TYPE.WORLD then
		if MarketWGData.Instance:GoodsListInfoIsInit(big_type, sub_type) then
			return
		end

		local sub_type_cfg = MarketWGData.Instance:GetSubTypeCfgBySubType(sub_type)
		if not sub_type_cfg or sub_type_cfg.big_id ~= big_type then
			return
		end
	end

	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleAuctionItem)
	protocol.auction_type = auction_type
	protocol.auction_index = 0
	protocol.big_id = big_type
	protocol.small_id = sub_type
	protocol:EncodeAndSend()
end

-- 请求大类型下所有子类型的商品列表
function MarketWGCtrl:SendCSRoleBigTypeAuctionItem(big_type)
	local sub_type_cfg = MarketWGData.Instance:GetSubTypeCfg(big_type)
	for i,v in ipairs(sub_type_cfg) do
		self:SendCSRoleAuctionItem(big_type, v.small_id)
	end
end

-- 请求获取所有自己上架的商品信息
function MarketWGCtrl:SendReqAllSelfGoodsInfo()
	if self.req_all_self_goods_is_send then
		return
	end
	self:SendCSRoleAuctionItem(0, 0, MARKET_GOODS_TYPE.SELF)
	self.req_all_self_goods_is_send = true
end

-- 请求获取所有商品的数目
function MarketWGCtrl:SendReqAllGoodsAmountInfo()
	if self.req_all_goods_amount_is_send then
		return
	end
	self:SendCSRoleAuctionItem(0, 0, MARKET_GOODS_TYPE.WORLD_NUM)
	self.req_all_goods_amount_is_send = true
end

-- 请求操作市场物品
function MarketWGCtrl:SendCSRoleOpAuctionItem(op_type, param1, param2, param3, param4, param5, param6)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleOpAuctionItem)
	protocol.op_type = op_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol.param5 = param5 or 0
	protocol.param6 = param6 or 0
	protocol:EncodeAndSend()
end

--请求购买市场物品
function MarketWGCtrl:SendBuyMarketGoods(auction_index, password)
	-- print_log("#SendBuyMarketGoods#", auction_index, password)
	password = password or 0
	self:SendCSRoleOpAuctionItem(AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_DIRECTYBUG, auction_index, password)
end

--请求撤回自己上架的商品
function MarketWGCtrl:SendRemoveGoods(auction_index)
	-- print_log("#SendRemoveGoods#", auction_index)
	self:SendCSRoleOpAuctionItem(AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_REMOVE, auction_index)
end

--请求上架的自己的物品到市场，(参数：背包类型，物品在背包的下标， 物品数目，总价，商品购买密码)
function MarketWGCtrl:SendAddGoods(knapsack_type, knapsack_index, item_num, total_price, password, auction_item_type)
	print_log("#SendAddGoods#", knapsack_type, knapsack_index, item_num, total_price, password, auction_item_type)
	self:SendCSRoleOpAuctionItem(AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_ADD, knapsack_type, knapsack_index, item_num, total_price, password, auction_item_type)
end

-- 请求吆喝
function MarketWGCtrl:SendYell(auction_index)
	self:SendCSRoleOpAuctionItem(AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_YAOHE, auction_index)
end

function MarketWGCtrl:SendReqAuctionTypeInfo()
	self:SendCSRoleOpAuctionItem(AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_INFO)
end

function MarketWGCtrl:SendReqJumpFlag(flag)
	self:SendCSRoleOpAuctionItem(AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_JUMP_FLAG, flag)
end

-- 请求获取购买日志
function MarketWGCtrl:SendCSAuctionLog()
	local protocol = ProtocolPool.Instance:GetProtocol(CSAuctionLog)
	protocol.auction_type = 1
	protocol:EncodeAndSend()
end

function MarketWGCtrl:OnSCAuctionLog(protocol)
	MarketWGData.Instance:SetRecordInfoList(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_record)
end

-- 求购信息列表
function MarketWGCtrl:OnSCRoleAuctionWantedInfo(protocol)
	-- print_log("#OnSCRoleAuctionWantedInfo#", protocol)
	MarketWGData.Instance:AddMarketWantListInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_want_list)
end

-- 单条求购信息
function MarketWGCtrl:OnSCRoleAuctionWantedInfoUpdate(protocol)
	-- print_log("#OnSCRoleAuctionWantedInfoUpdate#", protocol)
	MarketWGData.Instance:SetMarketWantInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_want_list)
end

-- 求购列表操作
function MarketWGCtrl:SendCSRoleOpAuctionWanted(op_type, param1, param2, param3, param4, param5)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleOpAuctionWanted)
	protocol.op_type = op_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol.param5 = param5 or 0
	protocol:EncodeAndSend()
end

-- 发送求购申请
function MarketWGCtrl:SendAddWant(item_id, item_star, item_num, total_price)
	-- print_log("#SendAddWant#", item_id, item_star, item_num, total_price)
	item_star = item_star or 0
	self:SendCSRoleOpAuctionWanted(AUCTIONWANTED_OP_TYPE.AUCTIONWANTED_OP_TYPE_ADD, item_id, item_star, item_num, total_price)
end

-- 发送移除求购
function MarketWGCtrl:SendRemoveWant(wanted_index)
	-- print_log("#SendAddWant#", wanted_index)
	self:SendCSRoleOpAuctionWanted(AUCTIONWANTED_OP_TYPE.AUCTIONWANTED_OP_TYPE_REMOVE, wanted_index)
end

-- 发送应答他人的求购
function MarketWGCtrl:SendResponseWant(wanted_index, knapsack_type, knapsack_index)
	-- print_log("#SendAddWant#", wanted_index, knapsack_type, knapsack_index)
	self:SendCSRoleOpAuctionWanted(AUCTIONWANTED_OP_TYPE.AUCTIONWANTED_OP_TYPE_RESPONSE, wanted_index, knapsack_type, knapsack_index)
end

-- 请求下发所有求购信息
function MarketWGCtrl:SendReqWantInfo()
	if not MarketWGData.Instance:GetMarketWantListIsInit() then
		self:SendCSRoleOpAuctionWanted(AUCTIONWANTED_OP_TYPE.AUCTIONWANTED_OP_TYPE_REQ_INFO)
	end
end

-- 打开物品上架面板
function MarketWGCtrl:OpenMarketTipItemView(bag_item_data, need_open_market)
	local limit_vip_level = GLOBAL_CONFIG.param_list.market_can_sell_vip_level or 0
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	if vip_level < limit_vip_level then
		local str = string.format(Language.Market.SellVipLimit, limit_vip_level)
		TipWGCtrl.Instance:ShowSystemMsg(str)
		return
	end

	--累充限制
	local php_recharge_limit = GLOBAL_CONFIG.param_list.vip_market_recharge_limit_open or 0
	local history_recharge = RechargeWGData.Instance:GetRealChongZhiRmb()
	if history_recharge < php_recharge_limit then
		local str = string.format(Language.Market.HistoryRechargeLimit1, php_recharge_limit)
		TipWGCtrl.Instance:ShowSystemMsg(str)
		return
	end

	self.sell_tips:SetData(bag_item_data, need_open_market)
	self.sell_tips:Open()
end

-- 关闭物品上架面板
function MarketWGCtrl:CloseMarketTipItemView()
	self.sell_tips:Close()
end

-- 打开物品下架面板(传入参数为服务器下发的商品信息)
function MarketWGCtrl:OpenMarketRemoveSellTips(my_goods_info)
	self.remove_sell_tips:SetData(my_goods_info)
	self.remove_sell_tips:Open()
end

-- 关闭物品下架面板
function MarketWGCtrl:CloseMarketRemoveSellTips()
	self.remove_sell_tips:Close()
end

function MarketWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if MarketWGData.Instance:GetAuctionCfgByItemId(change_item_id) then 						-- 判断是否是市场商品id
		self:CancelItemChangeCallbackFlush()
		-- 避免频繁刷新
		self.item_change_delay_flush = GlobalTimerQuest:AddDelayTimer(function ()
			ViewManager.Instance:FlushView(GuideModuleName.Market, TabIndex.market_sell, "Part", {flush_sell_bag = true})
		end, 0.3)
	end
end

function MarketWGCtrl:CancelItemChangeCallbackFlush()
	if self.item_change_delay_flush then
		GlobalTimerQuest:CancelQuest(self.item_change_delay_flush)
		self.item_change_delay_flush = nil
	end
end

-- 打开购买市场物品确认框
function MarketWGCtrl:OpenBuyMarketGoodsAlert(auction_index, has_password, total_price, item_id)
	--累充限制
	local php_recharge_limit = GLOBAL_CONFIG.param_list.vip_market_recharge_limit_open or 0
	local history_recharge = RechargeWGData.Instance:GetRealChongZhiRmb()
	if history_recharge < php_recharge_limit then
		local str = string.format(Language.Market.HistoryRechargeLimit2, php_recharge_limit)
		TipWGCtrl.Instance:ShowSystemMsg(str)
		return
	end

	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(item_id)
	local auction_price_type = cfg and cfg.auction_price_type or 0
	total_price = total_price or 0
	if auction_price_type == 3 then
		if not RoleWGData.Instance:GetIsEnoughBindGold(total_price) then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Bag.NoEnoughBindGold)
			return
		end
	elseif not RoleWGData.Instance:GetIsEnoughUseGold(total_price) then
		UiInstanceMgr.Instance:ShowChongZhiView()
		return
	end

	local password = 0
	if has_password then
		local num_keypad = TipWGCtrl.Instance:GetPopNumView()
		num_keypad:Open()
		num_keypad:SetPopString(Language.Market.ScaleMiMa)
		num_keypad:SetMaxValue(999999, true)
		num_keypad:SetOkCallBack(function(input_num)
			password = input_num
			self:SendCSRoleOpAuctionItem(AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_DIRECTYBUG, auction_index, password)
		end)
		return
	else
		TipWGCtrl.Instance:OpenAlertTips(Language.Market.AlerBuyTips, function()
			self:SendCSRoleOpAuctionItem(AUCTIONITEM_OP_TYPE.AUCTIONITEM_OP_TYPE_DIRECTYBUG, auction_index, password)
		end)
	end

end

---------拍卖----start----------------------------------------------------------------------------
function MarketWGCtrl:SendCSNewAuctionOperate(is_cross, op_type, param1, param2, param3, param4)
	-- print_error("【---拍卖请求----】：", is_cross, op_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSNewAuctionOperate)
	protocol.is_cross = is_cross and 1 or 0
	protocol.op_type = op_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol.param4 = param4 or 0
	protocol:EncodeAndSend()
end

--所有拍卖商品信息
function MarketWGCtrl:OnSCNewAuctionItemInfo(protocol)
	-- print_error("FFF=== 所有拍卖商品信息", protocol)
	self.data:SetAllAuctionInfo(protocol)
	self:FlushAuctionView()
	MainuiWGCtrl.Instance:FlushView(0,"set_auction_tips")
	AuctionTipsWGCtrl.Instance:SetTimer()
end
--单条 拍卖商品信息更新
function MarketWGCtrl:OnSCNewAuctionItemInfoUpdate(protocol)
	-- print_error("单条 拍卖商品信息  更新 ==", protocol)
	--刷新
	self.data:OnAuctionItemInfoUpdate(protocol)
	self:FlushAuctionView()
	MainuiWGCtrl.Instance:FlushView(0,"set_auction_tips")
	AuctionTipsWGCtrl.Instance:SetTimer()
end
--单条 拍卖商品信息移除
function MarketWGCtrl:OnSCNewAuctionItemInfoRemove(protocol)
	-- print_error("单条 拍卖商品信息  移除 ==", protocol)
	self.data:OnAuctionItemInfoRemove(protocol)
	--刷新
	self:FlushAuctionView()
	MainuiWGCtrl.Instance:FlushView(0,"set_auction_tips")
	AuctionTipsWGCtrl.Instance:SetTimer()
end
----------------------------

--竞价信息返还
function MarketWGCtrl:OnSCNewAuctionBidReturn(protocol)
	-- print_error("竞价信息返还 == ", protocol)
	local info = {}
	info.index = protocol.index 	-- 拍卖商品索引
	info.price = protocol.price		-- 竞拍价格
	info.is_auto_bid = protocol.is_auto_bid	-- 是否自动竞价:0 / 1
	info.type = protocol.type
	info.item_id = protocol.item_id
	self.data:OnAuctionBeOutdoneInfoUpdate(info)
	self:ShowAuctionTipsBtnView()
	self:ShowAuctionBidReturnTips(info)
end
----------------------------

--竞拍日志下发
function MarketWGCtrl:OnSCNewAuctionLogInfo(protocol)
	-- print_error("FFF=== 竞拍日志下发", protocol)
	self.data:SetAuctionLogInfo(protocol)
end
--单条 竞拍日志下发
function MarketWGCtrl:OnSCNewAuctionLogAdd(protocol)
	-- print_error("单条 竞拍日志下发 == ", protocol)
	self.data:OnAuctionLogInfoUpdate(protocol)
	self:FlushAuctionView(TabIndex.market_auction_record)
end
----------------------------

--当前我的竞拍信息下发
function MarketWGCtrl:OnSCNewAuctionBidRecordInfo(protocol)
	-- print_error("FFF=== 当前我的竞拍信息下发", protocol)
	self.data:SetMyAuctionInfo(protocol)
end
--单条 当前我的竞拍信息下发
function MarketWGCtrl:OnSCNewAuctionBidRecordAdd(protocol)
	-- print_error("单条 当前我的竞拍信息下发 == ", protocol)
	self.data:OnMyAuctionInfoAdd(protocol)
	--刷新市场竞价面板
	self:FlushAuctionView(TabIndex.market_my_auction)
end

--刷新市场竞价面板
function MarketWGCtrl:FlushAuctionView(flush_index)
	if self.view:IsOpen() then
		local cur_show_index = self.view:GetShowIndex()
		if flush_index then
			if flush_index == cur_show_index then
				self.view:Flush(flush_index)
			end
			return
		end

		if cur_show_index == TabIndex.market_cross_server
			or cur_show_index == TabIndex.market_country_auction
			or cur_show_index == TabIndex.market_guild_auction
			or cur_show_index == TabIndex.market_my_auction
			or cur_show_index == TabIndex.market_auction_record then
			self.view:Flush(cur_show_index)
		end
	end
end

--打开竞价确认面板
function MarketWGCtrl:OpenAuctionComfirmView(info)
	self.auction_comfirm_view:SetInfo(info)
	if self.auction_comfirm_view:IsOpen() then
		self.auction_comfirm_view:Flush()
		return
	end
	self.auction_comfirm_view:Open()
end

function MarketWGCtrl:ShowAuctionTipsBtnView()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Market_Auction_Beyond, 1, function ()
		local show_info = nil
		local show_info_list = MarketWGData.Instance:GetAuctionBeOutdoneInfo()
		MarketWGData.Instance:ClearAuctionBeOutdoneInfo()
		if IsEmptyTable(show_info_list) then
			return
		end
		if #show_info_list == 1 then
			show_info = show_info_list[1]
		else
			for i = #show_info_list, 1, -1 do
				if MarketWGData.Instance:GetIsMyAuctionByIndex(show_info_list[i].index) then
					show_info = show_info_list[i]
					break
				end
			end
			if not show_info then
				show_info = show_info_list[#show_info_list]
			end
		end

		if not show_info then
			return
		end
		if not self.auction_tips_btn_alert then
			self.auction_tips_btn_alert = Alert.New()
		end

		self.auction_tips_btn_alert:SetOkFunc(function()
			local is_info_exist = MarketWGData.Instance:GetIsMyAuctionByIndex(show_info.index)
			if not is_info_exist then--该商品已被购买
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.Auction_Btn_Tips_2)
			else
				--跳转我的竞拍面板
				FunOpen.Instance:OpenViewByName(GuideModuleName.Market, TabIndex.market_my_auction)
			end
		end)
		local cur_cfg = MarketWGData.Instance:GetNewAuctionCfgByType(show_info.type, show_info.item_id)
		local item_name = ItemWGData.Instance:GetItemName(cur_cfg.item_id, nil ,true)
		local show_des = string.format(Language.Market.Auction_Btn_Tips_1, item_name, show_info.price)
		self.auction_tips_btn_alert:SetLableString(show_des)
		self.auction_tips_btn_alert:SetOkString(Language.Market.Auction_Btn_Ok_Str)
		self.auction_tips_btn_alert:Open()
	end)
end

function MarketWGCtrl:OnSCNewAuctionTypeAdded(protocol)
	local show_type = protocol.type
	local is_fix = protocol.is_fix --仙盟流拍至系统拍卖上架
	if show_type == AUCTION_TYPE.Guild and RoleWGData.Instance.role_vo.guild_id == 0 then--无仙盟不弹提示
		return
	end

	--功能开启判断
	local fun_name = FunName.market_country_auction
	if show_type == AUCTION_INFO_BIG_TYPE.Guild then
		fun_name = FunName.market_guild_auction
	elseif show_type == AUCTION_INFO_BIG_TYPE.CrossServer then
		fun_name = FunName.market_cross_server
	end

	if not FunOpen.Instance:GetFunIsOpened(fun_name) then
		return
	end

	self:OpenAuctionInfoAddByType(show_type, is_fix)
	self.data:SetAuctionUpQiPaoActive(true)
end

function MarketWGCtrl:OnSCRoleAuctionInfo(protocol)
	self.data:SetAuctionInfo(protocol)
	if self.sell_tips:IsOpen() then
		self.sell_tips:Flush()
	end
end

function MarketWGCtrl:OpenAuctionInfoAddByType(show_type, is_fix)
	if not self.auction_goods_add_alert then
		self.auction_goods_add_alert = Alert.New()
	end

	local show_str = Language.Market.Auction_Map_Tips
	local jump_index = TabIndex.market_country_auction
	local is_guild_liupai = is_fix == 1

	if is_guild_liupai then--仙盟流拍
		if show_type == AUCTION_TYPE.Guild_Battle then
			show_str = Language.Market.Auction_Guild_LiuPai_Tips2
			jump_index = TabIndex.market_cross_server
		else
			show_str = Language.Market.Auction_Guild_LiuPai_Tips
			jump_index = TabIndex.market_country_auction
		end
	else
		if show_type == AUCTION_TYPE.Guild then
			show_str = Language.Market.Auction_Guild_Tips
			jump_index = TabIndex.market_guild_auction
		elseif show_type == AUCTION_TYPE.System then
			show_str = Language.Market.Auction_Country_Tips
		elseif show_type == AUCTION_TYPE.Guild_Battle then
			show_str = Language.Market.Auction_Guild_LiuPai_Tips2
			jump_index = TabIndex.market_cross_server
		end
	end

	self.auction_goods_add_alert:SetOkFunc(function()
		--取消区别,默认跳到全部页签
		-- if show_type == AUCTION_TYPE.Country then
		-- 	self.view:CountryAuctionJumpIndexShow(1, 1)--选中版图分类页签
		-- elseif show_type == AUCTION_TYPE.System then
		-- 	self.view:CountryAuctionJumpIndexShow(2, 1)--选中国家分类页签
		-- end
		--打开市场拍卖
		FunOpen.Instance:OpenViewByName(GuideModuleName.Market, jump_index)
	end)
	self.auction_goods_add_alert:SetOkString(Language.Market.Auction_Tips_Ok)
	self.auction_goods_add_alert:SetCancelString(Language.Market.Auction_Tips_Cancel)
	self.auction_goods_add_alert:SetLableString(show_str)
	self.auction_goods_add_alert:Open()
end

function MarketWGCtrl:OpenAuctionOnePriceGetView(info)
	TipWGCtrl.Instance:ShowGetReward(nil, info, nil, nil, nil, true)--通用奖励展示
end

--竞价被超越信息飘字提示
function MarketWGCtrl:ShowAuctionBidReturnTips(info)
	if not info then return end
	local cur_cfg = MarketWGData.Instance:GetNewAuctionCfgByType(info.type, info.item_id)
	local item_name = ItemWGData.Instance:GetItemName(cur_cfg.item_id, nil ,true)
	local show_des = string.format(Language.Market.Auction_Btn_Tips_3, item_name, info.price)
	SysMsgWGCtrl.Instance:ErrorRemind(show_des)
end
---------拍卖----end----------------------------------------------------------------------------

---------商城----start----------------------------------------------------------------------------
function MarketWGCtrl:SetDefaultJumpIndex(index)
	if self.view:IsOpen() then
		self.view:SetDefaultJumpIndex(index)
	end
end
---------商城----end----------------------------------------------------------------------------