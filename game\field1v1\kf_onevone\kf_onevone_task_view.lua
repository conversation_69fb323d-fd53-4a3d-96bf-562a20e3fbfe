KFOneVOneTaskView = KFOneVOneTaskView or BaseClass(SafeBaseView)

function KFOneVOneTaskView:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf_onevone_scene")
	self.old_score = 0
    self.active_close = false
    self.is_safe_area_adapter = true
	self.view_layer = UiLayer.MainUIHigh
    self.need_play_start_tween = false
    self.need_play_jingji_anim = false
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD

    -- self.shrink_button_event = BindTool.Bind1(self.ShrinkButtonsValueChange, self)
    -- self.main_menu_icon_event = BindTool.Bind1(self.MainMenuIconChangeEvent, self)
end

function KFOneVOneTaskView:LoadCallBack()
 	MainuiWGCtrl.Instance:AddInitCallBack(nil,BindTool.Bind(self.MainInitCallBack,self))
 	XUI.AddClickEventListener(self.node_list["reward_btn"], BindTool.Bind(self.ClickRewardBtn,self))
 	XUI.AddClickEventListener(self.node_list["changci_reward_btn"], BindTool.Bind(self.ClickChangCiRewardBtn,self))
 	XUI.AddClickEventListener(self.node_list["jingcai_btn"], BindTool.Bind(self.ClickJingCaiBtn,self))
	 XUI.AddClickEventListener(self.node_list["jingcai_bom_btn"], BindTool.Bind(self.ClickJingCaiBtn,self))
 	-- self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, self.shrink_button_event)
    -- self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, self.main_menu_icon_event)

 	if self.need_play_jingji_anim then
 		self:RealPlayJingJiTips(self.jingji_old_match_state,self.jingji_new_match_state,self.jingji_knockout)
 		self.need_play_jingji_anim = false
 	end

 	if self.need_play_start_tween then
 		self:StartTween()
 		self.need_play_start_tween = false
 	end
end

function KFOneVOneTaskView:ReleaseCallBack()
	-- if nil ~= self.shrinkbuttons_change then
	-- 	GlobalEventSystem:UnBind(self.shrinkbuttons_change)
	-- 	self.shrinkbuttons_change = nil
	-- end

	-- if nil ~= self.main_menu_icon_change then
	-- 	GlobalEventSystem:UnBind(self.main_menu_icon_change)
	-- 	self.main_menu_icon_change = nil
	-- end

    if self.tween then
    	self.tween:Kill()
    	self.tween = nil
    end

    if self.tween_end_pos then
    	self.tween_end_pos:Kill()
    	self.tween_end_pos = nil
    end

	self.old_score = 0

	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
end

-- function KFOneVOneTaskView:ShrinkButtonsValueChange(isOn)
-- 	-- if self:IsOpen() and self.node_list["jingcai"] then
-- 	--     local menu_ison = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
-- 	-- 	if isOn or menu_ison then
-- 	-- 		self.node_list["jingcai"].rect:DOAnchorPosX(200, 0.3)
-- 	-- 	else
-- 	-- 		self.node_list["jingcai"].rect:DOAnchorPosX(-60, 0.3)
-- 	-- 	end
-- 	-- end
-- end

-- function KFOneVOneTaskView:MainMenuIconChangeEvent(isOn)
-- 	-- if self:IsOpen() and self.node_list["jingcai"] then
-- 	-- 	if isOn then
-- 	-- 		self.node_list["jingcai"].rect:DOAnchorPosX(200, 0.3)
-- 	-- 	else
-- 	-- 		self.node_list["jingcai"].rect:DOAnchorPosX(-60, 0.3)
-- 	-- 	end
-- 	-- end
-- end

function KFOneVOneTaskView:MainInitCallBack()
	-- local scene_type = Scene.Instance:GetSceneType()
	-- if scene_type == SceneType.Kf_OneVOne_Prepare then
	-- end

	MainuiWGCtrl.Instance:AddBtnToGuajiLine(self.node_list["jingcai"])

	local player_info_parent = MainuiWGCtrl.Instance:GetPlayerInfoWidgetRoot()
	self.node_list["jingcai_bom"].transform:SetParent(player_info_parent.gameObject.transform)

	self:InitCallBack()
end

function KFOneVOneTaskView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	if self.node_list["fp_move_left_node"] then
		self.obj = self.node_list["fp_move_left_node"].gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end

    if self.is_out_fb then
		self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function KFOneVOneTaskView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end
end

function KFOneVOneTaskView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function KFOneVOneTaskView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("onevone_start_time") then
		CountDownManager.Instance:RemoveCountDown("onevone_start_time")
	end

    if self.show_info then
        GlobalTimerQuest:CancelQuest(self.show_info)
        self.show_info = nil
    end

    if self.node_list["jingcai"] then
		self.node_list["jingcai"].gameObject.transform:SetParent(self.root_node_transform, false)
	end

	if self.node_list["jingcai_bom"] then
		self.node_list["jingcai_bom"].gameObject.transform:SetParent(self.root_node_transform, false)
	end

	self.is_out_fb = true

    if self.obj then
        self.obj:SetActive(false)
    end
end


function KFOneVOneTaskView:OnFlush(param)
	local person_info = KFOneVOneWGData.Instance:GetOneVOnePersonInfo()
	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local prematch_round = KFOneVOneWGData.Instance:GetPermatchRound()
	local onevone_match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local is_prematch_all_matched = KFOneVOneWGData.Instance:GetIsPrematchAllMatched()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)

	if not IsEmptyTable(person_info) then
		local win_num = person_info.win_times
		local def_num = person_info.lose_times
		local score = person_info.score
		local rank_info = KFOneVOneWGData.Instance:GetOneVOneMyRankPerson()
		local rank = rank_info and rank_info.rank or 0
		local old_score = KFOneVOneWGData.Instance:GetOldScore()
		self.node_list["score_text"].text.text = score
		local lose_num =  0
		if is_open and prematch_round  > (win_num + def_num) then
			if is_prematch_all_matched then
				lose_num = prematch_round - win_num - def_num
			else
				lose_num = prematch_round - win_num - def_num - 1
			end
		end
		self.node_list["zhanji_text"].text.text = string.format(Language.Kuafu1V1.SceneTaskDesc3,win_num,def_num,lose_num)
		local rank_str = ""
		if onevone_match_state == KFOneVOneWGData.MatchState.TaoTai or knockout_state == KFOneVOneWGData.KnockoutState.WinnerEnd then
			rank = person_info.knockout_rank + 1
			self.node_list["rank_text"]:SetActive(rank > 0)
			rank_str = rank <= 0 and Language.Rank.NoRank or rank
			self.node_list["rank_text"].text.text = string.format(Language.Kuafu1V1.TaoTaiRankText,rank_str)
		else
			rank_str = rank <= 0 and Language.Rank.NoRank or rank
			self.node_list["rank_text"].text.text = string.format(Language.Kuafu1V1.RankText,rank_str)
		end
	end

	local red_flag = KFOneVOneWGData.Instance:GetKFOneVOneMsgRed()
	local rank_red = KFOneVOneWGData.Instance:IsShowRankRewardRed()

	self.node_list["reward_btn_red"]:SetActive(red_flag == 1 or rank_red == 1)
	local is_show_point_1 = Field1v1WGData.Instance:IsShowActOneVSOneJiFenbArenaRedPoint()
	local is_show_point_2 = KFOneVOneWGData.Instance:IsShowKFOneVOneRedPoint()

	self.node_list["changci_btn_red"]:SetActive(is_show_point_1 == 1 or is_show_point_2 == 1)

	local jingcai_flag = KFOneVOneWGData.Instance:IsShowJingCaiRed()
	self.node_list["jingcai_btn_red"]:SetActive(jingcai_flag == 1)

	self:FlushTaskTimer()

end

function KFOneVOneTaskView:FlushTaskTimer()
	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local prematch_round = KFOneVOneWGData.Instance:GetPermatchRound()
	local onevone_match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local is_over = false

	self.node_list["fight_countdowm"]:SetActive(true)
	self.node_list["auto_pipei"]:SetActive(false)
	self.node_list["jingcai"]:SetActive(false)
	self.node_list["jingcai_bom"]:SetActive(false)
	local other_cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
	-- self.node_list["task_title"].text.alignment = UnityEngine.TextAnchor.MiddleLeft
	
	if onevone_match_state == KFOneVOneWGData.MatchState.None then
		self.node_list["start_panel"]:SetActive(false)
		if knockout_state == KFOneVOneWGData.KnockoutState.WinnerEnd then
			local grade_str = Language.Kuafu1V1.TaoTaiGrade[5]
			local vs_str = KFOneVOneWGData.Instance:GetWinnerEndVSStr()
			self.node_list["task_title"].text.text = string.format(grade_str,vs_str)
			self.node_list["fight_countdowm"]:SetActive(false)
		end
	elseif onevone_match_state == KFOneVOneWGData.MatchState.YuXuan then
		self.node_list["auto_pipei"]:SetActive(next_time > server_time)
		-- if next_time > server_time then
		-- 	GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.AutoPiPei)		--自动普配
		-- else
		-- 	GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)		
		-- end
		local prematch_max_round = other_cfg.prematch_max_round
		local is_prematch_all_matched = KFOneVOneWGData.Instance:GetIsPrematchAllMatched()
		if is_prematch_all_matched then
			next_time = next_time + other_cfg.prematch_per_match_interval_sec
			prematch_round = prematch_round >= prematch_max_round and prematch_round or prematch_round + 1
		end
		self.node_list["task_title"].text.text = string.format(Language.Kuafu1V1.SceneTaskTitle2,prematch_round,prematch_max_round)

		--预选赛开始的图片动画 img_id = 7
		local can_play = KFOneVOneWGData.Instance:GetPlayJingJiAnim(7)
		if not can_play and prematch_round == 1 then
			self:ShowJingJiImgAnim(7)
		end

	elseif onevone_match_state == KFOneVOneWGData.MatchState.YuXuanEnd then
		self.node_list["task_title"].text.text = Language.Kuafu1V1.SceneTaskTitle4
		self.node_list["start_text_img"].image:LoadSprite(ResPath.GetF2Field1v1("a2_jingji_img8"))
		self.node_list["start_text_img"].image:SetNativeSize()
		--需要先弹晋级或者不能进入16强的动画 在弹淘汰赛开始的提示
		self:FlsuhSceneText()
		
	elseif onevone_match_state == KFOneVOneWGData.MatchState.TaoTai then
		is_over = KFOneVOneWGData.Instance:GetMyInKnockoutIsOver()
		local other_cfg = KFOneVOneWGData.Instance:GetKFOneVOneOtherCfg()
		local knockout_fight_sec = other_cfg.knockout_fight_sec
		self.node_list["jingcai"]:SetActive(knockout_state <= KFOneVOneWGData.KnockoutState.Index2To1)

		--加延迟2秒
		next_time = next_time > server_time and next_time + 2 or next_time + knockout_fight_sec
		if is_over then
			self.node_list["fight_countdowm"].text.text = Language.Kuafu1V1.SceneTaskDesc5			
		end
		local grade_str = Language.Kuafu1V1.TaoTaiGrade[knockout_state]
		local str = ""
		if knockout_state == KFOneVOneWGData.KnockoutState.Index2To1 then
			local vs_str = KFOneVOneWGData.Instance:GetIndex2To1VSStr()
			str = string.format(grade_str,vs_str)
			-- self.node_list["task_title"].text.alignment = UnityEngine.TextAnchor.MiddleLeft
		elseif knockout_state == KFOneVOneWGData.KnockoutState.WinnerEnd then
			local vs_str = KFOneVOneWGData.Instance:GetWinnerEndVSStr()
			str = string.format(grade_str,vs_str)
		else
			str = grade_str
			local is_lunkong = KFOneVOneWGData.Instance:GetMyInKnockoutIsLunKong()
			if is_lunkong and not is_over then
				str = str .. Language.Kuafu1V1.LungKongTitle
			end
		end

		self:FlsuhSceneText()

		self.node_list["task_title"].text.text = str
		if knockout_state == KFOneVOneWGData.KnockoutState.WinnerEnd then
			self.node_list["fight_countdowm"]:SetActive(false)
		end

		--淘汰赛开始的图片动画 img_id = 8
		local can_play = KFOneVOneWGData.Instance:GetPlayJingJiAnim(8)
		if not can_play then
			self:ShowJingJiImgAnim(8)
		end

	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
	if act_info and act_info.status == ACTIVITY_STATUS.STANDY then
		self.node_list["start_text_img"].image:LoadSprite(ResPath.GetF2Field1v1("a2_jingji_img7"))
		self.node_list["start_text_img"].image:SetNativeSize()
		self.node_list["fight_countdowm"]:SetActive(true)
		self.node_list["task_title"].text.text = Language.Kuafu1V1.SceneTaskTitle3
		next_time = act_info.next_time
	end

	if next_time > 0 then
		if next_time > server_time then
			local time = next_time - server_time
			self:StartRefreshTime(server_time, next_time)
			if CountDownManager.Instance:HasCountDown("onevone_start_time") then
				CountDownManager.Instance:RemoveCountDown("onevone_start_time")
			end
			CountDownManager.Instance:AddCountDown("onevone_start_time", BindTool.Bind1(self.StartRefreshTime, self), BindTool.Bind1(self.StartTimeComplete, self), nil, time,1)
		else
			self:StartTimeComplete(true)
		end
	end
end

function KFOneVOneTaskView:FlsuhSceneText()
	local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	local match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local my_rank = KFOneVOneWGData.Instance:GetOneVOneMyRankNum()
	local has_knockout_match_info = KFOneVOneWGData.Instance:GetHasKnockoutMatchInfo()
	if match_state == KFOneVOneWGData.MatchState.YuXuanEnd then
		-- self:StartTween()
		local can_play0 = KFOneVOneWGData.Instance:GetPlayJingJiAnim(0)
		local can_play1 = KFOneVOneWGData.Instance:GetPlayJingJiAnim(1)
		local my_knockout_index = KFOneVOneWGData.Instance:GetMyKnockoutIndex()
		if can_play0 or can_play1 then
			if my_rank and my_rank > 0 and my_rank <= 16 then
			-- if my_knockout_index and my_knockout_index > 0 then
				-- self:StartTween()
				self.node_list["scene_text2"]:SetActive(false)
				self.node_list["scene_text1"]:SetActive(false)
			else
				self.node_list["scene_text2"]:SetActive(true)
				self.node_list["scene_text1"]:SetActive(false)
			end
		end
	elseif match_state == KFOneVOneWGData.MatchState.TaoTai and has_knockout_match_info then
		local my_knockout_index = KFOneVOneWGData.Instance:GetMyKnockoutIndex()
		if my_knockout_index and my_knockout_index > 0 then
			if knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then 
				local has_4_to_2 = KFOneVOneWGData.Instance:GetKnockoutIndexes4To2Info(my_knockout_index)
				if has_4_to_2 then
					local lose_ainm = KFOneVOneWGData.Instance:GetPlayJingJiAnim(6)
					local winner_ainm = KFOneVOneWGData.Instance:GetPlayJingJiAnim(5)
					if lose_ainm or winner_ainm then
						self.node_list["scene_text1"]:SetActive(true)
						self.node_list["scene_text2"]:SetActive(false)
					end
				else
					self.node_list["scene_text1"]:SetActive(true)
					self.node_list["scene_text2"]:SetActive(false)
				end

			else
				local lose_ainm = KFOneVOneWGData.Instance:GetPlayJingJiAnim(6)
				local is_over = KFOneVOneWGData.Instance:GetMyInKnockoutIsOver()
				if lose_ainm and is_over then
					self.node_list["scene_text2"]:SetActive(true)
					self.node_list["scene_text1"]:SetActive(false)
				end
			end
		else
			if knockout == KFOneVOneWGData.KnockoutState.WinnerEnd then 
				self.node_list["scene_text1"]:SetActive(true)
				self.node_list["scene_text2"]:SetActive(false)
			else
				self.node_list["scene_text2"]:SetActive(true)
				self.node_list["scene_text1"]:SetActive(false)
			end
		end
	end
end

function KFOneVOneTaskView:StartRefreshTime(now_time, total_time)
	local lerp_time = total_time - now_time
	local time_str = TimeUtil.FormatSecond(lerp_time, 2)
	if self.node_list["fight_countdowm"] then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_ONEVONE)
		if act_info and act_info.status == ACTIVITY_STATUS.STANDY then
			self.node_list["fight_countdowm"].text.text = string.format(Language.Kuafu1V1.SceneTaskDesc8,time_str)
		else
			local onevone_match_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
			local next_time = KFOneVOneWGData.Instance:GetNextFightStartTime()
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			if onevone_match_state == KFOneVOneWGData.MatchState.YuXuanEnd then
				self.node_list["fight_countdowm"].text.text = string.format(Language.Kuafu1V1.SceneTaskDesc9,time_str)
			elseif onevone_match_state == KFOneVOneWGData.MatchState.YuXuan then
				if next_time > server_time then
					self.node_list["fight_countdowm"].text.text = string.format(Language.Kuafu1V1.SceneTaskDesc1,time_str)
				else
					self.node_list["fight_countdowm"].text.text = string.format(Language.Kuafu1V1.SceneTaskDesc7,time_str)
				end
			elseif onevone_match_state == KFOneVOneWGData.MatchState.TaoTai then
				local is_over = KFOneVOneWGData.Instance:GetMyInKnockoutIsOver()
				if is_over then
					self.node_list["fight_countdowm"].text.text = Language.Kuafu1V1.SceneTaskDesc5
				else
					if next_time + 2 > server_time then
						self.node_list["fight_countdowm"].text.text = string.format(Language.Kuafu1V1.SceneTaskDesc1,time_str)
					else
						self.node_list["fight_countdowm"].text.text = string.format(Language.Kuafu1V1.SceneTaskDesc7,time_str)
					end
				end
				if next_time + 2 > server_time then
					self.node_list["jingcai_time_text"].text.text = string.format(Language.Kuafu1V1.JingCaiTimeStr1,time_str)
					if self.node_list["can_jingcai_effect"]:GetActive() == false then
						self.node_list["can_jingcai_effect"]:SetActive(true)
					end

					if self.node_list.jingcai_bom then
						local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
						local num = KFOneVOneDuiZhenView.Lunci[knockout]
						local jingcai_count = KFOneVOneWGData.Instance:GetKnockoutIsGuessWinnerCount(knockout)
						local show_jingcai_bom_btn = (nil ~= num) and (nil ~= jingcai_count) and (jingcai_count < num)
						-- self.node_list.jingcai_bom:CustomSetActive(show_jingcai_bom_btn)
					end
				else
					self.node_list["jingcai_time_text"].text.text = string.format(Language.Kuafu1V1.JingCaiTimeStr2,time_str)
					if self.node_list["can_jingcai_effect"]:GetActive() == true then
						self.node_list["can_jingcai_effect"]:SetActive(false)
					end

					if self.node_list.jingcai_bom then
						self.node_list.jingcai_bom:CustomSetActive(false)
					end
				end
			end
		end

	end
end

function KFOneVOneTaskView:StartTimeComplete(only_one)
	local cur_state = KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local is_over = KFOneVOneWGData.Instance:GetMyInKnockoutIsOver()
	if cur_state == KFOneVOneWGData.MatchState.TaoTai and not is_over then
		local is_lunkong = KFOneVOneWGData.Instance:GetMyInKnockoutIsLunKong()
		if is_lunkong then
			Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_KNOCKOUT_MATCH_INFO)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.LunKongTip)
		else
			local enemy_info = KFOneVOneWGData.Instance:GetMyEnemyInfo()
			if enemy_info then
				--设置进入前对手的下标
				KFOneVOneWGData.Instance:SetEnterSceneEnemyIndex(enemy_info.index)
				local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_ENTER_PK_SCENE
				Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type)
			end
		end

		local knockout = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
		KFOneVOneWGData.Instance:SetEnterSceneKnockout(knockout)
		if self.node_list["fight_countdowm"] then
			self.node_list["fight_countdowm"].text.text = Language.Kuafu1V1.SceneTaskDesc4
		end
		if not only_one then
			self:Flush()
		end
	end
end

function KFOneVOneTaskView:ClickRewardBtn()
	ViewManager.Instance:Open(GuideModuleName.KFOneVOneRewardView)
end

function KFOneVOneTaskView:ClickChangCiRewardBtn()
	ViewManager.Instance:Open(GuideModuleName.ActJjc,TabIndex.arena_kf1v1)
end

function KFOneVOneTaskView:ClickJingCaiBtn()
	Field1v1WGCtrl.Instance:OpenJingCai()
end

function KFOneVOneTaskView:DoEndTween(is_close)
	if self:IsLoaded() then
	    local tween_scale_close = 0.3

	    if self.tween_end_pos then
	    	self.tween_end_pos:Kill()
	    	self.tween_end_pos = nil
	    end

	    self.tween_end_pos = DG.Tweening.DOTween.Sequence()
	    self.tween_end_pos:AppendCallback(function()
            local tween = self.node_list["start_panel"].rect:DOScaleY(0, tween_scale_close)
       		tween:SetEase(DG.Tweening.Ease.Linear)
	    end)

	    self.tween_end_pos:AppendInterval(0.1)
	    self.tween_end_pos:AppendCallback(function()
	        self.node_list["effect_2"]:SetActive(true)
	    end)

	    self.tween_end_pos:AppendInterval(0.5)--effect_2的特效时间

	    self.tween_end_pos:OnComplete(function()
	        if is_close then
		        self.node_list["start_panel"]:SetActive(false)
	        end

	    end)
	end
end

function KFOneVOneTaskView:StartTween()
	if not self:IsOpen() then
		self.need_play_start_tween = true
		return
	end
	local can_show = KFOneVOneWGData.Instance:GetCanShowAnim()
	if not can_show then
		KFOneVOneWGData.Instance:SetOnlyOneShowAnim()
		self.node_list["start_panel"]:SetActive(true)
	    local tween_scale_open = 0.2
	    local img_kill_time = 0.3
	    self.node_list["effect_1"]:SetActive(false)
	    self.node_list["effect_2"]:SetActive(false)
	    self.node_list["start_panel"].rect.localScale = Vector3(0,0,0)

	    if self.tween then
	    	self.tween:Kill()
	    	self.tween = nil
	    end

	    self.tween = DG.Tweening.DOTween.Sequence()

	    local tween = self.node_list["start_panel"].rect:DOScale(1, tween_scale_open)
	    tween:SetEase(DG.Tweening.Ease.OutBack)
	    self.tween:Append(tween)

	    self.tween:AppendInterval(tween_scale_open + 0.1)
	    self.tween:AppendCallback(function()
	        self.node_list["effect_1"]:SetActive(true)
	    end)

	    if self.show_info then
	        GlobalTimerQuest:CancelQuest(self.show_info)
	        self.show_info = nil
	    end

	    self.show_info = GlobalTimerQuest:AddDelayTimer(function()
	        self:DoEndTween(true)
	    end, 30)
	end
end

function KFOneVOneTaskView:PlayJingJiTips(old_match_state,new_match_state,knockout)
	if not self:IsOpen() or not self:IsLoaded() then
		self.jingji_old_match_state = old_match_state
		self.jingji_new_match_state = new_match_state
		self.jingji_knockout = knockout
		self.need_play_jingji_anim = true
		return
	end
	self:RealPlayJingJiTips(old_match_state,new_match_state,knockout)
	
end

function KFOneVOneTaskView:RealPlayJingJiTips(old_match_state,new_match_state,knockout)
	knockout = knockout or KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	old_match_state = old_match_state or KFOneVOneWGData.Instance:GetOneVOneMatchState()
	new_match_state = new_match_state or KFOneVOneWGData.Instance:GetOneVOneMatchState()
	local my_knockout_index = KFOneVOneWGData.Instance:GetMyKnockoutIndex()
	-- if old_match_state == KFOneVOneWGData.MatchState.YuXuanEnd and new_match_state == KFOneVOneWGData.MatchState.TaoTai
	-- 	and knockout == KFOneVOneWGData.KnockoutState.Index16To8 then
	if old_match_state == KFOneVOneWGData.MatchState.YuXuan and new_match_state == KFOneVOneWGData.MatchState.YuXuanEnd then
		local str = ""
		local img_id = -1

		-- if my_knockout_index and my_knockout_index > 0 then
		local my_rank = KFOneVOneWGData.Instance:GetOneVOneMyRankNum()
		if my_rank and my_rank > 0 and my_rank <= 16 then
			--恭喜您晋级到16强！
			-- str = Language.Kuafu1V1.TaoTaiJingJiTip[1]
			img_id = 1
			DebugLog("跨服1v1    晋级文字提示>>>>>>>>>>",Language.Kuafu1V1.TaoTaiJingJiTip[1])
		else
			--很遗憾，你本次未进入16强，请下次努力！
			-- str = Language.Kuafu1V1.TaoTaiJingJiTip[0]
			DebugLog("跨服1v1    晋级文字提示>>>>>>>>>>",Language.Kuafu1V1.TaoTaiJingJiTip[0])
			img_id = 0
		end
		if img_id >= 0 then
			local can_play = KFOneVOneWGData.Instance:GetPlayJingJiAnim(img_id)
			if not can_play then
				self.need_flush_scene_text_flag = true
				self:ShowJingJiImgAnim(img_id)
			end
		end
		return
	end

	if new_match_state == KFOneVOneWGData.MatchState.TaoTai and my_knockout_index and my_knockout_index > 0 then
		local is_win = KFOneVOneWGData.Instance:GetKnockoutItemWinInfo(knockout,my_knockout_index)
		local emeny_index = KFOneVOneWGData.Instance:GetEnterSceneEnemyIndex()
		local enemy_is_win = KFOneVOneWGData.Instance:GetKnockoutItemWinInfo(knockout,emeny_index)
		local can_show_win = is_win or enemy_is_win
		local str = ""
		local img_id = -1
		if can_show_win then
			if is_win then
				--晋级的提示
				local index = knockout >= KFOneVOneWGData.KnockoutState.WinnerEnd and KFOneVOneWGData.KnockoutState.WinnerEnd or knockout + 1
				-- str = Language.Kuafu1V1.TaoTaiJingJiTip[index]
				DebugLog("跨服1v1    晋级文字提示>>>>>>>>>>",Language.Kuafu1V1.TaoTaiJingJiTip[index])
				img_id = index

			else
				--惜败，请少侠下次努力！
				-- str = Language.Kuafu1V1.TaoTaiJingJiTip[6]
				DebugLog("跨服1v1    晋级文字提示>>>>>>>>>>",Language.Kuafu1V1.TaoTaiJingJiTip[6])
				img_id = 6
			end
		end
		if img_id > 0 then
			local can_play = KFOneVOneWGData.Instance:GetPlayJingJiAnim(img_id)
			if not can_play then
				self.need_flush_scene_text_flag = true
				self:ShowJingJiImgAnim(img_id)
			end
		end
	end
end

function KFOneVOneTaskView:ShowJingJiImgAnim(img_id)
	local is_main_role_fly_down = KFOneVOneWGData.Instance:GetMainRoleFlyDownFalg()
	-- DebugLog("跨服1v1    晋级提示ShowJingJiImgAnim>>>>>>>>>>",is_main_role_fly_down,img_id)
	if not is_main_role_fly_down then
		return 
	end
	-- DebugLog("跨服1v1    晋级提示ShowJingJiImgAnim>>>>>>>>>> play",img_id)

	KFOneVOneWGData.Instance:SetPlayJingJiAnim(img_id)
	KFOneVOneWGData.Instance:SetEnterSceneKnockout(nil)
	KFOneVOneWGData.Instance:SetEnterSceneEnemyIndex(nil)
	self.node_list["taotai_tip_text"].image:LoadSprite(ResPath.GetF2Field1v1("a2_jingji_img"..img_id))
	self.node_list["taotai_tip_text"].image:SetNativeSize()

	local bg_img_txt = "a2_jjc_tsk"
	if img_id == 0 or img_id == 6 then
		bg_img_txt = "a2_jjc_tsk_1"
	end

	self.node_list["taotai_bg_img"].image:LoadSprite(ResPath.GetF2Field1v1(bg_img_txt))
	self.node_list["taotai_bg_img"].image:SetNativeSize()
	self.node_list["taotai_bg_img"]:SetActive(true)
	self.node_list["taotai_bg_img"].canvas_group.alpha = 1
	local hide_func = function ()
		if self.node_list["taotai_bg_img"] then
			UITween.AlpahShowPanel(self.node_list["taotai_bg_img"], false, 0.5,nil,function ()
				if self.node_list["taotai_bg_img"] then
					self.node_list["taotai_bg_img"]:SetActive(false)
				end
				if self.need_flush_scene_text_flag and self:IsOpen() then
					self:FlsuhSceneText()
					self.need_flush_scene_text_flag = false
				end
			end)
		end
	end
	UITween.ScaleShowPanel(self.node_list["taotai_bg_img"],u3dpool.vec3(2, 2, 2),0.3,nil,function ()
		AddDelayCall(self,hide_func, 2)
	end)
end
