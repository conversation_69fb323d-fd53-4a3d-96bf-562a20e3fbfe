VipShowInitialView = VipShowInitialView or BaseClass(SafeBaseView)

function VipShowInitialView:__init()
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_vip_uplevel")
end

function VipShowInitialView:ReleaseCallBack()
	if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

	self:CancelTween()
end

function VipShowInitialView:SetDataAndOpen(show_vip)
	self.show_vip = show_vip
    if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end

function VipShowInitialView:LoadCallBack()

end

function VipShowInitialView:CloseCallBack()
end

function VipShowInitialView:OnFlush(param_t)
	self:FlushVipLevel()
	self:PlayOpenTween()
	self:LoadVipUpEffect()
end

function VipShowInitialView:FlushVipLevel()
	self.node_list.vip_level_text.text.text = self.show_vip
end

function VipShowInitialView:PlayOpenTween()
	if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

	self.node_list.vip_level_node.canvas_group.alpha = 0
	UITween.DoUpDownCrashTween(self.node_list.vip_bg)
	self.tween = self.node_list.vip_level_node.canvas_group:DoAlpha(0, 1, 0.1)
	self.tween:SetEase(DG.Tweening.Ease.OutCubic)
	self:CancelTween()
	local sequence = DG.Tweening.DOTween.Sequence()
	sequence:Append(self.tween)
	local initial_vip_level = VipWGData.Instance:GetVipOtherInfo("initial_vip_level")
	if self.show_vip == initial_vip_level then
		sequence:AppendInterval(1)
	end

	sequence:AppendCallback(function ()
		self:Close()
	end)

	self.sequence = sequence
end

function VipShowInitialView:CancelTween()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
end

function VipShowInitialView:LoadVipUpEffect()
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Levelup))
	local bundle, asset = ResPath.GetEffectUi("ui_jinjichenggong")
    EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list.active_eff.transform, 1,
            nil, nil, Vector3(1, 1, 1))
end