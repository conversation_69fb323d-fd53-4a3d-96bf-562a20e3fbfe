﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class PressActivator : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,IPointerDownHandler, IPointerUpHandler, IPointerEnterHandler, IPointerExitHandler
{
    //public GameObject normal;
    //public GameObject press;

    private bool is_click;
    private int count = 0;
    private int real_index = 0;

    void Start()
    {
        if (0 == count)
        {
            var parent = this.transform.parent;
            count = parent.childCount;
        }

        real_index = this.transform.GetSiblingIndex();
    }

    //放在点着不放，然后切换界面导致位置没还原
    void OnDisable()
    {
        if (is_click)
        {
            this.transform.SetSiblingIndex(real_index);
        }
        is_click = false;
    }

    public void OnPointerDown(PointerEventData eventData)
    {
        is_click = true;

        this.transform.SetSiblingIndex(count - 1);
    }

    public void OnPointerUp(PointerEventData eventData)
    {
        is_click = false;

        this.transform.SetSiblingIndex(real_index);
    }

    public void OnPointerEnter(PointerEventData eventData)
    {
        if (is_click)
        {
            this.transform.SetSiblingIndex(count - 1);
        }
    }

    public void OnPointerExit(PointerEventData eventData)
    {
        if (is_click)
        {
            this.transform.SetSiblingIndex(real_index);
        }
    }
}
