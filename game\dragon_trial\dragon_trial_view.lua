DragonTrialView = DragonTrialView or BaseClass(SafeBaseView)

local COUNT_DOWN_KEY = "dragon_trial_count_down1"

function DragonTrialView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	self:AddViewResource(0, "uis/view/dragon_trial_ui_prefab", "layout_dragon_trial")
end

function DragonTrialView:__delete()

end

function DragonTrialView:LoadCallBack()
	self.node_list.title_view_name.tmp.text = Language.DragonTrial.Title
	if not self.show_reward_list then
		self.show_reward_list = AsyncListView.New(DragonTrialRewardItem, self.node_list.reward_list)
	end
	
	local bundle, asset = ResPath.GetCommonButton("a3_ty_round_btn_01")
    self.node_list.btn_rule_tips.image:LoadSprite(bundle, asset, function()
        self.node_list.btn_rule_tips.image:SetNativeSize()
    end)

	XUI.AddClickEventListener(self.node_list.btn_challenge, BindTool.Bind(self.OnClickChallengeBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_activate, BindTool.Bind(self.OnClickActivateBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind(self.JumpToYuanShen, self))

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.DragonTrialView, self.get_guide_ui_event)
end

function DragonTrialView:ReleaseCallBack()
	if self.show_reward_list then
		self.show_reward_list:DeleteMe()
		self.show_reward_list = nil
	end

	if CountDownManager.Instance:HasCountDown(COUNT_DOWN_KEY) then
		CountDownManager.Instance:RemoveCountDown(COUNT_DOWN_KEY)
	end

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.DragonTrialView, self.get_guide_ui_event)

	self.cur_cfg_data = nil
end

function DragonTrialView:LoadIndexCallBack(index)
end

function DragonTrialView:OpenCallBack()
end

function DragonTrialView:CloseCallBack()
end

function DragonTrialView:ShowIndexCallBack(index)
	local bundle, assert = ResPath.GetF2RawImagesJPG("a3_wqcy_bj")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.DragonTrialView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL)
end

function DragonTrialView:OnFlush(param_t, index)
	local trial_info = DragonTrialWGData.Instance:GetDragonTrialInfo()
	if IsEmptyTable(trial_info) then
		return
	end

	local cur_grade_cfg = DragonTrialWGData.Instance:GetCurGradeCfg()
	if IsEmptyTable(cur_grade_cfg) then
		print_error("未获取到当前配置！档位：", DragonTrialWGData.Instance:GetCurrentGrade())
		return
	end

	local show_level_cfg = DragonTrialWGData.Instance:GetLevelMonsterCfg(trial_info.pass_seq + 1)
	self.cur_cfg_data = show_level_cfg

	local level_cfg_list = DragonTrialWGData.Instance:GetLevelMonsterCfgList()
	local show_leval = math.min(trial_info.pass_seq + 1, #level_cfg_list)
	local str = string.format(Language.DragonTrial.ChapterDes, show_level_cfg.level_des, show_leval, #level_cfg_list)
	self.node_list["txt_progress"].text.text = str

	self.node_list.desc.text.text = show_level_cfg.level_des1

	local role_cap = GameVoManager.Instance:GetMainRoleVo().capability
	local is_enough = role_cap >= show_level_cfg.need_cap
	self.node_list["target_capability_0"].text.text = string.format(Language.DragonTrial.Enough, CommonDataManager.ConverExpByThousand(show_level_cfg.need_cap))

	local str = is_enough and Language.DragonTrial.Enough or Language.DragonTrial.UnEnough
	str = string.format(str, CommonDataManager.ConverExpByThousand(role_cap))
	self.node_list["cap_value"].text.text = str
	
	self:FlushRewards(show_level_cfg)

	local red_point = DragonTrialWGData.Instance:GetDragonTrialRed()
	self.node_list["red_point"]:SetActive(red_point > 0)

	self:FlushProgress()
	self:FlushActivateBtnStatus()
end

function DragonTrialView:FlushRewards(show_level_cfg)
	local show_list = {}
	-- 先排固定奖励
	local fix_data = DragonTrialWGData.Instance:GetLevelMonsterReward(show_level_cfg.seq)
	if not IsEmptyTable(fix_data) then
		table.insert(show_list, fix_data)
	end

	local num = #show_level_cfg.pass_reward_item
	for i = 0, num do
		local data = show_level_cfg.pass_reward_item[i]
		table.insert(show_list, data)
	end

	self.show_reward_list:SetDataList(show_list)
end

function DragonTrialView:OnCDComplete()

end

function DragonTrialView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
    local fun = function()
        self:OnClickChallengeBtn()
    end

    return self.node_list[ui_name], fun
end

-- 点击挑战
function DragonTrialView:OnClickChallengeBtn()
    if (not self.cur_cfg_data) or (self.cur_cfg_data == nil) then
        return
    end
    local cfg_data = self.cur_cfg_data
	
	-- 已全部通关
    if DragonTrialWGData.Instance:IsAllUnlock() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.DragonTrial.DragonTrialErrorTips2)
        return
    end

    local capability = GameVoManager.Instance:GetMainRoleVo().capability
	local role_lv = RoleWGData.Instance:GetRoleLevel()

    if role_lv < cfg_data.need_level then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.WuHunZhenShen.WuHunTowerErrorTips2)
        return
    end

    if capability < cfg_data.need_cap then
		TipWGCtrl.Instance:ShowSystemMsg(Language.DragonTrial.DragonTrialErrorTips)
    else
        self:SendDragonTrialOperate()
    end
end

function DragonTrialView:SendDragonTrialOperate()
	DragonTrialWGCtrl.Instance:SendDragonTrialOperate(DRAGON_TRIAL_OPERATE_TYPE.OA_DRAGON_TRIAL_OPERATE_TYPE_DARE)
end

-- 解锁进度刷新
function DragonTrialView:FlushProgress()
	local num = DragonTrialWGData.Instance:GetProgressNum()
	for i = 1, num do
		local obj = self.node_list["progress_"..i]
		local is_unlock = DragonTrialWGData.Instance:CheckUnlock(i)
		if obj then
			obj.toggle.isOn = is_unlock
		end
		local eff = self.node_list["eff_"..i]
		if eff and is_unlock then
			local asset, bundle = ResPath.GetEffectUi("UI_wqcy_iconJH"..i)
			eff:ChangeAsset(asset, bundle)
		end
	end
end

-- 激活按钮状态
function DragonTrialView:FlushActivateBtnStatus()
	local is_can_activate = DragonTrialWGData.Instance:IsCanActivate()
	local is_activated = CultivationWGData.Instance:IsOpenNuQi()
	self.node_list.btn_activate:CustomSetActive(not is_activated and is_can_activate)
	self.node_list.btn_goto:CustomSetActive(is_activated)
	self.node_list.model_disply.canvas_group.alpha = (not is_activated and is_can_activate) and 0.4 or 1
end

-- 激活元神
function DragonTrialView:OnClickActivateBtn()
	-- 请求激活元神
	CultivationWGCtrl.Instance:SendRoleXiuWeiOperate(XIUWEI_OPERA_TYPE.XIUWEI_OPERA_TYPE_OPEN_NUQI_BIANSHEN)
	FunOpen.Instance:OpenFunByDefine2(OPEN_FUN_NAME.yuanshen, "a3_dz_btn11", FunOpenType.FlyTabOpen, "BtnDujie", nil, "0|0|0")
	self:Close()
end

-- 跳转元神
function DragonTrialView:JumpToYuanShen()
	FunOpen.Instance:OpenViewNameByCfg("DujieView#0#op=0", GameEnum.YUAN_SHEN_ACTIVATE_ITEM)
end


------------------------------------------------- 奖励item ----------------------------------------
DragonTrialRewardItem = DragonTrialRewardItem or BaseClass(BaseRender)

function DragonTrialRewardItem:__init()
	
end

function DragonTrialRewardItem:ReleaseCallBack()
	if self.item_obj then
		self.item_obj:DeleteMe()
		self.item_obj = nil
	end
end

function DragonTrialRewardItem:LoadCallBack()
	self.item_obj = ItemCell.New(self.node_list.item_root)
end

function DragonTrialRewardItem:OnFlush()
	self.node_list.level_tag:CustomSetActive(self.data.is_show_tag)
	if self.data.is_show_tag then
		self.node_list.text.text.text = string.format(Language.DragonTrial.LevelTag, self.data.show_level)
	end
	self.item_obj:SetData(self.data)
end