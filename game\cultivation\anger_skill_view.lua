AngerSkillView = AngerSkillView or BaseClass(SafeBaseView)
local Break_Skill_Type = {
    BREAK_SKILL = 1,
}

function AngerSkillView:__init()
	self:SetMaskBg(false)
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    local view_bundle = "uis/view/cultivation_ui_prefab"
    local common_path = "uis/view/common_panel_prefab"

    self:AddViewResource(0, common_path, "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_anger_skill_view")
    self:AddViewResource(0, common_path, "layout_a3_common_top_panel")
end

function AngerSkillView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Common.SkillPreview

	--类别列表
	if not self.anger_skill_list then
        self.anger_skill_list = {}
        for i = 1, 6 do
            local cell = AngerSkillRender.New(self.node_list.anger_skill_list:FindObj("anger_skill_item_" .. i))
            cell:SetIndex(i)
            cell:SetClick<PERSON>allBack(BindTool.Bind1(self.OnSelectSkillCB, self))
            self.anger_skill_list[i] = cell
        end
	end

    -- 技能列表
    if not self.anger_skill_type_list then
		self.anger_skill_type_list = AsyncListView.New(AngerSkillTypeRender, self.node_list.anger_skill_type_list)
        self.anger_skill_type_list:SetStartZeroIndex(true)
		self.anger_skill_type_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectSkillTypeCB, self))
	end

    -- 怒气技能境界相关列表
    if not self.anger_sp_attr_list then
		self.anger_sp_attr_list = AsyncListView.New(AngerSPAttrRender, self.node_list.anger_sp_attr_list)
	end

    -- 怒气普通技能境界相关列表
    if not self.anger_skill_attr_list then
		self.anger_skill_attr_list = AsyncListView.New(AngerSkillAttrRender, self.node_list.anger_skill_attr_list)
	end

    if not self.active_item then
        self.active_item = ItemCell.New(self.node_list.active_item)
    end

    if not self.skill_pre_view then
        self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage) 
        self.skill_pre_view:SetPreviewPlayEndCb(BindTool.Bind1(self.PreviewPlayEnd, self))
    end

    XUI.AddClickEventListener(self.node_list.btn_transform, BindTool.Bind(self.OnClickTransform, self))
    XUI.AddClickEventListener(self.node_list.btn_active, BindTool.Bind(self.OnClickActive, self))
end

function AngerSkillView:ReleaseCallBack()
    if self.anger_skill_list and #self.anger_skill_list > 0 then
		for _, anger_skill_cell in ipairs(self.anger_skill_list) do
			anger_skill_cell:DeleteMe()
			anger_skill_cell = nil
		end

		self.anger_skill_list = nil
	end

    if self.anger_skill_type_list then
		self.anger_skill_type_list:DeleteMe()
		self.anger_skill_type_list = nil
	end

    if self.anger_sp_attr_list then
		self.anger_sp_attr_list:DeleteMe()
		self.anger_sp_attr_list = nil
	end

    if self.anger_skill_attr_list then
		self.anger_skill_attr_list:DeleteMe()
		self.anger_skill_attr_list = nil
	end

	if self.skill_pre_view then
		self.skill_pre_view:DeleteMe()
		self.skill_pre_view = nil
	end

    if self.active_item then
		self.active_item:DeleteMe()
		self.active_item = nil
	end

    self.cur_select_type_index = nil
    self.cur_select_skill_index = nil
    self.cur_select_skill_data = nil
    self.cur_role_res_id = nil

    self:RemovePreSkillDelayTimer()
end

-- 选择类型回调
function AngerSkillView:OnSelectSkillTypeCB(type_item, cell_index, is_default, is_click)
    if nil == type_item or nil == type_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = type_item:GetIndex()
	if self.cur_select_type_index == cell_index then   ---这里不能return，左侧列表也要跟着协议刷新而刷新
        return
	end

	self.cur_select_type_index = cell_index
    self.cur_select_skill_index = nil
    self:FlushSkillList()
    self:FlushActiveStatus()
end

-- 选择类型回调
function AngerSkillView:OnSelectSkillCB(skill_item)
    if nil == skill_item or nil == skill_item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = skill_item:GetIndex()
	if self.cur_select_skill_index == cell_index then   ---这里不能return，左侧列表也要跟着协议刷新而刷新
        return
	end

	self.cur_select_skill_index = cell_index
    self.cur_select_skill_data = skill_item.data
    self:FlushSkillSelect()
    self:FlushSkillMessage()
    self:FlushSkillXiuWeiList()
    self:FlushPlaySkill(true)
end

-- 技能预览结束回调
function AngerSkillView:PreviewPlayEnd()
    self:RemovePreSkillDelayTimer()

	self.show_pre_skill_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if not self.cur_select_skill_data then
			return
		end

		if self.skill_pre_view and self.skill_pre_view:GetPreviewIsLoaded() then
			self.skill_pre_view:PlaySkill(self.cur_select_skill_data)
		end
	end, 4)
end

--移除回调
function AngerSkillView:RemovePreSkillDelayTimer()
    if self.show_pre_skill_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_pre_skill_delay_timer)
        self.show_pre_skill_delay_timer = nil
    end
end

function AngerSkillView:OnFlush()
    -- 刷新入口
    self:FlushSkillTypeList()
end

-- 刷新类型
function AngerSkillView:FlushSkillTypeList()
    local select_index = 0
    local type_list = Language.Cultivation.AngerSkillType
    local need_jump = false     -- 需要跳转是为切换自动跳转
    
    if self.cur_select_type_index == nil then
        -- self.cur_select_type_index = select_index
        need_jump = true
    end

    self.anger_skill_type_list:SetDataList(type_list)

    if need_jump then
        self.anger_skill_type_list:JumpToIndex(select_index, 10)
    else
        self:FlushSkillList()
    end

    self:FlushActiveStatus()
end

function AngerSkillView:FlushActiveStatus()
    local act_id = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
    self.node_list.active_layer:CustomSetActive(act_id == 0)
    self.node_list.break_layer:CustomSetActive(act_id ~= 0)
    self.node_list.btn_transform:CustomSetActive(act_id ~= 0)
    self.node_list.active_remind:CustomSetActive(false)
    
    if act_id ~= 0 then
        -- 刷新幻化状态
        local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
        local str = curr_nuqi_type == self.cur_select_type_index and Language.Common.YiHuanHua or Language.Common.HuanHua
        self.node_list.btn_transform_text.text.text = str
    else
        local cfg = CultivationWGData.Instance:GetActiveCfgByType(self.cur_select_type_index)
        if cfg then
            self.active_item:SetData({ item_id = cfg.item_id })
            local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
            local color = item_num >= cfg.item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
            self.node_list.active_remind:CustomSetActive(item_num >= cfg.item_num)
            self.active_item:SetRightBottomTextVisible(true)
            self.active_item:SetRightBottomColorText(item_num .. '/' .. cfg.item_num, color)
        end
    end
end

-- 刷新技能列表
function AngerSkillView:FlushSkillList()
    local skill_list = CultivationWGData.Instance:GetActiveSkillListByType(self.cur_select_type_index) 
    local select_index = 1

    if self.cur_select_skill_index == nil then
        self.cur_select_skill_index = select_index
        self.cur_select_skill_data = skill_list[select_index]
        self:FlushPlaySkill()
    end

    for i, anger_skill_cell in ipairs(self.anger_skill_list) do
        if skill_list[i] then
            anger_skill_cell:SetVisible(true)
            anger_skill_cell:SetData(skill_list[i])
        else
            anger_skill_cell:SetVisible(false)
        end
    end

    self:FlushSkillSelect()
    self:FlushSkillMessage()
    self:FlushSkillXiuWeiList()
end

-- 刷新技能选中
function AngerSkillView:FlushSkillSelect()
    for i, anger_skill_cell in ipairs(self.anger_skill_list) do
        anger_skill_cell:ChangeSelect(i == self.cur_select_skill_index)
    end
end

-- 刷新技能详情
function AngerSkillView:FlushSkillMessage()
    if not self.cur_select_skill_data then
        return
    end

    local skill_id = self.cur_select_skill_data
    local skill_level = 1
    self.node_list.skill_lv.text.text = string.format(Language.Rank.Level, ToColorStr(skill_level, COLOR3B.GREEN)) 

    if self.cur_select_skill_index == Break_Skill_Type.BREAK_SKILL then
        self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_id))
    else
        local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
        if skill_cfg then
            self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon_resource))
        end
    end

	local xiuxian_cfg = SkillWGData.Instance:GetXiuXianSkillConfig(skill_id, skill_level)
	if xiuxian_cfg then
        self.node_list.skill_name.text.text = xiuxian_cfg.skill_name
        self.node_list.skill_desc.text.text = SkillWGData.Instance:GetSKillDescBySkillId(skill_id)
        -- self.node_list.skill_cd.text.text = string.format(Language.Skill.SkillCd, xiuxian_cfg.cd_s / 1000)
    else
        -- self.node_list.skill_name.text.text = Language.Cultivation.AngerSkillName
        self.node_list.skill_name.text.text = Language.Cultivation["AngerName"..self.cur_select_type_index]
        -- self.node_list.skill_cd.text.text = Language.Cultivation.AngerSkillCD
        self.node_list.skill_desc.text.text = Language.Cultivation.AngerDesc
	end

    
end

-- 刷新技能突破列表
function AngerSkillView:FlushSkillXiuWeiList()
    -- self.node_list.anger_sp_attr_list:CustomSetActive(self.cur_select_skill_index == Break_Skill_Type.BREAK_SKILL)
    -- self.node_list.anger_skill_attr_list:CustomSetActive(self.cur_select_skill_index ~= Break_Skill_Type.BREAK_SKILL)

    -- if self.cur_select_skill_index == Break_Skill_Type.BREAK_SKILL then
    --     local list = CultivationWGData.Instance:GetSkillXiuweiCache()
    --     self.anger_sp_attr_list:SetDataList(list)
    -- else
    --     local list = CultivationWGData.Instance:GetSkillImproveBySkillId(self.cur_select_skill_data)
    --     self.anger_skill_attr_list:SetDataList(list)
    -- end

    -- 用同一个
    self.node_list.anger_sp_attr_list:CustomSetActive(false)
    self.node_list.anger_skill_attr_list:CustomSetActive(true)
    local list = CultivationWGData.Instance:GetSkillImproveBySkillId(self.cur_select_skill_data)
    self.anger_skill_attr_list:SetDataList(list)
end

-- 刷新人物模型
function AngerSkillView:FlushPlaySkill(is_list_change)
    local skill_id = self.cur_select_skill_data
    if self.skill_pre_view:GetPreviewIsLoaded() then
		self.skill_pre_view:PlaySkill(skill_id, is_list_change)
	else
		self.skill_pre_view:SetPreviewLoadCb(function()
			self.skill_pre_view:PlaySkill(skill_id, is_list_change)
		end)
	end
end
------------------------------------------------------------------------------
-- 幻化
function AngerSkillView:OnClickTransform()
    if not self.cur_select_type_index then
        return
    end

    local nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
    if nuqi_type == self.cur_select_type_index then
        return
    end

    CultivationWGCtrl.Instance:ChooseAngerType(self.cur_select_type_index)
end

-- 激活
function AngerSkillView:OnClickActive()
    if not self.cur_select_type_index then
        return
    end

    local act_id = CultivationWGData.Instance:GetAngerLevel(self.cur_select_type_index)
    if act_id ~= 0  then
        CultivationWGCtrl.Instance:ActiveAngerType(self.cur_select_type_index)
    else
        local cfg = CultivationWGData.Instance:GetActiveCfgByType(self.cur_select_type_index)
        if cfg then
            local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
            if item_num >= cfg.item_num then
                CultivationWGCtrl.Instance:ActiveAngerType(self.cur_select_type_index)
            else
                TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = cfg.item_id })
            end
        end
    end
end
------------------------------------------------------------------------------
----------------------------------页签列表-----------------------