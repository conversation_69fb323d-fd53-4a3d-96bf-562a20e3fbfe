require("game/serveractivity/xianlingguzhen/xianling_guzhen_wg_data")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_view")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_record")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_result")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_big_reward_tip")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_big_reward_notice")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_gailv")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_niudan")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_item")

require("game/serveractivity/xianlingguzhen/xianling_guzhen_ohdj_view")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_qmfl_view")
require("game/serveractivity/xianlingguzhen/xianling_guzhen_ohdj_reward_view")

XianLingGuZhenWGCtrl = XianLingGuZhenWGCtrl or BaseClass(BaseWGCtrl)

function XianLingGuZhenWGCtrl:__init()
	if XianLingGuZhenWGCtrl.Instance then
        error("[XianLingGuZhenWGCtrl]:Attempt to create singleton twice!")
	end
	XianLingGuZhenWGCtrl.Instance = self

	self.data = XianLingGuZhenWGData.New()
	self.view = XianLingGuZhenView.New(GuideModuleName.XianLingGuZhen)
	self.result_view = XianLingGuZhenResult.New()
	self.big_reward_tip = XianLingBigRewardTip.New()
	self.big_reward_notice = XianLingBigRewardNotice.New()
	self.gailv_view = XianLingGuZhenGaiLv.New()
	self.niudan_view = XianLingGuZhenNiuDan.New()
	self.check_box_tip = Alert.New()

	self.ohdj_reward_view = XianLingGuZhenOHDJRewardView.New()
	self.ohdj_view = XianLingGuZhenOHDJView.New()
	self.qmfl_view = XianLingGuZhenQMFLView.New()
	self:RegisterAllProtocals()

end

function XianLingGuZhenWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.big_reward_tip then
		self.big_reward_tip:DeleteMe()
		self.big_reward_tip = nil
	end

	if self.big_reward_notice then
		self.big_reward_notice:DeleteMe()
		self.big_reward_notice = nil
	end

	if self.niudan_view then
		self.niudan_view:DeleteMe()
		self.niudan_view = nil
	end

	if self.result_view then
		self.result_view:DeleteMe()
		self.result_view = nil
	end

	if self.result_view then
		self.result_view:DeleteMe()
		self.result_view = nil
	end

	if self.check_box_tip then
		self.check_box_tip:DeleteMe()
		self.check_box_tip = nil
	end

	if self.ohdj_view then
		self.ohdj_view:DeleteMe()
		self.ohdj_view = nil
	end

	if self.ohdj_reward_view then
		self.ohdj_reward_view:DeleteMe()
		self.ohdj_reward_view = nil
	end

	if self.qmfl_view then
		self.qmfl_view:DeleteMe()
		self.qmfl_view = nil
	end

	XianLingGuZhenWGCtrl.Instance = nil
end

function XianLingGuZhenWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCNewLingXiMangTuTaskInfo, "OnSCNewLingXiMangTuTaskInfo")
	self:RegisterProtocol(SCNewLingXiMangTuAllInfo, "OnSCNewLingXiMangTuAllInfo")
	self:RegisterProtocol(SCNewLingXiMangTuDrawRecord, "OnSCNewLingXiMangTuDrawRecord")
	self:RegisterProtocol(SCNewLingXiMangTuSpecialRewardFetch,"OnSCNewLingXiMangTuSpecialRewardFetch")
	self:RegisterProtocol(SCNewLingXiMangTuReward,"OnSCNewLingXiMangTuReward")
	self:RegisterProtocol(SCNewLingXiMangTuServerDrawTimesInfo,"OnSCNewLingXiMangTuServerDrawTimesInfo")
	self:RegisterProtocol(SCNewLingxiMangTuDailyRewardInfo,"OnSCNewLingxiMangTuDailyRewardInfo")
end

function XianLingGuZhenWGCtrl:SendXianLingRollReq(opera_type, param_1, param_2, param_3)
	if opera_type == NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_ADD_REWARD_COUNT then
		if self.view:IsOpen() then
			self.view:Flush()
		end
	end

	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
  	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN
  	protocol.opera_type = opera_type or 0
  	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
  	protocol:EncodeAndSend()
end

function XianLingGuZhenWGCtrl:OnSCNewLingXiMangTuTaskInfo(protocol)
	self.data:OnSCNewLingXiMangTuTaskInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.XianLing_GuZhen_DRAW)
end

function XianLingGuZhenWGCtrl:OpenBigRewardTip(data)
	if self.big_reward_tip then
		self.big_reward_tip:SetData(data)
		self.big_reward_tip:Open()
	end
end

function XianLingGuZhenWGCtrl:OpenBigRewardNotice(data)
	if self.big_reward_notice then
		self.big_reward_notice:SetData(data)
		self.big_reward_notice:Open()
	end
end

function XianLingGuZhenWGCtrl:OnSCNewLingXiMangTuAllInfo(protocol)
	self.data:OnSCNewLingXiMangTuAllInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end

	if self.result_view:IsOpen() and protocol.is_draw_flag == 1 then
		self.result_view:Flush()
	end
end

function XianLingGuZhenWGCtrl:OpenGaiLvShowView(info)
	self.gailv_view:SetDataAndOpen(info)
end

function XianLingGuZhenWGCtrl:OnSCNewLingXiMangTuDrawRecord(protocol)
	self.data:OnSCNewLingXiMangTuDrawRecord(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function XianLingGuZhenWGCtrl:OnSCNewLingXiMangTuSpecialRewardFetch(protocol)
	if protocol.name == "" then
		return
	end

	self.data:SaveLastBigReward(protocol)
	XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_RECORD)
	if self.view:IsOpen() then
		self.view:FlushBigReward()
	end

	if self.data:GetNolongerTipFlag() then
		return
	end

	-- 添加弹出等级限制
	local other_cfg = XianLingGuZhenWGData.Instance:GetOtherCfg()
	local role_level = RoleWGData.Instance:GetAttr('level')
	if role_level < other_cfg.big_reward_tips_limited then
		return
	end

	if self.niudan_view:IsOpen() then
		self.niudan_view:SetData(protocol)
		self.niudan_view:PlayAnim()
	else
		self.niudan_view:SetData(protocol)
		self.niudan_view:Open()
	end
end

function XianLingGuZhenWGCtrl:OpenBigReward(info)
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	local active_show = scene_cfg and scene_cfg.is_show_lxmt_tip == 1

 	if active_show and self.data:IsOpenLingXiChuanWen() and info and info.name ~= "" then
		self:OpenBigRewardTip(info)
	elseif self.data:IsOpenLingXiChuanWen() and active_show then
		self:OpenBigRewardNotice(info)
	end
end

function XianLingGuZhenWGCtrl:OpenCheckAlertTips(text_dec, ok_fun, check_string,chect_text_str)
	self.check_box_tip:SetLableString(text_dec)
	self.check_box_tip:SetOkFunc(ok_fun)
	self.check_box_tip:SetCheckBoxDefaultSelect(false)
	self.check_box_tip:SetShowCheckBox(true)
	self.check_box_tip:SetCurLoginNoRemindKey(check_string)
	if chect_text_str and chect_text_str ~= "" then
		self.check_box_tip:SetCheckBoxText(chect_text_str)
	end
	self.check_box_tip:Open()
end

function XianLingGuZhenWGCtrl:ShowClickAddTip(str)
	if self.view and self.view:IsOpen() then
		TipWGCtrl.Instance:OpenConfirmAlertTips(str)
	end
end

--抽奖奖励返回
function XianLingGuZhenWGCtrl:OnSCNewLingXiMangTuReward(protocol)
	XianLingGuZhenWGData.Instance:SetXianLingResultRewardDataList(protocol)

	local reward_item_list = protocol.reward_item_list
	local is_show_big_reward = false
	local big_reward_id = 0
	if not IsEmptyTable(reward_item_list) then
		local open_day_cfg = XianLingGuZhenWGData.Instance:GetCurOpenDayCfg()
		if open_day_cfg and open_day_cfg.big_reward then
			big_reward_id = open_day_cfg.big_reward.item_id
		end
		for key, value in pairs(reward_item_list) do
			if value.item_id == big_reward_id then
				is_show_big_reward = true
				break
			end
		end
	end

	if not self.result_view:IsOpen() then
		self.result_view:Open()
	end

	if big_reward_id > 0 and is_show_big_reward then
		local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		self:OpenBigRewardTip({
			name = main_role_vo.name or '',
			item_id = big_reward_id
		})
	end
end

function XianLingGuZhenWGCtrl:OnSCNewLingXiMangTuServerDrawTimesInfo(protocol)
	self.data:SetNewLingXiMangTuServerDrawTimesInfo(protocol)
	RemindManager.Instance:Fire(RemindName.XianLingGuZhen_QMLJ)

	if self.qmfl_view and self.qmfl_view:IsOpen() then
		self.qmfl_view:Flush()
	end
end

function XianLingGuZhenWGCtrl:OnSCNewLingxiMangTuDailyRewardInfo(protocol)
	self.data:SetNewLingxiMangTuDailyRewardInfo(protocol)
	RemindManager.Instance:Fire(RemindName.XianLing_GuZhen_DRAW)
	
	if self.qmfl_view and self.qmfl_view:IsOpen() then
		self.qmfl_view:Flush()
	end
end

-- 获得终极大奖
function XianLingGuZhenWGCtrl:OnGetSpecailBigReward(protocol)
	if self.data:GetNoOHDJTipFlag() then
		return
	end

	self.ohdj_reward_view:Open()
end

function XianLingGuZhenWGCtrl:CheckActGrade()
	self.data:CheckGrade()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function XianLingGuZhenWGCtrl:OpenOHDJView()
	if self.ohdj_view then
		self.ohdj_view:Open()
	end
end

function XianLingGuZhenWGCtrl:OpenQMFLView()
	if self.qmfl_view then
		self.qmfl_view:Open()
	end
end

function XianLingGuZhenWGCtrl:FlushQMFLView()
	if self.qmfl_view and self.qmfl_view:IsOpen() then
		self.qmfl_view:Flush()
	end
end