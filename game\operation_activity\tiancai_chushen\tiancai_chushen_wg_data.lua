ChuShenWGData = ChuShenWGData or BaseClass()

function ChuShenWGData:__init()
	if ChuShenWGData.Instance then
		ErrorLog("[ChuShenWGData] Attemp to create a singleton twice !")
	end

	ChuShenWGData.Instance = self
	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_CHUSHEN, {[1] = OPERATION_EVENT_TYPE.LEVEL},
															BindTool.Bind(self.ChunShenOpenLimit, self), BindTool.Bind(self.IsShowChuShenRedPoint, self))
	RemindManager.Instance:Register(RemindName.OperationChuShen, BindTool.Bind(self.IsShowChuShenRedPoint, self))


	-- self.chushen_item_data_event = BindTool.Bind1(self.ChuShenItemDataChange, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.chushen_item_data_event)

	self.ani_mark = 0
	self.chushen_rank_data_list = {}
	self.chushen_add_list = {}
	self.menu_list = {}
	self.oa_index = 1
	self.unlock_all = 0

	self:LoadConfig()
end

function ChuShenWGData:LoadConfig()
	self.chushen_cfg = ConfigManager.Instance:GetAutoConfig("oa_tiancaichushen_auto")
	self.food_list = ListToMapList(self.chushen_cfg.show_food, "oa_index")
	self.food_menu_list = ListToMapList(self.chushen_cfg.food_menu, "oa_index")
	self.interface = ListToMap(self.chushen_cfg.interface, "oa_index")

	--ItemWGData.Instance:UnNotifyDataChangeCallBack(self.chushen_item_data_event)
end

function ChuShenWGData:__delete()
	ChuShenWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.OperationChuShen)
	self.chushen_rank_data_list = nil
	self.oa_index = nil
	self.unlock_all = nil
	self.menu_list = nil
	self.chushen_add_list = nil
	self.ani_mark = nil

	if self.chushen_item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.chushen_item_data_event)
		self.chushen_item_data_event = nil
	end
end

--厨神排行榜
function ChuShenWGData:ChuShenRankData(protocol)
	for k,v in pairs(protocol.rank_list) do
		AvatarManager.Instance:SetAvatarKey(v.uid, v.avatar_key_big, v.avatar_key_small)
	end
	self.chushen_rank_data_list = protocol.rank_list
end

function ChuShenWGData:GetChuShenRankData()
	return self.chushen_rank_data_list or {}
end

function ChuShenWGData:IsOtherRoleCreat(menu_id)
	if IsEmptyTable(self.chushen_rank_data_list) then
		return false
	end

	for k,v in pairs(self.chushen_rank_data_list) do
		if v.menu_id == menu_id then
			return true
		end
	end
	return false
end

--厨神信息
function ChuShenWGData:ChuShenActInfoData(protocol)
	self.oa_index = protocol.oa_index				--当前配置索引
	self.unlock_all = protocol.unlock_all			--全部菜谱的解锁标记 0 == 未解锁 1 == 已解锁未领取 2 == 已解锁已领取
	self.ani_mark = protocol.ani_mark
end

--获取厨神信息
function ChuShenWGData:GetChuShenActInfoData()
	return self.oa_index, self.unlock_all, self.ani_mark 
end

function ChuShenWGData:GetPreviousRedPoint()
	return self.unlock_all == 1, self.unlock_all == 2
end

--天才厨神菜谱信息返回
function ChuShenWGData:ChuShenFoodInfoData(protocol)
	self.menu_list = protocol.menu_list
	self:ClearCurFoodMenuList()
	self:GetCurFoodMenuList()
end

--活动关闭重置所有活动数据
function ChuShenWGData:ResertAllActInfo()
	self.menu_list = {}
	self.oa_index = 1				--当前配置索引
	self.unlock_all = 0			--全部菜谱的解锁标记 0 == 未解锁 1 == 已解锁未领取 2 == 已解锁已领取
	self.ani_mark = 0
	self.chushen_rank_data_list = {}
end

function ChuShenWGData:GetChuShenFoodInfoData()
	return self.menu_list
end

function ChuShenWGData:GetChuShenFoodInfoById(id)
	return self.menu_list[id]
end

function ChuShenWGData:CheckMenuIsUnlock(id)
	local temp_info = self:GetChuShenFoodInfoById(id)
	if IsEmptyTable(temp_info) or nil == id then
		return false,nil
	end

	return temp_info.state == 1 , temp_info
end

function ChuShenWGData:GetActivityCanOpenDay()
	local is_open = false
    local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_CHUSHEN)
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_CHUSHEN)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
     
	for k,v in pairs(self:GetChuShenParamCfg()) do
		if v.start_server_day <= open_day and open_day < v.end_server_day and week == v.week_index then
			is_open =true
		end
	end
	return is_open
end
--

function ChuShenWGData:ChunShenOpenLimit()
    if not self:GetActivityCanOpenDay() then
        return false
    end
	local cfg = self.chushen_cfg.config_param
	local vo = GameVoManager.Instance:GetMainRoleVo()
	for k,v in pairs(cfg) do
        if v.oa_index == self.oa_index then
			return vo.level >= v.level_limit, v
		end
    end
	return false, nil
end

function ChuShenWGData:IsShowChuShenRedPoint()
	local is_open = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_CHUSHEN)
	if not is_open then
		return 0
	end

	if self.unlock_all == 1 then
		return 1
	end

	for k,v in pairs(self.menu_list) do
		if v.state == 1 and v.is_remind == 1 then
			local base_cfg = self:GetOneMenuCfg(v.id)
			if base_cfg and base_cfg.limit_count > v.use_count then
				local is_enough = self:OperateChuShenMenuList(base_cfg)
				if is_enough then
					return 1
				end
			end
		end
	end
	return 0
end

function ChuShenWGData:OperateChuShenMenuList(check_data)
 	local data_list = check_data.consume_item
	local temp_list = {}
 	if #data_list <= 1 then
 		local is_niu_za = self:ChuShenAutoMenu()
 		return is_niu_za or false
 	end
 	
 	for i=1,3 do
 		local num = ItemWGData.Instance:GetItemNumInBagById(data_list[i -1].item_id)
 		temp_list[i] = {}
	 	temp_list[i].item_id = data_list[i -1].item_id

 		if i == 1 then
	 		temp_list[i].num = num >= data_list[0].num and num or 0
	 	elseif i == 2 then
			if data_list[1].item_id == data_list[0].item_id then
				local all_num = data_list[1].num + data_list[0].num
				temp_list[i].num = num >= all_num and num or 0
			else
				temp_list[i].num = num >= data_list[1].num and num or 0
			end

	 	else
	 		if (data_list[2].item_id == data_list[0].item_id and data_list[2].item_id ~= data_list[1].item_id) 
	 			or  (data_list[2].item_id == data_list[1].item_id and data_list[2].item_id ~= data_list[0].item_id)  then
	 			local all_num = data_list[1].num + data_list[0].num
				temp_list[i].num = num >= all_num and num or 0
			elseif data_list[2].item_id == data_list[0].item_id and data_list[2].item_id == data_list[1].item_id then
				local all_num = data_list[1].num + data_list[0].num + data_list[2].num 
				temp_list[i].num = num >= all_num and num or 0
			else
				temp_list[i].num = num >= data_list[0].num and num or 0
			end
	 	end
 	end

 	local num_count = 0
 	for k,v in pairs(temp_list) do
 		if v.num > 0 then
 			num_count = num_count + 1
 		end
 	end

 	return num_count >= 3
 end

--牛杂红点 只要满足1付费2个免费就成立
 function ChuShenWGData:ChuShenAutoMenu()
	local fufei_id_cfg, menu_list = ChuShenWGData.Instance:GetMenuListCfg()
	local fufei_num = 0
	local auto_shicai = {}
	local free_material_num = 0
	for k,v in pairs(fufei_id_cfg) do
		local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
		if v.is_special ~= 1 then
			free_material_num = free_material_num + num
		else
			fufei_num = fufei_num + num
		end
	end

	if fufei_num > 1 and free_material_num > 2 then
		return true
	end
	return false
end


function ChuShenWGData:GetMenuListCfg()
	return self.food_list[self.oa_index] or {}, self.food_menu_list[self.oa_index] or {}
end

function ChuShenWGData:GetCurFoodMenuList()
	if self.cur_food_menu_list == nil then
		local menu_list_cfg = self.food_menu_list[self.oa_index] or {}
		self.cur_food_menu_list = {}
		if not IsEmptyTable(menu_list_cfg) then
			for k,v in pairs(menu_list_cfg) do
				local data = {}
				data.cfg = v
				local is_lock,info = self:CheckMenuIsUnlock(v.id)
				data.state = is_lock and 1 or 0
				data.info = info
				table.insert(self.cur_food_menu_list,data)
			end
		end
		if not IsEmptyTable(self.cur_food_menu_list) then
			table.sort(self.cur_food_menu_list,function (a,b)
				if a and b then
					if a.state == b.state then
						if a.cfg and b.cfg and a.cfg.menu_quality and b.cfg.menu_quality then
							return a.cfg.menu_quality > b.cfg.menu_quality
						end
					else
						return a.state > b.state
					end
				else
					return false
				end

			end)
		end
	end
	return self.cur_food_menu_list
end

function ChuShenWGData:ClearCurFoodMenuList()
	self.cur_food_menu_list = nil
end

function ChuShenWGData:GetPartiAsesst(color)
 	local parti_name = BaseCell_Ui_Circle_Effect[color]
 	if parti_name then
 		return ResPath.GetWuPinKuangEffectUi(parti_name)
 	end
end

--获取食谱配置
function ChuShenWGData:GetOneMenuCfg(id)
	local menu_list = self.food_menu_list[self.oa_index]
	if menu_list then
		for k,v in pairs(menu_list) do
			if v.id == id then
				return v
			end
		end
	end
end

--厨神配置other
function ChuShenWGData:GetChuShenOtherCfg()
	return self.interface[self.oa_index]
end

function ChuShenWGData:GetChuShenParamCfg()
	return self.chushen_cfg.config_param or {}
end
--
function ChuShenWGData:SetChuShenSelectData(data_list)
	self.chushen_add_list = data_list
end

function ChuShenWGData:SetPanelAddMark(is_add)
	self.add_flag = is_add
end

function ChuShenWGData:GetPanelAddMark()
	return self.add_flag or false
end

function ChuShenWGData:GetChuShenSelectData()
	local real_num = 0
	for k,v in pairs(self.chushen_add_list) do
		if v then
			real_num = real_num + 1
		end
	end
	return self.chushen_add_list or {}, real_num
end

function ChuShenWGData:GetLastNum(item_id)
	local num = 0
	if self.chushen_add_list.id and self.chushen_add_list.id > 0 then
		return num
	end

	for k,v in pairs(self.chushen_add_list) do
		if v.item_id == item_id then
			num = num +1
		end
	end
	return num
end

function ChuShenWGData:ResultData(protocol)
	self.cook_type = protocol.cook_type
	self.menu_id = protocol.id
	self.result_reward_list = protocol.reware_list
	self.unlock_reware_list = protocol.unlock_reware_list
end 

function ChuShenWGData:GetResultData()
	return self.cook_type, self.menu_id
end

function ChuShenWGData:GetAllUnlockNum()
	local num = 0
	for k,v in pairs(self.menu_list) do
		if v.state == 1 then
			num = num + 1
		end
	end
	return num
end

function ChuShenWGData:ChatHelp(id, role_id)
	if not id and id <= 0 then
		return 
	end

	local is_open = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_CHUSHEN)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenHelActNoOpen)
		return
	end

	local level_limit = self:ChunShenOpenLimit()
	if not level_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenHelAnwserLevelLimit)
		return
	end

	-- local vo = GameVoManager.Instance:GetMainRoleVo()
	-- if role_id == vo.role_id then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenHelAnwserNoActive)
	-- 	return
	-- end

	local temp_info = self:GetChuShenFoodInfoById(id)
	if not IsEmptyTable(temp_info) and temp_info.state == 1 then
		local cfg = self:GetOneMenuCfg(id)
		if cfg and cfg.menu_name then
			local color = ITEM_COLOR[cfg.menu_quality]
			local content = string.format(Language.Activity.ChuShenHelAnwser, color, cfg.menu_name, color, cfg.food_name) 
			ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD, content, CHAT_CONTENT_TYPE.TEXT, nil, nil, true)
			return
		end
	end

	SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenHelAnwserNoActive)
end

function ChuShenWGData:ChunShenChuanWen()
	local level_limit = self:ChunShenOpenLimit()
	return level_limit or false
end

--5个食材监听改变
function ChuShenWGData:ChuShenItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local is_open = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_CHUSHEN)
	if not is_open then
		return 
	end

	local cfg = self.food_list[self.oa_index]
	for k,v in pairs(cfg) do
		if v.item_id == change_item_id then
			OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_chushen)
			RemindManager.Instance:Fire(RemindName.OperationChuShen)
			return
		end
	end
end

--本地保存今天提醒状态（用于remind局部单日提醒）
function ChuShenWGData:ChuShenUnlockRemindMark()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()  
	local name = uid.."ChuShen"
	PlayerPrefsUtil.SetString(name, cur_day)
end

--今日是否提醒过
function ChuShenWGData:ChuShenRemindTodayBtn()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local name = uid.."ChuShen"
	local remind_day = PlayerPrefsUtil.GetString(name) or 0
	return tonumber(remind_day) ~= cur_day
end
