TianShenView = TianShenView or BaseClass()

local TOGGLE_MAX = 5
function TianShenView:ShenQiLoadCallBack()
    self.shenqi_series = 0
    self.select_shenqi_index = 1
    self.shenqi_zhu_skill = {}

	if not self.sq_model then
		self.sq_model = RoleModel.New()
		self.sq_model:SetUISceneModel(self.node_list["sq_model"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.sq_model, TabIndex.tianshen_shenQi)
	end

    XUI.AddClickEventListener(self.node_list["btn_shenqi_shengxing"], BindTool.Bind(self.OnClickShenQiShengXing, self))

    self.node_list.btn_operate.button:AddClickListener(BindTool.Bind(self.ShengQiOperate,self))
    self.node_list.shenqi_skill_btn.button:AddClickListener(BindTool.Bind1(self.OnClickShenQiSkill,self))
    self.item_cell = ItemCell.New(self.node_list.shenqi_cell)

    if nil == self.shenqi_uplevel_attr_list then
        self.shenqi_uplevel_attr_list = {}
        local attr_num = self.node_list.shenqi_attr.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(self.node_list.shenqi_attr:FindObj("shenqi_attr_" .. i))
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            self.shenqi_uplevel_attr_list[i] = cell
        end
    end

    self:FlushShenQiAccordionTable()
    self:CreateTianShenShenQiAccordion()
end

function TianShenView:ShengQiReleaseCallBack()
    if nil ~= self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    if self.sq_model then
        self.sq_model:DeleteMe()
        self.sq_model = nil
    end

    if self.cell_list then
        for k,v in pairs(self.cell_list) do
            if v then
                for _,j in pairs(v) do
                    if j then
                        j:DeleteMe()
                    end
                end
            end
        end
        self.cell_list = nil
    end

    self.asset_cache = nil
    self.selectet_tree_item = nil
    self.selectet_item = nil
    self.shenqi_series = 0
    self.ts_sq_accor_index = nil
    self.shenqi_load_complete_list = nil
    self.shenqi_need_call_list = nil
    self.old_select_shenqi_info = nil
    self.old_select_shenqi_index = nil

    if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil
    end
    self.shenqi_move_loop = false

    if self.shenqi_uplevel_attr_list then
        for k,v in pairs(self.shenqi_uplevel_attr_list) do
            v:DeleteMe()
        end
        self.shenqi_uplevel_attr_list = nil
    end

    self.jump_shenqi_index = nil

    if not self.tianshen_shenqi_cell_list then return end

	for i, v in pairs(self.tianshen_shenqi_cell_list) do
		for ii, vv in ipairs(v) do
			vv:DeleteMe()
		end
	end

    self.tianshen_shenqi_cell_list = nil
	self.tianshen_shenqi_list = nil
    self.shenqi_list_loaded = nil
    self.shenqi_list_index = nil
    self.shenqi_list_series = nil
end

function TianShenView:FlushShenQiAccordionTable()
	local data_list = self:GetShenQiTypeListVo()
	for i = 1, TOGGLE_MAX do
		-- 如果里面的子成就都没开启  则隐藏大标签
		local accor_data = TianShenWGData.Instance:GetShenQiBySeries(data_list[i].series)
		local show_flag = accor_data and #accor_data > 0

        self.node_list["sq_SelectBtn_" .. i]:SetActive(show_flag)
	end
end

function TianShenView:GetShenQiTypeListVo()
    if IsEmptyTable(self.shenqi_type_vo_list) then
        for i = 1, 5 do
            local vo = {}
            vo.name = string.format(Language.TianShen.TianShenSeriesColor[i], Language.TianShen.TianShenSeriesColorStr[i])
            vo.series = i
            self.shenqi_type_vo_list[i] = vo
        end
    end
    return self.shenqi_type_vo_list
end

-- 创建扩展列表
function TianShenView:CreateTianShenShenQiAccordion()
	local data_list = self:GetShenQiTypeListVo()
	self.tianshen_shenqi_list = {}
    self.tianshen_shenqi_cell_list = {}
    self.jump_defult_shenqiqi_index = true

	for i = 1, TOGGLE_MAX do
        self.tianshen_shenqi_list[i] = {}
        local parent = self.node_list[string.format("sq_SelectBtn_%d", i)].transform
		self.tianshen_shenqi_list[i].text_name = self:GetTianShenShenQiNodeList(parent, "noraml/text_btn")
		self.tianshen_shenqi_list[i].text_high_name = self:GetTianShenShenQiNodeList(parent, "Image_hl/text_high_btn")
		self.tianshen_shenqi_list[i].select_btn = self.node_list[string.format("sq_SelectBtn_%d", i)]--按钮
		self.tianshen_shenqi_list[i].select_btn.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTianShenShenQiTypeItem, self, i))
		self.tianshen_shenqi_list[i].list =  self.node_list[string.format("sq_List_%d", i)]--列表
        self.tianshen_shenqi_list[i].red_dot = self:GetTianShenShenQiNodeList(parent, "img_read") --红点
		self.tianshen_shenqi_list[i].list_item_cell = {}

		local color = i + 2
		local bundle, asset = ResPath.GetCommonButton(string.format("a3_sz_btn_%d", color))
		local normal_bg = self:GetActivationNodeList(parent, "noraml")
		self.tianshen_shenqi_list[i].normal_bg = normal_bg
		if normal_bg then
			normal_bg.image:LoadSprite(bundle, asset)
		end
		
		local select_bg = self:GetActivationNodeList(parent, "Image_hl")
		self.tianshen_shenqi_list[i].select_bg = select_bg
		if select_bg then
			select_bg.image:LoadSprite(bundle, asset)
		end

		local btn_str = Language.TianShen.TianShenSeriesColorStr[i] --string.format(Language.TianShen.TianShenSeriesColor[i], Language.TianShen.TianShenSeriesColorStr[i])
		local color_str = btn_str --ToColorStr(btn_str, color)

		local accor_data = nil
		if nil ~= data_list[i] then
			self.tianshen_shenqi_list[i].text_name.text.text = btn_str
			self.tianshen_shenqi_list[i].text_high_name.text.text = btn_str
			accor_data = data_list[i]
		end

        if self.jump_defult_shenqiqi_index then
            self.defult_shenqi_list_series = 1
            local defult_data_list = TianShenWGData.Instance:GetShenQiBySeries(accor_data.series)
            if not IsEmptyTable(defult_data_list) then
                self.defult_shenqi_list_series = accor_data.series
                self.jump_defult_shenqiqi_index = false
            end
        end
        
		self:LoadTianShenShenQiCell(i, accor_data)
	end
end

-- 获取节点
function TianShenView:GetTianShenShenQiNodeList(parent, str)
    local obj = parent.transform:Find(str).gameObject
    local item = U3DObject(obj, obj.transform, self)
    return item
end

function TianShenView:LoadTianShenShenQiCell(index, accor_data)
	if nil == accor_data then
		return
	end

	local res_async_loader = AllocResAsyncLoader(self, "tianshen_shenqi_activation_item" .. index)
	res_async_loader:Load("uis/view/tianshen_prefab", "drop_down_render", nil,
		function(new_obj)
			local item_vo = {}
            local data_list = TianShenWGData.Instance:GetShenQiBySeries(accor_data.series)
			for i = 1, #data_list do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.tianshen_shenqi_list[index].list.transform, false)
				obj:GetComponent("Toggle").group = self.tianshen_shenqi_list[index].list.toggle_group
				self.tianshen_shenqi_list[index].list_item_cell[i] = obj
				local item_render = TianShenShenQiItemRender.New(obj)
				item_render:SetClickCallBack(BindTool.Bind(self.OnClickShengQiProductHandler, self), true)
				item_render:SetData(data_list[i]) --里层按钮赋值信息
				item_render.parent_view = self
				item_vo[i] = item_render
			end

			self.tianshen_shenqi_cell_list[index] = item_vo
            if index == TOGGLE_MAX then
				self.shenqi_list_loaded = true
				if self.shenqi_list_series and self.shenqi_list_index then
					self:ShenQiJumpToSelect(self.shenqi_list_series, self.shenqi_list_index)
					self.shenqi_list_index = nil
					self.shenqi_list_series = nil
                else
                    self:ShenQiJumpToSelect(self.defult_shenqi_list_series, 1)
				end
			end
		end
	)
end

function TianShenView:OpenShenQiCallBack()

end

function TianShenView:CloseShenQiCallBack()

end

function TianShenView:ShenQiShowIndexCallback()

end

function TianShenView:PlayEffectShenQi()
    local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tiansheng_huohua)
    self.node_list.effect_sq:ChangeAsset(bundle_name, asset_name, false)
end

function TianShenView:OnClickShenQiShengXing()
    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.selectet_tree_item.index)
    if not shenqi_info then return end
    local next_star_cfg = TianShenWGData.Instance:GetShenQiStarCfg(self.selectet_tree_item.index, shenqi_info.star + 1)
    if not next_star_cfg then return end
    local item_num = ItemWGData.Instance:GetItemNumInBagById(next_star_cfg.stuff_id)

    if item_num < next_star_cfg.stuff_num then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = next_star_cfg.stuff_id})
        TipWGCtrl.Instance:CloseOtherView(function ()
            self:Close()
            ViewManager.Instance:Close(GuideModuleName.TianShenView)
        end)
    else
        TianShenWGCtrl.Instance:SendTianShenShenqiOpReq(TianShenWGCtrl.ShenQi_Opera_Type.ShenQi_Opera_Type6, self.selectet_tree_item.index)
    end
end

function TianShenView:OnClickTianShenShenQiTypeItem(series, isOn)
	if series == nil or not isOn then
		return
	end

	local jump_sub_index = 1

    local data_list = TianShenWGData.Instance:GetShenQiBySeries(series)
    for k,v in pairs(data_list) do
        if (TianShenWGData.Instance:GetShenQiActivationRemind(v.index) > 0 or
        TianShenWGData.Instance:GetShenQiShenXingRemind(v.index) > 0) then
            jump_sub_index = k
            break
        end
    end

	self:ShenQiJumpToSelect(series, jump_sub_index, true)
	if self.tianshen_shenqi_cell_list and self.tianshen_shenqi_cell_list[series] and self.tianshen_shenqi_cell_list[series][jump_sub_index] then
		local cell = self.tianshen_shenqi_cell_list[series][jump_sub_index]
		self:OnClickShengQiProductHandler(cell)
	end
end

-- 跳转到对应的选择
function TianShenView:ShenQiJumpToSelect(series, index, is_cell)
	if not self.shenqi_list_loaded then
		self.shenqi_list_series = series
		self.shenqi_list_index = index
		return
	end

	if not self.tianshen_shenqi_list then 
        return end
    
	if self.tianshen_shenqi_list[series] and (not is_cell) then
		self.tianshen_shenqi_list[series].select_btn.toggle.isOn = true
	end

	if self.tianshen_shenqi_list[series].list_item_cell and self.tianshen_shenqi_list[series].list_item_cell[index] then
		local cell_toggle = self.tianshen_shenqi_list[series].list_item_cell[index]:GetComponent("Toggle")
		if cell_toggle then
			cell_toggle.isOn = true
		end
	end
end

function TianShenView:ShengXingItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.selectet_tree_item.index)
    if not shenqi_info then return end
    local next_star_cfg = TianShenWGData.Instance:GetShenQiStarCfg(self.selectet_tree_item.index, shenqi_info.star + 1)
    if not next_star_cfg or next_star_cfg.stuff_id ~= change_item_id then return end
    local item_num = ItemWGData.Instance:GetItemNumInBagById(next_star_cfg.stuff_id)
    local color = ITEM_NUM_COLOR.ENOUGH
    if item_num < next_star_cfg.stuff_num then
        color = ITEM_NUM_COLOR.NOT_ENOUGH
    end
    self.item_cell:SetRightBottomColorText(ToColorStr(item_num .. "/" .. next_star_cfg.stuff_num,color))
end

function TianShenView:OnBtnGoConsume(index)
    local other = TianShenWGData.Instance:GetShenQiOther()
    TipWGCtrl.Instance:OpenItem({item_id = other["show_item" .. index]})
end

function TianShenView:OnBtnConsume(index)
    local other = TianShenWGData.Instance:GetShenQiOther()
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = other["show_item" .. index]})
end

function TianShenView:ShenQiItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    --self:ShengXingItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    if not self.selectet_tree_item then return end
    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.selectet_tree_item.index)
    local state = TianShenWGData.Instance:IsShenQiActivation(self.selectet_tree_item.index) -- 激活状态

    local other = TianShenWGData.Instance:GetShenQiOther()
    local item_id = 0
    for i=1,3 do
        item_id = other["show_item" .. i]
        if item_id == change_item_id then
            self.node_list["text_consume" .. i].text.text = ItemWGData.Instance:GetItemNumInBagById(item_id)
            break
        end
    end

    if not state and self.selectet_tree_item.stuff_id == change_item_id then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(self.selectet_tree_item.stuff_id)
        local color = ITEM_NUM_COLOR.ENOUGH
        if item_num < self.selectet_tree_item.stuff_num then
            color = ITEM_NUM_COLOR.NOT_ENOUGH
        end
        self.item_cell:SetRightBottomColorText(item_num .. "/" .. self.selectet_tree_item.stuff_num,color)
        self.item_cell:SetRightBottomTextVisible(true)
    end
    if old_num ~= new_num then
        self:FlushShenQiAllRemind()
        self:UpdateCellList()
    end
end

function TianShenView:FlushShenQiSelect(param_t)
    for k,v in pairs(param_t) do
        if k == "sq_succes" then
            self:ShenQiEffectActive(v.id)
        end
    end

    local shenqi_index = param_t and param_t.all and param_t.all.to_ui_param
    shenqi_index = tonumber(shenqi_index)
    local jump_shenqi_index = shenqi_index or (self.selectet_tree_item and self.selectet_tree_item.index)
    local cur_list_index = 1
    local data_list = TianShenWGData.Instance:GetShenQiDataList()
    local select_shenqi_index = 0
    
    if jump_shenqi_index then
        for k,v in pairs(data_list) do
            if v.index == jump_shenqi_index then
                cur_list_index = k
                select_shenqi_index = v.index
                break
            end
        end
    end
    
    self:RefreshShenQiListData()
    self:ShenQiJumpToSelectForIndex(select_shenqi_index)
    self:OnShengQiFlush()
    self:FlushShenQiBtnRemind()
end

function TianShenView:ShenQiEffectActive(id)
    if id == self.selectet_tree_item.index then
        -- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tianshen_shengji)
        -- EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.sq_effect.transform, 1.2)
    end
end
--更新self.data_cache
function TianShenView:UpdateCellList()
    local cur_list_index = 1
    local select_shenqi_index = 0
    local data_list = TianShenWGData.Instance:GetShenQiDataList()
    for k,v in pairs(data_list) do
        if v.index == self.selectet_tree_item.index then
            cur_list_index = k
            select_shenqi_index = v.index
            break
        end
    end

    self:RefreshShenQiListData()
    self:ShenQiJumpToSelectForIndex(select_shenqi_index)
end

-- 跳转到对应对象
function TianShenView:ShenQiJumpToSelectForIndex(sq_index)
    local shenqi_cfg = TianShenWGData.Instance:GetShenQiByIndex(sq_index)
    local series = 1
    local jump_index = 1

    if not shenqi_cfg then
        return 1, 1
    end

    series = shenqi_cfg.series
    local shenqi_data_list = TianShenWGData.Instance:GetShenQiBySeries(series)

    for i, v in pairs(shenqi_data_list) do
        if v.index == sq_index then
            jump_index = i
        end
    end

    self:ShenQiJumpToSelect(series, jump_index)
end

--刷新列表
function TianShenView:RefreshShenQiListData()
	if nil == self.tianshen_shenqi_cell_list then
        return
    end

	for i, v in pairs(self.tianshen_shenqi_cell_list) do
        local data_list = TianShenWGData.Instance:GetShenQiBySeries(i)
        if not IsEmptyTable(v) and data_list then
            for ii, vv in pairs(v) do
                vv:SetData(data_list[ii])
            end
        end

        self.tianshen_shenqi_list[i].red_dot:SetActive(false)
        for j = 1, #data_list do
            local remind_index = (data_list[j] or {}).index or -1
            if (TianShenWGData.Instance:GetShenQiActivationRemind(remind_index) > 0 or TianShenWGData.Instance:GetShenQiShenXingRemind(remind_index) > 0) then
                self.tianshen_shenqi_list[i].red_dot:SetActive(true)
            end
        end
	end
end

--选择子项回调
function TianShenView:OnClickShengQiProductHandler(item)
    if nil ~= item and nil ~= item.data then
        self.selectet_tree_item = item.data
        self.selectet_item = item
        self.select_shenqi_index = item.data.index
        self:OnShengQiFlush()
        self:FlushShenQiBtnRemind()
    end
end

function TianShenView:GetShenQiSelectItem()
    return self.selectet_item
end

function TianShenView:ShengQiOperate()
    if not self.selectet_tree_item then return end
    local item_num = ItemWGData.Instance:GetItemNumInBagById(self.selectet_tree_item.stuff_id)
    if item_num < self.selectet_tree_item.stuff_num then
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.selectet_tree_item.stuff_id})
        return
    end

    TianShenWGCtrl.Instance:SendTianShenShenqiOpReq(TianShenWGCtrl.ShenQi_Opera_Type.ShenQi_Opera_Type1, self.selectet_tree_item.index)
end

function TianShenView:OnClickShenQiSkill()
    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.selectet_tree_item.index)
    local now_shenqi_cfg = TianShenWGData.Instance:GetShenQiStarCfg(self.selectet_tree_item.index, shenqi_info and shenqi_info.star or 0)
    local now_skill_level = now_shenqi_cfg.skill_level
    local skill_id = self.shenqi_zhu_skill[1].skill_id
    local skill_cfg = TianShenWGData.Instance:GetShenQiSkillCfg(now_shenqi_cfg.skill, now_skill_level)

    local next_skill_cfg = TianShenWGData.Instance:GetShenQiSkillCfg(now_shenqi_cfg.skill, now_skill_level +1)
    if skill_cfg then
        local body_text = ""
        local tab ={}
        local is_per = {}
        local list = {}
        local next_text = ""
        local show_next = 0
        local capability_inc = skill_cfg.capability_inc
        for k,v in pairs(skill_cfg) do
            if Language.Common.TipsAttrNameList[k] and v > 0 then
                is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(k)
                tab = {}
                tab.attr_name = Language.Common.TipsAttrNameList[k]
                if is_per then
                    tab.attr_value = "<color=#99ffbb>+" .. (v/100) .. "%</color>"
                    if next_skill_cfg and next_skill_cfg[k] then
                        show_next = 1
                        tab.next_value = "<color=#99ffbb>+" .. (next_skill_cfg[k]/100) .. "%</color>"
                    end
                else
                    tab.attr_value = "<color=#99ffbb>+" .. v .. "</color>"
                    if next_skill_cfg and next_skill_cfg[k] then
                        show_next = 1
                        tab.next_value = "<color=#99ffbb>+" .. next_skill_cfg[k] .. "%</color>"
                    end
                end
                tab.attr_type = k
                table.insert(list, tab)
            end  
        end

        body_text = skill_cfg.skill_des
        if list[1].next_value then
            next_text = next_skill_cfg.skill_des
        end

        local top_text = skill_cfg.skill_name
        local limit_text = ""
        if show_next == 1 and shenqi_info and shenqi_info.is_active then
            limit_text = string.format(Language.TianShen.ShenQiSkillTipLimite,self.selectet_tree_item.name,shenqi_info and shenqi_info.star + 1 or 1)
        elseif not shenqi_info or not shenqi_info.is_active then
            limit_text = string.format(Language.TianShen.ShenQiActiveOpen2,self.selectet_tree_item.name)
        else
            limit_text = Language.TianShen.ShenQiSkillIsMax
        end

        local show_data = {
            icon = skill_cfg.skill_icon,
            skill_id = now_shenqi_cfg.skill,
            top_text = top_text,
            body_text = body_text,
            skill_level = now_skill_level,
            is_lock = (not shenqi_info) or (not shenqi_info.is_active),
            x = 0,
            y = 0,
            show_bottom = 0,
            next_skill_desc = next_text,
            skill_box_type = SKILL_BOX_TYPE.TIANSHEN_WUQI,
            feishen_limit_text = limit_text,
            capability = capability_inc,
            set_pos2 = true,
        }
        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end

function TianShenView:OnShengQiFlush()
    if IsEmptyTable(self.selectet_tree_item) then
        return 
    end

    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.selectet_tree_item.index)
    self.node_list["shenqi_skill_lock"]:SetActive(true)
    self.node_list["shenqi_level_bg"]:SetActive(false)
    self.shenqi_zhu_skill = TianShenWGData.Instance:GetShenQiSkillInfo(self.selectet_tree_item.index,shenqi_info and shenqi_info.star or 0)
    if not self.old_select_shenqi_index or self.old_select_shenqi_index ~= self.selectet_tree_item.index then
        self.old_select_shenqi_info = {}
        for k,v in pairs(self.shenqi_zhu_skill) do
            self.old_select_shenqi_info[k] = self.shenqi_zhu_skill[k].is_active
        end
        self.old_select_shenqi_index = self.selectet_tree_item.index
        self.old_select_star =  shenqi_info and shenqi_info.star or 0

        if self.shenqi_zhu_skill[1].is_active then
            self.node_list["shenqi_skill_lock"]:SetActive(false)
            self.node_list["shenqi_level_bg"]:SetActive(true)
        end
    else
        self.old_select_shenqi_index = self.selectet_tree_item.index
        local chang_index = -1
        for k,v in pairs(self.old_select_shenqi_info) do
            if not v and self.shenqi_zhu_skill[k].is_active then
                self.old_select_shenqi_info[k] = self.shenqi_zhu_skill[k].is_active
                chang_index = k
            end
        end

        if self.shenqi_zhu_skill[1].is_active then
            self.node_list["shenqi_skill_lock"]:SetActive(false)
            self.node_list["shenqi_level_bg"]:SetActive(true)
        end

        local new_star =  shenqi_info and shenqi_info.star or 0
        if self.old_select_star ~= new_star then
            self.old_select_star = new_star
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, pos = Vector2(0, 0),
                parent_node = self.node_list["sq_effect"]})
        end

        -- if chang_index ~= -1 then
        --     -- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tianshen_jiesuo)
        --     EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.shenqi_skill_effect.transform, 2)
        -- end
    end

    local now_shenqi_cfg = TianShenWGData.Instance:GetShenQiStarCfg(self.selectet_tree_item.index, shenqi_info and shenqi_info.star or 0)
    local skill_cfg = TianShenWGData.Instance:GetShenQiSkillCfg(self.shenqi_zhu_skill[1].skill, now_shenqi_cfg.skill_level)
    local next_star_cfg = TianShenWGData.Instance:GetShenQiStarCfg(self.selectet_tree_item.index, shenqi_info and shenqi_info.star +1 or 1)

    if skill_cfg then
        local bundel, asset = ResPath.GetSkillIconById(skill_cfg.skill_icon)
        self.node_list["shenqi_skill_icon"].image:LoadSprite(bundel, asset, function ()
                self.node_list.shenqi_skill_icon.image:SetNativeSize()
            end)

        local item_cfg = {}
        if now_shenqi_cfg and now_shenqi_cfg.stuff_id > 0 then
            item_cfg = ItemWGData.Instance:GetItemConfig(now_shenqi_cfg.stuff_id)
        elseif next_star_cfg and next_star_cfg.stuff_id > 0 then
            item_cfg = ItemWGData.Instance:GetItemConfig(next_star_cfg.stuff_id)
        end

        local color = ITEM_COLOR[item_cfg.color or 3]
        if now_shenqi_cfg and now_shenqi_cfg.skill_level and self.node_list["shenqi_skill_level"] then
            self.node_list["shenqi_skill_level"].text.text = now_shenqi_cfg.skill_level
        end
        
		self.node_list.shenqi_skill_name.text.text = skill_cfg.skill_name
		self.node_list.shenqi_skill_desc.text.text = skill_cfg.skill_des
    end

    local state = TianShenWGData.Instance:IsShenQiActivation(self.selectet_tree_item.index) -- 激活状态
    if state then
        local flag = TianShenWGData.Instance:IsWaiGuanHuanHua(self.selectet_tree_item.index, 0)
        self.node_list.title_down.text.text = Language.Appearance.UpStarStuff
        self.node_list["btn_operate"]:SetActive(false)
        self.node_list.shenqi_cell:SetActive(true)
        self.node_list["btn_shenqi_shengxing"]:SetActive(true)
        self.node_list["shenqi_max_star"]:SetActive(false)
        if shenqi_info and shenqi_info.star then 
            local text_visible = true
            if not next_star_cfg then
                self.node_list["btn_shenqi_shengxing"]:SetActive(false)
                self.node_list["shenqi_max_star"]:SetActive(true)
                text_visible = false
                next_star_cfg = now_shenqi_cfg
            end

            if not next_star_cfg then
            else
                local item_num = ItemWGData.Instance:GetItemNumInBagById(next_star_cfg.stuff_id)  
                local color = ITEM_NUM_COLOR.ENOUGH
                if item_num < next_star_cfg.stuff_num then
                     color = ITEM_NUM_COLOR.NOT_ENOUGH
                end
                self.item_cell:SetData({item_id = next_star_cfg.stuff_id})
                self.item_cell:SetRightBottomColorText(item_num .. "/" .. next_star_cfg.stuff_num,color)
                self.item_cell:SetRightBottomTextVisible(text_visible)
            end
        end
    else
        self.node_list["btn_shenqi_shengxing"]:SetActive(false)
        self.node_list["shenqi_max_star"]:SetActive(false)
        self.node_list["btn_operate"]:SetActive(true)
        self.node_list.title_down.text.text = Language.Appearance.ActiveStuff
        self.node_list.shenqi_cell:SetActive(true)
        self.item_cell:SetData({item_id = self.selectet_tree_item.stuff_id})
        local item_num = ItemWGData.Instance:GetItemNumInBagById(self.selectet_tree_item.stuff_id)
        local color = ITEM_NUM_COLOR.ENOUGH
        if item_num < self.selectet_tree_item.stuff_num then
            color = ITEM_NUM_COLOR.NOT_ENOUGH
        end
        self.item_cell:SetRightBottomColorText(item_num .. "/" .. self.selectet_tree_item.stuff_num,color)
        self.item_cell:SetRightBottomTextVisible(true)
    end

	local ts_index = self.selectet_tree_item.sq_index
    local tianshen_img_cfg = TianShenWGData.Instance:GetTianShenCfg(ts_index)
    self.node_list.lbl_top_des.text.text = string.format(Language.TianShen.ShenQiOwnerDes, tianshen_img_cfg.bianshen_name) -- 专属描述

    self:FlushAttr()
    self:FlushShengQiModel()
end

function TianShenView:PlayShenQiEffect()
    -- body
    if not self.selectet_tree_item then return end
    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.selectet_tree_item.index)
    if not shenqi_info then return end
    local old_shenqi_info = TianShenWGData.Instance:GetTianShenShenQiOldInfoByIndex(self.selectet_tree_item.index)

    if not old_shenqi_info or (old_shenqi_info.is_active ~= shenqi_info.is_active) then
        TianShenWGData.Instance:SetOldTianShenShenQiInfoOne(self.selectet_tree_item.index)
    end
end

function TianShenView:FlushAttr()
    if not self.selectet_tree_item then return end

    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.selectet_tree_item.index)
    local star = shenqi_info and shenqi_info.star or 0
    local level = shenqi_info and shenqi_info.level or -1
    local num = math.floor(TIANSHEN_SHENQI_STAR_MAX / 2)
    local golden_star_num  = math.ceil(star / num)
    --local silver_star_num = star % num
    local is_active = TianShenWGData.Instance:IsShenQiActivation(self.selectet_tree_item.index)

    local attr_cfg =  TianShenWGData.Instance:GetShenQiStarCfg(self.selectet_tree_item.index, star)
    local next_attr_cfg = TianShenWGData.Instance:GetShenQiStarCfg(self.selectet_tree_item.index, star + 1)
    local now_attri_list = AttributeMgr.GetAttributteByClass(__TableCopy(attr_cfg))
    local next_attri_list
    local up_attri_list = AttributePool.AllocAttribute()
    local is_max_level = next_attr_cfg == nil
    if is_active and (not is_max_level) then
        next_attri_list = AttributeMgr.GetAttributteByClass(next_attr_cfg)
        up_attri_list = AttributeMgr.LerpAttributeAttr(now_attri_list, next_attri_list)
    end

    local need_show_attr_up_effect = false
    if nil ~= self.selectet_tree_item_index_cache and nil ~= self.selectet_tree_item_star_cache then
        if (self.selectet_tree_item_index_cache == self.selectet_tree_item.index) and (star - self.selectet_tree_item_star_cache == 1) then
            need_show_attr_up_effect = true
        end
    end

    self.selectet_tree_item_index_cache = self.selectet_tree_item.index
    self.selectet_tree_item_star_cache = star

    local attr_list = EquipWGData.GetSortAttrListHaveAddByCfg(now_attri_list, up_attri_list)
    for k, v in ipairs(self.shenqi_uplevel_attr_list) do
        v:SetData(attr_list[k])

        if need_show_attr_up_effect then
            v:PlayAttrValueUpEffect()
        end
    end

    local star_res_list_2 = GetStarImgResByStar(star)
	for i = 1, GameEnum.ITEM_MAX_STAR do
		self.node_list["ts_sq_star" .. i]:SetActive(true)
		self.node_list["ts_sq_star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list_2[i]))
	end

    if shenqi_info then
        self.node_list["text_shenqi_zhanli"].text.text = TianShenWGData.Instance:GetShenQiZhanLi(self.selectet_tree_item.index)
    else
        self.node_list["text_shenqi_zhanli"].text.text = TianShenWGData.Instance:GetPreViewCap(self.selectet_tree_item.index)
    end

end

function TianShenView:FlushShengQiModel()
    local bundle,asset = ResPath.GetTianShenShenQiPath(self.selectet_tree_item.ts_res_idui)
    if self.asset_cache ~= asset then
        self.asset_cache = asset
        self.sq_model:SetMainAsset(bundle, asset)
    end
end

function TianShenView:PlayWeaponTween()
    if not self.shenqi_move_loop then
        UITween.MoveLoop(self.node_list["sq_model"].gameObject, u3dpool.vec3(0, 0, 0), u3dpool.vec3(0, 15, 0), 1)
        self.shenqi_move_loop = true
    end
end


function TianShenView:OnClickSQUse()
    if not self.selectet_tree_item then return end
    local flag = TianShenWGData.Instance:IsWaiGuanHuanHua(self.selectet_tree_item.index, 0)
    if flag then
        TianShenWGCtrl.Instance:SendTianShenShenqiOpReq(TianShenWGCtrl.ShenQi_Opera_Type.ShenQi_Opera_Type5, self.selectet_tree_item.index, -1)
    else
        TianShenWGCtrl.Instance:SendTianShenShenqiOpReq(TianShenWGCtrl.ShenQi_Opera_Type.ShenQi_Opera_Type5, self.selectet_tree_item.index,0)
        local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jinjietexiao_two)
        EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_model"].transform)
    end

end 

function TianShenView:OnClickYiHuanHua()

    SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.ShenQiYiHuanHua)
end

-- 刷新所有红点
function TianShenView:FlushShenQiAllRemind()
    -- body
    self:FlushShenQiSeriesRemind()
    self:FlushShenQiBtnRemind()
end

function TianShenView:FlushShenQiSeriesRemind()
    -- body
    local list = TianShenWGData.Instance:TianShenShenQiSeriesListRemindWithoutInvateTip()
    for i=0,#list do
        if self.node_list["shenqi_red" .. i] then
            self.node_list["shenqi_red" .. i]:SetActive(1 == list[i])
        end
    end
end

function TianShenView:FlushShenQiBtnRemind()
    if not self.selectet_tree_item then return end
    local flag = TianShenWGData.Instance:GetShenQiActivationRemind(self.selectet_tree_item.index)
    self.node_list.operate_red:SetActive(1 == flag)
    flag = TianShenWGData.Instance:GetShenQiShenXingRemind(self.selectet_tree_item.index)
    self.node_list.shengxing_btn_red:SetActive(1 == flag)
end

-------------------------------------------------------------------------
---  TianShenShenQiItemRender
-------------------------------------------------------------------------

TianShenShenQiItemRender = TianShenShenQiItemRender or BaseClass(BaseRender)
function TianShenShenQiItemRender:__delete( ... )
    self.parent_view = nil
    self.star_type = nil
end

function TianShenShenQiItemRender:LoadCallBack( )
    self.star_type = nil
end

function TianShenShenQiItemRender:ChangeStarType(type)
    self.star_type = type
end

function TianShenShenQiItemRender:OnFlush()
    if not self.data then
        return
    end

	local color = self.data.series + 2
	self.node_list.tianshen_name.text.text = ToColorStr(self.data.name, ITEM_COLOR[color])
    self.node_list.tianshen_name_hl.text.text = self.data.name
	local bundle, asset = ResPath.GetF2TianShenImage(string.format("a3_ts_small_yq0%d", color))
	self.node_list.img_bg.image:LoadSprite(bundle, asset)

    local flag = false
    local active_status = TianShenWGData.Instance:IsShenQiActivation(self.data.index)
	self.node_list.tianshen_star:CustomSetActive(active_status)
	self.node_list.tianshen_lv_root:CustomSetActive(active_status)
    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.data.index)
    local star = shenqi_info and shenqi_info.star or 0
    self.node_list.tianshen_lv.text.text = star
	self.node_list.not_active:CustomSetActive(not active_status)
	-- self.node_list.battle_flag:CustomSetActive(self.data.battle_status ~= -1)
    if active_status then
        flag = TianShenWGData.Instance:GetShenQiShenXingRemind(self.data.index)
    else
        flag = TianShenWGData.Instance:GetShenQiActivationRemind(self.data.index)
    end
    self.node_list.remind:SetActive(flag == 1)
    self:FlushStar()

    local bundle, asset = ResPath.GetTianShenNopackImg("sq_" .. self.data.sq_index)
    self.node_list.tianshen_skill_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.tianshen_skill_icon.image:SetNativeSize()
    end)
end

function TianShenShenQiItemRender:FlushStar()
    local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(self.data.index)
    local state = TianShenWGData.Instance:IsShenQiActivation(self.data.index)
    if state then
        local star = shenqi_info and shenqi_info.star or 0
        local star_res_list = GetStarImgResByStar(star)
        for i = 1, 5 do
            self.node_list[string.format("star%d", i)].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
        end
    end
end

function TianShenShenQiItemRender:CreateSelectEffect()

end

function TianShenShenQiItemRender:OnSelectChange(is_select)
    if self.node_list.highlight then
        self.node_list.highlight:SetActive(is_select)
    end
end