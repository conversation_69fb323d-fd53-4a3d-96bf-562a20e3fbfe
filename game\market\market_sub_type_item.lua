
---------------------市场小类型item------------------
MarketSubTypeItem = MarketSubTypeItem or BaseClass(BaseRender)
function MarketSubTypeItem:__init()
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind1(self.OnClick, self))
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	self.click_callback = nil
end

function MarketSubTypeItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
	end
	self.item_cell = nil

	self.click_callback = nil
end

function MarketSubTypeItem:OnFlush()
	if self.data then
		-- 子类型名称
		self.node_list["sub_type_name"].text.text = self.data.small_name
		-- 展示用物品
		self.item_cell:SetData({item_id = self.data.item_id})

		self:FlushAmountDesc()
	end
end

-- 刷新在售数目
function MarketSubTypeItem:FlushAmountDesc()
	if not self.data then
		return
	end
	-- 在售数目
	local goods_amount = MarketWGData.Instance:GetGoodsAmount(self.data.big_id, self.data.small_id)
	self.node_list["sub_type_sell_amount"].text.text = string.format(Language.Market.GoodsAmountStr, goods_amount)
end

function MarketSubTypeItem:SetClickCallBack(click_callback)
	self.click_callback = click_callback
end

function MarketSubTypeItem:OnClick()
	if self.click_callback then
		self.click_callback(self.data)
	end
end

---------------------------求购列表面板用----------------------------
MarketWantListSubTypeItem = MarketWantListSubTypeItem or BaseClass(MarketSubTypeItem)

function MarketWantListSubTypeItem:FlushAmountDesc()
	if not self.data then
		return
	end
	local want_list_info = MarketWGData.Instance:GetWantListInfo(self.data.big_id, self.data.small_id) -- 获取对应子类型已上架的商品列表
	self.node_list["sub_type_sell_amount"].text.text = string.format(Language.Market.WantListAmountStr, #want_list_info)
end

---------------------------我的求购面板用----------------------------
MarketMyWantSubTypeItem = MarketMyWantSubTypeItem or BaseClass(MarketSubTypeItem)

function MarketMyWantSubTypeItem:FlushAmountDesc()
	if not self.data then
		return
	end
	
	self.node_list["sub_type_sell_amount"].text.text = ""
end