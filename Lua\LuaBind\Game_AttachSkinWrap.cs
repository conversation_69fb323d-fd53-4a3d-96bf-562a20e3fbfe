﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Game_AttachSkinWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(Game.AttachSkin), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("AttachMesh", AttachMesh);
		<PERSON><PERSON>unction("ClearMeshes", ClearMeshes);
		<PERSON><PERSON>unction("SetRootBone", SetRootBone);
		L<PERSON>unction("ResetRootBone", ResetRootBone);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.Reg<PERSON>ar("AttachPrefab", get_AttachPrefab, null);
		<PERSON><PERSON>("AttachRootBone", get_AttachRootBone, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AttachMesh(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Game.AttachSkin obj = (Game.AttachSkin)ToLua.CheckObject(L, 1, typeof(Game.AttachSkin));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.AttachMesh(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearMeshes(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Game.AttachSkin obj = (Game.AttachSkin)ToLua.CheckObject(L, 1, typeof(Game.AttachSkin));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.ClearMeshes(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRootBone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Game.AttachSkin obj = (Game.AttachSkin)ToLua.CheckObject(L, 1, typeof(Game.AttachSkin));
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.SetRootBone(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetRootBone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Game.AttachSkin obj = (Game.AttachSkin)ToLua.CheckObject(L, 1, typeof(Game.AttachSkin));
			obj.ResetRootBone();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AttachPrefab(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.AttachSkin obj = (Game.AttachSkin)o;
			UnityEngine.GameObject ret = obj.AttachPrefab;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AttachPrefab on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AttachRootBone(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.AttachSkin obj = (Game.AttachSkin)o;
			UnityEngine.Transform ret = obj.AttachRootBone;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AttachRootBone on a nil value");
		}
	}
}

