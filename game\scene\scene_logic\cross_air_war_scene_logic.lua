CrossAirWarSceneLogic = CrossAirWarSceneLogic or BaseClass(CrossServerSceneLogic)
local START_CHECKFIND_BOSS_TIME = 4

local SceneEffectGroup = {
	[1] = 0,
	[2] = 2,
	[3] = 3,
	[4] = 4,
	[5] = 5,
}

function CrossAirWarSceneLogic:__init()
	self.next_check_time = 0
end

function CrossAirWarSceneLogic:__delete()
	self.cur_moster_seq = nil
	self.cur_act_stage = nil
	self.cur_entrance_seq = nil
	self.boss_camera_seq = nil
	self.load_scene_finish = nil
	self.air_wall_flag = nil
	self.next_check_time = nil
end

function CrossAirWarSceneLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	self.air_wall_flag = {}
	self.next_check_time = 0

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		CrossAirWarWGCtrl.Instance:OpenTaskView()

		self:CheckGoToMosterPos()
		self:CheckSceneStaticObjActive()
		self:CheckSceneAirWall()
		self.load_scene_finish = true
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_AIR_WAR)
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if activity_info and activity_info.next_time and activity_info.next_time > server_time then
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time)
		end

		-- 每天弹一次规则提示
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
		local str_day = string.format("%s/%s", "air_war_rule_tips", main_role_id)
		local save_day = PlayerPrefsUtil.GetInt(str_day, 0)
		if save_day ~= open_day then
			local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
			RuleTip.Instance:SetContent(fb_cfg.fb_desc, Language.TreasureLoft.TipsRollCardTitle)
			PlayerPrefsUtil.SetInt(str_day, open_day)
		end
	end)
end


function CrossAirWarSceneLogic:Update(now_time, elapse_time)
	BaseSceneLogic.Update(self, now_time, elapse_time)

	local floor_time = math.floor(now_time)
	if floor_time >= self.next_check_time then
		self.next_check_time = floor_time + START_CHECKFIND_BOSS_TIME
		self:OnCheckFindBossPos()
	end
end

function CrossAirWarSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

	self.cur_moster_seq = nil
	self.cur_act_stage = nil
	self.cur_entrance_seq = nil
	self.boss_camera_seq = nil
	self.load_scene_finish = nil
	self.air_wall_flag = nil
	self.cur_wait_moster_seq = nil

	if self.pick_finish then
		GlobalEventSystem:UnBind(self.pick_finish)
		self.pick_finish = nil
	end

	for k, _ in pairs(self.block_t) do
		local i = math.floor(k / 100000)
		local j = k % 100000
		AStarFindWay:RevertBlockInfo(i, j)
	end
	self.block_t = nil

	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelFB)
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
	CrossAirWarWGCtrl.Instance:CloseTaskView()
	CrossAirWarWGCtrl.Instance:CloseAirWarAuctionView()
end

function CrossAirWarSceneLogic:OnCheckFindBossPos()
	local have_pick = Scene.Instance:CheckFinishPick()
	if have_pick or (not self.is_pack_status) then
		return
	end

	self:CheckGoToMosterPos()
	self.is_pack_status = false
end

function CrossAirWarSceneLogic:CheckGoToMosterPos()
	local have_pick = Scene.Instance:CheckFinishPick()
	if have_pick then
		self.is_pack_status = true
		return
	end

	local moster_seq, is_wait = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
	-- print_error("寻路的位置", moster_seq)
	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(moster_seq)

	if moster_seq == nil or moster_cfg == nil then
		return
	end

	local aim_num = moster_cfg.monster_num or 0
	local cur_num = CrossAirWarWGData.Instance:GetWarCurStageMosterNum()
	local finish_num = aim_num - cur_num
	local aim_moster_num = finish_num + 1

	if is_wait then
		aim_moster_num = 1
	end

	local moster_pos = CrossAirWarWGData.Instance:GetMonsterPosBySeq(moster_seq)
	if not moster_pos then
		return
	end

	local pos = moster_pos[aim_moster_num]
	if pos == nil then
		return
	end

	if finish_num > 1 and moster_seq == 3 then
		return
	end

	local status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
	if status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_GATHER then
		pos = CrossAirWarWGData.Instance:GetFindBoxPos()
	end

	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	local scene_id = Scene.Instance:GetSceneId()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, pos.x, pos.y, 12)
end

-- 检测是否切换阶段
function CrossAirWarSceneLogic:CheckChangeActStage(act_stage)
	if act_stage == nil then
		return false
	end

	if self.cur_act_stage and self.cur_act_stage ~= act_stage then
		self.cur_act_stage = act_stage
		return true
	end

	self.cur_act_stage = act_stage
	return false
end

-- 检测是否有展示切换阶段引导
function CrossAirWarSceneLogic:CheckChangeWaitMosterSeq(moster_seq)
	if moster_seq == nil then
		return false
	end

	if self.cur_wait_moster_seq and self.cur_wait_moster_seq ~= moster_seq then
		self.cur_wait_moster_seq = moster_seq
		return true
	end

	self.cur_wait_moster_seq = moster_seq
	return false
end

-- 检测是否有boss出场
function CrossAirWarSceneLogic:CheckHaveBossEntrance(entrance_seq)
	if entrance_seq == nil then
		return false
	end

	if self.cur_entrance_seq ~= entrance_seq then
		self.cur_entrance_seq = entrance_seq
		return true
	end

	self.cur_entrance_seq = entrance_seq
	return false
end

-- 检测是否存在这个boss如果存在直接展示
function CrossAirWarSceneLogic:CheckHaveBossShowBySeq(seq)
	if self.boss_camera_seq == seq then
		return
	end

	if not self.load_scene_finish then
		return
	end

	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(seq)

	if nil ~= moster_cfg then
		local monster_obj = Scene.Instance:GetMonstObjByMonstID(moster_cfg.monster_id0)
		local enter_monster_cfg = CrossAirWarWGData.Instance:GetMosterCgCfgById(moster_cfg.monster_id0)

		if monster_obj == nil then
			monster_obj = Scene.Instance:GetMonstObjByMonstID(moster_cfg.monster_id1)
			enter_monster_cfg = CrossAirWarWGData.Instance:GetMosterCgCfgById(moster_cfg.monster_id1)
		end

		if monster_obj == nil or enter_monster_cfg == nil then
			return
		end

		local camera_offset_t = Split(enter_monster_cfg.camera_offset, "##")
		local operate_param_t = Split(enter_monster_cfg.operate_param, "##")
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		BossCamera.Instance:BossFollowCaneraShow(monster_obj, enter_monster_cfg.cg_time, operate_param_t, camera_offset_t,
		enter_monster_cfg.boss_name_bundle, enter_monster_cfg.boss_name_assets, enter_monster_cfg.interlude_name_img, true)
	end

	self.boss_camera_seq = seq
end

-- 检测空气墙
function CrossAirWarSceneLogic:CheckSceneAirWall()
	local status = CrossAirWarWGData.Instance:GetAirWarSceneStatus() 
	if status == CROSS_AIR_WAR_AUCTION_STATUS.STATUS_WAIT_START then
		local wait_wall_id, data = CrossAirWarWGData.Instance:GetWaitAirWallPos()
		self:OperateAirWall(true, nil, wait_wall_id, data)
		self:SetSceneStaticObjActive(100, true)
		return
	else
		local wait_wall_id, data = CrossAirWarWGData.Instance:GetWaitAirWallPos()
		self:OperateAirWall(false, nil, wait_wall_id, data)
		self:SetSceneStaticObjActive(100, false)
	end

	local moster_seq = CrossAirWarWGData.Instance:GetWarCurMonsterSeq()
	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(moster_seq)

	if moster_cfg == nil then
		return
	end

	local wall_cfg = CrossAirWarWGData.Instance:GetAirWallCfg()
	
	if not wall_cfg then
		return
	end

	for i, cfg in ipairs(wall_cfg) do
		if cfg.stage >= moster_cfg.stage then		-- 生成空气墙
			self:OperateAirWall(true, cfg)
		else	-- 移除空气墙
			self:OperateAirWall(false, cfg)
		end
	end
end

function CrossAirWarSceneLogic:OperateAirWall(is_add, cfg, other_id, other_pos)
	local get_air_data = function(aim_cfg)
		if not aim_cfg then
			return nil
		end

		local camera_offset_t = Split(aim_cfg.air_wall, "##")
		local data = {}
		data.x = tonumber(camera_offset_t[1]) or 0
		data.y = tonumber(camera_offset_t[2]) or 0
		data.w = tonumber(camera_offset_t[3]) or 0
		data.h = tonumber(camera_offset_t[4]) or 0
		return data
	end

	local air_wall_id = other_id or (cfg and cfg.air_wall_id or -1)
	local data_message = other_pos or get_air_data(cfg)

	if air_wall_id < 0 or data_message == nil then
		return
	end

	if not self.air_wall_flag then
		self.air_wall_flag = {}
	end

	if is_add then
		if self.air_wall_flag[air_wall_id] then
			return
		end

		local data = data_message
		self.air_wall_flag[air_wall_id] = true
		self:OpCreateAirWall(data.x, data.y, data.w, data.h)
	else
		if self.air_wall_flag[air_wall_id] then
			local data = data_message
			self:OpDelAllWall(data.x, data.y, data.w, data.h)
		end

		self.air_wall_flag[air_wall_id] = false
	end
end

-- 生成空气墙
function CrossAirWarSceneLogic:OpCreateAirWall(x, y, w, h)
	if not self.block_t then
		self.block_t = {}
	end

	for i = x, x + w - 1 do
		for j = y, y + h - 1 do
			self.block_t[i * 100000 + j] = true
			AStarFindWay:SetBlockInfo(i, j)
		end
	end
end

-- 移除空气墙
function CrossAirWarSceneLogic:OpDelAllWall(x, y, w, h)
	if not self.block_t then
		self.block_t = {}
	end

	for i = x, x + w - 1 do
		for j = y, y + h - 1 do
			self.block_t[i * 100000 + j] = nil
			AStarFindWay:RevertBlockInfo(i, j)
		end
	end
end

-- 检测场景物体
function CrossAirWarSceneLogic:CheckSceneStaticObjActive()
	local moster_seq = CrossAirWarWGData.Instance:GetWarCurMonsterSeq(true)
	local moster_cfg = CrossAirWarWGData.Instance:GetMonsterCfgBySeq(moster_seq)
	local active_stage = moster_seq

	if moster_seq ~= 0 then
		active_stage = moster_cfg.stage
	end

	for i, stage in ipairs(SceneEffectGroup) do
		self:SetSceneStaticObjActive(stage, active_stage <= stage)
	end
end


-- 检测场景物体
function CrossAirWarSceneLogic:SetSceneStaticObjActive(group_id, active_status)
	Scene.Instance:TrySetSceneStaticObjActive(group_id, active_status)
end