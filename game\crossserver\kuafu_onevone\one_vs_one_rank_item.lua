-----------------------OneVsOneRankItem-----------------------------------
-- 1v1排行榜展示 根据排名 预制体也会不同
OneVsOneRankItem = OneVsOneRankItem or BaseClass(BaseRender)

function OneVsOneRankItem:__init()
    
end

function OneVsOneRankItem:ReleaseCallBack()
    
end

function OneVsOneRankItem:OnFlush()
	if self.data == nil then
		self:SetActive(false)
		return
	end
	self:SetActive(true)
	self.node_list.rank:SetText(self.data.rank_index)
	self.node_list.score:SetText(self.data.rank_value)
	local name_tab = Split(self.data.user_name,"_s")
	self.node_list.name:SetText(name_tab[1])
	local name = FlowerRankWGData.Instance:GetServerName(self.data)
	self.node_list.server:SetText(name)
end

function OneVsOneRankItem:SetClickCallBack(event)
    self.event = event
end

function OneVsOneRankItem:OnClick()
    if self.event then
        self.event(self)
    end
end

-----------------------OneVsOneTopItem-----------------------------------
OneVsOneTopItem = OneVsOneTopItem or BaseClass(OneVsOneRankItem)

function OneVsOneTopItem:__init()
    self.head_cell = BaseHeadCell.New(self.node_list.HeadCell)
end

function OneVsOneTopItem:ReleaseCallBack()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function OneVsOneTopItem:OnFlush()
	if self.data == nil then
		self:SetActive(false)
		return
	end
	self:SetActive(true)
	OneVsOneRankItem.OnFlush(self)
	self.head_cell:SetData({role_id = self.data.user_id, sex = self.data.sex, prof = self.data.prof})
	local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.data.rank_index)
	self.node_list.icon.image:LoadSprite(bundle, asset)
	self.node_list.rank:SetActive(false)
	local reward_cfg = KuafuOnevoneWGData.Instance:GetRewardBaseCell(self.data.rank_value)
	ChangeToQualityText(self.node_list.rank_name, RankGradeEnum[reward_cfg.rank_id])
	self.node_list.rank_name:SetText(reward_cfg.name)
end

function OneVsOneTopItem:SetClickCallBack(event)
    self.event = event
end

function OneVsOneTopItem:OnClick()
    if self.event then
        self.event(self)
    end
end