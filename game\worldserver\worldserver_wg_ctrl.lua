
require("game/worldserver/worldserver_wg_data")
require("game/worldserver/worldserver_view")
require("game/worldserver/worldserver_xianjie_view")
require("game/worldserver/worldserver_shenyuan_view")
require("game/worldserver/shenyuan_boss_jiesuan")
require("game/worldserver/treasure_hurt_boss_view")
require("game/worldserver/treasure_boss_jiesuan")
require("game/worldserver/worldserver_everyday_recharge_boss_view")
require("game/worldserver/worldserver_erb_scene_info_view")

-- 世界服
WorldServerWGCtrl = WorldServerWGCtrl or BaseClass(BaseWGCtrl)

function WorldServerWGCtrl:__init()
	if WorldServerWGCtrl.Instance then
        error("[WorldServerWGCtrl]:Attempt to create singleton twice!")
	end
	WorldServerWGCtrl.Instance = self

	self.data = WorldServerWGData.New()
	self.view = WorldServerView.New(GuideModuleName.WorldServer)
    
    self.shenyuan_boss_jiesuan = ShenyuanBossJieSuanView.New()
    self.treasure_hurt_boss_view = TreasureBossHurtView.New()
    self.treasure_boss_jiesuan = TreasureBossJieSuanView.New()

	self.erb_scene_info_view = WorldServerERBSceneInfoView.New()

	self:RegisterAllProtocals()
end

function WorldServerWGCtrl:__delete()
    WorldServerWGCtrl.Instance = nil

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

    if self.shenyuan_boss_jiesuan then
        self.shenyuan_boss_jiesuan:DeleteMe()
        self.shenyuan_boss_jiesuan = nil
    end

    if self.treasure_boss_jiesuan then
    	self.treasure_boss_jiesuan:DeleteMe()
    	self.treasure_boss_jiesuan = nil
    end

    if self.treasure_hurt_boss_view then
    	self.treasure_hurt_boss_view:DeleteMe()
    	self.treasure_hurt_boss_view = nil
    end
end

function WorldServerWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCShenYuanBossFinishInfo, "OnSCShenYuanBossFinishInfo") --深渊boss结算
    self:RegisterProtocol(SCShenYuanBossBoxGatherRoleList, "OnSCShenYuanBossBoxGatherRoleList") --深渊boss宝箱采集信息
end

function WorldServerWGCtrl:Open(tab_index, param_t)
	tab_index = tab_index or self:GetBossViewShowIndex()
	self.view:Open(tab_index)
end

function WorldServerWGCtrl:GetBossViewShowIndex()
	if self.view:IsOpen() then
		return self.view:GetShowIndex()
	end
end

--深渊boss结算
function WorldServerWGCtrl:OnSCShenYuanBossBoxGatherRoleList(protocol)
	self.data:SaveRoleData(protocol)
end

--深渊boss结算
function WorldServerWGCtrl:OnSCShenYuanBossFinishInfo(protocol)
	self.shenyuan_boss_jiesuan:SetWinData(protocol)
end

function WorldServerWGCtrl:OpenTreasureHurtBossInfoView()
	if self.treasure_hurt_boss_view then
		self.treasure_hurt_boss_view:Open()
	end
end

function WorldServerWGCtrl:CloseTreasureHurtBossInfoView()
	if self.treasure_hurt_boss_view then
		self.treasure_hurt_boss_view:Close()
	end
end

function WorldServerWGCtrl:FlushTreasureHurtBossInfoView()
	if self.treasure_hurt_boss_view and self.treasure_hurt_boss_view:IsOpen() then
		self.treasure_hurt_boss_view:Flush()
	end
end

function WorldServerWGCtrl:OpenTreasureJieSuan(protocol)
	-- 珍惜掉落检测
	local reward_list = {}
	for _,v in pairs(protocol.owner_reward_list) do
		reward_list[#reward_list + 1] = v
	end
	for _,v in pairs(protocol.join_reward_list) do
		reward_list[#reward_list + 1] = v
	end

	RareItemDropWGCtrl.Instance:AddEndCallBack(function ()
		self.treasure_boss_jiesuan:SetWinData(protocol)
	end)

	RareItemDropWGCtrl.Instance:CheckRewardList(reward_list)
end

function WorldServerWGCtrl:OpenERBSceneInfoView()
	if self.erb_scene_info_view and not self.erb_scene_info_view:IsOpen() then
		self.erb_scene_info_view:Open()
	end
end

function WorldServerWGCtrl:FlushERBSceneInfoView()
	if self.erb_scene_info_view and self.erb_scene_info_view:IsOpen() then
		self.erb_scene_info_view:Flush()
	end
end

function WorldServerWGCtrl:CloseERBSceneInfoView()
	if self.erb_scene_info_view and self.erb_scene_info_view:IsOpen() then
		self.erb_scene_info_view:Close()
	end
end

function WorldServerWGCtrl:SetSpecialBossRebirthNotice()
	if self.erb_scene_info_view and self.erb_scene_info_view:IsOpen() then
		if self.erb_scene_info_view.SpecialBossRebirthNotice then
			self.erb_scene_info_view.SpecialBossRebirthNotice()
		end
	end
end

function WorldServerWGCtrl:GetERBSceneInfoView()
	return self.erb_scene_info_view
end