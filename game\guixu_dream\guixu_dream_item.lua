
GuiXuDreamSuitCell = GuiXuDreamSuitCell or BaseClass(BaseRender)

function GuiXuDreamSuitCell:__init()
end

function GuiXuDreamSuitCell:LoadCallBack()

end

function GuiXuDreamSuitCell:__delete()

end

function GuiXuDreamSuitCell:OnFlush()
    if self.data == nil then
        return
    end

    local bundle, asset = ResPath.GetGuiXuDreamImg("a3_gxmy_yq_" .. self.data.suit)
    self.node_list.nor_cell_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.nor_cell_icon.image:SetNativeSize()
    end)

    self.node_list.sel_cell_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.sel_cell_icon.image:SetNativeSize()
    end)

    self.node_list.nor_name.text.text = self.data.suit_name
    self.node_list.sel_name.text.text = self.data.suit_name

    local suit_data = WardrobeWGData.Instance:GetSuitAllListBySuit(self.data.suit)
    if suit_data then
        self.node_list.sel_remind:SetActive(suit_data.can_act and self.data.state ~= REWARD_STATE_TYPE.UNDONE)
        self.node_list.nor_remind:SetActive(suit_data.can_act and self.data.state ~= REWARD_STATE_TYPE.UNDONE)
    end
end

function GuiXuDreamSuitCell:OnSelectChange(is_select)
    self.node_list.select_hl:SetActive(is_select)
    self.node_list.nor:SetActive(not is_select)
end
----------------
GuiXuDreamPartRender = GuiXuDreamPartRender or BaseClass(BaseRender)
function GuiXuDreamPartRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
    end
end

function GuiXuDreamPartRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function GuiXuDreamPartRender:OnFlush()
    if self.data == nil then
        return
    end

    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end

    self.view:SetActive(true)
    self.item_cell:SetData({item_id = self.data.show_item_id})
    self.item_cell:MakeGray(self.data.state == REWARD_STATE_TYPE.UNDONE)
end

--

GuiXuDreamActPartRender = GuiXuDreamActPartRender or BaseClass(BaseRender)
function GuiXuDreamActPartRender:LoadCallBack()

end

function GuiXuDreamActPartRender:__delete()
end

function GuiXuDreamActPartRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.hl_img:SetActive(self.data.state ~= REWARD_STATE_TYPE.UNDONE)
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.show_item_id)
    local color = self.data.state == REWARD_STATE_TYPE.UNDONE and COLOR3B.C12 or COLOR3B.C8
    self.node_list.name.text.text = ToColorStr(item_cfg and item_cfg.name or "", color)
end
---
GuiXuDreamAttrRender = GuiXuDreamAttrRender or BaseClass(BaseRender)
local AtrrColorText = {
    [1] = "#79fa82",
    [2] = "#fab379",
    [3] = "#ff9292",
    [4] = "#ffe58c",
    [5] = "#fa7994",
    [6] = "#fa7994",
}
function GuiXuDreamAttrRender:__init()
    self.attr_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
    end
end

function GuiXuDreamAttrRender:__delete()
    self.attr_list = nil
end

function GuiXuDreamAttrRender:SetFromItemTip(bool)
    self.is_from_itemtip = bool
end

function GuiXuDreamAttrRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
	end

    local attri_color = COLOR3B.C12
    if data.is_act then
        attri_color = COLOR3B.C8
    else
        attri_color = self.is_from_itemtip and COLOR3B.C8 or COLOR3B.C12
    end

    local need_str
    if self.is_from_itemtip then
        if self.node_list.suit_icon then
            XUI.SetGraphicGrey(self.node_list.suit_icon, not data.is_act)
        end
        need_str = data.all_act_attr and Language.Wardrobe.AllActDesc1 or string.format(Language.Wardrobe.SuitNumCompany1, data.need_num)
    else
        need_str = data.all_act_attr and Language.Wardrobe.AllActDesc or string.format(Language.Wardrobe.SuitNumCompany, data.need_num)
    end

    self.node_list.need_num.text.text = ToColorStr(need_str, attri_color)
    local list = data.attr_list

    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
            local name = ""
            local attr_str = ""
            local value = ""
            if list[k].attr_type == "add_per" then
                name = Language.GuiXuDream.SpecialAttr
                value = is_per and list[k].value or list[k].value / 100 .. "%"
                local format_str = self.is_from_itemtip and "%s   %s" or "%s  %s"
                attr_str = string.format(format_str, name, value)
            else
                name = EquipmentWGData.Instance:GetAttrName(list[k].attr_type, true)
                value = is_per and list[k].value or list[k].value / 100 .. "%"
                if self.is_from_itemtip then
                    attr_str = string.format("%s   %s", name, value)         
                elseif is_per then
                    attr_str = string.format("%s  %s", name, value)
                else
                    attr_str = string.format("%s  %s", name, value)
                end
            end

            v.text.text = ToColorStr(attr_str, attri_color)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end