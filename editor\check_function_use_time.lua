CheckFuntionUseTime = {}

local IsStart = false
local new_global_obj_list = {}
local stack = {}
local total_info = {}
local ready_to_start = false
local max_depth = 0
local cur_depth = 0

function CheckFuntionUseTime:Start(depth)
	UnityEngine.Debug.LogError("[CheckFuntionUseTime] Start  max_depth: " .. depth)
	ready_to_start = true
	max_depth = depth or 0
end

function CheckFuntionUseTime:Stop()
	UnityEngine.Debug.LogError("[CheckFuntionUseTime] Stop")
	ready_to_start = false
end

local frame_count = 1
function CheckFuntionUseTime:NewFrame(time, delta_time)
	if ready_to_start then
		IsStart = true
	else
		if IsStart then
			self:WriteAllStack()
			total_info = {}
		end
		IsStart = false
	end

	if not IsStart then
		return
	end

	if delta_time > 0.066 then
		table.insert(total_info, {frame_count = frame_count, frame_cost_time = delta_time, stack = stack})
	end

	stack = {}
	frame_count = frame_count + 1
	cur_depth = 0
end

function CheckFuntionUseTime:StartCall(func_name, func)
	if not IsStart then
		return
	end

	cur_depth = cur_depth + 1
	if max_depth > 0 and cur_depth > max_depth then
		return
	end

	local time_stamp = socket.gettime()
		-- print_error()
	table.insert(stack, {statue = 1, func_name = func_name, func = func, time_stamp = time_stamp})
end

function CheckFuntionUseTime:EndCall(func_name, func)
	if not IsStart then
		return
	end

	cur_depth = cur_depth - 1

	if max_depth > 0 and cur_depth + 1 > max_depth then
		return
	end

	local time_stamp = socket.gettime()
	table.insert(stack, {statue = 2, func_name = func_name, func = func, time_stamp = time_stamp})
end

function CheckFuntionUseTime:CollatingAllStack()
	for k,v in ipairs(total_info) do
		local new_stack = self:CollatingStack(v.stack)
		v.stack = new_stack
	end
end

function CheckFuntionUseTime:CollatingStack(stack)
	local new_stack = {}
	for k,v in ipairs(stack) do
		if v.statue == 1 then
			table.insert(new_stack, v)
		else
			local info = {func_name = v.func_name, func = v.func, cost_time = 0, self_cost_time = 0, children = {}, children_count = 0}
			local children_cost_time = 0
			local children_count = 0
			while #new_stack > 0 do
				local t = table.remove(new_stack)
				if t.func == v.func and t.time_stamp then
					info.cost_time = v.time_stamp - t.time_stamp
					info.self_cost_time = info.cost_time - children_cost_time
					info.children_count = children_count
					table.insert(new_stack, info)
					break
				else
					children_cost_time = children_cost_time + t.cost_time
					table.insert(info.children, t)
					children_count = children_count + t.children_count + 1
				end
			end
		end
	end

	return new_stack
end

function CheckFuntionUseTime:Update(now_time, elapse_time)
	self:OutputReport()
end

function CheckFuntionUseTime:OutputReport()
	if #new_global_obj_list > 0 then
		for _, msg in pairs(new_global_obj_list) do
			UnityEngine.Debug.LogError(msg)
		end

		new_global_obj_list = {}
	end
end

local function WriteStack(tbl, stack, str)
	local cost_time = stack.cost_time * 1000
	-- 剔除小于1ms的
	if cost_time < 1 then
		return cost_time, stack.children_count + 1
	end

	local info = debug.getinfo(stack.func, "Su")
	local msg
	if max_depth > 0 then
		msg = string.format("%s%s  @file:%s[line:%s]  CostTime: %0.2fms  SelfCostTime: %0.2fms\n",
			str, stack.func_name, info.short_src, info.linedefined, cost_time, stack.self_cost_time * 1000)
	else
		msg = string.format("%s%s  @file:%s[line:%s]  CostTime: %0.2fms  SelfCostTime: %0.2fms  CallFunctionsCount: %s  TotalCallFunctionsCount: %s\n",
			str, stack.func_name, info.short_src, info.linedefined, cost_time, stack.self_cost_time * 1000, #stack.children, stack.children_count)
	end
	table.insert(tbl, msg)

	table.sort(stack.children, function (a, b)
		return a.cost_time > b.cost_time
	end)

	local new_str = str .. "\t"
	local total_other_cost_time = 0
	local total_children_count = 0
	for k,v in ipairs(stack.children) do
		local other_cost_time, children_count = WriteStack(tbl, v, new_str)
		total_other_cost_time = total_other_cost_time + other_cost_time
		total_children_count = total_children_count + children_count
	end

	if total_other_cost_time > 1 then
		table.insert(tbl, string.format("%sOtherCostTime: %0.2fms  TotalCount: %s\n", new_str, total_other_cost_time, total_children_count))
	end

	return 0, 0
end

function CheckFuntionUseTime:WriteAllStack()
	UnityEngine.Debug.LogError("[CheckFuntionUseTime] WriteAllStack")
	self:CollatingAllStack()
	-- local file_path = string.format("%s/%s", UnityEngine.Application.dataPath, "LuaFunctionCostTime.txt")
	local file_path = "LuaFunctionCostTime.txt"
	local file = io.open(file_path, "w")

	local tbl = {}
	for k,v in ipairs(total_info) do
		local lua_cost_time = 0
		for k2,v2 in ipairs(v.stack) do
			lua_cost_time = lua_cost_time + v2.cost_time
		end

		table.insert(tbl, string.format("FrameCount: %s  FrameTotalCostTime: %0.2fms  LuaCostTime: %0.2fms  Details:\n",
			v.frame_count, v.frame_cost_time * 1000, lua_cost_time * 1000))

		table.sort(v.stack, function (a, b)
			return a.cost_time > b.cost_time
		end)

		for k2,v2 in ipairs(v.stack) do
			WriteStack(tbl, v2, "\t")
		end
		table.insert(tbl, "\n")
	end

	local content = table.concat(tbl)
	file:write(content)
	file:close()
end

return CheckFuntionUseTime