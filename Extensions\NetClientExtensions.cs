﻿using LuaInterface;
using Nirvana;

/// <summary>
/// <see cref="EnhancedNetClient"/> 的扩展方法。
/// 
/// 提供：
/// - 便捷监听/取消监听网络消息（自动封装为 LuaByteBuffer）
/// </summary>
public static class NetClientExtensions
{
    /// <summary>
    /// Lua 层的消息回调委托，参数为封装后的 <see cref="LuaByteBuffer"/>。
    /// </summary>
    public delegate void ReceiveMessageDelegate(LuaByteBuffer message);

    /// <summary>
    /// 监听来自 <see cref="EnhancedNetClient"/> 的完整消息，并将其封装为 <see cref="LuaByteBuffer"/> 回调给 Lua 层。
    /// 返回值为内部注册到 <see cref="EnhancedNetClient.ReceiveEvent"/> 的委托句柄，
    /// 可用于后续通过 <see cref="UnlistenMessage(EnhancedNetClient, EnhancedNetClient.ReceiveDelegate)"/> 取消监听。
    /// </summary>
    /// <param name="client">网络客户端实例。</param>
    /// <param name="receiveDelegate">Lua 层消息处理回调。</param>
    /// <returns>注册到 <see cref="EnhancedNetClient.ReceiveEvent"/> 的句柄。</returns>
    public static EnhancedNetClient.ReceiveDelegate ListenMessage(
        this EnhancedNetClient client, 
        ReceiveMessageDelegate receiveDelegate)
    {
        EnhancedNetClient.ReceiveDelegate handle = delegate(byte[] bytes, uint length)
        {
            // 将 C# 侧的原始字节与长度封装为 Lua 可读的 LuaByteBuffer
            var message = new LuaByteBuffer(bytes, (int)length);
            receiveDelegate(message);
        };

        client.ReceiveEvent += handle;
        return handle;
    }

    /// <summary>
    /// 取消监听通过 <see cref="ListenMessage(EnhancedNetClient, ReceiveMessageDelegate)"/> 注册的消息回调。
    /// </summary>
    /// <param name="client">网络客户端实例。</param>
    /// <param name="handle">注册时返回的句柄。</param>
    public static void UnlistenMessage(
        this EnhancedNetClient client, 
        EnhancedNetClient.ReceiveDelegate handle)
    {
        client.ReceiveEvent -= handle;
    }    
}
