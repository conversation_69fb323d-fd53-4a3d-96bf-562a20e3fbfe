
--每日累充--连续充值

local open_num_t = {
    [1] = "six_open_num",
    [2] = "thirtr_open_num",
}

local MaxItemNum = 6

function EveryDayRechargeView:ReleaseLXRechargeView()
    self.lxcz_slider_reward_cfg = nil
    self.lxcz_recharge_reward_cfg = nil
    -- if self.lxcz_slider_item_list then
    --     self.lxcz_slider_item_list:DeleteMe()
    --     self.lxcz_slider_item_list = nil
    -- end

    if self.lxcz_reward_item_list then
        for i,v in ipairs(self.lxcz_reward_item_list) do
            v:DeleteMe()
        end
        self.lxcz_reward_item_list = {}
    end

    if self.lianxu_recharge_gift_list then
		self.lianxu_recharge_gift_list:DeleteMe()
		self.lianxu_recharge_gift_list = nil
	end

    --模型相关参数
    if self.lxcz_role_model then
        self.lxcz_role_model:DeleteMe()
        self.lxcz_role_model = nil
    end

    if self.lxcz_xianwa_model then
        self.lxcz_xianwa_model:DeleteMe()
        self.lxcz_xianwa_model = nil
    end

    self.lxcz_cur_btn_type = nil
    self.lxcz_select_suit_seq = nil
    -- self.lxcz_is_foot_view = nil
    -- self.lxcz_foot_effect_id = nil
    -- self.lxcz_use_update = nil
    -- self.lxcz_next_create_footprint_time = nil
    -- self.lxcz_footprint_eff_t = nil
    self.lxcz_body_res_id = nil
    self.lxcz_mount_res_id = nil
    self.lxcz_mount_action = nil
    self.lxcz_have_foot_print = nil
    self.lxcz_show_role_idel_ani = nil

    self.lxcz_show_model_item_list = nil

    self.leichong_limit_jump = false

    self.lxcz_list_container = nil
    self.lxcz_slider_list_container = nil

    self.lxcz_cur_down_show_cfg = nil
end

function EveryDayRechargeView:InitLXRechargeView()
    self:LXRCInitParam()

    for i = 1, 2 do
        self.node_list["lxcz_recharge_btn_"..i].toggle:AddClickListener(BindTool.Bind(self.LXRCOnClickToggle, self, i))
        local open_num = EveryDayLianXuRechargeWGData.Instance:GetOtherCfg(open_num_t[i])
        self.node_list["recharge_btn_" .. i .."_n_text"].text.text = string.format(Language.RebateRecharge.RechargeBtnTex, open_num)
        self.node_list["recharge_btn_" .. i .."_h_text"].text.text = string.format(Language.RebateRecharge.RechargeBtnTex, open_num)
    end

    self.lxcz_reward_item_list = {}
	for i = 1 , MaxItemNum do
		if not self.lxcz_reward_item_list[i] then
        	self.lxcz_reward_item_list[i] = EveryDayLXRechargeSliderItem.New(self.node_list["everyday_lxcz_item_"..i])
    	end
	end

    self.lianxu_recharge_gift_list = AsyncListView.New(ItemCell, self.node_list.lianxu_recharge_gift_list)

    --XUI.AddClickEventListener(self.node_list["lxcz_wenhao_btn"], BindTool.Bind1(self.LXRCOnClickTipBtn, self))--玩法提示按键事件
    XUI.AddClickEventListener(self.node_list["lianxu_recharge_get_btn"], BindTool.Bind1(self.LXRCOnClickGetRewardBtn, self))--点击领取普通奖励
    --XUI.AddClickEventListener(self.node_list["lxcz_down_recharge_btn"], BindTool.Bind1(self.LXRCOnClickJumpRechargeBtn, self))

    if nil == self.lxcz_role_model then
        self.lxcz_role_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["lxcz_ph_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.XL,
            can_drag = false,
        }
        
        self.lxcz_role_model:SetRenderTexUI3DModel(display_data)
        -- self.lxcz_role_model:SetUI3DModel(self.node_list["lxcz_ph_display"].transform,
        --                             self.node_list["lxcz_EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.lxcz_role_model)
    end

    self.leichong_limit_jump = false
end

function EveryDayRechargeView:LXRCInitParam()
    self.lxcz_slider_reward_cfg = {}
    self.lxcz_recharge_reward_cfg = {}
    self.lxcz_cur_btn_type = self.lxcz_cur_btn_type or 1
    self.lxcz_group_index = 1
    EveryDayLianXuRechargeWGData.Instance:SetCacheBtnType(1)
    self.lxcz_tianshen_index = nil

    self.lxcz_show_model_item_list = {}

    self.lxcz_list_container = nil
    self.lxcz_slider_list_container = nil

    self.lxcz_cur_down_show_cfg = nil
end

function EveryDayRechargeView:ShowIndexLXRechargeView()
    self.lxcz_old_group_index = nil
    self.lxcz_old_btn_type = nil
    self.leichong_limit_jump = true
    -- self.node_list["lxcz_recharge_btn_"..self.lxcz_cur_btn_type].toggle.isOn = true
    self:LXRCPlayPanelAnim()

	self.node_list.everyday_lxcz_itemlist.animation_player:Play("Play")
end

function EveryDayRechargeView:LXRCOnClickToggle(btn_type)
    self.lxcz_cur_btn_type = btn_type
    EveryDayLianXuRechargeWGData.Instance:SetCacheBtnType(btn_type)
    self:FlushLXRechargeView(false)
    self.node_list.everyday_lxcz_itemlist.animation_player:Play("Play")
end

--大标签动画
function EveryDayRechargeView:LXRCPlayPanelAnim()
    -- self:LXRCDoAnimation()
    -- self:LXRCDoCellListAnimation()
end

function EveryDayRechargeView:FlushLXRechargeView(force_flush)
    -- print_error("FFF==== force_flush", force_flush)
    self.lxcz_group_index = EveryDayLianXuRechargeWGData.Instance:GetCurGroupByRechargeType(self.lxcz_cur_btn_type)
    
    self:LXRCFlushToggleRed()
    self:LXRCResetModelItemList()--刷新前清空一下模型item列表
    self:LXRCTodayRechargeText()

    if self.lxcz_group_index ~= self.lxcz_old_group_index or self.lxcz_cur_btn_type ~= self.lxcz_old_btn_type then
        self.lxcz_old_group_index = self.lxcz_group_index
        self.lxcz_old_btn_type = self.lxcz_cur_btn_type
        self:LXRCFlushRewardList()
        self:LXRCFlushModel()
        --设置完模型再设置战力显示
        self:LXRCSetCapabilityShow()
    elseif force_flush then
        self:LXRCFlushRewardList()
    end
end

function EveryDayRechargeView:LXRCFlushRewardList()
    --进度条奖励
    local slider_cfg = EveryDayLianXuRechargeWGData.Instance:GetSliderRewardCfg(self.lxcz_group_index, self.lxcz_cur_btn_type)
    if not IsEmptyTable(slider_cfg) then
        --self.lxcz_slider_item_list:SetDataList(slider_cfg)
        for i = 1 , MaxItemNum do
            --self.discount_item_list[i]:SetIndex(i)
            self.lxcz_reward_item_list[i]:SetData(slider_cfg[i])
        end
    end

    --底部奖励
    local cur_recharge_day = EveryDayLianXuRechargeWGData.Instance:GetCurDayByRechargeType(self.lxcz_cur_btn_type)              --天数
    local cur_cfg = EveryDayLianXuRechargeWGData.Instance:GetRewardCfgByRechargeDat(self.lxcz_cur_btn_type, cur_recharge_day)
    if IsEmptyTable(cur_cfg) then return end
    self.lxcz_cur_down_show_cfg = cur_cfg
    local reward_state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish
    if cur_cfg.reward_index then
        reward_state = EveryDayLianXuRechargeWGData.Instance:GetRewardState(self.lxcz_cur_btn_type, cur_cfg.reward_index + 1, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal)
    end

	local is_remind = reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet
    --按钮判断
    self.node_list["lianxu_recharge_get_red"]:SetActive(is_remind)--领取 的红点显示
    self.node_list["lianxu_recharge_get_flag"]:SetActive(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet)--已领取
    self.node_list["lianxu_recharge_get_btn"]:SetActive(reward_state ~= EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet)--已领取
    --self.node_list["lxcz_down_recharge_btn"]:SetActive(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish)--未达成

    local reward_item_list = {}
    if not IsEmptyTable(cur_cfg.reward_item) then
        reward_item_list = SortDataByItemColor(cur_cfg.reward_item)
    end
    self.lianxu_recharge_gift_list:SetDataList(reward_item_list)
    self.lianxu_recharge_gift_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet)
        end
    end)

    self.node_list.lianxu_recharge_get_text.text.text = is_remind and Language.RebateRecharge.LXCZGetText or Language.RebateRecharge.LXCZGetText2

    --self:LXCZFlushSlider()
end

--[[function EveryDayRechargeView:LXCZFlushSlider()
    local data_list = EveryDayLianXuRechargeWGData.Instance:GetSliderRewardCfg(self.lxcz_group_index, self.lxcz_cur_btn_type)
    if not data_list then
		return
	end

    local num = 0
    local slider_value = { [0] = 0, 0.2, 0.4, 0.6, 0.8, 1}
    for k,v in pairs(data_list) do
        local btn_type = EveryDayLianXuRechargeWGData.Instance:GetCacheBtnType()
        local reward_type = EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Stage
        local reward_state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish
        if data_list[k].reward_index then
            reward_state = EveryDayLianXuRechargeWGData.Instance:GetRewardState(btn_type, data_list[k].reward_index + 1, reward_type)
        end
		local is_get_flag = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet
		if self.lxcz_slider_item_list[k] then
			self.lxcz_slider_item_list[k]:SetActive(true)
		end
        if reward_state == is_get_flag then
            num = num + 1
        end
        if num <= 0 then
            num = 1
        end
    end
    self.node_list["lxcz_Slider"].slider.value = slider_value[num - 1]
end]]

function EveryDayRechargeView:LXRCTodayRechargeText()
    local recharge_num = EveryDayLianXuRechargeWGData.Instance:GetTodayRechargeNum() * RECHARGE_BILI
    local recharge_num_new = recharge_num > 10000 and CommonDataManager.ConverMoneyByThousand(recharge_num) or recharge_num
    --recharge_num = recharge_num > 10000 and recharge_num or CommonDataManager.ConverMoneyByThousand(recharge_num)
    -- local cur_recharge_day = EveryDayLianXuRechargeWGData.Instance:GetTotalDayByRechargeType(self.lxcz_cur_btn_type)
    local btn_type = EveryDayLianXuRechargeWGData.Instance:GetCacheBtnType()
    local need_recharge_num = EveryDayLianXuRechargeWGData.Instance:GetOtherCfg(open_num_t[btn_type])
    self.node_list["lianxu_today_recharge_num_text"].text.text =  string.format(Language.RebateRecharge.DailyRewareText, recharge_num_new, need_recharge_num)

    self.node_list["lxcz_text_today_rechage"].text.text = recharge_num_new
    --self.node_list["lxcz_text_rechage_day"].text.text = string.format(Language.RebateRecharge.TotalRechargeDay_3, cur_recharge_day)
end

function EveryDayRechargeView:LXRCFlushToggleRed()
    local red_type_1 = EveryDayLianXuRechargeWGData.Instance:IsShowLianXuRechargeRedByBtnType(EVERYDAY_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_1)
    local red_type_2 = EveryDayLianXuRechargeWGData.Instance:IsShowLianXuRechargeRedByBtnType(EVERYDAY_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_2)
    self.node_list["lxcz_toggle_red_1"]:SetActive(red_type_1 == 1)
    self.node_list["lxcz_toggle_red_2"]:SetActive(red_type_2 == 1)
end

---------------------------------------------------模型设置相关 start

function EveryDayRechargeView:LXRCFlushModel()
    if not self.lxcz_role_model then
        return
    end

    --获取配置数据
    local show_model_param = EveryDayLianXuRechargeWGData.Instance:GetShowModelParam(self.lxcz_group_index, self.lxcz_cur_btn_type)
    if IsEmptyTable(show_model_param) or not show_model_param.seq then
        return
    end

    -- --设置标题展示
    -- if show_model_param.img_res_name and show_model_param.img_res_name ~= "" then
    --     self.node_list["lxcz_img_item_name"].image:LoadSprite(ResPath.GetF2RechargeIcon(show_model_param.img_res_name))
    --     self.node_list["lxcz_img_item_name"].image:SetNativeSize()
    -- end

    local show_list = EveryDayLianXuRechargeWGData.Instance:GetShowModelList(show_model_param.seq)
    if IsEmptyTable(show_list) then
        return
    end

    --清理掉回调
    -- self:LXRCClearFootEff()
    self.lxcz_role_model:RemoveAllModel()

    self.node_list["lxcz_xw_root"]:SetActive(false)

    -- self.lxcz_is_foot_view = false
    self.lxcz_body_res_id = AppearanceWGData.Instance:GetRoleResId()
    self.lxcz_mount_res_id = 0
    self.lxcz_mount_action = ""
    self.lxcz_have_foot_print = false
    local have_weapon = false
    local res_id, fashion_cfg
    for k, data in pairs(show_list) do
        if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then      -- 时装大类
            fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
            if fashion_cfg then                                                                     -- 时装
                local prof = GameVoManager.Instance:GetMainRoleVo().prof
                self.lxcz_body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
            end
        elseif data.param1 == SHIZHUANG_TYPE.FOOT then                                              -- 足迹
            self.lxcz_have_foot_print = true
        elseif data.type == WARDROBE_PART_TYPE.MOUNT then                                           -- 坐骑
            fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
            if fashion_cfg then
                self.lxcz_mount_res_id = fashion_cfg.appe_image_id
                self.lxcz_mount_action = MOUNT_RIDING_TYPE[1]
                local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.lxcz_mount_res_id)
                if not IsEmptyTable(action_cfg) then
                    self.lxcz_mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
                end
                self:LXRCSetModelItemList(fashion_cfg.active_item_id)
            end
        elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then                                         -- 化鲲
            fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
            if fashion_cfg then
                self.lxcz_mount_res_id = fashion_cfg.active_id
                self.lxcz_mount_action = MOUNT_RIDING_TYPE[1]
                local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.lxcz_mount_res_id)
                if not IsEmptyTable(action_cfg) then
                    self.lxcz_mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
                end
                self:LXRCSetModelItemList(fashion_cfg.active_need_item_id)
            end
        elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.SHENBING then-- 武器
            have_weapon = true
        end
    end

    local main_role = Scene.Instance:GetMainRole()
    local vo = main_role and main_role:GetVo()
    local d_body_res, d_hair_res, d_face_res
    if vo and vo.appearance then
        if vo.appearance.fashion_body == 0 then
            d_body_res = vo.appearance.default_body_res_id
            d_hair_res = vo.appearance.default_hair_res_id
            d_face_res = vo.appearance.default_face_res_id
        end
    end

    local extra_role_model_data = {
        d_face_res = d_face_res,
        d_hair_res = d_hair_res,
		d_body_res = d_body_res,
		no_need_do_anim = true,
    }
    if self.lxcz_mount_res_id > 0 then
        self.lxcz_role_model:SetRoleResid(self.lxcz_body_res_id, nil, extra_role_model_data)
        if not have_weapon then
            self.lxcz_role_model:SetWeaponModelFakeRemove()
        end

        self.lxcz_role_model:SetMountResid(self.lxcz_mount_res_id)
        self.lxcz_role_model:PlayStartAction(self.lxcz_mount_action)
    else
        self.lxcz_role_model:SetRoleResid(self.lxcz_body_res_id, function()
            if self.lxcz_mount_res_id == 0 then
                if self.lxcz_have_foot_print then
                self.lxcz_role_model:PlayRoleAction(SceneObjAnimator.Move)
                else
                    if self.lxcz_show_role_idel_ani then
                        self.lxcz_role_model:PlayRoleShowAction()
                        self.lxcz_show_role_idel_ani = false
                    else
                        self.lxcz_role_model:PlayIdleAni()
                    end
                end
            end
        end, extra_role_model_data)
        
        if not have_weapon then
            self.lxcz_role_model:SetWeaponModelFakeRemove()
        end
    end

    for k, v in pairs(show_list) do
        self:LXRCShowModelByData(v)
    end

    self:LXRCChangeModelShowScale()

end

function EveryDayRechargeView:LXRCShowModelByData(data)
    if IsEmptyTable(data) then
        return
    end

    local res_id, fashion_cfg
    if data.type == WARDROBE_PART_TYPE.FASHION then             -- 时装大类
        fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
        if fashion_cfg then
            res_id = fashion_cfg.resouce
            local item_id = NewAppearanceWGData.Instance:GetFashionItemId(data.param1, data.param2)
            self:LXRCSetModelItemList(item_id)
            -- if data.param1 == SHIZHUANG_TYPE.BODY then           -- 时装
            --  local prof = GameVoManager.Instance:GetMainRoleVo().prof
            --  res_id = ResPath.GetFashionModelId(prof, res_id)
            --  self.lxcz_role_model:SetRoleResid(res_id)
            if data.param1 == SHIZHUANG_TYPE.MASK then          -- 脸饰
                self.lxcz_role_model:SetMaskResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.BELT then      -- 腰饰
                self.lxcz_role_model:SetWaistResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.WEIBA then     -- 尾巴
                self.lxcz_role_model:SetTailResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then  -- 手环
                self.lxcz_role_model:SetShouHuanResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
                self.lxcz_role_model:SetHaloResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.WING then      -- 羽翼
                self.lxcz_role_model:SetWingResid(res_id, true)
            elseif data.param1 == SHIZHUANG_TYPE.FABAO then     -- 法宝
                self.lxcz_role_model:SetBaoJuResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then  -- 剑阵
                self.lxcz_role_model:SetJianZhenResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.SHENBING then  -- 武器
                res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
                self.lxcz_role_model:SetWeaponResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.FOOT then      -- 足迹
                self.lxcz_role_model:SetFootTrailModel(res_id)
                self.lxcz_role_model:PlayRoleAction(SceneObjAnimator.Move)
                -- self.lxcz_role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
                -- self.lxcz_is_foot_view = true
                -- self.lxcz_foot_effect_id = res_id
                -- if not self.lxcz_use_update then
                --     Runner.Instance:AddRunObj(self, 8)
                --     self.lxcz_use_update = true
                -- end
            end
        end
    elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then         -- 仙娃
        fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
        if fashion_cfg then
            self:LXRCSetXianWaModelData(fashion_cfg.appe_image_id)
            self:LXRCSetModelItemList(fashion_cfg.item_id)
        end
    end
end

--设置仙娃模型
function EveryDayRechargeView:LXRCSetXianWaModelData(res_id)
    self.node_list["lxcz_xw_root"]:SetActive(true)
    if nil == self.lxcz_xianwa_model then
        self.lxcz_xianwa_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["lxcz_xw_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.lxcz_xianwa_model:SetRenderTexUI3DModel(display_data)
        -- self.lxcz_xianwa_model:SetUI3DModel(self.node_list["lxcz_xw_display"].transform,
        --                             self.node_list["lxcz_xw_EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.lxcz_xianwa_model)
    else
        if self.lxcz_xianwa_model then
            self.lxcz_xianwa_model:ClearModel()
        end
    end

    local bundle, asset = ResPath.GetHaiZiModel(res_id)
    self.lxcz_xianwa_model:SetMainAsset(bundle, asset, function()
        self.lxcz_xianwa_model:PlaySoulAction()
    end)
    self.lxcz_xianwa_model:FixToOrthographic(self.root_node_transform)
end

-- function EveryDayRechargeView:LXRCUpdate(now_time, elapse_time)
--     if not self.lxcz_is_foot_view then
--         return
--     end

--     if self.lxcz_next_create_footprint_time == 0 then
--         self:LXRCCreateFootPrint()
--         self.lxcz_next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
--     end

--     if self.lxcz_next_create_footprint_time == nil then --初生时也是位置改变，不播
--         self.lxcz_next_create_footprint_time = 0
--     end

--     if self.lxcz_next_create_footprint_time > 0 and now_time >= self.lxcz_next_create_footprint_time then
--         self.lxcz_next_create_footprint_time = 0
--     end

--     self:LXRCUpdateFootprintPos()
-- end

-- function EveryDayRechargeView:LXRCCreateFootPrint()
--     if nil == self.lxcz_foot_effect_id then
--         return
--     end

--     if nil == self.lxcz_footprint_eff_t then
--         self.lxcz_footprint_eff_t = {}
--     end

--     local pos = self.lxcz_role_model.draw_obj:GetRoot().transform
--     local bundle, asset = ResPath.GetUIFootEffect(self.lxcz_foot_effect_id)
--     EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x , pos.position.y, pos.position.z), nil, pos, nil, function(obj)
--         if obj then
--             if nil ~= obj then
--                 if self.lxcz_role_model then
--                     obj.transform.localPosition = Vector3.zero
--                     obj:SetActive(false)
--                     obj:SetActive(true)
--                     table.insert(self.lxcz_footprint_eff_t, {obj = obj, lxcz_role_model = self.lxcz_role_model})
--                     self.lxcz_role_model:OnAddGameobject(obj)
--                 else
--                     ResPoolMgr:Release(obj)
--                 end
--             end
--         end
--     end)

--     if #self.lxcz_footprint_eff_t > 2 then
--         local obj = table.remove(self.lxcz_footprint_eff_t, 1)
--         obj.lxcz_role_model:OnRemoveGameObject(obj.obj)
--         if not IsNil(obj.obj) then
--             obj.obj:SetActive(false)
--         end
--     end
-- end

-- function EveryDayRechargeView:LXRCUpdateFootprintPos()
--     if nil == self.lxcz_footprint_eff_t then
--         return
--     end

--     for k,v in pairs(self.lxcz_footprint_eff_t) do
--         if not IsNil(v.obj) then
--             local pos = v.obj.transform.localPosition
--             v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
--         end
--     end
-- end

-- function EveryDayRechargeView:LXRCClearFootEff()
--     if self.lxcz_footprint_eff_t ~= nil then
--         for k,v in pairs(self.lxcz_footprint_eff_t) do
--             if v.obj ~= nil and not IsNil(v.obj) and v.lxcz_role_model ~= nil then
--                 v.lxcz_role_model:OnRemoveGameObject(v.obj)
--                 v.obj:SetActive(false)
--             end
--         end
--     end

--     self.lxcz_footprint_eff_t = {}
-- end

----设置模型显示参数
function EveryDayRechargeView:LXRCChangeModelShowScale()
    local scale_cfg = EveryDayLianXuRechargeWGData.Instance:GetShowModelParam(self.lxcz_group_index, self.lxcz_cur_btn_type)
    if IsEmptyTable(scale_cfg) then
        return
    end

    local scale, pos_str, rotate_str

    if self.lxcz_role_model then
        pos_str = scale_cfg.whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPositionXY(self.node_list.lxcz_ph_display.rect, pos[1] or 0, pos[2] or 0)
		end

		pos_str = scale_cfg.model_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			self.lxcz_role_model:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		rotate_str = scale_cfg.main_rotation
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			self.lxcz_role_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end

		scale = scale_cfg.main_scale
		if scale and scale ~= "" then
			self.lxcz_role_model:SetRTAdjustmentRootLocalScale(scale)
		end
    end

    if self.node_list["lxcz_xw_root"]:GetActive() then
        if self.lxcz_xianwa_model then
            pos_str = scale_cfg.wx_display_pos
            if pos_str and pos_str ~= "" then
                local pos = Split(pos_str, "|")
                RectTransform.SetAnchoredPositionXY(self.node_list.lxcz_xw_display.rect, pos[1] or 0, pos[2] or 0)
            end

            pos_str = scale_cfg.xw_pos
            if pos_str and pos_str ~= "" then
                local pos = Split(pos_str, "|")
                self.lxcz_xianwa_model:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
            end

            rotate_str = scale_cfg.xw_rotation
            if rotate_str and rotate_str ~= "" then
                local rot = Split(rotate_str, "|")
                self.lxcz_xianwa_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
            end

            scale = scale_cfg.xw_scale
            if scale and scale ~= "" then
                self.lxcz_xianwa_model:SetRTAdjustmentRootLocalScale(scale)
            end
        end
    end
end


---------------------------------------------------模型设置相关 end





--设置天神
function EveryDayRechargeView:LXRCSetTianShenModel(item_id)
    -- local ts_active_cfg = TianShenWGData.Instance:GetTianShenActItemCfgByActId(item_id)
    -- if not ts_active_cfg then
    --     return
    -- end
    -- self.lxcz_tianshen_index = ts_active_cfg.index
    -- local ts_appe_cfg = TianShenWGData.Instance:GetTianShenCfg(ts_active_cfg.index)--形象配置
    -- if ts_appe_cfg then
    --     self.lxcz_show_model:SetTianShenModel(ts_appe_cfg.appe_image_id, ts_appe_cfg.index, false, nil, SceneObjAnimator.Rest)
    -- end
end

function EveryDayRechargeView:LXRCOnClickTipBtn()
    RuleTip.Instance:SetContent(Language.RebateRecharge.LXCZTipDescent, Language.RebateRecharge.LXCZTipTitle)
end

--点击领取普通奖励
function EveryDayRechargeView:LXRCOnClickGetRewardBtn()
    if not self.lxcz_cur_down_show_cfg then
        return
    end

    local cur_recharge_day = EveryDayLianXuRechargeWGData.Instance:GetCurDayByRechargeType(self.lxcz_cur_btn_type)              --天数
    local cur_cfg = EveryDayLianXuRechargeWGData.Instance:GetRewardCfgByRechargeDat(self.lxcz_cur_btn_type, cur_recharge_day)
    if IsEmptyTable(cur_cfg) then return end
    self.lxcz_cur_down_show_cfg = cur_cfg
    local reward_state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish
    if cur_cfg.reward_index then
        reward_state = EveryDayLianXuRechargeWGData.Instance:GetRewardState(self.lxcz_cur_btn_type, cur_cfg.reward_index + 1, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal)
    end

    local reward_item_list = {}
    if not IsEmptyTable(cur_cfg.reward_item) then
        reward_item_list = SortDataByItemColor(cur_cfg.reward_item)
    end

    if reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet then
        ServerActivityWGCtrl.Instance:SendLianXuChongZhiReq(self.lxcz_cur_btn_type, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal, self.lxcz_cur_down_show_cfg.reward_index)
        TipWGCtrl.Instance:ShowGetReward(nil, reward_item_list, nil, nil, nil, true)--通用奖励展示
    else
        -- local data_list =
        -- {
        --     view_type = RewardShowViewType.Normal,
        --     reward_item_list = reward_item_list
        -- }
        -- RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)--物品浏览
        ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
    end
end

function EveryDayRechargeView:LXRCOnClickJumpRechargeBtn()
    ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
end

function EveryDayRechargeView:LXRCResetModelItemList()
    self.lxcz_show_model_item_list = {}
end

function EveryDayRechargeView:LXRCSetModelItemList(item_id)
    -- print_error("FFFFF======= item_id", item_id)
    table.insert(self.lxcz_show_model_item_list, item_id)
end

--设置战力显示
function EveryDayRechargeView:LXRCSetCapabilityShow()
    local power_num = 0
    -- print_error("FFF==== self.lxcz_show_model_item_list", #self.lxcz_show_model_item_list, self.lxcz_show_model_item_list)
    if not IsEmptyTable(self.lxcz_show_model_item_list) then
        for k, item_id in pairs(self.lxcz_show_model_item_list) do
            -- print_error("FFFFF====== 战力", item_id, ItemShowWGData.CalculateCapability(item_id))
            power_num = power_num + ItemShowWGData.CalculateCapability(item_id)
        end
    end
    if self.node_list["lxcz_txt_zhanli"] then
        self.node_list["lxcz_txt_zhanli"].text.text = power_num
    end
end

function EveryDayRechargeView:LXRCDoAnimation()
    local tween_info = UITween_CONSTS.EveryDayRechargeSys
    UITween.FakeHideShow(self.node_list["lxcz_box_container"])
    local tween = UITween.AlphaShow(GuideModuleName.EveryDayRechargeView, self.node_list["lxcz_box_container"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

--[[
function EveryDayRechargeView:LXRCDoCellListAnimation()
    local tween_info = UITween_CONSTS.EveryDayRechargeSys.ListCellRender
    self.node_list["lxcz_reward_item_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["lxcz_reward_item_list"]:SetActive(true)
        local list = self.lxcz_reward_item_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        local max_count = #sort_list
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end

            v.item:PalyLXRCItemAnim(count, count == max_count, BindTool.Bind(self.AllCellAnimationComplete, self))
        end
    end, tween_info.DelayDoTime, "LXRC_Cell_Tween")
end
--]]

function EveryDayRechargeView:AllCellAnimationComplete()
    if not self:IsOpen() then
        return
    end

    self.leichong_limit_jump = false
    self:LXRCFlushRewardList()
end
--------------------------EveryDayLXRechargeSliderItem start--------------------------------------------
--进度条
EveryDayLXRechargeSliderItem = EveryDayLXRechargeSliderItem or BaseClass(BaseRender)
function EveryDayLXRechargeSliderItem:__init()
    self.reward_item_cell = ItemCell.New(self.node_list["item_pos"])
    self.reward_item_cell:SetIsUseRoundQualityBg(true)
    self.reward_item_cell:SetCellBgEnabled(false)
    self.reward_item_cell:NeedDefaultEff(false)
    self.node_list["item_eff_pos"].button:AddClickListener(BindTool.Bind(self.OnGetSliderReward, self))
    -- self.node_list.btn_no_get.button:AddClickListener(BindTool.Bind(self.OnClickGetNotFinishBtn, self))
end

function EveryDayLXRechargeSliderItem:__delete()
    if self.reward_item_cell then
        self.reward_item_cell:DeleteMe()
        self.reward_item_cell = nil
    end
end

function EveryDayLXRechargeSliderItem:OnFlush()
    if not self.data and not self.data.rare_reward_item and not self.data.need_chongzhi_day then
        return
    end

    local btn_type = EveryDayLianXuRechargeWGData.Instance:GetCacheBtnType()
    -- local reward_day = EveryDayLianXuRechargeWGData.Instance:TransitionRewardDay(self.data.reward_index)
    local reward_type = EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Stage
    local reward_state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish

    if self.data.reward_index then
        reward_state = EveryDayLianXuRechargeWGData.Instance:GetRewardState(btn_type, self.data.reward_index + 1, reward_type)
    end
    self.node_list["item_bg"]:SetActive(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish)
    self.node_list["item_eff_pos"]:SetActive(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet)--领取
    local total_day = EveryDayLianXuRechargeWGData.Instance:GetTotalDayByRechargeType(btn_type)
    self.node_list["text_lie"].text.text = string.format(Language.RebateRecharge.LXCZLeiJiDay, self.data.need_chongzhi_day)
    local total_day_new = total_day < self.data.need_chongzhi_day and ToColorStr(total_day, "#fff8bb") or total_day

    local can_get = reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet
    self.node_list["text_hang"].text.text = can_get and Language.RebateRecharge.LXCZLeiKeLingQu
        or string.format(Language.RebateRecharge.LXCZLeiKeLingDay, total_day_new, self.data.need_chongzhi_day)

    --奖励物品设置
    local rewrad_item_cfg = self.data.rare_reward_item[0] or {}
    local item_id = rewrad_item_cfg.item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    local is_ylq = reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet and true or false
    if item_cfg then
        self.reward_item_cell:SetUseButton(not can_get)
        self.reward_item_cell:SetData(rewrad_item_cfg)
        self.reward_item_cell:TrySetActive("item_icon", nil, false)
        self.node_list.item_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end
    if is_ylq then
        self.node_list["text_hang"].text.text = ""
    end

	self.node_list.ylq:SetActive(is_ylq)
	self.node_list.ylq_bg:SetActive(is_ylq)
end

function EveryDayLXRechargeSliderItem:OnGetSliderReward()
    if not self.data then
        return
    end
    local btn_type = EveryDayLianXuRechargeWGData.Instance:GetCacheBtnType()
    ServerActivityWGCtrl.Instance:SendLianXuChongZhiReq(btn_type, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Stage, self.data.reward_index)
    -- print_error("点击领取进度条奖励 btn_type", btn_type, self.data.need_chongzhi_day)
end

function EveryDayLXRechargeSliderItem:OnClickGetNotFinishBtn()
    SysMsgWGCtrl.Instance:ErrorRemind(Language.RebateRecharge.ConditionNo)
end

--------------------------EveryDayLXRechargeSliderItem end----------------------------------------------

--------------------------EveryDayLXRechargeRewardItem start--------------------------------------------
--[[--下方奖励--屏蔽
EveryDayLXRechargeRewardItem = EveryDayLXRechargeRewardItem or BaseClass(BaseRender)
function EveryDayLXRechargeRewardItem:__init()
    self.reward_item_cell = ItemCell.New(self.node_list["item_pos"])
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnGetReward, self))
end

function EveryDayLXRechargeRewardItem:__delete()
    if self.reward_item_cell then
        self.reward_item_cell:DeleteMe()
        self.reward_item_cell = nil
    end
end

function EveryDayLXRechargeRewardItem:OnFlush()
    if not self.data and not self.data.reward_item then
        return
    end
    local rewrad_item_cfg = self.data.reward_item[0] or {}
    local btn_type = EveryDayLianXuRechargeWGData.Instance:GetCacheBtnType()
    -- local reward_day = EveryDayLianXuRechargeWGData.Instance:TransitionRewardDay(self.data.reward_index)
    local reward_type = EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal
    local reward_state = EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish

    if self.data.reward_index then
        reward_state = EveryDayLianXuRechargeWGData.Instance:GetRewardState(btn_type, self.data.reward_index + 1, reward_type)
    end

    --按钮判断
    self.node_list["get_btn"]:SetActive(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet)--领取
    self.node_list["effect"]:SetActive(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.CanGet)--领取
    self.node_list["not_finish"]:SetActive(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.NotFinish)--未达成
    self.node_list["is_get"]:SetActive(reward_state == EVERYDAY_LIANXU_RECHARGE_REWARD_STATE.HasGet)--已领取

    self.reward_item_cell:SetData(rewrad_item_cfg)
    self.node_list["text_recharge"].text.text = string.format(Language.RebateRecharge.TotalRechargeDay_2, self.data.need_chongzhi_day)
end

function EveryDayLXRechargeRewardItem:OnGetReward()
    if not self.data then
        return
    end
    local btn_type = EveryDayLianXuRechargeWGData.Instance:GetCacheBtnType()
    ServerActivityWGCtrl.Instance:SendLianXuChongZhiReq(btn_type, EVERYDAY_LIANXU_RECHARGE_REWARD_TYPE.Normal, self.data.reward_index)
    -- print_error("点击领取普通奖励 btn_type", btn_type, self.data.need_chongzhi_day)
end

function EveryDayLXRechargeRewardItem:PalyLXRCItemAnim(item_index, is_max, call_back)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.EveryDayRechargeSys.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            if is_max then
                UITween.MoveAlphaShow(GuideModuleName.EveryDayRechargeView, self.node_list["tween_root"], tween_info, call_back)
            else
                UITween.MoveAlphaShow(GuideModuleName.EveryDayRechargeView, self.node_list["tween_root"], tween_info)
            end
        end

    end, tween_info.NextDoDelay * wait_index, "EveryDayLXRechargeRewardItem_Anim" .. wait_index)
end
--]]
--------------------------EveryDayLXRechargeRewardItem end----------------------------------------------