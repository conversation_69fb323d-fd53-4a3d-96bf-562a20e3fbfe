
function QuanMinBeiZhanView:InitDBView()
	XUI.AddClickEventListener(self.node_list.db_btn_tip, BindTool.Bind(self.OnDBBtnTipClickHnadler,self))

	local theme_cfg = TianshenRoadWGData.Instance:GetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_duobei)
	if theme_cfg ~= nil then
		self.node_list.db_tip_label.text.text = theme_cfg.rule_tip
	end

	self.db_reward_list = AsyncListView.New(BZDuoBeiItemRender, self.node_list.db_list)

	self:FlushDBView()
	self:DBTimeCountDown()
end

function QuanMinBeiZhanView:ReleaseDBView()
	if self.db_reward_list then
		self.db_reward_list:DeleteMe()
		self.db_reward_list = nil
	end
	CountDownManager.Instance:RemoveCountDown("quanminbeizhan_duobei_count_down")
end

function QuanMinBeiZhanView:FlushDBView()
	self:FlushDBReward()
end

function QuanMinBeiZhanView:FlushDBReward()
	local info_list = QuanMinBeiZhanWGData.Instance:GetDuoBeiInfo()
	if info_list and self.db_reward_list then
		self.db_reward_list:SetDataList(info_list)
	end
end

function QuanMinBeiZhanView:OnDBBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = QuanMinBeiZhanWGData.Instance:GetActivityTip(TabIndex.quanmin_beizhan_duobei)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

--有效时间倒计时
function QuanMinBeiZhanView:DBTimeCountDown()
	CountDownManager.Instance:RemoveCountDown("quanminbeizhan_duobei_count_down")
	local invalid_time = QuanMinBeiZhanWGData.Instance:GetActivityInValidTime(TabIndex.quanmin_beizhan_duobei)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - server_time)
		CountDownManager.Instance:AddCountDown("quanminbeizhan_duobei_count_down", BindTool.Bind1(self.UpdateDBCountDown, self), BindTool.Bind1(self.DBTimeCountDown, self), invalid_time, nil, 1)
	end
end

function QuanMinBeiZhanView:UpdateDBCountDown(elapse_time, total_time)
	self.node_list.db_time_label.text.text = TimeUtil.FormatSecondDHM2(total_time - elapse_time)
end

-------------------------------------

BZDuoBeiItemRender = BZDuoBeiItemRender or BaseClass(BaseRender)

function BZDuoBeiItemRender:LoadCallBack()
	self.db_reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClickDuoBei, self))
end

function BZDuoBeiItemRender:__delete()
	if self.db_reward_item_list then
		self.db_reward_item_list:DeleteMe()
		self.db_reward_item_list = nil
	end
end

function BZDuoBeiItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local bundle, asset = ResPath.GetMainUIIcon(data.cfg.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset)
	self.node_list.beishu.text.text = string.format("x%d", data.cfg.reward_mult)
	self.node_list.name.text.text = data.cfg.wanfa_name

	if IsEmptyTable(data.cfg.reward_item) then
		local reward_list = TianshenRoadWGData.Instance:GetDuoBeiRewardListByTaskType(data.cfg.task_type)
		self.db_reward_item_list:SetDataList(reward_list)
	else
		local list = SortTableKey(data.cfg.reward_item)
		self.db_reward_item_list:SetDataList(list)
	end
end

function BZDuoBeiItemRender:OnBtnClickDuoBei()
	local data = self:GetData()
	if data and data.cfg then
		FunOpen.Instance:OpenViewNameByCfg(data.cfg.panel)
	end
end