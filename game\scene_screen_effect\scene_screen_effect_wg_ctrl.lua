require("game/scene_screen_effect/scene_screen_effect_wg_data")
require("game/scene_screen_effect/scene_screen_effect_view")

-- 场景特定区域屏幕特效控制
SceneScreenEffectWGCtrl = SceneScreenEffectWGCtrl or BaseClass(BaseWGCtrl)
function SceneScreenEffectWGCtrl:__init()
	if SceneScreenEffectWGCtrl.Instance ~= nil then
		ErrorLog("[SceneScreenEffectWGCtrl] attempt to create singleton twice!")
		return
	end
	SceneScreenEffectWGCtrl.Instance = self

	self.update_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.Update, self), 0.5)

	self.data = SceneScreenEffectWGData.New()
	self.view = SceneScreenEffectView.New(GuideModuleName.SceneScreenEffectView)

	self.cur_view_effect = nil
	self.old_in_effect_area = false
	self.scene_audio_play_timestemp = 0
end

function SceneScreenEffectWGCtrl:__delete()
	SceneScreenEffectWGCtrl.Instance = nil
	self.old_in_effect_area = false
	self.scene_audio_play_timestemp = 0

	GlobalTimerQuest:CancelQuest(self.update_timer)
	self.update_timer = nil

	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil
end

function SceneScreenEffectWGCtrl:Update()
	self:CheckEnterAreaTrigger()
end

-- 检查移动到某个区域触发
function SceneScreenEffectWGCtrl:CheckEnterAreaTrigger()
	local cfg = SceneScreenEffectWGData.Instance:GetCurSceneEffectCfg()
	local in_effect_area = false 		-- 是否在需要显示特效的区域
	if not IsEmptyTable(cfg) then
		local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
		for i,v in ipairs(cfg) do
			if GameMath.IsInPolygon(v.pos_list, {x = role_x, y = role_y}) and nil ~= v.cfg then
				in_effect_area = true
				if self.old_in_effect_area ~= in_effect_area then
					self.old_in_effect_area = in_effect_area
					if not ViewManager.Instance:IsOpen(GuideModuleName.SceneScreenEffectView) then
						ViewManager.Instance:Open(GuideModuleName.SceneScreenEffectView, nil, "all", {data = v.cfg})
						self.cur_view_effect = v.cfg.effcet_name
					end
				end

				-- 全屏特效面板
				if v.cfg.effcet_name ~= "" and self.cur_view_effect ~= v.cfg.effcet_name then
					ViewManager.Instance:FlushView(GuideModuleName.SceneScreenEffectView, nil, "all", {data = v.cfg})
					self.cur_view_effect = v.cfg.effcet_name
				end

				if v.cfg.audio_name ~= "" and self.scene_audio_play_timestemp <= Status.NowTime then
					self.scene_audio_play_timestemp = Status.NowTime + (v.cfg.audio_cd ~= 0 and v.cfg.audio_cd or 5)
					AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Thunder))
				end

				break
			end
		end
	end

	if not in_effect_area and self.old_in_effect_area ~= in_effect_area then
		self.old_in_effect_area = in_effect_area
		ViewManager.Instance:Close(GuideModuleName.SceneScreenEffectView)
	end
end