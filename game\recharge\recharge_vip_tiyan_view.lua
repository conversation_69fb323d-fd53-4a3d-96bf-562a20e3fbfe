VipTiYanView = VipTiYanView or BaseClass(SafeBaseView)

function VipTiYanView:__init()
	self.can_do_fade = false
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/recharge_ui_prefab", "layout_guizutequan")

	self.star_count_down_mark = false
	self.star_count_down_mark_2 = false
	self.loading_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind1(self.MainUiLoadingCallBack, self))
	self.card_seq = -1
end

function VipTiYanView:__delete()
	self.card_seq = -1
end

function VipTiYanView:ReleaseCallBack()
	if self.loading_event then
		GlobalEventSystem:UnBind(self.loading_event)
		self.loading_event = nil
	end
	CountDownManager.Instance:RemoveCountDown("vip_trial_timer")
	CountDownManager.Instance:RemoveCountDown("vip_trial_timer2")
end

function VipTiYanView:Open()
	if IS_AUDIT_VERSION then
		return
	end
	SafeBaseView.Open(self)
end

function VipTiYanView:LoadCallBack()
	self:InitListener()
	self:InitTitle()
	if self.star_count_down_mark then
		local can_get_card = VipWGData.Instance:CheckCanGetVipCardReward(self.card_seq)
		if can_get_card then
			self:StraCountDown()
		end
	end

	if self.star_count_down_mark_2 then
		self:StraCountDown2()
	end

	self:DoOpenPanelTween()
end

function VipTiYanView:CloseCallBack()
	local can_get_card = VipWGData.Instance:CheckCanGetVipCardReward(self.card_seq)
	if can_get_card then
		self:OnClickOperate()
	end
end

function VipTiYanView:MainUiLoadingCallBack()
	-- self:StraCountDown()
	GlobalEventSystem:UnBind(self.loading_event)
	self.loading_event = nil
end

function VipTiYanView:InitListener()
	XUI.AddClickEventListener(self.node_list.zb_btn_operate, BindTool.Bind(self.OnClickOperate, self))
	XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind1(self.Close, self))
end

function VipTiYanView:InitTitle()
	local title_item_id = VipWGData.Instance:GetVipOtherInfo("vip_title")
	local title_cfg = TitleWGData.Instance:GetTitleConfigByItemId(title_item_id)
	local bundle,asset = ResPath.GetTitleModel(title_cfg.title_id)
end

function VipTiYanView:OnFlush(param_list, index)
	self:RefreshView()
end

function VipTiYanView:RefreshView()
	local mark, seq = VipWGData.Instance:CanGetFreeCard()
	if not mark then
		-- self:Close()
		local has_free_card, card_seq = VipWGData.Instance:CheckHasFreeCard()
		seq = card_seq
		-- return
	end

	self.card_seq = seq

	if mark then
		self:StraCountDown()
		self.node_list.zb_btn_operate_text.text.text = ""
	else
		self:StraCountDown2()
		self.node_list.count_down_label.text.text = ""
	end
	
	self.node_list.zb_btn_operate:SetActive(mark)

	local card_cfg = VipWGData.Instance:GetVipCardCfg(self.card_seq)
	local text = VipWGData.Instance:GetTiYanText(card_cfg.vip_up_lv)
	self.node_list.tiyan_text.text.text = text
end

function VipTiYanView:StraCountDown()
	if self:IsLoaded() then
		self.node_list.count_down_label.text.text = string.format(Language.Vip.VipTips_8, 10)
		CountDownManager.Instance:AddCountDown("vip_trial_timer", function (time, total_time)
			self.node_list.count_down_label.text.text = string.format(Language.Vip.VipTips_8, total_time - time)
		end, function ()
			self:OnClickOperate()
		end, nil, 10, 1)
	else
		self.star_count_down_mark = true
	end
end

function VipTiYanView:StraCountDown2()
	if self:IsLoaded() then
		local card_cfg = VipWGData.Instance:GetVipCardCfg(self.card_seq)
		if card_cfg.active_condition == VipWGData.VipCardActiveType.online then
			local online_time_s = TimeWGCtrl.Instance:GetOnlineTimes()
			local time = card_cfg.active_value * 60 - online_time_s
			time = time > 0 and time or 0
			if time > 0 then
				self.node_list.zb_btn_operate_text.text.text = string.format(Language.Vip.VipTips_26, TimeUtil.FormatSecondDHM6(time), card_cfg.vip_up_lv)
				self:StarCountDownTime(time)
			else
				self.node_list.zb_btn_operate_text.text.text = ""
				self.node_list.zb_btn_operate:SetActive(true)
			end
		elseif card_cfg.active_condition == VipWGData.VipCardActiveType.day then
			local login_day_count = ServerActivityWGData.Instance:GetTotalLoginDays()
			if login_day_count < card_cfg.active_value then
				local now_time = TimeWGCtrl.Instance:GetServerTime()
				local end_time = TimeWGCtrl.Instance:NowDayTimeEnd(now_time)
				local time = end_time - now_time
				self.node_list.zb_btn_operate_text.text.text = string.format(Language.Vip.VipTips_26, TimeUtil.FormatSecondDHM6(time), card_cfg.vip_up_lv)
				self:StarCountDownTime(time)
			else
				self.node_list.zb_btn_operate_text.text.text = ""
				self.node_list.zb_btn_operate:SetActive(true)
			end
		end
	else
		self.star_count_down_mark_2 = true
	end
end

function VipTiYanView:StarCountDownTime(total_time)
	local card_cfg = VipWGData.Instance:GetVipCardCfg(self.card_seq)
	CountDownManager.Instance:AddCountDown("vip_trial_timer2", function (time, total_time)
			self.node_list.zb_btn_operate_text.text.text = string.format(Language.Vip.VipTips_26, TimeUtil.FormatSecondDHM6(total_time - time), card_cfg.vip_up_lv)
		end, function ()
			RemindManager.Instance:Fire(RemindName.Vip_Col)
			self:Close()
	end, nil, total_time + 1, 1)
end

function VipTiYanView:OnClickOperate()
	local can_get_card = VipWGData.Instance:CheckCanGetVipCardReward(self.card_seq)
	if can_get_card then
		if self.card_seq ~= -1 then
			VipWGCtrl.Instance:SendBuyVipTimeCard(BUY_VIPTIME_CARD_TYPE.OP_TYPE_ACTIVE_VIP_CARD, self.card_seq)
			CountDownManager.Instance:RemoveCountDown("vip_trial_timer")
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotCanGetTime)
	end
	self:Close()
end

function VipTiYanView:DoOpenPanelTween()
	self.node_list.panel_root.canvas_group.alpha = 0
	UITween.ScaleShowPanel(self.node_list.panel_bg, Vector3(0,1,0), 0.15, DG.Tweening.Ease.Linear, function ()
		self.node_list.panel_root.canvas_group:DoAlpha(0, 1, 0.15)
	end)
end