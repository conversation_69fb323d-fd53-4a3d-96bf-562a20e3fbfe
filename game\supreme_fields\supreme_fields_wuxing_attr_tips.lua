SupremeFieldsWuxingAttrTips = SupremeFieldsWuxingAttrTips or BaseClass(SafeBaseView)

function SupremeFieldsWuxingAttrTips:__init()
	self.view_name = "SupremeFieldsWuxingAttrTips"

	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/supreme_fields_ui_prefab", "layout_supreme_fields_wuxing_attr_tip")
end

function SupremeFieldsWuxingAttrTips:ReleaseCallBack()
	if nil ~= self.attr_obj_list then
		for k, v in pairs(self.attr_obj_list) do
			v:DeleteMe()
		end
		self.attr_obj_list = nil
	end

	if nil ~= self.late_attr_obj_list then
		for k, v in pairs(self.late_attr_obj_list) do
			v:DeleteMe()
		end
		self.late_attr_obj_list = nil
	end
end

function SupremeFieldsWuxingAttrTips:LoadCallBack()
	if nil == self.attr_obj_list then
		self.attr_obj_list = {}
		local attr_num = self.node_list.wuxing_cur_attr_list.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAddAttrRender.New(self.node_list.wuxing_cur_attr_list:FindObj("attr_" .. i))
			cell:SetAttrNameNeedSpace(true)
			cell:SetIndex(i)
			self.attr_obj_list[i] = cell
		end
	end

	if nil == self.late_attr_obj_list then
		self.late_attr_obj_list = {}
		local attr_num = self.node_list.wuxing_last_attr_list.transform.childCount
		for i = 1, attr_num do
			local cell = CommonAddAttrRender.New(self.node_list.wuxing_last_attr_list:FindObj("attr_" .. i))
			cell:SetAttrNameNeedSpace(true)
			cell:SetIndex(i)
			self.late_attr_obj_list[i] = cell
		end
	end

	XUI.AddClickEventListener(self.node_list.wuxing_activate_attr_btn, BindTool.Bind(self.OnBtnActivateAttr, self))
end

function SupremeFieldsWuxingAttrTips:OnFlush()
	local lv = SupremeFieldsWGData.Instance:GetSingleTotalStarAttrInfo()
	local cur_lv, late_lv, cur_attr_list, late_attr_list = SupremeFieldsWGData.Instance:GetWuXingTotalAttrList(lv)
	if IsEmptyTable(cur_attr_list) or IsEmptyTable(late_attr_list) then
		return
	end

	self.node_list.wuxing_cur_attr_title_text.text.text = string.format(
		Language.SupremeFields.WuXing_Field_All_Attr_Text, cur_lv)
	self.node_list.wuxing_last_attr_title_text.text.text = string.format(
		Language.SupremeFields.WuXing_Field_All_Attr_Text, late_lv)

	if cur_lv ~= 0 then
		self:SetAttr(self.attr_obj_list, cur_attr_list)
	end

	if cur_lv ~= late_lv then
		self:SetAttr(self.late_attr_obj_list, late_attr_list)
	end

	self.node_list.cur_attr_group:SetActive(cur_lv ~= 0)
	self.node_list.common_no_data_panel:SetActive(cur_lv == 0)

	self.node_list.last_attr_group:SetActive(cur_lv ~= late_lv)
	self.node_list.common_no_data_panel2:SetActive(cur_lv == late_lv)

	self:SetBtnRemind()
end

function SupremeFieldsWuxingAttrTips:SetAttr(obj, attr_list)
	if not IsEmptyTable(attr_list) then
		for k, v in ipairs(obj) do
			v:SetData(attr_list[k])
		end
	end
end

function SupremeFieldsWuxingAttrTips:SetBtnRemind()
	local is_red = SupremeFieldsWGData.Instance:GetWuXingTotalAttrIsCanActivate()
	self.node_list.remind:SetActive(is_red)
	XUI.SetButtonEnabled(self.node_list.wuxing_activate_attr_btn, is_red)
end

function SupremeFieldsWuxingAttrTips:OnBtnActivateAttr()
	SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.ACTIVATE_TOTA_STAR_ATTR)
end
