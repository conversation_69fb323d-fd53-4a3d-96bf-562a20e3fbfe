-------------------------------------------------------------------

DAILYFIND_REWARD_TYPE =
{
	OFFLINE_HOURS = 7, -- 离线时间
	OFFLINE_EXP = 8, -- 离线经验
	TIMES = 9, -- 找回个数
	ITEM = 10, --物品

	EXP = 1, --经验
	COIN = 2, --铜币
	XIANHUN = 3, --仙魂
	YUANLI = 4, --真气
	GONGXIAN = 5, --贡献
	HONOR = 6, --荣誉
	LINGLI = 11, --灵力
	SHENGWANG = 12, --声望
	GUIDE_EXP = 13, --公会建设度
	BIND_GOLD = 14, --绑元
	REWARD_SLIVER_TICKET = 15, --元宝


	MAX = 8,
}

local MaxVipLevel = 12

DAILYFIND_REWARD_ITEM = {
	[DAILYFIND_REWARD_TYPE.EXP] = COMMON_CONSTS.VIRTUAL_ITEM_SAMLL_EXP,
	[DAILYFIND_REWARD_TYPE.COIN] = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN,
	[DAILYFIND_REWARD_TYPE.XIANHUN] = COMMON_CONSTS.VIRTUAL_ITEM_XIANHUN,
	[DAILYFIND_REWARD_TYPE.YUANLI] = COMMON_CONSTS.VIRTUAL_ITEM_ZHENGQI,
	[DAILYFIND_REWARD_TYPE.GONGXIAN] = COMMON_CONSTS.VIRTUAL_ITEM_GONGXIAN,
	[DAILYFIND_REWARD_TYPE.HONOR] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR,
	[DAILYFIND_REWARD_TYPE.OFFLINE_EXP] = COMMON_CONSTS.VIRTUAL_ITEM_EXP,
	[DAILYFIND_REWARD_TYPE.LINGLI] = COMMON_CONSTS.VIRTUAL_ITEM_LINGLI,
	[DAILYFIND_REWARD_TYPE.SHENGWANG] = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR,
	[DAILYFIND_REWARD_TYPE.GUIDE_EXP] = COMMON_CONSTS.VIRTUAL_ITEM_90735,
	[DAILYFIND_REWARD_TYPE.BIND_GOLD] = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD,
	[DAILYFIND_REWARD_TYPE.REWARD_SLIVER_TICKET] = COMMON_CONSTS.VIRTUAL_ITEM_YUANBAO,
}

DailyFindRender = DailyFindRender or BaseClass(BaseGridRender)
function DailyFindRender:__init()

end

function DailyFindRender:__delete()
	self.rich_reward_list = {}
	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end

	if self.cell_list then
		for k, v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end

	self.icon_name = nil
end

function DailyFindRender:LoadCallBack()
	self.rich_reward_list = {}
	self.gold_cost = 0
	if not self.cell_list then
		self.cell_list = {}
	end

	self.data_list = {}

	--self.node_list.Button.button:AddClickListener(BindTool.Bind(self.OnClickRewardHnadler, self))
	self.node_list.Btn_zw.button:AddClickListener(BindTool.Bind(self.OnClickRewardHnadler, self))

	self.Btn_zw_block = self.node_list.Btn_zw:GetComponent("UIBlock")
end

function DailyFindRender:OnFlush()
	if not self.data or not self.data.plusvo then
		return
	end


	local dailyfind_reward_cfg = WelfareWGData.Instance:GetNewDailyFindRewardCfg(self.data.big_type,
		self.data.plusvo.find_type, self.data.btn_flag - 1, self.data.role_level)

	if not dailyfind_reward_cfg then
		--策划会通过等级来屏蔽配置，拿不到可能是屏蔽了
		-- print_error("拿不到配置：找回类型（任务或活动），配置的找回类型，仙玉或元宝找回，服务器下发的等级"
		-- 	, self.data.big_type, self.data.plusvo.find_type, self.data.btn_flag - 1, self.data.role_level)
		return
	end

	local vip_times = self.data.vip_times

	if self.data.btn_flag == 1 then
		-- 2 可元宝及免费找回 ,只有仙玉才能找回VIP次数
		vip_times = self.data.plusvo.is_open == 2 and 0 or self.data.vip_times
	end

	local total_times = self.data.times + vip_times

	local cost_str = 0
	local vip_str
	if vip_times > 0 then
		--string.format(Language.Welfare.RetrieveName2, self.data.times, vip_times)
		if self.data.times > 0 then
			cost_str = dailyfind_reward_cfg.cost
		else
			cost_str = dailyfind_reward_cfg.vip_extra_cost
		end
		--self.node_list.vip_count.text.text = string.format(Language.BiZuo.ZhaoHuiDesc, vip_times)
		vip_str = string.format(Language.BiZuo.ZhaoHuiDesc, vip_times)
	elseif vip_times == 0 then
		cost_str = dailyfind_reward_cfg.cost
		--self.node_list.vip_count.text.text = ""
		vip_str = ""

		if self.data.btn_flag == 2 and total_times <= 0 then

			local vip_cfg = VipWGData.Instance:GetVipSpecPermissions(self.data.plusvo.vip_type)
			-- local vip_level = RoleWGData.Instance.role_vo.vip_level

			local vip_level = VipWGData.Instance:IsVip() and RoleWGData.Instance.role_vo.vip_level or 0

			if vip_cfg then
				local cur_vip_times = vip_cfg["param_" .. vip_level]
				local max_vip_times = vip_cfg["param_" .. MaxVipLevel]
				if max_vip_times and max_vip_times and max_vip_times - cur_vip_times > 0 then
					--self.node_list.vip_count.text.text = string.format(Language.BiZuo.ZhaoHuiDesc, max_vip_times - cur_vip_times)
					vip_str = string.format(Language.BiZuo.ZhaoHuiDesc, max_vip_times - cur_vip_times)
				end
			end
		end
	end
	self.node_list.rich_desc.text.text = self.data.plusvo.name
	local zh_count_str = string.format(Language.Welfare.RetrieveName5, self.data.times)
	self.node_list.zh_count.text.text = vip_str ~= "" and zh_count_str .. "(" .. vip_str .. ")" or zh_count_str
	self.node_list.cost_text.text.text = cost_str

	local reward_item_list = {}

	local reward_exp = WelfareWGData.Instance:GetNewDailyFindRewardExp(self.data.role_level) or 0
	reward_exp = reward_exp * dailyfind_reward_cfg.exp_factor
	if reward_exp > 0 then
		table.insert(reward_item_list, { item_id = COMMON_CONSTS.VIRTUAL_ITEM_SAMLL_EXP, num = reward_exp })
	end

	local reward_list = self.data.opengame_day > 5 and dailyfind_reward_cfg.openserver_day_1_reward_item or
		dailyfind_reward_cfg.openserver_day_2_reward_item

	for k, v in pairs(reward_list) do
		table.insert(reward_item_list, v)
	end
	reward_item_list = self:SortDataList(reward_item_list)

	local tab = #reward_item_list
	local index = 0
	for i = 1, tab do
		if self.cell_list[i] == nil then
			self.cell_list[i] = ItemCell.New(self.node_list.content)
		end
		self.cell_list[i]:SetActive(true)
		self.cell_list[i]:SetData(reward_item_list[i])
		index = index + 1
	end

	for i = index + 1, #self.cell_list do
		if self.cell_list[i] then
			self.cell_list[i]:SetActive(false)
		end
	end

	if dailyfind_reward_cfg.get_type == 0 then
        local bundle, asset = ResPath.GetCommonIcon("a3_huobi_bangyu")
        self.node_list.img_gold.image:LoadSprite(bundle, asset)
	elseif dailyfind_reward_cfg.get_type == 1 then
		local bundle, asset = ResPath.GetCommonIcon("a3_huobi_xianyu")
        self.node_list.img_gold.image:LoadSprite(bundle, asset)
    end

	self.node_list.normal_bg:SetActive(total_times > 0)
	self.node_list.img_flag:SetActive(total_times <= 0)
	--self.node_list.Button:SetActive(self.data.btn_flag == 1 and total_times > 0)
	self.node_list.btn_text:SetActive((self.data.btn_flag == 1 or self.data.btn_flag == 2) and total_times > 0)
	self.Btn_zw_block.enabled = (self.data.btn_flag == 1 or self.data.btn_flag == 2) and total_times > 0
	self.node_list.img_gold:SetActive(self.data.btn_flag == 2 and total_times > 0)
	self.node_list.cost_text:SetActive(self.data.btn_flag == 2 and total_times > 0)
end

function DailyFindRender:SortDataList(data_list)
	if data_list and not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			if v.item_id and v.item_id > 0 then
				local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
				v.color = item_cfg and item_cfg.color or 0
			else
				v.color = 0
			end
		end
		table.sort(data_list, SortTools.KeyUpperSorter("color"))
	end
	return data_list
end

function DailyFindRender:OnClickRewardHnadler()
	if not self.data or not self.data.plusvo then
		return
	end

	BiZuoWGCtrl.Instance:OpenBatchView(self.data)
end
