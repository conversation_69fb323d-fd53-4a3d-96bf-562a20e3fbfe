ActTreasureWGData = ActTreasureWGData or BaseClass()

function ActTreasureWGData:__init()
	if ActTreasureWGData.Instance ~= nil then
		print("[ActTreasureWGData] attempt to create singleton twice!")
		return
	end
	ActTreasureWGData.Instance = self
	self.national_treasure_process = 0 
	self.open_act_day = 1
	self.times = 0
end
function ActTreasureWGData:__delete()
	ActTreasureWGData.Instance = nil
end

function ActTreasureWGData:UpdataInfoData(protocol)
	self.national_treasure_process = protocol.national_treasure_process
	self.open_act_day = protocol.open_act_day
	self.times = protocol.times

	if self.times >= 1 and self.national_treasure_process == 0 then
		self.is_big_reward = true
	else
		self.is_big_reward = false
	end
end

function ActTreasureWGData:GetTreasureIsBigReward()
	return self.is_big_reward
end

function ActTreasureWGData:GetTreasureData()
	-- local act_cfg = {}
	-- local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	-- local pool_t = cfg.national_treasure
	-- return pool_t
	local act_cfg = {}
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local pool_t = cfg.national_treasure
	local times = self.times
	if self.times > 1 then
		times = 1
	end

	for k,v in pairs(pool_t) do
		if self.open_act_day == v.open_act_day and times == v.times and v.show_icon == 1 then
			table.insert(act_cfg, v)
		end
	end
	return act_cfg
end
--读其他表
function ActTreasureWGData:GetTreasureOtherData()
	local other_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local pool_t = other_cfg.other
    return pool_t
end

function ActTreasureWGData:GetTreasureProcess()
	return self.national_treasure_process
end
function ActTreasureWGData:GetTreasureDay()
	return self.open_act_day
end

function ActTreasureWGData:SetAutoUseGiftFlag(flag)
	self.flag = flag
end

function ActTreasureWGData:GetAutoUseGiftFlag()
	return self.flag or false
end

-- 策划说大奖是礼包时，不显示礼包，显示礼包中的物品
function ActTreasureWGData:GetRewardMax()
	local reward_list = self:GetTreasureData()
	local data = {}
	for k,v in pairs(reward_list) do
		if v.type == 1 and v.show ~= 0 and v.show_icon == 1 then
			data.item_id = v.show
			data.num = 1
			data.is_bind = 0
		elseif v.type == 1 and v.show == 0 and v.show_icon == 1 then
			data = v.reward_item[0]
		end
	end
	return data
end