﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FancyScrollView_AnimScrollViewWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(FancyScrollView.AnimScrollView), typeof(FancyScrollView.FancyScrollView<FancyScrollView.Context>));
		<PERSON><PERSON>ction("SetDataCount", SetDataCount);
		<PERSON><PERSON>unction("OnSelectionChanged", OnSelectionChanged);
		<PERSON><PERSON>Function("SelectNextCell", SelectNextCell);
		<PERSON><PERSON>Function("SelectPrevCell", SelectPrevCell);
		<PERSON><PERSON>unction("SelectCell", SelectCell);
		<PERSON><PERSON>unction("SetContent", SetContent);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDataCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.AnimScrollView obj = (FancyScrollView.AnimScrollView)ToLua.CheckObject<FancyScrollView.AnimScrollView>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetDataCount(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnSelectionChanged(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.AnimScrollView obj = (FancyScrollView.AnimScrollView)ToLua.CheckObject<FancyScrollView.AnimScrollView>(L, 1);
			System.Action<int,FancyScrollView.MovementDirection> arg0 = (System.Action<int,FancyScrollView.MovementDirection>)ToLua.CheckDelegate<System.Action<int,FancyScrollView.MovementDirection>>(L, 2);
			obj.OnSelectionChanged(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SelectNextCell(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FancyScrollView.AnimScrollView obj = (FancyScrollView.AnimScrollView)ToLua.CheckObject<FancyScrollView.AnimScrollView>(L, 1);
			obj.SelectNextCell();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SelectPrevCell(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FancyScrollView.AnimScrollView obj = (FancyScrollView.AnimScrollView)ToLua.CheckObject<FancyScrollView.AnimScrollView>(L, 1);
			obj.SelectPrevCell();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SelectCell(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.AnimScrollView obj = (FancyScrollView.AnimScrollView)ToLua.CheckObject<FancyScrollView.AnimScrollView>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SelectCell(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetContent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.AnimScrollView obj = (FancyScrollView.AnimScrollView)ToLua.CheckObject<FancyScrollView.AnimScrollView>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.SetContent(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

