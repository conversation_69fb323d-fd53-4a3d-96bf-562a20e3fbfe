CustomizedRumorsTipView = CustomizedRumorsTipView or BaseClass(SafeBaseView)

local tween_info = {
	FromScale = Vector3(0, 1, 0),
	ToScale = Vector3(1, 1, 1),
	ScaleTweenTime = 0.5,
	AlphaTweenTime = 0.5,
	ScaleTweenType = DG.Tweening.Ease.Linear,
	AlphaTweenType = DG.Tweening.Ease.Linear,
}

local RUMORS_ANIMATOR_TOTAL_TIME = 10

function CustomizedRumorsTipView:__init()
	-- self:SetMaskBg()
	self.view_name = "CustomizedRumorsTipView"
	self.view_layer = UiLayer.PopWhite

    self.animator_total_time = 8
	self:AddViewResource(0, "uis/view/customized_rumors_ui_prefab", "layout_customized_rumors_tip")
end

function CustomizedRumorsTipView:LoadCallBack()
    if not self.special_rumors_cell then
        self.special_rumors_cell = BaseSpecialRumorsCell.New(self.node_list.rumors_root)
    end
end

function CustomizedRumorsTipView:ReleaseCallBack()
    self.tip_data = nil
    self:CleanAnimatorTime()

    if self.special_rumors_cell then
        self.special_rumors_cell:DeleteMe()
        self.special_rumors_cell = nil
    end
end

function CustomizedRumorsTipView:CloseCallBack()
	if self.close_open_func then
		self.need_stop = false
		self.close_open_func()
		self.close_open_func = nil
	end

	self:CleanAnimatorTime()
end

function CustomizedRumorsTipView:CleanAnimatorTime()
    if self.animator_timer then
        GlobalTimerQuest:CancelQuest(self.animator_timer)
        self.animator_timer = nil
    end
end

function CustomizedRumorsTipView:SetDataList(data)
	if self.tip_data == nil then
		self.tip_data = {}
	end

	table.insert(self.tip_data, data)
end

function CustomizedRumorsTipView:OnFlush()
	if IsEmptyTable(self.tip_data) then
		self:Close()
		return
	end

	UITween.CleanScaleAlaphaShow(self.view_name)
	UITween.DoScaleAlaphaShow(self.view_name, self.node_list.parent_root, tween_info)

    local data = self.tip_data[1]
    self.special_rumors_cell:SetData(data)
	self:CleanAnimatorTime()

	local show_time = RUMORS_ANIMATOR_TOTAL_TIME
	if data.show_time then
		show_time = data.show_time
	else
		local show_cfg = CustomizedRumorsWGData.Instance:GetRumorShowInfoByRumorType(data.rumor_type)
		show_time = show_cfg and show_cfg.show_time or show_time
	end

	self.animator_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.ShowAnimator, self), tween_info.ScaleTweenTime + show_time)--延时处理
end

function CustomizedRumorsTipView:ShowAnimator()
	local parent_tween = self.node_list["parent_root"].canvas_group:DoAlpha(1, 0, 0.5)
	parent_tween:SetEase(DG.Tweening.Ease.OutCubic)
	parent_tween:OnComplete(function()

		table.remove(self.tip_data, 1)
		self:ShowNextRumorsHint()
    end)
end

function CustomizedRumorsTipView:ShowNextRumorsHint()
	if self.need_stop then
		self.tip_data = {}
	end

	self:Flush()
end

function CustomizedRumorsTipView:StopNextShow(close_open_func)
	self.need_stop = true
	self.close_open_func = close_open_func
end