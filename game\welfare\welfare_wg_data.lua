WelfareWGData = WelfareWGData or BaseClass()

--新找回类型
NEW_DAILYFIND_TYPE =
{
	ACTIVITY_FIND = 0,
	TASK_FIND = 1,
}

--新找回货币类型 0：元宝 1：仙玉
NEW_DAILYFIND_MONEY_TYPE =
{
	GOLF_FIND = 0,
	XIANYU_FIND = 1,
}

SEVEN_COMPLETE_STATUS =
{
	WEIDACHENG = 1, 	-- 未达成
	YILINGQU = 0, 		-- 已领取
	KELINGQU = 2, 		-- 可领取
	MingRiKeLingQu = 3, -- 明日可领取
}

SEVENDAY_NOTIFY_REASON =
{
	SDLRI_DAFAULT = 0,								-- 通知原因
	SDLRI_FETCH_REWARD = 1,							-- 领取了七天登录奖励
}

OFFLINE_EXP_FIND_TYPE =
{
	FREE = 0,										-- 离线经验找回 免费
	COIN = 1,										-- 离线经验找回 铜币
	GOLD = 2,										-- 离线经验找回 元宝
	VIP  = 3,                                       -- 离线经验找回 是否vip
	MAX = 4
}

DAILYFIND_GET_TYPE =
{
	FREE = 0,										-- 免费领取
	GOLD = 1,										-- 元宝领取
	GOLD_CONFIG = 2,								-- 元宝配置信息
	VIP = 3,
	MAX = 4,
}

GITT_LINGQU_TYPE =
{
	UPGRADE = 1,									-- 等级礼包
	VIPGIFT = 2,									-- VIP礼包
}

RED_PAPER_TYPE = {
	GUILR_RED_PAPER = 0,
	WORLD_RED_PAPER = 1,
}

COIN_NUM = {
	COIN_NUMS = 10000,
}

BIND_PHONE_TYPE = {
	NOT_OPEN = 0,
	SDK_BIND = 1,
	YUN_YING_BIND = 2,
}

SEVEN_DAY_LOGIN_MAX_REWARD_DAY = 8					-- 七天登录奖励 最大奖励天数
OFFLINE_EXP_GET_MAX_HOURS = 24 * 3					-- 离线经验最多找回时间（小时）

function WelfareWGData:__init()
	if WelfareWGData.Instance then
		ErrorLog("[WelfareWGData]:Attempt to create singleton twice!")
	end
	WelfareWGData.Instance = self

	self.sevendayvo = WelfareWGData.CreateSevenDayVo()
	self.dailyfind_list = {}
	self.upgradevo = WelfareWGData.CreateUpGradeVo()
	self.vipgiftvo = WelfareWGData.CreateVipGiftVo()

	self.remind_num_list = {}

	--七日目标
	self.goal_cfg = ConfigManager.Instance:GetAutoConfig("rolegoalconfig_auto").goal
	self.chapter_reward = ConfigManager.Instance:GetAutoConfig("rolegoalconfig_auto").reward

	local welfare_auto_cfg = ConfigManager.Instance:GetAutoConfig("welfare_auto")

	self.new_dailyfind_cfg = ListToMap(welfare_auto_cfg.activity_find, "daily_find", "find_type")

	self.activity_find_reward_cfg = welfare_auto_cfg.activity_find_reward

	self.level_vip_reward_cfg_list = welfare_auto_cfg.level_vip_reward

	self.activity_find_cfg = welfare_auto_cfg.activity_find

	self.welfare_other_cfg = welfare_auto_cfg.other

	self.welfare_bind_phone_cfg = ListToMap(welfare_auto_cfg.bind_phone_reware,"gift_index")

	self.chapter_reward_list = {} 	--是否领取奖励

	self.goal_param_list = {} 		--目标进度

	--日常奖励信息(活跃度奖励)
	self.total_time = 0

	self.is_seven_day_end = false

	-- 好友畅玩
	self.syncfriendinvite_info = {}
	self.friendinvite_notice = {}

	self.rewardvo = WelfareWGData.CreateFriendRewardVo()

	self.fetch_online_reward_flag = 0

	self.today_online_time = 0

	self.online_gift_char = {}

	self.leiji_item_cfg = {}
	self.chongji_limit = {}

	--系统红包信息
	self.system_redpaper_info_list = {}

	self.roll_index = 0

	self.open_day = 1

	self.update_affiche = {
		server_version = 0,
		fetch_reward_version = 0,
	}
	self.is_update_affiche_ok = false
	self.everyday_text = ""
	--记录未进入跨服之前冲级和vip专属模块的红点情况
	self.rember_chongji_red = 0
	self.rember_vipgift_red = 0

	self.welfare_qifu_data = {}
	self.welfare_qifu_cfg = {}
	local level_config = ConfigManager.Instance:GetAutoConfig("vip_auto")
	self.welfare_qifu_cfg.qifu_reward = level_config.qifu_reward
	self.welfare_qifu_cfg.qifu_cost = level_config.qifu_cost
	self.welfare_qifu_cfg.qifu_coin_num = level_config.other[1].qifu_coin_num
	self.equip_star_show = ListToMap(ConfigManager.Instance:GetAutoConfig("sevendaylogincfg_auto").equip_show, "item_ID")

	self.redpape_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	self.guild_redpaper_cfg = ListToMap(self.redpape_cfg.guild_redpaper,"type","level")
	self.custom_redpaper_cfg = ListToMap(self.redpape_cfg.custom_redpaper,"seq")
	self.world_redpaper_cfg = ListToMap(self.redpape_cfg.world_redpaper,"type","level")

	local cfg = ConfigManager.Instance:GetAutoConfig("leiji_login_cfg_auto")
    self.wek_reward_cfg = cfg.reward
    self.day_reward_cfg = cfg.day_reward
	self.day_seq_reward_cfg = ListToMap(cfg.day_reward, "seq")

	self.redpaper_dec_cfg = self.redpape_cfg.redpaper_dec

		--红点显示
	-- RemindManager.Instance:Register(RemindName.Welfare, BindTool.Bind(self.IsShowWelfareRedPoint, self))--福利主界面按钮
	RemindManager.Instance:Register(RemindName.WelfareT, BindTool.Bind(self.IsShowWelfareTRedPoint, self))--福利侧边第一个按钮
	-- RemindManager.Instance:Register(RemindName.WelfareZaiXian, BindTool.Bind(self.IsShowZaiXianRedPoint, self))--在线
	RemindManager.Instance:Register(RemindName.WelfareQianDao, BindTool.Bind(self.IsShowQianDaoRedPoint, self))--签到
	RemindManager.Instance:Register(RemindName.AccumulativeLogin, BindTool.Bind(self.GetAccumulativeLoginRed, self)) --周签到
	--RemindManager.Instance:Register(RemindName.WelfarePaper, BindTool.Bind(self.IsShowWelfarePaperRedPoint, self))--世界红包
	-- RemindManager.Instance:Register(RemindName.WelfareChangWan, BindTool.Bind(self.IsShowChangWanRedPoint, self))--畅玩
	RemindManager.Instance:Register(RemindName.WelfareSevenServer, BindTool.Bind(self.IsShowSevenServerRedPoint, self))--七天登陆
	-- RemindManager.Instance:Register(RemindName.WelfareChongJi, BindTool.Bind(self.IsShowWelfareChongJiRedPoint, self))--冲级
	RemindManager.Instance:Register(RemindName.WelfareLevelGift, BindTool.Bind(self.IsShowWelfareLevelOrVipGiftRedPoint, self, GITT_LINGQU_TYPE.UPGRADE))--等级礼包
	RemindManager.Instance:Register(RemindName.WelfareVipGift, BindTool.Bind(self.IsShowWelfareLevelOrVipGiftRedPoint, self, GITT_LINGQU_TYPE.VIPGIFT))--vip礼包
	-- RemindManager.Instance:Register(RemindName.WelfareZhaoHui, BindTool.Bind(self.IsShowWelfareZhaoHuiRedPoint, self))--找回
	--RemindManager.Instance:Register(RemindName.WelfareQiFu, BindTool.Bind(self.IsShowWelfareQiFuRedPoint, self))--祈福
	RemindManager.Instance:Register(RemindName.WelfareGongGao, BindTool.Bind(self.IsShowWelfareGongGaoRedPoint, self))--公告
	--RemindManager.Instance:Register(RemindName.WelfareOnlineGift,BindTool.Bind(self.IsShowWelfareOnlineGiftRedPoint, self))--在线奖励
	RemindManager.Instance:Register(RemindName.WelfarePhoneBind,BindTool.Bind(self.IsShowWelfarePhoneBindRedPoint, self))--手机绑定
	-- 红点回调绑定
	self.welfare_levelup_gift_callback = BindTool.Bind(self.WelfareLevelGiftCallBack, self) 							-- 等級礼包
	RemindManager.Instance:Bind(self.welfare_levelup_gift_callback, RemindName.WelfareLevelGift)

	self.welfare_Vip_gift_callback = BindTool.Bind(self.WelfareVipGiftCallBack, self) 							-- Vip礼包
	RemindManager.Instance:Bind(self.welfare_Vip_gift_callback, RemindName.WelfareVipGift)

	self.guild_redpaper_all_info = {}
	self.world_redpaper_all_info = {}

	self.dailyfind_protocol = {}

	self.find_btn_flag = 0

	self.is_phone_bind_open = false
	self.is_phone_bind_got_reward = false
	self.is_phone_binded = false

	self.is_bind_phone_reward = false			-- 是否绑定手机领取过奖励
	self.is_handsetverify_red = true
	self.is_handsetverify_sdk_red = true
	self.is_open_handset_verify_sdk = false
	self.is_open_handset_verify = false
	self.have_show_pohoe_red = false
	self.have_open_pohoe_view = false
	self.verify_check_max_time = 0

	self.notify_info = {
		version = 0,
		reward_version = 0,
		content = "",
		reward_item = {},
		is_new = false
	}
end

function WelfareWGData:__delete()
	WelfareWGData.Instance = nil
	self.sevendayvo = nil
	self.dailyfind_list = nil
	self.upgradevo = nil
	self.total_degree = nil
	self.is_world_paper_type = nil
	self.show_record_data = nil
	self.total_time = 0
	self.syncfriendinvite_info = nil
	self.friendinvite_notice =nil
	self.open_day = nil
	self.rewardvo = nil
	self.rember_chongji_red = nil
	self.rember_vipgift_red = nil
	if self.from ~= nil then
		self.from = nil
	end
			--红点显示
	RemindManager.Instance:UnRegister(RemindName.Welfare)--福利主界面按钮
	RemindManager.Instance:UnRegister(RemindName.WelfareT)--福利侧边第一个按钮
	RemindManager.Instance:UnRegister(RemindName.WelfarePaper)
	-- RemindManager.Instance:UnRegister(RemindName.WelfareZaiXian, BindTool.Bind(self.IsShowZaiXianRedPoint, self))--在线
	RemindManager.Instance:UnRegister(RemindName.WelfareQianDao)--签到
	RemindManager.Instance:UnRegister(RemindName.AccumulativeLogin)--周签到
	-- RemindManager.Instance:UnRegister(RemindName.WelfareChangWan, BindTool.Bind(self.IsShowChangWanRedPoint, self))--畅玩
	RemindManager.Instance:UnRegister(RemindName.WelfareSevenServer)--七天登陆
	-- RemindManager.Instance:UnRegister(RemindName.WelfareChongJi)--冲级
	RemindManager.Instance:UnRegister(RemindName.WelfareLevelGift)--等级礼包
	RemindManager.Instance:UnRegister(RemindName.WelfareVipGift)--vip礼包
	-- RemindManager.Instance:UnRegister(RemindName.WelfareZhaoHui, BindTool.Bind(self.IsShowWelfareZhaoHuiRedPoint, self))--找回
	--RemindManager.Instance:UnRegister(RemindName.WelfareQiFu)--祈福
	RemindManager.Instance:UnRegister(RemindName.WelfareGongGao)--公告
	--RemindManager.Instance:UnRegister(RemindName.WelfareOnlineGift)
	RemindManager.Instance:UnRegister(RemindName.WelfarePhoneBind) --手机绑定
end

function WelfareWGData:GetRemindNum(remind_id)
	return self.remind_num_list[remind_id] or 0
end

------------------------------------------------------------------------- 在线奖励
function WelfareWGData:SetFetchOnlineRewardFlag(flag)
	self.fetch_online_reward_flag = flag
	-- RemindManager.Instance:Fire(RemindName.WelfareZaiXian)--在线
	RemindManager.Instance:Fire(RemindName.WelfareT)--福利侧边第一个按钮
	RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮

end

function WelfareWGData:GetFetchOnlineRewardFlag()
	return self.fetch_online_reward_flag
end

function WelfareWGData:SetTodayOnlineTime(time)
	self.today_online_time = time
end

function WelfareWGData:GetTodayOnlineTime()
	return self.today_online_time
end

function WelfareWGData:SetOnlineGiftMax(char)
	self.online_gift_char = char
end

function WelfareWGData:GetOnlineGiftMax()
	return self.online_gift_char
end

function WelfareWGData:GetOnlineGiftMaxSeq(reward_type)
	for i,v in ipairs(self.online_gift_char) do
		if reward_type == i then
			return v
		end
	end
end

function WelfareWGData:GetZaiXianListData()
	local list = {}
	local flag = bit:d2b(self.fetch_online_reward_flag)
	for i = 1, 4 do
		list[i] = {}
		list[i].min_time = self:GetZaiXianOnlineMinTime(i -1) * 60    --读秒
		list[i].reward_item = self:GetZaiXianOnlineRewardItem(i -1)

		local is_lingqu = false
		if flag[33 - i] == 1 then
			is_lingqu = true
		end
		list[i].is_lingqu = is_lingqu

	end

	return list
end

function WelfareWGData:GetZaiXianIsCanReward()
	local time = self:GetTodayOnlineTime()
	local data = {}
	local i = 0
	for k, v in pairs(self:GetZaiXianListData()) do
		data[i] = false
		if v.min_time <= time and v.is_lingqu == false then
			data[i] = true
		end
		i = i + 1
	end
	return data
end

function WelfareWGData:GetOnlineRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("welfare_auto").online_reward
end

function WelfareWGData:GetOnlineRewardFlag(type)
	local flag = bit:d2b(self.fetch_online_reward_flag)
	return flag[33 - type]
end

function WelfareWGData:GetZaiXianOnlineMinTime(reward_type)
	local min_time = 0
	for k, v in pairs(self:GetOnlineRewardCfg()) do
		if v.reward_type == reward_type then
			if min_time ~= v.online_min then
				min_time = v.online_min
			end
		end
	end
	return min_time
end

function WelfareWGData:GetZaiXianOnlineRewardItem(reward_type)
	local item = {}
	for k, v in pairs(self:GetOnlineRewardCfg()) do
		if v.reward_type == reward_type and v.seq == self:GetOnlineGiftMaxSeq(reward_type + 1) then
			item = v.reward_item
		end
	end
	return item
end

function WelfareWGData:GetZaiXianOnlineAniItem(reward_type)
	local item = {}
	local i = 1
	for k, v in pairs(self:GetOnlineRewardCfg()) do
		if v.reward_type == reward_type then
			item[i] = v.reward_item
			i = i + 1
		end
	end
	return item
end

function WelfareWGData:GetOnlineRewardRemind()
	if not self:GetOnlineRewardCfg() then return 0 end

	local min_time = {}
	for k, v in pairs(self:GetOnlineRewardCfg()) do
		if min_time[v.reward_type] ~= v.online_min then
			min_time[v.reward_type] = v.online_min
		end
	end

	local i = 1
	for k, v in pairs(min_time) do
		local is_get = self:GetOnlineRewardFlag(i)
		i = i + 1
		local online_time = self:GetTodayOnlineTime()
		local time = v * 60
		if is_get == 0 and online_time >= time then
			return 1
		end
	end
	return 0
end

------------------------------------------------------------------------- 七天狂欢

function WelfareWGData.CreateSevenDayVo()
	local vo = {}
	vo.notify_reason = 0								-- 通知原因
	vo.account_total_login_daycount = 0					-- 一生累计登录天数
	vo.seven_day_login_fetch_reward_mark = {}			-- 七天累计登录奖励领取标记
	vo.day_list = {}
	return vo
end

-- 七天中的一天
function WelfareWGData.CreateSevenDayCellVo(info)
	local vo = {}
	vo.itemvo = info.reward_item or {}
	vo.login_daycount = info.login_daycount or 0
	vo.reward_text = info.reward_text
	vo.award_text = info.award_text
	vo.model_id = info.model_id
	vo.status = SEVEN_COMPLETE_STATUS.WEIDACHENG
	vo.res_type = info.res_type
	vo.position = info.position
	vo.rotation = info.rotation
	vo.scale = info.scale
	vo.is_image = info.is_image
	vo.soul_ring_id = info.soul_ring_id
	return vo
end

function WelfareWGData:GetSevenDayVo()
	return self.sevendayvo
end

function WelfareWGData:GetSelectSeventDayIndex()
	return self.sevenday_select_index
end

function WelfareWGData:SetSelectSeventDayIndex(index)
	self.sevenday_select_index = index
end

function WelfareWGData:GetSevenDayCfg()
	local config = ConfigManager.Instance:GetAutoConfig("sevendaylogincfg_auto").reward
	if nil == config then
		return
	end

	for k, v in pairs(config) do
		if not self.sevendayvo.day_list[k] then
			self.sevendayvo.day_list[k] = WelfareWGData.CreateSevenDayCellVo(v)
			self.sevendayvo.day_list[k]["show_ID"] = v.show_ID
			self.sevendayvo.day_list[k]["show_ID2"] = v.show_ID2
			self.sevendayvo.day_list[k]["show_name"] = v.show_name
			self.sevendayvo.day_list[k]["icon_id"] = v.icon_id
			self.sevendayvo.day_list[k]["background_id"] = v.background_id
		end
	end
	return self.sevendayvo.day_list
end

function WelfareWGData:GetEightSevenDayCfg( day )
	local data_list = self:GetSevenDayCfg()
	local canday = day or self:GetCanRewardDay()
	local data_info = {}
	for k,v in pairs(data_list) do
		if canday > SEVEN_DAY_LOGIN_MAX_REWARD_DAY then
			if v.login_daycount > SEVEN_DAY_LOGIN_MAX_REWARD_DAY then
				table.insert(data_info, v )
			end
		else
			if v.login_daycount <= SEVEN_DAY_LOGIN_MAX_REWARD_DAY then
				table.insert( data_info, v )
			end
		end
	end
	return data_info
end


function WelfareWGData:GetOpenDayReward()
	local config = ConfigManager.Instance:GetAutoConfig("sevendaylogincfg_auto").reward
	if nil ~= config and config[self.sevendayvo.account_total_login_daycount] then
		return config[self.sevendayvo.account_total_login_daycount]
	end
end

function WelfareWGData:GetSevenDayGiftItems(seven_index)
	local prof = RoleWGData.Instance:GetRoleProf()
	local config = ConfigManager.Instance:GetAutoConfig("sevendaylogincfg_auto").reward
	if config[seven_index] and config[seven_index]["reward_item_prof_"..prof] then
		local data = config[seven_index]["reward_item_prof_"..prof]

		for k,v in pairs(data) do
			if self.equip_star_show and self.equip_star_show[v.item_id] and self.equip_star_show[v.item_id].star_level then
				v["star_level"] = self.equip_star_show[v.item_id].star_level
			end
		end

		return data
	end
	return nil
end

--是否是在七天奖励内
function WelfareWGData:GetInInSevenDay()
	return self.now_day < SEVEN_DAY_LOGIN_MAX_REWARD_DAY + 1
end

function WelfareWGData:SetLoginDay(now_day)
	self.now_day = now_day
	RemindManager.Instance:Fire(RemindName.WelfareSevenServer)--七天登陆
	RemindManager.Instance:Fire(RemindName.WelfareT)--福利侧边第一个按钮
	RemindManager.Instance:Fire(RemindName.Welfare)--福利主 界面按钮

	--region 目前开服活动面板没有福利活动先关闭 by tansen
	-- RemindManager.Instance:Fire(RemindName.OpenServer)
	-- self:UpdataOpenServerBtn()
	--endregion
end

function WelfareWGData:UpdataOpenServerBtn()
	local is_can_close = TianShuWGData.Instance:GetTianShuIsClose()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.ActOpenServerLanding)
	local day = self:GetCanRewardDay()
	if day < SEVEN_DAY_LOGIN_MAX_REWARD_DAY + 1 and not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPEN_SERVER) and is_open then
		local cur_timestamp = TimeWGCtrl.Instance:GetServerTime()
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER, ACTIVITY_STATUS.OPEN, cur_timestamp + COMMON_CONSTS.MAX_LOOPS)
	elseif day >= SEVEN_DAY_LOGIN_MAX_REWARD_DAY + 1 and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPEN_SERVER) and not ActivityWGCtrl.Instance:IsOpenServerOpen() and is_can_close then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.OPEN_SERVER, ACTIVITY_STATUS.CLOSE)
	end
end
--用于天书模块关闭开服按钮的时候做判断
function WelfareWGData:GetSevenLoginDayIsClose()
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.ActOpenServerLanding)
	local can_day = self:GetCanRewardDay()
	if is_open and can_day < SEVEN_DAY_LOGIN_MAX_REWARD_DAY + 1 then
		return false
	end
	return true
end


function WelfareWGData:GetLoginDay()
	return self.now_day
end

-- 获取可以领取的天数 canday 等于9时就是领完了
function WelfareWGData:GetCanRewardDay()
	local canday = 0
	local daycount = self.sevendayvo.account_total_login_daycount
	for i = 1, SEVEN_DAY_LOGIN_MAX_REWARD_DAY do
		if i <= daycount then
			local tag = self.sevendayvo.seven_day_login_fetch_reward_mark[32 - i]
			if tag == 0 then
				canday = i
				return canday
			elseif tag == 1 then
				canday = i + 1
			end
		else
			return canday
		end
	end

	return 1
end

--根据14天登录页签  获取当前页签需要跳转的数据
function WelfareWGData:GetTabJumpItemData(total_price, index)
	if index == 2 then --7天后的页签
		local eight_price = {}
		local price_index = 0
		local cell_data = nil
		local today_cell = nil
		for k, v in pairs(total_price) do
			if k > 7 then
				table.insert(eight_price, v)
			end
		end

		for k, v in pairs(eight_price) do
			if v.status == 2 then
				price_index = k
				cell_data = v
				break
			elseif v.status == 3 then
				today_cell = v
				break
			end
		end
		--price_index  可领取的灯笼下标   cell_data 可领取的灯笼数据  today_cell 明日可领的灯笼
		return price_index, cell_data, today_cell
	else --前7天的页签
		local one_price = {}
		local price_index = 0
		local cell_data = nil
		local today_cell = nil
		for k, v in pairs(total_price) do
			if k < 8 then
				table.insert(one_price, v)
			end
		end

		for k, v in pairs(one_price) do
			if v.status == 2 then
				price_index = k
				cell_data = v
				break
			elseif v.status == 3 then
				today_cell = v
				break
			end
		end
		return price_index, cell_data, today_cell
	end
end

function WelfareWGData:UpdateSevenDayStatus()
	self.sevendayvo.day_list = self:GetSevenDayCfg()
	local daycount = self.sevendayvo.account_total_login_daycount
	local remain_count = 0
	for k, v in pairs(self.sevendayvo.day_list) do
		--倒序读取,1为领取,0可领取
		local tag = self.sevendayvo.seven_day_login_fetch_reward_mark[32 - k]
		if k <= daycount then
			if tag == 0 then
				v.status = SEVEN_COMPLETE_STATUS.KELINGQU	--可领取
				remain_count = remain_count + 1
			elseif tag == 1 then
				v.status = SEVEN_COMPLETE_STATUS.YILINGQU	--已领取
			end
		elseif k == daycount + 1 then
			v.status = SEVEN_COMPLETE_STATUS.MingRiKeLingQu --明日可领取
		else
			v.status = SEVEN_COMPLETE_STATUS.WEIDACHENG	--未达成
		end
	end
end
------------------------------------------------------------------------- 每月签到
function WelfareWGData:SetQianDaoInfo(info)
	self.qian_dao_info = info
	self.frist_find_index = nil
	self:SetQianDaoItemCfg()
	self:SetLeiJiItemCfg()
	RemindManager.Instance:Fire(RemindName.WelfareQianDao)--签到
	RemindManager.Instance:Fire(RemindName.WelfareT)--福利侧边第一个按钮
	RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮
end

function WelfareWGData:GetQianDaoInfo()
	return self.qian_dao_info or {}
end

function WelfareWGData:SetIsQianDaoDay(sign_in_reward_mark)
	self.yiling_day = 0
	for i = 1, 31 do
		if sign_in_reward_mark[32 - i] == 0 then
			self.yiling_day = self.yiling_day + 1
		end
		if sign_in_reward_mark[32 - i] == 1 then
			break
		end
	end
end

function WelfareWGData:GetIsQianDaoDay()
	return self.yiling_day
end

function WelfareWGData:SetQianDaoItemCfg()
	self.qian_dao_item_cfg = {}
	local frist_find = false
	local offset_index = self.qian_dao_info.offset_day
	local sign_in_days = self.qian_dao_info.sign_in_days
	local indexes = self:GetConfigIndexes()
	-- 计算已领取的天数
	local sign_in_reward_mark = self.qian_dao_info.sign_in_reward_mark
	-- self:SetIsQianDaoDay(sign_in_reward_mark)
	local qian_dao_all_info = ConfigManager.Instance:GetAutoConfig("welfare_auto").sign_in

	-- for k, v in ipairs(qian_dao_all_info) do
	-- 	if self.qian_dao_info.cur_month == v.month then
	-- 		table.insert(self.qian_dao_item_cfg, __TableCopy(v))
	-- 	end
	-- 	print_error(v.month,self.qian_dao_info.cur_month)
	-- 	if v.month > self.qian_dao_info.cur_month then
	-- 		break
	-- 	end
	-- end

	for k, v in ipairs(qian_dao_all_info) do
		if indexes == v.indexes then
			table.insert(self.qian_dao_item_cfg, __TableCopy(v))
		end
		if v.indexes > indexes then
			break
		end
	end

	-- local day = tonumber(os.date("%d", TimeWGCtrl.Instance:GetServerTime()))
	local open_day = self.open_day  --TimeWGCtrl.Instance:GetCurOpenServerDay()
	local day = (open_day%28) == 0 and 28 or (open_day%28)
	self.qiandao_day = day
	for k, v in ipairs(self.qian_dao_item_cfg) do
		if v.day == day and sign_in_reward_mark[32 - v.day] == 0 then
			v.flag = QIANDDAO_STATUS.QIANDAO 		-- 可领取
		end
		local index = sign_in_reward_mark[32 - k]
		if index == 1 then
				v.flag = QIANDDAO_STATUS.ALREADYGET 		-- 已经领取
		elseif  v.day ~= day and index == 0  then
			v.flag = QIANDDAO_STATUS.WAIT 			-- 即将领取
		end
		if v.day < day and offset_index > 0 then

			local index = sign_in_reward_mark[32 - v.day]
			if index == 0 then
				v.flag = QIANDDAO_STATUS.BACK 		-- 可找回
			end
		end
		if v.day > day + 1 then
				v.tab_flag = 2
		end

	end
end
function WelfareWGData:GetQiandaoDay()
	return self.qiandao_day
end
function WelfareWGData:GetCanQianDaoRemind()
	if not self.qian_dao_item_cfg then return 0 end

	for k,v in ipairs(self.qian_dao_item_cfg) do
		if v.flag == QIANDDAO_STATUS.QIANDAO then
			return 1

		elseif self.qian_dao_info.day_chongzhi_sign_in_reward_mark[32 - self.qian_dao_info.to_day] == 0 and RechargeWGData.Instance:GetTodayRecharge() ~= 0   then
			return 1
		end
	end
	for k,v in pairs(self.leiji_item_cfg) do
		if v.flag == QIANDDAO_STATUS.QIANDAO then
			return 1
		end
	end
	return 0
end

function WelfareWGData:SetFristFind(index)
	self.frist_find_index = index
end

function WelfareWGData:GetFristFind()
	return self.frist_find_index
end

function WelfareWGData:GetQianDaoItemCfg()
	if not self.qian_dao_item_cfg then return end
	--判断平瑞年用，现在不按年份来了，搞不懂，搞不懂
	-- local year,month = TimeUtil.FormatSecond5MYHM1(TimeWGCtrl.Instance:GetServerTime())
	-- year = tonumber(year)
	-- month = tonumber(month)
	-- if(year % 400 ~= 0 or year % 4 ~= 0 and year % 100 == 0) then
	-- 	if month == 2 and #self.qian_dao_item_cfg == 29 then
	-- 		table.remove(self.qian_dao_item_cfg,31)
	-- 		table.remove(self.qian_dao_item_cfg,30)
	-- 		table.remove(self.qian_dao_item_cfg,29)
	-- 	end
	-- end
	-- if month == 4 or month == 6 or month == 9 or month == 11  then
	-- 	table.remove(self.qian_dao_item_cfg,31)
	-- end

	return self.qian_dao_item_cfg or {}
end

function WelfareWGData:SetLeiJiItemCfg()
	self.leiji_item_cfg = {}
	local leiji_reward_flag = self.qian_dao_info.leiji_reward_flag 					-- 是否可领取的标记
	local fetch_leiji_reward_flag = self.qian_dao_info.fetch_leiji_reward_flag 		-- 是否已经领取的标记
	local leiji_all_cfg = ConfigManager.Instance:GetAutoConfig("welfare_auto").sign_heap_in
	local indexes = self:GetConfigIndexes()

	for k, v in ipairs(leiji_all_cfg) do
		if indexes == v.indexes then
			table.insert(self.leiji_item_cfg, __TableCopy(v))
		end
		if v.indexes > indexes then
			break
		end
	end
	for k, v in ipairs(self.leiji_item_cfg) do
		v.flag = leiji_reward_flag[32 - (k - 1)]
		if v.flag == 1 then
			v.flag = QIANDDAO_STATUS.QIANDAO 				-- 可领取
			local index = fetch_leiji_reward_flag[32 - (k - 1)]
			if index == 1 then
				v.flag = QIANDDAO_STATUS.ALREADYGET 				-- 已领取
			end
		else
			v.flag = QIANDDAO_STATUS.NONE 						-- 无
		end
	end
	-- self.leiji_item_cfg[0] = table.remove(self.leiji_item_cfg, 1)
end

function WelfareWGData:GetLeiJiItemCfg()
	local info = self:GetQianDaoInfo()
	local sign_in_days = info.sign_in_days or 0 --如果协议还没下发随便编一个值
	for k,v in pairs(self.leiji_item_cfg) do
		if v.heap_times > sign_in_days then
			v.flag = QIANDDAO_STATUS.WAIT  --即将领取
			break
		end
	end
	return self.leiji_item_cfg
end

function WelfareWGData:GetTokenNameBySignInDays()
	local leiji_cfg = self:GetLeiJiItemCfg()
	local qiandao_info = self:GetQianDaoInfo()
	local token_name = ""
	for k, v in pairs(leiji_cfg) do
		if qiandao_info.sign_in_days <= tonumber(v.heap_times) then
			token_name = leiji_cfg[k].sign_word
			break
		end
	end
	return token_name
end

function WelfareWGData:GetCurQDAccProgress()
	local cur_progress = 0
	local cur_act_light = 0
	local cfg = self:GetLeiJiItemCfg()
	local info = self:GetQianDaoInfo()
	if next(cfg) == nil or next(info) == nil then
		return cur_progress, cur_act_light
	end
	local cur_acc_day = info.sign_in_days
	local progress_list = {0.1, 0.33, 0.56, 0.8, 1}			--对应的进度条值

	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = cfg[seq] and cfg[seq].heap_times or 0
		local next_need = cfg[seq + 1] and cfg[seq + 1].heap_times or cfg[#cfg].heap_times
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]

		if cfg[k] and cur_acc_day >= cfg[k].heap_times then
			cur_act_light = cur_act_light + 1
		end

		if cur_acc_day > cur_need and cur_acc_day <= next_need then
			cur_progress = (cur_acc_day - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif cur_acc_day > cfg[#cfg].heap_times then
			cur_progress = progress_list[length]
			cur_act_light = length
			break
		end
	end
	return cur_progress, cur_act_light
end

function WelfareWGData:GetLeiJiRewardItemByIndex(index)
	local leiji_all_cfg = ConfigManager.Instance:GetAutoConfig("welfare_auto").sign_heap_in

	local indexes = self:GetConfigIndexes()
	for k,v in pairs(leiji_all_cfg) do
		if indexes == v.indexes and index == v.reward_type then
			return v.reward_item
		end
	end
end
function WelfareWGData:GetConfigIndexes()
	local lun = self.open_day/28
	local lun_zheng = math.modf(lun)  -- 取整数
	local reslut_lun = 1
	if lun > lun_zheng then
		reslut_lun = lun_zheng + 1
	else
		reslut_lun = lun
	end
	if reslut_lun <= 0 then
		reslut_lun = 1
	elseif reslut_lun > 3 then
		reslut_lun = reslut_lun%2 + 2
	end

	return reslut_lun
end


------------------------------------------------------------------------- 奖励找回

function WelfareWGData.CreateDailyFindVo()
	local vo = {}
	vo.big_type = DAILYFIND_TYPE.OFFLINE_EXP 	--大类型
	vo.small_type = 0 							--任务类型或则活动类型
	vo.plusvo = {}								--附加内容
	vo.status = SEVEN_COMPLETE_STATUS.WEIDACHENG
	vo.times = 0
	vo.vip_times = 0
	vo.opengame_day = 0
	vo.role_level = 0
	return vo
end

function WelfareWGData.CreateRewardVo()
	return
	{
		reward_type = 0,
		reward = 0,
	}
end

--重置列表
function WelfareWGData:ResetDailyFindItems()
	self.dailyfind_list = {}
end

function WelfareWGData:AddDailyFindItem(item)
	table.insert(self.dailyfind_list, item)

end

function WelfareWGData:RemoveDailyFindItem(big_type, small_type)
	-- print_error("WelfareWGData.RemoveDailyFindItem,big_type=",big_type,"small_type=",small_type)
	for k,v in pairs(self.dailyfind_list) do
		if v.big_type == big_type and v.small_type == small_type then
			table.remove(self.dailyfind_list, k)
			return
		end
	end
end

--如果没打过诛神塔，找回列表中不显示
function WelfareWGData:CheckZhushenTaLevel()
	for k,v in pairs(self.dailyfind_list) do
		if v.big_type == DAILYFIND_TYPE.TASK then
			if v.small_type == 9 then
				local cur_level = FuBenPanelWGData.Instance:GetZhuShenTaPassMaxLevel()
				if cur_level < 0 then
					self:RemoveDailyFindItem(v.big_type, v.small_type)
				end
			end
		end
	end
end

function WelfareWGData:GetDailyFindItems()
	return self.dailyfind_list
end

function WelfareWGData:CacheFindBtnFlag(flag)
	self.find_btn_flag = flag
end


-- 0 不能找回
-- 1 可元宝找回
-- 2 可元宝及免费找回 ,只有仙玉才能找回VIP次数

function WelfareWGData:ResetDailyFindList(is_remind)
	local daily_find_list = {}
	for k,v in pairs(self.dailyfind_list) do

		local check_funopen = true --检测功能开启
		local cur_cfg = self:GetNewDailyFindCfg(v.plusvo.daily_find, v.small_type)
		if cur_cfg and cur_cfg.module_name then
			check_funopen = FunOpen.Instance:GetFunIsOpened(cur_cfg.module_name)
		end

		local vip_times = v.vip_times
		if v.btn_flag == 1 then
			-- 2 可元宝及免费找回 ,只有仙玉才能找回VIP次数
			vip_times = v.plusvo.is_open == 2 and 0 or v.vip_times
		end
		local total_times = v.times + vip_times

		if check_funopen and total_times > 0 then	--没有找回次数就不显示
			if v.plusvo.is_open == 1 then 	-- 1 可元宝找回
				if self.find_btn_flag == 2 or is_remind then
					table.insert(daily_find_list, v)
				end
			else
				table.insert(daily_find_list, v)
			end
		end
	end

	return daily_find_list
end

-- 一键找回 需要的总仙玉
function WelfareWGData:DailyFindListTotalMoney(find_list)
	local total_money = 0
	if find_list == nil then
		return
	end

	for k, v in ipairs(find_list) do
		local all_vip_times = v.vip_times
		if v.btn_flag == 1 then
			-- 2 可元宝及免费找回 ,只有仙玉才能找回VIP次数
			all_vip_times =  v.plusvo.is_open == 2 and 0 or v.vip_times
		end

		local cur_times = v.times
		if v.times == 0 then
			cur_times = v.times + all_vip_times
		end
		local retrieved_times = GameMath.Round(cur_times)
		local dailyfind_reward_cfg = self:GetNewDailyFindRewardCfg(v.big_type, v.plusvo.find_type , v.btn_flag - 1, v.role_level)
		if dailyfind_reward_cfg then
			if v.plusvo.daily_find == 1 then --日常找回
				-- print_error(v.plusvo.name, all_vip_times)
				-- print_error(v.plusvo.name, v.times)

				if all_vip_times > 0 then
					local nomal_times_cost = 0
					local vip_times_cost = all_vip_times * dailyfind_reward_cfg.vip_extra_cost
					if v.times > 0 then
						nomal_times_cost = retrieved_times * dailyfind_reward_cfg.cost
					end

					local cost = nomal_times_cost + vip_times_cost
					total_money = total_money + cost
				else
					local nomal_times_cost = retrieved_times * dailyfind_reward_cfg.cost
				 	total_money = total_money + nomal_times_cost
				end
				
				-- if retrieved_times - v.times > 0 then
				-- 	local vip_times = retrieved_times - v.times
				-- 	local nomal_times_cost = v.times * dailyfind_reward_cfg.cost
				-- 	local vip_times_cost = vip_times * dailyfind_reward_cfg.cost
				-- 	if dailyfind_reward_cfg.vip_extra_cost then
				-- 		vip_times_cost = vip_times * dailyfind_reward_cfg.vip_extra_cost
				-- 	end
				-- 	local cost = nomal_times_cost + vip_times_cost
				-- 	total_money = total_money + cost
				-- else
				-- 	local nomal_times_cost = retrieved_times * dailyfind_reward_cfg.cost
				-- 	total_money = total_money + nomal_times_cost
				-- end
			else --活动找回
				--print_error(v.plusvo.name, retrieved_times)
				local nomal_times_cost = retrieved_times * dailyfind_reward_cfg.cost
				if dailyfind_reward_cfg.vip_extra_cost and retrieved_times - v.times > 0 then
					nomal_times_cost = v.times * dailyfind_reward_cfg.cost
					local vip_times = retrieved_times - v.times
					local vip_times_cost = vip_times * dailyfind_reward_cfg.vip_extra_cost
					nomal_times_cost = nomal_times_cost + vip_times_cost
				end
				total_money = total_money + nomal_times_cost
			end
		else
			print_error("拿不到配置：找回类型（任务或活动），配置的找回类型，仙玉或元宝找回，服务器下发的等级",v.big_type, v.plusvo.find_type , v.btn_flag - 1, v.role_level)
		end
	end

	return total_money
end

--是否可一键找回
function WelfareWGData:IsDailyFindList(find_list)
	if find_list == nil then
		return
	end

	for k, v in ipairs(find_list) do
		local vip_times = v.vip_times
		if v.btn_flag == 1 then
			vip_times =  v.plusvo.is_open == 2 and 0 or v.vip_times
		end
		local total_times = v.times + vip_times
		if total_times > 0 then --还有可找回的次数
			return 1
		end
	end

	return 0
end

function WelfareWGData:GetOfflineExpConfig(dailyfind_get_type)
	dailyfind_get_type = dailyfind_get_type or DAILYFIND_GET_TYPE.GOLD_CONFIG
	local config = ConfigManager.Instance:GetAutoConfig("welfare_auto").offline_exp
	for k, v in pairs(config) do
		if v.type == dailyfind_get_type then
			return v
		end
	end
	return nil
end

function WelfareWGData:GetDailyFindActivityRewardConfig(find_type, level)
	local value = nil
	level = level or RoleWGData.Instance.role_vo.level
	local function GetActivityReard()
		local config = ConfigManager.Instance:GetAutoConfig("welfare_auto").activity_find_reward
		for k, v in pairs(config) do
			if v.find_type == find_type then -- and v.level == level then
				value = v
			end
		end
	end
	GetActivityReard()
	return value
end

function WelfareWGData:GetDailyFindActivityConfig(find_type)
	local config = ConfigManager.Instance:GetAutoConfig("welfare_auto").activity_find
	if not find_type then
		return config
	end
	for k, v in pairs(config) do
		if v.find_type == find_type then
			return v
		end
	end
	return nil
end

function WelfareWGData:GetDailyFindActivityRewardExpain(reward_item)
    local value = ""
    for k, v in pairs(reward_item) do
    	local config = ItemWGData.Instance:GetItemConfig(v.item_id)
    	if config then
    		value = value ~= "" and value.."\n" or value
	    	value = value..config.name.."X "..v.num
	    end
    end
    return value
end

------------------------------------------------------------------------升级福利
function WelfareWGData.CreateUpGradeVo()	--初始化
	local vo = {}
	vo.upgrade_list = nil
	vo.uplevel_reward_mark = {}
	return vo
end

function WelfareWGData.CreateUpGradeCellVo(info)--每一项列表数据
	local vo = {}
	vo.seq = info.seq or 0 				--索引
	vo.level = info.level or 0
	local prof = RoleWGData.Instance:GetRoleProf()
	prof = prof % 2 == 0 and 2 or 1
    vo.reward_item = info["reward_item_prof_"..prof] and __TableCopy(info["reward_item_prof_"..prof]) or {}
	vo.status = info.status or SEVEN_COMPLETE_STATUS.WEIDACHENG
	vo.gift_data = nil
	vo.limit_or_not = info.limit_or_not
	vo.limit_count = info.limit_count
	return vo
end

function WelfareWGData:GetUpGradeVo()
	return self.upgradevo
end



function WelfareWGData:UpdataUpGrade()
	if not self.upgradevo.upgrade_list then
		self.upgradevo.upgrade_list = {}
		local config = ConfigManager.Instance:GetAutoConfig("welfare_auto").uplevel_reward --奖励配置
		for k, v in pairs(config) do
			self.upgradevo.upgrade_list[k] = WelfareWGData.CreateUpGradeCellVo(v) 	--每一项列表数据
		end
	end
	local remain_count = 0
	local level = GameVoManager.Instance:GetMainRoleVo().level
	for k, v in pairs(self.upgradevo.upgrade_list) do
		if self.upgradevo.uplevel_reward_mark then			-- 登记奖励标识
			if v.level <= level then						-- 等级高于奖励等级
				if 0 == self.upgradevo.uplevel_reward_mark[32 - (v.seq)] then
					remain_count = remain_count + 1			-- 剩余数量
					v.status = SEVEN_COMPLETE_STATUS.KELINGQU 	-- 设置未领取
				else
					v.status = SEVEN_COMPLETE_STATUS.YILINGQU		-- 设置已领取
				end
			else
				v.status = SEVEN_COMPLETE_STATUS.WEIDACHENG
			end
			if v.level <= level then
				if v.limit_or_not == 1 and self.upgradevo.uplevel_reward_mark[32 - (v.seq)] == 0 then
					if self:GetUpLevelInfo(v.seq + 1) > 0 and v.limit_count - self:GetUpLevelInfo(v.seq +1 ) == 0 then
						remain_count = remain_count - 1
					end
				end
			end
		end

		if not v.gift_data then
			-- local gift_data_null  = {}
			-- local gift_data_child = nil
			-- for m,n in pairs(v.reward_item) do
			-- 	gift_data_child = nil
			-- 	local _,item_type = ItemWGData.Instance:GetItemConfig(n.item_id)
			-- 	-- print_error(item_type,n.item_id)
			-- 	if item_type == GameEnum.ITEM_BIGTYPE_GIF then --礼包类
			-- 		--礼包暂不解析，配表问题一堆，策划一直改，一直错
			-- 		-- local data_child = ItemWGData.Instance:GetGiftConfig(n.item_id)
			-- 		-- if data_child then
			-- 		-- 	gift_data_child = data_child.item_data
			-- 		-- end
			-- 	else
			-- 		table.insert(gift_data_null,n)
			-- 	end
			-- 	if gift_data_child then
			-- 		for p,q in pairs(gift_data_child) do
			-- 			table.insert(gift_data_null,q)
			-- 		end
			-- 	end

			-- end
			local equip_show = ConfigManager.Instance:GetAutoConfig("welfare_auto").equip_show
			v.gift_data = v.reward_item
			for p,q in pairs(v.gift_data) do
				for m,n in pairs(equip_show) do
					if q.item_id == n.item_ID then
						q["star_level"] = n.star_level
					end
				end
			end
		end
	end

	local function scortfun (a, b)
		if a.status == b.status then
			return a.level < b.level
		else
			return a.status > b.status
		end
	end
	table.sort(self.upgradevo.upgrade_list, scortfun)
	-- self.remind_num_list[RemindId.welfare_levelup] = remain_count
	return self.upgradevo.upgrade_list
end

function WelfareWGData:GetUpGradeList()
	return self.upgradevo.upgrade_list
end

function WelfareWGData.CreateVipGiftVo()	--初始化
	local vo = {}
	vo.vipgift_list = nil
	vo.vipgift_reward_mark = {}
	return vo
end

function WelfareWGData.CreateVipGiftCellVo(info)--每一项列表数据
	local vo = {}
	vo.seq = info.seq or 0 				--索引
	vo.level = info.level or 0
	vo.vip_limit = info.vip_limit or 0
    vo.reward_item = info.reward_item and __TableCopy(info.reward_item) or {}
	vo.status = info.status or SEVEN_COMPLETE_STATUS.WEIDACHENG
	vo.gift_data = nil
	vo.limit_or_not = info.limit_or_not
	vo.limit_count = info.limit_count
	return vo
end

function WelfareWGData:GetVipGiftVo()
	return self.vipgiftvo
end

function WelfareWGData:UpdataVipGift()
	if not self.vipgiftvo.vipgift_list then
		self.vipgiftvo.vipgift_list = {}
		local config = ConfigManager.Instance:GetAutoConfig("welfare_auto").VIP_reward --奖励配置
		for k, v in pairs(config) do
			self.vipgiftvo.vipgift_list[k] = WelfareWGData.CreateVipGiftCellVo(v) 	--每一项列表数据
		end
	end
	local remain_count = 0
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	if VipWGData.Instance:IsVipTy() then
		vip_level = 0
	end
	for k, v in pairs(self.vipgiftvo.vipgift_list) do
		if self.vipgiftvo.vipgift_reward_mark then			-- 登记奖励标识
			if v.level <= level and v.vip_limit <= vip_level then						-- 等级高于奖励等级
				if 0 == self.vipgiftvo.vipgift_reward_mark[32 - (v.seq)] then
					remain_count = remain_count + 1			-- 剩余数量
					v.status = SEVEN_COMPLETE_STATUS.KELINGQU 	-- 设置未领取
				else
					v.status = SEVEN_COMPLETE_STATUS.YILINGQU		-- 设置已领取
				end
			else
				v.status = SEVEN_COMPLETE_STATUS.WEIDACHENG
			end
			if v.level <= level and v.vip_limit <= vip_level then
				if v.limit_or_not == 1 and self.vipgiftvo.vipgift_reward_mark[32 - (v.seq)] == 0 then
					if self:GetVipGiftInfo(v.seq + 1) > 0 and v.limit_count - self:GetVipGiftInfo(v.seq +1 ) == 0 then
						remain_count = remain_count - 1
					end
				end
			end
		end

		if not v.gift_data then
			v.gift_data = v.reward_item
		end
	end

	local function scortfun (a, b)
		if a.status == b.status then
			return a.level < b.level
		else
			return a.status > b.status
		end
	end
	table.sort(self.vipgiftvo.vipgift_list, scortfun)
	-- self.remind_num_list[RemindId.welfare_levelup] = remain_count
	return self.vipgiftvo.vipgift_list
end

function WelfareWGData:GetVipGiftList()
	return self.vipgiftvo.vipgift_list
end

function WelfareWGData:GetUpGradeRewardByLv(lv)
	for k,v in pairs(self:UpdataUpGrade()) do
		if v.level == lv then
			return v
		end
	end
	return nil
end

function WelfareWGData:GetUpGradeRewardStateByLv(lv)
	for k,v in pairs(self:UpdataUpGrade()) do
		if v.level == lv then
			return v.status
		end
	end
	return SEVEN_COMPLETE_STATUS.WEIDACHENG
end

--活动图标是否显示
function WelfareWGData:CheckIconVisible()
	if self.is_seven_day_end then
		return false
	else
		return true
	end
end

------------------------------------------------------------------------活跃度
function WelfareWGData:GetRewardConfigList()
	local pkg_data = ConfigManager.Instance:GetAutoConfig("activedegree_auto").reward
	local item_data_list = {}
	for k,v in pairs(pkg_data) do
		item_data_list[#item_data_list + 1] = v.item
	end
	return item_data_list
end

--通过活动标题类型获取配置
function WelfareWGData:GetDegreeListBytitle(type)
	local degree = ConfigManager.Instance:GetAutoConfig("activedegree_auto").degree
	local list = {}
	for k,v in pairs(degree) do
		if v.title == type and v.is_show == 1 then
			list[#list + 1] = v
		end
	end
	-- list[0] = table.remove(list, 1)
	return list
end

--通过活动类型获取配置
function WelfareWGData:GetDegreeListByType(type)
	local degree = ConfigManager.Instance:GetAutoConfig("activedegree_auto").degree
	for k,v in pairs(degree) do
		if v.type == type then
			return v
		end
	end
	return nil
end

--------------------------------------------------------好友畅玩
function WelfareWGData:SetFriendinviteNotice(protocol)
	self.friendinvite_notice.notice_type = protocol.notice_type
	self.friendinvite_notice.operate_role_id = protocol.operate_role_id
	self.friendinvite_notice.operate_role_name = protocol.operate_role_name
end

function WelfareWGData:GetFriendinviteNotice()
	return self.friendinvite_notice
end

function WelfareWGData:SetSyncFriendInviteInfo(protocol)
	self.syncfriendinvite_info.active_flag = protocol.active_flag
	self.syncfriendinvite_info.fetch_flag = protocol.fetch_flag
	--self.syncfriendinvite_info.beinvited_list = protocol.beinvited_list

	local function SortMemberList(a, b)
		if a.level > b.level then
			return true
		end
	end
	table.sort(protocol.invite_list,SortMemberList)
	self.syncfriendinvite_info.invite_list = protocol.invite_list

	self:GetRewardVo().reward_mark = bit:d2b(protocol.fetch_flag)
	-- RemindManager.Instance:Fire(RemindName.WelfareChangWan)--畅玩
	RemindManager.Instance:Fire(RemindName.WelfareT)--福利侧边第一个按钮
	RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮

end

function WelfareWGData:GetSyncFriendInviteInfo()
	return self.syncfriendinvite_info
end


-- reward
function WelfareWGData.CreateFriendRewardVo()
	local vo = {}
	vo.reward_list = nil
	vo.reward_mark = {}
	return vo
end

function WelfareWGData.CreateRewardCellVo(info)
	local vo = {}
	vo.index = info.index or 0 				--索引
	vo.role_level = info.role_level or 0
	vo.need_num = info.need_num or 0
    vo.reward_item = info.reward_item and __TableCopy(info.reward_item) or {}
	vo.status = info.status or SEVEN_COMPLETE_STATUS.WEIDACHENG
	return vo
end

function WelfareWGData:GetRewardVo()
	return self.rewardvo
end

function WelfareWGData:UpdataReward()
	local  remain_count  =  0
	if not self.rewardvo.reward_list then
		self.rewardvo.reward_list = {}
		local config = ConfigManager.Instance:GetAutoConfig("friendinviteconfig_auto").friendinvite
		for k, v in pairs(config) do
			self.rewardvo.reward_list[k] = WelfareWGData.CreateRewardCellVo(v)
		end
	end

	---------------
	-- 达标状态
	local conditions = {}
	local num_list = {}
	for k, v in pairs(self.rewardvo.reward_list) do
		if nil ~= self:GetSyncFriendInviteInfo().invite_list then
			for kk, vv in pairs(self:GetSyncFriendInviteInfo().invite_list) do
				if vv.level >= v.role_level then
					num_list[k] = num_list[k] or 0
					num_list[k] = num_list[k] + 1	-- 被邀请人等级大于奖励所需等级，人数加1
				end
			end
		end
	end

	for k, v in pairs(self.rewardvo.reward_list) do
		v.cur_num = num_list[k]			-- 设置进度
		num_list[k] = num_list[k] or 0
		if num_list[k] >= v.need_num then
			conditions[k]=1				-- 达标状态标识
		end
	end

	for k, v in pairs(self.rewardvo.reward_list) do
		if conditions then
			if conditions[k] == 1 then						-- 状态为可领取
				if 0 == self.rewardvo.reward_mark[32 - (v.index)] then
					v.status = SEVEN_COMPLETE_STATUS.KELINGQU 	-- 设置未领取
					remain_count = remain_count + 1
				else
					v.status = SEVEN_COMPLETE_STATUS.YILINGQU		-- 设置已领取
				end
			else
				v.status = SEVEN_COMPLETE_STATUS.WEIDACHENG
			end
		end
	end

	local function scortfun (a, b)
		if a.status == b.status then
			return a.index < b.index
		else
			return a.status > b.status
		end
	end
	table.sort(self.rewardvo.reward_list, scortfun)
	-- self.remind_num_list[RemindId.welfare_friend] = remain_count
	return self.rewardvo.reward_list
end


------------------------------------------------------------------------------------------------
-- 更新公告信息
function WelfareWGData:SetUpdateNoticeInfo(info)
	if nil == info then return end
	self.update_affiche.server_version = info.server_version or 0
	self.update_affiche.fetch_reward_version = info.fetch_reward_version or 0
	self.is_update_affiche_ok = true
	-- RemindManager.Instance:Fire(RemindName.WelfareGongGao)--公告
	-- RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮

end

-- 是否可以领取奖励
function WelfareWGData:CanFetchReward()
	--return self.update_affiche.server_version > self.update_affiche.fetch_reward_version
	return self.notify_info.version > self.notify_info.reward_version
end

function WelfareWGData:GetAfficheRemind()
	return self:CanFetchReward() and 1 or 0
end

function WelfareWGData:GetUpdateNoticeInfo()
	return self.update_affiche
end

function WelfareWGData:IsUpdateAfficheOk()
	return self.is_update_affiche_ok
end

function WelfareWGData:GetUpdateAfficheCfg()
	return ConfigManager.Instance:GetAutoConfig("updatenotice_auto").affiche_award
end

-- 获取当前公告显示配置
function WelfareWGData:GetUpdateAfficheCfgByLevel()
	local level = RoleWGData.Instance.role_vo.level
	local cfg = self:GetUpdateAfficheCfg()
	for k,v in pairs(cfg) do
		if level >= v.grade_rise and level <= v.grade_end then
			return v
		end
	end
end
-- 设置后台公告版本
function WelfareWGData:SetNotifyVersion(version)
	self.notify_info.version = version
	self.notify_info.is_new = false
end

-- 设置后台公告内容
function WelfareWGData:SetNotifyInfo(info)
	self.notify_info.version = tonumber(info.last_ver)
	self.notify_info.reward_version = tonumber(info.user_ver)
	self.notify_info.content = info.content
	self.notify_info.reward_item = info.items
	self.notify_info.is_new = true
end

-- 后台公告内容奖励获取成功
function WelfareWGData:NotifyRewardGetSuc()
	self.notify_info.reward_version = self.notify_info.version
end

-- 获取后台公告内容
function WelfareWGData:GetNotifyInfo()
	return self.notify_info
end

-- 设置日常公告内容
function WelfareWGData:SetEverydayContent(data)
	if "table" ~= type(data) or nil == next(data) then return -1 end

	local content = ""
	for k,v in ipairs(data) do
		local str = v.content
		str = string.gsub(str, "\n", "\n")
		content = content .. str .. "\n"
	end

	self.everyday_text = content
end

-- 获取日常公告内容
function WelfareWGData:GetEverydayContent()
	return self.everyday_text or ""
end
------------------------------------------------------------------------------------------------
function WelfareWGData:SetWelfareQiFuData(gold_buycoin_times, gold_buyyuanli_times, free_buycoin_time)
	self.welfare_qifu_data.gold_buycoin_times = gold_buycoin_times
	self.welfare_qifu_data.gold_buyyuanli_times = gold_buyyuanli_times
	self.welfare_qifu_data.free_buycoin_time = free_buycoin_time
	RemindManager.Instance:Fire(RemindName.WelfareQiFu)--祈福
	RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮

end

function WelfareWGData:GetWelfareQiFuDataList()
	local data_list = {}
	data_list[1] = {
		free_buycoin_time = self.welfare_qifu_data.free_buycoin_time,
		gold_buy_times = self.welfare_qifu_data.gold_buycoin_times,
	}
	data_list[2] = {
		free_buycoin_time = 0,
		gold_buy_times = self.welfare_qifu_data.gold_buyyuanli_times,
	}
	return data_list
end

function WelfareWGData:GetWelfareQiFuBuyCost(num)
	for k,v in ipairs(self.welfare_qifu_cfg.qifu_cost) do
		if v.min_buy_times <= num and num <= v.max_buy_times then
			return v
		end
	end
end

function WelfareWGData:GetWelfareQiFuExpReward()
	local level = GameVoManager.Instance:GetMainRoleVo().level
	return self.welfare_qifu_cfg.qifu_reward[level].exp
end

function WelfareWGData:GetWelfareQiFuCoinReward()
	return self.welfare_qifu_cfg.qifu_coin_num
end

function WelfareWGData:SetUpLevelWelfare(protocol)
	self.chongji_limit = protocol.chongji_limit
	--跨服中不要刷新该红点数据是错的
	if not IS_ON_CROSSSERVER then
		RemindManager.Instance:Fire(RemindName.Welfare)--福利主界面按钮
	end
end
function WelfareWGData:GetUpLevelWelfare()
	return self.chongji_limit
end
--冲级获取次数
function WelfareWGData:GetUpLevelInfo(seq)
	local upgrade_states = 0
	local vipgift_states = 0
	if self.upgradevo.uplevel_reward_mark then
		upgrade_states = self.upgradevo.uplevel_reward_mark[32 - (seq - 1)]
	end
	if self.vipgiftvo.vipgift_reward_mark then
		vipgift_states = self.vipgiftvo.vipgift_reward_mark[32 - (seq - 1)]
	end
	for k,v in ipairs(self:GetUpLevelWelfare()) do
	 	if  k ==  seq then
	 		if upgrade_states ~= 1 and vipgift_states == 1 then
	 			local num = v
	 			return num - 1
	 		end
	 		return v
	 	end
	end
	return 0
end

--获取vip礼包次数
function WelfareWGData:GetVipGiftInfo(seq)
	local upgrade_states = 0
	local vipgift_states = 0
	if self.upgradevo.uplevel_reward_mark then
		upgrade_states = self.upgradevo.uplevel_reward_mark[32 - (seq - 1)]
	end
	if self.vipgiftvo.vipgift_reward_mark then
		vipgift_states = self.vipgiftvo.vipgift_reward_mark[32 - (seq - 1)]
	end
	for k,v in ipairs(self:GetUpLevelWelfare()) do
	 	if  k ==  seq then
	 		if upgrade_states == 1 and vipgift_states ~= 1 then
	 			local num = v
	 			return num - 1
	 		end
	 		return v
	 	end
	end
	return 0
end

function WelfareWGData:GetExpLevel()
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local config = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").level_reward_list
	 for k,v in ipairs(config) do
	 	if  level == v.level then
	 		return v
	 	end
	 end
	 return nil
end
--福利主界面按钮
function WelfareWGData:IsShowWelfareRedPoint()
	--因为配置表里的面版名称与其他地方有些不同,故写的比较死板
	-- print_error(fun_list.welfare_kuanghuan.trigger_param,fun_list.welfare_qifu.trigger_param,fun_list.welfare_dailyfind.trigger_param,role_level)
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfareTRedPoint() then--福利
		return 1
	end
	if IS_ON_CROSSSERVER and ShowRedPoint.SHOW_RED_POINT == self.rember_chongji_red then
		return 1
	end
	if IS_ON_CROSSSERVER and ShowRedPoint.SHOW_RED_POINT == self.rember_vipgift_red then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfareLevelOrVipGiftRedPoint(GITT_LINGQU_TYPE.UPGRADE) then --等级礼包
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfareLevelOrVipGiftRedPoint(GITT_LINGQU_TYPE.VIPGIFT) then --vip礼包
		return 1
	end

	-- if fun_list and fun_list.welfare_dailyfind.trigger_param<=role_level then
	-- 	if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfareZhaoHuiRedPoint() then--找回
	-- 		return 1
	-- 	end
	-- end
	-- if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfareQiFuRedPoint() then--祈福
	-- 	return 1
	-- end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfareGongGaoRedPoint() then--3
		return 1
	end

	--[[if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfareOnlineGiftRedPoint() then
		return 1
	end--]]
	return 0
end

--福利侧边第一个按钮
function WelfareWGData:IsShowWelfareTRedPoint()
	-- local fun_list = ConfigManager.Instance:GetAutoConfig("funopen_auto").funopen_list
	-- local role_level = RoleWGData.Instance.role_vo.level
	-- if fun_list and fun_list.welfare_kuanghuan.trigger_param>role_level then
	-- 	return 0
	-- end
	-- if ShowRedPoint.SHOW_RED_POINT == self:IsShowZaiXianRedPoint() then
	-- 	return 1
	-- end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowQianDaoRedPoint() then
		return 1
	end

	-- if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfarePaperRedPoint() then

	-- 	return 1
	-- end

	-- if ShowRedPoint.SHOW_RED_POINT == self:IsShowChangWanRedPoint() then
	-- 	return 1
	-- end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowSevenServerRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowWelfarePhoneBindRedPoint() then --手机绑定
		return 1
	end

	return 0
end
--在线
function WelfareWGData:IsShowZaiXianRedPoint()
	local data_list = self:GetZaiXianListData()
	local online_time = self:GetTodayOnlineTime()
	if data_list then
		for k,v in pairs(data_list) do
			if not v.is_lingqu  and v.min_time - online_time<=0 then
				return 1
			end
		end

	end
	return 0
end
--签到
function WelfareWGData:IsShowQianDaoRedPoint()
	local qiandao_cfg = self:GetQianDaoItemCfg()
	local qiandao_info = self:GetQianDaoInfo()
	local leiji_item_cfg = self:GetLeiJiItemCfg()
	if qiandao_cfg then
		for k,v in pairs(qiandao_cfg) do
			if v.flag == QIANDDAO_STATUS.QIANDAO then--领取
				return 1
			-- elseif v.flag == QIANDDAO_STATUS.ALREADYGET and RechargeWGData.Instance:GetTodayRecharge() ~= 0 and qiandao_info.day_chongzhi_sign_in_reward_mark[32 - v.day] == 0 and qiandao_info.to_day == v.day then --在领取一次
			-- 	return 1
			end
		end
	end
	if not IsEmptyTable(leiji_item_cfg) then
		for k,v in pairs(leiji_item_cfg) do
			if v.flag == QIANDDAO_STATUS.QIANDAO then
				return 1
			end
		end
	end
	-- .flag == QIANDDAO_STATUS.QIANDAO
	return 0
end
function WelfareWGData:IsShowWelfarePaperRedPoint()
	local red_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	local person_info = self:GetPersonRedpaperInfo()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if IsEmptyTable(person_info) then
		return 0
	end

	local have_count = false
	local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	-- if person_info.world_receive_count < red_cfg.other_config[1].world_daily_fetch_count_limit then
	-- 	have_count = true
	-- end
	local data_base_cfg = {}
	local red_all_info =self:GetWorldRedpaperAllInfo()
	for m,n in pairs(red_all_info) do
		if n.paper_type == 0 then
			data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(n.paper_level)
		else
			data_base_cfg = WelfareWGData.Instance:GetWelfareWorldRedpaperSeqCfg(n.paper_type,n.paper_level)
		end

		local is_get_over = false
		local have_get_count = #n.record_list
		for k,v in pairs(n.record_list) do
			if v.uid == role_id then
				is_get_over = true
				break
			end
		end

		if data_base_cfg and data_base_cfg.bind_gold > 0 then
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.vip_gold_bind_max
			else
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.gold_bind_max
			end

		else
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.vip_sliver_ticket_max
			else
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.sliver_ticket_max
			end
		end

		if data_base_cfg and have_get_count < data_base_cfg.num and not is_get_over and have_count then
			return 1
		end
	end

	local data_lsit = WelfareWGData.Instance:GetGuildSystemRedpaperCfg(2)
	for k,v in pairs(data_lsit) do
		if v.sort_index == 0 then
			return 1
		end
	end

	return 0
end






--畅玩
function WelfareWGData:IsShowChangWanRedPoint()
	local list = self:UpdataReward()
	if list then
		for k,v in pairs(list) do
			if v.status == SEVEN_COMPLETE_STATUS.KELINGQU then
				return 1
			end
		end
	end
	return 0

end
--七天登陆
function WelfareWGData:IsShowSevenServerRedPoint()
	local day_list = self:GetSevenDayCfg()
	if day_list then
		for k,v in pairs(day_list) do
			if v.status == SEVEN_COMPLETE_STATUS.KELINGQU then
				return 1
			end
		end
	end
	return 0
end

--冲级
function WelfareWGData:IsShowWelfareChongJiRedPoint()
	if IS_ON_CROSSSERVER and self.rember_chongji_red then
		return self.rember_chongji_red
	end
	self.rember_chongji_red = 1
	local list = self:UpdataUpGrade()
	if list then
		for k,v in pairs(list) do
			local status = v.status == SEVEN_COMPLETE_STATUS.KELINGQU or v.status == SEVEN_COMPLETE_STATUS.WEIDACHENG
			if status then
				if v.limit_or_not == 1 and v.status ~= SEVEN_COMPLETE_STATUS.WEIDACHENG then
					local level_num  = self:GetUpLevelInfo(v.seq + 1)
					if v.limit_count > level_num then
						return 1
					end
				elseif v.status == SEVEN_COMPLETE_STATUS.KELINGQU then
					return 1
				end
			end
		end
	end
	self.rember_chongji_red = 0
	return 0
end

--找回
function WelfareWGData:IsShowWelfareZhaoHuiRedPoint()
	-- local fun_list = ConfigManager.Instance:GetAutoConfig("funopen_auto").funopen_list
	-- local role_level = RoleWGData.Instance.role_vo.level
	-- if fun_list and fun_list.welfare_dailyfind.trigger_param<=role_level then
	-- 	return 0
	-- end
	local daily_find_datas = self:GetDailyFindItems()
	if not IsEmptyTable(daily_find_datas) then
		return 1
	end
	return 0
end

--公告
function WelfareWGData:IsShowWelfareGongGaoRedPoint()
	if self:CanFetchReward() then--可以领取
		return 1
	end
	return 0
end

--数据刷新来源true  表示来源自己 false 表示他人
function WelfareWGData:SetDataFrom(from)
	self.from = from
end
function WelfareWGData:GetDataFrom()
	return self.from
end

function WelfareWGData:GetOther()
	local other = ConfigManager.Instance:GetAutoConfig("welfare_auto").other
	return other[1]
end

function WelfareWGData:GetOpenGameTime(open_game_timestamp)
	self.open_day = open_game_timestamp
end

function WelfareWGData:GetWelfareCfg()
	return self.redpape_cfg
end

function WelfareWGData:GetWelfareGuildRedpaperCfg()
	return self.guild_redpaper_cfg
end

function WelfareWGData:GetWelfareGuildRedpaperTypeCfg(red_type,level)
	local cfg = self:GetWelfareGuildRedpaperCfg()
	return cfg and cfg[red_type] and cfg[red_type][level]
end

function WelfareWGData:GetWelfareCustomRedpaperCfg()
	return self.custom_redpaper_cfg
end

function WelfareWGData:GetWelfareCustomRedpaperSeqCfg(seq)
	local cfg = self:GetWelfareCustomRedpaperCfg()
	return cfg and cfg[seq]
end

function WelfareWGData:GetWelfareWorldRedpaperCfg()
	return self.world_redpaper_cfg
end

function WelfareWGData:GetWelfareWorldRedpaperCfgByTypeAndLevel(type, level)
	return (self.world_redpaper_cfg[type] or {})[level] or {}
end

function WelfareWGData:GetWelfareWorldRedpaperSeqCfg(red_type,level)
	local cfg = self:GetWelfareWorldRedpaperCfg()
	return cfg and cfg[red_type] and cfg[red_type][level]
end

--仙盟红包数据
function WelfareWGData:SetGuildRedpaperAllInfo(protocol)
	self.guild_redpaper_all_info = {}

	for k,v in pairs(protocol.guild_paper_all_info) do
		if v.owner_uid > 0 then
			table.insert(self.guild_redpaper_all_info,v)
			AvatarManager.Instance:SetAvatarKey(v.owner_uid, v.avatar_key_big, v.avatar_key_small)
			for m,n in pairs(v.record_list) do
				if n.uid > 0 then
					AvatarManager.Instance:SetAvatarKey(n.uid, n.avatar_key_big, n.avatar_key_small)
				end
			end
		end
	end
end
function WelfareWGData:GetGuildRedpaperAllInfo()
	return self.guild_redpaper_all_info or {}
end
--仙盟红包数据改变
function WelfareWGData:GuildRedpaperAllInfoChange(protocol)
	local is_new_info = true
	AvatarManager.Instance:SetAvatarKey(protocol.info.owner_uid, protocol.info.avatar_key_big, protocol.info.avatar_key_small)
	for m,n in pairs(protocol.info.record_list) do
		if n.uid > 0 then
			AvatarManager.Instance:SetAvatarKey(n.uid, n.avatar_key_big, n.avatar_key_small)
		end
	end
	if not IsEmptyTable(self.guild_redpaper_all_info) then
		for k,v in pairs(self.guild_redpaper_all_info) do
			if v.info_index == protocol.info.info_index then
				if protocol.info.owner_uid < 0 then
					table.remove(self.guild_redpaper_all_info,k)
				else
					self.guild_redpaper_all_info[k] = protocol.info
				end
				--self.guild_redpaper_all_info[k] = protocol.info
				is_new_info = false
				break
			end
		end
	end
	if is_new_info or IsEmptyTable(self.guild_redpaper_all_info) then
		if protocol.info.owner_uid ~= -1 then
			table.insert(self.guild_redpaper_all_info,protocol.info)
		end

	end
--	print_error(self.is_world_paper_type,self.show_record_data )
	if self.is_world_paper_type and self.is_world_paper_type == 1 then
		self:OpenRecordPanelMain()
	end
end
--个人红包数据
function WelfareWGData:SetPersonRedpaperInfo(protocol)
	self.person_redpaper_info_list = protocol
--	print_error(self.person_redpaper_info_list )
end

--系统红包数据
function WelfareWGData:SetSystemRedpaperInfo(protocol)
	self.system_redpaper_info_list = protocol
end

function WelfareWGData:GetSystemRedpaperInfo()
	return self.system_redpaper_info_list
end

function WelfareWGData:GetPersonRedpaperInfo()
	return self.person_redpaper_info_list or {}
end
function WelfareWGData:GetGuildSystemRedpaperCfg(paper_type)
	local cfg = paper_type == 1 and __TableCopy(self.redpape_cfg.guild_redpaper) or __TableCopy(self.redpape_cfg.world_redpaper)
	if (not cfg) or (IsEmptyTable(cfg)) then 
		return {}
	end
	local max_guild_redpaper_type = cfg[#cfg].type
	local base_system_guild_cfg = {}
	for i=1,max_guild_redpaper_type do
		local level,is_active,info_index,info = self:GetPersonBase(paper_type,i)
		for k,v in pairs(cfg) do
			if v.type == i and v.level == level then
				if is_active then
					v.sort_index = 0
					v.creat_timestamp = info.creat_timestamp
				else
					v.sort_index = 1
				end
				v.info_index = info_index
				v.is_guid_packet = paper_type == 1 and true or false
				table.insert(base_system_guild_cfg,v)
			end
		end
	end
	return base_system_guild_cfg
end
function WelfareWGData:GetPersonBase(big_paper_type,paper_type)
	local person_paper_info = self:GetPersonRedpaperInfo().redpaper_list
	if nil == person_paper_info or IsEmptyTable(person_paper_info) then
		return 0 ,false, -1
	end
	for k,v in pairs(person_paper_info) do
		if v.red_paper_type == big_paper_type and v.paper_type == paper_type then
			return v.paper_level,true,v.info_index,v
		end
	end

	--系统红包
	local system_paper_info = self.system_redpaper_info_list.redpaper_list
	if not IsEmptyTable(system_paper_info) then
		for k,v in pairs(system_paper_info) do
			if v.red_paper_type == big_paper_type and v.paper_type == paper_type then
				return v.paper_level, true, v.info_index, v
			end
		end
	end

	return 0,false,-1
end
--世界红包数据
function WelfareWGData:SetWorldRedpaperAllInfo(protocol)
	--self.world_redpaper_all_info = protocol.world_paper_all_info
		self.world_redpaper_all_info = {}

	for k,v in pairs(protocol.world_paper_all_info) do
		if v.owner_uid > 0 then
			table.insert(self.world_redpaper_all_info,v)
			AvatarManager.Instance:SetAvatarKey(v.owner_uid, v.avatar_key_big, v.avatar_key_small)
			for m,n in pairs(v.record_list) do
				if n.uid > 0 then
					AvatarManager.Instance:SetAvatarKey(n.uid, n.avatar_key_big, n.avatar_key_small)
				end
			end
		end
	end
end
function WelfareWGData:GetWorldRedpaperAllInfo()
	return self.world_redpaper_all_info or {}
end
--世界红包数据改变
function WelfareWGData:WorldRedpaperAllInfoChange(protocol)
	local is_new_info = true
	AvatarManager.Instance:SetAvatarKey(protocol.info.owner_uid, protocol.info.avatar_key_big, protocol.info.avatar_key_small)
	for m,n in pairs(protocol.info.record_list) do
		if n.uid > 0 then
			AvatarManager.Instance:SetAvatarKey(n.uid, n.avatar_key_big, n.avatar_key_small)
		end
	end
	if not IsEmptyTable(self.world_redpaper_all_info) then
		for k,v in pairs(self.world_redpaper_all_info) do
			if v.info_index == protocol.info.info_index then
				if protocol.info.owner_uid < 0 then
					table.remove(self.world_redpaper_all_info,k)
				else
					self.world_redpaper_all_info[k] = protocol.info
				end
				is_new_info = false
				break
			end
		end
	end
	if is_new_info or IsEmptyTable(self.world_redpaper_all_info) then
		if protocol.info.owner_uid ~= -1 then
			table.insert(self.world_redpaper_all_info,protocol.info)
		end
	end
	if self.is_world_paper_type and self.is_world_paper_type == 2 then
		self:OpenRecordPanelMain()
	end
end
function WelfareWGData:SetMainUiShowRecordMark(is_world_paper_type,data)
	self.is_world_paper_type = is_world_paper_type
	self.show_record_data = data
end
function WelfareWGData:OpenRecordPanelMain()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if self.is_world_paper_type == 1 then
		for k,v in pairs(self.guild_redpaper_all_info) do
			if v.owner_uid == role_id and self.show_record_data.type == v.paper_type and self.show_record_data.level == v.paper_level then
				GuildWGCtrl.Instance:OpenGuildRedPacketView(v,1)
			end
		end
	else
		for k,v in pairs(self.world_redpaper_all_info) do
			if v.owner_uid == role_id and self.show_record_data.type == v.paper_type and self.show_record_data.level == v.paper_level then
				GuildWGCtrl.Instance:OpenGuildRedPacketView(v,2)
			end
		end
	end
	self.is_world_paper_type = nil
	self.show_record_data = nil
end
function WelfareWGData:OpenRecordPanelByProto(protocol)
	if protocol.red_paper_type == 1 then
		for k,v in pairs(self.guild_redpaper_all_info) do
			if v.info_index == protocol.info_index then
				GuildWGCtrl.Instance:OpenGuildRedPacketView(v,1)
				break
			end
		end
	else
		for k,v in pairs(self.world_redpaper_all_info) do
			if v.info_index == protocol.info_index then
				GuildWGCtrl.Instance:OpenGuildRedPacketView(v,2)
				break
			end
		end
	end
end

--在线礼包
function WelfareWGData:SetOnlineGiftInfo(protocol)
	self.acumulate_online_time = protocol.acumulate_online_time
	self.local_online_time_star = TimeWGCtrl.Instance:GetServerTime() -- 本地保存在线时间起始
	self.reward_fetch_flag = bit:d2b(protocol.reward_fetch_flag)
end

function WelfareWGData:GetOnLineStarTime()
	return self.local_online_time_star or TimeWGCtrl.Instance:GetServerTime()
end

function WelfareWGData:GetOnlineTime()
	return self.acumulate_online_time or 0
end

function WelfareWGData:GetRewardFlag(stage)
	if self.reward_fetch_flag then
		return self.reward_fetch_flag[32-stage]
	end
	return 0
end

function WelfareWGData:GetOnlineRewardCfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or -1
	if open_day < 0 then
		return
	end
	local reward_cfg = ConfigManager.Instance:GetAutoConfig("onlinegiftcfg_auto").reward_cfg
	-- local max_grade = self:GetMaxRewardStage()
	-- local reward_data = {}
	-- local data = nil
	-- for k,v in pairs(reward_cfg) do
	-- 	if open_day >= v.og_start_day and open_day <= v.og_end_day then
	-- 		data = v
	-- 	end
	-- end
	-- if data then
	-- 	for i=1,max_grade do
	-- 		local data_info = {}
	-- 		data_info["index"] = data["index"]
	-- 		data_info["stage"] = i - 1
	-- 		data_info["time"] = data["time_stage_"..i]
	-- 		data_info["reward"] = data["stage_reward_"..i]
	-- 		reward_data[i] = data_info
	-- 	end
	-- end
	return reward_cfg
end

function WelfareWGData:GetCurRewardStage()
	-- local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or -1
	-- if open_day < 0 then
	-- 	return
	-- end
	-- local reward_cfg = ConfigManager.Instance:GetAutoConfig("onlinegiftcfg_auto").reward_cfg
	-- local data = nil
	-- for k,v in pairs(reward_cfg) do
	-- 	if open_day >= v.og_start_day and open_day <= v.og_end_day then
	-- 		data = v
	-- 	end
	-- end
	-- if data then
	-- 	return data.index
	-- end
	return -1
end

function WelfareWGData:GetMaxRewardStage()
	local max_grade = ConfigManager.Instance:GetAutoConfig("onlinegiftcfg_auto").other[1].max_grade
	return max_grade
end

function WelfareWGData:IsShowWelfareOnlineGiftRedPoint()
	local data = self:GetOnlineRewardCfg()
	local online_time = self:GetOnlineTime()
	if nil == data then
		return 0
	end
	for k,v in pairs(data) do
		local need_time = v.time_stage*60
		local flag = self:GetRewardFlag(v.stage)
		if online_time >= need_time and 0 == flag then
			return 1
		end
	end
	return 0
end
--获取一个未到领取时间的时间戳
function WelfareWGData:GetOneEndTime()
	local data = self:GetOnlineRewardCfg()
	local online_time = self:GetOnlineTime()
	if nil == data then
		return 0
	end
	for k,v in pairs(data) do
		local need_time = v.time_stage*60
		local flag = self:GetRewardFlag(v.stage)
		if online_time <= need_time then
			return v.time_stage*60
		end
	end
	return 0
end

function WelfareWGData:GetOneRewardStage()
	local data = self:GetOnlineRewardCfg()
	local online_time = self:GetOnlineTime()
	if nil == data then
		return -1
	end
	for k,v in pairs(data) do
		local need_time = v.time_stage*60
		local flag = self:GetRewardFlag(v.stage)
		if online_time <= need_time then
			return v.stage
		end
	end
	return -1
end
--获取动画是否可以播放
function WelfareWGData:GetAniCanPlay(is_play)
	self.ani_isplay = is_play
end

--获取一个可以播放动画的页签（又可领取播放可领取的第一个，没有的话播放显示时间戳的那个）
function WelfareWGData:GetOneCanAniIndex()
	local data = self:GetOnlineRewardCfg()
	local online_time = self:GetOnlineTime()
	if nil == data then
		return -1,self.ani_isplay
	end
	for k,v in pairs(data) do
		local need_time = v.time_stage*60
		local flag = self:GetRewardFlag(v.stage)
		if online_time >= need_time and flag == 0 then
			return v.stage, self.ani_isplay
		end
	end
	local cur_stage = self:GetOneRewardStage()
	if cur_stage then
		return cur_stage,self.ani_isplay
	end
	return -1,self.ani_isplay
end

--获取一个未到领取时间的时间戳
function WelfareWGData:GetOneOpenLevel()
	local open_level = ConfigManager.Instance:GetAutoConfig("onlinegiftcfg_auto").other[1].open_level
	return open_level
end

--获取15天登录默认选中标签
function WelfareWGData:GetSevenDayDefaultIndex()
	local canday = self:GetCanRewardDay()
	local sevenday_select_index = canday > SEVEN_DAY_LOGIN_MAX_REWARD_DAY and 1 or canday
	return sevenday_select_index
end

--活动找回奖励表
function WelfareWGData:GetNewDailyFindRewardCfg(daily_find, find_type , money_type, role_level)
	for k,v in pairs(self.activity_find_reward_cfg) do
		if v.daily_find == daily_find and v.find_type == find_type
		 	and v.get_type == money_type and role_level >= v.level
			and role_level <= v.max_level then

			return v
		end
	end

	return nil
end

function WelfareWGData:GetNewDailyFindCfgByDailyFind(daily_find)
	local cfg_list = {}
	for k,v in pairs(self.activity_find_cfg) do
		if v.daily_find == daily_find then
			table.insert(cfg_list, v)
		end
	end

	return cfg_list
end

function WelfareWGData:GetWelfareOtherCfg()
	return self.welfare_other_cfg
end

-------------------------------------------------------
--是否为日常找回	找回类型
--活动找回表
function WelfareWGData:GetNewDailyFindCfg(daily_find, find_type)
	for k,v in pairs(self.activity_find_cfg) do
		if v.daily_find == daily_find and v.find_type == find_type then
			return v
		end
	end

	return nil
end

--活动奖励经验值
function WelfareWGData:GetNewDailyFindRewardExp(role_level)
	local cfg = TaskWGData.Instance:GetRoleLevelReward(role_level)
	if cfg then
		return cfg.kill_monster_exp
	end
end

--is_speical-需要对答题红包区分
function WelfareWGData:GetSendRedPaperListInfo(is_speical)
	local person_info = self:GetPersonRedpaperInfo()
	local need_remove_table = person_info.send_system_record
	local system_info = self:GetSystemRedpaperInfo()
	local need_remove_system_info = system_info.send_system_record
 	local data_lsit = self:GetGuildSystemRedpaperCfg(1)
	local last_data_list = {}
	for k,v in pairs(data_lsit) do
		local is_need = true
		for m,n in pairs(need_remove_table) do
			if n.red_paper_type == 1 and n.systerm_paper_type == v.type and n.systerm_paper_level >= v.level then
				is_need = false
				break
			elseif n.red_paper_type == 4 then
				is_need = false
				break
			end
		end
		if need_remove_system_info then
			for i,j in pairs(need_remove_system_info) do
				--答题红包
				if is_speical and v.type == SPECIAL_SEND_TYPE.GUILD_ANSWER then
					local pos = RoleWGData.Instance.role_vo.guild_post
					if pos ~= GUILD_POST.TUANGZHANG and pos ~= GUILD_POST.JiaMengZhu then
						is_need = false
						break
					end
				end
				if j.systerm_paper_type == v.type and j.systerm_paper_level >= v.level then
					is_need = false
					break
				end
			end
		end
		if v.type == SPECIAL_SEND_TYPE.GUILD_ZhuZai then--主宰神殿红包,策划要求暂时去掉
			is_need = false
		elseif v.type == SPECIAL_SEND_TYPE.GUILD_MW_Hunter then
			is_need = false
		end
		if is_need then
			v.is_shoudong = 0
			table.insert(last_data_list,v)
		end
	end
	return last_data_list
end

--获取今日手动红包剩余和可发数量
function WelfareWGData:GetDailyRedBagNumInfo()
	local cfg = self.redpape_cfg.other_config[1]
	local person_info = self:GetPersonRedpaperInfo()
	if nil == person_info or nil == person_info.day_distribute_count then
		return 0, 0
	end

	if nil == cfg.distribute_count_limit_per_day then
		return 0, 0
	end

	local num = cfg.distribute_count_limit_per_day - person_info.day_distribute_count
	return num, cfg.distribute_count_limit_per_day
end

function WelfareWGData:SetCurRedPacketNum(cur_num,max_num)
	self.cur_redpackt_num = cur_num
	self.cur_redpackt_max_num = max_num
end

function WelfareWGData:GetCurRedPacketNum()
	return self.cur_redpackt_num , self.cur_redpackt_max_num
end

---[[F2 等级礼包 or vip礼包
function WelfareWGData:GetUpLevelGiftCfgList()
	return self.level_vip_reward_cfg_list
end

function WelfareWGData:SetExchangeRewardFlag(protocol)
	self.exchange_reward_flag = protocol.exchange_reward_flag
end

function WelfareWGData:GetExchangeRewardFlag()
	return self.exchange_reward_flag or 0
end

function WelfareWGData:SetUpLevelAndVIPGiftInfo(protocol)
	local info_list = {}
	info_list.level_gift_can_fetch_flag = protocol.level_gift_can_fetch_flag
	info_list.level_gift_fetch_flag = protocol.level_gift_fetch_flag
	info_list.vip_level_gift_can_fetch_flag = protocol.vip_level_gift_can_fetch_flag
	info_list.vip_level_gift_fetch_flag = protocol.vip_level_gift_fetch_flag

	local server_count = protocol.server_count
	local temp_count = {}
	local is_infinite = {}
	local cfg_list = self.level_vip_reward_cfg_list or {}
	for i=1,#cfg_list do
		if cfg_list[i].server_count == 0 then
			is_infinite[i] = true
			temp_count[i] = 999999999
		else
			is_infinite[i] = false
			temp_count[i] = cfg_list[i].server_count - server_count[i]
		end
	end
	info_list.server_count = temp_count
	info_list.is_infinite = is_infinite

	self.uplevel_vip_gift_info = info_list
end

function WelfareWGData:GetUpLevelAndVIPGiftInfo()
	return self.uplevel_vip_gift_info
end

function WelfareWGData:GetGiftInfoByIndex(gift_type, index)
	local gift_info = {can_fetch = false, fetch_flag = false, ser_count = 0}
	local gift_info_list = self:GetUpLevelAndVIPGiftInfo()
	if not gift_info_list then
		return gift_info
	end
	if gift_type == GITT_LINGQU_TYPE.UPGRADE then
		if index then
			gift_info.can_fetch = gift_info_list.level_gift_can_fetch_flag[index] == 1
			gift_info.fetch_flag = gift_info_list.level_gift_fetch_flag[index] == 1
			gift_info.ser_count = gift_info_list.server_count[index]
			gift_info.is_infinite = gift_info_list.is_infinite[index]
		end
	elseif gift_type == GITT_LINGQU_TYPE.VIPGIFT then
		if index then
			gift_info.can_fetch = gift_info_list.vip_level_gift_can_fetch_flag[index] == 1
			gift_info.fetch_flag = gift_info_list.vip_level_gift_fetch_flag[index] == 1
			gift_info.ser_count = gift_info_list.server_count[index]
			gift_info.is_infinite = gift_info_list.is_infinite[index]
		end
	end
	return gift_info
end

-- 等级礼包
function WelfareWGData:IsShowWelfareLevelOrVipGiftRedPoint(gift_type)
	if gift_type == GITT_LINGQU_TYPE.UPGRADE then
		if not FunOpen.Instance:GetFunIsOpened(FunName.welfare_upgrade) then
			return 0
		end
	elseif gift_type == GITT_LINGQU_TYPE.VIPGIFT then
		if not FunOpen.Instance:GetFunIsOpened(FunName.welfare_vipgift) then
			return 0
		end
	end

	local gift_info_list = self:GetUpLevelAndVIPGiftInfo()
	if not gift_info_list then
		return 0
	end
	local server_count = gift_info_list.server_count
	local function CheckCanGetReward(can_fetch_flag, fetch_flag)
		for i=1,#can_fetch_flag do
			if can_fetch_flag[i] == 1 and fetch_flag[i] == 0 and server_count[i] > 0 then
				return true
			end
		end
	end

	if gift_type == GITT_LINGQU_TYPE.UPGRADE then
		if CheckCanGetReward(gift_info_list.level_gift_can_fetch_flag, gift_info_list.level_gift_fetch_flag) then
			return 1
		end
	elseif gift_type == GITT_LINGQU_TYPE.VIPGIFT then
		if CheckCanGetReward(gift_info_list.vip_level_gift_can_fetch_flag, gift_info_list.vip_level_gift_fetch_flag) then
			return 1
		end
	end

	return 0
end
--]]

function WelfareWGData:WelfareLevelGiftCallBack(remind_name, num)
	if num > 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Welfare_Upgrade_Gift, 1, function()
			ViewManager.Instance:Open(GuideModuleName.Welfare, TabIndex.welfare_upgrade)
			return true
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Welfare_Upgrade_Gift, 0)
	end	
end

function  WelfareWGData:WelfareVipGiftCallBack(remind_name, num)
	if num > 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Welfare_Vip_Gift, 1, function()
			ViewManager.Instance:Open(GuideModuleName.Welfare, TabIndex.welfare_vipgift)
			return true
		end)
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Welfare_Vip_Gift, 0)
	end	
end

--获取没领取的等级礼包
function WelfareWGData:GetLevelGiftsList()
    local cfg_list = self:GetUpLevelGiftCfgList()
    local ser_data_list = self:GetUpLevelAndVIPGiftInfo()
    local data_list = {}
    if cfg_list and ser_data_list then
		local fetch_flag = ser_data_list.level_gift_fetch_flag
        local server_count = ser_data_list.server_count
        for i=1,#cfg_list do
			if fetch_flag[i] ~= 1 and server_count[i] > 0 then
                data_list = cfg_list[i]
                break
			end
		end
    end
    return data_list
end

--获取等级礼包按等级来的第一个
function WelfareWGData:GetLevelGiftsListFirst()
    local data_list = self:GetLevelGiftsList()
    if not IsEmptyTable(data_list) then
       return data_list, data_list.level_gift_item[0]
    end
    return nil, nil
end

--能否显示等级礼包主界面按钮
function WelfareWGData:CanShowLevelGiftBtn()
    local lv = RoleWGData.Instance:GetRoleLevel()
    local end_lv = self:GetWelfareOtherCfg()[1].end_level
    if lv > end_lv then
        return false, 0
    end
    local first = self:GetLevelGiftsListFirst()
    local remind_lv = self:GetWelfareOtherCfg()[1].remind_level
    if first then
        if lv >= first.level_limit then
            return true, 0
        end
        if lv + remind_lv >= first.level_limit then
            return true, first.level_limit - lv
        end
    end
    return false, 0
end

-- 等级礼包是否可领取并返回下一档礼包的领取等级差.
function WelfareWGData:CheckWelfareLevelCanGetGift()
	local is_show = false
	local next_get_lv = 0

	local gift_info_list = self:GetUpLevelAndVIPGiftInfo()
	if not gift_info_list then
		return is_show, next_get_lv
	end

	local server_count = gift_info_list.server_count
	local can_fetch_flag = gift_info_list.level_gift_can_fetch_flag
	local fetch_flag = gift_info_list.level_gift_fetch_flag

	for i=1,#can_fetch_flag do
		--可领取.
		if can_fetch_flag[i] == 1 and fetch_flag[i] == 0 and server_count[i] > 0 then
			is_show = true
			break
		end
	end

	if not is_show then
		local lv = RoleWGData.Instance:GetRoleLevel()
		local first = self:GetLevelGiftsListFirst()
		local cul_level = self:GetWelfareOtherCfg()[1].cul_level
		if first then
			if lv + cul_level >= first.level_limit then
				is_show = true
				next_get_lv = first.level_limit - lv
			end
		end
	end

	return is_show, next_get_lv
end

function WelfareWGData:GetRandamRedpaperDec()
	local count = #self.redpaper_dec_cfg
	local num = math.random(1, count)
	local cfg = self.redpaper_dec_cfg[num]
	return cfg and cfg.des or Language.Guild.GuildRedPackageDesc
end


-- -----------------------------------------------手机绑定start--------------------------------
-- function WelfareWGData:IsPhoneBindOpen()
-- 	return self.is_phone_bind_open
-- end

-- function WelfareWGData:SetPhoneBindOpen(b)
-- 	self.is_phone_bind_open = b
-- end

-- function WelfareWGData:IsPhoneBindGotReward()
-- 	return self.is_phone_bind_got_reward
-- end

-- function WelfareWGData:SetPhoneBindGotReward(b)
-- 	self.is_phone_bind_got_reward = b
-- end

-- function WelfareWGData:IsPhoneBinded()
-- 	return self.is_phone_binded
-- end

-- function WelfareWGData:SetIsPhoneBinded(b)
-- 	self.is_phone_binded = b
-- end

-- function WelfareWGData:IsShowWelfarePhoneBindRedPoint()
-- 	if self:IsPhoneBinded() and not self:IsPhoneBindGotReward() then
-- 		return 1
-- 	end

-- 	return 0
-- end


-- ---------------------------------------------手机绑定end-------------------------------------



---------------------------------------------------------------------------
-- 新手机验证相关逻辑

function WelfareWGData:SetHandsetverifyRed(value)
	-- self.is_handsetverify_red = value
	-- self:CheckHandsetVerifyRedPoint()
	-- WelfareWGCtrl.Instance:SetRedPoint()
	RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
end

function WelfareWGData:SetHandsetverifySdkRed(value)
	-- self.is_handsetverify_sdk_red = value
	-- self:CheckHandsetVerifySdkRedPoint()
	-- WelfareWGCtrl.Instance:SetRedPoint()
	RemindManager.Instance:Fire(RemindName.WelfarePhoneBind)
end

function WelfareWGData:IsShowWelfarePhoneBindRedPoint()
	if self.have_show_pohoe_red and self.have_open_pohoe_view then
		return 0
	end

	if self:CheckHandsetVerifyRedPoint() or self:CheckHandsetVerifySdkRedPoint() then
		self:SaveHavePhoneRed()
		return 1
	end

	return 0
end

function WelfareWGData:SaveHavePhoneRed()
	self.have_show_pohoe_red = true
end

function WelfareWGData:HaveOpenPhoneView()
	self.have_open_pohoe_view = true
end

function WelfareWGData:SetBindPhoneNumber(value)
	self.bind_phone_number = value
end

function WelfareWGData:GetBindPhoneNumber()
	return self.bind_phone_number
end

function WelfareWGData:SetIsBindPhoneReward(value)--已领取奖励
	self.is_bind_phone_reward = value
end

function WelfareWGData:GetIsBindPhoneReward()
	return self.is_bind_phone_reward
end

-- 设置开启php手机验证按钮
function WelfareWGData:SetIsOpenHandsetVerify(value)
	self.is_open_handset_verify = value
end

-- 获取开启php手机验证按钮
function WelfareWGData:GetIsOpenHandsetVerify()
	return self.is_open_handset_verify
end

function WelfareWGData:IsPhoneBindOpen()
	return (self:GetIsOpenHandsetVerify() or self:GetIsOpenHandsetVerifySdk()) and not self.is_bind_phone_reward
end

function WelfareWGData:GetBindPhoneOpenType()
	if self:GetIsOpenHandsetVerify() then
		return BIND_PHONE_TYPE.YUN_YING_BIND

	elseif self:GetIsOpenHandsetVerifySdk() then
		return BIND_PHONE_TYPE.SDK_BIND
	end
	return 0
end

-- 设置开启sdk手机验证按钮
function WelfareWGData:SetIsOpenHandsetVerifySdk(value)
	self.is_open_handset_verify_sdk = value
end
-- 获取开启sdk手机验证按钮
function WelfareWGData:GetIsOpenHandsetVerifySdk()
	return self.is_open_handset_verify_sdk
end

-- 设置绑定手机状态
function WelfareWGData:SetBindPhoneState(value)
	local user_id = GameVoManager.Instance:GetUserVo().account_user_id or ""
	PlayerPrefsUtil.SetInt(user_id .. "bind_phone_state", value)
end

-- 获取绑定手机状态
function WelfareWGData:GetBindPhoneState()
	local user_id = GameVoManager.Instance:GetUserVo().account_user_id or ""
	return PlayerPrefsUtil.GetInt(user_id .. "bind_phone_state")
end

function WelfareWGData:IsPhoneBinded()
	local bind_value = self:GetBindPhoneState()
	return bind_value and bind_value == 1
end

function WelfareWGData:CheckHandsetVerifyRedPoint()	--检查手机验证红点
	local is_red = false
	if self.is_handsetverify_red and self.is_open_handset_verify and self:GetBindPhoneState() == 0 then
		is_red = true
	end
	--self.red_point_list["HandsetVerify"] = is_red
	return is_red
end

function WelfareWGData:GetHandsetVerifyRemind()		--手机验证红点
	-- if not OpenFunWGData.Instance:CheckIsHide("welfare") then
	-- 	return 0
	-- end 
	return self:CheckHandsetVerifyRedPoint() and 1 or 0
end

function WelfareWGData:CheckHandsetVerifySdkRedPoint()	--检查手机验证SDK红点
	local is_red = false
	if self.is_handsetverify_sdk_red and self.is_open_handset_verify_sdk and self:GetBindPhoneState() == 0 then
		is_red = true
	end
	return is_red
end

function WelfareWGData:GetHandsetVerifySdkRemind()	--手机验证SDK红点
	-- if not OpenFunWGData.Instance:CheckIsHide("welfare") then
	-- 	return 0
	-- end
	return self:CheckHandsetVerifySdkRedPoint() and 1 or 0
end

function WelfareWGData:SetVerifyCheckMaxTime(value)
	self.verify_check_max_time = value
end

function WelfareWGData:GetVerifyCheckMaxTime()
	return self.verify_check_max_time
end

function WelfareWGData:GetBindPhoneRewared()
	if self.phone_bind_reward_list and not IsEmptyTable(self.phone_bind_reward_list) then
		return self.phone_bind_reward_list
	end

	--if self.welfare_bind_phone_cfg and self.welfare_bind_phone_cfg[1] then
	--	return self.welfare_bind_phone_cfg[1].bind_phone_reware or {}
	--end
	return {}
end

function WelfareWGData:SetPhoneBindReward(data)
	if data and data.card then
		local gift_index = tonumber(data.card)
		if gift_index then
			local gift_cfg = self.welfare_bind_phone_cfg[gift_index] or {}
			if gift_cfg and not IsEmptyTable(gift_cfg) then
				local drop_item_list = ItemWGData.Instance:GetGiftDropList(gift_cfg.gift_item_id,1)
				self.phone_bind_reward_list = drop_item_list or {}
			end
		end
	end
end

-----------------------------------------周签到-------------------------------------------------

function WelfareWGData:SetAllInfo(protocol)
	self.open_time = protocol.open_time
	self.reward_can_fetch_flag = protocol.reward_can_fetch_flag --可领取奖励
	self.reward_flag = protocol.reward_flag						--已领取标记
	self.day_reward_flag = protocol.day_reward_flag				--天数奖励标记
	RemindManager.Instance:Fire(RemindName.Welfare)
end

function WelfareWGData:GetRewardCfg()
	return self.wek_reward_cfg
end

function WelfareWGData:GetDayRewardCfg()
	return self.day_reward_cfg
end

function WelfareWGData:GetRewardCanFlag(day)
	return (self.reward_can_fetch_flag and self.reward_can_fetch_flag[day]) or 0
end

function WelfareWGData:GetWeekRewardFlag(day)
	return (self.reward_flag and self.reward_flag[day]) or 0
end

function WelfareWGData:GetSpecialRewardFlag(seq)
	return (self.day_reward_flag and self.day_reward_flag[seq]) or 0
end

--功能开启的时间
function WelfareWGData:GetGuiOpenTime()
	return self.open_time or 0
end

-- 获取当前应该是领取第几天的奖励
-- 根据功能开启时间计算 功能开启时算第一天 7天循环
function WelfareWGData:GetGuiOpenWeek()
	local open_week = TimeUtil.FormatSecond3MYHM1(self:GetGuiOpenTime())
	local cur_week = TimeUtil.FormatSecond3MYHM1(TimeWGCtrl.Instance:GetServerTime())
	local offset_day = 7 - open_week + 1
	local week = (cur_week + offset_day)%7
	week = week == 0 and 7 or week
	return week
end

--天数奖励是否已领取
function WelfareWGData:GetRewardIsGet(day)
	local is_get = self:GetWeekRewardFlag(day)
	return is_get > 0
end

--天数奖励是否可领取
function WelfareWGData:GetRewardIsCanGet(day)
	local is_can_get = self:GetRewardCanFlag(day)
	return is_can_get > 0
end

--额外奖励是否已领取
function WelfareWGData:GetSpecialRewardIsGet(seq)
	local is_get = self:GetSpecialRewardFlag(seq)
	return is_get > 0
end

--额外奖励是否可领取
function WelfareWGData:GetSpecialRewardIsCanGet(seq)
	local day_cfg = (self.day_seq_reward_cfg[seq] or 0).need_day or 0
	local leiji_day = self:GetLeijiLoginday()
	local is_get = self:GetSpecialRewardIsGet(seq)
	if leiji_day >= day_cfg and (not is_get) then
		return true
	end

	return false
end

--天数奖励红点
function WelfareWGData:GetRewardIsRed(day)
	local is_can_get = self:GetRewardCanFlag(day)
	local is_get = self:GetRewardIsGet(day)
	return is_can_get > 0 and (not is_get)
end

--累计登录了几天
function WelfareWGData:GetLeijiLoginday()
	local day = 0
	if not self.reward_can_fetch_flag then
		return day
	end

	for k, v in pairs(self.reward_can_fetch_flag) do
		if v > 0 then
			day = day + 1
		end
	end

	return day
end

--周签到红点
function WelfareWGData:GetAccumulativeLoginRed()
	for k, v in pairs(self.wek_reward_cfg) do
		if self:GetRewardIsRed(v.day) then
			return 1
		end
	end

	for k = 0, 1 do
		local spe_reward = self:GetSpecialRewardIsCanGet(k)
		if spe_reward then
			return 1
		end
	end

	return 0
end