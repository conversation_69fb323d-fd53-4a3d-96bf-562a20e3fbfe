require("game/serveractivity/tehuishop/tehui_shop_view")
require("game/serveractivity/tehuishop/tehui_shop_wg_data")
require("game/serveractivity/tehuishop/next_tehui_show_view")

TehuiShopWGCtrl = TehuiShopWGCtrl or BaseClass(BaseWGCtrl)

function TehuiShopWGCtrl:__init()
	if nil ~= TehuiShopWGCtrl.Instance then
		print("[TehuiShopWGCtrl] attempt to create singleton twice!")
		return
	end
	TehuiShopWGCtrl.Instance = self
	self.data = TehuiShopWGData.New()
	self.view = TehuiShopView.New()
	self.next_panic_view = NextTeHuiShopView.New()  -- 特惠秒杀-下期预览
	self:RegisterAllProtocols()
end

function TehuiShopWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.next_panic_view then
		self.next_panic_view:DeleteMe()
		self.next_panic_view = nil
	end

	TehuiShopWGCtrl.Instance = nil
end

function TehuiShopWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(RAPanicBuyOperaReq)					--随机活动特惠秒杀请求
	self:RegisterProtocol(RAPanicBuyInfo, "OnRAPanicBuyInfo")   --随机活动特惠秒杀
end

-- 打开主窗口
function TehuiShopWGCtrl:Open()
	self.view:Open()
end

function TehuiShopWGCtrl:OpenNextTehuiView()
	self.next_panic_view:Open()
end

function TehuiShopWGCtrl:SendRAPanicBuyOperaReq(opera_type, param_1, param_2)
	local protocol = ProtocolPool.Instance:GetProtocol(RAPanicBuyOperaReq)
	if nil ~= protocol then
		protocol.opera_type = opera_type
		protocol.param_1 = param_1 or 0
		protocol.param_2 = param_2 or 0
		protocol:EncodeAndSend()
	end
end

function TehuiShopWGCtrl:OnRAPanicBuyInfo(protocol)
	self.data:SetRAPanicBuyInfo(protocol)
	self.view:Flush()
end
