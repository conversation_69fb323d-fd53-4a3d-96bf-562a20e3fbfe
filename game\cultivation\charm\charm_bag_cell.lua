CharmBagCell = CharmBagCell or BaseClass(BaseRender)

function CharmBagCell:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.cell)
		self.item_cell:SetIsShowTips(false)
		self.item_cell:SetClickCallBack(BindTool.Bind(self.OnItemClick, self))
		self.item_cell:SetCellBg(ResPath.GetCommonImages("a3_ty_wpk_0"))
	end
end

function CharmBagCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function CharmBagCell:OnFlush()
	if IsEmptyTable(self.data) or self.data.item_id < 0 or self.data.num < 0 then
		self.item_cell:ClearData()
		self.item_cell:SetCellBg(ResPath.GetCommonImages("a3_ty_wpk_0"))
		return
	end

	local item_data = {item_id = self.data.item_id, num = self.data.num, is_bind = self.data.is_bind}
	self.item_cell:SetData(item_data)

	local data_cfg = CultivationWGData.Instance:GetCharmEquipByItemId(self.data.item_id)
	local order_str = string.format(Language.Charm.CharmOrderLevel, data_cfg.order)
	self.item_cell:SetRightTopImageText(order_str)

	local up_flag = CultivationWGData.Instance:CanHolySealItemUp(self.data.item_id)
	self.item_cell:SetUpFlagIconVisible(up_flag)
end

function CharmBagCell:OnItemClick()
	if IsEmptyTable(self.data) then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local btn_callback_event

	if not IsEmptyTable(item_cfg) then
		btn_callback_event = {}
		btn_callback_event[1] = {btn_text = Language.Charm.CharmXiangQian, callback = function()
			local can_xianqian = CultivationWGData.Instance:CanHolySealItemUp(self.data.item_id)
			local data_cfg = CultivationWGData.Instance:GetCharmEquipByItemId(self.data.item_id)
			if can_xianqian then
				TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_xiangqian,
	    		is_success = true, pos = Vector2(0, 0)})
				
				CultivationWGCtrl.Instance:OnCSCharmOperate(CHARM_OPERATE_TYPE.WEAR_EQUIP, data_cfg.solt, self.data.index)
			else
				local slot_cfg = CultivationWGData.Instance:GetCharmHolySealSoltCfgBySolt(data_cfg.solt)
				
				local need_stage = slot_cfg.stage
				if CultivationWGData.Instance:GetXiuWeiState() < need_stage then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Charm.CharmSlotLocked)
				elseif CultivationWGData.Instance:GetXiuWeiState() < data_cfg.need_xiuwei_stage then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Charm.CharmNoStage)
				else
					TipWGCtrl.Instance:ShowSystemMsg(Language.Charm.CharmInlaidHigherLevel)
				end
			end
		end}
	end

	TipWGCtrl.Instance:OpenItem(self.data, ItemTip.HANDLE_CHARM_INLAY, nil, nil, btn_callback_event)
end