require("game/supreme_fields/supreme_fields_wg_data")
require("game/supreme_fields/supreme_fields_view")
require("game/supreme_fields/supreme_fields_battle_view")
require("game/supreme_fields/supreme_fields_dialog_view")
require("game/supreme_fields/supreme_fields_activity_view")
require("game/supreme_fields/supreme_fields_wuxing_view")
require("game/supreme_fields/supreme_fields_wuxing_attr_tips")

SupremeFieldsWGCtrl = SupremeFieldsWGCtrl or BaseClass(BaseWGCtrl)

function SupremeFieldsWGCtrl:__init()
	if SupremeFieldsWGCtrl.Instance then
        error("[SupremeFieldsWGCtrl]:Attempt to create singleton twice!")
	end

	SupremeFieldsWGCtrl.Instance = self
	self.data = SupremeFieldsWGData.New()
	self.view = SupremeFieldsView.New(GuideModuleName.SupremeFieldsWGView)
	self.fields_battle_view = SupremeFieldsBattleView.New()
	self.fields_dialog_view = SupremeFieldsDialog.New()
	self.fields_activity_view = SupremeFieldsActivityView.New(GuideModuleName.SupremeFieldsActView)
	self.fields_wuxing_view = SupremeFieldsWuxing.New()
	self.fields_wuxing_attr_tips = SupremeFieldsWuxingAttrTips.New()
	self:RegisterAllProtocals()
	self:RegisterAllEvents()
end

function SupremeFieldsWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil
	self.view:DeleteMe()
	self.view = nil
	if self.fields_battle_view then
		self.fields_battle_view:DeleteMe()
		self.fields_battle_view = nil
	end

	if self.fields_activity_view then
		self.fields_activity_view:DeleteMe()
		self.fields_activity_view = nil
	end

	if self.fields_dialog_view then
		self.fields_dialog_view:DeleteMe()
		self.fields_dialog_view = nil
	end

	if self.fields_wuxing_view then
		self.fields_wuxing_view:DeleteMe()
		self.fields_wuxing_view = nil
	end

	if self.fields_wuxing_attr_tips then
		self.fields_wuxing_attr_tips:DeleteMe()
		self.fields_wuxing_attr_tips = nil
	end

	SupremeFieldsWGCtrl.Instance = nil
	self:UnRegisterAllEvents()
end

function SupremeFieldsWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSFootLightOperate)
	self:RegisterProtocol(CSFootLightSKillAll)--一键装配
	self:RegisterProtocol(SCFootLightSkill, "SCFootLightSkill")--更新所有的技能孔
	self:RegisterProtocol(SCFootLightCur, "SCFootLightCur")--当前使用独尊领域
	self:RegisterProtocol(SCFootLightItemInfo, "SCFootLightItemInfo")--更新其中一个独尊领域
	self:RegisterProtocol(SCFootLightInfo, "SCFootLightInfo")--全量更新
	self:RegisterProtocol(SCFootLightActive, "SCFootLightActive")--第一次获得领域
	self:RegisterProtocol(SCOAFootLightInfo, "SCOAFootLightInfo")--更新所有的技能孔
	self:RegisterProtocol(SCFootLightAllStarAttrInfo, "SCFootLightAllStarAttrInfo")--全部领域的星级.
	self:RegisterProtocol(SCFootLightSingleTotalStarAttrInfo, "SCFootLightSingleTotalStarAttrInfo")--单个总属性的信息.

	self:RegisterProtocol(CSFootLightSkillAttack)
	self:RegisterProtocol(SCFootLightPlaySkillEff, "OnFootLightPlaySkillEff")
end

function SupremeFieldsWGCtrl:RegisterAllEvents()
    -- 物品改变
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function SupremeFieldsWGCtrl:UnRegisterAllEvents()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
   		self.activity_change_callback = nil
	end
end



-- 物品变化
function SupremeFieldsWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    -- 物品数量增加
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		local change = self.data:CheckStuffRemind(change_item_id)
		if change then
			if self.view:IsOpen() then
				self.view:Flush(nil, "OnShowRed")
			end
			RemindManager.Instance:Fire(RemindName.SupremeFields)
		end
	end
end

-- 活动信息改变
function SupremeFieldsWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	--print_error(activity_type, status)
	if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
		if activity_info == nil then
			return
		end

		local footact_cfg = SupremeFieldsWGData.Instance:GetFootActInfo().other[1]
		if self.fields_activity_view:IsOpen() then
			self.fields_activity_view:Flush()
		else
			if ACTIVITY_STATUS.OPEN == activity_info.status and
				RoleWGData.Instance:GetRoleVo().level >= footact_cfg.open_level then
					local key = "supreme_fields_everyday_tips_key"
					local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
					local need_open_view = true
					if PlayerPrefsUtil.HasKey(key .. main_role_id) then
						need_open_view = SupremeFieldsWGData.Instance:GetNeedEveryDayOpenView()
					end

					SupremeFieldsWGData.Instance:SetEveryDayOpenView()
					if need_open_view then
						self.fields_activity_view:Open()
					end
			end
		end

	end
end


function SupremeFieldsWGCtrl:ReqActivityFootLightInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function SupremeFieldsWGCtrl:CheckNeedCloseAct()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	if activity_info and activity_info.status == ACTIVITY_STATUS.CLOSE then
		return
	end

	local buy_tmb_count, is_buy_free = self.data:GetShopIsBuyFlag()
	local _, cur_shop_cfg = self.data:GetCurShopCfg()
	if IsEmptyTable(cur_shop_cfg) then
		return
	end

	if buy_tmb_count >= cur_shop_cfg.buy_count_limit and is_buy_free then--奖励领取完了,关闭入口
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN, ACTIVITY_STATUS.CLOSE)
		if self.fields_activity_view:IsOpen() then
			self.fields_activity_view:Close()
		end
	end
end

function SupremeFieldsWGCtrl:SCOAFootLightInfo(protocol)
	self.data:SetShopIsBuyFlag(protocol)
	if self.fields_activity_view:IsOpen() then
		self.fields_activity_view:Flush()
	end

	if self.view:IsOpen() then
		self.view:Flush()
	end

	self:CheckNeedCloseAct()
	RemindManager.Instance:Fire(RemindName.SupremeFieldsAct)
end

function SupremeFieldsWGCtrl:SCFootLightAllStarAttrInfo(protocol)
	self.data:SetAllStarAttrInfo(protocol)
	if self.fields_wuxing_view:IsOpen() then
		self.fields_wuxing_view:Flush()
	end

	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function SupremeFieldsWGCtrl:SCFootLightSingleTotalStarAttrInfo(protocol)
	self.data:SetSingleTotalStarAttrInfo(protocol)
	RemindManager.Instance:Fire(RemindName.SupremeFields)
	if self.fields_wuxing_attr_tips:IsOpen() then
		self.fields_wuxing_attr_tips:Flush()
	end

	if self.view:IsOpen() then
		self.view:Flush()
	end
end

--1：请求数据，2:五行孔位激活(param1 = 独尊id, parame2 = 孔id, param3 = bag_index)，3:五行孔位升级(param1 = 独尊id, parame2 = 孔id), 
--4:幻化(param1 = 独尊id)，5：技能孔位解锁(param1 = 洞id)，6：技能上阵(param1 = 洞id, param2 = 独尊id , param3 = 等级(1开始)), 7:领域直购领取免费道具，8:取消幻化(param1 = 独尊id)
--9:请求所有领域的五行通天星级, 10:请求所有领域的五行通天总星级, 11:激活五行通天星级属性加成(param1 = 领域id), 12:激活五行通天总星级属性加成.
function SupremeFieldsWGCtrl:SendOperation(operation_index, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFootLightOperate)
	protocol.operate_type = operation_index or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

--一键上阵
function SupremeFieldsWGCtrl:SendAutoSkillBattle(count, data_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFootLightSKillAll)
	protocol.count = count
	protocol.data_list = data_list
	protocol:EncodeAndSend()
end

function SupremeFieldsWGCtrl:SCFootLightActive(protocol)
	--print_error("SCFootLightActive:::", protocol)
	self:OpenGetNewView(protocol.foot_id)
end

function SupremeFieldsWGCtrl:SCFootLightSkill(protocol)
	--print_error("SCFootLightSkill:::", protocol.all_skill_slots)
	self.data:AllSFootLightSkillInfo(protocol)
	if self.fields_battle_view:IsOpen() then
		self.fields_battle_view:Flush(nil, "OnPutOnSkill")
	end

	if self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.SupremeFields)
end

function SupremeFieldsWGCtrl:SCFootLightCur(protocol)
	--print_error("SCFootLightCur:::", protocol)
	self.data:SetCurUseFootLight(protocol.use_foot_id)
	if self.view:IsOpen() then
		self.view:Flush(nil, "OnFootLightUse")
	end
end

function SupremeFieldsWGCtrl:SCFootLightItemInfo(protocol)
	--print_error("SCFootLightItemInfo:::", protocol)
	self.data:ChangeSFootLightInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(nil, "OnFootLightItem")
	end

	RemindManager.Instance:Fire(RemindName.SupremeFields)
end

function SupremeFieldsWGCtrl:SCFootLightInfo(protocol)
	--print_error("SCFootLightItemInfo:::", protocol.all_skill_slots)
	self:ReqActivityFootLightInfo(OA_FOOT_LIGHT_OPERATE_TYPE.INFO)

	self.data:AllSFootLightInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(nil, "OnAllFootLight")
	end
end

function SupremeFieldsWGCtrl:OpenGetNewView(type_index)
	local protocol = {appe_image_id = type_index, appe_type = ROLE_APPE_TYPE.ZHIZUN,}
	AppearanceWGCtrl.Instance:OnGetNewAppearance(protocol)
end

function SupremeFieldsWGCtrl:OpenFieldsBattleView()
	if not self.fields_battle_view:IsOpen() then
		self.fields_battle_view:Open()
	else
		self.fields_battle_view:Flush()
	end
end

function SupremeFieldsWGCtrl:OpenFieldsActivityView()
	if not self.fields_activity_view:IsOpen() then
		self.fields_activity_view:Open()
	else
		self.fields_activity_view:Flush()
	end
end

function SupremeFieldsWGCtrl:OpenItemAlertTips(item_id, need_num, is_money, ok_fun, title_str)
	self.fields_dialog_view:Open()
	self.fields_dialog_view:Flush(0, "item_tip_info", {need_num = need_num, item_id = item_id, is_money = is_money})
	self.fields_dialog_view:SetOkFunc(ok_fun)
	self.fields_dialog_view:SetTextViewName(title_str)
end

function SupremeFieldsWGCtrl:OpenReplaceAlertTips(left_data, right_data, ok_fun)
	self.fields_dialog_view:Open()
	self.fields_dialog_view:Flush(0, "tip_info", {left_data = left_data, right_data = right_data})
	self.fields_dialog_view:SetOkFunc(ok_fun)
end

function SupremeFieldsWGCtrl:OpenFieldsWuxingView(type_index)
	self.fields_wuxing_view:Flush(0, "all", {type_index = type_index})
	if not self.fields_wuxing_view:IsOpen() then
		self.fields_wuxing_view:Open()
	end
end

function SupremeFieldsWGCtrl:OpenFieldsWuxingAttrTips()
	if not self.fields_wuxing_attr_tips:IsOpen() then
		self.fields_wuxing_attr_tips:Open()
	else
		self.fields_wuxing_attr_tips:Flush()
	end
end

--自动上阵操作
function SupremeFieldsWGCtrl:AutoBattle()
	local data = self.data:GetAutoBattleSkillList()
end


-- 法阵技能
function SupremeFieldsWGCtrl:OnFootLightPlaySkillEff(protocol)
	-- print_error("---法阵技能----", protocol)
	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if not obj or obj:IsDeleted() then
		return
	end

	if obj.PlaySpecialEffect ~= nil then
		local skill_type = self.data:GetFootLightTypeBySkillID(protocol.skill_id)
		-- print_error("----skill_type---", skill_type)
		if skill_type then
			local bundle = "effects2/prefab/footlight_prefab"
			local asset = string.format("FootLight_%d_skill", skill_type)
			-- print_error("----bundle asset---", bundle, asset)
			obj:PlaySpecialEffect(bundle, asset, nil, AttachPoint.HurtRoot, 0, true, 5, 1)
		end
	end

	-- 延迟造成伤害
	if obj:IsMainRole() then
		local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(protocol.skill_id)
		if skill_cfg then
			if skill_cfg.delay > 0 then
				GlobalTimerQuest:AddTimesTimer(function ()
					self:SendFootLightSkillAtk(protocol.inc_id)
				end, skill_cfg.delay / 1000, 1)
			else
				self:SendFootLightSkillAtk(protocol.inc_id)
			end
		end
	end
end

function SupremeFieldsWGCtrl:SendFootLightSkillAtk(inc_id)
	-- print_error("---延迟造成伤害----", inc_id)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFootLightSkillAttack)
	protocol.inc_id = inc_id or 0
	protocol:EncodeAndSend()
end