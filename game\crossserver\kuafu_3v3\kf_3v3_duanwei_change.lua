KF3V3DuanWeiChangeiew = KF3V3DuanWeiChangeiew or BaseClass(SafeBaseView)
function KF3V3DuanWeiChangeiew:__init()
    self.view_layer = UiLayer.PopTop
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_duanwei_change")
end

function KF3V3DuanWeiChangeiew:ReleaseCallBack()
    if self.auto_close_timer then
        GlobalTimerQuest:CancelQuest(self.auto_close_timer)
        self.auto_close_timer = nil
    end
end

function KF3V3DuanWeiChangeiew:LoadCallBack()
    if self.auto_close_timer then
        GlobalTimerQuest:CancelQuest(self.auto_close_timer)
        self.auto_close_timer = nil
    end
    self.auto_close_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(function()
        self:Close()
    end), 5) --5秒自动关闭
end

-- data_type = 段位提升、段位下降、当前段位
--
function KF3V3DuanWeiChangeiew:SetData(data)
    self.data = data
end

function KF3V3DuanWeiChangeiew:ShowIndexCallBack()
    self:Flush()
end

function KF3V3DuanWeiChangeiew:OnFlush()
    if self.data.data_type == EnumDuanweiChangeType.Cur then
        self.node_list["effect"]:SetActive(true)
        self.node_list.title.text.text = Language.KuafuPVP.DuanWei[1]
        --self:CurOnFlush()
        self:SameOnFlush()
        self:EffectTiSheng()
    elseif self.data.data_type == EnumDuanweiChangeType.Up then
        self.node_list["effect"]:SetActive(true)
        self.node_list.title.text.text = Language.KuafuPVP.DuanWei[2]
        --self:UpOnFlush()
        self:SameOnFlush()
        self:EffectTiSheng()
    elseif self.data.data_type == EnumDuanweiChangeType.Down then
        self.node_list["effect"]:SetActive(false)
        self.node_list.title.text.text = Language.KuafuPVP.DuanWei[3]
        --self:DownOnFlush()
        self:SameOnFlush()
    end
end

--[[function KF3V3DuanWeiChangeiew:UpOnFlush()
    self.node_list.title.text.text = Language.KuafuPVP.DuanWei[2]
    local zhandui_new_duanwei_cfg = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.new_score)
    if zhandui_new_duanwei_cfg == nil then return end
    local star_res_list = GetStarImgResByStar(zhandui_new_duanwei_cfg.star)
    for i = 1, 5 do
        self.node_list["star" .. i]:SetActive(true)
        self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
    end
    self.node_list.duanwei_name.text.text = zhandui_new_duanwei_cfg.tier_name
    self.node_list.duanwei_icon.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz".. zhandui_new_duanwei_cfg.grade))
    self.node_list.duanwei_icon.image:SetNativeSize()
    self:EffectTiSheng()
end

function KF3V3DuanWeiChangeiew:DownOnFlush()
    self.node_list.title.text.text = Language.KuafuPVP.DuanWei[3]
    local zhandui_new_duanwei_cfg = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.new_score)
    if zhandui_new_duanwei_cfg == nil then return end
    local star_res_list = GetStarImgResByStar(zhandui_new_duanwei_cfg.star)
    for i = 1, 5 do
        self.node_list["star" .. i]:SetActive(true)
        self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
    end
    self.node_list.duanwei_name.text.text = zhandui_new_duanwei_cfg.tier_name
    self.node_list.duanwei_icon.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz".. zhandui_new_duanwei_cfg.grade))
    self.node_list.duanwei_icon.image:SetNativeSize()
end

function KF3V3DuanWeiChangeiew:CurOnFlush()
    self.node_list.title.text.text = Language.KuafuPVP.DuanWei[1]
    local zhandui_new_duanwei_cfg = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.new_score)
    if zhandui_new_duanwei_cfg == nil then return end
    local star_res_list = GetStarImgResByStar(zhandui_new_duanwei_cfg.star)
    for i = 1, 5 do
        self.node_list["star" .. i]:SetActive(true)
        self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
    end
    self.node_list.duanwei_name.text.text = zhandui_new_duanwei_cfg.tier_name
    self.node_list.duanwei_icon.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz".. zhandui_new_duanwei_cfg.grade))
    self.node_list.duanwei_icon.image:SetNativeSize()
    self:EffectTiSheng()
end
]]

function KF3V3DuanWeiChangeiew:SameOnFlush()
    local zhandui_new_duanwei_cfg = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.new_score)
    if zhandui_new_duanwei_cfg == nil then return end
    local star_res_list = GetStarImgResByStar(zhandui_new_duanwei_cfg.star)
    for i = 1, 5 do
        self.node_list["star" .. i]:SetActive(true)
        self.node_list["star" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
    end
    self.node_list.duanwei_name.text.text = zhandui_new_duanwei_cfg.tier_name
    self.node_list.duanwei_icon.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. zhandui_new_duanwei_cfg.grade))
    self.node_list.duanwei_icon.image:SetNativeSize()
end

function KF3V3DuanWeiChangeiew:EffectTiSheng()
    -- local bundle, asset = ResPath.GetEffectUi("UI_duanweitisheng")
    -- self.node_list["effect"]:ChangeAsset(bundle, asset)
end
