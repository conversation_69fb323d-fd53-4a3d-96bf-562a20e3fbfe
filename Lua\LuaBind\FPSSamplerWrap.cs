﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FPSSamplerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(FPSSampler), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("__eq", op_Equality);
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("SamplePeriod", get_SamplePeriod, set_SamplePeriod);
		<PERSON><PERSON>("ThresholdDeltaTime", get_ThresholdDeltaTime, set_ThresholdDeltaTime);
		<PERSON><PERSON>("FPSEvent", get_FPSEvent, set_FPSEvent);
		L<PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SamplePeriod(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FPSSampler obj = (FPSSampler)o;
			float ret = obj.SamplePeriod;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SamplePeriod on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ThresholdDeltaTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FPSSampler obj = (FPSSampler)o;
			float ret = obj.ThresholdDeltaTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ThresholdDeltaTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FPSEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<int>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SamplePeriod(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FPSSampler obj = (FPSSampler)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SamplePeriod = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SamplePeriod on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ThresholdDeltaTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FPSSampler obj = (FPSSampler)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ThresholdDeltaTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ThresholdDeltaTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_FPSEvent(IntPtr L)
	{
		try
		{
			FPSSampler obj = (FPSSampler)ToLua.CheckObject(L, 1, typeof(FPSSampler));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'FPSSampler.FPSEvent' can only appear on the left hand side of += or -= when used outside of the type 'FPSSampler'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<int> ev = (System.Action<int>)arg0.func;
				obj.FPSEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<int> ev = (System.Action<int>)arg0.func;
				obj.FPSEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

