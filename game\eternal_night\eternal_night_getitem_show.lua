EternalNightGetItemShow = EternalNightGetItemShow or BaseClass(SafeBaseView)

function EternalNightGetItemShow:__init()
	self.view_layer = UiLayer.Pop
	self.view_name = "EternalNightGetItemShow"
	self:AddViewResource(0, "uis/view/tips/getitem_prefab", "GetItemTip")
	self.item_list = {}
	self.team_list = {}
end

function EternalNightGetItemShow:__delete()
end

function EternalNightGetItemShow:LoadCallBack()
end

function EternalNightGetItemShow:ReleaseCallBack()
	self.team_list = {}
	for i,v in ipairs(self.item_list) do
		v:DeleteMe()
	end
	self.item_list = {}
end

function EternalNightGetItemShow:ShowIndexCallBack()
	if not self.show_num_tips then
		self.show_num_tips = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RunShow, self), 0.5)
	end
end

function EternalNightGetItemShow:CloseCallBack()
	if self.show_num_tips then
		GlobalTimerQuest:CancelQuest(self.show_num_tips)
		self.show_num_tips = nil
	end
	EternalNightWGData.Instance:SetNewSelfInfo()
	self.team_list = {}
end

function EternalNightGetItemShow:RunShow()
	if IsNil(self:GetRootNode()) then
		if self.show_num_tips then
			GlobalTimerQuest:CancelQuest(self.show_num_tips)
			self.show_num_tips = nil
		end
		return
	end

	if #self.team_list <= 0 then
		local has_play = false
		for k,v in pairs(self.item_list) do
			if v.playing_ani then
				has_play = true
				break
			end
		end
		if not has_play then
			if self.show_num_tips then
				GlobalTimerQuest:CancelQuest(self.show_num_tips)
				self.show_num_tips = nil
			end
			self:Close()
		end
	else
		local item
		for i,v in ipairs(self.item_list) do
			if not v.playing_ani then
				item = v
			end
		end

		if not item then
			item = EternalNightGetItemIcon.New()
			item:SetParntNode(self.node_list.root_obj.rect)
			item:LoadAsset("uis/view/tips/getitem_prefab", 'ItemImage', self.node_list.root_obj.transform)
			table.insert(self.item_list, item)
		end

		local item_data = table.remove(self.team_list, 1)
		item:SetData(item_data)
	end
end

function EternalNightGetItemShow:Show(item_data)
	if not self:IsOpen() then
		self:Open()
	end
	table.insert(self.team_list, item_data)
end


EternalNightGetItemIcon = EternalNightGetItemIcon or BaseClass(BaseRender)
function EternalNightGetItemIcon:__init()
	self.playing_ani = false
end

function EternalNightGetItemIcon:__delete()
	self.playing_ani = nil
	self.item_id = nil
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	self.parent_node_rect = nil
end

function EternalNightGetItemIcon:ResetCanvasGroup()
	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	if self.canvas_group then
		self.canvas_group.alpha = 1
		self.canvas_group.interactable = true
		self.canvas_group.blocksRaycasts = true
	end

	if self.node_list and self.node_list.view then
		self.node_list.view:SetActive(false)
	end
	self.playing_ani = false
end

function EternalNightGetItemIcon:LoadCallBack()
	self.canvas_group = self.node_list.view.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
	self.item_cell = ItemCell.New(self.node_list.view)
	self:SetData(self.item_data)
end

function EternalNightGetItemIcon:SetData(item_data)
	self.item_data = item_data
	if not item_data or not self.node_list or not self.node_list.view then
		return
	end

	self.pos = {555.7,61.4}
	local btn_node = MainuiWGCtrl.Instance:GetBtnRoleBagView()
	if Scene.Instance:GetSceneType() == SceneType.ETERNAL_NIGHT or Scene.Instance:GetSceneType() == SceneType.ETERNAL_NIGHT_FINAL then
		btn_node = EternalNightWGCtrl.Instance:GetEquipBtnNode(item_data.item_id)
	end
	if btn_node then
	-- --获取指引按钮的屏幕坐标
		local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
		local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera, btn_node.rect.position)
		local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
		self.pos = {local_bullet_start_pos_tbl.x,local_bullet_start_pos_tbl.y}
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	if item_cfg and item_cfg.icon_id and item_cfg.icon_id > 0 then
		self.node_list.view:SetActive(true)
		self.item_cell:SetActive(true)
		self.item_cell:SetFlushCallBack(function ()
			self.item_cell:SetRightBottomText('')
			self.item_cell:SetRightBottomTextVisible(false)
		end)
		self.item_cell:SetData(item_data)
		self.node_list.view.rect.anchoredPosition = Vector2(150, -200)
		self.node_list.view.rect.localScale = Vector3(0.2, 0.2, 0.2)
		self.playing_ani = true
		self:PlayAni()
	else
		self:ResetCanvasGroup()
	end
end

function EternalNightGetItemIcon:PlayAni()
	self.canvas_group.alpha = 1
	self.canvas_group.interactable = false
	self.canvas_group.blocksRaycasts = false

	local move_tween_1 = self.node_list.view.rect:DOAnchorPos(Vector2(150, -50), 0.6)
	local move_tween_2 = self.node_list.view.rect:DOAnchorPos(Vector2(self.pos[1], self.pos[2]), 1.3)

	local scale_tween_1 = self.node_list.view.rect:DOScale(Vector3(0.8, 0.8, 0.8), 0.5)
	local scale_tween_2 = self.node_list.view.rect:DOScale(Vector3(0, 0, 0), 0.3)

	if self.sequence then
		self.sequence:Kill()
		self.sequence = nil
	end

	self.sequence = DG.Tweening.DOTween.Sequence()

	self.sequence:Append(move_tween_1)
	self.sequence:Join(scale_tween_1)

	self.sequence:AppendInterval(0.2)
	self.sequence:AppendCallback(function ()
		self.canvas_group:DoAlpha(1, 0.5, 0.5)
	end)

	self.sequence:Append(move_tween_2)
	self.sequence:Append(scale_tween_2)

	self.sequence:OnComplete(function ()
		self.canvas_group.interactable = true
		self.canvas_group.blocksRaycasts = true
		self.node_list.view:SetActive(false)
		self.playing_ani = false
		if self.item_data and self.item_data.anim_complete_fun then
			self.item_data.anim_complete_fun()
		end
	end)
end

function EternalNightGetItemIcon:SetParntNode(node)
	self.parent_node_rect = node
end
