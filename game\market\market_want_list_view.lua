function MarketView:WantListLoadCallBack()
	self.want_list_big_type_list = AsyncListView.New(MarketBigTypeItem, self.node_list["want_list_type_list"]) 			-- 求购大类型列表
	self.want_list_big_type_list:SetSelectCallBack(BindTool.Bind1(self.OnClickWantListBigType, self))
	self.want_list_big_type_list:SetDataList(MarketWGData.Instance:GetBigTypeCfgAndMyWant())
	self.want_list_big_id = 1 																							-- 当前选中的大类型
	self.want_list_sub_type = -1 																						-- 当前选中的子类型

	self.want_list = AsyncListView.New(MarketWantListItem, self.node_list["want_list_item_list"]) 						-- 求购列表

	self.want_list_sub_type_item_list = {} 																				-- 用于存储子类型gameobject

	-- 品阶筛选下拉框
	self.node_list["want_list_order_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnWantListOrderDropdownChange, self))
	self.order_dropdown_select = 0
	self.node_list["want_list_order_dropdown"].dropdown.value = self.order_dropdown_select

	-- 品质筛选下拉框
	self.node_list["want_list_color_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnWantListColorDropdownChange, self))
	self.color_dropdown_select = 0
	self.node_list["want_list_color_dropdown"].dropdown.value = self.color_dropdown_select

	-- 搜索按钮
	XUI.AddClickEventListener(self.node_list["want_list_search_btn"], BindTool.Bind1(self.OnClickWantListSearch, self))

	-- 点击按总价排序
	XUI.AddClickEventListener(self.node_list["want_list_total_price_btn"], BindTool.Bind(self.OnClickWantListTotalPriceSort, self))
	self.want_list_total_sort_param = 0
	self.is_sort = false
end

function MarketView:WantListReleaseCallBack()	
	if self.want_list_big_type_list ~= nil then
		self.want_list_big_type_list:DeleteMe()
		self.want_list_big_type_list = nil
	end

	if self.want_list_sub_type_item_list then
		for i, v in ipairs(self.want_list_sub_type_item_list) do
			v:DeleteMe()
			self.want_list_sub_type_item_list = {}
		end
	end

	if self.want_list then
		self.want_list:DeleteMe()
		self.want_list = nil
	end
end

function MarketView:WantListShowIndexCallBack()

end

function MarketView:WantListOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushWantListAllPart()
		elseif k == "Part" then
			if v.flush_want_list then
				self:FlushWantList()
			end
		end
	end
end

function MarketView:FlushWantListAllPart()
	self:FlushWantListChildPanelActive()
	self:FlushWantListSubTypeGrid()
	self:FlushWantList()
	self:FlushWantListOrderDrowDown()
	self:FlushWantListColorDrowDown()
end

-- 子面板显隐
function MarketView:FlushWantListChildPanelActive()
	self.node_list["want_list_subtype_panel"]:SetActive(self.want_list_sub_type == -1)
	self.node_list["want_list_item_panel"]:SetActive(self.want_list_sub_type ~= -1 or self.want_list_big_id == MarketWGData.MyWantBigId or self.want_list_big_id == MarketWGData.AllWantBigId)
end

-- 刷新子类型grid
function MarketView:FlushWantListSubTypeGrid()
	if self.sub_type_item_prefab == nil then
		self:LoadSubTypeItemPrefab(BindTool.Bind(self.FlushWantListSubTypeGrid, self))
		return
	end

	for i, v in ipairs(self.want_list_sub_type_item_list) do
		v:SetActive(false)	
	end

	if self.want_list_big_id == MarketWGData.MyWantBigId or self.want_list_big_id == MarketWGData.AllWantBigId then
		return
	end

	local sub_type_cfg = {}
	sub_type_cfg = MarketWGData.Instance:GetSubTypeCfg(self.want_list_big_id)

	local index = 0
	for sub_type, cfg in pairs(sub_type_cfg) do
		index = index + 1
		if not self.want_list_sub_type_item_list[index] then
			local go = ResMgr:Instantiate(self.sub_type_item_prefab)
			go:SetActive(true)
			go.transform:SetParent(self.node_list["want_list_subtype_grid"].transform, false)
			self.want_list_sub_type_item_list[index] = MarketWantListSubTypeItem.New(go)
			self.want_list_sub_type_item_list[index]:SetClickCallBack(BindTool.Bind(self.OnClickWantListSubType, self))
		end
		self.want_list_sub_type_item_list[index]:SetData(cfg)
		self.want_list_sub_type_item_list[index]:SetActive(true)
	end
end

-- 根据大类型和子类型刷新求购列表
function MarketView:FlushWantList()
	if self.want_list_sub_type < 0 and self.want_list_big_id ~= MarketWGData.MyWantBigId and self.want_list_big_id ~= MarketWGData.AllWantBigId then
		return
	end
	local want_list_info = {}
	local bundel, asset
	local bundel1, asset1
	-- 判断是不是我的求购大类
	if self.want_list_big_id == MarketWGData.MyWantBigId then
		want_list_info = self:CalWantList(MarketWGData.Instance:GetMyWantListInfo())
	elseif self.want_list_big_id == MarketWGData.AllWantBigId then
		want_list_info = self:CalWantList(MarketWGData.Instance:GetAllWantListInfo())
	else
		want_list_info = self:CalWantList(MarketWGData.Instance:GetWantListInfo(self.want_list_big_id, self.want_list_sub_type))
	end
	self.want_list:SetDataList(want_list_info)

	if self.is_sort then
		if self.want_list_total_sort_param > 0 then
			bundel, asset = ResPath.GetF2CommonImages("t_paixu_xuanzhon")
			bundel1, asset1 = ResPath.GetF2CommonImages("t_paixu_moren")
		else
			bundel, asset = ResPath.GetF2CommonImages("t_paixu_moren")
			bundel1, asset1 = ResPath.GetF2CommonImages("t_paixu_xuanzhon")
		end
		self.is_sort = false
	end
	
	self.node_list["unit_up1"].image:LoadSprite(bundel, asset, function ()
		self.node_list["unit_up1"].image:SetNativeSize()
	end)

	self.node_list["unit_down1"].image:LoadSprite(bundel1, asset1, function ()
		self.node_list["unit_down1"].image:SetNativeSize()
	end)
	self.node_list["want_list_empty_tips"]:SetActive(IsEmptyTable(want_list_info))
end

-- 筛选求购信息
function MarketView:CalWantList(want_list)
	local result = {}
	for i,v in ipairs(want_list) do
		if self:CanShowWant(v) then
			v.sort_key = self.want_list_total_sort_param * v.total_price
			table.insert(result, v)
		end
	end
	table.sort(result, SortTools.KeyUpperSorter("sort_key"))
	return result
end

function MarketView:CanShowWant(want_info)
	local item_cfg = ItemWGData.Instance:GetItemConfig(want_info.item_id)
	-- 筛选阶数
	if self.want_list_order_dropdown_select ~= 0 then
		if item_cfg.order ~= self.want_list_order_list[self.want_list_order_dropdown_select] then
			return false
		end
	end

	-- 筛选品质
	if self.want_list_color_dropdown_select ~= 0 then
		if item_cfg.color ~= self.want_list_color_list[self.want_list_color_dropdown_select] then
			return false
		end
	end

	-- 筛选搜索文字内容
	if self.want_list_search_name and self.want_list_search_name ~= "" then
		if not string.find(item_cfg.name, self.want_list_search_name) then
			return false
		end
	end

	return true
end

-- 刷新阶数筛选下拉框
function MarketView:FlushWantListOrderDrowDown()
	if self.want_list_sub_type < 0 and self.want_list_big_id ~= MarketWGData.AllWantBigId then
		self.node_list["want_list_order_dropdown"]:SetActive(false)
		return
	end
	
	if IsEmptyTable(self.want_list_order_list) then
		self.order_dropdown_select = 0
		self.node_list["want_list_order_dropdown"]:SetActive(false)
		return
	end
	local list_string = System.Collections.Generic.List_string.New()
	list_string:Add(Language.Market.NameList1[0])
	for i, order in ipairs(self.want_list_order_list) do
		list_string:Add(Language.Market.NameList1[order])
	end
	self.node_list["want_list_order_dropdown"].dropdown:ClearOptions()
	self.node_list["want_list_order_dropdown"].dropdown:AddOptions(list_string)
	self.node_list["want_list_order_dropdown"].dropdown.value = self.want_list_order_dropdown_select
	self.node_list["want_list_order_dropdown"]:SetActive(true)
end

-- 刷新品质筛选下拉框
function MarketView:FlushWantListColorDrowDown()
	if self.want_list_sub_type < 0 and self.want_list_big_id ~= MarketWGData.AllWantBigId then
		self.node_list["want_list_color_dropdown"]:SetActive(false)
		return
	end
	if IsEmptyTable(self.want_list_color_list) then
		self.color_dropdown_select = 0
		self.node_list["want_list_color_dropdown"]:SetActive(false)
		return
	end
	local list_string = System.Collections.Generic.List_string.New()
	list_string:Add(Language.Market.NameList2[0])
	for i, color in ipairs(self.want_list_color_list) do
		list_string:Add(Language.Market.NameList2[color])
	end
	self.node_list["want_list_color_dropdown"].dropdown:ClearOptions()
	self.node_list["want_list_color_dropdown"].dropdown:AddOptions(list_string)
	self.node_list["want_list_color_dropdown"].dropdown.value = self.color_dropdown_select
	self.node_list["want_list_color_dropdown"]:SetActive(true)
end

-- 点击大类型
function MarketView:OnClickWantListBigType(big_type_item, select_index)
	self.want_list_big_id = big_type_item.data.big_id
	-- 清空子类型
	self.want_list_sub_type = -1

	-- 清空筛选下拉框选择
	self.want_list_order_list = MarketWGData.Instance:GetOrderList(self.want_list_big_id) -- 点击全部求购的时候没有小类型，直接在这里取order_list
	self.want_list_order_dropdown_select = 0
	self.want_list_color_list = MarketWGData.Instance:GetColorList(self.want_list_big_id) -- 点击全部求购的时候没有小类型，直接在这里取color_list
	self.want_list_color_dropdown_select = 0

	self.want_list_search_name = ""

	self:FlushWantListAllPart()
end

-- 点击子类型
function MarketView:OnClickWantListSubType(sub_type_cfg)
	self.want_list_total_sort_param = 0
	self.want_list_sub_type = sub_type_cfg.small_id
	MarketWGCtrl.Instance:SendCSRoleAuctionItem(self.want_list_big_id, self.want_list_sub_type)
	self.want_list_order_list = MarketWGData.Instance:GetOrderList(self.want_list_big_id, self.want_list_sub_type)
	self.want_list_color_list = MarketWGData.Instance:GetColorList(self.want_list_big_id, self.want_list_sub_type)
	self:FlushWantListChildPanelActive()
	self:FlushWantListOrderDrowDown()
	self:FlushWantListColorDrowDown()
	self:FlushWantList()
end

-- 阶数筛选下拉框更变
function MarketView:OnWantListOrderDropdownChange(index)
	self.want_list_order_dropdown_select = index
	self:FlushWantList()
end

-- 品质筛选下拉框更变
function MarketView:OnWantListColorDropdownChange(index)
	self.want_list_color_dropdown_select = index
	self:FlushWantList()
end

-- 点击搜索
function MarketView:OnClickWantListSearch()
	self.want_list_search_name = self.node_list["want_list_search_input"].input_field.text
	self:FlushWantList()
end

-- 点击按总价排序
function MarketView:OnClickWantListTotalPriceSort()
	self.is_sort = true
	if self.want_list_total_sort_param == 0 then
		self.want_list_total_sort_param = 1
	else
		self.want_list_total_sort_param = self.want_list_total_sort_param * -1
	end
	self:FlushWantList()
end

---------------------市场求购面板求购清单列表item------------------
MarketWantListItem = MarketWantListItem or BaseClass(BaseRender)
function MarketWantListItem:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	XUI.AddClickEventListener(self.node_list["sell_btn"], BindTool.Bind1(self.OnClickResponse, self))
	XUI.AddClickEventListener(self.node_list["cancel_btn"], BindTool.Bind1(self.OnClickCancel, self))
end

function MarketWantListItem:__delete()
	self.item_cell:DeleteMe()
	self.item_cell = nil
end

function MarketWantListItem:OnFlush()
	if self.data then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		self.item_cell:SetData({item_id = self.data.item_id, star_level = self.data.equip_star or 0, num = self.data.item_num})
		
		-- 物品名称
		self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id, nil, true)

		-- 评分
		local pingfen = TipWGData.Instance:GetCfgBasePingFenByAttrList(item_cfg)
		self.node_list["item_desc"]:SetActive(pingfen > 0)
		self.node_list["item_desc"].text.text = string.format(Language.Common.PingFen, pingfen)

		-- 阶数
		self.node_list["item_level"].text.text = Language.Market.NameList1[item_cfg.order]
		
		-- 总价
		self.node_list["total_price"].text.text = self.data.total_price


		local role_id = RoleWGData.Instance:GetOriginUid()
		local my_want = role_id == self.data.wanted_uid
		self.node_list["sell_btn"]:SetActive(not my_want)
		self.node_list["cancel_btn"]:SetActive(my_want)

		-- 自己是否有他人求购的装备
		local has_item = MarketWGData.Instance:SearchItemInAllBag(self.data.item_id, self.data.item_num, self.data.equip_star)
		XUI.SetGraphicGrey(self.node_list["sell_btn"], not has_item)
	end
end

-- 响应求购，卖出自己的装备
function MarketWantListItem:OnClickResponse()
	local has_item = MarketWGData.Instance:SearchItemInAllBag(self.data.item_id, self.data.item_num, self.data.equip_star)
	if has_item == nil then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.NotEnoughToResponse)
		return
	end

	local color = ItemWGData.Instance:GetItemColor(has_item.item_id)
	local name = ItemWGData.Instance:GetItemName(has_item.item_id)
	local item_name = HtmlTool.GetHtml(string.format(Language.Market.Brackets, name), color)
	TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Market.ConfirmResponseWant, item_name, self.data.item_num), function()
		MarketWGCtrl.Instance:SendResponseWant(self.data.wanted_index, has_item.knapsack_type, has_item.index)
	end)
end

-- 撤销自己的求购
function MarketWantListItem:OnClickCancel()
	TipWGCtrl.Instance:OpenAlertTips(Language.Market.ConfirmRemoveWant, function()
		MarketWGCtrl.Instance:SendRemoveWant(self.data.wanted_index)
	end)
end