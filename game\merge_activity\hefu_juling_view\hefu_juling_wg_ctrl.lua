require("game/merge_activity/hefu_juling_view/hefu_juling_wg_data")
require("game/merge_activity/hefu_juling_view/he_jungling_friend_view")


HeFuJuLingWGCtrl = HeFuJuLingWGCtrl or BaseClass(BaseWGCtrl)

function HeFuJuLingWGCtrl:__init()
	if HeFuJuLingWGCtrl.Instance then
		ErrorLog("[HeFuJuLingWGCtrl] Attemp to create a singleton twice !")
	end
	HeFuJuLingWGCtrl.Instance = self

	self.hefu_juling_data = HeFuJuLingWGData.New()

	--上线要自己请求一次好友跟仙盟成员的花盆信息
	self.is_send_friend_list = false

	self:RegisterAllProtocols()
    self.juling_friend_view = HefuJulingFriendView.New()
	self:BindGlobalEvent(OtherEventType.PASS_DAY2,BindTool.Bind(self.DoDayPass, self))
end

function HeFuJuLingWGCtrl:__delete()
	HeFuJuLingWGCtrl.Instance = nil

	if self.hefu_juling_data then
		self.hefu_juling_data:DeleteMe()
		self.hefu_juling_data = nil
	end
    if self.juling_friend_view then
		self.juling_friend_view:DeleteMe()
		self.juling_friend_view = nil
	end

end

function HeFuJuLingWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCSAJuLingZhuZhenInfo,'OnSCCSAJuLingZhuZhenInfo')-- 10401 个人信息下发
	self:RegisterProtocol(SCCSAJuLingZhuZhenHelpFriendInfo,'OnSCCSAJuLingZhuZhenHelpFriendInfo')-- 10402 我帮浇过的人
	self:RegisterProtocol(SCCSAJuLingZhuZhenHelpMeInfo,'OnSCCSAJuLingZhuZhenHelpMeInfo')-- 10404 下发一些人的帮浇次数
	self:RegisterProtocol(SCCSAJuLingZhuZhenInviteHelpOtherAck,'OnSCCSAJuLingZhuZhenInviteHelpOtherAck')-- 10405 通知被某人邀请注灵
	self:RegisterProtocol(SCCSAJuLingZhuZhenZhuLingLog,'OnSCCSAJuLingZhuZhenZhuLingLog')-- 10406 下发注灵日志
	self:RegisterProtocol(SCCSAJuLingZhuZhenOtherRoleLingBao,'OnSCCSAJuLingZhuZhenOtherRoleLingBao')-- 10407 其他玩家盆栽信息
	self:RegisterProtocol(SCCSAJuLingZhuZhenSingleRoleInfo,'OnSCCSAJuLingZhuZhenSingleRoleInfo')-- 10408 其他玩家盆栽信息
	self:RegisterProtocol(SCCSAJuLingZhuZhenFetchReward, 'OnSCCSAJuLingZhuZhenFetchReward')-- 10409 成熟奖励列表

	self:RegisterProtocol(CSCSAJuLingZhuZhenInfo)--10400 聚灵助阵通用请求协议
	self:RegisterProtocol(CSCSAJuLingZhuZhenHelpFriendReqInfo)-- 10403 查询这些人的帮浇次数

end

-- 10401  合服活动-聚灵助阵信息
function HeFuJuLingWGCtrl:OnSCCSAJuLingZhuZhenInfo(protocol)
	self.hefu_juling_data:SetJuLingZhuZhenInfo(protocol)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)
	RemindManager.Instance:Fire(RemindName.HeFu_JuLingZhuZhen)
    self.juling_friend_view:Flush()
	if not self.is_send_friend_list then
		self:SendAllFirendAndMemberLingBaoInfo()
		self.is_send_friend_list = true
	end
end

-- 10402 合服活动-聚灵助阵帮助他人次数信息
function HeFuJuLingWGCtrl:OnSCCSAJuLingZhuZhenHelpFriendInfo(protocol)
	self.hefu_juling_data:SetJuLingZhuZhenHelpFriendInfo(protocol)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)
    RemindManager.Instance:Fire(RemindName.HeFu_JuLingZhuZhen)
    self.juling_friend_view:Flush()
end

-- 10404 合服活动-聚灵助阵他人帮助我次数信息
function HeFuJuLingWGCtrl:OnSCCSAJuLingZhuZhenHelpMeInfo(protocol)
	self.hefu_juling_data:SetJuLingZhuZhenHelpMeInfo(protocol)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)
    RemindManager.Instance:Fire(RemindName.HeFu_JuLingZhuZhen)
    self.juling_friend_view:Flush()
end

-- 10405 合服活动-聚灵助阵别人邀请帮助他提示
function HeFuJuLingWGCtrl:OnSCCSAJuLingZhuZhenInviteHelpOtherAck(protocol)
	--print_error('D3', '-- 10405 通知被某人邀请注灵', protocol)
	self.hefu_juling_data:SetJuLingZhuZhenInviteHelpOtherAck(protocol)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)
    RemindManager.Instance:Fire(RemindName.HeFu_JuLingZhuZhen)
    self.juling_friend_view:Flush()
end


function HeFuJuLingWGCtrl:FlushTextInvite()
	if self.juling_friend_view:IsOpen() and self.juling_friend_view:IsLoaded() then
	    self.juling_friend_view:FlushTextInvite()
    end
end

-- 10406 合服活动-聚灵助阵注灵日志
function HeFuJuLingWGCtrl:OnSCCSAJuLingZhuZhenZhuLingLog(protocol)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	if role_id == protocol.uid then
		self.hefu_juling_data:SetJuLingZhuZhenZhuLingLog(protocol)

		for i=1,6 do
			if protocol.record_list[i] and protocol.record_list[i].uid > 0 then
				BrowseWGCtrl.Instance:SendQueryRoleInfoReq(protocol.record_list[i].uid, BindTool.Bind(self.QueryRoleInfoCallBack, self))
			end
		end
	else
		self.hefu_juling_data:SetJuLingZhuZhenOtherZhuLingLog(protocol)

		for i=1,6 do
			if protocol.record_list[i] and protocol.record_list[i].uid > 0 then
				BrowseWGCtrl.Instance:SendQueryRoleInfoReq(protocol.record_list[i].uid, BindTool.Bind(self.QueryOtherRoleInfoCallBack, self))
			end
		end
	end
	RemindManager.Instance:Fire(RemindName.HeFu_JuLingZhuZhen)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)
end

function HeFuJuLingWGCtrl:QueryRoleInfoCallBack(info)
	self.hefu_juling_data:QueryRoleInfoCallBack(info)
end

function HeFuJuLingWGCtrl:QueryOtherRoleInfoCallBack(info)
	self.hefu_juling_data:QueryOtherRoleInfoCallBack(info)
end

-- 10407 合服活动-聚灵助阵其他玩家灵宝信息
function HeFuJuLingWGCtrl:OnSCCSAJuLingZhuZhenOtherRoleLingBao(protocol)
	self.hefu_juling_data:SetJuLingZhuZhenOtherRoleLingBao(protocol)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)
    RemindManager.Instance:Fire(RemindName.HeFu_JuLingZhuZhen)
end

-- 10408 合服活动-聚灵助阵单个其他玩家灵宝信息
function HeFuJuLingWGCtrl:OnSCCSAJuLingZhuZhenSingleRoleInfo(protocol)
	self.hefu_juling_data:SetOtherJuLingZhuZhenSingleRoleInfo(protocol)
	MergeActivityWGCtrl.Instance:FlushView(TabIndex.merge_activity_2127)
	RemindManager.Instance:Fire(RemindName.HeFu_JuLingZhuZhen)
end

-- 10400 聚灵助阵通用请求协议
function HeFuJuLingWGCtrl:CSCSAJuLingZhuZhenInfo(type, param_0, param_1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSAJuLingZhuZhenInfo)
	protocol.type = type or 0
	protocol.param_0 = param_0 or 0
	protocol.param_1 = param_1 or 0
	protocol:EncodeAndSend()
end

-- 10403 合服活动-聚灵助阵查询他人帮助我信息
function HeFuJuLingWGCtrl:CSCSAJuLingZhuZhenHelpFriendReqInfo(type, count, uid_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSAJuLingZhuZhenHelpFriendReqInfo)
	protocol.type = type or 0
	protocol.count = count or 0
	protocol.uid_list = uid_list or {}
	protocol:EncodeAndSend()
end

-- 10409 成熟奖励列表
function HeFuJuLingWGCtrl:OnSCCSAJuLingZhuZhenFetchReward(protocol)
	if not IsEmptyTable(protocol.reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, protocol.reward_list, false)
	end
end

--好友改变主动请求信息
function HeFuJuLingWGCtrl:FlushJulingActInfo()
    if MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN) then
        self.hefu_juling_data:UpdateJuLingFriendList()
        self:SendAllFirendAndMemberLingBaoInfo()
    end
end

--请求所有好友和仙盟成员花盆信息
function HeFuJuLingWGCtrl:SendAllFirendAndMemberLingBaoInfo()
    --print_error("请求所有好友和仙盟成员花盆信息")
	local uid_list = {}
	local count = 0
	local friend_list = SocietyWGData.Instance:GetFriendList2()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()

	if friend_list and not IsEmptyTable(friend_list) then
		for k,v in pairs(friend_list) do
			if v.user_id > 0 then
				count = count + 1
				table.insert(uid_list, v.user_id)
			end
		end
	end

	local member_list = GuildDataConst.GUILD_MEMBER_LIST

	if member_list and member_list.list and not IsEmptyTable(member_list.list) then
		for k,v in pairs(member_list.list) do
			if not IsEmptyTable(v) and v.uid > 0 and v.uid ~= role_id then
				count = count + 1
				table.insert(uid_list, v.uid)
			end
		end
	end
	self:CSCSAJuLingZhuZhenHelpFriendReqInfo(MERGE_JULINGZHUZHEN_QUERY_TYPE.MERGE_JULINGZHUZHEN_QUERY_TYPE_INFO, count, uid_list)
end

function HeFuJuLingWGCtrl:DoDayPass()
	local status = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_JULINGZHUZHEN)

	if not status then
		return
	end

	--跳天重新请求种花信息，服务器说准点请求可能数据还没重置完所以延时5秒钟请求
	GlobalTimerQuest:AddDelayTimer(function ()
		self:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_INFO_REQ)
		self:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_OTHER_HELP_ME_INFO_REQ)
		self.hefu_juling_data:SetGardenFlag(0)
		HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_ZHULING_LOG_REQ)

	end, 5)
end


function HeFuJuLingWGCtrl:OpenFriendView()
    if self.juling_friend_view then
        if self.juling_friend_view:IsOpen() then
            self.juling_friend_view:PlayMoveInTween()
        else
            self.juling_friend_view:Open()
        end
	end
end

function HeFuJuLingWGCtrl:OnClickJuLingBackBtn()
    if self.juling_friend_view and self.juling_friend_view:IsOpen() and self.juling_friend_view:IsLoaded() then
		self.juling_friend_view:OnClickJuLingBackBtn()
	end
end

function HeFuJuLingWGCtrl:CloseFriendView()
    if self.juling_friend_view and self.juling_friend_view:IsOpen() then
		self.juling_friend_view:Close()
	end
end