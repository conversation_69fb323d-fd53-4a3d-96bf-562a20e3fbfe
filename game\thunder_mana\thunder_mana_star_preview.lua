ThunderManaStarPreview = ThunderManaStarPreview or BaseClass(SafeBaseView)
function ThunderManaStarPreview:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
    self.view_name = "ThunderManaStarPreview"
	local bundle_name = "uis/view/thunder_mana_ui_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(814, 580)})
    self:AddViewResource(0, bundle_name, "layout_thunder_mana_star_preview")
end

function ThunderManaStarPreview:SetShowDataAndOpen(data)
	if data then
        self.show_data = data
		self.part_select_index = data and data.select_part_index or 0
		self:Open()
	end
end

function ThunderManaStarPreview:LoadCallBack()
	--- 初始化三个位置
    self.node_list.title_view_name.text.text = Language.ThunderMana.ThunderTitleName[5]

	if not self.item_list then
		self.item_list = AsyncListView.New(ThunderManaStarPreviewRender, self.node_list.ph_item_list)	
	end
end

function ThunderManaStarPreview:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

	self.show_data = nil
end

function ThunderManaStarPreview:OnFlush(param_t)
	if not self.show_data then
		return
	end

	local show_list = ThunderManaWGData.Instance:GetThundePreviewTypeList(self.show_data.thunder_type, self.show_data.part)
	self.item_list:SetDataList(show_list)
	
	self.node_list.ph_item_list:SetActive(show_list and #show_list > 0)
	self.node_list.img_no_record:SetActive(show_list == nil or #show_list <= 0)

	local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(self.show_data.thunder_type, self.show_data.part)
	if part_info and part_info.item_id <= 0 then
		return
	end

	local equip_cfg = ThunderManaWGData.Instance:GetEquipItemStarCfg(part_info.item_id)
	if equip_cfg and equip_cfg.next_star_id > 0 then
		local percent = 0
		for k, v in pairs(show_list) do
			if v.next_star_id == equip_cfg.next_star_id and k > 3 then
				local cell_height = 114
				local cell_num = self.item_list:GetListViewNumbers()
				local total_length = cell_num * cell_height
				local view_show_height = self.node_list["ph_item_list"].rect.sizeDelta.y
				local scroll_size = total_length - view_show_height
				local jump_position = (k - 1) * cell_height
				percent = jump_position / scroll_size
			end
		end

		self.item_list:JumptToPrecent(percent)
	end
end


--=================================================================================
ThunderManaStarPreviewRender = ThunderManaStarPreviewRender or BaseClass(BaseRender)
function ThunderManaStarPreviewRender:LoadCallBack()
	self.cell_list = {}
	for i = 1, 4 do
		self.cell_list[i] = ItemCell.New(self.node_list["content"])
	end

	if not self.target_item then
		self.target_item = ItemCell.New(self.node_list["target_item"])
	end
end

function ThunderManaStarPreviewRender:__delete()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
    end

    if self.target_item then
        self.target_item:DeleteMe()
        self.target_item = nil
    end
end

function ThunderManaStarPreviewRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.equal:SetActive(self.data.star ~= 1)
	self.target_item:SetData({item_id = self.data.next_star_id})
	local equip_item_data = ThunderManaWGData.Instance:GetEquipItemStarCfg(self.data.next_star_id)
	self.node_list.cur_hl_img:SetActive(false)
	self.node_list.target_item_star_txt.text.text = ""
	if equip_item_data then
		self.node_list.target_item_star_txt.text.text = string.format(Language.Common.StarStr, CommonDataManager.GetDaXie(equip_item_data.star or 0))
		local part_info = ThunderManaWGData.Instance:GetEquipPartInfo(equip_item_data.seq, equip_item_data.part)
		self.node_list.cur_hl_img:SetActive(part_info and part_info.star == equip_item_data.star)
	else
		self.node_list.target_item_star_txt.text.text = ""
	end
	
	local cost_cfg = ThunderManaWGData.Instance:GetEquipComposeCost(self.data.item_id)
	for i = 1, 4 do
		self.cell_list[i]:SetActive(cost_cfg[i] ~= nil) 

		if cost_cfg[i] ~= nil then
			local star_up_data = cost_cfg[i]
			if star_up_data.special_item ~= nil then
				self.cell_list[i]:SetData({item_id = star_up_data.special_item, num = star_up_data.num})
			elseif star_up_data.star ~= nil then
				local virtual_cfg = ThunderManaWGData.Instance:GetVirualItemByStar(self.data.item_id, star_up_data.star)
                local virtual_id = virtual_cfg and virtual_cfg.virtual_item_id or 0
				self.cell_list[i]:SetData({item_id = virtual_id, num = star_up_data.num})
				self.cell_list[i]:SetThunderManaStarMessage(star_up_data.star)
			end
		end
	end
end
