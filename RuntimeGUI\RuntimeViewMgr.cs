﻿using Nirvana;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuntimeViewName
{
    public static string LOGIN = "登陆";
    public static string TOOL = "工具栏";
    public static string GM = "GM调试";
    public static string SCENE_EDIT = "场景编辑";
    public static string LUA_PROFILER = "luaProfiler";
}

public class RuntimeViewMgr : Nirvana.Singleton<RuntimeViewMgr>
{
    private Dictionary<string, RuntimeBaseView> viewDic = new Dictionary<string, RuntimeBaseView>();
    public void RegisterView(string viewName, RuntimeBaseView view)
    {
        viewDic.Add(viewName, view);
    }

    public RuntimeBaseView GetView(string viewName)
    {
        RuntimeBaseView view;
        if (viewDic.TryGetValue(viewName, out view))
        {
            return view;
        }

        return null;
    }

    public void OpenView(string viewName)
    {
        RuntimeBaseView view = this.GetView(viewName);
        if (null == view)
        {
            Debug.LogErrorFormat("找不到窗口 {0}", viewName);
            return;
        }

        this.CloseAll();
        view.Open();
    }

    public void CloseView(string viewName)
    {
        RuntimeBaseView view = this.GetView(viewName);
        if (null != view)
        {
            view.Close();
        }
    }

    public void CloseAll()
    {
        foreach (var item in viewDic)
        {
            item.Value.Close();
        }
    }

    public void OnGUI()
    {
        RuntimeGUIMgr.Instance.GetGUIBlock().HideRect();
        foreach (var kv in viewDic)
        {
            kv.Value.OnGUI();
        }
    }
    public void OnGameStop()
    {
        foreach (var item in viewDic)
        {
            item.Value.OnGameStop();
        }
    }

    public void OnApplicationQuit()
    {
        foreach (var item in viewDic)
        {
            item.Value.OnApplicationQuit();
        }
    }
}
