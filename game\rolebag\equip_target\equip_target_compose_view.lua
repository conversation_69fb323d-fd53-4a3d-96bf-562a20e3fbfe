EquipTargetComposeView = EquipTargetComposeView or BaseClass(SafeBaseView)

function EquipTargetComposeView:__init()
    --self.view_name = "EquipTargetTipiew"
    self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", { sizeDelta = Vector2(802, 544)})
    self:AddViewResource(0, "uis/view/rolebag_ui/equip_target_prefab", "layout_equiptarget_compose")

end

function EquipTargetComposeView:ReleaseCallBack()

    if self.ph_item_list then
        self.ph_item_list:DeleteMe()
        self.ph_item_list = nil
    end

end

function EquipTargetComposeView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.EquipTarget.ComposeTitle
    if not self.ph_item_list then
		self.ph_item_list = AsyncListView.New(EquipComposeListRender, self.node_list.ph_item_list)
	end
end


function EquipTargetComposeView:OnFlush()
    local compose_desc_cfg = EquipTargetWGData.Instance:GetComposeDescCfg()
    if self.ph_item_list then
        self.ph_item_list:SetDataList(compose_desc_cfg)
    end
end

------------------------------------装备列表------------------------
EquipComposeListRender = EquipComposeListRender or BaseClass(BaseRender)

function EquipComposeListRender:__init()
    self.target_item_cell = ItemCell.New(self.node_list.target_item)

    
    if not self.item_list then
		self.item_list = AsyncListView.New(EquipComposeItemRender, self.node_list.item_list)
	end
end

function EquipComposeListRender:__delete()
   if self.target_item_cell then
        self.target_item_cell:DeleteMe()
        self.target_item_cell = nil
    end

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

end

function EquipComposeListRender:LoadCallBack()
    
end

function EquipComposeListRender:OnFlush()
    local data = self:GetData()
    if not data then
        return
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(data.target_item_id)

    if not item_cfg then
        return
    end
    local str = ""
    if data.star_level == 5 then
        str = string.format(Language.EquipTarget.QualityName,Language.Common.ColorName[item_cfg.color])
    else
        str = string.format(Language.EquipTarget.QualityName2,Language.Common.ColorName4[item_cfg.color],data.star_level)
    end
    self.node_list.text_target.text.text = ToColorStr(str, ITEM_COLOR[item_cfg.color])

    self.target_item_cell:SetData({item_id = data.target_item_id, param = {star_level = data.star_level}})
    self.target_item_cell:SetRightTopImageTextActive(false)
    self.target_item_cell.button:CustomSetActive(false)


    local item_id_str_list = Split(data.need_item_id,"|")
    local item_id_list = {}
    for index, value in ipairs(item_id_str_list) do
        local item_data = Split(value,",")
        table.insert(item_id_list,{item_id = tonumber(item_data[1]), param = {star_level = tonumber(item_data[2])}})
    end
    self.item_list:SetDataList(item_id_list)

    self.node_list.plus:CustomSetActive(#item_id_list > 0)

    local count = EquipTargetWGData.Instance:GetComposeDescCount() 
    self.node_list.equal:CustomSetActive(data.index ~= count)
end

------------------------------------装备列表------------------------
EquipComposeItemRender = EquipComposeItemRender or BaseClass(BaseRender)

function EquipComposeItemRender:__init()
    self.item_cell = ItemCell.New(self.node_list.node_cell)
end

function EquipComposeItemRender:__delete()
   if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function EquipComposeItemRender:LoadCallBack()
    
end

function EquipComposeItemRender:OnFlush()
    local data = self:GetData()
    if not data then
        return
    end
    self.item_cell:SetData(data)
    self.item_cell:SetRightTopImageTextActive(false)
    self.item_cell.button:CustomSetActive(false)
end
