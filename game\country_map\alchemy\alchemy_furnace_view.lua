AlchemyFurnaceView = AlchemyFurnaceView or BaseClass(SafeBaseView)

function AlchemyFurnaceView:__init()
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(2, -4), sizeDelta = Vector2(1102, 590)})
	self:AddViewResource(0, "uis/view/country_map_ui/alchemy_prefab", "layout_alchemy_furnace")
end

function AlchemyFurnaceView:__delete()

end

function AlchemyFurnaceView:LoadCallBack()
    self.node_list["title_view_name"].text.text = Language.CountryAlchemy.TItleName3

	self.furnace_seq = 0
	if self.cur_attr_list == nil then
        self.cur_attr_list = {}
        local node_num = self.node_list["cur_attr_list"].transform.childCount - 1
        for i = 1, node_num do
            self.cur_attr_list[i] = CommonAddAttrRender.New(self.node_list["cur_attr_list"]:FindObj("attr_" .. i))
        end
    end

    if self.next_attr_list == nil then
        self.next_attr_list = {}
        local node_num = self.node_list["next_attr_list"].transform.childCount - 1
        for i = 1, node_num do
            self.next_attr_list[i] = CommonAddAttrRender.New(self.node_list["next_attr_list"]:FindObj("attr_" .. i))
        end
    end

	if self.max_attr_list == nil then
		self.max_attr_list = {}
        local node_num = self.node_list["max_attr_list"].transform.childCount - 1
        for i = 1, node_num do
            self.max_attr_list[i] = CommonAddAttrRender.New(self.node_list["max_attr_list"]:FindObj("attr_" .. i))
        end
	end

    XUI.AddClickEventListener(self.node_list["level_up_btn"], BindTool.Bind(self.OnClickLevelUp, self))
end

function AlchemyFurnaceView:CloseCallBack()

end

function AlchemyFurnaceView:ReleaseCallBack()
	self.furnace_seq = nil

	if self.cur_attr_list then
	    for k, v in pairs(self.cur_attr_list) do
	        v:DeleteMe()
	    end
	    self.cur_attr_list = nil
    end

    if self.next_attr_list then
	    for k, v in pairs(self.next_attr_list) do
	        v:DeleteMe()
	    end
	    self.next_attr_list = nil
    end

	if self.max_attr_list then
	    for k, v in pairs(self.max_attr_list) do
	        v:DeleteMe()
	    end
	    self.max_attr_list = nil
    end
end

function AlchemyFurnaceView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.furnace_seq then
				self.furnace_seq = v.furnace_seq
			end
		end
	end

	self:FlushFurnacePanel()
end

function AlchemyFurnaceView:FlushFurnacePanel()
	self:FlushCurFurnacePanel()
	local max_level = AlchemyWGData.Instance:GetFurnaceCfgMaxLevel(self.furnace_seq)
	local cur_level = AlchemyWGData.Instance:GetFurnaceLevelBySeq(self.furnace_seq)
	self.node_list.next_furnace_panel:SetActive(cur_level < max_level)
	self.node_list.group:SetActive(cur_level < max_level)
	self.node_list.level_up_btn:SetActive(cur_level < max_level)
	self.node_list.max_furnace_panel:SetActive(cur_level >= max_level)
	self.node_list.max_level:SetActive(cur_level >= max_level)
	if cur_level < max_level then
		self:FlushNextFurnacePanel()
	end
end

function AlchemyFurnaceView:FlushCurFurnacePanel()
	local cur_level = AlchemyWGData.Instance:GetFurnaceLevelBySeq(self.furnace_seq)
	local max_level = AlchemyWGData.Instance:GetFurnaceCfgMaxLevel(self.furnace_seq)

	local cur_cfg
	if cur_level == 0 then -- 0级没配置（只好先拿一级相关数据显示）
		cur_cfg = AlchemyWGData.Instance:GetFurnaceCfgBySeqAndLevel(self.furnace_seq, cur_level + 1)
	else
		cur_cfg = AlchemyWGData.Instance:GetFurnaceCfgBySeqAndLevel(self.furnace_seq, cur_level)
	end

	self.node_list.name.text.text = cur_cfg.name
	local attr_list = AlchemyWGData.Instance:GetCurFurnaceAttrList(self.furnace_seq, cur_level) -- 属性
	if cur_level < max_level then
		self.node_list["cur_furnace_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ldf_" .. cur_level))
		self.node_list.cur_level.text.text = cur_level == 0 and "" or cur_level
		self.node_list.up_value.text.text = cur_level == 0 and 0 or (cur_cfg.reduce_time_scale / 100) .. "%"
		for k,v in pairs(self.cur_attr_list) do
        	v:SetData(attr_list[k])
		end
		local cap = AlchemyWGData.Instance:GetFurnaceCapability(self.furnace_seq, cur_level)
    	self.node_list.cur_cap_value.text.text = cap
	else
		self.node_list["max_furnace_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ldf_" .. cur_level))
		self.node_list.max_level_txt.text.text = cur_level
		self.node_list.max_up_value.text.text = cur_level == 0 and 0 or (cur_cfg.reduce_time_scale / 100) .. "%"
		for k,v in pairs(self.max_attr_list) do
        	v:SetData(attr_list[k])
		end
		local cap = AlchemyWGData.Instance:GetFurnaceCapability(self.furnace_seq, cur_level)
    	self.node_list.max_cap_value.text.text = cap
    end
end

function AlchemyFurnaceView:FlushNextFurnacePanel()
	local next_level = AlchemyWGData.Instance:GetFurnaceLevelBySeq(self.furnace_seq) + 1
	local next_level_cfg = AlchemyWGData.Instance:GetFurnaceCfgBySeqAndLevel(self.furnace_seq, next_level)
	self.node_list["next_furnace_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_ldf_" .. next_level))
	self.node_list.next_level.text.text = next_level

	local attr_list = AlchemyWGData.Instance:GetCurFurnaceAttrList(self.furnace_seq, next_level) -- 属性
	self.node_list.next_up_value.text.text = next_level_cfg.reduce_time_scale / 100 .. "%"
	for k,v in pairs(self.next_attr_list) do
        v:SetData(attr_list[k])
    end

    local cap = AlchemyWGData.Instance:GetFurnaceCapability(self.furnace_seq, next_level)
    self.node_list.next_cap_value.text.text = cap

	local btn_str
	if next_level_cfg.condition_type == 1 then --等级类型
        btn_str = string.format(Language.CountryAlchemy.ArrayBuyStr1, next_level_cfg.condition_value)
  	elseif next_level_cfg.condition_type == 2 then --元宝类型
        btn_str = string.format(Language.CountryAlchemy.ArrayBuyStr2, next_level_cfg.condition_value)
 	elseif next_level_cfg.condition_type == 3 then  --灵玉类型
 		btn_str = string.format(Language.CountryAlchemy.ArrayBuyStr3, next_level_cfg.condition_value)
 	elseif next_level_cfg.condition_type == 4 then  --直购类型
 		local price = RoleWGData.GetPayMoneyStr(next_level_cfg.condition_value, next_level_cfg.rmb_type, next_level_cfg.rmb_seq)
 		btn_str = string.format(Language.CountryAlchemy.ArrayBuyStr4, price)
  	end

  	self.node_list.up_condition_text.text.text = btn_str
end

function AlchemyFurnaceView:OnClickLevelUp()
	local next_level = AlchemyWGData.Instance:GetFurnaceLevelBySeq(self.furnace_seq) + 1
	local next_level_cfg = AlchemyWGData.Instance:GetFurnaceCfgBySeqAndLevel(self.furnace_seq, next_level)
	if not next_level_cfg then
		return
	end

	local text_dec = ""
	local is_enough_lv
    local is_enough_money
    if next_level_cfg.condition_type == 1 then --等级类型
        text_dec = string.format(Language.CountryAlchemy.ArrayBuyTips1, next_level_cfg.condition_value)
        is_enough_lv = RoleWGData.Instance.role_vo.level >= next_level_cfg.condition_value
  	elseif next_level_cfg.condition_type == 2 then --元宝类型
        text_dec = string.format(Language.CountryAlchemy.ArrayBuyTips2, next_level_cfg.condition_value)
        is_enough_money = RoleWGData.Instance:GetIsEnoughAllGold(next_level_cfg.condition_value)
 	elseif next_level_cfg.condition_type == 3 then  --灵玉类型
 		text_dec = string.format(Language.CountryAlchemy.ArrayBuyTips3, next_level_cfg.condition_value)
 		is_enough_money = RoleWGData.Instance:GetIsEnoughUseGold(next_level_cfg.condition_value)
 	elseif next_level_cfg.condition_type == 4 then  --直购类型
 		local price = RoleWGData.GetPayMoneyStr(next_level_cfg.condition_value, next_level_cfg.rmb_type, next_level_cfg.rmb_seq)
 		text_dec = string.format(Language.CountryAlchemy.ArrayBuyTips4, price)
  	end
  	
    local ok_func = function ()
        if next_level_cfg.condition_type == 1 then
 			if is_enough_lv then
				AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.FURNACE_UP, self.furnace_seq)
            else
            	SysMsgWGCtrl.Instance:ErrorRemind(Language.CountryAlchemy.NoEnoughlv)
            end
        elseif next_level_cfg.condition_type == 2 or next_level_cfg.condition_type == 3 then
            if is_enough_money then
            	AlchemyWGCtrl.Instance:SenSAlchemyReq(ALCHEMY_OPERATE_TYPE.FURNACE_UP, self.furnace_seq)
            else
               VipWGCtrl.Instance:OpenTipNoGold()
            end
        elseif next_level_cfg.condition_type == 4 then
        	RechargeWGCtrl.Instance:Recharge(next_level_cfg.condition_value, next_level_cfg.rmb_type, next_level_cfg.rmb_seq)
        end
    end

    TipWGCtrl.Instance:OpenAlertTips(text_dec, ok_func)
end