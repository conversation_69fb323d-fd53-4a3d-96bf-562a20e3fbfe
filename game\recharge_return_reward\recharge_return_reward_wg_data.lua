
RechargeReturnRewardWGData = RechargeReturnRewardWGData or BaseClass()
function RechargeReturnRewardWGData:__init()
	if RechargeReturnRewardWGData.Instance ~= nil then
		print("[RechargeReturnRewardWGData]error:create a singleton twice")
	end
	RechargeReturnRewardWGData.Instance = self
	-- local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_CRAZY_REBATE)
	-- ActivityWGData.Instance:SetActivityRedPointState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_CRAZY_REBATE, is_open)

	self.chongzhi_count = 0
end

function RechargeReturnRewardWGData:__delete()
	RechargeReturnRewardWGData.Instance = nil
end

function RechargeReturnRewardWGData:GetActConfig()
	return ServerActivityWGData.Instance:GetCurrentRandActivityConfig().crazy_rebate
end

function RechargeReturnRewardWGData:SetRechargeNum(num)
	self.chongzhi_count = num
end

function RechargeReturnRewardWGData:GetRechargeNum()
	return self.chongzhi_count
end