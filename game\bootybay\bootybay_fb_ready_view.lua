--- 队伍准备阶段界面
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by 123.
--- DateTime: 2019/9/18 19:52
---
BootyBayFBReadyView = BootyBayFBReadyView or BaseClass(SafeBaseView)
local prepare_timestamp = 10
function BootyBayFBReadyView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(1102, 640)})
    self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_team_prepare")
end

function BootyBayFBReadyView:ReleaseCallBack()
    if  self.member_info_list ~= nil then
        for i, v in pairs(self.member_info_list) do
            v:DeleteMe()
        end
        self.member_info_list = nil
    end
    if CountDownManager.Instance:HasCountDown("bootybay_prepare_time") then
        CountDownManager.Instance:RemoveCountDown("bootybay_prepare_time")
    end
end

function BootyBayFBReadyView:LoadCallBack()
    --界面名字为当前目标名
    self.node_list["title_view_name"].text.text = Language.BootyBay.BootyBayFBTitle2
	self:SetSecondView(nil, self.node_list["size"])
    XUI.AddClickEventListener(self.node_list["btn_prepare"], BindTool.Bind(self.OnClickPrepare, self))
    XUI.AddClickEventListener(self.node_list["btn_refuse"], BindTool.Bind(self.OnClickRefuse, self))
    XUI.AddClickEventListener(self.node_list["btn_exp_add"], BindTool.Bind(self.OnClickExpRule, self))
    XUI.AddClickEventListener(self.node_list["btn_add_times"], BindTool.Bind(self.OnClickAddTimes, self))
    self.node_list.btn_add_times:SetActive(false)
    self.node_list["layout_combine_mark_root"]:SetActive(false)
end

function BootyBayFBReadyView:OpenCallBack()

end

function BootyBayFBReadyView:ShowIndexCallBack()
	ViewManager.Instance:Close(GuideModuleName.BootybayTeamBaoMingView)
   self:Flush()
end

function BootyBayFBReadyView:OnClickExpRule()
    local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.NewTeam.EXP_rule_title)
	rule_tip:SetContent(Language.NewTeam.EXP_desc_rule)
end

function BootyBayFBReadyView:OnClickAddTimes()

end

--点击准备
function BootyBayFBReadyView:OnClickPrepare()
    --ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
    local team_info = BootyBayWGData.Instance:GetBootybayTeamInfo()
    local member_info_list = team_info.confirm_item_list or {}
    local role_id = RoleWGData.Instance:GetRoleVo().role_id
    local is_ready = false
    for k,v in pairs(member_info_list) do
    	if v.uid == role_id then
    		is_ready = v.is_confirm == 1
    	end
    end
    if is_ready then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.HasPrepare)
        return
    end
    BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_CONFIRM_ENTER_TEAM_FB, team_info.wabao_uid)
end

--点击拒绝
function BootyBayFBReadyView:OnClickRefuse()

end

function BootyBayFBReadyView:OnFlush(param_t)
    self.node_list.btn_close_window:SetActive(false)
    if not self.member_info_list then
        self.member_info_list = {}
    end
    local team_info = BootyBayWGData.Instance:GetBootybayTeamInfo()
    local member_info_list = team_info.confirm_item_list or {}

    --print_error("刷新准备界面》》》》》》成员个数 》》》》 ", #member_info_list, member_info_list)
    for i = 1, 3 do
        if not self.member_info_list[i] then
            self.member_info_list[i] = BootyBayFBReadyRendetr.New(self.node_list["info"..i])
        end
        if member_info_list[i] then
            self.member_info_list[i]:SetData(member_info_list[i])
            self.node_list["info"..i]:SetActive(true)
        else
            self.member_info_list[i]:SetData({})
            self.node_list["info"..i]:SetActive(false)
        end
    end

    local prepare_timestamp = team_info.cound_down_timestamp or 0
    local time = math.ceil(prepare_timestamp - TimeWGCtrl.Instance:GetServerTime())
    if CountDownManager.Instance:HasCountDown("bootybay_prepare_time") then
        CountDownManager.Instance:RemoveCountDown("bootybay_prepare_time")
    end
    if time > 0 then
        self:UpdatePrepreTime(0,time)
        CountDownManager.Instance:AddCountDown("bootybay_prepare_time",BindTool.Bind(self.UpdatePrepreTime,self),BindTool.Bind(self.ComletePrepreTime,self),nil,time)
    end

    local role_id = RoleWGData.Instance:GetRoleVo().role_id
    for k,v in pairs(member_info_list) do
    	if v.uid == role_id then
    		XUI.SetGraphicGrey(self.node_list["btn_prepare"], v.is_confirm == 1)
    	end
    end

    self.node_list["btn_refuse"]:SetActive(false)

    --剩余次数
    local enter_teamfb_times = BootyBayWGData.Instance:GetEnterTeamFBTimes()
    local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
    local times = other_cfg.team_fb_enter_times_limit - enter_teamfb_times
    local color = times > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
    self.node_list.remain_times.text.text = string.format(Language.NewTeam.RemainRewardTimes, color, times, other_cfg.team_fb_enter_times_limit)

    self.node_list.exp_add:SetActive(times > 0)

    local other_member_list = SocietyWGData.Instance:GetTeamOtherMemberList()
    local scene_id = Scene.Instance:GetSceneId()
    local other_member_count = 0
    for k,v in pairs(other_member_list) do
        if v and v.scene_id == scene_id then
            other_member_count = other_member_count + 1
        end
    end
    local exp_add = other_member_count * 10
    self.node_list.exp_add_value.text.text = string.format(Language.NewTeam.EXP_Add2, exp_add)
    EmojiTextUtil.ParseRichText(self.node_list.tips.emoji_text, Language.NewTeam.EXP_max_desc, 20, COLOR3B.RED)

end

function BootyBayFBReadyView:UpdatePrepreTime(elapse_time,total_time)
    local time = math.ceil(total_time - elapse_time)
    local team_info = BootyBayWGData.Instance:GetBootybayTeamInfo()
    local role_id = RoleWGData.Instance:GetRoleVo().role_id
    local member_info_list = team_info.confirm_item_list or {}
    local my_prepare_state = false
    for k,v in pairs(member_info_list) do
    	if v.uid == role_id then
    		my_prepare_state = v.is_confirm == 1
    	end
    end
    if my_prepare_state then
        self.node_list.btn_prepare_text.text.text = string.format(Language.NewTeam.HasPrepareState,time)
    else
        self.node_list.btn_prepare_text.text.text = string.format(Language.NewTeam.NoPrepareState,time)
    end
end

function BootyBayFBReadyView:ComletePrepreTime(elapse_time,total_time)
    if CountDownManager.Instance:HasCountDown("bootybay_prepare_time") then
        CountDownManager.Instance:RemoveCountDown("bootybay_prepare_time")
    end
    self:Close()
end

-------------------------------------------------------------------------------------------
--- BootyBayFBReadyRendetr
-------------------------------------------------------------------------------------------

BootyBayFBReadyRendetr = BootyBayFBReadyRendetr or BaseClass(BaseRender)
function BootyBayFBReadyRendetr:__init()
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function BootyBayFBReadyRendetr:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function BootyBayFBReadyRendetr:OnFlush()
    if not self.data then return end
    local is_empty_data = IsEmptyTable(self.data)

	--没有队员信息，隐藏相关信息
	self.node_list.no_data_hide:SetActive(not is_empty_data and self.data.uid > 0)
    self.node_list.no_data_active:SetActive(is_empty_data or self.data.uid <= 0)
	-- self.node_list.head_icon:SetActive(not is_empty_data and self.data.uid > 0)
	self.node_list.prepare_state_text.text.text = ""
    if is_empty_data then return end


    --队长标记
	self.node_list.leader_img:SetActive(false)
    if self.data.uid > 0 then
        local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
        -- 准备
        local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
        local desc
        if other_cfg.open_level <= self.data.level then
            desc = self.data.is_confirm == 1 and Language.NewTeam.StateHasPrepare or Language.NewTeam.StateWaitPrepare
        else
            desc = ToColorStr(Language.Skill.LevelNotEnough, COLOR3B.RED)
        end
        self.node_list.prepare_state_text.text.text = desc
        --人物名字
        if GoalCrossType[team_type] then
            --内网无法请求PHP，先设置默认名字
            local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id)
            self.node_list["role_name"].text.text = string.format(Language.NewTeam.ServerName, COLOR3B.RED, temp_name, self.data.role_name)
        else
            self.node_list.role_name.text.text = self.data.role_name
        end

		--Vip等级
  --       self.node_list.vip_level:SetActive(self.data.vip_level > 0)
		-- --self.node_list.vip_level.text.text = string.format(Language.NewTeam.VipLevel, self.data.vip_level)
  --       if self.data.vip_level > 0 then
  --           local bundle, asset = ResPath.GetVipIcon("vip"..self.data.vip_level)
  --           self.node_list.vip_level.image:LoadSpriteAsync(bundle, asset, function ()       
  --               self.node_list.vip_level.image:SetNativeSize()
  --           end)
  --       end
        self.node_list.vip_level.text.text = self.data.vip_level > 0 and ("V" .. self.data.vip_level) or ""

		--人物等级
        local str = string.format(Language.NewTeam.PTLevel, self.data.level)
        EmojiTextUtil.ParseRichText(self.node_list["role_level"].emoji_text, str, 21, COLOR3B.DEFAULT)
		--头像
        local data = {}
        data.role_id = self.data.uid
        data.sex = self.data.sex
        data.prof = self.data.prof
        data.fashion_photoframe = self.data.shizhuang_photoframe
        self.head_cell:SetImgBg(true)
        self.head_cell:SetData(data)
    else
        self.head_cell:SetImgBg(false)
	end
end