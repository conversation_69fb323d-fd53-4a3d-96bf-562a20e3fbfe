MainUiHideModeView = MainUiHideModeView or BaseClass(SafeBaseView)

function MainUiHideModeView:__init()
	self.view_layer = UiLayer.MainUIHigh
	if MainUiHideModeView.Instance then
		ErrorLog("[MainUiHideModeView] Attemp to create a singleton twice !")
	end
	MainUiHideModeView.Instance = self
	self.view_cache_time = 0
	self:LoadConfig()
end

function MainUiHideModeView:__delete()
	MainUiHideModeView.Instance = nil
end

function MainUiHideModeView:LoadConfig()
	self:AddViewResource(0, "uis/view/main_ui_prefab", "MainUiHideModeView")
end

function MainUiHideModeView:ReleaseCallBack()
end

function MainUiHideModeView:LoadCallBack()
	for i = 1, 3 do
		XUI.AddClickEventListener(self.node_list["btn_hide_" .. i], BindTool.Bind(self.HideMode, self, i))
	end
	XUI.AddClickEventListener(self.node_list["hide_btn"], BindTool.Bind(self.OnClickHide, self))
end

function MainUiHideModeView:OpenCallBack()
end

function MainUiHideModeView:CloseCallBack()
end

function MainUiHideModeView:HideMode(i)
	if not MIANUI_VIEW_EDITOR_FLAG then
		return
	end

	local is_on = true
	if i == 1 then
		MIANUI_VIEW_EDITOR_FLAG = false
		is_on = false
		self:Close()
	end

	MainuiWGCtrl.Instance:FlushView(0, "hide_mian_ui", {is_on, i - 1})
end
function MainUiHideModeView:OnClickHide()
	local cur_alpha = self.node_list["AttackModeList"].canvas_group.alpha
	local target_alpha = cur_alpha > 0.9 and 0 or 1
	self.node_list["AttackModeList"].canvas_group:DoAlpha(cur_alpha, target_alpha, 1)
	TipWGCtrl.Instance:ShowSystemMsg("点击同一个位置恢复显示")
end