--骑宠装备套装
MountLingChongEquipView = MountLingChongEquipView or BaseClass(SafeBaseView)


function MountLingChongEquipView:InitSuitView()
    if not self.suit_list_view then
        self.suit_list_view = AsyncListView.New(EquipSuitCell, self.node_list.suit_list)
        self.suit_list_view:SetSelectCallBack(BindTool.Bind(self.SelectSuitCallBack, self))
        local default = MountLingChongEquipWGData.Instance:GetEquipSuitDefaultIdx(self.cur_show_type)
        self.suit_list_view:SetDefaultSelectIndex(default)
    end
    if not self.suit_item_list then
        self.suit_item_list = {}
        local parent = self.node_list.suit_item_container
        for i = 1, 6 do
            local node = self:GetNodeList(parent, i, "suit_equip_cell")
            self.suit_item_list[i] = ItemCell.New(node)
        end
    end
    if not self.suit_right_attr_list then
        self.suit_right_attr_list = {}
        for i = 1, 3 do
            self.suit_right_attr_list[i] = SuitAttrRender.New(self.node_list["suit_right_attr"..i], self)
        end
    end

    if not self.model_display_suit then
		self.model_display_suit = OperationActRender.New(self.node_list.model_display_suit)
		self.model_display_suit:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    -- if not self.suit_display_model then
    --     self.suit_display_model = RoleModel.New()
    --     local display_data = {
    --         parent_node = self.node_list["suit_model_display"],
    --         camera_type = MODEL_CAMERA_TYPE.BASE,
    --         -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
    --         rt_scale_type = ModelRTSCaleType.S,
    --         can_drag = true,
    --     }
        
    --     self.suit_display_model:SetRenderTexUI3DModel(display_data)
    --     -- self.suit_display_model:SetUI3DModel(self.node_list.suit_model_display.transform, self.node_list.suit_model_display.event_trigger_listener,
    --     --                     1, false, MODEL_CAMERA_TYPE.BASE)
    -- end
    self.node_list["btn_suit_active"].button:AddClickListener(BindTool.Bind(self.OnClickBtnSuitActive, self)) --激活套装
    self.node_list["btn_suit_tips"].button:AddClickListener(BindTool.Bind(self.OnClickPlayDes, self))--弹出tips

    local model_cfg = MountLingChongEquipWGData.Instance:GetModelCfgByType(self.cur_show_type)
    self:FlushModel(self.model_display_suit, self.node_list.model_display_suit, model_cfg, true)
end

function MountLingChongEquipView:DeleteSuitView()
    if self.suit_list_view then
        self.suit_list_view:DeleteMe()
        self.suit_list_view = nil
    end
    -- if self.suit_display_model then
    --     self.suit_display_model:DeleteMe()
    --     self.suit_display_model = nil
    -- end
    if not IsEmptyTable(self.suit_item_list) then
        for k,v in pairs(self.suit_item_list) do
            v:DeleteMe()
        end
        self.suit_item_list = nil
    end
    if not IsEmptyTable(self.suit_right_attr_list) then
        for k,v in pairs(self.suit_right_attr_list) do
            v:DeleteMe()
        end
    end

    if self.model_display_suit then
		self.model_display_suit:DeleteMe()
		self.model_display_suit = nil
	end
    --self.suit_show_res_id = nil
    self.suit_right_attr_list = nil
    self.cur_suit_index = nil
end

function MountLingChongEquipView:OnClickBtnSuitActive()
    if not IsEmptyTable(self.cur_suit_data) then
        local is_active, cur_num, active_count = MountLingChongEquipWGData.Instance:GetSuitCanActive(self.cur_suit_data.show_type, self.cur_suit_data.quality)
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(self.cur_show_type,
        MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_ACTIVE_SUIT, self.cur_suit_data.quality, cur_num) --请求激活
    end
end

-- function MountLingChongEquipView:FlushSuitDisplayModel()
--     local temp_res_id, name, grade_num = MountLingChongEquipWGData.Instance:GetCurShowModelId(self.cur_show_type)
--     -- self.node_list.suit_model_name.text.text = name
--     -- self.node_list.suit_grade_num.text.text = NumberToChinaNumber(grade_num) .. Language.Common.Jie
--     local path = nil
--     if self.cur_show_type == MOUNT_PET_EQUIP_TYPE.PET then
-- 		path = ResPath.GetPetModel
-- 	elseif self.cur_show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
-- 		path = ResPath.GetMountModel
--     elseif self.cur_show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN then
--         path = ResPath.GetMountModel
--     end
--     if self.suit_show_res_id == temp_res_id then
-- 		return
--     end
-- 	self.suit_show_res_id = temp_res_id
-- 	-- if self.cur_show_type == MOUNT_PET_EQUIP_TYPE.PET then
-- 	-- 	self.suit_display_model:ClearModel()
-- 	-- 	self.suit_display_model:PlaySoulAction()
-- 	-- elseif self.cur_show_type == MOUNT_PET_EQUIP_TYPE.MOUNT then
-- 	-- 	self.suit_display_model:PlayMountAction(false)
-- 	-- end

-- 	-- local bundle, asset = path(temp_res_id)
-- 	-- if self.suit_display_model and bundle ~= nil then
-- 	-- 	self.suit_display_model:SetMainAsset(bundle,asset)
--     -- end
-- end

function MountLingChongEquipView:FlushTuTengIcon()
    -- local temp_res_id, name, grade_num = MountLingChongEquipWGData.Instance:GetCurShowModelId(self.cur_show_type)
    -- if self.suit_show_res_id == temp_res_id then
	-- 	return
    -- end
	-- self.suit_show_res_id = temp_res_id
    self.node_list.suit_tuteng_zuoqi:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.PET)
    self.node_list.suit_tuteng_chongwu:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.MOUNT)
    self.node_list.suit_tuteng_huakun:SetActive(self.cur_show_type == MOUNT_PET_EQUIP_TYPE.HUAKUN)
end

function MountLingChongEquipView:FlushSuitCapability()
    local capa = MountLingChongEquipWGData.Instance:GetSuitCapability(self.cur_show_type)
    self.node_list.suit_cap_value.text.text = capa
end

function MountLingChongEquipView:ShowSuitCallBack()
    --self:PlaySuitTween()
    local default = MountLingChongEquipWGData.Instance:GetEquipSuitDefaultIdx(self.cur_show_type)
    self.suit_list_view:JumpToIndex(default)

    if not self.cur_suit_index then
        return
    end
    self:FlushSuitOtherView()
    self:FlushSuitCapability()
end

function MountLingChongEquipView:PlaySuitTween()
    local tween_info = UITween_CONSTS.MountPetEquipSys.Suit

	UITween.CleanAllTween(self.view_name)
	UITween.FakeHideShow(self.node_list.suit_right_container)
	UITween.FakeHideShow(self.node_list.suit_item_container)
    UITween.FakeHideShow(self.node_list.left_list_part)

    RectTransform.SetAnchoredPositionXY(self.node_list.suit_list_bg.rect, -800, -19)
	RectTransform.SetAnchoredPositionXY(self.node_list.right_suit_part.rect, 900, 0)
	RectTransform.SetAnchoredPositionXY(self.node_list.suit_capa_container.rect, 16, -200)

	self.node_list.suit_capa_container.rect:DOAnchorPos(Vector2(16, -2), tween_info.MoveTime)
	self.node_list.right_suit_part.rect:DOAnchorPos(Vector2(517, 0), tween_info.MoveTime)
    self.node_list.suit_list_bg.rect:DOAnchorPos(Vector2(-497, -19), tween_info.MoveTime)

    ReDelayCall(self, function()
		UITween.AlphaShow(self.view_name, self.node_list.suit_item_container, 0, tween_info.ToAlpha, tween_info.AlphaTime)
		UITween.AlphaShow(self.view_name, self.node_list.suit_right_container, 0, tween_info.ToAlpha, tween_info.AlphaTime)
		UITween.AlphaShow(self.view_name, self.node_list.left_list_part, 0, tween_info.ToAlpha, tween_info.AlphaTime)
	end, tween_info.AlphaDelay, "mount_pet_equip_suit_tween")
end

function MountLingChongEquipView:SelectSuitCallBack(item)
    if not item or IsEmptyTable(item:GetData()) then
        return
    end
    if self.cur_suit_index == item.index then
        return
    end
    self.cur_suit_index = item.index
    self:FlushSuitOtherView()
    self:FlushSuitCapability()
end

function MountLingChongEquipView:FlushSuitView()
    --self:FlushSuitDisplayModel()
    --self:FlushTuTengIcon()

    local suit_list = MountLingChongEquipWGData.Instance:GetSuitViewList(self.cur_show_type)
    self.suit_list_view:SetDataList(suit_list)
    if self.cur_suit_index then
        self:FlushSuitOtherView()
        self:FlushSuitCapability()
    end
end

function MountLingChongEquipView:PlaySuitActiveSuccess()
    if self.node_list.suit_effect_root then
        self.node_list.suit_effect_root:SetActive(true)
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0),
									parent_node = self.node_list["suit_effect_root"]})
    end
end

function MountLingChongEquipView:FlushSuitOtherView()
    local suit_list = MountLingChongEquipWGData.Instance:GetSuitList(self.cur_show_type)
    self.cur_suit_data = suit_list[self.cur_suit_index]
    local quality = self.cur_suit_data.quality
    local item_list = MountLingChongEquipWGData.Instance:GetSuitItemList(self.cur_show_type, quality)
    for i = 1, 6 do --刷新中间6间装备显示
        if item_list[i] and item_list[i].equip_id then
            local data = {}
            data.item_id = item_list[i].equip_id
            local cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
            self.suit_item_list[i]:SetData(data)
            local satisty_flag_tb = self.cur_suit_data.satisty_flag_tb
            self.suit_item_list[i]:MakeGray(satisty_flag_tb[i-1] == 0)
        end
    end

    --刷新右边属性
    for k, v in ipairs(self.suit_right_attr_list) do
        if not IsEmptyTable(self.cur_suit_data.suit_grade) then
            local suit_cfg = self.cur_suit_data.suit_grade[k]
            if suit_cfg then
                v:SetVisible(true)
                v:SetData(suit_cfg, self.cur_suit_data.active_flag_tb)
            else
                v:SetVisible(false)
            end
        end
    end

    local is_active, cur_num, active_count = MountLingChongEquipWGData.Instance:GetSuitCanActive(self.cur_suit_data.show_type, self.cur_suit_data.quality)
    XUI.SetButtonEnabled(self.node_list["btn_suit_active"], is_active)
    self.node_list.suit_active_remind:SetActive(is_active)
    local total_count = self.cur_suit_data.suit_grade[#self.cur_suit_data.suit_grade].num
    if not is_active then
        self.node_list.btn_suit_txt.text.text = active_count == total_count and Language.MountPetEquip.HadActive or Language.MountPetEquip.ActiveSuit
    else
        self.node_list.btn_suit_txt.text.text = Language.MountPetEquip.ActiveSuit
    end
end

function MountLingChongEquipView:GetNodeList(parent, index, str)
    local obj = parent.transform:Find(str..index).gameObject
    local item = U3DObject(obj, obj.transform, self)
    return item
end


EquipSuitCell = EquipSuitCell or BaseClass(BaseRender)

function EquipSuitCell:LoadCallBack()

end

function EquipSuitCell:__delete()

end

function EquipSuitCell:OnFlush()
    local is_active, cur_num, active_count = MountLingChongEquipWGData.Instance:GetSuitCanActive(self.data.show_type, self.data.quality)
    local total_count = 0
    if not IsEmptyTable(self.data.suit_grade) then
        total_count = self.data.suit_grade[#self.data.suit_grade].num
    end
    local str = string.format(Language.MountPetEquip.SuitNameStr, self.data.suit_name, active_count, total_count)
    self.node_list.suit_name.text.text = ToColorStr(str, ITEM_COLOR[self.data.quality])
    self.node_list.suit_name_hl.text.text = ToColorStr(str, ITEM_COLOR_LIGHT_NEW[self.data.quality])

    self.node_list.suit_remind_tip:SetActive(is_active)
end

function EquipSuitCell:OnSelectChange(is_select)
    self.node_list.img_ml_item_hight:SetActive(not is_select)
	self.node_list.item_hightlight:SetActive(is_select)	--非高亮
end

--右边属性render
SuitAttrRender = SuitAttrRender or BaseClass(BaseRender)

function SuitAttrRender:__init(instance, parent)
    self.parent_view = parent
end

function SuitAttrRender:__delete()
    self.parent_view = nil
    self.is_active = nil
    self.suit_name = nil
    self.suit_name_hl = nil
end

function SuitAttrRender:SetData(data, active_flag_tb)
    self.data = data
    self.active_flag_tb = active_flag_tb
	if self.has_load or self.is_use_objpool then
		self:OnFlush()
	else
		self:Flush()
	end
end

function SuitAttrRender:OnFlush()
    if not self.data then
        return
    end
    local is_active = self.active_flag_tb[self.data.num] == 1
    if self.is_active == nil or self.suit_name == nil or self.suit_name_hl == nil then
        self.is_active = is_active
        self.suit_name = self.data.suit_name
        self.suit_name_hl = self.data.suit_name_hl
    elseif self.suit_name ~= self.data.suit_name then
        self.suit_name = self.data.suit_name
        self.suit_name_hl = self.data.suit_name_hl
        self.is_active = is_active
    elseif self.is_active == false and is_active == true then
        if self.parent_view then
            self.parent_view:PlaySuitActiveSuccess()
        end
        self.is_active = is_active
    end
    local color = is_active and COLOR3B.GREEN or TIPS_COLOR.ATTR_NAME
    -- local str = ToColorStr(string.format(Language.MountPetEquip.SuitNum, self.data.num), color)
    -- self.node_list.text_num.text.text = str
    for i = 0, 3 do
        if self.data["attr_name_"..i] then
            self.node_list["suit_attr_".. i + 1]:SetActive(true)
            --self.node_list["suit_attr_text".. i + 1]:SetActive(true)
            --local str = ToColorStr(self.data["attr_name_"..i].."："..self.data["attr_value_"..i], color)
            local str = ToColorStr(self.data["attr_name_"..i], color)
            local data_statue = ToColorStr(self.data["attr_value_"..i], color)
            self.node_list["suit_attr_text".. i + 1].text.text = str
            self.node_list["suit_attr_data".. i + 1].text.text = data_statue
        else
            --self.node_list["suit_attr_text".. i + 1]:SetActive(false)
            self.node_list["suit_attr_".. i + 1]:SetActive(false)
        end
    end
    local add_percent = self.data.add_percent
    if add_percent and add_percent ~= 0 then
        --local str = ToColorStr(Language.MountPetEquip.UpgradeAddPercent .."："..add_percent / 100 .. "%", color)
        local str = ToColorStr(Language.MountPetEquip.UpgradeAddPercent, color)
        local data_statue = ToColorStr(add_percent / 100 .. "%", color)
        self.node_list["suit_attr_text".. 5].text.text = str
        --self.node_list["suit_attr_text".. 5]:SetActive(true)
        self.node_list["suit_attr_".. 5]:SetActive(true)
        self.node_list["suit_attr_data".. 5].text.text = data_statue
    else
        --self.node_list["suit_attr_text".. 5]:SetActive(false)
        self.node_list["suit_attr_".. 5]:SetActive(false)
    end
    local attr = {}
    for i = 0, 4 do
        if self.data["attr_type_str_"..i] then
            attr[self.data["attr_type_str_"..i]] = self.data["attr_value_"..i]
        end
    end
    -- local add_capa = 0
    -- if add_percent and add_percent > 0 then
    --     add_capa = MountLingChongEquipWGData.Instance:MountPetEquipAttr(self.parent_view.cur_show_type, add_percent/10000)
    -- end
    --local attribute = AttributeMgr.GetAttributteByClass(attr)
    --local capa = AttributeMgr.GetCapability(attribute)
    --self.node_list.cap_value.text.text = string.format(Language.MountPetEquip.Suitcapability, capa + add_capa)
    --self.node_list.active_flag:SetActive(is_active)
end