DaoHangHolySealView = DaoHangHolySealView or BaseClass(SafeBaseView)

function DaoHangHolySealView:__init()
	self:SetMaskBg()
	local daohang_bundle_name = "uis/view/multi_function_ui/daohang_prefab"
	self:AddViewResource(0, daohang_bundle_name, "layout_daohang_shengyin")
end

function DaoHangHolySealView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_answer"], BindTool.Bind(self.OnClickAnswer, self))
	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuy, self))
	XUI.AddClickEventListener(self.node_list["skill_icon"], BindTool.Bind(self.OnClickSkill, self))
	XUI.AddClickEventListener(self.node_list["skill_tip_close"], BindTool.Bind(self.OnClickSkillClose, self))
	XUI.AddClickEventListener(self.node_list["btn_up"], BindTool.Bind(self.OnClickUp, self))
	XUI.AddClickEventListener(self.node_list["btn_tequan"], BindTool.Bind(self.OnClickChange, self))
	XUI.AddClickEventListener(self.node_list["logo"], BindTool.Bind(self.OnClickLogo, self))

	self.view_data = MultiFunctionWGData.Instance:GetDaoHangOtherCfg()
	if self.reward_grade_list == nil then
		self.reward_grade_list = AsyncListView.New(DaoHangHolyBuy, self.node_list["tequan_list"])
	end
	local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(self.view_data.reward_item or {})
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	self.reward_list:SetDataList(reward_list)

	self.cost_item_cell = ItemCell.New(self.node_list["cost_item_pos"])
	self.cost_item_cell:SetNeedItemGetWay(true)

	if self.attr_list == nil then
        self.attr_list = {}
        local node_num = self.node_list["attr_list"].transform.childCount
        for i = 1, node_num do
            self.attr_list[i] = CommonAddAttrRender.New(self.node_list["attr_list"]:FindObj("attr_" .. i))
			--self.attr_list[i]:SetAttrNameNeedSpace(true)
        end
	end

	self.view_flag = true
	self.level = 0
	self.buy_cfg_num = MultiFunctionWGData.Instance:GetDaoHangRmbBuyCfgNum() or 0

	self:KillArrowTween()
	local tween_time = 0.8
    local node = self.node_list["arrow_5"]
	if node then
        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -18)
        self.arrow_tweener = node.rect:DOAnchorPosY(-12, tween_time)
        self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function DaoHangHolySealView:KillArrowTween()
    if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
    end
end

function DaoHangHolySealView:ReleaseCallBack()

	if self.reward_grade_list then
		self.reward_grade_list:DeleteMe()
		self.reward_grade_list = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.cost_item_cell then
		self.cost_item_cell:DeleteMe()
		self.cost_item_cell = nil
	end

	if self.attr_list then
		for k, v in pairs(self.attr_list) do
			v:DeleteMe()
		end
		self.attr_list = nil
	end
	self.view_data = nil
	self.buy_cfg_num = nil
	self.data = nil
	self.level = nil
	self.have_num = nil

	self:KillArrowTween()
end

function DaoHangHolySealView:OpenCallBack()
	self.up_flag = true
end

function DaoHangHolySealView:CloseCallBack()
	CancleAllDelayCall(self)
end

function DaoHangHolySealView:OnFlush()
	self.level = MultiFunctionWGData.Instance:GetPiXiuLv()
	self.data = MultiFunctionWGData.Instance:GetDaoHangCurRmbBuyCfg()
	local next_data = MultiFunctionWGData.Instance:GetNextDaoHangCurRmbBuyCfg()
	self.node_list["active_panel"]:SetActive(self.level == 0)
	self.node_list["up_level_panel"]:SetActive(self.level ~= 0)
	self.node_list["btn_answer"]:SetActive(self.level ~= 0)
	self.node_list["btn_tequan"]:SetActive(self.level ~= 0)
	local cap_data = self.data
	if self.level == 0 then
		cap_data = next_data
	end
	local _, capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cap_data, "attr_id", "attr_value")
	self.node_list["active_cap_value"].text.text = capability
	local seq_reward_list = MultiFunctionWGData.Instance:GetDaoHangPrivilegeCfg()

	local num = ItemWGData.Instance:GetItemNumInBagById(self.data.cost_item_id)
	self.cost_item_cell:SetData({ item_id = self.data.cost_item_id or 0 })
	local is_have_stuff = num >= (self.data.cost_item_num or 0)
	self.cost_item_cell:SetRightBottomColorText(num .. "/" .. (self.data.cost_item_num or 0),
		is_have_stuff and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
	self.cost_item_cell:SetRightBottomTextVisible(true)

	if seq_reward_list then
		self.reward_grade_list:SetDataList(seq_reward_list)
	end
	if self.level == 0 then --0级表示未购买
		self.node_list["attr_desc_text"].text.text = self.view_data.rmb_description
		self:LoadSkillImg(self.node_list["skill_icon"], self.view_data.skill_icon)
		self:LoadSkillImg(self.node_list["skill_tip_icon"], self.view_data.skill_icon)
		self.node_list["skill_tip_name"].text.text = self.view_data.skill_name
		self.node_list["skill_tip_desc"].text.text = self.view_data.skill_description
		self.node_list["answer_timebg"]:SetActive(false)
		for i = 1, 4 do
			local name = EquipmentWGData.Instance:GetAttrNameByAttrId(self.data["attr_id" .. i], true)
			self.node_list["active_attr" .. i].text.text = string.format(Language.Charm.DaoHangArtText, name,
				(next_data["attr_value" .. i] or 0))
		end
		self.node_list["active_attr5"].text.text = string.format(Language.Charm.DaoHangArtText, self.view_data.sp_attr_txt,
			next_data.add / 10 .. "%")
		self.node_list["logo"].button.interactable = false
	else
		local fumo_time = MultiFunctionWGData.Instance:GetFuMoTime()
		local zero_time = TimeWGCtrl.Instance:GetTodayBeginningTimestamp()
		self.node_list["up_level_panel"]:SetActive(self.view_flag)
		self.node_list["tequan_panel"]:SetActive(not self.view_flag)
		self.node_list["answer_timebg"]:SetActive(true)

		self.node_list["skill_name"].text.text = self.view_data.skill_name
		self.node_list["skill_desc"].text.text = self.view_data.skill_description
		self:LoadSkillImg(self.node_list["up_skill_icon"], self.view_data.skill_icon)
		local attr_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(self.data, next_data, "attr_id", "attr_value", 1, 4)
		for k,v in pairs(self.attr_list) do
       		v:SetData(attr_list[k])
    	end

		self.node_list["attr_name_5"].text.text = self.view_data.sp_attr_txt
		local attr_flag = 0
		if self.level ~= self.buy_cfg_num then
			local attr_flag = next_data.add - self.data.add
		end
		self.node_list["arrow_5"]:SetActive(attr_flag > 0)
		self.node_list["add_value_5"]:SetActive(attr_flag > 0)
		self.node_list["attr_value_5"].text.text = self.data.add / 10 .. "%"
		self.node_list["add_value_5"].text.text = attr_flag / 10 .. "%"

		local pri = MultiFunctionWGData.Instance:GetPrivilegeNum()
		local fumo_max = pri[1]
		for i = 1, 3 do
			self.node_list["tequan_desc" .. i].text.text = string.format(self.view_data["privilege_txt" .. i], pri[i])
		end
		
		self.node_list["level"].text.text = string.format(Language.ShenShou.Leave, self.level)
		self.node_list["up_max_flag"]:SetActive(self.level == self.buy_cfg_num)
		self.node_list["btn_up"]:SetActive(self.level ~= self.buy_cfg_num)

		local dati_index = MultiFunctionWGData.Instance:GetQuestionId()
		local dati_num = MultiFunctionWGData.Instance:GetDaoHangDaTiNum()
		self.node_list["btn_answer"]:SetActive(dati_index ~= dati_num)
		self.have_num = ItemWGData.Instance:GetItemNumInBagById(self.data.cost_item_id) or 0
		if self.up_flag == false then
			self.node_list["btn_up_remind"]:SetActive(false)
			self.node_list["btn_text"].text.text = Language.Skill.StopUpLevel
			if is_have_stuff == false then
				self.node_list["btn_text"].text.text = Language.Skill.UpLeveling
			end
		else
			self.node_list["btn_text"].text.text = Language.Skill.UpLeveling
			self.node_list["btn_up_remind"]:SetActive(self.have_num >= self.data.cost_item_num)
		end
		local kuatian_flag = fumo_time < zero_time
		if kuatian_flag then MultiFunctionWGData.Instance:ClearFuMoNum() end
		local fumo_num = MultiFunctionWGData.Instance:GetTouchNum()
		local fumo_flag = fumo_max - fumo_num
		self.node_list["logo"].button.interactable = (fumo_flag ~= 0)
		self.node_list["answer_timebg"]:SetActive(fumo_flag ~= 0)
		self.node_list["answer_time"].text.text = string.format(Language.Charm.DaoHangTouchText, fumo_flag, fumo_max)
		
	end
end

function DaoHangHolySealView:LoadSkillImg(node, icon_id)
	local bundle, asset
	bundle, asset = ResPath.GetSkillIconById(icon_id)
	node.image:LoadSprite(bundle, asset)
end

function DaoHangHolySealView:OnClickAnswer()
	MultiFunctionWGCtrl.Instance:OpenDaoHangDaTiView()
end

function DaoHangHolySealView:OnClickBuy()
	RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
end

function DaoHangHolySealView:OnClickSkill()
	self.node_list["skill_tip"]:SetActive(true)
	self.node_list["skill_tip_close"]:SetActive(true)
end

function DaoHangHolySealView:OnClickSkillClose()
	self.node_list["skill_tip"]:SetActive(false)
	self.node_list["skill_tip_close"]:SetActive(false)
end

function DaoHangHolySealView:OnClickUp()
	self.up_flag = not self.up_flag
	if self.up_flag == true then
		self.node_list["btn_text"].text.text = Language.Skill.UpLeveling
		self.node_list["btn_up_remind"]:SetActive(self.have_num >= self.data.cost_item_num)
	end
	self:UpBuyLevel()
end

function DaoHangHolySealView:UpBuyLevel()
	if self.up_flag == false then
		local num = ItemWGData.Instance:GetItemNumInBagById(self.data.cost_item_id)
		if num < self.data.cost_item_num then
			TipWGCtrl.Instance:OpenItem({ item_id = self.data.cost_item_id })
			SysMsgWGCtrl.Instance:ErrorRemind(Language.SupremeFields.Tips3)
			self.up_flag = true
		else
			if self.level ~= self.buy_cfg_num then
				MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.RMB_BUY_UP_LEVEL)				
				self:PlayUpGradeEffect()
				AddDelayCall(self, function()
					self:UpBuyLevel()
				end, 0.5)
			end
		end
	end

end

function DaoHangHolySealView:OnClickChange()
	self.node_list["up_level_panel"]:SetActive(not self.node_list["up_level_panel"].gameObject.activeSelf)
	self.node_list["tequan_panel"]:SetActive(not self.node_list["tequan_panel"].gameObject.activeSelf)
	self.view_flag = not self.view_flag
	local btn_tequan_text = ""
	if self.view_flag == true then
		btn_tequan_text = Language.Charm.DaoHangUpLevelText
	else
		btn_tequan_text = Language.Charm.DaoHangPeiYangText
	end
	self.node_list["btn_tequan_text"].text.text = btn_tequan_text
end

function DaoHangHolySealView:OnClickLogo()
	if self.level > 0 then
		MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.TOUCH)
		self:PlayFuMoEffect()
	end
end

function DaoHangHolySealView:PlayFuMoEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_shengyin_baozha)
	EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list["logo"].transform, 1, nil, nil, nil, nil)
end

function DaoHangHolySealView:PlayUpGradeEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["logo"].transform)
	TipWGCtrl.Instance:ShowEffect({ effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0) })
end

DaoHangHolyBuy = DaoHangHolyBuy or BaseClass(BaseRender)
function DaoHangHolyBuy:__init()
	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuyBtn, self))
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
end

function DaoHangHolyBuy:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function DaoHangHolyBuy:OnFlush()
	if self.data == nil then
		return
	end
	local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(self.data.reward_item)
	self.reward_list:SetDataList(reward_list)
	self.node_list["name"].text.text = self.data.rmb_title
	self.node_list["desc"].text.text = self.data.rmb_description
	local price = RoleWGData.GetPayMoneyStr(self.data.price, self.data.type, self.data.rmb_id)
	self.node_list["btn_text"].text.text = price
	local buy_flag_list = MultiFunctionWGData.Instance:GetDaoHangRmbBuyFlag()
	local buy_flag = buy_flag_list[self.index - 1] == 1
	self.node_list["buy_flag"]:SetActive(buy_flag)
	self.node_list["btn_buy"]:SetActive(not buy_flag)
end

function DaoHangHolyBuy:OnClickBuyBtn()
	if self.data == nil then
		return
	end
	RechargeWGCtrl.Instance:Recharge(self.data.price or 0, self.data.type or 0, self.data.rmb_id or 0)
end
