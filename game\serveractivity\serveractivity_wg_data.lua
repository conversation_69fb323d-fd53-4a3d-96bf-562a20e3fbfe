ServerActClientId = {
	TOTAL_CHONGZHI = 1, 				--每日累充
	EQUIP_GATHER = 2,					--装备收集
	SEVENDAY_GOAL = 3, 					--七天目标
	LV_SPRINT = 4,						--等级冲刺
	EQUIP_GIFT = 5,						--装备特惠礼包
	-- TUAN_MONTH_CARD = 6,				--月卡团购(删)
	ALL_PERSION_RANK = 6, 				--全民冲榜
	TUAN_VIP = 7,						--vip 团购(删)
	TUAN_XUNBAO = 8,					--寻宝团购(删)
	BUY_MONTH_CARD = 9,					--购买月卡
	DAILY_TOTAL_CHONGZHI = 10, 			--每日累充
	DAILY_FISRT_CHONGZHI = 11, 			--每日首充
	DISCOUNT_ONSELL1 = 12, 				--一折抢购

	--封测活动
	FC_LOGIN_REWARD = 13, 				--登陆大礼
	FC_LEVEL_REWARD = 14, 				--升级狂欢
	FC_ONLINE_REWARD = 15, 				--在线奖励
	FC_NATIONAL_VIP = 16, 				--全民VIP
	FC_CHALLENGE_REWARD = 17, 			--挑战大礼
	FC_ACTIVITY_REWARD = 18, 			--活动大礼
	FC_JOIN_GUILD_REWARD = 19, 			--仙盟大礼
	FC_MARRY_REWARD = 20, 				--情缘大礼
	FC_FIGHT_REWARD = 21, 				--竞技大礼
	FC_BATTLE_REWARD = 22, 				--战场大礼
	FC_FEED_BACK = 23, 					--封测回馈
	FC_FIND_BUG = 24, 					--抓虫有奖

	GUILD_LEVEL_RANK = 25, 				--仙盟等级比拼(最强仙盟)(删)
	GUILD_HUNT_RANK = 26, 				--仙盟BOSS狩猎比拼(删)
	INVEST_PLAN = 27, 					--投资计划
	LUCKY_ROLL_COIN = 28, 				--幸运铜币
	LUCKY_ROLL_GOLD = 29, 				--幸运元宝
	CELEBRITY_TITLE = 30,				--名人称号(删)

	SHEN_FANGJU = 31,					-- 开服活动神装收集

	--随机活动
	RAND_DAY_CHONGZHI_FANLI = 32,		--单日充值返利
	RAND_DAY_CONSUME_GOLD = 33,			--单日消费
	RAND_DAY_ACTIVIE_DEGREE = 35,		--单日活跃奖励
	RAND_CHONGZHI_RANK = 36,			--充值排行
	RAND_SERVER_PANIC_BUY = 38,			--全服疯狂抢购O
	RAND_PERSONAL_PANIC_BUY = 39,		--个人疯狂抢购O
	RAND_CONSUME_GOLD_FANLI = 40,		--消费返利
	RAND_EQUIP_STRENGTHEN = 41,			--装备强化
	RAND_CHESTSHOP = 42,				--奇珍异宝
	RAND_STONE_UPLEVEL = 43,			--宝石升级
	RAND_XN_CHANMIAN_UPLEVEL = 44,		--仙女缠绵
	RAND_MOUNT_UPGRADE = 45,			--坐骑进阶
	RAND_QIBING_UPGRADE = 46,			--骑兵升级
	RAND_MENTALITY_TOTAL_LEVEL = 47,	--根骨全身等级
	RAND_WING_UPGRADE = 48,				--羽翼进化
	RAND_QUANMIN_QIFU = 49,				-- 全民祈福
	RAND_ACTIVITY_TYPE_XIANLING_ZHEN = 50,			-- F2仙灵古阵
	RAND_XIANMENG_JUEQI = 51,			-- 仙盟崛起
	RAND_XIANMENG_BIPIN = 52,			-- 仙盟比拼
	RAND_DAY_ONLINE_GIFT = 53,			-- 每日在线好礼
	RAND_KILL_BOSS = 54,				-- 掉落狂欢
	RAND_DOUFA_KUANGHUAN = 55,			-- 斗法狂欢
	RAND_ZHANCHANG_FANBEI = 56,			-- 战场翻倍
	RAND_LOGIN_GIFT = 57,				-- 登录奖励

	ALWAYS_CONTACT_GM = 58,				-- gm有礼
	GOLD_INGOT = 59,					-- 我要元宝

	RAND_CHARGE_REPALMENT = 60, 		--充值回馈
	RAND_SINGLE_CHARGE = 61,			--单笔充值

	--合服活动
	CS_LOGIN_REWARD = 62,				 --登录有礼
	CS_COLLECT_WORD = 63,				 --集字有礼
	CS_TYPE_CONSUME = 64,				 --消费有礼
	CS_TYPE_DOUBLE_EXP = 65,			 --双倍经验
	CS_GUILD_BATTLE1 = 66,				 --帮派争霸1
	CS_GUILD_BATTLE2 = 67,				 --帮派争霸2
	CS_PANIC_BUY = 68,					 --特惠秒杀
	CS_LIMIT_BUY = 69,					 --限时云购
	CS_FB_DOUBLE = 70,					 --副本双倍
	CS_RECHARGE_RANK = 71,				 --充值排行
	CS_RECHARGE_CHOU = 72,				 --充值抽抽

	--随机活动
	RAND_CHONGZHI_DOUBLE = 74,			--双倍充值
	RAND_DAY_DANBI_CHONGZHI = 75,		--随机活动每日单笔充值
	RAND_TOTAL_CHARGE_DAY = 76, 		--随机活动每日累充

	CS_CHONGZHI_DOUBLE = 77, 		    --合服活动双倍充值

	RAND_TOMORROW_REWARD = 78,			--次日福利
	RAND_SEVEN_DOUBLE = 79,		 		--七日双倍
	RAND_XUNBAO_JIFEN = 83,				--寻宝积分翻倍
	RAND_EQUIP_EXCHANGE = 84,			--装备兑换
	RAND_SPRITE_EXCHANGE = 85,			--精灵兑换
	RAND_DAILY_ONLINE_LOTTERY = 86,		--在线时间领取奖励
	MOLONG_LONGXINGTIANXIA = 87,		--魔龙(龙行天下)
	RAND_ACTIVITY_ZHENGGUZJ = 88,		--整蛊专家(变身榜)
	RAND_ACTIVITY_BEIZHENGDAREN = 89,	--被整达人(被变身榜)
	MIGONGXUNBAO = 90,					--迷宫寻宝
	--RAND_TIMEBOSS = 91,					--定时boss
	--RAND_MONSTER_DROP = 92,				--打怪掉落
	--RAND_ZHANCHANG_ZHENGBA = 93,		--战场争霸
	RAND_CHAOJI_VIP = 94,				--超级vip
	RAND_FISHING = 95, 					--开心捕鱼
	COLLECT_ITEMS = 96,					--集字活动
	RAND_EVALUATE = 97,					--五星评价
	ADVANCED_REWARD = 98,				--进阶奖励
	GROUP_BUYING = 99,					--首充团购
	ALL_RANK = 100,						--全民冲榜
	GUILD_RANK_XIANGMENGZHAN = 101,		--帮派争霸
	DOUBLE_AWARD = 102,					--双倍奖励

	TUPU_EXCHANGE = 103,                            --图谱兑换

	RAND_MOUNT_CHARGE = 104,                  -- 坐骑充值
	RAND_WING_CHARGE = 105,                   -- 羽翼充值
	RAND_FABAO_CHARGE = 106,                  -- 法宝充值
	RAND_SHENWU_CHARGE = 107,                 -- 神武充值
	RAND_LINGCHONG_CHARGE = 108,  -- 灵宠充值
	RAND_LINGQI_CHARGE = 109,                 -- 灵骑充值
	RAND_LINGGONG_CHARGE = 110,         -- 灵弓充值
	RAND_LINGYI_CHARGE = 111,                 -- 灵翼充值

	RAND_MOUNT_SHOP = 112,                    -- 坐骑抢购
	RAND_WING_SHOP = 113,                     -- 羽翼抢购
	RAND_FABAO_SHOP = 114,                    -- 法宝抢购
	RAND_SHENWU_SHOP = 115,                   -- 神武抢购
	RAND_LINGCHONG_SHOP = 116,                -- 灵宠抢购
	RAND_LINGQI_SHOP = 117,                   -- 灵骑抢购
	RAND_LINGGONG_SHOP = 118,                 -- 灵弓抢购
	RAND_LINGYI_SHOP = 119,                   -- 灵翼抢购

	RAND_LOTTERY_DRAW = 120,                   -- 幸运抽奖
	FC_NATIONAL_ZONDONGYUAN = 121,             -- 全民总动员
	FC_NATIONAL_MARRIDE = 122,                 -- 全民结婚

	RAND_SHENWU_UPGRADE = 123,			--神武进阶
	RAND_FABAO_UPGRADE = 124,			--法宝进阶
	RAND_LINGCHONG_UPGRADE = 125,			--灵宠进阶
	RAND_LINGGONG_UPGRADE = 126,			--灵弓进阶
	RAND_LINGQI_UPGRADE = 127,			--灵骑进阶
	RAND_LINGYI_UPGRADE = 128,			--灵翼进阶

	ACT_JING_JI_CHANG = 133 ,				-- 开服活动竞技场
	RAND_WEEKEND_BOSS = 134,                        -- 周末BOSS

	SPRINT_LEVEL= 135 ,				   		-- 开服活动 -- 冲级达人
	MOUNT_JINJIE = 136,						-- 开服活动 -- 坐骑进阶
	LINGCHONG_JINJIE = 137,					-- 开服活动 -- 灵宠达人
	TODAY_RECHARGE = 138,					-- 开服活动 -- 今日充值
	GEM_INSET = 139,                        -- 开服活动 -- 宝石镶嵌
	CAPABILITY_RANK	= 140,		         	-- 开服活动 -- 战力排行
	WING_JINJIE = 156,                      -- 开服活动 -- 羽翼进阶
	-- 157 开服活动 -- 战斗坐骑进阶
	LINGCHONG_MOUNT_JINJIE = 162,                -- 开服活动 -- 灵骑进阶
	LINGCHONG_LINGYI_JINJIE = 163,                -- 开服活动 -- 灵骑进阶
	SEVENDAY_CHONGZHI = 164,					-- 开服活动 -- 七天充值
	SEVENDAY_CONSUME = 141,					-- 开服活动 -- 七天消费
	LOVERS = 142,					        -- 开服活动 -- 完美情人
	GUILDCONTEND = 143,                     -- 开服活动 -- 仙盟争霸
	ZITIE_EXCHANGE	= 144,		         	-- 开服活动 -- 字帖兑换
	FIRST_CHONGZHI_GROUP_BUY = 132,         -- 开服活动 -- 首充团购
	OPEN_INNEGLECTABLE = 159,               -- 开服活动 -- 开宗立派
	BOSS_HUNTER = 160,						-- 开服活动 -- boss猎人
	EVERYDAY_SHOP = 161,                    -- 开服活动 -- 一折抢购
	ZHEKOU_SHOP = 170,                    -- 开服活动 -- 折扣礼包
	QITIANLIANSHUO  = 145, 					-- 七天返利

	RAND_ACTIVITY_TYPE_CONVERT_CRAZY = 146,        -- 兑换狂欢
	RAND_ACTIVITY_TYPE_DROP_RATE_ADD = 147,	       -- 掉率增加
	RAND_ACTIVITY_TYPE_EXP_DOUBLE = 148,           -- 经验双倍
	RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP = 149,       -- 副本双倍掉落
	RAND_ACTIVITY_TYPE_CHONGZHI_YOULI = 151,		-- 充值有礼
	ACT_GOBAL_XUNBAO = 152,		-- 全民庆典

	RAND_TOTAL_CHONGZHI2 = 153, 			                --累计充值
	RAND_ACTIVITY_TYPE_TOTAL_CONSUME_GOLD2 = 154,		    --累计消费
	RAND_CONSUME_GOLD_RANK = 155,					-- 本服消费排行

	RAND_ACTIVITY_TYPE_XUNBAO_CARNIVAL = 158,		-- 寻宝狂欢

	OPEN_LIMIT_BUY = 165,		-- 开服 幸运云购
	RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2 = 171,	--充值回馈
	RAND_ACTIVITY_TYPE_TOTAL_CHONGZHI = 172,	--累计重置
	RAND_ACTIVITY_TYPE_CONTINUE_CONSUME = 173,	--连消特惠
	--RAND_ACTIVITY_TYPE_CHONGZHI_CONSUME = 174,	--连消特惠
	Rank_ACTIVITY_TYPE_XIAOFEICHONGBANG = 175,  --消费冲榜
	KF_BOSS_FIGHT = 176, --BOSS乱斗
	TotalConsume = 177, --累消返利
	Rank_ACTIVITY_TYPE_SUPERGIFTBAG = 178,  --超级礼包
	OPEN_SERVER_WAR = 179,  --开服激战
	RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG = 180, --F2周一运程
}

--活动同现时间类型
ACTIVITY_TIME_TYPE = {
	MONTH_CARD = 9000,					--月卡，购买后消失
	CHONGZHI_END_3 = 9001,				--充值3次后出现的g
	OPEN_SERVER_END_7 = 9002,			--7天后出现的
	DISCOUNT = 9003, 					--打折抢购
	TOUZIJIHUA_PLAN = 9004,				--投资计划
	LUCKY_ROLL = 9005,					--幸运拉霸
	WOYAO_GOLD_INGOT = 9006,			--我要元宝
	RAND_CHAOJI_VIP = 9007,				--超级vip
	RAND_EVALUATE = 9008,				--五星评价
	DOUBLE_AWARD = 9009,				--双倍活动
	TUPU_EXCHANGE = 9010,					--图谱兑换
	GUILD_RANK_XIANGMENGZHAN = 9011, 							--帮派争霸

	ALWAYS_ON = 10000,					--一直存在
}

--显示的活动分布配置，具体显示时根据条件进行组合显示
ShowSmallActCfg = {
	[ACTIVITY_TYPE.CLOSE_BETA] = {13, 14, 15, 16, 18, 19, 20, 24},								-- 封测活动
	[ACTIVITY_TYPE.OPEN_SERVER] = {141, 142, 143, 144, 145, 132, 156, 157, 159, 160, 162, 163, 164, 165, 179, 180}, 		-- 开服活动要显示的子活动
                                            --
	[ACTIVITY_TYPE.COMBINE_SERVER] = {															-- 合服活动
		ServerActClientId.CS_LOGIN_REWARD,													 --登录有礼
		ServerActClientId.CS_COLLECT_WORD,													 --集字有礼
		ServerActClientId.CS_TYPE_CONSUME,													 --消费有礼
		ServerActClientId.CS_TYPE_DOUBLE_EXP,												 --双倍经验
		ServerActClientId.CS_GUILD_BATTLE1,													 --帮派争霸1
		ServerActClientId.CS_GUILD_BATTLE2,													 --帮派争霸2
		ServerActClientId.CS_PANIC_BUY,														 --特惠秒杀
		ServerActClientId.CS_LIMIT_BUY,														 --限时云购
		ServerActClientId.CS_FB_DOUBLE,														 --副本双倍
		ServerActClientId.CS_RECHARGE_RANK,													 --充值排行
		ServerActClientId.CS_RECHARGE_CHOU,													 --充值抽抽
	},


	[ACTIVITY_TIME_TYPE.MONTH_CARD] = {9},						 								-- 普通活动要显示的子活动
	[ACTIVITY_TIME_TYPE.OPEN_SERVER_END_7] = {10},			 									-- 7天后要显示的子活动
	[ACTIVITY_TIME_TYPE.CHONGZHI_END_3] = {11},													-- 充值3次后要显示的子活动
	[ACTIVITY_TIME_TYPE.DISCOUNT] = {12},				 	 									--一折抢购活动
	[ACTIVITY_TIME_TYPE.TOUZIJIHUA_PLAN] = {27},												-- 投资计划
	[ACTIVITY_TIME_TYPE.LUCKY_ROLL] = {29},
	[ACTIVITY_TIME_TYPE.RAND_CHAOJI_VIP] = {94},												-- 超级vip
	[ACTIVITY_TIME_TYPE.RAND_EVALUATE] = {97},													-- 五星评价
	[ACTIVITY_TIME_TYPE.DOUBLE_AWARD] = {102},													-- 五星评价
	[ACTIVITY_TIME_TYPE.TUPU_EXCHANGE] = {103},													-- 图谱兑换
	[ACTIVITY_TIME_TYPE.GUILD_RANK_XIANGMENGZHAN] = {101},													-- 帮派争霸
	-- [ACTIVITY_TIME_TYPE.WOYAO_GOLD_INGOT] = {59},											-- 我要元宝

	--随机活动
	[ACTIVITY_TYPE.RAND_DAY_CHONGZHI_FANLI] = {ServerActClientId.RAND_DAY_CHONGZHI_FANLI},		--单日充值返利
	[ACTIVITY_TYPE.RAND_DAY_CONSUME_GOLD] = {ServerActClientId.RAND_DAY_CONSUME_GOLD},			--单日消费
	-- [ACTIVITY_TYPE.RAND_TOTAL_CONSUME_GOLD] = {ServerActClientId.RAND_TOTAL_CONSUME_GOLD},		--累计消费
	[ACTIVITY_TYPE.RAND_DAY_ACTIVIE_DEGREE] = {ServerActClientId.RAND_DAY_ACTIVIE_DEGREE},		--单日活跃奖励
	[ACTIVITY_TYPE.RAND_CHONGZHI_RANK] = {ServerActClientId.RAND_CHONGZHI_RANK},				--充值排行
	[ACTIVITY_TYPE.RAND_SERVER_PANIC_BUY] = {ServerActClientId.RAND_SERVER_PANIC_BUY},			--全服疯狂抢购O
	-- [ACTIVITY_TYPE.RAND_PERSONAL_PANIC_BUY] = {ServerActClientId.RAND_PERSONAL_PANIC_BUY},		--个人疯狂抢购O
	[ACTIVITY_TYPE.RAND_CONSUME_GOLD_FANLI] = {ServerActClientId.RAND_CONSUME_GOLD_FANLI},		--消费返利
	[ACTIVITY_TYPE.RAND_EQUIP_STRENGTHEN] = {ServerActClientId.RAND_EQUIP_STRENGTHEN},			--装备强化
	[ACTIVITY_TYPE.RAND_CHESTSHOP] = {ServerActClientId.RAND_CHESTSHOP},						--奇珍异宝
	[ACTIVITY_TYPE.RAND_STONE_UPLEVEL] = {ServerActClientId.RAND_STONE_UPLEVEL},				--宝石升级
	[ACTIVITY_TYPE.RAND_XN_CHANMIAN_UPLEVEL] = {ServerActClientId.RAND_XN_CHANMIAN_UPLEVEL},	--仙女缠绵
	[ACTIVITY_TYPE.RAND_MOUNT_JINJIE] = {ServerActClientId.RAND_MOUNT_UPGRADE},				--坐骑进阶
	[ACTIVITY_TYPE.RAND_QIBING_UPGRADE] = {ServerActClientId.RAND_QIBING_UPGRADE},				--骑兵升级
	[ACTIVITY_TYPE.RAND_MENTALITY_TOTAL_LEVEL] = {ServerActClientId.RAND_MENTALITY_TOTAL_LEVEL},--根骨全身等级
	[ACTIVITY_TYPE.RAND_WING_JINJIE] = {ServerActClientId.RAND_WING_UPGRADE},					--羽翼进化
	[ACTIVITY_TYPE.RAND_SHENWU_JINJIE] = {ServerActClientId.RAND_SHENWU_UPGRADE},					--神武进阶
	[ACTIVITY_TYPE.RAND_FABAO_JINJIE] = {ServerActClientId.RAND_FABAO_UPGRADE},					--法宝进阶
	[ACTIVITY_TYPE.RAND_LINGCHONG_JINJIE] = {ServerActClientId.RAND_LINGCHONG_UPGRADE},					--灵宠进阶
	[ACTIVITY_TYPE.RAND_LINGGONG_JINJIE] = {ServerActClientId.RAND_LINGGONG_UPGRADE},					--灵弓进阶
	[ACTIVITY_TYPE.RAND_LINGQI_JINJIE] = {ServerActClientId.RAND_LINGQI_UPGRADE},					--灵骑进阶
	[ACTIVITY_TYPE.RAND_LINGYI_JINJIE] = {ServerActClientId.RAND_LINGYI_UPGRADE},					--灵翼进阶

	[ACTIVITY_TYPE.RAND_LOTTERY_DRAW] = {ServerActClientId.RAND_LOTTERY_DRAW},					--幸运抽奖

	[ACTIVITY_TYPE.RAND_MOUNT_CHARGE] = {ServerActClientId.RAND_MOUNT_CHARGE},                                                          -- 坐骑充值
	[ACTIVITY_TYPE.RAND_WING_CHARGE] = {ServerActClientId.RAND_WING_CHARGE},                                                                  -- 羽翼充值
	[ACTIVITY_TYPE.RAND_FABAO_CHARGE] = {ServerActClientId.RAND_FABAO_CHARGE},                                                          -- 法宝充值
	[ACTIVITY_TYPE.RAND_SHENWU_CHARGE] = {ServerActClientId.RAND_SHENWU_CHARGE},                                                  -- 神武充值
	[ACTIVITY_TYPE.RAND_LINGCHONG_CHARGE] = {ServerActClientId.RAND_LINGCHONG_CHARGE},                                -- 灵宠充值
	[ACTIVITY_TYPE.RAND_LINGQI_CHARGE] = {ServerActClientId.RAND_LINGQI_CHARGE},                                                  -- 灵骑充值
	[ACTIVITY_TYPE.RAND_LINGGONG_CHARGE] = {ServerActClientId.RAND_LINGGONG_CHARGE},                                        -- 灵弓充值
	[ACTIVITY_TYPE.RAND_LINGYI_CHARGE] = {ServerActClientId.RAND_LINGYI_CHARGE},                                                  -- 灵翼充值

	[ACTIVITY_TYPE.RAND_MOUNT_SHOP] = {ServerActClientId.RAND_MOUNT_SHOP},											-- 坐骑抢购
	[ACTIVITY_TYPE.RAND_WING_SHOP] = {ServerActClientId.RAND_WING_SHOP},											-- 羽翼抢购
	[ACTIVITY_TYPE.RAND_FABAO_SHOP] = {ServerActClientId.RAND_FABAO_SHOP},										-- 法宝抢购
	[ACTIVITY_TYPE.RAND_SHENWU_SHOP] = {ServerActClientId.RAND_SHENWU_SHOP},									-- 神武抢购
	[ACTIVITY_TYPE.RAND_LINGCHONG_SHOP] = {ServerActClientId.RAND_LINGCHONG_SHOP},									-- 灵宠抢购
	[ACTIVITY_TYPE.RAND_LINGQI_SHOP] = {ServerActClientId.RAND_LINGQI_SHOP},									-- 灵骑抢购
	[ACTIVITY_TYPE.RAND_LINGGONG_SHOP] = {ServerActClientId.RAND_LINGGONG_SHOP},											-- 灵弓抢购
	[ACTIVITY_TYPE.RAND_LINGYI_SHOP] = {ServerActClientId.RAND_LINGYI_SHOP},											-- 灵翼抢购

	[ACTIVITY_TYPE.RAND_QUANMIN_QIFU] = {ServerActClientId.RAND_QUANMIN_QIFU},					-- 全民祈福
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XIANLING_ZHEN] = {ServerActClientId.RAND_ACTIVITY_TYPE_XIANLING_ZHEN},			-- F2仙灵古阵
	[ACTIVITY_TYPE.RAND_XIANMENG_JUEQI] = {ServerActClientId.RAND_XIANMENG_JUEQI},				-- 仙盟崛起
	[ACTIVITY_TYPE.RAND_XIANMENG_BIPIN] = {ServerActClientId.RAND_XIANMENG_BIPIN},				-- 仙盟比拼
	[ACTIVITY_TYPE.RAND_DAY_ONLINE_GIFT] = {ServerActClientId.RAND_DAY_ONLINE_GIFT},			-- 每日在线好礼
	[ACTIVITY_TYPE.RAND_DOUFA_KUANGHUAN] = {ServerActClientId.RAND_DOUFA_KUANGHUAN},			-- 斗法狂欢
	[ACTIVITY_TYPE.RAND_ZHANCHANG_FANBEI] = {ServerActClientId.RAND_ZHANCHANG_FANBEI},			-- 战场翻倍
	[ACTIVITY_TYPE.RAND_CHARGE_REPALMENT] = {ServerActClientId.RAND_CHARGE_REPALMENT},			-- 充值回馈
	[ACTIVITY_TYPE.RAND_SINGLE_CHARGE] = {ServerActClientId.RAND_SINGLE_CHARGE},				-- 单笔充值

	[ACTIVITY_TYPE.RAND_CHONGZHI_DOUBLE] = {ServerActClientId.RAND_CHONGZHI_DOUBLE},			-- 双倍充值
	[ACTIVITY_TYPE.RAND_DAY_DANBI_CHONGZHI] = {ServerActClientId.RAND_DAY_DANBI_CHONGZHI},		-- 每日单笔充值
	[ACTIVITY_TYPE.RAND_TOMORROW_REWARD] = {ServerActClientId.RAND_TOMORROW_REWARD},			-- 次日福利
	[ACTIVITY_TYPE.RAND_TOTAL_CHARGE_DAY] = {ServerActClientId.RAND_TOTAL_CHARGE_DAY},			-- 随机活动每日累充
	[ACTIVITY_TYPE.RAND_SEVEN_DOUBLE] = {ServerActClientId.RAND_SEVEN_DOUBLE},					-- 随机活动七日双倍
	-- [ACTIVITY_TYPE.RAND_TOTAL_CHONGZHI] = {ServerActClientId.RAND_TOTAL_CHONGZHI},				-- 随机活动累计充值排行
	[ACTIVITY_TYPE.RAND_DOUBLE_XUNBAO_JIFEN] = {ServerActClientId.RAND_XUNBAO_JIFEN},			-- 随机活动双倍寻宝积分
	[ACTIVITY_TYPE.RAND_EQUIP_EXCHANGE] = {ServerActClientId.RAND_EQUIP_EXCHANGE},				-- 随机活动装备积分兑换
	[ACTIVITY_TYPE.RAND_SPRITE_EXCHANGE] = {ServerActClientId.RAND_SPRITE_EXCHANGE},			-- 随机活动精灵积分兑换
	[ACTIVITY_TYPE.RAND_DAILY_ONLINE_LOTTERY] = {ServerActClientId.RAND_DAILY_ONLINE_LOTTERY},	-- 随机活动在线时间领取奖励
	--[ACTIVITY_TYPE.RAND_TIMEBOSS] = {ServerActClientId.RAND_TIMEBOSS},							-- 随机活动定时BOSS
	--[ACTIVITY_TYPE.RAND_MONSTER_DROP] = {ServerActClientId.RAND_MONSTER_DROP},					-- 随机活动打怪掉落
	--[ACTIVITY_TYPE.RAND_ZHANCHANG_ZHENGBA] = {ServerActClientId.RAND_ZHANCHANG_ZHENGBA},		-- 随机活动战场争霸

	[ACTIVITY_TYPE.RAND_ACTIVITY_ZHENGGUZJ] = {ServerActClientId.RAND_ACTIVITY_ZHENGGUZJ},		-- 随机活动整蛊专家
	[ACTIVITY_TYPE.RAND_ACTIVITY_BEIZHENGDAREN] = {ServerActClientId.RAND_ACTIVITY_BEIZHENGDAREN},	-- 随机活动被整达人
	[ACTIVITY_TYPE.RAND_COLLECT_ITEMS] = {ServerActClientId.COLLECT_ITEMS},						-- 随机活动集字活动

	[ACTIVITY_TYPE.DOUBLE_AWARD] = {ServerActClientId.DOUBLE_AWARD},						-- 双倍活动

	[ACTIVITY_TIME_TYPE.ALWAYS_ON] = {ServerActClientId.ALWAYS_CONTACT_GM},	-- gm有礼

	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONVERT_CRAZY] = {ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY},	-- 兑换狂欢
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_DROP_RATE_ADD] = {ServerActClientId.RAND_ACTIVITY_TYPE_DROP_RATE_ADD},	-- 掉率增加
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE] = {ServerActClientId.RAND_ACTIVITY_TYPE_EXP_DOUBLE},	        -- 经验双倍
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP] = {ServerActClientId.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP},	-- 副本双倍掉落
	[ACTIVITY_TYPE.RAND_KILL_BOSS] = {ServerActClientId.RAND_KILL_BOSS},						                -- 掉落狂欢
	[ACTIVITY_TYPE.RAND_LOGIN_GIFT] = {ServerActClientId.RAND_LOGIN_GIFT},					                	-- 登录奖励
	[ACTIVITY_TYPE.RAND_WEEKEND_BOSS] = {ServerActClientId.RAND_WEEKEND_BOSS},					               	-- 周末BOSS
	[ACTIVITY_TYPE.RAND_TOTAL_CHONGZHI2] = {ServerActClientId.RAND_TOTAL_CHONGZHI2},					        -- 累计充值
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_TOTAL_CONSUME_GOLD2] = {ServerActClientId.RAND_ACTIVITY_TYPE_TOTAL_CONSUME_GOLD2}, -- 累计消费
	[ACTIVITY_TYPE.RAND_CONSUME_GOLD_RANK] = {ServerActClientId.RAND_CONSUME_GOLD_RANK},					        -- 消费排行
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_YOULI] = {ServerActClientId.RAND_ACTIVITY_TYPE_CHONGZHI_YOULI},		-- 充值有礼
	[ACTIVITY_TYPE.ACT_GOBAL_XUNBAO] = {ServerActClientId.ACT_GOBAL_XUNBAO},		-- 全民庆典
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XUNBAO_CARNIVAL] = {ServerActClientId.RAND_ACTIVITY_TYPE_XUNBAO_CARNIVAL},		-- 寻宝狂欢
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_TOTAL_CHONGZHI] = {ServerActClientId.RAND_ACTIVITY_TYPE_TOTAL_CHONGZHI},		-- 累计重置
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME] = {ServerActClientId.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME},		-- 连消特惠
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2] = {ServerActClientId.RAND_ACTIVITY_TYPE_CHARGE_REPAYMENT2},	-- 充值回馈
	--[ACTIVITY_TYPE.RAND_CONTINUE_CHONGZHI] = {ServerActClientId.RAND_ACTIVITY_TYPE_CHONGZHI_CONSUME},	-- 连续充值
	[ACTIVITY_TYPE.KF_BOSS_FIGHT] = {ServerActClientId.KF_BOSS_FIGHT},	-- BOSS乱斗
	[ACTIVITY_TYPE.TotalConsume] = {ServerActClientId.TotalConsume},	-- 累消返利
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SUPERGIFTBAG] = {ServerActClientId.Rank_ACTIVITY_TYPE_SUPERGIFTBAG},	-- 累消返利
	[ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG] = {ServerActClientId.RAND_ACTIVITY_TYPE_ZHOUYI_YUNCHENG},			-- F2仙灵古阵

}

ShowSmallActCfg2 = {
		[ACTIVITY_TYPE.OPEN_SERVER] = {135,136,137,138,139,140,156}, 		--开服比拼活动要显示的子活动
}

RollMoneyEnum = {
	ROLL_MONEY_OPERA_TYPE_QUERY = 0,
	ROLL_MONEY_OPERA_TYPE_ROLL_GOLD = 1,
	ROLL_MONEY_OPERA_TYPE_ROLL_COIN = 2,
	ROLL_MONEY_OPERA_TYPE_FETCH_GOLD_REWARD = 3,
	ROLL_MONEY_OPERA_TYPE_FETCH_COIN_REWARD = 4,
}

RUSH_TYPE = {
	RUSH_TYPE_LEVEL = 1,      -- 冲级达人
	RUSH_TYPE_MOUNT = 3,      -- 坐骑比拼
	RUSH_TYPE_LINGCHONG = 2,  -- 灵宠比拼
	-- 8 战斗坐骑阶数（废弃）
	RUSH_TYPE_LINGCHONG_MOUNT = 9,--灵骑进阶
	RUSH_TYPE_LINGCHONG_LINGYI = 10,--灵翼进阶
	RUSH_TYPE_RECHARGE = 4,   -- 今日充值
	RUSH_TYPE_GEM = 6,        -- 宝石镶嵌
	RUSH_TYPE_CAPABILITY = 7, -- 战力排行
	RUSH_TYPE_XIAOFEICHONGBANG = 1,    --消费冲榜
	RUSH_TYPE_WING = 5,	      -- 羽翼阶数
	RUSH_TYPE_BEAST = 11,	      -- 幻兽战力
	RUSH_TYPE_SHENBING = 12,	      -- 神兵战力
	RUSH_TYPE_CULTIVATION = 13,	      -- 修为等级
	RUSH_TYPE_TIANSHEN = 14,	      -- 神灵等级
	RUSH_TYPE_GOLD = 15,	      -- 灵玉消费
	RUSH_TYPE_SHENSHOU = 16,	      -- 异兽战力

	RUSH_TYPE_TOTAL_CONSUME = 20, -- 累计消费
	RUSH_TYPE_LOVERS = 21,       -- 完美情人
	RUSH_TYPE_GUILD_CONTEND = 22,-- 仙盟争霸
	RUSH_TYPE_EXCHANGE = 23,     -- 字帖兑换
	RUSH_TYPE_HONGBAO = 24,		 --七天消费返利
	RUSH_TYPE_CREATE_GUILD = 25, -- 开宗立派
	RUSH_TYPE_DAILY_LIMIT_BUY = 26, --每日限购
	RUSH_TYPE_BOSS_HUNTER = 27,  -- BOSS猎人
	RUSH_TYPE_TOTAL_RECHARGE = 28, -- 累计充值
	RUSH_TYPE_OPEN_LIMIT_BUY = 29, -- 幸运云购
	RUSH_TYPE_OPEN_ZHEKOU = 30,    -- 折扣礼包
	RUSH_TYPE_OPEN_WAR = 31,    -- 开服激战
}

ACT_RUSH_TYPE = {
	[ServerActClientId.SPRINT_LEVEL] = RUSH_TYPE.RUSH_TYPE_LEVEL,
	[ServerActClientId.MOUNT_JINJIE] = RUSH_TYPE.RUSH_TYPE_MOUNT,
	[ServerActClientId.LINGCHONG_JINJIE] = RUSH_TYPE.RUSH_TYPE_LINGCHONG,
	[ServerActClientId.TODAY_RECHARGE] = RUSH_TYPE.RUSH_TYPE_RECHARGE,
	[ServerActClientId.GEM_INSET] = RUSH_TYPE.RUSH_TYPE_GEM,
	[ServerActClientId.CAPABILITY_RANK] = RUSH_TYPE.RUSH_TYPE_CAPABILITY,
	[ServerActClientId.WING_JINJIE] = RUSH_TYPE.RUSH_TYPE_WING,
	[ServerActClientId.LINGCHONG_LINGYI_JINJIE] = RUSH_TYPE.RUSH_TYPE_LINGCHONG_LINGYI,
	[ServerActClientId.LINGCHONG_MOUNT_JINJIE] = RUSH_TYPE.RUSH_TYPE_LINGCHONG_MOUNT,
	-- [ServerActClientId.LINGCHONG_LINGYI_JINJIE] = RUSH_TYPE.RUSH_TYPE_LINGCHONG_LINGYI,
	--[ServerActClientId.SEVENDAY_CHONGZHI] = RUSH_TYPE.RUSH_TYPE_TOTAL_RECHARGE,
	[ServerActClientId.SEVENDAY_CONSUME] = RUSH_TYPE.RUSH_TYPE_TOTAL_CONSUME,
	[ServerActClientId.LOVERS] = RUSH_TYPE.RUSH_TYPE_LOVERS,
	[ServerActClientId.GUILDCONTEND] = RUSH_TYPE.RUSH_TYPE_GUILD_CONTEND,
	[ServerActClientId.ZITIE_EXCHANGE] = RUSH_TYPE.RUSH_TYPE_EXCHANGE,
	[ServerActClientId.FIRST_CHONGZHI_GROUP_BUY] = RUSH_TYPE.RUSH_TYPE_HONGBAO,

	[ServerActClientId.OPEN_INNEGLECTABLE] = RUSH_TYPE.RUSH_TYPE_CREATE_GUILD,
	[ServerActClientId.BOSS_HUNTER] = RUSH_TYPE.RUSH_TYPE_BOSS_HUNTER,
	[ServerActClientId.EVERYDAY_SHOP] = RUSH_TYPE.RUSH_TYPE_DAILY_LIMIT_BUY,
	[ServerActClientId.OPEN_LIMIT_BUY] = RUSH_TYPE.RUSH_TYPE_OPEN_LIMIT_BUY,
	[ServerActClientId.ZHEKOU_SHOP] = RUSH_TYPE.RUSH_TYPE_OPEN_ZHEKOU,
	[ServerActClientId.OPEN_SERVER_WAR] = RUSH_TYPE.RUSH_TYPE_OPEN_WAR,
	[ServerActClientId.Rank_ACTIVITY_TYPE_XIAOFEICHONGBANG] = RUSH_TYPE.RUSH_TYPE_XIAOFEICHONGBANG,
}

KAIZONGLIPAI_TARGET_TYPE = {
	creat_guid = 1,
	guild_role_num = 2,
	guild_level = 3,
	guild_fubangzhu_num = 4,
	vip_role_num = 5,
}

local SpecCrazySeq = 11 		--特殊处理 策划需求

OGA_RUSH_TYPE_RANK_MAX_COUNT = 20  		-- 开服活动-冲榜达人类型数目
OGA_RUSH_RANK_DAILY_REWARD_COUNT = 15 	-- 开服活动-冲榜达人某类型里的每日礼包档位数目

ServerActivityWGData = ServerActivityWGData or BaseClass()

function ServerActivityWGData:__init()
	if ServerActivityWGData.Instance ~= nil then
		ErrorLog("[ServerActivityWGData] attempt to create singleton twice!")
		return
	end
	ServerActivityWGData.Instance = self
	self.supergiftviewopen_num = 50000 --超级充值阈值，超过此阈值则超级充值界面不再弹出
	self.total_login_day = 0

	self.opening_type_list = {}
	self.opening_type2_list ={}
	self.cache_act_reward_list = {}
	self.close_act_chache = {}
	self.is_open_openserver_act = false --是否开启开服活动
	self.openserver_act_red = true

	self.opengameactivity_auto = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto")
	self.bipinjumpicon_list = ListToMapList(self.opengameactivity_auto.icon_jump, "rush_type")
	self.bipin_other = self.opengameactivity_auto.other
	self.openserver_act_map = ListToMap(self.opengameactivity_auto.openserver_act_order, "actid")					  -- 开服活动排序表
	self.openserver_act_order_rd = -1				  																  -- 开服活动序号
	self.shouchongxuchongcfg_auto = ConfigManager.Instance:GetAutoConfig("shouchongxuchongcfg_auto")
	self.shouchong_anim_cfg = ListToMap(self.shouchongxuchongcfg_auto.animation, "grade", "day")
	self.shouchong_reward_cfg_list = {}
	self.shouchong_special_reward_cfg_list = {}
	self.shouchong_type_list = {}

	self.daily_leichong_auto = ConfigManager.Instance:GetAutoConfig("daily_leichong_auto")
	self.leichong_cfg = ListToMapList(self.daily_leichong_auto.leichong_cfg, "open_server_day")  --每日累充
    self.leiji_reward = ListToMapList(self.daily_leichong_auto.leiji_reward, "cycle")
	self.daily_gift = ListToMapList(self.daily_leichong_auto.daily_gift, "grade")
	self.rapidly_gift_cfg = self.daily_leichong_auto.rapidly_gift						--秒杀礼包.

	local cfg_list = ConfigManager.Instance:GetAutoConfig("randact_buddhabag_auto")		-- 灵妖卖货
	self.lingyaomaihuo_reward_cfg = cfg_list.item

	self.daily_xiaofei_auto = ConfigManager.Instance:GetAutoConfig("daily_xiaofei_auto")
	self.xiaofei_cfg = ListToMapList(self.daily_xiaofei_auto.xiaofei_cfg, "open_server_day")  --每日累充

	self.show_act2_cfg = __TableCopy(ShowSmallActCfg2)
	self.show_act_cfg = __TableCopy(ShowSmallActCfg)
	if IS_AUDIT_VERSION or IS_FREE_VERSION then
		self.show_act_cfg[ACTIVITY_TIME_TYPE.TOUZIJIHUA_PLAN] = nil
		self.show_act_cfg[ACTIVITY_TIME_TYPE.MONTH_CARD] = nil
	end
	self.finish_act_cache = {}
	self.finish_act2_cache = {}
	self.month_card_info = {}
	self.chong_zhi_info = {}
	self.roll_money_info = {}
	self.rush_limit_time_buy_data_list = {}
	self.rush_target_reward_list = {}
	self.rush_target_value_list = {}
	self.bipin_bind_gift_is_remind = {}

	self.invest_plan_info = {}							-- 投资计划
	self.celebrity_title_info = {}						-- 名人争夺

	self.chongzhi_wantmoney_info = {					-- 我要元宝(变元宝)
		reward_state = 0,
		history_chongzhi = 0,
		get_gold_bind = 0,
	}

	self.act_rank_list = {} --排名雷列表缓存
	self.active_flag_state = {}  --投资计划的三个档次激活状态
	self.now_reward_index = nil

	self.remind_list_num = {}

	self.iwg_info = {									--聚宝盆信息
		reward_lun = 0,
		history_chongzhi = 0,
		bet_list = {},
	}

	self.tomorrow_reward = {}							--次日福利信息

	self.tupu_times = {}							--活动兑换次数
	self.boss_hunter_info = {}                      --boss猎人
	-- self:AddActOpeningType(ACTIVITY_TIME_TYPE.ALWAYS_ON)
	self.is_first_open_chongzhi_view = false
	self.rand_act_zhuanfu_type = 1

	self.total_charge_value = 0
	self.reward_has_fetch_flag = 0
	self.marry_type = -1
	self.weekend_boss_id = 0 							--周末BOSS ID

	self.counucopia_is_open_view = 1
	self.active_flag = {}
	self.reward_flag = {}

	self.dailyonlinelottery = {
		dailyonlinelottery_online_time = 0,
		dailyonlinelottery_fetch_times = 0,
	}
	self.online_lottery_hit_seq = 0 					--随机活动-每日在线抽奖结果
	self.zhanchangzhengba_info = {						--随机活动-战场争霸信息
		qunxianluandou_first = 0,
		gongchengzhan_first = 0,
		xianmengzhan_first = 0,
		role_id = {},
		begintime = {},
	}
	self.gobal_xunbao = {
		commit_times = 0,
		fetch_flag = 0,
		server_commit_times = 0,
	}
	self.scene_item_list = {}
	self.init_openserver_act_list = {} 					-- 随机活动开服期间开启列表

	self.hight_rebate_list = {}                         -- 随机活动-高倍返利

	local tmp = ConfigManager.Instance:GetAutoConfig("randactivityopencfg_auto")
	for k,v in pairs(tmp.client_open_cfg) do
		self.init_openserver_act_list[v.activity_type] = v.sevenday_open
	end

	self.open_game_info = {}                          -- 开服活动信息
	self.login_remind = true                          -- 登录开服活动提醒

	self.login_lovers_remind = true                   -- 开服活动完美情人提醒

	--版本活动

	self.verson_act_status = {}

	-- 折扣礼包
	self.zhekou_info = {}

	self.sub_act_status = {}

	-- 周末boss
	self.is_open_weekend_boss = false
	self.wb_next_status_time = 0
	self.weekend_boss_status_time = {}
	self.weekend_boss_status = ACTIVITY_STATUS.CLOSE

	self.sevenday_data = {}

	self.verson_activity_info = {}

	self.first_login = true

	self.open_server_competition_click_remind = {}

	self.csa_panic_buy_times_list = {}
	self.batch = 0

	self.everyday_reward_list = {}
	self.everyday_reward_list.everyday_recharge_num = 0
	self.everyday_reward_list.everyday_cycle_num = 0
	self.everyday_reward_list.everyday_reward_flag = bit:d2b_two(0)
	self.everyday_reward_list.everyday_get_reward_flag = bit:d2b_two(0)
	self.everyday_reward_list.leichong_reward_flag = bit:d2b_two(0)
	self.everyday_reward_list.leichong_get_reward_flag = bit:d2b_two(0)

	self.everyday_gift_list = {}

	self.everyday_expense_list = 
	{
		daily_consume_gold = 0, -- 每日消费总仙玉
		daily_consume_reward_record = {}, --奖励领取
	}
	-- self.everyday_expense_list.daily_consume_gold = 0 -- 每日消费总仙玉
	-- self.everyday_expense_list.daily_consume_reward_record = bit:d2b_two(0) --奖励领取
	self.everyday_expense_seq = nil

	self.everyday_rapidly_gift_info = {}				--每日累充-秒杀礼包信息.
	self.everyday_rapidly_gift_record_info = {}			--每日累充-秒杀礼包记录信息.

	self:InitActBiPinCfg()

	self:InitOpenServerCompetitonInfo()

	RemindManager.Instance:Register(RemindName.Fireworks,BindTool.Bind(self.GetActYanHuaRemind, self))
	RemindManager.Instance:Register(RemindName.FirstCharge_Tab_ShouChong, BindTool.Bind(self.GetSCXCRemind, self))
	RemindManager.Instance:Register(RemindName.DailyRecharge_LeiChong, BindTool.Bind(self.IsShowDailyRed, self))
	RemindManager.Instance:Register(RemindName.DailyRecharge_XiaoFei, BindTool.Bind(self.IsShowExpenseRed, self))
	RemindManager.Instance:Register(RemindName.DailyRecharge_Libao, BindTool.Bind(self.IsShowDailyLiBaoRed, self))
	RemindManager.Instance:Register(RemindName.DailyRecharge_Rapidly, BindTool.Bind(self.IsShowEveryDayRapidlyGiftRed, self))

	RemindManager.Instance:Register(RemindName.OpenServer, BindTool.Bind(self.GetOpenServerRedRemind, self))
	RemindManager.Instance:Register(RemindName.KFActivity, BindTool.Bind(self.GetKFActivityRedRemind, self))
	RemindManager.Instance:Register(RemindName.KFCompetition, BindTool.Bind(self.GetOpenserverCommpetionRed, self))
	RemindManager.Instance:Register(RemindName.KFSevendayRecharge, BindTool.Bind(self.GetTotalChongzhiRemind, self))
	RemindManager.Instance:Register(RemindName.KFXianMengFengBang, BindTool.Bind(self.GetKaiZongLiPaiRemind, self))
	RemindManager.Instance:Register(RemindName.KFKaiZongLiPai, BindTool.Bind(self.GetCreateGuildRemind, self))
	-- RemindManager.Instance:Register(RemindName.KFXianMengZhengBa, BindTool.Bind(self.GetGuildContentRemind, self))
	RemindManager.Instance:Register(RemindName.KaiFuJiZiRed, BindTool.Bind(self.RemindKiFuJiZiRed, self))
	RemindManager.Instance:Register(RemindName.KaiFuGiftRed, BindTool.Bind(self.GetActKfGiftRemind, self))
	self:RegisterKiFuJiZiRemindInBag(RemindName.KaiFuJiZiRed)
end

function ServerActivityWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.Fireworks)
	RemindManager.Instance:UnRegister(RemindName.FirstCharge_Tab_ShouChong)
	RemindManager.Instance:UnRegister(RemindName.KaiFuJiZiRed)
	RemindManager.Instance:UnRegister(RemindName.DailyRecharge_LeiChong)
	RemindManager.Instance:UnRegister(RemindName.DailyRecharge_XiaoFei)
	RemindManager.Instance:UnRegister(RemindName.DailyRecharge_Libao)
	RemindManager.Instance:UnRegister(RemindName.DailyRecharge_Rapidly)

	RemindManager.Instance:UnRegister(RemindName.OpenServer)
	RemindManager.Instance:UnRegister(RemindName.KFActivity)
	RemindManager.Instance:UnRegister(RemindName.KFCompetition)
	RemindManager.Instance:UnRegister(RemindName.KFSevendayRecharge)
	RemindManager.Instance:UnRegister(RemindName.KFXianMengFengBang)
	RemindManager.Instance:UnRegister(RemindName.KFKaiZongLiPai)
	RemindManager.Instance:UnRegister(RemindName.KFXianMengZhengBa)

	if self.first_recharge_open_check then
		FunOpen.Instance:UnNotifyFunOpen(self.first_recharge_open_check)
	end

	ServerActivityWGData.Instance = nil
	self.first_login = true
end

function ServerActivityWGData:RegisterKiFuJiZiRemindInBag(remind_name)
    local map = {}
    local word_exchange = self:GetOpenGameActivityConfig().word_exchange
    for _, v in pairs(word_exchange) do
    	for i = 1, 5 do
    		map[v["item_id_" .. i]] = true
    	end
    end
    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function ServerActivityWGData:GetIndexByItemId(item_id)
	if nil == item_id or item_id == 0 then
		return 0
	end
	local list = self.opengameactivity_auto.other[1].word_list
	local items = Split(list, ',')
	for index, value in ipairs(items) do
		if tonumber(value) == item_id then
			return index
		end
	end
	return 0
end

function ServerActivityWGData:GetContinuwConsumeViewRemind()
	if ContinuousXiaoFeiWGData.Instance:GetRemind() then
		return 1
	end
	return 0
end

function ServerActivityWGData:GetBiPinSuitSeq()
	for k,v in pairs(self.bipin_other) do
		return v.seq
	end
	
	return 0
end

function ServerActivityWGData:GetBiPinOtherCfg()
	return self.bipin_other
end

--缓存结束的活动
function ServerActivityWGData:SetActFinish(act_id)
	local view_isopen = ServerActivityWGCtrl.Instance:GetViewIsOpen()
	if not view_isopen then
		for k, v in pairs(self.show_act_cfg) do
			for key, value in pairs(v) do
				if value == act_id then
					v[key] = nil
				end
			end
		end
	else
		self.finish_act_cache[act_id] = act_id
	end
end

function ServerActivityWGData.IsNormalAct(act_type)
	if ACTIVITY_TYPE.OPEN_SERVER == act_type or 1 == ServerActivityWGData.Instance.init_openserver_act_list[act_type] then
		return true
	end
	if ACTIVITY_TYPE.COMBINE_SERVER == act_type then
		return true
	end
	for k,v in pairs(ACTIVITY_TIME_TYPE) do
		if v == act_type then
			return true
		end
	end
	return false
end

function ServerActivityWGData.IsNormalActId(act_id)
	if 1 == ServerActivityWGData.Instance.init_openserver_act_list[ServerActivityWGData.Instance:GetActTimeType(act_id)] then
		return true
	end

	for k,v in pairs(ShowSmallActCfg[ACTIVITY_TYPE.OPEN_SERVER]) do
		if act_id == v then
			return true
		end
	end
	for k,v in pairs(ACTIVITY_TIME_TYPE) do
		if ShowSmallActCfg[v] then
			for k1,v1 in pairs(ShowSmallActCfg[v]) do
				if act_id == v1 then
					return true
				end
			end
		end
	end
	return false
end

--清理结束的活动
function ServerActivityWGData:CleanUpFinishAct()
	local count = 0
	for kk, vv in pairs(self.finish_act_cache) do
		local finish_id = vv
		for k, v in pairs(self.show_act_cfg) do
			for key, value in pairs(v) do
				if value == finish_id then
					v[key] = nil
					count = count + 1
				end
			end
		end
	end
	self.finish_act_cache = {}
	if count > 0 then
		ServerActivityWGCtrl.Instance:RefreshViewButtonList()
	end
end
function ServerActivityWGData:CleanUpTwoFinishAct()
	local count = 0
	for kk, vv in pairs(self.finish_act_cache) do
		local finish_id = vv
		for k, v in pairs(self.show_act2_cfg) do
			for key, value in pairs(v) do
				if value == finish_id then
					v[key] = nil
					count = count + 1
				end
			end
		end
	end
	self.finish_act_cache = {}
	if count > 0 then
		ServerActivityWGCtrl.Instance:RefreshViewButtonList()
	end
end

--添加一个开启的活动
function ServerActivityWGData:AddActOpeningType(open_type)
	self.opening_type_list[open_type] = open_type
	FunOpen.Instance:ForceOpenFunByName(FunName.ActvityIcon)
		if nil == self.show_act_cfg[open_type] then
			local new_show_cfg = ShowSmallActCfg[open_type]
			self.show_act_cfg[open_type] = new_show_cfg
		end
		if nil == self.show_act2_cfg[open_type] then
			local new_show_cfg2 = ShowSmallActCfg2[open_type]
			self.show_act2_cfg[open_type] = new_show_cfg2
		end
		--活动重开，关闭的活动不清理了
		if nil ~= self.close_act_chache[open_type] then
			self.close_act_chache[open_type] = nil
		end
		--活动重开，结束活动不需要清理了
		for k, v in pairs(ShowSmallActCfg[open_type]) do
			if nil ~= self.finish_act_cache[v] then
				self.finish_act_cache[v] = nil
			end
		end
end

--删除一个活动，先缓存，后清理
function ServerActivityWGData:DeleteActOpeningType(open_type)
	if self.opening_type_list[open_type] then
		-- if (not self.IsNormalAct(open_type) and false == ServerActivityWGCtrl.Instance.server_activity_view:IsOpen()) or
		if (self.IsNormalAct(open_type) and false == ServerActivityWGCtrl.Instance.act_openserver_view:IsOpen()) then
			self.opening_type_list[open_type] = nil
		else
			self.close_act_chache[open_type] = open_type
		end
	end
end

function ServerActivityWGData:SetIsOpenOpenserverAct(value)
	self.is_open_openserver_act = value
end

function ServerActivityWGData:GetIsOpenOpenserverAct()
	if not ActivityWGCtrl.Instance:IsOpenServerOpen() then
		self.is_open_openserver_act = false
	end
	return self.is_open_openserver_act
end

function ServerActivityWGData:SetLuckyBuyClickRemind(index)
	self.lucky_buy_click_remind = index
end

function ServerActivityWGData:GetLuckyBuyClickRemind()
	return self.lucky_buy_click_remind or 0
end


function ServerActivityWGData:GetServerActivityIsOpen(open_type)
	if nil ~= self.opening_type_list[open_type] and nil == self.close_act_chache[open_type] and nil == self.finish_act_cache[open_type] then
		return true
	else
		return false
	end
end

--清理删除的活动
function ServerActivityWGData:CleanActCloseType()
	local close_count = 0
	for k, v in pairs(self.close_act_chache) do
		if self.opening_type_list[v] then
			self.opening_type_list[v] = nil
			close_count = close_count + 1
		end
	end
	if close_count > 0 then
		ServerActivityWGCtrl.Instance:RefreshViewButtonList()
	end
	self.close_act_chache = {}

	local count = 0
	for k, v in pairs(self.opening_type_list) do
		count = count + 1
	end
	if count < 1 then
		FunOpen.Instance:ForceCloseFunByName(FunName.ActvityIcon)
	end
end

--获得客户端自定义的活动配置列表
function ServerActivityWGData:GetClientActList()
	return ConfigManager.Instance:GetAutoConfig("client_activity_auto").client_activity_list
end

--获得活动的客户端配置
function ServerActivityWGData:GetClientActCfg(client_act_id)
	local list = self:GetClientActList()
	return list[client_act_id]
end

--获得活动的客户端配置
function ServerActivityWGData:GetClientActCfgByOpenType(open_type)
	local list = self:GetClientActList()

	for k,v in pairs(list) do
		if v.open_type == open_type then
			return v
		end
	end
end

--获得活动所在的时间类型，如在充值3次后等
function ServerActivityWGData:GetActTimeType(act_id)
	for k,v in pairs(self.show_act_cfg) do
		for _,id in pairs(v) do
			if id == act_id then
				return k
			end
		end
	end
	return -1
end

--获取所有显示的活动的配置表
function ServerActivityWGData:GetShowActConfig()
	return self.show_act_cfg
end

--获得当前开启的活动列表，一般都要排序
function ServerActivityWGData:GetShowOpenActList(is_sort, is_normal)  -- 是否排序, 是否是普通活动
	is_normal = is_normal or false
	local show_act_cfg_list = {}
	local temp_act_id_dic = {} --避免内容重复
	local list = nil
	for k,v in pairs(self.opening_type_list) do
		list = self.show_act_cfg[v]
		if list ~= nil then
			for _,id in pairs(list) do
				if temp_act_id_dic[id] == nil then

					local act_cfg = self:GetClientActCfg(id)

					if act_cfg ~= nil then
						if ServerActivityWGData.IsNormalAct(act_cfg.open_type) == is_normal then
							if act_cfg.id == ServerActClientId.OPEN_SERVER_WAR then
								local role_level = RoleWGData.Instance:GetRoleLevel()
								local act_level = self:GetCurrentRandActivityConfig().other[1].kaifujizhan_rolelevel
								if role_level >= act_level and ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FIRST_KILL) then
									table.insert(show_act_cfg_list, act_cfg)
									temp_act_id_dic[id] = 1
								end

							elseif act_cfg.id == ServerActClientId.OPEN_INNEGLECTABLE then --（新）开宗立派等级开服天数限制
								local role_level = RoleWGData.Instance:GetRoleLevel()
								local other_cfg = self.opengameactivity_auto.other[1]
								local cur_open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
								if role_level >= other_cfg.kaizonglipai_open_level and (cur_open_server_day > 0 and cur_open_server_day < other_cfg.kaizonglipai_close_day) then
									table.insert(show_act_cfg_list, act_cfg)
									temp_act_id_dic[id] = 1
								end
							else
								table.insert(show_act_cfg_list, act_cfg)
								temp_act_id_dic[id] = 1
							end
						end
					end
				end
			end
		end
	end
	--筛选出开启的子活动
	if is_normal then
		for k = #show_act_cfg_list, 1, -1 do
			if not self:IsOpenSprintAct(show_act_cfg_list[k].id) then
				table.remove(show_act_cfg_list, k)
			end
		end
	end

	-- 排序
	if is_sort then
		table.sort(show_act_cfg_list, SortTools.KeyLowerSorter("order"))
	end
	return show_act_cfg_list
end


--开服冲刺活动
function ServerActivityWGData:GetShowOpenSprintActList(is_sort, is_normal)  -- 是否排序, 是否是普通活动
	is_normal = is_normal or false
	local show_act_cfg_list = {}
	local temp_act_id_dic = {} --避免内容重复
	local list = nil
	for k,v in pairs(self.opening_type_list) do
		list = self.show_act2_cfg[v]
		if list ~= nil then
			for _,id in pairs(list) do
				if temp_act_id_dic[id] == nil then
					local act_cfg = self:GetClientActCfg(id)
					if act_cfg ~= nil then
						if ServerActivityWGData.IsNormalAct(act_cfg.open_type) == is_normal then
							table.insert(show_act_cfg_list, act_cfg)
							temp_act_id_dic[id] = 1
						end
					end
				end
			end
		end
	end
	if is_sort then
		table.sort(show_act_cfg_list, SortTools.KeyLowerSorter("order"))
	end
	return show_act_cfg_list
end


------------------------------------------
-- 获取开服活动排序顺序
------------------------------------------
function ServerActivityWGData:GetOpenServerActOrder(act_id)
	local order = 0;

	if self.openserver_act_map and self.openserver_act_map[act_id] then
		order = self.openserver_act_map[act_id].order
	else
		-- print_log("can not get server act order:",act_id)
		order = self.openserver_act_order_rd
		self.openserver_act_order_rd = self.openserver_act_order_rd - 1
	end
	return order
end


-- 是否开启的冲刺活动
function ServerActivityWGData:IsOpenSprintAct(act_id)
	if  ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.COMBINE_SERVER) then
		for k,v in pairs(ShowSmallActCfg[ACTIVITY_TYPE.COMBINE_SERVER]) do
			if act_id == v then
				return true
			end
		end
	end

	if not ActivityWGCtrl.Instance:IsOpenServerOpen() then return false end
	local rush_info = self.opengameactivity_auto.rush_rank_type
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local activity_info =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SEVENDAY)

	if ACT_RUSH_TYPE[act_id] ~= nil then
		for k,v in pairs(rush_info) do
			if v.rush_type == ACT_RUSH_TYPE[act_id] then
				return open_day >= v.open_day_index and open_day <= v.close_day_index
			end
		end
	elseif act_id == ServerActClientId.QITIANLIANSHUO and activity_info and ACTIVITY_STATUS.OPEN == activity_info.status then
		return true
	end
	return false
end

-- 是否开启的冲刺活动
function ServerActivityWGData:IsOpenSprintActTwo(act_id)
	if  ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.COMBINE_SERVER) then
		for k,v in pairs(ShowSmallActCfg[ACTIVITY_TYPE.COMBINE_SERVER]) do
			if act_id == v then
				return true
			end
		end
	end

	if not ActivityWGCtrl.Instance:IsOpenServerOpen() then return false end
	local rush_info = self.opengameactivity_auto.rush_rank_type
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local activity_info =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SEVENDAY)

	if ACT_RUSH_TYPE[act_id] ~= nil then
		for k,v in pairs(rush_info) do
			if v.rush_type == ACT_RUSH_TYPE[act_id] then
				return open_day >= v.open_day_index --and open_day <= v.close_day_index
			end
		end
	elseif act_id == ServerActClientId.QITIANLIANSHUO and activity_info and ACTIVITY_STATUS.OPEN == activity_info.status then
		return true
	end
	return false
end

-- 是否进行中的冲刺活动
function ServerActivityWGData:IsOpenningSprintActTwo(act_id)
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.COMBINE_SERVER) then
		for k,v in pairs(ShowSmallActCfg[ACTIVITY_TYPE.COMBINE_SERVER]) do
			if act_id == v then
				return true
			end
		end
	end

	if not ActivityWGCtrl.Instance:IsOpenServerOpen() then
		return false
	end
	local rush_info = self.opengameactivity_auto.rush_rank_type
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local activity_info =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SEVENDAY)

	if ACT_RUSH_TYPE[act_id] ~= nil then
		for k,v in pairs(rush_info) do
			if v.rush_type == ACT_RUSH_TYPE[act_id] then
				return open_day >= v.open_day_index and open_day <= v.close_day_index
			end
		end
	elseif act_id == ServerActClientId.QITIANLIANSHUO and activity_info and ACTIVITY_STATUS.OPEN == activity_info.status then
		return true
	end
	return false
end

function ServerActivityWGData:IsOpenTwoSprintAct(id)
	if not ActivityWGCtrl.Instance:IsOpenServerOpen() then return false end
	local rush_info = self.opengameactivity_auto.rush_rank_type
	if id ~= nil then
		for k,v in pairs(rush_info) do
			if v.rush_type == id then
				return v
			end
		end
	end
end

function ServerActivityWGData:OpenCompareActivity()
	local cfg = self:GetOpenCompareActivityCfg()
	if cfg then
		return cfg.rush_type
	end
end


function ServerActivityWGData:GetOpenCompareActivityCfg(time)
    time = time or TimeWGCtrl.Instance:GetCurOpenServerDay()
    --time = math.min(#ShowSmallActCfg2[ACTIVITY_TYPE.OPEN_SERVER],time)
	local rush_info = self.opengameactivity_auto.rush_rank_type
	for k,v in pairs(rush_info) do --目前 rush_type和第几天一致
		if v.rush_type == time then
			return v
		end
	end
end


-- 是否开启的冲刺活动
function ServerActivityWGData:GetNextOpenSprintActName()
	local rush_info = self.opengameactivity_auto.rush_rank_type
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k,v in pairs(rush_info) do
		if open_day + 1 == v.open_day_index then
			return v.show_title
		end
	end
end

--设置开服活动数据
function ServerActivityWGData:SetOpenServerData(protocol)
	-- self.open_game_info.oga_today_rush_type = protocol.oga_today_rush_type
	-- self.open_game_info.oga_today_rush_value = protocol.oga_today_rush_value
	-- self.open_game_info.oga_today_rush_rank = protocol.oga_today_rush_rank
	self.open_game_info.rush_info_list = protocol.rush_info_list

	self.open_game_info.oga_total_consume_num = protocol.oga_total_consume_num
	self.open_game_info.oga_total_consume_reward_fetch_flag = protocol.oga_total_consume_reward_fetch_flag

	self.open_game_info.oga_total_chongzhi_num = protocol.oga_total_chongzhi_num
	self.open_game_info.oga_total_chongzhi_reward_fetch_flag = protocol.oga_total_chongzhi_reward_fetch_flag

	self.open_game_info.oga_guild_battle_reward_type = protocol.oga_guild_battle_reward_type
	self.open_game_info.oga_guild_battle_reward_flag = protocol.oga_guild_battle_reward_flag
	self.open_game_info.oga_marry_type_record_flag = protocol.oga_marry_type_record_flag
	self.open_game_info.oga_couple_role_count = protocol.oga_couple_role_count
	self.open_game_info.oga_couple_list = protocol.oga_couple_list
	self.oga_word_exchange_close_timestamp = protocol.oga_word_exchange_close_timestamp
	self.open_game_info.oga_group_buy_reward_flag = bit:d2b(protocol.oga_group_buy_reward_flag)

	self.open_game_info.first_charge_person_num = protocol.first_charge_person_num

	self.open_game_info.create_guild_reward = protocol.create_guild_reward
	self.open_game_info.guild_level = protocol.guild_level
	self.open_game_info.guild_member_count = protocol.guild_member_count
	self.open_game_info.guild_fu_tuanzhang_count = protocol.guild_fu_tuanzhang_count
	self.open_game_info.guild_vip_level_count = protocol.guild_vip_level_count
	self.open_game_info.oge_create_guild_reward_flag = bit:d2b(protocol.oge_create_guild_reward_flag)
	self.open_game_info.create_guild_reward_list = protocol.create_guild_reward_list
	self.open_game_info.daily_limit_buy_count_flag = protocol.daily_limit_buy_count_flag     -- 每日限购
	self.open_game_info.oga_word_exchange_flag = protocol.oga_word_exchange_flag
	self.open_game_info.oga_cloud_buy_times = protocol.oga_cloud_buy_times
	self.open_game_info.kaizonglipai_forever_give_title = protocol.kaizonglipai_forever_give_title	-- 仙盟封榜 称号永久获得标志
	self.open_game_info.kaizonglipai_frist_give_title = protocol.kaizonglipai_frist_give_title
	self.open_game_info.kaizonglipai_close_timestamp = protocol.kaizonglipai_close_timestamp --仙盟封榜 结束时间戳
	self.open_game_info.oga_rush_rank_daily_reward_gift_flag = bit:d2b_two(protocol.oga_rush_rank_daily_reward_gift_flag) --开服活动 比拼每日标记， >> 0 每日礼包
	self.open_game_info.oga_rush_rank_daily_reward_buy_flag = bit:d2b_two(protocol.oga_rush_rank_daily_reward_buy_flag) --开服活动 比拼购买标记
	--[[
	self.open_game_info.oga_rush_rank_goal_reward_flags = {}
	for i,v in ipairs(protocol.oga_rush_rank_goal_reward_flags) do
		-- >> 0 达标1 >> 1 领取达标1 >> 2达标2 >> 3 领取达标2
		-- print_error("oga_rush_rank_goal_reward_flags",i,v)
		self.open_game_info.oga_rush_rank_goal_reward_flags[i] = bit:d2b_two(v)
	end
	--]]

	self:CheckOpenChongZhiWindow()
	self:GuildRankXianMengZhanIsOpen()
end

function ServerActivityWGData:GetKaiFuJiZiEndTime()
	return self.oga_word_exchange_close_timestamp or 0
end

function ServerActivityWGData:GetBiPinIconJump(rush_type)
	local cfg_list = self.bipinjumpicon_list[rush_type]
	local temp_list = {}
	if cfg_list then
		local function check(cfg)
			if cfg.activity_id ~= "" then
				local activity_id = tonumber(cfg.activity_id)
				if activity_id == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_MUST_BUY then
					local data_list = MustBuyWGData.Instance:GetItemList()
					if #data_list <= 0 then
						return false
					end
				end
				local act_info = ActivityWGData.Instance:GetActivityStatuByType(activity_id)
				if act_info and act_info.status == ACTIVITY_STATUS.OPEN then
					return true
				end
			end
			return true
		end
		for i,v in ipairs(cfg_list) do
			if check(v) then
				temp_list[#temp_list + 1] = v
			end
		end
	end
	return temp_list
end


-- region 获取开服比拼服务器数据
-- 是否可以购买
function ServerActivityWGData:OpenServerCompetitonCanBuy(rushtype)
	if self.open_game_info.oga_rush_rank_daily_reward_buy_flag then
		return self.open_game_info.oga_rush_rank_daily_reward_buy_flag[rushtype] == 0
	end
end

-- 是否可以领取每日礼包
function ServerActivityWGData:OpenServerCompetitonCanGetGift()
	if self.open_game_info.oga_rush_rank_daily_reward_gift_flag and
		TimeWGCtrl.Instance:GetCurOpenServerDay() < 8 then
		return self.open_game_info.oga_rush_rank_daily_reward_gift_flag[0] == 0
	end
end

-- 开服冲榜达人协议数据初始化
function ServerActivityWGData:InitOpenServerCompetitonInfo()
	-- 开服冲榜达人, 达标标记
	self.oga_rush_rank_goal_reward_flags = {}
	self.oga_rush_rank_goal_reward_flag_new = {}
	for i = 1, OGA_RUSH_TYPE_RANK_MAX_COUNT - 1 do
		self.oga_rush_rank_goal_reward_flags[i] = bit:d2b_two(0)
		self.oga_rush_rank_goal_reward_flag_new[i] = bit:ll2b_two(0,0)
	end

	-- 开服冲榜达人, 已领取标记
	self.oga_rush_rank_goal_fetch_reward_flags = {}
	self.oga_rush_rank_goal_fetch_reward_flag_new = {}
	for i = 1, OGA_RUSH_TYPE_RANK_MAX_COUNT - 1 do
		self.oga_rush_rank_goal_fetch_reward_flags[i] = bit:d2b_two(0)
		self.oga_rush_rank_goal_fetch_reward_flag_new[i] = bit:ll2b_two(0,0)
	end

	-- 每日礼包购买次数, 表示某个比拼类型某个奖励档位的已购买次数
	self.oga_rush_rank_daily_reward_buy_times = {}
	for i = 1, OGA_RUSH_TYPE_RANK_MAX_COUNT - 1 do
		local buy_times_list = {}
		for j = 1, OGA_RUSH_RANK_DAILY_REWARD_COUNT do
			buy_times_list[j] = 0
		end
		self.oga_rush_rank_daily_reward_buy_times[i] = buy_times_list
	end
end

-- 获取已购买次数
function ServerActivityWGData:GetCompetitionDailyBuyTimes(rush_type, index)
	if self.oga_rush_rank_daily_reward_buy_times[rush_type] then
		return self.oga_rush_rank_daily_reward_buy_times[rush_type][index] or 0
	end
	return 0
end

-- 是否可以购买每日限购
function ServerActivityWGData:GetCompetitionDailyCanBuy(rush_type, index)
	local buy_times = self:GetCompetitionDailyBuyTimes(rush_type, index)
	local daily_buy_cfg = self:GetRushDailyBuyCfgByRushType(rush_type)[index]
	local times_limit = daily_buy_cfg.daily_buy_times
	return buy_times < times_limit
end

-- 设置开服冲榜达人信息
function ServerActivityWGData:SetOpenServerCompetitonInfo(protocol)
	self.oga_rush_rank_close_flag = protocol.oga_rush_rank_close_flag
	self.oga_rush_rank_goal_reward_flags = protocol.oga_rush_rank_goal_reward_flags
	self.oga_rush_rank_goal_fetch_reward_flags = protocol.oga_rush_rank_goal_fetch_reward_flags
	self.oga_rush_rank_daily_reward_buy_times = protocol.oga_rush_rank_daily_reward_buy_times
	self.oga_role_rush_fetch_reward_flag = protocol.oga_role_rush_fetch_reward_flag
	self.oga_rush_rank_rank_list = protocol.oga_rush_rank_rank_list
	self.oga_rush_rank_goal_reward_flag_new = protocol.oga_rush_rank_goal_reward_flag_new
	self.oga_rush_rank_goal_fetch_reward_flag_new = protocol.oga_rush_rank_goal_fetch_reward_flag_new
	self.oga_role_rush_fetch_reward_flag_rank = protocol.oga_role_rush_fetch_reward_flag_rank
end

-- 是否可以领奖
function ServerActivityWGData:OpenServerCompetitonCanGetReward()
	local cur_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local rank_cfg_list = self.opengameactivity_auto.rush_rank_type
	if rank_cfg_list then
		for _,cfg in ipairs(rank_cfg_list) do
			if cfg.open_day_index > 0 and cur_server_day >= cfg.open_day_index then
				if self:OpenServerCompetitonSingleReward(cfg.rush_type) then
					return true
				end
			end
		end
	end
end

-- 单个冲榜能否领取奖励
function ServerActivityWGData:OpenServerCompetitonSingleReward(rush_type)
	-- 达标标记
	local flags = self:IsSpecialRank(rush_type) and self.oga_rush_rank_goal_reward_flag_new or self.oga_rush_rank_goal_reward_flags
	-- 已领取标记
	local fetch_flags = self:IsSpecialRank(rush_type) and self.oga_rush_rank_goal_fetch_reward_flag_new or self.oga_rush_rank_goal_fetch_reward_flags
	if flags and rush_type and flags[rush_type] then
		local flag = flags[rush_type]
		local fetch_flag = fetch_flags[rush_type]
		for i = 0, #flag do
			if flag[i] == 1 and fetch_flag[i] == 0 then
				return true, i
			end
		end
	end
end

-- 每次登陆提示一次绑玉礼包可以购买红点
function ServerActivityWGData:BiPinBindXianYuGiftCanBuyRemind(rush_type)
	if not rush_type then
		local cur_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local rank_cfg_list = self.opengameactivity_auto.rush_rank_type
		for _,cfg in ipairs(rank_cfg_list) do
			if cfg.open_day_index > 0 and cur_server_day >= cfg.open_day_index and not self.bipin_bind_gift_is_remind[cfg.rush_type] then
				local data_list = self:GetRushDailyBuyCfgByRushType(cfg.rush_type)
				if not IsEmptyTable(data_list) then
					local buy_times = 0
					for i=1,#data_list do
						if data_list[i].gold_type == 2 then
							buy_times = self:GetCompetitionDailyBuyTimes(data_list[i].rush_type, data_list[i].index)
							if buy_times < data_list[i].daily_buy_times then
								return true
							end
						end
					end
					self.bipin_bind_gift_is_remind[cfg.rush_type] = true
				end
			end
		end
	elseif not self.bipin_bind_gift_is_remind[rush_type] then
		local data_list = self:GetRushDailyBuyCfgByRushType(rush_type)
		if not IsEmptyTable(data_list) then
			local buy_times = 0
			for i=1,#data_list do
				if data_list[i].gold_type == 2 then
					buy_times = self:GetCompetitionDailyBuyTimes(data_list[i].rush_type, data_list[i].index)
					if buy_times < data_list[i].daily_buy_times then
						return true
					end
				end
			end
		end
	end
end

function ServerActivityWGData:SetBinPinBindGiftIsRemind(rush_type)
	if not self.bipin_bind_gift_is_remind[rush_type] then
		self.bipin_bind_gift_is_remind[rush_type] = true
		for _,is_remind in pairs(self.bipin_bind_gift_is_remind) do
			if not is_remind then
				return true
			end
		end

		RemindManager.Instance:Fire(RemindName.KFCompetition)
		return true
	end
end

-- 达标奖励能否领取(flag_index 从0开始)
function ServerActivityWGData:GetRushGoldCanReward(rush_type, flag_index)
	-- 达标标记
	local flags = self:IsSpecialRank(rush_type) and self.oga_rush_rank_goal_reward_flag_new or self.oga_rush_rank_goal_reward_flags
	-- 已领取标记
	local fetch_flags = self:IsSpecialRank(rush_type) and self.oga_rush_rank_goal_fetch_reward_flag_new or self.oga_rush_rank_goal_fetch_reward_flags
	local flag = flags[rush_type]
	local fetch_flag = fetch_flags[rush_type]
	if flag and fetch_flag then
		return flag[flag_index] + fetch_flag[flag_index] == 1
	end
	return false
end

-- 达标奖励是否已领取(flag_index 从0开始)
function ServerActivityWGData:GetRushGoldRewardFetch(rush_type, flag_index)
	-- 已领取标记
	local fetch_flags = self:IsSpecialRank(rush_type) and self.oga_rush_rank_goal_fetch_reward_flag_new or self.oga_rush_rank_goal_fetch_reward_flags
	if fetch_flags[rush_type] then
		return fetch_flags[rush_type][flag_index] == 1
	end
	return false
end

-- 获取达标奖励标记
function ServerActivityWGData:GetOpenServerGoalFlags(rushtype, flag_index)
	local reward_flags = self:IsSpecialRank(rushtype) and self.oga_rush_rank_goal_reward_flag_new or self.oga_rush_rank_goal_reward_flags

	if flag_index then
		if reward_flags[rushtype] then
			return reward_flags[rushtype][flag_index] or 0
		else
			return 0
		end
	end

	return reward_flags[rushtype]
end

-- 获取达标已领取标记
function ServerActivityWGData:GetOpenServerGoalFetchFlags(rushtype, flag_index)
	local fetch_flags = self:IsSpecialRank(rushtype) and self.oga_rush_rank_goal_fetch_reward_flag_new or self.oga_rush_rank_goal_fetch_reward_flags
	if flag_index then
		if fetch_flags[rushtype] then
			return fetch_flags[rushtype][flag_index] or 0
		else
			return 0
		end
	end
	return fetch_flags[rushtype]
end

-- 获得第几档奖励
function ServerActivityWGData:GetOpenServerBiPinRankRewardFlag(rushtype)
	print_error("---self.oga_rush_rank_rank_list[rushtype]----",self.oga_rush_rank_rank_list[rushtype])
	return self.oga_rush_rank_rank_list[rushtype]
end

-- 排行奖励领取标志
function ServerActivityWGData:GetOpenServerBiPinRankRewardFetch(rushtype)
	if self.oga_role_rush_fetch_reward_flag_rank then
		if self.oga_role_rush_fetch_reward_flag_rank[rushtype] ~= 0 then
			return self.oga_role_rush_fetch_reward_flag_rank[rushtype]
		end
	end
	return 0
end

function ServerActivityWGData:GetMainUiIsShowBiPinRank()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "MainUiIsShowBiPinRank")
	if not PlayerPrefsUtil.HasKey(key) then -- 默认显示
		--开服冲榜的气泡，玩家等级200级后默认勾选，如果玩家手动点掉了那就不显示，200级前默认不勾选
		local level = RoleWGData.Instance:GetRoleLevel()
		if level >= 200 then
			PlayerPrefsUtil.SetInt(key, 1)
			return true
		else
			PlayerPrefsUtil.SetInt(key, 0)
			return false
		end
		
	end
	local flag = PlayerPrefsUtil.GetInt(key)
	return flag == 1
end

function ServerActivityWGData:SetMainUiIsShowBiPinRank(flag)
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "MainUiIsShowBiPinRank")
	PlayerPrefsUtil.SetInt(key, flag)
	ServerActivityWGCtrl.Instance:RuningRequesFlushBiPinRankInfo()
end
-- endregion

function ServerActivityWGData:GetLoginRemind()
	return self.login_remind and ActivityWGCtrl.Instance:IsOpenServerOpen()
end

function ServerActivityWGData:SetLoginRemind(flag)
	self.login_remind = flag
end

function ServerActivityWGData:SetLoginLoversRemind(flag)
	self.login_lovers_remind = flag
end

function ServerActivityWGData:GetOpenServerData()
	return self.open_game_info
end

function ServerActivityWGData:GetOpenServerListData(open_act_type)
	if self.open_game_info ~= nil then
		return self.open_game_info.rush_info_list[open_act_type]
	end
end

function ServerActivityWGData:GetBuyRewardFlag()
	return self.open_game_info.oga_group_buy_reward_flag
end

function ServerActivityWGData:GuildRankXianMengZhanIsOpen()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if open_day > 0 and open_day < 4 then
		--self:AddActOpeningType(ACTIVITY_TIME_TYPE.GUILD_RANK_XIANGMENGZHAN)
	else
		--self:DeleteActOpeningType(ACTIVITY_TIME_TYPE.GUILD_RANK_XIANGMENGZHAN)
		self:SetActFinish(ServerActClientId.GUILD_RANK_XIANGMENGZHAN)
	end
end

function ServerActivityWGData:GetMrrayFlag()
	return self.oga_quanming_marry_reward_flag or 0
end

function ServerActivityWGData:GetMarryType()
	return self.marry_type
end

--缓存服务端发过来的活动数据
function ServerActivityWGData:CacheActRewardData(act_id, reward_flag, cur_value_t, can_reward_flag)
	-- print_error("缓存服务端发过来的活动数据", act_id, data)
	local data = {}
	data.reward_flag = reward_flag
	data.cur_value_t = cur_value_t
	data.can_reward_flag = can_reward_flag
	self.cache_act_reward_list[act_id] = data
end

function ServerActivityWGData:GetCacheActRewardData(act_id)
	return self.cache_act_reward_list[act_id]
end

function ServerActivityWGData:GetConvertCrazyListData()
	local reward_cfg = self:GetActRewardListByActId(ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY)
	table.sort(reward_cfg, SortTools.KeyLowerSorters("consume_stuff_num"))
	return reward_cfg
end

--某个奖励标记是否激活
function ServerActivityWGData:GetRewardIsGeted(act_id, seq)
	local flag_t = {}
	if self.cache_act_reward_list[act_id] then
		flag_t = self.cache_act_reward_list[act_id].reward_flag
	else
		flag_t = bit:d2b(0)
	end
	return flag_t[33 - (seq + 1)] == 1
end

--某个奖励是否已领取
function ServerActivityWGData:GetRewardIsCanGet(act_id, seq)
	local flag_t = {}
	if self.cache_act_reward_list[act_id] and self.cache_act_reward_list[act_id].can_reward_flag then
		flag_t = self.cache_act_reward_list[act_id].can_reward_flag
		return flag_t[33 - (seq + 1)] == 1
	else
		return true
	end
end

--活动兑换奖励是否可领
function ServerActivityWGData:TuPuRewardIsCanGet(seq)
	local config = ConfigManager.Instance:GetAutoConfig("activityconvert_auto").plates_convert
	local info = {}
	local flag = 1
	for k=1,4 do
		local item_id = config[#config]["stuff_id" .. k]
		local count = ItemWGData.Instance:GetItemNumInBagById(item_id)
		if count < config[seq+1]["stuff_num" .. k] then
			flag = 0
		end
	end
	if flag == 1 then
		return true
	else
		return false
	end
end

--获得当前进度值，如坐骑现在进阶到XX阶
function ServerActivityWGData:GetCurrentValue(act_id, index)
	local act_data = self.cache_act_reward_list[act_id]
	if act_data == nil or act_data.cur_value_t == nil then
		return 0
	end
	index = index or 1
	if act_data.cur_value_t[1] == -1 then
		act_data.cur_value_t[1] = 0
	end
	if act_data.cur_value_t.is_table then
		local list = {}
		for k,v in ipairs(act_data.cur_value_t) do
			if #list == 0 then
				table.insert(list, v[1] or 0)
			else
				table.insert(list, v[index] or 0)
			end
		end
		return list
	else
		return act_data.cur_value_t[index] or 0
	end
end

--领取奖励需要达到的值, 如坐骑达到XX阶才可领取XX
function ServerActivityWGData:GetRewardNeedValue(client_act_id, reward_cfg)
	local act_client_cfg = self:GetClientActCfg(client_act_id)
	if act_client_cfg == nil or reward_cfg == nil then return 1 end
	local limit_name = Split(act_client_cfg.limit, ",")
	local need_name = nil
	for i, name in ipairs(limit_name) do
		if nil ~= string.find(name, "need_") then
			need_name = name
		end
	end

	if need_name == nil then
		return 1
	end

	return reward_cfg[need_name] or 1
end

--获得奖励物描述如：vip5人数达到xx人可领取
--@act_id 客户端活动id
--@reward_cfg
function ServerActivityWGData:GetRewardItemDesc(act_id, reward_cfg)
	local act_client_cfg = self:GetClientActCfg(act_id)
	if act_client_cfg == nil or reward_cfg == nil then return end

	local cur_value = 0
	if act_client_cfg.id == ServerActClientId.TUAN_VIP then
		cur_value = self:GetCurrentValue(act_client_cfg.id, reward_cfg.vip_level)
	elseif act_client_cfg.id == ServerActClientId.FC_NATIONAL_ZONDONGYUAN then
		cur_value = self:GetCacheActRewardData(act_client_cfg.id)
		cur_value = cur_value.cur_value_t[1]
		cur_value = cur_value[reward_cfg.seq + 1]
	elseif act_client_cfg.id == ServerActClientId.EQUIP_GATHER or
		act_client_cfg.id == ServerActClientId.SEVENDAY_GOAL or
		act_client_cfg.id == ServerActClientId.LV_SPRINT or
		act_client_cfg.id == ServerActClientId.RAND_EQUIP_EXCHANGE or
		act_client_cfg.id == ServerActClientId.RAND_SPRITE_EXCHANGE then
		cur_value = self:GetCurrentValue(act_client_cfg.id, reward_cfg.seq)
	else
		cur_value = self:GetCurrentValue(act_client_cfg.id, 1)
	end
	local content = act_client_cfg.item_desc

	if act_client_cfg.id == ServerActClientId.ADVANCED_REWARD or act_client_cfg.id == ServerActClientId.FC_NATIONAL_ZONDONGYUAN then
		local cur_type = ServerActivityWGData.Instance:GetOpenServerAdvancedType()
		local replace_str = ""
		if -1 ~= cur_type then
			replace_str = Language.NewAppearance.AdvancedTypeName[cur_type] or ""
		end
		content = XmlUtil.RelaceTagContent(content, "cur_name", replace_str)
	end

	if act_client_cfg.id == ServerActClientId.SEVENDAY_GOAL then
		content = XmlUtil.RelaceTagContent(content, "limit_value_1", reward_cfg.cond_param)
	end

	if act_client_cfg.id == ServerActClientId.ALL_RANK then
		local cur_type = ServerActivityWGData.Instance:GetOpenServerAllRankType()
		local replace_str = ""
		if -1 ~= cur_type then
			replace_str = Language.NewAppearance.AdvancedTypeName[cur_type] or ""
		end
		
		content = XmlUtil.RelaceTagContent(content, "cur_name", replace_str)
		local rank_str = ""
		if reward_cfg.low_rank == reward_cfg.high_rank then
			rank_str = reward_cfg.low_rank
		else
			rank_str = reward_cfg.low_rank .. "-" .. reward_cfg.high_rank
		end
		content = XmlUtil.RelaceTagContent(content, "limit_value_1", rank_str)
	end

	if act_client_cfg.id == ServerActClientId.GUILD_RANK_XIANGMENGZHAN then
		local replace_str = ""
		if reward_cfg.reward_type == 1 then
			replace_str = Language.OpenServer.GuildNameBangZhu
		else
			replace_str = Language.OpenServer.GuildNameChengYuan
		end

		content = XmlUtil.RelaceTagContent(content, "cur_name", replace_str)
		content = XmlUtil.RelaceTagContent(content, "limit_value_1", reward_cfg.need_rank)
	end

	local limit_name_list = Split(act_client_cfg.limit,",") --可能为达到目标有多个限制条件值，如vip5达到XX人
	for k,v in pairs(limit_name_list) do
		local target_value = reward_cfg[v] or 0
		local replace_limit_str = "limit_value_" .. k  --替换目标名
		if (act_client_cfg.id == ServerActClientId.GROUP_BUYING) and 2 == k then
			if target_value > 0 then
				local add_str = Language.OpenServer.GroupBuyingDesc1
				if target_value > 1 then
					add_str = string.format(Language.OpenServer.GroupBuyingDesc2, target_value)
				end
				content = XmlUtil.RelaceTagContent(content, replace_limit_str, add_str)
			else
				content = XmlUtil.RelaceTagContent(content, replace_limit_str, "")
			end
			break
		else
			content = XmlUtil.RelaceTagContent(content, replace_limit_str, target_value)
		end
	end
	if type(cur_value) == "table" then
		if reward_cfg.global_fetch_times then
			content = XmlUtil.RelaceTagContent(content, "cur_value_2", math.max(reward_cfg.global_fetch_times - cur_value[2], 0))
		end
		content = XmlUtil.RelaceTagContent(content, "cur_value", cur_value[1])
	else
		content = XmlUtil.RelaceTagContent(content, "cur_value", cur_value)
	end
	return content
end

--获得显示物品奖励列表
--@is_unpack_gift 是否要展开礼包显示
function ServerActivityWGData:GetShowRewardOneItem(act_id, seq, param1)
	local act_client_cfg = self:GetClientActCfg(act_id)
	if act_client_cfg == nil then return end
	local is_unpack_gift = (1 == act_client_cfg.is_unpack_gift)

	local cfg_list = {}
	seq = seq or 0

	local item_list = {}
	if act_id == ServerActClientId.DAILY_TOTAL_CHONGZHI then -- 每日累充
		item_list = self:GetDaiyTotalChongzhiRewardList(seq, is_unpack_gift)
	elseif act_id == ServerActClientId.GUILD_LEVEL_RANK then --仙盟争霸
		item_list = self:GetGuildLevelRankRewardList(seq, is_unpack_gift)
	elseif act_id == ServerActClientId.GUILD_HUNT_RANK then --BOSS狩猎
		item_list = self:GetGuildWordBossRankRewardList(seq, is_unpack_gift)
	elseif act_id == ServerActClientId.RAND_DAY_DANBI_CHONGZHI then
		item_list = self:GetDayDanBiChongZhiRewardList(seq, is_unpack_gift)
	elseif act_id == ServerActClientId.TUPU_EXCHANGE then -- 活动兑换
		item_list = self:GetTuPuExchangeRewardList(seq, is_unpack_gift)
	else
		item_list = self:GetCommonShowRewardOneItem(act_id, seq, is_unpack_gift, param1)
	end
	return item_list
end

function ServerActivityWGData:GetCommonShowRewardOneItem(act_id, seq, is_unpack_gift, param1)
	local reward_cfg_list = self:GetActRewardListByActId(act_id, param1)

	if reward_cfg_list == nil then return {} end

	local reward_cfg = reward_cfg_list[seq]
	for k,v in pairs(reward_cfg_list) do
		if v.seq and v.seq == seq then
			reward_cfg = v
		end
	end
	reward_cfg = reward_cfg or reward_cfg_list[seq]
	if act_id == ServerActClientId.EQUIP_GIFT then
		local prof = RoleWGData.Instance:GetRoleProf()
		for k,v in pairs(reward_cfg_list) do
			if v.prof == prof and v.seq == seq then
				 reward_cfg = v
			end
		end
	end
	if reward_cfg == nil or reward_cfg.reward_item == nil then return {} end

	local item_list = {}
	if reward_cfg.reward_item.item_id ~= nil then
		return self:GetShowRewardListByCfg({reward_cfg.reward_item}, is_unpack_gift)
	else
		return self:GetShowRewardListByCfg(reward_cfg.reward_item, is_unpack_gift)
	end
	return item_list
end

--仙盟争霸
function ServerActivityWGData:GetGuildLevelRankRewardList(seq, is_unpack_gift)
	local guild_level_rank_cfg = self.opengameactivity_auto.guild_level_rank_cfg
	local level_rank_cfg = guild_level_rank_cfg[seq]
	if level_rank_cfg ~= nil then
		local reward_cfg = level_rank_cfg.reward_item
		if reward_cfg ~= nil then
			return self:GetShowRewardListByCfg({reward_cfg}, is_unpack_gift)
		end
	end
end

--BOSS狩猎
function ServerActivityWGData:GetGuildWordBossRankRewardList(seq, is_unpack_gift)
	local world_boss_rank_cfg = self.opengameactivity_auto.world_boss_rank_cfg
	local boss_rank_cfg = world_boss_rank_cfg[seq]
	if boss_rank_cfg ~= nil then
		local reward_cfg = boss_rank_cfg.reward_item
		if reward_cfg ~= nil then
			return self:GetShowRewardListByCfg({reward_cfg}, is_unpack_gift)
		end
	end
end

--随机活动-每日单笔充值奖励
function ServerActivityWGData:GetDayDanBiChongZhiRewardList(seq, is_unpack_gift)
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local day_danbi_cfg =  rand_config.danbichongzhi

	local activity_day = self:GetActDayPassFromStart(ACTIVITY_TYPE.RAND_DAY_DANBI_CHONGZHI)
	for k, v in pairs(day_danbi_cfg) do
		if v.activity_day == activity_day and v.seq == seq then
			local reward_cfg = v.reward_item
			if reward_cfg ~= nil then
				return reward_cfg
			end
		end
	end
end

--随机活动-狂嗨庆典配置
function ServerActivityWGData:GetCrazyHighCfg()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local role_level = RoleWGData.Instance.role_vo.level
	local crazy_high_celebration_cfg = {}
	for k,v in pairs(__TableCopy(rand_config.crazy_high_celebration)) do
		if role_level >= v.min_level and role_level <= v.max_level then
			table.insert(crazy_high_celebration_cfg, v)
		end
	end
	return crazy_high_celebration_cfg, __TableCopy(rand_config.crazy_high_celebration_2)
end

-- 开服活动比拼奖励配置
function ServerActivityWGData:GetOpenServerActivityRewardConfig(rush_type)
	local rank_reward_cfg = self.opengameactivity_auto.rush_rank_reward
	local reward_list = {}

	for i,v in ipairs(rank_reward_cfg) do
		if v.rush_type == rush_type then
			reward_list[#reward_list + 1] = v
		end
	end
	return reward_list
end

-- 获得对应比拼类型的达标值列表
function ServerActivityWGData:GetRushReachGoalList(rush_type)
	if self.rush_target_value_list[rush_type] then
		return self.rush_target_value_list[rush_type]
	end

	if self:IsSpecialRank(rush_type) then
		local special_list = self:GetSpecialRankReward(rush_type)
	    if not IsEmptyTable(special_list) then
	    	local value_list = {}
	    	for i=1,#special_list do
	    		value_list[i - 1] = special_list[i].reach_goal
	    	end
	    	self.rush_target_value_list[rush_type] = value_list
	    	return value_list
	    end
    end

    local list = {}
    local rush_rank_cfg = self:GetOpenServerActivityRushConfig(rush_type)
    if rush_rank_cfg then
        local reach_goal_str = rush_rank_cfg.reach_goal
        local goal_str_list = Split(reach_goal_str, "|")
        local index = 0
        for k, v in ipairs(goal_str_list) do
            list[index] = tonumber(v)
            index = index + 1
        end
    end
    self.rush_target_value_list[rush_type] = list

	return list
end

-- 获得对应比拼类型的达标值
function ServerActivityWGData:GetRushReachGoal(rush_type, special_index)
	return self:GetRushReachGoalList(rush_type)[special_index] or 0
end

-- 根据比拼类型获得比拼里多个达标奖励列表
function ServerActivityWGData:GetRushLastRewardItemListCfg(rush_type)
    if self.rush_target_reward_list[rush_type] then
    	return self.rush_target_reward_list[rush_type]
    end

    if self:IsSpecialRank(rush_type) then
	    local special_list = self:GetSpecialRankReward(rush_type)
	    if not IsEmptyTable(special_list) then
	    	local reward_list = {}
	    	for i=1,#special_list do
	    		reward_list[i] = special_list[i].reach_goal_reward_item
	    	end
	    	self.rush_target_reward_list[rush_type] = reward_list
	    	return reward_list
	    end
    end

    local rush_rank_cfg = self:GetOpenServerActivityRushConfig(rush_type)
    local rush_reward_list = {} -- 多个档位奖励列表
	if rush_rank_cfg then
        local reward_cfg = rush_rank_cfg.reach_goal_reward_item
        local reward_str_list = Split(reward_cfg, "|")
		for i, str in ipairs(reward_str_list) do
			local reward_item_list = self:AnalysisRewardItemList(str)
			table.insert(rush_reward_list, reward_item_list)
		end
	end
	self.rush_target_reward_list[rush_type] = rush_reward_list

	return rush_reward_list
end

-- 解析奖励列表
function ServerActivityWGData:AnalysisRewardItemList(reward_str)
	local t = Split(reward_str, ",")
	local reward_item = {}
	local index = 0
	for i, v in ipairs(t) do
		local item_data = self:AnalysisRewardItem(v)
		if item_data then
			reward_item[index] = item_data
			index = index + 1
		end
	end
	return reward_item
end

-- 解析单个奖励
function ServerActivityWGData:AnalysisRewardItem(str)
	if not str then
		return nil
	end
	local item_data = {}

	local t = Split(str, ":")
	item_data.item_id = tonumber(t[1])
	item_data.num = tonumber(t[2])
	item_data.is_bind = tonumber(t[3])
	return item_data
end

-- 获得比拼每日限购配置
function ServerActivityWGData:GetRushDailyBuyCfg()
	if self.rush_daily_buy_cfg == nil then
		self.rush_daily_buy_cfg = {}
		local rush_cfg = self.opengameactivity_auto.rush_rank_type
		for i,v in pairs(rush_cfg) do
			local data = {}

			-- 限购物品信息
			local daily_buy_item_list = Split(v.daily_buy_item, "|")
			for j, value in ipairs(daily_buy_item_list) do
				data[j] = {}
				data[j].index = j
				data[j].rush_type = v.rush_type
				data[j].daily_buy_item = self:AnalysisRewardItemList(value)
			end

			-- 限购次数
			local daily_buy_times_list = Split(v.daily_buy_times, "|")
			for j, value in ipairs(daily_buy_times_list) do
				if data[j] then
					data[j].daily_buy_times = tonumber(value)
				end
			end

			-- 原价
			local old_gold_list = Split(v.old_gold, "|")
			for j, value in ipairs(old_gold_list) do
				if data[j] then
					data[j].old_gold = tonumber(value)
				end
			end

			-- 现价
			local need_gold_list = Split(v.need_gold, "|")
			for j, value in ipairs(need_gold_list) do
				if data[j] then
					data[j].need_gold = tonumber(value)
				end
			end

			-- 货币类型
			local gold_type = Split(v.gold_type, "|")
			for j,value in ipairs(gold_type) do
				if data[j] then
					data[j].gold_type = tonumber(value)
				end
			end

			self.rush_daily_buy_cfg[v.rush_type] = data
		end
	end
	return self.rush_daily_buy_cfg
end

-- 限时无限礼包购买配置
function ServerActivityWGData:GetRushLimitTimeGiftDataListByRushType(rush_type)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg_list = self:GetOpenServerActivityRushConfig(rush_type)
	-- 当天才显示
	if not cfg_list or cfg_list.open_game_day ~= open_day then
		return {}
	end

	---[[ 当天23点前有效
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local tab = os.date("*t", server_time)
	tab.hour = 23
	tab.min = 0
	tab.sec = 0
	local end_time = os.time(tab)
	if server_time > end_time then
		return {}
	end
	--]]

	if not self.rush_limit_time_buy_data_list[rush_type] then
		local item_list = Split(cfg_list.no_limit_item, "|")
		local old_gold_list = Split(cfg_list.init_price, "|")
		local gold_list = Split(cfg_list.price, "|")
		local data_list = {}
		for i=1,#item_list do
			local data = {is_limit_time = true, rush_type = rush_type, end_time = end_time, open_day = cfg_list.open_game_day,
				daily_buy_times = 99, index = -1, old_gold = 0, need_gold = 0, gold_type = 2, daily_buy_item = {}}
			data.daily_buy_item = self:AnalysisRewardItemList(item_list[i])
			data.old_gold = tonumber(old_gold_list[i]) or 0
			data.need_gold = tonumber(gold_list[i]) or 0
			data.tips_discount = cfg_list.tips_discount or ""
			data_list[i] = data
		end

		self.rush_limit_time_buy_data_list[rush_type] = data_list
	end

	return self.rush_limit_time_buy_data_list[rush_type] or {}
end

-- 获取每日限购配置
function ServerActivityWGData:GetRushDailyBuyCfgByRushType(rush_type)
	local buy_cfg_list = self:GetRushDailyBuyCfg()
	local buy_list = buy_cfg_list and buy_cfg_list[rush_type] or {}

	local limit_time_list = self:GetRushLimitTimeGiftDataListByRushType(rush_type)
	if not IsEmptyTable(limit_time_list) then
		local data_list = {}
		for i=1,#limit_time_list do
			data_list[#data_list + 1] = limit_time_list[i]
		end
		for i=1,#buy_list do
			data_list[#data_list + 1] = buy_list[i]
		end
		return data_list
	end

	return buy_list
end

function ServerActivityWGData:GetGetWay(rush_type)
	if type(rush_type) ~= "number" then return end
	local rank_reward_cfg = self.opengameactivity_auto.rush_rank_reward
	local get_way = nil
	local getway_params = {}

	for i,v in ipairs(rank_reward_cfg) do
		if v.rush_type == rush_type then
			getway_params[#getway_params+1] = v.get_way_param
		end
	end

	local rush_rank_cfg = self:GetOpenServerActivityRushConfig(rush_type)
	if rush_rank_cfg then
		return rush_rank_cfg.get_way, getway_params
	end
end

-- 开服活动比拼配置
function ServerActivityWGData:GetOpenServerActivityRushConfig(rush_type)
	local rank_cfg = self.opengameactivity_auto.rush_rank_type
	for i,v in pairs(rank_cfg) do
		if v.rush_type == rush_type then
			return v
		end
	end
end

-- 开服活动比拼配置
function ServerActivityWGData:GetOpenServerActivityRushConfigByRankType(rank_type)
	local rank_cfg = self.opengameactivity_auto.rush_rank_type
	for i,v in pairs(rank_cfg) do
		if v.rush_type == rank_type then
			return v
		end
	end
end

function ServerActivityWGData:GetTodayBipinRankCfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local open_act_cfg = self:GetOpenGameActivityConfig()
    local list = {}
	if open_act_cfg and open_act_cfg.rush_rank_type then
		local data = nil
		for i = 1, COMMON_CONSTS.OpenServerBiPinNum do
			data = open_act_cfg.rush_rank_type[i]
			if data and open_day >= data.open_day_index and open_day <= data.close_day_index and data.close_day_index > 0 then
				list[#list + 1] = data
			end
		end
    end
    return list
end

-- ***端不会改就**搞特殊
function ServerActivityWGData:IsSpecialRank(rush_type)
	if rush_type == 2 or rush_type == 3 then -- 坐骑 宠物
		return true
	end
	return false
end

function ServerActivityWGData:GetSpecialRankReward(rush_type)
	local cfg_atuo = self:GetOpenGameActivityConfig()
	local cfg_list = cfg_atuo and cfg_atuo.rank_reach_reward
	if cfg_list then
		local data_list = {}
		for i=1,#cfg_list do
			if cfg_list[i].rush_type == rush_type then
				data_list[#data_list + 1] = cfg_list[i]
			end
		end
		return data_list
	end
end

-- 开服活动配置
function ServerActivityWGData:GetOpenGameActivityConfig()
	return self.opengameactivity_auto
end

-- 开服活动配置数据
function ServerActivityWGData:GetOpenGameActivityDataList()
	local open_act_cfg = self:GetOpenGameActivityConfig()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local data_list = {}
	if open_act_cfg and open_act_cfg.rush_rank_type then
		for i = 1, COMMON_CONSTS.OpenServerBiPinNum do
			local data = {}
			data.cfg = open_act_cfg.rush_rank_type[i]
			if data.cfg and data.cfg.close_day_index > 0 then
				local is_close = open_day > data.cfg.close_day_index
				data.sort = is_close and (1000 + i) or i
				data_list[#data_list + 1] = data
			end
		end
	end

	table.sort(data_list, SortTools.KeyLowerSorter("sort"))
	return data_list
end


function ServerActivityWGData:GetOpenSerActOtherCfg(key)
	if self.opengameactivity_auto and key then
		return self.opengameactivity_auto.other[1][key]
	end
end

--开服累充按钮入口控制
function ServerActivityWGData:CheckOpenSevenChongzhi()
	--角色等级
	local role_level = RoleWGData.Instance.role_vo.level
	--配置开启等级
	local open_level = self.opengameactivity_auto.other[1].total_chongzhi_level_limit
	local is_open = self:IsCanClose()
	if role_level >= open_level and is_open then
		return true
	end
	return false
end

--开服累充按钮入口是否可以关闭控制
function ServerActivityWGData:IsCanClose()
	local consume_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig().total_chongzhi
	if IsEmptyTable(consume_cfg) then
		return
	end
	local index = 1
	local flag_t = bit:d2b(ServerActivityWGData.Instance:GetOpenServerData().oga_total_chongzhi_reward_fetch_flag or 0)

	if flag_t then
		for i,v in ipairs(consume_cfg) do
			if flag_t[32 - v.seq] == 0 then
				return true
			end
		end
	end
	return false
end

--活动兑换奖励
function ServerActivityWGData:GetTuPuExchangeRewardList(seq, is_unpack_gift)
	local rand_config = ConfigManager.Instance:GetAutoConfig("activityconvert_auto")
	local rand_t =  rand_config.plates_convert
	if rand_t[seq + 1] ~= nil then
		local reward_cfg = rand_t[seq + 1].reward_item
		if reward_cfg ~= nil then
			return self:GetShowRewardListByCfg({reward_cfg}, is_unpack_gift)
		end
	end
end

--获得七天后每日累充奖励物品列表
function ServerActivityWGData:GetDaiyTotalChongzhiRewardList(seq, is_unpack_gift)
	local stage = self.chong_zhi_info.daily_total_chongzhi_stage

	local chongzhi_cfg_list = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").daily_total_chongzhi_reward
	for k,v in pairs(chongzhi_cfg_list) do
		if v.stage == stage and v.seq == seq then
			local reward_cfg = v.total_recharge
			if reward_cfg ~= nil then
				return self:GetShowRewardListByCfg({reward_cfg}, is_unpack_gift)
			end
		end
	end
	return nil
end

--礼包解包
function ServerActivityWGData:GetShowRewardListByCfg(cfg_list, is_unpack_gift)
	if cfg_list == nil then return nil end

	local show_list = {}
	for k,v in pairs(cfg_list) do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg ~= nil then
			if big_type == GameEnum.ITEM_BIGTYPE_GIF and is_unpack_gift then
				local item_list_in_gift = ItemWGData.Instance:GetItemListInGift(item_cfg.id)
				for _,item_in_gift in pairs(item_list_in_gift) do
					table.insert(show_list, item_in_gift)
				end
			else
				table.insert(show_list, v)
			end
		end
	end

	return show_list
end

function ServerActivityWGData:GetActDayPassFromStart(activity_type)
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(activity_type)
	local activity_day = 0
	if nil ~= activity_status then
		local format_time_start = os.date("*t", activity_status.start_time)
		local end_zero_time_start = os.time{year=format_time_start.year, month=format_time_start.month, day=format_time_start.day, hour=0, min = 0, sec=0}
		if nil == end_zero_time_start then
			return activity_day
		end

		local format_time_now = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
		local end_zero_time_now = os.time{year=format_time_now.year, month=format_time_now.month, day=format_time_now.day, hour=0, min = 0, sec=0}

		local format_start_day = math.floor(end_zero_time_start / (60 * 60 * 24))
		local format_now_day =  math.floor(end_zero_time_now / (60 * 60 * 24))
		activity_day = format_now_day - format_start_day
	end
	return activity_day
end

--根据act_id 获取奖励配置表
function ServerActivityWGData:GetActRewardListByActId(act_id, param1)
	if ServerActClientId.DAILY_FISRT_CHONGZHI == act_id then				--每日首充，根据当天是星期几获得奖励列表
		local fisrt_chong_reward_list = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").daily_fisrt_chongzhi_reward
		for k,v in pairs(fisrt_chong_reward_list) do
			if v.day == tonumber(self:GetNowDate()) then
				return {v}
			end
		end
	elseif ServerActClientId.DAILY_TOTAL_CHONGZHI == act_id then
		local stage = self.chong_zhi_info.daily_total_chongzhi_stage
		local today_cfg = {}
		local cfg = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").daily_total_chongzhi_reward
		for k, v in pairs(cfg) do
			if v.stage == stage then
				table.insert(today_cfg, v)
			end
		end
		return today_cfg
	-- elseif ServerActClientId.RAND_MONSTER_DROP == act_id then
	-- 	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	-- 	local drop_cfg = rand_config.any_monster_drop

	-- 	return drop_cfg
	elseif ServerActClientId.RAND_DAY_DANBI_CHONGZHI == act_id then
		local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
		local rand_t =  rand_config.danbichongzhi

		-- local day_danbi_cfg = self:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_DAY_DANBI_CHONGZHI)

		local activity_day = self:GetActDayPassFromStart(ACTIVITY_TYPE.RAND_DAY_DANBI_CHONGZHI)

		local today_cfg = {}
		for i, v in ipairs(rand_t) do
			if v.activity_day == activity_day then
				table.insert(today_cfg, v)
			end
		end
		return today_cfg
	elseif ServerActClientId.GROUP_BUYING == act_id then
		local opengameactivity_config = self.opengameactivity_auto
		local act_cfg = self:GetRandActivityConfig(opengameactivity_config["firstchongzhi_groupbuy"], ACTIVITY_TYPE.OPEN_SERVER)
		if nil == param1 then
			return act_cfg
		end

		local today_cfg = {}
		for k,v in ipairs(act_cfg) do
			if param1 == v.groupbuy_active_need_person then
				table.insert(today_cfg, v)
			end
		end
		return today_cfg
	elseif ServerActClientId.TUPU_EXCHANGE == act_id then
		local rand_config = ConfigManager.Instance:GetAutoConfig("activityconvert_auto")
		local rand_t =  rand_config.plates_convert

		local today_cfg = {}
		for k,v in ipairs(rand_t) do
			table.insert(today_cfg, v)
		end
		return today_cfg
	elseif ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY == act_id then
		-- local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().convert_crazy
		-- local info = self:GetCacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY)
		-- local join_role_level = info and info.cur_value_t.join_role_level or nil
		-- local role_level = RoleWGData.Instance.role_vo.level
		-- if nil == join_role_level then
		-- 	join_role_level = role_level
		-- end
		-- local reward_cfg = {}
		-- local special_seq_cfg = {}
		-- for k,v in pairs(cfg) do
		-- 	if v.open_level <= join_role_level then
		-- 		if v.seq == SpecCrazySeq then
		-- 			special_seq_cfg[#special_seq_cfg + 1] = __TableCopy(v)
		-- 		else
		-- 			local data = __TableCopy(v)
		-- 			table.insert(reward_cfg, data)
		-- 		end
		-- 	end
		-- end
		-- if #special_seq_cfg > 0 then
		-- 	table.insert(reward_cfg, special_seq_cfg[#special_seq_cfg])
		-- end
		-- return reward_cfg
		return {}
	end
	local act_client_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	if nil == act_client_cfg then
		return
	end
	local big_type = ""
	local big_type_t = Split(act_client_cfg.big_type, "/")
	if big_type_t[1] then
		local is_ios_plat = false
		local p_str = is_ios_plat and "_ios" or ""
		big_type_t[1] = string.gsub(big_type_t[1], "@ios", p_str)
	end
	if nil ~= big_type_t[2] then
		local k1, k2 = big_type_t[1], big_type_t[2]
		if nil ~= ConfigManager.Instance:GetAutoConfig(k1)[k2] then
			return ConfigManager.Instance:GetAutoConfig(k1)[k2]
		end
	else
		big_type = big_type_t[1]
	end

	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()

	local opengameactivity_config = self.opengameactivity_auto
	if nil ~= opengameactivity_config[big_type] then
		local clientact_List = self:GetClientActCfgByType(big_type)
		if clientact_List.is_adapt == 0 then 				-- 开服活动的分天与随机活动不同,注意哦
			return opengameactivity_config[big_type]
		else
			return self:GetOpenServerActivityConfig(opengameactivity_config[big_type])
		end
	elseif nil ~= rand_config[big_type] then
		local day_consume_cfg = nil

		local clientact_List = self:GetClientActCfgByType(big_type)
		if clientact_List then
			if clientact_List.is_adapt == 0 then
				day_consume_cfg = rand_config[big_type]
			else
				day_consume_cfg = self:GetRandActivityConfig(rand_config[big_type], clientact_List.get_type)
			end
		else
			return
		end
		return day_consume_cfg
	end
end

-- 获取开服活动配置
function ServerActivityWGData:GetOpenServerActivityConfig(cfg)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local rand_t = {}
	local day = nil
	for k,v in pairs(cfg) do
		if v and (nil == day or v.opengame_day == day) and open_day <= v.opengame_day then
			day = v.opengame_day
			table.insert(rand_t, v)
		end
	end
	return rand_t
end

function ServerActivityWGData:GetClientActCfgByType(big_type)
	local list = self:GetClientActList()
	for k,v in pairs(list) do
		if big_type == v.big_type then
			return v
		end
	end
	return nil
end

--列表型界面的数据
function ServerActivityWGData:GetActivityItemDataListNew(act_id, param1)
	local act_client_cfg = self:GetClientActCfg(act_id)
	if nil == act_client_cfg then
		return
	end

	if ServerActivityWGData.Instance:GetServerActivityIsOpen(act_client_cfg.open_type) == false then
		return {}
	end
	local reward_cfg_list = self:GetActRewardListByActId(act_id, param1) or {}
	local data_list = {}
	for i = 0, table.maxn(reward_cfg_list) do
		local v = reward_cfg_list[i]
		if v ~= nil then
			local reward_seq = v.seq or i
			local is_geted = self:GetRewardIsGeted(act_id, reward_seq)
			local reward_type = act_client_cfg.open_type

			local cur_value = 0
			cur_value = self:GetCurrentValue(act_id, 1)

			local need_num = self:GetRewardNeedValue(act_id, v)
			if "number" == type(need_num) and "number" == type(cur_value) and (not is_geted) and (cur_value >= need_num) then
				is_geted = true
			end

			local is_have_opp = self:GetRewardIsCanGet(act_id, reward_seq)
			local instruction = self:GetRewardItemDesc(act_id, v)
			local btn_name = act_client_cfg.item_btn
			local btn_name_list = Split(act_client_cfg.item_btn, "#")
			if #btn_name_list > 1 then
				btn_name = btn_name_list[i + 1]
			end

			local data = __TableCopy(v)
			data.act_id = act_id
			data.is_geted = is_geted
			data.is_have_opp = is_have_opp
			data.reward_type = reward_type
			data.reward_seq = reward_seq
			data.instruction = instruction
			data.btn_name = btn_name
			data.item_list = self:GetShowRewardOneItem(act_id, reward_seq, param1)
			data.item_func = self:GetListItemFunc(act_id, v)
			table.insert(data_list, data)
		end
	end

	local sort_func = function (a, b)
		local order_a = 100000
		local order_b = 100000
		if a.is_have_opp then
			order_a = order_a + 10000
		end
		if b.is_have_opp then
			order_b = order_b + 10000
		end

		if a.reward_seq > b.reward_seq then
			order_a = order_a + 100
		elseif a.reward_seq < b.reward_seq then
			order_b = order_b + 100
		end
		return order_a < order_b
	end

	table.sort(data_list, sort_func)
	return data_list
end

--列表型界面的数据
function ServerActivityWGData:GetActivityItemDataList(act_id, param1)
	if act_id == ServerActClientId.RAND_LOGIN_GIFT then
		return self:GetLoginGiftItemDataList()
	-- elseif act_id == ServerActClientId.CS_CHONGZHI_RANK or act_id == ServerActClientId.CS_CONSUME_RANK then
	-- 	return ActCombineData.Instance:GetItemDataList(act_id)
	--elseif act_id == ServerActClientId.RAND_TOTAL_CHONGZHI then
	--	return TotalChargeWGData.Instance:GetRechargeDataInfo()
	end

	local act_client_cfg = self:GetClientActCfg(act_id)
	if nil == act_client_cfg then
		return
	end

	if ServerActivityWGData.Instance:GetServerActivityIsOpen(act_client_cfg.open_type) == false then
		return
	end
	local reward_cfg_list = self:GetActRewardListByActId(act_id, param1) or {}
	local data_list = {}
	local sort_list = {}
	for i = 0, table.maxn(reward_cfg_list) do
		local v = reward_cfg_list[i]
		if v ~= nil then
			local reward_seq = v.seq or i
			local is_geted = self:GetRewardIsGeted(act_id, reward_seq)
			local reward_type = act_client_cfg.get_type

			local cur_value = 0

			if act_id == ServerActClientId.TUAN_VIP then --vip需要拿等级获得对应的当前值
				cur_value = self:GetCurrentValue(act_id, v.vip_level)
			elseif act_id == ServerActClientId.FC_NATIONAL_ZONDONGYUAN then
				cur_value = self:GetCacheActRewardData(act_client_cfg.id)
				cur_value = cur_value.cur_value_t[1]
				cur_value = cur_value[v.seq + 1]
				-- cur_value = self:GetCurrentValue(act_id, TimeWGCtrl.Instance:GetCurOpenServerDay())
			else 										--其他则从值表中取第一个即可
				cur_value = self:GetCurrentValue(act_id, 1)
			end
			if act_id == ServerActClientId.SEVENDAY_GOAL or
				act_id == ServerActClientId.LV_SPRINT or
				act_id == ServerActClientId.EQUIP_GATHER or
				act_id == ServerActClientId.RAND_EQUIP_EXCHANGE or
				act_id == ServerActClientId.RAND_SPRITE_EXCHANGE then
				cur_value = self:GetCurrentValue(act_id, reward_seq)
			end

			local need_num = self:GetRewardNeedValue(act_id, v)
			local is_can_get = false
			if "number" == type(need_num) and "number" == type(cur_value) and (not is_geted) and (cur_value >= need_num) then
				is_can_get = true
			end
			if act_id == ServerActClientId.SEVENDAY_GOAL then
				local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
				local limit = self:GetSevendayGoalRewardLimit(cur_day, v.seq) or 0
				local is_enough = false

				if cur_day == 5 then 			-- 写死，由于第五天比较的是排名
					if cur_value[1] > 0 then 	-- 功能未开启排名小于1条件会达成
						is_enough = cur_value[1] <= limit
					end
				else
					is_enough = cur_value[1] >= limit
				end
				is_can_get = not is_geted and is_enough
			elseif act_id == ServerActClientId.LV_SPRINT then
				is_can_get = v.level <= GameVoManager.Instance:GetMainRoleVo().level and not is_geted and cur_value[2] < v.global_fetch_times
			elseif act_id == ServerActClientId.EQUIP_GATHER then
				is_can_get = v.need_num <= EquipWGData.Instance:GetDataCountByAttr(v.need_jie, v.need_color) and not is_geted and cur_value[2] < v.global_fetch_times
			elseif act_id == ServerActClientId.COLLECT_ITEMS then
			elseif act_id == ServerActClientId.TUPU_EXCHANGE then
				if EquipWGData.Instance:GetDataList() == nil then return end
				-- if EquipWGData.Instance:GetDataList()[3] ~= nil and EquipWGData.Instance:GetDataList()[3].param.quality >= 3 then
				-- 	self.tupu_times[1].times = 1
				-- 	elseif EquipWGData.Instance:GetDataList()[9] ~= nil and EquipWGData.Instance:GetDataList()[9].param.quality >= 3 then
				-- 		self.tupu_times[5].times = 1
				-- 	elseif EquipWGData.Instance:GetDataList()[4] ~= nil and EquipWGData.Instance:GetDataList()[4].param.quality >= 3 then
				-- 		self.tupu_times[2].times = 1
				-- 	elseif EquipWGData.Instance:GetDataList()[0] ~= nil and EquipWGData.Instance:GetDataList()[0].param.quality >= 3 then
				-- 		self.tupu_times[3].times = 1
				-- 	elseif EquipWGData.Instance:GetDataList()[2] ~= nil and EquipWGData.Instance:GetDataList()[2].param.quality >= 3 then
				-- 		self.tupu_times[4].times = 1
				-- 	elseif EquipWGData.Instance:GetDataList()[5] ~= nil and EquipWGData.Instance:GetDataList()[5].param.quality >= 3 then
				-- 		self.tupu_times[6].times = 1
				-- end
				is_geted = self.tupu_times[i].times >= v.limit_times
				is_can_get = not is_geted
			elseif act_id == ServerActClientId.GROUP_BUYING then
				is_can_get = cur_value[1] >= v.groupbuy_active_need_person and cur_value[2] >= v.fetch_need_min_chongzhi_value and not is_geted
			end

			local is_have_opp = self:GetRewardIsCanGet(act_id, reward_seq)
			if act_id == ServerActClientId.TUPU_EXCHANGE then
				local tupu_have_opp = self:TuPuRewardIsCanGet(reward_seq)
				is_can_get = is_can_get and tupu_have_opp
			else
				is_can_get = is_can_get and is_have_opp
			end

			local instruction = self:GetRewardItemDesc(act_id, v)
			local btn_name = act_client_cfg.item_btn
			local btn_name_list = Split(act_client_cfg.item_btn, "#")
			if #btn_name_list > 1 then
				btn_name = btn_name_list[i + 1]
			end

			local data = {}
			data.act_id = act_id
			data.is_geted = is_geted
			data.is_can_get = is_can_get
			data.reward_type = reward_type
			data.reward_seq = reward_seq
			data.instruction = instruction
			data.btn_name = btn_name
			data.item_list = self:GetShowRewardOneItem(act_id, reward_seq, param1)
			if act_id == ServerActClientId.RAND_EQUIP_EXCHANGE or act_id == ServerActClientId.RAND_SPRITE_EXCHANGE then
				local item = {}
				if act_id == ServerActClientId.RAND_EQUIP_EXCHANGE then
					item.item_id = 90126
				else
					item.item_id = 90127
				end
				item.num = v.need_score * math.pow(2, math.floor(cur_value[2] / v.double_count))
				item.is_bind = 0
				table.insert(data.item_list, 1, item)
				local count = v.double_count - cur_value[2] % v.double_count
				data.instruction = string.format(Language.OpenServer.NextDoubleTimes, count)
			elseif ServerActClientId.TOTAL_CHONGZHI == data.act_id then
				data.need_chongzhi = v.need_chongzhi
			end
			data.item_func = self:GetListItemFunc(act_id, v)
			if act_id ~= ServerActClientId.EQUIP_GIFT or v.prof == RoleWGData.Instance:GetRoleProf() then
				if data.is_can_get == true or data.is_geted == false then
					table.insert(data_list, data)
				else
					table.insert(sort_list, data)
				end
			end
			if act_id == ServerActClientId.ALL_RANK then
				data.rank_type = self:GetOpenServerAllRankInfoType()
			end
		end
	end
	for k,v in pairs(sort_list) do
		table.insert(data_list, v)
	end
	sort_list = nil
	return data_list
end

function ServerActivityWGData:GetTuPuRewardNum()
	local list = self:GetActivityItemDataList(ServerActClientId.TUPU_EXCHANGE)
	if list == nil then return end
	local flag = 0
	if list[1].is_can_get == true then
		flag = flag + 1
	end
	local num = 0
	if flag > 0  then
		return flag
	else
		return num
	end
end

function ServerActivityWGData:GetLotteryDrawRewardNum()
	local lucky_roll_times = self:GetLuckydrawInfo()
	if lucky_roll_times == nil then return end
	local flag = 0
	if lucky_roll_times.lucky_draw_reward_can_fetch_count >= 1 then
		flag = lucky_roll_times.lucky_draw_reward_can_fetch_count
	end
	return flag
end

function ServerActivityWGData:GetQuanMingMarriedNum()
	local num = 0
	if self.oga_quanming_marry_reward_flag == 0 and RoleWGData.Instance.role_vo.lover_uid > 0 then
		num = 1
	end
	return num
end

--列表形, 获取按钮回调函数
function ServerActivityWGData:GetListItemFunc(act_id, config)
	local item_func = nil
	if act_id == ServerActClientId.RAND_SINGLE_CHARGE then
		item_func = function()
			--RechargeWGCtrl.Instance:Recharge(config.charge_value / RECHARGE_BILI) --元宝化rmb
			Log("跳转平台充值:", config.charge_value / RECHARGE_BILI)
		end
	end
	return item_func
end

--获取温馨提示数字
function ServerActivityWGData:GetRemindNum(remind_id)
	local client_act_list = self:GetClientActList()
	local act_client_cfg = nil
	for k, v in pairs(client_act_list) do
		if v.remind_id == remind_id then
			act_client_cfg = v
		end
	end
	if nil == act_client_cfg then
		return 0
	end
	if nil == self.cache_act_reward_list[act_client_cfg.id] then
		return 0
	end
	local act_cfg_list = self:GetActivityItemDataList(act_client_cfg.id) or {}
	local count = 0
	if act_cfg_list ~= nil then
		for k,v in pairs(act_cfg_list) do
			if v.is_can_get then
				count = count + 1
			end
		end
	end
	return count
end

--获取活动剩余时间
function ServerActivityWGData:GetActivityLeftTime(act_type, sub_type)
	if act_type == 1001 then
		local daily_total_chongzhi_stage = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").daily_total_chongzhi_stage
		local time_cfg = daily_total_chongzhi_stage[self.chong_zhi_info.daily_total_chongzhi_stage]
		if nil ~= time_cfg then
			local end_time = TimeWGCtrl.Instance:GetServerRealStartTime() + time_cfg.end_day * 60 * 60 * 24
			local format_time = os.date("*t", end_time)
			local end_zero_time = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=0, min = 0, sec=0}
			local time_left = end_zero_time - TimeWGCtrl.Instance:GetServerTime()
			if time_left < 0 then
				time_left = 0
			end
			return TimeUtil.Format2TableDHM(time_left)
		else
			return nil
		end
	elseif act_type == ACTIVITY_TYPE.COMBINE_SERVER then
		if nil ~= sub_type and "" ~= sub_type then
			return ActCombineData.Instance:GetCombineActTimeLeft(sub_type)
		end
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if nil == act_info or act_info.status ~= ACTIVITY_STATUS.OPEN then
		return
	end
	local time_left = act_info.next_time - TimeWGCtrl.Instance:GetServerTime()

	if act_type == ACTIVITY_TYPE.RAND_DAY_DANBI_CHONGZHI then
		local format_time = TimeWGCtrl.Instance:GetServerTimeFormat()
		local end_time =  os.time{year=format_time.year, month=format_time.month, day=format_time.day + 1, hour=0, min = 0, sec=0}
		local day_left = end_time - TimeWGCtrl.Instance:GetServerTime()
		time_left = math.min(time_left, day_left)
	end
	if time_left < 0 then
		time_left = 0
	end
	return TimeUtil.Format2TableDHM(time_left)
end

--开服活动是否开启
function ServerActivityWGData:IsOpenSeverActivityEnd()
	return nil == self.opening_type_list[ACTIVITY_TYPE.OPEN_SERVER] or nil ~= self.close_act_chache[ACTIVITY_TYPE.OPEN_SERVER]
end

--总登录天数
function ServerActivityWGData:OnTotalLoginDays(protocol)
	self.total_login_day = protocol.total_login_day
	TimeWGCtrl.Instance:SetOnlineTime(protocol.online_time_s)
	MainuiWGCtrl.Instance:FlushView(0, "flush_ser_rec_tip")
end

--总登录天数
function ServerActivityWGData:GetTotalLoginDays()
	return self.total_login_day or 0
end

--获取当前是星期几
function ServerActivityWGData:GetNowDate()
	local date = tonumber(os.date("%w", TimeWGCtrl.Instance:GetServerTime()))
	return date
end

--获取仙盟当前排名
function ServerActivityWGData:GetMyGuildRank()
	local cur_value = 0
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if guild_id == 0 then
		cur_value = Language.OpenServer.PleaseJoinGuild
	else
		local my_guild_lv_rank = RankWGData.Instance:GetMyRank(RankClientType.XIANMENG)
		if my_guild_lv_rank and my_guild_lv_rank <= 5 then
			cur_value = string.format(Language.Guild.GuildBattleRank, my_guild_lv_rank)
		else
			cur_value = Language.Rank.NoRank
		end
	end
	return cur_value
end

--------------------------------------------------------------------------------
--至尊月卡
---------------------------------------------------------------------------------------
function ServerActivityWGData:SetMonthCardInfo(protocol)
	if IS_FREE_VERSION then
		self:DeleteActOpeningType(ACTIVITY_TIME_TYPE.MONTH_CARD)
		return
	end
	self.month_card_info.active_timestamp = protocol.active_timestamp
	self.month_card_info.last_days = protocol.last_days
	self.month_card_info.next_reward_day_idx = protocol.next_reward_day_idx
	self.month_card_info.buy_times = protocol.buy_times

	--添加IOS审核标记 如果是审核版本就不开放至尊会员
	if not IS_AUDIT_VERSION then
		--self:AddActOpeningType(ACTIVITY_TIME_TYPE.MONTH_CARD)
	end

	-- if self.month_card_info.buy_times < 1 then
	--	self:AddActOpeningType(ACTIVITY_TIME_TYPE.MONTH_CARD)
	-- else
	-- 	self:DeleteActOpeningType(ACTIVITY_TIME_TYPE.MONTH_CARD)
	-- end
end

function ServerActivityWGData:GetMonthCardInfo()
	return self.month_card_info
end

function ServerActivityWGData:GetMonthCardNum()

	local info = self:GetMonthCardInfo() or {}
	if nil == info.active_timestamp then return 0 end
	if info.buy_times < 1 then
		return 0
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()

	local day_index = TimeWGCtrl.Instance:GetDayIndex(info.active_timestamp, server_time)
	if day_index >= info.next_reward_day_idx then
		return 1
	else
		return 0
	end
end

--------------------------------------------------------------------------------------
--充值相关的活动
--------------------------------------------------------------------------------------
--特殊首充
function ServerActivityWGData:GetSpecialChongZhiRewardList(index)
	local is_unpack_gift = true
	local chongzhi_cfg_list = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").special_chongzhi_reward
	local chongzhi_cfg = chongzhi_cfg_list[index]
	if chongzhi_cfg ~= nil then
		local reward_cfg = chongzhi_cfg.reward_item
		if chongzhi_cfg == nil or reward_cfg == nil then return nil end
		return self:GetShowRewardListByCfg({reward_cfg}, is_unpack_gift)
	end
end

--获得特殊充值奖励物品列表（前三次特殊首充）
function ServerActivityWGData:GetDailyFirstChongZhiRewardList()
	local day = self:GetNowDate()
	local is_unpack_gift = true
	local chongzhi_cfg_list = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").daily_fisrt_chongzhi_reward
	local chongzhi_cfg = chongzhi_cfg_list[day]
	if chongzhi_cfg ~= nil then
		local reward_cfg = chongzhi_cfg.first_recharge
		if reward_cfg ~= nil then
			return self:GetShowRewardListByCfg({reward_cfg}, is_unpack_gift)
		end
	end
end

--充值信息
function ServerActivityWGData:SetTotalChongZhiData(protocol)
	self.chong_zhi_info = {}
	self.chong_zhi_info.history_recharge = protocol.history_recharge
	self.chong_zhi_info.history_recharge_count = protocol.history_recharge_count
	self.chong_zhi_info.today_recharge = protocol.today_recharge
	self.chong_zhi_info.reward_flag = protocol.reward_flag
	self.chong_zhi_info.diff_wd_chongzhi_value = protocol.diff_wd_chongzhi_value

	--每日累充是否开启
	self.chong_zhi_info.diff_weekday_chongzhi_is_open = protocol.diff_weekday_chongzhi_is_open
	--每日累充阶级奖励领取标记
	self.chong_zhi_info.diff_weekday_chongzhi_stage_fetch_flag = bit:d2b(protocol.diff_weekday_chongzhi_stage_fetch_flag)
	--特殊首冲开始时间戳
	self.chong_zhi_info.special_first_chongzhi_timestamp = protocol.special_first_chongzhi_timestamp								--特殊首冲记录次数
	--每日首冲是否开启
	self.chong_zhi_info.is_daily_first_chongzhi_open = protocol.is_daily_first_chongzhi_open										--每日首冲是否开启
	--每日充值奖励是否已经领取
	self.chong_zhi_info.is_daily_first_chongzhi_fetch_reward = protocol.is_daily_first_chongzhi_fetch_reward			--每日充值奖励是否已经领取
	--每日累计充值奖励领取标记
	self.chong_zhi_info.daily_total_chongzhi_fetch_reward_flag = bit:d2b(protocol.daily_total_chongzhi_fetch_reward_flag)			--每日累计充值奖励领取标记
	--累计充值当前阶段
	self.chong_zhi_info.daily_total_chongzhi_stage = protocol.daily_total_chongzhi_stage
	--每日首冲累计次数（满7次有额外奖励）
	self.chong_zhi_info.daily_first_chongzhi_times = protocol.daily_first_chongzhi_times
	--特殊首冲领取标志
	self.chong_zhi_info.special_first_chongzhi_fetch_reward_flag = bit:d2b(protocol.special_first_chongzhi_fetch_reward_flag)
	--日常累充阶段
	self.chong_zhi_info.daily_total_chongzhi_stage_chongzhi = protocol.daily_total_chongzhi_stage_chongzhi
	--再充值标志----0未充值.1可领取.2已领取
	self.chong_zhi_info.zai_chongzhi_fetch_reward_flag = protocol.zai_chongzhi_fetch_reward_flag
	--三充值标志----0未充值.1可领取.2已领取
	self.chong_zhi_info.third_chongzhi_reward_flag = protocol.third_chongzhi_reward_flag

	if self.chong_zhi_info.daily_total_chongzhi_stage > 0 then
		self:CacheActRewardData(ServerActClientId.DAILY_TOTAL_CHONGZHI, self.chong_zhi_info.daily_total_chongzhi_fetch_reward_flag, {protocol.daily_total_chongzhi_stage_chongzhi})
	end

	local daily_total_chongzhi_stage = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").daily_total_chongzhi_stage
	local stage_max = #daily_total_chongzhi_stage
	if self.chong_zhi_info.daily_total_chongzhi_stage > 0 and self.chong_zhi_info.daily_total_chongzhi_stage <= stage_max then
		self:AddActOpeningType(ACTIVITY_TIME_TYPE.OPEN_SERVER_END_7)
	else
		self:DeleteActOpeningType(ACTIVITY_TIME_TYPE.OPEN_SERVER_END_7)
	end

	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
	-- ServerActivityWGCtrl.Instance:RefreshDailyRechargeView()
	-- ServerActivityWGCtrl.Instance:RefreshRechargeAgainView()
	RechargeRewardWGCtrl.Instance:RefreshFirstRechargeView()
	RechargeRewardWGCtrl.Instance:RefreshDailyRechargeView()
	RemindManager.Instance:Fire(RemindName.FirstCharge_Tab_ShouChong)
	self:UpdataMainUiIcons()
	-- MainuiWGCtrl.Instance:UpdateGivingShow()
	self:CheckOpenChongZhiWindow()
end

function ServerActivityWGData:GetFirstChongRemind( )
	if self.chong_zhi_info.history_recharge >= RechargeWGData.Instance:GetFirstRechargeValue() then
		return 1
	end
	return 0
end

-- 20级或以上的玩家，没充值过，每次登陆强制弹首冲UI，如果充值过，超值送礼还在就弹超级送礼
function ServerActivityWGData:CheckOpenChongZhiWindow()

end

function ServerActivityWGData:UpdataMainUiIcons()
	self:SetDailyRechargeMainUiIconVisible()
	self:SetFirstRechargeMainUiIconVisible()
end

function ServerActivityWGData:IsIconOpen(fun_name)
	if nil == self.chong_zhi_info.history_recharge_count then
			return false
		end
	if fun_name == FunName.ShouChong then
		local gold = RechargeRewardWGData.Instance:GetFirstRechargeMaxNeedGold()
		local can_get = RechargeRewardWGData.CanGetFirstRechargeReward()
		return self.chong_zhi_info.history_recharge < gold or can_get

	elseif fun_name == FunName.RechargeAgain then
		local icon_visible = self.chong_zhi_info.history_recharge_count > 0 and self.chong_zhi_info.zai_chongzhi_fetch_reward_flag < 2
		return icon_visible
	elseif fun_name == FunName.RechargeThird then
		local icon_visible = self.chong_zhi_info.history_recharge_count > 1 and self.chong_zhi_info.third_chongzhi_reward_flag < 2
		return icon_visible
	elseif fun_name == FunName.DailyRecharge then
		local icon_visible = true
		if self.chong_zhi_info.is_daily_first_chongzhi_open == 1 then
			if self.chong_zhi_info.today_recharge > 0 then
				icon_visible = self.chong_zhi_info.is_daily_first_chongzhi_fetch_reward ~= 1
			end
		else
			icon_visible = false
		end
		return icon_visible
	elseif fun_name == FunName.DailyTotalRecharge then
		local icon_visible = false
		if self.chong_zhi_info.diff_weekday_chongzhi_is_open == 1 and self.chong_zhi_info.is_daily_first_chongzhi_fetch_reward == 1 then
			-- for i = 0, TotalRechargeRewardView.MAX_PAGE_COUNT - 1 do
			-- 	if self.chong_zhi_info.diff_weekday_chongzhi_stage_fetch_flag[32 - i] == 0 then
			-- 		icon_visible = true
			-- 	end
			-- end
		end
		return icon_visible
	elseif fun_name == FunName.InvestPlan then
		local touzi_info = ServerActivityWGData.Instance:GetTouZiJiHuaInfo()
		if touzi_info == nil then
			return false
		end
		-- 以前是判断没充值的话隐藏界面UI 现在改为没充值也显示投资计划
		-- local icon_visible = self.chong_zhi_info.history_recharge_count > 0 and touzi_info.active_plan_0 == 0 and touzi_info.active_plan_1 == 0
		local icon_visible = touzi_info.active_plan_0 == 0 and touzi_info.active_plan_1 == 0
		return not IS_AUDIT_VERSION and icon_visible
	end
end

--每日首充图标显示与否
function ServerActivityWGData:SetDailyRechargeMainUiIconVisible()
	if IS_FREE_VERSION then
		FunOpen.Instance:ForceCloseFunByName(FunName.DailyRecharge)
		return
	end

	local chong_zhi_info = ServerActivityWGData.Instance:GetTotalChongZhiData()

	local icon_visible = chong_zhi_info.is_daily_first_chongzhi_open == 1
	local recharge_reward_cfg = RechargeRewardWGData.Instance:GetDailyTotalRechargeReward(2)
	if recharge_reward_cfg ~= nil and chong_zhi_info.today_recharge then
		if self.first_login and chong_zhi_info.today_recharge < recharge_reward_cfg.need_recharge_gold then

			self.first_login = false
		end
	end
end

--获取特殊首冲现在是第几天
function ServerActivityWGData:GetTotalChongZhiData()
	return self.chong_zhi_info
end

function ServerActivityWGData:GetSpecialChongzhiTheDay()
	local now_format_time = TimeUtil.Format2TableDHM(TimeWGCtrl.Instance:GetServerTime())
	local open_format_time = TimeUtil.Format2TableDHM(self.chong_zhi_info.special_first_chongzhi_timestamp or 0)
	local the_day = now_format_time.day - open_format_time.day

	the_day = (the_day < 3) and the_day + 1 or 3
	return the_day
end

--特殊首冲的温馨提示数字
function ServerActivityWGData:GetFirstRechargeRemindNum()
	if nil == self.chong_zhi_info.today_recharge then
		return 0
	end
	local the_day = self:GetSpecialChongzhiTheDay()
	local special_reward_count = 0
	for i = 1, the_day do
		local reward_flag = (self.chong_zhi_info.special_first_chongzhi_fetch_reward_flag[33 - i] == 0) and 1 or 0
		special_reward_count = special_reward_count + reward_flag
	end
	if self.chong_zhi_info.history_recharge > 0 and special_reward_count > 0 then
		return special_reward_count
	end
	return 0
end

--日常首充温馨提示
function ServerActivityWGData:GetDailyFirstRechargeRemindNum()
	if nil == self.chong_zhi_info.today_recharge then
		return 0
	end
	if self.chong_zhi_info.today_recharge > 0 and
		self.chong_zhi_info.is_daily_first_chongzhi_fetch_reward ~= 1 then
		return 1
	end
	return 0
end

-- --获取每日累充的温馨提示数字
-- function ServerActivityWGData:GetDayRechargeRemindNum()
-- 	if TimeWGCtrl.Instance:GetCurOpenServerDay() > 7 then
-- 		return self:GetRemindNum(RemindId.day_total_recharge)
-- 	end
-- 	return 0
-- end

function ServerActivityWGData:GetFirstRechargeCapability()
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	if cfg == nil then return end
	return cfg[1].capability_left, cfg[1].capability_right
end
function ServerActivityWGData:GetFirstRechargeTipsCapability()
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	if cfg == nil then return end
	return cfg[1].capability_tips
end

function ServerActivityWGData:GetFirstRechargeRewardItemList(chongzhi_type, prof)
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	if not cfg or not cfg[chongzhi_type] or prof == nil then
		print_error("没有配置", chongzhi_type, prof)
		return
	end

	return cfg[chongzhi_type]["reward_item_prof_" .. prof]
end

function ServerActivityWGData:GetFirstRechargeStarLevel()
	if self.first_recharge_equip_star_level == nil then
		local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").other[1]
		self.first_recharge_equip_star_level = cfg.star_level
	end
	return self.first_recharge_equip_star_level or 0
end

function ServerActivityWGData:GetFirstRechargeDisplay(index)
	local cfg = ConfigManager.Instance:GetAutoConfig("firstchongzhirewardcfg_auto").first_chongzhi_reward
	local model_info = nil
	local role_model_id, weapon_model_id = "", ""

	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local data = cfg[index] and cfg[index]["show_model_" .. sex .. "_" .. prof]
	if data then
		model_info = Split(data, ":")
	end

	if model_info then
		role_model_id = model_info[1]
		weapon_model_id = model_info[2]
	end

	return role_model_id, weapon_model_id
end
-----------------------------
--滚动拉霸
-----------------------------
function ServerActivityWGData:SetRollMoneyData(protocol)
	self.roll_money_info = {}
	self.roll_money_info.fetch_gold_reward_times = protocol.fetch_gold_reward_times
	self.roll_money_info.fetch_coin_reward_times = protocol.fetch_coin_reward_times
	self.roll_money_info.gold_roll_num_list = protocol.gold_roll_num_list
	self.roll_money_info.gold_roll_times = protocol.gold_roll_times
	self.roll_money_info.coin_roll_num_list = protocol.coin_roll_num_list
	self.roll_money_info.coin_roll_times = protocol.coin_roll_times
	self.roll_money_info.already_roll_gold_num = protocol.already_roll_gold_num
end

function ServerActivityWGData:GetRollMoneyDataList(roll_type)
	local reward_times = 0
	local roll_times = 0
	local num_list = {}
	local is_gotten = false
	local today_roll = 0

	local PER_DEGREE = 100

	if RollMoneyEnum.ROLL_MONEY_OPERA_TYPE_ROLL_COIN == roll_type then
		reward_times = self.roll_money_info.fetch_coin_reward_times or 0
		roll_times = self.roll_money_info.coin_roll_times or 0
		num_list = self.roll_money_info.coin_roll_num_list or {0,0,0,0,0,0,0}
		is_gotten = reward_times >= 1
		if is_gotten then
			self:SetActFinish(ServerActClientId.LUCKY_ROLL_COIN)
		end
	elseif RollMoneyEnum.ROLL_MONEY_OPERA_TYPE_ROLL_GOLD == roll_type then
		reward_times = self.roll_money_info.fetch_gold_reward_times or 0
		roll_times = self.roll_money_info.gold_roll_times or 0
		num_list = self.roll_money_info.gold_roll_num_list or {0,0,0}
		today_roll = self.roll_money_info.already_roll_gold_num or 0

		local activity_finish = self:IsOpenSeverActivityEnd()
		if activity_finish then
			self:SetActFinish(ServerActClientId.LUCKY_ROLL_GOLD)
		end

		is_gotten = today_roll >= 3 or activity_finish
	end

	local roll_money_info = {}
	roll_money_info.number_data_list = {}
	roll_money_info.is_gotten = is_gotten

	for i, v in ipairs(num_list) do
		local number_data = {}
		number_data.index = i
		number_data.is_canroll = (i == roll_times + 1)
		number_data.is_open = (i <= roll_times)
		number_data.has_oppertunity = false
		if RollMoneyEnum.ROLL_MONEY_OPERA_TYPE_ROLL_COIN == roll_type then
			number_data.has_oppertunity = not is_gotten and not number_data.is_open and (i <= self.total_login_day)
		elseif RollMoneyEnum.ROLL_MONEY_OPERA_TYPE_ROLL_GOLD == roll_type then
			roll_money_info.today_degree = 0
			roll_money_info.total_need_degree = today_roll < 3 and (today_roll + 1) * PER_DEGREE or 3 * PER_DEGREE
			number_data.has_oppertunity = not is_gotten and not number_data.is_open and roll_money_info.today_degree >= roll_money_info.total_need_degree and number_data.is_canroll
		end
		number_data.number = v
		number_data.oprate_func = function()
			ServerActivityWGCtrl.Instance:SendRollMoneyOperaReq(roll_type)
		end
		table.insert(roll_money_info.number_data_list, number_data)
	end

	return roll_money_info
end

function ServerActivityWGData:GetLuckRollRemindNum(remind_id)
	-- local roll_money_info = nil
	-- if RemindId.server_fc_luck_coin == remind_id then
	-- 	roll_money_info = self:GetRollMoneyDataList(RollMoneyEnum.ROLL_MONEY_OPERA_TYPE_ROLL_COIN)
	-- end
	-- if RemindId.server_fc_luck_gold == remind_id then
	-- 	roll_money_info = self:GetRollMoneyDataList(RollMoneyEnum.ROLL_MONEY_OPERA_TYPE_ROLL_GOLD)
	-- end
	local count = 0
	-- for k,v in pairs(roll_money_info.number_data_list) do
	-- 	if v.has_oppertunity then
	-- 		count = count + 1
	-- 	end
	-- end
	-- local last_num_info = roll_money_info.number_data_list[#roll_money_info.number_data_list]
	-- if last_num_info.is_open then
	-- 	count = count + 1
	-- end
	return count
end

function ServerActivityWGData:GetSingleRewardInfo(act_id)
	if act_id == ServerActClientId.FC_JOIN_GUILD_REWARD then
		return ActCloseBetaData.Instance:GetGuildRewardInfo()
	elseif act_id == ServerActClientId.FC_MARRY_REWARD then
		return ActCloseBetaData.Instance:GetMarryRewardInfo()
	elseif act_id == ServerActClientId.FC_LOGIN_REWARD then
		return ActCloseBetaData.Instance:GetLoginRewardInfo()
	elseif act_id == ServerActClientId.RAND_KILL_BOSS then
		return self:GetRandKillBossInfo()
	-- elseif act_id == ServerActClientId.CS_GONGCHENGZHAN then
	-- 	return ActCombineData.Instance:GetChengZhuReward()
	-- elseif act_id == ServerActClientId.CS_XIANMENGZHAN then
	-- 	return ActCombineData.Instance:GetMengZhuReward()
	-- elseif act_id == ServerActClientId.CS_KILL_BOSS then
		-- return ActCombineData.Instance:GetRandKillBossInfo()
	end
end

----------------------------------------
-- 名人争夺
----------------------------------------------------
function ServerActivityWGData:GetCelebrityTitleCfg()
	local celebrity_title_cfg = self.opengameactivity_auto.title_rank
	if celebrity_title_cfg ~= nil then
		return celebrity_title_cfg
	end
	return nil
end

-- 获取称号拥有者信息
function ServerActivityWGData:GetTitleOwnerInfo()
	return self.celebrity_title_info
end
-- 保存称号拥有者信息
function ServerActivityWGData:SetTitleOwnerInfo(protocol)
	self.celebrity_title_info["xianjiezhizhun"] = {uid = protocol.xianjiezhizhun_owner_uid, name = protocol.xianjiezhizhun_owner_name}
	self.celebrity_title_info["junlintianxia"] = {uid = protocol.junlintianxia_owner_uid, name = protocol.junlintianxia_owner_name}
	self.celebrity_title_info["qingshihongyan"] = {uid = protocol.qingshihongyan_owner_uid, name = protocol.qingshihongyan_owner_name}
	self.celebrity_title_info["fengliutitang"] = {uid = protocol.fengliutitang_owner_uid, name = protocol.fengliutitang_owner_name}
	self.celebrity_title_info["guosetianxiang"] = {uid = protocol.guosetianxiang_owner_uid, name = protocol.guosetianxiang_owner_name}
	self.celebrity_title_info["kunlunzhanshen"] = {uid = protocol.kunlunzhanshen_owner_uid, name = protocol.kunlunzhanshen_owner_name}
	self.celebrity_title_info["penglaizhanshen"] = {uid = protocol.penglaizhanshen_owner_uid, name = protocol.penglaizhanshen_owner_name}
	self.celebrity_title_info["cangqiongzhanshen"] = {uid = protocol.cangqiongzhanshen_owner_uid, name = protocol.cangqiongzhanshen_owner_name}
	self.celebrity_title_info["wangchengchengzhu"] = {uid = protocol.wangchengchengzhu_owner_uid, name = protocol.wangchengchengzhu_owner_name}
	self.celebrity_title_info["zuiqiangxianmeng"] = {uid = protocol.zuiqiangxianmeng_owner_uid, name = protocol.zuiqiangxianmeng_owner_name}
	self.celebrity_title_info["weizhencangqiong"] = {uid = protocol.weizhencangqiong_onwer_uid, name = protocol.weizhencangqiong_owner_name}
	self.celebrity_title_info["bosshunter"] = {uid = protocol.bosshunter_owner_uid, name = protocol.bosshunter_owner_name}
	self.celebrity_title_info["tianxiawushuang"] = {uid = protocol.tianxiawushuang_owner_uid, name = protocol.tianxiawushuang_owner_name}
	self.celebrity_title_info["xiongbatianxia"] = {uid = protocol.xiongbatianxia_owner_uid, name = protocol.xiongbatianxia_owner_name}
end

--———————————————————————————————————————————————————
-- 随机活动
----------------------------------------------------

--随机活动-全服疯狂抢购
function ServerActivityWGData:OnRAServerPanicBuyInfo(protocol)
	self.panic_buy_data = {}
	self.panic_buy_data.user_buy_numlist = protocol.user_buy_numlist
	self.panic_buy_data.server_buy_numlist = protocol.server_buy_numlist
end

function ServerActivityWGData:GetPanicBuyItemListData(act_id)
	local item_data_list = {}
	if ServerActClientId.RAND_SERVER_PANIC_BUY == act_id then
		local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
		local rand_t = rand_config.server_panic_buy

		local server_panic_buy = self:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_SERVER_PANIC_BUY)

		for i = 1, table.maxn(server_panic_buy) do
			local v = server_panic_buy[i]
			local item_data = {}
			item_data.seq = v.seq
			item_data.gold_price = v.gold_price
			item_data.reward_item = v.reward_item
			item_data.get_callback = function()
				ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_SERVER_PANIC_BUY, opera_type = RendActOperaType.GET_ITEM, param_1 = v.seq})
			end
			local user_buy_count = self.panic_buy_data.user_buy_numlist[i]
			local server_buy_count = self.panic_buy_data.server_buy_numlist[i]
			item_data.person_limit = v.personal_limit_buy_count - user_buy_count
			item_data.server_limit = v.server_limit_buy_count - server_buy_count
			table.insert(item_data_list, item_data)
		end
	-- elseif ServerActClientId.CS_PERSONAL_PANIC_BUY == act_id then
	-- 	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	-- 	local personal_panic_buy = combine_cfg.personal_panic_buy
	-- 	for i = 0, table.maxn(personal_panic_buy) do
	-- 		local v = personal_panic_buy[i]
	-- 		local item_data = {}
	-- 		item_data.seq = v.seq
	-- 		item_data.gold_price = v.gold_price
	-- 		item_data.reward_item = v.reward_item
	-- 		local sub_type = CSActIdToSubType[act_id]
	-- 		item_data.get_callback = function()
	-- 			ServerActivityWGCtrl.Instance:SendCSARoleOperaReq(sub_type, v.seq)
	-- 		end
	-- 		local combine_person_info = ActCombineData.Instance:GetCombineRoleInfo()
	-- 		local user_buy_count = combine_person_info.personal_panic_buy_numlist[i + 1]
	-- 		item_data.person_limit = v.limit_buy_count - user_buy_count
	-- 		table.insert(item_data_list, item_data)
	-- 	end
	-- elseif ServerActClientId.CS_SERVER_PANIC_BUY == act_id then
	-- 	local combine_cfg = ServerActivityWGData.GetCurrentCombineActivityConfig()
	-- 	local server_panic_buy = combine_cfg.server_panic_buy
	-- 	local combine_person_info = ActCombineData.Instance:GetCombineRoleInfo()
	-- 	for k,v in pairs(server_panic_buy) do
	-- 		if combine_person_info.combine_server_days == v.combine_server_day then
	-- 			local item_data = {}
	-- 			item_data.seq = v.seq
	-- 			item_data.gold_price = v.gold_price
	-- 			item_data.reward_item = v.reward_item
	-- 			local sub_type = CSActIdToSubType[act_id]
	-- 			item_data.get_callback = function()
	-- 				ServerActivityWGCtrl.Instance:SendCSARoleOperaReq(sub_type, v.seq)
	-- 			end
	-- 			local user_buy_count = combine_person_info.server_panic_buy_numlist[v.seq + 1]
	-- 			-- local combine_server_info = ActCombineData.Instance:GetCombineActivityInfo()
	-- 			-- local server_buy_count = combine_server_info.server_panic_buy_num_list[i + 1]

	-- 			item_data.person_limit = v.personal_limit_buy_count - user_buy_count
	-- 			-- item_data.server_limit = v.server_limit_buy_count - server_buy_count
	-- 			table.insert(item_data_list, item_data)
	-- 		end
	-- 	end
	end
	return item_data_list
end

--随机活动-充值排行
function ServerActivityWGData:OnRAChongzhiRankInfo(protocol)
	local chongzhi_num = protocol.chongzhi_num
	self.chongzhi_num = chongzhi_num
	RankWGCtrl.Instance:SendRankListReq(RankClientType.CHONGZHI, BindTool.Bind1(self.OnChongZhiRankList, self))
end
function ServerActivityWGData:OnChongZhiRankList(rank_type, rank_list)
	local my_rank = nil
	for k,v in pairs(rank_list) do
		if v.user_id == RoleWGData.Instance.role_vo.role_id then
			my_rank = k
		end
	end
	if nil == my_rank then
		my_rank = Language.Rank.NoRank
	end
	self.act_rank_list[ServerActClientId.RAND_CHONGZHI_RANK] = rank_list
	self:CacheActRewardData(ServerActClientId.RAND_CHONGZHI_RANK, bit:d2b(0), {my_rank})
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

--随机活动-消费排行
function ServerActivityWGData:OnRAConsumeGoldRankInfo(protocol)
	local consume_gold = protocol.consume_gold_num
	self.consume_gold = consume_gold
	if ViewManager.Instance:IsOpen(GuideModuleName.JinyintaView) then
		return
	end
	RankWGCtrl.Instance:SendRankListReq(RankClientType.XIAOFEI, BindTool.Bind1(self.OnXiaoFeiRankList, self))
end
function ServerActivityWGData:OnXiaoFeiRankList(rank_type, rank_list)
	local my_rank = nil
	for k,v in pairs(rank_list) do
		if v.user_id == RoleWGData.Instance.role_vo.role_id then
			my_rank = k
		end
	end
	if nil == my_rank then
		my_rank = Language.Rank.NoRank
	end
	self.act_rank_list[ServerActClientId.RAND_CONSUME_GOLD_RANK] = rank_list
	self:CacheActRewardData(ServerActClientId.RAND_CONSUME_GOLD_RANK, bit:d2b(0), {my_rank}, bit:d2b(0))
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

function ServerActivityWGData:SetRankListData(act_id, rank_list)
	self.act_rank_list[act_id] = rank_list
end

function ServerActivityWGData:GetRankListData(act_id, rank)
	if nil ~= self.act_rank_list[act_id] then
		local rank_data_list = self.act_rank_list[act_id]
		if nil ~= rank_data_list[rank] then
			return rank_data_list[rank]
		end
	end
end

--获取排行信息
function ServerActivityWGData:GetRankListDataById(act_id)
	return self.act_rank_list[act_id]
end

--特殊当前值
function ServerActivityWGData:GetSecondCurrentValue(act_id)
	if act_id == ServerActClientId.RAND_CHONGZHI_RANK then
		return self.chongzhi_num
	elseif act_id == ServerActClientId.RAND_CONSUME_GOLD_RANK then
		return self.consume_gold
	elseif act_id == ServerActClientId.RAND_XIANMENG_BIPIN then
		return self.guild_kill_boss_count
	elseif act_id == ServerActClientId.RAND_XIANMENG_JUEQI then
		return self.guild_increase_capability
	end
end

--随机活动-消费返利
function ServerActivityWGData:OnRAConsumeGoldFanliInfo(protocol)
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	self.consume_gold_fanli_data = {}
	self.consume_gold_fanli_data.consume_gold = protocol.consume_gold
	self.consume_gold_fanli_data.consume_percent = rand_config.other[1].fanli_rate
	self.consume_gold_fanli_data.consume_fanli = math.floor(protocol.consume_gold * rand_config.other[1].fanli_rate / 100)
end

function ServerActivityWGData:GetRAConsumeGoldFanliData(act_id)
	return self.consume_gold_fanli_data
end

--随机活动-每日消费
function ServerActivityWGData:OnRADayConsumeGoldInfo(protocol)
	local consume_gold = protocol.consume_gold
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_DAY_CONSUME_GOLD, bit:d2b(fetch_reward_flag), {consume_gold})
end

--随机活动-每日活跃度信息
function ServerActivityWGData:OnRADayActiveDegreeInfo(protocol)
	local active_degree = protocol.active_degree
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_DAY_ACTIVIE_DEGREE, bit:d2b(fetch_reward_flag), {active_degree})
end

-- 随机活动-奇珍异宝
function ServerActivityWGData:OnRAChestshopInfo(protocol)
	local chestshop_times = protocol.chestshop_times
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_CHESTSHOP, bit:d2b(fetch_reward_flag), {chestshop_times})
end

--随机活动-宝石升级
function ServerActivityWGData:OnRAStoneUplevelInfo(protocol)
	local total_level = protocol.total_level
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_STONE_UPLEVEL, bit:d2b(fetch_reward_flag), {total_level})
end

--随机活动-仙女缠绵
function ServerActivityWGData:OnRAXiannvChanmianUplevelInfo(protocol)
	local chanmian_grade = protocol.chanmian_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_XN_CHANMIAN_UPLEVEL, bit:d2b(fetch_reward_flag), {chanmian_grade})
end

--随机活动-坐骑进阶
function ServerActivityWGData:OnRAMountUpgradeInfo(protocol)
	local mount_grade = protocol.mount_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_MOUNT_UPGRADE, bit:d2b(fetch_reward_flag), {mount_grade}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-骑兵进阶
function ServerActivityWGData:OnRAQibingUpgradeInfo(protocol)
	local qibing_grade = protocol.qibing_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_QIBING_UPGRADE, bit:d2b(fetch_reward_flag), {qibing_grade}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-根骨全身等级
function ServerActivityWGData:OnRAMentalityUplevelInfo(protocol)
	local total_mentality_level = protocol.total_mentality_level
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_MENTALITY_TOTAL_LEVEL, bit:d2b(fetch_reward_flag), {total_mentality_level}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-全民祈福
function ServerActivityWGData:OnRAQuanminQifuInfo(protocol)
	local qifu_times = protocol.qifu_times
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_QUANMIN_QIFU, bit:d2b(fetch_reward_flag), {qifu_times})
end

--随机活动-副本双倍掉落
function ServerActivityWGData:OnRAKillBossInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_KILL_BOSS, bit:d2b(0), {day_index = protocol.day_index, fb_open_list = protocol.fb_open_list})
end
function ServerActivityWGData:GetRandKillBossInfo()
	local cur_kill_boss = self:GetCurrentValue(ServerActClientId.RAND_KILL_BOSS)
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local need_kill_boss_count = rand_config.other[1].need_kill_boss_count

	local reward_info = {}
	reward_info.is_can_get = cur_kill_boss >= need_kill_boss_count
	reward_info.is_gotten = false
	reward_info.item_data = rand_config.other[1].kill_boss_reward
	reward_info.bind_callback = function()
		local param_t = {rand_activity_type = ACTIVITY_TYPE.RAND_KILL_BOSS, opera_type = RendActOperaType.GET_ITEM}
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	end
	return reward_info
end
function ServerActivityWGData:GetRandKillBossRemind()
	local cur_kill_boss = self:GetCurrentValue(ServerActClientId.RAND_KILL_BOSS)
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local need_kill_boss_count = rand_config.other[1].need_kill_boss_count
	if nil ~= cur_kill_boss and need_kill_boss_count then
		return cur_kill_boss >= need_kill_boss_count and 1 or 0
	else
		return 0
	end
end

--随机活动-F2仙灵古阵
function ServerActivityWGData:OnRAShouYouYuXiangInfo(protocol)
	local shouyou_yuxiang_flower_num = protocol.shouyou_yuxiang_flower_num
	local can_fetch_reward_flag = {}
	if 1 == protocol.shouyou_yuxiang_give_flower_flag then
		for i = 1, 32 do
			can_fetch_reward_flag[i] = 1
		end
	else
		can_fetch_reward_flag = bit:d2b(0)
	end
	local fetch_reward_flag = protocol.shouyou_yuxiang_fetch_flag
	self:CacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_XIANLING_ZHEN, bit:d2b(fetch_reward_flag), {shouyou_yuxiang_flower_num}, can_fetch_reward_flag)
end

--随机活动-登录领奖
function ServerActivityWGData:OnRALoginGiftInfo(protocol)
	self.rand_login_gift_info = {}
	self.rand_login_gift_info.reward_active_flag = protocol.reward_active_flag      -- 登录激活标记
	self.rand_login_gift_info.reward_fetch_flag = protocol.reward_fetch_flag        -- 领取标记
	self:CacheActRewardData(ServerActClientId.RAND_LOGIN_GIFT, bit:d2b(protocol.reward_active_flag), {}, bit:d2b(protocol.reward_fetch_flag))
end

function ServerActivityWGData:GetLoginGiftItemDataList()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local data_list = rand_config.login_gift
	return data_list
end

--随机活动-仙盟比拼
function ServerActivityWGData:OnRAXianMengBiPinInfo(protocol)
	local kill_boss_count = protocol.kill_boss_count
	self.guild_kill_boss_count = kill_boss_count

	--请求仙盟比拼数据
	-- RankWGCtrl.Instance:SendRankListReq(RankClientType.XIANMENGKILLBOSS, BindTool.Bind1(self.OnGuildKillBossRankList, self))
	-- 以前仙盟比拼请求错误了？？现在换成的是 XIANMENG_UPKILLBOSS
	RankWGCtrl.Instance:SendRankListReq(RankClientType.XIANMENG_UPKILLBOSS, BindTool.Bind1(self.OnGuildKillBossRankList, self))
end
function ServerActivityWGData:OnGuildKillBossRankList(rank_type, rank_list)
	local my_rank = nil
	for k,v in pairs(rank_list) do
		if v.guild_id == RoleWGData.Instance.role_vo.guild_id then
			my_rank = k
		end
	end
	if nil == my_rank then
		if 0 == RoleWGData.Instance.role_vo.guild_id then
			my_rank = Language.Browse.NoGuild
		else
			my_rank = Language.Rank.NoRank
		end
	end
	self:CacheActRewardData(ServerActClientId.RAND_XIANMENG_BIPIN, bit:d2b(0), {my_rank})
	--ServerActivityWGCtrl.Instance:OnGuildUpKillBossRankList(rank_type, rank_list)
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

--随机活动-仙盟崛起
function ServerActivityWGData:OnRAXianMengJueQiInfo(protocol)
	local increase_capability = protocol.increase_capability
	self.guild_increase_capability = increase_capability > 0 and increase_capability or 0

	--请求仙盟崛起数据
	RankWGCtrl.Instance:SendRankListReq(RankClientType.XIANMENG_UPZHANLI, BindTool.Bind1(self.OnGuildUpZhanLiRankList, self))
end
function ServerActivityWGData:OnGuildUpZhanLiRankList(rank_type, rank_list)
	local my_rank = nil
	for k,v in pairs(rank_list) do
		if v.guild_id == RoleWGData.Instance.role_vo.guild_id then
			my_rank = k
		end
	end
	if nil == my_rank then
		if 0 == RoleWGData.Instance.role_vo.guild_id then
			my_rank = Language.Browse.NoGuild
		else
			my_rank = Language.Rank.NoRank
		end
	end
	self:CacheActRewardData(ServerActClientId.RAND_XIANMENG_JUEQI, bit:d2b(0), {my_rank})
	ServerActivityWGCtrl.Instance:OnGuildKillUpZhanliRankList(rank_type, rank_list)
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

--随机活动-充值回馈
function ServerActivityWGData:OnChargeRewardInfo(protocol)
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	local cur_value = protocol.charge_value

	self:CacheActRewardData(ServerActClientId.RAND_CHARGE_REPALMENT, bit:d2b(fetch_reward_flag), {cur_value}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-每日单笔充值
function ServerActivityWGData:OnRADanbiChongzhiInfo(protocol)
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag

	self:CacheActRewardData(ServerActClientId.RAND_DAY_DANBI_CHONGZHI, bit:d2b(fetch_reward_flag), {99999999}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-次日福利
function ServerActivityWGData:OnRATomorrowRewardInfo(protocol)
	self.tomorrow_reward = {}
	self.tomorrow_reward.reword_count = protocol.reword_count
	self.tomorrow_reward.reward_index = protocol.reward_index
end

function ServerActivityWGData:GetRATomorrowRewardInfo()
	return self.tomorrow_reward
end

function ServerActivityWGData:GetRATomorrowRewardNum()
	if self.tomorrow_reward.reword_count > 0 then
		return 1
	else
		return 0
	end
end

--随机活动-装备兑换
function ServerActivityWGData:OnRATimeLimitExchangeEquiInfo(protocol)
	-- self:CacheActRewardData(ServerActClientId.RAND_EQUIP_EXCHANGE, bit:d2b(0), {is_table = true, {XunbaoData.Instance:GetXunbaoJiFen()}, protocol.time_list})
end

--随机活动-精灵兑换
function ServerActivityWGData:OnRATimeLimitExchangeJLInfo(protocol)
	-- self:CacheActRewardData(ServerActClientId.RAND_SPRITE_EXCHANGE, bit:d2b(0), {is_table = true, {JingLingData.Instance:GetJingLingJiFen()}, protocol.time_list})
end

--活动兑换
function ServerActivityWGData:OnRALuckydrawInfo(protocol)
	self.lucky_draw = {}
	self.lucky_draw.lucky_draw_reward_can_fetch_count = protocol.lucky_draw_reward_can_fetch_count
	self.lucky_draw.consume_gold = protocol.consume_gold
	self.lucky_draw.hit_index = protocol.hit_index
	-- Remind.Instance:DoRemind(RemindId.lottery_draw)
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

function ServerActivityWGData:GetLuckydrawInfo()
	return self.lucky_draw
end

--活动兑换
function ServerActivityWGData:OnActivityConvertInfo(protocol)
	self.tupu_times = protocol.perfect_plate_convert_mark

	-- self:AddActOpeningType(ACTIVITY_TIME_TYPE.TUPU_EXCHANGE)
end

function ServerActivityWGData:GetActivityConvertInfo()
	return self.tupu_times
end

function ServerActivityWGData:OnDailyConsumeRankList(rank_type, rank_list)
	local my_rank = nil
	for k,v in pairs(rank_list) do
		if v.user_id == RoleWGData.Instance.role_vo.role_id then
			my_rank = k
		end
	end
	if nil == my_rank then
		my_rank = Language.Rank.NoRank
	end
	self.act_rank_list[ServerActClientId.RAND_DAILY_CONSUME_RANK] = rank_list
	self:CacheActRewardData(ServerActClientId.RAND_DAILY_CONSUME_RANK, bit:d2b(0), {my_rank})
	-- ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
end

function ServerActivityWGData:GetRandActZhuanFuType()
	return self.rand_act_zhuanfu_type
end

function ServerActivityWGData:SetServerSystemInfo(protocol)
	self.rand_act_zhuanfu_type = protocol.param1

	-- 根据渠道ID 强制显示读随机配置表2(1是安卓，2是IOS)
	local plat_id = AgentAdapter:GetSpid()
	if "ihy" == plat_id or "iy1" == plat_id or "itx" == plat_id  then
		self.rand_act_zhuanfu_type = 2
	end
end

--服务器在不同阶段有不同的奖励配置表，用这个方法来读相应的配置表
function ServerActivityWGData:GetCurrentRandActivityConfig()
	-- print_error("===================",self.rand_act_zhuanfu_type)
	return ConfigManager.Instance:GetAutoConfig("randactivityconfig_" .. self.rand_act_zhuanfu_type .. "_auto")
end

--用这个方法来获取跨服活动的配置表
function ServerActivityWGData:GetCrossRandActivityConfig()
	return ConfigManager.Instance:GetAutoConfig("cross_randactivity_cfg_" .. self.rand_act_zhuanfu_type .. "_auto")
end

function ServerActivityWGData:GetRandActivityConfig(cfg, type)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local pass_day = self:GetActDayPassFromStart(type)
	local rand_t = {}
	local day = nil
	for k,v in pairs(cfg) do
		if nil ~= v.opengame_day then
			if v and (nil == day or v.opengame_day == day) and (open_day - pass_day) <= v.opengame_day then
				day = v.opengame_day
				table.insert(rand_t, v)
			end
		end
	end
	return rand_t
end

--根据开服天数给奖励
function ServerActivityWGData:GetRandHappyActivity(cfg,open_day)
	-- local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local pass_day = self:GetActDayPassFromStart(type)
	local rand_t = {}
	local day = nil
	for k,v in pairs(cfg) do
		if nil ~= v.opengame_day then
			if v and (nil == day or v.opengame_day == day) and open_day  <= v.opengame_day then
				day = v.opengame_day
				table.insert(rand_t, v)
			end
		end
	end
	return rand_t
end
function ServerActivityWGData:GetRandCfgByOpenDay(rand_cfg)
	local cfg = {}
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k,v in pairs(rand_cfg) do
		if v[1] and v[1].opengame_day then
			local rand_t = {}
			local day = nil
			for key = 0, #v do
				local val = v[key]
				if val and (nil == day or val.opengame_day == day) and open_day <= val.opengame_day then
					day = val.opengame_day
					table.insert(rand_t, val)
				end
			end
			if v[0] then
				rand_t[0] = table.remove(rand_t, 1)
			end
			cfg[k] = rand_t
		else
			cfg[k] = v
		end
	end
	return cfg
end

--根据不同平台获取相应合服配置
function ServerActivityWGData.GetCurrentCombineActivityConfig()
	local is_ios_plat = false
	local combineserveractivity_cfg = is_ios_plat and ConfigManager.Instance:GetAutoConfig("combineserveractivity_ios_auto") or ConfigManager.Instance:GetAutoConfig("combineserveractivity_auto")
	local is_enforce_cfg = GLOBAL_CONFIG.param_list.is_enforce_cfg
	if is_enforce_cfg == 1 then
		combineserveractivity_cfg = ConfigManager.Instance:GetAutoConfig("combineserveractivity_auto")
	elseif is_enforce_cfg == 2 then
		combineserveractivity_cfg = ConfigManager.Instance:GetAutoConfig("combineserveractivity_ios_auto")
	end
	return combineserveractivity_cfg
end

---------------------------------
--我要元宝
---------------------------------
function ServerActivityWGData:GetChongzhiWantmoneyInfo()
	return self.chongzhi_wantmoney_info
end

function ServerActivityWGData:SetTotalChongzhiWantMoneyFetchInfo(info)
	self.chongzhi_wantmoney_info.reward_state = info.reward_state
	self.chongzhi_wantmoney_info.history_chongzhi = info.history_chongzhi

	local total_chongzhi_want_money = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").total_chongzhi_want_money
	local list_length = (#total_chongzhi_want_money or 0)

end

function ServerActivityWGData:SetTotalChongzhiWantMoneyFetchReward(info)
	self.chongzhi_wantmoney_info.get_gold_bind = info.get_gold_bind
end

function ServerActivityWGData:GetChongzhiWantMnoeyCfg(param)
	local want_money = ConfigManager.Instance:GetAutoConfig("chongzhireward_auto").total_chongzhi_want_money
	if want_money ~= nil then
		for k,v in pairs(want_money) do
			if param == v.state then
				return v
			end
		end
	end
end

function ServerActivityWGData:GetWantGoldRemindNum()
	local stage = self.chongzhi_wantmoney_info.reward_state
	local config = self:GetChongzhiWantMnoeyCfg(stage)
	if nil == config then return 0 end
	if self.chongzhi_wantmoney_info.history_chongzhi >= config.charged
		and RoleWGData.Instance:GetIsEnoughUseGold(config.charged) then
		return 1
	end
	return 0
end

---------------------------------
--聚宝盆
---------------------------------

function ServerActivityWGData:GetIWGRollerCfg(lun)
	lun = lun or 1
	local config = self:GetCurrentRandActivityConfig().cornucopia_rate or {}
	local roller_cfg = {}
	for i,v in ipairs(config) do
		if v.lun == lun then
			table.insert(roller_cfg, v)
		end
	end
	return roller_cfg
end

-- 设置聚宝盆数据
function ServerActivityWGData:SetIWGInfo(protocol)
	self.iwg_info.reward_lun = protocol.reward_lun
	self.iwg_info.history_chongzhi = protocol.history_chongzhi
	self.iwg_info.bet_list = protocol.bet_list
	self.iwg_info.reward_all_bind_gold = protocol.reward_all_bind_gold
	if self.iwg_info.reward_lun > self:GetIwgMaxLun() then
		local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_CORNUCOPIA)
		if act_cornucopia_info and act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_CORNUCOPIA, ACTIVITY_STATUS.CLOSE, 0)
		end
	end
end

-- 获取聚宝盆数据
function ServerActivityWGData:GetIWGInfo()
	return self.iwg_info
end

-- 聚宝盆通过元宝百分比获取index
function ServerActivityWGData:GetIwgIndexByRate(reward_rate)
	local index = 0
	local lun_cfg = self:GetIWGRollerCfg(self.iwg_info.reward_lun)
	for k,v in pairs(lun_cfg) do
		if reward_rate == v.reward_rate then
			index = k
		end
	end
	return index
end

--获取本轮聚宝盆所需消费元宝
function ServerActivityWGData:GetIwgChargeByLun(lun)
	lun = lun or self.iwg_info.reward_lun
	local config = self:GetCurrentRandActivityConfig().cornucopia or {}
	for k,v in pairs(config) do
		if v.lun == lun then
			return v.need_total_charge
		end
	end
	return 0
end

--获取聚宝盆最大
function ServerActivityWGData:GetIwgMaxLun()
	local config = self:GetCurrentRandActivityConfig().cornucopia or {}

	return #config
end

--获取聚宝盆提示数量
function ServerActivityWGData:GetCounucopiaRemindNum()
	local num = 0
	local need_charge = self:GetIwgChargeByLun()
	if self.iwg_info.reward_lun <= self:GetIwgMaxLun() and
		self.iwg_info.history_chongzhi >= need_charge and
		RoleWGData.Instance:GetIsEnoughUseGold(need_charge) then
		num = num + 1
	end

	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_CORNUCOPIA) then
		num = 0
	end

	return num
end

function ServerActivityWGData:GetCounucopiaIsOpened()
	self.counucopia_is_open_view = 0
end

--获取每日累计充值数量
function ServerActivityWGData:GetTotalChargeValue()
	return self.total_charge_value
end

--获取每日累充礼包领取标记
function ServerActivityWGData:GetRewardHasFetchFlag()
	return self.reward_has_fetch_flag
end


------------------------------------------------------------------------------
--随机活动-每日在线抽奖信息
function ServerActivityWGData:SetRADailyOnlineLotteryInfo(protocol)
	self.dailyonlinelottery.dailyonlinelottery_online_time = protocol.dailyonlinelottery_online_time
	self.dailyonlinelottery.dailyonlinelottery_fetch_times = protocol.dailyonlinelottery_fetch_times
end

--获取随机活动-每日在线抽奖信息
function ServerActivityWGData:GetRADailyOnlineLotteryInfo()
	return self.dailyonlinelottery
end

--随机活动-每日在线抽奖结果
function ServerActivityWGData:SetRADailyOnlineLotteryFetchResult(protocol)
	self.online_lottery_hit_seq = protocol.hit_seq
end

--获取随机活动每日在线抽奖结果
function ServerActivityWGData:GetRADailyOnlineLotteryFetchResult()
	return self.online_lottery_hit_seq
end

--------------------------------------------------------------------------
--变身榜活动, 被变身榜活动
function ServerActivityWGData:SetSpecialAppearanceInfo(protocol)
	self.role_change_times = protocol.role_change_times

	self.bianshenbang_rank_count = protocol.rank_count
	self.festival_change_rank_list = protocol.rank_list
	table.sort(self.festival_change_rank_list, SortTools.KeyUpperSorters("change_num", "m_capablity"))
	if self.bianshenbang_rank_count > 10 then
		self.bianshenbang_rank_count = 10
	end
	local bianshen_rank_list = self:GetCurrentRandActivityConfig().special_appearance_rank
	local beibianshen_rank_list = self:GetCurrentRandActivityConfig().special_appearance_passive_rank
	self.change_rank_list = {}
	self.change_rank_list = protocol.msg_type == 2405 and __TableCopy(bianshen_rank_list) or __TableCopy(beibianshen_rank_list)

	for i = 1, 10 do
		self.change_rank_list[i].user_name = ""
		self.change_rank_list[i].change_num = 0
		self.change_rank_list[i].uid = 0
		if protocol.msg_type == 2405 then
			self.change_rank_list[i].act_type = 1
		elseif protocol.msg_type == 2406 then
			self.change_rank_list[i].act_type = 2
		end
	end

	for i=1, self.bianshenbang_rank_count do
		self.change_rank_list[i].user_name = self.festival_change_rank_list[i].user_name
		self.change_rank_list[i].change_num = self.festival_change_rank_list[i].change_num
		self.change_rank_list[i].uid = self.festival_change_rank_list[i].uid
		if protocol.msg_type == 2405 then
			self.change_rank_list[i].act_type = 1
		elseif protocol.msg_type == 2406 then
			self.change_rank_list[i].act_type = 2
		end
	end

	for k,v in pairs(self.change_rank_list) do
		if v.rank == 1 then
			self.bian_shen = table.remove(self.change_rank_list,k)
			break
		end
	end
end

function ServerActivityWGData:GetBianShenCount()
	return self.bianshenbang_rank_count
end

function ServerActivityWGData:GetBianShenRankList()
	return self.change_rank_list or {}
end

function ServerActivityWGData:GetFestivalRoleChangeTimes()
	return self.role_change_times or 0
end

---------------------------------------------------------------
--随机活动 战场争霸
function ServerActivityWGData:SetZhanchangzhenbaInfo(protocol)
	self.zhanchangzhengba_info.qunxianluandou_first = protocol.qunxianluandou_first
	self.zhanchangzhengba_info.gongchengzhan_first = protocol.gongchengzhan_first
	self.zhanchangzhengba_info.xianmengzhan_first = protocol.xianmengzhan_first

	self.zhanchangzhengba_info.qunxianluandou_begintime = protocol.qunxianluandou_begintime
	self.zhanchangzhengba_info.gongchengzhan_begintime = protocol.gongchengzhan_begintime
	self.zhanchangzhengba_info.xianmengzhan_begintime = protocol.xianmengzhan_begintime
	self.zhanchangzhengba_info.begintime = {}
	for i = 1, 3 do
		if i == 1 then
			self.zhanchangzhengba_info.role_id[i] = protocol.qunxianluandou_first
			self.zhanchangzhengba_info.begintime[i] = protocol.qunxianluandou_begintime
		elseif i == 2 then
			self.zhanchangzhengba_info.role_id[i] = protocol.gongchengzhan_first
			self.zhanchangzhengba_info.begintime[i] = protocol.gongchengzhan_begintime
		elseif i == 3 then
			self.zhanchangzhengba_info.role_id[i] = protocol.xianmengzhan_first
			self.zhanchangzhengba_info.begintime[i] = protocol.xianmengzhan_begintime
		end
	end

	-- MainuiWGCtrl.Instance:OnBattleZhengBa()
end

-- 随机活动兑换狂欢
function ServerActivityWGData:SetConvertCrazyInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY, nil, {count = protocol.count, join_role_level = protocol.join_role_level, can_convert_num_list = protocol.can_convert_num_list, convert_info_list = protocol.convert_info_list}, nil)
end

-- 随机活动累计充值
function ServerActivityWGData:SetRATotalChargeDay2Info(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_TOTAL_CHONGZHI2, bit:d2b(0), {protocol.total_charge_value}, bit:d2b(protocol.reward_has_fetch_flag))
end

-- 随机活动累计消费
function ServerActivityWGData:SetRATotalConsumeGold2Info(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_TOTAL_CONSUME_GOLD2, bit:d2b(0), {protocol.consume_gold}, bit:d2b(protocol.fetch_reward_flag))
end

-- 随机活动充值有礼
function ServerActivityWGData:SetRAChongZhiYouLiInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CHONGZHI_YOULI, bit:d2b(0), {protocol.chongzhi_count}, bit:d2b(protocol.chongzhi_jiangli_fetch_reward_flag))
end

--根据活动号获取版本活动信息
function ServerActivityWGData:GetVersonActivityInfoByActType(activity_type)
	if self.verson_activity_info[activity_type] then
		return self.verson_activity_info[activity_type]
	end
end

--获取战场争霸信息
function ServerActivityWGData:GetZhanchangzhenbaInfo()
	return self.zhanchangzhengba_info
end

--随机活动超级vip是否显示
function ServerActivityWGData:GetChaojiVipBool()
	local cfg = ConfigManager.Instance:GetAutoConfig("agent_adapt_auto").agent_adapt
	if nil ~= cfg then
		local plat_id = AgentAdapter:GetSpid()
		for k,v in pairs(cfg) do
			if v.spid == plat_id and v.show == 1 then
				return true
			end
		end
	end
	return false
end


--主界面超级vip是否显示
function ServerActivityWGData:GetSuperVipBool()
	local cfg = ConfigManager.Instance:GetAutoConfig("agent_adapt_auto").agent_adapt
	if nil ~= cfg then
		local plat_id = AgentAdapter:GetSpid()
		for k,v in pairs(cfg) do
			if v.spid == plat_id and v.show2 == 1 then
				return true
			end
		end
	end
	return false
end

-- 获取开服活动当前进阶类型
function ServerActivityWGData:GetOpenServerAdvancedType()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if -1 ~= open_day then
		local reward_cfg_list = self:GetActRewardListByActId(ServerActClientId.ADVANCED_REWARD)
		if nil ~= reward_cfg_list then
			for k,v in pairs(reward_cfg_list) do
				if open_day == v.opengame_day then
					return v.upgrade_type
				end
			end
		end
	end
	return -1
end

-- 获取开服活动全民冲榜当前进阶类型
function ServerActivityWGData:GetOpenServerAllRankType()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if -1 ~= open_day then
		local reward_cfg_list = self:GetActRewardListByActId(ServerActClientId.ALL_RANK)
		if nil ~= reward_cfg_list then
			for k,v in pairs(reward_cfg_list) do
				if open_day == v.opengame_day then
					return v.upgrade_type
				end
			end
		end
	end
	return -1
end

-- 获取开服活动全民冲榜当前排行榜信息
function ServerActivityWGData:GetOpenServerAllRankInfoType()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if -1 ~= open_day then
		local reward_cfg_list = self:GetActRewardListByActId(ServerActClientId.ALL_RANK)
		if nil ~= reward_cfg_list then
			for k,v in pairs(reward_cfg_list) do
				if open_day == v.opengame_day then
					return v.rank_type - 8
				end
			end
		end
	end
	return -1
end

-- 七天奖励当前进度和阀值
-- function ServerActivityWGData:GetSevendayGoalRewardCurVal()
-- 	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
-- 	local vo = GameVoManager.Instance:GetMainRoleVo()
-- 	local cur_value = 0

-- 	if cur_day == 1 then 		-- 等级
-- 		cur_value = vo.level
-- 	elseif cur_day == 2 then 	-- 江湖闯关次数
-- 		cur_value = FuBenPanelWGData.Instance:GetJiangHuLaterData("day")
-- 	elseif cur_day == 3 then 	-- 天命闯关次数
-- 		cur_value = FuBenPanelWGData.Instance:GetTianMingLaterData("day")
-- 	elseif cur_day == 4 then 	-- 经验闯关次数
-- 		cur_value = FuBenPanelWGData.Instance:GetExpLayerData()
-- 	elseif cur_day == 5 then 	-- 竞技场排名
-- 		cur_value = Field1v1WGData.Instance:GetRankByUid(vo.role_id)
-- 	elseif cur_day == 6 then 	-- 跨服1V1胜场
-- 		cur_value = KuafuOnevoneWGData.Instance:Get1V1Info().cross_day_win_1v1_count
-- 	elseif cur_day == 7 then 	-- 战力
-- 		cur_value = vo.capability
-- 	end

-- 	return cur_value
-- end

-- 获取七天目标奖励领取限制
function ServerActivityWGData:GetSevendayGoalRewardLimit(day, seq)
	local cfg = self.opengameactivity_auto.sevenday_goal
	for i = 1, #cfg do
		if day == cfg[i].opengame_day and seq == cfg[i].seq then
			return cfg[i].cond_param
		end
	end
end


------------------------------------------------------------
-- 坐骑充值
function ServerActivityWGData:OnRAMountChargeInfo(protocol)
      self:CacheActRewardData(ServerActClientId.RAND_MOUNT_CHARGE, bit:d2b(protocol.fetch_reward_flag), {protocol.day_chongzhi})
end

-- 羽翼充值
function ServerActivityWGData:OnRAWingChargeInfo(protocol)
      self:CacheActRewardData(ServerActClientId.RAND_WING_CHARGE, bit:d2b(protocol.fetch_reward_flag), {protocol.day_chongzhi})
end

-- 神武充值
function ServerActivityWGData:OnRAShenwuChargeInfo(protocol)
      self:CacheActRewardData(ServerActClientId.RAND_SHENWU_CHARGE, bit:d2b(protocol.fetch_reward_flag), {protocol.day_chongzhi})
end

-- 法宝充值
function ServerActivityWGData:OnRAFabaoChargeInfo(protocol)
      self:CacheActRewardData(ServerActClientId.RAND_FABAO_CHARGE, bit:d2b(protocol.fetch_reward_flag), {protocol.day_chongzhi})
end

-- 灵宠充值
function ServerActivityWGData:OnRALingchongChargeInfo(protocol)
      self:CacheActRewardData(ServerActClientId.RAND_LINGCHONG_CHARGE, bit:d2b(protocol.fetch_reward_flag), {protocol.day_chongzhi})
end

-- 灵骑充值
function ServerActivityWGData:OnRALingqiChargeInfo(protocol)
      self:CacheActRewardData(ServerActClientId.RAND_LINGQI_CHARGE, bit:d2b(protocol.fetch_reward_flag), {protocol.day_chongzhi})
end

-- 灵弓充值
function ServerActivityWGData:OnRALinggongChargeInfo(protocol)
      self:CacheActRewardData(ServerActClientId.RAND_LINGGONG_CHARGE, bit:d2b(protocol.fetch_reward_flag), {protocol.day_chongzhi})
end

-- 灵翼充值
function ServerActivityWGData:OnRALingyiChargeInfo(protocol)
      self:CacheActRewardData(ServerActClientId.RAND_LINGYI_CHARGE, bit:d2b(protocol.fetch_reward_flag), {protocol.day_chongzhi})
end

------------------------------------------------------------
-- 坐骑抢购
function ServerActivityWGData:OnRAMountShopInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_MOUNT_SHOP, bit:d2b(protocol.shop_buy_flag), {0})
end

-- 羽翼抢购
function ServerActivityWGData:OnRAWingShopInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_WING_SHOP, bit:d2b(protocol.shop_buy_flag), {0})
end

-- 法宝抢购
function ServerActivityWGData:OnRAFabaoShopInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_FABAO_SHOP, bit:d2b(protocol.shop_buy_flag), {0})
end

-- 神武抢购
function ServerActivityWGData:OnRAShenwuShopInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_SHENWU_SHOP, bit:d2b(protocol.shop_buy_flag), {0})
end

-- 灵宠抢购
function ServerActivityWGData:OnRALingchongShopInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_LINGCHONG_SHOP, bit:d2b(protocol.shop_buy_flag), {0})
end

-- 灵骑抢购
function ServerActivityWGData:OnRALingqiShopInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_LINGQI_SHOP, bit:d2b(protocol.shop_buy_flag), {0})
end

-- 灵弓抢购
function ServerActivityWGData:OnRALinggongShopInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_LINGGONG_SHOP, bit:d2b(protocol.shop_buy_flag), {0})
end

-- 灵翼抢购
function ServerActivityWGData:OnRALingyiShopInfo(protocol)
	self:CacheActRewardData(ServerActClientId.RAND_LINGYI_SHOP, bit:d2b(protocol.shop_buy_flag), {0})
end

function ServerActivityWGData:GetRushPurchaseDataList(act_id)
	local act_client_cfg = self:GetClientActCfg(act_id)
	if nil == act_client_cfg then
		return
	end

	local reward_cfg_list = self:GetActRewardListByActId(act_id) or {}
	local data_list = {}

	for i = 0, table.maxn(reward_cfg_list) do
		local v = reward_cfg_list[i]
		if v ~= nil then
			local data = {}
			data.is_buy = self:GetRewardIsGeted(act_id, v.seq)
			data.get_type = act_client_cfg.get_type
			data.seq = v.seq
			data.item_data = v.buy_item[0]
			data.show_price = v.origin_gold
			data.price = v.cost_gold
			table.insert(data_list, data)
		end
	end
	return data_list
end

--随机活动-神武进阶
function ServerActivityWGData:OnRAShenwuUpgradeInfo(protocol)
	local shenwu_grade = protocol.shenwu_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_SHENWU_UPGRADE, bit:d2b(fetch_reward_flag), {shenwu_grade}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-法宝进阶
function ServerActivityWGData:OnRAFabaoUpgradeInfo(protocol)
	local fabao_grade = protocol.fabao_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_FABAO_UPGRADE, bit:d2b(fetch_reward_flag), {fabao_grade}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-羽翼进阶
function ServerActivityWGData:OnRAWingUpgradeInfo(protocol)
	local wing_grade = protocol.wing_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_WING_UPGRADE, bit:d2b(fetch_reward_flag), {wing_grade}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-灵宠进阶
function ServerActivityWGData:OnRALingchongUpgradeInfo(protocol)
	local lingchong_grade = protocol.lingchong_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_LINGCHONG_UPGRADE, bit:d2b(fetch_reward_flag), {lingchong_grade}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-灵骑进阶
function ServerActivityWGData:OnRALingqiUpgradeInfo(protocol)
	local lingqi_grade = protocol.lingqi_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_LINGQI_UPGRADE, bit:d2b(fetch_reward_flag), {lingqi_grade}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-灵弓进阶
function ServerActivityWGData:OnRALinggongUpgradeInfo(protocol)
	local linggong_grade = protocol.linggong_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_LINGGONG_UPGRADE, bit:d2b(fetch_reward_flag), {linggong_grade}, bit:d2b(can_fetch_reward_flag))
end

--随机活动-灵翼进阶
function ServerActivityWGData:OnRALingyiUpgradeInfo(protocol)
	local lingyi_grade = protocol.lingyi_grade
	local can_fetch_reward_flag = protocol.can_fetch_reward_flag
	local fetch_reward_flag = protocol.fetch_reward_flag
	self:CacheActRewardData(ServerActClientId.RAND_LINGYI_UPGRADE, bit:d2b(fetch_reward_flag), {lingyi_grade}, bit:d2b(can_fetch_reward_flag))
end

--获取打怪掉落的活动配置
function ServerActivityWGData:GetMonsterDropCfg()

end

--获取随机活动双倍掉落配置
function ServerActivityWGData:GetDoubleDropCfg()
	local cfg = self:GetCurrentRandActivityConfig()
	if nil == cfg then
		return nil
	end
	return cfg.fb_double_drop
end

--周末boss
function ServerActivityWGData:GetWeekendBossDataList()
	if nil == next(self.scene_item_list) then
		local scene_list = self:GetCurrentRandActivityConfig().weekend_boss_flush_scene
		for i = 1, #scene_list do
			self.scene_item_list[i] = {}
			self.scene_item_list[i].scene_id = scene_list[i].scene_id
			self.scene_item_list[i].boss_num = 0
			self.scene_item_list[i].scene_level = scene_list[i].scene_level
		end
	end
	return self.scene_item_list
end

function ServerActivityWGData:SetWeekendBossData(protocol)
	self.weekend_boss_id = protocol.boss_id
	if nil ~= next(protocol.scene_item_list) then
		local scene_list = self:GetWeekendBossDataList()
		for i = 1, #scene_list do
			for k, v in pairs(protocol.scene_item_list) do
				if scene_list[i].scene_id == v.scene_id then
					scene_list[i].boss_num = v.boss_num
				end
			end
		end
	end
end

function ServerActivityWGData:GetIsCanAutoPick(monster_id, scene_id)
	local act_status = ServerActivityWGData.Instance:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_WEEKEND_BOSS)
	if not act_status or act_status.status ~= ACTIVITY_STATUS.OPEN then
		return true
	end
	if self.weekend_boss_id == monster_id then
		for i = 1, #self.scene_item_list do
			if self.scene_item_list[i].scene_id == scene_id then
				return false
			end
		end
	end
	return true
end

function ServerActivityWGData:GetWeekendBossId()
	return self.weekend_boss_id
end

function ServerActivityWGData:GetWeekendIsOpenToday()
	return self.is_open_weekend_boss
end

function ServerActivityWGData:SetVersionActivityNowStatus(act_type, status, next_status_time)
	self.verson_act_status[act_type] = {
		["type"] = act_type,
		["status"] = status,
		["next_status_time"] = next_status_time,
	}
end

function ServerActivityWGData:GetVersionActivityNowStatus(act_type)
	return self.verson_act_status[act_type]
end

-------------------------------七日返利----------------------------
function ServerActivityWGData:OnSCQitianXiaofeiFanliInfo(protocol)
	self.sevenday_data.consume_data = protocol.consum_gold_per_day
  	self.sevenday_data.fetch_flag = bit:d2b(protocol.fretch_falg)
  	self.sevenday_data.activity_day_index = protocol.activity_day_index + 1
end

function ServerActivityWGData:GetQitianXiaofeiInfo()
	return self.sevenday_data
end

-- 领取奖励标记
function ServerActivityWGData:GetFetchFlagByIndex(index)
	local data = self:GetQitianXiaofeiInfo()
	local fetch_flag = data.fetch_flag[32 - index]
	return fetch_flag
end

-- 当前天数累计消费金额
function ServerActivityWGData:GetTotalYuanbaoByIndex(index)
	local data = self:GetQitianXiaofeiInfo()
	local consume_gold = 0
	for i=1,7 do
		if i <= index then
			consume_gold = consume_gold + data.consume_data[i]
		end
	end
	return consume_gold
end

function ServerActivityWGData:GetRemainingTiem()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SEVENDAY)
	if nil == activity_status then
		return TimeUtil.Format2TableDHM(0)
	end
	local end_time = activity_status.start_time + GameEnum.SEVENDAY_DISCOUNT_DAY * 60 * 60 * 24
	local format_time = os.date("*t", end_time)
	local end_zero_time = os.time{year=format_time.year, month=format_time.month, day=format_time.day, hour=0, min = 0, sec=0}
	local time_left = end_zero_time - TimeWGCtrl.Instance:GetServerTime()
	if time_left < 0 then
		time_left = 0
	end
	return TimeUtil.Format2TableDHM(time_left)
end

-- 开服活动字帖兑换提醒
function ServerActivityWGData:GetActExchagneRemind()
	local opengame_cfg = self:GetOpenGameActivityConfig()
	local data_list = opengame_cfg.word_exchange
	local item_id_list = {}
	local num = 0
	for i,v in ipairs(data_list) do
		local need_item_num = 0
		for k=1,5 do
			if 0 ~= v["item_id_" .. k] then
				need_item_num = need_item_num + 1
			end
		end
		local have_item_num = 0
		for k=1,5 do
			if 0 ~= v["item_id_" .. k] then
				local exchange_flag = self:GetOpenServerData().oga_word_exchange_flag[v.seq + 1]
				if v.limit - exchange_flag > 0 then
					local item_num = ItemWGData.Instance:GetItemNumInBagById(v["item_id_" .. k])
					if item_num > 0 then
						have_item_num = have_item_num + 1
					end
				end
			end
			if have_item_num >= need_item_num then
				return 1
			end
		end
	end
	return 0
end

-- 开服活动 仙盟争霸领取提醒 策划说不要了
-- function ServerActivityWGData:GetGuildContentRemind()
-- 	if not ServerActivityWGData.Instance:ActIsOpenByActId(ServerActClientId.OPEN_INNEGLECTABLE) then
-- 		return 0
-- 	end
-- 	if IsEmptyTable(self.open_game_info) then
-- 		return 0
-- 	elseif 0 <= self.open_game_info.oga_guild_battle_reward_type and 0 == self.open_game_info.oga_guild_battle_reward_flag then
-- 		return 1
-- 	end
-- 	return 0
-- end

-- 群雄逐鹿红点
function ServerActivityWGData:GetGuildContentRemind1()
	if IsEmptyTable(self.open_game_info) then
		return 0
	elseif 0 <= self.open_game_info.oga_guild_battle_reward_type and 0 == self.open_game_info.oga_guild_battle_reward_flag then
 		return 1
	end
	return 0
end

-- 开服活动 累计消费领取提醒
function ServerActivityWGData:GetTotalConsumeRemind()
	local flag_t = bit:d2b(self.open_game_info.oga_total_consume_reward_fetch_flag)
	local consume_cfg = self:GetOpenGameActivityConfig().total_consume
	for i,v in ipairs(consume_cfg) do
		if v.consume_gold <= self.open_game_info.oga_total_consume_num and 0 == flag_t[32 - v.seq] then
			return 1
		end
	end

	return 0
end

-- 开服活动 累计充值领取提醒
function ServerActivityWGData:GetTotalChongzhiRemind()
	if not ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.SEVENDAY_RECHARGE) then
		return 0
	end
	
	local chongzhi_num = self.open_game_info.oga_total_chongzhi_num
	if chongzhi_num and chongzhi_num > 0 and self.open_game_info.oga_total_chongzhi_reward_fetch_flag then
		local flag_t = bit:d2b_two(self.open_game_info.oga_total_chongzhi_reward_fetch_flag)
		local consume_cfg = self:GetOpenGameActivityConfig().total_chongzhi
		for i,v in ipairs(consume_cfg) do
			if v.chongzhi_gold <= chongzhi_num and 0 == flag_t[v.seq] then
				return 1
			end
		end
	end

	return 0
end

--  开服活动 完美情人
function ServerActivityWGData:GetActLoverRemind()
	local level_limit = MarryWGData.Instance:GetMarryOtherCfg().marry_limit_level
	local marry_flag = self.open_game_info.oga_marry_type_record_flag
	local count = bit:d2b1n(marry_flag)
	if self.login_lovers_remind and RoleWGData.Instance.role_vo.level >= level_limit and count < 3 then
		return 1
	end
	return 0
end

function ServerActivityWGData:GetActExchagneItemList()
	local data_list = self:GetOpenGameActivityConfig().word_exchange
	local item_list = data_list[#data_list]
	local remind_item = {}
	for i = 1, 5 do
		table.insert(remind_item, item_list["item_id_" .. i])
	end
	return remind_item
end

function ServerActivityWGData:GetActBenActRemind(remind_id)
	if RemindName.rand_login_gift == remind_id then
		return self:GetActBenLoginGiftRemind()
	end

	if RemindName.rand_exchange_crazy == remind_id then
		return self:GetExchagneCrazyRemind()
	end

	if RemindName.rand_recharge_gift == remind_id then
		return self:GetActBenRechargeGiftRemind()
	end

	if RemindName.rand_total_recharge == remind_id then
		return self:TotalRechargeViewRemind()
		-- return self:GetActBenTotalRechargeRemind() --153的充值活动
	end

	if RemindName.rand_total_consume == remind_id then
		return self:GetActBenTotalConsumeRemind()
	end

	if RemindName.rand_xunbaocarnival == remind_id then
		return self:GetActBenXunbaoCarnivalRemind()
	end

	if RemindName.rand_charge_repayment == remind_id then
		return self:GetChargeRepayMent2ViewRemind()
	end

	if RemindName.rand_consumedis_count == remind_id then --连消特惠
		return self:GetConsumeDisCountViewRemind()
	end

	-- if RemindName.rand_continuousre_charge == remind_id then --连续充值
	-- 	return self:GetContinueChongZhiViewRemind()
	-- end

	if RemindName.rand_act_gobal_xunbao == remind_id then --连续充值
		return self:GetGlobalXunBaoViewRemind()
	end

	if RemindName.rand_act_continue_consume == remind_id then --累消返利
		return self:GetContinuwConsumeViewRemind()
	end

	if RemindName.WeekendBoss == remind_id then --周末BOSS
		return self:GetWeekendBossViewRemind()
	end
end

function ServerActivityWGData:GetActBenLoginGiftRemind()
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_LOGIN_GIFT)
	if info == nil then return 0 end
	local reward_cfg = ServerActivityWGData.Instance:GetActivityItemDataList(ServerActClientId.RAND_LOGIN_GIFT)
	for i=1, 32 do
		local is_limit = (reward_cfg[33 - i] and reward_cfg[33 - i].role_level_min > RoleWGData.Instance.role_vo.level) and true or false
		if info.reward_flag[i] == 1 and info.can_reward_flag[i] == 0 and not is_limit then
			return 1
		end
	end
	return 0
end

function ServerActivityWGData:GetExchagneCrazyRemind()
	local reward_cfg = ServerActivityWGData.Instance:GetActRewardListByActId(ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY)
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY)
	if info then
		for i,v in ipairs(reward_cfg) do
			if ItemWGData.Instance:GetItemNumInBagById(v.consume_stuff_id) >= v.consume_stuff_num and v.max_convert_num > info.cur_value_t.convert_info_list[i] then
				return 1
			end
		end
	end
	return 0
end

function ServerActivityWGData:GetExchagneCrazyItemList()
	local reward_cfg = ServerActivityWGData.Instance:GetActRewardListByActId(ServerActClientId.RAND_ACTIVITY_TYPE_CONVERT_CRAZY)
	local item_list = {}
	for i,v in ipairs(reward_cfg) do
		table.insert(item_list, v.consume_stuff_id)
	end
	return item_list
end

function ServerActivityWGData:GetActBenRechargeGiftRemind()
	local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_ACTIVITY_TYPE_CHONGZHI_YOULI)
	local reward_cfg = ServerActivityWGData.Instance:GetActivityItemDataListNew(ServerActClientId.RAND_ACTIVITY_TYPE_CHONGZHI_YOULI)

	local count = 0
	if info and reward_cfg then
		if info.cur_value_t and reward_cfg[1] and info.cur_value_t[1] >= reward_cfg[1].chongzhi_edu  then
			if info.can_reward_flag and 1 ~= info.can_reward_flag[32] then
				count = 1
			end
		end
	end
	return count
end

function ServerActivityWGData:GetActBenTotalRechargeRemind()
	local reward_cfg = ServerActivityWGData.Instance:GetActivityItemDataListNew(ServerActClientId.RAND_TOTAL_CHONGZHI2)
	for i,v in ipairs(reward_cfg) do
		if v.is_geted and not v.is_have_opp then
			return 1
		end
	end
	return 0
end

function ServerActivityWGData:GetActBenTotalConsumeRemind()
	local reward_cfg = ServerActivityWGData.Instance:GetActivityItemDataListNew(ServerActClientId.RAND_ACTIVITY_TYPE_TOTAL_CONSUME_GOLD2)
	if reward_cfg then
		for i,v in ipairs(reward_cfg) do
			if v.is_geted and not v.is_have_opp then
				return 1
			end
		end
	end
	return 0
end

function ServerActivityWGData:GetOpenServerActivityConfigBy(act_id)
	if act_id == nil then return end
	local act_client_cfg = ServerActivityWGData.Instance:GetClientActCfg(act_id)
	local rand_config = self:GetOpenGameActivityConfig()
	if act_client_cfg ~= nil and act_client_cfg.big_type ~= nil then
		return rand_config[act_client_cfg.big_type]
	end
end

--折扣礼包
function ServerActivityWGData:GetZheKouReward(open_day)
	local discount_gift = self.opengameactivity_auto.discount_gift
	local data = {}
	for k,v in pairs(discount_gift) do
		if v.opengame_day == open_day then
			table.insert(data,v)
		end
	end
	return data
end
function ServerActivityWGData:SetZheKouInfo(protocol)
	self.zhekou_info = protocol.buy_count_list
end
function ServerActivityWGData:GetZheKouInfo(seq)
	for k,v in pairs(self.zhekou_info) do
		if seq == k - 1 then
			return v
		end
	end
end

function ServerActivityWGData:GetZheKouDescInfo(open_day)
	local discount_gift = self.opengameactivity_auto.discount_describe
	for i,v in ipairs(discount_gift) do
		if v.activity_open == open_day then
			return v
		end
	end
end

--首充团购
function ServerActivityWGData:GetGroupBuyReward(act_id)
	if act_id == ServerActClientId.FIRST_CHONGZHI_GROUP_BUY then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if open_day == nil then return end

		local combine_cfg = self:GetOpenServerActivityConfigBy(act_id)

		local today_cfg = {}
		local data = {}
		local today_recharge = RechargeWGData.Instance:GetTodayRecharge()
		local first_charge_person_num = self:GetOpenServerData().first_charge_person_num
		local flag_list = self:GetBuyRewardFlag()
		for k,v in ipairs(combine_cfg) do
			if open_day == v.opengame_day then
				data = __TableCopy(v)
				local is_get = false
				if today_recharge >= data.fetch_need_min_chongzhi_value and first_charge_person_num >= data.groupbuy_active_need_person
					and flag_list[32 - v.seq] < 1 then
					is_get = true
				end
				data.is_get = is_get
				table.insert(today_cfg, data)
			end
		end
		return today_cfg
	end
end

--获取列表信息
function ServerActivityWGData:GetCurrentActivityDataList(act_id, data_list, param1)
	if act_id == ServerActClientId.FIRST_CHONGZHI_GROUP_BUY then
		local today_recharge = RechargeWGData.Instance:GetTodayRecharge()
		local first_charge_person_num = self:GetOpenServerData().first_charge_person_num
		local flag_list = self:GetBuyRewardFlag()
		if data_list == nil and param1 == nil then return end
		local item_list = {}
		local item_data = {}
		local str = ""
		local num = -1
		local img_flag = -1
		local img_num = -1

		for k,v in ipairs(data_list) do
			if param1 == v.groupbuy_active_need_person then
				local btn_effect = false
				if today_recharge >= v.fetch_need_min_chongzhi_value and first_charge_person_num >= v.groupbuy_active_need_person and flag_list[32 - v.seq] < 1 then
					btn_effect = true
				end
				local color = first_charge_person_num >= v.groupbuy_active_need_person and "#7cffb7" or "#df140f"
				if v.fetch_need_min_chongzhi_value < 1 then
					str = string.format(Language.Activity.GroupBuyingTips1, v.groupbuy_active_need_person)
					num = ToColorStr(string.format('(%d/%d)',first_charge_person_num, v.groupbuy_active_need_person),color)
					img_flag = 1
				elseif v.fetch_need_min_chongzhi_value < 2 then
					str = string.format(Language.Activity.GroupBuyingTips1, v.groupbuy_active_need_person)
					num = ToColorStr(string.format('(%d/%d)',first_charge_person_num, v.groupbuy_active_need_person),color)
					img_flag = 2
				else
					str = string.format(Language.Activity.GroupBuyingTips1, v.groupbuy_active_need_person)
					num = ToColorStr(string.format('(%d/%d)',first_charge_person_num, v.groupbuy_active_need_person),color)
					img_flag = 3
					img_num = v.fetch_need_min_chongzhi_value
				end
				item_data = __TableCopy(v)
				item_data.chongzhi = today_recharge
				item_data.effect = btn_effect
				item_data.des = str
				item_data.num = num
				item_data.img_flag = img_flag
				item_data.img_num = img_num
				item_data.flag = flag_list[32 - v.seq]

				table.insert(item_list, item_data)
			end
		end
		return item_list
	end
end

function ServerActivityWGData:GetGroupBuyingRemind()
	if ActivityWGCtrl.Instance:IsOpenServerOpen() then
		if RechargeWGData.Instance:GetHistoryRecharge() <= 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FIRST_CHONGZHI_GROUP_BUY, 1, function()
				ServerActivityWGCtrl.Instance:OpenByActId(ServerActClientId.FIRST_CHONGZHI_GROUP_BUY, true)
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FIRST_CHONGZHI_GROUP_BUY, 0)
			end)
		else
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FIRST_CHONGZHI_GROUP_BUY, 0)
		end
	end
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if open_day then
		local combine_cfg = self:GetOpenServerActivityConfigBy(ServerActClientId.FIRST_CHONGZHI_GROUP_BUY)
		local today_recharge = RechargeWGData.Instance:GetTodayRecharge()
		local first_charge_person_num = self:GetOpenServerData().first_charge_person_num
		local flag_list = self:GetBuyRewardFlag()
		for k,v in ipairs(combine_cfg) do
			if open_day == v.opengame_day then
				if today_recharge >= v.fetch_need_min_chongzhi_value and first_charge_person_num >= v.groupbuy_active_need_person
					and flag_list[32 - v.seq] < 1 then
					return 1
				end
			end
		end
		return 0
	end
	return 0
end

function ServerActivityWGData:GetEverydayShopRemind()
	if ActivityWGCtrl.Instance:IsOpenServerOpen() then
		if self:GetEveryDayShopClickRemind() == 0 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EVERYDAY_SHOP, 1, function()
				ServerActivityWGCtrl.Instance:OpenByActId(ServerActClientId.EVERYDAY_SHOP, true)
				MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EVERYDAY_SHOP, 0)
			end)
			return 1
		else
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EVERYDAY_SHOP, 0)
		end
	end
	return 0
end

function ServerActivityWGData:GetHotsellRemind()
	return ActCombineData.Instance:GetCombineRoleInfo().csa_recharge_draw_count or 0
end

function ServerActivityWGData:GetConsumeGiftRemind()
	local num = 0
	local data_list = ActCombineData.Instance:GetCommonOneViewData(ServerActClientId.CS_TYPE_CONSUME)
	if data_list then
		for k,v in pairs(data_list) do
			if v.btn_enabled and v.btn_text == Language.Common.LingQu then
				num = num + 1
			end
		end
	end
	return num
end

function ServerActivityWGData:GetCollectionRemind()
	local data_list = ActCombineData.Instance:GetCombineActivityConfigBy(ServerActClientId.CS_COLLECT_WORD)

	for k,v in pairs(data_list) do
		for i = 1,4 do
			if v["item_id_" .. i] ~= 0 and ItemWGData.Instance:GetItemNumInBagById(v["item_id_" .. i]) <= 0 then
				break
			end
			if i == 4 then
				return 1
			end
		end
	end
	return 0
end

function ServerActivityWGData:GetCsaLoginGiftRemind()
	local info = ActCombineData.Instance:GetActLoginGiftData(ServerActClientId.CS_LOGIN_REWARD)
	if info == nil then return 0 end
	for k,v in pairs(info.login_gift_cfg)do
		if v.combine_days == info.login_days then
			local role_level = RoleWGData.Instance:GetRoleLevel()
			if info.reward_flag[31 - v.seq] == 0 and role_level >= info.min_level then
				return 1
			end
			break
		end
	end
	return 0
end

function ServerActivityWGData:GetGroupBuyingTitleFlag(num)
	local today_recharge = RechargeWGData.Instance:GetTodayRecharge()
	local first_charge_person_num = self:GetOpenServerData().first_charge_person_num
	local flag_list = self:GetBuyRewardFlag()
	local data_list = self:GetGroupBuyReward(ServerActClientId.FIRST_CHONGZHI_GROUP_BUY)
	for k,v in ipairs(data_list) do
		if num == v.groupbuy_active_need_person then
			if today_recharge >= v.fetch_need_min_chongzhi_value and first_charge_person_num >= v.groupbuy_active_need_person
				and flag_list[32 - v.seq] < 1 then
				return true
			end
		end
	end
	return false
end

function ServerActivityWGData:OnChestshopCarnivalInfo(protocol)
	self.xunbao_carnival = {}
	self.xunbao_carnival.chestshop_carnival_score = protocol.chestshop_carnival_score
	self.xunbao_carnival.chestshop_carnival_fetch_reward_flag = bit:d2b(protocol.chestshop_carnival_fetch_reward_flag)
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind()
	-- Remind.Instance:DoRemind(RemindId.rand_xunbaocarnival)
end

--全民庆典返回
function ServerActivityWGData:OnSCNationalCelebrationInfo(protocol)
	self.gobal_xunbao.commit_times = protocol.commit_times or 0
	self.gobal_xunbao.fetch_flag = protocol.fetch_flag or 0
	self.gobal_xunbao.server_commit_times = protocol.server_commit_times or 0
	self.gobal_xunbao.max_progress_var = protocol.max_progress_var or {}
	ServerActivityWGCtrl.Instance:UpdataViewAndRemind(ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
end

--获取全民寻宝背包中种子数量及需要提交一次的最大数量
function ServerActivityWGData:OnGobalXubBaoZhongZiData()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_three = rand_config.other
	local other_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_three, ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].national_celebration_commit_item_id)
	return item_num, 5
end

function ServerActivityWGData:GetGobalXunbao()
	return self.gobal_xunbao
end

function ServerActivityWGData:GetGobalProgrss()
	-- local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	-- local gift_list_cfg = rand_config.nationa_celebration_progress
	-- local max = gift_list_cfg[#gift_list_cfg].need_progress_var
	-- return self.gobal_xunbao.server_commit_times / max

	local times = self.gobal_xunbao.server_commit_times
	local rand_config = self:GetCurrentRandActivityConfig()
	local gift_list_cfg = rand_config.nationa_celebration_progress
	-- local people_var = self.gobal_xunbao.max_progress_var/gift_list_cfg[#gift_list_cfg].need_progress_var
	local percent = 0
	for i=1,#gift_list_cfg - 1 do
		if times <= self.gobal_xunbao.max_progress_var[1] then
			percent = 0.07 * times / self.gobal_xunbao.max_progress_var[1]
		elseif times >= self.gobal_xunbao.max_progress_var[i] and times <= self.gobal_xunbao.max_progress_var[i+1] then
			local p1 = self:GetPercent(i)
			local p2 = 0.12 * (times - self.gobal_xunbao.max_progress_var[i])/ (self.gobal_xunbao.max_progress_var[i+1] - self.gobal_xunbao.max_progress_var[i])
			percent = p1 + p2
		elseif times > self.gobal_xunbao.max_progress_var[i+1] then
			percent = 1
		end
	end
	return percent
end

function ServerActivityWGData:GetPercent(k)
	local pingfen_list = {0.07, 0.19, 0.32, 0.44, 0.56, 0.68, 0.80, 0.92}
	for i,v in ipairs(pingfen_list) do
		if k == i then
			return v
		end
	end
	return nil
end

--合服活动寻宝狂欢
function ServerActivityWGData:GetActXunbaoCarnivalData(act_id)
	if act_id == ServerActClientId.RAND_ACTIVITY_TYPE_XUNBAO_CARNIVAL then
		if nil == self.xunbao_carnival then
			return nil
		end
		local act_client_cfg = self:GetClientActCfg(act_id)
		local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
		local chestshop_carnival_reward = rand_config.chestshop_carnival_reward

		if chestshop_carnival_reward == nil or act_client_cfg == nil then
			return nil
		end

		local carnival_score = self.xunbao_carnival.chestshop_carnival_score or 0
		local fetch_reward = self.xunbao_carnival.chestshop_carnival_fetch_reward_flag
		local is_unpack_gift = (1 == act_client_cfg.is_unpack_gift)

		local item_data_list = {}
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
		local open_day_cfg = nil
		for k ,v in ipairs(chestshop_carnival_reward)do
			if open_day_cfg == nil and open_day <= v.opengame_day then
				open_day_cfg = v.opengame_day
			end

			if open_day_cfg ~= nil and open_day_cfg == v.opengame_day then
				local item_data = {}
				item_data.reward_flag = fetch_reward[32 - v.seq] == 1
				item_data.canlingqu = v.need_score <= carnival_score
				item_data.is_enabled = v.need_score <= carnival_score
				item_data.reward_item = ServerActivityWGData.Instance:GetShowRewardListByCfg({v.reward_item}, is_unpack_gift)
				item_data.lbl_desc_text = string.format(Language.Activity.XunbaoCarnivalItemText, v.need_score , carnival_score, v.need_score)

				item_data.item_func = function()
					local param_t = {rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XUNBAO_CARNIVAL, opera_type = 1, param_1 = v.seq}
					ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
				end

				table.insert(item_data_list, item_data)
			end
		end
		return item_data_list
	end
end


function ServerActivityWGData:GetActBenXunbaoCarnivalRemind()
	if ServerActivityWGData.Instance:GetServerActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_XUNBAO_CARNIVAL) then
		if nil == self.xunbao_carnival then
			return 0
		end
		local act_client_cfg = self:GetClientActCfg(ServerActClientId.RAND_ACTIVITY_TYPE_XUNBAO_CARNIVAL)
		local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
		local chestshop_carnival_reward = rand_config.chestshop_carnival_reward

		if chestshop_carnival_reward == nil or act_client_cfg == nil then
			return 0
		end

		local carnival_score = self.xunbao_carnival.chestshop_carnival_score or 0
		local fetch_reward = self.xunbao_carnival.chestshop_carnival_fetch_reward_flag
		local is_unpack_gift = (1 == act_client_cfg.is_unpack_gift)

		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay() or 0
		local open_day_cfg = nil

		for k ,v in ipairs(chestshop_carnival_reward)do
			if open_day_cfg == nil and open_day <= v.opengame_day then
				open_day_cfg = v.opengame_day
			end

			if open_day_cfg ~= nil and open_day_cfg == v.opengame_day and fetch_reward[32 - v.seq] == 0 and v.need_score <= carnival_score then
				return 1
			end
		end
	end
	return 0
end

--充值回馈
function ServerActivityWGData:GetChargeRepayMent2ViewRemind()
	local charge_repaymentdata_instance = ChargeRepayment2WGData.Instance
	local charge_repayment2_cfg = charge_repaymentdata_instance:GetPaymentData()
	local payment_item_num = #charge_repayment2_cfg

	for k = 1, payment_item_num do
		local has_fetch_reward_flag = charge_repaymentdata_instance:GetRewardHasFetchFlag()
		if has_fetch_reward_flag == nil then 	--没有收到协议
			return 0
		end

		local oga_fetch_flag = bit:d2b(has_fetch_reward_flag)
		local is_auto_fetch = oga_fetch_flag[33 - k] == 1  --是否领取

		local reward_active_flag = charge_repaymentdata_instance:GetRewardHasActiveFlag()
		if reward_active_flag == nil then 		--没有收到协议
			return 0
		end

		local oga_active_flag = bit:d2b(reward_active_flag)
		local is_auto_active = oga_active_flag[33 - k] == 1  ---是否激活

		if is_auto_fetch then
			--"已领取"
		else
			if is_auto_active then
				return 1--"可领取"
			else
				--"不可领取"
			end
		end
	end
	return 0
end

--172 累计充值 act_type = 2168
function ServerActivityWGData:TotalRechargeViewRemind()
	local data_list = TotalChargeWGData.Instance:GetRechargeDataInfo()
	if data_list == nil or IsEmptyTable(data_list) then
		return 0
	end

	for k,v in pairs(data_list) do
		if v.is_geted and not v.is_have_opp then
			return 1
		end
	end

	return 0
end

--173 连消特惠 act_type = 2120
function ServerActivityWGData:GetConsumeDisCountViewRemind()
	local conse_instance = ConsumeDiscountWGData.Instance
	local data_list = conse_instance:GetRewardData()
	if data_list == nil or IsEmptyTable(data_list) then
		return 0
	end
	for k,v in pairs(data_list) do
		local consume_info = conse_instance:GetRAContinueConsumeInfo()
		if consume_info == nil then
			return 0
		end
		local has_fetch_flag = bit:d2b(consume_info.has_fetch_flag)
		local is_not_lingqu = has_fetch_flag[32 - v.day_index] == 0

		if v.day_index == consume_info.current_day_index and consume_info.cur_consume_gold >= v.need_consume_gold and is_not_lingqu then
			return 1
		end
	end
	return 0
end

--174 连续充值 act_type = 2112
function ServerActivityWGData:GetContinueChongZhiViewRemind()
	-- local data_list = ContinuousRechargeData.Instance:GetRewardData()
	-- if data_list == nil or IsEmptyTable(data_list) then
	-- 	return 0
	-- end
	-- for k,v in pairs(data_list) do
	-- 	if 1 == v.can_fetch_reward_flag then
	-- 		if 1 == v.has_fetch_reward_flag then

	-- 		else
	-- 			return 1
	-- 		end
	-- 	end
	-- end
	return 0
end

--152 全民庆典 act_type = 2172
function ServerActivityWGData:GetGlobalXunBaoViewRemind()
	local rand_config = self:GetCurrentRandActivityConfig()
	if rand_config == nil or IsEmptyTable(rand_config) then
		return 0
	end
	local nationa_celebration_progress_cfg = rand_config.nationa_celebration_progress
	local max_need_progress_var = 0
	local tmp = self:GetGobalXunbao()
	local server_commit_times = tmp and tmp.server_commit_times or nil
	if server_commit_times == nil then
		return 0
	end
	if nationa_celebration_progress_cfg == nil or IsEmptyTable(nationa_celebration_progress_cfg) then
		return 0
	end

	local fetch_flag = bit:d2b(tmp.fetch_flag)
	if fetch_flag == nil then
		return 0
	end

	for k,v in pairs(nationa_celebration_progress_cfg) do
		-- if max_need_progress_var < v.need_progress_var then
			-- max_need_progress_var = v.need_progress_var
		-- end
		--2是全服奖励，需要手动领取
		if v.reward_type == 2 and server_commit_times >= v.need_progress_var and fetch_flag[32 - v.seq] == 0  then
			return 1
		end
	end

	local res_mun, need_count = self:OnGobalXubBaoZhongZiData()
	local boo = res_mun >= need_count
	if boo then
		return 1
	end
	-- if boo and max_need_progress_var > 0 and server_commit_times < max_need_progress_var then
		-- return 1
	-- end

	return 0
end

--------------------------------周末boss红点-----------------------------------
function ServerActivityWGData:GetWeekendBossViewRemind()
	--local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(ServerActClientId.RAND_WEEKEND_BOSS)
	--local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
	--if act_info and act_info.status == ACTIVITY_STATUS.OPEN then
	--	return 1
	--end
	--return 0

	local act_status = self:GetVersionActivityNowStatus(ACTIVITY_TYPE.RAND_WEEKEND_BOSS)
	if act_status == nil or IsEmptyTable(act_status) then return 0 end
	if act_status.status == ACTIVITY_STATUS.OPEN then
		return 1
	end
	return 0
end

--------------------------------开宗立派-----------------------------------

function ServerActivityWGData:GetOpenLeekpaiCfgList()
	return self.opengameactivity_auto.createguild or {}
end

function ServerActivityWGData:GetOpenLeekpaiInfoList()
	local ser_data = self:GetOpenServerData()
	local info_list = {}
	if ser_data then
		info_list.create_guild_reward = ser_data.create_guild_reward
		info_list.guild_level = ser_data.guild_level
		info_list.guild_member_count = ser_data.guild_member_count
		info_list.guild_fu_tuanzhang_count = ser_data.guild_fu_tuanzhang_count
		info_list.guild_vip_level_count = ser_data.guild_vip_level_count
	end
	return info_list
end

function ServerActivityWGData:GetCreateGuildFlagList(index)
	if self.open_game_info ~= nil then
		local flag_list = self.open_game_info.oge_create_guild_reward_flag
		return flag_list[32 - index]
	end
end

function ServerActivityWGData:GetCreateGuildNumList(index)
	if self.open_game_info ~= nil then
		return self.open_game_info.create_guild_reward_list[index]
	end
end

function ServerActivityWGData:GetOpenLeekpaiCanReward(cfg, info)
	if not info then
		info = self:GetOpenLeekpaiInfoList()
	end
	if IsEmptyTable(cfg) or IsEmptyTable(info) then
		return false
	end
	local can_reward = false
	if cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.creat_guid then
		can_reward = info.create_guild_reward == 1
	elseif cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.guild_level then
		can_reward = info.guild_level >= cfg.reward_level
	elseif cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.guild_role_num then
		can_reward = info.guild_member_count >= cfg.reward_condition
	elseif cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.guild_fubangzhu_num then
		can_reward = info.guild_fu_tuanzhang_count >= cfg.reward_condition
	elseif cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.vip_role_num then
		can_reward = info.guild_vip_level_count >= cfg.reward_condition
	end
	return can_reward
end

function ServerActivityWGData:GetOpenLeekpaiProgress(cfg, info)
	if not info then
		info = self:GetOpenLeekpaiInfoList()
	end
	if IsEmptyTable(cfg) or IsEmptyTable(info) then
		return false
	end
	local cur_value, max_value = "0", "1"
	if cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.creat_guid then
		cur_value = info.create_guild_reward == 1 and ToColorStr(1, COLOR3B.C2) or ToColorStr(0, COLOR3B.C3)
	elseif cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.guild_level then
		cur_value = info.guild_level >= cfg.reward_level and ToColorStr(cfg.reward_level, COLOR3B.C2) or ToColorStr(info.guild_level, COLOR3B.C3)
		max_value = cfg.reward_level
	elseif cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.guild_role_num then
		cur_value = info.guild_member_count >= cfg.reward_condition and ToColorStr(cfg.reward_condition, COLOR3B.C2) or ToColorStr(info.guild_member_count, COLOR3B.C3)
		max_value = cfg.reward_condition
	elseif cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.guild_fubangzhu_num then
		cur_value = info.guild_fu_tuanzhang_count >= cfg.reward_condition and ToColorStr(cfg.reward_condition, COLOR3B.C2) or ToColorStr(info.guild_fu_tuanzhang_count, COLOR3B.C3)
		max_value = cfg.reward_condition
	elseif cfg.reward_type == KAIZONGLIPAI_TARGET_TYPE.vip_role_num then
		cur_value = info.guild_vip_level_count >= cfg.reward_condition and ToColorStr(cfg.reward_condition, COLOR3B.C2) or ToColorStr(info.guild_vip_level_count, COLOR3B.C3)
		max_value = cfg.reward_condition
	end
	return cur_value, max_value
end

function ServerActivityWGData:GetCreateGuildRemind()
	-- 屏蔽
	-- local zhengba_flag = self:GetGuildContentRemind()
	-- if not ServerActivityWGData.Instance:ActIsOpenByActId(ServerActClientId.OPEN_INNEGLECTABLE) then
	-- 	return 0 == zhengba_flag and 0 or 1
	-- end

	-- -- 就帮主可以领取
	local guild_post = RoleWGData.Instance.role_vo.guild_post
	if guild_post ~= GUILD_POST.TUANGZHANG then
		return 0
	end

	local cfg_list = self:GetOpenLeekpaiCfgList()
	if IsEmptyTable(cfg_list) then
		return 0
	end

	local info_list = self:GetOpenLeekpaiInfoList()
	if IsEmptyTable(info_list) then
		return 0
	end

	for k,cfg in pairs(cfg_list) do
		local flag = self:GetCreateGuildFlagList(cfg.seq)
		local num = self:GetCreateGuildNumList(cfg.seq + 1)
		if flag == 0 and num > 0 then
			if self:GetOpenLeekpaiCanReward(cfg, info_list) then
				return 1
			end
		end
	end

	return 0
end

----------------------------每日限购-----------------------------
function ServerActivityWGData:GetEveryDayShopList()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local everyday_shop_cfg = self.opengameactivity_auto.daily_limit_buy
	local list = {}
	if -1 ~= open_day then
		for k,v in pairs(everyday_shop_cfg) do
			if open_day == v.opengame_day then
				table.insert(list, v)
			end
		end
	end
	return list
end
function ServerActivityWGData:GetEveryDayShopBuyNum(index)
	if self.open_game_info ~= nil then
		return self:GetOpenServerData().daily_limit_buy_count_flag[index]
	end
end



----------------------------随机活动 高倍返利---------------------------
function ServerActivityWGData:OnRAHighRebateInfo(protocol)
	self.hight_rebate_list.reward_can_fetch_flag = bit:d2b(protocol.reward_can_fetch_flag)
	self.hight_rebate_list.reward_fetch_flag = bit:d2b(protocol.reward_fetch_flag)
end

function ServerActivityWGData:GetHighRebateFlag()
	return self.hight_rebate_list
end

---[[
----------------------BOSS猎人---------------------------
function ServerActivityWGData:SetBossHunterInfo(protocol)
	local boss_hunter_info = {}
	boss_hunter_info.role_guild_name = protocol.role_guild_name
	boss_hunter_info.role_tuan_zhang_name = protocol.role_tuan_zhang_name
	boss_hunter_info.role_fu_tuanzhang_name = protocol.role_fu_tuanzhang_name
	boss_hunter_info.role_boss_score = protocol.role_boss_score
	boss_hunter_info.rank_list = protocol.rank_list
	self.boss_hunter_info = boss_hunter_info
end

function ServerActivityWGData:GetBossHunterInfo()
	return self.boss_hunter_info
end

function ServerActivityWGData:SetBossHunterMyGuidRankInfo(protocol)
	local rank_info = {}
	rank_info.last_killer_name = protocol.last_killer_name
	rank_info.boss_type = protocol.boss_type
	rank_info.monster_id = protocol.monster_id
	rank_info.rank_list = protocol.rank_list
	self.boss_hunter_my_guid_rank_info = rank_info
end

function ServerActivityWGData:GetBossHunterMyGuidRankInfo()
	return self.boss_hunter_my_guid_rank_info
end
--]]

function ServerActivityWGData:GetBossHunterJiangLi(index)
	local boss_hunter_cfg = self.opengameactivity_auto.boss_hunter_reward
	for k,v in pairs(boss_hunter_cfg) do
		if index == v.rank then
			return v.rank_bind_gold
		end
	end
	return 0
end

function ServerActivityWGData:GetBossHunterList()
	local list = {}
	for i = 1, 3 do
		list[i] = {}
		list[i].guild_id = ""
   		list[i].guild_name = ""
   		list[i].tuan_zhang_uid = ""
   		list[i].tuan_zhang_name = ""
   		list[i].boss_score = 0
	end
	return list
end

function ServerActivityWGData:SetOpenServerCompetitionClickRemind(index)
	self.open_server_competition_click_remind[index] = index
end

function ServerActivityWGData:GetOpenServerCompetitionClickRemind(index)
	return self.open_server_competition_click_remind[index] or 0
end

function ServerActivityWGData:SetEveryDayClickRemind(index)
	self.act_everyday_shop_click_remind = index
end

function ServerActivityWGData:GetEveryDayShopClickRemind()
	return self.act_everyday_shop_click_remind or 0
end

------------------特惠秒杀------------------------
-- function ServerActivityWGData:SetRAPanicBuyInfo(protocol)
-- 	self.csa_panic_buy_times_list = protocol.csa_panic_buy_times_list
-- 	print_error(self.csa_panic_buy_times_list)
-- 	self.batch = protocol.batch
-- end

-- function ServerActivityWGData:GetRAPanicBuyBatch()
-- 	return self.batch
-- end

-- function ServerActivityWGData:GetCanBuyTimes()
-- 	return self.csa_panic_buy_times_list
-- end

function ServerActivityWGData:GetTeHuiShopCfg()
	local data_list = {}
	local cfg = self:GetCurrentRandActivityConfig()
	local tehui_shop_cfg = self:GetOpenServerActivityConfig(cfg.panic_buy_item)
	for k,v in pairs(tehui_shop_cfg) do
		if self.batch == v.batch then
			table.insert(data_list, v)
		end
	end
	return data_list
end

function ServerActivityWGData:GetNextTeHuiShopCfg()
	local data_list = {}
	local cfg = self:GetCurrentRandActivityConfig()
	local tehui_shop_cfg = self:GetOpenServerActivityConfig(cfg.panic_buy_item)
	for k,v in pairs(tehui_shop_cfg) do
		if self.batch+1 == v.batch then
			table.insert(data_list, v)
		end
	end
	return data_list
end
----------------特惠秒杀end------------------------

----------------开服活动红点-----------------------
function ServerActivityWGData:GetOpenServerRedRemind()
	if self:GetKaiZongLiPaiRemind() == 1 then
		return 1
	elseif self:GetCreateGuildRemind() == 1 then
		return 1
	-- elseif self:GetGuildContentRemind() == 1 then
	-- 	return 1
	elseif self:RemindKiFuJiZiRed() == 1 then
        return 1
    elseif self:GetActKfGiftRemind() == 1 then
    	return 1
    elseif ActivePerfertQingrenWGData.Instance:GetCityLoveRemind() == 1 then
		return 1
	elseif OpenServerAssistWGData.Instance:GetHighPointRemind() == 1 then
		return 1 
	elseif self:GetTotalChongzhiRemind() == 1 then
		return 1 
	elseif ActivityCollectCardWGData.Instance:GetActCollectCardRemind() == 1 then
		return 1 
	end
	return 0
end

function ServerActivityWGData:GetKFActivityRedRemind()
	if self:GetOpenserverCommpetionRed() == 1 then
		return 1
	elseif self:GetTotalChongzhiRemind() == 1 then
		return 1
	end

	local seq = self:GetBiPinSuitSeq()
	local suit_data = GuiXuDreamWGData.Instance:GetGuiXuDreamSuitInfoBySuit(seq)
	if IsEmptyTable(suit_data) then
		return 0
	else
		return suit_data.can_act and 1 or 0
	end

	return 0
end

function ServerActivityWGData:GetOpenServerRedPoint(id,is_select) -- 获取红点显示标识
	if not self.open_server_remind then
		return false
	end
	if id == 132 then  -- 首充团购
		local flag = ServerActivityWGData.Instance:GetBuyRewardFlag()
		local data_list = ServerActivityWGData.Instance:GetGroupBuyReward(id)
		local tab_list = {}
		if data_list then
			local cur_value = -1
			for i,v in ipairs(data_list) do
				if cur_value ~= v.groupbuy_active_need_person then
					cur_value = v.groupbuy_active_need_person
					table.insert(tab_list, cur_value)
				end
			end
		end
		local reward_data_list = {}
		for k,v in pairs(tab_list) do
			reward_data_list = ServerActivityWGData.Instance:GetCurrentActivityDataList(id,data_list, v)
			for k1,v1 in pairs(reward_data_list) do
				if v1.effect and flag[32 - v1.seq] == 0 then
					return true
				end
			end
		end
		return false
	elseif id == ServerActClientId.OPEN_INNEGLECTABLE then  -- 开宗立派
		-- local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		-- local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
		-- local remind_day = PlayerPrefsUtil.GetInt(main_role_id .. ServerActClientId.OPEN_INNEGLECTABLE)
		-- if is_select then
		-- 	PlayerPrefsUtil.SetInt(main_role_id .. ServerActClientId.OPEN_INNEGLECTABLE, cur_day)
		-- end
		-- if remind_day ~= cur_day then
		-- 	return true
		-- end
		-- return false
	elseif id == 144 then  -- 字帖兑换
		local opengame_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
		local data_list = opengame_cfg.word_exchange
		local flag = false
		local have_num = 0
		local need_num = 0
		local exchange_flag
		for i,v in ipairs(data_list) do
			exchange_flag = ServerActivityWGData.Instance:GetOpenServerData().oga_word_exchange_flag[i]
			need_num = 0
			have_num = 0
			for i=1,5 do
				if v["item_id_" .. i] > 0 then
					need_num = need_num + 1
				end
			end
			for i=1,5 do
				if v["item_id_" .. i] > 0 then
					if v.limit - exchange_flag > 0 then
						local item_num = ItemWGData.Instance:GetItemNumInBagById(v["item_id_" .. i])
						if item_num > 0 then
							have_num = have_num + 1
						end
					end
				end
			end
			if have_num >= need_num then
				return true
			end
		end
		return false
	-- elseif id == 143 then --帮派争霸
	-- 	return self:GetGuildContentRemind()
	else
		if is_select then
			self.open_server_remind[id] = false
			return false
		end
		if self.open_server_remind[id] == nil then
			return true
		end
		return self.open_server_remind[id]
	end
end

function ServerActivityWGData:GetQuaMinQingDianReward()
	return self:GetCurrentRandActivityConfig().other or {}
end

function ServerActivityWGData:GetOpenserverCommpetionRed()
	if not ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.OPENSERVER_COMPETITION) then
		return 0
	end
	if self:OpenServerCompetitonCanGetReward() then -- or self:OpenServerCompetitonCanGetGift()
		return 1
	end

	-- local ret_num = self:GetOGALotteryRemind()
	-- ret_num = ret_num + self:GetOGARechargeRemind()
	-- ret_num = ret_num + self:GetOGADailyRewardRemind()
	-- if ret_num > 0 then
	-- 	return 1
	-- end

	-- if self:BiPinBindXianYuGiftCanBuyRemind() then
	-- 	return 1
	-- end
	return 0
end

function ServerActivityWGData:SetOpenserverCommpetionRed(flag)
	self.openserver_act_red = flag
end

--------------------------------------------------------------------------------
--烟花庆典配置奖励(版本活动改随机活动)
function ServerActivityWGData:GetActYanHuaRewardCfg()
	if not self.yanhua_cfg then
		local yanhua_qingdian_cfg = self:GetCurrentRandActivityConfig().yanhua_celebration
		self.yanhua_cfg = ListToMapList(yanhua_qingdian_cfg,"role_level")

	end
	local role_level = RoleWGData.Instance.role_vo.level
	local list = {}
	local reward_item_id = 0
	local index = 0
	local cur_reward_stage = 1000
	for k,v in pairs(self.yanhua_cfg) do
		if k >= role_level and k < cur_reward_stage then
			cur_reward_stage = k
		end
	end
	for i ,v in pairs (self.yanhua_cfg[cur_reward_stage]) do
		if  v.show == 1 then
			list[index] = v.reward_item
			list[index].show_gift = v.show_gift
			index = index + 1
		end
		if v.show_gift ~= 0  then
			reward_item_id = v.show_gift
		end
	end
	return list,reward_item_id
end

--烟花庆典红点提醒(版本活动改随机活动)
function ServerActivityWGData:GetActYanHuaRemind()
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().other
	local cost_item_id = cfg[1].yanhua_celebration_cost_item_id
	local item_count = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
	return item_count > 0 and 1 or 0
end

function ServerActivityWGData:GetYanhuaReward(data)
	local list = {}
	if not self.yanhua_cfg then
		local yanhua_qingdian_cfg = self:GetCurrentRandActivityConfig().yanhua_celebration
		self.yanhua_cfg = ListToMapList(yanhua_qingdian_cfg,"role_level")
	end
	local role_level = RoleWGData.Instance.role_vo.level
	local cur_reward_cfg = {}
	local cur_reward_stage = 1000
	for k,v in pairs(self.yanhua_cfg) do
		if k >= role_level and k < cur_reward_stage then
			cur_reward_stage = k
		end
	end
	for k,v in pairs(data) do
		for m,n in pairs(self.yanhua_cfg[cur_reward_stage]) do
			if v == n.seq then
				table.insert(list,n.reward_item)
				break
			end
		end
	end
	table.sort(list,function(a,b)
		local _,color1 = ItemWGData.Instance:GetItemColor(a.item_id)
		local _,color2 = ItemWGData.Instance:GetItemColor(b.item_id)
		return color1 > color2
	end)
	return list
end

----------------------------start------------------------------------------------
--炫装白送
function ServerActivityWGData:SaveDazzleSuitInfo(info)
	self.dazzle_suit_info = info
end

function ServerActivityWGData:GetXuanZhuangDonorCfg()
	local config = self:GetCurrentRandActivityConfig()
	local shizhaung_donor_cfg = config.shizhaung_donor or {}
	return shizhaung_donor_cfg
end

function ServerActivityWGData:GetDazzleSuitInfo()
	return self.dazzle_suit_info
end

----------------------------end--------------------------------------------------

--------------------------------------------------------------------------------
--------------------------------boss乱斗----------------------------------------
function ServerActivityWGData:GetBossFightCfg()
	if not self.boss_fight_cfg then
		self.boss_fight_cfg = ConfigManager.Instance:GetAutoConfig("boss_messbattle_auto")
	end
	return self.boss_fight_cfg
end

function ServerActivityWGData:GetBossFightReward()
	return self:GetBossFightCfg().score_reward
end

function ServerActivityWGData:GetBossFightWaveReward()
	local list = self:GetBossFightCfg().other[1]
	if not self.boss_fight_wave_reward then
		self.boss_fight_wave_reward = {}
		table.insert(self.boss_fight_wave_reward, list.first_round_drop_item)
		table.insert(self.boss_fight_wave_reward, list.second_round_drop_item)
		table.insert(self.boss_fight_wave_reward, list.third_round_drop_item)
	end
	return self.boss_fight_wave_reward
end

function ServerActivityWGData:SetBossFightRankInfo(rank_item)
	self.boss_fight_rank_item = rank_item
end

function ServerActivityWGData:GetBossFightRankInfo()
	return self.boss_fight_rank_item
end

function ServerActivityWGData:SetBossFightUserInfo(info)
	if not self.boss_fight_user_info then
		self.boss_fight_user_info = {}
	end
	self.boss_fight_user_info.uuid = info.uuid
	self.boss_fight_user_info.score = info.score
end

function ServerActivityWGData:GetBossFightUserInfo()
	return self.boss_fight_user_info
end

function ServerActivityWGData:SetBossFightSceneInfo(protocol)
	self.boss_fight_flush_time = protocol.time
	if not self.boss_fight_scene_info then
		self.boss_fight_scene_info = {}
	end
	self.boss_fight_scene_info.boss_count = protocol.boss_count
	self.boss_fight_scene_info.monster_count = protocol.monster_count
	self.boss_fight_scene_info.cur_wave = protocol.cur_wave
end

function ServerActivityWGData:GetBossFightSceneInfo()
	return self.boss_fight_scene_info
end

function ServerActivityWGData:GetBossFightFlushTime()
	return self.boss_fight_flush_time
end

function ServerActivityWGData:GetBossFightUserRank()
	if not self.boss_fight_user_info or not self.boss_fight_rank_item then
		return
	end
	for i,v in ipairs(self.boss_fight_rank_item) do
		if v.uuid == self.boss_fight_user_info.uuid then
			return i
		end
	end
end

function ServerActivityWGData:GetBossFightCountLastTime()
	local cfg = self:GetBossFightCfg()
	return cfg.other[1].next_round_delay_s
end
----------------------------------------------------------------------------------

function ServerActivityWGData:SetActivitySubStatus(act_type,next_time,status)
	self.sub_act_status[act_type] =
	{
		["type"] = act_type,
		["next_time"] = next_time,
		["status"] = status,
	}
end

function ServerActivityWGData:GetActivityState(act_type)
	return self.sub_act_status[act_type]
end
----------------------------------------------------------------------------------

-------------------------------------仙盟封榜---------------------------------------------
function ServerActivityWGData:GetKaiZongLiPaiNewCfg()
	return self.opengameactivity_auto.kaizonglipai_new
end

function ServerActivityWGData:GetKaiZongLiPaiNewCfgByTitleId(title_id)
	local cfg = self.opengameactivity_auto.kaizonglipai_new
	for k,v in pairs(cfg) do
		if title_id == v.title_id then
			return v
		end
	end
end

function ServerActivityWGData:GetKaiZongLiPaiIsOpen()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local other_cfg = self.opengameactivity_auto.other[1]
	local cur_open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	--级别够,开服天数小于关闭天数
	if role_level >= other_cfg.kaizonglipai_open_level and
		(cur_open_server_day > 0 and cur_open_server_day < other_cfg.kaizonglipai_close_day) then

		return true
	end
	return false
end

function ServerActivityWGData:GetKaiZongLiPaiRemind()
	return 0
end

function ServerActivityWGData:IsOpenActivityJuanXian()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACT_OPEN_SERVER_JUANXIAN)
	if activity_data ~= nil then
		return activity_data.status == ACTIVITY_STATUS.OPEN
	end
	return false
end

function ServerActivityWGData:IsOpenActivityZhenLong()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACT_OPEN_SERVER_ZHENLONG)
	if activity_data ~= nil then
		return activity_data.status == ACTIVITY_STATUS.OPEN
	end
	return false
end

function ServerActivityWGData:RemindKiFuJiZiRed()
	if not ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.RAND_COLLECT_ITEMS) then
		return 0
	end
	local opengame_cfg = self:GetOpenGameActivityConfig()
	local data_list = opengame_cfg.word_exchange
	local flag = false
	local have_num = 0
	local need_num = 0
	local exchange_flag
	local oga_word_exchange_flag = self:GetOpenServerData().oga_word_exchange_flag
	if nil == oga_word_exchange_flag then
		return 0
	end
	local flag = PlayerPrefsUtil.GetInt("OpenServerZiTieRemindToggle") or 0
	local flags = bit:d2b(flag)
	for i,v in ipairs(data_list) do
		if flags[i] ~= 1 then
			exchange_flag = oga_word_exchange_flag[i]
			need_num = 0
			have_num = 0
			for i=1,5 do
				if v["item_id_" .. i] > 0 then
					need_num = need_num + 1
				end
			end
			for i=1,5 do
				if v["item_id_" .. i] > 0 then
					if v.limit - exchange_flag > 0 then
					local item_num = ItemWGData.Instance:GetItemNumInBagById(v["item_id_" .. i])
					if item_num > 0 then
						have_num = have_num + 1
						end
					end
				end
			end
			if have_num >= need_num then
				return 1
			end
		end
	end
	return 0
end

-----------------------------------首充续充-------------------------------------------------
function ServerActivityWGData:SetSCXCServerInfo(protocol)
	self.sc_server_day = protocol.sc_server_day
	self.xc_chongzhi_num = protocol.chongzhi_num
	self.sc_advance_end_time = protocol.sc_advance_end_time
	self.rc_gear_flag = bit:d2b_two(protocol.gear_flag)
	
	local cz_info = protocol.cz_info
	local rc_flag_list = {}
	for i=1,#cz_info do
		local temp = {}
		temp.reward_flag = bit:d2b_two(cz_info[i].reward_flag)
		temp.day_flag = bit:d2b_two(cz_info[i].day_flag)
		rc_flag_list[i] = temp
	end
	self.rc_flag_list = rc_flag_list

	self.tips_end_time = protocol.tips_end_time
	if self.tips_end_time > 0 then
		ServerActivityWGCtrl.Instance:FlushMainUiFirstRechargeTip()
	end

	self:SetFirstRechargeMainUiIconVisible()
	self:CheckFirstRechargeOpen()
end

function ServerActivityWGData:GetSCAdvanceEndTime()
	return self.sc_advance_end_time or 0
end

function ServerActivityWGData:GetSCTipsEndTime()
	return self.tips_end_time or 0
end

--主界面首充图标显示与否
function ServerActivityWGData:SetFirstRechargeMainUiIconVisible()
	if IS_FREE_VERSION then
		FunOpen.Instance:ForceCloseFunByName(FunName.ShouChong)
		return
	end

	local fr_act_state = false
	if self:FirstChargeIsOpen() then
		if FunOpen.Instance:GetFunIsOpened(FunName.ShouChong) then
			fr_act_state = true
		else
			fr_act_state = false
			self.first_recharge_open_check = BindTool.Bind(self.FirstRechargeOpenCheck, self)
			FunOpen.Instance:NotifyFunOpen(self.first_recharge_open_check)
		end
	else
		fr_act_state = false
	end

	local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.FIRST_CHONGZHI)
	if fr_act_state ~= act_is_open then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.FIRST_CHONGZHI, fr_act_state and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE)
		ServerActivityWGCtrl.Instance:SetShouChongShowFlag(fr_act_state)
	end
end

function ServerActivityWGData:FirstRechargeOpenCheck(fun_name)
	if fun_name == FunName.ShouChong then
		FunOpen.Instance:UnNotifyFunOpen(self.first_recharge_open_check)
		self:SetFirstRechargeMainUiIconVisible()
	end
end
----------------------每日充值-----------------------

function ServerActivityWGData:GetLeiChongCfg(day, id)
	local cfg_list = self.leichong_cfg
	local temp_cfg_list = {}
	local data_list = {}
	local info = self:GetEveryDayRechargeInfo()
	local temp_level = RoleWGData.Instance:GetRoleLevel()
	local role_level = info and info.old_role_level or temp_level

	if cfg_list and day then
		if id then --整个数据列表
			-- temp_cfg_list = cfg_list[day] and cfg_list[day][id] or cfg_list[#cfg_list][id]--策划需求:开服天数大于配置天数,则取最后一天的配置
			temp_cfg_list = cfg_list[day] or cfg_list[#cfg_list]
			for i=1,#temp_cfg_list do
				local data = temp_cfg_list[i]
				local level_enough = role_level >= data.limit_level_min and role_level <= data.limit_level_max
				if data and data.id == id and level_enough then
					return data
				end
			end
		else --单个数据
			temp_cfg_list = cfg_list[day] or cfg_list[#cfg_list]
			local index = 0
			for i=1,#temp_cfg_list do
				local data = temp_cfg_list[i]
				local level_enough = role_level >= data.limit_level_min and role_level <= data.limit_level_max
				if data and level_enough  then
					-- table.insert(data_list,data)
					data_list[index] = data
					index = index + 1
				end
			end
		end
	end

	return data_list
end

----------------------每日消费-----------------------

function ServerActivityWGData:GetXiaoFeiCfg(day, id)
	local cfg_list = self.xiaofei_cfg
	local temp_cfg_list = {}
	local data_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()

	if cfg_list and day then
		if id then
			temp_cfg_list = cfg_list[day] or cfg_list[#cfg_list]
			for i=1,#temp_cfg_list do
				local data = temp_cfg_list[i]
				local level_enough = role_level >= data.limit_level_min and role_level <= data.limit_level_max
				if data and data.id == id and level_enough then
					return data
				end
			end
		else
			temp_cfg_list = cfg_list[day] or cfg_list[#cfg_list]
			local index = 1
			for i=1,#temp_cfg_list do
				local data = temp_cfg_list[i]
				if data then
					data_list[index] = data
					index = index + 1
				end
			end
		end
	end
	return data_list
end

function ServerActivityWGData:GetXiaoFeiListdata()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local list_data = ServerActivityWGData.Instance:GetXiaoFeiCfg(open_day)

    local finish_list = {}
	local not_finish_list = {}
	for k,v in pairs(list_data) do
		if self:GetEveryDayRewardState(v.id) then
			table.insert(finish_list,v)
		else
			table.insert(not_finish_list,v)
		end
	end
	for t,v in pairs(finish_list) do
		table.insert(not_finish_list,v)
	end
    return not_finish_list
end

function ServerActivityWGData:GetLeiChongReward(cycle)
	return self.leiji_reward[cycle]
end

function ServerActivityWGData:GetDailyGiftInfo()
	local grade = self.everyday_gift_list.grade or 1
	return self.daily_gift[grade]
end

function ServerActivityWGData:GetLeiChongOtherCfg(key)
	local cfg_list = self.daily_leichong_auto.other
	if cfg_list[1] and cfg_list[1][key] then
		return cfg_list[1][key]
	end
end

function ServerActivityWGData:GetRewardList(key)

end

--设置秒杀礼包信息.
function ServerActivityWGData:SetEveryDayRapidlyGiftInfo(protocol)
	self.everyday_rapidly_gift_info.free_reward_flag = protocol.free_reward_flag					--免费奖励领取标志，0：未领取，1：已领取.
	self.everyday_rapidly_gift_info.super_reward_flag = protocol.super_reward_flag					--超级返利奖励领取标志，0：未领取，1：已领取.
	self.everyday_rapidly_gift_info.ten_day_remain = protocol.ten_day_remain						--购买10天的剩余可领取天数.
	self.everyday_rapidly_gift_info.rapidly_gift_flag = bit:d2b_two(protocol.rapidly_gift_flag)		--秒杀礼包奖励领取标志.
end

--秒杀礼包配置.
function ServerActivityWGData:GetRapidlyGiftCfg()
	return self.rapidly_gift_cfg
end

--秒杀礼包一键购买配置.
function ServerActivityWGData:GetRapidlyGiftOneKey(id)
	return (self.daily_leichong_auto.rapidly_gift_one_key or nil)[id]
end

--获取秒杀礼包信息.
function ServerActivityWGData:GetEveryDayRapidlyGiftInfo()
	return self.everyday_rapidly_gift_info
end

--获取秒杀礼包 购买10天 的剩余可领取天数.
function ServerActivityWGData:CheckCanGetEveryDayRapidlyGiftDay()
	return self.everyday_rapidly_gift_info.ten_day_remain or 0
end

--是否可以领取秒杀礼包每日奖励.
function ServerActivityWGData:CanGetEveryDayRapidlyGiftFreeBox()
	return (self.everyday_rapidly_gift_info.free_reward_flag or false) == 0
end

--是否可以领取秒杀礼包超级大奖.
function ServerActivityWGData:CanGetEveryDayRapidlyGiftSuperReward()
	if not self.everyday_rapidly_gift_info.rapidly_gift_flag then
		return false
	end

	local can_get = (self.everyday_rapidly_gift_info.super_reward_flag or false) == 0
	if can_get then
		local is_have = self:CheckCanGetEveryDayRapidlyGiftDay() > 0
		if is_have then
			return true
		end

		for key, value in pairs(self.everyday_rapidly_gift_info.rapidly_gift_flag) do
			if self.rapidly_gift_cfg[key] and key == self.rapidly_gift_cfg[key].id and value == 0 then
				return false
			end
		end
	else
		return false
	end

	return true
end

--是否可以一键购买秒杀礼包.
function ServerActivityWGData:CanOneKeyBuyEveryDayRapidlyGift()
	local can_buy = false
	if not self.everyday_rapidly_gift_info.rapidly_gift_flag then
		return can_buy
	end

	can_buy = self:CheckCanGetEveryDayRapidlyGiftDay() == 0

	if can_buy then
		for key, value in pairs(self.everyday_rapidly_gift_info.rapidly_gift_flag) do
			if self.rapidly_gift_cfg[key] and key == self.rapidly_gift_cfg[key].id and value == 1 then
				can_buy = false
				break
			end
		end
	end

	return can_buy
end

--是否可以购买/领取秒杀礼包.
function ServerActivityWGData:CanBuyEveryDayRapidlyGiftById(id)
	local can_buy = false
	if not self.everyday_rapidly_gift_info.rapidly_gift_flag then
		return can_buy
	end

	for key, value in pairs(self.everyday_rapidly_gift_info.rapidly_gift_flag) do
		if self.rapidly_gift_cfg[key] and key == self.rapidly_gift_cfg[key].id and key == id then
			can_buy = value == 0
			break
		end
	end

	return can_buy
end

--秒杀礼包红点.
function ServerActivityWGData:IsShowEveryDayRapidlyGiftRed()
	if not FunOpen.Instance:GetFunIsOpened(FunName.billion_subsidy_rapidly) then
		return 0
	end

	if self:CanGetEveryDayRapidlyGiftFreeBox() then
		return 1
	end

	if self:CanGetEveryDayRapidlyGiftSuperReward() then
		return 1
	end

	local is_have = self:CheckCanGetEveryDayRapidlyGiftDay() > 0
	if is_have then
		if not self.everyday_rapidly_gift_info.rapidly_gift_flag then
			return 0
		end

		for key, value in pairs(self.everyday_rapidly_gift_info.rapidly_gift_flag) do
			if self.rapidly_gift_cfg[key] and key == self.rapidly_gift_cfg[key].id and value == 0 then
				return 1
			end
		end
	end

	return 0
end

--设置秒杀礼包记录信息.
function ServerActivityWGData:SetEveryDayRapidlyGiftRecordInfo(protocol)
	self.everyday_rapidly_gift_record_info.buy_record_list = protocol.buy_record_list				--购买记录列表.
	self.everyday_rapidly_gift_record_info.draw_record_list = protocol.draw_record_list				--抽奖记录列表.
end

--获取秒杀礼包记录信息. type:1 购买记录，type:2 抽奖记录.
function ServerActivityWGData:GetEveryDayRapidlyGiftRecordInfo(type)
	local function SetData(list)
		local data = {}
		if list then
			for k,v in ipairs(list) do
				--过滤空数据.
				if v.timestamp > 0 then
					v.type_index = type
					table.insert(data, v)
				end
			end
		end
		return data
	end

	local data = {}
	if type == 1 then
		data = SetData(self.everyday_rapidly_gift_record_info.buy_record_list)
	elseif type == 2 then
		data = SetData(self.everyday_rapidly_gift_record_info.draw_record_list)
	end
	return data
end

function ServerActivityWGData:SetEveryDayRechargeInfo(protocol)
	self.everyday_reward_list.everyday_recharge_num = protocol.everyday_recharge_num  --每日充值数
	self.everyday_reward_list.everyday_cycle_num = protocol.everyday_cycle_num   --累充天数奖励当前循环数
	self.everyday_reward_list.everyday_reward_flag = bit:d2b_two(protocol.everyday_reward_flag)   --每日充值可领取奖品标志
	self.everyday_reward_list.everyday_get_reward_flag = bit:d2b_two(protocol.everyday_get_reward_flag)  --每日充值已领取奖品标志
	self.everyday_reward_list.leichong_reward_flag = bit:d2b_two(protocol.leichong_reward_flag)	 --充值天数奖品可领取标志
	self.everyday_reward_list.leichong_get_reward_flag = bit:d2b_two(protocol.leichong_get_reward_flag)  --充值天数奖品已领取标志
	self.everyday_reward_list.old_role_level = protocol.old_role_level  	--昨天保存的领取等级
end

--每日消费信息
function ServerActivityWGData:SetEveryDayExpenseInfo(protocol)
	self.everyday_expense_list.daily_consume_gold = protocol.daily_consume_gold  --每日消费数
	self.everyday_expense_list.daily_consume_reward_record = protocol.daily_consume_reward_record  --每日消费可领取奖品标志
end

--每日累充-每日礼包信息
function ServerActivityWGData:SetEveryDayLeiChongDailyGiftInfo(protocol)
	self.everyday_gift_list.grade = protocol.grade  						--档位.
	self.everyday_gift_list.free_reward_flag = protocol.free_reward_flag  --免费奖励领取标志
	self.everyday_gift_list.daily_gift_list = protocol.daily_gift_count  --每日消费可领取奖品标志
end

function ServerActivityWGData:GetLeiChongDay()
	local leichong_cfg = self:GetLeiChongReward(self.everyday_reward_list.everyday_cycle_num)
	local leichong_reward_flag = self.everyday_reward_list.leichong_reward_flag
	local num = 0
	if leichong_cfg and leichong_reward_flag then
		for i,v in ipairs(leichong_cfg) do
			num = num + leichong_reward_flag[v.index]
		end
	end
	return num
end

function ServerActivityWGData:GetRechargeNum()
	return self.everyday_reward_list.everyday_recharge_num
end

function ServerActivityWGData:GetLeiChongFlagOne(id)
	return 	self.everyday_reward_list.leichong_reward_flag[id] == 1
end

function ServerActivityWGData:GetLeiChongFlagTow(id)
	return self.everyday_reward_list.leichong_get_reward_flag[id] == 1
end

function ServerActivityWGData:GetMeiRiFlagOne(id)
	return self.everyday_reward_list.everyday_reward_flag[id] == 1
end

function ServerActivityWGData:GetMeiRiFlagTow(id)
	return self.everyday_reward_list.everyday_get_reward_flag[id] == 1
end

function ServerActivityWGData:GetEveryDayRechargeInfo()
	return self.everyday_reward_list
end

function ServerActivityWGData:GetEveryDayCurCycleCount()
	return self.everyday_reward_list.everyday_cycle_num
end

--每日消费
function ServerActivityWGData:GetEveryDayExpenseNum() --每日消费仙玉总数
	return self.everyday_expense_list.daily_consume_gold
end

function ServerActivityWGData:GetEveryDayRewardState(id) --每日消费可领取奖品标志
	return self.everyday_expense_list.daily_consume_reward_record[32 - id] == 1
end

function ServerActivityWGData:GetEveryDayRewardInfo()
	return self.everyday_expense_list
end

--每日充值-每日礼包
function ServerActivityWGData:GetCanEveryDayGiftIsYlq()
	return self.everyday_gift_list.free_reward_flag ~= 1
end

function ServerActivityWGData:GetEveryDayGiftList()
	return self.everyday_gift_list.daily_gift_list
end

function ServerActivityWGData:GetEveryDayGiftInfo()
	return self.everyday_gift_list
end

function ServerActivityWGData:IsShowDailyLiBaoRed() --每日礼包红点
	if not FunOpen.Instance:GetFunIsOpened(FunName.billion_subsidy_dailygift) then
		return 0
	end

	local free_libao_can_lq = ServerActivityWGData.Instance:GetCanEveryDayGiftIsYlq()
	if free_libao_can_lq then
		return 1
	end

	return 0
end

function ServerActivityWGData:IsShowExpenseRed() --每日消费红点
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.RechargeXiaofei)
	if not is_open then
		return 0
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local total_gold = self:GetEveryDayExpenseNum()
	local data_list =  ServerActivityWGData.Instance:GetXiaoFeiCfg(open_day)
	if data_list then
		for _,v in pairs(data_list) do
			local is_get = self:GetEveryDayRewardState(v.id)
			if not is_get and total_gold >= v.consumer_num then  --没领取 and  总消费大于该档位的消费
				return 1
			end
		end
	end

	return 0
end

function ServerActivityWGData:IsShowDailyRed() --大红点
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.RechargeLeichong)
	if not is_open then
		return 0
	end

	--是否首充
	local is_recharge = RechargeWGData.Instance:GetIsFirstRecharge()
	if not is_recharge then
		return 0 
	end
	-- 每日
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local data_list = ServerActivityWGData.Instance:GetLeiChongCfg(open_day)
	if data_list then
		for _,v in pairs(data_list) do
			local can_get = self:GetMeiRiFlagOne(v.id)
			local is_get = self:GetMeiRiFlagTow(v.id)
			if can_get and not is_get then
				return 1, 1, v.id
			end
		end
	end

	-- 累计
	local cur_cycle = self:GetEveryDayCurCycleCount()
	local leichong_cfg = self:GetLeiChongReward(cur_cycle)
	if leichong_cfg then
		for _,v in pairs(leichong_cfg) do
			local can_get = self:GetLeiChongFlagOne(v.index)
			local is_get = self:GetLeiChongFlagTow(v.index)
			if can_get and not is_get then
				return 1, 2, v.index
			end
		end
	end
	return 0
end

function ServerActivityWGData:IsShowDailyRedEveryDay()
	-- 每日
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local data_list = ServerActivityWGData.Instance:GetLeiChongCfg(open_day)
	local can_get, is_get
	if data_list then
		for _,v in pairs(data_list) do
			if v.id ~= 0 then
				can_get = self:GetMeiRiFlagOne(v.id)
				is_get = self:GetMeiRiFlagTow(v.id)
				if can_get and not is_get then
					return 1
				end
			end
		end
	end
	return 0
end

function ServerActivityWGData:IsShowDailyRedEveryDayLogin()
	-- 每日
	local login_index = 0
	local can_get = self:GetMeiRiFlagOne(login_index)
	local is_get = self:GetMeiRiFlagTow(login_index)
	if is_get then
		return 2
	elseif can_get then
		return 1
	end
	return 0
end

function ServerActivityWGData:IsShowDailyRedLeiJi()
	-- 累计
	local cur_cycle = self:GetEveryDayCurCycleCount()
	local leichong_cfg = self:GetLeiChongReward(cur_cycle)
	local can_get, is_get
	if leichong_cfg then
		for _,v in pairs(leichong_cfg) do
			can_get = self:GetLeiChongFlagOne(v.index)
			is_get = self:GetLeiChongFlagTow(v.index)
			if can_get and not is_get then
				return 1
			end
		end
	end
	return 0
end

function ServerActivityWGData:ShowDailyRechargeRedInfo()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cur_cycle = self:GetEveryDayCurCycleCount()
	print_error("每日充值红点信息", "天数", open_day, "轮数", cur_cycle)
	local num, mark, param = self:IsShowDailyRed()
	if num > 0 then
		if mark == 1 then
			print_error("每日充值有登录奖励红点", "id=", param)
			print_error(self.everyday_reward_list.everyday_reward_flag)
			print_error(self.everyday_reward_list.everyday_get_reward_flag)
		else
			print_error("每日充值有累计奖励红点", "index=", param)
			print_error(self.everyday_reward_list.leichong_reward_flag)
			print_error(self.everyday_reward_list.leichong_get_reward_flag)
		end
	end
	-- local data_list = ServerActivityWGData.Instance:GetLeiChongCfg(open_day)
	-- local leichong_cfg = self:GetLeiChongReward(cur_cycle)
	-- print_error(data_list)
	-- print_error(leichong_cfg)
end
---------------------每日充值end-------------------------

function ServerActivityWGData:GetRCGearFlag(grade)
	if self.rc_gear_flag and grade then
		return self.rc_gear_flag[grade - 1] == 1
	end
end

function ServerActivityWGData:GetRCServerDay()
	return self.sc_server_day
end

function ServerActivityWGData:GetRewardFlagList(grade)
	if self.rc_flag_list then
		return self.rc_flag_list[grade]
	end
end

-- 首充续充是否开启(全部奖励领完就隐藏)
function ServerActivityWGData:FirstChargeIsOpen()
	--首充判断
	local is_all_shouchong_reward_get = self:CheckAllSCRewardIsGet()
	if not is_all_shouchong_reward_get then
		return true
	end
	
	-- --直购判断
	-- local is_all_zhigou_reward_get = FirstRechargeRebateData.Instance:CheckAllRebateRewardIsGet()
	-- if not is_all_zhigou_reward_get then
	-- 	return true
	-- end

	--默认返回
	return false
end

function ServerActivityWGData:CheckAllSCRewardIsGet()
	local flag_list = nil
	for grade = 1, MAX_C_Z_SIZE do
		flag_list = self:GetRewardFlagList(grade)
		if not IsEmptyTable(flag_list) then
			for i=1,MAX_C_Z_DAY do
				if flag_list.reward_flag[i - 1] == 0 then
					return false
				end
			end
		end
	end
	return true
end

-- 获取首充续充奖励列表
function ServerActivityWGData:GetSCXCRewardCfg(grade, day, prof)
	local index = grade * 100 + day * 10 + prof
	if not self.shouchong_reward_cfg_list[index] then
		local show_list = self.shouchongxuchongcfg_auto.show_item or {}
		local param_list = {}
		for kk,vv in pairs(show_list) do
	 		local xianpin_type_list = Split(vv.xianpin_type_list, '|')
	 		param_list[vv.item_ID] = {star_level = vv.star_level, xianpin_type_list = xianpin_type_list}
	 	end

		local cfg_list = self.shouchongxuchongcfg_auto.rewardcfg or {}
		local data_list = {}
		local special_data_list = {}
		for _, v in pairs(cfg_list) do
			if v.grade == grade and v.day == day and (v.prof == prof or v.prof == 5) then
				local item_data = {item_id = v.item_id, num = v.num, txt = v.txt, is_bind = v.is_bind, jiaobiao = v.jiaobiao}
				if param_list[v.item_id] then
					item_data.param = param_list[v.item_id]
				end

				if v.special == 1 then
					special_data_list[#special_data_list + 1] = item_data
				else
					data_list[#data_list + 1] = item_data
				end
			end
		end

		self.shouchong_reward_cfg_list[index] = data_list
		self.shouchong_special_reward_cfg_list[index] = special_data_list
	end

	return self.shouchong_reward_cfg_list[index], self.shouchong_special_reward_cfg_list[index]
end

-- 根据标签排序获取充值类型
function ServerActivityWGData:GetSCXCChongZhiType(grade)
	if self.shouchong_type_list[grade] then
		return self.shouchong_type_list[grade]
	end
	if self.shouchongxuchongcfg_auto then
		local show_list = self.shouchongxuchongcfg_auto.rewardcfg or {}
		for i=1,#show_list do
			if show_list[i].grade == grade then
				self.shouchong_type_list[grade] = show_list[i].chongzhi_type
				break
			end
		end
	end
	return self.shouchong_type_list[grade]
end

function ServerActivityWGData:GetSCXCOtherCfg(key)
 	local other_cfg = self.shouchongxuchongcfg_auto.other[1]
 	if key and other_cfg then
 		return other_cfg[key]
 	end
	return other_cfg
end

function ServerActivityWGData:GetSCAnimCfg(grade, day)
	return CheckList(self.shouchong_anim_cfg, grade, day)
end

function ServerActivityWGData:GetSCRewardFlagByGradeDay(grade, day)
	local chongzhi_type = self:GetSCXCChongZhiType(grade)
	local gear_flag = self:GetRCGearFlag(chongzhi_type)
	if not gear_flag then
		return 0
	end

	local flag_list = self:GetRewardFlagList(grade)
	if not IsEmptyTable(flag_list) then
		if flag_list.reward_flag[day - 1] == 1 then
			return 4	-- 已领取
		else
			if flag_list.day_flag[day - 1] == 1 then
				return 3	-- 可领取
			elseif day - 2 >= 0 then
				if flag_list.day_flag[day - 2] == 1 then
					return 1 		-- 等一天
				else
					return 2		-- 等两天
				end
			end
		end
	end
	
	return 0
end

function ServerActivityWGData:GetSCRemind(grade, day)
	local chongzhi_type = self:GetSCXCChongZhiType(grade)
	local gear_flag = self:GetRCGearFlag(chongzhi_type)
	if not gear_flag then
		return false, 0
	end

	local flag_list = self:GetRewardFlagList(grade)
	if not IsEmptyTable(flag_list) then
		if day then
			if flag_list.reward_flag[day - 1] == 0 and flag_list.day_flag[day - 1] == 1 then
				return true, day
			end
		else
			for i=1,MAX_C_Z_DAY do
				if flag_list.reward_flag[i - 1] == 0 and flag_list.day_flag[i - 1] == 1 then
					return true, i
				end
			end
		end
	end

	return false, 0
end

function ServerActivityWGData:GetSCXCRemind()
	for i=1,MAX_C_Z_SIZE do
		if self:GetSCRemind(i) then
			return 1
		end
	end
	return 0
end

-- 所有档位都充值过了
function ServerActivityWGData:IsAllGradeRecharge()
	for grade = 1, MAX_C_Z_SIZE do
		local gear_flag = self:GetRCGearFlag(grade)
		if not gear_flag then
			return false
		end
	end
	return true
end

-- 是否至少达成一个档位
function ServerActivityWGData:AtLeastOneGradeRecharge()
	for grade = 1, MAX_C_Z_SIZE do
		local gear_flag = self:GetRCGearFlag(grade)
		if gear_flag then
			return true
		end
	end
	return false
end

-- 首充每档激活第一次自动打开
function ServerActivityWGData:CheckFirstRechargeOpen()
	local key_str = ""
	for grade = 1, MAX_C_Z_SIZE do
		key_str = "first_recharge_open_tip" .. grade
		local gear_flag = self:GetRCGearFlag(grade)
		local first_tip = RoleWGData.GetRolePlayerPrefsInt(key_str)
		if gear_flag and first_tip ~= 1 then
			ViewManager.Instance:Open(GuideModuleName.FirstRechargeView)
			RoleWGData.SetRolePlayerPrefsInt(key_str, 1)
		end
	end
end

--------------------------------------------------------------------
---走日常弹出提示的等级和活动是否开启
function ServerActivityWGData:GetOpenServerActCanOpen(act_type)
	if not ActivityWGData.Instance:GetActivityIsOpen(act_type) then
		return false
	end
	local cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
	if cfg then
		local level = RoleWGData.Instance:GetRoleLevel()
		if level > cfg.level then
			return true
		end
	else
		return true
	end
end

-----------------  开服活动-捐献-------------------
function ServerActivityWGData:GetQFJXDayRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").qfjx_server_day_reward
end

function ServerActivityWGData:GetQFRoleRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").qfjx_role_lj_reward
end

function ServerActivityWGData:GetQFJXDayRewardCfgMaxCount()
	local cfg = self:GetQFJXDayRewardCfg()
	if cfg ~= nil then
		return #cfg
	end
	return 0
end

function ServerActivityWGData:SetQuanFuJuanXianInfo(protocol)
	if self.JuanXianData == nil then
		self.JuanXianData = {}
	end
	self.JuanXianData.quanfu_juanxian_num = protocol.quanfu_juanxian_num        --全服捐献次数
	self.JuanXianData.role_juanxian_num = protocol.role_juanxian_num			--个人捐献次数
	self.JuanXianData.server_reward_state_list = protocol.server_reward_state_list
	self.JuanXianData.role_reward_state_list = protocol.role_reward_state_list
	self.JuanXianData.server_reward_time_list = protocol.server_reward_time_list
end

--获取全服礼包奖励状态
function ServerActivityWGData:GetGiftState(index)
	if self.JuanXianData ~= nil and self.JuanXianData.server_reward_state_list ~= nil then
		return self.JuanXianData.server_reward_state_list[index]
	end
	return 0
end

--获取有多少个激活
function ServerActivityWGData:GetActiveNum()
	local max_count = #self:GetQFJXDayRewardCfg()
	if max_count > 0 then
		local active_num = 0
		if self.JuanXianData ~= nil and self.JuanXianData.server_reward_state_list ~= nil then
			for i,v in ipairs(self.JuanXianData.server_reward_state_list) do
				if v > 0 then
					active_num = active_num + 1
				end
			end
		end
		return active_num, max_count
	end

	return 0, 1
end

--获取全服奖励生效时间
function ServerActivityWGData:GetRewardTime(index)
	if self.JuanXianData ~= nil and self.JuanXianData.server_reward_time_list ~= nil then
		return self.JuanXianData.server_reward_time_list[index]
	end
	return 0
end

--个人奖励状态
function ServerActivityWGData:GetRoleRewardState(index)
	if self.JuanXianData ~= nil and self.JuanXianData.role_reward_state_list ~= nil then
		return self.JuanXianData.role_reward_state_list[index]
	end
	return 0
end

--获取全服捐献次数和个人捐献次数
function ServerActivityWGData:GetServerJuanXianCount()
	if self.JuanXianData ~= nil then
		return self.JuanXianData.quanfu_juanxian_num
	end
	return 0
end

--获取全服捐献次数和个人捐献次数
function ServerActivityWGData:GetRoleJuanXianCount()
	if self.JuanXianData ~= nil then
		return self.JuanXianData.role_juanxian_num
	end
	return 0
end

--获取当前节点对应的百分比
function ServerActivityWGData:IsActiveServerJXReward(index)
	local cfg = self:GetQFJXDayRewardCfg()
	local server_count = self:GetServerJuanXianCount()
	local need_count = 0
	local cur_count = 0
	if index == 1 then
		need_count = cfg[index].server_day_juanxian_num
		cur_count = server_count
	else
		need_count = cfg[index].server_day_juanxian_num - cfg[index-1].server_day_juanxian_num
		cur_count = server_count - cfg[index-1].server_day_juanxian_num
	end

	if cur_count > 0 then
		local rate = cur_count / need_count
		return rate > 1 and 1 or rate
	else
		return 0
	end
end

--全民捐献红点
function ServerActivityWGData:IsShowJuanXianRedPoint()
	if not self:IsOpenActivityJuanXian() then
		return false
	end

	if self.JuanXianData ~= nil then
		local cfg = self:GetQFJXDayRewardCfg()

		if self.JuanXianData.server_reward_state_list ~= nil then
			for k,v in ipairs(self.JuanXianData.server_reward_state_list) do
				if cfg[k] ~= nil and cfg[k].reward_type == 2 then  --只有礼包需要领取
					if v == ActivityRewardState.KLQ then
						return 1
					end
				end
			end
		end

		if self.JuanXianData.role_reward_state_list ~= nil then
			for k,v in ipairs(self.JuanXianData.role_reward_state_list) do
				if v == ActivityRewardState.KLQ then
					return 1
				end
			end
		end

		local other_cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").other
		if other_cfg then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].juanxian_stuff_item_id)
			if item_num > 0 then
				return 1
			end
		end

		if self:IsXunYuLuRedPoint() then
			return 1
		end
	end
	return 0
end


function ServerActivityWGData:IsShowZhenLongRedPoint()
	return 0
end

function ServerActivityWGData:GetXunYuLuCfgList()
	local list = {}
	local cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").xunyulu
	if cfg then
		local weekday =TimeUtil.FormatSecond3MYHM1(TimeWGCtrl.Instance:GetServerTime())
		local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

		for k,v in pairs(cfg) do
			if self:IsCurDayData(v, weekday, open_server_day) then
				local info = {}
				local task_info = self:GetXunYuLuTaskInfo(v.id)
				info.cfg = v
				if task_info and task_info.status == ActivityRewardState2.WLQ and task_info.times >= v.join_times then
					info.sort = 1
				elseif task_info and task_info.status == ActivityRewardState2.YLQ then
					info.sort = 3
				else
					info.sort = 2
				end

				table.insert(list, info)
			end
		end

		table.sort(list, SortTools.KeyLowerSorter("sort"))
	end
	return list
end

function ServerActivityWGData:IsCurDayData(cfg, weekday, open_server_day)
	if cfg.open_server_day > 0 then
		if open_server_day < cfg.open_server_day then
			return false
		end

		if cfg.activity_id == ACTIVITY_TYPE.XIANMENGZHAN then  -- 仙盟战
			if cfg.open_server_day == open_server_day then
				return true
			else
				return self:IsWeekDay(cfg.act_open_days, weekday)
			end
		elseif cfg.activity_id == ACTIVITY_TYPE.KF_PVP then  -- 跨服3v3
			return self:IsWeekDay(cfg.act_open_days, weekday)
		end
	else
		return self:IsWeekDay(cfg.act_open_days, weekday)
	end
end

function ServerActivityWGData:IsWeekDay(act_open_days, weekday)
	local dayarr = Split(act_open_days, "|")
	for k,v in ipairs(dayarr) do
		if tonumber(v) == weekday then
			return true
		end
	end

	return false
end

function ServerActivityWGData:IsXunYuLuRedPoint()
	local cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").xunyulu
	if not cfg then
		return
	end

	local task_info
	local weekday =TimeUtil.FormatSecond3MYHM1(TimeWGCtrl.Instance:GetServerTime())
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for k,v in pairs(cfg) do
		if self:IsCurDayData(v, weekday, open_server_day) then
			task_info = self:GetXunYuLuTaskInfo(v.id)
			if task_info and task_info.status == ActivityRewardState2.WLQ and task_info.times >= v.join_times then
				return true
			end
		end
	end

	return false
end

function ServerActivityWGData:SetXunYuLuData(protocol)
	self.xunyulu_info_list = {}
	for k,v in pairs(protocol.info_list) do
		self.xunyulu_info_list[v.id] = v
	end
end

function ServerActivityWGData:GetXunYuLuTaskInfo(id)
	if self.xunyulu_info_list then
		return self.xunyulu_info_list[id]
	end
	return nil
end

--集字兑换最大次数

function ServerActivityWGData:GetExchangeMaxTimes(data)
	local max_times = 0
	if not data then
		return max_times
	end

	local item_num_list = {}
	for i=1,5 do
		if data["item_id_" .. i] and data["item_id_" .. i] > 0 then
			item_num_list[i] = ItemWGData.Instance:GetItemNumInBagById(data["item_id_" .. i])
		end
	end

	local temp_num
	for i,v in ipairs(item_num_list) do
		if not temp_num then
			temp_num = v
		else
			temp_num = temp_num < v and temp_num or v
		end
	end
	max_times = temp_num or 0

	return max_times
end

---[[ F2妖灵卖货
function ServerActivityWGData:SetLingYaoMaiHuoInfo(protocol)
	local info_list = {}
	info_list.buy_index_list = protocol.buy_index_list
	self.lingyaomaihuo_info = info_list
end

function ServerActivityWGData:GetLingYaoMaiHuoIndex(is_get)
	if self.lingyaomaihuo_info then
		local index_list = self.lingyaomaihuo_info.buy_index_list
		for i=0,#index_list do
			if index_list[i] == 0 then
				return i
			end
		end
		if is_get then
			local get_index = 0
			for i=0,#index_list do
				if index_list[i] == 1 then
					get_index = i
				end
			end
			return get_index
		end
	end
	return 0
end

function ServerActivityWGData:GetLingYaoMaiHuoCfg(index)
	return index and self.lingyaomaihuo_reward_cfg[index]
end

function ServerActivityWGData:GetLingYaoMaiHuoMaxIndex()
	return self.lingyaomaihuo_reward_cfg[#self.lingyaomaihuo_reward_cfg].index
end

function ServerActivityWGData:GetLingYaoMaiHuoIsBuyAllGift()
	local index = self:GetLingYaoMaiHuoIndex()
	local cfg_list = self:GetLingYaoMaiHuoCfg(index)
	return cfg_list == nil
end
--]]
---[[ F2开服礼包
function ServerActivityWGData:SetActKfGiftInfoList(protocol)
	if protocol.reason == OGA_TEHUI_LIBAO_INFO.OGA_TEHUI_LIBAO_INFO_ALL then
		self.kf_gift_info_list = {}
	end

	local info_list = self.kf_gift_info_list or {}
	for k,v in pairs(protocol.gift_info_list) do
		local data = self:GetActGiftData(v)
		if data then
			info_list[k] = data
		end
	end
	
	self.kf_gift_info_list = info_list
	self:CheckKfGiftPassTip()
	self:CheckNewKfGiftTip()
end

function ServerActivityWGData:GetActGiftData(gift_info)
	local gift_cfg = self:GetActKfGiftCfg(gift_info.libao_index)
	if not gift_cfg then
		return
	end

	local data_list = {}
	for k,v in pairs(gift_info) do
		data_list[k] = v
	end
	data_list.pass_time = gift_cfg.dur_times * 60 + gift_info.put_libao_timestamp
	data_list.can_buy_num = gift_cfg.num - gift_info.buy_num

	return data_list
end

function ServerActivityWGData:GetActKfGiftInfoList()
	return self.kf_gift_info_list or {}
end

function ServerActivityWGData:GetActKfGiftCfg(gift_index)
	local cfg_atuo = self:GetOpenGameActivityConfig()
	local cfg_list = cfg_atuo and cfg_atuo.tehuilibao
	if cfg_list then
		for i=1,#cfg_list do
			if cfg_list[i].index == gift_index then
				return cfg_list[i]
			end
		end
	end
end

-- 查看模型测试用
function ServerActivityWGData:GetTestActKfGiftCfg()
	local cfg_atuo = self:GetOpenGameActivityConfig()
	local cfg_list = cfg_atuo and cfg_atuo.tehuilibao
	local data_list = {}
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	for i=1,#cfg_list do
	    data_list[i] = {libao_index = i, put_libao_timestamp = now_time, buy_num = 0, can_buy_num = 10, pass_time = now_time + 3600 * 10}
	end
	return data_list
end

function ServerActivityWGData:ActKfGiftIsOpen()
    local gift_list = self:GetActKfGiftInfoList()
    if IsEmptyTable(gift_list) then
    	return false
    end
    
    return ServerActivityWGData.Instance:ActIsOpenByActId(ACTIVITY_TYPE.KF_GIFT, true)

    --[[ 全部礼包卖完或者过期才关闭
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local cfg_atuo = self:GetOpenGameActivityConfig()
	local cfg_list = cfg_atuo and cfg_atuo.tehuilibao
	local gift_info = nil
	for i=1,#cfg_list do
		gift_info = gift_list[cfg_list[i].libao_index]
		if not gift_info or gift_info.can_buy_num > 0 or gift_info.pass_time > now_time then
			return true
		end
	end
	return false
	--]]
end

-- 每次登陆显示一次红点提示（先把红点干掉）
function ServerActivityWGData:GetActKfGiftRemind()
	-- if self.act_kf_gift_remind then
	-- 	return self.act_kf_gift_remind
	-- end

	-- if self:ActKfGiftIsOpen() then
	-- 	-- 有可买的礼包才提示红点
	-- 	local now_time = TimeWGCtrl.Instance:GetServerTime()
	-- 	local gift_list = self:GetActKfGiftInfoList()
	-- 	if not IsEmptyTable(gift_list) then
	-- 		for _,gift_info in pairs(gift_list) do
	-- 			if gift_info.can_buy_num > 0 or gift_info.pass_time > now_time then
	-- 				return 1
	-- 			end
	-- 		end
	-- 		return 0
	-- 	end
	-- end

	return 0
end

function ServerActivityWGData:SetActKfGiftRemind(remind)
	self.act_kf_gift_remind = remind
	RemindManager.Instance:Fire(RemindName.KaiFuGiftRed)
end

function ServerActivityWGData:CheckNewKfGiftTip()
	if IS_AUDIT_VERSION then
		return
	end
	if not self:ActKfGiftIsOpen() then
		return
	end

	if ViewManager.Instance:IsOpen(GuideModuleName.ServerActivityTabView) then
		return
	end

	local gift_list = self:GetActKfGiftInfoList()
	local key = ""
	local mark = 0
	local has_new_gift = false
	local select_gift_index = nil
	local tip_time = LimitTimeGiftWGData.Instance:GetPassTipTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local count_down_time = 0
	for k,v in pairs(gift_list) do
		key = "kf_gift_" .. v.libao_index
		mark = RoleWGData.GetRolePlayerPrefsInt(key)
		if not mark or mark <= 0 then
			count_down_time = v.pass_time - server_time
			if v.can_buy_num > 0 and count_down_time > tip_time then
				has_new_gift = true
				select_gift_index = v.libao_index
			end
			RoleWGData.SetRolePlayerPrefsInt(key, 1)
		end
	end

	if has_new_gift then
		local btn_func = function ()
    		ServerActivityWGCtrl.Instance:OpenOpenserverCompetition("open_server", TabIndex.act_kf_gift, {libao_index = select_gift_index})
    	end
    	local info = {
    		btn_click = btn_func,
			-- title_img = "xs_ksifuhuodong",
			-- desc_img = "xs_liebiao_tishi3",
			-- desc_content = Language.LimitTimeGift.PassGiftTip5,
			is_new_gift = true,
			count_down = count_down_time,
			libao_index = select_gift_index
    	}
    	TipWGCtrl.Instance:OpenGiftTipView(info)
	end
end

function ServerActivityWGData:CheckKfGiftPassTip()
	if ViewManager.Instance:IsOpen(GuideModuleName.ServerActivityTabView) then
		return
	end

	local need_tip = false
	local count_down_time = 0
	local next_pass_time = 0
	local sub_time = 0
	local select_gift_index = nil
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local tip_time = LimitTimeGiftWGData.Instance:GetPassTipTime()
	local gift_list = self:GetActKfGiftInfoList()
    for k,v in pairs(gift_list) do
    	if v.can_buy_num > 0 and v.pass_time > server_time then
    		sub_time = v.pass_time - server_time
    		if sub_time < tip_time then
    			need_tip = true
    			if sub_time < count_down_time or count_down_time == 0 then
    				local mark = "kf_gift_" .. v.libao_index
    				if not TipWGCtrl.Instance:GetTipsGiftMark(mark) then
	    				count_down_time = sub_time
	    				select_gift_index = v.libao_index
	    				TipWGCtrl.Instance:SetTipsGiftMark(mark, true)
    				end
    			end
    		elseif sub_time < next_pass_time or next_pass_time == 0 then
    			next_pass_time = sub_time
    		end
    	end
    end

    CountDownManager.Instance:RemoveCountDown("kf_gift_pass_time_tip")
	local rest_time = next_pass_time - tip_time
    if rest_time > 0 then
    	CountDownManager.Instance:AddCountDown("kf_gift_pass_time_tip", nil, BindTool.Bind(self.CheckKfGiftPassTip, self), nil, rest_time)
    end

    if not self:ActKfGiftIsOpen() then
		return
	end

    if need_tip and count_down_time > 10 then -- 至少10s不然点过去就没了
    	local btn_func = function ()
    		ServerActivityWGCtrl.Instance:OpenOpenserverCompetition("open_server", TabIndex.act_kf_gift, {libao_index = select_gift_index})
    	end
    	local info = {
    		btn_click = btn_func,
			-- title_img = "xs_ksifuhuodong",
			-- desc_img = "xs_liebiao_tishi1",
			-- desc_content = Language.LimitTimeGift.PassGiftTip4,
			is_new_gift = false,
			count_down = count_down_time,
			libao_index = select_gift_index
    	}
    	TipWGCtrl.Instance:OpenGiftTipView(info, "kf_gift_" .. select_gift_index)
    end
end
--]]
---[[
-- F2从开服开始算起活动剩余时间戳
function ServerActivityWGData:GetOpenServerToSevenDayTime(act_id)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg_data = self:GetActLimitTimeCfg(act_id)
	if not cfg_data or open_day < cfg_data.open_day or open_day > cfg_data.close_day then
		return 0
	end

	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local format_time = os.date("*t", now_time)
	local end_time = os.time({
		year = format_time.year,
		month = format_time.month,
		day = format_time.day + cfg_data.close_day - open_day + 1,
		hour = 0,
		min = 0,
		sec = 0
	})
	return end_time - now_time
end

-- 判断开服活动是否开启
function ServerActivityWGData:ActIsOpenByActId(act_id, need_show)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local cfg_data = self:GetActLimitTimeCfg(act_id)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if not cfg_data or role_level < (cfg_data.level_limit or 0) then
		return false
	end
	if need_show then
		return open_day >= cfg_data.open_day and open_day <= (cfg_data.close_day + cfg_data.show_day)
	else
		return open_day >= cfg_data.open_day and open_day <= cfg_data.close_day
	end
	return false
end

function ServerActivityWGData:GetActLimitTimeCfg(act_id)
	local cfg_list = self.opengameactivity_auto.act_time_limit
	if cfg_list then
		for i=1,#cfg_list do
			if cfg_list[i].act_id == act_id then
				return cfg_list[i]
			end
		end
	end
end
--]]
---[[ 活动获得的称号
function ServerActivityWGData:IsActGetTitle(title_id)
	-- 仙盟封榜
	local act_cfg = ServerActivityWGData.Instance:GetKaiZongLiPaiNewCfg()
	for i=1,#act_cfg do
		if act_cfg[i].title_id == title_id then
			return true, ACTIVITY_TYPE.OPENSERVER_KAIZONGLIPAI
		end
	end

	return false
end
--]]

--获得排序后的list view
function ServerActivityWGData:GetSortListView(list_tb)
	local sort_list = {}
	for i, v in pairs(list_tb) do
		local data = {}
		data.index = v:GetIndex()
		data.item = v
		sort_list[#sort_list + 1] = data
	end
	table.sort(sort_list, SortTools.KeyLowerSorter("index"))
	return sort_list
end

--开服比拼无限打折购买物品
function ServerActivityWGData:GetIsOpenServerBiPinUnLimiteItem(item_id)
	if not item_id then 
		return false ,{}
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if not self.opengameactivity_auto then 
		return false ,{}
	end

	local rank_cfg = self.opengameactivity_auto.rush_rank_type
	if not rank_cfg then 
		return false ,{}
	end
	local new_data = {}
	for k,cfg_list in pairs(rank_cfg) do
		if cfg_list and cfg_list.open_game_day == open_day then
			local unlimite_list = self:GetRushLimitTimeGiftDataListByRushType(cfg_list.rush_type)
			for t,q in pairs(unlimite_list) do
				for n,m in pairs(q.daily_buy_item) do
					if m.item_id == item_id then
						local buy_call_back = function (buy_count)
							ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_LIMIT_TIME_BUY, q.rush_type, open_day, item_id, buy_count)
						end
						local buy_info = {buy_count = 1, old_price = q.old_gold,price = q.need_gold, price_type = q.gold_type, max_buy_count = q.daily_buy_times,discount_str = q.tips_discount,item_id = item_id}
						new_data.item_id = item_id
						new_data.special_buy_call_back = buy_call_back
						new_data.special_buy_info = buy_info 
						return true,new_data
					end
				end
			end
		end
	end
	return false,{}
end

function ServerActivityWGData:GetSCXCTypeCfg(type)
	local cfg = ConfigManager.Instance:GetAutoConfig("shouchongxuchongcfg_auto").type
	for k,v in ipairs(cfg) do
		if type == v.type then
			return v
		end
	end

	return nil
end

function ServerActivityWGData:BiPinGetSelectRushType(rush_type)
	if rush_type then
		self.rush_type = rush_type
	else
		return self.rush_type
	end
end

function ServerActivityWGData:BiPinActIsFirstOpen(is_open)
	if is_open then
		self.bp_is_open = is_open
	else
		return self.bp_is_open
	end
end
