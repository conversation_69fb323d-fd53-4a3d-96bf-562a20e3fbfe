DailyFuliFbSceneLogic = DailyFuliFbSceneLogic or BaseClass(CommonFbLogic)

function DailyFuliFbSceneLogic:__init()

end

function DailyFuliFbSceneLogic:__delete()
	
end

function DailyFuliFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	DailyWGCtrl.Instance:Close()

	-- XuiBaseView.CloseAllView()
end

function DailyFuliFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function DailyFuliFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	if RoleWGData.Instance.role_vo.level > 130 then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Daily)
	end
end