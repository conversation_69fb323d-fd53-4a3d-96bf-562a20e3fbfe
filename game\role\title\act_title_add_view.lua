 ------------------------------------------------------------
--开宗立派活动专用的View
------------------------------------------------------------
ActTitleAddView = ActTitleAddView or BaseClass(SafeBaseView)

local close_timer = 5 --5秒后自动佩戴

function ActTitleAddView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
	self:AddViewResource(0, "uis/view/chenghao_prefab", "layout_act_title_view")

	self.title_id = 0
	self.is_forever = false
end

function ActTitleAddView:ReleaseCallBack()
	if CountDown.Instance:HasCountDown(self.quest) then
        CountDown.Instance:RemoveCountDown(self.quest)
        self.quest = nil
    end
    if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ActTitleAddView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
	self.title_id = 0
	self.is_forever = false
end

function ActTitleAddView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_ok,BindTool.Bind1(self.Close,self))	--绑定按钮
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ActTitleAddView, self.get_guide_ui_event)
end

function ActTitleAddView:CloseCallBack()
	local use_title_list = TitleWGData.Instance:GetUsedTitleId()
	if #use_title_list > 0 then
		local mark = true
		for i=1,#use_title_list do
			if use_title_list[i] == self.title_id then
				mark = false
				break
			end
		end
		if mark then
			TitleWGCtrl.Instance:OpenTitleComparisonView(self.title_id)
		else
			TitleWGCtrl.Instance:SendUseTitleReq({self.title_id})
		end
	else
		TitleWGCtrl.Instance:SendUseTitleReq({self.title_id})
	end
end

function ActTitleAddView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "act_title_id" == k then
			if v.title_id and v.title_id > 0 then
				self.title_id = v.title_id
				self.is_forever = v.is_forever
				self:FlushPanel()
				self:SetDesc()
				self:FlushCountDown()
			end
		elseif "guild_rank" == k then
			self:SetDesc()
		end
	end
end

function ActTitleAddView:FlushPanel()
	local title_id = self.title_id
	local title_cfg = TitleWGData.GetTitleConfig(title_id)
	if not title_cfg then
		return 
	end
	local title_bundle,title_asset = ResPath.GetTitleModel(title_id)
	self.node_list["title_img"]:ChangeAsset(title_bundle, title_asset)

	-- local open_ser_data = ServerActivityWGData.Instance:GetOpenServerData()
	-- local top_bundle = "uis/images/commonpanel_atlas"
	-- local top_asset = self.is_forever and "a1_yjsh" or "a1_gxhd_gx"
	-- self.node_list.top_title_img.image:LoadSprite(top_bundle, top_asset)
end

function ActTitleAddView:SetDesc()
	local cfg = ServerActivityWGData.Instance:GetKaiZongLiPaiNewCfgByTitleId(self.title_id) -- 仙盟封榜
	if cfg then
		self.node_list.desc_1.text.text = string.format(Language.Common.BestDrops1, cfg.drop_rate / 100000)
		local role_id = RoleWGData.Instance:InCrossGetOriginUid()
		local guild_rank, member_rank = GuildWGData.Instance:GetGuildMemberRankInfoByUserId(role_id)
		if guild_rank and member_rank then
			self.node_list.desc_2.text.text = string.format(Language.OpenServer.TitleGetWay, CommonDataManager.GetDaXie(guild_rank), member_rank)
		end
		self.node_list.power_value.text.text = string.format(Language.OpenServer.ZhanliAddValue, cfg.zhanli)
	elseif FengShenBangWGData.Instance:IsFSBTitle(self.title_id) then	-- 封神榜
		local title_cfg = TitleWGData.GetTitleConfig(self.title_id)
		local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgDataByTitleID(self.title_id)
		local rank = (scene_cfg and scene_cfg.layer + 1) or ""
		local add_attribute = AttributeMgr.GetAttributteByClass(title_cfg)
		local add_military = AttributeMgr.GetCapability(add_attribute)
		self.node_list.power_value.text.text = string.format(Language.OpenServer.ZhanliAddValue, add_military)
		self.node_list.desc_1.text.text = string.format(Language.FengShenBang.AddExp, (title_cfg and title_cfg.add_exp_per / 100) or 0)
		self.node_list.desc_2.text.text = string.format(Language.FengShenBang.TitleGetWay, rank)
	else
		self.node_list.desc_1.text.text = ""
		self.node_list.desc_2.text.text = ""
	end
end

function ActTitleAddView:FlushCountDown()
	if CountDown.Instance:HasCountDown(self.quest) then
        CountDown.Instance:RemoveCountDown(self.quest)
        self.quest = nil
    end
    self:ChangeTime(0, close_timer)
    self.quest = CountDown.Instance:AddCountDown(close_timer, 1, BindTool.Bind(self.ChangeTime, self), BindTool.Bind(self.Close, self))
end

function ActTitleAddView:ChangeTime(elapse_time, total_time)
    self.node_list.btn_text.text.text = string.format(Language.Common.AdornTitle, math.ceil(total_time - elapse_time))
end

function ActTitleAddView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.AddTitleBtn then
		return self.node_list.btn_ok, BindTool.Bind1(self.Close,self)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end