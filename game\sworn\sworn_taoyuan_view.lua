function SwornView:InitSwornTaoYuanView()
    if self.taoyuan_skill_list == nil then
        self.taoyuan_skill_list = {}
        for i = 1, SwornWGData.Instance.MAX_TAOYUAN_SKILL_NUM do
            self.taoyuan_skill_list[i] = SwornTaoYuanSkillCell.New(self.node_list.taoyuan_skill_list:FindObj("skill_cell_" .. i))
        end
    end

    XUI.AddClickEventListener(self.node_list["taoyuan_box_img"], BindTool.Bind(self.ClickTaoYuanBox, self))
end

function SwornView:ShowSwornTaoYuanView()

end

function SwornView:SwornTaoYuanViewReleaseCallBack()
    if self.taoyuan_skill_list then
		for k, v in pairs(self.taoyuan_skill_list) do
            v:DeleteMe()
        end
        self.taoyuan_skill_list = nil
	end
end

function SwornView:FlushSwornTaoYuanView()
    self:FlushSwornTaoYuanOtherInfo()
    self:FlushSwornTaoYuanSkillInfo()
end

function SwornView:FlushSwornTaoYuanOtherInfo()
    local build_level = SwornWGData.Instance:GetBuildLevel()
    local build_level_cfg = SwornWGData.Instance:GetBuildLevelCfgBylevel(build_level)
    if not build_level_cfg then
        return
    end

    self.node_list.taoyuan_level.text.text = string.format(Language.Sworn.BuildLevelStr, build_level_cfg.jianzhu_name, build_level)
    local bundle, asset = ResPath.GetNoPackPNG("a2_jyjl_ty_jz" .. build_level_cfg.jianzhu_img)
    self.node_list["taoyuan_jz_img"].image:LoadSprite(bundle, asset, function()
        self.node_list["taoyuan_jz_img"].image:SetNativeSize()
    end)

    local build_exp = SwornWGData.Instance:GetBuildExp()
    local next_build_level_cfg = SwornWGData.Instance:GetBuildLevelCfgBylevel(build_level + 1)
    if not next_build_level_cfg then
        self.node_list.taoyuan_exp_slider.slider.value = 1
        self.node_list.taoyuan_progress_text.text.text = "-/-"
    else
        self.node_list.taoyuan_exp_slider.slider.value = build_exp / next_build_level_cfg.exp
        self.node_list.taoyuan_progress_text.text.text = build_exp .. "/" .. next_build_level_cfg.exp
    end

    local box_cfg = SwornWGData.Instance:GetBuildShowBoxCfg()
    if box_cfg then
        local box_bundle, box_asset = ResPath.GetSwornImg("a2_jyjl_ty_bx" .. box_cfg.box_img)
        self.node_list["taoyuan_box_img"].image:LoadSprite(box_bundle, box_asset, function()
            self.node_list["taoyuan_box_img"].image:SetNativeSize()
        end)
    end

    local build_back_num = SwornWGData.Instance:GetBuildBackNum()
    self.node_list.taoyuan_leiji_value.text.text = build_back_num
    self.node_list.taoyuan_box_img_red:SetActive(build_back_num > 0)
    self.node_list.taoyuan_box_di:SetActive(build_back_num > 0)
end

function SwornView:FlushSwornTaoYuanSkillInfo()
    local skill_list = SwornWGData.Instance:GetBuildShowSkillList()
    if self.taoyuan_skill_list then
        for i = 1, SwornWGData.MAX_TAOYUAN_SKILL_NUM do
            self.taoyuan_skill_list[i]:SetData(skill_list[i])
        end
    end
end

function SwornView:ClickTaoYuanBox()
    local build_back_num = SwornWGData.Instance:GetBuildBackNum()
    if build_back_num <= 0 then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Sworn.NoBackNum)
        return
    end

    SwornWGCtrl.Instance:SendSwornRequest(JIEYI_OPERATE_TYPE.BUILD_FANLI_LINGYU_REWARD)
end

--------------------------------SwornTaoYuanSkillCell---------
SwornTaoYuanSkillCell = SwornTaoYuanSkillCell or BaseClass(BaseRender)

function SwornTaoYuanSkillCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["skill_icon"], BindTool.Bind(self.OnSkillClick, self))
end

function SwornTaoYuanSkillCell:__delete()

end

function SwornTaoYuanSkillCell:OnFlush()
	if not self.data then
		return
	end

    self.node_list.skill_name.text.text = self.data.skill_name
    self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
    self.node_list.skill_level.text.text = string.format(Language.Common.LevelNormal, self.data.skill_level)
    self.node_list.cur_skill_desc.text.text = self.data.skill_des

    local next_skill_cfg = SwornWGData.Instance:GetBuildSkillCfgById(self.data.skill_id, self.data.skill_level + 1)
    self.node_list.next_skill_desc_group:SetActive(next_skill_cfg ~= nil)
    self.node_list.need_condition_group:SetActive(next_skill_cfg ~= nil)
    if next_skill_cfg then
        self.node_list.need_condition_str.text.text = next_skill_cfg.skill_need_condition
        self.node_list.next_condition_str.text.text = next_skill_cfg.skill_des
    end
end

function SwornTaoYuanSkillCell:OnSkillClick()
    if not self.data then
		return
	end

    local next_skill_cfg = SwornWGData.Instance:GetBuildSkillCfgById(self.data.skill_id, self.data.skill_level + 1)
    local is_max = not next_skill_cfg
	local show_data = {
        x = 0,
        y = 0, 
        set_pos2 = true,
		icon = self.data.skill_icon,
		top_text = self.data.skill_name or "",
        body_text = self.data.skill_des,
        limit_text = "",
        skill_level = self.data.skill_level,
	}

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end