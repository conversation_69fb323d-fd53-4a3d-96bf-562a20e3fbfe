FestivalTurnTableWGData = FestivalTurnTableWGData or BaseClass()
function FestivalTurnTableWGData:__init()
	if FestivalTurnTableWGData.Instance then
		error("[FestivalTurnTableWGData] Attempt to create singleton twice!")
		return
	end
	FestivalTurnTableWGData.Instance = self
	RemindManager.Instance:Register(RemindName.Festival_TurnTable, BindTool.Bind(self.GetZhouYiYunChengRed, self))
	--self.pass_day_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
	local all_cfg = ConfigManager.Instance:GetAutoConfig("festival_ra_happy_monday_auto")
	self.task_cfg = ListToMapList(all_cfg.task,"task_type")
	self.all_task_cfg = all_cfg.task
	self.item_rewared_cfg = ListToMap(all_cfg.reward_level,"level")
	self.other_cfg = all_cfg.other[1]
	self.jump_yuncheng_ani = false

	self.notify_flush = BindTool.Bind(self.CheckActiveChange,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.notify_flush)

	self.first_login_flush_remind_flag = false
	self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
	FestivalActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_HAPPY_MONDAY, {[1] = MERGE_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self),BindTool.Bind(self.GetZhouYiYunChengRed, self))

	self.task_finish_flag_list = {} --任务完成标记
	self.draw_times_fetch_flag_list = {} -- 抽奖次数领取标记
	self.task_type_param_list = {} -- 任务参数

end

function FestivalTurnTableWGData:__delete()
	FestivalTurnTableWGData.Instance = nil

	if self.notify_flush then
   		ActivityWGData.Instance:UnNotifyActChangeCallback(self.notify_flush)
    	self.notify_flush = nil
    end

	if self.remind_time_quest then
		GlobalTimerQuest:CancelQuest(self.remind_time_quest)
		self.remind_time_quest = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	self.first_login_flush_remind_flag = nil
end

function FestivalTurnTableWGData:GetActCanOpen()
	return self:GetZhouYiYunChengFunOpen()
end

function FestivalTurnTableWGData:CheckActiveChange(activity_type, status, next_time, open_type)

	if activity_type == ACTIVITY_TYPE.FESTIVAL_ACT_HAPPY_MONDAY and status == ACTIVITY_STATUS.OPEN then
		self:CheckNeedReqInfo()
	end
end

function FestivalTurnTableWGData:GetZhouYiYunChengRed()
	--红点增加活动开启判断
	local is_fun_open = self:GetZhouYiYunChengFunOpen()
	if not is_fun_open then
		return 0
	end
	if not IsEmptyTable(self.task_finish_flag_list) and not IsEmptyTable(self.draw_times_fetch_flag_list) then
		for k,v in pairs(self.all_task_cfg) do
			if self.task_finish_flag_list[v.index] == 1 and self.draw_times_fetch_flag_list[v.index] == 0 then
				return 1
			end
		end
	end

	-- if not FestivalTurnTableWGData.Instance:IsAvalibaleTime() then
	-- 	return 0
	-- end

	if self.can_draw_times and self.can_draw_times > 0 then 
		return 1
	end

	return 0
end

function FestivalTurnTableWGData:CheckIfNeedRemindTimeQuest()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_HAPPY_MONDAY)

	if activity_status and activity_status.status == ACTIVITY_STATUS.OPEN then
		self.remind_time_quest =  GlobalTimerQuest:AddRunQuest(function()
			--RemindManager.Instance:Fire(RemindName.ZhouYi_YunCheng)
		end, 5)
	else
		if self.remind_time_quest then
			GlobalTimerQuest:CancelQuest(self.remind_time_quest)
			self.remind_time_quest = nil
		end
		--RemindManager.Instance:Fire(RemindName.ZhouYi_YunCheng)
	end
end

function FestivalTurnTableWGData:OnDayChange()
	-- local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- local index = -1
	-- for k,v in pairs(self.lingxi_item_cfg) do
	-- 	if k <= open_day then
	-- 		if index == -1 then
	-- 			index = k
	-- 		elseif index < k then
	-- 			index = k
	-- 		end
	-- 	end
	-- end
	-- self.cur_xianling_item_data = self.lingxi_item_cfg[index] or {}
end

function FestivalTurnTableWGData:OnSCRAHappyMondayInfo(protocol)
	self.task_finish_flag_list = protocol.task_finish_flag_list --任务完成标记
	self.draw_times_fetch_flag_list = protocol.draw_times_fetch_flag_list -- 抽奖次数领取标记
	self.task_type_param_list = protocol.task_type_param_list -- 任务参数
	self.can_draw_times = protocol.can_draw_times --  可抽奖次数
	self.bounus_draw_times = protocol.bounus_draw_times -- 保底抽奖次数
	self.choose_reward_level = protocol.choose_reward_level -- 选择的奖励列表
	if nil == self.remind_time_quest then
		self:CheckIfNeedRemindTimeQuest()
	end
end

function FestivalTurnTableWGData:GetLeftRollTimes()
	return self.can_draw_times or 0,self.bounus_draw_times or -1
end


function FestivalTurnTableWGData:GetLeftCanRollTimes()
	return self.can_draw_times or -1
end

function FestivalTurnTableWGData:GetBonusTimes()
	return self.other_cfg.bonus_need_draw_times or 10
end

function FestivalTurnTableWGData:GetOtherCfg()
	return self.other_cfg
end

function FestivalTurnTableWGData:CheckNeedReqInfo()
	if IsEmptyTable(self.task_finish_flag_list) or IsEmptyTable(self.draw_times_fetch_flag_list) or not self.choose_reward_level then
		FestivalTrunTableWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_REQ_INFO)
		return true
	end
	return false
end

function FestivalTurnTableWGData:OnSCRAHappyMondayDrawResult(protocol)
	self.reward_level = protocol.reward_level
	self.reward_index = protocol.reward_index
	self.result_item = self.rewared_item_list[self.reward_level + 1][self.reward_index + 1]
	local all_select_item = self:GetAllSelectItemList()
	self.roll_index = -1
	for k,v in pairs(all_select_item) do
		if tonumber(v.item_id) == tonumber(self.result_item.item_id) then
			self.roll_index = k
			break
		end
	end
end

function FestivalTurnTableWGData:GetRollResult()
	return self.result_item,self.roll_index
end

function FestivalTurnTableWGData:OnSCRAHappyMondayServerRecord(protocol)
	self.server_draw_record_count = protocol.server_draw_record_count
	self.server_draw_record_item_list = protocol.server_draw_record_item_list
end

function FestivalTurnTableWGData:OnSCRAHappyMondayPersonRecord(protocol)
	self.person_draw_record_count = protocol.person_draw_record_count
	self.person_draw_record_item_list = protocol.person_draw_record_item_list
end

function FestivalTurnTableWGData:GetWorldRecord()
	return self.server_draw_record_item_list or {} ,self.server_draw_record_count or 0
end

function FestivalTurnTableWGData:GetPersonalRecord()
	return self.person_draw_record_item_list or {} ,self.person_draw_record_count or 0
end

function FestivalTurnTableWGData:GetJumpAni()
	return self.jump_yuncheng_ani or false
end

function FestivalTurnTableWGData:SetJumpAni()
	self.jump_yuncheng_ani = not self.jump_yuncheng_ani
end

function FestivalTurnTableWGData:GetItemFlagList()
	if nil == self.choose_reward_level then return {} end
	local item_flag_list = {}
	local had_select = false
	for i = 1,4 do
		item_flag_list[i] = {}
		local flag_list = bit:d2b(self.choose_reward_level[i])
		for q=1,32 do
			item_flag_list[i][q] = flag_list[33-q] == 1
			had_select = had_select and had_select or flag_list[33-q] == 1
		end
	end
	if not had_select then
		self:InitAllSelectItem()
		return {}
	end
	return item_flag_list
end

function FestivalTurnTableWGData:GetAllItemList()
	if nil == self.rewared_item_list then
		self.rewared_item_list= {}
		for i = 1, 4 do 
			self.rewared_item_list[i] = {}
			local item_data = Split(self.item_rewared_cfg[i-1].reward_item,"|")
			for k,v in pairs(item_data) do
				local item_detail = Split(v,":")
				self.rewared_item_list[i][k] = {}
				self.rewared_item_list[i][k].item_id = item_detail[1]
				self.rewared_item_list[i][k].num = item_detail[2]
				self.rewared_item_list[i][k].is_bind = item_detail[3]
				self.rewared_item_list[i][k].index1 = i
				self.rewared_item_list[i][k].index2 = k
			end
		end
	end
	return self.rewared_item_list or {}
end

function FestivalTurnTableWGData:GetAllSelectItemList()
	local choose_item_list = {}
	local all_item = self:GetAllItemList()
	local item_flag = self:GetItemFlagList()
	if IsEmptyTable(item_flag) then return {} end
	local num = 0
	for i= 1,4 do
		for k,v in pairs(all_item[i]) do
			if item_flag[i][k] then
				num = num + 1
				choose_item_list[num] = v
			end
		end
	end
	return choose_item_list,num
end

function FestivalTurnTableWGData:InitAllSelectItem()
	local default_select = {}
	for i = 1 , 4 do
		default_select[i] = {}
		for q = 1, 32 do
			local num = self.item_rewared_cfg[i-1].choose_count
			default_select[i][33-q] = num >= q and 1 or 0
		end
	end
	FestivalTrunTableWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_CHOOSE_REWARD,bit:b2d(default_select[1]),bit:b2d(default_select[2]),bit:b2d(default_select[3]),bit:b2d(default_select[4]))
	return 
end

function FestivalTurnTableWGData:GetAllTaskDetail()
	local finish_flag = self.task_finish_flag_list
	local get_flag = self.draw_times_fetch_flag_list
	local task_type_param_list = self.task_type_param_list
	local task_data_list = {}
	for k,list in pairs(self.task_cfg) do
		local cfg = list[#list]
		if #list > 1 then
			for k1,v in ipairs(list) do
				if get_flag[v.index] == 0 then
					cfg = v
					break
				end
			end
		end
		local task_type = cfg.task_type
		local index = cfg.index
		task_data_list[task_type] = {}
		task_data_list[task_type].task_type = task_type
		task_data_list[task_type].index = index
		task_data_list[task_type].complete_param = cfg.complete_param
		task_data_list[task_type].task_type_param = task_type_param_list[task_type] or 0
		task_data_list[task_type].des = cfg.des or ""
		task_data_list[task_type].open_panel = cfg.open_panel or ""
		task_data_list[task_type].finish_flag = finish_flag[index] == 1
		task_data_list[task_type].get_flag = get_flag[index] == 1	
		local sort_index = 0
		if not task_data_list[task_type].finish_flag and not task_data_list[task_type].get_flag then
			sort_index = 200
		elseif not task_data_list[task_type].get_flag then
			sort_index = 100
		elseif task_data_list[task_type].get_flag then
			sort_index = 300
		end
		task_data_list[task_type].sort_index = sort_index
	end
	table.sort(task_data_list, SortTools.KeyLowerSorters("sort_index"))
	return task_data_list
end


function FestivalTurnTableWGData:TryToSelectOrUnSelectThis(index1,index2)
	if not self.tem_select_list then return end
	local limite_num = self.item_rewared_cfg[index1-1].choose_count
	if self.tem_select_list[index1][index2] then
		self.tem_select_list[index1][index2] = not self.tem_select_list[index1][index2]
		self.tem_select_num_list[index1] = self.tem_select_num_list[index1] - 1
		return true
	elseif not self.tem_select_list[index1][index2] and limite_num > self.tem_select_num_list[index1] then
		self.tem_select_list[index1][index2] = not self.tem_select_list[index1][index2]
		self.tem_select_num_list[index1] = self.tem_select_num_list[index1] + 1
		return true
	end
	return false
end

function FestivalTurnTableWGData:GetItemIsBeenSelect(index1,index2)
	return self.tem_select_list[index1][index2] or false
end

function FestivalTurnTableWGData:CreateTempSelectList()
	self.tem_select_list = FestivalTurnTableWGData.Instance:GetItemFlagList()
	self.tem_select_num_list = {}
	for i = 1, 4 do
		local num = 0
		for k,v in pairs(self.tem_select_list[i]) do
			if v then
				num = num + 1
			end
		end
		self.tem_select_num_list[i] = num
	end
end

function FestivalTurnTableWGData:GetHaoManyWeSelect(index1)
	local limite_num = self.item_rewared_cfg[index1-1].choose_count
	local select_num = self.tem_select_num_list[index1]
	return select_num,limite_num
end

function FestivalTurnTableWGData:GetTempSelectList()
	return self.tem_select_list or {}, self.tem_select_num_list or {}
end

function FestivalTurnTableWGData:IsAvalibaleTime()
	local now_time = TimeWGCtrl.Instance:GetServerTimeFormat()
	local limite_time = Split(self.other_cfg.draw_time,",")
	if tonumber(now_time.wday) == 2 and tonumber(now_time.hour) >= tonumber(limite_time[1]) and tonumber(now_time.hour) < tonumber(limite_time[2]) then
		return true
	end
	return false 
end

function FestivalTurnTableWGData:GetRewaredLevel(item_id)
	local all_item = self:GetAllItemList()
	for i = 1, 4 do
		for k,v in pairs(all_item[i]) do
			if tonumber(v.item_id) == tonumber(item_id) then
				return tonumber(v.index1)
			end
		end
	end
	return -1
end

--活动功能开启判断
function FestivalTurnTableWGData:GetZhouYiYunChengFunOpen()
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_HAPPY_MONDAY)
	local level_limit = 0 -- ActivityWGData.Instance:GetActivityLimitLevelById(ACTIVITY_TYPE.FESTIVAL_ACT_HAPPY_MONDAY)
	local my_level = RoleWGData.Instance.role_vo.level
	if not activity_status or activity_status.status ~= ACTIVITY_STATUS.OPEN or my_level < level_limit then
		return false
	end

	return true
end

function FestivalTurnTableWGData:RoleLevelChange(attr_name, value)
	if attr_name == "level" then
		if not self.first_login_flush_remind_flag then
			local level_limit = 0 -- ActivityWGData.Instance:GetActivityLimitLevelById(ACTIVITY_TYPE.FESTIVAL_ACT_HAPPY_MONDAY)
			if value >= level_limit then
				--RemindManager.Instance:Fire(RemindName.ZhouYi_YunCheng)
				self.first_login_flush_remind_flag = true
				--刷新后移除等级监听
				if self.role_data_change then
					RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
					self.role_data_change = nil
				end
			end
		end
	end
end

function FestivalTurnTableWGData:SetTurnTableIsTurning(flag)
	self.turn_table_is_turning = flag
end

function FestivalTurnTableWGData:GetTurnTableIsTurning()
	return self.turn_table_is_turning or false
end
