CrossAirWarGatherTips = CrossAirWarGatherTips or BaseClass(SafeBaseView)

function CrossAirWarGatherTips:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(602, 424)})
	self:AddViewResource(0, "uis/view/cross_air_war_ui_prefab", "layout_kf_air_war_gather_tips")
	self:SetMaskBg(true)
end

function CrossAirWarGatherTips:ReleaseCallBack()
	if self.stuff_cell then
		self.stuff_cell:DeleteMe()
		self.stuff_cell = nil
	end

	self.data = nil
	self.callback = nil
	self.cancel_fun = nil
end

function CrossAirWarGatherTips:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Common.Hint
	self.stuff_cell = ItemCell.New(self.node_list.ph_stuff_cell)

	self.node_list.btn_ok.button:AddClickListener(BindTool.Bind(self.On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	self.node_list.btn_cancel.button:AddClickListener(BindTool.Bind(self.Close, self))
end

-- 关闭前调用
function CrossAirWarGatherTips:CloseCallBack()
	if self.cancel_fun then
		self.cancel_fun()
	end
end

function CrossAirWarGatherTips:SetData(data, callback, cancel_fun)
	self.data = data
	self.callback = callback
	self.cancel_fun = cancel_fun
	self:Open()
end

function CrossAirWarGatherTips:OnFlush()
	if not self.data then
		return
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.cost_item_id)
	local need_num = self.data.cost_item_num or 1
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.cost_item_id)
	local item_str = string.format("%sx%d", item_cfg.name or "", need_num)
	item_str = ToColorStr(item_str, ITEM_COLOR[item_cfg.color])
	self.node_list.use_stuff_tips.text.text = string.format(Language.CrossAirWar.BoxGatherTips, item_str)
	local color = has_num >= need_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH

	self.stuff_cell:SetFlushCallBack(function ()
		self.stuff_cell:SetRightBottomColorText(string.format("%d/%d", has_num, need_num), color)
		self.stuff_cell:SetRightBottomTextVisible(true)
	end)

	self.stuff_cell:SetData({item_id = self.data.cost_item_id})
end

function CrossAirWarGatherTips:OnClinkOkHandler()
	if not self.data then
		return
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.cost_item_id)
	local need_num = self.data.cost_item_num or 1
	local need_num = self.data.cost_item_num or 1
	if has_num >= need_num then
		if self.callback then
			self.callback()
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
	end
	
	self:Close()
end