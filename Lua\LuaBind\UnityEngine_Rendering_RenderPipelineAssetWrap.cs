﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Rendering_RenderPipelineAssetWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Rendering.RenderPipelineAsset), typeof(UnityEngine.ScriptableObject));
		<PERSON><PERSON>("__eq", op_Equality);
		<PERSON><PERSON>("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("renderingLayerMaskNames", get_renderingLayerMaskNames, null);
		<PERSON><PERSON>("prefixedRenderingLayerMaskNames", get_prefixedRenderingLayerMaskNames, null);
		<PERSON><PERSON>("defaultMaterial", get_defaultMaterial, null);
		<PERSON><PERSON>("autodeskInteractiveShader", get_autodeskInteractiveShader, null);
		<PERSON><PERSON>("autodeskInteractiveTransparentShader", get_autodeskInteractiveTransparentShader, null);
		<PERSON><PERSON>("autodeskInteractiveMaskedShader", get_autodeskInteractiveMaskedShader, null);
		<PERSON><PERSON>("terrainDetailLitShader", get_terrainDetailLitShader, null);
		L.RegVar("terrainDetailGrassShader", get_terrainDetailGrassShader, null);
		L.RegVar("terrainDetailGrassBillboardShader", get_terrainDetailGrassBillboardShader, null);
		L.RegVar("defaultParticleMaterial", get_defaultParticleMaterial, null);
		L.RegVar("defaultLineMaterial", get_defaultLineMaterial, null);
		L.RegVar("defaultTerrainMaterial", get_defaultTerrainMaterial, null);
		L.RegVar("defaultUIMaterial", get_defaultUIMaterial, null);
		L.RegVar("defaultUIOverdrawMaterial", get_defaultUIOverdrawMaterial, null);
		L.RegVar("defaultUIETC1SupportedMaterial", get_defaultUIETC1SupportedMaterial, null);
		L.RegVar("default2DMaterial", get_default2DMaterial, null);
		L.RegVar("default2DMaskMaterial", get_default2DMaskMaterial, null);
		L.RegVar("defaultShader", get_defaultShader, null);
		L.RegVar("defaultSpeedTree7Shader", get_defaultSpeedTree7Shader, null);
		L.RegVar("defaultSpeedTree8Shader", get_defaultSpeedTree8Shader, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderingLayerMaskNames(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			string[] ret = obj.renderingLayerMaskNames;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderingLayerMaskNames on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_prefixedRenderingLayerMaskNames(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			string[] ret = obj.prefixedRenderingLayerMaskNames;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index prefixedRenderingLayerMaskNames on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autodeskInteractiveShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.autodeskInteractiveShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autodeskInteractiveShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autodeskInteractiveTransparentShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.autodeskInteractiveTransparentShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autodeskInteractiveTransparentShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autodeskInteractiveMaskedShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.autodeskInteractiveMaskedShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autodeskInteractiveMaskedShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_terrainDetailLitShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.terrainDetailLitShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index terrainDetailLitShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_terrainDetailGrassShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.terrainDetailGrassShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index terrainDetailGrassShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_terrainDetailGrassBillboardShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.terrainDetailGrassBillboardShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index terrainDetailGrassBillboardShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultParticleMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultParticleMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultParticleMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultLineMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultLineMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultLineMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTerrainMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultTerrainMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultTerrainMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultUIMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultUIMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultUIMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultUIOverdrawMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultUIOverdrawMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultUIOverdrawMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultUIETC1SupportedMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.defaultUIETC1SupportedMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultUIETC1SupportedMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_default2DMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.default2DMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index default2DMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_default2DMaskMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Material ret = obj.default2DMaskMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index default2DMaskMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.defaultShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultSpeedTree7Shader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.defaultSpeedTree7Shader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultSpeedTree7Shader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultSpeedTree8Shader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Rendering.RenderPipelineAsset obj = (UnityEngine.Rendering.RenderPipelineAsset)o;
			UnityEngine.Shader ret = obj.defaultSpeedTree8Shader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index defaultSpeedTree8Shader on a nil value");
		}
	}
}

