local normal_viewport_high = 100
local normal_panel_high = 390
local ATTR_COUNT = 10

--boss奖励预览面板
BossRewardView = BossRewardView or BaseClass(SafeBaseView)

function BossRewardView:__init()
    self:SetMaskBg(true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    -- self.view_layer = UiLayer.MainUIHigh
    self.view_name = "BossRewardView"
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_reward")
end

function BossRewardView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Boss.RewardDes

    -- self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.Close, self))
    if not self.reward_grid then
        self.reward_grid = AsyncBaseGrid.New()
		local t = {}
		t.col = 4
		t.change_cells_num = 1
		t.itemRender = ItemCell
		t.list_view = self.node_list["ph_grid"]
		self.reward_grid:CreateCells(t)
        self.reward_grid:SetStartZeroIndex(false)
    end

    if not self.join_reward_grid then
        self.join_reward_grid = AsyncBaseGrid.New()
		local t1 = {}
		t1.col = 4
		t1.change_cells_num = 1
		t1.itemRender = ItemCell
		t1.list_view = self.node_list["ph_grid_1"]
		self.join_reward_grid:CreateCells(t1)
        self.join_reward_grid:SetStartZeroIndex(false)
    end

    self.des_list = {}
    for i=1,ATTR_COUNT do
		self.des_list[i] = self.node_list["text" .. i]
	end
end

function BossRewardView:ReleaseCallBack()
    if self.reward_grid then
        self.reward_grid:DeleteMe()
        self.reward_grid = nil
    end
    
    if self.join_reward_grid then
        self.join_reward_grid:DeleteMe()
        self.join_reward_grid = nil
    end

    self.des_list = nil
end

function BossRewardView:SetData(cfg)
    self.data = cfg

    if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function BossRewardView:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local list = {}
    local drop_item_list = self.data.drop_item_list or {}
    for i = 0, #drop_item_list do
        table.insert(list,drop_item_list[i])
    end

    self.reward_grid:SetDataList(list, 2)
    local has_join_reward = not IsEmptyTable(self.data.join_drop_item_list)
    self.node_list.join_container:SetActive(has_join_reward)
    self.node_list.scroll.rect.anchoredPosition = Vector2(62, has_join_reward and -158 or 8)

    if has_join_reward then
        local list1 = {}
        for i = 0, #self.data.join_drop_item_list do
            table.insert(list1,self.data.join_drop_item_list[i])
        end
        self.join_reward_grid:SetDataList(list1, 2)
    end

    local reward_tips_des = Split(self.data.reward_tips_des, "\n") 
    local index = #reward_tips_des
    for i = 1, ATTR_COUNT do
        self.des_list[i]:SetActive(i <= index)
        if i <= index then
            self.des_list[i].text.text = reward_tips_des[i]
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.des_list[i].rect)
        end
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.des_list.rect)
    -- local now_high = self.node_list.des_list.rect.sizeDelta.y
    -- local panel_high = normal_panel_high
    -- if now_high < normal_viewport_high then
        -- self.node_list.scroll.scroll_rect.enabled = false
        -- panel_high = normal_panel_high - (normal_viewport_high - now_high)
    -- else
        -- self.node_list.scroll.scroll_rect.enabled = true
        -- panel_high = normal_panel_high
    -- end
    -- local high = has_join_reward and 156 or 0 
    -- self.node_list.panel.rect.sizeDelta = Vector2(370, panel_high + high)
end



