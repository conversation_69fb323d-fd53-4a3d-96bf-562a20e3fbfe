﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Networking_DownloadHandlerScriptWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Networking.DownloadHandlerScript), typeof(UnityEngine.Networking.DownloadHandler));
		<PERSON><PERSON>("New", _CreateUnityEngine_Networking_DownloadHandlerScript);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Networking_DownloadHandlerScript(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Networking.DownloadHandlerScript obj = new UnityEngine.Networking.DownloadHandlerScript();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else if (count == 1)
			{
				byte[] arg0 = ToLua.CheckByteBuffer(L, 1);
				UnityEngine.Networking.DownloadHandlerScript obj = new UnityEngine.Networking.DownloadHandlerScript(arg0);
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Networking.DownloadHandlerScript.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

