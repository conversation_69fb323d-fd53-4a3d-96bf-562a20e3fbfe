-- 拼图任务
TianshenRoadJigsawTask = TianshenRoadJigsawTask or BaseClass(SafeBaseView)

function TianshenRoadJigsawTask:__init()
	self:SetMaskBg(false)
    
    self:AddViewResource(0, "uis/view/tianshenroad_ui_prefab", "jigsaw_task_panel")
    
    self.is_loaded = false
end

function TianshenRoadJigsawTask:ReleaseCallBack()
	if self.sq_task_list then
		self.sq_task_list:DeleteMe()
		self.sq_task_list = nil
	end
	
	if self.get_guide_ui_event then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TianshenRoadJigsawTask, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

    self.is_loaded = false
end

function TianshenRoadJigsawTask:LoadCallBack()
    self.sq_task_list = AsyncListView.New(TSSQTaskItemRender, self.node_list.sq_task_list)
    self.is_loaded = true
	self:DoTRSQCellsAnim()
	
	--功能引导注册
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TianshenRoadJigsawTask, self.get_guide_ui_event)
end

function TianshenRoadJigsawTask:OpenCallBack()
    if self.is_loaded then
        self:DoTRSQCellsAnim()
    end
end

function TianshenRoadJigsawTask:OnFlush()
    local task_list = TianshenRoadWGData.Instance:GetSQTaskList()
	if task_list then
		self.sq_task_list:SetDataList(task_list)
	end
end

function TianshenRoadJigsawTask:DoTRSQCellsAnim()
	local tween_info = UITween_CONSTS.TianshenRoadView.ListCellRender
	self.node_list["sq_task_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["sq_task_list"]:SetActive(true)
        local list =  self.sq_task_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyTRSQItemAnim(count)
        end
    end, tween_info.DelayDoTime, "TRSQ_Cell_Tween")
end

function TianshenRoadJigsawTask:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.BtnLingQu then
		local obj = self.sq_task_list and self.sq_task_list:GetItemAt(1)
		if obj then
			return obj.node_list.btn_lingqu, BindTool.Bind(obj.OnClickRewardHnadler, obj)
		end
	elseif ui_name == GuideUIName.BtnCloseWindow then
		return self.node_list[ui_name], BindTool.Bind(self.Close, self)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

----------------------------------------------------------------------------------------------------

TSSQTaskItemRender = TSSQTaskItemRender or BaseClass(BaseRender)

function TSSQTaskItemRender:LoadCallBack()
	self.item_list = {}
	self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
	self.node_list["btn_go"].button:AddClickListener(BindTool.Bind1(self.OnClickGoHnadler, self))
	self.sq_task_reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
end

function TSSQTaskItemRender:__delete()
	if self.sq_task_reward_list then
		self.sq_task_reward_list:DeleteMe()
		self.sq_task_reward_list = nil
	end
end

function TSSQTaskItemRender:OnFlush()
	if not self.data then
		return
	end 

	local receive_state = TianshenRoadWGData.Instance:GetSQTaskState(self.data.cfg.ID)
	self.node_list["task_name"].text.text = self.data.cfg.task_name
	self.node_list["condition"].text.text = self:GetTaskDesc()

	self.node_list["btn_lingqu"]:SetActive(receive_state == 1)
	self.node_list["btn_yilingqu"]:SetActive(receive_state == 2)
	self.node_list["btn_go"]:SetActive(receive_state == 0 and self.data.cfg.panel ~= "")

	self.sq_task_reward_list:SetDataList(SortTableKey(self.data.cfg.reward_item))
end

function TSSQTaskItemRender:OnClickRewardHnadler(sender)
	TianshenRoadWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.GOD_WANT_SHENQI,WOYAOSHENQI_OP_TYPE.TASK_REWARD,self.data.cfg.ID)
end

function TSSQTaskItemRender:OnClickGoHnadler(sender)
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")  
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end

function TSSQTaskItemRender:GetTaskDesc()
	local str = self.data.cfg.task_desc
	local param = TianshenRoadWGData.Instance:GetShenQiTaskParamNum(self.data.cfg.ID)
	if param ~= nil then
		local pos = string.find(str, "%%s")
    	if pos ~= nil then 
			local color = param >= self.data.cfg.task_param1 and "<color=#3c8652>%d</color>" or "<color=#ff0000>%d</color>"
			param = math.min(param, self.data.cfg.task_param1)
			str = string.format(str, string.format(color, param))
    	end
	end
	return str
end

function TSSQTaskItemRender:PalyTRSQItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.TianshenRoadView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.TianShenRoadPanel, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "tssq_item_" .. wait_index)
end
