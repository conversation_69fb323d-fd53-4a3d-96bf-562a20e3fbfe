-- 好友邀请
WelfareView = WelfareView or BaseClass(SafeBaseView)

function WelfareView:InitFriendYaoqin()
	local ph = self.node_list["ph_friend_yq_ranklist"]
	self.rank_list = AsyncListView.New(FriendRankItemRender,ph)
	if not self.reward_list then
		self.reward_list = AsyncListView.New(FriendRewardItemRender,self.node_list["ph_friend_yq_rewardlist"])
	end
	XUI.AddClickEventListener(self.node_list["btn_req_yaoqing"], BindTool.Bind1(self.BtnYaoQingHander, self))
	XUI.AddClickEventListener(self.node_list["btn_opentips"], BindTool.Bind1(self.OnOpenTips, self))
	WelfareWGCtrl.Instance:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_REQ_INFO)
end

function WelfareView:DeleteFriend()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function WelfareView:OnOpenTips()
	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(Language.Welfare.FriendTitle)
	rule_tip:SetContent(Language.Welfare.FriendTips)
end


function WelfareView:FlushFriend()
	if nil == self.rank_list then
		self:InitFriendYaoqin()
	end
	local data = WelfareWGData.Instance:GetSyncFriendInviteInfo()
	-- 刷新左侧列表数据
	if nil ~= self.rank_list and data and data.invite_list then
		self.rank_list:SetDataList(data.invite_list)
	end

	local list = WelfareWGData.Instance:UpdataReward()
	-- 刷新右侧列表数据
	self.reward_list:SetDataList(list)

	local jumpindex = -1
	for i, v in ipairs(list) do
		if v.status == COMPLETE_STATUS.WEILINGQU then
			jumpindex = i
			break
		end
	end

	if -1 == jumpindex then
		for i, v in ipairs(list) do
			if v.status == COMPLETE_STATUS.DEFAULT then
				jumpindex = i
				break
			end
		end
	end

	if jumpindex > 0 then
		self.reward_list:SetSelectItemToTop(jumpindex)
	else
		self.reward_list:SetSelectItemToTop(1)
	end
end

-- 好友按钮邀请函数处理事件
function WelfareView:BtnYaoQingHander()
	local items = {Language.Society.FriendInviety, Language.Society.NearInviety}
	UiInstanceMgr.Instance:OpenCustomMenu(items, nil, BindTool.Bind1(self.OnInviteMenuCallback, self), nil)
end

function WelfareView:OnInviteMenuCallback(index, sender)
	if 1 == index then
		WelfareWGCtrl.Instance:OpenWelfareSetdata(index)
		-- self:OnFriendInvite()
	elseif 2 == index then
		WelfareWGCtrl.Instance:OpenWelfareSetdata(index)
		-- self:OnNearInvite()
	end
end

--好友邀请
function WelfareView:OnFriendInvite()
	SocietyWGCtrl.Instance:OpenFriendListView(16130,function (user_info)
		if nil ~= user_info then
			WelfareWGCtrl.Instance:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_REQ_INVITE, user_info.user_id)
		end
	end)
end

--附近玩家邀请
function WelfareView:OnNearInvite()
	SocietyWGCtrl.Instance:OpenNearListView(16130,function (user_info)
		if nil ~= user_info then
			WelfareWGCtrl.Instance:SendFriendinviteOperate(FRIENDINVITE_TYPE.FRIENDINVITE_REQ_INVITE, user_info.user_id)
		end
	end)
end
