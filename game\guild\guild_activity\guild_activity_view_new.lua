function GuildView:InitGuildActivityView()
    self.load_guild_cell_complete = false
    self.guild_act_cell_list = {}
    local guild_act_data = GuildActivityWGData.Instance:GetActData()
    local res_async_loader = AllocResAsyncLoader(self, "guild_activity_render")
	res_async_loader:Load("uis/view/guild_ui_prefab", "guild_activity_render", nil,
		function(new_obj)
			for i = 1, #guild_act_data do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.node_list["Content"].transform, false)
				local item_render = GuildActivityRender.New(obj)
                item_render:SetParent(self)
				item_render:SetIndex(i)
				item_render:SetClickCallBack(BindTool.Bind(self.OnClickGuildCell, self))
				self.guild_act_cell_list[i] = item_render
				if i == #guild_act_data then
					self.load_guild_cell_complete = true
				end
			end

			if self.load_guild_cell_complete then
				self:initList()
			end
		end)
end

function GuildView:initList()
    local guild_act_data = GuildActivityWGData.Instance:GetActData()
	for k,v in ipairs(self.guild_act_cell_list) do
		local data = guild_act_data[k]
		v:SetData(data)
	end

    local cell = self.guild_act_cell_list[1]
    if cell and not cell.is_click_cur then
        self:OnClickGuildCell(cell)
    end
end

function GuildView:OnClickGuildCell(cell)
	if not cell then
		return
	end

	local is_show = cell.is_click_cur
	local cell_index = cell:GetIndex()
	for k,v in ipairs(self.guild_act_cell_list) do
		if cell_index ~= k
		or (is_show and cell_index == k) then
			v:SetHideShow()
		end
	end

	if not is_show then
		cell:DoShowTween()
	end
end

function GuildView:ShowGuildCell()
    local cell = self.guild_act_cell_list[1]
	if not cell then
		return
	end

	for k,v in ipairs(self.guild_act_cell_list) do
		v:SetHideShow()
	end

	cell:DoShowTween()
end

function GuildView:DeleteGuildActivityView()
	self.load_guild_cell_complete = nil

	if self.guild_act_cell_list then
		for k, v in pairs(self.guild_act_cell_list) do
            v:DeleteMe()
		end

		self.guild_act_cell_list = nil
	end
end

function GuildView:ShowGuildActivityCallBack()
    GuildWGCtrl.Instance:SendReqBossInfo()
    GuildAnswerWGCtrl.Instance:SendCSGuildQuestionQueryRank()
    if self.load_guild_cell_complete then
        self:ShowGuildCell()
    end
end

function GuildView:OnFlushGuildActivityView(param)

end

function GuildView:OnClickGoBtn(type)
    ViewManager.Instance:Open(GuideModuleName.GuildActivityView, nil, nil, { activity_type = type })

    --[[
    if type == GuildActivityWGData.Act_Type.Boss then
        local data = GuildActivityWGData.Instance:GetActDataByType(type)
        if not data or not data.cfg then
            return
        end

        self:GuildActBossGoFunc()
    elseif type == GuildActivityWGData.Act_Type.DaTi then
        if RoleWGData.Instance.role_vo.guild_id == 0 then
            GuildWGCtrl.Instance:Open(TabIndex.guild_guildlist)
        else
            FunOpen.Instance:OpenViewNameByCfg("guildanswer")
        end
    elseif type == GuildActivityWGData.Act_Type.ShouHu then
        local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GUILD_FB)
        if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
            return
        end

        local is_finish = GuildWGData.Instance:IsFinishGuildFb()
        if is_finish then
        	SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ShouHuFinish)
        	return
        end

        GuildWGCtrl.Instance:SendGuildFbEnterReq()
    end
    ]]
end

----------------
GuildActivityRender = GuildActivityRender or BaseClass(BaseRender)

function GuildActivityRender:LoadCallBack()
    self.is_click_cur = false

    if self.reward_item_list == nil then
       self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
    end

    XUI.AddClickEventListener(self.node_list.btn_act, BindTool.Bind(self.OnClickTitle, self))
    XUI.AddClickEventListener(self.node_list.go_btn, BindTool.Bind(self.OnClickCellGoBtn, self))
end

function GuildActivityRender:__delete()
    self.parent = nil

    if self.reward_item_list then
        self.reward_item_list:DeleteMe()
        self.reward_item_list = nil
    end
end

function GuildActivityRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local act_data = GuildActivityWGData.Instance:GetActDataByType(self.data.cfg.type)
    local act_cfg = BiZuoWGData.Instance:GetAllActivityHallCfgByActType(self.data.cfg.act_id)
    if not act_data or not act_data.act_hall_cfg or not act_cfg then
        return
    end

    local bundle, asset = ResPath.GetGuildSystemImage("a3_xm_hd" .. self.data.cfg.type)
    self.node_list.guild_activity_bg.image:LoadSprite(bundle, asset)
    self.node_list.time_date.text.text = act_data.act_hall_cfg.open_tips
    self.node_list.time_txt.text.text = act_data.act_hall_cfg.open_time .. "-" .. act_data.act_hall_cfg.close_time
    local time = act_data.act_hall_cfg.open_tips .. act_data.act_hall_cfg.open_time .. "-" .. act_data.act_hall_cfg.close_time

    self.node_list.content_text.text.text = string.format(act_data.cfg.act_rule, time)
    self.reward_item_list:SetDataList(act_data.reward_list)
end

function GuildActivityRender:SetClickCallBack(callback)
	self.click_callback = callback
end

function GuildActivityRender:OnClickTitle()
	if self.click_callback then
		self.click_callback(self)
	end
end

function GuildActivityRender:OnClickCellGoBtn()
    if RoleWGData.Instance:GetRoleLevel() < self.data.cfg.open_level then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildActLevelTips)
        return
    end

    if self.parent then
		self.parent:OnClickGoBtn(self.data.cfg.type)
	end
end

function GuildActivityRender:SetParent(parent)
	self.parent = parent
end

function GuildActivityRender:SetHideShow()
    if(self.is_click_cur) then
        self.is_click_cur = false
        self.node_list.content_root.rect:DOSizeDelta(Vector2(0, 544), 0.1):OnComplete(function()
            if not self.is_click_cur then
                self.node_list.content_root:SetActive(false)
            end
        end)
        self.node_list.interval:SetActive(true)
    end
end

function GuildActivityRender:DoShowTween()
	self.is_click_cur = true
	self.node_list.content_root:CustomSetActive(true)
    self.node_list.interval:SetActive(false)
	RectTransform.SetSizeDeltaXY(self.node_list.content_root.rect, 0, 544)
	self.node_list.content_root.rect:DOSizeDelta(Vector2(378, 544), 0.4)
end