--收缩状态
local ShrinkState = {
    Open = 0,
    Close = 1,
    Close_None = 2,
}

LoverPkMatchingView = LoverPkMatchingView or BaseClass(SafeBaseView)

function LoverPkMatchingView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(36, 10), sizeDelta = Vector2(796, 535)})
    self:AddViewResource(0, "uis/view/cross_lover_prefab", "layout_loverpk_match")
end

function LoverPkMatchingView:LoadCallBack()
    self.member_info_list = {}
	for i = 1, 2 do
        if not self.member_info_list[i] then
            self.member_info_list[i] = KFLoverPKMatchMemberInfoRender.New(self.node_list["info"..i])
        end
    end

    XUI.AddClickEventListener(self.node_list.btn_canel, BindTool.Bind(self.CancelMatch, self))
    XUI.AddClickEventListener(self.node_list.btn_xiaohua, BindTool.Bind(self.Close, self)) --最小化
    self.node_list.title_view_name.tmp.text = Language.LoverPK.MatchViewTitleName
end

function LoverPkMatchingView:ReleaseCallBack()
    if  self.member_info_list ~= nil then
        for i, v in pairs(self.member_info_list) do
            v:DeleteMe()
        end

        self.member_info_list = nil
    end
end

function LoverPkMatchingView:ShowIndexCallBack()
    self:PlayPanelTween(ShrinkState.Open)
    self.cur_time_value = LoverPkWGData.Instance:GetMatchTime()
    self:RefreshState()
end

function LoverPkMatchingView:Open()
    --初始化缓存的状态,防止频繁的去访问C#
    SafeBaseView.Open(self)
    self.max_tip_state = true
    self.pre_container_state = nil
end

function LoverPkMatchingView:Close()
    self:PlayPanelTween(ShrinkState.Close)
end

function LoverPkMatchingView:CloseCallBack()
    self.pre_container_state = nil
end

function LoverPkMatchingView:OnFlush()
    local match_type = LoverPkWGData.Instance:GetMatchingTeamType()

    if match_type == CROSS_COUBLE_2V2_MATCH_TYPE.SINGLE_PERSON then
        local role_vo = GameVoManager.Instance:GetMainRoleVo()
        self.member_info_list[1]:SetData(role_vo)
        self.node_list["info"..1]:CustomSetActive(true)
        self.node_list["info"..2]:CustomSetActive(false)
    else
        local member_info_list = SocietyWGData.Instance:GetTeamMemberList()

        for i = 1, 2 do
            if member_info_list[i] then
                self.member_info_list[i]:SetData(member_info_list[i])
            end
            self.node_list["info"..i]:CustomSetActive(true)
        end
    end
end

function LoverPkMatchingView:CancelMatch()
    LoverPkWGCtrl.Instance:ReqCancelMatch()
    self:PlayPanelTween(ShrinkState.Close_None)
end

-- 走这里刷新
function LoverPkMatchingView:RefreshState()
    self.cur_time_value = LoverPkWGData.Instance:GetMatchTime()
    self:RefreshPanelState()
end

function LoverPkMatchingView:RefreshPanelState()
    local pre_time, is_max = NewTeamWGData.Instance:GetTeamMatchPreTime(self.cur_time_value)
    if is_max then
        self.node_list.pre_time.tmp.text = Language.NewTeam.MaxMatchTimeTips
        if self.pre_container_state then
            self.node_list.pre_container:SetActive(false)
            self.pre_container_state = false
        end
        if not self.max_tip_state then
            self.node_list.max_time_tip:SetActive(true)
            self.max_tip_state = true
        end
    else
        self.node_list.pre_time.tmp.text = TimeUtil.MSTime(pre_time)
        if not self.pre_container_state then
            self.node_list.pre_container:SetActive(true)
            self.pre_container_state = true
        end
        if self.max_tip_state then
            self.node_list.max_time_tip:SetActive(false)
            self.max_tip_state = false
        end
    end

    self.node_list.cur_time.tmp.text = TimeUtil.MSTime(self.cur_time_value)
end

function LoverPkMatchingView:PlayPanelTween(play_type)
    local start_pos = SocietyWGData.Instance:GetIsInTeam() == 1 and Vector3(200, -149, 0) or Vector3(-100, -149, 0)
    local end_pos = Vector3.zero
    local tween_time = 0.5

    if ShrinkState.Open == play_type then
        self.root_node_transform.localScale = Vector3.zero
        self.root_node_transform.anchoredPosition = start_pos
        self.root_node_transform:DOScale(1, tween_time)
        self.root_node_transform:DOAnchorPos(end_pos, tween_time)
    elseif ShrinkState.Close == play_type then
        self.root_node_transform.localScale = Vector3.one
        self.root_node_transform.anchoredPosition = end_pos
        self.root_node_transform:DOScale(0, tween_time)
        self.root_node_transform:DOAnchorPos(start_pos, tween_time)

        GlobalTimerQuest:AddDelayTimer(function()
                SafeBaseView.Close(self)
        end, tween_time)
    elseif ShrinkState.Close_None == play_type then 
        SafeBaseView.Close(self)
    end
end

--------------------------------------KFLoverPKMatchMemberInfoRender----------------------------------------
KFLoverPKMatchMemberInfoRender = KFLoverPKMatchMemberInfoRender or BaseClass(BaseRender)

function KFLoverPKMatchMemberInfoRender:LoadCallBack()
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function KFLoverPKMatchMemberInfoRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function KFLoverPKMatchMemberInfoRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local data_role_id = self.data.role_id or self.data.orgin_role_id
    local data = {}
    data.role_id = data_role_id
    data.prof = self.data.prof
    data.sex = self.data.sex
    data.fashion_photoframe = self.data.shizhuang_photoframe
    self.head_cell:SetData(data)

    local role_id = RoleWGData.Instance:GetRoleVo().role_id
    local is_leader = self.data.is_leader and self.data.is_leader == 1 or (role_id == data_role_id and SocietyWGData.Instance:GetIsTeamLeader() == 1)
    self.node_list.leader_img:SetActive(is_leader)

    self.node_list.text_vip:SetActive(self.data.vip_level > 0)
    self.node_list.text_vip.tmp.text = string.format(Language.NewTeam.VipLevel,  self.data.vip_level)

    local str = string.format(Language.NewTeam.PTLevel, self.data.level)
    EmojiTextUtil.ParseRichText(self.node_list["role_level"].emoji_text, str, 21, COLOR3B.DEFAULT)

    local temp_name = string.format(Language.LoverPK.ServerDefName, self.data.server_id)
    self.node_list.role_server.tmp.text = temp_name
    self.node_list.role_name.tmp.text = self.data.name
end
