CollectBlessWGData = CollectBlessWGData or BaseClass()

function CollectBlessWGData:__init()
	if CollectBlessWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[CollectBlessWGData] attempt to create singleton twice!")
		return
	end
	CollectBlessWGData.Instance = self
	self.card_reward_flag = {[0] = -1,-1,-1,-1,-1,-1,-1,-1,-1}
	self.word_reward_flag = 0
	self.word_num = 0
end

function CollectBlessWGData:__delete()
	CollectBlessWGData.Instance = nil
end

function CollectBlessWGData:SetCollectyBlessingInfo(protocol)
	self.card_reward_flag = protocol.card_reward_flag
	self.word_reward_flag = protocol.word_reward_flag
	self.word_num = protocol.word_num
end

function CollectBlessWGData:GetCollectBlessCardRewardFlag(index)
	return self.card_reward_flag[index]
end

function CollectBlessWGData:GetCollectBlessWordRewardFlag(index)
	return bit:d2b(self.word_reward_flag)[32 - index] or 0
end

function CollectBlessWGData:GetCollectBlessWordNum()
	return self.word_num
end

function CollectBlessWGData:GetCollectBlessOther()
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = cfg.other
	local other = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS)

	return other[1]
end

function CollectBlessWGData:GetCollectBlessCardInfo(index)
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = cfg.collect_blessing_card_reward
	local collect_blessing_card_reward_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS)
	for k,v in pairs(collect_blessing_card_reward_cfg) do
		if v.seq == index then
			return v
		end
	end
end

function CollectBlessWGData:GetCollectBlessWordInfo()
	local cfg1 = {}
	local cfg2 = {}
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = cfg.collect_blessing_word_reward
	local collect_blessing_word_reward_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_ACTIVITY_COLLECTBLESS)

	for i,v in ipairs(collect_blessing_word_reward_cfg) do
		if v.type == 1 then
			table.insert(cfg1, v)
		else
			table.insert(cfg2, v)
		end
	end
	table.sort(cfg1, SortTools.KeyLowerSorter("seq"))
	return cfg1, cfg2
end
