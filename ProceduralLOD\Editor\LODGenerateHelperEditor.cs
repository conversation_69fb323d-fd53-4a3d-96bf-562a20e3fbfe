using System;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace ProceduralLOD
{
    
    [CustomEditor(typeof(LODGenerateHelper))]
    public class LODGenerateHelperEditor : Editor
    {
        protected LODGenerateHelper m_Component;
        
        private Type[] m_SimplifierTypes;
    
        private void OnEnable()
        {
            m_Component = this.target as LODGenerateHelper;
            m_SimplifierTypes = Array.FindAll(typeof(Simplifier).Assembly.GetTypes(), t => t.IsSubclassOf(typeof(Simplifier)));
        }

        public override void OnInspectorGUI()
        {
            LODGroup lodGroup = m_Component.GetComponent<LODGroup>();

            if (lodGroup != null)
            {
                if (m_Component.reductionStrengths != null && m_Component.reductionStrengths.Length > 0)
                {
                    for (int i = 0; i < m_Component.reductionStrengths.Length; i++)
                    {
                        EditorGUILayout.BeginHorizontal();
                        float reductionStrength = m_Component.reductionStrengths[i];
                        ReductionStrengthItemGUI(i, reductionStrength);
                        EditorGUILayout.EndHorizontal();
                    }
                }
                else
                {
                    EditorGUILayout.LabelField("[未生成程序LOD]");
                }
            
                EditorGUILayout.LabelField("尺寸：" + m_Component.size.ToString("F2"));

                EditorGUILayout.LabelField("", GUI.skin.GetStyle("ToolbarSlider"), GUILayout.Height(20));
                
                SimplifierGUI();

                EditorGUILayout.BeginVertical("Box");
                EditorGUILayout.BeginHorizontal();
                if (GUILayout.Button("重置LOD距离"))
                {
                    m_Component.ResetSwitchDistance();
                }
                
                if (GUILayout.Button("刷新LOD材质"))
                {
                    this.m_Component.RefreshMaterials();
                }
                
                EditorGUILayout.EndHorizontal();

                EditorGUILayout.BeginHorizontal();
                
                GUI.backgroundColor = Color.green;
                if (GUILayout.Button("刷新LOD"))
                {
                    m_Component.Build();
                }
                GUI.backgroundColor = Color.white;
                GUI.backgroundColor = Color.red;
                if (GUILayout.Button("清除LOD"))
                {
                    this.m_Component.ClearAutoLOD();
                }

                GUI.backgroundColor = Color.white;
                
                EditorGUILayout.EndHorizontal();
                EditorGUILayout.EndVertical();
                
            }
            else
            {
                SimplifierGUI();
                if (GUILayout.Button("创建LOD"))
                {
                    m_Component.Build();
                }
            }
            
            EditorGUILayout.EndFoldoutHeaderGroup();

            this.serializedObject.ApplyModifiedProperties();
        }

        protected void SimplifierGUI()
        {
            int selection = Array.IndexOf(m_SimplifierTypes, m_Component.simplifier.GetType());
            string[] names = m_SimplifierTypes.Select(t => t.Name).ToArray();
            EditorGUI.BeginChangeCheck();
            selection = EditorGUILayout.Popup("优化工具", selection, names);
            if (EditorGUI.EndChangeCheck())
            {
                m_Component.simplifier = Activator.CreateInstance(m_SimplifierTypes[selection]) as Simplifier;
            }
        }

        protected virtual void ReductionStrengthItemGUI(int i, float reductionStrength)
        {
            EditorGUILayout.LabelField($"LOD {i + 1}  面数: {(100 - reductionStrength).ToString("N2")}%");
            if (GUILayout.Button("编辑"))
            {
                m_Component.Modify(i + 1);
            }
        }
    }
}