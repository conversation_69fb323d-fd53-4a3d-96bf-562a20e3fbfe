BrowseView = BrowseView or BaseClass(SafeBaseView)

function BrowseView:InitAttrView()
	self.is_feixian = false

	-- 装备
	local layout_equip = self.node_list.layout_equip
	self.equip_grid = {}
	for i = 0, GameEnum.EQUIP_INDEX_XIANZHUO do --COMMON_CONSTS.MAX_EQUIPMENT_GRID_NUM - 1
		--local ph= self.node_list["ph_equip_cell_" .. i]
		--if nil ~= ph then
			local cell = DiamondItemCell.New(self.node_list["ph_equip_cell_" .. i])
			cell:SetName("equip")
			--cell:SetNeedUselessModdal(false)
			cell:SetItemTipFrom(ItemTip.FROME_BROWSE_ROLE)
			cell:SetItemIcon(self.GetEquipBg(i))  -- 设置物品图标
			cell:SetItemIconLocalScale(0.7)

			self.equip_grid[i] = cell
		--end
    end
    if self.node_list.ph_equip_cell_10 then
        self.node_list.ph_equip_cell_10:SetActive(false)
    end
	--self:SetGridStyle()
	local xiao_gui_pos = self.node_list["ph_equip_cell_xiaogui_1"]
	self.xiao_gui_item = DiamondItemCell.New(xiao_gui_pos)
	self.xiao_gui_item:SetItemIconLocalScale(0.7)
	local xiao_gui_pos2 = self.node_list["ph_equip_cell_xiaogui_2"]
	self.xiao_gui_item2 = DiamondItemCell.New(xiao_gui_pos2)
	self.xiao_gui_item2:SetItemIconLocalScale(0.7)

	self.role_info_attri = ScrollGrid.New()
	self.role_info_attri:CreateCells({col = 2,cell_count = 7 ,itemRender = BrowseAttrItem,list_view = self.node_list["list_view_arr"]})
	--self.role_info_attri:Create(BrowseAttrItem, self.node_list["list_view_arr"])
	self.role_info_attri:SetStartZeroIndex(false)

	local special_attr_list = RoleWGData.Instance:GetRoleAttr(ROLE_SPECIAL_ATTR_TYPE.SPECIAL_ATTR) or {}
	local cell_count = (#special_attr_list > 0) and #special_attr_list - 1 or 0
	self.role_info_attri_special = ScrollGrid.New()
	self.role_info_attri_special:CreateCells({col = 2, cell_count = cell_count, itemRender = BrowseAttrItem, list_view = self.node_list["list_view_arr_special"]})
	self.role_info_attri_special:SetStartZeroIndex(false)
	self.node_list.attr_toggle_1.toggle.isOn = true

	--XUI.AddClickEventListener(self.node_list.btn_switchhead, BindTool.Bind(self.OnClickHdHeadHnadler, self))
	-- XUI.AddClickEventListener(self.node_list.default_head_icon, BindTool.Bind(self.OnClickHdHeadHnadler, self), false)
	--XUI.AddClickEventListener(self.node_list["img_vip"], BindTool.Bind(self.OnClickVipCallBack, self), true)

	if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	end
end

function BrowseView:ReleaseCallBackAttr()
	if self.xiao_gui_item then
		self.xiao_gui_item:DeleteMe()
		self.xiao_gui_item = nil
	end
	if self.xiao_gui_item2 then
		self.xiao_gui_item2:DeleteMe()
		self.xiao_gui_item2 = nil
	end

	if nil ~= self.attr_capability then
		self.attr_capability:DeleteMe()
		self.attr_capability = nil
	end

	if nil ~= self.equip_grid then
		for k, v in pairs(self.equip_grid) do
			v:DeleteMe()
		end
		self.equip_grid = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.role_info_attri then
		self.role_info_attri:DeleteMe()
		self.role_info_attri = nil
	end
	if self.role_info_attri_special then
		self.role_info_attri_special:DeleteMe()
		self.role_info_attri_special = nil
	end

	if nil ~= self.vip_level_number then
		self.vip_level_number:DeleteMe()
		self.vip_level_number = nil
	end

	if nil ~= self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	-- if nil ~= self.node_list.default_head_icon then
	-- 	AvatarManager.Instance:CancelUpdateAvatar(self.node_list.default_head_icon.node)
	-- end
end

function BrowseView:OnFlushAttr(param_list)
    local role_info = BrowseWGData.Instance:GetRoleInfo()
    if role_info.role_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
        if not IsEmptyTable(self.equip_grid) then
            for k, v in pairs(self.equip_grid) do
                if v then
                    v:SetItemTipFrom(ItemTip.FORM_ROBOT_SHOW_INFO)
                end
            end
        end
    else
        if not IsEmptyTable(self.equip_grid) then
            for k, v in pairs(self.equip_grid) do
                if v then
                    v:SetItemTipFrom(ItemTip.FROME_BROWSE_ROLE)
                end
            end
        end
    end
	-- -- 头像框显示
	-- if role_info.appearance.fashion_photoframe then
	-- 	local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.PHOTOFRAME, role_info.shizhuang_info[SHIZHUANG_TYPE.PHOTOFRAME].use_index)
	-- 	local resouce = role_info.appearance.fashion_photoframe
	-- 	self.node_list.head_icon_frame.image:LoadSprite(ResPath.GetPhotoFrame(resouce))
	-- end
	-- 战力
	--self.attr_capability:SetNumber(role_info.capability)

	local appearance = role_info and role_info.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = role_info.role_id
	data.sex = role_info.sex
	data.prof = role_info.prof

    local index = AvatarManager.Instance:GetAvatarKey(role_info.role_id, true)
    if index <= GameEnum.CUSTOM_HEAD_ICON then --系统头像
        self.role_head_cell:SetIsBgClick(false)
    else
        self.role_head_cell:SetIsBgClick(true)
    end
	self.role_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.role_head_cell:SetData(data)

	-- 是否跨服
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
	local main_role_plat_type = RoleWGData.Instance:GetPlatType()
	local server_id = role_info.server_id or main_role_server_id
	local plat_type = role_info.plat_type or main_role_plat_type
	local is_same_server = server_id == main_role_server_id and plat_type == main_role_plat_type
	if self.node_list["img_attr_capability"] then
		--self.node_list["img_attr_capability"]:SetActive(false)
		self.node_list["img_attr_capability"]:SetActive(is_same_server) --25/7/21跨服不显示战力
		if nil == self.node_list.capability_num then return end
		self.node_list["capability_num"].text.text = role_info.capability
	end

	-- 名字
	self.node_list.name_txt.text.text = (role_info.role_name)

	-- 属性
	local base_prof = RoleWGData.Instance:GetRoleProf()
	local attr_t = self:GetRoleAttrList(base_prof)
	local value_t = self:GetRoleAttrValueT(base_prof)
	local attr_data = {}
	for k,v in ipairs(attr_t) do
		local key = AttributeMgr.GetAttributteKey(v)
		attr_data[k] = {}
		if v == "evil" then
			attr_data[k].attr_value = role_info["evil"]
		elseif v == "all_charm" then
			attr_data[k].attr_value = role_info["all_charm"]
		else
			attr_data[k].attr_value = value_t[key]
		end
		attr_data[k].attr_name = key
	end

	local attr_data_special = {}
	local special_attr_list = RoleWGData.Instance:GetRoleAttr(ROLE_SPECIAL_ATTR_TYPE.SPECIAL_ATTR) or {}
	for k,v in ipairs(special_attr_list) do
		attr_data_special[k] = {}
		attr_data_special[k].attr_value = value_t[v]
		attr_data_special[k].attr_name = v
	end
	self.role_info_attri_special:SetDataList(attr_data_special,0)

	if self.role_info_attri then
		self.role_info_attri:SetDataList(attr_data,0)
	end
	-- 等级
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(role_info.level)
	self.node_list.Image_feixian:SetActive(is_vis)
	self.node_list.level_txt.text.text = is_vis and string.format(Language.Role.XXJi, role_level) or role_level

	-- 帮派
	local guild_txt_node = self.node_list.guild_name_txt
	if 0 == role_info.guild_id and guild_txt_node then
		guild_txt_node.text.text = (Language.Role.NoGuild)
	else
		guild_txt_node.text.text = (role_info.guild_name)
	end

	-- 伴侣
	local lover_txt_node = self.node_list.lover_name_txt
	if 0 == role_info.lover_info.lover_uid and lover_txt_node then
		lover_txt_node.text.text = (Language.Common.No)
	else
		lover_txt_node.text.text = (role_info.lover_info.lover_name)
	end

	-- 师徒
	-- local shifu_txt_node = self.node_list.shifu_name_txt
	-- if 0 == role_info.shitu_data.shifu_id and shifu_txt_node then
	-- 	shifu_txt_node.text.text = (Language.Common.No)
	-- else
	-- 	shifu_txt_node.text.text = (role_info.shitu_data.shifu_name)
	-- end

	-- 区服名称
	local server_name = ""
	self.node_list.server_name_txt.text.text = string.format(Language.WorldServer.ServerName_1, server_name, role_info.server_id)

	-- vip
	local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(role_info.shield_vip_flag)
	self.node_list.img_vip:SetActive(role_info.vip_level > 0)
	self.node_list.vip_num.text.text = is_hide_vip and "V" or "V".. role_info.vip_level

	-- if nil ~= self.vip_level_number then
	-- 	self.vip_level_number:SetNumber(role_info.vip_level)
	-- end

	-- 装备
	local equip_info_list = BrowseWGData.Instance:GetEquipInfoList()
	for k, v in pairs(self.equip_grid) do
		v:SetRightTopImageTextActive(false)
		if equip_info_list[k] and equip_info_list[k].item_id <= 0 then
			v:SetData(nil)
			if k <=10 then
				v:SetItemIcon(self.GetEquipBg(k))  -- 设置物品图标
			end
		else
			v:SetData(equip_info_list[k])
			-- if equip_info_list[k].param.suit_index and equip_info_list[k].param.suit_index >= 0 then
			-- 	local asset_name = "a3_equip_suit_" .. (equip_info_list[k].param.suit_index)
	        --     local bundle, asset = ResPath.GetLoadingPath(asset_name)
			-- 	self.equip_grid[k]:SetEqSuitIcon(bundle, asset, true)
			-- 	v:SetRightTopImageTextActive(true)
			-- end
		end
		v:Nodes("item_icon"):SetActive(true)
	end

	-- 小鬼
	local xiaogui_data = BrowseWGData.Instance:GetXiaoGuiInfoList()
	local xiaogui_use_data = BrowseWGData.Instance:GetXiaoGuiUseInfoList()
	self.xiao_gui_item:SetItemIcon(self.GetEquipBg(11))  -- 设置物品图标
	if xiaogui_data[1].item_id and xiaogui_data[1].item_id > 0 then
		self.xiao_gui_item:SetData(xiaogui_data[1])
		self.xiao_gui_item:SetCellBgEnabled(false)

		self.xiao_gui_item:Nodes("item_icon"):SetActive(true)
		self.xiao_gui_item:SetRightTopImageTextActive(false)
	else
		self.xiao_gui_item:SetData(nil)
		self.xiao_gui_item:SetItemIconValue(true)
	end

	if xiaogui_data[2].item_id and xiaogui_data[2].item_id > 0 then
		self.xiao_gui_item2:SetItemIcon(self.GetEquipBg(12))  -- 设置物品图标
		self.xiao_gui_item2:SetData(xiaogui_data[2])
		self.xiao_gui_item2:SetCellBgEnabled(false)

		self.xiao_gui_item2:Nodes("item_icon"):SetActive(true)
		self.xiao_gui_item2:SetRightTopImageTextActive(false)
	else
		self.xiao_gui_item2:SetData(nil)
		self.xiao_gui_item2:SetItemIconValue(true)
		if xiaogui_use_data[2] >= 0 then
		--	self.xiao_gui_lock[self.xiao_gui_list[i]] = false
			self.xiao_gui_item2:SetItemIcon(ResPath.GetEquipIcon(12))
			-- self.xiao_gui_item2:SetPartName(Language.Bag.ShouHu)
		else
			--self.xiao_gui_lock[self.xiao_gui_list[i]] = true
			self.xiao_gui_item2:SetItemIcon(ResPath.GetCommonImages("a1_suo_bb2"))
			-- self.xiao_gui_item2:SetPartName("")
		end
	end

	--模型
	self:FlushModel()
end

function BrowseView:FlushModel()
	if nil == self.role_model then
		self.role_model= RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["RoleDisplay"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.M,
		-- 	can_drag = true,
		-- }

		-- self.role_model:SetRenderTexUI3DModel(display_data)

		self.role_model:SetUISceneModel(self.node_list["RoleDisplay"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, 0)
	end

	if self.role_model then
		local role_info = BrowseWGData.Instance:GetRoleInfo()
		local special_status_table = {ignore_halo = true}
		self.role_model:SetModelResInfo(role_info, special_status_table)
		if role_info.updateattr[1] ~= nil and role_info.updateattr[1].use_image_id > 0 then
			self.role_model:SetBaoJuResid(role_info.updateattr[1].use_image_id)
		end

		self.role_model:FixToOrthographic(self.root_node_transform)
	end
end

function BrowseView:SetGridStyle()
	if not self.equip_grid then
		return
	end
	for i = 0, 9 do
		if self.equip_grid[i] then
			--self.equip_grid[i]:SetItemIcon(self.GetEquipBg(i))  -- 设置物品图标
			self.equip_grid[i]:SetCellBgEnabled(false)
		end
	end

	-- for k,v in pairs(self.equip_grid:GetAllCell())do
	-- 	v:SetEquipCell(true)
	-- end
end

function BrowseView.GetEquipBg(i)
	return  ResPath.GetEquipIcon(i)--"equip_ta_" ..
end

--选择格子
function BrowseView:SelectCellCallBack(cell)
	if cell == nil or cell:GetData() == nil then
		if 8 == cell.index or 9 == cell.index then                 -- 戒指手镯
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ROLEINFOSCGETTIP)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ROLEINFONCGETTIP)
		end
		return
	end
	local cell_data = cell:GetData()
	if cell:GetName() == GRID_TYPE_EQUIP then			--打开tip, 提示脱下装备
		TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.FROME_BROWSE_ROLE, {fromIndex = cell.index})
	end
end

--头像回调
function BrowseView:AvatarLoadCallBack(role_id, raw_img_obj, path)
	if path == nil then
		path = AvatarManager.GetFilePath(role_id, false)
	end

	raw_img_obj.image:LoadSprite(path)
end

function BrowseView:GetRoleAttrList(prof)
	local attr_t = RoleWGData.Instance:GetRoleAttr(ROLE_SPECIAL_ATTR_TYPE.BASE_ATTR)
	return attr_t
end

function BrowseView:GetRoleAttrValueT(prof)
	local role_info = __TableCopy(BrowseWGData.Instance:GetRoleInfo())
	return AttributeMgr.GetAttributteByClass(role_info)
end

function BrowseView:OnClickHdHeadHnadler()
	BrowseWGCtrl.Instance:HeadOpen()
end

-----------------------------------
--角色属性itemrender
BrowseAttrItem = BrowseAttrItem or BaseClass(BaseGridRender)
function BrowseAttrItem:__init()
	--self.not_icon = {"evil", "all_charm", "lei", "feng", "huo", "yu", "lei_kang", "feng_kang", "huo_kang", "yu_kang"}
end

function BrowseAttrItem:OnFlush()
	if self.data == nil then return end
	for i=1,2 do
		if nil ~= self.data[i] then
			local attr_value = self.data[i].attr_value or 0
			if EquipmentWGData.Instance:GetAttrIsPerByAttrStr(self.data[i].attr_name) then
				attr_value = tonumber(attr_value) == 0 and attr_value .. "%" or tonumber(attr_value) / 100 .."%"
			else
				attr_value = CommonDataManager.ConverNumberToThousand2(attr_value)
			end
			if not Language.Common.AttrNameList2[self.data[i].attr_name] then
				print_error('Language.Common.TipsAttrNameList[self.data[i].attr_name]',self.data[i].attr_name)
			else
				--AttrNameList2
				self.node_list["attr"..i].text.text = Language.Common.AttrNameList2[self.data[i].attr_name]
				self.node_list["value"..i].text.text = "<color=#ffffff>" .. attr_value.."</color>"
			end
		else
			self.node_list["attr"..i].text.text = ""
			self.node_list["value"..i].text.text = ""
		end
	end
end

function BrowseAttrItem:CreateSelectEffect()
end
