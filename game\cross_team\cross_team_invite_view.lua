-- 跨服组队邀请
CrossTeamInviteView = CrossTeamInviteView or BaseClass(SafeBaseView)

function CrossTeamInviteView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(814, 580)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_invite")
	self:SetMaskBg()
    self.select_index = 0
    self.view_name = "CrossTeamInviteView"
	self.data_list = {}
end

function CrossTeamInviteView:__delete()
end

function CrossTeamInviteView:ReleaseCallBack()
	if self.invite_list then
		self.invite_list:DeleteMe()
		self.invite_list = nil
	end
	if self.role_head_cell ~= nil then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

--打开界面请求一大波数据
function CrossTeamInviteView:OpenCallBack()
	SocietyWGCtrl.Instance:SendFriendInfoReq()
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_NEAR_ROLE)
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_LIST)
	if 0 ~= RoleWGData.Instance.role_vo.guild_id then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
	end
end

function CrossTeamInviteView:LoadCallBack()
	--self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(812,520)
    -- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	self:SetSecondView(nil, self.node_list["size"])
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleInvite
	self.node_list["layout_blank_tip"]:SetActive(false)

	self.invite_list = AsyncListView.New(CrossTeamInviteListItem, self.node_list["ph_invite_list"])

    self.node_list["ph_btn_" .. 4]:SetActive(false)
	for i=1,4 do
		self.node_list["ph_btn_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickInviteSelect, self, i))
	end
	self.node_list.btn_invite_all.button:AddClickListener(BindTool.Bind(self.OnClickInviteAll,self))
end

function CrossTeamInviteView:OnClickInviteAll()
    local is_invite = false
    local is_merge = false
    local merge_user_id_list = {}
	for k,v in pairs(self.data_list) do
        if v.team_index < 0 then
            if CrossTeamWGData.Instance:GetCacheCDByRoleid(v.uuid) <= 0 then
                is_invite = true
                CrossTeamWGData.Instance:AddCacheCDList(v.uuid)
                CrossTeamWGCtrl.Instance:SendInviteTeamReq(v.uuid)
            end
        elseif v.team_index >= 0 then
            if CrossTeamWGData.Instance:GetCacheCDByRoleid(v.uuid) <= 0 then
            	is_merge = true
	        	CrossTeamWGData.Instance:AddCacheCDList(v.uuid)
	        	table.insert(merge_user_id_list, v.uuid)
           end 	      	
		end
	end

    if is_merge and #merge_user_id_list > 0 then
		if #merge_user_id_list == 1 then
			CrossTeamWGCtrl.Instance:SendTeamMergeReq(merge_user_id_list[1])
		else
			CrossTeamWGCtrl.Instance:SendTeamOneKeyMergeReq(#merge_user_id_list, merge_user_id_list)
		end
	end
	if is_invite or is_merge then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.AutoInviteSucessTip)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OperateFrequencyTip)
	end
end

function CrossTeamInviteView:OnClickConfirm()
end

function CrossTeamInviteView:ShowIndexCallBack()
	self.node_list["ph_btn_1"].toggle.isOn = true
	self:OnClickInviteSelect(1)
	self:Flush()
end

function CrossTeamInviteView:OnFlush()
	local min_level, max_level = CrossTeamWGData.Instance:GetTeamLimitLevel()

	local data_list = {}
	if 1 == self.select_index then
        local temp_list = CrossTeamWGData.Instance:GetNearFriendList()
		for i, v in pairs(temp_list) do
			if not CrossTeamWGData.Instance:GetTargetIsTeamMember(v.uuid) and v.level >= min_level then -- v.team_index <= 0 and
				local data = v
				data.team_type = TEAM_INVITE_TYPE.FRIEND
				table.insert(data_list, v)
			end
		end
	elseif 2 == self.select_index then
        local temp_list = CrossTeamWGData.Instance:GetNearRoleList()
		for k,v in pairs(temp_list) do
			if not CrossTeamWGData.Instance:GetTargetIsTeamMember(v.uuid) and v.level >= min_level then
				v.team_type = TEAM_INVITE_TYPE.NEAR
				table.insert(data_list, v)
			end
		end
	elseif 3 == self.select_index then
		local temp_list = CrossTeamWGData.Instance:GetNearGuildList()
		for k,v in pairs(temp_list) do
			if not CrossTeamWGData.Instance:GetTargetIsTeamMember(v.uuid) and v.level >= min_level then
				v.team_type = TEAM_INVITE_TYPE.GUILD
				table.insert(data_list, v)
			end
		end
	-- elseif 4 == self.select_index then
	-- 	local list = ZhanDuiWGData.Instance:GetZhanDuiOtherMemberList()
	-- 	for i, v in ipairs(list) do
	-- 		if v.scene_id ~= 0 and not CrossTeamWGData.Instance:GetTargetIsTeamMember(v.uid) and v.level >= min_level then
	-- 			if sex_limit == GameEnum.SEX_NOLIMIT or sex_limit == v.sex then
	-- 				local data = v
	-- 				data.is_zhandui_index = true
	-- 				table.insert(data_list, data)
	-- 			end
	-- 		end
	-- 	end
	end
	
	self.data_list = data_list
	if nil ~= self.data_list and nil ~= self.invite_list then
		self.invite_list:SetDataList(self.data_list)
	end

	if #self.data_list == 0 then
		self.node_list["layout_blank_tip"]:SetActive(true)
		self.node_list["btn_invite_all"]:SetActive(false)
		if self.select_index == 1 then
			self.node_list["lbl_tips"].tmp.text = (Language.NewTeam.Friends)
		elseif self.select_index == 2 then
			self.node_list["lbl_tips"].tmp.text = (Language.NewTeam.Nearby)
		elseif self.select_index == 3 then
			if 0 ~= RoleWGData.Instance.role_vo.guild_id then
				self.node_list["lbl_tips"].tmp.text = (Language.NewTeam.FairyUnion)
			else
				self.node_list["lbl_tips"].tmp.text = (Language.Common.PleaseJoinGuild)
			end
		-- elseif self.select_index == 4 then
		-- 	if ZhanDuiWGData.Instance:GetIsInZhanDui() then
		-- 		self.node_list["lbl_tips"].tmp.text = (Language.NewTeam.NotZhanDuiMemberOnline)
		-- 	else
		-- 		self.node_list["lbl_tips"].tmp.text = (Language.Common.PleaseJoinZhanDui)
		-- 	end
		end
	else
		self.node_list["btn_invite_all"]:SetActive(true)
		self.node_list["layout_blank_tip"]:SetActive(false)
	end

end

function CrossTeamInviteView:ForceFlushInviteList()
	if not self:IsOpen() then return end
	if CrossTeamWGData.Instance:GetTeamMemberCount() == 3 then
		self:Close()
	end
    local index = self.select_index or 2
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_NEAR_ROLE)
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_LIST)
    if 1 == index then
		SocietyWGCtrl.Instance:SendFriendInfoReq()
	elseif 2 == index then
		--CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_NEAR_ROLE)
	elseif 3 == index then
		if 0 ~= RoleWGData.Instance.role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
		--else
		--	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
		end
	-- elseif 4 == index then
	-- 	ZhanDuiWGCtrl.Instance:ReqZhanDuiInfo()
	end
end

function CrossTeamInviteView:OnClickInviteSelect(index , force_flush)
    if index == self.select_index and not force_flush then return end
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_NEAR_ROLE)
    CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_LIST)
	if 1 == index then
		SocietyWGCtrl.Instance:SendFriendInfoReq()
	elseif 2 == index then
		
	elseif 3 == index then
		if 0 ~= RoleWGData.Instance.role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
		--else
		--	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
		end
	-- elseif 4 == index then
	-- 	ZhanDuiWGCtrl.Instance:ReqZhanDuiInfo()
    end

	self.select_index = index
	self:Flush()
end

function CrossTeamInviteView:CreateRoleHeadCell(uuid, role_name, prof, sex, is_online, node, plat_type, server_id, plat_name)
	if self.role_head_cell == nil then
		self.role_head_cell = RoleHeadCell.New(false)
	end
	CrossTeamWGCtrl.Instance:QueryCrossTeamInfo(uuid, function(protocol)
		if self:IsLoaded() and self:IsOpen() then
			local main_role = Scene.Instance:GetMainRole()
			local main_role_vo = main_role.vo
			local role_info = {
				role_id = uuid.temp_low,
				role_name = role_name,
				prof = prof,
				sex = sex,
				is_online = is_online,
				team_index = protocol.team_index,
				team_type = TEAM_INVITE_TYPE.CHAT,
				plat_type = main_role_vo.plat_type,
				plat_name = main_role_vo.plat_name,
				server_id = server_id,
			}
			self.role_head_cell:SetRoleInfo(role_info)
			self.role_head_cell:OpenMenu(node)
		end
	end)
end

function CrossTeamInviteView:FlushTextInvite()
    if not IsEmptyTable(self.invite_list.cell_list) then
        for k, v in pairs(self.invite_list.cell_list) do
            v:FlushTextInvite()
        end
    end
end

------------------itemRender-----------------
CrossTeamInviteListItem = CrossTeamInviteListItem or BaseClass(BaseRender)

function CrossTeamInviteListItem:__init()
	self.is_myself = false
	XUI.AddClickEventListener(self.node_list["btn_invite"], BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list["head_click"], BindTool.Bind1(self.OnClickHead, self))
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function CrossTeamInviteListItem:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function CrossTeamInviteListItem:FlushTextInvite()
    local uuid = self.data.uuid
    if self.data.team_index then
		if 0 > self.data.team_index then
			self.node_list["text_invite"].tmp.text = (Language.NewTeam.Invite)
		else
			if CrossTeamWGData.Instance:GetIsInTeam() == 1 then
				self.node_list["text_invite"].tmp.text = (Language.NewTeam.Merge)
			end
		end
	else
		self.node_list["text_invite"].tmp.text = (Language.NewTeam.Invite)
    end
    if CrossTeamWGData.Instance:GetCacheCDByRoleid(uuid) > 0 then
        self.node_list["text_invite"].tmp.text = CrossTeamWGData.Instance:GetCacheCDByRoleid(uuid)
        XUI.SetButtonEnabled(self.node_list["btn_invite"], false)
        return
    end
    XUI.SetButtonEnabled(self.node_list["btn_invite"], true)
end

function CrossTeamInviteListItem:OnFlush()
	if nil == self.data then
		return
	end

	self:FlushTextInvite()

	local name = self.data.name
	local uuid = self.data.uuid

	-- if self.data.vip_level and self.data.vip_level > 0 then
	-- 	self.node_list["vip_level"]:SetActive(true)
	-- 	local vip_res_name = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag) and "vip_hide" or "vip"..self.data.vip_level
	-- 	local bundle, asset = ResPath.GetVipIcon(vip_res_name)
	-- 	self.node_list.vip_level.image:LoadSpriteAsync(bundle, asset, function ()  		
	-- 		self.node_list.vip_level.image:SetNativeSize()
	-- 	end)

	-- else
	-- 	self.node_list["vip_level"]:SetActive(false)
	-- end
	self.node_list["vip_level"].tmp.text = self.data.vip_level > 0 and ("V" .. self.data.vip_level) or ""

	local level_str = string.format(Language.NewTeam.PTLevel3, self.data.level)
	EmojiTextUtil.ParseRichText(self.node_list["lbl_level"].emoji_text, level_str, 20, COLOR3B.WHITE)

	self.node_list.lbl_role_name.tmp.text = name
    self.node_list.power_right.tmp.text = self.data.capability
	local relation_flag = bit:d2b_two(self.data.relation_flag or 0)
	local index = -1
	for k,v in pairs(relation_flag) do
		if v == 1 then
			index = k
			break
		end
    end
	local relation_str = Language.NewTeam.TeamMateRelation[index + 2]
	if index + 2 == 1 then
		relation_str = ToColorStr(relation_str, COLOR3B.DEFAULT)
	end
	self.node_list.lbl_relation.tmp.text = relation_str

	local data = {}
	data.role_id = uuid.temp_low
	data.prof = self.data.prof
	data.sex = self.data.sex
	data.fashion_photoframe = self.data.shizhuang_photoframe or self.data.fashion_photoframe
    self.head_cell:SetData(data)
	self.node_list["power_icon"].image:LoadSprite(ResPath.GetCommonImages(RoleWGData.GetProfIcon(data.prof, data.sex)))
end

function CrossTeamInviteListItem:OnClickInvite()
	local uuid = self.data.uuid

    --战队
	-- if self.data.is_zhandui_index then
	-- 	if 0 == CrossTeamWGData.Instance:GetIsInTeam() then
	-- 		local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	-- 		NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
    --     end
    --     NewTeamWGData.Instance:AddCacheCDList(self.data.uid)
	-- 	NewTeamWGCtrl.Instance:SendInviteUser(self.data.uid, 0, 1)
	-- else
    if self.data.team_index >= 0 then
        if 0 == CrossTeamWGData.Instance:GetIsInTeam() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CurNotTeam)
            return
        end

        if 0 == CrossTeamWGData.Instance:GetIsTeamLeader() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.OnlyLeaderCanHeBing)
            return
        end
        CrossTeamWGData.Instance:AddCacheCDList(uuid)
        --合并
        CrossTeamWGCtrl.Instance:SendTeamMergeReq(uuid)
    else
        -- if 0 == CrossTeamWGData.Instance:GetIsInTeam() then
        --     local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
        --     local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
        --     NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
        -- end
        CrossTeamWGData.Instance:AddCacheCDList(uuid)
        CrossTeamWGCtrl.Instance:SendInviteTeamReq(uuid)
    end
	--end
end

function CrossTeamInviteListItem:OnClickHead()
    local uuid = self.data.uuid
	local name = self.data.name
    local role_id = uuid.temp_low
    local is_cross = not self:IsSameServer()
    BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
					--ChatWGCtrl.Instance:CreateRoleHeadCell(param_protocol.role_id, self.data.username,param_protocol.prof,param_protocol.is_online, node, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
		CrossTeamWGCtrl.Instance:CreateInviteRoleHeadCell(uuid, name, param_protocol.prof,param_protocol.sex,param_protocol.is_online, self.node_list.head_click, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
	end, nil, is_cross)
end


function CrossTeamInviteListItem:IsSameServer()
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
    local main_role_plat_type = RoleWGData.Instance:GetPlatType()
    local plat, server
    if self.data.usid then
        plat, server = self.data.usid.temp_high, self.data.usid.temp_low
    end
	local server_id = server or main_role_server_id
    local plat_type = plat or main_role_plat_type
    self.plat_type = plat_type
	return server_id == main_role_server_id and plat_type == main_role_plat_type
end