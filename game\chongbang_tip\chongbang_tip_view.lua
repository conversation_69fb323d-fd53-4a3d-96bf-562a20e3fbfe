ChongBangTipView = ChongBangTipView or BaseClass(SafeBaseView)

function ChongBangTipView:__init()
	self:AddViewResource(0, "uis/view/chongbang_tip_prefab", "layout_chongban_tip")
	self.view_layer = UiLayer.Pop
end

function ChongBangTipView:__delete()
end

function ChongBangTipView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["go_btn"], BindTool.Bind(self.ClickGoBtn,self))
	XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind(self.OnClickClose,self))
	XUI.AddClickEventListener(self.node_list["not_tip_toggle"], BindTool.Bind(self.ClickNotTipToggle,self))

 	self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValue<PERSON>hang<PERSON>, self))
    self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, BindTool.Bind1(self.MainMenuIconChangeEvent, self))
end

function ChongBangTipView:ReleaseCallBack()
	local count_down_key = "chongban_tip_count_down"
	if CountDownManager.Instance:HasCountDown(count_down_key) then
		CountDownManager.Instance:RemoveCountDown(count_down_key)
	end

	if nil ~= self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end

	if nil ~= self.main_menu_icon_change then
		GlobalEventSystem:UnBind(self.main_menu_icon_change)
		self.main_menu_icon_change = nil
	end
end

function ChongBangTipView:OnFlush()
	if not IsEmptyTable(self.data) then
		local cfg = self.data.cfg
		if cfg then
			self.node_list["act_img"].text.text = cfg.act_name
		end
		
		local jiesuan_time = self.data.jiesuan_time
		local count_down_key = "chongban_tip_count_down"
		if CountDownManager.Instance:HasCountDown(count_down_key) then
			CountDownManager.Instance:RemoveCountDown(count_down_key)
		end
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if jiesuan_time > server_time then
			self:ActTipUpdateTime(server_time,jiesuan_time)
			CountDownManager.Instance:AddCountDown(count_down_key,
					BindTool.Bind(self.ActTipUpdateTime,self),BindTool.Bind(self.ActTipTimeComplete,self),jiesuan_time,nil,1)
		else
			self:OnClickClose()
		end
	end
end

function ChongBangTipView:ActTipUpdateTime(now_time, elapse_time)
	local time = math.ceil(elapse_time - now_time)
	self.node_list["time_txt"].text.text = TimeUtil.FormatSecond(time)
end

function ChongBangTipView:ActTipTimeComplete()
	self:OnClickClose()
end

function ChongBangTipView:ClickGoBtn()
	if not IsEmptyTable(self.data) then
		local open_view = self.data and self.data.cfg and self.data.cfg.open_view
		local t = Split(open_view, "#")
		local view_name = t[1]
		if view_name == GuideModuleName.KfActivityView then
			local tab_idx = t[2]
			local tab_index = TabIndex[tab_idx] or 10
			local rush_type = tonumber(t[3])
			local param_t = {rush_type = rush_type}
			ServerActivityWGCtrl.Instance:OpenOpenserverCompetition("kf_act", tab_index, param_t)
		else
			FunOpen.Instance:OpenViewNameByCfg(open_view)
		end
		
		self:OnClickClose()
	end
end

function ChongBangTipView:ClickNotTipToggle(ison)
	local index = self.data and self.data.cfg and self.data.cfg.index or 0
	ChongBangTipWGData.Instance:SetNotTipActIndex(index,ison)
end

function ChongBangTipView:SetData(data)
	self.data = data
	local index = data and data.cfg and data.cfg.index or 0
	ChongBangTipWGData.Instance:SetCurShowTipIndex(index)
end

function ChongBangTipView:OnClickClose()
	local item = ChongBangTipWGData.Instance:GetOpenTipData()
	if not IsEmptyTable(item) then 
		 self:SetData(item)
		 self:Flush()
		 return
	end
	self:Close()
end

--当存在【等级礼包、VIP礼包提示】时，弹出了【冲榜提示】：
--·将等级礼包、VIP礼包提示做【收起动画】，再弹出冲榜提示
--·玩家关闭冲榜提示时，再弹出显示等级、VIP礼包提示

function ChongBangTipView:OpenCallBack()
	local function main_cb()
		local right_parent = MainuiWGCtrl.Instance:GetSkillRightOtherContent()
		right_parent.rect:DOAnchorPosX(300, 0.3)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,main_cb)
end

function ChongBangTipView:CloseCallBack()
	local function main_cb()
		local right_parent = MainuiWGCtrl.Instance:GetSkillRightOtherContent()
		right_parent.rect:DOAnchorPosX(0, 0.3)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,main_cb)
	ChongBangTipWGData.Instance:SetCurShowTipIndex(0)
end

function ChongBangTipView:ShrinkButtonsValueChange(isOn)
    local menu_ison = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
	if isOn or menu_ison then
		self.node_list.root.rect:DOAnchorPosX(300, 0.3)
	else
		self.node_list.root.rect:DOAnchorPosX(-180, 0.3)
	end
end

function ChongBangTipView:MainMenuIconChangeEvent(isOn)
	if isOn then
		self.node_list.root.rect:DOAnchorPosX(300, 0.3)
	else
		self.node_list.root.rect:DOAnchorPosX(-180, 0.3)
	end
end
