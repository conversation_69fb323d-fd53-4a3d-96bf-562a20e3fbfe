﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_SpaceWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(UnityEngine.Space));
		<PERSON><PERSON>("World", get_World, null);
		<PERSON><PERSON>("Self", get_Self, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.<PERSON>();
		TypeTraits<UnityEngine.Space>.Check = CheckType;
		StackTraits<UnityEngine.Space>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.Space arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.Space), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_World(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Space.World);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Self(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Space.Self);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.Space o = (UnityEngine.Space)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

