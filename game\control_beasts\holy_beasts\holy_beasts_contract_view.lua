
function HolyBeastsView:ContractLoadIndexCallBack()
	if not self.beast_model then
		self.beast_model = RoleModel.New()
		self.beast_model:SetUISceneModel(self.node_list["model_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.beast_model, TabIndex.holy_beasts_contract)
	end
	-- if not self.beast_model then
	-- 	self.beast_model = RoleModel.New()
	-- 	local display_data = {
	-- 		parent_node = self.node_list.model_display,
	-- 		camera_type = MODEL_CAMERA_TYPE.BASE,
	-- 		rt_scale_type = ModelRTSCaleType.L,
	-- 		can_drag = true,
	-- 	}
	-- 	self.beast_model:SetRenderTexUI3DModel(display_data)
	-- 	self:AddUiRoleModel(self.beast_model)
	-- end

	if not self.star_list then
		self.star_list = {}
		for i = 1, 5 do
			self.star_list[i] = self.node_list["star" .. i]
		end
	end

	-- 资质属性
	if not self.flair_attr_item_list then
		self.flair_attr_item_list = {}
		for i = 1, 6 do
			local attr_obj = self.node_list["layout_flair_attr"]:FindObj("attr_" .. i)
			if attr_obj then
				local cell = HolyBeastFlairItemRender.New(attr_obj)
				cell:SetIndex(i)
				self.flair_attr_item_list[i] = cell
			end
		end
	end

	if not self.active_consume_item then
		self.active_consume_item = ItemCell.New(self.node_list["consume_item_root"])
	end

	if not self.holy_beast_item then
		self.holy_beast_item = ItemCell.New(self.node_list["holy_beast_cell_root"])
		self.holy_beast_item:SetIsShowTips(false)
	end

	if not self.target_beast_item then
		self.target_beast_item = ItemCell.New(self.node_list["contract_beast_cell_root"])
		self.target_beast_item:SetIsShowTips(false)
	end

	XUI.AddClickEventListener(self.node_list["btn_holy_beast_active"], BindTool.Bind(self.OnClickActiveBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_beast_select"], BindTool.Bind(self.OnClickBeastsSelectBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_holy_beast_skill"], BindTool.Bind(self.OnClickBeastsSkillBtn, self))
end

function HolyBeastsView:ContractReleaseCallBack()
	if self.beast_model then
		self.beast_model:DeleteMe()
		self.beast_model = nil
	end

	if self.flair_attr_item_list then
		for k, v in pairs(self.flair_attr_item_list) do
			v:DeleteMe()
		end
		self.flair_attr_item_list = nil
	end
	
	if self.active_consume_item then
		self.active_consume_item:DeleteMe()
		self.active_consume_item = nil
	end

	if self.holy_beast_item then
		self.holy_beast_item:DeleteMe()
		self.holy_beast_item = nil
	end

	if self.target_beast_item then
		self.target_beast_item:DeleteMe()
		self.target_beast_item = nil
	end

	self.target_beast_data = nil
	self.beast_model_res_id = nil
	self.star_list = nil
end

function HolyBeastsView:ContractCloseCallBack()

end

function HolyBeastsView:ContractShowIndexCallBack()

end

function HolyBeastsView:ContractOnFlush(param_t)
	self:FlushBeastMessage()
	self:FlushBeastModel()
	self:FlushRightPart()
end

function HolyBeastsView:ContractOnSelectHolyBeast(item)
	self:FlushBeastMessage()
	self:FlushBeastModel()
	self:FlushRightPart()
end

-- 刷新中间的模型星级和等级
function HolyBeastsView:FlushBeastMessage()
	if not self.select_holy_beast_data then
		return
	end
	local beast_cfg, server_data
	if self.select_holy_beast_data.is_unlock then
		server_data = self.select_holy_beast_data.beast_data.server_data
		beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
	else
		beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.select_holy_beast_data.cfg.beast_id)
	end

	if beast_cfg then
		local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
		self.node_list["active_jingshen_img"].image:LoadSprite(bundle, asset, function()
			self.node_list["active_jingshen_img"].image:SetNativeSize()
		end)

		if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
			bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[beast_cfg.beast_color])
			self.node_list["active_jingshen_img"]:ChangeAsset(bundle, asset)
		end

		local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
		local no_have_star = "a3_ty_xx_zc0"
		for k,v in pairs(self.star_list) do
			v:CustomSetActive(star_res_list[k] ~= no_have_star)
			v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
		end

		bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
		self.node_list["beasts_element_img"].image:LoadSprite(bundle, asset, function()
			self.node_list["beasts_element_img"].image:SetNativeSize()
		end)
		self.node_list["beasts_name"].tmp.text = beast_cfg.beast_name
		self.node_list["beasts_level"].tmp.text = server_data and server_data.beast_level or 1
	end
end

-- 刷新模型
function HolyBeastsView:FlushBeastModel()
	if not self.select_holy_beast_data then
		return
	end

	local res_id
	if not self.select_holy_beast_data.is_unlock then
		res_id = ControlBeastsWGData.Instance:GetBeastModelResId(self.select_holy_beast_data.cfg.beast_id)
	else
		local server_data = self.select_holy_beast_data.beast_data.server_data
		res_id = ControlBeastsWGData.Instance:GetBeastModelResId(server_data.beast_id, server_data.use_skin)
	end

	if self.beast_model_res_id ~= res_id then
		self.beast_model_res_id = res_id
		local bundle, asset = ResPath.GetBeastsModel(res_id)
		self.beast_model:SetMainAsset(bundle, asset)
		self.beast_model:PlayRoleAction(SceneObjAnimator.Rest)
	end
end

function HolyBeastsView:FlushRightPart()
	if not self.select_holy_beast_data then
		return
	end

	local holy_beast_cfg = self.select_holy_beast_data.cfg
	local beast_cfg, beast_data
	if not self.select_holy_beast_data.is_unlock then
		beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(holy_beast_cfg.beast_id)
	else
		beast_data = self.select_holy_beast_data.beast_data
		beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_data.server_data.beast_id)
	end
	if not beast_cfg then return end

	-- 资质
	local refine_seq = beast_cfg and beast_cfg.refine_seq or -1
	if refine_seq ~= -1 then
		local list_data = ControlBeastsWGData.Instance:GetBeastRefineCfgListBySeq(refine_seq)
		for i, v in ipairs(self.flair_attr_item_list) do
			v:SetData(list_data[i - 1])
		end
	end

	-- 技能
	local client_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(beast_cfg.skill_id)
	local beast_skill_cfg = SkillWGData.Instance:GetBeastsSkillById(beast_cfg.skill_id)
	self.node_list["skill_type"].tmp.text = beast_cfg.skill_des
	self.node_list["skill_name"].tmp.text = beast_skill_cfg and beast_skill_cfg.skill_name or ""
	local bundle, asset = ResPath.GetSkillIconById(client_skill_cfg and client_skill_cfg.icon_resource or 0)
	self.node_list["skill_icon"].image:LoadSprite(bundle, asset)

	-- 状态
	self.node_list["no_active_part"]:SetActive(not self.select_holy_beast_data.is_unlock)
	self.node_list["active_part"]:SetActive(self.select_holy_beast_data.is_unlock)
	if not self.select_holy_beast_data.is_unlock then
		local consume_item_id = holy_beast_cfg.call_cost_item_id
		local had_num = ItemWGData.Instance:GetItemNumInBagById(consume_item_id)
		local need_num = holy_beast_cfg.call_cost_item_num
		self.active_consume_item:SetData({item_id = consume_item_id})
		local color = had_num >= need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        self.active_consume_item:SetRightBottomColorText(had_num .. '/' .. need_num, color)
		self.active_consume_item:SetRightBottomTextVisible(true)
		self.node_list["remind_holy_beast_active"]:SetActive(had_num >= need_num)
	else
		self.holy_beast_item:SetData(beast_data)
		self.node_list["img_jia"]:SetActive(not self.select_holy_beast_data.is_contracted)
		if not self.select_holy_beast_data.is_contracted then
			self.target_beast_item:ClearAllParts()
		else
			local target_beast_data = ControlBeastsWGData.Instance:GetBeastDataById(self.select_holy_beast_data.link_bag_id)
			self.target_beast_item:SetData(target_beast_data)
		end
		local can_contract = ControlBeastsWGData.Instance:GetHolyBeastCanContract(beast_data)
		self.node_list["remind_can_contract"]:SetActive(can_contract)
	end
end

-- 展示技能
function HolyBeastsView:OnClickBeastsSkillBtn()
	if not self.select_holy_beast_data then
		return
	end

	local holy_beast_cfg = self.select_holy_beast_data.cfg
	if self.select_holy_beast_data.is_unlock then
		local server_data = self.select_holy_beast_data.beast_data.server_data
		ControlBeastsWGData.Instance:ShowBeastSkill(server_data.beast_id, false, true)
	else
		ControlBeastsWGData.Instance:ShowBeastSkill(holy_beast_cfg.beast_id)
	end
end

-- 解锁按钮点击
function HolyBeastsView:OnClickActiveBtn()
	if not self.select_holy_beast_data then
		return
	end

	local cfg = self.select_holy_beast_data.cfg
	local had_num = ItemWGData.Instance:GetItemNumInBagById(cfg.call_cost_item_id)
	if had_num < cfg.call_cost_item_num then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.call_cost_item_id})
		return
	end

	ControlBeastsWGCtrl.Instance:SendOperateTypeHolyBeastCall(cfg.beast_type)
end

-- 缔结按钮点击
-- function HolyBeastsView:OnClickContractBtn()
-- 	if not self.select_holy_beast_data or not self.select_holy_beast_data.is_unlock or not self.target_beast_data then
-- 		return
-- 	end
-- 	local beast_data = self.select_holy_beast_data.beast_data
-- 	if beast_data.server_data.holy_spirit_link_index == -1 then
-- 		return
-- 	end
	
-- 	ControlBeastsWGCtrl.Instance:SendOperateTypeHolyBeastLink(beast_data.bag_id - 1, self.target_beast_data.bag_id - 1)
-- end

-- 缔结按钮点击
function HolyBeastsView:OnClickBeastsSelectBtn()
	if not self.select_holy_beast_data then
		return
	end
	ControlBeastsWGCtrl.Instance:OpenHolyBeastContractView(self.select_holy_beast_data)
end

-------------------------------------------
-- 圣兽资质Item
-------------------------------------------
HolyBeastFlairItemRender = HolyBeastFlairItemRender or BaseClass(BaseRender)
function HolyBeastFlairItemRender:OnFlush()
	if not self.data then
		return
	end
	local attr_name = EquipmentWGData.Instance:GetAttrName(self.data.attr_id, true)
	self.node_list.attr_name.tmp.text = attr_name
	self.node_list.attr_value.tmp.text = self.data.max_attr_value
end