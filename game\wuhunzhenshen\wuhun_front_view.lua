
local type_toggle_value = 
{
    hunzhen = 1,  --魂阵
    funeng = 2,   --赋能
}

local auto_strenge_time = 0.2 --自动强化时间间隔

function WuHunView:WuHunFrontInit()
    self.front_list_view = nil
    self.cur_front_wuhun_index = nil   --选中武魂的索引
    self.hunzhen_index = nil       --选中的魂阵的索引
    self.gem_index = 0        --选中的魂阵石的索引
    self.select_front_data = nil
    self.front_show_model = nil
    self.curr_front_appeid = nil
    self.curr_front_hunzhen_appid = nil
end

function WuHunView:WuHunFrontLoadCallBack()
    self.front_toggle_type = type_toggle_value.hunzhen
    self.need_starup_items = {}  --升星所需要的数据
    self.is_front_auto_strenge = false --是否在自动强化
    self.hunzhen_index = nil

    if not self.front_list_view then
        self.front_list_view = AsyncListView.New(TianShenWuHunR<PERSON>, self.node_list.front_list_view)
        self.front_list_view:SetSelectCallBack(BindTool.Bind(self.OnSelectWuHunFrontCallBack, self))
    end

    if not self.hunzhen_list_view then
        self.hunzhen_list_view = AsyncListView.New(HunZhenRender, self.node_list.hunzhen_list_view)
        self.hunzhen_list_view:SetSelectCallBack(BindTool.Bind(self.OnSelectHunZhenRenderCallBack, self))
    end

    if self.front_show_model == nil then
		self.front_show_model = RoleModel.New()
        local front_block = self.node_list.front_block

        local display_data = {
            parent_node = front_block,
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        
        self.front_show_model:SetRenderTexUI3DModel(display_data)
		-- self.front_show_model:SetUI3DModel(front_block.transform, nil, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.front_show_model)
	end

    self.attr_item_list = {}
    for i = 1, 5 do
        local attr_obj = self.node_list["front_active_attr"]:FindObj(string.format("attr_%d", i))
        if attr_obj then
            local cell = CommonAddAttrRender.New(attr_obj)
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            cell:SetRealHideNext(true)
            self.attr_item_list[i] = cell
        end
    end

    self.star_attr_items = {}
    for i = 1, 5 do
        local attr_obj = self.node_list["star_attr_parent"]:FindObj(string.format("attr_%d", i))
        if attr_obj then
            local cell = CommonAddAttrRender.New(attr_obj)
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            cell:SetRealHideNext(true)
            self.star_attr_items[i] = cell
        end
    end

    self.funeng_attr_items = {}
    for i = 1, 5 do
        local attr_obj = self.node_list["funeng_attr_parent"]:FindObj(string.format("attr_%d", i))
        if attr_obj then
            local cell = CommonAddAttrRender.New(attr_obj)
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            cell:SetRealHideNext(true)
            self.funeng_attr_items[i] = cell
        end
    end

    self.front_starup_itemcell = ItemCell.New(self.node_list.front_starup_itemcell)
    self.front_funeng_itemcell = ItemCell.New(self.node_list.node_funeng_itemcell)

    XUI.AddClickEventListener(self.node_list.button_mingke, BindTool.Bind(self.OnMingKeClick, self))
    XUI.AddClickEventListener(self.node_list.btn_funeng_now_strenge, BindTool.Bind(self.OnFrontNowStrengeClick, self))
    XUI.AddClickEventListener(self.node_list.btn_funeng_auto_strenge, BindTool.Bind(self.OnFrontAutoStrengeClick, self))
    XUI.AddClickEventListener(self.node_list.button_tab_hunzhen, BindTool.Bind(self.OnTabHunZhenClick, self))
    XUI.AddClickEventListener(self.node_list.button_tab_funeng, BindTool.Bind(self.OnTabFuNengClick, self))
    XUI.AddClickEventListener(self.node_list.front_btn_starup, BindTool.Bind(self.OnFrontStarUpClick, self))
    XUI.AddClickEventListener(self.node_list.front_huanhua, BindTool.Bind(self.OnFrontHuanHuaClick, self))
    XUI.AddClickEventListener(self.node_list.btn_diezhen_addprop, BindTool.Bind(self.OnFrontDieZhenAddPropClick, self))

    self:StartTweenPanel()
    self:InitFrontGemList()
end

function WuHunView:StartTweenPanel()
    UITween.MoveToShowPanel(GuideModuleName.WuHunView, self.node_list.node_leftlist, Vector2(-148, 0), Vector2(134, 0), 0.5, DG.Tweening.Ease.Linear)
end

function WuHunView:InitFrontGemList()
    local cambered_list_data = {
		item_render = WuHunFrontGemItem,
		asset_bundle = "uis/view/wuhunzhenshen_prefab",
		asset_name = "wuhun_front_gem_render",

		scroll_list = self.node_list.scroll_parent,
		center_x = 0, center_y = 0,
		radius_x = 300, radius_y = 120,-- x 椭圆半长轴,y 椭圆半短轴
		angle_delta = Mathf.PI / 3,
		origin_rotation = Mathf.PI,
		is_drag_horizontal = true,
		scale_min = 0.6,			-- 最小缩放比例
		need_change_scale = true,
		need_updown_click = true,
		up_drag_root = self.node_list.scroll_top_drag,
		down_drag_root = self.node_list.scroll_down_drag,
		arg_adjust = 0.9,			-- 手动拖动时的速度控制
		is_assist = true,

		click_item_cb = BindTool.Bind(self.OnClickFrontGemItem, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragGemToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragGemToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragGemEndCallBack, self),
		set_item_pos_cb = BindTool.Bind(self.SetGemItemPosCallBack, self),
	}

	self.cambered_list = CamberedList.New(cambered_list_data)
end

function WuHunView:WuHunFrontReleseCallBack()
    self.hunzhen_star_list = nil
    self.front_toggle_type = nil
    self.hunzhen_index = nil
    self.cur_front_wuhun_index = nil
    self.gem_index = nil
    self.select_front_data = nil
    self.curr_front_appeid = nil
    self.curr_front_hunzhen_appid = nil
    self.need_starup_items = nil
    self.is_front_auto_strenge = nil

    if self.front_list_view then
        self.front_list_view:DeleteMe()
        self.front_list_view = nil
    end

    if self.front_show_model then
        self.front_show_model:RemoveSoulFormation()
		self.front_show_model:DeleteMe()
        self.front_show_model = nil
	end

    if self.hunzhen_list_view then
        self.hunzhen_list_view:DeleteMe()
        self.hunzhen_list_view = nil
    end

    if self.cambered_list then
        self.cambered_list:DeleteMe()
        self.cambered_list = nil
    end

    if self.attr_item_list and #self.attr_item_list > 0 then
		for _, attr_cell in ipairs(self.attr_item_list) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end
		self.attr_item_list = nil
	end

    if self.star_attr_items and #self.star_attr_items > 0 then
		for _, attr_cell in ipairs(self.star_attr_items) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end
		self.star_attr_items = nil
	end

    if self.funeng_attr_items and #self.funeng_attr_items > 0 then
		for _, attr_cell in ipairs(self.funeng_attr_items) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end
		self.funeng_attr_items = nil
	end

    if self.front_starup_itemcell then
		self.front_starup_itemcell:DeleteMe()
		self.front_starup_itemcell = nil
	end

    if self.front_funeng_itemcell then
        self.front_funeng_itemcell:DeleteMe()
		self.front_funeng_itemcell = nil
    end
end

function WuHunView:WuHunFrontShowIndexCallBack()
end

function WuHunView:FlushWuHunFrontJump(param_t)
    local auto_gem_index = false
    for k_1, v_1 in pairs(param_t or {}) do
        if k_1 == "jump" then
            local _, list_index = WuHunFrontWGData.Instance:GetFrontWuHunListData(COMMON_GAME_ENUM.ONE, v_1.wuhun_id)
            self.cur_front_wuhun_index = list_index
            self.hunzhen_index = v_1.front_index
            self.gem_index = v_1.gem_index
            auto_gem_index = true
        end
    end

    self:FrontFlushListView(nil, auto_gem_index)
end

function WuHunView:FrontFlushListView(is_init_hunzhen, no_slt_gem_index)
    local wuhun_list = WuHunFrontWGData.Instance:GetFrontWuHunListData(COMMON_GAME_ENUM.ONE)
    self.front_list_view:SetDataList(wuhun_list)

    if self.cur_front_wuhun_index == nil then
        for index, data in ipairs(wuhun_list) do
            if not data.lock and self.cur_front_wuhun_index == nil then
                self.cur_front_wuhun_index = index
            end

            if data.show_red then
                self.cur_front_wuhun_index = index
                break
            end
        end

        if self.cur_front_wuhun_index == nil then
            --未取到带红点的默认第一个
            self.cur_front_wuhun_index = 1
        end
    end
    self.select_front_data = wuhun_list[self.cur_front_wuhun_index]
    self.front_list_view:SetDefaultSelectIndex(self.cur_front_wuhun_index)

    self:SetFrontListShowInfo(is_init_hunzhen, no_slt_gem_index)
end

--设置魂阵列表的显示
function WuHunView:SetFrontListShowInfo(is_init_hunzhen, no_slt_gem_index)
    local wuhun_id = self.select_front_data.wuhun_id
    local front_list = WuHunFrontWGData.Instance:GetFrontListByWuHunId(wuhun_id, COMMON_GAME_ENUM.ONE)
    self.hunzhen_list_view:SetDataList(front_list)

    --自动强化状态下不用初始化选择魂阵 选中魂阵已自身红点优先
    if self.hunzhen_index == nil or is_init_hunzhen then
        self.hunzhen_index = self.hunzhen_index or 0
        local init_index = COMMON_GAME_ENUM.FUYI
        for k_i, k_v in ipairs(front_list) do
            if k_v.show_red and init_index == COMMON_GAME_ENUM.FUYI then
                init_index = k_v.front_index
            end

            if self.hunzhen_index == k_v.front_index and k_v.show_red then --自身有红点不改变当前的索引
                init_index = COMMON_GAME_ENUM.FUYI
                break
            end
        end

        if init_index ~= COMMON_GAME_ENUM.FUYI then
            self.hunzhen_index = init_index
        end
    end
    self.hunzhen_list_view:SetDefaultSelectIndex(self.hunzhen_index + 1) --列表索引是从1开始

    self:SetFrontChangeShow()

    --自动强化状态下不用初始化选择魂石
    if not self.is_front_auto_strenge then
        self:SetSltResetGemIndex(no_slt_gem_index)
    end
end

--设置魂石的信息显示
function WuHunView:SetFrontGemShow()
    local wuhun_id = self.select_front_data.wuhun_id
    local btn_item_list = self.cambered_list:GetRenderList()
    local front_hunshi_list = WuHunFrontWGData.Instance:GetFrontGemList(wuhun_id, self.hunzhen_index, COMMON_GAME_ENUM.ONE)
    local all_active = true
	for k, item_cell in ipairs(btn_item_list) do
        local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, front_hunshi_list[k].gem_index)
        front_hunshi_list[k].select_gem_index = self.gem_index
        front_hunshi_list[k].show_star = true
        front_hunshi_list[k].gem_level = gem_data.gem_level
        front_hunshi_list[k].gem_star = gem_data.gem_star
		item_cell:Flush("set_data", front_hunshi_list[k])

        if not gem_data.gem_star or gem_data.gem_star == COMMON_GAME_ENUM.FUYI then
            all_active = false
        end
	end

    self:SetHunzhenFunengViewShow()
    self.node_list.front_huanhua:CustomSetActive(all_active)
    self:SetLevelOperatorShow()
end

function WuHunView:SetFrontChangeShow()
    local wuhun_id = self.select_front_data.wuhun_id
    local front_hunshi_list = WuHunFrontWGData.Instance:GetFrontGemList(wuhun_id, self.hunzhen_index, COMMON_GAME_ENUM.ONE)
    self.cambered_list.angle_delta = Mathf.PI * 2 / #front_hunshi_list
	self.cambered_list:CreateCellList(#front_hunshi_list)

    --设置战力的显示
    local fight_value = WuHunFrontWGData.Instance:GetWuHunFrontFight(wuhun_id, self.hunzhen_index)
    self.node_list.front_fight_value.text.text = fight_value

    local mingke_red = WuHunFrontWGData.Instance:GetWuHunFrontMingKeRed(wuhun_id, self.hunzhen_index)
    self.node_list.image_mingke_red:CustomSetActive(mingke_red)

    self:SetFrontModelShowInfo()
end

--设置升星和升级的界面
function WuHunView:SetHunzhenFunengViewShow()
    local wuhun_id = self.select_front_data.wuhun_id
    self.node_list.front_property_root:CustomSetActive(self.front_toggle_type == type_toggle_value.hunzhen)
    self.node_list.front_funeng_root:CustomSetActive(self.front_toggle_type == type_toggle_value.funeng)
    self.node_list.front_tab_hunzhen_Image_hl:CustomSetActive(self.front_toggle_type == type_toggle_value.hunzhen)
    self.node_list.front_tab_hunzhen_Image_nor:CustomSetActive(self.front_toggle_type == type_toggle_value.funeng)
    self.node_list.front_tab_funeng_Image_nor:CustomSetActive(self.front_toggle_type == type_toggle_value.hunzhen)
    self.node_list.front_tab_funeng_Image_hl:CustomSetActive(self.front_toggle_type == type_toggle_value.funeng)

    local starup_red = WuHunFrontWGData.Instance:GetFrontGemStarUpRed(wuhun_id, self.hunzhen_index, self.gem_index)
    local levelup_red = WuHunFrontWGData.Instance:GetFrontGemLevelUpRed(wuhun_id, self.hunzhen_index, self.gem_index)
    self.node_list.front_starup_toggle_red:CustomSetActive(starup_red)
    self.node_list.front_levelup_toggle_red:CustomSetActive(levelup_red)

    if self.front_toggle_type == type_toggle_value.hunzhen then
        self:SetHunZhenToggleView()
    else
        self:SetFuNengToggleView()
    end
end

--设置模型的显示
function WuHunView:SetFrontModelShowInfo()
    local wuhun_id = self.select_front_data.wuhun_id
    local temp_wuhun_appeid = self.curr_front_appeid
    local active = WuHunWGData.Instance:GetWuHunActiveCfg(wuhun_id)
    self.curr_front_appeid = active and active.appe_image_id or 10114

	if temp_wuhun_appeid ~= self.curr_front_appeid and self.front_show_model then
        local bundle, asset = ResPath.GetWuHunModel(self.curr_front_appeid)
        self.front_show_model:SetMainAsset(bundle, asset, function ()
            if self:IsOpen() then
                self.front_show_model:PlayRoleAction(SceneObjAnimator.Rest)
            end
        end)
	end

    local cfg = WuHunFrontWGData.Instance:GetSoulFormationAppimageCfg(wuhun_id, self.hunzhen_index)
    if cfg then
        if self.curr_front_hunzhen_appid ~= cfg.app_image_id then
            self.curr_front_hunzhen_appid = cfg.app_image_id
            self.front_show_model:SetSoulFormationResid(cfg.app_image_id, wuhun_id)
        end

        local split_cfg = WuHunFrontWGData.Instance:GetHunZhenXingXiangSpilt(wuhun_id, self.hunzhen_index)
        if split_cfg then
            RectTransform.SetAnchoredPositionXY(self.node_list.display_mask.rect, split_cfg.pos_t[1], split_cfg.pos_t[2])
            self.node_list.display_mask.transform.localScale = Vector3(split_cfg.display_scale, split_cfg.display_scale, split_cfg.display_scale)
            self.node_list.display_mask.transform.rotation = Quaternion.Euler(split_cfg.roa_t[1], split_cfg.roa_t[2], split_cfg.roa_t[3])
        end
    else
        self.curr_front_hunzhen_appid = nil
        self.front_show_model:RemoveSoulFormation()
    end

    local huanhua_index = WuHunFrontWGData.Instance:GetWuHunFrontHuanHuaIndex(wuhun_id)
    local txt1 = huanhua_index == self.hunzhen_index and Language.WuHunZhenShen.WuhunFrontTxt2 or Language.WuHunZhenShen.WuhunFrontTxt1
    self.node_list.text_front_huanhua.text.text = txt1
end

function WuHunView:OnTabHunZhenClick()
    if self.front_toggle_type == type_toggle_value.hunzhen then
        return
    end

    self.front_toggle_type = type_toggle_value.hunzhen
    self:SetHunzhenFunengViewShow()
end

function WuHunView:OnTabFuNengClick()
    if self.front_toggle_type == type_toggle_value.funeng then
        return
    end

    self.front_toggle_type = type_toggle_value.funeng
    self:SetHunzhenFunengViewShow()
end

--重置gem_index重置选中的魂石索引 刷新界面 no_slt_gem_index为true说明跳转打开了魂石 不用自动选
function WuHunView:SetSltResetGemIndex(no_slt_gem_index)
    if not no_slt_gem_index then
        self.gem_index = self.gem_index or 0
        local front_hunshi_list = WuHunFrontWGData.Instance:GetFrontGemList(self.select_front_data.wuhun_id, self.hunzhen_index, COMMON_GAME_ENUM.ONE)
        local init_index = COMMON_GAME_ENUM.FUYI
        for k_i, v_1 in ipairs(front_hunshi_list) do
            if v_1.show_red and init_index == COMMON_GAME_ENUM.FUYI then
                init_index = v_1.gem_index
            end

            if self.gem_index == v_1.gem_index and v_1.show_red then --自身有红点不改变当前的索引
                init_index = COMMON_GAME_ENUM.FUYI
                break
            end
        end

        if init_index ~= COMMON_GAME_ENUM.FUYI then
            self.gem_index = init_index
        end
    end

    self:OnDragGemEndCallBack()
end

--点击选择武魂的列表项
function WuHunView:OnSelectWuHunFrontCallBack(item, cell_index, is_default, is_click)
    if nil == item or nil == item.data then
		return
	end

    if self.cur_front_wuhun_index == item.index then
        return
    end

	self.cur_front_wuhun_index = item.index
    self.select_front_data = item.data
    self.is_front_auto_strenge = false
	self:FrontFlushListView(true)
end

--点击魂阵的回调
function WuHunView:OnSelectHunZhenRenderCallBack(item, cell_index, is_default, is_click)
    self.hunzhen_index = cell_index - 1
    if is_click then
        self:SetSltResetGemIndex()
        self.is_front_auto_strenge = false
        self:SetFrontChangeShow()
    end
end

--点击列表项
function WuHunView:OnClickFrontGemItem(item, index)
    if item then
		self.gem_index = item:GetIndex() - 1
	elseif index then
		self.gem_index = index - 1
	end

	self:OnSelectedBtnChange(function ()
        if self:IsOpen() then
		    self:SetFrontGemShow()
        end
	end, true)
end

--拖拽到下一个
function WuHunView:OnDragGemToNextCallBack()
    local front_list = WuHunFrontWGData.Instance:GetFrontGemList(self.select_front_data.wuhun_id, self.hunzhen_index)
    self.gem_index = self.gem_index + 1
	self.gem_index = self.gem_index + 1 > #front_list and 0 or self.gem_index
end

--拖拽到上一个
function WuHunView:OnDragGemToLastCallBack()
    local front_list = WuHunFrontWGData.Instance:GetFrontGemList(self.select_front_data.wuhun_id, self.hunzhen_index)
    self.gem_index = self.gem_index - 1
	self.gem_index = self.gem_index < 0 and #front_list - 1 or self.gem_index
end

-- 拖拽完成回调
function WuHunView:OnDragGemEndCallBack()
    local call_back = function ()
        if self:IsOpen() then
            self:SetFrontGemShow()
        end
    end
	self:OnSelectedBtnChange(call_back, nil, self.gem_index + 1)
end

-- 设置为之后的回调
function WuHunView:SetGemItemPosCallBack()
	local assist_render_list = self.cambered_list:GetAssistRenderList()
    local maxCount = #assist_render_list
    local pastIndex = math.ceil(maxCount / 2)

    for i, list_render in pairs(assist_render_list) do
        local parent = self.node_list.behind_gem_parent

        if maxCount - i < pastIndex then
            parent = self.node_list.front_gem_parent
        end

        list_render.view.gameObject.transform:SetParent(parent.gameObject.transform)
    end
end

-- 选中某一个
function WuHunView:OnSelectedBtnChange(callback, is_click, drag_index)
	local to_index = drag_index ~= nil and drag_index or self.gem_index + 1 or 1
	self.cambered_list:ScrollToIndex(to_index, callback, is_click)
end

function WuHunView:OnMingKeClick()
    local wuhun_id = self.select_front_data.wuhun_id
    if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip1)
        return
    end

    local param = {}
    param.cur_front_wuhun_index = self.cur_front_wuhun_index
	param.cur_hunzhen_index = self.hunzhen_index
	param.cur_front_gem_index = self.gem_index
    WuHunWGCtrl.Instance:OpenFrontGemShow(param)
end


-----------------------------------------------------魂阵升星   开始-----------------------------------

--设置魂阵页签的显示
function WuHunView:SetHunZhenToggleView()
    local wuhun_id = self.select_front_data.wuhun_id 
    local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, self.gem_index)
    local gem_all_cfg = WuHunFrontWGData.Instance:GetFrontGemStarCfg(wuhun_id, self.hunzhen_index, self.gem_index)
    local gem_star = (not gem_data.gem_star or gem_data.gem_star == COMMON_GAME_ENUM.FUYI) and 0 or gem_data.gem_star

    local is_max_star = gem_all_cfg and gem_all_cfg[#gem_all_cfg].star == gem_star
    local star_cfg = WuHunFrontWGData.Instance:GetFrontGemStarCfg(wuhun_id, self.hunzhen_index, self.gem_index, gem_star) or {}
    local next_cfg = WuHunFrontWGData.Instance:GetFrontGemStarCfg(wuhun_id, self.hunzhen_index, self.gem_index, gem_star + 1)
    local star_attr_list = WuHunFrontWGData.Instance:GetAttrFunc(star_cfg, next_cfg)

    local active_cfg = nil
    if not gem_data.gem_star or gem_data.gem_star == COMMON_GAME_ENUM.FUYI then
        active_cfg = WuHunFrontWGData.Instance:GetFrontGemTab(wuhun_id, self.hunzhen_index, self.gem_index)
    end

    local txt1 = nil
    local attr_list, state, num_tab = WuHunFrontWGData.Instance:GetFrontGemActiveStarProperty(wuhun_id, self.hunzhen_index)
    if state then
        txt1 = string.format(Language.WuHunZhenShen.WuhunFrontActiveTxt1, num_tab[2], num_tab[1], num_tab[2])
    else
        if num_tab[1] == num_tab[2] then
            txt1 = Language.WuHunZhenShen.WuhunFrontActiveTxt3
        else
            txt1 = string.format(Language.WuHunZhenShen.WuhunFrontActiveTxt2, num_tab[1] + 1, num_tab[3], num_tab[4])
        end
    end
    self.node_list.text_active_gem_attr.text.text = txt1

    for i, attr_cell in ipairs(self.attr_item_list) do
        if attr_cell then
            if attr_list[i] and attr_cell then
                if attr_cell.view then
                    attr_cell.view:SetActive(true)
                end

                attr_cell:SetData(attr_list[i])
            else
                if attr_cell.view then
                    attr_cell.view:SetActive(false)
                end
            end
        end
    end

    for i, attr_cell in ipairs(self.star_attr_items) do
        if attr_cell then
            if star_attr_list[i] and attr_cell then
                if attr_cell.view then
                    attr_cell.view:SetActive(true)
                end

                attr_cell:SetData(star_attr_list[i])
            else
                if attr_cell.view then
                    attr_cell.view:SetActive(false)
                end
            end
        end
    end

    self.node_list.node_front_upgrade_state:CustomSetActive(not is_max_star)
    self.node_list.node_front_max_starup:CustomSetActive(is_max_star)
    self.node_list.node_front_stars_list:CustomSetActive(not active_cfg and not is_max_star)
    self.node_list.text_front_btn_starup.text.text = active_cfg and Language.WuHunZhenShen.WuhunFrontStarTip1 or Language.WuHunZhenShen.WuhunFrontStarTip2
    local starup_red = WuHunFrontWGData.Instance:GetFrontGemStarUpRed(wuhun_id, self.hunzhen_index, self.gem_index)
    self.node_list.btn_front_starup_red:CustomSetActive(starup_red)

    local bom_txt = 0 .. "/" .. 0
    local color_v = ITEM_NUM_COLOR.NOT_ENOUGH
    self.need_starup_items.item_id = star_cfg and star_cfg.item_id or 0
    self.need_starup_items.item_num = star_cfg and star_cfg.item_num or 0
    local item_id = self.need_starup_items.item_id
    local item_num = self.need_starup_items.item_num

    --存在下一星级
    if next_cfg then
        local star_res_list = GetStarImgResByStar(gem_star)
        for i = 1, 5 do
            local image_star = self.node_list["hunzhen_star_" .. i]
            image_star.image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
        end

        local num = WuHunFrontWGData.Instance:GetItemNumInBagById(item_id)
        local is_have = num >= item_num
        color_v = is_have and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
        bom_txt = num .. "/" .. item_num
    end

    self.front_starup_itemcell:SetData({item_id = item_id})
    self.front_starup_itemcell:SetRightBottomColorText(bom_txt, color_v)
    self.front_starup_itemcell:SetRightBottomTextVisible(true)
end

function WuHunView:OnFrontStarUpClick()
    if not WuHunWGData.Instance:GetWuHunIsActive(self.select_front_data.wuhun_id) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip1)
        return
    end

    local item_num = self.need_starup_items.item_num
    local num = WuHunFrontWGData.Instance:GetItemNumInBagById(self.need_starup_items.item_id)
    local wuhun_id = self.select_front_data.wuhun_id 
    local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, self.gem_index)

    local op_type = gem_data.gem_star == COMMON_GAME_ENUM.FUYI and WUHUN_FRONT_OPERATE_TYPE.SOUL_STONE_ACTIVE 
        or WUHUN_FRONT_OPERATE_TYPE.SOUL_STONE_STAR
    if num >= item_num then
        WuHunWGCtrl.Instance:SendWuHunFrontOperate(
            op_type, 
            wuhun_id, 
            self.hunzhen_index, 
            self.gem_index, 
            0,
            {}
        )
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTxt4)
    end
end

-----------------------------------------------------魂阵升星   结束-----------------------------------


-----------------------------------------------------魂阵升级   开始-----------------------------------

--设置赋能页签的显示
function WuHunView:SetFuNengToggleView()
    local wuhun_id = self.select_front_data.wuhun_id 
    local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, self.gem_index)
    local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(wuhun_id, self.hunzhen_index)
    local gem_level = gem_data.gem_level or 0
    local gem_level_index = front_data.gem_level_index or COMMON_GAME_ENUM.FUYI

    self.node_list.text_funeng_prop_level.text.text = "LV." .. gem_level
    local engrave_list = WuHunFrontWGData.Instance:GetGemAllEngraveData(wuhun_id, self.hunzhen_index, self.gem_index)
    local min_level = -1 --默认的最大等级 只要很大就行  取出最小的来
    for k_1, v_1 in pairs(engrave_list) do
        if v_1 ~= 0 then
            local attr_cfg = WuHunFrontWGData.Instance:GetEngraveCfgByItemId(v_1)
            if attr_cfg and (min_level == -1 or min_level > attr_cfg.level) then
                min_level = attr_cfg.level
            end
        end
    end

    min_level = min_level == -1 and 0 or min_level
    local engrave_level_attr_cfg = WuHunFrontWGData.Instance:GetEngraveLevelAttrCfg(min_level) --刻印等级属性表
    local next_engrave_level_attr_cfg = WuHunFrontWGData.Instance:GetEngraveLevelAttrCfg(min_level + 1)
    local max_engrave_level = next_engrave_level_attr_cfg == nil
    local level_cfg = WuHunFrontWGData.Instance:GetFrontGemLevelCfgByLevel(gem_level)
    local next_gem_level_attr_cfg = WuHunFrontWGData.Instance:GetFrontGemLevelCfgByLevel(gem_level + 1)
    local add_prop = engrave_level_attr_cfg and engrave_level_attr_cfg.add or 0
    local attr_list = WuHunFrontWGData.Instance:GetAttrFunc(level_cfg, next_gem_level_attr_cfg, add_prop)
    local level_sum = WuHunFrontWGData.Instance:GetFrontGemLevelSum(wuhun_id, self.hunzhen_index)
    local is_max_level = next_gem_level_attr_cfg == nil
    self.node_list.text_funeng_property_title.text.text = string.format(Language.WuHunZhenShen.WuhunFrontFuNengTxt2, min_level)
    self.node_list.text_funeng_property_value1.text.text = string.format(Language.WuHunZhenShen.WuhunFrontFuNengTxt3, min_level + 1)
    self.node_list.text_funeng_propvalue.text.text = (engrave_level_attr_cfg and engrave_level_attr_cfg.add or 0) .. "%"
    self.node_list.text_funeng_next_propvalue.text.text = (next_engrave_level_attr_cfg and next_engrave_level_attr_cfg.add or 0) .. "%"

    local level_attr_cfg = WuHunFrontWGData.Instance:GetFrontGemLevelAttr(wuhun_id, self.hunzhen_index, gem_level_index + 1)
    local need_level = level_attr_cfg and level_attr_cfg.level_total or 0

    local pro_txt = string.format(Language.WuHunZhenShen.WuhunFrontFuNengTxt7, level_sum)
    if level_attr_cfg then
        local cor_str = level_sum >= need_level and "9DF5A7" or "ff9797"
        pro_txt = string.format(Language.WuHunZhenShen.WuhunFrontFuNengTxt1, level_sum, cor_str, level_sum, need_level)
    end
    self.node_list.funeng_totallevel_desc.text.text = pro_txt
    self.node_list.slider_funeng_level.slider.value = need_level == 0 and 0 or level_sum / need_level

    for i, attr_cell in ipairs(self.funeng_attr_items) do
        if attr_cell then
            if attr_list[i] and attr_cell then
                if attr_cell.view then
                    attr_cell.view:SetActive(true)
                end

                attr_cell:SetData(attr_list[i])
            else
                if attr_cell.view then
                    attr_cell.view:SetActive(false)
                end
            end
        end
    end

    self.node_list.text_funeng_next_propvalue:CustomSetActive(not max_engrave_level)
    self.node_list.text_funeng_property_value1:CustomSetActive(not max_engrave_level)
    self.node_list.node_funeng_levelstate:CustomSetActive(not is_max_level)
    self.node_list.funeng_strenge_max_state:CustomSetActive(is_max_level)

    local level_red = WuHunFrontWGData.Instance:GetFrontGemLevelUpCostRed(wuhun_id, self.hunzhen_index, self.gem_index)
    self.node_list.image_btn_front_levelup_red:CustomSetActive(level_red)
    self.node_list.image_btn_front_auto_levelup_red:CustomSetActive(level_red)
    local addprop_red = WuHunFrontWGData.Instance:GetFrontGemLevelUpAddPropRed(wuhun_id, self.hunzhen_index, self.gem_index)
    self.node_list.image_level_addprop_red:CustomSetActive(addprop_red)

    local role_coin = RoleWGData.Instance.role_info.coin or 0
	local color_str = ""
    local bom_txt = 0 .. "/" .. 0
    local color_v = ITEM_NUM_COLOR.NOT_ENOUGH
	if not is_max_level then
		local coin_str_color = level_cfg.coin_num <= role_coin and COLOR3B.GREEN or COLOR3B.D_PINK
		local cur_coin_str = ToColorStr(CommonDataManager.ConverExpByThousand(role_coin), coin_str_color)
		color_str = cur_coin_str .. "/" .. CommonDataManager.ConverExpByThousand(level_cfg.coin_num)

        local num = WuHunFrontWGData.Instance:GetItemNumInBagById(level_cfg.item_id)
        local is_have = num >= level_cfg.item_num
        color_v = is_have and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
        bom_txt = num .. "/" .. level_cfg.item_num
    end
    self.front_funeng_itemcell:SetData({item_id = level_cfg.item_id})
    self.front_funeng_itemcell:SetRightBottomColorText(bom_txt, color_v)
    self.front_funeng_itemcell:SetRightBottomTextVisible(true)
	self.node_list.text_funeng_coin_desc.text.text = color_str
end

--立即强化
function WuHunView:OnFrontNowStrengeClick()
    if not WuHunWGData.Instance:GetWuHunIsActive(self.select_front_data.wuhun_id) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip1)
        return
    end

    if self.is_front_auto_strenge then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTxt8)
        return
    end

	if self:GetFrontGemIsCanStrenge(self.gem_index) then
        WuHunWGCtrl.Instance:SendWuHunFrontOperate(
			WUHUN_FRONT_OPERATE_TYPE.SOUL_STONE_LEVEL, 
			self.select_front_data.wuhun_id, 
			self.hunzhen_index, 
			self.gem_index, 
			0,
            {}
		)
	end
end

--得到最小的魂阵索引
function WuHunView:GetFrontGemMinLevel()
    local front_data = WuHunFrontWGData.Instance:GetFrontDataByFrontIndex(self.select_front_data.wuhun_id, self.hunzhen_index)
    local min_gem_index = self.gem_index --默认值取自身
    local min_gem_level = COMMON_GAME_ENUM.MAX --随机无比大数

    for k_1, v_1 in pairs(front_data.gem_list) do
        local level_cfg = WuHunFrontWGData.Instance:GetFrontGemLevelCfgByLevel(v_1.gem_level)
        local role_coin = RoleWGData.Instance.role_info.coin or 0
        local item_num = level_cfg.item_num
        local coin_num = level_cfg.coin_num
        local num = WuHunFrontWGData.Instance:GetItemNumInBagById(level_cfg.item_id)

        if v_1.gem_star > COMMON_GAME_ENUM.FUYI and v_1.gem_level < min_gem_level and num >= item_num and role_coin >= coin_num then
            min_gem_level = v_1.gem_level
            min_gem_index = v_1.front_gem_index
        end
    end

    return min_gem_index
end

--自动强化
function WuHunView:OnFrontAutoStrengeClick()
    if not WuHunWGData.Instance:GetWuHunIsActive(self.select_front_data.wuhun_id) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip1)
        return
    end

    self.is_front_auto_strenge = not self.is_front_auto_strenge
    self:SetLevelOperatorShow()

    self:ReqFrontGemStrenge()
end

--记录下一次的请求
function WuHunView:FrontStrengeTimerFunc()
    local min_index = self:GetFrontGemMinLevel()
    if self:GetFrontGemIsCanStrenge(min_index) then
        self:CancelAutoStrengthLevelTimer()
        self.is_front_auto_strenge = true
        self.funeng_timer_quest = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.ReqFrontGemStrenge, self, 1, true), auto_strenge_time)
    else
        self.is_front_auto_strenge = false
		self:SetFrontGemShow()
		self:SetLevelOperatorShow()
    end
end

--返回是否自动强化
function WuHunView:GetIsAutoStrenge()
    return self.is_front_auto_strenge
end

--检查是否可以强化
function WuHunView:GetFrontGemIsCanStrenge(gem_index)
    local min_index = gem_index or self:GetFrontGemMinLevel()
    local wuhun_id = self.select_front_data.wuhun_id
    local gem_data = WuHunFrontWGData.Instance:GetWuHunFrontGemData(wuhun_id, self.hunzhen_index, min_index)
    local gem_level = gem_data.gem_level or 0

    local level_cfg = WuHunFrontWGData.Instance:GetFrontGemLevelCfgByLevel(gem_level)
    local role_coin = RoleWGData.Instance.role_info.coin or 0
    local item_num = level_cfg.item_num
    local coin_num = level_cfg.coin_num
    local num = WuHunFrontWGData.Instance:GetItemNumInBagById(level_cfg.item_id)

    --星级大于-1  已激活
    if not gem_data.gem_star or gem_data.gem_star <= COMMON_GAME_ENUM.FUYI then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip2)
        return false
    end

    --材料不足
    if num < item_num or role_coin < coin_num then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTxt4)
        return false
    end

    return true
end

--自动强化的请求
function WuHunView:ReqFrontGemStrenge()
    if not self:IsOpen() then
        return
    end

    if self:GetShowIndex() ~= TabIndex.wuhun_front or not self.is_front_auto_strenge then
		self:SetLevelOperatorShow()
		return
    end

    local min_index = self:GetFrontGemMinLevel()
    if self:GetFrontGemIsCanStrenge(min_index) then
        self.gem_index = min_index

        self:OnSelectedBtnChange(function ()
            self:SetFrontGemShow()

            WuHunWGCtrl.Instance:SendWuHunFrontOperate(
                WUHUN_FRONT_OPERATE_TYPE.SOUL_STONE_LEVEL, 
                self.select_front_data.wuhun_id, 
                self.hunzhen_index, 
                self.gem_index, 
                0,
                {}
            )
        end,
        true)
    else
        self.is_front_auto_strenge = false
        self:SetFrontGemShow()
        self:SetLevelOperatorShow()
    end
end

--取消自动强化倒计时
function WuHunView:CancelAutoStrengthLevelTimer()
	if nil ~= self.funeng_timer_quest then
		GlobalTimerQuest:CancelQuest(self.funeng_timer_quest)
		self.funeng_timer_quest = nil
	end
end

function  WuHunView:SetLevelOperatorShow()
    local btn_txt = self.is_front_auto_strenge and Language.WuHunZhenShen.WuhunFrontFuNengTxt6 or Language.WuHunZhenShen.WuhunFrontFuNengTxt5
    self.node_list.text_funeng_auto_strenge.text.text = btn_txt

	if not self.is_front_auto_strenge then
		self:CancelAutoStrengthLevelTimer()
	end
end

-----------------------------------------------------魂阵升级   结束-----------------------------------

function WuHunView:OnFrontHuanHuaClick()
    local wuhun_id = self.select_front_data.wuhun_id
    if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip1)
        return
    end

    local front_hunshi_list = WuHunFrontWGData.Instance:GetFrontGemList(wuhun_id, self.hunzhen_index)
    for k_1, v_1 in pairs(front_hunshi_list) do
        if v_1.gem_star == COMMON_GAME_ENUM.FUYI then
            TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontTxt3)
            return
        end
    end

    local op_type = nil
    local hunzhen_index = 0
    local huanhua_index = WuHunFrontWGData.Instance:GetWuHunFrontHuanHuaIndex(wuhun_id)
    if huanhua_index == self.hunzhen_index then
        op_type = WUHUN_FRONT_OPERATE_TYPE.SOUL_FRONT_USE_CANCEL
    else
        op_type = WUHUN_FRONT_OPERATE_TYPE.SOUL_FRONT_USE
        hunzhen_index = self.hunzhen_index
    end

    WuHunWGCtrl.Instance:SendWuHunFrontOperate(
        op_type,
        wuhun_id,
        hunzhen_index,
        0,
        0,
        {}
    )
end

--点击叠阵
function WuHunView:OnFrontDieZhenAddPropClick()
    local wuhun_id = self.select_front_data.wuhun_id
    if not WuHunWGData.Instance:GetWuHunIsActive(wuhun_id) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.WuHunZhenShen.WuhunFrontFuNengTip1)
        return
    end

    local param = {}
    param.prop_showtype = COMMON_GAME_ENUM.ONE
    param.wuhun_id = wuhun_id
    param.front_index = self.hunzhen_index
    param.gem_index = self.gem_index
    WuHunWGCtrl.Instance:SetFrontPropertyTipShow(param)
end