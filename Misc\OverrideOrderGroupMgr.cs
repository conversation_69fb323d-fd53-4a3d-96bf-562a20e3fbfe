﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Nirvana;
using System;

public sealed class OverrideOrderGroupMgr : Nirvana.Singleton<OverrideOrderGroupMgr>
{
    private Dictionary<Canvas, OverrideOrderGroupItem> overrideOrderDic = new Dictionary<Canvas, OverrideOrderGroupItem>();
    private int groupCanvasOrderInterval = 60;
    private Dictionary<String_Int_Key, int> uiEffectDic = new Dictionary<String_Int_Key, int>(String_Int_Comparer.Default);
    private LinkedListNode<Action> updateHandler;

    public OverrideOrderGroupMgr()
    {

    }

    public void OnGameStartup()
    {
        updateHandler = Scheduler.AddFrameListener(this.Update);
    }

    public void OnGameStop()
    {
        overrideOrderDic.Clear();
        uiEffectDic.Clear();
        if (null != updateHandler)
        {
            Scheduler.RemoveFrameListener(updateHandler);
            updateHandler = null;
        }
    }

    public void SetGroupCanvasOrderInterval(int groupCanvasOrderInterval)
    {
        if (groupCanvasOrderInterval <= 0) return;

        this.groupCanvasOrderInterval = groupCanvasOrderInterval;
    }

    public int GroupCanvasOrderInterval
    {
        get
        {
            return this.groupCanvasOrderInterval;
        }
    }

    public Canvas AddToGroup(IOverrideOrder overrideOrder)
    {
        var canvasScalers = ListPool<CanvasScaler>.Get();
        overrideOrder.GetTarget().GetComponentsInParent(true, canvasScalers);
        if (canvasScalers.Count <= 0)
        {
            ListPool<CanvasScaler>.Release(canvasScalers);
            return null;
        }

        var canvasScaler = canvasScalers[0];
        Canvas groupCanvas = canvasScaler.GetComponent<Canvas>();
        if (null == groupCanvas)
        {
            ListPool<CanvasScaler>.Release(canvasScalers);
            return null;
        }

        OverrideOrderGroupItem group;
        if (!overrideOrderDic.ContainsKey(groupCanvas))
        {
            group = new OverrideOrderGroupItem();
            group.groupCanvas = groupCanvas;
            group.groupOverrideOrder = groupCanvas.sortingOrder;
            overrideOrderDic.Add(groupCanvas, group);
        }
        else
        {
            group = overrideOrderDic[groupCanvas];
        }

        group.orderSet.Add(overrideOrder);
        group.isDirtry = true;
        ListPool<CanvasScaler>.Release(canvasScalers);

        return groupCanvas;
    }

    public void RemoveFromGroup(Canvas groupCanvas, IOverrideOrder overrideOrder)
    {
        if (null == groupCanvas || null == overrideOrder)
        {
            return;
        }

        OverrideOrderGroupItem group;
        if (overrideOrderDic.TryGetValue(groupCanvas, out group))
        {
            group.orderSet.Remove(overrideOrder);
            if (0 == group.orderSet.Count)
            {
                overrideOrderDic.Remove(groupCanvas);
            }

            group.isDirtry = true;
        }
    }

    public void SetGroupCanvasDirty(Canvas groupCanvas)
    {
        if (null == groupCanvas)
        {
            return;
        }

        OverrideOrderGroupItem groupItem;
        if (overrideOrderDic.TryGetValue(groupCanvas, out groupItem))
        {
            groupItem.isDirtry = true;
        }
    }

    public void Update()
    {
        this.ObserveGroupCanvasOrderChange();
        this.RefreshAllOrder();
    }

    private void ObserveGroupCanvasOrderChange()
    {
        var iter = this.overrideOrderDic.GetEnumerator();
        while (iter.MoveNext())
        {
            var kv = iter.Current;
            if (null != kv.Value.groupCanvas && kv.Value.groupOverrideOrder != kv.Value.groupCanvas.sortingOrder)
            {
                kv.Value.groupOverrideOrder = kv.Value.groupCanvas.sortingOrder;
                kv.Value.isDirtry = true;
            }
        }
    }

    private Dictionary<Transform, IOverrideOrder> filterDic;
    private void RefreshAllOrder()
    {
        var iter = this.overrideOrderDic.GetEnumerator();
        while (iter.MoveNext())
        {
            var kv = iter.Current;
            if (null == kv.Key)
            {
                //   Debug.LogError("出现内存泄漏，请通知主程认真检查!!!");
                this.overrideOrderDic.Remove(kv.Key);
                break;
            }

            if (!kv.Value.isDirtry)
            {
                continue;
            }

            kv.Value.isDirtry = false;

            var orderList = ListPool<IOverrideOrder>.Get();
            if (null == filterDic) filterDic = new Dictionary<Transform, IOverrideOrder>();

            this.RefreshOrder(kv.Value, filterDic, orderList);

            ListPool<IOverrideOrder>.Release(orderList);
            filterDic.Clear();
        }
    }

    private void RefreshOrder(OverrideOrderGroupItem orderGroup, Dictionary<Transform, IOverrideOrder> filterDic, List<IOverrideOrder> orderList)
    {
        uiEffectDic.Clear();
        orderGroup.groupCanvas.overrideSorting = true;
        int maxOrder = orderGroup.groupCanvas.sortingOrder + this.groupCanvasOrderInterval - 1;
        int order = orderGroup.groupCanvas.sortingOrder + 1;
        this.SortOrderSet(orderGroup, filterDic, orderList);

        int oldOrder = order;
        bool needResetOrder = false;
        for (int i = 0; i < orderList.Count; i++)
        {
            var item = orderList[i];
            bool isNeedIncOrder = true;
            var curEffect = item as SrpUIEffect;
            Canvas effectInCanvas = null;
            if (null != curEffect)
            {
                // 相同的UIEffect，并且Canvas相同，使用相同的order
                var canvasList = ListPool<Canvas>.Get();
                curEffect.GetComponentsInParent<Canvas>(true, canvasList);
                if (canvasList.Count > 0)
                {
                    effectInCanvas = canvasList[0];
                    String_Int_Key effectKey = new String_Int_Key(ObjectNameMgr.Instance.GetObjectName(curEffect), effectInCanvas.GetInstanceID());
                    if (uiEffectDic.ContainsKey(effectKey))
                    {
                        oldOrder = order;
                        needResetOrder = true;

                        isNeedIncOrder = false;
                        order = uiEffectDic[effectKey];
                    }
                }
                ListPool<Canvas>.Release(canvasList);
            }

            int incOrder = 0;
            item.SetOverrideOrder(order, this.groupCanvasOrderInterval, maxOrder, out incOrder);
            if (needResetOrder)
            {
                needResetOrder = false;
                order = oldOrder;
            }

            if (isNeedIncOrder)
            {
                if (null != curEffect && null != effectInCanvas)
                {
                    String_Int_Key effectKey = new String_Int_Key(ObjectNameMgr.Instance.GetObjectName(curEffect), effectInCanvas.GetInstanceID());
                    uiEffectDic.Add(effectKey, order);
                }
                order += incOrder + 1;
            }

            if (order > maxOrder)
            {
                order = maxOrder;
            }
        }
    }

    // 把每个canvas下的IOverrideOrder按照树节点顺序搜索出来。然后放进orderList
    private void SortOrderSet(OverrideOrderGroupItem orderGroup, Dictionary<Transform, IOverrideOrder> filterDic, List<IOverrideOrder> orderList)
    {
        foreach (var item in orderGroup.orderSet)
        {
            if (null == item)
            {
                //Debug.LogError("出现内存泄漏，item 为null, 请通知主程认真检查!!!");
                continue;
            }

            var target = item.GetTarget();
            if (null == target || null == target.transform)
            {
                //Debug.LogError("出现内存泄漏，target为null, 请通知主程认真检查!!!");
                continue;
            }

            if (!filterDic.ContainsKey(target.transform))
            {
                filterDic.Add(target.transform, item);
            }
        }

        this.RecursionTransform(orderGroup.groupCanvas.transform, filterDic, orderList);
    }

    private void RecursionTransform(Transform transform, Dictionary<Transform, IOverrideOrder> filterDic, List<IOverrideOrder> orderList)
    {
        for (int i = 0; i < transform.childCount; i++)
        {
            Transform child = transform.GetChild(i);
            if (filterDic.ContainsKey(child))
            {
                orderList.Add(filterDic[child]);
            }

            if (child.childCount > 0)
            {
                this.RecursionTransform(child, filterDic, orderList);
            }
        }
    }
}

public class OverrideOrderGroupItem
{
    public Canvas groupCanvas;
    public int groupOverrideOrder;
    public HashSet<IOverrideOrder> orderSet = new HashSet<IOverrideOrder>();
    public bool isDirtry = false;

    public bool IsInvalid()
    {
        return null == groupCanvas;
    }
}

public interface IOverrideOrder
{
    GameObject GetTarget();
    void SetOverrideOrder(int order, int orderInterval, int maxOrder, out int incOrder);
}
