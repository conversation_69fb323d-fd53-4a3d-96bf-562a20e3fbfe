
--时装套装协议请求
CSShiZhuangSuitOperaReq = CSShiZhuangSuitOperaReq or BaseClass(BaseProtocolStruct)
function CSShiZhuangSuitOperaReq:__init()
	self.msg_type = 7200

	self.opera_type = 0
	self.param_1 = 0
	self.param_2 = 0
end

function CSShiZhuangSuitOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
end

--接收时装套装协议
SCShiZhuangSuitAllInfo = SCShiZhuangSuitAllInfo or BaseClass(BaseProtocolStruct)
function SCShiZhuangSuitAllInfo:__init()
	self.msg_type = 7201
end

function SCShiZhuangSuitAllInfo:Decode()
	self.remainder_exchange_cnt_list = {}
	for i = 1, GameEnum.MAX_SUIT_CNT do
		self.remainder_exchange_cnt_list[i] = MsgAdapter.ReadChar()
	end

	self.suit_count = MsgAdapter.ReadInt()
	self.suit_active_info_list = {}
	for i = 1, self.suit_count do
		self.suit_active_info_list[i] = {}
		self.suit_active_info_list[i].suit_id = MsgAdapter.ReadShort()
		self.suit_active_info_list[i].active_num = MsgAdapter.ReadShort()
		self.suit_active_info_list[i].active_item_id_list = {}
		for j = 1, GameEnum.SUITE_TOTAL_MAX_CNT do
			self.suit_active_info_list[i].active_item_id_list[j] = MsgAdapter.ReadUShort()
		end
	end
end

--本服修罗塔协议请求
CSXiuluoTowerEnterReq = CSXiuluoTowerEnterReq or BaseClass(BaseProtocolStruct)
function CSXiuluoTowerEnterReq:__init()
	self.msg_type = 7210
end

function CSXiuluoTowerEnterReq:Encode()
	-- print_error("-----------------------发送7210",self.msg_type)
	MsgAdapter.WriteBegin(self.msg_type)
end


--跨服修罗塔进入通知
SCXiuluoTowerCrossNotice = SCXiuluoTowerCrossNotice or BaseClass(BaseProtocolStruct)
function SCXiuluoTowerCrossNotice:__init()
	self.msg_type = 7211
end

function SCXiuluoTowerCrossNotice:Decode()
	-- print_error("---------------------接受协议7211")
end

--套装操作请求
CSSuitOperaReq = CSSuitOperaReq or BaseClass(BaseProtocolStruct)
function CSSuitOperaReq:__init()
	self.msg_type = 7213

	self.opera_type = 0
	self.param_1 = 0
	self.param_2 = 0
end

function CSSuitOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
end

--套装全部信息
SCSuitAllInfo = SCSuitAllInfo or BaseClass(BaseProtocolStruct)
function SCSuitAllInfo:__init()
	self.msg_type = 7214
end

function SCSuitAllInfo:Decode()
	-- self.part_is_suit = {}
	-- self.part_order = {}
	-- self.part_smelt_level = {}
	-- for i = 0, GameEnum.SUIT_ROLE_INDEX_MAX - 1 do
	-- 	self.part_is_suit[i] = MsgAdapter.ReadChar()-- + 1  --服务端是从-1 开始 客户端是从0开始 强行加一（迷之操作）
	-- end
	-- for i = 0, GameEnum.SUIT_ROLE_INDEX_MAX - 1 do
	-- 	self.part_order[i] = MsgAdapter.ReadChar()
	-- end
	-- for i = 0, GameEnum.SUIT_ROLE_INDEX_MAX - 1 do
	-- 	self.part_smelt_level[i] = MsgAdapter.ReadUShort()
	-- end

	local order_flag_list = {}   -- 套装物品激活标记   25套转位标记
	for i = 0, 24 do
		order_flag_list[i] = bit:d2b_l2h(MsgAdapter.ReadShort(), nil, true)
	end	
	self.order_flag_list = order_flag_list
	MsgAdapter.ReadUShort()

	local active_total_equip_strength_lv = {}
	for i = 0, 24 do
		active_total_equip_strength_lv[i] = MsgAdapter.ReadUShort()
	end	
	self.active_total_equip_strength_lv = active_total_equip_strength_lv

	local active_total_xianqi_strength_lv = {}
	for i = 0, 24 do
		active_total_xianqi_strength_lv[i] = MsgAdapter.ReadUShort()
	end	
	self.active_total_xianqi_strength_lv = active_total_xianqi_strength_lv

	-- self.active_total_equip_strength_lv = MsgAdapter.ReadUShort() --           // 当前激活装备强化等级
	-- self.active_total_xianqi_strength_lv = MsgAdapter.ReadUShort() --         // 当前激活仙器强化等级

	self.active_total_equip_upquality_lv = MsgAdapter.ReadUShort() --          // 当前激活装备品数
	self.active_total_xianqi_upquality_lv = MsgAdapter.ReadUShort() --        // 当前激活仙器品数
end

--套装熔炼返还
SCSuitSmeltLevelUp = SCSuitSmeltLevelUp or BaseClass(BaseProtocolStruct)
function SCSuitSmeltLevelUp:__init()
	self.msg_type = 7216
end

function SCSuitSmeltLevelUp:Decode()
	self.part = MsgAdapter.ReadUShort()  		-- 操作部位
	self.cur_level = MsgAdapter.ReadUShort() 	-- 当前等级
end

--强化操作请求
CSEquipGridStrOperaReq = CSEquipGridStrOperaReq or BaseClass(BaseProtocolStruct)
function CSEquipGridStrOperaReq:__init()
	self.msg_type = 7217
	self.opera_type = 0
	self.param_1 = 0
	self.param_2 = 0  -- 肉身索引
	self.is_auto_uplevel = 0
	self.repeat_times = 0
end

function CSEquipGridStrOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.is_auto_uplevel)
	MsgAdapter.WriteInt(self.repeat_times)
end

--套装全部信息
SCEquipGridStrAllInfo = SCEquipGridStrAllInfo or BaseClass(BaseProtocolStruct)
function SCEquipGridStrAllInfo:__init()
	self.msg_type = 7218
end

function SCEquipGridStrAllInfo:Decode()
	local total_strength_level = {}
	for i = 0, 24 do
		total_strength_level[i] = MsgAdapter.ReadInt()
	end

	self.total_strength_level = total_strength_level  --普通装备累计强化等级

	local xianqi_total_strength_level = {}
	for i = 0, 24 do
		xianqi_total_strength_level[i] = MsgAdapter.ReadInt()
	end

	self.xianqi_total_strength_level = xianqi_total_strength_level--仙器累计强化等级
	
	-- self.total_strength_level = MsgAdapter.ReadInt()		 
	-- self.xianqi_total_strength_level = MsgAdapter.ReadInt()	 
	
	self.strength_level = {}
	-- for i = 0, GameEnum.SUIT_ROLE_INDEX_MAX - 1 do
	for i = 0, GameEnum.MAX_ROLE_EQUIP_NUM - 1 do
		self.strength_level[i] = MsgAdapter.ReadShort()
	end
	MsgAdapter.ReadUShort()
end

--套装合成请求
CSEquipComposeOperaReq = CSEquipComposeOperaReq	 or BaseClass(BaseProtocolStruct)
function CSEquipComposeOperaReq:__init()
	self.msg_type = 7219
end

function CSEquipComposeOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.compose_id)
	MsgAdapter.WriteInt(self.best_attr_num)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
	MsgAdapter.WriteInt(self.param_4)
	MsgAdapter.WriteInt(self.param_5)
end

--无尽祭坛请求
CSWuJinJiTanReq = CSWuJinJiTanReq or BaseClass(BaseProtocolStruct)
function CSWuJinJiTanReq:__init()
	self.msg_type = 7236
	self.req_type = 0
end

function CSWuJinJiTanReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.req_type)
end

--无尽祭坛信息
SCWuJinJiTanAllInfo = SCWuJinJiTanAllInfo or BaseClass(BaseProtocolStruct)
function SCWuJinJiTanAllInfo:__init()
	self.msg_type = 7237
	self.data = {}
end

function SCWuJinJiTanAllInfo:Decode()
	self.data = {}
	self.data.fuben_type = MsgAdapter.ReadInt()						-- 副本类型
	self.data.is_finish = MsgAdapter.ReadInt()                      -- 是否结束
	self.data.curr_wave_index = MsgAdapter.ReadInt()			    -- 当前波
	self.data.kill_monster_count = MsgAdapter.ReadInt()				-- 队伍杀怪数量
	self.data.fetch_exp = MsgAdapter.ReadLL()						-- 队伍获得经验
	self.data.next_wave_refresh_timestamp = MsgAdapter.ReadUInt()	-- 下波刷怪时间戳
	self.data.prepare_end_timestamp = MsgAdapter.ReadUInt()			-- 副本准备结束时间戳
	self.data.finish_timestamp = MsgAdapter.ReadUInt()				-- 副本结束时间戳
	self.data.next_boss_refresh_timestamp = MsgAdapter.ReadUInt()	-- 下个boss刷新时间戳

	self.data.has_get_first_rward = MsgAdapter.ReadInt()	        -- 1 代表引导进入  0代表正常进入.
	self.data.is_pass = MsgAdapter.ReadInt() 						-- 是否胜利
	self.data.scene_role_num = MsgAdapter.ReadInt() 				-- 场景角色数量
	self.data.min_exp = MsgAdapter.ReadLL()							-- 最小经验获取
	self.data.max_wave_count = MsgAdapter.ReadInt()			    	-- 最大波数
	-- self.data.cur_nuqi = MsgAdapter.ReadUShort()					-- 怒气值
	-- self.data.max_nuqi = MsgAdapter.ReadUShort()	 				-- 怒气上限

end


--无尽祭坛角色信息
SCWuJinJiTanRoleInfo = SCWuJinJiTanRoleInfo or BaseClass(BaseProtocolStruct)
function SCWuJinJiTanRoleInfo:__init()
	self.msg_type = 7238
	self.data = {}
end

function SCWuJinJiTanRoleInfo:Decode()
	self.data = {}
	self.data.fb_type = MsgAdapter.ReadInt()
	self.data.is_leave_fb = MsgAdapter.ReadShort()                  -- 是否离开
	self.data.is_pass =  MsgAdapter.ReadShort()                     -- 是否通关
	self.data.kill_monster_count = MsgAdapter.ReadInt()				-- 个人杀怪数量
	self.data.fetch_exp = MsgAdapter.ReadLL()						-- 个人获得经验
	self.data.fetch_coin = MsgAdapter.ReadLL()						-- 个人获得铜币
	self.data.coin_guwu_count = MsgAdapter.ReadShort()				-- 铜币鼓舞次数
	self.data.gold_guwu_count = MsgAdapter.ReadShort()				-- 元宝鼓舞次数
	self.data.exp_percent = MsgAdapter.ReadInt()                    -- 比较历史最高经验提升百分比
	self.data.pass_time = MsgAdapter.ReadInt()                      -- 通关时间
	self.data.is_finish = MsgAdapter.ReadInt()                      -- 是否结束
	self.data.is_help = MsgAdapter.ReadInt()						-- 是否助战
	self.data.first_enter_level = MsgAdapter.ReadInt()					-- 进入时的等级
	self.data.double_reward_count = MsgAdapter.ReadShort()			-- 双倍奖励次数
	self.data.reward_item_count = MsgAdapter.ReadShort()			-- 掉落物数量
	self.data.drop_items = {}
	for i = 1, self.data.reward_item_count do
		local drop_item = {}
		drop_item.item_id = MsgAdapter.ReadUShort()
		drop_item.is_bind = MsgAdapter.ReadShort()
		drop_item.num = MsgAdapter.ReadInt()
		table.insert(self.data.drop_items, drop_item)
	end
end

-- 装备洗炼 操作请求
CSEquipBaptizeOperaReq = CSEquipBaptizeOperaReq	 or BaseClass(BaseProtocolStruct)
function CSEquipBaptizeOperaReq	:__init()
	self.msg_type = 7221
end

function CSEquipBaptizeOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
end

-- 装备洗练 所有信息
SCEquipBaptizeAllInfo = SCEquipBaptizeAllInfo or BaseClass(BaseProtocolStruct)
function SCEquipBaptizeAllInfo:__init()
	self.msg_type = 7222
end

function SCEquipBaptizeAllInfo:Decode()
	self.today_free_baptize_num = MsgAdapter.ReadInt()					-- 今日剩余免费鉴定次数
	self.baptize_list = {}												-- 洗练部位
	for i = 0, GameEnum.MAX_ROLE_EQUIP_NUM - 1 do
		self.baptize_list[i] = ProtocolStruct.ReadEquipBaptizePart()
	end
end

CSSpecialEquipUpGradeReq = CSSpecialEquipUpGradeReq or BaseClass(BaseProtocolStruct)		 -- 特殊装备进阶
function CSSpecialEquipUpGradeReq:__init()
	self.msg_type = 7223
end

function CSSpecialEquipUpGradeReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.grid_index)
	MsgAdapter.WriteShort(self.is_bag_index)
end

SCSpecialEquipUpGradeInfo = SCSpecialEquipUpGradeInfo or BaseClass(BaseProtocolStruct)		 -- 特殊装备进阶
function SCSpecialEquipUpGradeInfo:__init()
	self.msg_type = 7215
end

function SCSpecialEquipUpGradeInfo:Decode()
	self.succ_reason_type = MsgAdapter.ReadInt()
end

SCWuJinJiTanFirstTimeInfo = SCWuJinJiTanFirstTimeInfo	 or BaseClass(BaseProtocolStruct)
function SCWuJinJiTanFirstTimeInfo:__init()
	self.msg_type = 7239
	self.first_time = 0
end

function SCWuJinJiTanFirstTimeInfo:Decode()
	self.first_time = MsgAdapter.ReadInt()
end

---------------宠物本---------------
CSNewPetFbReq = CSNewPetFbReq or BaseClass(BaseProtocolStruct)
function CSNewPetFbReq:__init()
	self.msg_type = 7240
end

function CSNewPetFbReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.req_type)
	MsgAdapter.WriteInt(self.param_1)
end


SCNewPetFbSceneInfo = SCNewPetFbSceneInfo or BaseClass(BaseProtocolStruct)
function SCNewPetFbSceneInfo:__init()
	self.msg_type = 7241
end

function SCNewPetFbSceneInfo:Decode()
	self.level = MsgAdapter.ReadInt()

	self.is_finish = MsgAdapter.ReadChar()
	self.is_pass = MsgAdapter.ReadChar()
	self.curr_wave_index = MsgAdapter.ReadShort()

	self.max_wave_count =  MsgAdapter.ReadInt()

	self.get_exp = MsgAdapter.ReadLL()

	self.get_coin = MsgAdapter.ReadInt()
	self.pass_time_s = MsgAdapter.ReadInt()
	self.cur_star_num = MsgAdapter.ReadShort()
	self.cur_monster_num = MsgAdapter.ReadShort()

	self.kick_out_timestamp = MsgAdapter.ReadUInt()
	self.next_wave_refresh_timestamp = MsgAdapter.ReadUInt()
	self.prepare_end_timestamp = MsgAdapter.ReadUInt()
	self.finish_timestamp = MsgAdapter.ReadUInt()
	self.next_star_timestamp = MsgAdapter.ReadUInt()

	self.goddess_hp = MsgAdapter.ReadLL()
	self.status = MsgAdapter.ReadInt()
end


SCNewPetFbOtherInfo = SCNewPetFbOtherInfo or BaseClass(BaseProtocolStruct)
function SCNewPetFbOtherInfo:__init()
	self.msg_type = 7242
end

function SCNewPetFbOtherInfo:Decode()
	self.pass_petfb_flag = MsgAdapter.ReadLL()
	-- MsgAdapter.ReadShort()
	self.pass_fb_max_level = MsgAdapter.ReadShort()
	self.day_times = MsgAdapter.ReadShort()
	self.buy_times = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadShort()
	self.pass_petfb_star = {}
	for i = 0, GameEnum.NEW_PETFB_LEVEL_TYPE_MAX - 1 do
		self.pass_petfb_star[i] = MsgAdapter.ReadChar()
	end
end

SCNewPetFbRewarInfo = SCNewPetFbRewarInfo or BaseClass(BaseProtocolStruct)
function SCNewPetFbRewarInfo:__init()
	self.msg_type = 7243
	self.item_list = {}
end

function SCNewPetFbRewarInfo:Decode()
	self.level = MsgAdapter.ReadInt()
	self.is_sweep_fb = MsgAdapter.ReadInt()
	self.is_pass =  MsgAdapter.ReadShort()
	self.star_num = MsgAdapter.ReadShort()
	self.get_exp = MsgAdapter.ReadLL()
	self.get_coin = MsgAdapter.ReadInt()
	self.item_count = MsgAdapter.ReadInt()
	self.item_list = {}
	local data
	for i=0, self.item_count - 1 do
		data = {}
		data.item_id = MsgAdapter.ReadUShort()
		data.is_bind =  MsgAdapter.ReadShort()
		data.num = MsgAdapter.ReadInt()
		self.item_list[i] = data
	end
end

--天书寻主奖励标记
SCTianshuXunzhuInfo = SCTianshuXunzhuInfo or BaseClass(BaseProtocolStruct)
function SCTianshuXunzhuInfo:__init()
	self.msg_type = 7246
end

function SCTianshuXunzhuInfo:Decode()
	self.tianshu_xunzhu_goal_fetch_flag_list = {}
	self.tianshu_xunzhu_goal_can_fetch_flag_list = {}
	for i = 1, GameEnum.TIANSHU_MAX_TYPE do              -- 领取
		self.tianshu_xunzhu_goal_fetch_flag_list[i] = MsgAdapter.ReadUInt()
	end
	for i = 1, GameEnum.TIANSHU_MAX_TYPE do              -- 激活
		self.tianshu_xunzhu_goal_can_fetch_flag_list[i] = MsgAdapter.ReadUInt()
	end
end

--天书寻主boss击杀标记
SCRoleKillBossIDList = SCRoleKillBossIDList or BaseClass(BaseProtocolStruct)
function SCRoleKillBossIDList:__init()
	self.msg_type = 7247
end

function SCRoleKillBossIDList:Decode()
	self.count = MsgAdapter.ReadInt()
	self.kill_boss_id_list = {}
	local boss_id = 0
	for i = 1, self.count do
		MsgAdapter.ReadShort()
		boss_id = MsgAdapter.ReadUShort()
		self.kill_boss_id_list[boss_id] = boss_id
	end
end

-- 神兽操作请求
CSShenshouOperaReq = CSShenshouOperaReq or BaseClass(BaseProtocolStruct)
function CSShenshouOperaReq	:__init()
	self.msg_type = 7227
	self.param_4 = 0
end

function CSShenshouOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.opera_type)
	MsgAdapter.WriteShort(self.param_1)
	MsgAdapter.WriteShort(self.param_2)
	MsgAdapter.WriteShort(self.param_3)
	MsgAdapter.WriteInt(self.param_4)
end

-- 神兽请求强化装备
CSSHenshouReqStrength = CSSHenshouReqStrength or BaseClass(BaseProtocolStruct)
function CSSHenshouReqStrength:__init()
	self.msg_type = 7228
end

function CSSHenshouReqStrength:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.soul_ring_seq) 					-- soul_ring_seq
	MsgAdapter.WriteShort(self.equip_index)						-- 要强化的装备下标
	MsgAdapter.WriteShort(self.is_double_shuliandu)				-- 是否双倍熟练度
	MsgAdapter.WriteShort(self.destroy_num)						-- 消耗物品个数
	for i=1, #self.destroy_backpack_index_list do
		MsgAdapter.WriteShort(self.destroy_backpack_index_list[i] or 0)
	end
end

-- 神兽背包信息
SCShenshouBackpackInfo = SCShenshouBackpackInfo or BaseClass(BaseProtocolStruct)
function SCShenshouBackpackInfo:__init()
	self.msg_type = 7229
end

function SCShenshouBackpackInfo:Decode()
    self.reason_type = MsgAdapter.ReadInt()
	self.is_full_backpack = MsgAdapter.ReadChar()			-- 是否全量信息下发
	MsgAdapter.ReadChar()
	self.grid_num = MsgAdapter.ReadShort()					-- 格子数量
	self.grid_list = {}										-- 格子信息列表
	for i = 1, self.grid_num do
		local data = {}
		MsgAdapter.ReadShort()
		data.index = MsgAdapter.ReadShort()
		data.item_id = MsgAdapter.ReadUShort()
		data.strength_level = MsgAdapter.ReadShort()		-- 强化等级
		data.shuliandu = MsgAdapter.ReadInt()				-- 熟练度
		data.bind = MsgAdapter.ReadInt()
		data.attr_list = {}
		for j=1, GameEnum.SHENSHOU_MAX_EQUIP_ATTR_COUNT do
			local vo = {}
			vo.attr_type = MsgAdapter.ReadShort()
			MsgAdapter.ReadShort()
			vo.attr_value = MsgAdapter.ReadInt()
			data.attr_list[j] = vo
		end
		self.grid_list[i] = data
	end
end
----------------------------坐骑装备--------------------

-- 坐骑背包信息
SCMountBackpackInfo = SCMountBackpackInfo or BaseClass(BaseProtocolStruct)
function SCMountBackpackInfo:__init()
	self.msg_type = 4731
end

function SCMountBackpackInfo:Decode()

	self.is_full_backpack = MsgAdapter.ReadChar()			-- 是否全量信息下发
	MsgAdapter.ReadChar()
	self.grid_num = MsgAdapter.ReadShort()					-- 格子数量
	self.grid_list = {}										-- 格子信息列表
	for i=1, self.grid_num do
		local data = {}
		MsgAdapter.ReadShort()
		data.index = MsgAdapter.ReadShort()
		data.item_id = MsgAdapter.ReadShort()
		data.strength_level = MsgAdapter.ReadShort()		-- 强化等级
		data.shuliandu = MsgAdapter.ReadInt()				-- 熟练度
		data.attr_list = {}
		for j=1, GameEnum.SHENSHOU_MAX_EQUIP_ATTR_COUNT do
			local vo = {}
			vo.attr_type = MsgAdapter.ReadShort()
			MsgAdapter.ReadShort()
			vo.attr_value = MsgAdapter.ReadInt()
			data.attr_list[j] = vo
		end
		self.grid_list[i] = data
	end
end

-- 坐骑背包信息
SCLingChongBackpackInfo = SCLingChongBackpackInfo or BaseClass(BaseProtocolStruct)
function SCLingChongBackpackInfo:__init()
	self.msg_type = 4732
end

function SCLingChongBackpackInfo:Decode()

	self.is_full_backpack = MsgAdapter.ReadChar()			-- 是否全量信息下发
	MsgAdapter.ReadChar()
	self.grid_num = MsgAdapter.ReadShort()					-- 格子数量
	self.grid_list = {}										-- 格子信息列表
	for i=1, self.grid_num do
		local data = {}
		MsgAdapter.ReadShort()
		data.index = MsgAdapter.ReadShort()
		data.item_id = MsgAdapter.ReadShort()
		data.strength_level = MsgAdapter.ReadShort()		-- 强化等级
		data.shuliandu = MsgAdapter.ReadInt()				-- 熟练度
		data.attr_list = {}
		for j=1, GameEnum.SHENSHOU_MAX_EQUIP_ATTR_COUNT do
			local vo = {}
			vo.attr_type = MsgAdapter.ReadShort()
			MsgAdapter.ReadShort()
			vo.attr_value = MsgAdapter.ReadInt()
			data.attr_list[j] = vo
		end
		self.grid_list[i] = data
	end
end

-- -- 坐骑操作请求
-- CSMountOperaReq = CSMountOperaReq or BaseClass(BaseProtocolStruct)
-- function CSMountOperaReq	:__init()
-- 	self.msg_type = 7227
-- 	self.param_4 = 0
-- end

-- function CSMountOperaReq:Encode()
-- 	MsgAdapter.WriteBegin(self.msg_type)
-- 	MsgAdapter.WriteShort(self.opera_type)
-- 	MsgAdapter.WriteShort(self.param_1)
-- 	MsgAdapter.WriteShort(self.param_2)
-- 	MsgAdapter.WriteShort(self.param_3)
-- 	MsgAdapter.WriteInt(self.param_4)
-- end

-- -- 坐骑请求强化装备
-- CSSHenshouReqStrength = CSSHenshouReqStrength or BaseClass(BaseProtocolStruct)
-- function CSSHenshouReqStrength	:__init()
-- 	self.msg_type = 7228
-- end

-- function CSMountReqStrength:Encode()
-- 	MsgAdapter.WriteBegin(self.msg_type)
-- 	MsgAdapter.WriteShort(self.shenshou_id) 					-- 神兽id
-- 	MsgAdapter.WriteShort(self.equip_index)						-- 要强化的装备下标
-- 	MsgAdapter.WriteShort(self.is_double_shuliandu)				-- 是否双倍熟练度
-- 	MsgAdapter.WriteShort(self.destroy_num)						-- 消耗物品个数
-- 	for i=1, #self.destroy_backpack_index_list do
-- 		MsgAdapter.WriteShort(self.destroy_backpack_index_list[i] or 0)
-- 	end
-- end
-------------------------坐骑装备------------------e

local function GetShenShouInfo()
	local data = {}
	data.shenshou_id = MsgAdapter.ReadShort()    -- 当前得魂环Id   id = shou_id > 0 代表解锁  不用手动激活  （注意shou_id 从1开始）
	MsgAdapter.ReadShort()

	local equip_list = {}
	for j = 0, 4 do
		local equip_data = {}
		equip_data.slot_index = j
		equip_data.item_id = MsgAdapter.ReadUShort()     
		equip_data.strength_level = MsgAdapter.ReadShort()
		equip_data.shuliandu = MsgAdapter.ReadInt()
		equip_data.bind = MsgAdapter.ReadShort()
		equip_data.start_level = MsgAdapter.ReadChar()
		equip_data.re_ch = MsgAdapter.ReadChar() 

		local attr_list = {}
		for k = 1, GameEnum.SHENSHOU_MAX_EQUIP_ATTR_COUNT do
			local tab = {}
			tab.attr_type = MsgAdapter.ReadShort()
			tab.is_star = MsgAdapter.ReadChar()
			tab.reserve_sh = MsgAdapter.ReadChar()
			tab.attr_value = MsgAdapter.ReadInt()
			attr_list[k] = tab
		end

		equip_data.attr_list = attr_list
		equip_data.quality = 0
		equip_data.star_count = 0
		equip_data.score = 0
		equip_data.base_score = 0

		local item_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(equip_data.item_id)
		if item_cfg then
			equip_data.quality = item_cfg.quality
			equip_data.star_count = ShenShouWGData.Instance:GetStarCount(equip_data, item_cfg)
			equip_data.score, equip_data.base_score = ShenShouWGData.Instance:GetShenShouItemScore(equip_data)
		end

		equip_list[j] = equip_data
	end

	data.equip_list = equip_list
	return data
end


-- 神兽信息
SCShenshouListInfo = SCShenshouListInfo or BaseClass(BaseProtocolStruct)
function SCShenshouListInfo:__init()
	self.msg_type = 7230
end

function SCShenshouListInfo:Decode()
	self.shenshou_exp = MsgAdapter.ReadLL()
	local shenshou_list = {}

	for i = 0, 7 do
		shenshou_list[i] = GetShenShouInfo()
	end

	self.shenshou_list = shenshou_list

	-- self.is_all_shenshou = MsgAdapter.ReadChar()			-- 是否全量信息下发
	-- MsgAdapter.ReadChar()
	-- self.shenshou_num = MsgAdapter.ReadShort()
	-- self.shenshou_list = {}								-- 有装备信息的神兽列表
	-- for i=1, self.shenshou_num do
	-- 	local data = {}
	-- 	MsgAdapter.ReadShort()
	-- 	data.shou_id = MsgAdapter.ReadShort()
	-- 	data.has_zhuzhan = MsgAdapter.ReadChar()			-- 是否助战了  0为未助战,1-5表示在第几个助战位
	-- 	MsgAdapter.ReadChar()
	-- 	MsgAdapter.ReadShort()
	-- 	data.equip_list = {}
	-- 	data.equip_amount = 0

	-- 	for j=1, GameEnum.SHENSHOU_MAX_EQUIP_SLOT_INDEX + 1 do
	-- 		local t = {}
	-- 		t.slot_index = j - 1
	-- 		t.item_id = MsgAdapter.ReadUShort()
	-- 		if t.item_id > 0 then
	-- 			data.equip_amount = data.equip_amount + 1
	-- 		end
	-- 		t.strength_level = MsgAdapter.ReadShort()
	-- 		t.shuliandu = MsgAdapter.ReadInt()
	-- 		t.bind = MsgAdapter.ReadInt()
	-- 		t.attr_list = {}
	-- 		t.score = 0
	-- 		t.base_score = 0
			
	-- 		for k=1, GameEnum.SHENSHOU_MAX_EQUIP_ATTR_COUNT do
	-- 			local tab = {}
	-- 			tab.attr_type = MsgAdapter.ReadShort()
	-- 			MsgAdapter.ReadShort()
	-- 			tab.attr_value = MsgAdapter.ReadInt()
	-- 			t.attr_list[k] = tab
	-- 		end
	-- 		data.equip_list[j] = t
	-- 	end
	-- 	self.shenshou_list[i] = data
	-- end
end

--获取成就奖励  7231
CSAchievementFetchReward = CSAchievementFetchReward or BaseClass(BaseProtocolStruct)
function CSAchievementFetchReward:__init()
	self.msg_type = 7231
end

function CSAchievementFetchReward:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.achievement_id)
	MsgAdapter.WriteShort(self.is_achievement_id)
end

--所有成就信息  7232 --全部
SCAchievementAllInfo = SCAchievementAllInfo or BaseClass(BaseProtocolStruct)
function SCAchievementAllInfo:__init()
	self.msg_type = 7232
end

function SCAchievementAllInfo:Decode()
	self.achieve_has_active_flag = {}
	self.achieve_has_fatch_reward_flag = {}
	self.achieve_progress_value = {}

	for i = 1, 128 do
		self.achieve_has_active_flag[i] = MsgAdapter.ReadUChar()
	end
	for i = 1, 128 do
		self.achieve_has_fatch_reward_flag[i] = MsgAdapter.ReadUChar()
	end
	for i = 0, 99 do
		self.achieve_progress_value[i] = MsgAdapter.ReadUInt()
	end
	local count = MsgAdapter.ReadInt()
	self.spe_progress_list = {}
	for i=1,count do
		local id = MsgAdapter.ReadInt()
		self.spe_progress_list[id] = MsgAdapter.ReadUInt()
	end

end
--成就变更信息 7233--单条
SCAchievementInfoChange = SCAchievementInfoChange or BaseClass(BaseProtocolStruct)
function SCAchievementInfoChange:__init()
	self.msg_type = 7233
end

function SCAchievementInfoChange:Decode()
	MsgAdapter.ReadShort()
	self.item_count = MsgAdapter.ReadShort()
	self.item_list = {}
	for i = 1, self.item_count do
		self.item_list[i] = {}
		self.item_list[i].achievement_id = MsgAdapter.ReadShort()
		self.item_list[i].has_active = MsgAdapter.ReadChar()
		self.item_list[i].has_fetch_reward = MsgAdapter.ReadChar()
		self.item_list[i].progress_value = MsgAdapter.ReadUInt()
	end
end

-- 神兽额外助战位
SCShenshouUpdateInfo = SCShenshouUpdateInfo or BaseClass(BaseProtocolStruct)
function SCShenshouUpdateInfo:__init()
	self.msg_type = 7234
end

function SCShenshouUpdateInfo:Decode()
	--助战位开启标识（从最低位到最高位表示1-5是否开启，0为未开启1为开启）
	-- self.extra_zhuzhan_count = MsgAdapter.ReadShort()
	-- MsgAdapter.ReadShort()
	self.index = MsgAdapter.ReadInt()
	self.shenshou_info = GetShenShouInfo()
end

CSShenshouDecompos = CSShenshouDecompos or BaseClass(BaseProtocolStruct)
function CSShenshouDecompos:__init()
	self.msg_type = 7235
end

function CSShenshouDecompos:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteShort(self.is_double)
	MsgAdapter.WriteShort(self.count)

	for i = 1, self.count do
		MsgAdapter.WriteUShort(self.msg_item[i].item_id or 0)
		MsgAdapter.WriteShort(self.msg_item[i].index or -1) -- bag_index
		-- MsgAdapter.WriteInt(self.msg_item[i].count or 0)
	end
end


--灵宠信息
SCLingChongInfo = SCLingChongInfo or BaseClass(BaseProtocolStruct)
function SCLingChongInfo:__init()
	self.msg_type = 7251
end

function SCLingChongInfo:Decode()
	self.level = MsgAdapter.ReadShort()
	local star_level = MsgAdapter.ReadShort()
	self.star_level = star_level
	local star_cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, star_level)
	self.grade = star_cfg and star_cfg.grade_num or 0
	self.used_imageid = MsgAdapter.ReadInt()
	self.upstar_bless_val = MsgAdapter.ReadInt()
	self.uplevel_exp_val = MsgAdapter.ReadInt()
	self.active_image_flag = MsgAdapter.ReadInt()
	self.active_skill_flag = MsgAdapter.ReadInt()

	self.shuxingdan_list = {}
	for k = 0, GameEnum.SHUXINGDAN_MAX_TYPE - 1 do
		self.shuxingdan_list[k] = MsgAdapter.ReadInt()
	end 

	----[[幻化信息
	local function lingChongSpecialImageItem()
		local data = {}
		local grade = MsgAdapter.ReadChar()
		data.grade = grade <= 0 and 0 or grade
		local flag = MsgAdapter.ReadChar()
		data.skill_active_flag = bit:d2b_l2h(flag, nil, true)
		MsgAdapter.ReadShort()
		data.star_level = MsgAdapter.ReadShort()
		MsgAdapter.ReadShort()
		data.grade_exp_val = MsgAdapter.ReadInt()
		return data
	end

	self.special_image_list = {}
	 for k = 0, 255 do
		self.special_image_list[k] = lingChongSpecialImageItem()
	end
	--]]

	----[[废弃
	MsgAdapter.ReadShort()
	self.fuhun_skill_level = MsgAdapter.ReadShort()
	self.equip_list = {}
	for j = 1, 4 do
		local t = {}
		t.item_id = MsgAdapter.ReadShort()
		t.strength_level = MsgAdapter.ReadShort()
		t.fuhun_level = MsgAdapter.ReadShort()
		t.reserve_sh = MsgAdapter.ReadShort()
		self.equip_list[j] = t
	end
	--]]

	self.skill_level_list = {}
	for i = 0, 4 do
		self.skill_level_list[i] = MsgAdapter.ReadInt()
	end

	-- 特权
	local right_flag_low = MsgAdapter.ReadUInt()
	local right_flag_high = MsgAdapter.ReadUInt()
	self.right_flag = bit:ll2b_two(right_flag_low, right_flag_high)
end

--灵宠请求
CSLingChongReq = CSLingChongReq or BaseClass(BaseProtocolStruct)
function CSLingChongReq:__init()
	self.msg_type = 7250
end

function CSLingChongReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.req_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
end

SCLingChongSkillTargetList = SCLingChongSkillTargetList or BaseClass(BaseProtocolStruct)
function SCLingChongSkillTargetList:__init()
	self.msg_type = 7252
end

function SCLingChongSkillTargetList:Decode()
	self.injure = MsgAdapter.ReadLL()
	self.target_count = MsgAdapter.ReadInt()
	self.deliver_obj_id = MsgAdapter.ReadUShort()
	self.target_objid_list = {}
	for i = 1, self.target_count do
		self.target_objid_list[i] = MsgAdapter.ReadUShort()
	end
end

CSLingChongDevourEquip = CSLingChongDevourEquip or BaseClass(BaseProtocolStruct)
function CSLingChongDevourEquip:__init()
	self.msg_type = 7253
end

function CSLingChongDevourEquip:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteShort(self.is_recalc)
	for i = 1, GameEnum.LINGCHONG_DEVOUR_INDEX_MAX do
		MsgAdapter.WriteShort(self.index_list[i] or -1)
	end
end

-- 组队装备本场景信息
SCTeamEquipFBInfo = SCTeamEquipFBInfo or BaseClass(BaseProtocolStruct)
function SCTeamEquipFBInfo:__init()
	self.msg_type = 7256

	self.level = 0											-- 层数
	self.is_finish = 0										-- 是否结束
	self.is_pass = 0										-- 是否离开
	self.curr_wave_index = 0								-- 当前波索引
	self.max_wave_count = 0								    -- 总的波数
	self.cur_star_num = 0									-- 当前星数

	self.prepare_end_timestamp = 0					        -- 副本准备状态结束时间戳
	self.finish_timestamp = 0						        -- 副本结束时间戳
	self.next_star_timestamp = 0					        -- 下一星级时间戳
	self.next_wave_refresh_timestamp = 0			        -- 下一波刷怪时间戳
	self.kick_out_timestamp = 0					            -- 延迟踢出时间戳
	self.pass_time_s = 0									-- 进入副本到目前经过的时间

	self.kill_monster_num = 0                               -- 当前波已被击杀的数量
	self.cur_wave_monster_num = 0                           -- 这一波总的数量
end

function SCTeamEquipFBInfo:Decode()
	self.level = MsgAdapter.ReadChar()
	self.is_finish = MsgAdapter.ReadChar()
	self.is_pass = MsgAdapter.ReadChar()
	self.curr_wave_index = MsgAdapter.ReadChar()

	self.max_wave_count = MsgAdapter.ReadShort()
	self.cur_star_num =  MsgAdapter.ReadShort()

	self.prepare_end_timestamp = MsgAdapter.ReadUInt()
	self.finish_timestamp = MsgAdapter.ReadUInt()
	self.next_star_timestamp = MsgAdapter.ReadUInt()
	self.next_wave_refresh_timestamp = MsgAdapter.ReadUInt()
	self.kick_out_timestamp = MsgAdapter.ReadUInt()
	self.pass_time_s = MsgAdapter.ReadInt()

	self.kill_monster_num = MsgAdapter.ReadShort()
	self.cur_wave_monster_num = MsgAdapter.ReadShort()
end

-- 组队装备本 角色信息
SCTeamEquipFBRoleInfo = SCTeamEquipFBRoleInfo or BaseClass(BaseProtocolStruct)
function SCTeamEquipFBRoleInfo:__init()
	self.msg_type = 7257

	self.layer = 0                                              -- 层数
	self.is_fb_info = 0                                         -- 是否是副本内的信息
	self.day_enterfb_times = 0								   	-- 每天进入次数
	self.help_times = 0									     	-- 已助战次数
	self.is_pass = 0										    -- 是否通关
	self.star_num = 0							            	-- 星数
	self.get_exp = 0							            	-- 经验
	self.get_coin = 0									        -- 铜币

	self.reward_item_list = {}                                  -- 奖励物品列表
end

function SCTeamEquipFBRoleInfo:Decode()
	self.layer = MsgAdapter.ReadShort()
	self.is_fb_info = MsgAdapter.ReadShort()
	self.day_enterfb_times = MsgAdapter.ReadChar()
	self.help_times = MsgAdapter.ReadChar()
	self.is_pass = MsgAdapter.ReadChar()
	self.star_num = MsgAdapter.ReadChar()

	self.get_exp = MsgAdapter.ReadLL()
	self.get_coin =  MsgAdapter.ReadInt()

	local reward_item_count = MsgAdapter.ReadInt()
	self.reward_item_list = {}
	for i = 0, reward_item_count - 1 do
		self.reward_item_list[i] = {}
		self.reward_item_list[i].item_id = MsgAdapter.ReadUShort()
		self.reward_item_list[i].is_bind = MsgAdapter.ReadShort()
		self.reward_item_list[i].num = MsgAdapter.ReadInt()
	end
end

-- 回复是否进入组队副本
CSApplyForEnterTeamFbReq = CSApplyForEnterTeamFbReq or BaseClass(BaseProtocolStruct)
function CSApplyForEnterTeamFbReq:__init()
	self.msg_type = 7259
end

function CSApplyForEnterTeamFbReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.team_index)
	MsgAdapter.WriteInt(self.fb_type)
	MsgAdapter.WriteShort(self.is_accept)
	MsgAdapter.WriteShort(self.layer)
end


-- 组队副本准备通知
SCEnterTeamFbNotice = SCEnterTeamFbNotice or BaseClass(BaseProtocolStruct)
function SCEnterTeamFbNotice:__init()
	self.msg_type = 7258
	self.member_list = {}
end

function SCEnterTeamFbNotice:Decode()
	self.member_list = {}
	local count = MsgAdapter.ReadInt()
	for i = 0, count - 1 do
		self.member_list[i] = {}
		self.member_list[i].uid = MsgAdapter.ReadInt()
		self.member_list[i].name = MsgAdapter.ReadStrN(32)
		self.member_list[i].prof = MsgAdapter.ReadShort()
		self.member_list[i].result = MsgAdapter.ReadChar()      -- 1为同意  0不同意
		self.member_list[i].is_leader = MsgAdapter.ReadChar()   -- 是否队长 1队长  0不是
		self.member_list[i].fb_type = MsgAdapter.ReadShort()
		self.member_list[i].fb_layer = MsgAdapter.ReadShort()
	end
end
---------------------------------------------------------------------
-- 人物天赋请求
CSRoleTelentOperate = CSRoleTelentOperate or BaseClass(BaseProtocolStruct)
function CSRoleTelentOperate:__init()
	self.msg_type = 7260
end

function CSRoleTelentOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
end


-- 人物天赋信息
SCRoleTelentInfo = SCRoleTelentInfo or BaseClass(BaseProtocolStruct)
function SCRoleTelentInfo:__init()
	self.msg_type = 7261
end

function SCRoleTelentInfo:Decode()
	self.talent_level_list = {}
	for zu = 1, GameEnum.MAX_TELENT_TYPE_COUT do
		self.talent_level_list[zu] = {}
		for i = 1,GameEnum.MAX_TELENT_INDEX_COUT do
			self.talent_level_list[zu][i] = MsgAdapter.ReadChar()
		end
	end
	self.talent_point = MsgAdapter.ReadInt()
end
------------------------------------------------------------------
---------------------------帮派答题------------------------------
--请求答题排行
CSGuildQuestionQueryRank = CSGuildQuestionQueryRank or BaseClass(BaseProtocolStruct)
function CSGuildQuestionQueryRank:__init()
	self.msg_type = 7204
end

function CSGuildQuestionQueryRank:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end


--请求进入活动
CSGuildQuestionEnterReq = CSGuildQuestionEnterReq or BaseClass(BaseProtocolStruct)
function CSGuildQuestionEnterReq:__init()
	self.msg_type = 7205
end

function CSGuildQuestionEnterReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

-- 玩家信息
SCGuildQuestionPlayerInfo = SCGuildQuestionPlayerInfo or BaseClass(BaseProtocolStruct)
function SCGuildQuestionPlayerInfo:__init()
	self.msg_type = 7206

	self.uid = 0
	self.answer_name = ""
	self.exp = 0                                              -- 经验
	self.guild_gongxian = 0                                   -- 帮派贡献
	self.guild_score = 0                                      -- 帮派积分
	self.is_gather = 0                                        -- 是否采集
	self.true_uid = 0                                         -- 大于0 答对
	self.true_name = 0                                        -- 答对人名字
end

function SCGuildQuestionPlayerInfo:Decode()
	self.uid = MsgAdapter.ReadInt()
	self.answer_name = MsgAdapter.ReadStrN(32)
	self.exp = MsgAdapter.ReadLL()
	self.guild_gongxian = MsgAdapter.ReadInt()
	self.guild_score = MsgAdapter.ReadInt()
	self.is_gather = MsgAdapter.ReadInt()
	self.true_uid = MsgAdapter.ReadInt()
	self.true_name = MsgAdapter.ReadStrN(32)
end

-- 帮派答题积分排行
SCGuildQuestionGuildRankInfo = SCGuildQuestionGuildRankInfo or BaseClass(BaseProtocolStruct)
function SCGuildQuestionGuildRankInfo:__init()
	self.msg_type = 7207

	self.rank_count = 0
	self.guild_rank_list = {}
end

function SCGuildQuestionGuildRankInfo:Decode()
	self.rank_count = MsgAdapter.ReadInt()
	self.guild_rank_list = {}
	for i = 1,self.rank_count do
		self.guild_rank_list[i] = {}
		self.guild_rank_list[i].guild_id = MsgAdapter.ReadInt()                      -- 帮派id
		self.guild_rank_list[i].guild_name = MsgAdapter.ReadStrN(32)                 -- 帮派名字
		self.guild_rank_list[i].guild_score = MsgAdapter.ReadInt()                   -- 帮派积分
		self.guild_rank_list[i].right_answer_num = MsgAdapter.ReadInt()              -- 帮派积分
		self.guild_rank_list[i].server_id = MsgAdapter.ReadInt()              		 -- 服务器id
	end
end

-- 帮派答题题目
SCGuildQuestionQuestionInfo = SCGuildQuestionQuestionInfo or BaseClass(BaseProtocolStruct)
function SCGuildQuestionQuestionInfo:__init()
	self.msg_type = 7208

	self.question_state = 0					     -- 0：准备中；1：开始了；2：将要结束
	self.question_state_change_timestamp = 0     -- 状态切换时间戳
	self.question_index = 0                      -- 第几题
	self.question_id = 0                         -- 题目id
	self.question_end_timestamp = 0 			 -- 回答问题倒计时
end

function SCGuildQuestionQuestionInfo:Decode()
	self.question_state = MsgAdapter.ReadInt()
	self.question_state_change_timestamp = MsgAdapter.ReadUInt()
	self.question_index = MsgAdapter.ReadInt()
	self.question_id = MsgAdapter.ReadInt()
	self.question_end_timestamp = MsgAdapter.ReadUInt()
end

-- 帮派答题结果
SCGuildQuestionRoundResult = SCGuildQuestionRoundResult or BaseClass(BaseProtocolStruct)
function SCGuildQuestionRoundResult:__init()
	self.msg_type = 7209
end

function SCGuildQuestionRoundResult:Decode()
	self.question_index = MsgAdapter.ReadInt()
	self.question_id = MsgAdapter.ReadInt()
	self.answer_role_uid = MsgAdapter.ReadInt()
	self.answer_role_name = MsgAdapter.ReadStrN(32)
	self.answer = MsgAdapter.ReadStrN(256)
	self.next_round_time = MsgAdapter.ReadUInt()
end

--------------------------心魔副本---------------------------------------------
SCXinMoFBFinishInfo = SCXinMoFBFinishInfo or BaseClass(BaseProtocolStruct)
function SCXinMoFBFinishInfo:__init()
	self.msg_type = 7270
end

function SCXinMoFBFinishInfo:Decode()
	self.fb_finish_timestamp = MsgAdapter.ReadUInt()
	self.fb_next_refresh_timestamp = MsgAdapter.ReadUInt() -- 下次刷怪时间
self.kick_out_timestamp = MsgAdapter.ReadUInt()
	self.get_exp = MsgAdapter.ReadLL()
	self.get_coin = MsgAdapter.ReadInt()
	self.skill_index = MsgAdapter.ReadInt()
	self.layer = MsgAdapter.ReadChar()
	self.is_finish = MsgAdapter.ReadChar()
	self.is_pass = MsgAdapter.ReadChar()
	local reward_item_count = MsgAdapter.ReadChar()
	self.reward_item_list = {}
	for i = 1, reward_item_count do
		local item_info = {}
		item_info.item_id = MsgAdapter.ReadUShort()
		item_info.is_bind = MsgAdapter.ReadShort()
		item_info.num = MsgAdapter.ReadInt()
		self.reward_item_list[i] = item_info
	end
end

SCXinMoFBRoleInfo = SCXinMoFBRoleInfo or BaseClass(BaseProtocolStruct)
function SCXinMoFBRoleInfo:__init()
	self.msg_type = 7271

	self.xinmo_fb_pass_flag = 0
end

function SCXinMoFBRoleInfo:Decode()
	self.xinmo_fb_pass_flag = MsgAdapter.ReadUShort()
	MsgAdapter.ReadShort()
end
------------------------------------------------------------------------------

-- 小鬼守护请求
CSImpGuardOperaReq = CSImpGuardOperaReq or BaseClass(BaseProtocolStruct)
function CSImpGuardOperaReq:__init()
	self.msg_type = 7266
end

function CSImpGuardOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteShort(self.param1)
	MsgAdapter.WriteShort(self.param2)
end

-- 小鬼守护信息
SCImpGuardInfo = SCImpGuardInfo or BaseClass(BaseProtocolStruct)
function SCImpGuardInfo:__init()
	self.msg_type = 7267
end

function SCImpGuardInfo:Decode()
	self.used_imp_type_1 = MsgAdapter.ReadInt()
	self.used_imp_type_2 = MsgAdapter.ReadInt()

	self.item_wrapper = {}
	for i=1,2 do
		self.item_wrapper[i] = ProtocolStruct.ReadItemDataWrapper()
	end
end


--7276 土豪金所有信息
SCTuHaoJinInfo = SCTuHaoJinInfo or BaseClass(BaseProtocolStruct)
function SCTuHaoJinInfo:__init()
	self.msg_type = 7276
end

function SCTuHaoJinInfo:Decode()
	self.tuhaojin_level = MsgAdapter.ReadShort()
	MsgAdapter.ReadShort()
	self.cur_tuhaojin_color = MsgAdapter.ReadChar()
	self.max_tuhaojin_color = MsgAdapter.ReadChar()
end

--7277 土豪金设置请求
CSUseTuHaoJinReq = CSUseTuHaoJinReq or BaseClass(BaseProtocolStruct)
function CSUseTuHaoJinReq:__init()
	self.msg_type = 7277
end

function CSUseTuHaoJinReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteChar(self.use_tuhaojin_color)
	MsgAdapter.WriteChar(self.reserve_1)
	MsgAdapter.WriteShort(self.reserve_2)
end

--7278 土豪金升级请求
CSTuhaojinUpLevelReq = CSTuhaojinUpLevelReq or BaseClass(BaseProtocolStruct)
function CSTuhaojinUpLevelReq:__init()
	self.msg_type = 7278
end

function CSTuhaojinUpLevelReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

--7279 大表情所有信息
SCBigChatFaceAllInfo = SCBigChatFaceAllInfo or BaseClass(BaseProtocolStruct)
function SCBigChatFaceAllInfo:__init()
	self.msg_type = 7279
end

function SCBigChatFaceAllInfo:Decode()
	self.big_face_level = MsgAdapter.ReadShort()
	MsgAdapter.ReadShort()
end

--7280 大表情升级请求
CSBigChatFaceUpLevelReq = CSBigChatFaceUpLevelReq or BaseClass(BaseProtocolStruct)
function CSBigChatFaceUpLevelReq:__init()
	self.msg_type = 7280
end

function CSBigChatFaceUpLevelReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

------------------------龙脉---------------------------
--龙脉升级
CSLongHunUplevel = CSLongHunUplevel or BaseClass(BaseProtocolStruct)
function CSLongHunUplevel:__init()
	self.msg_type = 7262
end

function CSLongHunUplevel:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end

--龙脉信息
SCLongHunInfo = SCLongHunInfo or BaseClass(BaseProtocolStruct)
function SCLongHunInfo:__init()
	self.msg_type = 7263

	self.notify_reason = 0   -- 0龙魂数改变  1龙魂升级
	self.longmai_level = 0
	self.longhun_val = 0
end

function SCLongHunInfo:Decode()
	self.notify_reason = MsgAdapter.ReadInt()
	self.longmai_level = MsgAdapter.ReadInt()
	self.longhun_val = MsgAdapter.ReadInt()
end

--------------------------------------SCFBSceneLogicInfo这个也会下发
--------------------------------------新手副本信息
SCNewPlayerFBInfo = SCNewPlayerFBInfo or BaseClass(BaseProtocolStruct)
function SCNewPlayerFBInfo:__init()
	self.msg_type = 7272
end

function SCNewPlayerFBInfo:Decode()
	local data = {}
	data.cur_wave_index = MsgAdapter.ReadShort()
	data.max_wave_count = MsgAdapter.ReadShort()

	data.fb_finish_timestamp = MsgAdapter.ReadUInt()
	data.kick_out_timestamp = MsgAdapter.ReadUInt()

	data.get_exp = MsgAdapter.ReadLL()
	data.get_coin = MsgAdapter.ReadInt()
	data.fb_type = MsgAdapter.ReadChar()
	data.is_finish = MsgAdapter.ReadChar()
	data.is_pass = MsgAdapter.ReadChar()
	local reward_item_count = MsgAdapter.ReadChar()
	data.kill_monster_count = MsgAdapter.ReadInt()
	local function getItemData()
		local data = {
			item_id = MsgAdapter.ReadUShort(),
			is_bind = MsgAdapter.ReadShort(),
			num = MsgAdapter.ReadInt(),
		}
	end
	data.reward_item_list ={}
	for k = 1, reward_item_count do
		data.reward_item_list[k] = getItemData()
	end
	self.new_player_data = data
end

-----------------------经验池---------------------------
SCExpPoolInfo = SCExpPoolInfo or BaseClass(BaseProtocolStruct)
function SCExpPoolInfo:__init()
	self.msg_type = 7284
	self.total_exp = 0
end

function SCExpPoolInfo:Decode()
	self.total_exp = MsgAdapter.ReadLL()
end

-- 领取经验
CSGetExpFromExpPool = CSGetExpFromExpPool or BaseClass(BaseProtocolStruct)
function CSGetExpFromExpPool:__init()
	self.msg_type = 7285
end

function CSGetExpFromExpPool:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end


CSGetChongJiGiftCount = CSGetChongJiGiftCount or BaseClass(BaseProtocolStruct)
function CSGetChongJiGiftCount:__init()
	self.msg_type = 7288
end

function CSGetChongJiGiftCount:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
end


SCSetChongJiGiftCount = SCSetChongJiGiftCount or BaseClass(BaseProtocolStruct)
function SCSetChongJiGiftCount:__init()
	self.msg_type = 7289
end

function SCSetChongJiGiftCount:Decode()
	self.chongji_limit = {}
	for i = 1, GameEnum.CFG_ITEM_MAX_COUNT do
		local chongji_limit_count = MsgAdapter.ReadInt()
		table.insert(self.chongji_limit, chongji_limit_count)
	end
end

SCConvertCrazyInfo = SCConvertCrazyInfo or BaseClass(BaseProtocolStruct)
function SCConvertCrazyInfo:__init()
	self.msg_type = 7290
	self.count = 0
	self.can_convert_num_list = {}
	self.convert_info_list = {}
end

function SCConvertCrazyInfo:Decode()
	self.count = MsgAdapter.ReadInt()
	self.join_role_level = MsgAdapter.ReadInt()
	self.can_convert_num_list = {}
	for i = 1, GameEnum.RAND_ACTIVITY_CONVERT_CRAZY_CONVERT_ITEM_MAX_NUM do
		self.can_convert_num_list[i] = MsgAdapter.ReadUChar()
	end
	self.convert_info_list = {}
	for i = 1, GameEnum.RAND_ACTIVITY_CONVERT_CRAZY_CONVERT_ITEM_MAX_NUM do
		self.convert_info_list[i] = MsgAdapter.ReadUChar()
	end
end

--被击杀
SCNotifyBeKillInfo = SCNotifyBeKillInfo or BaseClass(BaseProtocolStruct)
function SCNotifyBeKillInfo:__init()
	self.msg_type = 7292
end

function SCNotifyBeKillInfo:Decode()
	self.killer_name = MsgAdapter.ReadStrN(32)
	self.now_timetemp = MsgAdapter.ReadLL()
	self.killer_level = MsgAdapter.ReadInt()
	self.m_prof = MsgAdapter.ReadInt()
	self.plat_type = MsgAdapter.ReadInt()
	self.server_id = MsgAdapter.ReadInt()
	self.uid = MsgAdapter.ReadInt()
	self.type = MsgAdapter.ReadInt()  -- FUHUO_TYPE
	self.param = MsgAdapter.ReadLL()  
end

-- 随机活动 - 累计消费2活动信息
SCRATotalConsumeGold2Info = SCRATotalConsumeGold2Info or BaseClass(BaseProtocolStruct)
function SCRATotalConsumeGold2Info:__init()
	self.msg_type = 7294
	self.consume_gold = 0
	self.fetch_reward_flag = 0
end

function SCRATotalConsumeGold2Info:Decode()
	self.consume_gold = MsgAdapter.ReadInt()
	self.fetch_reward_flag = MsgAdapter.ReadInt()
end

-- 随机活动 - 充值有礼
SCRAChongZhiYouLiInfo = SCRAChongZhiYouLiInfo or BaseClass(BaseProtocolStruct)
function SCRAChongZhiYouLiInfo:__init()
	self.msg_type = 7293

	self.chongzhi_count = 0
	self.chongzhi_jiangli_fetch_reward_flag = 0
end

function SCRAChongZhiYouLiInfo:Decode()
	self.chongzhi_count = MsgAdapter.ReadInt()
	self.chongzhi_jiangli_fetch_reward_flag = MsgAdapter.ReadInt()
end


-- 随机活动 - 充值回馈
SCChargeReward2Info = SCChargeReward2Info or BaseClass(BaseProtocolStruct)
function SCChargeReward2Info:__init()
	self.msg_type = 7295

	self.reward_active_flag = 0 			-- 当前激活的充值奖励
	self.reward_fetch_flag = 0 				-- 当前已获取的充值奖励
	self.history_charge_during_act = 0 		-- 活动期间的累积充值额
end

function SCChargeReward2Info:Decode()
	self.reward_active_flag = MsgAdapter.ReadInt()
	self.reward_fetch_flag = MsgAdapter.ReadInt()
	self.history_charge_during_act = MsgAdapter.ReadInt()
end

--装备铸灵 操作请求 7298
CSEquipZhuLingOperaReq = CSEquipZhuLingOperaReq or BaseClass(BaseProtocolStruct)
function CSEquipZhuLingOperaReq:__init()
	self.msg_type = 7298
end

function CSEquipZhuLingOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
	MsgAdapter.WriteInt(self.param_4)
	MsgAdapter.WriteInt(self.param_5)
	MsgAdapter.WriteInt(self.param_6)
end

-- 成长天书
SCChengZhangTianShuInfo = SCChengZhangTianShuInfo or BaseClass(BaseProtocolStruct)
function SCChengZhangTianShuInfo:__init()
	self.msg_type = 7248
end

function SCChengZhangTianShuInfo:Decode()
	self.fetch_flag_list = MsgAdapter.ReadUInt()
	self.act_flag_list = MsgAdapter.ReadUInt()
end

CSChengZhangTianShuFetchReward = CSChengZhangTianShuFetchReward or BaseClass(BaseProtocolStruct)
function CSChengZhangTianShuFetchReward:__init()
	self.msg_type = 7249

	self.index = 0
end

function CSChengZhangTianShuFetchReward:Encode()
	MsgAdapter.WriteBegin(self.msg_type)

	MsgAdapter.WriteInt(self.index)
end

-- 7281 特惠秒杀请求
RAPanicBuyOperaReq = RAPanicBuyOperaReq or BaseClass(BaseProtocolStruct)
function RAPanicBuyOperaReq:__init()
	self.msg_type = 7281
end

function RAPanicBuyOperaReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
end

-- 7282  特惠秒杀活动信息
RAPanicBuyInfo = RAPanicBuyInfo or BaseClass(BaseProtocolStruct)
function RAPanicBuyInfo:__init()
	self.msg_type = 7282
end

function RAPanicBuyInfo:Decode()
	self.batch = MsgAdapter.ReadInt()
	self.ra_panic_buy_server_num = {}	-- 全服已经购买数量
	for i = 1, GameEnum.COMBINE_SERVER_PANIC_BUY_ITEM_MAX_COUNT do
		self.ra_panic_buy_server_num[i] = MsgAdapter.ReadUChar()
	end

	self.ra_panic_buy_num = {}	-- 个人已经购买数量
	for i = 1, GameEnum.COMBINE_SERVER_PANIC_BUY_ITEM_MAX_COUNT do
		self.ra_panic_buy_num[i] = MsgAdapter.ReadUChar()
	end
	self.level = MsgAdapter.ReadInt()
end

-- 新手副本通关信息
SCNewPlayerFbPassInfo = SCNewPlayerFbPassInfo or BaseClass(BaseProtocolStruct)
function SCNewPlayerFbPassInfo:__init()
	self.msg_type = 7273
end

function SCNewPlayerFbPassInfo:Decode()
	local u_int = 0
	self.new_player_fb_pass_flag = {}
	for i = 1, 4 do
		u_int = MsgAdapter.ReadUInt()
		local tab = bit:d2b_two(u_int)
		for i = 0, 31 do
			table.insert(self.new_player_fb_pass_flag, tab[i])
		end
	end
end

-- 7254 灵宠背包
SCLingChongBagInfo = SCLingChongBagInfo or BaseClass(BaseProtocolStruct)
function SCLingChongBagInfo:__init()
	self.msg_type = 7254
end

function SCLingChongBagInfo:Decode()
	self.count = MsgAdapter.ReadInt()
	self.bag_list = {}

	for i = 0, self.count - 1 do
		self.bag_list[i] = {}
		self.bag_list[i].item_id = MsgAdapter.ReadUShort()
		self.bag_list[i].bind = MsgAdapter.ReadShort()
		self.bag_list[i].index = i
		self.bag_list[i].knapsack_type = KNAPSACK_TYPE.LINGCHONG
	end
end
