---------------------------------
--我要冲战
----------------------------------
RushRechargeViewTwo = RushRechargeViewTwo or BaseClass(SafeBaseView)
function RushRechargeViewTwo:__init()
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
	self:AddViewResource(0, "uis/view/rush_recharge_ui_prefab", "layout_rush_recharge2")
end

function RushRechargeViewTwo:__delete()
end

function RushRechargeViewTwo:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("rush_recharge_timer2") then
		CountDownManager.Instance:RemoveCountDown("rush_recharge_timer2")
	end
	if self.recharge_gridscroll then
		self.recharge_gridscroll:DeleteMe()
		self.recharge_gridscroll = nil
	end
end

function RushRechargeViewTwo:LoadCallBack()
	self.node_list["img_title"].image:LoadSprite(ResPath.GetActivityTitle("UpPower_02"))
	self.node_list.bg_title.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("bg_activity_title"))
  	XUI.ImageSetNativeSize(self.node_list["img_title"])
	self.recharge_two_index = 1
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = config.jishuchongzhan2
	local jishuchongzhan2 = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.JISHUCHONGZHAN_2)

	local data_list = {}
	if jishuchongzhan2[0] then
		table.insert(data_list, jishuchongzhan2[0])
	end
	for i,v in ipairs(jishuchongzhan2) do
		table.insert(data_list, v)
	end
	local ph = self.node_list.ph_rush_list3
	self.recharge_gridscroll = AsyncListView.New(RushRechargeItemTwo,self.node_list.ph_rush_list3)
	self.recharge_gridscroll:SetSelectCallBack(BindTool.Bind1(self.OnClickRechargeItemHandlerTwo, self))
	self.recharge_gridscroll:SetDataList(data_list,0)
	self.recharge_gridscroll:SelectIndex(1)

	XUI.AddClickEventListener(self.node_list.btn_cz_chongzhi, BindTool.Bind1(self.OnClickRecharge, self))
end

function RushRechargeViewTwo:OnClickRechargeItemHandlerTwo(item)
	self.recharge_two_index = item.index
end

function RushRechargeViewTwo:OnClickRecharge()
	local config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local rand_t = config.jishuchongzhan2
	self.jishuchongzhan2 = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.JISHUCHONGZHAN_2)
	local item_data = self.jishuchongzhan2[self.recharge_two_index]
	if not item_data then
		return
	end	
end

function RushRechargeViewTwo:ShowIndexCallBack()
end
	
function RushRechargeViewTwo:OpenCallBack()
end

function RushRechargeViewTwo:CloseCallBack()
end

function RushRechargeViewTwo:OnFlush(param_t, index)
	if CountDownManager.Instance:HasCountDown("rush_recharge_timer2") then
		CountDownManager.Instance:RemoveCountDown("rush_recharge_timer2")
	end
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.JISHUCHONGZHAN_2)
	if nil == activity_info then
		self.node_list.lbl_leave_time.text.text = "0"
		return
	end
	local mul_time = activity_info.next_time - TimeWGCtrl.Instance:GetServerTime()
	if ACTIVITY_STATUS.OPEN == activity_info.status and mul_time > 0 then
		self:UpdateOpenCountDownTime(1, mul_time)
		CountDownManager.Instance:AddCountDown("rush_recharge_timer2", BindTool.Bind1(self.UpdateOpenCountDownTime, self), BindTool.Bind1(self.CompleteOpenCountDownTime, self), activity_info.next_time, nil, 1)
	else
		self:CompleteOpenCountDownTime()
	end
end

function RushRechargeViewTwo:UpdateOpenCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self.node_list.lbl_leave_time.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
	end
end

function RushRechargeViewTwo:CompleteOpenCountDownTime()
	self.node_list.lbl_leave_time.text.text = "0"
end




--------------------------------------------------
--RushRechargeItemTwo
--------------------------------------------------
RushRechargeItemTwo = RushRechargeItemTwo or BaseClass(BaseRender)
function RushRechargeItemTwo:__init()
	
end

function RushRechargeItemTwo:__delete()
	if self.reward_cell then
		self.reward_cell:DeleteMe()
		self.reward_cell = nil
	end
	if self.reward_list then
		for i,v in ipairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
end

function RushRechargeItemTwo:CreateChild()

end
function RushRechargeItemTwo:LoadCallBack()
	self.reward_list = {}
	for i = 1, 4 do
		local cell = ItemCell.New(self.node_list["ph_cell_"..i])
		table.insert(self.reward_list, cell)
	end
end
function RushRechargeItemTwo:OnFlush()
	self.node_list.rich_price_txt.text.text = string.format(Language.Recharge.RushRechargeDec2, self.data.charge_value)
	local temp_list = ItemWGData.Instance:GetItemListInGift(self.data.reward_item.item_id)
	if temp_list and next(temp_list) ~= nil then
		for i, v in ipairs(self.reward_list) do
			v:SetData(temp_list[i])
			v:SetActive(temp_list[i] ~= nil)
		end
	end
end
function RushRechargeItemTwo:OnSelectChange(is_select)
	self.node_list.select_image:SetActive(is_select)
end
