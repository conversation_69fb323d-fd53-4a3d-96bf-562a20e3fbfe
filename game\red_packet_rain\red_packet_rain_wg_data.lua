-- 跨服红包天降
RedPacketRainWGData = RedPacketRainWGData or BaseClass()
function RedPacketRainWGData:__init()
	if RedPacketRainWGData.Instance then
		error("[RedPacketRainWGData] Attempt to create singleton twice!")
		return
	end

	RedPacketRainWGData.Instance = self

    self.redpaper_falling_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_falling_auto")

	self.grade_cfg = ListToMap(self.redpaper_falling_cfg.grade, "item_id")
	self.barrage_cfg = ListToMap(self.redpaper_falling_cfg.barrage_cfg, "barrage_type")
	
	self.redpaper_random_table_cfg = self.redpaper_falling_cfg.rumor_random_table
	

	self.scene_id = self:GetOtherCfg("scene_id")
	self.scene_x = self:GetOtherCfg("scene_x")
	self.scene_y = self:GetOtherCfg("scene_y")
	self.scene_range = self:GetOtherCfg("scene_range1")
end

function RedPacketRainWGData:__delete()
    RedPacketRainWGData.Instance = nil
end

function RedPacketRainWGData:GetOtherCfg(key)
	if self.redpaper_falling_cfg and key then
		return self.redpaper_falling_cfg.other[1][key]
	end
end

function RedPacketRainWGData:GetGradeCfg()
	return self.grade_cfg
end

function RedPacketRainWGData:GetGradeCfgByItemId(item_id)
	return self.grade_cfg[item_id]
end

-- 随机感谢语
function RedPacketRainWGData:RandomBarrage()
	local randow = math.random(0, #self.barrage_cfg)
	return self.barrage_cfg[randow].desc
end

-- 是否是增加红包雨轮次的道具id
function RedPacketRainWGData:IsConsumeId(item_id)
	local add_round_item_id = self:GetOtherCfg("add_round_item_id")
	if tonumber(add_round_item_id) == item_id then
		return true
	else
		return false
	end
end

-- 是否是开启红包雨的道具id
function RedPacketRainWGData:IsAddActivityConsumeId(item_id)
	if not self.grade_cfg then
		return false
	end
	if self.grade_cfg[item_id] then
		return true
	end

	return false
end

-- 获取活动id
function RedPacketRainWGData:GetActivityId()
	return self:GetOtherCfg("activity_id")
end

-- 是否在红包雨范围
function RedPacketRainWGData:IsInRedPacketArea()
	local scene_id = Scene.Instance:GetSceneId()
	if scene_id == self.scene_id then
		local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
		local dis = GameMath.GetDistance(self.scene_x, self.scene_y, role_x, role_y, false)
		-- 做个+1的误差
		if dis <= (self.scene_range) * (self.scene_range) then
			return true
		end
	end
	return false
end

-- 是否在红包雨场景
function RedPacketRainWGData:IsInRedPacketScene()
	local scene_id = Scene.Instance:GetSceneId()
	if scene_id == self.scene_id then
		return true
	end
	return false
end


function RedPacketRainWGData:SetAcitivityInfo(protocol)
	self.activity_info = protocol.activity_info

	if self.activity_info.role_info.role_id > 0 then
		AvatarManager.Instance:SetAvatarKey(self.activity_info.role_info.role_id, self.activity_info.role_info.avatar_key_big, self.activity_info.role_info.avatar_key_small)
	end
end

function RedPacketRainWGData:GetAcitivityInfo()
	return self.activity_info 
end

function RedPacketRainWGData:IsOpenItemAcitivity()
	if self.activity_info and self.activity_info.is_role_item_open == 1 then
		return true
	end
	return false
end

function RedPacketRainWGData:SetRoleInfo(protocol)
	self.role_info = protocol.role_info
end

function RedPacketRainWGData:GetRoleInfo()
	return self.role_info 
end

function RedPacketRainWGData:SetRewardInfo(protocol)
	self.reward_info = protocol.reward_info
end

function RedPacketRainWGData:GetRewardInfo()
	return self.reward_info 
end


function RedPacketRainWGData:GetRedPakcetPos()
	return self.scene_x,self.scene_y
end

-- 是否可使用加轮次道具
function RedPacketRainWGData:IsCanUseItem()
	local add_max = self:GetOtherCfg("add_max")
	if self.role_info and self.role_info["add_round"] >= add_max then
		return false
	end

	local add_round_item_id = self:GetOtherCfg("add_round_item_id")
	local add_round_item_num = self:GetOtherCfg("add_round_item_num")
	local item_count = ItemWGData.Instance:GetItemNumInBagById(add_round_item_id)

	if item_count >= add_round_item_num then
		return true
	end

	return false
end

function RedPacketRainWGData:GetABarrageMessage()
	local speed = self:GetOtherCfg("desc_show_speed")
	return {desc_content = (self.redpaper_random_table_cfg[math.random(1, #self.redpaper_random_table_cfg)] or {}).conect or "",
			need_random_color = false,
			color_data_list = ITEM_COLOR_DARK,
			text_color = COLOR3B.WHITE,
			move_speed = speed,
			is_shield_bg = true
		}
end