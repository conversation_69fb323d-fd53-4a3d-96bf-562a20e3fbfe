MingWenConvertTips = MingWenConvertTips or BaseClass(SafeBaseView)

function MingWenConvertTips:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(634, 500)})
	self:AddViewResource(0, "uis/view/ming_wen_ui_prefab", "layout_mingwen_convert")
end

function MingWenConvertTips:__delete()

end

function MingWenConvertTips:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end	
end

function MingWenConvertTips:OpenCallBack()
	self.amount = 1
	self.max_num = 0
end

function MingWenConvertTips:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_cell"])
        self.item_cell:SetIsShowTips(true)
    end

	XUI.AddClickEventListener(self.node_list["btn_OK"], BindTool.Bind(self.OnClickOKBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_minus"], BindTool.Bind(self.OnClickMinus, self))
	XUI.AddClickEventListener(self.node_list["btn_plus"], BindTool.Bind(self.OnClickPlus, self))
	self.node_list.num_slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self))
end

function MingWenConvertTips:ShowIndexCallBack()
	self.node_list.num_slider.slider.value = 1
end

function MingWenConvertTips:SetDataAndOpen(convery_data)
    self.data = {}
	self.data.convery_index = convery_data.index
    self.data.item_id = convery_data.item_id
    self.data.price = convery_data.price

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function MingWenConvertTips:OnFlush()
	if IsEmptyTable(self.data) then 
        return 
    end

    self.item_cell:SetData({item_id = self.data.item_id})
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    self.node_list.name.text.text = item_cfg and item_cfg.name or ""

    local sui_pian = MingWenWGData.Instance:GetMingWenSuiPian()
    local one_need = self.data.price or 1
    local max_value = math.floor(sui_pian / one_need) > 1 and math.floor(sui_pian / one_need) or 1
    max_value = max_value > 100 and 100 or max_value
    self.max_num = max_value
    self.node_list.num_slider.slider.maxValue = max_value

    self:FlushConsume(self.amount)
end

function MingWenConvertTips:FlushConsume(value)
	if IsEmptyTable(self.data) then 
        return 
    end

    local sui_pian = MingWenWGData.Instance:GetMingWenSuiPian()
    local one_need = self.data.price or 1

	self.node_list.need_num.text.text = value * one_need
	self.node_list.has_num.text.text = sui_pian
	self.node_list.num.text.text = value
	self.num_value = value
end

function MingWenConvertTips:OnClickOKBtn()
	if IsEmptyTable(self.data) then
        return
    end

    local sui_pian = MingWenWGData.Instance:GetMingWenSuiPian()
	local num = math.floor(self.num_value)
    local one_need = self.data.price or 1
    if sui_pian < num * one_need then
        TipWGCtrl.Instance:ShowSystemMsg(Language.MingWenView.StuffNotEnough)
        self:Close()
        return
    end

	TreasureHuntWGCtrl.Instance:DoShopOperation(TreasureHuntWGData.CHESTSHOP_CONVERT_REQ.CHESTSHOP_CONVERT_BUY, self.data.convery_index, num)
    self:Close()
end

function MingWenConvertTips:OnSliderValueChange(value)
	self.amount = value
	self:FlushConsume(value)
end

function MingWenConvertTips:OnClickMinus()
	self.amount = math.max(self.amount - 1, 1)
	self:FlushConsume(self.amount)
	self.node_list.num_slider.slider.value = self.amount
end

function MingWenConvertTips:OnClickPlus()
    if self.amount + 1 > self.max_num then
        TipWGCtrl.Instance:ShowSystemMsg(Language.MingWenView.ConvertTip1)
        return
    end

	self.amount = math.min(self.amount + 1, self.max_num)
	self:FlushConsume(self.amount)
	self.node_list.num_slider.slider.value = self.amount
end