require("game/kf_attribute_stone_rank/kf_attribute_stone_rank_view")
require("game/kf_attribute_stone_rank/kf_attribute_stone_rank_wg_data")
require("game/kf_attribute_stone_rank/kf_attribute_stone_rank_score_reward_view")

KFAttributeStoneRankWGCtrl = KFAttributeStoneRankWGCtrl or BaseClass(BaseWGCtrl)
function KFAttributeStoneRankWGCtrl:__init()
	if KFAttributeStoneRankWGCtrl.Instance then
		ErrorLog("[KFAttributeStoneRankWGCtrl] Attemp to create a singleton twice !")
	end

	KFAttributeStoneRankWGCtrl.Instance = self
    self.data = KFAttributeStoneRankWGData.New()
	self.view = KFAttributeStoneRankView.New(GuideModuleName.KFAttributeStoneRankView)
	self.score_reward_view = KFAttributeStoneRankScoreRewardView.New()

	self:RegisterAllEvents()
    self:RegisterAllProtocols()
end

function KFAttributeStoneRankWGCtrl:__delete()
	if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.score_reward_view:DeleteMe()
	self.score_reward_view = nil

    KFAttributeStoneRankWGCtrl.Instance = nil
end

function KFAttributeStoneRankWGCtrl:RegisterAllEvents()
	self.mainui_create_complete = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self))

	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function KFAttributeStoneRankWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCrossAttributeStoneRankClient)
	self:RegisterProtocol(SCCrossAttributeStoneRankInfo, "OnSCCrossAttributeStoneRankInfo")
	self:RegisterProtocol(SCCrossYesterdayRankInfo, "OnSCCrossYesterdayRankInfo")
	self:RegisterProtocol(SCCrossAttributeStoneRankPersonScoreReward, "OnSCCrossAttributeStoneRankPersonScoreReward")
end

-- 事件相关
function KFAttributeStoneRankWGCtrl:MainuiOpenCreateCallBack()
	self:CheckActivityIsOpen()
end

function KFAttributeStoneRankWGCtrl:OnDayChange()
	self:CheckActivityIsOpen()
end

-- 事件相关end

-- 协议相关
function KFAttributeStoneRankWGCtrl:SendKFRankReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossAttributeStoneRankClient)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function KFAttributeStoneRankWGCtrl:OnSCCrossAttributeStoneRankInfo(protocol)
	self.data:SetKFAttributeStoneRankData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.KFAttributeStoneRankView, nil, "rank_update")
end

function KFAttributeStoneRankWGCtrl:OnSCCrossYesterdayRankInfo(protocol)
	self.data:SetKFAttributeStoneLastRankData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.KFAttributeStoneRankView, nil, "last_rank_update")
end

function KFAttributeStoneRankWGCtrl:OnSCCrossAttributeStoneRankPersonScoreReward(protocol)
	self.data:SetKFAttributeStoneRankScoreRewardeFlag(protocol)
	if self.score_reward_view:IsOpen() then
		self.score_reward_view:Flush()
	end

	ViewManager.Instance:FlushView(GuideModuleName.KFAttributeStoneRankView, nil, "score_reward")
	RemindManager.Instance:Fire(RemindName.KFAttributeStoneRank)
end

-- 协议相关end

function KFAttributeStoneRankWGCtrl:OpenScoreRewardView()
	if not self.score_reward_view:IsOpen() then
		self.score_reward_view:Open()
	end
end

function KFAttributeStoneRankWGCtrl:CheckActivityIsOpen()
	local min_open_day = self.data:GetOtherCfg().minimum_open_day
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if open_day >= min_open_day then
		local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ATTRIBUTE_STONE_RANK)
		if not act_is_open then
			local server_tiem = TimeWGCtrl.Instance:GetServerTime()
			local now_day_end_time = TimeWGCtrl.Instance:NowDayTimeEnd(server_tiem)
			ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.KF_ATTRIBUTE_STONE_RANK, ACTIVITY_STATUS.OPEN, now_day_end_time)
		end
	end
end