require("game/crossserver/kuafu_3v3/kf_3v3_view")
require("game/crossserver/kuafu_3v3/kf_3v3_wg_data")
require("game/crossserver/kuafu_3v3/kf_3v3_3v3info_view")
require("game/crossserver/kuafu_3v3/kf_3v3_zhandui_view")
require("game/crossserver/kuafu_3v3/kf_3v3_season_reward_view")
require("game/crossserver/kuafu_3v3/kf_3v3_zhandui_rank_view")
require("game/crossserver/kuafu_3v3/kf_3v3_matching_view")
require("game/crossserver/kuafu_3v3/kf_3v3_prepare_logic_view")
require("game/crossserver/kuafu_3v3/kf_3v3_logic_view")
require("game/crossserver/kuafu_3v3/kf_3v3_kill_info_view")
require("game/crossserver/kuafu_3v3/kf_3v3_start_down")
require("game/crossserver/kuafu_3v3/kf_3v3_jiesuan")
require("game/crossserver/kuafu_3v3/kf_3v3_duanwei_change")
require("game/crossserver/kuafu_3v3/kf_3v3_fetch_reward_view")
require("game/crossserver/kuafu_3v3/kf_3v3_ready_view")
require("game/crossserver/kuafu_3v3/kf_3v3_act_end_view")

KF3V3WGCtrl = KF3V3WGCtrl or BaseClass(BaseWGCtrl)

function KF3V3WGCtrl:__init()
    if KF3V3WGCtrl.Instance then
		error("[KF3V3WGCtrl]:Attempt to create singleton twice!")
	end
	KF3V3WGCtrl.Instance = self
	self:InitView()
    self:RegisterAllProtocals()
end

function KF3V3WGCtrl:__delete()
	self:DeleteView()
	if self.invite_start_cross_alert then
		self.invite_start_cross_alert:DeleteMe()
		self.invite_start_cross_alert = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end
end

function KF3V3WGCtrl:InitView()
	self.data = KF3V3WGData.New()
	self.view = KF3v3View.New(GuideModuleName.KF3v3View)
	--self.season_reward_view = Kf3V3SeasonRewardView.New() 	--赛季奖励
	--self.zhandui_rank_view = Kf3V3ZhanDuiRankView.New()  	--战队榜
	self.match_view = KF3V3MatchView.New(GuideModuleName.KF3V3MatchView)
	self.prepare_logic_view = KF3V3PrepareLogicView.New(GuideModuleName.KF3V3PrepareLogicView)	--3v3准备场景任务栏信息界面
	self.logic_view = KF3V3LogicView.New(GuideModuleName.KF3V3LogicView)
	self.kill_info_view = KF3V3KillInfoView.New(GuideModuleName.KF3V3KillInfoView)
	self.start_down = KF3V3StartDown.New()
	self.jiesuan_view = KF3V3JieSuanView.New()
	self.duanwei_change = KF3V3DuanWeiChangeiew.New()
	self.fetch_reward_view = KF3V3FetchRewardView.New()
	self.ready_view = KF3V3ReadyView.New()
	self.act_end_view = KF3V3ActEndView.New()
	self.match_time_run_quest = nil

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

end
function KF3V3WGCtrl:DeleteView()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	-- if self.season_reward_view then
	-- 	self.season_reward_view:DeleteMe()
	-- 	self.season_reward_view = nil
	-- end
	-- if self.zhandui_rank_view then
	-- 	self.zhandui_rank_view:DeleteMe()
	-- 	self.zhandui_rank_view = nil
	-- end
	if self.match_view then
		self.match_view:DeleteMe()
		self.match_view = nil
	end
	if self.prepare_logic_view then
		self.prepare_logic_view:DeleteMe()
		self.prepare_logic_view = nil
	end
	if self.logic_view then
		self.logic_view:DeleteMe()
		self.logic_view = nil
	end
	if self.kill_info_view then
		self.kill_info_view:DeleteMe()
		self.kill_info_view = nil
	end
	if self.start_down then
		self.start_down:DeleteMe()
		self.start_down = nil
	end
	if self.jiesuan_view then
		self.jiesuan_view:DeleteMe()
		self.jiesuan_view = nil
	end
	if self.duanwei_change then
		self.duanwei_change:DeleteMe()
		self.duanwei_change = nil
	end
	if self.fetch_reward_view then
		self.fetch_reward_view:DeleteMe()
		self.fetch_reward_view = nil
	end
	if self.ready_view then
		self.ready_view:DeleteMe()
		self.ready_view = nil
	end
	if self.act_end_view then
		self.act_end_view:DeleteMe()
		self.act_end_view = nil
	end

	if self.leader_enter_alert then
		self.leader_enter_alert:DeleteMe()
		self.leader_enter_alert = nil
	end
	if self.member_enter_alert then
		self.member_enter_alert:DeleteMe()
		self.member_enter_alert = nil
	end
	KF3V3WGCtrl.Instance = nil
	self:DeleteMatchTimer()
end
function KF3V3WGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSCross3V3Opera)													-- 跨服3V3请求 8591
	self:RegisterProtocol(SCCross3v3MatchingInfo, "OnSCCross3v3MatchingInfo") 				-- 跨服3V3匹配信息 8590
	self:RegisterProtocol(SCCross3V3PKSceneInfo, "OnSCCross3V3PKSceneInfo")					-- 跨服3V3场景信息 8592
	self:RegisterProtocol(SCCross3V3PKKillInfo, "OnSCCross3V3PKKillInfo")					-- 跨服3V3PK击杀信息 8593
	self:RegisterProtocol(SCCross3V3PKResult, "OnSCCross3V3PKResult")						-- 跨服3V3PK结算信息 8594
	self:RegisterProtocol(SCCross3V3RankInfo, "OnSCCross3V3RankInfo")						-- 跨服3V3排名（跨服战队排名） 8595
	self:RegisterProtocol(SCCross3V3StandbySceneInfo, "OnSCCross3V3StandbySceneInfo")		-- 跨服3V3备战场景信息 8596

	self:RegisterProtocol(SCBecomeGhost, "OnSCBecomeGhost") 								-- 成为鬼魂 7400
end


-------------------------------------协议操作start----------------------------------------------------------------------

-- 跨服3V3匹配信息
function KF3V3WGCtrl:OnSCCross3v3MatchingInfo(protocol)
	self.data:SetMatcthInfo(protocol)
	if protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.StartMatching then
		self:OpenMatchView()
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		MainuiWGCtrl.Instance:ShowMainuiMenu(false)
		--注册,刷新匹配时间
		if not self.match_time_run_quest then
			self.match_time_run_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateMatchTime, self), 1)
		end
		KF3V3WGData.Instance:SetStartMatchTime()
	elseif protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.CancelMatching then
		self:CloseMatchView(true)
	elseif protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.TimeOut then
		self:CloseMatchView(true)
	elseif protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.MatchingSucc then
		self:CloseMatchView(true)
		self:ReqEnterPKScene()
	elseif protocol.notify_reason == Cross3V3MatchingInfoNotifyReason.ClientReady then

	end
	self:UpdateMatchTime()
	GlobalEventSystem:Fire(OtherEventType.KF3V3MatchInfoChange, protocol.notify_reason)
end
--跨服3V3场景信息
function KF3V3WGCtrl:OnSCCross3V3PKSceneInfo(protocol)
	self.data:SetSceneInfo(protocol)
	if protocol.pk_state == EnumKF3V3PKStateType.WaitForClientReady then
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			MainuiWGCtrl.Instance.view:SetFlyTaskIsHideMainUi(true, true)
		end)
		KF3V3WGCtrl.Instance:OpenReadyView()
	elseif protocol.pk_state == EnumKF3V3PKStateType.StartCountDown then --开始倒计时
		if protocol.next_state_time > 0 then
			self:CloseReadyView()
			--self:DoStartDown(protocol.next_state_time, function()
			--end)
			UiInstanceMgr.Instance:DoFBStartDown(protocol.next_state_time,function()
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end)
		end
	elseif protocol.pk_state == EnumKF3V3PKStateType.Fight then		 --战斗
		 MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			 MainuiWGCtrl.Instance.view:SetFlyTaskIsHideMainUi(false, true)
			 self.logic_view:SetHideUiIsOn(false)
		end)
	elseif protocol.pk_state == EnumKF3V3PKStateType.End then		 --结算面板
	end
	GlobalEventSystem:Fire(OtherEventType.KF3V3SceneInfoChange)
end
--跨服3V3PK结算信息
function KF3V3WGCtrl:OnSCCross3V3PKResult(protocol)
	self.data:SetJieSuanInfo(protocol)
	--隐藏血条
	MainuiWGCtrl.Instance:SetMianUITargetState(false)
    if Scene.Instance:GetSceneType() == SceneType.Kf_PVP then
        self:SetTimeScale(0.3, 3, function()
            self:OpenJieSuanView(protocol)
        end)
    end
end

function KF3V3WGCtrl:SetTimeScale(scale, time, callback)
    UnityEngine.Time.timeScale = scale
    GlobalTimerQuest:AddDelayTimer(function()
        UnityEngine.Time.timeScale = 1
		if callback then
			callback()
		end
    end, time)
end

--跨服3V3PK击杀信息
function KF3V3WGCtrl:OnSCCross3V3PKKillInfo(protocol)
	--self.data:SetKillInfo(protocol)
	local t = {}
	t.broadcast_type = -1
	local killer_uid = protocol.killer.role_id
	if protocol.killer.is_robert == 1 then
		killer_uid = 0
	end
	t.killer_uid = killer_uid
	t.killer_name = protocol.killer.name
    t.killer_sex = protocol.killer.sex
    t.killer_prof = protocol.killer.prof
	t.killer_is_red_side =  protocol.killer.side == 0 and 1 or 2
	local target_uid = protocol.dead.role_id
	if protocol.dead.is_robert == 1 then
		target_uid = 0
	end
	t.target_uid = target_uid
	t.target_name = protocol.dead.name
    t.target_sex = protocol.dead.sex
    t.target_prof = protocol.dead.prof
	t.target_is_red_side = protocol.dead.side == 1 and 2 or 1
	t.param = -10000
	TipWGCtrl.Instance:AddZCRuneMsgCenter(t)
end

--跨服3V3战队排行信息
function KF3V3WGCtrl:OnSCCross3V3RankInfo(protocol)
	self.data:SetRankInfo(protocol)
	--ZhanDuiWGCtrl.Instance:OpenZhanDuiRankView()

	-- if self.zhandui_rank_view:IsOpen() then
	-- 	self.zhandui_rank_view:Flush(self.zhandui_rank_view:GetShowIndex())
	-- end
	if protocol.rank_count > 0 and protocol.is_last_season == 0 then
		if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
			for i = 1, COMMON_CONSTS.CROSS3V3_PLAYER_COUNT_PER_SIDE do
				if protocol.rank_list[1].origin_uid_list[i] then
					BrowseWGCtrl.Instance:BrowRoelInfo(protocol.rank_list[1].origin_uid_list[i], function(info)
						self:QueryRoleInfoCallBack(info, i)
					end)
				end
			end
		end
	end
end

--跨服3V3备战场景信息
function KF3V3WGCtrl:OnSCCross3V3StandbySceneInfo(protocol)
	self.data:SetPrepareSceneInfo(protocol)
	GlobalEventSystem:Fire(OtherEventType.KF3V3PrepareSceneInfoChange)
end
--成为鬼魂
function KF3V3WGCtrl:OnSCBecomeGhost(protocol)
	GuajiWGCtrl.Instance:StopGuaji()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	self:ChangeGhost()
	--显示遮罩
	-- MainuiWGCtrl.Instance:SetTransparentMaskActive(true)
	--隐藏血条
	MainuiWGCtrl.Instance:SetMianUITargetState(false)
	local role_list = Scene.Instance:GetRoleList()
	for i, v in pairs(role_list) do
		if not v:IsMainRole() then
			v:SetIsPerformer(true)
		end
	end
end

function KF3V3WGCtrl:ChangeGhost()
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:SetAttr("ghost", EnumGhostTpye.IsGhost)
	end
end
function KF3V3WGCtrl:CancelChangeGhost()
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:SetAttr("ghost", EnumGhostTpye.NotGhost)
	end
end

function KF3V3WGCtrl:QueryRoleInfoCallBack(info, index)
	self.data:SetStatueInfo(info, index)
	self:UpdateAllStatue()
end

function KF3V3WGCtrl:UpdateAllStatue()
	local statue = Scene.Instance:GetObjListByType(SceneObjType.CityOwnerStatue)
	for k,v in pairs(statue) do
		v:UpdateStatue()
	end
end

-- 跨服3V3请求
function KF3V3WGCtrl:SendCross3V3Opera(opera_type, param1, param2)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCross3V3Opera)
	send_protocol.opera_type = opera_type
	send_protocol.param1 = param1 or 0
	send_protocol.param2 = param2 or 0
	send_protocol:EncodeAndSend()
end
--请求开始匹配
function KF3V3WGCtrl:ReqStartMatch()
	self:SendCross3V3Opera(Cross3V3OperaType.StartMatch)
end

--删除,重置匹配时间
function KF3V3WGCtrl:DeleteMatchTimer()
	if self.match_time_run_quest then
        GlobalTimerQuest:CancelQuest(self.match_time_run_quest)      
		self.match_time_run_quest = nil
	end		
end

--请求取消匹配
function KF3V3WGCtrl:ReqCancelMatch()
	self:SendCross3V3Opera(Cross3V3OperaType.CancelMatch)
	self:DeleteMatchTimer()
	self.prepare_logic_view:RefreshBtnTime(true)
end
--刷新匹配时间
function KF3V3WGCtrl:UpdateMatchTime()
	--匹配按钮
	self.prepare_logic_view:RefreshBtnTime()
	--匹配界面
	if self.match_view:IsOpen() and self.match_view:IsLoaded() then
		self.match_view:RefreshState()
	end
end

--请求进入赛房
function KF3V3WGCtrl:ReqEnterPKScene()
	self:SendCross3V3Opera(Cross3V3OperaType.EnterPKRoom)
end

--加载完毕准备  已准备
function KF3V3WGCtrl:SendReady()
	self:SendCross3V3Opera(Cross3V3OperaType.ClientReady)
end
-------------------------------------协议操作end----------------------------------------------------------------------












-------------------------------------界面操作start----------------------------------------------------------------------
--打开3v3主面板
function KF3V3WGCtrl:Open3V3View(index)
	self.view:SetOpenData(index)
	self.view:Open()
end

--创建菜单按钮
function KF3V3WGCtrl:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
	self.view:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
end

--打开页签规则tips
function KF3V3WGCtrl:OpenTabRuleTip(index)
	local data = {}
    data.title = Language.TabRuleTip.Kf3V3_Title
    data.tab_group = Language.TabRuleTip.TabGroup_KF3V3
    data.content_list = Language.TabRuleTip.Kf3V3_Content_list
    TipWGCtrl.Instance:OpenTabRuleTip(data, index)
end

--打开匹配界面
function KF3V3WGCtrl:OpenMatchView()
	if not self.match_view:IsOpen() then
		self.match_view:Open()
	end
end

--关闭匹配界面
function KF3V3WGCtrl:CloseMatchView(is_clear)
	if is_clear then
		self:DeleteMatchTimer()
	end
	if self.match_view:IsOpen() then
		self.match_view:Close()
		if is_clear then
			self.prepare_logic_view:RefreshBtnTime(true)
		end
	end
end

--打开3v3准备场景任务栏信息界面
function KF3V3WGCtrl:OpenPrepareLogicView()
	if not self.prepare_logic_view:IsOpen() then
		self.prepare_logic_view:Open()
	end
end
--关闭3v3准备场景任务栏信息界面
function KF3V3WGCtrl:ClosePrepareLogicView()
	self.prepare_logic_view:Close()
end

--刷新3v3准备场景任务栏信息界面
function KF3V3WGCtrl:FlushPrepareLogicView()
	self.prepare_logic_view:Flush()
end


--打开3v3场景信息界面
function KF3V3WGCtrl:OpenLogicView()
	if not self.logic_view:IsOpen() then
		self.logic_view:Open()
	end
end
--关闭3v33v3场景信息界面
function KF3V3WGCtrl:CloseLogicView()
	self.logic_view:Close()
end
--刷新3v33v3场景信息界面
function KF3V3WGCtrl:FlushLogicView()
	self.logic_view:Flush()
end
--刷新3v33v3场景信息界面
function KF3V3WGCtrl:SetLogicViewRootNodeActive(is_active)
	self.logic_view:SetRootNodeActive(is_active)
end

function KF3V3WGCtrl:GetLogicView()
	return self.logic_view
end

--打开3v3击杀界面
function KF3V3WGCtrl:OpenKillInfoView(kill_data)
	self.kill_info_view:SetData(kill_data)
	self.kill_info_view:Open()
end
--关闭3v3击杀界面
function KF3V3WGCtrl:CloseKillInfoView()
	self.kill_info_view:Close()
end

--刷新3v3击杀界面
function KF3V3WGCtrl:FlushKillInfoView()
	self.kill_info_view:Flush()
end



--打开3v3结算界面
function KF3V3WGCtrl:OpenJieSuanView(jiesuan_data)
	if not self.jiesuan_view:IsOpen() then
		self.jiesuan_view:SetData(jiesuan_data)
		self.jiesuan_view:Open()
	end
end
--关闭3v3结算界面
function KF3V3WGCtrl:CloseJieSuanView()
	self.jiesuan_view:Close()
end

--刷新3v3结算界面
function KF3V3WGCtrl:FlushJieSuanView()
	self.jiesuan_view:Flush()
end


--打开3v3段位改变界面
function KF3V3WGCtrl:OpenDuanWeiChangeView(data)
	if not self.duanwei_change:IsOpen() then
		self.duanwei_change:SetData(data)
		self.duanwei_change:Open()
	end
end
--关闭3v3段位改变界面
function KF3V3WGCtrl:CloseDuanWeiChangeView()
	self.duanwei_change:Close()
end

--刷新3v3段位改变界面
function KF3V3WGCtrl:FlushDuanWeiChangeView()
	self.duanwei_change:Flush()
end

--打开阶段奖励界面
function KF3V3WGCtrl:OpenFetchView()
	if not self.fetch_reward_view:IsOpen() then
		self.fetch_reward_view:Open()
	end
end
--关闭阶段奖励界面
function KF3V3WGCtrl:CloseFetchView()
	self.fetch_reward_view:Close()
end

--刷新阶段奖励界面
function KF3V3WGCtrl:FlushFetchView()
	self.fetch_reward_view:Flush()
end

--打开准备界面
function KF3V3WGCtrl:OpenReadyView()
	if not self.ready_view:IsOpen() then
		self.ready_view:Open()
	end
end
--关闭准备界面
function KF3V3WGCtrl:CloseReadyView()
	self.ready_view:Close()
end

--刷新准备界面
function KF3V3WGCtrl:FlushReadyView()
	self.ready_view:Flush()
end

function KF3V3WGCtrl:CheckOpenActEndView(new_scene_type, data)
	if not data then
		return
	end

	local is_act_close = ActivityWGData.Instance:GetActivityIsClose(data.act_type)
	if new_scene_type == SceneType.Common and is_act_close then
		self:OpenActEndView(data)
	end
end
--打开活动结束界面
function KF3V3WGCtrl:OpenActEndView(data)
	self.act_end_view:SetData(data)
end
--关闭准备界面
function KF3V3WGCtrl:CloseActEndView()
	self.act_end_view:Close()
end

--打开3v3倒计时界面
function KF3V3WGCtrl:DoStartDown(time_stamp, finish_callback)
	self.start_down:DoStartDown(time_stamp, finish_callback)
end
--关闭3v3倒计时界面
function KF3V3WGCtrl:CloseStartDown()
	if self.start_down:IsOpen() then
		self.start_down:Close()
	end
end

-------------------------------------界面操作end----------------------------------------------------------------------


--进入准备场景
--进入之前做一些操作
function KF3V3WGCtrl:EnterPrepareScene()
	--已经在该场景直接返回
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Kf_PvP_Prepare then
		return
	end

	--功能未开启
	local is_open_fun = FunOpen.Instance:GetFunIsOpened(FunName.KFPVP)
	if not is_open_fun then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.FunOpenTip)
		return
	end

	--活动是否开启
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
	if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
        if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.StandbyTimeTips)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.OpenTimeTips)
        end
		return
	end

	--玩家自己是否有战队
	local is_in_zhandui = ZhanDuiWGData.Instance:GetIsInZhanDui()
	if not is_in_zhandui then
		--没战队的操作
		SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.PleaseJoinZhanDui)
		ZhanDuiWGCtrl.Instance:OpenJoinView()
		return
	end

	local change_index , member_list = NewTeamWGData.Instance:GetTeamMemberList()
	local not_my_zhandui_id_list = {}
	if #member_list > 1 then
		for k,v in ipairs(change_index) do
			if v and member_list[v] then
				local is_my_zhandui = ZhanDuiWGData.Instance:GetTargetIsMyZhanDui(member_list[v].role_id)
				if not is_my_zhandui then
					table.insert(not_my_zhandui_id_list, member_list[v].role_id)
				end
			end
		end
	end
	if #not_my_zhandui_id_list > 0 then
		if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
			TipWGCtrl.Instance:OpenAlertTips(Language.KuafuPVP.LeaderEnterPrepareSceneTip, function ()
				for i, v in ipairs(not_my_zhandui_id_list) do
					SocietyWGCtrl.Instance:SendKickOutOfTeam(v)
				end
				KF3V3WGCtrl.Instance:RealEnterPrepareScene()
			end)
		else
			TipWGCtrl.Instance:OpenAlertTips(Language.KuafuPVP.MemberEnterPrepareSceneTip, function ()
				NewTeamWGCtrl.Instance:ExitTeam()
				KF3V3WGCtrl.Instance:RealEnterPrepareScene()
			end)
		end
		return

	else
		self:RealEnterPrepareScene()
	end
end

function KF3V3WGCtrl:RealEnterPrepareScene()
	Field1v1WGCtrl.Instance:EnterFieldPrepareScene(ACTIVITY_TYPE.KF_PVP)
end

--处理接收到邀请消息
function KF3V3WGCtrl:HandleReceiveInviteMsg(protocol)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE_START_CROSS_3V3, 1, function ()
		self:OpenReceiveInviteView(protocol)
		return true
	end)

	self:OpenReceiveInviteView(protocol)
end

function KF3V3WGCtrl:OpenReceiveInviteView(protocol)
	if not self.invite_start_cross_alert then
		self.invite_start_cross_alert = Alert.New()
	end

	self.invite_start_cross_alert:SetLableString(string.format(Language.CrossInvite.Invite3v3, protocol.inviter_name))
	self.invite_start_cross_alert:SetOkString(Language.CrossInvite.BtnGoTo)
	self.invite_start_cross_alert:SetCancelString(Language.CrossInvite.BtnCancel)
	self.invite_start_cross_alert:SetCancelFunc(function()
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE_START_CROSS_3V3, 0)
		--拒绝前往, 1 是拒绝
		CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteAck,EnumInviteStartCrossReason.Zhandui3V3Match, protocol.inviter_uid , 1)
	end)
	self.invite_start_cross_alert:SetOkFunc(function()

		CrossServerWGCtrl.Instance:SendInviteStartCross(EnumInviteStartCrossOperType.InviteAck,EnumInviteStartCrossReason.Zhandui3V3Match, protocol.inviter_uid , 0)
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.INVITE_START_CROSS_3V3, 0)
			GlobalTimerQuest:AddDelayTimer(function()
				if Scene.Instance:GetSceneType() ~= SceneType.Kf_PvP_Prepare then
					--请求队友信息，协议回来的时候进入准备场景，并寻路到队长位置
					Scene.SendReqTeamMemberPos(EnumReqTeamMemberPosReason.OtherSceneEnterKF3V3Prepare)
				else
					--请求队友信息，协议回来的时候寻路到队长位置
					Scene.SendReqTeamMemberPos(EnumReqTeamMemberPosReason.KF3V3Prepare)
				end
			end, 1)
	end)
	self.invite_start_cross_alert:Open()
end
--邀请消息结果返回
function KF3V3WGCtrl:HandleInviteResultMsg(protocol)
	if protocol.is_reject == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.CrossInvite.Result3v3, protocol.inviter_name))
	end
end

--处理请求队员信息返回 EnumReqTeamMemberPosReason.KF3V3Prepare
function KF3V3WGCtrl:HandleReqMemberMsg(protocol)
	local leader = SocietyWGData.Instance:GetTeamLeader()
	for i = 1, #protocol.team_member_list do
		if leader.orgin_role_id == protocol.team_member_list[i].orgin_role_id then
			local pos_x = protocol.team_member_list[i].pos_x
			local pos_y = protocol.team_member_list[i].pos_y
			GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), pos_x, pos_y, 3)
		end
	end
end

--处理请求队员信息返回 EnumReqTeamMemberPosReason.OtherSceneEnterKF3V3Prepare
function KF3V3WGCtrl:HandleOtherSceneEnterKF3V3Prepare(protocol)
	local leader = SocietyWGData.Instance:GetTeamLeader()
	for i = 1, #protocol.team_member_list do
		if leader.orgin_role_id == protocol.team_member_list[i].orgin_role_id then
			local pos_x = protocol.team_member_list[i].pos_x
			local pos_y = protocol.team_member_list[i].pos_y
			local scene_id = protocol.team_member_list[i].scene_id
			if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
				GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y, 3)
			else
				KF3V3WGData.Instance:CacheLeaderPos(scene_id, pos_x, pos_y)
			end
		end
	end
end

function KF3V3WGCtrl:GoToNpc()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Kf_PvP_Prepare then
		return
	end
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
	if not activity_info or activity_info.status ~= ACTIVITY_STATUS.OPEN then
        if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.StandbyTimeTips)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.OpenTimeTips)
        end
		return
	end
	if activity_info.status == ACTIVITY_STATUS.OPEN then
		local cfg = KuafuPVPWGData.Instance:GetOtherCfg()
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
		MoveCache.SetEndType(MoveEndType.ClickNpc)
		MoveCache.param1 = cfg.npc_3v3
		local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
		GuajiWGCtrl.Instance:MoveToPos(cfg.npc_scene_3v3, cfg.npc_pos_x_3v3, cfg.npc_pos_z_3v3, 1 , nil, scene_key)--,
	end
end

function KF3V3WGCtrl:GoToNpcJoin()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Kf_PvP_Prepare then
		return
	end
	GuajiWGCtrl.Instance:StopGuaji()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	local cfg = KuafuPVPWGData.Instance:GetOtherCfg()
	MoveCache.SetEndType(MoveEndType.ClickNpc)
	MoveCache.param1 = cfg.npc_3v3
	local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
	GuajiWGCtrl.Instance:MoveToPos(cfg.npc_scene_3v3, cfg.npc_pos_x_3v3, cfg.npc_pos_z_3v3, 1, nil, scene_key)
end

--判断别人功能是否开启3v3
function KF3V3WGCtrl:CheckOpenFun(level)
	local cfg = FunOpen.Instance:GetFunByName(FunName.KFPVP)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- if open_day < COMMON_CONSTS.CROSS3V3_OPEN_DAY then
	-- 	return false
	-- end
	if cfg ~= nil and cfg.trigger_type == GuideTriggerType.LevelUp then
		if cfg.trigger_param ~= "" and level < cfg.trigger_param then
			return false
		end
	end
	return true
end

function KF3V3WGCtrl:FlushInfoView()
	self.view:Flush()
end

function KF3V3WGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KF_PVP and status == ACTIVITY_STATUS.OPEN then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if activity_info and activity_info.next_time and activity_info.next_time > server_time then
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time)
		end
	end
end
