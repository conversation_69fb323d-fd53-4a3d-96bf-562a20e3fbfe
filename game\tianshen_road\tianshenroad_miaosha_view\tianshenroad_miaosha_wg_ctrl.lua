
require("game/tianshen_road/tianshenroad_miaosha_view/tianshenroad_miaosha_wg_data")

TianShenRoadMiaoShaWGCtrl = TianShenRoadMiaoShaWGCtrl or BaseClass(BaseWGCtrl)

function TianShenRoadMiaoShaWGCtrl:__init()
	if TianShenRoadMiaoShaWGCtrl.Instance then
		ErrorLog("[TianShenRoadMiaoShaWGCtrl] Attemp to create a singleton twice !")
	end
	TianShenRoadMiaoShaWGCtrl.Instance = self
	self.tianshenroad_miaosha_data = TianShenRoadMiaoshaWGData.New()

	self:RegisterAllProtocols()

	self.refresh_remind_flag_list = {}

end

function TianShenRoadMiaoShaWGCtrl:__delete()
	TianShenRoadMiaoShaWGCtrl.Instance = nil

	if self.tianshenroad_miaosha_data ~= nil then
		self.tianshenroad_miaosha_data:DeleteMe()
		self.tianshenroad_miaosha_data = nil
	end

end

function TianShenRoadMiaoShaWGCtrl:RegisterAllProtocols()
	-----------------------------------限时秒杀协议------------------------------------
	self:RegisterProtocol(SCRATianShenTimedSpikeInfo, "OnSCRATianShenTimedSpikeInfo")--15550
	self:RegisterProtocol(SCRATianShenTimedSpikeItemUpdate, "OnSCRATianShenTimedSpikeItemUpdate")--15551
	self:RegisterProtocol(SCRATianShenTimedSpikeQuotaUpdate, "OnSCRATianShenTimedSpikeQuotaUpdate")--15552
	self:RegisterProtocol(SCRATianShenTimedSpikeBuyTimesInfo, "OnSCRATianShenTimedSpikeBuyTimesInfo")--15559
	--self:RegisterProtocol(SCTimedSpikeNewTagInfo, "OnSCTimedSpikeNewTagInfo")--10420（所有的秒杀活动）限时秒杀新字标记
	-----------------------------------------------------------------------------------
end

-----------------------------------------------------------限时秒杀----------------------------------------------------------------

--15550// 天神之路-限时秒杀信息
function TianShenRoadMiaoShaWGCtrl:OnSCRATianShenTimedSpikeInfo(protocol)
	-- print_error("  15550  >>>>>>>>OnSCRATianShenTimedSpikeInfo",protocol)
	self.tianshenroad_miaosha_data:SetSCHeFuTimedSpikeInfo(protocol)

	TianshenRoadWGCtrl.Instance:FlushView(TabIndex.tianshenroad_xianshi_miaosha)
	RemindManager.Instance:Fire(RemindName.TianShenRoad_MiaoSha)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

--15551// 天神之路-限时秒杀商品更新
function TianShenRoadMiaoShaWGCtrl:OnSCRATianShenTimedSpikeItemUpdate(protocol)
	-- print_error("  15551  >>>>>>>>OnSCRATianShenTimedSpikeItemUpdate",protocol)
	self.tianshenroad_miaosha_data:SetSCHeFuTimedSpikeItemUpdate(protocol)

	TianshenRoadWGCtrl.Instance:FlushView(TabIndex.tianshenroad_xianshi_miaosha)
	RemindManager.Instance:Fire(RemindName.TianShenRoad_MiaoSha)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

--15552// 天神之路-限时秒杀额度奖励更新
function TianShenRoadMiaoShaWGCtrl:OnSCRATianShenTimedSpikeQuotaUpdate(protocol)
	-- print_error("  15552  >>>>>>>>OnSCRATianShenTimedSpikeQuotaUpdate",protocol)
	self.tianshenroad_miaosha_data:SetSCHeFuTimedSpikeQuotaUpdate(protocol)
	TianshenRoadWGCtrl.Instance:FlushView(TabIndex.tianshenroad_xianshi_miaosha)
	RemindManager.Instance:Fire(RemindName.TianShenRoad_MiaoSha)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end

--15559,// 天神之路-限时秒杀购买次数更新
function TianShenRoadMiaoShaWGCtrl:OnSCRATianShenTimedSpikeBuyTimesInfo(protocol)
	-- print_error("  15559  >>>>>>>>OnSCRATianShenTimedSpikeBuyTimesInfo",protocol)
	self.tianshenroad_miaosha_data:SetSCHeFuTimedSpikeBuyTimesInfo(protocol)

	TianshenRoadWGCtrl.Instance:FlushView(TabIndex.tianshenroad_xianshi_miaosha)
	RemindManager.Instance:Fire(RemindName.TianShenRoad_MiaoSha)
	RemindManager.Instance:Fire(RemindName.TianShenRoad)
end
