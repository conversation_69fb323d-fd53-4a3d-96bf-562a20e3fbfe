---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/<PERSON><PERSON>ua)
--- Created by 123.
--- DateTime: 2019/10/17 20:41
---
CopperLogicView = CopperLogicView or BaseClass(SafeBaseView)

function CopperLogicView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_copper_info")
	self.last_add_value = 0
	self.active_close = false
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
end

function CopperLogicView:ReleaseCallBack()
	self.is_out_fb = nil
	if self.copper_reward_list then
        self.copper_reward_list:DeleteMe()
        self.copper_reward_list = nil
    end
	self.last_add_value = 0
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest) 
		self.timer_quest = nil
	end

	if self.star_view then
		self.star_view:DeleteMe()
		self.star_view = nil
	end
end

function CopperLogicView:LoadCallBack()
	self.copper_reward_list = AsyncListView.New(CopperRewardCell, self.node_list["tongbi_reward_list"])
end

function CopperLogicView:InitCallBack()
    if Scene.Instance:GetSceneType() == SceneType.COPPER_FB then
        local mainui_ctrl = MainuiWGCtrl.Instance
        local parent = mainui_ctrl:GetTaskOtherContent()
        self.node_list["layout_copper_info_root"].transform:SetParent(parent.gameObject.transform)
        self.node_list["layout_copper_info_root"].transform.localPosition = Vector3.zero
        self.node_list["layout_copper_info_root"].transform.localScale = Vector3.one
        mainui_ctrl:SetTaskContents(false)
        mainui_ctrl:SetOtherContents(true)
		mainui_ctrl:SetFBNameState(true, Scene.Instance:GetSceneName())
        mainui_ctrl:SetTeamBtnState(false)
        FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.FBCT_TONGBIBEN)

        if self.is_out_fb then
            self.node_list["layout_copper_info_root"]:SetActive(false)
        end
        self.is_out_fb = nil
    end
end

function CopperLogicView:ShowIndexCallBack()
    local init_callback = function ()
        self:InitCallBack()
    end
    local mainuictrl = MainuiWGCtrl.Instance
    mainuictrl:AddInitCallBack(nil,init_callback)
	self:Flush()
end

function CopperLogicView:OpenCallBack()
	if self.node_list["layout_copper_info_root"] and self:IsLoadedIndex(0) then
        self.node_list["layout_copper_info_root"]:SetActive(true)
    end
end

function CopperLogicView:CloseCallBack()
	self.is_out_fb = true
    if self.node_list["layout_copper_info_root"] then
        self.node_list["layout_copper_info_root"]:SetActive(false)
        self.node_list["layout_copper_info_root"].transform:SetParent(self.root_node_transform, false)
    end
    self.data = nil
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetFBNameState(false)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
end


function CopperLogicView:OnFlush()
	local item_list = CopperFbWGData.Instance:GetRewardItemList(3)
	self.copper_reward_list:SetDataList(item_list)
	local scene_info = CopperFbWGData.Instance:GetCopperScenceInfo()
	if not scene_info or IsEmptyTable(scene_info) then
		return
	end

	self.node_list.lbl_boshu.text.text = string.format("%s/%d", scene_info.cur_wave, scene_info.total_wave)

	--local last_value = self.last_add_value
	local all_num = 0
	for k ,v in pairs(scene_info.item_list) do
		if v.num > 0 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_cfg and item_cfg.param1 then
				all_num = all_num + item_cfg.param1*v.num
			end
		end
	end
	self.last_add_value = all_num
	--self.node_list.get_gold_text.text:DoNumberTo(last_value, scene_info.item_total_count or 0, 0.5, function ()
	--end)
	local value, postfix_name = CommonDataManager.ConverMoneyBar(self.last_add_value)
	if postfix_name == "" then
		self.node_list.get_gold_text.text.text = (string.format("%.0f", value)) .. postfix_name
	else
		self.node_list.get_gold_text.text.text = (value) .. postfix_name
	end

	self:OnFlushStar()
	self.node_list.fb_desc_tips.text.text = Language.FuBenPanel.FubenPerBossTips3
end

function CopperLogicView:OnFlushStar()
	if nil == self.star_view then
		self.star_view =  FuBenStarClock.New(self.node_list.layout_fuben)
	end
    local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
	local time_list = CopperFbWGData.Instance:GetStarTimeList()
	local data = {}
	if time_list then
		data = {time3 = time_list[3], time2 = time_list[2], time1 = time_list[1], time0 = time_list[0],per0 = other_cfg.zero_star_Prob, per1 = other_cfg.one_star_Prob, per2 = other_cfg.two_star_Prob, per3 = other_cfg.three_star_Prob, str = Language.Boss.StarAniStr,}
	end
	self.star_view:SetData(data)
	self.star_view:Flush()
    
end