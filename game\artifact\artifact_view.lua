ArtifactView = ArtifactView or BaseClass(SafeBaseView)

local ARTIFACT_RENDER_HEIGHT_EXPAND = 142
local ARTIFACT_RENDER_HEIGHT_SHRINK = 94

function ArtifactView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self.is_safe_area_adapter = true
	self.default_index = TabIndex.artifact_uplevel
	self.remind_tab = {{RemindName.ArtifactUpLevel}, {RemindName.ArtifactUpStar}, {RemindName.ArtifactAffection}, {RemindName.ArtifactBattle}}
	
	local main_tab = {TabIndex.artifact_uplevel, TabIndex.artifact_upstar, TabIndex.artifact_affection}
	self:AddViewResource(TabIndex.artifact_battle, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(main_tab, "uis/view/artifact_ui_prefab", "layout_artifact_bg_panel")
	self:AddViewResource(main_tab, "uis/view/artifact_ui_prefab", "layout_artifact_main")
	self:AddViewResource(TabIndex.artifact_uplevel, "uis/view/artifact_ui_prefab", "layout_artifact_up_level")
	self:AddViewResource(TabIndex.artifact_upstar, "uis/view/artifact_ui_prefab", "layout_artifact_up_star")
	self:AddViewResource(TabIndex.artifact_affection, "uis/view/artifact_ui_prefab", "layout_artifact_affection")
	self:AddViewResource(TabIndex.artifact_battle, "uis/view/artifact_ui_prefab", "layout_artifact_battle")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function ArtifactView:__delete()

end

function ArtifactView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.artifact_show_list then
		self.artifact_show_list:DeleteMe()
		self.artifact_show_list = nil
	end

	--[[
	if self.artifact_show_model then
		self.artifact_show_model:DeleteMe()
		self.artifact_show_model = nil
	end
	]]

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	self.select_artifact_seq = nil
	self.select_artifact_index = nil

	self.model_id = nil
	self.to_ui_param = nil

	self.old_act_state = nil
	self:CancelAutoLevelTimer()
	self:StopGiftCountdown()

	self:UpLevelReleaseCallBack()
	self:UpStarReleaseCallBack()
	self:AffectionReleaseCallBack()
	self:BattleReleaseCallBack()
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ArtifactView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function ArtifactView:OpenCallBack()

end

function ArtifactView:CloseCallBack()
	self:UpLevelCloseCallBack()
	self:UpStarCloseCallBack()
	self:AffectionCloseCallBack()
	self:BattleCloseCallBack()
end

function ArtifactView:ChangeCellSize(data_index)
	if (self.select_artifact_index - 1) == data_index then
		return ARTIFACT_RENDER_HEIGHT_EXPAND
	else
		return ARTIFACT_RENDER_HEIGHT_SHRINK
	end
end

function ArtifactView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Artifact.ViewName
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.FlushTabbarActive, self))
		self.tabbar:Init(Language.Artifact.TabGrop, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
	
	self.select_artifact_seq = nil
	self.select_artifact_index = nil
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ArtifactView, self.get_guide_ui_event)
end

function ArtifactView:MainLoadIndexCallBack()
	if not self.artifact_show_list then
		self.artifact_show_list = AsyncListView.New(ArtifactCell, self.node_list["artifact_show_list"])
		self.artifact_show_list:SetSelectCallBack(BindTool.Bind(self.OnArtifactCellHandler, self))
		self.artifact_show_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize, self))
	end

	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list["model_display"])
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	XUI.AddClickEventListener(self.node_list["btn_gift"], BindTool.Bind(self.OnClickGiftBtn, self))			--礼包
end

function ArtifactView:LoadIndexCallBack(index)
	if index == TabIndex.artifact_uplevel or index == TabIndex.artifact_upstar then
		self:MainLoadIndexCallBack()
	end

	if index == TabIndex.artifact_uplevel then
		self:UpLevelLoadIndexCallBack()
	elseif index == TabIndex.artifact_upstar then
		self:UpStarLoadIndexCallBack()
	elseif index == TabIndex.artifact_affection then
		self:AffectionLoadCallBack()
	elseif index == TabIndex.artifact_battle then
		self:BattleLoadIndexCallBack()
	end
end

function ArtifactView:MainShowIndexCallBack()
	self:PlayMainAni()
	self:PlayLeftListAni()
	-- local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq or 0)
	-- local bundle, asset = ResPath.GetRawImagesJPG(artifact_cfg.bg)
	-- if self.node_list.RawImage_bg then
	-- 	self.node_list["RawImage_bg"].raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list["RawImage_bg"].raw_image:SetNativeSize()
	-- 	end)
	-- end
end

function ArtifactView:ShowIndexCallBack(index)
	if index == TabIndex.artifact_uplevel or index == TabIndex.artifact_upstar or index == TabIndex.artifact_affection then
		self:MainShowIndexCallBack()
	end

	if index == TabIndex.artifact_uplevel then
		self:UpLevelShowIndexCallBack()
	elseif index == TabIndex.artifact_upstar then
		self:UpStarShowIndexCallBack()
	elseif index == TabIndex.artifact_affection then
		self:AffectionShowIndexCallBack()
	elseif index == TabIndex.artifact_battle then
		self:BattleShowIndexCallBack()
	end
end

function ArtifactView:OnFlush(param_t, index)
	if index == TabIndex.artifact_uplevel or index == TabIndex.artifact_upstar or index == TabIndex.artifact_affection then
		self:MainOnFlush(param_t)
	end

	if index == TabIndex.artifact_uplevel then
		self:UpLevelOnFlush(param_t)
	elseif index == TabIndex.artifact_upstar then
		self:UpStarOnFlush(param_t)
	elseif index == TabIndex.artifact_affection then
		self:AffectionOnFlush(param_t)
	elseif index == TabIndex.artifact_battle then
		self:BattleOnFlush(param_t)
	end

	-- for k, v in pairs(param_t) do
	-- 	if k == "all" then
	-- 		if index == TabIndex.artifact_uplevel then
	-- 			self:UpLevelOnFlush(param_t)
	-- 		elseif index == TabIndex.artifact_upstar then
	-- 			self:UpStarOnFlush(param_t)
	-- 		elseif index == TabIndex.artifact_battle then
	-- 			self:BattleOnFlush(param_t)
	-- 		end

	-- 		if index == TabIndex.artifact_uplevel or index == TabIndex.artifact_upstar then
	-- 			self:MainOnFlush(param_t)
	-- 		end

	-- 	elseif k == "battle_info" then
	-- 		if index == TabIndex.artifact_uplevel or index == TabIndex.artifact_upstar then
	-- 			self:FlushLeftList()
	-- 		elseif index == TabIndex.artifact_battle then
	-- 			self:BattleOnFlush()
	-- 		end
	-- 	end
	-- end
end

function ArtifactView:MainOnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" or k == "protocol_flush" then
			self.to_ui_param = v.to_ui_param
			self:FlushLeftList(self.to_ui_param ~= nil)
			self:FlushShowModel()
			self:FlushMidInfo()
			self:FlushGiftBtn()
			self:FlushTabbarActive()
		elseif k == "battle_info" then
			self:FlushLeftList()
		elseif k == "flush_remind" then
			self:FlushLeftList()
		end
	end
end

function ArtifactView:FlushLeftList(flush_jump)
	local artifact_list_info = ArtifactWGData.Instance:GetArtifactCfgList()
	if flush_jump then
		local click_cell_index = 1
		for k, v in ipairs(artifact_list_info) do
			if self.to_ui_param then
				if v.seq == tonumber(self.to_ui_param) then
					click_cell_index = k
					self.to_ui_param = nil
					break
				end
			elseif ArtifactWGData.Instance:ArtifactRemindBySeq(v.seq) then
				click_cell_index = k
				break
			end
		end
		self.select_artifact_index = click_cell_index
		if not IsEmptyTable(artifact_list_info) and self.artifact_show_list then
			self.artifact_show_list:SetDataList(artifact_list_info)
			self.artifact_show_list:JumpToIndex(click_cell_index, 4)
		end
	else
		self.select_artifact_index = self.select_artifact_index or 1
		if not IsEmptyTable(artifact_list_info) and self.artifact_show_list then
			self.artifact_show_list:SetDataList(artifact_list_info)
		end
	end
end

function ArtifactView:OnArtifactCellHandler(item, cell_index)
	if nil == item or nil == item.data then
		return
	end

	if self.select_artifact_seq and self.select_artifact_seq == item.data.seq then
		return
	end

	self.select_artifact_seq = item.data.seq
	self.select_artifact_index = cell_index

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	if cur_artifact_data.level <= 0 then
		self:ChangeToIndex(TabIndex.artifact_uplevel)
	end

	self:FlushShowModel()
	self:FlushMidInfo()
	self:FlushGiftBtn()
	self:FlushTabbarActive()

	self.artifact_show_list:ResetItemSize()

	if self.show_index == TabIndex.artifact_uplevel and self:IsLoadedIndex(TabIndex.artifact_uplevel) then
		self:UpLevelOnClickArtifactCell(cell)
	elseif self.show_index == TabIndex.artifact_upstar and self:IsLoadedIndex(TabIndex.artifact_upstar) then
		self:UpStarOnClickArtifactCell(cell)
	elseif self.show_index == TabIndex.artifact_affection and self:IsLoadedIndex(TabIndex.artifact_affection) then
		self:OnArtifactAffectionCellHandler(cell, cell_index)
	end
end

function ArtifactView:CheckFlushCondition()
	if not self.select_artifact_seq or self.select_artifact_seq < 0 then
		return false
	end

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)
	if IsEmptyTable(artifact_cfg) then
		return false
	end

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	if IsEmptyTable(cur_artifact_data) then
		return false
	end

	return true
end

function ArtifactView:FlushMidInfo()
	if not self:CheckFlushCondition() then
		return
	end
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)

	-- local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)
	-- local bundle, asset = ResPath.GetArtifactImg("a3_sx_mzt" .. artifact_cfg.battle_type)
	-- self.node_list["icon_type"].image:LoadSprite(bundle, asset)

	local cur_awake_name = ArtifactWGData.Instance:GetArtifactAwakeName(self.select_artifact_seq, cur_artifact_data.awake_level)
	self.node_list["txt_artifact_name"].text.text = cur_awake_name
end

--刷新裝備模型
function ArtifactView:FlushShowModel()
	if not self:CheckFlushCondition() then
		return
	end

	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	local is_act = cur_artifact_data.level > 0
	if not self.model_id or self.model_id ~= artifact_cfg.model_id or self.old_act_state ~= is_act then
		self.node_list["model_display"]:SetActive(is_act)
		self.node_list["image_outline"]:SetActive(not is_act)
		if is_act then
			--local bundle, asset = ResPath.GetMingQiModel(artifact_cfg.model_id)
			local bundle, asset = ResPath.GetShuangXiuShowUI(artifact_cfg.model_id)
			-- self.artifact_show_model:SetMainAsset(bundle, asset)
			-- self.artifact_show_model:FixToOrthographic(self.root_node_transform)
			-- self.artifact_show_model:PlayJianZhenAction()
			local display_data = {}
			display_data.bundle_name = bundle
			display_data.asset_name = asset
			display_data.render_type = OARenderType.Prefab
			self.model_display:SetData(display_data)
		end
		-- 背景图
		local bg_name = is_act and artifact_cfg.bg or ("a3_sx_mbg" .. self.select_artifact_seq)
		local bundle, asset = ResPath.GetRawImagesJPG(bg_name)
		if self.node_list.RawImage_bg then
			self.node_list["RawImage_bg"].raw_image:LoadSprite(bundle, asset, function()
				self.node_list["RawImage_bg"].raw_image:SetNativeSize()
			end)
		end
		self.model_id = artifact_cfg.model_id
		self.old_act_state = is_act
	end
end

function ArtifactView:FlushTabbarActive()
	if not self:CheckFlushCondition() then
		return
	end
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	local is_active = cur_artifact_data.level > 0

	local affection_is_open = FunOpen.Instance:GetFunIsOpened(FunName.ArtifactAffection)
	self.tabbar:SetToggleVisible(TabIndex.artifact_upstar, is_active)
	self.tabbar:SetToggleVisible(TabIndex.artifact_affection, affection_is_open and is_active)
	self.tabbar:SetToggleVisible(TabIndex.artifact_battle, is_active)
end

function ArtifactView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	if ui_name == "vertical_cell_guide_03" or ui_name == "vertical_cell_guide_02" then
		TipWGCtrl.Instance:CloseNowGoodsTips()
		local fun = function()
			if ui_param ~= nil and ui_param ~= "" then
				self:ChangeToIndex(ui_param)
			end
		end

		return self.node_list[ui_name], fun
	elseif ui_name == "ss_battle_item_1" then
		local fun = function()
			local apply_list = ArtifactWGData.Instance:GetArtifactBattleInfo()
			if apply_list and apply_list[1] then
				local data = apply_list[1]
				ArtifactWGData.Instance:GetSetSelectedPosSeq(data.pos_seq)
				ArtifactWGCtrl.Instance:OpenBattleSelectView()
			end
		end

		return self.node_list[ui_name], fun
	end

	return self.node_list[ui_name]
end
------------------
-- 礼包按钮
function ArtifactView:FlushGiftBtn()
	if not self:CheckFlushCondition() then
		return
	end
	self:StopGiftCountdown()
	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.select_artifact_seq)
	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self.node_list["img_gift_remind"]:SetActive(false)
	self.node_list["txt_gift_desc"].text.text = ""
	if cur_artifact_data.level == 0 then
		self.node_list["txt_gift_time"].text.text = Language.Artifact.GiftPreview
	elseif cur_artifact_data.gift_reward_flag == 1 or cur_artifact_data.next_reward_time <= server_time then
		self.node_list["txt_gift_time"].text.text = Language.Artifact.GiftCanGet
		self.node_list["img_gift_remind"]:SetActive(true)
	else
		self:OnGiftCountdownUpdate(server_time, cur_artifact_data.next_reward_time)
		-- 倒计时
		CountDownManager.Instance:AddCountDown("artifact_gift_countdown",
			BindTool.Bind(self.OnGiftCountdownUpdate, self),
			BindTool.Bind(self.OnGiftCountdownComplete, self),
			cur_artifact_data.next_reward_time, nil, 1)
	end
end

function ArtifactView:StopGiftCountdown()
	if CountDownManager.Instance:HasCountDown("artifact_gift_countdown") then
		CountDownManager.Instance:RemoveCountDown("artifact_gift_countdown")
	end
end

function ArtifactView:OnGiftCountdownUpdate(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list["txt_gift_desc"].text.text = Language.Artifact.GiftTimeStr
		self.node_list["txt_gift_time"].text.text = TimeUtil.FormatSecondDHM9(valid_time)
	end
end

function ArtifactView:OnGiftCountdownComplete()
	self:FlushGiftBtn()
	self:FlushLeftList()
end

function ArtifactView:OnClickGiftBtn()
	if not self:CheckFlushCondition() then
		return
	end
	local artifact_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.select_artifact_seq)
	local can_get_gift = ArtifactWGData.Instance:GetCanGetGiftBySeq(self.select_artifact_seq)
	if not can_get_gift then
		local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = artifact_cfg.reward_item_show,
			other_tips = Language.Artifact.GiftTips,
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
	else
		ArtifactWGCtrl.Instance:SendCSArtifactOperateRequest(ARTIFACT_OPERATE_TYPE.GET_GIFT_REWARD, self.select_artifact_seq)
	end
end
--------------------

-- 使用特效
function ArtifactView:PlayUseEffect(effect_name)
	TipWGCtrl.Instance:ShowEffect({
		effect_type = effect_name,
		is_success = true,
		pos = Vector2(0, 0),
		parent_node = self.node_list["effect_pos"]
	})
end

function ArtifactView:PlayMainAni()
	if self.node_list["main_anim_root"] then
		self.node_list["main_anim_root"].animation_player:Play("MainMiddleAnim")
	end
end

function ArtifactView:PlayLeftListAni()
	if self.node_list["main_left_root"] then
		self.node_list["main_left_root"].animation_player:Play("MainLeftListAnim")
	end
end

---------------------------------------
-- 左侧列表Item ArtifactCell
---------------------------------------
ArtifactCell = ArtifactCell or BaseClass(BaseRender)
function ArtifactCell:__init()
	self.is_selected = nil
end

function ArtifactCell:LoadCallBack()

end

function ArtifactCell:ReleaseCallBack()
	self.is_selected = nil
	--self:CleanTimer()
	self:ClearTweener()
end

function ArtifactCell:OnFlush()
	if not self.data then
		return
	end

	--self.item_cell:SetData({ item_id = self.data.item_id })
	local remind = ArtifactWGData.Instance:ArtifactRemindBySeq(self.data.seq)
	self.node_list.remind:SetActive(remind)

	local bundle, asset = ResPath.GetRawImagesPNG(self.data.icon_name)
	self.node_list["img_icon"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["img_icon"].raw_image:SetNativeSize()
	end)

	bundle, asset = ResPath.GetArtifactImg("a3_sx_mzt_icon" .. self.data.battle_type)
	self.node_list["icon_type"].image:LoadSprite(bundle, asset)
	bundle, asset = ResPath.GetArtifactImg("a3_sx_btn_xz" .. self.data.seq)
	self.node_list["type_bg"].image:LoadSprite(bundle, asset)
	self.node_list["txt_type"].text.text = self.data.battle_type_name

	local cur_artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(self.data.seq)
	local is_act = cur_artifact_data.level > 0
	self.node_list["stars_list"]:SetActive(is_act)
	if is_act then
		local star_res_list = GetTwenTyStarImgResByStar(cur_artifact_data.star_level)
		for i = 1, GameEnum.ITEM_MAX_STAR do
			self.node_list["star_" .. i].image:LoadSprite(ResPath.GetCommonImages(star_res_list[i]))
		end
	end

	local cur_awake_name = ArtifactWGData.Instance:GetArtifactAwakeName(self.data.seq, cur_artifact_data.awake_level)
	self.node_list["name_text"].text.text = cur_awake_name

	local is_battle = ArtifactWGData.Instance:GetArtifactBattleBySeq(self.data.seq)
	self.node_list["icon_battle"]:SetActive(is_battle)

	--礼包
	local can_get_gift = ArtifactWGData.Instance:GetCanGetGiftBySeq(self.data.seq)
	self.node_list["icon_gift"]:SetActive(can_get_gift)
end

function ArtifactCell:OnSelectChange(is_select)
	if not self.data then
		return
	end
	self.node_list["select_hl"]:SetActive(is_select)
	self.node_list["select_normal"]:SetActive(not is_select)
	self.node_list["icon_type"]:SetActive(is_select)
	self.node_list["type_bg"]:SetActive(is_select)

	if self.is_selected ~= is_select then
		self.target_height = is_select and ARTIFACT_RENDER_HEIGHT_EXPAND or ARTIFACT_RENDER_HEIGHT_SHRINK
		self:SetItemScale()
		self.is_selected = is_select
	end
end

function ArtifactCell:SetItemScale()
	self:ClearTweener()
	local rect = self.node_list["rect_size"].rect
	self.size_change_tweener = rect:DOSizeDelta(Vector2(292, self.target_height), 0.4)
	self.size_change_tweener:OnUpdate(function()
		self.view.layout_element.minHeight = rect.sizeDelta.y
	end)

    -- self:CleanTimer()
    -- self.size_change_timer = CountDown.Instance:AddCountDown(1, 0.02,
    --     -- 回调方法
    --     function(elapse_time, total_time)
    --         self.view.layout_element.minHeight = Mathf.Lerp(self.view.layout_element.minHeight, self.target_height, elapse_time)
    --     end,
    --     -- 倒计时完成回调方法
    --     function()
    --         self.view.layout_element.minHeight = self.target_height
    --     end
    -- )
end

-- function ArtifactCell:CleanTimer()
--     if self.size_change_timer and CountDown.Instance:HasCountDown(self.size_change_timer) then
--         CountDown.Instance:RemoveCountDown(self.size_change_timer)
--         self.size_change_timer = nil
--     end
-- end

function ArtifactCell:ClearTweener()
    if self.size_change_tweener then
		self.size_change_tweener:Kill()
		self.size_change_timer = nil
    end
end