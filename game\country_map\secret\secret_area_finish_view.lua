SecretAreaEndView = SecretAreaEndView or BaseClass(SafeBaseView)

function SecretAreaEndView:__init()
	self.view_style = ViewStyle.Half
	self.default_index = 0
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true)
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/country_map_ui/secret_area_prefab", "layout_secret_area_end")
end

function SecretAreaEndView:__delete()

end

function SecretAreaEndView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("finish_confirm") then
		CountDownManager.Instance:RemoveCountDown("finish_confirm")
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function SecretAreaEndView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_out_end"],BindTool.Bind(self.ClickCloseBtn,self))
	self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])

	if CountDownManager.Instance:HasCountDown("finish_confirm") then
		CountDownManager.Instance:RemoveCountDown("finish_confirm")
	end
	CountDownManager.Instance:AddCountDown("finish_confirm", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CloseViewHandler, self),  TimeWGCtrl.Instance:GetServerTime() + 15 , nil, 1)
end

function SecretAreaEndView:ShowIndexCallBack(index)

end

function SecretAreaEndView:OnFlush(param_list, index)
	local finish_info = SecretAreaWGData.Instance:GetSecretAreaFinishInfo()
	if IsEmptyTable(finish_info) then
		return
	end
	if finish_info.is_win == 1 then-- 成功
		self.node_list.title_img.image:LoadSprite(ResPath.GetCommonPanel("a3_gxhd_lbl_sl"))
		self.node_list.title_text.text.text = Language.CountrySecret.WinStr
	else
		self.node_list.title_img.image:LoadSprite(ResPath.GetCommonPanel("a3_gxhd_lbl_pm"))
		self.node_list.title_text.text.text = Language.CountrySecret.LoseStr
	end
	local value, postfix_name = CommonDataManager.ConverExpFBNum(finish_info.exp)
	if postfix_name == "" then
		self.node_list.exp_fb_exp.text.text = (string.format("%.0f", value)) .. postfix_name
	else
		self.node_list.exp_fb_exp.text.text = (value) .. postfix_name
	end
	self.reward_cell_list = {}
	if not IsEmptyTable(finish_info.reward_item_list) then
		self.reward_list:SetDataList(finish_info.reward_item_list)
	end
end

function SecretAreaEndView:CloseCallBack()
	if Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_KF_COUNTRY_SECRET_AREA then
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

function SecretAreaEndView:ClickCloseBtn()
	self:Close()
end

function SecretAreaEndView:UpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	self.node_list.lbl_close.text.text = string.format(Language.CountrySecret.EndTimeCountDown,temp_seconds)
end

function SecretAreaEndView:CloseViewHandler()
	self:Close()
end