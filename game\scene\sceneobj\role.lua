require("game/scene/role_part_shield_handle")
require("game/scene/role_part_effect_shield_handle")
require("game/scene/role_effect_shield_handle")

Role = Role or BaseClass(Character)
local SWIMMING_WATER_HIGHT = {
    SWIM_MOVE = 1.58,
    SWIM_IDLE = 1.7,
}

local MITSURUGI_HIGHT = 4.6     -- 御剑在可行走区域最小高度

Role.Count = 0

local Role_Dynamic_Actor_Config = {
    ["camera_shakes"] = {
        {
            CameraShakeBtnName = "sixiang_shake",
            eventName = "sixiang_shake",
            numberOfShakes = 4,
            distance = 0.3,
            speed = 50.0,
            delay = 0.3,
            decay = 0.0,
        },

        {
            CameraShakeBtnName = "break_effect",
            eventName = "break_effect",
            numberOfShakes = 5,
            distance = 0.2,
            speed = 50.0,
            delay = 0,
            decay = 0.0,
        },
    },
}

--初始化后先调用的SetAttr ,然后调用了进入场景的方法OnEnterScene
function Role:__init(vo)
    Role.Count = Role.Count + 1
    self.obj_type = SceneObjType.Role
    self.followui_class = RoleFollow
    self.draw_obj:SetObjType(self.obj_type)
    self.shield_obj_type = ShieldObjType.Role

    self.shadow_level = 1 

    -- 设置品质
    self.part_quality_handles = {}
    self.part_effect_quality_handles = {}
    self:InitPartQualityRules()

    -- 模型id相关
    self.role_res_id = 0            -- 主角资源id
    self.special_res_id = 0         -- 特殊形象资源id
    self.gundam_weapon_id = 0       -- 高达武器资源id（用于区分技能匹配的特效）
    self.weapon_res_id = 0          -- 武器资源id     
    self.halo_res_id = 0            -- 光环资源id
    self.baoju_res_id = 0           -- 宝具资源id
    self.mantle_res_id = 0          -- 披风资源id
    self.fazhen_res_id = 0          -- 法阵资源id
    self.qilinbi_res_id = 0         -- 麒麟臂资源id
    self.waist_res_id = 0           -- 腰饰资源id
    self.mask_res_id = 0            -- 面饰
    self.shouhuan_res_id = 0        -- 手环
    self.tail_res_id = 0            -- 尾巴
    self.jianzhen_res_id = 0        -- 剑阵
    self.hold_beauty_res_id = 0     -- 圣女

    --场景物体
    self.soulboy_obj = nil          -- 灵童
    self.guard_obj = nil            -- 警卫
    self.beauty_obj = nil           -- 美人
    self.mingjiang_obj = nil        -- 名将
    self.xiaotianquan = nil         -- 哮天犬
    self.wuhun_obj = nil            -- 武魂
    self.shaungsheng_tianshen_obj = nil -- 双生天神

    -- 变量
    self.update_num = 0             -- 更新个数
    self.elapse_time = 0

    self.is_gather_state = false
    self.attack_index = 1

    self.is_load_effect = false
    self.is_load_effect2 = false
    self.role_last_logic_pos_x = 0
    self.role_last_logic_pos_y = 0
    -- self.next_create_footprint_time = -1 -- 下一次生成足迹的时间
    self.play_chuansongmen_02_effect = false
    self.flush_follow_obj_index = math.huge
    self.is_from_enter_scene = nil

    self.action_type = 2
    self.body_color = 0
    self.foot_count = 0

    self.shield_spirit_helo = true --暂时屏蔽光环
    self.show_no_jump_flag = false

    self.play_action_back_fun = nil
    self.special_jump_speed = 10
    self.special_jump_call_back = nil
    self.special_jump_x = 0
    self.special_jump_y = 0

    self.is_xunyou = false
    self.update_marry_time = 0
    self.marry_role = nil

    self.hot_spring_skill = 0

    self.is_qinggong = false
    self.is_landed = true
    self.qinggong_index = 0
    self.is_force_landing = false
    self.has_play_qinggong_land = false
    self.init_model = false
    self.is_wing_fly = false
    self.chusheng_effect = false
    self.exist_time = 0
    self.record_time = self.now_time or 0
    self.is_talk = false
    self.can_bubble = false
    self.xunyou_reset_info = nil
    self.is_fly_mount = false

    self.shuibo_effect_time = 0
    self.shuibo_effect_is_show = false
    self.role_effect_visible = true

    self.is_chuan_gong = false          --是否处于传功状态
    self.is_in_sit_area = false
    self.is_in_boat = false
    self.cur_show_wuxing = nil
    self.on_hotspring_land_timestemp = 0      --温泉陆地站立时间
    self.is_bind_fight_mount_action_effect = nil

    self.total_move_time = 0
	self.is_draw_obj_change_to_fast_speed = false
    self.curr_now_time = 0

    -- 列表集合
    self.buff_list = {}
    self.pet_obj_list = {}
    self.beast_obj = nil
    self.task_callinfo_obj= nil
    self.xianji_equip_info = {} --头顶显示的仙界装备信息

    self.multi_mount_state = MULTI_MOUNT_STATE.NONE
    self.mount_up_end_call_back = nil
    self.other_role_mountupend_callback = nil

    -- 初始化
    self:UpdateAppearance()
    self:UpdateHoldBeauty()
    self:SetFightMovePos(0, 0) -- 冲锋技能的落点
    self:SetRobot(false)
    SceneObjLODManager.Instance:Insert(self)

    for part, detail_level in pairs(RoleDetailLevel) do
        self.draw_obj:SetPartNeedDetailLevel(part, detail_level)
    end

    self.draw_obj:SetCurDetailLevel(SceneObjDetailLevel.Low)

    self:GetUpdateNumber()
end

function Role:OnEnterScene()
    Character.OnEnterScene(self)--这里会触发父属性 ->OnEnterScene() ->会调用InitAppearance  UpdateMaterialQuality  self.follow_ui:OnEnterScene（)

    self.is_from_enter_scene = true
    if not self:IsMainRole() and not self:IsSkillShower() then
        self.effect_lod_handle = ReuseableHandleManager.Instance:GetShieldHandle(RoleEffectShieldHandle, self)
        self.effect_lod_handle:CreateShieldHandle()
    end

    self:GetFollowUi()
    self:CreateShadow()
    self:UpdateBoat(true)
    self:ChangeGuildBattle()
    self:ChangeBeauty()
    self:ChangeGuard()
    -- self:ChangePet()
    self:ChangeBeast()
    self:ChangeTaskCallInfo()
    self:ChangeFreeVipImage()

    if Scene.Instance:GetSceneType() == SceneType.Normal then
        if self.follow_ui then
            self.follow_ui:SetSpecialImage(false)
        end
    end

    --夜战云巅开始后瞬移进场不要更新称号
    if not Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN or
    not KuafuYeZhanWangChengWGData.Instance:CheckActIsStart() then
        self:CreateTitle()
    end

    if not self:IsMainRole() then
        -- 角色是否需要进行优先级计算(优先级低的更容易被屏蔽)
        local scene_logic = Scene.Instance:GetSceneLogic()
        self.need_calculate_priortiy = scene_logic:IsRoleNeedCalculatePriortiy()
    end

    -- 温泉
    self:InitWaterState()

    -- 巡游
    if self.is_xunyou then
        self:ForceSetVisible(false)
        self:ForceShadowVisible(false)
        if self.follow_ui then
            self.follow_ui:ForceSetVisible(false)
        end
    end

    -- 任务链活动场景 运送躲避
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= nil and scene_type == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID then
        if not self:IsEmtrace() then
            local cfg = OperationTaskChainWGData.Instance:GetCurTaskCfg()
            if cfg ~= nil then
                local dungeon_cfg = OperationTaskChainWGData.Instance:GetYunSongAvoidDungeonCfg(cfg.dungeon_id, Scene.Instance:GetSceneId())
                if dungeon_cfg ~= nil then
                    self:SetEmbraceStatus(true, dungeon_cfg.bundle, dungeon_cfg.asset)
                end
            end
        end
    end

    -- 打坐
    if not self:IsMainRole() then
        if self:IsDeleted() then
            return
        end

        local is_sit = self.vo.move_mode ~= nil and self.vo.move_mode == MOVE_MODE.MOVE_MODE_SIT
        if is_sit then
            self:EnterSit()
        end

        local is_sword = self.vo.move_mode ~= nil and self.vo.move_mode == MOVE_MODE.MOVE_MODE_SWORD
        if is_sword then
            self:Jump()
        end

        local is_dujie = self.vo.move_mode ~= nil and self.vo.move_mode == MOVE_MODE.MOVE_MODE_DUJIE
        if is_dujie then
            self:EnterDujie()
        end
    end

    self:SetWuXingIcon()
end

function Role:__delete()
    Role.Count = Role.Count - 1

    self.draw_obj:SetIsCanWeaponPointAttach(false)

    if SceneObjLODManager.Instance then
        SceneObjLODManager.Instance:Remove(self)
    end

    -- 场景物体
    self.guard_obj = self:DeleteObjFunction(SceneObjType.GuardObj, self.guard_obj)
    self.truck_obj = self:DeleteObjFunction(SceneObjType.TruckObj, self.truck_obj)
    self.spirit_obj = self:DeleteObjFunction(SceneObjType.SpriteObj, self.spirit_obj)
    self.soulboy_obj = self:DeleteObjFunction(SceneObjType.SoulBoyObj, self.soulboy_obj)
    self.beauty_obj = self:DeleteObjFunction(SceneObjType.BeautyObj, self.beauty_obj)
    self.mingjiang_obj = self:DeleteObjFunction(SceneObjType.MingJiangObj, self.mingjiang_obj)
    self.follow_mingjiang = self:DeleteObjFunction(SceneObjType.FollowMingJiang, self.follow_mingjiang)
    self.xiaotianquan = self:DeleteObjFunction(SceneObjType.XiaoTianQuan, self.xiaotianquan)
    self:RemoveWuHun()
    self:RemoveShuangShengTianShen()

    self:DeleteAllPet()
    self:DeleteAllBeast()
    self:DeleteTaskCallInfo()

    -- 变量
    self.is_load_effect = nil
    self.is_load_effect2 = nil
    self.weapon_effect_name = nil
    self.marry_role = nil
    self.is_imeprial_city_appe_changging = false
    self.cur_show_wuxing = nil

    self.buff_list = {}
    self.pet_obj_list = {}
    self.beast_obj = nil
    self.task_callinfo_obj= nil
    self.curr_now_time = nil
    self.sword_mitsurugi_status = nil
    self.sword_mitsurugi_time = nil
    self.sword_expedite_time = nil
    self.sword_shake_time = nil
    self.cur_sword_shake_time = nil
    self.project_cache_data = nil

    -- 定时器
    self.do_mount_up_delay = self:CancelTimeFunction(self.do_mount_up_delay)
    self.delay_remove_snow_eff_target = self:CancelTimeFunction(self.delay_remove_snow_eff_target)
    self.bobble_timer_quest = self:CancelTimeFunction(self.bobble_timer_quest)
    self.play_chuansong_effect = self:CancelTimeFunction(self.play_chuansong_effect)
    self.play_chusheng_effect = self:CancelTimeFunction(self.play_chusheng_effect)
    self.play_chuangong_anim = self:CancelTimeFunction(self.play_chuangong_anim)
    self.mount_delay_destroy = self:CancelTimeFunction(self.mount_delay_destroy)
    self.shuibo_time = self:CancelTimeFunction(self.shuibo_time)
    self:CancelMitsurugiDelayTimer()
    GlobalTimerQuest:CancelQuest(self.qinggong_rush_delay)
    self.qinggong_rush_delay = nil
    self.up_mount_end_func = nil

    if self.vo then
        self.vo.npc_res = nil
    end

    if self.vo then
        self.vo.is_demons = nil
    end

    if self.vo then
        self.vo.is_team_leader = 0
    end

    if self.vo then
        self.vo.ghost = EnumGhostTpye.NotGhost
    end

    for k,v in pairs(self.part_quality_handles) do
        ReuseableHandleManager.Instance:ReleaseShieldHandle(v)
    end
    self.part_quality_handles = {}

    for k,v in pairs(self.part_effect_quality_handles) do
        ReuseableHandleManager.Instance:ReleaseShieldHandle(v)
    end
    self.part_effect_quality_handles = {}

    if self.effect_lod_handle then
        ReuseableHandleManager.Instance:ReleaseShieldHandle(self.effect_lod_handle)
        self.effect_lod_handle = nil
    end

    if self.effect_chusheng_loader then
        self.effect_chusheng_loader:Destroy()
        self.effect_chusheng_loader = nil
    end

    if self.ts_union_skill_eff_loader then
        self.ts_union_skill_eff_loader:DeleteMe()
        self.ts_union_skill_eff_loader = nil
    end

    if self.up_down_mount_eff_loader then
        self.up_down_mount_eff_loader:DeleteMe()
        self.up_down_mount_eff_loader = nil
    end

    if self.artifact_trigger_eff_loader then
        self.artifact_trigger_eff_loader:DeleteMe()
        self.artifact_trigger_eff_loader = nil
    end

    self:ClearXianJieBossEquip()
    self:CancelPlayActionBackFun()
    self:ReleaseQingGongMount()
    self:ReleaseXianMengChuanGongEffect()
    self:ClearAreaIndexInfo()
    self:HotSpringEffectStop()
    self:ReleaseSitEffect()
    self:ClearTianShenSpecialPartEff()
    self:RemoveAransformationDelayTime()
    self:RemoveAransformationEffect()
    self:DestroyFootTrail()
    self:DelectAllMountModelCache()
    self:DestroyTrail()
    self:DestroyBossInvasionHuDun()
    self:DestroyMultiMountCameraSetTimer()

    if self:IsMainRole() and MainuiWGCtrl and MainuiWGCtrl.Instance then
        MainuiWGCtrl.Instance:SetAransformationMainUIEffect()
    end
end

-- 场景模型删除方法
function Role:DeleteObjFunction(scene_obj_type, operate_obj)
    if operate_obj and operate_obj:GetVo() then
        Scene.Instance:DeleteObjByTypeAndKey(scene_obj_type, operate_obj:GetObjKey())
    end

    return nil
end

-- 定时器取消方法
function Role:CancelTimeFunction(timer)
    if timer then
        GlobalTimerQuest:CancelQuest(timer)
    end

    return nil
end

-- 动作特效绑定
function Role:UpdateRoleActorConfigPrefabData()
    if not self.vo then
        return
    end
    
    local data_type = ACTOR_PREFAB_DATA_TYPE.NONE
    local data_res = 0
    if self:IsTianShenAppearance() then
        data_res = self.vo.appearance_param
        data_type = ACTOR_PREFAB_DATA_TYPE.TIAN_SHEN
    elseif self:IsGundam() then
        data_res = self.gundam_weapon_id
        data_type = ACTOR_PREFAB_DATA_TYPE.GUNDAM
    elseif self:IsRidingFightMount() then
        data_res = self.vo.mount_appeid
        data_type = ACTOR_PREFAB_DATA_TYPE.ZUO_QI
    elseif self:IsXMZCar() then
        data_res = self.special_res_id
        data_type = ACTOR_PREFAB_DATA_TYPE.MONSTER
    elseif self.vo.special_appearance == SPECIAL_APPEARANCE_TYPE.STORY_ROBERT then
         data_res = 0
        data_type = ACTOR_PREFAB_DATA_TYPE.NONE
    elseif self.vo.special_appearance == SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN then
        local prof = self:GetProf()
        local sex = self.vo.sex
        local power_type = self.vo.appearance_param + 1
        data_res = prof * 100 + sex + power_type
        data_type = ACTOR_PREFAB_DATA_TYPE.NUQI_BIANSHEN
    else
        local prof = self:GetProf()
        local sex = self.vo.sex
        data_res = prof * 100 + sex
        data_type = ACTOR_PREFAB_DATA_TYPE.ROLE
    end

    if self.cur_prefab_data_type == data_type and self.cur_prefab_data_res_id == data_res then
        return
    end

    self.cur_prefab_data_type = data_type
    self.cur_prefab_data_res_id = data_res
    if data_type == ACTOR_PREFAB_DATA_TYPE.NONE then
        self:SetActorConfigPrefabData()
    elseif data_type == ACTOR_PREFAB_DATA_TYPE.ROLE then
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetRoleAutoPrefabConfig(self.vo.sex, self:GetProf()), Role_Dynamic_Actor_Config)
    elseif data_type == ACTOR_PREFAB_DATA_TYPE.TIAN_SHEN then
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Tianshen", data_res), Role_Dynamic_Actor_Config)
    elseif data_type == ACTOR_PREFAB_DATA_TYPE.MONSTER then
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Monster", data_res))
    elseif data_type == ACTOR_PREFAB_DATA_TYPE.ZUO_QI then
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("zuoqi", data_res), Role_Dynamic_Actor_Config, true)
    elseif data_type == ACTOR_PREFAB_DATA_TYPE.GUNDAM then
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Gundam", data_res), Role_Dynamic_Actor_Config)
    elseif data_type == ACTOR_PREFAB_DATA_TYPE.NUQI_BIANSHEN then
        local power_type = math.floor(self.vo.appearance_param / 1000) + 1
        self:SetActorConfigPrefabData(ConfigManager.Instance:GetRolePowerAutoPrefabConfig(self.vo.sex, 1, power_type), Role_Dynamic_Actor_Config)
    end
end

-- 脚本实例会调用Init -> InitInfo
function Role:InitInfo()
    Character.InitInfo(self)

    self.is_bind_fight_mount_action_effect = nil
    self:UpdateRoleActorConfigPrefabData()
    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	local begin_str = "qinggong_pre%s/begin"
	local end_str = "qinggong_pre%s/end"
	for i = 1, COMMON_CONSTS.MAX_QING_GONG_COUNT do
		if main_part then
			main_part:ListenEvent(string.format(begin_str, i), BindTool.Bind(self.QingGongBeginEnter, self, i))
			main_part:ListenEvent(string.format(end_str, i), BindTool.Bind(self.QingGongBeginExit, self, i))
		end
	end

    main_part:ListenEvent("QingGongLandExit", BindTool.Bind(self.QingGongLandExit, self))

    for k,v in pairs(self.part_quality_handles) do
        v:CreateShieldHandle()
    end

    for k,v in pairs(self.part_effect_quality_handles) do
        v:CreateShieldHandle()
    end

    self:SetBuffList(self.vo.buff_flag, true)
end

function Role:IsRole()
    return true
end

function Role:GetObjKey()
    return self.vo.role_id
end

function Role:GetRoleId()
    if nil == self.vo then
        return 0
    end

    return self.vo.role_id
end

function Role:GetRoleResId()
    return self.role_res_id
end

function Role:GetOriginId()
    if self.vo == nil then
        return 0
    end

    if self.vo.origin_uid ~= nil and self.vo.origin_uid ~= 0 then
        return self.vo.origin_uid
    end

    return self:GetRoleId()
end

function Role:GetUUIDStr()
    if self:GetVo() then
        local uuid = self:GetVo().uuid
        return ToLLStr(uuid.temp_high, uuid.temp_low)
    end
    
    return nil
end

function Role:GetUUID()
    if self:GetVo() then
        local uuid = self:GetVo().uuid
        return uuid
    end

    return nil
end

function Role:GetProf()
    if self.vo then
        return self.vo.prof % 10
    end

    return 1
end

-- 是否已结婚
function Role:IsMarryUser()
    local marry_cfg = MarryWGData.Instance:GetCurWeddingInfo()
    if not IsEmptyTable(marry_cfg) and (self.vo.role_id == marry_cfg.role_id or self.vo.role_id == marry_cfg.lover_role_id) then
        return true
    end

    return false
end

-- 是否乘骑
function Role:IsRiding()
    return (self:GetMountResId() > 0 or self:GetTaskToHeighMount() > 0)
end

-- 获取当前的坐骑资源id
function Role:GetCurRidingResId()
    local res_id = self:GetTaskToHeighMount()
    if res_id > 0 then
        local _
        _, res_id = TaskWGData.Instance:ChangeMountResInfo(res_id)
        return res_id
    end

    res_id = self:GetMountResId()
    if res_id > 0 then return res_id end

    return 0
end

-- 获取当前的坐骑部位类型
function Role:GetCurRidingPart()
    --[[
    local res_id = self:GetTaskToHeighMount()
    if res_id > 0 then return SceneObjPart.Mount end

    local mount_appeid = self:GetMountResId()
    if mount_appeid > 0 then
        local is_fm_appe = NewFightMountWGData.Instance:IsFightMountAppImage(mount_appeid)
        if is_fm_appe and self.vo.fight_mount_skill_level > 0 then
            return SceneObjPart.FightMount
        else
            return SceneObjPart.Mount
        end
    end
    ]]

    return SceneObjPart.Mount
end

-- 是否乘骑 但不是乘骑战斗坐骑
function Role:IsRidingNoFightMount()
    if self:GetTaskToHeighMount() > 0 then return true end

    local mount_appeid = self:GetMountResId()
    if mount_appeid > 0 then
        local is_fm_appe = NewFightMountWGData.Instance:IsFightMountAppImage(mount_appeid)
        return not (is_fm_appe and self.vo.fight_mount_skill_level > 0)
    end

    return false
end

-- 是否乘骑战斗坐骑
function Role:IsRidingFightMount()
    if self:GetTaskToHeighMount() > 0 then return false end

    local mount_appeid = self:GetMountResId()
    if mount_appeid > 0 then
        local is_fm_appe = NewFightMountWGData.Instance:IsFightMountAppImage(mount_appeid)
        return is_fm_appe and self.vo.fight_mount_skill_level > 0
    end

    return false
end

-- 坐骑 默认资源id
function Role:GetMountResId()
    local vo = self.vo
    if not vo then
        return 0
    end

    return vo.mount_appeid
end

-- 一念神魔光环
function Role:ChangeGodOrDemonHalo()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    local is_show = fb_scene_cfg.pb_godordemonHalo ~= 1
    local cur_cfg = YinianMagicWGData.Instance:GetGradeCfg(self.vo.god_or_demon_type, self.vo.god_or_demon_grade) 
    local modle_type = cur_cfg and cur_cfg.modle_type or 0
    local show_model_type_cfg = YinianMagicWGData.Instance:GetModelTypeCfg(self.vo.god_or_demon_type, modle_type)
    local res_id = show_model_type_cfg and show_model_type_cfg.appe_image_id and show_model_type_cfg.appe_image_id or 0
    if res_id > 0 and is_show then
        self:ChangeModel(SceneObjPart.GodOrDemonHalo, ResPath.GetYiNianMagicHaloModel(res_id))
    else
        self:RemoveModel(SceneObjPart.GodOrDemonHalo)
    end
end

-- 坐骑 任务高级坐骑id
function Role:GetTaskToHeighMount()
    if not self.vo then return 0 end
    if self.vo.task_appearn == CHANGE_MODE_TASK_TYPE.MOUNT_HIGH then
        return self.vo.task_appearn_param_1
    end

    return 0
end

-- 是否乘骑鲲
function Role:IsKunRiding()
    if not NewAppearanceWGData.Instance then
        return false
    end
    local kun_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(self:GetMountResId())
    return kun_cfg ~= nil
end

-- 是否在安全区
function Role:IsInSafeArea()
    return AStarFindWay:IsInSafeArea(self.logic_pos.x, self.logic_pos.y)
end

-- 是否在永夜之巅场景
function Role:IsInEternalNightScene()
    if self:IsTianShenAppearance() then
        return false
    end

    local scene_type = Scene.Instance:GetSceneType()
    return scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL
end

-- 改变移动模式
function Role:ChangeMoveMode(move_mode)
    self.vo.move_mode = move_mode
end

-- 等待废弃
--boss检测用，存在同副本场景的情况
function Role:CheckIsRestState()
	if YunbiaoWGData.Instance:GetIsHuShong() or
		MarryWGData.Instance:GetOwnIsXunyou() or
		CgManager.Instance:IsCgIng() or
		self:IsQingGong() then
		return false
	end

	return true
end

function Role:GetUpdateNumber()
    if self:IsMainRole() then
        self.update_num = 0
    else
        self.update_num = math.ceil(Role.Count / 5)
    end
end

function Role:Update(now_time, elapse_time)
    self.curr_now_time = now_time
    self.update_num = self.update_num - 1
    self.elapse_time = self.elapse_time + elapse_time
    if self.update_num > 0 then
        return
    end

    elapse_time = self.elapse_time
    self.elapse_time = 0
    self:GetUpdateNumber()

    Character.Update(self, now_time, elapse_time)
    if self.role_last_logic_pos_x ~= self.logic_pos.x or self.role_last_logic_pos_y ~= self.logic_pos.y then
        self.role_last_logic_pos_x = self.logic_pos.x
        self.role_last_logic_pos_y = self.logic_pos.y

        -- -- unity3d项目暂时屏蔽(足迹)
        -- if self.next_create_footprint_time == 0 then
        --     self:CreateFootPrint()
        --     self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
        -- end

        -- if self.next_create_footprint_time == -1 then --初生时也是位置改变，不播
        --     self.next_create_footprint_time = 0
        -- end

        self:UpdateShuiboEffect()
    end

    self:UpdateFootTrail()

    -- if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
    --     self.next_create_footprint_time = 0
    -- end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.KF_HotSpring or scene_type == SceneType.KF_DUCK_RACE then--判断是否温泉的场景
        if self:IsWaterWay() then
            self.vo.is_in_hot_spring = true
            self.draw_obj:SetOffset(Vector3(0, self:IsMove() and SWIMMING_WATER_HIGHT.SWIM_MOVE or SWIMMING_WATER_HIGHT.SWIM_IDLE, 0))
            self.draw_obj:GetLookAtPoint(0, 1, 0)
            self:ForceShadowVisible(false)
            self.draw_obj:GetPart(SceneObjPart.Main):SetIsCastShadow(false)
        else
            self.vo.is_in_hot_spring = false
            self.draw_obj:SetOffset(Vector3(0, 0, 0))
            self.draw_obj:GetLookAtPoint(0, 2.8, 0)
            self:ForceShadowVisible(true)
            self.draw_obj:GetPart(SceneObjPart.Main):SetIsCastShadow(true)
        end
    end

    if self:IsMove() then
		self.total_move_time = self.total_move_time + elapse_time
	else
		self.total_move_time = 0
	end
    
    if not self:IsJump() then
        self:ChangeDrawObjectMoveToSpeed()
    end

    self:UpdateXunyou(now_time, elapse_time)
    self:UpdateFollowObjVisible()
    self:CheckDeleteMountModel(now_time)
end

-- 检测属性数值是否变化
function Role:CheckAttrIsChange(key, new_value)
    local old_value = self:GetAttr(key)
    local attr_type = type(old_value)
    if attr_type ~= type(new_value) then
        return true
    end

    if attr_type == "table" then
        local is_same_table = IsSameTable(old_value, new_value) 
        return not is_same_table
        -- for k,v in pairs(old_value) do
        --     if old_value[k] ~= new_value[k] then
        --         return true
        --     end
        -- end

        -- for k,v in pairs(new_value) do
        --     if old_value[k] ~= new_value[k] then
        --         return true
        --     end
        -- end
    end

    return old_value ~= new_value
end

function Role:SetAttr(key, value, param)
    --（减少刷新）
    local appearance_check_pass = false
    local diy_appearance_check_pass = false
    if key == "appearance" then
        appearance_check_pass = self:CheckAttrIsChange(key, value)
    elseif key == "role_diy_appearance" then
        diy_appearance_check_pass = self:CheckAttrIsChange(key, value)
    elseif key == "mount_appeid" then
        local fight_mount_skill_level = math.floor(value / COMMON_CONSTS.MOUNT_BOUNDARY)
		value = value % COMMON_CONSTS.MOUNT_BOUNDARY
        local is_fm = NewFightMountWGData.Instance:IsFightMountAppImage(value)
        if not is_fm then
            fight_mount_skill_level = 0
        end
        Character.SetAttr(self, "fight_mount_skill_level", fight_mount_skill_level)
    end

    if key == "beast_id" then
        self.vo.beast_id = value
        self:ChangeBeast()
    elseif key == "beast_skin" then
        self.vo.beast_skin = value
        self:ChangeBeast()
    else
        Character.SetAttr(self, key, value, param)
    end

    -- 转职 - 验证默认身体部位资源
    local prof_check_pass = false
    if key == "prof" then
        local appearance = self.vo.appearance
        prof_check_pass = RoleWGData.Instance:CheckDefaultResIsLegal(self.vo.sex, self.vo.prof,
                        appearance.default_face_res_id, appearance.default_hair_res_id, appearance.default_body_res_id)
    end

    local scene_type = Scene.Instance:GetSceneType()

    if self:GetIsInSpring(scene_type)
        and value ~= SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON
        and value ~= SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING
        and value ~= SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_SHUANGXIU
    then
        self:GetFollowUi():SetIconActive(false)
        -- if self.is_hot_spring_init_model == 1 then
        --     return
        -- end
    end

    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    local bundle, name = nil, nil

    -- 形象改变、乘骑改变 打断交互行为
    if (key == "special_appearance" or key == "mount_appeid") and value > 0 then
        self:TryClearInteractiveEffectsAndSounds()
        if self:IsInteractive() then
            self:ChangeToCommonState()
        end
    end

    if prof_check_pass
        or appearance_check_pass
        or key == "special_appearance"
        -- or key == "bianshen_param"
        or key == "buff_flag"
        or diy_appearance_check_pass
        or (key == "task_appearn" and self.vo.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE)
    then
        if self.vo.npc_res and self.vo.npc_res ~= "" then
            return
        end

        self:UpdateAppearance()
        self:RemoveAransformationEffect()
        if CgManager.Instance:IsCgIng() then
            self.special_res_id = 0
        end

        if self:IsMainRole() then
            self:CheckQingGong()
        end

        if self.special_res_id == 0 or not self:IsGundam() then
            self:RemoveModel(SceneObjPart.GundamLArm)
            self:RemoveModel(SceneObjPart.GundamRArm)
            self:RemoveModel(SceneObjPart.GundamLLeg)
            self:RemoveModel(SceneObjPart.GundamRLeg)
            self:RemoveModel(SceneObjPart.GundamLWing)
            self:RemoveModel(SceneObjPart.GundamRWing)
        end

        -- 更新形象
        local need_remove_weapon, need_remove_skill_halo = true, true
        local is_story_robert = SPECIAL_APPEARANCE_TYPE.STORY_ROBERT == self.vo.special_appearance  --剧情机器人形象
        if self.special_res_id ~= 0 or is_story_robert then
            if is_story_robert then
                local res_t = Split(self.vo.special_param, "##")
                bundle, name = res_t[1], res_t[2]
                if bundle and name then
                    self:ChangeMainPartModel(bundle, name)
                end
            elseif self:IsXMZCar() then  -- 仙盟战变身
                local hs_xs_id = self.vo.appearance_param
                local cfg = GuildBattleRankedWGData.Instance:GetHSXSIdByCfg(hs_xs_id)
                local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
                local blue_resid = cfg and cfg.blue_hs_xs_resid or other_cfg.blue_hs_kong_resid
                local red_resid = cfg and cfg.red_hs_xs_resid or other_cfg.red_hs_kong_resid
                local user_info = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()

                if user_info and self:IsMainRole() then -- 精简
                    self.special_res_id = user_info.side == 0 and blue_resid or red_resid
                else
                    self.special_res_id = self.vo.special_param == 0 and blue_resid or red_resid
                end

                bundle, name = ResPath.GetMonsterModel(self.special_res_id)
                self:ChangeMainPartModel(bundle, name)
                self.is_bind_fight_mount_action_effect = nil
                self:UpdateRoleActorConfigPrefabData()
            elseif SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON == self.vo.special_appearance then --温泉形象
                -- 只有身体资源需要改变，其余部位用玩家diy默认值
                local role_bundle, role_name = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.vo.sex, self:GetProf()))
                local appe_data = self.vo.appearance
                local def_hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, appe_data.default_hair_res_id, self.vo.sex, self:GetProf())
                local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(self.vo.sex, self:GetProf(), 0, 0, appe_data.default_face_res_id, appe_data.default_hair_res_id)
                local role_diy_appearance_data = self.vo.role_diy_appearance
                local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.vo.sex, self:GetProf(), role_diy_appearance_data.preset_seq or 1)
                local extra_model_data = {
                    sex = self.vo.sex,
                    prof = self:GetProf(),
                    role_body_res = self.special_res_id,
                    role_face_res = face_res,
                    role_hair_res = hair_res,

                    eye_size = role_diy_appearance_data.eye_size,
                    eye_position = role_diy_appearance_data.eye_position,
                    eye_shadow_color = role_diy_appearance_data.eye_shadow_color,
        
                    left_pupil_type = role_diy_appearance_data.left_pupil_type,
                    left_pupil_size = role_diy_appearance_data.left_pupil_size,
                    left_pupil_color = role_diy_appearance_data.left_pupil_color,
        
                    right_pupil_type = role_diy_appearance_data.right_pupil_type,
                    right_pupil_size = role_diy_appearance_data.right_pupil_size,
                    right_pupil_color = role_diy_appearance_data.right_pupil_color,
        
                    mouth_size = role_diy_appearance_data.mouth_size,
                    mouth_position = role_diy_appearance_data.mouth_position,
                    mouth_color = role_diy_appearance_data.mouth_color,
        
                    face_decal_id = role_diy_appearance_data.face_decal_id,
                    hair_color = role_diy_appearance_data.hair_color,

                    eye_angle = preset_diy_cfg and preset_diy_cfg.eye_angle,
                    eye_close = preset_diy_cfg and preset_diy_cfg.eye_close,
                    eyebrow_angle = preset_diy_cfg and preset_diy_cfg.eyebrow_angle,
                    nose_size = preset_diy_cfg and preset_diy_cfg.nose_size,
                    nose_angle = preset_diy_cfg and preset_diy_cfg.nose_angle,
                    mouth_angle = preset_diy_cfg and preset_diy_cfg.mouth_angle,
                    cheek_size = preset_diy_cfg and preset_diy_cfg.cheek_size,
                    chin_length = preset_diy_cfg and preset_diy_cfg.chin_length,
                }
                
                self:ChangeMainPartModel(role_bundle, role_name, nil, nil, DRAW_MODEL_TYPE.ROLE, extra_model_data)

            elseif self:IsXiuWeiBianShen() then --怒气形象
                -- 只有主部件模型改变，其他使用角色的外观
                local role_bundle, role_name = ResPath.GetRoleModel(self.special_res_id)
                local power_type = math.floor(self.vo.appearance_param / 1000)
                local image_lv = self.vo.appearance_param % 1000
                local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(power_type, image_lv)

                if cfg then
                    local body_res, face_res, hair_res = RoleWGData.GetShowRoleRealmSkinPartRes(self.vo.sex, self:GetProf(), cfg.default_body, cfg.default_face, cfg.default_hair)
                    local role_diy_appearance_data = self.vo.role_diy_appearance
                    local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.vo.sex, self:GetProf(), role_diy_appearance_data.preset_seq or 1)
                    local extra_model_data = {
                        role_body_res = body_res,
                        role_face_res = face_res,
                        role_hair_res = hair_res,
                        is_realm = true,
                    }

                    self:ChangeMainPartModel(role_bundle, role_name, nil, nil, DRAW_MODEL_TYPE.ROLE, extra_model_data)
                    self:UpdateRoleActorConfigPrefabData()
                    self:ExecuteAransformationAction()
                end
            elseif self:IsGundam() then
                need_remove_weapon = false
                self:UpdateGundamModel()

            elseif self:IsTianShenAppearance() then --天神变身
                need_remove_weapon = false
                bundle, name = self:GetSceneBianShenRes()
                self.is_bind_fight_mount_action_effect = nil
                self:ChangeMainPartModel(bundle, name, function ()
                    -- SkillWGCtrl.Instance:PlaySkill()
                    SkillWGCtrl.Instance:SetCheckTianShenBianShenSkill(true)
                    self:UpdateHpPosition()
                    self:ChangeShuangShengTianShen()
                    -- self:CheckTianShenSpecialEff()
                end, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN)
                self:UpdateRoleActorConfigPrefabData()
                self:TryLeaveSit()
            elseif self.vo.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE then
                bundle, name = TaskWGData.Instance:ChangeResInfo(self.special_res_id)
                self:ChangeMainPartModel(bundle, name, function ()
                    self:UpdateHpPosition()
                end)
            else
                bundle, name = ResPath.GetMonsterModel(self.special_res_id)
                self:ChangeMainPartModel(bundle, name, function ()
                    self:UpdateHpPosition()
                end)
            end

            if need_remove_weapon then
                self:RemoveModel(SceneObjPart.Weapon)
            end

            if need_remove_skill_halo then
                self:RemoveModel(SceneObjPart.SkillHalo)
            end

            self.is_wing_fly = false
            self:RemoveModel(SceneObjPart.Mantle)
            self:RemoveModel(SceneObjPart.Wing)
            self:RemoveModel(SceneObjPart.Halo)
            self:RemoveModel(SceneObjPart.BaoJu)
            self:RemoveModel(SceneObjPart.QiLinBi)
            self:RemoveModel(SceneObjPart.Waist)
            self:RemoveModel(SceneObjPart.Mask)
            self:RemoveModel(SceneObjPart.ShouHuan)
            self:RemoveModel(SceneObjPart.Tail)
            self:RemoveModel(SceneObjPart.Jianling)
            self:RemoveModel(SceneObjPart.FaZhen)
            self:RemoveModel(SceneObjPart.GodOrDemonHalo)
            self:RemoveWuHun()   --武魂特殊，特殊形象要移除，看需求
            self:RemoveShuangShengTianShen()

            if self:IsMainRole() and key == "special_appearance" then
                if value == SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN then
                    if not self:IsFightState() then
                        self:EnterFight()
                    else
                        Scene.Instance:UpdateMainRoleEnterFightCameraDis()
                    end
                end
            end

            return
        end

        SkillWGCtrl.Instance:SetCheckTianShenBianShenSkill(false)

        if self.role_res_id ~= 0 and self.init_model then
            local appe_data = self.vo.appearance
            local def_hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, appe_data.default_hair_res_id, self.vo.sex, self:GetProf())
            local body_res, face_res, hair_res = self:GetModelPartRes()
            local role_diy_appearance_data = self.vo.role_diy_appearance
            local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.vo.sex, self:GetProf(), role_diy_appearance_data.preset_seq or 1)
            local role_bundle, role_name = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.vo.sex, self:GetProf()))

            self:ClearTianShenSpecialEff()

            local extra_model_data = {
                sex = self.vo.sex,
                prof = self:GetProf(),

                role_body_res = body_res,
                role_face_res = face_res,
                role_hair_res = hair_res,

                eye_size = role_diy_appearance_data.eye_size,
                eye_position = role_diy_appearance_data.eye_position,
                eye_shadow_color = role_diy_appearance_data.eye_shadow_color,
    
                left_pupil_type = role_diy_appearance_data.left_pupil_type,
                left_pupil_size = role_diy_appearance_data.left_pupil_size,
                left_pupil_color = role_diy_appearance_data.left_pupil_color,
    
                right_pupil_type = role_diy_appearance_data.right_pupil_type,
                right_pupil_size = role_diy_appearance_data.right_pupil_size,
                right_pupil_color = role_diy_appearance_data.right_pupil_color,
    
                mouth_size = role_diy_appearance_data.mouth_size,
                mouth_position = role_diy_appearance_data.mouth_position,
                mouth_color = role_diy_appearance_data.mouth_color,
    
                face_decal_id = role_diy_appearance_data.face_decal_id,
                hair_color = role_diy_appearance_data.hair_color,

                eye_angle = preset_diy_cfg and preset_diy_cfg.eye_angle,
                eye_close = preset_diy_cfg and preset_diy_cfg.eye_close,
                eyebrow_angle = preset_diy_cfg and preset_diy_cfg.eyebrow_angle,
                nose_size = preset_diy_cfg and preset_diy_cfg.nose_size,
                nose_angle = preset_diy_cfg and preset_diy_cfg.nose_angle,
                mouth_angle = preset_diy_cfg and preset_diy_cfg.mouth_angle,
                cheek_size = preset_diy_cfg and preset_diy_cfg.cheek_size,
                chin_length = preset_diy_cfg and preset_diy_cfg.chin_length,
            }

            self:ChangeMainPartModel(role_bundle, role_name, function()
                self:UpdateHpPosition()
            end, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE, DRAW_MODEL_TYPE.ROLE, extra_model_data, function(skin_type)
                self:FlushPartColor(skin_type)
            end)

            self:EquipDataChangeListen()
        end

        -- 这些part 为何不放主物体加载之后，会不会出现没绑上的问题
        self:ChangeWuHun()
        self:UpdateWing()
        self:UpdateJianZhen()
        self:UpdateMount()
        self:ChangeMantle()
        self:ChangeHalo()
        self:ChangeQiLinBi()
        self:ChangeWaist()
        self:ChangeMask()
        self:ChangeShouHuan()
        self:ChangeTail()
        self:ChangeFaZhen()
        self:ChangeSkillHalo()

    elseif key == "fabao_appeid" then
        self:UpdateBaoJu()
        local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
        if self.baoju_res_id ~= nil and self.baoju_res_id ~= 0 and fb_scene_cfg.pb_zhibao ~= 1 then
            self:ChangeBaoJuModel()
        else
            self:RemoveModel(SceneObjPart.BaoJu)
        end
    elseif key == "task_appearn" and self.vo.task_appearn == CHANGE_MODE_TASK_TYPE.MOUNT_HIGH then
        self:UpdateMount()
    elseif key == "vip_level" or key == "shield_vip_flag" or key == "baozhu_act_flag" then
        self:ReloadUIVip()
    elseif key == "vip_free_card" then
        self:ChangeFreeVipImage()
    elseif key == "wing_appeid" then
        self:UpdateWing()
    elseif key == "ghost" then
        self:ChangeGhostModel()
    elseif key == "is_demons" then
        self:ChangeDemonsModel()
        self:EquipDataChangeListen()
    elseif key == "is_team_leader" then
        self:ReloadUILeaderFlag()
    elseif key == "jianzhen_appeid" then
        self:UpdateJianZhen()
    elseif key == "mount_appeid" then
        if main_part then
            main_part:EnableMountUpTrigger(false)
            self:OnMountUpEnd()
            -- 2021/08/25 乘骑状态隐藏圆形影子
            self:ForceShadowVisible(value and value <= 0)
        end
    elseif key == "own_drop_boss_count" then
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type ~= SceneType.GuDaoJiZhan_FB
        and scene_type ~= SceneType.FengShenBang
        and scene_type ~= SceneType.Guild_Invite
        and scene_type ~= SceneType.XianMengzhan
        and scene_type ~= SceneType.ETERNAL_NIGHT
        and scene_type ~= SceneType.ETERNAL_NIGHT_FINAL
        and scene_type ~= SceneType.SCENE_TYPE_DRAGON_TRIALT_FB
        and not MainuiWGData.IsGuideFbScene()
        and (not self:IsMainRole() or (self.vo and self.vo.tired_value_icon ~= 1))
        then
            self:SetAscriptionIcon(value)
        end
    elseif key == "tired_value_icon" then
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type == SceneType.WorldBoss or scene_type == SceneType.KF_BOSS or scene_type == SceneType.SG_BOSS or
            scene_type == SceneType.VIP_BOSS or scene_type == SceneType.HONG_MENG_SHEN_YU or scene_type == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
            if self.vo and self.vo.own_drop_boss_count ~= 1 then
                self:SetTiredIcon(value)
            end
        end
    elseif key == "used_title_list" then
        self:ChangeFreeVipImage()
        self:UpdateTitle()
    elseif key == "used_diy_title_name" then
        self:GetFollowUi():UpdateDiyTitleName()
    elseif key == "hp" or key == "max_hp" then
        self:SyncShowHp(nil, param)
    elseif key == "bianshen_hp" or key == "bianshen_max_hp" then
        self:SyncShowBianShenHp()
    elseif key == "origin_server_id" or key == "plat_name" then
        self:ReloadUIServerName()
    elseif key == "special_param" then
        self:ChangeGuildBattle()
        self:ChangeFollowUiName()
        self:ReloadUITianShen3v3Side()
        self:UpdateBoat()
    elseif key == "area_index" then
        self:UpdataAreaIndexImg(value)
        self:ReloadUINameColor()
    elseif key == "god_or_demon_type" or key == "god_or_demon_level" or key == "god_or_demon_grade" then
        self:ChangeGodOrDemonHalo()
        self:ReloadUIGodOrDemon()
    elseif key == "xianjie_equip" then
        self:UpdataXianJieEquipImg(value)
    elseif key == "is_shadow" then
        self:ReloadUITianShen3v3Side()
    elseif key == "xiaotianquan_id" then
        self:ChangeXiaoTianQuan()
    elseif key == "soulboy_lg_id" then
        self:ChangeSoulBoyWeapon(value)
    elseif key == "jingjie_level" then --
        if value then
            self.vo.jingjie_level = value
        end
        --self:ReloadUIJingJieIcon()
    -- elseif key == "level" then
    --     self:ReloadUIJingJieIcon()
    elseif key == "husong_color" then
        if self:IsMainRole() then
            self:CheckQingGong()
        end
        self:ChangeBeauty()
    elseif key == "guild_name" then
        self:ReloadUIGuildName()
        -- self:UpdateTitle()
    elseif key == "zhandui3v3_name" then
        if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
            self:UpdateZhanDuiName()
        end
    elseif key == "zhandui3v3_lingpai_id" then
        if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare
        or Scene.Instance:GetSceneType() == SceneType.Kf_PVP then
            self:UpdateZhanDuiZhanLingId()
        end
    elseif key == "zhandui3v3_lingpai_name" then
        if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare
        or Scene.Instance:GetSceneType() == SceneType.Kf_PVP then
            self:UpdateZhanDuiZhanLingText()
        end
    elseif key == "cross3v3_side" then
        self:ReloadUINameColor()
        self:ReloadUITianShen3v3Side()
    elseif key == "lover_uid" then
        self:ReloadUILoverName()
        -- self:UpdateTitle()
    elseif key == "lover_name" then
        self:ReloadUILoverName()
        -- self:UpdateTitle()
    elseif key == "name_color" then
        self:ChangeFollowUiName()
    elseif key == "move_speed" then
        -- 这里可能会出BUG，在客户端发起移动的时候，会设置移动速度，但是如果这个时候服务端移动速度变更了
        -- 那么客户端必须要重新发起移动，因为不然速度不会设置到C#那边，但是如果有地方调用了dostand之类的
        -- 这里会进不来，然后逻辑层这边速度是和服务端同步的，但是moveobj那边不是，就会移动的时候穿过各种障碍区
        if self:IsMove() then
            if self.path_pos_list and #self.path_pos_list > 0 then
                local pos = self.path_pos_list[self.path_pos_index]
                if pos then
                    self.DoMove(self, pos.x, pos.y, self.path_pos_index)
                    self:SendMoveReq()
                end
            end
        end
    elseif key == "guard_id" then
        self:ChangeGuard()
    -- elseif key == "lingchong_appeid" then
    --     self:ChangePet()
    elseif key == "yzwc_ico" then
        self:GetFollowUi():SetYZWCIco(value, self)
    elseif key == "task_callinfo_id" then
        self.vo.task_callinfo_id = value
        self:ChangeTaskCallInfo()
    elseif key == "yzwc_score" then
        self:GetFollowUi():SetRoleScore(value)
    elseif key == "eternal_night_title" then
        self:ReloadUIEternalNight()
    elseif key == "xmz_client_show_effect" then  --仙盟战人物拾取buff播放的特效
        self:XMZClientShowEffect(value)
    elseif key == "xiezhu_ing" then
        self:ReloadUIXiezhuIng()
    elseif key == "wuhun_zhenshen" then
        if value ~= nil and param ~= nil then
            self.vo.wuhun_id = value
            self.vo.wuhun_lv = param
            self:ChangeWuHun()
        end
    elseif key == "shuangsheng_tianshen" then
        self.vo.shaungsheng_tianshen_aura_id = value
        self:ChangeShuangShengTianShen()
    elseif key == "xiuwei_stage" or key == "stage_level" then
        self:ReloadUIXiuWeiIcon()
    elseif key == "wuhun_hunzhen" then
        self.vo.wuhun_hunzhen_id = value
        self:ChangeWuHunHunZhen()
    end

    if self:GetIsInSpring(scene_type)
        and value ~= SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON
        and value ~= SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING
    then
        if self.is_hot_spring_init_model and self.is_hot_spring_init_model == 1 then
            if self:GetVo().special_appearance ~= SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON then
                self:SetAttr("special_appearance", SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON)
            end
            return
        end
        self.is_hot_spring_init_model = 1
        self:SetAttr("special_appearance", SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON)
    end

    if  SceneType.KF_DUCK_RACE == scene_type
        and value ~= SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON then
        self:SetAttr("special_appearance", SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON)
    end
end




-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------     人物状态      ----------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 行为
function Role:ChangeState(state)
    Character.ChangeState(self, state)

    if not self.is_from_enter_scene then
        return
    end

    self.is_from_enter_scene = nil
    -- 如果非玩家进入玩家视野是移动状态，非玩家直接设为加速状态
    if not self:IsMainRole() and self:IsMove() then
        self.total_move_time = Config.SCENE_LOW_SPEED_TIME
    end
end

----[[人物状态
-- 更新 - 站立
function Role:UpdateStateStand(elapse_time)
    if self.is_imeprial_city_appe_changging then
        return
    end

    if self.shuibo_effect_time <= Status.NowTime then
        self:ShuiboEffectStop()
    end
    Character.UpdateStateStand(self, elapse_time)

    if self:GetIsInSpring() then
        if self:IsWaterWay() then
            self:ReleaseHotSpringMoveEffect()
            self:CreateHotSpringStandEffect()
            self.on_hotspring_land_timestemp = 0
        else
            self:HotSpringEffectStop()
            if self.on_hotspring_land_timestemp == 0 then
                self.on_hotspring_land_timestemp = Status.NowTime + COMMON_CONSTS.SWIM_REST_TIME
            elseif Status.NowTime >= self.on_hotspring_land_timestemp then
                self.on_hotspring_land_timestemp = Status.NowTime + COMMON_CONSTS.SWIM_REST_TIME
                --self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Swim_Rest, nil, nil, true)
            end
        end
    end
end

-- 进入 - 站立
function Role:EnterStateStand()
    if self.is_imeprial_city_appe_changging then
        return
    end

    self:MultiMountEnterStateStand()

    Character.EnterStateStand(self)
    if self:GetIsInSpring() then--判断是否温泉的场景
        if self:IsWaterWay() then
            self:ReleaseHotSpringMoveEffect()
            self:CreateHotSpringStandEffect()
        end
    end

    if self.wuhun_obj then
        self.wuhun_obj:EnterStateStand()
    end

    if self.shaungsheng_tianshen_obj then
        self.shaungsheng_tianshen_obj:EnterStateStand()
    end

    if self:IsMitsurugi() and self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.MOVE or self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.EXPEDITE then
        self.sword_mitsurugi_status = SPRITE_MOVE_STATUS.IDLE
        self.sword_mitsurugi_time = 0
        self:ExecuteSwordIdleAction()  -- 执行御剑idle
    end
end

-- 移动 总时间
function Role:GetTotalMoveTime()
	return self.total_move_time
end

-- obj c#层变速
-- Character执行DoMove会执行一次MoveTo（慢速）
function Role:ChangeDrawObjectMoveToSpeed()
	if not self.move_end_pos or not self:IsMove() then
		self.is_draw_obj_change_to_fast_speed = false
		return
	end

	local is_fast_speed = self:GetTotalMoveTime() >= Config.SCENE_LOW_SPEED_TIME
	if not is_fast_speed then
		self.is_draw_obj_change_to_fast_speed = is_fast_speed
		return
	end

	if self.is_draw_obj_change_to_fast_speed == is_fast_speed then
		return
	end

    if self:IsSwordSprint() then
        return
    end

	self.is_draw_obj_change_to_fast_speed = is_fast_speed
    -- self:UpdateMainRoleMoveSpeed()
    local height = self.draw_obj.root_transform.localPosition.y
	self.draw_obj:MoveTo(self.move_end_pos.x, self.move_end_pos.y, self:GetMoveSpeed(), height)
end

-- 移动 获取移速
function Role:GetMoveSpeed()
    local move_speed = self.vo.move_speed
    if self:IsFear() then -- 服务器那边说恐惧时写死速度
        return Scene.ServerSpeedToClient(100)
    end

    local speed = Scene.ServerSpeedToClient(move_speed + self.special_speed)
    if self:IsJump() then
        if self.vo.jump_factor then
            speed = self.vo.jump_factor * speed
        else
            speed = 1.8 * speed
        end
        return speed
    end

    local is_low_speed = self:GetTotalMoveTime() < Config.SCENE_LOW_SPEED_TIME and not self.is_from_enter_scene
    speed = is_low_speed and speed * Config.SCENE_LOW_SPEED_FACTOR or speed
    return speed
end

-- 更新 - 移动
function Role:UpdateStateMove(elapse_time)
    Character.UpdateStateMove(self, elapse_time)

    self:MultiMountUpdateStateMove(elapse_time)

    if self.xiaotianquan then
        self.xiaotianquan:OwnerEnterStateMove(self.logic_pos)
    end

    if self:GetIsInSpring() then
        if self:IsWaterWay() then
            self:ReleaseHotSpringStandEffect()
            self:CreateHotSpringMoveEffect()
        else
            self:HotSpringEffectStop()
        end
    end

    self.on_hotspring_land_timestemp = 0

    if self:IsMitsurugi() then
        if self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.STOP then
            self:ExecuteSwordMoveAction() 
            self.sword_mitsurugi_status = SPRITE_MOVE_STATUS.MOVE
        elseif self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.IDLE then
            self.sword_mitsurugi_status = SPRITE_MOVE_STATUS.MOVE
            self.sword_mitsurugi_time = 0
            self:ExecuteSwordMoveAction() 
        elseif self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.MOVE then
            if self.sword_mitsurugi_time == nil then
                self.sword_mitsurugi_time = 0
            end

            local cur_sword_expedite_time = self.sword_expedite_time or 0
            if self.sword_mitsurugi_status ~= SPRITE_MOVE_STATUS.SPRINT then    -- 非冲刺加时间
                self.sword_mitsurugi_time = self.sword_mitsurugi_time + elapse_time
            end
    
            if self.sword_mitsurugi_time >= cur_sword_expedite_time  then
                self.sword_mitsurugi_status = SPRITE_MOVE_STATUS.EXPEDITE   -- 加速状态
                self:ShowSwordSprintEff()
            end    
        end

        if self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.MOVE or self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.EXPEDITE then
            if self.cur_sword_shake_time == nil then
                self.cur_sword_shake_time = 0
            end
        
            local cur_sword_shake_time = self.sword_shake_time or 0
            self.cur_sword_shake_time = self.cur_sword_shake_time + elapse_time
            if self.cur_sword_shake_time >= cur_sword_shake_time then
                self.cur_sword_shake_time = 0
                self:ExecuteSwordShakeAction()  -- 执行剑身晃动
            end
        end
    end
end

-- 进入 - 移动
function Role:EnterStateMove()
    Character.EnterStateMove(self)

    self:MultiMountEnterStateMove()

    if self:IsMitsurugi() and self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.IDLE then
        self.sword_mitsurugi_status = SPRITE_MOVE_STATUS.MOVE
        self:ExecuteSwordMoveAction()
    end

    if self:GetIsInSpring() then--判断是否温泉的场景
        if self:IsWaterWay() then
            self:ReleaseHotSpringStandEffect()
            self:CreateHotSpringMoveEffect()
        end
    end

    if self.wuhun_obj then
        self.wuhun_obj:EnterStateMove()
    end

    if self.shaungsheng_tianshen_obj then
        self.shaungsheng_tianshen_obj:EnterStateMove()
    end
end

-- 移动 退出状态
function Role:QuitStateMove()
    -- 御剑冲刺时没有退出移动状态
    local is_not_stop = self:IsMitsurugi() and self:IsSwordSprint()
    Character.QuitStateMove(self, is_not_stop)
    self.sword_mitsurugi_time = 0
    self.cur_sword_shake_time = 0
end

-- 执行移动
function Role:DoMove(pos_x, pos_y, move_reason, is_comefrom_joystick, height)
    Character.DoMove(self, pos_x, pos_y, move_reason, is_comefrom_joystick, height)
end

----[[攻击
-- 进入 - 攻击状态
function Role:EnterStateAttack()
    local anim_name = ""
    local ignore_do_ani = self:GetIsIgnoreAnimSkill()
    if not ignore_do_ani then
        anim_name = SkillWGData.GetSkillActionStr(self.obj_type, self.attack_skill_id, self.attack_index)
        if anim_name ~= nil and anim_name ~= "" then
            self.action_time_record = self:GetActionTimeRecord(anim_name)
            if self.action_time_record and self.action_time_record.speed then
                local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
                main_part:SetFloat(anim_name .. "_speed", self.action_time_record.speed)
            end
        end
    else
        if self.action_time_record ~= nil and self:IsAtkPlaying() and self.anim_name ~= nil and self.anim_name ~= "" then
            anim_name = self.anim_name
        end
    end

    Character.EnterStateAttack(self, anim_name)
end

function Role:OnAnimatorBegin(param, state_info)
    Character.OnAnimatorBegin(self, param, state_info)
    self:ReqYueji()
end

function Role:SetAttackIndex(index)
    self.attack_index = index

    if self:IsTianShenAppearance() and self.shaungsheng_tianshen_obj then --变身
		self.shaungsheng_tianshen_obj:SetAttackIndex(index)
	end
end

function Role:CancelPlayActionBackFun()
    if self.play_action_back_fun then
        GlobalTimerQuest:CancelQuest(self.play_action_back_fun)
    end
    self.play_action_back_fun = nil
end

-- 攻击
function Role:DoAttack(skill_id, target_x, target_y, target_obj_id, target_type, awake_level, use_type, skill_type)
    self:CancelPlayActionBackFun()
    Character.DoAttack(self, skill_id, target_x, target_y, target_obj_id, target_type, awake_level, use_type, skill_type)

	if self:IsTianShenAppearance() and self.shaungsheng_tianshen_obj then --变身
		self.shaungsheng_tianshen_obj:DoAttack(skill_id, target_x, target_y, target_obj_id, target_type, awake_level, use_type, skill_type)
	end
end

-- 攻击 - 无目标
function Role:DoAttackForNoTarget(skill_id, awake_level, x, y, obj)
    self:CancelPlayActionBackFun()
    Character.DoAttackForNoTarget(self, skill_id, awake_level , x, y, obj)

	if self:IsTianShenAppearance() and self.shaungsheng_tianshen_obj then --变身
		self.shaungsheng_tianshen_obj:DoAttackForNoTarget(skill_id, awake_level, x, y, obj)
	end
end

-- 攻击动作结束处理
function Role:AttackActionEndHandle()
    if self.action_time_record ~= nil and self.action_time_record.has_back then
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        local part_obj = part:GetObj()
        if part_obj == nil or IsNil(part_obj.gameObject) then
            return
        end

        self.play_action_back_fun = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnPlayActionBackEnd, self), self.action_time_record.back_time)
        local anim_name = self.anim_name.."_back"
        if self:IsRidingFightMount() then
            self:CrossAction(SceneObjPart.Mount, anim_name, false)
        else
            self:CrossAction(SceneObjPart.Main, anim_name)
        end
    else
        self:ChangeToCommonState()
    end
end

function Role:OnPlayActionBackEnd()
    if self:IsDeleted() then
        self:CancelPlayActionBackFun()
    else
        self:ChangeToCommonState()
    end
end
--攻击 end]]

-- 跃击(天神技能)
function Role:ReqYueji()
    if not self:IsMainRole() then
        return
    end

    local cfg = SkillWGData.Instance:GetBeforeSkillCfg(self.attack_skill_id)
    if cfg == nil then
        return
    end
    
    if cfg.skill_type ~= FRONT_SKILL_TYPE.YUE_JI then
        return
    end

    if 1 == self.vo.is_shadow then
        return
    end

    if not self:IsTianShenAppearance() then
        return
    end

    local p_pos_x, p_pos_y = self:GetLogicPos()
    -- if self.attack_target_obj and not self.attack_target_obj:IsDeleted() then
    --     t_pos_x, t_pos_y = self.attack_target_obj:GetLogicPos()
    --     x, y = self.attack_target_obj:GetRealPos()
    -- else
    --     t_pos_x, t_pos_y = self.attack_target_pos_x, self.attack_target_pos_y
    --     x, y = GameMapHelper.LogicToWorld(t_pos_x, t_pos_y)
    -- end
    local t_pos_x, t_pos_y, x, y = self.attack_target_pos_x, self.attack_target_pos_y, 0, 0
    t_pos_x, t_pos_y = self:GetMovePosByLimit(t_pos_x, t_pos_y)
    x, y = GameMapHelper.LogicToWorld(t_pos_x, t_pos_y)

    if t_pos_x ~= nil and t_pos_y ~= nil and AStarFindWay:IsBlock(t_pos_x, t_pos_y, false) then
    	return
    end

    local dis = GameMath.GetDistance(p_pos_x, p_pos_y, t_pos_x, t_pos_y, true)
    local speed = dis -- 动作时间先写死1
    self:SetFightMovePos(t_pos_x, t_pos_y)
    if speed > 0 then
        self.draw_obj:MoveTo(x, y, speed)

        self.draw_obj:SetMoveCallback(function (flag)
            if flag == 2 then
                self:StopMove()
            else
                self:YuejiEnd()
            end
        end)
    else
        self:YuejiEnd()
    end

    if self:IsMainRole() then
        MountWGCtrl.Instance:SendMountGoonReq(0)
        FightWGCtrl.Instance:SendChongfengReq(t_pos_x, t_pos_y, SKILL_RESET_POS_TYPE.SKILL_RESET_POS_TYPE_YUEJI)
    end
end

-- 跃击end
function Role:YuejiEnd()
    if self:IsDeleted() then
        return
    end

    self.draw_obj:SetMoveCallback(nil)
    if 0 ~= self.fight_move_pos.x and 0 ~= self.fight_move_pos.y then
        self:SetLogicPos(self.fight_move_pos.x, self.fight_move_pos.y)
        self:SetFightMovePos(0, 0)
    end

    if self:IsMainRole() and self.attack_skill_id ~= nil and self.attack_skill_id ~= 0 then
        -- self.attack_skill_id = SkillWGData.Skill_Id_285
        self:SendFight(true)

        -- 285 技能需要客户端加CD
        -- local skill_info = SkillWGData.Instance:GetSkillInfoById(SkillWGData.Skill_Id_285)
        -- if not skill_info then return end
        -- local server_time = TimeWGCtrl.Instance:GetServerTime()
        -- skill_info.last_perform = server_time
        -- SkillWGData.Instance:ChangeSkillInfo(skill_info)
        -- self.attack_skill_id = 0
    end
end

-- 冲锋技能的落点
function Role:SetFightMovePos(x, y)
    if not self.fight_move_pos then
        self.fight_move_pos = u3d.vec2(0, 0)
    end
    self.fight_move_pos.x = x
    self.fight_move_pos.y = y
end

-- 进入战斗状态
function Role:EnterFightState()
    Character.EnterFightState(self)

    local follow_ui = self:GetFollowUi()
    if follow_ui and self:CanShowHpOnScene() then
        follow_ui:SetHpVisiable(true)
    end

    self:ChangeSkillHalo()
end

-- 离开战斗状态
function Role:LeaveFightState()
    Character.LeaveFightState(self)
    local follow_ui = self:GetFollowUi()
    if follow_ui and not self:IsRealDead() then
        follow_ui:SetHpVisiable(false)
    end

    self:ChangeSkillHalo()
end

-- 设置攻击模式
function Role:SetAttackMode(attack_mode)
    self.vo.attack_mode = attack_mode
end

-- 被打
function Role:OnBeHit(real_blood, deliverer, skill_id)
    if real_blood >= 0 or self.vo.hp <= 0 then
        return
    end

    Character.OnBeHit(self, real_blood, deliverer, skill_id)
end

-- 采集 是否采集状态
function Role:GetIsGatherState()
    return self.is_gather_state
end

-- 采集 设置状态
function Role:SetIsGatherState(is_gather_state, caiji_type, time)
    self.is_gather_state = is_gather_state

    if is_gather_state then
        if (caiji_type == nil) then
            self:ChangeFollowUIGather()
        end
    else
        self:ChangeFollowUIGather()
    end

    -- 采集状态 清除交互残留
    self:TryClearInteractiveEffectsAndSounds()
    if not self.draw_obj then
        return
    end

    self.caiji_type = caiji_type
    local scene_type = Scene.Instance:GetSceneType()
    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if is_gather_state then
        if self:IsXMZCar() then  -- 仙盟战变身
            local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
            local blue_resid = other_cfg.blue_caiji_resid
            local red_resid = other_cfg.red_caiji_resid
            local user_info = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
            local need_change_part_id = blue_resid

            if user_info and self:IsMainRole() then
                need_change_part_id = user_info.side == 0 and blue_resid or red_resid
            else
                need_change_part_id = self.vo.special_param == 0 and blue_resid or red_resid
            end

            local bundle, asset = ResPath.GetMonsterModel(need_change_part_id)
            self:ChangeMainPartModel(bundle, asset)
        end
        self:CrossToCaiji(time)

    elseif self:IsStand() then
        if self:GetIsInSpring(scene_type) then
            if self:IsWaterWay() then
                self:playHotSpringAction(SceneObjAnimator.Idle_YouYong)
            else
                self:playHotSpringAction(self:CurIdleAni())
            end
        else
            if not self:IsChuanGong() then
                local anim, anim_time = nil, nil
                if main_part:GetIsCurrentAnimatorStateName(SceneObjAnimator.CaiJi2) then
                    anim = self:CurIdleAni()
                    anim_time = 0.65
                else
                    anim = self:CurIdleAni()
                end

                if anim then
                    self:CrossAction(SceneObjPart.Main, anim, nil, anim_time)
                end
            end
        end
    end

    if not is_gather_state then
        if self:IsXMZCar() then  -- 仙盟战变身
            local hs_xs_id = self.vo.appearance_param
            local cfg = GuildBattleRankedWGData.Instance:GetHSXSIdByCfg(hs_xs_id)
            local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
            local blue_resid = cfg and cfg.blue_hs_xs_resid or other_cfg.blue_hs_kong_resid
            local red_resid = cfg and cfg.red_hs_xs_resid or other_cfg.red_hs_kong_resid
            local user_info = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
            if user_info and self:IsMainRole() then
                self.special_res_id = user_info.side == 0 and blue_resid or red_resid
            else
                self.special_res_id = self.vo.special_param == 0 and blue_resid or red_resid
            end

            local bundle, asset = ResPath.GetMonsterModel(self.special_res_id)
            self:ChangeMainPartModel(bundle, asset)
            self.is_bind_fight_mount_action_effect = nil
            self:UpdateRoleActorConfigPrefabData()
        end
    end

    if self:IsRiding() and nil == self.do_mount_up_delay then
        self.do_mount_up_delay = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OnMountUpEnd, self), 0.2)
    end
    self:EquipDataChangeListen()
end

-- 当重生
function Role:OnRealive()
    if self:IsGhost() then
        self:SetAttr("ghost", EnumGhostTpye.NotGhost)
    end

    self:InitShow()
    self:InitAppearance()
    self:ChangeBeauty()
    -- self:ChangePet()
    self:ChangeBeast()
    self:ChangeTaskCallInfo()
    self:ChangeGuard()
    self:ChangeFreeVipImage()

    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:SetHpVisiable(self:IsFightState() and self:CanShowHpOnScene())
    end
end

-- 当死亡
function Role:OnDie()
    self:RemoveModel(SceneObjPart.Weapon, true)
    self:RemoveModel(SceneObjPart.Wing)
    self:RemoveModel(SceneObjPart.Halo)
    self:RemoveModel(SceneObjPart.BaoJu)
    self:RemoveModel(SceneObjPart.QiLinBi)
    self:RemoveModel(SceneObjPart.Waist)
    self:RemoveModel(SceneObjPart.Mask)
    self:RemoveModel(SceneObjPart.ShouHuan)
    self:RemoveModel(SceneObjPart.Tail)
    self:RemoveModel(SceneObjPart.Jianling)
    self:RemoveModel(SceneObjPart.FaZhen)
    self:RemoveModel(SceneObjPart.SkillHalo)
    self:RemoveModel(SceneObjPart.GodOrDemonHalo)

    if self.spirit_obj then
        self.spirit_obj:RemoveModel(SceneObjPart.Main)
        self.spirit_obj:DeleteMe()
        self.spirit_obj = self:DeleteObjFunction(SceneObjType.SpriteObj, self.spirit_obj)
    end

    self.soulboy_obj = self:DeleteObjFunction(SceneObjType.SoulBoyObj, self.soulboy_obj)
    self.beauty_obj = self:DeleteObjFunction(SceneObjType.BeautyObj, self.beauty_obj)
    self.mingjiang_obj = self:DeleteObjFunction(SceneObjType.MingJiangObj, self.mingjiang_obj)
    self.guard_obj = self:DeleteObjFunction(SceneObjType.GuardObj, self.guard_obj)
    self:RemoveWuHun()
    self:RemoveShuangShengTianShen()

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.ETERNAL_NIGHT_FINAL then
        local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType()
        local role_uuid = self.vo.uuid
        local uuid_str = role_uuid.temp_low .. role_uuid.temp_high
        local can_change_ghost = EternalNightWGData.Instance:GetCanChangeGhost(uuid_str)

        if cur_grade_type and cur_grade_type == EternalNightWGData.GradeType.Eliminate and can_change_ghost then
            self:SetAttr("ghost", EnumGhostTpye.IsGhost)
            FightRevengeWGCtrl.Instance:ClearRevengeList()  --变成幽灵后 清除复仇面板
        end
        -- self:SetAttr("ghost", EnumGhostTpye.IsGhost)
    elseif scene_type == SceneType.TEAM_COMMON_TOWER_FB_1  then
        self:SetAttr("ghost", EnumGhostTpye.IsGhost)
    end

    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:SetHpVisiable(self:CanShowHpOnScene())
    end

    self:Landing()
    self:QingGongLandExit()
end

---- [[上坐骑
-- 上坐骑回调
function Role:LoadRideBack()
    self:InvokeMountUpEndCallBack()

    if self:IsStand() and not self:GetIsGatherState() then
        if self:IsRiding() then
            self:CrossAction(SceneObjPart.Main, self:SetRidingActionIdelParam())
        end
    end
end

-- 坐骑 状态改变
function Role:OnMountUpEnd()
    local load_ride_back = BindTool.Bind(self.LoadRideBack, self)
    self.do_mount_up_delay = nil

    if self:IsRiding() then
        self:RemoveWuHun()

        self:UpdateMount(load_ride_back)
        if not self.is_gather_state then
            self.show_fade_in = true
        end

        self:SetUpDownMountEffectLoader()
    else
        self:UpdateRoleActorConfigPrefabData()

        if self.special_res_id ~= 0 then
            self:RemoveModel(SceneObjPart.Mount)
            -- self:RemoveModel(SceneObjPart.FightMount)
            self:RemoveModel(SceneObjPart.Wing)
            self:RemoveModel(SceneObjPart.Halo)
            self:RemoveModel(SceneObjPart.FaZhen)
            self:RemoveModel(SceneObjPart.SkillHalo)
            self:RemoveModel(SceneObjPart.GodOrDemonHalo)
            self:RemoveWuHun()
        else
            if not self:IsQingGong() then
                self:ChangeWuHun()
            end
        end
        
        self:RemoveMonutWithFade()
        if self:IsMainRole() then
            if self.cache_mount_camera_dis then
                Scene.Instance:RecoverCamera()
                self.cache_mount_camera_dis = nil
            end
        end
    end
end

-- 设置坐姿
-- 1 骑马动作 SceneObjAnimator.Mount_Idle
-- 2 站立动作 SceneObjAnimator.Mount_Idle_2
-- 3 盘腿动作 SceneObjAnimator.Mount_Idle_3
-- 4 摩托车动作 SceneObjAnimator.Mount_Idle_4
function Role:SetRidingActionIdelParam()
    if self:IsMultiMountState() then
        local action = self:GetMultiMountRoleRidingAction()

        if action > 0 and MOUNT_RIDING_TYPE[action] then
            return MOUNT_RIDING_TYPE[action]
        end
    end

    local mount_appeid = self:GetCurRidingResId()
    local cfg = NewAppearanceWGData.Instance:GetMountActionCfg(mount_appeid)
    if not IsEmptyTable(cfg) then
        return MOUNT_RIDING_TYPE[cfg.action]
    end
    return MOUNT_RIDING_TYPE[1]
end

-- 设置坐骑跑的时候 人物动作
-- 1 骑马动作 SceneObjAnimator.Mount_Run
-- 2 站立动作 SceneObjAnimator.Mount_Run_2
-- 3 盘腿动作 SceneObjAnimator.Mount_Run_3
-- 4 摩托车动作 SceneObjAnimator.Mount_Run_4
function Role:SetRidingActionRunParam()
    if self:IsMultiMountState() then
        local action = self:GetMultiMountRoleRidingAction()

        if action > 0 and MOUNT_RIDING_RUN_TYPE[action] then
            return MOUNT_RIDING_RUN_TYPE[action]
        end
    end

    local mount_appeid = self:GetCurRidingResId()
    local cfg = NewAppearanceWGData.Instance:GetMountActionCfg(mount_appeid)
    if not IsEmptyTable(cfg) then
        return MOUNT_RIDING_RUN_TYPE[cfg.action]
    end
    return MOUNT_RIDING_RUN_TYPE[1]
end
--上坐骑 end]]


---武魂特殊一下，在主部位加载完后绑定一下位置，总是绑不上去
function Role:OnMainPartLoadCallBack()
	self:CheckUnderShow()
    
    if self.wuhun_obj then
        self.wuhun_obj:ChangeWuHunImageId(self.vo.wuhun_id, self.vo.wuhun_lv)
        self.wuhun_obj:AttachToRoleMainPart()
    end

    if self:IsTianShenAppearance() and self.shaungsheng_tianshen_obj then --天神变身 
        self.shaungsheng_tianshen_obj:ChangeShuangShengImageId(self.vo.shaungsheng_tianshen_aura_id)
        self.shaungsheng_tianshen_obj:AttachToRoleMainPart()
    end
end

-------------------------------------------------拥抱物品--------------------------------------------------
----[[ 拥抱物品
function Role:EmbraceItem(bundle_name, asset_name)
    if (self:IsMainRole() and TaskWGData.Instance:IsXiguaTask()) or self:IsDeleted() then
        if self.embrace_loader then
            self.embrace_loader:Destroy()
        end

        self.embrace_loader = nil
        return
    end

    if self.embrace_loader == nil then
        self.embrace_loader = AllocAsyncLoader(self, "embrace_loader")
    end

    local draw_obj = self:GetDrawObj()
    if draw_obj == nil then
        return
    end

    local hurt_point = draw_obj:GetRealAttachPoint(AttachPoint.Bao)
    if hurt_point == nil then
        return
    end

    self.embrace_data = nil
    self.embrace_loader:SetParent(hurt_point.transform)
    self.embrace_loader:Load(bundle_name, asset_name)
end

-- 拥抱物品 - 设置
function Role:SetEmbraceStatus(value, bundle_name, asset_name)
    if value then
        local old_state = self.is_embrace
        self.is_embrace = true
        self.embrace_data = {bundle_name = bundle_name, asset_name = asset_name}
        if not old_state then
            self:EmbraceItem(bundle_name, asset_name)
        end
    else
        self.is_embrace = false
        if self.embrace_loader then
            self.embrace_loader:Destroy()
        end

        self.embrace_loader = nil
        self.embrace_data = nil
    end
end

-- 拥抱物品 是否拥抱
function Role:IsEmtrace()
    return self.is_embrace
end
--拥抱物品 end]]

-------------------------------------------------特殊跳跃--------------------------------------------------
----[[设置特殊跳跃时间
-- 九蛇岛副本
function Role:SetSpecialJumpSpeed(distance)
    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if main_part ~= nil then
        distance = distance == 0 and REFER_DISTANCE or distance
        main_part:SetFloat("jump_speed", REFER_DISTANCE / distance)
    end
end

-- 执行特殊跳跃
-- 九蛇岛副本
function Role:DoSpecialJump(x, y, call_back)
    self.special_jump_x = x
    self.special_jump_y = y
    self:CalcMoveInfo(x, y)
    self:SetSpecialJumpCallBack(call_back)
    self:SetSpecialJumping(true)
    if self:IsMove() then
        self:ChangeToCommonState()
    end

    self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Jump2)
end

-- 特殊跳跃处理
-- 九蛇岛副本
function Role:SpecialJumpingHandle(elapse_time)
    local distance = elapse_time * self.special_jump_speed
    local mov_dir = u3d.v2Mul(self.move_dir, distance)
    local now_pos = u3d.v2Add(self.real_pos, mov_dir)
    self:SetSpecialRealPos(now_pos.x, now_pos.y)

    local delta_pos = u3d.v2Sub(self.move_end_pos, now_pos)
    local residue = u3d.v2Length(delta_pos)
    if residue <= 1 then
        self:OnSpecialMoveEnd()
    end
end

-- 特殊跳跃结束
-- 九蛇岛副本
function Role:OnSpecialMoveEnd()
    if self.special_jumping == true then
        self:SetSpecialJumping(false)
        local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
        if self.draw_obj:IsDeleted() == false and self:IsRealDead() == false then
            if main_part ~= nil then
                self:CrossAction(SceneObjPart.Main, self:CurIdleAni())
            end
        end

        self:SetLogicPos(self.special_jump_x, self.special_jump_y)

        if self.special_jump_call_back ~= nil then
            self.special_jump_call_back()
        end
    end
end

-- 特殊跳跃状态
-- 九蛇岛副本
function Role:SetSpecialJumping(state)
    self.special_jumping = state
end

-- 特殊跳跃回调
-- 九蛇岛副本
function Role:SetSpecialJumpCallBack(call_back)
    self.special_jump_call_back = call_back
end
-- 特殊跳跃 end]]

-------------------------------------------------温泉--------------------------------------------------

---- [[温泉
--温泉场景中的攻击
--@自己实现攻击动画是因为原来那个动画在水中攻击时逻辑不好处理
function Role:DoHotSpringAttack(callback, target)
    --雪球
    local action_name = nil
    local idle_name = nil
    local action_end_time = 0
    self.hot_spring_skill = 0
    if self.vo.shuangxiu_action_type == HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_SNOW then
        if self.vo.shuangxiu_attack_state == 0 then
            --受到攻击
            action_name = SceneObjAnimator.beipo_wq
        elseif self.vo.shuangxiu_attack_state == 1 then
            --发起攻击
            action_name = SceneObjAnimator.TouZhi2
            --self.hot_spring_skill = HotSpringWGData.XUEQIU_SKILL
        end
        action_end_time = HotSpringActionConfig[0].touzhi
        idle_name = self:CurIdleAni()

    --泼水
    elseif self.vo.shuangxiu_action_type == HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_INTIMIDATION then
        if self.vo.shuangxiu_attack_state == 0 then
            --受到攻击
            action_name = SceneObjAnimator.beipo_wq
        elseif self.vo.shuangxiu_attack_state == 1 then
            action_name = SceneObjAnimator.TouZhi2
            --self.hot_spring_skill = HotSpringWGData.SHUIQIU_SKILL
        end
        action_end_time = HotSpringActionConfig[0].touzhi2
        idle_name = SceneObjAnimator.Idle_YouYong
    end

    --播放动画
    if action_name ~= nil then
        self:playHotSpringAction(action_name)
    end 
   --[[ if target and self.hot_spring_skill > 0 then
        local target_x, target_y = target:GetLogicPos()
        self:DoAttack(self.hot_spring_skill, target_x, target_y, target:GetObjId())
        return
    elseif action_name ~= nil then
        self:playHotSpringAction(action_name)
    end--]]

    --恢复为idle状态
    self:RemoveHotSpringActionDelayTime()
    if action_end_time ~= 0 then
        self.cancel_quest_of_hot_spring_action = GlobalTimerQuest:AddDelayTimer(
            function ()
                if self:IsDeleted() or not self.draw_obj then
                    return
                end
                if callback then
                    callback()
                end
                if action_name then
                    self:playHotSpringAction(idle_name)
                end
            end,action_end_time)
    end
end

--温泉场景中的攻击 计时器移除
function Role:RemoveHotSpringActionDelayTime()
    if self.cancel_quest_of_hot_spring_action then
        GlobalTimerQuest:CancelQuest(self.cancel_quest_of_hot_spring_action)
        self.cancel_quest_of_hot_spring_action = nil
    end
end

-- 温泉 特殊图片
function Role:UpdateHotSpringSpecialImage()
    if not self:GetIsInSpring() then
        return
    end

    if self.vo.shuangxiu_action_type == HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_SNOW then
        if self.vo.shuangxiu_attack_state == 0 then
            self:ChangeToCommonState()
            self:SetHotSpringSnowTarget()
        elseif self.vo.shuangxiu_attack_state == 1 then

        end
    elseif self.vo.shuangxiu_action_type == HOTSPRING_ACTION_TYPE.HOTSPRING_ACTION_TYPE_INTIMIDATION then
        if self.vo.shuangxiu_attack_state == 0 then
            self:ChangeToCommonState()
        elseif self.vo.shuangxiu_attack_state == 1 then
        end
    end
end

-- 温泉 雪球目标
function Role:SetHotSpringSnowTarget()
    if not self.vo.is_in_snow then
        self.vo.is_in_snow = true
    end

    if self.vo.is_in_snow then
        if nil ~= self.delay_remove_snow_eff_target then
            GlobalTimerQuest:CancelQuest(self.delay_remove_snow_eff_target)
            self.delay_remove_snow_eff_target = nil
        end

        local role_id = self.vo.role_id
        self.delay_remove_snow_eff_target = GlobalTimerQuest:AddDelayTimer(function()
            -- self:SetAttr("special_appearance",0)
            self:SetAttr("special_appearance", SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON)
            self.vo.is_in_snow = false
            HotSpringWGCtrl.Instance:HideFace()
            if nil ~= self.delay_remove_snow_eff_target then
                GlobalTimerQuest:CancelQuest(self.delay_remove_snow_eff_target)
                self.delay_remove_snow_eff_target = nil
            end
        end, 5)
    end
end

-- 温泉是否可移动
function Role:IsHotSpringCanMove()
    return self.vo.is_in_snow
end

-- 温泉 初始化水状态
function Role:InitWaterState()
    if self.draw_obj then
        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic then
            local flag = scene_logic:IsCanCheckWaterArea() and true or false
            self.draw_obj:SetCheckWater(flag)
            if flag then
                self.draw_obj:SetWaterHeight(COMMON_CONSTS.WATER_HEIGHT)
                self.draw_obj:SetEnterWaterCallBack(BindTool.Bind(self.EnterWater, self))
            end
        end
    end
end

-- 温泉 设置是否在船上
function Role:SetIsInBoat(is_in_boat)
    self.is_in_boat = is_in_boat
    if is_in_boat then
        self:HotSpringEffectStop()
    end
end

-- 温泉 刷新动画 温泉双休搞特殊
function Role:ReflushAnimation(is_init)
    local part_obj = self.draw_obj:GetPart(SceneObjPart.Wing)
    part_obj:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)

    part_obj = self.draw_obj:GetPart(SceneObjPart.BaoJu)
    part_obj:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)

    if not self:GetIsInSpring() then
        return
    end
    
    if self:ShuangXiuState() == SHUANGXIU_TYPE.IS_SHUANGXIU then
        self:playHotSpringAction(self.is_vip_boat and SceneObjAnimator.Pass or SceneObjAnimator.SHUANG_XIU, is_init)
    else
        if self:IsWaterWay() then
            self:playHotSpringAction(SceneObjAnimator.Idle_YouYong, is_init)
        else
            self:playHotSpringAction(self:CurIdleAni())
        end
    end
end
--温泉 end]]

-------------------------------------------------巡游--------------------------------------------------
----[[巡游
--巡游 更新
function Role:UpdateXunyou(now_time, elapse_time)
    if self:IsInXunYou() and self.update_marry_time and self.update_marry_time < now_time then
        if self:IsMainRole() then
            if self:IsQingGong() then
                self.draw_obj:StopQinggong()
            end

            GuajiWGCtrl.Instance:StopGuaji() --停止挂机
            local marry_info = MarryWGData.Instance:GetXunYouPos()
            self.marry_role = Scene.Instance:GetObjectByObjId(marry_info.obj_id)
            if self.marry_role and marry_info.is_own ~= 0 then
                self.partner_point2 = self.marry_role.draw_obj:_TryGetPartObj(SceneObjPart.Main)
                if self.partner_point2 then
                    if not IsNil(MainCameraFollow) then
                        MainCameraFollow.Target = self.partner_point2.transform
                    end
                    self.draw_obj.root.transform:SetParent(self.partner_point2.transform)
                end
            end
        end
        
        self.update_marry_time = now_time + 1
    end
end

-- 巡游 是否巡游者 只有新郎和新娘
function Role:SetMarryFlag(state)
    local old_state = self.is_xunyou
    self.is_xunyou = state ~= 0
    if self.is_xunyou and self:IsMainRole() then
        self:TryLeaveSit()
    end

    if old_state ~= self.is_xunyou and self:IsMainRole() then
        if self.is_xunyou then
            if self.is_xunyou and not IsNil(MainCameraFollow) and not IsNil(MainCamera) and CAMERA_TYPE ~= CameraType.Fixed then
                self.xunyou_reset_info = {}
                self.xunyou_reset_info.dis = MainCameraFollow.Distance
                local angle = MainCamera.transform.parent.transform.localEulerAngles
                self.xunyou_reset_info.x = angle.x
                self.xunyou_reset_info.y = angle.y
                self.xunyou_reset_info.mode = CAMERA_TYPE
            end

            -- 因为巡游时玩家可以自己切换摄像机模式，可是策划要求进入巡游又要强制先锁定，为了不影响旧逻辑，模仿点击
            if CAMERA_TYPE ~= CameraType.Fixed then
                MainuiWGCtrl.Instance:ChangeCameraMode()
            else
                -- 如果已经是锁定，那么刷新摄像机，把距离设置为巡游时的锁定摄像机距离
                Scene.Instance:UpdateCameraSetting(false)
            end
        else
            -- 策划要求，进巡游前是什么样的镜头参数，巡游结束就怎么样
            if self.xunyou_reset_info ~= nil then
                if not IsNil(MainCameraFollow) then
                    -- 因为玩家在巡游时可以移动摄像机，所以把巡游前的数据覆盖过去
                    local list = {[1] = {HOT_KEY.CAMERA_ROTATION_X, self.xunyou_reset_info.x},
                                [2]  = {HOT_KEY.CAMERA_ROTATION_Y, self.xunyou_reset_info.y},
                                [3] = {HOT_KEY.CAMERA_DISTANCE, self.xunyou_reset_info.dis}}
                    SettingWGCtrl.Instance:SendChangeHotkeyReq(list)

                    SettingWGData.Instance:SetSettingDataListByKey(HOT_KEY.CAMERA_ROTATION_X, self.xunyou_reset_info.x)
                    SettingWGData.Instance:SetSettingDataListByKey(HOT_KEY.CAMERA_ROTATION_Y, self.xunyou_reset_info.y)
                    SettingWGData.Instance:SetSettingDataListByKey(HOT_KEY.CAMERA_DISTANCE, self.xunyou_reset_info.dis)

                    MainuiWGCtrl.Instance:ChangeCameraMode()
                    Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.XUNYOU, self.xunyou_reset_info.x, self.xunyou_reset_info.y, self.xunyou_reset_info.dis, nil)
                end
            else
                Scene.Instance:UpdateCameraSetting(false)
            end

            self.xunyou_reset_info = nil
        end

        MarryWGCtrl.Instance:SetXunYouMainUiState(self.is_xunyou)
        MainuiWGCtrl.Instance:UpdateCameraAutoRotate()
    end

    if not self.is_xunyou then
        self.need_change_camera_mode = false
    end

    if not self.is_xunyou then
        self.draw_obj.root.transform:SetParent(SceneObjLayer.transform)
        self.draw_obj.root.transform.localScale = u3dpool.vec3(1, 1, 1)
        self.draw_obj:SetPosition(self.real_pos.x, self.real_pos.y)
        self.partner_point2 = nil
        self.update_marry_time = 0
    end

    if self.is_xunyou then
        self:ForceSetVisible(false)
        self:ForceShadowVisible(false)
        if self.follow_ui then
			self.follow_ui:ForceSetVisible(false)
		end
    else
        if self:CheckRoleIsForceHideFollowUi() then
            self:ForceShadowVisible(true)
            self:CancelForceSetVisible()
            if self.follow_ui then
                self.follow_ui:CancelForceSetVisible()
            end
        end
    end
end

--巡游 角色是否在巡游中
function Role:IsInXunYou()
    return self.is_xunyou
end
--]]


-------------------------------------------------轻功-御剑--------------------------------------------------
----[[轻功-御剑
-- 执行剑身晃动(执行完要回到当前状态)
function Role:ExecuteSwordShakeAction()
    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    if part then
        self:CancelMitsurugiDelayTimer()
        local action_end_time = part:GetClipLength(AnimatorParameters.MitsurugiRest)
        self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRest, true)
        self.mitsurugi_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
            self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRest, false)
        end, action_end_time)
    end
end

-- 执行御剑idle
function Role:ExecuteSwordIdleAction()
    self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRun, false)
    self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRest, false)
    self:DestroySwordSprintEff()
end

-- 执行御剑移动
function Role:ExecuteSwordMoveAction()
    self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRun, true)
end

--轻功 开始御剑
function Role:Jump()
    MountWGCtrl.Instance:SendMountGoonReq(0)
	local qinggong_obj = ResPreload["QingGongObject1_1"]
    local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
    self.sword_expedite_time = other_cfg and other_cfg.sword_expedite_time or 0
    self.sword_shake_time = other_cfg and other_cfg.sword_shake_time or 0
	self.draw_obj:Mitsurugi(qinggong_obj)
	self.draw_obj:SetMitsurugi(MITSURUGI_HIGHT)
    self.draw_obj:SetDrag(0.1)
	self:MitsurugiEnable(true)
    -- 轻功整体改成御剑，动作采用之前的轻功三
	self.qinggong_index = 3
    self.is_force_landing = false
    self.has_play_qinggong_land = false
    self.sword_mitsurugi_status = SPRITE_MOVE_STATUS.IDLE
	self:SetActionTrigger(SceneObjPart.Main, AnimatorParameters.IsMitsurugi)
	local jump_eff_pos = self.draw_obj:GetRoot().gameObject.transform.position
	self:PlayJumpEffect(self.qinggong_index, jump_eff_pos)
	self:RemoveWuHun()
end

--御剑 跳跃特效
function Role:PlayJumpEffect(prof, pos)
    local jump_bundle, jump_asset = ResPath.GetRoleJumpEff(prof)
    if jump_bundle and jump_asset then
        EffectManager.Instance:PlayControlEffect(self, jump_bundle, jump_asset, pos)
    end
end

-- 御剑 每段动画开始（进入）
function Role:QingGongBeginEnter(index)
	if self:IsMitsurugi() then
		self:JumpQingGongMount()
	end
end

--御剑 轻功脚踩的灵剑
function Role:JumpQingGongMount()
    self:ReleaseQingGongMount()

    local rider_sword_carrier_func = function()
        local vo = self.vo
        local lingchong_appeid = vo.lingchong_appeid ~= 0 and vo.lingchong_appeid or 10101001
        local bundle_mount, asset_mount = ResPath.GetPetModel(lingchong_appeid)
        local role_foot_point = self.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
        if role_foot_point then
            self.qinggongmount_async_loader = AllocAsyncLoader(self, "JumpQingGongMount"..vo.role_id)
            self.qinggongmount_async_loader:SetParent(role_foot_point.transform)
            self.qinggongmount_async_loader:Load(bundle_mount, asset_mount,
                function(obj)
                    if IsNil(obj) then
                        return
                    end

                    if not self.draw_obj or self.is_force_landing then
                        self.qinggongmount_async_loader:Destroy()
                        return
                    end
    
                    self.jump_qinggong_mount = obj
                    if IsNil(role_foot_point.gameObject) then
                        self:ReleaseQingGongMount()
                    end

                    local animator = obj:GetComponent(typeof(UnityEngine.Animator))
                    if not IsNil(animator) then
                        animator:CrossFadeInFixedTime(SceneObjAnimator.Sword_Idle, 0.2)
                    end 
                    obj.transform.localScale = u3dpool.vec3(4, 4, 4)
            end)
        end
    end

    if self:IsMitsurugi() then
        self:CancelMitsurugiDelayTimer()
        self.mitsurugi_delay_timer = GlobalTimerQuest:AddDelayTimer(rider_sword_carrier_func, 0.15)
    else
        rider_sword_carrier_func()
    end
end

--御剑 释放脚踩的灵剑
function Role:ReleaseQingGongMount()
    local rider_release_carrier_func = function()
        if self.jump_qinggong_mount and not IsNil(self.jump_qinggong_mount) then
            self.qinggongmount_async_loader:Destroy()
            self.jump_qinggong_mount = nil
        end
    end

    if self:IsMitsurugi() then
        self:CancelMitsurugiDelayTimer()
        self.mitsurugi_delay_timer = GlobalTimerQuest:AddDelayTimer(rider_release_carrier_func, 0.55)
    else
        rider_release_carrier_func()
    end
end

--移除回调
function Role:CancelMitsurugiDelayTimer()
    if self.mitsurugi_delay_timer then
        GlobalTimerQuest:CancelQuest(self.mitsurugi_delay_timer)
        self.mitsurugi_delay_timer = nil
    end
end

-- 轻功 每段动画开始（退出）
function Role:QingGongBeginExit(index)
end

--轻功 状态改变
function Role:QingGongStateChange(state)
	self.cur_qinggong_state = state
	local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	if state == QingGongState.Stop then
        self.draw_obj:SetDrag(0.1)
        self.qinggong_index = 0
		self:SetIsMoving(false)
    	self.state_machine:ChangeState(SceneObjState.Stand)
		self:DestroyTrail() -- 清除拖尾特效
		self:ReleaseQingGongMount() -- 清除第四段坐骑
		self:QingGongLandExit()
		Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
	elseif state == QingGongState.OnGround then
		-- 关闭动态模糊
		self.draw_obj:SetDrag(0.1)
		local root = self.draw_obj.root
		self:SetRealPos(root.transform.position.x, root.transform.position.z)
		-- 播放落地特效
		local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_yujian_jump")
		EffectManager.Instance:PlayControlEffect(self, bundle_name, asset_name, root.transform.position)
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Down))
		self:DestroyTrail() -- 清除拖尾特效
		self:ReleaseQingGongMount() -- 清除第四段坐骑
	elseif state == QingGongState.Up then
		-- 增加拖尾特效
		local bundle_name2, asset_name2 = ResPath.GetEnvironmentBanyunEffect("tongyong_tuowei")
		local left_hand = self.draw_obj:GetAttachPoint(AttachPoint.LeftHand)
		local right_hand = self.draw_obj:GetAttachPoint(AttachPoint.RightHand)
		if nil == self.left_hand_effect and left_hand then
			self.left_hand_effect = AllocAsyncLoader(self, "left_hand_effect")
			self.left_hand_effect:SetIsUseObjPool(true)
			self.left_hand_effect:SetIsInQueueLoad(true)
			self.left_hand_effect:SetParent(left_hand)
			self.left_hand_effect:Load(bundle_name2, asset_name2)
		end
		
		if nil == self.right_hand_effect and right_hand then
			self.right_hand_effect = AllocAsyncLoader(self, "right_hand_effect")
			self.right_hand_effect:SetIsUseObjPool(true)
			self.right_hand_effect:SetIsInQueueLoad(true)
			self.right_hand_effect:SetParent(right_hand)
			self.right_hand_effect:Load(bundle_name2, asset_name2)
		end
		self:ForceShadowVisible(false)
        if self.follow_ui then
            self.follow_ui:ForceSetVisible(false)
        end
        self.draw_obj:GetPart(SceneObjPart.BaoJu):SetVisible(false)
	elseif state == QingGongState.Down then
        if self.qinggong_index < COMMON_CONSTS.MAX_QING_GONG_COUNT or self.is_force_landing then
            if not self:IsDead() then
                self:SetActionTrigger(SceneObjPart.Main, AnimatorParameters.MitsurugiDown)
            end
        end

        Scene.Instance:UpdateVolumeMotionBlur(MotionBlurStage.SpeedCut)
	elseif state == QingGongState.ReadyToGround then
        if self.is_force_landing then
            if not self:IsDead() then
                self:SetActionTrigger(SceneObjPart.Main, AnimatorParameters.MitsurugiLand)
            end

            self.has_play_qinggong_land = true
        elseif self.qinggong_index == COMMON_CONSTS.MAX_QING_GONG_COUNT then
            if not self:IsDead() then
                self:SetActionTrigger(SceneObjPart.Main, AnimatorParameters.MitsurugiLand)
            end
        end

        self.ready_to_ground = true
	end
end

-- 获取当前的御剑状态
function Role:GetQingGongState()
    return self.cur_qinggong_state
end

-- 御剑 停止
function Role:StopQinggong()
	self.draw_obj:StopQinggong()
end

-- 御剑 停止
function Role:MoveSwordDown()
    if not self:IsMainRole() and self:IsMitsurugi() then
        self:Landing()
    end
end

-- 御剑 冲刺
function Role:MoveSwordSprint(protocol)
    if not self:IsMainRole() and self:IsMitsurugi() then
        self:DoSwordSprint(true, protocol.x, protocol.y, protocol.height)
    end
end

-- 玩家御剑移动(冲刺)
function Role:DoSwordSprint(is_logic, logic_pos_x, logic_pos_y, height)
    local pos_x = logic_pos_x
    local pos_y = logic_pos_y
    if is_logic then
        pos_x, pos_y = GameMapHelper.LogicToWorld(logic_pos_x, logic_pos_y)
    end

    local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
    local move_sword_sprint_speed = other_cfg and other_cfg.move_sword_sprint_speed or self:GetMoveSpeed()
    local min_move_sword_sprint_speed = other_cfg and other_cfg.min_move_sword_sprint_speed or self:GetMoveSpeed()
    local move_sword_sprint_distance = other_cfg and other_cfg.move_sword_sprint_distance or 12
	local min_move_sword_sprint_distance = other_cfg and other_cfg.min_move_sword_sprint_distance or 12

    -- 计算时间
    local min_move_time = min_move_sword_sprint_distance / min_move_sword_sprint_speed
    local move_time = move_sword_sprint_distance / move_sword_sprint_speed
    local all_move_time = min_move_time + move_time
    local is_now_show_eff = self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.EXPEDITE
    self.sword_mitsurugi_status = SPRITE_MOVE_STATUS.SPRINT
    local min_move_time_per = min_move_time / all_move_time
    local move_time_per = move_time / all_move_time

    --生成特效位移
    self.cur_sword_shake_time = 0
    self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRun, false)
    self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRest, false)     -- 打断剑身晃动
    self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRush, true)
    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    if part then
        local action_end_time = part:GetClipLength(AnimatorParameters.MitsurugiRush)
        local move_speed = (action_end_time * min_move_time_per) / min_move_time
        self:SetActionFloat(SceneObjPart.Main, AnimatorParameters.MitsurugiRushSpeed, move_speed)
    end

    -- 移动第一段
    -- if self:IsMainRole() then
        -- Scene.Instance:UpdateVolumeMotionBlur(MotionBlurStage.Spurt)
    -- end

    self:ShowSwordSprintEff()
    --self:DoStand()  -- 切换到站立状态开始冲刺
    self.draw_obj:MoveTo(pos_x, pos_y, min_move_sword_sprint_speed, height)
    GlobalTimerQuest:CancelQuest(self.qinggong_rush_delay)
    self.qinggong_rush_delay = GlobalTimerQuest:AddDelayTimer(function ()
        -- 移动第二段
        self:SetActionBool(SceneObjPart.Main, AnimatorParameters.MitsurugiRush, false)
        self.draw_obj:MoveTo(pos_x, pos_y, move_sword_sprint_speed, height)
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        if part then
            local action_end_time = part:GetClipLength(AnimatorParameters.MitsurugiRush)
            local move_speed = (action_end_time * move_time_per) / move_time
            self:SetActionFloat(SceneObjPart.Main, AnimatorParameters.MitsurugiRushSpeed, move_speed)
        end

        GlobalTimerQuest:CancelQuest(self.qinggong_rush_delay)
        self.qinggong_rush_delay = GlobalTimerQuest:AddDelayTimer(function ()
            if (not is_now_show_eff) or self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.IDLE then
                self:DestroySwordSprintEff()
            end

            self.sword_mitsurugi_status = SPRITE_MOVE_STATUS.STOP
            -- 关闭动态模糊
            --if self:IsMainRole() then
                -- Scene.Instance:UpdateVolumeMotionBlur(MotionBlurStage.None)
            --end

            self.move_end_pos.x = pos_x
            self.move_end_pos.y = pos_y
            self:SetRealPos(self.move_end_pos.x, self.move_end_pos.y)
        end, move_time)
    end, min_move_time)
end

-- 御剑 着陆
function Role:Landing()
	if (not self:IsMitsurugi()) or (self:IsMitsurugi() and self.cur_qinggong_state ~= QingGongState.Mitsurugi) then
		return
	end

	if not self.is_force_landing then
		self.is_force_landing = true
		self.has_play_qinggong_land = true
		Scene.Instance:SendRoleLandingReq()
		self.draw_obj:SetDrag(0.3)
		self.draw_obj:StopMitsurugi()
		self:ReleaseQingGongMount()
	end
end

-- 从一半开始播放
function Role:Jump2(qinggong_index, dir, height, percent)
    self:QingGongEnable(true)
    self.qinggong_index = qinggong_index > 0 and qinggong_index or 1
    self.is_landed = false
    self.is_force_landing = false
    self.has_play_qinggong_land = false

    local target = u3d.v3Add(self:GetLuaPosition(), u3d.v3Mul(dir, 1000))

    local prof = self:GetProf()
    local qinggong_obj = ResPreload[string.format("QingGongObject%s_%s", prof, qinggong_index)]
    if nil ~= qinggong_obj then
        self.draw_obj:JumpFormAir(height, target, qinggong_obj, percent)
    else
        print_error("qinggong_obj is nil", qinggong_index, self:GetProf(), prof)
    end

    if qinggong_index == 1 then
        self.draw_obj:SetDrag(0.1)
    elseif qinggong_index == 2 then
        self.draw_obj:SetDrag(2)
    elseif qinggong_index == 3 then
        self.draw_obj:SetDrag(0.1)
    elseif qinggong_index == 4 then
        self.draw_obj:SetDrag(3)
    end

    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    main_part:Play("QingGongAir" .. qinggong_index, 0, 0)
end

--轻功 着陆退出
function Role:QingGongLandExit()
	if self.cur_qinggong_state ~= QingGongState.Up then
		self.is_landed = true
		FunctionGuide.GUIDE_JUMP_TARGET = nil
	end

	self.is_force_landing = false
	self.has_play_qinggong_land = false

	if not self.is_joystick_touched then
		self:QingGongEnable(false)
	end

    if not self:IsDead() then
        self:EnterStateStand()
    end

	self:SetMitsurugi(false)
	self:ForceShadowVisible(true)
    if self.follow_ui then
        self.follow_ui:ForceSetVisible(true)
    end
    self.draw_obj:GetPart(SceneObjPart.BaoJu):SetVisible(true)
	self:ChangeWuHun()
end

--轻功 可能性
function Role:QingGongEnable(enabled)
    self.is_qinggong = enabled
    self.draw_obj:QingGongEnable(enabled)
    if enabled then
        self.draw_obj:SetStateChangeCallBack(BindTool.Bind(self.QingGongStateChange, self))
        self.draw_obj:SetGravityMultiplier(2)
        self.draw_obj:SetJumpHorizonSpeed(12)
        self.draw_obj:SetDrag(0.1)
    end
end

-- 轻功 清除拖尾特效
function Role:DestroyTrail()
	if self.left_hand_effect then
		self.left_hand_effect:Destroy()
		self.left_hand_effect = nil
	end

	if self.right_hand_effect then
		self.right_hand_effect:Destroy()
		self.right_hand_effect = nil
	end
end

--轻功 是否在轻功
function Role:IsQingGong()
    return not self.is_landed
end

--轻功 是否在地面
function Role:IsOnGround()
    return self.cur_qinggong_state == QingGongState.OnGround
end

function Role:SaveMoveTarget(target)
    self.move_target = target
end

--御剑
function Role:MitsurugiEnable(enabled)
    self:SetMitsurugi(enabled)
    if enabled then
        self.draw_obj:SetStateChangeCallBack(BindTool.Bind(self.QingGongStateChange, self))
        self.draw_obj:SetGravityMultiplier(2)
        self.draw_obj:SetJumpHorizonSpeed(12)
        self.draw_obj:SetDrag(0.1)
    end
end

--轻功 是否在轻功冲刺
function Role:IsSwordSprint()
    return self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.SPRINT
end

--轻功 是否在运动中或是加速中
function Role:IsSwordMove()
    return self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.MOVE 
    or self.sword_mitsurugi_status == SPRITE_MOVE_STATUS.EXPEDITE
end

-- 加速或冲刺特效
function Role:ShowSwordSprintEff()
    -- local eff_yujian_root = self.draw_obj:GetAttachPoint(AttachPoint.Root)
    -- local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_yujian")

    -- if nil == self.sword_sprint_effect_loder and eff_yujian_root then
    --     self.sword_sprint_effect_loder = AllocAsyncLoader(self, "sword_sprint_effect")
    --     self.sword_sprint_effect_loder:SetIsUseObjPool(true)
    --     self.sword_sprint_effect_loder:SetIsInQueueLoad(true)
    --     self.sword_sprint_effect_loder:SetParent(eff_yujian_root)
    --     self.sword_sprint_effect_loder:Load(bundle_name, asset_name)
    -- end

    if self:IsMainRole() then
        local param_t = {}
        param_t.effect_bundle, param_t.effect_asset = ResPath.GetEffect("UI_yujian_qiliu")
        param_t.is_show = true
        MainuiWGCtrl.Instance:SetAransformationMainUIEffect(param_t)
        -- Scene.Instance:UpdateVolumeMotionBlur(MotionBlurStage.Spurt)
    end
end

-- 轻功 清除拖尾特效
function Role:DestroySwordSprintEff()
    if self:IsMainRole() then
        MainuiWGCtrl.Instance:SetAransformationMainUIEffect()
        -- Scene.Instance:UpdateVolumeMotionBlur(MotionBlurStage.None)
    end
end

--轻功 end]]

-------------------------------------------------渡劫--------------------------------------------------

-- 渡劫 进入状态
function Role:EnterDujie()
    if self:IsDeleted() then
        return
    end

    self:ChangeToCommonState()
    if self:IsMainRole() then
        MountWGCtrl.Instance:SendMountGoonReq(0)
    end

    self:SendMoveReqDujie(true)

    self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Mount_Idle_Nq02)
    -- self:PlaySitEffect()

    self:ForceChengePartVisibleOnSpecialStatus("dujie", false)
end

-- 渡劫 离开
function Role:LeaveDujie()
    if self:IsDeleted() then
        return
    end

    self:ResetPlayIdle()
    -- self:StopSitEffect()

    self:SendMoveReqDujie()

    if self:IsMainRole() then
        RoleWGCtrl.Instance:RemoveSuitEquipCoinDelay(true)
        if self:GetIsInSit() then
            Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
        end
    end

    self:ForceChengePartVisibleOnSpecialStatus("dujie", true)
end

-- 设置渡劫高度
function Role:SendMoveReqDujie(is_dujie)
	if Scene.Instance:IsChangeSceneIng() then return end
	if self.is_only_client_move then return end
	if self:IsJump() then return end

	local height = 8

	if is_dujie then
        self.dujie_move_tweener = self.draw_obj.root_transform:DOLocalMoveY(self.draw_obj.root_transform.localPosition.y + height, 3)
		self.dujie_height = self.draw_obj.root_transform.localPosition.y 
		self.draw_obj.root_transform.localPosition = Vector3(self.draw_obj.root_transform.localPosition.x, self.draw_obj.root_transform.localPosition.y + height, self.draw_obj.root_transform.localPosition.z)
	else
        if self.dujie_move_tweener then
            self.dujie_move_tweener:Kill()
            self.dujie_move_tweener = nil
        end

		self.draw_obj.root_transform.localPosition = Vector3(self.draw_obj.root_transform.localPosition.x, self.dujie_height, self.draw_obj.root_transform.localPosition.z)
	end
end

-------------------------------------------------打坐--------------------------------------------------
-- 打坐 是否在打坐区域
function Role:IsInSitArea()
    if self:IsMainRole() then
        return self.is_in_sit_area
    else
        if not self:IsDeleted() then
            return self.vo.move_mode ~= nil and self.vo.move_mode == MOVE_MODE.MOVE_MODE_SIT
        else
            return false
        end
    end
end

-- 打坐 是否在打坐
function Role:GetIsInSit()
    local is_sit = false
    if self:IsDeleted() then
        return is_sit
    end

    if not self:IsInSitArea() then
        return is_sit
    end

    is_sit = self.vo.move_mode ~= nil and self.vo.move_mode == MOVE_MODE.MOVE_MODE_SIT
    return is_sit
end

-- 打坐 进入状态
function Role:EnterSit()
    if self:IsDeleted() then
        return
    end

    self:ChangeToCommonState()
    if self:IsMainRole() then
        MountWGCtrl.Instance:SendMountGoonReq(0)
    end

    self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Sit_Idle)
    self:PlaySitEffect()

    self:ForceChengePartVisibleOnSpecialStatus("sit", false)
end

-- 打坐 尝试离开
function Role:TryLeaveSit()
    if self:GetIsInSit() then
        self:LeaveSit(true)
    end
end

-- 打坐 离开
function Role:LeaveSit(is_change)
    if self:IsDeleted() then
        return
    end

    self:ResetPlayIdle()
    self:StopSitEffect()

    if self:IsMainRole() and is_change then
        RoleWGCtrl.Instance:RemoveSuitEquipCoinDelay(true)
        if self:GetIsInSit() then
            Scene.SendMoveMode(MOVE_MODE.MOVE_MODE_NORMAL)
        end
    end

    self:ForceChengePartVisibleOnSpecialStatus("sit", true)
end

-- 打坐 复原idle
function Role:ResetPlayIdle()
    if self:IsDeleted() then
        return
    end

    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    local mantle_part = self.draw_obj:GetPart(SceneObjPart.Mantle)

    if self:IsStand() then
        if self.is_gather_state then
            self:CrossToCaiji()
        else

            if self:IsMarryObj() then
                local main_part_obj = part:GetObj()
                if main_part_obj then
                    local children = main_part_obj.gameObject:GetComponentsInChildren(typeof(UnityEngine.Animator))
                    for i = 0, children.Length - 1 do
                        children[i]:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)
                    end
                end
            elseif self:GetIsInSpring() then
                self:SetHotSpringStand()
            elseif self.vo.hold_beauty_npcid and self.vo.hold_beauty_npcid > 0 then
                part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Hug)
                local holdbeauty_part = self.draw_obj:GetPart(SceneObjPart.HoldBeauty)
                if holdbeauty_part then
                    holdbeauty_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Hug)
                end

            elseif self:IsRiding() then
                self:CrossAction(SceneObjPart.Main, self:SetRidingActionIdelParam())

            else
                self:CrossAction(SceneObjPart.Main, self:CurIdleAni(true))
                if self:IsRole() and mantle_part then
                    mantle_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)
                end
            end
        end

        if self:IsRole() then
            self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle, false)
        end

        self:CrossAction(SceneObjPart.Mount, SceneObjAnimator.Idle)
        -- self:CrossAction(SceneObjPart.FightMount, SceneObjAnimator.Idle)
    end
end
--打坐 end]]

-------------------------------------------------传功--------------------------------------------------
-- 传功 特效
function Role:ReleaseXianMengChuanGongEffect()
    if self.xianmeng_chuan_gong_loader then
        self.xianmeng_chuan_gong_loader:DeleteMe()
        self.xianmeng_chuan_gong_loader = nil
    end
end

-- 传功 动画
function Role:PlayChuanGongAnim(anim_name)
    if self.play_chuangong_anim then
        return
    end

    local time = GuildAnswerWGData.Instance:GetPassRemainTime()
    self:CrossAction(SceneObjPart.Main, anim_name)

    local part1 = self:GetDrawObj():GetPart(SceneObjPart.Weapon)
    part1:SetVisible(false)

    local bundle, asset = "effects2/prefab/gz_chuangjing/tongyong_prefab", "effect_chuangong_you"
    self:ReleaseXianMengChuanGongEffect()
    self.xianmeng_chuan_gong_loader = AllocAsyncLoader(self, "chuan_gong_effect")
    self.xianmeng_chuan_gong_loader:SetParent(self:GetRoot().transform)
    self.xianmeng_chuan_gong_loader:SetIsUseObjPool(true)
    self.xianmeng_chuan_gong_loader:SetObjAliveTime(time)
    self.xianmeng_chuan_gong_loader:Load("effects2/prefab/gz_chuangjing/tongyong_prefab", "effect_chuangong_you", nil, function(new_obj)
        new_obj = U3DObject(new_obj)
        
        if new_obj then
            new_obj.transform.localPosition = Vector3(0, 0, 0)
            new_obj.transform.localRotation = Quaternion.Euler(0, 0, 0)
            new_obj.transform.localScale = Vector3(0.7, 0.7, 0.7)
        end
    end)

    self:SetChuangGongState(true)
    self.play_chuangong_anim = GlobalTimerQuest:AddDelayTimer(function()
        self:SetChuangGongState(false)
        if part1 then
            part1:SetVisible(true)
        end
        -- if part2 then
        --     part2:SetVisible(true)
        -- end

        self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle)
        self.play_chuangong_anim = nil
    end, time)
end

-- 传功 是否传功
function Role:IsChuanGong()
    return self.is_chuan_gong
end

-- 传功 状态
function Role:SetChuangGongState(state)
    self.is_chuan_gong = state
end
--传功 end]]
--人物状态 end]]





-- 观测点的pos
function Role:GetLookAtPointPos()
    local x, y, z = 0, 2.4, 0
    local vo = self:GetVo()
    if vo and vo.sex == GameEnum.MALE then
        y = 2.4
    else
        y = 2.2
    end

    -- self:IsTianShenAppearance() or self:IsGundam()
    local point = self.draw_obj:GetAttachPoint(AttachPoint.Head)
    local draw_obj_trans = self.draw_obj:GetTransfrom()
    local is_fly = TaskWGCtrl.Instance and TaskWGCtrl.Instance:IsFly() -- 在天上
    local is_mitsurugi = self:IsMitsurugi()
    local is_jump = self:IsJump()
    if not self:IsAtkPlaying()
        and not (CgManager.Instance and CgManager.Instance:IsCgIng())
        and not is_fly
        and not is_mitsurugi and not is_jump
        and point and not IsNil(point.gameObject) then

        if not IsNil(draw_obj_trans) then
            local local_pos = draw_obj_trans:InverseTransformPoint(point.transform.position)
            x = local_pos.x
            y = local_pos.y
            z = local_pos.z
        end
    end

    return x, y, z
end

local Vec3Zero = Vector3.zero
-- 旋转到目标
function Role:RotaToTarget(target_position, timer)
    local root_obj = self:GetRoot()
    if nil == root_obj then
        return
    end

    timer = timer or 0.5
    local position = root_obj.transform.position
    local direction = target_position - position
    direction.y = 0
    if direction ~= Vec3Zero then
        local rotation = Quaternion.LookRotation(direction).eulerAngles
        root_obj.transform:DORotate(rotation, timer)
    end
end

--移动忽略不可行走区域判断
function Role:SetIsWalkFree(is_walk_free)
    self.draw_obj:SetIsWalkFree(is_walk_free)
end


















-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    玩家头顶信息     --------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
--[[玩家头顶信息]]
-- 是否强制隐藏
function Role:CheckRoleIsForceHideFollowUi()
    local is_show = true
    if self:IsInXunYou() or self:IsInvisible() then
        is_show = false
    end

    return is_show
end

-- 创建
function Role:CreateFollowUi(...)
    Character.CreateFollowUi(self, ...)
    if nil == self.follow_ui then
        return
    end
    local is_marry = self:IsMarryUser()
    self.follow_ui:SetIsMarryCouple(is_marry)
    -- 默认不显示血条
    self.follow_ui:SetHpVisiable(false)
    if self.draw_obj then
        local point = self.draw_obj:GetAttachPoint(AttachPoint.UI)
        self.follow_ui:SetFollowTarget(point, self.draw_obj:GetName())
    end

    self:SyncShowHp(true)
end

-- 加载
function Role:ReloadUIName()
    if self.follow_ui ~= nil then
        local scene_logic = Scene.Instance:GetSceneLogic()
        if nil == scene_logic then
            return
        end

        local name, color = scene_logic:GetColorName(self)
        if self.vo.is_demons then
            local _, monster_id = FuBenPanelWGData.Instance:GetXinMoMonNum()
            local mon_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_id]
            if mon_cfg then
                name = mon_cfg.name
                color = COLOR3B.RED
            end
        end

        self.follow_ui:SetName(name, self, color)
        -- self:ReloadUIJingJieIcon()
        self:ReloadUITianShen3v3Side()
        self:ReloadFollowXianJieEquipImg()
        --self:ReloadAreaIndexEquipImg()
        self:ReloadDrunkLabel()
        self:ReloadUIGuildName()
        self:ReloadUILoverName()
        self:ReloadSpecialImage()
        self:ReloadUIServerName()
        self:ReloadUILeaderFlag()
        self:ReloadUIVip()
        self:ReloadUIEternalNight()
        self:FlushPetNameByMainRoleChange()--当玩家更名时,刷新宠物名称
        self:ReloadUIXiezhuIng()
        self:ReloadUIXiuWeiIcon()
        self:ReloadUIGodOrDemon()
        self:ReloadUIPWCampIcon()
    end
end

-- 名字
function Role:ChangeFollowUiName(name)
    if name then
        self.vo.name = name
    end
    self:ReloadUIName()
end

-- 名字 颜色
function Role:ReloadUINameColor()
    if self.follow_ui ~= nil then
       local scene_logic = Scene.Instance:GetSceneLogic()
       if nil == scene_logic then
           return
       end

       local name, color = scene_logic:GetColorName(self)
       if self.vo.is_demons then
           local _, monster_id = FuBenPanelWGData.Instance:GetXinMoMonNum()
           local mon_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_id]
           if mon_cfg then
               name = mon_cfg.name
               color = COLOR3B.RED
           end
       end

       self.follow_ui:SetName(name, self, color)
   end
end

-- 名字 服务器名
function Role:ReloadUIServerName()
    if self.follow_ui ~= nil then
        local origin_server_id = self:GetVo().origin_server_id
        local role_id = self:GetVo().role_id
        local main_role = Scene.Instance:GetMainRole()
        if not main_role then return end
        local main_role_vo = main_role.vo
        --role_id <= 0 表明是机器人 无分配服id 显示自己的原服id
        --庆隆要改成判断任意条件为0都是机器人.
        if role_id <= 0 or origin_server_id <= 0 then
            origin_server_id = main_role_vo.origin_server_id
        end

        local server_name = origin_server_id
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type == SceneType.HONG_MENG_SHEN_YU and IS_ON_CROSSSERVER then
            server_name = self:GetHMSYServerName(server_name)
            self.follow_ui:SetRoleServerInfo(server_name)
        else
            -- if IS_ON_CROSSSERVER then   -- 在跨服
            --     self.follow_ui:SetRoleServerInfo(server_name)
            -- else
            --     self.follow_ui:SetRoleServerInfo("")
            -- end

            local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
            if fb_scene_cfg.pb_server_name == 0 then
                self.follow_ui:SetRoleServerInfo(server_name)
            else
                self.follow_ui:SetRoleServerInfo("")
            end
        end
    end
end

-- 名字 仙盟
function Role:ReloadUIGuildName()
    if self.follow_ui ~= nil then
        local guild_id = self:GetVo().guild_id
        -- unity3d项目暂时屏蔽
        -- local touxian_level = self:GetVo().touxian_level
        -- local touxian_name = ""
        -- if touxian_level > 0 then
        -- local level_cfg = TouXianData.Instance:GetConfigByLevel(touxian_level)
        -- if next(level_cfg) then
        -- touxian_name = "[" .. ToColorStr(level_cfg.title_name, level_cfg.scene_color) .. "]"
        -- end
        -- end

        -- unity3d项目暂时屏蔽
        -- if guild_id > 0 then
        -- local guild_name = self:GetVo().guild_name
        -- local guild_post = GameVoManager.Instance:GetMainRoleVo().guild_post
        -- guild_name = guild_name
        -- -- local post = GuildWGData.Instance:GetGuildPostNameByPostId(self:GetVo().guild_post)
        -- -- if post then
        -- -- guild_name = guild_name .. post
        -- -- end
        -- -- guild_name = ToColorStr(guild_name, GUILD_NAME_COLOR[self:GetVo().guild_post] or COLOR.GREEN)
        -- -- self.follow_ui:SetGuildName(touxian_name .. guild_name)
        -- self.follow_ui:SetGuildName("[" .. guild_name .. "] " .. GuildDataConst.SCENE_GUILD_POST_LIST[guild_post])
        -- else
        -- -- self.follow_ui:SetGuildName(touxian_name)
        -- self.follow_ui:SetGuildName("")
        -- end
        local guild_post = self:GetVo().guild_post
        local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()

        if guild_id > 0 and guild_post > 0 and fb_scene_cfg.pb_guild_name == 0 and GuildDataConst.SCENE_GUILD_POST_LIST[guild_post] then
            local guild_name = Language.Guild.GuildRoleNane..self:GetVo().guild_name.."_"..GuildDataConst.SCENE_GUILD_POST_LIST[guild_post]
            self.follow_ui:SetGuildName(guild_name)
        else
            self.follow_ui:SetGuildName()
        end
    end
end

-- 名字 爱人
function Role:ReloadUILoverName()
    if self.follow_ui ~= nil then
        local lover_name = self:GetVo().lover_name
        local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
        if lover_name and lover_name ~= "" and fb_scene_cfg.pb_fere_name == 0 then
            lover_name = lover_name .. (Language.Marry.LoverNameFormat[self:GetVo().sex])
            self.follow_ui:SetLoverName(lover_name, self)
        else
            self.follow_ui:SetLoverName("", self)
        end
    end
end

-- 血量 位置
function Role:UpdateHpPosition()
    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:UpdatePosition()
    end
end

-- 血量 同步变身血量
function Role:SyncShowBianShenHp()
    if self.vo.bianshen_max_hp and 0 ~= self.vo.bianshen_max_hp then
        self:GetFollowUi():SetBianShenHpPercent(self.vo.bianshen_hp / self.vo.bianshen_max_hp,self)
    end
end

-- 血量 可见性
function Role:UpdateHpVisible()
    local follow_ui = self:GetFollowUi()
    if follow_ui and not self:IsRealDead() then
        follow_ui:SetHpVisiable(self.is_fighting == true and self:CanShowHpOnScene())
    end
end

-- 血量 在场景是否可显示
function Role:CanShowHpOnScene()
    local scene_logic = Scene.Instance:GetSceneLogic()
    return scene_logic ~= nil and scene_logic:RoleCanShowHp(self)
end

-- 称号 创建
function Role:CreateTitle()
    self:UpdateTitle()
end

-- 称号 更新
function Role:UpdateTitle()
    local is_shield_title = false
    if not self:IsMainRole() then
        local vo = self:GetVo()
        local server_id = RoleWGData.Instance:GetMergeServerId()
        local plat_type = RoleWGData.Instance:GetPlatType()
        if vo ~= nil then
            if vo.origin_plat_type ~= nil and vo.merge_server_id ~= nil and server_id ~= nil and plat_type ~= nil then
                is_shield_title = vo.origin_plat_type ~= plat_type or server_id ~= vo.merge_server_id
            end
        end
    end

    if is_shield_title then
        local follow_ui = self:GetFollowUi()
        if follow_ui ~= nil then
            follow_ui:ClearTitle()
        end
        return
    end

    self:GetFollowUi():CreateTitleEffect(self.vo)
end

-- 称号 vip特殊显示
function Role:ChangeFreeVipImage()
    if self.follow_ui == nil then
        return
    end

    local is_active, str
    if self:IsMainRole() then
        local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
        if not fb_scene_cfg or not fb_scene_cfg.pb_chenhao or fb_scene_cfg.pb_chenhao ~= 1 then -- 特殊场景屏蔽称号
            local title_used_list = TitleWGData.Instance:GetUsedTitleId()
            local vo_title_used_list = self.vo.used_title_list
            if (not title_used_list or #title_used_list == 0) and (not vo_title_used_list or #vo_title_used_list == 0) then
                local has_free_card, card_seq = VipWGData.Instance:CheckHasFreeCard()
                local vip_level_show = VipWGData.Instance:CheckHasFreeCardByVipLevel(card_seq)
                if has_free_card and vip_level_show then
                    str = VipWGData.Instance:GetVipFreeCardTitleID(card_seq)
                    is_active = true
                end
            end
        end
    end

    if is_active and nil ~= str and "" ~= str and not IS_AUDIT_VERSION then -- 审核服不显示
        self:GetFollowUi():ChangeSpecailTitle(str)
    else
        self:GetFollowUi():ChangeSpecailTitle(nil)
        self:GetFollowUi():ClearTitle(0)
    end
end

-- 组队 队长图标
function Role:ReloadUILeaderFlag()
    if self.follow_ui ~= nil then
        local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
        if scene_cfg.pb_team_leader_name and scene_cfg.pb_team_leader_name == 1 then
            self.follow_ui:SetLeaderFlag(false)
            return
        end
        self.follow_ui:SetLeaderFlag(self.vo.is_team_leader == 1)
    end
end

function Role:ReloadUIYZWCBuff(is_main_role)
    if self.follow_ui and self.vo ~= nil then
        local buff_list = nil
        local list = {}
        if is_main_role then
            buff_list = FightWGData.Instance:GetMainRoleShowEffect()
        else
            buff_list = FightWGData.Instance:GetOtherRoleShowEffect(self.vo.obj_id)
        end

        for k,v in pairs(buff_list) do
            local cfg = FightWGData.Instance:GetBuffDescCfgByInfo(v.product_id, v.buff_type, v.info.client_effect_type)
            if cfg.show_name_buff_icon == 1 then
                table.insert(list, v)
            end 
        end

        self.follow_ui:SetYZWCBuffShow(list)
    end
end

-- VIP
function Role:ReloadUIVip()
    if self.follow_ui ~= nil and self.vo ~= nil then
        local vip_level = self.vo.vip_level or -1
        local is_vip = VipWGData.Instance:IsVip()
        local is_hide_vip = false
        if self:IsMainRole() then
            is_hide_vip = SettingWGData.Instance:IsHideMainRoleVipLv()
        else
            is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.vo.shield_vip_flag)
        end
        self.follow_ui:SetRoleVip(vip_level, is_vip, is_hide_vip)
    end
end

--一念神魔
function Role:ReloadUIGodOrDemon()
    if self.follow_ui ~= nil and self.vo ~= nil then
        local god_or_demon_type = self.vo.god_or_demon_type or 0
        local god_or_demon_level = self.vo.god_or_demon_level or 0

        self.follow_ui:SetRoleGodOrDemon(god_or_demon_type, god_or_demon_level)
    end
end

-- 
function Role:ReloadUIPWCampIcon()
    if self.follow_ui ~= nil then
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type == SceneType.CROSS_LAND_WAR then
            local camp = PositionalWarfareWGData.Instance:GetMyCamp()
            self.follow_ui:SetPWCampIcon(camp)
        end
    end
end

function Role:UpdataAreaIndexImg(area_index)
    if area_index then
        self.vo.area_index = area_index
    end
end


function Role:ReloadAreaIndexEquipImg()
    -- if self.follow_ui and self.vo and self.vo.area_index then
         --策划说屏蔽掉势力显示
        --local is_show = XianJieBossWGData.Instance:GetIsShowAreaIndexImg(self.vo.area_index)
        --local area_index = -1 -- is_show and self.vo.area_index or -1 -- -1表示不显示
        --self.follow_ui:SetAreaIndexImg(area_index)
    -- end
end

function Role:ClearAreaIndexInfo()
    if self.vo then
        self.vo.area_index = -1
    end
end

function Role:ReloadUIXiuWeiIcon()
    if self.follow_ui ~= nil and self.vo ~= nil then
        -- local stage = CultivationWGData.Instance:GetXiuWeiState()
        local stage = self.vo.stage_level

        if stage then
            self.follow_ui:SetXiuWeiIcon(stage)
        else
            self.follow_ui:SetXiuWeiIcon()
        end
    end
end

-- 修改人物头顶的采集中图片
function Role:ChangeFollowUIGather()
    local show_gather_img = (not RoleWGData.Instance:IsSameServer(self:GetVo())) and self:GetIsGatherState()
    if self.follow_ui then
        self.follow_ui:SetGatherImg(show_gather_img)
    end
end

-- [[仙界装备
function Role:UpdataXianJieEquipImg(xianjie_boss_equip)
    if xianjie_boss_equip then
        self.vo.xianji_equip_info = xianjie_boss_equip
    end
    self:ReloadFollowXianJieEquipImg()
end

function Role:ClearXianJieBossEquip()
    if self.vo then
        self.vo.xianji_equip_info = {}
    end
end

function Role:ReloadFollowXianJieEquipImg()
    if self.follow_ui and self.vo and self.vo.xianji_equip_info then
        local is_show, item_id = XianJieBossWGData.Instance:GetIsShowXianJieEquip(self.vo.xianjie_boss_equip, self)
        local info = {}
        info.is_show = is_show
        info.item_id = item_id
        self.follow_ui:SetXianjieEquipImg(info)
    end
end
--仙界装备 end]]

-- 3v3 战队
function Role:UpdateZhanDuiName()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg.pb_zhandui_name == 0 then
        self:GetFollowUi():SetZhanDuiName(self.vo.zhandui3v3_name)
    else
        self:GetFollowUi():SetZhanDuiName(nil)
    end
end

-- 3v3 战令
function Role:UpdateZhanDuiZhanLingId()
    self:GetFollowUi():SetZhanDuiZhanLingId(self.vo.zhandui3v3_lingpai_id)
end

-- 3v3 战令
function Role:UpdateZhanDuiZhanLingText()
    self:GetFollowUi():SetZhanDuiZhanLingText(self.vo.zhandui3v3_lingpai_name)
end

-- 仙盟战
function Role:ChangeGuildBattle()
    local scene_logic = Scene.Instance:GetSceneLogic()
    if scene_logic then
        if scene_logic:GetSceneType() ~= SceneType.LingyuFb then
            return
        end
    end
    if self.vo.special_param ~= 0 then
        local str = "guild_battle_" .. self.vo.special_param
        self:GetFollowUi():ChangeSpecailTitle(str)
    else
        self:GetFollowUi():ChangeSpecailTitle(nil)
    end
end

-- 天神3v3阵营icon
function Role:ReloadUITianShen3v3Side()
    if self.follow_ui ~= nil and self.vo ~= nil then
        local scene_type = Scene.Instance:GetSceneType()
        if scene_type == SceneType.TianShen3v3 then
            local side = self.vo.is_shadow == 1 and self.vo.special_param or self.vo.cross3v3_side
            self.follow_ui:SetTianShen3v3Side(side)
        else
            self.follow_ui:SetTianShen3v3Side(-1)
        end
    end
end

-- 醉酒中标签
function Role:ReloadDrunkLabel()
    if self.follow_ui ~= nil and self.vo ~= nil then
        self.follow_ui:SetIsDrunk(self:IsDrunk() and self:IsMainRole())
    end
end

-- 鸿蒙神域
function Role:GetHMSYServerName(server_name)
    local scene_id = Scene.Instance:GetSceneId()
    local scene_index = BossWGData.Instance:GetCurHMSYSceneIndex()
    local info = BossWGData.Instance:GetHMSYTotalInfoBySceneIdIndex(scene_id, scene_index)
    local origin_server_id = self:GetVo().origin_server_id
    local plat_type = self:GetVo().plat_type
    if origin_server_id ~= info.server_id or plat_type ~= info.plat_type then
        return server_name .. Language.WorldServer.ServerName_4
    end
    return server_name .. Language.WorldServer.ServerName_3
end

-- 特殊图标 深渊副本
function Role:ReloadSpecialImage()
    local scene_logic = Scene.Instance:GetSceneLogic()
    local is_show_special_image, asset, bundle = scene_logic:GetIsShowSpecialImage(self)

    if self.vo.top_dps_flag and self.vo.top_dps_flag > 0 then
        is_show_special_image, asset, bundle = true, ResPath.GetDpsIcon()
    end

    if Scene.Instance:GetSceneType() == SceneType.KFSHENYUN_FB then
        local obj_cfg = BossWGData.Instance:GetTuTengObj(self.vo.obj_id)
        if next(obj_cfg) then
            if obj_cfg.owner_id > 0 then
                is_show_special_image, asset, bundle = true, "uis/uires/res/x1ui/boss_atlas", "go_ahead1" ---临时
            else
                is_show_special_image, asset, bundle = false, nil, nil
            end
        end
    end
    if self.follow_ui then
        self.follow_ui:SetSpecialImage(is_show_special_image, bundle, asset)
    end
end

-- 永夜之巅
function Role:ReloadUIEternalNight()
    if self:IsInEternalNightScene() then
        if self.follow_ui then
            local equip_data = {}
            local kill_num = 0
            local role_uuid = self.vo.uuid
            local uuid_str = role_uuid.temp_low .. role_uuid.temp_high
            local player_info = EternalNightWGData.Instance:GetPlayerInfoByUUId(uuid_str)
            kill_num = player_info and player_info.combo_kill_num or 0
            if self:IsMainRole() then
                equip_data = EternalNightWGData.Instance:GetSelfJiPingEquipByUUId()
            else
                equip_data = EternalNightWGData.Instance:GetPlayerJiPingEquipByUUId(uuid_str)
            end
            self.follow_ui:SetEternalNightEquipList(equip_data)
            self.follow_ui:SetComboKillNum(kill_num)
        end
    end
end

-- 协助
function Role:ReloadUIXiezhuIng()
    if self.follow_ui then
        if self:IsMainRole() then
            local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
            local is_xiezhu_scene = BossXiezhuWGData.Instance:IsInXieZhuScene()
            local flag = state == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER and is_xiezhu_scene
            self.follow_ui:SetXiezhuIngVisible(flag)
        elseif self:IsRole() then
            local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
            local uid = self.vo.uuid.temp_low
            local is_help = BossXiezhuWGData.Instance:GetHasHelpMeUid(uid)
            local flag = state == ASSIST_STATUS.ASSIST_STATUS_INVOKE and is_help
            self.follow_ui:SetXiezhuIngVisible(flag)
        else
            self.follow_ui:SetXiezhuIngVisible(false)
        end
    end
end

-- 气泡 随机文本
function Role:GetRandBubbletext(event_id)
    local bubble_cfg = BootyBayWGData.Instance:GetWaBaoEventById(event_id)

    if bubble_cfg then
        return bubble_cfg.talk
    else
        return ""
    end
end

-- 气泡 更新
function Role:UpdataBubble(event_id)
    if nil ~= self.follow_ui then
        local text = self:GetRandBubbletext(event_id)
        self.follow_ui:ChangeBubble(text)
    end
end

-- 气泡 改变
function Role:ChangeBubble(text, time, callback)
    if self.follow_ui then
        self.follow_ui:ChangeRoleBubble(text, time, callback)
    end
end

-- 气泡 显示
function Role:ShowBubble(event_id)
    if nil ~= self.follow_ui and self:IsMainRole() then
        if self.is_talk == false then
            self.is_talk = true
            self:UpdataBubble(event_id)
            self.follow_ui:ForceSetVisible(true)
            self.follow_ui:ShowBubble()
            self.bobble_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
            	if self:CheckRoleIsForceHideFollowUi() then
            		self.follow_ui:CancelForceSetVisible()
            	end

                self.follow_ui:HideBubble()
                self.is_talk = false
            end,self.exist_time)
        end
    end
end

-- 气泡 隐藏
function Role:HideBubble()
    if self.follow_ui then
        self.follow_ui:HideBubble()
    end
end

-- 气泡 可见性
function Role:IsBubbleVisible()
    if self.follow_ui then
        return self.follow_ui:IsBubbleVisible()
    end
end

-- 五行图标
function Role:SetWuXingIcon()
    local wuxing_type = self:GetWuXingType()
    if self.follow_ui then
        self.follow_ui:SetWuXingType(wuxing_type)
    end
end
--玩家头顶信息 end]]









-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------     模型展示      ----------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
----[[模型展示
-- 品质规则 部位
function Role:InitPartQualityRules()
    local offset = self:GetQualityOffsetLevel()

    for scene_obj_part, shield_obj_type in pairs(RolePartQualityRules) do
        local handle = ReuseableHandleManager.Instance:GetShieldHandle(RolePartShieldHandle, self, shield_obj_type, scene_obj_part)
        handle:SetQualityLevelOffset(offset)
        self.part_quality_handles[scene_obj_part] = handle
    end

    for scene_obj_part, shield_obj_type in pairs(RolePartEffectQualityRules) do
        local handle = ReuseableHandleManager.Instance:GetShieldHandle(RolePartEffectShieldHandle, self, shield_obj_type, scene_obj_part)
        handle:SetQualityLevelOffset(offset)
        self.part_effect_quality_handles[scene_obj_part] = handle
    end
end

-- 材质 品质
function Role:UpdateMaterialQuality()
    Character.UpdateMaterialQuality(self)
    if not self:IsMainRole() then
        -- 其他玩家特效最多使用中品质
        if QualityConfig.QualityLevel < 2 then
            self.draw_obj:SetQualityControlOverrideLevel(2)
        else
            self.draw_obj:SetQualityControlOverrideLevel(-1)
        end
    end
end

-- 材质 计算
function Role:CalculatePriortiy()
    local scene_logic = Scene.Instance:GetSceneLogic()
    return scene_logic:GetRoleVisiblePriortiy(self)
end

function Role:FlushPartColor(skin_type)
    local appe_data = self.vo.appearance
    if not appe_data then
        return
    end

    local part_color = appe_data.part_color
    if part_color == nil or IsEmptyTable(part_color) then
        return
    end

    -- 获取染色配置
    local image_cfg = NewAppearanceWGData.Instance:GetFashionCfgByProtocolIndex(SHIZHUANG_TYPE.BODY, appe_data.shizhuang_body)
    if not image_cfg then
        return
    end

    local fashion_dye_cfg = NewAppearanceDyeWGData.Instance:GetConsumeCfgByIndex(image_cfg.index, image_cfg.part_type)
    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if (not fashion_dye_cfg) or (main_part == nil) then
        return
    end

    local skin = main_part:GetChangeSkinComponent()
    if skin == nil then
        return
    end

    local server_project_index = appe_data.shizhuang_project_id
    local use_project_index = server_project_index + 1
    local body_res, face_res, hair_res = self:GetModelPartRes()

    if not self.project_cache_data then
		self.project_cache_data = {}
	end

    local change_body_dye_color_fun = function()
        local dye_color_table = {}
        for show_part, color_data in ipairs(part_color) do
            local dye_index_list_data = NewAppearanceDyeWGData.Instance:GetDyeIndexListBySeqPart(fashion_dye_cfg.seq, show_part)
            local index_list = dye_index_list_data and dye_index_list_data.dye_index_list
    
            local color = nil
            if color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0 then
                color = Color.New(color_data.r / 255, color_data.g / 255, color_data.b / 255, color_data.a / 255)
            end
    
            if show_part ~= HAIR_PART then
                if index_list and color then
                    for _, dye_index in ipairs(index_list) do
                        local dye_color_data = {}
                        dye_color_data.dye_index = dye_index
                        dye_color_data.r = color.r
                        dye_color_data.g = color.g
                        dye_color_data.b = color.b
                        dye_color_data.a = color.a
                        table.insert(dye_color_table, dye_color_data)
                    end
                end
            end
        end
    
        if not IsEmptyTable(dye_color_table) then
  
            skin:BatchSetPartDyeColor(cjson.encode({dye_list = dye_color_table}))
        end
    end

    local change_hair_dye_color_fun = function()
        local color_data = part_color[HAIR_PART]
        if color_data and (color_data.r ~= 0 or color_data.g ~= 0 or color_data.b ~= 0 or color_data.a ~= 0) then
            main_part:UpdateChangeExtraModelData(HeadCustomizationType.HairColor, color_data)
            main_part:ChangeSkinHeadMatColorByRGBATable(HeadCustomizationType.HairColor, color_data)
        end
    end

    if skin_type == ROLE_SKIN_TYPE.BODY then
        local body_material_id = NewAppearanceDyeWGData.Instance:GetProjectBodyIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if body_material_id ~= 0 and body_res ~= appe_data.default_body_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.BODY, body_res, body_material_id, change_body_dye_color_fun)
            self.project_cache_data.body_id = body_material_id
        else
            if self.project_cache_data.body_id and self.project_cache_data.body_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.body_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.BODY)
            end
            change_body_dye_color_fun()
        end
    elseif skin_type == ROLE_SKIN_TYPE.HAIR then
        local hair_material_id = NewAppearanceDyeWGData.Instance:GetProjectHairIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if hair_material_id ~= 0 and hair_res ~= appe_data.default_hair_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.HAIR, hair_res, hair_material_id)
            self.project_cache_data.hair_id = hair_material_id
        else
            if self.project_cache_data.hair_id and self.project_cache_data.hair_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.hair_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.HAIR)
            end
        end
    elseif skin_type == ROLE_SKIN_TYPE.FACE then
        local face_material_id = NewAppearanceDyeWGData.Instance:GetProjectFaceIdCfgBySeq(fashion_dye_cfg.seq, use_project_index)
        if face_material_id ~= 0 and face_res ~= appe_data.default_face_res_id then
            main_part:ChangeRoleProjectMaterials(ROLE_SKIN_TYPE.FACE, face_res, face_material_id, change_hair_dye_color_fun)
            self.project_cache_data.face_id = face_material_id
        else
            if self.project_cache_data.face_id and self.project_cache_data.face_id ~= 0 then		-- 有发生改变才会重置
                self.project_cache_data.face_id = 0
                main_part:ResetRoleProjectMaterials(ROLE_SKIN_TYPE.FACE)
            end

            change_hair_dye_color_fun()
        end
    end
end

-- 其他玩家品质等级-1，主角+1
function Role:GetQualityOffsetLevel()
    return self:IsMainRole() and 1 or -1
end

-- 跟随对象 是否屏蔽跟随对象
function Role:IsShieldFollowObj()
    local shield = true
    if self:GetVisiable() then
        shield = false
    end
    -- 幽灵状态隐藏
    if self:IsGhost() then
        shield = true
    end

    if self:IsInvisible() then
        shield = true
    end

    return shield
end

local follow_obj_func_list =
{
    Role.UpdateGuardVisible,
    Role.UpdatePetVisible,
    Role.UpdateBeastVisible,
    Role.UpdateTaskCallInfoVisible,
    Role.UpdateFollowMingJiangVisible,
}
-- 跟随对象 可见性更新
function Role:UpdateFollowObjVisible()
    if self.flush_follow_obj_index > #follow_obj_func_list then
        return
    end

    local func = follow_obj_func_list[self.flush_follow_obj_index]
    if func then
        func(self)
    end
    self.flush_follow_obj_index = self.flush_follow_obj_index + 1
end

-- 可见性改变
function Role:VisibleChanged(visible)
    Character.VisibleChanged(self, visible)

    if self:IsMainRole() then
        for k,v in pairs(follow_obj_func_list) do
            v(self)
        end
    else
        self.flush_follow_obj_index = 1
    end

    --重新显示模型的时候是默认idle状态，如果此时在双修中，那么模型显示会有问题
    self:ReflushAnimation(true)

    if visible then
        local buff_list = nil
        if self:IsMainRole() then
            buff_list = FightWGData.Instance:GetMainRoleEffectList()
        else
            buff_list = FightWGData.Instance:GetOtherRoleEffectList(self:GetObjId())
        end

        if buff_list ~= nil then
            for k,v in pairs(buff_list) do
                self:AddBuff(v.buff_type, v.product_id, nil, v.client_effect_type)
            end
        end
    else
        self:RemoveAllBuff(true)
        self:ResetSpecialStateInfo()
    end

    self:SetWuXingIcon()
end

-- 部位 可见性
function Role:PartVisibleChanged(part, visible)
    self.draw_obj:ShieldPart(part, not visible)
    if part == SceneObjPart.Foot then
        self.hide_foot = not visible
    end
end

-- 特效 部位可见性
function Role:PartEffectVisibleChanged(part, visible)
    self.draw_obj:SetPartIsDisableAttachEffect(part, not visible)
end

-- 特效 可见性
function Role:RoleEffectVisibleChanged(visible)
    self.role_effect_visible = visible
    for k,v in pairs(self.part_effect_quality_handles) do
        if visible then
            v:CancelForceSetVisible()
        else
            v:ForceSetVisible(false)
        end
    end

    -- if visible then
    --     self.part_quality_handles[SceneObjPart.Halo]:CancelForceSetVisible()
    --     self.part_quality_handles[SceneObjPart.ShouHuan]:CancelForceSetVisible()
    --     self.part_quality_handles[SceneObjPart.FaZhen]:CancelForceSetVisible()
    --     self.part_quality_handles[SceneObjPart.SkillHalo]:CancelForceSetVisible()
    -- else
    --     self.part_quality_handles[SceneObjPart.Halo]:ForceSetVisible(false)
    --     self.part_quality_handles[SceneObjPart.ShouHuan]:ForceSetVisible(false)
    --     self.part_quality_handles[SceneObjPart.FaZhen]:ForceSetVisible(false)
    --     self.part_quality_handles[SceneObjPart.SkillHalo]:ForceSetVisible(false)
    -- end

    if self.guard_obj then
        self.guard_obj:UpdateEffectVisible()
    end

    for k,v in pairs(self.pet_obj_list) do
        v:UpdateEffectVisible()
    end

    if self.beast_obj then
        self.beast_obj:UpdateEffectVisible()
    end

    if self.task_callinfo_obj then
        self.task_callinfo_obj:UpdateEffectVisible()
    end

    self:UpdateSitEffect()
end

-- 部位 可见性  当玩家处理特殊状态时 对部件进行强制隐藏
function Role:ForceChengePartVisibleOnSpecialStatus(key, visible)
    if not visible then
        if not self.part_visible_special_status_list then
            self.part_visible_special_status_list = {}
        end

        local part_list = {}
        if key == "sit" then
            part_list = {
                SceneObjPart.Wing, SceneObjPart.Halo, SceneObjPart.BaoJu, SceneObjPart.Mantle,
                SceneObjPart.FaZhen, SceneObjPart.QiLinBi, SceneObjPart.Waist, SceneObjPart.Tail,
                SceneObjPart.Jianling, SceneObjPart.GodOrDemonHalo, SceneObjPart.ShouHuan, SceneObjPart.SkillHalo,
            }
        elseif key == "dujie" then
            part_list = {
                SceneObjPart.Wing, SceneObjPart.Halo, SceneObjPart.BaoJu, SceneObjPart.Mantle,
                SceneObjPart.FaZhen, SceneObjPart.QiLinBi, SceneObjPart.Waist, SceneObjPart.Tail,
                SceneObjPart.Jianling, SceneObjPart.GodOrDemonHalo, SceneObjPart.ShouHuan, SceneObjPart.SkillHalo,
                SceneObjPart.Weapon,
            }
        elseif key == "multi_mount" then
            part_list = {
                SceneObjPart.Halo, SceneObjPart.BaoJu, SceneObjPart.Mantle,
                SceneObjPart.FaZhen, SceneObjPart.QiLinBi, SceneObjPart.Waist, SceneObjPart.Tail,
                SceneObjPart.Jianling, SceneObjPart.GodOrDemonHalo, SceneObjPart.ShouHuan, SceneObjPart.SkillHalo,
                SceneObjPart.Weapon,
            }
        end

        for k,v in pairs(part_list) do
            self.part_visible_special_status_list[v] = true
        end

        for k,v in pairs(self.part_visible_special_status_list) do
            if self.part_quality_handles[k] then
                self.part_quality_handles[k]:ForceSetVisible(false)
            end
        end
    else
        if not self.part_visible_special_status_list then
            return
        end

        for k,v in pairs(self.part_visible_special_status_list) do
            if self.part_quality_handles[k] then
                self.part_quality_handles[k]:CancelForceSetVisible()
            end
        end

        self.part_visible_special_status_list = {}
    end
end

-- 特效 可见性get
function Role:GetRoleEffectVisible()
    return self.role_effect_visible
end

-- 隐藏或显示角色
function Role:HideOrShowRole(state)
    if self:IsMainRole() then
        GlobalEventSystem:Fire(FlyShoesState.IS_FLY, state)
    else
        self:ForceSetVisible(state)
    end
end

-- 初始化形象
function Role:InitAppearance()
    self:ClearTianShenSpecialEff()
    self:RemoveAransformationEffect()
    if self:IsMainRole() then
        self.load_priority = 5
    end

    self.is_need_force_change_wait_sync_anim_type = true
    self.is_from_init_appearance = true
    self.init_model = true
    if self:ChangeGhostModel() then
        return
    end

    if self:ChangeDemonsModel() then
        self:EquipDataChangeListen()
        return
    end

    if self.vo.npc_res and self.vo.npc_res ~= "" then
        local bundle, asset = ResPath.GetNpcModel(self.vo.npc_res)
        self:ChangeMainPartModel(bundle, asset)
        return
    end

    local bundle, name = nil, nil
    if self.special_res_id ~= 0 and self:IsTianShenAppearance() then --变身
        bundle, name = self:GetSceneBianShenRes()
        self:ChangeMainPartModel(bundle, name, function ()
            self:UpdateHpPosition()
            -- self:CheckTianShenSpecialEff()
        end, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN)
        self:UpdateRoleActorConfigPrefabData()
        self:EquipDataChangeListen()
        self:ChangeShuangShengTianShen()
        return
    end

    if self.special_res_id ~= 0 and self:IsGundam() then
        self:UpdateGundamModel()
        return
    end

    if self.special_res_id ~= 0 and  SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON == self.vo.special_appearance then --温泉
        -- 只有身体资源需要改变，其余部位用玩家diy默认值
        local role_bundle, role_name = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.vo.sex, self:GetProf()))
        local appe_data = self.vo.appearance
        local def_hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, appe_data.default_hair_res_id, self.vo.sex, self:GetProf())
        local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(self.vo.sex, self:GetProf(), 0, 0, appe_data.default_face_res_id, appe_data.default_hair_res_id)
        local role_diy_appearance_data = self.vo.role_diy_appearance
        local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.vo.sex, self:GetProf(), role_diy_appearance_data.preset_seq or 1)
        local extra_model_data = {
            sex = self.vo.sex,
            prof = self:GetProf(),

            role_body_res = self.special_res_id,
            role_face_res = face_res,
            role_hair_res = hair_res,

            eye_size = role_diy_appearance_data.eye_size,
            eye_position = role_diy_appearance_data.eye_position,
            eye_shadow_color = role_diy_appearance_data.eye_shadow_color,

            left_pupil_type = role_diy_appearance_data.left_pupil_type,
            left_pupil_size = role_diy_appearance_data.left_pupil_size,
            left_pupil_color = role_diy_appearance_data.left_pupil_color,

            right_pupil_type = role_diy_appearance_data.right_pupil_type,
            right_pupil_size = role_diy_appearance_data.right_pupil_size,
            right_pupil_color = role_diy_appearance_data.right_pupil_color,

            mouth_size = role_diy_appearance_data.mouth_size,
            mouth_position = role_diy_appearance_data.mouth_position,
            mouth_color = role_diy_appearance_data.mouth_color,

            face_decal_id = role_diy_appearance_data.face_decal_id,
            hair_color = role_diy_appearance_data.hair_color,

            eye_angle = preset_diy_cfg and preset_diy_cfg.eye_angle,
            eye_close = preset_diy_cfg and preset_diy_cfg.eye_close,
            eyebrow_angle = preset_diy_cfg and preset_diy_cfg.eyebrow_angle,
            nose_size = preset_diy_cfg and preset_diy_cfg.nose_size,
            nose_angle = preset_diy_cfg and preset_diy_cfg.nose_angle,
            mouth_angle = preset_diy_cfg and preset_diy_cfg.mouth_angle,
            cheek_size = preset_diy_cfg and preset_diy_cfg.cheek_size,
            chin_length = preset_diy_cfg and preset_diy_cfg.chin_length,
        }
        self:ChangeMainPartModel(role_bundle, role_name, nil, nil, DRAW_MODEL_TYPE.ROLE, extra_model_data)
        return
    end

    if self.special_res_id ~= 0 and self:IsXiuWeiBianShen() then --怒气形象
        local role_bundle, role_name = ResPath.GetRoleModel(self.special_res_id)
        local power_type = math.floor(self.vo.appearance_param / 1000)
        local image_lv = self.vo.appearance_param % 1000
        local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(power_type, image_lv)

        if cfg then
            local body_res, face_res, hair_res = RoleWGData.GetShowRoleRealmSkinPartRes(self.vo.sex, self:GetProf(), cfg.default_body, cfg.default_face, cfg.default_hair)
            local role_diy_appearance_data = self.vo.role_diy_appearance
            local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.vo.sex, self:GetProf(), role_diy_appearance_data.preset_seq or 1)
            local extra_model_data = {
                role_body_res = body_res,
                role_face_res = face_res,
                role_hair_res = hair_res,
                is_realm = true,
            }
            self:ChangeMainPartModel(role_bundle, role_name, nil, nil, DRAW_MODEL_TYPE.ROLE, extra_model_data)
            self:ExecuteAransformationAction(nil, true)
        end
        return
    end

    if self.special_res_id ~= 0 and self:IsXMZCar() then --变身
        local hs_xs_id = self.vo.appearance_param
        local cfg = GuildBattleRankedWGData.Instance:GetHSXSIdByCfg(hs_xs_id)
        local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
        local blue_resid = cfg and cfg.blue_hs_xs_resid or other_cfg.blue_hs_kong_resid
        local red_resid = cfg and cfg.red_hs_xs_resid or other_cfg.red_hs_kong_resid
        local user_info = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
        if user_info and self:IsMainRole() then
            if user_info.side == 0 then
                bundle, name = ResPath.GetMonsterModel(blue_resid)
                self:ChangeMainPartModel(bundle, name)
            else
                bundle, name = ResPath.GetMonsterModel(red_resid)
                self:ChangeMainPartModel(bundle, name)
            end
        else
            if self.vo.special_param == 0 then
                bundle, name = ResPath.GetMonsterModel(blue_resid)
                self:ChangeMainPartModel(bundle, name)
            else
                bundle, name = ResPath.GetMonsterModel(red_resid)
                self:ChangeMainPartModel(bundle, name)
            end
        end

        return
    end

    if SPECIAL_APPEARANCE_TYPE.STORY_ROBERT == self.vo.special_appearance then
        local res_t = Split(self.vo.special_param, "##")
        bundle, name = res_t[1], res_t[2]
        if bundle and name then
            self:ChangeMainPartModel(bundle, name)
        end
        return
    end

    if self.vo.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE and self.special_res_id ~= 0 then
        bundle, name = TaskWGData.Instance:ChangeResInfo(self.special_res_id)
        self:ChangeMainPartModel(bundle, name, function ()
            self:UpdateHpPosition()
        end)
        return
    end

    if self.special_res_id ~= 0 then
        bundle, name = ResPath.GetMonsterModel(self.special_res_id)
        self:ChangeMainPartModel(bundle, name, function ()
            self:UpdateHpPosition()
        end)
        return
    end

    self.draw_obj:GetPart(SceneObjPart.Main):EnableMountUpTrigger(false)

    if self.role_res_id ~= nil and self.role_res_id ~= 0 then
        local load_skin_callback = function(skin_type)
            self:FlushPartColor(skin_type)
        end

        local role_bundle, role_name = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.vo.sex, self:GetProf()))
        local appe_data = self.vo.appearance
        local def_hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, appe_data.default_hair_res_id, self.vo.sex, self:GetProf())
        local body_res, face_res, hair_res = self:GetModelPartRes()
        local role_diy_appearance_data = self.vo.role_diy_appearance
        local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.vo.sex, self:GetProf(), role_diy_appearance_data.preset_seq or 1)
        local extra_model_data = {
            sex = self.vo.sex,
            prof = self:GetProf(),
            
            role_body_res = body_res,
            role_face_res = face_res,
            role_hair_res = hair_res,

            eye_size = role_diy_appearance_data.eye_size,
            eye_position = role_diy_appearance_data.eye_position,
            eye_shadow_color = role_diy_appearance_data.eye_shadow_color,

            left_pupil_type = role_diy_appearance_data.left_pupil_type,
            left_pupil_size = role_diy_appearance_data.left_pupil_size,
            left_pupil_color = role_diy_appearance_data.left_pupil_color,

            right_pupil_type = role_diy_appearance_data.right_pupil_type,
            right_pupil_size = role_diy_appearance_data.right_pupil_size,
            right_pupil_color = role_diy_appearance_data.right_pupil_color,

            mouth_size = role_diy_appearance_data.mouth_size,
            mouth_position = role_diy_appearance_data.mouth_position,
            mouth_color = role_diy_appearance_data.mouth_color,

            face_decal_id = role_diy_appearance_data.face_decal_id,
            hair_color = role_diy_appearance_data.hair_color or nil,

            eye_angle = preset_diy_cfg and preset_diy_cfg.eye_angle,
			eye_close = preset_diy_cfg and preset_diy_cfg.eye_close,
			eyebrow_angle = preset_diy_cfg and preset_diy_cfg.eyebrow_angle,
			nose_size = preset_diy_cfg and preset_diy_cfg.nose_size,
			nose_angle = preset_diy_cfg and preset_diy_cfg.nose_angle,
			mouth_angle = preset_diy_cfg and preset_diy_cfg.mouth_angle,
			cheek_size = preset_diy_cfg and preset_diy_cfg.cheek_size,
			chin_length = preset_diy_cfg and preset_diy_cfg.chin_length,
        }
        
        if ResMgr:IsBundleMode() and not ResMgr:IsVersionCached(role_bundle) then
            self:ChangeMainPartModel(role_bundle, role_name, nil, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE, DRAW_MODEL_TYPE.ROLE, extra_model_data, load_skin_callback)

            DownloadHelper.DownloadBundle(role_bundle, 3, function(ret)
                if ret then
                    self:ChangeMainPartModel(role_bundle, role_name, nil, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE, DRAW_MODEL_TYPE.ROLE, extra_model_data, load_skin_callback)
                end
            end)
        else
            self:ChangeMainPartModel(role_bundle, role_name, nil, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE, DRAW_MODEL_TYPE.ROLE, extra_model_data, load_skin_callback)
        end

        self.init_model = true
        local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
        self.can_bubble = fb_scene_cfg.is_bubble and fb_scene_cfg.is_bubble == 1 or false
    end

    self:EquipDataChangeListen()    --更新武器

    -- 抱美人
    if self.hold_beauty_res_id > 0 then
        self:ChangeModel(SceneObjPart.HoldBeauty, ResPath.GetGoddessModel(self.hold_beauty_res_id))
    end

    -- 和SetAttr 一样
    self:UpdateWing()
    self:UpdateJianZhen()
    self:UpdateMount()
    self:ChangeBaoJuModel()
    self:ChangeMantle()
    self:ChangeHalo()
    self:ChangeWaist()
    self:ChangeMask()
    self:ChangeShouHuan()
    self:ChangeTail()
    self:ChangeFaZhen()
    self:ChangeSkillHalo()
    self:ChangeWuHun()
    self:ChangeGodOrDemonHalo()
    
    self.exist_time = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].exist_time
    self.interval = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].interval
    self.record_time = self.record_time + self.interval
end

-- 更新形象res_id
function Role:UpdateAppearance()
    local vo = self.vo
    local prof = vo.prof
    local sex = vo.sex
    --清空缓存
    self.role_res_id = 0
    local old_special_res_id = self.special_res_id
    self.special_res_id = 0 -- 这里清0迟早是个坑
    self.body_color = 0
    self.gundam_weapon_id = 0

    if self:IsGhost() then
        return
    end

    -- 最后查找职业表
    if self.role_res_id == 0 then
        self.role_res_id = RoleWGData.GetJobModelId(sex, prof)
    end

    -- 变身
    if self.vo.bianshen_param and self.vo.bianshen_param ~= "" and self.vo.bianshen_param ~= 0 then
        if self.vo.bianshen_param == BIANSHEN_EFEECT_APPEARANCE.APPEARANCE_MOJIE_GUAIWU then
            self.special_res_id = 2127001
        elseif self.vo.bianshen_param == BIANSHEN_EFEECT_APPEARANCE.APPEARANCE_DATI_XIAOTU then
            self.special_res_id = 7212001
        elseif self.vo.bianshen_param == BIANSHEN_EFEECT_APPEARANCE.APPEARANCE_DATI_XIAOZHU then
            self.special_res_id = 7212001
        end

    elseif self:IsXMZCar() and GuildBattleRankedWGData.Instance ~= nil then
        local hs_xs_id = self.vo.appearance_param
        local cfg = GuildBattleRankedWGData.Instance:GetHSXSIdByCfg(hs_xs_id)
        local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
        local blue_resid = cfg and cfg.blue_hs_xs_resid or other_cfg.blue_hs_kong_resid
        local red_resid = cfg and cfg.red_hs_xs_resid or other_cfg.red_hs_kong_resid
        local user_info = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
        if user_info and self:IsMainRole() then
            if user_info.side == 0 then
                 self.special_res_id = blue_resid
            else
                 self.special_res_id = red_resid
            end
        else
            if self.vo.special_param == 0 then
                self.special_res_id = blue_resid
            else
                self.special_res_id = red_resid
            end
        end

    elseif self:IsGundam() and nil ~= MechaWGData.Instance then
        -- special_res_id 索引从0开始，兼容旧逻辑
        local cfg = MechaWGData.Instance:GetPartCfgBySeq(self.vo.appearance_param_extend[MECHA_PART_TYPE.BODY])
        if cfg then
            local gundam_seq = cfg.mechan_seq + 1
            self.special_res_id = gundam_seq * 100000 + cfg.res_id
        end

        cfg = MechaWGData.Instance:GetPartCfgBySeq(self.vo.appearance_param_extend[MECHA_PART_TYPE.WEAPON])
        if cfg then
            local gundam_seq = cfg.mechan_seq + 1
            self.gundam_weapon_id = gundam_seq * 100000 + cfg.res_id + MECHA_PART_TYPE.WEAPON * 1000
        end

    elseif SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING == self.vo.special_appearance then
        self.special_res_id = 3011001

    elseif SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON == self.vo.special_appearance then
        if sex == 1 then
            self.special_res_id = 1201091
        else
            self.special_res_id = 3201091
        end

    elseif self:IsTianShenAppearance() then --变身
        self.special_res_id = self.vo.appearance_param

    elseif self.vo.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE then
        self.special_res_id = self.vo.task_appearn_param_1
        -- self.role_res_id = 0
    elseif self:IsXiuWeiBianShen() then     --修为变身
        local power_type = math.floor(self.vo.appearance_param / 1000) + 1     --(0 人, 1 仙, 2 魔)（不区分职业）
        self.special_res_id = string.format("%s0%s", RoleWGData.GetJobModelId(sex, 1), power_type)
    end

    if self:IsBianxingFool() then
        self.special_res_id = 3014001 --跨服答题变身
    end

    if self.special_res_id == 0 and old_special_res_id ~= self.special_res_id then
        self:UpdateTitle()
        self.is_bind_fight_mount_action_effect = nil
        self:UpdateRoleActorConfigPrefabData()
    end

        -- 先查找时装的武器和衣服
    if vo.appearance ~= nil and self.special_res_id == 0 then
        if vo.appearance.fashion_body ~= 0 then
            self.role_res_id = ResPath.GetFashionModelId(prof, vo.appearance.fashion_body)
        end
        self.body_color = vo.appearance.body_color

        if self:IsTianShenAppearance() and self:IsWingFly() and self:IsStand() then
            --飞行羽翼状态下变身，需要手动切换一下idle动画
            self:CrossAction(SceneObjPart.Main, self:CurIdleAni())
        end

        self:UpdateMantleResId()
        self:UpdateHaloResId()
        self:UpdateBaoJu()
        self:UpdateQiLinBi()
        self:UpdateWaist()
        self:UpdateMask()
        self:UpdateShouHuan()
        self:UpdateTail()
        self:UpdateSkillHaloResId()
        self:RemoveShuangShengTianShen()
    end

    self:UpdateHotSpringSpecialImage()
    self:UpdatePetVisible()
    self:UpdateBeastVisible()
    self:UpdateTaskCallInfoVisible()
end

-- 模型 改变
function Role:ChangeModel(part, bundle_name, asset_name, callback, draw_model_type, extra_model_data, load_skin_callback)
    Character.ChangeModel(self, part, bundle_name, asset_name, callback, draw_model_type, extra_model_data, load_skin_callback)

    -- 重新进入状态，修复初始化后的动作问题
    if self.is_from_init_appearance and part == SceneObjPart.Main then
        self.is_from_init_appearance = nil
        if not self:IsAtk() then
            self:ReEnterState()
        end
    end

    -- 模型品质规则，修改偏移值。偏移值越大越不容易被屏蔽
    local base_offset = self:GetQualityOffsetLevel()
    local model_offset = base_offset
    -- if bundle_name and "" ~= bundle_name and asset_name and "" ~= asset_name then
    --     model_offset = model_offset + QualityWGData.Instance:GetModelQualityOffsetConfig(bundle_name, asset_name)
    -- end

    local handle = self.part_quality_handles[part]
    if handle then
        handle:SetQualityLevelOffset(model_offset)
    end

    -- 模型特效品质规则，修改偏移值。偏移值越大越不容易被屏蔽
    local effect_offset = base_offset
    -- if bundle_name and "" ~= bundle_name and asset_name and "" ~= asset_name then
    --     effect_offset = effect_offset + QualityWGData.Instance:GetModelEffectQualityOffsetConfig(bundle_name, asset_name)
    -- end

    local effect_handle = self.part_effect_quality_handles[part]
    if effect_handle then
        effect_handle:SetQualityLevelOffset(effect_offset)
    end
    self:SetWuXingIcon()
end

-- 模型 当加载完
function Role:OnModelLoaded(part, obj, obj_class)
    Character.OnModelLoaded(self, part, obj, obj_class)
    if self:IsMainRole() then
        if part == SceneObjPart.Mount or part == SceneObjPart.FightMount then
            if not self:IsRiding() then
                self:RemoveModel(part)
            --[[
            elseif self.show_fade_in then
                self.show_fade_in = false
                local mount_part = self.draw_obj:GetPart(part)
                if mount_part then
                    mount_part:RemoveOcclusion()
                end

                local call_back = function()
                    if mount_part then
                        ReDelayCall(self, function()
                            mount_part:AddOcclusion()
                        end, 0, "mount_part_occlusion")
                    end
                end
                self:PlayMountFade(1, 1, call_back)
            ]]
            end
        end

        if part == SceneObjPart.Main then
            local logic = Scene.Instance:GetSceneLogic()
            if self:IsAtk() or (logic and not logic:CanCancleAutoGuaji()) then
                self:OnAnimatorEnd()
            end
            for _, v in pairs(self.animator_handle_t) do
                v:Dispose()
            end
            self.animator_handle_t = {}
            self:SetFightMovePos(0, 0) --修复变身时有概论发生角色无法移动
            self:InitWaterState()

            if self.embrace_data ~= nil then
                self:EmbraceItem(self.embrace_data.bundle_name, self.embrace_data.asset_name)
            end
        end

        self.draw_obj:GetPart(part):SetIsAlwaysAnimate(true)
    end

    if part == SceneObjPart.Main then
        -- if self:IsTianShenAppearance() then --变身
        --     self.draw_obj:GetPart(part):SetIsAlwaysAnimate(true)
        -- end
        --天神光环特效
        -- self:ShowTianShenUnionSkillEffect(self:IsTianShenAppearance())

        -- self:CheckTianShenSpecialPartEff()

        local boat_obj = Scene.Instance:GetBoatByRole(self:GetObjId())
        if boat_obj then
            self.draw_obj:GetPart(SceneObjPart.Main):SetInteger("status", ActionStatus.Die)
            local point = boat_obj:GetBoatAttachPoint(self:GetObjId())
            if point then
                obj.gameObject.transform:SetParent(point, false)
                obj.gameObject.transform:SetLocalPosition(0, 0, 0)
                obj.gameObject.transform.rotation = u3dpool.vec3(0, 0, 0)
                obj.gameObject.transform:SetLocalScale(1, 1, 1)
            end
        end
        self.is_landed = true

        if self.embrace_data ~= nil then
            self:EmbraceItem(self.embrace_data.bundle_name, self.embrace_data.asset_name)
        end

        self:CheckBiggerBuff()
        if self:IsRole() and not self:IsInvisible() then
            self:CheckBuffShow()
        end

    elseif part == SceneObjPart.Wing or part == SceneObjPart.BaoJu then
        obj_class:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)
    elseif part == SceneObjPart.Weapon then
        if self.vo and self.vo.appearance_param ~= nil then
            self.draw_obj:GetPart(part):SetIsAlwaysAnimate(true)
        end
    end

    if self:IsGhost() and part == SceneObjPart.Main then
        obj.gameObject.transform:SetLocalScale(2.5, 2.5, 2.5)
    end

    if self:IsRiding() then
        if part == SceneObjPart.Main or part == SceneObjPart.Mount then
            local main_part_obj = self.draw_obj:_TryGetPartObj(SceneObjPart.Main)
            local mount_part_obj = self.draw_obj:_TryGetPartObj(SceneObjPart.Mount)

            if main_part_obj and mount_part_obj then
                if not self:IsMove() and not self:GetIsGatherState() then
                    self:CrossAction(SceneObjPart.Main, self:SetRidingActionIdelParam())
                end
            end
        end
    end
end 

-- 模型 移除 by part
function Role:RemoveModel(part, real_remove)
    if self:IsMainRole() then
        self.draw_obj:GetPart(part):SetIsAlwaysAnimate(false)
    end

    Character.RemoveModel(self, part, real_remove)
end

-- 获取角色部位资源
function Role:GetModelPartRes()
    --根据选择职业获取默认模型资源id

    local prof = self:GetProf()
    local appe_data = self.vo.appearance
    local body_res, face_res, hair_res = RoleWGData.GetShowRoleSkinPartRes(self.vo.sex, prof, appe_data.fashion_body,
    appe_data.default_body_res_id, appe_data.default_face_res_id, appe_data.default_hair_res_id)
    return body_res, face_res, hair_res
end

-- 改变主部位模型
function Role:ChangeMainPartModel(bundle, asset, callback, sync_anim_type, draw_model_type, extra_model_data, load_skin_callback)
    if not self.vo then return end
    
    local appe_data = self.vo.appearance
    local resouce = appe_data.fashion_body
    local fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByResId(SHIZHUANG_TYPE.BODY, resouce)

    if fashion_cfg then
        local is_open_dye = fashion_cfg and fashion_cfg.is_open_dyeing or 0
        local is_show_dye = is_open_dye == 1
        -- 这里加了一个操作，时装标记染色才会使用创角编辑的头发颜色，
        -- 没有染色的直接使用默认材质色，不适用编辑捏脸头发色
        if not is_show_dye and extra_model_data then
            extra_model_data.hair_color = nil
        end
    end

    local need_force = false
    if self.is_need_force_change_wait_sync_anim_type then
        need_force = true
        self.is_need_force_change_wait_sync_anim_type = nil
    end
    self:ChangeObjWaitSyncAnimType(sync_anim_type, need_force)
    
    if self:IsInEternalNightScene() and (not self:IsXiuWeiBianShen()) then
        if self:IsGhost() then
            self:ChangeModel(SceneObjPart.Main, bundle, asset, callback, draw_model_type, extra_model_data)
        else
            local role_uuid = self.vo.uuid
            local fashion_body = EternalNightWGData.Instance:GetRoleEquipShiZhuang(role_uuid, "fashion_body")

            local ete_night_face_res, ete_night_hair_res, ete_night_body_res
                = NewAppearanceWGData.Instance:GetRolePartResByResId(fashion_body, self.vo.sex, self:GetProf())

                local ete_night_extra_model_data = {
                    role_body_res = ete_night_body_res,
                    role_face_res = ete_night_face_res,
                    role_hair_res = ete_night_hair_res,
                }
            self:ChangeModel(SceneObjPart.Main, bundle, asset, callback, DRAW_MODEL_TYPE.ROLE, ete_night_extra_model_data, nil)
        end

        return
    end

    self:ChangeModel(SceneObjPart.Main, bundle, asset, callback, draw_model_type, extra_model_data, load_skin_callback)
    if self:IsMainRole() and (bundle == nil or asset == nil or bundle == "" or asset == "")
        and (TaskWGCtrl.Instance ~= nil and not TaskWGCtrl.Instance:IsFly()) then
        print_error("Set mainRole nil asset !", self.special_res_id, self.role_res_id)
    end
end

-- 高达模型
function Role:UpdateGundamModel()
    if not self.special_res_id or self.special_res_id == 0 then return end

    local gundam_seq = math.floor(self.special_res_id / 100000)
    self.is_bind_fight_mount_action_effect = nil
    self:UpdateRoleActorConfigPrefabData()
    self:ChangeObjWaitSyncAnimType(SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.GUNDAM)

    local part_res_list = {}
    for i = MECHA_PART_TYPE.BODY, MECHA_PART_TYPE.WEAPON do
        local cfg = MechaWGData.Instance:GetPartCfgBySeq(self.vo.appearance_param_extend[i])
        part_res_list[i] = cfg and cfg.res_id or 0
    end

    local gundam_body_res = part_res_list[MECHA_PART_TYPE.BODY]
    local gundam_weapon_res = part_res_list[MECHA_PART_TYPE.WEAPON]
    local gundam_left_arm_res = part_res_list[MECHA_PART_TYPE.LEFT_HAND]
    local gundam_right_arm_res = part_res_list[MECHA_PART_TYPE.RIGHT_HAND]
    local gundam_left_leg_res = part_res_list[MECHA_PART_TYPE.LEFT_FOOT]
    local gundam_right_leg_res = part_res_list[MECHA_PART_TYPE.RIGHT_FOOT]
    local gundam_left_wing_res = part_res_list[MECHA_PART_TYPE.LEFT_WING]
    local gundam_right_wing_res = part_res_list[MECHA_PART_TYPE.RIGHT_WING]

    local bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.BODY, gundam_body_res)
    self:ChangeModel(SceneObjPart.Main, bundle, asset, function ()
        SkillWGCtrl.Instance:PlaySkill()
        self:UpdateHpPosition()
    end)


	if gundam_weapon_res <= 0 then
        self:RemoveModel(SceneObjPart.Weapon)
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.WEAPON, gundam_weapon_res)
		self.draw_obj:ChangeModel(SceneObjPart.Weapon, bundle, asset)
	end

	if gundam_left_arm_res <= 0 then
		self:RemoveModel(SceneObjPart.GundamLArm)
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.LEFT_HAND, gundam_left_arm_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamLArm, bundle, asset)
	end

	if gundam_right_arm_res <= 0 then
		self:RemoveModel(SceneObjPart.GundamRArm)
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.RIGHT_HAND, gundam_right_arm_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamRArm, bundle, asset)
	end

	if gundam_left_leg_res <= 0 then
		self:RemoveModel(SceneObjPart.GundamLLeg)
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.LEFT_FOOT, gundam_left_leg_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamLLeg, bundle, asset)
	end

	if gundam_right_leg_res <= 0 then
		self:RemoveModel(SceneObjPart.GundamRLeg)
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.RIGHT_FOOT, gundam_right_leg_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamRLeg, bundle, asset)
	end

	if gundam_left_wing_res <= 0 then
		self:RemoveModel(SceneObjPart.GundamLWing)
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.LEFT_WING, gundam_left_wing_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamLWing, bundle, asset)
	end

	if gundam_right_wing_res <= 0 then
		self:RemoveModel(SceneObjPart.GundamRWing)
	else
		bundle, asset = ResPath.GetGundamPartModel(gundam_seq, MECHA_PART_TYPE.RIGHT_WING, gundam_right_wing_res)
		self.draw_obj:ChangeModel(SceneObjPart.GundamRWing, bundle, asset)
	end

    self:TryLeaveSit()
end

-- 武器 资源id
function Role:GetWeaponResId()
    return self.weapon_res_id
end

-- 羽翼 资源id
function Role:GetWingResId()
    local vo = self.vo
    if not vo  then return 0 end
    return vo.wing_appeid
end

-- 羽翼 更新翅膀
function Role:UpdateWing()
    local vo = self.vo
    if not vo  then return end
    if self:IsGhost() then
        return
    end

    if self:IsInEternalNightScene() then
        local role_uuid = self.vo.uuid
        local wing_appeid = EternalNightWGData.Instance:GetRoleEquipShiZhuang(role_uuid,"wing_appeid")
        self:ChangeModel(SceneObjPart.Wing, ResPath.GetWingModel(wing_appeid))
        -- if self:IsStand() then
        --     self:EnterStateStand()
        -- end
    else
        local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
        if 0 ~= vo.wing_appeid and 1 ~= fb_scene_cfg.pb_wing
            and SPECIAL_APPEARANCE_TYPE.NORMAL == self.vo.special_appearance
            then

            local skill_index = self.vo.appearance and self.vo.appearance.yuyi_skillindex or 0
            self.is_wing_fly = NewAppearanceWGData.Instance:GetFashionIsCanFly(SHIZHUANG_TYPE.WING, skill_index)
            self:ChangeModel(SceneObjPart.Wing, ResPath.GetWingModel(vo.wing_appeid))
            -- if self:IsStand() then
            --     self:EnterStateStand()
            -- end
        else
            self.is_wing_fly = false
            self:RemoveModel(SceneObjPart.Wing)
        end
        self.draw_obj:UpdateFabaoPosition()
        -- self:FlushStande()
    end
end

-- 羽翼 是否翅膀飞行
function Role:IsWingFly()
    return self.is_wing_fly
end

-- 披风 资源id
function Role:GetMantleResId()
    return self.mantle_res_id
end

-- 披风 更新资源id
function Role:UpdateMantleResId()
    self.mantle_res_id = 0
end

-- 披风 改变
function Role:ChangeMantle()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if self.mantle_res_id ~= nil and self.mantle_res_id ~= 0 and fb_scene_cfg.pb_wing ~= 1 then
        self:ChangeModel(SceneObjPart.Mantle, ResPath.GetPifengModel(self.mantle_res_id))
    else
        self:RemoveModel(SceneObjPart.Mantle)
    end
end

--坐骑 更新
function Role:UpdateMount(call_back)
    local vo = self.vo
    if not vo then
        return
    end

    self.is_fly_mount = false
    local mount_res_id = self:GetCurRidingResId()
    local mount_part_type = self:GetCurRidingPart()
    self:UpdateRoleActorConfigPrefabData()

    -- 策划需求，上坐骑调整镜头距离
    if self:IsMainRole() then
        local camera_dis = NewAppearanceWGData.Instance:GetMountCameraDis(mount_res_id)

        if camera_dis > 0 and camera_dis ~= self.cache_mount_camera_dis then
            self.cache_mount_camera_dis = camera_dis
            Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.UP_MOUNT, nil, nil, camera_dis)

        elseif camera_dis == 0 and self.cache_mount_camera_dis then
            Scene.Instance:RecoverCamera()
            self.cache_mount_camera_dis = nil
        end
    end

    if mount_res_id > 0 and not self.is_gather_state and not self:IsJump() then
        -- 当前坐骑类型不一样需要移除旧坐骑类型
        --[[
        local nor_mount_part = self.draw_obj:GetPart(SceneObjPart.Mount)
        local is_old_nor_mount = nor_mount_part.asset_name ~= nil
        local fight_mount_part = self.draw_obj:GetPart(SceneObjPart.FightMount)
        local is_old_fight_mount = fight_mount_part.asset_name ~= nil
        -- print_error("-----坐骑 更新----", mount_res_id, is_old_nor_mount, is_old_fight_mount)
        if mount_part_type == SceneObjPart.Mount and is_old_fight_mount then
            -- print_error("------切换普通坐骑 移除战斗坐骑-----")
            self:RemoveModel(SceneObjPart.FightMount)
        elseif mount_part_type == SceneObjPart.FightMount and is_old_nor_mount then
            -- print_error("------切换战斗坐骑 移除普通坐骑-----")
            self:RemoveModel(SceneObjPart.Mount)
        end
        ]]

        local bundle, name = ResPath.GetMountModel(mount_res_id)
        self:ChangeModel(mount_part_type, bundle, name, call_back)
        self.is_fly_mount = NewAppearanceWGData.Instance:GetIsFlyMount(mount_res_id)
    else
        self:RemoveModel(SceneObjPart.Mount)
        -- self:RemoveModel(SceneObjPart.FightMount)
    end

    -- 换坐骑时，刷新Role的显示优先级
    if not self:IsMainRole() then
        self:UpdatePriority()
    end
end

function Role:SetUpDownMountEffectLoader()
    local bundle, asset = ResPath.GetEnvironmentCommonEffect("eff_qicheng")
    if not self.up_down_mount_eff_loader then
        local obj = self.draw_obj:GetRoot()
        if obj ~= nil and not IsNil(obj.transform) then
            self.up_down_mount_eff_loader = AllocAsyncLoader(self, "up_down_mount_effect")
            self.up_down_mount_eff_loader:SetIsUseObjPool(true)
            self.up_down_mount_eff_loader:SetLoadPriority(ResLoadPriority.low)
            self.up_down_mount_eff_loader:SetParent(obj.transform)
            self.up_down_mount_eff_loader:SetObjAliveTime(3)
            self.up_down_mount_eff_loader:Load(bundle, asset)
        end
    else
        self.up_down_mount_eff_loader:SetActive(false)
        self.up_down_mount_eff_loader:Load(bundle, asset)
        self.up_down_mount_eff_loader:SetActive(true)
    end
end

-- 坐骑 带渐变效果移除
function Role:RemoveMonutWithFade()
    if self.draw_obj == nil then
        return
    end

    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if main_part == nil then
        return
    end
    
    local is_jump = self:IsJump()
    local is_strike_fly = self:GetIsSpecialState(OBJ_SPECIAL_STATE.STRIKE_FLY)
    if self:IsMove() then
        if not is_strike_fly and not is_jump and (not self:IsMitsurugi()) then
            self:CrossAction(SceneObjPart.Main, self:CurRunAni())
        end
    end

    if self:IsStand() then
        if not is_strike_fly and not is_jump and (not self:IsMitsurugi()) then
            self:CrossAction(SceneObjPart.Main, self:CurIdleAni())
        end
    end

    self:RemoveModel(SceneObjPart.Mount)
    self.is_fly_mount = false

    self:SetUpDownMountEffectLoader()

    --[[
    local nor_mount_part = self.draw_obj:GetPart(SceneObjPart.Mount)
    local is_old_nor_mount = nor_mount_part.asset_name ~= nil
    local fight_mount_part = self.draw_obj:GetPart(SceneObjPart.FightMount)
    local is_old_fight_mount = fight_mount_part.asset_name ~= nil

    if is_old_nor_mount and is_old_fight_mount then
        print_error("------【BigBug】坐骑移除渐变时 普通坐骑和战斗坐骑同时存在-----")
    elseif is_old_nor_mount then
        part_type = SceneObjPart.Mount
    elseif is_old_fight_mount then
        part_type = SceneObjPart.FightMount
    end
    ]]

    --[[
    local part_type = SceneObjPart.Mount
    if not part_type then
        return
    end

    -- 坐骑渐变
    local mount_part = self.draw_obj:GetPart(part_type)
    if nil ~= mount_part and mount_part:GetObj() then
        mount_part:Reset()
        local obj = mount_part:GetObj()
        if mount_part.remove_callback ~= nil then
            mount_part.remove_callback(self.draw_obj, obj, part_type, mount_part)
            mount_part.remove_callback = nil
        end

        local call_back = function()
            local new_obj = mount_part:GetObj()
            if new_obj ~= nil and new_obj.gameObject ~= nil then
                new_obj.gameObject.transform.rotation = u3dpool.vec3(0, 0, 0)
                mount_part:RemoveModel()
                mount_part:DeleteMe()
            end
        end

        if CgManager.Instance:IsCgIng() then
            call_back()
            self.draw_obj.part_list[part_type] = nil
            return
        end

        local fade_time = 1
        self.show_fade_out = false
        self:PlayMountFade(0, fade_time, call_back)
        if obj and obj.gameObject then
            self:DoMountRun(obj.gameObject, fade_time, 10)
            self.xiama_effect = AllocAsyncLoader(self, "eff_jump_luodi")
            self.xiama_effect:SetIsUseObjPool(true)
            self.xiama_effect:SetParent(self:GetRoot().transform)
            self.xiama_effect:SetObjAliveTime(5)
            local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_jump_luodi")
            self.xiama_effect:ReLoad(bundle_name, asset_name)
        end
        self.draw_obj.part_list[part_type] = nil
    end
    ]]
end

-- 坐骑 渐变
function Role:PlayMountFade(fade_type, fade_time, call_back)
    --[[
    local nor_mount_part = self.draw_obj:GetPart(SceneObjPart.Mount)
    local is_old_nor_mount = nor_mount_part.asset_name ~= nil
    local fight_mount_part = self.draw_obj:GetPart(SceneObjPart.FightMount)
    local is_old_fight_mount = fight_mount_part.asset_name ~= nil

    local part_type
    if is_old_nor_mount and is_old_fight_mount then
        print_error("------【BigBug】坐骑生成渐变时 普通坐骑和战斗坐骑同时存在-----")
    elseif is_old_nor_mount then
        part_type = SceneObjPart.Mount
    elseif is_old_fight_mount then
        part_type = SceneObjPart.FightMount
    end
    ]]
    
    local part_type = SceneObjPart.Mount
    local mount_part = self.draw_obj:GetPart(part_type)
    if nil ~= mount_part then
        local mount_obj = mount_part:GetObj()
        if mount_obj == nil then
            if call_back then
                call_back()
            end
            return
        end

        local actor_render = mount_obj.actor_render
        if actor_render ~= nil then
            actor_render:PlayFadeEffect(fade_type == 1, fade_time, nil)
        else
            if call_back then
                call_back()
            end
        end
    end

    self:AddDeleteMountCallBack(call_back)
end

-- 坐骑 位移
function Role:DoMountRun(obj, time, distance)
    if obj and obj.transform then
        local anim = obj:GetComponent(typeof(UnityEngine.Animator))
        if anim == nil then
            return
        end
        local target_pos = obj.transform.position + obj.transform.forward * distance
        if SceneObjLayer then
            obj.transform:SetParent(SceneObjLayer.transform, true)
        end

        local ani_name = self:CurRunAni()
        if ani_name == SceneObjAnimator.FightMove then  ---坐骑没有FightMove状态，这里这边判断方便以后拓展
            ani_name = SceneObjAnimator.Move
        end

        anim:CrossFadeInFixedTime(ani_name, 0.2)
        local tween = obj.transform:DOMove(target_pos, time)
        tween:SetEase(DG.Tweening.Ease.Linear)
    end
end

function Role:AddDeleteMountCallBack(callback)
    if not self.delete_mount_cache then
        self.delete_mount_cache = {}
    end

    table.insert(self.delete_mount_cache, {check_call_back = callback, check_time = self.curr_now_time + 1.5})
end

function Role:CheckDeleteMountModel(now_time)
    if self.delete_mount_cache == nil or #self.delete_mount_cache <= 0 then
        return
    end

    for i, v in ipairs(self.delete_mount_cache) do
        if v ~= nil and now_time >= v.check_time then
            if v.check_call_back then
                v.check_call_back()
            end
            table.remove(self.delete_mount_cache, i)
            v = nil
            break
        end
    end
end

function Role:DelectAllMountModelCache()
    if self.delete_mount_cache then
        for i, v in ipairs(self.delete_mount_cache) do
            if v.check_call_back then
                v.check_call_back()
            end
        end

        self.delete_mount_cache = nil
    end
end

-- 法宝 资源id
function Role:GetBaoJuResId()
    return self.baoju_res_id
end

-- 法宝 更新资源id
function Role:UpdateBaoJu()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if not fb_scene_cfg.pb_baoju or fb_scene_cfg.pb_baoju ~= 0 then
        self.baoju_res_id = nil
        return
    end

    self.baoju_res_id = self.vo.fabao_appeid
end

-- 法宝 模型改变
function Role:ChangeBaoJuModel()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if nil ~= self.baoju_res_id and self.baoju_res_id ~= 0 and fb_scene_cfg.pb_zhibao ~= 1 then
        self:ChangeModel(SceneObjPart.BaoJu, ResPath.GetFaBaoModel(self.baoju_res_id))
    else
        self:RemoveModel(SceneObjPart.BaoJu)
    end
end

-- 麒麟臂
function Role:UpdateQiLinBi()
    self.qilinbi_res_id = self.vo.appearance and self.vo.appearance.qilinbi or -1
end

-- 麒麟臂
function Role:ChangeQiLinBi()
    if self.vo == nil or self.qilinbi_res_id <= 0 then
         self:RemoveModel(SceneObjPart.QiLinBi)
        return
    end
    
    self:ChangeModel(SceneObjPart.QiLinBi, ResPath.GetQilinBiModel(self.qilinbi_res_id, self.vo.sex))
end

-- 麒麟臂
function Role:GetQiLinBiResId()
    return self.qilinbi_res_id
end

-- 腰饰
function Role:UpdateWaist()
    self.waist_res_id = self.vo.appearance and self.vo.appearance.waist or -1
end

-- 腰饰
function Role:ChangeWaist()
    if self.vo == nil or self.waist_res_id <= 0 then
         self:RemoveModel(SceneObjPart.Waist)
        return
    end

    self:ChangeModel(SceneObjPart.Waist, ResPath.GetBeltModel(self.waist_res_id))
end

-- 腰饰
function Role:GetWaistResId()
    return self.waist_res_id
end

-- 面饰
function Role:UpdateMask()
    self.mask_res_id = self.vo.appearance and self.vo.appearance.mask or -1
end

-- 面饰
function Role:ChangeMask()
    if self.vo == nil or self.mask_res_id <= 0 then
         self:RemoveModel(SceneObjPart.Mask)
        return
    end

    self:ChangeModel(SceneObjPart.Mask, ResPath.GetMaskModel(self.mask_res_id))
end

-- 面饰
function Role:GetMaskResId()
    return self.mask_res_id
end

-- 手环
function Role:UpdateShouHuan()
    self.shouhuan_res_id = self.vo.appearance and self.vo.appearance.shouhuan or -1
end

-- 手环
function Role:ChangeShouHuan()
    if self.vo == nil or self.shouhuan_res_id <= 0 then
         self:RemoveModel(SceneObjPart.ShouHuan)
        return
    end

    self:ChangeModel(SceneObjPart.ShouHuan, ResPath.GetShouhuanModel(self.shouhuan_res_id))
end

-- 手环
function Role:GetShouHuanResId()
    return self.shouhuan_res_id
end

-- 尾巴
function Role:UpdateTail()
    self.tail_res_id = self.vo.appearance and self.vo.appearance.tail or -1
end

-- 尾巴
function Role:ChangeTail()
    if self.vo == nil or self.tail_res_id <= 0 then
         self:RemoveModel(SceneObjPart.Tail)
        return
    end

    self:ChangeModel(SceneObjPart.Tail, ResPath.GetWeibaModel(self.tail_res_id))
end

-- 尾巴
function Role:GetTailResId()
    return self.tail_res_id
end

-- 剑阵
function Role:UpdateJianZhen()
    local vo = self.vo
    if not vo  then return end
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if 0 ~= vo.jianzhen_appeid and 1 ~= fb_scene_cfg.pb_jianling
        and SPECIAL_APPEARANCE_TYPE.NORMAL == self.vo.special_appearance
        then
        self:ChangeModel(SceneObjPart.Jianling, ResPath.GetJianZhenModel(vo.jianzhen_appeid))
        if self:IsStand() then
            self:EnterStateStand()
        end
    else
        self:RemoveModel(SceneObjPart.Jianling)
    end
    self.jianzhen_res_id = self.vo.jianzhen_appeid or -1
end

-- 剑阵
function Role:GetJianZhenResId()
    return self.jianzhen_res_id
end

-- 光环 资源id
function Role:GetHaloResId()
    return self.halo_res_id
end

-- 光环 更新资源id
function Role:UpdateHaloResId()
    if self.vo ~= nil and self.vo.appearance ~= nil and self.vo.appearance.fashion_guanghuan > 0 then
        self.halo_res_id = self.vo.appearance.fashion_guanghuan
    else
        self.halo_res_id = 0
    end
end

-- 光环
function Role:ChangeHalo()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if self.halo_res_id ~= nil and self.halo_res_id ~= 0 and fb_scene_cfg.pb_guanghuan ~= 1 then
        self:ChangeModel(SceneObjPart.Halo, ResPath.GetHaloModel(self.halo_res_id))
    else
        self:RemoveModel(SceneObjPart.Halo)
    end
end

function Role:UpdateSkillHaloResId()
    if self.vo ~= nil and self.vo.appearance ~= nil and self.vo.appearance.skill_halo_id > 0 then
        self.skill_halo_res_id = self.vo.appearance.skill_halo_id
    else
        self.skill_halo_res_id = 0
    end
end

-- 技能光环
function Role:ChangeSkillHalo()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    local is_show = fb_scene_cfg.pb_skillhalo ~= 1 and (not self.is_fighting)
    if self.skill_halo_res_id ~= nil and self.skill_halo_res_id ~= 0 and is_show then
        self:ChangeModel(SceneObjPart.SkillHalo, ResPath.GetSkillHaloModel(self.skill_halo_res_id))
    else
        self:RemoveModel(SceneObjPart.SkillHalo)
    end
end

-- 法阵
function Role:ChangeFaZhen()
    local res_id = self.vo.appearance and self.vo.appearance.fazhen_id
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg.pb_fazhen == 1 then
        res_id = -1
    end

    self.fazhen_res_id = res_id
    if res_id and res_id > -1 then
        self:ChangeModel(SceneObjPart.FaZhen, ResPath.GetSkillFaZhenModel(self.fazhen_res_id))
    else
        self:RemoveModel(SceneObjPart.FaZhen)
    end
end

-- 任务变身id
function Role:GetTaskToBianShen()
    if self.vo.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE then
        return self.vo.task_appearn_param_1
    end

    return 0
end

-- 鬼魂模型
function Role:ChangeGhostModel()
    if self:IsGhost() then
        self:RemoveModel(SceneObjPart.Weapon)
        self:RemoveModel(SceneObjPart.Wing)
        self:RemoveModel(SceneObjPart.Halo)
        self:RemoveModel(SceneObjPart.BaoJu)
        self:RemoveModel(SceneObjPart.QiLinBi)
        self:RemoveModel(SceneObjPart.Waist)
        self:RemoveModel(SceneObjPart.Mask)
        self:RemoveModel(SceneObjPart.ShouHuan)
        self:RemoveModel(SceneObjPart.Tail)
        self:RemoveModel(SceneObjPart.Jianling)
        self:RemoveModel(SceneObjPart.FaZhen)
        self:RemoveModel(SceneObjPart.SkillHalo)
        self:RemoveModel(SceneObjPart.GodOrDemonHalo)
        -- local bundle, asset = ResPath.GetPetModel(10112001)
        local bundle, asset = ResPath.GetBeastsModel(4032)
        self:ChangeMainPartModel(bundle, asset)
        self:RemoveWuHun()
        self:DeleteTaskCallInfo()
        return true
    end
    
    return false
end

-- 是否鬼魂
function Role:IsGhost()
    local scene_type = Scene.Instance:GetSceneType()
    local can_show_ghost = ((scene_type == SceneType.Kf_PVP) or (scene_type == SceneType.ETERNAL_NIGHT_FINAL)
    or (scene_type == SceneType.TEAM_COMMON_TOWER_FB_1))
    if can_show_ghost then
        return self.vo.ghost and self.vo.ghost == EnumGhostTpye.IsGhost
    else
        self.vo.ghost = EnumGhostTpye.NotGhost
        return false
    end
end

-- 心魔模型
function Role:ChangeDemonsModel()
    if self.vo.is_demons and self.vo.is_demons == EnumDemonsTpye.IsDemons then
        local bundle, asset = ResPath.GetMonsterModel(FuBenPanelWGData.Instance:GetDemonsResId())
        self:ChangeMainPartModel(bundle, asset)
        return true
    end

    return false
end

-- 灵童 可见性
function Role:UpdateSoulBoyVisible()
    if self.soulboy_obj then
        self.soulboy_obj:RefreshVisiable()
    end
end

--灵童
function Role:ChangeSoulBoy()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg.pb_lingtong == 0 then
        return
    end

    if ((self.vo.soulboy_lt_id and self.vo.soulboy_lt_id > 0) or self.vo.use_baby_id > -1) and not self:IsRealDead() then
        if not self.soulboy_obj then
            self.soulboy_obj = Scene.Instance:CreateSoulBoyObjByRole(self)
        end

        if self.soulboy_obj then
            if self.vo.soulboy_lt_id > 0 then
                self.soulboy_obj:SetAttr("soulboy_lt_id", self.vo.soulboy_lt_id)
            elseif self.vo.use_baby_id > -1 then
                self.soulboy_obj:SetAttr("use_baby_id", self.vo.use_baby_id)
            end
        end
    else
        self.soulboy_obj = self:DeleteObjFunction(SceneObjType.SoulBoyObj, self.soulboy_obj)
    end
end

-- 灵童 对象获取
function Role:GetSoulBoyObj()
    return self.soulboy_obj
end

-- 灵童
function Role:ReleaseSoulBoyObj()
    self.soulboy_obj = nil
end

--灵童武器
function Role:ChangeSoulBoyWeapon(value)
    if self.soulboy_obj ~= nil then
        self.soulboy_obj:SetAttr("soulboy_lg_id", value or self.vo.soulboy_lg_id)
    end
end

-- 足迹 回调
Role.FootPrintCount = 0
local function CreateFootPrintCallBack(obj, transform)
    if IsNil(obj) then
        return
    end

    local pos = transform.position
    local rot = transform.localEulerAngles
    obj.transform.position = u3dpool.vec3(pos.x, pos.y + 0.25, pos.z)
    obj.transform.localEulerAngles = Vector3(0, rot.y, 0)
end


local function ReducePrintCount()
    Role.FootPrintCount = Role.FootPrintCount - 1
end
-- 足迹 创建
function Role:CreateFootPrint()
    if self:IsJump() or self:IsWaterWay() or self:IsNeedCreadFoot() or self.hide_foot or not self:GetVisiable() or self:IsInvisible() then
        return
    end

    local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if scene_cfg.pb_zuji and 1 == scene_cfg.pb_zuji then
        return
    end

    local bundle, asset
    if self:IsSplit() then
        bundle = "effects2/prefab/misc/effect_tianshen_xueji_prefab"
        asset = "effect_tianshen_xueji"
    else
        if not self.vo.appearance then
            return
        end

        local foot_id = self.vo.appearance.fashion_foot
        if foot_id == 0 then
            return
        end

        if not self:IsMainRole() and Role.FootPrintCount > 10 then
            return
        end

        bundle, asset = ResPath.GetFootEffect(foot_id)
    end

    if bundle == nil or asset == nil then
        return
    end

    Role.FootPrintCount = Role.FootPrintCount + 1
    GlobalTimerQuest:AddDelayTimer(ReducePrintCount, 1)

    self.foot_count = self.foot_count + 1
    local async_loader = AllocAsyncLoader(self, "foot_print" .. self.foot_count % 15)
    async_loader:SetIsUseObjPool(true)
    async_loader:SetObjAliveTime(2) --防止永久存在
    async_loader:SetParent(G_EffectLayer)
    async_loader:Load(bundle, asset, CreateFootPrintCallBack, self.draw_obj:GetRoot().transform)
end

-- 足迹 是否创建足迹只对自己(巡游时候不播放，false是)
function Role:IsNeedCreadFoot()
    if self.obj_type and self.obj_type == SceneObjType.MainRole then
        local is_xunyou = MarryWGData.Instance:GetOwnIsXunyou()
        return is_xunyou

    --她对象的足迹也得屏蔽
    elseif self.vo then
        local is_show = MarryWGData.Instance:GetRoleIsshowFoot(self.vo.role_id)
        return is_show
    end

    return false
end

-- 名将 可见性
function Role:UpdateFollowMingJiangVisible()
    if self.follow_mingjiang then
        self.follow_mingjiang:RefreshVisiable()
    end
end

-- 守护 可见性
function Role:UpdateGuardVisible()
    if self.guard_obj then
        self.guard_obj:RefreshVisiable()
    end
end

-- 守护 更新
function Role:ChangeGuard()
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if fb_scene_cfg.pb_shouhu == 1 then
        return
    end

    if self.vo.guard_id and self.vo.guard_id > 0 then
        if not self.guard_obj then
            self.guard_obj = Scene.Instance:CreateGuardObjByRole(self)
        else
            self.guard_obj:SetAttr("guard_id", self.vo.guard_id)
        end
    else
        self.guard_obj = self:DeleteObjFunction(SceneObjType.GuardObj, self.guard_obj)
    end
end

-- 守护 对象获取
function Role:GetGuardObj()
    return self.guard_obj
end

-- 守护
function Role:ReleaseGuardObj()
    self.guard_obj = nil
end

--哮天犬
function Role:ChangeXiaoTianQuan()
     if self.vo.xiaotianquan_id and self.vo.xiaotianquan_id > 0 and not self:IsRealDead() then
        if not self.xiaotianquan then
            self.xiaotianquan = Scene.Instance:CreateXiaoTianQuanByRole(self)
        end
    else
        self.xiaotianquan = self:DeleteObjFunction(SceneObjType.XiaoTianQuan, self.xiaotianquan)
    end
end

--哮天犬
function Role:GetXiaoTianQuan()
    return self.xiaotianquan
end

--哮天犬
function Role:ReleaseXiaoTianQuan()
    self.xiaotianquan = nil
end

-- 美人
function Role:ChangeBeauty()
    if 0 ~= self.vo.husong_taskid and 0 ~= self.vo.husong_color then
        if not self.beauty_obj then
            self.beauty_obj = Scene.Instance:CreateBeautyObjByRole(self)
            self.beauty_obj:SetAttr("husong_color", self.vo.husong_color)
        -- else
        -- --此处赋值会在断线重连的时候对空值进行赋值导致整个游戏卡在护送不能动弹
        --     self.beauty_obj:SetAttr("husong_color", self.vo.husong_color)
        end
    else
        self.beauty_obj = self:DeleteObjFunction(SceneObjType.BeautyObj, self.beauty_obj)
    end
end

-- 美人
function Role:GetBeautyObj()
    return self.beauty_obj
end

--名将
function Role:ChangeMingJiang(task_id)
    if TaskWGData.Instance:GetTaskIsAccepted(task_id) then
        if not self.mingjiang_obj and self:IsMainRole() then
            self.mingjiang_obj = Scene.Instance:CreateMingjiangObjByRole(self)
        end
    else
        self.mingjiang_obj = self:DeleteObjFunction(SceneObjType.MingJiangObj, self.mingjiang_obj)
    end
end

--名将
function Role:GetMingjaingObj()
    return self.mingjiang_obj
end

--名将
function Role:GetFollowMingJiang()
    return self.follow_mingjiang
end

--名将
function Role:ReleaseFollowMingJiang()
    self.follow_mingjiang = nil
end

-- 宠物
function Role:UpdatePetVisible()
    if self.pet_obj_list then
        for k,v in pairs(self.pet_obj_list) do
            v:RefreshVisiable()
            v:UpdateGameObjectAttachVisible()
        end
    end
end

--宠物
function Role:ChangePet()
    if self.vo.lingchong_appeid and self.vo.lingchong_appeid > 0 then
        local pet_obj_list = self:GetPetObjList()
        local pet_obj = pet_obj_list and pet_obj_list[1] -- 目前只有一只
        if not pet_obj or pet_obj:IsDeleted() then
            pet_obj = Scene.Instance:CreatePetObjByRole(self)
            if pet_obj then
                self:SetPet(pet_obj)
            end
        else
            pet_obj:SetAttr("lingchong_appeid", self.vo.lingchong_appeid)
        end
        if pet_obj and pet_obj:IsOnwerPet() then
            pet_obj:SetIsPerformer(true)
        end
    else
        self:DeleteAllPet()
    end
end

-- 宠物 对象列表
function Role:GetPetObjList()
    return self.pet_obj_list
end

-- 宠物 find
function Role:FindPet()
    self.pet_obj_list = {}
    local pet_list = Scene.Instance:GetObjListByType(SceneObjType.Pet)
    for k,v in pairs(pet_list) do
        if not v:IsDeleted() and v:IsOnwerPet() then
            self:SetPet(v)
        end
    end
end

-- 宠物 set
function Role:SetPet(pet_obj)
    for _,v in pairs(self.pet_obj_list) do
        if v.res_id == pet_obj.res_id then
            return
        end
    end
    table.insert(self.pet_obj_list, pet_obj)
    pet_obj:SetOwnerObj(self)
    pet_obj:UpdateQualityLevel()
    pet_obj:UpdateEffectVisible()
    self:UpdatePetVisible()
end

-- 宠物 人物改变 刷新宠物名
function Role:FlushPetNameByMainRoleChange()
    if not self.pet_obj_list then
        return
    end
    for k,v in pairs(self.pet_obj_list) do
        if v.follow_ui then
            v.follow_ui:SetPetNameByMainRoleChange()
        end
    end
end

-- 宠物 移除by obj
function Role:RemovePet(pet_obj)
    local pet_obj_list = self.pet_obj_list
    for i=1,#pet_obj_list do
        if pet_obj_list[i] == pet_obj then
            table.remove(self.pet_obj_list, i)
            break
        end
    end
end

-- 宠物 移除all
function Role:DeleteAllPet()
    for k,v in pairs(self.pet_obj_list) do
        if v and v:GetVo() then
            Scene.Instance:DeleteObjByTypeAndKey(SceneObjType.Pet, v:GetObjKey())
        end
    end
    self.pet_obj_list = {}
end

-- 温泉皮艇
function Role:UpdateBoat(is_enter_scene)
    if self:GetIsInSpring() then --温泉场景
        if self:IsMove() then
            self:QuitStateMove()
            if self:IsMainRole() then
                GuajiWGCtrl.Instance:ResetMoveCache()
                self.delay_end_move_time = Status.NowTime
                self:ChangeToCommonState()
            end
        end

        --角色刚进入场景
        if is_enter_scene then
            local action_data = HotSpringWGData.Instance:GetShuangXiuRoleInfoById(self:GetObjId())
            if action_data ~= nil then
                if action_data.action_type == HOTSPRING_BOAT_TYPE.SHUANGXIU then
                    self:ShuangXiuState(SHUANGXIU_TYPE.IS_SHUANGXIU)
                    self:SetShuangxiuPartner(action_data.role_obj_id2)
                elseif action_data.action_type == HOTSPRING_BOAT_TYPE.ANMO then
                    self:SetAnmoState(ANMO_TYPE.IS_ANMO)
                    self:SetAnmoPartnerId(action_data.role_obj_id2)
                end
            end
            -- return
        end

        local is_anmo = false
        local anmo_prop = self:GetAnmoPartnerId()
        if anmo_prop >= 0 and anmo_prop < 65535 then
            is_anmo = true
            local obj = Scene.Instance:GetObjectByObjId(anmo_prop)
            if obj then
                if self:GetIsAnmoSender() then --如果是发起人
                    Scene.Instance:CreateBoatByCouple(self:GetObjId(), anmo_prop, obj, HOTSPRING_BOAT_TYPE.ANMO)
                else
                    Scene.Instance:CreateBoatByCouple(anmo_prop, self:GetObjId(), obj, HOTSPRING_BOAT_TYPE.ANMO)
                end
            end
        else
            Scene.Instance:DeleteBoatByRole(self:GetObjId())
        end

        if not is_anmo then
            local special_param = self:SetShuangxiuPartner()
            if special_param >= 0 and special_param < 65535 then
                local obj = Scene.Instance:GetObjectByObjId(special_param)
                if obj then
                    Scene.Instance:CreateBoatByCouple(self:GetObjId(), special_param, obj, HOTSPRING_BOAT_TYPE.SHUANGXIU)
                end
            else
                Scene.Instance:DeleteBoatByRole(self:GetObjId())
            end
        end
    end
end

-- 圣女 更新资源id
function Role:UpdateHoldBeauty()
    self.hold_beauty_res_id = 0
    local npc_cfg = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.vo.hold_beauty_npcid]
    if npc_cfg then
        if npc_cfg.beauty_res and npc_cfg.beauty_res ~= "" and npc_cfg.beauty_res > 0 then
            self.hold_beauty_res_id = npc_cfg.beauty_res
        end
    end
end

-- 圣女 改变
function Role:ChangeHoldBeauty()
    self:UpdateHoldBeauty()

    if self.hold_beauty_res_id > 0 then
        self:ChangeModel(SceneObjPart.HoldBeauty, ResPath.GetGoddessModel(self.hold_beauty_res_id))
    else
        self:RemoveModel(SceneObjPart.HoldBeauty)
    end
end

-- 战车 是否是仙盟战战车
function Role:IsXMZCar()
    return self.vo and SPECIAL_APPEARANCE_TYPE.XMZ == self.vo.special_appearance
end

-- 跟随对象 检测可见性
function Role:CheckFollowObjShadowVisiable(visible)
    local guard_obj = self:GetGuardObj()
    if guard_obj and not guard_obj:IsDeleted() then
    	guard_obj:ChangeShadowVisible(visible)
    end
end

-- 设置人物模型移动速度
function Role:UpdateMainRoleMoveSpeed()
    local move_speed_type = 1
    local move_speed = self.vo and self.vo.move_speed or 0
    if move_speed < Config.SCENE_MOUNT_MOVE_SPEED then
        move_speed_type = Config.SCENE_ROLE_MOVE_SPEED
    else
        move_speed_type = Config.SCENE_MOUNT_MOVE_SPEED
    end

    move_speed_type = Scene.ServerSpeedToClient(move_speed_type)
    local speed = self:GetMoveSpeed()
    local role_move_speed = speed / move_speed_type
    if self.draw_obj_ani_speed_factor == role_move_speed then
        return
    end

    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if main_part then
        self.draw_obj_ani_speed_factor = role_move_speed
        main_part:SetFloat("speed", role_move_speed)
    end
end

-- 设置模型拜谒时的动作和气泡框
function Role:SetRoleModeBaiYeAction(target, time, ia_auto_cancel)
    if not target then return end
    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    if nil ~= part then
        --for k,v in pairs(Scene.Instance:GetCgObjList()) do
        --    if v:GetRoot() then
        --        self:RotaToTarget(v:GetRoot().transform.position, 0.1)
        --        break
        --    end
        --end
        if self:IsMainRole() then
            self:StopMove()
        end

        self:SetIsGatherState(true, 0, time)
        self:RotaToTarget(target:GetRoot().transform.position, 0.)

        local scene_id = Scene.Instance:GetSceneId()
        local is_click = ActivityWGData.Instance:GetIsInListByID(scene_id)
        if is_click then
            local content_cfg = ActivityWGData.Instance:GetBubbleContentById(scene_id)
            if content_cfg and next(content_cfg) then
                self:Say(content_cfg.bubble_text, content_cfg.disappear_time)
            end
        end
    end
    --if is_auto_cancel then
    --    if self.delay_stop_baiye then
    --        GlobalTimerQuest:CancelQuest(self.delay_stop_baiye)
    --        self.delay_stop_baiye = nil
    --    end
    --    self.delay_stop_baiye = GlobalTimerQuest:AddDelayTimer(function ()
    --        self:SetIsGatherState(false)
    --    end, 1)
    --end
end

-- 获取场景变身资源
function Role:GetSceneBianShenRes(res_id)
    local bundle = nil
    local asset = nil
    res_id = res_id or self.special_res_id
    if res_id ~= nil and res_id ~= 0 then
        local cfg = TianShenWGData.Instance:GetImageModelByAppeId(res_id)
        bundle, asset = ResPath.GetBianShenModel(res_id)
        if cfg ~= nil and cfg.scene_image_id ~= "" then
            asset = cfg.scene_image_id .. "_scene"
        end
    end

    return bundle, asset
end

-- 获取动作时间
function Role:GetActionTimeRecord(anim_name)
    if (self:IsTianShenAppearance()
        or self:IsXMZCar()) and TianShenBossActionConfig[self.special_res_id] --变身
        then
        return TianShenBossActionConfig[self.special_res_id][anim_name]
    
    elseif self:IsGundam() then
        return GundamActionConfig[self.special_res_id][anim_name]

    elseif self:IsRidingFightMount() then
        local appid = self:GetCurRidingResId()
        return (ZuoqiActionConfig[appid] or {})[anim_name]
    elseif self:IsXiuWeiBianShen() then
        local power_type = math.floor(self.vo.appearance_param / 1000) + 1
        return ((RoleActionConfig[self.vo.sex] or {})[power_type + 1000] or {})[anim_name]
    end

    return ((RoleActionConfig[self.vo.sex] or {})[self:GetProf()] or {})[anim_name]
end

-- 更新形象 永夜之巅
function Role:UpdateAppearanceInEternalNight()
    if self:IsGhost() then
        return
    end

    if self:IsXiuWeiBianShen() then
        return
    end

    local bundle, name = nil, nil
    if self:IsTianShenAppearance() then --变身
        bundle, name = self:GetSceneBianShenRes(self.vo.appearance_param)
        self:ChangeMainPartModel(bundle, name, function ()
            self:UpdateHpPosition()
        end, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN)
        
        self:EquipDataChangeListen()
        self:UpdateRoleActorConfigPrefabData()
        return
    end

    if self:IsGundam() then
        self:UpdateGundamModel()
        return
    end

    bundle, name = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.vo.sex, self:GetProf()))
    self:ChangeMainPartModel(bundle, name, nil, SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE)

    self:UpdateWing()
    self:EquipDataChangeListen()
end

-- 武魂
function Role:ChangeWuHun()
    local is_mount = self:IsRiding()
    if self.vo and self.vo.wuhun_id and self.vo.wuhun_id > 0 and not self:IsRealDead() and (not is_mount) then
        if not self.wuhun_obj then
            self.wuhun_obj = Scene.Instance:CreateWuHunByRole(self)
        else
            self.wuhun_obj:ChangeWuHunImageId(self.vo.wuhun_id, self.vo.wuhun_lv)
            self.wuhun_obj:AttachToRoleMainPart()
        end
    else
        self:RemoveWuHun()
    end
end

-- 武魂魂阵
function Role:ChangeWuHunHunZhen()
    if self.wuhun_obj then
        self.wuhun_obj:BuildWuHunHunZhenInstance()
    end
end

--武魂
function Role:GetWuHun()
    return self.wuhun_obj
end

--武魂
function Role:RemoveWuHun()
    self.wuhun_obj = self:DeleteObjFunction(SceneObjType.WuHunZhenShen, self.wuhun_obj)
end

-- 移除武魂
function Role:ReleaseWuHunObj()
    self.wuhun_obj = nil
end

-- 武魂
function Role:UpdateWuHunVisible()
    if self.wuhun_obj then
        self.wuhun_obj:RefreshVisiable()
        self.wuhun_obj:UpdateGameObjectAttachVisible()
    end
end

function Role:BuildWuHunInstance(wuhun_skill_id)
    if self.wuhun_obj then
        self.wuhun_obj:BuildWuHunInstance(wuhun_skill_id)
    end
end

function Role:RemoveWuHunInstance()
    if self.wuhun_obj then
        self.wuhun_obj:RemoveWuHunInstance()
    end

    self:SetAttr("wuhun_hunzhen", -1)
end

-- 灵兽 更新灵兽显示
function Role:UpdateBeastVisible()
    if not self.beast_obj then
        return
    end

    self.beast_obj:RefreshVisiable()
    self.beast_obj:UpdateGameObjectAttachVisible()
end

-- 灵兽变更
function Role:ChangeBeast()
	local shield_beast = SettingWGData.Instance:GetSettingData(SETTING_TYPE.CLOSE_OTHERS_BEAST)
    if self.vo.beast_id and self.vo.beast_id > 0 and (not shield_beast) then
        local beast_obj = self:FindBeast()
        if not beast_obj or beast_obj:IsDeleted() then
            beast_obj = Scene.Instance:CreateBeastObjByRole(self, self.vo.beast_id)
            if beast_obj then
                self:SetBeast(beast_obj)
            end
        else
            self:SetBeast(beast_obj)
            beast_obj:TryFlushAppearance(self.vo.beast_id, self.vo.beast_skin)
        end
    else
        self:DeleteAllBeast()
    end
end

-- 灵兽 对象列表
function Role:GetBeastObjList()
    return self.beast_obj
end

-- 灵兽 find
function Role:FindBeast()
    return self.beast_obj
end

-- 灵兽 
function Role:SetBeast(beast_obj)
    local now_beast_obj = self:FindBeast()
    -- 和当前已存在的一摸一样的不予理会
    if now_beast_obj then
        now_beast_obj:DoStand()
    end

    if now_beast_obj and now_beast_obj.beast_id == beast_obj.beast_id then
        return
    end

    self.beast_obj = beast_obj
    beast_obj:DoStand()
    beast_obj:SetOwnerObj(self)
    beast_obj:UpdateQualityLevel()
    beast_obj:UpdateEffectVisible()
    self:UpdateBeastVisible()
end

-- 灵兽 移除通过出战id
function Role:DeleteAllBeastById()
    if self.beast_obj then
        if self.beast_obj:GetVo() then
            Scene.Instance:DeleteObjByTypeAndKey(SceneObjType.BeastObj, self.beast_obj:GetObjKey())
        end
    end

    self.beast_obj = nil
end

-- 灵兽 移除all
function Role:DeleteAllBeast()
    if self.beast_obj then
        if self.beast_obj:GetVo() then
            Scene.Instance:DeleteObjByTypeAndKey(SceneObjType.BeastObj, self.beast_obj:GetObjKey())
        end
    end

    self.beast_obj = nil
end

function Role:GeBeastRealPosNumber(real_x, real_y, beast_battle_index, radius)
    if (not beast_battle_index) then
        return real_x, real_y
    end

    if not self.draw_obj then
        return self.real_pos 
    end

    local role_transform = self.draw_obj:GetTransfrom()

    if not role_transform then
        return self.real_pos 
    end

    local pos = nil
    --left_back 
    local left_back = -role_transform.forward + (-role_transform.right - role_transform.forward) * (radius / 2)
    local center_back = -role_transform.forward * radius
    local right_back = -role_transform.forward + (role_transform.right - role_transform.forward) * (radius / 2)
    if beast_battle_index == 1 then
        pos = role_transform.position + left_back       ---左后
    elseif beast_battle_index == 2 then
        pos = role_transform.position + center_back   ---正后
    elseif beast_battle_index == 3 then
        pos = role_transform.position + right_back   ---右后
    end

    if not pos then
        return real_x, real_y
    end

    return pos.x, pos.z
end

--任务召唤物
function Role:ChangeTaskCallInfo()
    if self.vo.task_callinfo_id and self.vo.task_callinfo_id > 0 then
        local task_callinfo_obj = self:GetTaskCallInfoObj()
        if not task_callinfo_obj or task_callinfo_obj:IsDeleted() then
            task_callinfo_obj = Scene.Instance:CreateTaskCallInfoObjObjByRole(self, self.vo.task_callinfo_id)
            if task_callinfo_obj then
                self:SetTaskCallInfo(task_callinfo_obj)
            end
        else
            self:SetTaskCallInfo(task_callinfo_obj)
            task_callinfo_obj:TryFlushAppearance()
        end
    else
        self:DeleteTaskCallInfo()
    end
end

-- 任务召唤物 对象
function Role:GetTaskCallInfoObj()
    return self.task_callinfo_obj
end

-- 任务召唤物 set
function Role:SetTaskCallInfo(task_callinfo_obj)
    if self.task_callinfo_obj and self.task_callinfo_obj.task_callinfo_id == task_callinfo_obj.task_callinfo_id then
        return
    end

    self.task_callinfo_obj = task_callinfo_obj
    task_callinfo_obj:SetOwnerObj(self)
    task_callinfo_obj:UpdateQualityLevel()
    task_callinfo_obj:UpdateEffectVisible()
    self:UpdateTaskCallInfoVisible()
end

-- 任务召唤物
function Role:UpdateTaskCallInfoVisible()
    if self.task_callinfo_obj then
        self.task_callinfo_obj:RefreshVisiable()
        self.task_callinfo_obj:UpdateGameObjectAttachVisible()
    end
end

-- 任务召唤物 移除
function Role:DeleteTaskCallInfo()
    if self.task_callinfo_obj and self.task_callinfo_obj:GetVo() then
        Scene.Instance:DeleteObjByTypeAndKey(SceneObjType.TaskCallInfoObj, self.task_callinfo_obj:GetObjKey())
    end

    self.task_callinfo_obj = nil
end

-- 双生神灵
function Role:ChangeShuangShengTianShen()
    if self:IsTianShenAppearance() then --变身
        if self.vo and self.vo.shaungsheng_tianshen_aura_id and self.vo.shaungsheng_tianshen_aura_id > -1 and (not self:IsRealDead()) then
            if not self.shaungsheng_tianshen_obj then
                self.shaungsheng_tianshen_obj = Scene.Instance:CreateShuangShengTianShenByRole(self)
            else
                self.shaungsheng_tianshen_obj:ChangeShuangShengImageId(self.vo.shaungsheng_tianshen_aura_id)
                self.shaungsheng_tianshen_obj:AttachToRoleMainPart()
            end
        else
            self:RemoveShuangShengTianShen()
        end
    else
        self:RemoveShuangShengTianShen()
    end
end

--双生神灵
function Role:GetShuangShengTianShen()
    return self.shaungsheng_tianshen_obj
end

--双生神灵
function Role:RemoveShuangShengTianShen()
    self.shaungsheng_tianshen_obj = self:DeleteObjFunction(SceneObjType.ShuangShengTianShen, self.shaungsheng_tianshen_obj)
end

-- 移除双生神灵
function Role:ReleaseShuangShengTianShenObj()
    self.shaungsheng_tianshen_obj = nil
end

-- 双生神灵
function Role:UpdateShuangShengTianShenVisible()
    if self.shaungsheng_tianshen_obj then
        self.shaungsheng_tianshen_obj:RefreshVisiable()
        self.shaungsheng_tianshen_obj:UpdateGameObjectAttachVisible()
    end
end
--模型展示 end]]





-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------     技能 or 特效 or Buff    --------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-- 技能绑定
function Role:ChangeSkillBindEffect()
    self:UpdateRoleActorConfigPrefabData()
end

-- 温泉特效 移动时 水流散开特效
function Role:CreateHotSpringMoveEffect()
    if nil == self.hot_spring_move_effect then
        local role_foot_point = self.draw_obj:GetAttachPoint(AttachPoint.HurtRoot)
        local bundle, asset = ResPath.GetMiscEffect("effect_swimming_02")
        self.hot_spring_move_effect = AllocAsyncLoader(self, "hot_spring_move_effect")
        self.hot_spring_move_effect:SetIsUseObjPool(true)
        self.hot_spring_move_effect:SetParent(role_foot_point.transform)
        self.hot_spring_move_effect:Load(bundle, asset,
            function(obj)
                if IsNil(obj) then
                    return
                end
                if not self.draw_obj then
                    ResMgr:Destroy(obj)
                    return
                end
                obj.transform.localPosition = u3dpool.vec3(0, 0, 0)
                obj.transform.localRotation = Quaternion.Euler(0, 0, 0)
        end)
    end
end

-- 温泉特效 移动时 水流散开特效
function Role:ReleaseHotSpringMoveEffect()
    if self.hot_spring_move_effect then
        self.hot_spring_move_effect:DeleteMe()
        self.hot_spring_move_effect = nil
    end
end

-- 温泉特效 站立时 特效
function Role:CreateHotSpringStandEffect()
    if nil == self.hot_spring_stand_effect and not self.is_in_boat then
        local role_foot_point = self.draw_obj:GetAttachPoint(AttachPoint.HurtRoot)
        local bundle, asset = ResPath.GetMiscEffect("effect_swimming_01")
        self.hot_spring_stand_effect = AllocAsyncLoader(self, "hot_spring_stand_effect")
        self.hot_spring_stand_effect:SetIsUseObjPool(true)
        self.hot_spring_stand_effect:SetParent(role_foot_point.transform)
        self.hot_spring_stand_effect:Load(bundle, asset,
            function(obj)
                if IsNil(obj) then
                    return
                end
                if not self.draw_obj then
                    ResMgr:Destroy(obj)
                    return
                end
                obj.transform.localPosition = u3dpool.vec3(0, 0, 0)
                obj.transform.localRotation = Quaternion.Euler(0, 0, 0)
            end)
    end
end

-- 温泉特效 站立特效
function Role:ReleaseHotSpringStandEffect()
    if self.hot_spring_stand_effect then
        self.hot_spring_stand_effect:DeleteMe()
        self.hot_spring_stand_effect = nil
    end
end

-- 温泉特效 特效释放
function Role:HotSpringEffectStop()
    self:ReleaseHotSpringStandEffect()
    self:ReleaseHotSpringMoveEffect()
end

-- 水波特效 更新
function Role:UpdateShuiboEffect()
    if not self:GetVisiable() or self.draw_obj == nil or self.is_fly_mount then
        return
    end

    if self:IsWaterRipple() and self:IsMainRole() and not self.is_jump and not self:IsQingGong() and not self:IsMitsurugi() then
        if self.shuibo_time then
            GlobalTimerQuest:CancelQuest(self.shuibo_time)
            self.shuibo_time = nil
        end
        if not self.shuibo_effect_is_show then
            if nil == self.shuibo_effect and nil ~= self.draw_obj then
                local role_foot_point = self.draw_obj:GetRoot()
                if role_foot_point and not IsNil(role_foot_point.gameObject) then
                    local bundle, asset = ResPath.GetEnvironmentMiscEffect("eff_shuibo")
                    self.shuibo_effect = AllocAsyncLoader(self, "eff_tongyong_shuibo")
                    self.shuibo_effect:SetIsUseObjPool(true)
                    self.shuibo_effect:SetParent(role_foot_point.gameObject.transform)
                    self.shuibo_effect:Load(bundle, asset,
                        function(obj)
                            if IsNil(obj) then
                                return
                            end
                            if not self.draw_obj then
                                ResMgr:Destroy(obj)
                                return
                            end
                            self.shuibo_effect_obj = obj
                            obj.transform.localPosition = u3dpool.vec3(0, 0.25, 0)
                        end)
                end
            -- elseif self.shuibo_effect_time <= Status.NowTime then
                -- if not IsNil(self.shuibo_effect_obj) then
                --     self.shuibo_effect_obj:GetComponent(typeof(UnityEngine.ParticleSystem)):Play()
                -- end
            end
        end
        self.shuibo_effect_is_show = true
        self.shuibo_effect_time = Status.NowTime + COMMON_CONSTS.SHUIBO_SHOW_DELAY_TIME
    else
        self:ShuiboEffectStop()
    end
end

-- 水波特效 停止
function Role:ShuiboEffectStop()
    if not self.shuibo_effect_is_show then return end

    if not IsNil(self.shuibo_effect_obj) then
        -- self.shuibo_effect_obj:GetComponent(typeof(UnityEngine.ParticleSystem)):Stop()
        self.shuibo_effect_is_show = false
        self.shuibo_effect_time = 0
        if self.shuibo_time then
            GlobalTimerQuest:CancelQuest(self.shuibo_time)
            self.shuibo_time = nil
        end
        
        self.shuibo_time = GlobalTimerQuest:AddDelayTimer(function ()
            self:ReleaseShuiboEffect()
        end, 1)
    end
end

-- 水波特效 释放
function Role:ReleaseShuiboEffect()
    if self.shuibo_effect then
        self.shuibo_effect:DeleteMe()
        self.shuibo_effect = nil
    end
end

-- 出生特效
function Role:PlayChuShengEffect()
    if self.chusheng_effect then
        return
    end
    self.chusheng_effect = true

    if self:IsMainRole() then
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
    end

    self:HideOrShowRole(false)

    local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_chusheng")
    if self.effect_chusheng_loader == nil then
        self.effect_chusheng_loader = AllocAsyncLoader(self, "effect_chusheng")
        self.effect_chusheng_loader:SetParent(self:GetRoot().transform or G_EffectLayer)
        self.effect_chusheng_loader:SetObjAliveTime(5)
        self.effect_chusheng_loader:SetIsUseObjPool(true)
    end
    self.effect_chusheng_loader:Load(bundle_name, asset_name)

    self.play_chusheng_effect = GlobalTimerQuest:AddDelayTimer(function()
        self.chusheng_effect = false
        self:HideOrShowRole(true)
        if self:IsMainRole() then
            GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
        end
        if self.effect_chusheng_loader then
            self.effect_chusheng_loader:Destroy()
            self.effect_chusheng_loader = nil
        end
    end, 1.5)
end

-- 打坐特效 - 播放
function Role:PlaySitEffect()
    self.sit_effect_is_show = true

    if nil == self.sit_effect_handle then
        self.sit_effect_handle = ReuseableHandleManager.Instance:GetShieldHandle(SitEffectHandle, self)
        self.sit_effect_handle:CreateShieldHandle()
    end

    self:UpdateSitEffect()
end

-- 打坐特效 - 停止
function Role:StopSitEffect()
    self:ReleaseSitEffect()
    self.sit_effect_is_show = false
    self:UpdateSitEffect()
end

-- 打坐特效 - 释放
function Role:ReleaseSitEffect()
    if self.sit_effect_handle then
        ReuseableHandleManager.Instance:ReleaseShieldHandle(self.sit_effect_handle)
        self.sit_effect_handle = nil
    end
end

-- 打坐特效 - 可见性
function Role:SetSitEffectVisible(visible)
    self.sit_effect_visible = visible
    self:UpdateSitEffect()
end

-- 打坐特效 - 更新
function Role:UpdateSitEffect()
    if self.role_effect_visible and self.sit_effect_is_show and self:GetVisiable() and self.sit_effect_visible and self.draw_obj then
        -- 先去掉一下打坐特效
        local bundle, asset = ResPath.GetEnvironmentBanyunEffect("effect_dazuo")
        if nil == self.sit_effect_loader then
            self.sit_effect_loader = AllocAsyncLoader(self, "sit_effect")
            self.sit_effect_loader:SetIsUseObjPool(true)
            self.sit_effect_loader:SetParent(self:GetRoot().transform or G_EffectLayer)
        end
        self.sit_effect_loader:Load(bundle, asset)
    else
        if self.sit_effect_loader then
            self.sit_effect_loader:Destroy()
        end
    end
end

-- 特效 下落时的光束
function Role:PlayFlyDownEffect(callback)
    if self.play_chuansongmen_02_effect then
        return
    end

    local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("chuansongmen_02")
    local async_loader = AllocAsyncLoader(self, "player_fly_down_effect")
    async_loader:SetParent(self:GetRoot().transform or G_EffectLayer)
    async_loader:SetObjAliveTime(5)
    async_loader:SetIsUseObjPool(true)
    async_loader:Load(bundle_name, asset_name, function(obj)
        self.play_chuansongmen_02_effect = true
        self.play_chuansong_effect = GlobalTimerQuest:AddDelayTimer(function()
                self.play_chuansongmen_02_effect = false
                if callback then
                    callback()
                end
                if self.follow_ui then
                    self.follow_ui:ForceSetVisible(true)
                end
        end, 1.13)
    end)
end

-- 跳跃特效
function Role:OnJump4Effect()
    if self.draw_obj then
        local trans = self.draw_obj:GetRoot().transform
        local bundle_name, asset_name = ResPath.GetEffect("effect_tioayue_001")
        EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, trans, nil, nil, nil, nil, function(obj)
            obj.transform:SetParent(SceneObjLayer.transform, false)
            obj.transform.position = trans.position + Vector3(0, -1.5, 0)
        end)
    end
end

--仙盟战 场景buff的特效展示
function Role:XMZClientShowEffect(client_effect_type)
    local obj_id = self.vo.obj_id
    local effect_list = {}
    if self:IsMainRole() then
        effect_list = FightWGData.Instance:GetMainRoleEffectList()
    else
        effect_list = FightWGData.Instance:GetOtherRoleEffectList(obj_id)
    end
    local effect_info = {}
    for k,v in pairs(effect_list) do
        if v.client_effect_type == client_effect_type then
            effect_info = v
        end
    end
    if not IsEmptyTable(effect_info) then
        local buff_config = FightWGData.Instance:GetBuffEffectCfg(effect_info.buff_type, effect_info.client_effect_type)
        if buff_config then
            local time = TimeWGCtrl.Instance:GetServerTime() + effect_info.remaining_time
            self:AddBuff(effect_info.buff_type,effect_info.client_effect_type, time)
        end
    end
end

-- Buff 变大
function Role:CheckBiggerBuff(buff_type)
    if self:IsDeleted() then
        return
    end

    local scale_value = nil
    if self:IsTianShenAppearance() then
        if self:IsBigger() then
		    local effect_list = nil
		    if self:IsMainRole() then
		        effect_list = FightWGData.Instance:GetMainRoleShowEffect()
		    else
		        effect_list = FightWGData.Instance:GetOtherRoleShowEffect(self:GetObjId())
		    end

		    if effect_list ~= nil then
		    	for i = 1, #effect_list do
		    		if effect_list[i].info ~= nil and effect_list[i].info.buff_type == BUFF_TYPE.EBT_TYPE_BIGGER then
		    			if effect_list[i].merge_layer == 0 then
		    				scale_value = 1.25
		    			else
		    				scale_value = 1 + effect_list[i].info.merge_layer * 0.25
		    			end

		    			break
		    		end
		    	end
		    end
        end
    end

    if self.draw_obj ~= nil then
        if scale_value ~= nil then
            self.draw_obj:SetScale(scale_value, scale_value, scale_value)
        else
            self.draw_obj:TryResetScale()
        end
    end
end

-- Buff 添加
function Role:AddBuff(buff_type, product_id, time, client_effect_type)
    Character.AddBuff(self, buff_type, product_id, client_effect_type)
    local key = buff_type
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
    if time and time > cur_time and nil == self.buff_list[key] then
        self.buff_list[key] = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.RemoveBeautyBuff, self, buff_type, product_id), time - cur_time)
    end
end

-- Buff 移出（为毛命名叫美人）
function Role:RemoveBeautyBuff(buff_type, product_id)
    local key = buff_type
    self:RemoveBuff(buff_type)
    if self.buff_list[key] then
        GlobalTimerQuest:CancelQuest(self.buff_list[key])
        self.buff_list[key] = nil
    end
end

-- Buff 检测
function Role:CheckBuffShow()
    if self:IsDeleted() or not self:GetVisiable() then
        return
    end

    self:RemoveAllBuff()
    local buff_list = nil
    if self:IsMainRole() then
        buff_list = FightWGData.Instance:GetMainRoleEffectList()
    else
        buff_list = FightWGData.Instance:GetOtherRoleEffectList(self:GetObjId())
    end

    if buff_list ~= nil and self.buff_type_list ~= nil then
        for k,v in pairs(buff_list) do
            self:AddBuff(v.buff_type, v.product_id, nil, v.client_effect_type)
        end
    end
end

--天神特效 光环特效
function Role:ShowTianShenUnionSkillEffect(is_show)
    if not is_show then
        if self.ts_union_skill_eff_loader then
            self.ts_union_skill_eff_loader:SetActive(false)
        end
        return
    end
    --[[
    local tianshen_cfg = TianShenWGData.Instance:GetImageModelByAppeId(self.vo.appearance_param)
    if tianshen_cfg and tianshen_cfg.ts_union_skills then
        local temp_idx_list = Split(tianshen_cfg.ts_union_skills or "", "|")
        local act_skill_list = {}
        for i, idx in ipairs(temp_idx_list) do
            if TianShenWGData.Instance:CheckTSUnionSkillActiveById(tonumber(idx)) then
                act_skill_list[#act_skill_list + 1] = tonumber(idx)
            end
        end
    end
    --]]
    --test
    local guanghuan_eff_name = {"Effects_fazhen01_hongse", "Effects_fazhen01_huang", "Effects_fazhen01_lan"}
    local bundle, asset = ResPath.GetEnvironmentEffect(guanghuan_eff_name[2])
    if not bundle or not asset then
        return
    end

    if not self.ts_union_skill_eff_loader then
        local obj = self.draw_obj:GetRoot()
        if obj ~= nil and not IsNil(obj.transform) then
            self.ts_union_skill_eff_loader = AllocAsyncLoader(self, "tianshen_skill_union_effect")
            self.ts_union_skill_eff_loader:SetIsUseObjPool(true)
            self.ts_union_skill_eff_loader:SetLoadPriority(ResLoadPriority.low)
            self.ts_union_skill_eff_loader:SetParent(obj.transform)
            self.ts_union_skill_eff_loader:Load(bundle, asset)
        end
    else
        self.ts_union_skill_eff_loader:Load(bundle, asset)
        self.ts_union_skill_eff_loader:SetActive(true)
    end
end

local MULTI_WUXING_TS_EFF = {
    [1] = {"effect_10033_buff_jin", "effect_10033_buff_jin01"},
    [2] = {"effect_10033_buff_mu", "effect_10033_buff_mu01"},
    [3] = {"effect_10033_buff_shui", "effect_10033_buff_shui01"},
    [4] = {"effect_10033_buff_huo", "effect_10033_buff_huo01"},
    [5] = {"effect_10033_buff_tu", "effect_10033_buff_tu01"},
}
--天神特效 检测
function Role:CheckTianShenSpecialEff(index)
    if not self:IsTianShenAppearance() then
        self:ClearTianShenSpecialEff()
        return
    end

    if self.cur_show_wuxing ~= nil and index ~= nil and self.cur_show_wuxing == index then
        return
    end

    local use_index = index or TianShenWGData.Instance:GetMultiSkillUseIndex()
    if use_index == nil then
        self:ClearTianShenSpecialEff()
        return
    end 

    local draw_obj = self:GetDrawObj()
    if draw_obj == nil then
        if self.special_ts_eff_loader ~= nil then
            self.special_ts_eff_loader:Destroy()
        end
        return
    end

    local point = draw_obj:GetRealAttachPoint(AttachPoint.UI)
    local point2 = draw_obj:GetRealAttachPoint(AttachPoint.BuffBottom)
    if point == nil or point2 == nil then
        if self.special_ts_eff_loader ~= nil then
            self.special_ts_eff_loader:Destroy()
        end
        return       
    end

    self.cur_show_wuxing = use_index
    if self.special_ts_eff_loader == nil then
        self.special_ts_eff_loader = AllocAsyncLoader(self, "special_ts_eff_loader")
        self.special_ts_eff_loader:SetIsUseObjPool(true)
        self.special_ts_eff_loader:SetLoadPriority(ResLoadPriority.low)
        self.special_ts_eff_loader:SetObjAliveTime(3)
    end

    local bundle = "effects2/prefab/mingjiang/10033_prefab"
    local asset = MULTI_WUXING_TS_EFF[use_index][1]
    self.special_ts_eff_loader:SetParent(point)
    self.special_ts_eff_loader:Load(bundle, asset)   

    if self.special_ts_show_loader == nil then
        self.special_ts_show_loader = AllocAsyncLoader(self, "special_ts_show_loader")
        self.special_ts_show_loader:SetIsUseObjPool(true)
        self.special_ts_show_loader:SetLoadPriority(ResLoadPriority.low)
        self.special_ts_show_loader:SetObjAliveTime(3)
    end

    asset = MULTI_WUXING_TS_EFF[use_index][2]
    self.special_ts_show_loader:SetParent(point2)
    self.special_ts_show_loader:Load(bundle, asset)    
end

-- 天神 清除
function Role:ClearTianShenSpecialEff()
    if self.special_ts_eff_loader ~= nil then
        self.special_ts_eff_loader:Destroy()
    end

    if self.special_ts_show_loader ~= nil then
        self.special_ts_show_loader:Destroy()
    end

    self.cur_show_wuxing = nil
end

local MULTI_WUXING_TS_MAIN_EFF = {
    [1] = "effect_10033_Bone042_t01",
    [2] = "effect_10033_Bone042_t02",
    [3] = "effect_10033_Bone042_t03",
    [4] = "effect_10033_Bone042_t04",
    [5] = "effect_10033_Bone042_t05",
}

local MULTI_WUXING_TS_WEAPON_EFF = {
    [1] = "100330201_Bip001 Prop1_jin",
    [2] = "100330201_Bip001 Prop1_mu",
    [3] = "100330201_Bip001 Prop1_shui",
    [4] = "100330201_Bip001 Prop1_huo",
    [5] = "100330201_Bip001 Prop1_tu",
}
--天神特效 特殊特效
function Role:CheckTianShenSpecialPartEff(index)
    if not self:IsTianShenAppearance() then
        self:ClearTianShenSpecialPartEff()
        return
    end

    local draw_obj = self:GetDrawObj()
    if draw_obj == nil then
        self:ClearTianShenSpecialPartEff()
        return
    end

    -- if obj == nil then
    --     local weapon_part = draw_obj:GetPart(SceneObjPart.Main)
    --     if weapon_part then
    --         obj = weapon_part:GetObj()
    --     end
    -- end

    -- if obj == nil or IsNil(obj.transform) then
    --     self:ClearTianShenSpecialWeaponEff()
    --     return
    -- end

    local point = draw_obj:GetRealAttachPoint(AttachPoint.Weapon2)
    local point2 = draw_obj:GetRealAttachPoint(AttachPoint.LeftHand)
    if point == nil or point2 == nil then
        self:ClearTianShenSpecialPartEff()
        return
    end

    local use_index = index or TianShenWGData.Instance:GetMultiSkillUseIndex()
    if use_index == nil then
        self:ClearTianShenSpecialPartEff()
        return
    end

    local bundle = ResPath.GetBianShenModel(10033)
    local bundle2 = ResPath.GetTianShenShenQiPath(100330201)
    local asset = MULTI_WUXING_TS_MAIN_EFF[use_index]

    if self.special_ts_main_eff_loader == nil then
        self.special_ts_main_eff_loader = AllocAsyncLoader(self, "special_ts_main_eff_loader")
        self.special_ts_main_eff_loader:SetIsUseObjPool(true)
        self.special_ts_main_eff_loader:SetLoadPriority(ResLoadPriority.low)
    end
    self.special_ts_main_eff_loader:SetParent(point)
    self.special_ts_main_eff_loader:Load(bundle, asset) 

    if self.special_ts_weapon_eff_loader == nil then
        self.special_ts_weapon_eff_loader = AllocAsyncLoader(self, "special_ts_weapon_eff_loader")
        self.special_ts_weapon_eff_loader:SetIsUseObjPool(true)
        self.special_ts_weapon_eff_loader:SetLoadPriority(ResLoadPriority.low)
    end
    local asset = MULTI_WUXING_TS_WEAPON_EFF[use_index]
    self.special_ts_weapon_eff_loader:SetParent(point2)
    self.special_ts_weapon_eff_loader:Load(bundle2, asset)  
end

function Role:ClearTianShenSpecialPartEff()
    if self.special_ts_main_eff_loader ~= nil then
        self.special_ts_main_eff_loader:Destroy()
    end

    if self.special_ts_weapon_eff_loader ~= nil then
        self.special_ts_weapon_eff_loader:Destroy()
    end
end



-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-------------------------------------    武器动画搞特殊     ------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
-----------------------------------------------------------------------------------------------------------------
--特殊需求
-- 武器自身动画
function Role:IsWeaponOwnAnim()
    if self:IsDeleted() then
        return false
    end

    return true
end

-- 移出武器动画
local function NeedRemoveWeapon(special_appearance)
    return special_appearance == SPECIAL_APPEARANCE_TYPE.CROSS_HOTSPRING_COMMON
        or special_appearance == SPECIAL_APPEARANCE_TYPE.XMZ
end

-- 更新武器模型
function Role:EquipDataChangeListen(callback)
    if not self.is_enter_scene then
        return
    end

    local vo = self.vo
    if not vo or vo.appearance == nil or vo.shenwu_appeid == nil then
        self:RemoveModel(SceneObjPart.Weapon)
        return
    end

    -- 心魔
    if vo.is_demons and vo.is_demons == EnumDemonsTpye.IsDemons then
        self:RemoveModel(SceneObjPart.Weapon)
        return
    end
	
	--任务变身状态下不加载武器
    if vo.task_appearn == CHANGE_MODE_TASK_TYPE.TALK_IMAGE and vo.task_appearn_param_1 ~= 0 and self.special_res_id ~= 0 then
        self:RemoveModel(SceneObjPart.Weapon)
        return
    end

    --修为变身不需要武器
    if self:IsXiuWeiBianShen() then
        self:RemoveModel(SceneObjPart.Weapon)
        return
    end

    local cgmanager_ins = CgManager.Instance
    local special_appearance = vo.special_appearance
    if cgmanager_ins and cgmanager_ins:IsCgIng() then
        special_appearance = 0
    end

    if NeedRemoveWeapon(special_appearance) then
        self:RemoveModel(SceneObjPart.Weapon)
        return
    end

    self.draw_obj:SetIsCanWeaponPointAttach(false)
    local weapon_res_id = 0
    local res_func = ResPath.GetWeaponModelRes
    local is_multi_wuxing = false

    if self:IsTianShenAppearance() then -- 天神变身 武器
        res_func = ResPath.GetTianShenShenQiPath
        local tianshen_cfg, is_huamo = TianShenWGData.Instance:GetImageModelByAppeId(self.vo.appearance_param, false)
        local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_cfg and tianshen_cfg.index)
        local cur_huanhua_id = TianShenWGData.Instance:GetWaiGuanHuanHua(shenqi_cfg and shenqi_cfg.index)
        if self.vo.tianshenshenqi_appeid == 0 then
            if cur_huanhua_id == -1 then
                weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or weapon_res_id
            else
                weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id1 or weapon_res_id
            end

            if tianshen_cfg ~= nil then
                is_multi_wuxing = tianshen_cfg.is_multi_wuxing == 1
            end

            weapon_res_id = tianshen_cfg and is_huamo and tianshen_cfg.ts_res_id or weapon_res_id
        elseif self.vo.tianshenshenqi_appeid == -1 then
            if tianshen_cfg ~= nil then
                is_multi_wuxing = tianshen_cfg.is_multi_wuxing == 1
            end

            weapon_res_id = shenqi_cfg and shenqi_cfg.ts_res_id or weapon_res_id
            weapon_res_id = tianshen_cfg and is_huamo and tianshen_cfg.ts_res_id or weapon_res_id
        else
            local waiguan_cfg = TianShenWGData.Instance:GetWaiGuanCfg(self.vo.tianshenshenqi_appeid)
            weapon_res_id = waiguan_cfg and waiguan_cfg.facade_res_id or weapon_res_id
        end
    -- 钓鱼
    elseif self.caiji_type == CaijiType.Fish then
        weapon_res_id = vo.sex == 0 and 950600101 or 950600102
    -- 永夜之巅
    elseif self:IsInEternalNightScene() then
        local role_uuid = self.vo.uuid
        local fashion_wuqi = EternalNightWGData.Instance:GetRoleEquipShiZhuang(role_uuid, "fashion_wuqi")
        weapon_res_id = RoleWGData.GetFashionWeaponId(vo.sex, vo.prof, fashion_wuqi)

    elseif 0 ~= vo.appearance.fashion_wuqi or 0 ~= vo.shenwu_appeid then
        weapon_res_id = RoleWGData.GetFashionWeaponId(vo.sex, vo.prof, 0 ~= vo.appearance.fashion_wuqi and vo.appearance.fashion_wuqi or vo.shenwu_appeid)
    else
        weapon_res_id = RoleWGData.GetJobWeaponId(vo.sex, vo.prof)
    end

    self.weapon_res_id = weapon_res_id
    if 0 == weapon_res_id then
        self:RemoveModel(SceneObjPart.Weapon)
        return
    end

    local bundle, name = res_func(weapon_res_id)
    if is_multi_wuxing then
        name = name .. "_scene"
    end

    self:ChangeModel(SceneObjPart.Weapon, bundle, name, callback)
end

function Role:IsInTianShenState()
    if self:IsDeleted() then
        return false
    end

    return SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == self.vo.special_appearance
end


------------------------------------------------ 增加怒气变身动作---------------------
-- 增加怒气变身动作
function Role:ExecuteAransformationAction(callback, is_not_play)
    local anim_name = SceneObjAnimator.transformation 
	local clip_name = SceneObjAnimator.transformation 
    local power_type = math.floor(self.vo.appearance_param / 1000)

    if is_not_play then
        self:ExecuteAransformationLingyu(power_type, callback, is_not_play)
    else
        local part = self.draw_obj:GetPart(SceneObjPart.Main)
        if not part or not part.obj then 
            return 
        end
    
        local anim = part:GetObj().animator
        if not anim then 
            return 
        end
    
        local clip = anim:GetAnimationClip(clip_name)
        if not clip then 
            return 
        end

        local action_end_time = clip.length or 0.1
        self:CrossAction(SceneObjPart.Main, anim_name)
        local bundle_name, asset_name = ResPath.GetAransformationEffect("bianshen", power_type)
        self:CreateAransformationEffect(bundle_name, asset_name, power_type)
        
        self:RemoveAransformationDelayTime()
        self.cancel_transformation_action = GlobalTimerQuest:AddDelayTimer(function ()
            self:RemoveAransformationDelayTime()
            self:ExecuteAransformationLingyu(power_type, callback, is_not_play)
        end, action_end_time)
    end   
end

-- 创建变身特效
function Role:CreateAransformationEffect(bundle, asset, index)
    local foot_trail_point = self.draw_obj:GetAttachPoint(AttachPoint.HurtRoot)
    local uuid = self.vo and self.vo.uuid or 0
    local load_str = string.format("aransformation_effect_%s", uuid)

    if foot_trail_point then
        if self.aransformation_effect_loader == nil then
            self.aransformation_effect_loader = AllocAsyncLoader(self, load_str)
            self.aransformation_effect_loader:SetIsUseObjPool(true)
            self.aransformation_effect_loader:SetIsInQueueLoad(true)
            self.aransformation_effect_loader:SetParent(foot_trail_point)
            self.aransformation_effect_loader:SetLocalPosition(Vec3Zero)
        end

        self.aransformation_effect_loader:Load(bundle, asset)
    end
end

-- 展示怒气领域表现
function Role:ExecuteAransformationLingyu(power_type, callback, is_not_play)
    local is_show_lingyu  = true
    local is_show_wangqi = true
	local logic = Scene.Instance:GetSceneLogic()
    if logic then
        if logic and logic.IsShowAransformationLingyu then
            is_show_lingyu = logic:IsShowAransformationLingyu()
        end

        if logic and logic.IsShowAransformationWangQi then
            is_show_wangqi = logic:IsShowAransformationWangQi()
        end
    end

    local delay_fun = function(game_obj)
        local bundle_name, asset_name = ResPath.GetAransformationEffect("qiliu", power_type)
        self:CreateAransformationEffect(bundle_name, asset_name, power_type)

        if game_obj ~= nil then
            game_obj:SetActive(false)
            Scene.Instance:SetScreenEffectZoneEffect(game_obj.transform)
        end

        if is_show_wangqi then
            Scene.Instance:PlayScreenEffectZone(self.draw_obj.root_transform, 0.5, 0.5)
        end

        if self:IsMainRole() then
            local x, y, z = self:GetLookAtPointPos()
            self.draw_obj:GetLookAtPoint(x, y, z, true)
        end

        if (not is_not_play) and self:IsStand() then
            self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle)
        end

        if callback then
            callback()
        end
    end

    local scene_type = Scene.Instance:GetSceneType()
	if not is_show_lingyu then   -- 不需要场景特效
        delay_fun()
    else
        local bundle_name, asset_name = ResPath.GetEnvironmentMiscEffect("eff_lingyu0")
        self:CreateAransformationZoneEffect(bundle_name, asset_name, power_type, function()
            local game_obj = self.aransformation_zone_effect_loader:GetGameObj()
            if IsNil(game_obj) then
                return
            end

            delay_fun(game_obj)
        end)
	end
end

-- 创建变身领域特效
function Role:CreateAransformationZoneEffect(bundle, asset, index, callback)
    local uuid = self.vo and self.vo.uuid or 0
    local load_str = string.format("aransformation_zone_effect_%s", uuid)

    if SceneObjLayer then
        if self.aransformation_zone_effect_loader == nil then
            self.aransformation_zone_effect_loader = AllocAsyncLoader(self, load_str)
            self.aransformation_zone_effect_loader:SetIsUseObjPool(true)
            self.aransformation_zone_effect_loader:SetIsInQueueLoad(true)
            self.aransformation_zone_effect_loader:SetParent(SceneObjLayer.transform)
            self.aransformation_zone_effect_loader:SetLocalPosition(self.draw_obj.root_transform.localPosition)
        end

        self.aransformation_zone_effect_loader:Load(bundle, asset, callback)
    end
end

-- 移除变身特效
function Role:RemoveAransformationEffect()
	if self.aransformation_effect_loader then
        self.aransformation_effect_loader:Destroy()
		self.aransformation_effect_loader = nil
	end

    if self.aransformation_zone_effect_loader then
        self.aransformation_zone_effect_loader:Destroy()
		self.aransformation_zone_effect_loader = nil
	end

    if self:IsMainRole() then
        Scene.Instance:StopScreenEffectZone()
        local x, y, z = self:GetLookAtPointPos()
        self.draw_obj:GetLookAtPoint(x, y, z, true)
    end
end

-- 移除定时器
function Role:RemoveAransformationDelayTime()
    if self.cancel_transformation_action then
        GlobalTimerQuest:CancelQuest(self.cancel_transformation_action)
        self.cancel_transformation_action = nil
    end
end
------------------------------------------------ 增加怒气变身动作End---------------------

---------------------------------------新版足迹_START-----------------------------------------
function Role:UpdateFootTrail()
    if self:IsQingGong() or self:IsMitsurugi() or self:IsJump() or self:IsWaterWay() or self:IsNeedCreadFoot() or self.hide_foot or not self:GetVisiable() or self:IsInvisible() then
        self:SetFootTrailVisiable(false)
        return
    end
    
    local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if scene_cfg.pb_zuji and 1 == scene_cfg.pb_zuji then
        self:SetFootTrailVisiable(false)
        return
    end

    local foot_id = self.vo.appearance.fashion_foot

    if foot_id == 0 then
        self:SetFootTrailVisiable(false)
        return
    end

    if nil == self.foot_trail_effect_loader then
        self:CreateFootTrail()
    elseif nil ~= self.foot_trail_eff and (nil == self.foot_trail_res_cache or self.foot_trail_res_cache ~= foot_id ) then
        local bundle, asset = ResPath.GetFootEffect(foot_id)

        if bundle == nil or asset == nil then
            print_error("缺少新足迹资源, 足迹res_id = ", foot_id, bundle, asset)
            self:SetFootTrailVisiable(false)
            return
        end

        self.foot_trail_res_cache = foot_id
        self.foot_trail_eff.BundleName = bundle
        self.foot_trail_eff.AssetName = asset
        self:SetFootTrailVisiable(false)
        self:SetFootTrailVisiable(true)
        return
    end

    self:SetFootTrailVisiable(true)
end

function Role:SetFootTrailVisiable(visiable)
    if self.foot_trail_effect_loader then
        self.foot_trail_effect_loader:SetActive(visiable)
    end
end

function Role:CreateFootTrail()
    -- 增加足迹拖尾特效
    local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("eff_role_foot")
    local foot_trail_point = self.draw_obj:GetAttachPoint(AttachPoint.Root)

    if foot_trail_point then
        local load_call_back = function()
            local parent_root = self.foot_trail_effect_loader:GetGameObj()

            if nil ~= parent_root then
                if not self.foot_trail_eff then
                    local eff_root = parent_root.transform:Find("foot")
        
                    if eff_root then
                        self.foot_trail_eff = eff_root:GetComponent(typeof(Game.GameObjectAttach))
                    end
                end
            end
        end

        self.foot_trail_effect_loader = AllocAsyncLoader(self, "foot_trail_effect")
        self.foot_trail_effect_loader:SetIsUseObjPool(true)
        self.foot_trail_effect_loader:SetIsInQueueLoad(true)
        self.foot_trail_effect_loader:SetParent(foot_trail_point)
        self.foot_trail_effect_loader:Load(bundle_name, asset_name, load_call_back)
    end
end

function Role:DestroyFootTrail()
	if self.foot_trail_effect_loader then
        self.foot_trail_effect_loader:Destroy()
		self.foot_trail_effect_loader = nil
	end

    self.foot_trail_eff = nil
end
---------------------------------------新版足迹_END-------------------------------------------

------------------------------------------BOSS入侵特权护盾_START------------------------------------------
function Role:SetBossInvasionHuDunVisiable(visiable)
    if self.bsrq_tqhd_effect_loader then
        self.bsrq_tqhd_effect_loader:SetActive(visiable)
    else
        if visiable then
            self:CreateBossInvasionHuDun()
        end
    end
end

function Role:CreateBossInvasionHuDun()
    local bundle_name, asset_name = ResPath.GetEnvironmentCommonEffect("boss_invasion_hudun")
    local bsrq_hudun_point = self.draw_obj:GetAttachPoint(AttachPoint.Root)

    if bsrq_hudun_point then
        local load_call_back = function()
            -- local parent_root = self.bsrq_tqhd_effect_loader:GetGameObj()
            -- if nil ~= parent_root then
            --     local eff_root = parent_root.transform:Find("hudun")
        
            --     if eff_root then
            --         eff_root.transform.localPosition = Vector3(0.4, -1.4, 0)
            --     end
            -- end
        end

        self.bsrq_tqhd_effect_loader = AllocAsyncLoader(self, "boss_invasion_hudun")
        self.bsrq_tqhd_effect_loader:SetIsUseObjPool(true)
        self.bsrq_tqhd_effect_loader:SetIsInQueueLoad(true)
        self.bsrq_tqhd_effect_loader:SetParent(bsrq_hudun_point)
        self.bsrq_tqhd_effect_loader:Load(bundle_name, asset_name, load_call_back)
    end
end

function Role:DestroyBossInvasionHuDun()
	if self.bsrq_tqhd_effect_loader then
        self.bsrq_tqhd_effect_loader:Destroy()
		self.bsrq_tqhd_effect_loader = nil
	end
end
--------------------------------------------BOSS入侵特权护盾_END------------------------------------------

-- 双修技能触发特效
function Role:SetAtrifactTriggerEffectLoader(effect_name)
    local bundle, asset = ResPath.GetEnvironmentMiscEffect(effect_name)
    if not self.artifact_trigger_eff_loader then
        local obj = self.draw_obj:GetRoot()
        if obj ~= nil and not IsNil(obj.transform) then
            self.artifact_trigger_eff_loader = AllocAsyncLoader(self, "artifact_trigger_effect")
            self.artifact_trigger_eff_loader:SetIsUseObjPool(true)
            self.artifact_trigger_eff_loader:SetLoadPriority(ResLoadPriority.low)
            self.artifact_trigger_eff_loader:SetParent(obj.transform)
            self.artifact_trigger_eff_loader:SetObjAliveTime(4)
            self.artifact_trigger_eff_loader:Load(bundle, asset)
        end
    else
        self.artifact_trigger_eff_loader:SetActive(false)
        self.artifact_trigger_eff_loader:Load(bundle, asset)
        self.artifact_trigger_eff_loader:SetActive(true)
    end
end

---------------------------------------------------------------------------------------------------
--------------------------------------------双人坐骑_start------------------------------------------
---------------------------------------------------------------------------------------------------
function Role:IsMultiMountState()
    return self.multi_mount_state ~= MULTI_MOUNT_STATE.NONE
end

-- 刷新双人坐骑状态
function Role:UpdateMultiMount()
    local multi_mount_data, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(self.vo.obj_id)

    if self.multi_mount_state ~= multi_mount_state then
        self.multi_mount_state = multi_mount_state

        if self:IsMainRole() then
            MainuiWGCtrl.Instance:FlushXunLuStates()
        end

        if not IsEmptyTable(multi_mount_data) and self.multi_mount_state ~= MULTI_MOUNT_STATE.NONE then
            self:EnterMultiMountState(multi_mount_data)
        else
            self:OutMultiMountState()
        end
    end
end

-- 进入双人坐骑状态   默认位置mount_point 多人位置 mount_point1，mount_point2，mount_point3，mount_point4
function Role:EnterMultiMountState(multi_mount_data)
    local main_role = Scene.Instance:GetMainRole()
    local pos_id = 0  

    local ride_item_list = multi_mount_data.ride_item_list
    if not IsEmptyTable(ride_item_list) then
        for k, v in pairs(ride_item_list) do
            if v.obj_id == self.vo.onj_id then
                pos_id = v.pos
                break
            end
        end
    end

    local target_obj = Scene.Instance:GetObjectByObjId(multi_mount_data.main_obj_id)
    if not target_obj or target_obj:IsDeleted() then
        return
    end

    -- 双人坐骑需要屏蔽的外观
    self:ForceChengePartVisibleOnSpecialStatus("multi_mount", false)
    self:DeleteTaskCallInfo()

    if MultiMountWGData.Instance:IsMultiMountDrive(self.vo.obj_id) then
        self:UpdateMultiMountSpecialApperaence()
    else
        self:RemoveModel(SceneObjPart.Wing)
    end

    if self.vo.obj_id ~= multi_mount_data.main_obj_id then
        if self:IsRiding() then
            self:RemoveModel(SceneObjPart.Mount)
        end

        local mount_obj = target_obj.draw_obj:_TryGetPartObj(SceneObjPart.Mount)
        if mount_obj then
            self:AddMountToPoint(pos_id, target_obj)
        else
            self.other_role_mountupend_callback = function()
                self:AddMountToPoint(pos_id, target_obj)
            end
    
            target_obj:AddMountUpEndCallBack(self.other_role_mountupend_callback)
        end
    end
end

-- JXW 要求
-- 双人坐骑 一个人时候需要显示翅膀  两人都不显示翅膀
-- 多人坐骑 驾驶者需要显示翅膀 其他人都不显示翅膀 
function Role:UpdateMultiMountSpecialApperaence()
    if self:IsMultiMountState() then
        local multi_mount_data, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(self.vo.obj_id)

        if multi_mount_state == MULTI_MOUNT_STATE.DRIZVE then
            local multi_mount_cfg = MultiMountWGData.Instance:GetTotalMountCfgBySeq(multi_mount_data.seq)

            if not IsEmptyTable(multi_mount_cfg) then
                local role_num = MultiMountWGData.Instance:GetMyMultiMountTotalRoleNum(self.vo.obj_id)

                if multi_mount_cfg.ride_num <= 2 then
                    if role_num <= 1 then
                        self:UpdateWing()
                    else
                        self:RemoveModel(SceneObjPart.Wing)
                    end
                else
                    self:UpdateWing()
                end
            end
        end
    end
end

-- 加入坐骑点同步
function Role:AddMountToPoint(pos, target_obj)
    if self.other_role_mountupend_callback then
        target_obj:RemoveMountUpEndCallBack(self.other_role_mountupend_callback)
        self.other_role_mountupend_callback = nil
    end

    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    local main_obj = main_part:GetObj()
    main_obj.transform:SetParent(target_obj:GetRoot().gameObject.transform)
    main_obj.transform.localEulerAngles = Vector3(0, 0, 0)

    local attachment = self.draw_obj:_TryGetPartAttachment(SceneObjPart.Main)
	if not attachment then
		return
	end

	local mount_obj = target_obj.draw_obj:_TryGetPartObj(SceneObjPart.Mount)
	if mount_obj ~= nil then
		attachment:AddMount(mount_obj.gameObject, "mount_point" .. (pos + 1))

        if self:IsMainRole() and not MultiMountWGData.Instance:IsMultiMountDrive(self.vo.obj_id) then
            self:SetMultiMountMainCameraFollowTarget(target_obj)
        end
	end

    -- 组合动画刷新
    local multi_mount_data, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(self.vo.obj_id)
    if not IsEmptyTable(multi_mount_data) then
        local owner_obj = Scene.Instance:GetObjectByObjId(multi_mount_data.main_obj_id)

        if owner_obj and owner_obj:IsRole() then
            local ride_item_list = multi_mount_data.ride_item_list

            if not IsEmptyTable(ride_item_list) then
                for k, v in pairs(ride_item_list) do
                    local scene_obj = Scene.Instance:GetObj(v.obj_id)

                    if scene_obj and scene_obj:IsRole() then
                        scene_obj:UpdateMultiMountJointAction(owner_obj:IsMove())
                    end
                end
            end
        end
    end
end

-- 监听坐骑创建事件
function Role:AddMountUpEndCallBack(callback)
    if callback == nil then
		return
	end

    if self.mount_up_end_call_back then
        if not IsEmptyTable(self.mount_up_end_call_back) then
            for k,v in pairs(self.mount_up_end_call_back) do
                if v == callback then
                    return
                end
            end
        end
    else
        self.mount_up_end_call_back = {}
    end

    table.insert(self.mount_up_end_call_back, callback)
end

-- 移除坐骑创建事件
function Role:RemoveMountUpEndCallBack(callback)
    if self.mount_up_end_call_back then
        for i,v in ipairs(self.mount_up_end_call_back) do
            if v == callback then
                table.remove(self.mount_up_end_call_back, i)
                break
            end
        end
    end
end

-- 执行坐骑监听事件
function Role:InvokeMountUpEndCallBack()
    if not IsEmptyTable(self.mount_up_end_call_back) then
        for k,v in pairs(self.mount_up_end_call_back) do
            v()
        end

        self.mount_up_end_call_back = nil
    end
end

-- 乘坐他人坐骑 在双人坐骑主人物身上生成一个摄像机绑定点
function Role:SetMultiMountMainCameraFollowTarget(target_obj)
    local x, y, z = 0, 2.4, 0
    local vo = target_obj:GetVo()
    if vo and vo.sex == GameEnum.MALE then
        y = 2.4
    else
        y = 2.2
    end

    local point = target_obj.draw_obj:GetAttachPoint(AttachPoint.Head)
    local draw_obj_trans = target_obj.draw_obj:GetTransfrom()

    if not IsNil(draw_obj_trans) then
        local local_pos = draw_obj_trans:InverseTransformPoint(point.transform.position)
        x = local_pos.x
        y = local_pos.y
        z = local_pos.z
    end

    local point_trans = target_obj.draw_obj:GetLookAtPoint(x, y, z, true)

    MainCameraFollow.Target = point_trans
    SceneOptimizeMgr.SetCenterPoint(target_obj:GetRoot().transform)

    local main_role = Scene.Instance:GetMainRole()
    main_role:CancelCameraUpdateTimer()
end

function Role:UpdateMultiMountMainCameraPos(target_obj)
    local x, y, z = 0, 2.4, 0
    local vo = target_obj:GetVo()
    if vo and vo.sex == GameEnum.MALE then
        y = 2.4
    else
        y = 2.2
    end

    local point = target_obj.draw_obj:GetAttachPoint(AttachPoint.Head)
    local draw_obj_trans = target_obj.draw_obj:GetTransfrom()

    if not IsNil(draw_obj_trans) then
        local local_pos = draw_obj_trans:InverseTransformPoint(point.transform.position)
        x = local_pos.x
        y = local_pos.y
        z = local_pos.z
    end

    local point_trans = target_obj.draw_obj:GetLookAtPoint(x, y, z, true)
    SceneOptimizeMgr.SetCenterPoint(target_obj:GetRoot().transform)

    local main_role = Scene.Instance:GetMainRole()
    main_role:CancelCameraUpdateTimer()
end

-- 退出双人坐骑状态
function Role:OutMultiMountState()
    local main_part = self.draw_obj:_TryGetPartObj(SceneObjPart.Main)
    if main_part then
        -- self:RemoveModel(SceneObjPart.Main)
        main_part.transform:SetParent(self.draw_obj:GetRoot().transform)

        local attachment = self.draw_obj:_TryGetPartAttachment(SceneObjPart.Main)

        if attachment ~= nil then
            attachment:RemoveMount()
        end
    end

    self:ForceChengePartVisibleOnSpecialStatus("multi_mount", true)
    self:UpdateWing()

    if self:IsMainRole() then
        local main_role = Scene.Instance:GetMainRole()
        main_role:UpdateCameraFollowTarget(true)
    end
end

-- 下坐骑组员更新同步动作
function Role:OutMultiMountUpdateJointAction(multi_mount_info)
    if multi_mount_info.main_obj_id == multi_mount_info.obj_id then
        return
    end

    local multi_mount_data, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(multi_mount_info.main_obj_id)
    if not IsEmptyTable(multi_mount_data) then
        local owner_obj = Scene.Instance:GetObjectByObjId(multi_mount_data.main_obj_id)

        if owner_obj and owner_obj:IsRole() then
            local ride_item_list = multi_mount_data.ride_item_list

            if not IsEmptyTable(ride_item_list) then
                for k, v in pairs(ride_item_list) do
                    local scene_obj = Scene.Instance:GetObj(v.obj_id)
                    
                    if scene_obj and scene_obj:IsRole() then
                        scene_obj:UpdateMultiMountJointAction(owner_obj:IsMove())
                    end
                end
            end
        end
    end
end

function Role:UpdateMultiMountJointAction(is_move)
    local main_part = self.draw_obj:_TryGetPartObj(SceneObjPart.Main)

    if main_part then
        local action = is_move and self:SetRidingActionRunParam() or self:SetRidingActionIdelParam()
        self:CrossAction(SceneObjPart.Main, action)
        self:UpdateMultiMountMainCameraFollowTarget()
    end
end

-- 动画刷新后，驾驶者的摄像机观察点坐标会变化需要修复
function Role:UpdateMultiMountMainCameraFollowTarget()
    if not self:IsMainRole() and MultiMountWGData.Instance:IsMultiMountDrive(self.vo.obj_id) then
        self:DestroyMultiMountCameraSetTimer()

        self.multi_mount_look_at_point_fixed_timer = GlobalTimerQuest:AddDelayTimer(function()
            if not self:IsDeleted() then
                self:UpdateMultiMountMainCameraPos(self)
            end
        end, 0.2)
    end
end

function Role:DestroyMultiMountCameraSetTimer()
	if self.multi_mount_look_at_point_fixed_timer then
		GlobalTimerQuest:CancelQuest(self.multi_mount_look_at_point_fixed_timer)
		self.multi_mount_look_at_point_fixed_timer = nil
	end
end

-- 同步坐骑上玩家动画
function Role:MultiMountEnterStateStand()
    if self:IsMultiMountState() then
        local multi_mount_data, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(self.vo.obj_id)

        if multi_mount_state == MULTI_MOUNT_STATE.DRIZVE and not IsEmptyTable(multi_mount_data) then
            local ride_item_list = multi_mount_data.ride_item_list

            if not IsEmptyTable(ride_item_list) then
                for k, v in pairs(ride_item_list) do
                    if v.obj_id ~= self.vo.obj_id then
                        local scene_obj = Scene.Instance:GetObj(v.obj_id)
                        
                        if scene_obj and scene_obj:IsRole() then
                            scene_obj:EnterStateStand()
                        end
                    end
                end
            end
        end
    end
end

function Role:MultiMountEnterStateMove()
    if self:IsMultiMountState() then 
        local multi_mount_data, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(self.vo.obj_id)

        if multi_mount_state == MULTI_MOUNT_STATE.DRIZVE and not IsEmptyTable(multi_mount_data) then
            local ride_item_list = multi_mount_data.ride_item_list

            if not IsEmptyTable(ride_item_list) then
                for k, v in pairs(ride_item_list) do
                    if v.obj_id ~= self.vo.obj_id then
                        local scene_obj = Scene.Instance:GetObj(v.obj_id)
                        
                        if scene_obj and scene_obj:IsRole() then
                            scene_obj:EnterStateMove()
                        end
                    end
                end
            end
        end
    end
end

function Role:MultiMountUpdateStateMove(elapse_time)
    if self:IsMultiMountState() then 
        local multi_mount_data, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(self.vo.obj_id)

        if multi_mount_state == MULTI_MOUNT_STATE.DRIZVE and not IsEmptyTable(multi_mount_data) then
            local ride_item_list = multi_mount_data.ride_item_list

            if not IsEmptyTable(ride_item_list) then
                for k, v in pairs(ride_item_list) do
                    if v.obj_id ~= self.vo.obj_id then
                        local scene_obj = Scene.Instance:GetObj(v.obj_id)
                        
                        if scene_obj and scene_obj:IsRole() then
                            scene_obj:UpdateStateMove(elapse_time)
                        end
                    end
                end
            end
        end
    end
end

function Role:GetMultiMountRoleRidingAction()
    local action = -1

    local multi_mount_info, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(self.vo.obj_id)
    if not IsEmptyTable(multi_mount_info) and multi_mount_info.app_image_id and multi_mount_info.app_image_id > 0 then
        local ride_item_list = multi_mount_info.ride_item_list

        if not IsEmptyTable(multi_mount_info.ride_item_list) then
            local pos_index = -1
            local role_act_data = {}

            for k, v in pairs(multi_mount_info.ride_item_list) do
                if self.vo.obj_id == v.obj_id then
                   pos_index = v.pos
                end

                local scene_obj = Scene.Instance:GetObj(v.obj_id)
                if scene_obj and scene_obj:IsRole() and scene_obj:IsMultiMountState() then
                    role_act_data[v.pos + 1] = true
                else
                    role_act_data[v.pos + 1] = false
                end
            end

            pos_index = pos_index + 1

            if pos_index >= 1 then
                -- 先处理联合
                local action_cfg = NewAppearanceWGData.Instance:GetMultiMountJointAction(multi_mount_info.app_image_id)

                if not IsEmptyTable(action_cfg) then
                    for k, v in pairs(action_cfg) do
                        local pos_all_act = true
                        local target_action = -1

                        if not IsEmptyTable(v) then    
                            for i, u in pairs(v) do
                                if u.pos == pos_index then
                                    target_action = u.action
                                end

                                if not role_act_data[u.pos] then
                                    pos_all_act = false
                                end
                            end
                        end

                        if pos_all_act and target_action > 0 then
                           return target_action 
                        end
                    end
                end

                -- 默认
                local default_action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(multi_mount_info.app_image_id)
                local default_action = default_action_cfg and default_action_cfg["action" .. pos_index] or -1

                if default_action > 0 then
                    action = default_action
                end
            end
        end
    end

    return action
end

---------------------------------------------------------------------------------------------------
---------------------------------------------双人坐骑_end-------------------------------------------
---------------------------------------------------------------------------------------------------