ActXianQiJieFengView = ActXianQiJieFengView or BaseClass(SafeBaseView)

function ActXianQiJieFengView:__init()
	self:SetMaskBg(true, false)
	self.default_index = 10

	self.view_name = GuideModuleName.ActXianQiJieFengView

	local assetbundle = "uis/view/act_xianqi_jiefeng_ui_prefab"
	self:AddViewResource(0, assetbundle, "layout_quanmin_beizhan_panel")
	self:AddViewResource(TabIndex.xianqi_jiefeng_login, assetbundle, "layout_login")

	self:AddViewResource(TabIndex.xianqi_jiefeng_shouchong, assetbundle, "layout_first_recharge")
	self:AddViewResource(TabIndex.xianqi_jiefeng_leichong, assetbundle, "layout_total_recharge")

	self:AddViewResource(TabIndex.xianqi_jiefeng_duobei, assetbundle, "layout_duobei")
	self:AddViewResource(TabIndex.xianqi_jiefeng_longhun, assetbundle, "layout_longhun")
	self:AddViewResource(TabIndex.xianqi_jiefeng_laixi, assetbundle, "layout_xingtian_laixi")
	self:AddViewResource(TabIndex.xianqi_jiefeng_juanxian, assetbundle, "layout_juanxian")

	self:AddViewResource(TabIndex.xianqi_jiefeng_haoli, assetbundle, "layout_haoli")
	self:AddViewResource(TabIndex.xianqi_jiefeng_haoli2, assetbundle, "layout_haoli")
	self:AddViewResource(TabIndex.xianqi_jiefeng_haoli3, assetbundle, "layout_haoli")
	self:AddViewResource(TabIndex.xianqi_jiefeng_haoli4, assetbundle, "layout_haoli")

	--self:AddViewResource(TabIndex.xianqi_jiefeng_cap, assetbundle, "layout_cap_bipin_common2")
	--self:AddViewResource(TabIndex.xianqi_jiefeng_cap2, assetbundle, "layout_cap_bipin_common2")
	--self:AddViewResource(TabIndex.xianqi_jiefeng_cap3, assetbundle, "layout_cap_bipin_common2")
	-- self:AddViewResource(TabIndex.xianqi_jiefeng_cap, assetbundle, "layout_cap_bipin_common")
	-- self:AddViewResource(TabIndex.xianqi_jiefeng_cap2, assetbundle, "layout_cap_bipin_common")
	-- self:AddViewResource(TabIndex.xianqi_jiefeng_cap3, assetbundle, "layout_cap_bipin_common")

	-- self:AddViewResource(TabIndex.xianqi_jiefeng_turntable, assetbundle, "layout_bz_turntable")

	-- self:AddViewResource(TabIndex.xianqi_jiefeng_cap, assetbundle, "layout_cap_bipin_role")
	-- self:AddViewResource(TabIndex.xianqi_jiefeng_cap2, assetbundle, "layout_cap_bipin_server")
	-- self:AddViewResource(TabIndex.xianqi_jiefeng_cap3, assetbundle, "layout_cap_bipin_kajia")
	self:AddViewResource(TabIndex.xianqi_jiefeng_haoli, assetbundle, "haoli_tab_grid")
	self:AddViewResource(TabIndex.xianqi_jiefeng_haoli2, assetbundle, "haoli_tab_grid")
	self:AddViewResource(TabIndex.xianqi_jiefeng_haoli3, assetbundle, "haoli_tab_grid")
	self:AddViewResource(TabIndex.xianqi_jiefeng_haoli4, assetbundle, "haoli_tab_grid")

	--龙魂冲榜
	self:AddViewResource(TabIndex.xianqi_jiefeng_longhun_rank, assetbundle, "layout_longhun_rank")

	self:AddViewResource(0, assetbundle, "HorizontalTabbar")
	self:AddViewResource(0, assetbundle, "VerticalTabbar")
end

function ActXianQiJieFengView:ReleaseCallBack()
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:ReleaseLoginView()
	self:ReleaseShouChongView()
	self:ReleaseRechargeView()
	self:ReleaseDBView()
	self:ReleaseXingTianLaiXiView()
	self:ReleaseLongHunView()
	self:ReleaseJuanXianView()
	self:ReleaseHaoLiView()
	self:ReleaseCapView()
	-- self:ReleaseTurnTableView()
	self:ReleaseLongHunRankView()

	if self.tab_count_down and CountDownManager.Instance:HasCountDown(self.tab_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.tab_count_down)
	end
end

function ActXianQiJieFengView:LoadCallBack()
	if not self.tabbar then
		self:SetTabIndex()
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabSate, self))
		self.tabbar:Init(self.tab_name_list, self.sub_tab_name_list, "uis/view/act_xianqi_jiefeng_ui_prefab",
			"uis/view/act_xianqi_jiefeng_ui_prefab", self.remind_name_list)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
	end
end

function ActXianQiJieFengView:SetLoadTabIndex()
	self:SetTabIndex()
	self.tabbar:SetHorTabData(self.sub_tab_name_list)
	self:FlusnHaoLiBtns()
	self.BZHLdelay_timer = nil
end

function ActXianQiJieFengView:LoadIndexCallBack(index)
	if index == TabIndex.xianqi_jiefeng_login then
		self:InitLoginRewarView()
	elseif index == TabIndex.xianqi_jiefeng_shouchong then
		self:InitShouChongView()
	elseif index == TabIndex.xianqi_jiefeng_leichong then
		self:InitLeiChongView()
	elseif index == TabIndex.xianqi_jiefeng_duobei then
		self:InitDBView()
	elseif index == TabIndex.xianqi_jiefeng_longhun then
		self:InitLongHunView()
	elseif index == TabIndex.xianqi_jiefeng_juanxian then
		self:InitJuanXianView()
		-- elseif index == TabIndex.xianqi_jiefeng_cap
		-- 	or index == TabIndex.xianqi_jiefeng_cap2
		-- 	or index == TabIndex.xianqi_jiefeng_cap3 then
		-- 	self:InitCapView(index)
	elseif index == TabIndex.xianqi_jiefeng_haoli
		or index == TabIndex.xianqi_jiefeng_haoli2
		or index == TabIndex.xianqi_jiefeng_haoli3
		or index == TabIndex.xianqi_jiefeng_haoli4 then
		self:InitHaoLiView()
		-- elseif index == TabIndex.xianqi_jiefeng_turntable then
		-- 	self:LoadTurnTableView()
	elseif index == TabIndex.xianqi_jiefeng_laixi then
		self:InitXingTianLaiXiView()
	elseif index == TabIndex.xianqi_jiefeng_longhun_rank then
		--龙魂冲榜
		self:InitLongHunRankView()
	end
end

function ActXianQiJieFengView:ShowIndexCallBack(index)
	if index == TabIndex.xianqi_jiefeng_juanxian then
		self:JXGotoLastProgress()
		-- elseif index == TabIndex.xianqi_jiefeng_turntable then
		-- 	self:ShowIndexTurnTable()
	elseif index == TabIndex.xianqi_jiefeng_longhun then
		self:ShenLongHunIndexCallBack()
	elseif index == TabIndex.xianqi_jiefeng_login then
		self:LoginShowIndexCallBack()
		-- elseif index == TabIndex.xianqi_jiefeng_cap
		-- 	or index == TabIndex.xianqi_jiefeng_cap2
		-- 	or index == TabIndex.xianqi_jiefeng_cap3 then
		-- 	self.node_list.HorizontalTabbarContent.rect.anchoredPosition = Vector2(0, 0)
	elseif index == TabIndex.xianqi_jiefeng_haoli
		or index == TabIndex.xianqi_jiefeng_haoli2
		or index == TabIndex.xianqi_jiefeng_haoli3
		or index == TabIndex.xianqi_jiefeng_haoli4 then
		self.node_list.HorizontalTabbarContent.rect.anchoredPosition = Vector2(324, 0)
	elseif index == TabIndex.xianqi_jiefeng_shouchong then
		self:ShowShouChongView()
	elseif index == TabIndex.xianqi_jiefeng_leichong then
		self:ShowLeiChongView()
	elseif index == TabIndex.xianqi_jiefeng_laixi then
		ActXianQiJieFengWGCtrl.Instance:CSRandActXTLX2InfoReq()
	elseif index == TabIndex.xianqi_jiefeng_longhun_rank then
		--龙魂冲榜
		self:ShenLongHunRankIndexCallBack()
	end
end

function ActXianQiJieFengView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.xianqi_jiefeng_shouchong then
				self:FlushShouChongView()
			elseif index == TabIndex.xianqi_jiefeng_leichong then
				self:FlushLeiChongView()
			elseif index == TabIndex.xianqi_jiefeng_haoli
				or index == TabIndex.xianqi_jiefeng_haoli2
				or index == TabIndex.xianqi_jiefeng_haoli3
				or index == TabIndex.xianqi_jiefeng_haoli4 then
				self:FlushHaoLiView(index % 10)
			elseif index == TabIndex.xianqi_jiefeng_longhun_rank then
				self:FlushLongHunRankView()
			end
		elseif "login_reward" == k then
			local day_index = v[1] or 0
			self:ShowLoginReward(day_index)
		elseif "login_view" == k then
			self:FlushLoginView()
			--self:FlushLoginSelectDay()
		elseif "first_recharge_view" == k then
			self:FlushShouChongView()
		elseif "leichong_recharge_view" == k then
			self:FlushLeiChongView()
		elseif "duobei_view" == k then
			self:FlushDBView()
		elseif "longhun_view" == k then
			self:FlushLHView()
		elseif "xingtian_laixi" == k then
			self:FlushJiangLinView()
		elseif "juanxian" == k then
			self:FlushJuanXianView()
			self:FlushJXRewardState()
		elseif "haoli" == k then
			self:FlushHaoLiView()
		elseif "role_cap" == k then
			self:FluahRoleCap()
		elseif "turntable" == k then
			self:FlushTurnTableView(v)
		elseif "server_cap" == k then
			self:FluahServerCap()
		elseif "kanjia" == k then
			self:FluahKanJia()
		elseif "change_day_flush" == k then
			self:SetLoadTabIndex()
		end
	end
end

function ActXianQiJieFengView:SetTabIndex()
	local tb = ConfigManager.Instance:GetAutoConfig("act_xianqi_jiefeng_config_auto").beizhan_theme_dec
	local remind_data_map = ActXianQiJieFengWGData.Instance:GetRemindNameMap()
	self.tab_name_list = {}
	self.sub_tab_name_list = {}
	self.remind_name_list = {}

	for k, v in ipairs(tb) do
		self.tab_name_list[v.rank_id] = v.tab_name

		self.remind_name_list[v.rank_id] = {}
		table.insert(self.remind_name_list[v.rank_id], remind_data_map[v.real_id])

		if v.real_id == 71 then
			self.sub_tab_name_list[v.rank_id] = Language.XianQiJieFengAct.CatTabName
			local len = #self.sub_tab_name_list[v.rank_id]
			if len > 1 then
				for i = 1, len - 1 do
					table.insert(self.remind_name_list[v.rank_id], remind_data_map[v.real_id + i])
				end
			end
		elseif v.real_id == 81 then
			self.sub_tab_name_list[v.rank_id] = ActXianQiJieFengWGData.Instance:GetHaoLiTabName()
			local len = #self.sub_tab_name_list[v.rank_id]
			if len > 1 then
				for i = 1, len - 1 do
					table.insert(self.remind_name_list[v.rank_id], remind_data_map[v.real_id + i])
				end
			end
		end
	end
end

function ActXianQiJieFengView:SetHaoLiTabState()
	CountDownManager.Instance:RemoveCountDown(self.tab_count_down)
	local cur_day = ActXianQiJieFengWGData.Instance:GetHaoLiDayNum()
	local theme_count = ActXianQiJieFengWGData.Instance:GetThemeCount()
	for i = cur_day, theme_count - 1 do
		self.tabbar:SetToggleEnable(TabIndex.xianqi_jiefeng_haoli + i, false)
	end
end

function ActXianQiJieFengView:SetTabSate()
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_login,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_login))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_shouchong,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_shouchong))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_leichong,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_leichong))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_duobei,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_duobei))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_longhun,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_longhun))
	-- self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_juanxian, ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_juanxian))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_juanxian, false)
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_turntable,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_turntable))

	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_cap,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_cap))
	--self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_cap2, ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_cap))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_cap2, false)
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_cap3,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_cap))

	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_haoli,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_haoli))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_haoli2,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_haoli))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_haoli3,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_haoli))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_haoli4,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_haoli))
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_laixi,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_laixi))

	--龙魂冲榜
	self.tabbar:SetToggleVisible(TabIndex.xianqi_jiefeng_longhun_rank,
		ActXianQiJieFengWGData.Instance:GetActivityState(TabIndex.xianqi_jiefeng_longhun_rank))

	self.tabbar:SetHorToggleSelect(ActXianQiJieFengWGData.Instance:GetCanBuyGiftTabIndex())
end

function ActXianQiJieFengView:TabTimeCountDown()
	self.tab_count_down = "tab_count_down"
	CountDownManager.Instance:AddCountDown(self.tab_count_down, nil, BindTool.Bind1(self.SetHaoLiTabState, self),
		TimeWGCtrl.Instance:GetServerTime() + 1, nil, 1)
end
