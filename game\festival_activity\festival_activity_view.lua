FestivalActivityView = FestivalActivityView or BaseClass(SafeBaseView)


--需要倒计时
local NeedShowCountDown = {
	[ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2] = true,  --秒杀
	[ACTIVITY_TYPE.FESTIVAL_ACT_OA_LIMIT_RECHARGE] = true, --累充
}

function FestivalActivityView:__init()
	self.view_style = ViewStyle.Half

	self:SetMaskBg(true, false)
	local assetbundle = "uis/view/festival_activity_ui_prefab"
	self:AddViewResource(0, assetbundle, "layout_festival_activity_panel")


	-- self:AddViewResource(TabIndex.festival_activity_2109, "uis/view/festival_activity_ui/day_first_chongzhi_prefab", "layout_festival_dayfirst_recharge")
	-- self:AddViewResource(TabIndex.festival_activity_2112, "uis/view/festival_activity_ui/zhaocai_miaomiao_prefab", "layout_zhaocai_miaomiao_view")
	-- self:AddViewResource(TabIndex.festival_activity_2247, "uis/view/festival_activity_ui/nation_war_prefab", "layout_festival_nation_war_view")
	-- self:AddViewResource(TabIndex.festival_activity_2118, "uis/view/festival_activity_ui/hmwd_ui_prefab", "layout_festival_hmwd")
	-- self:AddViewResource(TabIndex.festival_activity_2127, "uis/view/festival_activity_ui/juling_ui_prefab", "juling_view")
	-- self:AddViewResource(TabIndex.festival_activity_2119, "uis/view/festival_activity_ui/liemo_daren_prefab", "layout_liemo_daren")

	-- self:AddViewResource(TabIndex.festival_activity_2262, "uis/view/festival_activity_ui/login_youli_ui_prefab", "layout_login_reward")
	-- self:AddViewResource(TabIndex.festival_activity_2263, "uis/view/festival_activity_ui/leichong_ui_prefab", "layout_leichong_view")
	-- self:AddViewResource(TabIndex.festival_activity_2261, "uis/view/festival_activity_ui/festival_duobei_prefab", "layout_festival_duobei")
	-- self:AddViewResource(TabIndex.festival_activity_2271, "uis/view/festival_activity_ui/miaosha_ui_prefab", "festival_miaosha_view")
	-- self:AddViewResource(TabIndex.festival_activity_2269, "uis/view/festival_activity_ui/sp_rank_prefab", "layout_special_rank")
	-- self:AddViewResource(TabIndex.festival_activity_2270, "uis/view/festival_activity_ui/niudan_ui_prefab", "layout_festival_niudan")
	-- self:AddViewResource(TabIndex.festival_activity_2271, "uis/view/festival_activity_ui/festival_exchage_prefab", "layout_exchange_shop")



	--self:AddViewResource(TabIndex.festival_activity_2267, "uis/view/festival_activity_ui/mowang_youli_ui_prefab", "layout_festival_mowan")

	self:AddViewResource(TabIndex.festival_activity_2261, "uis/view/festival_activity_ui/festival_duobei_prefab",
		"layout_festival_duobei")
	self:AddViewResource(TabIndex.festival_activity_2262, "uis/view/festival_activity_ui/login_youli_ui_prefab",
		"layout_login_reward")
	self:AddViewResource(TabIndex.festival_activity_2263, "uis/view/festival_activity_ui/leichong_ui_prefab",
		"layout_leichong_view")
	self:AddViewResource(TabIndex.festival_activity_2264, "uis/view/festival_activity_ui/trun_table_prefab",
		"layout_festival_trun_table")
	self:AddViewResource(TabIndex.festival_activity_2265, "uis/view/festival_activity_ui/festival_exchage_prefab",
		"layout_exchange_shop")
	self:AddViewResource(TabIndex.festival_activity_2267, "uis/view/festival_activity_ui/mowang_youli_ui_prefab",
		"layout_festival_mowan")
	self:AddViewResource(TabIndex.festival_activity_2268, "uis/view/festival_activity_ui/chushen_ui_prefab",
		"layout_chushen_view")
	self:AddViewResource(TabIndex.festival_activity_2269, "uis/view/festival_activity_ui/sp_rank_prefab",
		"layout_special_rank")
	--self:AddViewResource(TabIndex.festival_activity_2270, "uis/view/festival_activity_ui/niudan_ui_prefab", "layout_festival_niudan")
	self:AddViewResource(TabIndex.festival_activity_2271, "uis/view/festival_activity_ui/miaosha_ui_prefab",
		"festival_miaosha_view")


	self:AddViewResource(0, assetbundle, "festival_view_time")
	self:AddViewResource(0, assetbundle, "rule_panel")
	self:AddViewResource(0, assetbundle, "VerticalTabbar")
	self:InitSPRank()
	self.tab_sub = {}
	self.default_index = 0

	self.item_change_callback = BindTool.Bind(self.OnNotifyDataChangeCallBack, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
end

-- 计算要显示的index（可重写）
function FestivalActivityView:CalcShowIndex()
	return FestivalActivityWGData.Instance:GetOneOpenTabIndex()
end

function FestivalActivityView:__delete()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
	self.item_change_callback = nil
end

function FestivalActivityView:OnNotifyDataChangeCallBack()
	if self.show_index == TabIndex.festival_activity_2265 then
		self:FlushExchange()
	end
end

function FestivalActivityView:ReleaseCallBack()
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if CountDownManager.Instance:HasCountDown("festival_activity_common_count_down") then
		CountDownManager.Instance:RemoveCountDown("festival_activity_common_count_down")
	end

	GlobalEventSystem:UnBind(self.new_flag_change)
	self.tabbar_loading = nil

	self.jump_tabbar_index = 0
	self.rule_content = nil
	self.rule_title = nil
	self.activity_cfg = nil
	self.need_tabbar_move_flag = nil
	self:ReleaseLoginRewardView()
	--self:DelFireworksView()
	self:DTYYReleaseLeiChongView()
	self:ReleaseDBView()
	self:DeleteHeFuMiaoSha()
	self:ReleaseCallBackSPRank()
	self:ReleaseExchangeCallBack()
	self:DeleteChuShen()
	self:TurnTableReleaseCallBack()
	self:ReleaseCallBackMoWang()
end

function FestivalActivityView:OpenCallBack()
	self:SetTabSate()
	self:SetTabbarMoveFlag()
end

function FestivalActivityView:OpenIndexCallBack(index)
	-- if index == TabIndex.festival_activity_2109 then
	-- 	self:OpenIndexCallBackShouChongView()
	-- else
	-- if index == TabIndex.festival_activity_2118 then
	-- 	self:OpenMergeHMWD()
	-- elseif index ==TabIndex.festival_activity_2247 then --仙盟争霸
	-- self:LoadIndexCallBackGuildWar()
	-- elseif index ==TabIndex.festival_activity_2112 then
	-- 	self:LoadIndexCallBackZhaoCaiMiao()
	if index == TabIndex.festival_activity_2263 then
		self:OpenIndexCallBackLeiChong()
	elseif index == TabIndex.festival_activity_2261 then
		self:OpenCallBackDuoBei()
		-- elseif index == TabIndex.festival_activity_2119 then
		-- 	self:OpenIndexCallBackLieMoDaRen()
	end
end

function FestivalActivityView:LoadCallBack()
	self.jump_tabbar_index = 0
	self.activity_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_config_auto").festival_activity_dec

	self.tabbar_loading = false
	if not self.tabbar then
		local tab_index_name, remind_tab = FestivalActivityWGData.Instance:GetOperationViewInfo()
		self.remind_tab = remind_tab
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabbarInfo, self))
		self.tabbar:Init(tab_index_name, nil, "uis/view/festival_activity_ui_prefab", nil, remind_tab,
			VerFestivalItemRender)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_bar"].transform)
	end

	self:SetFestivalCommonImg()
	self.green_color = FestivalActivityWGData.Instance:GetCommonGreenColor()
	self.common_color = FestivalActivityWGData.Instance:GetCommonColor()
	self.count_down_color = FestivalActivityWGData.Instance:GetCountDownColor()

	-- self.node_list.outside_rule_content.text.color = Str2C3b(self.count_down_color)
	local pos_x, pos_y = FestivalActivityWGData.Instance:GetCountPosition()
	RectTransform.SetAnchoredPositionXY(self.node_list.rule_panel_root.rect, pos_x, pos_y)
	RectTransform.SetAnchoredPositionXY(self.node_list.remain_time_root.rect, pos_x, pos_y)

	XUI.AddClickEventListener(self.node_list.common_rule_btn, BindTool.Bind(self.OnClickCommonRuleBtn, self))
	self.new_flag_change = GlobalEventSystem:Bind(ACTIVITY_BTN_EVENT.MERGE_ACT_BTN_CHANGE,
		BindTool.Bind(self.NewFalgChange, self))
end

--不同节日的通用的底板图片设置
function FestivalActivityView:SetFestivalCommonImg()
	local fa_big_bg_bundle, fa_big_bg_asset = ResPath.GetFestivalRawImages("di")
	self.node_list["fa_big_bg"].raw_image:LoadSprite(fa_big_bg_bundle, fa_big_bg_asset, function()
		self.node_list["fa_big_bg"].raw_image:SetNativeSize()
	end)

	local fa_close_bundle, fa_close_asset = ResPath.GetFestivalActImages("a2_jrkh_gb")
	self.node_list["btn_close_window"].image:LoadSprite(fa_close_bundle, fa_close_asset, function()
		self.node_list["btn_close_window"].image:SetNativeSize()
	end)

	local rule_bundle, rule_asset = ResPath.GetFestivalActImages("a2_jrkh_wh")
	self.node_list["common_rule_btn"].image:LoadSprite(rule_bundle, rule_asset, function()
		self.node_list["common_rule_btn"].image:SetNativeSize()
	end)
end

function FestivalActivityView:LoadIndexCallBack(index)
	if index == TabIndex.festival_activity_2262 then --登录有礼
		self:LoadIndexCallBackLoginRewarView()
		-- elseif index ==TabIndex.festival_activity_2270 then --占星
		-- 	self:InitFireworksView()
	elseif index == TabIndex.festival_activity_2263 then --超值累充
		self:LoadIndexCallBackLeiChongView()
	elseif index == TabIndex.festival_activity_2261 then --多倍有礼
		self:LoadIndexCallBackDuoBei()
	elseif index == TabIndex.festival_activity_2271 then --限时秒杀
		self:LoadIndexCallBackHeFuMiaoSha()
	elseif index == TabIndex.festival_activity_2267 then --魔王有礼
		self:LoadIndexCallBackMoWang()
	elseif index == TabIndex.festival_activity_2269 then --排行榜
		self:LoadCallBackSPRank()
	elseif index == TabIndex.festival_activity_2265 then
		self:LoadIndexCallBackExchange()
	elseif index == TabIndex.festival_activity_2268 then --天才厨神
		self:LoadIndexCallBackChuShen()
	elseif index == TabIndex.festival_activity_2264 then --转盘
		self:TurnTableLoadCallBack()
	end
end

function FestivalActivityView:ShowIndexCallBack(index)
	if self.is_clicked_miaosha then
		self.is_clicked_miaosha = false
		self:ClearMiaoShaoNewFlag()
	end

	self:JumpTabbarReloadData(index)

	if index ~= 0 then
		self:ClearRuleInfo()
		self:SetActRemainTime(index)
		self:SetRightTimeTitleText(string.format(Language.FestivalActivity.ActivityTime, self.count_down_color))
	end
	if index == TabIndex.festival_activity_2262 then
		self:SetLoginYouLiViewInfo()
		-- elseif index == TabIndex.festival_activity_2270 then --扭蛋
		-- 	self:ShowIndexFireworks()
	elseif index == TabIndex.festival_activity_2263 then --累充
		self:OnFlushLeiChongTipsAndRule()
	elseif index == TabIndex.festival_activity_2261 then --多倍有礼
		self:SetDuoBeiTips()
	elseif index == TabIndex.festival_activity_2271 then --限时秒杀
		self:ShowIndexCallHeFuMiaoSha()
	elseif index == TabIndex.festival_activity_2269 then --排行榜
		self:ShowIndexCallBackSPRank()
	elseif index == TabIndex.festival_activity_2265 then -- 兑换
		self:OnSelectExchangeShop()
	elseif index == TabIndex.festival_activity_2267 then --魔王有礼
	elseif index == TabIndex.festival_activity_2268 then --天才厨神
		self:ShowIndexCallChuShen()
	elseif index == TabIndex.festival_activity_2264 then --转盘
		self:SetRuleInfoActive(false)
		self:TurnTableShowIndexCallBack()
	end
end

function FestivalActivityView:ClearMiaoShaoNewFlag()
	local is_enough_level = FestivalMiaoshaWGData.Instance:GetAvtivityIsOpen()
	local state = FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2)

	if not is_enough_level or not state then
		return
	end
end

function FestivalActivityView:CloseCallBack()
	self:ClearMiaoShaoNewFlag()
	-- FestivalJuLingWGCtrl.Instance:CloseFriendView()
end

function FestivalActivityView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.festival_activity_2262 then --登录有礼
				self:OperationFlushLoginView()
				-- elseif index == TabIndex.festival_activity_2270 then 	--占星
				-- 	self:OnFlushFireworks(v)
			elseif index == TabIndex.festival_activity_2263 then
				self:DTYYFlushLeiChongView()
			elseif index == TabIndex.festival_activity_2261 then
				self:FlushDBView()
			elseif index == TabIndex.festival_activity_2271 then --限时秒杀
				self:FlushHeFuMiaoSha()
			elseif index == TabIndex.festival_activity_2267 then --魔王有礼
				self:OnFlushMoWang(param_t)
			elseif index == TabIndex.festival_activity_2269 then
				self:OnFlushSPRank(param_t)
			elseif index == TabIndex.festival_activity_2265 then
				self:FlushExchange()
			elseif index == TabIndex.festival_activity_2268 then --天才厨神
				self:FlushChuShen()
			elseif index == TabIndex.festival_activity_2264 then --转盘
				self:TurnTableOnFlush()
			end
		elseif "RefreshLogin" == k then
			self:OperationFlushLoginView()
		elseif "boss_times" == k then
			self:OnBossTimesChange()
		elseif "mowang_youli_hot_update" == k then
			self:FlushMoWangViewInfo()
		elseif "mowang_youli_times" == k then
			self:FlushPreviewRewardMoWang()
		elseif "ZhaoCaiMiaoMiaoPlayNum" == k then --播放数字动画
			self:MiaoMiaoCountGetAni()
		elseif "ZhaoCaiMiaoMiaoPlayXianyu" == k then
			-- self:PlayEffectByZhaoCaiMiaoMiao()
		elseif "ZhaoCaiMiaoMiaoPlayTween" == k then
			self:PlayMiaoMiaoTween()
		elseif "RefreshLoginReward" == k then
			self:ShowLoginReward()
		elseif "RefreshDuoBei" == k then
			self:FlushDBView()
		elseif "ChuShenResult" == k then
			self:ChuShenPlayAni()
		elseif "ChuShenAllInfo" == k then
			self:ChuShenPreviousRewardData()
		elseif "PlayRollAnimal" == k then
			self:TurnTablePlayRollAnimal()
			-- elseif "TurnTableFlushWorld" == k then
			-- 	self:TurnTableFlushWorld()
			-- elseif "TurnTableFlushPersonal" == k then
			-- 	self:TurnTableFlushPersonal()
		elseif "ChuShenItemChange" == k then
			self:ChuShenItemChange()
		end
	end

	self:SetRuleInfoActive(true)
end

--tabbar是否加载完毕
function FestivalActivityView:SetTabbarInfo()
	self.tabbar_loading = true
	self:SetTabSate()
	self:NewFalgChange()
end

--按钮的显示和隐藏
function FestivalActivityView:SetTabSate()
	if nil == self.tabbar or not self.tabbar_loading then
		return
	end

	-- 这里边的条件包含服务器的开启状态和你注册的开启条件，其他特殊处理你酌情来
	for k, v in ipairs(self.activity_cfg) do
		local tab_index = v.rank_id * 10

		local is_open = FestivalActivityWGData.Instance:GetActivityState(v.activity_type)
		self.tabbar:SetToggleVisible(tab_index, is_open or false)
	end
end

--设置活动剩余时间 index: tab_index , end_time:活动结束时间戳
function FestivalActivityView:SetActRemainTime(index, end_time)
	local act_end_time = 0
	if end_time then
		act_end_time = end_time
	else
		local activity_type = FestivalActivityWGData.Instance:GetCurSelectActivityType(index)
		act_end_time = FestivalActivityWGData.Instance:GetActivityInValidTime(activity_type)
	end

	if CountDownManager.Instance:HasCountDown("festival_activity_common_count_down") then
		CountDownManager.Instance:RemoveCountDown("festival_activity_common_count_down")
	end

	--合服排行结算后拦截
	if index == TabIndex.festival_activity_2269 then
		if FestivalSpecialRankWGData.Instance:GetIsEnd() then
			if self.node_list and self.node_list.common_remain_time_text then
				self.node_list.common_remain_time_text.text.text = Language.FestivalActivity.YiJieShu
			end
			return
		end
	end

	if act_end_time ~= 0 then
		self.node_list.remain_time_root:SetActive(true)
		local time = act_end_time - TimeWGCtrl.Instance:GetServerTime()
		if time > 0 then
			CountDownManager.Instance:AddCountDown("festival_activity_common_count_down",
				BindTool.Bind(self.CommonUpdateTime, self),
				BindTool.Bind(self.CommonCompleteTime, self), nil, time, 1)
		end
	end
end

function FestivalActivityView:CommonUpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)

	local str = string.format(Language.FestivalActivity.ActEndTime, self.green_color,
		TimeUtil.FormatSecondDHM6(temp_seconds))
	if self.node_list and self.node_list.common_remain_time_text then
		self.node_list.common_remain_time_text.text.text = str
	end
end

function FestivalActivityView:CommonCompleteTime(elapse_time, total_time)
	if self.node_list and self.node_list.common_remain_time_text and self.node_list.remain_time_root then
		self.node_list.remain_time_root:SetActive(false)
		self.node_list.common_remain_time_text.text.text = ""
	end
end

function FestivalActivityView:SetRuleInfoActive(enabled)
	if not self.rule_content or not self.rule_title or self.rule_content == "" or self.rule_title == "" then
		enabled = false
	end

	if self.node_list and self.node_list.rule_panel_root then
		self.node_list.rule_panel_root:SetActive(enabled)
	end
end

--设置外边玩法提示
function FestivalActivityView:SetOutsideRuleTips(content)
	-- self.node_list.outside_rule_content.text.text = content
end

function FestivalActivityView:SetRuleInfo(rule_content, rule_title)
	self.rule_content = rule_content
	self.rule_title = rule_title
end

function FestivalActivityView:ClearRuleInfo()
	self.rule_content = nil
	self.rule_title = nil
end

function FestivalActivityView:OnClickCommonRuleBtn()
	RuleTip.Instance:SetContent(self.rule_content, self.rule_title)
end

function FestivalActivityView:NewFalgChange()

end

function FestivalActivityView:SetRightTimeTitleText(dec)
	self.node_list["tip_label"].text.text = dec
end

function FestivalActivityView:SetTabbarMoveFlag()
	self.need_tabbar_move_flag = true
end

function FestivalActivityView:JumpTabbarReloadData(select_index)
	if not self.need_tabbar_move_flag then return end

	self.need_tabbar_move_flag = false
	-- 6 当前最大展示数    169 格子大小
	local can_see_count = 6
	local cell_size_x = 169
	local jump_index = math.floor(select_index / 10)
	self.jump_tabbar_index = jump_index
	local x = jump_index > can_see_count and (3 + (jump_index - can_see_count) * cell_size_x) or 0
	if self.node_list and self.node_list["VerticalTabbarContent"] then
		local y = self.node_list["VerticalTabbarContent"].rect.anchoredPosition.y
		self.node_list["VerticalTabbarContent"].rect.anchoredPosition = Vector2(-x, y)
	end
end

VerFestivalItemRender = VerFestivalItemRender or BaseClass(VerItemRender)

function VerFestivalItemRender:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("festival_activity_count_down_idx" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("festival_activity_count_down_idx" .. self.index)
	end
	self.is_loaded_view = false
end

function VerFestivalItemRender:SetIndex(index)
	VerItemRender.SetIndex(self, index)
	if self.is_loaded_view then
		self:LoadViewImg()
	end
end

function VerFestivalItemRender:LoadViewImg()
	local activity_type = FestivalActivityWGData.Instance:GetCurSelectActivityType(self.index * 10)

	if self.node_list["HLImage"] then
		local icon_hl_bundle, icon_hl_asset = ResPath.GetFestivalActImages("a2_jrkh_yq")
		self.node_list["HLImage"].image:LoadSprite(icon_hl_bundle, icon_hl_asset, function()
			self.node_list["HLImage"].image:SetNativeSize()
		end)
	end
	if self.node_list["line"] then
		local line_bundle, line_asset = ResPath.GetFestivalActImages("a2_jrkh_line")
		self.node_list["line"].image:LoadSprite(line_bundle, line_asset)
	end
end

function VerFestivalItemRender:LoadCallBack()
	local cfg = FestivalActivityWGData.Instance:GetOtherCfg()
	if cfg and cfg.text_color then
		if self.node_list["Text"] then
			--OperationActivityChangeToQualityText(self.node_list["Text"], cfg.text_color, false, true)
			self.node_list["Text"].text.color = Str2C3b(cfg.text_color)
		end
	end
	if cfg and cfg.hl_text_color then
		if self.node_list["TextHL"] then
			self.node_list["TextHL"].text.color = Str2C3b(cfg.hl_text_color)
			--OperationActivityChangeToQualityText(self.node_list["TextHL"], cfg.hl_text_color, false, true)
		end
	end
	self.is_loaded_view = true

	if self.index then
		self:LoadViewImg()
	end
end

function VerFestivalItemRender:OnFlush()
	VerItemRender.OnFlush(self)
	if CountDownManager.Instance:HasCountDown("festival_activity_count_down_idx" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("festival_activity_count_down_idx" .. self.index)
	end

	local activity_type = FestivalActivityWGData.Instance:GetCurSelectActivityType(self.index * 10)
	if NeedShowCountDown[activity_type] then
		-- self.node_list.Text.rect.anchoredPosition = Vector2(15, 9)
		-- self.node_list.TextHL.rect.anchoredPosition = Vector2(15, 9)
		-- local activity_type = FestivalActivityWGData.Instance:GetCurSelectActivityType(self.index * 10)
		-- local act_end_time = FestivalActivityWGData.Instance:GetActivityInValidTime(activity_type)
		-- if act_end_time ~= 0 then
		--     local time = act_end_time - TimeWGCtrl.Instance:GetServerTime()
		--     if time > 0 then
		--         CountDownManager.Instance:AddCountDown("festival_activity_count_down_idx"..self.index,
		--                 BindTool.Bind(self.CommonUpdateTime, self),
		--                 BindTool.Bind(self.CommonCompleteTime, self), nil, time, 1)
		--     end
		-- end
	else
		-- self.node_list.Text.rect.anchoredPosition = Vector2(15, -3)
		-- self.node_list.TextHL.rect.anchoredPosition = Vector2(15, -3)
		self.node_list.time_txt.text.text = ""
	end
end

function VerFestivalItemRender:CommonUpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	local str = TimeUtil.FormatDToHAndMS(temp_seconds)
	if self.node_list and self.node_list.time_txt then
		self.node_list.hl_time_txt.text.text = str
		self.node_list.time_txt.text.text = str
	end
end

function VerFestivalItemRender:CommonCompleteTime(elapse_time, total_time)
	if self.node_list and self.node_list.time_txt then
		self.node_list.time_txt.text.text = "00:00:00"
		self.node_list.hl_time_txt.text.text = "00:00:00"
	end
end
