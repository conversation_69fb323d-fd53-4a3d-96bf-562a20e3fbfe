HUDManager = HUDManager or BaseClass()
local HUDAssetsManager = typeof(HUDProgramme.HUDAssetsManager)
local TypeUnityTexture = typeof(UnityEngine.Texture)

local show_time = 0.4
local hud_finish_wait_time = 2

local ALL_ATLAS_TEXTURE = {
	[1] = "ui_main",
	[2] = "ui_main_alpha",
}


function HUDManager:__init()
	if nil ~= HUDManager.Instance then
		print_error("[HUDManager]:Attempt to create singleton twice!")
	end
	HUDManager.Instance = self

	-- 加载对应的HUD控制器
	-- 创建管理器
	self.hudmanager_loader = nil
	self.hud_asset_manager = nil
	self.hud_load_finish = false
	self.hud_start_wait = true
	self.check_time = nil
	self.hurt_data_list = nil
	self.scene_start_load = true
	self:CreateManager()
	Runner.Instance:AddRunObj(self, 5)
	GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneLoadingQuite, self))
	GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind1(self.OnSceneLoadingStart, self))
end

function HUDManager:CreateManager()
	local GameRoot = GameObject.Find("GameRoot").transform
	if not IsNil(GameRoot) then
		if not self.hudmanager_loader then
			local hudmanager_loader = AllocAsyncLoader(self, "hud_asset_manager")
			hudmanager_loader:SetIsUseObjPool(true)
			hudmanager_loader:SetParent(GameRoot)
			self.hudmanager_loader = hudmanager_loader
		end
	
		local bundle_name, asset_name = ResPath.GetHUDAssetManager()
		self.hudmanager_loader:Load(bundle_name, asset_name, function(obj)
			self.hud_asset_manager = obj:GetComponent(HUDAssetsManager)
			self:StartLoadAllAtlasTexture()
		end)
	end
end

-- 场景加载完成
function HUDManager:StartLoadAllAtlasTexture()
	self.load_count = 0
	for i, asset_name in ipairs(ALL_ATLAS_TEXTURE) do
		local bundle_name = "uis/hud_programme"
		local new_asset_name = asset_name .. ".png"
		local loader_key = bundle_name .. " " .. asset_name
		local async_loader = AllocResAsyncLoader(self, loader_key)
		async_loader:SetIsASyncLoad(self.is_async_load)
		if self.is_async_load then
			async_loader:SetLoadPriority(self.load_priority)
		end
	
		async_loader:Load(bundle_name, new_asset_name, TypeUnityTexture, function(tex)
			self.load_count = self.load_count + 1
			if not IsNil(self.hud_asset_manager) then
				self.hud_asset_manager:AddAtlaTexture(asset_name, tex)
			end

			self:CheckAllLoadFinish()
		end)
	end
end

-- 
function HUDManager:CheckAllLoadFinish()
	if self.load_count == #ALL_ATLAS_TEXTURE then
		self.hud_load_finish = true

		if not IsNil(self.hud_asset_manager) then
			self.hud_asset_manager:InitAltasCfg()
		end
	end
end

-- 场景加载完成
function HUDManager:OnSceneLoadingQuite()
	self.scene_start_load = false
end

-- 场景开始加载
function HUDManager:OnSceneLoadingStart()
	self.scene_start_load = true
end

-- 设置HUD主摄象机
function HUDManager:SetHUDMainCamera(camera)
	if not IsNil(self.hud_asset_manager) then
		-- print_error("设置HUD主摄象机", camera, self.hud_asset_manager)
		self.hud_asset_manager:SetHUDMeshCamera(camera)
	end
end

-- 设置HUD场景摄象机
function HUDManager:SetHUDSceneCamera(camera)
	if not IsNil(self.hud_asset_manager) then
		-- print_error("设置HUD主摄象机", camera, self.hud_asset_manager)
		self.hud_asset_manager:SetHUDSceneCamera(camera)
	end
end

-- 伤害飘字
---@param target 对象物体的 transform
---@param hurt_number 飘字数字
---@param hurt_type 飘字类型（具体请看hud_def）
---@param head_str 前缀字符
---@param head_str 前缀字符2
---@param show_add 是否展示加号
---@param show_sub 是否展示减号
---@param have_number 是否需要展示飘字（一般吸收闪避等不需要展示数字）
function HUDManager:ShowHurt(target, hurt_number, hurt_type, head_str, headStr2, show_add, show_sub, have_number)
	-- print_error("伤害飘字", self.hud_asset_manager)
	if not IsNil(self.hud_asset_manager) then
		self.hud_asset_manager:ShowHurt(target, hurt_number, hurt_type, head_str, headStr2, show_add, show_sub, have_number)
	end
end

function HUDManager:__delete()
	Runner.Instance:RemoveRunObj(self)
	self.hudmanager_loader = nil
	self.hurt_data_list = nil
	HUDManager.Instance = nil
end

-- 更新
function HUDManager:Update(now_time, elapse_time)
	if not self.hud_load_finish then
		return
	end

	if self.hud_start_wait then
		self.check_time = now_time + hud_finish_wait_time
		self.hud_start_wait = false
		return
	end

	if now_time > self.check_time then
		self.check_time = now_time + show_time
		self:ExecuteShowHurt()
	end
end

-- 执行受到的伤害
function HUDManager:ExecuteShowHurt()
	local list = self:GetHurtData()
	local main_role = Scene.Instance:GetMainRole()

	if list and main_role then
		for k, v in pairs(list) do
			self:ExecuteShowHurtEnter(v.new_target, v.new_data)
		end
	end
end

-- 设置主角收到的伤害加入队列
function HUDManager:AddHurtData(target, data)
	if not self.hurt_data_list then
		self.hurt_data_list = {}
	end
	local final_data = {new_target = target, new_data = TableCopy(data)}

	table.insert(self.hurt_data_list, final_data)
end

-- 获取队列
function HUDManager:GetHurtData()
	if self.hurt_data_list == nil or #self.hurt_data_list <= 0 then
		return nil
	end

	local out_put_list = {}

	local SplitType = function (data)
		local hurt_data = data.new_data

		if not out_put_list[hurt_data.fighttype] then
			out_put_list[hurt_data.fighttype] = data
		else
			local out_put_hurt_data = out_put_list[hurt_data.fighttype].new_data
			out_put_hurt_data.blood = out_put_hurt_data.blood + hurt_data.blood
		end
	end

	for i, v in ipairs(self.hurt_data_list) do
		SplitType(v)
	end

	self.hurt_data_list = nil
	return out_put_list
end

--- 展示伤害
function HUDManager:ShowHurtEnter(target, data)
	local blood = data and data.blood or 0
	if blood == nil or blood == 0 then
		return
	end

	local is_main_role = target:IsMainRole()
	if is_main_role then
		self:AddHurtData(target, data)
		return
	end

	self:SetSpecialHurtShow(data)
	self:ExecuteShowHurtEnter(target, data)
end

-- 展示特殊的表现
function HUDManager:SetSpecialHurtShow(data)
	local fight_type = data.fighttype
	if FIGHT_TYPE.NORMAL == fight_type or 
		FIGHT_TYPE.ERCIGONGJI == fight_type or 
		FIGHT_TYPE.BAOJI == fight_type or 
		FIGHT_TYPE.LIANJI == fight_type or 
		FIGHT_TYPE.JICHUAN == fight_type or 
		FIGHT_TYPE.HUIXINYIJI == fight_type or 
		FIGHT_TYPE.BOSS_ZHENSHANG == fight_type or
		FIGHT_TYPE.WUHUN_ZHENSHANG == fight_type then
		GlobalEventSystem:Fire(OtherEventType.ROLE_WUHUNZHENSHEN_HIT_NUMBER)
	end
end

--- 展示伤害
function HUDManager:ExecuteShowHurtEnter(target, data)
	if target == nil or target.draw_obj == nil then
		return
	end

	if self.scene_start_load then
		return
	end

	local fight_type = data.fighttype
	local blood = data.blood
	local deliverer_wuxing_type = data.deliverer_wuxing_type
	local text_type = data.text_type
	local my_wuxing_type = target:GetWuXingType()
	local floating_point = target.draw_obj:GetAttachPoint(AttachPoint.UI)
	local bottom_point = target.draw_obj:GetAttachPoint(AttachPoint.BuffBottom)
	local is_main_role = target:IsMainRole()

	local anim_type = is_main_role and FIGHT_ANIM_TYPE_MAIN_ROLE or FIGHT_ANIM_TYPE
	local anim_show_str = anim_type[fight_type]
	local setting = nil

	if is_main_role then	-- 先取角色特殊的类型
		setting = FIGHT_SPEC_ANIM_TYPE_SETTING[fight_type]
	end

	if setting == nil then	-- 角色没有特殊的类型，再取正常的类型
		setting = FIGHT_ANIM_TYPE_SETTING[fight_type]
	end

	if ( not setting) or (not anim_show_str) or blood == 0 then
		return
	end

	local point = floating_point
	if target:IsMainRole() or target:IsBoss() then
		point = bottom_point
	end

	-- 匹配战斗类型（特殊请新建）
	-- {td_str				--飘字图标
	--	text_str			--飘字艺术字
	--	add					--是否显示加号
	--  sub					--是否显示减号
	--  num					--是否显示数字
	--}
	self:ShowHurt(point, blood, anim_show_str, setting.td_str, setting.text_str, setting.add, setting.sub, setting.num)
end

