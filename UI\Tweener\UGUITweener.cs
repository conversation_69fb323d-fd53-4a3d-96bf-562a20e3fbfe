﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public enum TweenMethod
{
	Linear,
	EaseIn,
	EaseOut,
	EaseInOut,
	BounceIn,
	BounceOut,
}

public abstract class UGUITweener : MonoBehaviour 
{
	static public UGUITweener current;

	public enum Style
	{
		Once,
		Loop,
		PingPong,
	}

	[HideInInspector]
	public TweenMethod method = TweenMethod.Linear;

	[HideInInspector]
	public Style style = Style.Once;

	[HideInInspector]
	public AnimationCurve animationCurve = new AnimationCurve(new Keyframe(0f, 0f, 0f, 1f), new Keyframe(1f, 1f, 1f, 0f));

	[HideInInspector]
	public bool ignoreTimeScale = false;

	[HideInInspector]
	public float delay = 0f;

	[HideInInspector]
	public float duration = 1f;

	[HideInInspector]
	public bool enabled_play = false;

	[HideInInspector]
	public bool disable_finish = true;

	[HideInInspector]
	public bool steeperCurves = false;

	[HideInInspector]
	public int tweenGroup = 0;

	[Tooltip("By default, Update() will be used for tweening. Setting this to 'true' will make the tween happen in FixedUpdate() insted.")]
	public bool useFixedUpdate = false;


	[HideInInspector]
	public List<EventDelegate> onFinished = new List<EventDelegate>();

    [HideInInspector]
    public UnityEngine.Events.UnityAction onFinished2;

    [HideInInspector]
	public List<EventDelegate> onStarted = new List<EventDelegate>();

	[HideInInspector] public GameObject eventReceiver;
	[HideInInspector] public string callWhenFinished;

	bool mStarted = false;
	float mStartTime = 0f;
	float mDuration = 0f;
	float mAmountPerDelta = 1000f;
	float mFactor = 0f;

	bool mIsFinish = false;

	public float amountPerDelta
	{
		get
		{
			if (duration == 0f) return 1000f;

			if (mDuration != duration)
			{
				mDuration = duration;
				mAmountPerDelta = Mathf.Abs(1f / duration) * Mathf.Sign(mAmountPerDelta);
			}
			return mAmountPerDelta;
		}
	}

	public float tweenFactor 
	{ 
		get { return mFactor; } 
		set { mFactor = Mathf.Clamp01(value); } 
	}

	public AnimationOrTween.Direction direction 
	{ 
		get { return amountPerDelta < 0f ? AnimationOrTween.Direction.Reverse : AnimationOrTween.Direction.Forward; } 
	}

	void Reset ()
	{
		if (!mStarted)
		{
			SetStartToCurrentValue();
			SetEndToCurrentValue();
		}
	}

	protected virtual void Start () 
	{
        ExecuteStartEvent ();
		DoUpdate(); 
	}

	protected void Update () 
	{ 
		if (!useFixedUpdate) 
			DoUpdate(); 
	}

	protected void FixedUpdate () 
	{ 
		if (useFixedUpdate) 
			DoUpdate(); 
	}

	protected void DoUpdate ()
	{
		if (mIsFinish) {
			return;
		}

		float delta = ignoreTimeScale && !useFixedUpdate ? Time.unscaledDeltaTime : Time.deltaTime;
		float time = ignoreTimeScale && !useFixedUpdate ? Time.unscaledTime : Time.time;

		if (!mStarted)
		{
			delta = 0;
			mStarted = true;
			mStartTime = time + delay;
		}

		if (time < mStartTime) return;

		// Advance the sampling factor
		mFactor += (duration == 0f) ? 1f : amountPerDelta * delta;

		// Loop style simply resets the play factor after it exceeds 1.
		if (style == Style.Loop)
		{
			if (mFactor > 1f)
			{
				mFactor -= Mathf.Floor(mFactor);
			}
		}
		else if (style == Style.PingPong)
		{
			// Ping-pong style reverses the direction
			if (mFactor > 1f)
			{
				mFactor = 1f - (mFactor - Mathf.Floor(mFactor));
				mAmountPerDelta = -mAmountPerDelta;
			}
			else if (mFactor < 0f)
			{
				mFactor = -mFactor;
				mFactor -= Mathf.Floor(mFactor);
				mAmountPerDelta = -mAmountPerDelta;
			}
		}

		// If the factor goes out of range and this is a one-time tweening operation, disable the script
		if ((style == Style.Once) && (duration == 0f || mFactor > 1f || mFactor < 0f))
		{
			mFactor = Mathf.Clamp01(mFactor);
			Sample(mFactor, true);
			mIsFinish = true;

			if (disable_finish) 
			{
				enabled = false;
			}

			if (current != this)
			{
				UGUITweener before = current;
				current = this;

				if (onFinished != null)
				{
					mTemp = onFinished;
					onFinished = new List<EventDelegate>();

					// Notify the listener delegates
					EventDelegate.Execute(mTemp);

					// Re-add the previous persistent delegates
					for (int i = 0; i < mTemp.Count; ++i)
					{
						EventDelegate ed = mTemp[i];
						if (ed != null && !ed.oneShot) EventDelegate.Add(onFinished, ed, ed.oneShot);
					}
					mTemp = null;
				}

				// Deprecated legacy functionality support
				if (eventReceiver != null && !string.IsNullOrEmpty(callWhenFinished))
					eventReceiver.SendMessage(callWhenFinished, this, SendMessageOptions.DontRequireReceiver);

				current = before;
			}
		}
		else Sample(mFactor, false);
	}

	List<EventDelegate> mTemp = null;

	// Notify the started listener delegates
	public void ExecuteStartEvent()
	{
		mIsFinish = false;
		if (onStarted != null)
		{
			mTemp = onStarted;
			onStarted = new List<EventDelegate>();

			// Notify the listener delegates
			EventDelegate.Execute(mTemp);

			// Re-add the previous persistent delegates
			for (int i = 0; i < mTemp.Count; ++i)
			{
				EventDelegate ed = mTemp[i];
				if (ed != null && !ed.oneShot) EventDelegate.Add(onStarted, ed, ed.oneShot);
			}
			mTemp = null;
		}
	}

	/// <summary>
	/// Convenience function -- set a new OnFinished event delegate (here for to be consistent with RemoveOnFinished).
	/// </summary>
	public void SetOnFinished (EventDelegate.Callback del) { EventDelegate.Set(onFinished, del); }

	/// <summary>
	/// Convenience function -- set a new OnFinished event delegate (here for to be consistent with RemoveOnFinished).
	/// </summary>
	public void SetOnFinished (EventDelegate del) { EventDelegate.Set(onFinished, del); }

	/// <summary>
	/// Convenience function -- add a new OnFinished event delegate (here for to be consistent with RemoveOnFinished).
	/// </summary>
	public void AddOnFinished (EventDelegate.Callback del) { EventDelegate.Add(onFinished, del); }

	/// <summary>
	/// Convenience function -- add a new OnFinished event delegate (here for to be consistent with RemoveOnFinished).
	/// </summary>
	public void AddOnFinished (EventDelegate del) { EventDelegate.Add(onFinished, del); }

	/// <summary>
	/// Convenience function -- set a new OnStarted event delegate (here for to be consistent with RemoveOnFinished).
	/// </summary>
	public void SetOnStarted (EventDelegate.Callback del) { EventDelegate.Set(onStarted, del); }

	/// <summary>
	/// Convenience function -- set a new OnStarted event delegate (here for to be consistent with RemoveOnFinished).
	/// </summary>
	public void SetOnStarted (EventDelegate del) { EventDelegate.Set(onStarted, del); }

	/// <summary>
	/// Convenience function -- add a new OnStarted event delegate (here for to be consistent with RemoveOnFinished).
	/// </summary>
	public void AddOnSatrted (EventDelegate.Callback del) { EventDelegate.Add(onStarted, del); }

	/// <summary>
	/// Convenience function -- add a new OnStarted event delegate (here for to be consistent with RemoveOnFinished).
	/// </summary>
	public void AddOnStarted (EventDelegate del) { EventDelegate.Add(onStarted, del); }

	/// <summary>
	/// Remove an OnFinished delegate. Will work even while iterating through the list when the tweener has finished its operation.
	/// </summary>
	public void RemoveOnFinished (EventDelegate del)
	{
		if (onFinished != null) onFinished.Remove(del);
		if (mTemp != null) mTemp.Remove(del);
	}

	/// <summary>
	/// Mark as not started when finished to enable delay on next play.
	/// </summary>
	void OnDisable () { mStarted = false; }

	void OnEnable()
	{
		if (enabled_play) 
		{
			ResetToBeginning ();
		}
	}

	/// <summary>
	/// Sample the tween at the specified factor.
	/// </summary>
	public void Sample (float factor, bool isFinished)
	{
		// Calculate the sampling value
		float val = Mathf.Clamp01(factor);

		if (method == TweenMethod.EaseIn)
		{
			val = 1f - Mathf.Sin(0.5f * Mathf.PI * (1f - val));
			if (steeperCurves) val *= val;
		}
		else if (method == TweenMethod.EaseOut)
		{
			val = Mathf.Sin(0.5f * Mathf.PI * val);

			if (steeperCurves)
			{
				val = 1f - val;
				val = 1f - val * val;
			}
		}
		else if (method == TweenMethod.EaseInOut)
		{
			const float pi2 = Mathf.PI * 2f;
			val = val - Mathf.Sin(val * pi2) / pi2;

			if (steeperCurves)
			{
				val = val * 2f - 1f;
				float sign = Mathf.Sign(val);
				val = 1f - Mathf.Abs(val);
				val = 1f - val * val;
				val = sign * val * 0.5f + 0.5f;
			}
		}
		else if (method == TweenMethod.BounceIn)
		{
			val = BounceLogic(val);
		}
		else if (method == TweenMethod.BounceOut)
		{
			val = 1f - BounceLogic(1f - val);
		}

		// Call the virtual update
		OnUpdate((animationCurve != null) ? animationCurve.Evaluate(val) : val, isFinished);
	}

	/// <summary>
	/// Main Bounce logic to simplify the Sample function
	/// </summary>
	float BounceLogic (float val)
	{
		if (val < 0.363636f) // 0.363636 = (1/ 2.75)
		{
			val = 7.5685f * val * val;
		}
		else if (val < 0.727272f) // 0.727272 = (2 / 2.75)
		{
			val = 7.5625f * (val -= 0.545454f) * val + 0.75f; // 0.545454f = (1.5 / 2.75) 
		}
		else if (val < 0.909090f) // 0.909090 = (2.5 / 2.75) 
		{
			val = 7.5625f * (val -= 0.818181f) * val + 0.9375f; // 0.818181 = (2.25 / 2.75) 
		}
		else
		{
			val = 7.5625f * (val -= 0.9545454f) * val + 0.984375f; // 0.9545454 = (2.625 / 2.75) 
		}
		return val;
	}

	/// <summary>
	/// Play the tween.
	/// </summary>
	[System.Obsolete("Use PlayForward() instead")]
	public void Play () 
	{ 
		Play(true); 
	}

	/// <summary>
	/// Play the tween forward.
	/// </summary>
	public void PlayForward () 
	{ 
		Play(true); 
	}

	/// <summary>
	/// Play the tween in reverse.
	/// </summary>
	public void PlayReverse () 
	{ 
		Play(false); 
	}

    public void SetSelfStatusFalse()
    {
        this.gameObject.SetActive(false);
    }

	/// <summary>
	/// Manually activate the tweening process, reversing it if necessary.
	/// </summary>
	public virtual void Play (bool forward)
	{
		mAmountPerDelta = Mathf.Abs(amountPerDelta);
		if (!forward) mAmountPerDelta = -mAmountPerDelta;

		if (!enabled)
		{
			enabled = true;
			mStarted = false;
		}
		ExecuteStartEvent ();
		DoUpdate();
	}

	/// <summary>
	/// Manually reset the tweener's state to the beginning.
	/// If the tween is playing forward, this means the tween's start.
	/// If the tween is playing in reverse, this means the tween's end.
	/// </summary>
	public void ResetToBeginning ()
	{
		mStarted = false;
		mFactor = (amountPerDelta < 0f) ? 1f : 0f;
		ExecuteStartEvent ();
		Sample(mFactor, false);
	}

	/// <summary>
	/// Manually start the tweening process, reversing its direction.
	/// </summary>
	public void Toggle ()
	{
		if (mFactor > 0f)
		{
			mAmountPerDelta = -amountPerDelta;
		}
		else
		{
			mAmountPerDelta = Mathf.Abs(amountPerDelta);
		}
		enabled = true;
	}


	/// <summary>
	/// Actual tweening logic should go here.
	/// </summary>

	abstract protected void OnUpdate (float factor, bool isFinished);

	/// <summary>
	/// Starts the tweening operation.
	/// </summary>

	static public T Begin<T> (GameObject go, float duration, float delay = 0f) where T : UGUITweener
	{
		T comp = go.GetComponent<T>();
		#if UNITY_FLASH
		if ((object)comp == null) comp = (T)go.AddComponent<T>();
		#else
		// Find the tween with an unset group ID (group ID of 0).
		if (comp != null && comp.tweenGroup != 0)
		{
			comp = null;
			T[] comps = go.GetComponents<T>();
			for (int i = 0, imax = comps.Length; i < imax; ++i)
			{
				comp = comps[i];
				if (comp != null && comp.tweenGroup == 0) break;
				comp = null;
			}
		}

		if (comp == null)
		{
			comp = go.AddComponent<T>();

			if (comp == null)
			{
				Debug.LogError("Unable to add " + typeof(T) + " to " + GetHierarchy(go), go);
				return null;
			}
		}
		#endif
		comp.mStarted = false;
		comp.mFactor = 0f;
		comp.duration = duration;
		comp.mDuration = duration;
		comp.delay = delay;
		comp.mAmountPerDelta = duration > 0f ? Mathf.Abs(1f / duration) : 1000f;
		comp.style = Style.Once;
		comp.animationCurve = new AnimationCurve(new Keyframe(0f, 0f, 0f, 1f), new Keyframe(1f, 1f, 1f, 0f));
		comp.eventReceiver = null;
		comp.callWhenFinished = null;
		comp.onFinished.Clear();
		if (comp.mTemp != null) comp.mTemp.Clear();
		comp.onStarted.Clear ();
		comp.enabled = true;
		return comp;
	}

	/// <summary>
	/// Set the 'from' value to the current one.
	/// </summary>
	public virtual void SetStartToCurrentValue () { }

	/// <summary>
	/// Set the 'to' value to the current one.
	/// </summary>
	public virtual void SetEndToCurrentValue () { }

	/// <summary>
	/// Returns the hierarchy of the object in a human-readable format.
	/// </summary>
	static public string GetHierarchy (GameObject obj)
	{
		if (obj == null) return "";
		string path = obj.name;

		while (obj.transform.parent != null)
		{
			obj = obj.transform.parent.gameObject;
			path = obj.name + "\\" + path;
		}
		return path;
	}
}
