-- 整备

MechaArmamentView = MechaArmamentView or BaseClass(SafeBaseView)

function MechaArmamentView:__init()
    self:SetMaskBg(false)
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    local bundle = "uis/view/mecha_ui_prefab"
    self:AddViewResource(0, bundle, "layout_mecha_bg")
    self:AddViewResource(0, bundle, "layout_mecha_armament_view")
    self:AddViewResource(0, bundle, "layout_mecha_top_panel")
end

function MechaArmamentView:LoadCallBack()
    local bundle, asset = ResPath.GetRawImagesPNG("a2_jj_bg_1")
    self.node_list.mecha_bg.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.mecha_bg.raw_image:SetNativeSize()
    end)

    if not self.ma_part_list then
        self.ma_part_list = {}

        local item_cell = nil
        for i = 0, 7 do
            item_cell = MAMechaPartItemRender.New(self.node_list["mecha_mam_item" .. i], self)
            item_cell:SetIndex(i)
            self.ma_part_list[i] = item_cell

            self.node_list["mecha_mam_item" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickPart, self, i))
        end
    end

    if not self.gundam_model then
        self.gundam_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["mecha_mam_model_root"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.gundam_model:SetRenderTexUI3DModel(display_data)
        -- self.gundam_model:SetUI3DModel(self.node_list["mecha_mam_model_root"].transform,
        --     self.node_list["mecha_mam_model_root"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    if not self.mam_mecha_weapon_list then
        self.mam_mecha_weapon_list = AsyncListView.New(MAMMechaWeaponItemRender, self.node_list.mam_mecha_weapon_list)
        self.mam_mecha_weapon_list:SetSelectCallBack(BindTool.Bind(self.OnSelectMechaPartHandler, self))
    end

    XUI.AddClickEventListener(self.node_list.btn_mam_save, BindTool.Bind(self.OnClickMamSave, self))
    XUI.AddClickEventListener(self.node_list.mam_tips, BindTool.Bind(self.OnClickMAMTip, self))

    self.select_part = 0
    self.show_action = false
end

function MechaArmamentView:ShowIndexCallBack()
    if self.gundam_model then
        self.gundam_model:SetRotation({ x = 0, y = 180, z = 0 })
        self.gundam_model:PlayRoleShowAction()
    end
end

function MechaArmamentView:ReleaseCallBack()
    if self.gundam_model then
        self.gundam_model:DeleteMe()
        self.gundam_model = nil
    end

    if self.ma_part_list then
        for k, v in pairs(self.ma_part_list) do
            v:DeleteMe()
        end
    end

    if self.mam_mecha_weapon_list then
        self.mam_mecha_weapon_list:DeleteMe()
        self.mam_mecha_weapon_list = nil
    end

    self.model_cache = nil
    self.ma_part_list = nil
    self.select_part_info_cache = nil
    self.select_part_res_info_cache = nil
    self.select_mechan_seq = nil
    self.select_part = nil

    self:ClearNeedPartAlphaTween()
end

function MechaArmamentView:SetSelectMechaSeq(mecha_seq)
    self.select_part = 0
    self.select_mechan_seq = mecha_seq
    local part_info, part_res_info = MechaWGData.Instance:GetArmamentPartInfo(self.select_mechan_seq)
    self.select_part_info_cache = part_info
    self.select_part_res_info_cache = part_res_info
end

function MechaArmamentView:OnFlush()
    if nil == self.select_mechan_seq or self.select_mechan_seq < 0 then
        self:Close()
    end

    for i = 0, #self.ma_part_list do
        local data = { mechan_seq = self.select_mechan_seq }
        self.ma_part_list[i]:SetData(data)
    end

    self:SetTogSelect()
    self:FlushModel()
    self:SetMamSaveBtnState()
end

function MechaArmamentView:SetTogSelect()
    self.node_list["mecha_mam_item" .. self.select_part].toggle.isOn = true
    self:OnClickPart(self.select_part, true)
end

function MechaArmamentView:FlushModel()
    local part_info = {
        gundam_seq = self.select_mechan_seq,
        gundam_body_res = self.select_part_res_info_cache[MECHA_PART_TYPE.BODY] or 0,
        gundam_weapon_res = self.select_part_res_info_cache[MECHA_PART_TYPE.WEAPON] or 0,
        gundam_left_arm_res = self.select_part_res_info_cache[MECHA_PART_TYPE.LEFT_HAND] or 0,
        gundam_right_arm_res = self.select_part_res_info_cache[MECHA_PART_TYPE.RIGHT_HAND] or 0,
        gundam_left_leg_res = self.select_part_res_info_cache[MECHA_PART_TYPE.LEFT_FOOT] or 0,
        gundam_right_leg_res = self.select_part_res_info_cache[MECHA_PART_TYPE.RIGHT_FOOT] or 0,
        gundam_left_wing_res = self.select_part_res_info_cache[MECHA_PART_TYPE.LEFT_WING] or 0,
        gundam_right_wing_res = self.select_part_res_info_cache[MECHA_PART_TYPE.RIGHT_WING] or 0,
    }

    if self:IsModelChange(part_info) then
        self.model_cache = part_info
        self.gundam_model:SetGundamModel(part_info)

        if not self.show_action then
            self.show_action = true
            self.gundam_model:PlayRoleShowAction()
        end
    end
end

function MechaArmamentView:IsModelChange(part_info)
    if IsEmptyTable(self.model_cache) then
        return true
    end

    for k, v in pairs(part_info) do
        if self.model_cache[k] ~= v then
            return true
        end
    end

    return false
end

function MechaArmamentView:OnClickPart(part_id, is_on)
    if is_on then
        self.select_part = part_id
        local data_list = MechaWGData.Instance:GetActivePartDataList(self.select_mechan_seq, part_id)
        self.mam_mecha_weapon_list:SetDataList(data_list)
        local has_data = not IsEmptyTable(data_list)
        self.node_list.flag_no_part_active:CustomSetActive(not has_data)

        if has_data then
            self.mam_mecha_weapon_list:JumpToIndex(self:CalMechaWeaponSelect(part_id, data_list))
        else
            self.node_list.desc_no_part_active.text.text = string.format(Language.Mecha.ArmamentNoPartTips,
                Language.Mecha.MechaPartName[part_id])
        end
    end
end

function MechaArmamentView:CalMechaWeaponSelect(part_id, data_list)
    local select_part_seq = self.select_part_info_cache[part_id] or -1
    if select_part_seq >= 0 then
        for k, v in pairs(data_list) do
            if select_part_seq == v.seq then
                return k
            end
        end
    end

    return 1
end

function MechaArmamentView:OnSelectMechaPartHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end

    local data = item.data
    self.select_part_info_cache[data.part] = data.seq
    self.select_part_res_info_cache[data.part] = data.res_id
    self:FlushModel()
    self:SetMamSaveBtnState()
end

function MechaArmamentView:SetMamSaveBtnState()
    -- 机甲是否激活
    if not MechaWGData.Instance:IsMechaActive(self.select_mechan_seq) then
        self.node_list.desc_mar_tips.text.text = Language.Mecha.ArnanentNotActiveTips
        self.node_list.flag_need_part:CustomSetActive(false)
        XUI.SetButtonEnabled(self.node_list.btn_mam_save, false)
        self.node_list.flag_is_fight:CustomSetActive(false)
        self:SetNeedPartAlphaTween(false)
        return
    end

    local state = true
    local desc_tips = ""

    for k, v in pairs(self.select_part_info_cache) do
        if k ~= MECHA_PART_TYPE.LEFT_WING and k ~= MECHA_PART_TYPE.RIGHT_WING then
            if v < 0 then
                state = false
                local douhao = desc_tips == "" and "" or ","
                desc_tips = desc_tips .. douhao .. Language.Mecha.MechaPartName[k]
                -- break
            end
        end
    end

    local has_change = false
    for k, v in pairs(self.select_part_info_cache) do
        local cur_wear_part_seq = MechaWGData.Instance:GetMechaPartWearPart(self.select_mechan_seq, k)

        if v >= 0 and cur_wear_part_seq ~= v then
            has_change = true
            break
        end
    end

    if desc_tips ~= "" then
        local content = string.format(Language.Mecha.ArnanentTips, ToColorStr(desc_tips, COLOR3B.RED))
        self.node_list.desc_mar_tips.text.text = content
    else
        self.node_list.desc_mar_tips.text.text = ""
    end

    local flag_is_fight = true
    local flag_need_part = false

    for k, v in pairs(self.select_part_info_cache) do
        if k ~= MECHA_PART_TYPE.LEFT_WING and k ~= MECHA_PART_TYPE.RIGHT_WING then
            local waer_seq = MechaWGData.Instance:GetMechaPartWearPart(self.select_mechan_seq, k)
            if waer_seq < 0 then
                flag_is_fight = false
                flag_need_part = true
                break
            end
        end
    end

    self:SetNeedPartAlphaTween(flag_need_part)
    self.node_list.flag_need_part:CustomSetActive(flag_need_part)
    self.node_list.flag_is_fight:CustomSetActive(flag_is_fight)
    XUI.SetButtonEnabled(self.node_list.btn_mam_save, state and has_change)
end

function MechaArmamentView:SetNeedPartAlphaTween(status)
    self:ClearNeedPartAlphaTween()

    if status then
        if not self.need_part_tween then
            local sequence = DG.Tweening.DOTween.Sequence()
            local show_tween = self.node_list.flag_need_part.canvas_group:DoAlpha(0, 1, 0.5)
            local fade_tween = self.node_list.flag_need_part.canvas_group:DoAlpha(1, 0, 0.5):SetDelay(0.5)
            sequence:Append(show_tween)
            sequence:Append(fade_tween)
            sequence:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
            self.need_part_tween = sequence
            -- self.need_part_tween = self.node_list.flag_need_part.canvas_group:DoAlpha(1, 0, 1)
            -- self.need_part_tween:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
        end
    end
end

function MechaArmamentView:ClearNeedPartAlphaTween()
    if self.need_part_tween then
        self.need_part_tween:Kill()
        self.need_part_tween = nil
    end
end

function MechaArmamentView:OnClickMamSave()
    if IsEmptyTable(self.select_part_info_cache) then
        return
    end

    for k, v in pairs(self.select_part_info_cache) do
        if k ~= MECHA_PART_TYPE.LEFT_WING and k ~= MECHA_PART_TYPE.RIGHT_WING then
            if v < 0 then
                SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Mecha.WearNoPartTip,
                    Language.Mecha.MechaPartName[k]))
                return
            end
        end
    end

    local params_list = {}
    local has_change = false
    for k, v in pairs(self.select_part_info_cache) do
        local cur_wear_part_seq = MechaWGData.Instance:GetMechaPartWearPart(self.select_mechan_seq, k)
        params_list[k] = v

        if v >= 0 and cur_wear_part_seq ~= v then
            has_change = true
        end
    end

    if has_change then
        MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.PUTON_PART, self.select_mechan_seq, nil, nil, params_list)
        TipWGCtrl.Instance:ShowEffect({ effect_type = UIEffectName.s_jihuo, is_success = true, pos = Vector2(0, 0) })
    end
end

function MechaArmamentView:OnClickMAMTip()
    RuleTip.Instance:SetContent(Language.Mecha.MANTIPContent, Language.Mecha.MANPTipTitle)
end

--------------------------------------MAMechaPartItemRender-----------------------------------
MAMechaPartItemRender = MAMechaPartItemRender or BaseClass(BaseRender)

function MAMechaPartItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local part_id = self.index
    local mechan_seq = self.data.mechan_seq
    self.node_list.desc_name.text.text = Language.Mecha.MechaPartName[part_id]
    local wear_part = MechaWGData.Instance:GetMechaPartWearPart(mechan_seq, part_id)
    local is_wear = wear_part >= 0
    local data_list = MechaWGData.Instance:GetActivePartDataList(mechan_seq, part_id)
    local has_part = not IsEmptyTable(data_list)
    self.node_list.flag_wear:CustomSetActive(is_wear)
    self.node_list.flag_no_wear:CustomSetActive(not is_wear and not has_part)
    self.node_list.flag_can_wear:CustomSetActive(not is_wear and has_part)
end

--------------------------------------MAMMechaWeaponItemRender-----------------------------------
MAMMechaWeaponItemRender = MAMMechaWeaponItemRender or BaseClass(BaseRender)

function MAMMechaWeaponItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_name.text.text = self.data.part_name
    self.node_list.desc_name_hl.text.text = self.data.part_name

    local bundle, asset = ResPath.GetMechaImg(self.data.show_icon)
    self.node_list.weapon_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.weapon_icon.image:SetNativeSize()
    end)

    local seq = self.data.seq
    local wear_seq = MechaWGData.Instance:GetMechaPartWearPart(self.data.mechan_seq, self.data.part)
    self.node_list.flag_is_wear:CustomSetActive(seq == wear_seq)
end

function MAMMechaWeaponItemRender:OnSelectChange(is_select)
    self.node_list.bg_nor:CustomSetActive(not is_select)
    self.node_list.select_hl:CustomSetActive(is_select)

    local pos_x = is_select and -25 or 25
    self.node_list.tween_root.rect:DOAnchorPosX(pos_x, 0.3)
end
