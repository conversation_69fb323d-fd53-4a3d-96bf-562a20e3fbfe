require("game/yiyuanhaoli/yiyuanhaoli_view")
require("game/yiyuanhaoli/yiyuanhaoli_wg_data")

YiYuanHaoLiWGCtrl = YiYuanHaoLiWGCtrl or BaseClass(BaseWGCtrl)

YiYuanHaoLiWGCtrl.FirstOpenActFlagKey = "FirstOpenActFlagKey"

function YiYuanHaoLiWGCtrl:__init()
	if YiYuanHaoLiWGCtrl.Instance then
        print_error("[YiYuanHaoLiWGCtrl]:Attempt to create singleton twice!")
	end
	YiYuanHaoLiWGCtrl.Instance = self

	self.data = YiYuanHaoLiWGData.New()
	self.view = YiYuanHaoLiView.New(GuideModuleName.YiYuanHaoLiView)
	self:RegisterAllProtocals()
end

function YiYuanHaoLiWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	self:DelDelayOpenViewTimer()
	self:DelCloseActTimer()

	YiYuanHaoLiWGCtrl.Instance = nil
end

function YiYuanHaoLiWGCtrl:RegisterAllProtocals()
	-- 注册协议
	self:RegisterProtocol(SConeMoneyBuyInfo, "OnSConeMoneyBuyInfo")
end

function YiYuanHaoLiWGCtrl:OnSConeMoneyBuyInfo(protocol)
	-- print_error("FFF============ OnSConeMoneyBuyInfo", protocol)
	self.data:OnSConeMoneyBuyInfo(protocol)

	local end_time = protocol.activity_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if end_time < server_time or self.data:GetYiYuanHaoLiIsBuy() then
        -- print_error("设置主界面一元豪礼按钮隐藏", protocol)
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.YIYUANHAOLI_ACTIVITY, ACTIVITY_STATUS.CLOSE)
        self:DelDelayOpenViewTimer()
        self:DelCloseActTimer()
    else
        local start_time = server_time
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.YIYUANHAOLI_ACTIVITY, ACTIVITY_STATUS.OPEN, end_time, start_time, end_time )

        --活动首次开放,弹界面
        local role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
        local safe_key = YiYuanHaoLiWGCtrl.FirstOpenActFlagKey .. role_id
		if PlayerPrefsUtil.GetInt(safe_key) ~= 1 then
			self:DelDelayOpenViewTimer()
			local cfg = self.data:GetYiYuanHaoLiCfg()
			local delay_time = cfg and cfg.surface_delay or 180
			-- (策划要求:延时打开界面)
			self.delay_open_view_timer = GlobalTimerQuest:AddDelayTimer(function()
				PlayerPrefsUtil.SetInt(safe_key, 1)
				self:OpenYiYuanHaoLi()
				self:DelDelayOpenViewTimer()
			end, delay_time)
		end

		self:SetCloseActTimer(end_time - start_time)
    end

    self:FlushYiYuanHaoLi()
end

function YiYuanHaoLiWGCtrl:OpenYiYuanHaoLi()
	if self.view and not self.view:IsOpen() then
		self.view:Open()
	end
end

function YiYuanHaoLiWGCtrl:FlushYiYuanHaoLi()
	if self.view and self.view:IsOpen() then
		self.view:Flush()
	end
end

function YiYuanHaoLiWGCtrl:TOpenAct(flag)
	local state = flag == 0 and ACTIVITY_STATUS.CLOSE or ACTIVITY_STATUS.OPEN
	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.YIYUANHAOLI_ACTIVITY, state)
end

function YiYuanHaoLiWGCtrl:OnGetYiYuanHaoLiReward(protocol)   --领取一元豪礼奖励返回(用于奖励展示)
	-- print_error("FFF==== 领取一元豪礼奖励返回(用于奖励展示)", protocol)
	if IsEmptyTable(protocol) then
		return
	end

	local cfg = self.data:GetYiYuanHaoLiCfg()
	if cfg and not IsEmptyTable(cfg.reward_list) then
		local show_list = SortDataByItemColor(cfg.reward_list)
		TipWGCtrl.Instance:ShowGetReward(nil, show_list, nil, nil, nil, true)
	end
end

function YiYuanHaoLiWGCtrl:DelDelayOpenViewTimer()
	if self.delay_open_view_timer then
		GlobalTimerQuest:CancelQuest(self.delay_open_view_timer)
		self.delay_open_view_timer = nil
	end
end

function YiYuanHaoLiWGCtrl:SetCloseActTimer(delay_time)
	if not delay_time or delay_time <= 0 then
		return
	end

	self:DelCloseActTimer()
	self.close_act_timer = GlobalTimerQuest:AddDelayTimer(function()
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.YIYUANHAOLI_ACTIVITY, ACTIVITY_STATUS.CLOSE)
		self:DelDelayOpenViewTimer()
	end, delay_time)
end

function YiYuanHaoLiWGCtrl:DelCloseActTimer()
	if self.close_act_timer then
		GlobalTimerQuest:CancelQuest(self.close_act_timer)
		self.close_act_timer = nil
	end
end