HiddenWeaponViewQuickRL = HiddenWeaponViewQuickRL or BaseClass(SafeBaseView)

function HiddenWeaponViewQuickRL:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(782, 500)})
    self:AddViewResource(0, "uis/view/shenji_anqiruanjia_ui_prefab", "shenji_equip_quick_rl")
    self:SetMaskBg(true)
end

function HiddenWeaponViewQuickRL:ReleaseCallBack()
    self:CancleShengPinTimes()
    if self.item_list then
        self.item_list:DeleteMe()
    end
    self.item_list = nil
    self.is_click_sure = false
end

function HiddenWeaponViewQuickRL:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ShenJiEquip.QuickEnhanceQuality
    self.item_list = AsyncListView.New(HiddenWeaponRLRender, self.node_list["ph_item_list"])
    XUI.AddClickEventListener(self.node_list["sure_btn"], BindTool.Bind(self.OnClickSure, self))
    self.node_list.tip_text.text.text = Language.ShenJiEquip.QuickDes
    self.node_list.lbl_tips.text.text =  Language.ShenJiEquip.QuickHasNoData
    self.node_list.all_mark.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickAllSelect, self))
end

function HiddenWeaponViewQuickRL:OnClickAllSelect(is_on)
    local item_list = HiddenWeaponWGData.Instance:GetRLBagList()
    if not is_on then
        HiddenWeaponWGData.Instance:ResetMutiSelect()
    else
        if #item_list > 0 then
            for i = 1, #item_list do
                HiddenWeaponWGData.Instance:SaveMutiSelect(i, true)
            end
        end
    end

    self:Flush()
end

function HiddenWeaponViewQuickRL:ShowIndexCallBack(index)
    HiddenWeaponWGData.Instance:ResetMutiSelect()
    --self.node_list.all_mark.toggle.isOn = true
    --self:OnClickAllSelect(true)
end

function HiddenWeaponViewQuickRL:OnClickSure()
    local muti_list = HiddenWeaponWGData.Instance:GetMutiSelect()
    local be_select_list = {}
    for k, v in pairs(muti_list) do
        if v then
            be_select_list[#be_select_list+1] = k
        end
    end
    if #be_select_list == 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenJiEquip.LimitOne)
        return
    end

    local upgrade_list = {}
    local data_list = HiddenWeaponWGData.Instance:GetRLBagList()

    for k,v in pairs(be_select_list) do
        local info = data_list[v]
        if not IsEmptyTable(info) then
            local list = {}
            list.meterials_list = info.material_index_list
            list.cur_index = info.cur_index
            list.count = #info.material_index_list
            if info.cur_item.cfg.is_dress then
                list.is_in_use = info.cur_item.cfg.equip.big_type
            else
                list.is_in_use = 0
            end

            if #upgrade_list < 50 then
                upgrade_list[#upgrade_list + 1] = list
            end
        end
    end

    HiddenWeaponRequest:ReqRLQuickUpgrade(upgrade_list)
    self.node_list.all_mark.toggle.isOn = false
    HiddenWeaponWGData.Instance:ResetMutiSelect()
end

function HiddenWeaponViewQuickRL:OnFlush(params)
    local data_list = HiddenWeaponWGData.Instance:GetRLBagList()
    self.item_list:SetDataList(data_list)
    self.node_list.img_no_record:SetActive(#data_list==0)
    --self.node_list.all_mark:SetActive(#data_list > 0)
end

function HiddenWeaponViewQuickRL:GetIsClickSure()
    return self.is_click_sure
end

function HiddenWeaponViewQuickRL:CancleShengPinTimes()
    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
    end
    self.is_play_effect = false
    self.delay_timer = nil
end

function HiddenWeaponViewQuickRL:PlayShengPinSuccess()
    if self.node_list.shengpin_success_root then
        if not self.is_play_effect then
            self.is_play_effect = true
            self.delay_timer = GlobalTimerQuest:AddDelayTimer(function()
                self.is_play_effect = false
                self.delay_timer = nil
                self.is_click_sure = false
            end, 1)
            self.node_list.shengpin_success_root:SetActive(true)
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengpin, is_success = true, pos = Vector2(0, 0),
                parent_node = self.node_list["shengpin_success_root"]})
        end
    end
end

-------------------------------------------------------------------------------------------
HiddenWeaponRLRender = HiddenWeaponRLRender or BaseClass(BaseRender)

function HiddenWeaponRLRender:__init(instance)

end

function HiddenWeaponRLRender:__delete()
    if self.cell_list then
        for k,v in pairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = nil
    end
    if self.target_item then
        self.target_item:DeleteMe()
    end
    self.target_item = nil
    if self.must_item then
        self.must_item:DeleteMe()
    end
    self.must_item = nil
end

function HiddenWeaponRLRender:LoadCallBack()
    self.cell_list = {}
    for i = 1, 4 do
        self.cell_list[i] = ItemCell.New(self.node_list["content"])
    end
    self.target_item = ItemCell.New(self.node_list["target_item"])
    self.must_item = ItemCell.New(self.node_list["must_item"])
    self.node_list.check_mark.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickToggle, self))
end

function HiddenWeaponRLRender:OnClickToggle(is_on)
    HiddenWeaponWGData.Instance:SaveMutiSelect(self.index, is_on)
end

function HiddenWeaponRLRender:OnFlush()
   -- self.node_list.index_num.text.text = self.data.cur_item.index
    local is_on = HiddenWeaponWGData.Instance:GetMutiSelectIndex(self.index)
    self.node_list.check_mark.toggle.isOn = is_on
    self.node_list.wear:SetActive(self.data.cur_item.cfg.is_dress)
    self.must_item:SetData(self.data.cur_item.cfg)
    local target_vo = HiddenWeaponWGData.Instance:GetComposeTargetEquip(self.data.cur_item.cfg)
    local cell_count = 0
    if not IsEmptyTable(target_vo) then
        self.target_item:SetData(target_vo)
        for i = 1, 4 do
            self.cell_list[i]:SetActive(false)
            --self.cell_list[i]:SetRightBottomTextVisible(false) 
        end

        local idx = 1 
        for k, v in pairs(self.data.material_item_list) do  
            cell_count = cell_count + 1
            local data = self.data.material_item_list[cell_count]
            if data then
                self.cell_list[idx]:SetData(v)
                --self.cell_list[idx]:SetRightBottomText(data.index)
                --self.cell_list[idx]:SetRightBottomTextVisible(true)
                self.cell_list[idx]:SetActive(true)
                idx = idx + 1
            end
            --UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.scroll.rect)
        end
    end
end