------------------------幻兽抽奖 start------------------------
--抽奖数据
SCBeastDrawItemInfo =  SCBeastDrawItemInfo or BaseClass(BaseProtocolStruct)
function  SCBeastDrawItemInfo:__init()
	self.msg_type = 16407
end

function SCBeastDrawItemInfo:Decode()
	self.draw_item_list = {}
	for i = 0, BEAST_DEFINE.MAX_DRAW_ITEM_COUNT do
		self.draw_item_list[i] = {}
		self.draw_item_list[i].draw_times = MsgAdapter.ReadInt()
		self.draw_item_list[i].lucky = MsgAdapter.ReadLL()
	end
end

--抽奖数据更新
SCBeastDrawItemUpdate =  SCBeastDrawItemUpdate or BaseClass(BaseProtocolStruct)
function  SCBeastDrawItemUpdate:__init()
	self.msg_type = 16408
end

function SCBeastDrawItemUpdate:Decode()
	self.type = MsgAdapter.ReadInt()
	self.draw_item_data = {}
	self.draw_item_data.draw_times = MsgAdapter.ReadInt()
	self.draw_item_data.lucky = MsgAdapter.ReadLL()
end

--抽奖结果
SCBeastDrawResult =  SCBeastDrawResult or BaseClass(BaseProtocolStruct)
function  SCBeastDrawResult:__init()
	self.msg_type = 16409
end

function SCBeastDrawResult:Decode()
	self.type = MsgAdapter.ReadInt()
	self.mode = MsgAdapter.ReadInt()
	self.baodi_item_id = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()

	self.result_item_list = {}
	for i = 1, self.count do
		self.result_item_list[i] = {}
		self.result_item_list[i].item_id = MsgAdapter.ReadInt()
		self.result_item_list[i].bag_index = MsgAdapter.ReadInt()
	end
end

------------------------幻兽抽奖 end------------------------

------------------------委托任务 start------------------------
--历练值更新
SCConsignationTaskExp = SCConsignationTaskExp or BaseClass(BaseProtocolStruct)
function SCConsignationTaskExp:__init()
    self.msg_type = 16411
end

function SCConsignationTaskExp:Decode()
	self.exp = MsgAdapter.ReadInt()
	-- print_error("SCConsignationTaskExp", self.exp)
end


local function GetAssignRentData()--上架外形信息
	local type = MsgAdapter.ReadChar()
	local status = MsgAdapter.ReadChar()
	local seq = MsgAdapter.ReadShort()
	local begin_time_stamp = MsgAdapter.ReadUInt()
	local self_index = MsgAdapter.ReadChar()
	local is_my_fashion = MsgAdapter.ReadChar()
	local guild_index = MsgAdapter.ReadShort()


	local data = AssignmentWGData.GetFashionDataModel()
	data.cfg = AssignmentWGData.Instance:GetAssignFashionCfg(type, seq)
	data.status = status
	data.begin_time_stamp = begin_time_stamp
	data.self_index = self_index
	data.is_my_fashion = is_my_fashion
	data.guild_index = guild_index
	return data
end

local function GetAssignFashionData()--派遣外形信息

	local type = MsgAdapter.ReadChar()
	local tmp = MsgAdapter.ReadChar()
	local seq = MsgAdapter.ReadShort()   -- 幻兽类型是背包序号
	local item_id = MsgAdapter.ReadInt()

	local data = AssignmentWGData.GetFashionDataModel()
	-- 幻兽得背包数据是在这个的后面下发的，这个已经无效了
	data.type = type
	data.tmp = tmp
	data.seq = seq
	data.item_id = item_id -- 出任务得幻兽id

	if type == 4 then
		data.cfg = AssignmentWGData.Instance:GetAssignFashionCfg(type, data.item_id)
	else
		data.cfg = AssignmentWGData.Instance:GetAssignFashionCfg(type, seq)
	end

	return data
end

local function GetAssignTaskDataItem()--派遣任务信息
	local task_data = AssignmentWGData.GetTaskDataModel()
	task_data.task_id = MsgAdapter.ReadChar()
	local my_assign_count = MsgAdapter.ReadChar()
	local borrow_assign_count = MsgAdapter.ReadChar()
	local temp = MsgAdapter.ReadChar()
	task_data.begin_time_stamp = MsgAdapter.ReadUInt() --任务派遣时间戳 <=0表示未派遣 >0表示已派遣

	--租借外形信息
	task_data.brrow_assign_list = {}
	local max_borrow_count = 4
	for i = 1, max_borrow_count do
		local data = GetAssignFashionData()
		data.status = AssignmentWGData.GUILD_ASSIGN_STATUS.RENT_ASSIGN
		table.insert(task_data.brrow_assign_list, data)
	end
	--自己已派遣的外形列表索引
	task_data.my_assign_index_list = {}
	local max_assign_count = 8
	for i = 1, max_assign_count do
		local index = MsgAdapter.ReadChar()
		table.insert(task_data.my_assign_index_list, index)
	end

	return task_data
end

--单个任务更新
SCConsignationTaskSignleTaskInfo = SCConsignationTaskSignleTaskInfo or BaseClass(BaseProtocolStruct)
function SCConsignationTaskSignleTaskInfo:__init()
    self.msg_type = 16412
end

function SCConsignationTaskSignleTaskInfo:Decode()
	self.task_data = GetAssignTaskDataItem()

	self.used_borrow_times = MsgAdapter.ReadChar()

	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
end

--刷新任务信息
SCConsignationTaskRefreshInfo = SCConsignationTaskRefreshInfo or BaseClass(BaseProtocolStruct)
function SCConsignationTaskRefreshInfo:__init()
    self.msg_type = 16413
end

function SCConsignationTaskRefreshInfo:Decode()
	self.used_free_refresh_time = MsgAdapter.ReadUChar()

	-- print_error("16413")
	-- print_error(self.used_free_refresh_time)
	self.task_data_list = {}
	local max_consignation_task_num = 15 -- 最大任务数量
	for i = 1, max_consignation_task_num do
		table.insert(self.task_data_list , GetAssignTaskDataItem())
	end

	-- print_error(self.task_data_list)
end

--委托任务总信息
SCConsignationTaskAllInfo = SCConsignationTaskAllInfo or BaseClass(BaseProtocolStruct)
function SCConsignationTaskAllInfo:__init()
    self.msg_type = 16414
end

function SCConsignationTaskAllInfo:Decode()
	self.used_free_refresh_time = MsgAdapter.ReadChar()
	self.used_borrow_times = MsgAdapter.ReadChar()

	local task_count = MsgAdapter.ReadChar()
	self.current_special_task_id = MsgAdapter.ReadChar()

	self.task_data_list = {}
	local max_task_count = 25
	for i = 1, max_task_count do
		local item = GetAssignTaskDataItem()
		if task_count >= i and item.task_id >= 0 then
			table.insert(self.task_data_list, item)
		end
	end

	self.my_assign_data_list = {}
	local max_self_assign_num = 75 --最大派遣外形总数
	for i = 0, max_self_assign_num - 1 do
		local data = GetAssignFashionData()
		data.is_my_fashion = 1
		self.my_assign_data_list[i] =  data
	end
end

--单个上架信息更新
SCConsignationTaskSingleRentExteriorInfo = SCConsignationTaskSingleRentExteriorInfo or BaseClass(BaseProtocolStruct)
function SCConsignationTaskSingleRentExteriorInfo:__init()
    self.msg_type = 16415
end

function SCConsignationTaskSingleRentExteriorInfo:Decode()
	self.rent_data = GetAssignTaskDataItem()
end

-- 委托任务-客户端操作
CSConsignationTaskClientReq = CSConsignationTaskClientReq or BaseClass(BaseProtocolStruct)
function CSConsignationTaskClientReq:__init()
	self.msg_type = 16416
end

function CSConsignationTaskClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)

	local param_max = 8
	for i = 1, param_max do
		MsgAdapter.WriteChar(self.param_list1[i] or -1)
	end

	for i = 1, param_max do
		MsgAdapter.WriteShort(self.param_list2[i] or -1)
	end

	-- print_error("16416", self.operate_type, self.param1)
	-- print_error(self.param_list1)
	-- print_error(self.param_list2)
end

-- 客户端外形操作
CSGuildExteriorClientReq = CSGuildExteriorClientReq or BaseClass(BaseProtocolStruct)
function CSGuildExteriorClientReq:__init()
	self.msg_type = 16417
end

function CSGuildExteriorClientReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)

	-- print_error("16417", self.operate_type, self.param1, self.param2)
end

-- 自己的上架信息
SCConsignationTaskSelfRentExteriorInfo = SCConsignationTaskSelfRentExteriorInfo or BaseClass(BaseProtocolStruct)
function SCConsignationTaskSelfRentExteriorInfo:__init()
	self.msg_type = 16418
end

function SCConsignationTaskSelfRentExteriorInfo:Decode()
	self.my_rent_data_list = {}
	local rent_count = MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()

	for i = 1, rent_count do
		local rent_data = GetAssignRentData()
		rent_data.is_my_fashion = 1
		table.insert(self.my_rent_data_list, rent_data)
		--print_error(rent_data)
	end

	-- print_error(self.my_rent_data_list)
end

-- 仙盟上架信息
SCConsignationTaskAllRentExteriorInfo = SCConsignationTaskAllRentExteriorInfo or BaseClass(BaseProtocolStruct)
function SCConsignationTaskAllRentExteriorInfo:__init()
	self.msg_type = 16419
end

function SCConsignationTaskAllRentExteriorInfo:Decode()
	self.all_rent_data_list = {}
	local rent_count = MsgAdapter.ReadShort()
	MsgAdapter.ReadChar()
	MsgAdapter.ReadChar()

	for i = 1, rent_count do
		local rent_data = GetAssignRentData()
		rent_data.owner_name = MsgAdapter.ReadName()
		table.insert(self.all_rent_data_list, rent_data)
	end

	--print_error(rent_count, #self.all_rent_data_list)
end

-- 仙盟上架雇佣记录
SCConsignationTaskAllRentAndBorrowRecord = SCConsignationTaskAllRentAndBorrowRecord or BaseClass(BaseProtocolStruct)
function SCConsignationTaskAllRentAndBorrowRecord:__init()
	self.msg_type = 16420
end

function SCConsignationTaskAllRentAndBorrowRecord:Decode()
	self.all_log_list = {}
	local count = MsgAdapter.ReadInt()
	-- print_error(count)
	for i = 1, count do
		local data = {}
		data.name = MsgAdapter.ReadName()--发起操作的玩家名
		data.flag = MsgAdapter.ReadChar()--0:上架 1:雇佣
		data.type = MsgAdapter.ReadChar()--外形type
		data.seq = MsgAdapter.ReadShort()--外形seq
		data.time_stamp = MsgAdapter.ReadUInt()

		table.insert(self.all_log_list, data)
	end
	-- print_error(16420, self.all_log_list)
end

-- 玩家雇佣记录
SCConsignationTaskPlayerRentAndBorrowRecord = SCConsignationTaskPlayerRentAndBorrowRecord or BaseClass(BaseProtocolStruct)
function SCConsignationTaskPlayerRentAndBorrowRecord:__init()
	self.msg_type = 16421
end

function SCConsignationTaskPlayerRentAndBorrowRecord:Decode()
	self.all_log_list = {}
	local count = MsgAdapter.ReadInt()

	for i = 1, count do
		local data = {}
		data.name = MsgAdapter.ReadName()
		data.flag = MsgAdapter.ReadChar()--最低位[0,自己雇佣别人的,name是外形主人的名字:1,别人雇佣自己的,name是雇佣者的name]
		data.type = MsgAdapter.ReadChar()
		data.seq = MsgAdapter.ReadShort()
		data.time_stamp = MsgAdapter.ReadUInt()

		table.insert(self.all_log_list, data)
	end
	-- print_error(16421, self.all_log_list)
end


-- 完成任务奖励信息
SCConsignationTaskFetchRewardInfo = SCConsignationTaskFetchRewardInfo or BaseClass(BaseProtocolStruct)
function SCConsignationTaskFetchRewardInfo:__init()
	self.msg_type = 16422
end

function SCConsignationTaskFetchRewardInfo:Decode()
	self.reward_list = {}
	self.borrow_reward_id_list = {}
	local count = MsgAdapter.ReadInt()
	local borrow_begin_index = MsgAdapter.ReadInt() + 1
	-- local max_count = 20
	for i = 1, count do
		local data = {}
		data.item_id = MsgAdapter.ReadItemId()
		data.is_bind = MsgAdapter.ReadShort()
		data.num = MsgAdapter.ReadInt()
		if i < borrow_begin_index then
			table.insert(self.reward_list, data)
		else
			for j = 1, data.num do
				table.insert(self.borrow_reward_id_list, data.item_id)
			end
		end
	end
end
------------------------委托任务 end------------------------

------------------------永夜幻都数据 start------------------------
-- 永夜幻都数据最快通关数据
SCXiuLuoTaFastPassInfo = SCXiuLuoTaFastPassInfo or BaseClass(BaseProtocolStruct)
function SCXiuLuoTaFastPassInfo:__init()
	self.msg_type = 16423
end

function SCXiuLuoTaFastPassInfo:Decode()
	self.fast_pass_user_name = MsgAdapter.ReadStrN(32)
	self.server_id = MsgAdapter.ReadLL()
	self.fast_pass_time = MsgAdapter.ReadUInt()
	self.my_fast_pass_time = MsgAdapter.ReadUInt()
end

-- 永夜幻都当前层人数信息
SCXiuLuoTaLayerInfo = SCXiuLuoTaLayerInfo or BaseClass(BaseProtocolStruct)
function SCXiuLuoTaLayerInfo:__init()
	self.msg_type = 16424
end

function SCXiuLuoTaLayerInfo:Decode()
	self.cur_layer_role_num = MsgAdapter.ReadInt()						--当前层玩家的数量
end
------------------------永夜幻都数据 end------------------------

---------------------------- 领主商店 Start ----------------------------
SCEveryDayLingzhuShopInfo = SCEveryDayLingzhuShopInfo or BaseClass(BaseProtocolStruct)

function SCEveryDayLingzhuShopInfo:__init()
    self.msg_type = 16432
end

function SCEveryDayLingzhuShopInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.every_day_lingzhu_shop_buy = {}
	for i = 0, 63 do
		self.every_day_lingzhu_shop_buy[i] = MsgAdapter.ReadChar()
	end

	self.every_day_lingzhu_shop_choose = bit:ll2b_two(MsgAdapter.ReadUInt(), MsgAdapter.ReadUInt())
end

CSEveryDayLingzhuShopOperate = CSEveryDayLingzhuShopOperate or BaseClass(BaseProtocolStruct)
function CSEveryDayLingzhuShopOperate:__init()
    self.msg_type = 16433
end

function CSEveryDayLingzhuShopOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
end
---------------------------- 领主商店 End ----------------------------

------------------------转职 Start------------------------
-- 客户端请求
CSRoleZhuanZhiOper = CSRoleZhuanZhiOper or BaseClass(BaseProtocolStruct)
function CSRoleZhuanZhiOper:__init()
	self.msg_type = 16434
end

function CSRoleZhuanZhiOper:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)

	-- print_error("16432", self.operate_type, self.param1, self.param2, self.param3)
end

local function GetRoleZhuanZhiTask()--转职任务信息
	local task_data = {}
	task_data.task_seq = MsgAdapter.ReadInt() --任务参数
	MsgAdapter.ReadShort() --对齐
	task_data.has_fetched_reward = MsgAdapter.ReadChar() == 1 --领取奖励标志
	task_data.is_complete = MsgAdapter.ReadChar() == 1 --判断是否完成
	task_data.progress = MsgAdapter.ReadLL() --任务参数
	return task_data
end

SCRoleZhuanZhiTaskInfo = SCRoleZhuanZhiTaskInfo or BaseClass(BaseProtocolStruct)
function SCRoleZhuanZhiTaskInfo:__init()
    self.msg_type = 16435
end

function SCRoleZhuanZhiTaskInfo:Decode()
	self.count = MsgAdapter.ReadInt()					-- 进入次数
	self.task_info_list = {}
	for i = 0, self.count - 1 do
		local task_data = GetRoleZhuanZhiTask()
		self.task_info_list[task_data.task_seq] = task_data
	end
end

SCRoleZhuanZhiBaseInfo = SCRoleZhuanZhiBaseInfo or BaseClass(BaseProtocolStruct)
function SCRoleZhuanZhiBaseInfo:__init()
    self.msg_type = 16436
end

function SCRoleZhuanZhiBaseInfo:Decode()
	self.light_level = MsgAdapter.ReadUInt()					-- 点亮等级
	
	self.god_and_demons_type = MsgAdapter.ReadChar()
	-- print_error("16436 god_and_demons_type:",self.god_and_demons_type)
	self.reserve_ch = MsgAdapter.ReadChar()
	self.reserve_sh = MsgAdapter.ReadShort()

end

------------------------转职 end------------------------



------------------------------------------限时直购礼包Start---------------------------
--限时直购礼包单个信息
SPopupGiftUpdateInfo =  SPopupGiftUpdateInfo or BaseClass(BaseProtocolStruct)
function  SPopupGiftUpdateInfo:__init()
	self.msg_type = 16402
end

function SPopupGiftUpdateInfo:Decode()
	self.index = MsgAdapter.ReadInt()
	self.open_day_grade_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)		
	self.popupgift = ProtocolStruct.ReadPopGift()
end
------------------------------------------限时直购礼包End---------------------------

---------------------------- 天玑迷城 Start ----------------------------
CSCrossNightFightOperate = CSCrossNightFightOperate or BaseClass(BaseProtocolStruct)
function CSCrossNightFightOperate:__init()
    self.msg_type = 16403
end

function CSCrossNightFightOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
	MsgAdapter.WriteInt(self.param4)
end

SCCrossNightFightInfo = SCCrossNightFightInfo or BaseClass(BaseProtocolStruct)
function SCCrossNightFightInfo:__init()
    self.msg_type = 16404
end

function SCCrossNightFightInfo:Decode()
	self.enter_count = MsgAdapter.ReadInt()					-- 进入次数
	local reward_flag = MsgAdapter.ReadInt()				-- 次数奖励标记
	self.count_reward_flag = bit:d2b_l2h(reward_flag, nil, true)			
end
---------------------------- 天玑迷城 end ----------------------------

---------------------------- 战力福利 Start ----------------------------
SCCapabilityWealAllInfo = SCCapabilityWealAllInfo or BaseClass(BaseProtocolStruct)
function SCCapabilityWealAllInfo:__init()
    self.msg_type = 16405
end

function SCCapabilityWealAllInfo:Decode()
	self.end_timestamp = MsgAdapter.ReadUInt()				-- 活动结束时间戳
	self.day = MsgAdapter.ReadShort()						-- 当前天数
	self.final_reward_fetch_flag = MsgAdapter.ReadChar()	-- 最终奖励领取标记
	self.is_buy_tequan = MsgAdapter.ReadChar()				-- 是否购买特权
	self.total_capability = MsgAdapter.ReadLL()				-- 总战力
	self.fetch_reward_flag = {} -- 领取标记
	for i = 0, 3 do
		local u_int = MsgAdapter.ReadUInt()
		local tab = bit:d2b_two(u_int)
		for j = 0, 31 do
			self.fetch_reward_flag[i * 32 + j] = tab[j]
		end
	end
end

CSCapabilityWealFetchReward = CSCapabilityWealFetchReward or BaseClass(BaseProtocolStruct)
function CSCapabilityWealFetchReward:__init()
    self.msg_type = 16406
end

function CSCapabilityWealFetchReward:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param_1)
	MsgAdapter.WriteInt(self.param_2)
	MsgAdapter.WriteInt(self.param_3)
end
---------------------------- 战力福利 end ----------------------------

-----------------------三倍充值Start-----------------------
SCOATripleRechargeInfo = SCOATripleRechargeInfo or BaseClass(BaseProtocolStruct)
function SCOATripleRechargeInfo:__init()
    self.msg_type = 16431
end

function SCOATripleRechargeInfo:Decode()
	self.grade = MsgAdapter.ReadInt()
	self.buy_flag = MsgAdapter.ReadInt()
end
-----------------------三倍充值End-----------------------

---------------------------- boss每日秒杀 Start ----------------------------
SCEveryDayKillBossInfo = SCEveryDayKillBossInfo or BaseClass(BaseProtocolStruct)

function SCEveryDayKillBossInfo:__init()
    self.msg_type = 16437
end

function SCEveryDayKillBossInfo:Decode()
    self.level = MsgAdapter.ReadUShort()
	self.day_count = MsgAdapter.ReadUShort()
	self.condition_num = MsgAdapter.ReadUInt()
	self.kill_boss_open_flag = MsgAdapter.ReadChar()
	self.auto_kill_boss_flag = MsgAdapter.ReadChar()
	self.ext_count = MsgAdapter.ReadUShort()
	self.ext_emp_count = MsgAdapter.ReadUShort()
	local reserver_short = MsgAdapter.ReadUShort()
end

CSEveryDayKillBossOpenOperate = CSEveryDayKillBossOpenOperate or BaseClass(BaseProtocolStruct)
function CSEveryDayKillBossOpenOperate:__init()
    self.msg_type = 16438
end

function CSEveryDayKillBossOpenOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.open_flag)
end

CSEveryDayKillBossOpenAutoKillOperate = CSEveryDayKillBossOpenAutoKillOperate or BaseClass(BaseProtocolStruct)
function CSEveryDayKillBossOpenAutoKillOperate:__init()
    self.msg_type = 16474
end

function CSEveryDayKillBossOpenAutoKillOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.auto_flag)
end

---------------------------boss每日秒杀 end----------------------------

---------------------------- 时装兑换商店 Start ----------------------------
CSShapeShopOperate = CSShapeShopOperate or BaseClass(BaseProtocolStruct)
function CSShapeShopOperate:__init()
    self.msg_type = 16439
end

function CSShapeShopOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.opera_type)
	MsgAdapter.WriteInt(self.param1)
	MsgAdapter.WriteInt(self.param2)
	MsgAdapter.WriteInt(self.param3)
end

SCShapeShopInfo = SCShapeShopInfo or BaseClass(BaseProtocolStruct)
function SCShapeShopInfo:__init()
    self.msg_type = 16440
end

function SCShapeShopInfo:Decode()
	local count = MsgAdapter.ReadInt()

	local shop_item_list = {}
	for i = 1, count do
		shop_item_list[i] = {}
		shop_item_list[i].seq = MsgAdapter.ReadInt()
		shop_item_list[i].day_buy_count = MsgAdapter.ReadInt()		--每日已购买次数.
		shop_item_list[i].life_buy_count = MsgAdapter.ReadInt()	--终身已购买次数.
		shop_item_list[i].week_buy_count = MsgAdapter.ReadInt()		--每日已购买次数.
		shop_item_list[i].month_buy_count = MsgAdapter.ReadInt()	--终身已购买次数.
	end

	self.shop_item_list = shop_item_list
end

SCShapeShopUpdateInfo = SCShapeShopUpdateInfo or BaseClass(BaseProtocolStruct)
function SCShapeShopUpdateInfo:__init()
    self.msg_type = 16441
end

function SCShapeShopUpdateInfo:Decode()
	self.shop_type = MsgAdapter.ReadInt()

	self.shop_item = {}
	self.shop_item.seq = MsgAdapter.ReadInt()
	self.shop_item.day_buy_count = MsgAdapter.ReadInt()
	self.shop_item.life_buy_count = MsgAdapter.ReadInt()
	self.shop_item.week_buy_count = MsgAdapter.ReadInt()		--每日已购买次数.
	self.shop_item.month_buy_count = MsgAdapter.ReadInt()	    --终身已购买次数.
end
---------------------------时装兑换商店 end----------------------------

---------------------------------------强制关闭功能 start---------------------
SCSystemOpenLimitInfo = SCSystemOpenLimitInfo or BaseClass(BaseProtocolStruct)
function SCSystemOpenLimitInfo:__init()
    self.msg_type = 16442
end

function SCSystemOpenLimitInfo:Decode()
	local count = MsgAdapter.ReadInt()

	self.enforce_close_list = {}
	for i = 0, count - 1 do
		self.enforce_close_list[i] = MsgAdapter.ReadInt()
	end

end

SCSystemOpenLimitUpdate = SCSystemOpenLimitUpdate or BaseClass(BaseProtocolStruct)
function SCSystemOpenLimitUpdate:__init()
    self.msg_type = 16443
end

function SCSystemOpenLimitUpdate:Decode()
	self.is_open = MsgAdapter.ReadInt()
	self.system_type = MsgAdapter.ReadInt()
end


---------------------------------------强制关闭功能 end---------------------



---------------------------------------幻梦秘境副本 start---------------------

-- 副本信息
SCOADreamSecretBaseInfo = SCOADreamSecretBaseInfo or BaseClass(BaseProtocolStruct)
function SCOADreamSecretBaseInfo:__init()
	self.msg_type = 16426
end

function SCOADreamSecretBaseInfo:Decode()
	self.grade = MsgAdapter.ReadInt()								-- 档次
	self.max_pass_wave = MsgAdapter.ReadInt()						-- 最大通关数
	self.wave_pass_reward_flag = bit:d2b_two(MsgAdapter.ReadInt())	-- 副本通关波数信息
	self.harm_value = MsgAdapter.ReadLL()							-- 伤害值
end

SCOADreamSecretSceneInfo = SCOADreamSecretSceneInfo or BaseClass(BaseProtocolStruct)
function SCOADreamSecretSceneInfo:__init()
	self.msg_type = 16427
end

function SCOADreamSecretSceneInfo:Decode()
	self.grade = MsgAdapter.ReadInt()							-- 档次
	self.wave = MsgAdapter.ReadInt()							-- 副本波数
	self.is_new_record = MsgAdapter.ReadChar()					-- 是否新纪录
	self.is_end = MsgAdapter.ReadChar()							-- 当前波数是否结束
	self.is_pass = MsgAdapter.ReadChar()						-- 当前波数是否通过
	self.buy_time_count = MsgAdapter.ReadUChar()				-- 时间购买次数
	self.wave_end_time = MsgAdapter.ReadUInt()					-- 波数结束时间
	self.kick_out_time = MsgAdapter.ReadUInt()					-- 踢出时间
	self.fb_end_time = MsgAdapter.ReadUInt()					-- 副本结束时间
	self.harm_value = MsgAdapter.ReadLL()						-- 实时伤害
	self.monster_count = MsgAdapter.ReadInt()					-- 当前波次剩余怪物数量
	self.info_type = MsgAdapter.ReadInt()						-- 类型 0：刷新 1：通过波次 2：购买时间
end

-- 排行信息
SCOADreamSecretRankInfo = SCOADreamSecretRankInfo or BaseClass(BaseProtocolStruct)
function SCOADreamSecretRankInfo:__init()
	self.msg_type = 16428
end

function SCOADreamSecretRankInfo:Decode()
	self.self_rank = MsgAdapter.ReadInt()
	self.rank_item_list = {}

    for i = 1, 20 do
		local info = {}
		info.uid = MsgAdapter.ReadInt()						-- 玩家uid
		info.name = MsgAdapter.ReadStrN(32)					-- 玩家名字
		info.sex = MsgAdapter.ReadChar()					-- 性别
		MsgAdapter.ReadChar()					-- 职业
		info.is_hide_vip = MsgAdapter.ReadChar() 	
		info.rank = MsgAdapter.ReadChar()
		info.avatar = MsgAdapter.ReadLL()					-- 头像
		info.vip_level = MsgAdapter.ReadInt()				-- vip等级
		info.usid = MsgAdapter.ReadUniqueServerID()			-- 服务器id
		info.start_time = MsgAdapter.ReadUInt()				-- 挑战时间
		info.harm_value = MsgAdapter.ReadLL()				-- 伤害值
		info.prof = MsgAdapter.ReadInt()					-- 职业
		self.rank_item_list[i] = info
	end
end

-- 卡牌信息
SCOADreamSecretCardInfo = SCOADreamSecretCardInfo or BaseClass(BaseProtocolStruct)
function SCOADreamSecretCardInfo:__init()
	self.msg_type = 16429
end

function SCOADreamSecretCardInfo:Decode()
	self.card_count = MsgAdapter.ReadInt()						--当前卡牌数量
	self.card_item_list = {}

	for i = 1, self.card_count do
		local card_item = ProtocolStruct.ReadMsgCardItem()
		table.insert(self.card_item_list, card_item)
	end
end

SCOADreamSecretSkillInfo = SCOADreamSecretSkillInfo or BaseClass(BaseProtocolStruct)
function SCOADreamSecretSkillInfo:__init()
	self.msg_type = 16430
end

function SCOADreamSecretSkillInfo:Decode()
	self.skill_power = MsgAdapter.ReadInt()						--技能能量
end
---------------------------------------幻梦秘境副本 end---------------------

------------------------ 累充豪礼 start ------------------------
SCCumulateRechargeGiftInfo = SCCumulateRechargeGiftInfo or BaseClass(BaseProtocolStruct)
function SCCumulateRechargeGiftInfo:__init()
    self.msg_type = 16472
end

function SCCumulateRechargeGiftInfo:Decode()
	self.info_round 		= MsgAdapter.ReadChar() 	-- 当前轮次
	self.info_day 			= MsgAdapter.ReadChar() 	-- 当前天数
	self.fetch_flag_list = {}
	for i = 0, 8 do
		local fetch_flag 		= bit:d2b_two(MsgAdapter.ReadShort())	-- 领取标识 16位
		self.fetch_flag_list[i] = fetch_flag
	end
	self.recharge_num 		= MsgAdapter.ReadInt()		-- 今日充值额度
end

CSCumulateRechargeGiftReq = CSCumulateRechargeGiftReq or BaseClass(BaseProtocolStruct)
function CSCumulateRechargeGiftReq:__init()
    self.msg_type = 16473
end

function CSCumulateRechargeGiftReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
end

CSVirualGold2Req = CSVirualGold2Req or BaseClass(BaseProtocolStruct)

function CSVirualGold2Req:__init()
	self.msg_type = 16471
end

function CSVirualGold2Req:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
	MsgAdapter.WriteInt(self.param1)
end

------------------------ 累充豪礼  end  -----------------------

SCVipRoleExpInfo =  SCVipRoleExpInfo or BaseClass(BaseProtocolStruct)
function  SCVipRoleExpInfo:__init()
	self.msg_type = 16410
end

function SCVipRoleExpInfo:Decode()
	self.get_exp_flag = MsgAdapter.ReadInt()
end