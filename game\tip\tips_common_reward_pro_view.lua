--灵核概率展示面板
TipsRewardProView = TipsRewardProView or BaseClass(SafeBaseView)

function TipsRewardProView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_draw_pro_panel")
    self:SetMaskBg(true, true)
end

-- 设置属性值
function TipsRewardProView:SetData(info)
	self.info = info
    self:Open()
end

function TipsRewardProView:ReleaseCallBack()
    if self.draw_pro_list then
        self.draw_pro_list:DeleteMe()
        self.draw_pro_list = nil
    end
end

function TipsRewardProView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TianShenLingHe.ProbabilityTitle
     if not self.draw_pro_list then
        self.draw_pro_list = AsyncListView.New(CommonRewardProItemRender, self.node_list.draw_pro_list) 
    end
end

function TipsRewardProView:OnFlush()
    if not self.info then
        return
    end

    self.draw_pro_list:SetDataList(self.info)
end

----------------------------------------------------------------------------------
CommonRewardProItemRender = CommonRewardProItemRender or BaseClass(BaseRender)

function CommonRewardProItemRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = string.format("%s%%", self.data.random_count)
end
