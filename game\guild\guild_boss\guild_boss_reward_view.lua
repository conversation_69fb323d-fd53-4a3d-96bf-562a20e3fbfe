GuildBossRewardView = GuildBossRewardView or BaseClass(SafeBaseView)

function GuildBossRewardView:__init()
	self:AddViewResource(0, "uis/view/guild_boss_prefab", "layout_guild_boss_reward")
	self.view_layer = UiLayer.Normal
	self.is_safe_area_adapter = true

end

function GuildBossRewardView:__delete()
end

function GuildBossRewardView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	self.rect_target = nil
	self.load_complete = nil
	self.is_should_forward = nil
	self.is_should_backward = nil

	if self.forward_tween then
		self.forward_tween:Kill()
		self.forward_tween = nil
	end
	if self.backwards_tween then
		self.backwards_tween:Kill()
		self.backwards_tween = nil
	end

	if self.item_cell then
       self.item_cell:DeleteMe()
       self.item_cell = nil
    end
    self.cur_tz_round = 0
    self.is_tz_ing = false

    -- if self.num_list1 then
    --     self.num_list1:DeleteMe()
    --     self.num_list1 = nil
    -- end

    -- if self.num_list2 then
    --     self.num_list2:DeleteMe()
    --     self.num_lsit2 = nil
    -- end

	if self.scroll_tween1 then
		self.scroll_tween1:Kill()
		self.scroll_tween1 = nil
	end

	if self.scroll_tween2 then
		self.scroll_tween2:Kill()
		self.scroll_tween2 = nil
	end 

	if self.scroll_tween3 then
		self.scroll_tween3:Kill()
		self.scroll_tween3 = nil
	end

	if self.scroll_tween4 then
		self.scroll_tween4:Kill()
		self.scroll_tween4 = nil
	end
	self.shaizi_num = nil
end

local NumData = {[1] = 0,[2] = 1,[3] = 2,[4] = 3,[5] = 4,[6] = 5,[7] = 6,[8] = 7,[9] = 8,[10] = 9}
function GuildBossRewardView:LoadCallBack()

	XUI.AddClickEventListener(self.node_list.xmss_fq_btn, BindTool.Bind(self.OnClickFQBtn,self))
	XUI.AddClickEventListener(self.node_list.xmss_tz_btn, BindTool.Bind(self.OnClickTZBtn,self))
	-- self.num_list1 = AsyncListView.New(RandomNum,self.node_list["num_list1"])
	-- self.num_list2 = AsyncListView.New(RandomNum,self.node_list["num_list2"])
	-- self.num_list1:SetDataList(NumData)
	-- self.num_list2:SetDataList(NumData)

	self.item_cell = ItemCell.New(self.node_list["itemcell"])
	self.item_cell:SetItemTipFrom(ItemTip.FROM_BAG)

	self.cur_tz_round = 0
	self.is_tz_ing = false

	self.boss_shaizi_rect_target = self.node_list["boss_shaizi"]
	self.boss_shaizi_rect_target:SetActive(false)

	self.reward_list = AsyncListView.New(GuildBossRewardItem,self.node_list.ph_guild_list)

	self.rect_target = self.node_list["layout_guild_answer_root"].rect
	self.rect_target.gameObject:SetActive(false)

	self.load_complete = true

	 if self.is_should_forward then
 		self:DOPlayForward()
 		self.is_should_forward = nil
 	end
	if self.is_should_backward then
		self:PlayBackwards()
		self.is_should_backward = nil
	end
end

function GuildBossRewardView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("guild_boss_shaizi") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_shaizi")
	end
    if self.tween_complete_callBack then
        self.tween_complete_callBack = nil
    end
end

function GuildBossRewardView:OpenCallBack()
	if not self.load_complete then
		self.is_should_forward = true 
		return
	end
	self.is_open = true
	self:DOPlayForward()
end

function GuildBossRewardView:OnFlush()
    local data_list = GuildBossWGData.Instance:GetDiceRecordList()
    table.sort(data_list, SortTools.KeyUpperSorter("round"))
	self.reward_list:SetDataList(data_list)
end

function GuildBossRewardView:ShowIndexCallBack()
	self:ShowBossShaiZi()
end

function GuildBossRewardView:ShowBossShaiZi()
	local mydamage_list = GuildBossWGData.Instance:GetMyGuildBossPersonRankInfo()
	local hurt_val = mydamage_list.hurt_val or 0

	local shaizi_num = GuildBossWGData.Instance:GetDiceRoundShaiZiNum()
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local dice_end_time = scene_info.dice_end_time
	local dice_start_time = scene_info.dice_start_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self.node_list["shaizi_btns"]:SetActive(hurt_val > 0 and shaizi_num == nil)
	local max_dice_round_end = GuildBossWGData.Instance:GetMaxDiceRecordEnd()
	self.node_list["shaizi_num_panel"]:SetActive(shaizi_num and shaizi_num > 0 and not max_dice_round_end and hurt_val > 0)
	self.node_list["not_shaizi_panel"]:SetActive((shaizi_num and shaizi_num <= 0) or hurt_val <= 0)
	if shaizi_num and shaizi_num > 0 then
		local num2 = math.floor(shaizi_num / 10)
		local num1 = shaizi_num % 10
		local perent1 = 1 - (num1) / 9 
		local perent2 = 1 - (num2) / 9 
		-- self.node_list.num_list1.scroll_rect.enabled = true
		-- self.node_list.num_list2.scroll_rect.enabled = true
		-- self.num_list1:JumptToPrecent(1 - perent2)
		-- self.num_list2:JumptToPrecent(1 - perent1)
		-- self.node_list.num_ver_list1.scroll_rect.enabled = true
		-- self.node_list.num_ver_list2.scroll_rect.enabled = true
		-- self.num_ver_list1:JumptToPrecent(1 - perent2)
		-- self.num_ver_list2:JumptToPrecent(1 - perent1)
		self.node_list.num_ver_list1.scroll_rect.verticalNormalizedPosition = perent2
		self.node_list.num_ver_list2.scroll_rect.verticalNormalizedPosition = perent1
	end

    -- local data_info = GuildBossWGData.Instance:GetMaxDiceData()
    -- local max_str = ""
    -- if not IsEmptyTable(data_info) and data_info.role_name then
	--     local role_name = data_info.role_name
    -- 	max_str = string.format(Language.GuildBoss.ShaiZiNumStr, role_name, data_info.dice_num)
    -- end
	--self.node_list["shaizi_text"].text.text = max_str

	local dice_round = scene_info.max_dice_rounds or 1
	local cur_dice_round = scene_info.dice_rounds or 1
	local title_str = ""
	if dice_start_time <= 0 then
		title_str = string.format(Language.GuildBoss.ShaiZTitle1,cur_dice_round,dice_round)
	end
	
	local reward_item = scene_info.dice_round_reward_item
	self.node_list["shaizi_title"].text.text = title_str
	if not IsEmptyTable(reward_item) and reward_item.item_id > 0 then
		self.node_list["itemcell"]:SetActive(true)
		self.item_cell:SetData(reward_item)
		local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
		self.node_list["item_name"].text.text = ToColorStr(item_cfg.name,ITEM_COLOR[item_cfg.color])
	else
		self.node_list["itemcell"]:SetActive(false)
		self.node_list["item_name"].text.text = ""
	end	
	self:StartBossShaiZiTime()
end

function GuildBossRewardView:StartBossShaiZiTime()
	if CountDownManager.Instance:HasCountDown("guild_boss_shaizi") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_shaizi")
	end
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local time = scene_info.dice_end_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if time > server_time then
		CountDownManager.Instance:AddCountDown("guild_boss_shaizi", BindTool.Bind1(self.ShaiziRefreshTime, self), BindTool.Bind1(self.CompleteCallBack, self), time, time,0.02)
	end
end

function GuildBossRewardView:ShaiziRefreshTime(elapse_time, total_time)
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local end_time = scene_info.dice_end_time
	if total_time - elapse_time > 0 then
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		self.node_list["time_slider"].slider.value = (end_time - server_time) / 10
		self.node_list["time_num"].text.text = math.floor(total_time - elapse_time)
	end
end

function GuildBossRewardView:CompleteCallBack()
	self.node_list["time_slider"].slider.value = 0
	--self.node_list["shaizi_text"].text.text = ""
	if CountDownManager.Instance:HasCountDown("guild_boss_shaizi") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_shaizi")
	end
end

function GuildBossRewardView:FlushShaiZiNum(shaizi_num)
	self.node_list["shaizi_btns"]:SetActive(false)
	if shaizi_num > 0 then
		self.shaizi_num = shaizi_num
		self.node_list["shaizi_num_panel"]:SetActive(true)
		self.node_list["not_shaizi_panel"]:SetActive(false)
		self:PlayShaiZiAnim(shaizi_num)
	else
		self.node_list["shaizi_num_panel"]:SetActive(false)
		self.node_list["not_shaizi_panel"]:SetActive(true)
	end
end

function GuildBossRewardView:FlushMaxShaiziRole()
    local data_info = GuildBossWGData.Instance:GetMaxDiceData()
    if IsEmptyTable(data_info) then return end
    local role_name = data_info.role_name
    if data_info.uid == RoleWGData.Instance:InCrossGetOriginUid() and self.is_tz_ing then
        return
    end
    -- if self.node_list["shaizi_text"] then
	--     self.node_list["shaizi_text"].text.text = string.format(Language.GuildBoss.ShaiZiNumStr, role_name, data_info.dice_num)
	-- end
end

function GuildBossRewardView:OnClickFQBtn()
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local dice_start_time = scene_info.dice_start_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if dice_start_time > server_time then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBoss.RewardNotOpen)
		return
	end
	GuildBossWGCtrl.Instance:SendCSGuildBossShaiZi(1)
end

function GuildBossRewardView:OnClickTZBtn()
	local scene_info = GuildBossWGData.Instance:GetGuildBossSceneInfo()
	local dice_start_time = scene_info.dice_start_time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if dice_start_time > server_time then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBoss.RewardNotOpen)
		return
	end
	self.is_tz_ing = true
	self.cur_tz_round = scene_info.dice_rounds
	GuildBossWGCtrl.Instance:SendCSGuildBossShaiZi(0)
end

function GuildBossRewardView:PlayShaiZiAnim(shaizi_num)
	if self.scroll_tween1 then
		self.scroll_tween1:Kill()
		self.scroll_tween1 = nil
	end

	if self.scroll_tween2 then
		self.scroll_tween2:Kill()
		self.scroll_tween2 = nil
	end 

	if self.scroll_tween3 then
		self.scroll_tween3:Kill()
		self.scroll_tween3 = nil
	end

	if self.scroll_tween4 then
		self.scroll_tween4:Kill()
		self.scroll_tween4 = nil
	end

	-- self.node_list.num_list1.scroll_rect.enabled = true
	-- self.node_list.num_list2.scroll_rect.enabled = true
	-- self.node_list.num_list1.scroller.Loop = false
	-- self.node_list.num_list2.scroller.Loop = false
	-- self.node_list.num_list1.scroll_rect.verticalNormalizedPosition = 1
	-- self.node_list.num_list2.scroll_rect.verticalNormalizedPosition = 1

	-- self.node_list.num_ver_list1.scroll_rect.enabled = true
	-- self.node_list.num_ver_list2.scroll_rect.enabled = true
	-- self.node_list.num_ver_list1.scroller.Loop = false
	-- self.node_list.num_ver_list2.scroller.Loop = false
	self.node_list.num_ver_list1.scroll_rect.verticalNormalizedPosition = 1
	self.node_list.num_ver_list2.scroll_rect.verticalNormalizedPosition = 1
	local num2 = math.floor(shaizi_num / 10)
	local num1 = shaizi_num % 10
	local perent1 = 1 - (num1) / 9 
	local perent2 = 1 - (num2) / 9 

	self.scroll_tween1 = UITween.DoScrollRectVerticalPosition(self.node_list.num_ver_list2, 1, 0, 1,function ()
		self.scroll_tween2 = UITween.DoScrollRectVerticalPosition(self.node_list.num_ver_list2,1,perent1,1)
	end)
	ReDelayCall(self, function ()
		self.scroll_tween3 = UITween.DoScrollRectVerticalPosition(self.node_list.num_ver_list1, 1, 0, 1,function ()
            self.scroll_tween4 = UITween.DoScrollRectVerticalPosition(self.node_list.num_ver_list1,1,perent2,1,function ()          
				GuildBossWGCtrl.Instance:SendCSGuildBossShaiZiEnd()
                self.is_tz_ing = false
                self:FlushMaxShaiziRole()
				-- self.node_list.num_ver_list1.scroll_rect.enabled = false
				-- self.node_list.num_ver_list2.scroll_rect.enabled = false
			end)
		end)
	end, 0.3,"delay_scroll_num")

end

function GuildBossRewardView:Close()
	self:DOPlayBackwards()
	GuildBossWGCtrl.Instance:HideShaiZiBtn(true)
end

function GuildBossRewardView:GetOpenState()
	return self.is_open
end

function GuildBossRewardView:DOPlayForward()
 	self:PlayForward()
end

function GuildBossRewardView:PlayForward()
	if not self.rect_target or not self.boss_shaizi_rect_target then 
	 	return 
	end

 	self.rect_target.anchoredPosition = Vector2(0, 1000)
 	self.rect_target.gameObject:SetActive(true)
	self.forward_tween = self.rect_target:DOAnchorPosY(72, 0.5)
	GuildBossWGCtrl.Instance:SetShaiZiBtnActive(false)
	local max_dice_round_end = GuildBossWGData.Instance:GetMaxDiceRecordEnd()
	if not max_dice_round_end then
	    self.boss_shaizi_rect_target:SetActive(true)
	    self.node_list["shaizi_num_panel"]:SetActive(true)
		self.boss_shaizi_rect_target.transform.localScale = Vector3(1,1,1)
		-- 缩放动画导致了数字动画错位，先屏蔽
		-- UITween.ScaleShowPanel(self.boss_shaizi_rect_target, Vector3(0,0,0), 0.2,  DG.Tweening.Ease.Linear) 	
	 	MainuiWGCtrl.Instance:FlushView(0, "guild_boss_shaizi_state", {true})
	end
end

function GuildBossRewardView:DOPlayBackwards()
	if not self.load_complete then
		self.is_should_backward = true
		return
	end
	self:PlayBackwards()
end

function GuildBossRewardView:PlayBackwards()
	if not self.rect_target then
	 	return 
	end

	self.rect_target.anchoredPosition = Vector2(0, 78)
	self.backwards_tween = self.rect_target:DOAnchorPosY(1000, 0.5):OnComplete(
		function()
			SafeBaseView.Close(self)
		end)

	if not self.boss_shaizi_rect_target or not self.boss_shaizi_rect_target:GetActive() then
	 	return 
	end
	MainuiWGCtrl.Instance:FlushView(0, "guild_boss_shaizi_state", {false})
	self.boss_shaizi_rect_target.transform.localScale = Vector3(0,0,0)
	self.node_list["shaizi_num_panel"]:SetActive(false)
	UITween.ScaleShowPanel(self.boss_shaizi_rect_target, Vector3(1,1,1), 0.2,  DG.Tweening.Ease.Linear, function ()
		if self.boss_shaizi_rect_target then
			self.boss_shaizi_rect_target:SetActive(false)
		end
	end)
end



---------------------------------GuildBossRewardItem------------------------------------
GuildBossRewardItem = GuildBossRewardItem or BaseClass(BaseRender)
function GuildBossRewardItem:__init()
	XUI.AddClickEventListener(self.node_list["lbl_guild_reward"], BindTool.Bind(self.ClickReward,self))
end

function GuildBossRewardItem:__delete()

end

function GuildBossRewardItem:OnFlush()
	if not self.data then return end
	self.node_list["lbl_guild_round"].text.text = self.data.round
	local user_name = ""
	if self.data.uid <= 0 then
		user_name = Language.GuildBoss.NotUserName
		self.node_list["lbl_guild_dice_num"].text.text = 0
	else
		user_name = self.data.user_name
		self.node_list["lbl_guild_dice_num"].text.text = self.data.dice_num
	end
	self.node_list["lbl_guild_name"].text.text = user_name
	self.node_list["lbl_guild_reward"].text.text  = ""
	local item_id = self.data.dice_item and self.data.dice_item.item_id or 0
	if item_id ~= 0 then
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		local item_color = ITEM_COLOR[item_cfg.color]
		local item_name = ToColorStr(item_cfg.name, item_color)
		self.node_list["lbl_guild_reward"].text.text  = item_name
	end
	
end

function GuildBossRewardItem:ClickReward()
	local item_id = self.data.dice_item and self.data.dice_item.item_id or 0
	if item_id ~= 0 then
		TipWGCtrl.Instance:OpenItem(self.data.dice_item, ItemTip.FROM_NORMAL, nil)
	end
end


------------RandomNum-----
RandomNum = RandomNum or BaseClass(BaseRender)

function RandomNum:__init()
	
end

function RandomNum.__delete()
end

function RandomNum:OnFlush()
	if not self.data then return end
	self.node_list["num_text"].text.text = self.data
end
