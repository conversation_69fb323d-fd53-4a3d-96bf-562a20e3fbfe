-- 通天降临-迷影寻踪

function WorldTreasureView:PursuitReleaseCallBack()
    if self.pursuit_reward_list then
        self.pursuit_reward_list:DeleteMe()
        self.pursuit_reward_list = nil
    end
end

function WorldTreasureView:PursuitLoadCallBack()
    if not self.pursuit_reward_list then
        self.pursuit_reward_list = AsyncListView.New(ItemCell, self.node_list["pursuit_reward_preview_list"])
        self.pursuit_reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list["btn_get_chance"], BindTool.Bind1(self.OnClickOpenTaskBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_start_game"], BindTool.Bind1(self.OnClickStartGameBtn, self))
    XUI.AddClickEventListener(self.node_list["btn_pursuit_tips"], BindTool.Bind1(self.OnClickOpenRuleTips, self))
end

function WorldTreasureView:PursuitShowIndexCallBack()

end

function WorldTreasureView:PursuitOnFlush(param_t)
    local reward_preview_list = WorldTreasureWGData.Instance:IsPursuitRewardPreview()
    self.pursuit_reward_list:SetDataList(reward_preview_list)

    local is_task_red = WorldTreasureWGData.Instance:IsPursuitTaskRedPoint()
    self.node_list["get_chance_remind"]:SetActive(is_task_red)

    local is_game_red = WorldTreasureWGData.Instance:IsPursuitGameRedPoint()
    self.node_list["start_game_remind"]:SetActive(is_game_red)
end

function WorldTreasureView:OnClickOpenTaskBtn()
    WorldTreasureWGCtrl.Instance:OpenPursuitTaskView()
end

function WorldTreasureView:OnClickStartGameBtn()
    WorldTreasureWGCtrl.Instance:OpenPursuitGameView()
end

function WorldTreasureView:OnClickOpenRuleTips()
    RuleTip.Instance:SetContent(Language.WorldTreasure.PursuitRuleTipsContent, Language.WorldTreasure.PursuitRuleTipsTitle)
end