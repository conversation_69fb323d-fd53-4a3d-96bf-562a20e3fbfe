require("game/boss/boss_privilege_wg_data")
require("game/boss/boss_new_privilege_view")
require("game/boss/boss_privilege_view")
require("game/boss/boss_privilege_upgrade_view")
require("game/boss/boss_new_mabi_skill_show_view")
require("game/boss/boss_godwar_wg_data")
require("game/boss/boss_godwar_view")
require("game/boss/boss_kill_every_wg_data")
require("game/boss/boss_kill_every_view")

BossPrivilegeWGCtrl = BossPrivilegeWGCtrl or BaseClass(BaseWGCtrl)
function BossPrivilegeWGCtrl:__init()
	if BossPrivilegeWGCtrl.Instance ~= nil then
		print_error("[BossPrivilegeWGCtrl] attempt to create singleton twice!")
		return
	end

	BossPrivilegeWGCtrl.Instance = self
	self.data = BossPrivilegeWGData.New()
	self.godwar_data = BossGodWarWGData.New()
	self.kill_every_data = BossKillEveryWGData.New()
	self.view = BossNewPrivilegeView.New(GuideModuleName.BossNewPrivilegeView)
	self.upgrade_view = BossPrivilegeUpgradeView.New() --升级再爆一次特权view
	self:RegisterAllProtocals()
end

function BossPrivilegeWGCtrl:__delete()
	BossPrivilegeWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.godwar_data then
		self.godwar_data:DeleteMe()
		self.godwar_data = nil
	end

	if self.kill_every_data then
		self.kill_every_data:DeleteMe()
		self.kill_every_data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.upgrade_view then
		self.upgrade_view:DeleteMe()
		self.upgrade_view = nil
	end
end

function BossPrivilegeWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSBossDropPrivilegeOperate)
	self:RegisterProtocol(SCBossDropPrivilegeInfo, "OnSCBossDropPrivilegeInfo")

	self:RegisterProtocol(CSRoleZhanshenPrivilegeReq)
	self:RegisterProtocol(SCRoleZhanshenPrivilegeInfo, "OnSCRoleZhanshenPrivilegeInfo")

	self:RegisterProtocol(CSEveryDayKillBossOpenOperate)
    self:RegisterProtocol(SCEveryDayKillBossInfo, "OnSCEveryDayKillBossInfo")
end

----------------------------------------------------再爆一次----------------------------------------------------
--请求操作
function BossPrivilegeWGCtrl:SendBossPrivilegeReq(operate_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSBossDropPrivilegeOperate)
	protocol.operate_type = operate_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

--特权信息
function BossPrivilegeWGCtrl:OnSCBossDropPrivilegeInfo(protocol)
	--print_error("Boss特权信息", protocol)
	self.data:SetPrivilegeInfo(protocol)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_zaibaoyici) then
		ViewManager.Instance:FlushView(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_zaibaoyici)
	end

	if self.upgrade_view:IsOpen() then
		self.upgrade_view:Flush()
	end

	PrivilegedGuidanceWGCtrl.Instance:FlushGuidanceView()
	ViewManager.Instance:FlushView(GuideModuleName.CangJinExchangeView, TabIndex.cangjin_exchange_privilege)
	RemindManager.Instance:Fire(RemindName.PrivilegedGuidance)
	MainuiWGCtrl.Instance:FlushView(0, "boss_privrlege_info")
end

--打开特权升级界面
function BossPrivilegeWGCtrl:OpenBossPrivilegeUpgradeView()
	-- self.upgrade_view:Open()
end

function BossPrivilegeWGCtrl:OpenSceneView()
	-- local is_open = BossPrivilegeWGData.Instance:GetActivateState() --特权开启状态
	-- if is_open < 1 then
	-- 	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_zaibaoyici")
	-- 	if is_open then
	-- 		ViewManager.Instance:Open(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_zaibaoyici)
	-- 	end
	-- end
end

--进入场景回调 
function BossPrivilegeWGCtrl:EnterBossPrivilegeSceneCallBack()
	-- local is_open = BossPrivilegeWGData.Instance:GetActivateState() --特权开启状态
	-- if is_open < 1 then 
	-- 	if not CountDownManager.Instance:HasCountDown("boss_privilege_time") then
	-- 		CountDownManager.Instance:AddCountDown("boss_privilege_time",
	-- 			nil,
	-- 			BindTool.Bind(self.OpenSceneView, self),
	-- 			nil, GameEnum.BOSS_FB_COUNTDOWN_TIME, 1)
	-- 	end
	-- end
end

--离开场景
function BossPrivilegeWGCtrl:OutBossPrivilegeSceneCallBack()
	-- if CountDownManager.Instance:HasCountDown("boss_privilege_time") then
	-- 	CountDownManager.Instance:RemoveCountDown("boss_privilege_time")
	-- end
end

----------------------------------------------------战神特权----------------------------------------------------
--请求操作
function BossPrivilegeWGCtrl:SendBossGodwarPrivilegeReq(operate_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleZhanshenPrivilegeReq)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

--特权信息
function BossPrivilegeWGCtrl:OnSCRoleZhanshenPrivilegeInfo(protocol)
	-- print_error("战神特权信息", protocol)
	self.godwar_data:SetPrivilegeInfo(protocol)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_godwar) then
		ViewManager.Instance:FlushView(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_godwar)
	end

	-- 不做操作了
	-- self:CheckOpenFindBoss(protocol)
	MainuiWGCtrl.Instance:FlushView(0, "boss_god_war_info")
end

-- 检测是否查找boss
function BossPrivilegeWGCtrl:CheckOpenFindBoss(protocol)
	-- 开启了(进行检测)
	local god_war_data = self.godwar_data:GetPrivilegeInfo()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()

	if god_war_data.level ~= -1 and protocol.open_status == 1 and scene_cfg.scene_type == SceneType.VIP_BOSS then
		if protocol.total_count <= 0 then
			self:SendBossGodwarPrivilegeReq(ZHANSHENPRIVILEGE_OPERA_TYPE.SET_STATUS, 0)
		end
	end
end


----------------------------------------------------Boss秒杀----------------------------------------------------
function BossPrivilegeWGCtrl:SendChangeEveryDayKillBossFlagSeq(open_flag)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEveryDayKillBossOpenOperate)
	protocol.open_flag = open_flag or 0
	protocol:EncodeAndSend()
end

function BossPrivilegeWGCtrl:SendChangeAutoKillBossFlagSeq(auto_flag)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEveryDayKillBossOpenAutoKillOperate)
	protocol.auto_flag = auto_flag or 0
	protocol:EncodeAndSend()
end

function BossPrivilegeWGCtrl:OnSCEveryDayKillBossInfo(protocol)
	-- print_error("======info=====", protocol)
	self.kill_every_data:SetBossKillEveryInfo(protocol)
	if ViewManager.Instance:IsOpenByIndex(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_kill_every) then
		ViewManager.Instance:FlushView(GuideModuleName.BossNewPrivilegeView, TabIndex.boss_kill_every)
	end

	MainuiWGCtrl.Instance:FlushView(0, "boss_kill_every")
end
