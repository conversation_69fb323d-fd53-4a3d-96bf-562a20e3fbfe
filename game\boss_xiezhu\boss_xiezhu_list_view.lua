
BossXiezhuListView = BossXiezhuListView or BaseClass(SafeBaseView)
function BossXiezhuListView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(800, 546)})
	self:AddViewResource(0, "uis/view/boss_xiezhu_ui_prefab", "boss_xiezhu_list_view")
end

function BossXiezhuListView:__delete()
end

function BossXiezhuListView:ReleaseCallBack()
	if self.xiezhu_list then
		self.xiezhu_list:DeleteMe()
		self.xiezhu_list = nil
	end

	if self.buttom_reward_list then
		self.buttom_reward_list:DeleteMe()
		self.buttom_reward_list = nil
	end
end

function BossXiezhuListView:LoadCallBack()
	self:SetViewName(Language.BossXiezhu.XiezhuListTitle)
	self.xiezhu_list = AsyncListView.New(BossXiezhuListItem,self.node_list["xiezhu_list"])
	self.buttom_reward_list = AsyncListView.New(ItemCell,self.node_list["buttom_reward_list"])
	local reward_data = BossXiezhuWGData.Instance:GetXiezhuReward()
	self.buttom_reward_list:SetDataList(reward_data)
end

function BossXiezhuListView:OnFlush()
	local invoke_list = BossXiezhuWGData.Instance:GetShowInvokeList()
	if IsEmptyTable(invoke_list) then
		self:Close()
		BossXiezhuWGCtrl.Instance:BossXiezhuInvateTip()
		return
	end

	self.xiezhu_list:SetDataList(invoke_list)
	local xiezhu_count = BossXiezhuWGData.Instance:GetAssistCount()
    local xiezhu_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST)
	local str = string.format(Language.BossXiezhu.ListXiezhuNumText, xiezhu_count - xiezhu_num, xiezhu_count)
	self.node_list["xiezhu_num_text"].tmp.text = str
end


------------------------------------------------------------------------------------------------
BossXiezhuListItem = BossXiezhuListItem or BaseClass(BaseRender)

function BossXiezhuListItem:__init()
	self.head_cell = BaseHeadCell.New(self.node_list["head_pos"])
	XUI.AddClickEventListener(self.node_list["refuse_btn"], BindTool.Bind(self.ClickRefuseBtn,self))
	XUI.AddClickEventListener(self.node_list["xiezhu_btn"], BindTool.Bind(self.ClickXiezhuBtn,self))
end

function BossXiezhuListItem:__delete()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function BossXiezhuListItem:OnFlush()
	if IsEmptyTable(self.data) then return end
	local scene_id = self.data.scene_id
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	self.node_list["boss_pos"].tmp.text = scene_cfg.name
	self.node_list["role_name"].tmp.text = self.data.name
	local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(self.data.monster_id)
	local boss_jieshu = boss_cfg and boss_cfg.boss_jieshu or 0
	local jiesh_str = string.format(Language.BossXiezhu.BoosJieShu,boss_jieshu)
	self.node_list["boss_name"].tmp.text = boss_cfg.name .. jiesh_str
	local data = {}
	data.role_id = self.data.uid
	data.sex = self.data.sex
	data.prof = self.data.prof
	self.head_cell:SetData(data)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	local level_str = is_vis and Language.BossXiezhu.LevelFormat2 or Language.BossXiezhu.LevelFormat1
	local target_str = string.format(level_str, role_level)
	self.node_list["role_level"].tmp.text = target_str

	local is_xiezhu_ing = false
	local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
	local target_info = BossXiezhuWGData.Instance:GetTargetRoleNode()
	local target_uid = target_info and target_info.uid or 0
	is_xiezhu_ing = state == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER and target_uid == self.data.uid
	self.node_list["btns"]:SetActive(not is_xiezhu_ing)
	self.node_list["xiezhu_ing"]:SetActive(is_xiezhu_ing)
end

function BossXiezhuListItem:ClickRefuseBtn()
	if IsEmptyTable(self.data) then return end
	local uid = self.data.uid
	BossXiezhuWGCtrl.Instance:RemoveInvokeRoleInfo(uid,self.index)
	local opera_type = MONSTER_ASSIST_REQ.MONSTER_ASSIST_REQ_CANCEL_REPLY_HELP
	BossXiezhuWGCtrl.Instance:SendCSMonsterAssistReq(opera_type)
end

function BossXiezhuListItem:ClickXiezhuBtn()
	if IsEmptyTable(self.data) then return end
	local state = BossXiezhuWGData.Instance:GetXiezhuStatus()
	-- if state == ASSIST_STATUS.ASSIST_STATUS_INVOKE then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.IsZhaoJiState)
	-- 	return
	-- end

	local xiezhu_count = BossXiezhuWGData.Instance:GetAssistCount()
    local xiezhu_num = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_BOSS_ASSIST)
    local can_xiezhu = xiezhu_num < xiezhu_count
    if can_xiezhu then
    	BossXiezhuWGCtrl.Instance:SendXiezhuBossInfo(self.data)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BossXiezhu.XiezhuNotTimer)
    end
	
end
