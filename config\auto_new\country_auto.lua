-- L-龙云仙契.xls
local item_table={
[1]={item_id=22733,num=1,is_bind=1},
[2]={item_id=39144,num=250,is_bind=1},
[3]={item_id=22734,num=1,is_bind=1},
[4]={item_id=39144,num=300,is_bind=1},
[5]={item_id=26129,num=5,is_bind=1},
[6]={item_id=26357,num=1,is_bind=1},
[7]={item_id=26129,num=10,is_bind=1},
[8]={item_id=27830,num=1,is_bind=1},
[9]={item_id=27836,num=1,is_bind=1},
[10]={item_id=28033,num=5,is_bind=1},
[11]={item_id=27837,num=1,is_bind=1},
[12]={item_id=27832,num=1,is_bind=1},
[13]={item_id=27833,num=1,is_bind=1},
[14]={item_id=27838,num=1,is_bind=1},
[15]={item_id=29835,num=1,is_bind=1},
[16]={item_id=27834,num=1,is_bind=1},
[17]={item_id=27835,num=1,is_bind=1},
[18]={item_id=27831,num=1,is_bind=1},
[19]={item_id=29836,num=1,is_bind=1},
[20]={item_id=27613,num=1,is_bind=1},
[21]={item_id=37033,num=1,is_bind=1},
[22]={item_id=29839,num=1,is_bind=1},
[23]={item_id=39144,num=200,is_bind=1},
[24]={item_id=26129,num=2,is_bind=1},
[25]={item_id=27612,num=5,is_bind=1},
[26]={item_id=27612,num=2,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
task={
{task_type=14,task_value=500,open_panel="market#Tab_market50",},
{task_id=2,task_type=7,task_value=120,task_des="日常活跃度达到120",open_panel="bizuo#bizuo_bizuo",},
{task_id=3,task_type=12,task_des="挑战2次副本神灵仙岛",open_panel="fubenpanel#fubenpanel_bagua",},
{task_id=4,task_value=5,task_des="击杀5只仙遗洞天一层以上BOSS",},
{task_id=5,task_type=22,task_des="参与3次护送",open_panel="YunbiaoView",},
{task_id=6,task_type=5,task_value=3,task_des="击杀3只2层以上伏魔战场BOSS",open_panel="boss#boss_world",},
{task_id=7,task_type=16,task_value=1,task_des="参与击杀1次谪仙之境BOSS",open_panel="WorldServer#world_new_shenyuan_boss",},
{task_id=8,task_type=6,task_des="击杀2只2层以上灵妖奇脉BOSS",open_panel="boss#boss_personal",},
{task_id=9,task_type=24,task_des="击杀3只荒天炎窟BOSS",open_panel="WorldServer#worserv_boss_mh",},
{task_id=10,task_type=9,task_des="通关2次副本暗翼之巢",open_panel="fubenpanel#fubenpanel_pet",},
{task_id=11,task_type=34,task_des="通关2次副本熔火之心",open_panel="fubenpanel#fubenpanel_copper",},
{task_id=12,task_type=11,task_des="挑战2次副本天峰夺宝",open_panel="fubenpanel#fubenpanel_equip_high",},
{task_id=13,task_type=10,task_des="挑战2次副本日月修行",open_panel="fubenpanel#fubenpanel_exp",},
{task_id=14,task_type=40,task_des="幻兽召唤10次",open_panel="ControlBeastsPrizeDrawWGView",},
{task_id=15,task_type=27,task_des="参加10次天梯争霸",open_panel="act_jjc#arena_field1v1",},
{task_id=16,task_value=10,task_des="击杀10只仙遗洞天一层以上BOSS",},
{task_id=17,task_type=5,task_des="击杀5只2层以上伏魔战场BOSS",open_panel="boss#boss_world",},
{task_id=18,task_type=6,task_des="击杀3只2层以上灵妖奇脉BOSS",open_panel="boss#boss_personal",},
{task_id=19,task_type=16,reward_item={[0]=item_table[1],[1]=item_table[2]},add_fame=150,add_devote=6,task_des="参与击杀2次谪仙之境BOSS",quality=2,open_panel="WorldServer#world_new_shenyuan_boss",},
{task_id=20,task_type=7,task_value=200,task_des="日常活跃度达到200",open_panel="bizuo#bizuo_bizuo",},
{task_id=21,task_type=27,task_des="参加30次天梯争霸",open_panel="act_jjc#arena_field1v1",},
{task_id=22,task_type=9,task_des="通关5次副本暗翼之巢",open_panel="fubenpanel#fubenpanel_pet",},
{task_id=23,task_type=34,task_des="通关4次副本熔火之心",open_panel="fubenpanel#fubenpanel_copper",},
{task_id=24,task_type=12,task_value=4,task_des="挑战4次副本神灵仙岛",open_panel="fubenpanel#fubenpanel_bagua",},
{task_id=25,task_type=10,task_des="挑战3次副本日月修行",open_panel="fubenpanel#fubenpanel_exp",},
{task_id=26,task_value=30,reward_item={[0]=item_table[1],[1]=item_table[2]},add_fame=150,add_devote=6,task_des="击杀30只仙遗洞天一层以上BOSS",quality=2,},
{task_id=27,task_type=26,task_value=3,task_des="通关3层九重劫塔",open_panel="fubenpanel#fubenpanel_welkin",},
{task_id=28,task_type=40,task_des="幻兽召唤30次",open_panel="ControlBeastsPrizeDrawWGView",},
{task_id=29,task_type=43,task_des="铜钱祈福5次",},
{task_id=30,task_type=29,task_value=5,task_des="经验祈福5次",open_panel="qifu#qifu_qf",},
{task_id=31,task_type=2,task_value=3280,reward_item={[0]=item_table[3],[1]=item_table[4]},add_fame=200,add_devote=8,task_des="消费3280灵玉",quality=1,},
{task_id=32,task_type=1,task_value=1680,task_des="充值1680灵玉",open_panel="vip",},
{task_id=33,task_type=10,task_value=4,task_des="挑战4次副本日月修行",open_panel="fubenpanel#fubenpanel_exp",},
{task_id=34,task_type=27,task_value=50,task_des="参加50次天梯争霸",open_panel="act_jjc#arena_field1v1",},
{task_id=35,task_type=31,task_des="获得1次谪仙之境BOSS完美归属奖励",open_panel="WorldServer#world_new_shenyuan_boss",},
{task_id=36,task_type=32,task_value=1,task_des="领取所有活跃任务奖励",open_panel="bizuo#bizuo_bizuo",},
{task_id=37,task_type=4,task_value=1000,task_des="商店消耗1000灵玉",},
{task_id=38,task_type=4,task_value=300,task_des="商店消耗300灵玉",open_panel="market#Tab_market20",},
{task_id=39,task_value=12,task_des="击杀12只仙遗洞天一层以上BOSS",},
{task_id=40,task_type=2,task_value=1640,task_des="消费1640灵玉",open_panel="market#Tab_market20",},
{task_id=41,task_type=24,task_value=6,task_des="击杀6只荒天炎窟BOSS",open_panel="WorldServer#worserv_boss_mh",}
},

task_meta_table_map={
[15]=16,	-- depth:1
[14]=16,	-- depth:1
[5]=6,	-- depth:1
[9]=6,	-- depth:1
[31]=38,	-- depth:1
[37]=31,	-- depth:2
[36]=31,	-- depth:2
[35]=36,	-- depth:3
[34]=31,	-- depth:2
[33]=31,	-- depth:2
[32]=31,	-- depth:2
[30]=19,	-- depth:1
[21]=26,	-- depth:1
[28]=26,	-- depth:1
[27]=19,	-- depth:1
[25]=27,	-- depth:2
[24]=19,	-- depth:1
[23]=24,	-- depth:2
[22]=30,	-- depth:2
[40]=19,	-- depth:1
[20]=19,	-- depth:1
[18]=27,	-- depth:2
[17]=30,	-- depth:2
[29]=30,	-- depth:2
[41]=19,	-- depth:1
},
task_quality={
{},
{quality=2,name="挑战",},
{quality=1,name="成仙",}
},

task_quality_meta_table_map={
},
daily_task={
{},
{level_limit_min=341,level_limit_max=410,task_id_list="1|2|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|25|26|27|28|29|30|31|32|33|34|35|36|37|38|41",},
{level_limit_min=411,level_limit_max=2000,task_id_list="1|2|3|4|5|6|7|8|9|10|11|12|13|14|15|16|17|18|19|20|21|22|23|24|25|26|27|28|29|30|31|32|33|34|35|36|37|38|39",},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=3,},
{week_day=3,},
{week_day=3,},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=6,},
{week_day=6,},
{week_day=6,}
},

daily_task_meta_table_map={
[18]=3,	-- depth:1
[17]=2,	-- depth:1
[15]=18,	-- depth:2
[14]=17,	-- depth:2
[11]=14,	-- depth:3
[20]=11,	-- depth:4
[9]=15,	-- depth:3
[8]=20,	-- depth:5
[6]=9,	-- depth:4
[5]=8,	-- depth:6
[12]=6,	-- depth:5
[21]=12,	-- depth:6
},
daily_fame_reward={
[0]={reward_index=0,},
[1]={reward_index=1,need_fame=600,},
[2]={reward_index=2,need_fame=1000,},
[3]={reward_index=3,need_fame=1600,},
[4]={reward_index=4,need_fame=2200,reward_item={[0]=item_table[5]},},
[5]={reward_index=5,need_fame=2800,},
[6]={reward_index=6,need_fame=3400,},
[7]={reward_index=7,need_fame=4000,reward_item={[0]=item_table[6]},},
[8]={reward_index=8,need_fame=4600,reward_item={[0]=item_table[7]},}
},

daily_fame_reward_meta_table_map={
[5]=4,	-- depth:1
[6]=4,	-- depth:1
},
order_group={
{},
{index=1,min_open_day=31,max_open_day=999,rmb_seq=1,}
},

order_group_meta_table_map={
},
order={
{},
{seq=1,need_devote=100,},
{seq=2,need_devote=150,},
{seq=3,need_devote=200,},
{seq=4,need_devote=250,item={[0]=item_table[8]},added_item={[0]=item_table[9],[1]=item_table[10]},},
{seq=5,need_devote=300,},
{seq=6,need_devote=350,},
{seq=7,need_devote=400,},
{seq=8,need_devote=450,},
{seq=9,need_devote=500,added_item={[0]=item_table[11],[1]=item_table[10]},},
{seq=10,need_devote=600,},
{seq=11,need_devote=700,},
{seq=12,need_devote=800,},
{seq=13,need_devote=900,},
{seq=14,need_devote=1000,item={[0]=item_table[12]},},
{seq=15,need_devote=1100,},
{seq=16,need_devote=1200,},
{seq=17,need_devote=1300,},
{seq=18,need_devote=1400,},
{seq=19,need_devote=1500,item={[0]=item_table[13]},added_item={[0]=item_table[14],[1]=item_table[15]},},
{seq=20,need_devote=1600,},
{seq=21,need_devote=1700,},
{seq=22,need_devote=1800,},
{seq=23,need_devote=1900,},
{seq=24,need_devote=2000,item={[0]=item_table[16]},},
{seq=25,need_devote=2100,},
{seq=26,need_devote=2200,},
{seq=27,need_devote=2300,},
{seq=28,need_devote=2400,},
{seq=29,need_devote=2500,item={[0]=item_table[17]},},
{seq=30,need_devote=2600,},
{seq=31,need_devote=2700,},
{seq=32,need_devote=2800,},
{seq=33,need_devote=2900,},
{seq=34,need_devote=3000,},
{seq=35,need_devote=3100,},
{seq=36,need_devote=3200,},
{seq=37,need_devote=3300,},
{seq=38,need_devote=3400,},
{seq=39,need_devote=3500,item={[0]=item_table[18]},added_item={[0]=item_table[14],[1]=item_table[19]},},
{seq=40,need_devote=3600,},
{seq=41,need_devote=3700,},
{seq=42,need_devote=3800,},
{seq=43,need_devote=3900,},
{seq=44,need_devote=4000,},
{seq=45,need_devote=4100,},
{seq=46,need_devote=4200,},
{seq=47,need_devote=4300,},
{seq=48,need_devote=4400,},
{seq=49,need_devote=4500,added_item={[0]=item_table[11],[1]=item_table[10]},},
{seq=50,need_devote=4600,},
{seq=51,need_devote=4700,},
{seq=52,need_devote=4800,},
{seq=53,need_devote=4900,added_item={[0]=item_table[20]},},
{seq=54,need_devote=5000,},
{seq=55,need_devote=5100,},
{seq=56,need_devote=5200,},
{seq=57,need_devote=5300,},
{seq=58,need_devote=5400,},
{seq=59,need_devote=5500,item={[0]=item_table[21]},added_item={[0]=item_table[14],[1]=item_table[22]},fixed_show=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,item={[0]=item_table[21]},},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,},
{group_index=1,}
},

order_meta_table_map={
[118]=58,	-- depth:1
[106]=46,	-- depth:1
[4]=54,	-- depth:1
[59]=54,	-- depth:1
[2]=54,	-- depth:1
[63]=3,	-- depth:1
[66]=6,	-- depth:1
[68]=8,	-- depth:1
[103]=43,	-- depth:1
[71]=11,	-- depth:1
[73]=13,	-- depth:1
[76]=16,	-- depth:1
[78]=18,	-- depth:1
[101]=41,	-- depth:1
[81]=21,	-- depth:1
[83]=23,	-- depth:1
[86]=26,	-- depth:1
[88]=28,	-- depth:1
[98]=38,	-- depth:1
[91]=31,	-- depth:1
[93]=33,	-- depth:1
[52]=54,	-- depth:1
[19]=54,	-- depth:1
[57]=54,	-- depth:1
[49]=54,	-- depth:1
[17]=54,	-- depth:1
[22]=54,	-- depth:1
[24]=54,	-- depth:1
[113]=53,	-- depth:1
[116]=56,	-- depth:1
[27]=54,	-- depth:1
[14]=54,	-- depth:1
[29]=54,	-- depth:1
[12]=54,	-- depth:1
[34]=54,	-- depth:1
[111]=51,	-- depth:1
[37]=54,	-- depth:1
[32]=54,	-- depth:1
[9]=54,	-- depth:1
[47]=54,	-- depth:1
[7]=54,	-- depth:1
[108]=48,	-- depth:1
[44]=54,	-- depth:1
[42]=54,	-- depth:1
[96]=36,	-- depth:1
[39]=54,	-- depth:1
[72]=12,	-- depth:2
[94]=34,	-- depth:2
[114]=54,	-- depth:1
[92]=32,	-- depth:2
[97]=37,	-- depth:2
[117]=57,	-- depth:2
[89]=29,	-- depth:2
[67]=7,	-- depth:2
[87]=27,	-- depth:2
[62]=2,	-- depth:2
[99]=39,	-- depth:2
[84]=24,	-- depth:2
[69]=9,	-- depth:2
[82]=22,	-- depth:2
[64]=4,	-- depth:2
[79]=19,	-- depth:2
[109]=49,	-- depth:2
[77]=17,	-- depth:2
[112]=52,	-- depth:2
[102]=42,	-- depth:2
[74]=14,	-- depth:2
[107]=47,	-- depth:2
[119]=59,	-- depth:2
[104]=44,	-- depth:2
[40]=60,	-- depth:1
[5]=60,	-- depth:1
[10]=40,	-- depth:2
[15]=5,	-- depth:2
[20]=60,	-- depth:1
[25]=5,	-- depth:2
[30]=10,	-- depth:3
[55]=25,	-- depth:3
[50]=20,	-- depth:2
[45]=15,	-- depth:3
[35]=5,	-- depth:2
[115]=55,	-- depth:4
[90]=30,	-- depth:4
[95]=35,	-- depth:3
[105]=45,	-- depth:4
[65]=5,	-- depth:2
[70]=10,	-- depth:3
[75]=15,	-- depth:3
[80]=20,	-- depth:2
[100]=40,	-- depth:2
[85]=25,	-- depth:3
[110]=50,	-- depth:3
[120]=60,	-- depth:1
},
order_exp={
{},
{seq=1,rmb_seq=1,devote=40,price=12,devote_return=120,},
{seq=2,rmb_seq=2,devote=80,price=24,devote_return=240,},
{seq=3,rmb_seq=3,devote=200,price=60,devote_return=600,},
{group_index=1,rmb_seq=100,},
{group_index=1,rmb_seq=101,},
{group_index=1,rmb_seq=102,},
{group_index=1,rmb_seq=103,}
},

order_exp_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
other_default_table={open_day=9999,open_level=9999,order_cycle_day=30,},

task_default_table={task_id=1,task_type=33,task_value=2,reward_item={[0]=item_table[1],[1]=item_table[23]},add_fame=100,add_devote=4,task_des="消费500元宝",quality=3,open_panel="boss#boss_vip",},

task_quality_default_table={quality=3,name="活跃",},

daily_task_default_table={week_day=0,level_limit_min=0,level_limit_max=340,task_id_list="1|2|4|5|6|7|8|10|11|12|13|14|15|16|17|18|19|20|21|22|23|25|26|27|28|29|30|31|32|33|34|35|36|37|38|39|40",},

daily_fame_reward_default_table={reward_index=0,need_fame=300,reward_item={[0]=item_table[24]},},

order_group_default_table={index=0,min_open_day=1,max_open_day=30,rmb_type=192,rmb_seq=0,order_price=328,order_return=3280,order_preview={[0]=item_table[22],[1]=item_table[19],[2]=item_table[15],[3]=item_table[14],[4]=item_table[14],[5]=item_table[14],[6]=item_table[9],[7]=item_table[11],[8]=item_table[9],[9]=item_table[9],[10]=item_table[11],[11]=item_table[9],[12]=item_table[9],[13]=item_table[11],[14]=item_table[9],[15]=item_table[25],[16]=item_table[10],[17]=item_table[20],[18]=item_table[25],[19]=item_table[10],[20]=item_table[20],[21]=item_table[10],[22]=item_table[25],[23]=item_table[10],[24]=item_table[20],[25]=item_table[25],[26]=item_table[10],[27]=item_table[20],[28]=item_table[10],[29]=item_table[10],[30]=item_table[25],[31]=item_table[20],[32]=item_table[25],[33]=item_table[10],[34]=item_table[20],[35]=item_table[10],[36]=item_table[25],[37]=item_table[10],[38]=item_table[20],[39]=item_table[25],[40]=item_table[10],[41]=item_table[20],[42]=item_table[25],[43]=item_table[10],[44]=item_table[20],[45]=item_table[25],[46]=item_table[10],[47]=item_table[20],[48]=item_table[10],[49]=item_table[25],[50]=item_table[10],[51]=item_table[20],[52]=item_table[25],[53]=item_table[10],[54]=item_table[20],[55]=item_table[10],[56]=item_table[25],[57]=item_table[10],[58]=item_table[20],[59]=item_table[25],[60]=item_table[10],[61]=item_table[20],[62]=item_table[10],[63]=item_table[10],[64]=item_table[25],[65]=item_table[20],[66]=item_table[25],[67]=item_table[10],[68]=item_table[20],[69]=item_table[25],[70]=item_table[10],[71]=item_table[20],[72]=item_table[25],[73]=item_table[10],[74]=item_table[20],[75]=item_table[10],[76]=item_table[25],[77]=item_table[10],[78]=item_table[20],[79]=item_table[25],[80]=item_table[10],[81]=item_table[20],[82]=item_table[10],[83]=item_table[25],[84]=item_table[10],[85]=item_table[20],[86]=item_table[25],[87]=item_table[10],[88]=item_table[20],[89]=item_table[10],[90]=item_table[25],[91]=item_table[10],[92]=item_table[20],[93]=item_table[25],[94]=item_table[10],[95]=item_table[20]},},

order_default_table={group_index=0,seq=0,need_devote=50,item={[0]=item_table[26]},added_item={[0]=item_table[25],[1]=item_table[10]},fixed_show=0,},

order_exp_default_table={group_index=0,seq=0,rmb_type=193,rmb_seq=0,devote=20,price=6,devote_return=60,}

}

