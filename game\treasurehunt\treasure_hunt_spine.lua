--
TreasureHuntSpine = TreasureHuntSpine or BaseClass(SafeBaseView)

function TreasureHuntSpine:__init()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.One)
    self:SetMaskBg(false, false)
    self.view_layer = UiLayer.Pop
    self.view_name = "TreasureHuntSpine"
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_qizhenyibao_spine")
end

function TreasureHuntSpine:ReleaseCallBack()
	if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

    if self.call_back then
        self.call_back = nil
    end

    self.xunbao_graphic = nil
end

function TreasureHuntSpine:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_click"], BindTool.Bind(self.OnClickClose, self))
end

function TreasureHuntSpine:OnFlush()
    self:PlaySpineAnim()
end

function TreasureHuntSpine:FlushItemList()

end

function TreasureHuntSpine:CloseCallBack()
    self.xunbao_graphic.AnimationState:SetAnimation(0, "idle", true)

    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

    if self.call_back then
        self.call_back = nil
    end
end

function TreasureHuntSpine:SetDataAndOpen(call_back)
	self.call_back = call_back
    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function TreasureHuntSpine:PlaySpineAnim()
    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

	if not self.xunbao_graphic then
		self.xunbao_graphic = self.node_list["xunbao_graphic"].gameObject:GetComponent("SkeletonGraphic")
	end

	if self.xunbao_graphic then
		self.xunbao_graphic.AnimationState:SetAnimation(0, "idle", true)
		self.delay_play_draw_idle = GlobalTimerQuest:AddDelayTimer(function()
			self.xunbao_graphic.AnimationState:SetAnimation(0, "choujiang", false)
            self.delay_play_draw_idle2 = GlobalTimerQuest:AddDelayTimer(function()
                if self.call_back then
                    self.call_back()
                    self.call_back = nil
                end
            end, 3.5)
		end, 1)
	end
end

function TreasureHuntSpine:OnClickClose()
    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.delay_play_draw_idle2 then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle2)
		self.delay_play_draw_idle2 = nil
	end

    if self.call_back then
        self.call_back()
        self.call_back = nil
    end

    TryDelayCall(self, function()
        self:Close()
    end, 1, "DelayHuntSpineClose")
end