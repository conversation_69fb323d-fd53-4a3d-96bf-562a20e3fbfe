EternalNightFinalSceneLogic = EternalNightFinalSceneLogic or BaseClass(CommonFbLogic)

local GetMoveObjTime = 5		--获取移动对象的时间间隔

function EternalNightFinalSceneLogic:__init()
	self.next_get_all_move_obj_time = 0					-- 下次获取移动对象的时间

end

function EternalNightFinalSceneLogic:__delete()
	self.next_get_all_move_obj_time = nil
end

function EternalNightFinalSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		--local cur_grade_type = EternalNightWGData.Instance:GetCurGradeType() or 1
		--local grade_name = Language.EternalNight.GradeTypeName[cur_grade_type]
		--MainuiWGCtrl.Instance:SetFBNameState(true, grade_name)
		--MainuiWGCtrl.Instance:SetTeamBtnState(false)

		ViewManager.Instance:Open(GuideModuleName.EternalNightTaskView)
	end)


	self:SetLeaveFbTip(true)
	self.next_get_all_move_obj_time = 0
	self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))
	self.fly_down_end_event = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_FLY_DOWN_END, BindTool.Bind(self.OnFlyDownEnd,self))
    self.obj_dead_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjDead, self))

	Scene.SendGetAllObjMoveInfoReq()
	self.is_dead_role_list = {}

	local self_info = EternalNightWGData.Instance:GetSelfInfo()
    local other_cap = self_info.equip_attr_info and self_info.equip_attr_info.equip_capability or 0
	local main_cap = RoleWGData.Instance:GetOriginCapability()
	RoleWGData.Instance:SetAttr("capability", main_cap + other_cap)
end

function EternalNightFinalSceneLogic:Out()
	CommonFbLogic.Out(self)
	ViewManager.Instance:Close(GuideModuleName.EternalNightTaskView)
	-- EternalNightWGData.Instance:RestRoleOriginAppearance()

	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
	end
	
	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end
	
	if self.obj_dead_event then
		GlobalEventSystem:UnBind(self.obj_dead_event)
		self.obj_dead_event = nil
	end

	-- MainuiWGCtrl.Instance:SetFBNameState(false)
	-- MainuiWGCtrl.Instance:SetTeamBtnState(true)

	ViewManager.Instance:Close(GuideModuleName.EternalNightTaskView)
	ViewManager.Instance:Close(GuideModuleName.EternalNightEquipView)
	ViewManager.Instance:Close(GuideModuleName.EternalNightRankView)
	ViewManager.Instance:Close(GuideModuleName.EternalNightRewardView)

	local main_cap = RoleWGData.Instance:GetOriginCapability()
	RoleWGData.Instance:SetAttr("capability", main_cap)
	-- EternalNightWGData.Instance:SetCurEnterActType(nil)
	EternalNightWGData.Instance:SetGoToBossFun(nil)

end

-- 是否可以拉取移动对象信息
function EternalNightFinalSceneLogic:CanGetMoveObj()
	return true
end

function EternalNightFinalSceneLogic:OnFlyDownEnd()
	if self.fly_down_end_event then
		GlobalEventSystem:UnBind(self.fly_down_end_event)
		self.fly_down_end_event = nil
	end
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

end


function EternalNightFinalSceneLogic:OnRoleEnter(obj_id)
	local main_role = Scene.Instance:GetMainRole()
	if not main_role:IsFightState() and GuajiCache.guaji_type == GuajiType.Auto then
		local role = Scene.Instance:GetObj(obj_id)
		local role_vo = role:GetVo()
		if role and role:IsRole() then
			if role:IsRealDead() then
				self.is_dead_role_list[role_vo.role_id] = true
			end
			GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
	end
end

function EternalNightFinalSceneLogic:OnObjDead(obj)
	if not obj then
        return
    end
    local main_role = Scene.Instance:GetMainRole()
    --在诛仙战场 杀死怪物后停留一下
    if obj:IsRole() and obj:IsRealDead() and not main_role:IsFightState() and GuajiCache.guaji_type == GuajiType.Auto then
    	local role_vo = obj:GetVo()
        self.is_dead_role_list[role_vo.role_id] = true
        GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
    end
end

function EternalNightFinalSceneLogic:GetGuajiPos()
	if Status.NowTime >= self.next_get_all_move_obj_time then
		self.next_get_all_move_obj_time = Status.NowTime + GetMoveObjTime
		Scene.SendGetAllObjMoveInfoReq()
	end

	local pos_x,pos_y = self:GetGuiJiEnemyPos()
	return pos_x,pos_y
end

-- 获取挂机打怪的位置
function EternalNightFinalSceneLogic:GetGuiJiEnemyPos()
    local target_distance = 999999999
    local target_x = nil
    local target_y = nil
    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil or main_role:IsDeleted() then
		return
    end
    local my_vo = main_role:GetVo()
    local mian_obj_id = my_vo.obj_id

    local x, y = Scene.Instance:GetMainRole():GetLogicPos()
    local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
    for k, v in pairs(obj_move_info_list) do
        local vo = v:GetVo()
        local not_block = not AStarFindWay:IsBlock(vo.pos_x, vo.pos_y)
        if vo.obj_type == SceneObjType.Role and not_block and vo.obj_id ~= mian_obj_id then
            local is_dead = self.is_dead_role_list[vo.role_id]
            local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
            if distance < target_distance and not is_dead then
                target_x = vo.pos_x
                target_y = vo.pos_y
                target_distance = distance
            end
        end
    end
    return target_x, target_y
end
