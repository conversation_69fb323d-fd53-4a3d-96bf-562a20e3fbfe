LuckyGiftBagLocalGetRewardView = LuckyGiftBagLocalGetRewardView or BaseClass(SafeBaseView)

function LuckyGiftBagLocalGetRewardView:__init()
    self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "lucky_gift_bag_get_reward_view")
	self:SetMaskBg(true, true)
end

function LuckyGiftBagLocalGetRewardView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end

    if self.record_list_most then
        self.record_list_most:DeleteMe()
        self.record_list_most = nil
    end
end

function LuckyGiftBagLocalGetRewardView:LoadCallBack()
	if nil == self.record_list then
		self.record_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	end

    if nil == self.record_list_most then
        self.record_list_most = AsyncBaseGrid.New()
		local t = {}
		t.col = 10
		t.change_cells_num = 1
		t.itemRender = ItemCell
		t.list_view = self.node_list["reward_list_most"]
		self.record_list_most:CreateCells(t)
        self.record_list_most:SetStartZeroIndex(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_again, BindTool.Bind(self.OnBugAgainBtnClick, self))
end

function LuckyGiftBagLocalGetRewardView:OnFlush()
    local data = LuckyGiftBagLocalWgData.Instance:GetBuyResultInfo()
    if not IsEmptyTable(data) then
        local item_count = data.item_count
        local is_most_reward = item_count > 10
        local data_list = data.item_list
    
        if self.node_list["reward_list"] then
            self.node_list["reward_list"]:SetActive(not is_most_reward)
        end
    
        if self.node_list["reward_list_most"] then
            self.node_list["reward_list_most"]:SetActive(is_most_reward)
        end
    
        if is_most_reward then
            self.record_list_most:SetDataList(data_list)
        else
            self.record_list:SetDataList(data_list)
        end
    
        self.node_list.btn_close:SetActive(data.show_mode == 2)
        self.node_list.buy_btns:SetActive(data.show_mode == 1)
    
        local grade, seq = LuckyGiftBagLocalWgData.Instance:GetCurrentSelectInfo()
        local data_list = LuckyGiftBagLocalWgData.Instance:GetGradeCfgInfo(grade, seq)
        local prchase_mode = LuckyGiftBagLocalWgData.Instance:GetBeforePrchaseMode()
        self.node_list.btn_again_text.text.text = string.format(Language.LuckyGiftBag.BugAgainBtn ,data_list[prchase_mode][1].times)
        self.node_list.btn_buy_moneycost.text.text = data_list[prchase_mode][1].need_cost or "" 
    end
end

function LuckyGiftBagLocalGetRewardView:OnBugAgainBtnClick()
    LuckyGiftBagLoaclWgCtrl.Instance:BugLuckyGiftBagReq(LuckyGiftBagLocalWgData.Instance:GetBeforePrchaseMode())
end