﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_UI_LayoutRebuilderWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.UI.LayoutRebuilder), typeof(System.Object));
		<PERSON><PERSON>RegFunction("IsDestroyed", IsDestroyed);
		<PERSON><PERSON>RegFunction("ForceRebuildLayoutImmediate", ForceRebuildLayoutImmediate);
		<PERSON><PERSON>unction("Rebuild", Rebuild);
		<PERSON><PERSON>RegFunction("MarkLayoutForRebuild", MarkLayoutForRebuild);
		<PERSON><PERSON>RegFunction("LayoutComplete", LayoutComplete);
		<PERSON><PERSON>RegFunction("GraphicUpdateComplete", GraphicUpdateComplete);
		L.RegFunction("GetHashCode", GetHashCode);
		<PERSON><PERSON>RegFunction("Equals", Equals);
		<PERSON><PERSON>RegFunction("ToString", ToString);
		<PERSON><PERSON>RegFunction("New", _CreateUnityEngine_UI_LayoutRebuilder);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("transform", get_transform, null);
		<PERSON>.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_UI_LayoutRebuilder(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.UI.LayoutRebuilder obj = new UnityEngine.UI.LayoutRebuilder();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.UI.LayoutRebuilder.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsDestroyed(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.LayoutRebuilder obj = (UnityEngine.UI.LayoutRebuilder)ToLua.CheckObject<UnityEngine.UI.LayoutRebuilder>(L, 1);
			bool o = obj.IsDestroyed();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceRebuildLayoutImmediate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 1, typeof(UnityEngine.RectTransform));
			UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Rebuild(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.UI.LayoutRebuilder obj = (UnityEngine.UI.LayoutRebuilder)ToLua.CheckObject<UnityEngine.UI.LayoutRebuilder>(L, 1);
			UnityEngine.UI.CanvasUpdate arg0 = (UnityEngine.UI.CanvasUpdate)ToLua.CheckObject(L, 2, typeof(UnityEngine.UI.CanvasUpdate));
			obj.Rebuild(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MarkLayoutForRebuild(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 1, typeof(UnityEngine.RectTransform));
			UnityEngine.UI.LayoutRebuilder.MarkLayoutForRebuild(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LayoutComplete(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.LayoutRebuilder obj = (UnityEngine.UI.LayoutRebuilder)ToLua.CheckObject<UnityEngine.UI.LayoutRebuilder>(L, 1);
			obj.LayoutComplete();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GraphicUpdateComplete(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.LayoutRebuilder obj = (UnityEngine.UI.LayoutRebuilder)ToLua.CheckObject<UnityEngine.UI.LayoutRebuilder>(L, 1);
			obj.GraphicUpdateComplete();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHashCode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.LayoutRebuilder obj = (UnityEngine.UI.LayoutRebuilder)ToLua.CheckObject<UnityEngine.UI.LayoutRebuilder>(L, 1);
			int o = obj.GetHashCode();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Equals(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.UI.LayoutRebuilder obj = (UnityEngine.UI.LayoutRebuilder)ToLua.CheckObject<UnityEngine.UI.LayoutRebuilder>(L, 1);
			object arg0 = ToLua.ToVarObject(L, 2);
			bool o = obj != null ? obj.Equals(arg0) : arg0 == null;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.UI.LayoutRebuilder obj = (UnityEngine.UI.LayoutRebuilder)ToLua.CheckObject<UnityEngine.UI.LayoutRebuilder>(L, 1);
			string o = obj.ToString();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_transform(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.UI.LayoutRebuilder obj = (UnityEngine.UI.LayoutRebuilder)o;
			UnityEngine.Transform ret = obj.transform;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index transform on a nil value");
		}
	}
}

