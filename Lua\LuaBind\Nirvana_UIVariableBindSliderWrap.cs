﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_UIVariableBindSliderWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.UIVariableBindSlider), typeof(Nirvana.UIVariableBind));
		<PERSON><PERSON>("BindVariables", BindVariables);
		<PERSON><PERSON>("UnbindVariables", UnbindVariables);
		L<PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("tweenSpeed", get_tweenSpeed, set_tweenSpeed);
		<PERSON><PERSON>("tweenType", get_tweenType, set_tweenType);
		L.<PERSON>("TweenSpeed", get_TweenSpeed, set_TweenSpeed);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BindVariables(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariableBindSlider obj = (Nirvana.UIVariableBindSlider)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariableBindSlider));
			obj.BindVariables();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnbindVariables(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariableBindSlider obj = (Nirvana.UIVariableBindSlider)ToLua.CheckObject(L, 1, typeof(Nirvana.UIVariableBindSlider));
			obj.UnbindVariables();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_tweenSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBindSlider obj = (Nirvana.UIVariableBindSlider)o;
			float ret = obj.tweenSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tweenSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_tweenType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBindSlider obj = (Nirvana.UIVariableBindSlider)o;
			Nirvana.UIVariableBindSlider.TweenType ret = obj.tweenType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tweenType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TweenSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBindSlider obj = (Nirvana.UIVariableBindSlider)o;
			float ret = obj.TweenSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TweenSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_tweenSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBindSlider obj = (Nirvana.UIVariableBindSlider)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.tweenSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tweenSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_tweenType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBindSlider obj = (Nirvana.UIVariableBindSlider)o;
			Nirvana.UIVariableBindSlider.TweenType arg0 = (Nirvana.UIVariableBindSlider.TweenType)ToLua.CheckObject(L, 2, typeof(Nirvana.UIVariableBindSlider.TweenType));
			obj.tweenType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index tweenType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TweenSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBindSlider obj = (Nirvana.UIVariableBindSlider)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.TweenSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TweenSpeed on a nil value");
		}
	}
}

