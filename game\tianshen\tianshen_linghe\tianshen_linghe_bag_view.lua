TianShenLingHeBagView = TianShenLingHeBagView or BaseClass(SafeBaseView)
function TianShenLingHeBagView:__init()
	self.view_layer = UiLayer.Pop
    self.view_cache_time = 0
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function TianShenLingHeBagView:LoadConfig()
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(1, -3), sizeDelta = Vector2(1102, 589)})
	self:AddViewResource(0, "uis/view/tianshen_linghe_ui_prefab", "layout_linghe_bag")
end

function TianShenLingHeBagView:__delete()

end

function TianShenLingHeBagView:ReleaseCallBack()
	-- if self.bag_grid then
	-- 	self.bag_grid:DeleteMe()
	-- 	self.bag_grid = nil
	-- end

    if self.bag_cell_list then
        self.bag_cell_list:DeleteMe()
		self.bag_cell_list = nil
    end
end

function TianShenLingHeBagView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TianShenLingHe.BagTitle

    -- if nil == self.bag_grid then
    --     self.bag_grid = AsyncBaseGrid.New()
    --     self.bag_grid:SetStartZeroIndex(false)
    --     self.bag_grid:CreateCells({
    --         col = 2,
    --         cell_count = 8,
    --         list_view = self.node_list["bag_list"],
    --         itemRender = LingHeBagItem,
    --         assetBundle = "uis/view/tianshen_linghe_ui_prefab",
    --         assetName = "linghe_bag_cell",
    --         change_cells_num = 1,
    --     })
    --     self.bag_grid:SetSelectCallBack(BindTool.Bind(self.OnBagSelectCB, self))
    -- end

    if not self.bag_cell_list then
        self.bag_cell_list = AsyncListView.New(LingHeBagItem, self.node_list["ph_select_list_view"])
        self.bag_cell_list:SetSelectCallBack(BindTool.Bind(self.OnBagSelectCB, self))
        self.bag_cell_list:SetDefaultSelectIndex(nil)
    end

    XUI.AddClickEventListener(self.node_list.btn_inlay, BindTool.Bind(self.OnClickInlay, self))
    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickGet, self))
end

function TianShenLingHeBagView:SetDataAndOpen(bag_list, ts_index, slot_index)
    self.bag_list = bag_list or {}
	self.ts_index = ts_index
    self.slot_index = slot_index
    self:Open()
end

function TianShenLingHeBagView:OnFlush()
    local have_num = #self.bag_list
    self.node_list["bag_num"].text.text = string.format(Language.TianShenLingHe.BagItemNum, have_num, TianShenLingHeWGData.MAX_BAG)
    self.node_list["ph_select_list_view"]:SetActive(have_num > 0)
    self.node_list["no_linghe"]:SetActive(have_num <= 0)
    self.bag_cell_list:SetDataList(self.bag_list)
end

-- 选择列表项回调
function TianShenLingHeBagView:OnBagSelectCB(item)
	if nil == item or nil == item.data then
		return
	end

    self.select_bag_index = item.data.index
    self:OnClickInlay()
    self:Close()
end

function TianShenLingHeBagView:OnClickInlay()
    TianShenLingHeWGCtrl.Instance:SendOperateReq(TianShenLingHeWGData.OPERA_TYPE.ACT_CHANGE, self.ts_index, self.slot_index, self.select_bag_index)
end

function TianShenLingHeBagView:OnClickGet()
    ViewManager.Instance:Open(GuideModuleName.TianShenLingHeDrawView)
    self:Close()
end

-------------------------------------
-- 背包ItemRender
-------------------------------------
LingHeBagItem = LingHeBagItem or BaseClass(BaseRender)
function LingHeBagItem:LoadCallBack()
    self.lh_item = BaseLingHeCell.New(nil, self.node_list["ph_item"])

    if not self.arrow_tweener then
		self.arrow = self.node_list["img_remind"]
		self.arrow_tweener = self.arrow.gameObject.transform:DOAnchorPosY(24, 0.45):From()
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end
end

function LingHeBagItem:__delete()
    if self.lh_item then
        self.lh_item:DeleteMe()
        self.lh_item = nil
    end

    if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

function LingHeBagItem:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local item_id = self.data.item_id
    self.lh_item:SetData({item_id = item_id})
    self.node_list["lbl_name"].text.text = ItemWGData.Instance:GetItemName(item_id, nil, true)
    local attr_desc = ItemShowWGData.Instance:OnlyGetAttrDesc(item_id, COLOR3B.DEFAULT, COLOR3B.DEFAULT_NUM, " ")
    self.node_list["attr_text"].text.text = attr_desc
    local is_better = false
    local view = TianShenWGCtrl.Instance:GetTsView()
    if view and view.ul_select_slot_data then
        is_better = self.data.color > view.ul_select_slot_data.color
    end
    self.node_list["img_remind"]:SetActive(is_better)
end