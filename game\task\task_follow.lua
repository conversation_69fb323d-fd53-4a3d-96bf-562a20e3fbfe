require("game/task/task_follow_render")

--------------------------------------------------------------
--任务跟踪
--------------------------------------------------------------
TaskFollow = TaskFollow or BaseClass()
TaskFollow.Width = 280
TaskFollow.Height = 250
TaskFollow.Bar = 50
local cur_zhu_task = 0

function TaskFollow:__init()
	if TaskFollow.Instance then
		ErrorLog("[TaskFollow] Attemp to create a singleton twice !")
	end
	TaskFollow.Instance = self

	self.is_show_task_view = true
	self.task_prompt_data = {}
	self.task_sort_list = {}
	self.is_show = true
	self.mt_layout_follow = nil

	self.is_show_btn_transform = false
	self.replace_task_panel_view = nil --换了任务面板的试图

	self.task_data_list_change = BindTool.Bind1(self.OnTaskDataListChange, self)
    self.task_data_change = BindTool.Bind1(self.OnOneTaskDataChange, self)
    self.role_data_change = BindTool.Bind1(self.OnRoleAttrValueChange, self)
	TaskWGData.Instance:NotifyDataChangeCallBack(self.task_data_list_change, true)
	TaskWGData.Instance:NotifyDataChangeCallBack(self.task_data_change, false)
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level","capability", "prof","tianxiange_level"})

	self.day_count_change = GlobalEventSystem:Bind(OtherEventType.DAY_COUNT_CHANGE, BindTool.Bind1(self.DaycountChange, self))
	self.scene_change_complete = GlobalEventSystem:Bind(SceneEventType.SCENE_CHANGE_COMPLETE, BindTool.Bind1(self.OnSceneChangeComplete, self))
	self.main_btn_state_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_BTN_STATE, BindTool.Bind1(self.MainBtnStateChange, self))
	self.guild_change_event = GlobalEventSystem:Bind(OtherEventType.Guild_Change, BindTool.Bind1(self.OnGuildChange, self))
	--委托任务触发到任务栏监听
	self.task_change_event = GlobalEventSystem:Bind(OtherEventType.Task_Status_Change, BindTool.Bind1(self.OnTaskChange, self))

	self.delay_update_task_fun = BindTool.Bind(self.DelayUpdateTaskPanelShow, self)
end

function TaskFollow:__delete()
	TaskFollow.Instance = nil

	TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_list_change)
	TaskWGData.Instance:UnNotifyDataChangeCallBack(self.task_data_change)

    RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
    self.role_data_change = nil
	if nil ~= self.delay_sort_task_timer then
		GlobalTimerQuest:CancelQuest(self.delay_sort_task_timer)
		self.delay_sort_task_timer = nil
	end

	if self.day_count_change then
		GlobalEventSystem:UnBind(self.day_count_change)
		self.day_count_change = nil
	end

	if self.scene_change_complete then
		GlobalEventSystem:UnBind(self.scene_change_complete)
		self.scene_change_complete = nil
	end

	if self.main_btn_state_event then
		GlobalEventSystem:UnBind(self.main_btn_state_event)
		self.main_btn_state_event = nil
	end

	if self.guild_change_event then
		GlobalEventSystem:UnBind(self.guild_change_event)
		self.guild_change_event = nil
	end

	if self.task_change_event then
		GlobalEventSystem:UnBind(self.task_change_event)
		self.task_change_event = nil
	end

	if self.btn_effect then
		self.btn_effect = nil
	end

	if self.task_prompt_node ~= nil then
		self.task_prompt_node:removeFromParent()
		self.task_prompt_node = nil
	end

end

function TaskFollow:Init(mt_layout_root)
	self:InitTaskPrompt()
end

function TaskFollow:OnClickTask()
	self.is_show_task_view = true
	self:OnClickBarBtn(1)
	self:UpdateTaskPanelShow()
end

function TaskFollow:OnClickTeam()
	self.is_show_task_view = false
	self:OnClickBarBtn(2)
	self:UpdateTaskPanelShow()
end

--任务排序(各种原因引起的任务变化可能短时间内来好几个，延迟一点时间)
function TaskFollow:UpdateTaskPanelShow()
	if nil ~= self.delay_sort_task_timer then
		return
	end

	self.delay_sort_task_timer = GlobalTimerQuest:AddDelayTimer(self.delay_update_task_fun, 0.5)
end

function TaskFollow:DelayUpdateTaskPanelShow()
	self.delay_sort_task_timer = nil
	if self.is_show_task_view then
		local list = self:SortTask()
		GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE, list)

		self:FlushTaskPromptText(data_list)
	end
	self:UpdateTaskPromptNodePos()
end

function TaskFollow:OnClickBarBtn(index)
	if 1 == index then
		self.img_team:setOpacity(100)
		self.img_task:setOpacity(255)
	elseif 2 == index then
		self.img_team:setOpacity(255)
		self.img_task:setOpacity(100)
	end
end

-- 切换活动面板
function TaskFollow:OnClickTransform()
	local is_show_task = self.mt_layout_follow:isVisible()
	self.mt_layout_follow:setVisible(not is_show_task and MainuiWGData.CanShowTaskFollow())

	if self.is_updata_flag == nil then
		self.is_updata_flag = false
	end
end

function TaskFollow:GetLayoutFollowVisible()
	if self.mt_layout_follow then
		return self.mt_layout_follow:isVisible()
	end
	return true
end

function TaskFollow:CloseActPanel()
	self.mt_layout_follow:setVisible(self.is_show and MainuiWGData.CanShowTaskFollow())
	self.is_show_btn_transform = false
end

function TaskFollow:OnActivityDataChangeCallback(activity_type, status, next_time)
	if activity_type == ACTIVITY_TYPE.HUSONG  then
		self:UpdateTaskListView()
		self:UpdateTaskPanelShow()
	end
end

function TaskFollow:MainBtnStateChange(state)
	-- if state then
	-- 	self.mt_layout_follow:FadeIn(PLAY_TIME)
	-- 	self.mt_layout_follow:setVisible(MainuiWGData.CanShowTaskFollow())
	-- 	self.mt_layout_other:FadeIn(PLAY_TIME)
	-- 	self.mt_layout_other:setVisible(MainuiWGData.CanShowTaskFollow())
	-- else
	-- 	self.mt_layout_follow:FadeOut(PLAY_TIME)
	-- 	self.mt_layout_other:FadeOut(PLAY_TIME)
	-- end
	-- local function on_aciton_comoplete()
	-- 	self.mt_layout_follow:setVisible(state and MainuiWGData.CanShowTaskFollow())
	-- 	self.mt_layout_other:setVisible(state and MainuiWGData.CanShowTaskFollow())
	-- 	MammonBlessWGCtrl.Instance:UpdateMammonBlessVisible()
	-- 	EquipmentShenWGCtrl.Instance:UpdateHuizhanVisible()
	-- end
	-- MammonBlessWGCtrl.Instance:UpdateMammonBlessVisible()
	-- EquipmentShenWGCtrl.Instance:UpdateHuizhanVisible()
	-- self.action_timer = GlobalTimerQuest:AddDelayTimer(on_aciton_comoplete, PLAY_TIME)
end

function TaskFollow:OnTaskChange()
	self:UpdateTaskPanelShow()
end

function TaskFollow:OnGuildChange()
	-- body
	if 0 ~= GuildDataConst.GUILDVO.guild_id then return end
	self:UpdateTaskPanelShow()
end

function TaskFollow:OnRoleAttrValueChange(attr_name, value)
	if attr_name == "level" or attr_name == "capability" or attr_name == "prof" or attr_name == "tianxiange_level" then
		self:UpdateTaskListView()
	end
end

-- 当前主线是否卡任务
function TaskFollow:CanDoZhuTask(level)
	local cur_zhu_cfg = TaskWGData.Instance:GetTaskConfig(cur_zhu_task)
	return cur_zhu_cfg ~= nil and (level or RoleWGData.Instance.role_vo.level) >= cur_zhu_cfg.min_level
end

function TaskFollow:OnTaskDataListChange(reason)
	self:UpdateTaskListView()
	if reason == TASK_LIST_CHANGE_REASON_COMPLETED then
	end
end

function TaskFollow:OnOneTaskDataChange(task_id,reason)
	self:UpdateTaskListView()
end

--更新单个任务条
function TaskFollow:UpdateOneTaskItem(task_id)

end

function TaskFollow:DaycountChange(day_counter_id)
	if day_counter_id == -1 or
	   day_counter_id == DAY_COUNT.DAYCOUNT_ID_HUSONG_REFRESH_COLOR_FREE_TIMES or
	   day_counter_id == DAY_COUNT.DAYCOUNT_ID_HUSONG_TASK_VIP_BUY_COUNT or
	   day_counter_id == DAY_COUNT.DAYCOUNT_ID_ACCEPT_HUSONG_TASK_COUNT or
	   day_counter_id == DAY_COUNT.DAYCOUNT_ID_CAMP_TASK_COMPLETE_COUNT then
	   	self:UpdateTaskListView()
	end

	if day_counter_id == DAY_COUNT.DAYCOUNT_ID_MAMMONBLESS then
		self:UpdateTransformBtn()
	end
end

--刷新整个任务列表
function TaskFollow:UpdateTaskListView()
	self:UpdateTaskPanelShow()
	if TaskWGData.Instance:IsShowXinshouFollow() == false then
		self:UpdateTransformBtn()
	end
end

function TaskFollow:SortTask()
	local task_accepted_info_list = TaskWGData.Instance:GetTaskAcceptedInfoList()
	local task_can_accept_id_list = TaskWGData.Instance:GetTaskCapAcceptedIdList()
	local role_vo = RoleWGData.Instance.role_vo
	TaskWGData.Instance:ShowEffectTask(0)

	local com_task_list = {}
	local zhuxian_list = {}
	local accept_list = {}
	local can_accept_list = {}
	local task_notice_list = {}
	local task_data_list = {}
	local task_guild_build = {}
	local is_zero = true
	local task_cfg = nil
	local has_zhuxian_flag = false
	local has_richang_flag = false

	for k,v in pairs(task_accepted_info_list) do
		task_cfg = TaskWGData.Instance:GetTaskConfig(v.task_id)
		if task_cfg then
			if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
				has_richang_flag = true
			end

			if TaskWGData.Instance:RejectTask(task_cfg) then
				-- 这里的任务不需要显示
			elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
				-- 这里的任务不需要显示
			elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
				if not v.is_remove then
					zhuxian_list[#zhuxian_list] = task_cfg
					has_zhuxian_flag = true
					cur_zhu_task = task_cfg.task_id
				else
					has_zhuxian_flag = false
					cur_zhu_task = 0
				end
			elseif 1 == v.is_complete then
				com_task_list[#com_task_list + 1] = task_cfg
				if task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
					is_zero = false
				end
			elseif task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
				task_guild_build[#task_guild_build+1] = task_cfg
				accept_list[#accept_list + 1] = task_cfg
			else
				accept_list[#accept_list + 1] = task_cfg
			end
			self:AddSortIndexToConfig(task_cfg)
		else
			-- print_error("the task is no exist:::", v.task_id)
		end
	end

	for k,v in pairs(task_can_accept_id_list) do
		task_cfg = TaskWGData.Instance:GetTaskConfig(k)
		if task_cfg then
			if (task_cfg.task_type ~= GameEnum.TASK_TYPE_ZHUAN and not TaskWGData.Instance:IsCanShowTask(k)) then
				-- 这里的任务不需要显示
			elseif TaskWGData.Instance:RejectTask(task_cfg) then
				-- 这里的任务不需要显示
			elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then
				-- 这里的任务不需要显示
			elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then
				zhuxian_list[#zhuxian_list] = task_cfg
				has_zhuxian_flag = true
				cur_zhu_task = task_cfg.task_id
			elseif task_cfg.task_type == GameEnum.TASK_TYPE_RI then
				-- 日常不做操作了
			elseif task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then
				task_guild_build[#task_guild_build+1] = task_cfg
				accept_list[#accept_list + 1] = task_cfg
			else
				accept_list[#accept_list + 1] = task_cfg
			end
			self:AddSortIndexToConfig(task_cfg)
		else
			print_error("the task is no exist:::", k)
		end
	end

	--仙盟建设假的任务
	local is_finish = GuildWGData.Instance:GetBuildTaskIsAllFinish()
	local is_need_task =  GuildWGData.Instance:GetGuildBuildTaskListIsShow()
	if is_zero and #task_guild_build == 0 and is_need_task and not is_finish then
		local client_task_cfg = TaskWGData.Instance:GetTaskConfig(GUILD_BUILD_TASK_OTHER_TYPE.CLIENT_SHOW_TASK_ID)
		-- task_guild_build[#task_guild_build+1] = client_task_cfg
		accept_list[#accept_list + 1] = client_task_cfg
	end

	-- 无日常情况下，检测十环奖励，未领取时，给它一个虚日常
	-- if not has_richang_flag then
	-- 	local task_data = DailyWGData.Instance:GetTaskTuMoData()
	-- 	if task_data.commit_times > COMMON_CONSTS.TASK_DAILY_DAY_MAX_COUNT and task_data.has_fetch_complete_all_reward ~= ShangJinBox.AllDone then
	-- 		task_cfg = TaskWGData.Instance:GetFirstTaskCfgByType(GameEnum.TASK_TYPE_RI)
	-- 		com_task_list[#com_task_list + 1] = task_cfg
	-- 		has_richang_flag = true
	-- 	end
	-- end

	if not has_richang_flag then
		local ri_tab = TaskWGData.Instance:GetVirtualRiTask()
		if ri_tab ~= nil then
			accept_list[#accept_list + 1] = ri_tab
		end
	end

	local function FindTaskById(task_id)
		for k,v in pairs(com_task_list) do
			if v.task_id == task_id then
				return v
			end
		end

		for k,v in pairs(accept_list) do
			if v.task_id == task_id then
				return v
			end
		end

		for k,v in pairs(can_accept_list) do
			if v.task_id == task_id then
				return v
			end
		end
	end

	local sections_id = 0
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local section_level = ConfigManager.Instance:GetAutoConfig("tasklist_auto").section_level
	for k,v in pairs(section_level) do
		if v.min_level <= main_role_vo.level and main_role_vo.level <= v.max_level then
			sections_id = v.sections_id
			break
		end
	end

	local function task_list_sort(a, b)
		local a_s = TaskWGData.Instance:GetTaskSortWeight(a.task_id, sections_id)
		local b_s = TaskWGData.Instance:GetTaskSortWeight(b.task_id, sections_id)
		return a_s > b_s
	end

	if #accept_list ~= 0 then
		table.sort(accept_list, task_list_sort)
	end
	if #can_accept_list ~= 0 then
		table.sort(can_accept_list, task_list_sort)
	end
	if #com_task_list ~= 0 then
		table.sort(com_task_list, task_list_sort)
	end

    for k,v in pairs(zhuxian_list) do
        task_data_list[#task_data_list + 1] = v	
		if 0 == #com_task_list and 0 == TaskWGData.Instance:ShowEffectTask() then
			TaskWGData.Instance:ShowEffectTask(v.task_id)
		end
    end

	if not has_zhuxian_flag then
		local zhu_task_cfg = TaskWGData.Instance:GetNextZhuTaskConfig()
		if zhu_task_cfg then
			task_data_list[#task_data_list + 1] = zhu_task_cfg
			cur_zhu_task = zhu_task_cfg.task_id
		end
    end
    --修真任务
    if TaskWGData.Instance:IsShowXiuzhenRoadTask() then
        local xiuzhen_task_cfg = TaskWGData.Instance:GetXiuzhenRoadTask()
        task_data_list[#task_data_list + 1] = xiuzhen_task_cfg
    end

    --护送特殊任务
    if TaskWGData.Instance:IsShowHuSongSpcTask() then
        local husong_spc_task_cfg = TaskWGData.Instance:GetHuSongSpcTask()
        task_data_list[#task_data_list + 1] = husong_spc_task_cfg
    end
	
	--委托任务
	if TaskWGData.Instance:IsShowAssignTask() then
		local assign_task_cfg = TaskWGData.Instance:GetAssignTask()
		task_data_list[#task_data_list + 1] = assign_task_cfg
	end

	for k,v in pairs(com_task_list) do
		task_data_list[#task_data_list + 1] = v
	end

	for k,v in pairs(accept_list) do
		task_data_list[#task_data_list + 1] = v
	end

	--合并可接
	for k,v in pairs(can_accept_list) do
		task_data_list[#task_data_list + 1] = v
	end
	--合并预告
	for k,v in pairs(task_notice_list) do
		task_data_list[#task_data_list + 1] = v
	end
	return task_data_list
end

--为任务增加排序索引，勿模防,
 --主线、护送、日常、仙盟、支线
function TaskFollow:AddSortIndexToConfig(task_cfg)
	if task_cfg and self.task_sort_list[task_cfg.task_id] == nil then
		local order_index = 0
		if task_cfg.task_type == GameEnum.TASK_TYPE_ZHU then 	--主线
			order_index = 1000000 + task_cfg.task_id
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_HU then   --护送
			order_index = 2000000 + task_cfg.task_id
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN then 	 --转职
			order_index = 3000000 + task_cfg.task_id
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_RI then   --日常
			order_index = 4000000 + task_cfg.task_id
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_MENG or task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD then  --仙盟
			order_index = 5000000 + task_cfg.task_id

		elseif task_cfg.task_type == GameEnum.TASK_TYPE_ZHI then  --支线
			order_index = 6000000 + task_cfg.task_id
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_CAMP then 	--阵营
			order_index = 7000000 + task_cfg.task_id
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_HUAN then 	--跑环
			order_index = 8000000 + task_cfg.task_id
		elseif task_cfg.task_type == GameEnum.TASK_TYPE_SHANG then 	 --悬赏
			order_index = 9000000 + task_cfg.task_id
		end

		self.task_sort_list[task_cfg.task_id] = order_index
	end
end



function TaskFollow:SetVisible(value)
	local boo = self:ActiviIsOpen()
	self.mt_layout_follow:setVisible(value and MainuiWGData.CanShowTaskFollow() and not boo)
	-- if TaskFollow:IsZhuXianComplete() then
	-- 	MammonBlessWGCtrl.Instance:UpdateMammonBlessVisible()
	-- end
end

function TaskFollow:GetVisible()
	return self.mt_layout_follow:isVisible()
end

function TaskFollow:SetAllViewVisible(value)
	if value then
		self.mt_layout_follow:setVisible(MainuiWGData.CanShowTaskFollow())
	else
		self.mt_layout_follow:setVisible(value)
	end
	-- MammonBlessWGCtrl.Instance:UpdateMammonBlessVisible()
	-- EquipmentShenWGCtrl.Instance:UpdateHuizhanVisible()
end

function TaskFollow:GetTaskLayoutFollow()
	return self.mt_layout_follow
end

function TaskFollow:GetTaskLayoutOther()
	return self.mt_layout_other
end

--获得指引ui
function TaskFollow:GetGuideUI()
	if item ~= nil then
		return item:GetView(), nil
	end
	return nil, nil
end

-------------------------------------------------
--引导部分
-------------------------------------------------
function TaskFollow:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.MainTask then
		if item ~= nil and item:GetData() and item:GetData().task_type == GameEnum.TASK_TYPE_ZHU then
			return item:GetView(), BindTool.Bind1(item.OnClick, item)
		end
	end
	return nil, nil
end

function TaskFollow:ActiviIsOpen()
	local boo = false
	return boo
end

function TaskFollow:IsZhuXianComplete()
	return TaskWGData.Instance:GetTaskIsCompleted(1370)
end

function TaskFollow:UpdateTransformBtn()
	local btn_transform_visible = self:ActiviIsOpen()
	self.is_show_btn_transform = btn_transform_visible
end

function TaskFollow:OnSceneChangeComplete(old_scene_type,new_scene_type)
	if old_scene_type ~= new_scene_type then
		local boo = TaskFollow:IsZhuXianComplete()
		if boo == false then
			boo = true
		else
			boo = not self:ActiviIsOpen()
		end
		self:SetVisible(MainuiWGData.CanShowTaskFollow() and boo)
	end

	self:OnClickTask()
	self:UpdateTransformBtn()
end

-------------------------------------------------------------------------------
------------------------------ 务面板提示界面 ---------------------------------
-------------------------------------------------------------------------------

function TaskFollow:InitTaskPrompt()
	if not TaskWGData.Instance:GetTaskPromptIsOpen() then				--需要是再创建
		return
	end

end


function TaskFollow:FlushTaskPromptText(data_list)
	if self.task_prompt_node == nil then
		if TaskWGData.Instance:GetTaskPromptIsOpen() then
			self:InitTaskPrompt()
		else
			return
		end
		if task_prompt_node == nil then
			return
		end
	elseif not TaskWGData.Instance:GetTaskPromptIsOpen() then

		self.task_prompt_node:removeFromParent()
		self.task_prompt_node = nil
		return
	end

	local prompt_text = self.task_prompt_node:getChildByTag(902)
	local function removeTaskPromptText()
		if self.task_prompt_data.index ~= nil then

		end
	end

	if self.task_prompt_data.task_id ~= nil and prompt_text:getString() ~= "" then
		if TaskWGData.Instance:GetTaskIsCompleted(self.task_prompt_data.task_id) then
			removeTaskPromptText()
		else
			for k,v in ipairs(data_list)do
				if self.task_prompt_data.task_id == v.task_id then
					self.task_prompt_data.index = k
					return --上一个任务没有做完 更新一下位子结束
				end
			end
		end
	end

	local task_prompt_list = TaskWGData.Instance:GetTaskPromptList()
	local index = nil
	local text_string = ""
	local task_type = nil
	local task_id = nil
	local task_ri_data = nil
	local task_ri_index = nil
	local task_status = nil

	for k,v in ipairs(data_list)do
		if v.task_type == GameEnum.TASK_TYPE_ZHU then
			if RoleWGData.Instance.role_vo.level >= v.min_level then
				task_type = GameEnum.TASK_TYPE_ZHU
				task_id = v.task_id
				text_string = Language.TaskFollow.TaskPromptZhuXian
				task_status = TaskWGData.Instance:GetTaskStatus(v.task_id)
				index = k
				break
			end
		elseif v.task_type == GameEnum.TASK_TYPE_ZHI then
			for _, id in ipairs(task_prompt_list) do --特殊支线提示
				if v.task_id == id then
					task_type = GameEnum.TASK_TYPE_ZHI
					task_id = v.task_id
					text_string = Language.TaskFollow.TaskPromptZhiXian[v.task_id]
					task_status = TaskWGData.Instance:GetTaskStatus(v.task_id)
					index = k
					break
				end
			end

			if index ~= nil then
				break
			end
		elseif v.task_type == GameEnum.TASK_TYPE_RI then
			task_ri_data = v
			task_ri_index = k
		end
	end

	if index == nil and task_ri_data ~= nil then
		task_type = GameEnum.TASK_TYPE_RI
		task_id = task_ri_data.task_id
		text_string = Language.TaskFollow.TaskPromptRiChang
		task_status = TaskWGData.Instance:GetTaskStatus(task_ri_data.task_id)
		index = task_ri_index
	end

	if index ~= nil then
		if self.task_prompt_data.task_type == task_type then
			if self.task_prompt_data.task_type == GameEnum.TASK_TYPE_ZHI then 					--支线还有区分任务id
				if self.task_prompt_data.task_id == task_id then
					return
				end
			else
				return
			end
		end

		removeTaskPromptText()

		self.task_prompt_data.task_type = task_type
		self.task_prompt_data.task_id = task_id
		self.task_prompt_data.task_status = task_status
		self.task_prompt_data.index = index
		self.task_prompt_data.is_visible = true

		local prompt_text_size = prompt_text:getContentSize()
		local prompt_bg = self.task_prompt_node:getChildByTag(901)
		prompt_bg:setContentWH(prompt_text_size.width + 40, 60)
	else
		removeTaskPromptText()
	end
end

function TaskFollow:UpdateTaskPromptNodePos()
	if not self.task_prompt_data.is_visible then
		return
	end

	if self.task_prompt_node == nil or self.task_prompt_data == nil or self.task_prompt_data.index == nil then return end
	local prompt_text = self.task_prompt_node:getChildByTag(902)
	if not list_view:isVisible() or not self.is_show or prompt_text:getString() == "" or task_item == nil then
		self.task_prompt_node:setVisible(false)
		return
	end
	local item_node = task_item:GetView()
	local item_y = list_view:getInnerPosition().y + item_node:getPositionY()
	if 0 < item_y and item_y < list_view:getContentSize().height then
		self.task_prompt_node:setVisible(true)
		self.task_prompt_node:setPositionY(item_y)
	else
		self.task_prompt_node:setVisible(false)
	end
end


function TaskFollow:OnTaskItemCallback(item)
	if self.task_prompt_node == nil or self.task_prompt_data == nil or self.task_prompt_data.index == nil then return end
	if item ~= nil and item.index ~=  nil and item.index == self.task_prompt_data.index then
		self.task_prompt_data.is_visible = false
		self.task_prompt_node:setVisible(false)
	end
end

function TaskFollow:RestoreTaskPane()
	if self.replace_task_panel_view == nil then return end
	self.text_word_task_1.text.text = Language.Task.task_text
	self.text_word_task_2.text.text = Language.Task.task_text
	self.replace_task_panel_view:removeFromParent()
	if self.replace_task_panel_end_callback then
		self.replace_task_panel_end_callback()
		self.replace_task_panel_end_callback = nil
	end
	self.replace_task_panel_view = nil
end