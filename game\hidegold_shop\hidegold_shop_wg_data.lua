HideGoldShopWGData = HideGoldShopWGData or BaseClass()
function HideGoldShopWGData:__init()
	if HideGoldShopWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[HideGoldShopWGData] attempt to create singleton twice!")
		return
	end

	HideGoldShopWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_hide_gold_shop_auto")
    self.other_cfg = cfg.other[1]
    self.open_day_cfg = ListToMap(cfg.open_day, "grade")
    self.shop_cfg = ListToMapList(cfg.shop, "grade")
    self.shop_seq_cfg = ListToMap(cfg.shop, "grade", "seq")

	self.grade = 1 -- 档次
	self.shop_seq_list = {}
	self.buy_times_list = {}
end

function HideGoldShopWGData:__delete()
	HideGoldShopWGData.Instance = nil
	self.grade = nil
	self.shop_seq_list = nil
    self.buy_times_list = nil
end

function HideGoldShopWGData:SetAllInfo(protocol)
    self.grade = protocol.grade
    self.score = protocol.score
    self.refresh_times = protocol.refresh_times
    self.shop_seq_list = protocol.shop_seq_list
    self.buy_times_list = protocol.buy_times_list
end

--获取剩余积分
function HideGoldShopWGData:GetCurScore()
    return self.score or 0
end

--获取档位
function HideGoldShopWGData:GetCurGrade()
    return self.grade or 1
end

--获取主界面模型显示id
function HideGoldShopWGData:GetShowModelId()
    local cur_grade = self:GetCurGrade()
    if self.open_day_cfg[cur_grade] then
        return self.open_day_cfg[cur_grade].model_show_itemid
    end
end

--获取商店界面模型显示id
function HideGoldShopWGData:GetShopShowModelId()
    local cur_grade = self:GetCurGrade()
    if self.open_day_cfg[cur_grade] then
        return self.open_day_cfg[cur_grade].model_shangdain_show_itemid
    end
end

--获取积分描述
function HideGoldShopWGData:GetShowGoldDesc()
    return self.other_cfg.tips
end

function HideGoldShopWGData:GetRefreshCfg()
    return self.open_day_cfg[self.grade] or {}
end

--获取商品库配置
function HideGoldShopWGData:GetShopCfg()
    return self.shop_cfg[self.grade] or {}
end

--获取商品库索引配置
function HideGoldShopWGData:GetShopSeqCfg(seq)
    return (self.shop_seq_cfg[self.grade] or {})[seq] or {}
end

--获取商品已购次数
function HideGoldShopWGData:GetShopLimitTimes(seq)
    return self.buy_times_list[seq] or 0
end

--是否是限购商品
function HideGoldShopWGData:GetIsLimitShop(seq)
    local shop_cfg = self:GetShopSeqCfg(seq)
    if shop_cfg == nil then
        return false
    end

    return shop_cfg and shop_cfg.times_limit > 0 or false
end

--商品展示列表
function HideGoldShopWGData:GetShopShowList()
    local shop_list = {}
    if self.shop_seq_list == nil then
        return shop_list
    end

    local weight = 0

    for k, v in pairs(self.shop_seq_list) do
        local already_buy_times = self:GetShopLimitTimes(v) --已购次数
        local shop_cfg = self:GetShopSeqCfg(v)
        local is_limit_shop = self:GetIsLimitShop(v)
        if shop_cfg then
            weight = is_limit_shop and 0 or 100
            local data = {
                seq = v,
                times_limit = shop_cfg.times_limit,
                need_score = shop_cfg.need_score,
                item = shop_cfg.item,
                sort = v + weight,
            }
            table.insert(shop_list, data)
        end
    end

    SortTools.SortAsc(shop_list, "sort")
    return shop_list
end