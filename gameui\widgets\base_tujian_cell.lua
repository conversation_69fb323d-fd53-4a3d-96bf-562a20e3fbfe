BaseTuJianCell = BaseTuJianCell or BaseClass(BaseRender)

function BaseTuJianCell:__init(instance)
    local bundle, asset = ResPath.GetWidgets("TuJianCell")
    self:LoadAsset(bundle, asset, instance.transform)

    self.raw_icon = self.node_list["raw_icon"]
end

function BaseTuJianCell:__delete()
    self.raw_icon = nil
end

function BaseTuJianCell:OnFlush()
    if self.data == nil then
        return
    end

    local item_id = self.data.item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if item_cfg == nil then
        return
    end

    local tujian_cfg = ShanHaiJingWGData.Instance:GetTJResolveShowCfg(item_id)
    if tujian_cfg then
        local bundle, asset = ResPath.GetF2RawImagesPNG("a2_shj_tu" .. tujian_cfg.card_ID)
        self.raw_icon.raw_image:LoadSprite(bundle, asset, function ()
           self.raw_icon.raw_image:SetNativeSize()
        end)
    end
end
