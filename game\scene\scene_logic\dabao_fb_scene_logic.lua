DaBaoFbSceneLogic = DaBaoFbSceneLogic or BaseClass(CommonFbLogic)

function DaBaoFbSceneLogic:__init()
	self.open_view = false
end

function DaBaoFbSceneLogic:__delete()

end

function DaBaoFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenWGCtrl.Instance:OpenTaskFollow()
	-- XuiBaseView.CloseAllView()
	MapWGCtrl.Instance:MapClose()
	MapWGCtrl.Instance:DaBaoClose()
	
	MapWGData.Instance:SetServerTime(TimeWGCtrl.Instance:GetServerTime())
end

function DaBaoFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	FuBenWGCtrl.Instance:UpdataTaskFollow()
end

function DaBaoFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	-- UiInstanceMgr.Instance:CloseRewardAction()
end

--是否是挂机打怪的敌人
function DaBaoFbSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end

function DaBaoFbSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
end