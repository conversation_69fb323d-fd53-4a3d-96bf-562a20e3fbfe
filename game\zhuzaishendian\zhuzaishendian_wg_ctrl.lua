require("game/zhuzaishendian/zhuzaishendian_wg_data")
require("game/zhuzaishendian/zhuzaishendian_view")
require("game/zhuzaishendian/zhuzaishendianreward_view")
require("game/zhuzaishendian/zhuzaishendianzhongjie_view")
-- require("game/zhuzaishendian/zhuzaishendianfeipei_view")
require("game/zhuzaishendian/zhuzaishendian_fenpei_view2")

ZhuZaiShenDianWGCtrl = ZhuZaiShenDianWGCtrl or BaseClass(BaseWGCtrl)
function ZhuZaiShenDianWGCtrl:__init()
	if ZhuZaiShenDianWGCtrl.Instance then
		error("[ZhuZaiShenDianWGCtrl]:Attempt to create singleton twice!")
	end
	ZhuZaiShenDianWGCtrl.Instance = self

	self.data = ZhuZaiShenDianWGData.New()
	self.view = ZhuZaiShenDianView.New(GuideModuleName.ZhuZaiShenDian)
	self.reward_view = ZhuZaiRewardView.New()
	self.Endreward_view = ZhuZaiEndRewardView.New()
	-- self.FenPeiWard_view = ZhuZaiFenPeiRewardView.New()				--主宰神殿分配界面(旧)
	self.zhuzaishendian_fenpei_view = ZhuZaiShenDianFenPeiView.New()--主宰神殿分配界面(新)
	self.allocation_record_view = AllocationRecordView.New()
	self:RegisterAllProtocols()
end

function ZhuZaiShenDianWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSGuildBattleRankInfoReq)
	self:RegisterProtocol(SCGuildBattleRankInfo,"OnSCGuildBattleRankInfo")
	-- self:RegisterProtocol(CSGuildBattleWinDayRewardReq)
	self:RegisterProtocol(CSGuildAppointKeepWinReward)
	self:RegisterProtocol(SCGuildBattleKeepWinRewardRecordInfo,"OnSCGuildBattleKeepWinRewardRecordInfo")		--主宰神殿獎勵分配記錄  8502
	self:RegisterProtocol(CSGuildBattleKeepWinRewardAllocateRecord)												--请求主宰神殿獎勵分配記錄  8503
	self:RegisterProtocol(SCGuildBattleWinFirstRankFightResult,"OnSCGuildBattleWinFirstRankFightResult")		--仙盟战被终结
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.OnDayPass, self))
end

function ZhuZaiShenDianWGCtrl:MainuiOpenCreate()
	self:SendCSGuildBattleRankInfoReq()
end


function ZhuZaiShenDianWGCtrl:OnDayPass()
	self:SendCSGuildBattleRankInfoReq()
end

function ZhuZaiShenDianWGCtrl:OnSCGuildBattleRankInfo(protocol)
	self.data:SetGuildBattleRankInfo(protocol)
	GuildWGCtrl.Instance:BattleRanklist(protocol)
	self.view:Flush(TabIndex.zhuzaishendian)
	--ViewManager.Instance:FlushView(GuideModuleName.Rank,TabIndex.zhuzaishendian)
	-- GuildInviteWGCtrl.Instance:FlushInviteResultView()
	self.reward_view:Flush()
	if self.zhuzaishendian_fenpei_view:IsOpen() then
		self.zhuzaishendian_fenpei_view:Flush()
	end

	-- 屏蔽主宰神殿的内容
	-- RemindManager.Instance:Fire(RemindName.ZhuZaiShenDian)
	-- if protocol.xmz_winner_bazhu_role_id > 0 then
	-- 	BrowseWGCtrl.Instance:SendQueryRoleInfoReq(protocol.xmz_winner_bazhu_role_id, BindTool.Bind(self.QueryRoleInfoCallBack, self))
	-- else
	-- 	self.data:SetCityOwnerInfo(nil)
	-- 	self:CheckCityOwnerStatue()
	-- end
end

function ZhuZaiShenDianWGCtrl:QueryRoleInfoCallBack(info)
	self.data:SetCityOwnerInfo(info)
	if info.lover_info.lover_uid > 0 then
		BrowseWGCtrl.Instance:SendQueryRoleInfoReq(info.lover_info.lover_uid, BindTool.Bind(self.QueryRoleLoverInfoCallBack, self))
	else
		self.data:SetCityOwnerLover(nil)
		self:CheckCityOwnerStatue()
	end
end

function ZhuZaiShenDianWGCtrl:QueryRoleLoverInfoCallBack(info)
	self.data:SetCityOwnerLover(info)
	self:CheckCityOwnerStatue()
end

function ZhuZaiShenDianWGCtrl:CheckCityOwnerStatue()
	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.CityOwnerStatue)
	for k,v in pairs(fall_item_list) do
		v:UpdateStatue()
	end
end

function ZhuZaiShenDianWGCtrl:SendCSGuildBattleRankInfoReq(operate_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildBattleRankInfoReq)
	protocol.operate_type = operate_type or GUILD_BATTLE_INFO_REQ_TYPE.GUILD_BATTLE_INFO_REQ_TYPE_RANK
	protocol:EncodeAndSend()
end

function ZhuZaiShenDianWGCtrl:SendCSGuildBattleRewardReq(opera_type)
	-- local protocol = ProtocolPool.Instance:GetProtocol(CSGuildBattleWinDayRewardReq)
	-- protocol.opera_type = opera_type or 0
	-- protocol:EncodeAndSend()
	self:SendCSGuildBattleRankInfoReq()
end

function ZhuZaiShenDianWGCtrl:SendCSGuildAppointKeepWinReward(param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildAppointKeepWinReward)
	protocol.guild_id = param1 or 0
	protocol.beappoint_uid = param2 or 0
	protocol.reward_index = param3 or 0
	protocol:EncodeAndSend()
	self.view:Flush(TabIndex.zhuzaishendian)
	--ViewManager.Instance:FlushView(GuideModuleName.Rank,TabIndex.zhuzaishendian)
end

function ZhuZaiShenDianWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	if nil ~= self.Endreward_view then
		self.Endreward_view:DeleteMe()
		self.Endreward_view = nil
	end

	if nil ~= self.reward_view then
		self.reward_view:DeleteMe()
		self.reward_view = nil
	end

	-- if nil ~= self.FenPeiWard_view then
	-- 	self.FenPeiWard_view:DeleteMe()
	-- 	self.FenPeiWard_view = nil
	-- end

	if nil ~= self.allocation_record_view then
		self.allocation_record_view:DeleteMe()
		self.allocation_record_view = nil
	end

	if nil ~= self.zhuzaishendian_fenpei_view then
		self.zhuzaishendian_fenpei_view:DeleteMe()
		self.zhuzaishendian_fenpei_view = nil
	end

	ZhuZaiShenDianWGCtrl.Instance = nil
end

function ZhuZaiShenDianWGCtrl:Open(tab_index, param_t)
	self:SendCSGuildBattleRankInfoReq()
	--ViewManager.Instance:Open(GuideModuleName.Rank)
	self.view:Open(tab_index, param_t)

end

function ZhuZaiShenDianWGCtrl:Flush()
	if self.view then
		self.view:Flush(TabIndex.zhuzaishendian)
		--ViewManager.Instance:FlushView(GuideModuleName.Rank,TabIndex.zhuzaishendian)
	end
end

function ZhuZaiShenDianWGCtrl:OpenZhuZaiRewardView(index)
	if index then
		self.reward_view:SetIndex( index )
	end
	self.reward_view:Open()
	self.reward_view:Flush()
end
function ZhuZaiShenDianWGCtrl:OpenZhuZaiEndRewardView()
	self.Endreward_view:Open()
	self.Endreward_view:Flush()
end
function ZhuZaiShenDianWGCtrl:OpenZhuZaiFenPei()
	-- self.FenPeiWard_view:Open()
	-- self.FenPeiWard_view:Flush()
end

function ZhuZaiShenDianWGCtrl:OnFushFeiPeilist()
	-- self.FenPeiWard_view:Flush()
end

function ZhuZaiShenDianWGCtrl:CheckZhuZaiRemind(remind_id)
	-- if remind_id == RemindId.zhuzaishendian then
	-- 	return self.data:GetRewardNum()
	-- end
	return 0
end

function ZhuZaiShenDianWGCtrl:SetFenPeiType(type_index)
	-- self.FenPeiWard_view.open_type = type_index
end

function ZhuZaiShenDianWGCtrl:OpenZhuZaiShenDianFenPeiView(data)
	if not self.zhuzaishendian_fenpei_view:IsOpen() then
		self.zhuzaishendian_fenpei_view:SetBagData(data)
		self.zhuzaishendian_fenpei_view:Open()
	end
end

function ZhuZaiShenDianWGCtrl:OnSCGuildBattleKeepWinRewardRecordInfo(protocol)
	self.data:SetAllocationRecordInfo(protocol)
	if self.allocation_record_view:IsOpen() then
		self.allocation_record_view:Flush()
	end
end

function ZhuZaiShenDianWGCtrl:OnSCGuildBattleWinFirstRankFightResult(protocol)
	self.data:SetGuildBattleWinFirstRankFightResult(protocol)
	self.reward_view:Flush()
end

function ZhuZaiShenDianWGCtrl:FlushRewardView()
	if self.reward_view:IsOpen() then
		self.reward_view:Flush()
	end
end

function ZhuZaiShenDianWGCtrl:SendCSGuildBattleKeepWinRewardAllocateRecord()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildBattleKeepWinRewardAllocateRecord)
	protocol:EncodeAndSend()
end

function ZhuZaiShenDianWGCtrl:OpenAllocationRecordView()
	if not self.allocation_record_view:IsOpen() then
		self.allocation_record_view:Open()
	end
end


