ArenaTianTiRecordTip = ArenaTianTiRecordTip or BaseClass(SafeBaseView)

function ArenaTianTiRecordTip:__init()
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(810, 572)})
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_tianti_changlleng_record")
end

function ArenaTianTiRecordTip:OpenCallBack()
	ArenaTiantiWGCtrl.Instance:ReqFieldGetDetailRankInfo()
end

function ArenaTianTiRecordTip:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Field1v1.Arena_Record
	self.rank_list = AsyncListView.New(ArenaTianTiRecordRender, self.node_list.ph_ranking_list)
end

function ArenaTianTiRecordTip:ReleaseCallBack()
	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function ArenaTianTiRecordTip:OnFlush()
	local data_list = ArenaTianTiWGData.Instance:GetReportinfo()
	self.node_list.layout_blank_tip:SetActive(#data_list <= 0)
	self.rank_list:SetDataList(data_list)
end



ArenaTianTiRecordRender = ArenaTianTiRecordRender or BaseClass(BaseRender)

function ArenaTianTiRecordRender:__init()
	
end

function ArenaTianTiRecordRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_fight, BindTool.Bind(self.ClickFightBtn, self))
end

function ArenaTianTiRecordRender:ReleaseCallBack()
	
end

function ArenaTianTiRecordRender:OnFlush()
	--local  server_time = TimeWGCtrl.Instance:GetServerTime()
	--local time_table_server = os.date("*t", server_time)
	--local time_table_local = os.date("*t", self.data.challenge_time)
	-- if time_table_server.day == time_table_local.day then
	-- 	self.node_list.time.text.text = string.format("%02d:%02d", time_table_local.hour, time_table_local.min)
	-- else
	-- 	self.node_list.time.text.text = string.format(Language.Common.XXMXXD, time_table_local.month, time_table_local.day)
	-- end

	local desc = Language.Field1v1.TianTiRecord1
	if self.data.is_sponsor == 1 then --我挑战
		if self.data.is_win == 1 then
			desc = Language.Field1v1.TianTiRecord1
		else
			desc = Language.Field1v1.TianTiRecord2
		end
	else
		if self.data.is_win == 1 then
			desc = Language.Field1v1.TianTiRecord4
		else
			desc = Language.Field1v1.TianTiRecord3
		end
	end
	self.node_list.desc.text.text = string.format(desc, self.data.target_name .. "_s" .. self.data.server_id)
	local off_score = self.data.new_score - self.data.old_score
	self.node_list.text_score.text.text = off_score < 0 and off_score or "+" .. off_score
	self.node_list.image_down:SetActive(off_score < 0)
	self.node_list.image_up:SetActive(off_score > 0)
	self.node_list.btn_fight:SetActive(self.data.is_sponsor ~= 1)
end

function ArenaTianTiRecordRender:ClickFightBtn()
	if self.data.target_uuid then
		ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_FIGHT_BACK, self.data.target_uuid)
	end
end