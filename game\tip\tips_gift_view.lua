TipsGiftView = TipsGiftView or BaseClass(SafeBaseView)

--[[
local gift_info = {
	btn_click = fun,	-- 点击查看按钮回调函数
	title_img = "",		-- 左边大标题图片
	desc_img = "",		-- 描述标题图片
	desc_content = "",	-- 描述内容
	count_down = 0,		-- 倒计时
}
--]]

function TipsGiftView:__init(view_name)
	self.view_name = "TipsGiftView"
	self.view_layer = UiLayer.MainUILow
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:AddViewResource(0, "uis/view/tips/gifttips_prefab", "layout_gift_tip")
	--self:SetMaskBg()
end

function TipsGiftView:LoadCallBack()
	self:InitParam()
	self:InitListener()

	if not self.gift_reward_list then
		self.gift_reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.gift_reward_list:SetStartZeroIndex(true)
	end
end

function TipsGiftView:ReleaseCallBack()
	CountDownManager.Instance:RemoveCountDown("gift_tip_count_down")
	if self.gift_reward_list then
		self.gift_reward_list:DeleteMe()
		self.gift_reward_list = nil
	end
end

function TipsGiftView:InitParam()
	self.gift_info = nil
end

function TipsGiftView:InitListener()
	XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind1(self.OnClickGoToBtn, self))
end

function TipsGiftView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "gift_info" then
			self.gift_info = v.gift_info
			self:RefreshView()
		end
	end
end

function TipsGiftView:RefreshView()
	if IsEmptyTable(self.gift_info) then
		return
	end

	self:FlushGiftDesc()
	--self:FlushGiftCountDown()
	-- self:FlushGiftTitleLeftImg()
	-- self:FlushGiftDescTitleImg()
end

-- function TipsGiftView:FlushGiftTitleLeftImg()
	-- local gift_info = self.gift_info
	-- if gift_info.title_img and gift_info.title_img ~= "" then
	-- 	local bundle = "uis/view/tips/gifttips/images_atlas"
	-- 	local asset = gift_info.title_img
	-- 	self.node_list.limit_gift_title.image:LoadSprite(bundle, asset, function ()
	-- 		self.node_list.limit_gift_title.image:SetNativeSize()
	-- 	end)
	-- 	self.node_list.limit_gift_title:SetActive(true)
	-- else
	-- 	self.node_list.limit_gift_title:SetActive(false)
	-- end
-- end

-- function TipsGiftView:FlushGiftDescTitleImg()
	-- local gift_info = self.gift_info
	-- if gift_info.desc_img and gift_info.desc_img ~= "" then
	-- 	local bundle = "uis/view/tips/gifttips/images_atlas"
	-- 	local asset = gift_info.desc_img
	-- 	self.node_list.desc_title_img.image:LoadSprite(bundle, asset, function ()
	-- 		self.node_list.desc_title_img.image:SetNativeSize()
	-- 	end)
	-- 	self.node_list.desc_title_img:SetActive(true)
	-- else
	-- 	self.node_list.desc_title_img:SetActive(false)
	-- end
-- end


function TipsGiftView:FlushGiftDesc()
	local gift_info = self.gift_info
	local gift_cfg = ServerActivityWGData.Instance:GetActKfGiftCfg(gift_info.libao_index)
	if not gift_cfg then
		return
	end

	local count_down = gift_info.count_down or 0
	if gift_info.is_new_gift then
		self.node_list.pass_time_desc.text.text = string.format(Language.LimitTimeGift.PassGiftTip5, gift_cfg.gift_name)
	else
		self.node_list.limit_time_desc.text.text = string.format(Language.LimitTimeGift.PassGiftTip4, gift_cfg.gift_name)
	end

	--self.node_list.limit_time_root:SetActive(not gift_info.is_new_gift)
	--self.node_list.pass_time_root:SetActive(gift_info.is_new_gift)

	if self.gift_reward_list then
		self.gift_reward_list:SetDataList(gift_cfg.reward)
	end
	self.node_list.lbl_show_discount.text.text = gift_cfg.discount
	self.node_list.lbl_origin_price.text.text = gift_cfg.old_price
	self.node_list.lbl_discount_price.text.text = gift_cfg.price
end

function TipsGiftView:FlushGiftCountDown()
	local gift_info = self.gift_info
	local count_down = gift_info.count_down or 0
	CountDownManager.Instance:RemoveCountDown("gift_tip_count_down")
	if count_down > 0 then
		self.node_list.limit_time_lbl.text.text = string.format(Language.LimitTimeGift.RemainTime, TimeUtil.FormatSecondDHM8(count_down)) 
		CountDownManager.Instance:AddCountDown(
			"gift_tip_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.Close, self),
			nil,
			count_down,
			1
		)
	end
end

function TipsGiftView:UpdateCountDown(elapse_time, total_time)
	self.node_list.limit_time_lbl.text.text = string.format(Language.LimitTimeGift.RemainTime, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
end

function TipsGiftView:OnClickGoToBtn()
	self:Close()
	local gift_info = self.gift_info
	if gift_info and gift_info.btn_click then
		gift_info.btn_click()
	end
end