﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Game_GameObjectAttachWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>ginClass(typeof(Game.GameObjectAttach), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("OnLoadComplete", OnLoadComplete);
		<PERSON><PERSON>unction("SetIsDisableEffect", SetIsDisableEffect);
		<PERSON><PERSON>ction("SetIsSceneOptimize", SetIsSceneOptimize);
		<PERSON><PERSON>Function("IsSceneOptimize", IsSceneOptimize);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON>.<PERSON>ar("delayTime", get_delayTime, set_delayTime);
		<PERSON><PERSON>ar("IsSyncLayer", get_IsSyncLayer, set_IsSyncLayer);
		<PERSON><PERSON>("BundleName", get_BundleName, set_BundleName);
		<PERSON><PERSON>("AssetName", get_AssetName, set_AssetName);
		<PERSON><PERSON>("Asset", get_Asset, set_Asset);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnLoadComplete(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)ToLua.CheckObject<Game.GameObjectAttach>(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.OnLoadComplete(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsDisableEffect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)ToLua.CheckObject<Game.GameObjectAttach>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetIsDisableEffect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetIsSceneOptimize(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				Game.GameObjectAttach obj = (Game.GameObjectAttach)ToLua.CheckObject<Game.GameObjectAttach>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.SetIsSceneOptimize(arg0);
				return 0;
			}
			else if (count == 3)
			{
				Game.GameObjectAttach obj = (Game.GameObjectAttach)ToLua.CheckObject<Game.GameObjectAttach>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				obj.SetIsSceneOptimize(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Game.GameObjectAttach.SetIsSceneOptimize");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsSceneOptimize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)ToLua.CheckObject<Game.GameObjectAttach>(L, 1);
			bool o = obj.IsSceneOptimize();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_delayTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			float ret = obj.delayTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index delayTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsSyncLayer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			bool ret = obj.IsSyncLayer;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsSyncLayer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BundleName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			string ret = obj.BundleName;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index BundleName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AssetName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			string ret = obj.AssetName;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AssetName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Asset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			Nirvana.AssetID ret = obj.Asset;
			ToLua.PushValue(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Asset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_delayTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.delayTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index delayTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsSyncLayer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsSyncLayer = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsSyncLayer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_BundleName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.BundleName = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index BundleName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AssetName(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.AssetName = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AssetName on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Asset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Game.GameObjectAttach obj = (Game.GameObjectAttach)o;
			Nirvana.AssetID arg0 = StackTraits<Nirvana.AssetID>.Check(L, 2);
			obj.Asset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Asset on a nil value");
		}
	}
}

