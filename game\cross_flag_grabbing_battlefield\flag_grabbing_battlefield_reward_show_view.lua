FlagGrabbingBattleFieldRewardShowView = FlagGrabbingBattleFieldRewardShowView or BaseClass(SafeBaseView)

function FlagGrabbingBattleFieldRewardShowView:__init()
	self:SetMaskBg()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 514)})
    self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_reward_show_view")
end

function FlagGrabbingBattleFieldRewardShowView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FlagGrabbingBattlefield.RewardTitle
    for i = 1, 3 do
        self.node_list["fgb_toggle" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickFGBToggle, self, i))
    end

    if not self.fgb_left_reward_list then
		self.fgb_left_reward_list = AsyncListView.New(FGBLeftRewardItemRender, self.node_list.fgb_left_reward_list)
        self.fgb_left_reward_list:SetStartZeroIndex(true)
	end
end

function FlagGrabbingBattleFieldRewardShowView:ReleaseCallBack()
    if self.fgb_left_reward_list then
        self.fgb_left_reward_list:DeleteMe()
		self.fgb_left_reward_list = nil
	end
end

function FlagGrabbingBattleFieldRewardShowView:ShowIndexCallBack()
	self.node_list["fgb_toggle1"].toggle.isOn = true
    self:OnClickFGBToggle(1, true)
end

function FlagGrabbingBattleFieldRewardShowView:OnFlush(param_t, key)

end

function FlagGrabbingBattleFieldRewardShowView:OnClickFGBToggle(index, is_on)
    if  is_on then
        local data
        local num = 0

        if index == 1 then
            self.fgb_left_reward_list:SetStartZeroIndex(true)
            data = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBPersonScoreRewardList()
            num = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCurScore()
        elseif index == 2 then
            self.fgb_left_reward_list:SetStartZeroIndex(true)
            data = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBPersonKillRewardList()
            num = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBCurKillNum()
        else
            self.fgb_left_reward_list:SetStartZeroIndex(false)
            data = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBPersonRankRewardList()
        end

        self.node_list.title1.text.text = Language.FlagGrabbingBattlefield.FGBRewardTypeCellDesc[index]

		if not IsEmptyTable(data) then
			self.fgb_left_reward_list:SetDataList(data)
		end

        --self.node_list.fgb_left_text.text.text = string.format(Language.FlagGrabbingBattlefield.FGBRewardPanelDesc[index], num)
	end
end

---------------------------------FGBLeftRewardItemRender--------------------------------
FGBLeftRewardItemRender = FGBLeftRewardItemRender or BaseClass(BaseRender)

function FGBLeftRewardItemRender:LoadCallBack()
    if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
	end
end

function FGBLeftRewardItemRender:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function FGBLeftRewardItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
    local desc_str = ""
    if self.data.need_score then
        desc_str = self.data.need_score
    elseif self.data.need_kill then
        desc_str = self.data.need_kill
    elseif self.data.min_rank then
        desc_str = self.data.min_rank
        if self.data.min_rank ~= self.data.max_rank then
            desc_str = string.format("%d-%d", self.data.min_rank, self.data.max_rank)
        end
    end

    -- local is_score_rank = nil ~= self.data.need_score
    -- local num = is_score_rank and self.data.need_score or self.data.need_kill
    -- local desc_id = is_score_rank and 1 or 2
    --local desc_str = Language.FlagGrabbingBattlefield.FGBRewardTypeCellDesc[desc_id]
    self.node_list.desc.text.text = desc_str
    self.reward_list:SetDataList(self.data.reward_item)
end