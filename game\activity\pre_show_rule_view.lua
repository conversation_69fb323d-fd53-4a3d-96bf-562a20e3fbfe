PreShowRuleView = PreShowRuleView or BaseClass(SafeBaseView)
local this = PreShowRuleView

function this:__init()
	self.view_cache_time = 30
	self.view_layer = UiLayer.Pop
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/zhuxie_ui_prefab", "show_play_explain")
end

function this:__delete()
	
end

function this:LoadCallBack(index)
	XUI.AddClickEventListener(self.node_list.close_btn, BindTool.Bind(self.Close, self))
end

function this:OpenIndexCallBack()
	local getpath = {
		[SceneType.KFZhuXieZhanChang] = ResPath.GetF2ZhuXieIcon,
		[SceneType.KF_HotSpring] = ResPath.GetF2HotSpringIcon,
	}
	local str = {
		[SceneType.KFZhuXieZhanChang] = Language.ShowRule.RuleTitle[0],
		[SceneType.KF_HotSpring] = Language.ShowRule.RuleTitle[1],
	}
	local tu_str = {
		[SceneType.KFZhuXieZhanChang] = "zx_chahua",
		[SceneType.KF_HotSpring] = "hot_spring",
	}
	local scene_type = Scene.Instance:GetSceneType()
	for i=1,4 do
		local b, a = getpath[scene_type](i)
		self.node_list["explain_img"..i].image:LoadSprite(getpath[scene_type]("explain_icon"..i))
		self.node_list["explain_img"..i].image:SetNativeSize()
		self.node_list["tu_"..i].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(tu_str[scene_type]..i))
		self.node_list["tu_"..i].raw_image:SetNativeSize()
	end
	self.node_list.expain_txt.text.text = str[scene_type]
end

function this:ReleaseCallBack()
	if self.close_load_view then
		GlobalEventSystem:UnBind(self.close_load_view)
		self.close_load_view = nil
	end
end

function this:OpenCallBack()

end

function this:CloseCallBack()
	
end