-- 礼包

function WelfareView:InitGiftView()
	self.input_send_num = self.node_list["inp_duihuan_num"]:GetComponent(typeof(TMPro.TMP_InputField))
	self.btn_libaoget = self.node_list.btn_libaoget
	XUI.AddClickEventListener(self.btn_libaoget, BindTool.Bind1(self.OnClickGiftHandler, self))
end

function WelfareView:DescoryGift()
	self.btn_libaoget = nil
	self.input_send_num = nil
end

-- 一个渠道只能领取一个类型的礼包一次
function WelfareView:OnClickGiftHandler(sender)
	local card = self.input_send_num.text
	if nil == card or "" == card then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.NoActID)
		return
	end

	self:FetchGift2(card)
end

function WelfareView:OnFlushGift()
	-- local exchange_reward_flag = WelfareWGData.Instance:GetExchangeRewardFlag()
	local cfg = WelfareWGData.Instance:GetWelfareOtherCfg()[1]
	self.node_list.reward:SetActive(false)--屏蔽 --exchange_reward_flag == 0)
	if cfg.exchange_key then
		self.node_list.reward.text.text = string.format(Language.Welfare.GetRewardTips, cfg.exchange_key)
	end
end

function WelfareView:FetchGift2(card)
	local url = GLOBAL_CONFIG.api_urls.client.use_card
	if not url or url == "" then
		return
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	local main_role_vo =  GameVoManager.Instance:GetMainRoleVo()
	local plat_id = CHANNEL_AGENT_ID
	local post_data = {
		access_token = user_vo.access_token or "",
		plat_id = plat_id or DEFAULT_AGENT_ID,
		server_id = user_vo.plat_server_id or 0,
		role_id = user_vo:GetOriginalRoleID() or 0,
		role_level = main_role_vo.level or 0,
		card = card,
	}

	PhpHandle.HandlePhpJsonPostRequest(url, post_data, post_data, function (cbData)
		local info = cbData.info
		if nil == info then return end

		if cbData.code == PHP_RESULT_CODE.SUCCESS then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.GoRewardBtnTips0)
		end
	end,
	function (cbData)
		local info = cbData and cbData.info
		if nil == info then return end

		if info == PHP_RESULT_INFO.PARAM_ERROR then
			-- 卡号无效
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.GoRewardBtnTips1)
		elseif info == PHP_RESULT_INFO.PLAT_LIMIT then
			-- 渠道无效
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.GoRewardBtnTips2)
		elseif info == PHP_RESULT_INFO.SERVER_INVALID then
			-- 区服无效
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.GoRewardBtnTips3)
		elseif info == PHP_RESULT_INFO.EXPIRED then
			-- 卡号过期
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.GoRewardBtnTips4)
		elseif info == PHP_RESULT_INFO.EXCEEDED_LIMIT then
			-- 领取次数限制
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.GoRewardBtnTips5)
		elseif info == PHP_RESULT_INFO.LEVEL_LIMIT then
			-- 等级限制
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.GoRewardBtnTips6)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Gift.GoRewardBtnTips1)
		end
	end)
end
