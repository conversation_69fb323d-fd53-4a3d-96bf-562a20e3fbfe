GoddessBlessingView = GoddessBlessingView or BaseClass(SafeBaseView)

function GoddessBlessingView:__init()
    self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
    self.full_screen = true
	self.is_safe_area_adapter = true
	self.is_big_view = true
    local common_bundle = "uis/view/common_panel_prefab"
    local bundle_name = "uis/view/recharge_volume_ui_prefab"
	self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
    self:AddViewResource(0, bundle_name, "layout_goddess_blessing_view")
    self:AddViewResource(0, common_bundle, "layout_a3_light_common_top_panel")
end

function GoddessBlessingView:__delete()

end

function GoddessBlessingView:ReleaseCallBack()
    if self.privilege_card_list then
        self.privilege_card_list:DeleteMe()
        self.privilege_card_list = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function GoddessBlessingView:CloseCallBack()

end

function GoddessBlessingView:LoadCallBack()
    self.cur_select_index = 1
    self.is_jump_flag = true

    self.node_list.title_view_name.text.text = Language.GoddessBlessing.TitleName
    local bundle, asset = ResPath.GetRawImagesPNG("a3_czj_ns_bj")
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)

    XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuyBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_buy_limit, BindTool.Bind(self.OnClickBuyLimit, self))

    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    if not self.privilege_card_list then
        self.privilege_card_list = AsyncListView.New(GoddessBlessingItemRender, self.node_list.privilege_card_list)
        self.privilege_card_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectPrivilegeCard, self))
    end

    --self:PlayAnim(-1)
end

function GoddessBlessingView:OnSelectPrivilegeCard(cell)
    if (not cell) or (not cell.data) then
		return
	end

    local data = cell.data
    if self.cur_select_index == data.seq then
        return
    end

    --local old_index = self.cur_select_index
    self.cur_select_index = data.seq
    --self:PlayAnim(old_index)
    self:FlushView()
end

-- function GoddessBlessingView:PlayAnim(old_index)
--     local time_txt_tween
--     for k, v in pairs(self.privilege_card_list) do
--         if k == self.cur_select_index then
--             self.privilege_card_list[k].node_list.root_node.transform:SetAsLastSibling()

--             time_txt_tween = self.privilege_card_list[k].node_list.content.transform:DOScale(1.23, 0.6)
--             time_txt_tween:SetEase(DG.Tweening.Ease.OutCubic)
--         end

--         if k == old_index then
--             time_txt_tween = self.privilege_card_list[k].node_list.content.transform:DOScale(1, 0.6)
--             time_txt_tween:SetEase(DG.Tweening.Ease.OutCubic)
--         end

--         self.privilege_card_list[k].node_list.select_img:SetActive(k == self.cur_select_index)
--         self.privilege_card_list[k].node_list.no_select_img:SetActive(k ~= self.cur_select_index)
--     end
-- end

function GoddessBlessingView:ShowIndexCallBack()
    self.is_jump_flag = true
end

function GoddessBlessingView:SetJumpFlag()
    self.is_jump_flag = true
end

function GoddessBlessingView:OnFlush()
    self:FlushView()
end

function GoddessBlessingView:FlushView()
    local rmb_cfg = RechargeVolumeWGData.Instance:GetRmbCfg()
    local cur_select_rmb_cfg = RechargeVolumeWGData.Instance:GetRmbCfgBySeq(self.cur_select_index)
    local buy_state = RechargeVolumeWGData.Instance:GetRmbBuyStateBySeq(self.cur_select_index)

    self.privilege_card_list:SetDataList(rmb_cfg)
    if self.is_jump_flag then
        self.is_jump_flag = false
        local default_index = RechargeVolumeWGData.Instance:GetDefaultSelectIndex()
        self.privilege_card_list:JumpToIndex(default_index, 3)
    end

    for i = 1, 3 do
        if cur_select_rmb_cfg["desc" .. i] and cur_select_rmb_cfg["desc" .. i] ~= "" then
            self.node_list["reward_desc" .. i]:SetActive(true)
            self.node_list["reward_desc" .. i].text.text = cur_select_rmb_cfg["desc" .. i]
        else
            self.node_list["reward_desc" .. i]:SetActive(false)
        end
    end

    self.reward_list:SetDataList(cur_select_rmb_cfg.reward_item)

    local price_str = RoleWGData.GetPayMoneyStr(cur_select_rmb_cfg.price, cur_select_rmb_cfg.rmb_type, cur_select_rmb_cfg.rmb_seq)
	self.node_list.btn_buy_text.text.text = string.format(Language.GoddessBlessing.BuyDesc, price_str)

    self.node_list.btn_buy:SetActive(buy_state == GODDESS_BLESSING_BUY_TYPE.CAN_BUY)
    self.node_list.btn_buy_limit:SetActive(buy_state == GODDESS_BLESSING_BUY_TYPE.CAN_NOT_BUY)
    self.node_list.buy_flag:SetActive(buy_state == GODDESS_BLESSING_BUY_TYPE.HAVE_BUY)
end

function GoddessBlessingView:OnClickBuyBtn()
    local cur_select_rmb_cfg = RechargeVolumeWGData.Instance:GetRmbCfgBySeq(self.cur_select_index)
    RechargeWGCtrl.Instance:Recharge(cur_select_rmb_cfg.price, cur_select_rmb_cfg.rmb_type, cur_select_rmb_cfg.rmb_seq)
end

function GoddessBlessingView:OnClickBuyLimit()
    local rmb_seq = RechargeVolumeWGData.Instance:GetRmbBuySeq()
    --local old_index = self.cur_select_index
    self.privilege_card_list:JumpToIndex(rmb_seq + 1, 3)
    --self:PlayAnim(old_index)
end

----------------------------------特权卡item-----------------------
GoddessBlessingItemRender = GoddessBlessingItemRender or BaseClass(BaseRender)
function GoddessBlessingItemRender:__init()

end

function GoddessBlessingItemRender:LoadCallBack()

end

function GoddessBlessingItemRender:ReleaseCallBack()

end

function GoddessBlessingItemRender:OnFlush()
    if not self.data then return end

    self.node_list.name.text.text = self.data.name
    local bundle, asset = ResPath.GetRawImagesPNG(self.data.bg)
    self.node_list.role_bg.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["role_bg"].raw_image:SetNativeSize()
    end)
end

function GoddessBlessingItemRender:OnSelectChange(is_select)
    self.node_list.select_img:SetActive(is_select)
    self.node_list.role_bg.raw_image.color = is_select and StrToColor(COLOR3B.WHITE) or StrToColor(COLOR3B.GRAY)
end