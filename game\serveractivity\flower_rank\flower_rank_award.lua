FlowerRankAwardView = FlowerRankAwardView or BaseClass(SafeBaseView)
--活动奖励

function FlowerRankAwardView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/flower_rank_ui_prefab", "layout_act_award")
end

function FlowerRankAwardView:__delete()

end

function FlowerRankAwardView:ReleaseCallBack()
	if self.award_list then 
		self.award_list:DeleteMe()
		self.award_list = nil
	end
end

function FlowerRankAwardView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Activity.RechargeRankTopText[1]
	self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(837,679)
	-- self.layout_tip = self.node_list.layout_tip
	-- self.layout_tip:setVisible(false)
	XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind1(self.onClickTip, self))
	-- XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind1(self.onClickCloseTip, self))
	XUI.AddClickEventListener(self.node_list.btn_need_meili, BindTool.Bind1(self.onClickNeddMeili, self))
end

function FlowerRankAwardView:ShowIndexCallBack(index)
	self:Flush()
end

function FlowerRankAwardView:OnFlush()
	local meili = FlowerRankWGData.Instance:GetRoleMeili()
	local index = FlowerRankWGData.Instance:GetRoleRankIndex()
	self:FLushActivityAwradList()
	self.node_list.rich_rank_value.text.text = index
	self.node_list.rich_meili.text.text = meili

end
--刷新活动奖励列表
function FlowerRankAwardView:FLushActivityAwradList()
	if not self.award_list then
		self.award_list = AsyncListView.New(AwardListItemRender,self.node_list.ph_award_list)
	end
	local reward_list = FlowerRankWGData.Instance:GetRankAward()
	self.award_list:SetDataList(reward_list,3)
end

function FlowerRankAwardView:onClickTip()
	RuleTip.Instance:SetContent(Language.Flower.RankTips , Language.Flower.RankTitle)	
end

--点击我要魅力
function FlowerRankAwardView:onClickNeddMeili()
	FlowerWGCtrl.Instance:OpenSendFlowerView(0, Language.Marry.SendTips)
end

--------------------------------------------------------
			--活动奖励列表Item
--------------------------------------------------------
AwardListItemRender = AwardListItemRender or BaseClass(BaseRender)
function AwardListItemRender:__init()
	self.cell_list = {}
	self:CreateChild()
end
function AwardListItemRender:__delete()
	if self.cell_list then 
		for i = 1,5 do 
			self.cell_list[i]:DeleteMe()
			self.cell_list[i] = nil
		end
	end 
	self.cell_list = nil
end
function AwardListItemRender:CreateChild()
	for i = 1, 5 do
		local param = self.node_list["ph_item_cell_" .. i]
		self.item_cell = ItemCell.New(param)
		self.cell_list[i] = self.item_cell
	end
end

function AwardListItemRender:OnFlush()
	if self.data == nil then return end
	if self.data.high_rank ==  self.data.row_rank then 
		self.node_list.lbl_rank_value.text.text = (string.format(Language.Flower.RankIndex,self.data.high_rank))
	else
		self.node_list.lbl_rank_value.text.text = (string.format(Language.Flower.RankIndexGotoIndex,self.data.high_rank,self.data.row_rank))
	end
	local role_vo =  GameVoManager.Instance:GetMainRoleVo()
	local item_list = {}
	if role_vo.sex == 1 then 
		item_list = ItemWGData.Instance:GetItemListInGift(self.data.male_reward_item[0].item_id)
	else
		item_list = ItemWGData.Instance:GetItemListInGift(self.data.female_reward_item[0].item_id)
	end
	for i = 1,#self.cell_list do 
		if nil ~= self.cell_list[i] then
			self.node_list['ph_item_cell_' .. i]:SetActive(true)
	  	 	self.cell_list[i]:SetData(item_list[i])
	  	else
			self.node_list['ph_item_cell_' .. i]:SetActive(false)
		end
	end
end

function AwardListItemRender:CreateSelectEffect()
end
