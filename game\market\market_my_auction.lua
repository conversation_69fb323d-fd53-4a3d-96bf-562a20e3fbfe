
-- 市场-拍卖-我的竞拍

function MarketView:MyAuctionLoadCallBack()
	self.my_aiction_item_list = AsyncListView.New(MarketAuctionCommonRender, self.node_list.ma_item_list)
	self.ma_click_flush = nil--点击页签刷新标志
	self.old_ma_info_list = nil--保存上一次刷新的数据顺序
end

function MarketView:MyAuctionReleaseCallBack()	
	if self.my_aiction_item_list then
		self.my_aiction_item_list:DeleteMe()
		self.my_aiction_item_list = nil
	end

	self.old_ma_info_list = nil
end

function MarketView:MyAuctionShowIndexCallBack()
	self.ma_click_flush = true
end

function MarketView:MyAuctionOnFlush(param_t)
	local index_list = MarketWGData.Instance:GetMyAuctionIndexList()
	self.node_list.ma_not_info:SetActive(false)
	self.node_list.ma_item_list:SetActive(true)
	-- print_error("------UID", GameVoManager.Instance:GetMainRoleVo().role_id)
	if index_list and not IsEmptyTable(index_list) then
		local new_info_list = {}
		local info = nil
		for _, index in pairs(index_list) do
			-- print_error("------我的竞拍列表刷新-----", index)
			if index then
				info = MarketWGData.Instance:GetAuctionInfoByIndex(index)
				if info then
					table.insert(new_info_list, info)
				end
			end
		end

		if IsEmptyTable(new_info_list) then
			self.node_list.ma_not_info:SetActive(true)
			self.node_list.ma_item_list:SetActive(false)
			return
		end

		if not self.ma_click_flush and self.old_ma_info_list and not IsEmptyTable(self.old_ma_info_list) then
			new_info_list = MarketWGData.Instance:SortAuctionInfoListByOld(new_info_list, self.old_ma_info_list)
		else--点击页签刷新,才重新排序
			-- table.sort(new_info_list, SortTools.KeyLowerSorters("end_time", "index"))
			table.sort(new_info_list, MarketWGData.Instance:AuctionDataListSort())
		end
		self.old_ma_info_list = new_info_list
		
		self.my_aiction_item_list:SetDataList(new_info_list)
		if self.ma_click_flush then
			self.my_aiction_item_list:JumpToTop()
		end
	else
		self.node_list.ma_not_info:SetActive(true)
		self.node_list.ma_item_list:SetActive(false)
	end

	self.ma_click_flush = false
end

function MarketView:FlushMyAuctionAllPart()
	
end
