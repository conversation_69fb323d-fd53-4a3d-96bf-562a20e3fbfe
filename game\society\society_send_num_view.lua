--赠送道具数量界面显示
SocietySendNumPanel = SocietySendNumPanel or BaseClass(SafeBaseView)

function SocietySendNumPanel:__init()
	self:LoadConfig()
	self.default_num = nil
	self.cur_num = 0
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function SocietySendNumPanel:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 440)})
	self:AddViewResource(0, "uis/view/offlinerest_ui_prefab", "layout_use_offline")
end

function SocietySendNumPanel:ReleaseCallBack()
	if self.offline_item then
		self.offline_item:DeleteMe()
		self.offline_item = nil
	end
	self.default_num = nil
	self.cur_num = 0
end

function SocietySendNumPanel:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(576,409)
	self.node_list.title_view_name.text.text = Language.Bag.SendMaterialNum

	self.node_list.slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSoundValueChange, self))
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind1(self.OnClickSub, self))
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind1(self.OnClickAdd, self))
	XUI.AddClickEventListener(self.node_list.btn_confirm, BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind1(self.OnClickCancel, self))

	self.offline_item = ItemCell.New(self.node_list.ph_offline_cell)
end

function SocietySendNumPanel:SetData(item_id, num,index)
	self.item_id = item_id
	self.default_num = num
	self.index = index
	self:Open()
end

function SocietySendNumPanel:OnFlush()
	--local item_num = ItemWGData.Instance:GetItemNumInBagById(self.item_id)
	local item_num = self.default_num
	self.offline_item:SetData({item_id = self.item_id, num = item_num, is_bind = 0})
	self.offline_item:SetRightBottomTextVisible(true)
	self.offline_item:SetRightBottomText(item_num)
	local item_cfg, _ = ItemWGData.Instance:GetItemConfig(self.item_id)
	local item_data = ItemWGData.Instance:GetItem(self.item_id)
	if item_cfg.use_daytimes and item_cfg.use_daytimes ~= 0 then
		local can_user_num = item_cfg.use_daytimes - ItemWGData.Instance:GetItemUseTimes(self.item_id)
		self.max = item_num <= can_user_num and item_num or can_user_num
		if self.max >= 999 then
			self.max = 999
		end
		self.node_list.slider.slider.maxValue = self.max
		if self.max == 1 then
			self.node_list.slider.slider.minValue = 0
			self.node_list.slider.slider.interactable = false
		else
			self.node_list.slider.slider.minValue = 1
			self.node_list.slider.slider.interactable = true
		end
		
		self.node_list.slider.slider.value = self.max
	else
		self.max = item_num
		if self.max >= 999 then
			self.max = 999
		end
		self.node_list.slider.slider.maxValue = self.max
		if self.max == 1 then
			self.node_list.slider.slider.minValue = 0
			self.node_list.slider.slider.interactable = false
		else
			self.node_list.slider.slider.minValue = 1
			self.node_list.slider.slider.interactable = true
		end
		self.node_list.slider.slider.value = self.max
	end


	if self.default_num then
		self.num = self.default_num
		self.cur_num = self.num
		self.node_list.lbl_num.text.text = self.default_num
	else
		self.num = item_num
		self.cur_num = self.num
		self.node_list.lbl_num.text.text = self.max
	end

	self.node_list.lbl_item_name.text.text = ItemWGData.Instance:GetItemName(self.item_id)
	local color = ItemWGData.Instance:GetItemColor(self.item_id)
end

function SocietySendNumPanel:OnClickConfirm()
	local num = tonumber(self.node_list.lbl_num.text.text)
	SocietyWGCtrl.Instance:ClickNumOK(self.item_id,num,self.index)
	self:Close()
end

function SocietySendNumPanel:OnClickCancel()
	self:Close()
end

function SocietySendNumPanel:OnClickSub()
	self.cur_num = self.node_list.lbl_num.text.text - 1
	self.cur_num = self.cur_num <= 1 and 1 or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function SocietySendNumPanel:OnClickAdd()
	self.cur_num = self.node_list.lbl_num.text.text + 1
	self.cur_num = self.cur_num >= self.max and self.max or self.cur_num
	self.node_list.lbl_num.text.text = GameMath.Round(self.cur_num)
	self.node_list.slider.slider.value = self.cur_num
end

function SocietySendNumPanel:OnSoundValueChange(float_param)
	self.node_list.lbl_num.text.text = float_param

end


--赠送道具数量界面显示
SocietySendRecordPanel = SocietySendRecordPanel or BaseClass(SafeBaseView)

function SocietySendRecordPanel:__init()
	self:LoadConfig()
	self.default_num = nil
	self.cur_num = 0
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function SocietySendRecordPanel:LoadConfig()
	self.default_index = 1
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_send_material_panel")
end

function SocietySendRecordPanel:ReleaseCallBack()
	if self.record_list then
		self.record_list:DeleteMe()
		self.record_list = nil
	end
end

function SocietySendRecordPanel:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(1060,651)
	self.node_list.title_view_name.text.text = Language.Bag.SendMaterialScore
	for i=1,2 do
		XUI.AddClickEventListener(self.node_list["btn_send_record_"..i], BindTool.Bind(self.FlushRecordList, self,i))
	end
	self.record_list = AsyncListView.New(PreventRecordRender, self.node_list.record_list_view)
	self:FlushRecordList(1)
end

function SocietySendRecordPanel:FlushRecordList(index)
	self.show_record = index - 1
	for i=1,2 do
		self.node_list["btn_material_select_"..i]:SetActive(index == i)
	end
	self:Flush()
end

function SocietySendRecordPanel:OnFlush()
	if nil == self.show_record or nil == self.record_list then return end
	local data_list = SocietyWGData.Instance:GetMySendRecord(self.show_record)
	--if IsEmptyTable(data_list) then return end
	table.sort(data_list,SortTools.KeyUpperSorter("from_time"))
	--print_error(data_list)
	local temp_sort_data_list = {}
	for k,v in pairs(data_list) do
		for i = 1,v.itemciount do
			if i == 1 then
				local item_data = {}
				item_data.from_time = v.from_time
				item_data.from_name = v.from_name
				item_data.history_type = v.history_type
				table.insert(item_data, v[1] )
				table.insert(temp_sort_data_list,item_data)
			else
				table.insert(temp_sort_data_list, v[i] )
			end
		end
	end
	--self.record_list:SetIsSendOrReceive(self.show_record)
	self.record_list:SetDataList(temp_sort_data_list, 0)

end


PreventRecordRender = PreventRecordRender or BaseClass(BaseRender)

function PreventRecordRender:__init()
end

function PreventRecordRender:OnFlush()
	local item_cfg

	if self.data.history_type then
		local time_tab = os.date("*t", self.data.from_time)
		local YMD = string.format(Language.Society.YMD,time_tab.year,time_tab.month,time_tab.day)
		local HMS = TimeUtil.FormatHMS(self.data.from_time)
		self.node_list.time_day.text.text = YMD .. "  "..HMS
		if self.data.history_type == 0 then 
			self.node_list.rich_content.text.text = string.format(Language.Society.SendRecordName,self.data.from_name) --"赠送"....":" --432912FF
		else
			self.node_list.rich_content.text.text = string.format(Language.Society.ReceiveRecordName,self.data.from_name)--"收到"..self.data.from_name.."的礼物:"
		end

		item_cfg =ItemWGData.Instance:GetItemConfig(self.data[1].item_id)

		if not IsEmptyTable(item_cfg) then
			local color = ITEM_COLOR[item_cfg.color]
			self.node_list.daoju_num.text.text = string.format(Language.Society.SendRecordMaterialNum,color,item_cfg.name,self.data[1].num)
		end
	else
		item_cfg =ItemWGData.Instance:GetItemConfig(self.data.item_id)

		if not IsEmptyTable(item_cfg) then
			local color = ITEM_COLOR[item_cfg.color]
			self.node_list.daoju_num.text.text = string.format(Language.Society.SendRecordMaterialNum,color,item_cfg.name,self.data.num)
		end

		self.node_list.time_day.text.text = ""
		self.node_list.rich_content.text.text = ""
	end
end

function PreventRecordRender:ReleaseCallBackR()
	self.mark = nil
end