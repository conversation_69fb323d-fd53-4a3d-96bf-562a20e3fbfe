GuiXuDreamView = GuiXuDreamView or BaseClass(SafeBaseView)

local SHOW_SUIT_TYPE = {
	XIANXIA = 1, --仙侠
	JIJIA = 2 --机甲
}

TOGGLE_MAX = 13

function GuiXuDreamView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/guixudream_ui_prefab", "layout_guixu_dream")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_light_common_top_panel")
	self:SetTabShowUIScene(0,{type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.GuiXuDream_1})
end

function GuiXuDreamView:__delete()

end

function GuiXuDreamView:OpenCallBack()
end

function GuiXuDreamView:CloseCallBack()
	self.select_list_index = -1
	self.select_change_type = nil
end

function GuiXuDreamView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.GuiXuDream.TitleName

	self.jump_suit_seq = nil
	self.select_list_index = -1
	self.select_suit_seq = -1
	self.suit_scroll_pos = 0

	-- if not self.suit_list_xianxia then
	-- 	self.suit_list_xianxia = AsyncListView.New(GuiXuDreamSuitCell, self.node_list.suit_list_xianxia)
	-- 	self.suit_list_xianxia:SetSelectCallBack(BindTool.Bind1(self.OnSelectSuitItemCB, self))
	-- end
	-- self.node_list.suit_list_xianxia.scroller.scrollerScrolled = BindTool.Bind(self.OnSuitScrollerScrolled, self)

	-- if not self.suit_list_jijia then
	-- 	self.suit_list_jijia = AsyncListView.New(GuiXuDreamSuitCell, self.node_list.suit_list_jijia)
	-- 	self.suit_list_jijia:SetSelectCallBack(BindTool.Bind1(self.OnSelectSuitItemCB, self))
	-- end

	--加载模型时装
	if nil == self.role_model then
		self.role_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["ph_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.L,
		-- 	can_drag = false,
		-- }
		
		-- self.role_model:SetRenderTexUI3DModel(display_data)
		self.role_model:SetUISceneModel(self.node_list["ph_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.role_model, 0)
	end

	self:CreateAccordionList()
	-- self.node_list.suit_list_xianxia.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnAttrScrollValueChanged, self)) --列表滑动监听
	-- self.node_list.suit_list_jijia.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnAttrScrollValueChanged, self)) --列表滑动监听
	XUI.AddClickEventListener(self.node_list["suit_attr_btn"], BindTool.Bind(self.OnClickGoAttr, self))
	XUI.AddClickEventListener(self.node_list["god_act_btn"], BindTool.Bind(self.OnClickGoAct, self))
	XUI.AddClickEventListener(self.node_list["info_skill"], BindTool.Bind(self.OnClickSkill, self))
	XUI.AddClickEventListener(self.node_list["change_type_btn"], BindTool.Bind(self.OnChangeSuitType, self))
end

function GuiXuDreamView:ReleaseCallBack()
	self.jump_suit_seq = nil
	self.select_list_index = nil
	self.select_suit_seq = nil

	if CountDownManager.Instance:HasCountDown("god_act_times") then
		CountDownManager.Instance:RemoveCountDown("god_act_times")
	end

	-- if self.suit_list_xianxia then
	-- 	self.suit_list_xianxia:DeleteMe()
	-- 	self.suit_list_xianxia = nil
	-- end

	-- if self.suit_list_jijia then
	-- 	self.suit_list_jijia:DeleteMe()
	-- 	self.suit_list_jijia = nil
	-- end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.lingchong_model then
		self.lingchong_model:DeleteMe()
		self.lingchong_model = nil
	end

	if self.xianwa_model then
		self.xianwa_model:DeleteMe()
		self.xianwa_model = nil
	end

	if self.mount_model then
		self.mount_model:DeleteMe()
		self.mount_model = nil
	end

	if self.suit_accordion_list then
		for k, v in pairs(self.suit_accordion_list) do
			v:DeleteMe()
		end

		self.suit_accordion_list = nil
	end

	if self.suit_cell_list then
		for _, list in pairs(self.suit_cell_list) do
			for _, v in pairs(list) do
				v:DeleteMe()
			end
		end

		self.suit_cell_list = nil
	end

	-- self.footprint_eff_t = nil
	-- self.is_foot_view = nil
	self.body_res_id = nil
	self.mount_res_id = nil
	self.have_foot_print = nil
	-- self.next_create_footprint_time = nil
	self.show_role_idel_ani = nil
	self.select_change_type = nil
	self.force_fulsh = nil
	self.suit_scroll_pos = nil
end

function GuiXuDreamView:ShowIndexCallBack()
	local remind, jump_suit, select_change_type = GuiXuDreamWGData.Instance:GetTotalRemindAndSuit()
	self.show_role_idel_ani = true
	self.select_change_type = select_change_type == 0 and 1 or select_change_type
	self:PlayLastAction()
end

function GuiXuDreamView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			self:FlushSuitList()
		elseif k == "show_suit_btn" then
			self:ShowSuiAttrBtn(true)
		elseif k == "flush_model" then
			self:PlayLastAction()
		end
	end

	self:FlushSkill()
end

function GuiXuDreamView:FlushSuitList()
	local curr_red_type = self.select_change_type
	if curr_red_type == SHOW_SUIT_TYPE.XIANXIA then
		curr_red_type = SHOW_SUIT_TYPE.JIJIA
	else
		curr_red_type = SHOW_SUIT_TYPE.XIANXIA
	end

	local other_list, _, is_red = GuiXuDreamWGData.Instance:GetSuitShowSplitList(curr_red_type)
	self.node_list.type_red:CustomSetActive(is_red)

	local show_list, jump_index, _ = GuiXuDreamWGData.Instance:GetSuitShowSplitList(self.select_change_type, self.select_list_index)
	self.jump_suit_seq = jump_index
	-- self.node_list["change_type_btn"]:SetActive(#other_list > 0 and #show_list > 0)
	-- local suit_list = self.select_change_type == SHOW_SUIT_TYPE.XIANXIA and self.suit_list_xianxia or
	-- 	self.suit_list_jijia

	-- if suit_list ~= nil then
	-- 	suit_list:SetDataList(show_list)
	-- 	if self.jump_suit_seq then
	-- 		suit_list:JumpToIndex(self.jump_suit_seq, 2)
	-- 		self.jump_suit_seq = nil
	-- 	end

		-- self:OnAttrScrollValueChanged()
	-- end

	if show_list then
		for k, v in pairs(self.suit_accordion_list) do
			v:SetData(show_list[k])
		end
	end

	if self.jump_suit_seq then
		self:FirstSelectAccordionList()
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	self.node_list.god_act_btn:SetActive(activity_info and activity_info.status == ACTIVITY_STATUS.OPEN)
	self:FlushLowerPanel()
	self:FlushActTimeCount()
	-- self:FulshShowChangeType()
end

function GuiXuDreamView:FlushActTimeCount()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local time = 0
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	else
		time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	end

	if time > 0 then
		if CountDownManager.Instance:HasCountDown("god_act_times") then
			CountDownManager.Instance:RemoveCountDown("god_act_times")
		end

		CountDownManager.Instance:AddCountDown("god_act_times",
			BindTool.Bind(self.FinalUpdateTimeCallBack, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function GuiXuDreamView:FinalUpdateTimeCallBack(now_time, total_time)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["act_time"].text.text = time_str
end

function GuiXuDreamView:OnComplete()
	self.node_list.act_time.text.text = ""
end

function GuiXuDreamView:FlushSkill()
	local skill_data = GuiXuDreamWGData.Instance:GetSkillBySeq(self.select_suit_seq)
	self.node_list.info_skill:SetActive(not IsEmptyTable(skill_data))
	if IsEmptyTable(skill_data) then
		return
	end

	local act = GuiXuDreamWGData.Instance:IsGuiXuDreamSkillAct(self.select_suit_seq)
	self.node_list.skill_lock:SetActive(not act)
	self.node_list.skill_name.text.text = skill_data.name_skill
	local bundle, asset = ResPath.GetSkillIconById(skill_data.icon_skill)
	self.node_list.img_skill_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.img_skill_icon.image:SetNativeSize()
	end)
end

function GuiXuDreamView:OnClickSkill()
	--策划临时要加的假技能显示
	local skill_data = GuiXuDreamWGData.Instance:GetSkillBySeq(self.select_suit_seq)
	if IsEmptyTable(skill_data) then
		return
	end

	local show_data = {
		icon = skill_data.icon_skill,
		top_text = skill_data.name_skill,
		body_text = skill_data.des_skill,
		show_bg_kunag = true,
		x = 0,
		y = 0,
		set_pos = true,
		hide_level = true,
		capability = skill_data.capability_inc,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function GuiXuDreamView:OnClickGoAct()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		ViewManager.Instance:Open(GuideModuleName.GodPchaseView)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.HmGosView.ActEnd)
	end
end

function GuiXuDreamView:OnSuitScrollerScrolled(scroller, val, scrollPosition)
	self.suit_scroll_pos = scrollPosition
end

function GuiXuDreamView:OnSelectSuitItemCB(item)
	if nil == item or nil == item.data then
		return
	end

	--拿到当前选中的格子下标
	local cell_index = item:GetIndex()
	for k, v in pairs(self.suit_accordion_list) do
		v:FlushHlImg(k == cell_index)
	end

	if self.select_list_index == cell_index and (not self.force_fulsh) then
		return
	end

	-- local cell_num = self.suit_list_xianxia:GetListViewNumbers()
	-- local cell_height = 136
	-- local viewport_height = self.node_list["suit_list_xianxia"].rect.sizeDelta.y
	-- local content_height = cell_num * cell_height
	-- local top_pos = (cell_index - 1) * cell_height
	-- local bottom_pos = (viewport_height - (cell_index * cell_height)) * -1
	-- if self.suit_scroll_pos > (top_pos + 1) then
	-- 	local percent = top_pos / (content_height - viewport_height)
	-- 	self.suit_list_xianxia:JumptToPrecent(percent)
	-- elseif self.suit_scroll_pos < (bottom_pos - 1) then
	-- 	local percent = bottom_pos / (content_height - viewport_height)
	-- 	self.suit_list_xianxia:JumptToPrecent(percent)
	-- end

	self.force_fulsh = nil
	local data = item.data
	self.select_list_index = cell_index
	self.select_suit_seq = data.suit
	local callback_func = function()
		self:FlushSuitModel()
		self:FlushLowerPanel()
		self:FulshShowBigBg()
		self:FlushSkill()
		self:FlushUIScene(data.ui_scene_config_index)
	end

	-- 播放CG
	local cg_bundle, cg_asset = data.cg_bundle, data.cg_asset
	if cg_bundle and cg_asset and (cg_bundle ~= "" and cg_asset ~= "") then
		-- print_error("----执行 播放CG-----", data.cg_bundle, data.cg_asset)
		CgManager.Instance:Play(UICg.New(cg_bundle, cg_asset), callback_func)
		return
	end

	callback_func()
end

function GuiXuDreamView:FlushUIScene(ui_scene_config_index)
	self.ui_scene_change_config_index = ui_scene_config_index
	Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.GuiXuDream_1, ui_scene_config_index)
end

function GuiXuDreamView:OnLoadCompleteWater(load_index, material)
	local cfg = ConfigManager.Instance:GetAutoConfig("ui_scene_config_auto").ui_scene or {}
	local asset_name
	for k,v in pairs(cfg) do
		if UI_SCENE_TYPE.DEFAULT == v.type then
			asset_name = v.asset_name
		end
	end
	Scene.Instance:ChangeUISceneWaterMaterial(asset_name,UI_SCENE_CONFIG_INDEX.GuiXuDream_1,material)
end
function GuiXuDreamView:OnLoadCompleteBgPanel(load_index, material)
	local cfg = ConfigManager.Instance:GetAutoConfig("ui_scene_config_auto").ui_scene or {}
	local asset_name
	for k,v in pairs(cfg) do
		if UI_SCENE_TYPE.DEFAULT == v.type then
			asset_name = v.asset_name
		end
	end
	Scene.Instance:ChangeUISceneBgPanelMaterial(asset_name,UI_SCENE_CONFIG_INDEX.GuiXuDream_1,material)
end

function GuiXuDreamView:OnLoadCompleteCharacterData(load_index, character_data)
	local cfg = ConfigManager.Instance:GetAutoConfig("ui_scene_config_auto").ui_scene or {}
	local asset_name
	for k,v in pairs(cfg) do
		if UI_SCENE_TYPE.DEFAULT == v.type then
			asset_name = v.asset_name
		end
	end
	Scene.Instance:ChangeUISceneCharacterData(asset_name,UI_SCENE_CONFIG_INDEX.GuiXuDream_1,character_data)
end

function GuiXuDreamView:FulshShowBigBg()
	if self.node_list.RawImage_tongyong then
		local suit_data = GuiXuDreamWGData.Instance:GetGuiXuDreamSuitInfoBySuit(self.select_suit_seq)
		local bundle, asset = ResPath.GetRawImagesPNG("a3_wdjx_bg" .. suit_data.badground)
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

-- 改变类型
-- function GuiXuDreamView:FulshShowChangeType()
-- 	self.node_list.type_1:CustomSetActive(self.select_change_type == SHOW_SUIT_TYPE.XIANXIA)
-- 	self.node_list.suit_list_xianxia:CustomSetActive(self.select_change_type == SHOW_SUIT_TYPE.XIANXIA)

-- 	self.node_list.type_2:CustomSetActive(self.select_change_type == SHOW_SUIT_TYPE.JIJIA)
-- 	self.node_list.suit_list_jijia:CustomSetActive(self.select_change_type == SHOW_SUIT_TYPE.JIJIA)
-- 	self.node_list.suit_list_jijia_bg:CustomSetActive(self.select_change_type == SHOW_SUIT_TYPE.JIJIA)
-- end

function GuiXuDreamView:FlushSuitModel()
	if not self.role_model then
		return
	end

	local show_list = WardrobeWGData.Instance:GetActivationPartList(self.select_suit_seq)
	if IsEmptyTable(show_list) then
		return
	end

	--清理掉回调
	-- self:ClearFootEff()
	self.role_model:RemoveAllModel()

	self.node_list["lc_root"]:SetActive(false)
	self.node_list["xw_root"]:SetActive(false)
	self.node_list["mount_root"]:SetActive(false)

	-- self.is_foot_view = false
	self.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	self.mount_res_id = 0
	self.have_foot_print = false
	local has_fashion_show = false

	local res_id, fashion_cfg
	for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				self.body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
				has_fashion_show = true
			end
		elseif data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
			self.have_foot_print = true
		end
	end


	local d_body_res, d_hair_res, d_face_res
	local main_role = not has_fashion_show and Scene.Instance:GetMainRole()
	local vo = main_role and main_role:GetVo()
	if not has_fashion_show and vo and vo.appearance then
		if vo.appearance.fashion_body == 0 then
			d_body_res = vo.appearance.default_body_res_id
			d_hair_res = vo.appearance.default_hair_res_id
			d_face_res = vo.appearance.default_face_res_id
		end
	end

	local animation_name = self.have_foot_print and SceneObjAnimator.Move or SceneObjAnimator.FallRest
	
	local extra_role_model_data = {
        d_face_res = d_face_res,
        d_hair_res = d_hair_res,
		d_body_res = d_body_res,
		animation_name = animation_name,
    }
	self.role_model:SetRoleResid(self.body_res_id, nil, extra_role_model_data)
	for k, v in pairs(show_list) do
		self:ShowModelByData(v)
	end

	self:ChangeModelShowScale()
end

function GuiXuDreamView:ShowModelByData(data)
	if IsEmptyTable(data) then
		return
	end

	local res_id, fashion_cfg
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
		if fashion_cfg then
			res_id = fashion_cfg.resouce
			if data.param1 == SHIZHUANG_TYPE.MASK then -- 脸饰
				self.role_model:SetMaskResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.BELT then -- 腰饰
				self.role_model:SetWaistResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WEIBA then -- 尾巴
				self.role_model:SetTailResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
				self.role_model:SetShouHuanResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
				self.role_model:SetHaloResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.WING then -- 羽翼
				self.role_model:SetWingResid(res_id, true)
			elseif data.param1 == SHIZHUANG_TYPE.FABAO then -- 法宝
				self.role_model:SetBaoJuResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
				self.role_model:SetJianZhenResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.SHENBING then -- 武器
				res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
				self.role_model:SetWeaponResid(res_id)
			elseif data.param1 == SHIZHUANG_TYPE.FOOT then -- 足迹
				self.role_model:SetFootTrailModel(res_id)
				self.role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
				self.role_model:PlayRoleAction(SceneObjAnimator.Move)

				-- self.is_foot_view = true
				-- self.foot_effect_id = res_id
				-- if not self.use_update then
				-- 	Runner.Instance:AddRunObj(self, 8)
				-- 	self.use_update = true
				-- end
			end
		end
	elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then -- 灵宠
		fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then -- 仙娃
		fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
		if fashion_cfg then
			self:SetXianWaModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.MOUNT then -- 坐骑
		fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.appe_image_id)
		end
	elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
		fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
		if fashion_cfg then
			self:SetMountModelData(fashion_cfg.active_id)
		end
	end
end

function GuiXuDreamView:SetLingChongModelData(type, res_id)
	self.node_list["lc_root"]:SetActive(true)
	if nil == self.lingchong_model then
		self.lingchong_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["lc_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.M,
		-- 	can_drag = false,
		-- }
		
		-- self.lingchong_model:SetRenderTexUI3DModel(display_data)
		self.lingchong_model:SetUISceneModel(self.node_list["lc_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.lingchong_model, 0)
	else
		if self.lingchong_model then
			self.lingchong_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetPetModel(res_id)

	self.lingchong_model:SetMainAsset(bundle, asset, function()
		self.lingchong_model:PlaySoulAction()
	end)

	self.lingchong_model:FixToOrthographic(self.root_node_transform)
end

function GuiXuDreamView:SetXianWaModelData(res_id)
	self.node_list["xw_root"]:SetActive(true)
	if nil == self.xianwa_model then
		self.xianwa_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["xw_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.S,
		-- 	can_drag = false,
		-- }
		
		-- self.xianwa_model:SetRenderTexUI3DModel(display_data)
		self.xianwa_model:SetUISceneModel(self.node_list["xw_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.xianwa_model, 0)
	else
		if self.xianwa_model then
			self.xianwa_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetHaiZiModel(res_id)
	self.xianwa_model:SetMainAsset(bundle, asset, function()
		self.xianwa_model:PlaySoulAction()
	end)

	self.xianwa_model:FixToOrthographic(self.root_node_transform)
end

function GuiXuDreamView:SetMountModelData(res_id)
	self.node_list["mount_root"]:SetActive(true)
	if nil == self.mount_model then
		self.mount_model = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["mount_display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.L,
		-- 	can_drag = false,
		-- }
		
		-- self.mount_model:SetRenderTexUI3DModel(display_data)
		self.mount_model:SetUISceneModel(self.node_list["mount_display"].event_trigger_listener,
		MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.mount_model, 0)
	else
		if self.mount_model then
			self.mount_model:ClearModel()
		end
	end

	local bundle, asset = ResPath.GetMountModel(res_id)

	self.mount_model:SetMainAsset(bundle, asset, function()
		self.mount_model:PlayMountAction()
	end)

	self.mount_model:FixToOrthographic(self.root_node_transform)
end

-- function GuiXuDreamView:Update(now_time, elapse_time)
-- 	if not self.is_foot_view then
-- 		return
-- 	end

-- 	if self.next_create_footprint_time == 0 then
-- 		self:CreateFootPrint()
-- 		self.next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
-- 	end

-- 	if self.next_create_footprint_time == nil then --初生时也是位置改变，不播
-- 		self.next_create_footprint_time = 0
-- 	end

-- 	if self.next_create_footprint_time > 0 and now_time >= self.next_create_footprint_time then
-- 		self.next_create_footprint_time = 0
-- 	end

-- 	self:UpdateFootprintPos()
-- end

-- function GuiXuDreamView:CreateFootPrint()
-- 	if nil == self.foot_effect_id then
-- 		return
-- 	end

-- 	if nil == self.footprint_eff_t then
-- 		self.footprint_eff_t = {}
-- 	end

-- 	local pos = self.role_model.draw_obj:GetRoot().transform
-- 	local bundle, asset = ResPath.GetUIFootEffect(self.foot_effect_id)
-- 	EffectManager.Instance:PlayControlEffect(self, bundle, asset,
-- 		Vector3(pos.position.x, pos.position.y, pos.position.z), nil, pos, nil, function(obj)
-- 		if obj then
-- 			if nil ~= obj then
-- 				if self.role_model then
-- 					obj.transform.localPosition = Vector3.zero
-- 					obj:SetActive(false)
-- 					obj:SetActive(true)
-- 					table.insert(self.footprint_eff_t, { obj = obj, role_model = self.role_model })
-- 					self.role_model:OnAddGameobject(obj)
-- 				else
-- 					ResPoolMgr:Release(obj)
-- 				end
-- 			end
-- 		end
-- 	end)

-- 	if #self.footprint_eff_t > 2 then
-- 		local obj = table.remove(self.footprint_eff_t, 1)
-- 		obj.role_model:OnRemoveGameObject(obj.obj)
-- 		if not IsNil(obj.obj) then
-- 			obj.obj:SetActive(false)
-- 		end
-- 	end
-- end

-- function GuiXuDreamView:UpdateFootprintPos()
-- 	if nil == self.footprint_eff_t then
-- 		return
-- 	end

-- 	for k, v in pairs(self.footprint_eff_t) do
-- 		if not IsNil(v.obj) then
-- 			local pos = v.obj.transform.localPosition
-- 			v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
-- 		end
-- 	end
-- end

-- function GuiXuDreamView:ClearFootEff()
-- 	if self.footprint_eff_t ~= nil then
-- 		for k, v in pairs(self.footprint_eff_t) do
-- 			if v.obj ~= nil and not IsNil(v.obj) and v.role_model ~= nil then
-- 				v.role_model:OnRemoveGameObject(v.obj)
-- 				v.obj:SetActive(false)
-- 			end
-- 		end
-- 	end

-- 	self.footprint_eff_t = {}
-- end

function GuiXuDreamView:FlushLowerPanel()
	if not self.select_suit_seq or self.select_suit_seq < 0 then
		return
	end

	local suit_data = GuiXuDreamWGData.Instance:GetGuiXuDreamSuitInfoBySuit(self.select_suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	local data_list = suit_data.part_list
	local cell_list = self.suit_cell_list[self.select_list_index]
	if cell_list then
		for i = 0, #cell_list do
			if cell_list[i] then
				cell_list[i]:SetData(data_list[i])
			end
		end
	end


	self:FlushCapStr(data_list)
	self.node_list.suit_red:SetActive(suit_data.can_act)
end

function GuiXuDreamView:FlushCapStr(info)
	local capability = 0
	for k, v in pairs(info) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.show_item_id)
		if item_cfg then
			capability = capability + ItemShowWGData.CalculateCapability(v.show_item_id)
		end
	end

	self.node_list.cap_value.text.text = capability
end

function GuiXuDreamView:ChangeModelShowScale()
	local data = WardrobeWGData.Instance:GetThemeCfgBySuit(self.select_suit_seq)
	if IsEmptyTable(data) then
		return
	end

	local pos_str = data.main_whole_display_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	end

	pos_str = data.main_pos
	if pos_str and pos_str ~= "" then
		local pos = Split(pos_str, "|")
		if self.role_model then
			-- self.role_model:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			self.role_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end
	end

	local rotate_str = data.main_rot
	if rotate_str and rotate_str ~= "" then
		local rot = Split(rotate_str, "|")
		if self.role_model then
			-- self.role_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			self.role_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end
	end

	local scale = data.main_scale
	if scale and scale ~= "" then
		if self.role_model then
			-- self.role_model:SetRTAdjustmentRootLocalScale(scale)
			self.role_model:SetUSAdjustmentNodeLocalScale(scale)
		end
	end

	--灵宠
	if self.node_list["lc_root"]:GetActive() then
		pos_str = data.pet_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.lc_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.pet_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.lingchong_model then
				-- self.lingchong_model:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
				self.lingchong_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.pet_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.lingchong_model then
				-- self.lingchong_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
				self.lingchong_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.lc_scale
		if scale and scale ~= "" then
			if self.lingchong_model then
				-- self.lingchong_model:SetRTAdjustmentRootLocalScale(scale)
				self.lingchong_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end

	--仙娃
	if self.node_list["xw_root"]:GetActive() then
		pos_str = data.xw_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.xw_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.xw_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.xianwa_model then
				-- self.xianwa_model:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
				self.xianwa_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.xw_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.xianwa_model then
				-- self.xianwa_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
				self.xianwa_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.xw_scale
		if scale and scale ~= "" then
			if self.xianwa_model then
				-- self.xianwa_model:SetRTAdjustmentRootLocalScale(scale)
				self.xianwa_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end

	--坐骑
	if self.node_list["mount_display"]:GetActive() then
		pos_str = data.mount_whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPosition3DXYZ(self.node_list.mount_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		pos_str = data.mount_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			if self.mount_model then
				-- self.mount_model:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
				self.mount_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
			end
		end

		rotate_str = data.mount_rot
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			if self.mount_model then
				-- self.mount_model:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
				self.mount_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
			end
		end

		scale = data.mount_scale
		if scale and scale ~= "" then
			if self.mount_model then
				-- self.mount_model:SetRTAdjustmentRootLocalScale(scale)
				self.mount_model:SetUSAdjustmentNodeLocalScale(scale)
			end
		end
	end
end

--如果列表滑动箭头提示
function GuiXuDreamView:OnAttrScrollValueChanged()
	local show_list, jump_index, is_red = GuiXuDreamWGData.Instance:GetSuitShowSplitList(self.select_change_type)
	if IsEmptyTable(show_list) then
		return
	end

	-- local suit_list = self.select_change_type == SHOW_SUIT_TYPE.XIANXIA and self.node_list.suit_list_xianxia or
	-- 	self.node_list.suit_list_jijia

	-- local length = #show_list
	-- local value = suit_list.scroll_rect.horizontalNormalizedPosition
	-- if length > 4 then
	-- 	self.node_list.left_jiantou:SetActive(value > 0)
	-- 	self.node_list.right_jiantou:SetActive(value < 1)
	-- else
	-- 	self.node_list.left_jiantou:SetActive(false)
	-- 	self.node_list.right_jiantou:SetActive(false)
	-- end
end

function GuiXuDreamView:OnClickGoAttr()
	if not self.select_suit_seq then
		return
	end

	GuiXuDreamWGCtrl.Instance:OpenGGuiXuDreamAttrView(self.select_suit_seq)

	self:ShowSuiAttrBtn(false)
end

function GuiXuDreamView:ShowSuiAttrBtn(is_show)
	self.node_list.suit_attr_btn:SetActive(is_show)
end

-- 切换展示类型
function GuiXuDreamView:OnChangeSuitType()
	if self.select_change_type == SHOW_SUIT_TYPE.XIANXIA then
		self.select_change_type = SHOW_SUIT_TYPE.JIJIA
	else
		self.select_change_type = SHOW_SUIT_TYPE.XIANXIA
	end

	self.force_fulsh = true
	self:FlushSuitList()
	self:FlushSkill()
end

function GuiXuDreamView:CreateAccordionList()
    if self.suit_accordion_list then
		return
	end

	local remind, jump_suit, select_change_type = GuiXuDreamWGData.Instance:GetTotalRemindAndSuit()
	local show_list, jump_index, _ = GuiXuDreamWGData.Instance:GetSuitShowSplitList(select_change_type)
	if not show_list then
		return
	end

    self.suit_accordion_list = {}
	self.suit_cell_list = {}
    self.first_index = #show_list > 0 and 1 or 0

    for i = TOGGLE_MAX, 1, -1 do
        local item_render = GuiXuDreamBigRender.New(self.node_list.suit_list_content:FindObj("SelectBtn" .. i))
        local accordion_data = show_list[i]
        local suit_seq = accordion_data and accordion_data.suit or -1
        item_render:SetToggleValueChangeCallBack(BindTool.Bind(self.OnSelectSuitItemCB, self, item_render))
		item_render:SetIndex(i)
        item_render:SetData(accordion_data)
        self.suit_accordion_list[i] = item_render
        self:LoadAccordionCell(i, suit_seq)
    end
end

function GuiXuDreamView:LoadAccordionCell(index, suit_seq)
	local suit_data = GuiXuDreamWGData.Instance:GetGuiXuDreamSuitInfoBySuit(suit_seq)
	if IsEmptyTable(suit_data) then
		return
	end

	local res_async_loader = AllocResAsyncLoader(self, "zwtq_accordion_item" .. index)
	res_async_loader:Load("uis/view/guixudream_ui_prefab", "part_item_cell", nil,
		function(new_obj)
            if IsNil(new_obj) then
                return
            end

			local item_vo = {}
			local data_list = suit_data.part_list
			for i = 0, #data_list do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
                local parent_list = self.suit_accordion_list[index]:GetSmallTypeList()
				obj_transform:SetParent(parent_list.transform, false)
				local item_render = GuiXuDreamSmallRender.New(obj)
                item_render:SetData(data_list[i]) --里层按钮赋值信息
				item_vo[i] = item_render
			end

			self.suit_cell_list[index] = item_vo
            if self.first_index > 0 and self.first_index == index then
				self:FirstSelectAccordionList()
            end
		end)
end

function GuiXuDreamView:FirstSelectAccordionList()
	local cell = self.suit_accordion_list[self.jump_suit_seq]
	if self.jump_suit_seq and cell then
		local is_first = self.select_list_index == -1
		if is_first then
			self:OnSelectSuitItemCB(cell)
		else
			local is_change = self.jump_suit_seq ~= self.select_list_index
			cell:SetToggleValue(true)
			if is_change then
				GuiXuDreamWGCtrl.Instance:FlushGuiXuDreamAttrView(self.select_suit_seq)
			end
		end


		self.jump_suit_seq = nil
	end
end

function GuiXuDreamView:PlayLastAction()
	if self.role_model then
		self.role_model:PlayLastAction()
	end

	if self.node_list.lc_root:GetActive() and self.lingchong_model then
		self.lingchong_model:PlayLastAction()
	end

	if self.node_list.xw_root:GetActive() and self.xianwa_model then
		self.xianwa_model:PlayLastAction()
	end

	if self.node_list.mount_display:GetActive() and self.mount_model then
		self.mount_model:PlayLastAction()
	end
end

------------- GuiXuDreamBigRender外层按钮 -------------
GuiXuDreamBigRender = GuiXuDreamBigRender or BaseClass(BaseRender)
local role_img_str = "a3_zwtq_role_%s_%s"
local big_cell_and_spacing_size = 90
local one_cell_size = 74
local one_cell_spacing = -8
function GuiXuDreamBigRender:OnFlush()
    local is_empty_data = not self.data
    self.view:SetActive(not is_empty_data)
    if is_empty_data then
        return
    end

	local suit_data = GuiXuDreamWGData.Instance:GetGuiXuDreamSuitInfoBySuit(self.data.suit)
	if suit_data then
		local cell_num = #suit_data.part_list
		local list_length = cell_num * one_cell_size - cell_num * one_cell_spacing + big_cell_and_spacing_size
		RectTransform.SetSizeDeltaXY(self.node_list.di.rect, list_length, one_cell_size)
	end

	local bg_bundle, bg_asset = ResPath.GetGuiXuDreamImg(self.data.zwtq_ui_bg)
	self.node_list.bg.image:LoadSprite(bg_bundle, bg_asset)

	local sex = RoleWGData.Instance:GetRoleSex()
	local icon_name = string.format(role_img_str, sex, self.data.zwtq_ui_icon)
	local icon_bundle, icon_asset = ResPath.GetGuiXuDreamImg(icon_name)
	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset)

	local title_bundle, title_asset = ResPath.GetGuiXuDreamImg(self.data.zwtq_ui_title)
	self.node_list.title_img.image:LoadSprite(title_bundle, title_asset)
	self.node_list.remind:CustomSetActive(self.data.can_act)
end

function GuiXuDreamBigRender:FlushHlImg(is_show)
	self.node_list.di:CustomSetActive(self.node_list.List:GetActive())
	self.node_list.hl_img:CustomSetActive(is_show)
end

function GuiXuDreamBigRender:GetSmallTypeList()
    return self.node_list.List
end

function GuiXuDreamBigRender:SetToggleValueChangeCallBack(call_back)
	self.view.toggle:AddValueChangedListener(call_back)
end

function GuiXuDreamBigRender:SetToggleValue(is_on)
	self.view.toggle.isOn = is_on
end

------------- GuiXuDreamSmallRender里层按钮 -------------
GuiXuDreamSmallRender = GuiXuDreamSmallRender or BaseClass(BaseRender)
function GuiXuDreamSmallRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["item_pos"])
    end
end

function GuiXuDreamSmallRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function GuiXuDreamSmallRender:OnFlush()
    if self.data == nil then
        return
    end

    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
        return
    end

    self.view:SetActive(true)
    self.item_cell:SetData({item_id = self.data.show_item_id})
    self.node_list.no_active_flag:CustomSetActive(self.data.state == REWARD_STATE_TYPE.UNDONE)
end
