﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FancyScrollView_FancyGridView_FancyScrollView_ContextWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(FancyScrollView.FancyGridView<FancyScrollView.Context>), typeof(FancyScrollView.FancyScrollRect<FancyScrollView.Context>), "FancyGridView_FancyScrollView_Context");
		<PERSON><PERSON>RegFunction("SetDataCount", SetDataCount);
		<PERSON>.RegFunction("__eq", op_Equality);
		L.<PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("DataCount", get_DataCount, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDataCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.FancyGridView<FancyScrollView.Context> obj = (FancyScrollView.FancyGridView<FancyScrollView.Context>)ToLua.CheckObject<FancyScrollView.FancyGridView<FancyScrollView.Context>>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetDataCount(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DataCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.FancyGridView<FancyScrollView.Context> obj = (FancyScrollView.FancyGridView<FancyScrollView.Context>)o;
			int ret = obj.DataCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DataCount on a nil value");
		}
	}
}

