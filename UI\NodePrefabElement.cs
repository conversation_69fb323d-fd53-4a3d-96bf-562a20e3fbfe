﻿using System;
using System.Collections;
using System.Collections.Generic;
#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;

[Serializable]
public class NodePrefabElement
{
    public GameObject prefab;
    public NodePrefabElement(GameObject prefab = null)
    {
        this.prefab = prefab;
    }
}

#if UNITY_EDITOR
[CustomPropertyDrawer(typeof(NodePrefabElement))]
public class NodePrefabElementDrawer : PropertyDrawer
{
    public override void OnGUI(Rect position, SerializedProperty property, GUIContent label)
    {
        //创建一个属性包装器，用于将常规GUI控件与SerializedProperty一起使用
        using (new EditorGUI.PropertyScope(position, label, property))
        {
            //默认一行的高度
            position.height = EditorGUIUtility.singleLineHeight;

            Rect prefabRect = new Rect(position)
            {
                width = position.width,
            };

            //找到每个属性的序列化值
            SerializedProperty prefabProperty = property.FindPropertyRelative("prefab");
            
            EditorGUI.PropertyField(prefabRect, prefabProperty, GUIContent.none);
        }
    }
}
#endif
