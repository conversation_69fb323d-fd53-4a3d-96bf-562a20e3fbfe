HitHamsterWGData = HitHamsterWGData or BaseClass()

function HitHamsterWGData:__init()
	if HitHamsterWGData.Instance then
		error("[HitHamsterWGData]:Attempt to create singleton twice!")
		return
	end
	HitHamsterWGData.Instance = self

	self:RegisterRemind()
	self:InitCfg()
end

function HitHamsterWGData:__delete()
	self.gift_info_list = nil
	self:UnRegisterRemind()
	HitHamsterWGData.Instance = nil
end

function HitHamsterWGData:RegisterRemind()
	RemindManager.Instance:Register(RemindName.HitHamster, BindTool.Bind(self.GetHitHamsterRed, self))	-- 打地鼠红点
end

function HitHamsterWGData:UnRegisterRemind()
	RemindManager.Instance:UnRegister(RemindName.HitHamster)
end

---------------------------------------------------------------------------------------
-- 初始化所有配置表
function HitHamsterWGData:InitCfg()
	local whack_mole_cfg = ConfigManager.Instance:GetAutoConfig("whack_mole_auto")
	if whack_mole_cfg then
		self.base_cfg = whack_mole_cfg.other[1]
		self.mole_cfg = whack_mole_cfg.mole
		self.hit_cfg = whack_mole_cfg.hit
		self.rank_reward_cfg = whack_mole_cfg.score_rank_reward
		self.join_reward_cfg = whack_mole_cfg.join_reward
		self:InitExperienceCfg(whack_mole_cfg.experience_mole)
	end
end

-- 拆分新手指导数据
function HitHamsterWGData:InitExperienceCfg(experience_mole_cfg)
	if not experience_mole_cfg then
		return
	end

	if not self.experience_mole_table then
		self.experience_mole_table = {}
	end

	for index, item_data in ipairs(experience_mole_cfg) do
		if not self.experience_mole_table[item_data.hole] then
			self.experience_mole_table[item_data.hole] = {}
		end

		local data = {}
		data.index = self:GetServerIndex(index)
		data.id = item_data.mole_id
		data.hole = item_data.hole
		data.is_touch = false
		data.occur_ms = item_data.occur_ms
		data.stop_ms = item_data.stop_ms
		data.bubble_str = item_data.bubble_str
	
		local mole_cfg = self:GetAllMoleCfgById(item_data.mole_id)
		local action_time = mole_cfg and mole_cfg.action_ms or 500
		data.action_time = action_time / 1000

		table.insert(self.experience_mole_table[item_data.hole], data)
	end
end

-- 获取基础配置表
function HitHamsterWGData:GetBaseConfig()
	return self.base_cfg
end

-- 获取所有地鼠配置
function HitHamsterWGData:GetAllMoleCfg()
	return self.mole_cfg
end

-- 获取地鼠根据id
function HitHamsterWGData:GetAllMoleCfgById(mole_id)
	return (self.mole_cfg or {})[mole_id]
end

-- 获取所有的参与奖励信息
function HitHamsterWGData:GetJoinRewardCfg()
	return self.join_reward_cfg
end

-- 获取一个参与奖励通过奖励次数
function HitHamsterWGData:GetJoinRewardByJoinTimes(join_time)
	return (self.join_reward_cfg or {})[join_time]
end

---------------------------------------------------------------------------------------
-- 设置所有的地鼠数据
function HitHamsterWGData:SetWhackMoleItemInfo(protocol)
	self.score = protocol.score
	self.hit_times = protocol.hit_times
	self.start_time_ms = protocol.start_time_ms
	self.end_time = protocol.end_time

	for index, item_data in ipairs(protocol.item_list) do
		self:SetWhackMoleItem(index, item_data)
		-- 将所有的地鼠按孔位拆分出来
		self:SplitMoleListByHole()
	end

	if #protocol.item_list > 0 then
		self:HoleGameListStart()
	end
end

-- 更新某个地鼠数据
function HitHamsterWGData:UpdateMoleItemInfo(protocol)
	self.score = protocol.score
	self.curr_score = protocol.score
	self.hit_times = protocol.hit_times
	local index = self:GetClientIndex(protocol.index)

	if self.best_hit < protocol.hit_times then
		self.best_hit = protocol.hit_times 
	end

	self:SetWhackMoleItem(protocol.index, protocol.whack_mole_item)
end

-- 更新某个地鼠数据
function HitHamsterWGData:SetWhackMoleItem(index, item_data)
	if not self.whack_mole_list then
		self.whack_mole_list = {}
	end

	if not self.whack_mole_list[index] then
		self.whack_mole_list[index] = {}
	end

	self.whack_mole_list[index].index = self:GetServerIndex(index)
	self.whack_mole_list[index].id = item_data.id
	self.whack_mole_list[index].hole = item_data.hole
	self.whack_mole_list[index].is_touch = item_data.is_touch
	self.whack_mole_list[index].occur_ms = item_data.occur_ms
	self.whack_mole_list[index].stop_ms = item_data.stop_ms

	local mole_cfg = self:GetAllMoleCfgById(item_data.id)
	local action_time = mole_cfg and mole_cfg.action_ms or 500
	self.whack_mole_list[index].action_time = action_time / 1000
end

-- 拆分整个地鼠数据
function HitHamsterWGData:SplitMoleListByHole()
	if not self.whack_mole_list then
		return
	end

	self.hole_list_data = {}

	for index, item_data in ipairs(self.whack_mole_list) do
		if not self.hole_list_data[item_data.hole] then
			self.hole_list_data[item_data.hole] = {}
		end

		table.insert(self.hole_list_data[item_data.hole], item_data)
	end
end

-- 获取分数
function HitHamsterWGData:GetWhackMoleScore()
	return self.score or 0
end

-- 获取点击次数
function HitHamsterWGData:GetWhackMoleHitTimes()
	return self.hit_times or 0
end

-- 获取开始时间
function HitHamsterWGData:GetWhackMoleStartTimes()
	return self.start_time_ms or 0
end

-- 获取地鼠列表
function HitHamsterWGData:GetWhackMoleList()
	return self.whack_mole_list
end

-- 获取客户端下标(客户端保存都用客户端下标)
function HitHamsterWGData:GetClientIndex(index)
	return index + 1
end

-- 获取服务器下标(发给服务器需要将客户端下标转换为服务器下标)
function HitHamsterWGData:GetServerIndex(index)
	local index = index - 1
	return index < 0 and 0 or index
end

-- 设置地鼠排行榜数据
function HitHamsterWGData:SetWhackMoleRankInfo(protocol)
	self.rank_item_list = protocol.rank_item_list

	if self.rank_item_list then
		local uuid = RoleWGData.Instance:GetUUid()
		local curr_uid = uuid.temp_low

		if #self.rank_item_list > 0 then
			for i, data in ipairs(self.rank_item_list) do
				if data.uid == curr_uid then
					if self.my_rank_data ~= nil then
						if data.rank < self.my_rank_data.rank then
							self.old_rank_data = self.my_rank_data
						end
					else
						self.old_rank_data = data
					end
	
					self.my_rank_data = data
					break
				end
			end
		else
			self.my_rank_data = nil
		end
	else
		self.my_rank_data = nil
	end
end

-- 获取地鼠排行榜数据
function HitHamsterWGData:GetWhackMoleRankList()
	local empty = {}
	local empty_table = {}
	for i = 1, 6 do
		if self.rank_item_list and self.rank_item_list[i] then
			table.insert(empty_table, self.rank_item_list[i])
		else
			table.insert(empty_table, empty)
		end
	end

	return empty_table
end

-- 获取地鼠排行榜自己的数据
function HitHamsterWGData:GetMyWhackMoleRank()
	if self.my_rank_data then
		return self.my_rank_data
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.my_rank_data = {}
	self.my_rank_data.rank = 999
	self.my_rank_data.name = role_vo.name
	self.my_rank_data.score = 0

	return self.my_rank_data
end

-- 获取地鼠排行榜自己的数据(之前的)
function HitHamsterWGData:GetOldMyWhackMoleRank()
	return self.old_rank_data
end

-- 设置玩家的参与次数与领取奖励次数
function HitHamsterWGData:SetWhackMoleBaseInfo(protocol)
	self.join_times = protocol.join_times
	self.join_reward_times = protocol.join_reward_times
end

-- 获取玩家的已领取次数
function HitHamsterWGData:GetWhackMoleRewardTimes()
	return self.join_reward_times or 0
end

-- 获取玩家的已参与次数
function HitHamsterWGData:GetWhackMoleJoinTimes()
	return self.join_times or 0
end
---------------------------------------------------------------------------------------
-- 获取打地鼠红点
function HitHamsterWGData:GetHitHamsterRed()
	return 0
end

-- 游戏开始地鼠数据重置
function HitHamsterWGData:HoleGameListStart()
	self.hole_game_list_data = {}
	self.cool_time = 0
	self.end_experience_time = nil
	self.experience_score = 0
	self.experience_hit = 0
	self.best_hit = 0
	self.curr_score = 0

	for hole = 1, 9 do
		if not self.hole_game_list_data[hole] then
			self.hole_game_list_data[hole] = {}
			self.hole_game_list_data[hole].show_index = 1
			self.hole_game_list_data[hole].is_occur = false
			self.hole_game_list_data[hole].is_exit = false
		end
	end
end

-- 更新孔位信息(秒数信息)
function HitHamsterWGData:UpdateMoleByHole(hole, cur_game_time, is_experience)
	local hole_data, is_occur, is_exit = self:GetMoleByHole(hole, true, is_experience)
	if not hole_data then
		return false, false
	end

	local game_time = cur_game_time * 1000
	local end_time = hole_data.stop_ms + hole_data.occur_ms
	local exit_time = 0 - hole_data.action_time * 1000
	local cool_time = self:GetGlobalMoleHoleTime()

	if is_occur then
		-- 出现了且结束了
		if game_time - (end_time + cool_time) > 0 then
			self:UpdateNextMoleByHole(hole)
			return true, false
		elseif (game_time - (end_time + cool_time) > exit_time) and (not is_exit) then
			self:UpdateNowMoleExitByHole(hole)
			return false, true
		end
	else
		if game_time - (hole_data.occur_ms + cool_time) > 0 then
			self:UpdateNowMoleOccurByHole(hole)
			return true, false
		end
	end

	return false, false
end

-- 获取当前孔位的数据
function HitHamsterWGData:GetMoleByHole(hole, is_check, is_experience)
	if ((not self.hole_list_data) and (not self.experience_mole_table)) or (not self.hole_game_list_data) then
		return nil
	end

	local curr_index = self.hole_game_list_data[hole].show_index
	local is_occur = self.hole_game_list_data[hole].is_occur
	local is_exit = self.hole_game_list_data[hole].is_exit

	if (not is_occur) and (not is_check) then
		return nil
	end

	if is_experience then
		if not self.experience_mole_table[hole] then
			return nil
		end

		return self.experience_mole_table[hole][curr_index], is_occur
	else
		if not self.hole_list_data[hole] then
			return nil
		end

		return self.hole_list_data[hole][curr_index], is_occur, is_exit
	end
end

-- 获取下一个的地鼠数据
function HitHamsterWGData:UpdateNextMoleByHole(hole)
	if (not self.hole_game_list_data) or (not self.hole_game_list_data[hole]) then
		return nil
	end

	self.hole_game_list_data[hole].show_index = self.hole_game_list_data[hole].show_index + 1
	self.hole_game_list_data[hole].is_occur = false
	self.hole_game_list_data[hole].is_exit = false
end

-- 更新当前的地鼠数据(出现)
function HitHamsterWGData:UpdateNowMoleOccurByHole(hole)
	if not self.hole_game_list_data then
		return nil
	end

	self.hole_game_list_data[hole].is_occur = true
end

function HitHamsterWGData:UpdateNowMoleExitByHole(hole)
	if not self.hole_game_list_data then
		return nil
	end

	self.hole_game_list_data[hole].is_exit = true
end

-- 获取一个全局时间(点击了特殊道具)
function HitHamsterWGData:GetGlobalMoleHoleTime()
	return self.cool_time
end

-- 获取一个全局时间(点击了特殊道具)
function HitHamsterWGData:SetGlobalMoleHoleTime(time)
	self.cool_time = self.cool_time + time
end

-- 游戏是否结束
function HitHamsterWGData:CheckIsGameEnd(is_experience)
	local end_time = self:GetWhackMoleEndTime(is_experience)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local cool_time = self:GetGlobalMoleHoleTime()
	end_time = end_time + cool_time

	if end_time - server_time <= 0 then
		return true
	end

	return false
end

-- 获取结束时间
function HitHamsterWGData:GetWhackMoleEndTime(is_experience)
	local base_cfg = self:GetBaseConfig()

	if is_experience then
		if self.end_experience_time == nil then
			local time = TimeWGCtrl.Instance:GetServerTime()
			local experience_time = base_cfg and base_cfg.experience_time
			self.end_experience_time = experience_time + time
		end

		return self.end_experience_time
	else
		return self.end_time or 0
	end
end

-- 设置地鼠积分和连击（新手练习使用）
function HitHamsterWGData:SetWhackMoleExperienceScore(mole_id, is_experience)
	local mole_cfg = self:GetAllMoleCfgById(mole_id)
	if not mole_cfg then
		return
	end

	local hit_add = self:GetWhackMoleHitAddition(is_experience)
	self.experience_score = self.experience_score + mole_cfg.score * hit_add
	self.curr_score = self.experience_score

	if self.experience_score <= 0 then
		self.experience_score = 0
	end

	if mole_cfg.hit_times == -1 then
		self.experience_hit = 0
	else
		self.experience_hit = self.experience_hit + mole_cfg.hit_times
	end

	if self.best_hit < self.experience_hit then
		self.best_hit = self.experience_hit
	end

	if mole_cfg.control_ms ~= 0 then
		self:SetGlobalMoleHoleTime(mole_cfg.control_ms)
	end
end

-- 获取当前的积分
function HitHamsterWGData:GetWhackMoleFinalScore(is_experience)
	local server_score = self:GetWhackMoleScore()
	return is_experience and self.experience_score or server_score
end

-- 获取当前的连击
function HitHamsterWGData:GetWhackMoleFinalHitTimes(is_experience)
	local server_hit_times = self:GetWhackMoleHitTimes()
	return is_experience and self.experience_hit or server_hit_times
end

-- 获取当前的连击加成
function HitHamsterWGData:GetWhackMoleHitAddition(is_experience)
	local hit_times = self:GetWhackMoleFinalHitTimes(is_experience) or 0
	local addition = 1

	if not self.hit_cfg then
		return addition
	end

	local hit_add = nil

	for k, hit_data in pairs(self.hit_cfg) do
		if hit_times >= hit_data.min_times and hit_times <= hit_data.max_times then
			hit_add = hit_data
			break
		end
	end

	if not hit_add then
		return addition
	end

	addition = hit_add.score_added / 10000
	return addition
end

-- 获取当局游戏的最高连击
function HitHamsterWGData:GetWhackMoleBestHit()
	return self.best_hit or 0
end

-- 获取当局游戏的最高连击
function HitHamsterWGData:GetWhackMoleCurrScore()
	return self.curr_score or 0
end

-- 获取当前排名奖励
function HitHamsterWGData:GetRankRewardByRank(rank)
	if not self.rank_reward_cfg then
		return nil
	end

	local reward_data = nil

	for k, rank_data in pairs(self.rank_reward_cfg) do
		if rank >= rank_data.min_rank and rank <= rank_data.max_rank then
			reward_data = rank_data
			break
		end
	end

	return reward_data
end

-- 获取当前参与次数与奖励次数判断是否可以领取奖励
function HitHamsterWGData:CheckHaveTimeRewardGet()
	local join_reward_times = self:GetWhackMoleRewardTimes()
	local join_times = self:GetWhackMoleJoinTimes()
	local reward_cfg = self:GetJoinRewardCfg()
	local reward_count = #reward_cfg

	if join_times ~= join_reward_times and join_times <= reward_count then		-- 存在可能领取的奖励
		return true, join_reward_times + 1
	end

	return false, join_times
end

-- 获取活动时间
function HitHamsterWGData:GetActTimeCount()
	local act = ACTIVITY_TYPE.HIT_HAMSTER or nil
	local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)

	if act_status and act_status.status == ACTIVITY_STATUS.OPEN then
		return act_status.next_time
	end

	return 0 
end