local AggregationPermanpentKey = {}
local AggregationPermanpentOpenfunKey = {}
local AggregationActKey = {}
local AggregationButtonList = {}
local AggregationButtonIndexList = {}
local AggregationBtnActTypeStart = 1000000
local BTN_MIN_OFFSET = -106

function MainUIView:ReleaseActivityAggregationBtn()
	for i, aggregation_data in ipairs(AggregationButtonList) do
		if aggregation_data.parent_cell ~= nil then
			aggregation_data.parent_cell:DeleteMe()
			aggregation_data.parent_cell = nil
		end
	end

	AggregationPermanpentKey = {}
	AggregationActKey = {}
	AggregationButtonList = {}
	AggregationButtonIndexList = {}
	AggregationPermanpentOpenfunKey = {}
end

-- 初始化聚合按钮操作
function MainUIView:GetAllActivityAggregationBtn()
	return AggregationButtonList
end

-- 初始化聚合按钮操作
function MainUIView:GetAllActivityAggregationBtnByIndex(index)
	return AggregationButtonIndexList[index]
end

-- 初始化聚合按钮操作
function MainUIView:InitActivityAggregationBtn()
	local daily_activity_button_aggregation_cfg = ConfigManager.Instance:GetAutoConfig("daily_activity_auto").button_aggregation
	for i, v in ipairs(daily_activity_button_aggregation_cfg) do
		local aggregation_data = {}
		aggregation_data.parent_cell = nil
		aggregation_data.aggregation_main_name = v.aggregation_main_name
		aggregation_data.aggregation_main_act = v.aggregation_main_act
		aggregation_data.aggregation_sort = v.sort
		aggregation_data.parent_cell = self:CreateMainActiviyAggregationBtn(i, aggregation_data, v.sort)

		self:ActivityAggregationActBtn(v.aggregation_act_ids, aggregation_data, i)
		self:ActivityAggregationPermanpentBtn(v.aggregation_permanpent_ids, aggregation_data, i)
		AggregationButtonList[i] = aggregation_data
		self:PushAllAggregationActBtn(i)
		self:FlushActiviyAggregationRemind(i)
		self:FlushActiviyAggregationActStatus(i)
	end

	XUI.AddClickEventListener( self.node_list.aggregation_collect_close, BindTool.Bind(self.OnClickActivityAggregationBtn, self))		-- 关闭聚合展示
end

-- 聚合活动按钮是否开启
function MainUIView:GetMainActiviyAggregationActBtnIsOpen(act_id)
	local act_data = ActivityWGData.Instance:GetActivityStatuByType(act_id)
	local status = act_data and act_data.status or 0
	local cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(act_id)
	local open_panel_name = cfg and cfg.open_panel_name or ""
	local is_open = FunOpen.Instance:GetCfgPathIsOpen(open_panel_name)

	if ACTIVITY_STATUS.OPEN == status and is_open then
		return true, cfg
	end

	return false, nil
end

-- 聚合常驻按钮是否开启
function MainUIView:GetMainActiviyAggregationPermanpentBtnIsOpen(permanpent_name)
	local data = self:GetOneSundryButton(permanpent_name)
	if not data then
		return false, nil
	end

	local view_name = data[1]
	local fun_name = FunOpen.Instance:GetFunNameByViewName(view_name)
	local is_open = FunOpen.Instance:GetFunIsOpened(fun_name)
	-- local is_open_other = FunOpen.Instance:GetFunIsOpened(view_name)

	if is_open then
		return true, data
	end

	return false, nil
end

-- 创建一个新的活动按钮替换当前的主按钮
function MainUIView:CreateMainActiviyAggregationBtn(aggregation_seq, aggregation_data, sort)
	local activity_type = AggregationBtnActTypeStart + aggregation_seq
	sort = sort or 10 --容错，默认排第一
	local index = math.floor(sort / 10)
	index = index > 0 and index or 1
	local parent = self.node_list["ButtonGroup" .. index]
	if nil == parent then
		return
	end
	
	local aggregation_btn = MainActivityBtn.New()
	aggregation_btn:CreateBtnAsset(parent, activity_type)
	aggregation_btn:SetClickCallBack(BindTool.Bind(self.OnClickMainActiviyAggregationBtn, self, aggregation_seq))
	if not AggregationButtonIndexList[index] then
		AggregationButtonIndexList[index] = {}
	end

	table.insert(AggregationButtonIndexList[index], aggregation_data)
	self:UpdateBtnGroupInIndex(index)
	return aggregation_btn
end

-- 聚合活动按钮
function MainUIView:ActivityAggregationActBtn(aggregation_act_ids, aggregation_data, aggregation_seq)
	if (not aggregation_act_ids) or aggregation_act_ids == "" then
		return
	end

	local act_list = Split(aggregation_act_ids, "|")
	for i, act_str in ipairs(act_list) do
		local act_id = tonumber(act_str) or 0

		if not aggregation_data.open_act_list then
			aggregation_data.open_act_list = {}
		end
		
		table.insert(aggregation_data.open_act_list, act_id)
		AggregationActKey[act_id] = aggregation_seq
	end
end

-- 聚合常驻按钮
function MainUIView:ActivityAggregationPermanpentBtn(aggregation_permanpent_ids, aggregation_data, aggregation_seq)
	if (not aggregation_permanpent_ids) or aggregation_permanpent_ids == "" then
		return
	end

	local permanpent_list = Split(aggregation_permanpent_ids, "|")
	for i, permanpent_str in ipairs(permanpent_list) do
		if not aggregation_data.permanpent_name_list then
			aggregation_data.permanpent_name_list = {}
		end
		
		table.insert(aggregation_data.permanpent_name_list, permanpent_str)

		local fun_data = self:GetOneSundryButton(permanpent_str)
		if fun_data and fun_data[1] then
			local view_name = fun_data[1]
			local fun_name = FunOpen.Instance:GetFunNameByViewName(view_name)
			AggregationPermanpentOpenfunKey[fun_name] = aggregation_seq
		end

		AggregationPermanpentKey[permanpent_str] = aggregation_seq
	end
end

-- 聚合活动按钮(检测是否需要创建聚合按钮, 新出现活动创建)
function MainUIView:CheckNeedCreateAggregationActBtn(act_id)
	local aggregation_seq = AggregationActKey[act_id]

	if not aggregation_seq then
		return false
	end

	return true
end

-- 判断是否属于是聚合按钮
function MainUIView:CheckIsActivityAggregationBtn(pararm)
	if AggregationActKey[pararm] then
		return true
	elseif AggregationPermanpentKey[pararm] then
		return true
	end

	return false
end

-- 将一个活动按钮加入到聚合节点
function MainUIView:ActivityAggregationActBtnForParent(act_id)
	local is_can_need, is_build_new_main = self:CheckNeedCreateAggregationActBtn(act_id)

	if is_can_need then
		if self:CheckIsActivityAggregationBtn(act_id) then
			local node = self:GetActivityButtonByActivityType(act_id)
			if node then
				self:AggregationSubBtnForParent(node:GetView())
				self:ActiviyAggregationActBtnRemind(act_id)
			end
		end
	end
end

-- 活动按钮的显示隐藏影响活动的状态更新
function MainUIView:ActivityAggregationActBtnVisibleChange(act_id)
	local aggregation_seq = AggregationActKey[act_id]

	if not aggregation_seq then
		return false
	end

	self:FlushActiviyAggregationActStatus(aggregation_seq)
end

-- 活动按钮的显示隐藏影响活动的状态更新
function MainUIView:ActivityAggregationPermanpentBtnVisibleChange(fun_name)
	local aggregation_seq = AggregationPermanpentOpenfunKey[fun_name]

	if not aggregation_seq then
		return false
	end

	self:FlushActiviyAggregationActStatus(aggregation_seq)
end

-- 将一个常驻按钮加入到聚合节点
function MainUIView:ActivityAggregationPermanpentBtnForParent(permanpent_name)
	if self:CheckIsActivityAggregationBtn(permanpent_name) then
		local node = self:GetOneSundryNode(permanpent_name)
		if node then
			self:AggregationSubBtnForParent(node)
			self:ActiviyAggregationPermanpentBtnRemind(permanpent_name)
		end
	end
end

-- 将一个聚合按钮全部丢到固定节点下
function MainUIView:PushAllAggregationActBtn(aggregation_seq)
	local aggregation_data = AggregationButtonList[aggregation_seq]
	if aggregation_data ~= nil then
		if aggregation_data.open_act_list then
			for _, act_id in ipairs(aggregation_data.open_act_list) do
				self:ActivityAggregationActBtnForParent(act_id)
			end
		end

		if aggregation_data.permanpent_name_list then
			for _, permanpent_name in ipairs(aggregation_data.permanpent_name_list) do
				self:ActivityAggregationPermanpentBtnForParent(permanpent_name)
			end
		end
	end
end

-- 将一个节点加入到聚合节点
function MainUIView:AggregationSubBtnForParent(node)
	if (not node) or (not self.node_list.aggregation_collect_btns_panel) then
		return
	end

	node.transform:SetParent(self.node_list.aggregation_collect_btns_panel.transform)
end

--  刷新聚合按钮的红点状态(活动)
function MainUIView:ActiviyAggregationActBtnRemind(act_id, num)
	local aggregation_seq = AggregationActKey[act_id]
	self:FlushActiviyAggregationRemind(aggregation_seq, num)
end

--  刷新聚合按钮的红点状态(常驻)
function MainUIView:ActiviyAggregationPermanpentBtnRemind(permanpent_name, num)
	local aggregation_seq = AggregationPermanpentKey[permanpent_name]
	self:FlushActiviyAggregationRemind(aggregation_seq, num)
end

--  刷新聚合按钮的红点状态(总)
function MainUIView:FlushActiviyAggregationRemind(aggregation_seq, num)
	local is_show_red = false
	local aggregation_data = AggregationButtonList[aggregation_seq]

	if aggregation_data ~= nil then
		if num ~= nil and num > 0 then
			is_show_red = true
		end

		if (not is_show_red) and aggregation_data.open_act_list then
			for _, act_id in ipairs(aggregation_data.open_act_list) do
				local remind_name = ActRemindList[act_id]
				-- local act_icon = self:GetActivityButtonByActivityType(act_id)
				if self.act_remindRecord[remind_name] ~= nil and self.act_remindRecord[remind_name] ~= 0 then
					is_show_red = true
					break
				end
			end
		end

		if (not is_show_red) and aggregation_data.permanpent_name_list then
			for _, permanpent_name in ipairs(aggregation_data.permanpent_name_list) do
				local data = self:GetOneSundryButton(permanpent_name)

				if data and data.remind and self.icon_remind_list_top_num[data.remind] ~= nil and self.icon_remind_list_top_num[data.remind] > 0 then
					is_show_red = true
					break
				end
			end
		end

		if aggregation_data.parent_cell ~= nil then
			aggregation_data.parent_cell:Flush("SetRedPoint", {is_show_red})
		end
	end
end

-- 刷新聚合按钮的开启状态，设置图标
function MainUIView:FlushActiviyAggregationActStatus(aggregation_seq)
	-- 默认拿第一个集合的图标
	local aggregation_data = AggregationButtonList[aggregation_seq]
	local res_name = nil

	if aggregation_data ~= nil then
		if aggregation_data.aggregation_main_name ~= nil and aggregation_data.aggregation_main_name ~= "" then
			local is_open, data = self:GetMainActiviyAggregationPermanpentBtnIsOpen(aggregation_data.aggregation_main_name)
			local node = self:GetOneSundryNode(aggregation_data.aggregation_main_name)

			if is_open and node ~= nil then
				res_name = data.res_name
			end
		end

		if aggregation_data.aggregation_main_act ~= nil and aggregation_data.aggregation_main_act ~= "" and res_name == nil then
			local is_open, cfg = self:GetMainActiviyAggregationActBtnIsOpen(aggregation_data.aggregation_main_act)
			local node = self:GetActivityButtonByActivityType(aggregation_data.aggregation_main_act)

			if is_open and node ~= nil then
				res_name = cfg.res_name

				if aggregation_data.parent_cell ~= nil then
					aggregation_data.parent_cell:ResetActId(aggregation_data.aggregation_main_act)
					if aggregation_data.parent_cell.relevance_child then
						-- 清空关联子节点的父节点
						aggregation_data.parent_cell.relevance_child:SetRelevanceParnet(nil)
					end

					node:SetRelevanceParnet(aggregation_data.parent_cell)
					aggregation_data.parent_cell:SetRelevanceChild(node)
				end
			end
		end

		if aggregation_data.permanpent_name_list and res_name == nil then
			for _, permanpent_name in ipairs(aggregation_data.permanpent_name_list) do
				local is_open, data = self:GetMainActiviyAggregationPermanpentBtnIsOpen(permanpent_name)
				local node = self:GetOneSundryNode(permanpent_name)

				if is_open and node ~= nil then
					res_name = data.res_name
				end
			end
		end

		if aggregation_data.open_act_list and res_name == nil then
			for _, act_id in ipairs(aggregation_data.open_act_list) do
				local is_open, cfg = self:GetMainActiviyAggregationActBtnIsOpen(act_id)
				local node = self:GetActivityButtonByActivityType(act_id)

				if is_open and node ~= nil then
					res_name = cfg.res_name

					if aggregation_data.parent_cell ~= nil then
						aggregation_data.parent_cell:ResetActId(act_id)
						if aggregation_data.parent_cell.relevance_child then
							-- 清空关联子节点的父节点
							aggregation_data.parent_cell.relevance_child:SetRelevanceParnet(nil)
						end
	
						node:SetRelevanceParnet(aggregation_data.parent_cell)
						aggregation_data.parent_cell:SetRelevanceChild(node)
					end
					break
				end
			end
		end

		if res_name ~= nil then
			if aggregation_data.parent_cell ~= nil then
				aggregation_data.parent_cell:SetVisible(true)
				aggregation_data.parent_cell:Flush("SetSprite", {res_name})
				local view = aggregation_data.parent_cell:GetView()
				
				if (not self.move_dis_ison) and view then
					view.canvas_group.alpha = 0
					view.canvas_group.blocksRaycasts = false
				end
			end
		else
			if aggregation_data.parent_cell ~= nil then
				aggregation_data.parent_cell:SetVisible(false)
			end
		end
	end
end

-- 检测功能活动集合是否只开启了一个，只开启了一个
function MainUIView:CheckMainActiviyAggregationBtnIsOpenOneAct(aggregation_seq)
	-- 默认拿第一个集合的图标
	local aggregation_data = AggregationButtonList[aggregation_seq]
	local open_count = 0
	local open_str = nil
	local open_index = nil

	if aggregation_data ~= nil then
		if aggregation_data.open_act_list then
			for _, act_id in ipairs(aggregation_data.open_act_list) do
				local is_open, cfg = self:GetMainActiviyAggregationActBtnIsOpen(act_id)
				local node = self:GetActivityButtonByActivityType(act_id)

				if is_open and node ~= nil then
					open_count = open_count + 1
					open_str = cfg and cfg.open_panel_name
				end
			end
		end

		if aggregation_data.permanpent_name_list then
			for _, permanpent_name in ipairs(aggregation_data.permanpent_name_list) do
				local is_open, data = self:GetMainActiviyAggregationPermanpentBtnIsOpen(permanpent_name)
				local node = self:GetOneSundryNode(permanpent_name)
				
				if is_open and node ~= nil then
					open_count = open_count + 1
					open_str = data[1]
					open_index = data[2]
				end
			end
		end
	end
	
	return open_count == 1, open_str, open_index, open_count
end

-- 聚合按钮点击
function MainUIView:OnClickMainActiviyAggregationBtn(aggregation_seq)
	local is_one_only, view_name, view_table_index = self:CheckMainActiviyAggregationBtnIsOpenOneAct(aggregation_seq)

	if is_one_only then
		if view_name and view_name ~= "" then
			if view_table_index ~= nil and view_table_index ~= "" then
				FunOpen.Instance:OpenViewByName(view_name, view_table_index)
			else
				FunOpen.Instance:OpenViewNameByCfg(view_name)
			end
		end

		return
	end

	if self.node_list.aggregation_collect_panel then
		self.node_list.aggregation_collect_panel:CustomSetActive(true)
	end

	for i, v in ipairs(AggregationButtonList) do
		local enable = aggregation_seq == i

		if v.permanpent_name_list then
			for _, permanpent_name in ipairs(v.permanpent_name_list) do
				local is_open, _ = self:GetMainActiviyAggregationPermanpentBtnIsOpen(permanpent_name)
				local node = self:GetOneSundryNode(permanpent_name)
				if node ~= nil then
					node:CustomSetActive(is_open and enable)

					if is_open and enable then
						node.transform:SetAsLastSibling()
						node.canvas_group.alpha = 1
						node.canvas_group.blocksRaycasts = true
					end
				end
			end
		end

		if v.open_act_list then
			for _, act_id in ipairs(v.open_act_list) do
				local is_open, _ = self:GetMainActiviyAggregationActBtnIsOpen(act_id)
				local node = self:GetActivityButtonByActivityType(act_id)
				if node ~= nil then
					node:GetView():CustomSetActive(enable and is_open)

					if enable and is_open then
						node:GetView().transform:SetAsLastSibling()
						node:GetView().canvas_group.alpha = 1
						node:GetView().canvas_group.blocksRaycasts = true
					end
				end
		
			end
		end
	end

	-- 设置计算位置
	local aggregation_data = AggregationButtonList[aggregation_seq]
	if aggregation_data and aggregation_data.parent_cell then
		local view = aggregation_data.parent_cell:GetView()
		if view then
			self.node_list.aggregation_collect_btns_panel.transform.position = view.transform.position
			local offset_vec = self.node_list.aggregation_collect_btns_panel.transform.localPosition + Vector3(0, -52, 0)
			local mask_vec = self.node_list.aggregation_collect_btns_mask.transform.anchoredPosition

			-- local screen = UnityEngine.Screen
			-- 20是排序【偏移量
			-- 48是一个格子的中心点, 96 是一个格子的宽度
			local left_min_pos_check = (1920 / 2 - 20 - (96)) * -1 
			if offset_vec.x < left_min_pos_check then
				offset_vec.x = offset_vec.x + math.abs(BTN_MIN_OFFSET)
				mask_vec.x = BTN_MIN_OFFSET
			else
				mask_vec.x = 0
			end

			self.node_list.aggregation_collect_btns_panel.transform.localPosition = offset_vec
			self.node_list.aggregation_collect_btns_mask.transform.anchoredPosition = mask_vec
		end
	end
end

-- 聚合按钮点击
function MainUIView:OnClickActivityAggregationBtn()
	if self.node_list.aggregation_collect_panel then
		self.node_list.aggregation_collect_panel:CustomSetActive(false)
	end	
end