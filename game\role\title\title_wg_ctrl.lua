require("game/role/title/title_wg_data")
require("game/role/title/title_add_view")
require("game/role/title/title_tips_view")
require("game/role/title/title_comparison_view")
require("game/role/title/act_title_add_view")
require("game/role/title/diy_title_name_view")

--------------------------------------------------------------
--角色称号
--------------------------------------------------------------
TitleWGCtrl = TitleWGCtrl or BaseClass(BaseWGCtrl)
function TitleWGCtrl:__init()
	if TitleWGCtrl.Instance then
		ErrorLog("[TitleWGCtrl] Attemp to create a singleton twice !")
	end
	TitleWGCtrl.Instance = self

	self.title_data = TitleWGData.New()
	self.title_add_view = TitleAddView.New(GuideModuleName.TitleAddView)
	self.act_title_add_view = ActTitleAddView.New(GuideModuleName.ActTitleAddView)
	self.title_tips_view = TitleTipsView.New()
	self.title_comparison_view = TitleComparisonView.New()
	self.diy_title_name_view = DiyTitleNameView.New()


	self:RegisterAllProtocols()
	RoleWGData.Instance:NotifyAttrChange(BindTool.Bind1(self.RoleDataEvilChangeCallback, self), {"name_color"})
end

function TitleWGCtrl:__delete()
	self.title_data:DeleteMe()
	self.title_data = nil

	self.title_comparison_view:DeleteMe()
	self.title_comparison_view = nil

	self.title_add_view:DeleteMe()
	self.title_add_view = nil

	self.title_tips_view:DeleteMe()
	self.title_tips_view = nil

	self.act_title_add_view:DeleteMe()
	self.act_title_add_view = nil

	self.diy_title_name_view:DeleteMe()
	self.diy_title_name_view = nil

	TitleWGCtrl.Instance = nil
end

function TitleWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCTitleInfo, "OnTitleInfo")
	self:RegisterProtocol(SCRoleUsedTitleChange, "OnRoleUsedTitleChange")
	self:RegisterProtocol(SCActiveNewTitleInfo, "OnSCActiveNewTitleInfo")

	self:RegisterProtocol(SCCustomTitleInfo, "OnSCCustomTitleInfo")
	self:RegisterProtocol(SCCustomTitleSingleInfo, "OnSCCustomTitleSingleInfo")
	self:RegisterProtocol(SCNewTitleUpgradeAllInfo, "OnSCNewTitleUpgradeAllInfo")

	self:RegisterProtocol(CSTitleOperate)
	self:RegisterProtocol(CSCustomTitleChangeName)
end

--同步可用的称号列表3600
function TitleWGCtrl:OnTitleInfo(protocol)
	-- print_error("----同步可用的称号列表----")
	self.title_data:SetTitleIdList(protocol.title_id_list)
	self.title_data:SetTitleLevelList(protocol.title_level_list)
	RoleWGCtrl.Instance:FlushTitleView()

	local used_title_list = protocol.used_title_list
	local use_pet_titleid = TitleWGData.GetAndRemoveLingpoTitleFromList(used_title_list)
	self.title_data:SetUsedTitleId(used_title_list)
	RoleWGData.Instance:SetAttr("used_title_list", used_title_list)
	RoleWGData.Instance:SetAttr("use_pet_titleid", use_pet_titleid)

    local main_role = Scene.Instance:GetMainRole()
	if main_role then
        if Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN and
                KuafuYeZhanWangChengWGCtrl.Instance:CheckActiIsOpen() then
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic then
                scene_logic:AddYezhanDelay(nil, protocol.used_title_list)
            end
        elseif Scene.Instance:GetSceneType() == SceneType.KFZhuXieZhanChang and
        	ActivityWGCtrl.Instance:CheckKFZhuXieActiIsOpen() then
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic then
                scene_logic:AddKFZhuXieDelay( protocol.used_title_list)
            end
        elseif Scene.Instance:GetSceneType() == SceneType.ZhuXie and
        	ActivityWGCtrl.Instance:CheckZhuXieActiIsOpen() then
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic then
                scene_logic:AddZhuXieDelay(protocol.used_title_list)
            end
        elseif Scene.Instance:GetSceneType() == SceneType.HunYanFb then
			--2021/10/13 策划说屏蔽婚宴里面的称号信息  1001称号资源暂时没有
		else
            main_role:SetAttr("used_title_list", used_title_list)
        end
	end

	GlobalEventSystem:Fire(OtherEventType.TitleInfoChange, protocol.title_id_list)
end

--打开获得称号提示面板
function TitleWGCtrl:OnSCActiveNewTitleInfo(protocol)
	-- print_error("---获得称号----")
	if protocol.active_new_title_id ~= 0 then
		local is_act_title = ServerActivityWGData.Instance:IsActGetTitle(protocol.active_new_title_id)
		if is_act_title then
			self:OpenActTitleAddView(protocol.active_new_title_id, false)
			return
		end

		if protocol.notice_type == TITLE_NOTICE_TYPE.TYPE_INVALID then
			local use_title_list = TitleWGData.Instance:GetUsedTitleId()
			if #use_title_list > 0 then
				TitleWGCtrl.Instance:OpenTitleComparisonView(protocol.active_new_title_id)
			else
				self.title_add_view:SetShowTitleId(protocol.active_new_title_id)
				self.title_add_view:Open()
			end
		-- elseif protocol.notice_type == TITLE_NOTICE_TYPE.TYPE_OGA_GODS_RANK then
		-- 	self:OpenActTitleAddView(protocol.active_new_title_id, false)
		end
	end
end

function TitleWGCtrl:FakeGetNewTitle(title_id)
	self.title_add_view:SetShowTitleId(title_id)
	self.title_add_view:Open()
end

function TitleWGCtrl:OnOpenTitleTips()
	if self.title_tips_view then
		self.title_tips_view:Open()
	end
end

function TitleWGCtrl:OpenTitleComparisonView(new_title_id)
	--婚宴场景不允许打开这个界面
	if Scene.Instance:GetSceneType() == SceneType.HunYanFb then
		return
	end
	local use_title_list = TitleWGData.Instance:GetUsedTitleId()
	local use_title_id = use_title_list and use_title_list[1]
    if not new_title_id or not use_title_id or new_title_id == use_title_id then
    	return
    end
	---[[ 后端是不是发了一些废弃称号过来呀
    local new_title_cfg = TitleWGData.Instance:GetConfig(new_title_id)
    if not new_title_cfg then
    	return
    end
    --]]
	self.title_comparison_view:Open()
	self.title_comparison_view:Flush(0, "title_id", {new_title_id = new_title_id, use_title_id = use_title_id})
end

function TitleWGCtrl:OpenActTitleAddView(title_id, is_forever)
	self.act_title_add_view:Open()
	self.act_title_add_view:Flush(0, "act_title_id", {title_id = title_id, is_forever = is_forever})
end

--广播称号的改变
function TitleWGCtrl:OnRoleUsedTitleChange(protocol)
	-- print_error("----广播称号的改变----")
	local used_title_list = protocol.used_title_list
	local used_diy_title_name = protocol.used_diy_title_name
	local use_pet_titleid = TitleWGData.GetAndRemoveLingpoTitleFromList(used_title_list)

	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if nil ~= obj and obj:IsRole() then
        if Scene.Instance:GetSceneType() == SceneType.YEZHANWANGCHENGFUBEN
		and KuafuYeZhanWangChengWGCtrl.Instance:CheckActiIsOpen() then
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic then
                scene_logic:AddYezhanDelay(nil, used_title_list)
            end
        elseif Scene.Instance:GetSceneType() == SceneType.KFZhuXieZhanChang and
        	ActivityWGCtrl.Instance:CheckKFZhuXieActiIsOpen() then
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic then
                scene_logic:AddKFZhuXieDelay(used_title_list)
            end
        elseif Scene.Instance:GetSceneType() == SceneType.ZhuXie and
        	ActivityWGCtrl.Instance:CheckZhuXieActiIsOpen() then
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic then
                scene_logic:AddZhuXieDelay(used_title_list)
            end
        else
		    obj:SetAttr("used_title_list", used_title_list)
			obj:SetAttr("used_diy_title_name", used_diy_title_name)

		    if obj:IsMainRole() then
				RoleWGData.Instance:SetAttr("used_title_list", used_title_list)
				RoleWGData.Instance:SetAttr("use_pet_titleid", use_pet_titleid)
				RoleWGData.Instance:SetAttr("used_diy_title_name", used_diy_title_name)
				self.title_data:SetUsedTitleId(used_title_list)
			end
		end
	end

	RoleWGCtrl.Instance:FlushTitleView()
end

--发送获取称号列表请求
 function TitleWGCtrl:SendGetTitleListReq()
 	self:SendTitleOperate(TITLE_OPERTYPE.REQ_INFO, nil)
 end

--发送使用称号请求
function TitleWGCtrl:SendUseTitleReq(used_title_list)
	self:SendTitleOperate(TITLE_OPERTYPE.REQ_USE, used_title_list)
end
--发送卸下称号请求
function TitleWGCtrl:SendUnUseTitleReq(un_used_title_list)
	self:SendTitleOperate(TITLE_OPERTYPE.REQ_UN_USE, un_used_title_list)
end

--发送升级称号请求
function TitleWGCtrl:SendUpLevelTitleReq(un_used_title_list)
	self:SendTitleOperate(TITLE_OPERTYPE.REQ_UP_LEVEL, un_used_title_list)
end

function TitleWGCtrl:SendTitleOperate(operate, used_title_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTitleOperate)
	protocol.operate = operate
	protocol.used_title_list = used_title_list or {}
	protocol:EncodeAndSend()
end

function TitleWGCtrl:OnTitleLevelUpResult(result,  title_id, level)
	if result == 1 then
		RoleWGCtrl.Instance:PlayTitleUpLevelEff()
	end
end

function TitleWGCtrl:RoleDataEvilChangeCallback(attr_name, value)
	if attr_name == "name_color" then
		local title_used_list = RoleWGData.Instance.role_vo.used_title_list or {}
		if value > EvilColorList.NAME_COLOR_RED_1 then
			-- if value == EvilColorList.NAME_COLOR_RED_2 then
			-- 	self.title_data:SetEvilTitle(COMMON_CONSTS.EVIL_TITLE_1)
			-- elseif value == EvilColorList.NAME_COLOR_RED_3 then
			-- 	self.title_data:SetEvilTitle(COMMON_CONSTS.EVIL_TITLE_2)
			-- end
		else
			self.title_data:SetEvilTitle(0)
		end
		self.title_data:SetUsedTitleId(__TableCopy(title_used_list))
		RoleWGCtrl.Instance:FlushTitleView()
	end
end

-- 全部 自定义称号数据
function TitleWGCtrl:OnSCCustomTitleInfo(protocol)
	self.title_data:SetDiyTitleInfo(protocol)

	----[[玩家称号改变
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role.vo == nil or main_role.vo.used_title_list == nil then
		return
	end

	local cur_title_id = main_role.vo.used_title_list[1]
	local used_diy_title_name = self.title_data:GetDiyTitleName(cur_title_id)
	if used_diy_title_name == nil then
		return
	end

	main_role:SetAttr("used_diy_title_name", used_diy_title_name)
	RoleWGData.Instance:SetAttr("used_diy_title_name", used_diy_title_name)
	--]]
end

-- 单个 自定义称号数据
function TitleWGCtrl:OnSCCustomTitleSingleInfo(protocol)
	-- print_error("----单个----", protocol)
	self.title_data:SetDiyTitleSingleInfo(protocol)

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil or main_role.vo == nil or main_role.vo.used_title_list == nil then
		return
	end

	local tilte_id = protocol.title_id
	local cur_title_id = main_role.vo.used_title_list[1]

	if tilte_id == cur_title_id then
		TitleWGCtrl.Instance:FakeGetNewTitle(tilte_id)
	end

	ViewManager.Instance:FlushView(GuideModuleName.RoleBranchView, TabIndex.cheng_hao)
	
--[[玩家称号改变
	local used_diy_title_name = self.title_data:GetDiyTitleName(cur_title_id)
	if used_diy_title_name == nil then
		return
	end

	main_role:SetAttr("used_diy_title_name", used_diy_title_name)
	RoleWGData.Instance:SetAttr("used_diy_title_name", used_diy_title_name)
	--]]
end

-- 设置自定义称号
function TitleWGCtrl:SendSetDiyTitleName(title_id, diy_title_name)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCustomTitleChangeName)
	protocol.title_id = title_id or -1
	protocol.diy_title_name = diy_title_name or ""
	protocol:EncodeAndSend()
end

function TitleWGCtrl:OpenDiyTitleNameView(title_id)
	self.diy_title_name_view:SetDataAndOpen(title_id)
end

function TitleWGCtrl:OnSCNewTitleUpgradeAllInfo(protocol)
	self.title_data:SetAllTitleData(protocol)
end

