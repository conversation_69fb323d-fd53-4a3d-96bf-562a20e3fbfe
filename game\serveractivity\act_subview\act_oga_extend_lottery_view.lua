
function KfActivityView:LoadOGALotteryCallBack()
    if not self.lottery_task_list then
        self.lottery_task_list = AsyncListView.New(OGALotteryTaskItemRender, self.node_list["lottery_task_list"])
        self.lottery_task_list:SetStartZeroIndex(false)
    end
    
    local root = self.node_list["lottery_reward_root"]
    local node_num = root.transform.childCount
	if not self.turn_table_reward_list then
		self.turn_table_reward_list = {}
        for i = 1, node_num do
        	self.turn_table_reward_list[i] = ItemCell.New(root:FindObj("reward_item" .. i))
        end
	end
    self.oga_lottery_day = nil

    XUI.AddClickEventListener(self.node_list["btn_lottery_goto"], BindTool.Bind(self.OnClickOGALotteryJumpToPanel, self))
    XUI.AddClickEventListener(self.node_list["btn_draw"], BindTool.Bind(self.OnClickOGALottery, self))
    XUI.AddClickEventListener(self.node_list["btn_skip_lottery"], BindTool.Bind(self.OnClickOGALotteryAnimSkip, self))
end

function KfActivityView:CloseOGALotteryCallBack()
    self.oga_lottery_day = nil

    if self.draw_tween then
        self.draw_tween:Kill(true)
        self.draw_tween = nil
    end

    self.is_click_oga_lock = false
end

function KfActivityView:ReleaseOGALotteryCallBack()
    if CountDownManager.Instance:HasCountDown("OGA_Lottery_CD") then
		CountDownManager.Instance:RemoveCountDown("OGA_Lottery_CD")
	end

    self.oga_lottery_day = nil
    if self.draw_tween then
        self.draw_tween:Kill()
        self.draw_tween = nil
    end

    if self.turn_table_reward_list then
		for key, value in pairs(self.turn_table_reward_list) do
            value:DeleteMe()
        end
        self.turn_table_reward_list = nil
    end

    if self.lottery_task_list then
        self.lottery_task_list:DeleteMe()
        self.lottery_task_list = nil
    end

    self.turn_table_reward_cfg = nil
    self.reward_map_item_id_cfg = nil
end

function KfActivityView:FlushOGALotteryCallBack()
    self.is_click_oga_lock = false

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local all_task_info = ServerActivityWGData.Instance:GetOgaLotteryTaskByDay(open_day)
    self.lottery_task_list:SetDataList(all_task_info)

    local cost_item_id, draw_cost_num = ServerActivityWGData.Instance:GetOGADrawCostItemId()
    local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    local color = have_num >= draw_cost_num and COLOR3B.D_GREEN or COLOR3B.D_PINK
    local show_str = string.format(Language.OpenServer.OGARemainDrawTimeHint, ToColorStr(math.floor(have_num / draw_cost_num), color))
    self.node_list.lottery_draw_time_txt.text.text = show_str

    local total_count = 0
    local cur_val = ServerActivityWGData.Instance:GetOGATaskProcessValueByTaskType(0)
    if not IsEmptyTable(all_task_info) then
        for key, value in pairs(all_task_info) do
            if value.cfg_data.type == 0 then
                if total_count < value.cfg_data.param1 then
                    total_count = value.cfg_data.param1
                end
            end
        end    
    end

    local val_color =  (total_count - cur_val) > 0 and COLOR3B.D_GREEN or COLOR3B.D_PINK
    local show_val = total_count - cur_val > 0 and total_count - cur_val or 0
    local kill_time_str = string.format(Language.OpenServer.OGAKillBossTimeHint, ToColorStr(show_val, val_color), ToColorStr(total_count, COLOR3B.D_GREEN))
    self.node_list["lottery_kill_time_txt"].text.text = kill_time_str

    self:FlushOGATurnTableReward()
    self:FlushOGALotteryTime()
end

-- 转盘奖励
function KfActivityView:FlushOGATurnTableReward()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if not IsEmptyTable(self.turn_table_reward_list) and open_day ~= self.oga_lottery_day then
        self.oga_lottery_day = open_day
        local node_num = #self.turn_table_reward_list
        self.turn_table_reward_cfg = ServerActivityWGData.Instance:GetOgaDrawTurnTableRewardCfg(open_day)
        local target_data = {}
        for i = 1, node_num do
            target_data = self.turn_table_reward_cfg[i]
            if target_data then
                self.turn_table_reward_list[i]:SetData(target_data.reward[0])
            end
            self.turn_table_reward_list[i].view:SetActive(nil ~= target_data)
        end        
    end
end

function KfActivityView:OnClickOGALotteryAnimSkip()
    ServerActivityWGData.Instance:ChangeIsSkipOGALotteryAnim()
    self.is_skip_anim = not self.is_skip_anim
    self.node_list.img_nohint_hook:SetActive(self.is_skip_anim)
end

function KfActivityView:OnClickOGALotteryJumpToPanel()
    FunOpen.Instance:OpenViewNameByCfg("boss#boss_vip")
end

function KfActivityView:OnClickOGALottery()
    if self.is_click_oga_lock then 
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.OGALotteryDrawing)
        return
    end

    local cost_item_id, draw_cost_num = ServerActivityWGData.Instance:GetOGADrawCostItemId()
    local have_num = ItemWGData.Instance:GetItemNumInBagById(cost_item_id)
    local can_turn_num = have_num
    if can_turn_num < draw_cost_num then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.LackOfTurnTimeTips)
        return
    end

    self.is_click_oga_lock = true
    self:CancelBlockTimer()
    self:ChangeLotteryBlock(true)
	self.cancel_block_timer = GlobalTimerQuest:AddDelayTimer(function()
        self:ChangeLotteryBlock(false)
    end, 4)
    ServerActivityWGCtrl.Instance:SendOGAExtendReqOperate(OGA_EXTEND_OPERATE_TYPE_ENUM.DRAW)
end

function KfActivityView:OGALotteryPlayRollAnim(index, call_back)
    self:CancelBlockTimer()
    local is_skip = ServerActivityWGData.Instance:GetIsSkipOGALotteryAnim()
    if is_skip then
        self:ChangeLotteryBlock(false)
    end
    if not index then return end

    local seq = index

    if is_skip then
	    self.node_list.point_root.transform.localEulerAngles = Vector3(0, 0, -720 - (45 * seq))
        self.is_click_oga_lock = false
        self:Flush()
        if call_back then call_back() end
        return
    end

	local time = 3
	local draw_time = 0
	draw_time = time
	self.draw_tween = self.node_list.point_root.transform:DORotate(Vector3(0, 0, -720 * time - 45 * (seq)),
        draw_time,
		DG.Tweening.RotateMode.FastBeyond360)
	self.draw_tween:SetEase(DG.Tweening.Ease.OutQuad)
	self.draw_tween:OnComplete(function()
        self.draw_tween = nil
        self.is_click_oga_lock = false
        self:ChangeLotteryBlock(false)
        self:Flush()
        if call_back then call_back() end
	end)
end

function KfActivityView:CancelBlockTimer()
    if self.cancel_block_timer then
        GlobalTimerQuest:CancelQuest(self.cancel_block_timer)
        self.cancel_block_timer = nil
    end
    self.is_click_oga_lock = false
end

function KfActivityView:ChangeLotteryBlock(is_show)
    is_show = is_show or false
    self.node_list["lottery_block"]:SetActive(is_show)
end

function KfActivityView:FlushOGALotteryTime()
    local start_stamp = TimeWGCtrl.Instance:GetTodayBeginningTimestamp()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    start_stamp = start_stamp + 86400
    local cd_time = start_stamp - server_time
    self:FlushOGALotteryCDPart(cd_time)
end

-- 活动倒计时cd
function KfActivityView:FlushOGALotteryCDPart(cd_time)
    local time = cd_time
    if CountDownManager.Instance:HasCountDown("OGA_Lottery_CD") then
		CountDownManager.Instance:RemoveCountDown("OGA_Lottery_CD")
	end
	if time > 0 then
		CountDownManager.Instance:AddCountDown("OGA_Lottery_CD",
			BindTool.Bind(self.UpdateOGALotteryCountDown, self),
			BindTool.Bind(self.OnOGALotteryCDComplete, self),
			nil, time, 1)
	else
		self:OnOGALotteryCDComplete()
	end
end

function KfActivityView:OnOGALotteryCDComplete()
    self.node_list["lottery_remain_time"].text.text = ""
end

function KfActivityView:UpdateOGALotteryCountDown(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	self.node_list["lottery_remain_time"].text.text = time_str
end


------------------------------------------- OGALotteryTaskItemRender -------------------------------------------
OGALotteryTaskItemRender = OGALotteryTaskItemRender or BaseClass(BaseRender)

function OGALotteryTaskItemRender:LoadCallBack()
    -- if not self.reward_list then
    --     self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
    --     self.reward_list:SetStartZeroIndex(true)
    -- end

    XUI.AddClickEventListener(self.node_list["goto_btn"],BindTool.Bind(self.OnClickGoToBtn, self))
    XUI.AddClickEventListener(self.node_list["get_btn"],BindTool.Bind(self.OnClickGetReward, self))

    self.is_can_get = false
    self.is_had_get = false
end

function OGALotteryTaskItemRender:ReleaseCallBack()
    -- if self.reward_list then
    --     self.reward_list:DeleteMe()
    --     self.reward_list = nil
    -- end
end

function OGALotteryTaskItemRender:OnFlush()
    self.is_can_get = false
    self.is_had_get = false

	if not self.data then return end

    local cfg_data = self.data.cfg_data

    -- local reward = cfg_data.reward or {}
    -- self.reward_list:SetDataList(reward)

    local task_str = cfg_data.task_desc
    local process_val = self.data.process_val or 0
    local target_num = cfg_data.param1 or 0

    local status_val = self.data.sort_val or 0
    self.is_can_get = status_val == -1
    self.is_had_get = status_val == 1

    process_val = process_val >= target_num and target_num or process_val
    local cur_num_str = self.is_can_get and ToColorStr(process_val, COLOR3B.L_GREEN) or ToColorStr(process_val, COLOR3B.L_RED)
    task_str = string.format("%s  %s/%s", task_str, cur_num_str, target_num)
    self.node_list["task_desc"].text.text = task_str

	self.node_list["goto_btn"]:SetActive((not self.is_can_get) and (not self.is_had_get))
    self.node_list["get_btn"]:SetActive(self.is_can_get and (not self.is_had_get))
    self.node_list["had_get_flag"]:SetActive(self.is_had_get)
end

function OGALotteryTaskItemRender:OnClickGoToBtn()
    local cfg_data = self.data.cfg_data
    local open_panel = cfg_data.open_panel
    if open_panel and open_panel ~= '' then
        FunOpen.Instance:OpenViewNameByCfg(open_panel)
    end
end

function OGALotteryTaskItemRender:OnClickGetReward()
    local cfg_data = (self.data or {}).cfg_data
    if self.is_can_get and cfg_data then
        ServerActivityWGCtrl.Instance:SendOGAExtendReqOperate(OGA_EXTEND_OPERATE_TYPE_ENUM.FETCH_TASK_REWARD, cfg_data.seq)
    end
end