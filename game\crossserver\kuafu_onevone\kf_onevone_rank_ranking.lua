FieldPvPRankView = FieldPvPRankView or BaseClass(SafeBaseView)

function FieldPvPRankView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_rank_ranking")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
end

function FieldPvPRankView:__delete()

end

function FieldPvPRankView:ReleaseCallBack()
	if self.ranking_list then
		self.ranking_list:DeleteMe()
		self.ranking_list = nil
	end
	self.has_load_callback = nil
	self.need_flush = nil
end

function FieldPvPRankView:LoadCallBack()
    --self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(990,678)  --self.node_list.size.rect.sizeDelta
    self:SetSecondView(nil, self.node_list.size)
   -- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
    self.node_list.title_view_name.text.text = Language.Field1v1.TitleRankRank
	self.ranking_list = AsyncListView.New(OneVOneRankingItemRender, self.node_list.ph_ranking_list)
	self.has_load_callback = true
	if self.need_flush then
		self:Flush(self.need_flush)
		self.need_flush = nil
	end
end

function FieldPvPRankView:OnFlush(param)
	if not self.has_load_callback then
		self.need_flush = param
		return
	end
	local tb = "table"
	if type(param) ~= tb then
		return
	end
	for k,v in pairs(param) do
		if type(v) == tb then
			for m,n in pairs(v) do
				self:FlushRankRankingPanel(n)
			end
		end
	end
end

function FieldPvPRankView:FlushRankRankingPanel(index)
	if index == KFPVP_TYPE.ONE then
		local ranking_data = RankWGData.Instance:GetRankData(RankKind.Cross, CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_1V1_SCORE)
		local rank_list  =  KuafuOnevoneWGData.Instance:GetRankList(ranking_data)
		if self.ranking_list then
			self.ranking_list:SetDataList(rank_list,0)
			self.ranking_list:JumpToTop(true)
		end
		local cfg = KuafuOnevoneWGData.Instance:GetKFOneVOneOtherCfg()
		local my_rank = RankWGData.Instance:GetMyRank(RankKind.Cross, CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_1V1_SCORE)
		self.node_list.lbl_my_jifen.text.text = (KuafuOnevoneWGData.Instance:Get1V1InfoJiFen())
		self.node_list.lbl_my_ranking.text.text = (KuafuOnevoneWGData.Instance:Get1V1InfoJiFen() >= cfg.rank_score_limit and  my_rank or Language.Kuafu1V1.HavaNoRank)

		-- if my_rank and KuafuOnevoneWGData.Instance:Get1V1InfoJiFen() >= 1500 then
			-- self.node_list.ranking_num.text.text = (my_rank)
			-- if my_rank > 9 then
			-- 	self.node_list.ranking_num:SetPosition(self.x - 28, self.y - 20)
			-- else
			-- 	self.node_list.ranking_num:SetPosition(self.x - 13, self.y - 20)
			-- end
			-- self.node_list.img_my_ranking:SetActive(false)
			-- self.node_list.ranking_num:SetActive(true)
			-- self.node_list.little_null:SetActive(false)
		-- else
			-- self.node_list.ranking_num:SetActive(false)
			-- self.node_list.img_my_ranking:SetActive(true)
			-- self.node_list.little_null:SetActive(true)
		-- end
		self.node_list.layout_blank_tip:SetActive(#rank_list <= 0)
	elseif index == KFPVP_TYPE.MORE then
		local ranking_data = KuafuPVPWGData.Instance:GetPvPRankData()
		if self.ranking_list then
			self.ranking_list:SetDataList(ranking_data,0)
			self.ranking_list:JumpToTop(true)
		end
		local kfpvp_other_cfg = KuafuPVPWGData.Instance:GetKaFuTvTCfg().other[1]
		local my_rank = RankWGData.Instance:GetMyRank(RankKind.Cross, CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_3V3_SCORE)
		self.node_list.lbl_my_jifen.text.text = (KuafuPVPWGData.Instance:GetRewardIntegral())
		self.node_list.lbl_my_ranking.text.text = (KuafuPVPWGData.Instance:GetRewardIntegral() >= kfpvp_other_cfg.rank_score_limit and my_rank or Language.Kuafu1V1.HavaNoRank)
		-- if my_rank and KuafuPVPWGData.Instance:GetRewardIntegral() >= kfpvp_other_cfg.rank_score_limit then
			-- self.node_list.ranking_num.text.text = (my_rank)
			-- if my_rank > 9 then
			-- 	self.node_list.ranking_num:SetPosition(self.x - 28, self.y - 20)
			-- else
			-- 	self.node_list.ranking_num:SetPosition(self.x - 13, self.y - 20)
			-- end
			-- self.node_list.img_my_ranking:SetActive(false)
			-- self.node_list.ranking_num:SetActive(true)
			-- self.node_list.little_null:SetActive(false)
		-- else
			-- self.node_list.ranking_num:SetActive(false)
			-- self.node_list.img_my_ranking:SetActive(true)
			-- self.node_list.little_null:SetActive(true)
		-- end
		self.node_list.layout_blank_tip:SetActive(#ranking_data <= 0)
	end
end



OneVOneRankingItemRender = OneVOneRankingItemRender or BaseClass(BaseRender)

function OneVOneRankingItemRender:__init()

end

function OneVOneRankingItemRender:__delete()

end

function OneVOneRankingItemRender:LoadCallBack()
	-- BaseRender.CreateChild(self)
end

function OneVOneRankingItemRender:OnFlush()
	self.node_list.lbl_ranking.text.text = (self.data.rank_index)
	-- self.node_list.img_ranking_bg_2:SetActive(self.data.rank_index % 2 == 0)
	-- self.node_list.img_ranking_bg_1:SetActive(self.data.rank_index % 2== 1)
	local name_tab = Split(self.data.user_name,"_s")
	self.node_list.lbl_rank_name.text.text = name_tab[1]
	self.node_list.lbl_rank_cap.text.text = (self.data.flexible_ll)
	self.node_list.lbl_rank_area.text.text = (self.data.server_id)
	self.node_list.lbl_rank_score.text.text = (self.data.rank_value)
	local rank = KuafuOnevoneWGData.Instance:GetRewardBaseCell(self.data.rank_value)
	self.node_list.lbl_rank_duanwei.text.text = (rank.name)
	local zhuan_num = math.floor(self.data.prof / 10)
	local rolr_prof = self.data.prof % 10
	local pro_str = TransFerWGData.Instance:GetRoleProfNameCfg(zhuan_num, rolr_prof)
	self.node_list.lbl_rank_pro.text.text = (pro_str)

	if self.data.rank_index > 0 and self.data.rank_index <= 3 then
		self:CreateRankMedal(self.data.rank_index)
	else
		self.node_list.rank_img:SetActive(false)
		--self.node_list.img_ranking_image_bg_1:SetActive(false)
	end
	local b, a = ResPath.GetF2Field1v1Rank(rank.rank_id)
	self.node_list.duanwei_1.image:LoadSprite(b, a, function()
	end)
	self.node_list.img_ranking_image_bg_1:SetActive(self.index%2 == 1)
end

function OneVOneRankingItemRender:CreateRankMedal(rank)
	self.node_list.rank_img:SetActive(true)
	self.node_list.rank_img.image:LoadSprite(ResPath.GetF2CommonIcon("icon_paiming_big_"..rank))
end

function OneVOneRankingItemRender:OnSelectChange(is_select)
	self.node_list.highlight:SetActive(is_select)
end
