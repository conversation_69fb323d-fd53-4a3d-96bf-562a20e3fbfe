require("game/festival_activity/festival_special_rank/festival_special_rank_wg_data")

FestivalSpecialRankWGCtrl = FestivalSpecialRankWGCtrl or BaseClass(BaseWGCtrl)
function FestivalSpecialRankWGCtrl:__init()
    if nil ~= FestivalSpecialRankWGCtrl.Instance then
        ErrorLog("[FestivalSpecialRankWGCtrl]:Attempt to create singleton twice!")
    end
    FestivalSpecialRankWGCtrl.Instance = self

    self.data = FestivalSpecialRankWGData.New()

    self:RegisterProtocol(SCFARankInfo, "OnSCFARankInfo")
end

function FestivalSpecialRankWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    FestivalSpecialRankWGCtrl.Instance = nil
end

function FestivalSpecialRankWGCtrl:OnSCFARankInfo(protocol)
    -- print_error("OnSCFARankInfo 《《《 ", protocol)
    self.data:SetInfo(protocol)

    self:FlushView()
end

local function MergeSpecialRankOperate(opera_type, param_1, param_2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.FESTIVAL_ACT_RANK_2
	protocol.opera_type = opera_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

function FestivalSpecialRankWGCtrl:RequestRankInfo()
    self:RequestGoldRankInfo()
end

function FestivalSpecialRankWGCtrl:RequestGoldRankInfo()
    MergeSpecialRankOperate(FESTIVAL_RANK_OPERATE.GOLD_RANK_INFO)
end


function FestivalSpecialRankWGCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2269)
end