OperationActivityView = OperationActivityView or BaseClass(SafeBaseView)

--1非高亮2高亮
local IMG_POS = {
	{ [2] = Vector2(0, 0) },
	{ [2] = Vector2(0, 0) },
}

local TXT_COLOR = {
	{
		[1] = {
			[0] = Color.New(1, 1, 1, 1),
		},
		[2] = {
			[0] = Color.New(29 / 255, 39 / 255, 49 / 255, 1),
		}
	},

	{
		[1] = {
			[0] = Color.New(234 / 255, 251 / 255, 1, 1),
		},
		[2] = {
			[0] = Color.New(15 / 255, 44 / 255, 58 / 255, 1),
		}
	},
}

function OperationActivityView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true, false)
	
	local assetbundle = "uis/view/operation_activity_bottom_ui_prefab"
	-- self:AddViewResource(0, assetbundle, "layout_operation_activity_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(TabIndex.operation_act_ctn_recharge, "uis/view/operation_ctn_recharge_prefab",
		"opera_ctn_recharge")
	-- self:AddViewResource(TabIndex.operation_act_task_chain, "uis/view/operation_activity_ui/task_chain_prefab", "layout_task_chain")
	self:AddViewResource(TabIndex.operation_act_xianshi_miaosha, "uis/view/operation_xianshi_miaosha_prefab",
		"Xianshi_Miaosha_View")
	self:AddViewResource(TabIndex.operation_act_first_recharge, "uis/view/operation_activity_ui/first_recharge_prefab",
		"layout_operation_dayfirst_recharge")
	self:AddViewResource(TabIndex.operation_act_total_recharge, "uis/view/operation_activity_ui/total_recharge_prefab",
		"layout_operation_total_recharge")
	self:AddViewResource(TabIndex.operation_act_mowang_youli, "uis/view/operation_activity_ui/mowang_youli_prefab",
		"layout_mowan")
	self:AddViewResource(TabIndex.operation_act_mowu_jianglin, "uis/view/operation_activity_ui/mowu_jianglin_prefab",
		"layout_mowu_jianglin")
	self:AddViewResource(TabIndex.operation_act_duobei, "uis/view/operation_activity_ui/duobei_prefab",
		"layout_operation_duobei")
	self:AddViewResource(TabIndex.operation_act_watering_flowers, "uis/view/operation_watering_flowers_prefab",
		"watering_flowers_view")
	-- self:AddViewResource(TabIndex.operation_act_chushen, "uis/view/operation_tiancai_chushen_prefab", "layout_tiancai_chushen_view")
	self:AddViewResource(TabIndex.operation_act_turntable, "uis/view/operation_turntable_prefab", "layout_oa_turntable")
	self:AddViewResource(TabIndex.operation_act_fish, "uis/view/operation_fish_prefab", "layout_oa_fish")
	--self:AddViewResource(TabIndex.operation_act_fish, "uis/view/operation_fish_prefab", "fish_61_cat")
	self:AddViewResource(TabIndex.operation_act_exchange, "uis/view/operation_exchange_shop_prefab",
		"layout_exchange_shop")
	-- self:AddViewResource(TabIndex.operation_act_quanfu_juanxian, "uis/view/operation_juanxian_prefab", "layout_juanxian")
	self:AddViewResource(TabIndex.operation_act_login_reward, "uis/view/operation_activity_ui/denglu_youli_prefab",
		"layout_login_reward")
	self:AddViewResource(TabIndex.operation_act_happy_kuanghuan, "uis/view/operation_happy_kuanghuan_prefab",
		"layout_happy_kuanghuan_view")
	-- self:AddViewResource(TabIndex.operation_act_fengzheng, "uis/view/operation_activity_ui/fengzheng_ui_prefab", "layout_fz_duobao")\
	self:AddViewResource(TabIndex.operation_act_recharge_rank, "uis/view/operation_activity_ui/recharge_rank_prefab",
		"layout_recharge_rank")
	self:AddViewResource(TabIndex.operation_act_watering_flowers, "uis/view/operation_watering_flowers_prefab",
		"watering_flowers_friend_obj")
	
	-- self:AddViewResource(0, assetbundle, "operation_activity_close_btn")
	self:AddViewResource(0, assetbundle, "rule_panel")
	-- self:AddViewResource(0, assetbundle, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	self:AddViewResource(0, assetbundle, "opera_act_common_remain_time")

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

	self:InitContinueChongZhi() --连续充值
	self:InitDayShouChongView() --首充
	self:InitXianShiMiaoSha() --限时秒杀
	self:InitLeiChongView()  --超值累充
	self:InitWateringFlowers()
	--风筝view
	--self:InitFengZhengView()
	self.item_data_event = BindTool.Bind1(self.ItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end


function OperationActivityView:ReleaseCallBack()
	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	self.is_can_set_zhuti = false
	if CountDownManager.Instance:HasCountDown("operation_common_count_down") then
		CountDownManager.Instance:RemoveCountDown("operation_common_count_down")
	end

	if self.chuanwen_update_event then
		GlobalEventSystem:UnBind(self.chuanwen_update_event)
		self.chuanwen_update_event = nil
	end

	self.cur_zhuti = nil
	self.old_longgu_list = nil

	self:ReleaseContinueChongZhi()
	self:DeleteXianShiMiaoSha()
	self:DTYYReleaseShouChongView()
	self:DTYYReleaseLeiChongView()
	self:ReleaseMoWuCallBack()
	self:ReleaseTaskChainView()
	self:ReleaseCallBackMoWang()
	self:ReleaseCallBackJuanXianView()
	self:ReleaseDBView()
	self:ReleaseTurnTableView()
	self:ReleaseFishView()
	self:ReleaseExchangeCallBack()
	self:DeleteWateringFlowers()
	self:DeleteChuShen()
	self:ReleaseCallBackHappyKhView()
	self:ReleaseLoginRewardView()
	--风筝view
	-- self:ReleaseFengZhengView()
	self:DeleteRechargeRank()
	self.tabbar_loading = nil

	self.rule_content = nil
	self.rule_title = nil
	self.is_can_set_zhuti = nil

	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end
end

function OperationActivityView:ItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num,
											  new_num)
	local index = self.show_index
	if index == TabIndex.operation_act_exchange then
		self:FlushExchange()
	elseif index == TabIndex.operation_act_fengzheng then
		self.OperationActivityView:OnLianHuaItemChangeCallBack(change_item_id, change_item_index, change_reason,
			put_reason, old_num, new_num)
	elseif index == TabIndex.operation_act_fish then
		self:FishItemChange()
	end
end

function OperationActivityView:OpenCallBack()
	if self.is_can_set_zhuti then
		self:SetBottomInfo()
	end
	ExchangeShopWGCtrl.Instance:SendOperExchange(OA_ACT_COMMON_CONVERT_OPERA_TYPE.INFO)
	self:SetTabSate()
end

function OperationActivityView:OpenIndexCallBack(index)
	if index == TabIndex.operation_act_first_recharge then
		self:OpenIndexCallBackShouChongView()
	elseif index == TabIndex.operation_act_total_recharge then
		self:OpenIndexCallBackLeiChong()
	end
end

function OperationActivityView:LoadCallBack()
	-- ViewName
	self.node_list.title_view_name.text.text = Language.ViewName.OperationActivityView

	self.tabbar_loading = false
	if not self.tabbar then
		local tab_index_name, remind_tab = OperationActivityWGData.Instance:GetOperationViewInfo()
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")

		local common_path = "uis/view/common_panel_prefab"

		-- local img_path = ResPath.GetOABottomImg()
		local cur_zhuti, theme_cfg = OperationActivityWGData.Instance:GetCurThemeType()
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabbarInfo, self))
		self.tabbar:Init(tab_index_name, nil, common_path, nil, remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.is_can_set_zhuti = true
	self:SetBottomInfo()
	XUI.AddClickEventListener(self.node_list.common_rule_btn, BindTool.Bind(self.OnClickCommonRuleBtn, self))

	--list的箭头显示.
	self.node_list.scroll.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.ScrollerEndScrolled, self)) --列表滑动监听
end


--列表的箭头显示.
function OperationActivityView:ScrollerEndScrolled()
	local val = self.node_list.scroll.scroll_rect.verticalNormalizedPosition
	-- self.node_list.boss_left_arrow:SetActive(val ~= 0 and val > 0.1)
	-- print_error("val:",val)
	self.node_list.img_more_arrow:SetActive( val > 0.2)
end

--重新设置
function OperationActivityView:CreatTabbar()
	if self.tabbar_loading then
		self:SetTabbarInfo()
	end
end

--tabbar是否加载完毕
function OperationActivityView:SetTabbarInfo()
	self.tabbar_loading = true

	local cur_zhuti, theme_cfg = OperationActivityWGData.Instance:GetCurThemeType()
	local img_bundle = ResPath.GetOABottomImg()

	if self.tabbar and self.tabbar_loading and theme_cfg then
		-- left_tap_icon 这个好像也用不到了
		self.tabbar:SetVerTabbarNameImgRes(img_bundle, theme_cfg.left_tap_icon)
		self.tabbar:FlushVerImage()
		-- Tabber资源切换 暂时注释
		-- local left_tap_light = OperationActivityWGData.Instance:GetCurTypeImgName("a2_zx_xz")
		-- local left_tap_dark  = OperationActivityWGData.Instance:GetCurTypeImgName("a2_zx_mxz")
		-- self.tabbar:SetVerImageTxtColor(img_bundle, left_tap_dark, left_tap_light, TXT_COLOR[cur_zhuti][1],
		-- 	TXT_COLOR[cur_zhuti][2], nil, IMG_POS[cur_zhuti])
	end

	self:SetTabSate()
end

--底板预制特殊加载，适应在线时候出现的热更情况
function OperationActivityView:SetBottomInfo()
	local cur_zhuti, theme_cfg = OperationActivityWGData.Instance:GetCurThemeType()
	if not theme_cfg then
		return
	end

	if self.cur_zhuti and self.cur_zhuti == cur_zhuti then
		return
	end
	self.cur_zhuti = cur_zhuti

	-- 背景底图 TODO 后续删除 改到 ShowIndex了
	-- local rawimg_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_bg")
	-- local bundle, asset = ResPath.GetRawImagesPNG(rawimg_bg_name)
	-- self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- end)


	-- local close_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a2_wg_yc_gb")
	-- self.node_list.btn_image.image:LoadSprite(ResPath.GetOABottomImg(close_bg_name))
	--[[ 挂龙骨动画
	if self.old_longgu_list then
		for _,v in pairs(self.old_longgu_list) do
			v:SetActive(false)
		end
	else
		self.old_longgu_list = {}
	end
	if theme_cfg.longgu_anim and theme_cfg.longgu_anim ~= "" then
		local longgu_anim_list = Split(theme_cfg.longgu_anim, ",")
		local obj_bundle = ResPath.GetOperationActivityLongGuPath()
		local parent = self.node_list.longgu_root.transform
		local res_async_loader = AllocResAsyncLoader(self, theme_cfg.longgu_anim)
		res_async_loader:Load(obj_bundle, theme_cfg.longgu_anim, nil,
			function(new_obj)
				if IsNil(new_obj) then
					return
				end
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(parent, false)
				table.insert(self.old_longgu_list, obj)
			end)
	end
	--]]
	local color = OperationActivityWGData.Instance:GetCurThemeColor()
	local color_c3d = Str2C3b(color)
	self.node_list.outside_rule_content.text.color = color_c3d
	--self.node_list.tip_label.text.color = color_c3d
end

function OperationActivityView:LoadIndexCallBack(index)
	if index == TabIndex.operation_act_ctn_recharge then --连续充值
		self:LoadIndexCallBackContinueChongZhi()
		-- elseif index == TabIndex.operation_act_task_chain then	-- 任务链
		-- 	self:LoadIndexCallBackTaskChain()
	elseif index == TabIndex.operation_act_xianshi_miaosha then --限时秒杀
		self:LoadIndexCallBackXianShiMiaoSha()
	elseif index == TabIndex.operation_act_first_recharge then --每日首充
		self:LoadIndexCallBackDayShouChongView()
	elseif index == TabIndex.operation_act_total_recharge then --超值累充
		self:LoadIndexCallBackLeiChongView()
	elseif index == TabIndex.operation_act_mowang_youli then --魔王有礼
		self:LoadIndexCallBackMoWang()
	elseif index == TabIndex.operation_act_mowu_jianglin then --魔物降临
		self:InitMoWuCallBack()
	elseif index == TabIndex.operation_act_quanfu_juanxian then --全服捐献
		self:LoadCallBackJuanXianView()
		-- elseif index == TabIndex.operation_act_chushen then  --天才厨神
		-- 	self:LoadIndexCallBackChuShen()
	elseif index == TabIndex.operation_act_duobei then --多倍有礼
		self:LoadIndexCallBackDuoBei()
	elseif index == TabIndex.operation_act_turntable then --兔女郎宝藏
		self:LoadTurnTableView()
		self:LoadChuanWenEvent()
	elseif index == TabIndex.operation_act_fish then --辛运锦鲤
		self:LoadFishView()
		self:LoadChuanWenEvent()
	elseif index == TabIndex.operation_act_exchange then
		self:LoadIndexCallBackExchange()
	elseif index == TabIndex.operation_act_login_reward then  --登录有礼
		self:LoadIndexCallBackLoginRewarView()
	elseif index == TabIndex.operation_act_watering_flowers then --种花浇水
		self:LoadIndexCallBackWateringFlowers()
	elseif index == TabIndex.operation_act_happy_kuanghuan then --幸福狂欢
		self:LoadCallBackHappyKhView()
	elseif index == TabIndex.operation_act_fengzheng then     --风筝夺宝	--风筝view
		self:LoadCallBackFengZhengView()
	elseif index == TabIndex.operation_act_recharge_rank then --充值排行
		self:LoadIndexCallBackRechargeRank()
	end
end

function OperationActivityView:LoadChuanWenEvent()
	if not self.chuanwen_update_event then
		self.chuanwen_update_event = GlobalEventSystem:Bind(OPERATION_ACTIVITY.CHUAN_WEN,
			BindTool.Bind(self.UpdateChuanWenRecord, self))
	end
end

function OperationActivityView:ShowIndexCallBack(index)
	local rawimg_bg_name = "a3_xskh_bg_1" --背景底图

	self:ClearRuleInfo()
	self:SetRemainTimePos()
	self:SetRuleInfoActive(false)
	self:SetTimeInfoActive(true)
	self:SetActRemainTime(index)
	self:SetRightTimeTitleText(Language.OpertionAcitvity.ActivityTime)
	if index == TabIndex.operation_act_ctn_recharge then     --连续充值
		self:ShowIndexCallBackContinueChongZhi()
	elseif index == TabIndex.operation_act_xianshi_miaosha then --限时秒杀
		self:ShowIndexCallXianShiMiaoSha()
	elseif index == TabIndex.operation_act_quanfu_juanxian then --全服捐献
		self:ShowIndexCallBackJuanXianView()
		self:OnJXBtnTipClickHnadler()
	elseif index == TabIndex.operation_act_total_recharge then --超值累充
		rawimg_bg_name = "a3_xskh_bg_3"
		self:ShowLeiChongCallBack()
		-- elseif index == TabIndex.operation_act_chushen then --天才厨神
		-- 	self:ShowIndexCallChuShen()
	elseif index == TabIndex.operation_act_watering_flowers then --种花浇水
		self:ShowIndexCallWateringFlowers()
	elseif index == TabIndex.operation_act_mowu_jianglin then --魔物降临
		rawimg_bg_name = "a3_xskh_bg_2"
		self:ShowIndexCallBackJiangLinView()
		self:OnJLBtnTipClickHnadler()
	elseif index == TabIndex.operation_act_first_recharge then --每日首充
		rawimg_bg_name = "a3_xskh_bg_2"
	elseif index == TabIndex.operation_act_login_reward then  --登录有礼
		rawimg_bg_name = "a3_xskh_bg_2"
	elseif index == TabIndex.operation_act_happy_kuanghuan then
		--self:ShowIndexCallBackHappyKhView()	
		rawimg_bg_name = "a3_xskh_bg_2"
		self:OnClickHappyKhState()
	elseif index == TabIndex.operation_act_mowang_youli then
		rawimg_bg_name = "a3_xskh_bg_2"
	elseif index == TabIndex.operation_act_exchange then
		rawimg_bg_name = "a3_xskh_bg_2"
		self:OnSelectExchangeShop()
	elseif index == TabIndex.operation_act_fengzheng then --风筝view
		self:ShowIndexCallBackFengZhengView()
	elseif index == TabIndex.operation_act_fish then
		self:FlushAllFishInfo()
	elseif index == TabIndex.operation_act_recharge_rank then
		self:FlushRechargeRank()
	elseif index == TabIndex.operation_act_turntable then --翻牌豪礼 兔女郎宝藏
		rawimg_bg_name = "a3_xskh_bg_2"
	end

	local act_type = 0
	if index == TabIndex.operation_act_first_recharge then
		act_type = ACTIVITY_TYPE.OPERATION_FIRST_RECHARGE
	elseif index == TabIndex.operation_act_total_recharge then
		act_type = ACTIVITY_TYPE.OPERA_ACT_TOTAL_RECHARGE
	elseif index == TabIndex.operation_act_mowang_youli then
		act_type = ACTIVITY_TYPE.OPERA_ACT_MOWANG_YOULI
	elseif index == TabIndex.operation_act_watering_flowers then
		act_type = ACTIVITY_TYPE.OPERA_ACT_WATERING_FLOWERS
	elseif index == TabIndex.operation_act_turntable then
		act_type = ACTIVITY_TYPE.OPERA_ACT_TURNTABLE
	elseif index == TabIndex.operation_act_fish then
		act_type = ACTIVITY_TYPE.OPERA_ACT_FISH
	elseif index == TabIndex.operation_act_exchange then
		act_type = ACTIVITY_TYPE.OPERA_ACT_EXCHANGE
	elseif index == TabIndex.operation_act_duobei then
		act_type = ACTIVITY_TYPE.OPERA_ACT_DUOBEI_YOULI
	elseif index == TabIndex.operation_act_mowu_jianglin then
		act_type = ACTIVITY_TYPE.OPERA_ACT_MOWU_JIANGLIN
	elseif index == TabIndex.operation_act_login_reward then
		act_type = ACTIVITY_TYPE.OPERA_ACT_LOGIN_REWARD
	elseif index == TabIndex.operation_act_happy_kuanghuan then
		act_type = ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN
	elseif index == TabIndex.operation_act_recharge_rank then
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_RANK_2
	end

	if act_type > 0 then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.OperationActivityView, act_type)
	end

	self:ShowIndexTurnTable(index)
	self:ShowIndexFishView(index)
	self:ShowIndexRechargeRank(index)

	-- 背景底图
	local rawimg_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName(rawimg_bg_name)

	local bundle, asset = ResPath.GetRawImagesPNG(rawimg_bg_name)
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function OperationActivityView:CloseCallBack()
	self:WateringFlowersShowIndexAndCloseCallBack()
	self:CloseFishCallBack()
	-- self:FengZhengCloseCallBack()
	self:CloseTurnTableView()
end

function OperationActivityView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.operation_act_ctn_recharge then
				self:OnFlushContinueChongZhi(param_t)
				-- elseif index == TabIndex.operation_act_task_chain then
				-- 	self:OnFlushTaskChainView(param_t)
			elseif index == TabIndex.operation_act_xianshi_miaosha then --限时秒杀
				self:FlushXianShiMiaoSha()
			elseif index == TabIndex.operation_act_first_recharge then
				self:DTYYOnFlushShouChongView()
			elseif index == TabIndex.operation_act_total_recharge then
				self:DTYYFlushLeiChongView()
				-- elseif index == TabIndex.operation_act_chushen then  --天才厨神
				-- 	self:FlushChuShen()
			elseif index == TabIndex.operation_act_mowang_youli then --魔王有礼
				self:OnFlushMoWang(param_t)
			elseif index == TabIndex.operation_act_mowu_jianglin then --魔物降临
				self:OnFlushMoWuView()
			elseif index == TabIndex.operation_act_quanfu_juanxian then --全服捐献
				self:OnFlushJuanXianView()
			elseif index == TabIndex.operation_act_duobei then
				self:FlushDBView()
			elseif index == TabIndex.operation_act_watering_flowers then --种花浇水
				self:FlushWateringFlowers()
			elseif index == TabIndex.operation_act_exchange then
				self:FlushExchange()
			elseif index == TabIndex.operation_act_login_reward then --登录有礼
				self:OperationFlushLoginView()
			elseif index == TabIndex.operation_act_happy_kuanghuan then --幸福狂欢
				self:OnFlushHappyKhView()
			elseif index == TabIndex.operation_act_fengzheng then --风筝view
				self:OnFlushFengZhengView()
			elseif index == TabIndex.operation_act_recharge_rank then --充值排行
				self:FlushRechargeRank()
			end
		elseif "flush_invite_time" == k then
			self:FlushWaterTextInvite()
		elseif "RefreshDayIndex" == k then
			self:RefreshDayIndex()
			self:OnFlushContinueChongZhi(param_t)
		elseif "RefreshFirstDayIndex" == k then
			self:DTYYOnFlushShouChongView()
		elseif "RefreshJuanXian" == k then
			self:ChangeViewInfoShow()
		elseif "RefreshJuanXianXunYuLu" == k then
			self:FlushXylRedPoint()
		elseif "RefreshDuoBei" == k then
			self:FlushDBView()
		elseif "turntable" == k then
			self:FlushTurnTableView(v)
		elseif "fish" == k then
			self:FlushFishView(v)
		elseif "RefreshLogin" == k then
			self:OperationFlushLoginView()
		elseif "ChuShenResult" == k then
			self:ChuShenPlayAni()
		elseif "RefreshLoginReward" == k then
			local day_index = v[1] or 0
			self:ShowLoginReward(day_index)
		elseif "xianshi_hot_update" == k then
			self:FlushXianShiMiaoShaPictrue()
			self:FlushXianShiMiaoSha()
		elseif "waterflower_hot_update" == k then
			self:FlushWateringFlowersPicture()
			self:FlushWateringFlowers()
		elseif "ChuShenHotUpdata" == k then
			self:FlushChuShenHot()
		elseif "ChuShenAllInfo" == k then
			self:ChuShenPreviousRewardData()
		elseif "FZRecord" == k then
			self:FZFlushRecordNum()
		elseif "FZAnim" == k then
			self:FZStartDrawAnim()
			self:FlushItemNum()
		elseif "mowang_youli_hot_update" == k then
			self:FlushMoWangViewInfo()
		elseif "mowang_youli_times" == k then
			self:FlushPreviewRewardMoWang()
		elseif "boss_times" == k then
			--self:OnBossTimesChange()
		elseif "flush_water_flower" == k then
			self:FlushFriendListView()
		end
	end
end

--按钮的显示和隐藏
function OperationActivityView:SetTabSate()
	if nil == self.tabbar or not self.tabbar_loading then
		return
	end

	local activity_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_config_auto").operation_activity_dec
	table.sort(activity_cfg, SortTools.KeyLowerSorter("rank_id"))
	-- 这里边的条件包含服务器的开启状态和你注册的开启条件，其他特殊处理你酌情来
	for k, v in ipairs(activity_cfg) do
		local tab_index = v.rank_id * 10
		local is_open = OperationActivityWGData.Instance:GetActivityState(v.activity_type)

		-- if v.activity_type == ACTIVITY_TYPE.OPERA_ACT_IMAGE_SHOW then
		-- 	is_open = true
		-- end

		is_open = is_open and v.btn_type < 100 -- 当屏蔽用不在这个界面显示
		self.tabbar:SetToggleVisible(tab_index, is_open)
	end
end

--如果传入的按钮索引为空才会走这里
function OperationActivityView:CalcShowIndex()
	return OperationActivityWGData.Instance:GetOneOpenTabIndex()
end

--部分活动需要实时更新传闻
function OperationActivityView:UpdateChuanWenRecord(protocol)
	local act_type, str = OperationActivityWGData.Instance:FilterChuanWen(protocol)
	if self:GetShowIndex() == TabIndex.operation_act_fish and act_type == ACTIVITY_TYPE.OPERA_ACT_FISH then
		self:UpdateFishRecord(str)
	elseif act_type == ACTIVITY_TYPE.OPERA_ACT_TURNTABLE then
		self:UpdateTurnTableRecord()
	end
end

--设置活动剩余时间 index: tab_index , end_time:活动结束时间戳
function OperationActivityView:SetActRemainTime(index, end_time)
	local act_end_time = 0
	if end_time then
		act_end_time = end_time
	else
		local activity_type = OperationActivityWGData.Instance:GetCurSelectActivityType(index)
		act_end_time = OperationActivityWGData.Instance:GetActivityInValidTime(activity_type)
	end

	if CountDownManager.Instance:HasCountDown("operation_common_count_down") then
		CountDownManager.Instance:RemoveCountDown("operation_common_count_down")
	end
	if act_end_time ~= 0 then
		self.node_list.remain_time_root:SetActive(true)
		local time = act_end_time - TimeWGCtrl.Instance:GetServerTime()
		CountDownManager.Instance:AddCountDown("operation_common_count_down",
			BindTool.Bind(self.CommonUpdateTime, self),
			BindTool.Bind(self.CommonCompleteTime, self), nil, time, 1)
	end
end

function OperationActivityView:CommonUpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	local str = string.format(Language.OpertionAcitvity.ActEndTime, TimeUtil.FormatTimeDHMS(temp_seconds))
	if self.node_list and self.node_list.common_remain_time_text then
		self.node_list.common_remain_time_text.text.text = str
	end
end

function OperationActivityView:CommonCompleteTime(elapse_time, total_time)
	if self.node_list and self.node_list.common_remain_time_text and self.node_list.remain_time_root then
		self.node_list.remain_time_root:SetActive(false)
		self.node_list.common_remain_time_text.text.text = ""
	end
end

--设置活动剩余时间位置
function OperationActivityView:SetRemainTimePos(vector2_pos)
	self.node_list.remain_time_root.rect.anchoredPosition = vector2_pos or Vector2.zero
end

function OperationActivityView:SetRuleInfoActive(enabled)
	if self.node_list and self.node_list.rule_panel_root then
		self.node_list.rule_panel_root:SetActive(enabled)
	end
end

function OperationActivityView:SetTimeInfoActive(enabled)
	if self.node_list and self.node_list.remain_time_root then
		self.node_list.remain_time_root:SetActive(enabled)
	end
end

--设置外边玩法提示
function OperationActivityView:SetOutsideRuleTips(content)
	self.node_list.outside_rule_content.text.text = content
end

function OperationActivityView:SetRuleInfo(rule_content, rule_title)
	self.rule_content = rule_content
	self.rule_title = rule_title
end

function OperationActivityView:ClearRuleInfo()
	self.rule_content = nil
	self.rule_title = nil
end

function OperationActivityView:OnClickCommonRuleBtn()
	if not self.rule_content or not self.rule_title then
		print_error("rule_content 或者 rule_title 是nil,  赶紧查运营活动调了 SetRuleInfo 的地方")
		return
	elseif self.rule_content == "" or self.rule_title == "" then
		print_error("rule_content 或者 rule_title 赋了空， 赶紧查运营活动调了 SetRuleInfo 的地方")
		return
	end
	RuleTip.Instance:SetContent(self.rule_content, self.rule_title)
end

function OperationActivityView:SetRightTimeTitleText(dec)
	self.node_list["tip_label"].text.text = dec
end
