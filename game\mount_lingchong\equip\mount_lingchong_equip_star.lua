--骑宠装备升星
MountLingChongEquipView = MountLingChongEquipView or BaseClass(SafeBaseView)

function MountLingChongEquipView:InitStarView()
    self.node_list.btn_upstar.button:AddClickListener(BindTool.Bind(self.OnClickUpstar, self))
    if not self.upstar_stuff_item then
        self.upstar_stuff_item = ItemCell.New(self.node_list.up_star_stuff_pos)
    end
    -- if not self.cur_upstar_item then
    --     self.cur_upstar_item = MountPetStarItem.New(self.node_list.cur_upstar_item, true)
    --     --self.cur_upstar_item:SetIsShowTips(false)
    -- end
    if not self.target_upstar_item then
        self.target_upstar_item = MountPetStarItem.New(self.node_list.target_upstar_item)
        --self.target_upstar_item:SetIsShowTips(false)
    end
    self.last_star_level = -1
	self.last_star_part = -1
end

function MountLingChongEquipView:ShowStarCallBack()
    self.cur_star_item_id = 0
end

function MountLingChongEquipView:OnSelectEquipStarItemHandler()
    local cur_item_data = self:GetCurSelectData()
	if not cur_item_data or self.cur_star_item_id == cur_item_data.item_id then
		return
	end
	self.last_star_level = -1
	self.last_star_part = -1
    self.cur_star_item_id = cur_item_data.item_id
    self:FlushStarView()
end

function MountLingChongEquipView:FlushStarView()
    local cur_item_data = self:GetCurSelectData()
    if not cur_item_data then
        return
    end
    
    --self.cur_upstar_item:SetData(cur_item_data)
    local cur_level = cur_item_data and cur_item_data.star_level or 0
    if cur_level >= MountLingChongEquipWGData.MaxStar_Lv then
        -- local data = {}
        -- data.item_id = 0
        -- self.target_upstar_item:SetData(data)
        -- self.target_upstar_item:SetLockImgEnable(true)
        self.node_list.upstar_max_state:SetActive(true)
        self.node_list.star_nomax_state:SetActive(false)
    else
        -- local data = {}
        -- data.item_id = cur_item_data.item_id
        -- data.is_bind = cur_item_data.is_bind
        -- data.star_level = cur_level + 1
        -- data.strengthen_level = cur_item_data.strengthen_level
        -- self.target_upstar_item:SetData(data)
        -- self.target_upstar_item:SetLockImgEnable(false)
        self.node_list.upstar_max_state:SetActive(false)
        self.node_list.star_nomax_state:SetActive(true)
    end

    local data = {}
    data.item_id = cur_item_data.item_id
    data.is_bind = cur_item_data.is_bind
    data.star_level = cur_level + 1
    data.strengthen_level = cur_item_data.strengthen_level
    self.target_upstar_item:SetData(data)
    self.target_upstar_item:SetLockImgEnable(false)


    self.node_list.qc_equip_star_name.text.text = ItemWGData.Instance:GetItemName(cur_item_data.item_id)

    self:FlushStarStuffItem()
    self:FlushStarAttr(cur_level)
    if self.last_star_part == cur_item_data.part and cur_level > self.last_star_level then
		self.node_list.upstar_effect_root:SetActive(true)
        TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengxing, is_success = true, pos = Vector2(0, 0),
            parent_node = self.node_list["upstar_effect_root"]})
    end
    
	self.last_star_level = cur_level
	self.last_star_part = cur_item_data.part
end

function MountLingChongEquipView:FlushStarStuffItem()
    if self:IsOpen() and self:IsLoadedIndex(MountLingChongEquipViewIndex[self.cur_show_type].UpStar ) then
        local cur_item_data = self:GetCurSelectData()
        local cur_level = cur_item_data and cur_item_data.star_level or 1
        if cur_level < MountLingChongEquipWGData.MaxStar_Lv and not IsEmptyTable(cur_item_data) then
            local cur_cfg = MountLingChongEquipWGData.Instance:GetStarCfgByLevel(self.cur_show_type, self.cur_select_part, cur_level + 1)
            if not IsEmptyTable(cur_cfg) then
                local data = {}
                data.item_id = cur_cfg.consume_stuff
                data.num = cur_cfg.num
                local num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
                self.upstar_stuff_item:SetData(data)
                local color = num >= cur_cfg.num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
                self.upstar_stuff_item:SetRightBottomTextVisible(true)
                self.upstar_stuff_item:SetRightBottomText(ToColorStr(num .. "/"..cur_cfg.num , color))
            end
        end
    end
end

function MountLingChongEquipView:FlushStarAttr(level)
    local attr, enhance_add_per = MountLingChongEquipWGData.Instance:GetStarAttrByLevel(self.cur_show_type, self.cur_select_part, MountLingChongEquipWGData.MaxStar_Lv) --取出所有属性
    enhance_add_per = enhance_add_per / 100 .. "%"
    for i = 1, 5 do
        local color, str
        local is_active = true
        str = i
        if level >= i then --已激活
            color = COLOR3B.DEFAULT_NUM
            --str = Language.MountPetEquip.Active
        elseif level + 1 == i then  --本次激活
            color = COLOR3B.DEFAULT
            --str = Language.MountPetEquip.CurStarActive
        else  --未激活
            is_active = false
            color = TIPS_COLOR.ATTR_NAME
            --str = string.format(Language.MountPetEquip.LevelStarActive, i)
        end

        if attr[i] then
            local attr_name = EquipmentWGData.Instance:GetAttrStrByAttrId(attr[i].attr_type)
            local attr_value = AttributeMgr.PerAttrValue(attr_name,  attr[i].attr_value)
            attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(attr_name, false, false)
            if is_active then
                self.node_list["upstar_attr"..i].text.text = ToColorStr(attr_name..attr_value, color)
            else
                self.node_list["upstar_attr"..i].text.text = ToColorStr(attr_name..attr_value, TIPS_COLOR.ATTR_NAME)
            end
            self.node_list["upstar_des"..i].text.text = ToColorStr(str, color)
            self.node_list["jihuo" .. i].text.text = ToColorStr(Language.MountPetEquip.Active, color)
        else
            local attr_name = Language.MountPetEquip.EquipAddPercent
            if is_active then
                self.node_list["upstar_attr"..i].text.text = ToColorStr(attr_name..enhance_add_per, color)
            else
                self.node_list["upstar_attr"..i].text.text = ToColorStr(attr_name..enhance_add_per, TIPS_COLOR.ATTR_NAME)
            end
            self.node_list["upstar_des"..i].text.text = ToColorStr(str, color)
            self.node_list["jihuo" .. i].text.text = ToColorStr(Language.MountPetEquip.Active, color)
        end
    end
end

function MountLingChongEquipView:DeleteStarView()
    if self.upstar_stuff_item then
        self.upstar_stuff_item:DeleteMe()
    end
    self.upstar_stuff_item = nil
    -- if self.cur_upstar_item then
    --     self.cur_upstar_item:DeleteMe()
    -- end
    -- self.cur_upstar_item = nil
    if self.target_upstar_item then
        self.target_upstar_item:DeleteMe()
    end
    self.target_upstar_item = nil
end

function MountLingChongEquipView:OnClickUpstar()
    local cur_item_data = self:GetCurSelectData()
    if not IsEmptyTable(cur_item_data) then
        local cur_cfg = MountLingChongEquipWGData.Instance:GetStarCfgByLevel(self.cur_show_type, self.cur_select_part, cur_item_data.star_level + 1)
        if not IsEmptyTable(cur_cfg) then
            local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.consume_stuff)
            if num < cur_cfg.num then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.UpstarNotEnough)
                TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cur_cfg.consume_stuff})
                return
            end
        end
        MountLingchongEquipWGCtrl.Instance:SendMountPetEquipReq(self.cur_show_type, 
        MOUNT_PET_EQUIP_OPERA_TYPE.MOUNT_PET_EQUIP_OPER_TYPE_UP_STAR, cur_item_data.part) --请求升星
    end
end

------------------------穿戴的装备----------------------------------------------------------------------
MountPetStarItem = MountPetStarItem or BaseClass(ItemCell)

function MountPetStarItem:__init(instance, is_cur_wear)
    self.is_cur_wear = is_cur_wear
end

function MountPetStarItem:LoadCallBack()

end

function MountPetStarItem:OnFlush()
    if IsEmptyTable(self.data)  then
        self:SetVisible(false)
    else
        self:SetVisible(true)
    end
    ItemCell.OnFlush(self)

    if self.data.star_level then
        self:SetLeftTopImg(self.data.star_level)
    end
    if self.data.strengthen_level and self.data.strengthen_level > 0 then
        self:SetRightBottomTextVisible(true)
        self:SetRightBottomColorText("+" .. self.data.strengthen_level)
    else
        self:SetRightBottomColorText("")
    end
end

-- 点击格子
function MountPetStarItem:OnClick()
	if self.tip_callback ~= nil then
		local is_black = self.tip_callback()
		if is_black == true then
			return
		end
	end

    if self.is_showtip then
        if self.data then
            local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
            if nil == item_cfg then return end
            TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
            local form_view = self.is_cur_wear and ItemTip.FROM_MOUNTEQUIP_EQUIP or nil
            TipWGCtrl.Instance:OpenItem(self.data, form_view, nil)
        end
        
    end
	BaseRender.OnClick(self)
end