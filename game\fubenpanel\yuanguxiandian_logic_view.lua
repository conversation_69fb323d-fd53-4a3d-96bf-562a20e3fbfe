--远古仙殿任务栏面板
YuanGuXianDianLogicView = YuanGuXianDianLogicView or BaseClass(SafeBaseView)
function YuanGuXianDianLogicView:__init()
    --self.view_cache_time = 5
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_yuangudianxian_info")
	self.active_close = false
end

function YuanGuXianDianLogicView:ReleaseCallBack()
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end
	self.is_out_fb = nil
	if self.yuangu_reward_list then
        self.yuangu_reward_list:DeleteMe()
        self.yuangu_reward_list = nil
    end
    
    if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest) 
		self.timer_quest = nil
	end

	if self.star_view then
		self.star_view:DeleteMe()
		self.star_view = nil
	end

end

function YuanGuXianDianLogicView:LoadCallBack()
	self.yuangu_reward_list = AsyncListView.New(HteRewardCell, self.node_list["yuangu_reward_list"])
end

function YuanGuXianDianLogicView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	self.obj = self.node_list["layout_yuangu_info_root"].gameObject
	self.obj.transform:SetParent(parent.gameObject.transform)
	self.obj.transform.localPosition = Vector3.zero
	self.obj.transform.localScale = Vector3.one
    mainui_ctrl:SetTaskPanel(false, 0, 0)
    mainui_ctrl:SetOtherContents(true)
    mainui_ctrl:SetTaskContents(false)
	mainui_ctrl:SetAutoGuaJi(true)
	mainui_ctrl:SetFBNameState(true, Language.FuBenPanel.ViewNameYuanGuXianDian)

	-- mainui_ctrl:SetTaskAndTeamCallBack(nil, nil)
    self.obj:SetActive(not self.is_out_fb)
    self.is_out_fb = nil
end
function YuanGuXianDianLogicView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function YuanGuXianDianLogicView:CloseCallBack()
	self.is_out_fb = true
    if self.obj then
        self.obj.transform:SetParent(self.root_node_transform.transform)
        self.obj:SetActive(false)
    end
    MainuiWGCtrl.Instance:SetOtherContents(false)
    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskAndTeamCallBack(nil, nil)
	self.data = nil
end

function YuanGuXianDianLogicView:ShowIndexCallBack()
    self.is_out_fb = false
    MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))
	self:Flush()
end

function YuanGuXianDianLogicView:OnFlush()
	local scene_info = TeamEquipFbWGData.Instance:GetHTEFbInfo()
	if not scene_info then
		return
	end
	local item_list = TeamEquipFbWGData.Instance:GetRewardList(scene_info.layer)
	self.yuangu_reward_list:SetDataList(item_list, 3)
	self.node_list.lbl_boshu.text.text = string.format(Language.FuBenPanel.HTENowProg, scene_info.curr_wave_index + 1, scene_info.max_wave_count + 1)
	self.node_list.lbl_desc.text.text = string.format(Language.FuBenPanel.HTEEnemyCount, scene_info.cur_wave_monster_num - scene_info.kill_monster_num)
	self.node_list.fb_desc_tips.text.text = Language.FuBenPanel.FubenPerBossTips2

	self:OnFlushStar()
end

function YuanGuXianDianLogicView:OnFlushStar()
    if nil == self.star_view then
		self.star_view =  FuBenStarClock.New(self.node_list.star_view)
	end
    local time_list = TeamEquipFbWGData.Instance:GetStarTimeList()
	local cfg = TeamEquipFbWGData.Instance:GetSceneCurCfg()
	local data = {}
	if time_list then
		data = {time3 = time_list[3], time2 = time_list[2], time1 = time_list[1], time0 = time_list[0],per3 = cfg.star3, per2 = cfg.star2, per1 = cfg.star1, per0 = cfg.star0, str = Language.Boss.StarAniStr,}
	end
	self.star_view:SetData(data)
	self.star_view:Flush()
    
end
