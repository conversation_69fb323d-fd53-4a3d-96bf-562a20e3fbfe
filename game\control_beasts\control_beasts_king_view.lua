ControlBeastsKingView = ControlBeastsKingView or BaseClass(SafeBaseView)

function ControlBeastsKingView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(832, 594)})
    self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_king")
    self:SetMaskBg(true)
end


function ControlBeastsKingView:__delete()

end

function ControlBeastsKingView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName13

    if not self.left_beast_king_condition then
		self.left_beast_king_condition = AsyncListView.New(BeststsKingConditionRender, self.node_list.left_beast_king_condition)
	end

    if not self.right_beast_king_condition then
		self.right_beast_king_condition = AsyncListView.New(BeststsKingConditionRender, self.node_list.right_beast_king_condition)
	end

    -- 属性
    if self.left_king_attrlist == nil then
        self.left_king_attrlist = {}
        for i = 1, 5 do
            local attr_obj = self.node_list.left_king_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststKingAttrItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.left_king_attrlist[i] = cell
            end
        end
    end

    if self.right_king_attrlist == nil then
        self.right_king_attrlist = {}
        for i = 1, 5 do
            local attr_obj = self.node_list.right_king_attr:FindObj(string.format("attr_%d", i))
            if attr_obj then
                local cell = BeststKingAttrItemRender.New(attr_obj)
                cell:SetIndex(i)
                self.right_king_attrlist[i] = cell
            end
        end
    end
    
	XUI.AddClickEventListener(self.node_list.king_star_up, BindTool.Bind2(self.KingStarUp, self))
end


function ControlBeastsKingView:CloseCallBack()
    self.is_can_unlock = nil
end

function ControlBeastsKingView:ReleaseCallBack()
	if self.left_beast_king_condition then
        self.left_beast_king_condition:DeleteMe()
        self.left_beast_king_condition = nil
    end

    if self.right_beast_king_condition then
        self.right_beast_king_condition:DeleteMe()
        self.right_beast_king_condition = nil
    end

    if self.left_king_attrlist and #self.left_king_attrlist > 0 then
		for _, compose_attr_cell in ipairs(self.left_king_attrlist) do
			compose_attr_cell:DeleteMe()
			compose_attr_cell = nil
		end

		self.left_king_attrlist = nil
	end

    if self.right_king_attrlist and #self.right_king_attrlist > 0 then
		for _, compose_attr_cell in ipairs(self.right_king_attrlist) do
			compose_attr_cell:DeleteMe()
			compose_attr_cell = nil
		end

		self.right_king_attrlist = nil
	end
end

function ControlBeastsKingView:OnFlush(param_t)
    self:FlushKingAttrPanel()
end

-- 刷新属性列表
function ControlBeastsKingView:FlushKingAttrPanel()
    local beast_base_info = ControlBeastsWGData.Instance:GetBeastBaseInfo()

    if not beast_base_info then
        return
    end

    local now_king_lv = beast_base_info.beast_king_level
    local next_king_lv = beast_base_info.beast_king_level + 1
    -- local last_king_lv = beast_base_info.beast_king_level - 1
    local cfg_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(now_king_lv)
    local next_cfg_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(next_king_lv)
    -- local last_cfg_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(last_king_lv)
    local is_full_lv = next_cfg_data == nil
    local pos_x = is_full_lv and 0 or -218
    self.node_list.arrow:CustomSetActive(not is_full_lv)
    self.node_list.right:CustomSetActive(not is_full_lv)
    self.node_list.king_level_max:CustomSetActive(is_full_lv)
    self.node_list.king_star_up:CustomSetActive(not is_full_lv)
    self.node_list.left:SetLocalPosition(pos_x, 0, 0)

    if cfg_data then
        local list = ControlBeastsWGData.Instance:GetLevelKingAttrList(cfg_data.beast_king_data)
        self.node_list.left_beast_king_txt.text.text = string.format(Language.ContralBeasts.KingTips6, now_king_lv) 

        for index, compose_attr_cell in ipairs(self.left_king_attrlist) do
            compose_attr_cell:SetVisible(list[index] ~= nil)

            if list[index] then
                compose_attr_cell:SetData(list[index])
            end
        end

        if self.left_beast_king_condition then
            self.left_beast_king_condition:SetDataList({[1] = {desc = Language.Role.HadActive, is_enough = true}})
        end
    end

    if next_cfg_data and cfg_data then
        local list = ControlBeastsWGData.Instance:GetLevelKingAttrList(next_cfg_data.beast_king_data)
        self.node_list.right_beast_king_txt.text.text = string.format(Language.ContralBeasts.KingTips6, next_king_lv) 

        for index, compose_attr_cell in ipairs(self.right_king_attrlist) do
            compose_attr_cell:SetVisible(list[index] ~= nil)

            if list[index] then
                compose_attr_cell:SetData(list[index])
            end
        end

        local list, is_can_unlock = ControlBeastsWGData.Instance:SetBeastKingConditionData(cfg_data)
        self.is_can_unlock = is_can_unlock

        if self.right_beast_king_condition then
            self.right_beast_king_condition:SetDataList(list)
        end
    end

    self.node_list.king_star_up_remind:CustomSetActive(self.is_can_unlock)
end

------------------------------------------------------------------------
-- 兽王升级
function ControlBeastsKingView:KingStarUp()
    if self.is_can_unlock == nil then
        local beast_base_info = ControlBeastsWGData.Instance:GetBeastBaseInfo()
        if beast_base_info then
            local cfg_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(beast_base_info.beast_king_level)
            local _, is_can_unlock = ControlBeastsWGData.Instance:SetBeastKingConditionData(cfg_data)
            self.is_can_unlock = is_can_unlock
        
            local next_cfg_data = ControlBeastsWGData.Instance:GetBeastKingDataBylevel(beast_base_info.beast_king_level + 1)
            if next_cfg_data == nil then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.KingTips5)
                return
            end
        end
    end
    
	if not self.is_can_unlock then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActCantOpen)
		return
	end

    ControlBeastsWGCtrl:SendOperateTypeBeastKingUpgrade()
end