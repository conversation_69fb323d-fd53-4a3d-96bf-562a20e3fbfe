DailyEquipFbSceneLogic = DailyEquipFbSceneLogic or BaseClass(CommonFbLogic)

function DailyEquipFbSceneLogic:__init()

end

function DailyEquipFbSceneLogic:__delete()
	
end

function DailyEquipFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	
	DailyWGCtrl.Instance:Close()

	FuBenWGCtrl.Instance:OpenTaskFollow()
	FuBenWGCtrl.Instance:UpdataTaskFollow()

	-- XuiBaseView.CloseAllView()
end

function DailyEquipFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function DailyEquipFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	if RoleWGData.Instance.role_vo.level > 130 then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Daily)
	end
end