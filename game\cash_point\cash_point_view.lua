CashPointView = CashPointView or BaseClass(SafeBaseView)

function CashPointView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/cash_point_ui_prefab", "casn_point_view")
end

function CashPointView:__delete()
end

function CashPointView:ReleaseCallBack()
end

function CashPointView:LoadCallBack()
    self.node_list.desc_info_text.text.text = Language.CashPoint.DescInfoText

    XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGoBtn, self))
end

function CashPointView:OnFlush()
    self:FlushCashPointNum()
end

function CashPointView:OnClickGoBtn()
    local jump_path = CashPointWGData.Instance:GetJumpPath()
    FunOpen.Instance:OpenViewNameByCfg(jump_path)
    self:Close()
end

function CashPointView:FlushCashPointNum()
    if self.node_list.cash_point_num then
       local cash_point_num = RoleWGData.Instance:GetAttr("cash_point")
       self.node_list.cash_point_num.text.text = string.format(Language.CashPoint.CashPointNum, cash_point_num)
    end
end