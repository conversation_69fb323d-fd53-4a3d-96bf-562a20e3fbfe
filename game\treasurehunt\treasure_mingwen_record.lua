--铭纹寻宝记录面板
TreasureHuntMingwenRecord = TreasureHuntMingwenRecord or BaseClass(SafeBaseView)

function TreasureHuntMingwenRecord:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "TreasureHuntMingwenRecord"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 488)})
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_mingwen_record")
    self:SetMaskBg(true, true)
end

function TreasureHuntMingwenRecord:ReleaseCallBack()
    if nil ~= self.record_list_view then
		self.record_list_view:DeleteMe()
		self.record_list_view = nil
    end

    if nil ~= self.record_person_list_view then
		self.record_person_list_view:DeleteMe()
		self.record_person_list_view = nil
    end

end

function TreasureHuntMingwenRecord:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TreasureHunt.MingWenRecord
    if self.node_list.ph_xunbao_show_list and not self.record_list_view then
       -- self.node_list.ph_xunbao_show_list.list_simple_delegate.CellSizeDel = BindTool.Bind(self.GetWorldCellSizeDel, self)
        self.record_list_view = AsyncListView.New(MingwenRecordItem, self.node_list.ph_xunbao_show_list)
        
    end
    if self.node_list.ph_xunbao_person_list and not self.record_person_list_view then
        self.record_person_list_view = AsyncListView.New(MingwenRecordPersonItem, self.node_list.ph_xunbao_person_list)
       
    end

    self.node_list.btn_zhanshi_1.button:AddClickListener(BindTool.Bind(self.OnToggleBtn, self, 1))
    self.node_list.btn_zhanshi_2.button:AddClickListener(BindTool.Bind(self.OnToggleBtn, self, 2))
    self:OnToggleBtn(1)
end

function TreasureHuntMingwenRecord:GetWorldCellSizeDel(index) 
    return 48
end

--index 1 全服 2个人
function TreasureHuntMingwenRecord:OnToggleBtn(index) --发送请求
    self.record_index = index
    self.node_list.bg_HL_1:SetActive(self.record_index == 1)
    self.node_list.bg_HL_2:SetActive(self.record_index == 2)
    self:Flush()
end

function TreasureHuntMingwenRecord:OnFlush()
    self.record_index = self.record_index or 1
    local data = TreasureHuntWGData.Instance:GetMingWenRecordViewByType(self.record_index)
    if data then
        if self.record_index == 1 then
            self.record_list_view:SetDataList(data)   
        else
            self.record_person_list_view:SetDataList(data)
        end
    end
    self.node_list.all_no_record:SetActive(IsEmptyTable(data))
    
    self.node_list.ph_xunbao_show_list:SetActive(self.record_index == 1)
    self.node_list.ph_xunbao_person_list:SetActive(self.record_index ~= 1)
end


