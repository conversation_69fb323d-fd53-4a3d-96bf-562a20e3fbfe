require("game/fuben_team_common_tower/fuben_team_common_tower_wg_data")
--符文塔
require("game/fuben_team_common_tower/rune_tower/fuben_rune_tower_task_view")
require("game/fuben_team_common_tower/rune_tower/fuben_rune_tower_level_show_view")
require("game/fuben_team_common_tower/rune_tower/fuben_rune_tower_draw_view")
require("game/fuben_team_common_tower/rune_tower/fuben_rune_tower_draw_get_reward_view")

FuBenTeamCommonTowerWGCtrl = FuBenTeamCommonTowerWGCtrl or BaseClass(BaseWGCtrl)

function FuBenTeamCommonTowerWGCtrl:__init()
	if FuBenTeamCommonTowerWGCtrl.Instance then
		ErrorLog("[FuBenTeamCommonTowerWGCtrl] attempt to create singleton twice!")
		return
	end

	FuBenTeamCommonTowerWGCtrl.Instance = self
	self.data = FuBenTeamCommonTowerWGData.New()
    self:RegisterAllProtocols()

	--符文塔
	self.rune_tower_task_view = FuBenRuneTowerTaskView.New()
	self.rune_tower_level_show_view = FuBenRuneTowerLevelShowView.New()
	self.rune_tower_draw_view = FuBenRuneTowerDrawView.New()
	self.rune_tower_draw_reward_view = FuBenRuneTowerDrawGetRewardView.New()
end

function FuBenTeamCommonTowerWGCtrl:__delete()
	FuBenTeamCommonTowerWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.rune_tower_task_view then
		self.rune_tower_task_view:DeleteMe()
		self.rune_tower_task_view = nil
	end

	if self.rune_tower_level_show_view then
		self.rune_tower_level_show_view:DeleteMe()
		self.rune_tower_level_show_view = nil
	end

	if self.rune_tower_draw_view then
		self.rune_tower_draw_view:DeleteMe()
		self.rune_tower_draw_view = nil
	end

	if self.rune_tower_draw_reward_view then
		self.rune_tower_draw_reward_view:DeleteMe()
		self.rune_tower_draw_reward_view = nil
	end
end

function FuBenTeamCommonTowerWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCTeamCommonTowerFBSceneInfo, "OnSCTeamCommonTowerFBSceneInfo")
	self:RegisterProtocol(SCTeamCommonTowerFBHurtInfo, "OnSCTeamCommonTowerFBHurtInfo")
    self:RegisterProtocol(SCTeamCommonTowerFBDrawInfo, "OnSCTeamCommonTowerFBDrawInfo")
	self:RegisterProtocol(SCTeamCommonTowerFBSummaryInfo, "OnSCTeamCommonTowerFBSummaryInfo")
end

function FuBenTeamCommonTowerWGCtrl:OnSCTeamCommonTowerFBSceneInfo(protocol)
  	--print_error("======SceneInfo========",protocol)
	local old_scene_info = self.data:GetTeamCommonTowerFBInfo(protocol.fb_seq)
	self.data:SetTeamCommonTowerFBInfo(protocol)

	if protocol.is_end == 1 then
		FuBenWGCtrl.Instance:SetOutFbTime(0, true, true)
	else
		FuBenWGCtrl.Instance:SetOutFbTime(protocol.fb_end_time, true, true)
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.TEAM_COMMON_TOWER_FB_1 then
		--符文塔
		--第一次进还没加载完loding页
		if (IsEmptyTable(old_scene_info) or old_scene_info.level ~= protocol.level) and protocol.level ~= 1 then
			self:OpenRuneTowerLevelShowView(protocol.level)
		end

		if self.rune_tower_task_view:IsOpen() then
			self.rune_tower_task_view:Flush()
		end
	end
end

function FuBenTeamCommonTowerWGCtrl:OnSCTeamCommonTowerFBHurtInfo(protocol)
    --print_error("======HurtInfo========",protocol)
	self.data:SetTeamCommonTowerFBHurtInfo(protocol)

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.TEAM_COMMON_TOWER_FB_1 then
		if self.rune_tower_task_view:IsOpen() then
			self.rune_tower_task_view:Flush(0, "rank_info")
		end
	end
end

function FuBenTeamCommonTowerWGCtrl:OnSCTeamCommonTowerFBDrawInfo(protocol)
	local old_nor_draw_reward_list, old_spe_draw_reward_list = self.data:GetTeamCommonTowerFBDrawInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	--小于服务器时间直接清空数据
	if server_time > protocol.draw_end_time then
		self.data:SetTeamCommonTowerFBDrawInfo({})
		if self.rune_tower_draw_view:IsOpen() then
			self.rune_tower_draw_view:Close()
		end
	else
		self.data:SetTeamCommonTowerFBDrawInfo(protocol)
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.TEAM_COMMON_TOWER_FB_1 then
		if server_time <= protocol.draw_end_time then
			if self.rune_tower_draw_view:IsOpen() then
				local change_info = {}
				for k, v in pairs(protocol.nor_draw_reward_list) do
					if not old_nor_draw_reward_list[k] then
						local data = {}
						data.is_nor = true
						data.change_index = k
						table.insert(change_info, data)
					end
				end

				for k, v in pairs(protocol.spe_draw_reward_list) do
					if not old_spe_draw_reward_list[k] then
						local data = {}
						data.is_nor = false
						data.change_index = k
						table.insert(change_info, data)
					end
				end
				self.rune_tower_draw_view:Flush(0, "change_draw",{change_info = change_info})
			else
				self.rune_tower_draw_view:Open()
			end
		end
	end
end

function FuBenTeamCommonTowerWGCtrl:OnSCTeamCommonTowerFBSummaryInfo(protocol)
  	--print_error("======SummaryInfo========",protocol)
	if protocol.is_pass == 1 then
		local table_reward = {}
		for i= #protocol.reward_item_list, 1, -1 do
			local data = protocol.reward_item_list[i]
			local item_config = ItemWGData.Instance:GetItemConfig(data.item_id)
			if item_config then
				data.color = item_config.color
			else
				data.color = 1
			end
			table.insert(table_reward, data)
		end
		
		table.sort(table_reward, SortTools.KeyUpperSorters("color"))
		self.data:SetGetIsHelpState(protocol.is_help)
		self.data:SetGetMvpInfo(protocol.mvp_player_info)
		FuBenWGCtrl.Instance:OpenWin(Scene.Instance:GetSceneType(), table_reward, 0, 0, 0, 10)
	else
		FuBenWGCtrl.Instance:OpenLose(Scene.Instance:GetSceneType())
	end
end


--------------符文塔----------------------
function FuBenTeamCommonTowerWGCtrl:OpenRuneTowerTaskView()
    if self.rune_tower_task_view then
		self.rune_tower_task_view:Open()
	end
end

function FuBenTeamCommonTowerWGCtrl:CloseRuneTowerTaskView()
    if self.rune_tower_task_view and self.rune_tower_task_view:IsOpen() then
		self.rune_tower_task_view:Close()
	end
end

function FuBenTeamCommonTowerWGCtrl:GetRuneTowerTaskView()
    return self.rune_tower_task_view
end

function FuBenTeamCommonTowerWGCtrl:FlushRuneTowerTaskView()
	if self.rune_tower_task_view and self.rune_tower_task_view:IsOpen() then
		self.rune_tower_task_view:Flush()
	end
end

function FuBenTeamCommonTowerWGCtrl:OpenRuneTowerLevelShowView(level)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		self.rune_tower_level_show_view:SetDataAndOpen(level)
	end)
end

function FuBenTeamCommonTowerWGCtrl:FlushRuneTowerDrawStateAnim()
	if self.rune_tower_draw_view:IsOpen() then
		self.rune_tower_draw_view:Flush(0, "draw_state_anim")
	end
end

function FuBenTeamCommonTowerWGCtrl:ShowRuneTowerDrawGetReward(id_list)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		self.rune_tower_draw_reward_view:SetDataAndOpen(id_list)
	end)
end

--------------符文塔End----------------------