-- 购买战令等级界面
PositionalWarfareBuyZhanLingLevelView = PositionalWarfareBuyZhanLingLevelView or BaseClass(SafeBaseView)

function PositionalWarfareBuyZhanLingLevelView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_buy_zhanling_level_view")
end

function PositionalWarfareBuyZhanLingLevelView:LoadCallBack()
    if not self.zhanling_level_list then
        self.zhanling_level_list = AsyncListView.New(PWZhanLingLevelCellItemRender, self.node_list.zhanling_level_list)
    end

    if not self.normal_item then
        self.normal_item = ItemCell.New(self.node_list.normal_item)
    end

    self.high_item_list = {}
    for i = 1, 2 do
        self.high_item_list[i] = PWLHighItemRender.New(self.node_list["high_item_" .. i])
    end

    self.node_list.desc_buy.text.text = Language.PositionalWarfare.BuyZhanLingLevelMax
end

function PositionalWarfareBuyZhanLingLevelView:ReleaseCallBack()
    if self.zhanling_level_list then
        self.zhanling_level_list:DeleteMe()
        self.zhanling_level_list = nil
    end

    if self.normal_item then
        self.normal_item:DeleteMe()
        self.normal_item = nil
    end

    if self.high_item_list then
        for k, v in pairs(self.high_item_list) do
            v:DeleteMe()
        end
        self.high_item_list = nil
    end
end

function PositionalWarfareBuyZhanLingLevelView:OnFlush()
    local zhanling_exp = PositionalWarfareWGData.Instance:GetDevote()
    local max_level_cfg = PositionalWarfareWGData.Instance:GetZhanLingMaxLevelCfg()

    if IsEmptyTable(max_level_cfg) then
        return
    end

    local zhanling_level = PositionalWarfareWGData.Instance:GetZhanLingLevel()
    local cur_zhanling_cfg = PositionalWarfareWGData.Instance:GetZhanLingCfg(zhanling_level)
    local next_zhanling_cfg = PositionalWarfareWGData.Instance:GetZhanLingCfg(zhanling_level + 1)
    local devote = PositionalWarfareWGData.Instance:GetDevote()

    local is_max = IsEmptyTable(next_zhanling_cfg)
    local start_zhanling_cfg = PositionalWarfareWGData.Instance:GetZhanLingCfg(0)
    local min_zhanling_level = devote < start_zhanling_cfg.need_devote

    if min_zhanling_level then
        self.node_list.m_level_num.text.text = 0
    else
        self.node_list.m_level_num.text.text = zhanling_level + 1
    end

    if is_max then
        self.node_list.m_pregress_label.text.text = devote
        self.node_list.m_pregress_slider.slider.value = 1
        self.node_list.next_level_reward:SetActive(false)
    else
        local cur_exp_value = min_zhanling_level and devote or (devote - cur_zhanling_cfg.need_devote)
        cur_exp_value = cur_exp_value > 0 and cur_exp_value or 0
        local need_act_value = min_zhanling_level and cur_zhanling_cfg.need_devote or (next_zhanling_cfg.need_devote - cur_zhanling_cfg.need_devote)
        self.node_list.m_pregress_label.text.text = cur_exp_value .. "/" .. need_act_value
        self.node_list.m_pregress_slider.slider.value = cur_exp_value / need_act_value
        self.node_list.next_level_reward:SetActive(true)

        self.normal_item:SetData(next_zhanling_cfg.item[0])

        for k, v in ipairs(self.high_item_list) do
            v:SetData(next_zhanling_cfg.added_item[k - 1])
        end
    end

    self.node_list.cur_score.text.text = string.format(Language.PositionalWarfare.BuyZhanLingLevelCurExp, zhanling_exp)
    local target_exp = max_level_cfg.need_devote - zhanling_exp
    target_exp = target_exp > 0 and target_exp or 0
    self.node_list.need_score.text.text = string.format(Language.PositionalWarfare.BuyZhanLingLevelToMax, target_exp)

    local level_list = PositionalWarfareWGData.Instance:GetZhanLingBuyLevelCfg()
	self.zhanling_level_list:SetDataList(level_list)
end

---------------------------------------PWZhanLingLevelCellItemRender--------------------------------------------------
PWZhanLingLevelCellItemRender = PWZhanLingLevelCellItemRender or BaseClass(BaseRender)
function PWZhanLingLevelCellItemRender:__init()
    XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuyLevelBtn, self))
    XUI.AddClickEventListener(self.node_list["cell_node_btn"], BindTool.Bind(self.OnClickBuyLevelBtn, self))
end

function PWZhanLingLevelCellItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.add_level_str.text.text = string.format(Language.PositionalWarfare.ZhanLingBuyLevel, self.data.devote)
    local price = RoleWGData.GetPayMoneyStr(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    self.node_list.now_price_text.text.text = string.format(Language.PositionalWarfare.ZhanLingBuyLevelBtn, price)

    local cur_devote = PositionalWarfareWGData.Instance:GetDevote()
    local target_level = PositionalWarfareWGData.Instance:GetZhanLingLevelByDevote(cur_devote + self.data.devote)
    local cur_zhanling_level = PositionalWarfareWGData.Instance:GetZhanLingLevel()
    local start_zhanling_cfg = PositionalWarfareWGData.Instance:GetZhanLingCfg(0)
    local min_zhanling_level = cur_devote < start_zhanling_cfg.need_devote

    if min_zhanling_level then
        cur_zhanling_level = -1
    end
    local can_up_level = target_level - cur_zhanling_level
    local china_num = NumberToChinaNumber(can_up_level)
    self.node_list.buy_desc_1.text.text = can_up_level > 0 and string.format(Language.PositionalWarfare.BuyZhanLingLevelCanUpLevel, china_num) or ""

    local devote_return = self.data.devote_return
    self.node_list.buy_desc_2.text.text = devote_return > 0 and string.format(Language.PositionalWarfare.BuyZhanLingLevelBackLingYu, devote_return) or ""
end

function PWZhanLingLevelCellItemRender:OnClickBuyLevelBtn()
    if IsEmptyTable(self.data) then
        return
    end

    RechargeWGCtrl.Instance:Recharge(self.data.price, self.data.rmb_type, self.data.rmb_seq)
    --PositionalWarfareWGCtrl.Instance:CloseBugZhanLingLevelView()
end

---------------------------------PWLHighItemRender--------------------------------------
PWLHighItemRender = PWLHighItemRender or BaseClass(BaseRender)

function PWLHighItemRender:LoadCallBack()
    self.reward_item = ItemCell.New(self.node_list.item_node)
end

function PWLHighItemRender:__delete()
    if self.reward_item then
        self.reward_item:DeleteMe()
        self.reward_item = nil
    end
end

function PWLHighItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        self.view:SetActive(false)
    else
        self.view:SetActive(true)
        local is_open_higer_zhanling = PositionalWarfareWGData.Instance:GetHigerOrderRewardFlag()
        self.reward_item:SetData(self.data)
        self.node_list.lock_state:SetActive(not is_open_higer_zhanling)
    end
end