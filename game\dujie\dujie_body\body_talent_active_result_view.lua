BodyTalentActiveResultView = BodyTalentActiveResultView or BaseClass(SafeBaseView)

function BodyTalentActiveResultView:__init()
	self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/dujie_body_ui_prefab", "layout_talent_active_result_view")
end

function BodyTalentActiveResultView:LoadCallBack()

    -- 天赋注灵消耗 
    if self.refine_cost_cell == nil then
        self.refine_cost_cell = ItemCell.New(self.node_list.cost_item_node)
        self.refine_cost_cell:SetShowCualityBg(false)
        self.refine_cost_cell:NeedDefaultEff(false)
        self.refine_cost_cell:SetCellBgEnabled(false)
    end

	if not self.old_attr_group_item then
		self.old_attr_group_item = BodyTalentActiveResultItemRender.New(self.node_list.old_attr_group)
	end

    if not self.new_attr_group_item then
		self.new_attr_group_item = BodyTalentActiveResultItemRender.New(self.node_list.new_attr_group)
	end

    self.old_attr_group_item:SetDataType(false)
    self.new_attr_group_item:SetDataType(true)

    XUI.AddClickEventListener(self.node_list.btn_ok, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_reset, BindTool.Bind(self.OnClickReset, self))

end

function BodyTalentActiveResultView:ReleaseCallBack()

    if self.refine_cost_cell then
        self.refine_cost_cell:DeleteMe()
        self.refine_cost_cell = nil
    end

    if self.old_attr_group_item then
        self.old_attr_group_item:DeleteMe()
        self.old_attr_group_item = nil
    end

    if self.new_attr_group_item then
        self.new_attr_group_item:DeleteMe()
        self.new_attr_group_item = nil
    end
end

function BodyTalentActiveResultView:CloseCallBack()
    DujieWGCtrl.Instance:OpenBodyTalentShowNumView(self.data.talent_cfg.seq)
end

function BodyTalentActiveResultView:OnClickReset()
    if self.data and self.data.talent_cfg then
        if DujieWGData.Instance:IsActiveReach(self.data.talent_cfg.body_seq, self.data.talent_cfg.seq) then
            DujieWGCtrl.Instance:ActiveTalent(self.data.talent_cfg.body_seq, self.data.talent_cfg.seq)
        else
            self.refine_cost_cell:OnClick()
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.ActiveCostNotReach)
        end
    end
end

function BodyTalentActiveResultView:SetData(data)
    self.data = data
end

function BodyTalentActiveResultView:OnFlush()
    if  IsEmptyTable(self.data) then
        return
    end

    local bode_seq = self.data.talent_cfg.body_seq
    local talent_seq = self.data.talent_cfg.talent_seq
    local body_info = DujieWGData.Instance:GetBodyInfo(bode_seq)
    -- 品质 灵根名字+完美度+品质
    local body_cfg = DujieWGData.Instance:GetBodyCfgBySeq(bode_seq)
    local body_score_cfg, next_body_score_cfg = DujieWGData.Instance:GetBodyScoreCfg(bode_seq, body_info.score)

    self.node_list.text_body_quality.tmp.text = string.format(Language.Dujie.BodyQualityStr, body_cfg.body_name, body_score_cfg.level_txt)
    -- 进度
    if next_body_score_cfg then
        self.node_list.img_scorce_progress.image.fillAmount = body_info.score/next_body_score_cfg.need_score
    else
        self.node_list.img_scorce_progress.image.fillAmount = 1
    end
    -- 评分
    if next_body_score_cfg then
        self.node_list.text_body_score.tmp.text = body_info.score .. "/" .. next_body_score_cfg.need_score
    else
        self.node_list.text_body_score.tmp.text = body_info.score .. "/" .. body_score_cfg.need_score
    end

    local arrow_res_name = "a3_ty_up_1"
    if body_info.score < self.data.old_score then
        arrow_res_name = "a3_ty_down_1"
    end
    if body_info.score ~= self.data.old_score then
        local bundle, asset = ResPath.GetCommon(arrow_res_name)
        self.node_list.img_arrow_score.image:LoadSprite(bundle, asset)
        self.node_list.img_arrow_score:SetActive(true)
    else
        self.node_list.img_arrow_score:SetActive(false)
    end


    -- 元素
    for i = 1, 5 do
        if body_info.element_count_list[i-1] then
            self.node_list["text_element_count_"..i].tmp.text = body_info.element_count_list[i-1]
        else
            self.node_list["text_element_count_"..i].tmp.text = 0
        end
        
    end
    self.node_list["element_effect_up"]:SetActive(false)
    self.node_list["element_effect_down"]:SetActive(false)

    

    local up_index = -1
    local down_index = -1
    if self.data.old_info then
        if self.data.old_info.element == self.data.new_info.element then
            if self.data.old_info.element < self.data.new_info.element then
                up_index = self.data.new_info.element
            else
                down_index = self.data.new_info.element
            end
        else
            down_index = self.data.old_info.element
            up_index = self.data.new_info.element
        end
    else
        up_index = self.data.new_info.element
    end
    up_index = up_index + 1
    down_index = down_index + 1

    self.node_list["element_effect_up"]:SetActive(up_index ~= 0)
    self.node_list["element_effect_down"]:SetActive(down_index ~= 0)

    self.node_list.element_effect_up.rect.anchoredPosition = Vector2((65*up_index-35),-30)
    self.node_list.element_effect_down.rect.anchoredPosition = Vector2((65*down_index-35),-30)

    local old_data = {}
    old_data.talent_cfg = self.data.talent_cfg
    old_data.info = self.data.old_info

    local new_data = {}
    new_data.talent_cfg = self.data.talent_cfg
    new_data.info = self.data.new_info
    self.old_attr_group_item:SetData(old_data)
    self.new_attr_group_item:SetData(new_data)

    -- 判断是否需要显示旧数据
    if self.data.old_info then
        self.node_list.old_attr_group:SetActive(true)
        self.node_list.img_arrow:SetActive(true)

        if self.data.old_info.element ~= self.data.new_info.element or self.data.new_info.element_count > self.data.old_info.element_count then
            self.new_attr_group_item:SetArrowVisible(true)
        else
            self.new_attr_group_item:SetArrowVisible(false)
        end
    else
        self.node_list.old_attr_group:SetActive(false)
        self.node_list.img_arrow:SetActive(false)
    end

    local type, num = DujieWGData.Instance:GetRefineCost(self.data.talent_cfg.body_seq, self.data.talent_cfg.seq)
    local item_id = DujieWGData.Instance:GetOtherCfg("body_item_"..type)

    self.refine_cost_cell:SetData({item_id = item_id})
    self.node_list.text_cost.tmp.text = "X"..num

end


---------------------------------------------------------
BodyTalentActiveResultItemRender = BodyTalentActiveResultItemRender or BaseClass(BaseRender)
function BodyTalentActiveResultItemRender:__init()
	if not self.attr_list then
		self.attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.attr_list)
	end
end

function BodyTalentActiveResultItemRender:__delete()
    if self.attr_list then
		self.attr_list:DeleteMe()
		self.attr_list = nil
	end
end

function BodyTalentActiveResultItemRender:LoadCallBack()

end

function BodyTalentActiveResultItemRender:ReleaseCallBack()

end

function BodyTalentActiveResultItemRender:OnFlush()
    if not self.data then return end

    if self.is_new then
        self.node_list.text_title.tmp.text = Language.Dujie.BodyResultDescNew
    else
        self.node_list.text_title.tmp.text = Language.Dujie.BodyResultDescOld
        self.node_list.img_arrow:SetActive(false)
    end

    local info = self.data.info
    local talent_cfg = self.data.talent_cfg
    local title_str = DujieWGData.Instance:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.TalentType, talent_cfg.type)



    if info and info.is_active > 0 then
        -- 元素和数量
        local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_0_"..info.element + 1)
        self.node_list.img_cur_element.image:LoadSprite(bundle, asset)
        self.node_list.text_cur_element_count.tmp.text = "X"..info.element_count

        local score = DujieWGData.Instance:CountTalentScore(talent_cfg.body_seq, info.element, info.element_count)

        self.node_list.text_score.tmp.text = score
        local element_str = ""
        if talent_cfg.type == DUJIE_BODY_TALENT_TYPE.ATTR then
            element_str = DujieWGData.Instance:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.Element, info.element)

            local attr_cfg = DujieWGData.Instance:GetTalentAttrCfg(talent_cfg.type,info.element,info.element_count)
            local attr_list, cap = DujieWGData.Instance:GetTalentAttrDataAndCap(attr_cfg)
        
            self.attr_list:SetDataList(attr_list)

            self.node_list.attr_list:SetActive(true)
            self.node_list.text_desc.tmp.text = ""
        elseif talent_cfg.type == DUJIE_BODY_TALENT_TYPE.EFFECT then
            self.node_list.attr_list:SetActive(false)

            local skill_desc,skill_name = DujieWGData.Instance:GetTalentSkillDesc(info.skill_seq, talent_cfg.body_seq)
            self.node_list.text_desc.tmp.text = skill_desc
        elseif talent_cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
            self.node_list.attr_list:SetActive(false)

            self.node_list.text_desc.tmp.text = ""
        end

        local str = string.format(Language.Dujie.TalentNameStr, title_str,element_str, info.color)
        self.node_list.text_name.tmp.text = str


    end

end

function BodyTalentActiveResultItemRender:SetDataType(is_new)
    self.is_new = is_new
end

function BodyTalentActiveResultItemRender:SetArrowVisible(visible)
    self.node_list.img_arrow:SetActive(visible)
end




