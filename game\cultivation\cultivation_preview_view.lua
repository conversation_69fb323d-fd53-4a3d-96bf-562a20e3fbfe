CultivationPreviewView = CultivationPreviewView or BaseClass(SafeBaseView)


function CultivationPreviewView:__init()
	self:SetMaskBg()
    self.view_style = ViewStyle.Full

    local view_bundle = "uis/view/cultivation_ui_prefab"
    local common_path = "uis/view/common_panel_prefab"

    self:AddViewResource(0, common_path, "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_cultivation_preview_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

    self.tab_sub = {}
    self.remind_tab = {}

    
end

function CultivationPreviewView:LoadCallBack()
    self.wg_data = CultivationWGData.Instance
    
    if not self.jingjie_list then
		self.jingjie_list = AsyncListView.New(CultivationPreviewItemRender, self.node_list.jingjie_list)
        self.node_list.jingjie_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.UpdateAnimation, self))
	end

    --list的箭头显示.
	self.node_list.jingjie_list.scroller.scrollerEndScrolled = BindTool.Bind(self.ScrollerEndScrolled, self)
    
end

function CultivationPreviewView:ReleaseCallBack()
    if self.jingjie_list then
		self.jingjie_list:DeleteMe()
		self.jingjie_list = nil
	end
end

function CultivationPreviewView:OnFlush()
    local preview_cfg = self.wg_data:GetCultivationPreviewCfg()
    -- 无限列表
    self.jingjie_list:SetDataList(preview_cfg)

    local is_remind, index = self.wg_data:GetPreviewAwradRemin()
    self.jingjie_list:JumpToIndex(index)

    -- 境界图标
	local cur_stage_cfg = self.wg_data:GetCurXiuWeiStageCfg()
	local jinjir_asset, jinjie_asset = ResPath.GetCultivationImg(cur_stage_cfg.stage_title_icon)
	self.node_list.jinjie_icon.image:LoadSprite(jinjir_asset, jinjie_asset, function ()
		self.node_list.jinjie_icon.image:SetNativeSize()
	end)
end

-- 根据Scroll的进度更新
function CultivationPreviewView:UpdateAnimation(pos)
    if pos then
        -- pos.x 范围[0,1]
        self.node_list.timeline.animator:Play("cultivation_preview",-1,pos.x)
        self.node_list.timeline.animator.speed = 0;
    end
end

--list的箭头显示.
function CultivationPreviewView:ScrollerEndScrolled()
	local val = self.node_list.jingjie_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.left_arrow:CustomSetActive(val ~= 0 and val > 0.05)
	self.node_list.right_arrow:CustomSetActive(val ~= 0 and val < 0.95)
end

-------------------------------------------------------------------------------------------------

CultivationPreviewItemRender = CultivationPreviewItemRender or BaseClass(BaseRender)

function CultivationPreviewItemRender:LoadCallBack()
    if not self.reward_item_list then
		self.reward_item_list = AsyncListView.New(CultivationPreviewRewardRender, self.node_list.reward_list)
	end
    XUI.AddClickEventListener(self.node_list.btn_reward,BindTool.Bind(self.OnClickBtnReward, self))
end

function CultivationPreviewItemRender:__delete()

    if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function CultivationPreviewItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end 

	self.node_list.text_cultivation.text.text = data.stage_name..Language.Cultivation.StageTitle
    local reward_item=SortTableKey(data.reward_item)

    if data.stage_desc then
        local stage_desc_list = string.split(data.stage_desc, ",")
        for index, value in ipairs(stage_desc_list) do
            self.node_list["desc_"..index].text.text=value
        end
    end
    
    local is_received = CultivationWGData.Instance:GetIsReceivedPreviewReward(data.stage_no)
    local is_can_receive = not is_received and CultivationWGData.Instance:GetXiuWeiState() >= data.need_stage 
    -- 是否已领取
    -- 奖励是否可领取
    -- 不可领取
    if is_received then
        self.node_list.btn_reward:SetActive(false)
    elseif is_can_receive then
        self.node_list.btn_reward:SetActive(true)
    else
        self.node_list.btn_reward:SetActive(false)
    end
    
    local reawrd_data_list = {}
    for i,v in ipairs(reward_item) do
        local item_data = {}
        item_data.is_received = is_received
        item_data.is_can_receive = is_can_receive
        item_data.stage_no = data.stage_no
        item_data.item = v
        table.insert(reawrd_data_list, item_data)
    end
    self.reward_item_list:SetDataList(reawrd_data_list)

end

-- 领取奖励
function CultivationPreviewItemRender:OnClickBtnReward()
    local data=self:GetData()
    CultivationWGCtrl.Instance:ReceiveStageReward(data.stage_no)
end

-- ------------------------------------------------------------------
CultivationPreviewRewardRender = CultivationPreviewRewardRender or BaseClass(BaseRender)
function CultivationPreviewRewardRender:__init()
    self.item_cell = ItemCell.New(self.node_list.item)
end

function CultivationPreviewRewardRender:__delete()
    -- 销毁创建的物品格子
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function CultivationPreviewRewardRender:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) then
        self.view:SetActive(false)
        return
    else
        self.view:SetActive(true)
        self.node_list.img_receive:SetActive(data.is_received)
        self.item_cell:SetData(data.item)
        -- self.item_cell:SetIconGrey(data.is_received)
        -- self.item_cell:SetGraphicGreyCualityBg(data.is_received)
        -- self.item_cell:NeedDefaultEff(data.is_can_receive)
        self.node_list.remind:SetActive(data.is_can_receive)
    end
end
