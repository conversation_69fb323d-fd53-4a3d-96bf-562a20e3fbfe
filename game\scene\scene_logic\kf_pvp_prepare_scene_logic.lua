
KfPVPPrepareSceneLogic = KfPVPPrepareSceneLogic or BaseClass(CrossServerSceneLogic)

function KfPVPPrepareSceneLogic:__init()
end

function KfPVPPrepareSceneLogic:__delete()
end

-- 进入场景
function KfPVPPrepareSceneLogic:Enter(old_scene_type, new_scene_type)--20 110
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)

	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance.view:SetAttackMode(ATTACK_MODE.PEACE)
        MainuiWGCtrl.Instance:SetOtherContents(true)
        MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:FlushKfPKRenPoint()
		MainuiWGCtrl.Instance.view:FlushWorkTalkBtnActive(new_scene_type ~= SceneType.Kf_PvP_Prepare)
		KF3V3WGCtrl.Instance:OpenPrepareLogicView()
	end)

    self.cross_3v3_side_change = GlobalEventSystem:Bind(OtherEventType.MAIN_ROLE_3V3_SIDE_CHANGE, BindTool.Bind1(self.Cross3V3SideChange, self)) 	-- 主角阵营更变
	self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))
	if not self.kf_3v3_info_change then
		self.kf_3v3_info_change = GlobalEventSystem:Bind(OtherEventType.KF3V3ZhanDuiRoleInfoChnage, self.FlushKfPKRenPoint, self)
	end
	local role_list = Scene.Instance:GetRoleList()
	for i, v in pairs(role_list) do
		if not v:IsMainRole() then
			local follow_ui = v:GetFollowUi()
			follow_ui:SetZhanDuiName(v:GetVo().zhandui3v3_name)
			follow_ui:SetZhanDuiZhanLingId(v:GetVo().zhandui3v3_lingpai_id)
			follow_ui:SetZhanDuiZhanLingText(v:GetVo().zhandui3v3_lingpai_name)
			follow_ui:SetGuildName()
			local hp_bar = follow_ui:GetHpBar()
			if hp_bar then
				hp_bar:AddShieldRule(ShieldRuleWeight.High, function()
					return true
				end)
			end
			v:SetIsPerformer(true)
		end
	end
	local main_role = Scene.Instance:GetMainRole()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local follow_ui = main_role:GetFollowUi()
	follow_ui:SetZhanDuiName(main_role_vo.zhandui3v3_name)
	follow_ui:SetZhanDuiZhanLingId(main_role_vo.zhandui3v3_lingpai_id)
	follow_ui:SetZhanDuiZhanLingText(main_role_vo.zhandui3v3_lingpai_name)
	follow_ui:SetGuildName()
	local hp_bar = follow_ui:GetHpBar()
	if hp_bar then
		hp_bar:AddShieldRule(ShieldRuleWeight.High, function()
			return true
		end)
	end
	--移动到队长判断
	local info = KF3V3WGData.Instance:GetLeaderPosInfo()
	if info ~= nil then
		--print_error("--------移动到队长身边--------", info.scene_id, info.pos_x, info.pos_y)
		if not AStarFindWay:IsBlock(info.pos_x, info.pos_y) then
			GuajiWGCtrl.Instance:MoveToPos(info.scene_id, info.pos_x, info.pos_y, 3)
		end
		KF3V3WGData.Instance:ClearLeaderPosInfo()
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN 
		and activity_info.next_time and activity_info.next_time > server_time then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(activity_info.next_time)
	end

	self:CreateStatueList()
end

function KfPVPPrepareSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self,old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		ViewManager.Instance:Close(GuideModuleName.KfOneVOneMatch)
		ViewManager.Instance:CloseAll()
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
            MainuiWGCtrl.Instance:SetOtherContents(false)
            MainuiWGCtrl.Instance:SetTaskContents(true)
			MainuiWGCtrl.Instance:ResetTaskPanel()
			-- MainuiWGCtrl.Instance.view:SetTaskCallBack(nil)
			MainuiWGCtrl.Instance.view:FlushWorkTalkBtnActive(new_scene_type ~= SceneType.Kf_PvP_Prepare)
		end)
		KF3V3WGCtrl.Instance:ClosePrepareLogicView()

		KF3V3WGCtrl.Instance:CheckOpenActEndView(new_scene_type, {act_type = ACTIVITY_TYPE.KF_PVP})
		--释放匹配计时器
		KF3V3WGCtrl.Instance:DeleteMatchTimer()
	end
	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end
	if self.kf_3v3_info_change then
		GlobalEventSystem:UnBind(self.kf_3v3_info_change)
		self.kf_3v3_info_change = nil
	end
    if self.cross_3v3_side_change then
        GlobalEventSystem:UnBind(self.cross_3v3_side_change)
        self.cross_3v3_side_change = nil
    end
	if MainuiWGCtrl.Instance.view then
		local before_act_mode = GameVoManager.Instance:GetMainRoleVo().attack_mode or 0
		MainuiWGCtrl.Instance.view:SetAttackMode(before_act_mode)
	end
end

function KfPVPPrepareSceneLogic:IsEnemy()
	return false
end

function KfPVPPrepareSceneLogic:OnRoleEnter(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	local follow_ui = role:GetFollowUi()
	follow_ui:SetZhanDuiName(role:GetVo().zhandui3v3_name)
	follow_ui:SetZhanDuiZhanLingId(role:GetVo().zhandui3v3_lingpai_id)
	follow_ui:SetZhanDuiZhanLingText(role:GetVo().zhandui3v3_lingpai_name)
	follow_ui:SetGuildName()
	local hp_bar = follow_ui:GetHpBar()
	if hp_bar then
		hp_bar:AddShieldRule(ShieldRuleWeight.High, function()
			return true
		end)
	end
	role:SetIsPerformer(true)
end

function KfPVPPrepareSceneLogic:CreateStatueList()
	ZhanDuiWGCtrl.Instance:SendZhanDuiRankReq(EnumCross3V3RankType.LastSeason)
	--ZhanDuiWGCtrl.Instance:SendZhanDuiRankReq(EnumCross3V3RankType.CurSeason)
end

-- 角色是否需要进行优先级的计算
function KfPVPPrepareSceneLogic:IsRoleNeedCalculatePriortiy()
	return true
end

-- 角色显示的优先级
function KfPVPPrepareSceneLogic:GetRoleVisiblePriortiy(role)
	local zhandui_info = ZhanDuiWGData.Instance:GetZhanDuiInfo()
	if role:GetVo().zhandui3v3_id == zhandui_info.zhandui_id then
		return SceneAppearPriority.High
	end

	return SceneAppearPriority.Middle
end

function KfPVPPrepareSceneLogic:FlushKfPKRenPoint()
	MainuiWGCtrl.Instance:FlushKfPKRenPoint()
end

-- 主角阵营更变
function KfPVPPrepareSceneLogic:Cross3V3SideChange()
	local role_list = Scene.Instance:GetRoleList()
	for i, v in ipairs(role_list) do
		v:ReloadUINameColor()
	end
end