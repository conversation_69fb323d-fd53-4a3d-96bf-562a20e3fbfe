FishpondFriendView = FishpondFriendView or BaseClass(SafeBaseView)

function FishpondFriendView:__init()
	self:AddViewResource(0, "uis/view/fishpond_ui_prefab", "layout_friendlist")
	self.is_any_click_close = true
	self.is_move = false
	self.data = {}
	self.data_type = -1  	-- -1没有数据，2 好友， 3 盟友
end

function FishpondFriendView:__delete()
	
end

function FishpondFriendView:ReleaseCallBack()
	self.lbl_not_friend = nil

	if nil ~= self.friend_list_view then
		self.friend_list_view:DeleteMe()
		self.friend_list_view = nil
	end
end

function FishpondFriendView:LoadCallBack()
    self.scroll_friend = self.node_list["scroll_friend_2"]
	self:CreateFriendContent()
	self:CreateFriendList()

	SocietyWGCtrl.Instance:SendFishPoolQueryReq(SocietyWGData.GetInfoType.StealRecord, SocietyWGData.Instance:GetShowPond())
	XUI.AddClickEventListener(self.node_list["layout_friend"], BindTool.Bind2(self.OnbtnFriend, self, SocietyWGData.GetInfoType.ServerAllInfo))
	XUI.AddClickEventListener(self.node_list["layout_guildfriend"], BindTool.Bind2(self.OnbtnFriend, self, SocietyWGData.GetInfoType.StealRecord))
	XUI.AddClickEventListener(self.node_list.bg_close_btn, BindTool.Bind1(self.Close, self))
	
	self:OnbtnFriend(SocietyWGData.GetInfoType.ServerAllInfo)
end 

function FishpondFriendView:ShowIndexCallBack()
end 

function FishpondFriendView:OnbtnFriend(value)


	self.data_type = SocietyWGData.GetInfoType.ServerAllInfo
	if value == SocietyWGData.GetInfoType.ServerAllInfo then
		SocietyWGCtrl.Instance:SendFishPoolQueryReq(SocietyWGData.GetInfoType.ServerAllInfo, 0)
		self.data_type = SocietyWGData.GetInfoType.ServerAllInfo
		self.node_list["Image_hightlight1"]:SetActive(true)
		self.node_list["img_hight_2"]:SetActive(false)
	else
		SocietyWGCtrl.Instance:SendFishPoolQueryReq(SocietyWGData.GetInfoType.StealRecord, SocietyWGData.Instance:GetShowPond())
		self.node_list["Image_hightlight1"]:SetActive(false)
		self.node_list["img_hight_2"]:SetActive(true)
		self.data_type = SocietyWGData.GetInfoType.StealRecord
	end
	self:Flush()
end

-- 更新数据数据
function FishpondFriendView:ChangeData(value)
	if nil == value or self.data_type ~= value then return end
	if SocietyWGData.GetInfoType.ServerAllInfo == value then
		local sorted_list = __TableCopy(SocietyWGData.Instance:GetAllPeopleInfoList())
		self.data = sorted_list or {}
	elseif SocietyWGData.GetInfoType.StealRecord == value then
		local sorted_list = __TableCopy(SocietyWGData.Instance:GetStolenInfoList())
		self.data = sorted_list or {}
	end
	
end

-- 设置数据类型
function FishpondFriendView:SetDataType(value)
	self.data_type = value
end


function FishpondFriendView:OnFlush(param_t)
	
	if self.friend_list_view then
		self:ChangeData(self.data_type)
		self.friend_list_view:SetDataList(self.data,3)
	end
end 

-- 创建显示面板
function FishpondFriendView:CreateFriendContent()
end 

-- 创建list
function FishpondFriendView:CreateFriendList()
	local ph_friend_list = self.node_list["ph_friend_list"]
	if not self.friend_list_view then 
	self.friend_list_view = AsyncListView.New(FishFriendItem, ph_friend_list)
	-- 
	self.friend_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectFishFriendListItemHandler, self))
	
	end
	Child(self.friend_list_view:GetView(), 999, 999)
end

-- 点击好友
function FishpondFriendView:OnSelectFishFriendListItemHandler(cell, index)
--print_error(index,"111")
	if cell and cell:GetData() and not SocietyWGData.Instance:IsShowPond(cell:GetData().friend_uid) then
		local color = "ffffff"
		if cell:GetData().camp ~= nil then
			color = CAMP_COLOR3B[cell:GetData().camp]
		end
		SocietyWGCtrl.Instance:ChangeShowPondUid(cell:GetData().friend_uid)
		local content = string.format(Language.Fishpond.ChangeFishpondTips, color, cell:GetData().friend_name)
		SysMsgWGCtrl.Instance:ErrorRemind(content)
	end
end


-------------itemRender--------------
FishFriendItem = FishFriendItem or BaseClass(BaseRender)

function FishFriendItem:__init()
end

function FishFriendItem:CreateChild()
	BaseRender.CreateChild(self)

	self.lbl_friend_index = self.node_tree.lbl_friend_index.node
	self.lbl_friend_pondlv = self.node_tree.lbl_friend_pondlv.node
	self.lbl_friend_name = self.node_tree.lbl_friend_name.node
	self.img_avatar = self.node_tree.img_avatar.node
	self.img_avatar:setScale(1.2)
end

function FishFriendItem:__delete()	
	AvatarManager.Instance:CancelUpdateAvatar(self.img_avatar)
end

function FishFriendItem:OnFlush()
 --    print_error(self.index,self:IsSelectIndex())
	-- self.node_list["ph_friend_item"].toggle.isOn = self:IsSelectIndex()
--print_error(self.index,self.data.friend_name)
	self.node_list["lbl_friend_index"].text.text = self.index
	self.node_list["lbl_friend_pondlv"].text.text = Language.Fishpond.FishpondNameAndLv .. self.data.pool_level
	self.node_list["lbl_friend_name"].text.text = self.data.friend_name

	local bundle,asset = ResPath.GetRoleHeadIconSociety(self.data.prof) 
	self.node_list["icon"].image:LoadSprite(bundle,asset)
end
function FishFriendItem:OnSelectChange(is_select)
	if true == is_select then
		-- bundle, asset = ResPath.GetCommon("img9_123")	--高亮
		self.node_list.HightLight:SetActive(true)
	else
		-- bundle, asset = ResPath.GetCommon("img9_122")	--非高亮
		self.node_list.HightLight:SetActive(false)
	end
end