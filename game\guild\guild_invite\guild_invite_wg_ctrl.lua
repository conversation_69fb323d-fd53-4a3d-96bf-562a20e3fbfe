require("game/guild/guild_invite/guild_war_invite_view")
require("game/guild/guild_invite/guild_war_invite_result_view")
require("game/guild/guild_invite/guild_invite_scene_view")
require("game/guild/guild_invite/guild_invite_wg_data")


GuildInviteWGCtrl = GuildInviteWGCtrl or BaseClass(BaseWGCtrl)

function GuildInviteWGCtrl:__init()
	if GuildInviteWGCtrl.Instance then
		ErrorLog("[GuildInviteWGCtrl]:Attempt to create singleton twice!")
	end
	GuildInviteWGCtrl.Instance = self

	self.guild_war_invite_view = GuildWarInviteView.New(GuideModuleName.GuildWarInviteView)
	self.guild_war_invite_result_view = GuildWarInviteResultView.New(GuideModuleName.GuildWarInviteResultView)
	self.guild_invite_scene_view = GuildInviteSceneView.New(GuideModuleName.GuildInviteSceneView)
	self.guild_invite_data = GuildInviteWGData.New()

	self:RegisterAllProtocols()

	self.mainui_open_complete_handle = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind(self.InitGuildBattleCallBack, self))

	self.attr_change = BindTool.Bind(self.RoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.attr_change, {"level"})

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

end

function GuildInviteWGCtrl:__delete()
	if self.guild_war_invite_view then
		self.guild_war_invite_view:DeleteMe()
		self.guild_war_invite_view = nil
	end

	if self.guild_war_invite_result_view then
		self.guild_war_invite_result_view:DeleteMe()
		self.guild_war_invite_result_view = nil
	end

	if self.guild_invite_scene_view then
		self.guild_invite_scene_view:DeleteMe()
		self.guild_invite_scene_view = nil
	end

	if self.guild_invite_data then
		self.guild_invite_data:DeleteMe()
		self.guild_invite_data = nil
	end

	GuildInviteWGCtrl.Instance = nil

	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
end

function GuildInviteWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSGuildDingJiEnterScene)
	self:RegisterProtocol(SCGuildDingJiHurtRankInfo,"OnSCGuildDingJiHurtRankInfo")
	self:RegisterProtocol(SCGuildDingJiBossDieInfo,"OnSCGuildDingJiBossDieInfo")
end

function GuildInviteWGCtrl:SendCSGuildDingJiEnterScene()
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSGuildDingJiEnterScene)
	send_protocol:EncodeAndSend()
end

function GuildInviteWGCtrl:OnSCGuildDingJiHurtRankInfo(protocol)
	self.guild_invite_data:SetSCGuildDingJiHurtRankInfo(protocol)
	if self.guild_invite_scene_view:IsOpen() then
		self.guild_invite_scene_view:Flush()
	end
end

function GuildInviteWGCtrl:OnSCGuildDingJiBossDieInfo(protocol)
	local is_boss_die = protocol.is_boss_die
	if is_boss_die == 1 then
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.Guild_Invite then
			ViewManager.Instance:Open(GuideModuleName.GuildWarInviteResultView)
		end
	end
end

function GuildInviteWGCtrl:FlushInviteResultView()
	if self.guild_war_invite_result_view:IsOpen() then
		self.guild_war_invite_result_view:Flush()
	end
end

function GuildInviteWGCtrl:PlayAnim()
	if self.guild_war_invite_view:IsOpen() then
		self.guild_war_invite_view:PlayAnim()
	end
end

function GuildInviteWGCtrl:RoleAttrChange(attr_name,value)
	if attr_name == "level" then
		self:InitGuildBattleCallBack()
	end
end

function GuildInviteWGCtrl:InitGuildBattleCallBack()
	--仙盟定级赛模型服务端下发数据  为了显示开服前3天的预告
	--屏蔽 避免以后加回来
	-- local time = self:CheckIsCanOpenActivity()
	-- if time <= 0 then return end
	-- local server_time = TimeWGCtrl.Instance:GetServerTime()
	-- local protocol = {is_broadcast = 1, activity_type = ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE,  next_status_switch_time = server_time + time, param_1 = 0, open_type = 0, param_2 = 0, msg_type = 9303, status = 1}
	-- ActivityWGData.Instance:SetActivityStatus(protocol.activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
	-- MainuiWGCtrl.Instance.view:ActivityChangeCallBack(ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE, 1, server_time + time, 0)
	
end

function GuildInviteWGCtrl:CheckIsCanOpenActivity()
	local guild_battle_limit_level,limit_day = GuildInviteWGData.Instance:GetActivityLimitLevel()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local role_level =  RoleWGData.Instance:GetRoleLevel()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local time_table = os.date("*t", server_time)
	local start_hour,start_min = GuildInviteWGData.Instance:GetGuildInviteOpenTime()
	local has_pass_time = time_table.hour * 3600 + time_table.min * 60 + time_table.sec
	local end_time1 = 0

	if role_level >= guild_battle_limit_level and open_day == limit_day then
		end_time1 = start_hour * 3600 - has_pass_time + start_min * 60
	end
	
	return end_time1 > 0 and end_time1 or 0 
end

function GuildInviteWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE and status == ACTIVITY_STATUS.OPEN then
		if self.guild_war_invite_view:IsOpen() then
			self.guild_war_invite_view:Flush()
		end
	end
end