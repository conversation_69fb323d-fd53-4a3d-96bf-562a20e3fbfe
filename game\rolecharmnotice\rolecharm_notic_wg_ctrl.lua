require("game/rolecharmnotice/rolecharm_notice_view")
require("game/rolecharmnotice/rolecharm_reward_preview_view")
require("game/rolecharmnotice/rolecharm_notice_wg_data")

RoleCharmNoticWGCtrl = RoleCharmNoticWGCtrl or BaseClass(BaseWGCtrl)

function RoleCharmNoticWGCtrl:__init()
	if RoleCharmNoticWGCtrl.Instance then
        error("[CangMingChaseWGCtrl]:Attempt to create singleton twice!")
	end

	RoleCharmNoticWGCtrl.Instance = self
	self.data = RoleCharmNoticeWGData.New()
	self.rolecharm_notice_view = RoleCharmNoticeView.New(GuideModuleName.RoleCharmNoticeView)
	self.rolecharm_notice_reward_view = RoleCharmRewardPreviewView.New(GuideModuleName.RoleCharmRewardPreviewView)

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
end

function RoleCharmNoticWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.rolecharm_notice_view:DeleteMe()
	self.rolecharm_notice_view = nil
	self.rolecharm_notice_reward_view = nil

	if self.mainui_bipin_rank_info_timer then
		GlobalTimerQuest:CancelQuest(self.mainui_bipin_rank_info_timer)
		self.mainui_bipin_rank_info_timer = nil
	end

	RoleCharmNoticWGCtrl.Instance = nil
end

function RoleCharmNoticWGCtrl:FlushView()
	local activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK
	if not ActivityWGData.Instance:GetActivityIsOpen(activity_type) then
		return
	end

	if self.rolecharm_notice_view:IsOpen() then
		self.rolecharm_notice_view:Flush()
	end
end

function RoleCharmNoticWGCtrl:OperateRankInfoByView()
	local activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK
	local is_open_act = ActivityWGData.Instance:GetActivityIsOpen(activity_type)
	if not is_open_act then
		return
	end

	-- if self.rolecharm_notice_view:IsOpen() then
	-- 	self.rolecharm_notice_view:SendRankInfo()
	-- end

	self:SendCharmRankReq()
end

function RoleCharmNoticWGCtrl:SendCharmRankReq()
	local role_sex = RoleWGData.Instance:GetRoleSex()
	if role_sex == GameEnum.MALE then
		--RankWGCtrl.Instance:SendGetPersonRankListReq(PROFESS_RANK_TYPE.SEND_FLOWER_MALE)
		self:SendActivityCharmRankReq(SEND_FLOWER_OPER_TYPE.SEND_FLOWER_OPER_TYPE_RANK_INFO, PROFESS_RANK_TYPE.SEND_FLOWER_MALE)
	else
		--RankWGCtrl.Instance:SendGetPersonRankListReq(PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE)
		self:SendActivityCharmRankReq(SEND_FLOWER_OPER_TYPE.SEND_FLOWER_OPER_TYPE_RANK_INFO, PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE)
	end
end

-- 发送协议（新）
function RoleCharmNoticWGCtrl:SendActivityCharmRankReq(opera_type, param1, param2, param3)
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK, opera_type, param1, param2, param3)
end

function RoleCharmNoticWGCtrl:GetCacularPos(node)
	if nil == node then return nil end
	local rolecharm_notice_view = self.rolecharm_notice_view
	if nil == rolecharm_notice_view or not rolecharm_notice_view:IsOpen() then
		return
	end
	local parent_rect= rolecharm_notice_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local x, y = parent_rect.sizeDelta.x / 2, parent_rect.sizeDelta.y / 2
	local screen_pos_tbl
	if node.gameObject then
		screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	else
		screen_pos_tbl = node
	end
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))
	if local_position_tbl then
		x = local_position_tbl.x + 100 + (node.rect and node.rect.sizeDelta.x or 0)
		x = math.max(x, -parent_rect.sizeDelta.x / 2)
		x = math.min(x, parent_rect.sizeDelta.x / 2 - 133)
		y = local_position_tbl.y + 80
		y = math.max(y, -parent_rect.sizeDelta.y / 2 +280)
		y = math.min(y, parent_rect.sizeDelta.y / 2 + 50)
		return Vector2(x, y)
	end
end

function RoleCharmNoticWGCtrl:CancelRequesFlushCharmRank()
	if self.mainui_charm_rank_info_timer then
		GlobalTimerQuest:CancelQuest(self.mainui_charm_rank_info_timer)
		self.mainui_charm_rank_info_timer = nil
	end
end

-- 魅力榜实时刷新显示排行数据
function RoleCharmNoticWGCtrl:RuningRequesFlushCharmRankInfo()
	local activity_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK)
	if not activity_is_open then
		self:CancelRequesFlushCharmRank()
		return
	end
	local is_show = self.data:GetMainUiIsShowCharmRank()
	if is_show then
		if not self.mainui_charm_rank_info_timer then
			self.mainui_charm_rank_info_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.SendCharmRankReq, self), 60)
		end
	else
		self:CancelRequesFlushCharmRank()
	end
	self:FlushMainUiCharmRankInfo()
end

function RoleCharmNoticWGCtrl:FlushMainUiCharmRankInfo()
	local is_show = self.data:GetMainUiIsShowCharmRank()
	local charm_rank_act_btn = MainuiWGCtrl.Instance:GetActivityButtonByActivityType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CHARMRANK)
	if charm_rank_act_btn then
		local data_info = {}
		if is_show then
			local my_rank = self.data:GetSelfRankNum()
			local rank_str = my_rank > 0 and ToColorStr(string.format(Language.RoleCharmRank.rank_text, my_rank), "#3c8552") or ToColorStr(Language.RoleCharmRank.nocharm, "#a93c3c")
			local rank_label = string.format(Language.RoleCharmRank.MyRank, rank_str)
			data_info[1] = { is_show = is_show, rank_label = rank_label }

			if my_rank ~= 1 then
				local show_str = my_rank == 0 and Language.RoleCharmRank.RankTips1 or Language.RoleCharmRank.RankTips2
				local need_charm = self.data:GetUpRankCharmCondition()
				local rank_tip = string.format(show_str, need_charm)
				data_info[2] = { is_show = is_show, rank_label = rank_tip }
			end
		else
			data_info[1] = {is_show = is_show}
		end
		charm_rank_act_btn:Flush("SetRankInfo", data_info)
	end
end

function RoleCharmNoticWGCtrl:MainuiOpenCreateCallBack()
	self:SendCharmRankReq()
end

-- 天数改变
function RoleCharmNoticWGCtrl:OnDayChange()
	self.data:OnDayChange()
end