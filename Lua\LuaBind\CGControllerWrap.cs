﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CGControllerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(CGController), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("SetOperaBtnCallback", SetOperaBtnCallback);
		<PERSON><PERSON>RegFunction("AddActor", AddActor);
		<PERSON><PERSON>Function("AddActorObj", AddActorObj);
		L.RegFunction("SetTrackMute", SetTrackMute);
		<PERSON>.RegFunction("SetPlayEndCallback", SetPlayEndCallback);
		<PERSON><PERSON>Function("SetChangeCallback", SetChangeCallback);
		<PERSON><PERSON>Function("Play", Play);
		L.RegFunction("RebuildPlay", RebuildPlay);
		<PERSON>.RegFunction("Stop", Stop);
		<PERSON><PERSON>RegFunction("Skip", Skip);
		<PERSON><PERSON>RegFunction("SkipToEnd", SkipToEnd);
		<PERSON><PERSON>RegFunction("GetPlayableDirector", GetPlayableDirector);
		<PERSON><PERSON>RegFunction("SetPlayableDirector", SetPlayableDirector);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("needFadeOut", get_needFadeOut, set_needFadeOut);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetOperaBtnCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			System.Action<int> arg1 = (System.Action<int>)ToLua.CheckDelegate<System.Action<int>>(L, 3);
			obj.SetOperaBtnCallback(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddActor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			string arg1 = ToLua.CheckString(L, 3);
			bool o = obj.AddActor(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddActorObj(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.CheckObject<UnityEngine.Object>(L, 2);
			string arg1 = ToLua.CheckString(L, 3);
			bool o = obj.AddActorObj(arg0, arg1);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTrackMute(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			string arg0 = ToLua.CheckString(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.SetTrackMute(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPlayEndCallback(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
				obj.SetPlayEndCallback();
				return 0;
			}
			else if (count == 2)
			{
				CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
				System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
				obj.SetPlayEndCallback(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CGController.SetPlayEndCallback");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetChangeCallback(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
				obj.SetChangeCallback();
				return 0;
			}
			else if (count == 2)
			{
				CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
				System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
				obj.SetChangeCallback(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CGController.SetChangeCallback");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			obj.Play();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RebuildPlay(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			obj.RebuildPlay();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			obj.Stop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Skip(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			obj.Skip();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SkipToEnd(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			obj.SkipToEnd();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetPlayableDirector(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			UnityEngine.Playables.PlayableDirector o = obj.GetPlayableDirector();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetPlayableDirector(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CGController obj = (CGController)ToLua.CheckObject(L, 1, typeof(CGController));
			UnityEngine.Playables.PlayableDirector arg0 = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 2);
			obj.SetPlayableDirector(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_needFadeOut(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CGController obj = (CGController)o;
			bool ret = obj.needFadeOut;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index needFadeOut on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_needFadeOut(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CGController obj = (CGController)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.needFadeOut = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index needFadeOut on a nil value");
		}
	}
}

