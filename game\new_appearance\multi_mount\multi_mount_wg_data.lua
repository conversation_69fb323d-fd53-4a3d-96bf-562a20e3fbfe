MultiMountWGData = MultiMountWGData or BaseClass()

function MultiMountWGData:__init()
	if MultiMountWGData.Instance then
		error("[MultiMountWGData]:Attempt to create singleton twice!")
	end

	MultiMountWGData.Instance = self

	local double_mount_cfg = ConfigManager.Instance:GetAutoConfig("double_mount_cfg_auto")
	self.mount_cfg = double_mount_cfg.mount
	self.star_level_cfg = ListToMap(double_mount_cfg.star_level, "seq", "star_level")

	RemindManager.Instance:Register(RemindName.NewAppearance_Multi_Mount, BindTool.Bind(self.GetMultiMountRemind, self))

	self.multi_mount_use_seq = -1
	self.multi_mount_start_level_info = {}

	self:InitCfgCache()

	-- 双人坐骑驾驶 自己及成员所有数据
	self.multi_mount_drive_list = {}
	-- 双人坐骑乘坐 只包含驾驶者 Obj_id
	self.multi_mount_take_list = {}
end

function MultiMountWGData:__delete()
    MultiMountWGData.Instance = nil
    
end
-----------------------------------------REMIND_START--------------------------------------
function MultiMountWGData:GetMultiMountRemind()
	for k, v in pairs(self:GetTotalMountCfgDataList()) do
		if self:GetMultiMountRemindBySeq(v.seq) then
			return 1
		end
	end

	return 0
end

function MultiMountWGData:GetMultiMountRemindBySeq(seq)
	if self:IsMultiMountIsMaxLevel(seq) then
		return false
	end

	local level = self:GetMultiMountLevelBySeq(seq) 
	local cur_level_cfg = self:GetMountStarLevelCfg(seq, level)

	if cur_level_cfg then
		local has_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)

		if has_num >= cur_level_cfg.cost_item_num then
			return true
		end
	end

	return false
end
------------------------------------------REMIND_END---------------------------------------

-----------------------------------------PROTOCOL_START------------------------------------
function MultiMountWGData:SetDoubleMountBaseInfo(protocol)
	self.multi_mount_use_seq = protocol.use_seq
end

function MultiMountWGData:SetDoubleMountItemInfo(protocol)
	self.multi_mount_start_level_info = protocol.item_list
end

function MultiMountWGData:UpdateDoubleMountItem(protocol)
	self.multi_mount_start_level_info[protocol.seq] = protocol.item
end

function MultiMountWGData:GetMultiMountLevelBySeq(seq)
	return (self.multi_mount_start_level_info[seq] or {}).star_level or 0
end

function MultiMountWGData:IsCurHuanHuaMultiMount(seq)
	return self.multi_mount_use_seq == seq
end

function MultiMountWGData:IsHuanHuaMultiMount()
	return self.multi_mount_use_seq >= 0
end

function MultiMountWGData:DoubleMountRideDown(protocol)
	if protocol.main_obj_id == protocol.obj_id then
		self:RemoveMultiMountDriveData(protocol.obj_id)
        return
    end

	self:RemoveMultiMountTakeData(protocol.obj_id)
	
	-- 更新主表数据
	local take_data = ((self.multi_mount_drive_list[protocol.main_obj_id] or {}).ride_item_list or{})[protocol.pos]
	if not IsEmptyTable(take_data) then
		self.multi_mount_drive_list[protocol.main_obj_id].ride_item_list[protocol.pos].obj_id = -1
	end
end
------------------------------------------PROTOCOL_END-------------------------------------

-----------------------------------------CFG_GET_START-------------------------------------
function MultiMountWGData:InitCfgCache()
	local item_check_data_list = {}

	for k, v in pairs(self.star_level_cfg) do
		for i, u in pairs(v) do
			if nil == item_check_data_list[u.cost_item_id] then
				item_check_data_list[u.cost_item_id] = u.cost_item_id
			end
		end
	end

	self.item_check_data_list = item_check_data_list
end

function MultiMountWGData:GetTotalMountCfgDataList()
	return self.mount_cfg
end

function MultiMountWGData:GetTotalMountCfgBySeq(seq)
	return self.mount_cfg[seq]
end

function MultiMountWGData:GetMountStarLevelCfg(seq, level)
	return (self.star_level_cfg[seq] or {})[level]
end
------------------------------------------CFG_GET_END--------------------------------------

-----------------------------------------CAL_START-----------------------------------------
function MultiMountWGData:IsUpLevelCostItem(change_item_id)
	return nil ~= self.item_check_data_list[change_item_id]
end

function MultiMountWGData:IsMultiMountIsMaxLevel(seq)
	local level = self:GetMultiMountLevelBySeq(seq) 
	local next_level_cfg = self:GetMountStarLevelCfg(seq, level + 1)
	return IsEmptyTable(next_level_cfg)
end

function MultiMountWGData:GetMultiMountAttrAndCap(seq)
	local level = self:GetMultiMountLevelBySeq(seq) 
	local cur_level_cfg = self:GetMountStarLevelCfg(seq, level)
	local next_level_cfg = self:GetMountStarLevelCfg(seq, level + 1)

	local attr_data_list = EquipWGData.GetSortAttrListHaveNextByTypeCfg(cur_level_cfg, next_level_cfg, "attr_id", "attr_value", 1, 5)
	local _, cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(cur_level_cfg, "attr_id", "attr_value", nil, nil, 1, 5)
	
	return attr_data_list, cap
end

function MultiMountWGData:GetItemShowCfgByItemId(item_id)
	local target_data = {}
	local seq = -1
	
	for k, v in pairs(self.mount_cfg) do
		if v.show_item_id == item_id then
			target_data = self:GetMountStarLevelCfg(v.seq, 1)
			break
		end
	end

	return target_data
end

function MultiMountWGData:GetMultiMountIsActive(item_id)
	for k, v in pairs(self.mount_cfg) do
		if v.show_item_id == item_id then
			return self:GetMultiMountLevelBySeq(v.seq)  > 0
		end
	end

	return false
end

function MultiMountWGData:GetMultiMountIsActive(item_id)
	for k, v in pairs(self.mount_cfg) do
		if v.show_item_id == item_id then
			return self:GetMultiMountLevelBySeq(v.seq)  > 0
		end
	end

	return false
end

function MultiMountWGData:GetIsOnlyPlayIdelAniInScene(obj_id)
	local multi_mount_info, multi_mount_state = self:GetMultiMountDataInfo(obj_id)

	if not IsEmptyTable(multi_mount_info) and multi_mount_state ~= MULTI_MOUNT_STATE.NONE then
		local multi_mount_cfg = self:GetTotalMountCfgBySeq(multi_mount_info.seq)
		return multi_mount_cfg and multi_mount_cfg.no_run_only_idle == 1
	end

	return false
end
------------------------------------------CAL_END------------------------------------------

-----------------------------------------双人坐骑数据_START-----------------------------------------

function MultiMountWGData:AddMultiMountDriveData(obj_id, multi_mount_data)
	self.multi_mount_drive_list[obj_id] = multi_mount_data
end

function MultiMountWGData:RemoveMultiMountDriveData(obj_id)
	self.multi_mount_drive_list[obj_id] = nil
end

function MultiMountWGData:GetMultiMountDriveData(obj_id)
	return self.multi_mount_drive_list[obj_id]
end

function MultiMountWGData:AddMultiMountTakeData(obj_id, multi_mount_data)
	self.multi_mount_take_list[obj_id] = multi_mount_data.main_obj_id
end

function MultiMountWGData:RemoveMultiMountTakeData(obj_id)
	self.multi_mount_take_list[obj_id] = nil
end

function MultiMountWGData:GetMultiMountTakeData(obj_id)
	return self.multi_mount_take_list[obj_id]
end

-- 获取状态及数据
function MultiMountWGData:GetMultiMountDataInfo(obj_id)
	local state = MULTI_MOUNT_STATE.NONE

	local drive_data = self:GetMultiMountDriveData(obj_id)
	local take_drive_main_obj_id = self:GetMultiMountTakeData(obj_id)

	local target_data = {}

	if not IsEmptyTable(drive_data) then
		state = MULTI_MOUNT_STATE.DRIZVE
		target_data = drive_data
	elseif nil ~= take_drive_main_obj_id then
		local my_drive_data = self:GetMultiMountDriveData(take_drive_main_obj_id)

		if not IsEmptyTable(my_drive_data) then
			state = MULTI_MOUNT_STATE.TAKE
			target_data = my_drive_data
		end
	end

	return target_data, state
end

-- 双人坐骑驾驶者
function MultiMountWGData:IsMultiMountDrive(obj_id)
	return not IsEmptyTable(self:GetMultiMountDriveData(obj_id))
end

-- 双人坐骑搭乘者
function MultiMountWGData:IsMultiMountTake(obj_id)
	return nil ~= self:GetMultiMountTakeData(obj_id)
end

function MultiMountWGData:IsMyMultiMountTake()
	local obj_id = RoleWGData.Instance:GetAttr("obj_id")
	return self:IsMultiMountTake(obj_id)
end

-- 我所在多人坐骑是否满员
function MultiMountWGData:IsMyMultiMountFullRole(obj_id)
	local multi_mount_info, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(obj_id)

	if not IsEmptyTable(multi_mount_info) then
		local multi_mount_cfg = self:GetTotalMountCfgBySeq(multi_mount_info.seq)
		
		if not IsEmptyTable(multi_mount_cfg) then
			local has_num = self:GetMyMultiMountTotalRoleNum(obj_id)
			return multi_mount_cfg.ride_num <= has_num
		end
	end

	return false
end

-- 我所在多人坐骑上的人数
function MultiMountWGData:GetMyMultiMountTotalRoleNum(obj_id)
	local num = 0

	local multi_mount_info, multi_mount_state = MultiMountWGData.Instance:GetMultiMountDataInfo(obj_id)
	if not IsEmptyTable(multi_mount_info) and multi_mount_state ~= MULTI_MOUNT_STATE.NONE then
		local ride_item_list = multi_mount_info.ride_item_list

		if not IsEmptyTable(ride_item_list) then
			for k, v in pairs(ride_item_list) do
				local scene_obj = Scene.Instance:GetObj(v.obj_id)
					
				if scene_obj and scene_obj:IsRole() then
					num = num + 1
				end
			end
		end
	end

	return num
end
------------------------------------------双人坐骑数据_END------------------------------------------
