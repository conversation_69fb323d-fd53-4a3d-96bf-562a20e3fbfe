﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class QualityBudgetWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(QualityBudget), typeof(System.Object));
		<PERSON><PERSON>Function("AddPayload", AddPayload);
		<PERSON><PERSON>RegFunction("RemovePayload", RemovePayload);
		<PERSON><PERSON>unction("SetBudget", SetBudget);
		<PERSON><PERSON>unction("New", _CreateQualityBudget);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Budget", get_Budget, null);
		<PERSON><PERSON>("Payload", get_Payload, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateQualityBudget(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				QualityBudget obj = new QualityBudget(arg0);
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: QualityBudget.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddPayload(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			QualityBudget obj = (QualityBudget)ToLua.CheckObject(L, 1, typeof(QualityBudget));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			System.Action arg2 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 4);
			System.Action arg3 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 5);
			QualityBudget.PayloadHandle o = obj.AddPayload(arg0, arg1, arg2, arg3);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemovePayload(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			QualityBudget obj = (QualityBudget)ToLua.CheckObject(L, 1, typeof(QualityBudget));
			QualityBudget.PayloadHandle arg0 = (QualityBudget.PayloadHandle)ToLua.CheckObject<QualityBudget.PayloadHandle>(L, 2);
			obj.RemovePayload(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetBudget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			QualityBudget obj = (QualityBudget)ToLua.CheckObject(L, 1, typeof(QualityBudget));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetBudget(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Budget(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			QualityBudget obj = (QualityBudget)o;
			int ret = obj.Budget;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Budget on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Payload(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			QualityBudget obj = (QualityBudget)o;
			int ret = obj.Payload;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Payload on a nil value");
		}
	}
}

