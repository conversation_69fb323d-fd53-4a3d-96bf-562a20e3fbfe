MultiFunctionWGCtrl = MultiFunctionWGCtrl or BaseClass(BaseWGCtrl)

function MultiFunctionWGCtrl:InitDaoHangCtrl()
    self.daohang_holyseal_view = DaoHangHolySealView.New()
    self.daohang_suit_attr_view = DaoHangSuitAttrView.New()
    self.daohang_decompose_view = DaoHangDeComposeView.New()
    self.daohang_keyin_upstore_view = DaoHangKeYinUpStoreView.New()
    self.daohang_keyin_inlaystore_view = DaoHangKeYinInlayStoreView.New()
    self.daohang_resonance_view = DaoHangResonanceView.New()
    self.daohang_compose_view = DaoHangComposeView.New()
    self.daohang_dati_view = DaoHangDaTiView.New()
    self.daohang_suit_fashion_preview_view = DaoHangSuitFashionPreviewView.New()

    self:RegisterProtocol(CSTaoistOperate)
    self:RegisterProtocol(CSTaoistDecompsEquip)
    self:RegisterProtocol(CSTaiostUseStone)
    self:RegisterProtocol(SCTaoistInfo, "OnSCTaoistInfo")
    self:RegisterProtocol(SCTaoistSlotUpdate, "OnSCTaoistSlotUpdate")
    self:RegisterProtocol(SCTaoistHoleUpdate, "OnSCTaoistHoleUpdate")
    self:RegisterProtocol(SCTaoistRMBBuyUpdate, "OnSCTaoistRMBBuyUpdate")
    self:RegisterProtocol(SCTaoistGongMingUpdate, "OnSCTaoistGongMingUpdate")
    self:RegisterProtocol(SCTaoistInlayGongMingUpdate, "OnSCTaoistInlayGongMingUpdate")
    self:RegisterProtocol(SCMsgBag, "OnSCMsgBag")
    self:RegisterProtocol(SCMsgGrid, "OnSCMsgGrid")
    self:RegisterProtocol(SCTaoistQueUpdate, "OnSCTaoistQueUpdate")
    self:RegisterProtocol(SCTaoistTouchUpdate, "OnSCTaoistTouchUpdate")
end

function MultiFunctionWGCtrl:DeleteDaoHangCtrl()
    if self.daohang_holyseal_view then
        self.daohang_holyseal_view:DeleteMe()
        self.daohang_holyseal_view = nil
    end

    if self.daohang_suit_attr_view then
        self.daohang_suit_attr_view:DeleteMe()
        self.daohang_suit_attr_view = nil
    end

    if self.daohang_decompose_view then
        self.daohang_decompose_view:DeleteMe()
        self.daohang_decompose_view = nil
    end

    if self.daohang_keyin_upstore_view then
        self.daohang_keyin_upstore_view:DeleteMe()
        self.daohang_keyin_upstore_view = nil
    end

    if self.daohang_keyin_inlaystore_view then
        self.daohang_keyin_inlaystore_view:DeleteMe()
        self.daohang_keyin_inlaystore_view = nil
    end

    if self.daohang_resonance_view then
        self.daohang_resonance_view:DeleteMe()
        self.daohang_resonance_view = nil
    end

    if self.daohang_compose_view then
        self.daohang_compose_view:DeleteMe()
        self.daohang_compose_view = nil
    end

    if self.daohang_suit_fashion_preview_view then
        self.daohang_suit_fashion_preview_view:DeleteMe()
        self.daohang_suit_fashion_preview_view = nil
    end

    if self.daohang_dati_view then
        self.daohang_dati_view:DeleteMe()
        self.daohang_dati_view = nil
    end
end

-----------------------------------------协议Start---------------------------------------------
function MultiFunctionWGCtrl:OnCSTaoistOperate(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTaoistOperate)
	protocol.operate_type = operate_type or 0
 	protocol.param1 = param1 or 0
 	protocol.param2 = param2 or 0
 	protocol.param3 = param3 or 0
 	protocol:EncodeAndSend()
end

function MultiFunctionWGCtrl:OnCSTaoistDecompsEquip(count, msg_item)
    local protocol = ProtocolPool.Instance:GetProtocol(CSTaoistDecompsEquip)
    protocol.count = count or 0
    protocol.msg_item = msg_item or {}
    protocol:EncodeAndSend()
end

function MultiFunctionWGCtrl:OnCSTaiostUseStone(count, msg_item)      
	local protocol = ProtocolPool.Instance:GetProtocol(CSTaiostUseStone)
 	protocol.count = count or 0
 	protocol.msg_item = msg_item or {}
 	protocol:EncodeAndSend()
end

function MultiFunctionWGCtrl:OnSCTaoistInfo(protocol)
	self.data:SetDaoHangInfo(protocol)
    RemindManager.Instance:Fire(RemindName.DaoHang_Suit)
    RemindManager.Instance:Fire(RemindName.DaoHang_KeLing)
    RemindManager.Instance:Fire(RemindName.DaoHang_QiangHua)
    RemindManager.Instance:Fire(RemindName.DaoHang_JinJie)
    RemindManager.Instance:Fire(RemindName.DaoHang_KeYin)
    RemindManager.Instance:Fire(RemindName.DaoHang_KaiGuang)
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)

    if self.view and self.view:IsOpen() then
        self.view:UpdateMutiFuncTabbarClickLimit()
    end

    if self.daohang_suit_attr_view and self.daohang_suit_attr_view:IsOpen() then
        self.daohang_suit_attr_view:Flush()
    end

    if self.daohang_holyseal_view and self.daohang_holyseal_view:IsOpen() then
        self.daohang_holyseal_view:Flush()
    end

    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_suit)
end

function MultiFunctionWGCtrl:OnSCTaoistSlotUpdate(protocol)
	self.data:UpdataDaoHangSlot(protocol)
    RemindManager.Instance:Fire(RemindName.DaoHang_Suit)
    RemindManager.Instance:Fire(RemindName.DaoHang_QiangHua)
    RemindManager.Instance:Fire(RemindName.DaoHang_JinJie)
    RemindManager.Instance:Fire(RemindName.DaoHang_KeYin)
    RemindManager.Instance:Fire(RemindName.DaoHang_KaiGuang)
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_suit)
    local slot = protocol.slot
    if self.view:GetShowIndex() == TabIndex.daohang_qianghua then
        self.view:StartUpLevelAni(slot)
    end

    if self.view:GetShowIndex() == TabIndex.daohang_jinjie then
        self.view:StartUpGradeAni(slot)
    end
end

function MultiFunctionWGCtrl:OnSCTaoistHoleUpdate(protocol)
    self.data:UpdateDaoHangHole(protocol)
    RemindManager.Instance:Fire(RemindName.DaoHang_KeLing)
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
end

function MultiFunctionWGCtrl:OnSCTaoistRMBBuyUpdate(protocol)
    self.data:SetPiXiuLv(protocol.rmb_buy_level or 0)
    RemindManager.Instance:Fire(RemindName.DaoHang_Suit)
    self.daohang_holyseal_view:Flush()
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_suit)
end

function MultiFunctionWGCtrl:OnSCTaoistGongMingUpdate(protocol)
    local enhance_change = self.data:UpdateTaoistGongMing(protocol)
    local resonance_data = {}

    if enhance_change then
        resonance_data = self.data:GetDaoHangQiangHuaResonanceData()
    else
        resonance_data = self.data:GetDaoHangKeYinResonanceData()
    end

    MultiFunctionWGCtrl.Instance:ShowResonanceView(resonance_data)
    RemindManager.Instance:Fire(RemindName.DaoHang_QiangHua)
    RemindManager.Instance:Fire(RemindName.DaoHang_KeYin)
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
end

function MultiFunctionWGCtrl:OnSCTaoistInlayGongMingUpdate(protocol)
    self.data:UpdataDaoHangKeLingRenonanceInfo(protocol)
    RemindManager.Instance:Fire(RemindName.DaoHang_KeLing)
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
    local resonance_data = self.data:GetDaoHangKeLingResonanceData()
    MultiFunctionWGCtrl.Instance:ShowResonanceView(resonance_data)
end

function MultiFunctionWGCtrl:OnSCMsgBag(protocol)
	self.data:SetDaoHangBagInfo(protocol)
end

function MultiFunctionWGCtrl:OnSCMsgGrid(protocol)
	local new_data = protocol.grid_info
	local is_add, add_num = self.data:UpdateDaoHangBagInfo(protocol)

    if not IsEmptyTable(new_data) and new_data.item_id > 0 and new_data.num > 0 then
        if is_add and add_num > 0 then
            local name = ItemWGData.Instance:GetItemNameDarkColor(new_data.item_id)
            local str = string.format(Language.Bag.GetItemTxt, ToColorStr(name, ITEM_TIP_D_COLOR[new_data.color]),
                add_num)
            GlobalTimerQuest:AddDelayTimer(function()
                SysMsgWGCtrl.Instance:ErrorRemind(str)
            end, 0.2)
        end
    end

    RemindManager.Instance:Fire(RemindName.DaoHang_Suit)
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_suit)

    if self.daohang_decompose_view and self.daohang_decompose_view:IsOpen() then
        self.daohang_decompose_view:Flush()
    end

    if self.daohang_compose_view and self.daohang_compose_view:IsOpen() then
        self.daohang_compose_view:Flush()
    end
end

function MultiFunctionWGCtrl:OnSCTaoistQueUpdate(protocol)
    self.data:SetDaoHangDaTiInfo(protocol)
    
    if self.daohang_dati_view and self.daohang_dati_view:IsOpen() then
        self.daohang_dati_view:Flush(0, "FlushView")
    end
end

function MultiFunctionWGCtrl:OnSCTaoistTouchUpdate(protocol)
    RemindManager.Instance:Fire(RemindName.DaoHang_Suit)
    self.data:SetFuMoTime(protocol.touch_time or 0)
    self.data:SetFuMoNum(protocol.touch_num or 0)
    self.daohang_holyseal_view:Flush()
    ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView, TabIndex.daohang_suit)
end

----------------------------------OpenView----------------------------------------------
function MultiFunctionWGCtrl:OpenDaoHangHolySealView()
    if self.daohang_holyseal_view and not self.daohang_holyseal_view:IsOpen() then
        self.daohang_holyseal_view:Open()
    end
end

function MultiFunctionWGCtrl:OpenDaoHangSuitAttrView()
    if self.daohang_suit_attr_view and not self.daohang_suit_attr_view:IsOpen() then
        self.daohang_suit_attr_view:Open()
    end
end

function MultiFunctionWGCtrl:OpenDaoHangDeComposeView()
    if self.daohang_decompose_view and not self.daohang_decompose_view:IsOpen() then
        self.daohang_decompose_view:Open()
    end
end

function MultiFunctionWGCtrl:OpenDaoHangComposeView()
    if self.daohang_compose_view and not self.daohang_compose_view:IsOpen() then
        self.daohang_compose_view:Open()
    end
end

function MultiFunctionWGCtrl:OpenDaoHangDaTiView()
    if self.daohang_dati_view and not self.daohang_dati_view:IsOpen() then
        self.daohang_dati_view:Open()
    end
end

function MultiFunctionWGCtrl:OpenDaoHangSuitFashionPreviewView()
    if self.daohang_suit_fashion_preview_view and not self.daohang_suit_fashion_preview_view:IsOpen() then
        self.daohang_suit_fashion_preview_view:Open()
    end
end

function MultiFunctionWGCtrl:ShowResonanceView(resonance_data)
    if self.daohang_resonance_view then
        self.daohang_resonance_view:SetData(resonance_data)

        if not self.daohang_resonance_view:IsOpen() then
            self.daohang_resonance_view:Open()
        else
            self.daohang_resonance_view:Flush()
        end
    end
end

function MultiFunctionWGCtrl:OpenDaoHangKeYinInlayStoreView(slot, data_list)
    if self.daohang_keyin_inlaystore_view then
        self.daohang_keyin_inlaystore_view:SetData(slot, data_list)

        if not self.daohang_keyin_inlaystore_view:IsOpen() then
            self.daohang_keyin_inlaystore_view:Open()
        else
            self.daohang_keyin_inlaystore_view:Flush()
        end
    end
end

function MultiFunctionWGCtrl:OpenDaoHangKeYinUpStoreView(data_info)
    if self.daohang_keyin_upstore_view then
        self.daohang_keyin_upstore_view:SetData(data_info)

        if not self.daohang_keyin_upstore_view:IsOpen() then
            self.daohang_keyin_upstore_view:Open()
        else
            self.daohang_keyin_upstore_view:Flush()
        end
    end
end

function MultiFunctionWGCtrl:FlushHolySealView()
    if self.daohang_holyseal_view and self.daohang_holyseal_view:IsOpen() then
        self.daohang_holyseal_view:Flush()
    end
end
