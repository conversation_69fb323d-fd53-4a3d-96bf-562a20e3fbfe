GuildAnswerWGData = GuildAnswerWGData or BaseClass()

function GuildAnswerWGData:__init()
	if GuildAnswerWGData.Instance ~= nil then
		print("[GuildAnswerWGData] attempt to create singleton twice!")
		return		
	end
	GuildAnswerWGData.Instance = self

	self.player_info = {}
	self.question_info = {}
	self.question_rank_info = {}
	self.round_info = {}
	self.pass_view_send_colddown_list = {}
	self.last_invite_count = 0
	self.show_invite_anim_flag = false
	self.guild_last_answer_time = 0
end

function GuildAnswerWGData:__delete()
	GuildAnswerWGData.Instance = nil
end

function GuildAnswerWGData:SetQuestionPlayerInfo(protocol)
	self.player_info.exp = protocol.exp or 0
	self.player_info.guild_gongxian = protocol.guild_gongxian or 0
	self.player_info.is_gather = protocol.is_gather or 0
end

function GuildAnswerWGData:GetQuestionPlayerInfo()
	return self.player_info
end

function GuildAnswerWGData:SetQuestionInfo(protocol)
	self.question_info.question_state = protocol.question_state
	self.question_info.question_state_change_timestamp = protocol.question_state_change_timestamp
	self.question_info.question_index = protocol.question_index
	self.question_info.question_id = protocol.question_id
	self.question_info.question_end_timestamp = protocol.question_end_timestamp
end

function GuildAnswerWGData:GetQuestionInfo()
	return self.question_info
end

function GuildAnswerWGData:GetQuestionCfgById(id)
	return ConfigManager.Instance:GetAutoConfig("guild_question_auto").question_list[id]
end

function GuildAnswerWGData:SetGuildRankInfo(protocol)
	self.question_rank_info = protocol.guild_rank_list
end

function GuildAnswerWGData:SetGroundInfo(protocol)
	self.round_info.question_index = protocol.question_index
	self.round_info.question_id = protocol.question_id
	self.round_info.answer_role_uid = protocol.answer_role_uid
	self.round_info.answer_role_name = protocol.answer_role_name
	self.round_info.answer_str = protocol.answer
	self.round_info.next_round_time = protocol.next_round_time
end

function GuildAnswerWGData:GetRoundInfo()
	return self.round_info
end

function GuildAnswerWGData:GetGuildRankInfo()
	return self.question_rank_info
end

function GuildAnswerWGData:GetSelfGuildInfo()
	local role_vo = RoleWGData.Instance:GetRoleVo()
	local self_guild_id = role_vo.guild_id
	for i, v in pairs(self.question_rank_info) do
		if v.guild_id == self_guild_id then
			return i, v.guild_score
		end
	end
	return 0, 0
end

function GuildAnswerWGData:GetGuildQuestionOtherCfg()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_question_auto").other[1]
	return other_cfg
end

function GuildAnswerWGData:GetGuildQuestionActivityDay()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_question_auto").other[1]
	return Split(other_cfg.activity_day, "|")
end

function GuildAnswerWGData:GetJoinMinMax()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_question_auto").other[1]
	return other_cfg.join_level_min, other_cfg.join_level_max
end

function GuildAnswerWGData:GetMaxAnswerNum()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_question_auto").other[1]
	return other_cfg.question_num_limit or 20
end

function GuildAnswerWGData:HasInvite()
	local list = self:GetSetInviteMeInfo()
	if list == nil then
		return false
	end
	return #list > 0
end

function GuildAnswerWGData:IsShowInviteAnim()
	local list = self:GetSetInviteMeInfo()
	if self.show_invite_anim_flag == false then
		if list then
			self.show_invite_anim_flag = #list > 0
		else
			self.show_invite_anim_flag = false
		end
	end
end

function GuildAnswerWGData:GetShowInviteAnimFlag()
	return self.show_invite_anim_flag
end

function GuildAnswerWGData:SetShowInviteAnimFlag(flag)
	self.show_invite_anim_flag = flag
end

function GuildAnswerWGData:HasGetPassExp()
	return DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_GUILD_CHUANGONG_REWARD_EXP_TIMES) > 0
end

function GuildAnswerWGData:GetSetInviteInfo(protocol)
	if protocol then
		self.invite_list = protocol.inviter_list
		return
	end
	return self.invite_list
end

function GuildAnswerWGData:SetSCStartGuildChuanGong(protocol)
	self.inviter_objid = protocol.inviter_objid
	self.invited_objid = protocol.invited_objid
end

function GuildAnswerWGData:GetSetInviteMeInfo(protocol)
	if protocol then
		self.invite_me_list = protocol.inviter_me_list
		self:SetSceneObjMoveInfoList()
		return
	end
	
	if IsEmptyTable(self.role_list_by_id) then
		self:SetSceneObjMoveInfoList()
	end
	
	local list = self.invite_me_list or {}
	local fiter_list = {}
	for i, v in pairs(list) do
		if self.role_list_by_id[v.uid] then
			local role_info = self.role_list_by_id[v.uid]
			if self.invited_objid and self.inviter_objid then
				if role_info.obj_id ~= self.invited_objid and role_info.obj_id ~= self.inviter_objid then
					local accepet_role = __TableCopy(self.role_list_by_id[v.uid])
					accepet_role.type = GuildPassView.STATE.ACCEPT
					table.insert(fiter_list, accepet_role)
				end
			else
				local accepet_role = __TableCopy(self.role_list_by_id[v.uid])
				accepet_role.type = GuildPassView.STATE.ACCEPT
				table.insert(fiter_list, accepet_role)
			end
		end		
	end
	return fiter_list
end

function GuildAnswerWGData:GetUidIsInvitemeList(uid1,uid2)
	-- local obj_list = Scene.Instance:GetRoleList()
	local self_info = RoleWGData.Instance:GetRoleVo()
	local role_obj1 = Scene.Instance:GetRoleByRoleId(uid1)
	local role_obj2 = Scene.Instance:GetRoleByRoleId(uid2)
	if self.invite_me_list then
		for k,v in pairs(self.invite_me_list) do
			if role_obj1 then
				local role_vo = role_obj1:GetVo()
				if role_vo.obj_type == SceneObjType.Role and (role_vo.role_id == self_info.role_id or role_vo.role_id == v.uid) then
					return true
				end
			end
			if role_obj2 then
				local role_vo = role_obj2:GetVo()
				if role_vo.obj_type == SceneObjType.Role and (role_vo.role_id == self_info.role_id or role_vo.role_id == v.uid) then
					return true
				end
			end
		end
	end
	return false
end

function GuildAnswerWGData:ClearInvitemeInfo()
	self.invite_me_list = {}
end

function GuildAnswerWGData:GetSceneObjMoveInfoList()
	if not IsEmptyTable(self.role_list) then
		return self.role_list
	end
	self:SetSceneObjMoveInfoList()
	return self.role_list
end


function GuildAnswerWGData:SetSceneObjMoveInfoList()
	local obj_list = Scene.Instance:GetRoleList()
	local self_info = RoleWGData.Instance:GetRoleVo()
	self.role_list = {}
	self.role_list_by_id = {}
	for i, v in pairs(obj_list) do
		local role_vo = v:GetVo()
		if v:IsRole() and role_vo.role_id ~= self_info.role_id then
			local t_info = {}
			t_info.level = role_vo.level
			t_info.obj_id = role_vo.obj_id
			t_info.name = role_vo.name
			t_info.avatar_key_big = role_vo.avatar_key_big
        	t_info.avatar_key_small = role_vo.avatar_key_small
            t_info.prof = role_vo.prof
            t_info.sex = role_vo.sex
			t_info.role_id = role_vo.role_id
			t_info.pos_x = role_vo.pos_x
			t_info.pos_y = role_vo.pos_y
			t_info.type = GuildPassView.STATE.SEND
			t_info.fashion_photoframe = CheckList(role_vo, "appearance", "fashion_photoframe") or 0
			table.insert(self.role_list, t_info)
			self.role_list_by_id[t_info.role_id] = t_info
		end
	end
	table.sort(self.role_list,SortTools.KeyLowerSorter("level"))
end

function GuildAnswerWGData:ClearRoleList()
	self.role_list = {}
	self.role_list_by_id = {}
end

function GuildAnswerWGData:GetPassCfg()
	return ConfigManager.Instance:GetAutoConfig("guild_chuangong_auto")
end

function GuildAnswerWGData:GetPassExpInfo(target_level)
	local cfg = self:GetPassCfg()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local base_exp = self:GetBaseExpByLevel(role_level).kill_monster_exp
	local dif_level = math.abs(target_level - role_level)
	local add_per = 0
	local show_factor = 0
	for i, v in ipairs(cfg.chuangong_factor) do
		if dif_level >= v.level_interval then
			add_per = v.factor
			show_factor = v.show_factor or 0
		else
			break
		end
	end
	local final_exp = base_exp * add_per
	return CommonDataManager.ConverExp(final_exp), "+" .. show_factor/100 .. "%"
end

function GuildAnswerWGData:GetMaxPassExpPer()
	local cfg = self:GetPassCfg()
	local factor_cfg = cfg.chuangong_factor
	local max_per = factor_cfg[#factor_cfg].show_factor
	return max_per
end

function GuildAnswerWGData:GetBaseExpByLevel(level)
	local config = ConfigManager.Instance:GetAutoConfig("role_level_reward_auto").level_reward_list
	return config[level]
end

function GuildAnswerWGData:GetPassRemainTime()
	local cfg = self:GetPassCfg()
	local time = cfg.other[1].chuangong_remain_ms
	return time and time/1000 or 3
end

function GuildAnswerWGData:HasBtnPassOperate()
	return not self:HasGetPassExp()
	--local invite_me_list = self:GetSetInviteMeInfo()
	--return not IsEmptyTable(invite_me_list)
end

function GuildAnswerWGData:GetGuildRankReward(index)
	local answer_reward_cfg = ConfigManager.Instance:GetAutoConfig("guild_question_auto").rank_reward
	return answer_reward_cfg[index] and answer_reward_cfg[index].reward_cell
end

function GuildAnswerWGData:GetAnswerNumByScore(score)
	local cfg = self:GetGuildQuestionOtherCfg()
	return score / cfg.guild_score
end

function GuildAnswerWGData:GetGuildViewAnswerRank()
	local protocol_rank_list = self:GetGuildRankInfo()
	local cfg_rank_list = ConfigManager.Instance:GetAutoConfig("guild_question_auto").new_rank_reward
	local result_list = {}
	local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
	for i = 1, 10 do
		result_list[i] = {}
		if protocol_rank_list[i] then
			local server_id = protocol_rank_list[i].server_id or 1
		    local server_str = ""
		    if is_cross_server_stage then
		        server_str = string.format(Language.Common.ServerIdFormat,server_id)
		    end
		    local guild_name = server_str .. protocol_rank_list[i].guild_name
			result_list[i].guild_name = guild_name
			result_list[i].guild_score = self:GetAnswerNumByScore(protocol_rank_list[i].guild_score)
		else
			result_list[i].guild_name = Language.Common.ActivateNoOpen_2
			result_list[i].guild_score = Language.Common.ActivateNoOpen_2
		end
		for _, v in ipairs(cfg_rank_list) do
			if i >= v.min_rank_pos + 1 and i <= v.max_rank_pos + 1 then
				result_list[i].reward = v.show_reward_item
			end
		end
		result_list[i].rank = i
	end
	for i, v in ipairs(cfg_rank_list) do
		if v.min_rank_pos + 1 > 10 then
			local t = {}
			t.guild_name = Language.Common.ActivateNoOpen_2
			t.guild_score = Language.Common.ActivateNoOpen_2
			t.reward = v.show_reward_item
			t.rank = v.min_rank_pos + 1 .. "-" .. v.max_rank_pos + 1
			table.insert(result_list, t)
		end
	end
	return result_list
end

-- 传功界面邀请按钮冷却
function GuildAnswerWGData:SetPassViewSendColdDownTimeStamp(count_down_key, end_time_stamp)
	self.pass_view_send_colddown_list[count_down_key] = end_time_stamp
end
function GuildAnswerWGData:GetPassViewSendColdDownTimeStamp(count_down_key)
	return self.pass_view_send_colddown_list[count_down_key] or 0
end

function GuildAnswerWGData:GetGatherId()
	local other = self:GetGuildQuestionOtherCfg()
	return other.gather_id
end

function GuildAnswerWGData:GetCenterPos()
	local cfg = ConfigManager.Instance:GetAutoConfig("guild_question_auto").other
	if cfg and cfg[1] then
		return cfg[1].x, cfg[1].y
	end
end

function GuildAnswerWGData:SetToDayNotAlert()
	local key = "chat_guild_answer_key"
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
    PlayerPrefsUtil.SetInt(key .. main_role_id, open_day)
end

function GuildAnswerWGData:GetIsNeedOpenAlert()
	local has_today_flag = false
	local key = "chat_guild_answer_key"
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	has_today_flag = PlayerPrefsUtil.GetInt(key .. main_role_id) == open_day
	return has_today_flag
end

function GuildAnswerWGData:SetSendGuildAnswerFlag(flag)
	self.send_guild_answer_flag = flag
end

function GuildAnswerWGData:GetSendGuildAnswerFlag()
	return self.send_guild_answer_flag
end

function GuildAnswerWGData:SendGuildAnswerChuangWen()
	local msg_info = ChatWGData.CreateMsgInfo()
	local time = TimeWGCtrl.Instance:GetServerTime()
	msg_info.send_time_str = TimeUtil.FormatTable2HMS(os.date("*t", time))
	msg_info.channel_type = CHANNEL_TYPE.GUILD
	msg_info.msg_reason = CHAT_MSG_RESSON.GUILD_TIPS
	msg_info.content = Language.GuildAnswer.EnterAnswerChuangWen
	ChatWGCtrl.Instance.data:AddChannelMsg(msg_info)
	ChatWGCtrl.Instance:RefreshChannel(CHANNEL_TYPE.GUILD, true)
	self:SetSendGuildAnswerFlag(false)
end

function GuildAnswerWGData:SetToDayNotSendChuangWen()
	local key = "guild_answer_send_chuangwen"
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
    PlayerPrefsUtil.SetInt(key .. main_role_id, open_day)
end

function GuildAnswerWGData:GetToDayNeedSendChuangWen()
	local has_today_flag = false
	local key = "guild_answer_send_chuangwen"
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	has_today_flag = PlayerPrefsUtil.GetInt(key .. main_role_id) == open_day
	return has_today_flag
end

function GuildAnswerWGData:SetGuildLastAnswerTime(time)
	self.guild_last_answer_time = time
end

function GuildAnswerWGData:IsCanGuildAnswer()
	local other_cfg = self:GetGuildQuestionOtherCfg()
	local answer_interval = other_cfg.answer_interval
	local time = TimeWGCtrl.Instance:GetServerTime()

	local diff = time - self.guild_last_answer_time
	local delay_time = answer_interval - diff

	if delay_time < 0 then
		return true, 0
	else
		return false, delay_time
	end
end