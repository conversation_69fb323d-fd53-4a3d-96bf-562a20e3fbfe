BossZhanLingWGData = BossZhanLingWGData or BaseClass()

function BossZhanLingWGData:__init()
	if BossZhanLingWGData.Instance then
		ErrorLog("[BossZhanLingWGData] Attempt to create singleton twice!")
		return
	end

	BossZhanLingWGData.Instance = self

    self.score = 0
    self.grade = 0
    self.senior_order_flag = 0
    self.next_grade_time = 0               -- 下一档位战令的时间
    self.free_reward_flag = {}
    self.added_reward_flag = {}
    self.zhanling_level = 0
    self.zhanling_max_level_cfg = {}

    self:InitConfig()
    self:InitDataCache()
    -- 红点注册
	RemindManager.Instance:Register(RemindName.Boss_ZhanLing, BindTool.Bind(self.GetZhanLingRemind, self))
end

function BossZhanLingWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("hunt_monster_order_auto")
    self.other_cfg = cfg.other
    self.senior_order_cfg = ListToMap(cfg.senior_order, "grade")
    self.order_reward_cfg = ListToMap(cfg.order_reward, "grade", "seq")
    self.order_score_cfg = ListToMapList(cfg.order_score, "grade")
end

function BossZhanLingWGData:__delete()
    BossZhanLingWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.Boss_ZhanLing)
end

function BossZhanLingWGData:SetAllInfo(protocol)
	self.score = protocol.score
	self.grade = protocol.grade
	self.senior_order_flag = protocol.senior_order_flag
    self.next_grade_time = protocol.next_grade_time

    local function cal_order_reward_flag(data)
		local index = 0
		local bit_list = {}
		local order_reward_flag = {}

		for i,v in ipairs(data) do
			bit_list = bit:d2b(v, bit_list)
			for ii = 1, 8 do
				order_reward_flag[index] = bit_list[33 - ii]
				index = index + 1
			end
		end

		return order_reward_flag
	end

	self.free_reward_flag = cal_order_reward_flag(protocol.free_reward_flag)
	self.added_reward_flag = cal_order_reward_flag(protocol.added_reward_flag)

    self:CalZhanLingLevel()
end

function BossZhanLingWGData:InitDataCache()
	local zhanling_max_level_cfg = {}
	for k, v in pairs(self.order_reward_cfg) do
		zhanling_max_level_cfg[k] = zhanling_max_level_cfg[k] or {}

		local max_level_cfg = {}
		for i, u in pairs(v) do
			if IsEmptyTable(max_level_cfg) or u.need_score > max_level_cfg.need_score then
				max_level_cfg = u
			end
		end

		zhanling_max_level_cfg[k] = max_level_cfg
	end

	self.zhanling_max_level_cfg = zhanling_max_level_cfg
end

function BossZhanLingWGData:CalZhanLingLevel()
	local level = 0
	local score = self:GetScore()

	if score > 0 then
		local zhanling_reward_cfg = self:GetCurZhanLingRewardCfgList()

		if not IsEmptyTable(zhanling_reward_cfg) then
			for k, v in pairs(zhanling_reward_cfg) do
				if score >= v.need_score and v.seq > level then
					level = v.seq
				end
			end
		end
	end

	self.zhanling_level = level
end

function BossZhanLingWGData:GetMyGrade()
	return self.grade
end

function BossZhanLingWGData:GetDailyScoreLimit()
	return self.other_cfg[1].daily_score_limit or 0
end

-- 战令积分
function BossZhanLingWGData:GetScore()
	return self.score
end

function BossZhanLingWGData:GetZhanLingLevel()
	return self.zhanling_level
end

function BossZhanLingWGData:GetZhanLingBuyLevelCfg()
	return self.order_score_cfg[self.grade]
end

function BossZhanLingWGData:GetZhanLingResetTime()
	return self.next_grade_time
end

function BossZhanLingWGData:GetCurZhanLingRewardCfgList()
	return self.order_reward_cfg[self.grade]
end

function BossZhanLingWGData:GetCurGradeSeniorOrderCfg()
	return self.senior_order_cfg[self.grade]
end

function BossZhanLingWGData:GetZhanLingCfg(seq)
	return (self.order_reward_cfg[self.grade] or {})[seq]
end

-- 是否解锁高级战令
function BossZhanLingWGData:GetHigerOrderRewardFlag()
	return self.senior_order_flag == 1
end

function BossZhanLingWGData:GetZhanLingRemind()
    local reward_list = self:GetCurZhanLingRewardCfgList()

	if not IsEmptyTable(reward_list) then
		for k, v in pairs(reward_list) do
			local nor_can_get, high_can_get = self:IsCanGetZhanLingRewardBySeq(v.seq)

			if nor_can_get or high_can_get then
				return 1
			end
		end
	end

	return 0
end

--可以获取奖励.
function BossZhanLingWGData:CanGetZhanLingAllReward()
	local reward_list = self:GetCurZhanLingRewardCfgList()
	local count = 0

	if not IsEmptyTable(reward_list) then
		for k, v in pairs(reward_list) do
			local is_get_nor, is_get_highr = self:IsGetZhanLingRewardBySeq(v.seq)
			if not is_get_nor or not is_get_highr then
				count = count + 1
			end
		end
	end

	return count > 0 and true or false
end

function BossZhanLingWGData:GetZhanLingLevelByDevote(score)
	local cur_zhanling_cfg = self:GetCurZhanLingRewardCfgList()
	local level = 0

	for k, v in pairs(cur_zhanling_cfg) do
		if score < v.need_score then
			break
		end

		level = v.seq
	end

	return level
end

-- 普通奖励 高级奖励能否领取
function BossZhanLingWGData:IsCanGetZhanLingRewardBySeq(seq)
	local can_get_nor, can_get_highr = false, false
	-- 没领取
	local is_get_nor, is_get_highr = self:IsGetZhanLingRewardBySeq(seq)

	local cur_reward_cfg = self:GetZhanLingCfg(seq)

	if IsEmptyTable(cur_reward_cfg) then
		return can_get_nor, can_get_highr
	end

	local score = self:GetScore()
	local is_open_higer_zhanling = self:GetHigerOrderRewardFlag()
	can_get_nor = not is_get_nor and (score >= cur_reward_cfg.need_score)
	can_get_highr = not is_get_highr and (score >= cur_reward_cfg.need_score) and is_open_higer_zhanling

	return can_get_nor, can_get_highr
end

-- 普通奖励 高级奖励 领取标记
function BossZhanLingWGData:IsGetZhanLingRewardBySeq(seq)
	return self.free_reward_flag[seq] == 1, self.added_reward_flag[seq] == 1
end

function BossZhanLingWGData:GetZhanLingMaxLevelCfg()
	return self.zhanling_max_level_cfg[self.grade]
end

--获取战令是否最高级.
function BossZhanLingWGData:GetZhanLingIsMaxLevel()
	local cur_lv = self:GetZhanLingLevel()
	local max_level_cfg = self:GetZhanLingMaxLevelCfg()

	if IsEmptyTable(max_level_cfg) then
		return false
	end

	return max_level_cfg.seq == cur_lv
end