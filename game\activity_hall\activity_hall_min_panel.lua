ActIvityHallMinPanel = ActIvityHallMinPanel or BaseClass(SafeBaseView)

function ActIvityHallMinPanel:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/activity_hall_ui_prefab", "layout_min_boby_view")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel_adorn")
	self.data = nil
end

function ActIvityHallMinPanel:__delete()
end

function ActIvityHallMinPanel:ReleaseCallBack()
	self.data = nil

	if self.item_cells then
		for k,v in pairs(self.item_cells) do
			v:DeleteMe()
		end
	end
	self.item_cells = nil
end

function ActIvityHallMinPanel:CloseCallBack()
end

function ActIvityHallMinPanel:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Activity.DailyActTips6
	self.node_list.btn_min_cj.button:AddClickListener(BindTool.Bind1(self.On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	self.item_cells = {}
	for i = 0, 2 do
		self.item_cells[i] = ItemCell.New()
		self.item_cells[i]:SetInstanceParent(self.node_list["ph_item_"..i])
	end
end

function ActIvityHallMinPanel:ShowIndexCallBack()
	ActivityWGCtrl.Instance:SendActivityRoomStatusReq(ACTIVITY_TYPE.ZHUXIE)
	self:Flush()
end

function ActIvityHallMinPanel:OnClickCloseBtnHandler()
	self.data = nil
	self:OnCloseHandler()
end

function ActIvityHallMinPanel:CreateActIvityHallMinPanel()
	if not self:IsLoadedIndex(0) then
		return
	end
	self.node_list.lbl_activity_name.text.text = self.data.name
	if self.data.open_time_2 == "" then
		self.node_list.lbl_activity_time.text.text = self.data.open_tips..self.data.open_time .. "--" .. self.data.close_time
	else
		local open_time_tab = Split(self.data.open_time_2, "|")
		local close_time_tab = Split(self.data.close_time_2, "|")
		self.node_list.lbl_activity_time.text.text = open_time_tab[1] .. "--" .. close_time_tab[1] .. "    " .. open_time_tab[2] .. "--" .. close_time_tab[2]
	end

	local reward_list = self.data.reward_item
	if self.data.act_type == ACTIVITY_TYPE.KF_HOTSPRING then
		local other_cfg = HotSpringWGData.Instance:GetHotSpringRewardCfg()
		reward_list = other_cfg
	end

	for k,v in pairs(self.item_cells) do
		v:SetData({})
	end
	for k, v in pairs(self.data1) do
		if self.item_cells[k] then
			self.item_cells[k]:SetData(v)
		end
	end
end

function ActIvityHallMinPanel:OnFlush()
	if nil == self.data then return end
	-- if self:IsOpen() then
		self:CreateActIvityHallMinPanel()
	-- end
end

-- 刷列表数据源
function ActIvityHallMinPanel:SetPanelData(type)
	if self.data ~= nil then self.data = nil end
	self.data = ActIvityHallWGData.Instance:GetActivityCfgByType(type)
	self.data1 = ActIvityHallWGData.Instance:GetBossInfo()
	if not self.data then
		return
	end
	-- if self:IsOpen() then
		self:CreateActIvityHallMinPanel()
	-- end
end

function ActIvityHallMinPanel:OnClickAttendHandler()
	-- if self.data and self.data.act_type then
	-- 	ActIvityHallWGCtrl.Instance:OpenActivity(self.data.act_type)
	-- 	self:Close()
	-- end
	if self.data and self.data.act_type then
		if self.data.act_type == ACTIVITY_TYPE.ZHUXIE then
			ActivityWGData.Instance:OnEnterRoom(ACTIVITY_TYPE.ZHUXIE)
		else
			ActIvityHallWGCtrl.Instance:OpenActivity(self.data.act_type)
		end
		if self:IsOpen() then
			self:Close()
		end
	end
end