-----新  跨服1v1界面
local Reward_Count = 5

local RankHight1 = 66
local RankHight2 = 66

function Field1v1View:IntkuafuOnevsOne()
	XUI.AddClickEventListener(self.node_list["btn_matching"], BindTool.Bind(self.ClickMatchIngBtn,self))
	XUI.AddClickEventListener(self.node_list["onevone_btn_tips"], BindTool.Bind(self.ClicnOneVOneTips,self))
	XUI.AddClickEventListener(self.node_list["btn_season_reward"], BindTool.Bind(self.ClickSeasonReward,self))
	XUI.AddClickEventListener(self.node_list["onevone_look_kf_btn"], BindTool.Bind(self.ClickOneVOneLookKF,self))
	XUI.AddClickEventListener(self.node_list["onevone_btn_zhanji"], BindTool.Bind(self.ClickOneVOneZhanJi,self))
	XUI.AddClickEventListener(self.node_list["onevone_btn_tips"], BindTool.Bind(self.OpenOneVOneTips,self))
	--XUI.AddClickEventListener(self.node_list["1v1close"], BindTool.Bind(self.Close,self))

	self.onevone_rank_list = AsyncListView.New(KFOneVOneRankItem,self.node_list["onevone_rank_list"])
	--self.onevone_rank_list:SetCellSizeDel(BindTool.Bind(self.OneVOneRankListCellSize,self))

	local score_slider_reward_obj = self.node_list["score_slider_reward"].transform
	self.score_slider_reward_list = {}
	for i=1,Reward_Count do
		local obj = score_slider_reward_obj:Find("ph_reward_one_group"..i)
		self.score_slider_reward_list[i] = KFOneVOneGroupRewardItem.New(obj)
	end
	for i = 1, 3 do
		self["onevone_model" .. i] = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["onevone_model_" .. i],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self["onevone_model" .. i]:SetRenderTexUI3DModel(display_data)
		-- self["onevone_model" .. i]:SetUI3DModel(self.node_list["onevone_model_" .. i].transform, self.node_list["onevone_model_" .. i].event_trigger_listener,
		-- 1, false, MODEL_CAMERA_TYPE.BASE)
	end

    self.onevone_isload = true
	self:FlushOneVOneModel()
end

function Field1v1View:Deletekfonevone()
	if self.onevone_rank_list then
		self.onevone_rank_list:DeleteMe()
		self.onevone_rank_list = nil
	end

	for i = 1, 3 do
		if self["onevone_model" .. i] then
			self["onevone_model" .. i]:DeleteMe()
			self["onevone_model" .. i] = nil
		end
	end

	if not IsEmptyTable(self.score_slider_reward_list) then
		for k,v in pairs(self.score_slider_reward_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.score_slider_reward_list = {}
	end
	self.onevone_isload = nil
end

function Field1v1View:ShowOneVOneCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a3_fb_bj_4")
    if self.node_list.RawImage_tongyong then
    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
        end)
    end
	self.node_list["title_view_name"].text.text = Language.Rank.DianFengJingJi
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_SCORE_RANK)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_PERSON_INFO)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_WINNER_INFO)
	Field1v1WGCtrl.Instance:SendCSCross1V1Opera(CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_QUERY_MATCH_INFO)

	--UITween.MoveShowPanel(self.node_list["onevone_left_anim"],u3dpool.vec2(195, 800) , 0.5)
	--UITween.MoveShowPanel(self.node_list["onevone_right_anim"],u3dpool.vec2(250, 50) , 0.5)
	--UITween.MoveShowPanel(self.node_list["onevone_bottom_anim"],u3dpool.vec2(0, -130) , 0.5)
end

-- function Field1v1View:OneVOneRankListCellSize(data_index)
-- 	local index = data_index + 1
-- 	if index <= 3 then
-- 		return RankHight1
-- 	else
-- 		return RankHight2
-- 	end
-- end

function Field1v1View:OnFlushKuafuOneVOne()
	if not self.onevone_isload then
		return
	end
	local onevone_rank_list_data = KFOneVOneWGData.Instance:GetOneVOneRankList()
	if self.onevone_rank_list then
		self.onevone_rank_list:SetDataList(onevone_rank_list_data)
	end

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)
	XUI.SetGraphicGrey(self.node_list["btn_matching"],not is_open)

	self.node_list["blank_tip"]:SetActive(IsEmptyTable(onevone_rank_list_data))
	self.node_list["onevone_rank_list"]:SetActive(not IsEmptyTable(onevone_rank_list_data))

	local reward_cfg = KFOneVOneWGData.Instance:GetMatchCountRewardCfg()
	local reward_flag = false
	if reward_cfg ~= nil then
		for i = 1 ,Reward_Count do
			local seq = i - 1
			if self.score_slider_reward_list[i] and reward_cfg[seq] then
				self.score_slider_reward_list[i]:SetData(reward_cfg[seq])
				local can_get = KFOneVOneWGData.Instance:GeMatchCountRewardIsGet(reward_cfg[seq].seq)
				local is_get = KFOneVOneWGData.Instance:GetMatchCountRewardFetchFlag(reward_cfg[seq].seq)
				if can_get or is_get then
					self.node_list["reward_progress"].slider.value = ONEVONE_SLIDER[i]
					reward_flag = true
				end
			end
		end
	end

	for index = 1, 3 do
		local rank_reward_cfg = KFOneVOneWGData.Instance:GetRankRewardCfgBySeq(index - 1)
		local node = self.node_list["onevone_title_" .. index]
		if node and rank_reward_cfg and rank_reward_cfg.title_id > 0 then
			local bundle, asset = ResPath.GetTitleModel(rank_reward_cfg.title_id)
			if rank_reward_cfg.title_pos ~= "" then
				local pos_str = Split(rank_reward_cfg.title_pos, "|")
				local pos_x = pos_str[1] or 0
				local pos_y = pos_str[2] or 0
				RectTransform.SetAnchoredPositionXY(node.rect, pos_x, pos_y)
			end

			if rank_reward_cfg.title_scale ~= "" then
				RectTransform.SetLocalScale(node.rect, rank_reward_cfg.title_scale)
			end


			node:ChangeAsset(bundle, asset)
			node:SetActive(true)
		else
			node:SetActive(false)
		end
	end

	if reward_flag == false then
		self.node_list["reward_progress"].slider.value = 0
	end

	-- local uuid = RoleWGData.Instance:GetUUid()
	local person_info = KFOneVOneWGData.Instance:GetOneVOneMyRankPerson()
	local onevone_person_info = KFOneVOneWGData.Instance:GetOneVOnePersonInfo()
	self.node_list["my_rank_type"]:SetActive(not IsEmptyTable(person_info))
	local rank_num = person_info and person_info.rank or 1
	local score_num = onevone_person_info and onevone_person_info.score or 0
	local server_id = person_info and person_info.server_id or 0
	self.node_list["my_rank_score"].text.text = score_num
	local user_vo = GameVoManager.Instance:GetUserVo()
	local my_server_id = user_vo.plat_server_id
	self.node_list["my_rank_text"].text.text = string.format(Language.Kuafu1V1.RankStr, rank_num)
	-- local max_cfg = KFOneVOneWGData.Instance:GetMaxScoreRewardCfg()
	-- local per = score_num / max_cfg.need_score
	-- local slider_value = KFOneVOneWGData.Instance:GetCurSliderProgress()
	-- self.node_list["onevone_score_slider"].slider.value = slider_value

	local rank_red = KFOneVOneWGData.Instance:IsShowRankRewardRed()
	self.node_list["rank_reward_red"]:SetActive(rank_red == 1)

	local zhanji_red = KFOneVOneWGData.Instance:GetKFOneVOneMsgRed()
	self.node_list["onevone_btn_zhanji_red"]:SetActive(zhanji_red == 1)

	local win_times = onevone_person_info and onevone_person_info.win_times or 0
	local lose_times = onevone_person_info and onevone_person_info.lose_times or 0
	local match_count = win_times + lose_times
	self.node_list["onevone_jifen_text"].text.text = string.format(Language.Kuafu1V1.OneVOneMatchText,match_count)
	-- for i = 1, 4 do
	-- 	local is_grey  = match_count < reward_cfg[i - 1].need_match
	-- 	XUI.SetGraphicGrey(self.node_list["line_" .. i], is_grey)
	-- end

	local zhou_str,start_time,end_time = BiZuoWGData.Instance:GetActOpenTimeStr(ACTIVITY_TYPE.KF_ONEVONE)
	local time_str = start_time .. "-" .. end_time
	local open_time_str = string.format(Language.Kuafu1V1.OpenTimeZhouStr,zhou_str,time_str)
	self.node_list["onevone_time_tips"].text.text = open_time_str
end

function Field1v1View:FlushOneVOneModel()
	for i = 1, 3 do
		self.node_list["onevone_name_" .. i]:SetActive(false)
		self.node_list["onevone_model_" .. i]:SetActive(false)
		self.node_list["no_model_" .. i]:SetActive(true)
		local role_id, plat_type = KFOneVOneWGData.Instance:GetCross1V1Winner(i)

		if role_id and plat_type and role_id > 0 and plat_type >= 0 then
			BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (data_info)
				local special_status_table = {ignore_wing = true, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}

				if self["onevone_model" .. i] then
					self["onevone_model" .. i]:SetModelResInfo(data_info, special_status_table)
				end

				self.node_list["onevone_model_" .. i]:SetActive(true)
				self.node_list["no_model_" .. i]:SetActive(false)

				if self.node_list and self.node_list["onevone_name_" .. i] then
					self.node_list["onevone_name_" .. i]:SetActive(true)
				end

				local server_id = UserVo.GetServerId(role_id)

				if self.node_list and self.node_list["onevone_name_text_" .. i] then
					self.node_list["onevone_name_text_" .. i].text.text = string.format(Language.Kuafu1V1.ServerName_2, server_id, data_info.role_name)
				end

				if self.node_list and self.node_list["onevone_cap_text_" .. i] then
					self.node_list["onevone_cap_text_" .. i].text.text = CommonDataManager.ConverExpByThousand(data_info.capability)
				end

			end, plat_type)
		end
	end
end

function Field1v1View:ClickMatchIngBtn()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
		return
	end

	local knockout_state = KFOneVOneWGData.Instance:GetOneVOneKnockoutState()
	if knockout_state == KFOneVOneWGData.KnockoutState.WinnerEnd then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi3)
		return
	end
	Field1v1WGCtrl.Instance:EnterFieldPrepareScene(ACTIVITY_TYPE.KF_ONEVONE)
end

function Field1v1View:ClicnOneVOneTips()

end

function Field1v1View:ClickSeasonReward()
	Field1v1WGCtrl.Instance:OpenPerson()
end

function Field1v1View:ClickOneVOneLookKF()
end

function Field1v1View:ClickOneVOneZhanJi()
	Field1v1WGCtrl.Instance:OpenJingCai()
end


function Field1v1View:OpenOneVOneTips()
	RuleTip.Instance:SetContent(Language.Kuafu1V1.Kf1v1AfterTips, Language.Kuafu1V1.Kf1v1Tips, nil, nil, true)
end


------------------------KFOneVOneRankItem
KFOneVOneRankItem = KFOneVOneRankItem or BaseClass(BaseRender)

function KFOneVOneRankItem:__init()
	XUI.AddClickEventListener(self.node_list["head_btn"], BindTool.Bind(self.ClickRoleHead,self))
end

function KFOneVOneRankItem:__delete()

end

function KFOneVOneRankItem:OnFlush()
	if IsEmptyTable(self.data) then return end
	local rank_num = self.data.rank
	-- if self.index <= 3 then
	-- 	self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_" .. self.index))
	-- else
	-- 	self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_zudui_lbdi"))
	-- end
	local color = rank_num <= 3 and "#FFFFFF" or "#545252"
	self.node_list["rank_icon"]:SetActive(rank_num <= 3)
	self.node_list.rank_index:SetActive(rank_num > 3)

	if rank_num <= 3 then
		local role_id = self.data.uid
		AvatarManager.Instance:SetAvatarKey(role_id, self.data.avatar_key_big, self.data.avatar_key_small)
		self.node_list["rank_icon"].image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank_num))
		self.node_list["rank_icon"].image:SetNativeSize()
		self.node_list.rank_index_top.text.text = rank_num
	else
		-- if self.data.rank % 2 == 0 then
		-- 	self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a1_ty_pmd4"))
		-- else
		-- 	self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a1_ty_pmd5"))
		-- end
		self.node_list.rank_index.text.text = rank_num
	end
	self.node_list["score1"].text.text = self.data.score
	self.node_list["server1"].text.text = string.format(Language.WorldServer.ServerDefName,self.data.server_id)
	local name_list = Split(self.data.name, "_")
	self.node_list["name1"].text.text = name_list[1]
	-- self.node_list.vip_text:SetActive(self.data.vip and self.data.vip >= 1) -- 设置VIP等级
	-- if self.data.vip then  
	-- 	self.node_list.vip_text.text.text = "V"..self.data.vip
	-- end
end

function KFOneVOneRankItem:ClickRoleHead()
	if IsEmptyTable(self.data) then return end
	local role_id = self.data.uid
	local plat_type = self.data.plat_type
	local name = self.data.name
	local server_id = self.data.server_id

	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
	local main_role_plat_type = RoleWGData.Instance:GetPlatType()
	if server_id == main_role_server_id and plat_type == main_role_plat_type then
		BrowseWGCtrl.Instance:ShowOtherRoleInfo(role_id)
	else
		local role_head_items = {
			Language.Menu.ShowInfo,
		}
		BrowseWGCtrl.Instance:ShowOtherRoleInfo(role_id,nil , true, plat_type, role_head_items)

	end
	
	
end


-------------------------KFOneVOneGroupRewardItem
KFOneVOneGroupRewardItem = KFOneVOneGroupRewardItem or BaseClass(BaseRender)

function KFOneVOneGroupRewardItem:__init()
	XUI.AddClickEventListener(self.node_list["ph_reward"], BindTool.Bind(self.OnClickTerraceCallBack, self))
end

function KFOneVOneGroupRewardItem:__delete()

end

function KFOneVOneGroupRewardItem:OnFlush()
	if not self.data then return end 
	self.node_list["redpoint"]:SetActive(false)
	local seq = self.data.seq
	local can_get = KFOneVOneWGData.Instance:GeMatchCountRewardIsGet(seq)
	self.node_list["redpoint"]:SetActive(can_get)
	local is_get = KFOneVOneWGData.Instance:GetMatchCountRewardFetchFlag(seq)
	self.node_list["get_flag"]:SetActive(is_get)
	self.node_list["hl_img"]:SetActive(can_get)
	self.node_list["count"].text.text = self.data.need_match
	local reward_item = self.data.reward_item[0]
	local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
	self.node_list.icon_img.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
end

function KFOneVOneGroupRewardItem:OnClickTerraceCallBack()
	if not self.data then return end 
	local seq = self.data.seq
	local can_get = KFOneVOneWGData.Instance:GeMatchCountRewardIsGet(seq)
	if can_get then
		local opera_type = CROSS_1V1_OPERA_TYPE.CROSS_1V1_OPERA_TYPE_FETCH_MATCH_REWARD
		Field1v1WGCtrl.Instance:SendCSCross1V1Opera(opera_type,seq)
	else
		local reward_item = self.data.reward_item[0]
		TipWGCtrl.Instance:OpenItem(reward_item, ItemTip.FROM_NORMAL)
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.JiFenBuZhu)
	end
end