﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_AssetBundleCreateRequestWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.AssetBundleCreateRequest), typeof(UnityEngine.AsyncOperation));
		<PERSON><PERSON>unction("New", _CreateUnityEngine_AssetBundleCreateRequest);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.<PERSON>ar("assetBundle", get_assetBundle, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_AssetBundleCreateRequest(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.AssetBundleCreateRequest obj = new UnityEngine.AssetBundleCreateRequest();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.AssetBundleCreateRequest.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_assetBundle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.AssetBundleCreateRequest obj = (UnityEngine.AssetBundleCreateRequest)o;
			UnityEngine.AssetBundle ret = obj.assetBundle;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index assetBundle on a nil value");
		}
	}
}

