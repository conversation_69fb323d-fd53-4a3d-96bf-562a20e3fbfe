DujieSpiritImageView = DujieSpiritImageView or BaseClass(SafeBaseView)

--进阶形象展示预览
function DujieSpiritImageView:__init()
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/dujie_ui_prefab", "layout_dujie_spirit_image_view")
end

function DujieSpiritImageView:SetCurSelectIndex(select_type)
	self.cur_select_type_index = select_type
end

function DujieSpiritImageView:ReleaseCallBack()
	self.cur_select_type_index = nil

    if self.spirit_image_list and #self.spirit_image_list > 0 then
		for _, spirit_image_cell in ipairs(self.spirit_image_list) do
			spirit_image_cell:DeleteMe()
			spirit_image_cell = nil
		end

		self.spirit_image_list = nil
	end
end

function DujieSpiritImageView:LoadCallBack()
    -- 形象点击
    if self.spirit_image_list == nil then
        self.spirit_image_list = {}
        for i = 1, 3 do
            local attr_obj = self.node_list.spirit_image_list:FindObj(string.format("spirit_image_render_%d", i))
            if attr_obj then
                local cell = DuJieSpiritImageShowRender.New(attr_obj)
                -- cell:SetClickCallBack(BindTool.Bind1(self.OnSelectSpriitImageCB, self))
                self.spirit_image_list[i] = cell
            end
        end
    end
end

function DujieSpiritImageView:CloseCallBack()

end

function DujieSpiritImageView:OnFlush()
	if not self.cur_select_type_index then
		return
	end

	local cfg = CultivationWGData.Instance:GetActiveCfgByType(self.cur_select_type_index)
	local changes_name = cfg and cfg.changes_name or ""
	self.node_list.spirit_image_title.text.text = changes_name
	
	local image_list, _, _ = CultivationWGData.Instance:GetProgressImage(self.cur_select_type_index)
	for i, spirit_image_cell in ipairs(self.spirit_image_list) do
        spirit_image_cell:SetVisible(image_list[i] ~= nil)

        if image_list[i] ~= nil then
			spirit_image_cell:SetNowNuQiType(self.cur_select_type_index)
            spirit_image_cell:SetData(image_list[i])
        end
    end
end

--------------------------------------------------------------
DuJieSpiritImageShowRender = DuJieSpiritImageShowRender or BaseClass(BaseRender)
function DuJieSpiritImageShowRender:LoadCallBack()
	if not self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list.display_root,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.PS_M,
			can_drag = true,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)
	end

	if not self.skill_render then
		self.skill_render = DuJieSpiritImageSkillRender.New(self.node_list.skill_render)
		self.skill_render:SetClickCallBack(BindTool.Bind1(self.OnSelectSpriitSkillCB, self))
	end
end

function DuJieSpiritImageShowRender:OnSelectSpriitSkillCB(skill_item)
    if nil == skill_item or nil == skill_item.data then
		return
	end

    if skill_item.data then
		local show_data = {
            icon = skill_item.data.skill_icon,
            top_text = skill_item.data.skill_name,
            body_text = skill_item.data.skill_desc,
            x = 0,
            y = -120,
            set_pos = true,
            hide_next = true,
            is_active_skill = true,
        }
        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end


function DuJieSpiritImageShowRender:ReleaseCallBack()
	self.nuqi_type = nil

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.skill_render then
		self.skill_render:DeleteMe()
		self.skill_render = nil
	end
end

function DuJieSpiritImageShowRender:SetNowNuQiType(nuqi_type)
	self.nuqi_type = nuqi_type
end

function DuJieSpiritImageShowRender:OnFlush()
	if (not self.data) or (not self.nuqi_type) then
		return
	end

	local cfg = CultivationWGData.Instance:GetNuqiUpgradeCfg(self.nuqi_type, self.data.image_lv)
	local nuqi_level = CultivationWGData.Instance:GetAngerLevel(self.nuqi_type)

	if cfg ~= nil then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true, ignore_wing = true}
		self.role_model:SetModelResInfo(role_vo, special_status_table)
		self.role_model:SetAngerImage(self.nuqi_type, true, cfg.default_body, cfg.default_face, cfg.default_hair, false)
		local show_lv = self.data.image_lv > 0 and self.data.image_lv or 1

		local is_unlock = nuqi_level >= show_lv
		self.node_list.spirit_image_name.text.text = cfg.nuqi_lv_name or ""
		local color = is_unlock and COLOR3B.GREEN or COLOR3B.RED
		self.node_list.spirit_lock_tips.text.text = ToColorStr(string.format(Language.Cultivation.LevelUnLockTips, show_lv), color)
	end

	local skill_cfg = CultivationWGData.Instance:GetImageSkillBySkillId(self.data.skill_id)
	if skill_cfg then
		self.skill_render:SetData(skill_cfg)
	end

	-- local skill_list = CultivationWGData.Instance:GetActiveSkillListByType(self.nuqi_type) 
    -- local star_index = 1
	-- local skill_data
end


-- 技能
DuJieSpiritImageSkillRender = DuJieSpiritImageSkillRender or BaseClass(BaseRender)
function DuJieSpiritImageSkillRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
	self.node_list.skill_name.text.text = self.data.skill_name
end

