LuckyGiftBagRankView = LuckyGiftBagRankView or BaseClass(SafeBaseView)

function LuckyGiftBagRankView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(850,500)})
    self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "Lucky_gift_bag_rank_view")
	self:SetMaskBg(true, true)
end

function LuckyGiftBagRankView:ReleaseCallBack()
    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end
end

function LuckyGiftBagRankView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.LuckyGiftBag.RankTitle
	if nil == self.rank_list then
		self.rank_list = AsyncListView.New(LuckyGiftBagRankRender, self.node_list["ph_rank_list"])
	end

    self.node_list.no_data_text.text.text = Language.LuckyGiftBag.RankNoData
end

function LuckyGiftBagRankView:OnFlush()
    local rank_info = LuckyGiftBagWgData.Instance:GetRankData()
    if not IsEmptyTable(rank_info) and  not IsEmptyTable(rank_info.rank_item_list) then
        self.rank_list:SetDataList(rank_info.rank_item_list)
    end
    self.node_list["no_flag"]:SetActive(IsEmptyTable(rank_info) or IsEmptyTable(rank_info.rank_item_list))
end

-------------------------------------------------------------------------------------
LuckyGiftBagRankRender = LuckyGiftBagRankRender or BaseClass(BaseRender)

function LuckyGiftBagRankRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list.name.text.text = self.data.name
    self.node_list.lucky_value.text.text = self.data.lucky_value

    -- local image_name
	if self.data.rank_id <= 3 then
		-- image_name = "a1_ty_pmd" .. self.data.rank_id
		local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank_id)
        self.node_list["rank_num_image"].image:LoadSprite(bundle, asset)
        self.node_list.name.text.color = Str2C3b(COLOR3B.WHITE)
	else
		-- image_name = self.index % 2 ~= 0 and "a1_ty_pmd5" or "a1_ty_pmd4"
        self.node_list.name.text.color = Str2C3b(COLOR3B.DEFAULT)
	end
	-- local bundle, asset = ResPath.GetCommonImages(image_name)
	-- self.node_list.bg.image:LoadSprite(bundle, asset)

    self.node_list["rank_num_image"]:SetActive(self.data.rank_id <= 3)
	self.node_list["rank_num_image_bg"]:SetActive(self.data.rank_id <= 3)
    -- TODO 需要改为新版处理方式
    -- self.node_list.name.shadow.enabled = self.data.rank_id <= 3
	self.node_list.rank_text:SetActive(self.data.rank_id > 3)
    self.node_list.rank_text.text.text = self.data.rank_id
end