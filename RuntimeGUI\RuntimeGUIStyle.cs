﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class RuntimeGUIStyle
{
    public static GUIStyle DefaultLabel;
    public static GUIStyle RedLabel;
    public static GUIStyle GreenLabel;
    public static GUIStyle YellowLabel;

    public static void Init()
    {
        DefaultLabel = new GUIStyle();
        DefaultLabel.fontSize = 16;
        DefaultLabel.normal.textColor = new Color(1, 1,  1, 1);

        RedLabel = new GUIStyle();
        RedLabel.fontSize = 16;
        RedLabel.normal.textColor = new Color(1, 0, 0, 1);

        GreenLabel = new GUIStyle();
        GreenLabel.fontSize = 16;
        GreenLabel.normal.textColor = new Color(0, 1, 0, 1);

        YellowLabel = new GUIStyle();
        YellowLabel.fontSize = 16;
        YellowLabel.normal.textColor = new Color(1, 1, 0, 1);
    }
}
