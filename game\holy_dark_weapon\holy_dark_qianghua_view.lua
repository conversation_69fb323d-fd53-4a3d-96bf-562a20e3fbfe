function HolyDarkWeaponView:InitHolyDarkQiangHuaView()
	if not self.qianghua_show_list then
		self.qianghua_show_list = AsyncListView.New(HolyDarkQiangHuaRender, self.node_list["qianghua_equip_list"])
		self.qianghua_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectQiangHuaCell, self))
		self.qianghua_show_list:SetDefaultSelectIndex(nil)
	end

	if not self.qianghua_equip_item then
		self.qianghua_equip_item = ItemCell.New(self.node_list["qianghua_equip_cell"])
	    self.qianghua_equip_item:SetIsShowTips(false)
		self.qianghua_equip_item:SetClickCallBack(BindTool.Bind(self.OnClickQiangHuaEquipCell, self))
	end

	if not self.qianghua_stuff_item then
		self.qianghua_stuff_item = ItemCell.New(self.node_list["qianghua_stuff_cell"])
	end

	XUI.AddClickEventListener(self.node_list["qianghua_btn"], BindTool.Bind(self.ClickeQiangHuaBtn, self))

	self.qianghua_select_data = nil
	self.qianghua_select_index = nil
end

function HolyDarkWeaponView:DeleteHolyDarkQiangHuaView()
	if self.qianghua_show_list then
		self.qianghua_show_list:DeleteMe()
		self.qianghua_show_list = nil
	end

	if self.qianghua_equip_item then
        self.qianghua_equip_item:DeleteMe()
        self.qianghua_equip_item = nil
    end

    if self.qianghua_stuff_item then
        self.qianghua_stuff_item:DeleteMe()
        self.qianghua_stuff_item = nil
    end

	self.qianghua_select_data = nil
	self.qianghua_select_index = nil
end

function HolyDarkWeaponView:FlushHolyDarkQiangHuaView()
	local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
		local wear_all_data = HolyDarkWeaponWGData.Instance:GetHolyDarkEquipWearList(relic_seq)
		local data = {}
		for i = 0, 8 do
			if wear_all_data[i] and wear_all_data[i].item_id > 0 then
				table.insert(data, wear_all_data[i])
			end
		end

		self.qianghua_show_list:SetDataList(data)
		
		local click_cell_index = self.qianghua_select_index or 1
		if not self.qianghua_select_index then
			for i, v in ipairs(data) do
				if HolyDarkWeaponWGData.Instance:GetRelicSlotCanUpLevel(v) then
					click_cell_index = i
					break
				end
			end
		end

		self.qianghua_show_list:JumpToIndex(click_cell_index)
    end
end

function HolyDarkWeaponView:OnSelectQiangHuaCell(cell)
	if nil == cell and nil == cell.data then
		return
	end

	self.qianghua_select_data = cell.data
	self.qianghua_select_index = cell.index
	self:FlushQiangHuaLeftPanel()
end

function HolyDarkWeaponView:FlushQiangHuaLeftPanel()
	if self.qianghua_select_data == nil then
		return
	end 

	local data = self.qianghua_select_data
	self.qianghua_equip_item:SetData({item_id = data.item_id})
	self.qianghua_equip_item:SetLeftTopImg(data.star_level or 0)
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local item_name = item_cfg and item_cfg.name or ""
	if data.star_level > 0 then
		local star_str = CommonDataManager.GetAncientNumber(data.star_level)
		item_name = string.format(Language.HolyDarkWeapon.EquipStarName, star_str, item_name)
	end

	if data.level > 0 then
		item_name = string.format(Language.HolyDarkWeapon.EquipLevelName, item_name, data.level)
	end
	self.node_list.qianghua_equip_name.text.text = item_name
	local cur_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotLevelCfg(data.relic_seq, data.slot_index, data.level)
	local next_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotLevelCfg(data.relic_seq, data.slot_index, data.level + 1)
	local is_max = IsEmptyTable(next_level_cfg)
	self.node_list.qianghua_stuff_cell:SetActive(not is_max)
	self.node_list.qianghua_btn:SetActive(not is_max)
	self.node_list.qianghual_level_max:SetActive(is_max)
	self.node_list.qianghua_remind:SetActive(false)

	if not IsEmptyTable(cur_level_cfg) then
		self.qianghua_stuff_item:SetData({item_id = cur_level_cfg.cost_item_id})
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		local color = item_num >= cur_level_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.qianghua_stuff_item:SetRightBottomTextVisible(true)
		self.qianghua_stuff_item:SetRightBottomText(ToColorStr(item_num .. '/' .. cur_level_cfg.cost_item_num, color))
		self.node_list.qianghua_remind:SetActive(not is_max and item_num >= cur_level_cfg.cost_item_num)
	end

	self:OnFlushQiangHuaAttrPanel()
end

function HolyDarkWeaponView:OnFlushQiangHuaAttrPanel()
	if self.qianghua_select_data == nil then
		return
	end 

	local data = self.qianghua_select_data
	local cur_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotLevelCfg(data.relic_seq, data.slot_index, data.level)
	local next_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotLevelCfg(data.relic_seq, data.slot_index, data.level + 1)
	local cur_value = (cur_level_cfg and cur_level_cfg.attr_per or 0) * 0.01
	local cur_str = "+" .. cur_value .."%"
	self.node_list.qianghua_cur_attr_1.text.text = string.format(Language.HolyDarkWeapon.QiangHuaAttr, cur_str)
	if not IsEmptyTable(next_level_cfg) then
		local next_value = (next_level_cfg and next_level_cfg.attr_per or 0) * 0.01
		local next_str = ToColorStr( "+" .. next_value .."%", '#8ee08e')
		self.node_list.qianghua_next_attr_1.text.text = string.format(Language.HolyDarkWeapon.QiangHuaAttr, next_str)
		self.node_list.qianghua_next_attr_max:SetActive(false)
	else
		self.node_list.qianghua_next_attr_1.text.text = ""
		self.node_list.qianghua_next_attr_max:SetActive(true)
	end
end

function HolyDarkWeaponView:OnClickQiangHuaEquipCell()
	if self.qianghua_select_data == nil then
		return
	end 

	TipWGCtrl.Instance:OpenItem(self.qianghua_select_data, ItemTip.FROM_RELIC_EQUIP)
end

function HolyDarkWeaponView:ClickeQiangHuaBtn()
	local relic_seq = HolyDarkWeaponWGCtrl.Instance:GetHolyDarkViewSelectWeaponSeq()
    if relic_seq and relic_seq >= 0 then
    	local data = self.qianghua_select_data
    	local cur_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotLevelCfg(data.relic_seq, data.slot_index, data.level)
		local next_level_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotLevelCfg(data.relic_seq, data.slot_index, data.level + 1)
		local is_max = IsEmptyTable(next_level_cfg)
		if is_max then
			TipWGCtrl.Instance:ShowSystemMsg(Language.HolyDarkWeapon.MaxLevel)
            return
		end

		if not IsEmptyTable(cur_level_cfg) then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
			if item_num >= cur_level_cfg.cost_item_num then
				HolyDarkWeaponWGCtrl.Instance:SendCSHolyDarkWeaponRequest(RELIC_OPERATE_TYPE.SLOT_UPLEVEL, data.relic_seq, data.slot_index)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Common.ItemNotEnough)
				TipWGCtrl.Instance:OpenItem({item_id = cur_level_cfg.cost_item_id})
            	return
			end
		end
    end
end

function HolyDarkWeaponView:PlayQiangHuaEffect()
	if self.node_list["qianghua_succ_pos"] then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_qianghua, is_success = true, pos = Vector2(0, 0),
								parent_node = self.node_list["qianghua_succ_pos"]})
	end
end

function HolyDarkWeaponView:DoHolyDarkQiangHuaViewAnim()
	local tween_info = UITween_CONSTS.HolyDark
	RectTransform.SetAnchoredPositionXY(self.node_list.qianghua_equip_list.rect, 440, 0)
	local right_tween = self.node_list.qianghua_equip_list.rect:DOAnchorPos(Vector2(0, 0), tween_info.movetime)
	RectTransform.SetSizeDeltaXY(self.node_list["qianghua_attr_panel"].rect, 0, 328)
	self.node_list["qianghua_attr_panel"].rect:DOSizeDelta(Vector2(820, 328), 1)
end


---------------------

-----------------------------
HolyDarkQiangHuaRender = HolyDarkQiangHuaRender or BaseClass(BaseRender)

function HolyDarkQiangHuaRender:LoadCallBack()
	self.equip_item = ItemCell.New(self.node_list["equip_cell"])
    self.equip_item:SetIsShowTips(false)
	self.equip_item:SetClickCallBack(BindTool.Bind(self.OnItemClick, self))
end

function HolyDarkQiangHuaRender:__delete()
	if self.equip_item then
        self.equip_item:DeleteMe()
        self.equip_item = nil
    end
end

function HolyDarkQiangHuaRender:OnFlush()
	if not self.data then
		return
	end

	self.equip_item:SetData({item_id = self.data.item_id})
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.name_text.text.text = item_cfg and item_cfg.name or ""
	self.node_list.qianghua_level.text.text = string.format(Language.HolyDarkWeapon.QiangHuaStr, self.data.level) 
	local is_up = HolyDarkWeaponWGData.Instance:GetRelicSlotCanUpLevel(self.data)
	self.node_list.remind:SetActive(is_up)
	self.equip_item:SetLeftTopImg(self.data.star_level or 0)
end

function HolyDarkQiangHuaRender:OnSelectChange(is_select)
	if not self.data then
		return
	end

	self.node_list.hl_img:SetActive(is_select)
end

function HolyDarkQiangHuaRender:OnItemClick()
	if not self.data then
		return
	end

	TipWGCtrl.Instance:OpenItem(self.data, ItemTip.FROM_RELIC_EQUIP)
end