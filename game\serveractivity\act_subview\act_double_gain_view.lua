----------------------------------------------------
-- 双倍获取
----------------------------------------------------
ActDoubleGainView = ActDoubleGainView or BaseClass(ActBaseViewTwo)

function ActDoubleGainView:__init(act_id)
	-- self.parent = parent
	-- local ui_config = ConfigManager.Instance:GetUiConfig("act_subview_ui_cfg")
	-- local child_config = nil
	-- for k, v in pairs(ui_config) do
	-- 	if v.n == "layout_double_gain" then
	-- 		child_config = v
	-- 		break
	-- 	end
	-- end
	self.ui_config = {"uis/view/act_subview_ui_prefab","DoubleGainView"}
	self.config_tab = {
		{"layout_double_gain", {0}},
	}
	self.data_pass = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind1(self.GetSpecialView, self))
	self.act_id = act_id --or 148--2162--act_id
	self.open_tween = nil
	self.close_tween = nil
end
function ActDoubleGainView:CloseCallBack()
	ActBaseViewTwo.CloseCallBack(self)
	if CountDownManager.Instance:HasCountDown("ActDoubleGainView_activity_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("ActDoubleGainView_activity_end_countdown")
	end
end
function ActDoubleGainView:ReleaseCallBack()
	
	if self.data_pass then
		GlobalEventSystem:UnBind(self.data_pass)
		self.data_pass = nil
	end
end

function ActDoubleGainView:LoadCallBack()
	local flag = self.act_id == 148 and ".png" or ".jpg"
	self.node_list.Img9_right.raw_image:LoadSprite("uis/rawimages/double_gain_bg_right_" .. self.act_id,"double_gain_bg_right_" .. self.act_id .. flag)
	XUI.AddClickEventListener(self.node_list.btn_join, BindTool.Bind1(self.OnClickJoin, self))
end
function ActDoubleGainView:ShowIndexCallBack()
	self:RefreshView1()
end

function ActDoubleGainView:RefreshView(param_list)

end

function ActDoubleGainView:RefreshView1(param_list)
	self:RefreshTopDesc()
	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)
	if nil == open_act_cfg then
		return
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
	if nil == act_info or act_info.status ~= ACTIVITY_STATUS.OPEN then
		return
	end

	local time_left = act_info.next_time - TimeWGCtrl.Instance:GetServerTime()
	self.node_list["lbl_end_time"]:SetActive(true)
	local act_open = open_act_cfg.open_type          -- 这两个活动有子状态
	if act_open == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_EXP_DOUBLE or act_open == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP then
		local act_status = ServerActivityWGData.Instance:GetVersionActivityNowStatus(act_open)
		-- time_left = act_status.next_status_time - TimeWGCtrl.Instance:GetServerTime()
		if act_status.status == ACTIVITY_STATUS.STANDY then
			self.node_list.img_activity_time.text.text = Language.Activity.DoubleGainDescStartTime
		elseif act_status.status == ACTIVITY_STATUS.OPEN then
			self.node_list.img_activity_time.text.text = Language.Activity.DoubleGainDescEndTime
		elseif act_status.status == ACTIVITY_STATUS.CLOSE then
			self.node_list.img_activity_time.text.text = Language.Activity.DoubleGainDescEnd
			self.node_list["lbl_end_time"]:SetActive(false)
		end
	end

	self:GetSpecialView()

	if CountDownManager.Instance:HasCountDown("ActDoubleGainView_activity_end_countdown") then
		CountDownManager.Instance:RemoveCountDown("ActDoubleGainView_activity_end_countdown")
	end
	self:UpdataNextTime(0, time_left)
	CountDownManager.Instance:AddCountDown("ActDoubleGainView_activity_end_countdown", BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind1(self.CompleteNextTime, self), nil, time_left, 1)
	self.node_list["lbl_end_time"]:SetActive(act_open ~= ACTIVITY_TYPE.RAND_KILL_BOSS)
	self.node_list.img_activity_time:SetActive(act_open ~= ACTIVITY_TYPE.RAND_KILL_BOSS)
	self.node_list.layout_desc:SetActive(act_open == ACTIVITY_TYPE.RAND_KILL_BOSS)
end

function ActDoubleGainView:OnClickJoin()
	-- local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_KILL_BOSS)
	-- local open_list = info.cur_value_t.fb_open_list
	-- local tabindex_list = {[0] = "fubenpanel_equip", [1] = "fubenpanel_copper", [2] = "fubenpanel_pet", [3] = "fubenpanel_tafang"}
	-- local index = nil

	-- for k,v in pairs(open_list) do
	-- 	if 1 == v then
	-- 		index = tabindex_list[k]
	-- 		break
	-- 	end
	-- end

	FunOpen.Instance:OpenViewNameByCfg("boss#boss_world")
	ServerActivityWGCtrl.Instance:CloseActBanbenServerView()
end

function ActDoubleGainView:UpdataNextTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		if self.node_list["lbl_end_time"] then
			self.node_list["lbl_end_time"].text.text = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
		end
	end
end

function ActDoubleGainView:CompleteNextTime(is_auto_fuhuo)
	if self.node_list["lbl_end_time"] then
		self.node_list["lbl_end_time"]:SetActive(false)
	end
end

--双倍掉落特殊处理
function ActDoubleGainView:GetSpecialView()
	if self.act_id == ServerActClientId.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP then
		local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)
		if nil == open_act_cfg then
			self.double_drop_str = ""
		else
			local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
			--双倍掉落
			local tmp = ServerActivityWGData.Instance:GetDoubleDropCfg()
			local time = os.date("*t", TimeWGCtrl.Instance:GetServerTime() - act_info.start_time)
			-- local time = os.date("*t", act_info.next_time - 1 - TimeWGCtrl.Instance:GetServerTime())
			-- print_error(time,TimeUtil.FormatSecond2MYHM(TimeWGCtrl.Instance:GetServerTime()), TimeUtil.FormatSecond2MYHM(act_info.start_time))
			local str = Language.Activity.DoubleGainDescDay
			local day = time.day - 1

			local fuben_str = {}
			for k,v in pairs(tmp) do
				if v.day_index == day and v.is_open == 1 then
					fuben_str[#fuben_str + 1] = v.name
				end
			end
			local count = #fuben_str
			for i = 1,count - 1 do
				str = str .. fuben_str[i] .. "、"
			end
			if count > 0 then
				self.double_drop_str = str .. fuben_str[count]			
			end
		end
	else
		self.double_drop_str = ""
	end
	self:FlushSpecialView()
end

function ActDoubleGainView:FlushSpecialView()
	self.node_list.version_double_drop.text.text = self.double_drop_str
end

-- function ActDoubleGainView:RefreshTopDesc(is_auto_fuhuo)
--     local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(self.act_id)
-- 	local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
-- 	local star_str = ""
-- 	local end_str = ""
-- 	local top_desc = open_act_cfg.top_desc
-- 	if self.act_id == ServerActClientId.RAND_ACTIVITY_TYPE_FB_DOUBLE_DROP then
-- 		local info = ServerActivityWGData.Instance:GetCacheActRewardData(ServerActClientId.RAND_KILL_BOSS)
-- 		if info then
-- 			local open_list = info.cur_value_t.fb_open_list
-- 			local str = ""
-- 			for i,v in pairs(open_list) do
-- 				if 1 == v then
-- 					str = str ..  Language.Activity.FuBenName[i]
-- 				end
-- 				str = str .. " "
-- 			end
-- 			str = string.format(Language.Activity.DoubleDropDes, str)
-- 			top_desc = top_desc .. str
-- 		end
-- 	end
-- 	if act_info then
-- 		star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
-- 		end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time)
-- 	end
-- 	self.node_list.version_act_time.text.text = (star_str .. "----" .. end_str)
-- 	self.node_list.version_act_des.text.text = (top_desc)
-- end

