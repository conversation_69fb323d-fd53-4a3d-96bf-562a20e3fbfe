﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class EffectOrderGroupWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginStaticLibs("EffectOrderGroup");
		<PERSON><PERSON>unction("OnGameStop", OnGameStop);
		<PERSON><PERSON>un<PERSON>("RefreshRenderOrder", RefreshRenderOrder);
		<PERSON><PERSON>tatic<PERSON>();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGameStop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			EffectOrderGroup.OnGameStop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshRenderOrder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			EffectOrderGroup.RefreshRenderOrder(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

