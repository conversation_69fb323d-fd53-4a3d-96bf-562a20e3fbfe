DiyTitleNameView = DiyTitleNameView or BaseClass(SafeBaseView)

function DiyTitleNameView:__init()
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/chenghao_prefab", "layout_diy_title_name_view")
end

function DiyTitleNameView:__delete()
end

function DiyTitleNameView:OpenCallBack()

end

function DiyTitleNameView:CloseCallBack()
    self.title_text = nil
end

function DiyTitleNameView:ReleaseCallBack()
    self.title_text = nil
end

function DiyTitleNameView:LoadCallBack()
    self.node_list["name_input_field"].input_field.onValueChanged:AddListener(BindTool.Bind(self.OnInputChange, self))
    XUI.AddClickEventListener(self.node_list["btn_diy"], BindTool.Bind(self.OnClickBtnDiy, self))
end

function DiyTitleNameView:SetDataAndOpen(title_id)
    self.title_id = title_id
    self.title_cfg = TitleWGData.Instance:GetConfig(title_id)
    self.diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
    if self.diy_cfg and self.title_cfg then
        if self:IsOpen() then
            self:Flush()
        else
            self:Open()
        end
    else
        SysMsgWGCtrl.Instance:ErrorRemind("专属称号，配置问题")
    end
end

function DiyTitleNameView:OnInputChange()
    if not self.diy_cfg or not self.title_cfg then
        return
    end

    if IsNil(self.title_text) or not self.title_text.text then
        return
    end

    local name_text = self.node_list["name_input_field"].input_field.text
    if name_text ~= "" then
        self.title_text.text = name_text
    else
        local title_name = TitleWGData.Instance:GetDiyTitleName(self.title_cfg.title_id)
        if title_name and title_name ~= "" then
            self.title_text.text = title_name
        else
            self.title_text.text = self.title_cfg.name
        end
    end
end

function DiyTitleNameView:OnFlush()
    self.node_list["default_desc"].text.text = string.format(Language.Title.NameFontNumLimit, self.diy_cfg.title_name_len)
    -- self.node_list["name_input_field"].input_field.characterLimit = self.diy_cfg.title_name_len

    -- 称号
    local bundle, asset = ResPath.GetTitleModel(self.title_cfg.title_id)
	self.node_list["title_node"]:ChangeAsset(bundle, asset, false, function (obj)
        self.title_text = nil
        if IsNil(obj) then
            return
        end

        local text_obj = obj.gameObject.transform:Find("Text")
        if text_obj == nil then
            return
        end
        
        self.title_text = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
        local title_name = TitleWGData.Instance:GetDiyTitleName(self.title_cfg.title_id)
        if title_name and title_name ~= "" then
            self.title_text.text = title_name
        else
            self.title_text.text = self.title_cfg.name
        end
    end)
end

function DiyTitleNameView:OnClickBtnDiy()
    local name_text = self.node_list["name_input_field"].input_field.text
    local length = UTFSub.SubStringGetTotalIndex(name_text)
    if length ~= self.diy_cfg.title_name_len then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Title.NameFontNumError, self.diy_cfg.title_name_len))
        return
    end

    if ChatFilter.Instance:IsIllegal(name_text, true) or ChatFilter.IsEmoji(name_text) then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
        return
    end

    local len = string.len(name_text)
    local qukong_text = string.gsub(name_text, "%s", "")
    local qukong_text_len = string.len(qukong_text)
    --判断输入的名字是否带空格
    if qukong_text_len ~= len then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
        return
    end

    local old_title_name = TitleWGData.Instance:GetDiyTitleName(self.title_cfg.title_id)
    if old_title_name == name_text then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Title.NameFontSameError)
        return
    end

    TitleWGCtrl.Instance:SendSetDiyTitleName(self.title_cfg.title_id, name_text)
    self:Close()
end