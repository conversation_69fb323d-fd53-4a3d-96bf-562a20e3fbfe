RoleCharmRewardPreviewView = RoleCharmRewardPreviewView or BaseClass(SafeBaseView)

function RoleCharmRewardPreviewView:__init()
	self.view_name = GuideModuleName.RoleCharmNoticeRewardView
	self.view_layer = UiLayer.Pop
	self.view_name = "RoleCharmRewardPreviewView"

	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(814, 578)})
	self:AddViewResource(0, "uis/view/rolecharmnotice_ui_prefab", "layout_reward")
end

function RoleCharmRewardPreviewView:ReleaseCallBack()
	if self.list_reward_view then
		self.list_reward_view:DeleteMe()
		self.list_reward_view  = nil
	end

	self.tab_index = nil
end

function RoleCharmRewardPreviewView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.RoleCharmRank.reward_preview_name
	self.list_reward_view = AsyncListView.New(CharmRankRewardInfoItem, self.node_list["ph_item_list"])
	XUI.AddClickEventListener(self.node_list.man_rank_reward_btn, BindTool.Bind(self.SelectBtnType, self, 1))
	XUI.AddClickEventListener(self.node_list.woman_rank_reward_btn, BindTool.Bind(self.SelectBtnType, self, 2))

	self.tab_index = 1
	self:SelectBtnType(self.tab_index)
end

function RoleCharmRewardPreviewView:SelectBtnType(index)
	self.node_list.man_btn_image:SetActive(index == 1)
	self.node_list.woman_btn_image:SetActive(index == 2)
	self.tab_index = index

	self:FlushReward()
end

function RoleCharmRewardPreviewView:OnFlush()
	self:FlushReward()
end

function RoleCharmRewardPreviewView:FlushReward()
	local rank_type = self.tab_index == 1 and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	local data_list = RoleCharmNoticeWGData.Instance:GetHadDataRankList(rank_type)
	if IsEmptyTable(data_list) then
		return
	end
	self.list_reward_view:SetDataList(data_list)
end


CharmRankRewardInfoItem = CharmRankRewardInfoItem or BaseClass(BaseRender)
function CharmRankRewardInfoItem:__init()
	if self.reward_rank_list == nil then
		self.reward_rank_list = AsyncListView.New(ItemCell, self.node_list["ph_reward_rank_list"])
	end
end

function CharmRankRewardInfoItem:__delete()
	if self.reward_rank_list then
		self.reward_rank_list:DeleteMe()
		self.reward_rank_list = nil
	end
end


function CharmRankRewardInfoItem:OnFlush()
	if not self.data then
		return
	end
	local my_rank_num = RoleCharmNoticeWGData.Instance:GetSelfRankNum()
	local type = self.data.sex == GameEnum.MALE and PROFESS_RANK_TYPE.SEND_FLOWER_MALE or PROFESS_RANK_TYPE.SEND_FLOWER_FEMALE
	local reward_list = RoleCharmNoticeWGData.Instance:GetCharmRankRewardCfg(type)
	self.node_list["rank_img"]:SetActive(self.index <= 3)
	self.node_list["rank_reward_bg"]:SetActive(self.index <= 3)
	if self.index <= 3 then
		local bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. self.index)
		self.node_list["rank_reward_bg"].image:LoadSprite(bundle, asset);

		bundle, asset = ResPath.GetCommonImages("a3_ty_panking_" .. self.index)
		self.node_list["rank_img"].image:LoadSprite(bundle, asset);
	end
	self.node_list.lbl_rank.text.text = self.index
	self.reward_rank_list:SetDataList(reward_list[self.index])
end