DIYDrawOneView = DIYDrawOneView or BaseClass(SafeBaseView)
function DIYDrawOneView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
    self:SetMaskBg()
	self.default_index = 10

    self:AddViewResource(0, "uis/view/diy_draw_ui_prefab", "layout_diy_draw_one_bg_panel")
    self:AddViewResource(0, "uis/view/diy_draw_ui_prefab", "diydraw_one_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function DIYDrawOneView:LoadCallBack()
    self:InitCommonUI()
    for i = 1, 3 do
        self.node_list["draw_btn_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickRecord, self, i))
        --self.node_list["const_img_" .. i].button:AddClickListener(BindTool.Bind(self.ShowDrawItemTips, self))
    end
    XUI.AddClickEventListener(self.node_list["btn_gailv"], BindTool.Bind1(self.OnClickGaiLvShow, self))
    XUI.AddClickEventListener(self.node_list["btn_choose_reward"], BindTool.Bind1(self.OnClickChooseReward, self))
	XUI.AddClickEventListener(self.node_list["btn_reward_preview"], BindTool.Bind(self.OnClickRewardPreviewBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_draw_record"], BindTool.Bind(self.OnClickDrawRecordBtn, self))
	XUI.AddClickEventListener(self.node_list["add_item"], BindTool.Bind(self.OnClickAddItem, self))

    if self.reward_list == nil then
        self.reward_list = {}
        for i = 1, 6 do
            self.reward_list[i] = DIYDrawOneRewardRender.New(self.node_list.reward_root:FindObj("reward_pos_" .. i))
        end
    end

    --self.record_list = AsyncListView.New(DIY1RecordCell, self.node_list["record_list"])
	--self.record_list:SetCellSizeDel(BindTool.Bind(self.CellSizeDel, self))

    --[[
    if nil == self.item_tween then
		self.item_tween = {}
		for i = 1, 6 do
			local tween_root = self.node_list["reward_pos_" .. i].rect
			self.item_tween[i] = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 10, Tween_Time[i])
			self.item_tween[i]:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		end
	end
    ]]

	self.node_list["skip_spine_check"].button:AddClickListener(BindTool.Bind(self.OnClickSkipSpine, self))
end

function DIYDrawOneView:InitCommonUI()
	self.node_list.title_view_name.text.text = Language.DIYDraw.TitleViewName
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		self.tabbar:Init(Language.DIYDraw.TabGroup, nil, ResPath.CommonBundleName)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true, show_bind_gold = true,
			show_coin = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function DIYDrawOneView:CloseCallBack()
	if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end
end

function DIYDrawOneView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end
	
    if self.reward_list then
        for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
	    self.reward_list = nil
	end

    if self.item_tween then
		for k, v in pairs(self.item_tween)do
			v:Kill()
		end
		self.item_tween = nil
	end

    -- if self.record_list then
    --     self.record_list:DeleteMe()
    --     self.record_list = nil
    -- end

    if CountDownManager.Instance:HasCountDown("diydraw_one_down") then
		CountDownManager.Instance:RemoveCountDown("diydraw_one_down")
	end

	if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end
end

function DIYDrawOneView:OpenCallBack()
    DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.INFO)
end

function DIYDrawOneView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.DIYDrawOneView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW)
end

function DIYDrawOneView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushshowView()
            self:LoginTimeCountDown()
        elseif k == "flush_record_list" then
            --self:FlushRecordList()
		end
	end
end

function DIYDrawOneView:FlushshowView()
    self:FlushDrawBtnShow()
	self:FlushSkipSpine()

    local show_reward_list = DIYDrawWGData.Instance:GetSpecialRewardShowList()
    if show_reward_list then
        for i = 1, #show_reward_list do
            self.reward_list[i]:SetData(show_reward_list[i])
        end
    end
end

function DIYDrawOneView:OnClickGaiLvShow() --抽奖概率
    --DIYDrawWGCtrl.Instance:OpenDrawOneGaiLvView()
    local info = DIYDrawWGData.Instance:GetDrawOneProbabilityInfo()
	TipWGCtrl.Instance:OpenTipsRewardProView(info)
end

function DIYDrawOneView:OnClickChooseReward() --切换奖池
    DIYDrawWGCtrl.Instance:OpenDrawChooseView()
end

function DIYDrawOneView:OnClickAddItem()
	local mode_cfg = DIYDrawWGData.Instance:GetDrawOneItem()
	local item_cfg = ItemWGData.Instance:GetItemConfig(mode_cfg.cost_item_id)
	TipWGCtrl.Instance:OpenItem({item_id = item_cfg.id})
end

--[[
--抽奖日志刷新
function DIYDrawOneView:FlushRecordList()
    local record_list = DIYDrawWGData.Instance:GetDIY1Serverloglist()
    if IsEmptyTable(record_list) then
        self.node_list.no_data:SetActive(true)
    else
        self.node_list.no_data:SetActive(false)
        self.record_list:SetDataList(record_list)
    end
end
]]

function DIYDrawOneView:FlushDrawBtnShow(is_flush_num)
	local cfg = DIYDrawWGData.Instance:GetDIY1DrawConsumeCfg()
	if cfg == nil then
		return
	end

	local mode_cfg = DIYDrawWGData.Instance:GetDrawOneItem()
	local item_id = mode_cfg.cost_item_id
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	local count
	for i = 1, 3 do
		if cfg[i] then
			if not is_flush_num then
				if not IsEmptyTable(item_cfg) then
					--道具图标+++++++++++++++++	---
					self.node_list["const_img_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
				end
				--抽几次
				local btn_str = string.format(Language.DIYDraw.BtnDrawDesc, cfg[i].times)
				self.node_list["txt_buy_" .. i].text.text = btn_str
			end
			--道具红点数量
			count = ItemWGData.Instance:GetItemNumInBagById(item_id)
			local is_enough = count >= cfg[i].cost_item_num
			self.node_list["btn_red_" .. i]:SetActive(is_enough)
			-- local color = is_enough and "#663109" or COLOR3B.D_RED
			self.node_list["const_lbl_" .. i].text.text = cfg[i].cost_item_num
		end
	end

	self.node_list["item_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	local item_num = ItemWGData.Instance:GetItemNumInBagById(mode_cfg.cost_item_id)
	self.node_list["item_num"].text.text = item_num
end

function DIYDrawOneView:OnClickRecord(btn_index)
    local cfg = DIYDrawWGData.Instance:GetDIY1DrawConsumeCfg()
    cfg = cfg[btn_index]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
    if num >= cfg.cost_item_num then
        DIYDrawWGData.Instance:CacheOrGetDIY1DrawIndex(btn_index)
        --发送协议
		local is_skip = DIYDrawWGData.Instance:GetSkipSpineDraw1Status()
		if is_skip then
			DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.DRAW, cfg.mode)
		else
			if self.delay_play_draw_idle then
				GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
				self.delay_play_draw_idle = nil
			end

			self.node_list["effect_draw_root"]:SetActive(true)
			self.delay_play_draw_idle = GlobalTimerQuest:AddDelayTimer(function()
				DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.DRAW, cfg.mode)
				TryDelayCall(self, function()
                    self.node_list["effect_draw_root"]:SetActive(false)
                end, 0.2, "hide_diy_draw_1_effect")
			end, 1.4)
		end
    else
        DIYDrawWGCtrl.Instance:ClickUseDrawItem(DIYDRAW_TYPE.DIY1, btn_index, function ()
            self:OnClickDrawBuy(btn_index)
        end)
    end
end

function DIYDrawOneView:OnClickDrawRecordBtn()
    local record_list = DIYDrawWGData.Instance:GetDIY1DrawRecordlist()
	TipWGCtrl.Instance:OpenTipsRewardRecordView(record_list)
end

function DIYDrawOneView:OnClickRewardPreviewBtn()
	local data_list = DIYDrawWGData.Instance:GetDIY1RewardPreviewList()
	RewardShowViewWGCtrl.Instance:SetRewardShowData({ reward_item_list = data_list })
end


--点击购买
function DIYDrawOneView:OnClickDrawBuy(draw_type)
    local cfg = DIYDrawWGData.Instance:GetDIY1DrawConsumeCfg()
    local item_cfg = DIYDrawWGData.Instance:GetDrawOneItem()
    local cur_cfg = cfg[draw_type]
    if cur_cfg == nil then
        return
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.cost_item_id)
    local consume = item_cfg.cost_gold * (cur_cfg.cost_item_num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        DIYDrawWGData.Instance:CacheOrGetDIY1DrawIndex(draw_type)
		--发送协议
		local is_skip = DIYDrawWGData.Instance:GetSkipSpineDraw1Status()
		if is_skip then
			DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.DRAW, cur_cfg.mode)
		else
			if self.delay_play_draw_idle then
				GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
				self.delay_play_draw_idle = nil
			end

			self.node_list["effect_draw_root"]:SetActive(true)
			self.delay_play_draw_idle = GlobalTimerQuest:AddDelayTimer(function()
				DIYDrawWGCtrl.Instance:SendDIYDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW, OA_DIY_DRAW1_OPERATE_TYPE.DRAW, cur_cfg.mode)
				self.node_list["effect_draw_root"]:SetActive(false)
			end, 1.4)
		end
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

function DIYDrawOneView:ShowDrawItemTips()
    local cfg = DIYDrawWGData.Instance:GetDrawOneItem()
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.cost_item_id})
end


function DIYDrawOneView:OnClickSkipSpine()
    local is_skip = DIYDrawWGData.Instance:GetSkipSpineDraw1Status()
    is_skip = not is_skip
    DIYDrawWGData.Instance:SetSkipSpineDraw1Status(is_skip)
    self:FlushSkipSpine()
end

function DIYDrawOneView:FlushSkipSpine()
	local is_skip = DIYDrawWGData.Instance:GetSkipSpineDraw1Status()
	self.node_list.skip_spine_yes:SetActive(is_skip)
end
--[[
function DIYDrawOneView:CellSizeDel(data_index)
	local hight = 30
	local spece = 10
	data_index = data_index + 1
	local list = self.record_list:GetDataList()
	local data = list[data_index]
	if not data then
		return hight
	end

	local msg_str = ""
	local item_data = ItemWGData.Instance:GetItemConfig(data.item_id)
	local str = string.format(Language.DIYDraw.RecordName, data.name, item_data.name)
	msg_str = str
	self.node_list.TestText.text.text = msg_str
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)
	hight = math.ceil(self.node_list["TestText"].rect.rect.height) <= 45 and 45 or math.ceil(self.node_list["TestText"].rect.rect.height) + spece
	return hight
end
]]

------------------------------------活动时间倒计时
function DIYDrawOneView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DIY1_DRAW)
	if activity_data ~= nil then
		local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            local time_str = string.format(Language.CatExplore.ActivityTime, TimeUtil.FormatSecondDHM8(invalid_time - TimeWGCtrl.Instance:GetServerTime()))
            self.node_list["act_time"].text.text = time_str
            CountDownManager.Instance:AddCountDown("diydraw_one_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
	end
end
   
function DIYDrawOneView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        local time_str = string.format(Language.CatExplore.ActivityTime, TimeUtil.FormatSecondDHM8(valid_time))
        self.node_list["act_time"].text.text = time_str
    end
end

function DIYDrawOneView:OnComplete()
    self.node_list["act_time"].text.text = ""
    self:Close()
end


--------------------------------------------------------


DIYDrawOneRewardRender = DIYDrawOneRewardRender or BaseClass(BaseRender)
function DIYDrawOneRewardRender:__init()
end

function DIYDrawOneRewardRender:LoadCallBack()
    --XUI.AddClickEventListener(self.node_list["icon"], BindTool.Bind1(self.OnClickItemTipsShow, self))
	if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_cell_root)
		self.item_cell:SetIsUseRoundQualityBg(true)
		self.item_cell:SetCellBgEnabled(false)
	end
end

function DIYDrawOneRewardRender:ReleaseCallBack()
	if self.item_cell then
        self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function DIYDrawOneRewardRender:OnFlush()
	if not self.data then
		return
	end
	self.item_cell:SetData({item_id = self.data.item_id})
	self.node_list["txt_item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_id)
	--[[
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg ~= nil then
        local cell_bundle, cell_asset = ResPath.GetCommon("a2_sc_btn_bg_" .. item_cfg.color)
        self.node_list["icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))

        self.node_list.icon_bg.image:LoadSprite(cell_bundle, cell_asset, function()
            self.node_list.icon_bg.image:SetNativeSize()
        end)
        local effect_name = BaseCell_Ui_Circle_Effect[item_cfg.color]
        if effect_name then
            local bundle, assert = ResPath.GetWuPinKuangEffectUi(effect_name)
            self.node_list["effect_root"]:ChangeAsset(bundle, assert)
            self.node_list["effect_root"]:SetActive(true)
        else
            self.node_list["effect_root"]:SetActive(false)
        end
    end
	]]
end

-- function DIYDrawOneRewardRender:OnClickItemTipsShow()
--     if not self.data then
-- 		return
-- 	end

--     TipWGCtrl.Instance:OpenItem({item_id = self.data.item_id})
-- end


DIY1RecordCell = DIY1RecordCell or BaseClass(BaseRender)

function DIY1RecordCell:__init()

end

function DIY1RecordCell:__delete()

end

function DIY1RecordCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local item_data = ItemWGData.Instance:GetItemConfig(self.data.item_id)
        local item_name = ToColorStr(item_data.name, ITEM_COLOR_LIGHT[item_data.color])
		self.node_list["name_text"].text.text = string.format(Language.DIYDraw.RecordName, self.data.name, item_name)
	else
		self.node_list["name_text"].text.text = ""
	end
end