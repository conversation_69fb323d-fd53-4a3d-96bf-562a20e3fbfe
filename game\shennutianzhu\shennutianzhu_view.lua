ShenNuTianZhuView = ShenNuTianZhuView or BaseClass(SafeBaseView)

local MAX_MODEL_COUNT = 5
function ShenNuTianZhuView:__init()
    self.view_style = ViewStyle.Half
    self.is_safe_area_adapter = true
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/shennutianzhu_ui_prefab", "layout_shennutianzhu_view")
end

function ShenNuTianZhuView:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ShenNuTianZhuItem, self.node_list.reward_list)
    end

    if not self.ts_hj_list then
        self.ts_hj_list = {}
        for i = 1, MAX_MODEL_COUNT do
            self.ts_hj_list[i] = ShenNuTianZhuModelRender.New(self.node_list["ts_hj_render" .. i])
        end
    end

    XUI.AddClickEventListener(self.node_list.preview_btn, BindTool.Bind(self.OnClickPreViewBtn, self))
    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
end

function ShenNuTianZhuView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.ts_hj_list then
        for k, v in pairs(self.ts_hj_list) do
            v:DeleteMe()
        end

        self.ts_hj_list = nil
    end
end

function ShenNuTianZhuView:OpenCallBack()
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SHENNUTIANZHU, OA_SHENNUTIANZHU_OPERATE_TYPE.INFO)
end

function ShenNuTianZhuView:ShowIndexCallBack()
    ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.ShenNuTianZhuView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SHENNUTIANZHU)
end

function ShenNuTianZhuView:OnFlush()
    local sntz_cfg = ShenNuTianZhuData.Instance:GetShenNuTianZhuCfg()
    if not sntz_cfg then
        return
    end

    local cur_grade = ShenNuTianZhuData.Instance:GetShenNuTianZhuCurGrade()
    cur_grade = cur_grade == -1 and 1 or cur_grade
    local des_bundle, des_asset = ResPath.GetF2RawImagesPNG("a2_sntz_ggcc_" .. cur_grade)
    self.node_list.des_img.raw_image:LoadSprite(des_bundle, des_asset, function()
        self.node_list.des_img.raw_image:SetNativeSize()
    end)

    self.cur_combo_index = sntz_cfg.skill_id
    local is_tianshen = self.cur_combo_index > 0
    self.node_list.preview_btn:SetActive(is_tianshen)
    if is_tianshen then
        local bundle, asset = ResPath.GetSkillIconById(sntz_cfg.skill_icon)
        self.node_list.preview_btn_icon.image:LoadSprite(bundle, asset, function()
            self.node_list.preview_btn_icon.image:SetNativeSize()
        end)

        self.node_list.preview_btn_txt.text.text = sntz_cfg.skill_name
    end

    local price_str = RoleWGData.GetPayMoneyStr(sntz_cfg.price, sntz_cfg.rmb_type, sntz_cfg.rmb_seq)
    self.node_list.price_txt.text.text = price_str
    self.node_list.buy_btn_des.text.text = sntz_cfg.price_desc

    local cap = ShenNuTianZhuData.Instance:GetCurProductCapability()
    self.node_list.zhanli_txt.text.text = cap

    local is_buy = ShenNuTianZhuData.Instance:GetCurProductIsBuy()
    self.node_list.buy_flag:SetActive(is_buy)
    self.node_list.buy_btn.button.interactable = not is_buy

    local reward_list = ShenNuTianZhuData.Instance:GetCurRewardList()
    self.reward_list:SetDataList(reward_list)

    self:FlushModel()
end

function ShenNuTianZhuView:FlushModel()
    local model_list = ShenNuTianZhuData.Instance:GetCurModelList()
    for i = 1, MAX_MODEL_COUNT do
        self.ts_hj_list[i]:SetData(model_list[i])
    end
end

function ShenNuTianZhuView:OnClickPreViewBtn()
    if self.cur_combo_index and self.cur_combo_index > 0 then
        local data = {}
        data.jiban_seq = self.cur_combo_index
        CommonSkillShowCtrl.Instance:SetTianShenHeJiSkillViewDataAndOpen(data)
    end
end

function ShenNuTianZhuView:OnClickBuyBtn()
    local sntz_cfg = ShenNuTianZhuData.Instance:GetShenNuTianZhuCfg()
    if not sntz_cfg then
        return
    end

    RechargeWGCtrl.Instance:Recharge(sntz_cfg.price, sntz_cfg.rmb_type, sntz_cfg.seq)
end

---------ShenNuTianZhuItem--------
ShenNuTianZhuItem = ShenNuTianZhuItem or BaseClass(BaseRender)

function ShenNuTianZhuItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list.item_pos)
end

function ShenNuTianZhuItem:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function ShenNuTianZhuItem:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item_cell:SetData(self.data.item_id)
    if self.data.kuang_icon then
        local bundle, asset = ResPath.GetCommon("a2_ty_wpk_bk_" .. self.data.kuang_icon)
        self.node_list.kuang.image:LoadSprite(bundle, asset, function()
            self.node_list.kuang.image:SetNativeSize()
        end)

        local kuang_pos_y = tonumber(self.data.kuang_icon) > 3 and 4 or 0
        RectTransform.SetAnchoredPositionXY(self.node_list.kuang.rect, 0, kuang_pos_y)
    end
end

---------ShenNuTianZhuModelRender--------
ShenNuTianZhuModelRender = ShenNuTianZhuModelRender or BaseClass(BaseRender)

function ShenNuTianZhuModelRender:LoadCallBack()
    if not self.role_model then
        self.role_model = RoleModel.New()
        self.role_model:SetVisible(true)
        local display_data = {
            parent_node = self.node_list["model_pos"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.role_model:SetRenderTexUI3DModel(display_data)
        -- self.role_model:SetUI3DModel(self.node_list["model_pos"].transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
    end
end

function ShenNuTianZhuModelRender:__delete()
    if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

    self.old_appe_image_id = nil
end

function ShenNuTianZhuModelRender:OnFlush()
    if self.data == nil then
        self.old_appe_image_id = nil
		self.role_model:RemoveAllModel()
        self.node_list.item_root:SetActive(false)
        self.node_list["model_pos"]:SetActive(false)
        return
    end

    local model_cfg
    if self.data.is_tianshen then
        model_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data.model_index)
    else
        model_cfg = ArtifactWGData.Instance:GetArtifactCfgBySeq(self.data.model_index)
    end

    if not model_cfg then
        return
    end

    self.node_list.item_root:SetActive(true)
    self.node_list["model_pos"]:SetActive(true)
    self.node_list.name_txt.text.text = self.data.is_tianshen and model_cfg.bianshen_name or model_cfg.name

    self.node_list.active_state.text.text = self.data.active and Language.ShenNuTianZhu.ActiveTxt or Language.ShenNuTianZhu.NoActiveTxt
    self:FlushModelDisPlay(model_cfg)
    Transform.SetLocalScaleXYZ(self.node_list["model_pos"].transform, self.data.model_scale, self.data.model_scale, self.data.model_scale)
    RectTransform.SetAnchoredPositionXY(self.node_list.item_root.rect, self.data.pos_ui_x , self.data.pos_ui_y)
    RectTransform.SetAnchoredPosition3DXYZ(self.node_list.model_pos.rect, self.data.pos_model_x , self.data.pos_model_y, self.data.pos_model_z)
end

--刷新模型展示
function ShenNuTianZhuModelRender:FlushModelDisPlay(model_cfg)
	local appe_image_id = model_cfg.appe_image_id or model_cfg.model_id
	if not appe_image_id then
		return
	end

	if self.old_appe_image_id == appe_image_id then
		return
	end

    self.role_model:RemoveAllModel()
	self.old_appe_image_id = appe_image_id

    if self.data.is_tianshen then
        self.role_model:SetTianShenModel(appe_image_id, model_cfg.index, true, nil, SceneObjAnimator.Rest)
    else
        local bundle, asset = ResPath.GetMingQiModel(appe_image_id)
	    self.role_model:SetMainAsset(bundle, asset)
	    self.role_model:PlayJianZhenAction()
    end
end