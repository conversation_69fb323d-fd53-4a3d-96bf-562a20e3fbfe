GuildListItem = GuildListItem or BaseClass(BaseRender)

function GuildListItem:__init()
	self.node_list.btn_guild_join.button:AddClickListener(BindTool.Bind(self.OnG<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
end

function GuildListItem:CreateChild()
end

function GuildListItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local member_str = self.data.cur_member_num .. "/" .. self.data.max_member_num
	self.node_list.lbl_rank.text.text = self.index
	local setting_model = GuildDataConst.GUILD_SETTING_MODEL
	self.node_list.lbl_guild_name.text.text = self.data.guild_name
	self.node_list.lbl_mengzhu_name.text.text = self.data.mengzhu_name
	self.node_list.lbl_level.text.text = self.data.guild_level
	self.node_list.lbl_member.text.text = member_str
	-- self.node_list.rank_txt.text.text = ""--self.index
	--战斗力
	if nil ~= setting_model then
		local cap_str = CommonDataManager.ConverExpByThousand(self.data.guild_all_capability)
		self.node_list.lbl_codition.text.text = cap_str
	end

	-- self.node_list.lbl_rank_bg:SetActive(self.index <= 3)
	self.node_list.lbl_rank_img:SetActive(self.index <= 3)
	local img_name = "";
	self.node_list.img9_bg:SetActive(true)
	if self.index <= 3 then
		self.node_list.lbl_rank_img.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..self.index))
		img_name = "a3_ty_list_" .. self.index
		self.node_list["img9_bg"].image:LoadSprite(ResPath.GetCommonImages(img_name))
	else
		self.node_list.img9_bg:SetActive(false)
	end



	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	-- self.node_list.btn_guild_join:CustomSetActive(0 == mainrolevo.guild_id)
	XUI.SetButtonEnabled(self.node_list.btn_guild_join, self.data.has_applying ~= 1)
	-- self.node_list["lbl_guild_zone"]:SetActive(self.data.zone > -1)
	-- if self.data.zone > -1 then
	-- 	self.node_list["lbl_guild_zone"].image:LoadSprite(ResPath.GetF2CommonIcon("xm_zone_"..self.data.zone))
	-- end

    self.node_list["lbl_guild_jingjie"]:SetActive(false)

	if self.data.vip_type > 0 then
		local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
		self.node_list["lbl_guild_vip_level"].text.text = is_hide_vip and "" or "V" .. self.data.vip_type
	end

	self.node_list["lbl_guild_vip_level"]:SetActive(self.data.vip_type > 0)
end

-- 申请入盟事件
function GuildListItem:OnGuildJoinHandler()
	if nil == self.data then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.SelectItem)
		return
	end

	GuildWGCtrl.Instance:SendJoinGuildReq(self.data.guild_id)
end

---------------------------NoGuildListItem--------------------------------------------------------
NoGuildListItem = NoGuildListItem or BaseClass(BaseRender)
function NoGuildListItem:__init()
	self.node_list.btn_guild_join.button:AddClickListener(BindTool.Bind(self.OnGuildJoinHandler, self)) --申请加入
	self.node_list.btn_guild_look.button:AddClickListener(BindTool.Bind(self.LookGuildInfoView, self))

	local is_shield = GuildWGData.Instance:GetIsShieldSeeGuildMember()
	self.node_list.btn_guild_look:SetActive(not is_shield)
end

function NoGuildListItem:CreateChild()
end

function NoGuildListItem:OnFlush()
	if nil == self.data then
		return
	end

	local setting_model = GuildDataConst.GUILD_SETTING_MODEL
	self.node_list.lbl_rank.text.text = self.index --宗门排行
	-- self.node_list.lbl_rank_bg:SetActive(self.index <= 3) --前三名icon
	self.node_list.lbl_rank_img:SetActive(self.index <= 3)
	self.node_list.lbl_guild_name.text.text = self.data.guild_name --宗门名字
	self.node_list.lbl_mengzhu_name.text.text = self.data.mengzhu_name --宗主名字
	self.node_list.lbl_level.text.text = self.data.guild_level --宗门等级
	self.node_list.lbl_member.text.text = self.data.cur_member_num .. "/" .. self.data.max_member_num

	if nil ~= setting_model then
		local cap_str = CommonDataManager.ConverExpByThousand(self.data.guild_all_capability)
		self.node_list.lbl_codition.text.text = cap_str --宗门战斗力
	end

	local img_name = ""
	if self.index <= 3 then
		-- self.node_list.rank_txt.text.text = ""--self.index
		self.node_list.lbl_rank_img.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..self.index))
		img_name = "a3_ty_list_" .. self.index
	else
		img_name = "a3_ty_jd_bg1"-- self.index % 2 == 0 and "a2_zudui_lbdi" or "a2_ty_xxd_5"
	end

	self.node_list["img9_bg"].image:LoadSprite(ResPath.GetCommonImages(img_name))

	local has_applying = self.data.has_applying == 1
	self.node_list.img_apply:SetActive(has_applying)
	self.node_list.btn_guild_join:SetActive(not has_applying)

    self.node_list["lbl_guild_jingjie"]:SetActive(false)

	--VIP等级
	if self.data.vip_type > 0 then
		local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
		self.node_list["lbl_guild_vip_level"].text.text = is_hide_vip and "" or "V" .. self.data.vip_type
	end

	self.node_list["lbl_guild_vip_level"]:SetActive(self.data.vip_type > 0) --大于0才显示
end

-- 申请入盟事件
function NoGuildListItem:OnGuildJoinHandler()
	if self.data.guild_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.FakeGuildLimitJoin)
		return
	end

	if nil == self.data then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.SelectItem)
		return
	end

	GuildWGCtrl.Instance:SendJoinGuildReq(self.data.guild_id)
end

--查看宗门信息
function NoGuildListItem:LookGuildInfoView()
	GuildWGCtrl.Instance:OpenGuildInfoView(self.data)
end

function NoGuildListItem:OnSelectChange(is_select)
	self.node_list.select_bg:CustomSetActive(is_select)
end