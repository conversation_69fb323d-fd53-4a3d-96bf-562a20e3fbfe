BuffTip = BuffTip or BaseClass(SafeBaseView)

local TEXT_SPACING = 10
local MAX_SCROLLVIEW_HEIGHT = 252
local MIN_SCROLLVIEW_HEIGHT = 64
local BG_SPACING = 0
local CELL_SPACING = 3

function BuffTip:__init()
	self.is_maskbg_button_click = true
	self.is_safe_area_adapter = true
	self.text_height = 0
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	if BuffTip.Instance then
		ErrorLog("[BuffTip] Attemp to create a singleton twice !")
	end
	BuffTip.Instance = self
	self:LoadConfig()
end

function BuffTip:__delete()
	BuffTip.Instance = nil
end

function BuffTip:LoadConfig()
    self.view_name = "BuffTip"
	self:AddViewResource(0, "uis/view/main_ui_prefab", "buff_tip")
	self.is_modal = false
end

function BuffTip:ReleaseCallBack()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
	end

	self.cell_list = nil
	self.list_view = nil
end

function BuffTip:LoadCallBack()
	self.cell_list = {}
end

function BuffTip:OpenCallBack()
	-- self.text_height = 0
end

function BuffTip:CloseCallBack()
	self.text_height = 0
end

-- 创建List
function BuffTip:CreateArrListView(cell, data_index)
	 local data_index = data_index + 1

    if nil == self.cell_list[cell] then
        self.cell_list[cell] = BuffItem.New(cell.gameObject)
    end

	self.cell_list[cell]:SetIndex(data_index)
	self.cell_list[cell]:SetDataSum(#self.content_t)
	self.cell_list[cell]:SetData(self.content_t[data_index])
end

function BuffTip:GetNumberOfCells()
    return  #self.content_t
end

function BuffTip:CellSizeDel(data_index)
	self.node_list["TestText"].text.text = self.content_t[data_index + 1].desc or ""
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

	local hight = math.ceil(self.node_list["TestText"].rect.rect.height)
	hight = hight > MIN_SCROLLVIEW_HEIGHT and hight or MIN_SCROLLVIEW_HEIGHT
	self.text_height = self.text_height + hight + CELL_SPACING + TEXT_SPACING
	self:ChangeHeight()
	return hight + (CELL_SPACING or 0)
end

function BuffTip:SetContent()
    local effect_list = FightWGData.Instance:GetMainRoleShowEffect()
	if #effect_list == 0 then
		return
	end

	for k,v in pairs(effect_list) do
		v.desc = FightWGData.Instance:GetEffectDesc(v)
	end

	self.content_t = effect_list
	self:Open()
end

--自适应
function BuffTip:ChangeHeight()
	if self.node_list["ScrollView"] == nil then
		return
	end

	local text_height = 0
	text_height = self.text_height > MAX_SCROLLVIEW_HEIGHT and MAX_SCROLLVIEW_HEIGHT or self.text_height + BG_SPACING
	self.node_list["ScrollView"].rect.sizeDelta = Vector2(self.node_list["ScrollView"].rect.sizeDelta.x, text_height)
end

function BuffTip:ShowIndexCallBack()
	self.list_view = self.node_list["ScrollView"]
	local list_view_delegate = self.list_view.list_simple_delegate
    list_view_delegate.CellSizeDel = BindTool.Bind(self.CellSizeDel, self)
    list_view_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
    list_view_delegate.CellRefreshDel = BindTool.Bind(self.CreateArrListView, self)
	self:Flush()
end

function BuffTip:ReFlush()
	self.text_height = 0
	local effect_list = FightWGData.Instance:GetMainRoleShowEffect()
	if #effect_list == 0 then
		self:Close()
		return
	end

	for k,v in pairs(effect_list) do
		v.desc = FightWGData.Instance:GetEffectDesc(v)
	end
	self.content_t = effect_list

	local list_view_delegate = self.list_view.list_simple_delegate
    list_view_delegate.CellSizeDel = BindTool.Bind(self.CellSizeDel, self)
    list_view_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
    list_view_delegate.CellRefreshDel = BindTool.Bind(self.CreateArrListView, self)
	self:Flush()
end

function BuffTip:OnFlush()
	self:FlushCellView()
end

function BuffTip:FlushCellView()
	-- self.list_view.scroller:RefreshAndReloadActiveCellViews(true)
	-- self.list_view.scroller:RefreshActiveCellViews()
	if self.list_view then
		self.list_view.scroller:ReloadData(0)
	end

	self:ChangeHeight()
end

function BuffTip:FlushRedEvil()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			if v.data.info.client_effect_type == EFFECT_CLIENT_TYPE.ECT_OTHER_HMCF then
				v:Flush()
			end
		end
	end
end


-------------------------------------------------------------------------------
BuffItem = BuffItem or BaseClass(BaseRender)
function BuffItem:__init()
	self.data_sum = 0
end

function BuffItem:__delete()
	if CountDownManager.Instance:HasCountDown(self.timer_type) then
		CountDownManager.Instance:RemoveCountDown(self.timer_type)
		self.timer_type = nil
	end
end

function BuffItem:OnFlush()
    if self.data == nil then return end
	local type_id = self.data.info.client_effect_type
	self.node_list["icon"]:SetActive(true)
	self.node_list["icon"].image:LoadSprite(ResPath.GetBuff(type_id))

	-- self.node_list["line"]:SetActive(self.index ~= self.data_sum)
    -- end
	self.node_list.rule_text.text.text = self.data.desc

	self.timer_type = "buff_count_down" .. self.data.info.unique_key
	if CountDownManager.Instance:HasCountDown(self.timer_type) then
		self.node_list["buff_time"].text.text = ""
		CountDownManager.Instance:RemoveCountDown(self.timer_type)
	end

	if self.data.info.residue_time and self.data.info.residue_time > 0 then
		if self.node_list and self.node_list["buff_time"] ~= nil then
			self.node_list["buff_time"]:CustomSetActive(true)
		end
		--if self.data.info.residue_time > 86400 then		--一天
			local time_str = string.format(Language.Common.BuffCutDown, TimeUtil.FormatSecondDHM3(math.ceil(self.data.info.residue_time)))
			self.node_list["buff_time"].text.text = ToColorStr(time_str,COLOR3B.D_RED)
		--else
		--	self.node_list["buff_time"].text.text = string.format(Language.Common.BuffCutDown, TimeUtil.FormatSecond(math.ceil(self.data.info.residue_time)))
		--end
	else
		if self.node_list and self.node_list["buff_time"] ~= nil then
			if self.data.info.client_effect_type == EFFECT_CLIENT_TYPE.ECT_TIANSHEN_FB_BUFF then
				self.node_list["buff_time"]:CustomSetActive(false)
			else
				self.node_list["buff_time"]:CustomSetActive(true)
			end
		end

		if self.data.info.cd_time and self.data.info.cd_time > 0 then
			if not CountDownManager.Instance:HasCountDown(self.timer_type) then
				self:UpdataNextTime(0,self.data.info.cd_time)
				CountDownManager.Instance:AddCountDown(self.timer_type, BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind(self.CompleteNextTime, self), nil, self.data.info.cd_time, 0)
            end
        else
            self.node_list["buff_time"].text.text = ""
		end
        if self.data.info.client_effect_type == EFFECT_CLIENT_TYPE.ECT_WORLD_BOSS_ROLE_REALIVE then --世界boss复活疲劳
            if self.data.info.merge_layer and self.data.info.merge_layer > 0 then
                self.node_list.merge_layer.text.text = self.data.info.merge_layer
            else
                self.node_list.merge_layer.text.text = ""
            end
        else
            if self.data.info.merge_layer and self.data.info.merge_layer > 1 then
			    self.node_list.merge_layer.text.text = self.data.info.merge_layer
            else
                self.node_list.merge_layer.text.text = ""
            end
        end
    end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["rule_text"].rect)
end

function BuffItem:UpdataNextTime(elapse_time, total_time)
	local on_time = math.ceil(total_time - elapse_time)
	--if on_time > 86400 then		--一天
		on_time = TimeUtil.FormatSecondDHM3(on_time)
	--else
	--	on_time = TimeUtil.FormatSecond(math.ceil(on_time))
	-- end
	if self.node_list and self.node_list["buff_time"] ~= nil then
		self.node_list["buff_time"].text.text = on_time
	end
end

function BuffItem:CompleteNextTime()
	if self.node_list and self.node_list["buff_time"] ~= nil then
		self.node_list["buff_time"].text.text = ""
	end
	CountDownManager.Instance:RemoveCountDown(self.timer_type)
    self.timer_type = nil
	TipWGCtrl.Instance:ReFlushBuffTip()
end

function BuffItem:SetDataSum(data_sum)
	self.data_sum = data_sum
end
