BeastGroupSkillShowView = BeastGroupSkillShowView or BaseClass(CommonSkillShowView)

function BeastGroupSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)
    
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "skill_show_bg_scene")
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_beast_group_skill_show_view")
end

function BeastGroupSkillShowView:LoadCallBack()
	self:InitEnemy()

    self.cur_select_skill_index = -1
    if not self.beast_skill_show_list then
        self.beast_skill_show_list = AsyncListView.New(BeststsShowSkillGroupItemRender, self.node_list["skill_list_view"])
        self.beast_skill_show_list:SetSelectCallBack(BindTool.Bind(self.OnSelectSkillCallBack, self))
        self.beast_skill_show_list:SetLimitSelectFunc(BindTool.Bind(self.IsLimitClick, self))
    end

    self.message_root_tween = self.node_list.skill_desc_root:GetComponent(typeof(UGUITweenPosition))

    self.skill_btn = SkillShowSkillRender.New(self.node_list.skill_btn)
    self.skill_btn:SetNeedChangeSkillBtnPos(false)
    self.skill_btn:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self, self.skill_btn))
    self:TrySetCamera()
    self:ChangeViewDisplay(nil, 18)

    XUI.AddClickEventListener(self.node_list.bag_forward_button, BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, false))
    XUI.AddClickEventListener(self.node_list.bag_reverse_button, BindTool.Bind2(self.PlayBeastsMessagePositionTween, self, true))
end

function BeastGroupSkillShowView:ReleaseCallBack()
    self:CleanSkillBtnCDTimer()
    self:CleanSkillHitCDTimer()
    CommonSkillShowView.ReleaseCallBack(self)

    self.cur_select_skill_index = -1
    self.cur_select_skill_data = nil

    if self.beast_skill_show_list then
        self.beast_skill_show_list:DeleteMe()
        self.beast_skill_show_list = nil
    end

    if self.skill_btn then
        self.skill_btn:DeleteMe()
        self.skill_btn = nil
    end

    self.message_root_tween = nil
end

function BeastGroupSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data
	self:Open()
end

function BeastGroupSkillShowView:ShowIndexCallBack()
    self:ResetCameraFieldOfView()
end

-- 列表选择返回
function BeastGroupSkillShowView:OnSelectSkillCallBack(item)
    if self:IsLimitClick() then
        return
    end

	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    if self.cur_select_skill_index == item.index then
        return
    end

    self.cur_select_skill_index = item.index
    self.cur_select_skill_data = data
    local beast_ids = ControlBeastsWGData.Instance:GetCurBattleGroupSkillBeastIds(data.beast_group_id)
    self:InitShower({beast_ids = beast_ids})

	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(data.group_skill_id)
	local beast_cfg = SkillWGData.Instance:GetBeastsSkillById(data.group_skill_id)
	if client_cfg and beast_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
		self.node_list.skill_nane.text.text = beast_cfg.skill_name
		self.node_list.skill_desc.text.text = client_cfg.description
	end

    -- 刷新技能格子
    self.skill_btn:SetData({skill_id = data.group_skill_id, skill_level = 1})
end


-- 点击技能
function BeastGroupSkillShowView:OnClickSkillBtn(item)
    if not self.is_shower_loaded or not self.is_enemy_loaded then
		return
	end

    if self:IsLimitClick() then
		return
	end

    local data = item:GetData()
    if not data then
        return
    end

    self:CleanSkillBtnCDTimer()
    self:CleanSkillHitCDTimer()

    -- -- 技能文字界面
    local pos = self.node_list["enemy_pos"].transform.position
    self:TryUseBeastSkill(data.skill_id, self.shower_obj, pos.x, pos.y)
    -- 延时2秒播放受击
    self.beast_group_skill_hit_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:AttackHitCallback()
	end, 2)

    -- 技能按钮CD
    local skill_show_time = 5
    self.skill_play_timestemp = Status.NowTime + skill_show_time
    self:SetAllSkillBtnCD(string.format("%.1f", skill_show_time), skill_show_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(skill_show_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), skill_show_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, skill_show_time)
        end
    )
end

function BeastGroupSkillShowView:TryUseBeastSkill(skill_id, deliverer, pos_x, pos_y)
    local cfg = ControlBeastsWGData.Instance:GetGroupSkillDataByGroupSkillID(skill_id)

    if not cfg then
        return
    end

    if deliverer == nil or deliverer:IsDeleted() then
        return
    end

    local deliverer_draw_obj = deliverer:GetDrawObj()
    if deliverer_draw_obj == nil or IsNil(deliverer_draw_obj:GetTransfrom()) then
        return
    end

    local play_pos = deliverer_draw_obj:GetTransfrom().position
    local dir_pos = nil
    if pos_x ~= nil and pos_y ~= nil and pos_x ~= 0 and pos_y ~= 0 then
        local target_x, target_y = GameMapHelper.LogicToWorld(pos_x, pos_y)
        play_pos.x = target_x
        play_pos.z = target_y
        dir_pos = play_pos
    end

    if cfg.is_target == 0 then
        play_pos = deliverer_draw_obj:GetTransfrom().position
    end

    local extra_effect_data = {
        referenceNodeHierarchyPath = "root/hurt_root",
        isAttach = true,
        isRotation = false,
    }

    deliverer:TryPlayNoActorEffect(cfg.skill_bundle, cfg.skill_asset, play_pos, dir_pos, nil, extra_effect_data)
end

-- 技能按钮倒计时
function BeastGroupSkillShowView:CleanSkillBtnCDTimer()
    if self.skill_btn_cd_timer and CountDown.Instance:HasCountDown(self.skill_btn_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.skill_btn_cd_timer)
        self.skill_btn_cd_timer = nil
    end
end

function BeastGroupSkillShowView:SetAllSkillBtnCD(time, total_time)
    if self.skill_btn then
        self.skill_btn:SetSkillBtnCD(time, total_time)
    end
end

function BeastGroupSkillShowView:IsLimitClick(no_tips)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end

-- 延迟播放受击
function BeastGroupSkillShowView:CleanSkillHitCDTimer()
    if self.beast_group_skill_hit_timer then
        GlobalTimerQuest:CancelQuest(self.beast_group_skill_hit_timer)
        self.beast_group_skill_hit_timer = nil
    end
end

function BeastGroupSkillShowView:OnFlush()
    if not self.show_data then
        return
    end

    local group_skill_list = ControlBeastsWGData.Instance:GetCurBattleGroupSkillList()
    if IsEmptyTable(group_skill_list) then
        return
    end

    if self.beast_skill_show_list then
        self.beast_skill_show_list:SetDataList(group_skill_list)
    end

    local jump_index = 1
    local group_skill_id = self.show_data.group_skill_id

    for index, group_skill_data in ipairs(group_skill_list) do
        if group_skill_data.group_skill_id == group_skill_id then
            jump_index = index
        end
    end

    self.beast_skill_show_list:JumpToIndex(jump_index)
end

--- 播放孵化详情动画
function BeastGroupSkillShowView:PlayBeastsMessagePositionTween(is_forward)
    self.node_list.bag_forward_button:CustomSetActive(is_forward)
    self.node_list.bag_reverse_button:CustomSetActive(not is_forward)

    if not IsNil(self.message_root_tween) then
        if is_forward then
            self.message_root_tween:PlayForward()
        else
            self.message_root_tween:PlayReverse()
        end
    end
end

--===================================================================
----------------------------------灵兽组合技能item-----------------------
BeststsShowSkillGroupItemRender = BeststsShowSkillGroupItemRender or BaseClass(BaseRender)
function BeststsShowSkillGroupItemRender:OnFlush()
    if not self.data then
        return
    end

	local client_cfg = SkillWGData.Instance:GetSkillClientConfig(self.data.group_skill_id)
	local beast_cfg = SkillWGData.Instance:GetBeastsSkillById(self.data.group_skill_id)
	if client_cfg and beast_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
		self.node_list.lbl_name.text.text = beast_cfg.skill_name
		self.node_list.desc.text.text = client_cfg.description
	end
end

function BeststsShowSkillGroupItemRender:OnSelectChange(is_select)
	self.node_list.select_bg:CustomSetActive(is_select)
	self.node_list.normal_bg:CustomSetActive(not is_select)
end