EternalNightWGData = EternalNightWGData or BaseClass()

EternalNightWGData.StartJiPingNum = 10

EternalNightWGData.GradeType = {
	Collect = 1,		-- 收集阶段
	Snatch = 2,			--抢夺阶段
	Eliminate = 3,		--决赛阶段
}

EternalNightWGData.EquipShiZhuang = {
	[10] = "fashion_body",
	[11] = "wing_appeid",
	[12] = "fashion_wuqi",
}


EternalNightWGData.DefaultEquipShiZhuang = {
	["fashion_body"] = 4,
	["wing_appeid"] = 8130,
	["fashion_wuqi"] = 1,
}

EternalNightWGData.CurEquipShiZhuang = {
	["fashion_body"] = 4,
	["wing_appeid"] = 8130,
	["fashion_wuqi"] = 1,
}

function EternalNightWGData:__init()
	if EternalNightWGData.Instance ~= nil then
		ErrorLog("[EternalNightWGData] attempt to create singleton twice!")
		return
	end

	EternalNightWGData.Instance = self
	self.eternal_night_cfg = ConfigManager.Instance:GetAutoConfig("eternal_night_auto")

	self.other_cfg = self.eternal_night_cfg.other[1]
	-- self.equip_cfg = ListToMap(self.eternal_night_cfg.equip,"equip_id")
	self.score_reward_cfg = ListToMap(self.eternal_night_cfg.score_reward,"score")
	self.rank_reward_cfg = self.eternal_night_cfg.rank_reward
	self.map_cfg = self.eternal_night_cfg.map
	self.buff_cfg = ListToMap(self.eternal_night_cfg.buff,"buff_type")

	self.scene_info = {}
	self.player_list = {}
	self.rank_list = {}
	self.player_equip_list = {}
	self.self_info = {}
	self.old_self_info = {}
	self.monster_list = {}
	self.origin_appearance = {}		--主角进入场景时的时装信息
	self.sub_room_info_list = {}		--子房间信息列表
	self.live_num = 0
	self.cur_enter_act_type = nil
	self.role_equip_shizhuang_list = {}
	self.scene_info_list = {}

	EternalNightWGData.DefaultEquipShiZhuang["fashion_body"] = self.other_cfg.default_fashion_body
	EternalNightWGData.DefaultEquipShiZhuang["wing_appeid"] = self.other_cfg.default_wing_appeid
	EternalNightWGData.DefaultEquipShiZhuang["fashion_wuqi"] = self.other_cfg.default_fashion_wuqi
	EternalNightWGData.CurEquipShiZhuang["fashion_body"] = EternalNightWGData.DefaultEquipShiZhuang["fashion_body"]
	EternalNightWGData.CurEquipShiZhuang["wing_appeid"] = EternalNightWGData.DefaultEquipShiZhuang["wing_appeid"]
	EternalNightWGData.CurEquipShiZhuang["fashion_wuqi"] = EternalNightWGData.DefaultEquipShiZhuang["fashion_wuqi"]
end

function EternalNightWGData:__delete()
	EternalNightWGData.Instance = nil
end

function EternalNightWGData:SetSCEternalNightInfo(protocol)
	self.scene_info = {}
	self.scene_info.grade = protocol.grade
	self.scene_info.start_time = protocol.start_time
	self.scene_info.start_snatch_time = protocol.start_snatch_time
	self.scene_info.start_finals_time = protocol.start_finals_time
	self.scene_info.kick_out_role_time = protocol.kick_out_role_time
	self.scene_info.rank_top_capability = protocol.rank_top_capability
end


function EternalNightWGData:SetSCEternalNightPlayerInfo(protocol)
	self.player_list = protocol.player_list
	self.rank_list = protocol.rank_list
	self.player_equip_list = protocol.player_equip_list
	table.sort(self.rank_list,function (a,b)
		if a and b then
			return a.rank < b.rank
		end
	end)
end

function EternalNightWGData:SetSCEternalNightEquipUpdate(protocol)
	local uuid_str = protocol.uuid_str
	if not self.player_equip_list[uuid_str] then
		self.player_equip_list[uuid_str] = {}
	end
	self.player_equip_list[uuid_str] = protocol.equip_list
end

function EternalNightWGData:SetSCEternalNightLiveNum(protocol)
	self.live_num = protocol.live_num
end

function EternalNightWGData:SetSCEternalNightSelfInfo(protocol)
	self.self_info = {}
	self.self_info.add_exp = protocol.add_exp
	self.self_info.relive_times = protocol.relive_times
	self.self_info.equip_list = protocol.equip_list
	self.self_info.equip_attr_info = protocol.equip_attr_info
	if IsEmptyTable(self.old_self_info) then
		self.old_self_info.add_exp = self.self_info.add_exp
		self.old_self_info.equip_list = self.self_info.equip_list
		self.old_self_info.equip_attr_info = self.self_info.equip_attr_info
	end
end

function EternalNightWGData:SetSCEternalNightRandomMonsterInfo(protocol)
	self.monster_list = protocol.monster_list
	self.monster_scene_id = protocol.scene_id
end

function EternalNightWGData:SetSCEternalNightEnterSubRoomInfo(protocol)
	self.sub_room_info_list = protocol.sub_room_info_list
end

function EternalNightWGData:SetEternalNightSceneInfo(protocol)
	self.scene_info_list = protocol.scene_info_list
end

function EternalNightWGData:GetEternalNightSceneRoleNum(scene_id)
	return (self.scene_info_list[scene_id] or {}).role_num or 0
end

function EternalNightWGData:GetSceneRoomInfo(scene_id)
	return self.sub_room_info_list and self.sub_room_info_list[scene_id]
end

--设置新旧数据是为了 捡起物品动画播放完了，才刷新数据
function EternalNightWGData:SetNewSelfInfo()
	self.old_self_info = {}
	self.old_self_info.add_exp = self.self_info.add_exp
	self.old_self_info.equip_list = self.self_info.equip_list
	self.old_self_info.equip_attr_info = self.self_info.equip_attr_info
end

function EternalNightWGData:ClearOldSelfInfo()
	self.old_self_info = {}
end

function EternalNightWGData:GetOldSelfInfo()
	return self.old_self_info
end

function EternalNightWGData:GetLiveNum()
	return self.live_num
end

function EternalNightWGData:GetSceneInfo()
	return self.scene_info
end

function EternalNightWGData:GetPlayerList()
	return self.player_list
end

function EternalNightWGData:GetRankList()
	return self.rank_list
end

function EternalNightWGData:GetPlayerEquipList()
	return self.player_equip_list
end

function EternalNightWGData:GetSelfInfo()
	return self.self_info
end

function EternalNightWGData:GetPlayerInfoByUUId(uuid_str)
	return self.player_list[uuid_str]
end

function EternalNightWGData:GetPlayerJiPingEquipByUUId(uuid_str)
	local equip_data = {}
	local info = self:GetPlayerInfoByUUId(uuid_str)
	if info then
		local equip_list = info.equip_list
		for i=0,#equip_list do
			local equip_id = equip_list[i]
			if EternalNightWGData.EquipShiZhuang[i] and equip_id > 0 then
				table.insert(equip_data,equip_id)
			end
		end
	end
	return equip_data
end

function EternalNightWGData:GetSelfJiPingEquipByUUId()
	local equip_data = {}
	local equip_list = self.self_info.equip_list
	if equip_list then
		for i=0,#equip_list do
			local equip_id = equip_list[i]
			if EternalNightWGData.EquipShiZhuang[i] and equip_id > 0 then
				table.insert(equip_data,equip_id)
			end
		end
	end
	return equip_data
end

function EternalNightWGData:GetMonsterList()
	return self.monster_list
end


function EternalNightWGData:GetMonsterSceneId()
	return self.monster_scene_id
end

--获取当前的阶段
function EternalNightWGData:GetCurGradeType()
	return self.scene_info.grade
end

--获取自己的信息
function EternalNightWGData:GetPlayerInfoBySelfId(uuid_str)
	local role_uuid = RoleWGData.Instance:GetUUid()
	local uuid_str = uuid_str or (role_uuid.temp_low .. role_uuid.temp_high)
	local info = self:GetPlayerInfoByUUId(uuid_str)
	return info
end

function EternalNightWGData:GetTotalScoreReward(score)
	local cfg = self.eternal_night_cfg.score_reward
	local total_cfg = {}
	for k,v in ipairs(cfg) do
		if score < v.score then
			total_cfg = v
			return total_cfg
		end
		if k >= #cfg then
			total_cfg = v
		end
	end
	return total_cfg
end

function EternalNightWGData:GetScoreRewardCfg()
	return self.eternal_night_cfg.score_reward or {}
end

function EternalNightWGData:GetRankRewardCfg()
	return self.rank_reward_cfg or {}
end

function EternalNightWGData:GetEquipCfgById(id)
	return ItemWGData.Instance:GetItemConfig(id)
end

function EternalNightWGData:GetRankRewardCfgByRank(rank)
	if self.rank_reward_cfg then
		for k,v in ipairs(self.rank_reward_cfg) do
			if v.min_rank <= rank and v.max_rank >= rank then
				return v
			end
		end
	end
end

function EternalNightWGData:GetSceneRoleObjById(uuid)
	local move_list = Scene.Instance:GetMapRoleInfoList()
	local role_obj = Scene.Instance:GetRoleByUUID(uuid)
	if role_obj and role_obj:GetVo() then
		local move_vo = move_list[role_obj:GetVo().role_id]
		return move_vo,role_obj
	end
end


function EternalNightWGData:SetEquipShiZhuang(appearance,equip_list,uuid)
	local uuid_str = uuid.temp_low .. uuid.temp_high
	if self.role_equip_shizhuang_list[uuid_str] == nil then
		self.role_equip_shizhuang_list[uuid_str] = {}
	end
	local data = self.role_equip_shizhuang_list[uuid_str]
	for k,v in pairs(EternalNightWGData.EquipShiZhuang) do
		if equip_list then
			local equip_id = equip_list[k]
			if equip_id and equip_id > 0 then
				local equip_cfg = self:GetEquipCfgById(equip_id)
				data[v] = equip_cfg[v]
				-- EternalNightWGData.CurEquipShiZhuang[v] = equip_cfg[v]
			else
				-- EternalNightWGData.CurEquipShiZhuang[v] = EternalNightWGData.DefaultEquipShiZhuang[v]
				data[v] = EternalNightWGData.DefaultEquipShiZhuang[v]
			end
		end
	end
	self.role_equip_shizhuang_list[uuid_str] = data
end

function EternalNightWGData:SetSceneEquipShiZhuang(uuid,appearance)
	local uuid_str = uuid.temp_low .. uuid.temp_high
	local player_info = self:GetPlayerInfoByUUId(uuid_str)
	if player_info then
		local equip_list = player_info.equip_list or {}
		self:SetEquipShiZhuang(appearance,equip_list,uuid)
	end
end

function EternalNightWGData:GetRoleEquipShiZhuang(uuid,shizhuang_key)
	local uuid_str = uuid.temp_low .. uuid.temp_high
	local data = self.role_equip_shizhuang_list[uuid_str]
	if data then
		return data[shizhuang_key]
	else
		return EternalNightWGData.DefaultEquipShiZhuang[shizhuang_key]
	end
end

function EternalNightWGData:GetOtherCfg()
	return self.other_cfg
end

function EternalNightWGData:GetSelfEquipInfo(equip_part)
	local equip_list = self.self_info.equip_list
	if equip_list then
		local equip_id = equip_list[equip_part] or 0
		local cfg = self:GetEquipCfgById(equip_id)
		return cfg
	end
end

--设置主角进入场景时初始时装信息
function EternalNightWGData:SetRoleOriginAppearance(appearance)
	if IsEmptyTable(self.origin_appearance) then
		for k,v in pairs(EternalNightWGData.EquipShiZhuang) do
			self.origin_appearance[v] = appearance[v]
		end
	end
end

--复原主角的时装信息
function EternalNightWGData:RestRoleOriginAppearance()
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		local main_role_vo = main_role:GetVo()
		if not IsEmptyTable(self.origin_appearance) then
			for k,v in pairs(EternalNightWGData.EquipShiZhuang) do
				main_role_vo.appearance[v] = self.origin_appearance[v]
				RoleWGData.Instance:SetAttr("appearance", main_role_vo.appearance)
			end
		end		
	end
end


function EternalNightWGData:SetDropEquipData(drop_equip_id,name)
	-- self.drop_equip_id = drop_equip_id
	self.drop_equip_data = {}
	self.drop_equip_data.equip_id = drop_equip_id
	self.drop_equip_data.name = name
end

function EternalNightWGData:GetDropEquipData()
	return self.drop_equip_data
end

function EternalNightWGData:ClearDropEquipData()
	self.drop_equip_data = {}
end

local EternalNightAttrZhanLiXS = {
	max_hp = "shengming_zhanli_xs",							-- 最大生命
	gong_ji = "gongji_zhanli_xs",						-- 攻击
	fang_yu = "fangyu_zhanli_xs",						-- 防御
	po_jia = "pojia_zhanli_xs",							-- 破甲
	yuansu_shanghai = "yuansu_shanghai_zhanli_xs",				-- 元素伤害
	yuansu_hujia = "yuansu_hj_zhanli_xs",					-- 元素护甲
    baoji_shanghai = "baojishanghai_zhanli_xs",					--暴击伤害固定值
    kangbao_shanghai = "kangbaoshanghai_zhanli_xs",				--抗暴伤害固定值
}

function EternalNightWGData:GetAttrValueXS(name)
	local cap_param = CommonDataManager.GetParamCfg()
	local attr_xs = EternalNightAttrZhanLiXS[name]
	local xs_value = 1
	if attr_xs then
		xs_value = cap_param[attr_xs] or 1
		-- xs_value = xs_value
	end
	return xs_value
end

function EternalNightWGData:GetInfoLogMsg()

end

function EternalNightWGData:GetMapCfgByIndex(index)
	return self.map_cfg[index]
end

function EternalNightWGData:GetMapCfgBySceneId(scene_id)
	for k,v in pairs(self.map_cfg) do
		if v.scene_id == scene_id then
			return v
		end
	end
end

function EternalNightWGData:SetCurEnterActType(act_type)
	self.cur_enter_act_type = act_type
end

function EternalNightWGData:GetCurEnterActType()
	return self.cur_enter_act_type
end

function EternalNightWGData:SetGoToBossFun(fun)
	self.goto_boss_fun = fun
end

function EternalNightWGData:GetGoToBossFun()
	return self.goto_boss_fun
end

--设置需要追杀的角色id
function EternalNightWGData:SetGoByRoleUUid(uuid)
	self.go_by_role_uuid = uuid
end

function EternalNightWGData:GetGoByRoleUUid()
	return self.go_by_role_uuid
end

function EternalNightWGData:GetSceneBuffTypeCfg(buff_type)
	local cfg = self.buff_cfg[buff_type]	
	return cfg
end

function EternalNightWGData:GetCanChangeGhost(uuid_str)
	local player_info = EternalNightWGData.Instance:GetPlayerInfoBySelfId(uuid_str)
	local relive_times = player_info and player_info.relive_times or 0
	local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
	local finals_max_relive_times = other_cfg.finals_max_relive_times

	return relive_times >= finals_max_relive_times
end

function EternalNightWGData:GetEliminateCanFuHuo()
	local player_info = EternalNightWGData.Instance:GetPlayerInfoBySelfId()
	local relive_times = player_info and player_info.relive_times or 0
	local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
	local finals_max_relive_times = other_cfg.finals_max_relive_times
	return relive_times < finals_max_relive_times
end

function EternalNightWGData:GetEliminateReliveTimes()
	local player_info = EternalNightWGData.Instance:GetPlayerInfoBySelfId()
	local relive_times = player_info and player_info.relive_times or 0
	local other_cfg = EternalNightWGData.Instance:GetOtherCfg()
	local finals_max_relive_times = other_cfg.finals_max_relive_times
	relive_times = finals_max_relive_times - relive_times
	return relive_times
end
