ShenShouExtraZhuZhanTip = ShenShouExtraZhuZhanTip or BaseClass(SafeBaseView)

function ShenShouExtraZhuZhanTip:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(600, 386)})
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_extra_msg")
	self:SetMaskBg(true)
end

function ShenShouExtraZhuZhanTip:ReleaseCallBack()
	if self.stuff_cell then
		self.stuff_cell:DeleteMe()
		self.stuff_cell = nil
	end
end

function ShenShouExtraZhuZhanTip:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.FuBenPanelBuyCountInfo
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnClink<PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	-- self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.OnClinkClose, self))

	self.stuff_cell = ItemCell.New(self.node_list.ph_stuff_cell)
end


function ShenShouExtraZhuZhanTip:SetData(data, callback)
	self.data = data
	self.callback = callback
	self:Open()
end

function ShenShouExtraZhuZhanTip:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(self.data.soul_ring_seq)
	
	if not IsEmptyTable(cfg) then
		local cost_str = ""

		if cfg.consume_lingyu > 0 then
			cost_str = string.format(Language.ShenShou.SoulRingUnlockMoney, cfg.consume_lingyu)
		end

		local need_stuff = false
		if cfg.stuff_id > 0 and cfg.stuff_num > 0 then
			need_stuff = true
			local has_num = ItemWGData.Instance:GetItemNumInBagById(cfg.stuff_id)
			local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.stuff_id)
			local item_str = string.format(Language.ShenShou.SoulRingUnlockItem, ITEM_COLOR[item_cfg.color], item_cfg.name, cfg.stuff_num)
			cost_str = cost_str .. "," .. item_str

			self.stuff_cell:SetFlushCallBack(function ()
				local string_num= string.format((has_num .. "/" .. cfg.stuff_num), has_num >= cfg.stuff_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
				self.stuff_cell:SetRightBottomColorText(string_num)
				self.stuff_cell:SetRightBottomTextVisible(true)
			end)
	
			self.stuff_cell:SetData({item_id = cfg.stuff_id})
		end

		self.node_list.rich_des_1.tmp.text = string.format(Language.ShenShou.SoulRingUnlockCost, cost_str, cfg.soul_ring_name)

		if need_stuff then
			self.node_list.ph_stuff_cell:CustomSetActive(true)
			self.node_list.rich_des_1.rect.localPosition = Vector2(0, 64)
		else
			self.node_list.ph_stuff_cell:CustomSetActive(false)
			self.node_list.rich_des_1.rect.localPosition = Vector2(0, 0)
		end

		-- EmojiTextUtil.ParseRichText(self.node_list.rich_des_1.emoji_text, string.format(Language.ShenShou.SoulRingUnlockCost, cost_str, cfg.soul_ring_name), 22)
	end

	-- local extra_num_cfg = ConfigManager.Instance:GetAutoConfig("shenshou_cfg_new_auto").extra_num_cfg
	-- local has_weizi_num = ShenShouWGData.Instance:GetCurOpenZhuZhanCount()
	-- local extra_zhuzhan_count = has_weizi_num
	-- local next_count = self.data - DEFAULT_CHU_ZHAN_SLOT_NUM
	-- local num_cfg = ShenShouWGData.Instance:GetExtraNumCfg(next_count)
	-- if IsEmptyTable(num_cfg) then
	-- 	return
	-- end

	-- self.level_limit_tip = nil
	-- local role_level = RoleWGData.Instance:GetAttr('level')
	-- if role_level < num_cfg.open_level then
	-- 	self.level_limit_tip = RoleWGData.GetLevelString(num_cfg.open_level) .. Language.MiJing.LevelOpen
	-- end

	-- local item_num = ItemWGData.Instance:GetItemNumInBagById(num_cfg.stuff_id)
	-- local string_num= string.format((item_num .. "/" .. num_cfg.stuff_num) ,item_num >= num_cfg.stuff_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
	-- local stuff_name = ItemWGData.Instance:GetItemName(num_cfg.stuff_id)
	-- local str = string.format(Language.ShenShou.RichDes1, num_cfg.stuff_num, stuff_name, extra_zhuzhan_count + 1)
	-- self.node_list["rich_des_1"].tmp.text = str

	-- self.has_enough_stuff = item_num >= num_cfg.stuff_num
	-- self.stuff_cell:SetFlushCallBack(function ()
	-- 		self.stuff_cell:SetRightBottomColorText(string_num)
	-- 		self.stuff_cell:SetRightBottomTextVisible(true)
	-- 	end)

	-- self.stuff_cell:SetData({item_id = num_cfg.stuff_id, is_bind = 1})

	-- local zhuzhan_num = ShenShouWGData.Instance:GetZhuZhanNum()
	-- self.node_list["rich_des_2"].tmp.text = string.format(Language.ShenShou.ExtraTip, zhuzhan_num , has_weizi_num)
end

function ShenShouExtraZhuZhanTip:OnClinkOkHandler()
	if IsEmptyTable(self.data) then
		return
	end

	local cfg = ShenShouWGData.Instance:GetSoulRingCfgBySoulRingSeq(self.data.soul_ring_seq)
	
	if not IsEmptyTable(cfg) then
		if cfg.consume_lingyu > 0 then
			if not RoleWGData.Instance:GetIsEnoughAllGold(cfg.consume_lingyu) then
                VipWGCtrl.Instance:OpenTipNoGold()
				self:Close()
				return
            end
		end

		if cfg.stuff_id > 0 and cfg.stuff_num > 0 then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(cfg.stuff_id)

			if has_num < cfg.stuff_num then
				TipWGCtrl.Instance:OpenItem({item_id = cfg.stuff_id})
				self:Close()
				return
			end
		end
	end
	
	ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ADD_ZHUZHAN, cfg.soul_ring_seq)

	-- if self.level_limit_tip then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(self.level_limit_tip)
	-- else
	-- 	if self.callback and self.has_enough_stuff then
	-- 		self.callback(true)
	-- 	end

	-- 	local next_count = self.data - DEFAULT_CHU_ZHAN_SLOT_NUM
	-- 	local num_cfg = ShenShouWGData.Instance:GetExtraNumCfg(next_count)
	-- 	if num_cfg then
	-- 		local item_num = ItemWGData.Instance:GetItemNumInBagById(num_cfg.stuff_id)
	-- 		if num_cfg.stuff_num > item_num then
	-- 			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = num_cfg.stuff_id, is_bind = 1})
	-- 			self:Close()
	-- 			return
	-- 		end
	-- 	end

	-- 	ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_ADD_ZHUZHAN, self.data)
	-- end

	self:Close()
end

-- function ShenShouExtraZhuZhanTip:OnClinkClose()
-- 	self:Close()
-- end
