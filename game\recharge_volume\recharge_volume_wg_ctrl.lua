require("game/recharge_volume/recharge_volume_view")
require("game/recharge_volume/recharge_volume_wg_data")
--require("game/recharge_volume/recharge_volume_exchange_view")
require("game/recharge_volume/recharge_volume_reward_view")
require("game/recharge_volume/recharge_volume_limit_view")
require("game/recharge_volume/goddess_blessing_view")


RechargeVolumeWGCtrl = RechargeVolumeWGCtrl or BaseClass(BaseWGCtrl)

function RechargeVolumeWGCtrl:__init()
	if RechargeVolumeWGCtrl.Instance then
		ErrorLog("[RechargeVolumeWGCtrl] attempt to create singleton twice!")
		return
	end

	RechargeVolumeWGCtrl.Instance = self
	self.data = RechargeVolumeWGData.New()
    self.view = RechargeVolumeView.New(GuideModuleName.RechargeVolumeView)
	self.recharge_volume_limit_view = RechargeVolumeLimitView.New(GuideModuleName.RechargeVolumeLimitView)
	self.goddess_blessing_view = GoddessBlessingView.New(GuideModuleName.GoddessBlessingView)
	self.recharge_volume_reward_view = RechargeVolumeRewardView.New(GuideModuleName.RechargeVolumeRewardView)

	self:RegisterAllProtocols()

	self.recharge_volume_change = BindTool.Bind1(self.FlushRechargeVolume, self)
	RoleWGData.Instance:NotifyAttrChange(self.recharge_volume_change, {"recharge_volume"})
	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
	--self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.OnDayChange, self))
	-- self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
end

-- function RechargeVolumeWGCtrl:MainuiOpenCreateCallBack()
-- 	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
-- 	local year, month, day = TimeUtil.FormatSecond5MYHM1(TimeWGCtrl.Instance:GetServerTime())
-- 	local flag = PlayerPrefsUtil.GetInt("RechargeVolumeWGCtrl" .. main_role_id .. year .. month .. day)
-- 	self.data:SetDailyOpenFlag(flag == 1)
-- end

function RechargeVolumeWGCtrl:__delete()
	RechargeVolumeWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.goddess_blessing_view then
		self.goddess_blessing_view:DeleteMe()
		self.goddess_blessing_view = nil
	end

	if self.recharge_volume_limit_view then
		self.recharge_volume_limit_view:DeleteMe()
		self.recharge_volume_limit_view = nil
	end

	if self.recharge_volume_reward_view then
		self.recharge_volume_reward_view:DeleteMe()
		self.recharge_volume_reward_view = nil
	end

	if self.recharge_volume_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.recharge_volume_change)
		self.recharge_volume_change = nil
	end

	if self.open_fun_change then
        GlobalEventSystem:UnBind(self.open_fun_change)
        self.open_fun_change = nil
    end
end

function RechargeVolumeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSCumulateRechargeGiftReq)
    self:RegisterProtocol(SCCumulateRechargeGiftInfo, "OnRecvCumulateGiftInfo")
end

-- 操作请求
-- CUMULATE_RECHARGE_OPERATE_TYPE.FETCH
function RechargeVolumeWGCtrl:SendOperateReq(operate_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCumulateRechargeGiftReq)
	protocol.operate_type = operate_type or 0
	protocol:EncodeAndSend()
end


function RechargeVolumeWGCtrl:OnRecvCumulateGiftInfo(protocol)
	-- body
	self.data:SetAllCumulateGiftInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush()
	end

	self:FlushVolumeRewardView()
    RemindManager.Instance:Fire(RemindName.CumulateRechargeGift)
end

function RechargeVolumeWGCtrl:FlushRechargeVolume()
	if self.view:IsOpen() then
		self.view:Flush()
	end

	self:FlushVolumeLimitView()
	self:FlushGoddessBlessingView()
	RemindManager.Instance:Fire(RemindName.RechargeVolumeCanUse)
	RemindManager.Instance:Fire(RemindName.PrivilegeCollection_NSTQ)
end

function RechargeVolumeWGCtrl:FlushRechargeVolumeView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function RechargeVolumeWGCtrl:PlayRechargeVolumeEffect()
	if self.view:IsOpen() then
		self.view:PlayRechargeVolumeEffect()
	end
end

function RechargeVolumeWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.RechargeVolumeView then 
        local is_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeVolumeView)
        local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        -- ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.CHONGZHIJUAN, state)
    end
end

function RechargeVolumeWGCtrl:OpenVolumeLimitView()
	if not self.recharge_volume_limit_view:IsOpen() then
		self.recharge_volume_limit_view:Open()
	else
		self.recharge_volume_limit_view:Flush()
	end
end

function RechargeVolumeWGCtrl:OpenVolumeRewardView()
	if not self.recharge_volume_reward_view:IsOpen() then
		self.recharge_volume_reward_view:Open()
	else
		self.recharge_volume_reward_view:Flush()
	end
end

function RechargeVolumeWGCtrl:FlushVolumeLimitView()
	if self.recharge_volume_limit_view:IsOpen() then
		self.recharge_volume_limit_view:Flush()
	end
end

function RechargeVolumeWGCtrl:FlushVolumeRewardView()
	if self.recharge_volume_reward_view:IsOpen() then
		self.recharge_volume_reward_view:Flush()
	end
end

function RechargeVolumeWGCtrl:FlushGoddessBlessingView()
	if self.goddess_blessing_view:IsOpen() then
		self.goddess_blessing_view:Flush()
	end
end

function RechargeVolumeWGCtrl:OpenGoddessBlessingView()
	self.goddess_blessing_view:Open()
end

function RechargeVolumeWGCtrl:SetGoddessBlessingViewJumpFlag()
	if self.goddess_blessing_view:IsOpen() then
		self.goddess_blessing_view:SetJumpFlag()
	end
end

function RechargeVolumeWGCtrl:SetVolumeMainViewTitleState(bool)
	if self.view:IsOpen() then
		self.view:SetTitleState(bool)
	end
end

function RechargeVolumeWGCtrl:OpenAccountTipContent()
	if self.view:IsOpen() then
		self.view:OpenAccountTipContent()
	end
end