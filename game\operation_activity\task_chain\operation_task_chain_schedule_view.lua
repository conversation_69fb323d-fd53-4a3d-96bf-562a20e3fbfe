OperationTaskChainScheduleView = OperationTaskChainScheduleView or BaseClass(SafeBaseView)

local STANDY_TIMER = 13

function OperationTaskChainScheduleView:__init()
	self.view_layer = UiLayer.MainUILow
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_task_schedule_view")
end

function OperationTaskChainScheduleView:ReleaseCallBack()
	self:CheckTimer()

	if self.do_tween ~= nil then
		self.do_tween:Kill()
		self.do_tween = nil
	end

	if self.tip_timer then
		GlobalTimerQuest:CancelQuest(self.tip_timer)
		self.tip_timer = nil
	end

	if CountDown.Instance:HasCountDown("task_chain_schedule_common") then
		CountDown.Instance:RemoveCountDown("task_chain_schedule_common")
	end
	
    if self.task_chain_task_list ~= nil then
    	for k,v in pairs(self.task_chain_task_list) do
    		if v then
    			v:DeleteMe()
    		end
    	end

    	self.task_chain_task_list = nil
    end

   	if self.follow_event then
		GlobalEventSystem:UnBind(self.follow_event)
		self.follow_event = nil
	end

	self.tip_start_pos = nil
end

function OperationTaskChainScheduleView:LoadCallBack()
	self.node_list.btn_follow.button:AddClickListener(BindTool.Bind1(self.OnClickFollow, self))
	self.tip_start_pos = self.node_list.root_tip.gameObject.transform.localPosition
end

function OperationTaskChainScheduleView:OnClickFollow()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.CROSS_TASK_CHAIN_HUSONG then
		local info = OperationTaskChainWGData.Instance:GetHuSongInfo()
		if info ~= nil and info.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.FIGHT then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.TaskChain.HasMonster)
			return
		end
	end

	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function OperationTaskChainScheduleView:OpenCallBack()
end

function OperationTaskChainScheduleView:CloseCallBack()
	if CountDown.Instance:HasCountDown("task_chain_schedule_common") then
		CountDown.Instance:RemoveCountDown("task_chain_schedule_common")
	end

	self:CheckTimer()

   	if self.follow_event then
		GlobalEventSystem:UnBind(self.follow_event)
		self.follow_event = nil
	end

	self:ResetTween()
end

function OperationTaskChainScheduleView:OnFlush(param_t)
	self:CheckTimer()

	for k,v in pairs(param_t) do
		if k == "all" then
			self.node_list.root_schedule:SetActive(false)
			self.node_list.root_common:SetActive(false)
			self.node_list.root_follow:SetActive(false)
			self.node_list.root_guard:SetActive(false)
			self.node_list.root_nor_tip:SetActive(false)

			local scene_type = Scene.Instance:GetSceneType()
			if scene_type == SceneType.CROSS_TASK_CHAIN then
				self:FlushScheduleInfo()
				self:FlushScheduleTip(SceneType.CROSS_TASK_CHAIN)
				self:ShowReadyDown(SceneType.CROSS_TASK_CHAIN)
			elseif scene_type == SceneType.CROSS_TASK_CHAIN_HUSONG then
				self:FlushFollowInfo()
				self:FlushCommonInfo(OperationTaskChainWGData.Instance:GetHuSongTip())
			elseif scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
				local cfg = OperationTaskChainWGData.Instance:GetXunLuoCaiJiDungeonInfo()
				if cfg ~= nil then
					self:FlushCommonInfo(cfg.tip)
				end
			elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
				local cfg = OperationTaskChainWGData.Instance:GetCaiJiDungeonInfo()
				local special_flag = OperationTaskChainWGData.Instance:GetCaiJiSpecialGatherstatus()
				if cfg ~= nil then
					local str = cfg.tip
					if special_flag ~= nil and special_flag == 1 then
						str = cfg.tip_2
					end

					self:FlushCommonInfo(str)
				end
			elseif scene_type == SceneType.CROSS_TASK_CHAIN_AVOID then
				local info = OperationTaskChainWGData.Instance:GetSixteenCellRoundInfo()
				if info ~= nil then
					if info.status == OPERATION_TASK_CHAIN_SIXTEEN_CELL_STATUS.WARN then
						local cfg = OperationTaskChainWGData.Instance:GetSixteenCellDungeonInfo()
						if cfg ~= nil then
							self:FlushCommonInfo(cfg.tip, info.status_end_timestamp, cfg.warning)
						end
					end
				end
			elseif scene_type == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID then
				local str = OperationTaskChainWGData.Instance:GetYunSongTip()
				if str ~= nil then
					self:FlushCommonInfo(str)
				end
			elseif scene_type == SceneType.CROSS_TASK_CHAIN_GUARD then
				local info = OperationTaskChainWGData.Instance:GetGuardWaveInfo()
				local cfg = OperationTaskChainWGData.Instance:GetGuardDungeonInfo()
				if info ~= nil and cfg ~= nil then
					if info.status == OPERATION_TASK_CHAIN_WAVE_STATUS.WARN then
						local time_str = "%s" .. cfg.tip3
						self:FlushCommonInfo("", info.status_end_timestamp, time_str, OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE.TIP)
					end

					local wave_str = string.format(Language.OpertionAcitvity.TaskChain.WaveStr, info.wave, cfg.monster_wave)
					self:FlushGuardInfo(wave_str)
				end
			end
		elseif k == "check_follow_btn" then
			local scene_type = Scene.Instance:GetSceneType()
			if scene_type == SceneType.CROSS_TASK_CHAIN_HUSONG then
				self.node_list.root_follow:SetActive(not v.is_follow)
			else
			   	if self.follow_event then
					GlobalEventSystem:UnBind(self.follow_event)
					self.follow_event = nil
				end				
			end
		elseif k == "show_tip" then
			self:ShowTip(v)
		elseif k == "common_slider" then
			if CountDown.Instance:HasCountDown("task_chain_schedule_common") then
				CountDown.Instance:RemoveCountDown("task_chain_schedule_common")
			end

			if v ~= nil and v.time ~= nil and v.time > 0 then
				self:FlushCommonInfo("", v.time, v.slider_str or "", v.time_type or OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE.SLIDER)
			end
		elseif k == "exp_change" then
			if v ~= nil and v.add_exp ~= nil then
				local str = OperationTaskChainWGData.Instance:GetExpTip(v.add_exp)
				self:FlushScheduleTip(SceneType.CROSS_TASK_CHAIN, str)
			end
		end
	end
end

function OperationTaskChainScheduleView:FlushScheduleTip(scene_type, tip)
	local str = tip or ""
	if scene_type ~= nil and tip == nil then
		if scene_type == SceneType.CROSS_TASK_CHAIN then
			str = OperationTaskChainWGData.Instance:GetExpTip()
		end
	end

	self.node_list.str_schedule_tip.text.text = str or ""
end

function OperationTaskChainScheduleView:FlushNorTip(tip)
	self.node_list.root_nor_tip:SetActive(true)
	self.node_list.text_tip.text.text = tip or ""
end

function OperationTaskChainScheduleView:FlushScheduleInfo()
	self.node_list.root_schedule:SetActive(true)
	local is_need_load = false
	if self.task_chain_task_list == nil then
		is_need_load = true
	end

	local show_list = OperationTaskChainWGData.Instance:GetScheduleTaskInfo()
	if show_list == nil or #show_list == 0 then
		return
	end

	local show_num = #show_list
	if self.task_chain_task_list ~= nil and show_num > #self.task_chain_task_list then
		is_need_load = true
	end

	if is_need_load then
		local asset,bundle = ResPath.GetOperationTaskChainPrefabF2("render_task_chain_scene_task_schedule")
		local res_async_loader = AllocResAsyncLoader(self, "render_task_chain_schedule")
		res_async_loader:Load(asset, bundle, nil,
			function(new_obj)
				if nil == new_obj then
					return 
				end

				local cur_num = 1
				if self.task_chain_task_list ~= nil then
					cur_num = show_num - #self.task_chain_task_list
				else
					self.task_chain_task_list = {}
				end

				for i = cur_num, show_num do
					local obj = ResMgr:Instantiate(new_obj)
					local obj_transform = obj.transform
					obj_transform:SetParent(self.node_list.root_task.transform, false)
					local item_render = TaskChainScheduleInfoRender.New(obj)
					table.insert(self.task_chain_task_list, item_render)
				end

				for k,v in pairs(self.task_chain_task_list) do
					v:SetData(show_list[k])
				end
			end)
	else
		if self.task_chain_task_list == nil then
			return
		end

		for i = 1, show_num do
			if self.task_chain_task_list[i] ~= nil then
				self.task_chain_task_list[i]:SetActive(true)
				self.task_chain_task_list[i]:SetData(show_list[i])
			end
		end

		if show_num < #self.task_chain_task_list then
			for i = show_num, #self.task_chain_task_list do
				if self.task_chain_task_list[i] ~= nil then
					self.task_chain_task_list[i]:SetActive(false)
				end
			end
		end
	end
end

function OperationTaskChainScheduleView:FlushCommonInfo(str, time, time_str, timer_type)
	if CountDown.Instance:HasCountDown("task_chain_schedule_common") then
		CountDown.Instance:RemoveCountDown("task_chain_schedule_common")
	end

	timer_type = timer_type or OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE.SLIDER
	self.node_list.root_common:SetActive(true)
	self.node_list.str_common_tip.text.text = str or ""
	self.node_list.tip_root:SetActive(str ~= "")
	if time == nil then
		self.node_list.root_slider:SetActive(false)
		self.node_list.slider.image.fillAmount = 0

		self.node_list.slider_left_root:SetActive(false)
		self.node_list.slider_left.image.fillAmount = 0
	else
		local count = math.floor(time - TimeWGCtrl.Instance:GetServerTime())
		if count > 0 then
			if timer_type == OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE.SLIDER then	
				self.node_list.root_slider:SetActive(true)
				self.node_list.slider.image.fillAmount = 0					
				self.node_list.str_slider.text.text = time_str

				self.node_list.slider_left_root:SetActive(false)
				self.node_list.slider_left.image.fillAmount = 0
			elseif timer_type == OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE.LEFT_SLIDER then
				self.node_list.slider_left_root:SetActive(true)
				self.node_list.slider_left.image.fillAmount = 0

				self.node_list.root_slider:SetActive(false)
				self.node_list.slider.image.fillAmount = 0				
			else
				self.node_list.root_slider:SetActive(false)
				self.node_list.slider.image.fillAmount = 0

				self.node_list.slider_left_root:SetActive(false)
				self.node_list.slider_left.image.fillAmount = 0
			end

			CountDownManager.Instance:AddCountDown("task_chain_schedule_common", BindTool.Bind(self.FlushTaskChainCommonTime, self, count, timer_type, time_str), BindTool.Bind(self.CompleteCommon, self), nil, count, 0.1)
		else
			self.node_list.root_common:SetActive(false)
			return
		end
	end
end

function OperationTaskChainScheduleView:CompleteCommon()
	if self.node_list.root_common then
		self.node_list.root_common:SetActive(false)
	end
end

function OperationTaskChainScheduleView:FlushTaskChainCommonTime(time, timer_type, time_str, elapse_time, total_time)
	if timer_type == OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE.SLIDER then
		if self.node_list.slider ~= nil and time ~= nil and time ~= 0 and time ~= "" then
			self.node_list.slider.image.fillAmount = elapse_time / time
		end
	elseif timer_type == OPERATION_TASK_CHAIN_SCHEDULE_TIMER_TYPE.LEFT_SLIDER then
		if self.node_list.slider_left ~= nil and time ~= nil and time ~= 0 and time ~= "" then
			self.node_list.slider_left.image.fillAmount = elapse_time / time
		end		
	else
		if self.node_list.str_common_tip ~= nil and time_str ~= nil then
			local value = math.floor(total_time - elapse_time)
			self.node_list.str_common_tip.text.text = string.format(time_str, value)
		end
	end
end

function OperationTaskChainScheduleView:FlushFollowInfo()
	self.node_list.root_follow:SetActive(true)

	if self.follow_event == nil then
		self.follow_event = GlobalEventSystem:Bind(ObjectEventType.ROLE_FOLLOW_STATUS_CHANGE, BindTool.Bind(self.FlushFollowBtn, self))
	end
end

function OperationTaskChainScheduleView:ShowReadyDown(scene_type)
	self:CheckTimer()

	local time = nil
	local show_type = FB_START_DOWN_END_TYPE.TASK
	local info = OperationTaskChainWGData.Instance:GetTaskChainActInfo()
	if info ~= nil and info.task_idx == -1 then
		time = math.floor(info.cur_task_status_end_timestamp - TimeWGCtrl.Instance:GetServerTime())
	elseif info ~= nil and info.task_idx ~= -1 and info.cur_task_status == OPERATION_TASK_CHAIN_ACT_STATUS.STANDY then
		time = math.floor(info.cur_task_status_end_timestamp - TimeWGCtrl.Instance:GetServerTime())
	end

	if time ~= nil and time > 0 then
		if time > STANDY_TIMER then
			self.guard_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.AddSceneCountDown, self, show_type), time - STANDY_TIMER)
		else
			self:AddSceneCountDown(show_type)
		end
	end
end

function OperationTaskChainScheduleView:AddSceneCountDown(show_type)
	UiInstanceMgr.Instance:DoFBStartDown(STANDY_TIMER + TimeWGCtrl.Instance:GetServerTime(), nil, nil, nil, show_type)
end

function OperationTaskChainScheduleView:FlushGuardInfo(str, under_str)
	self.node_list.str_guard_tip.text.text = str or ""
	self.node_list.str_guard_tip_under.text.text = under_str or ""
	self.node_list.root_guard:SetActive(true)	
end

function OperationTaskChainScheduleView:ShowCountDown()
	if CountDownManager.Instance:HasCountDown("task_chain_schedule_guard") then
		CountDownManager.Instance:RemoveCountDown("task_chain_schedule_guard")
	end

	CountDownManager.Instance:AddCountDown("task_chain_schedule_guard", BindTool.Bind1(self.FlushTaskChainGuardTime, self), BindTool.Bind1(self.CompleteGuard, self), nil, STANDY_TIMER, 1)
end

function OperationTaskChainScheduleView:FlushTaskChainGuardTime(elapse_time, total_time)
	if self.node_list.str_guard_tip ~= nil then
		local value = math.floor(total_time - elapse_time)
		local str = TimeUtil.FormatSecondDHM2(value)
		self.node_list.str_guard_tip.text.text = str
		local under_str = ""
		if value == 0 then
			under_str = Language.OpertionAcitvity.TaskChain.ActOpen
		end
		self.node_list.str_guard_tip_under.text.text = under_str
	end
end

function OperationTaskChainScheduleView:CompleteGuard()
	self.node_list.root_common:SetActive(false)
	self:CheckTimer()
end

function OperationTaskChainScheduleView:CheckTimer()
	if self.guard_timer then
		GlobalTimerQuest:CancelQuest(self.guard_timer)
		self.guard_timer = nil
	end

	if CountDownManager.Instance:HasCountDown("task_chain_schedule_guard") then
		CountDownManager.Instance:RemoveCountDown("task_chain_schedule_guard")
	end
end

function OperationTaskChainScheduleView:FlushFollowBtn(is_follow)
	self:Flush(nil, "check_follow_btn", {is_follow = is_follow})
end

function OperationTaskChainScheduleView:ResetTween()
	if self.tip_timer then
		GlobalTimerQuest:CancelQuest(self.tip_timer)
		self.tip_timer = nil
	end

	if self.do_tween ~= nil then
		self.do_tween:Kill()
		self.do_tween = nil
	end

	if self.tip_start_pos == nil then
		return false
	end

	if self.node_list.root_tip == nil then
		return false
	end

	local tr = self.node_list.root_tip.gameObject.transform
	tr.localPosition = self.tip_start_pos
	self.node_list.root_tip:SetActive(false)

	if self.node_list.root_tip_type_1 then
		self.node_list.root_tip_type_1:SetActive(false)
	end

	if self.node_list.root_tip_type_2 then
		self.node_list.root_tip_type_2:SetActive(false)
	end

	return true
end

function OperationTaskChainScheduleView:ShowTip(data)
	if not self:ResetTween() then
		return
	end

	if data == nil then
		return
	end

	if data.show_type == OPERATION_TASK_CHAIN_TIP_TYPE.CHANGE_IMG then
		local asset, bundle = ResPath.GetOperationTaskChainF2(data.res)
		self.node_list.img_tip_1.image:LoadSprite(asset, bundle, function ()
			self.node_list.img_tip_1.image:SetNativeSize()
		end)
		if data.res2 then
			asset, bundle = ResPath.GetOperationTaskChainF2(data.res2)
			self.node_list.img_tip_3.image:LoadSprite(asset, bundle, function ()
				self.node_list.img_tip_3.image:SetNativeSize()
			end)
			self.node_list.img_tip_3:SetActive(true)
		else
			self.node_list.img_tip_3:SetActive(false)
		end
		self.node_list.root_tip_type_1:SetActive(true)
		self.node_list.str_tip_1.text.text = data.str or ""
	elseif data.show_type == OPERATION_TASK_CHAIN_TIP_TYPE.FIXED then
		self.node_list.root_tip_type_2:SetActive(true)
		local asset, bundle = ResPath.GetOperationTaskChainF2(data.res)
		self.node_list.img_tip_2.image:LoadSprite(asset, bundle, function ()
			self.node_list.img_tip_2.image:SetNativeSize()
		end)
	end

	self.node_list.root_tip:SetActive(true)
	local tr = self.node_list.root_tip.gameObject.transform
	self.do_tween = tr:DOLocalMove(Vector3(self.tip_start_pos.x, self.tip_start_pos.y + 500, self.tip_start_pos.z), 1)
	self.do_tween:SetEase(DG.Tweening.Ease.OutCubic)
	self.do_tween:OnComplete(function ()
		if self.tip_timer then
			GlobalTimerQuest:CancelQuest(self.tip_timer)
			self.tip_timer = nil
		end		

		self.tip_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.ResetTween,self), 1)
	end)
end

-----------------------------------TaskChainScheduleInfoRender------------------------------------------------

TaskChainScheduleInfoRender = TaskChainScheduleInfoRender or BaseClass(BaseRender)

function TaskChainScheduleInfoRender:__init()
	self.node_list.img_icon.button:AddClickListener(BindTool.Bind1(self.OnClickGo, self))
end

function TaskChainScheduleInfoRender:OnFlush()
	local data = self:GetData()
	if not data then
		return 
	end

	local is_gray = data.status == OPERATION_TASK_CHAIN_TASK_STATUS.CLOSE and not data.is_join

	local asset, bundle = ResPath.GetOperationTaskChainF2(data.cfg and data.cfg.icon)
	self.node_list.img_icon.image:LoadSprite(asset, bundle, function ()
		self.node_list.img_icon.image:SetNativeSize()
	end)
	
	self.node_list.img_open:SetActive(data.status == OPERATION_TASK_CHAIN_TASK_STATUS.OPEN or data.status == OPERATION_TASK_CHAIN_TASK_STATUS.FOLLOW)
	self.node_list.img_close:SetActive(is_gray)
	self.node_list.img_finish:SetActive(data.is_join)
	self.node_list.str_task_name.text.text = data.name
	
	XUI.SetGraphicGrey(self.node_list.img_finish, is_gray)
	XUI.SetGraphicGrey(self.node_list.img_icon, is_gray)
	XUI.SetGraphicGrey(self.node_list.str_task_name, is_gray)
end

function TaskChainScheduleInfoRender:OnClickFollow()
	if self.data == nil and self.data.follow_id ~= nil then
		return
	end

	local follow_obj = Scene.Instance:GetObj(self.data.follow_id)
	local main_role = Scene.Instance:GetMainRole()
	if follow_obj ~= nil and main_role ~= nil then
		follow_obj:FollowMe(main_role, OperationTaskChainWGData.Instance:GetFollowDis(), OBJ_FOLLOW_TYPE.TASK_CHAIN)
	end
end

function TaskChainScheduleInfoRender:OnClickGo()
	if self.data ~= nil and self.data.move_pos ~= nil then
		GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), self.data.move_pos.x, self.data.move_pos.y)
	end
end