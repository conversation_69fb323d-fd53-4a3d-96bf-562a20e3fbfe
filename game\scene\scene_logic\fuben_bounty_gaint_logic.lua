-----------------------------------------
--赏金任务-擎天玉柱(86)
-----------------------------------------
FuBenBountyGaintLogic = FuBenBountyGaintLogic or BaseClass(CommonFbLogic)

function FuBenBountyGaintLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.show_word_img = true
end

function FuBenBountyGaintLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function FuBenBountyGaintLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenPanelWGCtrl.Instance:OpenBountyTaskView()
    GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

end

function FuBenBountyGaintLogic:GetGuiJiMonsterEnemy()
	CommonFbLogic.GetGuiJiMonsterEnemy(self)
	local target_id = 20000
	local target_obj = nil
	local monster_list = Scene.Instance:GetMonsterList()
	for k, v in pairs(monster_list) do
		local vo = v:GetVo()
		local monster_id = vo.monster_id
		if monster_id < target_id then
			target_obj = v
			target_id = monster_id
		end
	end
	if target_obj ~= nil and target_obj:GetVo().monster_id == 11256 and self.show_word_img then
		FuBenPanelWGCtrl.Instance:VistWordImg(11255)
		self.show_word_img = false
	end
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	
	if target_obj == nil then
		self:MoveToPos(45, 23)
	end
	return target_obj
end

function FuBenBountyGaintLogic:Out()
	CommonFbLogic.Out(self)
	self.show_word_img = true
	FuBenPanelWGCtrl.Instance:VistWordImg(0)	
	FuBenPanelWGCtrl.Instance:CloseBountyTaskView()
    GuajiWGCtrl.Instance:StopGuaji()
end
