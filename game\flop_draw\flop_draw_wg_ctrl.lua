require("game/flop_draw/flop_draw_wg_data")
require("game/flop_draw/flop_draw_view")

FlopDrawWGCtrl = FlopDrawWGCtrl or BaseClass(BaseWGCtrl)

function FlopDrawWGCtrl:__init()
	if FlopDrawWGCtrl.Instance then
		ErrorLog("[FlopDrawWGCtrl] attempt to create singleton twice!")
		return
	end

	FlopDrawWGCtrl.Instance = self
	self.data = FlopDrawWGData.New()
    self.view = FlopDrawView.New(GuideModuleName.FlopDrawView)
  
    self:RegisterAllProtocols()

    self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
end

function FlopDrawWGCtrl:__delete()
	FlopDrawWGCtrl.Instance = nil
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
	    self.data:DeleteMe()
		self.data = nil
	end

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function FlopDrawWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	local other_cfg = self.data:GetOtherCfg()
    if change_item_id == other_cfg.cost_item_id then
        ViewManager.Instance:FlushView(GuideModuleName.FlopDrawView, nil, "flush_cost")
        RemindManager.Instance:Fire(RemindName.FlopDraw)
    end
end

function FlopDrawWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOACatVentureInfo2,"OnSCOACatVentureInfo2")
	self:RegisterProtocol(SCOACatVentureTaskUpdate2,"OnSCOACatVentureTaskUpdate2")
end

function FlopDrawWGCtrl:ReqFlopDrawInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_CAT_VENTURE2
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function FlopDrawWGCtrl:OnSCOACatVentureInfo2(protocol)
	--print_error("========翻牌========",protocol)
	local flag = self.data:GetInfoFlag()
	local old_step = self.data:GetCurStep()
	local step = 0
	for k, v in pairs(protocol.step_flag) do
		if v == 1 then
			step = step + 1
		end
	end

	if flag and old_step < step then
		ViewManager.Instance:FlushView(GuideModuleName.FlopDrawView, nil, "flush_flop_step")
	else
		ViewManager.Instance:FlushView(GuideModuleName.FlopDrawView)
	end

	local old_reward_state = self.data:GetAllRewardState()
	local reward_list = {}
	local reward_cfg = self.data:GetAllStepRewardCfg()
	if not IsEmptyTable(old_reward_state) then
		for k, v in pairs(protocol.step_reward_flag) do
			if old_reward_state[k] ~= v and old_reward_state[k] == 0 then
				table.insert(reward_list, reward_cfg[k].item)
			end
		end
	end

	if not IsEmptyTable(reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end

	self.data:SetAllFlopDrawInfo(protocol)
	self.data:SetInfoFlag()
	RemindManager.Instance:Fire(RemindName.FlopDraw)
end

function FlopDrawWGCtrl:OnSCOACatVentureTaskUpdate2(protocol)
	--print_error("========任务========",protocol)
	self.data:SetSingleTaskInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FlopDrawView, nil, "flush_task")
	RemindManager.Instance:Fire(RemindName.FlopDraw)
end