TianShenBaGuaShopView = TianShenBaGuaShopView or BaseClass(SafeBaseView)

function TianShenBaGuaShopView:__init()
	self.view_style = ViewStyle.Half
	self.is_safe_area_adapter = true
	self:SetMaskBg()

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_bagua_shop_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function TianShenBaGuaShopView:__delete()
end

function TianShenBaGuaShopView:ReleaseCallBack()
	if self.bagua_shop_list then
		self.bagua_shop_list:DeleteMe()
		self.bagua_shop_list = nil
	end

	if self.flush_timer then
		GlobalTimerQuest:CancelQuest(self.flush_timer)
		self.flush_timer = nil
	end

  	if self.confirm_buy_alert then
		self.confirm_buy_alert:DeleteMe()
		self.confirm_buy_alert = nil
	end

	if self.confirm_flush_alert then
		self.confirm_flush_alert:DeleteMe()
		self.confirm_flush_alert = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self.old_have_count = nil
end

function TianShenBaGuaShopView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShen.TZTitleName
	self.node_list.bagua_shop_text.text.text = Language.TianShen.TZTitleText

	local bundle, asset = ResPath.GetRawImagesPNG("a3_yyhd_bj2")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	self.node_list["flush_btn"].button:AddClickListener(BindTool.Bind(self.FlushShop,self))

	-- self.bagua_shop_list = AsyncListView.New(BaGuaShopCell, self.node_list["bagua_shop_list"])
	self.bagua_shop_list = AsyncBaseGrid.New()
	self.bagua_shop_list:CreateCells({
		col = 3,
		change_cells_num = 1,
		list_view = self.node_list.bagua_shop_list,
		assetBundle = "uis/view/tianshen_prefab",
		assetName = "tianshen_shop_cell",
		itemRender = BaGuaShopCell
	})
	self.bagua_shop_list:SetStartZeroIndex(false)

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = false, show_bagua = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.should_play_turn_anim = 0
end

function TianShenBaGuaShopView:ShowIndexCallBack()
	--if TianShenBaGuaWGData.Instance:CheckNeedPlayShopOpen() then
		-- self:PlayOpenAnim()
		-- TianShenBaGuaWGData.Instance:SetShopOpenHadPlay()
	--end
end

function TianShenBaGuaShopView:PlayOpenAnim()
	self.is_playing_open_anim = true
	GlobalTimerQuest:AddDelayTimer(function ()
		self:HideOpenAnim()
	end, 0.1)
end

function TianShenBaGuaShopView:HideOpenAnim()
	self.is_playing_open_anim = false
	self:Flush(0,"flush_anim2")
end

function TianShenBaGuaShopView:FlushShop()
	if self.is_playing_turning  or self.just_send_and_wait then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.IsPlayingTrun)
		return
	end
	-- local flush_times = TianShenBaGuaWGData.Instance:GetBaGuaShopBuyCount()
	-- local flush_price = TianShenBaGuaWGData:GetShopFlushPrice(flush_times + 1)
	-- local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(flush_price)
	-- if not have_enough then

	-- 	return
	-- end



	local shop_list = TianShenBaGuaWGData.Instance:GetBaGuaShopData()
	local base_cfg = {}
	local equip_info = {}
	local have_useful = false
	for k,v in pairs (shop_list) do
		 if v.buy_num == 0 then
			if 1 == TianShenBaGuaWGData.Instance:GetShopItemRed(v.item_id) then
				have_useful = true
				break
			end
		-- 	base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(v.item_id)
		-- 	equip_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(base_cfg.index)
		-- 	if equip_info[base_cfg.part].item_id < 0 then
		-- 		have_useful = true
		-- 		break
		-- 	else
		-- 		local base_cfg2 = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(equip_info[base_cfg.part].item_id)
		-- 		if base_cfg2.star < base_cfg.star then
		-- 			have_useful = true
		-- 			break
		-- 		end
		-- 	end

		end
	end
	if have_useful then
		local ok_fun = function ()
			self:SendFlushRequire()
		end
		if not self.confirm_flush_alert then
			self.confirm_flush_alert = Alert.New()
		end
		self.confirm_flush_alert:SetCheckBoxText(Language.MountLingChongEquip.DontTip)
		self.confirm_flush_alert:SetLableString(Language.TianShen.HaveUsefulItem)
		self.confirm_flush_alert:SetOkFunc(ok_fun)
		self.confirm_flush_alert:SetShowCheckBox(true,"bagua_flush_shop")
		self.confirm_flush_alert:Open()
		return
	end
	self:SendFlushRequire()
end

function TianShenBaGuaShopView:SendFlushRequire()
	local have_count,next_time = TianShenBaGuaWGData.Instance:GetFlushTimesAndTime()
	if have_count > 0 then
		self.just_send_and_wait = true
		TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_SHOP_FRESH)

		GlobalTimerQuest:AddDelayTimer(function ()
			self.just_send_and_wait = false
		end,1)

	else

		local flush_times = TianShenBaGuaWGData.Instance:GetBaGuaShopBuyCount()
		local flush_money = TianShenBaGuaWGData.Instance:GetShopFlushPrice(flush_times + 1)
		local have_enough = RoleWGData.Instance:GetIsEnoughUseGold(flush_money)
		local str = string.format(Language.TianShen.BaGuaShopFlushConfirm,flush_money)
		local ok_func = function ()
				if have_enough then
					self.just_send_and_wait = true
					TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_SHOP_FRESH)
					GlobalTimerQuest:AddDelayTimer(function ()
						self.just_send_and_wait = false
					end,1)
				else
					TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.TianShen.NeedEnoughToFlush,flush_money))
					UiInstanceMgr.Instance:ShowChongZhiView()
				end
  			end


		if flush_times >= 4 then
			if not self.confirm_buy_alert then
				self.confirm_buy_alert = Alert.New()
			end
			self.confirm_buy_alert:SetOkFunc(ok_func)
			self.confirm_buy_alert:SetLableString(str)

			self.confirm_buy_alert:SetCheckBoxText(Language.MountLingChongEquip.DontTip)
			self.confirm_buy_alert:SetShowCheckBox(true,"bagua_shop")
			self.confirm_buy_alert:SetCheckBoxDefaultSelect(false)
			self.confirm_buy_alert:Open()
		else
			TipWGCtrl.Instance:OpenAlertTips(str, ok_func)
		end

	end
end

function TianShenBaGuaShopView:DelayClickAble()
	self.is_playing_turning = true
	GlobalTimerQuest:AddDelayTimer(function ()
		self.is_playing_turning = false
	end,1)

end

function TianShenBaGuaShopView:OnFlush(prarm_t)
	self.should_play_turn_anim = 0
	for k,v in pairs(prarm_t) do
		if k == "flush_anim" then
			self.should_play_turn_anim = 1
			self:DelayClickAble()
		elseif k == "flush_anim2" then
			self.should_play_turn_anim = 2
			--self:DelayClickAble()
		end
	end
	local shop_list = TianShenBaGuaWGData.Instance:GetBaGuaShopData()
	self.bagua_shop_list:SetDataList(shop_list)

	if nil == self.flush_timer then
		self.flush_timer = GlobalTimerQuest:AddRunQuest(function()
			self:CacularFlushTime()
		end,1)
		self:CacularFlushTime()
	end
	-- if not TianShenBaGuaWGData.Instance:GetHadClickSet() then
	-- 	self.node_list["btn_remind"]:SetActive(true)
	-- else
	-- 	self.node_list["btn_remind"]:SetActive(false)
	-- end
end

function TianShenBaGuaShopView:GetTianShenShouldPlayTurnAnim()
	return self.should_play_turn_anim or 0
end

function TianShenBaGuaShopView:CacularFlushTime()
	local have_count,next_time = TianShenBaGuaWGData.Instance:GetFlushTimesAndTime()
	local flush_cd = TianShenBaGuaWGData.Instance:GetBaGuaFlushTime()
	next_time = next_time + flush_cd

	-- if have_count > 0 then
	-- 	GlobalTimerQuest:CancelQuest(self.flush_timer)
	-- 	self.flush_timer = nil
	-- 	self.node_list["flush_time"].text.text = string.format(Language.TianShen.FreeFlushNum,have_count)
	-- else
	self.node_list["time_count"].text.text = string.format(Language.TianShen.FreeFlushNum,have_count)
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	local time_tab = TimeUtil.Format2TableDHMS(next_time - now_time)
	if time_tab.hour == 0 and time_tab.min == 0 and time_tab.s == 0 then
		self.node_list["flush_time"].text.text = Language.TianShen.NumFlushMax
	else
		self.node_list["flush_time"].text.text = string.format("%02d:%02d:%02d", time_tab.hour, time_tab.min,time_tab.s)..Language.TianShen.NextFlushNum
	end
	--end
end



BaGuaShopCell = BaGuaShopCell or BaseClass(BaseRender)

function BaGuaShopCell:LoadCallBack()
	self.shop_item = ItemCell.New(self.node_list["item_cell"])
	self.shop_item:SetItemTipFrom(ItemTip.FROM_TIANSHEN_BAGUA_BAG)
	self.node_list["buy_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBuy, self))
	self.node_list["price_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBaGuaMoney, self))

	self.should_play_turn_anim = TianShenWGCtrl.Instance:GetTianShenShouldPlayTurnAnim()
end

function BaGuaShopCell:ReleaseCallBack()
	if self.shop_item then
		self.shop_item:DeleteMe()
		self.shop_item = nil
	end
	if self.scale_anim then
		self.scale_anim:Kill()
		self.scale_anim = nil
	end
	self:StopAnimDelay()
end

function BaGuaShopCell:OnClickBaGuaMoney()
	local item_id = TianShenBaGuaWGData.Instance:GetBaGuaBiCfg()
    if item_id == 0 then return end
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
end

function BaGuaShopCell:OnFlush()
	self.node_list["normal"]:SetActive(true)

	local shop_item_cfg = {}
	local item_cfg = {}
	if nil == self.old_data then
		self.old_data = self.data
		self.node_list["sell_anim_panel"]:SetActive(false)
		self.node_list["sell_flag"].transform.localScale = Vector2(2, 2, 2)
		if self.data.buy_num == 1 then
			self:JustShowBuy()
		end
	else
		if self.old_data.buy_num == 0 and self.data.buy_num == 1
			and self.old_data.item_id == self.data.item_id
			and self.old_data.index == self.data.index then
			self:PlayItemBuyAnim()
		elseif self.data.buy_num == 1 then
			self:JustShowBuy()
		end
		self.old_data = self.data
	end

	if self.should_play_turn_anim == 1 then
		self.should_play_turn_anim = 0
		self:PlayTurnAnim()
	elseif self.should_play_turn_anim == 2 then
		self.should_play_turn_anim = 0
		--self:PlayTurnAnim()
	end

	local base_cfg = nil
	if self.data then
		self.shop_item:SetData({ item_id = self.data.item_id, num = 1 })

		base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.data.item_id)
		if base_cfg then
			self.shop_item:SetLeftTopImg(base_cfg.star or 0)
		else
			self.shop_item:SetLeftTopImg(0)
		end

		shop_item_cfg = TianShenBaGuaWGData.Instance:GetBaGuaShopItemCfg(self.data.item_id)
		self.node_list["price"].text.text = (shop_item_cfg.price or 0)
		item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if item_cfg and item_cfg.name then
			self.node_list["name"].text.text = item_cfg.name
		end

		self.node_list["remind"]:SetActive(1 == TianShenBaGuaWGData.Instance:GetShopItemRed(self.data.item_id) and self.data.buy_num == 0)
		self.node_list["sell_anim_panel"]:SetActive(self.data.buy_num == 1)

		local empty_flag = TianShenBaGuaWGData.Instance:GetIsEmptyPartEquip(base_cfg.index, base_cfg.part)
		self.node_list["no_wear_flag"]:SetActive(empty_flag)
	else
		self.node_list["name"].text.text = ""
		self.node_list["no_wear_flag"]:SetActive(false)
	end
end


function BaGuaShopCell:OnClickBuy()
	if self.data.buy_num == 1 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.BaGuaShopSellAll)
		return
	end
	local shop_item_cfg = TianShenBaGuaWGData.Instance:GetBaGuaShopItemCfg(self.data.item_id)
	if shop_item_cfg then
		local price = shop_item_cfg.price
		if price > TianShenBaGuaWGData.Instance:GetBaGuaCoinNum() then
			local baguabi_id = TianShenBaGuaWGData.Instance:GetBaGuaBiCfg()
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = baguabi_id})
			--TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.BaGuaMoneyNotEnough)
		else
			local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(self.data.item_id)
			local ok_func = function ()
				TianShenWGCtrl.Instance:SendBaGuaOpera(TIANSHEN_BAGUA_REQ.TIANSHEN_BAGUA_REQ_SHOP_BUY,self.data.index)
			end
			local str = ""
			if base_cfg.index < 4 then
				local three_star_cfg = TianShenBaGuaWGData.Instance:GetBaGuaCfgByIndexStarPart(base_cfg.index,3,base_cfg.part)
				local already_have = false
				if TianShenBaGuaWGData.Instance:ChackHaveBagua(three_star_cfg.itemid) then
					already_have = true
					str = Language.TianShen.AlreadyHaveBaGuaBuy
				else
					already_have = TianShenBaGuaWGData.Instance:CheckCanComposeItem(three_star_cfg.itemid)
					str = Language.TianShen.AlreadyHaveBaGuaBuy
				end

				if already_have then
					TianShenWGCtrl.Instance:DoBuyHaveBaGuaConfirm(ok_func,str)
				else
					ok_func()
				end
			else
				ok_func()
			end
		end
	end
end

function BaGuaShopCell:PlayItemBuyAnim()
	self.node_list["sell_anim_panel"]:SetActive(true)
	self.node_list["sell_flag"].transform.localScale = Vector3(2,2,2)

	if self.scale_anim then
		self.scale_anim:Kill()
		self.scale_anim = nil
	end
	self.scale_anim = self.node_list["sell_flag"].rect:DOScale(Vector3(1, 1, 1), 0.4)
	self.scale_anim:SetEase(DG.Tweening.Ease.OutCubic)
end

function BaGuaShopCell:JustShowBuy()
	self.node_list["sell_anim_panel"]:SetActive(true)
	if self.scale_anim then
		self.scale_anim:Kill()
		self.scale_anim = nil
	end

	self.node_list["sell_flag"].transform.localScale = Vector3(1,1,1)
end


function BaGuaShopCell:StopPlayItemBuyAnim()
	self.node_list["sell_anim_panel"]:SetActive(false)
end

function BaGuaShopCell:StopAnimDelay()

		if self.turning_anim1 then
			GlobalTimerQuest:CancelQuest(self.turning_anim1)
			self.turning_anim1 = nil
		end

		if self.turning_anim2 then
			GlobalTimerQuest:CancelQuest(self.turning_anim2)
			self.turning_anim2 = nil
		end

		if self.turning_anim3 then
			GlobalTimerQuest:CancelQuest(self.turning_anim3)
			self.turning_anim3 = nil
		end

end

function BaGuaShopCell:PlayTurnAnim(show_type)
	if self.playing_truning then
		return
		-- self:StopAnimDelay()
	end
	self.playing_truning = true
	self.should_play_turn_anim = 0
	self:StopPlayItemBuyAnim()

	self.node_list["normal"].canvas_group.alpha = 0

	self.node_list["normal"].transform.rotation = Quaternion.Euler(0, 180,0)
	self.node_list["hight_line_bg"].transform.rotation = Quaternion.Euler(0, 0,180)
	self.node_list["hight_line_bg"].transform.localScale = Vector3(1,1,1)
	self.node_list["hight_line_bg"]:SetActive(false)

	local base_time = 1.5
	local time1 = 0.15
	local time2 = 0.15

	if show_type == 1 then
		base_time = 0
	end
	self.turning_anim1 =  GlobalTimerQuest:AddDelayTimer(function ()
		self.node_list["hight_line_bg"]:SetActive(true)
		self.node_list["hight_line_bg"].canvas_group.alpha = 1
		self.node_list["normal"].rect:DORotate(Vector3(0, 90, 0), time1, DG.Tweening.RotateMode.FastBeyond360)
		self.node_list["hight_line_bg"].rect:DORotate(Vector3(0, -90, 180), time1, DG.Tweening.RotateMode.FastBeyond360)

		self.node_list["normal"].rect:DOScale(Vector3(1.1, 1.1, 1.1), time1)
		self.node_list["hight_line_bg"].rect:DOScale(Vector3(1.1, 1.1, 1.1), time1)

		self.node_list["normal"].canvas_group:DoAlpha(0, 1, time1)
	end,base_time)

	self.turning_anim2 = GlobalTimerQuest:AddDelayTimer(function ()
		self.node_list["hight_line_bg"]:SetActive(false)

		self.node_list["normal"].rect:DORotate(Vector3(0, 0, 0), time2, DG.Tweening.RotateMode.FastBeyond360)
		self.node_list["normal"].rect:DOScale(Vector3(1, 1, 1), time2)
		self.node_list["hight_line_bg"].rect:DORotate(Vector3(0, -180, 180), time1, DG.Tweening.RotateMode.FastBeyond360)
		self.node_list["hight_line_bg"].canvas_group:DoAlpha(1, 0, 0.2)
	end,base_time+time1)

	self.turning_anim3 = GlobalTimerQuest:AddDelayTimer(function ()

		self.playing_truning = false
		self:ReInitPosition()
	end,base_time+time1+time2)
end

function BaGuaShopCell:ReInitPosition()
	self.node_list["normal"].canvas_group.alpha = 1
	self.node_list["normal"].transform.rotation = Quaternion.Euler(0, 0,0)
	self.node_list["normal"].transform.localScale = Vector3(1,1,1)
	self.node_list["hight_line_bg"]:SetActive(false)

	if self.data.buy_num == 1 then
		self:JustShowBuy()
	end
 end
