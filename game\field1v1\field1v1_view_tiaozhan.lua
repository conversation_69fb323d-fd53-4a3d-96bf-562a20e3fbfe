local rank_amount = 3
local MY_UPVOTE_RANK_INDEX = 11

function Field1v1View:DeleteTiaoZhan()
	self.player_info_1 = nil
	self.player_info_2 = nil
	self.player_info_3 = nil
	self.role_intro_model = nil
	if self.role_list then
		for k,v in pairs(self.role_list) do
			v:DeleteMe()
		end
		self.role_list = nil
	end

	if self.alert then
		self.alert:DeleteMe()
		self.alert = nil
	end

	if self.rich_attr_capacity then
		self.rich_attr_capacity:DeleteMe()
		self.rich_attr_capacity = nil
	end

	self.btn_effect = nil
	if self.field1v1_alert then
		self.field1v1_alert:DeleteMe()
		self.field1v1_alert = nil
	end

	if self.high_power_enter then
		self.high_power_enter:DeleteMe()
		self.high_power_enter = nil
	end

	if self.role_arena_display_1 then
		self.role_arena_display_1:DeleteMe()
		self.role_arena_display_1 = nil
	end

	if self.role_arena_display_2 then
		self.role_arena_display_2:DeleteMe()
		self.role_arena_display_2 = nil
	end

	if self.role_arena_display_3 then
		self.role_arena_display_3:DeleteMe()
		self.role_arena_display_3 = nil
	end

	-- if self.challenge_list then
	-- 	for i,v in ipairs(self.challenge_list) do
	-- 		v:DeleteMe()
	-- 	end
	-- 	self.challenge_list = nil
	-- end

	if self.field_upvote_list then
		self.field_upvote_list:DeleteMe()
		self.field_upvote_list = nil
	end

	if self.field_1v1_rank_list then
		self.field_1v1_rank_list:DeleteMe()
		self.field_1v1_rank_list = nil
	end

	if self.field_1v1_count_reward_list then
		self.field_1v1_count_reward_list:DeleteMe()
		self.field_1v1_count_reward_list = nil
	end

	if self.field_1v1_count_big_reward then
		self.field_1v1_count_big_reward:DeleteMe()
		self.field_1v1_count_big_reward = nil
	end

	if self.my_field_upvote then
		self.my_field_upvote:DeleteMe()
		self.my_field_upvote = nil
	end

	if self.field_1v1_reward_item then
		self.field_1v1_reward_item:DeleteMe()
		self.field_1v1_reward_item = nil
	end

	self.view = nil
	self.layout_tip = nil
	self.layout_xyb = nil
	self.layout_honor = nil
	self.btn_shuaxing = nil
	self.click_enter_buy_count_data = nil
	self.is_click_enter_buy_count = nil
	self.jingji_isload = nil
	if self.top_one_role then
		self.top_one_role:DeleteMe()
		self.top_one_role = nil
	end
end

function Field1v1View:Initfieldtiaozhan()
	self.view = self.node_list.layout_field1v1_tz
	self.layout_tip = self.node_list.layout_tip --tip按钮
	self.layout_xyb = self.node_list.layout_xyb --排行奖励
	self.layout_honor = self.node_list.layout_honor --荣耀商店
	self.btn_shuaxing = self.node_list.btn_shuaxing --刷新按钮
	self.is_click_enter_buy_count = false --是否点击挑战按钮购买挑战次数
	self.click_enter_buy_count_data = nil
	self.node_list.btn_shuaxing_text.text.text = Language.Field1v1.TZBtnText1

	-- self.challenge_list = {}
	-- for i = 1, rank_amount do
	-- 	self.challenge_list[i] = Field1v1ChallengeRender.New(self.node_list["role_display" .. i], self)
	-- 	self.challenge_list[i]:SetIndex(i)
	-- 	self.challenge_list[i]:SetClickFightCallBack(BindTool.Bind(self.OnClickTiaoZhan, self))
	-- end

	if not self.field_upvote_list then
		self.field_upvote_list = AsyncListView.New(Field1v1UpvoteRankRender, self.node_list.field_upvote_rank_list)
	end

	if not self.field_1v1_rank_list then
		self.field_1v1_rank_list = AsyncListView.New(Field1v1RankInfoRender, self.node_list.field_1v1_rank_list)
		self.field_1v1_rank_list:SetRefreshCallback(function(item_cell, cell_index)
			if item_cell then
				item_cell:SetClickFightCallBack(BindTool.Bind(self.OnClickTiaoZhan, self))
			end
		end)
	end

	self.my_field_upvote = Field1v1UpvoteRankRender.New(self.node_list.my_upvote_rank)

	if not self.field_1v1_reward_item then
		self.field_1v1_reward_item = ItemCell.New(self.node_list.field_1v1_reward_item)
	end

	if not self.field_1v1_count_reward_list then
		self.field_1v1_count_reward_list = AsyncListView.New(Field1v1CountRewardRender, self.node_list["field_1v1_count_reward_list"])
	end

	if not self.field_1v1_count_big_reward then
		self.field_1v1_count_big_reward = Field1v1CountRewardRender.New(self.node_list["field_1v1_count_big_reward"])
	end

	self:InitTitleViewAndEvent()
	self.jingji_isload = true
end

function Field1v1View:InitTitleViewAndEvent()
	Field1v1View.IsShowWing = true
	XUI.AddClickEventListener(self.layout_xyb, BindTool.Bind1(Field1v1WGCtrl.Instance.OpenRewardInfo, Field1v1WGCtrl.Instance)) --排行帮奖励
	XUI.AddClickEventListener(self.btn_shuaxing, BindTool.Bind1(self.OnClickShuXin, self)) --刷新
	XUI.AddClickEventListener(self.layout_honor,BindTool.Bind1(Field1v1WGCtrl.Instance.OpenHonorShopPanel, Field1v1WGCtrl.Instance))  --荣誉商店
	XUI.AddClickEventListener(self.node_list.layout_changellent_record,BindTool.Bind1(self.OpenChangellengePanel, self))  --荣誉商店
	XUI.AddClickEventListener(self.node_list.btn_zhenjia, BindTool.Bind1(self.OnClickBuyNum, self))  --购买次数

	Field1v1WGCtrl.Instance:ReqFieldGetDetailRankInfo()
end

function Field1v1View:ShowIndexTiaoZhan()
	self.node_list.title_view_name.text.text = Language.Field1v1.Solo
	if self.node_list.RawImage_tongyong then
		local bundle, asset = ResPath.GetRawImagesPNG("a3_fb_bj_4")
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	Field1v1WGCtrl.Instance:SendChallengeField(CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_INFO)
	Field1v1WGCtrl.Instance:SendChallengeField(CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_CHALLENGE_INFO)
end

function Field1v1View:OnClickTiaoZhan(index)
	if Field1v1WGData.Instance:GetResidueTiaoZhanNum() <= 0 then
		-- self:OnClickBuyNum()
		-- self.is_click_enter_buy_count = true
		-- self.click_enter_buy_count_data = data
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.PKNoCount)
		return
	end

	local info = Field1v1WGData.Instance:GetUserinfo()
	-- local boo = Field1v1WGData.Instance:GetChallengeFlag()

	if nil == info then
		return
	end

	local sorted_list = info.rank_list
	table.sort(sorted_list, SortTools.KeyLowerSorter("rank"))

	local rank_index = sorted_list[index] or info.top_rank_list[1]
	if nil == rank_index or nil == rank_index.user_id then
		return
	end

	local enemy_info = Field1v1WGData.Instance:GetRoleInfoByUid(rank_index.user_id)
	if nil == enemy_info then
		return
	end

	local tz_info = Field1v1WGData.Instance:GetRoleTiaoZhanInfoByUid(rank_index.user_id) or info.top_rank_list[1]
	local role_base_info = RoleWGData.Instance.role_vo
	local is_skip = false
	if tz_info then
		local data = {}
		data.opponent_index = tz_info.index
		data.rank_pos = tz_info.rank_pos
		data.opponent_uid = rank_index.user_id
		data.capability = enemy_info.capability

		local other_cfg = Field1v1WGData.Instance:GetChallengeFieldOtherCfg()
		local info = Field1v1WGData.Instance:GetUserinfo()
		if role_base_info.level >= other_cfg.seckill_level
			and role_base_info.capability >= enemy_info.capability and info.rank < rank_index.rank then --and role_base_info.level < skip_level
			is_skip = true
		else
			is_skip = false
		end

		data.is_skip = is_skip and 1 or 0
		if role_base_info.capability >= enemy_info.capability then
			Field1v1WGCtrl.Instance:ResetFieldFightReq(data)
		else
			self:HighPowerIsEnter(data)
		end
	end
end

--挑战高战力tips提示
function Field1v1View:HighPowerIsEnter(data)
	if nil == data then
		return
	end

	if nil == self.high_power_enter then
		self.high_power_enter = Alert.New()
		self.high_power_enter:SetShowCheckBox(true)
		self.high_power_enter:SetCheckBoxDefaultSelect(false)
		self.high_power_enter:SetMarkName("JingJiChang_HighPowerIsEnter")
	end

	local ok_func = function ()
		Field1v1WGCtrl.Instance:ResetFieldFightReq(data)
	end
	self.high_power_enter:SetOkFunc(ok_func)
	self.high_power_enter:SetLableString(Language.Field1v1.TiaoZhanHighPower)
	self.high_power_enter:Open()
end

function Field1v1View:BuyCountEnterFBCallBack()
	if self.is_click_enter_buy_count and self.click_enter_buy_count_data then
		local role_info = RoleWGData.Instance.role_vo
		if self.click_enter_buy_count_data.capability > role_info.capability then
			self:HighPowerIsEnter(self.click_enter_buy_count_data)
		else
			Field1v1WGCtrl.Instance:ResetFieldFightReq(self.click_enter_buy_count_data)
		end
		self.click_enter_buy_count_data = nil
		self.is_click_enter_buy_count = false
	end
end

function Field1v1View:RoleInfoVo(vo,index)
	if nil == vo or nil == self.role_arena_display_1 or nil == self.role_arena_display_2 or nil == self.role_arena_display_3 then
		return
	end

	if index == 1 then
		self.role_arena_display_1:SetModelResInfo(vo)
		self.player_info_1 = vo

	elseif index == 2 then
		self.role_arena_display_2:SetModelResInfo(vo)
		self.player_info_2 = vo

	elseif index == 3 then
		self.role_arena_display_3:SetModelResInfo(vo)
		self.player_info_3 = vo
	end

	self:IsShowWingRes(vo,index)
end

function Field1v1View:GetShaoDangTimes()
	local times = 0
	local info = Field1v1WGData.Instance:GetUserinfo()
	if info == nil then return 0 end
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if info.challenge_cd_timer > server_time and info.is_cd_timer_limit > 0 then
		return 0
	end

	local cd_timer = info.challenge_cd_timer - server_time
	cd_timer = cd_timer < 0 and 0 or cd_timer
	times = math.ceil((1800 - cd_timer) / 360)
	times = math.min(times, Field1v1WGData.Instance:GetResidueTiaoZhanNum())
	return times
end

function Field1v1View:OpenChangellengePanel()
	Field1v1WGCtrl.Instance:OpenChangellengePanel()
end

function Field1v1View:IsShowWingRes(vo,index)
	if Field1v1View.IsShowWing then
		if index then
			if index == 1 then
				self.role_arena_display_1:SetWingAsset(ResPath.GetWingModel(self.player_info_1.appearance.wing_use_grade))
			elseif index == 2 then
				self.role_arena_display_2:SetWingAsset(ResPath.GetWingModel(self.player_info_2.appearance.wing_use_grade))
			elseif index == 3 then
				self.role_arena_display_3:SetWingAsset(ResPath.GetWingModel(self.player_info_3.appearance.wing_use_grade))
			end
		else
			self.role_arena_display_1:SetWingAsset(ResPath.GetWingModel(self.player_info_1.appearance.wing_use_grade))
			self.role_arena_display_2:SetWingAsset(ResPath.GetWingModel(self.player_info_2.appearance.wing_use_grade))
			self.role_arena_display_3:SetWingAsset(ResPath.GetWingModel(self.player_info_3.appearance.wing_use_grade))
		end
	else
		self.role_arena_display_1:RemoveWing()
		self.role_arena_display_2:RemoveWing()
		self.role_arena_display_3:RemoveWing()
	end
end

function Field1v1View:OnFlushTiaoZhan()
	if not self.jingji_isload then
		return
	end

	local info = Field1v1WGData.Instance:GetUserinfo()

	if nil == info then
		return
	end

	local sorted_list = info.rank_list
	if not IsEmptyTable(sorted_list) then
		table.sort(sorted_list, SortTools.KeyLowerSorter("rank"))
		self.field_1v1_rank_list:SetDataList(sorted_list)
	end

	-- for k,v in pairs(sorted_list) do
	-- 	if v.user_id and v.user_id ~= 0 then
	-- 		if self.challenge_list[k] then
	-- 			self.challenge_list[k]:SetData(v)
	-- 		end
	-- 	end
	-- end

	local upvote_data = Field1v1WGData.Instance:GetChallengeRankListInfo()
	if not IsEmptyTable(upvote_data) then
		self.field_upvote_list:SetDataList(upvote_data)
	end

	local my_upvote_data = Field1v1WGData.Instance:GetRoleChallengeRankListInfo()
	if not IsEmptyTable(my_upvote_data) then
		self.my_field_upvote:SetIndex(11)
		self.my_field_upvote:SetData(my_upvote_data)
	end


	local upvote_reward_data = Field1v1WGData.Instance:GetChallengeFieldFirstRankEndReward()
	if not IsEmptyTable(upvote_reward_data) and nil ~= upvote_reward_data[0] then
		self.field_1v1_reward_item:SetData(upvote_reward_data[0])
	end

	local role_vo = RoleWGData.Instance.role_vo
	local tiaozhan_num = Field1v1WGData.Instance:GetResidueTiaoZhanNum()
	local tiaozhan_sum_num = Field1v1WGData.Instance:GetSumTiaoZhanNum()
	-- local tiaozhan_num_color = tiaozhan_num > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
	self.node_list.label_tianzhannum.text.text = string.format(Language.Activity.ChuShenMaterialNumberShowType1_1, tiaozhan_num, tiaozhan_sum_num)
	self.node_list.label_currank.text.text = info.rank

	if role_vo then
		self.node_list.label_myzhanli.text.text = role_vo.capability
	end

	self.is_cd_timer_limit = info.is_cd_timer_limit
	if info.challenge_cd_timer == 0 then
		self.is_cd_timer_limit = 0
	end

	local remind_1 = Field1v1WGData.Instance:GetChallengeFieldRankRewardRed()
	--local remind_2 = Field1v1WGData.Instance:GetChallengeFieldCountRewardRemind()
	self.node_list.img_tiaozhantips:SetActive(remind_1)-- or remind_2)

	self:FlushTopModel(info.top_rank_list[1])
	self:FlushField1v1CountRewardList()
end

function Field1v1View:FlushField1v1CountRewardList()
	local battle_count = Field1v1WGData.Instance:GetChallengeFieldBattleCount()
	self.node_list.field_1v1_count_text.text.text = battle_count

	local init_btn_index_3, jump_pos = Field1v1WGData.Instance:GetChallengeFieldCountRewardRemind()
	local reward_data = Field1v1WGData.Instance:GetFieldCountRewardCfgBySpSort()
	local big_reward = {}
	local normal_reward = {}
	for index, value in ipairs(reward_data) do
		if index == #reward_data then
			big_reward = value
		else
			table.insert(normal_reward, value)
		end
	end
	self.field_1v1_count_reward_list:SetDataList(normal_reward)
	self.field_1v1_count_reward_list:JumpToIndex(jump_pos, 2)

	self.field_1v1_count_big_reward:SetData(big_reward)
end

function Field1v1View:OnClickBuyNum()
	if nil == self.alert then
		self.alert = Alert.New()
		self.alert:SetShowCheckBox(true)
		self.alert:SetMarkName("ArenaJingJiChang")
	end

	local ok_func = function()
		Field1v1WGCtrl.Instance:FieldBuyJoinTimes()
	end

	local cancle_func =  function()
		if self.is_click_enter_buy_count or self.click_enter_buy_count_data then
			self.is_click_enter_buy_count = false
			self.click_enter_buy_count_data = nil
		end
	end

	self.alert:SetOkFunc(ok_func)
	self.alert:SetCancelFunc(cancle_func)
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local total_buy_fb_cfg = FuBenPanelWGData.Instance:GetFieldCountCfg()

	-- local total_buy_fb_count = total_buy_fb_cfg["param_" .. role_vip]
	-- local buy_fb_count = Field1v1WGData.Instance:GetBuyJoinTimes()
	-- local can_buy_count = total_buy_fb_count - buy_fb_count
	-- if can_buy_count <= 0 then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Kuafu1V1.PKNoCount)
	-- 	return
	-- end

	Field1v1WGCtrl.Instance:FieldBuyView()
end

function Field1v1View:RequestFlushChallengesList()
	Field1v1WGCtrl.Instance:ResetOpponentList()

	if CountDownManager.Instance:HasCountDown("ShuaxinFlush") then
		CountDownManager.Instance:RemoveCountDown("ShuaxinFlush")
	end

	XUI.SetButtonEnabled(self.node_list.btn_shuaxing, false)
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), TimeWGCtrl.Instance:GetServerTime() + 3)
	CountDownManager.Instance:AddCountDown("ShuaxinFlush",
		BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime1, self), TimeWGCtrl.Instance:GetServerTime() + 3, nil, 1)
end

function Field1v1View:OnClickShuXin()
	if self.shuaxin_cd then
		self:OnClickShuXinTips()
		return
	end

	self:RequestFlushChallengesList()
end

function Field1v1View:OnClickShuXinTips()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.ReduceCDTips1)
end

function Field1v1View:UpdateTime(elapse_time, total_time)
	if self.node_list.btn_shuaxing_text then
		local show_time = math.ceil(total_time - elapse_time)
		self.node_list.btn_shuaxing_text.text.text = string.format(Language.Field1v1.TZBtnText2, show_time)
	end
	self.shuaxin_cd = true
end

function Field1v1View:CompleteTime1()
	if self.node_list.btn_shuaxing_text then
		self.node_list.btn_shuaxing_text.text.text = Language.Field1v1.TZBtnText1
	end

	if self.node_list.btn_shuaxing then
		XUI.SetButtonEnabled(self.node_list.btn_shuaxing, true)
	end
	self.shuaxin_cd = false
end

function Field1v1View:FlushTopModel(role_info)
	if IsEmptyTable(role_info) then
		return
	end

	if not self.top_one_role then
		self.top_one_role = Field1v1ChallengeRender.New(self.node_list.role_display, self)
		self.top_one_role:SetClickFightCallBack(BindTool.Bind(self.OnClickTiaoZhan, self))
	end

	self.top_one_role:SetData(role_info)
end

FieldBuyView = FieldBuyView or BaseClass(DayCountChangeView)

function FieldBuyView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(602, 480)})
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_copper_msg")
end

function FieldBuyView:__delete()

end

function FieldBuyView:ReleaseCallBack()
	DayCountChangeView.ReleaseCallBack(self)
	if self.copper_vip_num1 then
		self.copper_vip_num1:DeleteMe()
		self.copper_vip_num1 = nil
	end

	if self.copper_vip_num2 then
		self.copper_vip_num2:DeleteMe()
		self.copper_vip_num2 = nil
	end
end

function FieldBuyView:LoadCallBack()
	DayCountChangeView.LoadCallBack(self)
	self.node_list["title_view_name"].text.text = Language.ViewName.CiShuZengJia
	local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
	local cost =  Field1v1WGData.Instance:GetChallengeFieldBuyConsume()
	local str = string.format(Language.FuBenPanel.CopperBuyNum_1, cost)
	self.node_list["rich_buy_desc"].text.text = str

	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.OnClinkCancelHandler, self))
	self.node_list["btn_buy_count"].button:AddClickListener(BindTool.Bind(self.OnClinkBuyCount, self))
end

function FieldBuyView:ShowIndexCallBack()
end

function FieldBuyView:OnFlush()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetFieldCountCfg()
	local  buy_fb_count = Field1v1WGData.Instance:GetBuyJoinTimes()--copper_info
	local vip_count = vip_buy_cfg["param_" .. role_vip]
	local temp_str = vip_count - buy_fb_count
	local des
	if temp_str > 0 then
    	des = string.format(Language.FuBenPanel.CopperBuyTips, temp_str, vip_count)
    else
    	des = string.format(Language.FuBenPanel.CopperBuyTipsNotnough, temp_str, vip_count)
    end

	self.node_list["rich_buy_des_1"].text.text = des
	local next_vip = FuBenPanelWGData.Instance:GetFieldNextBuyCount(role_vip)

	local max_vip = VipWGData.Instance:GetMaxVIPLevel()
	if role_vip < next_vip and vip_buy_cfg["param_" .. next_vip] then
		if vip_buy_cfg["param_" .. next_vip] > 0 then
			des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. next_vip])
		end
	else
		des = string.format(Language.FuBenPanel.CopperBuyTips2, vip_buy_cfg["param_" .. role_vip])
	end

	if role_vip >= next_vip then
		des = Language.FuBenPanel.CopperBuyTips3
	end

	self.node_list["rich_buy_des_2"].text.text = des

	if self.node_list["img_vip_curlevel"] and self.node_list["img_vip_nexlevel"] then
		self.node_list["img_vip_curlevel"].text.text = "V".. role_vip

		if role_vip < next_vip then
			self.node_list["img_vip_nexlevel"].text.text = "V"..next_vip
			self.node_list["layout_next_vip"]:SetActive(true)

		else
			self.node_list["img_vip_nexlevel"].text.text = "V"..role_vip
			self.node_list["layout_next_vip"]:SetActive(false)
		end
	end
end

function FieldBuyView:OnClinkCancelHandler()
	self:Close()
end

function FieldBuyView:OnClinkBuyCount()
	Field1v1WGCtrl.Instance:FieldBuyJoinTimes()
end

--==============================================================================
local model_rotation = {
	Quaternion.Euler(0, -197, 0),
	Quaternion.Euler(0, -185, 0),
	Quaternion.Euler(0, -174, 0),
	Quaternion.Euler(0, -163, 0),
}

Field1v1ChallengeRender = Field1v1ChallengeRender or BaseClass(BaseRender)
function Field1v1ChallengeRender:__init(instance, parent_view)
	XUI.AddClickEventListener(self.node_list.fight_btn, BindTool.Bind1(self.OnClickFightCallBack, self))
	XUI.AddClickEventListener(self.node_list.rank_upvote_btn, BindTool.Bind1(self.OnClickUpvoteBtn, self))
	self.parent_view = parent_view

	self.late_id = -1
end

function Field1v1ChallengeRender:__delete()
	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	self.parent_view = nil
end

function Field1v1ChallengeRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local role_info = Field1v1WGData.Instance:GetRoleInfoByUid(self.data.user_id)
	if IsEmptyTable(role_info) then
		return
	end

	local is_top = self.data.rank == 1
	local self_rank = Field1v1WGData.Instance:GetUserinfo().rank

	if self_rank <= 10 then
		self.node_list.fight_btn:SetActive(self_rank ~= 1 or not is_top)
		self.node_list.top_one_flag:SetActive(self_rank == 1 and is_top)
	else
		self.node_list.fight_btn:SetActive(not is_top)
		self.node_list.top_one_flag:SetActive(is_top)
	end

	self.node_list["name_txt"].text.text = role_info.name

	self.node_list["capability_txt"].text.text =  role_info.capability
	local upvote_data = Field1v1WGData.Instance:GetChallengeRankListInfo()
	local top_one_role_data = upvote_data[1]
	if top_one_role_data and top_one_role_data.uid > 0 then
		self.node_list["rank_upvote_btn_text"].text.text =  top_one_role_data.have_like_count
		self.node_list["rank_upvote_remind"]:SetActive(top_one_role_data.daily_like_flag == 0)
	end

	if self.late_id ~= self.data.user_id then
		self.late_id = self.data.user_id
		self:FlushModel(role_info)
	end
	if self.role_model then
		self.role_model:PlayLastAction()
	end
end

function Field1v1ChallengeRender:SetClickFightCallBack(call_back)
	self.fight_call_back = call_back
end

function Field1v1ChallengeRender:OnClickFightCallBack()
	if self.fight_call_back then
		self.fight_call_back(self.index)
	end
end

function Field1v1ChallengeRender:FlushModel(role_info)
	if IsEmptyTable(role_info) then
		return
	end

	if not self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_pos"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)
		self.role_model:SetRTAdjustmentRootLocalScale(0.8)
		-- self.role_model:SetUI3DModel(self.node_list.model_pos.transform, self.node_list.model_event.event_trigger_listener,
		-- 							1, false, MODEL_CAMERA_TYPE.BASE)
		self.role_model:SetIsSupportClip(true)
	end

	local special_status_table = {ignore_wing = self.data.rank ~= 1, ignore_fazhen = true, ignore_jianzhen = true, ignore_halo = true}
	self.role_model:SetModelResInfo(role_info, special_status_table)
	self.role_model:FixToOrthographic(self.parent_view.root_node_transform)
	-- self.role_model:RemoveWeapon()
end

function Field1v1ChallengeRender:OnClickUpvoteBtn()
	local upvote_data = Field1v1WGData.Instance:GetChallengeRankListInfo()
	local top_one_role_data = upvote_data[1]
	if top_one_role_data and top_one_role_data.uid > 0 then
		if top_one_role_data.daily_like_flag == 0 then
			Field1v1WGCtrl.Instance:SendChallengeField(CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_LIKE, top_one_role_data.uid)
		else
			TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.UpvoteRankTips2)
		end
	else
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.UpvoteRankTips)
	end
end

---------------------------------Field1v1UpvoteRankRender Star----------------------------------
Field1v1UpvoteRankRender = Field1v1UpvoteRankRender or BaseClass(BaseRender)

function Field1v1UpvoteRankRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.upvote_btn, BindTool.Bind(self.OnClickUpvoteBtn, self))
	self.can_click = false
end

function Field1v1UpvoteRankRender:ReleaseCallBack()

end

function Field1v1UpvoteRankRender:OnFlush()
	if not self.data then
		return
	end

	local is_top_three = self.index <= 3
	local is_role_me = self.index == MY_UPVOTE_RANK_INDEX
	self.can_click = self.index < MY_UPVOTE_RANK_INDEX and self.data.daily_like_flag == 0 and self.data.uid > 0

	self.node_list.rank_num:SetActive(not is_top_three)

	self.node_list.upvote_btn.button.interactable= not is_role_me

	if not is_role_me then
		self.node_list.rank_num_bg:SetActive(is_top_three)
	end

	if is_top_three and not is_role_me then
		local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. self.index)
		self.node_list.rank_num_bg.image:LoadSprite(bundle, asset, function()
			self.node_list.rank_num_bg.image:SetNativeSize()
		end)
	end

	local str = self.data.uid > 0 and
		string.format(Language.Field1v1.UpvoteRankDescText, self.data.role_name, self.data.capatility) or
		Language.Field1v1.UpvoteRankNoRank

	self.node_list.desc.text.text = str
	self.node_list.btn_text.text.text = ToColorStr(self.data.have_like_count, COLOR3B.C5)
	self.node_list.rank_num.text.text = is_role_me and self.data.rank_pos + 1 or self.index
end

function Field1v1UpvoteRankRender:OnClickUpvoteBtn()
	if self.data.uid > 0 then
		if self.can_click then
			Field1v1WGCtrl.Instance:SendChallengeField(CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_LIKE, self.data.uid)
		else
			TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.UpvoteRankTips2)
		end
	else
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.UpvoteRankTips)
	end
end
---------------------------------Field1v1UpvoteRankRender End----------------------------------

---------------------------------Field1v1RankInfoRender Star----------------------------------
Field1v1RankInfoRender = Field1v1RankInfoRender or BaseClass(BaseRender)

function Field1v1RankInfoRender:LoadCallBack()
	if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["hero_icon"])
	end

	XUI.AddClickEventListener(self.node_list.fight_btn, BindTool.Bind1(self.OnClickFightCallBack, self))
end

function Field1v1RankInfoRender:ReleaseCallBack()
	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

function Field1v1RankInfoRender:OnFlush()
	if not self.data then
		return
	end

	local role_info = Field1v1WGData.Instance:GetRoleInfoByUid(self.data.user_id)
	if IsEmptyTable(role_info) then
		return
	end

	self.node_list["desc"].text.text = string.format(Language.Field1v1.RankName, self.data.rank, role_info.name)

	local role_cap = RoleWGData.Instance:GetAttr("capability") or 0
	local color =  role_cap >= role_info.capability and COLOR3B.C4 or COLOR3B.C10
	self.node_list["cap_value"].text.text = string.format(Language.Field1v1.ZhanLi, color, role_info.capability)

	self.node_list["recom_tips"]:SetActive(role_cap >= role_info.capability)

	local res_str = role_cap >= role_info.capability and "a3_jjc_tzan2" or "a3_jjc_tzan1"
	local bundle, asset = ResPath.GetF2Field1v1(res_str)
	self.node_list.fight_btn_bg.image:LoadSprite(bundle, asset, function()
		self.node_list.fight_btn_bg.image:SetNativeSize()
	end)

	local data = {}
	data.role_id = self.data.user_id
	data.prof = role_info.prof
	data.sex = role_info.sex
	data.fashion_photoframe = role_info.appearance.fashion_photoframe
	self.role_head_cell:SetImgBg(false)
	self.role_head_cell:SetData(data)
end

function Field1v1RankInfoRender:SetClickFightCallBack(call_back)
	self.fight_call_back = call_back
end

function Field1v1RankInfoRender:OnClickFightCallBack()
	if self.fight_call_back then
		self.fight_call_back(self.index)
	end
end
---------------------------------Field1v1RankInfoRender End----------------------------------

---------------------------------Field1v1CountRewardRender Star----------------------------------
Field1v1CountRewardRender = Field1v1CountRewardRender or BaseClass(BaseRender)

function Field1v1CountRewardRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickGet, self))
	XUI.AddClickEventListener(self.node_list.root_item, BindTool.Bind(self.ShowItemTips, self))
end

function Field1v1CountRewardRender:OnFlush()
	if self.data == nil or next(self.data) == nil then
		return
	end

	local reward_list = self.data.count_reward
	if reward_list and reward_list[0] then
		local item_cfg = ItemWGData.Instance:GetItemConfig(reward_list[0].item_id)
		if item_cfg then
			local icon_id = item_cfg.icon_id
			local bundle, asset = ResPath.GetItem(icon_id)
			self.node_list.root_item.image:LoadSprite(bundle, asset, function()
				self.node_list.root_item.image:SetNativeSize()
			end)
		end
	end

	self.node_list.desc_text.text.text = self.data.battle_count

	local battle_count = Field1v1WGData.Instance:GetChallengeFieldBattleCount()
	local count_reward_flag = Field1v1WGData.Instance:GetChallengeFieldCountRewardByIndex(self.data.index)

	local is_can_click = battle_count >= self.data.battle_count and count_reward_flag == 0
	self.node_list.btn_get:SetActive(is_can_click)
	self.node_list.img_red:SetActive(is_can_click)
	self.node_list.img_can_get:SetActive(is_can_click)

	local cur_count_index = Field1v1WGData.Instance:GetChallengeFieldBattleCountIndex()
	self.node_list["progress_up"]:SetActive(cur_count_index >= self.data.index)
	self.node_list["progress_down"]:SetActive(cur_count_index > self.data.index)

	local is_ylq = count_reward_flag == 1
	self.node_list.mask:SetActive(is_ylq)
end

function Field1v1CountRewardRender:OnClickGet()
	if self.data == nil or next(self.data) == nil then
		return
	end

	Field1v1WGCtrl.Instance:SendChallengeField(CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_COUNT_REWARD, self.data.index)
end

function Field1v1CountRewardRender:ShowItemTips()
	local battle_count = Field1v1WGData.Instance:GetChallengeFieldBattleCount()
	local count_reward_flag = Field1v1WGData.Instance:GetChallengeFieldCountRewardByIndex(self.data.index)
	local is_can_click = battle_count >= self.data.battle_count and count_reward_flag == 0

	local cur_count_index = Field1v1WGData.Instance:GetChallengeFieldBattleCountIndex()
	local cur_count_reward_flag = Field1v1WGData.Instance:GetChallengeFieldCountRewardByIndex(cur_count_index)
	if cur_count_index < self.data.index and cur_count_reward_flag == 0 and is_can_click then
		TipsSystemManager.Instance:ShowSystemTips(Language.Field1v1.NotGetRewardTips)
	else
		local reward_list = self.data.count_reward
		if reward_list and reward_list[0] then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = reward_list[0].item_id })
		end
	end
end
---------------------------------Field1v1CountRewardRender End----------------------------------
