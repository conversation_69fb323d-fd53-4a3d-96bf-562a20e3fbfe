ChessBoardTreasueData= ChessBoardTreasueData or BaseClass()
ChessBoardTreasueData.MAX_REWARD_COUNT = 25 -- 一轮奖励

function ChessBoardTreasueData:__init()
	if ChessBoardTreasueData.Instance then
		error("[ChessBoardTreasueData] Attempt to create singleton twice!")
		return
	end

    -- 单例
	ChessBoardTreasueData.Instance = self

    self:InitConfig()
    self.grade = 1 -- 档次
    self.round_num = 0
	self.reward_flag = {} -- 奖励领取状态

    RemindManager.Instance:Register(RemindName.RemindChessPieces, BindTool.Bind(self.GetRemind, self))
end

function ChessBoardTreasueData:__delete()
    RemindManager.Instance:UnRegister(RemindName.RemindChessPieces)
    ChessBoardTreasueData.Instance = nil
    self.grade = nil
    self.round_num = nil
	self.reward_flag = nil
end

function ChessBoardTreasueData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_recharge_score_cfg_auto")
    self.other_cfg = cfg.other[1]
    self.cur_openday_cfg = cfg.open_day
    self.cur_reward_cfg = ListToMapList(cfg.reward, "grade", "round")
    self.cur_model_cfg = ListToMap(cfg.display_model, "grade", "round")
    self.cur_show_model_map = ListToMap(cfg.show_model_map, "grade", "index")
    self.open_panel_map = cfg.open_panel_map
end

--全部数据
function ChessBoardTreasueData:SetAllCheckBoardInfo(protocol)
    self.score = protocol.score
    self.grade = protocol.grade
	self.round_num = protocol.round_num
	self.reward_flag = protocol.reward_flag
end

function ChessBoardTreasueData:GetCurScore()
    return self.score or 0
end

function ChessBoardTreasueData:GetCurOpenpanelCfg()
    local open_panel_list = {}
    local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if not IsEmptyTable(self.cur_openday_cfg) then
        for k, v in pairs(self.cur_openday_cfg) do
            if v.start_day <= server_day and server_day <= v.end_day then
                local str_list = Split(v.open_index, "|")
                for i1, open_index in ipairs(str_list) do
                    local cfg = self:GetOpenPanelCfg(tonumber(open_index))
                    if cfg then
                        table.insert(open_panel_list, cfg)
                    end
                end
            end
        end
    end

    return open_panel_list
end

function ChessBoardTreasueData:GetOpenPanelCfg(open_index)
    return self.open_panel_map[open_index]
end

function ChessBoardTreasueData:GetAllRewardState()
	return self.reward_flag[self.round_num] or {}
end

function ChessBoardTreasueData:GetCurRoundNum()
    return self.round_num or 0
end

function ChessBoardTreasueData:GetRemind()
	if self:GetReceiveRemind() then
		return 1
	end

	return 0
end

function ChessBoardTreasueData:GetReceiveRemind()
    local remind = false
    local score = self:GetCurScore()
    local flag_num = self:GetAllRewardState()
    local cur_cfg = self:GetCurRoundRewardCfg()
    if not IsEmptyTable(cur_cfg) and not IsEmptyTable(flag_num) then
        for i = 1, #cur_cfg do
            if score >= cur_cfg[i].need_score then
                if flag_num[i - 1] == 0 then
                    remind = true
                end
            end
        end
    end

    return remind
end

function ChessBoardTreasueData:GetCurChessPieces()
    local score = self:GetCurScore()
    local cur_cfg = self:GetCurRoundRewardCfg()
    if not IsEmptyTable(cur_cfg) then
        for i = #cur_cfg, 0, -1 do
            if score >= cur_cfg[i].need_score then
                return cur_cfg[i].seq
            end
        end
    end

    return 0
end

function ChessBoardTreasueData:GetCurLocation()
    local score = self:GetCurScore()
    local cur_cfg = self:GetCurRoundRewardCfg()
    local location = -1
    if not IsEmptyTable(cur_cfg) then
        for i = 1, #cur_cfg do
            if score < cur_cfg[i].need_score then
                location = i
                break
            elseif score >= cur_cfg[#cur_cfg].need_score then
                location = #cur_cfg + 1
            end
        end
    end

    return location
end


function ChessBoardTreasueData:GetRewardStateBySeq(seq) -- 获取奖励状态
    if not IsEmptyTable(self.reward_flag) then
        local flag_num = self:GetAllRewardState()
        return flag_num and flag_num[seq] == 1
    end
end

function ChessBoardTreasueData:GetOtherCfg()
    return self.other_cfg
end

function ChessBoardTreasueData:GetCurDayExchangeTypeAndRate()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local exchange_type = 0
    local rate_type = 1
    local title_img = ""
    for k, v in ipairs(self.cur_openday_cfg) do
        if open_day >= v.start_day and open_day <= v.end_day then
            exchange_type = v.exchange_type
            rate_type = v.rate_type
            title_img = v.title_img
            break
        end
    end

    return exchange_type, rate_type, title_img
end

function ChessBoardTreasueData:GetCurRoundRewardCfg()
    return (self.cur_reward_cfg[self.grade] or {})[self.round_num]
end

function ChessBoardTreasueData:GetCurRoundmodelCfg()
    --return self.grade, self.cur_model_cfg[self.grade], self.round_num
    local tab_ret = (self.cur_model_cfg[self.grade] or {})[0]
    return tab_ret
end

function ChessBoardTreasueData:GetCurRoundDisplayModelCfg()
    local tb_ret = self.cur_show_model_map[self.grade] or {}
    return tb_ret
end

function ChessBoardTreasueData:GetCurMaxRoundDataList()
    local data_list = {}
    local grade_data_list = self.cur_reward_cfg[self.grade] or {}

    if not IsEmptyTable(grade_data_list) then
        for k, v in pairs(grade_data_list) do
            local round_max_index = #v
            
            data_list[k] = v[round_max_index]
        end
    end

    return data_list
end

function ChessBoardTreasueData:GetCurRoundDataList()
    return self.cur_reward_cfg[self.grade] or {}
end

function ChessBoardTreasueData:GetShowRewardList()
    local reward_list = {}
    local cur_reward_cfg = self.cur_reward_cfg[self.grade] or {}
    local index = 0
    for k, v in pairs(cur_reward_cfg) do
        for k1, v1 in pairs(v) do
            reward_list[index] = v1.item
            index = index + 1
        end
    end

    return reward_list
end

function ChessBoardTreasueData:GetCurRoundBigRewardList()
    local big_reward_list = {}

    local round_cfg = self:GetCurRoundRewardCfg()
    if  IsEmptyTable(round_cfg) then
        return big_reward_list
    end

    for k, v in pairs(round_cfg) do
        if v.grand_prix == 1 then
            table.insert(big_reward_list, v)
        end
    end

    return big_reward_list
end