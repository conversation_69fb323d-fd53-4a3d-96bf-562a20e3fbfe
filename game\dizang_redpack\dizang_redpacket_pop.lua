-- 地藏红包提示弹窗

DiZangRedpacketPop = DiZangRedpacketPop or BaseClass(SafeBaseView)

function DiZangRedpacketPop:__init()
    self:SetMaskBg()
    self.view_layer = UiLayer.Pop
    self.open_source_view = "btn_dizang_redpack"
    self:AddViewResource(0, "uis/view/dizang_redpack_ui_prefab", "layout_dizang_redpacket_pop")
end

function DiZangRedpacketPop:ReleaseCallBack()
    
end

function DiZangRedpacketPop:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.Goto, self))
    ReDelayCall(self, BindTool.Bind(self.ShowEffect, self), 0.25, "show_pop_open_finsh_effect")
end

function DiZangRedpacketPop:OnFlush()
    self.node_list.tips.text.text = Language.DiZangRedPack.PopTip
end

function DiZangRedpacketPop:Goto()
    ViewManager.Instance:Open(GuideModuleName.DiZangRedPackView)
    self:Close()
end

function DiZangRedpacketPop:ShowEffect()
    if self.node_list.effect then
        self.node_list.effect:CustomSetActive(true)
    end

    ReDelayCall(self, function()
        if self.node_list.effect then
            self.node_list.effect:CustomSetActive(false)
        end
    end, 2, "hide_pop_open_finsh_effect")
end