ScreenShotWGData = ScreenShotWGData or BaseClass()

SnapShotShieldType = {
    MainRole = ShieldObjType.MainRole,
    XianLv = "XianLv",
    Teammeat = "Teammeat",
    OtherRole = "OtherRole",
    GuildRole = "GuildRole",
    Pet = ShieldObjType.Pet,
    FightSoul = ShieldObjType.FightSoul,
    Npc = ShieldObjType.Npc,
    Monster = {ShieldObjType.Monster, ShieldObjType.Boss},
    FollowTitle = ShieldObjType.FollowTitle,
    PartBaoJu = ShieldObjType.PartBaoJu,
    Guard = ShieldObjType.Guard,
    PartWing = ShieldObjType.PartWing,
    PartJianling = ShieldObjType.PartJianling,
    PartHalo = ShieldObjType.PartHalo,
    PartMask = ShieldObjType.PartMask,
    PartWaist = ShieldObjType.PartWaist,
    PartTail = ShieldObjType.PartTail,
    PartShouHuan = ShieldObjType.PartShouHuan,
    FollowUI = ShieldObjType.FollowUI,
    PartFoot = ShieldObjType.PartFoot,
    QuFu = "QuFu",
    PartFootTrail = ShieldObjType.PartFootTrail,
    PartSoulRing1 = ShieldObjType.PartSoulRing1,
    PartSoulRing2 = ShieldObjType.PartSoulRing2,
    PartSoulRing3 = ShieldObjType.PartSoulRing3,
    PartSoulRing4 = ShieldObjType.PartSoulRing4,
    PartSoulRing5 = ShieldObjType.PartSoulRing5,
    PartSoulRing6 = ShieldObjType.PartSoulRing6,
    PartSoulRing7 = ShieldObjType.PartSoulRing7,
    PartSoulRing8 = ShieldObjType.PartSoulRing8,
    PartSoulRingSelect = ShieldObjType.PartSoulRingSelect,
}

--改了这里的顺序， 一定要改 flag    策划要求去掉显示，只保留两个字
local ScreenShotConfig = {
    {name = "自己", shield_type = SnapShotShieldType.MainRole, default_flag = 1},
    {name = "仙侣", shield_type = SnapShotShieldType.XianLv, default_flag = 0},
    {name = "队友", shield_type = SnapShotShieldType.Teammeat, default_flag = 0},
    {name = "路人", shield_type = SnapShotShieldType.OtherRole, default_flag = 0},
    {name = "盟友", shield_type = SnapShotShieldType.GuildRole, default_flag = 0},
    {name = "伙伴", shield_type = SnapShotShieldType.Pet, default_flag = 1},
    {name = "NPC", shield_type = SnapShotShieldType.Npc, default_flag = 0},
    {name = "怪物", shield_type = SnapShotShieldType.Monster, default_flag = 0},
    {name = "称号", shield_type = SnapShotShieldType.FollowTitle, default_flag = 0},
    {name = "灵宝", shield_type = SnapShotShieldType.PartBaoJu, default_flag = 1},
    {name = "精灵", shield_type = SnapShotShieldType.Guard, default_flag = 1},
    {name = "仙翼", shield_type = SnapShotShieldType.PartWing, default_flag = 1},
    {name = "背饰", shield_type = SnapShotShieldType.PartJianling, default_flag = 1},
    {name = "魂息", shield_type = SnapShotShieldType.PartHalo, default_flag = 1},
    {name = "脸饰", shield_type = SnapShotShieldType.PartMask, default_flag = 1},
    {name = "腰饰", shield_type = SnapShotShieldType.PartWaist, default_flag = 1},
    --{name = "尾巴", shield_type = SnapShotShieldType.PartTail, default_flag = 1},
    {name = "手环", shield_type = SnapShotShieldType.PartShouHuan, default_flag = 1},
    {name = "足迹", shield_type = SnapShotShieldType.PartFoot, default_flag = 0},
    {name = "区服", shield_type = SnapShotShieldType.QuFu, default_flag = 0},
    {name = "名称", shield_type = SnapShotShieldType.FollowUI, default_flag = 0},
}
--is_have_frame: 是否有相框,要默认选中第一个有相框的
-- SnapShotFrameConfig = {
--     {item_icon = "frame_item_icon1", big_img = "snap_shot_frame1", is_have_frame = false},
--     {item_icon = "frame_item_icon2", big_img = "snap_shot_frame2", is_have_frame = true},
--     -- {item_icon = "frame_item_icon2", big_img = "snap_shot_frame2"},
-- }

function ScreenShotWGData:__init()
     if nil ~= ScreenShotWGData.Instance then
		ErrorLog("[ScreenShotWGData]:Attempt to create singleton twice!")
	end
	ScreenShotWGData.Instance = self

    if PlayerPrefsUtil.HasKey("ScreenShotSettingFlag") then
        local flag = PlayerPrefsUtil.GetString("ScreenShotSettingFlag")
        flag = tonumber(flag)
        if flag and flag > 0 then
            self.flag_list = bit:d2b(flag)
        end
    end

    if not self.flag_list then
        local temp_list = {}
        for i=1,#ScreenShotConfig do
            temp_list[i] = ScreenShotConfig[i].default_flag
        end
        self.flag_list = temp_list
    end

    -- self:CreateFrameTextureList()
end

function ScreenShotWGData:__delete()
    ScreenShotWGData.Instance = nil
    self.frame_texture_list = {}
end

function ScreenShotWGData:GetList()
    return ScreenShotConfig
end

function ScreenShotWGData:ChangeSelect(index, is_select)
    if index > #self.flag_list then
        print_error("[ScreenShotWGData] bug")
        return
    end

    self.flag_list[index] = is_select and 1 or 0
    PlayerPrefsUtil.SetString("ScreenShotSettingFlag", bit:b2d(self.flag_list))
end

function ScreenShotWGData:GetIsSelect(index)
    return self.flag_list[index] or 0
end

function ScreenShotWGData:GetFlagList()
    return self.flag_list
end

-- function ScreenShotWGData:CreateFrameTextureList()
--     self.frame_texture_list = {}
--     for i, v in ipairs(SnapShotFrameConfig) do
--         if v.is_have_frame then
--             local frame_loader = AllocResAsyncLoader(self, "Frame" .. i)
--             local bundle_name, asset_name = ResPath.GetF2RawImagesPNG(v.big_img)
--             frame_loader:Load(bundle_name, asset_name, typeof(UnityEngine.Texture),
--                 function(texture)
--                     self.frame_texture_list[i] = texture
--                 end)
--         end
--     end
-- end

-- function ScreenShotWGData:GetFrameTexture(index)
--     return self.frame_texture_list[index]
-- end