local POS_RATIO = 118
local SPIRIT_CELL_HEIGHT = 108
local SPIRIT_MIN_HEIGHT = 482

-- 圣魂类型
local SPIRIT_TYPE = {
	Attr = 1,	-- 属性
	Multy = 2,	-- 百分比加成
	Skill = 3,	-- 技能
	Awake = 4,  -- 觉醒
}

function HolyBeastsView:SpiritLoadIndexCallBack()
	if not self.spirit_page_item_list then
		self.spirit_page_item_list = AsyncListView.New(SpiritPageItemRender, self.node_list["page_select_list"])
		self.spirit_page_item_list:SetSelectCallBack(BindTool.Bind(self.OnSelectPageHandler, self))
		self.spirit_page_item_list.IsLimitSelectByIndex = function(list, cell_index)
			local page_data = list:GetCellData(cell_index)
			local is_unlock_page = ControlBeastsWGData.Instance:GetSpiritPageIsUnlock(page_data.beast_type, page_data.page)
			if not is_unlock_page then
				TipWGCtrl.Instance:ShowSystemMsg(Language.ContralBeasts.SpiritPageLocked)
				return true
			end
			return false
		end
	end

	if not self.spirit_item_list then
		self.spirit_item_list = {}
		for i = 1, 20 do
			local obj = self.node_list["spirit_cell_content"]:FindObj("spirit_cell_" .. i)
			local cell = HolyBeastSpiritItemRender.New(obj)
			cell:SetIndex(i)
			cell:SetClickCallBack(BindTool.Bind(self.OnSpiritCellSelectCallBack, self))
			self.spirit_item_list[i] = cell
		end
	end

	if not self.cur_attr_list then
		self.cur_attr_list = {}
		local num = self.node_list["cur_attr_content"].transform.childCount
		for i = 1, num do
			local cell = CommonAttrRender.New(self.node_list["cur_attr_content"]:FindObj("attr_" .. i))
			cell:SetAttrNeedMaoHao(true)
			cell:SetAttrValuePrefix("+")
			self.cur_attr_list[i] = cell
		end
	end

	if not self.next_attr_list then
		self.next_attr_list = {}
		local num = self.node_list["next_attr_content"].transform.childCount
		for i = 1, num do
			local cell = CommonAttrRender.New(self.node_list["next_attr_content"]:FindObj("attr_" .. i))
			cell:SetAttrNeedMaoHao(true)
			cell:SetAttrValuePrefix("+")
			self.next_attr_list[i] = cell
		end
	end

	if not self.consume_item_list then
		self.consume_item_list = {}
		for i = 1, 2 do
			self.consume_item_list[i] = ItemCell.New(self.node_list["consume_cell_root_" .. i])
		end
	end

	XUI.AddClickEventListener(self.node_list["btn_spirit_uplevel"], BindTool.Bind(self.OnClickSpiritUpLevelBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_spirit_reset"], BindTool.Bind(self.OnClickSpiritResetBtn, self))

	self.select_page = 1
	self.spirit_select_index = 1
end

function HolyBeastsView:SpiritReleaseCallBack()
	if self.spirit_page_item_list then
		self.spirit_page_item_list:DeleteMe()
		self.spirit_page_item_list = nil
	end

	if self.spirit_item_list then
		for k, v in pairs(self.spirit_item_list) do
			v:DeleteMe()
		end
		self.spirit_item_list = nil
	end

	if self.cur_attr_list then
		for k, v in pairs(self.cur_attr_list) do
			v:DeleteMe()
		end
		self.cur_attr_list = nil
	end

	if self.next_attr_list then
		for k, v in pairs(self.next_attr_list) do
			v:DeleteMe()
		end
		self.next_attr_list = nil
	end

	if self.consume_item_list then
		for k, v in pairs(self.consume_item_list) do
			v:DeleteMe()
		end
		self.consume_item_list = nil
	end

	if self.spirit_reset_alert then
		self.spirit_reset_alert:DeleteMe()
		self.spirit_reset_alert = nil
	end

	self.select_page = nil
	self.spirit_select_data = nil
	self.spirit_select_index = nil
	self.spirit_need_jump_flush = nil
	self.spirit_old_beast_type = nil
end

function HolyBeastsView:SpiritCloseCallBack()

end

function HolyBeastsView:SpiritShowIndexCallBack()
	self.spirit_need_jump_flush = true
end

function HolyBeastsView:SpiritOnFlush(param_t)
	self:FlushPageList()
	self:FlushOtherInfo()
end

function HolyBeastsView:SpiritOnSelectHolyBeast(item)
	if self.spirit_old_beast_type ~= self.select_holy_beast_data.beast_type then
		self.spirit_need_jump_flush = true
		self.spirit_old_beast_type = self.select_holy_beast_data.beast_type
	end
	self:FlushPageList()
	self:FlushOtherInfo()
end

function HolyBeastsView:FlushOtherInfo()
	if not self.select_holy_beast_data then
		return
	end
	local cap = ControlBeastsWGData.Instance:GetHolySpiritTotalCap(self.select_holy_beast_data.beast_type)
	self.node_list["txt_total_cap"].text.text = cap-- string.format(Language.ContralBeasts.SpiritCap, cap)
end

-- 刷新分页
function HolyBeastsView:FlushPageList()
	if not self.select_holy_beast_data then
		return
	end

	local spirit_cfg_list = ControlBeastsWGData.Instance:GetHolySpiritPageList(self.select_holy_beast_data.beast_type)
	self.spirit_page_item_list:SetDataList(spirit_cfg_list)
	if self.spirit_need_jump_flush then
		self.spirit_page_item_list:JumpToIndex(1)
	else
		local is_unlock_page = ControlBeastsWGData.Instance:GetSpiritPageIsUnlock(self.select_holy_beast_data.beast_type, self.select_page - 1)
		self.spirit_page_item_list:JumpToIndex(is_unlock_page and self.select_page or 1)
	end
end

function HolyBeastsView:OnSelectPageHandler(page_cell, index)
	if not page_cell or not page_cell.data then
		return
	end
	self.node_list["txt_spirit_page_name"].text.text = page_cell.data.page_name

	if self.select_page ~= index or self.spirit_need_jump_flush then
		self.select_page = index
		self.spirit_need_jump_flush = false
		self:FlushSpiritList(true)
	else
		self:FlushSpiritList()
	end
end

-- 刷新圣魂格子
function HolyBeastsView:FlushSpiritList(need_jump)
	if not self.select_holy_beast_data then
		return
	end

	local cfg_list = ControlBeastsWGData.Instance:GetHolySpiritCfgByPage(self.select_holy_beast_data.beast_type, self.select_page - 1)
	local max_y = 0
	local max_can_upgrade_y = 0
	for i, v in ipairs(self.spirit_item_list) do
		local data = cfg_list[i]
		if data then
			local pos_data = Split(data.holy_pos, "|")
			local pos_y = pos_data[2] and tonumber(pos_data[2]) or 0
			max_y = pos_y > max_y and pos_y or max_y
			if need_jump then
				local can_up_grade = ControlBeastsWGData.Instance:GetSpiritCanUpGrade(self.select_holy_beast_data.beast_type, data.index)
				if can_up_grade and pos_y > max_can_upgrade_y then
					max_can_upgrade_y = pos_y
				end
			end
		end
		v:SetHolyBeastData(self.select_holy_beast_data)
		v:SetData(cfg_list[i])
	end
	-- 计算高度
	local calc_height = max_y * POS_RATIO + SPIRIT_CELL_HEIGHT
	local target_height = calc_height > SPIRIT_MIN_HEIGHT and calc_height or SPIRIT_MIN_HEIGHT
	RectTransform.SetSizeDeltaXY(self.node_list["spirit_cell_content"].rect, 0, target_height)

	if need_jump then
		self.node_list["spirit_scroll_view"].scroll_rect.verticalNormalizedPosition = max_can_upgrade_y / max_y
		self:OnSpiritCellSelectCallBack(self.spirit_item_list[1])
	else
		self:OnSpiritCellSelectCallBack(self.spirit_item_list[self.spirit_select_index])
	end
end

function HolyBeastsView:OnSpiritCellSelectCallBack(cell)
	if not cell or not cell.data then
		return
	end
	self.spirit_select_data = cell.data
	self.spirit_select_index = cell:GetIndex()

	for i, v in pairs(self.spirit_item_list) do
		v:SetSelectIndex(self.spirit_select_index)
	end
	self:FlushRightInfo()
end

function HolyBeastsView:FlushRightInfo()
	if not self.select_holy_beast_data or not self.spirit_select_data then
		return
	end
	self.cur_spirit_can_uplevel = false

	local spirit_cfg = self.spirit_select_data
	local beast_data = self.select_holy_beast_data.beast_data
	local type = spirit_cfg.type
	self.node_list["txt_spirit_name"].text.text = spirit_cfg.name
	self.node_list["cur_attr_part"]:SetActive(type == SPIRIT_TYPE.Attr)
	self.node_list["next_attr_part"]:SetActive(type == SPIRIT_TYPE.Attr)
	self.node_list["txt_cur_desc"].text.text = ""
	self.node_list["txt_next_desc"].text.text = ""

	local beast_type = self.select_holy_beast_data.beast_type
	local level = ControlBeastsWGData.Instance:GetHolySpiritLevel(beast_type, spirit_cfg.index)
	local cur_level_cfg = ControlBeastsWGData.Instance:GetHolySpiritLevelCfg(beast_type, spirit_cfg.index, level)
	local next_level_cfg = ControlBeastsWGData.Instance:GetHolySpiritLevelCfg(beast_type, spirit_cfg.index, level + 1)
	if not cur_level_cfg then return end
	local is_max_level = next_level_cfg == nil
	self.node_list["flag_max_level"]:SetActive(is_max_level)
	self.node_list["fgx"]:SetActive(not is_max_level)
	self.node_list["upgrade_part"]:SetActive(not is_max_level)
	self.node_list["txt_upgrade_tips"].text.text = ""

	if level == 0 then
		self.node_list["txt_cur_desc"].text.text = Language.ContralBeasts.SpiritNoAddition
	end
	-- if next_level_cfg == nil then
	-- 	self.node_list["txt_next_desc"].text.text = Language.ContralBeasts.SpiritMaxLevel
	-- end

	if type == SPIRIT_TYPE.Attr then
		self.node_list["cur_attr_part"]:SetActive(level ~= 0)
		self.node_list["next_attr_part"]:SetActive(next_level_cfg ~= nil)
		if level ~= 0 then
			local cur_attr_list = EquipWGData.GetSortAttrListByTypeCfg(cur_level_cfg, "attr_id", "attr_value", 0, 3)
			for i, v in ipairs(self.cur_attr_list) do
				v:SetData(cur_attr_list[i])
			end
		end
		if next_level_cfg ~= nil then
			local next_attr_list = EquipWGData.GetSortAttrListByTypeCfg(next_level_cfg, "attr_id", "attr_value", 0, 3)
			for i, v in ipairs(self.next_attr_list) do
				v:SetData(next_attr_list[i])
			end
		end
	elseif type == SPIRIT_TYPE.Multy or type == SPIRIT_TYPE.Awake then
		if level ~= 0 then
			local multy_per = cur_level_cfg.param0 / 10000  -- 加成万分比
			self.node_list["txt_cur_desc"].text.text = string.format(Language.ContralBeasts.SpiritMultyType, multy_per * 100)
		end
		if next_level_cfg ~= nil then
			local multy_per = next_level_cfg.param0 / 10000  -- 加成万分比
			self.node_list["txt_next_desc"].text.text = string.format(Language.ContralBeasts.SpiritMultyType, multy_per * 100)
		end
	elseif type == SPIRIT_TYPE.Skill then
		if level ~= 0 then
			local skill_cfg = ControlBeastsWGData.Instance:GetHolySpiritSkillCfg(beast_type, cur_level_cfg.param0)
			self.node_list["txt_cur_desc"].text.text = skill_cfg.skill_desc2
		end
		if next_level_cfg ~= nil then
			local skill_cfg = ControlBeastsWGData.Instance:GetHolySpiritSkillCfg(beast_type, next_level_cfg.param0)
			self.node_list["txt_next_desc"].text.text = skill_cfg.skill_desc2
		end
	end

	-- 升级部分
	if not is_max_level then
		local is_upgrade_limit = false
		local upgrade_limit_str = ""

		local pre_index_list = Split(spirit_cfg.pre_index, "|")
		local pre_param_list = Split(spirit_cfg.pre_param, "|")

		for i = 1, #pre_index_list do
			local pre_index = tonumber(pre_index_list[i])
			local pre_param = tonumber(pre_param_list[i])
			local is_limit
			if pre_index == -1 then --判断总等级
				local total_level = ControlBeastsWGData.Instance:GetTotalLevelByPage(beast_type, spirit_cfg.page)
				is_limit = total_level < pre_param
				if i ~= 1 then
					upgrade_limit_str = upgrade_limit_str .. "\n"
				end
				local temp_str = string.format(Language.ContralBeasts.SpiritUpgradeLimit2, pre_name,  pre_param)
				temp_str = string.format(Language.ContralBeasts.SpiritUpgradeLimit1, pre_param)
				temp_str = temp_str .. string.format("\n(%d/%d)", total_level, pre_param)
				local color = is_limit and COLOR3B.RED or COLOR3B.GREEN
				temp_str = ToColorStr(temp_str, color)
				upgrade_limit_str = upgrade_limit_str .. temp_str
			else -- 判断父节点
				local parent_level = ControlBeastsWGData.Instance:GetHolySpiritLevel(beast_type, pre_index)
				is_limit = parent_level < pre_param
				if i ~= 1 then
					upgrade_limit_str = upgrade_limit_str .. "\n"
				end
				local pre_spirit_cfg = ControlBeastsWGData.Instance:GetHolySpiritCfg(beast_type, pre_index)
				local pre_name = pre_spirit_cfg and pre_spirit_cfg.name or ""
				local temp_str = string.format(Language.ContralBeasts.SpiritUpgradeLimit2, pre_name,  pre_param)
				local color = is_limit and COLOR3B.RED or COLOR3B.GREEN
				temp_str = ToColorStr(temp_str, color)
				upgrade_limit_str = upgrade_limit_str .. temp_str
			end
			if not is_upgrade_limit and is_limit then
				is_upgrade_limit = true
			end
		end

		self.node_list["txt_upgrade_tips"]:SetActive(is_upgrade_limit)
		self.node_list["txt_upgrade_tips"].text.text = upgrade_limit_str
		self.node_list["upgrade_part"]:SetActive(not is_upgrade_limit)

		if not is_upgrade_limit then
			self.cur_spirit_can_uplevel = true
			for i, v in ipairs(self.consume_item_list) do
				local item_id = cur_level_cfg["cost_item_id" .. (i - 1)]
				if item_id > 0 then
					local cost_num = cur_level_cfg["cost_item_num" .. (i - 1)]
					local had_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
					local color = had_num >= cost_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
					v:SetData({item_id = item_id})
					v:SetRightBottomTextVisible(true)
					v:SetRightBottomColorText(string.format("%s/%s", had_num, cost_num), color)
					if had_num < cost_num then
						self.cur_spirit_can_uplevel = false
					end
				end
				self.node_list["consume_cell_root_" .. i]:SetActive(item_id > 0)
			end
		end
	end
	local remind = self.select_holy_beast_data.is_contracted and self.cur_spirit_can_uplevel
	self.node_list["spirit_uplevel_remind"]:SetActive(remind)
end

function HolyBeastsView:OnClickSpiritUpLevelBtn()
	if not self.select_holy_beast_data or not self.spirit_select_data then
		return
	end

	if not self.select_holy_beast_data.is_contracted then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ContralBeasts.SpiritBeastNoLink)
		return
	end

	if self.cur_spirit_can_uplevel then
		local bag_id, index = self.select_holy_beast_data.bag_id - 1, self.spirit_select_data.index
		ControlBeastsWGCtrl.Instance:SendOperateTypeHolySpiritUpLevel(bag_id, index)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.ContralBeasts.SpiritItemNotEnough)
	end
end

function HolyBeastsView:OnClickSpiritResetBtn()
	if not self.select_holy_beast_data or not self.spirit_select_data then
		return
	end

	local is_upgraded = false
	for k, v in pairs(self.select_holy_beast_data.beast_data.server_data.holy_spirit_level_list) do
		if v > 0 then
			is_upgraded = true
			break
		end
	end

	if is_upgraded then
		local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
		local reset_cost = base_cfg.reset_holy_spirit_cost_gold
		--确认消耗
		if not self.spirit_reset_alert then
			self.spirit_reset_alert = Alert.New()
		end
		local func = function ()
			local enough = RoleWGData.Instance:GetIsEnoughUseGold(reset_cost)
			if enough then
				local bag_id = self.select_holy_beast_data.bag_id - 1
				ControlBeastsWGCtrl.Instance:SendOperateTypeHolySpiritReset(bag_id)
			else
				VipWGCtrl.Instance:OpenTipNoGold()
			end 
		end
		local str = string.format(Language.ContralBeasts.SpiritResetTip, reset_cost)
		self.spirit_reset_alert:SetLableString(str)
		self.spirit_reset_alert:SetOkFunc(func)
		self.spirit_reset_alert:Open()
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.ContralBeasts.SpiritNoUpgrade)
	end
end

-------------------------------------------
-- 分页Item
-------------------------------------------
SpiritPageItemRender = SpiritPageItemRender or BaseClass(BaseRender)
function SpiritPageItemRender:OnFlush()
	if not self.data then
		return
	end
	self.node_list["txt_normal"].text.text = self.data.page_name
	self.node_list["txt_hl"].text.text = self.data.page_name
	local remind = ControlBeastsWGData.Instance:GetHolySpiritPageRed(self.data.beast_type, self.data.page)
	self.node_list["img_remind"]:SetActive(remind)
end

function SpiritPageItemRender:OnSelectChange(is_select)
	self.node_list["img_hl"]:SetActive(is_select)
end

-------------------------------------------
-- 圣兽圣魂Item
-------------------------------------------
HolyBeastSpiritItemRender = HolyBeastSpiritItemRender or BaseClass(BaseRender)

function HolyBeastSpiritItemRender:LoadCallBack()

end

function HolyBeastSpiritItemRender:ReleaseCallBack()
	self.holy_beast_data = nil
end

function HolyBeastSpiritItemRender:OnFlush()
	if not self.data then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)

	local pos_data = Split(self.data.holy_pos, "|")
	pos_data[1], pos_data[2] = tonumber(pos_data[1]), tonumber(pos_data[2])
	local pos_x, pos_y = pos_data[1] * POS_RATIO, pos_data[2] * POS_RATIO
	self.view.rect.anchoredPosition = u3dpool.vec2(pos_x, pos_y)

	local cur_beast_type = self.holy_beast_data.beast_type
	-- 设置连线
	local parent_list = Split(self.data.parent_index, "|")
	parent_list[1], parent_list[2] = tonumber(parent_list[1]), tonumber(parent_list[2])
	local had_parent = parent_list[1] ~= -1
	local show_left, show_right, show_up, show_down
	if had_parent then
		for i = 1, #parent_list do
			local parent_pos_data
			local parent_cfg = ControlBeastsWGData.Instance:GetHolySpiritCfg(cur_beast_type, parent_list[i])
			parent_pos_data = parent_cfg and Split(parent_cfg.holy_pos, "|") or {0, 0}
			parent_pos_data[1], parent_pos_data[2] = tonumber(parent_pos_data[1]), tonumber(parent_pos_data[2])
			if not show_left then
				show_left = parent_pos_data[1] < pos_data[1]
			end
			if not show_right then
				show_right = parent_pos_data[1] > pos_data[1]
			end
			if not show_up then
				show_up = parent_pos_data[2] > pos_data[2]
			end
			if not show_down then
				show_down = parent_pos_data[2] < pos_data[2]
			end
		end
	end

	self.node_list["line_left"]:SetActive(had_parent and show_left)
	self.node_list["line_right"]:SetActive(had_parent and show_right)
	self.node_list["line_up"]:SetActive(had_parent and show_up)
	self.node_list["line_down"]:SetActive(had_parent and show_down)

	for i = 1, 4 do
		self.node_list["type_" .. i]:SetActive(i == self.data.type)
	end
	
	if self.data.type == SPIRIT_TYPE.Attr then
		local bundle, asset = ResPath.GetControlBeastsImg("a3_hs_cs_tb_" .. self.data.icon_type)
		self.node_list["attr_type_icon"].image:LoadSprite(bundle, asset)
	elseif self.data.type == SPIRIT_TYPE.Skill then
		local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.holy_beast_data.item_id)
		local icon_name = string.format("a3_hs_head_%s", beast_cfg and beast_cfg.chang_head or 30450)
		self.node_list["beast_icon"].image:LoadSprite(ResPath.GetControlBeastsImg(icon_name))
	end

	self.node_list["text_bg"]:SetActive(self.data.type ~= SPIRIT_TYPE.Awake)
	local level = ControlBeastsWGData.Instance:GetHolySpiritLevel(cur_beast_type, self.data.index)
	local level_cfg_list = ControlBeastsWGData.Instance:GetHolySpiritLevelCfgList(cur_beast_type, self.data.index)
	local max_level = #level_cfg_list
	self.node_list["txt_level"].text.text = string.format("%s/%s", level, max_level)

	local can_up_grade = ControlBeastsWGData.Instance:GetSpiritCanUpGrade(cur_beast_type, self.data.index)
	self.node_list["img_remind"]:SetActive(self.data.type == SPIRIT_TYPE.Awake and can_up_grade)
	self.node_list["remind_effect"]:SetActive(self.data.type ~= SPIRIT_TYPE.Awake and can_up_grade)
end

function HolyBeastSpiritItemRender:SetHolyBeastData(holy_beast_data)
	self.holy_beast_data = holy_beast_data
end

function HolyBeastSpiritItemRender:OnSelectChange(is_select)
	self.node_list["img_selected"]:SetActive(self.data and self.data.type ~= SPIRIT_TYPE.Awake and is_select)
	self.node_list["img_selected2"]:SetActive(self.data and self.data.type == SPIRIT_TYPE.Awake and is_select)
end