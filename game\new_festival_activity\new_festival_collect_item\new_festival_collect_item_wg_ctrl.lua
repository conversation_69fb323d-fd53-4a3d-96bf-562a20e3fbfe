require("game/new_festival_activity/new_festival_collect_item/new_festival_collect_item_wg_data")

NewFestivalCollectItemWGCtrl = NewFestivalCollectItemWGCtrl or BaseClass(BaseWGCtrl)

function NewFestivalCollectItemWGCtrl:__init()
	if NewFestivalCollectItemWGCtrl.Instance then
		error("[NewFestivalCollectItemWGCtrl]:Attempt to create singleton twice!")
	end

    NewFestivalCollectItemWGCtrl.Instance = self

    self.data = NewFestivalCollectItemWGData.New()

    self:RegisterAllProtocols()
end

function NewFestivalCollectItemWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    NewFestivalCollectItemWGCtrl.Instance = nil
end

function NewFestivalCollectItemWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCNAItemCollectAllInfo, "OnSCNAItemCollectAllInfo")
    self:RegisterProtocol(SCNAItemCollectCountInfo, "OnSCNAItemCollectCountInfo")
end

function NewFestivalCollectItemWGCtrl:OnSCNAItemCollectAllInfo(protocol)
    self.data:SetNewJRCollectItemAllInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2349)
    RemindManager.Instance:Fire(RemindName.NewFestivalCollectItem)
end

function NewFestivalCollectItemWGCtrl:OnSCNAItemCollectCountInfo(protocol)
    self.data:SetNewJRCollectItemCountInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2349)
    RemindManager.Instance:Fire(RemindName.NewFestivalCollectItem)
end