-- Y-运营活动-月下求灯.xls
local item_table={
[1]={item_id=26356,num=1,is_bind=1},
[2]={item_id=26358,num=1,is_bind=1},
[3]={item_id=26359,num=1,is_bind=1},
[4]={item_id=26502,num=1,is_bind=1},
[5]={item_id=26517,num=1,is_bind=1},
[6]={item_id=26500,num=1,is_bind=1},
[7]={item_id=26515,num=1,is_bind=1},
[8]={item_id=22531,num=1,is_bind=1},
[9]={item_id=26127,num=1,is_bind=1},
[10]={item_id=26126,num=1,is_bind=1},
[11]={item_id=26125,num=1,is_bind=1},
[12]={item_id=26415,num=2,is_bind=1},
[13]={item_id=22010,num=1,is_bind=1},
[14]={item_id=26200,num=3,is_bind=1},
[15]={item_id=26203,num=3,is_bind=1},
[16]={item_id=26346,num=2,is_bind=1},
[17]={item_id=26347,num=1,is_bind=1},
[18]={item_id=26348,num=1,is_bind=1},
[19]={item_id=26349,num=2,is_bind=1},
[20]={item_id=26350,num=1,is_bind=1},
[21]={item_id=26351,num=1,is_bind=1},
[22]={item_id=44182,num=1,is_bind=1},
[23]={item_id=44183,num=1,is_bind=1},
[24]={item_id=44184,num=1,is_bind=1},
[25]={item_id=44185,num=1,is_bind=1},
[26]={item_id=26161,num=1,is_bind=1},
[27]={item_id=38710,num=1,is_bind=1},
[28]={item_id=48117,num=1,is_bind=1},
[29]={item_id=44074,num=1,is_bind=1},
[30]={item_id=27410,num=1,is_bind=1},
[31]={item_id=26357,num=1,is_bind=1},
[32]={item_id=26360,num=1,is_bind=1},
[33]={item_id=26355,num=1,is_bind=1},
[34]={item_id=48120,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
open_day={
{}
},

open_day_meta_table_map={
},
mode={
{},
{mode=2,times=50,cost_item_num=50,},
{mode=3,times=100,cost_item_num=100,}
},

mode_meta_table_map={
},
reward_pool={
{},
{seq=1,item=item_table[1],},
{seq=2,item=item_table[2],},
{seq=3,item=item_table[3],},
{seq=4,item=item_table[4],},
{seq=5,item=item_table[5],},
{seq=6,item=item_table[6],},
{seq=7,item=item_table[7],},
{seq=8,item=item_table[8],},
{seq=9,item=item_table[9],},
{seq=10,item=item_table[10],},
{seq=11,item=item_table[11],},
{seq=12,item=item_table[12],},
{seq=13,item=item_table[13],},
{seq=14,item=item_table[14],},
{seq=15,item=item_table[15],},
{seq=16,item=item_table[16],},
{seq=17,item=item_table[17],},
{seq=18,item=item_table[18],},
{seq=19,item=item_table[19],},
{seq=20,item=item_table[20],},
{seq=21,item=item_table[21],},
{seq=22,item=item_table[22],},
{seq=23,item=item_table[23],},
{seq=24,item=item_table[24],},
{seq=25,item=item_table[25],}
},

reward_pool_meta_table_map={
},
special_reward_pool={
{times=2999,},
{seq=1,item=item_table[26],},
{seq=2,item=item_table[27],reward_big_show=3,},
{seq=3,item=item_table[28],times=799,},
{seq=4,item=item_table[29],},
{seq=5,item=item_table[30],times=899,},
{seq=6,item=item_table[31],times=100,},
{seq=7,item=item_table[32],}
},

special_reward_pool_meta_table_map={
[5]=4,	-- depth:1
[8]=7,	-- depth:1
},
item_random_desc={
{random_count=0.1,},
{number=1,item_name="魂之圣晶",item_id=26161,random_count=0.2,},
{number=2,item_name="绝圣斩魂",item_id=38710,random_count=0.3,},
{number=3,item_name="3星十万年魂骨自选包",item_id=48117,random_count=0.13,},
{number=4,item_name="3星十万年魂骨随机包",item_id=44074,random_count=0.15,},
{number=5,item_name="兽灵髓",item_id=27410,random_count=0.2,},
{number=6,item_name="诛天石",item_id=26357,random_count=0.16,},
{number=7,item_name="诛法石",item_id=26360,random_count=0.18,},
{number=8,item_name="飞天石",item_id=26355,random_count=2,},
{number=9,item_name="凌天石",item_id=26356,},
{number=10,item_name="青法石",item_id=26358,random_count=4,},
{number=11,item_name="凌法石",item_id=26359,random_count=5,},
{number=12,item_name="3级攻击玉魄",item_id=26502,random_count=1.54,},
{number=13,item_name="3级生命玉魄",item_id=26517,random_count=1.54,},
{number=14,item_name="1级攻击玉魄",item_id=26500,random_count=6,},
{number=15,item_name="1级生命玉魄",item_id=26515,random_count=6,},
{number=16,item_name="幻兽经验(中)",item_id=22531,random_count=8,},
{number=17,item_name="豪华游艇",item_id=26127,},
{number=18,item_name="火箭",item_id=26126,random_count=4,},
{number=19,item_name="香槟",item_id=26125,random_count=5,},
{number=20,item_name="洗炼瓶",item_id=26415,random_count=2,},
{number=21,item_name="100万铜钱",item_id=22010,},
{number=22,item_name="淬火灰矿",item_id=26200,random_count=8,},
{number=23,item_name="淬火灰矿·饰品",item_id=26203,random_count=8,},
{number=24,item_name="良品·背饰器灵",item_id=26346,random_count=8,},
{number=25,item_name="珍宝·背饰器灵",item_id=26347,},
{number=26,item_name="秘藏·背饰器灵",item_id=26348,random_count=0.1,},
{number=27,item_name="良品·法器器灵",item_id=26349,random_count=8,},
{number=28,item_name="珍宝·法器器灵",item_id=26350,},
{number=29,item_name="秘藏·法器器灵",item_id=26351,random_count=0.1,},
{number=30,item_name="绿色秘籍礼包",item_id=44182,},
{number=31,item_name="蓝色秘籍礼包",item_id=44183,random_count=2.5,},
{number=32,item_name="紫色秘籍礼包",item_id=44184,random_count=0.6,},
{number=33,item_name="橙色秘籍礼包",item_id=44185,random_count=0.2,}
},

item_random_desc_meta_table_map={
},
baodi={
{},
{times=1500,index_list=2,},
{times=3000,index_list=1,},
{times=5250,index_list="2|1",},
{times=6750,index_list=0,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=2,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{activity_day=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{activity_day=3,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{activity_day=2,},
{grade=6,},
{grade=6,},
{activity_day=2,},
{grade=6,},
{grade=6,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=6,},
{grade=6,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{activity_day=3,},
{activity_day=3,},
{grade=7,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=8,},
{grade=8,},
{activity_day=4,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{grade=9,},
{activity_day=3,},
{activity_day=3,},
{grade=9,},
{grade=9,},
{activity_day=4,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{activity_day=3,},
{activity_day=3,},
{grade=11,},
{activity_day=4,},
{grade=11,},
{grade=11,},
{grade=11,},
{activity_day=4,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{activity_day=3,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,},
{grade=12,}
},

baodi_meta_table_map={
[226]=6,	-- depth:1
[111]=11,	-- depth:1
[116]=111,	-- depth:2
[106]=116,	-- depth:3
[206]=106,	-- depth:4
[51]=111,	-- depth:2
[186]=206,	-- depth:5
[46]=186,	-- depth:6
[191]=186,	-- depth:6
[216]=206,	-- depth:5
[96]=216,	-- depth:6
[196]=96,	-- depth:7
[91]=96,	-- depth:7
[66]=46,	-- depth:7
[86]=66,	-- depth:8
[211]=91,	-- depth:8
[71]=211,	-- depth:9
[76]=71,	-- depth:10
[56]=76,	-- depth:11
[36]=56,	-- depth:12
[156]=36,	-- depth:13
[131]=71,	-- depth:10
[231]=131,	-- depth:11
[171]=231,	-- depth:12
[166]=171,	-- depth:13
[26]=166,	-- depth:14
[176]=166,	-- depth:14
[126]=26,	-- depth:15
[236]=176,	-- depth:15
[31]=26,	-- depth:15
[146]=126,	-- depth:16
[136]=236,	-- depth:16
[151]=146,	-- depth:17
[202]=2,	-- depth:1
[163]=3,	-- depth:1
[122]=202,	-- depth:2
[85]=5,	-- depth:1
[84]=4,	-- depth:1
[83]=163,	-- depth:2
[82]=122,	-- depth:3
[203]=83,	-- depth:3
[162]=82,	-- depth:4
[164]=84,	-- depth:2
[143]=203,	-- depth:4
[145]=85,	-- depth:2
[144]=164,	-- depth:3
[142]=162,	-- depth:5
[102]=142,	-- depth:6
[103]=143,	-- depth:5
[104]=144,	-- depth:4
[105]=145,	-- depth:3
[185]=105,	-- depth:4
[184]=104,	-- depth:5
[183]=103,	-- depth:6
[125]=185,	-- depth:5
[124]=184,	-- depth:6
[123]=183,	-- depth:7
[165]=125,	-- depth:6
[182]=102,	-- depth:7
[204]=124,	-- depth:7
[19]=4,	-- depth:1
[45]=165,	-- depth:7
[225]=45,	-- depth:8
[42]=182,	-- depth:8
[24]=204,	-- depth:8
[23]=123,	-- depth:8
[224]=24,	-- depth:9
[223]=23,	-- depth:9
[222]=42,	-- depth:9
[25]=225,	-- depth:9
[205]=25,	-- depth:10
[22]=222,	-- depth:10
[20]=5,	-- depth:1
[44]=224,	-- depth:10
[18]=3,	-- depth:1
[43]=223,	-- depth:10
[62]=22,	-- depth:11
[7]=2,	-- depth:1
[8]=18,	-- depth:2
[9]=19,	-- depth:2
[10]=20,	-- depth:2
[17]=7,	-- depth:2
[64]=44,	-- depth:11
[65]=205,	-- depth:11
[13]=8,	-- depth:3
[63]=43,	-- depth:11
[14]=9,	-- depth:3
[15]=10,	-- depth:3
[12]=17,	-- depth:3
[175]=15,	-- depth:4
[238]=18,	-- depth:2
[174]=14,	-- depth:4
[177]=17,	-- depth:3
[178]=238,	-- depth:3
[207]=7,	-- depth:2
[169]=174,	-- depth:5
[172]=177,	-- depth:4
[170]=175,	-- depth:5
[237]=177,	-- depth:4
[168]=178,	-- depth:4
[167]=172,	-- depth:5
[160]=20,	-- depth:2
[159]=19,	-- depth:2
[173]=168,	-- depth:5
[235]=175,	-- depth:5
[229]=169,	-- depth:6
[233]=173,	-- depth:6
[208]=168,	-- depth:5
[209]=229,	-- depth:7
[210]=170,	-- depth:6
[212]=172,	-- depth:5
[213]=208,	-- depth:6
[214]=209,	-- depth:8
[200]=160,	-- depth:3
[199]=159,	-- depth:3
[198]=178,	-- depth:4
[197]=237,	-- depth:5
[195]=200,	-- depth:4
[215]=195,	-- depth:5
[194]=199,	-- depth:4
[193]=198,	-- depth:5
[192]=197,	-- depth:6
[217]=197,	-- depth:6
[218]=198,	-- depth:5
[219]=199,	-- depth:4
[220]=215,	-- depth:6
[190]=195,	-- depth:5
[189]=194,	-- depth:5
[188]=193,	-- depth:6
[187]=192,	-- depth:7
[227]=187,	-- depth:8
[228]=188,	-- depth:7
[180]=220,	-- depth:7
[179]=219,	-- depth:5
[230]=190,	-- depth:6
[232]=227,	-- depth:9
[234]=194,	-- depth:5
[158]=218,	-- depth:6
[120]=180,	-- depth:8
[155]=215,	-- depth:6
[58]=158,	-- depth:7
[59]=179,	-- depth:6
[60]=120,	-- depth:9
[67]=227,	-- depth:9
[68]=228,	-- depth:8
[69]=189,	-- depth:6
[70]=230,	-- depth:7
[57]=217,	-- depth:7
[72]=67,	-- depth:10
[74]=69,	-- depth:7
[75]=70,	-- depth:8
[77]=72,	-- depth:11
[78]=68,	-- depth:9
[79]=74,	-- depth:8
[80]=75,	-- depth:9
[87]=67,	-- depth:10
[73]=78,	-- depth:10
[55]=75,	-- depth:9
[54]=74,	-- depth:8
[53]=73,	-- depth:11
[27]=87,	-- depth:11
[28]=68,	-- depth:9
[29]=69,	-- depth:7
[30]=70,	-- depth:8
[32]=27,	-- depth:12
[33]=28,	-- depth:10
[34]=29,	-- depth:8
[35]=30,	-- depth:9
[37]=32,	-- depth:13
[38]=33,	-- depth:11
[39]=34,	-- depth:9
[40]=35,	-- depth:10
[47]=27,	-- depth:12
[48]=28,	-- depth:10
[49]=29,	-- depth:8
[50]=30,	-- depth:9
[52]=47,	-- depth:13
[88]=48,	-- depth:11
[89]=49,	-- depth:9
[90]=50,	-- depth:10
[92]=52,	-- depth:14
[129]=89,	-- depth:10
[130]=90,	-- depth:11
[132]=92,	-- depth:15
[133]=33,	-- depth:11
[134]=129,	-- depth:11
[135]=130,	-- depth:12
[137]=132,	-- depth:16
[138]=133,	-- depth:12
[139]=134,	-- depth:12
[140]=135,	-- depth:13
[147]=47,	-- depth:13
[148]=88,	-- depth:12
[149]=129,	-- depth:11
[150]=130,	-- depth:12
[152]=147,	-- depth:14
[153]=148,	-- depth:13
[154]=149,	-- depth:12
[128]=148,	-- depth:13
[157]=152,	-- depth:15
[127]=147,	-- depth:14
[119]=139,	-- depth:13
[93]=153,	-- depth:14
[94]=154,	-- depth:13
[95]=135,	-- depth:13
[97]=157,	-- depth:16
[98]=93,	-- depth:15
[99]=94,	-- depth:14
[100]=95,	-- depth:14
[107]=127,	-- depth:15
[108]=128,	-- depth:14
[109]=119,	-- depth:14
[110]=150,	-- depth:13
[112]=107,	-- depth:16
[113]=108,	-- depth:15
[114]=109,	-- depth:15
[115]=110,	-- depth:14
[117]=112,	-- depth:17
[118]=113,	-- depth:16
[239]=99,	-- depth:15
[240]=100,	-- depth:15
},
reward_pool_choose={
{},
{index=1,seq_list="3|4|5|6",},
{index=2,seq_list="7|8|9|10|11",},
{index=3,seq_list="12|13|14|15|16|17",},
{index=4,seq_list="18|19|20|21|22|23",},
{index=5,seq_list="24|25|26|27|28|29|30|31",},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=2,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{activity_day=3,},
{activity_day=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{grade=3,},
{activity_day=4,},
{grade=3,},
{grade=3,},
{grade=3,},
{activity_day=4,},
{activity_day=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{grade=4,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=4,},
{grade=4,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{activity_day=2,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{grade=5,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=5,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{activity_day=2,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{activity_day=3,},
{grade=6,},
{activity_day=3,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{grade=6,},
{activity_day=4,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{activity_day=2,},
{grade=7,},
{grade=7,},
{grade=7,},
{grade=7,},
{activity_day=2,},
{grade=7,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=7,},
{activity_day=4,},
{activity_day=4,},
{activity_day=4,},
{grade=7,},
{grade=7,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{grade=8,},
{activity_day=3,},
{activity_day=3,},
{grade=8,},
{activity_day=3,},
{activity_day=3,},
{activity_day=4,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=8,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{activity_day=2,},
{grade=9,},
{grade=9,},
{grade=9,},
{activity_day=2,},
{grade=9,},
{grade=9,},
{activity_day=3,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=9,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{activity_day=2,},
{grade=10,},
{activity_day=2,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=10,},
{activity_day=3,},
{activity_day=3,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=10,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{activity_day=2,},
{grade=11,},
{activity_day=2,},
{activity_day=2,},
{grade=11,},
{grade=11,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,},
{grade=11,}
},

reward_pool_choose_meta_table_map={
[199]=7,	-- depth:1
[229]=217,	-- depth:1
[253]=229,	-- depth:2
[85]=253,	-- depth:3
[43]=19,	-- depth:1
[223]=229,	-- depth:2
[91]=43,	-- depth:2
[205]=85,	-- depth:4
[235]=91,	-- depth:3
[211]=235,	-- depth:4
[109]=205,	-- depth:5
[37]=109,	-- depth:6
[103]=109,	-- depth:6
[55]=103,	-- depth:7
[247]=55,	-- depth:8
[67]=55,	-- depth:8
[61]=67,	-- depth:9
[115]=67,	-- depth:9
[259]=115,	-- depth:10
[127]=247,	-- depth:9
[163]=259,	-- depth:11
[175]=127,	-- depth:10
[133]=127,	-- depth:10
[181]=133,	-- depth:11
[157]=181,	-- depth:12
[187]=181,	-- depth:12
[151]=157,	-- depth:13
[79]=151,	-- depth:14
[31]=79,	-- depth:15
[139]=187,	-- depth:13
[147]=3,	-- depth:1
[198]=6,	-- depth:1
[171]=147,	-- depth:2
[196]=4,	-- depth:1
[195]=171,	-- depth:3
[222]=198,	-- depth:2
[126]=222,	-- depth:3
[221]=5,	-- depth:1
[220]=196,	-- depth:2
[170]=2,	-- depth:1
[219]=195,	-- depth:4
[218]=170,	-- depth:2
[173]=221,	-- depth:2
[174]=126,	-- depth:4
[98]=218,	-- depth:3
[125]=173,	-- depth:3
[99]=219,	-- depth:5
[100]=220,	-- depth:3
[101]=125,	-- depth:4
[102]=174,	-- depth:5
[194]=98,	-- depth:4
[124]=100,	-- depth:4
[123]=99,	-- depth:6
[122]=194,	-- depth:5
[150]=102,	-- depth:6
[149]=101,	-- depth:5
[148]=124,	-- depth:5
[146]=122,	-- depth:6
[197]=149,	-- depth:6
[172]=148,	-- depth:6
[244]=172,	-- depth:7
[24]=6,	-- depth:1
[245]=197,	-- depth:7
[246]=150,	-- depth:7
[54]=246,	-- depth:8
[53]=245,	-- depth:8
[16]=4,	-- depth:1
[52]=244,	-- depth:8
[51]=123,	-- depth:7
[17]=5,	-- depth:1
[18]=24,	-- depth:2
[20]=2,	-- depth:1
[50]=146,	-- depth:7
[21]=3,	-- depth:1
[22]=16,	-- depth:2
[23]=17,	-- depth:2
[243]=51,	-- depth:8
[242]=50,	-- depth:8
[26]=242,	-- depth:9
[15]=21,	-- depth:2
[8]=20,	-- depth:2
[9]=15,	-- depth:3
[77]=53,	-- depth:9
[10]=22,	-- depth:3
[76]=52,	-- depth:9
[75]=243,	-- depth:9
[11]=23,	-- depth:3
[12]=18,	-- depth:3
[30]=54,	-- depth:9
[74]=26,	-- depth:10
[29]=77,	-- depth:10
[14]=8,	-- depth:3
[28]=76,	-- depth:10
[27]=75,	-- depth:10
[78]=30,	-- depth:10
[192]=24,	-- depth:2
[188]=20,	-- depth:2
[190]=22,	-- depth:3
[191]=23,	-- depth:3
[189]=21,	-- depth:2
[232]=16,	-- depth:2
[183]=189,	-- depth:3
[185]=191,	-- depth:4
[184]=232,	-- depth:3
[262]=190,	-- depth:4
[182]=188,	-- depth:3
[180]=192,	-- depth:3
[179]=185,	-- depth:5
[178]=184,	-- depth:4
[177]=183,	-- depth:4
[176]=182,	-- depth:4
[186]=180,	-- depth:4
[261]=189,	-- depth:3
[255]=261,	-- depth:4
[258]=186,	-- depth:5
[214]=262,	-- depth:5
[215]=191,	-- depth:4
[216]=192,	-- depth:3
[240]=216,	-- depth:4
[239]=215,	-- depth:5
[238]=214,	-- depth:6
[237]=261,	-- depth:4
[213]=237,	-- depth:5
[236]=188,	-- depth:3
[233]=239,	-- depth:6
[224]=236,	-- depth:4
[225]=237,	-- depth:5
[226]=238,	-- depth:7
[228]=240,	-- depth:5
[230]=224,	-- depth:5
[231]=225,	-- depth:6
[234]=228,	-- depth:6
[260]=236,	-- depth:4
[212]=260,	-- depth:5
[209]=233,	-- depth:7
[257]=209,	-- depth:8
[200]=212,	-- depth:6
[201]=225,	-- depth:6
[202]=226,	-- depth:8
[256]=262,	-- depth:5
[254]=260,	-- depth:5
[252]=228,	-- depth:6
[210]=234,	-- depth:7
[251]=257,	-- depth:9
[249]=201,	-- depth:7
[203]=251,	-- depth:10
[204]=210,	-- depth:8
[248]=254,	-- depth:6
[206]=254,	-- depth:6
[207]=201,	-- depth:7
[208]=256,	-- depth:6
[250]=256,	-- depth:6
[227]=203,	-- depth:11
[132]=204,	-- depth:9
[167]=239,	-- depth:6
[65]=257,	-- depth:9
[66]=210,	-- depth:8
[68]=212,	-- depth:6
[69]=213,	-- depth:6
[70]=238,	-- depth:7
[71]=65,	-- depth:10
[72]=66,	-- depth:9
[80]=248,	-- depth:7
[81]=249,	-- depth:8
[82]=250,	-- depth:7
[83]=227,	-- depth:12
[84]=132,	-- depth:10
[86]=80,	-- depth:8
[87]=81,	-- depth:9
[88]=82,	-- depth:8
[89]=83,	-- depth:13
[90]=84,	-- depth:11
[92]=86,	-- depth:9
[93]=87,	-- depth:10
[64]=88,	-- depth:9
[94]=88,	-- depth:9
[63]=87,	-- depth:10
[60]=84,	-- depth:11
[32]=80,	-- depth:8
[33]=81,	-- depth:9
[34]=82,	-- depth:8
[35]=83,	-- depth:13
[36]=60,	-- depth:12
[38]=32,	-- depth:9
[39]=33,	-- depth:10
[40]=34,	-- depth:9
[41]=35,	-- depth:14
[42]=36,	-- depth:13
[44]=38,	-- depth:10
[45]=39,	-- depth:11
[46]=40,	-- depth:10
[47]=41,	-- depth:15
[48]=42,	-- depth:14
[56]=32,	-- depth:9
[57]=33,	-- depth:10
[58]=34,	-- depth:9
[59]=35,	-- depth:14
[62]=56,	-- depth:10
[168]=48,	-- depth:15
[95]=47,	-- depth:16
[104]=56,	-- depth:10
[138]=42,	-- depth:14
[140]=44,	-- depth:11
[141]=45,	-- depth:12
[142]=46,	-- depth:11
[143]=95,	-- depth:17
[144]=138,	-- depth:15
[152]=104,	-- depth:11
[153]=57,	-- depth:11
[154]=58,	-- depth:10
[155]=59,	-- depth:15
[156]=168,	-- depth:16
[158]=152,	-- depth:12
[159]=153,	-- depth:12
[160]=154,	-- depth:11
[161]=155,	-- depth:16
[162]=156,	-- depth:17
[164]=158,	-- depth:13
[165]=159,	-- depth:13
[166]=160,	-- depth:12
[137]=161,	-- depth:17
[96]=144,	-- depth:16
[136]=160,	-- depth:12
[134]=158,	-- depth:13
[105]=153,	-- depth:12
[106]=154,	-- depth:11
[107]=155,	-- depth:16
[108]=156,	-- depth:17
[110]=134,	-- depth:14
[111]=105,	-- depth:13
[112]=106,	-- depth:12
[113]=107,	-- depth:17
[114]=108,	-- depth:18
[116]=110,	-- depth:15
[117]=111,	-- depth:14
[118]=112,	-- depth:13
[119]=113,	-- depth:18
[120]=114,	-- depth:19
[128]=134,	-- depth:14
[129]=105,	-- depth:13
[130]=106,	-- depth:12
[131]=107,	-- depth:17
[263]=119,	-- depth:19
[135]=129,	-- depth:14
[264]=120,	-- depth:20
},
other_default_table={cost_item_id=26172,cost_gold=100,show_item_id="48120|38710|26161|48117|44074|44185",},

open_day_default_table={start_day=1,end_day=999,grade=1,},

mode_default_table={mode=1,times=1,cost_item_num=1,},

reward_pool_default_table={grade=1,activity_day=1,seq=0,item=item_table[33],},

special_reward_pool_default_table={grade=1,activity_day=1,seq=0,item=item_table[34],times=1999,reward_big_show=0,},

item_random_desc_default_table={grade=1,activity_day=1,number=0,item_name="5星十万年魂骨自选包",item_id=48120,random_count=3,is_rare=0,},

baodi_default_table={grade=1,activity_day=1,times=750,index_list="3|4",},

reward_pool_choose_default_table={grade=1,activity_day=1,index=0,seq_list="0|1|2",}

}

