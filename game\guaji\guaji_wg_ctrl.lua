require("game/guaji/guaji_wg_data")

local FLY_TO_POS_LEVEL_LIMIT = 70 -- 传送到传送门等级
local OUTO_FLY_DIS = 280

-- 背包满后能自动捡东西的场景
Auto_Pick_SceneType = { [SceneType.PERSON_BOSS] = true }

local RESET_GUAJI_TIME = 5
local IS_AUTO_GUAJI = true

-- 挂机
GuajiWGCtrl = GuajiWGCtrl or BaseClass(BaseWGCtrl)

function GuajiWGCtrl:__init()
    if GuajiWGCtrl.Instance ~= nil then
        -- print_error("[GuajiWGCtrl] attempt to create singleton twice!")
        return
    end
    GuajiWGCtrl.Instance = self

    self:RegisterAllEvents()

    self.last_update_time = 0
    self.on_arrive_func = BindTool.Bind(self.OnArrive, self)

    self.last_play_time = 0--npc最后说话时间
    self.last_play_id = 0--最后播放的npc声音
    self.npc_talk_interval = 60--同一个npc说话的CD

    Runner.Instance:AddRunObj(self, 8)

    self.last_scene_id = 0
    self.scene_type = 0
    self.last_scene_key = 0
    self.guai_ji_next_move_time = 0
    self.path_list = nil
    self.move_target = nil
    self.move_target_left_time = 0
    self.auto_mount_up = false
    self.last_mount_time = 0
    self.is_gather = false
    self.bag_rich_quest = nil
    self.cache_select_obj_onloading = nil
    self.goto_pick_x = 0
    self.goto_pick_y = 0
    self.next_can_goto_pick_time = 0
    self.arrive_call_back = nil
    self.move_to_pos_call_back = nil
    self.next_scan_target_monster_time = 0

    -- 挂机临时机制
    self.old_guaji_type = GuajiCache.None
    self.temporary_time = 0
    self.temporary_total_time = 2

    self.guaji_update_time = 0
    self.guaji_check_time = 0.1

    self.boss_deal_stop_time = 0
    self.auto_scene_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("fb_scene_config_auto").sence_guaji, "scene_id")
    self.auto_sx_skill = false
    self.auto_longzhu_skill = false
    self.auto_wuxing_skill = false
    self.auto_tianshen_heji_skill = false
    self.auto_customized_skill = false
    self.auto_esoterica_skill = false
    self.auto_beasts_skill = false
    self.sit_is_open = false
    self.recovery_follow_cache = nil
    self.realive_follow_cache = nil

    self.check_role_bianshen_time = 0
    self.instant_release_skill_next_time = 0 -- 瞬间释放的技能，挂机自动释放，需做间隔检测
end

function GuajiWGCtrl:__delete()
    GuajiWGCtrl.Instance = nil
    Runner.Instance:RemoveRunObj(self)

    GlobalTimerQuest:CancelQuest(self.bag_rich_quest)
end

function GuajiWGCtrl:RegisterAllEvents()
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_DEAD, BindTool.Bind1(self.OnMainRoleDead, self))
    self:BindGlobalEvent(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObj, self))
    self:BindGlobalEvent(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
    self:BindGlobalEvent(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjDead, self))
    self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneLoadingQuite, self))
    self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.OnSceneLoadingEnter, self))
    self:BindGlobalEvent(ObjectEventType.VISIBLE_OBJ_ENTER_MONSTER, BindTool.Bind(self.OnMonsterEnter, self))

    self:BindGlobalEvent(ObjectEventType.CAN_NOT_FIND_THE_WAY,
    BindTool.Bind1(self.OnCanNotFindWay, self))
    self:BindGlobalEvent(ObjectEventType.OBJ_DELETE,
    BindTool.Bind(self.OnObjDelete, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_MOVE_END,
    BindTool.Bind(self.PlayerOperation, self))
    self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_POS_CHANGE,
    BindTool.Bind(self.PlayerPosChange, self))
    self:BindGlobalEvent(SettingEventType.AUTO_RELEASE_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_RELEASE_SKILL))
    self:BindGlobalEvent(SettingEventType.AUTO_RELEASE_ANGER,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_RELEASE_ANGER))
    self:BindGlobalEvent(SettingEventType.AUTO_PICK_PROPERTY,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_PICK_PROPERTY))
    self:BindGlobalEvent(SettingEventType.AUTO_SIXIANG_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_SIXIANG_SKILL))
    self:BindGlobalEvent(SettingEventType.AUTO_LONGZHU_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_LONGZHU_SKILL))
    self:BindGlobalEvent(SettingEventType.AUTO_WUXING_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_WUXING_SKILL))
    self:BindGlobalEvent(SettingEventType.AUTO_HALO_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_HALO_SKILL))
    self:BindGlobalEvent(SettingEventType.AUTO_TIANSHEN_HEJI_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_TIANSHEN_HEJI_SKILL))
    self:BindGlobalEvent(SettingEventType.AUTO_CUSTOMIZED_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_CUSTOMIZED_SKILL))
    self:BindGlobalEvent(SettingEventType.AUTO_ESOTERICA_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_ESOTERICA_SKILL))
    self:BindGlobalEvent(SettingEventType.AUTO_BEASTS_SKILL,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_BEASTS_SKILL))


    -- self:BindGlobalEvent(SettingEventType.AUTO_RELEASE_GODDESS_SKILL,
    -- BindTool.Bind(self.SettingChange, self, SETTING_TYPE.AUTO_RELEASE_GODDESS_SKILL))
    self:BindGlobalEvent(ObjectEventType.STOP_GATHER,
    BindTool.Bind(self.OnStopGather, self))
    self:BindGlobalEvent(ObjectEventType.START_GATHER,
    BindTool.Bind(self.OnStartGather, self))
    self:BindGlobalEvent(OtherEventType.VIEW_CLOSE,
    BindTool.Bind(self.OnViewClose, self))
    self:BindGlobalEvent(OpenFunEventType.OPEN_TRIGGER,
    BindTool.Bind(self.OpenFunEventChange, self))
end

function GuajiWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all then
        is_open = FunOpen.Instance:GetFunIsOpened(FunName.RoleSit)
        self.sit_is_open = is_open
    elseif fun_name == FunName.RoleSit then
        self.sit_is_open = is_open
    end
end

-- return param1 是否可以打坐, param2 是否需要重置待机时间
function GuajiWGCtrl:IsCanGoToSit()
    if not GLOBAL_AUTO_RUN_TASK_SWITCH or IS_ON_CROSSSERVER then
        return false, true
    end

    if GuajiCache and GuajiCache.guaji_type ~= GuajiType.None then
        return false, true
    end

    if not self.sit_is_open then
        return false, true
    end

    if FunctionGuide.Instance:GetIsGuide() then
        return false, true
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.Common then
        return false, true
    end

    local scene_id = Scene.Instance:GetSceneId()
    local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
    if bootybay_scene_id == scene_id then
        return false, true
    end

    local main_role = Scene.Instance:GetMainRole()
    if not main_role then
        return false, true
    end

    if main_role:GetIsInSit() then
        return false, true
    end

    if main_role:IsFollowState() then
        return false, true
    end

    if not main_role:CanDoMove() and not main_role:HasCantMoveBuffButCanShowMove() then
        return false, true
    end

    if main_role:IsMitsurugi() then
        return false, true
    end

    if not TaskGuide or TaskGuide.Instance:NoviceCheckTask() then
        return false, true
    end

    if main_role:GetTaskToBianShen() > 0 then
        return false, true
    end

    if main_role:IsRidingFightMount() then
        return false, true
    end

    if main_role.vo and main_role.vo.special_appearance > 0 then
        return false, true
    end

    if YunbiaoWGData.Instance:GetIsHuShong() then
        return false, true
    end

    local ia_can_auto, auto_time = self:GetCurAutoGuaJiCfg()
    if not ia_can_auto or auto_time <= 0 then
        return false, true
    end

    if ScreenShotWGCtrl.Instance:IsOpenScreenShotView() then
        return false, true
    end

    local stand_time = main_role:GetRoleStandTime()
    if stand_time == 0 or (Status.NowTime - stand_time < auto_time) then
        return false, false
    end

    return true, true
end

function GuajiWGCtrl:Update(now_time, elapse_time)
    local scene_logic = Scene.Instance:GetSceneLogic()
    local scene_id = Scene.Instance:GetSceneId()
    local scene_type = Scene.Instance:GetSceneType()
    if not scene_logic then
        return
    end

    if MainUIView.LAST_DO_TASK_TIME then --任务引导对话框
        MainUIView.LAST_DO_TASK_TIME = MainUIView.LAST_DO_TASK_TIME + elapse_time
        if MainUIView.LAST_DO_TASK_TIME > 10 then
            MainUIView.LAST_DO_TASK_TIME = 0
            GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_GUIDE_DIALOG, true)
        end
    end
    self:UpdateTemporary(now_time, elapse_time)

    if self.guaji_update_time > now_time then
        return
    end

    self.guaji_update_time = now_time + self.guaji_check_time

    if Scene.Instance:IsSceneLoading() then
        return
    end

    -- 杀了BOSS之后，要原地停留一秒，捡东西
    if GuajiCache.guaji_type == GuajiType.Auto and self.boss_deal_stop_time >= Status.NowTime then
        local stop_wait_pick = false
        local scene_config = Scene.Instance:GetCurFbSceneCfg()
        if scene_config ~= nil and scene_config.pb_shouhu ~= nil then
            stop_wait_pick = EquipWGData.Instance:CanAutoPick(scene_config.pb_shouhu == 1)
        end

        if self:CheckCanPick() and not stop_wait_pick then
            self:ClearBossDeadTime()
        end

        return
    end

    self:ClearBossDeadTime()

    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil or main_role:IsDead() or main_role:GetIsGatherState() then
        return
    end

    if not scene_logic:IsIgnoreResetGuaJi() then
        local is_reset_time, is_reset_guaji = self:ResetGuaJi()
        if is_reset_time and main_role ~= nil then
            main_role:ResetGuaJiCheckMoveTime()
        end
    end

    local is_can_go_to_sit, is_need_reset_stand_time = self:IsCanGoToSit()
    if is_can_go_to_sit then
        main_role:ResetRoleStandTime()
        self:TryGoToSit()
        return
    elseif is_need_reset_stand_time then
        main_role:ResetRoleStandTime()
    end

    if main_role:IsFollowState() then
        local member_list = SocietyWGData.Instance:GetTeamMemberList()
        local leader_role_id
        local plat_type

        for k, v in pairs(member_list) do
            if v.is_leader == 1 then
                leader_role_id = v.role_id
                plat_type = v.plat_type
                break
            end
        end

        local leader_role_uuid = MsgAdapter.ReadUUIDByValue(leader_role_id, plat_type)
        local leader_role_obj = Scene.Instance:GetRoleByUUID(leader_role_uuid)
        if leader_role_obj and leader_role_obj:GetIsInSit() then --
            MainuiWGCtrl.Instance:OnClickSit()
            return
        end
    end

    if GuajiCache.guaji_type == GuajiType.None and not MoveCache.is_valid and not AtkCache.is_valid then
        return
    end

    if PickCache.last_time > Status.NowTime then
        return
    end

    --藏宝湾场景id
    local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
    if bootybay_scene_id == scene_id and self:CheckCanPick(true) then
        return
    end

    -- 蓄力中，停止挂机逻辑
    if main_role:GetIsInSpecialState(FRONT_SKILL_TYPE.ACCUMULATION) then
        return
    end

    -- 恐惧中，停止挂机逻辑
    if main_role:IsFear() or main_role:IsRepelMoving() then
        return
    end

    if GuajiCache.guaji_type ~= GuajiType.None and main_role:IsMove() then
        local atk_obj = nil
        if AtkCache.target_obj then
            atk_obj = AtkCache.target_obj
        end

        if atk_obj then
            local new_x, new_y = AtkCache.target_obj:GetLogicPos()
            if GuajiWGCtrl.CheckRange(new_x, new_y, MoveCache.range) then
                main_role:ChangeToCommonState()
                self:OnOperate()
                return
            end
        end
    end

    if scene_type ~= SceneType.Field1v1 then
        self:CheckAutoYuLong(now_time, elapse_time)
        self:CheckAutoGundam(now_time, elapse_time)
        self:CheckAutoJingjieBianshen(now_time, elapse_time)
        self:CheckAutoBianshen(now_time, elapse_time)
    end

    if scene_type == SceneType.ZhuXie or scene_type == SceneType.KFZhuXieZhanChang then
        if self:CheckCanPick() then
            return
        end
    end

    --在寻路过程中点击挂机按钮时，挂机按钮变成挂机状态，此时寻路不会被打断，但是在寻路中遇到怪物会马上进入战斗
    if not main_role:IsFightState() and GuajiCache.guaji_type == GuajiType.Auto and not self:CheckRenvengeIsVaild() then
        local monster_list = Scene.Instance:GetMonsterList()
        local obj = GuajiCache.target_obj
        if not TaskGuide.Instance:NoviceCheckTask() and not IsEmptyTable(monster_list) and (not obj or not obj:IsCharacter()) then
            if self:CheckCanPick() then
                return
            else
                obj = self:SelectAtkTarget(true)

                if obj and obj:IsCharacter() then
                    self:StopGuaji(nil, nil, nil, true)
                    self:SetGuajiType(GuajiType.Auto)
                    return
                end
            end
        end
    end

    if main_role:IsMove() then
        if MoveEndType.Fight == MoveCache.end_type then
            self:UpdateFollowAtkTarget(now_time)
        end

        if MoveEndType.FightByMonsterId == MoveCache.end_type then
            self:UpdateMonsterTarget(now_time)
        elseif MoveEndType.GatherById == MoveCache.end_type then
            self:UpdateGatherTarget(now_time)
        end
    end

    if main_role:IsMove() or not main_role:CanDoMove() or main_role:HasCantMoveBuffButCanShowMove() then
        return
    end

    if MoveCache.is_move_scan and not MoveCache.is_ignore_auto_fight then
        if self:CheckCanPick() then
            return
        end

        local obj = self:SelectAtkTarget(true)
        if obj and obj:IsCharacter() then
            self:StopGuaji(nil, nil, nil, true)
            self:SetGuajiType(GuajiType.Auto)
            return
        end
    end

    if self.next_scan_target_monster_time > 0 then
        self:OnOperateFightByMonsterId()
        self.next_scan_target_monster_time = 0
    end

    if not AtkCache.is_valid then
        --临时状态也不让去拾取物品
        if GuajiCache.guaji_type ~= GuajiType.None and GuajiCache.guaji_type ~= GuajiType.Temporary then
            if not self:CheckCanPick() then -- 没有可捡点则直接挂机
                self:UpdateGuaji(now_time)
            end
        end
    end

    if AtkCache.is_valid then
        self:UpdateAtk(now_time)
        self:UpdateFollowAtkTarget(now_time)
    end

    self:FixGuajiStopBug(elapse_time)
end

function GuajiWGCtrl:CheckAutoYuLong(now_time, elapse_time)
    if GuajiCache.guaji_type ~= GuajiType.Auto and GuajiCache.guaji_type ~= GuajiType.Monster then
        return
    end

    local cur_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if cur_scene_cfg.pb_yulong == 1 then
		return
    end

    -- 是否自动御龙
    local flag = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_YU_LONG)
    if not flag then
        return
    end

    if self.check_yulong_time then
        if self.check_yulong_time > now_time then
            return
        end
    end
    self.check_yulong_time = now_time + 1

    local cur_now_time = Status.NowTime
	if cur_now_time < self.check_role_bianshen_time then
		return
	end

    local main_role = Scene.Instance:GetMainRole()
    if not main_role or main_role.vo.special_appearance > 0 then
        return
    end

    if main_role:IsRidingFightMount() then
        return
    end

    local target_obj = self:SelectAtkTarget(true, {[SceneIgnoreStatus.MAIN_ROLE_IN_SAFE] = true,}, false)
    if not target_obj then
        return
    end

    local target_x,target_y = target_obj:GetLogicPos()
    local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
    local delta_pos = u3d.v2Sub(u3d.vec2(target_x, target_y), u3d.vec2(self_x, self_y))
    local distance = u3d.v2Length(delta_pos)
    if distance >= 20 then
        return
    end

    local use_skill_end_time, next_use_skill_time, use_skill_start_time = NewFightMountWGData.Instance:GetUseSkillTimeInfo()
    local use_skill_id = NewFightMountWGData.Instance:GetUseSkillId()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local can_use_skill = server_time >= next_use_skill_time and use_skill_id >= 0
    if not can_use_skill then
        return
    end

    self.check_role_bianshen_time = cur_now_time + 1.5
    NewFightMountWGCtrl.Instance:SendCSNewFightMountOperateRequest(FIGHT_MOUNT2_OPERATE_TYPE.FIGHT)
end



function GuajiWGCtrl:CheckAutoGundam(now_time, elapse_time)
    if GuajiCache.guaji_type ~= GuajiType.Auto and GuajiCache.guaji_type ~= GuajiType.Monster then
        return
    end

    -- 是否自动高达
    local flag = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_GUNDAM)
    if not flag then
        return
    end

    if self.check_auto_gundam_time then
        if self.check_auto_gundam_time > now_time then
            return
        end
    end
    self.check_auto_gundam_time = now_time + 1

    local cur_now_time = Status.NowTime
	if cur_now_time < self.check_role_bianshen_time then
		return
	end

    local main_role = Scene.Instance:GetMainRole()
    if not main_role or main_role.vo.special_appearance > 0 then
        return
    end

    if main_role:IsRidingFightMount() then
        return
    end

    local bs_mecha_seq = MechaWGData.Instance:GetBianShenSeq()
	if bs_mecha_seq < 0 then
		return
	end

	local mecha_cfg = MechaWGData.Instance:GetMechaCfgBySeq(bs_mecha_seq)
	if not mecha_cfg then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local bs_end_time = MechaWGData.Instance:GetBianShenEndTime()
	if bs_end_time + mecha_cfg.bianshen_cd > server_time then
        return
	end

    local target_obj = self:SelectAtkTarget(true, {[SceneIgnoreStatus.MAIN_ROLE_IN_SAFE] = true,}, false)
    if not target_obj then
        return
    end

    local target_x,target_y = target_obj:GetLogicPos()
    local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
    local delta_pos = u3d.v2Sub(u3d.vec2(target_x, target_y), u3d.vec2(self_x, self_y))
    local distance = u3d.v2Length(delta_pos)
    if distance >= 20 then
        return
    end

    MountWGCtrl.Instance:SendMountGoonReq(0)
    self.check_role_bianshen_time = cur_now_time + 1.5
    MechaWGCtrl.Instance:SendRequest(MECHA_OPERA_TYPE.BIANSHEN)
end

function GuajiWGCtrl:CheckAutoJingjieBianshen(now_time, elapse_time)
    if GuajiCache.guaji_type ~= GuajiType.Auto and GuajiCache.guaji_type ~= GuajiType.Monster then
        return
    end

    -- 是否自动境界变身
    local flag = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_JINGJIE_BIANSHEN)
    if not flag then
        return
    end

    if self.check_auto_jingjiebianshen_time then
        if self.check_auto_jingjiebianshen_time > now_time then
            return
        end
    end
    self.check_auto_jingjiebianshen_time = now_time + 1

    local cur_now_time = Status.NowTime
    if cur_now_time < self.check_role_bianshen_time then
		return
	end

    local main_role = Scene.Instance:GetMainRole()
    if not main_role or main_role.vo.special_appearance > 0 then
        return
    end

    if main_role:IsRidingFightMount() then
        return
    end

    local power_bianshen_flag = CultivationWGData.Instance:GetRoleBianshenFlag()
    local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
    local curr_nuqi = CultivationWGData.Instance:GetRoleCurrNuqi()
    local curr_slider_value = curr_nuqi / 100
    if curr_slider_value >= 1 and power_bianshen_flag == 0 and curr_nuqi_type ~= -1 then
        CultivationWGCtrl.Instance:AngerBecome()
    end
end

function GuajiWGCtrl:CheckAutoBianshen(now_time, elapse_time)
    if GuajiCache.guaji_type ~= GuajiType.Auto and GuajiCache.guaji_type ~= GuajiType.Monster then
        return
    end

    if self.check_bianshen_time then
        if self.check_bianshen_time > now_time then
            return
        end
    end
    self.check_bianshen_time = now_time + 1

    -- 是否自动变身
    local flag = SettingWGData.Instance:GetSettingData(SETTING_TYPE.AUTO_BIANSHEN)
    if not flag then
        return
    end

    local cur_now_time = Status.NowTime
    if cur_now_time < self.check_role_bianshen_time then
		return
	end
    local main_role = Scene.Instance:GetMainRole()
    if not main_role or main_role.vo.special_appearance > 0 then
        return
    end

    if main_role:IsRidingFightMount() then
        return
    end

    local target_obj = self:SelectAtkTarget(true, {[SceneIgnoreStatus.MAIN_ROLE_IN_SAFE] = true,}, false)
    if not target_obj then
        return
    end

    local target_x,target_y = target_obj:GetLogicPos()
    local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
    local delta_pos = u3d.v2Sub(u3d.vec2(target_x, target_y), u3d.vec2(self_x, self_y))
    local distance = u3d.v2Length(delta_pos)
    if distance >= 20 then
        return
    end

    local info = TianShenWGData.Instance:GetTianShenAutoChuZhan()
    if not info then
        return
    end

    self.check_role_bianshen_time = cur_now_time + 1.5
    MountWGCtrl.Instance:SendMountGoonReq(0)
    TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type15, info.image_seq)
end

function GuajiWGCtrl:CheckCanPick(is_ignore_time)
    local pick_x, pick_y = self:GetPickPos(is_ignore_time)
    if 0 == pick_x and 0 == pick_y then -- 没有可捡点则直接挂机
        return MoveEndType.PickAroundItem == MoveCache.end_type
    end

    -- 去到目标点不做任何事，场景有机制自动触发捡取, 不设置会断掉挂机
    if self.goto_pick_x ~= pick_x or self.goto_pick_y ~= pick_y and Status.NowTime >= self.next_can_goto_pick_time then
        self.next_can_goto_pick_time = 0
        self.goto_pick_x = pick_x
        self.goto_pick_y = pick_y
        MoveCache.SetEndType(MoveEndType.PickAroundItem)
        self:MoveToPos(Scene.Instance:GetSceneId(), pick_x, pick_y, 0)
    end

    return true
end

-- 非普通场景，自动挂机时，有怪物进入视，则重制目标点
function GuajiWGCtrl:OnMonsterEnter(monster_vo)
    if not monster_vo then
        return
    end

    if nil ~= GuajiCache.target_obj and not GuajiCache.target_obj:IsDeleted() then
        if Scene.Instance:IsEnemy(GuajiCache.target_obj) then
            return
        end
    end

    local scene_type = Scene.Instance:GetSceneType()
    -- 塔防不重置
    if scene_type == SceneType.DefenseFb then
        return
    end

    local monster_obj = Scene.Instance:GetObjectByObjId(monster_vo.obj_id)
    if not monster_obj then
        return
    end

    if not Scene.Instance:IsEnemy(monster_obj) then
        return
    end

    if scene_type ~= SceneType.Common then
        if GuajiCache.guaji_type == GuajiType.Auto then
            self:SetMoveScan(true)
        end
    end
end

local last_fix_bug_time = 0
local maybe_bug_keep_time = 0
function GuajiWGCtrl:FixGuajiStopBug(elapse_time)
    -- if (GuajiCache.guaji_type == GuajiType.Auto or GuajiCache.guaji_type == GuajiType.Monster) then
    --     if Scene.Instance:GetMainRole():IsStand()
    --         and not Scene.Instance:IsSceneLoading()
    --         and not CgManager.Instance:IsCgIng() then
    --         maybe_bug_keep_time = maybe_bug_keep_time + elapse_time
    --     else
    --         maybe_bug_keep_time = 0
    --     end

    --     if maybe_bug_keep_time > 1 then
    --         maybe_bug_keep_time = 0
    --         -- print_error("is guaji stop? auto restart now!")
    --         last_fix_bug_time = Status.NowTime
    --         self:StopGuaji()
    --         self:SetGuajiType(GuajiType.Auto)
    --     end
    -- end
end

-- is_ignore_time 忽略生成时间
function GuajiWGCtrl:GetPickPos(is_ignore_time)
    if MoveEndType.PickAroundItem == MoveCache.end_type then -- 正在往捡起路上，不该干其他事
        return 0, 0
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.DefenseFb then
        return 0, 0
    end

    if not BossWGData.IsBossScene(scene_type) then
        if nil ~= GuajiCache.target_obj and Scene.Instance:IsEnemy(GuajiCache.target_obj) and GuajiCache.target_obj.IsRealDead and not GuajiCache.target_obj:IsRealDead() then
            return 0, 0
        end
    end

	local recovery_valid, recovery_x, recovery_y = self:CheckIsRecoveryFollowVaild()
	if recovery_valid and recovery_x ~= nil and recovery_y ~= nil then
        local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
		if GameMath.GetDistance(recovery_x, recovery_y, self_x, self_y, false) >= 15 * 15 then
			return 0, 0
		end
	end

    if Scene.Instance:GetMainRole():IsRealDead() then
        return 0, 0
    end

    local auto_pick = self:IsCanPickItem()
    if not auto_pick then
        return 0, 0
    end

    --守护小鬼自动拾取
    local is_pb_shouhu = Scene.Instance:GetCurFbSceneCfg().pb_shouhu
    local xiaogui_auto_pick = EquipWGData.Instance:CanAutoPick(is_pb_shouhu == 1)

    if xiaogui_auto_pick then
        return 0, 0
    end

    local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem)
    if not next(fall_item_list) then
        return 0, 0
    end

    local main_role = Scene.Instance:GetMainRole()
    local main_role_x, main_role_y = main_role:GetLogicPos()

    local add_time = 2      --延长时间，要求要先展示一下掉落物品 过段时间再捡起来
    if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
        add_time = 0
    end

    local pick_x, pick_y, pick_distance = 0, 0, 99999
    for k, v in pairs(fall_item_list) do
        local item_cfg = ItemWGData.Instance:GetItemConfig(v.vo.item_id)
        local is_can_pick = true

        if item_cfg
            and self:IsCanPickItemByColor(v:IsEquip(),item_cfg)
            -- and self:IsCanPick(v:IsEquip(),item_cfg)
            and not v:IsPicked()
            and is_can_pick    -- 
            -- and v:IsCanPackItem()
            and not v:IsInBlock()-- 掉落物在障碍点不捡，服务器处理下
            and (is_ignore_time or TimeWGCtrl.Instance:GetServerTime() >= v:GetVo().drop_time + add_time)-- (可能会忽略物品，跑去打别的怪，暂时屏蔽掉，有问题再说) 生成时有动画要看，太快捡掉不好
            and self:InSceneCanPickItem(v.vo)
            and (v:GetVo().owner_role_id < 0
            or v:GetVo().owner_role_id == main_role:GetRoleId()
            or (v:GetVo().lose_owner_time > 0 and v:GetVo().lose_owner_time <= TimeWGCtrl.Instance:GetServerTime())  -- 只捡自己的(无归属的也属于自己)
            or scene_type == SceneType.CROSS_LIEKUN
            or scene_type == SceneType.GuildMiJingFB) then

                local target_x, target_y = v:GetLogicPos()
                local distance = GameMath.GetDistance(main_role_x, main_role_y, target_x, target_y, false)
                if distance < pick_distance then
                    if scene_type == SceneType.CROSS_LIEKUN then
                        if Scene.Instance:GetSceneLogic():CheckIsOurGuildFall(v:GetVo()) then
                            pick_x = target_x
                            pick_y = target_y
                            pick_distance = distance
                            break
                        end
                    else
                        pick_x = target_x
                        pick_y = target_y
                        pick_distance = distance
                    end
                end
        end
    end

    return pick_x, pick_y
end

--判断该装备在可拾取范围内是否可以捡
function GuajiWGCtrl:InSceneCanPickItem(vo)
    local scene_type = Scene.Instance:GetSceneType()
    --在永夜之巅判断该装备是否可以捡
    if scene_type == SceneType.ETERNAL_NIGHT or scene_type == SceneType.ETERNAL_NIGHT_FINAL then
        local id = vo.coin
        local equip_cfg = EternalNightWGData.Instance:GetEquipCfgById(id)
        if equip_cfg and equip_cfg.equip_part then
            local self_equip = EternalNightWGData.Instance:GetSelfEquipInfo(equip_cfg.equip_part)
            if self_equip then
                return equip_cfg.zhanli > self_equip.zhanli
            else
                return true
            end
        end
    elseif scene_type == SceneType.XianJie_Boss and vo.item_id == COMMON_CONSTS.XiamVirtualItemId then
        local id = vo.coin
        local is_can_pick = XianJieBossWGData.Instance:GetCanPickItemById(id)
        return is_can_pick
    else
        return true
    end
end

function GuajiWGCtrl:IsCanPickItem()
    return true
end

function GuajiWGCtrl:IsCanPick(is_equip,item_cfg)
    return false
end

function GuajiWGCtrl:IsCanPickItemByColor(is_equip,item_cfg)
    return true
end

function GuajiWGCtrl:CalToShowTips()
    local timer = 2
    self.bag_rich_quest = GlobalTimerQuest:AddRunQuest(function()
        timer = timer - UnityEngine.Time.deltaTime
        if timer <= 0 then
            -- TipWGCtrl.Instance:ShowSystemMsg("您的背包空间不足，无法拾取物品，请清理！")
            GlobalTimerQuest:CancelQuest(self.bag_rich_quest)
            self.bag_rich_quest = nil
        end
    end, 0)
end

-- 获取挂机打怪的位置
function GuajiWGCtrl:GetGuiJiMonsterPos()
    local target_distance = 1000 * 1000
    local target_x = nil
    local target_y = nil
    local x, y = Scene.Instance:GetMainRole():GetLogicPos()

    local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()
    local scene_logic = Scene.Instance:GetSceneLogic()
    for k, v in pairs(obj_move_info_list) do
        local vo = v:GetVo()
        if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id, vo) and scene_logic:ConditionScanMonster(vo) then
            local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
            if distance < target_distance then
                target_x = vo.pos_x
                target_y = vo.pos_y
                target_distance = distance
            end
        end
    end
    return target_x, target_y
end

-- 检测范围
function GuajiWGCtrl.CheckRange(x, y, range, s_x, s_y)
    if not tonumber(x) or not tonumber(y) or not tonumber(range) then
        print_error("Error pos:::", x, y, range)
        return
    end

    if Scene.Instance == nil then
        return
    end

    local main_role = Scene.Instance:GetMainRole()

    if main_role == nil then
        return
    end

    local self_x, self_y = main_role:GetLogicPos()
    if s_x ~= nil and s_y ~= nil then
        self_x = s_x
        self_y = s_y
    end

    -- print_error("--检测范围--", self_x, self_y, x, y, range, math.floor((x - self_x) * (x - self_x)) + math.floor((y - self_y) * (y - self_y)) <= range * range)
    return math.floor((x - self_x) * (x - self_x)) + math.floor((y - self_y) * (y - self_y)) <= range * range
end

function GuajiWGCtrl:SetMoveScan(value)
    MoveCache.is_move_scan = value
end

-- 选择（寻找）攻击目标
function GuajiWGCtrl:SelectAtkTarget(can_select_role, ignore_table, cant_select_monster)
    -- print_error("----选择（寻找）攻击目标----")
    local target_obj = nil
    local scene = Scene.Instance

    local is_enemy = scene:IsEnemy(GuajiCache.target_obj, ignore_table)
    if nil ~= GuajiCache.target_obj
        and GuajiCache.target_obj == scene:GetObj(GuajiCache.target_obj_id)
        and is_enemy
        and not AStarFindWay:IsBlock(GuajiCache.target_obj:GetLogicPos()) then
        target_obj = GuajiCache.target_obj
        if not GuajiCache.is_click_select then
            local self_x, self_y = scene:GetMainRole():GetLogicPos()
            local target_x, target_y = GuajiCache.target_obj:GetLogicPos()
            if AStarFindWay:GetWaySpatium(self_x, self_y, target_x, target_y, 10) >= math.sqrt(self:GetGuajiRange()) then
                target_obj = nil
            end
        end

        if target_obj and cant_select_monster and target_obj:GetType() == SceneObjType.Monster then
            target_obj = nil
        end
    end

    if nil == target_obj or not is_enemy then
        local scene_logic = scene:GetSceneLogic()
        local target_distance = scene_logic:GetGuajiSelectObjDistance()
        -- print_error("SelectAtkTarget target_distance "..target_distance)
        local x, y = scene:GetMainRole():GetLogicPos()
        local temp_role_target = nil
        local temp_monster_target = nil
        local is_need_stop

        -- 是否优先攻击玩家
        local role_first = not scene:GetSceneForbidPk()
        local scene_type = scene:GetSceneType()

        if scene_logic then
            temp_role_target, _, is_need_stop = scene_logic:GetGuajiCharacter()

            -- 新需求，死了的对象，是不能被选中的
            if temp_role_target and not (temp_role_target:IsCharacter() and temp_role_target:IsRealDead())
                and not (temp_role_target:IsMonster() and temp_role_target:IsNoSelectTianShenMonster()) then
                self:SetMoveScan(false)
                GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, temp_role_target, SceneTargetSelectType.SELECT)
                return temp_role_target
            else
                --如果GetGuajiCharacter这个方法里找不到怪，
                --会根据scene_logic:SpecialSelectEnemy() 这个标识判断是否返回, 直接不继续找怪了
                if scene_logic:SpecialSelectEnemy() then
                    return nil
                end
            end
        end

         if is_need_stop then
            return nil
         end

         -- 诛邪战场，无目标时，有任务不自动选中
         if scene_type == SceneType.ZhuXie and target_obj == nil then
            local boss_info = ActivityWGData.Instance:GetOneZhuXieBossInfo() or {}
            if not (boss_info.boss_id ~= nil and boss_info.boss_cur_hp > 0) then
                 local task_list = ActivityWGData.Instance:GetZhuXieTaskList()
                 if task_list ~= nil then
                     for k,v in pairs(task_list) do
                         if v.sort_index ~= 8 then
                             return nil
                         end
                     end
                 end
            end
         end

        -- 诛邪战场，无目标时，有任务不自动选中
         if scene_type == SceneType.KFZhuXieZhanChang and target_obj == nil then
            local boss_info = ActivityWGData.Instance:GetKFZhuXieBossInfo() or {}
            if not (boss_info.boss_id ~= nil and boss_info.boss_cur_hp > 0) then
                 local task_list = ActivityWGData.Instance:GetKFZhuXieTaskList()
                 if task_list ~= nil then
                     for k,v in pairs(task_list) do
                         if v.sort_index ~= 8 then
                             return nil
                         end
                     end
                 end
            end
         end

        if can_select_role then
            temp_role_target, _ = scene:SelectObjHelper(SceneObjType.Role, x, y, target_distance * target_distance, SelectType.Enemy, ignore_table)
        end

        if not cant_select_monster then
            temp_monster_target, _ = scene:SelectObjHelper(SceneObjType.Monster, x, y, target_distance * target_distance, SelectType.Enemy, ignore_table)
        end

        if role_first == nil and temp_role_target and temp_monster_target then
            local role_x, role_y = temp_role_target:GetLogicPos()
            local monster_x, monster_y = temp_monster_target:GetLogicPos()
            local role_dis = u3d.v2Distance({x = x, y = y}, {x = role_x, y = role_y})
            local monster_dis = u3d.v2Distance({x = x, y = y}, {x = monster_x, y = monster_y})
            target_obj = role_dis < monster_dis and temp_role_target or temp_monster_target
        elseif role_first == true then
            target_obj = temp_role_target or temp_monster_target
        else
            target_obj = temp_monster_target or temp_role_target
        end
        if nil ~= target_obj then
            GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
        end
    end

    if target_obj ~= nil then
        self:SetMoveScan(false)
    end
    return target_obj
end

-- 选择（寻找）攻击目标（玩家）
function GuajiWGCtrl:SelectFriend()
    local target_obj = nil
    if nil ~= GuajiCache.target_obj
        and GuajiCache.target_obj == Scene.Instance:GetObj(GuajiCache.target_obj_id)
        and Scene.Instance:IsFriend(GuajiCache.target_obj)
        and not AStarFindWay:IsBlock(GuajiCache.target_obj:GetLogicPos()) then
        target_obj = GuajiCache.target_obj
        if not target_obj:IsRole() then
            target_obj = nil
        end

        if not GuajiCache.is_click_select then
            local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
            local target_x, target_y = GuajiCache.target_obj:GetLogicPos()
            if GameMath.GetDistance(self_x, self_y, target_x, target_y, false) >= 640 then
                target_obj = nil
            elseif not AStarFindWay:IsWayLine(self_x, self_y, target_x, target_y) then
                target_obj = nil
            end
        end
    end
    if nil == target_obj then
        local scene = Scene.Instance

        local target_distance = scene:GetSceneLogic():GetGuajiSelectObjDistance()
        local x, y = scene:GetMainRole():GetLogicPos()

        local temp_target = nil
        temp_target, target_distance = scene:SelectObjHelper(SceneObjType.Role, x, y, target_distance, SelectType.Friend)
        target_obj = temp_target or target_obj

        if nil ~= target_obj then
            GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
        end
    end
    return target_obj
end

function GuajiWGCtrl.SetMoveValid(is_valid)
    MoveCache.is_valid = is_valid
end

function GuajiWGCtrl.SetAtkValid(is_valid)
    AtkCache.is_valid = is_valid
end

-- 重置移动缓存
function GuajiWGCtrl:ResetMoveCache()
    MoveCache.is_valid = false
    MoveCache.move_type = MoveType.None
    MoveCache.SetEndType(MoveEndType.Normal)
    MoveCache.cant_fly = false
    MoveCache.scene_id = 0
    MoveCache.x = 0
    MoveCache.y = 0
    MoveCache.target_obj = nil
    MoveCache.range = 0
    MoveCache.task_id = 0
    MoveCache.task_type = -1
    MoveCache.is_ignore_auto_fight = false
    MoveCache.follow_move_reason = CLIENT_MOVE_REQ_PARAM.NORMAL
end

function GuajiWGCtrl:SetGuajiType(guaji_type)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("SetGuajiType", GuajiCache.guaji_type, guaji_type)
    end

    if not IS_AUTO_GUAJI and guaji_type == GuajiType.Auto then
        return
    end

    if BossCamera.Instance:BossFollowCaneraShowStatus() then
        return
    end

    if guaji_type ~= nil and guaji_type ~= GuajiCache.guaji_type then
        -- print_log("内网日志：挂机状态改变 ", GuajiCache.guaji_type, guaji_type)
    end
    -- local role_vo = GameVoManager.Instance:GetMainRoleVo()
    -- if role_vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP2 then

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= nil and scene_type == SceneType.HotSpring and guaji_type ~= GuajiType.None then
        return
    end

    local main_role = Scene.Instance:GetMainRole()
    if main_role:IsJump() then
        return
    end

    if guaji_type ~= nil and guaji_type ~= GuajiType.None and main_role:IsFollowState() then
        main_role:SetIsFollowState(false, nil, nil, nil, nil, nil, true, true)
    end

    -- 寻路状态，点挂机，有怪的话立即停止，进入挂机
    if XunLuStatus.XunLu == MainuiWGCtrl.Instance:GetView():GetRoleAutoXunluState() then
        local monster_list = Scene.Instance:GetMonsterList()
        if not IsEmptyTable(monster_list) then
            self:ResetPickCache()
            self:ResetMoveCache()
            main_role:SetArriveFunc()
            main_role:ChangeToCommonState()
        end
    end

    local status = XunLuStatus.None
    if guaji_type ~= GuajiType.None and guaji_type ~= GuajiType.Temporary then
        status = XunLuStatus.AutoFight
        GuajiWGCtrl.SetMoveValid(false)
    end

    GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, status)
    if GuajiCache.guaji_type ~= guaji_type then
        self:PlayerOperation()
        GuajiCache.guaji_type = guaji_type
        if guaji_type == GuajiType.Auto then
            local scene_logic = Scene.Instance:GetSceneLogic()
            if scene_logic and scene_logic:GetAutoGuajiPriority() then
                GuajiWGCtrl.SetMoveValid(false)
            end
            if TaskGuide.Instance:NoviceCheckTask() then
                self.guaji_update_time = Status.NowTime + self.guaji_check_time
                TaskGuide.Instance:CanAutoAllTask(true)
            end
        end
        GlobalEventSystem:Fire(OtherEventType.GUAJI_TYPE_CHANGE, guaji_type)
        GuajiCache.event_guaji_type = guaji_type
    end

	--打印战斗log
    FightWGCtrl.Instance:StartGuaji(guaji_type)
end

-- 取消选中
function GuajiWGCtrl:CancelSelect()
    if nil ~= GuajiCache.target_obj and GuajiCache.target_obj == Scene.Instance:GetObj(GuajiCache.target_obj_id) then
        --if GuajiCache.target_obj:GetType() ~= SceneObjType.MainRole and GuajiCache.target_obj:GetType() ~= SceneObjType.Role then
        GuajiCache.target_obj:CancelSelect()
        if self.select_obj then
            self.select_obj:CancelSelect()
            self.select_obj = nil
        end
        --end
        GuajiCache.target_obj = nil
        GuajiCache.target_obj_id = COMMON_CONSTS.INVALID_OBJID
        if MainuiWGCtrl.Instance.view then
            MainuiWGCtrl.Instance.view:SetMianUITargetState(false)
        end
    end
end

--角色死亡后
function GuajiWGCtrl:OnMainRoleDead()
    self:StopGuaji()
end

-- 选择场景对象
function GuajiWGCtrl:OnSelectObj(target_obj, select_type)
    local scene_logic = Scene.Instance:GetSceneLogic()
    if not scene_logic then return false end
    if nil == target_obj
        or CgManager.Instance:IsCgIng()
        or target_obj:IsDeleted()
        or (target_obj:IsCharacter() and target_obj:IsInvisible())
        or target_obj:GetType() == SceneObjType.MainRole
        or target_obj:GetType() == SceneObjType.TruckObj
        or (target_obj:IsCharacter() and target_obj:IsRealDead())       -- -- 新需求，死了的对象，是不能被选中的
        or (target_obj:GetType() == SceneObjType.Monster and target_obj:GetMonsterId() == 1101 and Scene.Instance:GetSceneType() == SceneType.QunXianLuanDou)
        then
            self:CancelSelect()
        return false
    end

    if target_obj ~= GuajiCache.target_obj then
        self:CancelSelect()
    end

    scene_logic:OnSelectObj(target_obj, select_type)

    if self.select_obj and not self.select_obj:IsDeleted() then
        self.select_obj:CancelSelect()
        self.select_obj = nil
    end
    self.select_obj = target_obj

    target_obj:OnClick()
    GuajiCache.target_obj = target_obj
    GuajiCache.target_obj_id = target_obj:GetObjId()
    GuajiCache.is_click_select = SceneWGData:TargetSelectIsScene(select_type)

    if SceneWGData:TargetSelectIsSelect(select_type) then
        --若选择人物但当前为怪物挂机，则切换挂机状态
        if GuajiCache.guaji_type == GuajiType.Monster and target_obj:GetType() == SceneObjType.Role then
            self:SetGuajiType(GuajiType.Auto)
        end
        return false
    elseif not SceneWGData:TargetSelectIsTask(select_type) then -- 非任务
        MoveCache.task_id = 0
        MoveCache.task_type = -1
    end

    if GuajiCache.guaji_type == GuajiType.Auto and target_obj:GetType() ~= SceneObjType.Monster and target_obj:GetType() ~= SceneObjType.Role then
        self:StopGuaji()
    end

    local scene_type = Scene.Instance:GetSceneType()
    if target_obj:GetType() == SceneObjType.Monster or target_obj:GetType() == SceneObjType.Role or target_obj:GetType() == SceneObjType.Pet then
        local is_enemy, msg = Scene.Instance:IsEnemy(target_obj, {
            [SceneIgnoreStatus.MAIN_ROLE_IN_SAFE] = true,
            [SceneIgnoreStatus.OTHER_IN_SAFE] = true,
        })

        if is_enemy then
            GuajiWGCtrl.SetAtkValid(false)
            if GuajiCache.guaji_type == GuajiType.None or target_obj:GetType() == SceneObjType.Role then -- SceneWGData:TargetSelectIsScene(select_type) and
                 self:SetGuajiType(GuajiType.Auto)
            end
            local x, y = target_obj:GetLogicPos()
            self:DoAttackTarget(target_obj)
        else
            if scene_type == SceneType.DefenseFb then
                self:ClearAllOperate()
                MoveCache.SetEndType(MoveEndType.DefenseObj)
                self:MoveToObj(target_obj, 4)
            end

            if target_obj:GetType() == SceneObjType.Monster and BossWGData.IsBossScene(scene_type) then
                -- TipWGCtrl.Instance:ShowSystemMsg(msg)
                SysMsgWGCtrl.Instance:ErrorRemind(msg)
            end

            if target_obj:GetType() == SceneObjType.Monster and scene_type == SceneType.CROSS_DIVINE_DOMAIN then
                SysMsgWGCtrl.Instance:ErrorRemind(msg)
            end
        end
    elseif target_obj:GetType() == SceneObjType.Npc then
        self:ClearAllOperate()
        MoveCache.SetEndType(MoveEndType.ClickNpc)
        MoveCache.param1 = target_obj:GetNpcId()
        local range = TaskWGData.Instance:GetNPCRange(target_obj:GetNpcId())
        self:MoveToObj(target_obj, range)

    elseif target_obj:GetType() == SceneObjType.FallItem then
        self:ClearAllOperate()
        MoveCache.SetEndType(MoveEndType.PickItem)
        self:MoveToObj(target_obj, 0, 0)
    elseif target_obj:GetType() == SceneObjType.GatherObj then
        if scene_type == SceneType.GuDaoJiZhan_FB then
            if GuDaoFuBenWGData.Instance and GuDaoFuBenWGData.Instance:GetGatherNum() == 3 then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.GuDaoJiZhan.IsLimiteNum)
                return false
            end
        end

        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            scene_logic:ClearGuaJiInfo()
        end

        self:ClearAllOperate()
        MoveCache.SetEndType(MoveEndType.Gather)
        self:MoveToObj(target_obj, scene_logic:MoveToGatherRange())

    elseif target_obj:GetType() == SceneObjType.DefenseTowerObj then
        self:ClearAllOperate()
        MoveCache.SetEndType(MoveEndType.DefenseObj)
        self:MoveToObj(target_obj, 4)
    elseif target_obj:GetType() == SceneObjType.EventObj then
        self:ClearAllOperate()
        MoveCache.SetEndType(MoveEndType.EventObj)
        self:MoveToObj(target_obj, 2)
    end

    return true
end

function GuajiWGCtrl:OnObjCreate(obj)
    if Scene.Instance:IsSceneLoading() then -- 在场景加载中不触发选择对象
        self.cache_select_obj_onloading = obj
        return
    end

    if MoveCache.is_valid and not self.select_obj then
        if MoveCache.end_type == MoveEndType.FightByMonsterId then
            if 0 ~= MoveCache.param1 and obj:GetType() == SceneObjType.Monster and obj:GetMonsterId() == MoveCache.param1 then
                if 0 == self.next_scan_target_monster_time then
                    self.next_scan_target_monster_time = Status.NowTime + 0.2
                end
            end
        elseif MoveCache.end_type == MoveEndType.NpcTask or MoveCache.end_type == MoveEndType.ClickNpc then
            if obj:GetType() == SceneObjType.Npc and obj:GetNpcId() == MoveCache.param1 then
                self:OnSelectObj(obj, 0 ~= MoveCache.task_id and SceneTargetSelectType.TASK or "")
            end
        elseif MoveCache.end_type == MoveEndType.FollowObj then
            if obj:GetObjId() == MoveCache.param1 then
                GuajiCache.target_obj = obj
            end
        end
    end

    if self.co then
        local flag = self.co(obj)
        if flag then
            self.co = nil
        end
    else
        if obj:IsNpc() and self.is_fly and (MoveCache.end_type == MoveEndType.NpcTask or MoveCache.end_type == MoveEndType.ClickNpc) then
            self:SetFlytoNpcTalkCamera(obj:GetNpcId())
        end
    end
end

function GuajiWGCtrl:OnObjDead(obj)
    self:OnObjInvalid(obj)
end

function GuajiWGCtrl:OnObjDelete(obj)
    self:OnObjInvalid(obj)
end

function GuajiWGCtrl:OnObjInvalid(obj)
    if obj == GuajiCache.target_obj then
        self:CancelSelect()
        GuajiCache.target_obj = nil
        GuajiCache.target_obj_id = COMMON_CONSTS.INVALID_OBJID
    end

    if obj == MoveCache.target_obj then
        MoveCache.target_obj = nil
    end

    if obj == AtkCache.target_obj then
        AtkCache.target_obj = nil
    end

    if obj == self.select_obj then
        self.select_obj:CancelSelect()
        self.select_obj = nil
    end
end

function GuajiWGCtrl:OnSceneLoadingQuite(old_scene_type, new_scene_type)
    -- print_error("OnSceneLoadingQuite:::",old_scene_type, new_scene_type)
    if nil ~= self.delay_timer_on_change_scene then
        GlobalTimerQuest:CancelQuest(self.delay_timer_on_change_scene)
        self.delay_timer_on_change_scene = nil
    end

    self.delay_timer_on_change_scene = GlobalTimerQuest:AddDelayTimer(function()
        local new_scene_id = Scene.Instance:GetSceneId()
        local map_config = MapWGData.Instance:GetMapConfig(new_scene_id)
        if map_config then
            self.scene_type = map_config.scene_type
        end

        local new_scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
        self.last_scene_id = new_scene_id
        self.last_scene_key = new_scene_key
        
        if MoveCache.is_valid then
            if MoveCache.move_type == MoveType.Pos then
                if MoveCache.scene_id == new_scene_id
                    and GuajiWGCtrl.CheckRange(MoveCache.x, MoveCache.y, MoveCache.range) then
                    self:DelayArrive()
                elseif (MoveCache.scene_id == self.last_scene_id and self.last_scene_id == new_scene_id and self.last_scene_key == new_scene_key) then
                    if GuajiCache.guaji_type ~= GuajiType.Auto then
                        if MoveCache.scene_id ~= self.last_scene_id then
                            self:StopGuaji()
                        else
                            local call_back = GuajiWGCtrl.Instance:GetMoveToPosCallBack()
                            GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
                            self:MoveToScenePos(MoveCache.scene_id, MoveCache.x, MoveCache.y, nil, nil, MoveCache.is_auto_move)
                            return
                        end
                    end
                else
                    local call_back = GuajiWGCtrl.Instance:GetMoveToPosCallBack()
                    GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
                    self:MoveToScenePos(MoveCache.scene_id, MoveCache.x, MoveCache.y, nil, nil, MoveCache.is_auto_move)
                end

            elseif MoveCache.move_type == MoveType.Fly then
                self:DelayArrive()
            end
        end

        if self.path_list then
            if self:CheakCanFly() then
                self.path_list = nil
            else
                local flag = true
                for k, v in pairs(self.path_list) do
                    if v then
                        if v.scene_id == new_scene_id then
                            self:MoveToSceneHelper(v.x, v.y)
                            flag = false
                            break
                        end
                    end
                end
                if flag then
                    self.path_list = nil
                end
            end
        end

        self:PlayerOperation()
    end, 0)--.3)

    if nil ~= self.cache_select_obj_onloading then
        self:OnObjCreate(self.cache_select_obj_onloading)
        self.cache_select_obj_onloading = nil
    end
end

function GuajiWGCtrl:OnSceneLoadingEnter()

    local scene_type = Scene.Instance:GetSceneType()
    local scene_logic = Scene.Instance:GetCurFbSceneCfg()
    if nil ~= scene_logic and 1 == scene_logic.is_auto_guaji then
        -- self:StopGuaji()
        -- self:SetGuajiType(GuajiType.Auto)
    elseif scene_type == SceneType.Common then
        -- unity3d项目暂时屏蔽
        -- local scene_id = Scene.Instance:GetSceneId()
        -- if BossWGData.IsWorldBossScene(scene_id) then
        -- return
        -- end
        if GuajiCache.guaji_type == GuajiType.Auto then
            self:StopGuaji()
        end
    end
end

function GuajiWGCtrl:DelayArrive()
    if nil ~= self.delay_arrive_timer then
        GlobalTimerQuest:CancelQuest(self.delay_arrive_timer)
        self.delay_arrive_timer = nil
    end

    if MoveCache.end_type == MoveEndType.Normal then
        self:OnArrive()
        return
    end

    local scene_id = MoveCache.scene_id
    self.delay_arrive_timer = GlobalTimerQuest:AddDelayTimer(function()
        self.delay_arrive_timer = nil
        if MoveCache.is_valid and MoveCache.move_type == MoveType.Fly and scene_id == MoveCache.scene_id then
            self:OnArrive()
        end
    end, 0.5)
end

-- 移动到目标对象  ignore_auto_fight 是否忽略寻路自动打怪
function GuajiWGCtrl:MoveToObj(target_obj, range, ignore_vip, scene_key, ignore_auto_fight, move_reason)
    -- print_error("MoveToObj", range, (target_obj ~= nil and target_obj.vo ~= nil) and target_obj.vo.name or "nil", ignore_auto_fight, move_reason)

    if not target_obj or not target_obj:GetType() or target_obj:IsDeleted() then
        return
    end

    GuajiWGCtrl.SetMoveValid(true)
    MoveCache.move_type = MoveType.Obj
    MoveCache.target_obj = target_obj
    MoveCache.range = range or 3
    MoveCache.is_ignore_auto_fight = ignore_auto_fight or false
    MoveCache.follow_move_reason = move_reason or CLIENT_MOVE_REQ_PARAM.NORMAL

    if MoveCache.end_type == MoveEndType.NpcTask then
        if target_obj:GetType() == SceneObjType.Npc and target_obj:GetNpcId() == MoveCache.param1 then
            if self:OnSelectObj(target_obj, 0 ~= MoveCache.task_id and SceneTargetSelectType.TASK or "") then
                return
            end
        end
    end

    -- local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
    local x, y = target_obj:GetLogicPos()

    local max_range = MoveCache.range
    MoveCache.x = x
    MoveCache.y = y
    MoveCache.scene_id = Scene.Instance:GetSceneId()

    local targetModleR = 3
    if target_obj:GetType() == SceneObjType.Monster then
        if AtkCache.range and AtkCache.range > 0 then
            targetModleR = AtkCache.range
        else
            -- targetModleR = BossWGData.Instance:GetMonsterRangeByid(target_obj:GetVo().monster_id)
            targetModleR = SkillWGData.Instance:GetRoleMinAtkRange()
        end
    elseif target_obj:GetType() == SceneObjType.Npc then
        targetModleR = TaskWGData.Instance:GetNPCRange(target_obj:GetNpcId())
    end

    if MoveCache.range < targetModleR then
        MoveCache.range = targetModleR
    end

    if GuajiWGCtrl.CheckRange(x, y, MoveCache.range) then
        if scene_key and scene_key ~= RoleWGData.Instance:GetAttr("scene_key") then
            -- Scene.SendChangeSceneLineReq(scene_key)
        else
            self.move_target = nil
            self:OnOperate()
        end

        return
    end

    self:MoveHelper(x, y, max_range, target_obj, ignore_vip, scene_key, MoveCache.is_auto_move, move_reason)
end

-- 移动到某个位置(一般情况下请调用这个函数)    sign 表示0普通，1点击世界地图传送 如果是1表示去到主城时会到中间点的随机位置 ignore_auto_fight 是否忽略寻路自动打怪
function GuajiWGCtrl:MoveToPos(scene_id, x, y, range, ignore_vip, scene_key, is_auto_move, move_to_pos_call_back, sign, move_reason, ignore_auto_fight, is_comefrom_joystick)
    -- print_error("MoveToPos", scene_id, x, y, range, move_reason, ignore_auto_fight, is_comefrom_joystick)

    if not tonumber(x) or not tonumber(y) then
        print_error("Error pos:::", x, y, scene_id, debug.traceback())
        return
    end

    x = math.floor(x)
    y = math.floor(y)
    range = range or 3

    scene_id = tonumber(scene_id)
    local cur_scene_id = Scene.Instance:GetSceneId()
    if scene_id == cur_scene_id and MoveCache.end_type == MoveEndType.NpcTask or MoveCache.end_type == MoveEndType.ClickNpc then
        if GuajiCache.target_obj_id then
            local target_obj = Scene.Instance:GetNpcByNpcId(GuajiCache.target_obj_id)
            if target_obj then
                range = TaskWGData.Instance:GetNPCRange(GuajiCache.target_obj_id)
                self:MoveToObj(target_obj, range, ignore_vip, nil)
                return
            end
        end
    end

    if MoveCache.end_type == MoveEndType.FightByMonsterId then
        if AtkCache.range and AtkCache.range > 0 then
            range = AtkCache.range
        else
            range = SkillWGData.Instance:GetRoleMinAtkRange()
        end
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.Common and scene_id ~= cur_scene_id and not self:CheakCanFly() then
        return
    end

    GuajiWGCtrl.SetMoveValid(true)
    MoveCache.move_type = MoveType.Pos
    MoveCache.scene_id = scene_id
    MoveCache.x = x
    MoveCache.y = y
    MoveCache.range = range
    MoveCache.is_auto_move = is_auto_move or false
    MoveCache.is_ignore_auto_fight = ignore_auto_fight or false
    MoveCache.follow_move_reason = move_reason or CLIENT_MOVE_REQ_PARAM.NORMAL

    if scene_id ~= cur_scene_id then
        self:MoveToScenePos(scene_id, x, y, ignore_vip, scene_key, is_auto_move,sign)
        return
    end

    -- local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
    if GuajiWGCtrl.CheckRange(x, y, range) then
        local call_back = move_to_pos_call_back or self.move_to_pos_call_back
        if scene_key and scene_key ~= RoleWGData.Instance:GetAttr("scene_key") then
            -- Scene.SendChangeSceneLineReq(scene_key)
        else
            self.move_target = nil
            self:OnOperate()
        end

        if call_back ~= nil then
            call_back()
            self.move_to_pos_call_back = nil
        end

        return
    end

    self.move_to_pos_call_back = move_to_pos_call_back or self.move_to_pos_call_back
    self:MoveHelper(x, y, range, nil, ignore_vip, scene_key, is_auto_move, move_reason, is_comefrom_joystick)
end

-- 飞行到场景的入口(特殊情况下调用，此方法会无视vip等级)
function GuajiWGCtrl:FlyToScene(scene_id, scene_key, sign)
    local scene_logic = Scene.Instance:GetSceneLogic()
    local x, y = scene_logic:GetTargetScenePos(scene_id)
    if x and y then
        MoveCache.move_type = MoveType.Fly
        TaskWGCtrl.Instance:JumpFly(scene_id, x, y, true, nil, sign)
        -- TaskWGCtrl.Instance:SendFlyByShoe(scene_id, x, y, scene_key, true)
    end
end

-- 飞行到场景的位置(特殊情况下调用，此方法会无视vip等级)
function GuajiWGCtrl:FlyToScenePos(scene_id, x, y, is_world_boss, scene_key, call_back)
    if self:CheakCanFly() or is_world_boss then
        self.fly_cache = {scene_id = scene_id, x = x, y = y}
        self.is_fly = true
        MoveCache.move_type = MoveType.Fly
        MoveCache.scene_id = scene_id
        MoveCache.x = x
        MoveCache.y = y

        if call_back ~= nil then
        	local cur_scene_id = Scene.Instance:GetSceneId()
        	if cur_scene_id ~= scene_id then
        		TaskWGCtrl.Instance:AddFlyUpList(call_back)
        	else
        		self:SetMoveToPosCallBack(call_back)
        	end
        end
        TaskWGCtrl.Instance:JumpFly(scene_id, x, y)
        -- TaskWGCtrl.Instance:SendFlyByShoe(scene_id, x, y, scene_key)
    else
        if call_back ~= nil then
            self:SetMoveToPosCallBack(call_back)
        end
        self:MoveToPos(scene_id, x, y, 1, 1)
    end
end

function GuajiWGCtrl:SetFlyCache(x, y, scene_id)
    self.fly_cache = {scene_id = scene_id, x = x, y = y}
    self.is_fly = true
end

-- 移动
function GuajiWGCtrl:MoveHelper(x, y, range, target_obj, ignore_vip, scene_key, is_auto_move, move_reason, is_comefrom_joystick)
    -- print_error("MoveHelper", x, y, range, (target_obj ~= nil and target_obj.vo ~= nil) and target_obj.vo.name or "nil", is_auto_move, move_reason, is_comefrom_joystick)

    if Scene.Instance:IsSceneLoading() then
        return
    end

    -- 容错,在自动捡东西的时候，如果触发了移动，把缓存坐标清掉
    if (self.goto_pick_x ~= nil and self.goto_pick_x ~= 0 and self.goto_pick_x ~= x) or (self.goto_pick_y ~= nil and self.goto_pick_y ~= 0 and self.goto_pick_y ~= y) then
        self:ResetPickCache()
    end

    local main_role = Scene.Instance:GetMainRole()
    local self_x, self_y = main_role:GetLogicPos()
    if x and y and AStarFindWay:IsBlock(x, y) then --障碍区，就寻找最近的点
        local old_x, old_y = x, y
        if Scene.Instance:GetSceneType() == SceneType.GuildMiJingFB then
            x, y = AStarFindWay:FindNearestValidPoint(x, y, 20)
        else
            x, y = AStarFindWay:FindNearestValidPoint(x, y, 10)
        end


        -- 仙盟争霸塔所在的点都是障碍区，对range做处理会导致攻城车攻击距离错误
        if Scene.Instance:GetSceneType() == SceneType.XianMengzhan and target_obj ~= nil and not target_obj:IsDeleted() and target_obj:IsGather() then
            range = range
        else
            local len = u3d.v2Length({x = old_x - x, y = old_y - y}, true)
            range = math.max(range - len - 1, 0)
        end

        MoveCache.x = x
        MoveCache.y = y
        MoveCache.range = range
    end

    if main_role ~= nil then
        local limit_x, limit_y = main_role:GetMovePosByLimit(x, y)
        if limit_x ~= x or limit_y ~= y then
            x = limit_x
            y = limit_y
            MoveCache.x = x
            MoveCache.y = y
            MoveCache.range = 1

            if GuajiWGCtrl.CheckRange(x, y, MoveCache.range) then
                local call_back = self.move_to_pos_call_back

                if scene_key and scene_key ~= RoleWGData.Instance:GetAttr("scene_key") then
                else
                    self:OnOperate()
                end

                if call_back ~= nil then
                    call_back()
                    self.move_to_pos_call_back = nil
                end

                return
            end
        end
    end

    self.last_mount_time = Status.NowTime
    self.auto_mount_up = true

    local new_range = range
    local scene_logic = Scene.Instance:GetSceneLogic()
    if target_obj and target_obj:IsCharacter() and target_obj:IsMove() and scene_logic then
        local dis = main_role:GetLogicDistance(target_obj.logic_pos, true)
        local tar_ex, tar_ey = GameMapHelper.WorldToLogic(target_obj.move_end_pos.x, target_obj.move_end_pos.y)
        local dis2 = main_role:GetLogicDistance(u3d.vec2(tar_ex, tar_ey), true)
        if dis2 > dis then
            new_range = math.max(new_range - (dis2 - dis), 0)
        end
    end

    if scene_key and scene_key ~= RoleWGData.Instance:GetAttr("scene_key") and Scene.SendChangeSceneLineReq then
        -- Scene.SendChangeSceneLineReq(scene_key)
        return
    end

    --策划需求，去掉vip可以直接点击1次传送的设定，改为都要双击才直接传送
    -- if (ignore_vip or VipPower.Instance:GetHasPower(VipPowerId.scene_fly)) and self:CheakCanFly(true) then
    if (ignore_vip) and self:CheakCanFly(true) then
        local distance = (x - self_x) * (x - self_x) + (y - self_y) * (y - self_y)
        if distance > OUTO_FLY_DIS * OUTO_FLY_DIS then
            main_role:SetArriveFunc(self.on_arrive_func)
            if MoveCache.end_type == MoveEndType.NpcTask or MoveCache.end_type == MoveEndType.ClickNpc then
                self.need_change_camera = true
            end
            self:FlyToScenePos(Scene.Instance:GetSceneId(), x, y, false, scene_key)
            return
        end
    end

    local call_back = GuajiWGCtrl.Instance:GetMoveToPosCallBack()
    main_role:DoMoveOperate(x, y, new_range, self.on_arrive_func, is_auto_move, nil, move_reason, nil, is_comefrom_joystick)
    GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)

    self.move_target = target_obj
    self.move_target_left_time = 1.0

    if (x - self_x) * (x - self_x) + (y - self_y) * (y - self_y) > 100 then
        GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.XunLu)
    end
end

-- 移动超过一段距离，自动上坐骑
function GuajiWGCtrl:MoveToRide(target_x, target_y)
    local flag = RoleWGData.Instance:CheckCanMount()
    if not flag then return end
    local main_role = Scene.Instance:GetMainRole()
    if not main_role then
        return
    end

    local self_x, self_y = main_role:GetLogicPos()
    local delta_pos = u3d.v2Sub(u3d.vec2(target_x, target_y), u3d.vec2(self_x, self_y))
    local distance = u3d.v2Length(delta_pos)
    if distance >= 20 then
        if not main_role:IsInTianShenState() and not main_role:IsRiding() and not main_role:IsXiuWeiBianShen() and not main_role:IsMitsurugi() then
            MountWGCtrl.Instance:SendMountGoonReq(1)
        end
    end
end

-- 移动到某个场景位置
function GuajiWGCtrl:MoveToScenePos(scene_id, x, y, ignore_vip, scene_key, is_auto_move, sign)
    -- print_error("MoveToScenePos", scene_id, x, y, ignore_vip, scene_key, is_auto_move)
    if Scene.Instance:GetSceneId() == scene_id then
        self:MoveHelper(x, y, MoveCache.range, nil, ignore_vip, scene_key, is_auto_move)
        return
    end

    local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
    if scene_cfg then
        if scene_cfg.levellimit > GameVoManager.Instance:GetMainRoleVo().level then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.CanNotEnter, RoleWGData.GetLevelString(scene_cfg.levellimit)))
            self:StopGuaji()
            return
        end
    end

    if self:CheakCanFly() then
        if MoveCache.cant_fly == false then
            TaskWGCtrl.Instance:SendFlyForFree(scene_id, x, y, scene_key, nil, nil)
        else --挖宝
            self:FlyToScene(scene_id, scene_key)
        end
    else
        MoveCache.scene_id = scene_id
        MoveCache.x = x
        MoveCache.y = y
        local scene_logic = Scene.Instance:GetSceneLogic()
        self.path_list = scene_logic:GetScenePath(Scene.Instance:GetSceneId(), scene_id)
        if self.path_list then
            local path = self.path_list[1]
            if path then
                self:MoveToSceneHelper(path.x, path.y, is_auto_move)
                path = nil
            end
        end
    end
end

-- 移动到其它场景
function GuajiWGCtrl:MoveToSceneHelper(x, y, is_auto_move)
    self.last_mount_time = Status.NowTime
    self.auto_mount_up = true
    local range = 0
    if GuajiWGCtrl.CheckRange(x, y, 2) then
        if Scene.Instance:GetSceneId() ~= MoveCache.scene_id then
            local flag, str = Scene.Instance:CheckCanChangeScene()
            if not flag then
                if str then
                    TipWGCtrl.Instance:ShowSystemMsg(str)
                end
                if TaskGuide.Instance:CanAutoAllTask() then
                    TaskGuide.Instance:CanAutoAllTask(false)
                end
                return
            end
            local bool, door_obj =  Scene.Instance:GetIsInDoor()
            if bool then
                Scene.Instance:IsChangeSceneIng(true)
                Scene.SendTransportReq(door_obj:GetDoorId())
            end
        end
    end

    if nil == is_auto_move then
        is_auto_move = true
    end

    Scene.Instance:GetMainRole():DoMoveOperate(x, y, range, function()
        if self.path_list then
            if self:CheakCanFly() then
                self.path_list = nil
            else
                local flag = true
                for k, v in pairs(self.path_list) do
                    if v.scene_id == Scene.Instance:GetSceneId() then
                        self:MoveToSceneHelper(v.x, v.y)
                        flag = false
                        -- GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)
                        break
                    end
                end
                if flag then
                    self.path_list = nil
                end
            end
        end
    end, is_auto_move)
    -- GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.XunLu)
end

-- 移动到某个场景
-- 找不到场景入口点的情况下则找该场景的传送点
function GuajiWGCtrl:MoveToScene(scene_id)
    if Scene.Instance:GetSceneId() == scene_id then
        return
    end

    --在非普通场景或者特殊的普通场景不能传送
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type ~= SceneType.Common then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotFindPath)
        print_error("GuajiWGCtrl:MoveToScene1165","scene_type:",scene_type,"SceneType.Common:",SceneType.Common)
        return
    end

    local scene_logic = Scene.Instance:GetSceneLogic()
    local x, y = scene_logic:GetTargetScenePos(scene_id)
    if x == nil or y == nil then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotToTarget)
        return
    end
    self:MoveToScenePos(scene_id, x, y)
end

-- 到达
function GuajiWGCtrl:OnArrive()
    self.move_target = nil
    -- if self.time_quest then
    --     GlobalTimerQuest:CancelQuest(self.time_quest)
    --     self.time_quest = nil
    -- end

    local move_reason = MoveCache.follow_move_reason or CLIENT_MOVE_REASON.NONE
    if MoveCache.move_type == MoveType.Obj then
        if nil ~= MoveCache.target_obj then
            local x, y = MoveCache.target_obj:GetLogicPos()
            local main_role = Scene.Instance:GetMainRole()
            if main_role ~= nil and not main_role:IsDeleted() then
                x, y = main_role:GetMovePosByLimit(x, y)
            end

            if AStarFindWay:IsBlock(x, y) and not GuajiWGCtrl.CheckRange(MoveCache.x, MoveCache.y, MoveCache.range) then
                self:MoveToPos(Scene.Instance:GetSceneId(), MoveCache.x, MoveCache.y, MoveCache.range, nil, nil, nil, nil, nil, move_reason)
                return
            elseif not AStarFindWay:IsBlock(x, y) and not GuajiWGCtrl.CheckRange(x, y, MoveCache.range) then
                self:MoveToObj(MoveCache.target_obj, MoveCache.range, nil, nil, nil, move_reason)
                return
            end
        end
    elseif MoveCache.move_type == MoveType.Pos or MoveCache.move_type == MoveType.Fly then
        if MoveCache.scene_id ~= Scene.Instance:GetSceneId() then
            return
        end

        if not GuajiWGCtrl.CheckRange(MoveCache.x, MoveCache.y, MoveCache.range) then
            self:MoveToPos(MoveCache.scene_id, MoveCache.x, MoveCache.y, MoveCache.range, nil, nil, MoveCache.is_auto_move, nil, nil, move_reason)
            return
        end
    end

    if self.move_to_pos_call_back ~= nil then
        self.move_to_pos_call_back()
        self.move_to_pos_call_back = nil
    end

    self:OnOperate()
    if GuajiCache.guaji_type == GuajiType.None then
        GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)
    end
end

-- 处理移动后的操作逻辑
function GuajiWGCtrl:OnOperate()
    TaskGuide.Instance:ClickSkillStopTask()
    if not MoveCache.is_valid then
        return
    end

    GuajiWGCtrl.SetMoveValid(false)
    local end_type = MoveCache.end_type
    MoveCache.SetEndType(MoveEndType.Normal)
    MoveCache.cant_fly = false
    -- print_error("----处理移动后的操作逻辑----", end_type)
    if end_type == MoveEndType.Fight then
        self:OnOperateFight()
    elseif end_type == MoveEndType.AttackTarget then
        self:OnOperateAttackTarget()
    elseif end_type == MoveEndType.ClickNpc then --采集和npc对话时停止挂机（避免期间跑去打怪）
        self:OnOperateClickNpc()
    elseif end_type == MoveEndType.NpcTask then
        self:OnOperateNpcTalk()
    elseif end_type == MoveEndType.FightByMonsterId then
        self:OnOperateFightByMonsterId()
    elseif end_type == MoveEndType.Gather then
        self:OnOperateGather()
    elseif end_type == MoveEndType.GatherById then
        self:OnOperateGatherById()
    elseif end_type == MoveEndType.PickItem then
        self:OnOperatePickItem()
    elseif end_type == MoveEndType.Auto then
        self:OnOperateAutoFight()
    elseif end_type == MoveEndType.FollowObj then
        self:OnOperateFollowObj()
    elseif end_type == MoveEndType.EventObj then
        self:OnOperateZhuaGui()
    elseif end_type == MoveEndType.PickAroundItem then
        self:OnOperatePickAroundItem()
    elseif end_type == MoveEndType.DoNothing then
        --donothing
    elseif end_type == MoveEndType.DefenseObj then
        self:OnOperateDefenseTowerObj()
    else
        if GuajiCache.guaji_type == GuajiType.Auto
                or GuajiCache.guaji_type == GuajiType.Monster
                or GuajiCache.guaji_type == GuajiType.Temporary then
            return
        end
        self:StopGuaji()
    end

    if self.arrive_call_back ~= nil then
        self.arrive_call_back()
    end
    self.arrive_call_back = nil
    self:ResetMoveCache()
end

function GuajiWGCtrl:TaskToGuaJi()
    return false
end

-- 战斗1
function GuajiWGCtrl:OnOperateFight()
    if not AtkCache.is_valid then
        return
    end

    GuajiWGCtrl.SetAtkValid(false)
    if nil == AtkCache.target_obj then
        self:DoAttack()
    else
        if AtkCache.target_obj == Scene.Instance:GetObj(AtkCache.target_obj_id) then
            local is_guaji = self:TaskToGuaJi()
            if not is_guaji then
                -- print_error("战斗1")
                if GuajiWGCtrl.CheckRange(MoveCache.x, MoveCache.y, AtkCache.range) then
                    -- print_error("战斗2")
                    self:DoAttack()
                end
            end
        else
            self:TaskToGuaJi()
        end
    end
end

function GuajiWGCtrl:DoAttack()
    local status = XunLuStatus.None
    if GuajiCache.guaji_type ~= GuajiType.None and GuajiCache.guaji_type ~= GuajiType.Temporary then
        status = XunLuStatus.AutoFight
    end

    GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, status)
    local main_role = Scene.Instance:GetMainRole()
    -- local is_not_normal_skill = SkillWGData.IsNotNormalSkill(AtkCache.skill_id)
    if main_role and not main_role:IsAtkPlaying() then --or is_not_normal_skill
        main_role:SetAttackIndex(AtkCache.attack_index)
        main_role:SetAttackParam(AtkCache.is_specialskill)

        local skill_id = SkillWGData.Instance:GetNormalSkillTransformAwakeSkill(AtkCache.skill_id)
        main_role:DoAttack(skill_id, AtkCache.x, AtkCache.y, AtkCache.target_obj_id, nil, nil, AtkCache.use_type, AtkCache.skill_type)
        if AtkCache.skill_id == self.skill_id then
            self.skill_id = nil
        end
    end
end

function GuajiWGCtrl:ClearAtkCache()
    GuajiWGCtrl.SetAtkValid(false)
    AtkCache.atk_type = AtkType.Normal
    AtkCache.skill_id = 0
    AtkCache.x = 0
    AtkCache.y = 0
    AtkCache.dir = 0
    AtkCache.is_specialskill = false
    AtkCache.special_distance = 0
    AtkCache.target_obj = nil
    AtkCache.target_obj_id = COMMON_CONSTS.INVALID_OBJID
    AtkCache.range = 0
    AtkCache.next_sync_pos_time = 0
    AtkCache.attack_index = 1
    AtkCache.use_type = ATTACK_USE_TYPE.GUAJI
    AtkCache.skill_type = ATTACK_SKILL_TYPE.NONE
end

-- 攻击目标
function GuajiWGCtrl:OnOperateAttackTarget()
    if nil ~= MoveCache.target_obj then
        local is_enemy, msg = Scene.Instance:IsEnemy(MoveCache.target_obj)
        if is_enemy then
            self:DoAttackTarget(MoveCache.target_obj)
        else
            TipWGCtrl.Instance:ShowSystemMsg(msg)
        end
    end
end

--播放NPC对话声音
function GuajiWGCtrl:PlayNpcVoice(npc_obj_id)
    if npc_obj_id then
        local npc_obj = Scene.Instance:GetObjectByObjId(npc_obj_id)
        if npc_obj then
            local npc_vo = npc_obj:GetVo()
            if npc_vo then
                local npc_config = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list
                local npc_data = npc_config[npc_vo.npc_id or - 1]
                if npc_data then
                    local voice_id = npc_data.voiceid
                    if voice_id and voice_id ~= "" then
                        if self.last_play_id == voice_id then
                            if Status.NowTime - self.npc_talk_interval < self.last_play_time then
                                return
                            end
                        end
                        self.last_play_id = voice_id
                        self.last_play_time = Status.NowTime
                        local bundle, asset = ResPath.GetNpcVoiceRes(voice_id)
                        AudioManager.PlayAndForget(bundle, asset)
                    end
                end
            end
        end
    end
end

-- 与npc对话(点击)
function GuajiWGCtrl:OnOperateClickNpc()
    if MoveCache.param1 then
        self:SetFlytoNpcTalkCamera(MoveCache.param1)
    end

    if FunctionGuide.Instance:GetGuideViewIsOpen() then
        return
    end

    local ignore_list = {[GuideModuleName.ChatView] = true}
    if ViewManager.Instance:HasOpenView(ignore_list) then
        self.cacha_open_npc = true
        return
    end

    self.cacha_open_npc = false
    if not MoveCache.target_obj then
        if not MoveCache.param1 then
            return
         end

        MoveCache.target_obj = Scene.Instance:GetNpcByNpcId(MoveCache.param1)
    end

    if not MoveCache.target_obj or not MoveCache.target_obj:GetVo() then
        print_warning("No target_obj", MoveCache.param1)
        return
    end

    if nil == MoveCache.target_obj or MoveCache.target_obj:GetType() ~= SceneObjType.Npc then
        return
    end

    local npc_obj_id = MoveCache.target_obj:GetVo().obj_id
    if nil == npc_obj_id then
        return
    end

    local npc_obj = Scene.Instance:GetObjectByObjId(npc_obj_id)
    if nil == npc_obj then
        return
    end

    local main_role = Scene.Instance:GetMainRole()
    if not npc_obj:IsBuilding() then
        npc_obj:SetDirectionByXY(main_role:GetLogicPos())
    end

    main_role:SetDirectionByXY(npc_obj:GetLogicPos())
    if npc_obj:IsWalkNpc() then
        npc_obj:Stop()
    end

    local npc_vo = npc_obj:GetVo()
    if nil == npc_vo then
        return
    end

    local npc_id = npc_vo.npc_id
    if YunbiaoWGData.Instance:GetIsHuShong() and YunbiaoWGData.Instance:GetAcceptNpcId() ~= npc_id then
        local yun_biao_task = YunbiaoWGData.Instance:GetTaskIdByCamp()
        if TaskWGData.Instance:GetTaskIsCanCommint(yun_biao_task) then
            TaskWGCtrl.Instance:SendTaskCommit(yun_biao_task)
        end

        return
    end

    --日常公会任务副本特殊处理
    local exp_task_fb_cfg = TaskWGData.Instance:GetExpTaskFbcfg(npc_id)
    if exp_task_fb_cfg then
        --进入副本以前记录当前的任务id
        if exp_task_fb_cfg.task_id then
            TaskWGData.Instance:CurrTaskId(exp_task_fb_cfg.task_id)
        end

        FuBenWGCtrl.Instance:SendEnterFB(exp_task_fb_cfg.c_param1, exp_task_fb_cfg.c_param3)
        return
    end

    local task_cfg = TaskWGData.Instance:GetNpcOneExitsTask(npc_id)
    if npc_id == GameEnum.Qicheng_Npc then
        if not task_cfg then
            local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
            task_cfg = TaskWGData.Instance:GetTaskConfig(task_list[1])
        end
    end

    -- 任务链玩法NPC对话处理, 只在NPC身上没任务的时候处理
    if task_cfg == nil and OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN) then
        local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
        if day_index_cfg ~= nil then
        local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
            if task_chain_cfg ~= nil and task_chain_cfg.npc_id == npc_id then
                ViewManager.Instance:Open(GuideModuleName.OperationTaskChainEntranceView)
                return
            end
        end
    end

    if task_cfg then -- 有任务
        local status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
        local task_data = DailyWGData.Instance:GetTaskTuMoData()
        local commit_times = task_data.commit_times
        local transfer_task_cfg = TaskWGData.Instance:GetTransferTaskFbcfg(npc_id)

        -- 有时候没接悬赏，但是可接任务有个任务
        if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
            if TaskWGData.Instance:GetCurrBountyTask() == nil then
                if TaskWGData.Instance:GetTaskOpen(GameEnum.TASK_TYPE_RI) then
                    ViewManager.Instance:Open(GuideModuleName.TaskShangJinView)
                else
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.TaskShangJinNotOpen)
                end
                return
            end
        end

        if status == GameEnum.TASK_STATUS_ACCEPT_PROCESS and
            task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_7 then
            if task_cfg.c_param1 == GameEnum.TASK_SATISFY_STATUS_TYPE_66 then
                TaskWGCtrl.Instance:SendYouLi(YouLiType.Type1)
                TaskGuide.Instance:SpecialConditions(true, 5)
            end
        elseif (task_cfg.task_type == GameEnum.TASK_TYPE_RI or
            task_cfg.task_type == GameEnum.TASK_TYPE_MENG or
            task_cfg.task_type == GameEnum.TASK_TYPE_ZHUAN) and
            task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 and
            task_cfg.accept_dialog == "" and                                                -- 没有接取对话才不弹对话框
            status == GameEnum.TASK_STATUS_CAN_ACCEPT then
            -- 日常，仙盟，转职接取进副本任务时，不弹对话框,直接请求领取
            TaskWGCtrl.Instance:SendTaskAccept(task_cfg.task_id)
        elseif transfer_task_cfg then
            ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = npc_id, transfer_task_cfg = transfer_task_cfg})
        else
            ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = npc_id})
        end
    elseif GuildBattleRankedWGData.Instance:GetGuildNpcId(0) == npc_id or GuildBattleRankedWGData.Instance:GetGuildNpcId(1) == npc_id then
        ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = npc_id})
        GuajiWGCtrl.Instance:StopGuaji()

    elseif MarryWGData.Instance:GetYueLaoNpcId() == npc_id then -- 月老
        MarryWGCtrl.Instance:OpenExplainView()
        npc_obj:AutoRotation(true)

    elseif YunbiaoWGData.Instance:GetAcceptNpcId() == npc_id then -- 护送
        YunbiaoWGCtrl.Instance:OpenWindow()
    elseif npc_id == GameEnum.ShangJin_Npc then
        if TaskWGData.Instance:GetTaskOpen(GameEnum.TASK_TYPE_RI) then
            ViewManager.Instance:Open(GuideModuleName.TaskShangJinView)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.TaskShangJinNotOpen)
        end
    else
        ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = npc_id})
    end
end

-- 与npc对话(任务)
function GuajiWGCtrl:OnOperateNpcTalk()
    local npc_id = MoveCache.param1
    local npc = Scene.Instance:GetNpcByNpcId(npc_id)
    if nil ~= npc_id then
        if nil ~= npc then
            -- TaskWGCtrl.Instance:SendNpcTalkReq(npc:GetObjId(), nil)
             self:OnSelectObj(npc, 0 ~= MoveCache.task_id and SceneTargetSelectType.TASK or "")
            self:SetFlytoNpcTalkCamera(npc_id)
        else
            -- TaskWGCtrl.Instance:SendNpcTalkReq(nil, npc_id)
            self.co = function(obj)
                if not obj then return false end
                if obj:GetType() == SceneObjType.Npc and obj:GetNpcId() == npc_id then
                    self:OnSelectObj(obj, 0 ~= MoveCache.task_id and SceneTargetSelectType.TASK or "")
                    -- TaskWGCtrl.Instance:SendNpcTalkReq(obj:GetObjId(), nil)
                    self:SetFlytoNpcTalkCamera(npc_id)
                    return true
                else
                    return false
                end
            end
        end
    end
end

-- 飞到npc位置是的相机视角
function GuajiWGCtrl:SetFlytoNpcTalkCamera(npc_id, force)
    if npc_id and not IsNil(MainCameraFollow) and (self.need_change_camera or force) then
        local scene_cfg = Scene.Instance:GetSceneConfig()
        if scene_cfg then
            for k,v in pairs(scene_cfg.npcs) do
               if v.id == npc_id then
                    local x = MainCamera.transform.parent.transform.localEulerAngles.x
                    local y = v.rotation_y - 180
                    if y < -150 then
                        y = y + 360
                    end

                    Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.NORMAL, x, y, nil, nil)
                    break
               end
            end
        end
    end
    self.need_change_camera = false
end

-- 打怪
function GuajiWGCtrl:OnOperateFightByMonsterId()
    local monster = Scene.Instance:SelectMinDisMonster(MoveCache.param1, Scene.Instance:GetSceneLogic():GetGuajiSelectObjDistance(),MoveCache.param2)
    if nil ~= monster then
        GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, monster, SceneTargetSelectType.SELECT)
        GuajiCache.monster_id = monster:GetMonsterId()
        GuajiCache.target_obj = monster
        GuajiWGCtrl.SetMoveValid(false)
        self:SetGuajiType(GuajiType.Monster)
        self:DoAttackTarget(monster)
   end
end

-- 采集
function GuajiWGCtrl:OnOperateGather()
    if nil ~= MoveCache.target_obj and nil ~= MoveCache.target_obj.vo then
        self:CheckCanGather(MoveCache.target_obj)
    end
end

function GuajiWGCtrl:CheckCanGather(obj)
    if not obj then
        return false
    end

    local scene_logic = Scene.Instance:GetSceneLogic()
    local can_gather = scene_logic:GetIsCanGather(obj)
    if not can_gather then
        return
    end

    local vo = obj:GetVo()
    local scene_type = Scene.Instance:GetSceneType()

    if scene_type == SceneType.CROSS_AIR_WAR then
        if vo and vo.param then
            local cfg = CrossAirWarWGData.Instance:GetBoxGatherCfgBySeq(vo.param)
            if cfg and cfg.cost_item_id ~= nil and cfg.cost_item_id ~= 0 then
                CrossAirWarWGCtrl.Instance:OpenAirWarGatherTips(cfg, function()
                    scene_logic:SetIsSendGather(true)
                    Scene.SendStartGatherReq(obj:GetObjId(), 1)
                end)
                return
            end
        end
    end

    local can_gather_num = obj:CheckIsCanGather()

    if can_gather_num ~= 0 then
        if can_gather_num == 1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips2)
        elseif can_gather_num == 2 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips)
        elseif can_gather_num == 3 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips5)
        elseif can_gather_num == 4 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips6)
        elseif can_gather_num == 5 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips8)
        end
        return
    end

    scene_logic:SetIsSendGather(true)
    Scene.SendStartGatherReq(obj:GetObjId(), 1)
end

-- 根据采集id采集
function GuajiWGCtrl:OnOperateGatherById()
    local gather_id = MoveCache.param1
    local x = MoveCache.x
    local y = MoveCache.y
    local obj = Scene.Instance:GetGatherByGatherIdAndPosInfo(gather_id, x, y)

    if nil ~= obj then
        obj:OnClick()
        self:CheckCanGather(obj)
    end
end

-- 拾取
function GuajiWGCtrl:OnOperatePickItem()
    if nil ~= MoveCache.target_obj and MoveCache.target_obj:GetVo() then
        Scene.ScenePickItem({MoveCache.target_obj:GetVo().obj_id})
    end
end

-- 自动进入战斗状态
function GuajiWGCtrl:OnOperateAutoFight()
    self:SetGuajiType(GuajiType.Auto)
end

--抓鬼
function GuajiWGCtrl:OnOperateZhuaGui()
    if MoveCache.target_obj then
        MoveCache.target_obj:ArriveOperateHandle()
    end
end

function GuajiWGCtrl:OnOperatePickAroundItem()
    Scene.Instance:PickAllFallItem()
    self.goto_pick_x = 0
    self.goto_pick_y = 0
    self.next_can_goto_pick_time = Status.NowTime + PickCache.interval
end

function GuajiWGCtrl:ResetPickCache()
    if MoveEndType.PickAroundItem == MoveCache.end_type then
        MoveCache.SetEndType(MoveEndType.Normal)
    end

    self.goto_pick_x = 0
    self.goto_pick_y = 0
    self.next_can_goto_pick_time = 0
end

function GuajiWGCtrl:GetPickCache()
    return self.goto_pick_x, self.goto_pick_y, self.next_can_goto_pick_time
end

function GuajiWGCtrl:OnOperateDefenseTowerObj()
    if MoveCache.target_obj then
        GlobalEventSystem:Fire(ObjectEventType.SELECT_DEFENSE_OBJ, MoveCache.target_obj)
    end
end

-- 跟随obj
function GuajiWGCtrl:OnOperateFollowObj()
    if Scene.Instance:GetMainRole():GetObjId() == MoveCache.param1 then
        -- print_warning("不能跟随自己")
        self:StopGuaji()
        return
    end
    MoveCache.SetEndType(MoveEndType.FollowObj)
end

function GuajiWGCtrl:ClearAllOperate()
    GuajiWGCtrl.SetAtkValid(false)
    GuajiWGCtrl.SetMoveValid(false)
    MoveCache.SetEndType(MoveEndType.Normal)
    self.skill_id = nil
    self.cache_skill_x = nil
    self.cache_skill_y = nil
    self.path_list = nil
    self.move_target = nil
    self.goto_pick_x = 0
    self.goto_pick_y = 0
    self.next_can_goto_pick_time = 0
    self.next_scan_target_monster_time = 0
    self.arrive_call_back = nil
    self.move_to_pos_call_back = nil
    MoveCache.cant_fly = false
    MoveCache.is_auto_move = true
end

function GuajiWGCtrl:ClearTaskOperate(not_clear_toggle)
    self:ClearAllOperate()
    if not_clear_toggle then
        return
    end
    GlobalEventSystem:Fire(MainUIEventType.MAINUI_CLEAR_TASK_TOGGLE)
end

-- 攻击
local stand_on_atk_time = nil
function GuajiWGCtrl:UpdateAtk(now_time)
    local main_role = Scene.Instance:GetMainRole()
    if not main_role or main_role:IsAtkPlaying() then
        return
    end

    local role_vo = main_role:GetVo()
    if AtkCache.target_obj then
        -- 冲锋逻辑处理
        --[[
        原本是写在DoAttackTarget里面的
        由于AtkCache.is_valid和MoveCache.is_valid这两个导致上面那个方法只会进入一次
        从远距离跑到冲锋距离后也不会冲锋
        所以写在这边
        ]]

        local check_flag = true
        local is_can_chongfeng = false
        local is_charge_before_skill = false

        if AtkCache.skill_id == nil or role_vo == nil then
            check_flag = false
        end

        if check_flag
        and (Scene.Instance:GetSceneType() == SceneType.GuideFb                                               -- 不是假副本的情况下才能冲锋
        or SPECIAL_APPEARANCE_TYPE.XMZ == role_vo.special_appearance
        or SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == role_vo.special_appearance) then
            check_flag = false
        end

        if check_flag and AtkCache.target_obj:GetType() ~= SceneObjType.Monster then
            check_flag = false
        end

        if check_flag then
            local main_role_x, main_role_y = main_role:GetLogicPos()
            local target_x, target_y = AtkCache.target_obj:GetLogicPos()
            local delta_pos = u3d.v2Sub(u3d.vec2(target_x, target_y), u3d.vec2(main_role_x, main_role_y))
            local distance = u3d.v2Length(delta_pos, true)

            -- 角色觉醒技能id只做特效变化逻辑处理，实际逻辑要转换成正常角色技能处理
            local normal_skill = SkillWGData.Instance:GetAwakeSkillToNormalSkill(AtkCache.skill_id)
            -- 冲锋完再释放技能
            local before_skill_cfg = SkillWGData.Instance:GetBeforeSkillCfg(normal_skill or AtkCache.skill_id)
            if before_skill_cfg and before_skill_cfg.skill_type == FRONT_SKILL_TYPE.CHARGE then
                is_charge_before_skill = true
                local cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(normal_skill or AtkCache.skill_id, 1)
                if cfg ~= nil and cfg.distance + 2 < distance and distance <= COMMON_CONSTS.CHONGFENG_MAX_DIS then
                    is_can_chongfeng = true
                    AtkCache.skill_type = ATTACK_SKILL_TYPE.BEFOTR_SKILL_CHARGE
                end
            end
        end

        if is_can_chongfeng then
            local pos_x, pos_y = AtkCache.target_obj:GetRealPos()
            local t_x, t_y = AtkCache.target_obj:GetLogicPos()
            local check_dis = nil

            if AtkCache.target_obj:IsMonster() then
                check_dis = AtkCache.target_obj:GetChecksModel()
            end

            local is_succ =  main_role:ReqChongfengToObj(pos_x, pos_y, function()
                -- 冲锋完必须同步一次，因为冲锋完释放技能时会检测客户端当前坐标与服务器坐标是否完成一致
                -- 否则会fixpos。
                -- 冲锋设计缺陷，客户端发送目标id。服务器跟客户端各自计算冲锋到的目标点，这里有可能有误差
                -- 导致释放技能时坐标不一致而触发fixpos
                main_role:SendMoveReq()
                main_role:DoStand()

                -- 下一帧马上发起攻击，才有击锋造成伤害之感
                self.next_scan_target_monster_time = 0
            end, check_dis, t_x, t_y)

            if is_succ then
                return
            end
        end
    end

    if MoveCache.is_valid then
        stand_on_atk_time = stand_on_atk_time or now_time + 0.5
        if Scene.Instance:GetMainRole():IsStand() and stand_on_atk_time < now_time then --容错，如果在挂机中站着不动，就重新进入挂机
            GuajiWGCtrl.SetMoveValid(false)
        end
        return
    end

    stand_on_atk_time = nil

    if nil ~= AtkCache.target_obj and AtkCache.use_type == ATTACK_USE_TYPE.GUAJI then
        if AtkCache.target_obj == Scene.Instance:GetMainRole() then
            GuajiWGCtrl.SetAtkValid(false)
            self:DoAttack()
            return
        end

        if AtkCache.target_obj ~= Scene.Instance:GetObj(AtkCache.target_obj_id) then
            GuajiWGCtrl.SetAtkValid(false)
            return
        end

        MoveCache.SetEndType(MoveEndType.Fight)
        self:MoveToObj(AtkCache.target_obj, AtkCache.range, nil, nil, nil, CLIENT_MOVE_REASON.GUAJI)
    else
        if AtkCache.use_type == ATTACK_USE_TYPE.GUAJI then
            MoveCache.SetEndType(MoveEndType.Fight)
            self:MoveToPos(Scene.Instance:GetSceneId(), AtkCache.x, AtkCache.y, AtkCache.range, nil, nil, nil, nil, nil, nil, nil, CLIENT_MOVE_REASON.GUAJI)
        else
            GuajiWGCtrl.SetAtkValid(false)
            self:DoAttack()
        end
    end
end

function GuajiWGCtrl:UpdateFollowAtkTarget(now_time)
    if AtkCache.use_type == nil or AtkCache.use_type == ATTACK_USE_TYPE.GUAJI then
        if (MoveEndType.Fight == MoveCache.end_type or MoveEndType.FightByMonsterId == MoveCache.end_type)
            and nil ~= AtkCache.target_obj and now_time >= AtkCache.next_sync_pos_time
        then
            local new_x, new_y = AtkCache.target_obj:GetLogicPos()
            -- GuajiWGCtrl.CheckRange(x, y, range, s_x, s_y)
            if (new_x - AtkCache.x)^ 2 + (new_y - AtkCache.y)^ 2 > 4 then
                AtkCache.next_sync_pos_time = now_time + 0.3
                AtkCache.x = new_x
                AtkCache.y = new_y
                self:MoveToObj(AtkCache.target_obj, AtkCache.range, nil, nil, nil, CLIENT_MOVE_REASON.GUAJI)
            end
        end
    end
end

function GuajiWGCtrl:UpdateMonsterTarget(now_time)
    if now_time < AtkCache.next_sync_pos_time or MoveEndType.FightByMonsterId ~= MoveCache.end_type or GuajiCache.monster_id <= 0 then
        return
    end

    if nil ~= AtkCache.target_obj and AtkCache.target_obj.vo ~= nil and AtkCache.target_obj.vo.monster_id == GuajiCache.monster_id then
        return
    end

    local monster = Scene.Instance:SelectMinDisMonster(GuajiCache.monster_id, 100)
    if not monster then
        AtkCache.target_obj = nil
        return
    end

    AtkCache.next_sync_pos_time = now_time + 1
    local range = (MoveCache.range and MoveCache.range > 0) and MoveCache.range or BossWGData.Instance:GetMonsterRangeByid(GuajiCache.monster_id)
    self:MoveToObj(monster, range, 2, nil, nil, CLIENT_MOVE_REASON.GUAJI)
end

function GuajiWGCtrl:UpdateGatherTarget(now_time)
    if now_time >= AtkCache.next_sync_pos_time and MoveEndType.GatherById == MoveCache.end_type and MoveCache.param1 and MoveCache.param1 > 0 then
        local gather = Scene.Instance:SelectMinDisGather(MoveCache.param1)
        if gather then
            AtkCache.next_sync_pos_time = now_time + 5
            local new_x, new_y = gather:GetLogicPos()
            if  MoveCache.x ~= new_x or MoveCache.y ~= new_y then
                self:MoveToPos(MoveCache.scene_id, new_x, new_y, MoveCache.range)
            end
        end
    end
end

-- 挂机逻辑
function GuajiWGCtrl:UpdateGuaji(now_time)
    if ViewManager.Instance:IsOpen(GuideModuleName.TaskDialog) then
        return
    end

    if TaskGuide.Instance:TaskGuajiLimit() then
        return
    end

    if GuajiCache.guaji_type == GuajiType.Follow then
        if GuajiCache.target_obj then
            if GuajiCache.target_obj:IsDeleted() then
                -- print_warning("目标离开视野")
                GuajiCache.target_obj = nil
                self:StopGuaji()
                return
            end

            local target_x, target_y = GuajiCache.target_obj:GetLogicPos()
            local self_x, self_y = Scene.Instance:GetMainRole():GetLogicPos()
            local delta_pos = u3d.vec2(target_x - self_x, target_y - self_y)
            local distance = u3d.v2Length(delta_pos)
            if distance > 4 then
                self:MoveToObj(GuajiCache.target_obj, 1, 1, nil, nil, CLIENT_MOVE_REASON.GUAJI)
            end
        else
            local obj_list = Scene.Instance:GetObjList()
            if obj_list then
                for k, v in pairs(obj_list) do
                    if v:GetObjId() == MoveCache.param1 then
                        GuajiCache.target_obj = v
                        break
                    end
                end
            end
        end
        return
    end

    if (MoveCache.is_valid and not Scene.Instance:GetMainRole():IsStand())
        -- or now_time < SkillWGData.Instance:GetGlobalCDEndTime()-- 这里会影响普攻释放速度，先去掉
        or now_time < FightWGCtrl.Instance:NextCanAtkTime() then
        return
    end

    local target_obj = nil
    if GuajiCache.guaji_type == GuajiType.Auto then
        local scene = Scene.Instance
        target_obj = self:SelectAtkTarget(true)
        local scene_logic = scene:GetSceneLogic()
        local main_role = scene:GetMainRole()

        -- 挂机跟随，如果已经没目标可以打了，并且缓存的跟随对象是有效的，这个时候就恢复跟随
        local recovery_valid = self:CheckIsRecoveryFollowVaild()
        if recovery_valid and target_obj == nil then
            self:TryRecoveryFollowState()
            return
        end

        if nil == target_obj and (main_role ~= nil and not main_role:IsFollowState()) and scene_logic and scene_logic:GetGuaJiInfo() == nil then
            if Status.NowTime >= self.guai_ji_next_move_time then
                self.guai_ji_next_move_time = Status.NowTime + 2
                -- 默认了场景逻辑的挂机坐标,取不到默认全地图搜怪
                local target_x, target_y = scene_logic:GetGuajiPos()
                if nil == target_x or nil == target_y
                    or scene:GetSceneType() == SceneType.PET_FB
                    or scene:GetSceneType() == SceneType.FakePetFb
                     or scene:GetSceneType() == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
                    if scene_logic:CanGetMoveObj() then
                        local scan_target_x, scan_target_y = self:GetGuiJiMonsterPos()
                        if nil == scan_target_x or nil == scan_target_y then
                            -- Scene.SendGetAllObjMoveInfoReq()
                            self.guai_ji_next_move_time = Status.NowTime + 0.1
                        else
                            target_x = scan_target_x
                            target_y = scan_target_y
                        end
                    end
                end

                if nil ~= target_x and nil ~= target_y then
                    self:SetMoveScan(true)
                    -- MoveCache.SetEndType(MoveEndType.Auto)
                    local role_range = COMMON_CONSTS.GUAJI_MAX_RANGE
                    if scene:GetSceneType() == SceneType.PET_FB or scene:GetSceneType() == SceneType.FakePetFb then
                        role_range = 0
                    end

                    local guaji_dir_x, guaji_dir_y = scene_logic:GetGuaJiDir()
                    if guaji_dir_x ~= nil and guaji_dir_y ~= nil then
                        self:SetMoveToPosCallBack(function()
                            local check_main_role = Scene.Instance:GetMainRole()
                            if check_main_role ~= nil and not check_main_role:IsDeleted() then
                                check_main_role:SetDirectionByXY(guaji_dir_x, guaji_dir_y)
                            end
                        end)
                    end

                    local is_ignore_auto_fight = false
                    self:MoveToPos(scene:GetSceneId(), target_x, target_y, role_range, nil, nil, nil, nil, nil, nil, is_ignore_auto_fight, CLIENT_MOVE_REASON.GUAJI)
                    return
                else
                    if scene:GetSceneType() == SceneType.FakePetFb then
                        scene_logic:TryToGather()
                    end
                end
            end
        end
    elseif GuajiCache.guaji_type == GuajiType.Monster then
        if 0 == GuajiCache.monster_id then
            self:StopGuaji()
            return
        end

        if nil ~= GuajiCache.target_obj and GuajiCache.target_obj:GetType() == SceneObjType.Monster
            and GuajiCache.target_obj == Scene.Instance:GetObj(GuajiCache.target_obj_id)
            and GuajiCache.target_obj:GetMonsterId() == GuajiCache.monster_id
            and Scene.Instance:IsEnemy(GuajiCache.target_obj) then
            target_obj = GuajiCache.target_obj
        else
            target_obj = Scene.Instance:SelectMinDisMonster(GuajiCache.monster_id or GuajiCache.target_obj_id, Scene.Instance:GetSceneLogic():GetGuajiSelectObjDistance())
        end
    end

    self:DoAttackTarget(target_obj)
end

local anger_skill_list = {
    [0] = { [1] = 1004, [3] = 1104, },
    [1] = { [1] = 1204, [3] = 1304, },
}

function GuajiWGCtrl:DoAttackTarget(target_obj)
    if MoveCache.is_valid or Status.NowTime < FightWGCtrl.Instance:NextCanAtkTime() or GuajiCache.guaji_type == GuajiType.None then
        -- Status.NowTime < FightWGCtrl.Instance:NextCanAtkTime(), GuajiCache.guaji_type == GuajiType.None)
        return false
    end

    local main_role = Scene.Instance:GetMainRole()
    if nil == target_obj then
        return false
    end

    -- if not main_role:CanAttack() then
    --     return false
    -- end
    -- 冲锋过程中不发起战斗，也不寻找新怪，否则可能冲过去后又冲回来，
    -- 因为怪是在冲锋过程中寻的（因为GetTargetObj有可能返回nil)
    if main_role:GetIsSpecialMove() then
        return false
    end

    if not Scene.Instance:IsEnemy(target_obj) then
        return false
    end

    local revenge_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
    if revenge_role_id ~= nil and (main_role:IsInSafeArea() or target_obj:IsInSafeArea()) then
        if RevengeWGData.Instance:IsCurRevengeObj(target_obj) then
            return false
        end
    end

    target_obj:OnClick()

    if KuafuPVPWGData.Instance:IsChckEnemyOccupyKFPvP(target_obj) then
        return false
    end

    -- 冲锋逻辑处理
    local is_can_chongfeng, pos_x, pos_y = self:GetIsCanSprint(main_role, target_obj, COMMON_CONSTS.MIN_SKILL_DIS)

    if is_can_chongfeng then
        -- local main_role_x, main_role_y = main_role:GetLogicPos()
        -- local target_x, target_y = target_obj:GetLogicPos()
        main_role:ReqChongfengToObj(pos_x, pos_y, function()
            -- 冲锋完必须同步一次，因为冲锋完释放技能时会检测客户端当前坐标与服务器坐标是否完成一致
            -- 否则会fixpos。
            -- 冲锋设计缺陷，客户端发送目标id。服务器跟客户端各自计算冲锋到的目标点，这里有可能有误差
            -- 导致释放技能时坐标不一致而触发fixpos
            main_role:SendMoveReq()
            main_role:ChangeToCommonState()

            -- 下一帧马上发起攻击，才有击锋造成伤害之感
            -- self.next_scan_target_monster_time = 0
        end)
        return true
    end

    return self:DoAttackTargetUseSkill(main_role, target_obj)
end

function GuajiWGCtrl:DoAttackTargetUseSkill(main_role, target_obj)
    local main_role = Scene.Instance:GetMainRole()
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    local prof = RoleWGData.Instance:GetRoleProf()
    local sex = RoleWGData.Instance:GetRoleSex()

    if self.skill_id then
        --当手动使用指示器释放技能时 传入一个释放目标位置 可以避免技能释放出来时取了挂机目标的位置
        if FightWGCtrl.Instance:TryUseRoleSkill(self.skill_id, target_obj,self.cache_skill_x,self.cache_skill_y) or SkillWGData.Instance:IsInSkillCD(nil, self.skill_id) then
            self.skill_id = nil
            self.cache_skill_x = nil
            self.cache_skill_y = nil
            return true
        else
            return false
        end
    else
        self.use_anger_skill = false
        if self.use_anger_skill then
            local anger_skill = anger_skill_list[sex][prof]
            if FightWGCtrl.Instance:TryUseRoleSkill(anger_skill, target_obj) then
                return true
            end
        end

        -- 挂机中连招不放技能
        -- local is_lianzhaoing = false

        -- if not SkillWGData.IsNotNormalSkill(main_role:GetLastSkillId())
        --     and main_role:GetLastSkillIndex() < 2 then
        --     is_lianzhaoing = true
        -- end
        -- 测试代码---
        self.use_skill = true
        -- 测试代码---
        -- 追逐玩家，用普攻冲刺
        local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
        local appearance_param = main_role_vo.appearance_param or 0
        local is_riding_fight_mount = main_role:IsRidingFightMount()
        local is_gundam = main_role:IsGundam()
        local is_xiuwei_bianshen = main_role:IsXiuWeiBianShen()
        local need_main_skill = true -- not target_obj:IsRole() or (target_obj:IsRole() and not target_obj:IsMove())
        local skill_order, skill_flag
        if self.use_skill and need_main_skill then
            if is_system_bianshen then
                skill_order, skill_flag = TianShenWGData.Instance:GetTianShenSkill()
            elseif is_riding_fight_mount then
                local riding_mount_appeid = main_role:GetCurRidingResId()
                skill_order, skill_flag = NewFightMountWGData.Instance:GetMainUISkillOrder(riding_mount_appeid)
            elseif is_gundam then
                skill_order, skill_flag = MechaWGData.Instance:GetMainUISkillOrder()
            elseif is_xiuwei_bianshen then
                local nuqi_type = math.floor(appearance_param / 1000)
                skill_order, skill_flag = CultivationWGData.Instance:GetMainUISkillOrder(nuqi_type)
            else
                skill_order, skill_flag = RoleWGData.Instance:GetSkillCustomInfo()
            end

            local no_fight_car = not main_role:IsXMZCar()
            if no_fight_car then
                -- 秘笈技能
                if self.auto_esoterica_skill and Status.NowTime >= self.instant_release_skill_next_time then
                    local skill_info_list = CultivationWGData.Instance:GetEsotericaShowSkill()
                    for k,v in pairs(skill_info_list) do
                        if FightWGCtrl.Instance:TryUseRoleSpecialSkill(v.skill_id, target_obj) then
                            self.instant_release_skill_next_time = Status.NowTime + 1
                            local target_obj_id = target_obj and target_obj:GetObjId() or COMMON_CONSTS.INVALID_OBJID
                            local skill_pos_x, skill_pos_y = main_role:GetLogicPos()
                            CultivationWGCtrl.Instance:OnEsotericaOperate(ESOTERICA_OPERATE_TYPE.PERFORM_SKILL, v.slot, target_obj_id, skill_pos_x, skill_pos_y)
                            return true
                        end
                    end
                end

                -- 定制技能
                if self.auto_customized_skill then
                    local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
                    for k,v in pairs(skill_info_list) do
                        --判断是否在cd中
                        local skill_end_time = SkillWGData.Instance:GetSkillCDEndTime(v.skill_index)
                        if skill_end_time <= Status.NowTime * 1000 then -- 不在CD中
                            if SkillWGData.Instance:GetIsCustomizedSkill(v.skill_id) then
                                if FightWGCtrl.Instance:TryUseRoleSkill(v.skill_id, target_obj) then
                                    return true
                                end
                            end
                        end
                    end
                end

                -- 光环技能
                if self.auto_halo_skill then
                    local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
                    for k,v in pairs(skill_info_list) do
                        if SkillWGData.Instance:GetIsHaloSkill(v.skill_id) then
                            local enough = FiveElementsWGData.Instance:GetIsCanDoHaloSkill()
                            if enough and FightWGCtrl.Instance:TryUseRoleSkill(v.skill_id, target_obj) then
                                return true
                            end
                        end
                    end
                end

                -- 五行技能
                if self.auto_wuxing_skill then
                    local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
                    for k,v in pairs(skill_info_list) do
                        if SkillWGData.Instance:GetIsWuXingSkill(v.skill_id) then
                            if FightWGCtrl.Instance:TryUseRoleSkill(v.skill_id, target_obj) then
                                return true
                            end
                        end
                    end
                end

                -- 龙珠技能
                if self.auto_longzhu_skill then
                    local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
                    for k,v in pairs(skill_info_list) do
                        if SkillWGData.Instance:GetIsLongZhuSkill(v.skill_id) then
                            if FightWGCtrl.Instance:TryUseRoleSkill(v.skill_id, target_obj) then
                                return true
                            end
                        end
                    end
                end

                -- 四象技能
                if self.auto_sx_skill then
                    local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
                    for k,v in pairs(skill_info_list) do
                        if SkillWGData.Instance:GetIsSiXiangSkill(v.skill_id) then
                            local cur_nuqi, need_nuqi, enough = FightSoulWGData.Instance:GetSkillNuQiData()
                            if enough and FightWGCtrl.Instance:TryUseRoleSkill(v.skill_id, target_obj) then
                                return true
                            end
                        end
                    end
                end

                -- 天神合击技能
                if self.auto_tianshen_heji_skill then
                    local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
                    for k,v in pairs(skill_info_list) do
                        if SkillWGData.Instance:GetIsTianShenHeJiSkill(v.skill_id) then
                            if FightWGCtrl.Instance:TryUseRoleSkill(v.skill_id, target_obj) then
                                return true
                            end
                        end
                    end
                end
            end

            -- 幻兽技能
            if self.auto_beasts_skill then
                local list = ControlBeastsWGData.Instance:GetSkillInfoList()
                local cool_offset = 1   -- 加一秒的误差
                if list then
                    for i, skill_data in ipairs(list) do
                        if skill_data.bag_id ~= -1 and skill_data.skill_id ~= -1 then
                            if skill_data.battle_index == 0 and (skill_data.cd_finish_time + cool_offset) - TimeWGCtrl.Instance:GetServerTime() <= 0 then --- 冷却了直接放
                                local target_obj_id = target_obj and target_obj:GetObjId() or COMMON_CONSTS.INVALID_OBJID
                                if target_obj_id ~= COMMON_CONSTS.INVALID_OBJID and main_role:IsFightState() then
                                    local target_x, target_y = target_obj:GetLogicPos()
                                    local beast_obj = main_role:FindBeast()
                            
                                    if beast_obj then
                                        beast_obj:TryUseBeastSkill(skill_data.skill_id, target_x, target_y, target_obj_id, true)
                                    else
                                        local main_role_vo = main_role:GetVo()
                                
                                        -- 隐藏了幻兽存在上阵幻兽，直接使用技能
                                        if main_role_vo and main_role_vo.beast_id and main_role_vo.beast_id > 0 then
                                            ControlBeastsWGCtrl.Instance:OpenBeastsSkillShowTips(main_role_vo.beast_id)
                                            ControlBeastsWGCtrl.Instance:SendCSBeastUseSkill(skill_data.skill_id, target_obj_id, target_x, target_y)	
                                        end
                                    end
                                end
                            elseif skill_data.battle_index ~= 0 and     -- 出战位未冷却，查看其他位置是否冷却且可以切换
                                (skill_data.cd_finish_time + cool_offset) - TimeWGCtrl.Instance:GetServerTime() <= 0 and
                                (skill_data.cd_change_time + cool_offset) - TimeWGCtrl.Instance:GetServerTime() <= 0 then
                                    ControlBeastsWGCtrl.Instance:SendCSChangeBeast(skill_data.battle_index)
                                    break
                            end
                        end
                    end
                end
            end

            -- 普通技能
            local skill_id = 0
            for k, v in ipairs(skill_order) do
                for k1,v1 in pairs(skill_flag) do
                    if v == v1 or (is_system_bianshen or is_riding_fight_mount or is_gundam or is_xiuwei_bianshen) then
                        skill_id = tonumber(v)
                        break
                    end
                end

                if skill_id ~= SkillWGData.Skill_Id_291 and
                    SkillWGData.GetSkillBigType(skill_id) ~= SKILL_BIG_TYPE.NORMAL and
                    0 ~= skill_id and
                    FightWGCtrl.Instance:TryUseRoleSkill(skill_id, target_obj) then
                        MainuiWGCtrl.Instance:PlayCommonSkillBaoDianEffect(skill_id)
                    return true
                end
            end
        end

        -- 普攻
        if is_system_bianshen then
            local first_bianshen_normalskill = TianShenWGData.Instance:GetSkillByAttackSeq(1, true)
            local is_can_use_skill = FightWGCtrl.Instance:TryUseRoleSkill(first_bianshen_normalskill, target_obj)
            if is_can_use_skill then
                return true
            end

        elseif is_riding_fight_mount then
            if FightWGCtrl.Instance:TryUseRoleSkill(skill_order[0], target_obj) then
                return true
            end
        elseif is_gundam then
            if FightWGCtrl.Instance:TryUseRoleSkill(skill_order[0], target_obj) then
                return true
            end
        elseif is_xiuwei_bianshen then
            if FightWGCtrl.Instance:TryUseRoleSkill(skill_order[0], target_obj) then
                return true
            end
        elseif main_role:IsXMZCar() then
        else
            if FightWGCtrl.Instance:TryUseRoleSkill(use_prof_normal_skill_list[sex][prof][1], target_obj) then
                return true
            end
        end
    end
    --TestTrackPrint("Fight", "无意义返回")
    return false
end

--获取是否能对目标进行冲锋
function GuajiWGCtrl:GetIsCanSprint(main_role, target_obj, min_skill_dis)
    return false
end

-- 停止挂机
--is_thorough彻底停止挂机
function GuajiWGCtrl:StopGuaji(is_moveing, is_thorough, is_reset, is_reset_revenge)
    if CLIENT_DEBUG_LOG_STATE then
        print_error("StopGuaji", is_moveing, is_thorough, is_reset, is_reset_revenge)
    end

    is_moveing = is_moveing ~= false
    self:SetMoveScan(false)
    MoveCache.task_id = 0
    MoveCache.task_type = -1
    -- unity3d项目暂时屏蔽
    -- TaskWGData.Instance:SetCurTaskId(0)
    -- self:ClearGuajiCache()
    self:ClearTaskOperate()
    if is_thorough then
        self:ClearTemporary()
    end

    if (is_thorough or not TaskGuide.Instance:NoviceCheckTask()) and not is_reset then
        self:SetGuajiType(GuajiType.None)
    end
    Scene.Instance:GetMainRole():StopMove(is_moveing)

    if not is_reset_revenge then
        MainuiWGCtrl.Instance:StopAutoRevenge()
    end
end

function GuajiWGCtrl:ClearGuajiCache(is_reset)
    if is_reset then
        GuajiCache.guaji_type = GuajiType.Auto
    else
        self:SetGuajiType(GuajiType.None)
    end


    GuajiCache.target_obj = nil
    GuajiCache.target_obj_id = COMMON_CONSTS.INVALID_OBJID
    GuajiCache.is_click_select = false
    GuajiCache.monster_id = 0
    GuajiCache.event_guaji_type = GuajiType.None
end

function GuajiWGCtrl:UpdateTemporary(now_time, elapse_time)
    if self.old_guaji_type == GuajiType.Temporary then return end
    if 0 == self.temporary_time then return end
    if now_time < self.temporary_time then return end
    self.temporary_time = 0

    -- 使用技能指示器的时候，要一直处于临时手动
    if MainuiWGCtrl.Instance:GetIsShowRang() then
        self.temporary_time = self.temporary_total_time + Status.NowTime
        return
    end

    self:TemporaryOverAndGuaJiReduction()
end

-- 临时手动结束，恢复旧挂机状态
function GuajiWGCtrl:TemporaryOverAndGuaJiReduction()
    -- 临时挂机恢复时，如果之前是固定打某个ID的怪，并且当前选中的目标是无效的, 那么如果附近找不到这个怪，就变成自动挂机
    if self.old_guaji_type ~= nil and self.old_guaji_type == GuajiType.Monster then
        local scene_logic = Scene.Instance:GetSceneLogic()
        if GuajiCache.monster_id ~= nil and GuajiCache.monster_id ~= 0 and scene_logic ~= nil then
            if GuajiCache.target_obj == nil or GuajiCache.target_obj:IsDeleted() then
                local target_obj = Scene.Instance:SelectMinDisMonster(GuajiCache.monster_id, scene_logic:GetGuajiSelectObjDistance())
                if target_obj == nil then
                    self.old_guaji_type = GuajiType.Auto
                end
            end
        end
    end
    self:SetGuajiType(self.old_guaji_type)
end

function GuajiWGCtrl:ClearTemporaryAndGuaJiReduction()
    self.temporary_time = 0
    self:TemporaryOverAndGuaJiReduction()
end

function GuajiWGCtrl:ClearTemporary()
    self.old_guaji_type = GuajiType.None
    self.temporary_time = 0
end

-- 临时状态
function GuajiWGCtrl:SetTemporary()
    self:SetGuajiType(GuajiType.Temporary)
end

function GuajiWGCtrl:TrySetTemporary()
    if GuajiCache.guaji_type ~= GuajiType.None and GuajiCache.guaji_type ~= GuajiType.Temporary then
        self.old_guaji_type = GuajiCache.guaji_type
        self:StopGuaji()
        if self.old_guaji_type ~= GuajiType.None and self.old_guaji_type ~= GuajiType.Temporary then
            self.temporary_time = self.temporary_total_time + Status.NowTime
            self:SetTemporary()
        end
        self.skill_id = nil
    elseif GuajiCache.guaji_type == GuajiType.Temporary then
        self:ResetPickCache()
        self.temporary_time = self.temporary_total_time + Status.NowTime
    end
end

-- 挂机时的移动处理
function GuajiWGCtrl:DoMoveByClick(x, y, is_comefrom_joystick)
    self:ClearBossDeadTime()
    local scene_logic = Scene.Instance:GetSceneLogic()
    if scene_logic ~= nil then
        scene_logic:ClearGuaJiInfo()
    end

    if GuajiCache.guaji_type == GuajiType.Temporary then
        self:ResetPickCache()
        self.temporary_time = self.temporary_total_time + Status.NowTime
        return
    end

    self.old_guaji_type = GuajiCache.guaji_type

    if(GuajiCache.guaji_type == GuajiType.Auto) then
        self:StopGuaji()
        -- Scene.Instance:GetMainRole():ChangeToCommonState()
        self:MoveToPos(self.last_scene_id, x, y, 3, nil, nil, nil, nil, nil, nil, nil, is_comefrom_joystick)
    else
        self:StopGuaji()
        Scene.Instance:ClearAllOperate()
    end

    if self.old_guaji_type ~= GuajiType.None and self.old_guaji_type ~= GuajiType.Temporary then
        self.temporary_time = self.temporary_total_time + Status.NowTime
        self:SetTemporary()
    end
    self.skill_id = nil

    -- unity3d项目暂时屏蔽
    -- if SkyMoneyAutoTaskEvent.CancelHightLightFunc then
    -- SkyMoneyAutoTaskEvent.CancelHightLightFunc()
    -- SkyMoneyAutoTaskEvent.CancelHightLightFunc = nil
    -- end

    -- if DaFuHaoAutoGatherEvent.func then
    -- DaFuHaoAutoGatherEvent.func()
    -- end
    -- if ShengDiFuBenAutoGatherEvent.func then
    -- ShengDiFuBenAutoGatherEvent.func()
    -- end

    if Scene.Instance:GetSceneType() == SceneType.ZhuXie or Scene.Instance:GetSceneType() == SceneType.KFZhuXieZhanChang then
        local scene_logic = Scene.Instance:GetSceneLogic()
        if scene_logic ~= nil then
            local is_atuo = scene_logic:GetIsAutoTask()
            if is_atuo and GuajiCache.guaji_type == GuajiType.None then
                self:SetGuajiType(GuajiType.Auto)
            else
                scene_logic:SetIsAutoTask(false)
            end
        end
    end
end

-- 挂机时，主动选择技能，先标记一下，待上一次技能完结就释放这个
--skill_x,skill_y 当手动使用指示器释放技能时 传入一个释放目标位置 可以避免技能释放出来时 取了挂机目标的位置
function GuajiWGCtrl:SelectSkillToFight(skill_id, skill_x, skill_y)
    if not SkillWGData.Instance:IsInSkillCDEx(nil, self.skill_id) then
        self.skill_id = skill_id
        self.cache_skill_x = skill_x
        self.cache_skill_y = skill_y
    end
end

-- 挂机时的技能处理
function GuajiWGCtrl:DoFightByClick(skill_id, target_obj)
    self:PlayerOperation()
    self.skill_id = skill_id
    -- -- if GuajiCache.guaji_type == GuajiType.None or GuajiCache.guaji_type == GuajiType.Temporary then
    --     FightWGCtrl.Instance:TryUseRoleSkill(skill_id, target_obj)
    -- -- end
    -- -- self:StopGuaji()
    if FightWGCtrl.Instance:TryUseRoleSkill(skill_id, target_obj) then
        self.skill_id = nil
    end
end

-- 找不到通往目标的路径时
local CanNotFindWayTimer = nil
function GuajiWGCtrl:OnCanNotFindWay()
    print_warning("OnCanNotFindWay")
    local main_role = Scene.Instance:GetMainRole()
    if main_role and main_role:IsQingGong() or CanNotFindWayTimer or main_role:IsMitsurugi() then
        return
    end

    if GuajiCache.guaji_type ~= GuajiType.None then
        if GuajiCache.guaji_type == GuajiType.Auto then
            GuajiCache.target_obj = nil
            local guaji_type = GuajiCache.guaji_type
            self:StopGuaji()
            CanNotFindWayTimer = GlobalTimerQuest:AddDelayTimer(function()
                CanNotFindWayTimer = nil
                self:SetGuajiType(guaji_type)
                end, 0.1)
        elseif MoveCache.task_id > 0 then
            CanNotFindWayTimer = GlobalTimerQuest:AddDelayTimer(function()
                CanNotFindWayTimer = nil
                TaskWGCtrl.Instance:DoTask(MoveCache.task_id)
                end, 0.1)
        else
            self:StopGuaji()
        end
    end
end

function GuajiWGCtrl:PlayerOperation()

end

function GuajiWGCtrl:PlayerPosChange(x, y)
    if self.is_fly then
        if self.fly_cache then
            local scene_id = Scene.Instance:GetSceneId() or 0
            if self.fly_cache.x == x and self.fly_cache.y == y and self.fly_cache.scene_id == scene_id then
                self.is_fly = false
                self:DelayArrive()
            end
        end
    end
end

---[[
function GuajiWGCtrl:GetMoveToNpcCfg(npc_cfg_id, task_id, scene_id)
    local scene_npc_cfg = nil
    if task_id then
        local config = TaskWGData.Instance:GetTaskConfig(task_id)
        if config then
            if config.accept_npc and config.accept_npc ~= "" then
                if npc_cfg_id == config.accept_npc.id then
                    scene_npc_cfg = config.accept_npc
                    scene_id = config.accept_npc.scene
                end
            end
            if not scene_npc_cfg and config.commit_npc and config.commit_npc ~= "" then
                if npc_cfg_id == config.commit_npc.id then
                    scene_npc_cfg = config.commit_npc
                    scene_id = config.commit_npc.scene
                end
            end
        end
    end

    if not scene_npc_cfg and scene_id then
        local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
        if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
            for i, j in pairs(scene_cfg.npcs) do
                if j.id == npc_cfg_id then
                    scene_npc_cfg = j
                    break
                end
            end
        end
    end

    --[[
    if not scene_npc_cfg then
        for k, v in pairs(Config_scenelist) do
            if v.sceneType == SceneType.Common and v.id ~= 1 then
                local scene_cfg = ConfigManager.Instance:GetSceneConfig(v.id)
                if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
                    for i, j in pairs(scene_cfg.npcs) do
                        if j.id == npc_cfg_id then
                            scene_npc_cfg = j
                            scene_id = v.id
                            break
                        end
                    end
                end
                if scene_npc_cfg ~= nil then
                    print_error("not find scene npc id :"..npc_cfg_id)
                    break
                end
            end
        end
    end
    --]]

    return scene_npc_cfg
end

function GuajiWGCtrl:ForceMoveToNpc(npc_cfg_id, task_id, scene_id, ignore_vip, move_to_pos_call_back)
    local scene_npc_cfg = self:GetMoveToNpcCfg(npc_cfg_id, task_id, scene_id)
    if not scene_npc_cfg then
        return
    end

    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

    local x = scene_npc_cfg.x
    local y = scene_npc_cfg.y
    local range = 1

    MoveCache.SetEndType(MoveEndType.NpcTask)
    MoveCache.param1 = npc_cfg_id
    GuajiCache.target_obj_id = npc_cfg_id
    MoveCache.target_obj = Scene.Instance:GetNpcByNpcId(npc_cfg_id) or nil
    self.force_param = {}
    self.force_param.force_move_type = 2
    self.force_param.npc_id = npc_cfg_id
    self.force_param.task_id = task_id
    self.force_param.scene_id = scene_id
    self.force_param.ignore_vip = ignore_vip
    self.force_param.move_to_pos_call_back = move_to_pos_call_back
    self:ForceMoveToPos(scene_id, x, y, range, ignore_vip, move_to_pos_call_back)
end

function GuajiWGCtrl:ForceMoveToPos(scene_id, x, y, range, ignore_vip, force_move_to_pos_call_back)
    range = range or 1

    GuajiWGCtrl.SetMoveValid(true)
    MoveCache.move_type = MoveType.Pos
    MoveCache.scene_id = scene_id
    MoveCache.x = x
    MoveCache.y = y
    MoveCache.range = range
    MoveCache.is_auto_move = true

    if GuajiWGCtrl.CheckRange(x, y, range) then
        self.move_target = nil
        self:ForceArrive()
        return
    end

    self.force_move_to_pos_call_back = force_move_to_pos_call_back or self.force_move_to_pos_call_back


    if IsEmptyTable(self.force_param) then
        self.force_param = {}
        self.force_param.force_move_type = 1
        self.force_param.scene_id = scene_id
        self.force_param.x = x
        self.force_param.y = y
        self.force_param.ignore_vip = ignore_vip
        self.force_param.move_to_pos_call_back = self.force_move_to_pos_call_back
    end

    if tonumber(scene_id) ~= tonumber(Scene.Instance:GetSceneId()) then
        local scene_config = ConfigManager.Instance:GetSceneConfig(scene_id)
        local is_vip = VipWGData.Instance:IsVip()
        local pos_x = 0
        local pos_y = 0
        if ignore_vip or not is_vip then
            pos_x = scene_config.scenex
            pos_y = scene_config.sceney
        else
            pos_x = x
            pos_y = y
        end
        TaskWGCtrl.Instance:JumpFly(scene_id, pos_x, pos_y, nil)

        self:OpenForceMove()
    else
        self:MoveToPos(scene_id, x, y, range, ignore_vip)
    end
end

function GuajiWGCtrl:OnCommonSceneEnter()
    if self.is_force and self.force_param then
        local param = self.force_param
        self:ForceMoveForParam(param)
    end

    self.is_force = false
    self.force_param = nil
end

function GuajiWGCtrl:ForceMoveForParam(param)
    local move_type = param.force_move_type
    if move_type == 2 then
        self:ForceMoveToNpc(param.npc_id, param.task_id, param.scene_id, param.ignore_vip, param.move_to_pos_call_back)
    else
        self:ForceMoveToPos(param.scene_id, param.x, param.y, param.ignore_vip, param.move_to_pos_call_back)
    end
end

function GuajiWGCtrl:ForceArrive()
    local call_back = self.force_move_to_pos_call_back
    self.force_move_to_pos_call_back = nil
    if call_back and self.is_force then
        call_back()
    end

    self.is_force = false
    self.force_param = nil
end

function GuajiWGCtrl:ClearForceParam()
    self.force_param = nil
    self.is_force = nil
    self.force_move_to_pos_call_back = nil
end

function GuajiWGCtrl:OpenForceMove()
    self.is_force = true
end
--]]

-- 通过npc_id移动到npc，如果是任务npc请同时传任务id，不知道任务id可以传场景id 提高效率
function GuajiWGCtrl:MoveToNpc(npc_cfg_id, task_id, scene_id, ignore_vip, scene_key, is_auto_move)
    local scene_npc_cfg = nil
    if task_id then
        local config = TaskWGData.Instance:GetTaskConfig(task_id)
        if config then
            if config.accept_npc and config.accept_npc ~= "" then
                if npc_cfg_id == config.accept_npc.id then
                    scene_npc_cfg = config.accept_npc
                    scene_id = config.accept_npc.scene
                end
            end
            if not scene_npc_cfg and config.commit_npc and config.commit_npc ~= "" then
                if npc_cfg_id == config.commit_npc.id then
                    scene_npc_cfg = config.commit_npc
                    scene_id = config.commit_npc.scene
                end
            end
        end
    end

    if not scene_npc_cfg and scene_id then
        local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
        if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
            for i, j in pairs(scene_cfg.npcs) do
                if j.id == npc_cfg_id then
                    scene_npc_cfg = j
                    break
                end
            end
        end
    end

    if not scene_npc_cfg then
        local scene_cfg = nil
        for k, v in pairs(Config_scenelist) do
            if v.sceneType == SceneType.Common and v.id ~= 1 then
                scene_cfg = ConfigManager.Instance:GetSceneConfig(v.id)
                if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
                    for i, j in pairs(scene_cfg.npcs) do
                        if j.id == npc_cfg_id then
                            scene_npc_cfg = j
                            scene_id = v.id
                            break
                        end
                    end
                end
                if scene_npc_cfg ~= nil then
                    print_error("not find scene npc id :"..npc_cfg_id)
                    break
                end
            end
        end
    end

    if scene_npc_cfg ~= nil then
        MoveCache.SetEndType(MoveEndType.NpcTask)
        MoveCache.param1 = npc_cfg_id
        GuajiCache.target_obj_id = npc_cfg_id
        MoveCache.target_obj = Scene.Instance:GetNpcByNpcId(npc_cfg_id) or nil
        local range = TaskWGData.Instance:GetNPCRange(npc_cfg_id)
        self:MoveToPos(scene_id, scene_npc_cfg.x, scene_npc_cfg.y, range, ignore_vip, scene_key, is_auto_move)
    end
end

function GuajiWGCtrl:CheakCanFly(not_error_mind)
    local scene_logic = Scene.Instance:GetSceneLogic()

    -- if scene_logic:GetSceneType() ~= SceneType.Common then
    --     -- SysMsgWGCtrl.Instance:ErrorRemind(Language.Map.TransmitLimitTip)
    --     return false
    -- end
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    local main_role = Scene.Instance:GetMainRole()

    -- 等级不够
    if main_role_vo.level < FLY_TO_POS_LEVEL_LIMIT then
        return false, Language.Common.HaveNotEnterLevel
    end

    if 0 < main_role:GetTaskToHeighMount() then
        return false, Language.Task.task_mount02
    end

    if 0 < main_role:GetTaskToBianShen() then
        return false, Language.Task.task_bianshen02
    end

    if main_role_vo.husong_color > 0 and main_role_vo.husong_taskid > 0 then -- 护送任务不能传送
        return false
    end

    if main_role:IsJump() and main_role:IsQingGong() or main_role:IsMitsurugi() then
    -- if main_role_vo.move_mode == MOVE_MODE.MOVE_MODE_JUMP2 then -- 跳跃状态不能传送
        return false
    end

    if Scene.Instance:GetMainRole():IsFightStateByRole() then
        return false, Language.Fight.FightDesc01
    end

    if self:CheckIsBootybaySce(Scene.Instance:GetSceneId()) then
        return false
    end

    if Scene.Instance:GetMainRole().hold_beauty_res_id > 0 then
        return false
    end
    --暂时屏蔽直接跳转
    return true
end

function GuajiWGCtrl:SettingChange(setting_type, switch)
    if setting_type == SETTING_TYPE.AUTO_RELEASE_SKILL then
        self.use_skill = switch
    elseif setting_type == SETTING_TYPE.AUTO_RELEASE_ANGER then
        self.use_anger_skill = switch
        -- elseif setting_type == SETTING_TYPE.AUTO_RELEASE_GODDESS_SKILL then
        -- self.use_goddess_skill = switch
    elseif setting_type == SETTING_TYPE.AUTO_SIXIANG_SKILL then
        self.auto_sx_skill = switch
    elseif setting_type == SETTING_TYPE.AUTO_LONGZHU_SKILL then
        self.auto_longzhu_skill = switch
    elseif setting_type == SETTING_TYPE.AUTO_WUXING_SKILL then
        self.auto_wuxing_skill = switch
    elseif setting_type == SETTING_TYPE.AUTO_HALO_SKILL then
        self.auto_halo_skill = switch
    elseif setting_type ==  SETTING_TYPE.AUTO_TIANSHEN_HEJI_SKILL then
        self.auto_tianshen_heji_skill = switch
    elseif setting_type ==  SETTING_TYPE.AUTO_CUSTOMIZED_SKILL then
        self.auto_customized_skill = switch
    elseif setting_type ==  SETTING_TYPE.AUTO_ESOTERICA_SKILL then
        self.auto_esoterica_skill = switch
    elseif setting_type == SETTING_TYPE.AUTO_BEASTS_SKILL then
        self.auto_beasts_skill = switch
    end
end

function GuajiWGCtrl:GetWayLineDistance(path_pos_list)
    if not path_pos_list then
        return 0
    end
    local distance = 0
    local length = #path_pos_list
    local x, y = Scene.Instance:GetMainRole():GetLogicPos()
    if length > 1 then
        for i = 2, length do
            local delta_pos = u3d.vec2(path_pos_list[i - 1].x - path_pos_list[i].x, path_pos_list[i - 1].y - path_pos_list[i].y)
            distance = distance + u3d.v2Length(delta_pos)
        end
        local delta_pos = u3d.vec2(path_pos_list[1].x - x, path_pos_list[1].y - y)
        distance = distance + u3d.v2Length(delta_pos)
    elseif length == 1 then
        local delta_pos = u3d.vec2(path_pos_list[1].x - x, path_pos_list[1].y - y)
        distance = distance + u3d.v2Length(delta_pos)
    end
    return distance
end

function GuajiWGCtrl:OnStartGather()
    self.is_gather = true

    local scene_logic = Scene.Instance:GetSceneLogic()
    if scene_logic then
        scene_logic:SetIsSendGather(false)
    end
end

function GuajiWGCtrl:OnStopGather(stop_reason)
    self.is_gather = false

    local scene_logic = Scene.Instance:GetSceneLogic()
    if scene_logic then
        scene_logic:SetIsSendGather(false)
        scene_logic:StopGatherCallBack(stop_reason)
    end

    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.ZhuXie or scene_type == SceneType.KFZhuXieZhanChang then
        if scene_logic ~= nil then
            local is_atuo = scene_logic:GetIsAutoTask()
            if is_atuo and GuajiCache.guaji_type == GuajiType.None then
                self:SetGuajiType(GuajiType.Auto)
            else
                scene_logic:SetIsAutoTask(false)
            end
        end
    end
end

function GuajiWGCtrl:OnViewClose()
    if ViewManager.Instance:HasOpenView() then
        return
    end
    if self.cacha_open_npc then
        self:OnOperateClickNpc()
    elseif self.cacha_npc_id then
        ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = self.cacha_npc_id})
        self.cacha_npc_id = nil
    end
end

function GuajiWGCtrl:SetCacheNpc(cacha_npc_id) --无npc任务对话缓存
   self.cacha_npc_id = cacha_npc_id
end

function GuajiWGCtrl:ExitFuBen()
    local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if scene_cfg.fight_cant_exit and 1 == scene_cfg.fight_cant_exit then
        local main_role = Scene.Instance:GetMainRole()
        if main_role:IsFightStateByRole() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.FightingCantExitFb)
            return
        end
    end
    local scene_type = Scene.Instance:GetSceneType()
    if scene_type == SceneType.GongChengZhan or
        scene_type == SceneType.HunYanFb or
        scene_type == SceneType.TombExplore or
        scene_type == SceneType.ClashTerritory or
        scene_type == SceneType.QunXianLuanDou or
        scene_type == SceneType.Kf_XiuLuoTower or
        scene_type == SceneType.ZhongKui or
        scene_type == SceneType.TianJiangCaiBao or
        scene_type == SceneType.Question or
        scene_type == SceneType.QingYuanFB or
        scene_type == SceneType.LingyuFb then
        return
    end
    if scene_type == SceneType.CrossFB then
        local str = Language.KuaFuFuBen.Exit
        return
    end
    local scene_id = Scene.Instance:GetSceneId()
    if BossWGData.IsWorldBossScene(scene_id) then
        GuajiWGCtrl.Instance:StopGuaji()
        local scene_logic = Scene.Instance:GetSceneLogic()
        local x, y = scene_logic:GetTargetScenePos(scene_id)
        if x == nil or y == nil then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CannotToTarget)
            return
        end
        GuajiWGCtrl.Instance:MoveToPos(scene_id, x, y, 0)
        return
    end

    local fb_scene_info = FuBenWGData.Instance:GetFBSceneLogicInfo()
    if not IsEmptyTable(fb_scene_info) then
        local diff_time = fb_scene_info.time_out_stamp - TimeWGCtrl.Instance:GetServerTime()
        if diff_time >= 0 and fb_scene_info.is_pass == 0 then
            FuBenWGCtrl.Instance:SendExitFBReq()
            return
        end
    end
end

function GuajiWGCtrl:SetArriveCallBack(call_back)
    self.arrive_call_back = call_back
end

function GuajiWGCtrl:SetMoveToPosCallBack(call_back)
    self.move_to_pos_call_back = call_back
end

function GuajiWGCtrl:GetMoveToPosCallBack()
    return self.move_to_pos_call_back
end

function GuajiWGCtrl:CheckIsBootybaySce(scene_id)
    local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
    if bootybay_scene_id == scene_id then
        return true
    end
    return false
end

function GuajiWGCtrl:SetGuajiRange(range)
    self.guaji_range = range
end
function GuajiWGCtrl:GetGuajiRange()
    return self.guaji_range or 640
end

function GuajiWGCtrl:GetCurSelectTargetObj()
    return self.select_obj
end

-- 清除旧的挂机信息，在发起其他关于战斗移动之类的操作时，最好清理一下，避免旧的数据对逻辑有影响
-- is_thorough = true, is_moveing = false, is_clear_temporary = true, 停止挂机清除缓存数据并停止移动
function GuajiWGCtrl:ClearCurGuaJiInfo(is_thorough, is_moveing, is_clear_temporary)
    if is_clear_temporary then
        self:ClearTemporary()
    end

    self:CancelSelect()
    self:StopGuaji(is_moveing, is_thorough)
    self:ClearGuajiCache()
    self:ClearAtkCache()
    self:ClearAllOperate()
    self:ResetMoveCache()
end

-- 挂机恢复
function GuajiWGCtrl:ResetGuaJi()
    if GuajiCache.guaji_type == GuajiType.None or GuajiCache.guaji_type == GuajiType.Temporary then
        return true, false
    end

    -- 特殊状态重置检测时间
    if CgManager.Instance:IsCgIng() or FunctionGuide.Instance:GetIsGuide() or Scene.Instance:IsSceneLoading() then
        return true, false
    end

    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil then
        return true, false
    end

    -- NPC对话界面重置检测时间
    if ViewManager.Instance:IsOpen(GuideModuleName.TaskDialog) then
        return true, false
    end

    -- 特殊状态重置检测时间
    if main_role:IsDead() or main_role:GetIsGatherState() or main_role:IsAtkPlaying() or main_role:IsFollowState() or main_role:IsQingGong() or main_role:IsMitsurugi() then
        return true, false
    end

    local scene_logic = Scene.Instance:GetSceneLogic()
    if scene_logic == nil then
        return true, false
    end

    if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() then
        -- 处于挂机状态中，手动选中非敌人目标，不打怪是正常的
        if GuajiCache.is_click_select and not scene_logic:IsEnemy(GuajiCache.target_obj) then
            return true, false
        -- 处于挂机状态中，非手动选择的目标，并且目标不是可攻击对象，取消选中
        elseif not GuajiCache.is_click_select and not scene_logic:IsEnemy(GuajiCache.target_obj) then
            self:CancelSelect()
            return true, false
        elseif self:CheckRenvenge(GuajiCache.target_obj) then
            return true, false
        end
    else
        if self:CheckRenvengeIsVaild() then
            return true, false
        end
    end

    -- 挂机打指定怪物，会存在一直在原地等指定怪物刷新，这种情况不能恢复
    if GuajiCache.guaji_type == GuajiType.Monster and GuajiCache.monster_id ~= nil and GuajiCache.monster_id ~= 0 then
        return true, false
    end

    -- 挂机跟随，跟谁目标存在，不恢复
    if GuajiCache.guaji_type == GuajiType.Follow and GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() then
        return true, false
    end

    local check_time = main_role:GetGuaJiCheckMoveTime()
    if check_time == 0 then
        return true, false
    end

    if Status.NowTime - check_time < RESET_GUAJI_TIME then
        return false, false
    end

    self:CancelSelect()
    self:ClearGuajiCache(true)
    self:ClearAtkCache()
    self:ResetMoveCache()
    self:ResetPickCache()
    self:StopGuaji(false, nil, true)

    return true, true
end

-- 野外自动挂机
function GuajiWGCtrl:GetCurAutoGuaJiCfg()
    local is_can = false
    local time = 0
    if self.auto_scene_cfg == nil then
        return is_can, time
    end

    local scene_id = Scene.Instance:GetSceneId() or 0
    local cfg = self.auto_scene_cfg[scene_id]
    if cfg ~= nil then
        is_can = true
        if cfg.time ~= "" then
            time = cfg.time
        end
    end

    return is_can, time
end

function GuajiWGCtrl:GetAutoGuajiScene()
    return self.auto_scene_cfg
end

-- 击杀BOSS停留等拾取
function GuajiWGCtrl:ClearBossDeadTime()
    self.boss_deal_stop_time = 0
end

function GuajiWGCtrl:SetBossDeadTime()
    local main_role = Scene.Instance:GetMainRole()
    if main_role ~= nil then
        main_role:StopMove()
    end

    self.boss_deal_stop_time = Status.NowTime + 2
end

function GuajiWGCtrl:IsInBossDeadTime()
    local is_in = false
    if self.boss_deal_stop_time ~= nil then
        is_in = self.boss_deal_stop_time >= Status.NowTime
    end

    return is_in
end

-- 反杀
function GuajiWGCtrl:CheckRenvenge(target_obj, select_type)
    local is_in_revenge = false

    if target_obj == nil or target_obj:IsDeleted() then
        return is_in_revenge
    end

    if select_type ~= nil and SceneWGData:TargetSelectIsScene(select_type) then
        return is_in_revenge
    end

    local revenge_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
    if revenge_role_id ~= nil then
        if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() then
            if GuajiCache.target_obj:IsRole() and not GuajiCache.target_obj:IsMainRole() then
                local target_role_id = GuajiCache.target_obj:GetRoleId()
                if target_role_id == revenge_role_id then
                    is_in_revenge = true
                end
            end
        end
    end

    return is_in_revenge
end

function GuajiWGCtrl:CheckRenvengeIsVaild()
    return RevengeWGData.Instance:GetRevengeIsVaild()
end

function GuajiWGCtrl:ChcekIsCurMovePos(scene_id, x, y)
    local is_pos = false
    if MoveCache.is_valid and scene_id ~= nil and scene_id == MoveCache.scene_id and x ~= nil and x == MoveCache.x and y ~= nil and y == MoveCache.y then
        is_pos = true
    end

    return is_pos
end

function GuajiWGCtrl:SetIsAutoGuaJi(is_auto_guaji)
    IS_AUTO_GUAJI = is_auto_guaji
end

function GuajiWGCtrl:TryGoToSit()
    if not self.sit_is_open then
        return
    end

    -- 如果当前在自动做任务，检查状态是否有效
    if TaskGuide.Instance:CanAutoAllTask() then
        local cur_task_type = TaskGuide.Instance:CurrTaskType()
        if cur_task_type ~= nil then
            -- 主线，日常，仙盟任务要检查是否卡等级，其他的不需要检查
            if cur_task_type == GameEnum.TASK_TYPE_ZHU
                or cur_task_type == GameEnum.TASK_TYPE_RI
                or cur_task_type == GameEnum.TASK_TYPE_MENG then

                local task_list = TaskWGData.Instance:GetTaskListIdByType(cur_task_type)
                local vo = GameVoManager.Instance:GetMainRoleVo()
                if task_list ~= nil and #task_list > 0 and vo ~= nil then
                    local task_id = task_list[1]
                    local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)
                    if task_cfg ~= nil and task_cfg.min_level > vo.level then
                        TaskGuide.Instance:CanAutoAllTask(false)
                    end
                else
                    TaskGuide.Instance:CanAutoAllTask(false)
                end
            else
                TaskGuide.Instance:CanAutoAllTask(false)
            end
        end
    end

    if TaskGuide.Instance:CanAutoAllTask() then
        return
    end

    self:ClearCurGuaJiInfo()

    local sit_info = OfflineRestWGData.Instance:GetSitPosInfo()

    local function sit_call()
        if sit_info ~= nil and sit_info.is_need_move then
            local real_info = OfflineRestWGData.Instance:GetSitPosInfo(sit_info.index)
            if sit_info ~= nil and real_info ~= nil then
                self:SetMoveToPosCallBack(function()
                    local m_main_role = Scene.Instance:GetMainRole()
                    if m_main_role ~= nil and not m_main_role:IsDeleted() then
                        m_main_role:TrySit()
                    end
                 end)
                self:MoveToPos(real_info.scene_id, real_info.x, real_info.y, 1, 1)
            end

            return
        end

        local m_main_role = Scene.Instance:GetMainRole()
        if m_main_role ~= nil and not m_main_role:IsDeleted() then
            m_main_role:TrySit()
        end
    end

    if sit_info ~= nil then
        self:FlyToScenePos(sit_info.scene_id, sit_info.x, sit_info.y, false, RoleWGData.Instance:GetAttr("scene_key") or 0, sit_call)
    end
end

function GuajiWGCtrl:GetMinSkillRang()
    local main_role = Scene.Instance:GetMainRole()
    local rang = 0
    local skill_info_list = nil
    if main_role and main_role:IsXMZCar() then
        skill_info_list = SkillWGData.Instance:GetXMZCarSkillCfgList()
    else
        skill_info_list = SkillWGData.Instance:GetCurrSkillList()
    end

    if skill_info_list ~= nil then
        for k, v in pairs(skill_info_list) do
            local cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(v.skill_id, 1)
            if cfg ~= nil and (rang == 0 or rang > cfg.distance) then
                rang = cfg.distance
            end
        end
    end

    return rang or 0
end

function GuajiWGCtrl:SetIsIgnoreLoadOpera(is_ignore)
    self.ignore_scene_load_opera = is_ignore
end

function GuajiWGCtrl:SaveRecoveryFollowCache(uuid, follow_type)
    if uuid == nil or follow_type == nil then
        return
    end

    self.recovery_follow_cache = {uuid = uuid, follow_type = follow_type}
end

function GuajiWGCtrl:GetRecoveryFollowCache()
    return self.recovery_follow_cache
end

function GuajiWGCtrl:ResetRecoveryFollowCache()
    self.recovery_follow_cache = nil
end

function GuajiWGCtrl:CheckIsRecoveryFollowVaild()
    local is_valid = false
    local x = nil
    local y = nil
    if self.recovery_follow_cache == nil then
        return is_valid, x, y
    end

    if self.recovery_follow_cache.uuid == nil or self.recovery_follow_cache.follow_type == nil then
        return is_valid, x, y
    end

    if self.recovery_follow_cache.follow_type == OBJ_FOLLOW_TYPE.TEAM then
        if SocietyWGData.Instance:GetIsInTeam() == 0 then
            return is_valid, x, y
        end
    end

    local obj = Scene.Instance:GetRoleByUUID(self.recovery_follow_cache.uuid)
    if obj ~= nil and not obj:IsDeleted() then
        x, y = obj:GetLogicPos()
        is_valid = true
    end

    return is_valid, x, y
end

function GuajiWGCtrl:TryRecoveryFollowState()
    local recovery_uuid = nil
    local recovery_follow_type = nil
    local recovery_cache = self:GetRecoveryFollowCache()
    if recovery_cache == nil then
        return
    end

    recovery_uuid = recovery_cache.uuid
    recovery_follow_type = recovery_cache.follow_type
    self:ClearCurGuaJiInfo(true, false, true)
    self:ResetRecoveryFollowCache()

    if recovery_uuid ~= nil and recovery_follow_type ~= nil and recovery_follow_type == OBJ_FOLLOW_TYPE.TEAM then
        if MainuiWGCtrl ~= nil and MainuiWGCtrl.Instance ~= nil then
            MainuiWGCtrl.Instance:OnClickFollowBtn()
        end
    end
end

function GuajiWGCtrl:TrySaveRecoveryFollowState(obj)
    if self.recovery_follow_cache ~= nil then
        return
    end

    if GuajiCache.guaji_type ~= GuajiType.None then
        return
    end

    if obj == nil or obj:IsDeleted() or not obj:IsRole() then
        return
    end

    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil or main_role:IsDeleted() then
        return
    end

    if main_role:IsTeamFollowState() then
        local vo = obj:GetVo()
        local follow_uuid = main_role:GetFollowObjUuid()
        if vo.uuid ~= nil and follow_uuid ~= nil and follow_uuid == vo.uuid then
            self:SaveRecoveryFollowCache(vo.uuid, OBJ_FOLLOW_TYPE.TEAM)
            self:ClearCurGuaJiInfo(true, false, true)
            self:SetGuajiType(GuajiType.Auto)
        end
    end
end

function GuajiWGCtrl:ResetRealiveFollowCache()
    self.realive_follow_cache = nil
end

function GuajiWGCtrl:TrySaveFollowInfoByDie()
    self:ResetRealiveFollowCache()
    local recovery_uuid = nil
    local recovery_follow_type = nil
    local recovery_cache = self:GetRecoveryFollowCache()
    if recovery_cache ~= nil then
        recovery_uuid = recovery_cache.uuid
        recovery_follow_type = recovery_cache.follow_type
    end

    if recovery_uuid ~= nil and recovery_follow_type ~= nil and recovery_follow_type == OBJ_FOLLOW_TYPE.TEAM then
        self.realive_follow_cache = {uuid = recovery_uuid, follow_type = OBJ_FOLLOW_TYPE.TEAM}
    end

    if self.realive_follow_cache == nil then
        local main_role = Scene.Instance:GetMainRole()
        if main_role ~= nil and not main_role:IsDeleted() then
            if main_role:IsTeamFollowState() then
                local uuid = main_role:GetFollowObjUuid()
                if uuid ~= nil then
                    self.realive_follow_cache = {uuid = uuid, follow_type = OBJ_FOLLOW_TYPE.TEAM}
                end
            end
        end
    end
end

function GuajiWGCtrl:TryRecoveryFollowByRealive()
    self:ResetRecoveryFollowCache()
    if self.realive_follow_cache == nil then
        return false
    end

    local uuid = self.realive_follow_cache.uuid
    local follow_type = self.realive_follow_cache.follow_type
    self:ClearCurGuaJiInfo(true, false, true)
    self:ResetRealiveFollowCache()

    if uuid == nil or follow_type == nil then
        return false
    end

    if uuid ~= nil and follow_type ~= nil and follow_type == OBJ_FOLLOW_TYPE.TEAM then
        if MainuiWGCtrl ~= nil and MainuiWGCtrl.Instance ~= nil then
            MainuiWGCtrl.Instance:OnClickFollowBtn()
        end
    end

    return true
end

function GuajiWGCtrl:IsAutoEsotericaSkill()
    return self.auto_esoterica_skill
end