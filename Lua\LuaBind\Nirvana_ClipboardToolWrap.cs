﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_ClipboardToolWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("ClipboardTool");
		<PERSON><PERSON>RegFunction("CopyTextToClipboard", CopyTextToClipboard);
		<PERSON><PERSON>unction("GetTextClipboard", GetTextClipboard);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CopyTextToClipboard(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			Nirvana.ClipboardTool.CopyTextToClipboard(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTextClipboard(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = Nirvana.ClipboardTool.GetTextClipboard();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

