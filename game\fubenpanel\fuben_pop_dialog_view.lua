FuBenPopDialogView = FuBenPopDialogView or BaseClass(SafeBaseView)

function FuBenPopDialogView:__init()
	self.view_name = GuideModuleName.FuBenPopDialogView
	self.open_tween = self.OpenTween
	self.close_tween = self.CloseTween
	self.active_close = false
	self:AddViewResource(0, "uis/view/novice_pop_dialog_prefab", "layout_novice_pop_dialog")
end

function FuBenPopDialogView:CloseCallBack()
	self.data = nil
	self.show_name = nil
	self.show_model = nil

	if self.hole_delay_timer then
		GlobalTimerQuest:CancelQuest(self.hole_delay_timer)
		self.hole_delay_timer = nil
	end
end

function FuBenPopDialogView:LoadCallBack()
	self.display = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["display"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		rt_scale_type = ModelRTSCaleType.PS_M,
		can_drag = false,
	}
	
	self.display:SetRenderTexUI3DModel(display_data)
end

function FuBenPopDialogView:ReleaseCallBack()
	if self.display then
		self.display:DeleteMe()
		self.display = nil
	end
end

function FuBenPopDialogView:SetOpenData(var_data, var_name, var_model)
	self.data = var_data
	self.show_name = var_name
	self.show_model = var_model
end

function FuBenPopDialogView:OnFlush()
	if not self.data then
		self:Close()
		return
	end

	self:ShowStep()
end

function FuBenPopDialogView:ShowStep()
	local step_data = self.data
	
	if not self.node_list.txt_content then
		return
	end

	local name = self.show_name or step_data.name
	local res_id = self.show_model or step_data.model
	self.node_list.txt_content.text.text = step_data.word
	self.node_list.txt_name.text.text = name

	local get_fun = ResPath.GetNpcModel
	if self.show_model then
		get_fun = ResPath.GetMonsterModel
	end

	self.display:SetMainAsset(get_fun(res_id))
	
	-- 音频
	local audio_id = step_data.talk_audio
	if audio_id and audio_id ~= "" then
		local bundle, asset = ResPath.GetNpcTalkVoiceResByResName(audio_id)
		TalkCache.PlayGuideTalkAudio(bundle, asset)
	end

	if step_data.center_pos and step_data.center_pos ~= "" then
		local center_pos = Split(step_data.center_pos, '|')
		local pos = self.node_list.content.transform.anchoredPosition
		pos.x = tonumber(center_pos[1]) or 0
		pos.y = tonumber(center_pos[2]) or 0
		self.node_list.content.transform.anchoredPosition = pos
	end
	
	if step_data.display_pos and step_data.display_pos ~= "" then
		local display_pos = Split(step_data.display_pos, '|')
		local pos = self.node_list.display.transform.anchoredPosition
		pos.x = tonumber(display_pos[1]) or 0
		pos.y = tonumber(display_pos[2]) or 0
		self.node_list.display.transform.anchoredPosition = pos
	end

	if step_data.model_pos and step_data.model_pos ~= "" then
		local pos = Split(step_data.model_pos, '|')
		self.display:SetRTAdjustmentRootLocalPosition(pos[1], pos[2], pos[3])
	end

	if step_data.model_euler and step_data.model_euler ~= "" then
		self.display:SetRTAdjustmentRootLocalRotation(0, step_data.model_euler, 0)
	end

	if step_data.model_scale and step_data.model_scale ~= "" then
		self.display:SetRTAdjustmentRootLocalScale(step_data.model_scale)
	end

	self.hole_delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		self:Close()
	end, step_data.hole_time)
end

function FuBenPopDialogView:SetRendering(value)
	SafeBaseView.SetRendering(self, value)
	if value then
		-- 避免动画播到一半，界面被隐藏，导致坐标错误。这里恢复到0
		self.root_node_transform.transform.anchoredPosition = Vector2(0, 0)
	end
end