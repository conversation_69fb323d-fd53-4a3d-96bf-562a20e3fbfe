CapabilityUpGiftView = CapabilityUpGiftView or BaseClass(SafeBaseView)

function CapabilityUpGiftView:__init()
    self.view_style = ViewStyle.Half
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/capability_up_gift_ui_prefab", "layout_capability_up_gift")
end

function CapabilityUpGiftView:ShowIndexCallBack()
    self:FlushTimer()
end

function CapabilityUpGiftView:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.OnClickBuyBtn, self))
end

function CapabilityUpGiftView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("capability_up_gift_timer") then
		CountDownManager.Instance:RemoveCountDown("capability_up_gift_timer")
	end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function CapabilityUpGiftView:OnFlush()
    local gift_cfg = CapabilityUpGiftWGData.Instance:GetCurGiftCfg()
    if not gift_cfg then
        return
    end

    local cur_seq = CapabilityUpGiftWGData.Instance:GetCurSeq()
    self.node_list.buy_price.text.text = RoleWGData.GetPayMoneyStr(gift_cfg.buy_cost, gift_cfg.rmb_type, cur_seq)
    self.node_list.original_price.text.text = RoleWGData.GetPayMoneyStr(gift_cfg.original_price, gift_cfg.rmb_type, cur_seq)
    self.reward_list:SetDataList(gift_cfg.reward_item)
end

function CapabilityUpGiftView:FlushTimer()
    if CountDownManager.Instance:HasCountDown("capability_up_gift_timer") then
		CountDownManager.Instance:RemoveCountDown("capability_up_gift_timer")
	end

    local time = CapabilityUpGiftWGData.Instance:GetCurGiftEndTime() - TimeWGCtrl.Instance:GetServerTime()
    if time > 0 then
        self.node_list["act_time"].text.text = string.format(Language.CapabilityUpGift.ActTime, TimeUtil.FormatSecondDHM2(time))
        CountDownManager.Instance:AddCountDown("capability_up_gift_timer", BindTool.Bind1(self.RefreshCountDown, self), BindTool.Bind1(self.OnComplete, self), nil, time, 1)
    else
        self:OnComplete()
    end
end

function CapabilityUpGiftView:RefreshCountDown(elapse_time, total_time)
    local time = total_time - elapse_time
    if time > 0 and self.node_list and self.node_list.act_time then
        local time_str = TimeUtil.FormatSecondDHM8(time)
        self.node_list["act_time"].text.text = string.format(Language.CapabilityUpGift.ActTime, time_str)
    end
end

function CapabilityUpGiftView:OnComplete()
    self.node_list.act_time.text.text = ""
end

function CapabilityUpGiftView:OnClickBuyBtn()
    local gift_cfg = CapabilityUpGiftWGData.Instance:GetCurGiftCfg()
    if not gift_cfg then
        return
    end

    local cur_seq = CapabilityUpGiftWGData.Instance:GetCurSeq()
    RechargeWGCtrl.Instance:Recharge(gift_cfg.buy_cost, gift_cfg.rmb_type, cur_seq)
end