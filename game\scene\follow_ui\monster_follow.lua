MonsterFollow = MonsterFollow or BaseClass(CharacterFollow)

function MonsterFollow:__init()
	self.shield_obj_type = ShieldObjType.MonsterFollowUI
	self.is_boss = false
end

function MonsterFollow:__delete()
end

function MonsterFollow:SetIsBoss(is_boss)
	self.is_boss = is_boss
end

function MonsterFollow:SetIsTower(is_tower)
	self.is_tower = is_tower
end

function MonsterFollow:Show()
	CharacterFollow.Show(self)
	if Scene.Instance:GetSceneType() == SceneType.DefenseFb and self.is_tower then
		self:ShowName()
		return
	end

	--强制显示血条仙盟守护
	if Scene.Instance:GetSceneType() == SceneType.GuildMiJingFB and self.is_tower then
		self:SetHpVisiable(true)
		return
	end

	if self.is_boss then
		self:SetHpVisiable(false)
		self:ShowName()
		return
	end

	self:SetHpVisiable(true)
end

function MonsterFollow:Hide()
	CharacterFollow.Hide(self)
	self:SetHpVisiable(false)
	if (Scene.Instance:GetSceneType() == SceneType.DefenseFb  and self.is_tower or self.is_boss) or Scene.Instance:GetSceneType() == SceneType.KF_DUCK_RACE then
		self:ShowName()
		return
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic and scene_logic:IsHideBOSSNameState() then
		self:HideName()
	end
end

function MonsterFollow:ShowName()
	self:SetNameVis(true)
end

function MonsterFollow:HideName()
	local is_force_hide = false
	if not is_force_hide then
		if Scene.Instance:GetSceneType() == SceneType.DefenseFb and self.is_tower or self.is_boss then
			return --不允许隐藏名称
		end
	end

	self:SetNameVis(false)
end