--------------------------------------------------
-- 副本挑战提醒
--------------------------------------------------
FuBennoticeView = FuBennoticeView or BaseClass(SafeBaseView)

function FuBennoticeView:__init()
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self.view_name = "FuBennoticeView"
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third2_panel")
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_fb_forenotice2")
	self.show_list = {}
end

function FuBennoticeView:__delete()
end

function FuBennoticeView:ReleaseCallBack()

end

function FuBennoticeView:CloseCallBack()
	FuBenPanelWGCtrl.Instance:DealWithTuiJianCloseType(false)
	self.data = nil
end

function FuBennoticeView:LoadCallBack()
	self.node_list["btn_go_kill"].button:AddClickListener(BindTool.Bind1(self.ClickHandler, self))
end

function FuBennoticeView:ShowIndexCallBack()

end

function FuBennoticeView:OnFlush()
	if self.data then
		self.node_list["img_boss_head"].text.text = self.data.name
		self.node_list["boss_name_txt"].text.text = self.data.desc
		local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id]
		local bundle, asset = nil, nil
		if monster_cfg.small_icon ~= "" then
			bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. monster_cfg.small_icon)
			self.node_list.icon.image:LoadSprite(bundle, asset, function()
				self.node_list.icon.image:SetNativeSize()
			end)
		end
	end
end

function FuBennoticeView:SetFuBenData(data)
	if not data then
		return
	end
	self.data = data
end


function FuBennoticeView:ClickHandler()
	if self.data and self.data.click_call_back then
		self.data.click_call_back()
		self:Close()
	else
		self:Close()
	end
end


function FuBennoticeView:OnClickCloseWindow()
	FuBenPanelWGCtrl.Instance:DealWithTuiJianCloseType(true)
	self:Close()
end