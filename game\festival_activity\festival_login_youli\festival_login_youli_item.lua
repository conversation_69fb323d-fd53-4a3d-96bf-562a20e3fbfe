
FestivalLoginYouLiRewardItem = FestivalLoginYouLiRewardItem or BaseClass(BaseRender)
function FestivalLoginYouLiRewardItem:__init()
	self.num_str = {"一","二","三","四","五","六","七","八","九","十","十一","十二"}
end

function FestivalLoginYouLiRewardItem:__delete()
	if self.is_use_objpool and not self:IsNil() then
		self.select_bg = nil
		self.day_num = nil
		self.btn_item = nil
		self.vip_tip = nil
		ResPoolMgr:Release(self.view.gameObject)
	end
end


function FestivalLoginYouLiRewardItem:SetData(data,is_select)
	BaseRender.SetData(self, data)
	self.select_bg = self.node_list.select_bg
	self.day_num = self.node_list.day_num
	self.btn_item = self.node_list.btn_item
	self.vip_tip = self.node_list.vip_tip

	local day_num = string.format(Language.FestivalLoginGift.LoginStr1, self.num_str[data.index])
	self.day_num.text.text = day_num
	self.node_list["day_num_normal"].text.text = day_num
	local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
	if data.vip_level > 0 then
		self.vip_tip:SetActive(data.common_gift_state == LoginYouLiRewardState.YLQ and cur_vip_level < data.vip_level)
		self.vip_tip.text.text = string.format(Language.FestivalLoginGift.LoginStr2, data.vip_level)
	end

	self.btn_item.button:AddClickListener(BindTool.Bind1(self.OnBtnItemClickHnadler, self))

	if is_select then
		self.select_bg:SetActive(is_select)
		-- self.btn_item.transform.localScale = Vector3(1.2,1.2,1.2)
		FestivalLoginYouLiWGData.Instance:SetSelectDayItem(self.btn_item, self.select_bg)
	end

	local cur_login_day = FestivalLoginYouLiWGData.Instance:GetLoginDyaIndex()
	self.node_list.redpoint:SetActive((data.special_gift_state == LoginYouLiRewardState.KLQ or data.common_gift_state == LoginYouLiRewardState.KLQ))
end

function FestivalLoginYouLiRewardItem:OnBtnItemClickHnadler()
	local old_item, old_select_bg = FestivalLoginYouLiWGData.Instance:GetDyaItemSelect()
	if old_item ~= nil and old_select_bg ~= nil then
		old_select_bg:SetActive(false)
		old_item.transform.localScale = Vector3(1,1,1)
	end

	self.select_bg:SetActive(true)
	-- self.btn_item.transform.localScale = Vector3(1.2,1.2,1.2)
	FestivalLoginYouLiWGData.Instance:SetSelectDayItem(self.btn_item, self.select_bg)

	ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, 
		TabIndex.festival_activity_2262, "RefreshLoginReward", {self.data.index})
end

function FestivalLoginYouLiRewardItem:IsShowLine(is_show)
	self.node_list.day_line:SetActive(is_show)
end

function FestivalLoginYouLiRewardItem:IsGetReward(is_getreward, index)
	if is_getreward then 
		ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, 
			TabIndex.festival_activity_2262, "RefreshLoginReward", {index})
	else	
		-- self.btn_item.transform.localScale = Vector3(1,1,1)
		self.select_bg:SetActive(is_getreward)
	end
end

function FestivalLoginYouLiRewardItem:GetItemState()
	return self.data.special_gift_state
end
