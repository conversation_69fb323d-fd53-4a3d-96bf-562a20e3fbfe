CommonInputView = CommonInputView or BaseClass(SafeBaseView)

function CommonInputView:__init()
	self.view_layer = UiLayer.PopTop
	self:SetMaskBg(true, true)
	self.view_name = "CommonInputView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/dialog_ui_prefab", "layout_input_text")
	self.old_input_text = ""
end

function CommonInputView:__delete()
end

function CommonInputView:ReleaseCallBack()

end

function CommonInputView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
	self.input_text = self.node_list["input_text"]

	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind1(self.OnClickOK, self))
	self.input_text.input_field.onValueChanged:AddListener(BindTool.Bind(self.OnInputFieldValueChange,self))
end

function CommonInputView:OpenCallBack()
	
end

function CommonInputView:SetOkFunc(ok_func)
	self.ok_func = ok_func
end

function CommonInputView:OnClickOK()
	--有非法字符直接不让发
	if ChatFilter.Instance:IsIllegal(self.input_text.input_field.text, false) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
		return
	end
	if self.ok_func then
		self.ok_func(self.input_text.input_field.text)
	end
	self:ClearInputField()
	self:Close()
end

function CommonInputView:SetData(title_text,ok_func,max_text,placeholder_text)
	self.title_text = title_text
	self.ok_func = ok_func
	self.max_text = max_text
	self.placeholder_text = placeholder_text
end

function CommonInputView:OnFlush()
	if self.title_text then
		self:SetViewName(self.title_text)
	end
	if self.placeholder_text then
		self.node_list["placeholder_text"].text.text = self.placeholder_text
	end
	local num = self.max_text or 100
	self.node_list["shengyu_text"].text.text = string.format(Language.Common.ShengYuStr,num)
end

function CommonInputView:ClearInputField()
	if self.input_text and self.input_text.input_field then
		self.input_text.input_field.text = ""
	end
end

function CommonInputView:OnInputFieldValueChange(text)
	local num = StringLen(text)
	if self.max_text and num > self.max_text then
		self.input_text.input_field.text = self.old_input_text
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToLong)
		return
	end
	local max_num = self.max_text and self.max_text or 100
	self.node_list["shengyu_text"].text.text = string.format(Language.Common.ShengYuStr,max_num - num)
	self.old_input_text = text
end
