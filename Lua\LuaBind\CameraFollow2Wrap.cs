﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CameraFollow2Wrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(CameraFollow2), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("SyncFieldOfView", SyncFieldOfView);
		<PERSON><PERSON>unction("DOFieldOfView", DOFieldOfView);
		<PERSON><PERSON>RegFunction("Swipe", Swipe);
		L.RegFunction("Pinch", Pinch);
		<PERSON><PERSON>RegFunction("SyncImmediate", SyncImmediate);
		<PERSON><PERSON>RegFunction("SyncRotation", SyncRotation);
		<PERSON><PERSON>RegFunction("ClampRotationAndDistance", ClampRotationAndDistance);
		L.RegFunction("MoveToTarget", MoveToTarget);
		<PERSON>.RegFunction("Bind", Bind);
		<PERSON>.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("TargetOffset", get_TargetOffset, set_TargetOffset);
		<PERSON><PERSON>("SmoothOffsetSpeed", get_SmoothOffsetSpeed, set_SmoothOffsetSpeed);
		L.RegVar("AllowRotation", get_AllowRotation, set_AllowRotation);
		L.RegVar("AllowXRotation", get_AllowXRotation, set_AllowXRotation);
		L.RegVar("AllowYRotation", get_AllowYRotation, set_AllowYRotation);
		L.RegVar("OriginAngle", get_OriginAngle, set_OriginAngle);
		L.RegVar("RotationSensitivity", get_RotationSensitivity, set_RotationSensitivity);
		L.RegVar("MinPitchAngle", get_MinPitchAngle, set_MinPitchAngle);
		L.RegVar("MaxPitchAngle", get_MaxPitchAngle, set_MaxPitchAngle);
		L.RegVar("MinYawAngle", get_MinYawAngle, set_MinYawAngle);
		L.RegVar("MaxYawAngle", get_MaxYawAngle, set_MaxYawAngle);
		L.RegVar("RotationSmoothing", get_RotationSmoothing, set_RotationSmoothing);
		L.RegVar("AutoSmoothing", get_AutoSmoothing, set_AutoSmoothing);
		L.RegVar("AllowZoom", get_AllowZoom, set_AllowZoom);
		L.RegVar("Distance", get_Distance, set_Distance);
		L.RegVar("MaxDistance", get_MaxDistance, set_MaxDistance);
		L.RegVar("MinDistance", get_MinDistance, set_MinDistance);
		L.RegVar("ZoomSmoothing", get_ZoomSmoothing, set_ZoomSmoothing);
		L.RegVar("Target", get_Target, set_Target);
		L.RegVar("AudioListener", get_AudioListener, set_AudioListener);
		L.RegVar("FieldOfView", get_FieldOfView, set_FieldOfView);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SyncFieldOfView(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow2 obj = (CameraFollow2)ToLua.CheckObject<CameraFollow2>(L, 1);
			obj.SyncFieldOfView();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DOFieldOfView(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CameraFollow2 obj = (CameraFollow2)ToLua.CheckObject<CameraFollow2>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			DG.Tweening.Tweener o = obj.DOFieldOfView(arg0, arg1);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Swipe(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CameraFollow2 obj = (CameraFollow2)ToLua.CheckObject<CameraFollow2>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.Swipe(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Pinch(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraFollow2 obj = (CameraFollow2)ToLua.CheckObject<CameraFollow2>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Pinch(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SyncImmediate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow2 obj = (CameraFollow2)ToLua.CheckObject<CameraFollow2>(L, 1);
			obj.SyncImmediate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SyncRotation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow2 obj = (CameraFollow2)ToLua.CheckObject<CameraFollow2>(L, 1);
			obj.SyncRotation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClampRotationAndDistance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow2 obj = (CameraFollow2)ToLua.CheckObject<CameraFollow2>(L, 1);
			obj.ClampRotationAndDistance();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MoveToTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow2 obj = (CameraFollow2)ToLua.CheckObject<CameraFollow2>(L, 1);
			obj.MoveToTarget();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Bind(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			CameraFollow2 o = CameraFollow2.Bind(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TargetOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.Vector3 ret = obj.TargetOffset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TargetOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SmoothOffsetSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.SmoothOffsetSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SmoothOffsetSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllowRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool ret = obj.AllowRotation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllowXRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool ret = obj.AllowXRotation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowXRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllowYRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool ret = obj.AllowYRotation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowYRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OriginAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.Vector2 ret = obj.OriginAngle;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OriginAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RotationSensitivity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.Vector2 ret = obj.RotationSensitivity;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotationSensitivity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MinPitchAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.MinPitchAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinPitchAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaxPitchAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.MaxPitchAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxPitchAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MinYawAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.MinYawAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinYawAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaxYawAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.MaxYawAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxYawAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RotationSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.RotationSmoothing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotationSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool ret = obj.AutoSmoothing;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllowZoom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool ret = obj.AllowZoom;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowZoom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Distance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.Distance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Distance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaxDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.MaxDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MinDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.MinDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ZoomSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.ZoomSmoothing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ZoomSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Target(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.Transform ret = obj.Target;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Target on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AudioListener(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.AudioListener ret = obj.AudioListener;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AudioListener on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FieldOfView(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float ret = obj.FieldOfView;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FieldOfView on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TargetOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.TargetOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TargetOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SmoothOffsetSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SmoothOffsetSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SmoothOffsetSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AllowRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AllowRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AllowXRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AllowXRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowXRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AllowYRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AllowYRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowYRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OriginAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.OriginAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OriginAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RotationSensitivity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.RotationSensitivity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotationSensitivity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MinPitchAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MinPitchAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinPitchAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MaxPitchAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MaxPitchAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxPitchAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MinYawAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MinYawAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinYawAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MaxYawAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MaxYawAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxYawAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RotationSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.RotationSmoothing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotationSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AutoSmoothing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AllowZoom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AllowZoom = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowZoom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Distance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Distance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Distance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MaxDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MaxDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MinDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MinDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ZoomSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ZoomSmoothing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ZoomSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Target(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.Target = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Target on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AudioListener(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			UnityEngine.AudioListener arg0 = (UnityEngine.AudioListener)ToLua.CheckObject(L, 2, typeof(UnityEngine.AudioListener));
			obj.AudioListener = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AudioListener on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_FieldOfView(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow2 obj = (CameraFollow2)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.FieldOfView = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FieldOfView on a nil value");
		}
	}
}

