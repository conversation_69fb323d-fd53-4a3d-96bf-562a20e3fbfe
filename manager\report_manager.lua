-- 上报管理器
ReportManager = {
	view_opreta_time_list = {},									-- 界面操作时间
	scene_opreta_time_list = {},								-- 场景停留时间

	device_id = DeviceTool.GetDeviceID(),						-- 设备id
	pkg_ver = GLOBAL_CONFIG.local_package_info.version,
	assets_ver = GLOBAL_CONFIG.local_assets_info.version,
}

-- plat_id          渠道id
-- device_id        设备id

-- 上报枚举
Report = {
	STEP_GAME_BEGIN								= 10000, -- 游戏开始，获取第一条PHP
	STEP_UPGRADE								= 10010, -- 游戏需要更新包
	STEP_UPGRADE_SUCC							= 10011, -- 更新包成功
	STEP_UPGRADE_FAIL							= 10012, -- 更新包失败
	STEP_REQUEST_REMOTE_LUA_MANIFEST 			= 10020, -- 开始请求lua RemoteManifest
	STEP_REQUEST_REMOTE_LUA_MANIFEST_FAILED		= 10021, -- 请求lua RemoteManifest失败
	STEP_LUA_MANIFEST_UNZIP						= 10022, -- 解压Lua Manifest
	STEP_REQUEST_REMOTE_MANIFEST				= 10025, -- 开始请求RemoteManifest
	STEP_REQUEST_REMOTE_MANIFEST_FAILED			= 10026, -- 请求RemoteManifest失败
	STEP_MANIFEST_UNZIP							= 10027, -- 解压Manifest
	STEP_REQUEST_LOAD_STRONG_CFG				= 10030, -- 请求强更列表
	STEP_REQUEST_LOAD_STRONG_CFG_FAIL			= 10031, -- 请求强更列表失败
	STEP_REQUEST_LOAD_FILE_INFO					= 10032, -- 开始请求file_info
	STEP_REQUEST_LOAD_FILE_INFO_FAILED			= 10033, -- 下载file_info失败
	STEP_UPDATE_ASSET_BUNDLE					= 10040, -- 开始更新AssetBundle
	STEP_UPDATE_ASSET_BUNDLE_COMPLETE			= 10050, -- 更新AssetBundle完成
	STEP_UPDATE_ASSET_BUNDLE_FAIL				= 10051, -- 更新AssetBundle失败
	STEP_REQUIRE_START							= 10060, -- 开始require列表
	STEP_REQUIRE_END							= 10070, -- require完成
	STEP_SHOW_LOGIN								= 10080, -- 显示登陆界面
	STEP_LOGIN_FAIL								= 10081, -- 登录失败
	STEP_LOGIN_COMPLETE							= 10090, -- 登陆完成
	STEP_CLOSE_ANNOUNCEMENT						= 10091, -- 关闭公告
	STEP_BEGIN_SELECT_SERVER					= 10092, -- 点击选服
	STEP_SELECT_SERVER_END						= 10093, -- 选服完成
	STEP_CLICK_START_GAME						= 10100, -- 点击开始游戏
	STEP_CONNECT_LOGIN_SERVER					= 10110, -- 开始连接登陆服务器
	STEP_LOGIN_SERVER_CONNECTED					= 10120, -- 登陆服连接上了
	STEP_LOGIN_SERVER_CONNECTED_FAILED			= 10130, -- 登陆服连接失败
	STEP_CREATE_ROLE_CG_END						= 10131, -- 完成创角动画/到达创角页
	STEP_CHANGE_PROF							= 10132, -- 换选职业
	STEP_ROLE_LIST_MERGE_ACK					= 10140, -- 合并角色列表(合服之后)
	STEP_SEND_CREATE_ROLE						= 10150, -- 发送创建角色请求
	STEP_CREATE_ROLE_ACK						= 10160, -- 创建角色成功
	STEP_CREATE_ROLE_ACK_FAILED					= 10170, -- 创建角色失败
	STEP_ROLE_LIST_ACK							= 10180, -- 获得角色列表
	STEP_SEND_ROLE_REQUEST						= 10190, -- 请求登陆角色
	STEP_SEND_ROLE_REQUEST_CROSS				= 10200, -- 请求跨服登陆角色
	STEP_ON_LOGIN_ACK							= 10210, -- 收到登陆回复

	STEP_ON_LOGIN_ACK_FAILED					= 10220, -- 登陆回复失败

	STEP_CONNECT_GAME_SERVER					= 10230, -- 游戏服连接上了
	STEP_CONNECT_GAME_SERVER_FAILED				= 10240, -- 游戏服连接失败
	STEP_SEND_ENTER_GS							= 10250, -- 请求进入游戏服
	STEP_ENTER_GS_ACK							= 10260, -- 进入场景
	STEP_ENTER_GS_ACK_FAILED					= 10270, -- 进入场景失败
	STEP_CHANGE_SCENE_BEGIN						= 10280, -- 开始切换场景
	STEP_UPDATE_SCENE_BEGIN						= 10290, -- 更新场景开始
	STEP_UPDATE_SCENE_COMPLETE					= 10300, -- 更新场景完成
	STEP_CHANGE_SCENE_COMPLETE					= 10310, -- 切换场景完成
	STEP_CHANGE_SCENE_OVERTIME					= 10311, -- 切换场景超时
	STEP_NO_MAIN_ROLE_DATA						= 10312, -- 主角信息没有返回
	STEP_CONNECT_PHP_SERVER_FAILED				= 10320, -- php请求失败
	STEP_JSON_DECODE_FAILED						= 10330, -- cjson解析失败
	STEP_START_HOT_UPDATE						= 10340, -- 启动在线热更新
	STEP_START_HOT_UPDATE_SUCC					= 10341, -- 在线热更新成功
	STEP_HOT_UPDATE_REQUIRE_FAIL				= 10342, -- 在线热更新请求Query失败
	STEP_HOT_UPDATE_DOWNLOADING_MANIFEST_FAIL	= 10343, -- 在线热更新下载Manifest失败
	STEP_HOT_UPDATE_DOWNLOADING_BUNDLE_FAIL		= 10344, -- 在线热更新下载AB包失败
	STEP_HOT_UPDATE_LUA_FILE_NOT_MATCH_BUNDLE	= 10345, -- 在线热更新Lua文件找不到对应AB包


	STEP_DISCONNECT_LOGIN_SERVER				= 11010, -- 登陆服断线
	STEP_DISCONNECT_GAME_SERVER					= 11020, -- 游戏服断线
	STEP_DISCONNECT_SHOW						= 11030, -- 显示断线提示
	STEP_DISCONNECT_RETRY						= 11040, -- 提示后重试连接
	STEP_DISCONNECT_BACK						= 11050, -- 断线后返回登陆

	CHAT_PRIVATE								= 20100, --私聊记录
	CHAT_PRIVATE_NEW							= 9, 	 -- 私聊记录新()
	CHAT_ALL									= 8, 	 -- 公聊记录
	----------------------------------------------------------------------
	STEP_REPORT_TASK							= 13, 	 -- 上报游戏新手任务

	STEP_CG_START_INFO 							= 1010, 	 -- 触发CG
	STEP_CG_LOADED_INFO 						= 1013, 	 -- 加载完成CG
	STEP_CG_END_INFO							= 1012, 	 -- CG结束

	STEP_CLICK_MAIN_ICON						= 1011,  -- 点击主界面按钮

	STEP_LOAD_SCENE_BEGIN						= 1015, -- 加载开始
	STEP_LOAD_SCENE_COMPLETE					= 1016, -- 加载场景完成
	STEP_LOAD_SCENE_END							= 1017, -- 切换场景完成
}

--获得当前ip地址
function ReportManager.GetIpAddress()
	-- return PlatformBinder:JsonCall("call_get_ip_address")
	if nil ~= GLOBAL_CONFIG.param_list and nil ~= GLOBAL_CONFIG.param_list.client_ip and "" ~= GLOBAL_CONFIG.param_list.client_ip then
		return GLOBAL_CONFIG.param_list.client_ip
	end
	return "0.0.0.0"
end

-- 测试服玩家 正式服返利
function ReportManager:SendLoginFanLi2PHP()
	if self:IsPassReport() then
		return
	end
	local user_vo
	if GameVoManager ~= nil and GameVoManager.Instance ~= nil then
		user_vo = GameVoManager.Instance:GetUserVo()
	end

	if user_vo == nil then
		return
	end

	local user_id = user_vo.account_user_id
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- 不需要发放 need_login_fanli_
	local need_fanli_key = string.format("%s_%s_%s_%s", "nlfl", user_id, role_id, server_day)
	local need_fanli_flag = PlayerPrefsUtil.GetInt(need_fanli_key)
	if need_fanli_flag == 1 then
		return
	end

	-- 已发放 had_login_fanli_
	local had_fanli_key = string.format("%s_%s_%s_%s", "hlfl", user_id, role_id, server_day)
	local had_fanli_flag = PlayerPrefsUtil.GetInt(had_fanli_key)
	if had_fanli_flag == 2 then
		return
	end

	local event_url = GlobalUrl .. "/api/rebate_login.php"
	local server_id = user_vo.plat_server_id
	local time = os.time()

	-- 存在安卓或苹果的账号，用不同的包登录的情况
	local name_list = Split(user_id, "_")
	local spid = CHANNEL_AGENT_ID
	local zone_id = #name_list > 1 and name_list[1] or spid
	local key = GlobalUrlSignKey
	local sign_tab = {
		role_id,
		server_id,
		spid,
		time,
		user_id,
		zone_id,
		key
	}

	local sign_str = table.concat(sign_tab)
	local sign = MD52.GetMD5(sign_str)
	local fl_url = string.format("%s?spid=%s&zoneId=%s&userId=%s&roleId=%s&serverId=%s&time=%s&sign=%s",
							event_url, spid, zone_id, user_id, role_id, server_id, time, sign)
	----[[
	HttpClient:Request(fl_url, function(url, arg, data, size)
		local ret_t = cjson.decode(data)
		if ret_t and ret_t.ret == 0 and ret_t.data then
			local is_rebate = ret_t.data.isRebate
			local isRebated = ret_t.data.isRebated
			if is_rebate and need_fanli_flag ~= (is_rebate + 1) then
				PlayerPrefsUtil.SetInt(need_fanli_key, is_rebate + 1)
			end

			if is_rebate ~= 0 and isRebated then
				PlayerPrefsUtil.SetInt(had_fanli_key, isRebated + 1)
			end
		end
	end)
	--]]
end

-- 开发版PC包、审核状态、 内网开发不上报
function ReportManager:IsPassReport()
	if self.is_pass_report == nil then
		self.is_pass_report = IS_LOCLA_WINDOWS_DEBUG_EXE or IS_AUDIT_VERSION
						or string.match(GLOBAL_CONFIG.local_package_info.config.init_urls[1], "192.168.0.133") ~= nil
	end

	return self.is_pass_report
end


-- 上报玩家操作行为
REPORT_ROLE_ACTTION = {
	none = "none",
	enterGame = "enterGame",
	levelUp = "levelUp",
	switchRole = "switchRole",
	rename = "rename",
	quit = "quit",
}
function ReportManager:ReportRole(action)
	if self:IsPassReport() then
		return
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	local main_role_vo =  GameVoManager.Instance:GetMainRoleVo()
	local plat_id = CHANNEL_AGENT_ID
	local post_data = {
		access_token = user_vo.access_token or "",
		plat_id = plat_id or DEFAULT_AGENT_ID,
		server_id = user_vo.plat_server_id or 0,
		role_id = user_vo:GetOriginalRoleID() or 0,
		role_name = main_role_vo.role_name or "",
		role_level = main_role_vo.level or 0,
		action = action or REPORT_ROLE_ACTTION.none,
		gender = main_role_vo.sex or 0,
		prof = main_role_vo.prof or 1,
	}

	local url = string.format("%s/v1/report/role", GlobalUrl)
	PhpHandle.HandlePhpJsonPostRequest(url, post_data, post_data, nil, nil)
end

-- 上报玩家聊天
function ReportManager:ReportChat(channel, content, chat_target_data)
	if self:IsPassReport() then
		return
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	local main_role_vo =  GameVoManager.Instance:GetMainRoleVo()
	local plat_id = CHANNEL_AGENT_ID
	local post_data = {
		access_token = user_vo.access_token or "",
		plat_id = plat_id or DEFAULT_AGENT_ID,
		server_id = user_vo.plat_server_id or 0,
		role_id = user_vo:GetOriginalRoleID() or 0,
		role_name = main_role_vo.role_name or "",
		role_level = main_role_vo.level or 0,
		channel = channel or CHANNEL_TYPE.WORLD,
		msg = content or "",
		target = chat_target_data,
	}

	local url = string.format("%s/v1/report/chat", GlobalUrl)
	PhpHandle.HandlePhpJsonPostRequest(url, post_data, post_data, nil, nil)
end

-- 异常日志上报
function ReportManager:ReportPrintError(error_str)
	if self:IsPassReport() then
		return
	end

	local plat_id = CHANNEL_AGENT_ID
	local post_data = {
		plat_id = plat_id or DEFAULT_AGENT_ID,
		error_str = error_str or "",
	}

	local url = string.format("%s/v1/report/ex-log", GlobalUrl)
	PhpHandle.HandlePhpJsonPostRequest(url, post_data, post_data, nil, nil)
end

-- 埋点计数上报
-- php要求进入游戏场景前的事件埋点走这个（未确定token）
BURIED_COUNTER_TYPE = {
	none = 0,
	gameInit = 1,			-- 游戏开始，获取第一条PHP
	upgrade = 2,			-- 游戏更新包（目前成功发）
	showLogin = 3,			-- 显示登陆界面
}
function ReportManager:ReportBuriedCounter(type)
	if self:IsPassReport() then
		return
	end

	local plat_id = CHANNEL_AGENT_ID
	local post_data = {
		plat_id = plat_id or DEFAULT_AGENT_ID,
		type = type or BURIED_COUNTER_TYPE.none,
	}

	local url = string.format("%s/v1/report/buried-counter", GlobalUrl)
	PhpHandle.HandlePhpJsonPostRequest(url, post_data, post_data, nil, nil)
end

-- 埋点事件日志上报
BURIED_EVENT_LOG_ID = {
	none = 0,
	login = 1,					-- 登录状态 param1：(1:成功)
	clickServer = 2,			-- 点击选服（不传服id）
	clickLoginStartGame = 3,	-- 登录-点击开始游戏
	createRoleCG = 4,			-- 创角CG  param1：(性别)   param2：(职业)
	CreateRoleDIY = 5,			-- 创角捏脸  param1：(性别)   param2：(职业)
	clickLoginEnterGame = 6,	-- 登录-点击进入游戏
	sceneChange = 7,			-- 场景切换  param1：(切换前场景id)   param2：(切换后场景id)   param3：(停留时间)

	pay = 8,					-- 支付  param1：(直购类型)  param2：(直购索引)  param3：(直购价格)
	viewClick = 9,				-- 界面点击  param1：(界面名) param2：(页签索引)
										-- param3：（1=打开）
										-- param3：（2=关闭） param4：(界面停留时间)
	actClick = 10,				-- 活动点击  param1：(界面名)  param2：(活动号)
	viewTabClick = 11, 			-- 界面标签切换  param1：(界面名)  param2：(页签索引)
}

BURIED_EVENT_PARAM = {
	openView = 1,
	closeView = 2,
	loginSuccess = 1,
}
-- param1（string）, param2(string), param3(string), param4(string)
function ReportManager:ReportBuriedEvent(log_id, param1, param2, param3, param4)
	if self:IsPassReport() then
		return
	end

	-- 界面点击事件需要统计在界面停留的时间
	if log_id == BURIED_EVENT_LOG_ID.viewClick then
		if param2 == nil then
			param2 = 0
		end
		if param3 == BURIED_EVENT_PARAM.openView then
			param4 = 0
			if not self.view_opreta_time_list[param1] then
				self.view_opreta_time_list[param1] = {}
			end
			-- 已经打开的情况直接return
			-- 因为页签也加了记录，带页签的首次打开changeindex也会进入，所以排除掉
			if self.view_opreta_time_list[param1][param2] then
				return
			end
			self.view_opreta_time_list[param1][param2] = Status.NowTime
		elseif param3 == BURIED_EVENT_PARAM.closeView then
			local old_time = self.view_opreta_time_list[param1] and self.view_opreta_time_list[param1][param2] or nil
			if not old_time then
				-- print_error("无界面打开点击上报", param1)
				return
			end

			param4 = math.floor(Status.NowTime - old_time)
			self.view_opreta_time_list[param1] = nil
		end
	end

	-- 场景切换事件 统计在场景停留的时间
	if log_id == BURIED_EVENT_LOG_ID.sceneChange then
		local time = self.scene_opreta_time_list[param1]
		if not time then
			param3 = 0
		else
			param3 = math.floor(Status.NowTime - time)
			self.scene_opreta_time_list[param1] = nil
		end

		self.scene_opreta_time_list[param2] = Status.NowTime
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	local main_role_vo =  GameVoManager.Instance:GetMainRoleVo()
	local plat_id = CHANNEL_AGENT_ID
	-- 存在不赋值默认值，因为php需要不传
	local post_data = {
		access_token = user_vo.access_token or "",
		plat_id = plat_id or DEFAULT_AGENT_ID,
		server_id = user_vo.plat_server_id or 0,
		role_id = user_vo:GetOriginalRoleID() or 0,
		role_name = main_role_vo.role_name,
		log_id = log_id or BURIED_EVENT_LOG_ID.none,
		param1 = param1,
		param2 = param2,
		param3 = param3,
		param4 = param4,
	}

	local url = string.format("%s/v1/report/buried-event", GlobalUrl)
	PhpHandle.HandlePhpJsonPostRequest(url, post_data, post_data, nil, nil)
end

