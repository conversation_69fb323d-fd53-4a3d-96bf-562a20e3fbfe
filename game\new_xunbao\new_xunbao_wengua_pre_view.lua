WenGuaPreView = WenGuaPreView or BaseClass(SafeBaseView)
function WenGuaPreView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/zhuangbeixunbao_ui_prefab", "layout_wengua_pre")
    self:SetMaskBg(true, true)
end

function WenGuaPreView:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function WenGuaPreView:LoadCallBack()
    self:SetSecondView(nil, self.node_list["bg_size"])
    self.node_list.title_view_name.text.text = Language.WenGua.Pre_Title

    self.item_list = AsyncBaseGrid.New()
    local t = {
        col = 5,
        itemRender = WenGuaRender,
        list_view = self.node_list["gua_list"],
        change_cells_num = 1,
        assetBundle = "uis/view/zhuangbeixunbao_ui_prefab",
        assetName = "wengua_cell",
    }
    self.item_list:CreateCells(t)
    self.item_list:SetStartZeroIndex(false)
    self.mask_bg.image.color = Color.New(0, 0, 0, 0.8)
end

function WenGuaPreView:ShowIndexCallBack()
    self:Flush()
end

function WenGuaPreView:OnFlush()
    local list = NewXunbaoWGData.Instance:GetAllRedGuaList()
    self.item_list:SetDataList(list, 3)
end

---------------------------------------------------------------------------------
----------------------------------------------------------------------------------
---------------------------------------------------------------------------------
WenGuaRecordView = WenGuaRecordView or BaseClass(SafeBaseView)

WenGuaRecordView.STATE = {
    ALL = 1,
    SELF = 2
}

function WenGuaRecordView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/zhuangbeixunbao_ui_prefab", "layout_xunbao_record")
	self:SetMaskBg()
end

function WenGuaRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function WenGuaRecordView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.WenGua.RecordTitle
    self:SetSecondView(nil, self.node_list["size"])

    self.node_list["btn_send"].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, WenGuaRecordView.STATE.ALL))
	self.node_list["btn_accept"].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, WenGuaRecordView.STATE.SELF))

    self.record_list = AsyncListView.New(WenGuaRecordRender, self.node_list["role_list"])
end

function WenGuaRecordView:ShowIndexCallBack()
    self.node_list["btn_send"].toggle.isOn = true
end

function WenGuaRecordView:OnFlush(param)
    for i, v in pairs(param) do
        if self.choose_state == i then
            local data
            if i == WenGuaRecordView.STATE.ALL then
                data = NewXunbaoWGData.Instance:GetWenGuaServerRecord()
            else
                data = NewXunbaoWGData.Instance:GetWenGuaPersonRecord()
            end
            self.node_list["no_invite"]:SetActive(IsEmptyTable(data))
            self.record_list:SetDataList(data, 3)
        end
    end
end

function WenGuaRecordView:OnClickSwitch(state)
    self.choose_state = state
    if state == WenGuaRecordView.STATE.ALL then
        NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.QUERY_SERVER_RECORD)
    else
        NewXunbaoWGCtrl.Instance:SendWenGuaOp(WENGUA_OPERA_TYPE.QUERY_INFO)
    end
end

function WenGuaRecordView:SetLayer(layer)
    self.layer = layer
end

-------------------------------------------------------------------------------------------------------
WenGuaRecordRender = WenGuaRecordRender or BaseClass(BaseRender)
function WenGuaRecordRender:LoadCallBack()
    self.node_list["txt_btn"].button:AddClickListener(BindTool.Bind(self.OnclickItem, self))
end

function WenGuaRecordRender:OnFlush()
    local cfg = NewXunbaoWGData.Instance:GetGuaItemByRewardId(self.data.reward_id)
    local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.reward_item.item_id)
    local str1, name, num
    if self.data.open_next_pool_id then
        local pool_color = ITEM_COLOR[self.data.draw_pool_id + 1]
        local pool_name = Language.WenGua.PoolName[self.data.draw_pool_id + 1]
        str1 = string.format(Language.WenGua.TxtRecord1_1, pool_color, pool_name)
        name = string.format(Language.WenGua.TxtRecord1_2, ITEM_COLOR[item_cfg.color], item_cfg.name)
        num = string.format(Language.WenGua.TxtRecord1_3, ITEM_COLOR[item_cfg.color], cfg.reward_item.num)
        if self.data.open_next_pool_id ~= 0 then
            pool_color = ITEM_COLOR[self.data.open_next_pool_id + 1]
            pool_name = Language.WenGua.PoolName[self.data.open_next_pool_id + 1]
            num = num .. string.format(Language.WenGua.TxtRecord2, pool_color, pool_name)
        end
    else
        local role_name = self.data.name
        str1 = string.format(Language.WenGua.TxtRecord3, role_name)
        name = string.format(Language.WenGua.TxtRecord1_2, ITEM_COLOR[item_cfg.color], item_cfg.name)
        num = string.format(Language.WenGua.TxtRecord1_3, ITEM_COLOR[item_cfg.color], cfg.reward_item.num)
    end
    self.node_list["desc"].text.text = str1
    self.node_list["txt_btn"].text.text = name
    self.node_list["num"].text.text = num
    local str = os.date("*t", self.data.timestamp)
    self.node_list["time"].text.text = string.format(Language.WenGua.WenGuaTimeHint,str.month,str.day,str.hour,str.min,str.sec)
    self.node_list["img_root_bg"].image.enabled = (self.index % 2) == 0
end

function WenGuaRecordRender:OnclickItem()
    local cfg = NewXunbaoWGData.Instance:GetGuaItemByRewardId(self.data.reward_id)
    TipWGCtrl.Instance:OpenItem(cfg.reward_item)
end