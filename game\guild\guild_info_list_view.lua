GuildPopInfoView = GuildPopInfoView or BaseClass(SafeBaseView)

function GuildPopInfoView:InitListView(  )
    self.memberlist_view = self.node_list.info_member_list
    self.select_item = nil
	self.click_list_flag = false
    self:CreateGuildList()

	self:RegisterGuildListEvent()
end

function GuildPopInfoView:DeleteGuildListView()
	if self.member_list2 then
		self.member_list2:DeleteMe()
		self.member_list2 = nil
		self.click_list_flag = false
	end	
end

-- 创建列表控件
function GuildPopInfoView:CreateGuildList()
	if nil == self.member_list2 then
		self.member_list2 = AsyncListView.New(GuildInfoListItem, self.node_list["info_member_list"])--替换
		self.member_list2:SetSelectCallBack(BindTool.Bind1(self.OnSelectGuildListItemHandler, self))
		self.member_list2:JumpToTop()
	end
end


function GuildPopInfoView:OnSelectGuildListItemHandler(item)
	if nil == item then
		return
	end
	if nil == item.data then
		return
	end
	self.select_item = item
	if not self.click_list_flag then
		self.click_list_flag = true
		return
    end
    --点击成员
end

function GuildPopInfoView:FlushMemberListDatasource()
	self.click_list_flag = false
	local member_datasource = self:GetMemberDatasource()
    if nil ~= self.member_list2 then
		self.member_list2:SetDataList(member_datasource, 0)
		self:ClickItemByGuide(1)
	end
end

-- 排序数据源
function GuildPopInfoView:SortGuildList(a, b)

end

function GuildPopInfoView:ClickItemByGuide(index)
	self.member_list2:SelectIndex(index)
end