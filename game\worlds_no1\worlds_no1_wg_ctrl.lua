require("game/worlds_no1/worlds_no1_wg_data")
require("game/worlds_no1/worlds_no1_view")
require("game/worlds_no1/worlds_no1_reward_view")
require("game/worlds_no1/worlds_no1_timetable_view")
require("game/worlds_no1/worlds_no1_scene_view")
require("game/worlds_no1/worlds_no1_countdown_view")
require("game/worlds_no1/worlds_no1_end_countdown_view")
require("game/worlds_no1/worlds_no1_observation_view")
require("game/worlds_no1/worlds_no1_end_view")
require("game/worlds_no1/worlds_no1_get_new_item_view")

-- 天下第一
WorldsNO1WGCtrl = WorldsNO1WGCtrl or BaseClass(BaseWGCtrl)

function WorldsNO1WGCtrl:__init()
	if WorldsNO1WGCtrl.Instance ~= nil then
		ErrorLog("[WorldsNO1WGCtrl] attempt to create singleton twice!")
		return
	end

	WorldsNO1WGCtrl.Instance = self
	self.data = WorldsNO1WGData.New()
	self.view = WorldsNO1View.New(GuideModuleName.WorldsNO1View)
	self.reward_view = WorldsNO1RewardView.New(GuideModuleName.WorldsNO1RewardView) 								-- 奖励面板
	self.timetable_view = WorldsNO1TimetableView.New(GuideModuleName.WorldsNO1TimetableView) 						-- 对战场次时间面板
	self.scene_view = WorldsNO1SceneView.New(GuideModuleName.WorldsNO1SceneView)  									-- 场景面板
	self.worlds_no1_countdown_view = WorldsNO1CountdownView.New(GuideModuleName.WorldsNO1CountdownView) 			-- 开打倒计时
	self.worlds_no1_end_countdown_view = WorldsNO1EndCountdownView.New(GuideModuleName.WorldsNO1EndCountdownView) 	-- 结束倒计时
	self.worlds_no1_end_view = WorldsNO1EndView.New() 																-- 结算面板
	self.observation_view = WorldsNO1ObservationView.New(GuideModuleName.WorldsNO1ObservationView) 					-- 观战面板
	self.get_new_item_view = WorldsNO1GetNewItemView.New()

	self:RegisterAllProtocols()
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
	self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneChangeEnd, self))
end

function WorldsNO1WGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.timetable_view:DeleteMe()
	self.timetable_view = nil

	self.reward_view:DeleteMe()
	self.reward_view = nil

	self.worlds_no1_end_countdown_view:DeleteMe()
	self.worlds_no1_end_countdown_view = nil

	self.observation_view:DeleteMe()
	self.observation_view = nil

	self.worlds_no1_countdown_view:DeleteMe()
	self.worlds_no1_countdown_view = nil

	self.get_new_item_view:DeleteMe()
	self.get_new_item_view = nil

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	self.shield_rule = nil
	self.follow_ui_shield_rule = nil

	WorldsNO1WGCtrl.Instance = nil
end

function WorldsNO1WGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSTianXiaDiYiOper) 	
	self:RegisterProtocol(CSTianXiaDiYiBulletChat) 	 												-- 发送弹幕
	self:RegisterProtocol(SCTianXiaDiYiBulletChat, "OnSCTianXiaDiYiBulletChat")  					-- 收到弹幕
	self:RegisterProtocol(SCTianXiaDiYiActRandData, "OnSCTianXiaDiYiActRandData")  					-- 排名信息
	self:RegisterProtocol(SCTianXiaDiYiPKSceneRoleInfo, "OnSCTianXiaDiYiPKSceneRoleInfo")  			-- 观战中战斗场景角色实时信息
	self:RegisterProtocol(SCTianXiaDiYiGuessInfo, "OnSCTianXiaDiYiGuessInfo")  						-- 竞猜信息
	self:RegisterProtocol(SCTianXiaDiYiPKFinishInfo, "OnSCTianXiaDiYiPKFinishInfo")  				-- 结算信息
	self:RegisterProtocol(SCTianXiaDiYiPKScoreRankInfo, "OnSCTianXiaDiYiPKScoreRankInfo")  			-- 战斗场景角色实时排名信息
	self:RegisterProtocol(SCTianXiaDiYiWatchInfo, "OnSCTianXiaDiYiWatchInfo")  						-- 当前观战的目标uuid
	self:RegisterProtocol(SCTianXiaDiYiChampionData, "OnSCTianXiaDiYiChampionData")  				-- 上届冠军信息
	self:RegisterProtocol(SCTianXiaDiYiDianZhanInfo, "OnSCTianXiaDiYiDianZhanInfo")  				-- 对上届冠军点赞信息
	self:RegisterProtocol(SCTianXiaDiYiPKScenePosi, "OnSCTianXiaDiYiPKScenePosi")  					-- 出生点信息
	self:RegisterProtocol(SCTianXiaDiYiActivityStatus, "OnSCTianXiaDiYiActivityStatus")  			-- 轮次信息
	self:RegisterProtocol(SCTianXiaDiYiPKAddScore, "OnSCTianXiaDiYiPKAddScore")  					-- 增加的积分
	self:RegisterProtocol(SCTianXiaDiYiJoinFlag, "OnSCTianXiaDiYiJoinFlag")  						-- 参与资格
	self:RegisterProtocol(SCTianXiaDiYiStandbySceneFlag, "OnTianXiaDiYiStandbySceneFlag")  			-- 准备场景pk阶段子轮状态
	self:RegisterProtocol(SCTianXiaDiYiDianZanRewardInfo, "OnTianXiaDiYiDianZanRewardInfo")  		-- 点赞获得的奖励
	self:RegisterProtocol(SCTianXiaDiYiBuffInfo, "OnSCTianXiaDiYiBuffInfo")  						-- 捡取buff返回
end

-- 操作（对应枚举TianShen3V3OperaType）
function WorldsNO1WGCtrl:SendWorldsNO1Opera(opera_type, param1, uuid_str)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianXiaDiYiOper)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.uuid_str = uuid_str or ToLLStr(0, 0)
	protocol:EncodeAndSend()
end

-- 请求所有排名信息
function WorldsNO1WGCtrl:SendRequestAllRankInfo()
	for i,v in pairs(WORLDS_RANK_INFO_TYPE) do
		WorldsNO1WGCtrl.Instance:SendRequestRankInfo(v)
	end
end

-- 请求获取排名信息，参数对应枚举WORLDS_RANK_INFO_TYPE
function WorldsNO1WGCtrl:SendRequestRankInfo(rank_info_type)
	self:SendWorldsNO1Opera(TIANXIADIYI_PERSON_OPER_TYPE.TIANXIADIYI_PERSON_OPER_TYPE_1, rank_info_type)
end

-- 请求竞猜某人
function WorldsNO1WGCtrl:SendGuess(subround, uuid_str)
	self:SendWorldsNO1Opera(TIANXIADIYI_PERSON_OPER_TYPE.TIANXIADIYI_PERSON_OPER_TYPE_2, subround, uuid_str)
end

-- 请求观战
function WorldsNO1WGCtrl:SendObservate(uuid_str)
	self:SendWorldsNO1Opera(TIANXIADIYI_PERSON_OPER_TYPE.TIANXIADIYI_PERSON_OPER_TYPE_3, nil, uuid_str)
end

-- 请求获取上届冠军信息
function WorldsNO1WGCtrl:SendRequestChampionInfo()
	self:SendWorldsNO1Opera(TIANXIADIYI_PERSON_OPER_TYPE.TIANXIADIYI_PERSON_OPER_TYPE_4)
end

-- 请求点赞
function WorldsNO1WGCtrl:SendLike()
	self:SendWorldsNO1Opera(TIANXIADIYI_PERSON_OPER_TYPE.TIANXIADIYI_PERSON_OPER_TYPE_5)
end

-- 请求获取点赞信息
function WorldsNO1WGCtrl:SendRequestLikeInfo()
	self:SendWorldsNO1Opera(TIANXIADIYI_PERSON_OPER_TYPE.TIANXIADIYI_PERSON_OPER_TYPE_6)
end

-- 请求获取轮次信息（场景外使用，场景内服务端会主动下发）
function WorldsNO1WGCtrl:SendRequestRoundInfo()
	self:SendWorldsNO1Opera(TIANXIADIYI_PERSON_OPER_TYPE.TIANXIADIYI_PERSON_OPER_TYPE_7)
end

-- 发送弹幕
function WorldsNO1WGCtrl:SendWorldsNO1Barrage(talk_content)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianXiaDiYiBulletChat)
	protocol.talk_content = talk_content or ""
	protocol:EncodeAndSend()
end

-- 收到弹幕
function WorldsNO1WGCtrl:OnSCTianXiaDiYiBulletChat(protocol)
	if WorldsNO1WGData.Instance:GetShowBarrage() then
		DuckRaceWGCtrl.Instance:OpenBarrageView(protocol.talk_content, false, protocol.sender_uuid_str == RoleWGData.Instance:GetUUIDStr())
	end
end

-- 排名信息
function WorldsNO1WGCtrl:OnSCTianXiaDiYiActRandData(protocol)
	self.data:SetRankListInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1View)
end

-- 观战中战斗场景角色实时信息
function WorldsNO1WGCtrl:OnSCTianXiaDiYiPKSceneRoleInfo(protocol)
	self.data:SetObservationalSceneRoleListInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1ObservationView)

	-- 因为天下第一改了AOI，血量不同步了，所以在这里设置HP
	if self.data:IsObservationStatus() then
		for i,v in ipairs(protocol.scene_role_info_list) do
			if v.obj_id >= 0 and v.max_hp > 0 then
				local obj = Scene.Instance:GetObjectByObjId(v.obj_id)
				if obj and obj:IsRole() and not obj:IsMainRole() then
					obj:SetAttr("hp", v.cur_hp)
					obj:SetAttr("max_hp", v.max_hp)
				end
			end
		end
	end
end

-- 竞猜信息
function WorldsNO1WGCtrl:OnSCTianXiaDiYiGuessInfo(protocol)
	self.data:SetGuessInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1View, nil, "knockout_list")
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1View, nil, "others")
	self.data:FlushMainRoleChatBetBtn()
end

-- 结算信息
function WorldsNO1WGCtrl:OnSCTianXiaDiYiPKFinishInfo(protocol)
	self.worlds_no1_end_view:SetData(protocol)
	if self.worlds_no1_end_view:IsOpen() then
		self.worlds_no1_end_view:Flush()
	else
		self.worlds_no1_end_view:Open()
	end
end

-- 战斗场景角色实时排名信息
function WorldsNO1WGCtrl:OnSCTianXiaDiYiPKScoreRankInfo(protocol)
	self.data:SetSceneRankInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1SceneView)
	if protocol.pk_begin_countdown_timestamp > TimeWGCtrl.Instance:GetServerTime() and not ViewManager.Instance:IsOpen(GuideModuleName.WorldsNO1CountdownView) then
		ViewManager.Instance:Open(GuideModuleName.WorldsNO1CountdownView)
	end
end

-- 主角观战目标
function WorldsNO1WGCtrl:OnSCTianXiaDiYiWatchInfo(protocol)
	local last_target = WorldsNO1WGData.Instance:GetObservationUUIDStr()
	if last_target ~= protocol.target_uuid_str then
		ViewManager.Instance:Close(GuideModuleName.WorldsNO1View)
	end
	
	self.data:SetObservationTarget(protocol.target_uuid_str)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1SceneView)
	-- 刷新主角隐藏规则
	self:FlushObservationMainRoleHide()

	-- 观战面板
	if self.data:IsObservationStatus() then
		if ViewManager.Instance:IsOpen(GuideModuleName.WorldsNO1ObservationView) then
			ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1ObservationView)
		else
			ViewManager.Instance:Open(GuideModuleName.WorldsNO1ObservationView)
		end

		GuajiWGCtrl.Instance:ClearAllOperate()
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo()
		GuajiWGCtrl.Instance:StopGuaji()
	else
		ViewManager.Instance:Close(GuideModuleName.WorldsNO1ObservationView)
	end

	-- 刷新摄像机
	self:FlushObservationCamera()

	-- 刷新主界面
	MainuiWGCtrl.Instance:FlushView(0, "ObservationState")

	-- 刷新屏蔽规则
	if SceneType.WorldsNO1 == Scene.Instance:GetSceneType() then
		local logic = Scene.Instance:GetSceneLogic()
		logic:RefreshEffectRule()
	end

	-- 清空反击列表
	RevengeWGData.Instance:ClearList()
	MainuiWGCtrl.Instance:RevengeChange()
end

function WorldsNO1WGCtrl:OnSceneChangeEnd()
	-- 刷新主界面
	MainuiWGCtrl.Instance:FlushView(0, "ObservationState")
end

-- 上届冠军信息
function WorldsNO1WGCtrl:OnSCTianXiaDiYiChampionData(protocol)
	self.data:SetChampionInfo(protocol)
	RemindManager.Instance:Fire(RemindName.WorldsNO1LikeRemind)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1View, nil, "no1_palyer_info")
end

-- 冠军点赞状态
function WorldsNO1WGCtrl:OnSCTianXiaDiYiDianZhanInfo(protocol)
	self.data:SetLikeStatus(protocol)
	RemindManager.Instance:Fire(RemindName.WorldsNO1LikeRemind)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1View, nil, "no1_palyer_info")
end

-- 进场景复活点信息
function WorldsNO1WGCtrl:OnSCTianXiaDiYiPKScenePosi(protocol)
	if Scene.Instance:GetSceneType() == SceneType.WorldsNO1 then
		local scene_logic = Scene.Instance:GetSceneLogic()
		scene_logic:TryChangeCameraAngle(protocol.pos_index) 	-- 根据复活点位置设置摄像机角度
	end
end

-- 轮次信息
function WorldsNO1WGCtrl:OnSCTianXiaDiYiActivityStatus(protocol)
	self.data:SetRoundStatusInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1SceneView)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1View)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1TimetableView)
	self.data:FlushMainRoleChatBetBtn()

	if Scene.Instance:GetSceneType() == SceneType.WorldsNO1 then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(WorldsNO1WGData.Instance:GetNextChangeStatusTimestamp())
	end

	-- 请求刷新一下排名信息
	self:SendRequestAllRankInfo()
end

-- 主角积分增加
local VECTOR2_POS = Vector2(245, 300)
function WorldsNO1WGCtrl:OnSCTianXiaDiYiPKAddScore(protocol)
	local str = Language.WorldsNO1.ScoreAdd
	if protocol.add_type == WORLDS_NO1_SCORE_ADD_TYPE.AddType_1 then
		str = Language.WorldsNO1.KillScoreAdd
	end
	TipWGCtrl.Instance:ShowNumberMsg(string.format(str, protocol.add_score), 0.2, VECTOR2_POS, nil, nil, true)
end

-- 参与资格
function WorldsNO1WGCtrl:OnSCTianXiaDiYiJoinFlag(protocol)
	self.data:SetCanJoin(protocol.can_join)
	RemindManager.Instance:Fire(RemindName.WorldsNO1EnterRemind)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1SceneView)
end

-- 准备场景pk阶段子轮状态
function WorldsNO1WGCtrl:OnTianXiaDiYiStandbySceneFlag(protocol)
	self.data:SetCurSubroundIsMiss(protocol.is_miss)
	self.data:SetCurSubroundIsEmpty(protocol.is_empty)
	ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1SceneView)
end

-- 点赞奖励返回
function WorldsNO1WGCtrl:OnTianXiaDiYiDianZanRewardInfo(protocol)
	self:OpenWorldsNO1GetNewItemView(protocol.item_list)
end

-- 捡取buff返回
local HP_BUFF_TYPE = 2
function WorldsNO1WGCtrl:OnSCTianXiaDiYiBuffInfo(protocol)
	self.data:AddBuffInfo(protocol)
	local buff_cfg = self.data:GetBuffCfg(protocol.buff_type)
	if buff_cfg then
		if buff_cfg.show_in_buff_list == 1 then
			ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1SceneView, nil, "fly_to_buff_list")
		elseif buff_cfg.buff_type == HP_BUFF_TYPE then
			ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1SceneView, nil, "fly_to_hp_bar")
		end
		TipWGCtrl.Instance:ShowSystemMsg(buff_cfg.buff_desc)
	end
end

-- 打开物品恭喜获得面板
function WorldsNO1WGCtrl:OpenWorldsNO1GetNewItemView(item_info_list)
	self.get_new_item_view:SetData(item_info_list)
	self.get_new_item_view:Open()
end

-- 活动状态更变
function WorldsNO1WGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.WORLDS_NO1 then
		local act_cfg = DailyWGData.Instance:GetActivityConfig(activity_type)
		if not MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg) then
			ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1View)
			GlobalTimerQuest:AddDelayTimer(function ()
				ViewManager.Instance:FlushView(GuideModuleName.WorldsNO1View)
			end, 2) -- 过几秒在刷一次，避免客户端自己算的时间状态有误差，没刷新到
			
			if status == ACTIVITY_STATUS.OPEN and not ViewManager.Instance:IsOpen(GuideModuleName.WorldsNO1View) then
				-- 活动开启Tips
				-- local round_cfg = self.data:GetRoundCfg(self.data:GetCurRound(), self.data:GetCurSubround())
				-- if round_cfg then
				-- 	local str = string.format(Language.WorldsNO1.ActOpenTips, round_cfg.round_name)
					TipWGCtrl.Instance:OpenAlertTips(Language.WorldsNO1.ActOpenTips, function()
						ViewManager.Instance:Open(GuideModuleName.WorldsNO1View)
				 	end)
				-- end
			end
		end
		RemindManager.Instance:Fire(RemindName.WorldsNO1EnterRemind)
		self:SendRequestRoundInfo()
	end
end

-- 如果主角是观战者，则把主角模型隐藏
function WorldsNO1WGCtrl:FlushObservationMainRoleHide()
	if WorldsNO1WGData.Instance:IsObservationStatus() then
		if not self.shield_rule then
			self.shield_rule = Scene.Instance:GetMainRole():AddShieldRule(ShieldRuleWeight.Max, function() return true end)
		end
		if not self.follow_ui_shield_rule then
			local follow_ui = Scene.Instance:GetMainRole():GetFollowUi()
		   	self.follow_ui_shield_rule = follow_ui:AddShieldRule(ShieldRuleWeight.Max, function() return true end)
		end
	else
		if self.shield_rule then
			Scene.Instance:GetMainRole():RemoveShieldRule(self.shield_rule)
			self.shield_rule = nil
		end

		if self.follow_ui_shield_rule then
			local follow_ui = Scene.Instance:GetMainRole():GetFollowUi()
			if follow_ui then
				follow_ui:RemoveShieldRule(self.follow_ui_shield_rule)
			end
			self.follow_ui_shield_rule = nil
		end
	end
end

-- 刷新摄像机目标
function WorldsNO1WGCtrl:FlushObservationCamera()
	if WorldsNO1WGData.Instance:IsObservationStatus() then
		local target_obj = Scene.Instance:GetRoleByOrginalUUIDStr(WorldsNO1WGData.Instance:GetObservationUUIDStr())
		if target_obj and MainCameraFollow then
			if target_obj.draw_obj:GetLookAtPoint() ~= MainCameraFollow.Target then
				-- 修改摄像机角度
				local scene_id = Scene.Instance:GetSceneId()
				local param_t = {scene_id = scene_id}
				Scene.Instance:ChangeCamera(ADJUST_CAMERA_TYPE.CAN_CHANGE, 40, MainCameraFollow.transform.localEulerAngles.y, 17, param_t)
			end

			-- 修改摄像机跟随目标
			MainCameraFollow.Target = target_obj.draw_obj:GetLookAtPoint()

			-- 选中目标玩家
			GuajiCache.target_obj = nil
			GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
		end
	else
		local main_role = Scene.Instance:GetMainRole()
		main_role:UpdateCameraFollowTarget()
	end
end