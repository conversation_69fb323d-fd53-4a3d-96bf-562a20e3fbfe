UltimateBattlefieldBarrageView = UltimateBattlefieldBarrageView or BaseClass(SafeBaseView)

function UltimateBattlefieldBarrageView:__init()
    self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_barrage")
end

function UltimateBattlefieldBarrageView:LoadCallBack()
    if not self.regional_barrage then
        self.regional_barrage = RegionalBarrage.New(self.node_list.regional_barrage_area)
        self.regional_barrage:SetGetDanMuInfoFunc(BindTool.Bind(function ()
            return UltimateBattlefieldWGData.Instance:GetBarrageMessage()
        end, self))
    end

    XUI.AddClickEventListener(self.node_list.btn_get_reward, BindTool.Bind1(self.OnClickGetBarrageReward, self))
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.OnClickICloseMainRoot, self))
    XUI.AddClickEventListener(self.node_list.open_panel_btn, BindTool.Bind1(self.OnClickIOpenMainRoot, self))
    XUI.AddClickEventListener(self.node_list.send_button, BindTool.Bind1(self.OnClickSendBarrage, self))
    XUI.AddClickEventListener(self.node_list.quick_send, BindTool.Bind1(self.OnClickQuickSend, self))
    XUI.AddClickEventListener(self.node_list.shield_barrage, BindTool.Bind1(self.OnClickShieldBarrage, self))

    self.cool_time = 0
    self.shield_danmu = false
    self.can_get_reward = false
    self:ShowMainRoot(true)
end

function UltimateBattlefieldBarrageView:CloseCallBack()
    -- if self.regional_barrage then
    --     self.regional_barrage:StopAndClearDanMu()
    -- end

    self.cool_time = 0
    self:CleanTimeDown()
end

function UltimateBattlefieldBarrageView:ShowIndexCallBack()
    if self.regional_barrage then
        self.regional_barrage:StartShowDanMu(0.3)
    end
end

function UltimateBattlefieldBarrageView:ReleaseCallBack()
    if self.regional_barrage then
        self.regional_barrage:DeleteMe()
        self.regional_barrage = nil 
    end
end

function UltimateBattlefieldBarrageView:OnFlush()
    local barrage_data = UltimateBattlefieldWGData.Instance:GetBarrageChatOpenData()
    
    if not barrage_data then
        return
    end

    self.node_list.title_content.text.text = string.format(Language.UltimateBattlefield.BarrageTitle, barrage_data.name) 
    -- 刷新倒计时
    local base_cfg = UltimateBattlefieldWGData.Instance:GetBaseCfg()
    local barrage_show_time = base_cfg and base_cfg.barrage_show_time or 1
    self:FlushTimeCountDown(barrage_data.time + barrage_show_time)
    self:FlushShieldButton()
    self:FlushRewardButton()
end

-- 打开弹幕主界面
function UltimateBattlefieldBarrageView:ShowMainRoot(is_show)
    self.node_list.open_panel_btn:CustomSetActive(not is_show)
    self.node_list.main_root:CustomSetActive(is_show)

    if self.regional_barrage then
        if is_show then
            self.regional_barrage:StartShowDanMu(0.3)
        else
            self.regional_barrage:StopAndClearDanMu()
        end
    end
end

-- 刷新屏蔽弹幕按钮状态
function UltimateBattlefieldBarrageView:FlushShieldButton()
    XUI.SetGraphicGrey(self.node_list.shield_barrage, not self.shield_danmu)
end

-- 刷新领取奖励箱子状态
function UltimateBattlefieldBarrageView:FlushRewardButton()
    if self.node_list and self.node_list.btn_get_reward then
        local base_data = UltimateBattlefieldWGData.Instance:GetBaseInfo()
        local base_cfg = UltimateBattlefieldWGData.Instance:GetBaseCfg()
        if (not base_data) or (not base_cfg) then
            return
        end
    
        local limit_times = base_cfg.send_barrage_reward_times or 3
        local finish_times = base_data.barrage_reward_flag or 3
        local is_can_get_reward = finish_times < limit_times
        local can_get_status = is_can_get_reward and self.can_get_reward
    
        XUI.SetGraphicGrey(self.node_list.btn_get_reward, not can_get_status)
    end
end

---------------------------------------------------------------------------
-- 关闭弹幕主界面
function UltimateBattlefieldBarrageView:OnClickICloseMainRoot()
    self:ShowMainRoot(false)
end

-- 打开弹幕主界面
function UltimateBattlefieldBarrageView:OnClickIOpenMainRoot()
    self:ShowMainRoot(true)
end

-- 领取弹幕奖励
function UltimateBattlefieldBarrageView:OnClickGetBarrageReward()
    local base_data = UltimateBattlefieldWGData.Instance:GetBaseInfo()
    local base_cfg = UltimateBattlefieldWGData.Instance:GetBaseCfg()
    if (not base_data) or (not base_cfg) then
        return
    end

    local limit_times = base_cfg.send_barrage_reward_times or 3
    local finish_times = base_data.barrage_reward_flag or 3
    local is_can_get_reward = finish_times < limit_times

    if is_can_get_reward and self.can_get_reward then
        UltimateBattlefieldWGCtrl.Instance:RequestBarrageReward()
        self.can_get_reward = false
    else
        if not is_can_get_reward then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.UltimateBattlefield.BarrageRewardNotGet2)
        elseif not self.can_get_reward then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.UltimateBattlefield.BarrageRewardNotGet1)
        end
    end
end

-- 发送弹幕
function UltimateBattlefieldBarrageView:OnClickSendBarrage()
    local is_cool = false
    if self.cool_time ~= 0 then
        is_cool = TimeWGCtrl.Instance:GetServerTime() <= self.cool_time
    end

    local content = self.node_list.barrage_input.input_field.text

    -- 内容为空或者冷却break
    if content == "" or is_cool then
        return
    end

    -- 需要做输入敏感词屏蔽
    UltimateBattlefieldWGCtrl.Instance:SendCSCross1VNBarrageChat(content)
    self.node_list.barrage_input.input_field.text = ""
end

-- 快捷弹幕
function UltimateBattlefieldBarrageView:OnClickQuickSend()
    local cfg = UltimateBattlefieldWGData.Instance:GetBarrageRandomCfg()

    if not cfg then
        return
    end

    self.node_list.barrage_input.input_field.text = cfg.barrage_content
end

-- 隐藏弹幕
function UltimateBattlefieldBarrageView:OnClickShieldBarrage()
    self.shield_danmu = not self.shield_danmu
    self:FlushShieldButton()

    if self.regional_barrage then
        if not self.shield_danmu then
            self.regional_barrage:StartShowDanMu(0.3)
        else
            self.regional_barrage:StopAndClearDanMu()
        end
    end
end

function UltimateBattlefieldBarrageView:AddOneSpecialDanMu(desc_content, time, uid)
    local self_uuid = RoleWGData.Instance:GetUUid().temp_low
    if uid == self_uuid then
        self.can_get_reward = true
        self:FlushRewardButton()
        -- 冷却时间为当前时间加上2秒
        self.cool_time = time + 2
    end

    if self.regional_barrage then
        local danmu_info = {
            desc_content = desc_content,
            need_random_color = false,
            is_shield_bg = true,
            bg_bundle = nil,
            bg_asset = nil,
            move_speed = math.random(200, 500),
            color_data_list = ITEM_COLOR_DARK
        }
        self.regional_barrage:AddOneTemporaryDanMu(danmu_info)
    end
end


-----------------弹幕时间倒计时-------------------
function UltimateBattlefieldBarrageView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("ultimate_battlefield_barrage_view_down") then
		CountDownManager.Instance:RemoveCountDown("ultimate_battlefield_barrage_view_down")
	end
end

function UltimateBattlefieldBarrageView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanTimeDown()
        local time_down_str = TimeUtil.FormatSecondDHM6(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		self.node_list["time_txt"].text.text = time_down_str
        self.node_list["main_root_time_txt"].text.text = time_down_str
		CountDownManager.Instance:AddCountDown("ultimate_battlefield_barrage_view_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function UltimateBattlefieldBarrageView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
        local time_down_str = TimeUtil.FormatSecondDHM6(valid_time)
		self.node_list["time_txt"].text.text = time_down_str
        self.node_list["main_root_time_txt"].text.text = time_down_str
	end
end

function UltimateBattlefieldBarrageView:OnComplete()
    self.node_list["time_txt"].text.text = ""
    self.node_list["main_root_time_txt"].text.text = ""
	self:Close()
end