FlowerBackView = FlowerBackView or BaseClass(SafeBaseView)

function FlowerBackView:__init()
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/sendflower_ui_prefab", "layout_flowerback")
	--设置是否自动提醒
	self.noremind = false
end

function FlowerBackView:__delete()
	self.ctrl = nil
end

function FlowerBackView:ReleaseCallBack()	
end

function FlowerBackView:LoadCallBack()
	self.ctrl = FlowerWGCtrl.Instance:GetFlowerData()
	local layout_daxie = self.node_list.layout_daxie
	local layout_sendback = self.node_list.layout_sendback
	local layout_close = self.node_list.btn_close_window

	--回赠事件注册
	XUI.AddClickEventListener(layout_daxie , BindTool.Bind1(self.ClickDaxieH<PERSON><PERSON>, self))
	XUI.AddClickEventListener(layout_sendback , BindTool.Bind1(self.ClickSend<PERSON>ack<PERSON><PERSON><PERSON>, self))
	XUI.AddClickEventListener(layout_close, BindTool.Bind1(self.Close, self))
	XUI.AddClickEventListener(self.node_list["btn_not_tips"], BindTool.Bind(self.OnCloseNoTips, self))

	self.node_list["btn_not_tips_hook"]:CustomSetActive(self.noremind)
end 

function FlowerBackView:OnFlush()
	if self.ctrl then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.ctrl.item_id)
		local flower_name = item_cfg and ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color]) or ""
		--魅力值==亲密度
		local content = string.format(Language.Flower.ReceiveFlowerTxt, self.ctrl.from_name, flower_name,self.ctrl.flower_num,self.ctrl.flower_num)
		self.node_list.rich_tip_text.text.text = content
		self.node_list.title.text.text = Language.Flower.ReceiveFlowerTxt1
	end
end

function FlowerBackView:ClickDaxieHandler()
	-- print("__________________________点击答谢按钮______________________________________")
	self.ctrl = FlowerWGCtrl.Instance:GetFlowerData()
	ChatWGCtrl.Instance:SendSingleChat(self.ctrl.from_uid, Language.Flower.ThankForFlower, CHAT_CONTENT_TYPE.TEXT, true)
	--以后有了配置在改成配置的提醒
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Flower.ReceiveSuc)
	self:Close()
end

function FlowerBackView:ClickSendBackHandler()
	self.ctrl = FlowerWGCtrl.Instance:GetFlowerData()
	-- print("__________________________点击回赠按钮______________________________________")
	FlowerWGCtrl.Instance:OpenSendFlowerView(self.ctrl.from_uid, self.ctrl.from_name, self.ctrl.sex, self.ctrl.prof)
	self:Close()
end

function FlowerBackView:Onopen()
	if self.noremind then
		return
	end

	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function FlowerBackView:OnCloseNoTips()
	self.noremind = not self.noremind
	self.node_list["btn_not_tips_hook"]:CustomSetActive(self.noremind)
end