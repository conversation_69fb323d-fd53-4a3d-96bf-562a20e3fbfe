﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_LocalNotificationWrap
{
	public static void Register(LuaState L)
	{
		L.<PERSON>ginStaticLibs("LocalNotification");
		<PERSON><PERSON>unction("SendNotification", SendNotification);
		<PERSON><PERSON>("SendRepeatingNotification", SendRepeatingNotification);
		<PERSON><PERSON>unction("CancelNotification", CancelNotification);
		<PERSON><PERSON>RegFunction("CancelAllNotifications", CancelAllNotifications);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SendNotification(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			long arg1 = LuaDLL.tolua_checkint64(L, 2);
			string arg2 = ToLua.CheckString(L, 3);
			string arg3 = ToLua.CheckString(L, 4);
			Nirvana.LocalNotification.SendNotification(arg0, arg1, arg2, arg3);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SendRepeatingNotification(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			long arg1 = LuaDLL.tolua_checkint64(L, 2);
			Nirvana.LocalNotification.CalendarUnit arg2 = (Nirvana.LocalNotification.CalendarUnit)ToLua.CheckObject(L, 3, typeof(Nirvana.LocalNotification.CalendarUnit));
			string arg3 = ToLua.CheckString(L, 4);
			string arg4 = ToLua.CheckString(L, 5);
			Nirvana.LocalNotification.SendRepeatingNotification(arg0, arg1, arg2, arg3, arg4);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CancelNotification(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			Nirvana.LocalNotification.CancelNotification(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CancelAllNotifications(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.LocalNotification.CancelAllNotifications();
				return 0;
			}
			else if (count == 1)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				Nirvana.LocalNotification.CancelAllNotifications(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.LocalNotification.CancelAllNotifications");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

