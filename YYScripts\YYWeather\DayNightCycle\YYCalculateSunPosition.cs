﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using System;

public class YYCalculateSunPosition
{
    private const double degreesToRadians = Math.PI / 180.0;
    private const double dayMs = 1000.0 * 60.0 * 60.0 * 24.0;
    private const double j0 = 0.0009;
    private const double j1970 = 2440587.5;
    private const double j2000 = 2451545.0;
    private const double jDiff = (j1970 - j2000);
    public static void CalculateSunPosition(SunInfo sunInfo, float rotateYDegrees)
    {
        // dateTime should already be UTC format
        double d = (sunInfo.DateTime.Subtract(new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Utc)).TotalMilliseconds / dayMs) + jDiff;
        //double d = sunInfo.DateTime.ToOADate() + 2415018.5;
        double e = degreesToRadians * sunInfo.AxisTilt; // obliquity of the Earth
        double m = SolarMeanAnomaly(d);
        double l = EclipticLongitude(m);
        double dec = Declination(e, l, 0);
        double ra = RightAscension(e, l, 0);
        double lw = -degreesToRadians * sunInfo.Longitude;
        double phi = degreesToRadians * sunInfo.Latitude;
        double h = SiderealTime(d, lw) - ra;
        double azimuth = Azimuth(h, phi, dec);
        double altitude = Altitude(h, phi, dec);
        ConvertAzimuthAtltitudeToUnitVector(azimuth, altitude, ref sunInfo.UnitVectorUp);

        sunInfo.UnitVectorUp = Quaternion.AngleAxis(rotateYDegrees, Vector3.up) * sunInfo.UnitVectorUp;
        sunInfo.UnitVectorDown = -sunInfo.UnitVectorUp;
        sunInfo.JulianDays = d;
        sunInfo.Declination = dec;
        sunInfo.RightAscension = ra;
        sunInfo.Azimuth = azimuth;
        sunInfo.Altitude = altitude;
        sunInfo.SolarMeanAnomaly = m;
        sunInfo.EclipticLongitude = l;
        sunInfo.SiderealTime = h;

        double n = JulianCycle(d, lw);
        double ds = ApproxTransit(0, lw, n);
        double jnoon = SolarTransit(ds, m, l);
        double jSunSet = JulianDateForSunAltitude(-0.8 * (Math.PI / 180.0), lw, phi, dec, n, m, l);
        double jSunRise = jnoon - (jSunSet - jnoon);
        double jDusk = JulianDateForSunAltitude(-10.0 * (Math.PI / 180.0), lw, phi, dec, n, m, l);
        double jDawn = jnoon - (jDusk - jnoon);

        try
        {
            sunInfo.Dawn = JulianToDateTime(jDawn).TimeOfDay;
            sunInfo.Dusk = JulianToDateTime(jDusk).TimeOfDay;
            sunInfo.SunRise = JulianToDateTime(jSunRise).TimeOfDay;
            sunInfo.SunSet = JulianToDateTime(jSunSet).TimeOfDay;
        }
        catch
        {
            // don't crash if date time is out of bounds
        }
    }

    private static double SolarMeanAnomaly(double d)
    {
        return degreesToRadians * (357.5291 + 0.98560028 * d);
    }
    private static double EclipticLongitude(double m)
    {
        double c = degreesToRadians * (1.9148 * Math.Sin(m) + 0.02 * Math.Sin(2.0 * m) + 0.0003 * Math.Sin(3.0 * m)); // equation of center
        double p = degreesToRadians * 102.9372; // perihelion of the Earth
        return m + c + p + Math.PI;
    }
    private static double Declination(double e, double l, double b)
    {
        return Math.Asin(Math.Sin(b) * Math.Cos(e) + Math.Cos(b) * Math.Sin(e) * Math.Sin(l));
    }

    private static double RightAscension(double e, double l, double b)
    {
        return Math.Atan2(Math.Sin(l) * Math.Cos(e) - Math.Tan(b) * Math.Sin(e), Math.Cos(l));
    }

    private static double SiderealTime(double d, double lw)
    {
        return degreesToRadians * (280.16 + 360.9856235 * d) - lw;
    }

    private static double Azimuth(double h, double phi, double dec)
    {
        return Math.Atan2(Math.Sin(h), Math.Cos(h) * Math.Sin(phi) - Math.Tan(dec) * Math.Cos(phi));
    }

    private static double Altitude(double h, double phi, double dec)
    {
        return Math.Asin(Math.Sin(phi) * Math.Sin(dec) + Math.Cos(phi) * Math.Cos(dec) * Math.Cos(h));
    }

    public static void ConvertAzimuthAtltitudeToUnitVector(double azimuth, double altitude, ref Vector3 vector)
    {
        vector.y = (float)Math.Sin(altitude);
        float hyp = (float)Math.Cos(altitude);
        vector.z = hyp * (float)Math.Cos(azimuth);
        vector.x = hyp * (float)Math.Sin(azimuth);
    }

    private static double JulianCycle(double d, double lw)
    {
        return Math.Round(d - j0 - lw / (2 * Math.PI));
    }

    private static double ApproxTransit(double Ht, double lw, double n)
    {
        return j0 + (Ht + lw) / (2 * Math.PI) + n;
    }

    private static double SolarTransit(double ds, double M, double L)
    {
        return j2000 + ds + 0.0053 * Math.Sin(M) - 0.0069 * Math.Sin(2 * L);
    }
    private static double HourAngle(double h, double phi, double d)
    {
        return Math.Acos((Math.Sin(h) - Math.Sin(phi) * Math.Sin(d)) / (Math.Cos(phi) * Math.Cos(d)));
    }
    private static double JulianDateForSunAltitude(double h, double lw, double phi, double dec, double n, double M, double L)
    {
        double w = HourAngle(h, phi, dec);
        double a = ApproxTransit(w, lw, n);
        return SolarTransit(a, M, L);
    }

    public static DateTime JulianToDateTime(double julianDate)
    {
        double unixTime = (julianDate - 2440587.5) * 86400;
        DateTime dtDateTime = new DateTime(1970, 1, 1, 0, 0, 0, 0, System.DateTimeKind.Utc);
        dtDateTime = dtDateTime.AddSeconds(unixTime);
        return dtDateTime;
    }


}
