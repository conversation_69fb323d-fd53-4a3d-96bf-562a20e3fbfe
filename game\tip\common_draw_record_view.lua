TipsCommonDrawRecordView = TipsCommonDrawRecordView or BaseClass(SafeBaseView)

function TipsCommonDrawRecordView:__init()
    self.view_name = "TipsCommonDrawRecordView"
    self.view_layer = UiLayer.PopTop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(706, 488)})
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_common_draw_record_panel2")
end

function TipsCommonDrawRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end

    self.title_str = nil
    self.world_list = nil
    self.person_list = nil
end

function TipsCommonDrawRecordView:LoadCallBack()
    self.toggle_index = 1

    self.node_list.title_view_name.text.text = self.title_str or Language.Common.TipsTitleStr
    self.node_list.btn_all.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 1))
    self.node_list.btn_self.toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, 2))
    self.record_list = AsyncListView.New(CommonDrawRecordItem, self.node_list["role_list"])

    self.node_list.btn_all.toggle.isOn = true
end

function TipsCommonDrawRecordView:SetData(world_list, person_list, title_str)
    self.world_list = world_list
    self.person_list = person_list
    self.title_str = title_str
end


function TipsCommonDrawRecordView:OnClickSwitch(state, is_on)
    if is_on and state then
        self.toggle_index = state
        self:FlushRecordList()
    end
end

function TipsCommonDrawRecordView:OnFlush()
    self:FlushRecordList()
end

function TipsCommonDrawRecordView:FlushRecordList()
    local data_list = self.toggle_index == 1 and self.world_list or self.person_list
    local is_show_list = not IsEmptyTable(data_list)
    if is_show_list then
        self.record_list:SetDataList(data_list)
    end
    self.node_list["role_list"]:SetActive(is_show_list)
    self.node_list["no_invite"]:SetActive(not is_show_list)
end


CommonDrawRecordItem = CommonDrawRecordItem or BaseClass(BaseRender)

function CommonDrawRecordItem:OnFlush()
	local index = self:GetIndex()
   	local mark = (index % 2) == 1

	if not self.data then
		return
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)

    if item_cfg == nil then
        print_error("找不到item_id:"..tostring(self.data.item_id))
        return
    end
    local color = ITEM_COLOR[item_cfg.color]
    self.node_list["time"].text.text = os.date("%m-%d  %X", self.data.consume_time)
    self.node_list["root_bg"].image.enabled = mark

    local role_name = self.data.role_name or RoleWGData.Instance:GetAttr("name")
    local str1 = string.format(Language.SiXiangCall.TxtRecord3, role_name)
    local name = string.format(Language.SiXiangCall.TxtRecord1_2, color, item_cfg.name)
    local num = string.format(Language.SiXiangCall.TxtRecord1_3, color, self.data.num or 1)
    self.node_list["desc"].text.text = str1
    self.node_list["txt_btn"].text.text = name
    self.node_list["num"].text.text = num
end