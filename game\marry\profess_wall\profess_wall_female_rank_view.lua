function ProfessView:LoadProfessFeMaleRankViewCallBack()
	self.female_rank_model= RoleModel.New()
	local display_data = {
		parent_node = self.node_list["female_profess_display"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}
	
	self.female_rank_model:SetRenderTexUI3DModel(display_data)

    if not self.female_rank_list then
        self.female_rank_list = AsyncListView.New(ProfessRankRender, self.node_list.female_rank_list)
    end

    self.female_my_rank = ProfessRankRender.New(self.node_list["ph_female_profess_wall"])
	self.female_my_rank:SetIndex(0)

    XUI.AddClickEventListener(self.node_list["btn_female_select_profess"], BindTool.Bind(self.OpenFemaleSelectProfess, self))
end

function ProfessView:ShowProfessFeMaleRankViewCallBack()

end

function ProfessView:ReleaseProfessFeMaleRankViewCallBack()
    if self.female_rank_model then
		self.female_rank_model:DeleteMe()
		self.female_rank_model = nil
	end

    if self.female_rank_list then
        self.female_rank_list:DeleteMe()
        self.female_rank_list = nil
    end

    if self.female_my_rank then
        self.female_my_rank:DeleteMe()
        self.female_my_rank = nil
    end
end

function ProfessView:OnFlushProfessFeMaleRankView()
    local rank_data_list = ProfessWallWGData.Instance:GetProfessRankInfoByRankType(TabIndex.profess_wall_rank_female)
    self.female_rank_list:SetDataList(rank_data_list)
    self:FlushFemaleMyRankInfo()
    self:FlushFemalePersonModel()

    	--刷新时间
	if self.female_time_quest == nil then
		self.female_time_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushFemaleRankNextTime, self), 1)
		self:FlushFemaleRankNextTime()
	end
end

function ProfessView:FlushFemaleMyRankInfo()
	local my_info_data = ProfessWallWGData.Instance:GetSpecialProfessRankInfo()
	self.female_my_rank:SetData(my_info_data[1])
	local score = my_info_data[1].limit_score
	self.node_list["female_integral_desc"].text.text = score > 0 and string.format(Language.ProfessWall.rank_tips, score) or ""
end

function ProfessView:FlushFemalePersonModel()
	local reward_item = ProfessWallWGData.Instance:GetProfessFashionImage()
	if reward_item == nil then
		return
	end
	
	local res_id, _, cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(reward_item.item_id)
	self.female_rank_model:SetRoleResid(res_id)
	--战力

	local fashion_level        = 0
	-- local level_vo             = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(cfg.part_type, cfg.index, fashion_level)
	-- local next_level_vo        = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(cfg.part_type, cfg.index, fashion_level + 1)
	local level_vo             = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(cfg.part_type, cfg.index, fashion_level)
	local next_level_vo        = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(cfg.part_type, cfg.index, fashion_level + 1)
	local level_attribute      = AttributeMgr.GetAttributteByClass(level_vo)
	local next_level_attribute = AttributeMgr.GetAttributteByClass(next_level_vo)
	local add_attribute        = AttributeMgr.LerpAttributeAttr(level_attribute, next_level_attribute)
	add_attribute              = AttributeMgr.AttributeFloorRounding(add_attribute)
	self.node_list["female_fight_power"].text.text       = "+" .. AttributeMgr.GetCapability(add_attribute)
end

function ProfessView:FlushFemaleRankNextTime()
	local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
	if time <= 0 then
		if self.female_time_quest then
			GlobalTimerQuest:CancelQuest(self.female_time_quest)
			self.female_time_quest = nil
		end
	end

	local time_str = ""
	if time > 3600 then
		time_str = TimeUtil.FormatSecond(time, 6)
	else
		time_str = TimeUtil.FormatSecond(time, 2)
	end

	self.node_list["female_time"].text.text = Language.OpenServer.ActRemainTime .. ToColorStr(time_str, COLOR3B.GREEN)
end

function ProfessView:OpenFemaleSelectProfess()
	ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
end