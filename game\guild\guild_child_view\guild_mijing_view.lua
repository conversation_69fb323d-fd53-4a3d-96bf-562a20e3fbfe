-- 仙盟秘境
function GuildView:InitMiJingView()
	local guild_othercfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1]
	self.node_list.acctivetime_sh.text.text = guild_othercfg.time_shouhu
	self.node_list.acctivetime_dt.text.text = guild_othercfg.time_dati
	self.node_list.acctivetime_jd.text.text = guild_othercfg.time_jindi
	local item_shouhu = Split(guild_othercfg.get_shouhu, "|")
	local item_cell = {}
	for i=1,#item_shouhu do
		item_cell[i] = ItemCell.New()
		item_cell[i]:SetInstanceParent(self.node_list["reward_cell_sh_"..i])
		local data = {}
		data.item_id = tonumber(item_shouhu[i])
		item_cell[i]:SetData(data)
	end
	local item_dati = Split(guild_othercfg.get_dati, "|")
	for i=1,#item_dati do
		item_cell[i] = ItemCell.New()
		item_cell[i]:SetInstanceParent(self.node_list["reward_cell_dt_"..i])
		local data = {}
		data.item_id = tonumber(item_dati[i])
		item_cell[i]:SetData(data)
	end
	local item_jindi = Split(guild_othercfg.get_jindi, "|")
	for i=1,#item_dati do
		item_cell[i] = ItemCell.New()
		item_cell[i]:SetInstanceParent(self.node_list["reward_cell_jd_"..i])
		local data = {}
		data.item_id = tonumber(item_jindi[i])
		item_cell[i]:SetData(data)
	end
	self.node_list.btn_open_mijing.button:AddClickListener(BindTool.Bind(self.OnClickInGuildMiJingHandler, self))
	self.node_list.btn_open_jindi.button:AddClickListener(BindTool.Bind(self.OnClickJinDi, self))--禁地
	self.node_list.btn_open_question.button:AddClickListener(BindTool.Bind(self.OnClickQuestion, self))-- 答题
end

function GuildView:DeleteMiJingView()
	if self.guild_mijing_alert then
		self.guild_mijing_alert:DeleteMe()
	end
end

function GuildView:OnFlushMiJingView()
	if false == self:IsLoadedIndex(math.floor(TabIndex.guild_mijing)) then return end
	self:FlushMiJingBtnText()
end

function GuildView:FlushMiJingBtnText()
	if GuildWGData.Instance:IsGuildFbOpen() and GuildWGData.Instance:GetGuildFbIsPass() then
		XUI.SetButtonEnabled(self.node_list.btn_open_mijing, false)
	elseif GuildWGData.Instance:IsGuildFbOpen() then
		XUI.SetButtonEnabled(self.node_list.btn_open_mijing, true)
	else
		XUI.SetButtonEnabled(self.node_list.btn_open_mijing, false)
	end

	if GuildWGData.Instance:IsGuildAnswerOpen() then
		XUI.SetButtonEnabled(self.node_list.btn_open_question, true)
	else
		XUI.SetButtonEnabled(self.node_list.btn_open_question, false)
	end
	if GuildWGData:IsGuildAnswerOpen() then
		XUI.SetButtonEnabled(self.node_list.btn_open_question, true)
	else
		XUI.SetButtonEnabled(self.node_list.btn_open_question, false)
	end
	if GuildWGData.Instance:IsGuildJDOpen() then
		XUI.SetButtonEnabled(self.node_list.btn_open_jindi, true)
	else
		XUI.SetButtonEnabled(self.node_list.btn_open_jindi, false)
	end
end

function GuildView:OnClickInGuildMiJingHandler()
	GuildWGCtrl.Instance:SendGuildFbEnterReq()
	self:Close()
end

function GuildView:OnClickJinDi()
	MountWGCtrl.Instance:SendMountGoonReq(0)
	GuildWGCtrl.Instance:SendJiuSheDaoFBEnter()
	self:Close()
end

function GuildView:OnClickQuestion()
	FunOpen.Instance:OpenViewNameByCfg("guildanswer")
	self:Close()
end