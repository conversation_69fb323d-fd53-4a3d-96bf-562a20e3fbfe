HolyHeavenlyDomainRankView = HolyHeavenlyDomainRankView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainRankView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(922, 650)})
	self:AddViewResource(0, "uis/view/holy_heavenly_domain_ui_prefab", "layout_holy_heavenly_domain_rank")
end

function HolyHeavenlyDomainRankView:OpenCallBack()
	HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.PLAYER_RANK, CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.TODAY_SCORE_RANK)
    HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.SERVER_RANK)
end

function HolyHeavenlyDomainRankView:LoadCallBack()
    if not self.person_rank_list then
        self.person_rank_list = AsyncListView.New(HHDPersonRankListItemRender, self.node_list.ph_person_rank_list)
    end

    if not self.camp_rank_list then
        self.camp_rank_list = AsyncListView.New(HHDCampRankListItemRender, self.node_list.ph_camp_rank_list)
    end

    self:SetTogSelect()
    self.node_list.title_view_name.text.text = Language.HolyHeavenlyDomain.RankViewTitle
	self.node_list.person_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function HolyHeavenlyDomainRankView:OnClickItem(is_on)
    self.node_list.desc_get_reward_tip.text.text = is_on and Language.HolyHeavenlyDomain.PersonRankViewTip or Language.HolyHeavenlyDomain.CampRankViewTip
end

function HolyHeavenlyDomainRankView:ShowIndexCallBack()
    self:SetTogSelect()
end

function HolyHeavenlyDomainRankView:SetTogSelect()
    local is_select_person = self.is_select_person or false
    self.node_list.person_tog.toggle.isOn = is_select_person
    self.node_list.camp_tog.toggle.isOn = not is_select_person

    self.node_list.desc_get_reward_tip.text.text = is_select_person and Language.HolyHeavenlyDomain.PersonRankViewTip or Language.HolyHeavenlyDomain.CampRankViewTip
end

function HolyHeavenlyDomainRankView:ReleaseCallBack()
    if self.person_rank_list then
        self.person_rank_list:DeleteMe()
        self.person_rank_list = nil
    end

    if self.camp_rank_list then
        self.camp_rank_list:DeleteMe()
        self.camp_rank_list = nil
    end
end

function HolyHeavenlyDomainRankView:SetSelectRankType(is_select_person_rank)
    self.is_select_person = is_select_person_rank
end

function HolyHeavenlyDomainRankView:OnFlush()
    local person_rank_list_data, camp_rank_list_data, my_person_rank_data, my_camp_rank_data = HolyHeavenlyDomainWGData.Instance:GetRankDataList()
    self.node_list.not_person_data:CustomSetActive(IsEmptyTable(person_rank_list_data))
    self.node_list.not_camp_data:CustomSetActive(IsEmptyTable(camp_rank_list_data))
    self.person_rank_list:SetDataList(person_rank_list_data)
    self.camp_rank_list:SetDataList(camp_rank_list_data)

    local has_my_person_rank_data = not IsEmptyTable(my_person_rank_data)
    local has_my_camp_rank_data = not IsEmptyTable(my_camp_rank_data)

    local my_rank_str = has_my_person_rank_data and my_person_rank_data.rank or Language.HolyHeavenlyDomain.NotInTheList
    -- local my_score_str = has_my_person_rank_data and my_person_rank_data.rank_score or 0
    local camp_rank_str = has_my_camp_rank_data and my_camp_rank_data.rank or Language.HolyHeavenlyDomain.NotInTheList
    local camp_rank_score = has_my_camp_rank_data and my_camp_rank_data.score or 0

    self.node_list.desc_person_rank.text.text = string.format(Language.HolyHeavenlyDomain.PersonRankName, my_rank_str)

    local my_score_str = HolyHeavenlyDomainWGData.Instance:GetScore()
    self.node_list.desc_person_score.text.text = string.format(Language.HolyHeavenlyDomain.PersonRankScore, my_score_str)
    self.node_list.desc_camp_rank.text.text = string.format(Language.HolyHeavenlyDomain.CampRankName, camp_rank_str)
    self.node_list.desc_camp_score.text.text = string.format(Language.HolyHeavenlyDomain.CampRankScore, camp_rank_score)
end

HHDPersonRankListItemRender = HHDPersonRankListItemRender or BaseClass(BaseRender)

function HHDPersonRankListItemRender:LoadCallBack()
    if not self.rank_reward_list then
        self.rank_reward_list = AsyncListView.New(ItemCell, self.node_list.rank_reward_list)
        self.rank_reward_list:SetStartZeroIndex(true)
    end
end

function HHDPersonRankListItemRender:__delete()
    if self.rank_reward_list then
        self.rank_reward_list:DeleteMe()
        self.rank_reward_list = nil
    end
end

function HHDPersonRankListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local rank_index = self.data.rank
	local is_top_three = rank_index <= 3
    self.node_list.desc_rank:CustomSetActive(not is_top_three)
    self.node_list.img_rank:CustomSetActive(is_top_three)
    self.node_list.desc_name.text.text = self.data.name

	if is_top_three then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..rank_index))
		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_"..rank_index))
	else
        self.node_list.desc_rank.text.text = rank_index
		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_zudui_lbdi"))
	end

    self.node_list.desc_score.text.text = self.data.rank_score

    local reward_cfg = HolyHeavenlyDomainWGData.Instance:GetPersonScoreRankCfgByRank(rank_index)
    local reward_list = reward_cfg and reward_cfg.reward_item or {}
    self.rank_reward_list:SetDataList(reward_list)
end

HHDCampRankListItemRender = HHDCampRankListItemRender or BaseClass(BaseRender)

function HHDCampRankListItemRender:LoadCallBack()
    if not self.rank_reward_list then
        self.rank_reward_list = AsyncListView.New(ItemCell, self.node_list.rank_reward_list)
        self.rank_reward_list:SetStartZeroIndex(true)
    end
end

function HHDCampRankListItemRender:__delete()
    if self.rank_reward_list then
        self.rank_reward_list:DeleteMe()
        self.rank_reward_list = nil
    end
end

function HHDCampRankListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local rank_index = self.data.rank
	local is_top_three = rank_index <= 3
    local rank_str = is_top_three and "" or rank_index
    self.node_list.desc_rank:CustomSetActive(not is_top_three)
    self.node_list.img_rank:CustomSetActive(is_top_three)

	if is_top_three then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..rank_index))
		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_"..rank_index))
	else
		self.node_list.bg.image:LoadSprite(ResPath.GetCommonImages("a2_zudui_lbdi"))
	end

    self.node_list.desc_rank.text.text = rank_str
    self.node_list.desc_score.text.text = self.data.score

    local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByCamp(self.data.camp_seq)
    self.node_list.desc_name.text.text = camp_cfg and camp_cfg.camp_name or ""

    local reward_cfg = HolyHeavenlyDomainWGData.Instance:GetServerScoreRankCfgByRank(rank_index)
    local reward = reward_cfg and reward_cfg.reward_item or {}
    self.rank_reward_list:SetDataList(reward)
end