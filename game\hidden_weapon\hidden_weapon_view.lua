HiddenWeaponView = HiddenWeaponView or BaseClass(SafeBaseView)

function HiddenWeaponView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.is_ruanjia = false -- 是否软甲TAB
    self.default_index = TabIndex.shenji_equip_detail
    self:SetMaskBg(false)

    local common_bundle_name = "uis/view/shenji_anqiruanjia_ui_prefab"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
    self:AddViewResource(0, common_bundle_name, "layout_left_shenji")
    self:AddViewResource(TabIndex.shenji_equip_detail, common_bundle_name, "layout_shenji_equip_detail")
    self:AddViewResource(TabIndex.shenji_equip_upgrade, common_bundle_name, "layout_middle_qianghua")
    self:AddViewResource(TabIndex.shenji_equip_upgrade, common_bundle_name, "layout_right_qianghua")
    self:AddViewResource(TabIndex.shenji_equip_upgrade, common_bundle_name, "layout_qianghua_zhuling")
    self:AddViewResource(TabIndex.shenji_equip_upgrade, common_bundle_name, "layout_qianghua_keming")
    self:AddViewResource(TabIndex.shenji_equip_upgrade, common_bundle_name, "layout_qianghua_awaken")
    self:AddViewResource(TabIndex.shenji_equip_compose, common_bundle_name, "layout_shenji_wudao")
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_top_panel")

    self.detail_show_bag = false
    self.datachange_callback = BindTool.Bind1(self.OnItemDataChange, self)
end

function HiddenWeaponView:ReleaseCallBack()
    if self.datachange_callback then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.datachange_callback)
    end

    if self.hidden_weapon_change_fun then
        HiddenWeaponWGCtrl.Instance:RemoveEquipBagChangeListener(self.hidden_weapon_change_fun)
        self.hidden_weapon_change_fun = nil
    end
    if self.weapon_equiped_change_fun then
        HiddenWeaponWGCtrl.Instance:RemoveEquipGridChangeListener(self.weapon_equiped_change_fun)
        self.weapon_equiped_change_fun = nil
    end

    if self.strengthen_view then
        self.strengthen_view:DeleteMe()
        self.strengthen_view = nil
    end

    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    self.arr_star_ui = {}
    --self.sub_index = 1
    self.left_tab_nodes = nil

    self:ReleaseDetailCallBack()
    self.detail_show_bag = nil

    if self.item_cell_1 then
        self.item_cell_1:DeleteMe()
    end
    self.item_cell_1 = nil

    if self.item_cell_2 then
        self.item_cell_2:DeleteMe()
    end
    self.item_cell_2 = nil

    self:ReleaseDetailCallBack()
    self:ReleaseBuildCallBack()
end

-- 计算要显示的index（可重写）
-- function HiddenWeaponView:CalcShowIndex()
--     return 1
-- end

function HiddenWeaponView:LoadCallBack()
    self:SetupTabContentView()
    self:SetupClickEvent()

    self.node_list.title_view_name.text.text = Language.ViewName.HiddenView
    if not self.weapon_equiped_change_fun then
        self.weapon_equiped_change_fun = BindTool.Bind2(self.RefreshSubView, self, "grid")
        HiddenWeaponWGCtrl.Instance:AddEquipGridChangeListener(self.weapon_equiped_change_fun)
    end
    if not self.hidden_weapon_change_fun then
        self.hidden_weapon_change_fun = BindTool.Bind2(self.RefreshSubView, self, "bag")
        HiddenWeaponWGCtrl.Instance:AddEquipBagChangeListener(self.hidden_weapon_change_fun)
    end
    self:OnWeaponTypeChange(false)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.datachange_callback)

    XUI.AddClickEventListener(self.node_list.btn_rule,BindTool.Bind(self.ClickBtXQRule,self))

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			    show_gold = true, show_bind_gold = true,
			    show_coin = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

    self.item_cell_1 = ItemCell.New(self.node_list["sj_icon_1"])
    self.item_cell_2 = ItemCell.New(self.node_list["sj_icon_2"])

    XUI.AddClickEventListener(self.node_list["nol_1"],BindTool.Bind(self.ClickAddbtn,self, false))
    XUI.AddClickEventListener(self.node_list["nol_2"],BindTool.Bind(self.ClickAddbtn,self, true))
end

function HiddenWeaponView:ClickBtXQRule()
    if self.show_index == TabIndex.shenji_equip_compose then
        RuleTip.Instance:SetContent(Language.ShenJiEquip.BUILD_RULE_TIP_CONTENT, Language.ShenJiEquip.BUILD_RULE_TIP_TITLE,nil,nil,true)
    else
        RuleTip.Instance:SetContent(Language.ShenJiEquip.RULE_TIP_CONTENT, Language.ShenJiEquip.RULE_TIP_TITLE,nil,nil,true)
    end
end

function HiddenWeaponView:OpenCalllBack()
    self:OnWeaponTypeChange(false)
end

function HiddenWeaponView:LoadIndexCallBack(index)
    if index == TabIndex.shenji_equip_detail then
        self:LoadDetailIndexCallBack()
    elseif index == TabIndex.shenji_equip_upgrade then
        if self.strengthen_view then
            self.strengthen_view:LoadIndexCallBack()
        end
    elseif index == TabIndex.shenji_equip_compose then
        self:LoadBuildIndexCallBack()
    end

    self:RefreshMainRedNode()
    self:RefreshLeftInfo()
end

function HiddenWeaponView:ShowIndexCallBack(show_index)
    self:ResetSubView(show_index)
    self:SetupLeftTabStatus()
    self:DoBuildBagTween(show_index ~= TabIndex.shenji_equip_compose)
end

function HiddenWeaponView:OnFlush(param_t, index)
    if param_t == nil or param_t["all"] == nil then
        return
    end
    local param_all = param_t["all"]
    if param_all["weapon_type"] or param_all["sub_view_name"] then
        local weapon_type = param_all["weapon_type"] or param_all["sub_view_name"] or 1
        -- 暗器or灵甲
        self.is_ruanjia = tonumber(weapon_type) == 2
        self:SelectWeaponType(self.is_ruanjia)
    end

    if index == TabIndex.shenji_equip_upgrade then
        local sub_type = param_all["to_ui_name"] or param_all["sub_type"]
        if sub_type and tonumber(sub_type) > 0 then
            if self.strengthen_view then
                self.strengthen_view:OnClickTab(tonumber(sub_type))
            end
        end
    elseif index == TabIndex.shenji_equip_compose then
        self:RefreshRemindQuick()--一键容灵
    end
end

function HiddenWeaponView:RefreshMainRedNode()
    if self.node_list["remind_main_1"] == nil then
        return
    end

    
    --主红点
    local red1 = HiddenWeaponWGData.Instance.remind_manager:IsWeaponTypeRed(1, self.show_index)
    local red2 = HiddenWeaponWGData.Instance.remind_manager:IsWeaponTypeRed(2, self.show_index)
    self.node_list["remind_main_1"]:SetActive(red1)
    self.node_list["remind_main_2"]:SetActive(red2)

    -- 详情红点
    local x0 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_FENJIE)
    local x1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_BetterEquip1)
    local x2 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_BetterEquip2)
    self.node_list["remind_detail"]:SetActive(x0 > 0 or x1 > 0 or x2 > 0)

    -- 修炼红点
    local k1 = HiddenWeaponWGData.Instance.remind_manager:IsStrenghenRed(1)
    local k2 = HiddenWeaponWGData.Instance.remind_manager:IsStrenghenRed(2)
    self.node_list["remind_upgrade"]:SetActive(k1 or k2)

    -- 融灵红点
    local b1 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_BUILD1)
    local b2 = RemindManager.Instance:GetRemind(RemindName.ShenJiEquip_BUILD2)
    self.node_list["remind_build"]:SetActive(b1 > 0 or b2 > 0)
end

function HiddenWeaponView:RefreshLeftInfo()
    local info_data_1 = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(1)
    local info_data_2 = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(2)
    self.node_list["nol_1"]:SetActive(info_data_1.item_id == 0)
    self.node_list["nol_2"]:SetActive(info_data_2.item_id == 0)
    if self.item_cell_1 then
        self.item_cell_1:SetData(info_data_1)
        self.item_cell_1:SetButtonComp(false)
        self.item_cell_1:SetCellBgEnabled(false)
    end

    if self.item_cell_2 then
        self.item_cell_2:SetData(info_data_2)
        self.item_cell_2:SetButtonComp(false)
        self.item_cell_2:SetCellBgEnabled(false)
    end
    --for i = 1, 2 do
        --info_data = HiddenWeaponWGData.Instance:GetSCShenJiEquipGridByType(i)
        -- if info_data.equip then
        --     self.node_list["sj_text_" .. i]:SetActive(false)
        --     --self.node_list["sj_bg_" .. i]:SetActive(true)
        --    -- self.node_list["sj_icon_" .. i]:SetActive(true)
        --     --self.node_list["sj_star_area_" .. i]:SetActive(true)
        --     local bundle, asset = ResPath.GetShenJiImage("a1_sjxt_pzd_" .. info_data.equip.base_color)
        --     --self.node_list["sj_bg_" .. i].image:LoadSprite(bundle, asset, function()
        --     --    self.node_list["sj_bg_" .. i].image:SetNativeSize()
        --     --end)
        --     local bundle1, asset1 = ResPath.GetNoPackPNG("a1_xq_icon_" .. info_data.equip.sj_icon)
        --     -- self.node_list["sj_icon_" .. i].image:LoadSprite(bundle1, asset1, function()
        --     --     self.node_list["sj_icon_" .. i].image:SetNativeSize()
        --     -- end)
        --     -- self:RefreshEquipShowStar(i, info_data.equip.base_star)
        -- else
        --     self.node_list["sj_text_" .. i]:SetActive(true)
        --     --self.node_list["sj_bg_" .. i]:SetActive(false)
        --     --self.node_list["sj_icon_" .. i]:SetActive(false)
        --     --self.node_list["sj_star_area_" .. i]:SetActive(false)
        -- end
   -- end
end

-- function HiddenWeaponView:RefreshEquipShowStar(equiptype, show_num)
    -- for i = 1, 6 do
    --     self.node_list["jin_star_" .. equiptype .. "_" .. i]:SetActive(show_num >= i)
    -- end
    -- if show_num <= 6 then
    --     for i = 1, 6 do
    --         if i <= show_num then
    --             self.node_list["jin_star_" .. equiptype .. "_" .. i].image:LoadSprite(ResPath.GetCommonImages("a1_xing_jin_l"))
    --         end
    --     end
    -- else
    --     for i = 1, 6 do
    --         if i <= show_num - 6 then
    --            self.node_list["jin_star_" .. equiptype .. "_" .. i].image:LoadSprite(ResPath.GetCommonImages("a2_ty_xx_fs"))
    --         else
    --            self.node_list["jin_star_" .. equiptype .. "_" .. i].image:LoadSprite(ResPath.GetCommonImages("a1_xing_jin_l"))
    --         end
    --     end
    -- end
-- end

-- 刷新当前TAB数据，不切换其选择（筛选，3级页签等...）
function HiddenWeaponView:RefreshSubView(pro_type)
    self:RefreshMainRedNode()
    self:RefreshLeftInfo()

    if self.show_index == TabIndex.shenji_equip_detail then
        self:RefreshDetailView()
    elseif self.show_index == TabIndex.shenji_equip_upgrade then
        if self.strengthen_view then
            self.strengthen_view:Refresh(pro_type)
        end
    elseif self.show_index == TabIndex.shenji_equip_compose then
        self:RefreshBuildView()
        self:RefreshRemindQuick()--一键容灵
    end
end

function HiddenWeaponView:CloseCallBack()
    self.open_tween_flag = nil
    if self.strengthen_view and self.strengthen_view.CloseCallBack then
        self.strengthen_view:CloseCallBack()
    end
end

-- 重置当前TAB为初始项
function HiddenWeaponView:ResetSubView(show_index)
    if self.pre_sel_view_index then
        if self.pre_sel_view_index == TabIndex.shenji_equip_upgrade then
            if self.strengthen_view then
                self.strengthen_view:CloseCallBack()
            end
        end
    end

    local bg_name = "a2_sw_beijin1"
    if show_index == TabIndex.shenji_equip_detail then
        self:ShowDetailIndexCallBack()
    elseif show_index == TabIndex.shenji_equip_upgrade then
        self.pre_sel_view_index = TabIndex.shenji_equip_upgrade

        if self.strengthen_view and self.strengthen_view.ShowIndexCallBack then
            self.strengthen_view:ShowIndexCallBack(self:GetWeaponType())
        end
    elseif show_index == TabIndex.shenji_equip_compose then
        self:ShowBuildIndexCallBack()
        bg_name = "a2_sw_beijin2"
    end

    local bundle, asset = ResPath.GetRawImagesPNG(bg_name)
    if self.node_list.RawImage_tongyong then
        self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
        end)
    end
end

function HiddenWeaponView:SetupTabContentView()
    if not self.strengthen_view then
        self.strengthen_view = HiddenWeaponStrengthenView.New(self)
    end
end

function HiddenWeaponView:SetupClickEvent()
    self:SelectWeaponType(self.is_ruanjia)
    -- 默认暗器，选中 isOn==true 为软甲
    XUI.AddClickEventListener(self.node_list["tabbar_begin"], BindTool.Bind(self.OnWeaponTypeChange, self))
    self.left_tab_nodes = {}
    for index = 1, 3 do
        local node_list = U3DNodeList(self.node_list["type_tabbar" .. index].uiname_table, self)
        table.insert(self.left_tab_nodes, node_list)
        XUI.AddClickEventListener(
            self.node_list["type_tabbar" .. index],
            BindTool.Bind(self.OnClickLeftTab, self, index)
        )
    end
end

function HiddenWeaponView:GetWeaponType()
    if self.node_list["tabbar_begin"].toggle.isOn == true then
        return 2
    else
        return 1
    end
end

-- 暗器/软甲切换
function HiddenWeaponView:SelectWeaponType(is_ruanjia)
    local game_object = self.node_list["tabbar_begin"]

    if game_object and game_object.toggle then
        if is_ruanjia then
            game_object.toggle.isOn = true
        else
            game_object.toggle.isOn = false
        end
    end
end

-- 根据暗器/软甲和选中状态，左侧三个页签有不同的图片
function HiddenWeaponView:SetupLeftTabStatus()
    if self.node_list["tabbar_begin"] == nil then
        return
    end
    local weapon_type = self:GetWeaponType()
    local sub_type = self.show_index
    for index, node_list in pairs(self.left_tab_nodes or {}) do
        if sub_type == index then
            node_list["tabbar_selected"]:SetActive(true)
            node_list["tabbar_aq"]:SetActive(false)
        else
            node_list["tabbar_selected"]:SetActive(false)
            node_list["tabbar_aq"]:SetActive(true)
        end
    end

    local is_on = weapon_type == 2
    -- self.node_list["bg_taiji_title_rj_sel"]:SetActive(is_on)
    -- self.node_list["bg_taiji_title_aq_nor"]:SetActive(is_on)
    -- self.node_list["bg_taiji_title_aq_sel"]:SetActive(not is_on)
    -- self.node_list["bg_taiji_title_rj_nor"]:SetActive(not is_on)
end

function HiddenWeaponView:OnWeaponTypeChange(is_on)
    -- 默认暗器，按下选中为软甲
    if not self.node_list["bg_taiji_title_rj_sel"] then
        return
    end

    self:ResetSubView(self.show_index)
    self:SetupLeftTabStatus()
    self:RefreshMainRedNode()
    self:DoViewTabberTween()
    self:RefreshLeftInfo()
end

-- 左侧切换
function HiddenWeaponView:OnClickLeftTab(index, is_on)
    if is_on == nil or is_on == false then
        return
    end

    if index == 3 then
        self:ChangeToIndex(TabIndex.shenji_equip_compose)
    elseif index == 2 then
        self:ChangeToIndex(TabIndex.shenji_equip_upgrade)
    else
        self:ChangeToIndex(TabIndex.shenji_equip_detail)
    end

    self:RefreshMainRedNode()

    self:SetupLeftTabStatus()
end

-- 背包数据发生变化通知
function HiddenWeaponView:OnItemDataChange(
    change_item_id,
    change_item_index,
    change_reason,
    put_reason,
    old_num,
    new_num)
    if self.show_index == TabIndex.shenji_equip_upgrade then
        if self.strengthen_view then
            self.strengthen_view:FlushMaterialNum(change_item_id)
        end
    end
end

function HiddenWeaponView:DoViewTabberTween()
    local is_on = self.node_list["tabbar_begin"].toggle.isOn
    self.detail_change_flag = true
    self.node_list["bg_taiji_title_rj_sel"]:SetActive(is_on)
    self.node_list["bg_taiji_title_aq_sel"]:SetActive(not is_on)
end

function HiddenWeaponView:ClickAddbtn(type)
    self:ChangeToIndex(TabIndex.shenji_equip_detail) -- GuideModuleName.HiddenWeaponView

    if self.node_list.tabbar_begin then
        self.node_list.tabbar_begin.toggle.isOn = type
    end

    if self:IsLoadedIndex(TabIndex.shenji_equip_detail) then
        local weapon_type = self:GetWeaponType()
        local filter_tag = self:GetDetailFilterTag()
        local datas = HiddenWeaponWGData.Instance:GetDataList(weapon_type, filter_tag)
        self:OnClickDetailBag(#datas > 0)
    end
end