MarryFriendView = MarryFriendView or BaseClass(SafeBaseView)

function MarryFriendView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_fb_invite_friend2")
	self.friend_id = 0
end

function MarryFriendView:__delete()

end

function MarryFriendView:ReleaseCallBack()
	if nil ~= self.friend_list_view then
		self.friend_list_view:DeleteMe()
		self.friend_list_view = nil
	end
end

function MarryFriendView:LoadCallBack()
   	self.friend_list_view = AsyncListView.New(FbFriendItemRender, self.node_list["ph_fbfriend_list"])
  	self.friend_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectItemCallBack, self))

	self.node_list["btn_invite_friend"].button:AddClickListener(BindTool.Bind1(self.Invite<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
end

function MarryFriendView:OnFlush()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local friend_data = {}
	for k,v in pairs(SocietyWGData.Instance:GetMarryFriendList()) do
		if v.user_id ~= lover_id then
			table.insert(friend_data, v)
		end
	end
	if self.friend_list_view then
		self.friend_list_view:SetDataList(friend_data)
		self.friend_list_view:SelectIndex(1)
	end
end

function MarryFriendView:SelectItemCallBack(item)
	if not item or not item:GetData() then return end

	local data = item:GetData()
	self.friend_id = data.user_id
end

function MarryFriendView:InviteToFbHandler()
	SocietyWGCtrl.Instance:ITeamInvite(self.friend_id,TEAM_INVITE_TYPE.FRIEND)
end

---------------------------------------------------------------------
------- FbFriendItemRender
FbFriendItemRender = FbFriendItemRender or BaseClass(SocietyBaseRender)
function FbFriendItemRender:__init()
	self.is_choose = true
	if self.node_list["tog_choose"] then
		self.node_list["tog_choose"].toggle:AddValueChangedListener(BindTool.Bind(self.OnChoose, self))
	end
end

function FbFriendItemRender:__delete()
end

function FbFriendItemRender:OnFlush()
	if not self.data then
		print_error("error!!!!")
		return
	end
	self.node_list["lbl_name"].text.text = ToColorStr(self.data.gamename, COLOR3B.DEFAULT)
	self.node_list["label_prof"].text.text = ToColorStr(tostring(Language.Common.ProfName[self.data.sex][self.data.prof]), COLOR3B.RED)

	XUI.UpdateRoleHead(self.node_list["icon"], self.node_list["custom_role_head"], self.data.user_id,self.data.sex,self.data.prof)
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list["dianfen_img"]:SetActive(is_vis)
	self.node_list["TextLevel"].text.text = level
end

function FbFriendItemRender:OnSelectChange(is_select)
	if next(self.node_list) == nil then return end
	if is_select then
		self.node_list.img_choose:SetActive(true)
	else
		self.node_list.img_choose:SetActive(false)
	end
end

function FbFriendItemRender:OnChoose(isOn)
	self.is_choose = isOn
	SocietyWGData.Instance:GetAddfriendList()[self.data.user_id] = self.is_choose
end