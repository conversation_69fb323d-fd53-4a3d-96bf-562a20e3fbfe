﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ExternalAlphaHelperWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(ExternalAlphaHelper), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>("Initialize", Initialize);
		<PERSON><PERSON>ction("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("targetImage", get_targetImage, set_targetImage);
		<PERSON><PERSON>("spriteShader", get_spriteShader, set_spriteShader);
		<PERSON><PERSON>("externalAlphaTex", get_externalAlphaTex, set_externalAlphaTex);
		<PERSON><PERSON>("LightModeTag", get_LightModeTag, null);
		<PERSON><PERSON>("_AlphaTex", get__AlphaTex, set__AlphaTex);
		<PERSON><PERSON>("_EnableExternalAlpha", get__EnableExternalAl<PERSON>, set__EnableExternalAlpha);
		<PERSON><PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Initialize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ExternalAlphaHelper obj = (ExternalAlphaHelper)ToLua.CheckObject<ExternalAlphaHelper>(L, 1);
			obj.Initialize();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_targetImage(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ExternalAlphaHelper obj = (ExternalAlphaHelper)o;
			UnityEngine.UI.RawImage ret = obj.targetImage;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetImage on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_spriteShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ExternalAlphaHelper obj = (ExternalAlphaHelper)o;
			UnityEngine.Shader ret = obj.spriteShader;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_externalAlphaTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ExternalAlphaHelper obj = (ExternalAlphaHelper)o;
			UnityEngine.RenderTexture ret = obj.externalAlphaTex;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index externalAlphaTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LightModeTag(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushstring(L, ExternalAlphaHelper.LightModeTag);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get__AlphaTex(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, ExternalAlphaHelper._AlphaTex);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get__EnableExternalAlpha(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, ExternalAlphaHelper._EnableExternalAlpha);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_targetImage(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ExternalAlphaHelper obj = (ExternalAlphaHelper)o;
			UnityEngine.UI.RawImage arg0 = (UnityEngine.UI.RawImage)ToLua.CheckObject<UnityEngine.UI.RawImage>(L, 2);
			obj.targetImage = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetImage on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_spriteShader(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ExternalAlphaHelper obj = (ExternalAlphaHelper)o;
			UnityEngine.Shader arg0 = (UnityEngine.Shader)ToLua.CheckObject(L, 2, typeof(UnityEngine.Shader));
			obj.spriteShader = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index spriteShader on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_externalAlphaTex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ExternalAlphaHelper obj = (ExternalAlphaHelper)o;
			UnityEngine.RenderTexture arg0 = (UnityEngine.RenderTexture)ToLua.CheckObject<UnityEngine.RenderTexture>(L, 2);
			obj.externalAlphaTex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index externalAlphaTex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set__AlphaTex(IntPtr L)
	{
		try
		{
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			ExternalAlphaHelper._AlphaTex = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set__EnableExternalAlpha(IntPtr L)
	{
		try
		{
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			ExternalAlphaHelper._EnableExternalAlpha = arg0;
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

