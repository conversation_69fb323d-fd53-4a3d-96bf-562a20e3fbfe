CustomizedSetBarrageView = CustomizedSetBarrageView or BaseClass(SafeBaseView)

function CustomizedSetBarrageView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(700, 420)})
	self:AddViewResource(0, "uis/view/customized_rumors_ui_prefab", "layout_custom_set_danmu_view")
end

function CustomizedSetBarrageView:LoadCallBack()
    self.node_list.input_field.input_field.text = ""
    XUI.AddClickEventListener(self.node_list.btn_send, BindTool.Bind(self.OnClicSend, self))
    XUI.AddClickEventListener(self.node_list.btn_random_danmu, BindTool.Bind(self.OnClicRandomDanmu, self))
end

function CustomizedSetBarrageView:OnClicSend()
    local input_field = self.node_list.input_field

    if nil ~= input_field then
        local text = input_field.input_field.text
        if text == "" then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.PlaeseInputDesc)
            return
        end

        if string.len(text) <= 0 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CustomizedRumors.PlaeseInputDesc)
            return
        end

        if ChatFilter.Instance:IsIllegal(text, false) or ChatFilter.IsEmoji(text) then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
            return
        end

        local i, j = string.find(text, "*")
        if i ~= nil and j ~= nil then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
            return
        end

        local len = string.len(text)
        local qukong_text = string.gsub(text, "%s", "")
        local qukong_text_len = string.len(qukong_text)
        --判断输入的名字是否带空格
        if qukong_text_len ~= len then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.IllegalContent)
            return
        end

        CustomizedRumorsWGCtrl.Instance:AddOneTemporaryDanMu(text)
        self:Close()
    end
end

function CustomizedSetBarrageView:OnClicRandomDanmu()
    self.node_list.input_field.input_field.text = CustomizedRumorsWGData.Instance:GetBullScreenBarrageMessageDesc()
end