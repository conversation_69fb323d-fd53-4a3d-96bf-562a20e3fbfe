-- 仙盟红包

Red_Packet_TYPE = {
	SEND_REQ = 1,  -- 发送红包类型
	OTHER_RED = 2, -- 其他红包类型
}

OPEN_VIEE_TYPE = {
	SHOU_CHONG = 1,
	UP_VIP = 2,
	TOUZIJIHUA = 3,
	LEICHONG = 4,
	GUILD_DATI = 5,
}

--需要区分红包按钮
SPECIAL_SEND_TYPE = {
	GUILD_ANSWER = 4,		-- 仙盟答题
	GUILD_ZhuZai = 5,		-- 主宰神殿
	GUILD_MW_Hunter = 6,	-- 魔王猎人
	GUILD_SIGN = 8,			-- 仙盟签到
}

function GuildView:InitRedPacketView()
	if self.red_pocket_scroll == nil then
		self:CreateRedPacketGrid()
	end
	self.qiangzhi_red_packet_shuaxin = true
	GuildWGData.Instance:SetGuildRedFlush(true)
	self.node_list.btn_red_tips.button:AddClickListener(BindTool.Bind(self.OnClickRedPocketTips, self))
	--self.node_list.panel_change.button:AddClickListener(BindTool.Bind(self.OnClickChangePanlel, self))
	self.node_list.sendredpocket.button:AddClickListener(BindTool.Bind(self.OnClickSendRedPocketTips, self))
end

function GuildView:DeleteRedPacketView()
	if nil ~= self.red_pocket_scroll then
		self.red_pocket_scroll:DeleteMe()
		self.red_pocket_scroll  = nil
	end

	if nil ~= self.jilu_list then
		self.jilu_list:DeleteMe()
		self.jilu_list  = nil
	end

end

function GuildView:ShowRedPacketCallBack()
	self.qiangzhi_red_packet_shuaxin = true
end

function GuildView:OnFlushRedPacketView()
	-- local is_show_point = WelfareWGData.Instance:IsShowWelfarePaperRedPoint()
	-- self.node_list.red_paper_panel_tips:SetActive(is_show_point == 1)
	local last_data_list = WelfareWGData.Instance:GetSendRedPaperListInfo()
	table.sort( last_data_list, SortTools.KeyLowerSorter("sort_index", "type") )
	local red_paper_info1 = WelfareWGData.Instance:GetGuildRedpaperAllInfo()
	local red_paper_info = DeepCopy(red_paper_info1)
	local pos = RoleWGData.Instance.role_vo.guild_post

	local temp_list = {}
	for k,v in pairs(last_data_list) do
		local btn_state = v.sort_index == 0
		if v.type == SPECIAL_SEND_TYPE.GUILD_ANSWER then
			btn_state = btn_state and (pos == GUILD_POST.TUANGZHANG or pos == GUILD_POST.JiaMengZhu)
		end
		if btn_state then
			v.red_type = Red_Packet_TYPE.SEND_REQ
			table.insert(temp_list,v)
		end
	end
	for k,v in pairs(red_paper_info) do
		if v.owner_uid > 0 then
			v.red_type = Red_Packet_TYPE.OTHER_RED
			table.insert(temp_list,v)
		end
	end
	local role_uid = RoleWGData.Instance:InCrossGetOriginUid()
	for k,v in pairs(temp_list) do
		v.sort_order = 3
		if v.red_type == Red_Packet_TYPE.SEND_REQ then
			v.sort_order = 4
		elseif v.red_type == Red_Packet_TYPE.OTHER_RED then
			local data_base_cfg = {}
			if v.paper_type == 0 then
				data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(v.paper_level)
			else
				data_base_cfg = WelfareWGData.Instance:GetWelfareGuildRedpaperTypeCfg(v.paper_type,v.paper_level)
			end

			local have_get_count = 0
			for m,n in pairs(v.record_list) do
				if n.uid == role_uid then
					v.sort_order = 2
				end

				if n.uid > 0 then
					have_get_count = have_get_count + 1
				end
			end
			if v.sort_order ~= 2 and data_base_cfg and have_get_count >= data_base_cfg.num then
				v.sort_order = 1
			end
		end
	end
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()


	table.sort( temp_list, SortTools.KeyUpperSorter("sort_order") )

	self.node_list["no_red_hint"]:SetActive(#temp_list <= 0)
	self.red_pocket_scroll:SetDataList(temp_list, 3)
	if IsEmptyTable(person_info) then return end
	--local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
	--local count = data_base_cfg_info.guild_daily_fetch_count_limit - person_info.guild_receive_count
	--local red_paper_other_cfg = WelfareWGData.Instance:GetWelfareCfg()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()   

	local shengyu_num, max_num = WelfareWGData.Instance:GetDailyRedBagNumInfo()
	self.node_list["rich_num_limint"].text.text = string.format(Language.Guild.DayLimintRed, shengyu_num, max_num) 
end

function GuildView:CreateRedPacketGrid()
	local bundle, asset = "uis/view/guild_ui_prefab", "ph_red_item"
	self.red_pocket_scroll = AsyncBaseGrid.New()
	self.red_pocket_scroll:CreateCells({col = 5, cell_count = 100, itemRender = RedPocketGridItemRender,
		list_view = self.node_list["ph_red_scroll"], assetBundle = bundle, assetName = asset, change_cells_num = 1})
	self.red_pocket_scroll:SetStartZeroIndex(false)
end

function GuildView:OnClickRedPocketTips()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.Guild.GuildAfterTips)
		role_tip:SetContent(Language.Guild.GuildTips)
	else
		print_error("GuildView:ClickbtnGuildCK","OnClickBtnMLImageChongTip() can not find the get way!")
	end
end
--主动发送红包
function GuildView:OnClickSendRedPocketTips()
	local cfg = WelfareWGData.Instance:GetWelfareCfg()
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	if role_level < cfg.other_config[1].level_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Welfare.RedPaperLevelLimit,cfg.other_config[1].level_limit) )
		return
	end

	if role_vip_level < cfg.other_config[1].vip_limit then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Welfare.RedPaperVipLimit,cfg.other_config[1].vip_limit))
		return
	end
	if person_info.day_distribute_count >= cfg.other_config[1].distribute_count_limit_per_day then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RedPaperSendOver)
		return
	end

	GuildWGCtrl.Instance:OpenGuildVIPRedView(1)
end

-- function GuildView:OnClickChangePanlel()
-- 	ViewManager.Instance:Open(GuideModuleName.WorldRedPaper)
-- end

--RedPocketGridItemRender
----------------------------------------------------------------------------
RedPocketGridItemRender = RedPocketGridItemRender or BaseClass(BaseRender)
function RedPocketGridItemRender:__init()
end

function RedPocketGridItemRender:__delete()
end
function RedPocketGridItemRender:LoadCallBack()
	self.node_list.record_btn.button:AddClickListener(BindTool.Bind(self.OnClickRecord, self))
	self.node_list.img_red_send.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	self.node_list.btn_open_viewname.button:AddClickListener(BindTool.Bind(self.OnClickOpenView, self))
end

function RedPocketGridItemRender:ReleaseCallBack()

end

function RedPocketGridItemRender:OnFlush()
	if not self.data then return end
	if self.data.red_type == Red_Packet_TYPE.SEND_REQ then
		self.node_list["panel_view_1"]:SetActive(true)
		self.node_list["panel_view_2"]:SetActive(false)
		self:OnFlushViewOne()
	elseif self.data.red_type == Red_Packet_TYPE.OTHER_RED then
		self.node_list["panel_view_1"]:SetActive(false)
		self.node_list["panel_view_2"]:SetActive(true)
		self:OnFlushViewTwo()
	end
end

function RedPocketGridItemRender:OnFlushViewOne()

	local bundle_red, asset_red = ResPath.GetGuildSystemImage("a3_xm_x_hb_1")
	self.node_list["red_bg"].image:LoadSprite(bundle_red, asset_red, function()
		XUI.ImageSetNativeSize(self.node_list["red_bg"])
	end)
	self.node_list.rich_desc_limit.text.text = self.data.descript
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
	self.node_list.lbl_role_name.text.text = mainrole_vo.role_name
end

function RedPocketGridItemRender:OnFlushViewTwo()
	self.node_list.lbl_role_name.text.text = self.data.owner_game_name
	local data_base_cfg = {}
	

	local role_uid = RoleWGData.Instance:InCrossGetOriginUid()
	--是否领过
	local is_get_over = false
	local have_get_count = #self.data.record_list

	for k,v in pairs(self.data.record_list) do
		if v.uid == role_uid then
			is_get_over = true
			break
		end
	end
	local color = is_get_over and GuildColor.TRUE or GuildColor.FALSE
	if self.data.paper_type == 0 then
		local data_base_cfg_1 = WelfareWGData.Instance:GetWelfareCfg().custom_redpaper
		data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(self.data.paper_level)
		self.node_list.rich_desc_limit.text.text = ToColorStr(self.data.str_des, color)

	else
		data_base_cfg = WelfareWGData.Instance:GetWelfareGuildRedpaperTypeCfg(self.data.paper_type,self.data.paper_level)
		if data_base_cfg then
			self.node_list.rich_desc_limit.text.text = ToColorStr(data_base_cfg.descript, color)
		end
	end
	local num = data_base_cfg and data_base_cfg.num or 0
	local is_num_over = have_get_count >= num
	if is_num_over then
		--判断自己是否领取过
		self.node_list.record_btn:SetActive(true)--查看他人手气
		self.node_list.img_red_send:SetActive(false)--开
		--self.node_list.number_paper_group:SetActive(false)--总数量
	else
		self.node_list.record_btn:SetActive(is_get_over)
		self.node_list.img_red_send:SetActive(not is_get_over)
		--self.node_list.number_paper_group:SetActive(not is_get_over)
	end
	self.node_list.yicuoguo:SetActive(not is_get_over and is_num_over)--已错过
	self.node_list.img_red_yilingqu_text:SetActive(is_get_over)--已领取
	local b,a
	local is_enough = false
	local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()  
	if data_base_cfg and data_base_cfg.bind_gold > 0 then
		self.node_list.number_paper.text.text = data_base_cfg.bind_gold
		b,a = ResPath.GetF2CommonIcon("i_xiaohao_bangyu")
		 
		if role_vip >= data_base_cfg_info.double_fetch_vip then
			is_enough = person_info.day_receive_gold_count >= data_base_cfg_info.vip_gold_bind_max
		else
			is_enough = person_info.day_receive_gold_count >= data_base_cfg_info.gold_bind_max
		end

	else
		self.node_list.number_paper.text.text = data_base_cfg and data_base_cfg.silver_ticket or 0
		b,a = ResPath.GetF2CommonIcon("i_xiaohao_yuanbao")
		if role_vip >= data_base_cfg_info.double_fetch_vip then
			is_enough = person_info.day_distribute_ticket_count >= data_base_cfg_info.vip_sliver_ticket_max
		else
			is_enough = person_info.day_distribute_ticket_count >= data_base_cfg_info.sliver_ticket_max
		end
	end
	XUI.SetButtonEnabled(self.node_list.img_red_send,not is_enough)
	-- self.node_list["ticket_image"].image:LoadSprite(b,a,function()
	-- 	XUI.ImageSetNativeSize(self.node_list["ticket_image"])
	-- end)

	local bundle_red, asset_red
	if is_get_over or (not is_get_over and is_num_over) then
		bundle_red, asset_red = ResPath.GetGuildSystemImage("a3_xm_x_hb_2")
	else
		bundle_red, asset_red = ResPath.GetGuildSystemImage("a3_xm_x_hb_1")
	end
	
	self.node_list["red_bg"].image:LoadSprite(bundle_red, asset_red, function()
		XUI.ImageSetNativeSize(self.node_list["red_bg"])
	end)
end

function RedPocketGridItemRender:OnClickLingQu()
	-- local red_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	-- local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()


	-- if person_info.guild_receive_count >= red_cfg.other_config[1].guild_daily_fetch_count_limit then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Welfare.RedPaperGetOver)
	-- 	return
	-- end

	--local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_RECEIVE_GUILD,self.data.info_index)
	self:OnClickRecord()
end
function RedPocketGridItemRender:OnClickRecord()
	GuildWGCtrl.Instance:OpenGuildRedPacketView(self.data,1)
end

function RedPocketGridItemRender:CreateSelectEffect()

end

function RedPocketGridItemRender:OnClickOpenView()
	if self.data then
		if self.data.type == SPECIAL_SEND_TYPE.GUILD_ANSWER then
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_GUILD_SYSTEM_DISTRIBUTE,self.data.info_index)
		else
			WelfareWGCtrl.Instance:OnCSRedpaperOperaReq(REDPAPER_OPERA_TYPE.REDPAPER_OPERA_TYPE_DISTRIBUTE_SYSTEM,self.data.info_index)
		end
		local content = self.data.Chanel_descript .. "{openLink;119}"
		ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, content, CHAT_CONTENT_TYPE.TEXT,nil,nil,true)
	end
end

-------------------------------------------------------------------------------
----------------------------------------------------------------------------