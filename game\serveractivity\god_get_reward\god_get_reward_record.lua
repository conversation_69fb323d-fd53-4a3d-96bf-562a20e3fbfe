GodGetRewardRecord = GodGetRewardRecord or BaseClass(SafeBaseView)

function GodGetRewardRecord:__init()
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(-7, -6), sizeDelta = Vector2(932, 590)})
    self:AddViewResource(0, "uis/view/ts_duobao_ui_prefab", "layout_ts_duobao_record")
	self:SetMaskBg(true, true)
end

function GodGetRewardRecord:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function GodGetRewardRecord:ShowIndexCallBack()
    GodGetRewardWGCtrl.Instance:SendOpera(TS_XUNBAO_OPERA_TYPE.DRAW_RECORD)
    GodGetRewardWGData.Instance:UpdateRecordCount()
end

function GodGetRewardRecord:CloseCalBack()
    GodGetRewardWGData.Instance:UpdateRecordCount()
end

function GodGetRewardRecord:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TSXunBao.TitleName
    self.record_list = AsyncListView.New(GodGetRewardRecordRender, self.node_list["record_list"])
end

function GodGetRewardRecord:OnFlush()
    local data_list = GodGetRewardWGData.Instance:GetRecordInfo()
    local is_empty = IsEmptyTable(data_list)
	print_error("结果数据", data_list)

    if not is_empty then
        local temp_list = {}
        local reward_cfg = nil
        for k,data in pairs(data_list) do
            reward_cfg = GodGetRewardWGData.Instance:GetRewardInfoByRewardID(data.reward_id)
            if reward_cfg then
                if reward_cfg.is_best == 1 then
                    temp_list[k + 200] = data
                else
                    temp_list[k] = data
                end
            end
        end
        data_list = SortTableKey(temp_list, true)
    end

    print_error("结果数据", data_list)
    self.record_list:SetDataList(data_list)
    self.node_list.no_flag:SetActive(is_empty)
end
-------------------------------------------------------------------------------------
GodGetRewardRecordRender = GodGetRewardRecordRender or BaseClass(BaseRender)

function GodGetRewardRecordRender:OnFlush()
    local data = self:GetData()
    local reward = GodGetRewardWGData.Instance:GetRewardInfoByRewardID(data.reward_id)
    local layer_cfg = GodGetRewardWGData.Instance:GetLayerCfgByLayer(data.layer)
    local item_cfg = nil
    if reward and reward.reward_item then
        item_cfg = ItemWGData.Instance:GetItemConfig(reward.reward_item.item_id)
    end
    local index = self:GetIndex()
    local mark = (index % 2) == 0
    self.node_list["root"].image.enabled = mark
    if not item_cfg or not layer_cfg then
        self:SetVisible(false)
        return
    end
    self:SetVisible(true)

    self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(data.timestamp)
    self.node_list["info"].text.text = string.format(Language.TSXunBao.TxtRecord, data.name, item_cfg.name, reward.reward_item.num)
end

