FuBenPanelZhuoGuiTaskView = FuBenPanelZhuoGuiTaskView or BaseClass(SafeBaseView)

function FuBenPanelZhuoGuiTaskView:__init()
	self:AddViewResource(0, "uis/view/zhuogui_scene_ui_prefab", "layout_zhuogui_task")
    self:SetMaskBg(true)
end

function FuBenPanelZhuoGuiTaskView:ReleaseCallBack()
	self.wait_flush_flag = nil
	self.load_task_cell_complete = nil

	if self.task_cell_list then
		for k,v in pairs(self.task_cell_list) do
            v:DeleteMe()
		end

		self.task_cell_list = nil
	end

	if self.one_key_alert then
		self.one_key_alert:DeleteMe()
		self.one_key_alert = nil
	end
end

function FuBenPanelZhuoGuiTaskView:LoadCallBack()
    self.node_list["task_tips"].text.text = Language.ZhuoGuiFuBen.TaskTips
    XUI.AddClickEventListener(self.node_list["btn_one_key"], BindTool.Bind(self.OnClickOneKey, self))

    self.task_cell_list = {}
    local task_list = FuBenPanelWGData.Instance:GetZhuoGuiTaskList()
    local res_async_loader = AllocResAsyncLoader(self, "zhuogui_task_render")
	res_async_loader:Load("uis/view/zhuogui_scene_ui_prefab", "TaskCellRender", nil,
		function(new_obj)
			for i = 1, #task_list do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.node_list["Content"].transform, false)
				local item_render = ZhuoGuiTaskRender.New(obj)
				item_render.parent_view = self
				item_render:SetIndex(i)
				item_render:SetClickCallBack(BindTool.Bind(self.OnClickTaskCell, self))
				self.task_cell_list[i] = item_render
				if i == #task_list then
					self.load_task_cell_complete = true
				end
			end

			if self.load_task_cell_complete and self.wait_flush_flag then
				self:FlushList()
			end
		end)
end

function FuBenPanelZhuoGuiTaskView:ShowIndexCallBack()
	self.need_jump_task = true
end

function FuBenPanelZhuoGuiTaskView:OnClickTaskCell(cell)
	if not cell then
		return
	end

	local is_show = cell.is_click_cur
	local cell_index = cell:GetIndex()
	for k,v in ipairs(self.task_cell_list) do
		if cell_index ~= k
		or (is_show and cell_index == k) then
			v:SetHideShow()
		end
	end

	if not is_show then
		cell:DoShowTween()
	end
end

function FuBenPanelZhuoGuiTaskView:OnFlush()
	self.wait_flush_flag = true
	self:FlushList()

	local num = FuBenPanelWGData.Instance:GetZhuoGuiTaskNoFinishNum()
	XUI.SetButtonEnabled(self.node_list["btn_one_key"], num > 0)
end

function FuBenPanelZhuoGuiTaskView:FlushList()
	if not self.load_task_cell_complete then
		return
	end

	self.wait_flush_flag = nil

	local jump_index
	local task_list = FuBenPanelWGData.Instance:GetZhuoGuiTaskList()
	local remind_flag = false
	for k,v in ipairs(self.task_cell_list) do
		local data = task_list[k]
		v:SetData(data)
		if data and self.need_jump_task then
			local info = FuBenPanelWGData.Instance:GetZhuoGuiTaskInfo(data.task_id)
			if info then
				if info.status == 0 and not jump_index then
					jump_index = k
				elseif info.status == 1 and not remind_flag then
					remind_flag = true
					jump_index = k
				end
			end
		end
	end

	if self.need_jump_task then
		jump_index = jump_index or 1
		local cell = self.task_cell_list[jump_index]
		if cell and not cell.is_click_cur then
			self:OnClickTaskCell(cell)
		end

		self.need_jump_task = nil
	end
end

function FuBenPanelZhuoGuiTaskView:OnClickOneKey()
    local num = FuBenPanelWGData.Instance:GetZhuoGuiTaskNoFinishNum()
    if num <= 0 then
        return
    end

	if nil == self.one_key_alert then
		self.one_key_alert = Alert.New()
	end

	local other_cfg = FuBenPanelWGData.Instance:GetZhuoGuiOtherCfg()
    local total_price = other_cfg.task_need_gold * num

	self.one_key_alert:SetLableString(string.format(Language.ZhuoGuiFuBen.OnKeyTaskDesc, total_price))
	self.one_key_alert:SetOkFunc(function()
		local enough = RoleWGData.Instance:GetIsEnoughUseGold(total_price)
		if enough then
			FuBenPanelWGCtrl.Instance:SendZhuoGuiOperate(GHOST_FB_OPERATE_TYPE.TASK_AUTO_FINISH)
		else
			VipWGCtrl.Instance:OpenTipNoGold()
		end
	end)

	self.one_key_alert:Open()
end





ZhuoGuiTaskRender = ZhuoGuiTaskRender or BaseClass(BaseRender)
function ZhuoGuiTaskRender:LoadCallBack()
	self.is_click_cur = false
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	self.reward_list:SetStartZeroIndex(true)

	XUI.AddClickEventListener(self.node_list["btn_title"], BindTool.Bind(self.OnClickTitle, self))
	XUI.AddClickEventListener(self.node_list["btn_content"], BindTool.Bind(self.OnClickGet, self))
end

function ZhuoGuiTaskRender:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function ZhuoGuiTaskRender:SetClickCallBack(callback)
	self.click_callback = callback
end

function ZhuoGuiTaskRender:OnFlush()
	local info = FuBenPanelWGData.Instance:GetZhuoGuiTaskInfo(self.data.task_id)
	if not info then
		return
	end

	self.reward_list:SetDataList(self.data.reward_item)
	self.node_list["title_text"].text.text = self.data.task_name

	local color = info.progress_num >= self.data.param1 and COLOR3B.GREEN or COLOR3B.RED
	self.node_list["task_desc"].text.text = string.format("%s <color=%s>(%d/%d)</color>", self.data.task_des, color, info.progress_num, self.data.param1)
	self.node_list["remind"]:SetActive(info.status == 1)
	self.node_list["is_get_flag"]:SetActive(info.status == 2)
	XUI.SetGraphicGrey(self.node_list["title_part"], info.status == 2)
end

function ZhuoGuiTaskRender:OnClickTitle()
	if self.click_callback then
		self.click_callback(self)
	end
end

function ZhuoGuiTaskRender:SetHideShow()
	self.is_click_cur = false
	self.node_list["content_root"]:CustomSetActive(false)
end

function ZhuoGuiTaskRender:DoShowTween()
	self.is_click_cur = true
	self.node_list["content_root"]:CustomSetActive(true)
	RectTransform.SetSizeDeltaXY(self.node_list["content_root"].rect, 0, 244)
	self.node_list["content_root"].rect:DOSizeDelta(Vector2(274, 266), 0.4)
end

function ZhuoGuiTaskRender:OnClickGet()
	local info = FuBenPanelWGData.Instance:GetZhuoGuiTaskInfo(self.data.task_id)
	if not info or info.status ~= 1 then
		return
	end

	FuBenPanelWGCtrl.Instance:SendZhuoGuiOperate(GHOST_FB_OPERATE_TYPE.FETCH_TASK_REWARD, self.data.task_id)
end



