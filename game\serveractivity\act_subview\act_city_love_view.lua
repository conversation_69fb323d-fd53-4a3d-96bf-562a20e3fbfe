--全城热恋
ServerActivityTabView = ServerActivityTabView or BaseClass(SafeBaseView)

function ServerActivityTabView:CityLoveLoadCallBack()
	self.city_love_task_list = AsyncListView.New(CityLoveTaskRender,self.node_list["love_city_list"])
    ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY, PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_INFO)

    self.node_list.city_btn_get_baby.button:AddClickListener(BindTool.Bind(self.OnGetCityLoveBaby, self))
    self.city_love_box_list = {}
    local parent_slider = self.node_list.city_love_slider_container
    for i = 1, 5 do
        self.city_love_box_list[i] = {}
        local box = self:GetCityLoveNodeList(parent_slider, "city_box_", i)
        local lingqu_bg = self:GetCityLoveNodeList(box, "lingqu_bg")
        self.city_love_box_list[i].lingqu_bg = lingqu_bg
        self.city_love_box_list[i].num_img = self:GetCityLoveNodeList(box, "num_img")
        self.city_love_box_list[i].red_point = self:GetCityLoveNodeList(box, "red")
        box.button:AddClickListener(BindTool.Bind(self.OpenCityInfoScoreRewardView, self))
    end
    self.node_list.btn_city_love_rule.button:AddClickListener(BindTool.Bind(self.OnClickCityLoveRule, self))
end

function ServerActivityTabView:OnClickCityLoveRule()
    RuleTip.Instance:SetContent(Language.OpenServer.ActCityLoveDes, Language.OpenServer.ActCityLoveTitle)
end

function ServerActivityTabView:OpenCityInfoScoreRewardView()
    ServerActivityWGCtrl.Instance:OpenCityInfoRewardView()
end

function ServerActivityTabView:OnGetCityLoveBaby()
    ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,
	    PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_IMAGE)
end

function ServerActivityTabView:GetCityLoveNodeList(parent, str, index)
    local obj 
    if index then
        obj = parent.transform:Find(str..index).gameObject
    else
        obj = parent.transform:Find(str).gameObject
    end
    local item = U3DObject(obj, obj.transform, self)
    return item
end

function ServerActivityTabView:ReleaseCityLoveCallBack()
	if self.task_list then
		self.task_list:DeleteMe()
		self.task_list = nil
    end

    if self.city_love_model then
		self.city_love_model:DeleteMe()
		self.city_love_model = nil
    end
    if CountDownManager.Instance:HasCountDown("act_city_love_count_down") then
		CountDownManager.Instance:RemoveCountDown("act_city_love_count_down")
	end

    if self.city_love_task_list then
        self.city_love_task_list:DeleteMe()
        self.city_love_task_list = nil
    end

    self.ache_city_love_res = nil
end

function ServerActivityTabView:QCRLShowIndexCallBack()
    self:DoQCRLAnim()
end


function ServerActivityTabView:FlushCityLoveStarPanel()
    local _, loving_city_process_reward = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianTaskCfg()
    local server_info = ActivePerfertQingrenWGData.Instance:GetServerLoveCityInfo()
    local need_process = ActivePerfertQingrenWGData.Instance:GetCurLoverProgress(loving_city_process_reward, server_info.total_process)
	self.node_list.city_love_slider_img.slider.value = need_process
    local num_flag = bit:d2b(server_info.process_reward_flag)
    for k, v in ipairs(self.city_love_box_list) do
        v.num_img.text.text = loving_city_process_reward[k].need_process
        local is_get = num_flag[32 - k] == 0
        if is_get then --领取过了
            if server_info.total_process >= loving_city_process_reward[k].need_process then
                v.red_point:SetActive(true)
            else
                v.red_point:SetActive(false)
                v.lingqu_bg:SetActive(false)
            end
        else
            v.red_point:SetActive(false)
            v.lingqu_bg:SetActive(true)
        end
    end

    self.node_list.sorce_value.text.text = server_info.total_process
end

function ServerActivityTabView:FlushCityLoveInfo()
    local love_city_list_info = ActivePerfertQingrenWGData.Instance:GetLoveCityListInfo()
    self.city_love_task_list:SetDataList(love_city_list_info)
    self:FlushCityLoveBaseInfo()
    self:FlushCityLoveStarPanel()
    self:FlushCityLoveModel()
    self:UpdateCityLoveTimeShow()
end

function ServerActivityTabView:FlushCityLoveBaseInfo()
    local city_info_cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
    
    local server_info = ActivePerfertQingrenWGData.Instance:GetServerLoveCityInfo()

    local data1 = {}
    
    data1.item_id = city_info_cfg.loving_city_image_item.item_id

    local cap_value = ItemShowWGData.Instance.CalculateCapability(data1.item_id, nil)
    self.node_list.city_love_cap_value.text.text = cap_value
    
    local total_score = server_info.total_process
    local need_score = city_info_cfg.loving_city_convert_image_progress_value
    self.node_list.city_love_progress.text.text = string.format(Language.Activity.ActivityProcess, total_score, need_score)

    self.node_list.desc.text.text = string.format("<color=#FFE4D000>空格空</color>%s", Language.Activity.PerfertLoverDes_1)
	local num_flag = bit:d2b(server_info.process_reward_flag)
	self.node_list.city_info_get_remind:SetActive(total_score >= need_score and num_flag[32] == 0)
    self.node_list.city_btn_get_baby:SetActive(num_flag[32] == 0)
    self.node_list.city_baby_had_get:SetActive(num_flag[32] == 1)
    --self.node_list.city_info_get_effect:SetActive(total_score >= need_score and num_flag[32] == 0)
    XUI.SetGraphicGrey(self.node_list.city_btn_get_baby, total_score < need_score and num_flag[32] == 0)
end


function ServerActivityTabView:FlushCityLoveModel()
    if not self.city_love_model then
        self.city_love_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["citylove_model_pos"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.city_love_model:SetRenderTexUI3DModel(display_data)
        -- self.city_love_model:SetUI3DModel(self.node_list["citylove_model_pos"].transform, self.node_list["citylove_model_pos"].event_trigger_listener,
        --  1, false, MODEL_CAMERA_TYPE.BASE)
    end
    local cfg = ActivePerfertQingrenWGData.Instance:GetQuanChengReLianCfgOther()
    if cfg.loving_city_resource_id and self.ache_city_love_res ~= cfg.loving_city_resource_id then
        local res_id = cfg.loving_city_resource_id
        self.ache_city_love_res = res_id
        local bundle, asset = ResPath.GetHaiZiModel(res_id)
        self.city_love_model:SetMainAsset(bundle, asset,function()
            self.city_love_model:PlaySoulAction()
        end)
    end
end

function ServerActivityTabView:UpdateCityLoveTimeShow()
	local activity_id = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(activity_id)
    local time = ActivityWGData.Instance:GetActivityResidueTime(activity_id)
	if nil == activity_info then
		return
	end
    self:UpdateCityLoveNextTime(TimeWGCtrl.Instance:GetServerTime(), activity_info.next_time)
    if activity_info.next_time > TimeWGCtrl.Instance:GetServerTime() then
	    CountDownManager.Instance:AddCountDown("act_city_love_count_down", BindTool.Bind1(self.UpdateCityLoveNextTime, self), BindTool.Bind1(self.CompleteCityLoveNextTime, self), activity_info.next_time, nil, 1)
    else
        self:CompleteCityLoveNextTime()
    end
end

function ServerActivityTabView:CompleteCityLoveNextTime()
    self:SetTabVisible()
end

function ServerActivityTabView:UpdateCityLoveNextTime(elapse_time, total_time)
	local have_total_time = total_time - elapse_time
	local str = ""
    local time_tab = TimeUtil.Format2TableDHM(have_total_time)
    if time_tab.day > 0 then
        str = string.format(Language.Common.TimeStr, time_tab.day, time_tab.hour, time_tab.min)
    else
        if time_tab.hour > 0 then
            str = string.format(Language.Common.TimeStr1, time_tab.hour, time_tab.min)
        else
            str = string.format(Language.Common.TimeStr6, time_tab.min)
        end
    end
    str = ToColorStr(str, COLOR3B.GREEN)
    self.node_list.city_love_count_down.text.text = string.format(Language.Activity.ActivityEndTimeDesc_4,str)--"已发奖，距离活动结束："..str	
end

function ServerActivityTabView:DoQCRLAnim()
    local tween_info = UITween_CONSTS.ServerActivityTab
    UITween.FakeHideShow(self.node_list["city_love_root"])
    UITween.AlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["city_love_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)

    self:DoQCRLCellsAnim()
end

function ServerActivityTabView:DoQCRLCellsAnim()
    local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender
    self.node_list["love_city_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["love_city_list"]:SetActive(true)
        local list = self.city_love_task_list:GetAllItems()
        local sort_list = ServerActivityWGData.Instance:GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyQCRLItemAnim(count)
        end
    end, tween_info.DelayDoTime, "QCRL_Cell_Tween")
end

--=======================================================================================================

--全城热恋任务列表
CityLoveTaskRender = CityLoveTaskRender or BaseClass(BaseRender)

function CityLoveTaskRender:__init()

end

function CityLoveTaskRender:__delete()
    if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
end

function CityLoveTaskRender:LoadCallBack()
	self.cell_list = {}
	for i = 1, 6 do
		self.cell_list[i] = ItemCell.New(self.node_list["cell_list"])
	end
	XUI.AddClickEventListener(self.node_list["btn_task_lingqu"], BindTool.Bind(self.GetRewardOrGoAct, self))
end

function CityLoveTaskRender:OnFlush()
	if nil == self.data then
		return
    end
	self.node_list.img_ylq:SetActive(self.data.sort_index == 2)
    self.node_list.image_redmind:SetActive(self.data.sort_index == 0)
    --self.node_list.can_get_effect:SetActive(self.data.sort_index == 0)
	self.node_list["btn_task_lingqu"]:SetActive(self.data.sort_index ~= 2)
	local num_1 = self.data.process >= self.data.task_process and self.data.task_process or self.data.process
	local is_show_str = self.data.process >= self.data.task_process and string.format(Language.Activity.ActivityProcess1,num_1) or string.format(Language.Activity.ActivityProcess2,num_1)
	--self.node_list.task_name.text.text = self.data.task_name
	self.node_list.task_des_text.text.text = string.format(self.data.task_desc, is_show_str)
    self.node_list.btn_txt.text.text = self.data.sort_index == 0 and Language.OpenServer.Get or Language.OpenServer.Goto
	-- for i = 1, 6 do
	-- 	self.node_list["heart_"..i]:SetActive(i <= self.data.reward_process)
    -- end
    local cell_count = 0
    for i = 1, 6 do
        if self.data.reward_show[i - 1] then
            self.cell_list[i]:SetData(self.data.reward_show[i - 1])
            self.cell_list[i]:SetVisible(true)
            cell_count = cell_count + 1
        else
            self.cell_list[i]:SetVisible(false)
        end
    end
    self.node_list.scroll_rect.scroll_rect.enabled = cell_count > 2
    local width = self.node_list.scroll_rect.rect.sizeDelta.x
    self.node_list.cell_list.rect.anchoredPosition = Vector2(-width/2, 0)
end

function CityLoveTaskRender:GetRewardOrGoAct()
	if self.data.sort_index == 0 then
		ServerActivityWGCtrl.Instance:SendActLoverReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY,
			PERFERT_QINGREN_ENUM.RA_ACTIVE_LOVING_CITY_OP_TYPE_FETCH_TASK_REWARD,self.data.task_id)
	else
		FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
	end
end

function CityLoveTaskRender:PalyQCRLItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.ServerActivityTab.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["tween_root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "qcrl_item_" .. wait_index)
end
