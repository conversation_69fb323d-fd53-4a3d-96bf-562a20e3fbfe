BTRComposeItemRender = BTRComposeItemRender or BaseClass(BaseRender)
function BTRComposeItemRender:LoadCallBack()
    if nil == self.compose_item then
        self.compose_item = ItemCell.New(self.node_list.item_pos)
        self.compose_item:SetIsShowTips(false)
    end

    self:CleanTween()
	local tween_time = 0.8
    local node = self.node_list["select_img"]
	if node then
        RectTransform.SetLocalScale(node.rect, 1)
        self.select_tweener = node.transform:DOScale(1.1, tween_time)
        self.select_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.select_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function BTRComposeItemRender:CleanTween()
    if self.select_tweener then
        self.select_tweener:Kill()
        self.select_tweener = nil
    end
end

function BTRComposeItemRender:__delete()
    self:CleanTween()

    if nil ~= self.compose_item then
        self.compose_item:DeleteMe()
        self.compose_item = nil
    end
end

function BTRComposeItemRender:OnFlush()
    if IsEmptyTable(self.data) then
    	self.view:SetActive(false)
        return
    end

    self.compose_item:SetData(self.data)
    self.node_list["select_img"].image.enabled = self.data.is_cur
    self.view:SetActive(true)
end
