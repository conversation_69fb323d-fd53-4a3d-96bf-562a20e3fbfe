XiuWeiWGData = XiuWeiWGData or BaseClass()
XiuWeiWGData.TaskType = {
    XiuWeiLevel = 1,                    -- 提升修为等级到param1级
    TowerLayer = 2,                     -- 爬塔本达到param1层
    ActiveFasion = 3,                   -- 激活param1时装类型param2索引
    RoleLevel = 4,                      -- 人物等级达到param1
    RoleCapability = 5,                 -- 战力达到param1
}

XiuWeiWGData.TaskState = {
    None = 0,           -- 0默认
    CanFetch = 1,       -- 1可领取
    Complete = 2,       -- 2完成
}

function XiuWeiWGData:__init()
	if XiuWeiWGData.Instance then
		print_error("[XiuWeiWGData] Attempt to create singleton twice!")
		return
	end

	XiuWeiWGData.Instance = self

	local xiuwei_auto = ConfigManager.Instance:GetAutoConfig("xiuwei_auto")
    self.xiuwei_task_cfg = ListToMap(xiuwei_auto.task, "stage", "seq")
    self.xiuwei_stage_cfg = ListToMap(xiuwei_auto.stage, "client_stage", "stage")
    self.level_stage_list_cfg = ListToMap(xiuwei_auto.level, "stage", "level")

    self.task_data_list = {}
end

function XiuWeiWGData:__delete()
    XiuWeiWGData.Instance = nil
end

----------------------------协议相关start-----------------------------
function XiuWeiWGData:SetAllTaskData(protocol)
    self.task_data_list = protocol.task_data_list
end

function XiuWeiWGData:SetSingleTaskData(protocol)
    self.task_data_list[protocol.change_seq] = protocol.change_data
end

function XiuWeiWGData:GetTaskDataBySeq(seq)
    return self.task_data_list[seq]
end

function XiuWeiWGData:HasCanFetchTaskReward()
    for k, v in pairs(self.task_data_list) do
        if v.status == XiuWeiWGData.TaskState.CanFetch then
            return true
        end
    end

    return false
end
-----------------------------协议相关end------------------------------

function XiuWeiWGData:GetTaskCfg()
    local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
    return self.xiuwei_task_cfg[cur_stage]
end

function XiuWeiWGData:GetTaskCfgBySeq(seq)
    local cur_stage = CultivationWGData.Instance:GetXiuWeiState()
    return (self.xiuwei_task_cfg[cur_stage] or {})[seq]
end

function XiuWeiWGData:GetXiuWeiClientStageCfg(client_stage)
    return self.xiuwei_stage_cfg[client_stage]
end

function XiuWeiWGData:GetShowSmallStageList(client_stage)
    local show_list = {}
    local cfg = self.xiuwei_stage_cfg[client_stage]
    if cfg then
        for k, v in pairs(cfg) do
            table.insert(show_list, v)
        end
        table.sort(show_list, SortTools.KeyLowerSorter("stage"))
    end

    return show_list
end

function XiuWeiWGData:GetLevelStageShowList(stage)
	local show_list = {}
	stage = stage or CultivationWGData.Instance:GetXiuWeiState()
	local cfg = self.level_stage_list_cfg[stage]
	if cfg then
		for k, v in ipairs(cfg) do
			table.insert(show_list, v)
		end
	end

	return show_list
end

function XiuWeiWGData:IsCompleteAllLevelCellFlag()
    local stage = CultivationWGData.Instance:GetXiuWeiState()
    local cfg = self.level_stage_list_cfg[stage]
    local is_comple = true
    local cur_level = CultivationWGData.Instance:GetXiuWeiLevel()

	if cfg then
		for k, v in ipairs(cfg) do
			if v.level > cur_level then
                is_comple = false
                break
            end
		end
    else
        is_comple = false
	end

    return is_comple
end

function XiuWeiWGData:GetCanUpLevelRootId()
    local data_list = self:GetLevelStageShowList()

    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if self:GetSingleLevelIsCanUp(v.stage, v.level) then 
                return k
            end
        end
    end
end

function XiuWeiWGData:IsCanShowTuPoTip()
    local stage_complete = self:IsCompleteAllLevelCellFlag()
    local is_complete_all_task = CultivationWGData.Instance:IsCompleteAllTask()
    return stage_complete and not is_complete_all_task
end

function XiuWeiWGData:GetLevelStageCfg(stage, level)
	stage = stage or CultivationWGData.Instance:GetXiuWeiState()
    level = level or CultivationWGData.Instance:GetXiuWeiLevel()
	return (self.level_stage_list_cfg[stage] or {})[level]
end

function XiuWeiWGData:GetSingleLevelIsCanUp(stage, level)
    local cur_level = CultivationWGData.Instance:GetXiuWeiLevel()
    local next_xiuwei_level_cfg = XiuWeiWGData.Instance:GetLevelStageCfg(stage, cur_level + 1)
    if not next_xiuwei_level_cfg then
        return false, Language.Cultivation.IsMaxLevel
    end

    if (cur_level + 1) ~= level then
        return false
    end

	local can_break = CultivationWGData.Instance:IsCanBreak()
    if can_break then
        return false
    end

	local cur_xiuwei_level_cfg = XiuWeiWGData.Instance:GetLevelStageCfg(stage, level)
	local cur_exp = CultivationWGData.Instance:GetXiuWeiExp()
    local need_exp = cur_xiuwei_level_cfg and cur_xiuwei_level_cfg.need_exp or 0
    if cur_exp < need_exp then
        return false, Language.Cultivation.JudgmentReadyTips
    end

    return true
end

function XiuWeiWGData.MergeAttrList(attr_list1, attr_list2)
    local function new_data(cfg_data1, cfg_data2)
        local data = {}
        data.attr_str = cfg_data1.attr_str
        data.attr_value = (cfg_data1.attr_value or 0) + (cfg_data2 and cfg_data2.attr_value or 0)
        data.attr_next_value = (cfg_data1.attr_next_value or 0) + (cfg_data2 and cfg_data2.attr_next_value or 0)
        data.add_value = (cfg_data1.add_value or 0) + (cfg_data2 and cfg_data2.add_value or 0)
        data.attr_sort = cfg_data1.attr_sort
        return data
    end

    local new_attr_list = {}
	for _, data in pairs(attr_list1) do
        table.insert(new_attr_list, new_data(data))
	end

    for _, data_1 in pairs(attr_list2) do
        for k, data_2 in pairs(new_attr_list) do
            local count = 1
            if data_1.attr_str == data_2.attr_str then
                new_attr_list[k] = new_data(data_1, data_2)
                break
            else
                count = count + 1
                if count == #attr_list2 then
                    table.insert(new_attr_list, new_data(data_1))
                end
            end
        end
    end

	return new_attr_list
end