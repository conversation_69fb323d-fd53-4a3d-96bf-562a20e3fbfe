DuckRaceWGData = DuckRaceWGData or  BaseClass()

DuckRaceWGData.CROSS_RACING_DUCK_INFO_COUNT = 3  	-- 鸭子数目
DuckRaceWGData.CROSS_RACING_ROUND_COUNT = 3  		-- 轮次数目

function DuckRaceWGData:__init()
	if DuckRaceWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[DuckRaceWGData] attempt to create singleton twice!")
		return
	end
	DuckRaceWGData.Instance = self

	self:InitCfg()
	self:InitInfo()

	RemindManager.Instance:Register(RemindName.DuckRaceFetchRemind, BindTool.Bind(self.FetchRemind, self))
end

function DuckRaceWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.DuckRaceFetchRemind)
	DuckRaceWGData.Instance = nil
end

------------------------------------------------------------- 配置信息 --------------------------------------------------------
-- 初始化配置表
function DuckRaceWGData:InitCfg()
	self.duck_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("crossracing_auto").duck, "id")
	self.duck_trait_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("crossracing_auto").duck_trait, "duck_index")
	self.duck_trait_cfg_2 = ListToMap(ConfigManager.Instance:GetAutoConfig("crossracing_auto").duck_trait, "monster_id")
	self.barrage_cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("crossracing_auto").barrage, "op_type", "op_res")
	self.event_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("crossracing_auto").rand_event, "event_id")
	self.shortcut_event_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("crossracing_auto").shortcut_event, "event_id")
end

-- 获得鸭子配置
function DuckRaceWGData:GetDuckCfg(...)
	return CheckList(self.duck_cfg, ...)
end

-- 根据鸭子index获得鸭子性格配置
function DuckRaceWGData:GetDuckTraitCfg(duck_index)
	return self.duck_trait_cfg[duck_index]
end

-- 根据鸭子index获得鸭子怪物id
function DuckRaceWGData:GetDuckMonsterId(duck_index)
	return self:GetDuckTraitCfg(duck_index).monster_id

end

-- 小鸭疾走其他配置
function DuckRaceWGData:GetOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("crossracing_auto").other[1]
end

-- 获取弹幕、对话配置
function DuckRaceWGData:GetBarrageCfg(op_type, op_res)
	return self.barrage_cfg[op_type][op_res]
end

-- 获取弹幕描述, op_type：1-鼓舞， 2-干扰, op_res: ==0-失败， >=1-成功
function DuckRaceWGData:GetBarrageDesc(op_type, op_res, role_name, duck_index)
	local cfg_list = self:GetBarrageCfg(op_type, op_res)
	local cfg = cfg_list[math.random(#cfg_list)]
	local desc = cfg.desc
	local monster_id = self:GetDuckMonsterId(duck_index)
	local monster_cfg = BossWGData.Instance:GetMonsterInfo(monster_id)
	desc = string.gsub(desc, "<duck_index>", (duck_index + 1) .. Language.DuckRace.DuckIndexDesc)
	desc = string.gsub(desc, "<duck_name>", monster_cfg.name)
	desc = string.format(desc, role_name)
	return desc
end

-- 获得鸭子对话内容, op_type：1-鼓舞， 2-干扰, op_res: ==0-失败， >=1-成功
function DuckRaceWGData:GetDuckDialog(op_type, op_res, duck_index)
	local cfg_list = self:GetBarrageCfg(op_type, op_res)
	local cfg = cfg_list[math.random(#cfg_list)]
	local dialog_content = cfg.dialog_content
	local monster_id = self:GetDuckMonsterId(duck_index)
	local monster_cfg = BossWGData.Instance:GetMonsterInfo(monster_id)
	dialog_content = string.gsub(dialog_content, "<duck_index>", (duck_index + 1) .. Language.DuckRace.DuckIndexDesc)
	dialog_content = string.gsub(dialog_content, "<duck_name>", monster_cfg.name)
	return dialog_content
end

-- 获得鸭子气泡内容, op_type：1-鼓舞， 2-干扰, op_res: ==0-失败， >=1-成功
function DuckRaceWGData:GetDuckBubble(op_type, op_res, duck_index)
	local cfg_list = self:GetBarrageCfg(op_type, op_res)
	local cfg = cfg_list[math.random(#cfg_list)]
	local duck_qipao = cfg.duck_qipao
	return duck_qipao
end

-- 获取赛跑起点
function DuckRaceWGData:GetRaceStartPoint()
	local cfg = ConfigManager.Instance:GetAutoConfig("crossracing_auto").run_path[1]
	local pos_str = Split(cfg.run_path_points, "|")
	local pos = Split(pos_str[1], ",")
	return tonumber(pos[1]), tonumber(pos[2])
end

-- 判断是否是鸭子怪物
function DuckRaceWGData:IsDuckRaceMonster(monster_id)
	return self.duck_trait_cfg_2[monster_id] ~= nil
end

-- 根据怪物id获得鸭子性格配置
function DuckRaceWGData:GetDuckTraitCfgByMonsterId(monster_id)
	return self.duck_trait_cfg_2[monster_id]
end

-- 获取事件配置
function DuckRaceWGData:GetEventCfgById(event_id)
	return self.event_cfg[event_id]
end

-- 获得抄近路事件配置
function DuckRaceWGData:GetShortcutEventCfgById(event_id)
	return self.shortcut_event_cfg[event_id]
end
------------------------------------------------------------- 配置信息End -----------------------------------------------------

------------------------------------------------------------- 协议信息 --------------------------------------------------------
-- 初始化协议信息
function DuckRaceWGData:InitInfo()
	-- 轮次信息
	self.round = 1 												-- 当前第几轮，从1开始
	self.round_state = DUCK_RACE_ROUND_STATE.BET 				-- 当前轮次的状态（下注、比赛中、结算）
	self.next_state_timestamp = 0 								-- 进入下一个状态的时间
	self.is_game_over = false 									-- 是否三轮都已经结束了

	-- 玩家信息
	self.play_coin = 0 											-- 当前玩家的应援币
	self.has_fetched_play_coin = false 							-- 是否领取了应援币
	self.bet_duck_count_list = {}								-- 主角每轮对每个鸭子的下注次数
	for round = 0, DuckRaceWGData.CROSS_RACING_ROUND_COUNT do 	-- 第0轮没有意义，放这里容错
		self.bet_duck_count_list[round] = {}
		for duck_index = 0, DuckRaceWGData.CROSS_RACING_DUCK_INFO_COUNT - 1 do
			self.bet_duck_count_list[round][duck_index] = 0
		end
	end

	-- 玩家每轮获得的奖金
	self.reward_coin = {}
	for round = 0, DuckRaceWGData.CROSS_RACING_ROUND_COUNT do 	-- 第0轮没有意义，放这里容错
		self.reward_coin[round] = 0
	end

	self.inspire_count = 0										-- 当前鼓舞次数
	self.disturb_count = 0										-- 当前干扰次数
	self.last_inspire_duck_timestamp = 0
	self.last_disturb_duck_timestamp = 0

	-- 鸭子信息，下标是鸭子index，从0开始
	self.duck_info_list = {}

	-- 结算信息，下标是轮次，从1开始
	self.winner_duck_list = {}
	-- 下一轮开始的时间戳
	self.next_round_begin_time = 0 		

	-- 比赛实况	
	self.duck_progress_list = {}
	for duck_index = 0, DuckRaceWGData.CROSS_RACING_DUCK_INFO_COUNT - 1 do
		self.duck_progress_list[duck_index] = 0
	end
end

-- 设置当前轮次信息
function DuckRaceWGData:SetRoundInfo(protocol)
	self.round = protocol.round
	self.round_state = protocol.round_state
	self.next_state_timestamp = protocol.next_state_timestamp
	self.is_game_over = protocol.is_game_over
end

-- 获得当前轮次
function DuckRaceWGData:GetCurRound()
	return self.round
end

-- 三轮是否都结束了
function DuckRaceWGData:GetIsGameOver()
	return self.is_game_over
end

-- 获得当前轮次的状态
function DuckRaceWGData:GetCurRoundState()
	return self.round_state
end

-- 设置玩家信息
function DuckRaceWGData:SetPlayerInfo(protocol)
	self.play_coin = protocol.play_coin
	self.has_fetched_play_coin = protocol.has_fetched_play_coin > 0
	self.bet_duck_count_list = protocol.bet_duck_count
	self.reward_coin = protocol.reward_coin
	self.inspire_count = protocol.inspire_count										-- 当前鼓舞次数
	self.disturb_count = protocol.disturb_count										-- 当前干扰次数
	self.last_inspire_duck_timestamp = protocol.last_inspire_duck_timestamp 		-- 上次鼓舞鸭子时间戳
	self.last_disturb_duck_timestamp = protocol.last_disturb_duck_timestamp 		-- 上次干扰鸭子时间戳
end

-- 获得玩家应援币
function DuckRaceWGData:GetPlayCoin()
	return self.play_coin
end

-- 玩家是否领取了应援币
function DuckRaceWGData:GetHasFetchedPlayCoin()
	return self.has_fetched_play_coin
end

-- 是否可领取应援币
function DuckRaceWGData:GetCanFetchPlayCoin()
	if not self:GetHasFetchedPlayCoin() and self:GetCurRoundState() == DUCK_RACE_ROUND_STATE.BET then
		return true
	end
	return false
end

-- 获得本人对所有鸭子的下注数目信息
function DuckRaceWGData:GetAllSlefBetCount()
	return self.bet_duck_count_list
end

-- 获得对应轮次角色获得的金币奖励
function DuckRaceWGData:GetRoundRewardCoin(round)
	return self.reward_coin[round]
end

-- 获得对应轮次本人对所有鸭子的下注数目信息
function DuckRaceWGData:GetCurRoundAllSlefBetCount(round)
	round = round or self:GetCurRound()
	return self:GetAllSlefBetCount()[round]
end

-- 获得本人对所给鸭子的下注数目(round 为nil的时候取当前轮的)
function DuckRaceWGData:GetMyBetCountByDuckIndex(duck_index, round)
	return self:GetCurRoundAllSlefBetCount(round)[duck_index]
end

-- 获得当前轮下一个状态开始的时间戳
function DuckRaceWGData:GetNextStateTimstamp()
	return self.next_state_timestamp
end

-- 获得下一轮开始的时间戳
function DuckRaceWGData:GetNextRoundTimstamp()
	return self.next_round_begin_time
end

-- 设置鸭子信息
function DuckRaceWGData:SetAllDuckInfo(protocol)
	self.duck_info_list = protocol.duck_info_list
end

-- 获得参加比赛的鸭子信息
function DuckRaceWGData:GetAllDuckInfo()
	return self.duck_info_list
end

function DuckRaceWGData:GetResultsDuckList()
	local info_list = self:GetAllDuckInfo()
	local data_list = {}
	for k,v in pairs(info_list) do
		table.insert(data_list,v)
	end
	table.sort( data_list,function (a,b)
		if a and b then
			return a.rank < b.rank
		end
	end )
	return data_list
end

-- 获得单个鸭子信息
function DuckRaceWGData:GetDuckInfo(duck_index)
	return self:GetAllDuckInfo()[duck_index]
end

-- 获得所有鸭子的下注总数
function DuckRaceWGData:GetAllDuckBetCount()
	local all_bet_count = 0
	if not IsEmptyTable(self:GetAllDuckInfo()) then
		for duck_index = 0, #self:GetAllDuckInfo() do
			all_bet_count = all_bet_count + self:GetAllDuckInfo()[duck_index].bet_count
		end
	end
	return all_bet_count
end

-- 根据鸭子index获得参加比赛的鸭子信息
function DuckRaceWGData:GetDuckInfoByIndex(duck_index)
	return self.duck_info_list[duck_index]
end

-- 获得自己下注的跑得最快的鸭子index
function DuckRaceWGData:GetSelfBetFastestDuckIndex()
	local process_list = self:GetDuckProgressList()
	local _, my_bet_duck_list = self:GetSelfBetDuckIndexList()
	local fastest_duck_index = -1
	local fastest_process = -1
	for i = 0, DuckRaceWGData.CROSS_RACING_DUCK_INFO_COUNT - 1 do
		local process = process_list[i]
		if IsEmptyTable(my_bet_duck_list) or my_bet_duck_list[i] then
			if process > fastest_process then
				fastest_process = process
				fastest_duck_index = i
			end
		end
	end
	return fastest_duck_index, fastest_process
end

-- 获得自己下注最多的鸭子index
function DuckRaceWGData:GetSelfMaxBetDuckIndex()
	local all_self_bet_count = self:GetCurRoundAllSlefBetCount()
	local max_bet_duck_index = 0
	local max_bet_value = 0
	if not IsEmptyTable(all_self_bet_count) then
		for i = 0, #all_self_bet_count do
			local bet_value = all_self_bet_count[i]
			if bet_value > max_bet_value then
				max_bet_value = bet_value
				max_bet_duck_index = i
			end
		end
	end
	return max_bet_duck_index, max_bet_value
end

-- 获得自己下注的所有鸭子列表
function DuckRaceWGData:GetSelfBetDuckIndexList()
	local result = {}
	local bet_duck_index = {}
	local all_self_bet_count = self:GetCurRoundAllSlefBetCount()
	if not IsEmptyTable(all_self_bet_count) then
		for duck_index = 0, #all_self_bet_count do
			if all_self_bet_count[duck_index] > 0 then
				table.insert(result, duck_index)
				bet_duck_index[duck_index] = true
			end
		end
	end
	return result, bet_duck_index
end

-- 获得可被自己干扰的鸭子列表
function DuckRaceWGData:GetCanDisturbDuckIndexList()
	local result = {}
	-- 如果自己没有下注则不允许干扰
	if IsEmptyTable(self:GetSelfBetDuckIndexList()) then
		return result
	end

	for duck_index = 0, DuckRaceWGData.CROSS_RACING_DUCK_INFO_COUNT - 1 do
		 --  除开自己跟随的鸭子，其他鸭子都可以干扰
		if duck_index ~= self:GetFollowDuckIndex() then
			table.insert(result, duck_index)
		end
	end
	return result
end

-- 判断所给的鸭子index是否是自己下注的鸭子
function DuckRaceWGData:GetIsMyBetDuckIndex(duck_index)
	local _, bet_duck_index = self:GetSelfBetDuckIndexList()
	return bet_duck_index[duck_index] == true
end

-- 判断能否鼓舞当前跟随的鸭子
function DuckRaceWGData:GetCanInspire()
	if not self:GetFollowDuckIndex() then
		return false, Language.DuckRace.Reason1
	end

	if self:GetFollowDuckIndex() then
		if not self:GetIsMyBetDuckIndex(self:GetFollowDuckIndex()) then
			return false, ""
		end
	end

	if DuckRaceWGData.Instance:GetSurplusInspireCount() <= 0 then
		return false, Language.DuckRace.Reason2
	end
	return true, ""
end

-- 设置结算信息
function DuckRaceWGData:SetReusltsInfo(protocol)
	self.winner_duck_list = protocol.winner_duck_list 			 -- 记录每轮胜利的鸭子, 轮次从1开始
	self.next_round_begin_time = protocol.next_round_begin_time  -- 下一轮的开始时间戳（结算之后才有效）
end

-- 获得每轮胜利的鸭子index
function DuckRaceWGData:GetRoundDuckWinner(round)
	if not self.winner_duck_list[round] or self.winner_duck_list[round].index == -1 then
		return nil
	end
	return self.winner_duck_list[round]
end

-- 设置实况信息
function DuckRaceWGData:SetRaceLiveInfo(protocol)
	self.duck_progress_list = protocol.duck_progress_list 		-- 三只鸭子比赛进度（0~100）
end

function DuckRaceWGData:GetDuckProgressList()
	return self.duck_progress_list
end

-- 获得轮次描述
function DuckRaceWGData:GetRoundDesc(is_dark)
	if is_dark then
		return string.format(Language.DuckRace.RoundDescDark, self:GetCurRound(), DuckRaceWGData.CROSS_RACING_ROUND_COUNT)
	else
		return string.format(Language.DuckRace.RoundDesc, self:GetCurRound(), DuckRaceWGData.CROSS_RACING_ROUND_COUNT)
	end
end

-- 获得当前轮次状态描述
function DuckRaceWGData:GetRoundStateDesc()
	return Language.DuckRace.RoundStateDescList[self:GetCurRoundState()]
end

-- 获得第一名的鸭子下标
function DuckRaceWGData:GetLeaderDuckIndex()
	-- 如果当前轮比赛已结束
	if self:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RESULT then
		local winner = self:GetRoundDuckWinner(self:GetCurRound())
		if winner and winner.index then
			return winner.index
		end
	end

	local max_duck_index = 0
	local max_progress = 0
	for duck_index = 0, #self:GetDuckProgressList() do
		local progress = self:GetDuckProgressList()[duck_index]
		if progress > max_progress then
			max_duck_index = duck_index
			max_progress = progress
		end
	end
	return max_duck_index
end

-- 获得第一名的鸭子名称
function DuckRaceWGData:GetLeaderDuckName()
	if self:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RACING or self:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RESULT then
		local leader_duck_index = self:GetLeaderDuckIndex()
		local duck_info = self:GetDuckInfoByIndex(leader_duck_index)
		if duck_info then
			local monster_cfg = BossWGData.Instance:GetMonsterInfo(DuckRaceWGData.Instance:GetDuckMonsterId(duck_info.index))
			return string.format(Language.DuckRace.DuckNameInDarkView[duck_info.index], monster_cfg.name)
		end
	end
	return Language.DuckRace.EmptyLeader
end

-- 获取当前轮剩余可鼓舞次数
function DuckRaceWGData:GetSurplusInspireCount()
	local other_cfg = self:GetOtherCfg()
	return other_cfg.inspire_count_per_round - self.inspire_count
end

-- 获取当前轮剩余可干扰次数
function DuckRaceWGData:GetSurplusDisturbCount()
	local other_cfg = self:GetOtherCfg()
	return other_cfg.disturb_count_per_round - self.disturb_count
end

-- 获取鼓舞冷却倒计时
function DuckRaceWGData:GetInspireCD()
	local other_cfg = self:GetOtherCfg()
	return (self.last_inspire_duck_timestamp + other_cfg.inspire_duck_cd_second) - TimeWGCtrl.Instance:GetServerTime()
end

-- 获取干扰冷却倒计时
function DuckRaceWGData:GetDisturbCD()
	local other_cfg = self:GetOtherCfg()
	return (self.last_disturb_duck_timestamp + other_cfg.disturb_duck_cd_second) - TimeWGCtrl.Instance:GetServerTime()
end
------------------------------------------------------------- 协议信息End -----------------------------------------------------

-- 设置当前跟随目标
function DuckRaceWGData:SetFollowDuckIndex(follow_duck_index)
	if self.follow_duck_index == follow_duck_index then
		return
	end
	if Scene.Instance:GetSceneType() == SceneType.KF_DUCK_RACE then
		self.follow_duck_index = follow_duck_index
		if self.follow_duck_index then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		else
			GuajiWGCtrl.Instance:StopGuaji(nil, true)
		end
		ViewManager.Instance:FlushView(GuideModuleName.DuckRaceFight)
	end
end

function DuckRaceWGData:GetFollowDuckIndex()
	return self.follow_duck_index
end

-- 应援币领取红点
function DuckRaceWGData:FetchRemind()
	if self:GetCanFetchPlayCoin() then
		return 1
	else
		return 0
	end
end