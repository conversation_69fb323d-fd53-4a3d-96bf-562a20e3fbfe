﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TMPro_VertexGradientWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(TMPro.VertexGradient), null);
		<PERSON><PERSON>unction("New", _CreateTMPro_VertexGradient);
		<PERSON><PERSON>un<PERSON>("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("topLeft", get_topLeft, set_topLeft);
		<PERSON><PERSON>("topRight", get_topRight, set_topRight);
		<PERSON><PERSON>ar("bottomLeft", get_bottomLeft, set_bottomLeft);
		<PERSON><PERSON>("bottomRight", get_bottomRight, set_bottomRight);
		L<PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateTMPro_VertexGradient(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Color arg0 = ToLua.ToColor(L, 1);
				TMPro.VertexGradient obj = new TMPro.VertexGradient(arg0);
				ToLua.PushValue(L, obj);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Color arg0 = ToLua.ToColor(L, 1);
				UnityEngine.Color arg1 = ToLua.ToColor(L, 2);
				UnityEngine.Color arg2 = ToLua.ToColor(L, 3);
				UnityEngine.Color arg3 = ToLua.ToColor(L, 4);
				TMPro.VertexGradient obj = new TMPro.VertexGradient(arg0, arg1, arg2, arg3);
				ToLua.PushValue(L, obj);
				return 1;
			}
			else if (count == 0)
			{
				TMPro.VertexGradient obj = new TMPro.VertexGradient();
				ToLua.PushValue(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: TMPro.VertexGradient.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_topLeft(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.VertexGradient obj = (TMPro.VertexGradient)o;
			UnityEngine.Color ret = obj.topLeft;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index topLeft on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_topRight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.VertexGradient obj = (TMPro.VertexGradient)o;
			UnityEngine.Color ret = obj.topRight;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index topRight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bottomLeft(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.VertexGradient obj = (TMPro.VertexGradient)o;
			UnityEngine.Color ret = obj.bottomLeft;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bottomLeft on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bottomRight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.VertexGradient obj = (TMPro.VertexGradient)o;
			UnityEngine.Color ret = obj.bottomRight;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bottomRight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_topLeft(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.VertexGradient obj = (TMPro.VertexGradient)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.topLeft = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index topLeft on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_topRight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.VertexGradient obj = (TMPro.VertexGradient)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.topRight = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index topRight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_bottomLeft(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.VertexGradient obj = (TMPro.VertexGradient)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.bottomLeft = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bottomLeft on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_bottomRight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.VertexGradient obj = (TMPro.VertexGradient)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.bottomRight = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bottomRight on a nil value");
		}
	}
}

