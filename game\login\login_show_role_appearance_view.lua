LoginShowRoleAppearanceView = LoginShowRoleAppearanceView or BaseClass(SafeBaseView)

local camera_distance_param = {
    [GameEnum.FEMALE] = {
        [GameEnum.ROLE_PROF_1] = {
            role_pos = Vector3(0.15, 0, 0),
            role_rot = Vector3(0, 5, 0),
            role_scale = Vector3(1, 1, 1),
        },
        [GameEnum.ROLE_PROF_2] = {
            role_pos = Vector3(0.25, 0.1, 0),
            role_rot = Vector3(0, 20, 0),
            role_scale = Vector3(1, 1, 1),
        },
        [GameEnum.ROLE_PROF_3] = {
            role_pos = Vector3(0.2, 0.13, 0),
            role_rot = Vector3(0, -20, 0),
            role_scale = Vector3(1, 1, 1),
        },
        [GameEnum.ROLE_PROF_4] = {
            role_pos = Vector3(0.2, 0.13, 0),
            role_rot = Vector3(0, -20, 0),
            role_scale = Vector3(1, 1, 1),
        },
    },
    [GameEnum.MALE] = {
        [GameEnum.ROLE_PROF_1] = {
            role_pos = Vector3(0.3, 0, 0),
            role_rot = Vector3(0, 26, 0),
            role_scale = Vector3(1, 1, 1),
        },
        [GameEnum.ROLE_PROF_2] = {
            role_pos = Vector3(0.3, 0, 0),
            role_rot = Vector3(0, 26, 0),
            role_scale = Vector3(1, 1, 1),
        },
        [GameEnum.ROLE_PROF_3] = {
            role_pos = Vector3(0.2, 0.5, 0),
            role_rot = Vector3(0, -20, 0),
            role_scale = Vector3(0.8, 0.8, 0.8),
        },
        [GameEnum.ROLE_PROF_4] = {
            role_pos = Vector3(0.3, 0, 0),
            role_rot = Vector3(0, 26, 0),
            role_scale = Vector3(1, 1, 1),
        },
    },
}

local ROLEPOS_ROT_Y = 165
local CAMERA_POS = Vector3(0, 2.25, -3.2)

function LoginShowRoleAppearanceView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
	self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self.view_cache_time = 0
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self:SetMaskBg(false, true)
	
    self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_login_show_appearance")
end


function LoginShowRoleAppearanceView:__delete()
end

--[[
	data = {
		operate_type = 0,
		sex = 0,
		prof = 1,
        country = 1,
	}
]]

function LoginShowRoleAppearanceView:SetShowDataAndOpen(data)
	self.data = data
	if data then
		self:Open()
	end
end

function LoginShowRoleAppearanceView:OpenCallBack()
end

function LoginShowRoleAppearanceView:CloseCallBack()

end

function LoginShowRoleAppearanceView:LoadCallBack()
    self.suit_list = {}
    local node_num = self.node_list.suit_list.transform.childCount
    for i = 1, node_num do
        self.suit_list[i] = LoginShowRoleAppearanceRender.New(self.node_list.suit_list:FindObj("suit_item_" .. i))
        self.suit_list[i]:SetIndex(i)
        self.suit_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSelectSuitCallBack, self))
    end

    XUI.AddClickEventListener(self.node_list["return_view_btn"], BindTool.Bind(self.OnCloseView, self, true))
    XUI.AddClickEventListener(self.node_list.diy_go_btn, BindTool.Bind(self.OnClickGoDiy, self))
    XUI.AddClickEventListener(self.node_list.creat_role_btn, BindTool.Bind(self.OnClickCreatRole, self))


    self.node_list["ph_display"].event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDrawObjDrag, self))
    self.draw_obj_parent_transform = UnityEngine.GameObject.Find("RolePos").transform
    local camera_obj = UnityEngine.GameObject.Find("Camera")
    self.scene_camera = camera_obj and camera_obj:GetComponent(typeof(UnityEngine.Camera)) or nil

    self.draw_obj_parent_transform.localRotation = Quaternion.Euler(0, ROLEPOS_ROT_Y, 0)
    self.diy_do_onece_anim = true
end

function LoginShowRoleAppearanceView:ReleaseCallBack()
    self.data = nil
    self.cur_select_data = nil
    self.scene_camera = nil
    self.diy_do_onece_anim = nil

    if self.suit_list then
        for k,v in pairs(self.suit_list) do
            v:DeleteMe()
        end
        self.suit_list = nil
    end

    self.draw_obj_parent_transform = nil
    self:DestroyDIYRoleDrawObj()
end


function LoginShowRoleAppearanceView:ShowIndexCallBack()
     
end

function LoginShowRoleAppearanceView:OnFlush(param_t)
	if not self.data then
		return
	end

    self:FlushSuit()
    self:FlushSuitModel()
end

function LoginShowRoleAppearanceView:FlushSuit()
    local suit_show_list = LoginWGData.Instance:GetDiyRoleSuitShowCfg(self.data.sex, self.data.prof)
    for k,v in pairs(self.suit_list) do
        v:SetData(suit_show_list[k])
    end

    if self.cur_select_data == nil then
        self.suit_list[1]:OnClick()
    end
end

function LoginShowRoleAppearanceView:OnClickSelectSuitCallBack(cell)
    if not cell or not cell.data then
        return
    end

    if self.cur_select_data == cell.data then
		return
	end

	for i, v in ipairs(self.suit_list) do
		v:OnSelectChange(cell.index == v.index)
	end

	self.cur_select_data = cell.data
	self:FlushSuitModel()
end

function LoginShowRoleAppearanceView:FlushSuitModel()
    if self.cur_select_data == nil then
		return
	end

    local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(self.data.sex, self.data.prof)
    if not default_diy_data then
        return
    end

    local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.data.sex, self.data.prof, default_diy_data.preset_seq or 1)
    if not preset_diy_cfg then
        return
    end

    local draw_obj = self:GetDIYRoleDrawObj()
    self.role_draw_obj_transform = draw_obj.root.transform
    -- 主角
    local main_part = draw_obj:GetPart(SceneObjPart.Main)
    -- 改为CastShadow是为了产生倒影
    main_part:SetIsCastShadow(true)

    local bundle, asset = ResPath.GetRoleModel(RoleWGData.GetJobModelId(self.data.sex, self.data.prof))

    local body_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.BODY, self.cur_select_data.body_id, self.data.sex, self.data.prof)
    local hair_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.HAIR, self.cur_select_data.hair_id, self.data.sex, self.data.prof)
    local face_res = RoleWGData.GetPartModelRes(ROLE_SKIN_TYPE.FACE, self.cur_select_data.face_id, self.data.sex, self.data.prof)
    
    local extra_model_data = {
        sex = self.data.sex,
        prof = self.data.prof,

        role_body_res = body_res,
        role_face_res = face_res,
        role_hair_res = hair_res,

        eye_size = default_diy_data.eye_size,
        eye_shadow_color = default_diy_data.eye_shadow_color,
        eye_position = default_diy_data.eye_pos,

        left_pupil_type = default_diy_data.left_pupil_type,
        left_pupil_size = default_diy_data.left_pupil_size,
        left_pupil_color = default_diy_data.left_pupil_color,

        right_pupil_type = default_diy_data.right_pupil_type,
        right_pupil_size = default_diy_data.right_pupil_size,
        right_pupil_color = default_diy_data.right_pupil_color,

        mouth_color = default_diy_data.mouth_color,
        mouth_size = default_diy_data.mouth_size,
        mouth_position = default_diy_data.mouth_pos,

        face_decal_id = default_diy_data.face_decal_id,
        hair_color = default_diy_data.hair_color,

        eye_angle = preset_diy_cfg.eye_angle,
        eye_close = preset_diy_cfg.eye_close,
        eyebrow_angle = preset_diy_cfg.eyebrow_angle,
        nose_size = preset_diy_cfg.nose_size,
        nose_angle = preset_diy_cfg.nose_angle,
        mouth_angle = preset_diy_cfg.mouth_angle,
        cheek_size = preset_diy_cfg.cheek_size,
        chin_length = preset_diy_cfg.chin_length,
    }

    draw_obj:ChangeModel(SceneObjPart.Main, bundle, asset, function()
        self:ChangeCameraDistance()
    end, DRAW_MODEL_TYPE.ROLE, extra_model_data)

    self:ShowDrawObjPart()
    --self:PlayRoleDrawObjAni()
end

-- 加载人物部位模型
function LoginShowRoleAppearanceView:ShowDrawObjPart()
	local draw_obj = self:GetDIYRoleDrawObj()
	if self.data == nil or draw_obj == nil or draw_obj:IsDeleted() then
		return
	end

    local sex = self.data.sex
    local prof = self.data.prof

	-- 武器
	local weapon_res_id = 0
	if self.cur_select_data.weapon_id > 0 then
		weapon_res_id = RoleWGData.GetFashionWeaponId(sex, prof, self.cur_select_data.weapon_id)
	else
		weapon_res_id = RoleWGData.GetJobWeaponId(sex, prof)
	end

	local wepapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
	wepapon_part:SetIsCastShadow(true)
	local weapon_bundle, weapon_asset = ResPath.GetWeaponModelRes(weapon_res_id)
    draw_obj:ChangeModel(SceneObjPart.Weapon, weapon_bundle, weapon_asset, function()
        self:PlayRoleDrawObjAni()
    end)
end

-- 人物模型做动画
function LoginShowRoleAppearanceView:PlayRoleDrawObjAni()
    local draw_obj = self:GetDIYRoleDrawObj()
    if draw_obj == nil or draw_obj:IsDeleted() then
		return
	end

    if self.diy_do_onece_anim then
        local sex = self.data.sex
        local prof = self.data.prof
        if sex == GameEnum.FEMALE and prof == GameEnum.ROLE_PROF_1 then
            local anim = SceneObjAnimator.CGIdle
            draw_obj:CrossAction(SceneObjPart.Main, anim)
        else
            local anim = SceneObjAnimator.UiIdle
            draw_obj:CrossAction(SceneObjPart.Main, anim)
        end
        self.diy_do_onece_anim = nil
    end
end

function LoginShowRoleAppearanceView:ChangeCameraDistance(dis_type)
    local sex = self.data.sex
    local prof = self.data.prof
    local param = camera_distance_param[sex] and camera_distance_param[sex][prof]
    if not param then
        return
    end

    if IsNil(self.scene_camera) then
		return
	end

    self.scene_camera.transform.localPosition = CAMERA_POS
    if not IsNil(self.role_draw_obj_transform) then
        self.role_draw_obj_transform.localPosition = param.role_pos
        self.role_draw_obj_transform.localScale = param.role_scale
        self.role_draw_obj_transform.localRotation = Quaternion.Euler(param.role_rot.x, param.role_rot.y, param.role_rot.z)
	end
end

function LoginShowRoleAppearanceView:GetDIYRoleDrawObj()
	if nil == self.role_draw_obj then
		self.role_draw_obj = DrawObj.New(self, self.draw_obj_parent_transform)
        self.role_draw_obj:SetIsNeedSyncAnim(true)
        self.role_draw_obj:ChangeWaitSyncAnimType(SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.ROLE)

		self.role_draw_obj:SetIsInQueueLoad(true)
		self.role_draw_obj:SetIsUseObjPool(true)
        self.role_draw_obj:SetIsCanWeaponPointAttach(false)
	end

	return self.role_draw_obj
end

function LoginShowRoleAppearanceView:DestroyDIYRoleDrawObj()
	if self.role_draw_obj ~= nil then
		self.role_draw_obj:DeleteMe()
		self.role_draw_obj = nil
	end

    self.role_draw_obj_transform = nil
end

-- return_create  true:手动点击  false：一般为断线重连
function LoginShowRoleAppearanceView:OnCloseView(return_create)
	if not self.data then
		return
	end

	if return_create then
        LoginWGCtrl.Instance:ChangeLoginViewShow(true)
        local login_view = LoginWGCtrl.Instance:GetView()
        if login_view then
            login_view:OnDIYToCreate()
        end

        self:Close()
	else
		LoginWGCtrl.Instance:ChangeLoginViewShow(true)
		self:Close()
	end
end

function LoginShowRoleAppearanceView:OnDrawObjDrag(data)
	if not IsNil(self.role_draw_obj_transform) then
		local y = -data.delta.x * 0.25
		self.role_draw_obj_transform:Rotate(0, y, 0)
	end
end

function LoginShowRoleAppearanceView:OnClickGoDiy()
	if not self.data then
		return
	end

    local data = {}
	data.operate_type = self.data.operate_type
	data.sex = self.data.sex
	data.prof = self.data.prof
	data.country = self.data.country
    RoleDiyAppearanceWGCtrl.Instance:SetViewDataAndOpen(data)
    self:Close()
end

function LoginShowRoleAppearanceView:OnClickCreatRole()
	if not self.data then
		return
	end

    local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(self.data.sex, self.data.prof)
    if not default_diy_data then
        return
    end

    local preset_diy_cfg = RoleDiyAppearanceWGData.Instance:GetPresetDiyCfgBySexAndProf(self.data.sex, self.data.prof, 1)
    if not preset_diy_cfg then
        return
    end

    local diy_appearance_info = {
		face_id = preset_diy_cfg.face_id,
		hair_id = preset_diy_cfg.hair_id,
		body_id =  preset_diy_cfg.body_id,
		eye_size = default_diy_data.eye_size,
		eye_shadow_color = default_diy_data.eye_shadow_color,
		eye_position = default_diy_data.eye_pos,
        left_pupil_type = default_diy_data.left_pupil_type,
        left_pupil_size = default_diy_data.left_pupil_size,
        left_pupil_color = default_diy_data.left_pupil_color,
        right_pupil_type = default_diy_data.right_pupil_type,
        right_pupil_size = default_diy_data.right_pupil_size,
        right_pupil_color = default_diy_data.right_pupil_color,
		mouth_color = default_diy_data.mouth_color,
		mouth_size = default_diy_data.mouth_size,
		mouth_position = default_diy_data.mouth_pos,
		face_decal_id = default_diy_data.face_decal_id,
		hair_color = default_diy_data.hair_color,
		preset_seq = default_diy_data.preset_seq
	}

    local data = {
		sex = self.data.sex,
		prof = self.data.prof,
		country = self.data.country,
		diy_appearance_info = diy_appearance_info,
	}
	
	LoginWGCtrl.Instance:OpenCreateSelectNameView(data)
end
-----------------------------------LoginShowRoleAppearanceRender-----------------------
LoginShowRoleAppearanceRender = LoginShowRoleAppearanceRender or BaseClass(BaseRender)
function LoginShowRoleAppearanceRender:LoadCallBack()

end

function LoginShowRoleAppearanceRender:__delete()

end


function LoginShowRoleAppearanceRender:OnFlush()
	if not self.data then
        self.view:CustomSetActive(false)
		return
	end

    local bundle, asset = ResPath.GetLoginUiImg(self.data.icon)
    self.node_list.icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.icon.image:SetNativeSize()
    end)

    self.view:CustomSetActive(true)
end

function LoginShowRoleAppearanceRender:OnSelectChange(is_select)
	self.node_list.select_hl:SetActive(is_select)
end