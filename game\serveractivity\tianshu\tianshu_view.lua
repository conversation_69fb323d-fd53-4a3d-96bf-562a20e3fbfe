----------------------------------------------------
--上古卷轴（天书）
----------------------------------------------------

TianShuView = TianShuView or BaseClass(SafeBaseView)

function TianShuView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/tianshu_ui_prefab", "layout_tianshu")
	self.select_index = 1
	self.HLImage_list = {}
	self.red_remind_list = {}
	-- self.num_list = {}
end

function TianShuView:__delete()
	self.HLImage_list = nil
	self.red_remind_list = nil
end

function TianShuView:ReleaseCallBack()
	if self.tianshu_list then
		self.tianshu_list:DeleteMe()
		self.tianshu_list = nil
	end
	self.select_index = 1
	if self.close_btn~=nil then
		self.close_btn = nil
	end
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TianShuView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
end

function TianShuView:LoadCallBack()
	for i=1,5 do
		self.node_list["HorizontalTabbarCell_"..i].button:AddClickListener(BindTool.Bind2(self.OnclickHorizonal,self,i))
		self.HLImage_list[i] = self.node_list["HLImage_"..i]--高亮
		self.node_list["HLImage_"..i]:SetActive(false)
		self.red_remind_list[i] = self.node_list["red_remind_"..i] --红点
		self.red_remind_list[i]:SetActive(false)
		self.node_list["Text_"..i].text.text = Language.TianShuXunZhu.TabGrop[i]
	end
	self:CreateListView()
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TianShuView, self.get_guide_ui_event)
	self.close_btn = self.node_list.close_btn
end

function TianShuView:OnclickLingQu(index)
		if self.index == GameEnum.TIANSHU_CHENGZHANG_TYPE then
			TianShuWGCtrl.Instance:SendChengZhangTianShuFetchReward(31)
			AudioService.Instance:PlayRewardAudio()
		else
			--通知服务端领取
			RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TIANSHU_XUNZHU_FETCH_REWARD, self.index - 1, 31)
			AudioService.Instance:PlayRewardAudio()
		end
		--show新技能
		local skill_id = TianShuWGData.Instance:GetSkillId(self.index)
		TianShuWGCtrl.Instance:ShowGetNewSkillView(skill_id)
end

function TianShuView:OnclickHorizonal(index)
	self:GetOpenIndex(index)
end

function TianShuView:ShowIndexCallBack(index)
	if self.index == nil then
		self:GetOpenIndex(index)
	end
end

function TianShuView:GetOpenIndex(index)
	local all_fetch_flag_t = TianShuWGData.Instance:GetAllFetchFlag()

	local data_info = TianShuWGData.Instance:GetChengZhangTisnShuDataListByIndex()
		-- 1 == self.data.can_fetch_flag
	if index == nil or index == 0 then
		for i=1,#data_info do --判断天书是否有可领取的
			if 1==data_info[i].can_fetch_flag and 0 == data_info[i].fetch_flag then
				index = 5
				self.index = index
				RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TIANSHU_XUNZHU_REQ_INFO)
				return
			end
		end
		for i=1,#all_fetch_flag_t do
			if not all_fetch_flag_t[i] then
				index = i
				break
			else
				index = 5
			end
		end
	end
	self.index = index
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TIANSHU_XUNZHU_REQ_INFO) --天书
end

function TianShuView:OpenCallBack()

end

function TianShuView:CloseCallBack()
	self.index = nil
end

function TianShuView:CreateListView()
	self.tianshu_list = AsyncListView.New(TianShuItem,self.node_list.ph_list)
end

function TianShuView:OnFlush()
	if self.index == nil then
		return
	end
	local all_fetch_flag_t = TianShuWGData.Instance:GetAllFetchFlag()
	local flush_index = 0
	-- if all_fetch_flag_t[self.index] ~= nil and not all_fetch_flag_t[self.index - 1] and self.index ~= 1 then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShuXunZhu.OpenTip)
	-- 	self.index = self.upindex
	-- 	flush_index = 3 --刷新可见格子
	-- 	-- return
	-- end
	-- -- else
	-- 	self.upindex = self.index
	for i=1,#self.HLImage_list do
		if self.index == i then
			self.HLImage_list[i]:SetActive(true)
		else
			self.HLImage_list[i]:SetActive(false)
		end
	end
	--最终领取按钮
	self.node_list.btn_final_fetch.button:AddClickListener(BindTool.Bind(self.OnclickLingQu,self,self.index))
	--经验百分比
	self.node_list.tianshu_text.text.text = Language.TianShuXunZhu.WordText[self.index]
	--左侧插画图片
	self.node_list.Img_5.image:LoadSprite(ResPath.GetTianshuResPath("img_"..self.index))
	--设置每个卷轴的数据
	local data_list = {}
	if self.index == GameEnum.TIANSHU_CHENGZHANG_TYPE then -- 5天书寻主 成长天书类型 在普通天书之后
		data_list = TianShuWGData.Instance:GetChengZhangTisnShuDataListByIndex()
	else
		data_list = TianShuWGData.Instance:GetTisnShuDataListByIndex(self.index)
	end
	self.tianshu_list:SetDataList(data_list,flush_index)
	--判断红点
	self:IsShowRedRemind()

	local final_reward_item, fetch_flag, can_fetch_flag = nil, false, false

	if self.index == GameEnum.TIANSHU_CHENGZHANG_TYPE then
		final_reward_item, fetch_flag, can_fetch_flag = TianShuWGData.Instance:GetChengZhangFinalRewardByIndex()
	else
		final_reward_item, fetch_flag, can_fetch_flag = TianShuWGData.Instance:GetFinalRewardByIndex(self.index)
	end

	XUI.SetButtonEnabled(self.node_list.btn_final_fetch, can_fetch_flag)--设置按钮不可用,置灰
	--可领取按钮
	self.node_list.btn_final_fetch:SetActive(not fetch_flag)
	--不可领取
	self.node_list.img_unfinished_1:SetActive(fetch_flag)


	local all_fetch_flag_t = TianShuWGData.Instance:GetAllFetchFlag()
	for i = 2, #all_fetch_flag_t do
		local fetch_flag = all_fetch_flag_t[i - 1]
		local name = fetch_flag and Language.TianShuXunZhu.TabGrop[i] or Language.TianShuXunZhu.Unknow
	end

	--final_reward_cfg[1]:奖励的技能名字,[3] 条件的总数
	local final_reward_cfg = Split(final_reward_item, ":")
	local final_reward_num = 0
	for k, v in pairs(data_list) do
		if v.fetch_flag == 1 then
			final_reward_num = final_reward_num + 1
		end
	end
	local num = tonumber(final_reward_cfg[3])
	local str = string.format(Language.TianShuXunZhu.FinalRewardDesc, final_reward_cfg[1], final_reward_num, num)
	--final_reward_num:已经达成的,num 总数
	self.node_list.rich_reward_desc.text.text = str
	self:BtnIsHaveActive()
end
-- end
function TianShuView:BtnIsHaveActive()
	local all_fetch_flag_t = TianShuWGData.Instance:GetAllFetchFlag()
	local active_index = 0
	for i=1,4 do
		if all_fetch_flag_t[i] then
			active_index = i
		end
	end
	for i=1,4 do
		XUI.SetButtonEnabled(self.node_list["HorizontalTabbarCell_"..i], i <= (active_index + 1) )--设置按钮不可用,置灰
	end
end
--红点展示
function TianShuView:IsShowRedRemind()
	local all_fetch_flag_t = TianShuWGData.Instance:GetAllFetchFlag()
	local index = 1
	for i=1,#all_fetch_flag_t do
		if not all_fetch_flag_t[i] then
			index = i
			break
		end
	end
	for i=1,5 do
		if i == GameEnum.TIANSHU_CHENGZHANG_TYPE then
			local final_reward_item, fetch_flag, can_fetch_flag = TianShuWGData.Instance:GetChengZhangFinalRewardByIndex()
			if not fetch_flag and can_fetch_flag then
				self.red_remind_list[i]:SetActive(true)
			else
				local data_list = TianShuWGData.Instance:GetChengZhangTisnShuDataListByIndex()
				for k=1,#data_list do --判断天书是否有可领取的
					if 1==data_list[k].can_fetch_flag and 0 == data_list[k].fetch_flag then
						self.red_remind_list[i]:SetActive(true)
						break
					else
						self.red_remind_list[i]:SetActive(false)
					end
				end
			end
		else
			local final_reward_item, fetch_flag, can_fetch_flag = TianShuWGData.Instance:GetFinalRewardByIndex(i)
			if not fetch_flag and can_fetch_flag and i <= index then
				self.red_remind_list[i]:SetActive(true)
			else
				local data_list = TianShuWGData.Instance:GetTisnShuDataListByIndex(i)
				for j=1,#data_list do
					if 1 == data_list[j].can_fetch_flag and  0 == data_list[j].fetch_flag and i <= index then
						self.red_remind_list[i]:SetActive(true)
						break
					else
						self.red_remind_list[i]:SetActive(false)
					end
				end
			end
		end
	end
end

function TianShuView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.TianShuViewGet then
		if self.tianshu_list ~= nil then
			for k, v in ipairs(self.tianshu_list:GetAllItems())do
				if v:GetData().can_fetch_flag == 1 and v:GetData().fetch_flag == 0 then
					return v.node_list.btn_lingqu, BindTool.Bind1(v.FetchReward, v)
				end
			end
		end
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end



----------------------------------------------------------------------------------------------------
--天书item
----------------------------------------------------------------------------------------------------
TianShuItem = TianShuItem or BaseClass(BaseRender)
function TianShuItem:__init()
	self.item_cell = {}
	self.boss_img_cell = {}
	self.node_list.btn_go:SetActive(false)
	self.node_list.btn_huoqu:SetActive(false)
	self.node_list.img_lingqu:SetActive(false)
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.FetchReward, self))
	self.node_list.btn_go.button:AddClickListener(BindTool.Bind(self.GoToKillBoss, self))--前往击杀
	self.node_list.btn_huoqu.button:AddClickListener(BindTool.Bind(self.OpenBossView,self))
	self.node_list.btn_qianwang.button:AddClickListener(BindTool.Bind(self.OnGoToChengZhangView, self))
	for i=1,2 do
		self.node_list["ph_boss_"..i].button:AddClickListener(BindTool.Bind(self.SetBossTip, self,i))
	end

end

function TianShuItem:__delete()
	if nil ~= next(self.item_cell) then
		for k,v in pairs(self.item_cell) do
			v:DeleteMe()
		end
		self.item_cell = nil
	end
end
function TianShuItem:OpenBossView()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Boss)
end
function TianShuItem:OnFlush()
	if nil == self.data then return end
	if 1 == self.data.item_index  then
		self:FlushPanelOne()
	elseif 2 == self.data.index or 4 == self.data.index then
		self:FlushPanelOne_two()
	elseif 3 == self.data.index then
		self:FlushPanelTwo()
	elseif self.data.index == GameEnum.TIANSHU_CHENGZHANG_TYPE then -- 5 天书寻主 成长天书类型
		self:FlushPanelTree()
	end
	XUI.SetButtonEnabled(self.node_list.btn_lingqu,1 == self.data.can_fetch_flag)--设置按钮不可用,置灰
	self.node_list.btn_lingqu:SetActive(0 == self.data.fetch_flag)
	self.node_list.img_lingqu:SetActive(1 == self.data.fetch_flag)
	self:ShowIconImg()
end

function TianShuItem:ShowIconImg()
	if self.data.index == 1 then
		local asset_name,bundle_name = ResPath.GetTianshuResPath("icon_gold")
		self.node_list.icon_img.image:LoadSprite(asset_name,bundle_name,function ()
			self.node_list.icon_img.image:SetNativeSize()
		end)
	elseif self.data.index == 2 then
		local asset_name,bundle_name = ResPath.GetTianshuResPath("fuwen_jinghua")
		self.node_list.icon_img.image:LoadSprite(asset_name,bundle_name,function ()
			self.node_list.icon_img.image:SetNativeSize()
		end)
	else
		local asset_name,bundle_name = ResPath.GetTianshuResPath("icon_coin_xianyu_bind")
		self.node_list.icon_img.image:LoadSprite(asset_name,bundle_name,function ()
			self.node_list.icon_img.image:SetNativeSize()
		end)
	end
end
function TianShuItem:FlushPanelOne()
	self.node_list["ph_show_" .. 1]:SetActive(true)
	for i=1,2 do
		self.node_list["ph_boss_"..i]:SetActive(false)
		self.node_list["ph_boss_"..i.."_bg"]:SetActive(false)

	end
	local count = #self.data.recommend_t
	for i = 1, count do
		if nil == self.item_cell[i] then
			self.item_cell[i] = ItemCell.New()
			self.item_cell[i]:SetInstanceParent(self.node_list["ph_show_" .. i])
		end
		local data = {item_id = self.data.recommend_t[i]}
		data.param = CommonStruct.ItemParamData()
		data.param.xianpin_type_list = {
			[1] = 5,
			[2] = 2,
			[3] = 3,
		}
		self.item_cell[i]:SetData(data)
	end
	local desc = ""
	if 4 == self.data.index then
		desc = string.format(self.data.desc, self.data.reward)
	elseif 2 == self.data.index or 1 == self.data.index then
		local equip_desc = Language.TianShuXunZhu.EquipType[self.data.param2]
		local item_config = ItemWGData.Instance:GetItemConfig(self.data.recommend_t[1])
		if nil ~= item_config then
			equip_desc = Language.Common.ColorName[item_config.color] .. equip_desc
		end
		local reward = self.data.reward >= 10000 and (self.data.reward / 10000)..Language.Common.Wan or self.data.reward
		desc = string.format(self.data.desc, self.data.param1, reward)

	end
	self.node_list.btn_huoqu:SetActive(false)
	self.node_list.text_up.text.text = desc
	self.node_list.btn_go:SetActive(3 == self.data.index)
	self.node_list.btn_qianwang:SetActive(false)
	self.node_list.text_tuijian:SetActive(true)
end


function TianShuItem:FlushPanelOne_two()--嗜血天书
	--"ph_show 用来显示icon的
	self.node_list["ph_show_" .. 1]:SetActive(true)
	--隐藏boss图标
	for i=1,2 do
		self.node_list["ph_boss_"..i]:SetActive(false)
		self.node_list["ph_boss_"..i.."_bg"]:SetActive(false)

	end
	--推荐(击杀boss时会有两个推荐icon)
	local count = #self.data.recommend_t
	print_error(">>>>>>",self.data.recommend_t,count)
	for i = 1, count do--
		--生成推荐icon
		if nil == self.item_cell[i] then
			self.item_cell[i] = ItemCell.New()
			self.item_cell[i]:SetInstanceParent(self.node_list["ph_show_" .. i])
		end
		--设置icon数据
		local data = {item_id = self.data.recommend_t[i]}
		data.param = CommonStruct.ItemParamData()
		data.param.xianpin_type_list = {
			[1] = 5,
			[2] = 2,
			[3] = 3,
		}
		self.item_cell[i]:SetData(data)
	end

	local desc = ""
	if 4 == self.data.index then
		desc = string.format(self.data.desc, self.data.reward)
	elseif 2 == self.data.index or 1 == self.data.index then
		local equip_desc = Language.TianShuXunZhu.EquipType[self.data.param2]
		local item_config = ItemWGData.Instance:GetItemConfig(self.data.recommend_t[1])
		if nil ~= item_config then
			equip_desc = Language.Common.ColorName[item_config.color] .. equip_desc
		end
		local reward = self.data.reward >= 10000 and (self.data.reward / 10000)..Language.Common.Wan or self.data.reward
		desc = string.format(self.data.desc, self.data.param1, equip_desc, reward)
	end
	self.node_list.text_up.text.text = desc
	self.node_list.btn_go:SetActive(3 == self.data.index)
	self.node_list.btn_huoqu:SetActive(3 ~= self.data.index)
	self.node_list.btn_qianwang:SetActive(false)
	self.node_list.text_tuijian:SetActive(true)
end



function TianShuItem:FlushPanelTwo()
	self.node_list["ph_show_" .. 1]:SetActive(false)
	local count = #self.data.recommend_t
	for i = 1, count do
		local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.recommend_t[i]]
		self.node_list["ph_boss_".. i]:SetActive(true)
		self.node_list["ph_boss_"..i.."_bg"]:SetActive(true)
		self.node_list["ph_boss_".. i].image:LoadSprite(ResPath.GetBossIcon("boss_" .. monster_cfg.small_icon))
		self.node_list["hook_".. i]:SetActive(self.data["is_boss_killed_index_" .. i])
	end
	local desc = string.format(self.data.desc, self.data.reward)
	self.node_list.text_up.text.text = desc
	self.node_list.btn_go:SetActive(3 == self.data.index)
	self.node_list.btn_huoqu:SetActive(3 ~= self.data.index)
	self.node_list.btn_qianwang:SetActive(false)
	self.node_list.text_tuijian:SetActive(false)
end

--天书寻主
function TianShuItem:FlushPanelTree()
	if self.data.reward_item == nil then return end
	self.node_list["ph_show_" .. 1]:SetActive(true)
	for i=1,2 do
		self.node_list["ph_boss_"..i]:SetActive(false)
		self.node_list["ph_boss_"..i.."_bg"]:SetActive(false)
	end
	local item_index = 1
	if nil == self.item_cell[item_index] then
		self.item_cell[item_index] = ItemCell.New()
		self.item_cell[item_index]:SetInstanceParent(self.node_list["ph_show_" .. item_index])
	end
	self.item_cell[item_index]:SetData(self.data.reward_item)
	if self.data.desc ~= nil  then
		self.node_list.text_up.text.text = self.data.desc
	end
	self.node_list.btn_go:SetActive(false)
	self.node_list.btn_huoqu:SetActive(false)
	self.node_list.btn_qianwang:SetActive(true)
	self.node_list.text_tuijian:SetActive(true)
end

function TianShuItem:OnGoToChengZhangView()
	if self.data.open_view_parm ~= nil and self.data.open_view_parm ~= "" then
		if self.data.open_view_parm1 ~= nil and self.data.open_view_parm1 ~= "" then
			local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("recharge_zztz")
			if is_open then
				FunOpen.Instance:OpenViewNameByCfg(self.data.open_view_parm1)
				return
			end
		end
		FunOpen.Instance:OpenViewNameByCfg(self.data.open_view_parm)
	end
end


function TianShuItem:FetchReward()
	if nil == self.data then return end
	if self.data.index == GameEnum.TIANSHU_CHENGZHANG_TYPE then
		TianShuWGCtrl.Instance:SendChengZhangTianShuFetchReward(self.data.seq)
		AudioService.Instance:PlayRewardAudio()
	else
		RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TIANSHU_XUNZHU_FETCH_REWARD, self.data.index - 1, self.data.seq - 1)
		AudioService.Instance:PlayRewardAudio()
	end
end

function TianShuItem:SetBossTip(index)
	if nil == self.data then return end
	local boss_id = self.data.recommend_t[index]
	local boss_info = TianShuWGData.Instance:GetBossInfoById(boss_id)
	local data = {boss_info = boss_info}
	TianShuWGCtrl.Instance.view.close_btn.button:AddClickListener(BindTool.Bind(self.OnclickCloseBossTip, self))
	self:OpenBossTip(data)
end


function TianShuItem:OpenBossTip(data)
	TianShuWGCtrl.Instance.view.close_btn:SetActive(true)
	self.node_list.layout_boss_info:SetActive(true)
	self.node_list.img_boss.image:LoadSprite(ResPath.GetBossIcon("boss_" .. data.boss_info.small_icon))
	self.node_list.label_boss_name.text.text = data.boss_info.name
	self.node_list.label_level.text.text = data.boss_info.level
	local boss_info = BossWGData.Instance:GetBossInfoByBossId(data.boss_info.id)
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(boss_info.scene_id)
	self.node_list.label_map.text.text = scene_cfg.name
end

function TianShuItem:OnclickCloseBossTip()
	TianShuWGCtrl.Instance.view.close_btn:SetActive(false)
	self.node_list.layout_boss_info:SetActive(false)
end


function TianShuItem:GoToKillBoss()
	-- local count = #self.data.recommend_t
	-- local boss_id = self.data.recommend_t[1]
	-- for i = 1, count do
	-- 	if not self.data["is_boss_killed_index_" .. i] then
	-- 		boss_id = self.data.recommend_t[i]
	-- 		break
	-- 	end
	-- end
	-- FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, nil, {open_boss_id = boss_id})
	local task_index = 1
	local count = #self.data.recommend_t
	for i=1,count do
		task_index = 1
		if not self.data["is_boss_killed_index_" .. i] then
			task_index = i
			break
		end
	end
	local monster_id = self.data.recommend_t[task_index] or nil
	local view_index, num_index, card_index = TianShuWGData.Instance:GetTuMoViewIndex(monster_id,task_index)
	BossWGData.Instance:SetBossTuJianIndex(view_index, num_index, card_index)
	FunOpen.Instance:OpenViewByName(GuideModuleName.Boss, view_index)
	TianShuWGCtrl.Instance:CloseView()
end

function TianShuItem:CreateSelectEffect()
	-- body
end