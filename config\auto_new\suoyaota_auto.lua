return {
	["monster_list_default_table"]={},
	["activity_time_list"]={
		{start_min=17,stop_min=27,standby_min=15,},
		{start_min=47,stop_min=57,standby_min=45,},},
	["monster_list"]={
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},
		{},},
	["eff_pos_default_table"]={eff_index=1,point5="104:46",point3="100:48",point1="64:96",eff_id=3157,point4="70:93",point2="66:95",point6="74:91",},
	["eff_pos"]={
		{point5="72:92",point3="68:94",eff_id=3156,},
		{eff_index=2,point5="105:94",point3="99:94",point1="93:94",point4="102:94",point2="96:94",point6="108:94",},
		{eff_index=3,point1="96:50",eff_id=3158,point4="102:47",point2="98:49",point6="106:45",},},
	["lv_list_default_table"]={lv_idx=1,kill_monster_score=10,kill_boss_score=500,reward_base_exp=143524,gathar_score=30,reward_base_coin=100000,min_lv=1,total_score=500,max_lv=500,},
	["lv_list"]={
		{lv_idx=0,max_lv=100,},
		{min_lv=101,max_lv=200,},
		{lv_idx=2,reward_base_exp=224713,min_lv=201,max_lv=300,},
		{lv_idx=3,reward_base_exp=295919,min_lv=301,max_lv=400,},
		{lv_idx=4,reward_base_exp=394068,min_lv=401,},
		{lv_idx=5,reward_base_exp=520170,min_lv=501,max_lv=600,},
		{lv_idx=6,reward_base_exp=637208,min_lv=601,max_lv=700,},
		{lv_idx=7,reward_base_exp=839486,min_lv=701,max_lv=800,},
		{lv_idx=8,reward_base_exp=1083440,min_lv=801,max_lv=900,},
		{lv_idx=9,reward_base_exp=1387120,min_lv=901,max_lv=1000,},},
	["task_list_default_table"]={lv_idx=1,param_id=183,task_index=1,param_max=30,},
	["task_list"]={
		{lv_idx=0,param_id=11081,param_max=50,},
		{lv_idx=0,task_index=2,param_max=20,},
		{lv_idx=0,param_id=11082,task_index=3,},
		{lv_idx=0,param_id=11071,task_index=4,param_max=1,},
		{param_id=11083,param_max=50,},
		{task_index=2,param_max=20,},
		{param_id=11084,task_index=3,},
		{param_id=11072,task_index=4,param_max=1,},
		{lv_idx=2,param_id=11085,param_max=50,},
		{lv_idx=2,task_index=2,param_max=20,},
		{lv_idx=2,param_id=11086,task_index=3,},
		{lv_idx=2,param_id=11073,task_index=4,param_max=1,},
		{lv_idx=3,param_id=11087,param_max=50,},
		{lv_idx=3,task_index=2,param_max=20,},
		{lv_idx=3,param_id=11088,task_index=3,},
		{lv_idx=3,param_id=11074,task_index=4,param_max=1,},
		{lv_idx=4,param_id=11089,param_max=50,},
		{lv_idx=4,task_index=2,param_max=20,},
		{lv_idx=4,param_id=11090,task_index=3,},
		{lv_idx=4,param_id=11075,task_index=4,param_max=1,},
		{lv_idx=5,param_id=11091,param_max=50,},
		{lv_idx=5,task_index=2,param_max=20,},
		{lv_idx=5,param_id=11092,task_index=3,},
		{lv_idx=5,param_id=11076,task_index=4,param_max=1,},
		{lv_idx=6,param_id=11093,param_max=50,},
		{lv_idx=6,task_index=2,param_max=20,},
		{lv_idx=6,param_id=11094,task_index=3,},
		{lv_idx=6,param_id=11077,task_index=4,param_max=1,},
		{lv_idx=7,param_id=11095,param_max=50,},
		{lv_idx=7,task_index=2,param_max=20,},
		{lv_idx=7,param_id=11096,task_index=3,},
		{lv_idx=7,param_id=11078,task_index=4,param_max=1,},
		{lv_idx=8,param_id=11097,param_max=50,},
		{lv_idx=8,task_index=2,param_max=20,},
		{lv_idx=8,param_id=11098,task_index=3,},
		{lv_idx=8,param_id=11079,task_index=4,param_max=1,},
		{lv_idx=9,param_id=11099,param_max=50,},
		{lv_idx=9,task_index=2,param_max=20,},
		{lv_idx=9,param_id=11100,task_index=3,},
		{lv_idx=9,param_id=11080,task_index=4,param_max=1,},},
	["area_limit"]={
		{point3="78:90",point1="65:96",point4="84:96",},
		{area_index=2,point2="109:95",},
		{area_index=3,point3="104:37",point1="96:50",point4="92:46",point2="108:41",},},
	["other_cfg"]={
		{free_join_times=3,guwu_dur_time=120,reward_three_cost=100,guwu_gold_cost=10,guwu_coin_cost=200000,guwu_gongji_addpercent=50,juanzhou_itemid=27412,buy_times_cost=50,reward_double_cost=50,},},
	["area_limit_default_table"]={area_index=1,point3="111:87",point1="91:95",point4="91:87",point2="69:101",},
}
