LoginWGData = LoginWGData or BaseClass()

Cache_Account = "cache_account"
function LoginWGData:__init()
	if LoginWGData.Instance then
		print_error("[LoginWGData] Attemp to create a singleton twice !")
	end
	LoginWGData.Instance = self

	self.role_list_ack_info = {
		result = 0,
		count = 0,
		role_list = {}
	}

	self.plat_type = ""
	self.curr_select_role_id = -1				-- 当前选择角色的ID
	self.camp_capability_list = {}
end

function LoginWGData:__delete()
	LoginWGData.Instance = nil
end

function LoginWGData:SetUserPlatType(plat_type)
	-- 进入游戏后只设置一次有效的渠道id（进入跨服时服务端下发的id不对，服务端说改不了, 只能客户端容错）
	if not self.plat_type or self.plat_type == "" then
		self.plat_type = plat_type
	end
end

-- 获取用户平台ID
function LoginWGData:GetUserPlatType()
	return self.plat_type
end

function LoginWGData:SetCurrSelectRoleId(role_id)
	self.curr_select_role_id = role_id
end

function LoginWGData:GetCurrSelectRoleId()
	return self.curr_select_role_id
end

function LoginWGData:SetCampIndex(index)
	self.camp_index = index
end

function LoginWGData:GetCampIndex()
	return self.camp_index
end

function LoginWGData:SetRoleListAck(protocol)
	self.role_list_ack_info.result = protocol.result or 0
	self.role_list_ack_info.count = protocol.count
	local role_list = protocol.role_list
	self.role_list_ack_info.role_list = role_list

	local last_server_list = self:GetLastLoginServer() --最近登陆服务器
	local cur_select_server = tonumber(last_server_list[1])
	-- 设置用户数据
	local user_vo = GameVoManager.Instance:GetUserVo()
	user_vo:ClearRoleList()
	for i = 1, protocol.count do
		user_vo:AddRole(
			protocol.role_list[i].role_id,
			protocol.role_list[i].role_name,
			protocol.role_list[i].avatar,
			protocol.role_list[i].sex,
			protocol.role_list[i].prof,
			protocol.role_list[i].country,
			protocol.role_list[i].level,
			protocol.role_list[i].create_time,
			protocol.role_list[i].online_time,
			protocol.role_list[i].last_logout_time,
			protocol.role_list[i].capability,
			protocol.role_list[i].avatar_key_big,
			protocol.role_list[i].avatar_key_small
			-- protocol.role_list[i].avatar_timestamp
			)

		self:SetServerListHead(cur_select_server, i, protocol.role_list[i].role_id,
			protocol.role_list[i].prof, protocol.role_list[i].sex, protocol.role_list[i].level, protocol.role_list[i].role_name)
	end
end

function LoginWGData:GetRoleListAck()
	return self.role_list_ack_info
end

function LoginWGData:GetServerListHeadCache(account_name, server_id)
	if not account_name or not server_id then
		return {}
	end

	if not self.server_list_head then
		self.server_list_head = {}
	end

	if not self.server_list_head[account_name] then
		self.server_list_head[account_name] = {}
	end

	if not self.server_list_head[account_name][server_id] then
		local server_head_str = PlayerPrefsUtil.GetString("server_list_head" .. account_name .. server_id)
		if server_head_str and server_head_str ~= "" then
			local success, result = pcall(StrToTable, server_head_str)
			if success and result then
				self.server_list_head[account_name][server_id] = result
			else
				self.server_list_head[account_name][server_id] = {}
			end
		else
			self.server_list_head[account_name][server_id] = {}
		end
	end

	return self.server_list_head[account_name][server_id]
end

function LoginWGData:SetServerListHead(server_id, index, role_id, prof, sex, level, name, avatar_key_big, avatar_key_small)
	local is_use_cache_server_list_data = self:GetIsUseCacheServerListData()
	if not is_use_cache_server_list_data then
		return
	end

	if not server_id or not role_id then
		return
	end

	avatar_key_big = avatar_key_big or 0
	avatar_key_small = avatar_key_small or 0
	prof = prof or 0
	sex = sex or 0
	level = level or 1
	name = name or ""

	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	if not account_name or account_name == "" then
		return
	end
	account_name = Cache_Account .. account_name

	local server_list_head = self:GetServerListHeadCache(account_name, server_id)

	-- 确定索引位置
	local len = #server_list_head
	if not index then
		index = len < 2 and (len + 1) or 2
	end

	if not server_list_head[index] then
		server_list_head[index] = {}
	end

	server_list_head[index].avatar_key_big = avatar_key_big
	server_list_head[index].avatar_key_small = avatar_key_small
	server_list_head[index].role_id = role_id
	server_list_head[index].prof = prof
	server_list_head[index].sex = sex
	server_list_head[index].level = level
	server_list_head[index].name = name

	local server_head_str = TableToStr(server_list_head, 1)
	if server_head_str then
		PlayerPrefsUtil.SetString("server_list_head" .. account_name .. server_id, server_head_str)
	end

	AvatarManager.Instance:SetAvatarKey(role_id, avatar_key_big, avatar_key_small)
end

--等级或者名字改变就调用这个方法
function LoginWGData:SetServerListLevel()
	local is_use_cache_server_list_data = self:GetIsUseCacheServerListData()
	if not is_use_cache_server_list_data then
		return
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	local server_id = user_vo.plat_server_id
	if server_id == nil then
		return
	end
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local role_id = main_role_vo.role_id
	local prof = main_role_vo.prof
    local level = main_role_vo.level
    local sex = main_role_vo.sex
	local name = main_role_vo.name
	local server_head_str = nil
	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	account_name = Cache_Account .. account_name

	local index = 1
	local list = self:GetServerListHeadCache(account_name, server_id)
	for i=1,#list do
		if list[i].role_id == role_id then
			list[i].prof = prof
            list[i].level = level
            list[i].name = name
            list[i].sex = sex
		end
	end
	server_head_str = TableToStr(list, 1)

	PlayerPrefsUtil.SetString("server_list_head" .. account_name .. server_id, server_head_str)
end

function LoginWGData:GetServerListHead(server_id)
	if not server_id then
		return nil
	end

	local is_use_cache_server_list_data = self:GetIsUseCacheServerListData()
	-- 获取PHP下发的角色列表
	if not is_use_cache_server_list_data then
		local role_list = self:GetPHPAccountRoleList()
		if not IsEmptyTable(role_list) then
			return role_list[server_id]
		end

		return nil
	end

	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	if not account_name or account_name == "" then
		return nil
	end

	-- 获取本地缓存数据
	account_name = Cache_Account .. account_name
	local head_data = self:GetServerListHeadCache(account_name, server_id)
	if IsEmptyTable(head_data) then
		return nil
	end

	return head_data
end

function LoginWGData:GetPHPAccountRoleListCache(account_name)
	if not account_name or account_name == "" then
		return {}
	end

	if not self.php_account_role_list_cache then
		self.php_account_role_list_cache = {}
	end

	if not self.php_account_role_list_cache[account_name] then
		local role_list_str = PlayerPrefsUtil.GetString("php_account_role_list" .. account_name)
		if role_list_str and role_list_str ~= "" then
			local success, result = pcall(StrToTable, role_list_str)
			if success and result then
				self.php_account_role_list_cache[account_name] = result
			else
				self.php_account_role_list_cache[account_name] = {}
			end
		else
			self.php_account_role_list_cache[account_name] = {}
		end
	end

	return self.php_account_role_list_cache[account_name]
end

function LoginWGData:SetPHPAccountRoleList(role_list)
	local list = {}
	for _, role_data in pairs(role_list) do
		local server_id = role_data.server_id
		local data = {
			server_id = server_id,
			role_id = role_data.role_id,
			role_name = role_data.role_name,
			level = role_data.role_level,
			sex = role_data.gender,
			prof = role_data.prof	
		}

		if not list[server_id] then
			list[server_id] = {}
		end

		table.insert(list[server_id], data)
	end

	-- 缓存到本地，由于切换角色等操作，重新加载游戏，lua数据会没，不走重新登录，需要从本地缓存读取
	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	if account_name and account_name ~= "" then
		account_name = Cache_Account .. account_name
		local role_list_str = TableToStr(list, 1)
		if role_list_str then
			PlayerPrefsUtil.SetString("php_account_role_list" .. account_name, role_list_str)
		end
		
		if not self.php_account_role_list_cache then
			self.php_account_role_list_cache = {}
		end

		self.php_account_role_list_cache[account_name] = list
	end
end

function LoginWGData:GetPHPAccountRoleList()
	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	if account_name and account_name ~= "" then
		account_name = Cache_Account .. account_name
		local cached_data = self:GetPHPAccountRoleListCache(account_name)
		if not IsEmptyTable(cached_data) then
			return cached_data
		end
	end

	return {}
end

-- 是否使用旧接口的缓存数据
-- 内网开发，后台不下发角色列表，使用旧接口的缓存数据
function LoginWGData:GetIsUseCacheServerListData()
	if self.is_use_cache_server_list_data == nil then
		self.is_use_cache_server_list_data = string.match(GLOBAL_CONFIG.local_package_info.config.init_urls[1], "192.168.0.133") ~= nil	
	end

	return self.is_use_cache_server_list_data
end

-- 获取玩家登录过的服务器列表
function LoginWGData:GetPlayerLoginServerList()
	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	account_name = Cache_Account .. account_name

	local result_server_list = {}

	-- 获取缓存的服务器ID列表
	local server_id_list = {}
	local server_id_str = PlayerPrefsUtil.GetString("PRVE_SRVER_ID")
	if server_id_str and "" ~= server_id_str and not tonumber(server_id_str) then
		local total_server_list = StrToTable(server_id_str)
		if not IsEmptyTable(total_server_list) and total_server_list[account_name] then
			server_id_list = total_server_list[account_name]
		end
	end

	local all_server_list = self:GetViewShowShowServerList()

	local is_use_cache_server_list_data = self:GetIsUseCacheServerListData()
	if is_use_cache_server_list_data then
		-- 使用旧接口的本地缓存的服务器列表数据
		
		if not IsEmptyTable(server_id_list) then
			local server_map = {}
			for _, server_data in pairs(all_server_list) do
				server_map[server_data.id] = true
			end
			
			for i, v in ipairs(server_id_list) do
				local number_id = tonumber(v)
				if number_id and server_map[number_id] then
					table.insert(result_server_list, number_id)
				end
			end
		end
	else
		-- 使用PHP下发缓存在本地的角色数据
		local php_role_list = self:GetPHPAccountRoleList()
		local php_server_id_set = {}
		local added_server_set = {}

		-- 需要过滤记录里的隐藏服
		local all_server_map = {}
		for _, server_data in pairs(all_server_list) do
			all_server_map[server_data.id] = true
		end

		for server_id, roles in pairs(php_role_list) do
			if not IsEmptyTable(roles) then
				local number_server_id = tonumber(server_id)
				if number_server_id then
					php_server_id_set[number_server_id] = true
				end
			end
		end

		-- 以server_id_list的顺序添加服务器
		if not IsEmptyTable(server_id_list) then
			for i, v in ipairs(server_id_list) do
				local number_id = tonumber(v)
				if number_id and php_server_id_set[number_id] and all_server_map[number_id] and not added_server_set[number_id] then
					table.insert(result_server_list, number_id)
					added_server_set[number_id] = true
				end
			end
		end

		for server_id, _ in pairs(php_server_id_set) do
			if all_server_map[server_id] and not added_server_set[server_id] then
				table.insert(result_server_list, server_id)
				added_server_set[server_id] = true
			end
		end
	end

	return result_server_list
end

-- 获取最近登录的服务器列表
function LoginWGData:GetLastLoginServer()
	local server_list = self:GetPlayerLoginServerList()
	if not IsEmptyTable(server_list) then
		return server_list
	end

	-- 推荐服务器列表
	local recommend_list = {}
	local min_num = nil
	local recommend_index = nil
	local recommend_type = GLOBAL_CONFIG.param_list.recommend_type or 0
	if recommend_type ~= "" and recommend_type == 1 then
		for _, v in pairs(server_list) do
			if v.flag == 1 then
				table.insert(recommend_list, v)
			end
		end

		for k,v in pairs(recommend_list) do
			if v.role_num ~= nil and (min_num == nil or min_num > v.role_num) then
				min_num = v.role_num
				recommend_index = k
			end
		end
	end

	if recommend_index ~= nil then
		local r_server = recommend_list[recommend_index]
		return r_server ~= nil and {r_server.id} or {}
	else
		local last_server = GLOBAL_CONFIG.server_info.last_server
		if nil ~= last_server and "" ~= last_server then
			return {last_server}
		end
	end

	return {}
end

-- 获得服务器表
function LoginWGData:GetShowServerList()
	local server_offset = GLOBAL_CONFIG.server_info.server_offset or 0 --偏移id
	if server_offset >= 30010 then
		server_offset = 0
	end

	-- 过滤要显示的服
	local show_list = {}
	local server_list = GLOBAL_CONFIG.server_info.server_list
	local is_white = GameVoManager.Instance:GetUserVo().is_white
	local is_test_player = 1 == is_white
	for _, v in pairs(server_list) do
		if v.id > server_offset and (v.is_show == 1 or is_test_player) then
			table.insert(show_list, v)
		end
	end

	SortTools.SortAsc(show_list, "id")
	return show_list
end

function LoginWGData:GetIsAreaServer(server_id)
	local server_area = GLOBAL_CONFIG.server_info.server_area or {}
	if not server_id or IsEmptyTable(server_area) then
		return false
	end

	for k,v in pairs(server_area) do
		if server_id >= v.start_server_id and server_id <= v.end_server_id then
			return true
		end
	end
	
	return false
end

function LoginWGData:GetShowAreaServerList()
	local show_list = self:GetViewShowShowServerList()
	local server_area = GLOBAL_CONFIG.server_info.server_area or {}
	local group_list = {}

	-- 大区
	if not IsEmptyTable(server_area) then
		local arr_server_area = {}
		for k,v in pairs(server_area) do
			local data = __TableCopy(v)
			data.sort = k
			table.insert(arr_server_area, data)
		end
		SortTools.SortAsc(arr_server_area, "sort")

		for i = #show_list , 1, -1 do
			local server_data = show_list[i]
			for k,v in pairs(server_area) do
				if server_data.id >= v.start_server_id and server_data.id <= v.end_server_id then
					if group_list[k] == nil then
						group_list[k] = {name = v.name, server_list = {}, sort = k}
					end
	
					table.insert(group_list[k].server_list, server_data)
					table.remove(show_list, i)
					break
				end
			end
		end
	end

	-- 每10个一区间
	local temp_num = 0
	local seq = #group_list + 1
	for i = 1 , #show_list do
		local server_data = show_list[i]
		if temp_num >= 10 then
			temp_num = 0
			seq = #group_list + 1
		end
		
		if group_list[seq] == nil then
			group_list[seq] = {server_list = {}, sort = seq}
		end

		temp_num = temp_num + 1
		table.insert(group_list[seq].server_list, server_data)
	end

	local begin_server_id = 0
	local end_server_id = 0
	for k,v in pairs(group_list) do
		local server_list = v.server_list
		if v.name == nil then
			begin_server_id = server_list[1].id
			end_server_id = server_list[#server_list].id
			if begin_server_id == end_server_id then
				v.name = string.format("%s%s", begin_server_id, Language.Login.Fu)
			else
				v.name = string.format("%s-%s%s", begin_server_id, end_server_id, Language.Login.Fu)
			end
		end

		SortTools.SortDesc(v.server_list, "id")
	end

	SortTools.SortDesc(group_list, "sort")
	return group_list
end


-- 是否可登陆指定的服务器
function LoginWGData:IsCanLoginServer(server_id)
	local user_vo = GameVoManager.Instance:GetUserVo()
	local server_vo = self:GetServerVoById(server_id)
	if nil == server_vo then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Login.ServerNotExist)
		return false
	end

	local is_emulator = DeviceTool.IsEmulator()
	local emulator_limit_login = GLOBAL_CONFIG.param_list.emulator_limit_login == 1
	local is_common_plat_account = 0 == user_vo.is_white
	local is_test_plat_account = 1 == user_vo.is_white
	-- 非白名单模拟器玩家限制登录
	if emulator_limit_login and is_emulator and is_common_plat_account then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Login.EmulatorLimitLogin)
		return false
	end

	local need_check = false
	-- 正常玩家需检测（非模拟器 or 模拟器白名单玩家）
	if emulator_limit_login and is_emulator then
		need_check = is_test_plat_account
	else
		need_check = is_common_plat_account and not IS_LOCLA_WINDOWS_DEBUG_EXE
	end
	
	local client_time = GLOBAL_CONFIG.client_time and tonumber(GLOBAL_CONFIG.client_time) or 0
	local now_server_time = GLOBAL_CONFIG.server_info.server_time + (Status.NowTime - client_time)
	if need_check then
		-- 正常玩家开服时间限制
		if nil ~= server_vo.ahead_time and server_vo.ahead_time > 0
			and server_vo.open_time >= server_vo.ahead_time and now_server_time < server_vo.ahead_time then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Login.ServerOpenTips2)
			return false
		end

		-- 正常玩家停服维护
		if nil ~= server_vo.pause_time and 0 < server_vo.pause_time and
		now_server_time < server_vo.pause_time then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Login.ServerOpenTips3)
			return false
		end
	end

	return true
end

function LoginWGData:GetShowServerNameById(server_id)
	return self:GetShowServerName(self:GetServerVoById(server_id))
end

-- 获得要显示的服务器名字
function LoginWGData:GetShowServerName(server_vo)
	if nil == server_vo then
		return "未选择"
	end

	-- 服id分段
	local show_server_id = server_vo.id
	-- 处理0 - 1500内的服
	local server_offset = GLOBAL_CONFIG.server_info.server_offset or 0
	show_server_id = show_server_id - server_offset

	local show_server_name = ""
	
	-- 分服 A B C D
	if 1 == GLOBAL_CONFIG.server_info.server_zone_flag then
		if GLOBAL_CONFIG.server_info.server_zone_subscript ~= nil  then
			local zone_subscript_t = Split(GLOBAL_CONFIG.server_info.server_zone_subscript, "_")
			if nil ~= next(zone_subscript_t) then
				show_server_id = self:GetRightServerAndPrefix(show_server_id, zone_subscript_t)
			end
		end
	end

	if GLOBAL_CONFIG.param_list.is_hide_area_server_id_name == 1 and self:GetIsAreaServer(show_server_id) then
		show_server_name = server_vo.name
	else
		show_server_name = string.format("%s%s-%s", show_server_id, Language.Login.Fu, server_vo.name)
	end

	local open_tips = self:GetShowServerOpenTips(server_vo)
	if "" ~= open_tips then
		show_server_name = show_server_name .. "\n" .. open_tips
	end

	return show_server_name
end

-- 服务器的开启提示
function LoginWGData:GetShowServerOpenTips(server_vo)
	if nil == server_vo then
		return "undefind"
	end

	local open_tips = ""
	local client_time = GLOBAL_CONFIG.client_time and tonumber(GLOBAL_CONFIG.client_time) or 0
	local server_time = GLOBAL_CONFIG.server_info.server_time or 0
	local now_server_time = server_time + (Status.NowTime - client_time)

	local open_time = tonumber(server_vo.open_time)
	-- 开服时间
	if nil ~= open_time and "" ~= open_time and now_server_time < open_time then
		local t_time = os.date("*t", open_time)
		open_tips = string.format(Language.Login.ServerOpenTips, t_time.month, t_time.day, t_time.hour, t_time.min)

	-- 维护时间
	elseif nil ~= server_vo.pause_time and now_server_time < server_vo.pause_time then
		local t_time = os.date("*t", server_vo.pause_time)
		open_tips = string.format(Language.Login.ServerOpenTips, t_time.month, t_time.day, t_time.hour, t_time.min)
	end

	return open_tips
end

function LoginWGData:GetRightServerAndPrefix(server_id, zone_subscript_t)
	local pre_subscript = 0
	local cur_subscript = 0
	local minus_pre_subscript = false
	for k,v in pairs(zone_subscript_t) do
		cur_subscript = tonumber(v)
		if server_id > pre_subscript and server_id <= cur_subscript then
			server_id = server_id - pre_subscript
			minus_pre_subscript = true
			break
		end
		pre_subscript = cur_subscript
	end
	local max_subscript = tonumber(zone_subscript_t[#zone_subscript_t])
	if not minus_pre_subscript and server_id > max_subscript then
		server_id = server_id - max_subscript
	end
	return server_id
end

function LoginWGData:GetServerVoById(server_id)
	local server_list = GLOBAL_CONFIG.server_info.server_list
	for k,v in pairs(server_list) do
		if v.id == server_id then
			return v
		end
	end

	return nil
end

function LoginWGData:GetServerFlag(server_id)
	local vo = self:GetServerVoById(server_id)
	return vo and vo.flag or 0
end

function LoginWGData:GetGetServerIP(server_id)
	local vo = self:GetServerVoById(server_id)
	return vo and vo.ip or ""
end

function LoginWGData:GetGetServerPort(server_id)
	local vo = self:GetServerVoById(server_id)
	return vo and vo.port or 0
end

function LoginWGData:GetServerName(server_id)
	local vo = self:GetServerVoById(server_id)
	return vo and vo.name or ""
end

function LoginWGData:SetUserMoney(num)
	UtilU3d.CacheData("login_user_money", num)

end

function LoginWGData:GetUserMoney()
	local flag = UtilU3d.GetCacheData("login_user_money")
	return flag or 0
end

-- 创角自定义配置
function LoginWGData:GetCreateRoleDIYCfg(part_type, sex, prof)
	local list = {}
	local cfg = ConfigManager.Instance:GetAutoConfig("job_auto").create_diy
	for k,v in ipairs(cfg) do
		if v.part == part_type and v.sex == sex and v.prof == prof then
			table.insert(list, v)
		end
	end

	return list
end

-- 创角套装
function LoginWGData:GetCreateRoleSuitCfg(sex, prof)
	local list = {}
	local cfg = ConfigManager.Instance:GetAutoConfig("job_auto").suit_show
	for k,v in ipairs(cfg) do
		if v.sex == sex and v.prof == prof then
			table.insert(list, v)
		end
	end

	return list
end

-- 展示套装
function LoginWGData:GetDiyRoleSuitShowCfg(sex, prof)
	local list = {}
	local cfg = ConfigManager.Instance:GetAutoConfig("job_auto").diy_appearance_show
	for k,v in ipairs(cfg) do
		if v.sex == sex and v.prof == prof then
			table.insert(list, v)
		end
	end

	return list
end




function LoginWGData:GetAgentCondition()
	local cfg = ConfigManager.Instance:GetAutoConfig("agent_adapt_auto").server_secret
	return cfg
end

-- 服务器标记 (1: 火爆 2: 新服 3: 即将开服 4: 维护)
function LoginWGData.GetServerStateAsset(flag)
	flag = flag ~= 0 and flag or 1
	return ResPath.GetLoginUiImg("a3_xf_" .. flag)
end

-- 本地记录登录过的服务器列表
function LoginWGData:CahcePreviousServer()
	if IS_ON_CROSSSERVER then
		return
	end

	local server_list = nil
	local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
	account_name = Cache_Account .. account_name
	local server_id_str = PlayerPrefsUtil.GetString("PRVE_SRVER_ID")
	local last_server_list = self:GetLastLoginServer()
	local view_select_server_id = self:ViewSelectServerID()

	if not server_id_str or "" == server_id_str or tonumber(server_id_str) then
		server_list = {}
		last_server_list[1] = view_select_server_id
		server_list[account_name] = last_server_list
		server_id_str = TableToStr(server_list, 1)
		PlayerPrefsUtil.SetString("PRVE_SRVER_ID", server_id_str)
		return
	end

	server_list = StrToTable(server_id_str)
	last_server_list = server_list[account_name]
	-- last_server_list = StrToTable(server_id_str)
	if not last_server_list then
		last_server_list = {}
		last_server_list[1] = view_select_server_id
		server_list[account_name] = last_server_list
		server_id_str = TableToStr(server_list, 1)
		PlayerPrefsUtil.SetString("PRVE_SRVER_ID", server_id_str)
		return
	end

	for i = 1, #last_server_list do
		if tonumber(last_server_list[i]) == view_select_server_id then
			table.remove(last_server_list, i)
			break
		end
	end

	table.insert(last_server_list, 1, view_select_server_id)
	server_list[account_name] = last_server_list
	server_id_str = TableToStr(server_list, 1)
	PlayerPrefsUtil.SetString("PRVE_SRVER_ID", server_id_str)
end

function LoginWGData:ViewSelectServerID(server_id)
	if server_id then
		self.view_select_server_id = server_id
	else
		if not self.view_select_server_id then
			local last_server_list = self:GetViewGetLastLoginServerList(true)
			self.view_select_server_id = last_server_list[1]
		end

		return self.view_select_server_id
	end
end

function LoginWGData:ViewSelectServerType(server_type)
	if server_type then
		self.view_select_server_type = server_type
	else
		return self.view_select_server_type
	end
end

function LoginWGData:GetViewShowShowServerList()
	if not self.view_server_list then
		self.view_server_list = self:GetShowServerList()
	end

	--推荐的服务器集合
	if not self.view_recommend_list then
		self.view_recommend_list = {}
		for _, v in pairs(self.view_server_list) do
			if v.flag == 1 or v.flag == 2 then
				table.insert(self.view_recommend_list, v)
			end
		end
	end

	return self.view_server_list, self.view_recommend_list
end

function LoginWGData:GetViewShowAreaServerList()
	if not self.view_area_server_list then
		self.view_area_server_list = self:GetShowAreaServerList()
	end

	return self.view_area_server_list
end

function LoginWGData:GetViewGetLastLoginServerList(need_update)
	if not self.view_last_server_list or need_update then
		self.view_last_server_list = self:GetLastLoginServer()
	end
	
	return self.view_last_server_list
end