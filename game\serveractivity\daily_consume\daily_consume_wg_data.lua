DailyConsumeWGData = DailyConsumeWGData or BaseClass()
function DailyConsumeWGData:__init()
	if DailyConsumeWGData.Instance then
		ErrorLog("[DailyConsumeWGData] Attemp to create a singleton twice !")
	end
	DailyConsumeWGData.Instance = self
	--红点显示
	RemindManager.Instance:Register(RemindName.DailyConsume, BindTool.Bind(self.IsShowDailyConsumeRedPoint, self))--每日累消费
end

function DailyConsumeWGData:__delete()
	DailyConsumeWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.DailyConsume)
end


function DailyConsumeWGData:SetSCDailyTotalConsumeInfo(protocol)
	self.daily_total_consume_fetch_reward_flag = bit:d2b(protocol.daily_total_consume_fetch_reward_flag)
	self.daily_total_consume_total_day_fetch_reward_flag = bit:d2b(protocol.daily_total_consume_total_day_fetch_reward_flag)
	self.daily_total_consume_total_day = protocol.daily_total_consume_total_day
	self.today_has_add_consume_day = protocol.today_has_add_consume_day
	self.daily_total_consume_can_fetch_reward_flag = bit:d2b(protocol.daily_total_consume_can_fetch_reward_flag)
	self.day_consume_gold = protocol.day_consume_gold
	RemindManager.Instance:Fire(RemindName.DailyConsume)
end

function DailyConsumeWGData:GetDayConsumeGold()
	return self.day_consume_gold or 0
end

-- 获取每日累充奖励rechargereward_daily
function DailyConsumeWGData:GetDailyTotalRechargeReward(type)
	local cfg = ConfigManager.Instance:GetAutoConfig("daily_total_consume_auto").daily_total_consume_reward
	-- local rand_t = ServerActivityWGData.Instance:GetRandActivityConfig(cfg, ACTIVITY_TYPE.RAND_ACTIVITY_DAILYCONSUME)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	-- local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for i = #cfg, 1,-1  do
		-- if cfg[i].seq == type - 1 and role_level >= cfg[i].role_level and server_day >= cfg[i].opengame_day then
		if cfg[i].seq == type - 1 and role_level >= cfg[i].role_level then
			return cfg[i]
		end
	end
end

-- 获取每日累充右边列表rechargereward_daily
function DailyConsumeWGData:GetDailyTotalRechargeRightReward()
	local level = 0
	local role_level =  RoleWGData.Instance:GetRoleLevel()
	local cfg = ConfigManager.Instance:GetAutoConfig("daily_total_consume_auto").total_consume_day_reward

	local fetch_flag = self:GetTotalRechargeTotalDayFetchRewardFlag()
	local days = self:GetTotalRechargeTotalDay()
	local data_list = {}
	for i = 1, #cfg do                 -- 先循环找一下当前档位等级
		local j = i + 1
		if j > #cfg then return end 
		if cfg[j].role_level < role_level then
			level = cfg[i].role_level
			break
		end
	end 
	local j = 0
	for i = 1, #cfg do
		if cfg[i].role_level > level then break end 
		if cfg[i].role_level == level then
			local flag = -1
			local has_fetch = fetch_flag[32 - j] == 1 and 1 or 0
			j = j + 1
			if cfg[i].total_consume_day <= days then 						-- 到达时间
				flag = has_fetch > 0 and 1 or 0			-- 是否已领取
			else
				flag = -1							-- 时间未到
			end
			table.insert(data_list, {fetch_flag = flag, reward_item = cfg[i].reward_item, total_consume_day = cfg[i].total_consume_day})
		end
	end
	return data_list
end
-- 每日累充奖励领取标记rechargereward_daily
function DailyConsumeWGData:GetTotalRechargeFetchRewardFlag(index)
	return self.daily_total_consume_fetch_reward_flag[32 - index]
end

function DailyConsumeWGData:GetTotalRechargeCanFetchRewardFlag(index)
	return self.daily_total_consume_can_fetch_reward_flag[32 - index]
end

-- 每日累充充值天数奖励标记rechargereward_daily
function DailyConsumeWGData:GetTotalRechargeTotalDayFetchRewardFlag()
	return self.daily_total_consume_total_day_fetch_reward_flag
end

-- 根据档次获取首充所需元宝
function DailyConsumeWGData:GetFirstRechargeNeedGold(type)
	local cfg = ConfigManager.Instance:GetAutoConfig("daily_total_consume_auto").daily_total_consume_reward
	local rand_t = ServerActivityWGData.Instance:GetRandActivityConfig(cfg, ACTIVITY_TYPE.RAND_ACTIVITY_DAILYCONSUME)	
	return rand_t[type].need_consume_gold
end

-- 每日累充充值天数rechargereward_daily
function DailyConsumeWGData:GetTotalRechargeTotalDay()
	return self.daily_total_consume_total_day
end

-- 检查小红点
function DailyConsumeWGData:RemindDailyConsume()
	local num = 0
	local day_consume_gold = DailyConsumeWGData.Instance:GetDayConsumeGold()
	if nil == day_consume_gold then
		return
	end	
	for i = 1, 3 do
		local cfg = self:GetDailyTotalRechargeReward(i)
		local fetch_flag = self:GetTotalRechargeFetchRewardFlag(i - 1)
		local can_fetch_flag = self:GetTotalRechargeCanFetchRewardFlag(i - 1)
		local is_can_get = false
		if cfg then
			is_can_get = can_fetch_flag == 1 and fetch_flag ~= 1
		end
		local remind_num = is_can_get and 1 or 0
		if remind_num > 0 then
			num = num + 1
		end
	end
	return num
end

-- 红点提示
function DailyConsumeWGData:IsShowDailyConsumeRedPoint()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_DAILYCONSUME) then
		return 0
	end
	--判断是否消费达到目标
	for i = 1, 3 do
		local cfg = self:GetDailyTotalRechargeReward(i)
		local fetch_flag = self:GetTotalRechargeFetchRewardFlag(i - 1)
		local can_fetch_flag = self:GetTotalRechargeCanFetchRewardFlag(i - 1)
		local is_can_get = false
		if cfg then
			is_can_get = can_fetch_flag == 1 and fetch_flag ~= 1
		end
		local remind_num = is_can_get and 1 or 0
		if remind_num == 1 then
			return 1
		end
	end
	local data_list = self:GetDailyTotalRechargeRightReward()
	if data_list ~= nil and not IsEmptyTable(data_list) then
		for k,v in pairs(data_list) do
			if v.fetch_flag == 0 then
				return 1 
			end
		end
	end
	return 0
end