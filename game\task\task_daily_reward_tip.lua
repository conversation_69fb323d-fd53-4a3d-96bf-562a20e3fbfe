TaskDailyRewardTip = TaskDailyRewardTip or BaseClass(SafeBaseView)

function TaskDailyRewardTip:__init()
	
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/task_prefab", "task_daily_reward_tip")
	self:SetMaskBg(true)
	self.time = 5
end

function TaskDailyRewardTip:__delete()
end

function TaskDailyRewardTip:LoadCallBack()
	self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(570, 392)

	XUI.AddClickEventListener(self.node_list["btn_ok"], BindTool.Bind(self.OnClick, self))
	self.item_cell = ItemCell.New(self.node_list["go_item"])
	self:UpdataView()
end

function TaskDailyRewardTip:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TaskDailyRewardTip:OpenCallBack()
	self:UpdataView()
end

function TaskDailyRewardTip:UpdataView()
	-- body
	if not self:IsLoadedIndex(0) then return end
	local reward_list = TaskWGData.Instance:ShowDailyTaskList()
	if not reward_list or IsEmptyTable(reward_list) then return end

	local reward_cfg = TaskWGData.Instance:GetTaskRewardCfg(GameEnum.TASK_TYPE_RI)
	if not reward_cfg then return end
	
	local reward_item = nil
	local reward_item_cfg = {}
	for k,v in pairs(reward_list) do
		reward_item = nil
		if 10 == tonumber(v) then
			reward_item = reward_cfg.first_round_reward_item[0]
		elseif 20 == tonumber(v) then
			reward_item = reward_cfg.second_round_reward_item[0]
		end
		if reward_item then
			reward_item_cfg.item_id = reward_item.item_id
			reward_item_cfg.num = reward_item_cfg.num and (reward_item_cfg.num + reward_item.num) or reward_item.num
			reward_item_cfg.is_bind = reward_item.is_bind
		end
	end
	if IsEmptyTable(reward_item_cfg) then return end	

	self.item_cell:SetData(reward_item_cfg)
	local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item_cfg.item_id)
	if not item_cfg then return end

	self.node_list["text_num"].text.text = string.format(Language.Task.DailyNumDes, reward_list[#reward_list])
	if reward_item_cfg.num > 1 then
		self.node_list["text_item_name"].text.text = item_cfg.name .. "*" .. reward_item_cfg.num
	else
		self.node_list["text_item_name"].text.text = item_cfg.name
	end

	self.time = 5
	self.time_quest = GlobalTimerQuest:AddTimesTimer(BindTool.Bind1(self.UpdateTime, self), 1, self.time)
end

function TaskDailyRewardTip:UpdateTime()
	-- body
	self.time = self.time - 1
	self.node_list["btn_text"].text.text = Language.Common.BtnOK .. "(" .. self.time .. ")"
	
	if self.time <= 0 then
		self:ClearTime()
		self:OnClick()
	end
end

function TaskDailyRewardTip:ClearTime()
	-- body
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function TaskDailyRewardTip:CloseCallBack()
	self:ClearTime()
end

function TaskDailyRewardTip:OnFlush()
	
end

function TaskDailyRewardTip:OnClick()
	local reward_list = TaskWGData.Instance:ShowDailyTaskList()
	TaskWGCtrl.Instance:SendReward(GameEnum.TASK_TYPE_RI, #reward_list)
	self:Close()
end