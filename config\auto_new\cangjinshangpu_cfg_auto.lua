-- C-藏金商铺.xls
local item_table={
[1]={item_id=37030,num=1,is_bind=1},
[2]={item_id=22099,num=18880,is_bind=1},
[3]={item_id=26492,num=1,is_bind=1},
[4]={item_id=40232,num=1,is_bind=1},
[5]={item_id=26367,num=50,is_bind=1},
[6]={item_id=26368,num=50,is_bind=1},
[7]={item_id=18819,num=1,is_bind=1},
[8]={item_id=22099,num=38880,is_bind=1},
[9]={item_id=40233,num=1,is_bind=1},
[10]={item_id=40234,num=1,is_bind=1},
[11]={item_id=40236,num=1,is_bind=1},
[12]={item_id=37007,num=1,is_bind=1},
[13]={item_id=37251,num=1,is_bind=1},
[14]={item_id=37736,num=1,is_bind=1},
[15]={item_id=37605,num=1,is_bind=1},
[16]={item_id=26186,num=1,is_bind=1},
[17]={item_id=29203,num=1,is_bind=1},
[18]={item_id=29206,num=1,is_bind=1},
[19]={item_id=29205,num=1,is_bind=1},
[20]={item_id=43327,num=1,is_bind=1},
[21]={item_id=43328,num=1,is_bind=1},
[22]={item_id=43329,num=1,is_bind=1},
[23]={item_id=43330,num=1,is_bind=1},
[24]={item_id=43331,num=1,is_bind=1},
[25]={item_id=43332,num=1,is_bind=1},
[26]={item_id=43333,num=1,is_bind=1},
[27]={item_id=43334,num=1,is_bind=1},
[28]={item_id=43335,num=1,is_bind=1},
[29]={item_id=43336,num=1,is_bind=1},
[30]={item_id=43337,num=1,is_bind=1},
[31]={item_id=43338,num=1,is_bind=1},
[32]={item_id=43342,num=1,is_bind=1},
[33]={item_id=43343,num=1,is_bind=1},
[34]={item_id=43344,num=1,is_bind=1},
[35]={item_id=43345,num=1,is_bind=1},
[36]={item_id=43346,num=1,is_bind=1},
[37]={item_id=43347,num=1,is_bind=1},
[38]={item_id=43348,num=1,is_bind=1},
[39]={item_id=43349,num=1,is_bind=1},
[40]={item_id=43350,num=1,is_bind=1},
[41]={item_id=43351,num=1,is_bind=1},
[42]={item_id=43352,num=1,is_bind=1},
[43]={item_id=43353,num=1,is_bind=1},
[44]={item_id=26554,num=1,is_bind=1},
[45]={item_id=26191,num=1,is_bind=1},
[46]={item_id=26193,num=1,is_bind=1},
[47]={item_id=26552,num=1,is_bind=1},
[48]={item_id=26553,num=1,is_bind=1},
[49]={item_id=37901,num=1,is_bind=1},
[50]={item_id=37904,num=1,is_bind=1},
[51]={item_id=37906,num=1,is_bind=1},
[52]={item_id=37804,num=1,is_bind=1},
[53]={item_id=37807,num=1,is_bind=1},
[54]={item_id=37810,num=1,is_bind=1},
[55]={item_id=26357,num=1,is_bind=1},
[56]={item_id=26363,num=1,is_bind=1},
[57]={item_id=26369,num=1,is_bind=1},
[58]={item_id=26380,num=1,is_bind=1},
[59]={item_id=27858,num=1,is_bind=1},
[60]={item_id=27857,num=1,is_bind=1},
[61]={item_id=27854,num=1,is_bind=1},
[62]={item_id=48441,num=1,is_bind=1},
[63]={item_id=48118,num=1,is_bind=1},
[64]={item_id=45017,num=1,is_bind=1},
[65]={item_id=22099,num=980,is_bind=1},
[66]={item_id=39195,num=1,is_bind=1},
[67]={item_id=22099,num=1980,is_bind=1},
[68]={item_id=39193,num=1,is_bind=1},
[69]={item_id=22013,num=1,is_bind=1},
[70]={item_id=22578,num=1,is_bind=1},
[71]={item_id=50077,num=1,is_bind=1},
[72]={item_id=30447,num=5,is_bind=1},
[73]={item_id=22532,num=1,is_bind=1},
[74]={item_id=39153,num=1,is_bind=1},
[75]={item_id=26500,num=1,is_bind=1},
[76]={item_id=26515,num=1,is_bind=1},
[77]={item_id=46041,num=1,is_bind=1},
[78]={item_id=43945,num=1,is_bind=1},
[79]={item_id=26415,num=1,is_bind=1},
[80]={item_id=22007,num=1,is_bind=1},
[81]={item_id=37053,num=1,is_bind=1},
[82]={item_id=22099,num=3880,is_bind=1},
[83]={item_id=44495,num=1,is_bind=1},
[84]={item_id=43086,num=58,is_bind=1},
[85]={item_id=26507,num=1,is_bind=1},
[86]={item_id=22099,num=588,is_bind=1},
[87]={item_id=23600,num=1000,is_bind=1},
[88]={item_id=44180,num=40,is_bind=1},
[89]={item_id=43078,num=10,is_bind=1},
[90]={item_id=44501,num=50,is_bind=1},
[91]={item_id=37052,num=1,is_bind=1},
[92]={item_id=22099,num=880,is_bind=1},
[93]={item_id=43086,num=18,is_bind=1},
[94]={item_id=26505,num=1,is_bind=1},
[95]={item_id=26344,num=200,is_bind=1},
[96]={item_id=22099,num=188,is_bind=1},
[97]={item_id=23600,num=648,is_bind=1},
[98]={item_id=44180,num=20,is_bind=1},
[99]={item_id=44072,num=10,is_bind=1},
[100]={item_id=44501,num=20,is_bind=1},
[101]={item_id=37051,num=1,is_bind=1},
[102]={item_id=22099,num=5880,is_bind=1},
[103]={item_id=44496,num=1,is_bind=1},
[104]={item_id=43086,num=88,is_bind=1},
[105]={item_id=26508,num=1,is_bind=1},
[106]={item_id=22099,num=888,is_bind=1},
[107]={item_id=44180,num=50,is_bind=1},
[108]={item_id=48187,num=10,is_bind=1},
[109]={item_id=44501,num=100,is_bind=1},
[110]={item_id=37054,num=1,is_bind=1},
[111]={item_id=22099,num=1880,is_bind=1},
[112]={item_id=44185,num=1,is_bind=1},
[113]={item_id=43086,num=28,is_bind=1},
[114]={item_id=26506,num=1,is_bind=1},
[115]={item_id=22099,num=288,is_bind=1},
[116]={item_id=44180,num=30,is_bind=1},
[117]={item_id=43077,num=10,is_bind=1},
[118]={item_id=44501,num=30,is_bind=1},
[119]={item_id=37050,num=1,is_bind=1},
[120]={item_id=22099,num=380,is_bind=1},
[121]={item_id=43086,num=5,is_bind=1},
[122]={item_id=26504,num=1,is_bind=1},
[123]={item_id=26344,num=100,is_bind=1},
[124]={item_id=22099,num=88,is_bind=1},
[125]={item_id=23600,num=328,is_bind=1},
[126]={item_id=44180,num=10,is_bind=1},
[127]={item_id=44072,num=3,is_bind=1},
[128]={item_id=44501,num=10,is_bind=1},
[129]={item_id=22099,num=10,is_bind=1},
[130]={item_id=38751,num=1,is_bind=1},
[131]={item_id=37095,num=1,is_bind=1},
[132]={item_id=38153,num=1,is_bind=1},
[133]={item_id=38765,num=1,is_bind=1},
[134]={item_id=37477,num=1,is_bind=1},
[135]={item_id=37532,num=1,is_bind=1},
[136]={item_id=37291,num=1,is_bind=1},
[137]={item_id=37796,num=1,is_bind=1},
[138]={item_id=37633,num=1,is_bind=1},
[139]={item_id=38422,num=1,is_bind=1},
[140]={item_id=38155,num=1,is_bind=1},
[141]={item_id=37480,num=1,is_bind=1},
[142]={item_id=38775,num=1,is_bind=1},
[143]={item_id=37549,num=1,is_bind=1},
[144]={item_id=37295,num=1,is_bind=1},
[145]={item_id=37798,num=1,is_bind=1},
[146]={item_id=37665,num=1,is_bind=1},
[147]={item_id=37039,num=1,is_bind=1},
[148]={item_id=38172,num=1,is_bind=1},
[149]={item_id=37492,num=1,is_bind=1},
[150]={item_id=38037,num=1,is_bind=1},
[151]={item_id=37556,num=1,is_bind=1},
[152]={item_id=37966,num=1,is_bind=1},
[153]={item_id=37864,num=1,is_bind=1},
[154]={item_id=37680,num=1,is_bind=1},
[155]={item_id=37146,num=1,is_bind=1},
[156]={item_id=38176,num=1,is_bind=1},
[157]={item_id=37497,num=1,is_bind=1},
[158]={item_id=38073,num=1,is_bind=1},
[159]={item_id=37558,num=1,is_bind=1},
[160]={item_id=37975,num=1,is_bind=1},
[161]={item_id=37871,num=1,is_bind=1},
[162]={item_id=37688,num=1,is_bind=1},
[163]={item_id=30805,num=1,is_bind=1},
[164]={item_id=40226,num=2,is_bind=1},
[165]={item_id=37627,num=1,is_bind=1},
[166]={item_id=22099,num=8880,is_bind=1},
[167]={item_id=26363,num=10,is_bind=1},
[168]={item_id=26362,num=30,is_bind=1},
[169]={item_id=26361,num=30,is_bind=1},
[170]={item_id=22599,num=1,is_bind=1},
[171]={item_id=22099,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
suit_shop={
{show_skilll_id=6090,},
{seq=1,rmb_seq=1,price=1888,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},suit_name="水墨戮神",model_show_itemid=37030,ui_scene_config_index=19,model_show_seq=1,theme_seq=1,zjtq_title="a3_tq_bt_4",zjtq_normal_bg="a3_tq_zj_yq2_1",zjtq_select_bg="a3_tq_zj_yq2_2",zjtq_reward_bg="a3_tq_bt_di_3",zjtq_button_bg="a3_tq_btn_2",buy_txt="#4a3a09",button_effect="UI_zjtq_smls",button_effect_xia="UI_zjtq_smls_xia",left_txt_effect="UI_TQ_smls_title",right_show_type=1,},
{seq=2,rmb_seq=2,price=3888,reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[3],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11]},suit_name="夜影幽灵",model_show_itemid=18819,ui_scene_config_index=20,model_show_seq=2,theme_seq=2,zjtq_title="a3_tq_bt_5",zjtq_normal_bg="a3_tq_zj_yq3_1",zjtq_select_bg="a3_tq_zj_yq3_2",zjtq_reward_bg="a3_tq_bt_di_4",zjtq_button_bg="a3_tq_btn_3",need_show_role=1,need_mount=1,buy_txt="#ffffff",button_effect="UI_zjtq_yyyl",button_effect_xia="UI_zjtq_yyll_xia",left_txt_effect="UI_TQ_wgqsx_title",right_show_type=2,}
},

suit_shop_meta_table_map={
},
limit_shop={
[0]={seq=0,consume_score=100,refresh_type=1,buy_limit=999,reward_type=1,reward_weight=100,is_grand_prize=1,min_open_server_day=2,},
[1]={seq=1,consume_score=24000,reward_item={[0]=item_table[12]},refresh_type=3,reward_weight=100,is_grand_prize=1,display_order=6,},
[2]={seq=2,consume_score=18000,reward_item={[0]=item_table[13]},display_order=7,},
[3]={seq=3,consume_score=12000,reward_item={[0]=item_table[14]},display_order=8,},
[4]={seq=4,consume_score=6480,reward_item={[0]=item_table[15]},display_order=9,},
[5]={seq=5,consume_score=150,reward_item={[0]=item_table[16]},refresh_type=1,buy_limit=12,reward_weight=100,is_grand_prize=1,display_order=10,},
[6]={seq=6,reward_item={[0]=item_table[17]},buy_limit=5,display_order=11,},
[7]={seq=7,consume_score=688,reward_item={[0]=item_table[18]},buy_limit=4,reward_weight=100,is_grand_prize=1,display_order=12,},
[8]={seq=8,consume_score=1000,reward_item={[0]=item_table[19]},buy_limit=2,reward_weight=100,is_grand_prize=1,display_order=13,},
[9]={seq=9,consume_score=1000,reward_item={[0]=item_table[20]},display_order=2,},
[10]={seq=10,reward_item={[0]=item_table[21]},display_order=4,},
[11]={seq=11,reward_item={[0]=item_table[22]},display_order=17,},
[12]={seq=12,reward_item={[0]=item_table[23]},display_order=18,},
[13]={seq=13,reward_item={[0]=item_table[24]},display_order=19,},
[14]={seq=14,reward_item={[0]=item_table[25]},display_order=20,},
[15]={seq=15,reward_item={[0]=item_table[26]},display_order=21,},
[16]={seq=16,reward_item={[0]=item_table[27]},display_order=22,},
[17]={seq=17,reward_item={[0]=item_table[28]},display_order=23,},
[18]={seq=18,reward_item={[0]=item_table[29]},display_order=24,},
[19]={seq=19,reward_item={[0]=item_table[30]},display_order=25,},
[20]={seq=20,reward_item={[0]=item_table[31]},display_order=26,},
[21]={seq=21,consume_score=1000,reward_item={[0]=item_table[32]},display_order=3,},
[22]={seq=22,consume_score=2000,reward_item={[0]=item_table[33]},level_show=145,display_order=5,},
[23]={seq=23,consume_score=3000,reward_item={[0]=item_table[34]},level_show=200,display_order=27,},
[24]={seq=24,consume_score=4000,reward_item={[0]=item_table[35]},level_show=250,display_order=28,},
[25]={seq=25,consume_score=5000,reward_item={[0]=item_table[36]},min_open_server_day=2,level_show=300,display_order=29,},
[26]={seq=26,consume_score=6000,reward_item={[0]=item_table[37]},min_open_server_day=4,level_show=350,display_order=30,},
[27]={seq=27,consume_score=7500,reward_item={[0]=item_table[38]},min_open_server_day=5,level_show=420,display_order=31,},
[28]={seq=28,consume_score=9000,reward_item={[0]=item_table[39]},min_open_server_day=8,level_show=520,display_order=32,},
[29]={seq=29,consume_score=10500,reward_item={[0]=item_table[40]},min_open_server_day=13,level_show=620,display_order=33,},
[30]={seq=30,consume_score=12000,reward_item={[0]=item_table[41]},min_open_server_day=23,level_show=720,display_order=34,},
[31]={seq=31,consume_score=13500,reward_item={[0]=item_table[42]},min_open_server_day=36,level_show=820,display_order=35,},
[32]={seq=32,consume_score=15000,reward_item={[0]=item_table[43]},min_open_server_day=54,level_show=920,display_order=36,},
[33]={seq=33,consume_score=960,reward_item={[0]=item_table[44]},buy_limit=10,reward_type=1,reward_weight=100,is_grand_prize=1,display_order=37,},
[34]={seq=34,consume_score=45,reward_item={[0]=item_table[45]},refresh_type=1,reward_type=1,reward_weight=100,is_grand_prize=1,display_order=38,},
[35]={seq=35,consume_score=90,reward_item={[0]=item_table[46]},display_order=39,},
[36]={seq=36,consume_score=480,reward_item={[0]=item_table[47]},refresh_type=1,buy_limit=5,reward_weight=20,is_grand_prize=1,display_order=40,},
[37]={seq=37,consume_score=960,reward_item={[0]=item_table[48]},refresh_type=1,buy_limit=5,reward_weight=1000,display_order=41,},
[38]={seq=38,reward_item={[0]=item_table[49]},reward_weight=100,display_order=42,},
[39]={seq=39,reward_item={[0]=item_table[50]},display_order=43,},
[40]={seq=40,reward_item={[0]=item_table[51]},display_order=44,},
[41]={seq=41,reward_item={[0]=item_table[52]},refresh_type=0,buy_limit=0,reward_weight=1000,display_order=45,},
[42]={seq=42,reward_item={[0]=item_table[53]},display_order=46,},
[43]={seq=43,reward_item={[0]=item_table[54]},display_order=47,},
[44]={seq=44,reward_item={[0]=item_table[55]},reward_weight=1000,display_order=48,},
[45]={seq=45,reward_item={[0]=item_table[56]},display_order=49,},
[46]={seq=46,consume_score=75,reward_item={[0]=item_table[57]},refresh_type=1,buy_limit=10,display_order=50,},
[47]={seq=47,reward_item={[0]=item_table[58]},display_order=51,},
[48]={seq=48,reward_item={[0]=item_table[59]},display_order=52,},
[49]={seq=49,reward_item={[0]=item_table[60]},display_order=53,},
[50]={seq=50,consume_score=150,reward_item={[0]=item_table[61]},refresh_type=2,display_order=54,},
[51]={seq=51,consume_score=450,reward_item={[0]=item_table[62]},refresh_type=1,buy_limit=2,display_order=15,},
[52]={seq=52,consume_score=75,reward_item={[0]=item_table[63]},display_order=14,},
[53]={seq=53,consume_score=45,reward_item={[0]=item_table[64]},display_order=16,}
},

limit_shop_meta_table_map={
[49]=50,	-- depth:1
[48]=50,	-- depth:1
[10]=22,	-- depth:1
[11]=23,	-- depth:1
[12]=24,	-- depth:1
[39]=41,	-- depth:1
[40]=41,	-- depth:1
[42]=41,	-- depth:1
[43]=41,	-- depth:1
[47]=46,	-- depth:1
[38]=41,	-- depth:1
[53]=51,	-- depth:1
[13]=25,	-- depth:1
[52]=51,	-- depth:1
[15]=27,	-- depth:1
[16]=28,	-- depth:1
[14]=26,	-- depth:1
[17]=29,	-- depth:1
[18]=30,	-- depth:1
[19]=31,	-- depth:1
[20]=32,	-- depth:1
[2]=1,	-- depth:1
[3]=1,	-- depth:1
[44]=46,	-- depth:1
[45]=44,	-- depth:2
[6]=7,	-- depth:1
[4]=1,	-- depth:1
[35]=34,	-- depth:1
},
limit_shop_refresh={
{},
{min_count=10000,max_count=999999,}
},

limit_shop_refresh_meta_table_map={
},
tequan={
[0]={seq=0,privilege_type=1,back_id=0,show_role_model=0,model_show_itemid=37048,display_pos="-255|-80",display_scale=0.6,score_doubling="75,2|93,2|77,2|76,2",title="a3_yyg_ysz4",icon="a3_yyg_bj14",rebate_lable="返利1000%",},
[1]={seq=1,privilege_type=1,name="珍藏特权",rmb_seq=1,price=98,reward_item={[0]=item_table[65],[1]=item_table[66]},day_reward_item={[0]=item_table[65],[1]=item_table[66]},back_id=1,model_show_itemid=37048,display_pos="-255|-80",display_scale=0.6,score_doubling="75,3|93,3|77,3|76,3",title="a3_yyg_ysz5",icon="a3_yyg_bj15",rebate_lable="返利1000%",model_show_seq=1,theme_seq=1,},
[2]={seq=2,name="典藏特权",rmb_seq=2,price=198,reward_item={[0]=item_table[67],[1]=item_table[68]},day_reward_item={[0]=item_table[67],[1]=item_table[68]},back_id=2,score_doubling="75,4|93,4|77,4|76,4",title="a3_yyg_ysz6",icon="a3_yyg_bj16",model_show_seq=2,theme_seq=2,need_show_role=1,need_mount=1,},
[3]={seq=3,privilege_type=2,name="哪吒特权",rmb_seq=3,price=500,reward_item={[0]=item_table[69],[1]=item_table[70],[2]=item_table[71],[3]=item_table[72],[4]=item_table[73],[5]=item_table[74],[6]=item_table[75],[7]=item_table[76],[8]=item_table[77],[9]=item_table[78],[10]=item_table[79],[11]=item_table[80]},is_can_get=0,day_reward_item={},back_id=3,show_role_model=0,model_show_itemid=37048,display_pos="-255|-80",display_scale=0.6,score_doubling="75,5|93,5|77,5|76,5",model_show_seq=3,theme_seq=3,need_show_role="",need_mount="",},
[4]={seq=4,last_seq=7,name="赤虹龙魂",add_num=5000,copy_drop="75,5|93,5|77,5",rmb_seq=4,one_key_recharge_index=3,show_reward=37053,price=388,reward_item={[0]=item_table[81],[1]=item_table[82],[2]=item_table[83],[3]=item_table[84],[4]=item_table[85]},day_reward_item={[0]=item_table[86],[1]=item_table[87],[2]=item_table[88],[3]=item_table[89],[4]=item_table[90]},bg="a3_yyg_zztq_tqk4",effect_name="UI_yytq_zuanshi",sign_icon="a3_yyg_zztq_tqd4",privilege_lable="获得<color=#fa79bf>绝世万魂幡道具</color>|绝版图鉴:<color=#fa79bf>羿射日墟</color>|天裳仙衣抽奖道具<color=#fa79bf>x200</color>|每日领取<color=#fa79bf>300积分</color>|每日领取<color=#fa79bf>10000灵玉</color>",model_show_seq=7,theme_seq=7,zztq_title="a3_tq_bt_1_3",zztq_right_title="a3_tq_bt_2_3",zztq_icon="a3_tq_mc_l_icon3",zztq_icon_di="a3_tq_mc_l_di_4",desc1="财神爷额度<color=#99ffbb>+2000</color>",desc2="激活特殊坐骑-<color=#99ffbb>赤虹龙魂</color>",desc3="BOSS财神票爆率<color=#99ffbb>+500%</color>",},
[5]={seq=5,last_seq=8,name="苍青龙魂",add_num=1500,copy_drop="75,3|93,3|77,3",rmb_seq=5,one_key_recharge_index=1,show_reward=37052,price=88,reward_item={[0]=item_table[91],[1]=item_table[92],[2]=item_table[93],[3]=item_table[94],[4]=item_table[95]},day_reward_item={[0]=item_table[96],[1]=item_table[97],[2]=item_table[98],[3]=item_table[99],[4]=item_table[100]},bg="a3_yyg_zztq_tqk2",effect_name="UI_yytq_huangjin",sign_icon="a3_yyg_zztq_tqd2",privilege_lable="获得<color=#fa79bf>绝版法宝</color>|绝版图鉴:<color=#fa79bf>提丰枷锁</color>|珍惜材料:<color=#fa79bf>须弥·奇缘石</color>|天裳仙衣抽奖道具<color=#fa79bf>x100</color>|每日领取<color=#fa79bf>100积分</color>|每日领取<color=#fa79bf>3280灵玉</color>",model_show_seq=5,theme_seq=5,zztq_title="a3_tq_bt_1_5",zztq_right_title="a3_tq_bt_2_2",zztq_icon="a3_tq_mc_l_icon2",zztq_icon_di="a3_tq_mc_l_di_3",desc1="财神爷额度<color=#99ffbb>+1000</color>",desc2="激活特殊坐骑-<color=#99ffbb>苍青龙魂</color>",desc3="BOSS财神票爆率<color=#99ffbb>+300%</color>",},
[6]={seq=6,last_seq=4,name="圣辉龙魂",add_num=8000,copy_drop="75,6|93,6|77,6",rmb_seq=6,one_key_recharge_index=4,show_reward=37051,price=588,reward_item={[0]=item_table[101],[1]=item_table[102],[2]=item_table[103],[3]=item_table[104],[4]=item_table[105]},day_reward_item={[0]=item_table[106],[1]=item_table[87],[2]=item_table[107],[3]=item_table[108],[4]=item_table[109]},bg="a3_yyg_zztq_tqk5",effect_name="UI_yytq_wangzhe",sign_icon="a3_yyg_zztq_tqd5",privilege_lable="获得<color=#fa79bf>绝版珍骑</color>|绝版图鉴:<color=#fa79bf>阿努比斯秤庭</color>|珍惜材料:<color=#fa79bf>鸿蒙·奇缘石</color>|天裳仙衣抽奖道具<color=#fa79bf>x300</color>|每日领取<color=#fa79bf>500积分</color>|每日领取<color=#fa79bf>20000灵玉</color>",model_show_seq=8,theme_seq=8,zztq_title="a3_tq_bt_1_2",zztq_right_title="a3_tq_bt_2",zztq_icon="a3_tq_mc_l_icon1",zztq_icon_di="a3_tq_mc_l_di_2",desc1="财神爷额度<color=#99ffbb>+3000</color>",desc2="激活特殊坐骑-<color=#99ffbb>圣辉龙魂</color>",desc3="BOSS财神票爆率<color=#99ffbb>+600%</color>",},
[7]={seq=7,last_seq=5,name="雷霆龙魂",add_num=3000,copy_drop="75,4|93,4|77,4",rmb_seq=7,one_key_recharge_index=2,show_reward=37054,price=188,reward_item={[0]=item_table[110],[1]=item_table[111],[2]=item_table[112],[3]=item_table[113],[4]=item_table[114]},day_reward_item={[0]=item_table[115],[1]=item_table[87],[2]=item_table[116],[3]=item_table[117],[4]=item_table[118]},bg="a3_yyg_zztq_tqk3",effect_name="UI_yytq_bojin",sign_icon="a3_yyg_zztq_tqd3",privilege_lable="获得<color=#fa79bf>绝版时装+武器</color>|绝版图鉴:<color=#fa79bf>克苏鲁之翼</color>|天裳仙衣抽奖道具<color=#fa79bf>x150</color>|每日领取<color=#fa79bf>200积分</color>|每日领取<color=#fa79bf>6480灵玉</color>",model_show_seq=6,theme_seq=6,zztq_title="a3_tq_bt_1_4",zztq_right_title="a3_tq_bt_2_4",zztq_icon="a3_tq_mc_l_icon4",zztq_icon_di="a3_tq_mc_l_di_5",desc1="财神爷额度<color=#99ffbb>+1500</color>",desc2="激活特殊坐骑-<color=#99ffbb>雷霆龙魂</color>",desc3="BOSS财神票爆率<color=#99ffbb>+400%</color>",},
[8]={seq=8,name="冰脉龙魂",add_num=500,copy_drop="75,2|93,2|77,2",rmb_seq=8,one_key_recharge_index=0,show_reward=37050,reward_item={[0]=item_table[119],[1]=item_table[120],[2]=item_table[121],[3]=item_table[122],[4]=item_table[123]},day_reward_item={[0]=item_table[124],[1]=item_table[125],[2]=item_table[126],[3]=item_table[127],[4]=item_table[128]},back_id=4,bg="a3_yyg_zztq_tqk1",effect_name="UI_yytq_baiyin",sign_icon="a3_yyg_zztq_tqd1",privilege_lable="获得<color=#fa79bf>精美头像框</color>|绝版图鉴:<color=#fa79bf>巴别残垣</color>|珍惜材料:<color=#fa79bf>绝世·奇缘石</color>|天裳仙衣抽奖道具<color=#fa79bf>x50</color>|每日领取<color=#fa79bf>50积分</color>|每日领取<color=#fa79bf>1280灵玉</color>",model_show_seq=4,theme_seq=4,zztq_title="a3_tq_bt_1_1",zztq_right_title="a3_tq_bt_2_1",zztq_icon="a3_tq_mc_l_icon",zztq_icon_di="a3_tq_mc_l_di_1",desc1="财神爷额度<color=#99ffbb>+500</color>",desc2="激活特殊坐骑-<color=#99ffbb>冰脉龙魂</color>",desc3="BOSS财神票爆率<color=#99ffbb>+200%</color>",}
},

tequan_meta_table_map={
[2]=0,	-- depth:1
},
choose_attr={
[0]={seq=0,},
[1]={seq=1,},
[2]={seq=2,base_choose_attr="102:300",per_choose_attr="175:100",},
[3]={seq=3,base_choose_attr="103:400",per_choose_attr="179:100",},
[4]={seq=4,base_choose_attr="104:100",per_choose_attr="170:100",}
},

choose_attr_meta_table_map={
},
initial_panel={
{display_scale=1.1,},
{index=2,display_pos="240|80",display_scale=0.7,},
{grade=1,model_show_itemid=38422,display_pos="330|-150",display_scale=1.2,},
{grade=1,index=2,model_show_itemid=38422,display_pos="340|-50",},
{grade=2,model_show_itemid=37039,display_pos="190|-130",display_scale=1,},
{grade=2,index=2,model_show_itemid=37039,display_pos="240|-90",},
{grade=3,model_show_itemid=37146,display_pos="310|-130",rotation="0|40|0",},
{grade=3,model_show_itemid=37146,display_pos="340|-80",rotation="0|55|0",}
},

initial_panel_meta_table_map={
[7]=1,	-- depth:1
[8]=6,	-- depth:1
},
convert={
{need_item="26042,1;27741,20",limit=10,},
{seq=1,need_item="26042,10;27740,10",reward_item={[0]=item_table[129]},limit=3,},
{seq=2,need_item="26042,1",need_score=1,reward_item={[0]=item_table[130]},},
{seq=3,need_score=8000,reward_item={[0]=item_table[131]},},
{seq=4,reward_item={[0]=item_table[132]},},
{seq=5,reward_item={[0]=item_table[133]},},
{seq=6,need_item="27741,400",need_score=4000,reward_item={[0]=item_table[134]},},
{seq=7,reward_item={[0]=item_table[135]},},
{seq=8,need_item="27741,300",need_score=1500,reward_item={[0]=item_table[136]},},
{seq=9,need_item="27741,200",need_score=1000,reward_item={[0]=item_table[137]},},
{seq=10,reward_item={[0]=item_table[138]},},
{grade=1,},
{grade=1,},
{seq=2,reward_item={[0]=item_table[139]},},
{grade=1,reward_item={[0]=item_table[140]},},
{seq=4,reward_item={[0]=item_table[141]},},
{grade=1,reward_item={[0]=item_table[142]},},
{seq=6,reward_item={[0]=item_table[143]},},
{grade=1,reward_item={[0]=item_table[144]},},
{seq=8,reward_item={[0]=item_table[145]},},
{grade=1,reward_item={[0]=item_table[146]},},
{grade=2,},
{grade=2,},
{seq=2,reward_item={[0]=item_table[147]},},
{grade=2,reward_item={[0]=item_table[148]},},
{seq=4,reward_item={[0]=item_table[149]},},
{grade=2,reward_item={[0]=item_table[150]},},
{seq=6,reward_item={[0]=item_table[151]},},
{grade=2,reward_item={[0]=item_table[152]},},
{grade=2,seq=8,reward_item={[0]=item_table[153]},},
{grade=2,reward_item={[0]=item_table[154]},},
{grade=3,},
{grade=3,},
{grade=3,reward_item={[0]=item_table[155]},},
{grade=3,reward_item={[0]=item_table[156]},},
{seq=4,reward_item={[0]=item_table[157]},},
{grade=3,reward_item={[0]=item_table[158]},},
{seq=6,reward_item={[0]=item_table[159]},},
{grade=3,reward_item={[0]=item_table[160]},},
{grade=3,reward_item={[0]=item_table[161]},},
{grade=3,reward_item={[0]=item_table[162]},}
},

convert_meta_table_map={
[5]=4,	-- depth:1
[32]=1,	-- depth:1
[22]=32,	-- depth:2
[12]=22,	-- depth:3
[25]=4,	-- depth:1
[24]=25,	-- depth:2
[34]=24,	-- depth:3
[35]=4,	-- depth:1
[15]=4,	-- depth:1
[14]=15,	-- depth:2
[8]=9,	-- depth:1
[6]=7,	-- depth:1
[11]=10,	-- depth:1
[39]=8,	-- depth:2
[30]=10,	-- depth:1
[38]=39,	-- depth:3
[37]=6,	-- depth:2
[36]=37,	-- depth:3
[33]=2,	-- depth:1
[31]=10,	-- depth:1
[29]=8,	-- depth:2
[21]=10,	-- depth:1
[27]=6,	-- depth:2
[26]=27,	-- depth:3
[23]=33,	-- depth:2
[40]=30,	-- depth:2
[20]=21,	-- depth:2
[19]=8,	-- depth:2
[18]=19,	-- depth:3
[17]=6,	-- depth:2
[16]=17,	-- depth:3
[13]=23,	-- depth:3
[28]=29,	-- depth:3
[41]=10,	-- depth:1
},
grade={
{}
},

grade_meta_table_map={
},
init_score={
[1]={week=1,},
[2]={week=2,init_score=2500,}
},

init_score_meta_table_map={
},
extra_score={
{param1=680,desc_up="充值680元存额+1000",},
{seq=1,param1=1280,desc_up="充值1280元存额+1000",desc_up2="充值<color=#3c8652>128</color>元获得存额+<color=#3c8652>1000</color>",},
{seq=2,param1=1980,desc_up="充值1980元存额+1000",desc_up2="充值<color=#3c8652>198</color>元获得存额+<color=#3c8652>1000</color>",},
{seq=3,param1=3280,desc_up="充值3280元存额+1000",desc_up2="充值<color=#3c8652>328</color>元获得存额+<color=#3c8652>1000</color>",},
{seq=4,param2=3,desc_up2="完成每日累充<color=#3c8652>35000</color>档位利息+<color=#3c8652>5%</color>",},
{seq=5,param2=4,desc_up2="完成每日累充<color=#3c8652>65000</color>档位利息+<color=#3c8652>5%</color>",},
{seq=6,param2=5,desc_up2="完成每日累充<color=#3c8652>120000</color>档位利息+<color=#3c8652>5%</color>",},
{seq=7,task_type=2,param1=3,param2=6,get_type=2,reward=500,desc_up2="完成每日累充<color=#3c8652>200000</color>档位利息+<color=#3c8652>5%</color>",open_panel="EveryDayRechargeView#everyday_recharge_leichong",},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=4,param1=4,},
{week_day=4,param1=4,},
{week_day=4,param1=4,},
{week_day=4,param1=4,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,param1=5,},
{week_day=5,param1=5,},
{week_day=5,param1=5,},
{week_day=5,param1=5,},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{week_day=6,},
{week_day=7,},
{week_day=7,},
{week_day=7,},
{week_day=7,},
{seq=4,param2=3,desc_up2="完成每日累充<color=#3c8652>35000</color>档位利息+<color=#3c8652>5%</color>",},
{seq=5,param2=4,desc_up2="完成每日累充<color=#3c8652>65000</color>档位利息+<color=#3c8652>5%</color>",},
{seq=6,param2=5,desc_up2="完成每日累充<color=#3c8652>120000</color>档位利息+<color=#3c8652>5%</color>",},
{week_day=7,seq=7,task_type=2,param2=6,get_type=2,reward=500,desc_up2="完成每日累充<color=#3c8652>200000</color>档位利息+<color=#3c8652>5%</color>",open_panel="EveryDayRechargeView#everyday_recharge_leichong",},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=1,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week_day=2,},
{week=2,reward=1250,desc_up="充值680元存额+1250",desc_up2="充值<color=#3c8652>68</color>元获得存额+<color=#3c8652>1250</color>",},
{week=2,reward=1250,desc_up="充值1280元存额+1250",desc_up2="充值<color=#3c8652>128</color>元获得存额+<color=#3c8652>1250</color>",},
{week=2,reward=1250,desc_up="充值1980元存额+1250",desc_up2="充值<color=#3c8652>198</color>元获得存额+<color=#3c8652>1250</color>",},
{week=2,reward=1250,desc_up="充值3280元存额+1250",desc_up2="充值<color=#3c8652>328</color>元获得存额+<color=#3c8652>1250</color>",},
{seq=4,param2=3,desc_up2="完成每日累充<color=#3c8652>35000</color>档位利息+<color=#3c8652>6.25%</color>",},
{week=2,seq=5,task_type=2,param2=4,get_type=2,reward=625,desc_up2="完成每日累充<color=#3c8652>65000</color>档位利息+<color=#3c8652>6.25%</color>",open_panel="EveryDayRechargeView#everyday_recharge_leichong",},
{seq=6,param2=5,desc_up2="完成每日累充<color=#3c8652>120000</color>档位利息+<color=#3c8652>6.25%</color>",},
{seq=7,param2=6,desc_up2="完成每日累充<color=#3c8652>200000</color>档位利息+<color=#3c8652>6.25%</color>",},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=4,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,},
{week_day=5,}
},

extra_score_meta_table_map={
[33]=1,	-- depth:1
[25]=33,	-- depth:2
[9]=25,	-- depth:3
[17]=9,	-- depth:4
[28]=4,	-- depth:1
[27]=3,	-- depth:1
[26]=2,	-- depth:1
[18]=26,	-- depth:2
[34]=18,	-- depth:3
[35]=27,	-- depth:2
[36]=28,	-- depth:2
[20]=36,	-- depth:3
[19]=35,	-- depth:3
[57]=1,	-- depth:1
[12]=20,	-- depth:4
[11]=19,	-- depth:4
[10]=34,	-- depth:4
[73]=57,	-- depth:2
[49]=73,	-- depth:3
[41]=49,	-- depth:4
[60]=4,	-- depth:1
[59]=3,	-- depth:1
[58]=2,	-- depth:1
[65]=41,	-- depth:5
[52]=60,	-- depth:2
[51]=59,	-- depth:2
[50]=58,	-- depth:2
[42]=50,	-- depth:3
[44]=52,	-- depth:3
[43]=51,	-- depth:3
[66]=42,	-- depth:4
[67]=43,	-- depth:4
[68]=44,	-- depth:4
[74]=66,	-- depth:5
[75]=67,	-- depth:5
[76]=68,	-- depth:5
[64]=62,	-- depth:1
[61]=62,	-- depth:1
[63]=62,	-- depth:1
[5]=8,	-- depth:1
[39]=40,	-- depth:1
[38]=40,	-- depth:1
[37]=40,	-- depth:1
[6]=8,	-- depth:1
[7]=8,	-- depth:1
[29]=37,	-- depth:2
[32]=40,	-- depth:1
[31]=39,	-- depth:2
[30]=38,	-- depth:2
[69]=61,	-- depth:2
[70]=62,	-- depth:1
[71]=63,	-- depth:2
[46]=70,	-- depth:2
[13]=37,	-- depth:2
[77]=69,	-- depth:3
[78]=46,	-- depth:3
[72]=64,	-- depth:2
[14]=6,	-- depth:2
[22]=6,	-- depth:2
[16]=8,	-- depth:1
[21]=37,	-- depth:2
[45]=77,	-- depth:4
[23]=7,	-- depth:2
[24]=8,	-- depth:1
[56]=72,	-- depth:3
[55]=71,	-- depth:3
[54]=78,	-- depth:4
[53]=45,	-- depth:5
[79]=55,	-- depth:4
[48]=56,	-- depth:4
[47]=79,	-- depth:5
[15]=7,	-- depth:2
[80]=48,	-- depth:5
},
week={
{},
{week_day=4,title="开服4天",},
{week_day=5,title="开服5天",},
{week_day=6,title="开服6天",},
{week_day=7,title="开服7天",},
{week_day=1,title="周一投资",},
{week_day=2,title="周二投资",},
{week=2,title="周三投资",},
{week_day=4,title="周四投资",},
{week_day=5,title="周五投资",}
},

week_meta_table_map={
[6]=8,	-- depth:1
[7]=8,	-- depth:1
[9]=8,	-- depth:1
[10]=8,	-- depth:1
},
yytq_tooltip={
{},
{index=2,str="哎呀，别闹，奖励都给你",},
{index=3,str="够了够了，明天等你来戳一戳",},
{index=4,str="哎哟，走开，戳的受不了了",},
{type=2,str="购买所有特权解锁戳一戳，有惊喜",},
{index=2,str="购买特权解锁无限奖励，每天可领一次",},
{index=3,str="购买所有特权解锁戳一戳，大奖每天领",},
{index=4,str="购买特权，奖励每日白嫖",}
},

yytq_tooltip_meta_table_map={
[6]=5,	-- depth:1
[7]=5,	-- depth:1
[8]=5,	-- depth:1
},
nobility_level={
{add_recharge_volume=100,},
{level=1,uplevel_exp=400,daily_score_limit=200,add_recharge_volume=150,},
{level=2,uplevel_exp=600,daily_score_limit=300,add_recharge_volume=200,},
{level=3,uplevel_exp=800,daily_score_limit=400,add_recharge_volume=250,},
{level=4,uplevel_exp=1000,daily_score_limit=500,add_recharge_volume=300,},
{level=5,uplevel_exp=1200,daily_score_limit=600,add_recharge_volume=400,},
{level=6,uplevel_exp=1400,daily_score_limit=800,add_recharge_volume=500,},
{level=7,uplevel_exp=1600,daily_score_limit=1000,add_recharge_volume=600,},
{level=8,uplevel_exp=2000,daily_score_limit=1200,add_recharge_volume=700,},
{level=9,uplevel_exp=2400,daily_score_limit=1400,},
{level=10,uplevel_exp=2800,daily_score_limit=1600,},
{level=11,uplevel_exp=3200,daily_score_limit=1800,},
{level=12,uplevel_exp=3600,daily_score_limit=2000,},
{level=13,uplevel_exp=4200,daily_score_limit=2200,},
{level=14,uplevel_exp=4800,daily_score_limit=2400,},
{level=15,uplevel_exp=5400,daily_score_limit=2600,},
{level=16,uplevel_exp=6000,daily_score_limit=2800,},
{level=17,uplevel_exp=6800,daily_score_limit=3000,},
{level=18,uplevel_exp=7800,daily_score_limit=3200,},
{level=19,uplevel_exp=9000,daily_score_limit=3400,},
{level=20,uplevel_exp=10600,daily_score_limit=3600,},
{level=21,uplevel_exp=12600,daily_score_limit=3800,},
{level=22,uplevel_exp=15000,daily_score_limit=4000,},
{level=23,uplevel_exp=18200,daily_score_limit=4200,},
{level=24,uplevel_exp=22200,daily_score_limit=4400,},
{level=25,uplevel_exp=27000,daily_score_limit=4600,},
{level=26,uplevel_exp=32600,daily_score_limit=4800,},
{level=27,uplevel_exp=39200,daily_score_limit=5000,},
{level=28,uplevel_exp=46800,daily_score_limit=5200,},
{level=29,uplevel_exp=55400,daily_score_limit=5400,},
{level=30,uplevel_exp=65000,daily_score_limit=5600,},
{level=31,uplevel_exp=75600,daily_score_limit=5800,}
},

nobility_level_meta_table_map={
},
nobility_task={
{seq=0,show_type=1,name="珍藏特权",star_num=5,task_type=3,param1=0,score=100,is_score_limit=0,bg="a3_yyg_yygz_hdk2_1",open_param="YanYuGePrivilegeView#yanyuge_privilege_yytq",},
{only_seq=2,seq=1,show_type=1,name="特藏特权",star_num=5,task_type=3,score=200,exp=200,is_score_limit=0,bg="a3_yyg_yygz_hdk2_2",open_param="YanYuGePrivilegeView#yanyuge_privilege_yytq",},
{only_seq=3,seq=2,name="典藏特权",param1=2,score=500,exp=500,bg="a3_yyg_yygz_hdk2_3",},
{only_seq=4,seq=3,name="完成主线任务:征讨魔界",param1=200,desc="协助师姐深入魔族深处，血洗魔族还江湖清净",bg="a3_yyg_xfnc_rwk4",},
{only_seq=5,seq=4,name="完成主线任务:初入烟雨城",param1=800,desc="进入繁华烟雨城，赏尽烟雨楼阁美景",bg="a3_yyg_xfnc_rwk3",},
{only_seq=6,seq=5,name="完成主线任务:九重劫塔",task_type=5,param1=920,desc="历练千劫，收复凶险幻兽伴君遨游江湖",bg="a3_yyg_xfnc_rwk2",open_param="main_view",},
{only_seq=7,seq=6,show_type=3,name="等级提升至100级",task_type=6,param1=100,exp=50,is_score_limit=0,desc="完成每日任务提升等级，获得丰富奖励",bg="a3_yyg_xfnc_rwk5",show_batch=1,open_param="fubenpanel#fubenpanel_exp",},
{only_seq=8,seq=7,name="查看冲榜活动",param1=2,desc="完成冲榜任务，领全新炫酷坐骑畅游江湖",bg="a3_yyg_xfnc_rwk1",open_param="kf_activity_view",},
{only_seq=9,seq=8,name="财神爷充值1000灵玉",task_type=10,param1=1000,desc="灵玉不够花？怎么办？找财神爷！",bg="a3_yyg_xfnc_rwk6",open_param="recharge_volume",},
{only_seq=10,seq=9,show_type=3,name="查看0元购",task_type=8,exp=50,is_score_limit=0,desc="0元可获得奇珍异宝，快来看看吧！",bg="a3_yyg_xfnc_rwk7",show_batch=2,open_param="LayoutZeroBuyView",},
{only_seq=11,seq=10,name="贵族达1级",task_type=9,desc="提升贵族等级，解锁更多特权，奖励多多！",bg="a3_yyg_xfnc_rwk8",open_param="recharge#recharge_vip",},
{end_day=1,only_seq=12,name="财神充值<color=#99FFBB>%s</color>/1万玉",star_num=3,task_type=10,param2=10000,score=50,open_param="recharge_volume",},
{only_seq=13,seq=12,name="今日消耗<color=#99FFBB>%s</color>/1万玉",task_type=11,open_param="market#shop_limit",},
{end_day=1,only_seq=14,seq=13,task_type=12,score=50,open_param="FashionExchangeShopView#fashion_exchange_yanyu",},
{open_day=2,end_day=3,only_seq=15,score=50,},
{open_day=2,end_day=3,only_seq=16,score=80,},
{open_day=2,end_day=3,only_seq=17,score=120,},
{open_day=2,end_day=3,only_seq=18,seq=14,},
{open_day=2,end_day=3,only_seq=19,seq=15,},
{open_day=2,end_day=3,only_seq=20,seq=16,},
{open_day=4,end_day=7,only_seq=21,score=125,},
{open_day=4,end_day=7,only_seq=22,score=200,},
{open_day=4,end_day=7,only_seq=23,score=300,},
{only_seq=24,seq=14,name="财神爷充值<color=#99FFBB>%s</color>/1.2万灵玉",task_type=10,open_param="recharge_volume",},
{open_day=4,end_day=7,only_seq=25,name="今日消耗<color=#99FFBB>%s</color>/1.2万灵玉",score=125,},
{open_day=4,end_day=7,only_seq=26,score=125,},
{open_day=8,end_day=14,only_seq=27,score=450,},
{open_day=8,end_day=14,only_seq=28,score=720,},
{open_day=8,end_day=14,only_seq=29,score=1080,},
{open_day=8,end_day=14,only_seq=30,score=450,},
{open_day=8,end_day=14,only_seq=31,score=450,},
{open_day=8,end_day=14,only_seq=32,score=450,},
{open_day=15,end_day=30,only_seq=33,score=600,},
{open_day=15,end_day=30,only_seq=34,score=960,},
{open_day=15,end_day=30,only_seq=35,score=1440,},
{only_seq=36,seq=14,name="财神充值<color=#99FFBB>%s</color>/1.5万玉",task_type=10,open_param="recharge_volume",},
{only_seq=37,seq=15,name="今日消耗<color=#99FFBB>%s</color>/1.5万玉",task_type=11,param2=15000,open_param="market#shop_limit",},
{open_day=15,end_day=30,only_seq=38,score=600,},
{open_day=31,only_seq=39,name="招财进宝抽奖<color=#99FFBB>%s</color>/1次",star_num=3,score=1000,},
{open_day=31,only_seq=40,seq=12,name="招财进宝抽奖<color=#99FFBB>%s</color>/2次",param2=2,score=1600,exp=150,},
{open_day=31,only_seq=41,seq=13,name="招财进宝抽奖<color=#99FFBB>%s</color>/8次",star_num=5,param2=8,score=2400,exp=250,},
{only_seq=42,seq=14,name="财神充值<color=#99FFBB>%s</color>/2万玉",task_type=10,param2=20000,open_param="recharge_volume",},
{only_seq=43,seq=15,name="今日消耗<color=#99FFBB>%s</color>/2万玉",task_type=11,open_param="market#shop_limit",},
{open_day=31,only_seq=44,seq=16,task_type=12,score=1000,open_param="FashionExchangeShopView#fashion_exchange_yanyu",}
},

nobility_task_meta_table_map={
[33]=39,	-- depth:1
[27]=39,	-- depth:1
[21]=39,	-- depth:1
[15]=39,	-- depth:1
[38]=44,	-- depth:1
[32]=44,	-- depth:1
[26]=44,	-- depth:1
[20]=14,	-- depth:1
[34]=40,	-- depth:1
[28]=40,	-- depth:1
[22]=40,	-- depth:1
[16]=40,	-- depth:1
[17]=41,	-- depth:1
[42]=39,	-- depth:1
[13]=12,	-- depth:1
[43]=42,	-- depth:2
[23]=41,	-- depth:1
[35]=41,	-- depth:1
[29]=41,	-- depth:1
[37]=33,	-- depth:2
[36]=37,	-- depth:3
[31]=37,	-- depth:3
[30]=36,	-- depth:4
[25]=37,	-- depth:3
[24]=25,	-- depth:4
[19]=13,	-- depth:2
[18]=12,	-- depth:1
[11]=10,	-- depth:1
[8]=10,	-- depth:1
[6]=7,	-- depth:1
[5]=6,	-- depth:2
[4]=6,	-- depth:2
[3]=2,	-- depth:1
[9]=10,	-- depth:1
},
open_task={
{},
{ID=2,panel="kf_activity_view",}
},

open_task_meta_table_map={
},
show_model={
{seq=0,type=6,param1=38,},
{seq=1,param1=30,},
{type=1,param2=31,},
{param1=4,param2=47,},
{param1=45,},
{param1=3,param2=41,},
{seq=3,},
{seq=4,param1=50,},
{seq=5,param1=52,},
{seq=6,param1=54,},
{seq=7,param1=53,},
{seq=8,param1=51,}
},

show_model_meta_table_map={
[4]=3,	-- depth:1
[6]=3,	-- depth:1
[7]=3,	-- depth:1
},
show_model_theme={
{main_scale=1,lc_scale=1,xw_scale=1,mount_scale=1,main_whole_display_pos="-2.5|-2.4|0",main_pos="1|0.7|0",main_rot="0|0|0",main_pos_2_0="1|0.7|0",main_rot_2_0="0|0|0",},
{seq=1,main_scale=1.3,lc_scale=1.3,xw_scale=1.3,mount_scale=1.3,mount_pos="0|0.5|0",mount_rot="0|5|0",main_pos="0|0|0",main_rot="0|-5|0",main_pos_2_0="0|0|0",main_rot_2_0="0|0|0",},
{seq=2,main_scale=0.6,lc_scale=0.6,xw_scale=0.6,mount_scale=0.6,main_whole_display_pos="-2|-5.5|0",main_pos="-2|-0.95|0",main_rot="1|60|0",main_pos_2_0="-2|-0.95|0",main_rot_2_0="1|30|0",},
{seq=3,main_scale=0.56,lc_scale=0.7,xw_scale=0.6,mount_scale=1.2,main_whole_display_pos="-1.2|-1.1|0",main_pos="0|0|0",main_rot="0|13|0",main_pos_2_0="0|0|0",main_rot_2_0="0|0|0",},
{seq=4,main_whole_display_pos="-1.5|-2|0",},
{seq=5,},
{seq=6,main_whole_display_pos="-2|-1.85|0",},
{seq=7,main_whole_display_pos="-2.5|-2.8|0",},
{seq=8,main_whole_display_pos="-2.2|-1.4|0",}
},

show_model_theme_meta_table_map={
},
onekey_tequan={
[0]={seq=0,},
[1]={seq=1,last_seq=8,rmb_seq=1,price=970,last_price=1252,active_tequan="5|7|4|6",},
[2]={seq=2,last_seq=5,rmb_seq=2,price=900,last_price=1164,active_tequan="7|4|6",},
[3]={seq=3,last_seq=7,rmb_seq=3,price=750,last_price=976,active_tequan="4|6",}
},

onekey_tequan_meta_table_map={
},
other_default_table={score=0,refresh_not_limit_count=6,refresh_limit_count=2,role_level=1,real_chongzhi=0,sp_reward_need_num=10,show_reward_num=8,show_item=22099,show_exp_item=91640,suit_item_list_1={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15]},suit_item_list_2={[0]=item_table[163],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18]},init_interest=6000,invest_open_day=3,invest_end_day=7,invest_open_week_day=1,invest_end_week_day=5,invest_end_week_time=2400,active_tequan="0|1|2",active_tequan_reward={[0]=item_table[164]},score_item_id="40046|40047",recharge_add_nobility_exp=0,show_one_key_package_tequan=1,open_server_task_close_level=220,},

suit_shop_default_table={grade=0,seq=0,last_seq=-1,buy_type=1,rmb_type=229,rmb_seq=0,price=888,reward_item={[0]=item_table[165],[1]=item_table[166],[2]=item_table[3],[3]=item_table[167],[4]=item_table[168],[5]=item_table[169]},buy_limit=1,min_open_server_day=1,suit_name="至尊屠龙武",suit_icon="",model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=37627,display_pos="0|0",rotation="0|0|0",display_scale=1,ui_scene_config_index=18,model_show_seq=0,theme_seq=0,zjtq_title="a3_tq_bt_3",zjtq_normal_bg="a3_tq_zj_yq1_1",zjtq_select_bg="a3_tq_zj_yq1_2",zjtq_reward_bg="a3_tq_bt_di_2",zjtq_button_bg="a3_tq_btn_1",need_show_role=0,need_mount=0,buy_txt="#fff7bc",button_effect="UI_zjtq_zztlw",button_effect_xia="UI_zjtq_zztlw_xia",left_txt_effect="UI_TQ_zztlw_title",show_skilll_id=0,right_show_type=0,},

limit_shop_default_table={seq=0,consume_score=500,reward_item={[0]=item_table[163]},refresh_type=4,buy_limit=1,discount="",reward_type=2,reward_weight=500,is_grand_prize=0,min_open_server_day=1,level_show=1,display_order=1,},

limit_shop_refresh_default_table={min_count=1,max_count=9999,consume_count=200,},

tequan_default_table={seq=0,privilege_type=3,last_seq=-1,name="特藏特权",buy_type=1,add_num="",copy_drop="",rmb_type=228,rmb_seq=0,one_key_recharge_index="",show_reward="",price=38,reward_item={[0]=item_table[120],[1]=item_table[170]},is_can_get=1,appe_base_add_per=0,role_base_add_per=0,special_broast=0,is_can_choose=0,day_reward_item={[0]=item_table[120],[1]=item_table[170]},tequan_desc="",back_id=5,show_role_model=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=37050,display_pos="0|0",rotation="0|0|0",display_scale=1,score_doubling="0,0",title="",icon="",bg="",effect_name="",sign_icon="",reward_str="",rebate_lable="",privilege_lable="",recharge_item_daily_limit=0,model_show_seq=0,theme_seq=0,zztq_title="",zztq_right_title="",zztq_icon="",need_show_role=0,need_mount=0,zztq_icon_di="",desc1="",desc2="",desc3="",},

choose_attr_default_table={seq=0,base_choose_attr="101:100",per_choose_attr="174:100",},

initial_panel_default_table={grade=0,index=1,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=37095,display_pos="60|-63",rotation="0|20|0",display_scale=0.9,},

convert_default_table={grade=0,seq=0,need_item="27740,200",need_score=0,reward_item={[0]=item_table[171]},limit=1,},

grade_default_table={grade=0,open_min_day=1,open_max_day=9999,},

init_score_default_table={week=1,init_score=2000,},

extra_score_default_table={week=1,week_day=3,seq=0,task_type=1,param1=6,param2=0,get_type=1,reward=1000,desc_up="",desc_up2="充值<color=#3c8652>68</color>元获得存额+<color=#3c8652>1000</color>",open_panel="recharge#recharge_cz",},

week_default_table={week=1,week_day=3,title="开服3天",jump_title1="每日累充",jump_path1="EveryDayRechargeView",jump_title2="",jump_path2="",},

yytq_tooltip_default_table={type=1,index=1,str="别戳人家啦，好痛痛喔",},

nobility_level_default_table={level=0,uplevel_exp=200,daily_score_limit=100,add_recharge_volume=800,},

nobility_task_default_table={open_day=1,end_day=999,only_seq=1,seq=11,show_type=2,name="消耗任意积分",star_num=4,complete_limit=1,task_type=13,param1=1,param2=1,score=25,exp=100,is_score_limit=1,desc="",bg="",show_batch="",open_param="GodOfWealthView",},

open_task_default_table={ID=1,panel="LayoutZeroBuyView",},

show_model_default_table={seq=2,type=2,param1=1,param2=0,},

show_model_theme_default_table={seq=0,main_scale=1.1,lc_scale=1.1,xw_scale=1.1,mount_scale=1.1,mount_whole_display_pos="0|0",mount_pos="0|0|0",mount_rot="0|0|0",pet_whole_display_pos="0|0",pet_pos="0|0|0",pet_rot="0|0|0",main_whole_display_pos="-2|-2.4|0",main_pos="0.3|-2|0",main_rot="0|10|0",main_pos_2_0="0.3|-2|0",main_rot_2_0="0|-10|0",xw_whole_display_pos="0|0",xw_pos="0|0|0",xw_rot="0|0|0",},

onekey_tequan_default_table={seq=0,last_seq=-1,rmb_type=239,rmb_seq=0,price=999,last_price=1290,active_tequan="8|5|7|4|6",discount="7.8折",}

}

