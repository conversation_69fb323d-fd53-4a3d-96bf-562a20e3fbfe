--动态运营活动_魔王有礼
function OperationActivityView:LoadIndexCallBackMoWang()
    OperationMoWangWGCtrl.Instance:ReqCOAMoWangYouLiDropLimitItemInfo()

	XUI.AddClickEventListener(self.node_list.btn_goto_boss, BindTool.Bind(self.OnClickBtnGotoBoss,self))
	XUI.AddClickEventListener(self.node_list.btn_goto_treasure, BindTool.Bind(self.OnClickBtnGotoTreasure,self))

	if nil == self.mw_display_model then
		self.mw_display_model = OperationActRender.New(self.node_list.mw_display)
		self.mw_display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    self:FlushMoWangViewInfo()
	
	-- local cur_grade = OperationActivityWGData.Instance:GetCurThemeType()
	-- self.node_list.goto_boss_text.text.color = StrToColor(Language.OperationActivity.GradeColorList1[cur_grade])
	-- self.node_list.goto_treasure_text.text.color = StrToColor(Language.OperationActivity.GradeColorList1[cur_grade])
	-- self.node_list.mw_line1.image.color = StrToColor(Language.OperationActivity.GradeColorList1[cur_grade])
	-- self.node_list.mw_line2.image.color = StrToColor(Language.OperationActivity.GradeColorList1[cur_grade])

	local res_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_bg_6")
	local bundle, asset = ResPath.GetRawImagesPNG(res_name)
	self.node_list.mw_bg.raw_image:LoadSprite(bundle, asset)

	res_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_bg_7")
	bundle, asset = ResPath.GetRawImagesPNG(res_name)
	self.node_list.mw_bg2.raw_image:LoadSprite(bundle, asset)

	local param_cfg = OperationMoWangWGData.Instance:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return
	end
	local view_cfg = OperationMoWangWGData.Instance:GetViewCfg(param_cfg.grade)
	self.node_list.goto_treasure_text.text.text = view_cfg.drop_open_text
	
end

function OperationActivityView:ReleaseCallBackMoWang()
    if self.mw_reward_list then
		self.mw_reward_list:DeleteMe()
		self.mw_reward_list = nil
	end

	-- 销毁模型
	if self.mw_display_model then
		self.mw_display_model:DeleteMe()
		self.mw_display_model = nil
	end
end


function OperationActivityView:OnFlushMoWang(param_t)
	local param_cfg = OperationMoWangWGData.Instance:GetParamCfgByServerOpenDay()
	if not IsEmptyTable(param_cfg) and param_cfg.grade then
		local view_cfg = OperationMoWangWGData.Instance:GetViewCfg(param_cfg.grade)
		if not view_cfg then
			return
		end

		self:SetOutsideRuleTips(view_cfg.mw_tip_label)
		self:SetRuleInfo(view_cfg.rule_desc, view_cfg.btn_rule_title)
	end
    self:FlushPreviewRewardMoWang()

end

function OperationActivityView:FlushMoWangViewInfo()
	if not self:IsLoadedIndex(TabIndex.operation_act_mowang_youli) then
		return
	end
	local param_cfg = OperationMoWangWGData.Instance:GetParamCfgByServerOpenDay()
	if not IsEmptyTable(param_cfg) and param_cfg.grade then
		local view_cfg = OperationMoWangWGData.Instance:GetViewCfg(param_cfg.grade)
		self:FlushViewElementMoWang(view_cfg)
		-- self:FlushBossModelMoWang(param_cfg)
	end

	self:FlushMWModel()
end

function OperationActivityView:SetTextStrMoWang(node_name, str)
	if self.node_list[node_name] then
		self.node_list[node_name].text.text = str
	end
end

function OperationActivityView:SetImageMoWang(node_name, res_path, asset_name, is_async, not_set_native_size)
	local node = self.node_list[node_name]
	if node then
		local b, a = res_path(asset_name)
		if is_async then
			node.image:LoadSpriteAsync(b, a, function()
				if not not_set_native_size then
					XUI.ImageSetNativeSize(node)
				end
			end)
		else
			node.image:LoadSprite(b, a, function()
				if not not_set_native_size then
					XUI.ImageSetNativeSize(node)
				end
			end)
		end
	end
end

function OperationActivityView:SetRawImageMoWang(node_name, res_path, asset_name, is_async)
	local node = self.node_list[node_name]
	if node then
		local b, a = res_path(asset_name)
		if is_async then
			node.raw_image:LoadSpriteAsync(b, a, function()
				node.raw_image:SetNativeSize()
			end)
		else
			node.raw_image:LoadSprite(b, a, function()
				node.raw_image:SetNativeSize()
			end)
		end
	end
end

--刷新魔王有礼界面元素(界面显示配置)
function OperationActivityView:FlushViewElementMoWang(view_cfg, param_cfg)
	self:SetOutsideRuleTips(view_cfg.mw_tip_label)

	self.node_list.mw_tip_label.text.text = view_cfg.rule_desc

end

function OperationActivityView:FlushPreviewRewardMoWang()
    if not self:IsLoadedIndex(TabIndex.operation_act_mowang_youli) then
		return
	end
	local rewards = OperationMoWangWGData.Instance:GetMWPreViewReward()
	if rewards ~= nil then
		if not self.mw_reward_list then
			self.mw_reward_list = AsyncListView.New(OperaMoWangItemRender, self.node_list.mw_reward_list)
        end
		self.mw_reward_list:SetDataList(rewards)
	end
end

function OperationActivityView:OnClickBtnTipMoWang()
	local param_cfg = OperationMoWangWGData.Instance:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return
	end
	local view_cfg = OperationMoWangWGData.Instance:GetViewCfg(param_cfg.grade)
	if not view_cfg then
		return
	end

	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(view_cfg.btn_rule_title)
		role_tip:SetContent(view_cfg.rule_desc)
	end
end

function OperationActivityView:FlushMWModel()

	local param_cfg = OperationMoWangWGData.Instance:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return
	end
	local display_data = {}
	display_data.render_type = param_cfg.render_type
	if param_cfg.render_int_param1 and param_cfg.render_int_param1 > 0 then


		display_data.item_id = param_cfg.render_int_param1
	elseif param_cfg.model_bundle_name and param_cfg.model_bundle_name ~= ""
			and param_cfg.model_asset_name and param_cfg.model_asset_name ~= "" then
		display_data.bundle_name = param_cfg.model_bundle_name
		display_data.asset_name = param_cfg.model_asset_name
		
		display_data.model_rt_type = ModelRTSCaleType.L
		-- 模型位置
		if param_cfg.model_pos and param_cfg.model_pos ~= "" then
			local pos_list = string.split(param_cfg.model_pos, "|")
			local pos_x = tonumber(pos_list[1]) or 0
			local pos_y = tonumber(pos_list[2]) or 0
			local pos_z = tonumber(pos_list[3]) or 0
	
			display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
		end
		-- 模型旋转角度
		if param_cfg.model_rot and param_cfg.model_rot ~= "" then
            local rot_list = string.split(param_cfg.model_rot, "|")
            local rot_x = tonumber(rot_list[1]) or 0
            local rot_y = tonumber(rot_list[2]) or 0
            local rot_z = tonumber(rot_list[3]) or 0

            display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
        end
		-- 模型尺寸
		if param_cfg.model_scale then
			display_data.model_adjust_root_local_scale = param_cfg.model_scale
		end
	end

	if param_cfg.whole_display_pos and param_cfg.whole_display_pos ~= "" then
		local pos = Split(param_cfg.whole_display_pos, "|")
		RectTransform.SetAnchoredPosition3DXYZ(self.node_list.mw_display.rect, tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	self.mw_display_model:SetData(display_data)
end


function OperationActivityView:OnClickBtnGotoBoss()
	ViewManager.Instance:Open(GuideModuleName.Boss)
end

function OperationActivityView:OnClickBtnGotoTreasure()
	local param_cfg = OperationMoWangWGData.Instance:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return
	end
	local view_cfg = OperationMoWangWGData.Instance:GetViewCfg(param_cfg.grade)

	FunOpen.Instance:OpenViewNameByCfg(view_cfg.drop_open_panel_name)
end

----------------------------------------------------------------------------

OperaMWTaskItemRender = OperaMWTaskItemRender or BaseClass(BaseRender)

function OperaMWTaskItemRender:LoadCallBack()
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnClickGo, self))
end

function OperaMWTaskItemRender:OnFlush(param_t)
	if not self.data then
		return
	end

	for k,v in pairs(param_t or {"all"}) do
		if k == "times_change" then
			self:FlushTimes()
		else
			self:FlushAll()
		end
	end
end

function OperaMWTaskItemRender:FlushAll()
	local color = OperationActivityWGData.Instance:GetCurThemeColor()
	local bundle, asset = ResPath.GetF2MainUIImage(self.data.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset)
	self.node_list.name.text.text = ToColorStr(self.data.task_name, color)
	self.node_list.task_item_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(self.data.task_item_bg))
    self.node_list.task_item_icon_bg.image:LoadSprite(ResPath.GetOperationActivityImagePath(self.data.task_item_icon_bg))
    
	self.node_list.btn.image:LoadSprite(ResPath.GetF2CommonButtonToggle(self.data.task_item_btn_bg))
	self.node_list.task_item_btn_text.text.text = self.data.task_item_btn_text
	self.node_list.btn_ylq.image:LoadSprite(ResPath.GetF2CommonImages(self.data.complete_flag))
	self.node_list.complete_text.text.text = self.data.complete_text
	self.node_list.finish_left.text.text = ToColorStr(Language.OpertionAcitvity.MoWangYouLi.KillConditionLeft, color)

	if self.data.panel ~= "" then
		XUI.SetButtonEnabled(self.node_list.btn, true)
	else
		XUI.SetButtonEnabled(self.node_list.btn, false)
	end
	local is_show_condition = self.data.is_show_condition == 1
	self.node_list.number_txt:SetActive(is_show_condition)

	self:FlushTimes()
end

function OperaMWTaskItemRender:FlushTimes()
    if not self.node_list.btn then
        return
    end

    local left_times, max_times = BossWGData.Instance:GetBossTimesInfo(self.data.ID)
    if left_times == -1 then
        self.node_list.finish_right.text.text = Language.OpertionAcitvity.MoWangYouLi.Infinite
        self.node_list.btn:SetActive(true)
        if self.data.ID == BOSS_TASK_TYPE.VIP then
	    	self.node_list.btn_ylq:SetActive(false)
    	else
    		self.node_list.btn_ylq:SetActive(true)
	    end
    else
        local can_kill_boss = left_times > 0
        local color = can_kill_boss and "<color=#009621>" or "<color=#ff0000>"
        self.node_list.btn:SetActive(can_kill_boss)
        self.node_list.btn_ylq:SetActive(not can_kill_boss)
        self.node_list.finish_right.text.text = string.format(Language.OpertionAcitvity.MoWangYouLi.KillConditionRight, color, left_times, max_times)
    end
end

function OperaMWTaskItemRender:OnBossTimesChange(boss_type, enter_times, max_times)
	self:Flush("times_change", {boss_type, enter_times, max_times})
end

function OperaMWTaskItemRender:OnClickGo()
	if self.data.panel then
		local param = string.split(self.data.panel,"#")
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end

-------------------------------------------------------------------------------------------

OperaMoWangItemRender = OperaMoWangItemRender or BaseClass(BaseRender)

function OperaMoWangItemRender:__init()
	self.item = ItemCell.New(self.node_list.cell_pos)

	local cur_grade = OperationActivityWGData.Instance:GetCurThemeType()
	self.node_list.limit_text.text.color = StrToColor(Language.OperationActivity.GradeColorList2[cur_grade])

end

function OperaMoWangItemRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
    end
    
end

function OperaMoWangItemRender:OnFlush()
    self.item:SetData(self.data)
    local remain = self.data.activity_person_day_limit - self.data.cur_num
    local color = remain <= 0 and COLOR3B.RED or COLOR3B.GREEN
    self.node_list.limit_text.text.text = string.format(Language.OpertionAcitvity.MoWangYouLi.TodayLimitNum,color, self.data.cur_num, self.data.activity_person_day_limit)
end

