GuildActivityShouHuRank = GuildActivityShouHuRank or BaseClass(SafeBaseView)
local SelectState = {
    Person = 1,
    Corss = 2
}

function GuildActivityShouHuRank:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_shouhu_rank")
	self:SetMaskBg()
end

function GuildActivityShouHuRank:ReleaseCallBack()
    if self.role_list then
        self.role_list:DeleteMe()
        self.role_list = nil
    end

    if self.cross_list then
        self.cross_list:DeleteMe()
        self.cross_list = nil
    end
end


function GuildActivityShouHuRank:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Guild.RankInfoTitle
    self:SetSecondView(nil, self.node_list["size"])

	self.node_list["btn_person"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSwitch, self, SelectState.Person))
	self.node_list["btn_corss"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSwitch, self, SelectState.Corss))

    self.role_list = AsyncListView.New(ShouHuPersonRankItem, self.node_list["role_list"])
    self.cross_list = AsyncListView.New(ShouHuCrossRankItem, self.node_list["cross_list"])
end

function GuildActivityShouHuRank:ShowIndexCallBack()
    self.node_list["btn_person"].toggle.isOn = true
end

function GuildActivityShouHuRank:OnFlush(param)

end

function GuildActivityShouHuRank:OnClickSwitch(state)
    if state == SelectState.Person then
        local data_list = GuildWGData.Instance:GetGeRenRankDataList()
        if not data_list or IsEmptyTable(data_list) then
            data_list = GuildWGData.Instance:GetDefaultPersonRankDataList() --如果没有数据，获取一个默认数据
        end
        self.role_list:SetDataList(data_list)
    elseif state == SelectState.Corss then
        local data_list = GuildWGData.Instance:GetGuildRankDataList()
        if not data_list or IsEmptyTable(data_list) then
            data_list = GuildWGData.Instance:GetDefaultGuildRankDataList() --如果没有数据，获取一个默认数据
        end
        self.cross_list:SetDataList(data_list)
    end
    
end
--------------------------------------------------------------------------------------
ShouHuPersonRankItem = ShouHuPersonRankItem or BaseClass(BaseRender)

function ShouHuPersonRankItem:__init()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
end

function ShouHuPersonRankItem:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function ShouHuPersonRankItem:OnFlush()
    self.node_list["bg"]:SetActive(self.index % 2 == 1)
    self.node_list["rank_img"]:CustomSetActive(self.index <= 3)
    if self.index <= 3 then
        local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_"..self.index)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset)
        self.node_list["rank_img"].image:SetNativeSize()
        self.node_list["rank_num"].text.text = ""
    else
        self.node_list["rank_num"].text.text = self.data.rank_str or self.index
    end
    if self.data.uid and self.data.uid ~= 0 then
        if self.data.total_hurt ~= nil then
            self.node_list["pass_text"].text.text = CommonDataManager.ConverExp(BigNumFormat(self.data.total_hurt))
        end
        self.node_list["pass_text"]:CustomSetActive(true)
        self.node_list["name"]:CustomSetActive(true)
        self.node_list["name"].text.text = self.data.name
    else
        if self.index > 10 then
            self.node_list["pass_text"]:CustomSetActive(false)
            self.node_list["name"]:CustomSetActive(false)
        else
            self.node_list["pass_text"]:CustomSetActive(true)
            self.node_list["name"]:CustomSetActive(true)
            self.node_list["pass_text"].text.text = Language.Guild.SHJingQingQiDai
            self.node_list["name"].text.text = Language.Guild.SHJingQingQiDai
        end
    end
    local data_list = self:GetRewardDataList()
    self.reward_list:SetDataList(data_list)
end

function ShouHuPersonRankItem:GetRewardDataList()
    local data_list = {}
    if self.index > 10 then
        data_list = GuildWGData.Instance:GetPersonAfterTenRankItemRewardList(self.index)
    else
        data_list = GuildWGData.Instance:GetPersonItemRewardByRank(self.index-1)
    end
    return data_list
end

-------------------------------------------------------------------------------------

ShouHuCrossRankItem = ShouHuCrossRankItem or BaseClass(BaseRender)

function ShouHuCrossRankItem:__init()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
end

function ShouHuCrossRankItem:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function ShouHuCrossRankItem:OnFlush()
    self.node_list["bg"]:SetActive(self.index % 2 == 1)
    self.node_list["rank_img"]:CustomSetActive(self.index <= 3)
    if self.index <= 3 then
        local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_"..self.index)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset)
        self.node_list["rank_img"].image:SetNativeSize()
        self.node_list["rank_num"].text.text = ""
    else
        self.node_list["rank_num"].text.text = self.data.rank_str or self.index
    end
    if self.data.guild_id and self.data.guild_id > 0 then
        if self.data.pass_time and self.data.pass_time > 0 then
            self.node_list["pass_text"].text.text = TimeUtil.MSTime(self.data.pass_time)
        end
        self.node_list["pass_text"]:CustomSetActive(true)
        self.node_list["name"]:CustomSetActive(true)
        --如果进入跨服比拼，显示服信息
        if GuildWGData.Instance:GetIsEnterCrossBiPing() then
            local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id)
            self.node_list["name"].text.text = string.format(Language.Guild.SHGuildName, self.data.guild_name, temp_name)
        else
            self.node_list["name"].text.text = self.data.guild_name
        end
    else
        if self.index > 10 then
            self.node_list["pass_text"]:CustomSetActive(false)
            self.node_list["name"]:CustomSetActive(false)
        else
            self.node_list["pass_text"]:CustomSetActive(true)
            self.node_list["name"]:CustomSetActive(true)
            self.node_list["pass_text"].text.text = Language.Guild.SHJingQingQiDai
            self.node_list["name"].text.text = Language.Guild.SHJingQingQiDai
        end
    end
    local data_list = self:GetRewardDataList()
    self.reward_list:SetDataList(data_list)
end

function ShouHuCrossRankItem:GetRewardDataList()
    local data_list = {}
    if self.index > 10 then
        return GuildWGData.Instance:GetGuildAfterTenRankItemRewardList(self.index)
    end
    if self.data.guild_id and self.data.guild_id > 0 then
        return GuildWGData.Instance:GetBestFiveItemList(self.data.item_list)
    end
    return GuildWGData.Instance:GetGuildItemRewardByRank(self.index)
end
