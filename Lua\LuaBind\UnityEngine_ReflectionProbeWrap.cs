﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_ReflectionProbeWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(UnityEngine.ReflectionProbe), typeof(UnityEngine.Behaviour));
		<PERSON><PERSON>ction("Reset", Reset);
		<PERSON><PERSON>("RenderProbe", RenderProbe);
		<PERSON><PERSON>ction("IsFinishedRendering", IsFinishedRendering);
		<PERSON><PERSON>unction("BlendCubemap", BlendCubemap);
		<PERSON><PERSON>Function("UpdateCachedState", UpdateCachedState);
		<PERSON><PERSON>unction("New", _CreateUnityEngine_ReflectionProbe);
		<PERSON><PERSON>ction("__eq", op_Equality);
		L.<PERSON>unction("__tostring", ToLua.op_ToString);
		L.<PERSON>ar("size", get_size, set_size);
		<PERSON><PERSON>("center", get_center, set_center);
		<PERSON><PERSON>("nearClipPlane", get_nearClipPlane, set_nearClipPlane);
		<PERSON><PERSON>("farClipPlane", get_farClipPlane, set_farClip<PERSON>lane);
		<PERSON><PERSON>("intensity", get_intensity, set_intensity);
		L.RegVar("bounds", get_bounds, null);
		L.RegVar("hdr", get_hdr, set_hdr);
		L.RegVar("renderDynamicObjects", get_renderDynamicObjects, set_renderDynamicObjects);
		L.RegVar("shadowDistance", get_shadowDistance, set_shadowDistance);
		L.RegVar("resolution", get_resolution, set_resolution);
		L.RegVar("cullingMask", get_cullingMask, set_cullingMask);
		L.RegVar("clearFlags", get_clearFlags, set_clearFlags);
		L.RegVar("backgroundColor", get_backgroundColor, set_backgroundColor);
		L.RegVar("blendDistance", get_blendDistance, set_blendDistance);
		L.RegVar("boxProjection", get_boxProjection, set_boxProjection);
		L.RegVar("mode", get_mode, set_mode);
		L.RegVar("importance", get_importance, set_importance);
		L.RegVar("refreshMode", get_refreshMode, set_refreshMode);
		L.RegVar("timeSlicingMode", get_timeSlicingMode, set_timeSlicingMode);
		L.RegVar("bakedTexture", get_bakedTexture, set_bakedTexture);
		L.RegVar("customBakedTexture", get_customBakedTexture, set_customBakedTexture);
		L.RegVar("realtimeTexture", get_realtimeTexture, set_realtimeTexture);
		L.RegVar("texture", get_texture, null);
		L.RegVar("textureHDRDecodeValues", get_textureHDRDecodeValues, null);
		L.RegVar("minBakedCubemapResolution", get_minBakedCubemapResolution, null);
		L.RegVar("maxBakedCubemapResolution", get_maxBakedCubemapResolution, null);
		L.RegVar("defaultTextureHDRDecodeValues", get_defaultTextureHDRDecodeValues, null);
		L.RegVar("defaultTexture", get_defaultTexture, null);
		L.RegVar("reflectionProbeChanged", get_reflectionProbeChanged, set_reflectionProbeChanged);
		L.RegVar("defaultReflectionTexture", get_defaultReflectionTexture, set_defaultReflectionTexture);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_ReflectionProbe(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.ReflectionProbe obj = new UnityEngine.ReflectionProbe();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.ReflectionProbe.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Reset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)ToLua.CheckObject(L, 1, typeof(UnityEngine.ReflectionProbe));
			obj.Reset();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RenderProbe(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)ToLua.CheckObject(L, 1, typeof(UnityEngine.ReflectionProbe));
				int o = obj.RenderProbe();
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else if (count == 2)
			{
				UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)ToLua.CheckObject(L, 1, typeof(UnityEngine.ReflectionProbe));
				UnityEngine.RenderTexture arg0 = (UnityEngine.RenderTexture)ToLua.CheckObject<UnityEngine.RenderTexture>(L, 2);
				int o = obj.RenderProbe(arg0);
				LuaDLL.lua_pushinteger(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UnityEngine.ReflectionProbe.RenderProbe");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsFinishedRendering(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)ToLua.CheckObject(L, 1, typeof(UnityEngine.ReflectionProbe));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			bool o = obj.IsFinishedRendering(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BlendCubemap(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 1);
			UnityEngine.Texture arg1 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 3);
			UnityEngine.RenderTexture arg3 = (UnityEngine.RenderTexture)ToLua.CheckObject<UnityEngine.RenderTexture>(L, 4);
			bool o = UnityEngine.ReflectionProbe.BlendCubemap(arg0, arg1, arg2, arg3);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateCachedState(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			UnityEngine.ReflectionProbe.UpdateCachedState();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_size(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Vector3 ret = obj.size;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index size on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_center(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Vector3 ret = obj.center;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index center on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_nearClipPlane(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float ret = obj.nearClipPlane;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index nearClipPlane on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_farClipPlane(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float ret = obj.farClipPlane;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index farClipPlane on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_intensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float ret = obj.intensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index intensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Bounds ret = obj.bounds;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_hdr(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			bool ret = obj.hdr;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index hdr on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderDynamicObjects(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			bool ret = obj.renderDynamicObjects;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderDynamicObjects on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shadowDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float ret = obj.shadowDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_resolution(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			int ret = obj.resolution;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index resolution on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cullingMask(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			int ret = obj.cullingMask;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cullingMask on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_clearFlags(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Rendering.ReflectionProbeClearFlags ret = obj.clearFlags;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index clearFlags on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_backgroundColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Color ret = obj.backgroundColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index backgroundColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_blendDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float ret = obj.blendDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index blendDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_boxProjection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			bool ret = obj.boxProjection;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index boxProjection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_mode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Rendering.ReflectionProbeMode ret = obj.mode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_importance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			int ret = obj.importance;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index importance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_refreshMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Rendering.ReflectionProbeRefreshMode ret = obj.refreshMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index refreshMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_timeSlicingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Rendering.ReflectionProbeTimeSlicingMode ret = obj.timeSlicingMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeSlicingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bakedTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Texture ret = obj.bakedTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bakedTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_customBakedTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Texture ret = obj.customBakedTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index customBakedTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_realtimeTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.RenderTexture ret = obj.realtimeTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index realtimeTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_texture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Texture ret = obj.texture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index texture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textureHDRDecodeValues(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Vector4 ret = obj.textureHDRDecodeValues;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textureHDRDecodeValues on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_minBakedCubemapResolution(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.ReflectionProbe.minBakedCubemapResolution);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_maxBakedCubemapResolution(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushinteger(L, UnityEngine.ReflectionProbe.maxBakedCubemapResolution);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTextureHDRDecodeValues(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.ReflectionProbe.defaultTextureHDRDecodeValues);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultTexture(IntPtr L)
	{
		try
		{
			ToLua.Push(L, UnityEngine.ReflectionProbe.defaultTexture);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_reflectionProbeChanged(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_defaultReflectionTexture(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(System.Action<UnityEngine.Texture>)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_size(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.size = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index size on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_center(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.center = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index center on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_nearClipPlane(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.nearClipPlane = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index nearClipPlane on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_farClipPlane(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.farClipPlane = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index farClipPlane on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_intensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.intensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index intensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_hdr(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.hdr = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index hdr on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_renderDynamicObjects(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.renderDynamicObjects = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderDynamicObjects on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shadowDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.shadowDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shadowDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_resolution(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.resolution = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index resolution on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cullingMask(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.cullingMask = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cullingMask on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_clearFlags(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Rendering.ReflectionProbeClearFlags arg0 = (UnityEngine.Rendering.ReflectionProbeClearFlags)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.ReflectionProbeClearFlags));
			obj.clearFlags = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index clearFlags on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_backgroundColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.backgroundColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index backgroundColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_blendDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.blendDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index blendDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_boxProjection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.boxProjection = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index boxProjection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_mode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Rendering.ReflectionProbeMode arg0 = (UnityEngine.Rendering.ReflectionProbeMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.ReflectionProbeMode));
			obj.mode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index mode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_importance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.importance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index importance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_refreshMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Rendering.ReflectionProbeRefreshMode arg0 = (UnityEngine.Rendering.ReflectionProbeRefreshMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.ReflectionProbeRefreshMode));
			obj.refreshMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index refreshMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_timeSlicingMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Rendering.ReflectionProbeTimeSlicingMode arg0 = (UnityEngine.Rendering.ReflectionProbeTimeSlicingMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.ReflectionProbeTimeSlicingMode));
			obj.timeSlicingMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeSlicingMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_bakedTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			obj.bakedTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bakedTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_customBakedTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			obj.customBakedTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index customBakedTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_realtimeTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.ReflectionProbe obj = (UnityEngine.ReflectionProbe)o;
			UnityEngine.RenderTexture arg0 = (UnityEngine.RenderTexture)ToLua.CheckObject<UnityEngine.RenderTexture>(L, 2);
			obj.realtimeTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index realtimeTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_reflectionProbeChanged(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.ReflectionProbe.reflectionProbeChanged' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.ReflectionProbe'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent> ev = (System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent>)arg0.func;
				UnityEngine.ReflectionProbe.reflectionProbeChanged += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent> ev = (System.Action<UnityEngine.ReflectionProbe,UnityEngine.ReflectionProbe.ReflectionProbeEvent>)arg0.func;
				UnityEngine.ReflectionProbe.reflectionProbeChanged -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_defaultReflectionTexture(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.ReflectionProbe.defaultReflectionTexture' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.ReflectionProbe'");
			}

			if (arg0.op == EventOp.Add)
			{
				System.Action<UnityEngine.Texture> ev = (System.Action<UnityEngine.Texture>)arg0.func;
				UnityEngine.ReflectionProbe.defaultReflectionTexture += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				System.Action<UnityEngine.Texture> ev = (System.Action<UnityEngine.Texture>)arg0.func;
				UnityEngine.ReflectionProbe.defaultReflectionTexture -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

