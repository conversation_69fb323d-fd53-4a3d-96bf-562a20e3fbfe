-- 恭喜获得新道具面板
WorldsNO1GetNewItemView = WorldsNO1GetNewItemView or BaseClass(SafeBaseView)

function WorldsNO1GetNewItemView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "worlds_no1_get_new_item_layout")
end

function WorldsNO1GetNewItemView:__delete()

end

function WorldsNO1GetNewItemView:ReleaseCallBack()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function WorldsNO1GetNewItemView:LoadCallBack()
	self.item_list = AsyncListView.New(WorldsNO1GetNewItemViewItem, self.node_list.list_reward)
end

function WorldsNO1GetNewItemView:CloseCallBack()
end


-- item_list：获得的物品， show_bag_btn：是否显示“我的背包”按钮，is_caiji:是否由于采集打开的面板
function WorldsNO1GetNewItemView:SetData(item_list)
	table.sort(item_list, SortTools.KeyUpperSorter("color"))
	self.item_list_data = item_list

	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end


function WorldsNO1GetNewItemView:ShowIndexCallBack()
end

function WorldsNO1GetNewItemView:OnFlush(param_t, index)
	if self.item_list ~= nil then
		self.item_list:SetDataList(self.item_list_data)
	end
end


---------------------------------------------------------------------------
WorldsNO1GetNewItemViewItem = WorldsNO1GetNewItemViewItem or BaseClass(BaseRender)
function WorldsNO1GetNewItemViewItem:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end
function WorldsNO1GetNewItemViewItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end
function WorldsNO1GetNewItemViewItem:OnFlush()
	if self.data then
		self.node_list["item_cell"]:SetActive(false)
		self.item_cell:SetData(self.data)
		self.node_list["item_name"].text.text = ""
		
		GlobalTimerQuest:AddDelayTimer(function() 
			if self.node_list and self.node_list["item_cell"] then
				self.node_list["item_cell"]:SetActive(true)
			end

			if self.node_list and self.node_list["item_name"] then
				local str = ItemWGData.Instance:GetItemNameDarkColor(self.data.item_id)
				self.node_list["item_name"].text.text = str
			end

		end, 0.1 * self.index)
	end
end
