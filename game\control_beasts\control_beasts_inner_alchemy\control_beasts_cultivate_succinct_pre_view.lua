ControlBeastsSuccinctPreView = ControlBeastsSuccinctPreView or BaseClass(SafeBaseView)

function ControlBeastsSuccinctPreView:__init()
	self:SetMaskBg(true, true)
	local bundle_name = "uis/view/control_beasts_alchemy_ui_prefab"
    self:AddViewResource(0, bundle_name, "layout_beasts_alchemy_succinct_pre")
end

function ControlBeastsSuccinctPreView:ReleaseCallBack()
	if self.succinct_word_list then
        for i, v in ipairs(self.succinct_word_list) do
            v:DeleteMe()
        end

        self.succinct_word_list = nil
    end
	
	self.pre_item_id = nil
	self.pre_slot_index = nil
end

function ControlBeastsSuccinctPreView:SetNowPreItemId(pre_item_id, slot_index)
	self.pre_item_id = pre_item_id
	self.pre_slot_index = slot_index
end

function ControlBeastsSuccinctPreView:LoadCallBack()
    if not self.succinct_word_list then
        self.succinct_word_list = {}

        for i = 1, 20 do
            local cell_obj = self.node_list.scroll_root:FindObj(string.format("word_render_%d", i))
            if cell_obj then
                local cell = BeastsSuccinctPreWordRender.New(cell_obj)
                cell:SetIndex(i)
                self.succinct_word_list[i] = cell
            end
        end
    end
end

function ControlBeastsSuccinctPreView:OnFlush(param_t)
	if not self.pre_item_id then
		return
	end

	local list, color = ControlBeastsCultivateWGData.Instance:GetOneEquipWordPreviewList(self.pre_item_id, self.pre_slot_index)
	local title_str = string.format(Language.ContralBeastsAlchemy.SuccinctPreviewTitle, ToColorStr(Language.Common.ItemShowColor[color], ITEM_COLOR[color]))
	self.node_list.attr_title_name.text.text = title_str

	for i, succinct_word_cell in ipairs(self.succinct_word_list) do
		local data = list[i]
		succinct_word_cell:SetVisible(data ~= nil)

		if data ~= nil then
			succinct_word_cell:SetNowColorRange(color)
			succinct_word_cell:SetData(data)
		end
	end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.scroll_root.rect)
end

----------------------------------- 洗练词条预览 ----------------------------------------
BeastsSuccinctPreWordRender = BeastsSuccinctPreWordRender or BaseClass(BaseRender)
function BeastsSuccinctPreWordRender:ReleaseCallBack()
	self.pre_show_color = nil
end

function BeastsSuccinctPreWordRender:SetNowColorRange(show_color)
	self.pre_show_color = show_color
end

function BeastsSuccinctPreWordRender:OnFlush()
	if not self.data then
		return
	end

	-- self.node_list.operate_btn:CustomSetActive(self.data.words_type ~= 1)
	if self.data.words_type == 1 then
		local range_min_value,range_max_value = self:GetSuccinctPreWordVlaueInterval()
		local str = self:SetNowShowAttrStr(range_min_value,range_max_value)
		self.node_list.word_text.text.text = str
	else
		local str = self:SetNowShowSkillStr()
		self.node_list.word_text.text.text = str
	end
end

function BeastsSuccinctPreWordRender:GetSuccinctPreWordVlaueInterval()
	if not self.data then
		return
	end

	local range_index = self.pre_show_color or COMMON_CONSTS.NUMBER_ONE
	local anim_range = self.data["color_range" .. range_index]
	local range_min_value = 0
	local range_max_value = 0

	if anim_range then
		local range_str_list = Split(anim_range, ",")
		range_min_value = tonumber(range_str_list[1]) or 0
		range_max_value = tonumber(range_str_list[2]) or 0
	end

	return range_min_value / 100, range_max_value / 100
end

function BeastsSuccinctPreWordRender:SetNowShowAttrStr(min, max)
	if not self.data then
		return
	end

	local prefix = '   '
	local attr_name = EquipmentWGData.Instance:GetAttrName(self.data.param1, true, false)
	local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.param1)
	local per_desc = is_per and "%" or ""
	local min_value = (is_per and math.floor(self.data.param2 * min / 100) or math.floor(self.data.param2 * min)) or 0
	local max_value = (is_per and math.floor(self.data.param2 * max / 100) or math.floor(self.data.param2 * max)) or 0
	local str = string.format("%s：%s%s-%s", attr_name, prefix, min_value, max_value)
	return ToColorStr(str, ITEM_COLOR[self.data.entry_quality])
end

function BeastsSuccinctPreWordRender:SetNowShowSkillStr()
	if not self.data then
		return
	end

	local skill_name_str = ""
	local skill_desc_str = ""
	local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(self.data.param1)

	if skill_cfg then
		skill_name_str = ToColorStr(string.format("%s：", skill_cfg.skill_name), ITEM_COLOR[self.data.entry_quality])
		skill_desc_str = skill_cfg.skill_des
	end

	return string.format("%s%s", skill_name_str, skill_desc_str)
end
