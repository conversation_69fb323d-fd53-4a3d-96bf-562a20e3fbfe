require("game/artifact/artifact_view")
require("game/artifact/artifact_wg_data")
--require("game/artifact/artifact_awake_view")
require("game/artifact/artifact_battle_view")
require("game/artifact/artifact_battle_select_view")
require("game/artifact/artifact_uplevel_view")
require("game/artifact/artifact_upstar_view")
--require("game/artifact/artifact_fetter_view")
require("game/artifact/artifact_attr_view")
require("game/artifact/artifact_affection_view")
require("game/artifact/artifact_affection_level_view")
require("game/artifact/artifact_travel_view")
require("game/artifact/artifact_affection_change_view")

ArtifactWGCtrl = ArtifactWGCtrl or BaseClass(BaseWGCtrl)
function ArtifactWGCtrl:__init()
	if ArtifactWGCtrl.Instance then
		ErrorLog("[ArtifactWGCtrl]:Attempt to create singleton twice!")
	end
	ArtifactWGCtrl.Instance = self

	self.data = ArtifactWGData.New()
	self.view = ArtifactView.New(GuideModuleName.ArtifactView)
	--self.fetter_view = ArtifactFetterView.New()
	--self.awake_view = ArtifactAwakeView.New()
	--self.battle_view = ArtifactBattleView.New(GuideModuleName.ArtifactBattleView)
	self.battle_select_view = ArtifactBattleSelectView.New(GuideModuleName.ArtifactSelectView)
	self.attr_show_view = ArtifactAttrView.New()
	--self.artifact_affection_view = ArtifactAffectionView.New(GuideModuleName.ArtifactAffectionView)
	self.affection_level_view = AffectionLevelView.New()
	self.artifact_travel_view = ArtifactTravelView.New(GuideModuleName.ArtifactTravelView)
	self.affection_change_tip_view = ArtifactAffectionChangeView.New()

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
    self:RegisterAllProtocols()
end

function ArtifactWGCtrl:__delete()
    ArtifactWGCtrl.Instance = nil

  	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	-- if self.fetter_view then
	-- 	self.fetter_view:DeleteMe()
	-- 	self.fetter_view = nil
	-- end

	-- if self.awake_view then
	-- 	self.awake_view:DeleteMe()
	-- 	self.awake_view = nil
	-- end

	-- if self.battle_view then
	-- 	self.battle_view:DeleteMe()
	-- 	self.battle_view = nil
	-- end

	if self.battle_select_view then
		self.battle_select_view:DeleteMe()
		self.battle_select_view = nil
	end

	if self.artifact_attr_view then
		self.artifact_attr_view:DeleteMe()
		self.artifact_attr_view = nil
	end

	-- if self.artifact_affection_view then
	-- 	self.artifact_affection_view:DeleteMe()
	-- 	self.artifact_affection_view = nil
	-- end

	if self.affection_level_view then
		self.affection_level_view:DeleteMe()
		self.affection_level_view = nil
	end

	if self.artifact_travel_view then
		self.artifact_travel_view:DeleteMe()
		self.artifact_travel_view = nil
	end

	if self.affection_change_tip_view then
		self.affection_change_tip_view:DeleteMe()
		self.affection_change_tip_view = nil
	end

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
	end
end

function ArtifactWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSArtifactOperate)

	self:RegisterProtocol(SCArtifactItemInfo, "OnSCArtifactItemInfo")
	self:RegisterProtocol(SCArtifactItemUpdate, "OnSCArtifactItemUpdate")
	self:RegisterProtocol(SCArtifactApplyInfo, "OnSCArtifactApplyInfo")
	self:RegisterProtocol(SCArtifactSkillInfo, "OnSCArtifactSkillInfo")
	self:RegisterProtocol(SCArtifactFavorInfo, "OnSCArtifactFavorInfo")
	self:RegisterProtocol(SCArtifactTravelInfo, "OnSCArtifactTravelInfo")
end

-- 请求操作
function ArtifactWGCtrl:SendCSArtifactOperateRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSArtifactOperate)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function ArtifactWGCtrl:OnSCArtifactItemInfo(protocol)
	--print_error("=======全部信息======", protocol)
	self.data:SetArtifactItemAllInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Artifact)
	RemindManager.Instance:Fire(RemindName.ArtifactUpLevel)
	RemindManager.Instance:Fire(RemindName.ArtifactUpStar)
	RemindManager.Instance:Fire(RemindName.ArtifactBattle)
	RemindManager.Instance:Fire(RemindName.ArtifactAffection)
end

function ArtifactWGCtrl:OnSCArtifactItemUpdate(protocol)
	--print_error("=======单个======", protocol)
	self.data:ArtifactItemUpdateInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "protocol_flush")
	--ViewManager.Instance:FlushView(GuideModuleName.ArtifactAffectionView, nil, "protocol_flush")
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactTravelView, nil, "data_flush")
	if self.affection_level_view:IsOpen() then
		self.affection_level_view:Flush()
	end
	-- if self.awake_view:IsOpen() then
	-- 	if protocol.change_type == ARTIFACT_UPDATE_TYPE.UP_AWAKE_LEVEL then
	-- 		self.awake_view:Flush(nil, "up_awake_level")
	-- 	else
	-- 		self.awake_view:Flush()
	-- 	end
	-- end
	RemindManager.Instance:Fire(RemindName.Artifact)
	RemindManager.Instance:Fire(RemindName.ArtifactUpLevel)
	RemindManager.Instance:Fire(RemindName.ArtifactUpStar)
	RemindManager.Instance:Fire(RemindName.ArtifactBattle)
	RemindManager.Instance:Fire(RemindName.ArtifactAffection)
end

function ArtifactWGCtrl:OnSCArtifactApplyInfo(protocol)
	--print_error("=======出战信息======", protocol)
	self.data:SetArtifactApplyInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "battle_info")
	RemindManager.Instance:Fire(RemindName.Artifact)
	RemindManager.Instance:Fire(RemindName.ArtifactBattle)
end

function ArtifactWGCtrl:OnSCArtifactSkillInfo(protocol)
	--print_error("=======技能触发信息======", protocol)
	PlaySkillFloatWordWGCtrl.Instance:SetSkillShowWord(protocol.skill_id)
	local main_role = Scene.Instance:GetMainRole()
	local skill_cfg = self.data:GetArtifactSkillCfgById(protocol.skill_id)
	if main_role and skill_cfg then
		main_role:SetAtrifactTriggerEffectLoader(skill_cfg.skill_effect)
	end
end

function ArtifactWGCtrl:OnSCArtifactFavorInfo(protocol)
	--print_error("=======双修好感信息======", protocol)
	self.data:SetArtifactFavorInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "protocol_flush")
	RemindManager.Instance:Fire(RemindName.ArtifactAffection)
end

function ArtifactWGCtrl:OnSCArtifactTravelInfo(protocol)
	--print_error("=======双修同游信息======", protocol)
	self.data:SetArtifactTravelInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactTravelView, nil, "data_flush")
	ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "flush_remind")
	RemindManager.Instance:Fire(RemindName.ArtifactTravel)
	MapWGCtrl.Instance:FlushMapGlobalView("artifact_travel")
end

function ArtifactWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:GetLevelCostCfg(change_item_id) or 
			self.data:GetStarCostCfg(change_item_id) or 
			self.data:GetStarReplaceCostCfg(change_item_id) or
			self.data:GetIsSendGiftItem(change_item_id) or
			self.data:GetIsTravelCostItem(change_item_id) then
			ViewManager.Instance:FlushView(GuideModuleName.ArtifactView, nil, "protocol_flush")
			--ViewManager.Instance:FlushView(GuideModuleName.ArtifactAffectionView, nil, "protocol_flush")
			ViewManager.Instance:FlushView(GuideModuleName.ArtifactTravelView, nil, "data_flush")
			RemindManager.Instance:Fire(RemindName.Artifact)
			RemindManager.Instance:Fire(RemindName.ArtifactUpLevel)
			RemindManager.Instance:Fire(RemindName.ArtifactUpStar)
			RemindManager.Instance:Fire(RemindName.ArtifactTravel)
			RemindManager.Instance:Fire(RemindName.ArtifactAffection) -- 要在最后
		end
	end
end

local Artifact_Guide_id = 46
function ArtifactWGCtrl:OnUpLevelResult(result, seq, level)
	if 0 == result then
		self.view:StopLevelOperator()
	elseif 1 == result  then
		if level > 1 then
			if self.view:IsAutoUpLevel() then
				self.view:AutoUpLevelUpOnce()
			end
			self.view:PlayUseEffect(UIEffectName.s_shengji)
		else
			FunctionGuide.Instance:TriggerGuideById(Artifact_Guide_id, nil, seq)
			self.view:PlayUseEffect(UIEffectName.s_jihuo)
		end
	end
end

function ArtifactWGCtrl:OnUpStarResult(result, seq, star)
	if 1 == result then
		self.view:PlayUseEffect(UIEffectName.s_shengxing)
	end
end

function ArtifactWGCtrl:OnAwakeResult(result, seq)
	if 1 == result then
		self.view:PlayUseEffect(UIEffectName.s_juexing)
	end
end

function ArtifactWGCtrl:OnAffectionChangeResult(result, param1, param2)
	if 1 == result then
		self:ShowAffectionValueChange({seq = param1, value = param2})
	end
end

-- function ArtifactWGCtrl:OpenAwakeView(artifact_seq)
-- 	self.awake_view:SetDataAndOpen(artifact_seq)
-- end

function ArtifactWGCtrl:OpenAwakeView(show_data)
	self.attr_show_view:SetDataAndOpen(show_data)
end

function ArtifactWGCtrl:OpenBattleSelectView()
	if self.battle_select_view:IsOpen() then
		self.battle_select_view:Flush()
	else
		self.battle_select_view:Open()
	end
end

function ArtifactWGCtrl:CloseBattleSelectView(artifact_seq)
	if self.battle_select_view:IsOpen() then
		self.battle_select_view:Close()
	end
end

function ArtifactWGCtrl:OpenAffectionLevelView(show_data)
	self.affection_level_view:SetDataAndOpen(show_data)
end

--{ seq = 0, value = 0 }
function ArtifactWGCtrl:ShowAffectionValueChange(show_data)
	self.affection_change_tip_view:SetDataAndOpen(show_data)
end

-- function ArtifactWGCtrl:OnFetterResult(result, seq)
-- 	if 1 == result then
-- 		if self.fetter_view:IsOpen() then
-- 			self.fetter_view:PlayUseEffect(UIEffectName.s_jihuo)
-- 		end
-- 	end
-- end

-- function ArtifactWGCtrl:OpenFetterView(fetter_seq)
-- 	self.fetter_view:SetDataAndOpen(fetter_seq)
-- end