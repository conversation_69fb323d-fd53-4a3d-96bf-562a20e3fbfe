---------------
--全民寻宝
---------------
ActGobalXunbaoView = ActGobalXunbaoView or BaseClass(SafeBaseView)

local pos1 = 33.1
local pos2 = -10
local NATIONAL_CELEBRATION_MAX_COUNT=8

function ActGobalXunbaoView:__init(act_id)
	self:AddViewResource(0, "uis/view/act_subview_ui_prefab", "layout_act_gobal_xunbao")
	self.giftlist = {}
	self.res_num = 0
	self.is_nolonger_tips = nil
	self.open_tween = nil
	self.close_tween = nil
end

function ActGobalXunbaoView:__delete()
	-- body
end

function ActGobalXunbaoView:ReleaseCallBack()
	if self.item_list then
		for k,v in pairs(self.item_list) do
			if v then
				v:DeleteMe()
			end
		end
		self.item_list = {}
	end

	if self.talent_skill_layout_list ~= nil then
		self.talent_skill_layout_list:DeleteMe()
		self.talent_skill_layout_list = nil
	end

	if self.add_val_alert then
		self.add_val_alert:DeleteMe()
		self.add_val_alert = nil
	end

	if CountDownManager.Instance:HasCountDown("act_gobal_xunbao_view") then
		CountDownManager.Instance:RemoveCountDown("act_gobal_xunbao_view")
	end

	self.has_refresh = nil
	self.has_load_callback = nil
	-- if self.delay_frequency then
	-- 	GlobalTimerQuest:CancelQuest(self.delay_frequency)
	-- 	self.delay_frequency = nil
	-- end
end

function ActGobalXunbaoView:LoadCallBack()
	self.item_list = {}
	for i=1,6 do
		self.item_list[i] = ItemCell.New(self.node_list["ph_cell_" .. i])
		-- self.item_list[i].root_node.rect.localScale = Vector3(0.88, 0.88, 0.88)
	end

	self.talent_skill_layout_list = ActGobalXunbaoRender.New(self.node_list.reward_list)
	self.node_list.reward_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnScrollValueChanged, self))
	self.slider_pos_y = self.node_list.Slider.rect.anchoredPosition.y

	XUI.AddClickEventListener(self.node_list.btn_open_tip, BindTool.Bind1(self.OnOpenTip, self))
	XUI.AddClickEventListener(self.node_list.btn_recharge, BindTool.Bind1(self.OnClickRecharge, self))
	-- XUI.AddClickEventListener(self.node_list.btn_recharge_ten, BindTool.Bind1(self.OnClickRechargeTen, self))
	XUI.AddClickEventListener(self.node_list.btn_turnin, BindTool.Bind1(self.OnClickRechargeTen, self))

	-- XUI.AddClickEventListener(self.node_list.btn_reward, BindTool.Bind1(self.OpenRewradTip, self))

	self.add_val_alert = Alert.New(nil,nil,nil,nil,true)
	self.add_val_alert:SetOkFunc(function(is_nolonger_tips)
			self:OnClickGobalXunbaoAlertOk()
			self.add_val_alert:Close()
			ServerBanBenActivityView.ActGobalXunbaoView_Flag = is_nolonger_tips 	--特殊处理
		end)
	self.has_load_callback = true
end

function ActGobalXunbaoView:OnClickGobalXunbaoAlertOk()
	if self.res_num == 1 then
		self:OnClickRechargeData()
		self:OnClickTurnIn()
	elseif self.res_num == 2 then
		self:OnClickRechargeTenData()
		self:OnClickTurnInTen()
	end
end

function ActGobalXunbaoView:OnOpenTip()
	local str = Language.OpenServer.GobalXunbaoTips
	local gobal_xunbao_cfg = ServerActivityWGData.Instance:GetGobalXunbao()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local times_cfg = rand_config.nationa_celebration_times
	local nationa_celebration_times = ServerActivityWGData.Instance:GetRandActivityConfig(times_cfg, ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
	for i,v in ipairs(nationa_celebration_times) do
		local cfg = ItemWGData.Instance:GetItemConfig(v.reward_item.item_id)
		if cfg then
			str = str .. string.format(Language.OpenServer.RewardFormat, v.commit_times, cfg.name)
		end
	end
	RuleTip.Instance:SetContent(str, Language.OpenServer.GobalXunbaoTitleTips)
end

function ActGobalXunbaoView:OpenRewradTip()
	ServerActivityWGCtrl.Instance:OpenGobalXunbaoTip()
end


function ActGobalXunbaoView:OnClickRechargeData()
	-- local res_mun = ServerActivityWGData.Instance:OnGobalXubBaoZhongZiData()
	-- local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	-- local rand_three = rand_config.other
	-- if res_mun <= 0 then
	-- 	ShopWGCtrl.Instance:SendShopBuy(rand_three[1].national_celebration_gift, 1, 1, 0)
	-- end
end

function ActGobalXunbaoView:OnClickRecharge()
	if GlobalTimerQuest:GetRunQuest(self.bottom_on_off_state) then
		return
	end
	self.res_num = 1
	ActTreasureWGData.Instance:SetAutoUseGiftFlag(true)
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local other_cfg = cfg.other
	local price_info = ShopWGData.GetItemPrice(other_cfg[1].national_celebration_gift)
	--self.add_val_alert:SetLableString(string.format(Language.OpenServer.Qingdian, price_info.gold , 1))
	self.add_val_alert:SetLableString(string.format(Language.OpenServer.Qingdian, price_info.gold))
	local res_mun = ServerActivityWGData.Instance:OnGobalXubBaoZhongZiData()
	if res_mun <= 0 then
		if ServerBanBenActivityView.ActGobalXunbaoView_Flag then
			self:OnClickGobalXunbaoAlertOk()
		else
			self.add_val_alert:Open()
		end
	else
		self:OnClickRechargeData()
		self:OnClickTurnIn()
	end
	-- ActFestivalWGCtrl.Instance:UpdataFestival()

	self.bottom_on_off_state = GlobalTimerQuest:AddDelayTimer(function()
		if nil ~= self.bottom_on_off_state then
			GlobalTimerQuest:CancelQuest(self.bottom_on_off_state)
			self.bottom_on_off_state = nil
		end
	end, 0.5)

	-- local param_t ={}
	-- param_t = {
	-- 	rand_activity_type = ACTIVITY_TYPE.ACT_GOBAL_XUNBAO,
	-- 	opera_type = RA_NATIONAL_CELEBRATION_REQ_TYPE.RA_NATIONAL_CELEBRATION_REQ_TYPE_COMMIT,
	-- 	param_1 = NATIONAL_CELEBRATION_COMMIT_TYPE.NATIONAL_CELEBRATION_COMMIT_TYPE_GOLD
	-- }
	-- ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ActGobalXunbaoView:OnClickRechargeTen()
	if GlobalTimerQuest:GetRunQuest(self.bottom_on_off_state) then
		return
	end
	self.res_num = 2
	ActTreasureWGData.Instance:SetAutoUseGiftFlag(true)
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local other_cfg = cfg.other
	local price_info = ShopWGData.GetItemPrice(other_cfg[1].national_celebration_gift)
	local res_mun, need_count = ServerActivityWGData.Instance:OnGobalXubBaoZhongZiData()
	if res_mun < need_count then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OpenServer.Qingdian1)
		return
	end
	self.add_val_alert:SetLableString(string.format(Language.OpenServer.Qingdian,(need_count - res_mun) * price_info.gold, (need_count - res_mun)))
	if res_mun < need_count then
		if ServerBanBenActivityView.ActGobalXunbaoView_Flag then
			self:OnClickGobalXunbaoAlertOk()
		else
			self.add_val_alert:Open()
		end
	else
		self:OnClickRechargeTenData()
		self:OnClickTurnInTen()
	end

	self.bottom_on_off_state = GlobalTimerQuest:AddDelayTimer(function()
		if nil ~= self.bottom_on_off_state then
			GlobalTimerQuest:CancelQuest(self.bottom_on_off_state)
			self.bottom_on_off_state = nil
		end
	end, 0.5)
end

function ActGobalXunbaoView:OnClickRechargeTenData()
	-- local res_mun, need_count = ServerActivityWGData.Instance:OnGobalXubBaoZhongZiData()
	-- local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	-- local rand_three = rand_config.other
	-- if res_mun < need_count then
	-- 	ShopWGCtrl.Instance:SendShopBuy(rand_three[1].national_celebration_gift, need_count - res_mun, 1, 0)
	-- end
end

function ActGobalXunbaoView:OnClickTurnInTen()
	local param_t ={}
	param_t = {
		rand_activity_type = ACTIVITY_TYPE.ACT_GOBAL_XUNBAO,
		opera_type = RA_NATIONAL_CELEBRATION_REQ_TYPE.RA_NATIONAL_CELEBRATION_REQ_TYPE_COMMIT,
		param_1 = NATIONAL_CELEBRATION_COMMIT_TYPE.NATIONAL_CELEBRATION_COMMIT_TYPE_ITEM
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ActGobalXunbaoView:OnClickTurnIn()
	local param_t ={}
	param_t = {
		rand_activity_type = ACTIVITY_TYPE.ACT_GOBAL_XUNBAO,
		opera_type = RA_NATIONAL_CELEBRATION_REQ_TYPE.RA_NATIONAL_CELEBRATION_REQ_TYPE_COMMIT,
		param_1 = NATIONAL_CELEBRATION_COMMIT_TYPE.NATIONAL_CELEBRATION_COMMIT_TYPE_GOLD
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ActGobalXunbaoView:ShowIndexCallBack()
	-- if self.has_refresh then
	self:RefreshView(ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
	-- end
	local CDM = CountDownManager.Instance
	if CDM:HasCountDown("act_gobal_xunbao_view") then
		CDM:RemoveCountDown("act_gobal_xunbao_view")
	end
	CDM:AddCountDown("act_gobal_xunbao_view", BindTool.Bind(self.UpdateCountDown, self),
		BindTool.Bind(self.CompleteCountDown, self), act_info.next_time,nil,0.3)
end

function ActGobalXunbaoView:UpdateCountDown( elapse_time, total_time )
	local time = math.floor(total_time - elapse_time)
	self.node_list["time_txt"].text.text = TimeUtil.FormatSecond2DHMS(time)
end

function ActGobalXunbaoView:CompleteCountDown()

end

-- 刷新当前视图
function ActGobalXunbaoView:RefreshView(param_t)
	-- print_error("RefreshView",not self.has_load_callback)
	if param_t ~= ACTIVITY_TYPE.ACT_GOBAL_XUNBAO or not self.has_load_callback then
		self.has_refresh = true
		return
	end
	local gobal_xunbao_cfg = ServerActivityWGData.Instance:GetGobalXunbao()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	-- local rand_t = rand_config.nationa_celebration_times
	-- local nationa_celebration_times_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)

	local rand_t = rand_config.nationa_celebration_commit
	-- local times_cfg = rand_config.nationa_celebration_times
	--用等级分段
	-- local nationa_celebration_times = ServerActivityWGData.Instance:GetRandActivityConfig(times_cfg, ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
	-- print_error("nationa_celebration_commit=",#nationa_celebration_commit)

	local rand_three = rand_config.other
	local other_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_three, ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
	-- local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].national_celebration_commit_item_id)
	local price_info = ShopWGData.GetItemPrice(other_cfg[1].national_celebration_gift)

	local res_num, need_count = ServerActivityWGData.Instance:OnGobalXubBaoZhongZiData()
	self.node_list.lbl_cost.text.text = (price_info.gold)--30元宝提交一次
	self.node_list.lbl_item_count.text.text = (res_num .. "/" .. need_count)

	if res_num >= need_count then
		self.node_list.remind:SetActive(true)
	else
		self.node_list.remind:SetActive(false)
	end

	self.node_list.img_item_bg.image:LoadSprite(ResPath.GetItem(other_cfg[1].national_celebration_commit_item_id))

	local nationa_celebration_progress_cfg = rand_config.nationa_celebration_progress
	if not gobal_xunbao_cfg.max_progress_var then
		return
	end
	local max_value = gobal_xunbao_cfg.max_progress_var[NATIONAL_CELEBRATION_MAX_COUNT] or nationa_celebration_progress_cfg[#nationa_celebration_progress_cfg].need_progress_var
	-- local people_percent
	-- if gobal_xunbao_cfg.max_progress_var then
	-- 	people_percent = gobal_xunbao_cfg.max_progress_var/nationa_celebration_progress_cfg[#nationa_celebration_progress_cfg].need_progress_var
	-- end
	-- self.node_list.lbl_turnin_count.text.text = (string.format(Language.OpenServer.GobalXunbaoTurninCount, gobal_xunbao_cfg.commit_times))
	self.node_list.lbl_schedule.text.text = (gobal_xunbao_cfg.server_commit_times .. "/" .. max_value)

	local nationa_celebration_commit = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.ACT_GOBAL_XUNBAO)
	local cell_index = 1
	for i,v in ipairs(nationa_celebration_commit) do
		if v.show == 1 then
			self.item_list[cell_index]:SetData(v.reward_item)
			cell_index = cell_index + 1
		end
	end

	self:SetProgrssPercent()

	-- self.talent_skill_layout_list:SetPeoplePercent(people_percent)
	self.talent_skill_layout_list:SetData()
	-- self.delay_frequency = GlobalTimerQuest:AddDelayTimer(function()
	-- 	if nil ~= self.delay_frequency then
	-- 		GlobalTimerQuest:CancelQuest(self.delay_frequency)
	-- 		self.delay_frequency = nil
	-- 	end
	-- end,0.1)
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(other_cfg[1].national_celebration_commit_item_id)
	-- self.node_list.lbl_task_desc_1.text.text = (string.format(Language.PigTreasure.Describe1, item_cfg.name))
	-- self.node_list.lbl_task_desc_2.text.text = (string.format(Language.PigTreasure.Describe2, item_cfg.name))
	local gobal_xunbao_info = ServerActivityWGData.Instance:GetGobalXunbao()
	self.node_list.lbl_task_desc_3.text.text = (string.format(Language.OpenServer.GobalXunbaoTurninCount, gobal_xunbao_info.commit_times))
end

function ActGobalXunbaoView:SetProgrssPercent()
	local value = ServerActivityWGData.Instance:GetGobalProgrss()

	self.node_list.Slider.slider.value = value
end

function ActGobalXunbaoView:OnScrollValueChanged()
	local dif_value = pos1 - pos2
	local percent = self.node_list.reward_list.scroll_rect.normalizedPosition.x
	self.node_list.Slider.rect.anchoredPosition = Vector2(pos1 - dif_value * percent, self.slider_pos_y)
end

function ActGobalXunbaoView:PlayTween()
	self.open_tween = UITween.ShowFadeUp
end

function ActGobalXunbaoView:CloseCallBack()
	self.open_tween = nil
end

--------------------------------ActGobalXunbaoRender
ActGobalXunbaoRender = ActGobalXunbaoRender or BaseClass(BaseRender)
function ActGobalXunbaoRender:__init()
end

function ActGobalXunbaoRender:__delete()
	if self.giftlist then
		self.giftlist:DeleteMe()
		self.giftlist = nil
	end
	self.people_percent = nil
end

function ActGobalXunbaoRender:LoadCallBack()
	self.giftlist = AsyncListView.New(ActGobalXunbaoCell,self.view)
end

function ActGobalXunbaoRender:OnFlush()
	local rand_config = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local nationa_celebration_progress_cfg = rand_config.nationa_celebration_progress
	local gobal_xunbao_cfg = ServerActivityWGData.Instance:GetGobalXunbao()

	local cfg = {}
	for k,v in pairs(nationa_celebration_progress_cfg) do
		local list = __TableCopy(v)
		list.people_percent = gobal_xunbao_cfg.max_progress_var[k] or 1
		table.insert(cfg,list)
	end
	self.giftlist:SetDataList(cfg,3)
end

-- function ActGobalXunbaoRender:SetPeoplePercent(var)
-- 	self.people_percent = var
-- end

--------------------------------ActGobalXunbaoCell
ActGobalXunbaoCell = ActGobalXunbaoCell or BaseClass(BaseRender)
function ActGobalXunbaoCell:__init()

end

function ActGobalXunbaoCell:__delete()
	self.img_task_bar = nil
	self.img_task_bar_name = nil
	self.need_var = nil
end

function ActGobalXunbaoCell:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.ph_cell_1, BindTool.Bind1(self.ClickHandler, self))
end

function ActGobalXunbaoCell:OnFlush()
	if not self.data then return end
	self.need_var = self.data.people_percent
	self.node_list.icon.image:LoadSprite(ResPath.GetF2MainUIImage(self.data.pic_name))
	self.node_list.img_name.text.text = Language.XunbaoIconName[self.data.pic_name]
	self.node_list.lbl_cishu.text.text = self.need_var

	local cfg = ServerActivityWGData.Instance:GetGobalXunbao()
	local fetch_flag = bit:d2b(cfg.fetch_flag)
	local server_commit_times = ServerActivityWGData.Instance:GetGobalXunbao().server_commit_times
	XUI.SetGraphicGrey(self.node_list.ph_cell_1,server_commit_times < self.need_var)
	XUI.SetButtonEnabled(self.node_list.icon,server_commit_times >= self.need_var)
	if self.data.reward_type == 2 and server_commit_times >= self.need_var and fetch_flag[32 - self.data.seq] == 0 then
		self.node_list.remind:SetActive(true)
	else
		self.node_list.remind:SetActive(false)
	end
end

function ActGobalXunbaoCell:CreateSelectEffect()
end

function ActGobalXunbaoCell:ClickHandler()
	local cfg = ServerActivityWGData.Instance:GetGobalXunbao()
	local fetch_flag = bit:d2b(cfg.fetch_flag)
	local server_commit_times = ServerActivityWGData.Instance:GetGobalXunbao().server_commit_times

	--2是全服奖励，需要手动领取
	if self.data.reward_type == 2 and server_commit_times >= self.need_var and fetch_flag[32 - self.data.seq] == 0 then
		local param_t ={}
		param_t = {
			rand_activity_type = ACTIVITY_TYPE.ACT_GOBAL_XUNBAO,
			opera_type = RA_NATIONAL_CELEBRATION_REQ_TYPE.RA_NATIONAL_CELEBRATION_REQ_TYPE_FETCH_REWARD,
			param_1 = self.data.seq
		}
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	else
		local data = {}
		data.pic_name = self.data.pic_name
		local color = server_commit_times >= self.need_var and COLOR3B.GREEN or COLOR3B.RED
		data.need_progress_var = ToColorStr(self.need_var,color)
		ServerActivityWGCtrl.Instance:OpenNationalCelebrationRewardTip(data)
	end
end

-----------------------------------------------------------------------
