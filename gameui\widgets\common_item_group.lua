CommonItemGroup = CommonItemGroup or BaseClass(BaseRender)
function CommonItemGroup:__init()
end

--------------------------------------------------------------------------
--类名, 查找前缀名, 每组数量
--------------------------------------------------------------------------
function CommonItemGroup:Init(Item, name, num)
	self.item_group = {}
	self.num = num
	for i = 1, self.num do
		self.item_group[i] = Item.New(self.node_list[name .. i])
		self.node_list[name .. i]:SetActive(true)
	end

	for i= self.num + 1, self.view.transform.childCount do
		self.node_list[name .. i]:SetActive(false)
	end
end

function CommonItemGroup:ReleaseCallBack()
    if self.item_group then
    	for k, v in pairs(self.item_group) do
    		v:DeleteMe()
    	end
    	self.item_group = {}
    end
end

function CommonItemGroup:SetIndex(index)
	for i = 1, self.num do
		self.item_group[i]:SetIndex((index - 1) * self.num + i)
	end
end

function CommonItemGroup:OnFlush()
	for i = 1, self.num do
		local item = self.item_group[i]
		item:SetData(self.data[item.index])
	end
end

function CommonItemGroup:SetClickCallBack(event)
    for i = 1, self.num do
		self.item_group[i]:SetClickCallBack(event)
	end
end

function CommonItemGroup:FlushHL(select_index)
	for i = 1, self.num do
		local item = self.item_group[i]
		if item.FlushHL then
			item:FlushHL(item.index == select_index)
		end
	end
end

function CommonItemGroup:SetFuncData(data, func_key)
	for i = 1, self.num do
		local item = self.item_group[i]
		if item[func_key] then
			item[func_key](item, data)
		end
	end
end