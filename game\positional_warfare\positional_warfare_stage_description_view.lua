-- 武斗阶段说明弹窗
PositionalWarfareStageDescriptionView = PositionalWarfareStageDescriptionView or BaseClass(SafeBaseView)

function PositionalWarfareStageDescriptionView:__init()
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_stage_description_view")
end

function PositionalWarfareStageDescriptionView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.Close, self))
    self.node_list.desc_stage_content.text.text = Language.PositionalWarfare.StageDescContent

    local stage_cfg = PositionalWarfareWGData.Instance:GetCurGroupCfg()
    if not IsEmptyTable(stage_cfg) then
        local next_index = stage_cfg.next_index

        if next_index < 0 then
            self.node_list.desc_stage_time.text.text = string.format(Language.PositionalWarfare.StageDsescriptionMax, stage_cfg.stage_name) 
        else
            local next_stage_cfg = PositionalWarfareWGData.Instance:GetGroupCfgByGroupIndex(next_index)

            if not IsEmptyTable(next_stage_cfg) then
                local open_day = PositionalWarfareWGData.Instance:GetMainServerOpenTime()
                local time = next_stage_cfg.min_open_day - open_day
                time = time > 1 and time or 1

                self.node_list.desc_stage_time.text.text = string.format(Language.PositionalWarfare.StageTimeDesc, time, next_stage_cfg.stage_name) 
            else
                self.node_list.desc_stage_time.text.text = string.format(Language.PositionalWarfare.StageDsescriptionMax, stage_cfg.stage_name) 
            end
        end
    else
        self.node_list.desc_stage_time.text.text = ""
    end
end