BiZuoActivityDescView = BiZuoActivityDescView or BaseClass(SafeBaseView)

local normal_length = 192  --正常的文字最大长度192
-- local normal_bg_length = 367  --正常的背景图最大长度
-- local normal_bg_high = 430  --正常的背景图高度
local normal_bg_length = 400  --正常的背景图最大长度
local normal_bg_high = 364  --正常的背景图高度
function BiZuoActivityDescView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:LoadConfig()

	self.data = {}
    self.tips = 0
    self.replace_name_list = {}
end

function BiZuoActivityDescView:LoadConfig()
    self.view_name = "BizuoUi"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(602, 426)})
    self:AddViewResource(0, "uis/view/bizuo_ui_prefab", "layout_activity_des")
end


function BiZuoActivityDescView:SetData(data, replace_name_list, tips)
    self.tips = tips
	self.data = data
    self.replace_name_list = replace_name_list
end

function BiZuoActivityDescView:ReleaseCallBack()
	self.data = nil
	if self.reward_list ~= nil then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
    self.replace_name_list = nil
end

function BiZuoActivityDescView:LoadCallBack(index, loaded_times)
	-- self:SetSecondView(Vector2(590,420))
	self.reward_list = {}
	for i = 0, 5 do
		local base_item = ItemCell.New(self.node_list.ph_award_list)
		self.reward_list[i] = base_item
	end
end

function BiZuoActivityDescView:ShowIndexCallBack(index)
	self:Flush(index)
end


function BiZuoActivityDescView:OpenCallBack()

end

function BiZuoActivityDescView:CloseCallBack(is_all)

end

function BiZuoActivityDescView:OnFlush(param_t, index)
	if self.reward_list == nil then return end

	if not self.data then return end

	for i = 0, 5 do
		self.reward_list[i]:SetActive(false)
	end
	local reward_list_data = SortDataByItemColor(self.data.reward_item)
	if reward_list_data then
		for i, v in pairs(reward_list_data) do
			if self.reward_list[i - 1] then
				self.reward_list[i - 1]:SetData(v)
				self.reward_list[i - 1]:SetActive(true)
			end
		end
	end

	local name = self.data.name
	if self.data.type == BiZuoType.Type_3 and WuJinJiTanWGData.Instance:IsLingHunGuangChang() then
		name = Language.FuBenPanel.LingHunGuangChangTitle
	end
	
	-- 是否参与过该活动
	-- local is_participation = BiZuoWGData.Instance:GetIsParticipationBySeq(self.data.act_seq)
	-- local complete_times = is_participation and 1 or 0
	-- self.node_list.lbl_count.text.text = string.format(Language.BiZuo.CompleteTime, complete_times, 1)
	-- local bundle_name, asset = ResPath.GetF2MainUIImage("act_" .. self.data.res_head)
	-- self.node_list.img_head.image:LoadSprite(bundle_name, asset)
	-- str = string.format(Language.BiZuo.ActivityDesc, self.data.open_time, self.data.level, self.data.des)
	-- self.node_list.ph_suit_attr_text.text.text = ToColorStr(str, COLOR3B.WHITE)
    local text_time = ""
    local text_time2 = ""
    local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local updata_time = math.max(server_time, 0) -- 0点刷新时间
    local w_day = tonumber(os.date("%w", updata_time))
    if 0 == w_day then w_day = 7 end
    if self.data.signup_begin_time and self.data.signup_end_time then
        text_time2 = self.data.open_tips .. "    " .. self.data.signup_begin_time .. "-" .. self.data.signup_end_time
    else
        text_time2 = Language.Achieve.Nothing
    end

    text_time = self.data.smallview_time

    if self.data.day and self.data.day > 0 then
        text_time = string.format(Language.BiZuo.TimeTips, self.data.day).. "    " .. self.data.special_show_time
        text_time2 = string.format(Language.BiZuo.TimeTips, self.data.day).. "    " .. self.data.signup_begin_time .. "-" .. self.data.signup_end_time
    end

    local replace_name_list = {}
    if self.replace_name_list ~= "" and self.tips then
        local i_open_server_day = cur_openserver_day - (w_day - self.tips)
        if i_open_server_day <= 0 then
            i_open_server_day = i_open_server_day + 7
        end
        replace_name_list = Split(self.replace_name_list, "|")

        if replace_name_list[i_open_server_day] then
            if self.data.signup_begin_time and self.data.signup_end_time then
                text_time2 = string.format(Language.BiZuo.TimeTips, i_open_server_day).. "    " .. self.data.signup_begin_time .. "-" .. self.data.signup_end_time
            else
                text_time2 = Language.Achieve.Nothing
            end

            if self.data.special_show_time and self.data.special_show_time ~= "" then
                text_time = string.format(Language.BiZuo.TimeTips, i_open_server_day).. "    " .. self.data.special_show_time
            end
        end
    end

    self.node_list.title_view_name.text.text = name
    self.node_list.text_time2.text.text = text_time2
    self.node_list.text_time.text.text = text_time

	self.node_list.text_level.text.text = RoleWGData.GetLevelString(self.data.level) .. Language.Tip.Ji
	self.node_list.text_desc.text.text = self.data.smallview_des
	self.node_list.text_type.text.text = self.data.smallview_typedes

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.text_time.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.text_level.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.text_desc.rect)
    --UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.text_type.rect)
    --UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.text_time2.rect)
    
    local wid_tb = {}
    wid_tb[1] = self.node_list.text_time.rect.sizeDelta.x
    wid_tb[2] = self.node_list.text_level.rect.sizeDelta.x
    wid_tb[3] = self.node_list.text_desc.rect.sizeDelta.x
    --wid_tb[4] = self.node_list.text_type.rect.sizeDelta.x
    --wid_tb[5] = self.node_list.text_time2.rect.sizeDelta.x

    local preferred_width = wid_tb[1] > wid_tb[3] and wid_tb[1] or wid_tb[3]
    local x = 1
    for k, v in ipairs(wid_tb) do
        if v > preferred_width then
            preferred_width = v
        end
    end

    preferred_width = preferred_width > normal_length and preferred_width or normal_length
    --self.node_list.Image.rect.sizeDelta = Vector2(preferred_width - normal_length + normal_bg_length, normal_bg_high)
end
