WujinjitanFbSceneLogic = WujinjitanFbSceneLogic or BaseClass(CommonFbLogic)

function WujinjitanFbSceneLogic:__init()
	--self.show_win_view_event = GlobalEventSystem.Bind("OnScene_WuJinJiTan_LoadComplete", BindTool.Bind1(self.ShowWinView, self))
	self.first_enter = PlayerPrefsUtil.GetInt("exp_fb_enter")
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
end

function WujinjitanFbSceneLogic:__delete()
	self.first_enter = nil
	if self.shield_role_rule then
		self.shield_role_rule:DeleteMe()
		self.shield_role_rule = nil
	end

	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
	end
end

function WujinjitanFbSceneLogic:Enter(old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		ViewManager.Instance:CloseAll()
		CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	end

	self.guaji_jianche_time = Status.NowTime
	GuajiWGCtrl.Instance:StopGuaji()
	FuBenWGCtrl.Instance:OpenTeamExpInfoView()
	FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.FBCT_WUJINJITAN)

	WuJinJiTanWGData.Instance:SetRecordIsLingHunGuangChang(WuJinJiTanWGData.Instance:IsLingHunGuangChang())

	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetTaskContents(false)

		local fb_info = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()
		local title_name = (fb_info.fuben_type == FUBEN_TYPE.LINGHUNGUANGCHANG) and Language.FuBenPanel.LingHunGuangChangTitle or Language.FuBenPanel.LianYuTitle
		MainuiWGCtrl.Instance:SetFBNameState(true, title_name)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		-- mainui_ctrl:SetTaskPanel(false)
		-- mainui_ctrl:ChangeTaskBtnName(Scene.Instance:GetSceneName())
		-- mainui_ctrl:SetAutoGuaJi(true)
		-- mainui_ctrl:SetTaskAndTeamCallBack(nil, nil)
	end)

	local fb_info = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()
	if fb_info and fb_info.curr_wave_index and 0 < fb_info.curr_wave_index then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end

	local time_record = false
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	if self.first_enter then
		time_record = now_time <= self.first_enter
	else
		time_record = nil
	end

	if new_scene_type == SceneType.LingHunGuangChang or new_scene_type == SceneType.Wujinjitan then
		local open_guide_data = FuBenPanelWGData.Instance:GetFuBenJiangHuGuideFlag()
	    if open_guide_data ~= nil then
	    	local guide_data = Split(open_guide_data, "|")
	        if guide_data[3] ~= nil and tonumber(guide_data[3]) == 33 and tonumber(guide_data[2] or 0) + 15 * 60 > TimeWGCtrl.Instance:GetServerTime() then
	            local guide_cfg = FunctionGuide.Instance:GetGuideCfgByTrigger(GuideTriggerType.FuBenPanelResult, tonumber(guide_data[1] or 0))
	            if guide_cfg ~= nil then
	                FunctionGuide.Instance:SetCurrentGuideCfg(guide_cfg)
	            end
	        end
	    end
	end

	--策划要求无论如何也要显示所有人
	self.shield_role_rule = SimpleRule.New(ShieldObjType.Role, ShieldRuleWeight.Max, function (obj)
		return false
    end)
    self.shield_role_rule:Register()
    if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end
end

function WujinjitanFbSceneLogic:CloseLoadingCallBack()
    Scene.Instance:SendFBLoadedScene()
end

function WujinjitanFbSceneLogic:InitCallBack()
	MainuiWGCtrl.Instance:SetFirstEnterFb(true)
end

function WujinjitanFbSceneLogic:OnSceneDetailLoadComplete()
	--GlobalEventSystem:Fire("OnScene_WuJinJiTan_LoadComplete", true)
end

function WujinjitanFbSceneLogic:Out(old_scene_type, new_scene_type)
	self.first_enter = nil
	UiInstanceMgr.Instance:ColseFBStartDown()
	PlayerPrefsUtil.DeleteKey("DeleteKey")

	-- print_error("关闭界面",old_scene_type, new_scene_type)
	if old_scene_type ~= new_scene_type then
		GuajiWGCtrl.Instance:StopGuaji(nil, true)
		CommonFbLogic.Out(self, old_scene_type, new_scene_type)
		FuBenWGCtrl.Instance:CloseTeamExpInfoView()
		FuBenWGCtrl.Instance:CloseTeamExpCheerView()
		FuBenWGCtrl.Instance:CloseTeamExpMdeicineView()

		-- MainuiWGCtrl.Instance:SetTaskAndTeamCallBack(nil, nil)
		-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text2)
        MainuiWGCtrl.Instance:SetOtherContents(false)
		MainuiWGCtrl.Instance:SetTaskContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(true)
		if FuBenPanelCountDown.Instance then
			FuBenPanelCountDown.Instance:CloseViewHandler()
		end
		TipsNumberShowManager.Instance:CancleTeamPos(GameEnum.RD_FLOAT_V)
	end
	if self.click_event then
		GlobalEventSystem:UnBind(self.click_event)
	end
	-- FuBenPanelWGData.Instance:SetEnterType(FUBEN_ENTER_TYPE.TEAM)
	FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_exp)
	Scene.Instance:ClearJumpFlag()
	if self.shield_role_rule then
		self.shield_role_rule:DeleteMe()
		self.shield_role_rule = nil
    end
    if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
end

function WujinjitanFbSceneLogic:ShowWinView()

end
function WujinjitanFbSceneLogic:SendEncourgeTimes()
	local bol = FuBenPanelWGData.Instance:GetetIsSelect()
	if bol == false then return end
	FuBenPanelWGData.Instance:SetIsSelect(false)

	local flag1,flag2 = FuBenWGData.Instance:GetGuWuData()
	if flag1 == 1 or flag2 == 1 then
		local other_cfg = WuJinJiTanWGData.Instance:GetWuJinJiCfg().other[1]
		local per_gold_consume = other_cfg.gold_guwu_cost
		local per_coin_consume = other_cfg.coin_guwu_cost
		local main_role_vo = RoleWGData.Instance:GetRoleInfo()
		local all_gold = main_role_vo.gold + main_role_vo.bind_gold
		local all_coin = main_role_vo.coin
		local can_buy_gold_count = math.floor(all_gold/per_gold_consume)
		local can_buy_coin_count = math.floor(all_coin / per_coin_consume)
		can_buy_gold_count = math.floor(all_gold/per_gold_consume) <= 5 and math.floor(all_gold/per_gold_consume) or 5
		can_buy_coin_count = math.floor(all_coin / per_coin_consume) <= 5 and math.floor(all_coin / per_coin_consume) or 5
		if (can_buy_gold_count + can_buy_coin_count) <= 0 then return end


		FuBenWGCtrl.Instance:SendFbGuwu(-1,
			flag1 == 1 and can_buy_gold_count or 0,
			flag2 == 1 and can_buy_coin_count or 0)
	end
end

function WujinjitanFbSceneLogic:GetGuajiCharacter()
	local target_obj = nil
	target_obj = self:GetBoss()
	if target_obj ~= nil then
		return target_obj
	else
		return self:GetMonster()
	end
end

function WujinjitanFbSceneLogic:GetBoss()
	local target_obj = nil
	local monster_list = Scene.Instance:GetMonsterList()
	for k,v in pairs(monster_list) do
		if v:IsDead() == false and v:IsBoss() then
			target_obj = v
			GuajiCache.target_obj = target_obj
			return target_obj
		end
	end
end
function WujinjitanFbSceneLogic:GetMonster()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = 10000--COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	return Scene.Instance:SelectObjHelper(Scene.Instance:GetMonsterList(), x, y, distance_limit, SelectType.Enemy)
end

function WujinjitanFbSceneLogic:ForceHideGuard()
	local is_zhuzhan = WuJinJiTanWGData.Instance:GetIsZhuZhan()
	return is_zhuzhan
end

function WujinjitanFbSceneLogic:CanMonsterDoJump()
	-- 屏蔽怪跳跃 2021/10/8
	return false--true
end

function WujinjitanFbSceneLogic:GetMonsterJumpLogicPos(monster_obj)
	if monster_obj:IsBoss() then
		return WuJinJiTanWGData.Instance:GetBossJumpPoint()
	end
	local fb_info = WuJinJiTanWGData.Instance:GetWuJinJiTanAllInfo()
	local vo = monster_obj:GetVo()
	if vo and vo.special_param then
		local cfg = WuJinJiTanWGData.Instance:GetMonsterCfg(fb_info.curr_wave_index or 1, vo.special_param)
		if cfg then
			return cfg.jumppos_x, cfg.jumppos_y
		end
	end

	--没找到，就使用随机位置
	local x = GameMath.Rand(25, 45)
	local y = GameMath.Rand(35, 55)
	return x, y
end

function WujinjitanFbSceneLogic:CanAutoPick()
	return true
end

function WujinjitanFbSceneLogic:IsRoleEnemy(target_obj, main_role)
	local target_vo = target_obj:GetVo() or {}
	if target_vo.is_shadow == 1 then
		return false
	end

	return BaseSceneLogic.IsRoleEnemy(self, target_obj, main_role)
end