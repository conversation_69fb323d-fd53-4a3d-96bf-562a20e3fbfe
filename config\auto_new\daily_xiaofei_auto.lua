-- M-每日消费.xls
local item_table={
[1]={item_id=26355,num=1,is_bind=1},
[2]={item_id=39153,num=1,is_bind=1},
[3]={item_id=26122,num=3,is_bind=1},
[4]={item_id=26148,num=5,is_bind=1},
[5]={item_id=26149,num=5,is_bind=1},
[6]={item_id=22008,num=1,is_bind=1},
[7]={item_id=26356,num=2,is_bind=1},
[8]={item_id=26122,num=5,is_bind=1},
[9]={item_id=26148,num=8,is_bind=1},
[10]={item_id=26149,num=8,is_bind=1},
[11]={item_id=22008,num=2,is_bind=1},
[12]={item_id=26356,num=3,is_bind=1},
[13]={item_id=26122,num=8,is_bind=1},
[14]={item_id=26148,num=12,is_bind=1},
[15]={item_id=26149,num=12,is_bind=1},
[16]={item_id=22008,num=3,is_bind=1},
[17]={item_id=26355,num=4,is_bind=1},
[18]={item_id=39153,num=2,is_bind=1},
[19]={item_id=26123,num=1,is_bind=1},
[20]={item_id=26148,num=18,is_bind=1},
[21]={item_id=26149,num=18,is_bind=1},
[22]={item_id=22008,num=5,is_bind=1},
[23]={item_id=26357,num=1,is_bind=1},
[24]={item_id=39159,num=1,is_bind=1},
[25]={item_id=26123,num=2,is_bind=1},
[26]={item_id=26148,num=30,is_bind=1},
[27]={item_id=26149,num=30,is_bind=1},
[28]={item_id=22008,num=8,is_bind=1},
[29]={item_id=26357,num=2,is_bind=1},
[30]={item_id=26123,num=3,is_bind=1},
[31]={item_id=26148,num=50,is_bind=1},
[32]={item_id=26149,num=50,is_bind=1},
[33]={item_id=22008,num=12,is_bind=1},
[34]={item_id=26123,num=10,is_bind=1},
[35]={item_id=26148,num=2,is_bind=1},
[36]={item_id=26149,num=15,is_bind=1},
[37]={item_id=22008,num=10,is_bind=1},
[38]={item_id=39159,num=2,is_bind=1},
[39]={item_id=26123,num=20,is_bind=1},
[40]={item_id=26149,num=22,is_bind=1},
[41]={item_id=22008,num=14,is_bind=1},
[42]={item_id=26123,num=30,is_bind=1},
[43]={item_id=26149,num=29,is_bind=1},
[44]={item_id=22008,num=18,is_bind=1},
[45]={item_id=26356,num=1,is_bind=1},
[46]={item_id=22008,num=4,is_bind=1},
[47]={item_id=22008,num=6,is_bind=1},
[48]={item_id=26122,num=2,is_bind=1},
[49]={item_id=26149,num=3,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
xiaofei_cfg={
{reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{id=2,consumer_num=6000,reward_item={[0]=item_table[7],[1]=item_table[2],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11]},},
{id=3,consumer_num=20000,reward_item={[0]=item_table[12],[1]=item_table[2],[2]=item_table[13],[3]=item_table[14],[4]=item_table[15],[5]=item_table[16]},},
{id=4,consumer_num=40000,reward_item={[0]=item_table[17],[1]=item_table[18],[2]=item_table[19],[3]=item_table[20],[4]=item_table[21],[5]=item_table[22]},},
{id=5,consumer_num=65000,reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[25],[3]=item_table[26],[4]=item_table[27],[5]=item_table[28]},},
{id=6,consumer_num=120000,reward_item={[0]=item_table[29],[1]=item_table[24],[2]=item_table[30],[3]=item_table[31],[4]=item_table[32],[5]=item_table[33]},},
{open_server_day=3,},
{open_server_day=3,},
{open_server_day=3,},
{open_server_day=3,},
{open_server_day=3,},
{open_server_day=3,},
{open_server_day=4,},
{open_server_day=4,},
{open_server_day=4,},
{open_server_day=4,},
{open_server_day=4,},
{open_server_day=4,},
{open_server_day=5,},
{open_server_day=5,},
{open_server_day=5,},
{open_server_day=5,},
{open_server_day=5,},
{open_server_day=5,},
{open_server_day=6,},
{open_server_day=6,},
{open_server_day=6,},
{open_server_day=6,},
{open_server_day=6,},
{open_server_day=6,},
{open_server_day=7,},
{open_server_day=7,},
{open_server_day=7,},
{open_server_day=7,},
{open_server_day=7,},
{open_server_day=7,},
{open_server_day=8,},
{open_server_day=8,},
{open_server_day=8,},
{open_server_day=8,reward_item={[0]=item_table[1],[1]=item_table[18],[2]=item_table[34],[3]=item_table[35],[4]=item_table[36],[5]=item_table[37]},},
{open_server_day=8,reward_item={[0]=item_table[23],[1]=item_table[38],[2]=item_table[39],[3]=item_table[35],[4]=item_table[40],[5]=item_table[41]},},
{open_server_day=8,reward_item={[0]=item_table[23],[1]=item_table[38],[2]=item_table[42],[3]=item_table[35],[4]=item_table[43],[5]=item_table[44]},},
{open_server_day=9,},
{open_server_day=9,reward_item={[0]=item_table[45],[1]=item_table[2],[2]=item_table[8],[3]=item_table[35],[4]=item_table[5],[5]=item_table[46]},},
{open_server_day=9,},
{open_server_day=9,},
{open_server_day=9,},
{open_server_day=9,},
{open_server_day=10,},
{open_server_day=10,},
{open_server_day=10,},
{open_server_day=10,},
{open_server_day=10,},
{open_server_day=10,},
{open_server_day=11,},
{open_server_day=11,},
{open_server_day=11,},
{open_server_day=11,},
{open_server_day=11,},
{open_server_day=11,},
{open_server_day=12,},
{open_server_day=12,},
{open_server_day=12,reward_item={[0]=item_table[45],[1]=item_table[2],[2]=item_table[13],[3]=item_table[35],[4]=item_table[10],[5]=item_table[47]},},
{open_server_day=12,},
{open_server_day=12,},
{open_server_day=12,}
},

xiaofei_cfg_meta_table_map={
[55]=1,	-- depth:1
[13]=55,	-- depth:2
[19]=13,	-- depth:3
[7]=19,	-- depth:4
[63]=3,	-- depth:1
[40]=4,	-- depth:1
[41]=5,	-- depth:1
[42]=6,	-- depth:1
[44]=2,	-- depth:1
[45]=63,	-- depth:2
[46]=40,	-- depth:2
[47]=41,	-- depth:2
[48]=42,	-- depth:2
[50]=44,	-- depth:2
[51]=45,	-- depth:3
[52]=46,	-- depth:3
[53]=47,	-- depth:3
[54]=48,	-- depth:3
[56]=2,	-- depth:1
[57]=3,	-- depth:1
[58]=4,	-- depth:1
[59]=5,	-- depth:1
[64]=52,	-- depth:4
[60]=6,	-- depth:1
[62]=50,	-- depth:3
[39]=51,	-- depth:4
[33]=39,	-- depth:5
[36]=54,	-- depth:4
[8]=56,	-- depth:2
[9]=57,	-- depth:2
[10]=58,	-- depth:2
[11]=59,	-- depth:2
[12]=60,	-- depth:2
[14]=8,	-- depth:3
[15]=9,	-- depth:3
[16]=10,	-- depth:3
[17]=11,	-- depth:3
[18]=12,	-- depth:3
[20]=14,	-- depth:4
[38]=62,	-- depth:4
[21]=15,	-- depth:4
[23]=17,	-- depth:4
[24]=18,	-- depth:4
[26]=38,	-- depth:5
[27]=33,	-- depth:6
[28]=64,	-- depth:5
[29]=53,	-- depth:4
[30]=36,	-- depth:5
[32]=26,	-- depth:6
[65]=29,	-- depth:5
[34]=28,	-- depth:6
[35]=65,	-- depth:6
[22]=16,	-- depth:4
[66]=30,	-- depth:6
},
other_default_table={show_fabao_id=10106,},

xiaofei_cfg_default_table={open_server_day=2,id=1,consumer_num=1200,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[48],[3]=item_table[35],[4]=item_table[49],[5]=item_table[11]},}

}

