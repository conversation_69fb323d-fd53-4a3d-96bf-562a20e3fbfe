-- 鸭子赛跑主view
DuckRaceView = DuckRaceView or BaseClass(SafeBaseView)

DuckRaceIndex = {
	bet = TabIndex.duck_race_bet,      -- 小鸭疾走下注
	results = TabIndex.duck_race_results, -- 小鸭疾走比赛结果
}

function DuckRaceView:__init()
	self.view_style = ViewStyle.Half
	self.view_name = GuideModuleName.DuckRace
	self.default_index = DuckRaceIndex.bet

	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/duck_race_ui_prefab", "duck_race_common_layout")
	self:AddViewResource(0, "uis/view/duck_race_ui_prefab", "VerticalTabbar")
	self:AddViewResource(DuckRaceIndex.bet, "uis/view/duck_race_ui_prefab", "duck_race_bet_layout")      -- 下注
	self:AddViewResource(DuckRaceIndex.results, "uis/view/duck_race_ui_prefab", "duck_race_results_layout") -- 比赛结果
	self.remind_tab = {
		{ RemindName.DuckRaceFetchRemind },
	}
end

function DuckRaceView:__delete()

end

function DuckRaceView:OpenCallBack()

end

function DuckRaceView:CloseCallBack()
	DuckRaceWGCtrl.Instance:CloseDuckDetailsTips()
	TipWGCtrl.Instance:CloseRuleTip()
end

function DuckRaceView:LoadCallBack()
	self:InitTabbar()
	XUI.AddClickEventListener(self.node_list["rule_btn"], BindTool.Bind(self.OnClickRule, self))
	XUI.AddClickEventListener(self.node_list["coin_icon"], BindTool.Bind(self.OnClickPlayCoin, self))
end

function DuckRaceView:InitTabbar()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.DuckRace.VerTabGroup, nil, "uis/view/duck_race_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
end

function DuckRaceView:ReleaseCallBack()
	self:CancelNextStateTimer()

	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:BetReleaseCallBack()
	self:ResultsReleaseCallBack()
end

function DuckRaceView:LoadIndexCallBack(index)
	if index == DuckRaceIndex.bet then
		self:BetLoadCallBack()
	elseif index == DuckRaceIndex.results then
		self:ResultsLoadCallBack()
	end
end

function DuckRaceView:ShowIndexCallBack(index)
	if index == DuckRaceIndex.bet then
		-- self:BetShowIndexCallBack()
	elseif index == DuckRaceIndex.results then
		-- self:ResultsShowIndexCallBack()
	end
end

function DuckRaceView:OnFlush(param_list, index)
	if index == DuckRaceIndex.bet then
		self:BetOnFlush(param_list)
	elseif index == DuckRaceIndex.results then
		self:ResultsOnFlush(param_list)
	end
	self:FlushCommon()
end

function DuckRaceView:FlushCommon()
	self.node_list["cur_rount_desc"].text.text = DuckRaceWGData.Instance:GetRoundDesc()
	self.node_list["play_coint"].text.text = DuckRaceWGData.Instance:GetPlayCoin()

	self:CancelNextStateTimer()
	self:FlushNextStateTimeDesc()
	self.next_state_timer = GlobalTimerQuest:AddRunQuest(function()
		self:FlushNextStateTimeDesc()
	end, 1)
end

function DuckRaceView:CancelNextStateTimer()
	if self.next_state_timer then
		GlobalTimerQuest.CancelQuest(self.next_state_timer)
		self.next_state_timer = nil
	end
end

-- 刷新下注剩余时间
function DuckRaceView:FlushNextStateTimeDesc()
	if not self.node_list["next_state_desc"] then
		return
	end
	if DuckRaceWGData.Instance:GetIsGameOver() then
		self.node_list["next_state_desc"].text.text = Language.DuckRace.IsGameOver
	elseif DuckRaceWGData.Instance:GetNextStateTimstamp() - TimeWGCtrl.Instance:GetServerTime() > 0 and DuckRaceWGData.Instance:GetCurRoundState() == DUCK_RACE_ROUND_STATE.BET then
		self.node_list["next_state_desc"].text.text = string.format(Language.DuckRace.BetCountDown,
			math.floor(DuckRaceWGData.Instance:GetNextStateTimstamp() - TimeWGCtrl.Instance:GetServerTime()))
	elseif DuckRaceWGData.Instance:GetCurRoundState() == DUCK_RACE_ROUND_STATE.RESULT then
		if DuckRaceWGData.Instance:GetNextRoundTimstamp() - TimeWGCtrl.Instance:GetServerTime() > 0 then
			self.node_list["next_state_desc"].text.text = string.format(Language.DuckRace.NextRoundCountDown,
				math.floor(DuckRaceWGData.Instance:GetNextRoundTimstamp() - TimeWGCtrl.Instance:GetServerTime()))
		else
			self.node_list["next_state_desc"].text.text = Language.DuckRace.IsGameOver
		end
	else
		self.node_list["next_state_desc"].text.text = ""
	end
end

function DuckRaceView:OnClickRule()
	RuleTip.Instance:SetContent(Language.DuckRace.RuleTips, Language.DuckRace.RuleTipsTitle)
end

-- 点击应援币
function DuckRaceView:OnClickPlayCoin()
	local item_id = DuckRaceWGData.Instance:GetOtherCfg().play_coin_item
	TipWGCtrl.Instance:OpenItem({ item_id = tonumber(item_id) }, ItemTip.FROM_NORMAL, nil)
end
