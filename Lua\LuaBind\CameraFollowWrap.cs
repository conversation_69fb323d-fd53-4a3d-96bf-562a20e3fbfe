﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CameraFollowWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(CameraFollow), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("SetDistanceChangeCallback", SetDistanceChangeCallback);
		<PERSON><PERSON>unction("SetCameraType", SetCameraType);
		<PERSON><PERSON>Function("SetFreeCamera", SetFreeCamera);
		L.RegFunction("SetFixedCamera", SetFixedCamera);
		<PERSON>.RegFunction("ClearAllTransitionState", ClearAllTransitionState);
		<PERSON><PERSON>Function("Swipe", Swipe);
		<PERSON><PERSON>Function("Pinch", Pinch);
		L.RegFunction("SyncImmediate", SyncImmediate);
		L.RegFunction("SyncRotation", SyncRotation);
		<PERSON><PERSON>RegFunction("ClampRotationAndDistance", ClampRotationAndDistance);
		<PERSON><PERSON>RegFunction("ChangeAngle", ChangeAngle);
		<PERSON><PERSON>unction("CreateFocalPoint", CreateFocalPoint);
		<PERSON><PERSON>RegFunction("ClearFocalPoint", ClearFocalPoint);
		L.RegFunction("MoveToTarget", MoveToTarget);
		L.RegFunction("Bind", Bind);
		L.RegFunction("AddTarget", AddTarget);
		L.RegFunction("RemoveTarget", RemoveTarget);
		L.RegFunction("ClearTargetGroup", ClearTargetGroup);
		L.RegFunction("EnableTargetGroupMode", EnableTargetGroupMode);
		L.RegFunction("ResetManualSwipeState", ResetManualSwipeState);
		L.RegFunction("DisableTargetGroupMode", DisableTargetGroupMode);
		L.RegFunction("StopCameraDistanceAnimationImmediately", StopCameraDistanceAnimationImmediately);
		L.RegFunction("PauseCameraDistanceAnimation", PauseCameraDistanceAnimation);
		L.RegFunction("ResumeCameraDistanceAnimation", ResumeCameraDistanceAnimation);
		L.RegFunction("DOFieldOfView", DOFieldOfView);
		L.RegFunction("DOCameraDistanceTweenTo", DOCameraDistanceTweenTo);
		L.RegFunction("DOCameraDistanceElasticTween", DOCameraDistanceElasticTween);
		L.RegFunction("DOCameraDistanceElasticTweenTo", DOCameraDistanceElasticTweenTo);
		L.RegFunction("DOCameraDistanceTwoStageTween", DOCameraDistanceTwoStageTween);
		L.RegFunction("SetAutoPathfindingState", SetAutoPathfindingState);
		L.RegFunction("AdjustCameraToBehindTarget", AdjustCameraToBehindTarget);
		L.RegFunction("ForceTransitionToAngle", ForceTransitionToAngle);
		L.RegFunction("ForceTransitionToYaw", ForceTransitionToYaw);
		L.RegFunction("StopForcedAngleTransition", StopForcedAngleTransition);
		L.RegFunction("IsForcedAngleTransitioning", IsForcedAngleTransitioning);
		L.RegFunction("SetEnterBattleState", SetEnterBattleState);
		L.RegFunction("UpdateEnterBattleDistance", UpdateEnterBattleDistance);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("CurrentCameraType", get_CurrentCameraType, set_CurrentCameraType);
		L.RegVar("targetOffset", get_targetOffset, set_targetOffset);
		L.RegVar("smoothOffsetSpeed", get_smoothOffsetSpeed, set_smoothOffsetSpeed);
		L.RegVar("AllowRotation", get_AllowRotation, set_AllowRotation);
		L.RegVar("AllowXRotation", get_AllowXRotation, set_AllowXRotation);
		L.RegVar("AllowYRotation", get_AllowYRotation, set_AllowYRotation);
		L.RegVar("OriginAngle", get_OriginAngle, set_OriginAngle);
		L.RegVar("RotationSensitivity", get_RotationSensitivity, set_RotationSensitivity);
		L.RegVar("MinPitchAngle", get_MinPitchAngle, set_MinPitchAngle);
		L.RegVar("MaxPitchAngle", get_MaxPitchAngle, set_MaxPitchAngle);
		L.RegVar("MinYawAngle", get_MinYawAngle, set_MinYawAngle);
		L.RegVar("MaxYawAngle", get_MaxYawAngle, set_MaxYawAngle);
		L.RegVar("RotationSmoothing", get_RotationSmoothing, set_RotationSmoothing);
		L.RegVar("AutoSmoothing", get_AutoSmoothing, set_AutoSmoothing);
		L.RegVar("AllowZoom", get_AllowZoom, set_AllowZoom);
		L.RegVar("MaxDistance", get_MaxDistance, set_MaxDistance);
		L.RegVar("MinDistance", get_MinDistance, set_MinDistance);
		L.RegVar("ZoomSmoothing", get_ZoomSmoothing, set_ZoomSmoothing);
		L.RegVar("autoRotationCD", get_autoRotationCD, set_autoRotationCD);
		L.RegVar("autoRotationSpeedX", get_autoRotationSpeedX, set_autoRotationSpeedX);
		L.RegVar("autoRotationSpeedY", get_autoRotationSpeedY, set_autoRotationSpeedY);
		L.RegVar("bestRotationX", get_bestRotationX, set_bestRotationX);
		L.RegVar("isAutoPathfinding", get_isAutoPathfinding, set_isAutoPathfinding);
		L.RegVar("autoAdjustDelay", get_autoAdjustDelay, set_autoAdjustDelay);
		L.RegVar("autoAdjustSpeed", get_autoAdjustSpeed, set_autoAdjustSpeed);
		L.RegVar("behindPlayerAngleThreshold", get_behindPlayerAngleThreshold, set_behindPlayerAngleThreshold);
		L.RegVar("behindPlayerAngleByMannul", get_behindPlayerAngleByMannul, set_behindPlayerAngleByMannul);
		L.RegVar("angleReachThreshold", get_angleReachThreshold, set_angleReachThreshold);
		L.RegVar("isEnterBattle", get_isEnterBattle, set_isEnterBattle);
		L.RegVar("forcedAngleTransitionSpeed", get_forcedAngleTransitionSpeed, set_forcedAngleTransitionSpeed);
		L.RegVar("Target", get_Target, set_Target);
		L.RegVar("FieldOfView", get_FieldOfView, set_FieldOfView);
		L.RegVar("Distance", get_Distance, set_Distance);
		L.RegVar("AutoRotation", get_AutoRotation, set_AutoRotation);
		L.RegVar("AudioListener", get_AudioListener, set_AudioListener);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDistanceChangeCallback(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 2);
			obj.SetDistanceChangeCallback(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCameraType(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			CameraFollow.CameraType arg0 = (CameraFollow.CameraType)ToLua.CheckObject(L, 2, typeof(CameraFollow.CameraType));
			obj.SetCameraType(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetFreeCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.SetFreeCamera();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetFixedCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.SetFixedCamera();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearAllTransitionState(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.ClearAllTransitionState();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Swipe(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.Swipe(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Pinch(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Pinch(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SyncImmediate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.SyncImmediate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SyncRotation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.SyncRotation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClampRotationAndDistance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.ClampRotationAndDistance();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeAngle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.ChangeAngle(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateFocalPoint(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				obj.CreateFocalPoint();
				return 0;
			}
			else if (count == 2)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
				obj.CreateFocalPoint(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraFollow.CreateFocalPoint");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearFocalPoint(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.ClearFocalPoint();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MoveToTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.MoveToTarget();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Bind(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			CameraFollow o = CameraFollow.Bind(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddTarget(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
				obj.AddTarget(arg0);
				return 0;
			}
			else if (count == 3)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.AddTarget(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.AddTarget(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraFollow.AddTarget");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveTarget(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.RemoveTarget(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearTargetGroup(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.ClearTargetGroup();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EnableTargetGroupMode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.EnableTargetGroupMode();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetManualSwipeState(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.ResetManualSwipeState();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DisableTargetGroupMode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.DisableTargetGroupMode();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopCameraDistanceAnimationImmediately(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.StopCameraDistanceAnimationImmediately();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PauseCameraDistanceAnimation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.PauseCameraDistanceAnimation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResumeCameraDistanceAnimation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.ResumeCameraDistanceAnimation();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DOFieldOfView(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			DG.Tweening.Tweener o = obj.DOFieldOfView(arg0, arg1);
			ToLua.PushObject(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DOCameraDistanceTweenTo(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			DG.Tweening.Sequence o = obj.DOCameraDistanceTweenTo(arg0, arg1);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DOCameraDistanceElasticTween(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			DG.Tweening.Sequence o = obj.DOCameraDistanceElasticTween(arg0, arg1, arg2);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DOCameraDistanceElasticTweenTo(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			DG.Tweening.Sequence o = obj.DOCameraDistanceElasticTweenTo(arg0, arg1, arg2);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DOCameraDistanceTwoStageTween(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 5);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			float arg3 = (float)LuaDLL.luaL_checknumber(L, 5);
			DG.Tweening.Sequence o = obj.DOCameraDistanceTwoStageTween(arg0, arg1, arg2, arg3);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetAutoPathfindingState(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.SetAutoPathfindingState(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AdjustCameraToBehindTarget(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				obj.AdjustCameraToBehindTarget();
				return 0;
			}
			else if (count == 2)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.AdjustCameraToBehindTarget(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraFollow.AdjustCameraToBehindTarget");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceTransitionToAngle(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
				obj.ForceTransitionToAngle(arg0);
				return 0;
			}
			else if (count == 3)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				obj.ForceTransitionToAngle(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.ForceTransitionToAngle(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraFollow.ForceTransitionToAngle");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceTransitionToYaw(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.ForceTransitionToYaw(arg0);
				return 0;
			}
			else if (count == 3)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				obj.ForceTransitionToYaw(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.ForceTransitionToYaw(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraFollow.ForceTransitionToYaw");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopForcedAngleTransition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			obj.StopForcedAngleTransition();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsForcedAngleTransitioning(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
			bool o = obj.IsForcedAngleTransitioning();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnterBattleState(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.SetEnterBattleState(arg0);
				return 0;
			}
			else if (count == 3)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				obj.SetEnterBattleState(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraFollow.SetEnterBattleState");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateEnterBattleDistance(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				obj.UpdateEnterBattleDistance(arg0);
				return 0;
			}
			else if (count == 3)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				obj.UpdateEnterBattleDistance(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				CameraFollow obj = (CameraFollow)ToLua.CheckObject<CameraFollow>(L, 1);
				float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
				float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
				obj.UpdateEnterBattleDistance(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: CameraFollow.UpdateEnterBattleDistance");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CurrentCameraType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			CameraFollow.CameraType ret = obj.CurrentCameraType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CurrentCameraType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_targetOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.Vector3 ret = obj.targetOffset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_smoothOffsetSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.smoothOffsetSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index smoothOffsetSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllowRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool ret = obj.AllowRotation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllowXRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool ret = obj.AllowXRotation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowXRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllowYRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool ret = obj.AllowYRotation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowYRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_OriginAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.Vector2 ret = obj.OriginAngle;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OriginAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RotationSensitivity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.Vector2 ret = obj.RotationSensitivity;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotationSensitivity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MinPitchAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.MinPitchAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinPitchAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaxPitchAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.MaxPitchAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxPitchAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MinYawAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.MinYawAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinYawAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaxYawAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.MaxYawAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxYawAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RotationSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.RotationSmoothing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotationSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool ret = obj.AutoSmoothing;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllowZoom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool ret = obj.AllowZoom;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowZoom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MaxDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.MaxDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MinDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.MinDistance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ZoomSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.ZoomSmoothing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ZoomSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoRotationCD(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.autoRotationCD;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoRotationCD on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoRotationSpeedX(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.autoRotationSpeedX;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoRotationSpeedX on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoRotationSpeedY(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.autoRotationSpeedY;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoRotationSpeedY on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_bestRotationX(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.bestRotationX;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bestRotationX on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isAutoPathfinding(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool ret = obj.isAutoPathfinding;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isAutoPathfinding on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoAdjustDelay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.autoAdjustDelay;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoAdjustDelay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoAdjustSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.autoAdjustSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoAdjustSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_behindPlayerAngleThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.behindPlayerAngleThreshold;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index behindPlayerAngleThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_behindPlayerAngleByMannul(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.behindPlayerAngleByMannul;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index behindPlayerAngleByMannul on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_angleReachThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.angleReachThreshold;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index angleReachThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isEnterBattle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool ret = obj.isEnterBattle;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isEnterBattle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_forcedAngleTransitionSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.forcedAngleTransitionSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index forcedAngleTransitionSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Target(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.Transform ret = obj.Target;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Target on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FieldOfView(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.FieldOfView;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FieldOfView on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Distance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float ret = obj.Distance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Distance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AutoRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool ret = obj.AutoRotation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AudioListener(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.AudioListener ret = obj.AudioListener;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AudioListener on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CurrentCameraType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			CameraFollow.CameraType arg0 = (CameraFollow.CameraType)ToLua.CheckObject(L, 2, typeof(CameraFollow.CameraType));
			obj.CurrentCameraType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CurrentCameraType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_targetOffset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.targetOffset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetOffset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_smoothOffsetSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.smoothOffsetSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index smoothOffsetSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AllowRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AllowRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AllowXRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AllowXRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowXRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AllowYRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AllowYRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowYRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_OriginAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.OriginAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index OriginAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RotationSensitivity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.RotationSensitivity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotationSensitivity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MinPitchAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MinPitchAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinPitchAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MaxPitchAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MaxPitchAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxPitchAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MinYawAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MinYawAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinYawAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MaxYawAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MaxYawAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxYawAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RotationSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.RotationSmoothing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RotationSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AutoSmoothing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AllowZoom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AllowZoom = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AllowZoom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MaxDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MaxDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MaxDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MinDistance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MinDistance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MinDistance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ZoomSmoothing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ZoomSmoothing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ZoomSmoothing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoRotationCD(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.autoRotationCD = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoRotationCD on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoRotationSpeedX(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.autoRotationSpeedX = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoRotationSpeedX on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoRotationSpeedY(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.autoRotationSpeedY = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoRotationSpeedY on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_bestRotationX(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.bestRotationX = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index bestRotationX on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isAutoPathfinding(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isAutoPathfinding = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isAutoPathfinding on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoAdjustDelay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.autoAdjustDelay = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoAdjustDelay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoAdjustSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.autoAdjustSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoAdjustSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_behindPlayerAngleThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.behindPlayerAngleThreshold = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index behindPlayerAngleThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_behindPlayerAngleByMannul(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.behindPlayerAngleByMannul = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index behindPlayerAngleByMannul on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_angleReachThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.angleReachThreshold = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index angleReachThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isEnterBattle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isEnterBattle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isEnterBattle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_forcedAngleTransitionSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.forcedAngleTransitionSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index forcedAngleTransitionSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Target(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.Target = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Target on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_FieldOfView(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.FieldOfView = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FieldOfView on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Distance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Distance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Distance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AutoRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.AutoRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AutoRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AudioListener(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraFollow obj = (CameraFollow)o;
			UnityEngine.AudioListener arg0 = (UnityEngine.AudioListener)ToLua.CheckObject(L, 2, typeof(UnityEngine.AudioListener));
			obj.AudioListener = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AudioListener on a nil value");
		}
	}
}

