-- 初始化仙盟列表界面
function GuildView:InitGuildListView()
	self.guildlist_view = self.node_list.layout_guildlist
	self:GetLayoutListUnenterbtn(self.guildlist_view.layout_unenter_btn)
	self:GetBtnListCreate(self.guildlist_view.btn_create_guild)
	self:GetBtnListUlook(self.guildlist_view.btn_look_guild)
	self:GetBtnListFind(self.guildlist_view.btn_find_guild)
	-- self.btn_list_look = self.guildlist_view.btn_loook
	self.is_tanchuang = false
	
	self:GetGuildName(self.node_list["guild_name"])
	self:GetGuildRoleName(self.node_list["guild_role_name"])
	self.select_guildlist_item = nil
	self.can_enlist_guild = true
	self.click_list_flag = false
	self.old_guild = 0
	self:RegisterGuildListEvent()
	-- self.node_list["guild_name"].event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnClickInputName, self))
	-- self.click_guild_name = false
	-- self.node_list["guild_role_name"].event_trigger_listener:AddPointerClickListener(BindTool.Bind(self.OnClickInputRoleName, self))

	self.node_list.has_guild_declaration.text.text = Language.Guild.DefaultGuildNotice

	XUI.AddClickEventListener(self.node_list.btn_apply_guild, BindTool.Bind(self.OnClickApplyGuild, self))
end

function GuildView:DeleteGuildListView()
	if self.guild_list then
		self.guild_list:DeleteMe()
		self.guild_list = nil
	end

	if self.no_guild_list then
		self.no_guild_list:DeleteMe()
		self.no_guild_list = nil
	end


	self.click_list_flag = nil
	self.is_tanchuang = nil
	self.old_guild = 0
end

function GuildView:GetOneKeyShenQing()
	local btn_list_ulook = self:GetBtnListUlook()
	if btn_list_ulook then
		GuildWGData.Instance:SetGuideGuild(true)
		return btn_list_ulook, BindTool.Bind1(self.OnOneKeyShenQingGuild, self)
	end
end

function GuildView:OnClickInputName()
	-- if self.node_list.guild_name.input_field.text == "" then
	-- 	self.node_list.lbl_placeholder_name:CustomSetActive(false)
	-- end
end

function GuildView:OnClickInputRoleName()
	-- if self.node_list.guild_role_name.input_field.text == "" then
	-- 	self.node_list.lbl_placeholder_role_name:CustomSetActive(false)
	-- end
end

-- 创建列表控件
function GuildView:CreateGuildList()
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	if self.no_guild_list == nil then
		self.no_guild_list = AsyncListView.New(NoGuildListItem, self.node_list["ph_no_guild_list"])
		self.no_guild_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectNoGuildListItemHandler, self))  --点击宗门item 刷新右侧信息(现已去掉)
		self.no_guild_list:SetDefaultSelectIndex(1)
	end

	if nil == self.guild_list then
		self.guild_list = AsyncListView.New(GuildListItem, self.node_list["ph_list"])
		self.guild_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectGuildListItemHandler, self))
		self.guild_list:SetDefaultSelectIndex(1)
	else
		self.click_list_flag = false
		if mainrolevo.guild_id > 0 then
			if self.node_list.ph_guild_list:GetActive() then
				if self.guild_list then
					self.guild_list:JumpToTop()
					self.guild_list:SelectIndex(1)
				end
			end
		else
			if self.node_list.layout_no_guild:GetActive() then
				if self.no_guild_list then
					self.no_guild_list:JumpToTop()
					self.no_guild_list:SelectIndex(1)
				end
			end
		end
	end
end

function GuildView:OnSelectNoGuildListItemHandler(item)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	self.select_guildlist_item = item
	self.can_enlist_guild = item.data.can_enter
	GuildWGData.Instance:SetNoGuildListSelectIndex(item:GetIndex())
	self.data = item.data
	self:FlushCurrentGuildInfo()

	if nil == self.data.tuanzhang_uid or self.data.tuanzhang_uid <= 0 then
		if self.data.guild_id and self.data.guild_id > 0 then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, self.data.guild_id)--请求成员列表信息
		end
	end

	if not self.click_list_flag then
		self.click_list_flag = true
		return
	end
end

function GuildView:OnSelectGuildListItemHandler(item)
	local is_shield = GuildWGData.Instance:GetIsShieldSeeGuildMember()
	if is_shield then
		return
	end

	if nil == item then
		return
	end

	if nil == item.data then
		return
	end

	self.select_guildlist_item = item
	self.can_enlist_guild = item.data.can_enter
	GuildWGData.Instance:SetGuildListSelectIndex(item:GetIndex())
	self.data = item.data
	if not self.click_list_flag then
		self.click_list_flag = true
		return
	end
	
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo() 
	if mainrolevo.guild_id > 0 then
		GuildWGCtrl.Instance:OpenGuildInfoView(self.data) --打开帮派信息列表
	end
end

--查看宗门信息
function GuildView:OpenGuildInfoView()
	GuildWGCtrl.Instance:OpenGuildInfoView(self.data)
end

-- 显示创建仙盟按钮
function GuildView:ShowCreateGuildBtn(is_show)
	local layout_list_unenterbtn = self:GetLayoutListUnenterbtn()
	if layout_list_unenterbtn ~= nil then
		layout_list_unenterbtn:SetActive(is_show)
		self.node_list.layout_no_guild:SetActive(is_show)
		self.node_list.ph_guild_list:SetActive(not is_show)
		self.node_list.guild_role_name.input_field.text = ""
		self.node_list.guild_name.input_field.text = ""
		self.is_tanchuang = false
	end
end

-- 注册仙盟列表界面事件
function GuildView:RegisterGuildListEvent()
	local btn_list_create = self:GetBtnListCreate()
	if btn_list_create ~= nil then
		btn_list_create.button:AddClickListener(BindTool.Bind(self.OnCreateGuildHandler, self))
	end

	local btn_list_ulook = self:GetBtnListUlook()
	if btn_list_ulook ~= nil then
		btn_list_ulook.button:AddClickListener(BindTool.Bind(self.OnOneKeyShenQingGuild, self))
	end
	local btn_list_find = self:GetBtnListFind()
	if btn_list_find ~= nil then
		btn_list_find.button:AddClickListener(BindTool.Bind(self.OnFindGuildHandler, self))
	end

	--self.node_list["btn_check"].button:AddClickListener(BindTool.Bind(self.OpenGuildInfoView, self)) --查看宗门信息
	self.node_list["btn_find_guild"].button:AddClickListener(BindTool.Bind(self.OnFindGuildHandler, self))
end

-- 查找仙盟事件
function GuildView:OnFindGuildHandler()
	self.is_tanchuang = true
	self:FlushGuildListDatasource()
end

-- 创建仙盟事件
function GuildView:OnCreateGuildHandler()
	GuildWGCtrl.Instance:OpenCreateGuildView()
end

-- 一键申请
function GuildView:OnOneKeyShenQingGuild()
	if self.no_guild_list then
		GuildWGCtrl.Instance:SendJoinGuildReq(0, GameEnum.SEED_AUTO_JOIN_GUILD)
	end
end

-- 查看仙盟
function GuildView:OnLookGuildInfoHandler()
	if nil == self.select_guildlist_item then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.SelectItem)
		return
	end
	if nil ~= self.select_guildlist_item.data then
		GuildDataConst.GUILD_IOPEN.InfoView = true
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, self.select_guildlist_item.data.guild_id)
	end
end

-- 刷新仙盟列表数据源
function GuildView:FlushGuildListDatasource()
	local show_list = GuildWGData.Instance:GetFakeGuildShowList()

	local guild_role_name = self:GetGuildRoleName()
	local guild_name = self:GetGuildName()
	local need_find_name
	if guild_name and "" ~= guild_name.input_field.text then
		need_find_name = guild_name.input_field.text
	elseif guild_role_name and "" ~= guild_role_name.input_field.text then
		need_find_name = guild_role_name.input_field.text
	end

	self.guild_datasource = {}
	if need_find_name then
		for k,v in pairs(show_list) do
			if nil ~= string.find(v.guild_name, need_find_name) or
			nil ~= string.find(v.mengzhu_name, need_find_name) then
				table.insert(self.guild_datasource, v)
			end
		end
	else
		self.guild_datasource = show_list
	end

	local guild_id = GameVoManager.Instance:GetMainRoleVo().guild_id
	if guild_id > 0 then
		if nil ~= self.guild_list then
			self.guild_list:SetDataList(self.guild_datasource)
			--self.node_list.list_arrow:SetActive(#self.guild_datasource > 10)
		end
	else
		if self.no_guild_list ~= nil then
			self.no_guild_list:SetDataList(self.guild_datasource)
			self:FlushCurrentGuildInfo()
			--self.node_list.no_list_arrow:SetActive(#self.guild_datasource > 9) --列表下滑箭头提示  新界面不需要,暂时注释(防止以后要加回来)
		end
	end

	if 0 == #self.guild_datasource and self.is_tanchuang then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.NotFindGuild)
		self.is_tanchuang = false
	end

	if 0 == #self.guild_datasource then
        self.node_list.img_member:SetActive(true)
		self.node_list.no_guild_data:CustomSetActive(true)
		self.node_list.has_guild_data:CustomSetActive(false)
        --self.node_list.btn_check:SetActive(false)
        --self.node_list.lbl_no_guild:SetActive(false)
		--self.node_list.layout_info_show:SetActive(false)
	else
        self.node_list.img_member:SetActive(false)
		self.node_list.no_guild_data:CustomSetActive(false)
		self.node_list.has_guild_data:CustomSetActive(true)
        --self.node_list.btn_check:SetActive(true)
	end	
end

-- 刷新仙盟列表界面数据
function GuildView:OnFlushGuildList()
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	if 0 == mainrolevo.guild_id then 									-- 是否加入仙盟
		self:ShowCreateGuildBtn(true)
		local asset = "a3_ty_bg4"
		self.node_list.RawImage_tongyong.raw_image:LoadSprite(ResPath.GetRawImagesPNG(asset))
	else
		self:ShowCreateGuildBtn(false)
	end
	self:FlushGuildListDatasource()
end

function GuildView:GetGuildItemByGuide(index)
	local guild_list = GuildWGData.Instance:GetGuildListData()
	if IsEmptyTable(guild_list) then
		return nil
	end

	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	local item = nil
	if mainrolevo.guild_id > 0 then
		item = self.guild_list:GetItemAt(index)
		if item == nil or item:GetData() == nil then
			return nil
		end
	else
		item = self.no_guild_list:GetItemAt(index)
		if item == nil or item:GetData() == nil then
			return nil
		end
	end

	return item:GetView()
end

function GuildView:GetGuildItemCallback(index)
	return BindTool.Bind2(self.ClickItemByGuide, self, index)
end

function GuildView:ClickItemByGuide(index)
	local guild_id = GameVoManager.Instance:GetMainRoleVo().guild_id
	if guild_id > 0 then
		if self.guild_list then
			self.guild_list:SelectIndex(index)
		end
	else
		if self.no_guild_list then
			self.no_guild_list:SelectIndex(index)
		end
	end
end

--刷新右侧 仙盟图标信息
function GuildView:FlushCurrentGuildInfo()
	if not self.data then
		return
    end

	self.node_list.guild_leader_name.text.text = self.data.mengzhu_name
	local is_df, level = RoleWGData.Instance:GetDianFengLevel(self.data.mengzhu_level)
	self.node_list.flag_has_guild_dianfeng:CustomSetActive(is_df)
	self.node_list.has_guild_level_txt.text.text = level

	local has_applying = self.data.has_applying == 1
	self.node_list.flag_has_apply:SetActive(has_applying)
	self.node_list.btn_apply_guild:SetActive(not has_applying)

	if self.data.tuanzhang_uid and self.data.tuanzhang_uid > 0 then
		local role_id = self.data.tuanzhang_uid
		if role_id == RoleWGData.Instance:GetRoleInfo().role_id then
			local role_vo = RoleWGData.Instance:GetRoleVo()
			XUI.UpdateRoleHead(self.node_list.has_guild_head_pos_default, self.node_list.has_guild_head_pos, role_id, role_vo.sex, role_vo.prof, false, false, false)
		else
			BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function (protocol)
				XUI.UpdateRoleHead(self.node_list.has_guild_head_pos_default, self.node_list.has_guild_head_pos, role_id,  protocol.sex,  protocol.prof, false, false, false)
			end)
		end
	else
		-- 机器人
		local m_list = GuildDataConst.GUILD_MEMBER_LIST
		local member_list = m_list.list
		local member_num = m_list.count
		local leader_info = {}
		local role_vo = RoleWGData.Instance:GetRoleVo()
		local sex, prof = role_vo.sex, role_vo.prof

		if not IsEmptyTable(member_list) then
			for i = 1, member_num do
				local item = member_list[i]
				if item.post == 4 then
					sex, prof = item.sex, item.prof
					break
				end
			end
		end

		XUI.UpdateRoleHead(self.node_list.has_guild_head_pos_default, self.node_list.has_guild_head_pos, COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID, sex, prof, false, false, false)
	end

	-- if self.data.need_level == 0 or self.data.need_capability == 0 then
	-- 	self.node_list.lbl_no_guild:SetActive(true)
	-- 	self.node_list.layout_info_show:SetActive(false)	
	-- else
	-- 	self.node_list.lbl_no_guild:SetActive(false)
	-- 	self.node_list.layout_info_show:SetActive(true)	
	-- end

	-- self.node_list.lbl_limit_level.text.text = string.format(Language.Guild.LevelLimit, self.data.need_level) 
	-- self.node_list.lbl_limit_cap.text.text = string.format(Language.Guild.CapLimit, CommonDataManager.ConverExp(self.data.need_capability)) 
	-- --旗帜背景
	-- local qizi_index = GuildWGData.Instance:GetGuildQiZi(self.data.guild_level)
	-- self.node_list["xm_list_flag_bg"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("xm_daqizi"..qizi_index))
	-- --旗帜图标
	-- local flag_color = self.data.flag_color > 0 and self.data.flag_color or 1
	-- local flag_id = self.data.flag_id > 0 and self.data.flag_id or 1
	-- self.node_list["xm_list_flag"].image:LoadSprite(ResPath.GetGuildSystemImage("xm_flag".. flag_color .."_".. flag_id))
	-- --旗帜名字
	-- self.node_list["xm_list_flag_name"].text.text = self.data.flag_name
	-- self:FlushGuildListDatasource()
end

function GuildView:OnClickApplyGuild()
	if nil == self.data then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.SelectItem)
		return
	end

	if self.data.guild_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.FakeGuildLimitJoin)
		return
	end

	GuildWGCtrl.Instance:SendJoinGuildReq(self.data.guild_id)
end