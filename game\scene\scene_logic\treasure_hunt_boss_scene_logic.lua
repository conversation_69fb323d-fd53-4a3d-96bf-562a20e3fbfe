TreasureHuntBossSceneLogic = TreasureHuntBossSceneLogic or BaseClass(CommonFbLogic)

function TreasureHuntBossSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil

	self.create_obj_event = GlobalEventSystem:Bind(ObjectEventType.OBJ_CREATE, BindTool.Bind(self.OnObjCreate, self))
end

function TreasureHuntBossSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
	self.has_get_team_target = nil

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end
end

function TreasureHuntBossSceneLogic:Enter(old_scene_type, new_scene_type)
    CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    ViewManager.Instance:Close(GuideModuleName.TreasureHunt)

    local init_callback = function ()
    	GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,true)
   		MainuiWGCtrl.Instance:SetBtnLevel(true)
    	BaseFbLogic.SetLeaveFbTip(true)
    	MainuiWGCtrl.Instance:SetOtherContents(false)
    	WorldServerWGCtrl.Instance:OpenTreasureHurtBossInfoView()
    end
    MainuiWGCtrl.Instance:AddInitCallBack(nil,init_callback)

    BossWGCtrl.Instance:RefershBossStone()
    TreasureBossWGData.Instance:CheckShowTreasureBossTired()
    --self:CommonMoveCallBack()

    -- local here_callback = function ()
    -- 	self:FuHuoCallBack()
    -- end

    local common_callback = function ()
    	self:CommonMoveCallBack()
    end

	if not TaskWGCtrl.Instance:IsFly() then
		common_callback()
	else
		TaskWGCtrl.Instance:AddFlyUpList(common_callback)
	end

    FuhuoWGCtrl.Instance:SetFuhuoMustCallback(BindTool.Bind(self.FuHuoCallBack, self))
    MainuiWGCtrl.Instance:SetShowTimeTextState( false )
end

function TreasureHuntBossSceneLogic:FuHuoCallBack(use_type)
	local tarck_type, track_role_uuid = self:GetTrackRoleInfo()
	if tarck_type == OBJ_FOLLOW_TYPE.TEAM then
		return
	end
	
	use_type = use_type or FuHuoType.Common
	if use_type == FuHuoType.Common then
		self:CommonMoveCallBack()
	else
		self:HereFuHuoCallBack()
	end
end

function TreasureHuntBossSceneLogic:CommonMoveCallBack()
	local scene_id = Scene.Instance:GetSceneId()
    local cfg = TreasureBossWGData.Instance:GetHuntBossInfoByScene(scene_id)
	if IsEmptyTable(cfg) then
		return
	end

    local post_tab = Split(cfg.boss_pos,",")
    GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
    self:ResetRoleAndCameraDir()
    GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
    		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)
    MoveCache.SetEndType(MoveEndType.FightByMonsterId)
	MoveCache.param1 = cfg.boss_id or 0
	GuajiCache.monster_id = cfg.boss_id

	local range = BossWGData.Instance:GetMonsterRangeByid(cfg.boss_id)
	GuajiWGCtrl.Instance:MoveToPos(scene_id, tonumber(post_tab[1]), tonumber(post_tab[2]), range)
end

function TreasureHuntBossSceneLogic:HereFuHuoCallBack()
	local select_obj = nil
	if GuajiCache.target_obj then
		select_obj = GuajiCache.target_obj
	end

	if select_obj and not select_obj:IsDeleted() and Scene.Instance:IsEnemy(select_obj) then
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiCache.target_obj = select_obj
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		self:CommonMoveCallBack()
	end

end

function TreasureHuntBossSceneLogic:OnObjCreate(obj)
    local target_obj = MainuiWGData.Instance:GetTargetObj()
    if obj and not target_obj and not SceneObj.select_obj and self:IsEnemy(obj) and not GuajiCache.target_obj
    and obj:GetType() == SceneObjType.Monster then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
	end
end

function TreasureHuntBossSceneLogic:GuaiJiMonsterUpdate(now_time, elapse_time)
	self.guai_ji_next_move_time = Status.NowTime - 3

	if self:RolePickUpFallItem() then
		return true
	end

	local target_obj = MainuiWGData.Instance:GetTargetObj()
	if target_obj == nil then
		local main_role = Scene.Instance:GetMainRole()
		local x, y = main_role:GetLogicPos()
		local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
		target_obj = Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
		if target_obj ~= nil and self:IsRoleEnemy(target_obj, main_role) then
			MainuiWGCtrl.Instance:SetTargetObj(target_obj)
		end
	end
	local target_obj = MainuiWGData.Instance:GetTargetObj()
	local mosnter_type = nil
	if target_obj ~= nil and target_obj:GetType() == SceneObjType.Monster then
		mosnter_type = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[target_obj:GetVo().monster_id].type
	end
	target_obj = MainuiWGData.Instance:GetTargetObj()
	if (target_obj == nil  or (target_obj ~= nil and mosnter_type ~= MONSTER_TYPE.BOSS)) then
		BaseSceneLogic.GuaiJiMonsterUpdate(self, now_time, elapse_time)
		return
	end
end

-- 获取挂机打怪的位置
function TreasureHuntBossSceneLogic:GetGuajiPos()
	local target_x = nil
    local target_y = nil

    local scene_id = Scene.Instance:GetSceneId()
    local cfg = TreasureBossWGData.Instance:GetHuntBossInfoByScene(scene_id)
    if cfg ~= nil and cfg then
    	local t = Split(cfg.boss_pos, ",")
    	if t ~= nil and #t == 2 then
    		target_x = tonumber(t[1])
    		target_y = tonumber(t[2])
    	end
    end
	return target_x, target_y
end

-- 获取挂机打怪的位置
function TreasureHuntBossSceneLogic:GetGuiJiMonsterPos()
	-- MainuiWGCtrl.Instance:ResetLightBoss()
	-- CommonFbLogic.GetGuiJiMonsterPos(self)
	-- local target_distance = 20 * 20
	-- local target_x = nil
 --    local target_y = nil
	-- local x, y = Scene.Instance:GetMainRole():GetLogicPos()

	-- local obj_move_info_list = Scene.Instance:GetObjMoveInfoList()

	-- for k, v in pairs(obj_move_info_list) do
	-- 	local vo = v:GetVo()
	-- 	if vo.obj_type == SceneObjType.Monster and BaseSceneLogic.IsAttackMonster(vo.type_special_id) then
	-- 		local distance = GameMath.GetDistance(x, y, vo.pos_x, vo.pos_y, false)
	-- 		if distance < target_distance then
	-- 			target_x = vo.pos_x
 --                target_y = vo.pos_y
	-- 			target_distance = distance
	-- 		end
	-- 	end
	-- end
	local x,y = self:GetGuajiPos()
	return x,y
end

-- 是否是挂机打怪的敌人
function TreasureHuntBossSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

function TreasureHuntBossSceneLogic:Out()
    CommonFbLogic.Out(self)
    GlobalEventSystem:Fire(MainUIEventType.SHOW_TASK_TYPE,false)
    WorldServerWGCtrl.Instance:CloseTreasureHurtBossInfoView()
    local main_role = Scene.Instance:GetMainRole()
	main_role:SetAttr("tired_value_icon", 0)
	FuhuoWGCtrl.Instance:ClearFuhuoMustCallback()
	TreasureHuntWGData.Instance:ClearCacheSubName()
end

-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function TreasureHuntBossSceneLogic:GetGuajiCharacter()
	local is_need_stop = false

	local target_obj = self:GetMonster()
	if target_obj ~= nil then
		is_need_stop = true
		return target_obj, nil, is_need_stop
	end

	if target_obj == nil then
		target_obj, is_need_stop = self:GetNormalRole()
		return target_obj, nil, is_need_stop
	end
end

function TreasureHuntBossSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	local info = self:GetGuaJiInfo()
	local is_stop = info ~= nil

	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			if info ~= nil then
				if not v:IsDeleted() then
					local pos_x, pos_y = v:GetLogicPos()
					local dis = GameMath.GetDistance(info.x, info.y, pos_x, pos_y, false)
					if dis <= info.aoi_range * info.aoi_range then
						GuajiCache.target_obj = v
						return v, is_stop
					end
				end
			else
				GuajiCache.target_obj = v
				return v, is_stop
			end
		end
	end

	return nil, is_stop
end

function TreasureHuntBossSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()

		local info = self:GetGuaJiInfo()
		if info ~= nil then
			distance_limit = info.aoi_range * info.aoi_range
			x = info.x
			y = info.y
		end	

		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return obj
end

function TreasureHuntBossSceneLogic:GetNextSelectTargetId()
	-- 切换目标时，挂机模式需要变成自动
	if GuajiCache.guaji_type == GuajiType.Monster then
		GuajiWGCtrl.Instance:StopGuaji()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
	return BaseSceneLogic.GetNextSelectTargetId(self)
end

-- function TreasureHuntBossSceneLogic:AtkTeamLeaderTarget()
-- 	local team_leader_info = SocietyWGData.Instance:GetTeamLeader() or {}
-- 	local leader = Scene.Instance:GetRoleByRoleId(team_leader_info.role_id)
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

-- 	if not leader then
-- 		return
-- 	end

-- 	self.has_get_team_target = leader:GetVo() and leader:GetVo().obj_id or -1

-- 	if not leader:IsAtkPlaying() then
-- 		return
-- 	end

-- 	local target_obj
-- 	if leader:IsAtkPlaying() then
-- 		target_obj = leader:GetAttackTarget()
-- 		if not target_obj then
-- 			return
-- 		end
-- 	else
-- 		self.has_get_team_target = -1
-- 		return
-- 	end

-- 	if self:IsEnemy(target_obj) then
-- 		self.has_get_team_target = -1
-- 		GuajiWGCtrl.Instance:StopGuaji()
-- 		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, target_obj, SceneTargetSelectType.SELECT)
-- 	end
-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
-- end

function TreasureHuntBossSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	self:CheckGuaJiPosMove()
end

function TreasureHuntBossSceneLogic:CanGetMoveObj()
	return true
end