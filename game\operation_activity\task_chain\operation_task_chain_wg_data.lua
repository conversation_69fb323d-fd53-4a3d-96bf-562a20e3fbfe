OperationTaskChainWGData = OperationTaskChainWGData or BaseClass()

local WEEK_DAY = {[0] = 7, [1] = 1, [2] = 2, [3] = 3, [4] = 4, [5] = 5, [6] = 6}

function OperationTaskChainWGData:__init()
	if OperationTaskChainWGData.Instance then
		ErrorLog("[OperationTaskChainWGData] Attemp to create a singleton twice !")
	end
	OperationTaskChainWGData.Instance = self

	self:HotUpdateCommon()
	self:HotUpdateHuSong()
	self:HotUpdateCaiJiXunLuo()
	self:HotUpdateCaiJi()
	self:HotUpdateAvoid()
	self:HotUpdateYunSongAvoid()
	self:HotUpdateGround()
	self:HotUpdateBoss()

    OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN, {[1] = OPERATION_EVENT_TYPE.LEVEL, [2] = OPERATION_EVENT_TYPE.DAY},
    BindTool.Bind(self.CheckIsOpen, self), BindTool.Bind(self.GetRedPoint, self))

    self.show_think_mingwang = nil
    self.show_think_flag = false

    -- 采集成功等待时间
    self.gather_wait_time = nil
	-- 记录当前的阶段
	self.rember_cur_dayindex = -1
    RemindManager.Instance:Register(RemindName.OperationTaskChain, BindTool.Bind(self.GetRedPoint, self))
end

function OperationTaskChainWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.OperationTaskChain)
	OperationTaskChainWGData.Instance = nil
	self.show_think_mingwang = nil
	self.rember_cur_dayindex = nil
end

function OperationTaskChainWGData:GetTaskChainCfg(task_chain_id)
	if task_chain_id == nil then
		return
	end

	return self.task_chain_cfg[task_chain_id]
end

function OperationTaskChainWGData:GetTaskChainTaskCfg(task_id)
	if task_id == nil then
		return
	end

	return self.task_chain_task_cfg[task_id]
end

function OperationTaskChainWGData:GetGradeInfoCfg(grade, day_index)
	if grade == nil or day_index == nil then
		return
	end

	if self.task_chain_grade_cfg[grade] then
		return self.task_chain_grade_cfg[grade][day_index]
	end
end

function OperationTaskChainWGData:GetTaskReward(reward_index, task_index)
	if reward_index == nil or task_index == nil then
		return
	end

	if self.task_chain_task_reward_cfg[reward_index] then
		return self.task_chain_task_reward_cfg[reward_index][task_index]
	end
end

function OperationTaskChainWGData:GetTaskLevelReward(level_reward, tab_index, level)
	if level_reward == nil or level == nil or tab_index == nil then
		return
	end

	if self.task_chain_level_reward_cfg[level_reward] then
		if self.task_chain_level_reward_cfg[level_reward][tab_index] then
			return self.task_chain_level_reward_cfg[level_reward][tab_index][level]
		end
	end	
end

function OperationTaskChainWGData:GetMingWangRewardCfg(mingwang_reward)
	if mingwang_reward == nil then
		return
	end	

	return 	self.task_chain_mingwang_reward_cfg[mingwang_reward]
end

function OperationTaskChainWGData:GetDayIndexCfg(day_index)
	if day_index == nil then
		return
	end	

	local week = WEEK_DAY[day_index] or 1
	return 	self.task_chain_day_index_cfg[week]
end

function OperationTaskChainWGData:GetHuSongFbCfg(dungeon_id, scene_id)
	if dungeon_id == nil or scene_id == nil then
		return
	end

	if self.husong_fb_cfg[dungeon_id] then
		return self.husong_fb_cfg[dungeon_id][scene_id]
	end
end

function OperationTaskChainWGData:GetHuSongLevelCfg(dungeon_id, level)
	if dungeon_id == nil or level == nil then
		return
	end

	if self.husong_level_cfg[dungeon_id] then
		return self.husong_level_cfg[dungeon_id][level]
	end	
end

function OperationTaskChainWGData:GetCaiJiHuSongLevelCfg(dungeon_id, level, get_def)
	if dungeon_id == nil or level == nil then
		return
	end

	if self.xunluo_caiji_level_cfg[dungeon_id] then
		local data = self.xunluo_caiji_level_cfg[dungeon_id][level]
		if get_def and level < 0 and data == nil then
			local list = self.xunluo_caiji_level_cfg[dungeon_id]
			return list[#list]
		else
			return data
		end
	end	
end

function OperationTaskChainWGData:GetHuSongMonsterFreshCfg(dungeon_id, wave)
	if dungeon_id == nil or wave == nil then
		return
	end

	if self.husong_monsterfresh_cfg[dungeon_id] then
		return self.husong_monsterfresh_cfg[dungeon_id][wave]
	end
end

function OperationTaskChainWGData:GetHuSongBubbleCfg(index)
	if index == nil then
		return
	end

	if self.husong_bubble_cfg[index] then
		return self.husong_bubble_cfg[index]
	end
end

function OperationTaskChainWGData:GetXunLuoCaiJiDungeonCfg(dungeon_id)
	if dungeon_id == nil then
		return
	end

	return self.xunluo_caiji_dungeon_cfg[dungeon_id]
end 

function OperationTaskChainWGData:GetCaiJiDungeonCfg(dungeon_id, scene_id)
	if dungeon_id == nil or scene_id == nil then
		return
	end

	if self.caiji_dungeon_cfg[dungeon_id] then
		return self.caiji_dungeon_cfg[dungeon_id][scene_id]
	end
end

function OperationTaskChainWGData:GetCaiJiLevelCfg(dungeon_id, level, get_def)
	if dungeon_id == nil or level == nil then
		return
	end

	if self.caiji_level_cfg[dungeon_id] then
		local data = self.caiji_level_cfg[dungeon_id][level]
		if get_def and level < 0 and data == nil then
			local list = self.caiji_level_cfg[dungeon_id]
			return list[#list]
		else
			return data
		end
	end	
end

function OperationTaskChainWGData:GetSixteenCellDungeonCfg(dungeon_id, scene_id)
	if dungeon_id == nil or scene_id == nil then
		return
	end

	if self.sixteen_cell_dungeon_cfg[dungeon_id] then
		return self.sixteen_cell_dungeon_cfg[dungeon_id][scene_id]
	end
end

function OperationTaskChainWGData:GetSixteenCellLevelCfg(dungeon_id, level, get_def)
	if dungeon_id == nil or level == nil then
		return
	end

	if self.sixteen_cell_level_cfg[dungeon_id] then
		local data = self.sixteen_cell_level_cfg[dungeon_id][level]
		if get_def and level < 0 and data == nil then
			local list = self.sixteen_cell_level_cfg[dungeon_id]
			return list[#list]
		else
			return data
		end
	end	
end

function OperationTaskChainWGData:GetYunSongAvoidDungeonCfg(dungeon_id, scene_id)
	if dungeon_id == nil or scene_id == nil then
		return
	end

	if self.yun_song_avoid_dungeon_cfg[dungeon_id] then
		return self.yun_song_avoid_dungeon_cfg[dungeon_id][scene_id]
	end
end

function OperationTaskChainWGData:GetYunSongAvoidLevelCfg(dungeon_id, level, get_def)
	if dungeon_id == nil or level == nil then
		return
	end

	if self.yun_song_avoid_level_cfg[dungeon_id] then
		local data = self.yun_song_avoid_level_cfg[dungeon_id][level]
		if get_def and level < 0 and data == nil then
			local list = self.yun_song_avoid_level_cfg[dungeon_id]
			return list[#list]
		else
			return data
		end
	end	
end

function OperationTaskChainWGData:GetGuardDungeonCfg(dungeon_id, scene_id)
	if dungeon_id == nil or scene_id == nil then
		return
	end

	if self.guard_dungeon_cfg[dungeon_id] then
		return self.guard_dungeon_cfg[dungeon_id][scene_id]
	end
end

function OperationTaskChainWGData:GetGuardLevelCfg(dungeon_id, level, get_def)
	if dungeon_id == nil or level == nil then
		return
	end

	if self.guard_level_cfg[dungeon_id] then
		local data = self.guard_level_cfg[dungeon_id][level]
		if get_def and level < 0 and data == nil then
			local list = self.guard_level_cfg[dungeon_id]
			return list[#list]
		else
			return data
		end
	end	
end

function OperationTaskChainWGData:GetBossDungeonCfg(dungeon_id, scene_id)
	if dungeon_id == nil or scene_id == nil then
		return
	end

	if self.boss_dungeon_cfg[dungeon_id] then
		return self.boss_dungeon_cfg[dungeon_id][scene_id]
	end
end

function OperationTaskChainWGData:GetBossLevelCfg(dungeon_id, level, get_def)
	if dungeon_id == nil or level == nil then
		return
	end

	if self.boss_level_cfg[dungeon_id] then
		local data = self.boss_level_cfg[dungeon_id][level]
		if get_def and level < 0 and data == nil then
			local list = self.boss_level_cfg[dungeon_id]
			return list[#list]
		else
			return data
		end
	end	
end

function OperationTaskChainWGData:GetYunSongAvoidJiGuanCfg()
	return self.yun_song_avoid_jiguan_cfg
end

function OperationTaskChainWGData:GetServerCfg()
	return self.task_chain_server
end

function OperationTaskChainWGData:SetTaskChainInfo(protocol)
	self.task_chain_info = {
		task_flags = bit:d2b(protocol.task_flags),
		mw_reward_flags = bit:d2b(protocol.mw_reward_flags),
		mingwang = protocol.mingwang,
		grade = protocol.grade,
		day_index = protocol.day_index,
		have_done = protocol.have_done,
		week_day = protocol.week_day,
	}

	if self.rember_cur_dayindex ~= self.task_chain_info.day_index then
		self.rember_cur_dayindex = self.task_chain_info.day_index
		OperationActivityWGData.Instance:OnLoadingComplete(OPERATION_EVENT_TYPE.DAY)
	end
end

function OperationTaskChainWGData:SetTaskChainActInfo(protocol)
	self.task_chain_act_info = {
		task_chain_id = protocol.task_chain_id,
		taskchain_end_timestamp = protocol.taskchain_end_timestamp,
		task_idx = protocol.task_idx,
		task_flags = bit:d2b(protocol.task_flags),
		cur_task_status = protocol.cur_task_status,
		cur_task_status_end_timestamp = protocol.cur_task_status_end_timestamp,
		npc_objid = protocol.npc_objid,
		reason = protocol.reason,
		add_exp = protocol.add_exp,
	}
end

function OperationTaskChainWGData:ResetTaskChainActInfo()
	self.task_chain_act_info = nil
end

function OperationTaskChainWGData:SetTaskChainFbResult(protocol)
	self.task_chain_fb_result = {
		level = protocol.level,
		have_reward = protocol.have_reward,
		task_idx = protocol.task_idx,
		mingwang = protocol.mingwang,
		succ = protocol.succ,
		reward_array = protocol.reward_array,
		reward_exp = protocol.reward_exp,
	}
end

function OperationTaskChainWGData:SetTaskChainActStatus(protocol)
	self.task_chain_act_status = {
		status = protocol.status,
		next_time = protocol.next_status_switch_time,
	}
end

function OperationTaskChainWGData:SetHuSongInfo(protocol)
	self.husong_info = {
		level = protocol.level,
		task_status = protocol.task_status,
		progress = protocol.progress,
		objid = protocol.objid,
		score = protocol.score,
		wave = protocol.wave,
	}
end

function OperationTaskChainWGData:ResetHuSongInfo()
	self.husong_info = nil
end

function OperationTaskChainWGData:SetXunLuoCaiJiInfo(protocol)
	self.xunluo_caiji_info = {
		level = protocol.level,
		task_status = protocol.task_status,
		progress = protocol.progress,
		left_time = protocol.left_time,
		num = protocol.num,
		commit_self = protocol.commit_self,
		commit_all = protocol.commit_all
	}
end

function OperationTaskChainWGData:ResetXunLuoCaiJiInfo()
	self.xunluo_caiji_info = nil
end

function OperationTaskChainWGData:SetCaiJiInfo(protocol)
	self.caiji_info = {
		level = protocol.level,
		task_status = protocol.task_status,
		left_time = protocol.left_time,
		progress = protocol.progress,
		num = protocol.num,
		commit_self = protocol.commit_self,
		commit_all = protocol.commit_all,
	}
end

function OperationTaskChainWGData:SetCaiJiSpecialGatherstatus(protocol)
	self.have_special_gather = protocol.have_special_gather
end

function OperationTaskChainWGData:ResetCaiJiInfo()
	self.caiji_info = nil
	self.have_special_gather = nil
end

function OperationTaskChainWGData:SetSixteenCellInfo(protocol)
	self.sixteen_cell_info = {
		level = protocol.level,
		task_status = protocol.task_status,
		progress = protocol.progress,
		cur_status = protocol.cur_status,
		status_end_timestamp = protocol.status_end_timestamp,
		score = protocol.score,
	}

	self:InitSixteenCellList()
	self:CheckSixteenCellListStatus()
end

function OperationTaskChainWGData:SetSixteenCellRoundInfo(protocol)
	self.sixteen_cell_round_info = {
		round = protocol.round,
		status = protocol.status,
		status_end_timestamp = protocol.status_end_timestamp,
		jiguan_hor_flags = protocol.jiguan_hor_flags,
		jiguan_ver_flags = protocol.jiguan_ver_flags,
	}

	self:CheckSixteenCellListStatus()
end

function OperationTaskChainWGData:SetSixteenCellJiGuanInfo(protocol)
	self.sixteen_cell_jiguan_info = {
		-- succ = protocol.succ,
		-- add_score = protocol.add_score,
		jiguan_hor_flags = protocol.jiguan_hor_flags,
		jiguan_ver_flags = protocol.jiguan_ver_flags,
	}
end

function OperationTaskChainWGData:SetSixteenCellJiGuanInfo(protocol)
	self.sixteen_cell_result_info = {
		succ = protocol.succ,
		add_score = protocol.add_score,
	}
end

function OperationTaskChainWGData:ResetSixTeenCellInfo()
	self.sixteen_cell_info = nil
	self.sixteen_cell_round_info = nil
	self.sixteen_cell_jiguan_info = nil
	self.sixteen_cell_result_info = nil
end

function OperationTaskChainWGData:SetShowThinkData(value)
	self.show_think_mingwang = value
end

function OperationTaskChainWGData:SetYunSongAvoidInfo(protocol)
	self.yun_song_avoid_info = {
		level = protocol.level,
		task_status = protocol.task_status,
		progress = protocol.progress,
		num = protocol.num,
	}
end

function OperationTaskChainWGData:SetYunSongAvoidJiGuanInfo(protocol)
	self.yun_song_avoid_jiguan_info = {
		continue_time = protocol.continue_time,
		jiguan_flags = protocol.jiguan_flags,
		speed_status = protocol.speed_status,
		jiguan_status = protocol.jiguan_status,
	}
end

function OperationTaskChainWGData:ResetYunSongAvoidInfo()
	self.yun_song_avoid_info = nil
	self.yun_song_avoid_jiguan_info = nil
end

function OperationTaskChainWGData:SetGuardInfo(protocol)
	self.guard_info = {
		level = protocol.level,
		task_status = protocol.task_status,
		progress = protocol.progress,
		score = protocol.score,
		shouhu_objid = protocol.shouhu_objid,
	}
end

function OperationTaskChainWGData:SetGuardWaveInfo(protocol)
	self.guard_wave_info = {
		wave = protocol.wave,
		status = protocol.status,
		status_end_timestamp = protocol.status_end_timestamp,
	}
end

function OperationTaskChainWGData:ResetGuardInfo()
	self.guard_info = nil
	self.guard_wave_info = nil
end

function OperationTaskChainWGData:SetBossInfo(protocol)
	self.boss_info = {
		level = protocol.level,
		task_status = protocol.task_status,
		progress = protocol.progress,
		score = protocol.score,
	}
end

function OperationTaskChainWGData:ResetBossInfo()
	self.boss_info = nil
end

function OperationTaskChainWGData:GetTaskChainActStatusInfo()
	return self.task_chain_act_status
end

function OperationTaskChainWGData:GetTaskChainFbResult()
	return self.task_chain_fb_result
end

function OperationTaskChainWGData:GetTaskChainInfo()
	return self.task_chain_info
end

function OperationTaskChainWGData:GetTaskChainActInfo()
	return self.task_chain_act_info
end

function OperationTaskChainWGData:GetHuSongInfo()
	return self.husong_info
end

function OperationTaskChainWGData:GetXunLuoCaiJiInfo()
	return self.xunluo_caiji_info
end

function OperationTaskChainWGData:GetCaiJiInfo()
	return self.caiji_info
end

function OperationTaskChainWGData:GetCaiJiSpecialGatherstatus()
	return  self.have_special_gather
end

function OperationTaskChainWGData:GetSixteenCellInfo()
	return  self.sixteen_cell_info
end

function OperationTaskChainWGData:GetSixteenCellRoundInfo()
	return  self.sixteen_cell_round_info
end

function OperationTaskChainWGData:GetSixteenCellJiGuanInfo()
	return  self.sixteen_cell_jiguan_info
end

function OperationTaskChainWGData:GetShowThinkData()
	return self.show_think_mingwang
end

function OperationTaskChainWGData:GetYunSongAvoidInfo()
	return self.yun_song_avoid_info
end

function OperationTaskChainWGData:GetYunSongAvoidJiGuanInfo()
	return self.yun_song_avoid_jiguan_info
end

function OperationTaskChainWGData:GetGuardInfo()
	return self.guard_info
end

function OperationTaskChainWGData:GetGuardWaveInfo()
	return self.guard_wave_info
end

function OperationTaskChainWGData:GetBossInfo()
	return self.boss_info
end

function OperationTaskChainWGData:GetInfoBySceneType(scene_type)
	if scene_type == nil then
		return
	end

	if scene_type == SceneType.CROSS_TASK_CHAIN_HUSONG then
		return self:GetHuSongInfo()
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
		return self:GetXunLuoCaiJiInfo()
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
		return self:GetCaiJiInfo()
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_AVOID then
		return self:GetSixteenCellInfo()
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID then
		return self:GetYunSongAvoidInfo()
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_GUARD then
		return self:GetGuardInfo()
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_BOSS then
		return self:GetBossInfo()
	end
end

function OperationTaskChainWGData:GetIsTaskChainTaskScene(scene_type)
	local is_scene = false
	local scene_type = scene_type or Scene.Instance:GetSceneType()
	if scene_type then
		if scene_type == SceneType.CROSS_TASK_CHAIN_HUSONG or
		   scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI or
		   scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI or
		   scene_type == SceneType.CROSS_TASK_CHAIN_AVOID or
		   scene_type == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID or
		   scene_type == SceneType.CROSS_TASK_CHAIN_GUARD or
		   scene_type == SceneType.CROSS_TASK_CHAIN_BOSS then
		   is_scene = true
		end
	end

	return is_scene
end

function OperationTaskChainWGData:IsTaskChainScene()
	local scene_type = Scene.Instance:GetSceneType()
	local is_scene = false
	if scene_type == SceneType.CROSS_TASK_CHAIN_HUSONG or
	   scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI or
	   scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI or
	   scene_type == SceneType.CROSS_TASK_CHAIN_AVOID or
	   scene_type == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID or
	   scene_type == SceneType.CROSS_TASK_CHAIN_GUARD or
	   scene_type == SceneType.CROSS_TASK_CHAIN or
	   scene_type == SceneType.CROSS_TASK_CHAIN_BOSS then
	   is_scene = true
	end

	return is_scene
end

function OperationTaskChainWGData:CheckIsOpen()
	local is_open = false
	local level = RoleWGData.Instance:GetRoleLevel()
	local cfg = self:GetOpenServerDayCfg()
	local open_level = nil
	if cfg ~= nil then
		is_open = level >= cfg.lv_limit
		open_level = cfg.lv_limit
	end

	local activity_open_day = OperationActivityWGData.Instance:GetActOpenDay(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
	local grade = nil
	if self.task_chain_info and self.task_chain_info.grade then
		grade = self.task_chain_info.grade
	end

	if nil == grade then
		return false, open_level
	end

	local task_grade_list = self:GetTaskGradeListCfg(grade)

	if nil == task_grade_list then
		return false, open_level
	end

	table.sort(task_grade_list, SortTools.KeyLowerSorter("day_index"))
	local cfg = nil
	for k,v in pairs(task_grade_list) do
		if v.day_index == activity_open_day then
			cfg = v
			break
		end
	end

	if activity_open_day ~= nil and cfg == nil then
		is_open = false
	end

	return is_open, open_level
end

-- 获取奇遇任务开启时间(一天开两场)
function OperationTaskChainWGData:GetTodayOpenTime()
	local day_index_cfg = self:GetCurDayIndexCfg()
	if day_index_cfg and day_index_cfg.task_chain_start then
		local open_tab = Split(day_index_cfg.task_chain_start, "|")
		if not IsEmptyTable(open_tab) then
			local ser_time = TimeWGCtrl.Instance:GetServerTime()
			local zero_time = TimeWGCtrl.Instance:NowDayTimeStart(ser_time)
			for i = 1, #open_tab do
				local hour = tonumber(string.sub(open_tab[i], 1, 2)) or 0
				local min = tonumber(string.sub(open_tab[i], 3, 4)) or 0
				local open_time = zero_time + hour * 3600 + min * 60
				if ser_time <= open_time then
					return open_time, hour, min
				end
			end
		end
	end
	return 0, 0, 0
end

function OperationTaskChainWGData:GetRedPoint()
	local is_show = 0

	if not self:CheckIsOpen() then
		return is_show
	end

	if self:GetIsCanEnter() or self:GetIsHasBoxReward() then
		is_show = 1
	end

	return is_show
end

function OperationTaskChainWGData:GetIsCanEnter()
	if self:GetTaskChainIsDone() then
		return false
	end
	
	local act_info = self:GetTaskChainActStatusInfo()
	if act_info == nil or (act_info ~= nil and act_info.status == ACTIVITY_STATUS.CLOSE) then
		return false
	end

	return true
end

function OperationTaskChainWGData:GetTaskChainIsDone()
	local is_done = false
	if self.task_chain_info ~= nil then
		is_done = self.task_chain_info.have_done == 1
	end

	return is_done
end

function OperationTaskChainWGData:SetShowThinkFlag(value)
	self.show_think_flag = value
end

function OperationTaskChainWGData:GetShowThinkFlag()
	return self.show_think_flag
end

function OperationTaskChainWGData:GetMingWangValue()
	if self.task_chain_info ~= nil and self.task_chain_info.mingwang ~= nil then
		return self.task_chain_info.mingwang
	end

	return 0
end

function OperationTaskChainWGData:GetShowRewardList()
	local data_list = {}

	if self.task_chain_info == nil then
		return data_list
	end

	local grade_cfg = self:GetGradeInfoCfg(self.task_chain_info.grade, self.task_chain_info.day_index)
	if grade_cfg ~= nil then
		local item_id_tab = {}
		local task_reward_cfg = self:GetTaskReward(grade_cfg.task_reward, 1)
		if task_reward_cfg ~= nil then
			for k,v in pairs(task_reward_cfg.win_reward_item) do
				if item_id_tab[v.item_id] == nil then
					local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
					local sort_value = #data_list + 1
					if item_cfg ~= nil then
						sort_value = item_cfg.color * 1000 + sort_value
					end

					local data = {item_id = v.item_id, num = v.num, is_bind = v.is_bind, sort_value = sort_value}
					table.insert(data_list, data)
					item_id_tab[v.item_id] = v.item_id
				end
			end
		end

		local mingwang_cfg = self:GetMingWangRewardCfg(grade_cfg.mingwang_reward)
		if mingwang_cfg ~= nil then
			for k,v in pairs(mingwang_cfg) do
				for k1, v1 in pairs(v.reward_item) do
					if item_id_tab[v1.item_id] == nil then
						local item_cfg = ItemWGData.Instance:GetItemConfig(v1.item_id)
						local sort_value = #data_list + 1
						if item_cfg ~= nil then
							sort_value = item_cfg.color * 1000 + sort_value
						end

						local data = {item_id = v1.item_id, num = v1.num, is_bind = v1.is_bind, sort_value = sort_value}
						table.insert(data_list, data)
						item_id_tab[v1.item_id] = v1.item_id
					end
				end
			end
		end

		if #data_list > 1 then
			table.sort(data_list, SortTools.KeyUpperSorter("sort_value"))
		end
	end	

	return data_list
end

function OperationTaskChainWGData:GetNextMingWangReward(value)
	local next_value = nil
	local is_max = false
	local interval = nil
	if value == nil then
		return next_value, is_max, interval
	end

	local base_info = self:GetTaskChainInfo()
	if base_info == nil then
		return next_value, is_max, interval
	end

	local grade_cfg = self:GetGradeInfoCfg(base_info.grade, base_info.day_index)
	if grade_cfg ~= nil then
		local mingwang_cfg = self:GetMingWangRewardCfg(grade_cfg.mingwang_reward)
		if mingwang_cfg ~= nil then
			is_max = true
			local cur = 0
			for k,v in ipairs(mingwang_cfg) do
				if value < v.mingwang then
					next_value = v.mingwang
					is_max = false
					break
				else
					cur = v.mingwang
				end
			end

			if next_value ~= nil then
				interval = next_value - cur
			end
		end
	end

	return next_value, is_max, interval
end

function OperationTaskChainWGData:GetShowTaskList()
	local data_list = {}
	if self.task_chain_info == nil then
		return data_list
	end

	local day_index_cfg = self:GetDayIndexCfg(self.task_chain_info.week_day)
	if day_index_cfg ~= nil then
		local task_chain_cfg = self:GetTaskChainCfg(day_index_cfg.task_chain_id)
		if task_chain_cfg ~= nil then
			local task_tab = Split(task_chain_cfg.task_ids, "|")
			if #task_tab > 0 then
				for k,v in pairs(task_tab) do
					if v ~= nil and v ~= "" then
						local task_cfg = self:GetTaskChainTaskCfg(tonumber(v))
						if task_cfg ~= nil then
							local data = {task_cfg = task_cfg, task_flag = self.task_chain_info.task_flags[33 - k], is_last = k == #task_tab}
							table.insert(data_list, data)
						end
					end
				end
			end
		end
	end

	return data_list
end

function OperationTaskChainWGData:GetShowTaskListByActFb()
	local data_list = {}
	if self.task_chain_act_info == nil then
		return data_list
	end

	local task_chain_cfg = self:GetTaskChainCfg(self.task_chain_act_info.task_chain_id)
	if task_chain_cfg ~= nil then
		local task_tab = Split(task_chain_cfg.task_ids, "|")
		if #task_tab > 0 then
			for k,v in pairs(task_tab) do
				if v ~= nil and v ~= "" then
					local task_cfg = self:GetTaskChainTaskCfg(tonumber(v))
					if task_cfg ~= nil then
						local data = {task_cfg = task_cfg, task_flag = self.task_chain_info.task_flags[33 - k], is_last = k == #task_tab}
						table.insert(data_list, data)
					end
				end
			end
		end
	end

	return data_list
end

function OperationTaskChainWGData:GetIsHasBoxReward()
	local is_has = false
	if self.task_chain_info == nil then
		return is_has
	end

	local reward_list = nil
	local grade_cfg = self:GetGradeInfoCfg(self.task_chain_info.grade, self.task_chain_info.day_index)
	if grade_cfg ~= nil then
		reward_list = self:GetMingWangRewardCfg(grade_cfg.mingwang_reward)
	end

	if reward_list == nil then
		return is_has
	end

	for k,v in pairs(reward_list) do
		if self.task_chain_info.mingwang >= v.mingwang and self.task_chain_info.mw_reward_flags[33 - k] == 0 then
			is_has = true
			break
		end
	end

	return is_has
end

function OperationTaskChainWGData:GetBoxDataInfo()
	local data_list = nil
	local value = 0
	if self.task_chain_info == nil then
		return data_list, value
	end

	local reward_list = nil
	local grade_cfg = self:GetGradeInfoCfg(self.task_chain_info.grade, self.task_chain_info.day_index)
	if grade_cfg ~= nil then
		reward_list = self:GetMingWangRewardCfg(grade_cfg.mingwang_reward)
	end

	if reward_list == nil then
		return data_list, value
	end

	data_list = {}
	local num = #reward_list - GameEnum.TASK_CHAIN_MAX_SHOW_BOX_NUM
	local cur_index
	local open_index = num > 0 and num or 0
	if num > 0 then
		for  i = 1, num do
			if self.task_chain_info.mw_reward_flags[33 - i] == 0 then
				if cur_index == nil then
					cur_index = i
				end			
			end

			if cur_index ~= nil and i >= cur_index and reward_list[i] ~= nil then
				local box_info = {cfg = reward_list[i], reward_flag = self.task_chain_info.mw_reward_flags[33 - i], index = i}
				table.insert(data_list, box_info)
				open_index = i
			end
		end
	end

	open_index = open_index + 1
	for i = 1, GameEnum.TASK_CHAIN_MAX_SHOW_BOX_NUM - #data_list do
		if reward_list[open_index] ~= nil then
			local box_info = {cfg = reward_list[open_index], reward_flag = self.task_chain_info.mw_reward_flags[33 - open_index], index = open_index}
			table.insert(data_list, box_info)
			open_index = open_index + 1
		else
			break
		end
	end

	if #data_list > 0 then
		value = self.task_chain_info.mingwang / (data_list[#data_list].cfg.mingwang or 1)
	end

	return data_list, value
end

function OperationTaskChainWGData:GetShowMingWangRewardList(is_preview)
	local data_list = nil

	if self.task_chain_info == nil then
		return data_list
	end

	local grade_cfg = self:GetGradeInfoCfg(self.task_chain_info.grade, self.task_chain_info.day_index)
	if grade_cfg ~= nil then
		data_list = {}
		local mingwang_value = self.task_chain_info.mingwang
		local mingwang_cfg = self:GetMingWangRewardCfg(grade_cfg.mingwang_reward)
		if mingwang_cfg ~= nil then
			for k,v in pairs(mingwang_cfg) do
				local sort_value = k
				local sort_weight = self.task_chain_info.mw_reward_flags[33 - k] == 0 and 100 or 1

				if not is_preview and self.task_chain_info.mw_reward_flags[33 - k] == 1 then
					sort_weight = 1000 
				end

				sort_value = sort_value * sort_weight
				local reward_flag = self.task_chain_info.mw_reward_flags[33 - k]
				local data = {is_preview = is_preview, cfg = v, sort_value = sort_value, reward_flag = reward_flag, is_can = mingwang_value >= v.mingwang and reward_flag == 0, index = k}
				table.insert(data_list, data)
			end
		end

		if #data_list > 1 then
			table.sort(data_list, SortTools.KeyLowerSorter("sort_value"))
		end
	end

	return data_list
end

function OperationTaskChainWGData:GetCurGradeCfg()
	local data = nil
	if self.task_chain_info == nil then
		return data
	end	

	data = self:GetGradeInfoCfg(self.task_chain_info.grade, self.task_chain_info.day_index)
	return data
end

function OperationTaskChainWGData:GetCurDayIndexCfg()
	local data = nil
	if self.task_chain_info == nil then
		return data
	end	

	data = self:GetDayIndexCfg(self.task_chain_info.week_day)
	return data
end

function OperationTaskChainWGData:GetCurTaskRewardByIndex(task_index, is_succ)
	local data_list = nil

	if self.task_chain_info == nil then
		return data_list
	end

	if task_index == nil then
		return data_list
	end

	local grade_cfg = self:GetGradeInfoCfg(self.task_chain_info.grade, self.task_chain_info.day_index)
	if grade_cfg ~= nil then
		local reward_data = self:GetTaskReward(grade_cfg.task_reward, task_index)
		if reward_data ~= nil then
			data_list = {}
			if is_succ == nil or is_succ then
				for k,v in pairs(reward_data.win_reward_item) do
					table.insert(data_list, v)
				end
			elseif is_succ == false then
				for k,v in pairs(reward_data.lose_reward_item) do
					table.insert(data_list, v)
				end			
			end
		end
	end

	return data_list
end

-- 任务链玩法是否开启
function OperationTaskChainWGData:GetTaskChainIsOpen()
	local is_open = false
	local act_info = self:GetTaskChainActStatusInfo()
	local is_done = self:GetTaskChainIsDone()
	if act_info ~= nil then
		if act_info.status ~= ACTIVITY_STATUS.CLOSE and not is_done then
	    	is_open = true			
		end
	end
	
	return is_open
end

function OperationTaskChainWGData:IsCanJoin()
	return true
end

function OperationTaskChainWGData:GetScheduleTaskInfo()
	local data_list = {}
	if self.task_chain_act_info == nil then
		return data_list
	end

	local task_chain_cfg = self:GetTaskChainCfg(self.task_chain_act_info.task_chain_id)
	if task_chain_cfg == nil then
		return data_list
	end

	local follow_id = self.task_chain_act_info.npc_objid
	local move_pos

	local task_tab = Split(task_chain_cfg.task_ids, "|")
	if task_tab ~= nil and next(task_tab) ~= nil and task_tab[self.task_chain_act_info.task_idx + 1] ~= nil then
		local task_cfg = self:GetTaskChainTaskCfg(tonumber(task_tab[self.task_chain_act_info.task_idx + 1]))
		if task_cfg ~= nil then
		    local end_tab = Split(task_cfg.npc_end_pos, "|")
		    if end_tab ~= nil and next(end_tab) ~= nil then
		        move_pos = {
		            x = tonumber(end_tab[1]),
		            y = tonumber(end_tab[2])
		        }
		    end
		end
	end
	--[[
	local ready_data = {
		status = self.task_chain_act_info.task_idx == -1 and OPERATION_TASK_CHAIN_TASK_STATUS.STANDY or OPERATION_TASK_CHAIN_TASK_STATUS.CLOSE,
		name = task_chain_cfg.ready_name,
		iocn_index = 0,
		is_join = false,
		is_first = true,
		cfg = nil,
		follow_id = follow_id,
		move_pos = move_pos,
		is_cur = self.task_chain_act_info.task_idx == -1,
	}

	table.insert(data_list, ready_data)
	--]]
	local task_list = Split(task_chain_cfg.task_ids, "|")
	if task_list ~= nil and next(task_list) > 0 then
		for k,v in ipairs(task_list) do
			local status = OPERATION_TASK_CHAIN_TASK_STATUS.CLOSE
			local name = ""
			local is_join = self.task_chain_act_info.task_flags[33 - k] == 1
			local is_first = false
			local cfg = nil

			if self.task_chain_act_info.task_idx == -1 then
				status = OPERATION_TASK_CHAIN_TASK_STATUS.NO_START
			else
				if k - 1 == self.task_chain_act_info.task_idx then
					if self.task_chain_act_info.cur_task_status == OPERATION_TASK_CHAIN_ACT_STATUS.STANDY then
						status = OPERATION_TASK_CHAIN_TASK_STATUS.FOLLOW
					else
						status = OPERATION_TASK_CHAIN_TASK_STATUS.OPEN
					end
				elseif k - 1 > self.task_chain_act_info.task_idx then
					status = OPERATION_TASK_CHAIN_TASK_STATUS.NO_START
				end
			end

			cfg = self:GetTaskChainTaskCfg(tonumber(v))
			if cfg ~= nil then
				name = cfg.task_name
			end

			local data = {
				status = status,
				name = name,
				is_join = is_join,
				is_first = is_first,
				cfg = cfg,
				follow_id = follow_id,
				move_pos = move_pos,
				is_cur = (k - 1) == self.task_chain_act_info.task_idx,
			}

			table.insert(data_list, data)
		end
	end

	return data_list
end

function OperationTaskChainWGData:GetBlockPosList(scene_id)
	local cfg = self.task_chain_scene_block_cfg[scene_id]
	if cfg ~= nil then
		if cfg.airwall_type == 1 then
			return self:GetRectangleBlock(cfg)
		elseif cfg.airwall_type == 2 then
			return self:GetCircularBlock(cfg)
		end
	end
end

function OperationTaskChainWGData:GetRectangleBlock(cfg)
	local block_list = {}
	local angle = 0--cfg.airwall_angle
	local middle_pos = Split(cfg.airwall_pos, "|")
	local x_middle = math.floor(cfg.airwall_width * 0.5)
	local y_middle = math.floor(cfg.airwall_length * 0.5)
	local middle_x = tonumber(middle_pos[1])
	local middle_y = tonumber(middle_pos[2])

	local len = cfg.airwall_width + cfg.airwall_length
	for i = 0, len do
		local x = middle_x
		local y = middle_y
		if i <= cfg.airwall_width then
			if i <= x_middle then
				x = middle_x - i
			elseif i > x_middle then
				x = middle_x + i - x_middle
			end

			y = middle_y + y_middle
			table.insert(block_list, {x = x, y = y})

			y = middle_y - y_middle
			table.insert(block_list, {x = x, y = y})
		else
			local j = len - i
			if j <= y_middle then
				y = middle_y - j
			elseif j > y_middle then
				y = middle_y + j - y_middle
			end

			x = middle_x + x_middle
			table.insert(block_list, {x = x, y = y})

			x = middle_x - x_middle
			table.insert(block_list, {x = x, y = y})		
		end
	end

	local pos_list = {}
	pos_list[1] = {x = middle_x, y = middle_y + y_middle}
	pos_list[2] = {x = middle_x, y = middle_y - y_middle}
	pos_list[3] = {x = middle_x + x_middle, y = middle_y}
	pos_list[4] = {x = middle_x - x_middle, y = middle_y}

	--[[
	local angle_pos = u3d.v2ForAngle(angle * Mathf.PI / 180)
	local function rotation_func(pos_x, pos_y)
		local temp_pos = {x = 0, y = 0}
		temp_pos.x = (pos_x - middle_x) * angle_pos.x - (pos_y - middle_y) * angle_pos.y + middle_x
		temp_pos.y = (pos_y - middle_y) * angle_pos.x + (pos_x - middle_x) * angle_pos.y + middle_y
		return temp_pos
	end
	for i=1,#pos_list do
		pos_list[i] = rotation_func(pos_list[i].x, pos_list[i].y)
	end
	for i=1,#block_list do
		block_list[i] = rotation_func(block_list[i].x, block_list[i].y)
	end
	--]]

	local eff_list = {}
	eff_list[1] = {x = pos_list[1].x, y = pos_list[1].y, effect_y = 0, rotation_y = 0 + angle, scale_x = 1, scale_y = 1, effect_name = cfg.airwall_effect}
	eff_list[2] = {x = pos_list[2].x, y = pos_list[2].y, effect_y = 0, rotation_y = 0 + angle, scale_x = 1, scale_y = 1, effect_name = cfg.airwall_effect}
	eff_list[3] = {x = pos_list[3].x, y = pos_list[3].y, effect_y = 0, rotation_y = 90 + angle, scale_x = 1, scale_y = 1, effect_name = cfg.airwall_effect}
	eff_list[4] = {x = pos_list[4].x, y = pos_list[4].y, effect_y = 0, rotation_y = 90 + angle, scale_x = 1, scale_y = 1, effect_name = cfg.airwall_effect}

	return block_list, eff_list
end

function OperationTaskChainWGData:GetCircularBlock(cfg)
	local eff_list = {}
	local middle_pos = Split(cfg.airwall_pos, "|")
	local middle_x = tonumber(middle_pos[1])
	local middle_y = tonumber(middle_pos[2])
	local radius = cfg.airwall_radius

	local math_cos = math.cos
	local math_sin = math.sin
	local const = 3.14 / 180
	local point_list = {}
	for i=0,360 do
		local x = middle_x + radius * math_cos(i * const)
		local y = middle_y + radius * math_sin(i * const)
		point_list[i + 1] = {x = x, y = y}
	end

	if cfg.airwall_effect and cfg.airwall_effect ~= "" then
		eff_list[1] = {x = middle_x, y = middle_y, effect_y = 0, rotation_y = 0, scale_x = 1, scale_y = 1, effect_name = cfg.airwall_effect}
	end

	return point_list, eff_list
end

function OperationTaskChainWGData:GetShowFbInfo()
	if self.task_chain_act_info == nil then
		return
	end

	if self.task_chain_info == nil then
		return
	end

	local str_tab = Language.OpertionAcitvity.TaskChain
	local info = self.task_chain_act_info
	local data = {
		title = "",
		content = "",
		story_content = "",
		count_down_time = info.cur_task_status_end_timestamp - TimeWGCtrl.Instance:GetServerTime(),
		continue_time = 0,
		data_list = {},
		is_standy = info.task_idx == -1,
		pro_str = "",
		desc_1 = "",
		desc_2 = str_tab.IsMax,
		score_slider_value = 1,
		pro_value = 0,
		score_level = 0,
		is_join = false,
		time_str = "",
		level = 0,
		is_max = true,
		cur_level_mingwang_value = 0,
		task_idx = info.task_idx,
		state = info.cur_task_status,
	}

	local scene_type = Scene.Instance:GetSceneType()
	local task_chain_cfg = self:GetTaskChainCfg(info.task_chain_id)
	local cur_task_cfg
	local is_no_show_time = false
	
	if info.task_idx ~= -1 then
		if task_chain_cfg ~= nil then
			local task_tab = Split(task_chain_cfg.task_ids, "|")
			if task_tab ~= nil and #task_tab > 0 then
				local cur_task_id = task_tab[info.task_idx + 1]

				if scene_type == SceneType.CROSS_TASK_CHAIN then
					if cur_task_id ~= nil and cur_task_id ~= "" then
						cur_task_cfg = self:GetTaskChainTaskCfg(tonumber(cur_task_id))
						if cur_task_cfg ~= nil then
							data.title = string.format(str_tab.TaskChainTitleNext, info.task_idx + 1, cur_task_cfg.task_name)
							data.content = cur_task_cfg.task_desc
							data.story_content = cur_task_cfg.story_content
							data.continue_time = cur_task_cfg.continue_time
						end
					end
				else
					for k,v in pairs(task_tab) do
						local task_id = tonumber(v)
						local check_cfg = self:GetTaskChainTaskCfg(task_id)
						if check_cfg ~= nil and check_cfg.scene_type == scene_type then
							cur_task_cfg = check_cfg
							data.title = string.format(str_tab.TaskChainTitleNext, k, cur_task_cfg.task_name)
							data.content = cur_task_cfg.task_desc
							data.story_content = cur_task_cfg.story_content
							data.continue_time = cur_task_cfg.continue_time
							-- 服务端该任务结束，但是玩家还在房间
							if tonumber(cur_task_id) ~= task_id then
								data.count_down_time = 0
								is_no_show_time = true
							end
							break					
						end
					end
				end
			end
		end

		data.is_join = info.task_flags[33 - info.task_idx] == 1
		if not data.is_join then
			data.data_list= self:GetCurTaskRewardByIndex(info.task_idx + 1)
		end
	end

	if scene_type == SceneType.CROSS_TASK_CHAIN then
		if info.task_idx == -1 then
			local task_chain_cfg = self:GetTaskChainCfg(info.task_chain_id)
			if task_chain_cfg ~= nil then
				data.title = string.format(str_tab.TaskChainTitle, task_chain_cfg.task_chain_name)
				data.content = task_chain_cfg.desc
				data.story_content = task_chain_cfg.story_content
				data.time_str = task_chain_cfg.task_chain_name .. str_tab.TimerStr2
			end

			data.data_list = self:GetShowRewardList()
		else
			if info.cur_task_status == OPERATION_TASK_CHAIN_ACT_STATUS.OPEN then
				data.time_str = str_tab.TimerStr2
			else
				data.time_str = str_tab.TimerStr1
			end
		end

		data.is_standy = true
	else
		data.is_standy = false
		data.time_str = str_tab.TimerStr2
		local show_info = nil

		if scene_type == SceneType.CROSS_TASK_CHAIN_HUSONG then
			local husong_info = self:GetHuSongInfo()
			if husong_info ~= nil then
				data.pro_value = husong_info.progress * 0.01

				if cur_task_cfg ~= nil then
					data.desc_1 = cur_task_cfg.level_desc .. "：" .. ToColorStr(husong_info.score, COLOR3B.D_GREEN)
					data.pro_str = cur_task_cfg.progress_desc .. "：" ..husong_info.progress .. "%"
					data.level = husong_info.level

					local cur_level_cfg = self:GetHuSongLevelCfg(cur_task_cfg.dungeon_id, husong_info.level)
					if cur_level_cfg ~= nil then
						data.cur_level_mingwang_value = cur_level_cfg.score or 0
					end

					local level_cfg = self:GetHuSongLevelCfg(cur_task_cfg.dungeon_id, husong_info.level - 1)
					if level_cfg ~= nil then
						data.is_max = false
						data.desc_2 = string.format(str_tab.NextScoreLevel, level_cfg.score - husong_info.score)
						data.score_slider_value = (husong_info.score - data.cur_level_mingwang_value) / (level_cfg.score - data.cur_level_mingwang_value)
					end
				end

				show_info = husong_info
			end
		elseif scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
			local caiji_husong_info = self:GetXunLuoCaiJiInfo()
			if caiji_husong_info ~= nil then
				data.pro_value = caiji_husong_info.progress * 0.01

				if cur_task_cfg ~= nil then
					data.desc_1 = cur_task_cfg.level_desc .. "：" ..ToColorStr(caiji_husong_info.commit_self, COLOR3B.D_GREEN)
					data.pro_str = cur_task_cfg.progress_desc .. "：" ..caiji_husong_info.progress .. "%"
					data.level = caiji_husong_info.level

					local cur_level_cfg = self:GetCaiJiHuSongLevelCfg(cur_task_cfg.dungeon_id, caiji_husong_info.level, true)
					if cur_level_cfg ~= nil then
						data.cur_level_mingwang_value = cur_level_cfg.num or 0
					end

					local level_cfg = self:GetCaiJiHuSongLevelCfg(cur_task_cfg.dungeon_id, caiji_husong_info.level - 1, true)
					if level_cfg ~= nil then
						data.is_max = false
						data.desc_2 = string.format(str_tab.NextScoreLevel, level_cfg.num - caiji_husong_info.commit_self)
						data.score_slider_value = (caiji_husong_info.commit_self - data.cur_level_mingwang_value) / (level_cfg.num - data.cur_level_mingwang_value)
					end			

					if not is_no_show_time then
						data.count_down_time = caiji_husong_info.left_time
						if caiji_husong_info.task_status == OPERATION_TASK_CHAIN_ACT_STATUS.STANDY then
							data.count_down_time = math.floor(caiji_husong_info.left_time - cur_task_cfg.continue_time)
						end
					end
				end

				show_info = caiji_husong_info
			end	

			data.time_str = str_tab.SubmissionTimerStr

		elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
			local caiji_info = self:GetCaiJiInfo()
			if caiji_info ~= nil then
				data.pro_value = caiji_info.progress * 0.01

				if cur_task_cfg ~= nil then
					data.desc_1 = cur_task_cfg.level_desc .. "：" .. ToColorStr(caiji_info.commit_self, COLOR3B.D_GREEN)
					data.pro_str = cur_task_cfg.progress_desc .. "：" ..caiji_info.progress .. "%"
					data.level = caiji_info.level
					
					local cur_level_cfg = self:GetCaiJiLevelCfg(cur_task_cfg.dungeon_id, caiji_info.level, true)
					if cur_level_cfg ~= nil then
						data.cur_level_mingwang_value = cur_level_cfg.num or 0
					end

					local level_cfg = self:GetCaiJiLevelCfg(cur_task_cfg.dungeon_id, caiji_info.level - 1, true)
					if level_cfg ~= nil then
						data.is_max = false
						data.desc_2 = string.format(str_tab.NextScoreLevel, level_cfg.num - caiji_info.commit_self)
						data.score_slider_value = (caiji_info.commit_self - data.cur_level_mingwang_value) / (level_cfg.num - data.cur_level_mingwang_value)
					end

					if not is_no_show_time then
						data.count_down_time = caiji_info.left_time
						if caiji_info.task_status == OPERATION_TASK_CHAIN_ACT_STATUS.STANDY then
							data.count_down_time = math.floor(caiji_info.left_time - cur_task_cfg.continue_time)
						end
					end
				end

				show_info = caiji_info
			end

			data.time_str = str_tab.SubmissionTimerStr
		elseif scene_type == SceneType.CROSS_TASK_CHAIN_AVOID then
			local cell_info = self:GetSixteenCellInfo()
			if cell_info ~= nil then
				data.pro_value = cell_info.progress * 0.01

				if cur_task_cfg ~= nil then
					data.desc_1 = cur_task_cfg.level_desc .. "：" .. ToColorStr(cell_info.score, COLOR3B.D_GREEN)
					data.pro_str = cur_task_cfg.progress_desc .. "：" ..cell_info.progress .. "%"
					data.level = cell_info.level
					
					local cur_level_cfg = self:GetSixteenCellLevelCfg(cur_task_cfg.dungeon_id, cell_info.level, true)
					if cur_level_cfg ~= nil then
						data.cur_level_mingwang_value = cur_level_cfg.score or 0
					end

					local level_cfg = self:GetSixteenCellLevelCfg(cur_task_cfg.dungeon_id, cell_info.level - 1, true)
					if level_cfg ~= nil then
						data.is_max = false
						data.desc_2 = string.format(str_tab.NextScoreLevel, level_cfg.score - cell_info.score)
						data.score_slider_value = (cell_info.score - data.cur_level_mingwang_value) / (level_cfg.score - data.cur_level_mingwang_value)
					end				
				end

				show_info = cell_info
			end
		elseif scene_type == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID then
			local avoid_info = self:GetYunSongAvoidInfo()
			if avoid_info ~= nil then
				data.pro_value = avoid_info.progress * 0.01

				if cur_task_cfg ~= nil then
					data.desc_1 = cur_task_cfg.level_desc .. "：" .. ToColorStr(avoid_info.num, COLOR3B.D_GREEN)
					data.pro_str = cur_task_cfg.progress_desc .. "：" ..avoid_info.progress .. "%"
					data.level = avoid_info.level
					local cur_level_cfg = self:GetYunSongAvoidLevelCfg(cur_task_cfg.dungeon_id, avoid_info.level, true)
					if cur_level_cfg ~= nil then
						data.cur_level_mingwang_value = cur_level_cfg.score or 0
					end

					local level_cfg = self:GetYunSongAvoidLevelCfg(cur_task_cfg.dungeon_id, avoid_info.level - 1, true)
					if level_cfg ~= nil then
						data.is_max = false
						data.desc_2 = string.format(str_tab.NextScoreLevel, level_cfg.score - avoid_info.num)
						data.score_slider_value = (avoid_info.num - data.cur_level_mingwang_value) / (level_cfg.score - data.cur_level_mingwang_value)
					end
				end

				show_info = avoid_info
			end

			data.time_str = str_tab.SubmissionTimerStr

		elseif scene_type == SceneType.CROSS_TASK_CHAIN_GUARD then
			local guard_info = self:GetGuardInfo()
			if guard_info ~= nil then
				data.pro_value = guard_info.progress * 0.01

				if cur_task_cfg ~= nil then
					data.desc_1 = cur_task_cfg.level_desc .. "：" .. ToColorStr(guard_info.score, COLOR3B.D_GREEN)
					data.pro_str = cur_task_cfg.progress_desc .. "：" ..guard_info.progress .. "%"
					data.level = guard_info.level
					local cur_level_cfg = self:GetGuardLevelCfg(cur_task_cfg.dungeon_id, guard_info.level, true)
					if cur_level_cfg ~= nil then
						data.cur_level_mingwang_value = cur_level_cfg.score or 0
					end

					local level_cfg = self:GetGuardLevelCfg(cur_task_cfg.dungeon_id, guard_info.level - 1, true)
					if level_cfg ~= nil then
						data.is_max = false
						data.desc_2 = string.format(str_tab.NextScoreLevel, level_cfg.score - guard_info.score)
						data.score_slider_value = (guard_info.score - data.cur_level_mingwang_value) / (level_cfg.score - data.cur_level_mingwang_value)
					end					
				end

				show_info = guard_info
			end
		elseif scene_type == SceneType.CROSS_TASK_CHAIN_BOSS then
			local boss_info = self:GetBossInfo()
			if boss_info ~= nil then
				data.pro_value = boss_info.progress * 0.01

				if cur_task_cfg ~= nil then
					data.desc_1 = cur_task_cfg.level_desc .. "：" .. ToColorStr(boss_info.score, COLOR3B.D_GREEN)
					data.pro_str = cur_task_cfg.progress_desc .. "：" ..boss_info.progress .. "%"
					data.level = boss_info.level
					local cur_level_cfg = self:GetBossLevelCfg(cur_task_cfg.dungeon_id, boss_info.level, true)
					if cur_level_cfg ~= nil then
						data.cur_level_mingwang_value = cur_level_cfg.score or 0
					end	

					local level_cfg = self:GetBossLevelCfg(cur_task_cfg.dungeon_id, boss_info.level - 1, true)
					if level_cfg ~= nil then
						data.is_max = false
						data.desc_2 = string.format(str_tab.NextScoreLevel, level_cfg.score - boss_info.score)
						data.score_slider_value = (boss_info.score - data.cur_level_mingwang_value) / (level_cfg.score - data.cur_level_mingwang_value)
					end
				end

				show_info = boss_info
			end
		end

		if show_info ~= nil and show_info.task_status == OPERATION_TASK_CHAIN_ACT_STATUS.STANDY then
			data.is_standy = true
			data.time_str = str_tab.TimerStr1			
		end
	end

	return data
end

function OperationTaskChainWGData:GetHuSongIsNeedFollow()
	local is_need = false
	local obj_id = nil
	local huosong_info = self:GetHuSongInfo()
	if huosong_info == nil then
		return is_need, obj_id
	end

	if huosong_info.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.MOVE then
		is_need = true
		obj_id = huosong_info.objid
	end

	return is_need, obj_id
end

function OperationTaskChainWGData:GetMainSceneFollowPos()
	local pos = nil
	local act_info = self:GetTaskChainActInfo()
	if act_info == nil then
		return pos
	end

    local task_chain_cfg = self:GetTaskChainCfg(act_info.task_chain_id)
    if task_chain_cfg == nil then
        return pos
    end

	local task_tab = Split(task_chain_cfg.task_ids, "|")
	if task_tab ~= nil and next(task_tab) ~= nil and task_tab[act_info.task_idx + 1] ~= nil then
		local task_cfg = self:GetTaskChainTaskCfg(tonumber(task_tab[act_info.task_idx + 1]))
		if task_cfg ~= nil then
		    local end_tab = Split(task_cfg.npc_end_pos, "|")
		    if end_tab ~= nil and next(end_tab) ~= nil then
		        pos = {
		            x = tonumber(end_tab[1]),
		            y = tonumber(end_tab[2])
		        }
		    end
		end
	end

	return pos
end

function OperationTaskChainWGData:GetExpTip(add_exp)
	local str = ""
	local info = self:GetTaskChainActInfo()
	if info == nil then
		return str
	end

	local show_exp = add_exp or info.add_exp
	local exp_str = ToColorStr(tostring(show_exp), COLOR3B.GREEN)
	str = string.format(Language.OpertionAcitvity.TaskChain.ExpStr, self.task_chain_other_cfg.add_exp_interval or 0, exp_str)
	return str
end

function OperationTaskChainWGData:GetHuSongTip()
	local str = ""
	local husong_info = self:GetHuSongInfo()
	if husong_info == nil then
		return str
	end

	local task_cfg = self:GetCurTaskCfg()
	local husong_cfg = self:GetHuSongFbCfg(task_cfg.dungeon_id, Scene.Instance:GetSceneId())
	if husong_cfg ~= nil then
		str = husong_cfg.escort_tip
	end

	if task_cfg ~= nil and husong_info.task_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.FIGHT then
		local cfg = self:GetHuSongMonsterFreshCfg(task_cfg.dungeon_id, husong_info.wave)
		if cfg ~= nil then
			str = cfg.see_monster_tip
		end
	end

	return str
end

function OperationTaskChainWGData:GetFollowDis()
	local dis = 5
	local info = self:GetTaskChainActInfo()
	if info == nil then
		return dis
	end

	local task_cfg = self:GetCurTaskCfg()
	if task_cfg ~= nil then
		dis = GameMath.Rand(task_cfg.npc_user_dist_min, task_cfg.npc_user_dist_limit)
	end

	return dis
end

function OperationTaskChainWGData:GetCurTaskCfg()
	local cfg = nil
	local info = self:GetTaskChainActInfo()
	if info == nil then
		return cfg
	end

	if info.task_idx ~= -1 then
		local task_chain_cfg = self:GetTaskChainCfg(info.task_chain_id)
		if task_chain_cfg ~= nil then
			local task_tab = Split(task_chain_cfg.task_ids, "|")
			if task_tab ~= nil and #task_tab > 0 then
				local task_id = task_tab[info.task_idx + 1]
				if task_id ~= nil and task_id ~= "" then
					cfg = self:GetTaskChainTaskCfg(tonumber(task_id))
				end
			end
		end
	end

	return cfg
end

function OperationTaskChainWGData:GetDungeonInfo()
	local data = nil
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == nil then
		return data
	end

	if scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
		data = self:GetXunLuoCaiJiDungeonInfo()
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
		data = self:GetCaiJiDungeonInfo()
	end

	return data
end

function OperationTaskChainWGData:GetRandomBubble(task_status)
	local info = self:GetHuSongInfo()
	if info == nil and task_status == nil then
		return
	end

	local task_cfg = self:GetCurTaskCfg()
	local husong_cfg = self:GetHuSongFbCfg(task_cfg.dungeon_id, Scene.Instance:GetSceneId())
	if husong_cfg == nil then
		return
	end

	local tab = nil
	local check_status = task_status
	if check_status == nil and info == nil then
		return
	end

	check_status = check_status == nil and info.task_status or check_status

	if check_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.MOVE then
		local str = husong_cfg.bubbletalk_id1
		tab = Split(str, "|")
	elseif check_status == OPERATION_TASK_CHAIN_HUSONG_STATUS.FIGHT then
		local str = husong_cfg.bubbletalk_id2
		tab = Split(str, "|")
	end

	if tab ~= nil and next(tab) ~= nil then
		local index = math.floor(1)
		index = tonumber(tab[index])
		local cfg = self:GetHuSongBubbleCfg(index)
		if cfg ~= nil then
			return cfg.content
		end
	end
end

--------------巡逻采集-------------------------------------------------

function OperationTaskChainWGData:GetSkill()
	local skill_data = {
		skill_id = 0,
		skill_type = OPERATION_TASK_CHAIN_SKILL_TYPE.NONE,
		param = nil,
		param_2 = nil,
		param_3 = nil,
		name = "",
	}

	return skill_data
end

function OperationTaskChainWGData:GetShowSkillList()
	local data_list = nil
	local info = self:GetTaskChainActInfo()
	if info == nil then
		return data_list
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == nil then
		return data_list
	end

	if scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
		data_list = {}
		local npc_id, _, name, gather_range = self:GetXunLuoCaiJiOperaInfo()
		if npc_id ~= nil and gather_range ~= nil then
			local skill_gather = self:GetSkill()
			skill_gather.skill_type = OPERATION_TASK_CHAIN_SKILL_TYPE.CHECK_GATHER
			skill_gather.param = nil
			skill_gather.param_2 = name
			skill_gather.param_3 = gather_range
			skill_gather.name = Language.OpertionAcitvity.TaskChain.BtnGather
			skill_gather.img = "taskicon_6"
			data_list[1] = skill_gather

			local skill_submission = self:GetSkill()
			skill_submission.skill_type = OPERATION_TASK_CHAIN_SKILL_TYPE.SUBMISSION
			skill_submission.npc_id = npc_id
			skill_submission.name = Language.OpertionAcitvity.TaskChain.BtnSubmission
			skill_submission.img = "xx_shangjiao"
			data_list[2] = skill_submission
		end
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
		data_list = {}
		local npc_id, _, name, gather_range = self:GetCaiJiOperaInfo()
		if npc_id ~= nil and gather_range ~= nil then
			local skill_gather = self:GetSkill()
			skill_gather.skill_type = OPERATION_TASK_CHAIN_SKILL_TYPE.CHECK_GATHER
			skill_gather.param = nil
			skill_gather.param_2 = name
			skill_gather.param_3 = gather_range * gather_range
			skill_gather.name = Language.OpertionAcitvity.TaskChain.BtnGather
			skill_gather.img = "taskicon_3"
			data_list[1] = skill_gather

			local skill_submission = self:GetSkill()
			skill_submission.skill_type = OPERATION_TASK_CHAIN_SKILL_TYPE.SUBMISSION
			skill_submission.npc_id = npc_id
			skill_submission.name = Language.OpertionAcitvity.TaskChain.BtnSubmission
			skill_submission.img = "xx_shangjiao"
			data_list[2] = skill_submission
		end
	elseif scene_type == SceneType.CROSS_TASK_CHAIN_YUN_SONG_AVOID then
		data_list = {}
		local gather_id, name, gather_range = self:GetYunSongAvoidGatherCfg()
		if gather_id ~= nil and name ~= nil and name ~= "" and gather_range ~= nil then
			local skill_gather = self:GetSkill()
			skill_gather.skill_type = OPERATION_TASK_CHAIN_SKILL_TYPE.CHECK_GATHER
			skill_gather.param = gather_id
			skill_gather.param_2 = name
			skill_gather.param_3 = gather_range * gather_range
			skill_gather.name = Language.OpertionAcitvity.TaskChain.BtnSubmission
			skill_gather.img = "xx_shangjiao"
			data_list[1] = skill_gather
		end
	end

	return data_list
end

function OperationTaskChainWGData:GetXunLuoCaiJiOperaInfo()
	local npc_id = nil
	local gather_radius = nil
	local name = ""
	local task_cfg = self:GetCurTaskCfg()
	local gather_range = nil

	if task_cfg == nil then
		return npc_id, gather_radius, name, gather_range
	end

	local dungeon_cfg = self:GetXunLuoCaiJiDungeonCfg(task_cfg.dungeon_id)
	if dungeon_cfg ~= nil then
		npc_id = dungeon_cfg.commit_npc
		gather_radius = dungeon_cfg.gather_radius
		name = dungeon_cfg.item_name or ""
		gather_range = dungeon_cfg.gather_range
	end

	return npc_id, gather_radius, name, gather_range
end

function OperationTaskChainWGData:GetSubmissionData()
	local data = {
		icon = "",
		num = 0,
		name = "",
		btn_str = "",
	}

	local num = self:GetOldCanSubmission()
	if num == nil then
		return data
	end

	local dungeon_cfg = self:GetDungeonInfo()
	if dungeon_cfg ~= nil then
		data.name = dungeon_cfg.item_name or ""
		data.btn_str = dungeon_cfg.commit_btn or ""
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
			data.icon = "taskicon_6"
		elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
			data.icon = "taskicon_3"
		end
	end

	data.num = num

	return data
end

function OperationTaskChainWGData:GetOldCanSubmission()
	local info = nil
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= nil then
		if scene_type == SceneType.CROSS_TASK_CHAIN_XUN_LUO_CAI_JI then
			info = self:GetXunLuoCaiJiInfo()
		elseif scene_type == SceneType.CROSS_TASK_CHAIN_CAI_JI then
			info = self:GetCaiJiInfo()
		end
	end

	if info == nil then
		return nil
	end

	return info.num
end

function OperationTaskChainWGData:GetOldXunLuoCaiJiSelfCommit()
	local num = nil
	local info = self:GetXunLuoCaiJiInfo()
	if info ~= nil and info.commit_self ~= nil then
		num = info.commit_self
	end

	return num
end

function OperationTaskChainWGData:GetXunLuoCaiJiDungeonInfo()
	local cfg = nil
	local task_cfg = self:GetCurTaskCfg()
	if task_cfg == nil then
		return cfg
	end

	cfg = self:GetXunLuoCaiJiDungeonCfg(task_cfg.dungeon_id)
	return cfg
end

--------------巡逻采集-------------------------------------------------
--------------采集-------------------------------------------------
function OperationTaskChainWGData:GetCaiJiOperaInfo()
	local npc_id = nil
	local gather_radius = nil
	local name = ""
	local task_cfg = self:GetCurTaskCfg()
	local gather_range = nil

	if task_cfg == nil then
		return npc_id, gather_radius, name, gather_range
	end

	local scene_id = Scene.Instance:GetSceneId()
	local cfg = self:GetCaiJiDungeonCfg(task_cfg.dungeon_id, scene_id)
	if cfg ~= nil then
		npc_id = cfg.commit_npc
		gather_radius = cfg.gather_radius
		name = cfg.item_name or ""
		gather_range = cfg.gather_range
	end

	return npc_id, gather_radius, name, gather_range
end

function OperationTaskChainWGData:GetCaiJiDungeonInfo()
	local cfg = nil
	local task_cfg = self:GetCurTaskCfg()
	if task_cfg == nil then
		return cfg
	end

	local scene_id = Scene.Instance:GetSceneId()
	cfg = self:GetCaiJiDungeonCfg(task_cfg.dungeon_id, scene_id)
	return cfg
end
--------------采集------end-------------------------------------------

--------------十六宫格-------------------------------------------------
function OperationTaskChainWGData:InitSixteenCellList()
	local task_cfg = self:GetCurTaskCfg()
	if task_cfg == nil then
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	local cfg = self:GetSixteenCellDungeonCfg(task_cfg.dungeon_id, scene_id)
	if cfg == nil then
		return
	end

	if self.sixteen_cell_map ~= nil and self.sixteen_cell_map.dungeon_id == task_cfg.dungeon_id and self.sixteen_cell_map.scene_id == task_cfg.scene_id then
		return
	end

	local left_pos_str = cfg.leftbottom_pos
	local right_pos_str = cfg.righttop_pos

	local left_tab = Split(left_pos_str, "|")
	local right_tab = Split(right_pos_str, "|")

	if left_tab == nil or next(left_tab) == nil or right_tab == nil or next(right_tab) == nil then
		return
	end

	self.sixteen_cell_map = {}
	self.sixteen_cell_map.dungeon_id = task_cfg.dungeon_id
	self.sixteen_cell_map.scene_id = scene_id

	local left_pos = {x = tonumber(left_tab[1]), y = tonumber(left_tab[2])}
	local right_pos = {x = tonumber(right_tab[1]), y = tonumber(right_tab[2])}
	local o_pos = left_pos

    local min_pos_x, min_pos_y = GameMapHelper.LogicToWorld(right_pos.x, left_pos.y)
    local hor_delta_pos = u3d.v2Sub({x = min_pos_x, y = min_pos_y}, left_pos)
    local ver_delta_pos = u3d.v2Sub(right_pos, {x = min_pos_x, y = min_pos_y})

	self.sixteen_cell_map.hor_dir = u3d.v2Normalize(hor_delta_pos)
	self.sixteen_cell_map.ver_dir = u3d.v2Normalize(ver_delta_pos)

	-- local rotation = Quaternion.LookRotation(direction)

	local len = math.floor(right_pos.x - left_pos.x)
	local h = math.floor(right_pos.y - left_pos.y)
	-- local offst = {x = -1, y = 1}
	local offst = {x = 0, y = 0}

	local cell_size = math.ceil(len / GameEnum.SIXTEEN_CELL_NUM)

	local cell_list = {}
	for i = 1, GameEnum.SIXTEEN_CELL_NUM do
		for j = 1, GameEnum.SIXTEEN_CELL_NUM do
			if cell_list[i] == nil then
				cell_list[i] = {}
			end

			local logic_x = o_pos.x + (i - 0.5) * cell_size + offst.x
			local logic_y = o_pos.y + (j - 0.5) * cell_size + offst.y

			local real_x, real_y = GameMapHelper.LogicToWorld(logic_x, logic_y)
			cell_list[i][j] = {x = logic_x, y = logic_y, is_safe = true, real_x = real_x, real_y = real_y, size = cell_size}
		end
	end

	self.sixteen_cell_map.cell_list = cell_list
 end

 function OperationTaskChainWGData:GetSixteenCellDungeonInfo()
	local cfg = nil
	local task_cfg = self:GetCurTaskCfg()
	if task_cfg == nil then
		return cfg
	end

	local scene_id = Scene.Instance:GetSceneId()
	cfg = self:GetSixteenCellDungeonCfg(task_cfg.dungeon_id, scene_id)
	return cfg
end

function OperationTaskChainWGData:CheckSixteenCellListStatus()
	if self.sixteen_cell_map == nil then
		return
	end

	local info = self:GetSixteenCellRoundInfo()
	if info == nil then
		return
	end

	local hor_tab = bit:d2b_two(info.jiguan_hor_flags)
	local ver_tab = bit:d2b_two(info.jiguan_ver_flags)

	for i = 1, GameEnum.SIXTEEN_CELL_NUM do
		for j = 1, GameEnum.SIXTEEN_CELL_NUM do
			local is_safe = hor_tab[i - 1] == 0 and ver_tab[j - 1] == 0
			self.sixteen_cell_map.cell_list[i][j].is_safe = is_safe
		end
	end
end

function OperationTaskChainWGData:GetMapCellList()
	local list = nil
	if self.sixteen_cell_map ~= nil then
		list = self.sixteen_cell_map.cell_list
	end

	return list
end

function OperationTaskChainWGData:GetAvoidDir()
	local hor_dir = nil
	local ver_dir = nil

	if self.sixteen_cell_map ~= nil then
		hor_dir = self.sixteen_cell_map.hor_dir
		ver_dir = self.sixteen_cell_map.ver_dir
	end	

	return hor_dir, ver_dir
end
--------------十六宫格------end-------------------------------------------
--------------运送躲避----------------------------------------------------
function OperationTaskChainWGData:GetYunSongAvoidDungeonInfo()
	local task_cfg = self:GetCurTaskCfg()
	if task_cfg == nil then
		return nil
	end

	local scene_id = Scene.Instance:GetSceneId()
	return self:GetYunSongAvoidDungeonCfg(task_cfg.dungeon_id, scene_id)
end

function OperationTaskChainWGData:GetYunSongAvoidGatherCfg()
	local gather_id = nil
	local name = ""
	local gather_range = nil

	local cfg = self:GetYunSongAvoidDungeonInfo()
	if cfg ~= nil then
		gather_id = cfg.gather_id
		name = cfg.item_name
		gather_range = cfg.gather_range
	end

	return gather_id, name, gather_range
end

function OperationTaskChainWGData:GetYunSongTip()
	local str = nil
	local cfg = self:GetYunSongAvoidDungeonInfo()
	if cfg ~= nil then
		str = cfg.tip
	end	

	local info = self:GetYunSongAvoidJiGuanInfo()
	if info ~= nil and info.speed_status == OPERATION_TASK_CHAIN_YUN_SONG_ADOID_STATUS.FAST then
		str = str.tip_speedup
	end

	return str
end

function OperationTaskChainWGData:SetGatherWaitTime(value)
	self.gather_wait_time = value
end

function OperationTaskChainWGData:GetGatherWaitTime()
	return self.gather_wait_time
end

function OperationTaskChainWGData:GetShowYunSongWarnEffList()
	local show_list = nil

	local task_cfg = self:GetCurTaskCfg()
	if task_cfg == nil then
		return show_list
	end

	local all_cfg = self:GetYunSongAvoidJiGuanCfg()
	if all_cfg == nil then
		return show_list
	end

	local jiguan_cfg = all_cfg[task_cfg.dungeon_id]
	if jiguan_cfg == nil then
		return show_list
	end

	local info = self:GetYunSongAvoidJiGuanInfo()
	if info == nil then
		return show_list
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return show_list
	end

	local role_pos = main_role:GetLuaPosition()

	local tab = bit:d2b_two(info.jiguan_flags)
	if tab ~= nil and next(tab) ~= nil then
		show_list = {}
		local off = {x = 0, y = 0}

		for i = 1, 3 do
			local index = i - 1
			if tab[index] == 1 then
				local cfg = jiguan_cfg[i]
				if cfg ~= nil then
					local start_pos_tab = Split(cfg.trigger_born_pos, "|")
					local end_pos_tab = Split(cfg.trigger_end_pos, "|")
					if start_pos_tab ~= nil and next(start_pos_tab) ~= nil and end_pos_tab ~= nil and next(end_pos_tab) ~= nil then
						local logic_start = {x = tonumber(start_pos_tab[1]), y = tonumber(start_pos_tab[2])}
						local logic_end = {x = tonumber(end_pos_tab[1]), y = tonumber(end_pos_tab[2])}

						local real_start_x, real_start_y = GameMapHelper.LogicToWorld(logic_start.x, logic_start.y)
						local real_end_x, real_end_y = GameMapHelper.LogicToWorld(logic_end.x, logic_end.y)
						local pos_start = Vector3(real_start_x - off.x, role_pos.y, real_start_y - off.y)
						local pos_end = Vector3(real_end_x - off.x, role_pos.y, real_end_y - off.y)
						local rotation = Quaternion.LookRotation(pos_end - pos_start)

						local data = {
							bundle = "effects2/prefab/misc/effect_lanruosi_gsyj_prefab",
							asset = "effect_LanRuoSi_gsyj",
							pos = pos_start,
							rotation = rotation,
						}
						show_list[i] = data
					end
				end
			end
		end
	end

	return show_list
end
--------------运送躲避------end-------------------------------------------
--------------守护----------------------------------------------------
function OperationTaskChainWGData:GetGuardDungeonInfo()
	local task_cfg = self:GetCurTaskCfg()
	if task_cfg == nil then
		return nil
	end

	local scene_id = Scene.Instance:GetSceneId()
	return self:GetGuardDungeonCfg(task_cfg.dungeon_id, scene_id)
end

function OperationTaskChainWGData:GetShouHuObjId()
	local info = self:GetGuardInfo()
	if info == nil then
		return
	end

	return info.shouhu_objid
end

function OperationTaskChainWGData:GetGuardBubbleStr(hp)
	local cfg = self:GetGuardDungeonInfo()
	if cfg == nil then
		return
	end

	if hp ~= nil then
		if hp > 0.5 then
			return cfg.tip1
		else
			return cfg.tip2
		end
	end
end
--------------守护----------end---------------------------------------
--------------BOSS----------------------------------------------------
function OperationTaskChainWGData:GetBossDungeonInfo()
	local task_cfg = self:GetCurTaskCfg()
	if task_cfg == nil then
		return nil
	end

	local scene_id = Scene.Instance:GetSceneId()
	return self:GetBossDungeonCfg(task_cfg.dungeon_id, scene_id)
end
--------------BOSS----------end---------------------------------------

--获取活动开启当天的配置
function OperationTaskChainWGData:GetOpenServerDayCfg()
	local cfg = self.task_chain_server_cfg

	local activity_open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)

	for k,v in pairs(cfg) do
		if activity_open_day >= v.start_server_day and activity_open_day <= v.end_server_day then
			return v
		end
	end
end

function OperationTaskChainWGData:GetTaskChainInterfaceCfg()
	local openserver_day_cfg = self:GetOpenServerDayCfg()

	if not openserver_day_cfg then
		return
	end

	for k,v in pairs(self.task_chain_interface_cfg) do
		if openserver_day_cfg.interface == v.interface then
			return v
		end
	end
end

function OperationTaskChainWGData:GetViewShowTime()
	local time = 0
	local activity_open_day = OperationActivityWGData.Instance:GetActOpenDay(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
	local check_day = 0

	local task_grade_list = {}
	if self.task_chain_info and self.task_chain_info.grade then
		local grade = self.task_chain_info.grade
		task_grade_list = self:GetTaskGradeListCfg(grade)
	end

	if activity_open_day ~= nil and act_info ~= nil and task_grade_list then
		table.sort(task_grade_list, SortTools.KeyLowerSorter("day_index"))

		for k, v in pairs(task_grade_list) do
			if v.day_index == activity_open_day then
				activity_open_day = activity_open_day + 1
				check_day = check_day + 1
			else
				if v.day_index > activity_open_day then
					break
				end
			end
		end

		local has_time = act_info.end_time - act_info.start_time - activity_open_day * 86400
		if has_time <= 0 then
			time = act_info.end_time
		else
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			local time_tab = os.date("*t", server_time)
			if time_tab ~= nil then
				time = server_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec + 86400 * check_day
			end
		end
	end

	return time
end

function OperationTaskChainWGData:GetTaskGradeListCfg(grade)
	if nil == grade then
		return
	end

	return self.task_chain_grade_list_cfg[grade]
end


--------------------------hot------------------------------
function OperationTaskChainWGData:HotUpdateCommon()
	local all_cfg = ConfigManager.Instance:GetAutoConfig("cross_taskchain_auto")
	self.task_chain_cfg = ListToMap(all_cfg.task_chain, "task_chain_id")
	self.task_chain_task_cfg = ListToMap(all_cfg.task, "task_id")
	self.task_chain_grade_cfg = ListToMap(all_cfg.grade, "grade", "day_index")
	self.task_chain_grade_list_cfg = ListToMapList(all_cfg.grade, "grade")
	self.task_chain_task_reward_cfg = ListToMap(all_cfg.task_reward, "task_reward", "task_num")
	self.task_chain_level_reward_cfg = ListToMap(all_cfg.level_reward, "level_reward", "task_num", "level")
	self.task_chain_mingwang_reward_cfg = ListToMapList(all_cfg.mingwang_reward, "mingwang_reward")
	self.task_chain_day_index_cfg = ListToMap(all_cfg.gameplay, "week_index")
	self.task_chain_scene_block_cfg = ListToMap(all_cfg.airwall, "scene_id")
	self.task_chain_other_cfg = all_cfg.other[1]
	self.task_chain_server_cfg = all_cfg.server
	self.task_chain_interface_cfg = all_cfg.interface
end

function OperationTaskChainWGData:HotUpdateHuSong()
	local all_husong_cfg = ConfigManager.Instance:GetAutoConfig("husong_fb_cfg_auto")
	self.husong_fb_cfg = ListToMap(all_husong_cfg.dungeon, "dungeon_id", "scene_id")
	self.husong_level_cfg = ListToMap(all_husong_cfg.level, "dungeon_id", "level")
	self.husong_monsterfresh_cfg = ListToMap(all_husong_cfg.monsterfresh, "dungeon_id", "wave")
	self.husong_bubble_cfg = ListToMap(all_husong_cfg.bubbletalk, "bubbletalk_id")
end

function OperationTaskChainWGData:HotUpdateCaiJiXunLuo()
	local xunluo_caiji_cfg = ConfigManager.Instance:GetAutoConfig("caijixunluo_fb_cfg_auto")
	self.xunluo_caiji_dungeon_cfg = ListToMap(xunluo_caiji_cfg.dungeon, "dungeon_id")
	self.xunluo_caiji_level_cfg = ListToMap(xunluo_caiji_cfg.level, "dungeon_id", "level")
end

function OperationTaskChainWGData:HotUpdateCaiJi()
	local caiji_cfg = ConfigManager.Instance:GetAutoConfig("caiji_fb_cfg_auto")
	self.caiji_dungeon_cfg = ListToMap(caiji_cfg.dungeon, "dungeon_id", "scene_id")
	self.caiji_level_cfg = ListToMap(caiji_cfg.level, "dungeon_id", "level")
end

function OperationTaskChainWGData:HotUpdateAvoid()
	local sixteen_cell_cfg = ConfigManager.Instance:GetAutoConfig("shiliugonggeduobi_fb_cfg_auto")
	self.sixteen_cell_dungeon_cfg = ListToMap(sixteen_cell_cfg.dungeon, "dungeon_id", "scene_id")
	self.sixteen_cell_level_cfg = ListToMap(sixteen_cell_cfg.level, "dungeon_id", "level")
end

function OperationTaskChainWGData:HotUpdateYunSongAvoid()
	local yun_song_avoid_cfg = ConfigManager.Instance:GetAutoConfig("yunsongduobi_fb_cfg_auto")
	self.yun_song_avoid_dungeon_cfg = ListToMap(yun_song_avoid_cfg.dungeon, "dungeon_id", "scene_id")
	self.yun_song_avoid_level_cfg = ListToMap(yun_song_avoid_cfg.level, "dungeon_id", "level")
	self.yun_song_avoid_jiguan_cfg = ListToMap(yun_song_avoid_cfg.trigger_pos, "dungeon_id", "trigger_sqe")
end

function OperationTaskChainWGData:HotUpdateGround()
	local guard_cfg = ConfigManager.Instance:GetAutoConfig("shouhu_fb_cfg_auto")
	self.guard_dungeon_cfg = ListToMap(guard_cfg.dungeon, "dungeon_id", "scene_id")
	self.guard_level_cfg = ListToMap(guard_cfg.level, "dungeon_id", "level")
end

function OperationTaskChainWGData:HotUpdateBoss()
	local boss_cfg = ConfigManager.Instance:GetAutoConfig("boss_fb_cfg_auto")
	self.boss_dungeon_cfg = ListToMap(boss_cfg.dungeon, "dungeon_id", "scene_id")
	self.boss_level_cfg = ListToMap(boss_cfg.level, "dungeon_id", "level")
end

