require("game/operation_activity/operation_act_render_base")
require("game/operation_activity/operation_act_model")
require("game/operation_activity/operation_act_mecha")
require("game/operation_activity/operation_act_prefab")
require("game/operation_activity/operation_act_image")

OARenderType = {
	RoleModel = 0, 	--模型
	Prefab = 1,  	--相框之类预制体
	Image = 2, 		--图片
	Mecha = 3,      --机甲
	Prefabs = 11,      --相框之类预制体(多个)
	-- Images = 12,      --图片(多个) （未测试）
}

OperationActRender = OperationActRender or BaseClass(BaseRender)

function OperationActRender:__init(instance, bundle, asset)
	bundle = bundle or "uis/view/opera_render_prefab"
	asset = asset or "display_item"

	self:LoadAsset(bundle, asset, instance.transform, function(obj)
		if IsNil(obj) then
			return
		end

		obj.transform.localPosition = Vector3.zero
		obj.transform.localRotation = Quaternion.Euler(0, 0, 0)
		obj.transform.localScale = Vector3.one

		self.model_render = OperationActModel.New(self.node_list)
		self.prefab_render = OperationActPrefab.New(self.node_list)
		self.image_render = OperationActImage.New(self.node_list)
		self.mecha_render = OperationActMecha.New(self.node_list)

		-- self.prefab_render_list = AsyncFancyAnimView.New(OperationActPrefabRender, self.node_list)

		self.load_finish = true
		if self.need_flush then
			self:Flush()
			self.need_flush = false
		end
	end)
end

function OperationActRender:__delete()
	self.load_finish = false
	self.need_flush = false
	if self.model_render then
		self.model_render:DeleteMe()
		self.model_render = nil
	end

	if self.prefab_render then
		self.prefab_render:DeleteMe()
		self.prefab_render = nil
	end

	if self.image_render then
		self.image_render:DeleteMe()
		self.image_render = nil
	end

	if self.mecha_render then
		self.mecha_render:DeleteMe()
		self.mecha_render = nil
	end

	if self.prefab_render_list then
		self.prefab_render_list:DeleteMe()
		self.prefab_render_list = nil
	end
end

function OperationActRender:Reset()
	if self.model_render then
		self.model_render:Reset()
	end

	if self.prefab_render then
		self.prefab_render:Reset()
	end

	if self.image_render then
		self.image_render:Reset()
	end

	if self.mecha_render then
		self.mecha_render:Reset()
	end
end

--[[配置数据：
    ----[[总数据
        render_type = OARenderType.RoleModel,
        
        model_click_func
        hide_model_block

        -- 【额外修改 display_item 根节点transform】
		-- 整体的节点偏移修改这个
        position (table、Vector3)
        rotation (table、Quaternion、Vector3)
        scale (number)
    ----]]
--]]

--[[
	-- 提供给策划配置的定义
	整体节点位置:whole_display_pos
	整体节点旋转:whole_display_rot
	整体节点尺寸:whole_display_scale
	模型位置:model_pos
	模型旋转角度:model_rot
	模型尺寸:model_scale

	·因为现在是RT图渲染模型，在节点下挂载的为图片					
	·“整体节点”的修改是主要修改整体节点的位置旋转尺寸，主要修改预制体（气泡、头像、称号）、图片资源的展示	
	·“模型”调整是调整模型在摄像机视野下的位置旋转尺寸		

	·模型的摆放可以调整这个“整体pos”；“模型位置”调整不到位可能会超出摄像机视野，RT图出现边界感					
	·模型的旋转只能调“模型旋转角度”；“整体节点旋转”旋转不了模型，只会旋转到RT图片					
	·模型的尺寸调“模型尺寸”；调“整体节点尺寸”，容易造成RT图内存浪费，且尺寸不能比1大，不然会导致模型展示模糊					
					
	·如果功能只展示模型，可在配置表里将这3个字段去了，剩余3个基本满足调整需求				
		whole_display_rot、whole_display_scale、model_pos
--]]

function OperationActRender:OnFlush()
	if not self.load_finish then
		self.need_flush = true
		return
	end

	if not self.data then
		return
	end

	self:Reset()
	if self.data.render_type == OARenderType.RoleModel then
		self:ShowModel()
	elseif self.data.render_type == OARenderType.Prefab then
		self:ShowPrefab()
	elseif self.data.render_type == OARenderType.Image then
		self:ShowImage()
	elseif self.data.render_type == OARenderType.Mecha then
		self:ShowMecha()
	elseif self.data.render_type == OARenderType.Prefabs then
		self:ShowPrefab()
	-- elseif self.data.render_type == OARenderType.Images then
	-- 	self:ShowImage()
	end

	if type(self.data.position) == "table" then
		self:SetLocalPosition(self.data.position)
	end

	if type(self.data.rotation) == "table" then
		self:SetLocalRotation(self.data.rotation)
	end

	if type(self.data.scale) == "table" then
		self:SetLocalScale(self.data.scale)
	end
end

function OperationActRender:ShowModel()
	if self.model_render then
		self.model_render:SetData(self.data)
		self.model_render:Show()
	end
end

function OperationActRender:ShowPrefab()
	if self.prefab_render then
		self.prefab_render:SetData(self.data)
		self.prefab_render:Show()
	end
end

function OperationActRender:ShowPrefabs()
	if self.prefab_render_list then
		print_error("self.data==========",self.data)
		local item_id_list = string.split(self.data.item_id, ",") 

		local data_list = {}
		for i, v in ipairs(item_id_list) do
			local data = self.data
			data.item_id = tonumber(v)
			table.insert(data_list,data)
		end
		self.prefab_render_list:SetDataList(data_list)
	end
end

function OperationActRender:ShowImage()
	if self.image_render then
		self.image_render:SetData(self.data)
		self.image_render:Show()
	end
end

function OperationActRender:ShowMecha()
	if self.mecha_render then
		self.mecha_render:SetData(self.data)
		self.mecha_render:Show()
	end
end

function OperationActRender:SetLocalPosition(position)
	self.view.gameObject.transform.localPosition = position
end

function OperationActRender:SetLocalRotation(rotation)
	if not rotation.w then
		rotation = Quaternion.Euler(rotation.x, rotation.y, rotation.z)
	end

	self.view.gameObject.transform.localRotation = rotation
end

function OperationActRender:SetLocalScale(scale)
	self.view.gameObject.transform.localScale = scale
end

function OperationActRender:SetModelType(model_type, offset_type)
	if self.model_render then
		self.model_render:SetModelType(model_type, offset_type)
	end
end

function OperationActRender:ActModelPlayLastAction()
	if self.data and self.data.render_type == OARenderType.RoleModel and self.model_render then
		self.model_render:PlayLastAction()
	end
end

function OperationActRender:PlayWeaponAction()
	if self.data and self.data.render_type == OARenderType.RoleModel and self.model_render then
		self.model_render:PlayWeaponAction()
	end
end

function OperationActRender:SetModleStageIsActive(value)
	if self.model_render then
		self.model_render:SetModleStageIsActive(value)
	end
end

--显示当前玩家模型
--res_id默认当前角色模型，weapon_id默认当前武器，is_need_weapon是否展示武器，is_need_do_anim是否需要UIIdle动画.
function OperationActRender:ShowCurRoleModel(res_id, weapon_id, is_need_weapon, is_need_do_anim)
	if self.model_render then
		self.model_render:ShowCurRoleModel(res_id, weapon_id, is_need_weapon, is_need_do_anim)
	end
end

function OperationActRender:DisplayRemoveAllModel()
	if self.model_render then
		self.model_render:DisplayRemoveAllModel()
	end
end
