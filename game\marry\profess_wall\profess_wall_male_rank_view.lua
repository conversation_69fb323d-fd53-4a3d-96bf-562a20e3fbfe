function ProfessView:LoadProfessMaleRankViewCallBack()
	self.male_rank_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["male_profess_display"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}

	self.male_rank_model:SetRenderTexUI3DModel(display_data)

    if not self.male_rank_list then
        self.male_rank_list = AsyncListView.New(ProfessRankRender, self.node_list.male_rank_list)
    end

    self.male_my_rank = ProfessRankRender.New(self.node_list["ph_male_profess_wall"])
	self.male_my_rank:SetIndex(0)

    XUI.AddClickEventListener(self.node_list["btn_male_select_profess"], BindTool.Bind(self.OpenMaleSelectProfess, self))
end

function ProfessView:ShowProfessMaleRankViewCallBack()

end

function ProfessView:ReleaseProfessMaleRankViewCallBack()
    if self.male_rank_model then
		self.male_rank_model:DeleteMe()
		self.male_rank_model = nil
	end

    if self.male_rank_list then
        self.male_rank_list:DeleteMe()
        self.male_rank_list = nil
    end

    if self.male_my_rank then
        self.male_my_rank:DeleteMe()
        self.male_my_rank = nil
    end
end

function ProfessView:OnFlushProfessMaleRankView()
    local rank_data_list = ProfessWallWGData.Instance:GetProfessRankInfoByRankType(TabIndex.profess_wall_rank_male)
    self.male_rank_list:SetDataList(rank_data_list)
    self:FlushMaleMyRankInfo()
    self:FlushMalePersonModel()

    --刷新时间
	if self.male_time_quest == nil then
		self.male_time_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushMaleRankNextTime, self), 1)
		self:FlushMaleRankNextTime()
	end
end

function ProfessView:FlushMaleMyRankInfo()
	local my_info_data = ProfessWallWGData.Instance:GetSpecialProfessRankInfo()
	self.male_my_rank:SetData(my_info_data[1])
	local score = my_info_data[1].limit_score
	self.node_list["male_integral_desc"].text.text = score > 0 and string.format(Language.ProfessWall.rank_tips, score) or ""
end

function ProfessRankMaleView:FlushMalePersonModel()
	local reward_item = ProfessWallWGData.Instance:GetProfessFashionImage()
	if reward_item == nil then
		return
	end
	
	local res_id, _, cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(reward_item.item_id)
	self.male_rank_model:SetRoleResid(res_id)
	--战力

	local fashion_level        = 0
	-- local level_vo             = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(cfg.part_type, cfg.index, fashion_level)
	-- local next_level_vo        = NewAppearanceWGData.Instance:GetFashionUpLevelCfg(cfg.part_type, cfg.index, fashion_level + 1)
	local level_vo             = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(cfg.part_type, cfg.index, fashion_level)
	local next_level_vo        = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(cfg.part_type, cfg.index, fashion_level + 1)
	local level_attribute      = AttributeMgr.GetAttributteByClass(level_vo)
	local next_level_attribute = AttributeMgr.GetAttributteByClass(next_level_vo)
	local add_attribute        = AttributeMgr.LerpAttributeAttr(level_attribute, next_level_attribute)
	add_attribute              = AttributeMgr.AttributeFloorRounding(add_attribute)
	self.node_list["male_fight_power"].text.text       = "+" .. AttributeMgr.GetCapability(add_attribute)
end

function ProfessView:FlushMaleRankNextTime()
	local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
	if time <= 0 then
		if self.male_time_quest then
			GlobalTimerQuest:CancelQuest(self.male_time_quest)
			self.male_time_quest = nil
		end
	end

	local time_str = ""
	if time > 3600 then
		time_str = TimeUtil.FormatSecond(time, 6)
	else
		time_str = TimeUtil.FormatSecond(time, 2)
	end

	self.node_list["male_time"].text.text = Language.OpenServer.ActRemainTime .. ToColorStr(time_str, COLOR3B.GREEN)
end

function ProfessView:OpenMaleSelectProfess()
	ViewManager.Instance:Open(GuideModuleName.ChooseProfessView)
end