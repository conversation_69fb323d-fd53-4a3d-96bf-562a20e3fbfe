SevenDayView = SevenDayView or BaseClass(SafeBaseView)
--对应表里边的七个
local RES_TYPE = {
	ZERO = 0,
	HUJIA = 1,
	LINGCHONG = 2,
	ZUOQI = 3,
	BAOSHI = 4,
	FABAO = 5,
	SHENBING = 6,
	SHIZHUANG = 7,
	YUYI = 8,
	TIANSHEN = 9,
	JIANLING = 10,
	KUN = 11,
	IMAGE = 12,
	ZHUJI = 13,   --足迹
	WUQI = 14,    --武器外观
	NPC = 15,     --NPC
	PHOTOFRAME = 16, --相框
	BUBBLE = 17,  --气泡
	MASK = 18,    --脸饰
	BELT = 19,    --腰饰
	WEIBA = 20,   --尾巴
	SHOUHUAN = 21, --手环
	MONSTER = 22, --异兽
	BEASTS = 23,	--灵兽
	MINGQI = 24,	--命器
	SOULRING = 25,  --魂环
	TIANSHENWUQI = 26,  --天神武器
}

function SevenDayView:__init()
	self.view_style = ViewStyle.Full
	self.view_name = GuideModuleName.SevenDay
	self.is_align_right = true -- 是否向右对齐
	self.is_safe_area_adapter = true

	self.select_day = -1
	self:SetMaskBg()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_background_common_panel")
	self:AddViewResource(0, "uis/view/welfare_ui_prefab", "layout_seven")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function SevenDayView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a3_xsdlb_bg")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	self.node_list.title_view_name.text.text = Language.LingYuanLiBao.TitleViewName

	self:InitDisplayModel()

	if self.node_list.ph_display and not self.origin_display_pos then
		self.origin_display_pos = self.node_list.ph_display.rect.anchoredPosition
	end

	self.seven_list_view = AsyncListView.New(SevenDayItemRender, self.node_list.seven_list)

	self.background_loader = nil
end

function SevenDayView:InitDisplayModel()
	self.model_display = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["ph_display"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}
	
	self.model_display:SetRenderTexUI3DModel(display_data)
	-- self.model_display:SetUI3DModel(self.node_list["ph_display"].transform, self.node_list.Block.event_trigger_listener,
	-- 	1, false, MODEL_CAMERA_TYPE.BASE)
end

function SevenDayView:ReleaseCallBack()
	if self.seven_list_view then
		self.seven_list_view:DeleteMe()
		self.seven_list_view = nil
	end

	if WelfareWGData.Instance then
		WelfareWGData.Instance:SetSelectSeventDayIndex(nil)
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.tujian_cell then
		self.tujian_cell:DeleteMe()
		self.tujian_cell = nil
	end

	self.select_item_data = nil
	self.origin_display_pos = nil

	if self.show_head_cell then
		self.show_head_cell:DeleteMe()
		self.show_head_cell = nil
	end

	if self.background_loader then
		self.background_loader:Destroy()
		self.background_loader = nil
	end
end

function SevenDayView:FlushItemListRender()
	if not self.data_list then
		return
	end

	self.seven_list_view:SetDataList(self.data_list)

	for i, v in ipairs(self.data_list) do
		if v.status == SEVEN_COMPLETE_STATUS.KELINGQU then
			self.seven_list_view:JumpToIndex(i,3)
			return
		end
	end
	
end

function SevenDayView:OnFlush()
	self.data_list = WelfareWGData.Instance:GetSevenDayCfg()
	local canday = WelfareWGData.Instance:GetCanRewardDay()
	local cur_select_index = canday > SEVEN_DAY_LOGIN_MAX_REWARD_DAY and SEVEN_DAY_LOGIN_MAX_REWARD_DAY or canday
	WelfareWGData.Instance:SetSelectSeventDayIndex(cur_select_index)
	self.select_day = canday
	self:SetInfo(self.select_day) --默认选中
	self:ToUpdateSevenDay()
	self:FlushItemListRender()
end

function SevenDayView:SetInfo(canday)
	if canday > SEVEN_DAY_LOGIN_MAX_REWARD_DAY then
		self.seven_day_is_over = 1
		RoleWGData.SetRolePlayerPrefsInt("seven_day_over", self.seven_day_is_over)
		MainuiWGCtrl.Instance:SetMainTopBtnObjEnable("BtnSeventDayView", false)
		canday = SEVEN_DAY_LOGIN_MAX_REWARD_DAY
	end
end

function SevenDayView:OpenCallBack()
	if PlayerPrefsUtil.GetString("FirstOpenSevenDayView") ~= "FirstOpenSevenDayView" then
		PlayerPrefsUtil.SetString("FirstOpenSevenDayView", "FirstOpenSevenDayView")
		TalkCache.StopCurIndexAudio()
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Sound24, nil, true))
	end
end

function SevenDayView:ToUpdateSevenDay()
	self.select_item_data = self.data_list[self.select_day]
	if not self.select_item_data then
		return
	end

	self.node_list.tips_text.text.text = self.select_day
	self.node_list.seven_slogan_text.text.text = Language.LingYuanLiBao.Slogan[self.select_day]

	self:FlushBackground(self.select_item_data.background_id)

	--获取当前显示的是否是图片
	local is_show_image = self.select_item_data.is_image
	self.node_list["ph_display"]:SetActive(is_show_image == 0)
	self.node_list["display_image"]:SetActive(is_show_image == 1)
	self.node_list["image_title_display"]:SetActive(is_show_image == 2)
	self.node_list["tujian_image"]:SetActive(is_show_image == 3)

	--设置选中时显示的头奖的模型
	if is_show_image == 1 then
		self:SetRect(self.node_list.display_image, nil, self.select_item_data)
		self:ShowDisImages(self.select_item_data)
	elseif is_show_image == 0 then
		self:SetRect(self.node_list.ph_display, self.model_display, self.select_item_data)
		self:ShowDisModel(self.select_item_data)
	elseif is_show_image == 2 then
		self:SetRect(self.node_list.image_title_display, nil, self.select_item_data)
		self:ShowBubbleAndPhotoFrame(self.select_item_data)
	elseif is_show_image == 3 then --图鉴
		self:SetRect(self.node_list.tujian_image, nil, self.select_item_data)
		self:ShowSevenDayTuJian(self.select_item_data)
	end
end

function SevenDayView:ShowSevenDayTuJian(select_data)
	if not self.tujian_cell then
		self.tujian_cell = BaseTuJianCell.New(self.node_list["tujian_image"])
		self.tujian_cell:SetData({ item_id = select_data.model_id })
	end
end

function SevenDayView:ShowBubbleAndPhotoFrame(select_data)
	if select_data.res_type == RES_TYPE.PHOTOFRAME then
		self:ShowBubbleOrPhotoFrame(true, false, false)
		self:ShowPhotoFrameModel(select_data.model_id)
	elseif select_data.res_type == RES_TYPE.BUBBLE then
		self:ShowBubbleOrPhotoFrame(false, true, false)
		self:ShowBubbleModel(select_data.model_id)
	elseif select_data.res_type == RES_TYPE.MINGQI then
		self:ShowBubbleOrPhotoFrame(false, false, true)
		self:ShowMingQiModel(select_data.model_id)
	end
end

function SevenDayView:ShowBubbleOrPhotoFrame(frame_show, bubble_show, mingqi_show)
	self.node_list.photo_frame_pos:SetActive(frame_show)
	self.node_list.bubble_pos:SetActive(bubble_show)
	self.node_list.spine_cell_root:SetActive(mingqi_show)
end

function SevenDayView:ShowPhotoFrameModel(photoframe_id)
	if not self.show_head_cell then
		self.show_head_cell = BaseHeadCell.New(self.node_list["photo_frame_pos"])
	end

	local data = { fashion_photoframe = photoframe_id }
	self.show_head_cell:SetImgBg(true)
	self.show_head_cell:SetData(data)
	self.show_head_cell:SetBgActive(false)
end

function SevenDayView:ShowBubbleModel(bubble_id)
	if not IsNil(self.node_list.bubble_pos.transform) then
		self.show_bubble_cell = self.show_bubble_cell or AllocAsyncLoader(self, "fashion_bubble_cell")
		self.show_bubble_cell:SetIsUseObjPool(true)
		self.show_bubble_cell:SetParent(self.node_list.bubble_pos.transform)
		local asset, bundle = ResPath.ChatBigBubbleBig(bubble_id)
		self.show_bubble_cell:Load(asset, bundle)
	end
end

function SevenDayView:ShowMingQiModel(model_id)
	if not IsNil(self.node_list.spine_cell_root.transform) then
		local res_id = ArtifactWGData.Instance:GetArtifactCfgByItemId(model_id).model_id
		local bundle, asset = ResPath.GetShuangXiuTipUI(res_id)
		--self.model_display:SetMainAsset(bundle, asset)
		self:SetSpineCell(bundle, asset)
	end
end

function SevenDayView:ShowDisImages(select_data)
	local image_ID = "a3_" .. select_data.model_id
	self.node_list["display_image"].raw_image:LoadSprite(ResPath.GetRawImagesPNG(image_ID))
	self.node_list["display_image"].raw_image:SetNativeSize()
end

function SevenDayView:ShowDisModel(select_data)
	self.model_display:RemoveMain()
	self.model_display:RemoveWeapon()
	self.model_display:ResetRotation()
	self.model_display:RemoveFootTrail()

	if nil ~= self.model_display then
		self.model_display:ClearModel()
	end

	local bundle, asset
	if select_data.res_type == RES_TYPE.ZERO then
		bundle, asset = ResPath.GetWelfareModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.HUJIA then
		bundle, asset = ResPath.GetWelfareModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.LINGCHONG then
		bundle, asset = ResPath.GetPetModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.ZUOQI then
		bundle, asset = ResPath.GetMountModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.BAOSHI then
		bundle, asset = ResPath.GetWelfareModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.FABAO then
		bundle, asset = ResPath.GetFaBaoModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.NPC then
		bundle, asset = ResPath.GetNpcModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.SHENBING or select_data.res_type == RES_TYPE.WUQI then
		local weapon_res_id = RoleWGData.GetFashionWeaponId(nil, nil, select_data.model_id)
		bundle, asset = ResPath.GetWeaponModelRes(weapon_res_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.SHIZHUANG then
		self:FlushFashionModel(select_data.model_id)
	elseif select_data.res_type == RES_TYPE.YUYI then
		bundle, asset = ResPath.GetWingModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.TIANSHEN then
		bundle, asset = ResPath.GetBianShenModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.JIANLING then
		bundle, asset = ResPath.GetJianZhenModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.KUN then
		bundle, asset = ResPath.GetMountModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.ZHUJI then
		self:ShowFootModel(select_data.model_id)
	elseif select_data.res_type == RES_TYPE.MASK then
		self:FlushMaskModel(select_data.model_id)
	elseif select_data.res_type == RES_TYPE.BELT then
		self:FlushBeltModell(select_data.model_id)
	elseif select_data.res_type == RES_TYPE.WEIBA then
		self:FlushWeiBaModell(select_data.model_id)
	elseif select_data.res_type == RES_TYPE.SHOUHUAN then
		self:FlushShouhuanaModell(select_data.model_id)
	elseif select_data.res_type == RES_TYPE.MONSTER then
		bundle, asset = ResPath.GetMonsterModel(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	elseif select_data.res_type == RES_TYPE.BEASTS then
		local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(select_data.model_id)
		bundle, asset = ResPath.GetBeastsModel(res_id)
		self.model_display:SetMainAsset(bundle, asset)
	-- elseif select_data.res_type == RES_TYPE.MINGQI then
	-- 	local res_id = ArtifactWGData.Instance:GetArtifactCfgByItemId(select_data.model_id).model_id
	-- 	local bundle, asset = ResPath.GetShuangXiuTipUI(res_id)
	-- 	--self.model_display:SetMainAsset(bundle, asset)
	-- 	self:SetSpineCell(bundle, asset)
	elseif select_data.res_type == RES_TYPE.SOULRING then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		self.model_display:SetModelResInfo(role_vo, special_status_table)

		local target_data = {}
        if select_data.soul_ring_id and "" ~= select_data.soul_ring_id then
			local soul_ring_id_list = string.split(select_data.soul_ring_id, "|")
            for k, v in pairs(soul_ring_id_list) do
                local cfg = ShenShouWGData.Instance:GetShenShouCfg(tonumber(v))
                target_data[k - 1] = {soul_ring_effect = cfg.soul_ring_effect}
            end
            self.model_display:SetTotalSoulRingResid(target_data, false, #soul_ring_id_list)
		end
	elseif select_data.res_type == RES_TYPE.TIANSHENWUQI then
		bundle, asset = ResPath.GetTianShenShenQiPath(select_data.model_id)
		self.model_display:SetMainAsset(bundle, asset)
	end

	if self.model_display then
		if select_data.res_type == RES_TYPE.ZHUJI then
			self.model_display:PlayRoleAction("run")
		elseif select_data.res_type == RES_TYPE.BEASTS then
			self.model_display:PlayRoleAction(SceneObjAnimator.Rest, nil, true)
		else
			self.model_display:PlayLastAction()
		end
	end
end

--处理时装模型 + 剑
function SevenDayView:FlushFashionModel(model_id)
	local res_id, weapon_res_id = AppearanceWGData.GetFashionBodyResIdByResViewId(model_id)
	local extra_role_model_data = {
		weapon_res_id = weapon_res_id,
    }
	self.model_display:SetRoleResid(res_id, nil, extra_role_model_data)
	self.model_display:SetWeaponResid(weapon_res_id)
end

--脸饰
function SevenDayView:FlushMaskModel(model_id)
	local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
	local extra_role_model_data = {
		weapon_res_id = weapon_res_id,
    }
	self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	self.model_display:SetMaskResid(model_id)
end

-- 腰饰
function SevenDayView:FlushBeltModell(model_id)
	local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
	local extra_role_model_data = {
		weapon_res_id = weapon_res_id,
    }
	self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	self.model_display:SetWaistResid(model_id)
end

-- 尾巴
function SevenDayView:FlushWeiBaModell(model_id)
	local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
	local extra_role_model_data = {
		weapon_res_id = weapon_res_id,
    }
	self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	self.model_display:SetTailResid(model_id)
	self.model_display:SetRotation(MODEL_ROTATION_TYPE.WEIBA)
end

-- 手环
function SevenDayView:FlushShouhuanaModell(model_id)
	local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
	local extra_role_model_data = {
		weapon_res_id = weapon_res_id,
    }
	self.model_display:SetRoleResid(role_res_id, nil, extra_role_model_data)
	self.model_display:SetShouHuanResid(model_id)
end

function SevenDayView:ShowFootModel(res_id)
	local role_res_id, weapon_res_id = AppearanceWGData.Instance:GetRoleResId()
	local bundle, asset = ResPath.GetRoleModel(role_res_id)
	self.model_display:SetMainAsset(bundle, asset)
	self.model_display:SetWeaponResid(weapon_res_id)
	self.model_display:SetRotation(MODEL_ROTATION_TYPE.FOOT)
	self.model_display:SetFootTrailModel(res_id)
end

function SevenDayView:SetSpineCell(bundle, asset)
	if not self.spine_loader then
		local spine_loader = AllocAsyncLoader(self, "base_tip_spine_cell")
		spine_loader:SetIsUseObjPool(true)
		spine_loader:SetParent(self.node_list["spine_cell_root"].transform)
		self.spine_loader = spine_loader
	end

	self.spine_loader:Load(bundle, asset)
end

function SevenDayView:SetRect(node, display, select_data)
	if display then
		local pos_str = select_data.whole_display_pos
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			RectTransform.SetAnchoredPositionXY(node.rect, pos[1] or 0, pos[2] or 0)
		end

		pos_str = select_data.position
		if pos_str and pos_str ~= "" then
			local pos = Split(pos_str, "|")
			display:SetRTAdjustmentRootLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
		end

		local rotate_str = select_data.rotation
		if rotate_str and rotate_str ~= "" then
			local rot = Split(rotate_str, "|")
			display:SetRTAdjustmentRootLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
		end

		local scale = select_data.scale
		if scale and scale ~= "" then
			display:SetRTAdjustmentRootLocalScale(scale)
		end
	else
		if select_data.position and "" ~= select_data.position then
			local position_tab = string.split(select_data.position, "|")
			RectTransform.SetAnchoredPositionXY(node.rect, tonumber(position_tab[1]), tonumber(position_tab[2]))
		end

		if select_data.rotation and "" ~= select_data.rotation then
			local rotation_tab = string.split(select_data.rotation, "|")
			node.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
		end

		if select_data.scale and "" ~= select_data.scale then
			RectTransform.SetLocalScale(node.rect, select_data.scale)
		end
	end
end

function SevenDayView:FlushBackground(background_id)
	if background_id then
		local asset, bundle = nil, nil
		local back_data = BackgroundWGData.Instance:GetBigDataByID(background_id)

		self.node_list["background_root"]:SetActive(background_id ~= 0 and back_data ~= nil)

		if background_id ~= 0 and back_data then
			asset, bundle = ResPath.BackgroundShow(back_data.item_id)

			if not self.background_loader then
				local background_loader = AllocAsyncLoader(self, "base_tip_back_cell")
				background_loader:SetIsUseObjPool(true)
				background_loader:SetParent(self.node_list["background_root"].transform)
				self.background_loader = background_loader
			end
			self.background_loader:Load(asset, bundle)
		end
	else
		self.node_list["background_root"]:SetActive(false)
	end
end

----------------------------------------SevenDayItemRender----------------------------------------
SevenDayItemRender = SevenDayItemRender or BaseClass(BaseRender)

function SevenDayItemRender:LoadCallBack()
	self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
	self.item_list:SetStartZeroIndex(true)

	XUI.AddClickEventListener(self.node_list.receive_award_btn, BindTool.Bind1(self.OnClickGetReward, self))
end

function SevenDayItemRender:ReleaseCallBack()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function SevenDayItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local cur_day = self.data.login_daycount

	local res_name = self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU and "seven_item_day_s_" or "seven_item_day_q_"
	local b1, a1 = ResPath.GetNoPackPNG(res_name .. cur_day)
	self.node_list.day_img.image:LoadSprite(b1, a1, function()
		self.node_list.day_img.image:SetNativeSize()
	end)

	local get_text = ""
	local bg_res = "a3_xsdlb_di_wlq"
	if self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU then
		bg_res = "a3_xsdlb_di_ylq"
	elseif self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU then
		bg_res = "a3_xsdlb_di_klq"
	elseif self.data.status == SEVEN_COMPLETE_STATUS.MingRiKeLingQu or self.data.status == SEVEN_COMPLETE_STATUS.WEIDACHENG then
		bg_res = "a3_xsdlb_di_wlq"
		get_text = self.data.status == SEVEN_COMPLETE_STATUS.MingRiKeLingQu and Language.LingYuanLiBao.MingRiKeLingQu
			or string.format(Language.LingYuanLiBao.CanGet, cur_day)
	end
	local b2, a2 = ResPath.GetRawImagesPNG(bg_res)
	self.node_list.bg.raw_image:LoadSprite(b2, a2, function()
		self.node_list.bg.raw_image:SetNativeSize()
	end)

	self.node_list.next_text.text.text = get_text

	self.node_list.get_flag:SetActive(self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU)
	self.node_list.receive_award_btn:SetActive(self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU)

	local pos_y = self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU and -17 or -50
	RectTransform.SetAnchoredPositionXY(self.node_list.item_list.rect, 0, pos_y)

	local items = WelfareWGData.Instance:GetSevenDayGiftItems(cur_day)
	if items then
		self.item_list:SetRefreshCallback(function(item_cell, cell_index)
			if item_cell then
				item_cell:SetLingQuVisible(self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU)
				item_cell:SetRedPointEff(self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU)
				item_cell:SetFlushCallBack(function ()
					item_cell:SetLingQuVisible(self.data.status == SEVEN_COMPLETE_STATUS.YILINGQU)
					item_cell:SetRedPointEff(self.data.status == SEVEN_COMPLETE_STATUS.KELINGQU)
				end)
			end
		end)
		self.item_list:SetDataList(items)
	end
end

function SevenDayItemRender:OnClickGetReward()
	if IsEmptyTable(self.data) then
		return
	end

	WelfareWGCtrl.Instance:SendFetchSevenDayLoginReward(self.data.login_daycount)
	AudioService.Instance:PlayRewardAudio()
end
