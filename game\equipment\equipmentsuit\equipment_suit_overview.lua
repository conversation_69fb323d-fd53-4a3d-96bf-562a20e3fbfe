EquipmentSuitOverview = EquipmentSuitOverview or BaseClass(SafeBaseView)

function EquipmentSuitOverview:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 14),sizeDelta = Vector2(1080, 614)})
	self:AddViewResource(0, "uis/view/equipment_suit_ui_prefab", "layout_equipment_suit_overview")
end

function EquipmentSuitOverview:LoadCallBack()
    if not self.equipment_suit_equip_body_list then
        self.equipment_suit_equip_body_list = AsyncListView.New(TZEquipBodyOverviewItemCellRender, self.node_list.equipment_suit_equip_body_list)
    end

    self.node_list.title_view_name.text.text = Language.Equip.StrengthOverviewTitle
end

function EquipmentSuitOverview:ReleaseCallBack()
    if self.equipment_suit_equip_body_list then
        self.equipment_suit_equip_body_list:DeleteMe()
        self.equipment_suit_equip_body_list = nil
    end
end

function EquipmentSuitOverview:OnFlush()
    local data_list = EquipBodyWGData.Instance:GetTotalEquipBodyDataList()
    self.equipment_suit_equip_body_list:SetDataList(data_list)
end

-------------------------------------TZEquipBodyOverviewItemCellRender--------------------------------------
TZEquipBodyOverviewItemCellRender = TZEquipBodyOverviewItemCellRender or BaseClass(BaseRender)

function TZEquipBodyOverviewItemCellRender:LoadCallBack()
	if not self.equip_qh_list then
	    self.equip_qh_list = {}
	    for part = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
	        self.equip_qh_list[part] = TZEquipBodyOverviewEquipCellRender.New(self.node_list["item_" .. part])
	        self.equip_qh_list[part]:SetIndex(part)
            self.equip_qh_list[part]:SetClickCallBack(BindTool.Bind(self.OnClickEquipSuitEquipCallBack, self))
	    end
    end

    self.default_select_part = -1
    self.equip_body_seq_cache = -1
    self.default_select_part_data = {}

    XUI.AddClickEventListener(self.node_list["btn_to_equip_suit"], BindTool.Bind(self.OnClickToEquipSuitBtn, self)) 
end

function TZEquipBodyOverviewItemCellRender:__delete()
    if self.equip_qh_list then
        for k, v in pairs(self.equip_qh_list) do
            v:DeleteMe()
        end

        self.equip_qh_list = nil
    end

    self.default_select_part = nil
    self.equip_body_seq_cache = nil
    self.default_select_part_data = nil
end

function TZEquipBodyOverviewItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    if self.equip_body_seq_cache ~= self.data.seq then
        self.default_select_part = -1
        self.default_select_part_data = {}
    end

    self.equip_body_seq_cache = self.data.seq

    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    self.node_list.equip_suit_cell:CustomSetActive(unlock and is_wear_equip)
    self.node_list.unlock:CustomSetActive(not unlock or not is_wear_equip)
    self.node_list.name.text.text = self.data.name

    if unlock and is_wear_equip then
        local equip_data_list = EquipmentWGData.Instance:GetSuitEquipBodyEquipDataList(self.data.seq)
        for k,v in pairs(self.equip_qh_list) do
            v:SetData(equip_data_list[k])
        end

        self:SetDefaultSelect(equip_data_list)
    else
        self.node_list.desc_tip.text.text = not unlock and Language.RoleEquipBody.EquipBodyLock or Language.RoleEquipBody.EquipBodyNotWearEquip
    end
end

function TZEquipBodyOverviewItemCellRender:SetDefaultSelect(equip_data_list)
    if self.default_select_part < 0 and not IsEmptyTable(equip_data_list) then
        for k, v in pairs(equip_data_list) do
            if not IsEmptyTable(v) then
                if v.item_id > 0 then
                    self:OnClickEquipSuitEquipCallBack(self.equip_qh_list[k], true)
                    return
                end
            end
        end
    end
end

function TZEquipBodyOverviewItemCellRender:OnClickEquipSuitEquipCallBack(cell, is_default_select)
    if nil == cell or IsEmptyTable(cell.data) then
        return 
    end

    local equip_part = cell.index 
    local data = cell.data

    if self.default_select_part == equip_part then
        if not is_default_select then
            TipWGCtrl.Instance:OpenItem({item_id = data.item_id})
        end
    end

    self:SetSelectEquip(cell.index)
    self.default_select_part = equip_part
    self.default_select_part_data = data
end

function TZEquipBodyOverviewItemCellRender:SetSelectEquip(equip_part)
    if self.equip_qh_list then
        for k, v in pairs(self.equip_qh_list) do
            v:OnSelectChange(v.index == equip_part)
        end
    end
end

function TZEquipBodyOverviewItemCellRender:OnClickToEquipSuitBtn()
    local unlock = EquipBodyWGData.Instance:IsEquipBodyUnLock(self.data.seq)
    local is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyWearEquip(self.data.seq)

    if unlock and is_wear_equip then
        EquipmentWGCtrl.Instance:Flush(TabIndex.equipment_suit, "equip_suit_change_to_equip_body", {equip_body_seq = self.data.seq, selct_part_data = self.default_select_part_data})
        EquipmentWGCtrl.Instance:CloseEquipSuitOverviewView()
    else
        if not unlock then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyLock)
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyNotWearEquip)
        end
    end
end

----------------------------------TZEquipBodyOverviewEquipCellRender------------------------------------
TZEquipBodyOverviewEquipCellRender = TZEquipBodyOverviewEquipCellRender or BaseClass(BaseRender)

function TZEquipBodyOverviewEquipCellRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_node)
	self.item_cell:SetIsShowTips(false)

	self.node_list.select_img:SetActive(false)
    -- self.node_list.block_click:SetActive(false)
    self.node_list.remind:SetActive(false)
	self:OnSelectChange(false)
	XUI.AddClickEventListener(self.node_list.block_click, BindTool.Bind(self.OnClick, self))
end

function TZEquipBodyOverviewEquipCellRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TZEquipBodyOverviewEquipCellRender:OnSelectChange(is_select)
    if self.node_list.select_img then
        self.node_list.select_img:CustomSetActive(is_select)
    end
end

function TZEquipBodyOverviewEquipCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.item_cell:ClearData()
		self.item_cell:SetItemIcon(ResPath.GetEquipIcon(self.index))
		self:NoDataFlush()
		return
	end

	self.item_cell:SetData(self.data)
    self.item_cell:SetRightTopImageTextActive(false)
	self:OtherFlush()
end

function TZEquipBodyOverviewEquipCellRender:OtherFlush()
    local is_dz = EquipmentWGData.Instance:GetEquipSuitOrderFlagByIndex(self.data.index) > 0
    self.node_list.active_suit_flag:CustomSetActive(is_dz)
end

function TZEquipBodyOverviewEquipCellRender:NoDataFlush()
    self.node_list.active_suit_flag:CustomSetActive(false)
    self:OnSelectChange(false)
end