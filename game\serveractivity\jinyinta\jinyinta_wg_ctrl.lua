require("game/serveractivity/jinyinta/jinyinta_wg_data")
require("game/serveractivity/jinyinta/jinyinta_view")
require("game/serveractivity/jinyinta/jinyinta_show_view")
require("game/serveractivity/jinyinta/jinyinta_exchange_view")
JinyintaWGCtrl = JinyintaWGCtrl or BaseClass(BaseWGCtrl)

function JinyintaWGCtrl:__init()
	if JinyintaWGCtrl.Instance ~= nil then
		print("[JinyintaWGCtrl]error:create a singleton twice")
	end
	JinyintaWGCtrl.Instance = self

	self.view = JinyintaView.New(GuideModuleName.JinyintaView)
	self.data = JinyintaWGData.New()
	self.show_view = JinyintaShowView.New()
	self.exchange_view = JinyintaExchangeView.New()

	-- self:RegisterAllProtocols()
end

function JinyintaWGCtrl:__delete()
	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	if nil ~= self.show_view then
		self.show_view:DeleteMe()
		self.show_view = nil
	end
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	if nil ~= self.exchange_view then
		self.exchange_view:DeleteMe()
		self.exchange_view = nil
	end

	JinyintaWGCtrl.Instance = nil
end

function JinyintaWGCtrl:RegisterAllProtocols()
	-- self:RegisterProtocol(SCRALevelLotteryInfo, "OnRALevelLotteryInfo")
	-- self:RegisterProtocol(SCRALevelLotteryRewardList, "OnRALevelLotteryRewardList")
	self:RegisterProtocol(SCRALevelLotteryOnLineTimeInfo,"OnRALevelLotteryOnLineTimeInfo")
	self:RegisterProtocol(SCXunBaoScoreInfo,"OnSCXunBaoScoreInfo")
	self:RegisterProtocol(CSXunBaoScoreOpera)

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.SendJinYinTaReq, self))
	-- Remind.Instance:RegisterOneRemind(RemindId.jinyinta, BindTool.Bind1(self.CheckJinYinTaRemind, self))
end

function JinyintaWGCtrl:SendJinYinTaReq()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_JINYINTA) then
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_JINYINTA, opera_type = RendActOperaType.ONLINE_TIME_INFO})
		local param_t = {rand_activity_type = ACTIVITY_TYPE.RAND_JINYINTA, opera_type = RendActOperaType.QUERY_INFO}
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
		JinyintaWGCtrl.Instance:SendXunbaoReq(0)
	end
end

function JinyintaWGCtrl:CheckJinYinTaRemind()
	-- if self.data:GetFreeTimes() > 0 then
	-- 	return 1
	-- else
	-- 	return 0
	-- end
	return self.data:GetJinYinTaRemind()
end

function JinyintaWGCtrl:Open(index, param_t)
	local isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_JINYINTA)
	if not isopen then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
		return
	end
	self.view:Open(index)
	self.data:SetXunBaoScoreCfg()
	self.exchange_view:Flush()
end

function JinyintaWGCtrl:OnRALevelLotteryInfo(protocol)
	self.data:SetCurLayer(protocol.lottery_cur_level)
	self.data:SetHistoryList(protocol.history_list)
	self.view:Flush()
	-- Remind.Instance:DoRemind(RemindId.jinyinta)
end

function JinyintaWGCtrl:OnSCXunBaoScoreInfo(protocol)
	self.data:SetXunBaoScore(protocol.xunbao_score)
	self.view:Flush()
	self.exchange_view:Flush()
end

function JinyintaWGCtrl:OnRALevelLotteryRewardList(protocol)
	local list = {}
	for k,v in pairs(protocol.lottery_reward_list) do
		table.insert(list, self.data:GetOneShowItem(v))
	end
	list = self:SortList(list)
	list[0] = table.remove(list, 1)
	self.show_view:SetDataList(list)
end

function JinyintaWGCtrl:SortList(list)
	local temp_list = {}
	for k,v in pairs(list) do
		local cfg = __TableCopy(v)
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		cfg.color = item_cfg.color
		table.insert(temp_list, cfg)
	end
	table.sort(temp_list, SortTools.KeyUpperSorter("color"))
	return temp_list
end

function JinyintaWGCtrl:OpenExchange()
	self.exchange_view:Open()
end

function JinyintaWGCtrl:OnRALevelLotteryOnLineTimeInfo(protocol)
	self.data:SetFreeTimes(protocol.freetimes)
	self.data:SetNextFreeRemainTime(protocol.next_free_remain_time)
	self.data:SetXunBaoScore(protocol.xunbao_score)
	-- Remind.Instance:DoRemind(RemindId.jinyinta)
	self.view:Flush()
	self.exchange_view:Flush()
end

function JinyintaWGCtrl:SendXunbaoReq(operate, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSXunBaoScoreOpera)
	protocol.req_type = operate or 0
	protocol.param_1 = param1 or 0
	protocol.param_2 = param2 or 0
	protocol:EncodeAndSend()
end

function JinyintaWGCtrl:OnViewClickDraw(count)
	if self.view:IsOpen() and count > 0 then
		self.view:OnClickDraw(count)
	end
end