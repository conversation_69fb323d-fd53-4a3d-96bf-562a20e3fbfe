HongHuangGoodCoremonyView = HongHuangGoodCoremonyView or BaseClass(SafeBaseView)

local TWEEN_START_POS = {
	[1] = Vector2(0, 800),
	[2] = Vector2(0, -800),
	[3] = Vector2(0, 800),
	[4] = Vector2(0, -800),
}

function HongHuangGoodCoremonyView:__init()
	self:SetMaskBg(false, true)
	self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:AddViewResource(0, "uis/view/honghuang_good_ceremony_ui_prefab", "honghuang_good_ceremony_view")
end

function HongHuangGoodCoremonyView:OpenCallBack()
	HongHuangGoodCoremonyWGCtrl.Instance:SendHongHuangGoodCoremonyReq(CHAOTIC_GIFT_OPERATE_TYPE
	.CHAOTIC_GIFT_OPERATE_TYPE_GIFT_INFO)
end

function Hong<PERSON>uangGoodCoremonyView:DoShowTween()
	local tween_info = UITween_CONSTS.AchievementSys
	for i = 1, 4 do
		self.node_list["tween_root1_" .. i].rect.anchoredPosition = TWEEN_START_POS[i]
	end
	self.node_list.alpha_root.canvas_group.alpha = 0

	for i = 1, 4 do
		self.node_list["tween_root1_" .. i].rect:DOAnchorPos(Vector2.zero, 0.5)
	end

	ReDelayCall(self, function()
		self.node_list.alpha_root.canvas_group:DoAlpha(0, 1, 0.5)
	end, 0.5, "honghuang_good_ceremony_view")
end

function HongHuangGoodCoremonyView:LoadCallBack()
	if not self.good_ceremony_item_list then
		self.good_ceremony_item_list = {}
		for i = 1, 4 do
			local render = GoodCeremonyItemRender.New(self.node_list["good_ceremony_item" .. i].gameObject)
			render:SetIndex(i)
			self.good_ceremony_item_list[i] = render
		end
	end

	self.node_list.desc.text.text = Language.HongHuangGoodCeremony.ActiveDesc
end

function HongHuangGoodCoremonyView:ReleaseCallBack()
	if self.good_ceremony_item_list then
		for k, v in pairs(self.good_ceremony_item_list) do
			v:DeleteMe()
		end
		self.good_ceremony_item_list = nil
	end
end

function HongHuangGoodCoremonyView:ShowIndexCallBack()
	self:DoShowTween()
	self.current_grade = HongHuangGoodCoremonyWGData.Instance:GetCurrentShowSeq()
end

function HongHuangGoodCoremonyView:CloseCallBack()
	self.current_grade = nil
end

function HongHuangGoodCoremonyView:OnFlush()
	local data_list = HongHuangGoodCoremonyWGData.Instance:GetChaoticGifDataList(self.current_grade)
	if IsEmptyTable(data_list) then
		return
	end

	for k, v in pairs(self.good_ceremony_item_list) do
		v:SetData(data_list[k])
	end
end

------------------------------------------------------------------------------------------------
GoodCeremonyItemRender = GoodCeremonyItemRender or BaseClass(BaseRender)
function GoodCeremonyItemRender:__init()
	self.model_show_itemid = -1
end

function GoodCeremonyItemRender:__delete()
	if self.ph_reward_list then
		self.ph_reward_list:DeleteMe()
		self.ph_reward_list = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	self.model_show_itemid = nil
end

function GoodCeremonyItemRender:LoadCallBack()
	if not self.ph_reward_list then
		self.ph_reward_list = AsyncBaseGrid.New()
		self.ph_reward_list:CreateCells({ col = 3, change_cells_num = 1, list_view = self.node_list.ph_reward_list })
		self.ph_reward_list:SetStartZeroIndex(true)
	end

	if nil == self.model_display then
		self.model_display = OperationActRender.New(self.node_list.display_root)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	XUI.AddClickEventListener(self.node_list.btn_bug, BindTool.Bind(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.display_click, BindTool.Bind(self.OnClickShowTips, self))
end

function GoodCeremonyItemRender:OnFlush()
	if IsEmptyTable(self:GetData()) then
		return
	end
	local data = self:GetData()
	self.ph_reward_list:SetDataList(data.reward_item)

	self.node_list.name_text.text.text = data.name
	self.node_list.cost_text.text.text = data.need_gold
	local get_flag = HongHuangGoodCoremonyWGData.Instance:GetChaoticGifInfo(data.seq)
	self.node_list.btn_text.text.text = get_flag and Language.HongHuangGoodCeremony.BtnStatePurchased or
	Language.HongHuangGoodCeremony.BtnStateBugNow
	XUI.SetButtonEnabled(self.node_list.btn_bug, not get_flag)

	if self.model_show_itemid ~= data.model_show_itemid then
		self:FlushModel()
	end
end

function GoodCeremonyItemRender:FlushModel()
	local data = self:GetData()
	local display_data = {}
	if data.model_show_itemid ~= 0 and data.model_show_itemid ~= "" then
		local split_list = string.split(data.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = data.model_show_itemid
		end
		self.model_show_itemid = data.model_show_itemid
	end

	display_data.should_ani = true
	display_data.bundle_name = data.model_bundle_name
	display_data.asset_name = data.model_asset_name
	local model_show_type = tonumber(data.model_show_type) or 1
	display_data.render_type = model_show_type - 1
	-- display_data.model_click_func = function ()
	-- 	TipWGCtrl.Instance:OpenItem({item_id = data.model_show_itemid})
	-- end
	-- self.node_list.display_click:SetActive(model_show_type ~= 1)
	self.model_display:SetData(display_data)
	local scale = data.display_scale
	Transform.SetLocalScaleXYZ(self.node_list.display_root.transform, scale, scale, scale)
end

function GoodCeremonyItemRender:OnClickBuyBtn()
	local data = self.data
	if not IsEmptyTable(data) and data.seq then
		HongHuangGoodCoremonyWGCtrl.Instance:SendHongHuangGoodCoremonyReq(
		CHAOTIC_GIFT_OPERATE_TYPE.CHAOTIC_GIFT_OPERATE_TYPE_BUY_GIFT, data.seq)
	end
end

function GoodCeremonyItemRender:OnClickShowTips()
	local data = self:GetData()
	if not IsEmptyTable(data) and data.model_show_itemid ~= 0 and data.model_show_itemid ~= "" then
		TipWGCtrl.Instance:OpenItem({ item_id = data.model_show_itemid })
	end
end
