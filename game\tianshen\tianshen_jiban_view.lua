-- 已屏蔽
TianShenJiBanView = TianShenJiBanView or BaseClass(SafeBaseView)

function TianShenJiBanView:__init()
	self:SetMaskBg()
	 self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_tianshen_jiban_view")
	self.view_name = GuideModuleName.TianShenJiBanView
end

function TianShenJiBanView:__delete()
end

function TianShenJiBanView:ReleaseCallBack()
	if self.jiban_list then
        self.jiban_list:DeleteMe()
        self.jiban_list = nil
    end
end

function TianShenJiBanView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.TianShen.JiBanName
    self:SetSecondView(nil, self.node_list["size"])
    self.jiban_list = AsyncListView.New(TSJiBanViewRender, self.node_list.jiban_list)
end

function TianShenJiBanView:ShowIndexCallBack()

end

function TianShenJiBanView:OnFlush(prarm_t)
	local data_list = TianShenWGData.Instance:GetTianShenAllJiBan()
    self.jiban_list:SetDataList(data_list)
end

-- 羁绊render
TSJiBanViewRender = TSJiBanViewRender or BaseClass(BaseRender)
function TSJiBanViewRender:__init()
 	self.cur_select = 1 --默认第一个
end

function TSJiBanViewRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.ts_head1, BindTool.Bind(self.ClickHead, self, 1))
	XUI.AddClickEventListener(self.node_list.ts_head2, BindTool.Bind(self.ClickHead, self, 2))
end

function TSJiBanViewRender:__delete()

end

function TSJiBanViewRender:OnFlush()
	if not self.data then return end
	for i=1, 2 do
		local tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data["index"..i])
		if tianshen_cfg then
			local bundle, asset = ResPath.GetItem(tianshen_cfg.head_id)
			self.node_list["item_icon_"..i].image:LoadSprite(bundle, asset, function ()
			self.node_list["item_icon_"..i].image:SetNativeSize()
			end)
		end
		local act_flag = TianShenWGData.Instance:IsActivation(self.data["index"..i])
		XUI.SetGraphicGrey(self.node_list["head_bg_"..i], not act_flag)
		self.node_list["hight_img_"..i]:SetActive( i == self.cur_select)
	end
	local skill_info = TianShenWGData.Instance:GetTianShensMainkill(self.data["index".. self.cur_select], self.data["jiban_id".. self.cur_select])
	local skill_level = skill_info and skill_info.level or 1
	local ts_skill_cfg = SkillWGData.Instance:GetTianShenSkillCfg(self.data["jiban_id".. self.cur_select], skill_level)
	local skill_name = ""
	if ts_skill_cfg then
		skill_name = ts_skill_cfg.skill_name
	end
	local cur_tianshen_cfg = TianShenWGData.Instance:GetTianShenCfg(self.data["index".. self.cur_select])
	XUI.SetGraphicGrey(self.node_list.desc_text, not TianShenWGData.Instance:IsActivation(self.data["index".. self.cur_select]))
	self.node_list.desc_text.text.text = string.format(Language.TianShen.TianShenTips8, ToColorStr(skill_name,TIANSHEN_COLOR3B[cur_tianshen_cfg.series]), 
		self.data["des".. self.cur_select])
end

function TSJiBanViewRender:ClickHead(index)
	self.cur_select = index
	self:OnFlush()
end