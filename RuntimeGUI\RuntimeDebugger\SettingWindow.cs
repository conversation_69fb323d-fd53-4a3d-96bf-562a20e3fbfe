﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public partial class DebugRuntimeGUI
{
    public class SettingWindow : IDebugWindow
    {
        private DebugRuntimeGUI m_DebugGui;
        private float m_LastWindowScale = 1f;

        public void Init()
        {
            m_DebugGui = RuntimeGUIMgr.Instance.GetDebugGUI();
            m_LastWindowScale = m_DebugGui.WindowScale = PlayerPrefHelper.GetFloat("debug_runtime_gui_window_scale", 1f);
        }

        public void Destroy()
        {
            m_DebugGui = null;
        }

        public void OnEnter()
        {
        }

        public void OnLeave()
        {
        }

        public void OnDraw()
        {    
            GUILayout.Space(10);
            GUILayout.BeginHorizontal();
            {
                if (GUILayout.Button("Scale X 1", GUILayout.Width(100f), GUILayout.Height(40f)))
                {
                    m_DebugGui.WindowScale = 1f;
                }

                if (GUILayout.Button("Scale X 0.9", GUILayout.Width(100f), GUILayout.Height(40f)))
                {
                    m_DebugGui.WindowScale = 0.9f;
                }

                if (GUILayout.Button("Scale X 0.8", GUILayout.Width(100f), GUILayout.Height(40f)))
                {
                    m_DebugGui.WindowScale = 0.8f;
                }

                if (GUILayout.Button("Scale X 0.7", GUILayout.Width(100f), GUILayout.Height(40f)))
                {
                    m_DebugGui.WindowScale = 0.7f;
                }
            }
            GUILayout.EndHorizontal();
            m_DebugGui.WindowScale = GUILayout.HorizontalSlider(m_DebugGui.WindowScale, 0.7F, 1F, GUILayout.MaxWidth(500), GUILayout.Height(100));
        }

        public void Update()
        {
            if (m_LastWindowScale != m_DebugGui.WindowScale)
            {
                PlayerPrefHelper.SetFloat("debug_runtime_gui_window_scale", m_DebugGui.WindowScale);
                m_LastWindowScale = m_DebugGui.WindowScale;
            }
        }
    }
}