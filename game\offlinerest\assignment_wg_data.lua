AssignmentWGData = AssignmentWGData or BaseClass()

AssignmentWGData.ASSIGN_GUIDE_ID = 30 --引导表的id

AssignmentWGData.MAX_TASK_CONDITION = 3 --任务最大条件数
AssignmentWGData.MAX_SPECIAL_TASK= 3 --最大特殊任务数
AssignmentWGData.MAX_TASK_NUM = 25 --最大任务数

AssignmentWGData.TASK_STATUS = {
	NORMAL = 1,--任务未派遣
	ASSIGN = 2,--任务已派遣
	COMPLIT = 3--任务已完成并领取
}

AssignmentWGData.FASHION_TYPE_MAX = 4
AssignmentWGData.FASHION_TYPE = {
	RENT = 0,--雇佣的外形type固定为4(发派遣协议时)
	PET = 1,
	MOUNT = 2,
	TIANSHEN = 3,
	HUANSHOU = 4,
}

AssignmentWGData.ASSIGN_TAB_TYPE_MAX = 2
AssignmentWGData.ASSIGN_TAB_TYPE = {
	MY = 1,
	OTHER = 2,
}

AssignmentWGData.GUILD_ASSIGN_STATUS = {
	RENT = 0,--0:上架未被雇佣
	RENT_ASSIGN = 1,--1:上架且被雇佣
}

function AssignmentWGData.GetFashionCfgModel()--委托任务外形配置数据模型
	return {
		type = 0,--FASHION_TYPE
		seq = -1,--外形自己表里的index
		item_id = 0,--激活道具ID
		color = 0,
		name = "",--外形名字
	}
end

function AssignmentWGData.GetFashionDataModel()--外形数据模型
	return {
		cfg = nil,--GetFashionCfgModel
		--上架相关数据
		owner_name = "",
		status = -1,--0:上架未被雇佣 1:上架且被雇佣
		begin_time_stamp = 0,--上架开始时间
		self_index = -1,--自己已上架外形的唯一id
		guild_index = -1,--工会已上架外形的唯一id
		is_my_fashion = 0 -- 是否自己的外形 0是别人的 1是自己的
	}
end

function AssignmentWGData.GetTaskDataModel()--委托任务数据模型
	return {
		cfg = nil,--task_cfg
		begin_time_stamp = 0,--派遣开始时间
		my_assign_index_list = nil,--任务里自己派遣的外形下标列表
		brrow_assign_list = nil,--租借外形信息
	}
end


function AssignmentWGData:__init()
	if AssignmentWGData.Instance ~= nil then
		ErrorLog("[AssignmentWGData] Attemp to create a singleton twice !")
	end
	AssignmentWGData.Instance = self

	local cfg = ConfigManager.Instance:GetAutoConfig("consignation_task_auto")
	if cfg then
		self.task_cfg = ListToMap(cfg.task, "id")
		self.special_task_cfg = ListToMap(cfg.special_task, "id")
		self.exp_generator_cfg = cfg.exp_generator
		self.other_cfg = cfg.other
	end

	self:InitAllFashionCfgList()
	self.my_fashion_map = {}
	self.my_assign_data_map = {}
	self.task_data_list = {}
	RemindManager.Instance:Register(RemindName.RemindAssigment, BindTool.Bind(self.GetAssignRemind, self))			-- 红点
end

function AssignmentWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.RemindAssigment)
	self.my_fashion_map = {}
	self.my_assign_data_map = {}
	self.task_data_list = {}
	AssignmentWGData.Instance = nil
end

---------------------------------------------------------------cfg start--------------------------------------------------------
function AssignmentWGData:InitAllFashionCfgList()
	self.all_fashion_cfg_map = {}

	
	--坐骑
	local cfg = ConfigManager.Instance:GetAutoConfig("mount_auto").mount_image_active
	local type = AssignmentWGData.FASHION_TYPE.MOUNT
	if self.all_fashion_cfg_map[type] == nil then
		self.all_fashion_cfg_map[type] = {}
	end

	for k, v in ipairs(cfg) do
		local data = self.GetFashionCfgModel()
		data.type = type
		data.seq = v.image_id
		data.item_id = v.active_item_id
		local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
		data.color = item_cfg and item_cfg.color or 0
		data.name = v.image_name

		self.all_fashion_cfg_map[data.type][data.seq] = data
	end


	--灵宠
	cfg = ConfigManager.Instance:GetAutoConfig("lingchong_auto").special_image_active
	type = AssignmentWGData.FASHION_TYPE.PET
	if self.all_fashion_cfg_map[type] == nil then
		self.all_fashion_cfg_map[type] = {}
	end

	for k, v in ipairs(cfg) do
		local data = self.GetFashionCfgModel()
		data.type = type
		data.seq = v.image_id
		data.item_id = v.active_item_id
		local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
		data.color = item_cfg and item_cfg.color or 0
		data.name = v.image_name

		self.all_fashion_cfg_map[data.type][data.seq] = data
	end


	--天神
	cfg = ConfigManager.Instance:GetAutoConfig("tianshen_cfg_auto").item
	-- local magic_image_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("tianshen_cfg_auto").magic_image, "index")
	type = AssignmentWGData.FASHION_TYPE.TIANSHEN
	if self.all_fashion_cfg_map[type] == nil then
		self.all_fashion_cfg_map[type] = {}
	end

	for k, v in ipairs(cfg) do
		local magic_cfg = TianShenWGData.Instance:GetTianShenCfg(v.index)
		if not IsEmptyTable(magic_cfg) then
			local data = self.GetFashionCfgModel()
			data.type = type
			data.seq = v.index
			data.item_id = v.act_item_id
			local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			data.color = item_cfg and item_cfg.color or 0
			data.name = ( magic_cfg or {}).bianshen_name or ""

			self.all_fashion_cfg_map[data.type][data.seq] = data
		end

	end

	-- 幻兽
	cfg = ConfigManager.Instance:GetAutoConfig("beasts_cfg_auto").beasts
	type = AssignmentWGData.FASHION_TYPE.HUANSHOU
	if self.all_fashion_cfg_map[type] == nil then
		self.all_fashion_cfg_map[type] = {}
	end

	for k, v in pairs(cfg) do
		local data = self.GetFashionCfgModel()
		data.type = type
		data.seq = v.beast_id
		data.item_id = v.beast_id
		local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
		data.color = item_cfg and item_cfg.color or 0
		data.name = v.beast_name
		data.beast_cfg = v

		self.all_fashion_cfg_map[data.type][data.seq] = data
	end
end

function AssignmentWGData:GetTaskCfgList()
	local list = {}
	local index = 1
	for k, v in pairs(self.task_data_list) do
		local cfg = nil
		if self:GetHasSpecialTask() then
			cfg = (self.special_task_cfg or {})[v.task_id] or {}
		else
			cfg = self:GetTaskCfgById(v.task_id)
		end
		list[index] = cfg
		index = index + 1
	end

	table.sort(list, function(a, b)
		if self:GetSortPower(a)<= AssignmentWGData.MAX_TASK_NUM and self:GetSortPower(b) <= AssignmentWGData.MAX_TASK_NUM then
			return self:GetSortTime(a,b)
		end
		return self:GetSortPower(a) > self:GetSortPower(b)
	end)
	return list
end

function AssignmentWGData:GetSortTime(task_cfg_1,task_cfg_2)
	local data_1 = self:GetTaskData(task_cfg_1.id)
	local data_2 = self:GetTaskData(task_cfg_2.id)
	return self:GetTaskRestTime(data_1.task_id) < self:GetTaskRestTime(data_2.task_id)
end

function AssignmentWGData:GetSortPower(task_cfg)
	local length = AssignmentWGData.MAX_TASK_NUM
	local data = self:GetTaskData(task_cfg.id)
	if data.begin_time_stamp > 0 and self:GetTaskRestTime(data.task_id) <= 0 then--派遣已完成
		return length * 2 + task_cfg.color 
	elseif data.begin_time_stamp > 0 then--派遣中
		return length * 0 + task_cfg.color 
	else--未派遣
		return length * 1 + task_cfg.color 
	end
end

function AssignmentWGData:GetTaskCfgById(task_id)
	return (self.task_cfg or {})[task_id] or {}
end

--自己的已派遣的外形
function AssignmentWGData:GetMyAssignData(type, seq)
	return ((self.my_assign_data_map or {})[type] or {})[seq]
end

--自己的已上架的外形
function AssignmentWGData:GetMyFashionIsRent(type, seq)
	local rent_data = ((self.my_rent_data_map or {})[type] or {})[seq]
	local is_rent_data = ((self.my_is_rent_map or {})[type] or {})[seq]

	return rent_data ~= nil or is_rent_data ~= nil
end

--获取自己未上架以及未派遣的外形列表
function AssignmentWGData:GetMyAssignFashionDataList(fashion_type)
	local data_list = {}
	if fashion_type == 0 then
		for k, type_list in pairs(self.my_fashion_map) do
			for _, v in pairs(type_list) do
				local cfg = v.cfg
				local is_assign = self:GetMyAssignData(cfg.type, cfg.seq) ~= nil
				local is_rent = self:GetMyFashionIsRent(cfg.type, cfg.seq)
				if not is_assign and not is_rent then
					table.insert(data_list, v)
				end
			end
		end
	elseif self.my_fashion_map[fashion_type] ~= nil then
		for _, v in pairs(self.my_fashion_map[fashion_type]) do
			local cfg = v.cfg
			local is_assign = self:GetMyAssignData(cfg.type, cfg.seq) ~= nil
			local is_rent = self:GetMyFashionIsRent(cfg.type, cfg.seq)
			if not is_assign and not is_rent then
				table.insert(data_list, v)
			end
		end
	end

	table.sort(data_list, function(a, b)
		return a.cfg.color > b.cfg.color
	end)
	return data_list
end

function AssignmentWGData:GetMyRentList()
	return self.my_rent_data_list or {}
end

function AssignmentWGData:GetMyIsRentList()
	return self.my_is_rent_list or {}
end

function AssignmentWGData:GetAssignFashionItemId(fashion_type, seq)
	local cfg = self:GetAssignFashionCfg(fashion_type, seq)
	return cfg.item_id or 0
end

function AssignmentWGData:GetAssignFashionCfg(fashion_type, seq)
	return ((self.all_fashion_cfg_map or {})[fashion_type] or {})[seq] or {}
end

-- function AssignmentWGData:GetOneKeyDataList(task_id)
-- 	local task_cfg = self:GetHasSpecialTask() and self.special_task_cfg or self.task_cfg
-- 	local cfg = (task_cfg or {})[task_id]
-- 	if IsEmptyTable(cfg) then
-- 		return {}
-- 	end

-- 	local all_fashion_list = self:GetMyAssignFashionDataList(0)
-- 	local condition_list = Split(cfg.conditions, "|")
-- 	local type_list = {}
-- 	for k, v in pairs(condition_list) do
-- 		local condition = Split(v, ",")
-- 		type_list[k] = {}
-- 		type_list[k].need_type = tonumber(condition[1])
-- 		type_list[k].need_color = tonumber(condition[2])
-- 	end
-- 	table.sort(type_list, function(a, b)
-- 		return a.need_color > b.need_color
-- 	end)


-- 	local data_list = {}
-- 	for _, fashion in pairs(all_fashion_list) do
-- 		local cfg = fashion.cfg
-- 		local best_color = -1
-- 	 	local index = -1
-- 		for k, v in pairs(type_list) do
-- 			if (cfg.type == v.need_type or v.need_type == 0)
-- 				and cfg.color >= v.need_color
-- 				and v.need_color >= best_color then

-- 					best_color = v.need_color
-- 					index = k
					
-- 			end
-- 		end

-- 		if index > 0 then
-- 			table.insert(data_list, fashion)
-- 			table.remove(type_list, index)
-- 		end

-- 		if #type_list <= 0 then
-- 			break
-- 		end
-- 	end

-- 	return data_list
-- end

function AssignmentWGData:GetOtherCfg()
	return (self.other_cfg or {})[1] or {}
end

function AssignmentWGData:GetMaxTaskExp()
	return self:GetOtherCfg().max_lilian_exp or 1
end

function AssignmentWGData:GetRefreshNeedXianyu()
	return self:GetOtherCfg().refresh_cost_money or 0
end

function AssignmentWGData:GetMaxBorrowNum()
	return self:GetOtherCfg().max_rent_num or 0
end

function AssignmentWGData:GetGuildMaxBorrowNum()
	return self:GetOtherCfg().guild_max_borrow_num or 0
end

-- 获取历练比例
function AssignmentWGData:GetExpGeneratorByNum(battle_num)
	local empty = {}
	return (self.exp_generator_cfg or empty)[battle_num]
end
---------------------------------------------------------------cfg end--------------------------------------------------------

---------------------------------------------------------------协议 start--------------------------------------------------------
function AssignmentWGData:SetMyAssignFashionData(fashion_type, protocol)--更新自己的外形数据
	if self.my_fashion_map[fashion_type] == nil then
		self.my_fashion_map[fashion_type] = {}
	end

	if fashion_type == AssignmentWGData.FASHION_TYPE.MOUNT or fashion_type == AssignmentWGData.FASHION_TYPE.PET then
		for image_id, v in pairs(protocol.special_image_list) do
			if v.grade > 0 then
				local data = self.GetFashionDataModel()
				data.cfg = self:GetAssignFashionCfg(fashion_type, image_id)
				data.is_my_fashion = 1
				if not IsEmptyTable(data.cfg) then
					self.my_fashion_map[fashion_type][image_id] = data
				end
			end
		end
	elseif fashion_type == AssignmentWGData.FASHION_TYPE.TIANSHEN then
		for index, v in pairs(protocol.active_image_flag) do
			local data = self.GetFashionDataModel()
			data.cfg = self:GetAssignFashionCfg(fashion_type, index)
			data.is_my_fashion = 1
			if not IsEmptyTable(data.cfg) and v == 1 then
				self.my_fashion_map[fashion_type][index] = data
			end
		end
	elseif fashion_type == AssignmentWGData.FASHION_TYPE.HUANSHOU then
		self.my_fashion_map[fashion_type] = {}
		local data_list = ControlBeastsWGData.Instance:GetCultureBeastsList(false, true, false, true)

		for k, v in pairs(data_list) do
			local data = self.GetFashionDataModel()
			data.cfg = self:GetAssignFashionCfg(fashion_type, v.item_id)
			data.is_my_fashion = 1
			data.bag_index = v.bag_id - 1

			if not IsEmptyTable(data.cfg) and v.item_id > 0 then
				self.my_fashion_map[fashion_type][k] = data
			end
		end

		-- -- 设置数据 
		-- for k, v in pairs(protocol.beast_bag) do
		-- 	local data = self.GetFashionDataModel()
		-- 	data.cfg = self:GetAssignFashionCfg(fashion_type, v.beast_id)
		-- 	data.is_my_fashion = 1
		-- 	data.bag_index = v.bag_index

		-- 	if not IsEmptyTable(data.cfg) and v.beast_id > 0 then
		-- 		self.my_fashion_map[fashion_type][k] = data
		-- 	end
		-- end
	end
end

function AssignmentWGData:UpdateTaskExp(protocol)
	self.task_exp = protocol.exp
end

function AssignmentWGData:UpdateSingleTaskInfo(protocol)

	local id = protocol.task_data.task_id
	self.task_data_list[id] = protocol.task_data

	if self:GetHasSpecialTask() then
		self.task_data_list[id].cfg = (self.special_task_cfg or {})[protocol.task_data.task_id] or {}
	else
		self.task_data_list[id].cfg = self:GetTaskCfgById(protocol.task_data.task_id)
	end
	
	self.used_borrow_times = protocol.used_borrow_times
	--print_error("UpdateSingleTaskInfo", protocol)
end

function AssignmentWGData:UpdateTaskInfoList(protocol)
	self.used_free_refresh_time = protocol.used_free_refresh_time
	self:UpdateTaskDataList(protocol)
	--print_error("UpdateTaskInfoList", protocol)
end

function AssignmentWGData:UpdateTaskDataList(protocol)
	self.task_data_list = {}
	for i = 1, #protocol.task_data_list do
		local task_data = protocol.task_data_list[i]

		if self:GetHasSpecialTask() then
			task_data.cfg = (self.special_task_cfg or {})[task_data.task_id] or {}
		else
			task_data.cfg = self:GetTaskCfgById(task_data.task_id)
		end

		self.task_data_list[task_data.task_id] = task_data
	end
end

function AssignmentWGData:UpdateAllTaskInfo(protocol)
	self.used_free_refresh_time = protocol.used_free_refresh_time
	self.used_borrow_times = protocol.used_borrow_times

	self.current_special_task_id = protocol.current_special_task_id

	self:UpdateTaskDataList(protocol)

	self.my_assign_data_list = protocol.my_assign_data_list
	-- self.my_assign_data_map = {}
	-- for k, v in pairs(self.my_assign_data_list) do
	-- 	if not IsEmptyTable(v.cfg) then
	-- 		if self.my_assign_data_map[v.cfg.type] == nil then
	-- 			self.my_assign_data_map[v.cfg.type] = {}
	-- 		end
	-- 		self.my_assign_data_map[v.cfg.type][v.cfg.seq] = v
	-- 	end
	-- end

	-- 幻兽 type  = 4    seq 是背包的bag_index
	self.my_assign_data_map = {}
	for k, v in pairs(self.my_assign_data_list) do

		if self.my_assign_data_map[v.type] == nil then
			self.my_assign_data_map[v.type] = {}
		end

		self.my_assign_data_map[v.type][v.seq] = v
	end

	-- print_error(self.my_assign_data_map[4])
	-- print_error(self.my_assign_data_list)
	--print_error("UpdateAllTaskInfo", #protocol.task_data_list, protocol.task_data_list)
end

function AssignmentWGData:UpdateSingleRentInfo(protocol)
	local rent_data = protocol.rent_data
	self.my_rent_data_map[rent_data.type][rent_data.seq] = rent_data
end

function AssignmentWGData:UpdateMyRentInfo(protocol)
	self.my_rent_data_list = protocol.my_rent_data_list
	self.my_rent_data_map = {}
	for k, v in pairs(protocol.my_rent_data_list) do
		if self.my_rent_data_map[v.cfg.type] == nil then
			self.my_rent_data_map[v.cfg.type] = {}
		end
		self.my_rent_data_map[v.cfg.type][v.cfg.seq] = v
	end
end

function AssignmentWGData:UpdateGuildRentInfo(protocol)
	self.all_rent_data_list = protocol.all_rent_data_list
	--print_error(self.all_rent_data_list)

	self.can_rent_data_map = {}
	self.can_rent_data_list = {}
	self.my_is_rent_map = {}--玩家被雇佣的外形列表
	self.my_is_rent_list = {}--玩家被雇佣的外形列表
	for k, v in pairs(self.all_rent_data_list) do
		local cfg = v.cfg
		if v.status == AssignmentWGData.GUILD_ASSIGN_STATUS.RENT
			and v.is_my_fashion == 0 and not IsEmptyTable(cfg) then
				
			table.insert(self.can_rent_data_list, v)
			if self.can_rent_data_map[cfg.type] == nil then
				self.can_rent_data_map[cfg.type] = {}
			end	
			table.insert(self.can_rent_data_map[cfg.type], v)
		end

		if v.status == AssignmentWGData.GUILD_ASSIGN_STATUS.RENT_ASSIGN
			and v.is_my_fashion == 1 and not IsEmptyTable(cfg) then
				
			if self.my_is_rent_map[cfg.type] == nil then
				self.my_is_rent_map[cfg.type] = {}
			end	
			self.my_is_rent_map[cfg.type][cfg.seq] = v
			table.insert(self.my_is_rent_list, v)		
		end
	end
	
	table.sort(self.all_rent_data_list, function(a, b)
		return a.cfg.color > b.cfg.color
	end)
	--print_error(self.can_rent_data_list)
end

function AssignmentWGData:UpdateGuildRecordList(protocol)
	self.guild_rent_log_text = ""

	local list = protocol.all_log_list
	local time_str, rent_str, item_name, color_str, str
	for k, v in pairs(list) do
		time_str, color_str, item_name = self:GetRecordStr(v)
		rent_str = Language.Assignment.RentType[v.flag]
		str = string.format(Language.Assignment.RentStr, time_str, v.name, rent_str, color_str, item_name)

		self.guild_rent_log_text = self.guild_rent_log_text .. str .. "\n"
	end
end

function AssignmentWGData:UpdateSelfRecordList(protocol)
	self.my_rent_log_text = ""

	local list = protocol.all_log_list
	local time_str, item_name, color_str, str, type_str
	for k, v in pairs(list) do
		time_str, color_str, item_name = self:GetRecordStr(v)
		type_str = Language.Assignment.ShelvesTopStrs[v.type + 1]
		str = string.format(Language.Assignment.MyRentStr2, time_str, type_str, color_str, item_name, v.name)

		self.my_rent_log_text = self.my_rent_log_text .. str .. "\n"
	end
end

function AssignmentWGData:GetRecordStr(log_data)
	local time_str = TimeUtil.FormatMDHM(log_data.time_stamp)
	local fashion_cfg = self:GetAssignFashionCfg(log_data.type, log_data.seq) or {}
	local item_id = (fashion_cfg or {}).item_id or 0
	local item_name = (ItemWGData.Instance:GetItemConfig(item_id) or {}).name or ""
	local color_str = item_id <=0 and "" or ItemWGData:GetItemColor(item_id)

	return time_str, color_str, item_name
end
---------------------------------------------------------------协议 end--------------------------------------------------------

---------------------------------------------------------------数据 start--------------------------------------------------------
--获得未被雇佣的上架外形列表
function AssignmentWGData:GetRentAssignFashionDataList(fashion_type)
	local data_list = {}
	if fashion_type == 0 or self.can_rent_data_map == nil then
		data_list = self.can_rent_data_list or {}
	else
		data_list = (self.can_rent_data_map or {})[fashion_type] or {}
	end

	local sort_func = function(a, b)
		return a.cfg.color > b.cfg.color
	end
	table.sort(data_list, sort_func)

	return data_list
end

function AssignmentWGData:GetAllRentFashionDataList()
	return self.all_rent_data_list or {}
end

function AssignmentWGData:GetMyAssignDataByIndex(index)
	return (self.my_assign_data_list or {})[index] or {}
end

function AssignmentWGData:GetTaskData(task_id)
	return (self.task_data_list or {})[task_id] or {}
end

function AssignmentWGData:GetTaskDataList()
	return self.task_data_list or {}
end

function AssignmentWGData:GetTaskRestTime(task_id)
	local time_stamp = self:GetTaskData(task_id).begin_time_stamp or 0
	local cfg_time = (self:GetTaskData(task_id).cfg or {}).time or 0
	local rest_time = (cfg_time + time_stamp) - TimeWGCtrl.Instance:GetServerTime()

	return rest_time
end

function AssignmentWGData:GetTaskState(task_id)
	local time_stamp = self:GetTaskData(task_id).begin_time_stamp or 0
	if time_stamp > 0 then
		return AssignmentWGData.TASK_STATUS.ASSIGN
	else
		return AssignmentWGData.TASK_STATUS.NORMAL
	end
end

--获取正在派遣任务的外形
function AssignmentWGData:GetTaskFashionList(task_id)
	local task_data = self.task_data_list[task_id]
	if IsEmptyTable(task_data) then
		return {}
	end

	local fashion_data_list = {}

	for k, v in pairs(task_data.my_assign_index_list) do
		local fashion_data = self:GetMyAssignDataByIndex(v)
		if not IsEmptyTable(fashion_data) then
			-- if fashion_data.type == 4 then
			-- 	local bag_data = ControlBeastsWGData.Instance:GetBeastDataById(fashion_data.seq + 1)
			-- 	if not IsEmptyTable(bag_data) then
			-- 		local cfg = self:GetAssignFashionCfg(fashion_data.type, bag_data.item_id)
			-- 		fashion_data.cfg = cfg
			-- 	end
			-- end

			table.insert(fashion_data_list, fashion_data)
		end
	end

	for k, v in pairs(task_data.brrow_assign_list) do
		-- if v.type == 4 then
		-- 	local bag_data = ControlBeastsWGData.Instance:GetBeastDataById(v.seq + 1)
		-- 	if not IsEmptyTable(bag_data) then
		-- 		local cfg = self:GetAssignFashionCfg(v.type, bag_data.item_id)
		-- 		v.cfg = cfg
		-- 	end
		-- end

		if not IsEmptyTable(v.cfg) then
			--print_error(v)
			table.insert(fashion_data_list, v)
		end
	end

	return fashion_data_list
end

--检测是否还有未完成的特殊任务
function AssignmentWGData:GetHasSpecialTask()
	if IsEmptyTable(self.special_task_cfg) then
		return false
	end

	local last_task_id = self:GetLastSpecialTaskId()
	return self:GetCurrentSpecialTaskId() <= last_task_id
end

function AssignmentWGData:GetLastSpecialTaskId()
	return (self.special_task_cfg[#self.special_task_cfg] or {}).id or 0
end

function AssignmentWGData:GetCurrentSpecialTaskId()
	return self.current_special_task_id or 0
end

function AssignmentWGData:GetTaskExp()
	return self.task_exp or 0
end

function AssignmentWGData:GetHasFreeRefreshTimes()
	local free_refresh_time = self:GetOtherCfg().free_refresh_times or 0
	return free_refresh_time > self.used_free_refresh_time
end

function AssignmentWGData:GetRefreshItemId()
	return self:GetOtherCfg().refresh_item_id or 0
end

function AssignmentWGData:GetRefreshItemNum()
	return self:GetOtherCfg().refresh_item_num or 0
end

function AssignmentWGData:GetBorrowTimeLimit()
	return self:GetOtherCfg().borrow_time_limit or 0
end

function AssignmentWGData:GetIsSameFashionData(a, b)
	if IsEmptyTable(a) or IsEmptyTable(b) 
		or IsEmptyTable(a.cfg) or IsEmptyTable(b.cfg)  then

		return false
		
	end
	
	return a.cfg.type == b.cfg.type and a.cfg.seq == b.cfg.seq and (a.owner_name == b.owner_name)
end

function AssignmentWGData:GetGuildRentLogText()
	return self.guild_rent_log_text or ""
end

function AssignmentWGData:GetMyRentLogText()
	return self.my_rent_log_text or ""
end

function AssignmentWGData:GetCanBorrowTimes()
	local max_borrow_times = self:GetOtherCfg().borrow_times or 0

	return max_borrow_times - (self.used_borrow_times or 0)
end

function AssignmentWGData:GetHasAssignTask()
	if IsEmptyTable(self.task_data_list) then
		return false
	end

	for k, v in pairs(self.task_data_list) do
		if v.begin_time_stamp > 0 then--有正在派遣的任务
			return true
		end
	end

	return false
end

--获取最接近完成时间的派遣数据
function AssignmentWGData:GetOneAssignTask()
	if IsEmptyTable(self.task_data_list) then
		return {}
	end

	local small_rest_time = 9999999
	local task_data = {}
	for k, v in pairs(self.task_data_list) do
		local rest_time = self:GetTaskRestTime(v.task_id)
		local state = self:GetTaskState(v.task_id)
		if state == AssignmentWGData.TASK_STATUS.ASSIGN and rest_time < small_rest_time then
			task_data = v
			small_rest_time = rest_time
		end
	end

	return task_data
end

--获取有几条任务派遣中
function AssignmentWGData:GetAssignTaskNum()
	if IsEmptyTable(self.task_data_list) then
		return 0
	end

	local assign_num = 0
	for k, v in pairs(self.task_data_list) do
		local state = self:GetTaskState(v.task_id)
		if state == AssignmentWGData.TASK_STATUS.ASSIGN then
			assign_num = assign_num + 1
		end
	end

	return assign_num
end

function AssignmentWGData:IsRefreshConsumeId(item_id)
	return item_id == self:GetRefreshItemId()
end
---------------------------------------------------------------数据 end--------------------------------------------------------

---------------------------------------------------------------红点 start--------------------------------------------------------

function AssignmentWGData:GetAssignTaskRemind()
	if IsEmptyTable(self.task_data_list) then
		return 0
	end
	local task_cfg = self:GetOtherCfg()
	local exp = self:GetTaskExp()
	local need_exp = task_cfg.min_cost_exp or 0
	local is_can_accpet = exp >= need_exp

	for k, v in pairs(self.task_data_list) do
		if v.begin_time_stamp > 0 and self:GetTaskRestTime(v.task_id) <= 0 then--派遣已完成能领取的状态
			return 1
		end

		-- 未派遣 能够派遣完成
		if v.begin_time_stamp <= 0 then
			local conditions = v.cfg.conditions

			if nil ~= conditions then
				local all_condition_list = Split(conditions, "|")
				local data_list = self:GetBeastOneKeyDataList(v.task_id)

				if not IsEmptyTable(data_list) then
					if #all_condition_list <= #data_list and is_can_accpet then
						return 1
					end
				end
			end
		end
	end

	return 0
end

function AssignmentWGData:GetAssignGuideRemind()
	if IsEmptyTable(self.task_data_list) then
		return 0
	end

	local is_guide = FunctionGuide.GetIsAlreadyGuide(AssignmentWGData.ASSIGN_GUIDE_ID)
	local special_id = self:GetCurrentSpecialTaskId()
	local has_remind = special_id <= 1 and not is_guide

	return has_remind and 1 or 0
end

function AssignmentWGData:GetAssignRemind()
	local remind = 0

	local is_opened, reason_string = FunOpen.Instance:GetFunIsOpened("TianShenLiLianView")
	if not is_opened then
		return remind
	end	

	if self:GetAssignTaskRemind() == 1 then
		remind = 1
	end

	-- if self:GetAssignGuideRemind() == 1 then
	-- 	remind = 1
	-- end

	return remind
end

---------------------------------------------------------------红点 end--------------------------------------------------------


-----------------------------------------------------------御兽派遣----------------------------------------------------------------
--获取自己未上架以及未派遣的幻兽列表   1水2火3风4神5魔
function AssignmentWGData:GetMyAssignHuanShouDataList(beast_element)
	local data_list = {}

	if self.my_fashion_map[AssignmentWGData.FASHION_TYPE.HUANSHOU] ~= nil then
		-- 所有的幻兽
		if beast_element == 0 then
			for _, v in pairs(self.my_fashion_map[AssignmentWGData.FASHION_TYPE.HUANSHOU]) do
				local cfg = v.cfg
				local is_assign = self:GetMyAssignData(cfg.type, v.bag_index) ~= nil
				local is_rent = self:GetMyFashionIsRent(cfg.type, cfg.seq)

				if not is_assign and not is_rent then
					table.insert(data_list, v)
				end
			end
		else
			for _, v in pairs(self.my_fashion_map[AssignmentWGData.FASHION_TYPE.HUANSHOU]) do
				local cfg = v.cfg
				local is_assign = self:GetMyAssignData(cfg.type, v.bag_index) ~= nil
				local is_rent = self:GetMyFashionIsRent(cfg.type, cfg.seq)
				if not is_assign and not is_rent then
					local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(cfg.item_id)

					if not IsEmptyTable(beast_cfg) then
						if beast_cfg.beast_element == beast_element then
							table.insert(data_list, v)
						end
					end
				end
			end
		end
	end

	table.sort(data_list, function(a, b)
		return a.cfg.color > b.cfg.color
	end)

	return data_list
end

function AssignmentWGData:GetBeastOneKeyDataList(task_id)
	local task_cfg = self:GetHasSpecialTask() and self.special_task_cfg or self.task_cfg
	local cfg = (task_cfg or {})[task_id]
	if IsEmptyTable(cfg) then
		return {}
	end

	local all_fashion_list = self:GetMyAssignHuanShouDataList(0)
	local condition_list = Split(cfg.conditions, "|")
	local type_list = {}
	for k, v in pairs(condition_list) do
		local condition = Split(v, ",")
		type_list[k] = {}
		type_list[k].need_type = tonumber(condition[1])
		type_list[k].need_color = tonumber(condition[2])
		type_list[k].need_beast_element = tonumber(condition[3])
		type_list[k].need_beast_star = tonumber(condition[4])
	end
	table.sort(type_list, function(a, b)
		return a.need_color > b.need_color
	end)

	local data_list = {}
	for _, fashion in pairs(all_fashion_list) do
		local cfg = fashion.cfg
		local best_color = -1
	 	local index = -1

		for k, v in pairs(type_list) do
			if (cfg.type == v.need_type or v.need_type == 0) and cfg.color >= v.need_color and v.need_color >= best_color 
				and (v.need_beast_element == 0 or (cfg.beast_cfg.beast_element == v.need_beast_element)) 
				and cfg.beast_cfg.beast_star >= v.need_beast_star then
					best_color = v.need_color
					index = k

				-- and v.need_beast_element == 0 or (cfg.beast_cfg.beast_element == v.need_beast_element) then
				-- 	best_color = v.need_color
				-- 	index = k
			end
		end

		if index > 0 then
			table.insert(data_list, fashion)
			table.remove(type_list, index)
		end

		if #type_list <= 0 then
			break
		end
	end

	return data_list
end

function AssignmentWGData:GetIsSameHuanShouData(a, b)
	if IsEmptyTable(a) or IsEmptyTable(b) 
		or IsEmptyTable(a.cfg) or IsEmptyTable(b.cfg)  then
		return false
	end
	
	return a.cfg.type == b.cfg.type and a.cfg.seq == b.cfg.seq and (a.owner_name == b.owner_name) and a.bag_index == b.bag_index
end