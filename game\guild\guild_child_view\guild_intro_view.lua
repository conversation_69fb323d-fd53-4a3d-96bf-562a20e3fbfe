GuildView.PUBLISH_WORD_SIZE = 170
-- 仙盟简介
local Notice_State = {
	Change = 1,
	Save = 2,
}
function GuildView:InitIntroView()
	--操作按钮data
	self.caozuo_data_list = ""
	self.caozuo_name = ""
	self.caozuo_index = 0
	
	self.node_list.img_jx_num:SetActive(false)
	self.node_list.img_setting_num:SetActive(false)
	self.node_list.Close:SetActive(false)
	self.node_list.btn_gk_changenotice.button:AddClickListener(BindTool.Bind(self.OnModNoticeHandler, self))
	self.node_list.Close.button:AddClickListener(BindTool.Bind(self.Close_caozauo, self))
	--self.node_list.layout_gk_manage.button:AddClickListener(BindTool.Bind(self.OnGoXiulian, self))--修炼
	self.node_list.layout_zhudi_btn.button:AddClickListener(BindTool.Bind(self.OnGoZhudiReward<PERSON><PERSON><PERSON><PERSON><PERSON>, self)) --祈福
	--self.node_list.layout_guild_shop.button:AddClickListener(BindTool.Bind(self.OnGoShop, self))  --活动（更改为跳转仓库）
	self.node_list.layout_guild_lingdi.button:AddClickListener(BindTool.Bind(self.GuildFBCommon, self)) --领地
	self.node_list["btn_change_name"].button:AddClickListener(BindTool.Bind(self.OpenRenameView, self))

	--操作列表
	self.node_list["operation1"].button:AddClickListener(BindTool.Bind(self.OnClickManager1, self)) --申请设置
	self.node_list["operation2"].button:AddClickListener(BindTool.Bind(self.OnClickManager3, self)) --申请列表
	self.node_list["operation3"].button:AddClickListener(BindTool.Bind(self.OnClickManager4, self))  --招揽贤士
	self.node_list["operation4"].button:AddClickListener(BindTool.Bind(self.OnClickExitGuild, self)) --解散or退出宗门
	
	for i = 1 , 3 do
		self.node_list["operation" .. i]:SetActive(false)
	end
	self.node_list.operation4:SetActive(true) --退出宗门

	-- self.node_list.btn_state.button:AddClickListener(BindTool.Bind(self.OnClickGuildState, self))
	GuildWGData.Instance:SetCurMengZhuUid(0)
	self.notice_state = Notice_State.Change
	self.is_first_open = true

	self.flush_intro_delay_event = BindTool.Bind(self.OnFlushDealyIntroView,self)

	if not self.mengzhu_model then
		self.mengzhu_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["mengzhu_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.mengzhu_model:SetRenderTexUI3DModel(display_data)
		self.mengzhu_model:SetRTAdjustmentRootLocalScale(0.9)
		-- self.mengzhu_model:SetUI3DModel(self.node_list.mengzhu_model.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.mengzhu_model)
	end

	if not self.zhanshen_model then
		self.zhanshen_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["zhanshen_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.zhanshen_model:SetRenderTexUI3DModel(display_data)
		self.zhanshen_model:SetRTAdjustmentRootLocalScale(0.9)
		-- self.zhanshen_model:SetUI3DModel(self.node_list.zhanshen_model.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.zhanshen_model)
	end
end

--刷新操作按钮列表
function GuildView:FlushList()
	if RoleWGData.Instance.role_vo.guild_post == GUILD_POST.TUANGZHANG then --帮主
		for i = 1 , 4 do
			self.node_list["operation" .. i]:SetActive(true)
		end
	elseif RoleWGData.Instance.role_vo.guild_post == GUILD_POST.FU_TUANGZHANG then --副帮主
		self.node_list.operation1:SetActive(false)
		self.node_list.operation2:SetActive(true)
		self.node_list.operation3:SetActive(true)
	elseif RoleWGData.Instance.role_vo.guild_post == GUILD_POST.ZHANG_LAO then --长老
		self.node_list.operation2:SetActive(true)
		self.node_list.operation3:SetActive(false)
	else --其他成员
		for i = 1 , 3 do
			self.node_list["operation" .. i]:SetActive(false)
		end
		self.node_list.operation4:SetActive(true)
	end
	--策划要先屏蔽
	-- self.node_list.btn_gk_changenotice:SetActive(RoleWGData.Instance.role_vo.guild_post == GUILD_POST.TUANGZHANG) --修改按钮显示
end

-- 设置view大小尺寸
function GuildView:SetIntroBtnSize(count)
	local hight = count*50
	self.node_list["imggk_bg"].rect.sizeDelta = Vector2(207,hight + 10)
	self.node_list["ph_btn_caozuolist"].rect.sizeDelta = Vector2(196,hight)

	self.node_list["imggk_bg"].transform.anchoredPosition = Vector2(330, -101)
	self.node_list["ph_btn_caozuolist"].transform.anchoredPosition = Vector2(330,-107)
end

-- 仙盟管理事件
function GuildView:OnLingdiRewardGuildHandler()
	self:ChangeToIndex(TabIndex.guild_cangku)
end

-- 仙盟管理事件
function GuildView:OnGoZhudiRewardGuildHandler()
	GuildWGCtrl.Instance:OpenGuildJuanXinaView()
end

--跳转到修炼
function GuildView:OnGoXiulian()
	if FunOpen.Instance:GetFunIsOpenedByTabName(FunName.guild_skill) then
		self:ChangeToIndex(TabIndex.guild_skill)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.exercise_desc)
	end
end

--跳转到活动界面 (修改为仓库)
function GuildView:OnGoShop()
	self:ChangeToIndex(TabIndex.guild_cangku)
	--ViewManager.Instance:Open(GuideModuleName.Shop,40)
end

function GuildView:GetFahionBody(fashion_cfg_list,part_type, index)
	for k, v in pairs(fashion_cfg_list) do
		if v.part_type == part_type and index == v.index then
			return v
		end
	end
	return nil
end

function GuildView:DeleteIntroView()
	if self.battle_role_display then
		self.battle_role_display:DeleteMe()
		self.battle_role_display = nil
	end

	if self.mengzhu_model then
		self.mengzhu_model:DeleteMe()
		self.mengzhu_model = nil
	end

	if self.zhanshen_model then
		self.zhanshen_model:DeleteMe()
		self.zhanshen_model = nil
	end

	self.mengzhu_uid_cache = nil
	self.zhanshen_uid_cache = nil

    self.is_load_intro_model = false
end

function GuildView:OnFlushIntroView()
	-- local asset = "a3_ty_bg4"
	-- self.node_list.RawImage_tongyong.raw_image:LoadSprite(ResPath.GetRawImagesPNG(asset))
	local flag = GuildWGData.Instance:GetEnterGuildFlushFlag()
	if flag then
		ReDelayCall(self, self.flush_intro_delay_event, 0.5, "flush_intro_delay_event")
	else
		ReDelayCall(self, self.flush_intro_delay_event, 0.05, "flush_intro_delay_event")
	end

	self:FlushList()
end

function GuildView:OnFlushDealyIntroView()
	GuildWGData.Instance:SetEnterGuildFlushFlag(false)
	self.guildvo = GuildDataConst.GUILDVO

	--获取宗主职业id  加载职业图标
	local prof_id, sex_id = 1, 0
	if self.guildvo.tuanzhang_uid == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then --机器人
		local robot_data = GuildWGData.Instance:GetRobotBrowseCfgByRoleName(self.guildvo.tuanzhang_name)
		prof_id = math.fmod(robot_data.prof or 1, 10)
		sex_id = robot_data.sex
	else
		prof_id = math.fmod(self.guildvo.tuanzhang_prof, 10)
		sex_id = self.guildvo.tuanzhang_sex
	end
	local bundle, asset = ResPath.GetCommonImages(RoleWGData.GetProfIcon(prof_id, sex_id))
	self.node_list.prof_icon.image:LoadSprite(bundle, asset)

	self.node_list.lbl_gk_mengzhu.text.text = self.guildvo.tuanzhang_name --xxx
	self.node_list.lbl_gk_guildlv.text.text = self.guildvo.guild_level
	self.node_list.lbl_gk_cap.text.text = CommonDataManager.ConverExpByThousand(GuildWGData.Instance:GetMainRoleGuildCapability())
	local cur_zone = GuildWGData.Instance:GetCurGuildZone() or -1
	self.node_list.lbl_gk_grade.text.text = cur_zone < 0 and Language.Guild.GuildNotZone or Language.Guild.GuildZone[cur_zone]

	local my_guild_lv_rank = GuildWGCtrl.Instance:MyGuildRank()
	self.node_list.lbl_gk_rank_level.text.text = my_guild_lv_rank
	self.node_list.lbl_gk_name.text.text = self.guildvo.guild_name
	local cur_lv_cfg = GuildWGData.Instance:GetGuildLevelCfg(self.guildvo.guild_level)
	local guild_max_level = GuildWGData.Instance:GetGuildMaxLevel()
	if cur_lv_cfg and self.guildvo.guild_level >= guild_max_level then
		self.node_list.lbl_gk_consume.text.text = CommonDataManager.ConverExpByThousand(self.guildvo.guild_exp)
	elseif cur_lv_cfg then
		self.node_list.lbl_gk_consume.text.text = CommonDataManager.ConverExpByThousand(self.guildvo.guild_exp) .. "/" .. CommonDataManager.ConverExpByThousand(cur_lv_cfg.max_exp)
	else
		self.node_list.lbl_gk_consume.text.text = CommonDataManager.ConverExpByThousand(self.guildvo.guild_exp)
	end


	local guild_post = GameVoManager.Instance:GetMainRoleVo().guild_post--军团职位

	local member_str =self.guildvo.cur_member_count .. "/" .. self.guildvo.max_member_count
    self.node_list.lbl_gk_member.text.text = member_str
    local str = string.gsub(self.guildvo.guild_notice,"\\n", "\n")
	self.node_list.label_guild_publish_word.text.text = str
	-- if self.is_first_open and self.guildvo and self.guildvo.guild_notice ~= "" then
	-- 	GlobalTimerQuest:AddDelayTimer( function ()
	-- 	local posy = self.node_list.label_guild_publish_content.rect.sizeDelta.y / -2
	-- 	self.node_list.label_guild_publish_content.transform.localPosition =  Vector3(self.node_list.label_guild_publish_content.transform.localPosition.x, posy, 0)
	-- 	self.is_first_open = false
	-- 	end,0.1)
	-- end
	--策划要先屏蔽
	-- self.node_list.btn_gk_changenotice:SetActive(guild_post == GUILD_POST.TUANGZHANG)
	self.node_list["btn_change_name"]:SetActive(guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.FU_TUANGZHANG)
	self:GuildInfoUpdataNum()

	-- 模型	
	local menber_list = GuildDataConst.GUILD_MEMBER_LIST
	local mengzhu_index, zhanshen_index, cap = 0, 0, 0

	if menber_list.count <= 0 and self.guildvo.guild_id > 0 then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, self.guildvo.guild_id)
	else
		for i = 1, menber_list.count do
			local item = menber_list.list[i]
	
			if item.post == 4 then
				mengzhu_index = i
			end
	
			if item.capability > cap then
				cap = item.capability
				zhanshen_index = i
			end
		end

		local mengzhu_info, zhanshen_info =  menber_list.list[mengzhu_index],  menber_list.list[zhanshen_index]
		local role_id = RoleWGData.Instance:GetRoleVo().role_id
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true, ignore_shouhuan = true, ignore_tail = true, ignore_waist = true}
		if mengzhu_info then
			self.node_list.mengzhu_name.text.text = mengzhu_info.role_name
			self.node_list.mengzhu_cap_value.text.text = mengzhu_info.capability
			self.mengzhu_model:PlayLastAction()

			if nil == self.mengzhu_uid_cache or mengzhu_info.uid ~= self.mengzhu_uid_cache then
				self.mengzhu_uid_cache = mengzhu_info.uid

				if mengzhu_info.uid < 0 then
					-- 机器人
					local robot_data = GuildWGData.Instance:GetRobotBrowseCfgByRoleName(mengzhu_info.role_name)
					self.mengzhu_model:SetModelResInfo(robot_data, ignore_table)
				else
					if mengzhu_info.uid == role_id then
						self.mengzhu_model:SetModelResInfo(role_vo, ignore_table)
					else
						self.mengzhu_model:SetModelResInfo(mengzhu_info, ignore_table)
						BrowseWGCtrl.Instance:BrowRoelInfo(mengzhu_info.uid, function (protocol)
							if self.mengzhu_model then
								self.mengzhu_model:SetModelResInfo(protocol, ignore_table)
							end
						end)
					end
				end
			end
		end
	
		if zhanshen_info then
			self.node_list.zhanshen_name.text.text = zhanshen_info.role_name
			self.node_list.zhanshen_cap_value.text.text = zhanshen_info.capability
			self.zhanshen_model:PlayLastAction()

			if nil == self.zhanshen_uid_cache or zhanshen_info.uid ~= self.zhanshen_uid_cache then
				self.zhanshen_uid_cache = zhanshen_info.uid

				if zhanshen_info.uid < 0 then
					self.zhanshen_model:SetModelResInfo(zhanshen_info, ignore_table)
				else
					if zhanshen_info.uid == role_id then
						self.zhanshen_model:SetModelResInfo(role_vo, ignore_table)
					else
						BrowseWGCtrl.Instance:BrowRoelInfo(zhanshen_info.uid, function (protocol)
							self.zhanshen_model:SetModelResInfo(protocol, ignore_table)
						end)
					end
				end
			end
		end
	end
end

function GuildView:ChangeGuildName(name)
	self.node_list.lbl_gk_name.text.text = name
end

-- function GuildView:OnClickGuildState()
-- 	local role_tip = RuleTip.Instance
-- 	if role_tip then
-- 		role_tip:SetTitle(Language.Guild.GuildTuanZhangTips)
-- 		role_tip:SetContent(Language.Guild.GuildTuanZhangStateTips)
-- 	else
-- 		print_error("GuildView:OnClickGuildState()","OnClickBtnMLImageChongTip() can not find the get way!")
-- 	end
-- end

function GuildView:OpenZiJinPanel()
	self.node_list["zijin_panel"]:SetActive(true)
	UITween.AlpahShowPanel(self.node_list["zijin_panel"],true,0.5,DG.Tweening.Ease.Linear)
end

function GuildView:CloseZiJinPanel()
	self.node_list["zijin_panel"]:SetActive(false)
end

function GuildView:GuildInfoUpdataNum()
	self.node_list.img_jx_num:SetActive(GuildWGData.Instance:IsCanJuanXian() == 1)
	self.node_list.img_setting_num:SetActive(GuildWGData.Instance:GetIsHasGuildSetting())
end

function GuildView:GuildInfoOwnerVoAck(owner_vo)
	self.battle_role_display:SetRoleVo(owner_vo)
end

-- 仙盟管理事件（操作按钮事件）
function GuildView:OnManagerGuildHandler()
	local guild_post = RoleWGData.Instance.role_vo.guild_post
	--帮主  副帮主  长老 显示操作类型列表
	if guild_post == GUILD_POST.TUANGZHANG or guild_post == GUILD_POST.FU_TUANGZHANG or guild_post == GUILD_POST.ZHANG_LAO then
		self.node_list.Close:SetActive(true)
		self:FlushList()
	else
		--其他成员 直接显示退出操作
		self:OnClickManager7()
	end
	
end

-- 添加按钮点击事件(操作按钮菜单管理)
function GuildView:SelectGaiKuangItemCallBack(item,cell_index, is_default, is_click)
	if is_default then return end
	if not item or not item:GetData() then return end
	self:Close_caozauo()
	local index = item:GetData()[2]

	if index == 1 then
		self:OnClickManager1()
	elseif index == 3 then
		self:OnClickManager3()
    elseif index == 4 then
		self:OnClickManager4()
	elseif index == 6 then
		self:OnClickManager6()
	elseif index == 7 then
		self:OnClickManager7()
	end
end

function GuildView:Close_caozauo()
 	self.node_list.Close:SetActive(false)
 end

function GuildView:OnClickManager1()
	self.node_list.Close:SetActive(false)
	GuildWGCtrl.Instance:OpenSettingView()--申请设置
end

--这些功能暂时去掉
-- function GuildView:OnClickManager2()
-- 	self.node_list.Close:SetActive(false)
-- 	GuildWGCtrl.Instance:OpenMailView()--群发邮件
-- end
-- function GuildView:OnClickManager5()
-- 	self.node_list.Close:SetActive(false)
-- 	self:OnClickToOpenMergePanel()--仙盟合并
-- end
-- function GuildView:OnClickManager8()
-- 	self.node_list.Close:SetActive(false)
-- 	self:OnCheckCanDelateHandler()--弹劾帮主
-- end

function GuildView:OnClickManager3()
	self.node_list.Close:SetActive(false)
	GuildWGCtrl.Instance:IOpenGuildApplyforView()--申请列表
end
function GuildView:OnClickManager4()
	self.node_list.Close:SetActive(false)
	GuildWGCtrl.Instance:SendGuildCallUpOperate()--招揽贤士
end

--退出宗门判断
function GuildView:OnClickExitGuild()
	self.node_list.Close:SetActive(false)
	if RoleWGData.Instance.role_vo.guild_post == GUILD_POST.TUANGZHANG then --宗主
		local guildvo = GuildDataConst.GUILDVO
 		local cur_member_count = guildvo.cur_member_count
 		if cur_member_count > 1 then
			self:OnConfirmDismissGuildHandler()--解散仙盟
		else
			self:OnBangZhuExitGuildHandler()
		end
	else
		self:OnConfirmExitGuildHandler()--退出帮派
	end
end

-- 退出仙盟二次确认
function GuildView:OnConfirmExitGuildHandler()
	local pop_alert = self:GetPopAlert()
	if pop_alert ~= nil then
		pop_alert:SetOkFunc(BindTool.Bind1(self.ExitGuild, self))
		local lable = string.format(Language.Guild.QuitGuildTip, GuildWGData.Instance:GetJoinGuildTimeLimit())
		pop_alert:SetLableString(lable)
		pop_alert:Open()
	end
end

-- 帮主退出仙盟二次确认
function GuildView:OnBangZhuExitGuildHandler()
	local pop_alert = self:GetPopAlert()
	if pop_alert ~= nil then
		pop_alert:SetOkFunc(BindTool.Bind1(self.DismissGuild, self))
		local lable = string.format(Language.Guild.ConfirmDismissGuildTip, GuildWGData.Instance:GetJoinGuildTimeLimit())
		pop_alert:SetLableString(lable)
		pop_alert:Open()
	end
end

-- 检查是否能够弹劾盟主
function GuildView:OnCheckCanDelateHandler()
	GuildWGCtrl.Instance:SendCheckCanDelateReq(GuildDataConst.GUILDVO.guild_id)
end

-- 弹劾盟主二次确认
function GuildView:OnConfirmTanHeMengZhuHandler()
	local pop_alert = self:GetPopAlert()
	if pop_alert ~= nil then
		pop_alert:Open()
		pop_alert:SetOkFunc(BindTool.Bind2(self.TanHeMengZhu, self))
		pop_alert:SetLableString(string.format(Language.Guild.ConfirmTanHeMengZhuTip))
	end
end

-- 弹劾盟主
function GuildView:TanHeMengZhu()
	self.guildvo = GuildDataConst.GUILDVO
	local delate_item_id = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].delate_item_id
	local index = ItemWGData.Instance:GetItemIndex(delate_item_id)
	if -1 == index then
		GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, delate_item_id)
		return
	end
	GuildWGCtrl.Instance:SendGuildDelateReq(self.guildvo.guild_id, index)
end

-- 解散仙盟二次确认， 仙盟战帮派无法解散限制
function GuildView:OnConfirmDismissGuildHandler()
	local pop_alert = self:GetPopAlert()
	if pop_alert ~= nil then
		pop_alert:Open()
		pop_alert:SetOkFunc(BindTool.Bind1(self.DismissGuild, self))
	 	local guild_id = RoleWGData.Instance.role_vo.guild_id
	 	local guild_dissolution = GuildWGData.Instance:GetGuildDissolution(guild_id)
		-- if GongChengData.Instance:GetIsRoleChengZhu() or guild_dissolution then
		--RoleWGData.Instance.role_vo获取的是角色的公共信息
		-- if self.guildvo.tuanzhang_name == RoleWGData.Instance.role_vo.name or guild_dissolution then
		-- 	 pop_alert:SetLableString(string.format(Language.Guild.ConfirmDismissGuildTip3))
		-- else --这个条件暂时应该没用了
		-- 	pop_alert:SetLableString(string.format(Language.Guild.ConfirmDismissGuildTip))
		-- end
		pop_alert:SetLableString(string.format(Language.Guild.ConfirmDismissGuildTip4))
	end
end

-- 退出仙盟事件
function GuildView:ExitGuild()
	if Scene.Instance:GetSceneType() ~= SceneType.GuildBoss then
		GuildWGCtrl.Instance:SendExigGuildReq(GuildDataConst.GUILDVO.guild_id)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.LeaveGuildSceneLimit)
	end
end

function GuildView:GuildFBCommon()
	local ok_func = function()
		GuildAnswerWGCtrl.Instance:SendGuildQuestionEnterReq()
	end
	TipWGCtrl.Instance:OpenAlertTips(Language.Guild.GoToFBCommon, ok_func)
end

-- 解散仙盟
function GuildView:DismissGuild()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
 	local guild_dissolution = GuildWGData.Instance:GetGuildDissolution(guild_id)
 	local guildvo = GuildDataConst.GUILDVO
 	local cur_member_count = guildvo.cur_member_count

	if self.guildvo.tuanzhang_name == RoleWGData.Instance.role_vo.name then
		if cur_member_count > 1 then
			GuildWGCtrl.Instance:Open(TabIndex.guild_member)
			return
		else
			GuildWGCtrl.Instance:SendDismissGuildReq(GuildDataConst.GUILDVO.guild_id)
		end
	end
end

-- 修改公告事件
function GuildView:OnModNoticeHandler()
	-- 2级以上才给改公告
	local level = GuildDataConst.GUILDVO.guild_level
	if level < 2 then
		-- self.notice_des_input_text = self.guildvo.guild_notice
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.ModifyNotice)
		return
	end

	TipWGCtrl.Instance:OpenCommonInputView(Language.Guild.GuildNoticeTitle,function (text)
		if text ~= "" then
			local guildvo = GuildDataConst.GUILDVO
			GuildWGCtrl.Instance:SendGuildChangeNoticeReq(guildvo.guild_id, text)
		end
	end,80,Language.Guild.NoticePlaceholderText)
end

function GuildView:OpenAgentPanel()
	GuildWGCtrl.Instance:OpenAgentPanel()
end

function GuildView:OnClickChangeFlag()
	GuildWGCtrl.Instance:OpenCreateGuildView()
end

function GuildView:OpenRenameView()
	ViewManager.Instance:Open(GuideModuleName.GuildRenameView)
end

---------仙盟合并--------------
function GuildView:OnClickToOpenMergePanel()
	GuildWGCtrl.Instance:OpenGuildListHeBingView()
end


ApplyTuanZhangAgent = ApplyTuanZhangAgent or BaseClass(SafeBaseView)
function ApplyTuanZhangAgent:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_replace_mengzhu")
end

function ApplyTuanZhangAgent:LoadCallBack()
	--self.cell_list = {}
	self.node_list.title_view_name.text.text = Language.Guild.ApplyGuild
	self:SetSecondView(Vector2(714,534))
	XUI.AddClickEventListener(self.node_list.apply_btn, BindTool.Bind(self.OnClickApply, self))
	self:Flush()
end

function ApplyTuanZhangAgent:OnFlush()
	local agent_uid,time_stamp,name = GuildWGData.Instance:GetGuildAgentInfo()
	local _,agent_time =  GuildWGData.Instance:GetGuildActiveCount()
	self.node_list.apply_btn:SetActive(agent_uid <= 0)
	self.node_list.content_3:SetActive(agent_uid > 0)
	self.node_list["content_1"].text.text = Language.Guild.DaiLiMengZhuDesc

	for i=3,5 do
		self.node_list["content_"..i].text.text = ""
	end
	local sever_time = TimeWGCtrl.Instance:GetServerTime()
	if CountDownManager.Instance:HasCountDown("GuildAgentPanel") then
		CountDownManager.Instance:RemoveCountDown("GuildAgentPanel")
	end
	self.guildvo = GuildDataConst.GUILDVO
	local guid_menmber_list_all_info = GuildDataConst.GUILD_MEMBER_LIST
	local guid_menmber_list_all = guid_menmber_list_all_info.list
	for k,v in pairs(guid_menmber_list_all) do
		if v.uid == self.guildvo.tuanzhang_uid and v.is_online == 0 then
			if v.last_login_time + 	agent_time	< sever_time then
				local sec_num = sever_time - v.last_login_time - agent_time
				local hour = math.ceil(sec_num/3600)
				self.node_list["content_5"].text.text = string.format(Language.Guild.NotOnlineDesc, hour)
			end
			break
		end
	end
	if agent_uid <= 0 then return end
	self.node_list.content_4.text.text = name
	local all_time = time_stamp + agent_time

	self:UpdataNextTime(sever_time, all_time)
	CountDownManager.Instance:AddCountDown("GuildAgentPanel", BindTool.Bind1(self.UpdataNextTime, self),
			BindTool.Bind1(self.CompleteNextTime, self), all_time, nil, 1)
end

function ApplyTuanZhangAgent:OnClickApply()
	GuildWGCtrl.Instance:CSApplyForTuanZhangAgentReq(1)
end

function ApplyTuanZhangAgent:UpdataNextTime(elapse_time, total_time)
	local num = math.floor(total_time - elapse_time)
	local time_tab = TimeUtil.Format2TableDHM2(num )
	local str

	if time_tab.day > 0 then
			str = string.format(Language.Common.TimeStr2, time_tab.day, time_tab.hour, time_tab.min,time_tab.sec)
		else
			if time_tab.hour > 0 then
				str = string.format(Language.Common.TimeStr3, time_tab.hour, time_tab.min,time_tab.sec)
			else
				if time_tab.min > 0 then
					str = string.format(Language.Common.TimeStr5, time_tab.min,time_tab.sec)
				else
					str = time_tab.sec.."秒"
				end
			end
		end
	self.node_list.content_3.text.text = str
end

function ApplyTuanZhangAgent:CompleteNextTime()
	self:Close()
end

function ApplyTuanZhangAgent:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("GuildAgentPanel") then
		CountDownManager.Instance:RemoveCountDown("GuildAgentPanel")
	end

	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
	end
	self.cell_list = nil
	self.content = nil
	self.list_view = nil
end
