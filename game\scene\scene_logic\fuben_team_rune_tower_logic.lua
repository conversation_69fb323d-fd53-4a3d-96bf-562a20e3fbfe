FuBenTeamRuneTowerLogic = FuBenTeamRuneTowerLogic or BaseClass(CommonFbLogic)

function FuBenTeamRuneTowerLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
	self.update_elaspe_time = 0
end

function FuBenTeamRuneTowerLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function FuBenTeamRuneTowerLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	self.update_elaspe_time = 0
	self.lm_scene_enter_complete = true
	self.lm_check_auto_status = true
	self.scene_info_stage = nil

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.RuneTower.FBName)

		FuBenTeamCommonTowerWGCtrl.Instance:OpenRuneTowerTaskView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	self.lm_check_auto_status = false

	local view = FuBenTeamCommonTowerWGCtrl.Instance:GetRuneTowerTaskView()
	ViewManager.Instance:AddMainUIFuPingChangeList(view)
	ViewManager.Instance:AddMainUIRightTopChangeList(view)

	local scene_id = Scene.Instance:GetSceneId()
	local scene_fuben_cfg = FuBenTeamCommonTowerWGData.Instance:GetFuBenSceneCfg(scene_id)
	if scene_fuben_cfg then
		local fb_scene_info = FuBenTeamCommonTowerWGData.Instance:GetTeamCommonTowerFBInfo(scene_fuben_cfg.seq)
		FuBenTeamCommonTowerWGCtrl.Instance:OpenRuneTowerLevelShowView(fb_scene_info and fb_scene_info.level or 1)
	end
end


function FuBenTeamRuneTowerLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)

	self.lm_scene_enter_complete = false
	self.lm_check_auto_status = false
	self.scene_info_stage = nil

    MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)

	FuBenTeamCommonTowerWGCtrl.Instance:CloseRuneTowerTaskView()
	local view = FuBenTeamCommonTowerWGCtrl.Instance:GetRuneTowerTaskView()
	ViewManager.Instance:RemoveMainUIFuPingChangeList(view)
	ViewManager.Instance:RemoveMainUIRightTopChangeList(view)
	MainuiWGCtrl.Instance:SetMianUITargetPos(0, 0)

	FuBenTeamCommonTowerWGData.Instance:RestSetTeamCommonTowerFBInfo()
end


function FuBenTeamRuneTowerLogic:Update(now_time, elapse_time)
	if not self.lm_scene_enter_complete then
		return
	end

	BaseSceneLogic.Update(self, now_time, elapse_time)

	if self.update_elaspe_time + 1 > now_time then
		return
	end

	self.update_elaspe_time = now_time

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil and main_role:IsDeleted() then
		return
	end
end

function FuBenTeamRuneTowerLogic:IsRoleEnemy(target_obj, main_role)
	local target_vo = target_obj:GetVo() or {}
	if target_vo.is_shadow == 1 then
		return false
	end

	return BaseSceneLogic.IsRoleEnemy(self, target_obj, main_role)
end