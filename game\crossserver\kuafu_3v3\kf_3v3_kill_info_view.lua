KF3V3KillInfoView = KF3V3KillInfoView or BaseClass(SafeBaseView)
function KF3V3KillInfoView:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kill_info")
	self.open_tween = nil
	self.close_tween = nil
end

function KF3V3KillInfoView:ReleaseCallBack()
	if self.killer then
		self.killer:DeleteMe()
		self.killer = nil
	end
	if self.be_killer then
		self.be_killer:DeleteMe()
		self.be_killer = nil
	end
	if self.delay_close_timequest then
		GlobalTimerQuest:CancelQuest(self.delay_close_timequest)
		self.delay_close_timequest = nil
	end
	self.kill_data = nil
end

function KF3V3KillInfoView:LoadCallBack()
	self.killer = KF3V3KillRender.New(self.node_list["killer"])
	self.be_killer = KF3V3KillRender.New(self.node_list["be_killer"])
end

function KF3V3KillInfoView:OpenCallBack()
    self:Flush()
end

function KF3V3KillInfoView:OnFlush()
	if not self.kill_data then return end

	--TODO 可能需要tween动画、特效
	--tween_node
	self:DoTweenScaleContent(self.node_list.tween_node)

	self.killer:SetData(self.kill_data.killer)
	self.be_killer:SetData(self.kill_data.be_killer)

	self.delay_close_timequest = GlobalTimerQuest:AddDelayTimer(function()
		self:Close()
		self.delay_close_timequest = nil
	end, 3)
end

function KF3V3KillInfoView:SetData(kill_data)
	self.kill_data = kill_data
	if self.delay_close_timequest then
		GlobalTimerQuest:CancelQuest(self.delay_close_timequest)
		self.delay_close_timequest = nil
	end
end

function KF3V3KillInfoView:DoTweenScaleContent(node)
    if not node then return end
	local scale = Vector3(1,1,1)
	node.rect.transform.localScale = Vector3(4,4,1)
	local tweener = node.rect:DOScale(scale,0.3):SetEase(DG.Tweening.Ease.OutBack)
	tweener:SetUpdate(true)
	--EffectManager.Instance:PlayAtTransform("effects2/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.effect_point.transform)
end



KF3V3KillRender = KF3V3KillRender or BaseClass(BaseRender)
function KF3V3KillRender:__init()
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function KF3V3KillRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function KF3V3KillRender:OnFlush()
	local is_be_kill = self.data.is_be_kill

	self.node_list.role_name.text.text = self.data.name
	local asset_name = self.data.side == 1 and "red_frame_bg" or "blue_frame_bg"
	self.node_list.head_frame_bg.image:LoadSprite(ResPath.GetKF3V3(asset_name))

	local data = {}
    data.role_id = self.data.role_id
    data.prof = self.data.prof
    data.sex = self.data.sex
    self.head_cell:SetData(data)
end