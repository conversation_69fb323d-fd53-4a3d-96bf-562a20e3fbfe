BrowseHeadView = BrowseHeadView or BaseClass(SafeBaseView)

function BrowseHeadView:__init()
	self.is_modal = true
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/browse_ui_prefab", "layout_head")
end

function BrowseHeadView:__delete()

end

function BrowseHeadView:ReleaseCallBack()

end

function BrowseHeadView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(580,440)
	self.node_list["title_view_name"].text.text = "高清头像"
end

function BrowseHeadView:ShowIndexCallBack(index)
	self:Flush()
end

function BrowseHeadView:OnFlush()
	local role_info = BrowseWGData.Instance:GetRoleInfo()
	-- Í·Ïñ
	if self.node_list.img_head then
		XUI.UpdateRoleHead(self.node_list.img_head, role_info.role_id, role_info.sex, role_info.prof, nil,true)
	end
end
