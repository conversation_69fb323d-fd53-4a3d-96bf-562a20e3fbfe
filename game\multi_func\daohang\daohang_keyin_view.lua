function MultiFunctionView:ShowDaoHangKeYinCallBack()
    self:RightPanleShowTween(self.node_list.keyin_right_tween_root, self.node_list.keyin_mid)
end

function MultiFunctionView:LoadDaoHangKeYinCallBack()
    if not self.keyin_target_item then
        self.keyin_target_item = ItemCell.New(self.node_list.keyin_target_item)
        self.keyin_target_item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end

    if not self.keyin_equip_list then
        self.keyin_equip_list = AsyncListView.New(DaoHangKeYinEquipItemRender, self.node_list.keyin_equip_list)
        self.keyin_equip_list:SetSelectCallBack(BindTool.Bind(self.OnSelectKeYinEquipItemHandler, self))
    end

    if not self.keyin_item_list then
        self.keyin_item_list = {}

        for i = 1, 3 do
            local render = DaoHangKeYinItemRender.New(self.node_list["keyin_item" .. i])
            render:SetClickCallback(BindTool.Bind(self.DaoHangKeYinItemCallback, self))
            self.keyin_item_list[i] = render
            self.keyin_item_list[i]:SetIndex(i)
        end
    end

    if not self.keyin_attr_list then
        self.keyin_attr_list = AsyncListView.New(DaoHangKeYinAttrItemRender, self.node_list.keyin_attr_list)
    end

    if not self.keyin_cost_item then
        self.keyin_cost_item = ItemCell.New(self.node_list.keyin_cost_item)
    end

    self.daohang_keyin_select = -1
    self.daohang_keyin_select_cell = -1
    self.daohang_keyin_select_slot = -1
	self.daohang_keyin_equip_data = {}
    self.daohang_keyin_cell_data = {}

    XUI.AddClickEventListener(self.node_list.btn_keyin_up, BindTool.Bind(self.OnClickKeYinUp, self))
    XUI.AddClickEventListener(self.node_list.btn_keyin_resonance, BindTool.Bind(self.OnClickKeYinResonance, self))
end

function MultiFunctionView:ReleaseKeYinCallBack()
    if self.keyin_target_item then
        self.keyin_target_item:DeleteMe()
        self.keyin_target_item = nil
    end

    if self.keyin_equip_list then
        self.keyin_equip_list:DeleteMe()
        self.keyin_equip_list = nil
    end

    if self.keyin_attr_list then
        self.keyin_attr_list:DeleteMe()
        self.keyin_attr_list = nil
    end

    if self.keyin_item_list then
        for k, v in pairs(self.keyin_item_list) do
            v:DeleteMe()
        end

        self.keyin_item_list = nil
    end

    if self.keyin_cost_item then
        self.keyin_cost_item:DeleteMe()
        self.keyin_cost_item = nil
    end
end

function MultiFunctionView:OnFlushDaoHangKeYin()
    local data_list = MultiFunctionWGData.Instance:GetDaoHangEquipedEquip()
    local remind = MultiFunctionWGData.Instance:GetDaoHangKeYinResonanceRemind()
    local select = self:GetDaoHangKeYinSelect(data_list)

    self.keyin_equip_list:SetDataList(data_list)
    self.keyin_equip_list:JumpToIndex(select)
    self.node_list.btn_keyin_resonance_remind:CustomSetActive(remind)

    local cap = MultiFunctionWGData.Instance:GetDaoHangKeYinCap()
    self.node_list.keyin_cap_value.text.text = cap or 0
end

function MultiFunctionView:GetDaoHangKeYinSelect(data_list)
    if self.daohang_keyin_select > 0 then
        if MultiFunctionWGData.Instance:GetDaoHangKeYinEquipRemindByslot(self.daohang_keyin_equip_data.slot) then
            return self.daohang_keyin_select
        end
    end

    for k, v in pairs(data_list) do
        if MultiFunctionWGData.Instance:GetDaoHangKeYinEquipRemindByslot(v.slot) then
            return k
        end
     end

     self.daohang_keyin_select = self.daohang_keyin_select > 0 and self.daohang_keyin_select or 1
     return self.daohang_keyin_select
end

function MultiFunctionView:OnSelectKeYinEquipItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

    self.daohang_keyin_select = item.index
	self.daohang_keyin_equip_data = item.data
    self.daohang_keyin_select_slot = item.data.slot
    self:FlushKeYinMid()
end

function MultiFunctionView:FlushKeYinMid()
    if IsEmptyTable(self.daohang_keyin_equip_data) or self.daohang_keyin_select < 0 then
        return
    end

    self.keyin_target_item:SetData(self.daohang_keyin_equip_data)

    for i = 1, 3 do
        local slot = self.daohang_keyin_equip_data.slot
        local data = (self.daohang_keyin_equip_data.carve_item_list or {})[i]
        local level = data and data.level or 0
        local remind = MultiFunctionWGData.Instance:GetDaoHangKeYinCellRemindByslotAndSeq(slot, i)
        self.keyin_item_list[i]:SetData({level = level, remind = remind})
    end

    local select_render = self:GetKeYinCellSelect()
    self:DaoHangKeYinItemCallback(select_render, true)
end

function MultiFunctionView:GetKeYinCellSelect()
    if self.daohang_keyin_select_cell > 0 then
        if MultiFunctionWGData.Instance:GetDaoHangKeYinCellRemindByslotAndSeq(self.daohang_keyin_select_slot, self.daohang_keyin_select_cell) then
            return self.keyin_item_list[self.daohang_keyin_select_cell]
        end
    end

    local data_list = self.daohang_keyin_equip_data.carve_item_list
    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if MultiFunctionWGData.Instance:GetDaoHangKeYinCellRemindByslotAndSeq(self.daohang_keyin_select_slot, k) then
                return self.keyin_item_list[k]
            end 
         end
    end

    self.daohang_keyin_select_cell = self.daohang_keyin_select_cell > 0 and self.daohang_keyin_select_cell or 1
    return self.keyin_item_list[self.daohang_keyin_select_cell]
end

function MultiFunctionView:OnClickKeYinUp()
    if IsEmptyTable(self.daohang_keyin_equip_data) or self.daohang_keyin_select < 0 or self.daohang_keyin_select_cell < 0 then
        return
    end

    local data = (self.daohang_keyin_equip_data.carve_item_list or {})[self.daohang_keyin_select_cell]
    local level = data and data.level or 0
    local cfg = MultiFunctionWGData.Instance:GetDaoHangSlotCarveCfg(self.daohang_keyin_select_slot, self.daohang_keyin_select_cell, level)

    if not IsEmptyTable(cfg) then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)

        if item_num >= cfg.cost_item_num then
            TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0)})

            local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tzjmqh_lhl)
            EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["keyin_item" .. self.daohang_keyin_select_cell].transform)
            MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.SLOT_CARVE, self.daohang_keyin_select_slot, self.daohang_keyin_select_cell)
        else
            TipWGCtrl.Instance:OpenItem({item_id = cfg.cost_item_id})
        end
    end
end

function MultiFunctionView:OnClickKeYinResonance()
    local resonance_data = MultiFunctionWGData.Instance:GetDaoHangKeYinResonanceData()
    MultiFunctionWGCtrl.Instance:ShowResonanceView(resonance_data)
end

function MultiFunctionView:DaoHangKeYinItemCallback(cell, force_refresh)
    if nil == cell or nil == cell.data then
		return
	end

    if not force_refresh and self.daohang_keyin_select_cell == cell.index then
        return
    end

    self.daohang_keyin_select_cell = cell.index
    self.daohang_keyin_cell_data = cell.data

    for i = 1, 3 do
        self.keyin_item_list[i]:SetSelecet(i == self.daohang_keyin_select_cell)
    end

    self:FlushKeYinRight()
end

function MultiFunctionView:FlushKeYinRight()
    if IsEmptyTable(self.daohang_keyin_cell_data) or self.daohang_keyin_select_cell < 0 then
        return
    end

    local seq = self.daohang_keyin_select_cell
    local level = self.daohang_keyin_cell_data.level

    local next_level = level + 1
    local cur_level_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotCarveCfg(self.daohang_keyin_select_slot, seq, level)
    local next_level_cfg = MultiFunctionWGData.Instance:GetDaoHangSlotCarveCfg(self.daohang_keyin_select_slot, seq, next_level)
    local not_max_level = not IsEmptyTable(next_level_cfg)

    self.node_list.keyin_arrow:CustomSetActive(not_max_level)
    self.node_list.keyin_next_level_bg:CustomSetActive(not_max_level)
    self.node_list.keyin_current_level.text.text = string.format(Language.Common.LevelNormal, level)
    self.node_list.keyin_next_level.text.text = string.format(Language.Common.LevelNormal, next_level)

    local attr_data = MultiFunctionWGData.Instance:MergeTwoAttrTable(cur_level_cfg, next_level_cfg, 5, true)
    self.keyin_attr_list:SetDataList(attr_data)

    self.node_list.keyin_max_grade_flag:CustomSetActive(not not_max_level)
    self.node_list.keyin_cost_panel:CustomSetActive(not_max_level)

    local cost_enough = false
    if not_max_level then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
        local enough = item_num >= cur_level_cfg.cost_item_num
        self.keyin_cost_item:SetFlushCallBack(function ()
            local right_text = ToColorStr(item_num .. "/" .. cur_level_cfg.cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
            self.keyin_cost_item:SetRightBottomColorText(right_text)
            self.keyin_cost_item:SetRightBottomTextVisible(true)
        end)

        self.keyin_cost_item:SetData({item_id = cur_level_cfg.cost_item_id})
        cost_enough = enough
    end

    self.node_list.btn_keyin_up_remind:CustomSetActive(cost_enough)
end

-----------------------------DaoHangKeYinEquipItemRender----------------------------------
DaoHangKeYinEquipItemRender = DaoHangKeYinEquipItemRender or BaseClass(BaseRender)

function DaoHangKeYinEquipItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
        self.item:SetItemTipFrom(ItemTip.FROM_DAOHANG_EQUIP)
    end
end

function DaoHangKeYinEquipItemRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function DaoHangKeYinEquipItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local name = item_cfg and item_cfg.name or ""
    local remind = MultiFunctionWGData.Instance:GetDaoHangKeYinEquipRemindByslot(self.data.slot)

    self.item:SetData(self.data)
    self.node_list.text.text.text = name
    self.node_list.text_hl.text.text = name
    self.node_list.remind:CustomSetActive(remind)
    self.node_list.red_level.text.text = ((self.data.carve_item_list or {})[1] or {}).level or ""
    self.node_list.blue_level.text.text = ((self.data.carve_item_list or {})[2] or {}).level or ""
    self.node_list.green_level.text.text = ((self.data.carve_item_list or {})[3] or {}).level or ""
end

function DaoHangKeYinEquipItemRender:OnSelectChange(is_select)
    self.node_list["bg"]:CustomSetActive(not is_select)
    self.node_list["bg_hl"]:CustomSetActive(is_select)
end

---------------------------------DaoHangKeYinItemRender---------------------------------
DaoHangKeYinItemRender = DaoHangKeYinItemRender or BaseClass(BaseRender)

function DaoHangKeYinItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.keyin_item, BindTool.Bind(self.OnClick, self))
end

function DaoHangKeYinItemRender:__delete()
    self:CancleTewwn()
    self.click_callback = nil
end

function DaoHangKeYinItemRender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function DaoHangKeYinItemRender:OnClick()
	if self.click_callback then
		self.click_callback(self)
	end
end

function DaoHangKeYinItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.level.text.text = string.format(Language.Common.Level, self.data.level)
    self.node_list.remind:CustomSetActive(self.data.remind)
    self:CancleTewwn()

    if self.data.remind then
        RectTransform.SetAnchoredPositionXY(self.node_list["remind"].rect, -10, -2)
        self.arrow_tweener = self.node_list["remind"].gameObject.transform:DOAnchorPosY(8, 0.45)
        self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
        self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
    end
end

function DaoHangKeYinItemRender:CancleTewwn()
    if self.arrow_tweener then
        self.arrow_tweener:Kill()
        self.arrow_tweener = nil
    end
end

function DaoHangKeYinItemRender:SetSelecet(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

---------------------------------DaoHangKeYinAttrItemRender---------------------------------
DaoHangKeYinAttrItemRender = DaoHangKeYinAttrItemRender or BaseClass(BaseRender)

function DaoHangKeYinAttrItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.attr_name.text.text = self.data.attr_name or ""
    self.node_list.attr_value.text.text = self.data.attr_value or ""
    local can_up = self.data.add_attr_value > 0
    self.node_list.attr_arrow:CustomSetActive(can_up)
    local add_str = can_up and self.data.add_value_str or ""
    self.node_list.attr_add_value.text.text = add_str
end