
TimerQuest = TimerQuest or BaseClass()

function TimerQuest:__init()
	self.quest_list = {}
	self.check_callback_map = {}
	self.check_handle_map = {}
	self.execute_callback_list = {}

	Runner.Instance:AddRunObj(self, 4)
end

function TimerQuest:__delete()
	self.quest_list = {}
	Runner.Instance:RemoveRunObj(self)
end

function TimerQuest:Update(now_time, elapse_time)
	local excute_num = 0

	for k, v in pairs(self.quest_list) do
		if v[4] <= 0 then
			self.quest_list[k] = nil
			self:DelCheckQuest(k)
		else
			if v[3] <= now_time then
				excute_num = excute_num + 1
				self.execute_callback_list[excute_num] = k
				v[3] = now_time
				v[3] = v[3] + v[2]
				v[4] = v[4] - 1
			end
		end
	end

	local quest = nil
	for i=1, excute_num do
		local quest = self.quest_list[self.execute_callback_list[i]]
		self.execute_callback_list[i] = nil
		if nil ~= quest then
			Trycall(quest[1], quest[5]) 	-- 逻辑中尽是报错，如果一个出现报错，将影响其他地方出现BUG
		end
	end
end


function TimerQuest:AddDelayTimer(callback, delay_time, cbdata)
	return self:AddTimesTimer(callback, delay_time, 1, cbdata)
end

function TimerQuest:AddTimesTimer(callback, delay_time, times, cbdata)
	local t = {callback, delay_time, Status.NowTime + delay_time, times, cbdata}
	self.quest_list[t] = t
	return t
end

function TimerQuest:AddRunQuest(callback, delay_time)
	self.check_callback_map[callback] = callback
	local quest = self:AddTimesTimer(callback, delay_time, 999999999)

	self.check_callback_map[callback] = callback
	self.check_handle_map[quest] = callback

	return quest
end

function TimerQuest:InvokeRepeating(callback, start_time, delay_time, times)
	local t = nil
	t = self:AddDelayTimer(function ()
		callback()
		if t then
			t[1] = callback
			t[2] = delay_time
			t[3] = Status.NowTime + delay_time
			t[4] = times
		end
	end, start_time)
	return t
end

function TimerQuest:CancelQuest(quest)
	if quest == nil then return end

	self.quest_list[quest] = nil
	self:DelCheckQuest(quest)
end

function TimerQuest:EndQuest(quest)
	if quest == nil then return end

	if self.quest_list[quest] ~= nil then
		local callback = self.quest_list[quest][1]
		local cbdata = self.quest_list[quest][5]
		self.quest_list[quest] = nil
		self:DelCheckQuest(quest)
		callback(cbdata)
	end
end

function TimerQuest:GetRunQuest(quest)
	if nil == quest then return nil end
	return self.quest_list[quest]
end

function TimerQuest:IsExistsListen(callback)
	return nil ~= self.check_callback_map[callback]
end

function TimerQuest:DelCheckQuest(quest)
	if nil ~= self.check_handle_map[quest] then
		self.check_callback_map[self.check_handle_map[quest]] = nil
		self.check_handle_map[quest] = nil
	end
end

function TimerQuest:GetQuestCount(t)
	t.time_quest_count = 0
	for k,v in pairs(self.quest_list) do
		t.time_quest_count = t.time_quest_count + 1
	end
end

-- 增加延迟调用，在在obj的生命周期结束后将自动移除
function AddDelayCall(obj, callback, delay_time)
	obj.__delay_call_times = obj.__delay_call_times or 0
	obj.__delay_call_times = obj.__delay_call_times + 1
	local key = "__delay_call_times_" .. obj.__delay_call_times % 1000
	ReDelayCall(obj, callback, delay_time, key)
end

-- 延迟调用，将移除obj持有的上一个同key的延迟
function ReDelayCall(obj, callback, delay_time, key)
	__DelayCall(obj, callback, delay_time, key, true)
end

-- 延迟调用，如果当前已有，则不移除上次延迟，等上次延迟执行
function TryDelayCall(obj, callback, delay_time, key)
	__DelayCall(obj, callback, delay_time, key, false)
end

function __DelayCall(obj, callback, delay_time, key, is_recall)
	if "table" ~= type(obj) then
		print_error("[DelayCall]参数错误，请指定正确的obj")
		return
	end

	if "table" ~= type(obj) or nil == obj.DeleteMe then
		print_error("[DelayCall]参数错误，请指定正确的obj")
		return
	end


	if nil == callback then
		print_error("[DelayCall]参数错误，请指定正确的callback")
	end

	if nil == key then
		print_error("[DelayCall]参数错误，请指定唯一的key以便可移除旧的，不需移除请使用AddDelayCall")
		return
	end

	if nil == obj.__delay_call_map then
		obj.__delay_call_map = {}
	end

	if nil ~= obj.__delay_call_map[key] then
		if is_recall then
			GlobalTimerQuest:CancelQuest(obj.__delay_call_map[key])
		else
			return
		end
	end

	local quest = GlobalTimerQuest:AddDelayTimer(function ()
		obj.__delay_call_map[key] = nil
		callback()
	end, delay_time)

	obj.__delay_call_map[key] = quest
end

function CancleDelayCall(obj, key)
	if nil == obj or nil == obj.__delay_call_map or nil == key then
		return
	end

	GlobalTimerQuest:CancelQuest(obj.__delay_call_map[key])
	obj.__delay_call_map[key] = nil
end

function CancleAllDelayCall(obj)
	if nil == obj or nil == obj.__delay_call_map or nil == GlobalTimerQuest then
		return
	end

	for k,v in pairs(obj.__delay_call_map) do
		GlobalTimerQuest:CancelQuest(v)
	end

	obj.__delay_call_map = nil
	obj.__delay_call_times = 0
end
