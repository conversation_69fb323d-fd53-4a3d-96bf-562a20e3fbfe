require("game/shenji_notice/shenji_notice_wg_data")
require("game/shenji_notice/shenji_xianshi_view")
require("game/shenji_notice/shenji_xianshi_weapon_view")
require("game/shenji_notice/shenji_open_anim_view")


--神机预告。
ShenJiNoticeWGCtrl = ShenJiNoticeWGCtrl or BaseClass(BaseWGCtrl)

function ShenJiNoticeWGCtrl:__init()
    if nil ~= ShenJiNoticeWGCtrl.Instance then
        ErrorLog("[ShenJiNoticeWGCtrl]:Attempt to create singleton twice!")
    end
    ShenJiNoticeWGCtrl.Instance = self

    self.data = ShenJiNoticeWGData.New()

    self.shenji_xianshi_view = ShenJiXianShiView.New(GuideModuleName.ShenJiXianShiView)        --神机降世（任务）
    self.shenji_xianshi_weapon_view = ShenJiXianShiWeaponView.New()

    self.shenji_open_anim_view = ShenJiOpenAnimView.New(GuideModuleName.ShenJiOpenAnimView)               --神机降世动画效果

    self:RegisterAllProtocols()
    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function ShenJiNoticeWGCtrl:__delete()
    self.shenji_xianshi_view:DeleteMe()
    self.shenji_xianshi_view = nil

    self.shenji_xianshi_weapon_view:DeleteMe()
    self.shenji_xianshi_weapon_view = nil

    self.shenji_open_anim_view:DeleteMe()
    self.shenji_open_anim_view = nil

    self.data:DeleteMe()
    self.data = nil

    ShenJiNoticeWGCtrl.Instance = nil
end

function ShenJiNoticeWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCShenJiYuGaoInfo, "OnSCShenJiYuGaoInfo")                             --神机预告信息
    self:RegisterProtocol(SCShenJiYuGaoTaskStatusChange, "OnSCShenJiYuGaoTaskStatusChange")     --任务状态改变
    self:RegisterProtocol(SCShenJiYuGaoSpecialSaleInfo, "OnSCShenJiYuGaoSpecialSaleInfo")       --特卖信息改变
end

function ShenJiNoticeWGCtrl:CheckAllTaskFetched()
    local is_all_task_fetched = self.data:IsAllTaskFetched()
    if is_all_task_fetched then
        GlobalTimerQuest:AddDelayTimer(function()
            self:CloseXianShiView()
            self:OpenAnimView()
            HiddenWeaponWGData.Instance.remind_manager:FireAllRemind()
        end, 0.2)
    end
end

--关闭功能
function ShenJiNoticeWGCtrl:CloseFuntion()
    --释放主界面按钮
    --MainuiWGCtrl.Instance:FlushView(0, "ShenJiNoticeBtnRelease")
    FunOpen.Instance:ForceCloseFunByName(FunName.ShenJiNotice,true)
end

--region  协议相关
--神机预告信息
function ShenJiNoticeWGCtrl:OnSCShenJiYuGaoInfo(protocol)
    self.data:SetShenJiNoticeInfo(protocol)
    self:CheckShenJiNoticeFunOpen()
    self.shenji_xianshi_view:Flush()

    RemindManager.Instance:Fire(RemindName.ShenJiNotice)
    RemindManager.Instance:Fire(RemindName.ShenJiXianShi)

    --MainuiWGCtrl.Instance:FlushView(0, "ShenJiNoticeBtnFlush")

    GlobalEventSystem:Fire(ShenJiEventType.ShenJiNotice_TaskInfoChange)
    GlobalEventSystem:Fire(ShenJiEventType.ShenJiNotice_SaleInfoChange)
end

--任务状态改变
function ShenJiNoticeWGCtrl:OnSCShenJiYuGaoTaskStatusChange(protocol)
    self.data:UpdateShenJiTaskStatus(protocol)
    self:CheckAllTaskFetched()
    self:CheckShenJiNoticeFunOpen()

    RemindManager.Instance:Fire(RemindName.ShenJiNotice)
    RemindManager.Instance:Fire(RemindName.ShenJiXianShi)
    GlobalEventSystem:Fire(ShenJiEventType.ShenJiNotice_TaskInfoChange)
end

--特卖信息改变
function ShenJiNoticeWGCtrl:OnSCShenJiYuGaoSpecialSaleInfo(protocol)
    --print_error("jf >>>>>>> 神机预告特卖信息改变", protocol)

    self.data:UpdateShenJiSpecialSaleStatus(protocol)
    --self:CheckShenJiNoticeFunOpen()

    RemindManager.Instance:Fire(RemindName.ShenJiNotice)
    RemindManager.Instance:Fire(RemindName.ShenJiXianShi)
    GlobalEventSystem:Fire(ShenJiEventType.ShenJiNotice_SaleInfoChange, protocol.special_sale_info.now_special_sale_id)
end

local function ShenJiYuGaoOperate(opera_type, param1)
    local send_protocol = ProtocolPool.Instance:GetProtocol(CSShenJiYuGao)
    send_protocol.opera_type = opera_type
    send_protocol.param1 = param1 or 0
    send_protocol:EncodeAndSend()
end

--请求神机预告信息
function ShenJiNoticeWGCtrl:RequestInfo()
    ShenJiYuGaoOperate(SHEN_JI_YU_GAO_OP_TYPE.INFO)
end
--请求领任务奖励, 任务id
function ShenJiNoticeWGCtrl:RequestFetchTaskReward(task_id)
    -- print_error("请求领任务奖励, 任务id:", task_id)
    ShenJiYuGaoOperate(SHEN_JI_YU_GAO_OP_TYPE.GET_TASK_REWARD, task_id)
end
--请求领充值特卖, 特卖id
function ShenJiNoticeWGCtrl:RequestFetchSpecialSale(special_sale_id)
    --print_error("请求领充值特卖, 特卖id:", special_sale_id)
    ShenJiYuGaoOperate(SHEN_JI_YU_GAO_OP_TYPE.GET_SPECIAL_SALE, special_sale_id)
end

--endregion

--是否已经开启了神机预告
function ShenJiNoticeWGCtrl:GetIsOpenedShenJiNotice(is_need_tips)
    local is_open, tips = FunOpen.Instance:GetFunIsOpened(FunName.ShenJiNotice, true)
    if is_need_tips and tips and tips ~= "" then
        SysMsgWGCtrl.Instance:ErrorRemind(tips)
    end

    return is_open
end

--是否已经开启了神机系统
function ShenJiNoticeWGCtrl:GetIsOpenedShenJiSystem(is_need_tips)
    local is_open, tips = FunOpen.Instance:GetFunIsOpened(FunName.ShenJi, true)
    if is_need_tips and tips and tips ~= "" then
        SysMsgWGCtrl.Instance:ErrorRemind(tips)
    end

    return is_open
end


--region 界面操作
function ShenJiNoticeWGCtrl:OpenAnimView()
    -- ShenJiNoticeWGCtrl.Instance:OpenAnimView()
    self.shenji_open_anim_view:Open()
end

function ShenJiNoticeWGCtrl:CloseAnimView()
    self.shenji_open_anim_view:Close()
end

function ShenJiNoticeWGCtrl:OpenXianShiView()
    -- ShenJiNoticeWGCtrl.Instance:OpenXianShiView()
    self.shenji_xianshi_view:Open()
end

function ShenJiNoticeWGCtrl:CloseXianShiView()
    self.shenji_xianshi_view:Close()
end

function ShenJiNoticeWGCtrl:OpenXianShiWeaponView()
    self.shenji_xianshi_weapon_view:Open()
end

function ShenJiNoticeWGCtrl:CloseXianShiWeaponView()
    self.shenji_xianshi_weapon_view:Close()
end

--打开显示特卖界面，param_id：特卖id
function ShenJiNoticeWGCtrl:OpenSpecialSaleView(special_sale_id)
    if self["shenji_special_sale_view" .. special_sale_id] then
        self["shenji_special_sale_view" .. special_sale_id]:Open()
    end
end

--关闭显示特卖界面，param_id：特卖id
function ShenJiNoticeWGCtrl:CloseSpecialSaleView(special_sale_id)
    if self["shenji_special_sale_view" .. special_sale_id] then
        self["shenji_special_sale_view" .. special_sale_id]:Close()
    end
end

function ShenJiNoticeWGCtrl:OnMainUIBtnClickOpenView()
    local is_open_special_sale = self.data:GetNowSpecialSaleStatus() ~= SHEN_JI_YU_GAO_SPECIAL_SALE_STATUS.CLOSE
    if is_open_special_sale then
        --特卖逻辑
        local now_special_sale_id = self.data:GetNowSpecialSaleId()
        self:OpenSpecialSaleView(now_special_sale_id)
    else
        --现世逻辑
        self:OpenXianShiView()
    end
end

--背包装备格子打开界面
function ShenJiNoticeWGCtrl:BagEquipCellClickOpenView()
    local is_open_notice, tips = FunOpen.Instance:GetFunIsOpened(FunName.ShenJiNotice, true)
    if not is_open_notice then
        if tips then 
            tips = tips .. Language.ShenJiNotice.FunName
            SysMsgWGCtrl.Instance:ErrorRemind(tips)
        end
        return
    end

    self:OpenXianShiView()
end
--endregion

function ShenJiNoticeWGCtrl:OnPassDay()
    RemindManager.Instance:Fire(RemindName.ShenJiNotice)
    RemindManager.Instance:Fire(RemindName.ShenJiXianShi)
    GlobalEventSystem:Fire(ShenJiEventType.ShenJiNotice_TaskInfoChange)
end

function ShenJiNoticeWGCtrl:CheckShenJiNoticeFunOpen()
    local is_all_task_fetched = ShenJiNoticeWGData.Instance:IsAllTaskFetched()
    if is_all_task_fetched then
        ShenJiNoticeWGCtrl.Instance:CloseFuntion()
    end
end