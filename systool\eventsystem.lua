require("systool/event")
local develop_mode = require("editor/develop_mode")
local is_develop = develop_mode:IsDeveloper()

EventSystem = EventSystem or BaseClass()
function EventSystem:__init()
	self.is_deleted = false
	self.event_list = {}							-- 事件列表
	self.check_callback_map = {}
	self.check_handle_map = {}
	self.need_fire_events = {}						-- 需要激发的事件(延后调用方式)
	self.event_warning_num_map ={
		[MainUIEventType.MAINUI_OPEN_COMLETE] = 100,
		[OtherEventType.TASK_CHANGE] = 40
	}
end

function EventSystem:__delete()
	self.is_deleted = true
end

--调用已经处于派发队列中的Event
function EventSystem:Update()
	if #self.need_fire_events > 0 then
		local events = self.need_fire_events
		self.need_fire_events = {}

		for k, v in pairs(events) do
			v.event:Fire(v.arg_list)
		end
	end
end

function EventSystem:IsExistsListen(callback)
	local bind_t = self.check_callback_map[callback]
	return bind_t and bind_t.event_id or nil
end

function EventSystem:GetEventNum(t)
	for k, v in pairs(self.event_list) do
		t["global_event : " .. k] = v:GetBindNum()
	end
end

function EventSystem:Bind(event_id, event_func)
	if event_id == nil then
		return
	end

	if self.is_deleted then
		return
	end

	local tmp_event = self.event_list[event_id]
	if tmp_event == nil then
		tmp_event = Event.New(event_id)
		self.event_list[event_id] = tmp_event
	end

	local bind_t = tmp_event:Bind(event_func)

	if is_develop then
		local warning_num = self.event_warning_num_map[event_id] or 30
		local num = tmp_event:GetBindNum()
		if num >= warning_num then
			print_error(string.format("[EventSystem]监听%s事件的地方多达%d条，请检查！", event_id, num))
		end
	end

	self.check_callback_map[event_func] = bind_t
	self.check_handle_map[bind_t] = event_func

	return bind_t
end

function EventSystem:UnBind(event_handle)
	if event_handle == nil or event_handle.event_id == nil then
		return
	end

	if self.is_deleted then
		return
	end

	if nil ~= self.check_handle_map[event_handle] then
		self.check_callback_map[self.check_handle_map[event_handle]] = nil
		self.check_handle_map[event_handle] = nil
	end

	local tmp_event = self.event_list[event_handle.event_id]
	if tmp_event ~= nil then
		tmp_event:UnBind(event_handle)
	end
end

--立即触发
function EventSystem:Fire(event_id, ...)
	if event_id == nil then
		return
	end

	if self.is_deleted then
		return
	end

	local tmp_event = self.event_list[event_id]
	if tmp_event ~= nil then
		tmp_event:Fire({...})
	end
end

--下一帧触发
function EventSystem:FireNextFrame(event_id, ...)
	if event_id == nil then
		return
	end

	if self.is_deleted then
		return
	end

	local tmp_event = self.event_list[event_id]
	if tmp_event ~= nil then
		table.insert(self.need_fire_events, {event = tmp_event, arg_list = {...}})
	end
end

function EventSystem:GetDebugEventCount(t)
	t.event_count = 0
	for k, v in pairs(self.event_list) do
		t.event_count = t.event_count + v.bind_num
	end
end