SceneScreenEffectWGData = SceneScreenEffectWGData or BaseClass(BaseWGCtrl)
function SceneScreenEffectWGData:__init()
	if SceneScreenEffectWGData.Instance ~= nil then
		ErrorLog("[SceneScreenEffectWGData] attempt to create singleton twice!")
		return
	end
	SceneScreenEffectWGData.Instance = self

	self.cfg = ListToMapList(ConfigManager.Instance:GetAutoConfig("function_guide_auto").scene_effect, "scene_id")

	self.change_scene_handle = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))
	-- 已触发列表
	self.triggered_list = {}
end

function SceneScreenEffectWGData:__delete()
	SceneScreenEffectWGData.Instance = nil

	if self.change_scene_handle then
		GlobalEventSystem:UnBind(self.change_scene_handle)
		self.change_scene_handle = nil
	end
end

function SceneScreenEffectWGData:GetConfig()
	return self.cfg
end

function SceneScreenEffectWGData:OnSceneChangeComplete()
	self.cur_scene_effect_cfg = nil
end

-- 当前场景特效配置
function SceneScreenEffectWGData:GetCurSceneEffectCfg()
	if self.cur_scene_effect_cfg then
		return self.cur_scene_effect_cfg
	end

	local scene_id = Scene.Instance:GetSceneId()
	local all_cfg = self:GetConfig()[scene_id] or {}
	self.cur_scene_effect_cfg = {}
	for i,v in ipairs(all_cfg) do
		local data = {}
		data.cfg = v
		data.pos_list = {}
		local pos_str_list = Split(v.pos_list, "#")
		for j, k in ipairs(pos_str_list) do
			local pos = {}
			local pos_str = Split(k, ",")
			pos.x = tonumber(pos_str[1])
			pos.y = tonumber(pos_str[2])
			table.insert(data.pos_list, pos)
		end

		if #data.pos_list < 3 then
			print_error("G-功能引导-场景镜头特效配置有误，区域点数目必须大于等于三个！")
		end

		table.insert(self.cur_scene_effect_cfg, data)
	end

	return self.cur_scene_effect_cfg
end
