--boss奖励预览面板
BossFirstKillRewardView = BossFirstKillRewardView or BaseClass(SafeBaseView)
local ATTR_COUNT = 10
function BossFirstKillRewardView:__init()
    self.view_name = "BossFirstKillRewardView"
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_first_killreward")
	self:SetMaskBg(true)
end

function BossFirstKillRewardView:OpenCallBack()

end

function BossFirstKillRewardView:LoadCallBack()
    self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.Close, self))
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    end
    self.des_list = {}
    for i = 1, ATTR_COUNT do
		self.des_list[i] = self.node_list.des_list:FindObj("text" .. i)
	end
end

function BossFirstKillRewardView:ReleaseCallBack()
    if nil ~= self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    self.des_list = {}
end

function BossFirstKillRewardView:SetBossId(boss_id)
    self.boss_id = boss_id
end

function BossFirstKillRewardView:OnFlush()
    if not self.boss_id then
        return
    end
    self.data = BossWGData.Instance:GetBossInfoByBossId(self.boss_id)
    if not self.data then
        return
    end
    local list = {{item_id = self.data.world_firstkill_reward, },}

    self.reward_list:SetDataList(list)
    local FirstKillDes
    if self.data.world_firstkill_times == -1 then
        FirstKillDes = Language.Boss.InfiniteFirstKillDes
    else
        FirstKillDes = Language.Boss.FirstKillDes
    end

    local reward_tips_des = Split(FirstKillDes, "\n") 
    local index = #reward_tips_des
    for i = 1, ATTR_COUNT do
        self.des_list[i]:SetActive(i <= index)
        if i <= index then
            self.des_list[i].text.text = reward_tips_des[i]
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.des_list[i].rect)
        end
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.des_list.rect)
   
end



