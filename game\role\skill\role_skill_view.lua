------------------------------------------------------------
--人物技能View.升级学习相关
------------------------------------------------------------
SkillView = SkillView or BaseClass(SafeBaseView)

ZhuDongSkillType = {
	JueSeSkill = 1,
	BianShenSkill = 2,
}

SkillView.NowView ={
	Break = 1,
	UpLevel = 2
}

function SkillView:__init()
	self.view_style = ViewStyle.Full
	self.view_name = GuideModuleName.SkillView
	self:SetMaskBg(false)
	self:LoadConfig()
	self.is_safe_area_adapter = true
	self.tab_sub = {Language.Skill.TabSub1}
  	self.remind_tab = {
		{RemindName.SkillUpLevelAll, RemindName.SkillAwake},
		{},
		{RemindName.TalentSkill},
  	}
end

function SkillView:__delete()

end

function SkillView:LoadConfig()
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource({TabIndex.skill_zhudong, TabIndex.skill_awake}, "uis/view/role_ui_prefab", "layout_skill")
	self:AddViewResource(TabIndex.skill_beidong, "uis/view/role_ui_prefab", "layout_skill_beidong")
	self:AddViewResource(TabIndex.skill_talent, "uis/view/role_ui_prefab", "layout_role_talent")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")
	self:AddViewResource(0, "uis/view/role_ui_prefab", "HorizontalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function SkillView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
		show_gold = true, show_bind_gold = true,
		show_coin = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(Language.Skill.TabGrop, self.tab_sub, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.SkillView, self.tabbar)
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.SkillView, self.get_guide_ui_event)

	local bundle, asset = ResPath.GetRawImagesJPG("a3_jn_icon_bj")
	if self.node_list.RawImage_tongyong then
	 	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function ()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function SkillView:LoadIndexCallBack(index, loaded_times)
	if index == TabIndex.skill_zhudong or index == TabIndex.skill_awake then
		if not self.flush_skill_info then
			self:InitSkillView()
			self.flush_skill_info = GlobalEventSystem:Bind(SkillEventType.FLUSH_SKILL_LIST, BindTool.Bind1(self.FlushZhuDongView, self))
		end


		-- if not self.skill_pre_view then
		-- 	self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage) 
		-- 	self.skill_pre_view:SetPreviewPlayEndCb(BindTool.Bind1(self.PreviewPlayEnd, self))
		-- end
	--[[elseif index == TabIndex.skill_upgrade then
		self:InitSkillUpGradeView()--]]
	elseif index == TabIndex.skill_beidong then
		self:InitPassiveView()
	elseif index == TabIndex.skill_talent then
		self:InitRoleTalentView()
		if not self.flush_talent_info then
			self.flush_talent_info = GlobalEventSystem:Bind(SkillEventType.FLUSH_TALENT_INFO,BindTool.Bind1(self.FlushRoleTalentView, self))
		end
	end
end

function SkillView:ReleaseCallBack()
	self:DeleteSkillView()
	self:DeleteSkillUpGradeView()
	self:DeleteRoleTalentView()
	self:DeleteIntroView()

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	-- if self.skill_pre_view then
	-- 	self.skill_pre_view:DeleteMe()
	-- 	self.skill_pre_view = nil
	-- end

	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.SkillView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	self.origin_display_pos = nil
	self:RemovePreSkillDelayTimer()
	self:RemoveDelayCloseTimer()
end

function SkillView:OpenCallBack()
	TaskGuide.Instance:SideTOStopTask(true)
	RoleWGCtrl.Instance:SendRoleTelentOperate(ROLE_TALENT_OPERATE_TYPE.ROLE_TALENT_OPERATE_TYPE_INFO) -- 请求天赋信息
end

function SkillView:HandleHideEffect()
	if self.node_list["telent_effect_root"] then
		self.node_list["telent_effect_root"]:SetActive(false)
	end
end

function SkillView:CloseCallBack()
	self.old_index = nil
	self:StopUpLeveling()

	TipWGCtrl.Instance:ForceHideEffect()
	TaskGuide.Instance:SideTOStopTask(false)

end

function SkillView:ShowIndexCallBack(index)
	TipWGCtrl.Instance:ForceHideEffect()
	self:HandleHideEffect()
	self:StopUpLeveling()
	if index == TabIndex.skill_zhudong then
		self:OnClickSwitchUpLevel()
		self:FlushZhuDongView(1)
		self:CheckSKillItemRed(index)
	elseif index == TabIndex.skill_awake then
		if not self.old_index then
			self:FlushZhuDongView(1)
		end
		self:OnClickSwitchBreak()
		self:CheckSKillItemRed(index)
	elseif index == TabIndex.skill_talent then

	end
	self.node_list.title_view_name.text.text = Language.ViewName[GuideModuleName.SkillView]
	self.old_index = index
end

function SkillView:OnFlush(param, index)
    if index == TabIndex.skill_awake then
    	if param ~= nil then
    		for k,v in pairs(param) do
    			if k == "all" then
    				if v.to_ui_param ~= nil then
    					local select_index = tonumber(v.to_ui_param)
    					if select_index ~= nil and select_index ~= 0 then
    						self.def_skill_index = select_index
    						self:FlushZhuDongView(1)
    						break
    					end
    				end
				end
    		end
    	end
	elseif index == TabIndex.skill_zhudong then
		if param ~= nil then
    		for k,v in pairs(param) do
    			if k == "auto" then
					self:TriggerCloseOperate()
    			end
    		end
    	end
    end
end

function SkillView:InitSkillView()
	self.cur_select_zhudongskill_type = ZhuDongSkillType.JueSeSkill   -- 主动技能 1.角色 2.变身
	self.cur_skill_item = nil 			-- 当前选择的skill_item

	self.skill_item_list = {}
	local item
	self.role_skill_obj_list = {}
	for i = 1, 4 do
		item = SkillLearnItemRender.New(self.node_list['skill_' .. i])
		item:SetIndex(i)
		item:SetClickCallBack(BindTool.Bind(self.SelectSkillItemHandler,self))
		table.insert(self.role_skill_obj_list,item)
	end

	if not self.up_level_cell then
		self.up_level_cell = ItemCell.New(self.node_list["up_level_cell"])
	end

	self.def_skill_index = RoleWGData.Instance:GetRedSkillSelectId()
	self.cur_skill_tabbar_index = 1 	-- 同等与技能类型  1.主动   2.被动

	-- self.node_list["btn_skill_set"].button:AddClickListener(BindTool.Bind1(self.OpenSkillPeiZi, self))
	self.node_list["btn_skill_uplevel"].button:AddClickListener(BindTool.Bind1(self.OnClickUpLevel, self))
	self.node_list["btn_skill_upleveling"].button:AddClickListener(BindTool.Bind(self.OnClickUpLeveling, self))
	self.node_list["btn_break"].button:AddClickListener(BindTool.Bind(self.OnClickBtnBreak, self))
	self.node_list["btn_skill_pre_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBtnSkillPre, self))
	-- self.node_list["btn_tips"].button:AddClickListener(BindTool.Bind(self.OnClickBtnTips, self))
	--self.node_list["auto_btn"].button:AddClickListener(BindTool.Bind1(self.ClickAuto, self))
	self.node_list["txt_noskill_tip1"].text.text = Language.Skill.NoSkillTip1
	self.node_list["txt_noskill_tip2"].text.text = Language.Skill.NoSkillTip2
	-- effectPlayer("UI_jiNeng_up")
	-- self.skill_preview = SkillPreview.New(self.node_list["skill_pre_rawimage"], self.node_list["skill_pre_rawimage"])
end

function SkillView:DeleteSkillView()
	if nil ~= self.skill_item_list then
		for k,v in ipairs(self.skill_item_list) do
			v:DeleteMe()
		end
		self.skill_item_list = nil
	end

	if self.flush_talent_info then
		GlobalEventSystem:UnBind(self.flush_talent_info)
		self.flush_talent_info = nil
	end

	if self.flush_skill_info then
		GlobalEventSystem:UnBind(self.flush_skill_info)
		self.flush_skill_info = nil
	end

	if self.skill_role_display then
		self.skill_role_display:DeleteMe()
		self.skill_role_display = nil
	end

	if self.zhudong_listview then
		self.zhudong_listview:DeleteMe()
		self.zhudong_listview = nil
	end

	if nil ~= self.role_skill_obj_list then
		for k,v in pairs(self.role_skill_obj_list) do
			v:DeleteMe()
		end
		self.role_skill_obj_list = nil
	end

	if self.break_item_cell then
		for i, v in pairs(self.break_item_cell) do
			v:DeleteMe()
		end
		self.break_item_cell = nil
	end

	if self.up_level_cell then
		self.up_level_cell:DeleteMe()
		self.up_level_cell = nil
	end

	if self.skill_preview then
        self.skill_preview:DeleteMe()
        self.skill_preview = nil
    end

	self.uplevel_effct_attach = nil
	self.wait_play_list = nil
	self.skill_data = nil
	self.cur_skill_item = nil
end

function SkillView:CheckSKillItemRed(index)
	if self.role_skill_obj_list then
		for i, v in pairs(self.role_skill_obj_list) do
			v:SetRedType(index)
		end
	end
end

function SkillView:FlushZhuDongView(index)
	if not self.role_skill_obj_list then return end
	if type(index) ~= "number" then
		index = self.cur_skill_tabbar_index
	end
	self.cur_skill_tabbar_index = index or self.cur_skill_tabbar_index

	local data = SkillWGData.Instance:GetSkillListByType(self.cur_skill_tabbar_index)   -- 主动技能列表
	for i,v in ipairs(self.role_skill_obj_list) do
		if data[i] then
			v:SetData(data[i])
		else
			v:SetData({})
		end
		v:SetRedType(self.show_index)
	end

	if self.def_skill_index then   -- 选择默认技能item
		self.role_skill_obj_list[self.def_skill_index]:OnClick()
		self.def_skill_index = nil
	elseif self.cur_skill_item then
		self.role_skill_obj_list[self.cur_skill_item]:OnClick()
	end
	self:FlushSkillFightPower(data)
	if self.cur_skill_item then   -- 技能item高亮
		--策划又说不要了
		--一键升级的时候才调用此方法
		-- if self.is_goon_upleveling then
		-- 	self:SelectCanRemind(data,self.cur_skill_item)
		-- 	--等刷新后在对继续刷新进行赋值
		-- 	self.is_goon_upleveling = self.is_upleveling
		-- 	self:SelectSkillItemHandler(self.role_skill_obj_list[self.cur_skill_item])
		-- end
		self.role_skill_obj_list[self.cur_skill_item]:OnChangeHighLight(true)
	end

	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local img_asset, img_name = ResPath.GetProfRawImagesPNG(sex, prof)
	self.node_list["prof_name"].raw_image:LoadSprite(img_asset, img_name, function()
		self.node_list["prof_name"].raw_image:SetNativeSize()
	end)

    local bundle, asset = RoleWGData.Instance:GetJobProfEffect(sex, prof)
    if bundle and asset then
        self.node_list["prof_name"]:ChangeAsset(bundle, asset)
    end

	local cfg = ConfigManager.Instance:GetAutoConfig("roleskill_auto").normal_skill
    local aim_skill_id = nil
    for index, v in pairs(cfg) do
        if v.prof_limit == prof and v.sex_limit == sex then
            aim_skill_id = v.skill_id
			break
        end
    end

	if aim_skill_id then
		local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(aim_skill_id)
		if skill_cfg then
			local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon_resource)
			self.node_list.normal_skill_icon.image:LoadSprite(bundle, asset, function ()
				self.node_list.normal_skill_icon.image:SetNativeSize()
			end)
		end
	end
end

--一键刷新的时候自动选中可以升级的（策划又说不要了）
function SkillView:SelectCanRemind(data,cur_skill_item)
	--先判断当前的是否还存在红点
	local callback = function(cfg)
		if cfg.skill_id == nil or cfg.skill_id == 0 then
			return
		end
		local skill_info = SkillWGData.Instance:GetSkillInfoById(cfg.skill_id)
		if RoleWGData.Instance:GetSkillCanUpLevelBySkillId(cfg.skill_id, skill_info) or RoleWGData.Instance:GetSkillCanBreakBySkillId(cfg.skill_id, skill_info) then
			return true
		end
	end

	if callback(data[cur_skill_item]) then
		self.cur_skill_item = cur_skill_item
		return
	end

	for k,v in pairs(data) do
		if callback(v) then
			self.cur_skill_item = k
			return
		end
	end

	self.cur_skill_item = cur_skill_item
end

-- 选中技能回调刷新界面
function SkillView:SelectSkillItemHandler(item)
	if item == nil or nil == item.data then
		return
	end

	if self.cur_skill_item ~= item.index then
		self.cur_skill_item = item.index
		self:StopUpLeveling()
	end

	for i,v in ipairs(self.role_skill_obj_list) do
		v:OnChangeHighLight(self.cur_skill_item == i)
	end

	local is_noactive = item.data.skill_id == nil
	self.node_list["no_skill_info"]:SetActive(is_noactive)
	self.node_list["right_uplevel_panel"]:SetActive(not is_noactive)
	if is_noactive then
		return
	end
	self.skill_data = item.data
	self:FlushNowView()
	self:FlushUpLevelRemind()
	-- self:SelectPlaySkillPre()
end


function SkillView:SelectPlaySkillPre()
	if not self.skill_data then
		return
	end

	-- if self.skill_pre_view:GetPreviewIsLoaded() then
	-- 	self.skill_pre_view:PlaySkill(self.skill_data.skill_id)
	-- else
	-- 	self.skill_pre_view:SetPreviewLoadCb(function()
	-- 		self.skill_pre_view:PlaySkill(self.skill_data.skill_id)
	-- 	end)
	-- end
end

function SkillView:FlushUpLevelRemind()
	local skill_info = SkillWGData.Instance:GetSkillInfoById(self.skill_data.skill_id)

	local uplevel_remind = RoleWGData.Instance:GetSkillCanUpLevelBySkillId(self.skill_data.skill_id, skill_info)
	self.node_list["btn_skill_uplevel_red"]:SetActive(uplevel_remind)
	self.node_list["btn_skill_upleveling_red"]:SetActive(uplevel_remind)

	local break_red = RoleWGData.Instance:GetSkillCanBreakBySkillId(self.skill_data.skill_id, skill_info)
	self.node_list["btn_break_red"]:SetActive(break_red)
end

-- 打开技能配置
function SkillView:OpenSkillPeiZi()
	SkillWGCtrl.Instance:OpenSkillCfgView(self.cur_select_zhudongskill_type)
end

function SkillView:OnClickUpLevel(is_auto)
	if not self:CheckEnoughUpLevel(is_auto) then
		return
	end
	if is_auto then
		SkillWGCtrl.Instance:SendCareerUpgrade(CAREER_UPGRADE_OPERA.UPLEVEL_ONCE)
	else
		SkillWGCtrl.Instance:SendCareerUpgrade(CAREER_UPGRADE_OPERA.UPLEVEL, self.skill_data.skill_id)
	end
end

function SkillView:CheckEnoughUpLevel(is_auto)
	if not is_auto then
		if not self.skill_data then
			return false
		end

		if not RoleWGData.Instance:CheckEnoughSkillUp(self.skill_data.skill_id) then
			return false
		end
	end

	return true
end

function SkillView:OnClickUpLeveling()
	self:StartUpLeveling()
end

function SkillView:StartUpLeveling()
	self:OnClickUpLevel(true)
	self.node_list["txt_skill_upleveling"].text.text = Language.Skill.StopUpLevel

	self:StopUpLeveling()
end

function SkillView:StopUpLeveling()
	if self.up_level_delay then
		GlobalTimerQuest:CancelQuest(self.up_level_delay)
		self.up_level_delay = nil
	end
	--self.is_upleveling = false
	if self.node_list["txt_skill_upleveling"] then
		self.node_list["txt_skill_upleveling"].text.text = Language.Skill.UpLeveling
	end
end

function SkillView:OnClickBtnBreak()
	if self.skill_data == nil then
		return
	end

    local skill_info = SkillWGData.Instance:GetSkillInfoById(self.skill_data.skill_id)
	if not skill_info then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Skill.NoActive)
		return
	end
    local next_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(self.skill_data.skill_id, skill_info.awake_level + 1)
	for i = 1, 10 do
		local id = next_break_cfg["consume_stuff_id" .. i]
		if id then
			local need_num = next_break_cfg["consume_count" .. i]
			if need_num > 0 then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(id)
				if has_num < need_num then
					TipWGCtrl.Instance:OpenItemTipGetWay({item_id = id})
					return
				end
			end
		else
			break
		end
	end

	if  self.one_effct then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_juexing, is_success = true,pos = Vector2(0, 0), parent_node = self.node_list["effect_juexing"]})
		self.one_effct = false
	end
	SkillWGCtrl.Instance:SendCareerUpgrade(CAREER_UPGRADE_OPERA.AWAKE, self.skill_data.skill_id)
end

function SkillView:OnClickBtnTips()
	RuleTip.Instance:SetContent(Language.Skill.TipContent, Language.Skill.TipTitle, nil, nil, true)
end

function SkillView:OnClickSwitchBreak()
	self.node_list["uplevel_content"]:SetActive(false)
	self.node_list["break_content"]:SetActive(true)
	self.now_view = SkillView.NowView.Break
	self:FlushNowView()
	self:StopUpLeveling()
end

function SkillView:OnClickSwitchUpLevel()
	self.node_list["uplevel_content"]:SetActive(true)
	self.node_list["break_content"]:SetActive(false)
	self.now_view = SkillView.NowView.UpLevel
	self:FlushNowView()
end

-- 查看技能
function SkillView:OnClickBtnSkillPre()
	SkillWGCtrl.Instance:OpenRoleSkillPreView(self.cur_skill_item)
end

function SkillView:FlushNowView()
	if self.now_view == SkillView.NowView.UpLevel then
		self:FlushUpLevelView()
	elseif self.now_view == SkillView.NowView.Break then
		self:FlushBreakView()
	end
end

function SkillView:FlushSKillInfo(is_uplevel, skill_info)
	local skill_break_cfg,next_break_cfg
	if skill_info then
		skill_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(self.skill_data.skill_id, skill_info.awake_level)
		next_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(self.skill_data.skill_id, skill_info.awake_level + 1)
	else
		next_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(self.skill_data.skill_id, 1)
	end

	if skill_info then
		self.node_list["txt_select_skill_level"].text.text = string.format(Language.Rank.Level, ToColorStr(skill_info.level, COLOR3B.DEFAULT_NUM))
	else
		self.node_list["txt_select_skill_level"].text.text = ""
	end

	if is_uplevel or next_break_cfg == nil then
		self.node_list["skill_break_arrow"]:SetActive(false)
		self.node_list["next_skill_info"]:SetActive(false)
	else
		self.node_list["skill_break_arrow"]:SetActive(true)
		self.node_list["next_skill_info"]:SetActive(true)
	end

	if skill_break_cfg then
		self.node_list["cur_break_skill_eff"]:SetActive(true)
		self.node_list["cur_aweak_img"]:SetActive(true)
		self.node_list["cur_uplevel_aweak_img"]:SetActive(true)
		self.node_list["up_level_break_eff"]:SetActive(true)
		self:LoadSkillImg(self.node_list["cur_uplevel_skill_icon"], skill_break_cfg.skill_icon_id)
		self:LoadSkillImg(self.node_list["cur_break_skill_icon"], skill_break_cfg.skill_icon_id)
	else
		self.node_list["cur_break_skill_eff"]:SetActive(false)
		self.node_list["cur_aweak_img"]:SetActive(false)
		self.node_list["cur_uplevel_aweak_img"]:SetActive(false)
		self.node_list["up_level_break_eff"]:SetActive(false)
		self:LoadSkillImg(self.node_list["cur_uplevel_skill_icon"])
		self:LoadSkillImg(self.node_list["cur_break_skill_icon"])
	end
	self.node_list["rich_uplevel_skill_name"].text.text = self.skill_data.skill_name
	self.node_list["rich_break_skill_name"].text.text = self.skill_data.skill_name

	if next_break_cfg then
		if skill_info then
			local skill_name = self.skill_data.skill_name
			local index = string.find(skill_name, "·")
			if index then
				local str = string.sub(skill_name, 1, index - 1)
				skill_name = string.gsub(skill_name, str, next_break_cfg.skill_name)
			else
				skill_name = next_break_cfg.skill_name .. "·" .. skill_name
			end
			self.node_list["rich_break_next_skill_name"].text.text = skill_name
		else
			self.node_list["txt_select_skill_level"].text.text = ""
		end
		self:LoadSkillImg(self.node_list["next_break_skill_icon"], next_break_cfg.skill_icon_id)
	end
end

function SkillView:FlushBreakView()
	if not self.skill_data then
		return
	end
	self.node_list["rich_skill_lv"].text.text = ""
	self.node_list["not_active_state"].text.text = ""
	local skill_info = SkillWGData.Instance:GetSkillInfoById(self.skill_data.skill_id)
	local skill_break_cfg,next_break_cfg
	if skill_info then
		skill_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(self.skill_data.skill_id, skill_info.awake_level)
		next_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(self.skill_data.skill_id, skill_info.awake_level + 1)
	else
		next_break_cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(self.skill_data.skill_id, 1)
	end
	self:FlushSKillInfo(false, skill_info)
    local desc
	if next_break_cfg then
		self.node_list["skill_break_arrow"]:SetActive(true)
		self.node_list["next_skill_info"]:SetActive(true)
		self.node_list["img_has_break"]:SetActive(false)
		self.node_list["btn_break"]:SetActive(true)
		self.one_effct = true            --避免按钮被连续点击产生多次特效
		desc = SkillWGData.Instance:GetSkillBreakDesc(self.skill_data.skill_id, next_break_cfg.desc, false)
	else
		desc = skill_break_cfg and SkillWGData.Instance:GetSkillBreakDesc(self.skill_data.skill_id, skill_break_cfg.desc, false) or ""
		self.node_list["skill_break_arrow"]:SetActive(false)
		self.node_list["next_skill_info"]:SetActive(false)
		self.node_list["img_has_break"]:SetActive(true)
		self.node_list["btn_break"]:SetActive(false)
	end

    self.node_list["rich_cur_break_content"].text.text = desc
	self:FlushBreakCell(next_break_cfg)
end

function SkillView:FlushUpLevelView()
	if not self.skill_data then
		return
	end

	local cfg = SkillWGData.Instance:GetSkillClientConfig(self.skill_data.skill_id)
	local desc = cfg.description

	local skill_info = SkillWGData.Instance:GetSkillInfoById(self.skill_data.skill_id)
	local skill_level_cfg = SkillWGData.Instance:GetSkillLevelCfgByLevel(self.skill_data.skill_id, skill_info and skill_info.level or 1)
	self:FlushSKillInfo(true, skill_info)
	if skill_info then
		local cd_str = self.skill_data.cd_s / 1000
		self.node_list["rich_skill_lv"].text.text = string.format(Language.Skill.SkillCd, cd_str)
		self.node_list["not_active_state"].text.text = ""
		self.node_list["uplevel_attr_arrow"]:SetActive(false)
		self.node_list["uplevel_attr_add_value"]:SetActive(false)

		local next_level_cfg = SkillWGData.Instance:GetSkillLevelCfgByLevel(self.skill_data.skill_id, skill_info and skill_info.level+1)
		local curr_fix_hurt = 0

		if skill_level_cfg then
			self.node_list["uplevel_attr_name"].text.text = Language.Skill.Fix_hurt
			self.node_list["uplevel_attr_value"].text.text = skill_level_cfg.fix_hurt
			curr_fix_hurt = skill_level_cfg.fix_hurt
		end

		if next_level_cfg then
			self.node_list["uplevel_attr_add_value"]:SetActive(true)
            self.node_list["uplevel_attr_add_value"].text.text = next_level_cfg.fix_hurt - curr_fix_hurt
			self.node_list["uplevel_attr_arrow"]:SetActive(true)
			self.node_list["can_up_info"]:SetActive(true)
			self.node_list["img_max_level"]:SetActive(false)
			local role_info = RoleWGData.Instance:GetRoleInfo()
			if next_level_cfg.role_level > role_info.level then
				self.node_list["can_up_info"]:SetActive(false)
				self.node_list["uplevel_limit"].text.text = Language.Skill.LevelLimit .. string.format(Language.Common.LevelNormal, RoleWGData.GetLevelString(next_level_cfg.role_level))
			else
				self.node_list["can_up_info"]:SetActive(true)
				self.node_list["uplevel_limit"].text.text = ""
			end

			local consume_coin_str = CommonDataManager.ConverMoneyByThousand(next_level_cfg.consume_coin)
			local roleinfo_coin_str = CommonDataManager.ConverMoneyByThousand(role_info.coin)
			local str = string.format("%s/%s", consume_coin_str, roleinfo_coin_str)
			local color = role_info.coin < next_level_cfg.consume_coin and COLOR3B.D_RED or COLOR3B.DEFAULT_NUM
			self.up_level_cell:SetData({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN})
			self.node_list["up_level_money"].text.text = ToColorStr(str, color)
		else
			self.node_list["can_up_info"]:SetActive(false)
			self.node_list["img_max_level"]:SetActive(true)
			self.node_list["uplevel_attr_add_value"].text.text = ""
		end
	else
		if self.cur_skill_item > 4 then
			self.node_list["not_active_state"].text.text = Language.Skill.OpenTip_1 --技能激活条件方向不确定，写死5转
		else
			local info = SkillWGData.Instance:GetTaskSkillOpen(self.skill_data.skill_id)
			self.node_list["not_active_state"].text.text = info and info.unlock_desc
		end
		self.node_list["can_up_info"]:SetActive(false)
		self.node_list["img_max_level"]:SetActive(false)
		local cd_str = self.skill_data.cd_s / 1000
		self.node_list["rich_skill_lv"].text.text = string.format(Language.Skill.SkillCd, cd_str)
	end

    desc = SkillWGData.Instance:GetSKillDescBySkillId(self.skill_data.skill_id)

	self.node_list["rich_cur_content"].text.text = desc
end

function SkillView:LoadSkillImg(node, icon_id)
	local bundle, asset
	if icon_id then
		bundle, asset = ResPath.GetSkillIconById(icon_id)
	else
		bundle, asset = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.skill_data.skill_id))
	end
	node.image:LoadSprite(bundle, asset)
	node:SetActive(true)
end

function SkillView:FlushBreakCell(next_break_cfg)
	if not self.break_item_cell then
		self.break_item_cell = {}
	end

	if next_break_cfg then
		for i = 1, 10 do
			local id = next_break_cfg["consume_stuff_id" .. i]
			if id then
				if not self.break_item_cell[i] then
					self.break_item_cell[i] = ItemCell.New(self.node_list["break_cell"])
                    self.break_item_cell[i]:SetNeedItemGetWay(true)
				end

				local need_num = next_break_cfg["consume_count" .. i]
				if need_num > 0 then
					self.break_item_cell[i]:SetActive(true)
					self.break_item_cell[i]:SetData({item_id = id})
					local has_num = ItemWGData.Instance:GetItemNumInBagById(id)
					local color = has_num < need_num and ITEM_NUM_COLOR.NOT_ENOUGH or ITEM_NUM_COLOR.ENOUGH
					local str = has_num .. "/" .. need_num
    				self.break_item_cell[i]:SetRightBottomTextVisible(true)
					self.break_item_cell[i]:SetRightBottomColorText(str, color)
				else
					self.break_item_cell[i]:SetActive(false)
					
				end
			else
				break
			end
		end

		self.node_list["break_cell"]:SetActive(true)
	else
		for i, v in pairs(self.break_item_cell) do
			v:SetActive(false)
		end

		self.node_list["break_cell"]:SetActive(false)
	end
end

function SkillView:FlushSkillFightPower(data)
	local num = 0
	for i, v in pairs(data) do
		local skill_info = SkillWGData.Instance:GetSkillInfoById(v.skill_id)
		if skill_info then
			local cfg = SkillWGData.Instance:GetSkillLevelCfgByLevel(v.skill_id, skill_info.level)
			num = cfg and num + (cfg.capability or 0) or num
			cfg = SkillWGData.Instance:GetSkillBreakCfgByLevel(v.skill_id, skill_info.level)
			num = cfg and num + (cfg.capability or 0) or num
		end
	end
	self.node_list["fight_power_num"].text.text = math.floor(num)
end

function SkillView:GetGuideUiCallBack(ui_name, ui_param)
	local cell = nil
	local click_callback = nil
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end
	elseif ui_name == GuideUIName.BtnSkillUpLevel then
		return self.node_list["btn_skill_uplevel"], BindTool.Bind1(self.OnClickUpLevel, self)
	elseif ui_name == GuideUIName.BtnSkillUpLeveling then
		return self.node_list["btn_skill_upleveling"], BindTool.Bind(self.OnClickUpLeveling, self)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end


local QualityText = {
	[1] = {
		[0] = Color.New(1, 1, 1, 1),
		[1] = Color.New(254/255, 240/255, 91/255, 1),
		[2] = Color.New(254/255, 235/255, 235/255, 1),
		[3] = Color.New(57/255, 0, 0, 1)},		-- 黄色渐变
	[2] = {
		[0] = Color.New(1, 1, 1, 1),
		[1] = Color.New(1, 248/255, 134/255, 1),
		[2] = Color.New(1, 1, 1, 1),
		[3] = Color.New(164/255, 0, 0, 1)},		-- 红色渐变

	[3] = {
		[0] = Color.New(114/255, 235/255, 169/255, 1),
		[3] = Color.New(0, 0, 0, 1)},		-- 绿色描边
}

function SkillView:SetCurSkillTabbarIndex(cur_skill_tabbar_index)
	self.cur_skill_tabbar_index = cur_skill_tabbar_index
end

--技能升级成功特效
function SkillView:CreateSkillUpGradeSucessEffect()
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0)})
end


function SkillView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if (self:GetShowIndex() == TabIndex.skill_awake  or self:GetShowIndex() == TabIndex.skill_zhudong) 
		and RoleWGData.Instance:GetIsSkillAwakeItem(change_item_id) then
		self:FlushZhuDongView()
		self:FlushNowView()
	end
end

--移除回调
function SkillView:RemovePreSkillDelayTimer()
    if self.show_pre_skill_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_pre_skill_delay_timer)
        self.show_pre_skill_delay_timer = nil
    end
end

-- 技能预览结束延迟4秒继续播放
-- function SkillView:PreviewPlayEnd()
-- 	self:RemovePreSkillDelayTimer()

-- 	self.show_pre_skill_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
-- 		if not self.skill_data then
-- 			return
-- 		end

-- 		if self.skill_pre_view and self.skill_pre_view:GetPreviewIsLoaded() then
-- 			self.skill_pre_view:PlaySkill(self.skill_data.skill_id)
-- 		end
-- 	end, 4)
-- end

--移除回调
function SkillView:RemoveDelayCloseTimer()
    if self.trigger_close_timer then
        GlobalTimerQuest:CancelQuest(self.trigger_close_timer)
        self.trigger_close_timer = nil
    end
end

--延迟5秒一键升级关闭
function SkillView:TriggerCloseOperate()
	self:RemoveDelayCloseTimer()
	self.trigger_close_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:OnClickUpLeveling()
		self:Close()
	end, 5)
end