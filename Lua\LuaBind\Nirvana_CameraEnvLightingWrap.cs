﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_CameraEnvLightingWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(Nirvana.CameraEnvLighting), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("__eq", op_Equality);
		<PERSON><PERSON>un<PERSON>("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("AmbientMode", get_AmbientMode, set_AmbientMode);
		<PERSON><PERSON>("AmbientIntensity", get_AmbientIntensity, set_AmbientIntensity);
		<PERSON><PERSON>("AmbientLight", get_AmbientLight, set_AmbientLight);
		<PERSON><PERSON>("AmbientSkyColor", get_AmbientSkyColor, set_AmbientSkyColor);
		<PERSON><PERSON>("AmbientEquatorColor", get_AmbientEquatorColor, set_AmbientEquatorColor);
		<PERSON><PERSON>("AmbientGroundColor", get_AmbientGroundColor, set_AmbientGroundColor);
		<PERSON><PERSON>("CustomReflection", get_CustomReflection, set_CustomReflection);
		L.RegVar("ReflectionIntensity", get_ReflectionIntensity, set_ReflectionIntensity);
		L.RegVar("Fog", get_Fog, set_Fog);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AmbientMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Rendering.AmbientMode ret = obj.AmbientMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AmbientIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			float ret = obj.AmbientIntensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AmbientLight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Color ret = obj.AmbientLight;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientLight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AmbientSkyColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Color ret = obj.AmbientSkyColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientSkyColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AmbientEquatorColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Color ret = obj.AmbientEquatorColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientEquatorColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AmbientGroundColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Color ret = obj.AmbientGroundColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientGroundColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CustomReflection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Cubemap ret = obj.CustomReflection;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CustomReflection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ReflectionIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			float ret = obj.ReflectionIntensity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ReflectionIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Fog(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			bool ret = obj.Fog;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Fog on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AmbientMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Rendering.AmbientMode arg0 = (UnityEngine.Rendering.AmbientMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Rendering.AmbientMode));
			obj.AmbientMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AmbientIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.AmbientIntensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AmbientLight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.AmbientLight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientLight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AmbientSkyColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.AmbientSkyColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientSkyColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AmbientEquatorColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.AmbientEquatorColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientEquatorColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AmbientGroundColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.AmbientGroundColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AmbientGroundColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CustomReflection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			UnityEngine.Cubemap arg0 = (UnityEngine.Cubemap)ToLua.CheckObject(L, 2, typeof(UnityEngine.Cubemap));
			obj.CustomReflection = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CustomReflection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ReflectionIntensity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ReflectionIntensity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ReflectionIntensity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Fog(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.CameraEnvLighting obj = (Nirvana.CameraEnvLighting)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.Fog = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Fog on a nil value");
		}
	}
}

