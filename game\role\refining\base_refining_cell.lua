BaseRefiningCell = BaseRefiningCell or BaseClass(BaseRender)
function BaseRefiningCell:OnFlush()
	if self.data == nil or self.data.cost_item_id <= 0 then
		return
	end

	local bundle, asset = ResPath.GetRoleUIImage(string.format("a3_tm_refining_%d", self.data.cost_item_id))
	self.node_list.item_icon.image:LoadSprite(bundle, asset)

	bundle, asset = ResPath.GetRoleUIImage(string.format("a3_gf_sj_%d", self.data.big_type))
	self.node_list["type_bg"].image:LoadSprite(bundle, asset)

	bundle, asset = ResPath.GetRoleUIImage(string.format("a3_gf_sj_mask_%d", self.data.big_type))
	self.node_list["mask"].image:LoadSprite(bundle, asset)

	local remind = AlchemyWGData.Instance:GetPelletCellRemind(self.data)
	local level = AlchemyWGData.Instance:GetPelletItemLevel(self.data.seq)
	self.node_list["mask"]:SetActive(level <= 0)
	self.node_list["remind"]:SetActive(remind)
end

function BaseRefiningCell:OnSelectChange(is_select)
	self.node_list["click_hl"]:SetActive(is_select)
end

-- function BaseRefiningCell:ChangeSelectCell(is_big_type, is_small_type)
-- 	self.node_list["click_hl"]:SetActive(is_big_type and is_small_type)
-- end

-----------------------------------
-- 大类型Item
-----------------------------------
RefiningBigTypeItem = RefiningBigTypeItem or BaseClass(BaseRender)
function RefiningBigTypeItem:OnFlush()
	if self.data == nil then
		return
	end
	self.node_list["normal_text"].tmp.text = Language.Role.RefiningBigTypes[self.index] or ""
	self.node_list["highlight_text"].tmp.text = Language.Role.RefiningBigTypes[self.index] or ""
	local remind = AlchemyWGData.Instance:GetPelletUpBigTypeRemind(self.index)
	self.node_list["img_remind"]:SetActive(remind)
end

function RefiningBigTypeItem:OnSelectChange(is_select)
	if not self.data then
		return
	end
	self.node_list["normal"]:SetActive(not is_select)
	self.node_list["highlight"]:SetActive(is_select)
end