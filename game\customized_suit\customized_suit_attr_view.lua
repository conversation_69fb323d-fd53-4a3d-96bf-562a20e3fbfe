CustomizedAttrView = CustomizedAttrView or BaseClass(SafeBaseView)

function CustomizedAttrView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Normal
    self.view_name = "CustomizedAttrView"
	local bundle_name = "uis/view/customized_suit_ui_prefab"
    self:AddViewResource(0, bundle_name, "layout_customized_suit_attr")

	self.suit = nil							---当前的套装id
	self.suit_part = nil					---当前套装的部件id
	self.left_part_attr_list = nil			---左侧套装部件属性列表
	self.left_part_cell_list = nil
	self.right_part_attr_list = nil			---右侧套装部件属性列表
	self.right_part_cell_list = nil

	self.left_select = nil
	self.left_select_max = 3
	self.right_select = nil
	self.right_select_max = 1
end

function CustomizedAttrView:__delete()

end

function CustomizedAttrView:OpenCallBack()
end

function CustomizedAttrView:CloseCallBack()
	self.suit = nil							---当前的套装id
	self.suit_part = nil					---当前套装的部件id
	self.left_select = nil
	self.right_select = nil
end

function CustomizedAttrView:LoadCallBack()
	if not self.left_part_attr_list then
		self.left_part_attr_list = AsyncListView.New(CustomizedAttrRender, self.node_list.left_attr_scroll)
		self.left_part_attr_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectLeftPartAttrHandler, self))
		self.left_part_attr_list:SetRefreshCallback(BindTool.Bind1(self.OnSelectLeftPartRefreshCallbackHandler, self))
	end

	if not self.right_part_attr_list then
		self.right_part_attr_list = AsyncListView.New(CustomizedAttrRender, self.node_list.right_attr_scroll)
		self.right_part_attr_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectRightPartAttrHandler, self))
		self.right_part_attr_list:SetRefreshCallback(BindTool.Bind1(self.OnSelectRightPartRefreshCallbackHandler, self))
	end

	--加载模型时装
	if nil == self.user_model then
		self.user_model = CommonUserModelRender.New(self.node_list["ph_display"])
		self.user_model:AddUiRoleModel(self)
	end

	XUI.AddClickEventListener(self.node_list["one_key_btn"], BindTool.Bind(self.OperateSkillBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_tips"], BindTool.Bind(self.OnClickBtnTips, self))
end

function CustomizedAttrView:ReleaseCallBack()
	if self.left_part_attr_list then
		self.left_part_attr_list:DeleteMe()
		self.left_part_attr_list = nil
	end

	if self.right_part_attr_list then
		self.right_part_attr_list:DeleteMe()
		self.right_part_attr_list = nil
	end

	if self.user_model then
		self.user_model:DeleteMe()
		self.user_model = nil
	end

	self.suit = nil							---当前的套装id
	self.suit_part = nil					---当前套装的部件id
	self.left_select = nil
	self.right_select = nil

	self.left_part_cell_list = nil
	self.right_part_cell_list = nil
end

function CustomizedAttrView:SetSuitPart(suit, part_id)
	self.suit = suit
	self.suit_part = part_id
end

function CustomizedAttrView:OnSelectLeftPartAttrHandler(item, cell_index, is_default, is_click, is_init)
	if nil == item or nil == item.data then
		return
	end

	local part_data = CustomizedSuitWGData.Instance:GetThemeAttrBySuitPart(self.suit, self.suit_part)
	
	if is_click and part_data and part_data.red then
		self:ChechAndAddRemove(true, item.data.attr_str, cell_index)
	end

	for index, list_data in ipairs(self.left_part_cell_list) do
		local is_have ,_ = self:ChechHaveSelect(self.left_select, list_data.data.attr_str)
		list_data:SetSelect(is_have)
	end

	self:RefreshSkillBtnStatus()
	self:FlushSelectNum()
end

function CustomizedAttrView:OnSelectLeftPartRefreshCallbackHandler(item_cell, cell_index)
	if not self.left_part_cell_list then
		self.left_part_cell_list = {}
	end

	self.left_part_cell_list[cell_index] = item_cell

	local is_have ,_ = self:ChechHaveSelect(self.left_select, item_cell.data.attr_str)
	item_cell:SetSelect(is_have)

	self:FlushSelectNum()
end

function CustomizedAttrView:OnSelectRightPartAttrHandler(item, cell_index, is_default, is_click, is_init)
	if nil == item or nil == item.data then
		return
	end

	local part_data = CustomizedSuitWGData.Instance:GetThemeAttrBySuitPart(self.suit, self.suit_part)

	if is_click and part_data and part_data.red then
		self:ChechAndAddRemove(false, item.data.attr_str, cell_index)
	end

	for index, list_data in ipairs(self.right_part_cell_list) do
		local is_have ,_ = self:ChechHaveSelect(self.right_select, list_data.data.attr_str)

		list_data:SetSelect(is_have)
	end

	self:RefreshSkillBtnStatus()
	self:FlushSelectNum()
end

function CustomizedAttrView:OnSelectRightPartRefreshCallbackHandler(item_cell, cell_index)
	if not self.right_part_cell_list then
		self.right_part_cell_list = {}
	end
	self.right_part_cell_list[cell_index] = item_cell

	local is_have ,_ = self:ChechHaveSelect(self.right_select, item_cell.data.attr_str)
	item_cell:SetSelect(is_have)

	self:FlushSelectNum()
end

function CustomizedAttrView:ChechHaveSelect(operate_table, attr_str)
	for index, temp_data in ipairs(operate_table) do
		if temp_data.attr_str == attr_str then
			return true, index
		end
	end

	return false, nil
end


function CustomizedAttrView:FlushSelectNum()
	self.node_list.text_select_left_num.text.text = string.format("%s/%s", #self.left_select, self.left_select_max)
	self.node_list.text_select_right_num.text.text = string.format("%s/%s", #self.right_select, self.right_select_max)
end

function CustomizedAttrView:ChechAndAddRemove(is_left, attr_str, cell_index)
	local max_count = is_left and self.left_select_max or self.right_select_max
	local operate = is_left and self.left_select or self.right_select

	local is_have ,index = self:ChechHaveSelect(operate, attr_str)
	if is_have then
		table.remove(operate, index)
		return
	end

	---不存在相同的则检测最大上限添加
	if #operate == max_count then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.select_attr_limit)
		return
	end

	local data = {}
	data.attr_str = attr_str
	data.cell_index = cell_index
	table.insert(operate, data)
end


function CustomizedAttrView:OnFlush(param_t, index)
	self.left_select = {}
	self.right_select = {}
	local left_data = CustomizedSuitWGData.Instance:GetThemePartAttrList(self.suit, self.suit_part, true)
	local right_data = CustomizedSuitWGData.Instance:GetThemePartAttrList(self.suit, self.suit_part, false)

	if self.left_part_attr_list then
		self.left_part_attr_list:SetDataList(left_data)
		self.left_part_attr_list:JumpToIndex(1, 1)---只是为了刷新一下界面
	end

	if self.right_part_attr_list then
		self.right_part_attr_list:SetDataList(right_data)
		self.right_part_attr_list:JumpToIndex(1, 1)---只是为了刷新一下界面
	end

	local part_data = CustomizedSuitWGData.Instance:GetThemeAttrBySuitPart(self.suit, self.suit_part)
	self.node_list.one_key_btn:SetActive(part_data and part_data.red)
	self.node_list.btn_tips:SetActive(not (part_data and part_data.red))

	self:FlushSuitModel(true)
end

function CustomizedAttrView:RefreshSkillBtnStatus()
	XUI.SetGraphicGrey(self.node_list["one_key_btn"], #self.left_select ~= self.left_select_max or #self.right_select ~= self.right_select_max)
end

function CustomizedAttrView:OnClickBtnTips()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.AttrTips)
end

function CustomizedAttrView:OperateSkillBtn()
	if not self.left_select then
		self.left_select = {}
	end

	if not self.right_select then
		self.right_select = {}
	end

	---不存在相同的则检测最大上限添加
	if #self.left_select < self.left_select_max or #self.right_select < self.right_select_max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Customized.select_attr_num_error)
		return
	end

	local left = {}
	for index, left_data in ipairs(self.left_select) do
		if left_data then
			table.insert(left, left_data.cell_index)
		end
	end

	local right = 0
	if self.right_select and self.right_select[1] then
		right = self.right_select[1].cell_index
	end

	CustomizedSuitWGCtrl.Instance:SendSkillTypeAttrChoose(self.suit, self.suit_part, 0, left, right)
	self:Close()
end

-- 刷新模型
function CustomizedAttrView:FlushSuitModel(is_show_all)
	if not self.user_model then
		return
	end

	local show_list = WardrobeWGData.Instance:GetActivationPartList(self.suit)
	if IsEmptyTable(show_list) then
		return
	end

	local user_model_data = {}

	user_model_data.body_res_id = AppearanceWGData.Instance:GetRoleResId()
	local fashion_cfg

	for k, data in pairs(show_list) do
		if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then -- 时装大类
			fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
			if fashion_cfg then                                                          -- 时装
				local prof = GameVoManager.Instance:GetMainRoleVo().prof
				local new_resource_id = self:ReSetFashionId(data.part, fashion_cfg.resouce)
				user_model_data.body_res_id = ResPath.GetFashionModelId(prof, new_resource_id)
			end
		end

		if is_show_all then
			if data.type == WARDROBE_PART_TYPE.MOUNT then
				fashion_cfg = NewAppearanceWGData.Instance:GetSpecialQiChongActCfgByImageId(
				MOUNT_LINGCHONG_APPE_TYPE.MOUNT, data.param1)
				if fashion_cfg then
					user_model_data.mount_res_id = fashion_cfg.appe_image_id
					user_model_data.mount_res_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(
					self.suit, data.part, user_model_data.mount_res_id)
					user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
					local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
					if not IsEmptyTable(action_cfg) then
						user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
					end
				end
			elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then -- 化鲲
				fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
				if fashion_cfg then
					user_model_data.mount_res_id = fashion_cfg.active_id
					user_model_data.mount_res_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(
					self.suit, data.part, user_model_data.mount_res_id)
					user_model_data.mount_action = MOUNT_RIDING_TYPE[1]
					local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(user_model_data.mount_res_id)
					if not IsEmptyTable(action_cfg) then
						user_model_data.mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
					end
				end
			end
		end
	end

	for k, v in pairs(show_list) do
		self:ShowModelByData(v, user_model_data)
	end
	user_model_data = self:ChangeModelShowScale(user_model_data)
	self.user_model:SetData(user_model_data)
	
end

-- 设置当前保存的特殊炫彩形象
function CustomizedAttrView:SetXuanCaiRecordId(suit_part, part_type, export_data)
	if part_type == SHIZHUANG_TYPE.MASK then      -- 脸饰
		export_data.mask_id = self:ReSetFashionId(suit_part, export_data.mask_id)
	elseif part_type == SHIZHUANG_TYPE.BELT then  -- 腰饰
		export_data.belt_id = self:ReSetFashionId(suit_part, export_data.belt_id)
	elseif part_type == SHIZHUANG_TYPE.WEIBA then -- 尾巴
		export_data.tail_id = self:ReSetFashionId(suit_part, export_data.tail_id)
	elseif part_type == SHIZHUANG_TYPE.SHOUHUAN then -- 手环
		export_data.shou_huan_id = self:ReSetFashionId(suit_part, export_data.shou_huan_id)
	elseif part_type == SHIZHUANG_TYPE.HALO then  -- 光环
		export_data.halo_id = self:ReSetFashionId(suit_part, export_data.halo_id)
	elseif part_type == SHIZHUANG_TYPE.WING then  -- 羽翼
		export_data.wing_id = self:ReSetFashionId(suit_part, export_data.wing_id)
	elseif part_type == SHIZHUANG_TYPE.FABAO then -- 法宝
		export_data.fabao_id = self:ReSetFashionId(suit_part, export_data.fabao_id)
	elseif part_type == SHIZHUANG_TYPE.JIANZHEN then -- 剑阵
		export_data.jianzhen_id = self:ReSetFashionId(suit_part, export_data.jianzhen_id)
	elseif part_type == SHIZHUANG_TYPE.SHENBING then -- 武器
		export_data.weapon_id = self:ReSetFashionId(suit_part, export_data.weapon_id)
	elseif part_type == SHIZHUANG_TYPE.FOOT then  -- 足迹
		export_data.foot_effect_id = self:ReSetFashionId(suit_part, export_data.foot_effect_id)
	end
end

-- 重新设置时装形象id
function CustomizedAttrView:ReSetFashionId(suit_part, old_id)
	local new_id = CustomizedSuitWGData.Instance:GetXuanCaiAppimageId(self.select_suit_seq, suit_part, old_id)
	return new_id
end

function CustomizedAttrView:ShowModelByData(data, user_model_data)
	if IsEmptyTable(data) then
		return
	end

	local fashion_cfg = nil
	if data.type == WARDROBE_PART_TYPE.FASHION then -- 时装大类
		WardrobeWGData.Instance:AssembleRoleModelDataByTypeIndex(data.param1, data.param2, user_model_data)
		self:SetXuanCaiRecordId(data.part, data.param1, user_model_data)
	end
end

function CustomizedAttrView:ChangeModelShowScale(user_model_data)
	local data = WardrobeWGData.Instance:GetThemeCfgBySuit(self.suit)
	if IsEmptyTable(data) then
		return user_model_data
	end

	---这里设置角色的所有展示（切换到角色展示的类型）
	local pos_str2 = data.tzsx_pos
	local rotate_str2 = data.tzsx_rot
	if pos_str2 and pos_str2 ~= "" then
		local pos = Split(pos_str2, "|")
		user_model_data.model_adjust_root_local_position  = u3dpool.vec3(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
		-- RectTransform.SetAnchoredPosition3DXYZ(self.node_list.ph_display.rect, pos[1] or 0, pos[2] or 0, pos[3] or 0)
	end

	if rotate_str2 and rotate_str2 ~= "" then
		local rot = Split(rotate_str2, "|")
		user_model_data.role_rotation = u3dpool.vec3(tonumber(rot[1]) or 0, tonumber(rot[2]) or 0, tonumber(rot[3]) or 0)
	end

	local scale2 = data.main_scale2
	if scale2 and scale2 ~= "" then
		user_model_data.model_adjust_root_local_scale = scale2
	end
	user_model_data.model_rt_type = ModelRTSCaleType.M

	return user_model_data
end
-------------------------------------技能列表item---------------------------
CustomizedAttrRender = CustomizedAttrRender or BaseClass(BaseRender)
function CustomizedAttrRender:OnFlush()
	if self.data then
		local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
		local per_desc = is_per and "%" or ""
		local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
		self.node_list.desc.text.text = string.format("%s", EquipmentWGData.Instance:GetAttrName(self.data.attr_str, false, false))
		self.node_list.value.text.text = string.format("%s%s", value_str, per_desc)
	end
end

function CustomizedAttrRender:SetSelect(is_select)
	self.node_list.select:SetActive(is_select)
end