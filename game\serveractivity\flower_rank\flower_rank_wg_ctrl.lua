require("game/serveractivity/flower_rank/flower_rank_wg_data")
require("game/serveractivity/flower_rank/flower_rank_view")
require("game/serveractivity/flower_rank/flower_rank_award")

FlowerRankWGCtrl = FlowerRankWGCtrl or BaseClass(BaseWGCtrl)

function FlowerRankWGCtrl:__init()
	if nil ~= FlowerRankWGCtrl.Instance then
		print("[FlowerRankWGCtrl] attempt to create singleton twice!")
		return
	end
	FlowerRankWGCtrl.Instance = self
	self.data = FlowerRankWGData.New()
	self.view = FlowerRankView.New()
	self.AwardView = FlowerRankAwardView.New()
	self:RegisterAllProtocols()
end

function FlowerRankWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.AwardView then
		self.AwardView:DeleteMe()
		self.AwardView = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	FlowerRankWGCtrl.Instance = nil
end

function FlowerRankWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCrossRACommonInfo, "OnSCCrossRACommonInfo")
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.SendAllInfoReq, self))
end

function FlowerRankWGCtrl:SendAllInfoReq()
	local param_t = {
		activity_type = ACTIVITY_TYPE.CROSS_RAND_ACTIVITY_TYPE_FLOWER_RANK,
		opera_type = CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_FLOWER_HANDSOME,
	}
	CrossServerWGCtrl.Instance:SendCrossRandActivityOperaReq(param_t)
end

-- 打开主窗口
function FlowerRankWGCtrl:Open()
	self.view:Open()
end
--打开活动奖励面板
function FlowerRankWGCtrl:OpenFlowerRankAward()
	if not self.AwardView:IsOpen() then
		self.AwardView:Open()
	end
end

function FlowerRankWGCtrl:OnSCCrossRACommonInfo(protocol)
	self.data:SetActivityData(protocol)
end

