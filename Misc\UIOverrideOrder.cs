﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Nirvana;
using System;
using LuaInterface;

[RequireComponent(typeof(Canvas))]
[RequireComponent(typeof(GraphicRaycaster))]
public class UIOverrideOrder : MonoBeh<PERSON>our, IOverrideOrder, IClippable
{
    [SerializeField]
    private bool isSupportClip;

    private Canvas groupCanvas;

    private RectMask2D lastMask = null;

    private void Start()
    {
        groupCanvas = OverrideOrderGroupMgr.Instance.AddToGroup(this);
    }

    private void OnEnable()
    {
        this.TryAddClip();
    }

    protected  void OnDisable()
    {
        this.TryRemoveClip();
    }

    private void OnDestroy()
    {
        OverrideOrderGroupMgr.Instance.RemoveFromGroup(groupCanvas, this);
        groupCanvas = null;
    }

    protected void OnTransformParentChanged()
    {
        if (Application.isPlaying)
        {
            ResetRootCanvas();
            OverrideOrderGroupMgr.Instance.SetGroupCanvasDirty(groupCanvas);
        }

        this.TryAddClip();
    }

    private void TryAddClip()
    {
        if (isSupportClip)
        {
            RectMask2D mask2d = this.GetComponentInParent<RectMask2D>();
            if (lastMask != mask2d)
            {
                if (lastMask != null)
                {
                    lastMask.RemoveClippable(this);
                    lastMask = null;
                }

                if (mask2d != null)
                {
                    mask2d.AddClippable(this);
                    lastMask = mask2d;
                }
            }
        }
    }

    private void TryRemoveClip()
    {
        if (isSupportClip)
        {
            // lastMask可能被意外销毁，所以这里强制调用一次还原
            this.SetClipRect(new Rect(), false);

            if (lastMask != null)
            {
                lastMask.RemoveClippable(this);
                lastMask = null;
            }
        }
    }

    public GameObject GetTarget()
    {
        if (null == this) return null;

        return this.gameObject;
    }

    public void SetOverrideOrder(int order, int orderInterval, int maxOrder, out int incOrder)
    {
        incOrder = 0;
        Canvas canvas = this.GetComponent<Canvas>();
        if (null != canvas)
        {
            canvas.overrideSorting = true;
            if (order > maxOrder)
            {
                order = maxOrder;
            }
            canvas.sortingOrder = order;
            incOrder = 1;
        }
    }

    public void ResetRootCanvas()
    {
        OverrideOrderGroupMgr.Instance.RemoveFromGroup(this.groupCanvas, this);
        this.groupCanvas = null;

        var canvasScalers = ListPool<CanvasScaler>.Get();
        this.GetComponentsInParent(true, canvasScalers);
        if (canvasScalers.Count > 0)
        {
            var canvasScaler = canvasScalers[0];
            this.groupCanvas = OverrideOrderGroupMgr.Instance.AddToGroup(this);
        }

        ListPool<CanvasScaler>.Release(canvasScalers);
    }

    [NoToLua]
    public void RecalculateClipping()
    {
        // donothing
    }

    private RectTransform m_RectTransform;
    [NoToLua]
    public RectTransform rectTransform
    {
        get { return m_RectTransform ?? (m_RectTransform = GetComponent<RectTransform>()); }
    }

    [NoToLua]
    public void Cull(Rect clipRect, bool validRect)
    {
        // donothing
    }

    [NoToLua]
    public void SetClipRect(Rect value, bool validRect)
    {
        if (isSupportClip)
        {
            MaskableGraphic[] graphics = this.GetComponentsInChildren<MaskableGraphic>();
            for (int i = 0; i < graphics.Length; i++)
            {
                graphics[i].SetClipRect(value, validRect);
            }
        }
    }

    public void SetClipSoftness(Vector2 clipSoftness)
    {
        // throw new NotImplementedException();
    }
}
