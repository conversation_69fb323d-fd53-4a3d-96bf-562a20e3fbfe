require("game/serveractivity/lottery_tree/lottery_tree_wg_data")
-- require("game/serveractivity/lottery_tree/lottery_tree_show_view")

LotteryTreeWGCtrl = LotteryTreeWGCtrl or BaseClass(BaseWGCtrl)

function LotteryTreeWGCtrl:__init()
	if LotteryTreeWGCtrl.Instance ~= nil then
		print("[LotteryTreeWGCtrl]error:create a singleton twice")
	end
	LotteryTreeWGCtrl.Instance = self

	self.data = LotteryTreeWGData.New()
	-- self.show_view = LotteryTreeShowView.New()

	self:RegisterAllProtocols()

end

function LotteryTreeWGCtrl:__delete()
	-- if nil ~= self.show_view then
	-- 	self.show_view:DeleteMe()
	-- end
	if nil ~= self.data then
		self.data:DeleteMe()
	end

	LotteryTreeWGCtrl.Instance = nil
end

function LotteryTreeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCRAYaoqianshuInfo, "OnRAYaoqianshuInfo")
	self:RegisterProtocol(SCRAYaoqianshuBuyResultInfo, "OnRAYaoqianshuBuyResultInfo")
end

function LotteryTreeWGCtrl:Open()
	local isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_LOTTERY_TREE)
	if not isopen then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
		return
	end
end

function LotteryTreeWGCtrl:OnRAYaoqianshuInfo(protocol)
	self.data:SetMoneyTreeInfo(protocol)
	
end

function LotteryTreeWGCtrl:OnRAYaoqianshuBuyResultInfo(protocol)
	self.data:SetMoneyTreeChouResultInfo(protocol)
	local data_list = {}
	local index = 0
	for k,v in pairs(protocol.hit_item_list) do
		local reward_item = {}
		reward_item = {}
		reward_item.item_id = v.item_id
		reward_item.num = v.num
		reward_item.is_bind = v.is_bind
		data_list[index] = reward_item
		index = index + 1
	end
	-- self.show_view:SetDataList(data_list)
end