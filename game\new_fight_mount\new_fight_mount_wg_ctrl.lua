require("game/new_fight_mount/new_fight_mount_wg_data")
require("game/new_fight_mount/new_fight_mount_view")
require("game/new_fight_mount/new_fight_mount_lwsj_view")

NewFightMountWGCtrl = NewFightMountWGCtrl or BaseClass(BaseWGCtrl)

function NewFightMountWGCtrl:__init()
	if NewFightMountWGCtrl.Instance then
		ErrorLog("[NewFightMountWGCtrl] attempt to create singleton twice!")
		return
	end

	NewFightMountWGCtrl.Instance = self
	self.data = NewFightMountWGData.New()
	self.view = NewFightMountView.New(GuideModuleName.NewFightMountView)
	self.lwsj_view = NewFightMountLwsjView.New()
  
    self:RegisterAllProtocols()

	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function NewFightMountWGCtrl:__delete()
	NewFightMountWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.lwsj_view then
		self.lwsj_view:DeleteMe()
		self.lwsj_view = nil
	end

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
	end
end

function NewFightMountWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSFightMount2Operate)
    self:RegisterProtocol(SCFightMount2Info, "OnSCFightMount2Info")
    self:RegisterProtocol(SCFightMount2GoonMount, "OnSCFightMount2GoonMount")
    self:RegisterProtocol(SCFightMount2OneInfo, "OnSCFightMount2OneInfo")
	self:RegisterProtocol(SCFightMount2Time, "OnSCFightMount2Time")
end

-- 请求操作
function NewFightMountWGCtrl:SendCSNewFightMountOperateRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFightMount2Operate)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function NewFightMountWGCtrl:OnSCFightMount2Info(protocol)
	--print_error("=======全部战斗坐骑数据======", protocol)
	self.data:SetFightMountAllInfo(protocol)
    RemindManager.Instance:Fire(RemindName.DragonTemple_Hatch)
	RemindManager.Instance:Fire(RemindName.NewFightMount)

	MainuiWGCtrl.Instance:ChangeYuLongBtnState()
end

function NewFightMountWGCtrl:OnSCFightMount2GoonMount(protocol)
	--print_error("=======幻化数据数据改变======", protocol)
	self.data:SetFightMountUseUpdateInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
	ViewManager.Instance:FlushView(GuideModuleName.NewFightMountView)

	MainuiWGCtrl.Instance:ChangeYuLongBtnState()
end

function NewFightMountWGCtrl:OnSCFightMount2OneInfo(protocol)
	--print_error("=======单个战斗坐骑数据======", protocol)
	self.data:SetFightMountUpdateInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTempleView, TabIndex.dragon_temp_hatch)
	ViewManager.Instance:FlushView(GuideModuleName.NewFightMountView)
	RemindManager.Instance:Fire(RemindName.DragonTemple_Hatch)
	RemindManager.Instance:Fire(RemindName.NewFightMount)
end

function NewFightMountWGCtrl:OnSCFightMount2Time(protocol)
	--print_error("======时间======", protocol)
	self.data:SetFightMountTimeInfo(protocol)

	MainuiWGCtrl.Instance:ChangeYuLongBtnState()
end

function NewFightMountWGCtrl:OpenGetNewView(seq)
	local protocol = {appe_image_id = seq, appe_type = ROLE_APPE_TYPE.NEW_FIGHT_MOUNT}
	AppearanceWGCtrl.Instance:OnGetNewAppearance(protocol)
end

function NewFightMountWGCtrl:OpenLwsjView()
	if not self.lwsj_view:IsOpen() then
		self.lwsj_view:Open()
	else
		self.lwsj_view:Flush()
	end
end

function NewFightMountWGCtrl:OnNewFightMountSkillLevelResult(result, param1, param2)
	if result == 1 and self.view:Open() then
		self.view:PlayUseEffect(UIEffectName.s_shengji)
	end
end


function NewFightMountWGCtrl:OnNewFightMountYuLongResult(result, param1)
	if result == 1 then
		MainuiWGCtrl.Instance:ShowYuLongScreenEffect()
	end
end

function NewFightMountWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	  (change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:GetIsSkillLevelCostCfg(change_item_id) then
			ViewManager.Instance:FlushView(GuideModuleName.NewFightMountView)
			RemindManager.Instance:Fire(RemindName.NewFightMount)
		end
	end
end