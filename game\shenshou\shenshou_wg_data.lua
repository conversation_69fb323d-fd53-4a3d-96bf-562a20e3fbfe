ShenShouWGData = ShenShouWGData or BaseClass()
-- BH_BAG_FENJIE_MAX = 15 --背包超过15就需要显示红点
-- DEFAULT_CHU_ZHAN_SLOT_NUM = 3	-- 默认出战位数
-- EXTRA_CHU_ZHAN_SLOT_NUM = 5		-- 额外出战位数
-- MAX_CHU_ZHAN_SLOT_NUM = DEFAULT_CHU_ZHAN_SLOT_NUM + EXTRA_CHU_ZHAN_SLOT_NUM

function ShenShouWGData:__init()
	if ShenShouWGData.Instance ~= nil then
		ErrorLog("[ShenShouWGData] attempt to create singleton twice!")
		return
	end
	ShenShouWGData.Instance = self

	self.shenshou_cfg = ConfigManager.Instance:GetAutoConfig("shenshou_cfg_new_auto")
	self.assist_open_cfg = ListToMap(self.shenshou_cfg.assist_open ,"soul_ring_seq")
	self.shou_id_cfg = ListToMap(self.shenshou_cfg.shou_cfg, "shou_id")
	self.shenshou_equip_cfg = ListToMap(self.shenshou_cfg.equip_cfg, "item_id")
	self.equip_base_attr_cfg = 	ListToMap(self.shenshou_cfg.equip_base_attr, "slot_index", "quality")
	self.equip_level_cfg = ListToMap(self.shenshou_cfg.equip_level, "slot_index", "strength_level")
	self.shenshou_list_cfg = ListToMapList(self.shenshou_cfg.shou_cfg, "soul_ring_seq")
	self.shenshou_need_slot_cfg = ListToMap(self.shenshou_cfg.slot_need_quality_cfg ,"shou_id", "slot")
	self.shenshou_skill_cfg = ListToMap(self.shenshou_cfg.skill_cfg ,"skill_type", "level")
	self.shenshou_euip_attr_cfg = ListToMap(self.shenshou_cfg.equip_attr ,"quality", "attr_type")
	self.decompose_cfg = ListToMap(self.shenshou_cfg.decompose_cfg, "decompose_v_item_id", "decompose_need_quality", "decompose_need_star_num")
	self.soul_ring_pos_cfg = ListToMap(self.shenshou_cfg.soul_ring_pos, "num", "soul_ring_seq")
	self.skill_unlock_condition_cfg = ListToMap(self.shenshou_cfg.skill_unlock_condition, "soul_ring_seq", "skill_type", "level")
	self.shenshou_other_cfg = self.shenshou_cfg.other[1]
	self.shenshou_compose_cfg = self.shenshou_cfg.compose_cfg

	-- 数据
	self.shenshou_list_info = {}
	self.grid_list = {}  		-- 神兽背包信息
	self.shenshou_bag_list = {} -- 序号表
	self.use_stone_flag = false
	self.use_lingyu_flag = false
	self.view_select_soul_ring_seq = -1
	self.shenshou_equip_data_cache = {}
	self.equip_total_shuliandu = 0
	self.shenshou_ring_active_item = {}
	self.has_stone_flag = false
	self.soul_ring_pos_cache = {}

	self.base_attr_table = {}
	self.qh_attr_table = {}
	self.shenshou_exp = -1

	self:InitDataCache()
	RemindManager.Instance:Register(RemindName.ShenShouRemind, BindTool.Bind(self.GetShenShouRemind, self))

	-- self.shenshou_list = {}		-- 神兽信息
	-- self.ss_extra_item_id = {}
	-- self.extra_zhuzhan_count = 0 -- 神兽额外助战位
	-- self.ss_limit_red_list = nil -- 神兽红点限制表
	-- 默认助战位开启配置
	-- self.defult_open_cfg = ListToMap(self.shenshou_cfg.open_level ,"index")
	-- 额外助战位开启配置
	-- self.extra_open_cfg = ListToMap(self.shenshou_cfg.extra_num_cfg ,"extra_num")
	-- self.shou_series_cfg = ListToMapList(self.shenshou_cfg.shou_cfg, "series")
	-- self.shenshou_show_equip_cfg = ListToMapList(self.shenshou_cfg.equip_cfg, "slot_index","quality")
	-- self.shen_shou_back_packinfo = {}
	-- self.is_flush_shen_shou_back_packinfo = false
end

function ShenShouWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.ShenShouRemind)
	ShenShouWGData.Instance = nil
	self.soul_ring_pos_cache = nil
end

-------------------------------------------------remind_start-------------------------------------------------
function ShenShouWGData:GetShenShouRemind()
	local soul_ring_list = self:GetSoulRingDataList()
	
	if not IsEmptyTable(soul_ring_list) then
		for k, v in pairs(soul_ring_list) do
			if self:GetHunHuanRemind(v.soul_ring_seq) > 0 then
				return 1
			end
		end
	end

	return 0
end

function ShenShouWGData:GetHunHuanRemind(soul_ring_seq)
	local unlock = self:IsSoulRingUnLock(soul_ring_seq)

	if unlock then
		-- 能够穿戴
		local wear_data = self:GetOneKeyWearDataList(soul_ring_seq)

		if not IsEmptyTable(wear_data) then
			return 1
		end

		-- 有能强化的装备
		if self:IsSoulRingHasCanStrengthEquip(soul_ring_seq) > 0 then
			return 1
		end

		-- local equip_list = self:GetShenShouEquipInfoList(soul_ring_seq)
		-- if not IsEmptyTable(equip_list) then
		-- 	for k, v in pairs(equip_list) do
		-- 		if self:IsShenShouEquipStrengthUp(soul_ring_seq, v.slot_index) then
		-- 			return 1
		-- 		end
		-- 	end
		-- end
	else
		local soul_ring_cfg = self:GetSoulRingCfgBySoulRingSeq(soul_ring_seq)

		--  跳进去直接就解锁了两个默认解锁的 总有反馈说红点异常，直接干掉
		if soul_ring_cfg.is_auto_open == 1 then
			return 0
		end

		-- 前置未解锁
		if soul_ring_cfg.last_soul_ring_seq >= 0 then
			local last_unlock = self:IsSoulRingUnLock(soul_ring_cfg.last_soul_ring_seq)

			if not last_unlock then
				return 0
			end
		end

		if soul_ring_cfg.open_level > 0 then
			local role_level = RoleWGData.Instance:GetRoleLevel()

			if soul_ring_cfg.open_level > role_level then
				return 0
			end
		end

		if soul_ring_cfg.vip_level > 0 then
			local vip_level = VipWGData.Instance:GetRoleVipLevel()

			if soul_ring_cfg.vip_level > vip_level then
				return 0
			end
		end

		if soul_ring_cfg.stuff_id > 0 and soul_ring_cfg.stuff_num > 0 then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(soul_ring_cfg.stuff_id)

			if has_num < soul_ring_cfg.stuff_num then
				return 0
			end
		end

		return 1
	end

	return 0
end

function ShenShouWGData:IsSoulRingHasCanStrengthEquip(soul_ring_seq)
	local equip_list = self:GetShenShouEquipInfoList(soul_ring_seq)

	if not IsEmptyTable(equip_list) then
		for k, v in pairs(equip_list) do
			if self:IsShenShouEquipStrengthUp(soul_ring_seq, v.slot_index) then
				return 1
			end
		end
	end

	return 0
end
--------------------------------------------------remind_end--------------------------------------------------

-------------------------------------------------protocol_start-----------------------------------------------
function ShenShouWGData:SetShenShouListInfo(protocol)
	self.shenshou_exp = protocol.shenshou_exp
	self.shenshou_list_info = protocol.shenshou_list
end

function ShenShouWGData:UpdateShenShouListInfo(protocol)
	local soul_ring_seq = protocol.index
	self.shenshou_list_info[soul_ring_seq] = protocol.shenshou_info
end

function ShenShouWGData:GetShenShouInfoBySeq(soul_ring_seq)
	return self.shenshou_list_info[soul_ring_seq]
end

-- tips_from_view
function ShenShouWGData:GetShenShouEquipInfoList(soul_ring_seq)
	return (self.shenshou_list_info[soul_ring_seq] or {}).equip_list or {}
end

function ShenShouWGData:GetShenShouEquipInfo(soul_ring_seq, slot_index)
	return ((self.shenshou_list_info[soul_ring_seq] or {}).equip_list or {})[slot_index]
end

function ShenShouWGData:GetSingleShenSHouBagData(data)
	local target_data = {}
	target_data.index = data.index
	target_data.item_id = data.item_id
	target_data.strength_level = data.strength_level
	target_data.shuliandu = data.shuliandu
	target_data.is_bind = data.bind
	target_data.attr_list = {}
	target_data.attr_list = data.attr_list
	target_data.is_equip = 0
	target_data.quality = 0
	target_data.slot_index = 0
	target_data.star_count = 0
	target_data.contain_shulian = 0
	local shenshou_equip_cfg = self:GetShenShouEqCfg(data.item_id)
	if shenshou_equip_cfg then
		target_data.is_equip = shenshou_equip_cfg.is_equip
		target_data.quality = shenshou_equip_cfg.quality
		target_data.slot_index = shenshou_equip_cfg.slot_index
		target_data.star_count = self:GetStarCount(data, shenshou_equip_cfg)
		target_data.score, target_data.base_score = self:GetShenShouItemScore(data)
		if shenshou_equip_cfg.is_equip == 0 or shenshou_equip_cfg.quality <= 2 then
			target_data.contain_shulian = shenshou_equip_cfg.contain_shulian
		end
	end

	target_data.item_color = GameEnum.ITEM_COLOR_WHITE
	if data.item_id > 0 then
		local _, item_color = ItemWGData.Instance:GetItemColor(data.item_id)
		target_data.item_color = item_color
	end

	target_data.score = target_data.score or 0
	target_data.base_score = target_data.base_score or 0
	target_data.knapsack_type = KNAPSACK_TYPE.SHENSHOU --上架信息处理
	target_data.num = 1
	target_data.star_level = target_data.star_count
	target_data.param = {}
	target_data.param.star_level = target_data.star_count
	target_data.grid_name = GRID_TYPE_SHENSHOU_BAG

	return target_data
end

-- 设置背包总数据
function ShenShouWGData:SetShenShouBagInfo(protocol)
	local grid_list = {}

	if not IsEmptyTable(protocol.grid_list) then
		for k, v in pairs(protocol.grid_list) do
			grid_list[v.index] = self:GetSingleShenSHouBagData(v)
		end
	end

	self.grid_list = grid_list
	self:CalShenShouEquipList()
end

-- 更新背包数据
function ShenShouWGData:UpdateShenShouBagInfo(protocol)
	for i = 1, protocol.grid_num do
		local data = protocol.grid_list[i]

		if data.item_id > 0 then
			self.grid_list[data.index] = self:GetSingleShenSHouBagData(data)
		else
			self.grid_list[data.index] = nil
		end
	end

	self:CalShenShouEquipList()
end

function ShenShouWGData:GetShenShouBagDataList()
	-- local bag_data_list = {}

	-- for k, v in pairs(self.grid_list) do
	-- 	table.insert(bag_data_list, v)
	-- end

	-- return bag_data_list

	return self.shenshou_bag_list
end

function ShenShouWGData:GetShenShouDeComposeBagDataList()
	table.sort(self.shenshou_bag_list, function (a, b)
		local a_cfg = self:GetShenShouEqCfg(a.item_id)
        local b_cfg = self:GetShenShouEqCfg(b.item_id)

        if a_cfg.quality < b_cfg.quality then
            return true
        else
			local a_star_count = self:GetStarCount(a, a_cfg)
			local b_star_count = self:GetStarCount(b, b_cfg)

			if a_star_count < b_star_count then
				return true
			end
        end

		return false
    end)

    return self.shenshou_bag_list
end

-- -- 设置神兽背包信息
-- function ShenShouWGData:SetShenshouGridList(grid_list)
-- 	self.grid_list = grid_list

-- 	print_error("背包数据", self.grid_list)

-- 	for k, v in pairs(self.grid_list) do
-- 		print_error("背包数据-----", k, v)  -- 0 6 14
-- 	end

-- 	-- self.is_flush_shen_shou_back_packinfo = true
-- 	-- self:SetAllShuLianDu()
-- end

-- 设置神兽背包信息
function ShenShouWGData:GetShenshouGridList()
	return self.grid_list
end

function ShenShouWGData:GetShenShouExp()
	return self.shenshou_exp > 0 and self.shenshou_exp or 0
end
--------------------------------------------------protocol_end------------------------------------------------

-------------------------------------------------cfg_get_start------------------------------------------------
function ShenShouWGData:GetSoulRingDataList()
	return self.assist_open_cfg
end

function ShenShouWGData:GetSoulRingCfgBySoulRingSeq(soul_ring_seq)
	return self.assist_open_cfg[soul_ring_seq]
end

-- function ShenShouWGData:GetSoulRingPosCache(soul_ring_seq)
-- 	return self.shenshou_pos_cache[soul_ring_seq]
-- end

-- function ShenShouWGData:GetSoulRingRotationCache(soul_ring_seq)
-- 	return self.shenshou_rotation_cache[soul_ring_seq]
-- end

function ShenShouWGData:GetShenShouCfg(shou_id)
	return self.shou_id_cfg[shou_id]
end

-- 获取神兽装备配置
function ShenShouWGData:GetShenShouEqCfg(item_id)
	return self.shenshou_equip_cfg[item_id]
end

-- 获取神兽装备基础属性配置
function ShenShouWGData:GetShenshouBaseList(slot_index, quality)
	return (self.equip_base_attr_cfg[slot_index] or {})[quality]
end

-- 获取神兽装备升级属性配置
function ShenShouWGData:GetShenshouLevelList(slot_index, strength_level)
	return (self.equip_level_cfg[slot_index] or {})[strength_level]
end

function ShenShouWGData:GetShenshouLevelDataList(slot_index)
	return self.equip_level_cfg[slot_index]
end

-- 魂环 神兽列表
function ShenShouWGData:GetSoulRingShowShenShouList(soul_ring_seq)
	return self.shenshou_list_cfg[soul_ring_seq]
end

-- 获得对应格子装备品质要求   策划zp要求 部位的 品质 星级 都要配成一样的 ，前端展示拿品质做激活提醒展示
function ShenShouWGData:GetQualityRequirementCfg(shou_id, slot)
	return (self.shenshou_need_slot_cfg[shou_id] or {})[slot]
end

function ShenShouWGData:GetShenShouSKillListByType(skill_type)
	return self.shenshou_skill_cfg[skill_type]
end

function ShenShouWGData:GetShenShouSKillCfg(skill_type, skill_level)
	return (self.shenshou_skill_cfg[skill_type] or {})[skill_level]
end

function ShenShouWGData:GetRandomAttrCfg(quality, attr_type)
	return (self.shenshou_euip_attr_cfg[quality] or {})[attr_type]
end

function ShenShouWGData:SetUseStoneFlag(is_select)
	self.use_stone_flag = is_select
end

function ShenShouWGData:GetUseStoneFlag()
	return self.use_stone_flag
end

function ShenShouWGData:SetUseLingyuFlag(is_select)
	self.use_lingyu_flag = is_select
end

function ShenShouWGData:GetUseLingyuFlag()
	return self.use_lingyu_flag
end

function ShenShouWGData:GetShenShouOtherCfg()
	return self.shenshou_other_cfg
end

function ShenShouWGData:GetDecomposeCfg(decompose_v_item_id, decompose_need_quality, decompose_need_star_num)
	return ((self.decompose_cfg[decompose_v_item_id] or {})[decompose_need_quality] or {})[decompose_need_star_num]
end

function ShenShouWGData:GetRecommendAttr(quality)
	local recommend_list = {}
	for i,v in ipairs(self.shenshou_cfg.equip_attr) do
		local list = {}
		if v.quality == quality and v.recommend_show == 1 then
			table.insert(recommend_list, v)
		end
	end
	return recommend_list
end

function ShenShouWGData:GetShenShouEqRealItemId(item_id)
	return (self.shenshou_equip_cfg[item_id] or {}).real_item_id
end

function ShenShouWGData:GetSoulRingModelCfg(soul_ring_num, soul_ring_id)
	local pos_cache_cfg = (self.soul_ring_pos_cache[soul_ring_num] or{})[soul_ring_id] or {}

	if not IsEmptyTable(pos_cache_cfg) then
		return pos_cache_cfg
	end

	local function change_to_number(data)
		local target_data = {}

		for k, v in pairs(data) do
			target_data[k] = tonumber(v)
		end

		return target_data
	end

	local pos_cfg = (self.soul_ring_pos_cfg[soul_ring_num] or{})[soul_ring_id] or {}
	local pos_data

	if not IsEmptyTable(pos_cfg) then
		pos_data = {
			scale = pos_cfg.scale,
			select_scale = pos_cfg.select_scale,
			pos = change_to_number(Split(pos_cfg.pos, "|")),
			rotation = change_to_number(Split(pos_cfg.rotation, "|")),
		}
		
		self.soul_ring_pos_cache[soul_ring_num] = self.soul_ring_pos_cache[soul_ring_num] or {}
		self.soul_ring_pos_cache[soul_ring_num][soul_ring_id] = pos_data
	end

	return pos_data
end

function ShenShouWGData:GetSoulRingPosCfg(soul_ring_seq, num)
	local number = (num and num > 0) and num or self:GetActiveSoulRingNum()
	return self:GetSoulRingModelCfg(number, soul_ring_seq)
end

function ShenShouWGData:GetSoulRingSkillUnlockConditionCfg(soul_ring_seq, skill_type, level)
	return ((self.skill_unlock_condition_cfg[soul_ring_seq] or {})[skill_type] or {})[level]
end

function ShenShouWGData:GetShenShouComposeCfg()
	return self.shenshou_compose_cfg
end
--------------------------------------------------cfg_get_end-------------------------------------------------

---------------------------------------------------cal_start--------------------------------------------------
function ShenShouWGData:InitDataCache()
	-- local shenshou_pos_cache = {}
	-- local shenshou_rotation_cache = {}
	local shenshou_ring_active_item = {}

	-- local function change_to_number(data)
	-- 	local target_data = {}

	-- 	for k, v in pairs(data) do
	-- 		target_data[k] = tonumber(v)
	-- 	end

	-- 	return target_data
	-- end

	for k, v in pairs(self:GetSoulRingDataList()) do
		-- shenshou_pos_cache[v.soul_ring_seq] = change_to_number(Split(v.pos, "|"))
		-- shenshou_rotation_cache[v.soul_ring_seq] = change_to_number(Split(v.rotation, "|"))

		if v.stuff_id > 0 and v.stuff_num > 0 then
			shenshou_ring_active_item[v.stuff_id] = v.stuff_id
		end
	end

	-- self.shenshou_pos_cache = shenshou_pos_cache
	-- self.shenshou_rotation_cache = shenshou_rotation_cache
	self.shenshou_ring_active_item = shenshou_ring_active_item
end


function ShenShouWGData:IsSoulRingUnLock(soul_ring_seq)
	local info = self:GetShenShouInfoBySeq(soul_ring_seq)
	local shenshou_id = info and info.shenshou_id or -1   -- 配置表神兽id从1 开始，后端数据规则 shenshou_id < 0 表示未解锁
	return shenshou_id > 0
end

-- 魂环模型数据  选中模型
function ShenShouWGData:GetSoulRingModelData(soul_ring_seq)
	local soul_ring_data_list = self:GetSoulRingDataList()
	local soul_ring_model_data = {}
	local target_soul_ring_select_effect = ""

	for k, v in pairs(soul_ring_data_list) do
		local info = self:GetShenShouInfoBySeq(v.soul_ring_seq)

		-- local target_shenshou_id = info and info.shenshou_id and info.shenshou_id > 0 and info.shenshou_id or v.shou_id
		local target_shenshou_id = (info or {}).shenshou_id or -1
		if target_shenshou_id > 0 then
			local target_shenshou_cfg = self:GetShenShouCfg(target_shenshou_id)
			soul_ring_model_data[v.soul_ring_seq] = {
				shenshou_id = target_shenshou_id,
				soul_ring_effect = target_shenshou_cfg.soul_ring_effect,
				quality = target_shenshou_cfg.quality,
			}
	
			if v.soul_ring_seq == soul_ring_seq then
				target_soul_ring_select_effect = target_shenshou_cfg.soul_ring_select_effect
			end
		end
	end

	return soul_ring_model_data, target_soul_ring_select_effect
end

-- 魂环的加成属性 
function ShenShouWGData:GetSoulRingAttrData(soul_ring_seq)
	local equip_total_attr = AttributePool.AllocAttribute()
	local shenshou_equip_info_list = self:GetShenShouEquipInfoList(soul_ring_seq)

	if not IsEmptyTable(shenshou_equip_info_list) then
		for k, v in pairs(shenshou_equip_info_list) do
			if v.item_id > 0 then
				local shenshou_equip_cfg = self:GetShenShouEqCfg(v.item_id)
				if shenshou_equip_cfg then
					local base_shenshou_cfg = self:GetShenshouBaseList(shenshou_equip_cfg.slot_index, shenshou_equip_cfg.quality)
					-- local strength_shenshou_cfg = self:GetShenshouLevelList(shenshou_equip_cfg.slot_index, v.strength_level)
					local base_attr_struct = AttributeMgr.GetAttributteByClass(base_shenshou_cfg)
					-- local strength_attr_struct = AttributeMgr.GetAttributteByClass(strength_shenshou_cfg)
					-- local equip_attr = AttributeMgr.AddAttributeAttr(base_attr_struct, strength_attr_struct)
					-- equip_total_attr = AttributeMgr.AddAttributeAttr(equip_total_attr, equip_attr)
					equip_total_attr = AttributeMgr.AddAttributeAttr(equip_total_attr, base_attr_struct)
				end
			end
		end
	end

	return equip_total_attr
end

--是否有魂环穿戴装备
function ShenShouWGData:IsSoulRingWearEquip()
	for k, v in pairs(self:GetSoulRingDataList()) do
		local equip_list = self:GetShenShouEquipInfoList(v.soul_ring_seq)

		if not IsEmptyTable(equip_list) then
			for i, u in pairs(equip_list) do
				if u.item_id > 0 then
					return true
				end
			end
		end
	end

	return false
end

function ShenShouWGData:GetStrengthSoulRingDataList()
	local data_list = {}

	for k, v in pairs(self:GetSoulRingDataList()) do
		local equip_list = self:GetShenShouEquipInfoList(v.soul_ring_seq)

		if not IsEmptyTable(equip_list) then
			for i, u in pairs(equip_list) do
				if u.item_id > 0 then
					table.insert(data_list, v)
					break
				end
			end
		end
	end

	return data_list
end

-- 根据 装备 attr_list + 装备cfg 获取星数
function ShenShouWGData:GetStarCount(param, equip_cfg)
	local star_count = 0
	if param and equip_cfg then
		for k,v in pairs(param.attr_list) do
			if v.attr_type > 0 then
				local random_cfg = self:GetRandomAttrCfg(equip_cfg.quality, v.attr_type) or {}
				if random_cfg.is_star_attr == 1 then
					star_count = star_count + 1
				end
			end
		end
	end
	
	return star_count
end

-- 获取装备能够强化到的等级
function ShenShouWGData:GetCanQhLv(cell_data, left_shuliandu)
	local data_list = self:GetShenshouLevelDataList(cell_data.slot_index)
	local next_shuliandu_cfg = self:GetShenshouLevelList(cell_data.slot_index, cell_data.strength_level + 1)

	if IsEmptyTable(next_shuliandu_cfg) then
		return cell_data.strength_level
	end

	local strength_level = -1
	if left_shuliandu > 0 then
		for i = cell_data.strength_level, #data_list - 1 do
			left_shuliandu = left_shuliandu - data_list[i].upgrade_need_shulian
			if left_shuliandu < 0 then
				break
			end

			strength_level = data_list[i].strength_level
		end
	end

	if strength_level >= 0 then
		return strength_level
	else
		return cell_data.strength_level
	end
end

function ShenShouWGData:IsShenShouWearEquip(soul_ring_seq)
	local equip_list_info = self:GetShenShouEquipInfoList(soul_ring_seq)

	if not IsEmptyTable(equip_list_info) then
		for k, v in pairs(equip_list_info) do
			if v.item_id > 0 then
				return true
			end
		end
	end
	
	return false
end

-- 获取当前选中魂环
function ShenShouWGData:GetViewSelectSoulRingSeq()
	return self.view_select_soul_ring_seq
end

function ShenShouWGData:IsViewSelectSoulRingUnlock()
	return self:IsSoulRingUnLock(self.view_select_soul_ring_seq)
end

-- 设置当前选中魂环
function ShenShouWGData:SetViewSelectSoulRingSeq(view_select_soul_ring_seq)
	self.view_select_soul_ring_seq = view_select_soul_ring_seq
end

-- 背包装备 按照 slot_index 进行  quality 星级 降序排序  
-- 按照slot_index 评分排序    基础 base_score  >   总评分score
function ShenShouWGData:CalShenShouEquipList()
	local shenshou_equip_data_cache = {}
	local equip_total_shuliandu = 0
	local shenshou_bag_list = {}
	local has_stone_flag = false

	local data_list = self:GetShenshouGridList()
	if not IsEmptyTable(data_list) then
		for k, v in pairs(data_list) do
			if v.is_equip ~= 0 then
				shenshou_equip_data_cache[v.slot_index] = shenshou_equip_data_cache[v.slot_index] or {}
				table.insert(shenshou_equip_data_cache[v.slot_index], v)
			else
				equip_total_shuliandu = equip_total_shuliandu + v.contain_shulian
				has_stone_flag = true
			end

			shenshou_bag_list[#shenshou_bag_list + 1] = v
		end
	end

	for k, v in pairs(shenshou_equip_data_cache) do
		table.sort(v, SortTools.KeyUpperSorters("base_score", "score", "star_count"))
	end
	
	self.shenshou_equip_data_cache = shenshou_equip_data_cache
	self.equip_total_shuliandu = equip_total_shuliandu
	self.shenshou_bag_list = shenshou_bag_list
	self.has_stone_flag = has_stone_flag
end

function ShenShouWGData:GetShenShouEquipTotalShuLianDu()
	return self.equip_total_shuliandu
end

function ShenShouWGData:GetBestWearEquipData(slot_index)
	return (self.shenshou_equip_data_cache[slot_index] or {})[1]
end

function ShenShouWGData:GetOneKeyWearDataList(soul_ring_seq)
	local data_list = {}

	local cur_equip_list = self:GetShenShouEquipInfoList(soul_ring_seq)

	for k, v in pairs(cur_equip_list) do
		-- 评分最高的可以穿戴的装备
		local best_equip_data = self:GetBestWearEquipData(v.slot_index)

		if not IsEmptyTable(best_equip_data) then
			if v.item_id > 0 then
				-- 镶嵌装备的评分
				local item_score, item_base_score = self:GetShenShouItemScore(v)
				-- print_error(best_equip_data.base_score, item_base_score, best_equip_data.score, item_score)
				local base_score_better = best_equip_data.base_score > item_base_score
				local pin_fen_better = (best_equip_data.base_score == item_base_score and best_equip_data.score > item_score)
				local star_better = (best_equip_data.base_score == item_base_score and best_equip_data.star_count > v.star_count)

				if base_score_better or pin_fen_better or star_better then
					table.insert(data_list, best_equip_data)
				end

				-- if best_equip_data.base_score > item_base_score or (best_equip_data.base_score == item_base_score and best_equip_data.score > item_score) then
				-- 	table.insert(data_list, best_equip_data)
				-- end
			else
				table.insert(data_list, best_equip_data)
			end
		end
	end

	return data_list
end

-- 是否拥有更好的替换装备
function ShenShouWGData:IsHasBetterShenShouEquip(data)
	local equip_cfg = self:GetShenShouEqCfg(data.item_id)

	if IsEmptyTable(equip_cfg) then
		return false
	end

	local score, base_score = 0, 0
	if nil == data.base_score or nil == data.score then
		score, base_score = self:GetShenShouItemScore(data)
	else
		score, base_score = data.score, data.base_score
	end

	local best_data = self:GetBestWearEquipData(equip_cfg.slot_index)

	if IsEmptyTable(best_data) then
		return false
	end

	if best_data.base_score > base_score then
		return true
	elseif best_data.base_score == base_score then
		if best_data.star_count > data.star_count then
			return true
		end

		if best_data.star_count == data.star_count and best_data.score > score then
			return true
		end
	end

	return false
end

-- 魂环孔位装备能否强化
function ShenShouWGData:IsShenShouEquipStrengthUp(soul_ring_seq, slot_index)
	local equip_info = self:GetShenShouEquipInfo(soul_ring_seq, slot_index)

	if not IsEmptyTable(equip_info) then
		if equip_info.item_id > 0 then
			local strength_level = equip_info.strength_level
			local cur_cfg = self:GetShenshouLevelList(slot_index, strength_level)
			local next_cfg = self:GetShenshouLevelList(slot_index, strength_level + 1)

			if IsEmptyTable(next_cfg) then
				return false
			end

			-- local has_shuliandu = self:GetShenShouEquipTotalShuLianDu()
			-- if has_shuliandu + equip_info.shuliandu >= cur_cfg.upgrade_need_shulian then
			local shenshou_exp = self:GetShenShouExp()
			if (shenshou_exp + equip_info.shuliandu) >= cur_cfg.upgrade_need_shulian then
				return true
			end
		end
	end

	return false
end

function ShenShouWGData:IsSHenShouActiveStuffItemId(change_item_id)
	return self.shenshou_ring_active_item[change_item_id]
end

-- 拆解数据
function ShenShouWGData:GetEquipDecomposeDataList(data)
	local data_list = {}
	local decompose_cfg = self:GetDecomposeCfg(data.decompose_v_item_id, data.decompose_need_quality, data.decompose_need_star_num)

	if not IsEmptyTable(decompose_cfg) then
		if decompose_cfg.put_equip_v_id > 0 then
			data_list[1] = decompose_cfg.put_equip_v_id
			data_list[2] = decompose_cfg.put_equip_v_num
		end

		if decompose_cfg.put_item_id > 0 then
			data_list[3] = decompose_cfg.put_item_id
			data_list[4] = decompose_cfg.put_item_num
		end
	end
	
	return data_list
end

--获得一件神兽装备综合评分
function ShenShouWGData:GetShenShouItemScore(item_data)
	if IsEmptyTable(item_data) or item_data.item_id <= 0 then
		return 0, 0
	end

	local base_value = 0
	local add_value = 0
	local base_num = 0
	local pingfen = 0
	local base_score = 0
	local sum_add_value = 0

	local shenshou_equip_cfg = self:GetShenShouEqCfg(item_data.item_id)
	if not shenshou_equip_cfg or shenshou_equip_cfg.is_equip == 0 then
		return 0, 0
	end

	local slot_index = shenshou_equip_cfg.slot_index
	local quality = shenshou_equip_cfg.quality
	local strength_level = item_data.strength_level

	-- 基础+强化属性
	if not (self.base_attr_table[slot_index] and self.base_attr_table[slot_index][quality])
	   or not (self.qh_attr_table[slot_index] and self.qh_attr_table[slot_index][strength_level]) then
		local base_shenshou_cfg = self:GetShenshouBaseList(shenshou_equip_cfg.slot_index, shenshou_equip_cfg.quality)
		local qh_shenshou_cfg = self:GetShenshouLevelList(shenshou_equip_cfg.slot_index, item_data.strength_level)
		local base_attr_struct = AttributeMgr.GetAttributteByClass(base_shenshou_cfg)
		local qh_attr_struct = AttributeMgr.GetAttributteByClass(qh_shenshou_cfg)

		local attr_keys = AttributeMgr.SortAttribute()
		for k,v in pairs(attr_keys) do
			if base_attr_struct[v] and qh_attr_struct[v] then
				base_value = math.floor(base_attr_struct[v])
				add_value = math.floor(qh_attr_struct[v])
				if base_value > 0 then
					base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v)
					base_score = base_score + base_num * base_value
					if add_value > 0 then
						sum_add_value = sum_add_value + base_num * add_value
					end
				end
			end
		end

		pingfen = base_score + sum_add_value

		if not self.base_attr_table[slot_index] then
			self.base_attr_table[slot_index] = {}
			self.base_attr_table[slot_index][quality] = base_score
		elseif not self.base_attr_table[slot_index][quality] then
			self.base_attr_table[slot_index][quality] = base_score
		end

		self.qh_attr_table[slot_index] = self.qh_attr_table[slot_index] or {}
		if strength_level then
			self.qh_attr_table[slot_index][strength_level] = sum_add_value
		end

		-- if not self.qh_attr_table[slot_index] then
		-- 	self.qh_attr_table[slot_index] = {}
		-- 	if strength_level then
		-- 		self.qh_attr_table[slot_index][strength_level] = sum_add_value
		-- 	end
		-- elseif not self.qh_attr_table[slot_index][strength_level] then
		-- 	self.qh_attr_table[slot_index][strength_level] = sum_add_value
		-- end
	else
		base_score = self.base_attr_table[slot_index][quality]
		sum_add_value = self.qh_attr_table[slot_index][strength_level]
		pingfen = base_score + sum_add_value
	end

	-- 仙品属性
	if item_data.attr_list and not IsEmptyTable(item_data.attr_list) then
		for k,v in pairs(item_data.attr_list) do
			if v.attr_type > 0 and v.attr_value then
				base_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(0, v.attr_type, v.attr_value, 0, 2)
				pingfen = pingfen + base_num
				base_score = base_score + base_num
			end
		end
	end

	return pingfen, base_score
end

-- 获取合成面板的随机属性展示
function ShenShouWGData:GeRandomAttrByHeCheng(data)
	if not data then return {} end
	local star_count = data.star_count and data.star_count or 0
	-- 部位类型 1 攻击 2 防御 3 综合
	local item_cfg = self:GetShenShouEqCfg(data.item_id)
	local random_quilty = item_cfg and item_cfg.quality or 0
	local random_type = item_cfg and item_cfg.buwei or 0
	-- 所有符合部位类型的属性
	local attr_list = {}
	for k,v in pairs(self.shenshou_cfg.equip_attr) do
		if v.is_star_attr == 1 and random_quilty == v.quality and (v.buwei == random_type or random_type == 3) then
			table.insert(attr_list,v)
		end
	end

	local show_attr_list = {}
	if #attr_list > star_count then
		math.randomseed(os.time())
		local attr_index = math.random(#attr_list)
		show_attr_list[attr_index] = attr_list[attr_index]
		local count = 1
		while count < star_count do
			attr_index = math.random(#attr_list)
			count = show_attr_list[attr_index] and count or (count + 1)
			show_attr_list[attr_index] = show_attr_list[attr_index] or attr_list[attr_index]
		end
	else
		show_attr_list = attr_list
	end

	return show_attr_list
end

--合成那边的提升箭头          -- 判断是否是能镶嵌或者是更好的替换装备
function ShenShouWGData:GetShenShouEquipHaveUpFlag(item_data)
	if item_data == nil then
		 return false 
	end

	local shenshou_cfg = self:GetShenShouEqCfg(item_data.item_id)
	if shenshou_cfg == nil or shenshou_cfg.is_equip == 0 then
		return false
	end

	local item_score, item_base_score = item_data.score, item_data.base_score
	if item_score == nil or item_base_score == nil then
		if item_data == nil then
			item_score = 0
			item_base_score = 0
		else
			item_score, item_base_score = self:GetShenShouItemScore(item_data)
		end
	end

	for k, v in pairs(self:GetSoulRingDataList()) do
		if self:IsSoulRingUnLock(v.soul_ring_seq) then
			local equip_list = self:GetShenShouEquipInfoList(v.soul_ring_seq)

			for k,v in pairs(equip_list) do
				if v.item_id > 0 then
					local equip_score, equip_base_score = self:GetShenShouItemScore(v)

					if v.slot_index == shenshou_cfg.slot_index then
						if item_base_score > equip_base_score then
							return true
						elseif item_base_score == equip_base_score then
							if item_data.star_count > v.star_count then
								return true
							end

							if item_score > equip_score then
								return true
							end
						end
					end
				else
					if v.slot_index == shenshou_cfg.slot_index then
						return true
					end
				end
			end
		end
	end

	return false
end

--合成那边的提升箭头
function ShenShouWGData:GetComposeProductUpFlag(item_data)
	local shenshou_equip_cfg = self:GetShenShouEqCfg(item_data.item_id)
	local item_star_count = item_data.star_count or 0

	if shenshou_equip_cfg == nil or shenshou_equip_cfg.is_equip == 0 then
		return false
	end

	for k, v in pairs(self:GetSoulRingDataList()) do
		if self:IsSoulRingUnLock(v.soul_ring_seq) then
			local equip_list = self:GetShenShouEquipInfoList(v.soul_ring_seq)
			
			for k,v in pairs(equip_list) do
				if v.slot_index == shenshou_equip_cfg.slot_index and shenshou_equip_cfg.quality and v.quality and v.star_count then
					if shenshou_equip_cfg.quality > v.quality or (shenshou_equip_cfg.quality == v.quality and item_star_count > v.star_count) then
						return true
					end
				end
			end
		end
	end

	return false
end

function ShenShouWGData:GetIsShenShouEquip(item_id)
	if not item_id then 
		return false 
	end

	local shenshou_equip_cfg = self:GetShenShouEqCfg(item_id)
	if shenshou_equip_cfg and shenshou_equip_cfg.is_equip == 1 then
		return true
	else
		return false
	end
end

function ShenShouWGData:FilterShenShouEq(quality, star)
	local list = {}
	local i = 1
	local cur_bag_list = self:GetShenShouBagDataList()
	local bag_cfg = {}
	local sort_list_index = 1
	for k,v in pairs(cur_bag_list) do
		bag_cfg[sort_list_index] = v
		sort_list_index = sort_list_index + 1
	end

	if star == -1 then
		if quality == -1 then
			table.sort(bag_cfg, self:SortList("quality", "star_count", "is_equip"))
			return bag_cfg
		else
			for k,v in pairs(bag_cfg) do
				if quality == v.quality then
					list[i] = v
					i = i + 1
				end
			end
			table.sort(list, self:SortList("quality", "star_count", "is_equip"))
			return list
		end
	else
		if quality == -1 then
			for k,v in pairs(bag_cfg) do
				if star == v.star_count and v.is_equip ==1 then
					list[i] = v
					i = i + 1
				end
			end
			table.sort(list, self:SortList("quality", "star_count", "is_equip"))
			return list
		else
			for k,v in pairs(bag_cfg) do
				if quality == v.quality and star == v.star_count and v.is_equip ==1 then
					list[i] = v
					i = i + 1
				end
			end
			table.sort(list, self:SortList("quality", "star_count", "is_equip"))
			return list
		end
	end
end

function ShenShouWGData:SortList(sort_key_name1, sort_key_name2, sort_key_name3,slot_index,is_maxlevel)
	return function(a, b)
		local order_a = 100000
		local order_b = 100000
		if a[sort_key_name1] > b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] < b[sort_key_name1] then
			order_b = order_b + 10000
		end

		if nil == sort_key_name2 then  return order_a > order_b end

		if a[sort_key_name2] > b[sort_key_name2] then
			order_a = order_a + 1000
		elseif a[sort_key_name2] < b[sort_key_name2] then
			order_b = order_b + 1000
		end

		if nil == sort_key_name3 then  return order_a > order_b end

		if a[sort_key_name3] > b[sort_key_name3] then
			order_a = order_a + 100
		elseif a[sort_key_name3] < b[sort_key_name3] then
			order_b = order_b + 100
		end

		if nil == slot_index then  return order_a > order_b end

		if a[slot_index] < b[slot_index] then
			order_a = order_a + 10
		elseif a[slot_index] > b[slot_index] then
			order_b = order_b + 10
		end

		if nil == is_maxlevel then return order_a > order_b end
		if a[is_maxlevel] < b[is_maxlevel] then
			order_a = order_a + 100000
		elseif a[is_maxlevel] > b[is_maxlevel] then
			order_b = order_b + 100000
		end

		return order_a > order_b
	end
end

-- 需要显示所有的技能  0级得技能显示灰色
function ShenShouWGData:GetSoulRingSkillData(shou_id)
	local shou_cfg = self:GetShenShouCfg(shou_id)

	local loop = 1
	local list = {}

	if shou_cfg then
		for i = 1, 8 do
			local skill_id = shou_cfg["skill_id_" .. i]
			local skill_level = shou_cfg["skill_level_" .. i]

			if skill_id and skill_id ~= "" and skill_id > 0 and skill_level and skill_level ~= "" and skill_level >= 0 then
				table.insert(list, {skill_type = skill_id, level = skill_level})
			end
		end
	end

	return list
end

function ShenShouWGData:IsEquipBagHasStoneFlag()
	return self.has_stone_flag
end

function ShenShouWGData:GetActiveSoulRingNum()
	local num = 0

	for k, v in pairs(self:GetSoulRingDataList()) do
		if self:IsSoulRingUnLock(v.soul_ring_seq) then
			num = num + 1
		end
	end

	return num
end

function ShenShouWGData:IsSoulRingAllEquipMaxLevel(soul_ring_seq)
	local equip_list = self:GetShenShouEquipInfoList(soul_ring_seq)
	local is_all_max_level = true

	if not IsEmptyTable(equip_list) then
		for k, v in pairs(equip_list) do
			if v.item_id > 0 then
				local next_strength_cfg = ShenShouWGData.Instance:GetShenshouLevelList(v.slot_index, v.strength_level + 1)

				if not IsEmptyTable(next_strength_cfg) then
					is_all_max_level = false
					break
				end
			end
		end
	else
		is_all_max_level = false
	end

	return is_all_max_level
end
----------------------------------------------------cal_end---------------------------------------------------

----------------------------------------------------CMD_START-------------------------------------------------
function ShenShouWGData:CMDAddShenShouEquip()
	local now_num = 0
	if self.grid_list then
		now_num = #self.grid_list
	end
	local max_num = 200

	if not self.equip_type_num then
		self.equip_type_num = 0
	end
	if self.equip_type_num == 0 then
		for k,v in pairs(self.shenshou_equip_cfg) do
			if v.buwei > 0 then
				self.equip_type_num = self.equip_type_num + 1
			end

		end
	end

	local gm_additem_num = math.floor((max_num - now_num) / self.equip_type_num)
	local gm_left_num = max_num - now_num - self.equip_type_num * gm_additem_num

	for k,v in pairs(self.shenshou_equip_cfg) do
		if v.buwei > 0 then
			if gm_additem_num > 0 then
				SysMsgWGCtrl.SendGmCommand("additem", v.item_id .." ".. gm_additem_num .. " " .. 0)
			end
			if gm_left_num > 0 then
				SysMsgWGCtrl.SendGmCommand("additem", v.item_id .." ".. 1 .. " " .. 0)
				gm_left_num = gm_left_num - 1
			end
		end
	end
end
-----------------------------------------------------CMD_END--------------------------------------------------
