﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[CreateAssetMenu(menuName = "MySubMenue/Create YYFogProfile ")]
public class YYFogProfile : ScriptableObject
{
    [Header("Fog appearance")]
    /// <summary>Fog gradient color based on sun position where center is sun at horizon.</summary>
    [Tooltip("Fog gradient color based on sun position where center is sun at horizon.")]
    public Gradient FogGradientColor = new Gradient();

    /// <summary>Fog color</summary>
    [Tooltip("Fog color")]
    public Color FogColor = Color.white;

    /// <summary>The depth where fog starts (in world space units). Ignored if fog is not full screen or fog has a height limit.</summary>
    [Tooltip("The depth where fog starts (in world space units). Ignored if fog is not full screen or fog has a height limit.")]
    [Range(0.0f, 1000.0f)]
    public float FogStartDepth = 0.0f;

    /// <summary>The depth where fog ends (in world space units). Ignored if 0 or fog is not full screen or fog has a height limit.</summary>
    [Tooltip("The depth where fog ends (in world space units). Ignored if 0 or fog is not full screen or fog has a height limit.")]
    [Range(0.0f, 100000.0f)]
    public float FogEndDepth = 0.0f;

    /// <summary>Fog density</summary>
    [Tooltip("Fog density")]
    [Range(0.0f, 1.0f)]
    public float FogDensity = 0.0f;
    internal float fogDensity;

    [Tooltip("Multiply the computed fog factor by this value")]
    [Range(1.0f, 100.0f)]
    public float FogFactorMultiplier = 1.0f;

    /// <summary>Maximum fog factor, where 1 is the maximum allowed fog.</summary>
    [Tooltip("Maximum fog factor, where 1 is the maximum allowed fog.")]
    [Range(0.0f, 1.0f)]
    public float MaxFogFactor = 1.0f;


    /// <summary>Fog density gradient based on sun position where center is sun at horizon. Use alpha, color is ignored.</summary>
    [Tooltip("Fog density gradient based on sun position where center is sun at horizon. Use alpha, color is ignored.")]
    public Gradient FogDensityGradient = new Gradient();



    /// <summary>Fog light absorption - lower values absorb more light, higher values scatter and intensify light more.</summary>
    [Range(0.0f, 10.0f)]
    [Tooltip("Fog light absorption - lower values absorb more light, higher values scatter and intensify light more.")]
    public float FogLightAbsorption = 1.0f;

    /// <summary>How much cloud shadows effect the fog, set to 0 for indoor fog.</summary>
    [Tooltip("How much cloud shadows effect the fog, set to 0 for indoor fog.")]
    [Range(0.0f, 1.0f)]
    public float FogCloudShadowStrength = 1.0f;


    private float fogScatterReduction = 1.0f;
    public float FogScatterReduction { get { return fogScatterReduction; } }

    public virtual void UpdateMaterialProperties(Material material, Camera camera, bool global)
    {
        YYCelestialObject sun = YYLightManager.SunForCamera(camera);
        if (sun == null)
        {
            Debug.Log("sun == null");
            return;
        }
        Color fogDensityColor = sun.GetGradientColor(FogDensityGradient);
        fogDensity = FogDensity * fogDensityColor.a;

        float endDepth = (FogEndDepth <= FogStartDepth ? FogStartDepth + 5000.0f : FogEndDepth);

        float fogLinearFogFactor = 0.1f * (1.0f / Mathf.Max(0.0001f, (endDepth - FogStartDepth))) * fogDensity * (endDepth - FogStartDepth);

        Color fogColor = sun.GetGradientColor(FogGradientColor) * FogColor;
        //Shader.SetGlobalColor(WMS._WeatherMakerFogColor, fogColor);
        material.SetColor(WMS._WeatherMakerFogColor, fogColor);

        material.SetFloat(WMS._WeatherMakerFogStartDepth, FogStartDepth);
        material.SetFloat(WMS._WeatherMakerFogEndDepth, endDepth);

        //由deth  影响 fogfactor
        material.SetFloat(WMS._WeatherMakerFogLinearFogFactor, fogLinearFogFactor);
        material.SetFloat(WMS._WeatherMakerFogFactorMax, MaxFogFactor);
        material.SetFloat(WMS._WeatherMakerFogFactorMultiplier, FogFactorMultiplier);

        //散射
        material.SetFloat(WMS._WeatherMakerFogLightAbsorption, FogLightAbsorption);

        material.SetFloat(WMS._WeatherMakerFogDensity, fogDensity);

        //
        ///FogMode == WeatherMakerFogMode.Linear
        float scatterCover = (YYFullScreenClouds.Instance != null && YYFullScreenClouds.Instance.enabled && YYFullScreenClouds.Instance.CloudProfile != null ? YYFullScreenClouds.Instance.CloudProfile.CloudCoverTotal : 0.0f);
        scatterCover = Mathf.Pow(scatterCover, 4.0f);

        fogScatterReduction = Mathf.Clamp((1.0f - ((fogDensity + scatterCover) * 0.25f)), 0.15f, 1.0f);
        material.SetFloat(WMS._WeatherMakerFogDensityScatter, fogScatterReduction);


        material.SetFloat(WMS._WeatherMakerFogCloudShadowStrength, FogCloudShadowStrength);
    }


}
