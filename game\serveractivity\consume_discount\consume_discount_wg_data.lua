ConsumeDiscountWGData = ConsumeDiscountWGData or BaseClass()

function ConsumeDiscountWGData:__init()
	if ConsumeDiscountWGData.Instance ~= nil then
		ErrorLog("[ConsumeDiscountWGData] attempt to create singleton twice!")
		return
	end
	ConsumeDiscountWGData.Instance = self
end

function ConsumeDiscountWGData:__delete()
	ConsumeDiscountWGData.Instance = nil
end

function ConsumeDiscountWGData:SetRAContinueConsumeInfo(protocol)
	self.continue_consume_t = {}
	self.continue_consume_t.today_consume_gold_total = protocol.today_consume_gold_total
	self.continue_consume_t.cur_consume_gold = protocol.cur_consume_gold
	self.continue_consume_t.continue_days_total = protocol.continue_days_total
	self.continue_consume_t.continue_days = protocol.continue_days
	self.continue_consume_t.current_day_index = protocol.current_day_index
	self.continue_consume_t.extra_reward_num = protocol.extra_reward_num
	self.continue_consume_t.has_fetch_flag = protocol.has_fetch_flag
end

function ConsumeDiscountWGData:GetRAContinueConsumeInfo()
	return self.continue_consume_t
end

function ConsumeDiscountWGData:GetRAContinueConsumeRewardNum()
	local consume_info = self:GetRAContinueConsumeInfo()
	local randact_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if consume_info and randact_cfg and randact_cfg.continue_consume then
		local need_consume_gold = randact_cfg.continue_consume[consume_info.current_day_index].need_consume_gold or 0
		local count = consume_info.cur_consume_gold > need_consume_gold and 1 or 0
		count = count + consume_info.extra_reward_num
		return count
	end
	return 0
end

function ConsumeDiscountWGData:GetRewardData()
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if nil == cfg or nil == cfg.continue_consume then return end
	local data_list = {}
	local index = 1
	local rand_t = cfg.continue_consume
	local activity_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME)
	if activity_status == nil then
		return {}
	end
	local openday_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	local day  = math.ceil((activity_status.start_time  - openday_time)/ (3600*24))
	local continue_consume_cfg = ServerActivityWGData.Instance:GetRandHappyActivity(rand_t, day)
	for k, v in pairs(continue_consume_cfg) do
		local cfg = ItemWGData.Instance:GetItemConfig(v.reward_item.item_id)
		local datalist = {}
		for i = 1, 4 do
			local data = {}
			data["item_id"] = cfg["item_" .. i .. "_id"]
			data["num"] = cfg["item_" .. i .. "_num"] * v.reward_item.num
			data["is_bind_"] = cfg["is_bind_" .. i]
			datalist[i] = data
		end
		data_list[index] = datalist
		data_list[index].day_index = v.day_index
		data_list[index].need_consume_gold = v.need_consume_gold
		index = index + 1
	end
	return data_list
end