MarryTiQinView = MarryTiQinView or BaseClass(SafeBaseView)

local TiQinColorList1 = {"#471C5B", "#AF3804", "#A72676"}
local TiQinColorList2 = {"#6B4170", "#92623B", "#94627D"}
--[[
local MARRIAGW_PROMOTION_TYPE = {
	SELECT_OBJECT = 1,    -- 选择对象
	SELECT_TOKEN = 2,     -- 选择信物
}

local MARRING_MENBER_TYPE = {
	OPPOSITE_SEX = 1,   -- 异性
	SAME_SEX = 2,       -- 同性
	RECOMMEND = 3,      -- 推荐仙缘
}
]]

function MarryTiQinView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_tiqin")

	self.lover_name = ""
	self.lover_id = 0
	self.marry_type = -1
	self.select_role_id = 0
	self.select_role_name = ""
	self.select_prof = 0
	self.select_sex = 0
	--self.select_panel_index = MARRIAGW_PROMOTION_TYPE.SELECT_OBJECT
	self.marry_friend_list = {} -- 结婚对象列表
	self.cur_select_index = -1
end

--打开之前请求一下好友信息
function MarryTiQinView:OpenCallBack()
	SocietyWGCtrl.Instance:SendFriendInfoReq()
end

function MarryTiQinView:LoadCallBack()
	if not self.marry_item_list then
		--创建选择婚宴列表
		self.marry_item_list = AsyncListView.New(MarryListItemRender, self.node_list["ph_marry_list"])
		self.marry_item_list:SetDefaultSelectIndex(2)
		self.marry_item_list:SetSelectCallBack(BindTool.Bind1(self.SelectMarrtListItemCallBack, self))
		local marry_cfg = MarryWGData.Instance:GetMarryCfg() or {}
		self.marry_item_list:SetDataList(marry_cfg, 0)
	end

	-- if nil == self.yx_tiqin_friend_list then
	-- 	self.yx_tiqin_friend_list = AsyncBaseGrid.New()
	-- 	local bundle = "uis/view/marry_ui_prefab"
	-- 	local asset = "ph_tiqin_friend_item"
	-- 	self.yx_tiqin_friend_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["ph_yx_friend_list"],
	-- 	assetBundle = bundle, assetName = asset, itemRender = TiQinFriendListItemRender})
	-- 	self.yx_tiqin_friend_list:SetSelectCallBack(BindTool.Bind1(self.OnClickYXFriendRenderCalllBack, self))
	-- 	self.yx_tiqin_friend_list:SetStartZeroIndex(false)
	-- end

	self:SetSelfHeadInfo() -- 设置自己的头像信息

	-- for i = 1, 2 do
	-- 	XUI.AddClickEventListener(self.node_list["tag_btn_"..i], BindTool.Bind(self.OnClickTiQinTagBtn, self, i))
	-- end

	XUI.AddClickEventListener(self.node_list["btn_tiqin"], BindTool.Bind1(self.SelectRingHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_open_add_friend"], BindTool.Bind1(self.OnClickNoFriendCallback, self))
	--XUI.AddClickEventListener(self.node_list["btn_tiqin_tips"], BindTool.Bind1(self.OnClickTiQinHandler, self))
	--XUI.AddClickEventListener(self.node_list["btn_tiqin_invite"], BindTool.Bind1(self.OnClickTiQinInvite, self))

	-- self.select_menber_tog_index = MARRING_MENBER_TYPE.OPPOSITE_SEX
	-- for i = 1, 3 do
	-- 	self.node_list["SelectBtn" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickMenberTogList, self, i))
	-- end

	-- self.node_list["SelectBtn" .. self.select_menber_tog_index].toggle.isOn  = true

	self.node_list.txt_no_friend.text.text = Language.Marry.NoFriend
	self.node_list.txt_tips_1.text.text = Language.Marry.TiQInTips1
	self.node_list.txt_tips_2.text.text = Language.Marry.TiQinTips2

	self.node_list["select_target_dropdown"].dropdown.onValueChanged:AddListener(BindTool.Bind1(self.OnDropdownChange, self))
	self.dropdown_select = 0
	self.node_list["select_target_dropdown"].dropdown.value = self.dropdown_select

	self.is_first_load = true
end

function MarryTiQinView:CloseCallBack()
	self.select_role_id = 0
	self.select_role_name = ""
	self.select_prof = 0
	self.select_sex = 0
	self.lover_name = ""
	self.lover_id = 0
	if self.cur_yx_item then
		self.cur_yx_item:SetSelectGouState(false)
	end

	if self.cur_tx_item then
		self.cur_tx_item:SetSelectGouState(false)
	end
end

function MarryTiQinView:ReleaseCallBack()
	if self.marry_item_list then
		self.marry_item_list:DeleteMe()
		self.marry_item_list = nil
    end

	if self.yx_tiqin_friend_list then
		self.yx_tiqin_friend_list:DeleteMe()
		self.yx_tiqin_friend_list = nil
	end

	if self.my_head_cell then
		self.my_head_cell:DeleteMe()
		self.my_head_cell = nil
	end

	if self.targe_head_cell then
		self.targe_head_cell:DeleteMe()
		self.targe_head_cell = nil
	end

	if self.tiqin_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.tiqin_timer)
        self.tiqin_timer = nil
    end

	--self.select_panel_index = MARRIAGW_PROMOTION_TYPE.SELECT_OBJECT
	self.lover_name = ""
	self.lover_id = 0
	self.marry_type = -1
	self.select_role_id = 0
	self.select_role_name = ""
	self.select_prof = 0
	self.select_sex = 0
	self.is_select = false
	self.cur_tx_item = nil
	self.cur_yx_item = nil
	self.select_menber_tog_index = nil

	self.marry_friend_list = nil
	self.cur_select_index = -1
end

function MarryTiQinView:OnFlush()
	--self:FlushTiQinMenberList()
	self.marry_friend_list = SocietyWGData.Instance:GetMarryFriendListNoSex()
	self:FlushDropDown()

	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local lover_prof = MarryWGData.Instance:GetLoverInfo() or 0

	if lover_id > 0 then
		BrowseWGCtrl.Instance:BrowRoelInfo(lover_id, BindTool.Bind1(self.GetLoverInfoCallBack, self))
	end

	-- if self.open_select_index > 0 then
	-- 	self:OnDropdownChange(self.open_select_index)
	-- 	self.open_select_index = 0
	-- end
	if self.select_role_id > 0 then
		self:FlushSpecialState()
	else
		self.node_list.txt_no_friend.text.text = Language.Marry.NoFriend
	end

    -- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.lbl_lover_name.rect)
	-- local level_limit = MarryWGData.Instance:GetMarryOtherCfg().marry_limit_level
	-- self.node_list["lbl_marry_level_limit"].text.text = string.format(Language.Marry.MarryLevelLimit, level_limit)
	-- self.node_list["lbl_marry_level_limit"].text.text = Language.Marry.MarryLevelLimit
	-- local qinmi_limit = MarryWGData.Instance:GetMarryOtherCfg().marry_limit_intimacy
	-- self.node_list["lbl_marry_qinmi_limit"].text.text = string.format(Language.Marry.MarryQinMiLimit, qinmi_limit)
	-- self.node_list["lbl_marry_qinmi_limit"].text.text = Language.Marry.MarryQinMiLimit
end

-- 刷新下拉框
function MarryTiQinView:FlushDropDown()
	local dropdown = self.node_list["select_target_dropdown"].dropdown
	dropdown:ClearOptions()

	local is_empty_friend = IsEmptyTable(self.marry_friend_list)
	self.node_list.select_target_dropdown:SetActive(not is_empty_friend)
	self.node_list.btn_open_add_friend:SetActive(is_empty_friend)

	if not is_empty_friend then
		local list_string = System.Collections.Generic.List_string.New()
		list_string:Add(Language.Marry.TiQinDuiXiang)
		for k, v in ipairs(self.marry_friend_list) do
			list_string:Add(v.gamename)
		end
		dropdown:AddOptions(list_string)
		dropdown.value = 0
	end
end

function MarryTiQinView:OnClickNoFriendCallback()
	SocietyWGCtrl.Instance:SendGetRandomRoleList()
end

function MarryTiQinView:GetLoverInfoCallBack(protocol)
	if not protocol then
		print_error("protocol error！！")
		return
    end

	MarryWGData.Instance:SetLoverInfo2(protocol)
    -- self:SetLoverHead(protocol)
end

function MarryTiQinView:OnDropdownChange(index)
	self.select_role_id = 0
	self.select_role_name = ""
	self.select_prof = 0
	self.select_sex = 0

	self.cur_select_index = index

	if index < 1 then
		self.lover_id = 0
		self.lover_name = ""
	else
		self.lover_id = self.marry_friend_list[index].user_id
		self.lover_name = self.marry_friend_list[index].gamename
	end
	self:FlushMarryTarget()
end

-- 设置自己头像等信息
function MarryTiQinView:SetSelfHeadInfo()
	if not self.my_head_cell then
		self.my_head_cell = BaseHeadCell.New(self.node_list["head_cell_node_self"])
	end
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local data = {}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true
	self.my_head_cell:SetData(data)
	self.my_head_cell:SetBgActive(false)
	--自己的名字
	self.node_list["txt_my_name"].text.text = role_vo.name
end

-- 刷新选择的对象信息
function MarryTiQinView:FlushMarryTarget()
	if self.cur_select_index < 1 or not self.marry_friend_list then
		if self.targe_head_cell then
			self.targe_head_cell:SetActive(false)
		end
	else
		local cur_data = self.marry_friend_list[self.cur_select_index]
		if not cur_data then
			print_error("当前索引出现问题!")
			return 
		end
		if not self.targe_head_cell then
			self.targe_head_cell = BaseHeadCell.New(self.node_list["head_cell_node_target"])
		end
		self.targe_head_cell:SetActive(true)
		self.targe_head_cell:SetData({role_id = cur_data.user_id, prof = cur_data.prof, sex = cur_data.sex})
		self.targe_head_cell:SetBgActive(false)
	end
end

function MarryTiQinView:FlushSpecialState()
	if self.is_first_load then
		if self.tiqin_timer ~= nil then
			GlobalTimerQuest:CancelQuest(self.tiqin_timer)
			self.tiqin_timer = nil
		end

		self.tiqin_timer = GlobalTimerQuest:AddDelayTimer(function()
			self.node_list.Label.text.text = self.select_role_name
		end, 0.1)

		self.is_first_load = false
	else
		self.node_list.Label.text.text = self.select_role_name
	end

	if not self.targe_head_cell then
		self.targe_head_cell = BaseHeadCell.New(self.node_list["head_cell_node_target"])
	end
	self.targe_head_cell:SetActive(true)
	self.targe_head_cell:SetData({role_id = self.select_role_id, prof = self.select_prof, sex = self.select_sex})
	self.targe_head_cell:SetBgActive(false)

	self.node_list.txt_no_friend.text.text = self.select_role_name
end

function MarryTiQinView:SetSelectRoleId(role_id, role_name, prof, sex)
	self.select_role_id = role_id
	self.select_role_name = role_name
	self.select_prof = prof
	self.select_sex = sex
end

function MarryTiQinView:SelectMarrtListItemCallBack(item)
	if not item or not item:GetData() then return end
	self.marry_type = item:GetData().marry_type
	local data = item.data

	local lbl_gold_str = Language.Marry.MarryPrice1
	local bundle, asset
	if data.need_gold_bind > 0 then
		self.node_list["lbl_gold"].text.text = string.format(lbl_gold_str, data.need_gold_bind)
		bundle, asset = ResPath.GetCommonIcon("a3_huobi_bangyu")
	elseif data.need_lngot > 0 then
		self.node_list["lbl_gold"].text.text = string.format(lbl_gold_str, data.need_lngot)
		bundle, asset = ResPath.GetCommonIcon("a3_huobi_bangyu")---原本是未绑的图标
	else
		self.node_list["lbl_gold"].text.text = string.format(lbl_gold_str, data.need_gold)
		bundle, asset = ResPath.GetCommonIcon("a3_huobi_xianyu")
	end

	if bundle and asset then
		self.node_list["img_gold"].image:LoadSprite(bundle, asset,function ()
			self.node_list["img_gold"].image:SetNativeSize()
		end)
	end

	-- 折扣
	local activity_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.MARRY_DISCOUNT)
	if activity_is_open then
		local marry_discount_cfg = MarryWGData.Instance:GetMarryDiscountCfg()
		self.node_list["lbl_gold"].text.text = string.format(lbl_gold_str, marry_discount_cfg[data.marry_type + 1].need_gold)
	end
end

-- 提亲按钮点击
function MarryTiQinView:SelectRingHandler()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local lover_name = RoleWGData.Instance.role_vo.lover_name

	if lover_id > 0 then
		if (self.lover_id > 0 and lover_id ~= self.lover_id) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.TiQinQiuHunTips)
			return
		end
	end

	-- if lover_id > 0 then
    --     MarryWGCtrl.Instance:OpenProposeView(lover_id, lover_name, self.marry_type)
    --     MarryWGData.Instance:SetCurLoverName(lover_name)
	if self.select_role_id > 0 then
		MarryWGCtrl.Instance:OpenProposeView(self.select_role_id, self.select_role_name, self.marry_type)
        MarryWGData.Instance:SetCurLoverName(self.select_role_name)
	else
        MarryWGCtrl.Instance:OpenProposeView(self.lover_id, self.lover_name, self.marry_type)
        MarryWGData.Instance:SetCurLoverName(self.lover_name)
    end
end

--[[
function MarryTiQinView:ShowIndexCallBack()
	--local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	-- local show_select_type = lover_id > 0 and MARRIAGW_PROMOTION_TYPE.SELECT_TOKEN or MARRIAGW_PROMOTION_TYPE.SELECT_OBJECT
	--self.select_panel_index = show_select_type
	--self:SetTiQinNavBar(self.select_panel_index)

	local friend_list_data = SocietyWGData.Instance:GetMarryFriendList()
	--local select_falg = false
	local intimacy = MarryWGData.Instance:GetMarryOtherCfg().marry_limit_intimacy
	--local index = 0
	for i,v in ipairs(friend_list_data) do
		if self.select_role_id ~= 0 then
			if v.user_id == self.select_role_id then
				--if self.friend_list then
					--self.friend_list:SelectIndex(i)
					--self:ShowFriendList(false)
				--self.open_select_index = i
				--select_falg = true
				break
				--end
			end
		else
			if v.intimacy >= intimacy then
				intimacy = v.intimacy
				-- index = i
			end
		end
	end

	-- if 0 ~= self.select_role_id and not select_falg then
	-- 	SocietyWGData.Instance:IsMarryFriend(self.select_role_id)
	-- 	--SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryLimitTips)
	-- end
end

function MarryTiQinView:SetTiQinNavBar(select_type)
	for i = 1, 2 do
		self.node_list["tag_btn_hl_"..i]:SetActive(select_type == i)
		--self.node_list["group_panel_"..i]:SetActive(select_type == i)
	end
end

-- 切换页签 (Obsolete)
function MarryTiQinView:OnClickTiQinTagBtn(index)
	local navBar_type = MARRIAGW_PROMOTION_TYPE.SELECT_OBJECT
	if index == MARRIAGW_PROMOTION_TYPE.SELECT_OBJECT then
		self:ShowTinQinPanel(MARRIAGW_PROMOTION_TYPE.SELECT_OBJECT)
	elseif index == MARRIAGW_PROMOTION_TYPE.SELECT_TOKEN then
	--判断有无选择，无选择则提示
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
		if lover_id > 0 or self.is_select then
			navBar_type = MARRIAGW_PROMOTION_TYPE.SELECT_TOKEN
			self:ShowTinQinPanel(MARRIAGW_PROMOTION_TYPE.SELECT_TOKEN)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryNoLoverTips)
		end
	end
	self.select_panel_index = navBar_type
	self:SetTiQinNavBar(navBar_type)
end

-- Obsolete
function  MarryTiQinView:ShowTinQinPanel(show_panel_type)
	for i = 1, 2 do
		self.node_list["group_panel_"..i]:SetActive(show_panel_type == i)
	end
end

-- 同性朋友选中
function MarryTiQinView:OnClickFriendRenderCalllBack(item)
	if not item or not item:GetData() then return end
	local item_data = item:GetData()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	self.is_select = true
	if lover_id <= 0 then
		if self.cur_yx_item then
			self.cur_yx_item:SetSelectGouState(false)
		end

		self.lover_id = item_data.user_id
		self.lover_name = item_data.gamename
		item:SetSelectGouState(true)
		self.cur_tx_item = item
	else
		if lover_id ~= item_data.user_id then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.TiQinQiuHunTips)
		end
	end
	self.yx_tiqin_friend_list:CancleAllSelectCell()
end

-- 异性朋友选中
function MarryTiQinView:OnClickYXFriendRenderCalllBack(item)
	if not item or not item:GetData() then return end
	local item_data = item:GetData()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	self.is_select = true
	if lover_id <= 0 then
		if self.cur_tx_item then
			self.cur_tx_item:SetSelectGouState(false)
		end

		self.lover_id = item_data.user_id
		self.lover_name = item_data.gamename
		item:SetSelectGouState(true)
		self.cur_yx_item = item
	else
		if lover_id ~= item_data.user_id then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.TiQinQiuHunTips)
		end
	end

	self.yx_tiqin_friend_list:CancleAllSelectCell()
end

function MarryTiQinView:OnClickTiQinInvite()
	self:OnClickTiQinTagBtn(MARRIAGW_PROMOTION_TYPE.SELECT_TOKEN)
end

function MarryTiQinView:SelectFriendListItemCallBack(item)
	if not item or not item:GetData() then return end
	--self.is_select = true
	local data = item:GetData()
	self.lover_name = data.gamename
	self.lover_id = data.user_id
    --self.node_list["lbl_lover_name"].text.text = data.gamename
    --UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.lbl_lover_name.rect)
	local data1 = {}
	data1.appearance = {fashion_photoframe = data.fashion_photoframe}
	data1.role_id = data.user_id
	data1.prof = data.prof
	data1.sex = data.sex

	self:OnClickFriendList()
end

function MarryTiQinView:OnClickFriendList()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local friend_list_data = SocietyWGData.Instance:GetMarryFriendList()
	if lover_id > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.TiQinQiuHunTips)
		return
	end
	if #friend_list_data == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.NoFriendsCanMarry)
		return
	end
	-- if not self.node_list["layout_friend_list"]:GetActive() then
	-- 	self:FlushFriendList()
	-- end
	-- self:ShowFriendList(not self.node_list["layout_friend_list"]:GetActive())
end

function MarryTiQinView:OnClickTiQinHandler()
	MarryView.OpenTips(Language.Marry.TiQinAfterTips, Language.Marry.TiQinTips)
end

-- Obsolete
function MarryTiQinView:OnClickMenberTogList(index)
	if index == MARRING_MENBER_TYPE.RECOMMEND then
		MarryWGCtrl.Instance:SendGetRandomSingleRoleList()
	end

	if self.select_menber_tog_index ~= index then
		self.is_select = false
	end

	self.select_menber_tog_index = index
	self:FlushTiQinMenberList()
end


function MarryTiQinView:FlushTiQinMenberList()
	local data_list = {}

	if self.select_menber_tog_index == MARRING_MENBER_TYPE.RECOMMEND then
		data_list = MarryWGData.Instance:GetRecommendRoleList()
	else
		local tx_data_list, yx_data_list = SocietyWGData.Instance:GetMarryFriendListIncludeLover()

		if self.select_menber_tog_index == MARRING_MENBER_TYPE.OPPOSITE_SEX then
			data_list = yx_data_list
		else
			data_list = tx_data_list
		end
	end

	self.yx_tiqin_friend_list:SetDataList(data_list)
	self.node_list["tiqin_nodata_1"]:SetActive(IsEmptyTable(data_list))
end
]]--

--------------------------------------------------------------------------
--MarryListItemRender 	婚宴列表
--------------------------------------------------------------------------
MarryListItemRender = MarryListItemRender or BaseClass(BaseRender)
function MarryListItemRender:__init()
end

function MarryListItemRender:__delete()
    if self.marry_item_slot then
        for k, v in pairs(self.marry_item_slot) do
            v:DeleteMe()
        end
        self.marry_item_slot = nil
    end
end

function MarryListItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_title"], BindTool.Bind1(self.OnClickChengHao, self))
end

function MarryListItemRender:SetItemShow(item_data)
    ---[[ 排序时装>称号剩余的品质排
    local temp_list = {}
    local item_cfg = nil
    for k,v in pairs(item_data) do
    	item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
    	if item_cfg then
    		if item_cfg.is_display_role and item_cfg.is_display_role > 0 then
    			if item_cfg.is_display_role == 8 then -- 称号
    				temp_list[1000 + k] = v
    			else
    				temp_list[2000 + k] = v
    			end
    		else
    			temp_list[100 * item_cfg.color + k] = v
    		end
    	end
    end
    item_data = SortTableKey(temp_list, true)
	--]]
	if nil == self.marry_item_slot then
		self.marry_item_slot = {}
	end
	local item_num = #item_data
	local item_alearday_num = #self.marry_item_slot
	for i=1,item_num do
		if nil == self.marry_item_slot[i] then
			self.marry_item_slot[i] = ItemCell.New(self.node_list["ph_cell"])
		end
		self.marry_item_slot[i]:SetData(item_data[i])
	end
	for i=1,item_alearday_num do
		if nil ~= self.marry_item_slot[i] then
			self.marry_item_slot[i]:SetActive(i <= item_num)
		end
	end
end

function MarryListItemRender:SetToggleGroup(group)
	self.node_list["ph_marry_item"].toggle.group = group
end

function MarryListItemRender:OnFlush()
	if not self.data then return end

	local data = self.data
	local bundle, asset = nil, nil

	bundle, asset = ResPath.GetRawImagesPNG("a3_qy_tq_d" .. self.index)
	self.node_list["img_item_bg"].raw_image:LoadSprite(bundle, asset,function ()
		self.node_list["img_item_bg"].raw_image:SetNativeSize()
	end)

	local lbl_gold_str = ToColorStr(Language.Marry.MarryPrice, TiQinColorList2[self.index])
	if data.need_gold_bind > 0 then
		self.node_list["lbl_gold"].text.text = string.format(lbl_gold_str, data.need_gold_bind)
		bundle, asset = ResPath.GetCommonIcon("a3_huobi_bangyu")
	elseif data.need_lngot > 0 then
		self.node_list["lbl_gold"].text.text = string.format(lbl_gold_str, data.need_lngot)
		bundle, asset = ResPath.GetCommonIcon("a3_huobi_bangyu")---原本是未绑的图标
	else
		self.node_list["lbl_gold"].text.text = string.format(lbl_gold_str, data.need_gold)
		bundle, asset = ResPath.GetCommonIcon("a3_huobi_xianyu")
	end

	if bundle and asset then
		self.node_list["img_gold"].image:LoadSprite(bundle, asset,function ()
			self.node_list["img_gold"].image:SetNativeSize()
		end)
	end

	-- 折扣
	local activity_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.MARRY_DISCOUNT)
	self.node_list["img_discount_tag"]:SetActive(activity_is_open)
	if activity_is_open then
		local marry_discount_cfg = MarryWGData.Instance:GetMarryDiscountCfg()
		self.node_list["lbl_gold"].text.text = string.format(lbl_gold_str, marry_discount_cfg[data.marry_type + 1].need_gold)
		self.node_list["txt_discount"].text.text = NumberToChinaNumber(marry_discount_cfg[data.marry_type + 1].show_discount).. "折"
	end

	local is_aleardy_tiqin = MarryWGData.Instance:GetCurRoleIsTiQin(self.data.marry_type)
	local reward_data_1 = {}

	if is_aleardy_tiqin then
		reward_data_1 = self.data.after_reward_item
	else
		reward_data_1 = self.data.reward_item
	end

	local marry_get_id = MarryWGData.Instance:GetMarryRewardLimitInfo()
	local reward_data, power = MarryWGData.Instance:ExculeRewardInfo(reward_data_1, nil, true)

	if is_aleardy_tiqin and power == 0 then
		power = self.data.capability_show
	end
    self.node_list["capability_value"].text.text = power

	self:SetItemShow(reward_data)

	--local bundle,asset = ResPath.GetF2RawImagesPNG("a2_tq_item_bg_" .. self.index)
	--self.node_list["img_item_bg"].raw_image:LoadSpriteAsync(bundle, asset,function ()
		----self.node_list["img_item_bg"].raw_image:SetNativeSize()
	--end)

    --self.node_list["img_tiqin_title"].image:LoadSprite(ResPath.GetJieHunImg("tiqin_title_bg" .. self.index))
	--self.node_list["img_tiqin_title"].image:LoadSprite(ResPath.GetJieHunImg("a2_tq_titlebg_" .. self.index))
    self.node_list["title_name"].text.text = ToColorStr(Language.Marry.MarryTypeTitle[self.index], TiQinColorList1[self.index])
	local path = self.data.title_item_id
	local title_bundle,title_asset = ResPath.GetRoleTitle(path)
	self.node_list["img_title"].image:LoadSprite(title_bundle, title_asset, function()
		self.node_list["img_title"].image:SetNativeSize()
	end)

	self.node_list["ph_count"].text.text = string.format(ToColorStr(Language.Marry.WeddingTimes, TiQinColorList2[self.index]), self.data.wedding_times) --self.data.wedding_times .. Language.Marry.WeddingTimesHight

	--(巡游屏蔽)
	-- if self.index == 3 then
	-- 	self.node_list["ph_count"].text.text = string.format(Language.Marry.WeddingTimesHight, self.data.wedding_times) --self.data.wedding_times .. Language.Marry.WeddingTimesHight
	-- else
	-- 	self.node_list["ph_count"].text.text = string.format(Language.Marry.WeddingTimes, self.data.wedding_times)--self.data.wedding_times .. Language.Marry.WeddingTimes
	-- end
end

function MarryListItemRender:OnSelectChange(is_select)
    self.node_list["ph_marry_item"].toggle.isOn = is_select
	self.node_list["img_marry_select"]:SetActive(is_select)
    -- local num = is_select and 1 or 0.9
    -- if self.node_list.container then
    --     self.node_list.container.transform.localScale = Vector3(num, num, num)
    -- end
end
function MarryListItemRender:OnClickChengHao()
	TipWGCtrl.Instance:OpenItem({item_id = self.data.item_id_1},nil)
end

-------------------------------------------
-- 提亲好友列表
-------------------------------------------
--[[
TiQinFriendListItemRender = TiQinFriendListItemRender or BaseClass(BaseRender)

function TiQinFriendListItemRender:LoadCallBack()
	if not self.data then return end
	-- local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	-- local is_lover = lover_id > 0 and lover_id == self.data.user_id
	-- self.node_list["select_gou"]:SetActive(is_lover)
end

function TiQinFriendListItemRender:__delete()
    if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
    end
end

function TiQinFriendListItemRender:OnFlush()

	if not self.data then return end
	self.node_list["lbl_tiqin_friend_name"].text.text = self.data.gamename
	self:FlushHeadIcon()

	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	XUI.SetGraphicGrey(self.node_list.ph_tiqin_friend_item, lover_id > 0 and lover_id ~= self.data.user_id)
	--XUI.SetButtonEnabled(self.node_list.ph_tiqin_friend_item, lover_id <= 0 or lover_id == self.data.user_id)
end

function TiQinFriendListItemRender:FlushHeadIcon()
	if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["role_icon"])
    end
	self.role_head_cell:SetData({role_id = self.data.user_id, prof = self.data.prof, sex = self.data.sex})
	self.role_head_cell:SetHeadCellScale(0.7)
	self.role_head_cell:SetBgActive(false)
end

function TiQinFriendListItemRender:OnSelectChange(is_select)
	if is_select == false then
		local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
		if lover_id <= 0 or lover_id ~= self.data.user_id then 
			self:SetSelectGouState(is_select)
			--self.node_list["select_gou"]:SetActive(is_select)
		end
	end
	-- local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	-- if lover_id <= 0 or lover_id ~= self.data.user_id then 
	-- 	--self:SetSelectGouState(is_select)
	-- 	self.node_list["select_gou"]:SetActive(is_select)
	-- end

	-- local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	-- if lover_id > 0 and is_select then
	-- 	if 	lover_id == self.data.user_id then
	-- 		self.node_list["select_gou"]:SetActive(is_select)
	-- 	else
	-- 		self.node_list["select_gou"]:SetActive(false)
	-- 		--提示
	-- 		print_error("提示只能跟结的婚对象约会")
	-- 	end
	-- else
	-- 	self.node_list["select_gou"]:SetActive(is_select)
	-- end
end

function TiQinFriendListItemRender:SetSelectGouState(is_select)
	self.node_list["select_gou"]:SetActive(is_select)
end

--能否结婚 false == 亲密度不够  true == 可以结婚
function TiQinFriendListItemRender:CanMarry()
	-- local qinmi_limit = MarryWGData.Instance:GetMarryOtherCfg().marry_limit_intimacy
	-- if self.data.intimacy <= qinmi_limit then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Marry.TiqinIntimacy, qinmi_limit))
	-- 	return false
	-- end
	return true
end
]]