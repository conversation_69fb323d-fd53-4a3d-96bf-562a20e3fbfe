CrossTreasureView = CrossTreasureView or BaseClass(SafeBaseView)
local TAB_TYPE = {
    TREASURE_TAB = 1,
    BEAST_TAB = 2,
}

-- 每8秒获取一次服务器信息
local INTERVAL_SEND_TIME = 15

function CrossTreasureView:__init()
	self:SetMaskBg(false, true)
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
    self:AddViewResource(0, "uis/view/cross_treasure_ui_prefab", "layout_cross_treasure_panel")
end

function CrossTreasureView:OpenCallBack()
    self:SendRequestTreasureOperaTypeInfo()
    self.now_record_time = 0
    Runner.Instance:AddRunObj(self, 10)
end

-- 数据更新
function CrossTreasureView:Update(now_time, elapse_time)
    self.now_record_time = self.now_record_time + elapse_time
    if self.now_record_time >= INTERVAL_SEND_TIME then
        self:SendRequestTreasureOperaTypeInfo()
        self.now_record_time = 0
    end
end

function CrossTreasureView:CloseCallBack()
    if self.node_list and self.node_list.server_message_root then
        self.node_list.server_message_root:CustomSetActive(false)
    end

	Runner.Instance:RemoveRunObj(self)
end

-- 请求一下灵珠信息
function CrossTreasureView:SendRequestTreasureOperaTypeInfo()
     -- 请求一下所有服务器的信息
	for group_seq = 1, CROSS_TREASURE_TYPE.MSG_MAX_TYPE_COUNT do
        local server_group_data = CrossServerWGData.Instance:GetServerMainServerDataBySeq(group_seq)

        if server_group_data ~= nil and server_group_data.server_id ~= nil and server_group_data.server_id ~= 0 then
            local list = CrossTreasureWGData.Instance:GetAllTreasureCfg()
            if list then
                for _, data in pairs(list) do
                    if data and data.seq then
                        CrossTreasureWGCtrl.Instance:SendTreasureOperaTypeInfo(server_group_data.server_id or 0, data.seq)
                    end
                end
            end

            CrossTreasureWGCtrl.Instance:SendTreasureBeastInfo(server_group_data.server_id or 0)
        end
    end
end

function CrossTreasureView:LoadCallBack()
    local bundle, asset = ResPath.GetRawImagesJPG("a3_kfcb_dt")
    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
    self.node_list.title_view_name.text.text = Language.CrossTreasure.LingzhuRuleTitle

    if not self.tab_root_list then
        self.tab_root_list = AsyncListView.New(TreasureTabRender, self.node_list.tab_root_list)
        self.tab_root_list:SetSelectCallBack(BindTool.Bind(self.TabRootCellCallBack,self))
    end

    if not self.treasure_server_list then
        self.treasure_server_list = {}

        for i = 1, CROSS_TREASURE_TYPE.MSG_MAX_TYPE_COUNT do
            local attr_obj = self.node_list.cross_treasure_server_list:FindObj(string.format("treasure_server_render_0%d", i))
            if attr_obj then
                local cell = TreasureServerRender.New(attr_obj)
                cell:SetIndex(i)
                cell:SetClickCallBack(BindTool.Bind(self.OnSelectTreasureServer, self))
                self.treasure_server_list[i] = cell
            end
        end
    end

    if not self.self_linzhu_list then
        self.self_linzhu_list = AsyncListView.New(TreasureSelfLingZhuCell, self.node_list.self_linzhu_list)
    end

    if not self.server_reward_list then
		self.server_reward_list = AsyncListView.New(ItemCell,self.node_list.server_reward_list)
		self.server_reward_list:SetStartZeroIndex(true)
	end

    if not self.beast_function_grid then
        self.beast_function_grid = BeastsBagGird.New()
        self.beast_function_grid:CreateCells({
										col = 4,  
										list_view = self.node_list["beast_function_grid"], 
										itemRender = BeastFunctionCell,
										change_cells_num = 1,
										assetBundle = "uis/view/cross_treasure_ui_prefab",
										assetName = "beast_function_cell",
		})
		self.beast_function_grid:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.reward_show_btn, BindTool.Bind2(self.BtnLingZhuRewardShow, self))
    XUI.AddClickEventListener(self.node_list.server_message_root, BindTool.Bind2(self.BtnCloseServerMessageShow, self))
    XUI.AddClickEventListener(self.node_list.server_find_btn_go_to, BindTool.Bind2(self.BtnFindSererGoto, self))
    XUI.AddClickEventListener(self.node_list.btn_function_rule, BindTool.Bind2(self.BtnFunctionRule, self))
end

function CrossTreasureView:ReleaseCallBack()
    if self.tab_root_list then
        self.tab_root_list:DeleteMe()
        self.tab_root_list = nil
    end

    if self.treasure_server_list and #self.treasure_server_list > 0 then
		for _, treasure_server_cell in ipairs(self.treasure_server_list) do
			treasure_server_cell:DeleteMe()
			treasure_server_cell = nil
		end

		self.treasure_server_list = nil
	end

    if self.self_linzhu_list then
        self.self_linzhu_list:DeleteMe()
        self.self_linzhu_list = nil
    end

    if self.server_reward_list then
        self.server_reward_list:DeleteMe()
        self.server_reward_list = nil
    end

	if self.beast_function_grid then
		self.beast_function_grid:DeleteMe()
		self.beast_function_grid = nil
	end

    self.select_tab_type = nil
end

-- 选中页签
function CrossTreasureView:TabRootCellCallBack(item, cell_index, is_default, is_click)
    if (not item) or (not item.data) or is_default then
        return
    end

    self.select_tab_type = cell_index
    self:FlushNowTabMessage()
end

-- 选中服务器
function CrossTreasureView:OnSelectTreasureServer(treasure_server_cell)
    if (not treasure_server_cell) or (not treasure_server_cell.data) then
        return
    end

    local data = treasure_server_cell.data
    local temp_index = treasure_server_cell:GetIndex()
    -- --拿到当前选中的格子下标
    -- if temp_index == self.select_server_index then
    --     CountryMapWGCtrl.Instance:SendCrossEnterOtherGs(data.plat_type, data.server_id, CROSS_ENTER_OTHER_GS_KEY.CROSS_SPY, function()
    --         print_error("进入成功")
    --     end)
    --     return
    -- end
    self.select_server_data = data
    self.node_list.server_message_root:CustomSetActive(true)
    self:FlushSelectServerMessage()
    self.select_server_index = temp_index
    self:FlushTreasureServerSelect()
end

function CrossTreasureView:OnFlush(param_t, index)
    if not self.select_tab_type then
        -- 这里可以选择跳转页签
        self.select_tab_type = 1
    end

    self.tab_root_list:SetDataList(Language.CrossTreasure.TabGrop)
    self.tab_root_list:JumpToIndex(self.select_tab_type, 3)
    -- for k, v in pairs(param_t) do
    --     if "all" == k then
    --     end
    -- end
end


function CrossTreasureView:FlushNowTabMessage()
    self.node_list.beast_function_root:CustomSetActive(self.select_tab_type == TAB_TYPE.BEAST_TAB)
    self.node_list.linzhu_function_root:CustomSetActive(self.select_tab_type == TAB_TYPE.TREASURE_TAB)
    self.node_list.left_tips_root:CustomSetActive(self.select_tab_type == TAB_TYPE.TREASURE_TAB)
    self.node_list.reward_show_btn:CustomSetActive(self.select_tab_type == TAB_TYPE.TREASURE_TAB)
    self:FlushTreasureServerList(self.select_tab_type)
    self:FlushTreasureServerSelect()

    if self.select_tab_type == TAB_TYPE.TREASURE_TAB then
        self:FlushUserTreasureList()
    else
        self:FlushTreasureBeastMessage()
    end
end

-- 刷新服务器灵珠信息列表
function CrossTreasureView:FlushTreasureServerList(show_index)
    local curr_server_id = RoleWGData.Instance:GetCurServerId()

    for group_seq, treasure_server_cell in ipairs(self.treasure_server_list) do
        local server_group_data = CrossServerWGData.Instance:GetServerMainServerDataBySeq(group_seq)
        treasure_server_cell:SetVisible(server_group_data ~= nil)

        if server_group_data ~= nil then
            if self.select_server_index == nil and server_group_data.server_id == curr_server_id then
                self.select_server_index = group_seq
            end

            treasure_server_cell:SetShowIndex(show_index)
            treasure_server_cell:SetData(server_group_data) 
        end
    end
end

-- -- 修改选中效果
function CrossTreasureView:FlushTreasureServerSelect()
    if not self.select_server_index then
        self.select_server_index = 1
    end

    for index, treasure_server_cell in ipairs(self.treasure_server_list) do
        treasure_server_cell:OnSelectChange(index == self.select_server_index)
    end
end

-- 刷新自身灵珠数据
function CrossTreasureView:FlushUserTreasureList()
    self.node_list.function_name.text.text = Language.CrossTreasure.LingzhuTitle
	local base_cfg = CrossTreasureWGData.Instance:GetBaseCfg()

	if not base_cfg then
		return
	end

    local max_treasure = base_cfg.max_treasure
	local list, num = CrossTreasureWGData.Instance:GetRoleAllTreasureList(max_treasure)
	self.now_used_num = num
	local str = string.format(Language.CrossTreasure.CanUseTimesTips, self.now_used_num, max_treasure)
	self.node_list.last_find_times.text.text = str
	self.self_linzhu_list:SetDataList(list)
end

-- 刷新幻兽数据
function CrossTreasureView:FlushTreasureBeastMessage()
    self.node_list.function_name.text.text = Language.CrossTreasure.BeastServerRuleTitle
    self.node_list.function_desc.text.text = Language.CrossTreasure.BeastServerRuleContent
    local base_cfg = CrossTreasureWGData.Instance:GetBaseCfg()
    if base_cfg then
        self.beast_function_grid:SetDataList(base_cfg.beast_rare_reward_item)

        local buy_times = CrossTreasureWGData.Instance:GetServerTreasureBeastGatherBuyTimes()
        local limit_times = base_cfg.beast_gather_times + buy_times
        local use_times = CrossTreasureWGData.Instance:GetServerTreasureBeastGatherTimes()
        local last_times = limit_times - use_times
        local color = last_times >= 1 and COLOR3B.GREEN or COLOR3B.RED
        find_str = string.format(Language.CrossTreasure.BeastServerCatchTimes, ToColorStr(string.format("%s/%s", last_times, limit_times), color))
        self.node_list.last_find_times.text.text = find_str
    end
end

-- 刷新选择的服务器数据
function CrossTreasureView:FlushSelectServerMessage()
    if not self.select_server_data then
        return
    end

    local base_cfg = CrossTreasureWGData.Instance:GetBaseCfg()
    local list_str = ""
    local title_str = ""
    local reaward_data = nil
    local desc = ""
    local find_str = ""

    if self.select_tab_type == TAB_TYPE.TREASURE_TAB then
        local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
        local lingzhu_data = lingzhu_list and lingzhu_list[1]
        local treasure_data = CrossTreasureWGData.Instance:GetTreasureInfoByServerId(self.select_server_data.server_id, lingzhu_data.seq)
        list_str = CrossTreasureWGData.Instance:GetServerTreasureCountList(treasure_data)
        title_str = Language.CrossTreasure.LingzhuServerTitle
        desc = Language.CrossTreasure.LingzhuServerDesc

        if base_cfg then
            reaward_data = base_cfg.treasure_rare_reward_item
            local max_gather_times = base_cfg.gather_times
            local steal_num = CrossTreasureWGData.Instance:GetDailyStealedNum()
            local last_num = max_gather_times - steal_num
            local color = last_num >= 1 and COLOR3B.GREEN or COLOR3B.RED
            find_str = string.format(Language.CrossTreasure.StealTimesTips, ToColorStr(last_num, color), max_gather_times)
        end
    else
        local beast_data = CrossTreasureWGData.Instance:GetServerTreasureBeastByServerId(self.select_server_data.server_id)
        local str = "" 
        if beast_data and beast_data.beast_num > 0 then
            str = ToColorStr(beast_data.beast_num, COLOR3B.C8)
        else
            str = ToColorStr(Language.Boss.NoFlush, COLOR3B.C10)
        end

        list_str = string.format(Language.CrossTreasure.BeastServerLast, str) 
        title_str = Language.CrossTreasure.BeastServerTitle
        desc = Language.CrossTreasure.BeastServerDesc
        
        if base_cfg then
            reaward_data = base_cfg.beast_rare_reward_item
            local buy_times = CrossTreasureWGData.Instance:GetServerTreasureBeastGatherBuyTimes()
            local limit_times = base_cfg.beast_gather_times + buy_times
            local use_times = CrossTreasureWGData.Instance:GetServerTreasureBeastGatherTimes()
            local last_times = limit_times - use_times
            local color = last_times >= 1 and COLOR3B.GREEN or COLOR3B.RED
            find_str = string.format(Language.CrossTreasure.BeastServerCatchTimes, ToColorStr(string.format("%s/%s", last_times, limit_times), color))
        end
    end

    self.server_reward_list:SetDataList(reaward_data)
    self.node_list.server_message_name.text.text = string.format("%d%s", self.select_server_data.server_id, Language.Login.Fu) 
    self.node_list.server_reward_title_txt.text.text = title_str
    self.node_list.server_desc_title_txt.text.text = desc
    self.node_list.server_desc_msg_txt.text.text = list_str
    self.node_list.server_find_msg_txt.text.text = find_str
end


--------------------------------------------------------------------------------
-- 点击查看奖励预览
function CrossTreasureView:BtnLingZhuRewardShow()
    CrossTreasureWGCtrl.Instance:OpenRewardPreview()
end

-- 点击关闭服务器信息
function CrossTreasureView:BtnCloseServerMessageShow()
    self.node_list.server_message_root:CustomSetActive(false)
end

-- 点击去到对方主城
function CrossTreasureView:BtnFindSererGoto()
    if not self.select_server_data then
        return
    end

    local data = self.select_server_data
    local cur_server_id = RoleWGData.Instance:GetCurServerId()
    local self_server_id = RoleWGData.Instance:GetOriginServerId()

    if data.server_id == cur_server_id then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips4)
        return
    end

    if data.server_id == self_server_id then
        CountryMapWGCtrl.Instance:SendReturnToOriginalServer(nil)
        self:Close()
    else
        if cur_server_id ~= self_server_id then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips3)
        else
            CountryMapWGCtrl.Instance:SendCrossEnterOtherGs(data.plat_type, data.server_id, CROSS_ENTER_OTHER_GS_KEY.CROSS_SPY, function()
                MainuiWGCtrl.Instance:FlushView(0, "CrossCrossTreasureTaskOpen")
            end)
            self:Close()
        end
    end
end

-- 点击规则
function CrossTreasureView:BtnFunctionRule()
    local title_str = ""
    local rule_content = ""
    local rule_tip = RuleTip.Instance

    if self.select_tab_type == TAB_TYPE.TREASURE_TAB then
        title_str = Language.CrossTreasure.LingzhuRuleTitle2
        rule_content = Language.CrossTreasure.LingzhuRuleContent2
    else
        title_str = Language.CrossTreasure.BeastServerRuleTitle
        rule_content = Language.CrossTreasure.BeastServerRuleContent
    end

    rule_tip:SetTitle(title_str)
    rule_tip:SetContent(rule_content, nil, nil, nil, true)
end
--------------------------------------------------------------------------------
----------页签类型数据-------------
TreasureTabRender = TreasureTabRender or BaseClass(BaseRender)
function TreasureTabRender:OnFlush()
    if self.data == nil then
        return
    end

    self.node_list.normal_txt.text.text = self.data
    self.node_list.select_txt.text.text = self.data

    local is_show_red = false
    self.node_list.red:CustomSetActive(is_show_red)
end

function TreasureTabRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
    self.node_list.normal:CustomSetActive(not is_select)
end

----------跨服藏宝数据-------------
TreasureServerRender = TreasureServerRender or BaseClass(BaseRender)
function TreasureServerRender:ReleaseCallBack()
	self.show_index = nil
end

function TreasureServerRender:SetShowIndex(var_show_index)
	self.show_index = var_show_index
end

function TreasureServerRender:OnFlush()
    if not self.data then
        return
    end

    local server_id = RoleWGData.Instance:GetOriginServerId()
    self.node_list.my_server_flag:CustomSetActive(server_id == self.data.server_id)
    self.node_list.other_server_flag:CustomSetActive(server_id ~= self.data.server_id)
    self.node_list.select:CustomSetActive(server_id == self.data.server_id)

    if server_id == self.data.server_id then
        self.node_list.my_server_name.text.text = Language.BiZuo.CSPro_BenFu
    else
        self.node_list.other_server_name.text.text = string.format("%d%s", self.data.server_id, Language.Login.Fu) 
    end

    self.node_list.beast_qp:CustomSetActive(false)
    self.node_list.treasure_server_qp:CustomSetActive(false)

    if self.show_index == TAB_TYPE.TREASURE_TAB then
        local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
        local lingzhu_data = lingzhu_list and lingzhu_list[1]
        local treasure_data = CrossTreasureWGData.Instance:GetTreasureInfoByServerId(self.data.server_id, lingzhu_data.seq)
        local _, max_level = CrossTreasureWGData.Instance:GetServerTreasureCountList(treasure_data)
        self.node_list.treasure_server_qp:CustomSetActive(treasure_data ~= nil and max_level ~= nil and max_level ~= 0)

        if treasure_data ~= nil and max_level ~= nil and max_level ~= 0 then
            self.node_list.treasure_best_icon.image:LoadSprite(ResPath.GetGameCrossTreasureImg(string.format("a3_kfcb_lz_type_%d_%d", treasure_data.seq, max_level)))
        end
    else
        local beast_data = CrossTreasureWGData.Instance:GetServerTreasureBeastByServerId(self.data.server_id)
        self.node_list.beast_qp:CustomSetActive(beast_data and beast_data.special_beast_num > 0)
    end
end

-- -- 选中改变
function TreasureServerRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

----------跨服藏宝数据-------------
TreasureSelfLingZhuCell = TreasureSelfLingZhuCell or BaseClass(BaseRender)
function TreasureSelfLingZhuCell:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.main_root, BindTool.Bind(self.BtnOnClickLingzhu, self))
end

function TreasureSelfLingZhuCell:ReleaseCallBack()
    self:RemoveStatusCountDown()
    self.show_can_get = nil
end

function TreasureSelfLingZhuCell:OnFlush()
    if not self.data then
        return
    end

    local offset_pos_x = self.index % 2 == 1 and 74 or -56
    self.node_list.main_root:SetLocalPosition(offset_pos_x, 0, 0)
    self.node_list.state_empty:CustomSetActive(self.data.reserve_seat ~= nil)
    self.node_list.linzhu_icon:CustomSetActive(not self.data.reserve_seat)
    self.node_list.status_txt:CustomSetActive(not self.data.reserve_seat)
    self.show_can_get = false

    -- 有数据
    if not self.data.reserve_seat then
        self:FlushNowSelfLingZhu()
    else
        self.node_list.render_desc.text.text = Language.CrossTreasure.LingzhuEmptyName
    end

    self.node_list.state_can_get:CustomSetActive(self.show_can_get)
end

function TreasureSelfLingZhuCell:FlushNowSelfLingZhu()
    if not self.data then
        return
    end

	local type_cfg = CrossTreasureWGData.Instance:GetAllTreasureLevelCfgBySeqLevel(self.data.seq, self.data.level)
    local lingzhu_list = CrossTreasureWGData.Instance:GetTreasureDetailCfgByType(CROSS_TREASURE_TYPE.TREASURE_ITEM_TYPE_LINGZHU)
	local lingzhu_data = lingzhu_list and lingzhu_list[1]

	if (not type_cfg) or (not lingzhu_data) then
		return
	end

	local name = type_cfg.name or ""
	local color = type_cfg.color or 1
	self.node_list.render_desc.text.text = ToColorStr(name, ITEM_COLOR[color])
    self.node_list.linzhu_icon.image:LoadSprite(ResPath.GetGameCrossTreasureImg(string.format("a3_kfcb_lz_type_%d_%d", self.data.seq, self.data.level)))

	local mature_time = lingzhu_data.mature_times or 1
	local start_time = self.data.time
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local remain_time = server_time - start_time

	if remain_time < mature_time then
		self:RemoveStatusCountDown()
		local last_time = mature_time - remain_time
		self.node_list.status_txt.text.text = TimeUtil.FormatSecondDHM9(last_time)

		local function timer_func(elapse_time, total_time)
			local show_time = total_time - elapse_time
			local time_str = string.format(Language.CrossTreasure.LingzhuMatureStatus, TimeUtil.FormatSecondDHM9(show_time))
			self.node_list.status_txt.text.text = time_str
		end

		local complete_fun = function()
            self.show_can_get = true
            self.node_list.status_txt.text.text = ToColorStr(Language.CrossTreasure.LingzhuMatureStatus2, COLOR3B.C8)
			self:RemoveStatusCountDown()
		end
		
		self.count_down = CountDown.Instance:AddCountDown(last_time, 1, timer_func, complete_fun)
	else
        self.show_can_get = true
		self.node_list.status_txt.text.text = ToColorStr(Language.CrossTreasure.LingzhuMatureStatus2, COLOR3B.C8)
	end
end


function TreasureSelfLingZhuCell:RemoveStatusCountDown()
	if self.count_down then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

-- 点击灵珠
function TreasureSelfLingZhuCell:BtnOnClickLingzhu()
    if self.data.reserve_seat ~= nil then   -- 这里切换主城, 打开左侧标签页
        CrossTreasureWGCtrl.Instance:CloseTreasureView()

        if not RoleWGData.Instance:CheckCurServerIsOrigin() then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips3)
        else
            if Scene.Instance:GetSceneId() ~= MapWGData.WORLDCFG[3] then
                GuajiWGCtrl.Instance:FlyToScene(MapWGData.WORLDCFG[3], true, 1)
            end
        end

        MainuiWGCtrl.Instance:FlushView(0, "CrossCrossTreasureTaskOpen")
    else 
        if self.show_can_get then
            CrossTreasureWGCtrl.Instance:CloseTreasureView()

            if not RoleWGData.Instance:CheckCurServerIsOrigin() then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips3)
            else
                GuajiWGCtrl.Instance:MoveToPos(MapWGData.WORLDCFG[3], self.data.pos_x, self.data.pos_y, 2)
            end
        else
            SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTreasure.LingzhuGatherErrorTips2)
        end

        MainuiWGCtrl.Instance:FlushView(0, "CrossCrossTreasureTaskOpen")
    end
end

---------------------------------背包列表------------------------------
BeastFunctionCell = BeastFunctionCell or BaseClass(BaseRender)
function BeastFunctionCell:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_pos)
    end
end

function BeastFunctionCell:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function BeastFunctionCell:OnFlush()
    if not self.data then
        return
    end

    self.item_cell:SetData(self.data)
end

