ActorTriggerBase = ActorTriggerBase or BaseClass()

function ActorTriggerBase:__init()
	self.delay = 0
	self.transform = nil
	self.enabled = true
	self.target = nil
	self.delay_timer_quest = nil
	self.delay_timer_num = 0
end

function ActorTriggerBase:__delete()
	self:Reset()
end

function ActorTriggerBase:Reset()
	if self.delay_timer_quest then
		for k, v in pairs(self.delay_timer_quest) do
			GlobalTimerQuest:CancelQuest(v)
			self.delay_timer_quest[k] = nil
		end
	end

	self.delay_timer_num = 0
	self.delay = 0
end

-- get/set
function ActorTriggerBase:Enalbed(value)
	if value == nil then
		return self.enabled
	end
	self.enabled = value
end

function ActorTriggerBase:OnAnimatorEvent(param, stateInfo, source, target, anim_name)
	if self.enabled then
		if self.delay <= 0 then
			self:OnEventTriggered(source, target, stateInfo)
		else
			self:DelayTrigger(source, target, stateInfo)
		end
	end
end

function ActorTriggerBase:OnEventTriggered(source, target, stateInfo)
	-- Transform source, Transform target, AnimatorStateInfo stateInfo
	-- override
end

local function DelayTriggerCallBack(cbdata)
	local self = cbdata[1]
	local source = cbdata[2]
	local target = cbdata[3]
	local stateInfo = cbdata[4]
	ActorWGCtrl.ReleaseCBData(cbdata)

	if self.delay_timer_quest then
		self.delay_timer_quest[self.delay_timer_num] = nil
	end

	if source == nil or IsNil(source.gameObject) or not source.gameObject.activeInHierarchy or
	(target and IsNil(target.gameObject)) then
		return
	end

	if stateInfo ~= nil and stateInfo.pos_table ~= nil then
		for k,v in pairs(stateInfo.pos_table) do
			local data = {play_pos = v}
			self:OnEventTriggered(source, target, data)
		end
	else
		self:OnEventTriggered(source, target, stateInfo)
	end

	self.delay_timer_num = self.delay_timer_num + 1
end

function ActorTriggerBase:DelayTrigger(source, target, stateInfo)
	self.delay_timer_quest = self.delay_timer_quest or {}
	if self.delay_timer_quest[self.delay_timer_num] then
		GlobalTimerQuest:CancelQuest(self.delay_timer_quest[self.delay_timer_num])
	end

	local cbdata = ActorWGCtrl.GetCBData()
	cbdata[1] = self
	cbdata[2] = source
	cbdata[3] = target
	cbdata[4] = stateInfo
	self.delay_timer_quest[self.delay_timer_num] = GlobalTimerQuest:AddDelayTimer(DelayTriggerCallBack, self.delay, cbdata)
end