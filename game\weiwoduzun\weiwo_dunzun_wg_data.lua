WeiWoDunZunWGData = WeiWoDunZunWGData or BaseClass()
WEIWODUZUN_TASK_TYPE = {
	ATTR = 0,
	WING = 1,
	VIP = 2,
	RECHARGE = 3,
	TIANSHEN = 4,
	WORDROBE = 5,
	CHARM_MALE = 6,
	CHARM_FEMALE = 7,
	WUHUN = 8,
};

function WeiWoDunZunWGData:__init()
	if WeiWoDunZunWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[WeiWoDunZunWGData] attempt to create singleton twice!")
		return
	end

	WeiWoDunZunWGData.Instance = self

	self:InitCfg()
	self.task_list = {}

	RemindManager.Instance:Register(RemindName.WeiWoDunZun, BindTool.Bind(self.ShowWeiWoDunZunRemind, self))
end

function WeiWoDunZunWGData:__delete()
	WeiWoDunZunWGData.Instance = nil

	RemindManager.Instance:UnRegister(RemindName.WeiWoDunZun)
end

function WeiWoDunZunWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("randact_weiwoduzun_cfg_auto")
	self.task_cfg = cfg.task
end

--全部任务数据
function WeiWoDunZunWGData:SetAllTaskInfo(protocol)
	self.task_list = protocol.task_list
end

-- 单个任务信息改变
function WeiWoDunZunWGData:SetSingleTaskInfo(protocol)
	local data = protocol.change_data
	for i, v in pairs(self.task_list ) do
		if v.task_id == data.task_id then
			self.task_list[v.task_id] = data
			return
		end
	end
end

--获取任务类型配置
function WeiWoDunZunWGData:GetTaskTypeCfg()
	local info = {}
	local role_sex = RoleWGData.Instance:GetRoleSex()
	--sex == GameEnum.MALE
	for i, v in pairs(self.task_cfg) do
		local flag = true
		--根据性别提出魅力榜
		if v.task_id == WEIWODUZUN_TASK_TYPE.CHARM_MALE  then
			flag = role_sex == GameEnum.MALE
		elseif  v.task_id == WEIWODUZUN_TASK_TYPE.CHARM_FEMALE then
			flag = role_sex == GameEnum.FEMALE
		end

		if flag then
			table.insert(info, v)
		end
	end

	if not IsEmptyTable(info) then
		table.sort(info, SortTools.KeyLowerSorters("sort_index", "task_id"))
	end

	return info
end

--获取任务数据通过id
function WeiWoDunZunWGData:GetTaskInfoById(task_id)
	return self.task_list[task_id]
end

--类型选择
function WeiWoDunZunWGData:GetSelectTypeIndex()
	local index = 1
	local cfg = self:GetTaskTypeCfg()
	for i, v in ipairs(cfg) do
		local task_info = self:GetTaskInfoById(v.task_id)
		if task_info and task_info.reward_flag == REWARD_STATE_TYPE.CAN_FETCH then
			index = i
			break
		end
	end

	return index
end

---红点--
function WeiWoDunZunWGData:ShowWeiWoDunZunRemind()
	if self:ShowTaskRemind() then
		return 1
	end

	return 0
end

function WeiWoDunZunWGData:ShowTaskRemind()
	local cfg = self:GetTaskTypeCfg()
	for i, v in ipairs(cfg) do
		local task_info = self:GetTaskInfoById(v.task_id)
		if task_info and task_info.reward_flag == REWARD_STATE_TYPE.CAN_FETCH then
			return true
		end
	end

	return false
end
