OperationTaskChainFbInfoView = OperationTaskChainFbInfoView or BaseClass(SafeBaseView)

function OperationTaskChainFbInfoView:__init()
	self.view_layer = UiLayer.MainUILow
	self.view_cache_time = 0
	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_info_view")

	self.is_show_close = false
end

function OperationTaskChainFbInfoView:ReleaseCallBack()
	self:ResetInfo()
	
	if not IsNil(self.obj) then
        ResMgr:Destroy(self.obj)
    end
    self.obj = nil

    if self.reward_list then
    	self.reward_list:DeleteMe()
    	self.reward_list = nil
    end

   	if CountDownManager.Instance:HasCountDown("task_chain_fb_act_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_fb_act_timer")
	end

	if self.do_tween ~= nil then
		self.do_tween:Kill()
		self.do_tween = nil
	end

	if self.scale_tween ~= nil then
		self.scale_tween:Kill()
		self.scale_tween = nil
	end

	if MainuiWGCtrl.Instance then
		MainuiWGCtrl.Instance:RemoveInitCallBack(MainUIInitCallBackKey.TaskChainInfoKey)
	end

	if self.text_scale_tween ~= nil then
		self.text_scale_tween:Kill()
		self.text_scale_tween = nil
	end
end

function OperationTaskChainFbInfoView:LoadCallBack()
	self.move_pos_x = 0
	self.move_pos_y = 0

	local pos = self.node_list.root_prog.transform.localPosition
	self.move_pos_x = pos.x
	self.move_pos_y = pos.y

	self.reward_list = AsyncListView.New(ItemCell, self.node_list.list_reward)

	MainuiWGCtrl.Instance:AddInitCallBack(MainUIInitCallBackKey.TaskChainInfoKey, BindTool.Bind(self.InitCallBack, self))
	self.node_list.btn_box.button:AddClickListener(BindTool.Bind1(self.OnClickBox, self))
end

function OperationTaskChainFbInfoView:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.obj = self.node_list["layout_view"].gameObject
	self.obj.transform:SetParent(parent.gameObject.transform)
	self.obj.transform.localPosition = Vector3(15, 0, 0)
	self.obj.transform.localScale = Vector3.one
end

function OperationTaskChainFbInfoView:OpenCallBack()
	if self.obj and self:IsLoadedIndex(0) then
        self.obj:SetActive(true)
    end
end

function OperationTaskChainFbInfoView:CloseCallBack()
   	if CountDownManager.Instance:HasCountDown("task_chain_fb_act_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_fb_act_timer")
	end
	self:ResetInfo()

	MainuiWGCtrl.Instance:SetFBNameState(false)
end

function OperationTaskChainFbInfoView:ResetInfo()
	self.is_show_close = false
end

function OperationTaskChainFbInfoView:OnClickBox()
	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainRewardView, 0, "all", {is_preview = true})
end

function OperationTaskChainFbInfoView:FlushViewInfo(level_change)
	CountDownManager.Instance:RemoveCountDown("task_chain_fb_act_timer")
	local data = OperationTaskChainWGData.Instance:GetShowFbInfo()
	if data == nil then
		return
	end

	MainuiWGCtrl.Instance:SetFBNameState(true, data.title)
	self.node_list.str_title.text.text = data.title
	self.node_list.str_info.text.text = data.is_standy and data.story_content or data.content
	self.node_list.prog_node.slider.value = data.pro_value
	self.node_list.str_prog_value.text.text = data.pro_str

	if data.level > 0 then
		local asset, bundle = ResPath.GetOperationTaskChainF2("score_level_" .. data.level)
		self.node_list.img_score.image:LoadSprite(asset, bundle)
		self.node_list.img_score.image:SetNativeSize()
	end

	if not data.is_max then
		local next_step = ""
		local lv_str = Language.OpertionAcitvity.TaskChain.LevelStr[data.level - 1]
		if lv_str then
			next_step = string.format(Language.OpertionAcitvity.TaskChain.NextPingjia, lv_str)
		end
		self.node_list.str_desc_2.text.text = string.format("%s\n%s", data.desc_2, next_step)
	else
		self.node_list.str_desc_2.text.text = data.desc_2
	end

	self.node_list.score_slider.image.fillAmount = data.score_slider_value

	self.node_list.str_desc_1.text.text = data.desc_1
	self.node_list.root_score:SetActive(not data.is_standy)
	self.node_list.root_prog:SetActive(not data.is_standy)

	local is_fb = OperationTaskChainWGData.Instance:GetIsTaskChainTaskScene()
	local show_reward = data.is_standy or not data.is_join
	-- local show_join = data.is_join and not data.is_standy
	-- self.node_list.str_is_join:SetActive(show_join)
	self.node_list.time_item:SetActive(data.count_down_time > 0)
	self.continue_time = data.continue_time

	self.node_list.root_reward:SetActive(false)
	if not data.is_join and not is_fb and not IsEmptyTable(data.data_list) then
		self.node_list.root_reward:SetActive(true)
		self:FlushTaskChainShowReward(data.data_list)
	end

	if data.count_down_time > 0 then
		CountDownManager.Instance:AddCountDown("task_chain_fb_act_timer", BindTool.Bind(self.FlushTaskChainActTime, self, data.time_str, data.state), nil, nil, data.count_down_time, 1)
	end

	local is_show_panel_2 = not data.is_standy and is_fb
	self.node_list.root_mingwang:SetActive(is_show_panel_2)
	self.node_list.str_time:SetActive(not is_show_panel_2)

	if is_fb then
		local cur_value = 0
		local all_info = OperationTaskChainWGData.Instance:GetTaskChainInfo()
		if all_info ~= nil then
			local grade_cfg = OperationTaskChainWGData.Instance:GetGradeInfoCfg(all_info.grade, all_info.day_index)
			if grade_cfg ~= nil then
				local mingwang_cfg = OperationTaskChainWGData.Instance:GetTaskLevelReward(grade_cfg.level_reward, data.task_idx + 1, data.level)
				if mingwang_cfg ~= nil then
					cur_value = mingwang_cfg.mingwang
				end
			end
		end

		self.node_list.text_cur_value.text.text = cur_value

		local cfg =  OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
		if cfg ~= nil then
			local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.item_id)
			if item_cfg ~= nil then
				local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
				self.node_list.img_icon.image:LoadSprite(bundle, asset)
			end
		end

		if all_info ~= nil then
			cur_value = cur_value + all_info.mingwang
		end

		local next_value, is_max, interval = OperationTaskChainWGData.Instance:GetNextMingWangReward(cur_value)
		local str = is_max and Language.OpertionAcitvity.TaskChain.IsFinsh or next_value

		if level_change then
			self:AddMingWang()
		end

		local slider_value = is_max and 1 or 0
		if next_value ~= nil and next_value ~= 0 and not is_max then
			slider_value = cur_value / next_value
		end

		self.node_list.libao_slider.image.fillAmount = 0

		self:FlushBow(str or "", slider_value)
	end
end

function OperationTaskChainFbInfoView:FlushBow(next_str, slider_value)
	self.node_list.text_next_value.text.text = next_str
	self.node_list.img_slider.image.fillAmount = slider_value
end

function OperationTaskChainFbInfoView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushViewInfo(v.level_change)
		elseif k == "move" then
			self:FlushViewInfo(v.level_change)
			if v.node ~= nil then
				self:ShowSubmission(v.node, v.bundle, v.asset)
			end
		elseif k == "flush_bow" then
			if v ~= nil and v.next_str ~= nil and v.slider_value ~= nil then
				self:FlushBow(v.next_str, v.slider_value)
			end
		end
	end
end

function OperationTaskChainFbInfoView:AddMingWang()
	if self.text_scale_tween ~= nil then
		return
	end

	if self.node_list.text_cur_value == nil then
		return
	end

 	self.text_scale_tween = self.node_list.text_cur_value.transform:DOScale(Vector3(2, 2, 2), 1)
	self.text_scale_tween:SetEase(DG.Tweening.Ease.OutCubic)
	self.text_scale_tween:OnComplete(function ()
		if self.text_scale_tween ~= nil then
			self.text_scale_tween:Kill()
		end

		if self.node_list.text_cur_value ~= nil then
		 	self.text_scale_tween = self.node_list.text_cur_value.transform:DOScale(Vector3(1, 1, 1), 1)
			self.text_scale_tween:SetEase(DG.Tweening.Ease.OutCubic)
			self.text_scale_tween:OnComplete(function ()
				if self.text_scale_tween ~= nil then
					self.text_scale_tween:Kill()
				end

				self.text_scale_tween = nil	
			end)
		end
	end)
end

function OperationTaskChainFbInfoView:FlushTaskChainShowReward(data_list)
	data_list = data_list or {}
	self.reward_list:SetDataList(data_list)
end

function OperationTaskChainFbInfoView:FlushTaskChainActTime(time_str, state, elapse_time, total_time)
	if self.node_list.str_time ~= nil then
		local value = math.floor(total_time - elapse_time)
		local time = TimeUtil.FormatSecondDHM2(value)
		local str = time_str or ""
		self.node_list.str_time.text.text = str .. ToColorStr(time, COLOR3B.GREEN)

		if self.continue_time > 0 then
			self.node_list.time_slider.image.fillAmount = (self.continue_time - value) / self.continue_time
		else
			self.node_list.time_slider.image.fillAmount = 0
		end
		self.node_list.time_label.text.text = time

		if state ~= nil and state == OPERATION_TASK_CHAIN_ACT_STATUS.OPEN and not self.is_show_close and value <= 11 and OperationTaskChainWGData.Instance:GetIsTaskChainTaskScene() then
			self.is_show_close = true
			UiInstanceMgr.Instance:DoFBStartDown(value + TimeWGCtrl.Instance:GetServerTime(), nil, nil, nil, FB_START_DOWN_END_TYPE.TASK_CLOSE)
		end
	end
end

function OperationTaskChainFbInfoView:ShowSubmission(node, bundle, asset)
	if self.move_tween ~= nil then
		self.move_tween:Kill()
		self.move_tween = nil
	end

	if self.scale_tween ~= nil then
		self.scale_tween:Kill()
		self.scale_tween = nil
	end

	if IsNil(UICamera) then
		return
	end

	if IsNil(node) then
		return
	end

	if self.move_pos_x == nil or self.move_pos_y == nil then
		return
	end

	if self.node_list.layout_view == nil then
		return
	end

	if self.node_list.img_submission == nil then
		return
	end

	local obj = self.node_list.layout_view.gameObject
	if IsNil(obj) then
		return
	end

	local move_obj = self.node_list.img_submission.gameObject
	if IsNil(move_obj) then
		return
	end

	local move_trans = move_obj.transform
	local old_pos = move_trans.localPosition

	local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
    local parent_rect= self.node_list.layout_view.gameObject.transform:GetComponent(typeof(UnityEngine.RectTransform))
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))
    local target_x = local_position_tbl.x
    local target_y = local_position_tbl.y

	self.node_list.img_submission.image:LoadSprite(bundle, asset, function ()
		self.node_list.img_submission.image:SetNativeSize()
	end)
    move_trans.localPosition = Vector3(target_x, target_y, old_pos.z)
    move_trans.localScale = Vector3(1, 1, 1)
    self.node_list.img_submission:SetActive(true)

 	self.do_tween = move_trans:DOLocalMove(Vector3(self.move_pos_x, self.move_pos_y, old_pos.z), 0.5)
	self.do_tween:SetEase(DG.Tweening.Ease.OutCubic)
	self.do_tween:OnComplete(function ()
		if self.node_list.img_submission ~= nil then
			self.node_list.img_submission:SetActive(false)
		end
	end)

	self.scale_tween = move_trans:DOScale(Vector3(0,0,0), 0.5)
	self.scale_tween:SetEase(DG.Tweening.Ease.OutBack)  
end
