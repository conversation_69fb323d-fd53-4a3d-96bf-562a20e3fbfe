require("game/story/storydef")
require("game/story/story")
require("game/story/xinshou_storys")
require("game/story/base_cg")
require("game/story/story_view")
require("game/story/story_entrance_view")
require("game/story/cgmanager")
require("game/story/robert_manager")
require("game/story/robertmgr")

StoryWGCtrl = StoryWGCtrl or  BaseClass(BaseWGCtrl)

function StoryWGCtrl:__init()
	if StoryWGCtrl.Instance ~= nil then
		ErrorLog("[StoryWGCtrl] attempt to create singleton twice!")
		return
	end
	StoryWGCtrl.Instance = self

	self.cg_mgr = CgManager.New()
	self.robert_mgr = RobertManager.New()
	-- 客户端自己生成机器人（目前用于假装玩家跑主线，先合过来，后续整合到RobertManager中）
	self.robert_client_mgr = RobertMgr.New()
	--self.entrance_view = StoryEntranceView.New(GuideModuleName.StoryEntranceView)

	self:RegisterProtocol(CSNewPlayerFbReqWudi) 											-- 无敌buff请求
end

function StoryWGCtrl:__delete()
	--self.entrance_view:DeleteMe()
	self.robert_client_mgr:DeleteMe()
	self.robert_mgr:DeleteMe()
	self.cg_mgr:DeleteMe()

	StoryWGCtrl.Instance = nil
end

-- 请求无敌buff
function StoryWGCtrl:SendCSNewPlayerFbReqWudi(status)
	local protocol = ProtocolPool.Instance:GetProtocol(CSNewPlayerFbReqWudi)
	protocol.status = status
	protocol:EncodeAndSend()
end

function StoryWGCtrl:DirectEnter(target_type)
	FuBenWGCtrl.Instance:SendEnterFBReq(GameEnum.FB_CHECK_TYPE.FBCT_GUIDE, self:GetFbGuideTypeByTargetType(target_type))
end

-- function StoryWGCtrl:OpenEntranceView(fuben_type, task_id)
-- 	if SceneType.Common ~= Scene.Instance:GetSceneType() then
-- 		return
-- 	end

-- 	if self:GetTaskIdByFbGuideType(GUIDE_FB_TYPE.HUSONG) == task_id then
-- 		self:OpenHusongEntranceView(fuben_type, task_id)
-- 	else
-- 		self:OpenNormalEntranceView(fuben_type, task_id)
-- 	end
-- end

function StoryWGCtrl:OpenHusongEntranceView(fuben_type, task_id)
	 -- 挂机那有个update一直调自动执行任务，导致有可以关闭后马上又会打开, 这里主
	local yunbiao_view = ViewManager.Instance:GetView(GuideModuleName.YunbiaoView)
	if nil == yunbiao_view then
		return
	end

	if Status.NowTime - yunbiao_view:GetCloseTimeStamp() < 3 then
		return
	end

	yunbiao_view:SetHusongGuideEntrance(BindTool.Bind(self.DoEnter, self, fuben_type, task_id), 5)
	FunctionGuide.Instance:TriggerGuideByGuideName("husong_entrance")

	Scene.Instance:GetMainRole():StopMove()
	GuajiWGCtrl.Instance:ClearAllOperate()
end

function StoryWGCtrl:OpenNormalEntranceView(fuben_type, task_id)
	 -- 挂机那有个update一直调自动执行任务，导致有可以关闭后马上又会打开, 这里主动加个限制
	-- if Status.NowTime - self.entrance_view:GetCloseTimeStamp() < 3 then
	-- 	return
	-- end

	-- self.entrance_view:SetEnterCallback(BindTool.Bind(self.DoEnter, self, fuben_type, task_id))
	-- self.entrance_view:SetGuideFbType(self:GetFbGuideTypeByTaskId(task_id))
	-- self.entrance_view:Open()
	Scene.Instance:GetMainRole():StopMove()
	GuajiWGCtrl.Instance:ClearAllOperate()
end

function StoryWGCtrl:OpenGuideEntranceView(fuben_type, task_id)
	self:DoEnter(fuben_type, task_id)
end

function StoryWGCtrl:DoEnter(fuben_type, task_id)
	FuBenWGCtrl.Instance:SendEnterFBReq(fuben_type, self:GetFbGuideTypeByTaskId(task_id))
end

function StoryWGCtrl:CloseEntranceView()
	--self.entrance_view:Close()
	ViewManager.Instance:Close(GuideModuleName.YunbiaoView)
end

function StoryWGCtrl:GetTaskIdByFbGuideType(fb_guide_type)
	local guide_list = ConfigManager.Instance:GetAutoConfig("fb_guide_auto").guide_list
	if nil == guide_list then
		return 0
	end

	return guide_list[fb_guide_type] and guide_list[fb_guide_type].task_id or 0
end

function StoryWGCtrl:GetFbGuideTypeByTaskId(task_id)
	local guide_list = ConfigManager.Instance:GetAutoConfig("fb_guide_auto").guide_list
	if nil == guide_list then
		return 0
	end

	for _, v in pairs(guide_list) do
		if v.task_id == task_id then
			return v.type
		end
	end

	return 0
end

function StoryWGCtrl:GetFbGuideTypeByTargetType(target_type)
	local guide_list = ConfigManager.Instance:GetAutoConfig("fb_guide_auto").guide_list
	if nil == guide_list then
		return 0
	end

	for _, v in pairs(guide_list) do
		if v.target_type == target_type then
			return v.type
		end
	end
	return 0
end

function StoryWGCtrl:GetFunOpenFbTypeByTaskId(task_id)
	local fb_scene_cfg = ConfigManager.Instance:GetAutoConfig("funopenfbconfig_auto").fb_scene_cfg
	if nil == fb_scene_cfg then
		return 0
	end

	for _, v in pairs(fb_scene_cfg) do
		if v.task_id == task_id then
			return v.fb_type
		end
	end
	return 0
end

function StoryWGCtrl:GetFbCfg(task_id)
	local cfg = ConfigManager.Instance:GetAutoConfig("funopenfbconfig_auto").fb_scene_cfg
	if nil == cfg then
		return {}
	end

	for _, v in pairs(cfg) do
		if v.task_id == task_id then
			return v
		end
	end

	return {}
end