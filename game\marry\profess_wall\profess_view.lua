ProfessView = ProfessView or BaseClass(SafeBaseView)

function ProfessView:__init()
	self.default_index = TabIndex.profess_wall_xunyuan
	self.cur_page = 1
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "ProfessVerticalTabbar")
    self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_profess_wall_bg")
	self:AddViewResource(TabIndex.profess_wall_xunyuan, "uis/view/marry_ui_prefab", "layout_marry_xunyuan")
    self:AddViewResource(TabIndex.profess_wall_all, "uis/view/marry_ui_prefab", "layout_profess_wall_all_view")

    -- self:AddViewResource(TabIndex.profess_wall_to, "uis/view/marry_ui_prefab", "layout_profess_wall_tomy_view")
    -- self:AddViewResource(TabIndex.profess_wall_from, "uis/view/marry_ui_prefab", "layout_profess_wall_my_view")
    -- self:AddViewResource(TabIndex.profess_wall_rank_male, "uis/view/marry_ui_prefab", "layout_profess_male_rank_wall_view")
    -- self:AddViewResource(TabIndex.profess_wall_rank_female, "uis/view/marry_ui_prefab", "layout_profess_female_rank_wall_view")
end

function ProfessView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.activity_change_event then
    	ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_event)
		self.activity_change_event = nil
	end

	self:ReleaseAllConfessionViewCallBack()
	-- self:ReleaseMyConfessionViewCallBack()
	-- self:ReleaseToMyConfessionViewCallBack()
	self:ReleaseXunYuanCallBack()
end

function ProfessView:LoadCallBack()
    if not self.tabbar then
	    self.tabbar = Tabbar.New(self.node_list, "ProfessVerticalTabbar")
	    self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.SpecialVerBar, self))
		self.tabbar:SetVerTabbarCellName("ProfessVerticalTabbarCell")
	    self.tabbar:Init(Language.ProfessWall.TabGrop1, nil, "uis/view/marry_ui_prefab", nil)
	    self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
    end

	if not self.activity_change_event then
    	self.activity_change_event = BindTool.Bind(self.FlushTabBar, self)
    	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_event)
    end
end

function ProfessView:CloseCallBack()
	if self.is_xunyuan_task then
		self.is_xunyuan_task = nil
		if self.is_auto_task then
			self.is_auto_task = nil
			TaskGuide.Instance:CanAutoAllTask(true)
		end
	end
end

function ProfessView:SpecialVerBar()
	self:FlushTabBar(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
end

function ProfessView:FlushTabBar(activity_type)
	if self:IsOpen() and activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK then
		if self.tabbar then
			local state = ActivityWGData.Instance:GetActivityIsOpen(activity_type)
			self.tabbar:SetVerToggleVisble(TabIndex.profess_wall_rank_male, state)
			self.tabbar:SetVerToggleVisble(TabIndex.profess_wall_rank_female, state)
		end
	end
end

function ProfessView:LoadIndexCallBack(index)
	if index == TabIndex.profess_wall_all then
		self:LoadAllConfessionViewCallBack()
	elseif index == TabIndex.profess_wall_xunyuan then
		self:LoadXunYuanCallBack()
	-- elseif index == TabIndex.profess_wall_to then
	-- 	self:LoadToMyConfessionViewCallBack()
	-- elseif index == TabIndex.profess_wall_from then
	-- 	self:LoadMyConfessionViewCallBack()
	-- elseif index == TabIndex.profess_wall_rank_male then
	-- 	self:LoadProfessMaleRankViewCallBack()
	-- elseif index == TabIndex.profess_wall_rank_female then
	-- 	self:LoadProfessFeMaleRankViewCallBack()
	end
end

function ProfessView:ShowIndexCallBack(index)
	if index == TabIndex.profess_wall_all then
		ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_GLOBAL_ALL)
		self:ShowAllConfessionViewCallBack()
	elseif index == TabIndex.profess_wall_xunyuan then
		self:ShowXunYuanIndexCallBack()
	-- elseif index == TabIndex.profess_wall_to then
	-- 	ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_PASSIVE)
	-- 	self:ShowToMyConfessionViewCallBack()
	-- elseif index == TabIndex.profess_wall_from then
	-- 	ServerActivityWGCtrl.Instance:CSGetProfessInfo(TIANYAN_MIYU_ENUM.PROFESS_REQ_TYPE_ACTIVE)
	-- 	self:ShowMyConfessionViewCallBack()
	-- elseif index == TabIndex.profess_wall_rank_male then
	-- 	ProfessWallWGCtrl.Instance:SendGetKaifuActivityInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
	-- 	RankWGCtrl.Instance:SendGetPersonRankListReq(PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_MALE)
	-- 	self:ShowProfessMaleRankViewCallBack()
	-- elseif index == TabIndex.profess_wall_rank_female then
	-- 	ProfessWallWGCtrl.Instance:SendGetKaifuActivityInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_PROFESS_RANK)
	-- 	RankWGCtrl.Instance:SendGetPersonRankListReq(PROFESS_RANK_TYPE.PERSON_RANK_TYPE_RA_PROFESS_FEMALE)
	-- 	self:ShowProfessFeMaleRankViewCallBack()
	end
end

function ProfessView:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.profess_wall_all then
				self:OnFlushAllConfessionViewCallBack()
			elseif index == TabIndex.profess_wall_xunyuan then
				self:OnFlushXunYuan()
			-- elseif index == TabIndex.profess_wall_to then
			-- 	self:OnFlushToMyConfessionViewCallBack()
			-- elseif index == TabIndex.profess_wall_from then
			-- 	self:OnFlushMyConfessionViewCallBack()
			-- elseif index == TabIndex.profess_wall_rank_male then
			-- 	self:OnFlushProfessMaleRankView()
			-- elseif index == TabIndex.profess_wall_rank_female then
			-- 	self:OnFlushProfessFeMaleRankView()
			end
		elseif k == "jump_page" then
			self:OnFlushXunYuan(v.page)
		elseif k == "task_open" then
			self.is_xunyuan_task = true
			self.is_auto_task = TaskGuide.Instance.can_auto_all_task
			TaskGuide.Instance:CanAutoAllTask(false)

			self:OnClickFaBu()
		end
	end
end