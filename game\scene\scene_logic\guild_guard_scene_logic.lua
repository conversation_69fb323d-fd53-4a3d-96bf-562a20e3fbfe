-- require("game/dungeon/dungeon_scene/dungeon_guildguard")
-- require("game/dungeon/dungeon_scene/guild_xiongshou_scene")

GuildGuardSceneLogic = GuildGuardSceneLogic or BaseClass(CommonFbLogic)

function GuildGuardSceneLogic:__init()
	-- self.guard_view = GuildGuardView.New()
	-- self.xiongshou_view = GuildXiongShouSceneView.New()
end

function GuildGuardSceneLogic:__delete()
	-- self.guard_view:DeleteMe()
	-- self.guard_view = nil

	-- self.xiongshou_view:DeleteMe()
	-- self.xiongshou_view = nil
end

function GuildGuardSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)	
	-- self:UpdateGuardViewShowState()

	-- GuildWGCtrl.Instance:Close()
	-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIONGSHOU, 0)
end

function GuildGuardSceneLogic:Out()
	CommonFbLogic.Out(self)
	-- self.guard_view:Close()
	-- self.xiongshou_view:Close()

	-- FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelCJ)

	-- if GuildWGData.Instance:IsXiongShouOpen() then
	-- 	local callback = function()
	-- 		FunOpen.Instance:OpenViewNameByCfg("guild#guild_activity#uin=guild_act_item,uip=7")
	-- 	end
	-- 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.XIONGSHOU, 1, callback)
	-- end
end

function GuildGuardSceneLogic:UpdateGuardViewShowState()
	--仙盟酒会
	-- if not self.guard_view:IsOpen() and not MainuiWGCtrl.Instance:GetMenuIsShow() and GuildWGData.Instance:IsGuildPartyOpen() then
	-- 	self.guard_view:Open()
	-- 	self.guard_view:Flush()
	-- end

	-- if not GuildWGData.Instance:IsGuildPartyOpen() then
	-- 	self.guard_view:Close()
	-- end

	-- --仙盟凶兽
	-- if not self.xiongshou_view:IsOpen() and not MainuiWGCtrl.Instance:GetMenuIsShow() and GuildWGData.Instance:IsXiongShouOpen() then
	-- 	self.xiongshou_view:Open()
	-- 	self.xiongshou_view:Flush()
	-- end

	-- if not GuildWGData.Instance:IsXiongShouOpen() then
	-- 	self.xiongshou_view:Close()
	-- end
end

function GuildGuardSceneLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function GuildGuardSceneLogic:OpenFbSceneCd()

end

function GuildGuardSceneLogic:FlushGuildGuard()
	-- self.guard_view:StarCountDown()
end

function GuildGuardSceneLogic:FlushGuildXiongShou()
	-- self.xiongshou_view:StarCountDown()
end

function GuildGuardSceneLogic:OnClickHeadHandler(is_show)
	CommonActivityLogic.OnClickHeadHandler(self, is_show)
	-- 仙盟仙盟酒会
	-- self.guard_view:ShowAction(is_show)
	-- if not GuildWGData.Instance:IsGuildPartyOpen() then
	-- 	self.guard_view:SetVisible(false)
	-- end

	-- --仙盟凶兽
	-- self.xiongshou_view:ShowAction(is_show)
	-- if not GuildWGData.Instance:IsXiongShouOpen() then
	-- 	self.xiongshou_view:SetVisible(false)
	-- end
end

--"选择目标"
--主动根据情况决定是否行走过去
function GuildGuardSceneLogic:SelectTargetObj(target_obj, is_auto)
	-- if target_obj == nil then
	-- 	return
	-- end
	-- if target_obj:GetType() == SceneObjType.GatherObj then
	-- 	if self.guard_view:IsGatherDetection(target_obj.vo.gather_id) then
	-- 		self:MoveToObj(target_obj, is_auto)
	-- 	end
	-- else
	-- 	BaseSceneLogic.SelectTargetObj(self, target_obj, is_auto)
	-- end
end

function GuildGuardSceneLogic:GetTaskView()
	-- if GuildWGData.Instance:IsGuildPartyOpen() then
	-- 	return self.guard_view
	-- elseif GuildWGData.Instance:IsXiongShouOpen() then
	-- 	return self.xiongshou_view
	-- else
	-- 	return self.guard_view
	-- end
end
