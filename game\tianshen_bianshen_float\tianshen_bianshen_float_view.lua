TianShenBianShenFloat = TianShenBianShenFloat or BaseClass(SafeBaseView)

function TianShenBianShenFloat:__init()
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(0)

    self.view_layer = UiLayer.SkillFloatWord
    
    self:AddViewResource(0, "uis/view/tianshen_bianshen_prefab", "layout_tianshen_bianshen")
end


function TianShenBianShenFloat:CloseCallBack()
    self.curr_tween_status = nil
end


function TianShenBianShenFloat:ReleaseCallBack()
    self:CleanDelayTimer()
end

function TianShenBianShenFloat:SetDataAndOpen(appe_image_id)
    self.appe_image_id = appe_image_id

    if not self:IsOpen() then
        self:Open()
    else
		self:CleanDelayTimer()
		self:Flush()
    end
end

function TianShenBianShenFloat:OnFlush()
	self:FlushImage()
end



function TianShenBianShenFloat:FlushImage()
	if nil == self.appe_image_id then
		return
	end

	-- if self.node_list["bianshen_root"] then
		
	-- 	local bundle, asset = ResPath.GetF2RawImagesPNG("tianshen_img_bg_"..self.appe_image_id)
	-- 	self.node_list["bianshen_img_bg"].raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list["bianshen_img_bg"].raw_image:SetNativeSize()
	-- 	end)

	-- 	local bundle2, asset2 = ResPath.GetF2RawImagesPNG("tianshen_img_head_"..self.appe_image_id)
	-- 	self.node_list["bianshen_img_head"].raw_image:LoadSprite(bundle2, asset2, function()
	-- 		self.node_list["bianshen_img_head"].raw_image:SetNativeSize()
	-- 	end)

	-- 	local bundle3, asset3 = ResPath.GetF2RawImagesPNG("tianshen_img_eff_"..self.appe_image_id)
	-- 	self.node_list["bianshen_img_eff"].raw_image:LoadSprite(bundle3, asset3, function()
	-- 		self.node_list["bianshen_img_eff"].raw_image:SetNativeSize()
	-- 	end)

	-- 	local bundle4, asset4 = ResPath.GetF2RawImagesPNG("tianshen_img_name_"..self.appe_image_id)
	-- 	self.node_list["bianshen_img_name"].raw_image:LoadSprite(bundle4, asset4, function()
	-- 		self.node_list["bianshen_img_name"].raw_image:SetNativeSize()
	-- 	end)


	-- 	self.node_list["bianshen_root"]:SetActive(true)

	-- end

	if self.node_list["effect_root"] then
		local effect_bundle, effect_asset = ResPath.GetEffect("UI_tianshen_bianshen_"..self.appe_image_id)
		self.node_list["effect_root"]:ChangeAsset(effect_bundle, effect_asset)
		self.node_list["effect_root"]:SetActive(true)
	end

	
	self.hied_tianshen_root_timer = GlobalTimerQuest:AddDelayTimer(function ()
		self:Close()
	end, 5)
end

function TianShenBianShenFloat:CleanDelayTimer()
	if self.hied_tianshen_root_timer then
		GlobalTimerQuest:CancelQuest(self.hied_tianshen_root_timer)
		self.hied_tianshen_root_timer = nil
	end
end



