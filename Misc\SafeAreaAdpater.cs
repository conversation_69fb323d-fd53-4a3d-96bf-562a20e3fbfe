﻿using System;
using System.Collections.Generic;
using UnityEngine;
using UnityEngine.UI;
using Nirvana;

[RequireComponent(typeof(RectTransform))]
public class SafeAreaAdpater : MonoBehaviour
{
    public enum Mode
    {
        NORMAL = 0,
        INPHONE_XL,
        INPHONE_XR,
    }

    private RectTransform rectTransform;


    private static Vector2 referenceResolution;          // 参考分辨率
    private static Rect referenceSafeArea = new Rect();  // 参考分辨率下的当全区
    private static Rect screenSafeArea = new Rect();
    private static float lastCheckSafeAreaTime = 0;
    private static Rect safe_area;
    private static int resolution_width;
    private static int resolution_height;
    private static bool hasSafeArea = false;
    private static Action hasSafeAeraCallBack;

    private void Awake()
    {
        this.rectTransform = this.GetComponent<RectTransform>();
        this.rectTransform.anchorMin = new Vector2(0, 0);
        this.rectTransform.anchorMax = new Vector2(1, 1);

        // 获得参考分辨率
        if (referenceResolution == Vector2.zero)
        {
            CanvasScaler canvas = this.GetComponentInParent<CanvasScaler>();
            if (null != canvas)
            {
                referenceResolution = canvas.referenceResolution;
            }
        }

        this.AdjustLayout();
    }

    private void Update()
    {
        TryGetSafeAreaInfo();

        if (this.CheckSafeAreaChange())
        {
            this.AdjustLayout();

            hasSafeArea = true;
            if (null != hasSafeAeraCallBack)
            {
                hasSafeAeraCallBack();
            }
        }
    }

    private bool CheckSafeAreaChange()
    {
        if (this.rectTransform.offsetMin.x < 0)
        {
            return false;
        }

        if (resolution_width <= 0 || resolution_height <= 0)
        {
            return false;
        }

        if (safe_area.width == Screen.width && safe_area.height == Screen.height)
        {
            return false;
        }

        float factor_x = referenceResolution.x / resolution_width;
        float factor_y = referenceResolution.y / resolution_height;

        referenceSafeArea.x = Mathf.Floor(safe_area.x * factor_x);
        referenceSafeArea.y = Mathf.Floor(safe_area.y * factor_y);

        referenceSafeArea.width = Mathf.Floor(safe_area.width * factor_x);
        referenceSafeArea.height = Mathf.Floor(safe_area.height * factor_y);

        // 如果计算出来的值与当前已经设的值相同，则返回false，防止RectTransfrom一直变化
        if (this.rectTransform.offsetMin.x == referenceSafeArea.x && this.rectTransform.offsetMin.y == referenceSafeArea.y
            && this.rectTransform.offsetMax.x == referenceSafeArea.x + referenceSafeArea.width - referenceResolution.x
            && this.rectTransform.offsetMax.y == referenceSafeArea.y + referenceSafeArea.height - referenceResolution.y)
        {
            return false;
        }

        return true;
    }

    private static void TryGetSafeAreaInfo()
    {
        if (0 == lastCheckSafeAreaTime || Time.time >= lastCheckSafeAreaTime + 1)
        {
            lastCheckSafeAreaTime = Time.time;

#if UNITY_EDITOR
            Mode mode = (Mode)UnityEngine.PlayerPrefs.GetInt("safe_area_mode");
            if (Mode.INPHONE_XL == mode)  // 如iphonex留海在左边
            {
                safe_area = new Rect(88, 0, Screen.width - 88, Screen.height);
            }
            else if (Mode.INPHONE_XR == mode)  // 如iphonex留海在右边
            {
                safe_area = new Rect(0, 0, Screen.width - 88, Screen.height);
            }
            else
            {
                safe_area = new Rect(0, 0, Screen.width, Screen.height);
            }

            resolution_width = Screen.width;
            resolution_height = Screen.height;
#else
            DeviceTool.GetScreenSafeAreaFix(out safe_area, out resolution_width, out resolution_height);
            resolution_width = 0 != resolution_width ? resolution_width : Screen.width;
            resolution_height = 0 != resolution_height ? resolution_height : Screen.height;
#endif
        }
    }

    public void AdjustLayout()
    {
        if (0 == referenceSafeArea.width
            || 0 == referenceSafeArea.height)
        {
            return;
        }

        this.rectTransform.offsetMin = new Vector2(referenceSafeArea.x, referenceSafeArea.y);
        this.rectTransform.offsetMax = new Vector2(referenceSafeArea.x + referenceSafeArea.width - referenceResolution.x,
                                                    referenceSafeArea.y + referenceSafeArea.height - referenceResolution.y);

    }

    public static void SetSafeAreaChangeCallBack(Action action)
    {
        hasSafeAeraCallBack = action;
        if (hasSafeArea && null != hasSafeAeraCallBack)
        {
            hasSafeAeraCallBack();
        }
    }

    public static SafeAreaAdpater Bind(GameObject go)
    {
        var safeAreaAdpater = go.GetComponent<SafeAreaAdpater>() ?? go.AddComponent<SafeAreaAdpater>();
        return safeAreaAdpater;
    }
}
