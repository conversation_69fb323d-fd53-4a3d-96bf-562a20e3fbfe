TipCommonUseView = TipCommonUseView or BaseClass(SafeBaseView)

function TipCommonUseView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 440)})
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_common_buy_item_tips")
end

function TipCommonUseView:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["item_cell"])
	end

	XUI.AddClickEventListener(self.node_list["btn_ok"], BindTool.Bind(self.OnClickOKBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_minus"], BindTool.Bind(self.OnClickMinus, self))
	XUI.AddClickEventListener(self.node_list["btn_plus"], BindTool.Bind(self.OnClickPlus, self))
	self.node_list.num_slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self))
end

function TipCommonUseView:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TipCommonUseView:ShowIndexCallBack()
	self.max_num = 1
	self.node_list.num_slider.slider.value = 1
end

function TipCommonUseView:OnFlush()
	local data = self.data
	if not data then
		return
	end

	self.item_cell:SetData(data.item_data)
	self.node_list.name.text.text = ItemWGData.Instance:GetItemName(data.item_data.item_id, nil, true)
	self.node_list.btn_ok_text.text.text = data.btn_ok_str or Language.Tip.TipDefaultOKBtnStr

	self.max_num = data.max_num
	if self.max_num == 1 then
		self.node_list.num_slider.slider.minValue = 0
		self.node_list.num_slider.slider.interactable = false
	else
		self.node_list.num_slider.slider.minValue = 1
		self.node_list.num_slider.slider.interactable = true
	end

	self.node_list.num_slider.slider.maxValue = data.max_num
	self.node_list.num_slider.slider.value = data.max_num
	self:FlushConsume(self.amount)

	self.node_list.need_num_root:SetActive(data.need_huobi_root == nil and false or data.need_huobi_root)
	if data.need_huobi_root then
		self:FlushHuoBiRoot(data)
	end
end

function TipCommonUseView:SetDataAndOpen(data)
	self.data = data
	self.add_error = data.add_error
	self:Open()
end

function TipCommonUseView:OnClickOKBtn()
	local data = self.data
	if not data then
		return
	end

	local total_price = self.amount * data.price
	if self.data.is_sell or data.has_num >= total_price then
		if data.ok_func then
			data.ok_func(self.amount)
		end
	else
		if data.huobi_name then
			TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Tip.TipNotEnoughMoneyStr, data.huobi_name))
		end
	end

	self:Close()
end

function TipCommonUseView:FlushConsume(value)
	if not self.data then
		return
	end

	self.node_list.need_num.text.text = self.data.price * value
	self.node_list.num.text.text = value
end

function TipCommonUseView:FlushHuoBiRoot(data)
	self.node_list.has_num.text.text = data.has_num
	self.node_list.need_num.text.text = data.price * self.amount
	self.node_list.need_stuff_txt.text.text = data.need_stuff_str
	self.node_list.has_stuff_txt.text.text = data.has_stuff_str
	self.node_list.need_stuff_txt:SetActive(data.is_show_icon == nil and true or not data.is_show_icon )
	self.node_list.has_stuff_txt:SetActive(data.is_show_icon == nil and true or not data.is_show_icon )
	self.node_list.need_stuff_icon_root:SetActive(data.is_show_icon == nil and false or data.is_show_icon )
	self.node_list.has_stuff_icon_root:SetActive(data.is_show_icon == nil and false or data.is_show_icon )

	if data.is_show_icon == true and data.icon_id then
		local asset, bundle = ResPath.GetItem(data.icon_id)
		self.node_list.need_stuff_icon.image:LoadSprite(asset, bundle)
		self.node_list.has_stuff_icon.image:LoadSprite(asset, bundle)
	end
end

function TipCommonUseView:OnSliderValueChange(value)
	self.amount = value
	self:FlushConsume(value)
end

function TipCommonUseView:OnClickMinus()
	self.amount = math.max(self.amount - 1, 1)
	self:FlushConsume(self.amount)
	self.node_list.num_slider.slider.value = self.amount
end

function TipCommonUseView:OnClickPlus()
	self.amount = math.min(self.amount + 1, self.max_num)
	self:FlushConsume(self.amount)
	self.node_list.num_slider.slider.value = self.amount

	if self.max_num == self.amount then
		if self.add_error ~= nil then
			TipWGCtrl.Instance:ShowSystemMsg(string.format(self.add_error))
		end
	end
end