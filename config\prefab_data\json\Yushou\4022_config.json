{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false, "hurtEffectFreeDelay": 0.0, "QualityCtrlList": []}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.5, "triggerFreeDelay": 0.0, "effectGoName": "eff_4022yu<PERSON><PERSON>_attack1", "effectAsset": {"BundleName": "effects/prefab/model/yushou/4022/eff_4022yushou_attack1_prefab", "AssetName": "eff_4022yu<PERSON><PERSON>_attack1", "AssetGUID": "b58832a5df6ec184f906f64c92125981", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": false, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "rest/begin", "triggerDelay": 0.5, "triggerFreeDelay": 0.0, "effectGoName": "eff_4022<PERSON><PERSON><PERSON>_rest", "effectAsset": {"BundleName": "effects/prefab/model/yushou/4022/eff_4022yushou_rest_prefab", "AssetName": "eff_4022<PERSON><PERSON><PERSON>_rest", "AssetGUID": "2863ea063a85f93438ef021c7287cadf", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": false, "isUseCustomTransform": true, "offsetPosX": 0.0, "offsetPosY": 1.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": false}], "halts": [], "sounds": [], "cameraShakes": [], "cameraFOVs": [], "sceneFades": [], "footsteps": []}, "actorBlinker": {"blinkFadeIn": 0.0, "blinkFadeHold": 0.0, "blinkFadeOut": 0.0}, "TimeLineList": []}