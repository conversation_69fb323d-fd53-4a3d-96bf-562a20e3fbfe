require("game/bf_cap_rank/bf_cap_rank_view")
require("game/bf_cap_rank/bf_cap_rank_wg_data")

BFCapRankWGCtrl = BFCapRankWGCtrl or BaseClass(BaseWGCtrl)
function BFCapRankWGCtrl:__init()
	if BFCapRankWGCtrl.Instance then
		ErrorLog("[BFCapRankWGCtrl] Attemp to create a singleton twice !")
	end

	BFCapRankWGCtrl.Instance = self
    self.data = BFCapRankWGData.New()
	self.view = BFCapRankView.New(GuideModuleName.BFCapRankView)
	self:RegisterProtocol(CSCapabilityRank)
    self:RegisterProtocol(SCCapabilityRankInfo, 'OnSCCapabilityRankInfo')
end

function BFCapRankWGCtrl:__delete()
	BFCapRankWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
end

function BFCapRankWGCtrl:SendCapRankReq(opera_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCapabilityRank)
	protocol.opera_type = opera_type
	protocol:EncodeAndSend()
end

function BFCapRankWGCtrl:OnSCCapabilityRankInfo(protocol)
	self.data:SetSwornRankSort(protocol)

	if self.view:IsOpen() then
		self.view:Flush()
	end
end
