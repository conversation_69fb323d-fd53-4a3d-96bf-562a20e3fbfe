﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


namespace HUDProgramme
{
    public class HUDTexture
    {
        public string name;
        public Texture tex;

        public HUDTexture(string atlas_name, Texture tex)
        {
            this.name = atlas_name;
            this.tex = tex;
        }
    }

    public class HUDAssetsManager : MonoBehaviour
    {
        [Header("伤害飘字数据")]
        public HUDObjectData HUDAnimData;
        [Header("其他预制配置")]
        public HUDObjectData HUDPrefabsData;
        // 工具生成的图集对应配置
        public TextAsset HUDTextAsset;
        // 工具生成的图集对应配置
        public TextAsset HUDTextAssetBin;

        //所有的图集数据
        private Dictionary<string, HUDTexture> AllHUDTextureData = new Dictionary<string, HUDTexture>();

        public TextAsset get_HUDTextAsset
        {
            get { return this.HUDTextAsset; }
        }

        public TextAsset get_HUDTextAssetBin
        {
            get { return this.HUDTextAssetBin; }
        }

        private static HUDAssetsManager instance;
        public static HUDAssetsManager Instance
        {
            get
            {
                return instance;
            }
        }

        private void Awake()
        {
            instance = this;
            HUDMesh.OnEnterGame();  // 启动游戏场景后调用
        }

        private void OnDestroy()
        {
            HUDMesh.OnLeaveGame();
        }

        public void AddAtlaTexture(string atlas_name, Texture tex)
        {
            if (!AllHUDTextureData.ContainsKey(atlas_name))
            {
                AllHUDTextureData.Add(atlas_name, new HUDTexture(atlas_name, tex));
            }
        }

        public void InitAltasCfg()
        {
            AtlasManager.Instance.InitAltasCfg();
        }

        // 获取材质贴图
        public Texture GetAtlasTexture(string atlas_name)
        {
            HUDTexture hud_texture_data = null;
            if (AllHUDTextureData.TryGetValue(atlas_name, out hud_texture_data))
            {
                return hud_texture_data.tex;
            }

            return null;
        }

        //获取对应的动画配置
        public HUDAnim GetAnim(string anim_name)
        {
            if (HUDAnimData != null && HUDAnimData.list.Count > 0)
            {
                for (int i = 0; i < HUDAnimData.list.Count; i++)
                {
                    if (HUDAnimData.list[i].name == anim_name)
                    {
                        HUDAnim hud_anim = HUDAnimData.list[i].obj.GetComponent<HUDAnim>();
                        return hud_anim;
                    }
                }
            }

            return null;
        }

        //获取对应配置信息
        public T GetPrefabs<T>(string prefab_name)
        {
            T prefab_type = default(T);

            if (HUDPrefabsData != null && HUDPrefabsData.list.Count > 0)
            {
                for (int i = 0; i < HUDPrefabsData.list.Count; i++)
                {
                    if (HUDPrefabsData.list[i].name == prefab_name)
                    {
                        prefab_type = HUDPrefabsData.list[i].obj.GetComponent<T>();
                        return prefab_type;
                    }
                }
            }

            return prefab_type;
        }

        public int GetHUDAnimLength()
        {
            if (HUDAnimData != null && HUDAnimData.list.Count > 0)
                return HUDAnimData.list.Count;

            return 0;
        }

        public List<HUDObject> GetHUDAnimList()
        {
            if (HUDAnimData != null && HUDAnimData.list.Count > 0)
                return HUDAnimData.list;

            return null;
        }


        /// 提供给Lua端接口
        public void SetHUDMeshCamera(Camera camera)
        {
            HUDMesh.SetHUDMainCamera(camera);
        }

        public void SetHUDSceneCamera(Camera camera)
        {
            HUDMesh.SetHUDSceneCamera(camera);
        }

        //展示伤害
        public void ShowHurt(Transform target, long hurt_number, string hurt_type, string headStr, string headStr2, bool bShowAdd, bool bShowSub, bool bHaveNumber)
        {
            HUDNumberRender.Instance.AddHudNumber(target, hurt_type, hurt_number, headStr, headStr2, bShowAdd, bShowSub, bHaveNumber);
        }
    }
}