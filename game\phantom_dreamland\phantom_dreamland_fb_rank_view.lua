--排行榜
PhantomDreamlandFBRankView = PhantomDreamlandFBRankView or BaseClass(SafeBaseView)

function PhantomDreamlandFBRankView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", { sizeDelta = Vector2(816, 580) })
	self:AddViewResource(0, "uis/view/phantom_dreamland_ui_prefab", "layout_phantom_dreamland_fb_rank")
end

function PhantomDreamlandFBRankView:OpenCallBack()

end

function PhantomDreamlandFBRankView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.rank

	if not self.layout_rank_list then
		self.layout_rank_list = AsyncListView.New(PDRankItem, self.node_list.layout_rank_list)
	end

	if not self.layout_rank_reward_list then
		self.layout_rank_reward_list = AsyncListView.New(PDRankRewardItem, self.node_list.layout_rank_reward_list)
	end
end

function PhantomDreamlandFBRankView:CloseCallBack()

end

function PhantomDreamlandFBRankView:ReleaseCallBack()
	if self.layout_rank_list then
		self.layout_rank_list:DeleteMe()
		self.layout_rank_list = nil
	end

	if self.layout_rank_reward_list then
		self.layout_rank_reward_list:DeleteMe()
		self.layout_rank_reward_list = nil
	end
end

function PhantomDreamlandFBRankView:OnFlush(param_t)
	self:FlushRankList()
	self.node_list["txt_rank_tips"].text.text = Language.PhantomDreamland.RankTip
end

function PhantomDreamlandFBRankView:FlushRankList()
	local rank_list = PhantomDreamlandWGData.Instance:GetRankList()
	self.layout_rank_list:SetDataList(rank_list)

	local rank_reward_list = PhantomDreamlandWGData.Instance:GetRankRewardCfgList()
	self.layout_rank_reward_list:SetDataList(rank_reward_list)

	local self_rank = PhantomDreamlandWGData.Instance:GetSelfRank()
	local rank_str = self_rank > 0 and string.format(Language.PhantomDreamland.Rank, self_rank) or Language.PhantomDreamland.NoRank
	self.node_list["txt_my_rank"].text.text = string.format(Language.PhantomDreamland.MyRankDesc, rank_str)
end

-------------------------------------PDRankItem---------------------------------
PDRankItem = PDRankItem or BaseClass(BaseRender)

function PDRankItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local rank = self.data.rank == 0 and self.index or self.data.rank
	local bundle, asset
	local is_top_three = rank <= 3

	if is_top_three then
		bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. rank)
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank))
	else
		bundle, asset = ResPath.GetCommonImages("a3_ty_bg1_5")
	end

	self.node_list.img_bg.image:LoadSprite(bundle, asset)
	self.node_list.img_rank:CustomSetActive(is_top_three)
	self.node_list.rank_text.text.text = is_top_three and "" or rank
	if self.data.name == nil or self.data.name == "" then
		self.node_list.rank_name.text.text = Language.RoleCharmRank.noinfoname
	else
		self.node_list.rank_name.text.text = self.data.name
	end
	self.node_list.rank_harm_value.text.text = self.data.harm_value
end

-------------------------------------PDRankRewardItem---------------------------------
PDRankRewardItem = PDRankRewardItem or BaseClass(BaseRender)

function PDRankRewardItem:LoadCallBack()
	if not self.rank_reward_list then
		self.rank_reward_list = AsyncListView.New(ItemCell, self.node_list["rank_reward_list"])
		self.rank_reward_list:SetStartZeroIndex(true)
	end
end

function PDRankRewardItem:ReleaseCallBack()
	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end
end

function PDRankRewardItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local bundle, asset
	local is_top_three = self.index <= 3
	if is_top_three then
		bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. self.index)
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.index))
	else
		bundle, asset = ResPath.GetCommonImages("a3_ty_bg1_5")
	end
	self.node_list.img_bg.image:LoadSprite(bundle, asset)
	self.node_list.img_rank:CustomSetActive(is_top_three)
	self.node_list.rank_text.text.text = is_top_three and "" or (self.data.min_rank .. "-" .. self.data.max_rank)

	self.rank_reward_list:SetDataList(self.data.reward_item)
end
