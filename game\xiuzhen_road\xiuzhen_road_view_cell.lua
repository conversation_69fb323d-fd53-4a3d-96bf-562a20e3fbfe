-------------------------------- LiJiCellItemRender start --------------------------------
LiJiCellItemRender = LiJiCellItemRender or BaseClass(BaseRender)
function LiJiCellItemRender:__init()

end

function LiJiCellItemRender:__delete()
	if self.tween_shake then
		self.tween_shake:Kill(true)
		self.tween_shake = nil
	end
	
	if self.show_item_cell then
		self.show_item_cell:DeleteMe()
		self.show_item_cell = nil
	end
end

function LiJiCellItemRender:LoadCallBack()
	if not self.show_item_cell then
		self.show_item_cell = ItemCell.New(self.node_list["item_root"])
		self.show_item_cell:SetIsShowTips(false)
	end
	XUI.AddClickEventListener(self.node_list["leiji_click_btn"], BindTool.Bind(self.OnClickOpenProgReward, self))
end

function LiJiCellItemRender:OnFlush()
	if nil == self.data then
		return
	end

	local pos = Split(self.data.pos, "|")
	local cur_total_progress = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiRewardProgress()
	local reward_flag = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuLeiJiRewardFlag(self.data.progress)
	local index = self.index
	-- local img_index = 0
	-- local img_red_index = 2
	-- if self.index == 1 or self.index == 2 or self.index == 3 then
	-- 	img_index = 1
	-- elseif self.index == 4 or self.index == 5 or self.index == 6 then
	-- 	img_index = 2
	-- elseif self.index == 7 or self.index == 8 then
	-- 	img_index = 3
	-- 	img_red_index = 3
	-- else
	-- 	img_index = 3
	-- end

	self.node_list.gouzi:SetActive(false)
	-- local bundle, asset = ResPath.GetXiuZhenRoadImg("a3_xxym_baoxiang" .. img_index)
	-- self.node_list.item_cell.image:LoadSprite(bundle, asset, function()
	-- 	self.node_list.item_cell.image:SetNativeSize()
	-- end)

	-- local t_str = string.format("%s%s%s", "a3_xxym_baoxiang", img_red_index, "xz")
	-- local red_bundle, red_asset = ResPath.GetXiuZhenRoadImg(t_str)
	-- self.node_list.item_cell_red.image:LoadSprite(red_bundle, red_asset, function()
	-- 	self.node_list.item_cell_red.image:SetNativeSize()
	-- end)

	if index == 7 and self.data.progress > 0 then
		reward_flag = XiuZhenRoadWGData.Instance:GetRedFlag(self.data.progress)
	end

	if self.data.reward_item then
		self.show_item_cell:SetData(self.data.reward_item[0])
	end

	local yi_lq = self.data.condition <= cur_total_progress and 1 == reward_flag
	-- XUI.SetButtonEnabled(self.node_list.item_cell, (not yi_lq))


	self.node_list.gouzi:SetActive(yi_lq)
	self.node_list.image_bg_red:SetActive(cur_total_progress >= self.data.condition and 0 == reward_flag)
	-- self.node_list.item_cell_red:SetActive(cur_total_progress >= self.data.condition and 0 == reward_flag)
	self.node_list.red_point:SetActive(cur_total_progress >= self.data.condition and 0 == reward_flag)
	self.node_list.item_cell_num.text.text = self.data.condition

	if cur_total_progress >= self.data.condition and 0 == reward_flag then
		if not self.tween_shake then
			-- self.tween_shake = self:AniRotate()
			self.tween_shake = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.item_cell.transform, self.tween_shake)
		end
	else
		if self.tween_shake then
			self.tween_shake:Kill(true)
			self.tween_shake = nil
		end
		self.node_list.item_cell.transform.rotation = Quaternion.Euler(0, 0, 0)
	end
end

function LiJiCellItemRender:OnClickOpenProgReward()
	XiuZhenRoadWGCtrl.Instance:OpenXiuZhenRewardTips()
end

function LiJiCellItemRender:AniRotate()
	local ani_tween = function(trans)
		local sequence = DG.Tweening.DOTween.Sequence()
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 0), 1, DG.Tweening.RotateMode.Fast)) --恢复0
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, -30), 0.1, DG.Tweening.RotateMode.Fast)) --左50
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 25), 0.2, DG.Tweening.RotateMode.Fast)) --右40
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, -20), 0.2, DG.Tweening.RotateMode.Fast)) --左30
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 15), 0.2, DG.Tweening.RotateMode.Fast)) --右20
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, -10), 0.2, DG.Tweening.RotateMode.Fast)) --左10
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 5), 0.2, DG.Tweening.RotateMode.Fast)) --右5
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 0), 0.1, DG.Tweening.RotateMode.Fast)) --恢复 0
		sequence:SetEase(DG.Tweening.Ease.Linear)
		return sequence
	end
	ani_tween(self.node_list.item_cell.transform):SetLoops(-1)
end
-------------------------------- LiJiCellItemRender  end  --------------------------------

-------------------------------- XiuZhenBtnItemRender start --------------------------------
XiuZhenBtnItemRender = XiuZhenBtnItemRender or BaseClass(BaseRender)
function XiuZhenBtnItemRender:__init()

end

function XiuZhenBtnItemRender:__delete()

end

function XiuZhenBtnItemRender:LoadCallBack()
	self.node_list.other_btn.button:AddClickListener(BindTool.Bind(self.OnClickOtherHint, self))
end

function XiuZhenBtnItemRender:OnFlush()
	if nil == self.data then
		return
	end
	--未开启时候拦截显示
	local cur_day = XiuZhenRoadWGData.Instance:GetActivityOpenDay()
	local is_show_btn = false
	if cur_day < self.data.cfg.open_day then
		if 1 == self.data.cfg.open_day - cur_day and 1 == self.data.cfg.is_preview then
			is_show_btn = false
		else
			is_show_btn = true
		end
	end

	self.node_list.other_btn:SetActive(is_show_btn)
	--local is_show = XiuZhenRoadWGData.Instance:GetCurDayTaskIsFinish(self.data.cfg.open_day)
	--local is_unlock = not is_show_btn and not is_show
	self.node_list.unlock_image:SetActive(is_show_btn)
	-- self.node_list.unlock_text:SetActive(is_show_btn)
	-- self.node_list.unlock_text.text.text = Language.XiuZhenRoad.NoOpenTips
	--self.node_list.finish_num_text:SetActive(is_unlock)
	--self.node_list.hl_num_text:SetActive(is_unlock)
	self.node_list.text_name.text.text = self.data.cfg.button_name
	self.node_list.text_name2.text.text = self.data.cfg.button_name
	self:IsShowFinish()
end

function XiuZhenBtnItemRender:OnSelectChange(is_on)
	if nil ~= self.node_list.img_select then
		self.node_list.img_select:SetActive(is_on)
	end
end

function XiuZhenBtnItemRender:OnClickOtherHint()
	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.XiuZhenRoad.NoOpenHint, self.data.cfg.open_day))
end

function XiuZhenBtnItemRender:IsShowFinish()
	-- local is_show = XiuZhenRoadWGData.Instance:GetCurDayTaskIsFinish(self.data.cfg.open_day)
	-- if self.node_list.finish_task then
	-- 	self.node_list.finish_task:SetActive(is_show)
	-- end

	--local complse_num, max_num = XiuZhenRoadWGData.Instance:GetCurNeedInfoProgress(self.data.cfg.open_day)
	--local show_str = string.format("%s/%s", complse_num, max_num)
	--self.node_list.finish_num_text.text.text = show_str
	--self.node_list.hl_num_text.text.text = show_str

	--红点展示
	local red_show = XiuZhenRoadWGData.Instance:GetSelectBtnRedShow(self.data.cfg.open_day)
	if self.node_list.btn_xiuzhen_red then
		self.node_list.btn_xiuzhen_red:SetActive(red_show)
	end
end
-------------------------------- XiuZhenBtnItemRender  end  --------------------------------

-------------------------------- XiuZhenTaskItemRender start --------------------------------
XiuZhenTaskItemRender = XiuZhenTaskItemRender or BaseClass(BaseRender)
function XiuZhenTaskItemRender:__init()
end

function XiuZhenTaskItemRender:__delete()

end

function XiuZhenTaskItemRender:ReleaseCallBack()
	if self.save_last_idx and self.save_last_idx ~= self.index then
		if CountDownManager.Instance:HasCountDown("xiuzhen_task_online_cd_timer" .. self.save_last_idx) then
			CountDownManager.Instance:RemoveCountDown("xiuzhen_task_online_cd_timer" .. self.save_last_idx)
		end
	end
	if CountDownManager.Instance:HasCountDown("xiuzhen_task_online_cd_timer" .. self.index) then
		CountDownManager.Instance:RemoveCountDown("xiuzhen_task_online_cd_timer" .. self.index)
	end

	if self.item_reward_list then
		self.item_reward_list:DeleteMe()
		self.item_reward_list = nil
	end
end

function XiuZhenTaskItemRender:LoadCallBack()
	self.node_list.btn_task_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickLingQu, self))
	if not self.item_reward_list then
		self.item_reward_list = AsyncListView.New(XiuZhenTaskRewardItemRender, self.node_list.reward_list)
		-- self.item_reward_list:SetStartZeroIndex(true)
	end
end

function XiuZhenTaskItemRender:OnFlush()
	if nil == self.data then
		return
	end
	local cur_day = XiuZhenRoadWGData.Instance:GetActivityOpenDay()
	self.node_list.image_redmind:SetActive(cur_day >= self.data.old_info.open_day and
		self.data.task_states == XIUZHENZHILU_TASK_STATES.KLQ)
	self.node_list.redmind_effect:SetActive(cur_day >= self.data.old_info.open_day and
		self.data.task_states == XIUZHENZHILU_TASK_STATES.KLQ)
	self.node_list.img_ylq:SetActive(self.data.task_states == XIUZHENZHILU_TASK_STATES.YLQ)

	self.node_list.btn_task_lingqu:SetActive(self.data.task_states ~= XIUZHENZHILU_TASK_STATES.YLQ)
	local btn_name = self.data.task_states == XIUZHENZHILU_TASK_STATES.WDC and Language.XiuZhenRoad.Btn_QianQang or
	Language.XiuZhenRoad.Btn_LingQu
	self.node_list.lbl_btn_text.text.text = btn_name
	local now_num = math.min(self.data.task_progress, self.data.old_info.param_1)
	local color = now_num >= self.data.old_info.param_1 and "#CBFBFD" or "#ca3838"
	local str_progress = ToColorStr(now_num, color) .. ToColorStr("/" .. self.data.old_info.param_1, "#CBFBFD")

	if self.data.old_info.task_type == 80 then
		str_progress = ToColorStr(math.floor(now_num / 60), color) .. ToColorStr("/" .. math.floor(self.data.old_info.param_1 / 60), "#CBFBFD")
	elseif self.data.old_info.task_type == 90 then
		local is_finish = self.data.task_progress > 0 and self.data.task_progress <= self.data.old_info.param_1
		color = is_finish and "#CBFBFD" or "#ca3838"
		str_progress = ToColorStr(is_finish and 1 or 0, color) .. ToColorStr("/" .. 1, "#CBFBFD")
	end

	--self.node_list.task_name.text.text = self.data.old_info.task_name .. "  " ..  string.format(Language.XiuZhenRoad.TaskProGress,str_progress)
	local taks_slider = string.format(Language.XiuZhenRoad.TaskProGress, str_progress)
	self.node_list.task_progress_text.text.text = taks_slider
	self.node_list.task_desc.text.text = self.data.old_info.task_desc -- .. " " .. taks_slider

	local tb_temp = {}
	if not IsEmptyTable(self.data.old_info.reward_show) then
		for key, value in pairs(self.data.old_info.reward_show) do
			table.insert(tb_temp, {reward_item = value, task_states = self.data.task_states})
		end
	end

	if self.item_reward_list then
		self.item_reward_list:SetDataList(tb_temp)
	end

	if cur_day < self.data.old_info.open_day then
		self.node_list.lbl_btn_text.text.text = Language.XiuZhenRoad.NoCanTask
	end
	self.node_list.btn_image_1:SetActive(self.data.task_states == XIUZHENZHILU_TASK_STATES.KLQ and
	cur_day >= self.data.old_info.open_day)

	self.node_list.btn_image_2:SetActive(not (cur_day < self.data.old_info.open_day))
	self.node_list.btn_image_3:SetActive(cur_day < self.data.old_info.open_day)

	if self.node_list.btn_image_1.gameObject.activeSelf == true then
		self.node_list.btn_image_2:SetActive(false)
	end
	self.node_list.btn_task_lingqu.button.interactable = (cur_day >= self.data.old_info.open_day)


	self:FlushTaskTimer()
end

function XiuZhenTaskItemRender:FlushTaskTimer()
	if self.data.old_info.task_type ~= 80 then
		if self.node_list["task_cd_txt"].gameObject.activeSelf then
			self.node_list["task_cd_txt"]:SetActive(false)
		end
		return
	elseif not self.node_list["task_cd_txt"].gameObject.activeSelf then
		self.node_list["task_cd_txt"]:SetActive(true)
	end

	if self.save_last_idx ~= self.index then
		if self.save_last_idx ~= nil then
			if CountDownManager.Instance:HasCountDown("xiuzhen_task_online_cd_timer" .. self.save_last_idx) then
				CountDownManager.Instance:RemoveCountDown("xiuzhen_task_online_cd_timer" .. self.save_last_idx)
			end
		end
		self.save_last_idx = self.index
	end

	local save_idx = XiuZhenRoadWGData.Instance:GetTaskTimerIdx()
	if self.index == save_idx and self.data.old_info.task_type == 80 then
		local login_stamp = XiuZhenRoadWGData.Instance:GetXiuZhenRoadLoginTimeStamp() 
		local now_time = TimeWGCtrl.Instance:GetServerTime()
		local diff_time = 0
		if login_stamp then
			diff_time = 0 -- math.floor(now_time - login_stamp) -- 系统未开启的时候,后端也算登录的时间戳，所以这个偏移时间暂时不要了
		end

		local remain_sec = (self.data.old_info.param_1 - self.data.task_progress) - diff_time
		local time_str = TimeUtil.FormatSecond2HMS(math.ceil(remain_sec))
		if remain_sec > 0 then
			self.node_list["task_cd_txt"].text.text = time_str
			CountDownManager.Instance:AddCountDown("xiuzhen_task_online_cd_timer" .. self.index, 
			BindTool.Bind1(self.UpdataTaskRewardCD, self),
			BindTool.Bind1(self.EndTaskRewardCD, self),
			nil, remain_sec, 1)
		else
			self.node_list["task_cd_txt"].text.text = ''
		end
	else
		self.node_list["task_cd_txt"].text.text = ''
	end
end

function XiuZhenTaskItemRender:UpdataTaskRewardCD(elapse_time, total_time)
	if not self.node_list then
		return
	end

	local time = math.ceil(total_time - elapse_time)
	local time_str = TimeUtil.FormatSecond2HMS(time)
	self.node_list["task_cd_txt"].text.text = time_str
end

function XiuZhenTaskItemRender:EndTaskRewardCD()
	if self.node_list then
		self.node_list["task_cd_txt"].text.text = ''
	end
end

function XiuZhenTaskItemRender:PalyItemAnimator(item_index)
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.ExpAditionCellTween
	UITween.FakeHideShow(self.node_list.tween_root)

	ReDelayCall(self, function()
		if self.node_list and self.node_list.tween_root then
			UITween.RotateAlphaShow(GuideModuleName.XiuZhenRoadView, self.node_list.tween_root, tween_info)
		end
	end, tween_info.NextDoDelay * wait_index, "XiuZhenCell_" .. wait_index)
end

function XiuZhenTaskItemRender:OnClickLingQu()
	if self.data.task_states == XIUZHENZHILU_TASK_STATES.KLQ then
		XiuZhenRoadWGCtrl.Instance:SendXiuZhenZhiLuReq(XIUZHENZHILU_OPEN_TYPE.XIUZHENZHILU_OPEN_TYPE_TASK_REWARD,
			self.data.old_info.seq)
		return
	end
	if "" ~= self.data.old_info.open_panel then
		local item_id
		-- 神兽跳转特殊处理, 此类型只当前用到，写死
		if self.data.old_info.task_type == 56 then
			item_id = self.data.old_info.param_2
		end
		local open_name = self.data.old_info.open_panel
		local open_limit_type = self.data.old_info.open_limit_type
		if open_limit_type == 1 then
			local open_list = Split(self.data.old_info.open_panel, "|")
			local sex = RoleWGData.Instance:GetRoleSex()
			local def_index = sex == GameEnum.MALE and 1 or 2
			open_name = open_list[def_index] and open_list[def_index] or open_list[1]
			open_name = open_name ~= "" and open_name or open_list[1]
		end
		FunOpen.Instance:OpenViewNameByCfg(open_name, item_id)
		if self.data.old_info.is_close_view == 1 then
			XiuZhenRoadWGCtrl.Instance:CloseView()
		end
	end
end
-------------------------------- XiuZhenTaskItemRender  end  --------------------------------

-------------------------------- XiuZhenTaskRewardItemRender start --------------------------------
XiuZhenTaskRewardItemRender = XiuZhenTaskRewardItemRender or BaseClass(BaseRender)
function XiuZhenTaskRewardItemRender:__init()
end

function XiuZhenTaskRewardItemRender:__delete()
	if self.item_cell_root then
		self.item_cell_root:DeleteMe()
		self.item_cell_root = nil
	end
end

function XiuZhenTaskRewardItemRender:LoadCallBack()
	if not self.item_cell_root then
		self.item_cell_root = ItemCell.New(self.node_list["item_root"])
	end
end

function XiuZhenTaskRewardItemRender:OnFlush()
	if not self.data then return end
	self.item_cell_root:SetData(self.data.reward_item)
	self:SetHadGetStatus(self.data.task_states)
end

function XiuZhenTaskRewardItemRender:SetHadGetStatus(task_states)
	task_states = task_states or XIUZHENZHILU_TASK_STATES.WDC
	self.node_list.black_mask:SetActive(task_states == XIUZHENZHILU_TASK_STATES.YLQ)
end

-------------------------------- XiuZhenTaskRewardItemRender  end  --------------------------------


-------------------------------- XieZhenRoadSkillTips start --------------------------------
XieZhenRoadSkillTips = XieZhenRoadSkillTips or BaseClass(SafeBaseView)

function XieZhenRoadSkillTips:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/xiuzhen_road_ui_prefab", "layout_xiezhen_skill_tip")
end

function XieZhenRoadSkillTips:__delete()

end

function XieZhenRoadSkillTips:ShowIndexCallBack()
	if self.data == nil then
		return
	end
	self.node_list.ph_ml_skill_item.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
	self.node_list.skill_name.text.text = self.data.skill_name
	self.node_list.skill_dsc.text.text = self.data.skill_desc
	self:ShowCapability()
end

function XieZhenRoadSkillTips:ShowCapability()
	local cap = XiuZhenRoadWGData.Instance:GetSkillAttrCap(self.data)
	self.node_list.capability_value.text.text = cap
	self.node_list.capability_part:SetActive(cap > 0)
end

function XieZhenRoadSkillTips:SetSkillData(data)
	self.data = data
	self:Open()
end

function XieZhenRoadSkillTips:OnFlush()
end
-------------------------------- XieZhenRoadSkillTips end --------------------------------

-------------------------------- XiuZhenGiftPageCell start --------------------------------
XiuZhenGiftPageCell = XiuZhenGiftPageCell or BaseClass(BaseRender)

function XiuZhenGiftPageCell:__init()
	self.gift_cell = ItemCell.New(self.node_list.ph_gift)
	self.ph_jifen_gift = ItemCell.New(self.node_list.ph_jifen_gift)
	self.node_list.btn_buy.button:AddClickListener(BindTool.Bind(self.OnClickBuyGift, self))
	self.gift_cell:SetTipClickCallBack(BindTool.Bind(self.OnClickGiftCell, self))
end

function XiuZhenGiftPageCell:__delete()
	if self.gift_cell then
		self.gift_cell:DeleteMe()
		self.gift_cell = nil
	end

	if self.ph_jifen_gift then
		self.ph_jifen_gift:DeleteMe()
		self.ph_jifen_gift = nil
	end
end

function XiuZhenGiftPageCell:OnClickGiftCell()
	XiuZhenRoadWGData.Instance:SetCanGiftPanelLoop(false)
end

function XiuZhenGiftPageCell:OnClickBuyGift()
	XiuZhenRoadWGData.Instance:SetCanGiftPanelLoop(false)
	if IsEmptyTable(self.data) then return end
	local tab_type = self.index
	local opert_type = XIUZHENZHILU_OPEN_TYPE.XIUZHENZHILU_OPEN_TYPE_BUY_ITEM
	tab_type = tab_type - 1
	local item_cfg = self.data.cfg.show_item[tab_type]
	local open_day = self.data.cfg.open_day or 1
	if item_cfg then
		open_day = open_day - 1
		XiuZhenRoadWGCtrl.Instance:SendXiuZhenZhiLuReq(opert_type, open_day, tab_type, item_cfg.item_id)
	end
end

function XiuZhenGiftPageCell:OnFlush()
	if IsEmptyTable(self.data) then return end
	local tab_type = self.index
	self.gift_cell:SetData(self.data.cfg.show_item[tab_type - 1])
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.cfg.show_item[tab_type - 1].item_id)
	if item_cfg then
		self.node_list.gift_text.text.text = item_cfg.name
	end
	local cur_day = XiuZhenRoadWGData.Instance:GetActivityOpenDay()

	local init_price = tonumber(self.data.init_price_list[tab_type])
	local price = tonumber(self.data.price_list[tab_type])
	local limit_time = tonumber(self.data.limit_time_list[tab_type])
	local gift_star = tonumber(self.data.gift_star_list[tab_type])
	local glod_type = tonumber(self.data.glod_type_list[tab_type])
	local new_show_item_id = tonumber(self.data.new_show_item_list[tab_type])
	self.ph_jifen_gift:SetData({item_id = new_show_item_id, num = gift_star})
	self.node_list.ph_gift_picre.text.text = init_price
	self.node_list.btn_text.text.text = price
	-- local zhekou = math.ceil((price / init_price) * 10)
	--self.node_list.zhekou_num.text.text = string.format(Language.XiuZhenRoad.ZheKouNum,zhekou)
	local buy_count_info = XiuZhenRoadWGData.Instance:GetDayGiftInfo(self.data.cfg.open_day, tab_type)
	local buy_count = buy_count_info and buy_count_info.buy_count or 0
	self.node_list.ph_gift_limit.text.text = string.format(Language.XiuZhenRoad.BuyGiftLimit, buy_count, limit_time)
	--self.node_list.get_star_text.text.text = string.format(Language.XiuZhenRoad.GetStarText2, gift_star)
	--1 = 仙玉，2=绑玉
	self.node_list.yuanjia_picre_icon.image:LoadSprite(ResPath.GetMoneyIcon(glod_type))
	self.node_list.buy_picre_icon.image:LoadSprite(ResPath.GetMoneyIcon(glod_type))

	self.node_list.btn_buy:SetActive(buy_count < limit_time)
	self.node_list.img_ygm:SetActive(buy_count >= limit_time)
	self.node_list.buy_text:SetActive(self.data.cfg.open_day <= cur_day)
	self.node_list.no_buy_text:SetActive(self.data.cfg.open_day > cur_day)
	XUI.SetButtonEnabled(self.node_list.btn_buy, self.data.cfg.open_day <= cur_day)
	--self.node_list.btn_buy_effect:SetActive(self.data.cfg.open_day <= cur_day)
end
-------------------------------- XiuZhenGiftPageCell  end  --------------------------------