GuildView = GuildView or BaseClass()
local BossSelectState = {
    BoosActRule     = 1,    --活动规则
    BossPersonRank  = 2,    --个人排名
}
-- 仙盟boss  弃用的！
function GuildView:InitBossView()

	self.node_list.btn_enter.button:AddClickListener(BindTool.Bind(self.OnChallengeBosss, self))
	--boos id
	-- local boss_id = GuildBossWGData.Instance:GetGuildBossStatus().boss_id
	--根据bossid取得需要生成的模型id
	local boss_config = GuildBossWGData.Instance:GetBossCfg()

	self.boss_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["BossRoleDisplay"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}
	
	self.boss_model:SetRenderTexUI3DModel(display_data)
    -- self.boss_model:SetUI3DModel(self.node_list["BossRoleDisplay"].transform, self.node_list["BossModelEvent"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    self:AddUiRoleModel(self.boss_model)
	self.boss_model:SetMainAsset(ResPath.GetMonsterModel(boss_config.resid))
	self.boss_model:PlayMonsterAction()

	self.node_list.dec.text.text = Language.GuildBoss.GuildBossDec
	self.node_list.dec2.text.text = Language.GuildBoss.GuildBossDec2
	 for i = 1, 2 do
        self.node_list["boss_left_toggle_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnBossSelectToggle, self, i))
    end
	--页签1奖励格子
	self.boss_reward_list = AsyncBaseGrid.New()
	self.boss_reward_list:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list.boss_reward_preview_list,
                                                assetBundle = "uis/view/guild_shouhu_ui_prefab",
                                                assetName = "shouhu_reward_render",
                                                itemRender = ConditionBossItemRender, })
    self.boss_reward_list:SetStartZeroIndex(false)
      --页签2排名
    self.boss_person_rank_list = AsyncListView.New(BossPersonRankRender, self.node_list.boss_rank_list)--排名 信息下发,奖励自取
    XUI.AddClickEventListener(self.node_list.tips_btn, BindTool.Bind(self.OnClickTips,self))

end

function GuildView:DeleteBossView()
	if self.boss_reward_list then
       self.boss_reward_list:DeleteMe()
       self.boss_reward_list = nil
    end
    
	if self.boss_model then
		self.boss_model:DeleteMe()
		self.boss_model = nil
	end

	if CountDownManager.Instance:HasCountDown("guild_boss_info") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_info")
	end
	-- self.cache_resid = nil
	self.old_boss_state = nil
	if self.boss_person_rank_list then
       self.boss_person_rank_list:DeleteMe()
       self.boss_person_rank_list = nil
    end
	self.boss_special_toggle_param = nil
	if self.alert_window then
  		self.alert_window:DeleteMe()
   		self.alert_window = nil
	end
end
--ShowIndex

function GuildView:ShowGuildBossCallBack()
	GuildWGCtrl.Instance:SendReqBossInfo()
	--Toggle状态
	if self.boss_special_toggle_param then
        self.node_list["boss_left_toggle_" .. self.boss_special_toggle_param].toggle.isOn = true
        self.boss_special_toggle_param = nil
    else
        self.node_list["boss_left_toggle_1"].toggle.isOn = true
    end
end
--特殊情况设置直接打开个人排行榜(没设置默认第一个)
function GuildView:SetBossSpeciaPlaram(toggle_index)
    self.boss_special_toggle_param = toggle_index
end

function GuildView:OnBossSelectToggle(index, is_on)
    if is_on then
        if self.old_boss_state == index then
            return
        end
        self:SetBossContentActive(index, true)
        self:BossChangeState(index)
        self.old_boss_state = index
    end
end

function GuildView:SetBossContentActive(index, active)
    for i = 1, 2 do
        if i == index then
            self.node_list["boss_left_content"..i]:SetActive(active)
        else
            self.node_list["boss_left_content"..i]:SetActive(not active)
        end
    end
end

--改变页面状态
function GuildView:BossChangeState(index)
	--规则
    if index == BossSelectState.BoosActRule then
    	--生成奖励预览
		local boss_status_info = GuildBossWGData.Instance:GetGuildBossStatus()
		local reward_cfg = GuildBossWGData.Instance:GetGuildBossRewards()
		local reward_list = {}
		if reward_cfg and reward_cfg.reward_item then
			for i=0,#reward_cfg.reward_item do
				table.insert(reward_list,reward_cfg.reward_item[i])
			end
		end
		
		if not reward_list or IsEmptyTable(reward_list) then
       		reward_list = GuildBossWGData.Instance:GetDefaultPersonRankDataList() --如果没有数据，获取一个默认数据
    	end
 		self.boss_reward_list:SetDataList(reward_list, 3)
    --排名
    elseif index == BossSelectState.BossPersonRank then
    	--刷新排行榜
        self:FlushBossPersonRank()
    end
end

--刷新排行榜+个人
function GuildView:FlushBossPersonRank()
	local boss_status_info = GuildBossWGData.Instance:GetGuildBossStatus()
    local rank_list = GuildBossWGData.Instance:GetGuildBossOutRank()  --总排行榜信息
    local my_data_list = GuildBossWGData.Instance:MyGuildBossRankInfo() --个人排行信息
    self.boss_person_rank_list:SetDataList(rank_list, 0)
    --通关星数
	local star_num
	if boss_status_info.is_pass == 1 then
		star_num = boss_status_info.star_level
	else
		star_num = 0
	end
	--刷新title个人 如果通关了
	if my_data_list and my_data_list.total_hurt > 0 and boss_status_info.is_pass == 1 then
		self.node_list["rank_text"].text.text = my_data_list.rank
	    self.node_list["damage_text"].text.text = my_data_list.total_hurt
	    self.node_list["time_text"].text.text = "00:"..TimeUtil.MSTime(boss_status_info.pass_time)
	--如果没通关伤害大于零
	elseif my_data_list and my_data_list.total_hurt > 0 and boss_status_info.is_pass == 0 then
		self.node_list["rank_text"].text.text = my_data_list.rank
	    self.node_list["damage_text"].text.text = my_data_list.total_hurt
	    self.node_list["time_text"].text.text = Language.GuildBoss.NoTongGuan
	else
		self.node_list["rank_text"].text.text = Language.GuildBoss.NoRank--未上榜
		self.node_list["damage_text"].text.text = 0
		self.node_list["time_text"].text.text = Language.GuildBoss.NoTime
	end
    --星
    local asset_name = {[1] = "star_l_1",[2] = "star_l_3",} --star_l_1实心
    for i = 1, 3 do
    	if i <= star_num then
    		self.node_list["star_image_"..i].image:LoadSprite(ResPath.GetF2CommonImages(asset_name[1]))
    	else
    		self.node_list["star_image_"..i].image:LoadSprite(ResPath.GetF2CommonImages(asset_name[2]))
    	end
    end
end

-- 前往挑战
function GuildView:OnChallengeBosss()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local guild_post = role_vo.guild_post
	local is_mengzhu = guild_post == 3 or guild_post == 4 or guild_post == 7
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local is_can = guild_bosss_info.active_is_open == 1
	if self.is_open_act then
		GuildBossWGCtrl.SendGuildBossEnterReq()
	else
		if self.money_can_open then
			if is_mengzhu and self.is_week_can_open and self.today_can_open and self.money_can_open and is_can then
				if not self.alert_window then
					self.alert_window = Alert.New()
					self.alert_window:SetOkFunc(function()
						GuildBossWGCtrl.SendGuildBossStartReq()
					end)
				end
				self.alert_window:SetLableString(Language.GuildBoss.IsOpenTxt)
				local pos = Vector3(0,57,0)
				self.alert_window:SetRichDialogTxtPos(pos)
				self.alert_window:Open()
			else
				if not self.today_can_open then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossDayTips)
				elseif not self.is_week_can_open then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossWeekDayTips)
				end
			end
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GuildBossMoneyTips)
		end
	end
end

function GuildView:OnFlushBossView()
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	self.is_week_can_open = guild_bosss_info.open_times < GuildDataConst.GUILD_MAX_BOSS
	self.is_open_act = guild_bosss_info.finish_timestamp > TimeWGCtrl.Instance:GetServerTime()
	local is_can = guild_bosss_info.active_is_open == 1
	local tody_num
	if self.is_open_act then
		tody_num = 1
	else
		tody_num = guild_bosss_info.today_open_num
	end
	self.today_can_open = tody_num < GuildDataConst.TODAY_GUILD_MAX_BOSS
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local guild_post = role_vo.guild_post
	local week_num = guild_bosss_info.open_times
	local is_mengzhu = guild_post == 3 or guild_post == 4 or guild_post == 7 --GUILD_POST.TUANGZHANG
	local other_cfg = ConfigManager.Instance:GetAutoConfig("guild_boss_auto").other_cfg[1]
	local have_guild_money = guild_bosss_info.guild_money--GuildDataConst.GUILDVO.guild_money
	local need_guild_money = GuildBossWGData.Instance:GetBossOpenNeedMoneyNum(week_num + 1)
	self.money_can_open = have_guild_money >= need_guild_money
	local week_max_num = GuildDataConst.GUILD_MAX_BOSS
	local tody_max_num = GuildDataConst.TODAY_GUILD_MAX_BOSS
	local color1,color2,color0
	--开启可以进入
	local is_money_red = have_guild_money < need_guild_money
	if is_money_red then
		color0 = COLOR3B.RED
	else
		color0 = COLOR3B.GREEN
	end

	local consume_text = ToColorStr(have_guild_money .. "/" .. need_guild_money,color0)
	self.node_list.consume.text.text = string.format(Language.GuildBoss.EnterConsume,consume_text)

	if self.is_week_can_open then
		color1 = COLOR3B.GREEN
	else
		color1 = COLOR3B.RED
	end
	if self.today_can_open then
		color2 = COLOR3B.GREEN
	else
		color2 = COLOR3B.RED
	end
	--本周
	self.node_list.week_open_num.text.text = string.format(Language.GuildBoss.WeekOpenNum, color1, week_num, week_max_num)
	--今日2
	self.node_list.today_open_num.text.text = string.format(Language.GuildBoss.ToDayOpenNum, color2, tody_num, tody_max_num)
	if CountDownManager.Instance:HasCountDown("guild_boss_info") then
		CountDownManager.Instance:RemoveCountDown("guild_boss_info")
	end
	local str = Language.GuildBoss.EnterBtnTxt[2]
	if not self.is_open_act then
		if not self.is_week_can_open then
			str = Language.GuildBoss.EnterBtnTxt[4]
		elseif self.today_can_open and not self.is_open_act then
			if is_mengzhu then
				str = Language.GuildBoss.EnterBtnTxt[1]
			else
				str = Language.GuildBoss.EnterBtnTxt[3]
			end
		end
	end

	self.node_list.lbl_enter.text.text = str
	--true 置灰
	XUI.SetGraphicGrey(self.node_list.btn_enter, not self.is_open_act and (not self.is_week_can_open or not is_can or not self.today_can_open or not is_mengzhu))
	--刷新title和排名
	self:FlushBossPersonRank()
	self.node_list.red_remind_boss:SetActive(GuildBossWGData.Instance:IsGuildBossOpen())
end


function GuildView:SetBossModel(boss_id,data)
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
	if monster_cfg then
		if data and data.position then
			local position_tab = string.split(data.position, "|")
			local vector_pos = Vector3(position_tab[1], position_tab[2], position_tab[3])
			local scale = data.scale
			self.boss_model:CustomDisplayPositionAndRotation(vector_pos,Vector3(0, 180, 0), Vector3(scale,scale,scale))
		end
	end
end

function GuildView:OnClickTips()
   	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.GuildBoss.RUleTitleTips)
		role_tip:SetContent(Language.GuildBoss.RUleBodyTips)
	end
end
function GuildView:UpdateBossCDCountDown( elapse_time, total_time )
	local guild_bosss_info = GuildBossWGData.Instance:GetGuildBossStatus()
	local time = guild_bosss_info.open_cd_end_timestamp - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 then
		self.node_list.enter_times_cd.text.text = Language.GuildBoss.BossCD .. ToColorStr(TimeUtil.FormatSecond(time),COLOR3B.RED)
	else
		self.node_list.enter_times_cd.text.text = Language.GuildBoss.BossCD .. ToColorStr(TimeUtil.FormatSecond(0,3), COLOR3B.GREEN)
	end
end

function GuildView:CompleteBossCDCountDown()
	self.node_list.enter_times_cd.text.text = Language.GuildBoss.BossCD .. ToColorStr(TimeUtil.FormatSecond(0,3),COLOR3B.GREEN)
end



-------------BossPersonRankRender--------------
---个人排行格子
BossPersonRankRender = BossPersonRankRender or BaseClass(SHRankRenderBase)
function BossPersonRankRender:__init()

end

function BossPersonRankRender:__delete()

end

--获取个人排行名次
function BossPersonRankRender:GetRewardDataList(index)
	if index + 1 > 10 then
		--10名以后的格子数据
		return GuildBossWGData.Instance:GetAfterTenRankItemRewardList(index + 1)
	end
	 if self.data.uid and self.data.uid ~= 0 then
         return GuildBossWGData.Instance:GetPersonItemRewardByRank(index + 1)
    end
    --3是代表 默认取三星
    return GuildBossWGData.Instance:GetPersonItemRewardByRank(index + 1, 3)
end

function BossPersonRankRender:OnFlush()
    SHRankRenderBase.OnFlush(self)
    if self.data.uid and self.data.uid ~= 0 and self.data.total_hurt and self.data.total_hurt > 0 then
        if self.data.total_hurt ~= nil then
            self.node_list["damage"].text.text = CommonDataManager.ConverExp(BigNumFormat(self.data.total_hurt))
        end
        self.node_list["damage"]:CustomSetActive(true)
        self.node_list["role_name"]:CustomSetActive(true)
        self.node_list["role_name"].text.text = self.data.game_name
    else
        if self.data.rank_str then
            self.node_list["damage"]:CustomSetActive(false)
            self.node_list["role_name"]:CustomSetActive(false)
        else
            self.node_list["damage"]:CustomSetActive(true)
            self.node_list["role_name"]:CustomSetActive(true)
            self.node_list["role_name"]:CustomSetActive(true)
            self.node_list["damage"].text.text = Language.Guild.SHJingQingQiDai
            self.node_list["role_name"].text.text = Language.Guild.SHJingQingQiDai
        end
    end
end
------------------------ConditionBossItemRender------------------------------
--重写一下展示格子,为了展示仙品属性和星级
ConditionBossItemRender = ConditionBossItemRender or BaseClass(ConditionPaiPinItemRender)
function ConditionBossItemRender:__init()
end

function ConditionBossItemRender:SetData(data)
    local param_t = {}
	param_t.star_level = GuildBossWGData.Instance:GetGuildBossRewardStar()
	data.param = param_t
	ItemCell.SetData(self, data)
end
