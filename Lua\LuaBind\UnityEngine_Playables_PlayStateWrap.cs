﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Playables_PlayStateWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>BeginEnum(typeof(UnityEngine.Playables.PlayState));
		<PERSON><PERSON>("Paused", get_Paused, null);
		<PERSON><PERSON>("Playing", get_Playing, null);
		<PERSON><PERSON>unction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<UnityEngine.Playables.PlayState>.Check = CheckType;
		StackTraits<UnityEngine.Playables.PlayState>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.Playables.PlayState arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.Playables.PlayState), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Paused(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Playables.PlayState.Paused);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Playing(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.Playables.PlayState.Playing);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.Playables.PlayState o = (UnityEngine.Playables.PlayState)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

