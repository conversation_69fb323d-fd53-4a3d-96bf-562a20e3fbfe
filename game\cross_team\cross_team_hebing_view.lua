--申请界面（别人合并进我的队伍）
CrossTeamHeBingView = CrossTeamHeBingView or BaseClass(SafeBaseView)

function CrossTeamHeBingView:__init()
    self:SetMaskBg()
    self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector = Vector2(0, -4), sizeDelta = Vector2(1078, 608)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_hebing")
	self.select_index = 0
end

function CrossTeamHeBingView:ReleaseCallBack()
    if self.hebing_list then
		self.hebing_list:DeleteMe()
		self.hebing_list = nil
	end

	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	if self.new_hebing_event then
		GlobalEventSystem:UnBind(self.new_hebing_event)
		self.new_hebing_event = nil
	end
end

function CrossTeamHeBingView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleHeBing
	self.hebing_list = AsyncListView.New(CrossTeamHeBingListItem, self.node_list["ph_apply_list"])
	XUI.AddClickEventListener(self.node_list["btn_today_refuse_check"], BindTool.Bind1(self.OnClickTodayCheck, self))
	--XUI.AddClickEventListener(self.node_list["btn_auto_refuse"], BindTool.Bind1(self.OnClickAutoRefuse, self))
	self.new_hebing_event = GlobalEventSystem:Bind(OtherEventType.TeamHeBingInfo, BindTool.Bind(self.OnNewHeBingCallBack, self))
end

function CrossTeamHeBingView:ShowIndexCallBack()
	self:Flush()
	self.node_list["btn_today_refuse_select"]:SetActive(false)
end

function CrossTeamHeBingView:OnFlush()
	local hebing_list = CrossTeamWGData.Instance:GetHeBingList()
	if hebing_list then
		self.hebing_list:SetDataList(hebing_list, 0)
	end
	--self.node_list.layout_blank_tip2:SetActive(#hebing_list <= 0)
end

function CrossTeamHeBingView:OnNewHeBingCallBack()
	self:Flush()
end

function CrossTeamHeBingView:GetTodayCheckActive()
	if not self.node_list["btn_today_refuse_select"] then
		return false
	end
	return self.node_list["btn_today_refuse_select"].gameObject.activeSelf
end

function CrossTeamHeBingView:OnClickTodayCheck()
	local is_select = self.node_list["btn_today_refuse_select"].gameObject.activeSelf
	self.node_list["btn_today_refuse_select"]:SetActive(not is_select)
end

function CrossTeamHeBingView:OnClickAutoRefuse()
	local allJoinReq = CrossTeamWGData.Instance:GetReqTeamList()
	if nil == allJoinReq or #allJoinReq <= 0 then
		return
	end
	for k,v in pairs(allJoinReq) do
		local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamReqJoinRet)
		send_protocol.req_uuid = v.req_uuid
		send_protocol.result = 0
		send_protocol:EncodeAndSend()
	end
	CrossTeamWGData.Instance:TeamJoinReqClear()
	--关闭当前界面
	self:Close()
	ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
end

function CrossTeamHeBingView:CreateRoleHeadCell(uuid, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
	if self.role_head_cell == nil then
		self.role_head_cell = RoleHeadCell.New(false)
	end

	CrossTeamWGCtrl.Instance:QueryCrossTeamInfo(uuid, function(protocol)
		if self:IsLoaded() and self:IsOpen() then
			local main_role = Scene.Instance:GetMainRole()
			local main_role_vo = main_role.vo
			local role_info = {
				role_id = uuid.temp_low,
				role_name = role_name,
				prof = prof,
				sex = sex,
				is_online = is_online,
				team_index = protocol.team_index,
				team_type = TEAM_INVITE_TYPE.CHAT,
				plat_type = main_role_vo.plat_type,
				plat_name = main_role_vo.plat_name,
				server_id = server_id,
			}
            self.role_head_cell:SetRoleInfo(role_info)
			self.role_head_cell:OpenMenu(node)
		end
	end)
end



------------------itemRender-----------------
CrossTeamHeBingListItem = CrossTeamHeBingListItem or BaseClass(BaseRender)

function CrossTeamHeBingListItem:__init()
	self:CreateChild()
	XUI.AddClickEventListener(self.node_list["head_click"], BindTool.Bind1(self.OnClickHead, self))
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function CrossTeamHeBingListItem:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function CrossTeamHeBingListItem:CreateChild()
	self.node_list["btn_refuse"].button:AddClickListener(function()
		local uuid = self.data.uuid
		CrossTeamWGCtrl.Instance:SendTeamReqJoinRet(uuid, 0)

		if CrossTeamWGCtrl.Instance:GetHeBingTodayCheckActive() then
			CrossTeamWGCtrl.Instance:SendNoLongerOperateReq(LOGIN_NO_LONGER_TYPE.LOGIN_NO_LONGER_TYPE_ACCEPT_MERGE, uuid)
		end

		CrossTeamWGCtrl.Instance:RemoveHeBingItem(uuid)
	end)

    self.node_list["btn_agree"].button:AddClickListener(function()
        local uuid = self.data.uuid
		CrossTeamWGCtrl.Instance:SendTeamReqJoinRet(uuid, 1)
		CrossTeamWGCtrl.Instance:RemoveHeBingItem(uuid)
	end)
end

function CrossTeamHeBingListItem:OnFlush()
	--[[
	if self.data.req_role_vip_level and self.data.req_role_vip_level > 0 then
		self.node_list["vip_level"]:SetActive(true)
		self.node_list["vip_level"].text.text = string.format(Language.NewTeam.Vip_Level, self.data.req_role_vip_level)
	else
		self.node_list["vip_level"]:SetActive(false)
	end
	]]
	self.node_list.lbl_role_name.text.text = self.data.req_role_name

	-- 新增等级和关系
	local level_str = string.format(Language.NewTeam.PTLevel2,self.data.req_role_level)
	EmojiTextUtil.ParseRichText(self.node_list["lbl_level"].emoji_text, level_str, 20, COLOR3B.DEFAULT)
	local relation_flag = bit:d2b_two(self.data.relation_flag or 0)
	local index = -1
	for k,v in pairs(relation_flag) do
		if v == 1 then
			index = k
			break
		end
	end
	local relation_str = Language.NewTeam.TeamMateRelation[index + 2]
	if index + 2 == 1 then
		relation_str = ToColorStr(relation_str, COLOR3B.DEFAULT)
	end
	self.node_list.lbl_relationship.text.text = relation_str

	--头像框
	local data = {}
	data.role_id = self.data.uuid.temp_low
	data.prof = self.data.req_role_prof
	data.sex = self.data.req_role_sex
	data.fashion_photoframe = self.data.req_role_photoframe
    self.head_cell:SetData(data)
end

function CrossTeamHeBingListItem:OnClickInvite()

end

function CrossTeamHeBingListItem:OnClickHead()
	local uuid = self.data.uuid
	BrowseWGCtrl.Instance:BrowRoelInfo(uuid.temp_low, function(param_protocol)
		if self.view and self.view.gameObject and self.view.gameObject.activeInHierarchy == true then
			CrossTeamWGCtrl.Instance:CreateHeBingRoleHeadCell(uuid, self.data.req_role_name,param_protocol.prof,param_protocol.sex,param_protocol.is_online, self.node_list.head_click, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
		end
	end)
end
