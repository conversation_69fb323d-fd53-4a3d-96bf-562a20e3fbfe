require("game/discount_purchase/discount_purchase_wg_data")
require("game/discount_purchase/discount_purchase_view")
require("game/discount_purchase/discount_purchase_tip")

DiscountPurchaseWGCtrl = DiscountPurchaseWGCtrl or BaseClass(BaseWGCtrl)

function DiscountPurchaseWGCtrl:__init()
	if DiscountPurchaseWGCtrl.Instance then
		ErrorLog("[DiscountPurchaseWGCtrl] attempt to create singleton twice!")
		return
	end

	DiscountPurchaseWGCtrl.Instance = self
	self.data = DiscountPurchaseWGData.New()
    self.view = DiscountPurchaseView.New(GuideModuleName.DiscountPurchaseView)
  
    self:RegisterAllProtocols()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.CheckDiscountPurchaseIsOpen, self)) --主界面加载完成
end

function DiscountPurchaseWGCtrl:__delete()
	DiscountPurchaseWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil
end

function DiscountPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSPopupGift2ClientReq)
	self:RegisterProtocol(SPopupGift2AllInfo, "OnSPopupGiftAllInfo")
end

function DiscountPurchaseWGCtrl:SendPopupGiftClientReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSPopupGiftClientReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	-- print_error("一折弹窗礼包请求操作", protocol)
	protocol:EncodeAndSend()
end

-- 限时一折弹窗礼包信息
function DiscountPurchaseWGCtrl:OnSPopupGiftAllInfo(protocol)
	-- print_error("一折弹窗礼包信息", protocol)
	self.data:RefreshPopupGiftData(protocol)
	self:CheckDiscountPurchaseIsOpen()
	self:FlushDiscountPurchaseView()
	MainuiWGCtrl.Instance:FlushView(0, "discount_purchase_tip")
end

-- 刷新一折弹窗礼包
function DiscountPurchaseWGCtrl:FlushDiscountPurchaseView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

-- 关闭一折弹窗礼包
function DiscountPurchaseWGCtrl:CloseDiscountPurchaseView()
	if self.view:IsOpen() then
		self.view:Close()
	end
end

-- 打开一折弹窗礼包
function DiscountPurchaseWGCtrl:OpenDiscountPurchaseView()
	if self.view:IsOpen() then
		self.view:Flush()
	else
		self.view:Open()
	end
end

-- 设置活动开启状态
function DiscountPurchaseWGCtrl:CheckDiscountPurchaseIsOpen()
	local is_can_open = self.data:checkCanOpenDiscountPurchaseView()--获取是否达到开启条件
	local act_type = ACTIVITY_TYPE.DISCOUNT_PURCHASE

	if is_can_open then
		self:OpenDiscountPurchaseView()
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(act_type, false)
		ActivityWGData.Instance:SetActivityStatus(act_type, ACTIVITY_STATUS.OPEN, self.data.end_time, 0, 0)
	else
		self:FlushDiscountPurchaseView()--购买完不立即关闭界面
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(act_type, false)
		ActivityWGData.Instance:SetActivityStatus(act_type, ACTIVITY_STATUS.CLOSE)
	end
end

-- 检测是否需要请求灵玉不足直购操作
function DiscountPurchaseWGCtrl:CheckNoGoldPopupGift()
	local can_send, gift_grade = self.data:CheckNeedNoGoldPopupGift()
	--print_error("检测金币不足礼包开启", can_send, gift_grade)
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 检测是否需要请求道具不足直购操作
function DiscountPurchaseWGCtrl:CheckOpenViewPopupGift(act_id)
	local can_send, gift_grade = self.data:CheckNeedOpenViewPopupGift(act_id)
	--print_error("检测活动礼包开启", act_id, can_send, gift_grade)
	if can_send then
		self:SendPopupGiftClientReq(POPUP_GIFT_OPERA_TYPE.POPUP_GIFT_OPERA_TYPE_POPUP, gift_grade)
	end
end

-- 购买成功返回
function DiscountPurchaseWGCtrl:OnOperateResult(result)
	local is_suc = result == 1
	if is_suc then
		local gift_data = (((self.data or {}).show_data or {}).gift_data or {})[1]
		if IsEmptyTable(gift_data) then
			return
		end
		local reward_list = gift_data.item_list or {}
		TipWGCtrl.Instance:ShowGetReward(nil, reward_list, nil, nil, nil, true)
	end
end