ControlBeastsHandBookRewardView = ControlBeastsHandBookRewardView or BaseClass(SafeBaseView)
function ControlBeastsHandBookRewardView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_hand_book_reward_view")
end

function ControlBeastsHandBookRewardView:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ControlBeastsHandBookRewardCell, self.node_list.reward_list)
		self.reward_list:SetUseRenderClick(true)
		self.reward_list:SetSelectCallBack(BindTool.Bind(self.GetBookRewardClick, self))
	end

	-- if nil == self.display_model then
	-- 	local display_node = self.node_list.display
	-- 	self.display_model = RoleModel.New()
    --     local display_data = {
	-- 		parent_node = display_node,
	-- 		camera_type = MODEL_CAMERA_TYPE.BASE,
	-- 		rt_scale_type = ModelRTSCaleType.M,
	-- 		can_drag = true,
	-- 	}

	-- 	self.display_model:SetRenderTexUI3DModel(display_data)
	-- 	self:AddUiRoleModel(self.display_model)
	-- end

	XUI.AddClickEventListener(self.node_list.btn_change_left, BindTool.Bind2(self.ClickSelectLeft, self))
    XUI.AddClickEventListener(self.node_list.btn_change_right, BindTool.Bind2(self.ClickSelectRight, self))
    XUI.AddClickEventListener(self.node_list.btn_quick_get_reward, BindTool.Bind2(self.ClickQuickGetReward, self))
end

function ControlBeastsHandBookRewardView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	-- if self.display_model then
	-- 	self.display_model:DeleteMe()
	-- 	self.display_model = nil
	-- end

	self.beast_type = nil
	self.data_list = nil
	self.data_index = nil
	self.beast_model_res_id = nil
end

function ControlBeastsHandBookRewardView:SetSelectBeastType(beast_type)
	self.beast_type = beast_type
end

-- 关闭前调用
function ControlBeastsHandBookRewardView:CloseCallBack()
	self.beast_type = nil
	self.data_list = nil
	self.data_index = nil
	self.quick_reward_data = nil
	self.beast_model_res_id = nil
end

function ControlBeastsHandBookRewardView:GetBookRewardClick(cell, cell_index, is_default, is_click)
	if cell == nil or cell.data == nil or is_default then
		return
	end

	-- 领取奖励
	if self.data_list == nil or self.data_index == nil then
		return
	end

	local now_show_data = self.data_list[self.data_index]
	ControlBeastsWGCtrl.Instance:SendOperateTypeBeastHandBookReward(now_show_data.beast_type)
end

function ControlBeastsHandBookRewardView:FlushNowRewardList()
	self.data_list, self.quick_reward_data = ControlBeastsWGData.Instance:GetNowGetRewardList(self.beast_type)
	local red_data = self.quick_reward_data and self.quick_reward_data[1]
	
	if self.data_index ~= nil then
		return
	end
	
	for index, beast_data in ipairs(self.data_list) do
		if red_data ~= nil then
			if beast_data.beast_type == red_data.beast_type then
				self.data_index = index
				break
			end
		else
			if beast_data.beast_type == self.beast_type then
				self.data_index = index
				break
			end
		end
	end
end


function ControlBeastsHandBookRewardView:OnFlush()
	self:FlushNowRewardList()
	self:FlushCenterMessage()
end

function ControlBeastsHandBookRewardView:FlushCenterMessage()
	if self.data_list == nil or self.data_index == nil then
		return
	end

	local now_show_data = self.data_list[self.data_index]
	-- 刷新数据
	self.node_list.btn_change_left_red:CustomSetActive(self:GetLeftRed())
	self.node_list.btn_change_right_red:CustomSetActive(self:GetRightRed())
	self.node_list.btn_change_left:CustomSetActive(self.data_index ~= 1)
	self.node_list.btn_change_right:CustomSetActive(self.data_index ~= #self.data_list)

	-- 展示模型
    -- local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(now_show_data.beast_id)
    -- if self.beast_model_res_id ~= res_id then
    --     self.beast_model_res_id = res_id
    --     local bundle, asset = ResPath.GetBeastsModel(res_id)
    --     self.display_model:SetMainAsset(bundle, asset)
    --     self.display_model:PlayRoleAction(SceneObjAnimator.Rest)
    -- end
	local bundle, asset = ResPath.GetRawImagesPNG(string.format("a3_hs_tj_hs%d", now_show_data.beast_type))
	self.node_list.display.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.display.raw_image:SetNativeSize()
	end)

	self.node_list.btn_quick_get_reward:CustomSetActive(self.quick_reward_data ~= nil and (not IsEmptyTable(self.quick_reward_data)))

	-- 设置中间数据
	local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", now_show_data.beast_color))
	self.node_list.beast_color_img.image:LoadSprite(bundle, asset, function()
		self.node_list.beast_color_img.image:SetNativeSize()
	end)

	if BEAST_EFFECT_COLOR[now_show_data.beast_color] then
		bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[now_show_data.beast_color])
		self.node_list.beast_color_img:ChangeAsset(bundle, asset)
	end

	local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", now_show_data.beast_element))
	self.node_list.beast_element_img.image:LoadSprite(bundle, asset, function()
		self.node_list.beast_element_img.image:SetNativeSize()
	end)

	self.node_list.beast_color_name.text.text = now_show_data.beast_name
	self.node_list.beast_name_txt.text.text = now_show_data.beast_name
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.beast_name_layout.rect)
	local desc_cfg = ControlBeastsWGData.Instance:GetHandBookDescCfgByType(now_show_data.beast_type)
	local desc = desc_cfg and desc_cfg.beast_des or ""
	self.node_list.beast_desc_txt.text.text = desc
	local book_data = ControlBeastsWGData.Instance:GetBeastHoodbookInfo(now_show_data.beast_type)
	local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(now_show_data.beast_id)
	if beast_map_data and beast_map_data.beast_preview then
		local min_start_beast = beast_map_data.beast_preview[1]
		local min_star = min_start_beast and min_start_beast.beast_star or 1
		local star_num = book_data and book_data.reward_star or -1
		local max_star_num = book_data and book_data.max_star or -1
		local is_act = max_star_num ~= -1
		local final_star_num = min_star
		-- 如果已经激活
		if is_act and star_num ~= -1 then
			final_star_num = star_num + 1
		end

		local reward_cfg = ControlBeastsWGData.Instance:GetHandBookRewardCfgByTypeStar(now_show_data.beast_type, final_star_num)
		-- local is_ylq = ControlBeastsWGData.Instance:ChenkHaveRewardCanGeted(now_show_data.beast_type)
		local can_lq = ControlBeastsWGData.Instance:ChenkHaveRewardCanGet(now_show_data.beast_type)
		local color = can_lq and COLOR3B.L_GREEN or COLOR3B.L_RED
		self.node_list.get_reward_reward.text.text = ToColorStr(string.format(Language.ContralBeasts.BeastsBookRewardTips, final_star_num), color)
		if not is_act then
			self.node_list.get_reward_reward.text.text = Language.ContralBeasts.BeastsBookRewardTips2
		end

		self.node_list.reward_list:CustomSetActive(reward_cfg ~= nil)
		self.node_list.get_reward_reward:CustomSetActive(reward_cfg ~= nil)

		if reward_cfg == nil then
			return
		end

		local reward_list = {}
		if not IsEmptyTable(reward_cfg) then
			for i = 0, #reward_cfg.reward_item do
				local reward_data = {}
				local data = reward_cfg.reward_item[i]
				reward_data.item_id = data.item_id
				reward_data.num = data.num
				reward_data.is_bind = data.is_bind
				reward_data.can_lq = can_lq
				-- reward_data.is_ylq = is_ylq
	
				table.insert(reward_list, reward_data)
			end
		end
		
		self.reward_list:SetDataList(reward_list)
	end
end

-- 获取左侧红点
function ControlBeastsHandBookRewardView:GetLeftRed()
	if self.data_list == nil or self.data_index == nil then
		return false
	end

	for i = 1, self.data_index do
		if i ~= self.data_index then
			local now_show_data = self.data_list[i]
			if ControlBeastsWGData.Instance:ChenkHaveRewardCanGet(now_show_data.beast_type) 
			and ControlBeastsWGData.Instance:GetHandBookRewardCfgByTypeStar(now_show_data.beast_type, now_show_data.beast_star) then
				return true
			end
		end
	end

	return false
end

-- 获取右侧红点
function ControlBeastsHandBookRewardView:GetRightRed()
	if self.data_list == nil or self.data_index == nil then
		return false
	end

	for i = self.data_index, #self.data_list do
		if i ~= self.data_index then
			local now_show_data = self.data_list[i]
			if ControlBeastsWGData.Instance:ChenkHaveRewardCanGet(now_show_data.beast_type) 
			and ControlBeastsWGData.Instance:GetHandBookRewardCfgByTypeStar(now_show_data.beast_type, now_show_data.beast_star) then
				return true
			end
		end
	end

	return false
end

------------------------------------------------------------------------
-- 切换左
function ControlBeastsHandBookRewardView:ClickSelectLeft()
	if self.data_list == nil or self.data_index == nil then
		return
	end

	self.data_index = self.data_index - 1
	if self.data_index <= 1 then
		self.data_index = 1
	end

	self:FlushCenterMessage()
end

--切换右
function ControlBeastsHandBookRewardView:ClickSelectRight()
	if self.data_list == nil or self.data_index == nil then
		return
	end

	self.data_index = self.data_index + 1
	if self.data_index >= #self.data_list then
		self.data_index = #self.data_list
	end

	self:FlushCenterMessage()
end

-- 一键领取
function ControlBeastsHandBookRewardView:ClickQuickGetReward()
	if self.quick_reward_data == nil or IsEmptyTable(self.quick_reward_data) then
		return
	end

	for i, now_show_data in ipairs(self.quick_reward_data) do
		ControlBeastsWGCtrl.Instance:SendOperateTypeBeastHandBookReward(now_show_data.beast_type)
	end
end

--------------------------------------------------------------------------------
ControlBeastsHandBookRewardCell = ControlBeastsHandBookRewardCell or BaseClass(ItemCell)
function ControlBeastsHandBookRewardCell:OnFlush()
	ItemCell.OnFlush(self)
	self:SetCanOperateIconVisible(self.data.can_lq)
end

-- 点击格子
function ControlBeastsHandBookRewardCell:OnClick()
	if self.data and (not self.data.can_lq) then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if nil == item_cfg then return end

		TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
		if self.need_item_get_way then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
		else
			TipWGCtrl.Instance:OpenItem(self.data, self.item_tip_from or ItemTip.FROM_NORMAL, nil, nil, self.item_tips_btn_click_callback)
		end
	else
		BaseRender.OnClick(self)
	end
end


