local empty_table = {}
function NewAppearanceWGData:InitAdvancedParam()
    self.advanced_info = {}
    self.advanced_right_flag = {}
end

AppearanceActIndexList = {
    [TabIndex.new_appearance_upgrade_wing] = 5,
    [TabIndex.new_appearance_upgrade_lingchong] = 2,
    [TabIndex.new_appearance_upgrade_mount] = 3,
    [TabIndex.new_appearance_kun_upstar] = 6,
}

function NewAppearanceWGData:InitAdvancedCfg()
    local upgrade_cfg = ConfigManager.Instance:GetAutoConfig("upgrade_auto")
    self.advanced_uplevel_cfg = ListToMap(upgrade_cfg.up_level, "type", "level")
    self.advanced_uplevel_stuff_cfg = ListToMapList(upgrade_cfg.item, "type")
    self.advanced_skill_cfg = ListToMap(upgrade_cfg.skill, "type", "skill_id", "skill_level")
    self.advanced_sxd_cfg = ListToMap(upgrade_cfg.shuxingdan, "type", "slot_idx")
    self.advanced_sxd_map_itemid_cfg = ListToMap(upgrade_cfg.shuxingdan, "shuxingdan_id")
    self.advanced_right_cfg = upgrade_cfg.privilege
    self.advanced_right_map_type_cfg = {}
    for k, v in pairs(upgrade_cfg.privilege) do
        self.advanced_right_map_type_cfg[v.type] = v
    end


    local rm = RemindManager.Instance
	rm:Register(RemindName.NewAppearance_Upgrade_Wing, BindTool.Bind(self.GetAdvancedAllRemind, self, TabIndex.new_appearance_upgrade_wing))
    rm:Register(RemindName.NewAppearance_Upgrade_FaBao, BindTool.Bind(self.GetAdvancedAllRemind, self, TabIndex.new_appearance_upgrade_fabao))
    rm:Register(RemindName.NewAppearance_Upgrade_ShenBing, BindTool.Bind(self.GetAdvancedAllRemind, self, TabIndex.new_appearance_upgrade_shenbing))
    rm:Register(RemindName.NewAppearance_Upgrade_JianZhen, BindTool.Bind(self.GetAdvancedAllRemind, self, TabIndex.new_appearance_upgrade_jianzhen))
    
    self:GetAdvancedStuffRemindList()
end

function NewAppearanceWGData:DeleteAdvancedData()
    local rm = RemindManager.Instance
    rm:UnRegister(RemindName.NewAppearance_Upgrade_Wing)
    rm:UnRegister(RemindName.NewAppearance_Upgrade_FaBao)
    rm:UnRegister(RemindName.NewAppearance_Upgrade_ShenBing)
    rm:UnRegister(RemindName.NewAppearance_Upgrade_JianZhen)
end

-- 进阶 - 升级
function NewAppearanceWGData:GetAdvancedUplevelCfg(type, level)
    return (self.advanced_uplevel_cfg[type] or empty_table)[level]
end

function NewAppearanceWGData:GetAdvancedUplevelStuffList(type)
    return self.advanced_uplevel_stuff_cfg[type] or {}
end

-- 进阶 - 技能
function NewAppearanceWGData:GetAdvancedSkillCfg(type, skill_id, skill_level)
    return ((self.advanced_skill_cfg[type] or empty_table)[skill_id] or empty_table)[skill_level]
end

-- 进阶 - 技能名
function NewAppearanceWGData:GetAdvancedSkillName(type, skill_id)
    local cfg = self:GetAdvancedSkillCfg(type, skill_id, 1)
    return cfg and cfg.skill_name or ""
end

-- 进阶 - 属性丹 by 物品id
function NewAppearanceWGData:GetAdvancedSXDCfgByItemId(item_id)
    return self.advanced_sxd_map_itemid_cfg[item_id]
end

function NewAppearanceWGData:GetIsAdvancedSXD(item_id)
	return self.advanced_sxd_map_itemid_cfg[item_id] ~= nil
end

-- 进阶 - 形象展示
function NewAppearanceWGData:GetAdvancedImageCfgByType(type)
	local image_cfg = ConfigManager.Instance:GetAutoConfig("upgrade_auto").image
	local prof = RoleWGData.Instance:GetRoleProf()
	local cfg
	for k, v in ipairs(image_cfg) do
		if v.type == type then
			if cfg == nil and v.prof == 5 then
				cfg = v
			elseif v.prof == prof then
				cfg = v
                break
			end
		end
	end

	return cfg or {}
end




function NewAppearanceWGData:SetUpgradeInfo(protocol)
    local upgrade_param = protocol.upgrade_param

    -- 新技能激活
	for type, v in pairs(self.advanced_info) do
        local new_skill_level_list = upgrade_param[type].skill_level_list
		for skill_id, v2 in pairs(v.skill_level_list) do
			if v2 == 0 and new_skill_level_list[skill_id] > v2 then
				local cfg = self:GetAdvancedSkillCfg(type, skill_id, 1)
				if cfg then
					local data = {name = cfg.skill_name, desc = cfg.skill_describe, res_fun = ResPath.GetSkillIconById, icon = cfg.skill_icon}
					TipWGCtrl.Instance:ShowGetNewSkillView2(data)
					break
				end
			end
		end
	end

    self.advanced_info = upgrade_param
    self.appearance_hide_flag = protocol.appearance_hide_flag
    self.advanced_right_flag = protocol.right_flag
end

-- 进阶信息
function NewAppearanceWGData:GetAdvancedInfo(type)
    return self.advanced_info[type]
end

-- 进阶信息 - 等级
function NewAppearanceWGData:GetAdvancedLevel(type)
    return (self.advanced_info[type] or empty_table)["level"] or 0
end

-- 进阶信息 - 使用形象
function NewAppearanceWGData:GetAdvancedUsedAppedID(type)
    return (self.advanced_info[type] or empty_table)["use_image_id"] or -1
end

-- 进阶信息 - 技能激活
function NewAppearanceWGData:GetAdvancedSkillIsAct(type, skill_id)
	return ((self.advanced_info[type] or empty_table)["active_skill_flag"] or empty_table)[skill_id] == 1
end

-- 进阶信息 - 技能等级
function NewAppearanceWGData:GetAdvancedSkillLevel(type, skill_id)
	return ((self.advanced_info[type] or empty_table)["skill_level_list"] or empty_table)[skill_id] or 0
end

-- 进阶信息 - 属性丹使用数
function NewAppearanceWGData:GetAdvancedSXDUsedNum(type, slot_idx)
	return ((self.advanced_info[type] or empty_table)["shuxingdan_list"] or empty_table)[slot_idx] or 0
end

-- 外观隐藏
function NewAppearanceWGData:GetAppearanceIsHide(type)
	return self.appearance_hide_flag[type] == 1
end

-- 特权是否激活
function NewAppearanceWGData:GetAdvancedRightFlag(seq)
	return self.advanced_right_flag[seq] == 1
end

-- 特权配置
function NewAppearanceWGData:GetAdvancedRightCfgByType(type)
    return self.advanced_right_map_type_cfg[type]
end

-- 获取特权是否激活
function NewAppearanceWGData:GetAdvancedRightIsActByType(type)
    local cfg = self:GetAdvancedRightCfgByType(type)
    if cfg then
        return self:GetAdvancedRightFlag(cfg.seq)
    end

    return false
end

-- 技能列表
function NewAppearanceWGData:GetAdvancedSkillList(type)
    local skill_list = {}
    local cfg_list = self.advanced_skill_cfg[type]
    if not cfg_list then
        return skill_list
    end

    local level = self:GetAdvancedLevel(type)
    local skill_level, cfg_level = 0, 0
    for skill_id = 0, #cfg_list do
        skill_level = self:GetAdvancedSkillLevel(type, skill_id)
        cfg_level = skill_level > 0 and skill_level or 1
        local cfg = cfg_list[skill_id][cfg_level]
        if cfg then
            local is_remind = false
            local next_skill_cfg = self:GetAdvancedSkillCfg(type, skill_id, skill_level + 1)
            if next_skill_cfg then
                local uplevel_item_id = next_skill_cfg.uplevel_item_id
                local had_num = ItemWGData.Instance:GetItemNumInBagById(uplevel_item_id)
                is_remind = had_num >= next_skill_cfg.uplevel_item_num
            end

            local data = {cfg = cfg}
            data.skill_id = cfg.skill_id
            data.skill_level = cfg_level
            data.is_act = level >= cfg.active_level
            data.ad_type = type
            data.is_remind = is_remind and data.is_act
            table.insert(skill_list, data)
        end
    end

    return skill_list
end

-- 属性丹列表
function NewAppearanceWGData:GetAdvancedSXDlist(type)
    local sxd_cfg = self.advanced_sxd_cfg[type] or {}
    local list = {}
    if sxd_cfg == nil then
        return list
    end

    local level = self:GetAdvancedLevel(type)
    for slot = 0, #sxd_cfg do
        local cfg = sxd_cfg[slot]
        local data = {cfg = cfg}
        data.is_open = cfg.open_level <= level
        data.used_num = self:GetAdvancedSXDUsedNum(type, slot)
        data.had_num = ItemWGData.Instance:GetItemNumInBagById(cfg.shuxingdan_id)
        data.is_remind = data.had_num > 0 and data.is_open
        data.ad_type = type
        table.insert(list, data)
    end

    return list
end

-- 基础骑宠 属性列表 / 战力
function NewAppearanceWGData:GetAdvancedAttrListAndCap(type, level)
    local attr_list = {}
    local capability = 0
    local cur_uplevel_cfg = self:GetAdvancedUplevelCfg(type, level)
    local next_uplevel_cfg = self:GetAdvancedUplevelCfg(type, level + 1)
    local no_max = next_uplevel_cfg ~= nil
    if cur_uplevel_cfg == nil then
        return attr_list, capability
    end

    local get_attr_list_func = function (cur_list, next_list)
        local list = {}
        local cur_no_data = IsEmptyTable(cur_list)
        local temp_list = cur_no_data and next_list or cur_list
        for k, v in pairs(temp_list) do
            local data = {}
            data.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k, true)
            local cur_value = cur_no_data and 0 or v
            local next_value = next_list[k] or 0
            local is_per = EquipmentWGData.Instance:GetAttrIsPer(k)
            local per_desc = is_per and "%" or ""
            if next_value > 0 then
                next_value = next_value - cur_value
                next_value = is_per and string.format("%.2f", next_value / 100) or math.floor(next_value)
                data.add_value = tonumber(next_value) .. per_desc
            end

            cur_value = is_per and string.format("%.2f", cur_value / 100) or math.floor(cur_value)
            data.cur_value = tonumber(cur_value) .. per_desc
            
            data.attr_sort = AttributeMgr.GetSortAttributeIndex(k)
            table.insert(list, data)
        end

        table.sort(list, SortTools.KeyLowerSorter("attr_sort"))
        return list
    end

    local cur_list, next_list = {}, {}
    local get_add_attr_func = function (list, map_list, cfg, beishu)
        if not list or not map_list or not cfg then
            return
        end

        beishu = beishu or 1
        for k,v in pairs(map_list) do
            local value = cfg[k]
            if value > 0 then
                local is_per = EquipmentWGData.Instance:GetAttrIsPer(v)
                if not is_per then
                    value = value * beishu
                end

                list[v] = list[v] and list[v] + value or value
            end
        end
    end

    local sxd_list = self:GetAdvancedSXDlist(type)
    local add_per = 1
    if sxd_list then
        for k,v in ipairs(sxd_list) do
            if v.is_open and v.used_num > 0 and v.cfg and v.cfg.attr_per > 0 then
                add_per = add_per + (v.cfg.attr_per / 10000 * v.used_num)
            end
        end
    end

    -- 扩展属性丹百分比
    local sxd_extend_list = self:GetAdvancedSXDExtendlist(type)
    if sxd_extend_list then
        for k,v in ipairs(sxd_extend_list) do
            if v.is_open and v.used_num > 0 and v.cfg and v.cfg.attr_per > 0 then
                add_per = add_per + (v.cfg.attr_per / 10000 * v.used_num)
            end
        end
    end

    -- 特权加成
    local right_is_act = self:GetAdvancedRightIsActByType(type)
    local right_cfg = self:GetAdvancedRightCfgByType(type)
    if right_is_act and right_cfg then
        add_per = add_per + (right_cfg.attr_per / 10000)
    end

    --藏金百分比
    local tequan_info = YanYuGeWGData.Instance:GetShowTeQuanList()
    if not IsEmptyTable(tequan_info) then
        for k, v in ipairs(tequan_info) do
            if v.act_flag == 1 then
                add_per = add_per + (v.cfg.appe_base_add_per / 10000)
            end
        end
    end

    --王者特权万分比
    local king_data = RechargeWGData.Instance:GetCurKingVipAddition()
    local is_active = RechargeWGCtrl.Instance:GetKingVipIsActive()
	if not IsEmptyTable(king_data) and is_active then
        local king_vip_index = 0
        if type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING then
            king_vip_index = 0
        elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO then
            king_vip_index = 1
        elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING then
            king_vip_index = 2
        elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN then
            king_vip_index = 3
        end

        local attr_add = king_data["add_" .. king_vip_index]
		add_per = add_per + (attr_add / 10000)
	end

    --VIP周卡万分比.
    local remain_time = RechargeWGData.Instance:GetWeekCardRemainTime()
    if remain_time > 0 then
        local addition_idx = 0
        if type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_WING then
            addition_idx = VIP_WEEK_CARD_GRADE_ADDITION_TYPE.WEEK_CARD_GRADE_ADDITION_TYPE_WING
        elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_FABAO then
            addition_idx = VIP_WEEK_CARD_GRADE_ADDITION_TYPE.WEEK_CARD_GRADE_ADDITION_TYPE_FABAO
        elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_SHENBING then
            addition_idx = VIP_WEEK_CARD_GRADE_ADDITION_TYPE.WEEK_CARD_GRADE_ADDITION_TYPE_SHENBING
        elseif type == ADVANCED_UPGRADE_TYPE.UPGRADE_TYPE_JIANZHEN then
            addition_idx = VIP_WEEK_CARD_GRADE_ADDITION_TYPE.WEEK_CARD_GRADE_ADDITION_TYPE_JIANZHEN
        end

        local attr_add = RechargeWGData.Instance:GetWeekCardAdditionByIndex(addition_idx)
		add_per = add_per + attr_add
    end

    -- 骚策划技能也要加百分比技能百分比
    local skill_list = self:GetAdvancedSkillList(type)
    for k,v in pairs(skill_list) do
        if v.is_act then
            local attr_add = v.cfg.attr_per
            if attr_add and attr_add > 0 then
                add_per = add_per + (attr_add / 10000)
            end
        end
    end

    -- 骚策划还说天道石也要加百分比
    -- 天道石影响属性百分比,不影响百分比
    local charm_type = self:TransformAdvancedTypeToCharmType(type)
    local add_rate = CultivationWGData.Instance:GetAddRateByType(charm_type)
    add_per = add_per + (add_rate / 10000)

    local uplevel_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_uplevel_cfg)
    get_add_attr_func(cur_list, uplevel_cfg_map_attr_list, cur_uplevel_cfg, add_per)
    if no_max then
        get_add_attr_func(next_list, uplevel_cfg_map_attr_list, next_uplevel_cfg, add_per)
    end

    attr_list = get_attr_list_func(cur_list, next_list)

    -- 特权加成属性显示
    if right_cfg then
        local right_add_per_temp = {}
        right_add_per_temp.attr_name = ToColorStr(Language.Advanced.RightAttrStr, COLOR3B.GLOD)
        right_add_per_temp.cur_value = right_cfg.attr_per / 100 .. "%"
        right_add_per_temp.is_per = false
        right_add_per_temp.is_lock = not right_is_act
        right_add_per_temp.show_effect = true
        right_add_per_temp.hide_arrow = true
        if not right_is_act then
            right_add_per_temp.add_value = Language.Advanced.GoToAct
        end

        local is_has_cost = false
        local has_num = ItemWGData.Instance:GetItemNumInBagById(right_cfg.cost_item_id)
        is_has_cost = has_num >= right_cfg.cost_item_num
        right_add_per_temp.is_remind = not right_is_act and is_has_cost
        if not right_is_act and right_cfg then
            right_add_per_temp.click_func = function ()
                if right_cfg then
                    local btn_callback_event = {}
                    if not right_is_act and is_has_cost then
                        btn_callback_event[1] = {btn_text = Language.Tip.ButtonLabel[2], callback = function()
                            if right_cfg then
                                local index = ItemWGData.Instance:GetItemIndex(right_cfg.cost_item_id)
                                BagWGCtrl.Instance:SendUseItem(index, right_cfg.cost_item_num, 0, 0)
                            end
                        end
                        }
                    end

                    TipWGCtrl.Instance:OpenItem({item_id = right_cfg.cost_item_id}, nil, nil, nil, btn_callback_event, true)
                end
            end
        end
        table.insert(attr_list, right_add_per_temp)
    end

    -- 技能战力
    capability = capability + self:GetAdvancedSkillListBaseCap(type)
    local attribute = AttributePool.AllocAttribute()
    -- 属性丹基础属性不影响进阶属性显示
    if sxd_list then
        local sxd_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(sxd_list[1].cfg)
        for k,v in ipairs(sxd_list) do
            if v.is_open and v.used_num > 0 then
                get_add_attr_func(cur_list, sxd_cfg_map_attr_list, v.cfg, v.used_num)
            end
        end
    end

    for k,v in pairs(cur_list) do
        if attribute[k] then
            attribute[k] = attribute[k] + v
        end
    end

    capability = capability + AttributeMgr.GetCapability(attribute)
    return attr_list, capability
end

-- 获取 进阶类型技能基础战力
function NewAppearanceWGData:GetAdvancedSkillListBaseCap(type)
    local capability = 0

    local skill_list = self:GetAdvancedSkillList(type)
    for k,v in pairs(skill_list) do
        if v.is_act then
            capability = capability + self:GetSingleSkillCap(v.cfg)
        end
    end

    return capability
end

-- 进阶 升级红点
function NewAppearanceWGData:GetAdvancedUpLevelRemind(type)
    local info = self:GetAdvancedInfo(type)
    if info == nil then
        return false
    end

    local next_uplevel_cfg = self:GetAdvancedUplevelCfg(type, info.level + 1)
    if not next_uplevel_cfg then
        return false
    end

    local cur_uplevel_cfg = self:GetAdvancedUplevelCfg(type, info.level)
    if not cur_uplevel_cfg then
        return false
    end

    local uplevel_exp_val = info.uplevel_exp_val
    local need_exp = cur_uplevel_cfg.need_exp - uplevel_exp_val
    local stuff_list = self:GetAdvancedUplevelStuffList(type)
    local add_exp_sum = 0
    for k, v in pairs(stuff_list) do
        local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
        add_exp_sum = add_exp_sum + (item_num * v.add_exp)
        if add_exp_sum >= need_exp then
            return true
        end
    end

    return false
end

-- 进阶 - 全部红点
function NewAppearanceWGData:GetAdvancedAllRemind(tab_index)
    local tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
    if not tab_data then
        return 0
    end

    -- 判断功能开放
    local fun_name = tab_data.fun_name or FunName.NewAppearanceWGView
    if not FunOpen.Instance:GetFunIsOpened(fun_name) then
        return 0
    end

    local tip_type = tab_data.strengthen_type
    local ad_type = tab_data.ad_type
    local is_remind = self:GetAdvancedUpLevelRemind(ad_type)
    if not is_remind then
        local sxd_list = self:GetAdvancedSXDlist(ad_type)
        for k,v in ipairs(sxd_list) do
            if v.is_remind then
                is_remind = true
                break
            end
        end
    end

    if not is_remind then
        local skill_list = self:GetAdvancedSkillList(ad_type)
        for k,v in ipairs(skill_list) do
            if v.is_remind then
                is_remind = true
                break
            end
        end
    end

    if not is_remind then
        if AppearanceActIndexList[tab_index] then
            local rush_type = AppearanceActIndexList[tab_index]
            local is_act_open = NewAppearanceWGData.Instance:CheckBPActIsOpenByRushType(rush_type)
            local show_red_point = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(rush_type)
            if show_red_point and is_act_open then
                is_remind = true
            end
        end
    end

    if not is_remind then
        if self:GetAdvancedSXDExtendRemind(ad_type) then
            is_remind = true
        end
    end

    if not is_remind then
        if self:GetAdvancedRightRemind(ad_type) then
            is_remind = true
        end
    end

    if is_remind then
        MainuiWGCtrl.Instance:InvateTip(tip_type, 1, function ()
            FunOpen.Instance:OpenViewByName(GuideModuleName.NewAppearanceWGView, tab_index)
            return true
        end)
        return 1
    end

    MainuiWGCtrl.Instance:InvateTip(tip_type, 0)
    return 0
end

-- 特权红点
function NewAppearanceWGData:GetAdvancedRightRemind(type)
    local right_is_act = self:GetAdvancedRightIsActByType(type)
    if right_is_act then
        return false
    end

    local right_cfg = self:GetAdvancedRightCfgByType(type)
    if right_cfg then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(right_cfg.cost_item_id)
        return has_num >= right_cfg.cost_item_num
    end

    return false
end

-- 珍稀坐骑材料
function NewAppearanceWGData:GetAdvancedStuffRemindList()
    self.advanced_stuff_remind_list = {}
    -- 升级消耗
    local tab_index, tab_data, remind_name, item_id
    for type, list in pairs(self.advanced_uplevel_stuff_cfg) do
        tab_index = ADVANCED_TYPE_2_TABINDEX[type]
        tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
        if tab_data then
            remind_name = tab_data.remind_name
            for k,v in pairs(list) do
                item_id = v.item_id
                if not self.advanced_stuff_remind_list[item_id] then
                    self.advanced_stuff_remind_list[item_id] = {}
                end

                self.advanced_stuff_remind_list[item_id][remind_name] = true
            end
        end
    end

    -- 属性丹
    for type, list in pairs(self.advanced_sxd_cfg) do
        tab_index = ADVANCED_TYPE_2_TABINDEX[type]
        tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
        if tab_data then
            remind_name = tab_data.remind_name
            for k,v in pairs(list) do
                item_id = v.shuxingdan_id
                if not self.advanced_stuff_remind_list[item_id] then
                    self.advanced_stuff_remind_list[item_id] = {}
                end

                self.advanced_stuff_remind_list[item_id][remind_name] = true
            end
        end
    end

    -- 技能升级
    for type, id_list in pairs(self.advanced_skill_cfg) do
        tab_index = ADVANCED_TYPE_2_TABINDEX[type]
        tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
        if tab_data then
            remind_name = tab_data.remind_name
            for id, level_list in pairs(id_list) do
                for skill_level, level_cfg in pairs(level_list) do
                    item_id = level_cfg.uplevel_item_id
                    if not self.advanced_stuff_remind_list[item_id] then
                        self.advanced_stuff_remind_list[item_id] = {}
                    end

                    self.advanced_stuff_remind_list[item_id][remind_name] = true
                end
            end
        end
    end

    -- 属性丹扩展
    for type, list in pairs(self:GetUpGradeStoreCfg()) do
        tab_index = ADVANCED_TYPE_2_TABINDEX[type]
        tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
        if tab_data then
            remind_name = tab_data.remind_name
            for k,v in pairs(list) do
                if v.is_show == 1 then
                    item_id = v.shuxingdan_id
                    if not self.advanced_stuff_remind_list[item_id] then
                        self.advanced_stuff_remind_list[item_id] = {}
                    end

                    self.advanced_stuff_remind_list[item_id][remind_name] = true
                end
            end
        end
    end

    -- 特权加成
    for k, v in pairs(self.advanced_right_cfg) do
        tab_index = ADVANCED_TYPE_2_TABINDEX[v.type]
        tab_data = NEW_APPEARANCE_TAB_DATA[tab_index]
        remind_name = tab_data and tab_data.remind_name
        if remind_name then
            item_id = v.cost_item_id
            if not self.advanced_stuff_remind_list[item_id] then
                self.advanced_stuff_remind_list[item_id] = {}
            end

            self.advanced_stuff_remind_list[v.cost_item_id][remind_name] = true
        end
    end
end

function NewAppearanceWGData:CheckAdvancedStuffRemind(item_id)
    local remind_list = self.advanced_stuff_remind_list[item_id]
    if not remind_list then
        return false
    end

    for k,v in pairs(remind_list) do
        RemindManager.Instance:Fire(k)
    end

    return true
end

function NewAppearanceWGData:CheckBPActIsOpenByRushType(rush_type)
	local role_level = RoleWGData.Instance:GetAttr('level')
    local level_limit = self:GetFashionOtherCfgByKey("level_show2") or 0

    if role_level < level_limit then
        return false
    end

    local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
    if not opengame_cfg then
        return false, false
    end

    local close_day = opengame_cfg.close_day_index
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local format_time = os.date("*t", now_time)
    local end_time = os.time({
        year = format_time.year,
        month = format_time.month,
        day = format_time.day + close_day - open_day + 1,
        hour = 0,
        min = 0,
        sec = 0
    })
    
    return now_time <= end_time and open_day >= opengame_cfg.open_day_index
end