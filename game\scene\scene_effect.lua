Scene = Scene or BaseClass(BaseWGCtrl)

-- ==================================================================================
-- ====================================== 场景效果 ===================================
-- ==================================================================================
function Scene:InitSceneEffectLua()
    self:BindGlobalEvent(ObjectEventType.BE_SELECT, BindTool.Bind(self.OnSelectObjHead, self))
    self:BindGlobalEvent(ObjectEventType.CANCEL_SELECT, BindTool.Bind(self.OnCancelSelect, self))

    self.close_select_effect = false
    self:BindGlobalEvent(SettingEventType.CLOSE_SELECT_EFFECT,
    BindTool.Bind(self.SettingChange, self, SETTING_TYPE.CLOSE_SELECT_EFFECT))
end

function Scene:DeleteScenceEffectLua()

end

function Scene:OnSelectObjHead(target_obj, select_type)
    if self.close_select_effect or nil == target_obj or target_obj:IsDeleted() then
		return
	end

    local obj_type = target_obj:GetType()
    if obj_type == SceneObjType.MainRole then
        return
    end

    if obj_type ~= SceneObjType.Role and obj_type ~= SceneObjType.Monster then
        return
    end

    local monster_type = target_obj.vo.monster_type or 0
    if monster_type ~= SCENE_MONSTER_TYPE.MONSTER_TYPE_NORMAL and monster_type ~= SCENE_MONSTER_TYPE.MONSTER_TYPE_BOSS then
        return
    end

    local target_obj_id = target_obj:GetObjId()
    if self.main_role then
        self.main_role:AddSelectObjLine(target_obj_id)
    end
end

function Scene:OnCancelSelect(target_obj)
    if self.close_select_effect then
        return
    end

    if nil == target_obj or target_obj:IsDeleted() then
        return
    end

    if self.main_role then
        self.main_role:ClearSelectObjLine()
    end
end

function Scene:SettingChange(setting_type, switch)
    if setting_type == SETTING_TYPE.CLOSE_SELECT_EFFECT then
        self.close_select_effect = switch
        if switch then
            if self.main_role then
                self.main_role:ClearSelectObjLine()
            end
        else
            if GuajiCache.target_obj ~= nil then
                self:OnSelectObjHead(GuajiCache.target_obj, SceneTargetSelectType.SELECT)
            end
        end
    end
end

