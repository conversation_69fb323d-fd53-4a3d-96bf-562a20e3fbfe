﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

namespace HUDProgramme
{
    public class HUDConfig
    {
        public static string textAssetsPath = "Assets/Game/UIs/HUDProgramme/Atlas/assets_all.txt";
        public static string szCfgPathName = Application.dataPath + "/Game/UIs/HUDProgramme/Atlas/assets_all.txt";
        public static string szCfgBytePathName = Application.dataPath + "/Game/UIs/HUDProgramme/Atlas/assets_byte_all.bytes";
        public static string szAtlasPathName = "Assets/Game/UIs/HUDProgramme/Atlas/{0}.png";
        public static string szAtlasPath = "Assets/Game/UIs/HUDProgramme/Atlas/";
    }

    struct RectInt
    {
        public int left;
        public int top;
        public int right;
        public int bottom;
        static RectInt s_zero;
        //public RectInt()
        //{
        //    left = top = right = bottom = 0;
        //}
        public RectInt(int nLeft, int nTop, int nRight, int nBottom)
        {
            left = nLeft;
            top = nTop;
            right = nRight;
            bottom = nBottom;
        }
        public RectInt(Rect rc)
        {
            left = (int)rc.xMin;
            top = (int)rc.yMin;
            right = (int)(rc.xMax + 0.5f);
            bottom = (int)(rc.yMax + 0.5f);
        }
        static RectInt zero
        {
            get
            {
                if (s_zero == null)
                    s_zero = new RectInt(0, 0, 0, 0);
                return s_zero;
            }
        }

        public override bool Equals(object obj)
        {
            if (obj.GetType() == typeof(RectInt))
            {
                return Equals((RectInt)obj);
            }
            return false;
        }
        public bool Equals(RectInt other)
        {
            return left == other.left && top == other.top && right == other.right && bottom == other.bottom;
        }
        public override int GetHashCode()
        {
            return base.GetHashCode();
        }

        public static bool operator !=(RectInt lhs, RectInt rhs)
        {
            return lhs.left != rhs.left || lhs.top != rhs.top || lhs.right != rhs.right || lhs.bottom != rhs.bottom;
        }
        public static bool operator ==(RectInt lhs, RectInt rhs)
        {
            return lhs.left == rhs.left && lhs.top == rhs.top && lhs.right == rhs.right && lhs.bottom == rhs.bottom;
        }
        // 功能：左右交换，上下交换
        public void swap()
        {
            int nTemp = left; left = right; right = nTemp;
            nTemp = top; top = bottom; bottom = nTemp;
        }
        public void SetRect(int nLeft, int nTop, int nRight, int nBottom)
        {
            left = nLeft;
            top = nTop;
            right = nRight;
            bottom = nBottom;
        }
        public void SetRect(Rect rc)
        {
            left = (int)rc.xMin;
            top = (int)rc.yMin;
            right = (int)(rc.xMax + 0.5f);
            bottom = (int)(rc.yMax + 0.5f);
        }

        // 功能：合并一个坐标点，扩大四边形
        public void unitPoint(int x, int y)
        {
            if (left > x)
                left = x;
            if (right < x + 1)
                right = x + 1;
            if (top > y)
                top = y;
            if (bottom < y + 1)
                bottom = y + 1;
        }
        // 功能：求交
        static public RectInt clipRect(RectInt a, RectInt b)
        {
            RectInt r = RectInt.zero;
            r.left = a.left < b.left ? b.left : a.left;
            r.top = a.top < b.top ? b.top : a.top;
            r.right = a.right < b.right ? a.right : b.right;
            r.bottom = a.bottom < b.bottom ? a.bottom : b.bottom;
            if (r.right < r.left || r.bottom < r.top)
            {
                r.left = r.right = r.top = r.bottom = 0;
            }
            return r;
        }
        public int width
        {
            get { return right - left; }
            set { right = left + value; }
        }
        public int height
        {
            get { return bottom - top; }
            set { bottom = top + value; }
        }
    };

    public struct SUpdateTexInfo
    {
        public string m_szAssetsName;   // 资源目录
        public string m_szSpriteName;   // 精灵名字
        public string m_szAtlasName;    // 材质名字
    };

    // 单个纹理材质
    public class UITexAtlas
    {
        public enum Coordinates
        {
            Pixels,
            TexCoords,
        }

        public string m_szAtlasName = "Input name";     // 材质名字
        public string m_szTexName = "";                 // 纹理名字
        public string m_szShaderName = "";              // shader名字
        public Material m_material;                     // 材质
        public Texture m_MainAlpha;                     // 主贴图的通道图
        public int m_nAtlasID = 0;                      // 材质ID


        Coordinates m_Coordinates = Coordinates.Pixels;
        int m_PixelSize = 1;
        int m_PMA = -1;
        int m_nTexWidth = 1;   // 纹理的宽度
        int m_nTexHeight = 1;  // 纹理的高度

        bool m_bCanLOD = false; // 是不是可以LOD缩放

        // 以下时临时变量
        public int m_nRef;                              // 引用计数
        public int m_nSpriteNumb;                       // 精寻对象数量
        public int m_nVersion;                          // 当前修改的版本号(有修改就改变)
        public bool m_bDirty;                           // 脏了，需要修改
        public float m_fReleaseTime;                    // 释放时间
        public bool m_bLoading = false;                 // 是不是正在加载中
        public bool m_bAddAssetBundleRef = false;       // 是不是添加了AssetBundle的引用计数
        public string m_szMainResName;
        public string m_szAlphaResName;
        public float m_fLoadingTime = 0.0f;             // 上一次加载的时间
        public AssetBundle m_mainBundle;
        public AssetBundle m_mainAlphaBundle;

        public delegate void OnLoadAtlas();
        public OnLoadAtlas m_lpOnLoadAtlas;             // 加载纹理成功后的事件，因为是异步的操作


        public void CopyFromSetting(UITexAtlas from)
        {
            m_Coordinates = from.m_Coordinates;
            m_PixelSize = from.m_PixelSize;
            m_PMA = from.m_PMA;
        }

        public Texture MainAlphaTexture
        {
            get { return m_MainAlpha != null ? m_MainAlpha : mainTexture; }
        }
        public Texture mainTexture
        {
            get { if (m_material != null) return m_material.mainTexture; else return null; }
        }

        public void SetTextureSizeByMaterial(Material mat)
        {
            Texture tex = mat != null ? mat.mainTexture : null;
            SetTextureSizeByTexture(tex);
            if (mat != null && mat.shader != null)
                m_szShaderName = mat.shader.name;
        }

        public void SetTextureSizeByTexture(Texture tex)
        {
            if (tex != null)
            {
                m_nTexWidth = tex.width;
                m_nTexHeight = tex.height;
            }
            else
            {
                m_nTexWidth = m_nTexHeight = 1;
            }
        }
        public int texWidth
        {
            get { return m_nTexWidth; }
        }
        public int texHeight
        {
            get { return m_nTexHeight; }
        }
        public Coordinates coordinates
        {
            get
            {
                return m_Coordinates;
            }
            set
            {
                m_Coordinates = value;
            }
        }
        public int pixelSize
        {
            get
            {
                return m_PixelSize;
            }
            set
            {
                m_PixelSize = value;
            }
        }
        public bool premultipliedAlpha
        {
            get
            {
                if (m_PMA == -1)
                {
                    Material mat = m_material;
                    m_PMA = (mat != null && mat.shader != null && mat.shader.name.Contains("Premultiplied")) ? 1 : 0;
                }
                return (m_PMA == 1);
            }
        }
        public bool IsCanLOD()
        {
            return m_bCanLOD;
        }
        public void SetLODFlag(bool bCanLOD)
        {
            m_bCanLOD = bCanLOD;
        }
        public void AdjustAtlas(UITexAtlas other)
        {
            m_szAtlasName = other.m_szAtlasName;
            m_szTexName = other.m_szTexName;
            m_nAtlasID = other.m_nAtlasID;

            m_szShaderName = other.m_szShaderName;
            m_PixelSize = other.m_PixelSize;
            m_Coordinates = other.m_Coordinates;
            m_nTexWidth = other.m_nTexWidth;
            m_nTexHeight = other.m_nTexHeight;
            m_bCanLOD = other.m_bCanLOD;
        }

        public void Serailize(ref CSerialize ar)
        {
            int nCoordinatesType = (int)m_Coordinates;
            ar.ReadWriteValue(ref m_szAtlasName);
            ar.ReadWriteValue(ref m_szTexName);
            ar.ReadWriteValue(ref nCoordinatesType);
            ar.ReadWriteValue(ref m_PixelSize);
            m_Coordinates = nCoordinatesType == (int)Coordinates.Pixels ? Coordinates.Pixels : Coordinates.TexCoords;
            ar.ReadWriteValue(ref m_nTexWidth);
            ar.ReadWriteValue(ref m_nTexHeight);
            if (ar.GetVersion() >= 1)
            {
                ar.ReadWriteValue(ref m_nAtlasID);
            }
            if (ar.GetVersion() >= 2)
            {
                ar.ReadWriteValue(ref m_szShaderName);
            }
            if (ar.GetVersion() >= 4)
            {
                ar.ReadWriteValue(ref m_bCanLOD);
            }
        }
        public void SerializeToTxt(ref SerializeText ar)
        {
            int nCoordinatesType = (int)m_Coordinates;
            ar.ReadWriteValue("AtlasName", ref m_szAtlasName);
            ar.ReadWriteValue("TexName", ref m_szTexName);
            ar.ReadWriteValue("Coordinates", ref nCoordinatesType);
            ar.ReadWriteValue("PixelSize", ref m_PixelSize);
            m_Coordinates = nCoordinatesType == (int)Coordinates.Pixels ? Coordinates.Pixels : Coordinates.TexCoords;
            ar.ReadWriteValue("texWidth", ref m_nTexWidth);
            ar.ReadWriteValue("texHeight", ref m_nTexHeight);
            if (ar.GetVersion() >= 1)
            {
                ar.ReadWriteValue("AtlasID", ref m_nAtlasID);
            }
            if (ar.GetVersion() >= 2)
            {
                ar.ReadWriteValue("ShaderName", ref m_szShaderName);
            }
            if (ar.GetVersion() >= 4)
            {
                ar.ReadWriteValue("CanScale", ref m_bCanLOD);
            }
        }
    }

    public class UISpriteInfo  // 兼容NGUI的Sprite对象，将Sprite成员放到这里来
    {
        public string name = "Unity Bug";   // 对象的名字
        public Rect outer = new Rect(0f, 0f, 1f, 1f);     // 外框，精灵的实际大小（在纹理的像素坐标)
        public Rect inner = new Rect(0f, 0f, 1f, 1f);     // 内框，用来做填充模式时的像素坐标，这个必须是在外框之内的
        public bool rotated = false;

        // Padding is needed for trimmed sprites and is relative to sprite width and height
        public float paddingLeft = 0f;   // 用来做精灵图层选择时扩展选择框范围的东东，没有实际意义
        public float paddingRight = 0f;
        public float paddingTop = 0f;
        public float paddingBottom = 0f;

        // 下面是扩展属性
        public int m_nNameID;   // 精灵ID
        public int m_nAtlasID;  // 材质ID
        public string m_szAtlasName;  // 对应的材质名字

        public bool hasPadding { get { return paddingLeft != 0f || paddingRight != 0f || paddingTop != 0f || paddingBottom != 0f; } }

        public UISpriteInfo Clone()
        {
            UISpriteInfo p = new UISpriteInfo();
            p.Copy(this);
            return p;
        }

        // 功能：拷贝对象 
        public void Copy(UISpriteInfo src)
        {
            name = src.name.Clone() as string;
            outer = new Rect(src.outer.xMin, src.outer.yMin, src.outer.width, src.outer.height);
            inner = new Rect(src.inner.xMin, src.inner.yMin, src.inner.width, src.inner.height);
            rotated = src.rotated;
            paddingLeft = src.paddingLeft;
            paddingRight = src.paddingRight;
            paddingTop = src.paddingTop;
            paddingBottom = src.paddingBottom;
            m_nNameID = src.m_nNameID;
            m_nAtlasID = src.m_nAtlasID;
            m_szAtlasName = src.m_szAtlasName.Clone() as string;
        }
        public void Serailize(ref CSerialize ar)
        {
            ar.ReadWriteValue(ref name);
            ar.ReadWriteValue(ref outer);
            ar.ReadWriteValue(ref inner);
            ar.ReadWriteValue(ref rotated);
            ar.ReadWriteValue(ref paddingLeft);
            ar.ReadWriteValue(ref paddingRight);
            ar.ReadWriteValue(ref paddingTop);
            ar.ReadWriteValue(ref paddingBottom);
            ar.ReadWriteValue(ref m_szAtlasName);
            if (ar.GetVersion() >= 1)
            {
                ar.ReadWriteValue(ref m_nNameID);
                ar.ReadWriteValue(ref m_nAtlasID);
            }
        }
        public void SerializeToTxt(ref SerializeText ar)
        {
            ar.ReadWriteValue("name", ref name);
            ar.ReadWriteValue("outer", ref outer);
            ar.ReadWriteValue("inner", ref inner);
            ar.ReadWriteValue("rotated", ref rotated);
            ar.ReadWriteValue("paddingLeft", ref paddingLeft);
            ar.ReadWriteValue("paddingRight", ref paddingRight);
            ar.ReadWriteValue("paddingTop", ref paddingTop);
            ar.ReadWriteValue("paddingBottom", ref paddingBottom);
            ar.ReadWriteValue("AtlasName", ref m_szAtlasName);
            if (ar.GetVersion() >= 1)
            {
                ar.ReadWriteValue("NameID", ref m_nNameID);
                ar.ReadWriteValue("AtlasID", ref m_nAtlasID);
            }
        }
    }
}

