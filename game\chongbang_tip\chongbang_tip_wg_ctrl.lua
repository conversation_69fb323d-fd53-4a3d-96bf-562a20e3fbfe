-- 冲榜通用弹窗

require("game/chongbang_tip/chongbang_tip_wg_data")
require("game/chongbang_tip/chongbang_tip_view")

ChongBangTipWGCtrl = ChongBangTipWGCtrl or BaseClass(BaseWGCtrl)

function ChongBangTipWGCtrl:__init()
	if ChongBangTipWGCtrl.Instance then
		ErrorLog("[ChongBangTipWGCtrl]:Attempt to create singleton twice!")
	end
	ChongBangTipWGCtrl.Instance = self

	self.data = ChongBangTipWGData.New()
	self.view = ChongBangTipView.New(GuideModuleName.ChongBangTipView)

	self.main_ui_open = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.OnMainUIOpen, self))
	self.act_check_event = GlobalEventSystem:Bind(CHONGBANG_TIP_EVENT.ACT_CHECK, BindTool.Bind1(self.ChongBangActCheckEvent, self))

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function ChongBangTipWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil
	
	self.view:DeleteMe()
	self.view = nil

	ChongBangTipWGCtrl.Instance = nil

	GlobalEventSystem:UnBind(self.main_ui_open)
	GlobalEventSystem:UnBind(self.act_check_event)

	ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
	self.activity_change_callback = nil
end


function ChongBangTipWGCtrl:OnMainUIOpen()
	ChongBangTipWGData.Instance:CheckAllActJieSuanTime()
end

function ChongBangTipWGCtrl:ChongBangActCheckEvent(act_tip_type,act_id)
	ChongBangTipWGData.Instance:CheckEventActJieSuanTime(act_tip_type,act_id)
end

-- 活动信息改变
function ChongBangTipWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	local is_rank_act_tip = ChongBangTipWGData.Instance:GetIsRankActTip(ChongBangTipWGData.ACT_TIP_TYPE.ACT,activity_type)
	if is_rank_act_tip and status == ACTIVITY_STATUS.OPEN then
		ChongBangTipWGData.Instance:ActOpenCheckJieSuanTime(ChongBangTipWGData.ACT_TIP_TYPE.ACT,activity_type)
	end
end

function ChongBangTipWGCtrl:OpenTipView(data)
	if IS_AUDIT_VERSION then
		return
	end

	local index = data and data.cfg and data.cfg.index or 0
	local flag = ChongBangTipWGData.Instance:GetNoTipActIndex(index)
	if flag then
		return
	end
	ChongBangTipWGData.Instance:AddOpenTipData(data)
	local scene_type = Scene.Instance:GetSceneType()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if self.view and not self.view:IsOpen() and scene_cfg.pb_chongbang_tip == 0 then
		local data = ChongBangTipWGData.Instance:GetOpenTipData()
		if not IsEmptyTable(data) then
			self.view:SetData(data)
			self.view:Open()
		end
	end

end