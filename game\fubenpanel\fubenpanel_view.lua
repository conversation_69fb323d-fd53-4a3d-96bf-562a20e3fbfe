FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)

local SINGLE_FUBEN_MAX = 5 -- 单人副本数量
local TEAM_FUBEN_MAX = 5 -- 组队副本数量
local TYPE_SPAN = 10 

local FuBenIndex = {
	-- 注释的是因为有额外处理 不需要自动打开
	-- [TabIndex.fubenpanel_welkin] = 1,
	-- [TabIndex.fubenpanel_copper] = 5,
	[TabIndex.fubenpanel_pet] = 3,
	[TabIndex.fubenpanel_bagua] = 4,
	--[TabIndex.fubenpanel_zhuogui] = 5,
	[TabIndex.fubenpanel_exp] = 2,
	[TabIndex.fubenpanel_equip_high] = 11,
	[TabIndex.fubenpanel_tianshen] = 5,
	[TabIndex.fubenpanel_beauty] = 12,
	[TabIndex.fubenpanel_control_beasts] = 13,
	[TabIndex.fubenpanel_wuhun] = 14,
	[TabIndex.fubenpanel_rune_tower] = 15,
	[TabIndex.fubenpanel_bootybay] = 16,
}

function FuBenPanelView:__init()
	self.view_name = GuideModuleName.FuBenPanel
	--self.default_index = TabIndex.fubenpanel_welkin
	self.view_style = ViewStyle.Full
	self:LoadConfig()
	self:SetMaskBg()
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	
	self.fb_index = -1
	self.is_first_enter_exp_fb = false
	self.is_select_remind = false
	self.is_back_main_fb = false
	self.is_safe_area_adapter = true
	self.disslove_timer_quest = {}
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
	self.role_data_change_callback = BindTool.Bind1(self.OnRoleDataChange, self)
end

function FuBenPanelView:__delete()

end

function FuBenPanelView:OnRoleDataChange(attr_name, value, old_value)
	if attr_name == "capability" then
		self:FlushCapability()
		RemindManager.Instance:Fire(RemindName.TianXianFuBen)
	end

	if attr_name == "vip_level" then
		self:OnFlushTianShenAddEffect()
		self:FlushJingYanEffect()
	end

	if attr_name == "level" then
		self:FlushTabbarFlag()
		self:_SetList()
		self:_SetPetList()
		TeamEquipFbWGData.Instance:RecordHasGetItemList()
	end
end

function FuBenPanelView:ReleaseCallBack()
	self:DeleteFanRenXiuZhen()
	self:DeleteTfFuBenPanelView()
	self:DeleteTeamExp()
	self:DeleteTeamEquipView2()
	self:DeleteTongBiPanelView()
	self:DeletePetFuBenPanelView()
	self:DeleteZhuShenTaView()
	self:DeleteTianShen()
	self:DeleteBootybayView()
	self:DeleteBaGuaZhenView()
	self:DeleteManHuangGuDian()
	self:DeleteZhuoGuiView()
	self:DeleteTianShen()
	self:DeleteControlBeastsView()
	self:DeleteBeautyView()
	self:DeleteWuhunView()
	self:DeleteRuneTowerView()

	
	if nil ~= self.fuben_item_list then
		for i = 1, SINGLE_FUBEN_MAX do
			self.fuben_item_list[i]:DeleteMe()
			self.fuben_item_list[i] = nil
		end
		for i = TYPE_SPAN + 1, TYPE_SPAN + TEAM_FUBEN_MAX do
			self.fuben_item_list[i]:DeleteMe()
			self.fuben_item_list[i] = nil
		end
		self.fuben_item_list = nil
	end

	

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)
	if self.match_event then
		GlobalEventSystem:UnBind(self.match_event)
		self.match_event = nil
	end

	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.FuBenPanel, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	for _, v in pairs(self.disslove_timer_quest) do
		GlobalTimerQuest:CancelQuest(v)
	end
	self.disslove_timer_quest = {}
	self.first_open_fuben_panel = false
	self.is_first_enter_exp_fb = false
	self.is_select_remind = false
	self.fb_index = -1

	if self.effect_change then
		GlobalEventSystem:UnBind(self.effect_change)
		self.effect_change = nil
	end
end

-- 加载配置
function FuBenPanelView:LoadConfig()
	-- TODO 后面会统一改成特效 播放完打开界面
	-- 渐显动画节点
	self.view_animation_node_name_list = {
		-- [TabIndex.fubenpanel_copper] = { "tongbi_left_msg" },
		-- [TabIndex.fubenpanel_bootybay] = { "bootybay_left_msg" },
		-- [TabIndex.fubenpanel_equip_high] = { "team_equip_container" },
	}

	-- 底部动画
	self.view_animation_node_name_list_2 = {
		-- [TabIndex.fubenpanel_bagua] = { "bagua_container" },
		-- [TabIndex.fubenpanel_pet] = { "pet_left_msg" },
		-- [TabIndex.fubenpanel_exp] = { "fb_info_content" },
	}

	-- 左侧动画
	self.view_animation_node_name_list_3 = {
		-- [TabIndex.fubenpanel_equip_high] = { "cambered_mask" },
	}

	-- 左侧动画2
	self.view_animation_node_name_list_4 = {
		[TabIndex.fubenpanel_welkin] = { "first_pass_item_list" },
	}

	local fuben_bundle = "uis/view/fubenpanel_prefab"
	local common_path = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_path, "layout_a3_common_panel")
	self:AddViewResource(0, fuben_bundle, "fuben_tabber_panel")
	self:AddViewResource(TabIndex.fubenpanel_welkin, fuben_bundle, "layout_fanrenxiuzhen_bg")
	self:AddViewResource(TabIndex.fubenpanel_pet, fuben_bundle, "layout_pet_fb_bg")
	self:AddViewResource(TabIndex.fubenpanel_bagua, fuben_bundle, "layout_baguamizhen_bg")
	self:AddViewResource(TabIndex.fubenpanel_equip_high, "uis/view/team_equip_fb_prefab", "layout_team_equip_bg")
	self:AddViewResource(TabIndex.fubenpanel_tianshen, fuben_bundle, "layout_tianshen_panel_bg")
	self:AddViewResource(TabIndex.fubenpanel_exp, "uis/view/team_exp_fb_prefab", "layout_team_exp_bg")
	self:AddViewResource(TabIndex.fubenpanel_welkin, fuben_bundle, "layout_fanrenxiuzhen")
	self:AddViewResource(TabIndex.fubenpanel_copper, fuben_bundle, "layout_tongbi_bg")
	self:AddViewResource(TabIndex.fubenpanel_copper, fuben_bundle, "layout_tongbi_fuben1")
	self:AddViewResource(TabIndex.fubenpanel_pet, fuben_bundle, "layout_pet_panel")
	self:AddViewResource(TabIndex.fubenpanel_bagua, fuben_bundle, "layout_baguamizhen_panel")
	self:AddViewResource(TabIndex.fubenpanel_zhuogui, fuben_bundle, "layout_zhuogui")
	self:AddViewResource(TabIndex.fubenpanel_exp, "uis/view/team_exp_fb_prefab", "layout_team_exp")
	self:AddViewResource(TabIndex.fubenpanel_equip_high, "uis/view/team_equip_fb_prefab", "layout_team_equip_dif3")
	self:AddViewResource(TabIndex.fubenpanel_bootybay, fuben_bundle, "layout_bootybay_bg")
	self:AddViewResource(TabIndex.fubenpanel_bootybay, fuben_bundle, "layout_bootybay")
	self:AddViewResource(TabIndex.fubenpanel_tianshen, fuben_bundle, "layout_tianshen_panel")
	self:AddViewResource(TabIndex.fubenpanel_control_beasts, fuben_bundle, "layout_control_beasts_bg")
	self:AddViewResource(TabIndex.fubenpanel_control_beasts, fuben_bundle, "layout_control_beasts_panel")
	self:AddViewResource(TabIndex.fubenpanel_beauty, fuben_bundle, "layout_beauty_bg")
	self:AddViewResource(TabIndex.fubenpanel_beauty, fuben_bundle, "layout_beauty_panel")
	self:AddViewResource(TabIndex.fubenpanel_wuhun, fuben_bundle, "layout_wuhun_bg")
	self:AddViewResource(TabIndex.fubenpanel_wuhun, fuben_bundle, "layout_wuhun_panel")
	self:AddViewResource(TabIndex.fubenpanel_rune_tower, fuben_bundle, "layout_rune_tower_bg")
	self:AddViewResource(TabIndex.fubenpanel_rune_tower, fuben_bundle, "layout_rune_tower_panel")	

	self:AddViewResource(0, common_path, "layout_a3_light_common_top_panel")
	
end

function FuBenPanelView:LoadCallBack()
	local remind_tab = {
		{ RemindName.TianXianFuBen, RemindName.LianYuFuBen, RemindName.XianLingShengDian, RemindName.BaGuaMiZhenFb, RemindName.LongWangBaoZang}, -- RemindName.ZhuoGuiFuBen},
		{ RemindName.YuanGuXianDian, RemindName.FbBeauty, RemindName.FbControlBeasts, RemindName.FbWuHun, RemindName.FbRuneTower}, --RemindName.Bootybay 
	}

	self.node_list["single_toggle"].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, TabIndex.fubenpanel_single, false))
    self.node_list["team_toggle"].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, TabIndex.fubenpanel_team, false))

	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.OnClickClose, self))

	--获取数据枚举
    local single_list = FuBenPanelWGData.Instance:GetSingleFuBen()
    local team_list = FuBenPanelWGData.Instance:GetTeamFuBen()

	if not self.fuben_item_list then
        self.fuben_item_list = {}
        for i = 1, SINGLE_FUBEN_MAX do
            local fuben_render = FubenItem.New(self.node_list["btn_fb_" .. i])
            self.fuben_item_list[i] = fuben_render
            self.fuben_item_list[i]:SetIndex(i)

            -- 根据枚举获得数据
            local data = FuBenPanelWGData.Instance:GetFuBenCfg(single_list[i].BtnRemind)
            self.fuben_item_list[i]:SetData(data)
        end

		for i = TYPE_SPAN + 1, TYPE_SPAN + TEAM_FUBEN_MAX do
            local fuben_render = FubenItem.New(self.node_list["btn_fb_" .. i])
            self.fuben_item_list[i] = fuben_render
            self.fuben_item_list[i]:SetIndex(i)

            -- 根据枚举获得数据
            local data = FuBenPanelWGData.Instance:GetFuBenCfg(team_list[i - TYPE_SPAN].BtnRemind)
            self.fuben_item_list[i]:SetData(data)
        end
    end

	for i = 1, SINGLE_FUBEN_MAX do
        XUI.AddClickEventListener(self.node_list["btn_fb_"..i], BindTool.Bind(self.OnClickFuben, self, i))
    end
	for i = TYPE_SPAN + 1, TYPE_SPAN + TEAM_FUBEN_MAX do
        XUI.AddClickEventListener(self.node_list["btn_fb_"..i], BindTool.Bind(self.OnClickFuben, self, i))
    end

	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, { "vip_level", "capability", "level" })
	self.match_event = GlobalEventSystem:Bind(OtherEventType.MatchChangeEvent, BindTool.Bind(self.OnMatchInfoChange, self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.FuBenPanel, self.get_guide_ui_event)

	local is_single_remind, is_team_remind = self:FlushTabberRemind()

	local tab_index = TabIndex.fubenpanel_single
	if is_single_remind then
		tab_index = TabIndex.fubenpanel_single
    elseif is_team_remind then
		tab_index = TabIndex.fubenpanel_team
    end

	if tab_index then
		local toggle
		if tab_index == TabIndex.fubenpanel_single then
			toggle = self.node_list["single_toggle"].toggle
		elseif tab_index == TabIndex.fubenpanel_team then
			toggle = self.node_list["team_toggle"].toggle
		end

		if toggle and not toggle.isOn then
			toggle.isOn = true
		else
			self:OnClickSwitch(tab_index, true)
		end
	end

	self.effect_change = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.OnEffectChange, self))
end

function FuBenPanelView:OnClickClose()
	if self.is_back_main_fb then
		-- self:TabActive(true)
		self:ChangeToIndex(0)
		self.fb_index = -1
		self.is_back_main_fb = false
	else
		self:Close()
	end
end

-- 单人和组队切换
function FuBenPanelView:OnClickSwitch(show_index, is_first)
	if self.tab_index ~= show_index or is_first then
		self.tab_index = show_index
		self:PositionAnimShow()
		self.node_list.layout_single:SetActive(show_index == TabIndex.fubenpanel_single)
		self.node_list.layout_team:SetActive(show_index == TabIndex.fubenpanel_team)
		self:Flush()

		local str = show_index == TabIndex.fubenpanel_single and "a3_fb_bj_1" or "a3_fb_bj_2"
		local bundle, asset = ResPath.GetRawImagesPNG(str)
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	
end

function  FuBenPanelView:TabActive(is_active)
	if self.tab_index== TabIndex.fubenpanel_single then
		self.node_list.layout_single:SetActive(is_active)
	elseif self.tab_index== TabIndex.fubenpanel_team then
		self.node_list.layout_team:SetActive(is_active)
	end
end

-- 开发副本界面
function FuBenPanelView:OnClickFuben(index)
	local item = self.fuben_item_list[index]

	if item == nil then
		return 
	end
	if item:GetData().no_data then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.WeiKaiQi)
		return
	end

	self.fb_index1 = item:GetIndex()
	local data = item:GetData()
	if self.fb_index == data.fb_index or self.is_select_remind then
		self.is_select_remind = false
		return
	end

	self.fb_index = data.fb_index
	local is_open = data.is_open
	if not is_open then
		local str = FuBenPanelWGData.Instance:GetLimitFuBenLimitStr(RemindFunName[data.remind_name])
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		self.fb_index = 0
		return
	end

	--需要周几才能开限制
	if data.need_day_limit_type then
		local not_day_limit, open_day_tab = NewTeamWGData.Instance:CheckIsOpenDayLimit(data.need_day_limit_type)
		if not not_day_limit then
			local num_str = ""
			for k, v in pairs(open_day_tab) do
				if num_str == "" then
					num_str = NumberToChinaNumber(k)
				else
					num_str = num_str .. "." ..  NumberToChinaNumber(k)
				end
			end

			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FuBen.OpenDayLimitError, num_str))
			self.fb_index = 0
			return 
		end
	end
	
	-- self:TabActive(false)
	self:ChangeToIndex(TabIndex[RemindFunName[data.remind_name]])
end


function FuBenPanelView:CalcShowIndex()
	return 0
end

function FuBenPanelView:FlushTabberRemind()
	local is_single_remind = false
	local is_team_remind = false
	local single_list = FuBenPanelWGData.Instance:GetSingleFuBen()
	local team_list = FuBenPanelWGData.Instance:GetTeamFuBen()
	for key, value in pairs(single_list) do
		local num = RemindManager.Instance:GetRemind(value.BtnRemind)
		if num > 0 then
			is_single_remind = true
		end
	end
	for key, value in pairs(team_list) do
		local num = RemindManager.Instance:GetRemind(value.BtnRemind)

		if num > 0 then
			is_team_remind = true
		end
	end

	self.node_list.single_remind:SetActive(is_single_remind)
	self.node_list.team_remind:SetActive(is_team_remind)

	return is_single_remind, is_team_remind
end

function FuBenPanelView:FlushTabbarFlag()

	local single_list = FuBenPanelWGData.Instance:GetSingleFuBen()
	local team_list = FuBenPanelWGData.Instance:GetTeamFuBen()
	if self.fuben_item_list then
		for key, value in pairs(self.fuben_item_list) do
			if key <= TYPE_SPAN then 
				local data = FuBenPanelWGData.Instance:GetFuBenCfg(single_list[key].BtnRemind)
				value:SetData(data)

			elseif key <= TYPE_SPAN * 2 then
				local data = FuBenPanelWGData.Instance:GetFuBenCfg(team_list[key - TYPE_SPAN].BtnRemind)
				value:SetData(data)
				local num = RemindManager.Instance:GetRemind(data.remind_name)
			end
		end
	end

	self:FlushTabberRemind()
end

function FuBenPanelView:LoadIndexCallBack(index)
	if index == TabIndex.fubenpanel_welkin then
		self:InitFanRenXiuZhen()
	elseif index == TabIndex.fubenpanel_copper then
		self:InittongbiView()
	elseif index == TabIndex.fubenpanel_pet then
		self:InitPetView()
	elseif index == TabIndex.fubenpanel_exp then
		self:InitTeamExp()
	elseif index == TabIndex.fubenpanel_equip_high then
		self:InitHighTeamEquip()
	elseif index == TabIndex.fubenpanel_bootybay then
		self:InitBootybayView()
	elseif index == TabIndex.fubenpanel_bagua then
		self:InitBaGuaZhenView()
	elseif index == TabIndex.fubenpanel_zhuogui then
		self:InitZhuoGuiView()
	elseif index == TabIndex.fubenpanel_tianshen then
		self:InitTianShen()
	elseif index == TabIndex.fubenpanel_control_beasts then
		self:InitControlBeastsView()
	elseif index == TabIndex.fubenpanel_beauty then
		self:InitBeautyView()
	elseif index == TabIndex.fubenpanel_wuhun then
		self:InitWuhunView()
	elseif index == TabIndex.fubenpanel_rune_tower then
		self:InitRuneTowerView()
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function FuBenPanelView:OnTabChangeHandler(index)
	-- if index == 0 then
	-- 	self:TabActive(true)
	-- else
	-- 	self:TabActive(false)
	-- end
	self:ChangeToIndex(index)
end

function FuBenPanelView:OpenCallBack()
	self.fist_time_event = GlobalEventSystem:Bind(FuBenEvent.WuJinJiTanFirstTimeEvent,
		BindTool.Bind(self.FlushFirstTime, self))
	FuBenPanelWGCtrl.Instance:SendTongBiFbOperate(COIN_FB_OPERA_TYPE.COIN_FB_OPERA_TYPE_GET_FB_INFO)
	FuBenPanelWGCtrl.Instance:SendChongWuFbOperate(NEW_PETFB_REQ_TYPE.NEW_PETFB_REQ_TYPE_OTHER_INFO)

	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_TEAM_EQUIP_INFO)
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_YUANGU_XIANDIAN_FB_INFO_REQ)
	FuBenPanelWGData.Instance:BindPersonalReqCallBack()
	local protocol = ProtocolPool.Instance:GetProtocol(CSGetPersonRankListReq)
	protocol.rank_type = PersonRankType.TianXianGeRank
	protocol:EncodeAndSend()

	local enter_times = FuBenPanelWGData.Instance:GetExpFubenTimes() or 0 --策划要求第一次进入时不显示提示框
	self.is_first_enter_exp_fb = enter_times == 0
	if not self.first_open_fuben_panel then
		self.first_open_fuben_panel = true
	end
end

function FuBenPanelView:ShowIndexCallBack(index)
	if index == 0 then
		self:TabActive(true)
	else
		if index < 20 then
			self.node_list["single_toggle"].toggle.isOn = true
		else
			self.node_list["team_toggle"].toggle.isOn = true
		end
		self:TabActive(false)
	end

	if index == TabIndex.fubenpanel_bagua then
		self.is_back_main_fb = true
		self:DoBaGuaFBTweenStart()
		self:ShowBaGuaZhenCallBack()
		self:ShowBaGuaAnimCallBack()
	elseif index == TabIndex.fubenpanel_copper then
		self.is_back_main_fb = true
		self:DoTongBiTweenStart()
	elseif index == TabIndex.fubenpanel_bootybay then
		self.is_back_main_fb = true
		-- self:DoBootybayTweenStart()
	elseif index == TabIndex.fubenpanel_exp then
		self.is_back_main_fb = true
		local enter_times = FuBenPanelWGData.Instance:GetExpFubenTimes() or 0 --策划要求第一次进入时不显示提示框
		self.is_first_enter_exp_fb = enter_times == 0
	elseif index == TabIndex.fubenpanel_equip_high then
		self.is_back_main_fb = true
		-- self:DoTeamEquipFBTweenStart()
	elseif index == TabIndex.fubenpanel_pet then
		self.is_back_main_fb = true
	elseif index == TabIndex.fubenpanel_welkin then
		self.is_back_main_fb = true
		self:DoTXGFBTweenStart()
		-- self:PreParePlayChapterCellListAnim()
		-- self:PlayChapterCellListAnim()
	elseif index == TabIndex.fubenpanel_tianshen then
		self.is_back_main_fb = true
	elseif index == TabIndex.fubenpanel_control_beasts then
		self.is_back_main_fb = true
	elseif index == TabIndex.fubenpanel_beauty then
		self.is_back_main_fb = true
	elseif index == TabIndex.fubenpanel_wuhun then
		self.is_back_main_fb = true
	elseif index == TabIndex.fubenpanel_rune_tower then
		self.is_back_main_fb = true
	end

	self:ShowCommonSpecial(index)
	self:ViewAnimation(index)
	self:FlushTabberRemind()
end

function FuBenPanelView:ShowCommonSpecial(index)
	local fb_name = ""
	if self.is_back_main_fb then
		fb_name = Language.FuBenPanel.RuleTitle[index] or ""
	else
		fb_name = Language.FuBenPanel.RuleTitle[0]
	end
	self.node_list.title_view_name.text.text = fb_name
end

-- 界面动画
function FuBenPanelView:ViewAnimation(index)
	local tween_info = UITween_CONSTS.FuBen
	UITween.CleanAllTween(GuideModuleName.FuBenPanel)

	local node_name_list = self.view_animation_node_name_list[index]
	if not IsEmptyTable(node_name_list) then
		for i, node_name in pairs(node_name_list) do
			UITween.MoveAlphaShowPos(GuideModuleName.FuBenPanel, self.node_list[node_name].gameObject, Vector2(500, 0), Vector2(-204, 0), 0.8)
		end
	end

	local node_name_list_2 = self.view_animation_node_name_list_2[index]
	if not IsEmptyTable(node_name_list_2) then
		for i, v in pairs(node_name_list_2) do
			UITween.MoveAlphaShowPos(GuideModuleName.FuBenPanel, self.node_list[v].gameObject, Vector2(0, -145), Vector2(0, 0), 0.8)
		end
	end

	local node_name_list_3 = self.view_animation_node_name_list_3[index]
	if not IsEmptyTable(node_name_list_3) then
		for i, v in pairs(node_name_list_3) do
			UITween.MoveAlphaShowPos(GuideModuleName.FuBenPanel, self.node_list[v].gameObject, Vector2(-200, -24), Vector2(200, -24), 0.8)
			
		end
	end

	local node_name_list_4 = self.view_animation_node_name_list_4[index]
	if not IsEmptyTable(node_name_list_4) then
		for i, v in pairs(node_name_list_4) do
			UITween.MoveAlphaShowPos(GuideModuleName.FuBenPanel, self.node_list[v].gameObject, Vector2(-1000, -24), Vector2(-488, -24), 0.8)
			
		end
	end

end

function FuBenPanelView:RemoveDissloveTimeQuest(index)
	if self.disslove_timer_quest[index] then
		GlobalTimerQuest:CancelQuest(self.disslove_timer_quest[index])
		self.disslove_timer_quest[index] = nil
	end
end

function FuBenPanelView:FlushCurIndex()
	self:Flush(self.show_index, "combine_key")
end

function FuBenPanelView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k or "combine_key" == k then
			if index == TabIndex.fubenpanel_welkin then
				self:OnFlushFanRenXiuZhenPanel(param_t)
			elseif index == TabIndex.fubenpanel_copper then
				self:OnFlushTBView()
			elseif index == TabIndex.fubenpanel_pet then
				self:OnFlushPetView(param_t)
			elseif index == TabIndex.fubenpanel_exp then
				self:OnFlushTeamExpView()
			elseif index == TabIndex.fubenpanel_equip_high then
				self:OnFlushHighTeamEquipView(k, v)
			elseif index == TabIndex.fubenpanel_bootybay then
				self:FlushBootybayView()
			elseif index == TabIndex.fubenpanel_bagua then
				self:FlushBaGuaZhenView()
			elseif index == TabIndex.fubenpanel_zhuogui then
				self:FlushZhuoGuiView()
			elseif index == TabIndex.fubenpanel_tianshen then
				self:OnFlushTianShen()
			elseif index == TabIndex.fubenpanel_control_beasts then
				self:FlushControlBeastsView()
			elseif index == TabIndex.fubenpanel_beauty then
				self:FlushBeautyView()
			elseif index == TabIndex.fubenpanel_wuhun then
				self:FlushWuhunView()
			elseif index == TabIndex.fubenpanel_rune_tower then
				self:FlushRuneTowerView()
			end
		elseif k == "xz_fetch_result" then
			if index == TabIndex.fubenpanel_welkin then
				self:OnFlushFanRenXiuZhenPanel(param_t)
			end
		elseif k == "baguamizhen_enter_times" then
			if index == TabIndex.fubenpanel_bagua then
				self:FlushBaGuaZhenView()
			end
		elseif k == "baguamizhen_buy_times" then
			if index == TabIndex.fubenpanel_bagua then
				self:FlushBaGuaZhenView()
			end
		elseif k == "pet_buy_or_enter_times" then
			if index == TabIndex.fubenpanel_pet then
				self:OnFlushPetBuyTimes(param_t)
			end
		elseif k == "FlushTBHaveCount" then
			if index == TabIndex.fubenpanel_copper then
				self:FlushTBHaveCount()
			end
		elseif k == "yuangu_enter_times" then
			if index == TabIndex.fubenpanel_equip_high then
				self:FlushHasEnterCount()
				self:FlushYuanGuEffect()
			end
		elseif k == "tianshen_buy_times" then
			self:OnFlushBuyTimes()
		elseif k == "change_index" then
			-- 之前是跳转到具体的某一个 现在如果要加就判断跳到哪个页签的就好
			local index = FuBenIndex[v.tab_index]
			if nil ~= index then
				if index ~=0 then
					if index < 20 then
						self.node_list["single_toggle"].toggle.isOn = true
					else
						self.node_list["team_toggle"].toggle.isOn = true
					end
				end

				self:OnClickFuben(index)
			end
			
			return
		end
	end

	if index > 0 then
		self:FlushTabbarFlag()
	end
end

function FuBenPanelView:OnMatchInfoChange()
	if self.show_index == TabIndex.fubenpanel_exp
		or self.show_index == TabIndex.fubenpanel_equip_high 
		or self.show_index == TabIndex.fubenpanel_control_beasts 
		or self.show_index == TabIndex.fubenpanel_beauty 
		or self.show_index == TabIndex.fubenpanel_wuhun 
		or self.show_index == TabIndex.fubenpanel_rune_tower then
		self:Flush(self.show_index)
	end
end

function FuBenPanelView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end
	elseif ui_name == GuideUIName.FuBenWelKinStart then
		local time = TimeWGCtrl.Instance:GetServerTime()
		local guide_cfg = FunctionGuide.Instance:GetGuideCfg()[26]
		if guide_cfg then
			FuBenPanelWGData.Instance:SetFuBenJiangHuGuideFlag(guide_cfg.trigger_param .. "|" .. time .. "|2")
		end
		return self.node_list.btn_goto, BindTool.Bind(self.OnClickGoToXiuZhen, self, true)
	elseif ui_name == GuideUIName.FuBenExpMoreRole then
		if self:GetShowIndex() ~= TabIndex.fubenpanel_exp then
			self:ShowIndex(TabIndex.fubenpanel_exp)
			return
		end

		return self.node_list["btn_exp_baoming_enter"], BindTool.Bind(self.OnClickExpBaoMingEnter, self, true)
	elseif ui_name == GuideUIName.FuBenEnterYGXDMore then
		return self.node_list["btn_exp_baoming_enter"], BindTool.Bind(self.OnClickExpBaoMingEnter, self, true)
	elseif ui_name == GuideUIName.FuBenEnterHDFXMore then
		return self.node_list["btn_hte_baoming_enter"], BindTool.Bind1(self.OnClickHteBaoMingEnter, self)
	elseif ui_name == GuideUIName.FuBenPetStart then
		return self.node_list["btn_pet_enter"], BindTool.Bind(self.OnClickEnterPetFb, self)
	elseif ui_name == GuideUIName.FuBenTongbiStart then
		return self.node_list["btn_tongbi_enter"], BindTool.Bind(self.GoToCopperFb, self)
	elseif ui_name == GuideUIName.FuBenTianShenEnter then
		return self.node_list["btn_tianshen_enter"], BindTool.Bind(self.OnClickEnterTianshenFb, self, true)
	elseif ui_name == "btn_beasts_enter" then
		return self.node_list["btn_beasts_enter"], BindTool.Bind(self.OnClickControlBeastsBaoMingEnter, self, true)
	elseif ui_name == "btn_fb_13_guide" then
		local call_back = function()
			if self.fuben_item_list and self.fuben_item_list[13] then
				local render = self.fuben_item_list[13]
	
				if render and render.OnClickCell then
					render:OnClickCell()
				end
			end
		end

		return self.node_list["btn_fb_13_guide"], call_back
	end
	
	return self.node_list[ui_name]
end

function FuBenPanelView:ItemDataChangeCallback()
	self:Flush()
end

function FuBenPanelView:CloseCallBack()
	-- override
	if self.fist_time_event then
		GlobalEventSystem:UnBind(self.fist_time_event)
		self.fist_time_event = nil
	end
	self.first_time_flag = 0
	TaskGuide.Instance:SpecialConditions(true, 5)

	self.is_back_main_fb = false
end

local pos_y_list = {-15, -15, -12, -12, -10, -10}
local tween_time_list = {0.7, 0.7, 0.5, 0.5, 0.3, 0.3}

function FuBenPanelView:PositionAnimShow()
	for i = 1, 6 do
		local index = self.tab_index == TabIndex.fubenpanel_single and i or i + TYPE_SPAN
		if self.node_list["btn_fb_" .. index] then
			local pos_x = i%2 == 0 and 143 or -127
			UITween.MoveShowPanel(self.node_list["btn_fb_" .. index],Vector3(pos_x, pos_y_list[i]),tween_time_list[i])
		end
	end
end

local TypeList = {
	[RemindName.TianXianFuBen] = { 1, 2 },
	[RemindName.LongWangBaoZang] = { 3, 4 },
	[RemindName.XianLingShengDian] = { 5 },
	[RemindName.BaGuaMiZhenFb] = { 6 },
	[RemindName.ZhuoGuiFuBen] = { 7 },
	[RemindName.LianYuFuBen] = { 8 },
	[RemindName.YuanGuXianDian] = { 9 },
	[RemindName.Bootybay] = { 10 },
	[RemindName.TianShenFb] = { 11 },
	[RemindName.FbControlBeasts] = { 12 },
	[RemindName.FbBeauty] = { 13 },
	[RemindName.FbWuHun] = { 14 },
	[RemindName.FbRuneTower] = { 15 },
}

function FuBenPanelView:OnEffectChange()
	self:FlushTeamExpBuffTime()
end

---------------------------fuben render-------------------------

FubenItem = FubenItem or BaseClass(BaseRender)

function FubenItem:__init()

end

function FubenItem:__delete()
	if self.remind_callback then
		RemindManager.Instance:UnBind(self.remind_callback)
		self.remind_callback = nil
	end
end

function FubenItem:LoadCallBack()

    XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.OnClickCell, self))

	if not self.remind_callback then
		self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
	end
end

function FubenItem:GetColor(is_team,is_reach)
	if is_team then
		return is_reach and "#99ffbb" or "#ff9292"
		
	else
		return is_reach and "#3c8652" or "#a93c3c" 
	end
end

function FubenItem:OnFlush()
	local is_opne = self.data.is_open

	-- 是否没有数据
	-- self.data.no_data

	-- self.node_list.open:SetActive(is_opne)
	-- self.node_list.no_open:SetActive(not is_opne and not self.data.no_data)
	-- XUI.SetGraphicGrey(self.node_list.root, not is_opne and not self.data.no_data)
	-- if not is_opne and not self.data.no_data then
	-- 	-- local str = FuBenPanelWGData.Instance:GetLimitFuBenLimitStr(RemindFunName[self.data.remind_name])
	-- 	-- self.node_list.no_open_txt.text.text = str
	-- 	self.node_list.text_name.text.text = Language.FuBenPanel.TabSub4[self.data.fb_index]
	-- 	self.node_list.btn_text.text.text = self.data.btn_call_back and Language.FuBenPanel.BtnLabel6 or Language.FuBenPanel.BtnLabel1
	-- 	return
	-- elseif not is_opne then
	-- 	return
	-- end
	if self.data.no_data then
		return 
	end

	

	local is_open = self.data.is_open
	

	if not is_open and not self.data.no_data then
		local str = FuBenPanelWGData.Instance:GetLimitFuBenLimitStr(RemindFunName[self.data.remind_name])
		self.node_list.text_open_limit.text.text = str
		self.node_list.btn:SetActive(false)
	elseif not is_open then
		self.node_list.text_open_limit.text.text = ""
		self.node_list.btn:SetActive(false)
	else
		local has_day_limit = false
		--需要周几才能开限制
		if self.data.need_day_limit_type then
			local not_day_limit, open_day_tab = NewTeamWGData.Instance:CheckIsOpenDayLimit(self.data.need_day_limit_type)
			if not not_day_limit then
				local num_str = ""
				for k, v in pairs(open_day_tab) do
					if num_str == "" then
						num_str = NumberToChinaNumber(k)
					else
						num_str = num_str .. "." ..  NumberToChinaNumber(k)
					end
				end
				self.node_list.text_open_limit.text.text = string.format(Language.FuBen.OpenDayLimitError, num_str)
				self.node_list.btn:SetActive(false)
				has_day_limit = true
			end
		end
		if not has_day_limit then
			self.node_list.text_open_limit.text.text = ""
			self.node_list.btn:SetActive(true)
		end
	end

	-- 按钮文本
	self.node_list.btn_text.text.text = self.data.btn_call_back and Language.FuBenPanel.BtnLabel6 or Language.FuBenPanel.BtnLabel1

	-- 副本名
	self.node_list.text_name.text.text = Language.FuBenPanel.TabSub4[self.data.fb_index]

	-- 活跃度
	self.node_list.act_num:SetActive(self.data.act_num ~= -1)
	if self.data.act_num ~= -1 then
		local color = self:GetColor(self.data.is_team,true)
		local str = string.format(Language.FuBenPanel.HYD, color, self.data.act_num, self.data.act_max_num)
		self.node_list.act_num.text.text = str
	end
	
	-- 剩余次数
	self.node_list.time_num:SetActive(self.data.has_time_num ~= nil)
	if self.data.has_time_num then
		local str = self.data.is_team and Language.FuBenPanel.BtnLabel3_Dark or Language.FuBenPanel.BtnLabel3
		self.node_list.time_num.text.text = string.format(str, self.data.has_time_num,
			self.data.all_time_num)
	end

	-- 战力
	self.node_list.capability:SetActive(self.data.cur_capability ~= nil)
	self.node_list.tj_capability:SetActive(self.data.tuijian_capability ~= nil)
	if self.data.tuijian_capability then
		self.node_list.tj_capability.text.text = self.data.tuijian_capability
		local color = self:GetColor(self.data.is_team,self.data.is_reach_capability)
		self.node_list.capability.text.text = string.format(Language.FanRenXiuZhen.MyCapability1, color, self.data.cur_capability)
	end

	-- 周次数
	self.node_list.week_time_num:SetActive(self.data.has_week_num ~= nil)
	if self.data.has_week_num then
		local str = self.data.is_team and Language.FuBenPanel.BtnLabel5_Dark or Language.FuBenPanel.BtnLabel5
		self.node_list.week_time_num.text.text = string.format(str, self.data.has_time_num, self.data.all_time_num)
	end

	-- 组队标签显示 现在按页签分 没有显示
	-- self.node_list.team_type:SetActive(self.data.is_team)
	--开启特殊处理显示
	if self.data.need_day_limit_type then
		local not_day_limit, open_day_tab = NewTeamWGData.Instance:CheckIsOpenDayLimit(self.data.need_day_limit_type)
		if not not_day_limit then
			self.node_list.time_num:SetActive(true)
			self.node_list.act_num:SetActive(false)
			self.node_list.tj_capability:SetActive(false)
			self.node_list.week_time_num:SetActive(false)

			local num_str = ""
			for k, v in pairs(open_day_tab) do
				if num_str == "" then
					num_str = NumberToChinaNumber(k)
				else
					num_str = num_str .. "." ..  NumberToChinaNumber(k)
				end
			end

			self.node_list.time_num.text.text = ToColorStr(string.format(Language.FuBen.OpenDayLimitError, num_str), COLOR3B.C3) 
		end
	end

	self:ShowDuoBei()
	self:ShowTypeGroup()

	RemindManager.Instance:Bind(self.remind_callback, self.data.remind_name)
end

-- 双倍标签显示
function FubenItem:ShowDuoBei()
	local is_duobei, times = ActivityWGData.Instance:IsDuoBeiTimes(self.data.task_type)
	if is_duobei then
		self.node_list.img_rate_bg:SetActive(true)
	else
		self.node_list.img_rate_bg:SetActive(false)
	end
end

function FubenItem:OtherRemindCallBack()
	local num = RemindManager.Instance:GetRemind(self.data.remind_name)
	self.node_list.remind:SetActive(num > 0)
end

-- 类型标签显示
function FubenItem:ShowTypeGroup()
    local str = ""
	for i = 1, 2 do
		local index = TypeList[self.data.remind_name][i]

		if index then
            str =string.format("%s %s",str,Language.FuBenPanel.TypeTxt[index])
		end
	end
    self.node_list.text_type.text.text = str

end

-- 进入副本
function FubenItem:OnClickCell()
	local is_open = self.data.is_open

	if not is_open and not self.data.no_data then
		local str = FuBenPanelWGData.Instance:GetLimitFuBenLimitStr(RemindFunName[self.data.remind_name])
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	elseif not is_open then
		return
	end

	--需要周几才能开限制
	if self.data.need_day_limit_type then
		local not_day_limit, open_day_tab = NewTeamWGData.Instance:CheckIsOpenDayLimit(self.data.need_day_limit_type)
		if not not_day_limit then
			local num_str = ""
			for k, v in pairs(open_day_tab) do
				if num_str == "" then
					num_str = NumberToChinaNumber(k)
				else
					num_str = num_str .. "." ..  NumberToChinaNumber(k)
				end
			end

			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FuBen.OpenDayLimitError, num_str))
			self.fb_index = 0
			return 
		end
	end

	if self.data.btn_call_back then
		self.data.btn_call_back()
	end

	ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel, nil, "change_index", { tab_index = self.data.tab_index })
end