-------------------------------------------
--基础副本逻辑,统一处理一些副本活动类通用的逻辑
--<AUTHOR>
--------------------------------------------
BaseFbLogic = BaseFbLogic or BaseClass(BaseSceneLogic)
function BaseFbLogic:__init()
	-- self.move_event_handler = GlobalEventSystem:Bind(MainUIEventType.MAIN_HEAD_CLICK, BindTool.Bind1(self.OnClickHeadHandler, self))
	-- MainuiWGCtrl.Instance:CreateFbIconByList(self:GetFbSceneShowFbIconCfgList())
	self.cache_info = nil
end

function BaseFbLogic:__delete()
	if nil ~= self.base_fb_logic_main_role_create then
		GlobalEventSystem:UnBind(self.base_fb_logic_main_role_create)
		self.base_fb_logic_main_role_create = nil
	end
	self.basefb_is_enter = false
	self.cache_info = nil
end

function BaseFbLogic:Enter(old_scene_type, new_scene_type)
	BaseSceneLogic.Enter(self, old_scene_type, new_scene_type)

	self.basefb_is_enter = true

	if not self.base_fb_logic_main_role_create then
		self.base_fb_logic_main_role_create = GlobalEventSystem:Bind(ObjectEventType.MAIN_ROLE_CREATE, BindTool.Bind(self.LogicOnMainRoleCreate,self))
	end
	
	-- 进入副本的时候关闭聊天界面
	if new_scene_type ~= SceneType.GUILD_ANSWER_FB then
		ChatWGCtrl.Instance:Close()
	end

	local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < 50 and fb_cfg ~= nil and fb_cfg.fb_desc ~= nil and fb_cfg.fb_desc ~= "" then
		RuleTip.Instance:SetContent(fb_cfg.fb_desc, Language.Dungeon.HowToPlay)
	end

	local scene_id = Scene.Instance:GetSceneId()
	if fb_cfg and fb_cfg.default_mode ~= "" and role_level > COMMON_CONSTS.XIN_SHOU_LEVEL then
		if BossWGData.IsBossScene(new_scene_type) and IS_ON_CROSSSERVER and new_scene_type ~= SceneType.Shenyuan_boss then
			MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.NAMECOLOR)
		elseif new_scene_type == SceneType.WorldBoss and role_level < COMMON_CONSTS.ATTACK_MODEL_CHANGE_LEVEL then
				MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
		-- 魔王巢穴第一层强制和平, 第二层默认和平
		elseif new_scene_type == SceneType.VIP_BOSS and (BossWGData.Instance:GetEnterVipCfgbyIndex(1).scene_id == scene_id
			or BossWGData.Instance:GetEnterVipCfgbyIndex(2).scene_id == scene_id) then
			MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
		-- 深渊boss第一层默认仙盟模式
		elseif new_scene_type == SceneType.Shenyuan_boss and BossWGData.Instance:GetShenYuanBossSceneidByIndex(0) == scene_id then
			MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
		-- 跨服青云之巅最后一层强制和平不可切换
		elseif new_scene_type == SceneType.Kf_Honorhalls then
			local layer_cfg = KuafuHonorhallWGData.Instance:GetLayerCfgBySceneId(scene_id)
			if layer_cfg and layer_cfg.layer == KuafuHonorhallWGData.Instance:GetMaxLayer() then
				MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
			else
				MainuiWGCtrl.Instance:SendSetAttackMode(fb_cfg.default_mode)
			end
		-- 【养龙寺】世界等级 / 结义状态 影响攻击模式
		elseif new_scene_type == SceneType.SCENE_TYPE_CROSS_YANGLONG then
			local limit_world_level = YangLongSiaWGData.Instance:GetOtherCfgData("force_peace_cross_world_level") or 0
			local world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
			local had_sworn = SwornWGData.Instance:HadSworn()
			if world_level < limit_world_level or not had_sworn then
				MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
			else
				MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.JIEYI)
			end
		else
			MainuiWGCtrl.Instance:SendSetAttackMode(fb_cfg.default_mode)
		end
	end

	BaseSceneLogic.ResetRoleAndCameraDir(self, old_scene_type ~= new_scene_type)

	-- 检测地图阻挡
	if self.cache_info and self.cache_info[1] == scene_id then
		self:CheckSceneMapBlock(scene_id, self.cache_info[2], self.cache_info[3])
	else
		self:CheckSceneMapBlock(scene_id)
	end

	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.CheckShowFaceRuleTip, self))
	-- 默认不显示
	MainuiWGCtrl.Instance:SetBossTips(false)
end

function BaseFbLogic:CheckShowFaceRuleTip()
	local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	if nil ~= fb_cfg and fb_cfg.is_enter_show_tip and fb_cfg.is_enter_show_tip == 1 and fb_cfg.fb_desc and "" ~= fb_cfg.fb_desc and fb_cfg.tips_icon_show == 1 then
		local role_id = RoleWGData.Instance:InCrossGetOriginUid()
		local scene_type = Scene.Instance:GetSceneType()
		local scene_id = Scene.Instance:GetSceneId()
		local key = "show_fb_face_rule_tip" .. role_id .. scene_type .. scene_id
	
		local can_show_rule_tip = PlayerPrefsUtil.GetInt(key, 0)
		if can_show_rule_tip == 0 then
			if scene_type == SceneType.Common then
				return
			elseif scene_type == SceneType.Kf_PvP_Prepare then
				KF3V3WGCtrl.Instance:OpenTabRuleTip(30)
			elseif scene_type == SceneType.CROSS_FLAG_GRABBING_BATTLEFIELD then
				CrossFlagGrabbingBattleFieldWGCtrl.Instance:OpenFGBExplainView(false, true)
			else
				RuleTip.Instance:SetContent(fb_cfg.fb_desc, Language.TreasureLoft.TipsRollCardTitle)
			end

			PlayerPrefsUtil.SetInt(key, 1)
		end
	end
end

--退出
function BaseFbLogic:Out(old_scene_type, new_scene_type)
	BaseSceneLogic.Out(self,old_scene_type, new_scene_type)

	self.basefb_is_enter = false

	if nil ~= self.base_fb_logic_main_role_create then
		GlobalEventSystem:UnBind(self.base_fb_logic_main_role_create)
		self.base_fb_logic_main_role_create = nil
	end

	self:RemoveSceneBlock()
	self.SetLeaveFbTip()
	-- MainuiWGCtrl.Instance:RemoveAllFbIcon()

	-- UiInstanceMgr.Instance:HideGuaiJiUi()
	-- UiInstanceMgr.Instance:CloseSceneCountDown()

	GuajiWGCtrl.Instance:StopGuaji()
	-- GlobalEventSystem:UnBind(self.move_event_handler)
	if RoleWGData.Instance.role_vo.level > COMMON_CONSTS.XIN_SHOU_LEVEL then
		if Scene.Instance:GetSceneType() ~= SceneType.WorldsNO1
		 	and Scene.Instance:GetSceneType() ~= SceneType.WorldsNO1Prepare then
			MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.PEACE)
		end
	end

	if self.effect_obj_list and #self.effect_obj_list > 0 then
		for k,v in pairs(self.effect_obj_list) do
			v:Destroy()
		end
		self.effect_obj_list = {}
	end
end

function BaseFbLogic:LogicOnMainRoleCreate()
	BaseSceneLogic:FlushMainRoleDefaultDir()
end



--获得场景显标的副本图标列表
function BaseFbLogic:GetFbSceneShowFbIconCfgList()
	local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
	if fb_scene_cfg == nil then return end
	return fb_scene_cfg.show_fbicon
end

function BaseFbLogic:OnClickHeadHandler(is_show)
	-- override
end

--------------------------------------------------
-- 进入副本 处于准备期间的表现
--------------------------------------------------
-- 检测该场景是否需要准备状态中禁止走出去区域
function BaseFbLogic:CheckSceneMapBlock(scene_id, block_data, eff_data)
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if nil == act_scene then
		if block_data ~= nil then
			self.is_map_block = true
			self:SetSceneBlock(scene_id, block_data, eff_data)
		end
		return
	end

	self.act_id = act_scene.act_id
	self.use_scene_id = act_scene.use_scene_id == 1

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(self.act_id)
	if activity_info and activity_info.status == ACTIVITY_STATUS.STANDY then
		self.is_map_block = true
		self:SetSceneBlock(scene_id)
		if nil == self.act_state_event then
			self.act_state_event = BindTool.Bind1(self.OnActivityStatus, self)
			ActivityWGData.Instance:NotifyActChangeCallback(self.act_state_event)
		end

		local map_block_bubble_cfg = SceneWGData.Instance:GetMapBlockBubbleCfg()
		if nil == self.role_bubble_timer and map_block_bubble_cfg then
			self.role_bubble_timer = GlobalTimerQuest:AddRunQuest(
				function()
					self:SetRoleBubble(scene_id)
				end, map_block_bubble_cfg.show_time)
		end
	else
		self:RemoveSceneBlock()
	end
end

function BaseFbLogic:OnActivityStatus(activity_type, status, next_time, open_type)
	if nil == self.act_id or self.act_id ~= activity_type then
		return
	end

	if status ~= ACTIVITY_STATUS.STANDY then
		self:RemoveSceneBlock()
		self:OpenActivitySceneCd(activity_type)
	end
end

function BaseFbLogic:OpenActivitySceneCd(act_type)
end

-- 设置场景禁止走路的区域(根据场景ID获取配置)
function BaseFbLogic:SetSceneBlock(scene_id, block_data, eff_data)
	self.map_block_effect_list = {}
	self.block_info = {}

	-- 任务链动态障碍区
	if block_data ~= nil then
		for _, v in pairs(block_data) do
			AStarFindWay:SetBlockInfo(v.x, v.y)
			table.insert(self.block_info, v)
		end

		if eff_data ~= nil then
			for k,v in pairs(eff_data) do
				self:SetMapBlockEffect(v.effect_name, v.x, v.y, v.effect_y, v.rotation_y, v.scale_x, v.scale_y)
			end
		end
	end

	local map_block = SceneWGData.Instance:GetMapBlockCfg(self.use_scene_id and scene_id or self.act_id)
	if nil == map_block then
		return
	end

	for _, v in pairs(map_block) do
		if v.effect_name and v.effect_name ~= "" then
			local effect_y = v.effect_y ~= "" and v.effect_y or 0
			local rotation_y = v.rotation_y ~= "" and v.rotation_y or 0
			local scale_x = v.scale_x ~= "" and v.scale_x or 1
			local scale_y = v.scale_y ~= "" and v.scale_y or 1
			self:SetMapBlockEffect(v.effect_name, v.point_x, v.point_y, effect_y, rotation_y, scale_x, scale_y)
		end
		table.insert(self.block_info, {x = v.point_x, y = v.point_y})
	end
	for _, v in pairs(self.block_info) do
		AStarFindWay:SetBlockInfo(v.x, v.y)
	end
end

function BaseFbLogic:GetIsMapBlock()
	return self.is_map_block
end

function BaseFbLogic:CheckMapBlockIsCreate(scene_id, is_show, block_data, eff_data)
	if is_show then
		if not self.is_map_block then
			if self.basefb_is_enter then
				self:CheckSceneMapBlock(scene_id, block_data, eff_data)
			else
				self.cache_info = {scene_id, block_data, eff_data}
			end
		end
	else
		if self.is_map_block then
			self:RemoveSceneBlock()
		end
	end
end

-- 移除场景禁止走路的区域
function BaseFbLogic:RemoveSceneBlock()
	self.is_map_block = false
	self.act_id = nil
	if self.role_bubble_timer then
		GlobalTimerQuest:CancelQuest(self.role_bubble_timer)
		self.role_bubble_timer = nil
	end

	if self.act_state_event then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_state_event)
		self.act_state_event = nil
	end

	if self.block_info and #self.block_info > 0 then
		for _, v in pairs(self.block_info) do
			AStarFindWay:RevertBlockInfo(v.x, v.y)
		end
		self.block_info = {}
	end

	if self.effect_obj_list and #self.effect_obj_list > 0 then
		for k,v in pairs(self.effect_obj_list) do
			v:Destroy()
		end
		self.effect_obj_list = {}
	end
end

-- 设置玩家气泡框
function BaseFbLogic:SetRoleBubble(scene_id)
	local group_id = SceneWGData.Instance:GetMapBlockBubbleChatLimitGroupId(scene_id)
	if group_id <= 0 then
		return
	end
	local is_chat_content_random = false
	local map_block_bubble_cfg = SceneWGData.Instance:GetMapBlockBubbleCfg()
	if map_block_bubble_cfg and map_block_bubble_cfg.chat_content_random and  map_block_bubble_cfg.chat_content_random ~= "" then
		is_chat_content_random = map_block_bubble_cfg.chat_content_random == 1 and true or false
	end

	local chat_content_list = SceneWGData.Instance:GetMapBlockBubbleChatContent(group_id)
	if chat_content_list then
		local map_block_bubble_cfg = SceneWGData.Instance:GetMapBlockBubbleCfg()
		local role_chat_max_num = map_block_bubble_cfg.scene_limit
		local role_chat_num = 0

		local chat_max_num = #chat_content_list
		local i = 1

		local role_list = Scene.Instance:GetRoleList()
		for _ , v in pairs(role_list) do
			if role_chat_max_num >= role_chat_num then
				if is_chat_content_random then
					local content = chat_content_list[math.random(1, chat_max_num)]
					v:GetFollowUi():ChangeBubble(content.bubble_text, content.disappear_time)
				else
					local content = chat_content_list[i]
					v:GetFollowUi():ChangeBubble(content.bubble_text, content.disappear_time)
					if chat_max_num > i then
						i = i + 1
					else
						i = 1
					end
				end
				role_chat_num = role_chat_num + 1
			else
				break
			end
		end
	end
end

-- 设置地图阻挡区域特效
function BaseFbLogic:SetMapBlockEffect(effect_name, x, y, z, rotation_y, scale_x, scale_y)
	local bundle,asset = ResPath.GetEffect(effect_name)
	if not bundle or not asset then
		return
	end

	if nil == self.effect_obj_list then
		self.effect_obj_list = {}
	end

	local rotation = Quaternion.Euler(0, rotation_y, 0)
	local scale = Vector3(scale_x, scale_y, 1)
	local key = asset .. x .. y
	local loader = AllocAsyncLoader(self, key)
	loader:SetIsUseObjPool(true)
	loader:SetParent(G_SceneObjLayer)
	loader:Load(bundle, asset, function(obj)
		if IsNil(obj) then
			return
		end

		local wx, wy = GameMapHelper.LogicToWorld(x, y)
		obj:GetOrAddComponent(typeof(MoveableObject)):SetPosition(Vector3(wx, 0, wy))
		obj.transform.rotation = rotation
		obj.transform.localScale = scale
		table.insert(self.effect_obj_list, obj)
	end)
end
--------------------------------------------------
function BaseFbLogic.SetLeaveFbTip(is_enter)
	if is_enter then
		local scene_cfg = Scene.Instance:GetSceneConfig()
        local str = string.format(Language.Boss.ConfirmLevelFB, scene_cfg.name)
        if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
            str = str..Language.XianJieBoss.ExtTip
            FuBenWGCtrl.Instance:SetLevaeFbContent(str, true)
        else
            FuBenWGCtrl.Instance:SetLevaeFbContent(str, true)
        end
	else
		FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Dungeon.ConfirmLevelFB, true)
	end
end



