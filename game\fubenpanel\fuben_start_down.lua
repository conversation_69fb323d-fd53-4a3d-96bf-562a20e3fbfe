FBStartDown = FBStartDown or BaseClass(SafeBaseView)

local YeZhanWangCheng = 1 	--夜战王城
local SHOW_GATHER = 2 		--提示采集

function FBStartDown:__init(seconds)
	-- self.view_layer = UiLayer.Pop
	-- self:SetMaskBg(false,false)
	self.is_modal = true
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fuben_start_down")
	self.order = 2
	self.active_close = false
	self.is_showing = false
	self.seconds = 0
	self.type = 0
	self.text_seconds = -1
	self.view_layer = UiLayer.Normal
	self.show_end_type = FB_START_DOWN_END_TYPE.FIGHT
end

function FBStartDown:Show(seconds,callback)
	self.seconds = seconds
	self.finish_callback = callback
	if CountDownManager.Instance:HasCountDown("fuben_start_down") then
		CountDownManager.Instance:RemoveCountDown("fuben_start_down")
	end
	self:Open()
	if seconds > 0 then
		CountDownManager.Instance:AddCountDown("fuben_start_down", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), nil, seconds, 1)
	end
	
	self.is_showing = true
end

function FBStartDown:__delete()
	UiInstanceMgr.Instance.fuben_start_down = nil
	self.is_showing = false
	self.old_scene_type = nil
	self.cur_scene_type = nil
	self.old_record_time_num = nil
end

function FBStartDown:Close()
	if self.node_list["text_progress1"] ~= nil and self.node_list["text_progress1_1"] ~= nil then
		self.node_list["text_progress1"]:SetActive(false)
		self.node_list["text_progress1_1"]:SetActive(false)
	end

	self.show_end_type = FB_START_DOWN_END_TYPE.FIGHT

	SafeBaseView.Close(self)
end

function FBStartDown:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("fuben_start_down") then
		CountDownManager.Instance:RemoveCountDown("fuben_start_down")
	end

	if self.fuhuo_time then
		self.fuhuo_time:DeleteMe()
		self.fuhuo_time = nil
	end
	UiInstanceMgr.Instance.fuben_start_down = nil
	self.is_showing = false
	if self.node_list["text_progress1"] then
		self.node_list["text_progress1"].text.text = 5
	end

	if self.update_status_event then
		GlobalEventSystem:UnBind(self.update_status_event)
	end

	if self.quest then
		CountDown.Instance:RemoveCountDown(self.quest)
		self.quest = nil
	end

	self.show_end_type = FB_START_DOWN_END_TYPE.FIGHT
end

function FBStartDown:LoadCallBack()
	self.node_list["text_progress1"].text.text = self.seconds
	self.node_list.star_fight:SetActive(false)
	self.update_status_event = GlobalEventSystem:Bind(OtherEventType.ONEVONE_STATUS_CHANGE, BindTool.Bind1(self.SetNum1Active, self))

	if self.type == YeZhanWangCheng then
		self.node_list["yjwc_bg"]:SetActive(true)
		self.node_list["text_progress1_1"]:SetActive(false)
		self.node_list["YZWC_TXT"]:SetActive(true)
		self.node_list["YZWC_TXT"].text.text = self.seconds
	elseif self.type == SHOW_GATHER then
		self.node_list["text_progress1"]:SetActive(true)
		self.node_list["gather_image"]:SetActive(true)
		self.node_list["text_progress1_1"]:SetActive(false)
	else
		self.node_list["text_progress1"]:SetActive(false)
	end
end

function FBStartDown:ShowIndexCallBack()
	self.node_list.wave_coming_root:SetActive(false)

	-- local bundle, asset
	-- if self.show_end_type ~= nil then
	-- 	if self.show_end_type == FB_START_DOWN_END_TYPE.FIGHT then
	-- 		bundle, asset = ResPath.GetMainUIIcon("a1_zjm_zdks")
	-- 	elseif self.show_end_type == FB_START_DOWN_END_TYPE.TASK then
	-- 		bundle, asset = ResPath.GetFuBenImage("task_start")
	-- 	elseif self.show_end_type == FB_START_DOWN_END_TYPE.ACT then
	-- 		bundle, asset = ResPath.GetFuBenImage("act_start")
	-- 	elseif self.show_end_type == FB_START_DOWN_END_TYPE.TASK_CLOSE then
	-- 		bundle, asset = ResPath.GetFuBenImage("task_close")
	-- 	elseif self.show_end_type == FB_START_DOWN_END_TYPE.FEISHENG then
	-- 		bundle, asset = ResPath.GetFuBenImage("feisheng_end")
	-- 	end
	-- end

	-- if bundle and asset then
	-- 	self.node_list.star_fight.image:LoadSprite(bundle, asset, function ()
	-- 		self.node_list.star_fight.image:SetNativeSize()
	-- 	end)
	-- end
end

function FBStartDown:OnFlush(param_t)
end

function FBStartDown:OpenEquipFbFuhuo()
end

function FBStartDown:UpdateTime(elapse_time, total_time)
	if nil == self.node_list["text_progress1"] or nil == self.node_list["text_progress1_1"] then return end
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	self.cur_scene_type = Scene.Instance:GetSceneType()
	if nil == self.old_scene_type then
		self.old_scene_type = self.cur_scene_type
	end
	if Scene.Instance:GetSceneType() == SceneType.Kf_OneVOne then
		self.node_list.text_progress1_1.rect.anchorMin = Vector2(0.5, 0.5)
		self.node_list.text_progress1_1.rect.anchorMax = Vector2(0.5, 0.5)
		self.node_list.text_progress1_1.rect.anchoredPosition = Vector2(-3, 8)
		self.node_list.text_progress_number_pa:SetActive(false)
		self.node_list.text_progress_number_jjc_pa:SetActive(true)
	elseif Scene.Instance:GetSceneType() == SceneType.PHANTOM_DREAMLAND_FB then
		self.node_list.text_progress_number_pa:SetActive(true)
		self.node_list.text_progress_number_jjc_pa:SetActive(false)
	else
		self.node_list.text_progress1_1.rect.anchorMin = Vector2(0.5, 1)
		self.node_list.text_progress1_1.rect.anchorMax = Vector2(0.5, 1)
		self.node_list.text_progress1_1.rect.anchoredPosition = Vector2(0.15, -155)
		self.node_list.text_progress_number_pa:SetActive(true)
		self.node_list.text_progress_number_jjc_pa:SetActive(false)
	end

	if Scene.Instance:GetSceneType() == SceneType.ZHUSHENTA_FB
		or Scene.Instance:GetSceneType() == SceneType.Fb_Welkin
		or Scene.Instance:GetSceneType() == SceneType.Wujinjitan
		or Scene.Instance:GetSceneType() == SceneType.DEVILCOME_FB
		or Scene.Instance:GetSceneType() == SceneType.GAINT_FB
		or Scene.Instance:GetSceneType() == SceneType.KUNLUNTRIAL_FB
		or Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_TASKFB_ZHILIAO then --or

		self.node_list["text_progress1"]:SetActive(false)
		self.node_list["text_progress1_1"]:SetActive(true)

		self.text_seconds = temp_seconds - 1
		if temp_seconds <= 1 and temp_seconds > 0 then
			self.node_list["text_progress1_1"]:SetActive(false)
			self.node_list.star_fight:SetActive(true)
			--  self:DoTweenScaleContentFightStart()
		elseif  temp_seconds > 1 then
			self.node_list.star_fight:SetActive(false)
			self.node_list["text_progress_number"].text.text = GameMath.Round(total_time - elapse_time - 1)
			self:DoTweenScaleContent()
		else
			self.node_list["text_progress_number"].text.text = ""
			self.node_list.star_fight:SetActive(false)
		end
	elseif Scene.Instance:GetSceneType() == SceneType.PHANTOM_DREAMLAND_FB then
		self.node_list["text_progress1"]:SetActive(false)
		self.node_list["text_progress1_1"]:SetActive(true)
		self.node_list["effect_point"]:SetActive(true)
		
		local node_name = self.is_boss and "boss_star_fight" or "star_fight"
		self.text_seconds = temp_seconds - 1
		if temp_seconds <= 1 and temp_seconds > 0 then
			self.node_list["text_progress1_1"]:SetActive(false)
			self.node_list[node_name]:SetActive(true)

			if self.is_boss then
				self:DoTweenScaleContentFightStartByNode(node_name)
			end
		elseif  temp_seconds > 1 then
			self.node_list[node_name]:SetActive(false)
			self.node_list["text_progress_number"].text.text = GameMath.Round(total_time - elapse_time - 1)
			self:DoTweenScaleContent()
		else
			self.node_list["text_progress_number"].text.text = ""
			self.node_list[node_name]:SetActive(false)
		end
	elseif self.type == SHOW_GATHER then
		if  temp_seconds > 1 then
			self.node_list.star_fight:SetActive(false)
			self.node_list["text_progress1"].text.text = GameMath.Round(total_time - elapse_time - 1)
			self:DoTweenScaleContent2()
		else
			self.node_list["text_progress1"].text.text = ""
			self.node_list["gather_image"]:SetActive(false)
		end
	elseif self.type == YeZhanWangCheng then
		if temp_seconds > 1 then
			self.node_list["YZWC_TXT"]:SetActive(true)
			self.node_list["YZWC_TXT"].text.text = GameMath.Round(total_time - elapse_time - 1)
			-- self:DoTweenScaleContent(self.node_list["YZWC_TXT"])
		else
			self.node_list["YZWC_TXT"]:SetActive(false)
			self.node_list["YZWC_TXT"].text.text = ""
			self.node_list["yjwc_bg"]:SetActive(false)
		end

	elseif Scene.Instance:GetSceneType() == SceneType.Kf_OneVOne then
		self.node_list.text_progress_number_jjc_pa:SetActive(true)
		self.node_list.text_progress1_1:SetActive(true)
		local temp_seconds = total_time - elapse_time
		local num = GameMath.Round(temp_seconds)

		if num == 0 or num == -0 then
			self.node_list["text_progress_number_jjc"].text.text = ""
			self.node_list.one_vs_one_image:SetActive(true)
		end

		if num <= 0 then
			self.node_list.one_vs_one_image:SetActive(false)
			self.node_list.text_progress1_1:SetActive(false)
			GlobalEventSystem:Fire(OtherEventType.ONEVONE_PREPARE_ANI,false)
		end

		if self.old_record_time_num == nil or self.old_record_time_num ~= num then
			self.node_list["text_progress_number_jjc"].text.text = num
			self:DoTweenScaleContent(self.node_list["text_progress1_1"], 2)
		end

		self.old_record_time_num = num
	elseif Scene.Instance:GetSceneType() == SceneType.GuildMiJingFB then
		if FuBenWGCtrl.Instance.fuben_mijing_view:IsOpen() then
			FuBenWGCtrl.Instance.fuben_mijing_view:SetNextWaveTime(GameMath.Round(total_time - elapse_time))
		end
	elseif self.cur_scene_type == SceneType.Common and (self.old_scene_type ~= self.cur_scene_type or self.old_scene_type == SceneType.Common) then --快速退出副本倒计时依旧存在问题
		self:CompleteTime()
	else
		if temp_seconds >= 100 then return end
		self.node_list["text_progress1"]:SetActive(false)
		self.node_list["text_progress1_1"]:SetActive(true)
		self.node_list["text_progress_number"].text.text = GameMath.Round(total_time - elapse_time)
		self:DoTweenScaleContent()
	end

	self.old_scene_type = self.cur_scene_type
end

function FBStartDown:CompleteTime()
	--sFightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.BACK_HOME, 0, -1)
	--self.fuhuo_time:SetNumber(0)
	if self.finish_callback then
		self.finish_callback()
		self.finish_callback = nil
	end
	self:Close()
end

function FBStartDown:SetKillerName(killer_name)
	self.killer_name = killer_name or ''
	self:Flush()
end

--------------------------------------
function FBStartDown:Show2(seconds,callback)
	self.seconds = GameMath.Round(seconds - TimeWGCtrl.Instance:GetServerTime())
	self.finish_callback = callback
	if CountDownManager.Instance:HasCountDown("fuben_start_down") then
		CountDownManager.Instance:RemoveCountDown("fuben_start_down")
	end

	self:Open()
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), GameMath.Round(seconds))
	if seconds - TimeWGCtrl.Instance:GetServerTime() > 0 then
		CountDownManager.Instance:AddCountDown("fuben_start_down", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), GameMath.Round(seconds), nil, 1)
	end

	self.is_showing = true
end

--夜战王城倒计时
function FBStartDown:Show3(type,seconds,callback)
	self.seconds = GameMath.Round(seconds - TimeWGCtrl.Instance:GetServerTime()) - 1
	self.finish_callback = callback
	if CountDownManager.Instance:HasCountDown("fuben_start_down") then
		CountDownManager.Instance:RemoveCountDown("fuben_start_down")
	end
	self.type = type
	self:Open()
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), seconds)
	if seconds - TimeWGCtrl.Instance:GetServerTime() > 0 then
		CountDownManager.Instance:AddCountDown("fuben_start_down", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), seconds, nil, 1)
	end
	self.is_showing = true
end

--幻梦秘境倒计时
function FBStartDown:Show4(is_boss, seconds, callback)
	self.seconds = GameMath.Round(seconds - TimeWGCtrl.Instance:GetServerTime()) - 1
	self.finish_callback = callback
	if CountDownManager.Instance:HasCountDown("fuben_start_down") then
		CountDownManager.Instance:RemoveCountDown("fuben_start_down")
	end
	self.is_boss = is_boss
	self:Open()
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), seconds)
	if seconds - TimeWGCtrl.Instance:GetServerTime() > 0 then
		CountDownManager.Instance:AddCountDown("fuben_start_down", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), seconds, nil, 1)
	end
	self.is_showing = true
end

function FBStartDown:DoTweenScaleContent(node,set_scale)
	local scale = Vector3(1,1,1)
	if node ~= nil then
		if set_scale then
			node.rect.localScale = Vector3(set_scale,set_scale,1)
		else
			node.rect.localScale = Vector3(3,3,1)
		end
		
		node.rect:DOScale(scale,0.3)
	else
		self.node_list.text_progress1_1.rect.localScale = Vector3(3,3,1)
		self.node_list.text_progress1_1.rect:DOScale(scale,0.3)
	end
	EffectManager.Instance:PlayAtTransform("effects/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.effect_point.transform)
end

function FBStartDown:DoTweenScaleContent2()
	local scale = Vector3(1,1,1)
	self.node_list.text_progress1.rect.localScale = Vector3(3,3,1)
	self.node_list.text_progress1.rect:DOScale(scale,0.3)
end

-- function FBStartDown:DoTweenScaleContentFightStart()
-- 	local scale = Vector3(1,1,1)
-- 	self.node_list.star_fight.rect.localScale = Vector3(3,3,1)
-- 	self.node_list.star_fight.rect:DOScale(scale,0.3)
-- 	EffectManager.Instance:PlayAtTransform("effects/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.effect_point.transform)
-- end

function FBStartDown:DoTweenScaleContentFightStartByNode(node_name)
	local scale = Vector3(1, 1, 1)
	self.node_list[node_name].rect.localScale = Vector3(3, 3, 1)
	self.node_list[node_name].rect:DOScale(scale, 0.3)
	EffectManager.Instance:PlayAtTransform("effects/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.effect_point.transform)
end

function  FBStartDown:SetNum1Active(status)
	self.node_list["text_progress1_1"]:SetActive(status)
end




------------------新方法-------------------
function FBStartDown:Reset()
	self.node_list["yjwc_bg"]:SetActive(false)
	self.node_list["text_progress1_1"]:SetActive(false)
	self.node_list["YZWC_TXT"]:SetActive(false)
	self.node_list["text_progress1"]:SetActive(false)
	self.node_list["gather_image"]:SetActive(false)
end

function FBStartDown:ChangeToStartFightTimer(end_time_stamp, finish_callback, fix_time, show_type)
	if self.quest then
		CountDown.Instance:RemoveCountDown(self.quest)
		self.quest = nil
	end

	self.show_end_type = show_type or FB_START_DOWN_END_TYPE.FIGHT

	self:Open()

	self.finish_callback = finish_callback
	local total_time = end_time_stamp - TimeWGCtrl.Instance:GetServerTime() 
	local tmp_time = fix_time or math.floor(total_time)
	self.cur_total_count = tmp_time
	MainuiWGCtrl.Instance:SetFbPrepareTimeTextMark(true)

	self.quest = CountDown.Instance:AddCountDown(tmp_time, 1, BindTool.Bind(self.UpdateStartFightTimer, self), BindTool.Bind(self.EndOrCompleteStartFightTimer, self))
end

function FBStartDown:UpdateStartFightTimer(elapse_time, total_time)
	if nil == self.node_list["text_progress1_1"] then 
		return 
	end

	self.cur_total_count = self.cur_total_count - 1
	self.node_list["text_progress1"]:SetActive(false)
	if Scene.Instance:GetSceneType() == SceneType.XianMengzhan or Scene.Instance:GetSceneType() == SceneType.GuildMiJingFB then
		self.node_list["text_progress1_1"]:SetActive(true)
		self.node_list["effect_point"]:SetActive(true)
	else
		self.node_list["text_progress1_1"]:SetActive(self.cur_total_count <= 4)
		self.node_list["effect_point"]:SetActive(self.cur_total_count <= 4)
	end
	
	
	self.node_list.text_progress_number_pa:SetActive(true)
	self.node_list.text_progress_number_jjc_pa:SetActive(false)

	self.text_seconds = math.floor(total_time - elapse_time) - 1
	local temp_seconds = math.floor(total_time - elapse_time)
	if self.cur_total_count <= 1 and self.cur_total_count > 0 then
		self:TheLastSecondShowLogic()
	elseif  self.cur_total_count > 1 then
		self.node_list.star_fight:SetActive(false)
		self.node_list["text_progress_number"].text.text = self.cur_total_count - 1
		self:DoTweenScaleContent()
		MainuiWGCtrl.Instance:SetShowTimeTextState( false )
	else
		self.node_list["text_progress_number"].text.text = ""
		self.node_list.star_fight:SetActive(false)
	end
end

function FBStartDown:EndOrCompleteStartFightTimer()
	if self.finish_callback then
		self.finish_callback()
		self.finish_callback = nil
	end
	--MainuiWGCtrl.Instance:SetShowTimeTextState( true )
	MainuiWGCtrl.Instance:SetFbPrepareTimeTextMark(nil)
	self:Close()
	self.cur_total_count = 0
end

--最后一秒显示的东西
function FBStartDown:TheLastSecondShowLogic()
	self.node_list["text_progress1_1"]:SetActive(false)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.ManHuangGuDian_FB then
		--显示第几波来袭
		self.node_list.wave_coming_root:SetActive(true)
		local scale = Vector3(1,1,1)
		self.node_list.wave_coming_root.rect.localScale = Vector3(3,3,1)
		self.node_list.wave_coming_root.rect:DOScale(scale,0.3)
		EffectManager.Instance:PlayAtTransform("effects/prefab/ui/ui_daojishi_1_prefab", "UI_daojishi_1", self.node_list.effect_point.transform)
		--波数赋值
		local scene_info = ManHuangGuDianWGData.Instance:GetSceneInfo()
		self.node_list.wave_text.text.text = scene_info.curr_wave_index
		MainuiWGCtrl.Instance:ClearTimeText()
	else
		self.node_list.star_fight:SetActive(true)
		-- self:DoTweenScaleContentFightStart()
		if scene_type == SceneType.Wujinjitan or scene_type == SceneType.LingHunGuangChang then
			MainuiWGCtrl.Instance:SetShowTimeTextState(true)
		else
			MainuiWGCtrl.Instance:SetShowTimeTextState(false)
		end
	end
end