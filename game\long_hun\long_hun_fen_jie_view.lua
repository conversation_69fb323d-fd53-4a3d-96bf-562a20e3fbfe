LongHunFenJieView = LongHunFenJieView or BaseClass(SafeBaseView)

function LongHunFenJieView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:LoadConfig()
	self.cur_select_color = {}
end

function LongHunFenJieView:__delete()
	
end

function LongHunFenJieView:ReleaseCallBack()
	if self.long_hun_fen_jie_list then
		self.long_hun_fen_jie_list:DeleteMe()
		self.long_hun_fen_jie_list = nil
	end

	if self.delay_fenjie then
		GlobalTimerQuest:CancelQuest(self.delay_fenjie)
		self.delay_fenjie = nil
	end
	--LongHunWGData.Instance:SetFenJieStatus(false)
end

-- 加载配置
function LongHunFenJieView:LoadConfig()
	local bundle_name = "uis/view/long_hun_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_commmon_second_panel",{vector2 = Vector2(0, 0), sizeDelta = Vector2(1102, 590)})
	self:AddViewResource(0,bundle_name,"layout_longhun_fenjie")
end

function LongHunFenJieView:LoadCallBack(index, loaded_times)
	--self:SetSecondView(Vector2(886,551))
	self.node_list.title_view_name.text.text = Language.LongHunView.FenJieView
	self.long_hun_fen_jie_list = AsyncListView.New(LongHunFenJieBigCell, self.node_list.long_hun_fen_jie_list)
	self.node_list["confirm_fenjie_btn"].button:AddClickListener(BindTool.Bind(self.ConfirmFenJie, self))
	self.node_list["select_close"].button:AddClickListener(BindTool.Bind(self.OnClickCloseSelect,self))
	-- for i=1,5 do
	-- 	self.node_list["fenjie_color"..i].button:AddClickListener(BindTool.Bind(self.OnClickSelectColor,self,i))
	-- end

	for i=1,5 do
		self.node_list["color_btn"..i].button:AddClickListener(BindTool.Bind(self.OnClickSelectColor,self,i))
	end

	self.node_list["note_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFenJieTips,self))
	self.node_list["auto_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFenJieAuto,self))
end

function LongHunFenJieView:ShowIndexCallBack(index)
	LongHunWGData.Instance:SetFenJieStatus(false)
	LongHunWGData.Instance:InitFenJieSelectColor()
end

function LongHunFenJieView:ConfirmFenJie()
	local count,index_list = LongHunWGData.Instance:GetAllSelectIndex()
	if count > 0 then

		GlobalTimerQuest:AddDelayTimer(function ()
			LongHunWGCtrl.Instance:SendLongHunDeCompose(count, index_list)
		end,0.4)

		LongHunWGData.Instance:SetFenJieStatus(true)
		self:PlayLongHunFenJieAni()
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.LongHunView.PleaseSelectFenjie)
	end
end

function LongHunFenJieView:OnClickFenJieTips()
	RuleTip.Instance:SetContent(Language.LongHunView.RuleDesc,Language.LongHunView.RlueTitle)
end

function LongHunFenJieView:OnClickFenJieAuto()
	LongHunWGData.Instance:SetFenJieMark()
	self:FlushAutoMark()
end

function LongHunFenJieView:FlushAutoMark()
	self.node_list["auto_mark"]:SetActive(LongHunWGData.Instance:GetFenJieMark() == 1)
end

function LongHunFenJieView:OnFlush(param_t)
	-- self.now_slot_index = MingWenWGData.Instance:GetSelectPosyIndex()
	LongHunWGData.Instance:FenJieSelectColorItem()
	self.all_data = LongHunWGData.Instance:GetAllDeComposeData()
	self:ReloadListData()
	self:FlushColorSelect()
	self:FlushAutoMark()
end

function LongHunFenJieView:ReloadListData()
	if self.is_playing_fenjie then return end
	local list_data = {}
	local length = 11
	local num = 1
	for i=1,4 do
		list_data[i] = {}
	end
	for k,v in pairs(self.all_data) do
		if v.is_lock ~= 1 then
			v.select_callback = BindTool.Bind(self.OnClickCellCalllBack, self)
			local a = math.ceil(num/length)
			local b = num % length
			if nil == list_data[a] then
				list_data[a] = {}
			end
	
			if b == 0 then
				b = length
			end
	
			list_data[a][b] = v
			num = num + 1
		end
	end
	self.long_hun_fen_jie_list:SetDataList(list_data)
end

function LongHunFenJieView:ReFlushActiveCell()
	-- self.long_hun_fen_jie_list:RefreshActiveCellViews()
	self:FlushColorSelect()
	self:Flush()
end

function LongHunFenJieView:SelectAllData(index)
	local new_data = {}
	for k,v in pairs(self.all_data) do
		if v.index ~= index then
			table.insert(new_data,v)

		end
	end
	self.all_data = new_data
	self:ReloadListData()
end

function LongHunFenJieView:OnClickCellCalllBack(data)
	if not IsEmptyTable(data) then
		--data.index
		LongHunWGData.Instance:SelectFenJieItem(data.index)
		--self:SelectAllData(data.index)
		self.long_hun_fen_jie_list:RefreshActiveCellViews()
		self:FlushColorSelect()
	end
end


function LongHunFenJieView:OnClickSelectColor(color)
	-- self.node_list.btn_select.toggle.isOn = false
	--if color ~= self.cur_select_color then
		LongHunWGData.Instance:ChangeColorSelect(color)
		self:ReFlushActiveCell()
	--end
end
function LongHunFenJieView:OnClickCloseSelect()
	self.node_list.btn_select.toggle.isOn = false
end

function LongHunFenJieView:FlushColorSelect()
	self.cur_select_color = LongHunWGData.Instance:GetSelectingColor()
	local get_exp, get_xinjing = LongHunWGData.Instance:GetFenJieGetMoney()
	self.node_list["money1"].text.text = get_exp
	self.node_list["money2"].text.text = get_xinjing

	for i = 1 , 5 do
		self.node_list["select_img"..i]:SetActive(self.cur_select_color[i])
	end

	--self.node_list["selecting_color"].text.text = Language.LongHunView.ColorSelect[self.cur_select_color]
end

function LongHunFenJieView:PlayLongHunFenJieAni()
	self.is_playing_fenjie = true
	self.long_hun_fen_jie_list:RefreshActiveCellViews()
	self.delay_fenjie = GlobalTimerQuest:AddDelayTimer(function ()
		self.is_playing_fenjie = false
		LongHunWGData.Instance:SetFenJieStatus(false)
		GlobalTimerQuest:CancelQuest(self.delay_fenjie)
		self.delay_fenjie = nil
		LongHunWGCtrl.Instance:PlayFenJieAni()
		self:Close()
	end,0.3)
end

LongHunFenJieBigCell = LongHunFenJieBigCell or BaseClass(BaseRender)

function LongHunFenJieBigCell:__init()
	
end

function LongHunFenJieBigCell:__delete()
	if self.rune_list then
		for k,v in pairs(self.rune_list) do
			v:DeleteMe()
		end
		self.rune_list = nil
	end
end

function LongHunFenJieBigCell:LoadCallBack()
	self.rune_list = {}
	for i= 1,11 do
		self.rune_list[i] = LongHunFenJieSmallCell.New(self.node_list["cell"..i]) 
	end
end

function LongHunFenJieBigCell:OnFlush()
	if not IsEmptyTable(self.data) then
		for i=1,11 do
			if self.data[i] then
				--self.node_list["cell"..i]:SetActive(true)
				self.rune_list[i]:SetData(self.data[i])
			else
				--self.node_list["cell"..i]:SetActive(false)
				self.rune_list[i]:SetData({})
			end
		end
	else
		for i=1,11 do
			self.rune_list[i]:SetData({})
		end
	end
end

function LongHunFenJieBigCell:FlushHL()
	for k,v in pairs(self.rune_list) do
		v:FlushSelect()
	end
end


LongHunFenJieSmallCell = LongHunFenJieSmallCell or BaseClass(BaseRender)

function LongHunFenJieSmallCell:__init()
	self.old_level = 0
	self.old_item_id = 0
	self.old_index = 0
end

function LongHunFenJieSmallCell:ReleaseCallBack()

end

function LongHunFenJieSmallCell:LoadCallBack()
	self.node_list["click_area"].button:AddClickListener(BindTool.Bind(self.OnClickSmallCell,self))

end

function LongHunFenJieSmallCell:OnFlush()
	--self.node_list["effect_pos"]:SetActive(false)
	if not LongHunWGData.Instance:GetIsPlayingFenJieAni() then
		if not IsEmptyTable(self.data) then
			self:FlushCellData()
		else
			self:FlushEmptyData()
		end
	end
	self:FlushHL()
end

function LongHunFenJieSmallCell:OnClickSmallCell()
	if self.data and self.data.select_callback then
		self.data.select_callback(self.data)
	end
end

function LongHunFenJieSmallCell:FlushHL()
	if self.data and self.data.index then
		self.node_list["effect_pos"]:SetActive(true)
		local is_select = LongHunWGData.Instance:GetFenJieItemSelect(self.data.index)
		self.node_list["cell_hl"]:SetActive(is_select == 1)
		if is_select == 1 and LongHunWGData.Instance:GetIsPlayingFenJieAni() then
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_longhun_baodian)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos"].transform, 0.3)
		GlobalTimerQuest:AddDelayTimer(function ()
			self.data = {}
			self:FlushEmptyData()
			self.node_list["cell_hl"]:SetActive(false)
		end,0)
		end		
	else
		self.node_list["cell_hl"]:SetActive(false)
		self.node_list["effect_pos"]:SetActive(false)
	end
end

function LongHunFenJieSmallCell:FlushCellData()
	-- local bundle, asset = ResPath.GetLongHunImg("lh_puton_d1")
 	-- self.node_list["item_di"].image:LoadSpriteAsync(bundle, asset,function ()
	-- self.node_list["item_di"].image:SetNativeSize()
	-- end)
	self.old_level = self.data.level 
	self.old_item_id = self.data.item_id
	self.old_index =  self.data.index
	self.node_list["item_pos"]:SetActive(true)
	local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
 	local bundle, asset = ResPath.GetItem(cfg.icon_id)
 	self.node_list["item_pos"].image:LoadSpriteAsync(bundle, asset,function ()
		self.node_list["item_pos"].image:SetNativeSize()
	end)
	self.node_list["cell_name"].text.text = ToColorStr(cfg.name,ITEM_COLOR[cfg.color])
	if self.data.type ~= 1 then
		self.node_list["cell_level"].text.text = "Lv."..self.data.level
	else
		self.node_list["cell_level"].text.text = ""
	end
end

function LongHunFenJieSmallCell:FlushEmptyData()
	-- local bundle, asset = ResPath.GetLongHunImg("lh_puton_d2")
 	-- self.node_list["item_di"].image:LoadSprite(bundle, asset,function ()
	-- 	self.node_list["item_di"].image:SetNativeSize()
	-- end)
	self.node_list["cell_name"].text.text = ""
	self.node_list["cell_level"].text.text = ""
	self.node_list["item_pos"]:SetActive(false)
end
