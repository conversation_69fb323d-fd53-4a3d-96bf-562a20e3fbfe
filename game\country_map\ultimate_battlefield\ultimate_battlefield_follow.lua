UltimateBattlefieldFollow = UltimateBattlefieldFollow or BaseClass(SafeBaseView)

local ULTIMATE_TIME_DOWN = "ultimate_time_down"
--0红1蓝
local ULTIATE_CAMP = {
    CAMP_RED = 0,
    CAMP_BLUE = 1,
}

-- 这里使用的夜战王城的预制
function UltimateBattlefieldFollow:__init()
	self:AddViewResource(0, "uis/view/kuafu_yezhanwangcheng_ui_prefab", "layout_hh_new")
	self:AddViewResource(0, "uis/view/kuafu_yezhanwangcheng_ui_prefab", "layout_yezhanwangchen_battle_top")
	self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_talent_follow")
    self.is_safe_area_adapter = true
end

function UltimateBattlefieldFollow:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	self:CleanTimeDown()
	self.show_data = nil
	self.is_set_talent = false
	self.show_over = false
end

function UltimateBattlefieldFollow:SetShowData(show_data)
	self.show_data = show_data
end


function UltimateBattlefieldFollow:LoadCallBack()
	self.is_set_talent = false
	self.node_list.rank_contents:CustomSetActive(false)
	self.node_list.reward_root:CustomSetActive(true)

    -- 初始化已孵化列表
	if not self.reward_list then
		self.reward_list = AsyncListView.New(UltimateBattlefieldFollowItemRender, self.node_list.reward_list_view)
	end

	XUI.AddClickEventListener(self.node_list.btn_gift_see, BindTool.Bind1(self.OnClickSeeGift, self))
	XUI.AddClickEventListener(self.node_list.btn_get_rank, BindTool.Bind1(self.OnClickGetRank, self))
	XUI.AddClickEventListener(self.node_list.zc_result_1, BindTool.Bind(self.OnClickGetResult, self))
	XUI.AddClickEventListener(self.node_list.talent_skill_root, BindTool.Bind(self.OnClickTalentSkill, self))
end

function UltimateBattlefieldFollow:CloseCallBack()
    self.node_list.layout_hh_root_new.transform:SetParent(self.root_node_transform, false)

	if self.show_top_event then
    	GlobalEventSystem:UnBind(self.show_top_event)
    	self.show_top_event = nil
    end

	if self.main_menu_icon_change then
    	GlobalEventSystem:UnBind(self.main_menu_icon_change)
    	self.main_menu_icon_change = nil
    end
	
end

function UltimateBattlefieldFollow:ShowIndexCallBack(index)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ()
		self:InitCallBack()
	end)
end

--- 主界面加载回调
function UltimateBattlefieldFollow:InitCallBack()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
    MainuiWGCtrl.Instance:SetOtherContents(true)
	if parent then
		self.node_list.layout_hh_root_new.transform:SetParent(parent.transform, false)
	end

	self.show_top_event = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK,
		BindTool.Bind(self.ShowTopEvent, self))

	self.main_menu_icon_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_MENU_ICON_CHANGE, 
		BindTool.Bind1(self.ShowTopEvent, self))

	MainuiWGCtrl.Instance:ActiveVIPConnectToTop(false)
	-- 这里调用是第二阶段直接展示
	self:FlushTalentMessage()

	local scene_type = Scene.Instance:GetSceneType()
	self.node_list.score_root:CustomSetActive(scene_type == SceneType.CROSS_ULTIMATE_BATTLE)
	self:ShowTopEvent(false)
end

function UltimateBattlefieldFollow:ShowTopEvent(ison)
	self.node_list.panel.transform:DOAnchorPosY(ison and 380 or 0, 0.8)
	self.node_list.ultimate_talent_panel.transform:DOAnchorPosY(ison and 380 or 0, 0.8)
end

function UltimateBattlefieldFollow:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			if not self.show_data then
				return
			end
			self:FlushTimeCountDown(self.show_data.end_time)
			self:FlushScoreRewardMessage()
			self:FlushCampScore()
        elseif k == "player_score" then
			self:FlushScoreRewardMessage()
			self:FlushCampScore()
		end
	end
end

-- 刷新天赋
function UltimateBattlefieldFollow:FlushTalentMessage()
	local talent_seq = UltimateBattlefieldWGData.Instance:GetPlayerTalentSeq()
	local random_talent_seq = UltimateBattlefieldWGData.Instance:GetPlayerRandomTalentSeq()
	local talent_cfg = UltimateBattlefieldWGData.Instance:GetTalentCfgBySeq(talent_seq)

	if (talent_seq == -1 and random_talent_seq == -1) or talent_cfg == nil then
		return
	end

	self.node_list.talent_follow_root:CustomSetActive((talent_seq ~= -1 or random_talent_seq ~= -1) and talent_cfg ~= nil)

	if (talent_seq ~= -1 or random_talent_seq ~= -1) and talent_cfg and (not self.is_set_talent) then
		local real_talent_seq = talent_seq

		if real_talent_seq == -1 then
			real_talent_seq = talent_seq
		end

		self.is_set_talent = true
		self.node_list["talent_icon"].image:LoadSprite(ResPath.GetCountryUltimateImg(string.format("a2_zjzc_ucon%d", real_talent_seq)))
		self.node_list["talent_simple_desc"].text.text = talent_cfg.simple_desc
	end
end

-- 刷新天赋技能
function UltimateBattlefieldFollow:FlushTalentSkillMessage()
	local talent_data = UltimateBattlefieldWGData.Instance:GetTalentData()
	if (not talent_data) or (not talent_data.talent_item_list) or (#talent_data.talent_item_list <= 0) then
		return
	end
	
	local talent_item = talent_data.talent_item_list[1]
	if not talent_item then
		return
	end

	if self.node_list and self.node_list.talent_skill_root then
		local talent_cfg = UltimateBattlefieldWGData.Instance:GetTalentCfgBySeq(talent_item.talent_seq)
		self.node_list.talent_skill_root:CustomSetActive(talent_cfg and talent_cfg.skill_id ~= 0 and talent_item.skill_cd_time == 0)

		if talent_cfg and talent_cfg.skill_id ~= 0 and talent_item.skill_cd_time == 0 then
			local talent_cfg = UltimateBattlefieldWGData.Instance:GetTalentCfgBySeq(talent_item.talent_seq)
			if talent_cfg and talent_cfg.skill_id then
				local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(talent_cfg.skill_id, 1)
				if skill_cfg and skill_cfg.icon_resource then
					self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon_resource))
				end
			end
		end
	end
end

-- 关闭技能
function UltimateBattlefieldFollow:CloseTalentSkill()
	self.node_list.talent_skill_root:CustomSetActive(false)
end

-- 展示回合结束
function UltimateBattlefieldFollow:ShowRoundOver(is_show)
	if self.node_list and self.node_list.round_over then
		self.show_over = is_show
		self.node_list.round_over:CustomSetActive(is_show)
	end
end

-- 清除倒计时
function UltimateBattlefieldFollow:ClearTimeDownShow()
	if self.node_list and self.node_list.next_time_bg then
		self.node_list.next_time_bg:CustomSetActive(false)
	end

	if self.node_list and self.node_list.cir_time then
		self.node_list.cir_time:CustomSetActive(false)
	end

	self:CleanTimeDown()
end

-- 刷新奖励
function UltimateBattlefieldFollow:FlushScoreRewardMessage()
	local my_rank_info = UltimateBattlefieldWGData.Instance:GetMyRankInfo()
	local score = my_rank_info and my_rank_info.score or 0
	local rank = my_rank_info and my_rank_info.rank or 0
	local list = UltimateBattlefieldWGData.Instance:GetAllScoreRewardByPlayerScore(score)

	if list and self.reward_list then
		self.reward_list:SetDataList(list)
	end
	
	self.node_list.label_jifen.text.text = score
	self.node_list.label_rank_value.text.text = rank
end

-- 刷新总积分
function UltimateBattlefieldFollow:FlushCampScore()
    local rank_info = UltimateBattlefieldWGData.Instance:GetScoreRankInfo()
    if not rank_info then
        return
    end

    local left_num = 0
    local right_num = 0
    local left_score = 0
    local right_score = 0

    for _, rank_item in ipairs(rank_info.rank_item_list) do
        if rank_item then    --- 检测是否竞猜
            if rank_item.camp == ULTIATE_CAMP.CAMP_RED then
                left_score = left_score + rank_item.score
				left_num = left_num + 1 
            else
                right_score = right_score + rank_item.score
				right_num = right_num + 1 
            end
        end
    end

    self.node_list.num_l.text.text = left_num
	self.node_list.num_r.text.text = right_num
	self.node_list.score_l.text.text = left_score
	self.node_list.score_r.text.text = right_score
	local bundle, asset = ResPath.GetCountryUltimateImg(string.format("a2_zjzc_tb%d", ULTIATE_CAMP.CAMP_RED))
	self.node_list.guild_icon_l.image:LoadSprite(bundle, asset,function ()
		self.node_list.guild_icon_l.image:SetNativeSize()
	end)

	bundle, asset = ResPath.GetCountryUltimateImg(string.format("a2_zjzc_tb%d", ULTIATE_CAMP.CAMP_BLUE))
	self.node_list.guild_icon_r.image:LoadSprite(bundle, asset,function ()
		self.node_list.guild_icon_r.image:SetNativeSize()
	end)

	local cfg = UltimateBattlefieldWGData.Instance:GetCampCfgBySeq(ULTIATE_CAMP.CAMP_RED)
	self.node_list.guild_name_l:CustomSetActive(true)
	self.node_list.guild_name_l.text.text = cfg and cfg.camp_name or ""
	cfg = UltimateBattlefieldWGData.Instance:GetCampCfgBySeq(ULTIATE_CAMP.CAMP_BLUE)
	self.node_list.guild_name_r:CustomSetActive(true)
	self.node_list.guild_name_r.text.text = cfg and cfg.camp_name or ""
end

-----------------活动时间倒计时-------------------
function UltimateBattlefieldFollow:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown(ULTIMATE_TIME_DOWN) then
		CountDownManager.Instance:RemoveCountDown(ULTIMATE_TIME_DOWN)
	end
end

function UltimateBattlefieldFollow:FlushTimeCountDown(end_time)
	local last_time_show = end_time - TimeWGCtrl.Instance:GetServerTime()
	self.node_list.next_time_bg:CustomSetActive(last_time_show > 10)
	self.node_list.cir_time:CustomSetActive(last_time_show > 0 and last_time_show < 10)
	
	if last_time_show > 0 then
		self:CleanTimeDown()
		self.node_list.label_next.text.text = self.show_data and self.show_data.end_str or ""
		self.node_list.label_next_time.text.text = TimeUtil.FormatSecondDHM6(end_time - TimeWGCtrl.Instance:GetServerTime())
		self.node_list.text_progress_number.text.text = math.floor(end_time) 
		
		CountDownManager.Instance:AddCountDown(ULTIMATE_TIME_DOWN, BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), end_time, nil, 1)
	else
		if self.show_data.show_type == CROSS_1VN_TIME_DOWN_TYPE.CROSS_1VN_TIME_DOWN_QUESTION then
			-- 这里是检测中途进入准备场景时有答题进入答题，没有答题进入天赋选择
			UltimateBattlefieldWGCtrl.Instance:CheckIsQuestionIsFinish()
		end
	end
end

function UltimateBattlefieldFollow:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	self.node_list.next_time_bg:CustomSetActive(valid_time > 10)
	self.node_list.cir_time:CustomSetActive(valid_time > 0 and valid_time < 11)

	if valid_time > 0 then
		self.node_list.label_next_time.text.text = TimeUtil.FormatSecondDHM6(valid_time)
		self.node_list.text_progress_number.text.text = math.floor(valid_time) 
	end
end

function UltimateBattlefieldFollow:OnComplete()
    self.node_list.label_next_time.text.text = ""
	self.node_list.next_time_bg:CustomSetActive(false)
	self.node_list.cir_time:CustomSetActive(false)

	if self.show_data.show_type == CROSS_1VN_TIME_DOWN_TYPE.CROSS_1VN_TIME_DOWN_QUESTION then
		UltimateBattlefieldWGCtrl.Instance:OpenAnswerView()
	end
end

function UltimateBattlefieldFollow:OnClickSeeGift()
	UltimateBattlefieldWGCtrl.Instance:OpenPersonRewardView()
end

function UltimateBattlefieldFollow:OnClickGetRank()
	local my_rank_info = UltimateBattlefieldWGData.Instance:GetMyRankInfo()
	if my_rank_info then
		-- 执行查看排行信息
		UltimateBattlefieldWGCtrl.Instance:OpenScoreRankView()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Rank.NoRank)
	end
end

function UltimateBattlefieldFollow:OnClickGetResult(i)

end

-- 释放技能
function UltimateBattlefieldFollow:OnClickTalentSkill()
	local talent_data = UltimateBattlefieldWGData.Instance:GetTalentData()
	if (not talent_data) or (not talent_data.talent_item_list) or (#talent_data.talent_item_list <= 0) then
		return
	end
	
	local talent_item = talent_data.talent_item_list[1]
	if not talent_item then
		return
	end

	if self.show_over then
		return
	end

	local talent_cfg = UltimateBattlefieldWGData.Instance:GetTalentCfgBySeq(talent_item.talent_seq)
	self.node_list.talent_skill_root:CustomSetActive(talent_cfg and talent_cfg.skill_id ~= 0 and talent_item.skill_cd_time == 0)

	if talent_cfg and talent_cfg.skill_id ~= 0 and talent_item.skill_cd_time == 0 then
		local skill_id = talent_cfg.skill_id
		local skill_level = 1

		local main_role = Scene.Instance:GetMainRole()
		if main_role == nil or main_role:IsDeleted() then
			return
		end

		local skill_pos_x, skill_pos_y = main_role:GetLogicPos()
		local target_obj
		if SkillWGData.Instance:IsSkillToSelf(skill_id) then
			-- local forward = main_role:GetRoot().gameObject.transform.forward
			-- local dir = math.atan2(forward.z, forward.x)
		else
			if GuajiCache.target_obj ~= nil and not GuajiCache.target_obj:IsDeleted() and Scene.Instance:IsEnemy(GuajiCache.target_obj) then
				local g_x, g_y = GuajiCache.target_obj:GetLogicPos()
				local cur_dis = GameMath.GetDistance(g_x, g_y, skill_pos_x, skill_pos_y, true)
				local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, skill_level)
				if skill_cfg and cur_dis <= skill_cfg.distance then
					target_obj = GuajiCache.target_obj
				end
			end
		end

		if main_role:GetIsGatherState() then
			Scene.Instance:SendStopGather()
		end

		-- local skill_x, skill_y
		-- if not FightWGCtrl.Instance:TryUseRoleSpecialSkill(skill_id, target_obj, skill_x, skill_y, true) then
		-- 	return
		-- end

		local is_riding_no_fight_mount = main_role:IsRidingNoFightMount()
		if  is_riding_no_fight_mount then
			MountWGCtrl.Instance:SendMountGoonReq(0)
		end

		local target_obj_id = target_obj and target_obj:GetObjId() or COMMON_CONSTS.INVALID_OBJID
		TaskGuide.LAST_CLICK_SKILL_TIME = Status.NowTime
		UltimateBattlefieldWGCtrl.Instance:RequestTalentSkill(talent_item.talent_seq, target_obj_id, skill_pos_x, skill_pos_y)
	end
end


----------------------------------奖励物品-----------------------
UltimateBattlefieldFollowItemRender = UltimateBattlefieldFollowItemRender or BaseClass(BaseRender)
function UltimateBattlefieldFollowItemRender:LoadCallBack()
    if not self.item_list then
	    self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    end
end

function UltimateBattlefieldFollowItemRender:__delete()
	if self.item_list then
		self.item_list:DeleteMe()
		self.item_list = nil
	end
end

function UltimateBattlefieldFollowItemRender:OnFlush()
    if not self.data then
        return 
    end

	if self.data.cfg_data then

		local real_reward_item = {}

		for _, reward_data in pairs(self.data.cfg_data.reward_item) do
			if reward_data and reward_data.item_id then
				table.insert(real_reward_item, reward_data)
			end
		end

		self.item_list:SetDataList(real_reward_item)
		local str = string.format(Language.UltimateBattlefield.ScoreRewardDesc, self.data.cfg_data.need_score)
		if self.data.get_ed then
			str = Language.Common.YiLingQu
		end
		
		self.node_list.reward_score.text.text = str
	end
end
