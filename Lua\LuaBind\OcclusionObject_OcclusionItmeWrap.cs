﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class OcclusionObject_OcclusionItmeWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(OcclusionObject.OcclusionItme), null);
		<PERSON><PERSON>("New", _CreateOcclusionObject_OcclusionItme);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("renderer", get_renderer, set_renderer);
		<PERSON><PERSON>("occlusionMaterial", get_occlusionMaterial, set_occlusionMaterial);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateOcclusionObject_OcclusionItme(IntPtr L)
	{
		OcclusionObject.OcclusionItme obj = new OcclusionObject.OcclusionItme();
		ToLua.PushValue(L, obj);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			OcclusionObject.OcclusionItme obj = (OcclusionObject.OcclusionItme)o;
			UnityEngine.GameObject ret = obj.renderer;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_occlusionMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			OcclusionObject.OcclusionItme obj = (OcclusionObject.OcclusionItme)o;
			UnityEngine.Material ret = obj.occlusionMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index occlusionMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_renderer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			OcclusionObject.OcclusionItme obj = (OcclusionObject.OcclusionItme)o;
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 2, typeof(UnityEngine.GameObject));
			obj.renderer = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_occlusionMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			OcclusionObject.OcclusionItme obj = (OcclusionObject.OcclusionItme)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.occlusionMaterial = arg0;
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index occlusionMaterial on a nil value");
		}
	}
}

