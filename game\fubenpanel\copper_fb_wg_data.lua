---
--- Generated by <PERSON><PERSON><PERSON>(https://github.com/EmmyLua)
--- Created by 123.
--- DateTime: 2019/10/17 10:02
---
--- 副本-金蟾宝库
CopperFbWGData = CopperFbWGData or BaseClass()

function CopperFbWGData:__init()
    CopperFbWGData.Instance = self

    self.copper_scence_info = {}

    --初始化配置
	self:InitCopperCfg()
end
function CopperFbWGData:__delete()
    self.other_cfg = nil
    self.reward_cfg = nil
    CopperFbWGData.Instance = nil
end

function CopperFbWGData:InitCopperCfg()
    local all_cfg = ConfigManager.Instance:GetAutoConfig("coinfb_cfg_auto")
	--其他配置
	self.other_cfg = all_cfg.other[1]
	--奖励配置
	self.reward_cfg = all_cfg.reward
    --购买次数
    self.buy_times_cfg = ListToMap(all_cfg.buy_times, "buy_times")

    local count = 0
	for i, v in pairs(all_cfg.buy_times) do
		count = count + 1
	end
	self.max_buy_times = count
end
function CopperFbWGData:GetOtherCfg()
	return self.other_cfg
end
function CopperFbWGData:GetRewardItemList(star_num)
    local role_level = self:_FindLevel()
    local cfg = nil
    for i, v in pairs(self.reward_cfg) do
        if v.level == role_level and v.star_num == star_num then
            cfg = v
        end
    end
    local ret_data_list = {}
    
    local three_e_wai = cfg.item_1
    for i = 0, 10 do
        if three_e_wai[i] then
            local data = {}
            data.item_id = three_e_wai[i].item_id
            data.is_bind = three_e_wai[i].is_bind
            data.num = three_e_wai[i].num --策划要求只显示这个物品的数量（lsq）
            data.show_duobei = true
            data.task_type = RATSDUOBEI_TASK.JINCHANBAOKU
            data.is_three_must_drop = true
            table.insert(ret_data_list , data)
        else
            break
        end
    end

    --local split_tab = Split(cfg.item_1, "|")
    
    for i = 0, 10 do
        if cfg.reward_item[i] then
            local data = {}
            data.item_id = cfg.reward_item[i].item_id
            data.is_bind = cfg.reward_item[i].is_bind
            data.num = 0 --cfg.reward_item[i].num --策划要求不显示（lsq）
            data.show_duobei = true
            data.task_type = RATSDUOBEI_TASK.JINCHANBAOKU
            ---- 三星必掉
            --for i, v in ipairs(split_tab) do
            --    if tonumber(v) == data.item_id then
            --        data.is_three_must_drop = true
            --    end
            --end
            table.insert(ret_data_list , data)
        else
            break
        end
    end

    return ret_data_list
end

function CopperFbWGData:GetStarTimeList()
    local role_level = self:_FindLevel()
    local list = {}
    for i, v in pairs(self.reward_cfg) do
        if v.level == role_level then
            list[v.star_num] = v.time_length
        end
    end
    return list
end

function CopperFbWGData:GetEWaiItemList(star_num)
    local role_level = self:_FindLevel()
    local cfg = nil
    for i, v in pairs(self.reward_cfg) do
        if v.level == role_level and v.star_num == star_num then
            cfg = v
        end
    end
    local ret_data_list = {}
    local three_e_wai = cfg.item_1
    for i = 0, 10 do
        if three_e_wai[i] then
            local data = {}
            data.item_id = three_e_wai[i].item_id
            data.is_bind = three_e_wai[i].is_bind
            data.num = three_e_wai[i].num --策划要求只显示这个物品的数量（lsq）
            data.show_duobei = true
            data.task_type = RATSDUOBEI_TASK.JINCHANBAOKU
            data.is_three_must_drop = true
            table.insert(ret_data_list , data)
        else
            break
        end
    end
    return ret_data_list
end

function CopperFbWGData:GetDropList(item_list, star_num, get_item_num)
    local ret_data_list = {}
    for i = 0, 20 do
        if item_list[i] then
            table.insert(ret_data_list, item_list[i])
        else
            break
        end
    end
    local ewai_item_list = self:GetEWaiItemList(star_num)
    for i, v in ipairs(ewai_item_list) do
        local data = {}
        data.item_id = v.item_id
        data.is_bind = v.is_bind
        data.num = get_item_num --目前只有一个
        data.show_duobei = true
        data.task_type = RATSDUOBEI_TASK.JINCHANBAOKU
        data.is_three_must_drop = true
        table.insert(ret_data_list, data)
    end
    return ret_data_list
end

--查找等级段
function CopperFbWGData:_FindLevel()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    for i = #self.reward_cfg, 1, -1 do
        if role_level > self.reward_cfg[i].level then
            return self.reward_cfg[i].level
        end
    end
    return self.reward_cfg[1].level
end

--设置基本信息
function CopperFbWGData:SetTongBiInfo(protocol)
	--print_error("龙王宝藏副本返回信息",protocol)
	self.copper_msg_info = {}
	self.copper_msg_info.max_pass_layer = protocol.max_pass_layer
	self.copper_msg_info.day_has_enter_times = protocol.day_has_enter_times
	self.copper_msg_info.day_buy_times = protocol.day_buy_times
	self.copper_msg_info.day_star_num = protocol.day_star_num
end
function CopperFbWGData:GetTongBiInfo()
	return self.copper_msg_info
end
--获取购买次数消耗元宝
function CopperFbWGData:GetBuyComsumeGold()
	return self.other_cfg.buy_times_need_gold
end

function CopperFbWGData:CheckRemainTimes()
    local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
    if not copper_info then return false end
	local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
	local remian_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
    return remian_times > 0
end

--设置场景信息
function CopperFbWGData:SetCopperScenceInfo(protocol)
	self.copper_scence_info = {}
	self.copper_scence_info.is_leave_fb = protocol.is_leave_fb
	self.copper_scence_info.cur_wave = protocol.cur_wave
	self.copper_scence_info.is_pass = protocol.is_pass
	self.copper_scence_info.cur_star_num = protocol.cur_star_num
    self.copper_scence_info.total_wave = protocol.total_wave
	self.copper_scence_info.cur_layer = protocol.cur_layer
	self.copper_scence_info.is_sweep = protocol.is_sweep
	self.copper_scence_info.get_coin = protocol.get_coin
	self.copper_scence_info.get_item_num = protocol.get_item_num
	self.copper_scence_info.star_reward_coin = protocol.star_reward_coin
	self.copper_scence_info.get_exp = protocol.get_exp
	self.copper_scence_info.time_out_timestamp = protocol.time_out_timestamp
	self.copper_scence_info.next_star_timestamp = protocol.next_star_timestamp
	self.copper_scence_info.scene_timeout_timestamp = protocol.scene_timeout_timestamp
    self.copper_scence_info.prepare_end_timestamp = protocol.prepare_end_timestamp
    self.copper_scence_info.is_finish = protocol.is_finish
    self.copper_scence_info.item_total_count = protocol.item_total_count
    self.copper_scence_info.item_list = protocol.item_list
end
function CopperFbWGData:GetCopperScenceInfo()
	return self.copper_scence_info
end

function CopperFbWGData:GetBuyTimesComsumeGold(will_buy_times)
	if self.buy_times_cfg[will_buy_times] then
		return self.buy_times_cfg[will_buy_times].price
	end
	if will_buy_times > self.max_buy_times then
		return self.buy_times_cfg[self.max_buy_times].price
	end
	return self.buy_times_cfg[1].price
end