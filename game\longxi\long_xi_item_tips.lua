LongXiItemTips = LongXiItemTips or BaseClass(SafeBaseView)

function LongXiItemTips:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/long_xi_ui_prefab", "longxi_item_tips")
    self:SetMaskBg(true, true)
    self.longxi_activiti_type = nil
end

function LongXiItemTips:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_cell)
        self.item_cell:SetIsShowTips(false)
    end
end

function LongXiItemTips:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function LongXiItemTips:SetDataAndOpen(longxi_activiti_type)
	self.longxi_activiti_type = longxi_activiti_type
    self:Open()
end

function LongXiItemTips:ShowIndexCallBack()

end

function LongXiItemTips:OnFlush()
    local base_data = LongXiWGData.Instance:GetLongXiBaseInfoByType(self.longxi_activiti_type)
    local attr_list = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(base_data, "attr_id", "attr_value")
    local max_attr_list = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(base_data, "max_attr_id", "max_attr_value")

    for i = 1, 4 do
        self.node_list["attr_" .. i]:FindObj("attr_name").text.text = attr_list[i].attr_name
        self.node_list["attr_" .. i]:FindObj("attr_value").text.text = attr_list[i].value_str
    end

    for i = 1, 4 do
        self.node_list["max_attr_" .. i]:FindObj("attr_name").text.text = max_attr_list[i].attr_name
        self.node_list["max_attr_" .. i]:FindObj("attr_value").text.text = max_attr_list[i].value_str
    end

    self.item_cell:SetData({item_id = base_data.show_item_id})

    local item_cfg = ItemWGData.Instance:GetItemConfig(base_data.show_item_id)
    if item_cfg then
        local color = item_cfg.color or 1

        self.node_list.title.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[color])
        self.node_list.desc1.text.text = string.format(Language.LongXi.Desc1, item_cfg.name)
        self.node_list.desc2.text.text = string.format(Language.LongXi.Desc2, item_cfg.name)

        self.node_list["top_color_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_ty_tips_top_" .. color))

        local rich_level = ""
        local rich_type = ""

        rich_level = string.format(Language.Tip.ShiYongDengJiNew, TIPS_COLOR.SOCRE, RoleWGData.GetLevelString(item_cfg.limit_level))
        self.node_list.item_type.text.text = rich_level

        rich_type = string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.ShowItemType[item_cfg.goods_type])
        self.node_list.item_synthetical_socre.text.text = rich_type
    end
end