MarryXunYuanTipView = MarryXunYuanTipView or BaseClass(SafeBaseView)

function MarryXunYuanTipView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_xunyuan_tip")
end

function MarryXunYuanTipView:__delete()
end

function MarryXunYuanTipView:ReleaseCallBack()
    self:ClearTimeLimit()
    if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function MarryXunYuanTipView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_open_xunyuan, BindTool.Bind(self.OnClickOpenXunYuanBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.OnClickCloseBtn, self))
    self.head_cell = BaseHeadCell.New(self.node_list["head_pos"])
    self.head_cell:SetBgActive(false)
end

function MarryXunYuanTipView:OpenCallBack()

end

function MarryXunYuanTipView:SetData(data)
    self.data = data
end

function MarryXunYuanTipView:OnFlush()
    self:FlushView()
end

function MarryXunYuanTipView:FlushView()
    self:ClearTimeLimit()
    self.timer_limit = GlobalTimerQuest:AddTimesTimer(function ()
        self:CloseView()
    end, 5, 1)

    self.node_list.name.text.text = self.data.user_name
    if #self.data.zhenghun_info <= 24 then
        self.node_list.desc.text.text = string.format(Language.Marry.XunYuanTipDesc1, self.data.zhenghun_info)
    else
        local str = self.data.zhenghun_info:sub(1, 24)
        self.node_list.desc.text.text = string.format(Language.Marry.XunYuanTipDesc2, str)
    end

    local data = {}
	data.role_id = self.data.user_id
	data.sex = self.data.sex
	data.prof = self.data.prof
	self.head_cell:SetData(data)
end

function MarryXunYuanTipView:OnClickOpenXunYuanBtn()
    ViewManager.Instance:Open(GuideModuleName.ProfessWallView, TabIndex.profess_wall_xunyuan, "jump_page", {page = self.data.page})
    self:CloseView()
end

function MarryXunYuanTipView:OnClickCloseBtn()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt("MarryXunYuanTipView" .. main_role_id, 1)

    self:ClearTimeLimit()
	self:CloseView()
end

function MarryXunYuanTipView:CloseView()
	UITween.MoveToShowPanel(GuideModuleName.ProfessWallView, self.node_list.content, Vector2(0, 0), Vector2(479, 0), 0.3, DG.Tweening.Ease.Linear, function ()
        self:Close()
    end)
end

function MarryXunYuanTipView:ClearTimeLimit()
	if self.timer_limit then
		GlobalTimerQuest:CancelQuest(self.timer_limit)
		self.timer_limit = nil
	end
end