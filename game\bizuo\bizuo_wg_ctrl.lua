require("game/bizuo/bizuo_wg_data")
require("game/bizuo/bizuo_all_view")
require("game/bizuo/bizuo_view")
require("game/bizuo/bizuo_find_view")
require("game/bizuo/bizuo_activity_hall_view")
require("game/bizuo/bizuo_activity_desc_view")
require("game/bizuo/bizuo_lingpai_view")
require("game/bizuo/bizuo_get_stronger_view")
require("game/bizuo/bizuo_cultivation_view")
require("game/welfare/dailyfind_view")
require("game/welfare/user_batch_view")
require("game/welfare/welfare_zhaohui_reward_view")

require("game/bizuo/week_calendar_view")
require("game/bizuo/act_forecast_view")
require("game/bizuo/bizuo_guild_finish_tip_view")

require("game/bizuo/bizuo_cross_server_pro_view")--跨服进度
require("game/bizuo/compound_country_view")--合国进度界面

BiZuoWGCtrl = BiZuoWGCtrl or BaseClass(BaseWGCtrl)

function BiZuoWGCtrl:__init()
	if BiZuoWGCtrl.Instance then
		error("[BiZuoWGCtrl]:Attempt to create singleton twice!")
	end
	BiZuoWGCtrl.Instance = self

	self.view = BiZuoView.New(GuideModuleName.BiZuo)
	self.data = BiZuoWGData.New()
	self.find_view = BiZuoFindView.New()
	self.user_batch_view = BiZuoBatchView.New()
	self.lingpai_view = BiZuoLPView.New()
	self.welfare_reward_view =BiZuoRewardView.New()
	self.activity_desc_view = BiZuoActivityDescView.New()

	self.act_forecast_view = ActForecastView.New()
	self.guild_weekly_task_finish_view = BiZuoGuildTaskFinishTip.New()
	self.compound_country_view = CompoundCountryView.New()
	self:RegisterAllProtocals()
	self.can_open_view = false

	self.auto_join_flag = false

	self.week_guild_task_flag = 0

	self.temp_flag = {}
end

function BiZuoWGCtrl:__delete()
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.find_view then
		self.find_view:DeleteMe()
		self.find_view = nil
	end
	if self.lingpai_view then
		self.lingpai_view:DeleteMe()
		self.lingpai_view = nil
	end
	if self.user_batch_view then
		self.user_batch_view:DeleteMe()
		self.user_batch_view = nil
	end

	if self.welfare_reward_view then
		self.welfare_reward_view:DeleteMe()
		self.welfare_reward_view = nil
	end

	if self.activity_desc_view then
		self.activity_desc_view:DeleteMe()
		self.activity_desc_view = nil
	end

	if self.act_forecast_view then
		self.act_forecast_view:DeleteMe()
		self.act_forecast_view = nil
	end

	if self.guild_weekly_task_finish_view then
		self.guild_weekly_task_finish_view:DeleteMe()
		self.guild_weekly_task_finish_view = nil
	end

	self.can_open_view = nil

	GlobalTimerQuest:CancelQuest(self.check_iscan_show)
	self.check_iscan_show = nil

	self.temp_flag = nil

	self.auto_join_flag = nil

	BiZuoWGCtrl.Instance = nil
end

-- 注册协议
function BiZuoWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSDailyWorkOperaReq)
	self:RegisterProtocol(SCDailyWorkAllInfo, "OnBiZuoAllInfo")
	--self:RegisterProtocol(SCDailyWorkLevelInfo, "OnBiZuoWorkInfo")
	self:RegisterProtocol(SCDailyFindItemChange, "OnDailyFindItemChange") 			-- 日常找回单项变更
	self:RegisterProtocol(SCDailyWorkSignupInfo, "OnSCDailyWorkSignupInfo")			-- 6796 每日必做报名信息返回
end

function BiZuoWGCtrl:OnBiZuoAllInfo(protocol)
	self.data:SetAllInfo(protocol)

	if self.find_view:IsOpen() then
		self.find_view:Flush()
	end
	RemindManager.Instance:Fire(RemindName.Daily)
	RemindManager.Instance:Fire(RemindName.BiZuo)

	if self.check_iscan_show == nil then
		self.check_iscan_show = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.CheckDtaCount,self),1)
	end

	self:AutoJoinAct()
	self:ChackWeekGuild()
	--RemindManager.Instance:Fire(RemindName.ActivityHall)

	if self.view:IsOpen() then
		self.view:Flush()
	end

	if SecretRecordWGCtrl.Instance then
		SecretRecordWGCtrl.Instance:OnBottleInfo(protocol)
	end

	if SecretRecordWGCtrl.Instance then
		DiZangRedPackWGCtrl.Instance:CheckRedpackOpen()
	end
	
	ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView, nil, "flush_cell")
end

-- function BiZuoWGCtrl:OnBiZuoWorkInfo(protocol)
-- 	self.data:SetLevelInfo(protocol)
-- 	self.view:Flush(BiZuoView.TabIndex.BB, "uplevel")
-- 	if self.lingpai_view:IsOpen() then
-- 		self.lingpai_view:FlushLeftView()
-- 		self.lingpai_view:PlayEffect()
-- 	end
-- 	RemindManager.Instance:Fire(RemindName.Daily)
-- 	RemindManager.Instance:Fire(RemindName.BiZuo)
-- 	--RemindManager.Instance:Fire(RemindName.ActivityHall)
-- end

function BiZuoWGCtrl:ChackWeekGuild()
	local new_flag = self.data:GetWeekGuildUpDataStatus()
	if self.week_guild_task_flag == 1 and new_flag == 0 then
		self:OpenGuildTaskFinishView()
	end
	self.week_guild_task_flag = new_flag
end

function BiZuoWGCtrl:SendBiZuoOperate(opera_type, sword_type, level, is_charge, find_type, find_all)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSDailyWorkOperaReq)
	send_protocol.opera_type = opera_type or 0
	send_protocol.sword_type = sword_type or 0
	send_protocol.level = level or 0
	send_protocol.is_charge = is_charge or 0
	send_protocol.find_type = find_type or 0 		--param1
	send_protocol.find_all = find_all or 0 			--param2
	send_protocol:EncodeAndSend()
end

function BiZuoWGCtrl:Open(tab_index, param_t,table)
	if self.view then
		self.view:Open(tab_index, param_t)
	end
end

function BiZuoWGCtrl:SetViewTipsFlag(boo)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function BiZuoWGCtrl:Close()
	if self.view:IsOpen() then
		self.view:Close()
	end
end

function BiZuoWGCtrl:Flush()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

-- 打开找回
function BiZuoWGCtrl:OpenFindView()
	self.find_view:Open()
end


--打开令牌界面
function BiZuoWGCtrl:OpenLingPaiView()
	if self.lingpai_view then
		self.lingpai_view:Open()
	end
end

function BiZuoWGCtrl:OnDailyFindItemChange(protocol)
	if protocol.result == 1 then
		self.data:RemoveDailyFindItem(DAILYFIND_TYPE.TASK, protocol.dailyfind_type)
	end
	self.view:Flush(WELFARE_TYPE.DAILYFIND)
end


function BiZuoWGCtrl:OpenBatchView(item_id)
	self.user_batch_view:SetData(item_id)
end

function BiZuoWGCtrl:OpenRewardView()
	self.welfare_reward_view:Open()

end

function BiZuoWGCtrl:OpenApplySucceedView(act_seq)
	local signup_cfg = self.data:GetSignupConfigByActSeq(act_seq)
	local cfg = self.data:GetActivityHallCfgByActType(signup_cfg.act_type)
	if not IsEmptyTable(cfg) then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.BiZuo.Signup_Succeed_Desc_3, cfg.name))
	end
end

function BiZuoWGCtrl:OnSCDailyWorkSignupInfo(protocol)
	if protocol.is_succ == 1 then
		self:OpenApplySucceedView(protocol.act_seq)
		--由于发activity_hall_signup_flag字段的协议信息太多,直接在这边做位操作,protocol.act_seq是当前报名成功的索引
		self.data.activity_hall_signup_flag[32 - protocol.act_seq] = 1
	end
	--报名失败也刷一下界面,防止界面刷新不及时
	self:FlushActHall()
end

function BiZuoWGCtrl:FlushActHall()
	if self.view:IsOpen() then
		self.view:Flush(BiZuoView.TabIndex.BAH)
		self.view:Flush()
	end
	--RemindManager.Instance:Fire(RemindName.ActivityHall)
end

function BiZuoWGCtrl:CheckDtaCount()
	local data = self.data:GetActForecastData()
	if not data or IsEmptyTable(data) then return end
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local timer = os.date("%X",server_time)
	local flag = "act_forecast"
	for k,v in pairs(data) do
		if timer >= v.begin_time and timer < v.end_time then
			if self.temp_flag[k] == nil then
				local activity_cfg = DailyWGData.Instance:GetActivityConfig(v.act_type)
				MainuiWGCtrl.Instance:AddTempActIcon(v.act_type, nil, activity_cfg,COLOR3B.BLUE, flag)
				self.temp_flag[k] = 1
			end
		else
			if self.temp_flag[k] == 1 then
				MainuiWGCtrl.Instance:DelTempActIcon(v.act_type)
				self.temp_flag[k] = nil
				GlobalTimerQuest:CancelQuest(self.check_iscan_show)
			-- elseif self.temp_flag[k] == nil then
			-- 	MainuiWGCtrl.Instance:DelTempActIcon(v.act_type)
			end
		end
	end
end

function BiZuoWGCtrl:OpenActForecastView(act_type)
	if not self.act_forecast_view:IsOpen() then
		self.act_forecast_view:SetActType(act_type)
		self.act_forecast_view:Open()
	end
end

--报名过的活动自动进入该活动的活动场景
function BiZuoWGCtrl:AutoJoinAct()
	local data = self.data.task_cfg
	local scene_type = Scene.Instance:GetSceneType()
	if IS_ON_CROSSSERVER or scene_type ~= SceneType.Common then
		self.auto_join_flag = true
	end

	if IsEmptyTable(data) or self.auto_join_flag == true then
		return
	end

	local role_level = RoleWGData.Instance.role_vo.level
	for k,v in pairs(data) do
		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(v.act_type)
		if role_level >= act_cfg.limit_level and role_level < act_cfg.level_max then
			local signup_cfg = self.data:GetSignupConfigByActType(v.act_type)
			if signup_cfg and ActivityWGData.Instance:GetActivityIsOpen(v.act_type) and self.data:GetIsSignUpFlagBySeq(signup_cfg.seq) then
				ActivityWGCtrl.Instance:AutoJoinActByType(v.act_type)
				break
			end
		end
	end

	self.auto_join_flag = true
end


function BiZuoWGCtrl:TaskInfoViewShow(data)
	self.activity_desc_view:SetData(data)
	self.activity_desc_view:Open()
end


function BiZuoWGCtrl:OpenGuildTaskFinishView()
	if nil == self.guild_weekly_task_finish_view then
		self.guild_weekly_task_finish_view = BiZuoGuildTaskFinishTip.New()
	end
	self.guild_weekly_task_finish_view:Open()
	self.guild_weekly_task_finish_view:Flush()
end