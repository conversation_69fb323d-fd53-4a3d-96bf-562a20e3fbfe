local MENGLING_SUIT_EFFECT2 = {
    [1] = "UI_lingzhen_saoguang_lan1",
    [2] = "UI_lingzhen_saoguang_lan1",
    [3] = "UI_lingzhen_saoguang_zi1",
    [4] = "UI_lingzhen_saoguang_cheng1",
    [5] = "UI_lingzhen_saoguang_hong1",
    [6] = "UI_lingzhen_saoguang_fen1",
    [7] = "UI_lingzhen_saoguang_jin1",
    [8] = "UI_lingzhen_saoguang_cai1",
}

local MENGLING_SUIT_EFFECT1 = {
    [1] = "UI_lingzhen_saoguang_lan2",
    [2] = "UI_lingzhen_saoguang_lan2",
    [3] = "UI_lingzhen_saoguang_zi2",
    [4] = "UI_lingzhen_saoguang_cheng2",
    [5] = "UI_lingzhen_saoguang_hong2",
    [6] = "UI_lingzhen_saoguang_fen2",
    [7] = "UI_lingzhen_saoguang_jin2",
    [8] = "UI_lingzhen_saoguang_cai2",
}

function MengLingView:InitMengLIngCommonLeftView()
    if not self.mengling_equip_item_list then
        self.mengling_equip_item_list = {}

        for i = 0, 10 do
            local render = MengLingEquipItemRender.New(self.node_list["mengling_equip_item" .. i])
            render:SetIndex(i)
            render:SetClickCallBack(BindTool.Bind(self.OnClickMengLingEquipItem, self))
            self.mengling_equip_item_list[i] = render
        end
    end

    if not self.mengling_equip_suit_list then
        self.mengling_equip_suit_list = AsyncListView.New(MengLingEquipSuitRender, self.node_list.mengling_equip_suit_list)
        self.mengling_equip_suit_list:SetStartZeroIndex(false)
        self.mengling_equip_suit_list:SetSelectCallBack(BindTool.Bind(self.OnClickMengLingEquipSuitItem, self))
    end

    if not self.sxd_list then
        self.sxd_list = {}

        for i = 1, 4 do
            local render = MengLingSXDRender.New(self.node_list["sxd_" .. i])
            self.sxd_list[i] = render
        end
    end

    for i = 1, 2 do
        local suit_data = MengLingWGData.Instance:GetMengLingSuitCfgBySuit(i)
        self.node_list["mengling_suit_name" .. i].text.text = suit_data and suit_data.suit_name or ""
    end

    XUI.AddClickEventListener(self.node_list.btn_attr_add, BindTool.Bind(self.OnClickAttrAddBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_suit_attr, BindTool.Bind(self.OnClickSuitAttrBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_mengling_skill, BindTool.Bind(self.OnClickMengLingSkillBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_resolve, BindTool.Bind(self.OnClickResolveBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_jump_to_pw, BindTool.Bind(self.OnClickJumpToPWBtn, self))
end

function MengLingView:ReleaseCommonLeftViewCallBack()
    if self.mengling_equip_suit_list then
        self.mengling_equip_suit_list:DeleteMe()
        self.mengling_equip_suit_list = nil
    end

    if self.mengling_equip_item_list then
        for k, v in pairs(self.mengling_equip_item_list) do
            v:DeleteMe()
        end

        self.mengling_equip_item_list = nil
    end

    if self.sxd_list then
        for k, v in pairs(self.sxd_list) do
            v:DeleteMe()
        end

        self.sxd_list = nil
    end

    self:CancelTween()
end

function MengLingView:OnFlushMengLingCommonView()
    -- 套装
    local grade_data_list = MengLingWGData.Instance:GetMengLingShowGradeDataList()
    self.mengling_equip_suit_list:SetDataList(grade_data_list)
    self.mengling_equip_suit_list:JumpToIndex(self:GetMengLingEquipSuitSelectIndex())

    --技能
    self:FlushMengLingMidSkill()

    --属性丹
    local stone_cfg = MengLingWGData.Instance:GetStoneCfg()
    for i = 1, 4 do
        self.sxd_list[i]:SetData(stone_cfg[i])
    end

    -- local cap = MengLingWGData.Instance:GetMengLingTotalAttrInfo(true)
    -- self.node_list.cap_value.text.text = cap
end

function MengLingView:CheckShowResolveBtn(index)
    if self.node_list and self.node_list.btn_resolve then
        self.node_list.btn_resolve:SetActive(index == TabIndex.mengling_bag)
    end
end

function MengLingView:FlushMidSuitEffect()
    if self.select_equip_suit_seq then
        local suit_data = MengLingWGData.Instance:GetMengLingSuitInfo(self.select_equip_suit_seq)
        local effect_color = GameEnum.ITEM_COLOR_WHITE

        for k, v in pairs(suit_data) do
            local suit_effect_cfg = (k == 1) and MENGLING_SUIT_EFFECT1 or MENGLING_SUIT_EFFECT2

            if not IsEmptyTable(v) and v.color and v.color > GameEnum.ITEM_COLOR_WHITE and suit_effect_cfg[v.color] then
                local bundle, asset = ResPath.GetEffect(suit_effect_cfg[v.color])

                local attach = self.node_list["lingzhen_suit_effect" .. k].gameObject:GetComponent(typeof(Game.GameObjectAttach))
                attach.BundleName = bundle
                attach.AssetName = asset
                self.node_list["lingzhen_suit_effect" .. k]:CustomSetActive(false)
                self.node_list["lingzhen_suit_effect" .. k]:CustomSetActive(true)
            else
                self.node_list["lingzhen_suit_effect" .. k]:CustomSetActive(false)
            end
        end
    end
end

function MengLingView:FlushMengLingMidSkill()
    if self.select_equip_suit_seq then
        local suit_cfg = MengLingWGData.Instance:GetGradeCfgBySeq(self.select_equip_suit_seq)
        local skill_unlock = MengLingWGData.Instance:IsMengLingSuitSkillActive(self.select_equip_suit_seq)
        local icon_asset_name = (skill_unlock and "a3_lq_icon_" or "a3_lq_icon_m_") .. suit_cfg.skill_icon
        local bunlde, asset = ResPath.GetMengLingImg(icon_asset_name)
        self.node_list.mengling_skill_icon.image:LoadSprite(bunlde, asset, function ()
            self.node_list.mengling_skill_icon.image:SetNativeSize()
        end)

        self.node_list.mengling_skill_lock:CustomSetActive(not skill_unlock)
        self.node_list.ml_circle3:CustomSetActive(not skill_unlock)
        self.node_list.lingzhen_skill_effect:CustomSetActive(skill_unlock)
    end
end

-- 梦灵套装自动选中
function MengLingView:GetMengLingEquipSuitSelectIndex()
    if self.show_index == TabIndex.mengling_bag then
        if self.select_mengling_equip_item_index then
            local wear_data = MengLingWGData.Instance:GetMengLingOneKeyWearDataList(self.select_equip_suit_seq)
            if not IsEmptyTable(wear_data) then
                return self.select_mengling_equip_item_index
            end
        end

        for k, v in pairs(MengLingWGData.Instance:GetMengLingShowGradeDataList()) do
            local wear_data = MengLingWGData.Instance:GetMengLingOneKeyWearDataList(v.seq)
    
            if not IsEmptyTable(wear_data) then
                return k
            end
        end
    elseif self.show_index == TabIndex.mengling_strong then
        if self.select_mengling_equip_item_index then
            if MengLingWGData.Instance:IsCanStrongMengLingSuit(self.select_equip_suit_seq) then
                return self.select_mengling_equip_item_index
            end
        end

        for k, v in pairs(MengLingWGData.Instance:GetMengLingShowGradeDataList()) do
            if MengLingWGData.Instance:IsCanStrongMengLingSuit(v.seq) then
                return k
            end
        end
    end

    if self.select_mengling_equip_item_index then
        return self.select_mengling_equip_item_index
    end

    return 1
end

-- 点击套装
function MengLingView:OnClickMengLingEquipSuitItem(item)
    if nil == item or nil == item.data then
        return
    end

    local data = item.data
    self.select_mengling_equip_item_index = item.index
    self.select_equip_suit_seq = data.seq
    self.select_mengling_equip_item_data = data

    local cap = MengLingWGData.Instance:GetMengLingGradeTotalAttrInfo(self.select_equip_suit_seq, true)
    self.node_list.cap_value.text.text = cap

    -- 刷新中间格子数据
    self:FlushCommonMidInfo()
    self:FlushMengLingMidSkill()
    self:FlushMidSuitEffect()

    if self.show_index == TabIndex.mengling_bag and self:IsLoadedIndex(TabIndex.mengling_bag) then
        -- self:FlushMengLingBagWearRemind()
        self:FlushMengLingBagPanel()
    elseif self.show_index == TabIndex.mengling_suit and self:IsLoadedIndex(TabIndex.mengling_suit) then
        self:OnFlushMengLingSuitPanel()
    end
end

function MengLingView:FlushCommonMidInfo()
    local mid_equip_data_list = MengLingWGData.Instance:GetMengLingSuitItemDataBy(self.select_equip_suit_seq)
    for k, v in pairs(mid_equip_data_list) do
        if self.mengling_equip_item_list[k] then
            self.mengling_equip_item_list[k]:SetData(v)
        end
    end

    -- 选中
    local select_index = self:GetMidEquipItemSelect()
    if self.mengling_equip_item_list[select_index] then
        self:OnClickMengLingEquipItem(self.mengling_equip_item_list[select_index], true)
    end
end

-- 梦灵套装slot自动选中
function MengLingView:GetMidEquipItemSelect()
    if self.show_index == TabIndex.mengling_strong then
        if self.select_mengling_item_index then
            if MengLingWGData.Instance:IsCanStrongMengLingSuitEquip(self.select_equip_suit_seq, self.select_equip_item_slot) then
                return self.select_mengling_item_index
            end
        end

        local mid_equip_data_list = MengLingWGData.Instance:GetMengLingSuitItemDataBy(self.select_equip_suit_seq)
        for k, v in pairs(mid_equip_data_list) do
            if MengLingWGData.Instance:IsCanStrongMengLingSuitEquip(self.select_equip_suit_seq, v.slot) then
                return k
            end
        end

        -- 要选中一个存在的
        local slot_data = MengLingWGData.Instance:GetMengLingSuitSlotItemData(self.select_equip_suit_seq, self.select_equip_item_slot)
        if slot_data and slot_data.item_id > 0 then
            return self.select_mengling_item_index
        end

        local slot_cache_data = {}
        local index_cache = -1
        for k, v in pairs(mid_equip_data_list) do
            local slot_data = MengLingWGData.Instance:GetMengLingSuitSlotItemData(self.select_equip_suit_seq, v.slot)
            
            if slot_data and slot_data.item_id > 0 and (IsEmptyTable(slot_cache_data) or (slot_cache_data.level < slot_data.level)) then
                slot_cache_data = slot_cache_data
                index_cache = k
            end
        end

        if index_cache >= 0 then
            return index_cache
        end
    end

    if self.select_mengling_item_index then
        return self.select_mengling_item_index
    end

    return 0
end

-- 点击梦灵装备
function MengLingView:OnClickMengLingEquipItem(item, ignore_tip)
    if nil == item or IsEmptyTable(item.data) then
        return
    end
    
    local index = item:GetIndex()
    local data = item:GetData()
    local select_old_data = self.select_equip_item_data

    self.select_mengling_item_index = index
    self.select_equip_item_slot = data.slot
    self.select_equip_item_data = data

    for i = 0, 10 do
        self.mengling_equip_item_list[i]:OnSelectChange(index == i)
    end

    if self.show_index == TabIndex.mengling_strong and self:IsLoadedIndex(TabIndex.mengling_strong) then
        self:FlushMengLingStrengthPanel()
    end

    if ignore_tip then
        return
    end

    if data.item_id > 0 then
        if (select_old_data.seq == data.seq and select_old_data.slot == data.slot) then
            TipWGCtrl.Instance:OpenItem(data)--, ItemTip.FROM_MENGLING_EQUIP)
        end
    else
        local default_item_id = MengLingWGData.Instance:GetDefaultShowItemId(data.seq, data.slot)
        if default_item_id and default_item_id > 0 then
            TipWGCtrl.Instance:OpenItem({item_id = default_item_id})
        else
            local get_way = MengLingWGData.Instance:GetMengLingOtherCfg("get_way")
            TipWGCtrl.Instance:OpenEquipGetWayView(get_way)
        end
    end
end

function MengLingView:OnClickAttrAddBtn()
    -- local cap, attr_list = MengLingWGData.Instance:GetMengLingTotalAttrInfo(false)
    local cap, attr_list = MengLingWGData.Instance:GetMengLingGradeTotalAttrInfo(self.select_equip_suit_seq, true)

    local suit_cfg = MengLingWGData.Instance:GetGradeCfgBySeq(self.select_equip_suit_seq)

    local tips_data = {
        title_text = string.format(Language.MengLing.AttrAddTitle, suit_cfg.name),
        attr_data = attr_list,   --  { attr_str, attr_value }
    }

    TipWGCtrl.Instance:OpenTipsAttrView(tips_data)
end

function MengLingView:OnClickSuitAttrBtn()
    MengLingWGCtrl.Instance:OpenMengLingSuitView(self.select_equip_suit_seq)
end

function MengLingView:OnClickMengLingSkillBtn()
    if IsEmptyTable(self.select_mengling_equip_item_data) then
        return
    end

    local attr_list, cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(self.select_mengling_equip_item_data, "attr_id", "attr_value", nil, nil, 1, 7, false)
    local body_text = ""

    for i = 1, #attr_list do
        local data = attr_list[i]
        body_text = string.format(Language.MengLing.SkillAttrDesc, body_text, data.attr_name, data.value_str)

        if i ~= #attr_list then
            body_text = body_text .. "\n"
        end
    end

    -- for k, v in pairs(attr_list) do
    --     body_text = string.format(Language.MengLing.SkillAttrDesc, body_text, v.attr_name, v.value_str)
    -- end

    local limit_text = ""
    local skill_unlock = MengLingWGData.Instance:IsMengLingSuitSkillActive(self.select_equip_suit_seq)
    if not skill_unlock then
        local skill_active_color = self.select_mengling_equip_item_data.skill_active_color
        local color_str = Language.Common.ColorName[skill_active_color]-- ToColorStr(Language.Common.ColorName[skill_active_color], skill_active_color) -- ITEM_COLOR[skill_active_color])
        limit_text = string.format(Language.MengLing.ActiveSkillDesc, color_str)
    end

	local show_data = {
        icon_res_fun = ResPath.GetMengLingImg,
		icon = self.select_mengling_equip_item_data.skill_icon_res,
        top_text = self.select_mengling_equip_item_data.skill_name,
        hide_level = true,
        capability = cap,
        body_text = body_text,
		limit_text = limit_text,
		x = 0,
		y = 0,
		set_pos2 = true,
		show_level = true,
	}

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

function MengLingView:OnClickResolveBtn()
    MengLingWGCtrl.Instance:OpenMengLingResolveView()
end

-- 技能激活特效
function MengLingView:DoUnLockSkillEffect()
    self.node_list.lingzhen_jihuo_effect:CustomSetActive(true)

    local skill_icon_root = self.node_list.mengling_skill_icon
    skill_icon_root.rect.localScale = Vector3(1, 1, 1)
    self:CancelTween()
    local show_tweener = DG.Tweening.DOTween.Sequence()
    show_tweener:Append(skill_icon_root.transform:DOScale(Vector3(3, 3, 3), 0.3))
    show_tweener:Append(skill_icon_root.transform:DOScale(Vector3(1, 1, 1), 0.3))

    show_tweener:OnComplete(function()
        skill_icon_root.rect.localScale = Vector3(1, 1, 1)
        self.node_list.lingzhen_jihuo_effect:CustomSetActive(false)
	end)

    self.enter_play_tween = show_tweener
end

function MengLingView:CancelTween()
    if self.enter_play_tween then
        self.enter_play_tween:Kill()
        self.enter_play_tween = nil
    end
end

function MengLingView:DoUnLockSuitEffect(active_data)
    -- local old_suit_effect1 = self.node_list.lingzhen_suit_effect1:GetActive()
    -- local old_suit_effect2 = self.node_list.lingzhen_suit_effect2:GetActive()

    for i = 0, 10 do
        self.node_list["lingzhen_equip_effect" .. i]:CustomSetActive(false)
    end

    local suit_active1 = active_data[1].active
    local suit_active2 = active_data[2].active

    -- self.node_list.lingzhen_suit_effect1:CustomSetActive(false)
    -- self.node_list.lingzhen_suit_effect2:CustomSetActive(false)

    local end_func = function()
        for i = 0, 10 do
            self.node_list["lingzhen_equip_effect" .. i]:CustomSetActive(false)
        end

        -- self.node_list.lingzhen_suit_effect1:CustomSetActive(old_suit_effect1 or suit_active1)
        -- self.node_list.lingzhen_suit_effect2:CustomSetActive(old_suit_effect2 or suit_active2)
    end

    local total_time = suit_active1 and 6 or 5
    local index = 0

    local need_show_star_effect = suit_active1 or suit_active2
    -- if old_suit_effect1 ~= suit_active1 or old_suit_effect2 ~= suit_active2 then
    --     need_show_star_effect = true
    -- end

    if need_show_star_effect then
        GlobalTimerQuest:InvokeRepeating(function()
            if suit_active1 then
                if  self.node_list["lingzhen_equip_effect" .. index] then
                    self.node_list["lingzhen_equip_effect" .. index]:CustomSetActive(true)
                end
            end
    
            if suit_active2 then
                if  self.node_list["lingzhen_equip_effect" .. (6 + index)] then
                    self.node_list["lingzhen_equip_effect" .. (6 + index)]:CustomSetActive(true)
                end
            end
    
            index = index + 1
    
            if index > total_time then
                end_func()
            end
        end, 0, 0.3, total_time)
    else
        end_func()
    end
end

function MengLingView:OnClickJumpToPWBtn()
    ViewManager.Instance:Open(GuideModuleName.PositionalWarfareView)
end

--------------------------------------MengLingEquipSuitRender----------------------------------
MengLingEquipSuitRender = MengLingEquipSuitRender or BaseClass(BaseRender)

function MengLingEquipSuitRender:LoadCallBack()
    for i = 1, 2 do
        local suit_data = MengLingWGData.Instance:GetMengLingSuitCfgBySuit(i)
        local name_str = suit_data and suit_data.suit_name or ""
        self.node_list["suit_name" .. i].text.text = name_str
        self.node_list["suit_hl_name".. i].text.text = name_str
     end
end

function MengLingEquipSuitRender:OnFlush()
    if not self.data then
        return
    end

    local icon_bundle, icon_asset = ResPath.GetMengLingImg(self.data.icon)
    self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
        self.node_list.icon.image:SetNativeSize()
    end)

    self.node_list.name.text.text = self.data.name
    self.node_list.name_hl.text.text = self.data.name

    local remind = false
    local mengling_show_index = MengLingWGCtrl.Instance:GetMengLingShowIndex()

    if mengling_show_index == TabIndex.mengling_bag then
        local wear_data = MengLingWGData.Instance:GetMengLingOneKeyWearDataList(self.data.seq)
        remind = not IsEmptyTable(wear_data)
    elseif mengling_show_index == TabIndex.mengling_strong then
        remind = MengLingWGData.Instance:IsCanStrongMengLingSuit(self.data.seq)
    end

    self.node_list.remind:CustomSetActive(remind)

    -- 五行/乾坤 套装
    for k, v in pairs(MengLingWGData.Instance:GetMengLingSuitCfg()) do
        local suit_id = v.suit
        local show_suit_cfg = {}

        local data_list = MengLingWGData.Instance:GetSuitActiveNumDataList(self.data.seq, suit_id)
       
        if not IsEmptyTable(data_list) then
            -- need_num  
            for need_num, need_num_list in pairs(data_list) do
                for color, color_data in pairs(need_num_list) do
                    local active_num = MengLingWGData.Instance:GetMengLingSuitCacheNum(color_data.seq, color_data.suit, color_data.color)

                    if active_num >= color_data.need_num then
                        -- 数量更多/ 品质更好
                       if IsEmptyTable(show_suit_cfg) or ((show_suit_cfg.need_num < color_data.need_num) or 
                            ((show_suit_cfg.need_num == color_data.need_num) and (show_suit_cfg.color < color_data.color))) then
                            show_suit_cfg = color_data
                       end
                    end
                end
            end
        end
        
        local active = not IsEmptyTable(show_suit_cfg)
        self.node_list["suit_icon" .. suit_id]:CustomSetActive(active)

        if active then
            local bundle, asset = ResPath.GetMengLingImg("a3_lz_select_" .. show_suit_cfg.color)
            self.node_list["suit_icon" .. suit_id].image:LoadSprite(bundle, asset, function ()
                self.node_list["suit_icon" .. suit_id].image:SetNativeSize()
            end)
        end

        -- local suit_cfg = MengLingWGData.Instance:GetMengLingSuitIconCfg(self.data.seq, suit_id)
        -- local active = false

        -- if not IsEmptyTable(suit_cfg) then
        --     local active_num = MengLingWGData.Instance:GetMengLingSuitCacheNum(suit_cfg.seq, suit_cfg.suit, suit_cfg.color)
        --     active = active_num >= suit_cfg.need_num
        -- end

        -- self.node_list["suit_icon" .. suit_id]:CustomSetActive(active)
 
        -- if active then
        --     local bundle, asset = ResPath.GetMengLingImg("a3_lz_select_" .. suit_cfg.color)
        --     self.node_list["suit_icon" .. suit_id].image:LoadSprite(bundle, asset, function ()
        --         self.node_list["suit_icon" .. suit_id].image:SetNativeSize()
        --     end)
        -- end
    end
end

function MengLingEquipSuitRender:OnSelectChange(is_select)
    self.node_list.bg_hl:CustomSetActive(is_select)
    self.node_list.bg:CustomSetActive(not is_select)
end

--------------------------------------MengLingEquipItemRender----------------------------------
MengLingEquipItemRender = MengLingEquipItemRender or BaseClass(BaseRender)

function MengLingEquipItemRender:LoadCallBack()
    -- if not self.item then
    --     self.item = ItemCell.New(self.node_list.item)
    --     self.item:SetItemTipFrom(ItemTip.FROM_MENGLING_EQUIP)
    --     self.item:SetIsUseRoundQualityBg(true)
    -- end

    XUI.AddClickEventListener(self.node_list.click, BindTool.Bind1(self.OnClick, self))
end

function MengLingEquipItemRender:__delete()
    -- if self.item then
    --     self.item:DeleteMe()
    --     self.item = nil
    -- end
end

function MengLingEquipItemRender:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

function MengLingEquipItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_wear_equip = self.data.item_id > 0
    self.node_list.item:CustomSetActive(is_wear_equip)
    self.node_list.btn_add:CustomSetActive(not is_wear_equip)
    self.node_list.level_bg:CustomSetActive(is_wear_equip and self.data.level > 0)

    if is_wear_equip then
        -- self.item:SetData(self.data)
        -- self.item:SetButtonComp(true)
        -- self.item:SetDefaultEff(false)
        -- -- self.item:SetQualityIconVisible(false)
        -- self.item:SetCellBgEnabled(false)

        local _, color_index = ItemWGData.Instance:GetItemColor(self.data.item_id)
        local bundle, asset = ResPath.GetMengLingImg("a3_lq_icon_di_".. color_index)
        self.node_list.item_bg.image:LoadSprite(bundle, asset, function ()
            self.node_list.item_bg.image:SetNativeSize()
        end)

        -- self.item:SetQualityIcon(bundle, asset)

        local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(self.data.item_id)
        local icon_bundle, icon_asset = ResPath.GetMengLingImg(equip_cfg.item_icon)
        self.node_list.item_icon.image:LoadSprite(icon_bundle, icon_asset, function ()
            self.node_list.item_icon.image:SetNativeSize()
        end)
        -- self.item:SetItemIcon(icon_bundle, icon_asset)
    end

    local remind = false
    local mengling_show_index = MengLingWGCtrl.Instance:GetMengLingShowIndex()

    if mengling_show_index == TabIndex.mengling_bag then
        -- local wear_data = MengLingWGData.Instance:GetMengLingOneKeyWearDataList(self.data.seq)
        -- remind = not IsEmptyTable(wear_data)
    elseif mengling_show_index == TabIndex.mengling_strong then
        remind = MengLingWGData.Instance:IsCanStrongMengLingSuitEquip(self.data.seq, self.data.slot)
    end

    self.node_list.remind:CustomSetActive(remind)

    -- local level = is_wear_equip and string.format(Language.Common.Level1, self.data.level) or ""
    local level = is_wear_equip and ("+" .. self.data.level) or ""
    self.node_list.level.text.text = level
end

--------------------------------------MengLingSXDRender----------------------------------
MengLingSXDRender = MengLingSXDRender or BaseClass(BaseRender)
function MengLingSXDRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickSXD, self))
end

function MengLingSXDRender:__delete()
    self.remind = nil
end

function MengLingSXDRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local use_num = MengLingWGData.Instance:GetMengLingStoneDataByHole(self.data.hole)
    self.node_list["used_num"].text.text = use_num

    local is_max_level = self.data.limit_count <= use_num
    local remind = false
    if not is_max_level then
        local has_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)

        if has_num > 0 then
            self.node_list["can_add_num"].text.text = "+" .. has_num
            remind = true
        else
            self.node_list["can_add_num"].text.text = ""
        end
    else
        self.node_list["can_add_num"].text.text = ""
    end

    self.node_list.remind:CustomSetActive(remind)
    self.node_list["arrow"]:SetActive(remind)
    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(self.data.item_id)
    self.node_list["icon"].image:LoadSprite(bundle, asset)
    self.remind = remind
end

function MengLingSXDRender:OnClickSXD()
    if IsEmptyTable(self.data) then
        return
    end

    if self.remind then
        MengLingWGCtrl.Instance:OnCSDreamSpiritOperate(DREAM_SPIRIT_OPERATE_TYPE.EQUIP_STONE, self.data.hole)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
    end
end
