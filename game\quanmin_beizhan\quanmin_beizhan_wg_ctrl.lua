require("game/quanmin_beizhan/quanmin_beizhan_view")
require("game/quanmin_beizhan/quanmin_beizhan_wg_data")

require("game/quanmin_beizhan/quanmin_beizhan_login")
require("game/quanmin_beizhan/quanmin_beizhan_recharge")
require("game/quanmin_beizhan/quanmin_beizhan_duobei")
require("game/quanmin_beizhan/quanmin_beizhan_longhun")
require("game/quanmin_beizhan/quanmin_beizhan_item")
require("game/quanmin_beizhan/quanmin_beizhan_longhun_reward")
require("game/quanmin_beizhan/quanmin_beizhan_laixi")
require("game/quanmin_beizhan/quanmin_beizhan_laixi_downtime")
require("game/quanmin_beizhan/quanmin_beizhan_juanxian")
require("game/quanmin_beizhan/quanmin_beizhan_haoli")
require("game/quanmin_beizhan/quanmin_beizhan_dalian_view")
require("game/quanmin_beizhan/quanmin_beizhan_cap")
require("game/quanmin_beizhan/quanmin_beizhan_role_cap")
require("game/quanmin_beizhan/quanmin_beizhan_server_cap")
require("game/quanmin_beizhan/quanmin_beizhan_kanjia")
require("game/quanmin_beizhan/quanmin_beizhan_laixi_fb_view")

require("game/quanmin_beizhan/quanmin_beizhan_longhun_rank")
require("game/quanmin_beizhan/longhun_rank_log_view")

require("game/quanmin_beizhan/quanmin_beizhan_turntable")
require("game/quanmin_beizhan/bz_turntable_record")
require("game/quanmin_beizhan/quanmin_beizhan_turntable_wg_data")

QuanMinBeiZhanWGCtrl = QuanMinBeiZhanWGCtrl or BaseClass(BaseWGCtrl)
function QuanMinBeiZhanWGCtrl:__init()
	if QuanMinBeiZhanWGCtrl.Instance then
		ErrorLog("[QuanMinBeiZhanWGCtrl] Attemp to create a singleton twice !")
	end
	QuanMinBeiZhanWGCtrl.Instance = self
	
	self.quanmin_beizhan_data = QuanMinBeiZhanWGData.New()
	self.quanmin_beizhan_turntable_data = BZTurnTableWGData.New()

	self:SetTabIndex()
	
	self.quanmin_beizhan_view = QuanMinBeiZhanView.New() 
	self.lh_reward_tips = QuanMinBeiZhanRewardTips.New()
	self.xingtian_laixi_downtime_view = QuanMinBeiZhanLaiXiDownTime.New()
	self.xj_reward_tip = QUJXTipView.New()
	self.bz_dalian_view = BeiZhanDaLianView.New()
	self.record_view = BZTurnTableRecord.New(GuideModuleName.BZTurnTableRecord)
	self.laixi_fb_view = QuanMinBeiZhanLaiXiFBView.New()

	self.longhun_rank_log_view = LongHunRankLogView.New()

	self:RegisterAllProtocols()
end

function QuanMinBeiZhanWGCtrl:__delete()
	QuanMinBeiZhanWGCtrl.Instance = nil

	if self.quanmin_beizhan_data ~= nil then
		self.quanmin_beizhan_data:DeleteMe()
		self.quanmin_beizhan_data = nil
	end

	if self.quanmin_beizhan_turntable_data ~= nil then
		self.quanmin_beizhan_turntable_data:DeleteMe()
		self.quanmin_beizhan_turntable_data = nil
	end

	if self.quanmin_beizhan_view ~= nil then
		self.quanmin_beizhan_view:DeleteMe()
		self.quanmin_beizhan_view = nil
	end

	if self.record_view ~= nil then
		self.record_view:DeleteMe()
		self.record_view = nil
	end

	if self.lh_reward_tips ~= nil then
		self.lh_reward_tips:DeleteMe()
		self.lh_reward_tips = nil
	end

	if self.xingtian_laixi_downtime_view ~= nil then
		self.xingtian_laixi_downtime_view:DeleteMe()
		self.xingtian_laixi_downtime_view = nil
	end

	if self.xj_reward_tip ~= nil then
		self.xj_reward_tip:DeleteMe()
		self.xj_reward_tip = nil
	end

	if self.scene_change_complete then
		GlobalEventSystem:UnBind(self.scene_change_complete)
		self.scene_change_complete = nil
	end

	if self.longhun_rank_log_view then
		self.longhun_rank_log_view:DeleteMe()
		self.longhun_rank_log_view = nil
	end

	if self.bz_dalian_view then
		self.bz_dalian_view:DeleteMe()
		self.bz_dalian_view = nil
	end

	if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

	if self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end
end

function QuanMinBeiZhanWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCWoYaoLongHun, "SCWoYaoLongHun")
	self:RegisterProtocol(SCRandActXTLXInfo,"SCRandActXTLXInfo")
	self:RegisterProtocol(SCRandActXTLXRefreshInfo, "SCRandActXTLXRefreshInfo")
	self:RegisterProtocol(SCRAXingTianLaiXiFinishInfo, "OnSCRAXingTianLaiXiFinishInfo")
	self:RegisterProtocol(SCQuanFuJuanXian, "SCQuanFuJuanXian")
	self:RegisterProtocol(SCBeiZhanHaoLi, "SCBeiZhanHaoLi")
	self:RegisterProtocol(SCZhanLiBiPin, "SCZhanLiBiPin")
	self:RegisterProtocol(CSRandActXTLXInfoReq)
	self:RegisterProtocol(CSEnterXingTianLaiXiFB)

    self:RegisterProtocol(SCBZTunvlangLayerInfo,'OnSCRATunvlangLayerInfo')
    self:RegisterProtocol(SCBZTunvlangDrawRecord,'OnSCRATunvlangDrawRecord')
    self:RegisterProtocol(SCBZTunvlangDrawResult,'OnSCRATunvlangDrawResult')

    --龙魂冲榜
    self:RegisterProtocol(SCRALonghunRankZhanLiRank, 'OnSCRALonghunRankZhanLiRank')
    self:RegisterProtocol(SCRALonghunRankRoleInfo, 'OnSCRALonghunRankRoleInfo')

	self.scene_change_complete = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneChangeComplete, self))
	self.mainui_create_complete = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self))
end

function QuanMinBeiZhanWGCtrl:Open(tab_index, param_t)
	tab_index = tab_index or self:GetFirstOpenActivity()
	self.quanmin_beizhan_view:Open(tab_index)
end

function QuanMinBeiZhanWGCtrl:OpenLaiXiFbView()
	self.laixi_fb_view:Open()
end

function QuanMinBeiZhanWGCtrl:SetTabIndex()
	local BeiZhan_Tab_Index = {
		quanmin_beizhan_login	= 10,		-- 备战登录
		quanmin_beizhan_shouchong = 20,		-- 备战首充
		quanmin_beizhan_leichong = 30,		-- 备战累充
		quanmin_beizhan_duobei = 40,		-- 备战多倍
		quanmin_beizhan_longhun = 50,		-- 备战龙魂
		quanmin_beizhan_juanxian = 60,		-- 备战捐献
		-- quanmin_beizhan_cap	= 70,			-- 备战战力
		quanmin_beizhan_cap	= 71,			-- 个人战力
		quanmin_beizhan_cap2 = 72,			-- 全服战力
		quanmin_beizhan_cap3 = 73,			-- 砍价战力
		quanmin_beizhan_haoli = 81,			-- 备战好礼tab1
		quanmin_beizhan_haoli2 = 82,		-- 备战好礼tab2
		quanmin_beizhan_haoli3 = 83,		-- 备战好礼tab3
		quanmin_beizhan_haoli4 = 84,		-- 备战好礼tab4
		quanmin_beizhan_laixi = 90,			-- 备战来袭
		quanmin_beizhan_turntable = 100,		-- 备战兔女郎
		quanmin_beizhan_longhun_rank = 110,	-- 龙魂冲榜
	}

	self.tab_index_list = {}
	local tb = ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").beizhan_theme_dec
	table.sort(tb, SortTools.KeyLowerSorter("rank_id")) 
	for k,v in ipairs(tb) do
		local tab_index = v.rank_id * 10
		if v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_login then
			TabIndex.quanmin_beizhan_login = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_login, v)
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_shouchong then
			TabIndex.quanmin_beizhan_shouchong = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_shouchong, v)
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_leichong then
			TabIndex.quanmin_beizhan_leichong = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_leichong, v)
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_duobei then
			TabIndex.quanmin_beizhan_duobei = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_duobei, v)
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_longhun then
			TabIndex.quanmin_beizhan_longhun = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_longhun, v)
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_juanxian then
			TabIndex.quanmin_beizhan_juanxian = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_juanxian, v)
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_cap then
			TabIndex.quanmin_beizhan_cap = tab_index + 1
			TabIndex.quanmin_beizhan_cap2 = tab_index + 2
			TabIndex.quanmin_beizhan_cap3 = tab_index + 3
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_cap, v)
			for i=1,3 do
				table.insert(self.tab_index_list, tab_index + i)
			end
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_haoli then
			TabIndex.quanmin_beizhan_haoli = tab_index + 1
			TabIndex.quanmin_beizhan_haoli2 = tab_index + 2
			TabIndex.quanmin_beizhan_haoli3 = tab_index + 3
			TabIndex.quanmin_beizhan_haoli4 = tab_index + 4
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_haoli, v)
			for i=1,4 do
				table.insert(self.tab_index_list, tab_index + i)
			end
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_laixi then
			TabIndex.quanmin_beizhan_laixi = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_laixi, v)
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_turntable then
			TabIndex.quanmin_beizhan_turntable = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_turntable, v)
		elseif v.real_id == BeiZhan_Tab_Index.quanmin_beizhan_longhun_rank then
			TabIndex.quanmin_beizhan_longhun_rank = tab_index
			self.quanmin_beizhan_data:SetThemeCfgByTabIndex(TabIndex.quanmin_beizhan_longhun_rank, v)
		end
		table.insert(self.tab_index_list, tab_index)
	end
end

-- 获取排序后第一个开启并且有奖励的活动，没有则选中第一个开启的活动
function QuanMinBeiZhanWGCtrl:GetFirstOpenActivity()
	local first_tab_index = nil
	if self.tab_index_list ~= nil then
		for i,v in ipairs(self.tab_index_list) do
			if self.quanmin_beizhan_data:GetActivityState(v) then
				if self.quanmin_beizhan_data:GetActivityRewardState(v) > 0 then
					return v
				end
				if first_tab_index == nil then
					first_tab_index = v
				end
			end
		end
	end
	return first_tab_index
end

function QuanMinBeiZhanWGCtrl:ShowLoginDayReward(day_index)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_login, "login_reward", {day_index})
	end
end

--登录奖励信息(TianshenRoadWGCtrl 调用（协议公用）)
function QuanMinBeiZhanWGCtrl:SCNewLoginGiftRet(protocol)
	self.quanmin_beizhan_data:SetLoginRewardInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_login, "login_view")
	end
end

--首充返回
function QuanMinBeiZhanWGCtrl:SCShouChongRet(protocol)
	self.quanmin_beizhan_data:SetShouChongInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_shouchong, "first_recharge_view")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_shouchong + 3, "first_recharge_view")
	end
end

--累计充值返回
function QuanMinBeiZhanWGCtrl:SCLeiChongHaoLi(protocol)
	self.quanmin_beizhan_data:SetLeiChongInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_leichong, "leichong_recharge_view")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_leichong + 1, "leichong_recharge_view")
	end
end

--多倍来袭返回
function QuanMinBeiZhanWGCtrl:SCDuoBeiJiangLi(protocol)
	self.quanmin_beizhan_data:SetDuoBeiInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_duobei, "duobei_view")
	end
end

--我要龙魂返回
function QuanMinBeiZhanWGCtrl:SCWoYaoLongHun(protocol)
	self.quanmin_beizhan_data:SetLHInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_longhun, "longhun_view")
	end

	if self.lh_reward_tips:IsOpen() then
		self.lh_reward_tips:Flush()
	end
end

function QuanMinBeiZhanWGCtrl:OpenLongHunRewardTips()
	self.lh_reward_tips:Open()
end

function QuanMinBeiZhanWGCtrl:OpenXTLXFBCountDown()
	if self.quanmin_beizhan_data:IsInXingTianLaiXiActivity() then
		local activity_info = QuanMinBeiZhanWGData.Instance:GetJingLinData()
		if activity_info and activity_info.is_final_boss_skill ~= 1 then
			self.xingtian_laixi_downtime_view:Open()
		end
	end
end

function QuanMinBeiZhanWGCtrl:OnSCRAXingTianLaiXiFinishInfo(protocol)
	self.quanmin_beizhan_data:SetXTLXFinishInfo(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.XINGTIANLAIXI_FB then
		ActivityWGCtrl.Instance:OpenActJiseSuanView(ACTIVITY_TYPE.XINGTIANLAIXI)
	end
end

--全民捐献返回
function QuanMinBeiZhanWGCtrl:SCQuanFuJuanXian(protocol)
	self.quanmin_beizhan_data:SetQuanFuJuanXianInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_juanxian, "juanxian")
	end
end

--备战好礼
function QuanMinBeiZhanWGCtrl:FlushHLViewByPassDay()
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_haoli, "change_day_flush")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_haoli2, "change_day_flush")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_haoli3, "change_day_flush")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_haoli4, "change_day_flush")
	end
end

--备战好礼
function QuanMinBeiZhanWGCtrl:SCBeiZhanHaoLi(protocol)
	self.quanmin_beizhan_data:SetBeiZhanHaoLiInfo(protocol)
	local old_day = self.quanmin_beizhan_data:GetHaoLiOldCurDay()
	local new_day = self.quanmin_beizhan_data:GetHaoLiDayNum()
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_haoli, "haoli")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_haoli2, "haoli")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_haoli3, "haoli")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_haoli4, "haoli")
	end 
	if old_day ~= new_day then
		self:FlushHLViewByPassDay()
	end
end

--战力比拼
function QuanMinBeiZhanWGCtrl:SCZhanLiBiPin(protocol)
	-- print_error("---------SCZhanLiBiPin------  protocol = ",protocol)
	self.quanmin_beizhan_data:SetZhanLiBiPinInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_cap, "role_cap")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_cap2, "server_cap")
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_cap3, "kanjia")
	end 
end

function QuanMinBeiZhanWGCtrl:FlushKanJia()
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_cap3, "kanjia")
	end 
end

function QuanMinBeiZhanWGCtrl:OpenJXRewardTip(data)
	self.xj_reward_tip:Open()
	self.xj_reward_tip:SetData(data)
end

function QuanMinBeiZhanWGCtrl:OnSceneChangeComplete()
	local act_info = self.quanmin_beizhan_data:GetJingLinData()
	if act_info then
		if act_info.activity_state == ACTIVITY_STATUS.STANDY then
			local npc_scene = self.quanmin_beizhan_data:GetJiangLinOtherCfg("npc_scene")
			local scene_id = Scene.Instance:GetSceneId()
			if npc_scene == scene_id then
				self:XingTianLaiXiCountDown()
			else
				ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.XINGTIANLAIXI)
 			end
 		elseif act_info.activity_state == ACTIVITY_STATUS.CLOSE then
 			ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.XINGTIANLAIXI)
		end
	else
		ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.XINGTIANLAIXI)
	end
end

function QuanMinBeiZhanWGCtrl:SendActivityRewardOp(activity_type, opera_type,param1, param2, param3)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = activity_type
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol.param_2 = param2 or 0
 	protocol.param_3 = param3 or 0
 	protocol:EncodeAndSend()
 end	

---[[ 刑天来袭
function QuanMinBeiZhanWGCtrl:CSRandActXTLXInfoReq()
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActXTLXInfoReq)
	protocol:EncodeAndSend()
end

function QuanMinBeiZhanWGCtrl:EnterXingTianLaiXiFB()
	local protocol = ProtocolPool.Instance:GetProtocol(CSEnterXingTianLaiXiFB)
	protocol:EncodeAndSend()
end

--刑天来袭(降临)返回
function QuanMinBeiZhanWGCtrl:SCRandActXTLXInfo(protocol)
	self.quanmin_beizhan_data:SetXTLXInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_laixi, "xingtian_laixi")
	end
	self.laixi_fb_view:Flush(0, "xingtian_laixi")
end

-- 刷新波数信息
function QuanMinBeiZhanWGCtrl:SCRandActXTLXRefreshInfo(protocol)
	self.quanmin_beizhan_data:SetXTLXFlushInfo(protocol)
	if protocol.is_final_boss_skill == 1 then
		Scene.Instance:DeleteObjsByType(SceneObjType.QiongQiLaiXi)
        ActivityWGCtrl.Instance:RemoveActNotice(ACTIVITY_TYPE.XINGTIANLAIXI)
        self.xingtian_laixi_downtime_view:Close()
	else
		self.xingtian_laixi_downtime_view:Flush()
	end
	self.laixi_fb_view:Flush()
end

-- 刑天来袭最后10s特殊倒计时
function QuanMinBeiZhanWGCtrl:XingTianLaiXiCountDown()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.XINGTIANLAIXI)
	if act_info and (act_info.status == ACTIVITY_STATUS.STANDY or act_info.status == ACTIVITY_STATUS.OPEN) then
		if ActivityWGCtrl.Instance:CheckNowCountDownActType(ACTIVITY_TYPE.XINGTIANLAIXI) then
			return
		end

		local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.XINGTIANLAIXI)
		if not act_cfg then
			return
		end

		local role_level = RoleWGData.Instance:GetRoleLevel()
		if role_level < act_cfg.level or role_level > act_cfg.level_max then
			return
		end

		local info_list = {}
		info_list.act_type = ACTIVITY_TYPE.XINGTIANLAIXI
		info_list.calculate_time = 10
		info_list.timestamp = act_info.next_time
		info_list.image_name = act_info.status == ACTIVITY_STATUS.STANDY and "a1_bosslx_laix"
		info_list.is_auto_close = act_info.status == ACTIVITY_STATUS.OPEN
		ActivityWGCtrl.Instance:OpenActCountDown(info_list)
	else
		ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.XINGTIANLAIXI)
	end
end

-- NPC对话修改
function QuanMinBeiZhanWGCtrl:CheckXingTianLaiXiNpc(npc_id)
	local npcid = self.quanmin_beizhan_data:GetJiangLinOtherCfg("npcid")
	if npcid ~= npc_id then
		return false
	end

	local act_info = self.quanmin_beizhan_data:GetJingLinData()
	if act_info then
		if act_info.activity_state == ACTIVITY_STATUS.STANDY then
			return true, Language.QuanMinBeiZhan.NPCTalkStr_1
		elseif act_info.activity_state == ACTIVITY_STATUS.OPEN then
			return true, Language.QuanMinBeiZhan.NPCTalkStr_2, BindTool.Bind(self.EnterXingTianLaiXiFB, self)
		end
	end
end

-- 前往NPC
function QuanMinBeiZhanWGCtrl:GotoShiLian()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.XINGTIANLAIXI_FB then
		return
	end

	local other_cfg = self.quanmin_beizhan_data:GetJiangLinOtherCfg()
	other_cfg = other_cfg and other_cfg[1]
	if not other_cfg then
		return
	end
	
	RoleWGCtrl.Instance:SetJumpAlertCheck(other_cfg.npc_scene, function()
		GuajiWGCtrl.Instance:MoveToNpc(other_cfg.npcid, nil, other_cfg.npc_scene)
	end, true)
end
--]]

-----------------------------------兔女郎---------------------------------------
 function QuanMinBeiZhanWGCtrl:OnSCRATunvlangLayerInfo(protocol)
	-- print_error("layer", protocol)
	local old_num = self.quanmin_beizhan_turntable_data:GetLayerRewardNumByLayer(protocol.layer)
	self.quanmin_beizhan_turntable_data:SetLayerInfo(protocol)
	local new_num = self.quanmin_beizhan_turntable_data:GetLayerRewardNumByLayer(protocol.layer)
	--是否reset
	if self.quanmin_beizhan_view:IsOpen() and new_num - old_num ~= 1 then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_turntable, "turntable", {param1 = "normal"})
	end 

	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_Turntable)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)

	-- 已刷新提示
	local state = self.quanmin_beizhan_view:IsOpen() and self.quanmin_beizhan_view:GetShowIndex() == TabIndex.quanmin_beizhan_turntable
	if protocol.reset_sign == 1 and state then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OATurnTable.Tip_1)
	end
end

function QuanMinBeiZhanWGCtrl:OnSCRATunvlangDrawRecord(protocol)
	self.quanmin_beizhan_turntable_data:SetRecordInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BZTurnTableRecord)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_turntable, "turntable",{param2 = "record"})
	end 
end

function QuanMinBeiZhanWGCtrl:OnSCRATunvlangDrawResult(protocol)
	-- print_error("result", protocol)
	self.quanmin_beizhan_turntable_data:SetResultInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		self.quanmin_beizhan_view:Flush(TabIndex.quanmin_beizhan_turntable, "turntable", {param3 = "result"})
	end 
end

function QuanMinBeiZhanWGCtrl:SendOpera(opera_type, param_1, param_2, param_3)
	-- print_error(opera_type, param_1, param_2, param_3)
    local t = {}
    t.rand_activity_type = ACTIVITY_TYPE.RAND_ACT_BZ_TURNTABLE
    t.opera_type = opera_type
    t.param_1 = param_1
    t.param_2 = param_2
    t.param_3 = param_3
    ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

------------龙魂冲榜  start  --------------------------------------------------------------
function QuanMinBeiZhanWGCtrl:OnSCRALonghunRankZhanLiRank(protocol)
	-- print_error("OnSCRALonghunRankZhanLiRank", protocol)
 	self.quanmin_beizhan_data:SetLongHunRankLogInfo(protocol)
end

function QuanMinBeiZhanWGCtrl:OnSCRALonghunRankRoleInfo(protocol)
	-- print_error("OnSCRALonghunRankZhanLiRank", protocol)
	self.quanmin_beizhan_data:SetLongHunRankRoleInfo(protocol)
	if self.quanmin_beizhan_view:IsOpen() then
		if self.quanmin_beizhan_view:GetShowIndex() == TabIndex.quanmin_beizhan_longhun_rank then
			self.quanmin_beizhan_view:Flush()
		end
	end
end

function QuanMinBeiZhanWGCtrl:OpenLongHunRankLogView()
	if not self.longhun_rank_log_view:IsOpen() then
		self.longhun_rank_log_view:Open()
	end
end

function QuanMinBeiZhanWGCtrl:FlushLongHunRankLogView()
	if self.longhun_rank_log_view:IsOpen() then
		self.longhun_rank_log_view:Flush()
	end
end
------------龙魂冲榜  end  --------------------------------------------------------------
---[[ 备战大脸图
function QuanMinBeiZhanWGCtrl:MainuiOpenCreateCallBack()
	-- self:CheckNeedOpenDaLian()
end

function QuanMinBeiZhanWGCtrl:RoleAttrChange()
	-- self:CheckNeedOpenDaLian()
end

function QuanMinBeiZhanWGCtrl:CheckNeedOpenDaLian()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "quanminbeizhan_dalian_flag")
	local flag = PlayerPrefsUtil.GetInt(key)
	if flag == 1 then
		if self.mainui_create_complete then
			GlobalEventSystem:UnBind(self.mainui_create_complete)
			self.mainui_create_complete = nil
		end
		return false
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN)
	if not act_info or act_info.status ~= ACTIVITY_STATUS.OPEN then
		return false
	end

	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN)
	if not act_cfg then
		return false
	end

	local role_level = RoleWGData.Instance:GetAttr("level")
	if role_level < act_cfg.level or role_level > act_cfg.level_max then
		if not self.attr_change then
			self.attr_change = BindTool.Bind(self.RoleAttrChange, self)
			RoleWGData.Instance:NotifyAttrChange(self.attr_change, {"level"})
		end
		return false
	elseif self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end

	if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

	-- ViewManager.Instance:OpenByQueue(self.bz_dalian_view)
	return true
end
--]]