TianShenSkillShowView = TianShenSkillShowView or BaseClass(SafeBaseView)

function TianShenSkillShowView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self:SetMaskBg(false)
	self:SetMaskBgAlpha(0)
    self:AddViewResource(0, "uis/view/skill_show_ui_prefab", "layout_list_skill_show_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function TianShenSkillShowView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TianShen.SkillPreview

    self.cur_select_ts_index = -1
    self.cur_huamo_level = 0
    if not self.tianshen_list_view then
        self.tianshen_list_view = AsyncListView.New(SkillShowTianShenRender, self.node_list["tianshen_list_view"])
        self.tianshen_list_view:SetSelectCallBack(BindTool.Bind(self.OnSelectTianShenCallBack, self))
        self.tianshen_list_view:SetLimitSelectFunc(BindTool.Bind(self.IsLimitClick, self))
    end        
    -- 普攻
    XUI.AddClickEventListener(self.node_list.normal_attack_btn, BindTool.Bind(self.OnClickNormalAttackBtn, self))

    -- 技能
    if self.node_list.skill_list and self.skill_btn_list == nil then
        self.skill_btn_list = {}
        local node_num = self.node_list.skill_list.transform.childCount
        for i = 1, node_num do
            self.skill_btn_list[i] = SkillShowSkillRender.New(self.node_list.skill_list:FindObj("skill" .. i))
            self.skill_btn_list[i]:SetIndex(i)
            self.skill_btn_list[i]:SetNeedChangeSkillBtnPos(false)
            self.skill_btn_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickSkillBtn, self))
        end
    end

    if not self.skill_pre_view then
        self.skill_pre_view = SkillPreview.New(self.node_list.skill_pre_root, self.node_list.skill_pre_rawimage, true)
    end

    self.skill_play_timestemp = 0
    self.normal_attack_play_timestemp = 0
end

function TianShenSkillShowView:ReleaseCallBack()
    self:CleanSkillBtnCDTimer()

    self.cur_select_ts_index = -1
    if self.tianshen_list_view then
        self.tianshen_list_view:DeleteMe()
        self.tianshen_list_view = nil
    end
 

    if self.skill_btn_list then
        for k, v in pairs(self.skill_btn_list) do
            v:DeleteMe()
        end
        self.skill_btn_list = nil
    end

    if self.skill_pre_view then
        self.skill_pre_view:DeleteMe()
        self.skill_pre_view = nil
    end

    self.skill_play_timestemp = nil
    self.normal_attack_play_timestemp = nil
end

function TianShenSkillShowView:SetShowDataAndOpen(data)
	self.show_data = data
	self:Open()
end


-- 天神列表选择返回
function TianShenSkillShowView:OnSelectTianShenCallBack(item)
	if nil == item or nil == item.data then
		return
	end

    local data = item.data
    local is_same_ts = self.cur_select_ts_index == data.index
    if is_same_ts then
        return
    end

    self.cur_select_ts_index = data.index
    self.cur_select_ts_data = data

    -- 刷新技能格子
    local skill_list = TianShenWGData.Instance:GetMultiSkillListByIndex(data.appe_image_id, 1)
    for k, v in pairs(self.skill_btn_list) do
        v:SetData({skill_id = skill_list[k], skill_level = 1})
    end

    self.general_attack_list = TianShenWGData.Instance:GetNormalSkillListByIndex(data.appe_image_id, 1)
    self:OnClickNormalAttackBtn(true)
end

-- 点击普攻
function TianShenSkillShowView:OnClickNormalAttackBtn(is_change_list)
    if IsEmptyTable(self.general_attack_list) then
        return
    end

    if self:IsLimitClick() then
		return
	end

    self.general_attack_index = 1
    self:SimulateTSGeneralAttackOpera(is_change_list)
end

-- 模拟普攻
function TianShenSkillShowView:SimulateTSGeneralAttackOpera(is_change_list)
	if IsEmptyTable(self.general_attack_list) then
		return
	end

	local skill_id = self.general_attack_list[self.general_attack_index]
	if not skill_id then
		return
	end

	local do_next_action_time = SkillPreviewData.Instance:GetRoleSkillTime(skill_id)
    self.normal_attack_play_timestemp = Status.NowTime + do_next_action_time
    self:CleanSkillBtnCDTimer()
    self:SetAllSkillBtnCD(string.format("%.1f", do_next_action_time), do_next_action_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(do_next_action_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), do_next_action_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, do_next_action_time)
        end
    )

    if self.skill_pre_view:GetPreviewIsLoaded() then
        self.skill_pre_view:PlaySkill(skill_id, is_change_list)
    else
        self.skill_pre_view:SetPreviewLoadCb(function()
            self.skill_pre_view:PlaySkill(skill_id, is_change_list)
        end)
    end
end

-- 点击技能
function TianShenSkillShowView:OnClickSkillBtn(item)
    if self:IsLimitClick() then
		return
	end

    local data = item:GetData()
    if not data then
        return
    end

    self:CleanSkillBtnCDTimer()

    -- 0.5s 等带特效后小半截播完
    local skill_show_time = SkillPreviewData.Instance:GetRoleSkillTime(data.skill_id)
    self.skill_play_timestemp = Status.NowTime + skill_show_time
    self:SetAllSkillBtnCD(string.format("%.1f", skill_show_time), skill_show_time)
    self.skill_btn_cd_timer = CountDown.Instance:AddCountDown(skill_show_time, 0.1,
        function(elapse_time, total_time)
            self:SetAllSkillBtnCD(string.format("%.1f", total_time - elapse_time), skill_show_time)
        end,
        function()
            self:SetAllSkillBtnCD(0, skill_show_time)
        end
    )

    if self.skill_pre_view:GetPreviewIsLoaded() then
        self.skill_pre_view:PlaySkill(data.skill_id)
    else
        self.skill_pre_view:SetPreviewLoadCb(function()
            self.skill_pre_view:PlaySkill(data.skill_id)
        end)
    end
end

function TianShenSkillShowView:SetAllSkillBtnCD(time, total_time)
    if self.skill_btn_list then
        for k,v in pairs(self.skill_btn_list) do
            v:SetSkillBtnCD(time, total_time)
        end
    end
end

-- 清除倒计时器1
function TianShenSkillShowView:CleanSkillBtnCDTimer()
    if self.skill_btn_cd_timer and CountDown.Instance:HasCountDown(self.skill_btn_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.skill_btn_cd_timer)
        self.skill_btn_cd_timer = nil
    end
end

function TianShenSkillShowView:IsLimitClick(no_tips)
    local is_playing_skill = Status.NowTime < self.skill_play_timestemp or Status.NowTime < self.normal_attack_play_timestemp
    if not no_tips and is_playing_skill then
        TipWGCtrl.Instance:ShowSystemMsg(Language.Skill.ShowSkillLimitClickStr)
    end

    return is_playing_skill
end

function TianShenSkillShowView:OnFlush()
    if not self.show_data then
        return
    end

    local magic_cfg = CommonSkillShowData.Instance:GetTianShenShowByLevelLimitList()
    self.tianshen_list_view:SetDataList(magic_cfg)
    local show_ts_index = self.show_data.tianshen_index

    if self.cur_select_ts_index ~= show_ts_index then
        local jump_index = 1
        for k,v in ipairs(magic_cfg) do
            if show_ts_index == v.index then
                jump_index = k
                break
            end
        end

        self.tianshen_list_view:JumpToIndex(jump_index)
    end
end

--===================================================================
SkillShowTianShenRender = SkillShowTianShenRender or BaseClass(BaseRender)
function SkillShowTianShenRender:OnFlush()
    local bundle, asset = ResPath.GetSkillIconById(self.data.bianshen_icon)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        --self.node_list.icon.image:SetNativeSize()
    end)
    

    self.node_list.name.text.text = self.data.bianshen_name
end

function SkillShowTianShenRender:OnSelectChange(is_select)
    self.node_list["select_bg"]:SetActive(is_select)
end

--===================================================================
SkillShowHuaMoBtnRender = SkillShowHuaMoBtnRender or BaseClass(BaseRender)
function SkillShowHuaMoBtnRender:OnSelectChange(is_select)
    self.node_list["img_hl"]:CustomSetActive(is_select)
    self.node_list["img_normal"]:CustomSetActive(not is_select)
end