﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Video_VideoClipWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(UnityEngine.Video.VideoClip), typeof(UnityEngine.Object));
		<PERSON><PERSON>unction("GetAudioChannelCount", GetAudioChannelCount);
		<PERSON><PERSON>("GetAudioSampleRate", GetAudioSampleRate);
		<PERSON><PERSON>("GetAudioLanguage", GetAudioLanguage);
		<PERSON><PERSON>Function("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("originalPath", get_originalPath, null);
		<PERSON><PERSON>("frameCount", get_frameCount, null);
		<PERSON><PERSON>("frameRate", get_frameRate, null);
		<PERSON>.<PERSON>("length", get_length, null);
		<PERSON><PERSON>("width", get_width, null);
		<PERSON><PERSON>("height", get_height, null);
		<PERSON><PERSON>("pixelAspectRatioNumerator", get_pixelAspectRatioNumerator, null);
		<PERSON><PERSON>("pixelAspectRatioDenominator", get_pixelAspectRatioDenominator, null);
		L.RegVar("sRGB", get_sRGB, null);
		L.RegVar("audioTrackCount", get_audioTrackCount, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAudioChannelCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoClip));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			ushort o = obj.GetAudioChannelCount(arg0);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAudioSampleRate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoClip));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			uint o = obj.GetAudioSampleRate(arg0);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAudioLanguage(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoClip));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			string o = obj.GetAudioLanguage(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_originalPath(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			string ret = obj.originalPath;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index originalPath on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_frameCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			ulong ret = obj.frameCount;
			LuaDLL.tolua_pushuint64(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index frameCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_frameRate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			double ret = obj.frameRate;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index frameRate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_length(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			double ret = obj.length;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index length on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_width(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			uint ret = obj.width;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index width on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_height(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			uint ret = obj.height;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index height on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pixelAspectRatioNumerator(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			uint ret = obj.pixelAspectRatioNumerator;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pixelAspectRatioNumerator on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pixelAspectRatioDenominator(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			uint ret = obj.pixelAspectRatioDenominator;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pixelAspectRatioDenominator on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_sRGB(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			bool ret = obj.sRGB;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sRGB on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_audioTrackCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoClip obj = (UnityEngine.Video.VideoClip)o;
			ushort ret = obj.audioTrackCount;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index audioTrackCount on a nil value");
		}
	}
}

