GuildWGData = GuildWGData or BaseClass()

GuildDataConst = {
	MAX_JIANSHELING_NUM = 99,									-- 捐献建设令的最大值
	MAX_GOLD_NUM = 1000,										-- 捐献元宝的最大值
	MAX_COIN_NUM = 100,											-- 捐献铜币的最大值
	MAX_SHOU_WEI_COUNT = 3,										-- 仙盟守护 守卫数量
	MAX_CALLIN_TIMES = 5,										-- 免费招募次数
	MOVE_TIME = 0.4,											-- 仙盟聊天界面打开时间
	MAX_LEN = 6,												-- 最长输入
	GUILD_CHAT_WIDTH = 650,										-- 仙盟聊天面板宽度
	GUILD_CHAT_FB_WIDTH = 200,									-- 仙盟聊天编辑栏高度
	TABBAR_CONTENT = {
		INVALID = -1,
		Guild_Info = 1,
		Guild_Member = 2,
		Guild_Activity = 3,
		Guild_List = 4,
	},
	TABBAR_ACTIVE_CONTENT = {									-- 仙盟活动选项卡索引
		MiJing = 1,												-- 仙盟密境
		Task = 2,												-- 仙盟任务
		Guard = 3,												-- 仙盟争霸
		Animal = 4,												-- 仙盟酒会
		Shengshou = 5,											-- 仙盟神兽
		YunShi = 6,												-- 仙盟运势
		Xiongshou = 7,											-- 仙盟凶兽
	},
	GUILDVO = {
		guild_id = 0,
		guild_name = "",
		guild_post = 0,
		guild_level = 0,
		guild_exp = 10,
		guild_money = 0,
		guild_max_exp = 100,
		cur_member_count = 0,
		max_member_count = 0,
		tuanzhang_uid = 0,
		tuanzhang_name = "",
		tuanzhang_prof = 0,
		create_time = 0,
		camp = 0,
		vip_level = 0,
		applyfor_setup = 0,
		guild_notice = "",
		auto_kickout_setup = 0,
		applyfor_need_capability = 0,
		applyfor_need_level = 0,
		guild_gongxian = 0,
		guild_total_gongxian = 0,
		guild_callin_times = 0,
		my_lucky_color = 1,
		hall_level_list = {0, 0, 0, 0},
		stuff_list = {},
		flag_id = 1,
		flag_color = 1,
		flag_name = Language.Guild.DefaultFlagName,
	},
	GUARD_INFO_VO = {												-- 仙盟守护信息
		got_exp = 0,												-- 获得经验
		got_xianhun = 0,											-- 获得仙魂
		got_gongxian = 0,											-- 获得贡献
		gather_count = 0,											-- 采集次数
		is_double_rewardt = 0,										-- 双倍奖励否
		is_clear_cdt = 0,											-- 采集CD清理否
		next_gather_timet = 0,										-- 下次可以采集时间
		reset_gather_times = 0										-- 重置采集次数
	},
	MANAGER_MENU = {												-- 管理菜单列表
		Language.Guild.ShenQingSheZhi,								-- 申请设置
		Language.Guild.QunFaYouJian,								-- 群发邮件  此处和/mg不同
		Language.Guild.ApplyforList,								-- 申请列表
		Language.Guild.ZhaoLanXianShi,								-- 招揽贤士
		Language.Guild.TuiChuGuild,									-- 退出仙盟
		Language.Guild.TanHeMengZhu,								-- 弹劾盟主
		Language.Guild.GuildMerge,									-- 仙盟合并
		Language.Guild.JieSanXianMeng,								-- 解散仙盟
	},

	GUILD_APPLYFOR_LIST = {											-- 申请加入仙盟列表请求
		count = 0,
		list = {},
	},
	GUILD_EVENT_LIST = {											-- 所有仙盟事件列表
		count = 0,
		list = {},
	},
	GUILD_INFO_TYPE = {												-- 仙盟信息类型
		INVALID = 0,
		ALL_GUILD_BASE_INFO = 1,									-- 所有军团基本信息
		GUILD_APPLY_FOR_INFO = 2,									-- 军团申请列表
		GUILD_MEMBER_LIST = 3,										-- 军团成员列表
		GUILD_INFO = 4,												-- 军团信息
		GUILD_EVENT_LIST = 5,										-- 军团日志列表
		APPLY_FOR_JOIN_GUILD_LIST = 6,								-- 已申请加入的军团列表
		INVITE_LIST = 7,											-- 邀请列表
		MAX = 99,
	},
	GUILD_EVENT_TYPE = {											-- 仙盟事件类型
		INVALID = 0,
		CREATE = 1,													-- 创建军团
		MEMBER_ADD = 2,												-- 成员入团
		MEMBER_QUIT = 3,											-- 退出军团
		MEMBER_KICK = 4,											-- 踢出军团
		MEMBER_APPOINT = 5,											-- 成员任命
		MEMBER_LEAVE_POST = 6,										-- 卸任
		TRANSFER_TUANZHANG = 7,										-- 转让军团长
		DELATE_TUANZHANG = 8,										-- 弹劾军团长
		UPGRADE_LEVEL = 9,											-- 军团升级
		STORAGE_OPERATE = 10,										-- 仓库操作
		SET_STORAGESHOP_USE = 11,									-- 更改权限设置
		CALL_BOSS = 12,												-- 召唤神兽
		GUILD_UNION = 13,											-- 联盟结成 解除
		FEED_BOSS = 14,												-- 喂养神兽
		MEMBER_SIGNIN = 15,											-- 成员签到
		DOWNGRADE_LEVEL = 16,										-- 军团降级
		ADD_EXP = 17,												-- 成员捐献
		XIANGFANG_UP = 18,											-- 厢房升级
		GUILD_EVENT_TYPE_RENAME = 20,								-- 军团改名
		MAX = 99
	},

	GUILD_STORE_OPTYPE = {											-- 仙盟仓库事件类型
		GUILD_STORE_OPTYPE_TAKEOUT = 0,								-- 从仙盟仓库中取出
		GUILD_STORE_OPTYPE_PUTIN = 1,								-- 放入仙盟仓库
		GUILD_STORE_OPTYPE_DESTROY = 2,								-- 销毁仙盟仓库物品
	},

	GUILD_SETTING_MODEL = {											-- 设置仙盟方式
		APPROVAL = 0,
		FORBID = 1,
		AUTOPASS = 2,
	},

	GUILD_JUANXIAN_TYPE = {
		INVALID = 0,
		COIN = 1,													-- 铜币
		ITEM = 2,													-- 捐献物品
		MAX = 4,
	},

	GUILD_HALL_TYPE = {										-- 建筑类型
		GUILD_HALL_TYPE_INVALID = 0,
		GUILD_HALL_TYPE_1 = 1,
		GUILD_HALL_TYPE_2 = 2,
		GUILD_HALL_TYPE_3 = 3,
		GUILD_HALL_TYPE_4 = 4,
		GUILD_HALL_TYPE_MAX = 5,
	},

	GUILD_BUFF_TYPE = 3001,

	GUILD_MEMBER_LIST = {
		count = 0,
		list = {},
	},
	GUILD_AUTHORITY_LIST = {										-- 职位权限列表
		Language.Guild.LookInfo,									-- 查看资料
		Language.Guild.Private,										-- 私聊
		--Language.Guild.Trade,										-- 交易
		Language.Guild.AddFriend,									-- 加为好友
		Language.Guild.SendFlowers,									-- 赠送鲜花
		Language.Guild.TeamInvite,									-- 组队邀请
		Language.Guild.Kickout,										-- 踢出仙盟
		Language.Guild.AppointPost,									-- 任免职务
		Language.Guild.TransferMengZhu,								-- 转让盟主
		Language.Guild.GuildFuMeng,									-- 任命副盟
		Language.Guild.GuildZhangLao,								-- 任命长老
		Language.Guild.GuildHuFa,
		Language.Guild.GuildJingYing,
		Language.Guild.GuildChengYuan,								-- 任命成员
	},

	GUILD_AUTHORITY_LIST_TYPE = {
		LookInfo = 1,									-- 查看资料
		Private = 2,									-- 私聊
		AddFriend = 3,									-- 加为好友
		SendFlowers = 4,								-- 赠送鲜花
		TeamInvite = 5,									-- 组队邀请
		Kickout = 6,									-- 踢出仙盟
		AppointPost = 7,								-- 任免职务
		TransferMengZhu = 8,							-- 转让盟主
		GuildFuMeng = 9,								-- 任命副盟
		GuildZhangLao = 10,								-- 任命长老
		GuildHuFa = 11,									-- 任命护法
		GuildJingYing = 12,								-- 任命精英
		GuildChengYuan = 13,							-- 任命成员
	},


	GUILD_OPERA_TYPE = {											-- 仙盟操作类型
		INVALID = 0,
		APPLY_SET = 1,												-- 设置类型
		CALL_IN  = 2,												-- 招募类型
		MAX = 99
	},
	GUILD_POST_LIST = {												-- 职务列表
		Language.Guild.MengZhu,
		Language.Guild.FuMengZhu,
		Language.Guild.ZhangLao,
		Language.Guild.HuFa,
		Language.Guild.JingYing,
		Language.Guild.PuTong,
		Language.Guild.JiaMengZhu,
	},

	-- 场景上显示用的职位列表(描述和对应的Index没有对随便写的，不对再来改)
	SCENE_GUILD_POST_LIST = {												-- 职务列表
		[1] = Language.Guild.ScenePuTong,
		[2] = Language.Guild.ZhangLao,
		[3] = Language.Guild.FuMengZhu,
		[4] = Language.Guild.MengZhu,
		[5] = Language.Guild.JingYing,
		[6] = Language.Guild.HuFa,
		[7] = Language.Guild.JiaMengZhu,
	},
	GUILD_NOTIFY_TYPE = {											-- 消息通知类型
		INVALID = 0,
		APPLYFOR = 1,												-- 有人申请加入
		UNION_APPLYFOR = 2,											-- 有军团申请结盟
		UNION_JOIN = 3,												-- 加入联盟
		UNION_QUIT = 4,												-- 退出联盟
		UNION_REJECT = 5,											-- 拒绝联盟
		UNION_APPLYFOR_SUCC = 6,									-- 申请军团联盟成功
		MEMBER_ADD = 7,												-- 成员加入
		MEMBER_REMOVE = 8,											-- 成员退出
		MEMBER_SOS = 9,												-- 成员求救
		MEMBER_HUNYAN = 10,											-- 成员有婚宴
		REP_PAPER = 11,												-- 红包相关
		GUILD_PARTY = 12,											-- 仙盟酒会
		GUILD_FB = 13,												-- 仙盟副本
		GUILD_LUCKY = 14,											-- 仙盟运势
		BOSS = 15,													-- 仙盟boss
		GUILD_BONFIRE = 16,											-- 仙盟篝火
		GUILD_BATTLE_RANK = 17,										-- 仙盟战排行榜

		MAX = 99,
	},



	GUILD_AUTHORITY_TYPE = {										-- 权限
		MANAGERGUILD = 1,											-- 仙盟管理
		EXITGUILD = 2,												-- 退出仙盟
		MODNOTICE = 3,												-- 修改公告
		TANHE = 4,													-- 弹劾盟主
	},
	GUILD_IOPEN = {													-- 请求打开仙盟界面
		InfoView = false,											-- 信息查看界面
		GuildView = false,											-- 仙盟主界面
		QuitGuildView = false,										-- 退出仙盟
		ActivityGuardView = false,									-- 仙盟守护
		ActivityMonsterView = false,								-- 仙盟神兽
	},
	Effect = {														-- 特效
		Success = "3006",											-- 成功
		LongHuiBoWen = "3010",										-- 龙微波纹
		LongHuiShuiQiu = "3011",									-- 龙微水球
	},
	Guard_Option = {												-- 仙盟守护客户端请求类型
		DoubleAward = 1,											-- 双倍收益
		ClearCD = 2,												-- vip取消cd
		ResetGather = 3,											-- 重置采集数
	},
	GUILD_SELECT_LIST = {												-- 一键选择类型
		Language.Guild.SelectType1,
		Language.Guild.SelectType2,
		Language.Guild.SelectType3,
		Language.Guild.SelectType4,
		Language.Guild.SelectType5,
	},
	GUILD_CHAT_NUM = 0,												-- 收到的仙盟聊天数量
	GUILD_MAX_BOSS = 5,												-- 仙盟boss最大次数
	TODAY_GUILD_MAX_BOSS = 1,										-- 今日仙盟boss最大次数
}

GUILD_POST = {
	CHENG_YUAN = 1,													-- 成员
	ZHANG_LAO = 2,													-- 长老
	FU_TUANGZHANG = 3,												-- 副盟长
	TUANGZHANG = 4,													-- 盟长
	JINGYING = 5,													-- 精英成员
	HUFA = 6,														-- 护法
	JiaMengZhu = 7,													-- 代理盟主
}
--绑元/元宝/绑元不足（创建仙盟）
GUILD_CREATE_TYPE = {
	GUILD_CREATE_TYPE_INVALID = 0,
	GUILD_CREATE_TYPE_GUILD_BIND = 1,
	GUILD_CREATE_TYPE_GOLD = 2,
	GUILD_CREATE_TYPE_GUILD_BIND_NO_ENOUGH = 3,
	GUILD_CREATE_TYPE_ITEM = 4,
}


GuildDataConst.GUILD_POST_ENUM = {									-- 职务对应的枚举号
	[GuildDataConst.GUILD_POST_LIST[1]] = 4,
	[GuildDataConst.GUILD_POST_LIST[2]] = 3,
	[GuildDataConst.GUILD_POST_LIST[3]] = 2,
	[GuildDataConst.GUILD_POST_LIST[4]] = 6,
	[GuildDataConst.GUILD_POST_LIST[5]] = 5,
	[GuildDataConst.GUILD_POST_LIST[6]] = 1,
	[GuildDataConst.GUILD_POST_LIST[7]] = 7,
}
--用的地方太多不改了
GuildDataConst.GUILD_POST_AUTHORITY_LIST = {						-- 仙盟职位权限列表
	[1] = {post = GuildDataConst.GUILD_POST_LIST[6], post_index = 6, menu = 6, authority = {2, 4}},
	[2] = {post = GuildDataConst.GUILD_POST_LIST[3], post_index = 3, menu = 6, authority = {1, 2, 3, 4}},
	[3] = {post = GuildDataConst.GUILD_POST_LIST[2], post_index = 2, menu = 8, authority = {1, 2, 3, 4}},
	[4] = {post = GuildDataConst.GUILD_POST_LIST[1], post_index = 1, menu = 9, authority = {1, 3}},
	[5] = {post = GuildDataConst.GUILD_POST_LIST[5], post_index = 5, menu = 6, authority = {2, 4}},
	[6] = {post = GuildDataConst.GUILD_POST_LIST[4], post_index = 4, menu = 6, authority = {2, 4}},
	[7] = {post = GuildDataConst.GUILD_POST_LIST[7], post_index = 7, menu = 9, authority = {1,3}},
}

GuildDataConst.GUILD_POST_SORTINDEX_LIST = {						-- 仙盟职位权限列表
	[1] = 1,
	[2] = 4,
	[3] = 5,
	[4] = 6,
	[5] = 2,
	[6] = 3,
	[7] = 7,
}

GuildDataConst.SHOW_EVENT_LIST = {									-- 历史记录中显示的事件类型
	[1] = GuildDataConst.GUILD_EVENT_TYPE.CREATE,
	[2] = GuildDataConst.GUILD_EVENT_TYPE.MEMBER_ADD,
	[3] = GuildDataConst.GUILD_EVENT_TYPE.MEMBER_QUIT,
	[4] = GuildDataConst.GUILD_EVENT_TYPE.MEMBER_KICK,
	[5] = GuildDataConst.GUILD_EVENT_TYPE.MEMBER_APPOINT,
	[6] = GuildDataConst.GUILD_EVENT_TYPE.MEMBER_LEAVE_POST,
	[7] = GuildDataConst.GUILD_EVENT_TYPE.TRANSFER_TUANZHANG,
	[8] = GuildDataConst.GUILD_EVENT_TYPE.DELATE_TUANZHANG,
	[10] = GuildDataConst.GUILD_EVENT_TYPE.STORAGE_OPERATE,
	[17] = GuildDataConst.GUILD_EVENT_TYPE.ADD_EXP,
	[20] = GuildDataConst.GUILD_EVENT_TYPE.GUILD_EVENT_TYPE_RENAME,
}

GuildDataConst.MiJingStatus = {
	NotOpen = 0,	-- 未开启
	Open = 1,		-- 进行中
	Over = 2,		-- 已结束
}

GuildDataConst.BonfireStatus = {
	NotOpen = 0,	-- 未开启
	Open = 1,		-- 进行中
	Over = 2,		-- 已结束
}

GUILD_BATTLE_INFO_REQ_TYPE = {
	GUILD_BATTLE_INFO_REQ_TYPE_RANK = 0,					-- 排行榜
	GUILD_BATTLE_INFO_REQ_TYPE_MATCH = 1,					-- 匹配信息
}

GUILD_BATTLE_STATE_TYPE = {
	GUILD_BATTLE_STATE_TYPE_ACTIVITY_END = 0,				-- 活动结束
	GUILD_BATTLE_STATE_TYPE_FIRST_FIGHT_ON = 1,				-- 第一场进行中
	GUILD_BATTLE_STATE_TYPE_FIRST_FIGHT_END = 2,			-- 第一场结束
	GUILD_BATTLE_STATE_TYPE_SECOND_FIGHT_ON = 3,			-- 第二场进行中
	GUILD_BATTLE_STATE_TYPE_SECOND_FIGHT_END = 4,			-- 第二场结束
}
GUILD_LONGHUI = {
	MAX_LEVEL = 200 	--龙徽的最大等级
}

GUILD_KF_BATTLE_MATCH_STATE ={
	HALF_FINAL_GAME = 0,	--半决赛
	FINAL_GAME = 1,			--决赛
	END_GAME = 2,			--结束
}

function GuildWGData:__init()
	if GuildWGData.Instance then
		ErrorLog("[GuildWGData]:Attempt to create singleton twice!")
	end
	GuildWGData.Instance = self
	self.task_auto_lingqu = false									--仙盟建设任务刷新标识
	self.is_open_paimaihang = false									--是否点击仙盟拍卖
	self.chat_input_pos_count = 0									-- 仙盟聊天中输入坐标数量累加器
	self.invitation_list = {}
	self.qiujiu_list = {}
	self.is_animal_reminded = false
	self.act_guild_party_status = 0									--仙盟酒会开启状态
	self.act_guild_party_open_times = 0								--仙盟酒会开启次数
	self.finish_timestamp_data = 0									--仙盟酒会结束时间

	self.lucky_info = {}											--仙盟运势info
	self.invite_lucky_zhufu = {}									--邀请祝福
	self.xiong_shou_info = {}
	self.exchange_t = {}											--兑换次数
	self.boss_list = {}												--boss数据
	self.record_list = {}											--boss奖励数据
	self.xianshu_lv = {}
	self.guild_cd_time = 0                                          -- 退出宗门再次加入宗门冷却时间戳

	self.daycount_is_day_reward = 0									--帮派每日福利次数
	self.daycount_lingdi_reward = {}

	self.guild_rank_data = {}										--帮派攻城数据
	self.select_index = 1
	self.member_select_index = 1
	self.is_guide_guild = false

	self.guild_member_rank_info = {}								--仙盟成员战力排行信息

	self.guild_zhufu_num = 0										--帮派运势祝福次数
	self.guild_member_index = 1

	self.guild_yunshi_times = {}									--运势祝福里的倒计时存储
	self.my_yunshi_info = {}										--自己运势的数据

	self.red_pocket_list = {}                                       --红包列表
	self.red_pocket_num = 0 										--红包个数
	self.red_pocket_distribute = {}									--红包详细列表
	self.save_list = {}
	self.open_grid_count = 0
	self.show_redpacket_anim = false
	self.redpacket_anim_endobj = nil
	self.guild_sh_rank_pos = nil
	self.old_beauty_hp = 0 											--仙盟守护-女神血量
	self.is_waring = false

	self.is_can_join = 1

	self.is_can_join_shou_hu = 1

	self.cross_lieKun_info = {
		zone = -1,
		is_main_live_flag = 0,
		boss_id = {},
		guild_id = {},
		boss_next_flush_timestamp = {},
		boss_extra_level = 0
	}

	for i=1, GameEnum.LIEKUN_BOSS_TYPE_COUNT do
		self.cross_lieKun_info.guild_id[i] = 0
	end

	for i=1, GameEnum.LIEKUN_BOSS_TYPE_COUNT do
		self.cross_lieKun_info.boss_next_flush_timestamp[i] = 0
	end

	self.liekunfb_player_info = {
		is_enter_main_zone = 0,
		role_num = {},
		guild_role_num = {}
	}

	for i=1, GameEnum.LIEKUN_ZONE_TYPE_COUNT do
		self.liekunfb_player_info.role_num[i] = 0
		self.liekunfb_player_info.guild_role_num[i] = 0
	end

	self.liekun_remind_num = 0
	self.cross_liekun_auto = ConfigManager.Instance:GetAutoConfig("cross_liekun_auto")
	local fb_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto")
	self.total_chapter = #fb_cfg.wave_cfg
	self.door_cfg = fb_cfg.door_cfg
	self.guild_sh_other_cfg = fb_cfg.other[1]

	local guild_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto")
	self.guild_build_cfg = ConfigManager.Instance:GetAutoConfig("guildbuildcfg_auto")
	self.guild_build_task_cfg = ListToMap(self.guild_build_cfg.guild_build_task_cfg, "task_id")
	self.guild_wage_cfg = guild_cfg.guild_wage
	self.guild_skill_cfg = ConfigManager.Instance:GetAutoConfig("guildskill_cfg_auto")
	self.skill_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("guildskill_cfg_auto").skill_cfg, "skill_id")
	self.skill_level_cfg = ListToMap(ConfigManager.Instance:GetAutoConfig("guildskill_cfg_auto").skill_level, "skill_id","skill_level")
	self.wage_show_cfg = ListToMap(guild_cfg.wage_show, "act_type")
	self.guild_other_config = guild_cfg.other_config[1]

	self.bargain_gift_cfg = guild_cfg.gift_grade				-- 仙盟砍价礼包
	self.guild_shop_cfg = ListToMap(guild_cfg.shop, "seq")		-- 仙盟商店配置
	self.sign_redpaper_cfg = guild_cfg.sign_redpaper		-- 仙盟签到红包配置

    self.random_robot_cfg = guild_cfg.rand_name
	self.distribute_times = 0
	self.cur_guild_mengzhu_uid = 0 --用于记录当前的帮主id

	local fake_guild_cfg = ConfigManager.Instance:GetAutoConfig("guild_fake_auto")
	self.fake_guild_base_cfg = ListToMap(fake_guild_cfg.base, "seq")
	self.fake_guild_info_cfg = ListToMap(fake_guild_cfg.guild, "seq")
	self.fake_guild_member_cfg = ListToMap(fake_guild_cfg.member, "seq")

	self.guild_rank_reawrd_cfg = fb_cfg.guild_rank_reward_new

	--获取仙盟活动的等级限制
	self:GetActivityLevel()

	--红点显示
	RemindManager.Instance:Register(RemindName.Guild_ZongLan_GaiKuang, BindTool.Bind(self.IsShowGuildZongLanGKRedPoint, self))--综览概况
	RemindManager.Instance:Register(RemindName.Guild_ZongLan_RedBag, BindTool.Bind(self.IsShowGuildRedBagRedPoint, self))--红包
	RemindManager.Instance:Register(RemindName.Guild_Wage, BindTool.Bind(self.IsShowGuildWagePoint, self))--仙盟工资

	-- RemindManager.Instance:Register(RemindName.Guild_Activity, BindTool.Bind(self.IsShowGuildActivityRedPoint, self))--仙盟活动
	RemindManager.Instance:Register(RemindName.Guild_Activit_ShiLian, BindTool.Bind(self.IsShowGuildShiLianRedPoint, self))--仙盟试炼
	RemindManager.Instance:Register(RemindName.Guild_Activit_DaTi, BindTool.Bind(self.IsShowGuildDaTiRedPoint, self))--活动答题
	RemindManager.Instance:Register(RemindName.Guild_Activit_Boss, BindTool.Bind(self.IsShowGuildBossRedBagRedPoint, self))--活动Boss
	RemindManager.Instance:Register(RemindName.Guild_Activit_ZhengBa, BindTool.Bind(self.IsShowGuildZhengBaRedPoint, self))--活动争霸

	RemindManager.Instance:Register(RemindName.Guild_Activit_LieKun, BindTool.Bind(self.IsShowGuildLieKunRedPoint, self))--活动猎坤

	RemindManager.Instance:Register(RemindName.Guild_Skill, BindTool.Bind(self.IsShowGuildSkillRedPoint, self))--帮派技能


	--仙盟守护个人排名10以后的配置
	--self.person_rank_after_ten_cfg_list = self:_SetPersonAfterTenRankRewardCfg()	--屏蔽
	--仙盟守护个人排名列表
	self.ui_person_rank_data_list = {}
    self.ui_guild_rank_data_list = {}
	self.ui_person_data = {}
    self:InitPaiPinList()
	--仙盟守护在副本中的排名信息
	self.in_fb_rank_list = {}

	self.old_door_hp_list = {}
	for i = 1, 3 do
		self.old_door_hp_list[i] = 0
	end

	--仙盟争霸初始值
	self.guild_kf_fight_state = 0
	self.next_match_start_time = 0
	self.m_curr_match_end_time = 0
	self.my_guild_side = 0
	self.my_guild_opponent_guild = 0

	self:RegisterGuildSkillRemindInBag(RemindName.Guild_Skill)
end


function GuildWGData:GetGuildSHOtherCfg()
	return self.guild_sh_other_cfg
end

function GuildWGData:RegisterGuildSkillRemindInBag(remind_name)
    local map = {}

    local skill_cfg = ConfigManager.Instance:GetAutoConfig("guildskill_cfg_auto").skill_level
    for k,v in pairs(skill_cfg) do
    	map[v.consume_item_id] = true
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function GuildWGData:GetGuildFbIdMap(monster_id)
	local monster_id_index_map = {}
	local door_cfg = self.door_cfg
	local other_cfg = self:GetGuildSHOtherCfg()

	local id_num = 1
	if door_cfg then
		for k,v in pairs(door_cfg) do
			monster_id_index_map[v.monster_ID] = id_num
			id_num = id_num + 1
		end
	end

	if other_cfg.xiannv_id ~= nil then
		monster_id_index_map[other_cfg.xiannv_id] = id_num
	end

	return monster_id_index_map[monster_id]
end

function GuildWGData:GetGuideGuildFlag()
	if self.is_guide_guild ~= nil then
		return self.is_guide_guild
	end
	return false
end

function GuildWGData:SetGuideGuild(boo)
	if self.is_guide_guild ~= nil then
		self.is_guide_guild = boo
	end
end

function GuildWGData:GetActivityLevel()
	local daily_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(ACTIVITY_TYPE.KF_LIEKUN)
	if daily_cfg then
		self.liekun_level = daily_cfg.limit_level
	end

	daily_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	if daily_cfg then
		self.xianmengzhan_level = daily_cfg.limit_level
	end

	daily_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(ACTIVITY_TYPE.GUILD_ANSWER)
	if daily_cfg then
		self.guildanswer_level = daily_cfg.limit_level
	end

	daily_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(ACTIVITY_TYPE.GUILD_FB)
	if daily_cfg then
		self.guildfb_level = daily_cfg.limit_level
	end
end

function GuildWGData:__delete()
	-- print_error("-----------------------------------------------------")
	GuildWGData.Instance = nil
	self.liekun_level = nil
	self.xianmengzhan_level = nil
	self.guildanswer_level = nil
	self.guildfb_level = nil
	self.distribute_times = nil
	self.cur_guild_mengzhu_uid = nil --用于记录当前的帮主id
	self.task_auto_lingqu = nil
	self.in_fb_rank_list = {}
	self.guild_member_index = nil
	self:RemberOutScenrType()
		--红点显示
	RemindManager.Instance:UnRegister(RemindName.Guild_ZongLan_GaiKuang)--综览概况
	RemindManager.Instance:UnRegister(RemindName.Guild_ZongLan_RedBag)--综览概况
	RemindManager.Instance:UnRegister(RemindName.Guild_Wage)--仙盟工资

	RemindManager.Instance:UnRegister(RemindName.Guild_Skill)--帮派技能
	-- RemindManager.Instance:UnRegister(RemindName.Guild_Activity)--仙盟活动
	RemindManager.Instance:UnRegister(RemindName.Guild_Activit_ShiLian)--仙盟试炼
	RemindManager.Instance:UnRegister(RemindName.Guild_Activit_DaTi)--活动答题
	RemindManager.Instance:UnRegister(RemindName.Guild_Activit_Boss)--活动Boss
	RemindManager.Instance:UnRegister(RemindName.Guild_Activit_ZhengBa)--活动争霸
	RemindManager.Instance:UnRegister(RemindName.Guild_Activit_LieKun)--活动猎坤
end

-- 帮派每日福利次数
function GuildWGData:SetDaycountIsDayReward(index)
	self.daycount_is_day_reward = index
	-- GuildWGCtrl.Instance:GuildViewFlush(TabIndex.guild_info)
	-- RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面
	-- RemindManager.Instance:Fire(RemindName.Guild_ZongLan)--仙盟综览
	-- RemindManager.Instance:Fire(RemindName.Guild_ZongLan_GaiKuang)
end

-- 帮派领地福利次数
function GuildWGData:SetDaycountLingDiReward(index)
	local daycount_lingdi_reward_list = bit:d2b(index)
	for i=1,10 do
   		self.daycount_lingdi_reward[i] = daycount_lingdi_reward_list[33 - i]
	end
	GuildWGCtrl.Instance:GuildViewFlush(TabIndex.guild_info)
	GuildWGCtrl.Instance:GuildViewFlush()
end

function GuildWGData:GetDaycountIsDayReward()
	return self.daycount_is_day_reward
end

function GuildWGData:GetDaycountLingDiReward()
	return self.daycount_lingdi_reward
end

-- 设置已输入的坐标数量
function GuildWGData:SetChatPosCount(num)
	self.chat_input_pos_count = num
	if self.view and self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_info)
	end
end

-- 获取已输入的坐标数量
function GuildWGData:GetChatPosCount()
	return self.chat_input_pos_count
end

-- 格式化语言文字
function GuildWGData:FormatLanguage(key, str)
	local result = nil
	if nil ~= key and nil ~= str then
		local templet = Language.Guild[key]
		if nil ~= templet then
			result = string.format(templet, str)
		end
	end

	return result
end

function GuildWGData.FormatTime(time_format)
	local status = ""
	time_format = time_format or 0
	local lerp_min = math.floor(time_format / 60)
	local lerp_hour = math.floor(lerp_min / 60)
	local lerp_day = math.floor(lerp_hour / 24)
	local lerp_month = math.floor(lerp_day / 30)
	local lerp_year = math.floor(lerp_month / 30)

	if lerp_year > 0 then
		status = string.format(Language.Common.BeforeXXYear, lerp_year)
	elseif 0 == lerp_year and lerp_month > 0 then
		status = string.format(Language.Common.BeforeXXMonth, lerp_month)
	elseif 0 == lerp_month and lerp_day > 0 then
		status = string.format(Language.Common.BeforeXXDay, lerp_day)
	elseif 0 == lerp_day and lerp_hour > 0 then
		status= string.format(Language.Common.BeforeXXHour, lerp_hour)
	elseif 0 == lerp_hour and lerp_min > 0 then
		status = string.format(Language.Common.BeforeXXMinute, lerp_min)
	else
		status = string.format(Language.Common.BeforeXXMinute, 1)
	end

	return status
end

---------------------------------------------------------------
----仙盟酒会
---------------------------------------------------------------
function GuildWGData:SetGuildPartyStatus(status, open_times)
	self.act_guild_party_status = status or 0
	self.act_guild_party_open_times = open_times or 0
end

function GuildWGData:GetGuildPartyStatus()
	return self.act_guild_party_status
end

function GuildWGData:IsGuildPartyOpen()
	local is_open = self.act_guild_party_status == 2 or false
	return is_open
end

function GuildWGData:GetGuildPartyOpenTimes()
	return self.act_guild_party_open_times
end

function GuildWGData:SetGuildPartyFinsishTime(finish_timestamp)
	self.finish_timestamp_data = finish_timestamp
end

function GuildWGData:GetGuildPartyFinsishTime()
	return self.finish_timestamp_data or 0
end
-----------------------------------------------------------
---仙盟推图
------------------------------------------------------------

function GuildWGData:SetGuildFbIsPass(is_pass)
	if is_pass and is_pass > 0 then
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.GUILD_FB, ACTIVITY_STATUS.CLOSE)
	end

	self.act_guild_fb_is_pass = is_pass == 1
	self.act_guild_fb_pass_state = is_pass

	if self:IsFinishGuildFb() then
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.GUILD_FB, false)
		ActivityWGCtrl.Instance:RemoveActNotice(ACTIVITY_TYPE.GUILD_FB)
	end
end

function GuildWGData:ClearGuildFbPass()
	self.act_guild_fb_is_pass = false
	self.act_guild_fb_pass_state = 0
end

function GuildWGData:GetGuildFbIsPass()
	return self.act_guild_fb_is_pass
end

function GuildWGData:IsFinishGuildFb()
	return self.act_guild_fb_pass_state and self.act_guild_fb_pass_state > 0
end

function GuildWGData:IsGuildFbOpen()--仙盟守护是否开启
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_FB)
end
function GuildWGData:IsGuildAnswerOpen()--仙盟答题
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER)
end
function GuildWGData:IsGuildJDOpen()--仙盟禁地
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.JIUSHEDAO)
end

function GuildWGData:SetGuildMiJingSceneInfo(data)
	self.guild_fb_data = data
	self.is_waring = self.old_beauty_hp ~= data.hp
	if self.old_beauty_hp ~= 0 and self.is_waring then
		GlobalEventSystem:Fire(OtherEventType.GUILD_FB_LONGHUI_HURT)
		GlobalEventSystem:Fire(OtherEventType.GUILD_FB_SHOUWEI_HURT, GuildDataConst.MAX_SHOU_WEI_COUNT + 1, data.hp - self.old_beauty_hp)
	end
	self.old_beauty_hp = data.hp

	for i = 1, GuildDataConst.MAX_SHOU_WEI_COUNT do
		if self.old_door_hp_list[i] ~= 0 and self.old_door_hp_list[i] ~= data.doorHp[i] then
			GlobalEventSystem:Fire(OtherEventType.GUILD_FB_SHOUWEI_HURT, i, data.doorHp[i] - self.old_door_hp_list[i])
		end
		self.old_door_hp_list[i] = data.doorHp[i]
	end

	self:SetGuildRankPos(data)
end

function GuildWGData:SetGuildRankPos(data)
	self.guild_sh_rank_pos = data.guild_rank_pos
end

function GuildWGData:GetGuildRankPos()
	return self.guild_sh_rank_pos
end

function GuildWGData:GetBeautyIsHurt()
	return self.is_waring
end

function GuildWGData:SetBeautyHurtState(status)
	self.is_waring = status
end

function GuildWGData:GetBeautyPos()
	local other_cfg = self:GetGuildSHOtherCfg()
	return other_cfg.xiannv_x, other_cfg.xiannv_y
end

function GuildWGData:GetGuildFBDoorCfg(index)
	if not self.guild_fb_door_cfg then
		self.guild_fb_door_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").door_cfg
	end
	return self.guild_fb_door_cfg[index]
end

function GuildWGData:GetGuildMiJingWave()
	--怪物波数限制10
	local wave = 0
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").wave_cfg
	if wave_cfg[0] ~= nil then
		wave = #wave_cfg + 1
	else
		wave = #wave_cfg
	end
	return wave
end

function GuildWGData:GetGuildFbData()
	local jindu = ""
	local remain_num = 0
	local guard_blood = 100
	local notify_reason = 0
	local mijing_exp = 0
	local item_data = {}
	local item_data_2 = {}
	local curr_jindu = 0
	local boss_hint = 0
	local is_finish = 0
	local is_pass = 0
	local doorHp = {}
	local doorMaxHp = {}
	local rankList = {}
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").wave_cfg
	if nil ~= self.guild_fb_data then
		local data = self.guild_fb_data
		notify_reason = data.notify_reason
		doorHp = data.doorHp
		doorMaxHp = data.doorMaxHp
		rankList = data.rankList
		is_finish = data.is_finish
		is_pass = data.is_pass
		mijing_exp = data.exp_total
		local max_wave = self:GetGuildMiJingWave()
		jindu = math.min(data.curr_wave + 1, max_wave) .. "/" .. max_wave
		remain_num = data.wave_enemy_count
		-- mingji_exp = CommonDataManager.ConverExp(data.exp_total)

		local rate = data.hp / data.max_hp
		if data.hp == 0 then
			rate = 0
		end
		guard_blood = (rate - rate % 0.0001) * 100
		curr_jindu = data.curr_wave
		for k,v in pairs(wave_cfg) do
			if curr_jindu == v.wave then
				item_data = v.reward or {}
				item_data_2 = v.pass_reward_item[0] or {}
				boss_hint = v.boss_hint
			end
		end
	end
	local fb_task_data = {}
	fb_task_data[1] = {
		tab_img = "word_fuben_info",
		text_t = {
			{str = jindu},
			{str = guard_blood},
			{str = remain_num},
		},
		process_flag = {
			boss_flag = boss_hint,
			finsh_flag = is_finish,
			pass_flag = is_pass,
		},
		item_t = {
			{item_data = item_data,item_data_2 = item_data_2},
		},
		doorHp = doorHp,
		doorMaxHp = doorMaxHp,
		rankList = rankList,
		mijing_exp = mijing_exp,
		notify_reason = notify_reason,
	}
	-- print_error(fb_task_data)
	return fb_task_data
end

--设置帮派试炼的个人排行信息
function GuildWGData:SetGuildMiJingRoleInfo(protocol)
	self.rank = protocol.rank
	self.hurt_val = protocol.hurt_val
	--print_error("设置帮派试炼的个人排行信息 ", protocol)
	-- if FuBenWGCtrl.Instance.fuben_mijing_view:IsOpen() then
	-- 	FuBenWGCtrl.Instance.fuben_mijing_view:FlushRoleInfo(self.rank,self.hurt_val)
	-- end
end

--根据怪物的波数来获取是那个门要生成怪物
function GuildWGData:GetDoorNum()
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").monster_cfg
	local monster_path = ConfigManager.Instance:GetAutoConfig("guildfb_auto").monster_path
	if monster_cfg == nil then return end
	local monsternum = self.guild_fb_data.curr_wave
	local mdoor_num_list = {}
	local index = 1
	local savedat = false
	for k,v in pairs(monster_cfg) do
		savedat = true
		if v.wave == monsternum then
			if not IsEmptyTable(mdoor_num_list) then
			for i=1,#mdoor_num_list do
					if mdoor_num_list[i] == monster_path[v.path_idx+1].door_id then
						savedat = false
					end
				end
				if savedat then
					mdoor_num_list[index] = monster_path[v.path_idx + 1].door_id
					index = index + 1
				end
			else
				if savedat then
					mdoor_num_list[index] = monster_path[v.path_idx + 1].door_id
					index = index + 1
				end
			end

		end

	end
	return mdoor_num_list
end
--获取当前怪物的波束
function GuildWGData:GetNowMonsterLun()
	return self.guild_fb_data and self.guild_fb_data.curr_wave or 0
end

function GuildWGData:GetGuildFBData()
	return self.guild_fb_data
end

--获取当前点击的门号
function GuildWGData:GetMiJingDianDoor(doormark)
	self.doormark = doormark
end
--获取当前点击的门号
function GuildWGData:GetMjGuajiPos()
	local pos_x = 0
	local pos_y = 0

	if IsEmptyTable(self.guild_sh_other_cfg) then
		return pos_x, pos_y
	end

	if self.doormark == 4 then
		pos_x = self.guild_sh_other_cfg.guaji_x
		pos_y = self.guild_sh_other_cfg.guaji_Y
	elseif self.doormark ~= nil then
		local door_cfg = self:GetGuildFBDoorCfg(self.doormark)
		if not IsEmptyTable(door_cfg) then
			pos_x = door_cfg.desc_pos_x
			pos_y = door_cfg.desc_pos_y
		end
	end

	return pos_x, pos_y
end

function GuildWGData:GetGuildFBMonsterPath()
	if not self.mijing_monster_path_cfg then
		self.mijing_monster_path_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").monster_path
	end
	if self.doormark ~= nil then
		local cfg = self.mijing_monster_path_cfg[self.doormark]
		for k,v in pairs(self.mijing_monster_path_cfg) do
			if v.door_id == self.doormark -1 then
				return v.pos_x_1,v.pos_y_1
			end
		end
	end
end

--根据轮数获取仙盟守护帮贡
function GuildWGData:GetGuildBangGong(cur_num)
	--因为配表波数是从0开始，但实际是从1波显示
	if self.guild_fb_data.is_pass == 0 then
		cur_num = cur_num - 1
	end
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").wave_cfg
	local cur_banggong = 0
	for k,v in pairs(wave_cfg) do
		if cur_num >= v.wave then
			cur_banggong = cur_banggong + v.add_longhun
		end
	end

	return cur_banggong
end

--根据轮数获取仙盟守护经验
function GuildWGData:GetGuildExp(cur_num)
	--因为配表波数是从0开始，但实际是从1波显示
	if self.guild_fb_data.is_pass == 0 then
		cur_num = cur_num - 1
	end
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").wave_cfg
	local cur_exp = 0
	for k,v in pairs(wave_cfg) do
		if cur_num >= v.wave then
			cur_exp = cur_exp + v.guild_exp
		end
	end

	return cur_exp
end

--根据轮数获取仙盟守护的累积物品
function GuildWGData:GetGuildItemList(cur_num)
	--因为配表波数是从0开始，但实际是从1波显示
	if self.guild_fb_data.is_pass == 0 then
		cur_num = cur_num - 1
	end
	local reward_list = {}
	local wave_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").wave_cfg
	local wave_cfg_2 = DeepCopy(wave_cfg)
	local cur_banggong = 0
	for k,v in pairs(wave_cfg_2) do
		if cur_num >= v.wave then
			local is_can_insert1 = true
			local is_can_insert2 = true
			if not IsEmptyTable(reward_list) then
				if v.reward and  v.reward.item_id > 0 then
					for m,n in pairs(reward_list) do
						if n.item_id == v.reward.item_id then
							n.num = n.num + v.reward.num
							is_can_insert1 = false
						end
					end
					if is_can_insert1 and v.reward.item_id > 0 then
						table.insert(reward_list,v.reward)
					end
				end
				if v.pass_reward_item[0] and v.pass_reward_item[0].item_id > 0 then
					for m,n in pairs(reward_list) do
						if n.item_id == v.pass_reward_item[0].item_id then
							n.num = n.num + v.pass_reward_item[0].num
							is_can_insert2 = false
						end
					end
					if is_can_insert2 and v.pass_reward_item[0].item_id > 0 then
						table.insert(reward_list,v.pass_reward_item[0])
					end
				end
			else
				if v.reward and  v.reward.item_id > 0 then
					table.insert(reward_list,v.reward)
				end
				if v.pass_reward_item[0] and v.pass_reward_item[0].item_id > 0 then
					table.insert(reward_list,v.pass_reward_item[0])
				end
			end
		end
	end

	local _, rank_index = self:GetMainRoleRankData()
	
	-- if rank_index then
	local get_cfg = nil
	if rank_index then
		get_cfg = self:GetRankRewardCfg(rank_index - 1) --第一名从0开始
	else
	--如果没排名 取最后一档配置
		local cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").rank_reward
		get_cfg = cfg[#cfg]
	end
	--print_error(rank_index - 1, get_cfg.gongxian_reward)
	if not IsEmptyTable(get_cfg) then
		for k1, v1 in pairs(get_cfg.reward_item) do
			local is_can_insert3 = true
			for i, v in ipairs(reward_list) do
				if v.item_id == v1.item_id then
					v.num = v.num + v1.num
					is_can_insert3 = false
				end
			end
			if is_can_insert3 and v1.item_id > 0 then
				table.insert(reward_list, v1)
			end
		end

		for i, v in ipairs(reward_list) do
			if v.item_id == get_cfg.gongxian_reward.item_id then
				v.num = v.num + get_cfg.gongxian_reward.num
			end
		end
	end
	-- end
	return reward_list
end

--获取仙盟守护最后的存活数量
function GuildWGData:GetGuildCunHuoNum()
	local cunhuo_num = 0
	local fuben_info = self:GetGuildFbData()
	local text_list = fuben_info[1].text_t
	if (text_list[2].str/100) > 0 then
		cunhuo_num = cunhuo_num + 1
	end
    for i = 1, 3 do
        local num = fuben_info[1].doorHp[i] / fuben_info[1].doorMaxHp[i]
        if num > 0 then
        	cunhuo_num = cunhuo_num + 1
        end
    end
	return cunhuo_num
end

--最终的结算面板
function GuildWGData:IsCanShowJieSuanView()
	if self.guild_fb_data.notify_reason == JIUSHEDAOFB_NOTIFY_REASON.NOTIFY_REASON_FINISH then
		local fuben_info = self:GetGuildFbData()
		Field1v1WGCtrl.Instance:OpenFinish(fuben_info,DATAFROMTHERE.GUILD_SHOWHU)
	end
end

--仙盟守护需要特殊处理的血条id
function GuildWGData:SpecticalHpID()
	local ID_list = {}
	local door_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").door_cfg
	local other_cfg = self:GetGuildSHOtherCfg()
	local id_num = 1
	if door_cfg then
		for k,v in pairs(door_cfg) do
			ID_list[id_num] = v.monster_ID
			id_num = id_num + 1
		end
	end

	if other_cfg.xiannv_id ~= nil then
		ID_list[id_num] = other_cfg.xiannv_id
	end
	return ID_list
end

--获取元宝、铜币鼓舞消耗
function GuildWGData:GetGuWuConsume()
	local other_cfg = self:GetGuildSHOtherCfg()
	return other_cfg.gold_guwu_cost, other_cfg.coin_guwu_cost
end

--获取元宝鼓舞奖励物品列表
function GuildWGData:GetGoldGuWuItemList()
	local other_cfg = self:GetGuildSHOtherCfg()
	local data_list = {}
	for k,v in pairs(other_cfg.gold_reward_item) do
		table.insert(data_list, v)
	end
	return data_list
end

--获取铜币鼓舞奖励物品列表
function GuildWGData:GetCoinGuWuItemList()
	local other_cfg = self:GetGuildSHOtherCfg()
	local data_list = {}

	for k,v in pairs(other_cfg.coin_reward_item) do
		table.insert(data_list, v)
	end

	return data_list
end

--获取单次元宝鼓舞效果
function GuildWGData:GetOnceGoldGuWuEffect()
	local other_cfg = self:GetGuildSHOtherCfg()
	return other_cfg.gold_guwu_add_per / 100
end

--获取单次铜币鼓舞效果
function GuildWGData:GetOnceCoinGuWuEffect()
	local other_cfg = self:GetGuildSHOtherCfg()
	return other_cfg.coin_guwu_add_per / 100
end

--获取当前鼓舞效果  return 元宝效果,铜币效果
function GuildWGData:GetCurGuWuEffect()
	local other_cfg = self:GetGuildSHOtherCfg()
	if self.guild_fb_data then
		local gold = self.guild_fb_data.gold_guwu_times * other_cfg.gold_guwu_add_per / 100
		local coin = self.guild_fb_data.coin_guwu_times * other_cfg.coin_guwu_add_per / 100
		return gold, coin
	else
		return nil,nil
	end
end

--获取当前总鼓舞效果  return 总效果
function GuildWGData:GetCurGuWuTotalEffect()
	local other_cfg = self:GetGuildSHOtherCfg()
	if self.guild_fb_data then
		local gold = self.guild_fb_data.total_gold_guwu_tims * other_cfg.gold_guwu_add_per / 100
		local coin = self.guild_fb_data.total_coin_guwu_tims * other_cfg.coin_guwu_add_per / 100
		return gold + coin
	else
		return nil
	end
end

--获取当前鼓舞次数
function GuildWGData:GetGuWuTimes()
	if self.guild_fb_data then
		return self.guild_fb_data.gold_guwu_times, self.guild_fb_data.coin_guwu_times
	else
		return 0,0
	end
end

--获取鼓舞最大次数
function GuildWGData:GetGuWuTotalTimes()
	local other_cfg = self:GetGuildSHOtherCfg()
	return other_cfg.gold_guwu_max_times, other_cfg.coin_guwu_max_times
end

--获取是否进入跨服比拼
function GuildWGData:GetIsEnterCrossBiPing()
	local other_cfg = self:GetGuildSHOtherCfg()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	return server_day >= other_cfg.opengame_day
end

--获取活动奖励预览列表
function GuildWGData:GetActRewardPreviewItemList()
	local client_show_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").client_show
	local data_list = {}

	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if client_show_cfg then
		for k,v in pairs(client_show_cfg) do
			if cur_day >= v.opengame_day_min and cur_day < v.opengame_day_max then
				return v.reward_item
			end
		end
	end
	return nil
end

function GuildWGData:GetPersonRankJieSuanItemList()
	local curr_wave = self:GetNowMonsterLun()
	return self:GetGuildItemList(curr_wave)
end

function GuildWGData:GetMainRoleRankData()
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
 	local list = self.guild_fb_data.rankList
 	for k,v in pairs(list) do
 		if v.roleid == mainrole_vo.role_id then
 			return v, k
 		end
 	end
 	return nil, nil
end

function GuildWGData:GetGuildLogicViewItemList()
	local guild_rank_info = self:GetGuildShouHuInFBRankInfo()
	local rank = 1 	-- 默认值
	for k, v in ipairs(guild_rank_info) do
		if v.guild_id == RoleWGData.Instance.role_vo.guild_id then
			rank = k
		end
	end

	local data_list = self:GetGuildItemRewardByRank(rank)
	return data_list
end

function GuildWGData:SetGuildFbRankInfo(protocol)
	self.ui_guild_rank_data_list = protocol.guild_rank_info
end

function GuildWGData:GetGuildFbRankInfo()
	local info_list = {}
	local data_list = self.ui_guild_rank_data_list
	for i=1,#data_list do
		if data_list[i].guild_id > 0 then
			info_list[#info_list + 1] = data_list[i]
		end
	end
	return info_list
end

--设置个人排行列表(帮派界面UI数据)
function GuildWGData:SetGuildFbPersonRankInfo(protocol)
	self.ui_person_data.guild_rank 	= protocol.guild_rank 					-- 仙盟排名 -1为未通
	self.ui_person_data.pass_wave 	= protocol.pass_wave					-- 通关波数 -1为未参与
	self.ui_person_data.pass_time = protocol.pass_time 						-- 通关时间
	self.ui_person_data.active_reward_count = protocol.active_reward_count	-- 活跃奖励份数
	self.ui_person_data.person_rank = protocol.person_rank					-- 个人伤害排名 -1 为未参与
	self.ui_person_data.total_hurt 	= protocol.total_hurt 					-- 个人总伤害
	self.ui_person_rank_data_list 	= protocol.person_rank_info 			-- 个人排名列表
end

--获取个人伤害， 个人排名
function GuildWGData:GetPersonHurtRank()
	if self.ui_person_data.person_rank then
		if self.ui_person_data.person_rank == -1 then
			return self.ui_person_data.total_hurt, self.ui_person_data.person_rank
		end
		return self.ui_person_data.total_hurt, self.ui_person_data.person_rank + 1
	end
	return self.ui_person_data.total_hurt, nil
end
--获取通过时间， 通关波数， 仙盟排名
function GuildWGData:GetGuildPassTimeWaveRank()
	if self.ui_person_data.guild_rank then
		if self.ui_person_data.guild_rank == -1 then
			return self.ui_person_data.pass_time, self.ui_person_data.pass_wave, self.ui_person_data.guild_rank
		end
		return self.ui_person_data.pass_time, self.ui_person_data.pass_wave, self.ui_person_data.guild_rank --+ 1
	end
	return self.ui_person_data.pass_time, self.ui_person_data.pass_wave,  nil
end

function GuildWGData:GetGeRenRankDataCount()
	return #self.ui_person_rank_data_list
end

--获取个人排行列表
function GuildWGData:GetGeRenRankDataList()
	local data_list = {}
	for i, v in ipairs(self.ui_person_rank_data_list) do
		table.insert(data_list, v)
	end

	for i, v in ipairs(self.person_rank_after_ten_cfg_list) do
		local data = {}
		data.rank_str = string.format(Language.Guild.SHRankStr, v.min_rank_pos + 1, v.max_rank_pos + 1)
		table.insert(data_list, data)
	end
	return data_list
end

--获取帮派排行列表
function GuildWGData:GetGuildRankDataList()
	local data_list = {}
	for i, v in ipairs(self.ui_guild_rank_data_list) do
		table.insert(data_list, v)
	end
	for i, v in ipairs(self.guild_rank_after_ten_cfg_list) do
		local data = {}
		data.rank_str = string.format(Language.Guild.SHRankStr, v.min_rank_pos + 1, v.max_rank_pos + 1)
		table.insert(data_list, data)
	end
	return data_list
end

--获取个人排行默认列表（没有任何排名，没有数据时使用）
function GuildWGData:GetDefaultPersonRankDataList()
	local data_list = {}
	for i = 1, 10 do
		local data = {}
		data.total_hurt = Language.Guild.SHJingQingQiDai
		data.name = Language.Guild.SHJingQingQiDai
		data.is_fake_data = true
		table.insert(data_list, data)
	end
	for i, v in ipairs(self.person_rank_after_ten_cfg_list) do
		local data = {}
		data.rank_str = string.format(Language.Guild.SHRankStr, v.min_rank_pos + 1, v.max_rank_pos + 1)
		table.insert(data_list, data)
	end
	return data_list
end
--获取帮派排行默认列表（没有任何排名，没有数据时使用）
function GuildWGData:GetDefaultGuildRankDataList()
	local data_list = {}
	for i = 1, 10 do
		local data = {}
		data.pass_time = Language.Guild.SHJingQingQiDai
		data.guild_name = Language.Guild.SHJingQingQiDai
		data.is_fake_data = true
		table.insert(data_list, data)
	end
	for i, v in ipairs(self.guild_rank_after_ten_cfg_list) do
		local data = {}
		data.rank_str = string.format(Language.Guild.SHRankStr, v.min_rank_pos + 1, v.max_rank_pos + 1)
		table.insert(data_list, data)
	end
	return data_list
end

--设置第10名以后的个人排行奖励配置(所有)
function GuildWGData:_SetPersonAfterTenRankRewardCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").rank_reward
	local get_cfg_list = {}
	for i, v in pairs(cfg) do
		if 10 <= v.min_rank_pos then
			table.insert(get_cfg_list, v)
		end
	end
	return get_cfg_list
end

function GuildWGData:GetBestFiveItemList(item_list)
	local ret_list = {}
	local list = {}
	for i, v in ipairs(item_list) do
		if v.item_id > 0 then
			local data = v
			local cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if cfg then
				data.color = cfg.color
			else
				data.color = 1
			end
			table.insert(list, data)
		end
	end
	table.sort(list, SortTools.KeyLowerSorter("color"))
	for i, v in ipairs(list) do
		table.insert(ret_list, v)
		if i == 5 then
			break
		end
	end
	return ret_list
end

--获取第10名以后的个人排行奖励配置(所有)
function GuildWGData:GetPersonAfterTenRankRewardCfg()
	return self.person_rank_after_ten_cfg_list
end
--获取第10名以后的帮派排行奖励配置(所有)
function GuildWGData:GetGuildAfterTenRankRewardCfg()
	return self.guild_rank_after_ten_cfg_list
end

--获取第10名以后的个人排行奖励配置(指定ListView的Index)
function GuildWGData:GetPersonAfterTenRankItemRewardList(index)
	local cfg_index = index - 10
	local cfg = self.person_rank_after_ten_cfg_list[cfg_index]
	if cfg then
		local data_list = {}
		for i, v in pairs(cfg.reward_item) do
			table.insert(data_list, v)
		end
		return data_list
	end
	return {}
end
--获取第10名以后的帮派排行奖励配置(指定ListView的Index)
function GuildWGData:GetGuildAfterTenRankItemRewardList(index)
	local cfg_index = index - 10
	local cfg = self.guild_rank_after_ten_cfg_list[cfg_index]
	if cfg then
		local data_list = {}
		for i, v in pairs(cfg.reward_item) do
			table.insert(data_list, v)
		end
		return data_list
	end
	return {}
end

function GuildWGData:GetRankRewardCfg(rank, cfg)
	local real_rank = rank
    local real_cfg = cfg or ConfigManager.Instance:GetAutoConfig("guildfb_auto").rank_reward
	local get_cfg = nil
	for i, v in pairs(real_cfg) do
		if real_rank >= v.min_rank_pos and real_rank <= v.max_rank_pos then
			get_cfg = v
			break
		end
	end
	return get_cfg
end

--根据个人排名获取物品奖励(指定Index)
function GuildWGData:GetPersonItemRewardByRank(rank)
	local real_rank = rank
	local get_cfg = self:GetRankRewardCfg(rank)
	local data_list = {}
	if get_cfg then
		for i, v in pairs(get_cfg.reward_item) do
			table.insert(data_list, v)
		end
	end
	return data_list
end


--根据天数取帮派排名物品奖励(新)
function GuildWGData:GetGuildItemRewardByDayAndRank(rank)
	local real_cfg = self.guild_rank_reawrd_cfg
	--当前开服天数
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k,v in pairs(real_cfg) do
		if cur_day >= v.opengame_day_min and cur_day <= v.opengame_day and rank >= v.rank_min and rank <= v.rank_max then
			return v
		end
	end
	return nil
end

--根据帮派排名获取物品奖励(指定Index)
function GuildWGData:GetGuildItemRewardByRank(rank)
	local get_cfg = self:GetGuildItemRewardByDayAndRank(rank)
	local data_list = {}
	if get_cfg then
		for i, v in pairs(get_cfg.show_reward_item) do
			table.insert(data_list, v)
		end
	end
	return data_list, get_cfg
end

--根据帮派排名获取物品奖励(指定Index)
function GuildWGData:GetGuildItemRewardByRankNum(rank)
	local get_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").guild_rank_reward
	local data_list = {}
	local day = nil
	local guild_vo = GuildDataConst.GUILDVO
	local guild_level = guild_vo.guild_level > 0 and guild_vo.guild_level or 1
	local open_day =  TimeWGCtrl.Instance:GetCurOpenServerDay()

	for k,v in pairs(get_cfg) do
		if nil ~= v.opengame_day then
			if v and (nil == day or v.opengame_day == day) and open_day  <= v.opengame_day then
				if v.rank_min <= rank and rank <= v.rank_max then
					return v
				end
			end
		end
	end
	return data_list
end

--获取仙盟守护UI界面展示boss_cfg
function GuildWGData:GetSHBossCfg()
	local monster_cfg = ConfigManager.Instance:GetAutoConfig("guildfb_auto").monster_cfg
	local monster_id = nil
	for i, v in pairs(monster_cfg) do
		if v.wave == 7 then
			monster_id = v.monster_id
		end
	end
	local boss_id = RankWGData.Instance:GetReplaceMonster(monster_id)
	local boss_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
	return boss_config
end

--初始化规则面板拍品显示列表
function GuildWGData:InitPaiPinList()
    local other_cfg = self:GetGuildSHOtherCfg()
    if other_cfg.item then
        local list = Split(other_cfg.item, "|")
        self.paipin_list = {}
        for i, v in pairs(list) do
            self.paipin_list[tonumber(v)] = true
        end
    end
end

function GuildWGData:GetContainPaiPin(item_id)
    return self.paipin_list[item_id]
end

-----------------------------------------------------------
-- 仙盟篝火
-----------------------------------------------------------
function GuildWGData:SetGuildBonfireStatus(status, open_times)
	self.act_guild_bonfire_status = status or 0
	self.act_guild_bonfire_open_times = open_times or 0
end

function GuildWGData:GetGuildBonfireStatus()
	return self.act_guild_bonfire_status or 0
end

function GuildWGData:IsGuildBonfireOpen()
	return self.act_guild_bonfire_status == GuildDataConst.BonfireStatus.Open
end

function GuildWGData:GetGuildBonfireOpenTimes()
	return self.act_guild_bonfire_open_times
end

function GuildWGData:SetGuildBonfireFinsishTime(finish_timestamp)
	self.fb_finish_timestamp_data = finish_timestamp
end

function GuildWGData:GetGuildBonfireFinsishTime()
	return self.fb_finish_timestamp_data or 0
end

function GuildWGData:IsGatherBonfire(gather_id)
	return gather_id == ConfigManager.Instance:GetAutoConfig("guildbonfire_auto").other_cfg[1].gathar_id
end

function GuildWGData:GetBonfireOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("guildbonfire_auto").other_cfg[1]
end

-----------------------------------------------------------
-- 仙盟运势
-----------------------------------------------------------
function GuildWGData:GetLuckyZhufuCfg()
	return ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].guild_lucky_zhufu_times or 0
end

function GuildWGData:GetLuckyCfg(lucky_level)
	return ConfigManager.Instance:GetAutoConfig("guildconfig_auto").member_lucky[lucky_level]
end

function GuildWGData:GetGuildLuckyInfo()
	return self.lucky_info
end

function GuildWGData:SetGuildLuckyInfo(info)
	self.lucky_info.reason = info.reason
	self.lucky_info.member_count = info.member_count
	self.lucky_info.member_luckyinfo_list = info.member_luckyinfo_list

	local my_id = RoleWGData.Instance:InCrossGetOriginUid()
	for k,v in pairs(self.lucky_info.member_luckyinfo_list) do
		if v.uid == my_id then
			self.my_yunshi_info = v
		end
	end
end

--把查询到的玩家最基本的信息保存起来
function GuildWGData:SetRoleBaseInfoAck( protocol )
	local guildvo = GuildDataConst.GUILDVO.tuanzhang_uid
	if not protocol or protocol.role_id ~= guildvo then
		self.role_base_info_ack = nil
		return
	end
	self.role_base_info_ack = {}
	self.role_base_info_ack.appearance = protocol.appearance
	self.role_base_info_ack.role_id = protocol.role_id
	self.role_base_info_ack.role_name = protocol.role_name
	self.role_base_info_ack.prof = protocol.prof
	self.role_base_info_ack.sex = protocol.sex
end



function GuildWGData:GetRoleBaseInfoAck()
	if not self.role_base_info_ack then
		-- print_error("没有相关角色信息")
		self.role_base_info_ack = nil
		return
	end
	return self.role_base_info_ack
end

function GuildWGData:GetInviteLuckyZhufu()
	return self.invite_lucky_zhufu
end

function GuildWGData:SetInviteLuckyZhufu(info)
	self.invite_lucky_zhufu.reason = info.reason
	self.invite_lucky_zhufu.member_count = info.member_count
end

--求仙盟活动的tab_index 是反过来的
function GuildWGData.GetGuildActIndexById(guild_act_id)

	local active_menu = ConfigManager.Instance:GetAutoConfig("guild_active_auto").active_menu
	local config = active_menu[guild_act_id]
	local contract_idnex = config and config.show_order or 0
	return #active_menu - contract_idnex + 1
end

--由于tab_index 反过来的,所以要转换一下才是真正的显示顺序
function GuildWGData.GetGuildActIdByTabIndex(index)
	local active_menu = ConfigManager.Instance:GetAutoConfig("guild_active_auto").active_menu
	local real_index = #active_menu - index + 1
	for k,v in pairs(active_menu) do
		if v.show_order == real_index then
			return v.key
		end
	end
end

-----------------------------------------------------------
---仙盟凶兽
------------------------------------------------------------

function GuildWGData:SetGuildActiveDegree(guild_active_degree)
	self.xiong_shou_info.guild_active_degree = guild_active_degree
end

function GuildWGData:GetXiongShouInfo()
	local cur_degree = self.xiong_shou_info.guild_active_degree
	if nil == cur_degree then
		return
	end
	local word_level = RankWGData.Instance:GetWordLevel()
	local beast_cfg = ConfigManager.Instance:GetAutoConfig("guild_active_auto").beast
	local show_boss = nil

	self.xiong_shou_info.is_can_call = false
	for i, v in ipairs(beast_cfg) do
		if word_level >= v.world_level_min then
			show_boss = show_boss or v
			if cur_degree >= v.guild_active_degree_min then
				show_boss = v
				self.xiong_shou_info.is_can_call = true
			end
		end
	end
	local boss_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[show_boss.boss_id]
	self.xiong_shou_info.degree_config = show_boss
	self.xiong_shou_info.boss_config = boss_config

	return self.xiong_shou_info
end

function GuildWGData:GetNextGuildMonsterCfg(is_can_call, key)
	key = key or 0
	local show_key = is_can_call and key + 1 or key
	for k,v in ipairs(ConfigManager.Instance:GetAutoConfig("guild_active_auto").beast) do
		if v.key == show_key then
			return v
		end
	end
	return nil
end

function GuildWGData.GetMaxGuildActDegree()
	local act_degree = 0
	for k,v in pairs(ConfigManager.Instance:GetAutoConfig("guild_active_auto").beast) do
		if act_degree < v.guild_active_degree_min then
			act_degree = v.guild_active_degree_min
		end
	end
	return act_degree
end

function GuildWGData:SetXiongShouStatus(status, open_times)
	self.act_xiong_shou_status = status or 0
	self.act_xiong_shou_open_times = open_times or 0
end

function GuildWGData:GetXiongShouStatus()
	return self.act_xiong_shou_status
end

function GuildWGData:IsXiongShouOpen()
	return self.act_xiong_shou_status == 2
end

function GuildWGData:GetXiongShouOpenTimes()
	return self.act_xiong_shou_open_times
end

function GuildWGData:SetXiongShouFinsishTime(finish_timestamp)
	self.xiongshou_finish_timestamp = finish_timestamp
end

function GuildWGData:GetXiongShouFinsishTime()
	return self.xiongshou_finish_timestamp or 0
end

function GuildWGData.GetHallCfgByLvAndType(lv, hall_type)
	local hall_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").hall_config
	for k,v in pairs(hall_cfg) do
		if v.hall_type == hall_type and v.level == lv then
			return v
		end
	end
	return nil
end


function GuildWGData.GetHallCfgByLv(lv)
	local hall_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").hall_config
	local hall_cfg_t = {}
	for k,v in pairs(hall_cfg) do
		if v.level == lv then
			table.insert(hall_cfg_t, v)
		end
	end
	return hall_cfg_t
end

function GuildWGData.GetGuildMaxLv()
	local lv = 0
	local hall_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").hall_config
	for k,v in pairs(hall_cfg) do
		if v.level > lv then
			lv = v.level
		end
	end
	return lv
end

-- 设置兑换次数列表
function GuildWGData:SetExchangeList(list)
	self.exchange_t = list
end

-- 获取兑换次数
function GuildWGData:GetExchangeList(id)
	return self.exchange_t[id] or 0
end

-- 设置BOSSS列表
function GuildWGData:SetBossList(list)
	self.boss_list = list
end

-- 获取BOSS数据
function GuildWGData:GetBossListByIndex(index)
	return self.boss_list[index]
end

-- 获取BOSS数据
function GuildWGData:GetNextBossIndex()
	for i = 0, #self.boss_list do
		local boss_info = self.boss_list[i]
		if boss_info and boss_info.killer_roleid == 0 then
			return i + 1
		end
	end
	return -1
end

-- 设置BOSSS奖励列表
function GuildWGData:SetBossRewardList(list)
	self.record_list = list
end

-- 设置加入宗门冷却时间
function GuildWGData:SetGuildRoleOtherInfo(guild_cd_time)
	self.guild_cd_time = guild_cd_time
end

function GuildWGData:GetJoinGuildCdTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local d_value = self.guild_cd_time - server_time
	local cd_time = d_value > 0 and d_value or 0
	return d_value > 0, cd_time
end

-- 获取BOSS奖励数据
function GuildWGData:GetBossRewardListByIndex(index)
	return self.record_list[index] or 0
end

--猎鲲地带开启提醒
function GuildWGData:SetLieKunRemind(num)
	if num == nil then
		return self.liekun_remind_num
	end
	self.liekun_remind_num = num
end

-- 可捐献提醒
function GuildWGData:GetGuildJxRemind()
	local other_config = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1]
	if ItemWGData.Instance:GetHasItemInBag(other_config.jianshe_item_id) and GuildDataConst.GUILDVO.guild_id > 0 then
		return 1
	end

	return 0
end

-- 可领取boss奖励提醒
function GuildWGData:GetGuildBossRewardRemind()
	for k,v in pairs(self.boss_list) do
		if v.killer_roleid > 0 and self:GetBossRewardListByIndex(k) <= 0 then
			return 1
		end
	end
	return 0
end

-- boss可击杀提醒
function GuildWGData:GetKillGuildBossRemind()
	local next_index = self:GetNextBossIndex()
	local role_level = RoleWGData.Instance.role_vo.level
	if next_index ~= -1 and role_level >= 160 then
		return 1
	end
	return 0
end

-- 仙盟活动可开启提醒
function GuildWGData:GetGuildActOpenRemind()
	if self:IsGuildFbOpen() and self:GetGuildFbIsPass() then
		return 1
	end
	return 0
end

-- 仙术
function GuildWGData:SetXianshuLv(lv)
	self.xianshu_lv = lv
end

function GuildWGData:GetXianshuLv()
	return self.xianshu_lv
end

function GuildWGData:GetXianShuConfig(lv,xianshu_type)
	local cfg = ConfigManager.Instance:GetAutoConfig("guild_xianshu_auto").uplevel_config
	for k,v in pairs(cfg) do
		if lv == v.xianshu_level and xianshu_type == v.xianshu_type then
			return v
		end
	end
	return nil
end

function GuildWGData.GetXianShuName(xianshu_type)
	local cfg = ConfigManager.Instance:GetAutoConfig("guild_xianshu_auto").uplevel_config
	for k,v in pairs(cfg) do
		if xianshu_type == v.xianshu_type then
			return v.longhui_name
		end
	end
	return ""
end

function GuildWGData:GetGuildLevelCfg(lv)
	local cfg =  ConfigManager.Instance:GetAutoConfig("guildconfig_auto").guild_level
	for k,v in pairs(cfg) do
		if lv == v.level then
			return v
		end
	end
	return nil
end

-- 帮主的领地福利
function GuildWGData:GetGuildFuliBangZhu(index)
	local cfg =  ConfigManager.Instance:GetAutoConfig("guildconfig_auto").guild_lingdi_reward
	for k,v in pairs(cfg) do
		if index == v.need_rank and 0 == v.seq then
			return v
		end
	end
	return nil
end

function GuildWGData:GetGuildFuliRewardList(index)
	local cfg =  ConfigManager.Instance:GetAutoConfig("guildconfig_auto").guild_lingdi_reward
	local ph_list_data = {}
	for k,v in pairs(cfg) do
		if index == v.need_rank and 0 ~= v.seq then
			ph_list_data[v.seq] = v
		end
	end
	return ph_list_data
end

function GuildWGData:GetGuildFuliRewardItem(rank, index)
	local list_data = self:GetGuildFuliRewardList(rank)
	if next(list_data) ~= nil then
		return list_data[index]
	end
	return nil
end

function GuildWGData:GetGuildFightCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("gongchengzhanconfig_auto").guild_rank_client
	return cfg
end

function GuildWGData:SetGuildFightData(data)
	self.guild_rank_data = data
end

function GuildWGData:GetGuildFightData(rank)
	for k, v in ipairs(self.guild_rank_data) do
		if v.rank == rank then
			return v
		end
	end
	return nil
end

function GuildWGData:OnGongChengZhanOwnerInfo(protocol)
	self.win_guild_id = protocol.win_guild_id
	self.chengzhu = protocol.chengzhu
	self.guild_gongcheng_info = protocol.guild_info
end

function GuildWGData:GetGuildGongChengInfo()
	return self.guild_gongcheng_info
end

function GuildWGData:GetGuildGongChengId()
	return self.chengzhu or 0
end

-- 攻城战城主vo
function GuildWGData:SetGongChengZhanOwnerVo(vo)
	self.gong_cheng_vo = __TableCopy(vo)
end

function GuildWGData:SetGongChengZhanAiRenVo(vo)
	self.gong_cheng_airen_vo = __TableCopy(vo)
end

function GuildWGData:GetChengZhuVo()
	return self.gong_cheng_vo
end

function GuildWGData:GetChengZhuAiRenVo()
	return self.gong_cheng_airen_vo
end

function GuildWGData:SetCurMengZhuUid(uid)
	self.cur_guild_mengzhu_uid = uid --用于记录当前的帮主id
end
function GuildWGData:GetCurMengZhuUid()
	return self.cur_guild_mengzhu_uid
end
function GuildWGData:GetGuildAllFuliRewardList(index)
	local cfg =  ConfigManager.Instance:GetAutoConfig("guildconfig_auto").guild_lingdi_reward
	local ph_list_data = nil
	for k,v in pairs(cfg) do
		if index == v.need_rank then
			if ph_list_data == nil then
				ph_list_data = {}
			end
			ph_list_data[v.seq] = v
		end
	end
	return ph_list_data
end

function GuildWGData:GetIsDayGuildFuli()
	local is_flag =	self:GetDaycountIsDayReward()
	return is_flag == 0
end

function GuildWGData:GetIsCanJuanXian()
	-- local stuff = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].jianshe_item_id
	--背包里是否有某个物品
	-- return ItemWGData.Instance:GetHasItemInBag(stuff)
	--只有第一个档次需要做红点
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local coin = main_role_vo.coin or 0
	local contribute_cfg = self:GetGuildContributeCfg()
	for k,v in pairs(contribute_cfg) do
		if 1 == v.contribute_type and coin >= v.contribute_num then
			local cur_num = self:GetCurJxNum(v.contribute_type)
			return cur_num > 0
		end
	end
	return false
end

function GuildWGData:GetIsCanLingDiFuli()
	local my_guild_lv_rank = self:MyGuildLingDiRankLevel()
	-- 自己帮派领地等级为0的时候不提示remind
	if my_guild_lv_rank <= 0 then
		return false
	end

	local guildvo = GuildDataConst.GUILDVO
	local reward_flag = self:GetDaycountLingDiReward()
	local reward_data = self:GetGuildAllFuliRewardList(my_guild_lv_rank)
	local day_longhun = RoleWGData.Instance.role_vo.day_longhun
	if reward_data ~= nil then
		for k,v in pairs(reward_data) do
			if v.seq == 0 then
				if guildvo.tuanzhang_uid == RoleWGData.Instance:InCrossGetOriginUid() and day_longhun >= v.need_gongxian and reward_flag[1] == 0 then
					return true
				end
			else
				if day_longhun >= v.need_gongxian and reward_flag[v.seq + 1] == 0 then
					return true
				end
			end
		end
		return false
	else
		return false
	end
end

--帮派龙辉
function GuildWGData:IsShowGuildLongHuiRedPoint()
	if self:GetIsCanLongHun() then
		--GuildWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.GUILD_LOGNHUI,TabIndex.guild_longhui,1)
		return 1
	end

	GuildWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.GUILD_LOGNHUI,nil,0)
	return 0
end


function GuildWGData:GetIsCanLongHun()
	local longhui_level = self:GetXianshuLv()
	local levelconfig
	local longhun
	local guild_level
	local next_levelconfig
	for i=1,#longhui_level do
		local levelconfig = self:GetXianShuConfig(longhui_level[i],i-1)
		local longhun = RoleWGData.Instance.role_vo.longhun
		local next_levelconfig = self:GetXianShuConfig(longhui_level[i] + 1,i-1)

		if nil == levelconfig then
			return false
		end

		if next_levelconfig then
			local flag
			if next_levelconfig.upgrade_need_item_id ~= 0 then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(next_levelconfig.upgrade_need_item_id)
				flag = has_num >= levelconfig.upgrade_need_item_num
			else
				flag = true
			end
			if levelconfig.upgrade_need_gongxian <= longhun and flag then
				return true
			end
		end
	end

	return false
end

function GuildWGData:GetLongHunRedList()
	local longhui_level = self:GetXianshuLv()
	local levelconfig
	local longhun
	local guild_level
	local next_levelconfig
	local red_list = {0,0,0,0}
	for i=1,#longhui_level do
		local levelconfig = self:GetXianShuConfig(longhui_level[i],i-1)
		local longhun = RoleWGData.Instance.role_vo.longhun
		local next_levelconfig = self:GetXianShuConfig(longhui_level[i] + 1,i-1)

		if next_levelconfig then
			local flag
			if next_levelconfig.upgrade_need_item_id ~= 0 then
				local has_num = ItemWGData.Instance:GetItemNumInBagById(next_levelconfig.upgrade_need_item_id)
				flag = has_num >= levelconfig.upgrade_need_item_num
			else
				flag = true
			end
			if levelconfig.upgrade_need_gongxian <= longhun and flag then
				red_list[i] = 1
			end
		end
	end

	return red_list
end


function GuildWGData:GetIsHasGuildSetting()
	local guildvo = GuildDataConst.GUILDVO
	local applyfor_list = GuildDataConst.GUILD_APPLYFOR_LIST

	local guild_post = GameVoManager.Instance:GetMainRoleVo().guild_post
	if guild_post == GUILD_POST.FU_TUANGZHANG or guild_post ==  GUILD_POST.TUANGZHANG then
		return applyfor_list.count > 0
	end
	return false
end

function GuildWGData:GetIsHasGuildBattle()
	local guild_rank_list = self:GetGuildGongChengInfo() or {}
	local data_list = {}
	local daty_counter = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_GCZ_DAILY_REWARD_TIMES)

	for i, v in ipairs(guild_rank_list) do
		if v.guild_id == RoleWGData.Instance.role_vo.guild_id then
			if daty_counter < 1 then
				return true
			end
		end
	end
	return false
end

function GuildWGData:GetIsHasBossReward()
	local boss_cfg = ConfigManager.Instance:GetAutoConfig("guild_active_auto").guild_boss

	for i, v in ipairs(boss_cfg) do
		local boss_info = self:GetBossListByIndex(v.key)
  		if boss_info and boss_info.killer_roleid > 0 then
  			local is_befetched = self:GetBossRewardListByIndex(v.key)
		 	if is_befetched == 0 then
		  		return true
		  	end
  		end
	end
	return false
end

function GuildWGData:GetFuLiRemindNum()
	if self:GetIsDayGuildFuli() then
		return 1
	end

	if self:GetIsCanJuanXian() then
		return 1
	end

	if self:GetIsCanLingDiFuli() then
		return 1
	end

	if self:GetIsHasGuildSetting() then
		return 1
	end

	return 0
end

function GuildWGData:GetLongHuiRemindNum()
	if self:GetIsCanLongHun() then
		return 1
	end

	return 0
end

function GuildWGData:GetJuanXianRemindNum()
	if self:GetIsCanJuanXian() then
		return 1
	end

	return 0
end

function GuildWGData:GetLingDiFuLiRemindNum()
	if self:GetIsCanLingDiFuli() then
		return 1
	end

	return 0
end

function GuildWGData:GetBattleRemindNum()
	if self:GetIsHasGuildBattle() then
		return 1
	end

	return 0
end

function GuildWGData:GetBossRewardNum()
	if self:GetIsHasBossReward() then
		return 1
	end

	return 0
end

function GuildWGData:MyGuildLingDiRankLevel()
	self.guild_rank_list = self:GetGuildGongChengInfo() or {}
	local data_list = {}

	for i, v in ipairs(self.guild_rank_list) do
		if v.guild_id == RoleWGData.Instance.role_vo.guild_id then
			return v.rank
		end
	end
	return 0
end

-- 帮派运势祝福次数
function GuildWGData:SetGuildZhuFuNum(num)
	self.guild_zhufu_num = num
end

function GuildWGData:GetGuildZhuFuNum()
	return self.guild_zhufu_num
end

function GuildWGData:GetMyYunShiInfo()
	return self.my_yunshi_info
end

function GuildWGData:SetYunShiTimesTimeCountdown(time_type, btn_type, time)
	if nil == self.guild_yunshi_times[time_type] then
		self.guild_yunshi_times[time_type] = {}
	end
	self.guild_yunshi_times[time_type] = {btn_type = btn_type, time = time}
end

function GuildWGData:GetYunShiTimesTimeCountdown(time_type)
	return self.guild_yunshi_times[time_type]
end


function GuildWGData:GetYunShiRemindNum()
	local my_id = RoleWGData.Instance:InCrossGetOriginUid()
	local list = self.lucky_info.member_luckyinfo_list or {}
	local count = 0
	for k,v in pairs(list) do
		if v.uid ~= my_id and v.is_online == 1 then
			count = count + 1
		end
	end
	for k,v in pairs(list) do
		if v.uid == my_id and v.lucky_color < 5 and count > 0 then
			return 1
		end
	end
	return 0
end


----------仙盟合并-------------
function GuildWGData:SetGuildCombineReq(protocol)
	self.target_guild_id = protocol.target_guild_id
	self.target_guild_name = protocol.target_guild_name or ""
end

function GuildWGData:GetGuildCombineReq()
	return self.target_guild_id, self.target_guild_name
end

----------------红包----------------
function GuildWGData:SetRedPocketListInfo(protocol)
	local old_red_list = self:GetRedPocketListInfoIsOut()
	self.red_pocket_list = protocol.red_pocket_list
	self:GetRedDistributeTime(protocol.distribute_times)
	local new_red_list = self:GetRedPocketListInfoIsOut()
	local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	-- for k,v in ipairs(self:GetRedPocketListInfoPrune()) do
	-- 	if (v.status == GUILD_RED_POCKET_STATUS.DISTRIBUTED and v.is_fetch == 0) or
	-- 		(v.status == GUILD_RED_POCKET_STATUS.UN_DISTRIBUTED and role_id == v.owner_role_id)
	-- 		and not (IS_AUDIT_VERSION or IS_FREE_VERSION) then
	-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REDPOCKET, 1, function()
	-- 			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, "guild_redpacket") end)
	-- 		break
	-- 	else
	-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REDPOCKET, 0)
	-- 	end
	-- end
	-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REDPOCKET,0)

	for k,v in ipairs(self:GetRedPocketListInfoPrune()) do
		if (v.status == GUILD_RED_POCKET_STATUS.DISTRIBUTED and v.is_fetch == 0) then
			-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.REDPOCKET,1,BindTool.Bind1(self.OpenRedView, self))
			MainuiWGCtrl.Instance:ShowRedBtn()
			break
		end
	end

	local str_format = Language.Guild.GuildRedPacket
	local redpocket_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").red_pocket
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if next(old_red_list) then
		for i=1,#new_red_list do
			if old_red_list[i] and old_red_list[i].status == 1 and new_red_list[i].status == 2 and new_red_list[i].owner_role_id == role_id then
				for k,v in pairs(redpocket_cfg) do
					if v.type == new_red_list[i].type and v.level == new_red_list[i].level then
						str_format = v.Chanel_descript
					end
				end
				local content = str_format .. "{openLink;119}"
				ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, content, CHAT_CONTENT_TYPE.TEXT,nil,nil,true)
			end
		end
	end
end
function GuildWGData:GetRedDistributeTime(distribute_times)
	self.distribute_times = distribute_times
end

function GuildWGData:OpenRedView()
	-- ViewManager.Instance:Open(GuideModuleName.Guild, TabIndex.guild_redpacket)
	FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, "guild_redpacket")
end

function GuildWGData:GetRedDistributeTimes()
	return self.distribute_times
end

function GuildWGData:GetRedPocketListInfoPrune()
	local red_pocket_list = __TableCopy(self.red_pocket_list)
	local min_time = TimeWGCtrl.Instance:GetServerTime() - 2 * 24 * 60 * 60

	if next(red_pocket_list) then
		table.insert(red_pocket_list, 1, red_pocket_list[0])
		red_pocket_list[0] = nil
		for i = #red_pocket_list ,1 , -1 do
			if min_time >= red_pocket_list[i].create_timestamp then
				table.remove(red_pocket_list, i)
			end
		end
	end

	local red_pocket_list_prune = {}
	local index = 1
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	for k,v in ipairs(red_pocket_list) do
		if v.owner_role_id == role_id and v.is_fetch == 0 and v.status == 1 then
			v.flag = 1
		elseif v.owner_role_id == role_id and v.is_fetch == 0 and v.status == 2 then
			v.flag = 2
		elseif v.owner_role_id ~= role_id and v.is_fetch == 0 and v.status == 2 then
			v.flag = 3
		elseif v.owner_role_id ~= role_id and v.is_fetch == 0 and v.status == 1 then
			v.flag = 4
		elseif v.is_fetch == 1 and v.status == 2 then
			v.flag = 5
		elseif v.status == 3 then
			v.flag = 7
		else
			v.flag = 6
		end
		red_pocket_list_prune[index] = v
		index = index + 1
	end
	table.sort(red_pocket_list_prune, GuildWGData.CommonSorters("flag","create_timestamp"))
	return red_pocket_list_prune
end

--获得没有过期的红包
function GuildWGData:GetRedPocketListInfoIsOut()
	local red_pocket_list = __TableCopy(self.red_pocket_list)
	local min_time = TimeWGCtrl.Instance:GetServerTime() - 2 * 24 * 60 * 60

	if next(red_pocket_list) then
		table.insert(red_pocket_list, 1, red_pocket_list[0])
		red_pocket_list[0] = nil
		for i = #red_pocket_list ,1 , -1 do
			if min_time >= red_pocket_list[i].create_timestamp then
				table.remove(red_pocket_list, i)
			end
		end
	end
	return red_pocket_list
end

function GuildWGData:GetRedPocketListInfo()
	local red_pocket_list = {}
	local index = 1
	for k,v in pairs(self.red_pocket_list) do
		red_pocket_list[index] = v
		index = index + 1
	end
	return red_pocket_list
end

function GuildWGData:GetRedPocketListDesc(red_type, level)
	local red_pocket_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").red_pocket
	for k,v in pairs(red_pocket_cfg) do
		if v.type == red_type and v.level == level then
			return v
		end
	end
end

function GuildWGData:SetSaveRedPocketInfo(data)
	self.save_list = data
end

function GuildWGData:GetSaveRedPocketInfo()
	return self.save_list or {}
end

function GuildWGData:GetRedPocketInfo(id)
	local red_pocket_info = self:GetRedPocketListInfo()
	for k,v in pairs(red_pocket_info) do
		if id == v.id then
			return v
		end
	end
end

function GuildWGData:GetRedPocketZuiJia()
	local max_num = 0
	local max_index = 0
	local info = self:GetSaveRedPocketInfo()
	local red_pocket_info = self:GetRedPocketInfo(info.id)
	local fetch_info_list = self:GetRedPocketDistributeInfo()
	for k,v in pairs(fetch_info_list) do
		if v.gold_num >= max_num then
			max_num = v.gold_num
			max_index = k
		end
	end
	return max_index
end

-- 红包领取详细列表
function GuildWGData:SetRedPocketDistributeInfo(protocol)
	self.red_pocket_distribute = protocol.fetch_info_list
end

function GuildWGData:GetRedPocketDistributeInfo()
	local fetch_info_list = {}
	local index = 1
	for k,v in pairs(self.red_pocket_distribute) do
		fetch_info_list[index] = v
		index = index + 1
	end
	return fetch_info_list
end

function GuildWGData:GetOwnRedPocket()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local fetch_info_list = self:GetRedPocketDistributeInfo()
	for k,v in pairs(fetch_info_list) do
		if role_id == v.role_id then
			return v
		end
	end
end

-- 红包排序
function GuildWGData.CommonSorters(sort_key_name1, sort_key_name4)
	return function(a, b)
		local order_a = 10000
		local order_b = 10000
		if a[sort_key_name1] < b[sort_key_name1] then
			order_a = order_a + 10000
		elseif a[sort_key_name1] > b[sort_key_name1] then
			order_b = order_b + 10000
		end

		-- if nil == sort_key_name2 then return order_a < order_b end

		-- if a[sort_key_name2] + b[sort_key_name2] ~= 3 then
		-- 	if a[sort_key_name2] < b[sort_key_name2] then
		-- 		order_a = order_a + 1000
		-- 	elseif a[sort_key_name2] > b[sort_key_name2] then
		-- 		order_b = order_b + 1000
		-- 	end
		-- else
		-- 	if a[sort_key_name2] > b[sort_key_name2] then
		-- 		order_a = order_a + 1000
		-- 	elseif a[sort_key_name2] < b[sort_key_name2] then
		-- 		order_b = order_b + 1000
		-- 	end
		-- end

		-- if nil == sort_key_name3 then return order_a < order_b end

		-- if a[sort_key_name3] < b[sort_key_name3] then
		-- 	order_a = order_a + 100
		-- elseif a[sort_key_name3] < b[sort_key_name3] then
		-- 	order_b = order_b + 100
		-- end

		if nil == sort_key_name4 then return order_a < order_b end

		if a[sort_key_name4] > b[sort_key_name4] then
			order_a = order_a + 100
		elseif a[sort_key_name4] < b[sort_key_name4] then
			order_b = order_b + 100
		end

		return order_a > order_b
	end
end

function GuildWGData:GetRedPacketRemindNum()
	if next(self:GetRedPocketListInfoPrune()) then
		local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
		for k,v in ipairs(self:GetRedPocketListInfoPrune()) do
			if (v.status == GUILD_RED_POCKET_STATUS.DISTRIBUTED and v.is_fetch == 0) or
				(v.status == GUILD_RED_POCKET_STATUS.UN_DISTRIBUTED and role_id == v.owner_role_id) then
				return 1
			end
		end
	end
	return 0
end

function GuildWGData:UseRedItemOpenRedPacket(item_id)
	if self.red_item_list == nil then
		self.red_item_list = {}
		local red_item_id = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].red_item_id
		local red_item_list = Split(red_item_id, "|")
		for k,v in ipairs(red_item_list)do
			self.red_item_list[#self.red_item_list + 1] = tonumber(v)
		end
	end
	for k,v in ipairs(self.red_item_list)do
		if v == item_id then
			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, "guild_redpacket")
		end
	end
end

function GuildWGData:OnGuildBattleRanklist(protocol)
	self.rank_list = protocol.xmz_rank_list
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)--活动争霸
	RemindManager.Instance:Fire(RemindName.Guild)--仙盟主界面
	GuildWGCtrl.Instance:GuildViewFlush(TabIndex.guild_War)
end

function GuildWGData:GetGuildBattleRanklist()
	return self.rank_list
end

function GuildWGData:GetDuiZhanList()
	local battle_list = {}
	local xmz_match_info_list = self.xmz_match_info_list
	local length = #self.xmz_match_info_list
	for i=1,2 do
		battle_list[i] = {}
		local index = 1
		if length >= i then
			local match_info_list = self.xmz_match_info_list[i]
			for k,v in pairs(match_info_list) do
				local match_info = v.match_info
				for j=1,2 do
					local guild_info = {}
					guild_info.guild_id = match_info[j].guild_id
					guild_info.guild_name = match_info[j].guild_name
					battle_list[i][index] = guild_info
					index = index + 1
				end
			end
		else
			for j=1,2 do
				local guild_info = {}
				guild_info.guild_id = 0
				guild_info.guild_name = ""
				battle_list[i][j] = guild_info
				index = index + 1
			end
		end
	end
end

--帮派解散特殊处理
function GuildWGData:GetGuildDissolution(guild_id)
	if self.rank_list then
		--print_error()
		for k,data_list in ipairs(self.rank_list) do
			for k,v in ipairs(data_list) do
				if guild_id == v.guild_id then
					return true
				end
			end
		end
	end
	return false
end

--帮派解散特殊处理
function GuildWGData:GetCurZoneGuildData(guild_id)
	if self.rank_list then
		for k,data_list in ipairs(self.rank_list) do
			for i,v in ipairs(data_list) do
				if guild_id == v.guild_id then
					return k,data_list
				end
			end
		end
	end
end

function GuildWGData:GetGuildIsCanCanJiaZhengBa()
	local is_guild_war = GuildWGData.Instance:GetMyCanJoinGuildWar()
	if not is_guild_war then
		return false
	end

	local my_guild_opponent_guild = GuildWGData.Instance:GetKfMyGuildPpponentGuild()
	if my_guild_opponent_guild and my_guild_opponent_guild.guild_id <= 0 then
		return false
	end

	local m_curr_match_end_time = GuildWGData.Instance:GetKfXmzCurrMatchEndTime()
	local act_state = GuildWGData.Instance:GetCurGuildWarState()

	if act_state == GuildWGData.GuildWarState.OneState or act_state == GuildWGData.GuildWarState.TwoState then
		return true
	else
		return false	
	end

end

function GuildWGData:CanJoinGuildWar()
	local list_data = GuildWGData.Instance:GetGuildBattleMatchInfoList()
	local is_guild_war = false
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if guild_id <= 0 then
		return false
	end
	if list_data then
		for k,v in pairs(list_data) do
			for i=1,2 do
				if v[i].match_info[1].guild_id == guild_id or v[i].match_info[2].guild_id == guild_id then
					is_guild_war = true
				end
			end
		end
	end
	return is_guild_war
end

function GuildWGData:SetGuildBattleMatchInfo(protocol)
	self.guild_fight_state = protocol.guild_fight_state
	self.xmz_match_info_list = protocol.xmz_match_info_list
	RemindManager.Instance:Fire(RemindName.Guild_Activit_ZhengBa)--活动争霸
end

function GuildWGData:GetGuildBattleMatchInfoList()
	return self.xmz_match_info_list
end

function GuildWGData:GetGuildBattleFightState()
	return self.guild_fight_state or 0
end



---------------------------------------------------------------------
function GuildWGData:SetJiuSheDaoFBInfo(protocol)
	self.jiushedaofb_notify_reason = protocol.notify_reason
	self.jiushedaofb_is_pass = protocol.is_pass
	self.jiushedaofb_is_finish = protocol.is_finish
	self.jiushedaofb_boss_hp_per = protocol.boss_hp_per
	self.jiushedaofb_has_taken_box = protocol.has_taken_box
	self.jiushedaofb_has_passed_count = protocol.has_passed_count
	self.brith_pos = protocol.brith_pos
	self.live_pos = protocol.live_pos
end

function GuildWGData:GetJiuSheDaoFBNotifyReason()
	return self.jiushedaofb_notify_reason or 0
end

function GuildWGData:GetJiuSheDaoFBBossHpPer()
	return self.jiushedaofb_boss_hp_per or 0
end

function GuildWGData:GetJiuSheDaoFBHasTakenBox()
	return self.jiushedaofb_has_taken_box or 0
end

function GuildWGData:GetJiuSheDaoFBHasPassedCount()
	return self.jiushedaofb_has_passed_count or 0
end

function GuildWGData:GetJiuSheDaoFBIsPass()
	return self.jiushedaofb_is_pass or 0
end

function GuildWGData:GetJiuSheDaoFBIsFinish()
	return self.jiushedaofb_is_finish or 0
end

function GuildWGData:GetJiuSheDaoFBBoxNum()
	local num = 0
	local cfg = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").platform
	for k,v in pairs(cfg) do
		if v.has_box == 1 then
			num = num + 1
		end
	end
	return num
end

function GuildWGData:GetJiuSheDaoFBPlatformIndex()
	local fuben = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").fuben
	for k,v in pairs(fuben) do
		if v.pos_x == self.brith_pos.x and v.pos_y == self.brith_pos.y then
			return -1
		end
	end
	local platform = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").platform
	for i,v in pairs(platform) do
		if v.relive_x == self.brith_pos.x and v.relive_y == self.brith_pos.y then
			return v.platform
		end
	end
end

function GuildWGData:GetJiuSheDaoFBReward()
	local fuben = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").fuben
	local box_reward = {}
	local boss_reward = {}
	for i=0,2 do
		if fuben[1].reward_item[i] then
			table.insert(box_reward, fuben[1].reward_item[i])
		end
		if fuben[1].bossdrop_item[i] then
			table.insert(boss_reward, fuben[1].bossdrop_item[i])
		end
	end
	return box_reward, boss_reward
end

function GuildWGData:GetJiuSheDaoFBIsPlatformArea(x, y)
	local cfg = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").safe_arer
	for i,v in pairs(cfg) do
		local target_distance = math.floor(GameMath.GetDistance(x, y, v.x, v.y, true))
		if target_distance <= v.radius then
			GuildWGCtrl.Instance:SendJiuSheDaoFBHandler(JIUSHEDAOFB_REQUESET_REASON.REQUESET_REASON_BEEN_PLATFORM, i - 1)
		end
	end
end

function GuildWGData:GetIsInJiuSheDaoFBIsPlatformArea(x,y)
	local cfg = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").safe_arer
	for i,v in pairs(cfg) do
		local target_distance = math.floor(GameMath.GetDistance(x, y, v.x, v.y, true))
		if target_distance <= v.radius then
			return true,i
		end
	end
	return false, 0
end

function GuildWGData:GetCenterPointByIndex(index)
	local cfg = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").safe_arer
	return cfg[index]
end

function GuildWGData:GetJiuSheDaoFBJumpArea(x, y)
	local cfg = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").area
	for i,v in pairs(cfg) do
		local target_distance = math.floor(GameMath.GetDistance(x, y, v.point_x, v.point_y, true))
		if target_distance <= 3 then
			return true,v.point_x,v.point_y
		end
	end
	return false,0,0
end

function GuildWGData:GetJiuSheDaoFBBoomArea()
	local cfg = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").area
	local boom_list = __TableCopy(cfg)
	for k1,v1 in ipairs(self.live_pos) do
		for k2,v2 in ipairs(boom_list) do
			if v1.x == v2.point_x and v1.y == v2.point_y then
				table.remove(boom_list, k2)
			end
		end
	end
	return boom_list
end

function GuildWGData:GetJiuSheDaoFBIsStandingSafeArea(x, y)
	for i,v in pairs(self.live_pos) do
		local target_distance = math.floor(GameMath.GetDistance(x, y, v.x, v.y, true))
		local cfg = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").other
		if target_distance <= cfg[1].jump_platform_radius then
			return true,v.x,v.y
		end
	end
	return false
end

function GuildWGData:GetJiuSheDaoFBIsStandingBoom(x, y)
	local error_pos_list = self:GetJiuSheDaoFBBoomArea()
	for i,v in pairs(error_pos_list) do
		local target_distance = math.floor(GameMath.GetDistance(x, y, v.point_x, v.point_y, true))
		local cfg = ConfigManager.Instance:GetAutoConfig("jiushedao_cfg_auto").other
		-- print_error("GetJiuSheDaoFBIsStandingBoom",x,y,v.point_x,v.point_y,target_distance)
		if target_distance <= cfg[1].jump_platform_radius then
			return true
		end
	end
	return false
end

function GuildWGData:GetJiuSheDaoFBRoleState()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	if self:GetJiuSheDaoFBIsStandingBoom(x, y) then --如果在跳落点范围外
		--print_error("GetJiuSheDaoFBRoleState in error pos,跳到错误点 ")
		GuildWGCtrl.Instance:SendJiuSheDaoFBHandler(JIUSHEDAOFB_REQUESET_REASON.REQUESET_REASON_DEAD)
		FuhuoWGCtrl.Instance:SetKillerName(Language.Common.Trap)
		FuhuoWGCtrl.Instance:SetKillerLevel("")
	elseif self:GetJiuSheDaoFBIsStandingSafeArea(x, y) then --如果在安全范围
		GuildWGCtrl.Instance:SendJiuSheDaoFBHandler(JIUSHEDAOFB_REQUESET_REASON.REQUESET_REASON_BEEN_SPRINGBOARD, nil, x, y)
	end
	if not self:GetJiuSheDaoFBIsStandingSafeArea(x, y) and not self:GetJiuSheDaoFBIsStandingBoom(x, y) then
		self:GetJiuSheDaoFBIsPlatformArea(x, y)
	end
end

function GuildWGData:GetMainRoleGuildCapability()
	local guild_list = self:GetGuildListData()
	
	for k,v in pairs(guild_list) do
		if GuildDataConst.GUILDVO.guild_id == v.guild_id then
			return v.guild_all_capability
		end
	end

	return 0
end

function GuildWGData:SetGuildListData(protocol)
	self.guild_list = protocol
	local guildvo = GuildDataConst.GUILDVO
	for k,v in pairs(self.guild_list) do
		if v.guild_id == guildvo.guild_id then
			self:SetCurGuildZone(v.zone)
		end
	end

	--print_error(">>>>>>>>>>self.guild_list",self.guild_list)
end

function GuildWGData:GetGuildListData()
	return self.guild_list or {}
end

function GuildWGData:SetGuildListSelectIndex(index)
	self.select_index = index
end

function GuildWGData:GetGuildListSelectIndex()
	return self.select_index
end

function GuildWGData:SetNoGuildListSelectIndex(index)
	self.select_index = index
end

function GuildWGData:GetNoGuildListSelectIndex()
	return self.select_index
end

function GuildWGData:SetGuildMemberSelectIndex(index)
	self.member_select_index = index
end

function GuildWGData:GetGuildMemberSelectIndex()
	return self.member_select_index
end

------------------------------猎鲲---------------------------------------------------
function GuildWGData:SetCrossLieKunHurtInfo(protocol)
	if not self.cross_liekun_hurt_info then
		self.cross_liekun_hurt_info = {}
	end
	if not self.cross_liekun_hurt_info[protocol.boss_id] then
		self.cross_liekun_hurt_info[protocol.boss_id] = {}
	end
	self.cross_liekun_hurt_info[protocol.boss_id].hurt_list = protocol.hurt_list
	self.cross_liekun_hurt_info[protocol.boss_id].guild_hurt_list = protocol.guild_hurt_list
end

function GuildWGData:SetCrossLieKunScoreInfo(protocol)
	self.cross_liekun_score = protocol.score
end

function GuildWGData:GetCrossLieKunScore()
	return self.cross_liekun_score or 0
end

function GuildWGData:GetCrossLieKunScoreInfo()
	local cfg_list = ConfigManager.Instance:GetAutoConfig("cross_liekun_auto").score_reward
	local score = self:GetCrossLieKunScore()
	local cur_info
	local next_info
	local is_max
	if score >= cfg_list[#cfg_list].score then
		is_max = true
		cur_info = cfg_list[#cfg_list]
		return cur_info , next_info,is_max
	end
	for i,v in ipairs(cfg_list) do
		if score < cfg_list[1].score then
			next_info = v
			return cur_info , next_info
		end
		if score <= v.score then
			cur_info = v
			next_info = cfg_list[i + 1]
			return cur_info , next_info,is_max
		end
	end
end

function GuildWGData:GetCrossLieKunHurtInfo(boss_id)
	return self.cross_liekun_hurt_info and self.cross_liekun_hurt_info[boss_id]
end

function GuildWGData:SetCrossLieKunFBSceneInfo(protocol)
	self.cross_lieKun_info.zone = protocol.zone
	self.cross_lieKun_info.is_main_live_flag = protocol.is_main_live_flag
	self.cross_lieKun_info.boss_id = protocol.boss_id
	self.cross_lieKun_info.guild_id = protocol.guild_id
	self.cross_lieKun_info.guild_name = protocol.guild_name
	self.cross_lieKun_info.boss_next_flush_timestamp = protocol.boss_next_flush_timestamp
	self.cross_lieKun_info.boss_extra_level = protocol.boss_extra_level
end

function GuildWGData:GetCrossLieKunDoorActive()
	if self.cross_lieKun_info then
		if self.cross_lieKun_info.is_main_live_flag == 0 and self.cross_lieKun_info.boss_next_flush_timestamp[1] == 0 then
			return true
		end
	end
	return false
end

function GuildWGData:GetCrossLieKunFBSceneInfo()
	return self.cross_lieKun_info
end

function GuildWGData:SetCrossLieKunFBPlayerInfo(protocol)
	self.liekunfb_player_info.is_enter_main_zone = protocol.is_enter_main_zone
	self.liekunfb_player_info.role_num = protocol.role_num
	self.liekunfb_player_info.guild_role_num = protocol.guild_role_num
	self.liekunfb_player_info.boss_extra_level = protocol.boss_extra_level
	-- print_error('self.liekunfb_player_info',self.liekunfb_player_info)
end

function GuildWGData:GetCrossLieKunFBBestSelect()
	local count = 1
	local index = 1
	-- 是否四个区域本帮人数相同
	local num = self.liekunfb_player_info.guild_role_num[2]
	for i,v in ipairs(self.liekunfb_player_info.guild_role_num) do
		if i ~= 1 and num ~= v then
			count = count + 1
		end
	end
	-- 本帮人数相同
	if count == 1 then
		local data = self.liekunfb_player_info.role_num[2]
		for i,v in ipairs(self.liekunfb_player_info.role_num) do
			if i ~= 1 and data > v then
				data = v
				index = i - 1
			end
		end
	else
		local data = 0
		for i,v in ipairs(self.liekunfb_player_info.guild_role_num) do
			if i ~= 1 and data < v then
				data = v
				index = i - 1
			end
		end
	end

	return index
end

function GuildWGData:GetCrossLieKunFBPlayerInfo()
	return self.liekunfb_player_info
end

function GuildWGData:GetLieKunMonsterAttrCfg()
	if self.liekunfb_player_info and self.liekunfb_player_info.boss_extra_level then
		local level = self.liekunfb_player_info.boss_extra_level
		local cfg_list = ConfigManager.Instance:GetAutoConfig("cross_liekun_auto").monster_attribute
		for i,v in ipairs(cfg_list) do
			if v.level == level then
				return v.attribute
			end
		end
	end
	return 0
end

function GuildWGData:GetLieKunSceneId(zone)
	if self.cross_liekun_auto then
		for i,v in ipairs(self.cross_liekun_auto.zone) do
			if v.zone == zone then
				return v.scene_id
			end
		end
	end
end

function GuildWGData:GetLieKunZoneBySceneId(scene_id)
	if self.cross_liekun_auto then
		for i,v in ipairs(self.cross_liekun_auto.zone) do
			if v.scene_id == scene_id then
				return v.zone
			end
		end
	end
end

function GuildWGData:GetIsCanJoinLiekun(scene_id)
	local zone = self:GetLieKunZoneBySceneId(scene_id)
	if not zone then return false end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_LIEKUN)
	local is_open = activity_info and (activity_info.status == ACTIVITY_STATUS.OPEN or activity_info.status == ACTIVITY_STATUS.STANDY) or false
	if not is_open then return false end

	-- local end_time = activity_info.next_time
	-- local server_time = TimeWGCtrl.Instance:GetServerTime()

	-- -- 16分钟关闭入口的时间
	-- local lioekun_other = ConfigManager.Instance:GetAutoConfig("cross_liekun_auto").other[1]
	-- local close_time = end_time - lioekun_other.total_time + lioekun_other.enter_time_limit_s
	-- if zone > 0 and server_time >= close_time then  --超过16分钟  其他活动入口关闭
	-- 	return false,Language.Guild.LieKunTips_3
	-- end

	return true,zone
end

function GuildWGData:GetLieKunSmallMonsterListCfg()
	local data_list = {}
	if self.cross_liekun_auto then
		for k,v in ipairs(self.cross_liekun_auto.smallmonster_info) do
			if v.zone == self.cross_lieKun_info.zone then
				table.insert(data_list,v)
			end
		end
	end
	return data_list
end

function GuildWGData:GetLieKunBossListCfg()
	return self.cross_liekun_auto.cross_boss_information
end

function GuildWGData:GetLieKunBossRankReward(boss_id)
	local item_id
	if self.cross_liekun_auto then
		for i,v in ipairs(self.cross_liekun_auto.ranking_award) do
			if v.boss_id == boss_id then
				local t = v.reward_item[0]
				item_id = t.item_id
			end
		end
	end
	return item_id
end

function GuildWGData:GetLieKunBossListMapCfg(monsters_list)
	local data_list = self:GetLieKunBossListCfg()
	if data_list and self.cross_lieKun_info then
		local zone = self.cross_lieKun_info.zone
		for i = #data_list,1,-1 do
			if zone == data_list[i].zone then
				local list = {}
				list.x = data_list[i].x_pos
				list.y = data_list[i].y_pos
				list.id = data_list[i].boss_id
				list.name = data_list[i].name
				table.insert(monsters_list,1,list)
			end
		end
	end
end

function GuildWGData:GetLieKunBossListCfgByZone()
	local data_list = self:GetLieKunBossListCfg()
	local data = {}
	if data_list and self.cross_lieKun_info then
		local zone = self.cross_lieKun_info.zone
		for k,v in pairs(data_list) do
			if zone == v.zone then
				table.insert(data,v)
			end
		end
		return data
	end
end

function GuildWGData:GetOneBosscfgByZone(zone)
	for k,v in pairs(self.cross_liekun_auto.zone) do
		if zone == v.zone then
			return v
		end
	end
	return nil
end



function GuildWGData:IsLieKunPortal(gather_id)    -- 是否是猎鲲传送门
	local portal_id = self.cross_liekun_auto.other[1].portal_id
	return gather_id == portal_id
end

function GuildWGData:SetLieKunObjId(obj_id)
	self.liekun_obj_id = obj_id
end

function GuildWGData:GetLieKunObjId()
	return self.liekun_obj_id or COMMON_CONSTS.INVALID_OBJID
end

function GuildWGData:SetLieKunGatherInfo(info)
	self.liekun_gather_info = info
	if not self.old_liekun_gather_info then
		self.old_liekun_gather_info = __TableCopy(self.liekun_gather_info)
	end
end

function GuildWGData:GetLieKunGatherInfo()
	return self.liekun_gather_info
end

function GuildWGData:GetLieKunGatherGuaJiType()
	for k,v in pairs(self.liekun_gather_info) do
		for k1,v1 in pairs(self.old_liekun_gather_info) do
			if v1.obj_id == v.obj_id and v.times ~= v1.times then
				self.old_liekun_gather_info = __TableCopy(self.liekun_gather_info)
				return true
			end
		end
	end
	-- print_error('self.liekun_gather_info,self.old_liekun_gather_info',self.liekun_gather_info,self.old_liekun_gather_info)
	return false
end


--获取先仙盟争霸结束信息
function GuildWGData:GetGuildEndData(rank_list,is_win,zone)
	self.battle_rank_list = rank_list
	self.is_win = is_win
	self.cur_zone = zone
end

function GuildWGData:GetGuildEndDataInfo()
	return self.battle_rank_list , self.is_win
end

function GuildWGData:GetGuildCurZone()
	return self.cur_zone
end


--上线判断是否在仙盟当中
function GuildWGData:IsHaveGuild()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_role_vo.guild_id == 0 then
		return 1
	end
	return 0
end

-- 是否有仙盟
function GuildWGData:HasGuild()
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	return main_role_vo.guild_id > 0
end

--仙盟是否可以捐献
function GuildWGData:IsCanJuanXian()
	-- if self:GetIsCanJuanXian() and self:IsCanDhowJuXianRes() then
	-- 	return 1
	-- end
	if self:GetIsCanJuanXian() then
		return 1
	end
	return 0
end

--仙盟是否有申请列表
function GuildWGData:IsHaveApplay()
	if self:GetIsHasGuildSetting() then
		return 1
	end
	return 0
end


--综览概况
function GuildWGData:IsShowGuildZongLanGKRedPoint()
	if self:IsHaveGuild() == ShowRedPoint.SHOW_RED_POINT then return 0 end
	-- if self:GetIsDayGuildFuli() then
	-- 	GuildWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.GUILD_ALL,TabIndex.guild_info,1)
	-- 	return 1
	-- end
	if ShowRedPoint.SHOW_RED_POINT == self:IsCanJuanXian() then
		--GuildWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.GUILD_ALL,TabIndex.guild_info,1)
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsHaveApplay() then
		--GuildWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.GUILD_ALL,TabIndex.guild_info,1)
		return 1
	end
	GuildWGCtrl.Instance:CreatMainUiGuildBtn(MAINUI_TIP_TYPE.GUILD_ALL,nil,0)
	return 0
end

--综览红包红点
function GuildWGData:IsShowGuildRedBagRedPoint()
	local guild_red = self:IsShowGuildZongLanRedBagRedPoint()
	if guild_red > 0 then
		return 1
	end
	-- local world_red = WelfareWGData.Instance:IsShowWelfarePaperRedPoint()
	-- if world_red > 0 then
	-- 	return 1
	-- end

	return 0
end


--仙盟红包
function GuildWGData:IsShowGuildZongLanRedBagRedPoint()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if guild_id <= 0 then
		return 0
	end
	local data_base_cfg
	--local red_cfg = ConfigManager.Instance:GetAutoConfig("redpaper_auto")
	local person_info = WelfareWGData.Instance:GetPersonRedpaperInfo()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if IsEmptyTable(person_info) then
		return 0
	end
	local have_count = false
	local data_base_cfg_info = WelfareWGData.Instance:GetWelfareCfg().other_config[1]
	local role_vip = VipWGData.Instance:GetRoleVipLevel()

	local red_all_info = WelfareWGData.Instance:GetGuildRedpaperAllInfo()
	for m,n in pairs(red_all_info) do
		if n.paper_type == 0 then
			data_base_cfg = WelfareWGData.Instance:GetWelfareCustomRedpaperSeqCfg(n.paper_level)
		else
			data_base_cfg = WelfareWGData.Instance:GetWelfareGuildRedpaperTypeCfg(n.paper_type,n.paper_level)
		end
		local is_get_over = false
		local have_get_count = #n.record_list

		for k,v in pairs(n.record_list) do
			if v.uid == role_id then
				is_get_over = true
				break
			end
		end

		if data_base_cfg and data_base_cfg.bind_gold > 0 then
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.vip_gold_bind_max
			else
				have_count = person_info.day_receive_gold_count < data_base_cfg_info.gold_bind_max
			end
		else
			if role_vip >= data_base_cfg_info.double_fetch_vip then
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.vip_sliver_ticket_max
			else
				have_count = person_info.day_distribute_ticket_count < data_base_cfg_info.sliver_ticket_max
			end
		end

		if data_base_cfg and have_get_count < data_base_cfg.num and not is_get_over and have_count then
			return 1
		end
	end
	
	local data_lsit = WelfareWGData.Instance:GetSendRedPaperListInfo(true)
		for k,v in pairs(data_lsit) do
			if v.sort_index == 0 then
				return 1
			end
		end
	return 0
end

--仙盟活动
function GuildWGData:IsShowGuildActivityRedPoint()
	if self:IsHaveGuild() == ShowRedPoint.SHOW_RED_POINT then return 0 end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowGuildShiLianRedPoint() then
		return 1
	end
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowGuildDaTiRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowGuildBossRedBagRedPoint() then
		return 1
	end
	return 0

end

function GuildWGData:SetHideShiLianRemind(flag)
	self.hide_guild_shilian_remind = flag
end

--仙盟试炼
function GuildWGData:IsShowGuildShiLianRedPoint()
	if self.hide_guild_shilian_remind then
		return 0
	end
	if self:IsHaveGuild() == ShowRedPoint.SHOW_RED_POINT then return 0 end
	local role_level = RoleWGData.Instance.role_vo.level
	if self.guildfb_level > role_level then
		return 0
	end

	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_FB) and self:GetIsCanJoinGuildShouHu() == 1
		and not self.hide_guild_shilian_remind then
		local is_finish = self:IsFinishGuildFb()
		if is_finish then
			return 0
		end
		return 1
	end
	return 0
end

function GuildWGData:SetHideDaTiRemind(flag)
	self.hide_guild_dati_remind = flag
end

--仙盟答题
function GuildWGData:IsShowGuildDaTiRedPoint()--策划需求去掉红点/策划又要加上红点
	if self.hide_guild_dati_remind then
		return 0
	end
	if self:IsHaveGuild() == ShowRedPoint.SHOW_RED_POINT then return 0 end
	local role_level = RoleWGData.Instance.role_vo.level
	if self.guildanswer_level > role_level then
	 	return 0
	end

	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.GUILD_ANSWER) and self:GetIsCanJoin() == 1
		and not self.hide_guild_dati_remind then
	 	return 1
	end
	return 0
end

function GuildWGData:SetHideGuildBossRemind(flag)
	self.hide_guild_boss_remind = flag
end

function GuildWGData:GetHideGuildBossRemind()
    return self.hide_guild_boss_remind
end

--仙盟Boss
function GuildWGData:IsShowGuildBossRedBagRedPoint()
	if self.hide_guild_boss_remind then
		return 0
	end
	if self:IsHaveGuild() == ShowRedPoint.SHOW_RED_POINT then 
		return 0 
	end
	if GuildBossWGData.Instance:IsGuildBossOpen() and not self.hide_guild_boss_remind then
		return 1
	end
	return 0

end


function GuildWGData:SetHideGuildZhengBaRemind(flag)
	self.is_enter_zhengba_scene = flag
end

function GuildWGData:GetHideGuildZhengBaRemind()
	return self.is_enter_zhengba_scene
end

--仙盟争霸
function GuildWGData:IsShowGuildZhengBaRedPoint()
	if self:IsHaveGuild() == ShowRedPoint.SHOW_RED_POINT then return 0 end
	local role_level = RoleWGData.Instance.role_vo.level
	if self.xianmengzhan_level > role_level then
		return 0
	end
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN) then
 		local guild_dissolution = self:GetGuildIsCanCanJiaZhengBa()
 		if guild_dissolution and not self.is_enter_zhengba_scene then
 			return 1
 		end
	end

	-- local num = ZhuZaiShenDianWGData.Instance:GetRewardNum()
	-- local uid = ZhuZaiShenDianWGData.Instance:GetMengZhuUid()
	-- if num > 0 and uid > 0 then
	-- 	return 1
	-- end
	-- local fenpei_flag = GuildBattleRankedWGData.Instance:GetFenPeiRemind()
	-- if fenpei_flag == 1 then
	-- 	return 1
	-- end
	return 0
end

--仙盟猎坤
function GuildWGData:IsShowGuildLieKunRedPoint()
	if self:IsHaveGuild() == ShowRedPoint.SHOW_RED_POINT then return 0 end
	local role_level = RoleWGData.Instance.role_vo.level
	if self.liekun_level > role_level then
		return 0
	end
	local other = self.cross_liekun_auto.other[1]
	local enter_time_limit_s = other.enter_time_limit_s
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_LIEKUN)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if act_info and ACTIVITY_STATUS.OPEN == act_info.status and server_time < act_info.start_time + enter_time_limit_s then
		if self.delay_liekun_remind then
			GlobalTimerQuest:CancelQuest(self.delay_liekun_remind)
		end
		self.delay_liekun_remind = GlobalTimerQuest:AddDelayTimer(function ()
			RemindManager.Instance:Fire(RemindName.Guild_Activit_LieKun)
		end,(act_info.start_time + enter_time_limit_s) - server_time)
		return 1
	end
	return 0
end

--仙盟建设
function GuildWGData:IsShowGuildBuildRedPoint()
	if not self:GetBuildTaskIsAllFinish() then
		return 1
	end
	if self:GetGuildBuildCommitId() then
		return 1
	end
	return 0
end
--仙盟工资
function GuildWGData:IsShowGuildWagePoint()
	local task_info = self:GetGuildWageTaskInfo()
	if not IsEmptyTable(task_info) then
		for k,v in pairs(task_info) do
			if v.states == GUILD_WAGE_TASK_STATES.KELINGQU then
				return 1
			end

			--仙盟周任务特殊处理:未完成时红点提示
			local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
			--策划需求 修改成开服第一天玩家有可以完成的任务也不显示红点  仙盟任务的红点从开服第二天开始显示
			if GuildWGCtrl.Instance:GetWeekTaskClickFlag() == nil 	--本次登陆未点击仙盟福利界面
				and v.old_info.type == GUILD_WAGE_OTHER_TYPE.TASK_TYPE11 
				and v.states == GUILD_WAGE_TASK_STATES.WEIWANCHENG and open_day > 1 then
				return 1
			end	
			
		end
	end
	return 0
end

function GuildWGData:GetWeekTaskRemindState()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	--策划需求 修改成开服第一天玩家有可以完成的任务也不显示红点  仙盟任务的红点从开服第二天开始显示
	if open_day <= 1 then return 0 end
	local task_info = self:GetGuildWageTaskInfo()
	if not IsEmptyTable(task_info) then
		for k, v in pairs(task_info) do
			--仙盟周任务特殊处理:未完成时红点提示
			if GuildWGCtrl.Instance:GetWeekTaskClickFlag() == nil then	--本次登陆未点击:仙盟-福利界面
				if v.old_info.type == GUILD_WAGE_OTHER_TYPE.TASK_TYPE11 
					and v.states == GUILD_WAGE_TASK_STATES.WEIWANCHENG then
					return 1
				end
			else
				return 0
			end	
		end
	end
	return 0
end

function GuildWGData:SetIsCanJoin(is_can_join)
	self.is_can_join = is_can_join
end

function GuildWGData:GetIsCanJoin()
	return self.is_can_join
end

function GuildWGData:SetIsCanJoinGuildShouHu(is_can_join_shou_hu)
	self.is_can_join_shou_hu = is_can_join_shou_hu
end

function GuildWGData:GetIsCanJoinGuildShouHu()
	-- return self.is_can_join_shou_hu
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local activity_cfg = DailyWGData.Instance:GetActivityConfig(ACTIVITY_TYPE.GUILD_FB)
	if role_level >= activity_cfg.limit_level and role_level < activity_cfg.level_max then
		return 1
	end
	return 0
end

function GuildWGData:GetGuaJiFanWei()
	local other_cfg = self:GetGuildSHOtherCfg()
	return other_cfg.min_pos_x, other_cfg.max_pos_x, other_cfg.min_pos_y, other_cfg.max_pos_y
end

--设置帮派聊天接受者id
function GuildWGData:SetSendID(role_id)
	self.send_id = role_id
end

function GuildWGData:GetSendID()
	return self.send_id
end

--仙盟红包获取方式
function GuildWGData:GetRedPacketWay()
	return ConfigManager.Instance:GetAutoConfig("guildconfig_auto").red_pocket_tips
end

--仙盟红包是否刷新
function GuildWGData:SetGuildRedFlush(enable)
	self.is_flush_redbag_up = enable
end

function GuildWGData:GetGuildRedFlush()
	if self.is_flush_redbag_up == nil then
		self.is_flush_redbag_up = true
	end
	return self.is_flush_redbag_up
end
--获取仙盟最大等级
function GuildWGData:GetGuildMaxLevel()
	local other = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1]
	return other.guildmax_level
end

----------------------仙盟任务----------------------------------
function GuildWGData:SetGuildBuildTaskInfo(protocol)
	self.is_first_time_history = protocol.is_first_time_history --第一次（功能开启）
	-- self.is_first_time_today = 	protocol.is_first_time_today--第一次（今日）
	self.refresh_times = protocol.refresh_times --刷新次数
	self.finish_times = protocol.finish_times --任务完成次数
	self.has_commit_task_list = protocol.has_commit_task_list
	self:OpenGuildBuildView()
end

function GuildWGData:GetGuildTaskInfo()
	local can_accept_task = {}
	--是否存在s级别的任务且未完成
	self.is_have_s = false

	if self.has_commit_task_list and not IsEmptyTable(self.has_commit_task_list) then
		for k,v in pairs(self.has_commit_task_list) do
			local copy_v = {}
			local old_info = TaskWGData.Instance:GetTaskConfig(v.task_id)
			local other_info = self.guild_build_task_cfg[v.task_id]

			if old_info and other_info then
				local progress_num = TaskWGData.Instance:GetProgressNum(v.task_id)
				if v.task_states == GameEnum.TASK_STATUS_FINISH then
					progress_num = old_info.c_param2
				end

				--是否存在s级别的任务
				if not self.is_have_s and other_info and other_info.quality == GUILD_BUILD_TASK_RATE_TYPE.RATES and v.task_states ~= GameEnum.TASK_STATUS_FINISH then
					self.is_have_s = true
				end

				copy_v["task_states"] = v.task_states
				copy_v["progress_num"] = progress_num
				copy_v["old_info"] = old_info
				copy_v["other_info"] = other_info
				table.insert(can_accept_task,copy_v)
			end
		end
	end

	return can_accept_task
end

--是否存在s级任务
function GuildWGData:IsHaveSTask()
	return self.is_have_s
end

function GuildWGData:GetDayTaskMaxNum()
	local guild_build_other = self.guild_build_cfg.other[1]
	return guild_build_other.day_max_task_num or 0
end

--获取建设完成任务次数
function GuildWGData:GetBuildTaskFinishNum()
	return self.finish_times or 0
end

--获取当天建设任务是否都已完成
function GuildWGData:GetBuildTaskIsAllFinish()
	local day_max_task_num = self:GetDayTaskMaxNum()
	local finish_task_num = self:GetBuildTaskFinishNum()
	return day_max_task_num - finish_task_num <= 0 and true or false
end

--获取免费刷新次数
function GuildWGData:GetCanFlushTaskNum()
	local all_num = 0
	local vip_level_cfg = VipWGData.Instance:GetVipSpecPermissions(GUILD_BUILD_TASK_OTHER_TYPE.GUILD_TASK_VIP_TYPE)
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local free_num = self.guild_build_cfg.other[1].free_num
	local add_num_cfg = self.guild_build_cfg.vip_add_flush
	all_num = free_num + vip_level_cfg["param_"..vip_level]
	local refresh_times = self.refresh_times or 0
	all_num = all_num - refresh_times
	return all_num or 0
end

--获取刷新消耗
function GuildWGData:GetFlushTaskNeedGold()
	local need_gold = 0
	local gold_xiaohao = self.guild_build_cfg.gold_xiaohao
	local all_num = self:GetCanFlushTaskNum()
	local gold_flush_num = all_num < 0 and -all_num + 1 or all_num + 1
	if gold_xiaohao then
		for k,v in pairs(gold_xiaohao) do
			if gold_flush_num >= v.time_minimum and gold_flush_num <= v.time_maximum then
				need_gold = v.need_gold
			end
		end

	end
	return need_gold
end

--获取建设任务奖励
function GuildWGData:GetGuildBuildTaskReward(task_id)
	local reward_list = {}
	if nil == task_id then
		return reward_list
	end

	local get_exp = 0
	local cfg = self.guild_build_task_cfg[task_id]
	local role_level = RoleWGData.Instance:GetRoleLevel() or 0
	local exp_cfg = TaskWGData.Instance:GetRoleLevelReward(role_level)
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)

	if task_cfg and not IsEmptyTable(task_cfg) and exp_cfg and not IsEmptyTable(exp_cfg) then
		get_exp =  task_cfg.exp_copies * exp_cfg.kill_monster_exp
	end

	if cfg and cfg.reward_item_1 then
		for k,v in pairs(cfg.reward_item_1) do
			local data = {}
			local num = 0

			if v.item_id == GUILD_BUILD_TASK_OTHER_TYPE.ITEM_EXP_ID then
				num = get_exp
			end

			--不需要展示绑定
			data.item_id = v.item_id
			data.is_bind = 0
			data.num = num > 0 and num or v.num
			table.insert(reward_list, data)
		end
	end

	return reward_list
end

--任务面版的仙盟建设按钮是否需要显示
function GuildWGData:GetGuildBuildTaskIsShow()
	local is_open = FunOpen.Instance:GetFunIsOpened("guild_task")
	local task_is_finish = self:GetBuildTaskIsAllFinish()
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	if is_open and self.is_first_time_history and self.is_first_time_history == 1
		and not task_is_finish and mainrolevo and 0 ~= mainrolevo.guild_id then
		return true
	end
	return false
end

--任务栏的仙盟建设按钮是否需要显示
function GuildWGData:GetGuildBuildTaskListIsShow()
	local is_open = FunOpen.Instance:GetFunIsOpened("guild_task")
	local task_is_finish = self:GetBuildTaskIsAllFinish()
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	if is_open and self.is_first_time_history and self.is_first_time_history == 1
		and mainrolevo and 0 ~= mainrolevo.guild_id then
		return true
	end
	return false
end

--是否需要做一个场景退出的监听
function GuildWGData:IsNeedOpenOutScentEvent()
	local is_open = FunOpen.Instance:GetFunIsOpened("guild_task")
	local task_is_finish = self:GetBuildTaskIsAllFinish()
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	if is_open and not task_is_finish
		and mainrolevo and 0 ~= mainrolevo.guild_id then
		return true
	end
	return false
end

-- 获取任务状态
function GuildWGData:GetGuildBuildTaskStates(task_id)
	if  self.has_commit_task_list then
		for k,v in pairs(self.has_commit_task_list) do
			if task_id == v.task_id then
				return v.task_states
			end
		end
	end
	return 0
end

--是否有可领取的奖励
function GuildWGData:GetGuildBuildCommitId()
	if  self.has_commit_task_list then
		for k,v in pairs(self.has_commit_task_list) do
			if v.task_states == GameEnum.TASK_STATUS_COMMIT then
				return true
			end
		end
	end
	return false
end

function GuildWGData:OpenGuildBuildView()
	local cur_task_cfg = TaskWGData.Instance:GetTaskGuildBuildCfg()
	if cur_task_cfg then
		self.cur_task_id = cur_task_cfg.task_id
	else
		self.cur_task_id = nil
	end

	if  self.has_commit_task_list and nil ~= self.cur_task_id then

		if nil == self.cur_task_states then
			self.cur_task_states = self:GetGuildBuildTaskStates(self.cur_task_id)
			return
		end

		if self.cur_task_states ~= self:GetGuildBuildTaskStates(self.cur_task_id) then
			if self:GetGuildBuildTaskStates(self.cur_task_id) == GameEnum.TASK_STATUS_COMMIT then

				if cur_task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 then
					local scene_type = Scene.Instance:GetSceneType()
					self:RemberOutScenrType(scene_type)
				else
					ViewManager.Instance:Close(GuideModuleName.TaskDialog)
					FunOpen.Instance:OpenViewByName(GuideModuleName.guild_task)
				end

			end
			self.cur_task_states = self:GetGuildBuildTaskStates(self.cur_task_id)
		end

	end
end

--是否需要自动前往领取任务
function GuildWGData:NeedAutoTask()
	local guild_build_accept = TaskWGData.Instance:GetTaskGuildBuildCfg()
	if not self.task_auto_lingqu or nil == guild_build_accept then
		return
	end

	local condition = guild_build_accept.condition or 0
	local task_type = guild_build_accept.task_type or 0

	if task_type == GameEnum.TASK_TYPE_GUILD_BUILD and (condition == GameEnum.TASK_COMPLETE_CONDITION_6 or condition == GameEnum.TASK_COMPLETE_CONDITION_3) then
		--未接取前往npc
		TaskGuide.Instance:CurrTaskType(GameEnum.TASK_TYPE_GUILD_BUILD)
		TaskGuide.Instance:CanAutoAllTask(true)
        TaskGuide.Instance:SideTOStopTask(false)
		TaskWGCtrl.Instance:OperateFollowTask(guild_build_accept)
		self.task_auto_lingqu = false
	end

end

--是否需要自动前往领取任务标识
function GuildWGData:NeedAutoTaskFlag(task_id)
	local task_id = task_id and task_id or 0
	local task_cfg = TaskWGData.Instance:GetTaskConfig(task_id)

	if task_cfg and task_cfg.task_type == GameEnum.TASK_TYPE_GUILD_BUILD and (task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_6 or task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3) then
		self.task_auto_lingqu = true
		return
	end

	self.task_auto_lingqu = false
end

function GuildWGData:ClearGuildBuildAutoFlag(cur_state)
	self.task_auto_lingqu = cur_state
end
-------------------------------------仙盟工资----------------------------------
function GuildWGData:SetGuildWageTaskInfo(protocol)
	self.guild_wage_info = protocol.task_info
end

function GuildWGData:GetGuildWageTaskInfo()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local task_list = {}
	for k,v in pairs(self.guild_wage_cfg) do
		local list = {}
		if role_level >= v.task_open_level then
			local states, cur_time = self:GetGuildWageCurTaskStates(v.type,v.time)
			if "" ~= v.open_task_id then
				if TaskWGData.Instance:GetTaskIsCompleted(v.open_task_id)  then
					list["old_info"] = v
					list["states"] = states
					list["cur_time"] = cur_time
					table.insert(task_list,list)
				end
			else
				list["old_info"] = v
				list["states"] = states
				list["cur_time"] = cur_time
				table.insert(task_list,list)
			end
		else
			list["old_info"] = v
			list["states"] = GUILD_WAGE_TASK_STATES.LOCK
			list["cur_time"] = 0
			table.insert(task_list,list)
		end
	end
	if not IsEmptyTable(task_list) then
		table.sort( task_list, SortTools.KeyUpperSorter("states") )
	end
	return task_list
end

function GuildWGData:GetGuildWageCurTaskStates(type,need_num)
	local task_info = nil
	if self.guild_wage_info then
		task_info = self.guild_wage_info[type]
	end
	if nil ~= task_info then
		if need_num > task_info.cur_time then
			return GUILD_WAGE_TASK_STATES.WEIWANCHENG, task_info.cur_time
		elseif task_info.fetch_reward_flag == 1 then
			return GUILD_WAGE_TASK_STATES.YILINGQU, task_info.cur_time
		else
			return GUILD_WAGE_TASK_STATES.KELINGQU, task_info.cur_time
		end
	end
	return 1, 0
end

function GuildWGData:GetGuildWageSilverId()
	local other = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1]
	return other.silver_id
end
function GuildWGData:GetGuildActiveCount()
	local other = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1]
	local other_1 = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").apply_for_agent
	local opengame_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local agent_dur_time_s
	if opengame_day <= other_1[1].opengame_day then
		agent_dur_time_s = other_1[1].offline_time_s
	else
		agent_dur_time_s = other_1[2].offline_time_s
	end
	return other.active_count,agent_dur_time_s
end
function GuildWGData:GetMenmberSelectLimitCfg()
	local other = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").quick_clean
	return other
end
--获取任务工资的任务配置不考虑是否开启
function GuildWGData:GetAllActivityHallCfg(act_type)
	return self.wage_show_cfg[act_type]
end
----------------------------------------仙盟捐献-------------------------------------
function GuildWGData:GetGuildContributeCfg()
	local contribute_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").contribute
	return contribute_cfg
end
--剩余次数
function GuildWGData:GetCurJxNum(type)
	local contribute_cfg = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").contribute
	local guildvo = GuildDataConst.GUILDVO
	local max_num = contribute_cfg[type].contribute_maxnum
	local guild_contributeTimes = guildvo.guild_contributeTimes
	if guild_contributeTimes and guild_contributeTimes[type] then
		return max_num - guild_contributeTimes[type]
	end
	return 0
end
--道具已经捐献过一次不做红点提示
function GuildWGData:IsCanDhowJuXianRes()
	local juanxian_type = GUILD_JUANXIAN_OTHER_TYPE.ITEM_JAUNXIAN_TYPE
	local guildvo = GuildDataConst.GUILDVO
	local guild_contributeTimes = guildvo.guild_contributeTimes
	if guild_contributeTimes and guild_contributeTimes[juanxian_type] then
		return guild_contributeTimes[juanxian_type] == 0
	end
	return false
end

function GuildWGData:SetGuildMemberRankInfo(protocol)
	self.guild_member_rank_info = protocol.guild_list
end

function GuildWGData:GetGuildMemberRankInfo()
	return self.guild_member_rank_info
end

function GuildWGData:GetGuildMemberRankInfoByIndex(index)
	if self.guild_member_rank_info and self.guild_member_rank_info[index] then
		return self.guild_member_rank_info[index].guild_member_list
	end
end

function GuildWGData:SetGuildMemberRankIndex(index)
	if index < 0 then return end
	self.guild_member_index = index
end

function GuildWGData:GetGuildMemberRankIndex()
	return self.guild_member_index
end

-- 返回仙盟排名，跟角色排名
function GuildWGData:GetGuildMemberRankInfoByUserId(user_id)
	if self.guild_member_rank_info then
		for i,v in ipairs(self.guild_member_rank_info) do
			for i1,v1 in ipairs(v.guild_member_list) do
				if user_id == v1.user_id then
					return i,i1
				end
			end
		end
	end
end

-------------------------------------仙盟技能信息模块----------------------------------------
function GuildWGData:SetGuildSkillInfo(protocol)
	self.guild_skill_level = protocol.guild_skill_level
end

function GuildWGData:GetCurSkillLevel(skill_id)
	if self.guild_skill_level and self.guild_skill_level[skill_id] then
		return self.guild_skill_level[skill_id]
	end
	return 0
end

--阶段信息表
function GuildWGData:GetSkillStageInfo()
	--3个阶段
	local stage_info = {}
	for i=1,3 do
		local is_show = self:GetCurStageOpen(i)
		if is_show then
			table.insert(stage_info,self.guild_skill_cfg.stage_open_condition[i])
		end
	end
	return stage_info
end

--阶段技能信息
function GuildWGData:GetCurStageSkillCfg(stage)
	local skill_min_id = 1 + (stage - 1)*6
	local skill_max_id = stage*6
	local skill_list = {}
	for i = skill_min_id, skill_max_id do
		local cfg = self.skill_cfg[i]
		if cfg then
			table.insert(skill_list,cfg)
		end
	end
	return skill_list
end
--获取单个技能信息
function GuildWGData:GetCurOneSkillCfg(skill_id)
	return self.skill_cfg[skill_id]
end
--当前阶段是否有技能已经学习
function GuildWGData:GetCurStageIsSkillOpen(stage)
	local skill_min_id = 1 + (stage - 1)*6
	local skill_max_id = stage*6
	for i = skill_min_id, skill_max_id do
		local level  = self:GetCurSkillLevel(i)
		if level > 0 then
			return true
		end
	end
	return false
end

--阶段开启信息
function GuildWGData:GetCurStageOpen(stage)
	local is_show = false
	local is_open = false
	local cur_stage = self.guild_skill_cfg.stage_open_condition[stage]
	local is_study_skill = self:GetCurStageIsSkillOpen(stage)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local guild_level = GuildDataConst.GUILDVO.guild_level
	--已经学习过技能
	if is_study_skill then
		is_show = true
	end
	if not is_show then
		if role_level >= cur_stage.show_role_level and guild_level >= cur_stage.show_guild_level then
			is_show = true
		end
	end
	if role_level >= cur_stage.open_role_level and guild_level >= cur_stage.open_guild_level then
		is_open = true
	end
	return is_show, is_open
end

--技能开启信息
function GuildWGData:GetCurSkillOpen(skill_id)
	local is_show = false
	local is_open = false
	if skill_id == nil then
		return is_show, is_open
	end
	local cur_skill = self.skill_cfg[skill_id]
	local skill_level = self:GetCurSkillLevel(skill_id)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local guild_level = GuildDataConst.GUILDVO.guild_level
	local pre_skill_show_level = 0
	local pre_skill_open_level = 0
	if cur_skill.show_pre_skill_id > 0 then
		pre_skill_show_level = self:GetCurSkillLevel(cur_skill.show_pre_skill_id)
	end
	if cur_skill.open_pre_skill_id then
		pre_skill_open_level = self:GetCurSkillLevel(cur_skill.open_pre_skill_id)
	end
	--已经学习过技能
	if skill_level > 0 then
		is_show = true
	end
	if not is_show then
		if role_level >= cur_skill.show_role_level and guild_level >= cur_skill.show_guild_level and pre_skill_show_level >= cur_skill.show_pre_skill_level then
			is_show = true
		end
	end
	if role_level >= cur_skill.open_role_level and guild_level >= cur_skill.open_guild_level and pre_skill_open_level >= cur_skill.open_pre_skill_level then
		is_open = true
	end
	return is_show, is_open
end

--获取技能升级配置
function GuildWGData:GetCurSkillLevelCfg(skill_id,skill_level)
	if self.skill_level_cfg[skill_id] then
		return self.skill_level_cfg[skill_id][skill_level]
	end
end
--记录进入页面以前激活的skill
function GuildWGData:RemberEnterViewJiHuoSkill()
	self.rember_skill = {}
	self.rember_skill_level = {}
	if self.guild_skill_level then
		for i=1,GUILD_SKILL_TYPE.MAX_SKILL_NUM do
			local _,is_open = self:GetCurSkillOpen(i)
			self.rember_skill[i] = is_open
			local skill_level = self:GetCurSkillLevel(i)
			self.rember_skill_level[i] = skill_level
		end
	end
end

function GuildWGData:GetEnterViewJiHuoSkillState(skill_id)
	if self.rember_skill then
		return self.rember_skill[skill_id],self.rember_skill_level[skill_id]
	end
	return false
end

function GuildWGData:RemberSelectSkillId(skill_id)
	self.select_skill_id = skill_id
end

function GuildWGData:GetSelectSkillId()
	return self.select_skill_id
end

function GuildWGData:GetBnagGongItemId()
	local other = self.guild_skill_cfg.other[1]
	return other.item
end

function GuildWGData:CurSkillIsCanUpLevel(skill_id)
	local _, is_open = self:GetCurSkillOpen(skill_id)
	if not is_open then
		return false
	end
	local skill_level = self:GetCurSkillLevel(skill_id)
	local skill_cfg = self:GetCurOneSkillCfg(skill_id)
	if skill_level >= skill_cfg.skill_max_level then
		return false
	end
	local skill_level_cfg = self:GetCurSkillLevelCfg(skill_id,skill_level + 1)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local guild_level = GuildDataConst.GUILDVO.guild_level
	if role_level < skill_level_cfg.role_level_limit or guild_level < skill_level_cfg.guild_level_limit then
		return false
	end
	if skill_level_cfg.pre_skill_id > 0 then
		local pre_skill_level = self:GetCurSkillLevel(skill_level_cfg.pre_skill_id)
		if pre_skill_level < skill_level_cfg.pre_skill_level then
			return false
		end
	end
	if skill_level_cfg.contribution > 0 then
		local contribution = RoleWGData.Instance.role_vo.longhun
		if skill_level_cfg.contribution > contribution then
			return false
		end
	end
	if skill_level_cfg.consume_item_id > 0 then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(skill_level_cfg.consume_item_id)
		if skill_level_cfg.consume_num > item_num then
			return false
		end
	end
	return true
end

function GuildWGData:IsShowGuildSkillRedPoint()
	if RoleWGData.Instance.role_vo.guild_id == 0 then
		return 0
	end
	for i=1,GUILD_SKILL_TYPE.MAX_SKILL_NUM do
		if self:CurSkillIsCanUpLevel(i) then
	 		self:MainInvateTip(MAINUI_TIP_TYPE.GUILD_LOGNHUI, TabIndex.guild_skill, true)
			return 1
		end
	end
	self:MainInvateTip(MAINUI_TIP_TYPE.GUILD_LOGNHUI)
	return 0
end

function GuildWGData:MainInvateTip(show_tip, tab_index, result)
	if result then
	    MainuiWGCtrl.Instance:InvateTip(show_tip, 1, function ()
	        FunOpen.Instance:OpenViewByName(GuideModuleName.GuildView, tab_index)
	        return true
	    end)
	else
		MainuiWGCtrl.Instance:InvateTip(show_tip,0)
	end

end

function GuildWGData:GetCurCanUpLevelSkillId()
	for i=1,GUILD_SKILL_TYPE.MAX_SKILL_NUM do
		if self:CurSkillIsCanUpLevel(i) then
			return i
		end
	end
	return 1
end

--计算总战力
function GuildWGData:GetGuildSkillAllPower()
	local power = 0
	local info = {}
	for i=1,GUILD_SKILL_TYPE.MAX_SKILL_NUM do
		local skill_level = self:GetCurSkillLevel(i)
		local skill_level_cfg = self:GetCurSkillLevelCfg(i,skill_level)
		if skill_level > 0 and skill_level_cfg then
			if skill_level_cfg.attr_name then
				if nil == info[skill_level_cfg.attr_name] then
					info[skill_level_cfg.attr_name] = skill_level_cfg.attr_value
				else
					info[skill_level_cfg.attr_name] = info[skill_level_cfg.attr_name] + skill_level_cfg.attr_value
				end
			end
		end
	end
	local attr = AttributeMgr.GetAttributteByClass(info)
	power = AttributeMgr.GetCapability(attr)
	return power
end
function GuildWGData:SetGuildAgentInfo( protocol )
	self.agent_uid = protocol.uid
	self.agent_begi_timestamp = protocol.agent_begi_timestamp
	self.agent_name = protocol.name
end
function GuildWGData:GetGuildAgentInfo()
	return self.agent_uid or 0,self.agent_begi_timestamp or 0,self.agent_name or ""
end


function GuildWGData:SetSelectSelectDataLimit(index,limit_value)
	if nil == self.select_limit_list then
		self.select_limit_list = {}
		for i=1,3 do
			self.select_limit_list[i] = nil
		end
	end
	self.select_limit_list[index] = limit_value
end
function GuildWGData:GetSelectSelectDataLimit()
	if nil == self.select_limit_list then
		self.select_limit_list = {}
		for i=1,3 do
			self.select_limit_list[i] = nil
		end
	end
	return self.select_limit_list
end
function GuildWGData:SetSelectSelectMenmberLimit(list)
	self.has_select_list = list
end
function GuildWGData:GetSelectSelectMenmberLimit()
	--print_error(self.has_select_list)
	return self.has_select_list or {}
end
function GuildWGData:IsSelectMenmber(uid)
	if nil == self.has_select_list or IsEmptyTable(self.has_select_list) then return false end
	for k,v in pairs(self.has_select_list) do
		if v.uid == uid then
			return true
		end
	end
	return false
end
function GuildWGData:AddOrSubMenmber(is_add,data)
	if nil == self.has_select_list then
		self.has_select_list = {}
	end
	if is_add then
		table.insert(self.has_select_list,data)
	else
		for k,v in pairs(self.has_select_list) do
			if v.uid == data.uid then
				table.remove(self.has_select_list,k)
				break
			end
		end
	end
	--print_error(is_add,self.has_select_list)

end

--记录仙盟建设退出的场景类型
function GuildWGData:RemberOutScenrType(scene_type)
	self.rember_scene_type = scene_type
end
function GuildWGData:GetRemberOutScenrType()
	return self.rember_scene_type
end

function GuildWGData:SetGuildShouHuInFBRankInfo(protocol)
	self.in_fb_rank_list = protocol.rank_list
end

function GuildWGData:GetGuildShouHuInFBRankInfo()
	table.sort(self.in_fb_rank_list ,function (a,b)
		local order_a = 100000
		local order_b = 100000
		if a["wave"] > b["wave"] then
			order_a = order_a + 10000
		elseif a["wave"] < b["wave"] then
			order_b = order_b + 10000
		end

		if a["pass_time"] < b["pass_time"] then
			order_a = order_a + 1000
		elseif a["pass_time"] > b["pass_time"] then
			order_b = order_b + 1000
		end

		return order_a > order_b
	end)
	return self.in_fb_rank_list
end

function GuildWGData:GetGuildShouHuFBMaxRank()
	local hurt = self.in_fb_rank_list[1] and self.in_fb_rank_list[1].hurt or 1000000
	hurt = hurt > 0 and hurt or 1
	return hurt
end

--保存最后一个邀请我加入仙盟的信息
function GuildWGData:SetOnInviteNotify(protocol)
	if nil == protocol then
		return
	end

	if IsEmptyTable(protocol) then
		self.invite_data = protocol
		return
	end

	local data = {
		guild_id = protocol.guild_id,
		invite_uid = protocol.invite_uid,
		invite_name = protocol.invite_name,
		guild_name = protocol.guild_name
	}

	self.invite_data = data
end

--获取最后一个邀请我加入仙盟的信息
function GuildWGData:GetOnInviteNotify()
	return self.invite_data
end

-------------------- 红包动画信息 -----------------------------------------------
function GuildWGData:SetRedPacketAnimShow(show)
	self.show_redpacket_anim = show
end

function GuildWGData:GetRedPacketAnimShow()
	return self.show_redpacket_anim
end

function GuildWGData:SetRedPacketAnimEndObj(obj)
	self.redpacket_anim_endobj = obj
end

function GuildWGData:GetRedPacketAnimEndObj()
	return self.redpacket_anim_endobj
end
-------------------- 红包动画信息 end--------------------------------------------

--活动红点监听
function GuildWGData:IsNeedMonitorRed(activity_type)
	if nil == self.need_monitor_activity then
		self.need_monitor_activity = {}

		self.need_monitor_activity = {
			[ACTIVITY_TYPE.GUILD_ANSWER] = RemindName.Guild_Activit_DaTi, --答题
			[ACTIVITY_TYPE.KF_XIANMENGZHAN] = RemindName.Guild_Activit_ZhengBa, --争霸
			[ACTIVITY_TYPE.GUILD_FB] = RemindName.Guild_Activit_ShiLian, --守护
		}
	end

	return self.need_monitor_activity[activity_type]
end

--保存一下是否点击进入过仙盟拍卖行(策划要求如果点了结算面板的前往拍卖行,出去之后就不弹出了)
function GuildWGData:SetIsInPaiMaiHang(data)
	self.is_open_paimaihang = data
end

--是否点击进入过仙盟拍卖行
function GuildWGData:GetIsInPaiMaiHang()
	return self.is_open_paimaihang
end

function GuildWGData:GetGuildOtherCfg()
	return self.guild_other_config
end

function GuildWGData:GetGuildRobotCfgByGuildName(name)
    if not IsEmptyTable(self.random_robot_cfg) then
        for k, v in pairs(self.random_robot_cfg) do
            if v.guild_name == name then
                return v
            end
        end
    end
    return {}
end

function GuildWGData:GetRobotEquipListCfg(drop_id, role_sex, role_prof)
    if not self.robot_equip_cfg then --策划说所有机器人盟主的物品掉落id一致
        self.robot_equip_cfg = ItemWGData.Instance:GetRobotEquipsByDropId(drop_id)
    end
    local equip_list = {}
    local no_limit_sex = -1
    local no_limit_prof = 0
    for i = 0, GameEnum.EQUIP_INDEX_HUNJIE do
        equip_list[i] = {item_id = 0, index = i}
    end 
    if not IsEmptyTable(self.robot_equip_cfg) then
        for i_idx, i_data in ipairs(self.robot_equip_cfg.item_list) do
            if (i_data.sex == role_sex or i_data.sex == no_limit_sex) and
                 (i_data.prof == role_prof or i_data.prof == no_limit_prof) then
                local part = ItemWGData.GetEquipPartByItemId(i_data.item_id)
                local vo = {}
                vo.item_id = i_data.item_id
                vo.star_level = i_data.param
                vo.index = part
                equip_list[part] = vo
            end
        end
    end
    return equip_list
end


function GuildWGData:GetRobotBrowseCfgByRoleName(role_name)
    local data_info = {}
    local role_vo = RoleWGData.Instance.role_vo
    if not IsEmptyTable(self.random_robot_cfg) then
        for k, v in pairs(self.random_robot_cfg) do
            if v.role_name == role_name then
                local cfg = v
                data_info.server_id = role_vo.server_id
                data_info.plat_type = role_vo.plat_type
                data_info.plat_name = role_vo.plat_name

                data_info.role_id = COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID
                data_info.vip_level = 4
                data_info.role_name = role_name

                data_info.equipment_info = {}
                local item_list = self:GetRobotEquipListCfg(cfg.equip, cfg.sex, cfg.prof)--[cfg.equip]
                for k, v in pairs(item_list) do
                    if v.item_id then
                        local vo = {}
                        vo.equip_id = v.item_id
                        vo.star_level = v.star_level
                        vo.index = v.index
                        data_info.equipment_info[vo.index] = vo
                    end
                end
                data_info.sex = cfg.sex
                data_info.prof = cfg.prof
            
                data_info.hp = cfg.shengming
                data_info.gongji = cfg.gongji
                data_info.fangyu = cfg.fangyu
                data_info.pojia = cfg.pojia
                data_info.yuansu_sh = cfg.yuansu_shanghai
                data_info.mingzhong_per = cfg.per_mingzhong
                data_info.shanbi_per = cfg.per_shanbi
                data_info.shengming_hf = cfg.reblood

                data_info.capability = cfg.mengzhu_cap
                data_info.level = cfg.mengzhu_lv

                data_info.appearance = {}
                data_info.appearance.fashion_photoframe = 0
                data_info.appearance.fashion_body = cfg.model
        
                data_info.guild_name = cfg.guild_name
                data_info.lover_info = {}
                data_info.lover_info.lover_uid = 0

                data_info.shengpin_info = {}
                data_info.stone_infos = {}
                data_info.stone_refine_level = {}
                data_info.shizhuang_info = {}
                data_info.updateattr = {}
      
                local imp_item_id = cfg.imp_item_id
                data_info.xiaogui_data1 = {item_id = imp_item_id}
                data_info.xiaogui_data2 = {}
                local cfg = EquipmentWGData.GetXiaoGuiCfg(imp_item_id)
                data_info.used_imp_type_1 = cfg.imp_type
                data_info.used_imp_type_2 = 0
            end
        end
    end
    return data_info
end

function GuildWGData:GetGuildRobotCfgByRoleName(role_name)
    if not IsEmptyTable(self.random_robot_cfg) then
        for k, v in pairs(self.random_robot_cfg) do
            if v.role_name == role_name then
                return v
            end
        end
    end
    return {}
end

function GuildWGData:GetGuildSkillRemindByStage(stage)
	local stage_cfg = self:GetCurStageSkillCfg(stage)
	if stage_cfg then
		for k,v in pairs(stage_cfg) do
			local can_up_level = self:CurSkillIsCanUpLevel(v.skill_id)
			if can_up_level then
				return true
			end
		end
	end
	return false
end

--获取仙盟旗帜类型
function GuildWGData:GetGuildQiZi(level)
	level = level or 1
	local qizi_index = 1
	if level >= 1 and level <= 3 then
		qizi_index = 1
	elseif level >= 4 and level <= 7 then
		qizi_index = 2
	elseif level >= 8 and level <= 10 then
		qizi_index = 3
	end
	return qizi_index
end

--保存仙盟的评级

function GuildWGData:SetCurGuildZone(zone)
	if self.cur_guild_zone and self.cur_guild_zone ~= zone and self.cur_guild_zone < 0 and zone >= 0 then
		self:SetPlayZoneAnim(true)
	end
	self.cur_guild_zone = zone
end

function GuildWGData:GetCurGuildZone()
	return self.cur_guild_zone
end

function GuildWGData:SetPlayZoneAnim(flag)
	self.play_zone_anim = flag
end

function GuildWGData:GetPlayZoneAnim()
	return self.play_zone_anim
end

function GuildWGData:GetMyRinking()
	local m_list = GuildDataConst.GUILD_MEMBER_LIST
	local member_datasource = {}
	for i = 1, m_list.count do
		local item = m_list.list[i]
		local datasource = {uid = item.uid, role_name = item.role_name, level = item.level, sex = item.sex, prof = item.prof,
			post = item.post, vip_type = item.vip_type, vip_level = item.vip_level, is_online = item.is_online, join_time = item.join_time,
			last_login_time = item.last_login_time, gongxian = item.gongxian, total_gongxian = item.total_gongxian, capability = item.capability, photframe = item.photframe,
			sort_index = GuildDataConst.GUILD_POST_SORTINDEX_LIST[item.post]}
		table.insert(member_datasource, datasource)
	end
	table.sort(member_datasource, SortTools.KeyUpperSorters("capability"))
	local my_uuid = RoleWGData.Instance:GetUUid()
	for k,v in pairs(member_datasource) do
		if my_uuid.temp_low == v.uid then
			return k 
		end
	end
	return 0
end

--获取仙盟成员信息
function GuildWGData:GetGuildMemberRole(uid)
	local m_list = GuildDataConst.GUILD_MEMBER_LIST
	for i = 1, m_list.count do
		local item = m_list.list[i]
		if item.uid == uid then
			return item
		end
	end
	return nil
end

--跨服仙盟战
function GuildWGData:SetKFXianmengzhanMatchInfo(protocol)
	self.kf_xianmengzhan_match_info = protocol
	self.guild_kf_fight_state = protocol.match_state
	GlobalEventSystem:Fire(Guill_XMZ_FIGHT_STATE.STATE_CHANGE)
	self.next_match_start_time = protocol.next_match_start_time
	self.m_curr_match_end_time = protocol.m_curr_match_end_time
	self.my_guild_side = protocol.my_guild_side
	self.my_guild_opponent_guild = protocol.my_guild_opponent_guild

	self:AddKFXianmengzhanTimeCount()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	if act_info and act_info.status == ACTIVITY_STATUS.OPEN then
		if not act_cfg then
			return
		end

		local role_level = RoleWGData.Instance:GetAttr("level")
		if role_level < act_cfg.level or role_level > act_cfg.level_max then
			return
		end

		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.KF_XIANMENGZHAN, act_info.next_time, act_cfg)
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		if self.open_kf_xianmengzhan_notice == nil and self.m_curr_match_end_time > server_time then
			ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.KF_XIANMENGZHAN)
			self.open_kf_xianmengzhan_notice = true
		end
	end
end

function GuildWGData:ClearKFXianmengzhanNotice()
	self.open_kf_xianmengzhan_notice = nil
end

--跨服仙盟战 轮次或休赛期的倒计时
function GuildWGData:AddKFXianmengzhanTimeCount()
	local act_state = self.guild_kf_fight_state
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.m_curr_match_end_time and self.m_curr_match_end_time > 0 and 
		(act_state == GUILD_KF_BATTLE_MATCH_STATE.HALF_FINAL_GAME or act_state == GUILD_KF_BATTLE_MATCH_STATE.FINAL_GAME) then
		if self.m_curr_match_end_time > server_time then
			self:RemoveKFXianmengzhanTimeCount()
			CountDownManager.Instance:AddCountDown("kf_xianmengzhan_timecount", nil, BindTool.Bind(self.KFXianmengzhanTimeCountCB, self), self.m_curr_match_end_time, nil,1)
		end
	end
	if self.next_match_start_time and self.next_match_start_time > 0 and act_state == GUILD_KF_BATTLE_MATCH_STATE.FINAL_GAME then
		if self.next_match_start_time > server_time then
			self:RemoveKFXianmengzhanTimeCount()
			CountDownManager.Instance:AddCountDown("kf_xianmengzhan_timecount", nil, BindTool.Bind(self.KFXianmengzhanTimeCountCB, self), self.next_match_start_time, nil,1)
		end
	end
end

function GuildWGData:RemoveKFXianmengzhanTimeCount()
	if CountDownManager.Instance:HasCountDown("kf_xianmengzhan_timecount") then
		CountDownManager.Instance:RemoveCountDown("kf_xianmengzhan_timecount")
	end
end

function GuildWGData:KFXianmengzhanTimeCountCB()
	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local act_state = self.guild_kf_fight_state
	if is_open and (act_state == GUILD_KF_BATTLE_MATCH_STATE.HALF_FINAL_GAME or act_state == GUILD_KF_BATTLE_MATCH_STATE.FINAL_GAME) then
		self:ClearKFXianmengzhanNotice()
		GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_MATCH_STATE)
	end
end

--判断仙盟战是否处于休赛期
function GuildWGData:GetIsPrepareRestTime()
	local cur_state = self:GetCurGuildWarState()
	if cur_state == GuildWGData.GuildWarState.XiuSaiState then
		return true
	end
	return false
end

function GuildWGData:GetKfXmzNextMatchStartTime()
	return self.next_match_start_time
end

function GuildWGData:GetKfXmzCurrMatchEndTime()
	return self.m_curr_match_end_time
end

function GuildWGData:GetKfMyGuildPpponentGuild()
	return self.my_guild_opponent_guild
end

function GuildWGData:GetKfMyGuildSide()
	return self.my_guild_side
end

--判断我是否能参加仙盟战
function GuildWGData:GetMyCanJoinGuildWar()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	local plat_type = RoleWGData.Instance.role_vo.plat_type
	
	if IsEmptyTable(self.kf_xianmengzhan_match_info) then
		return false
	end
	local zone_list = self.kf_xianmengzhan_match_info.zone_list
	if IsEmptyTable(zone_list) then
		print_error(">>>>>>>>>>判断我是否能参加仙盟战  服务端下发数据为空！！！！")
		return false
	end
	for k,v in pairs(zone_list) do
		if v.pk_guild_list then
			for k1,v1 in pairs(v.pk_guild_list) do
				if v1.guild_id == guild_id and v1.plat_type == plat_type then
					return true
				end
			end
		end
	end
	return false
end

--仙盟排名
function GuildWGData:SetKFXianmengzhanGuildRank(protocol)
	self.kf_xianmengzhan_guild_rank = protocol.guild_rank_list
	self.xmz_top_1_guild_mengzhu_uuid = protocol.top_1_guild_mengzhu_uuid
end

--连胜结果
function GuildWGData:SetKFXianmengzhanKeepWinInfo(protocol)
	self.kf_xianmengzhan_keep_win_times = protocol.keep_win_times
end

function GuildWGData:GetLianShen()
	return self.kf_xianmengzhan_keep_win_times or 0
end

function GuildWGData:GetKFXianmengzhanGuildRank()
	return self.kf_xianmengzhan_guild_rank or {}
end

function GuildWGData:GetXMZZhanBaoRankData()
	local data = {}
	local rank_data = self:GetKFXianmengzhanGuildRank()
	for i=1,4 do
		table.insert(data,rank_data[i])
	end
	return data
end

function GuildWGData:GetXMZTop1GuildMengZhuUUId()
	return self.xmz_top_1_guild_mengzhu_uuid
end

function GuildWGData:GetKfMatchTeamList()
	if IsEmptyTable(self.kf_xianmengzhan_match_info) then
		return nil
	end

	local team_info_list = {}
	for k,v in pairs(self.kf_xianmengzhan_match_info.zone_list) do
		team_info_list[k] = {}
		team_info_list[k] = v.pk_guild_list
	end

	-- print_error("team_info_list =", team_info_list)
	return team_info_list
end

function GuildWGData:GetKfGuildBattleFightState()
	return self.guild_kf_fight_state or 0
end

function GuildWGData:GetKfGuildWarZoneInfo(cur_zone)
	if IsEmptyTable(self.kf_xianmengzhan_match_info) then
		return nil
	end
	return self.kf_xianmengzhan_match_info.zone_list and self.kf_xianmengzhan_match_info.zone_list[cur_zone]
end

--获取仙盟第一轮晋级的结果
function GuildWGData:GetGuildWarShowSecondWinData(cur_zone)
	local show_second_win_list = {
		[1] = false,
		[2] = false,
		[3] = false,
		[4] = false,
	}
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	if IsEmptyTable(zone_info) then return show_second_win_list end

	local show_temp_second_win_list = {}

	if zone_info.winner_4_to_2_team_info_list then
		for k1,v1 in pairs(zone_info.winner_4_to_2_team_info_list) do
			if v1.guild_id > 0 and v1.plat_type >= 0 then
				table.insert(show_temp_second_win_list,v1)
			end
		end
	end

	local pk_guild_key_list = zone_info.pk_guild_key_list
	for k,v in pairs(show_temp_second_win_list) do
		local key = tostring(v.guild_id) .. tostring(v.plat_type)
		local info = pk_guild_key_list[key]
		if info then
			local index = info.index
			local other_index = index % 2 == 0 and index - 1 or index + 1
			show_second_win_list[index] = true
			show_second_win_list[other_index] = true
		end
	end

	return show_second_win_list
end

--获取是否可以显示第二轮胜负数据
function GuildWGData:GetGuildWarShowChampionWinData(cur_zone)
	local show_champion_win_list = {
		[1] = false,
		[2] = false,
		[3] = false,
		[4] = false,
	}
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	if IsEmptyTable(zone_info) then return show_champion_win_list end
	
	if zone_info.winner_2_to_1_team_info_list then
		local info = zone_info.winner_2_to_1_team_info_list
		if info.guild_id > 0 and info.plat_type >= 0 then
			show_champion_win_list[1] = true
			show_champion_win_list[2] = true
		end
	end

	if zone_info.winner_3vs4 then
		local info = zone_info.winner_3vs4
		if info.guild_id > 0 and info.plat_type >= 0 then
			show_champion_win_list[3] = true
			show_champion_win_list[4] = true
		end
	end
	return show_champion_win_list
end

function GuildWGData:KfGuildWarIsSecondeWin(cur_zone,plat_type,guild_id)
	if guild_id <= 0 then return false end
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	if zone_info.winner_4_to_2_team_info_list then
		for k1,v1 in pairs(zone_info.winner_4_to_2_team_info_list) do
			if v1.guild_id == guild_id and v1.plat_type == plat_type then
				return true
			end
		end
	end
	return false
end

function GuildWGData:KfGuildWarIsChampionWin(cur_zone,plat_type,guild_id)
	if guild_id <= 0 then return false end
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	if zone_info.winner_2_to_1_team_info_list then
		local info = zone_info.winner_2_to_1_team_info_list
		if info.guild_id == guild_id and info.plat_type == plat_type then
			return true
		end
	end
	if zone_info.winner_3vs4 then
		local info = zone_info.winner_3vs4
		if info.guild_id == guild_id and info.plat_type == plat_type then
			return true
		end
	end
	return false
end


--判断我所在的赛区是否已结束
function GuildWGData:GetMyGuildWarIsOver()
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	local plat_type = RoleWGData.Instance.role_vo.plat_type
	local is_over = false
	if IsEmptyTable(self.kf_xianmengzhan_match_info) then
		return is_over
	end
	local zone_list = self.kf_xianmengzhan_match_info.zone_list
	if IsEmptyTable(zone_list) then
		print_error(">>>>>>>>>>判断我是否能参加仙盟战  服务端下发数据为空！！！！")
		return is_over
	end
	local my_guild_opponent_guild = GuildWGData.Instance:GetKfMyGuildPpponentGuild()
	-- if my_guild_opponent_guild and my_guild_opponent_guild.guild_id <= 0 then
	-- 	return is_over
	-- end

	local cur_state = self:GetCurGuildWarState()
	local zone_info = {}
	for k,v in pairs(zone_list) do
		if v.pk_guild_list then
			for k1,v1 in pairs(v.pk_guild_list) do
				if v1.guild_id == guild_id and v1.plat_type == plat_type then
					zone_info = v
					break
				end
			end
		end
	end

	if IsEmptyTable(zone_info) then
		return is_over
	end

	if cur_state == GuildWGData.GuildWarState.OneState then
		local winner_4_to_2_team_info_list = zone_info.winner_4_to_2_team_info_list
		for k,v in pairs(winner_4_to_2_team_info_list) do
			if (v.guild_id == guild_id and v.plat_type == plat_type) 
				or (my_guild_opponent_guild.guild_id == guild_id and my_guild_opponent_guild.plat_type == plat_type) then
				is_over = true
				break
			end
		end
	elseif cur_state == cur_state == GuildWGData.GuildWarState.EndState then
		is_over = true
	elseif cur_state == GuildWGData.GuildWarState.XiuSaiState then
		is_over = false
	elseif cur_state == GuildWGData.GuildWarState.TwoState then
		local my_over = self:KfGuildWarIsChampionWin(zone_info.zone_index,plat_type,guild_id)
		local opponent_over = self:KfGuildWarIsChampionWin(zone_info.zone_index,my_guild_opponent_guild.plat_type,my_guild_opponent_guild.guild_id)
		is_over = my_over or opponent_over
	end
	
	return is_over
end

GuildWGData.GuildWarState = {
	None = 0,			--未开始
	OneState = 1,		--第一轮
	XiuSaiState = 2,	--休赛期
	TwoState = 3,		--第二轮
	EndState = 4,		--已结束
}

function GuildWGData:GetCurGuildWarState()
	local cur_state = GuildWGData.GuildWarState.None
	local activity_info =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local is_standy_open = false
	if activity_info then
		is_standy_open = activity_info.status == ACTIVITY_STATUS.STANDY or activity_info.status == ACTIVITY_STATUS.OPEN
	end
	if is_standy_open then
		local act_state = self:GetKfGuildBattleFightState()
		local next_match_start_time = self:GetKfXmzNextMatchStartTime()
		local m_curr_match_end_time = self:GetKfXmzCurrMatchEndTime()
		if act_state == GUILD_KF_BATTLE_MATCH_STATE.HALF_FINAL_GAME then
			cur_state = GuildWGData.GuildWarState.OneState
		elseif act_state == GUILD_KF_BATTLE_MATCH_STATE.FINAL_GAME and next_match_start_time and next_match_start_time > 0 then
			cur_state = GuildWGData.GuildWarState.XiuSaiState
		elseif act_state == GUILD_KF_BATTLE_MATCH_STATE.FINAL_GAME and m_curr_match_end_time and m_curr_match_end_time > 0 then
			cur_state = GuildWGData.GuildWarState.TwoState
		elseif act_state == GUILD_KF_BATTLE_MATCH_STATE.END_GAME then
			cur_state = GuildWGData.GuildWarState.EndState
		end		
	end

	return cur_state
end

function GuildWGData:GetKfGuildWarWinner4To2Info(cur_zone)
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	return zone_info and zone_info.winner_4_to_2_team_info_list
end

function GuildWGData:GetKfGuildWarPKGameInfo(cur_zone)
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	return zone_info and zone_info.pk_guild_list
end

function GuildWGData:GetKfGuildWarFinalGameInfo(cur_zone)
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	local winner_4_to_2_team_info_list = zone_info.winner_4_to_2_team_info_list
	local info_list = {}
	local winner_1 = winner_4_to_2_team_info_list[1]
	local winner_2 = winner_4_to_2_team_info_list[2]
	info_list[1] = {}
	info_list[2] = {}

	for k,v in pairs(zone_info.pk_guild_list) do
		if v.guild_id == winner_1.guild_id and v.plat_type == winner_1.plat_type and IsEmptyTable(info_list[1]) then
			info_list[1] = v
		elseif v.guild_id == winner_2.guild_id and v.plat_type == winner_2.plat_type and IsEmptyTable(info_list[2]) then
			info_list[2] = v
		else
			table.insert(info_list,#info_list+1,v)
		end
	end
	return info_list
end

function GuildWGData:GetKfGuildWarWinner2To1Info(cur_zone)
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	local winner_2_to_1_info = zone_info.winner_2_to_1_team_info_list
	if not IsEmptyTable(winner_2_to_1_info) and winner_2_to_1_info.guild_id > 0 then
		local info = self:GetKfGuildWarBaseGuildInfo(cur_zone,winner_2_to_1_info.guild_id,winner_2_to_1_info.plat_type)
		return info
	end
end

function GuildWGData:GetKfGuildWarWinner4To2Index(cur_zone)
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	local winner_4_to_2_team_info_list = zone_info.winner_4_to_2_team_info_list
	local info_list = {}
	for k1,v1 in pairs(winner_4_to_2_team_info_list) do
		local info = self:GetKfGuildWarBaseGuildInfo(cur_zone,v1.guild_id,v1.plat_type)
		info_list[k1] = info
	end
	return info_list
end

function GuildWGData:GetKfGuildWarBaseGuildInfo(cur_zone,guild_id,plat_type)
	local zone_info = self:GetKfGuildWarZoneInfo(cur_zone)
	if zone_info.pk_guild_list then
		for k1,v1 in pairs(zone_info.pk_guild_list) do
			if v1.guild_id == guild_id and v1.plat_type == plat_type then
				return v1
			end
		end
	end
end

--第一次进入仙盟的刷新
function GuildWGData:SetEnterGuildFlushFlag(flag)
	self.enter_guild_flush_falg = flag
end

function GuildWGData:GetEnterGuildFlushFlag()
	return self.enter_guild_flush_falg 
end

--盟主、副盟主、长老才有权利审批入盟
function GuildWGData:IsMemberHasPowerToDealApply(guild_post)
	return guild_post == GUILD_POST.FU_TUANGZHANG 
		or guild_post == GUILD_POST.TUANGZHANG
		or guild_post == GUILD_POST.ZHANG_LAO
		or guild_post == GUILD_POST.JiaMengZhu
end

--更新仙盟名称
function GuildWGData:GuildSetAttr(key, value)
	local guildvo = GuildDataConst.GUILDVO
	if nil ~= guildvo[key] and nil ~= value then
		guildvo[key] = value
	end
end

function GuildWGData:GetJoinGuildTimeLimit()
	local cd_time = ConfigManager.Instance:GetAutoConfig("guildconfig_auto").other_config[1].guild_cd_time
	return TimeUtil.FormatSecondDHM7(cd_time)
end

----[[ 假仙盟
function GuildWGData:SetFakeGuildInfo(protocol)
	self.fake_guild_list = protocol.fake_guild_list
    self.fake_guild_member_online_flag = protocol.fake_guild_member_online_flag
    self.fake_guild_member_list = protocol.fake_guild_member_list
end

function GuildWGData:GetFakeGuildList()
	return self.fake_guild_list or {}
end

function GuildWGData:GetFakeGuildBaseCfg(seq)
	return self.fake_guild_base_cfg[seq]
end

function GuildWGData:GetFakeGuildInfoCfg(seq)
	return self.fake_guild_info_cfg[seq]
end

function GuildWGData:GetFakeGuildMemberCfg(seq)
	return self.fake_guild_member_cfg[seq]
end

-- 获取掺假的仙盟列表
function GuildWGData:GetFakeGuildShowList()
	-- 真
	local guild_list = {}
	local max_create_time = 0
	local max_mengzhu_fame = 1
	local real_list = self:GetGuildListData()
	for k,v in ipairs(real_list) do
		if v.create_time > max_create_time then
			max_create_time = v.create_time
		end

		if v.mengzhu_fame > max_mengzhu_fame then
			max_mengzhu_fame = v.mengzhu_fame
		end

		table.insert(guild_list, v)
	end

	-- 假
	local fake_list = self:GetFakeGuildList()
	for k,v in ipairs(fake_list) do
		fake_list[k].create_time = max_create_time
		fake_list[k].mengzhu_fame = max_mengzhu_fame
		table.insert(guild_list, v)
	end

	if not IsEmptyTable(guild_list) then
		table.sort(guild_list, function(a, b)
			if a.guild_all_capability > b.guild_all_capability then
				return true
			elseif a.guild_all_capability == b.guild_all_capability then
				return a.create_time < b.create_time
			else
				return false
			end
		end)
	end

	return guild_list
end

function GuildWGData:GetFakeGuildMemberShowList(guild_seq)
	local online_flag = (self.fake_guild_member_online_flag or {})[guild_seq]
    local member_list = (self.fake_guild_member_list or {})[guild_seq]
	if online_flag == nil or member_list == nil then
		return {}
	end

	local show_list = {}
	for k,v in ipairs(member_list) do
		local member_cfg = self:GetFakeGuildMemberCfg(v.seq)
		if member_cfg then
			local data = {
				uid = 0,
				role_name = member_cfg.name,
				capability = 0,
				post = k == 1 and GUILD_POST.TUANGZHANG or GUILD_POST.CHENG_YUAN,
				is_online = online_flag[k] or 0,
				level = v.level,
			}
			table.insert(show_list, data)
		end
	end

	return show_list
end

function GuildWGData:GetIsShieldFakeGuild()
	return GLOBAL_CONFIG.param_list.shield_fake_guild == 1
end

function GuildWGData:GetIsShieldSeeGuildMember()
	return GLOBAL_CONFIG.param_list.shield_see_guild_member == 1
end
--]]

-- 获取仙盟守护失败奖励
function GuildWGData:GetGuildSHLoseList()
	local other_cfg = self:GetGuildSHOtherCfg()
	local lose_reward_list = other_cfg.fail_reward_item
	return lose_reward_list
end

--获取仙盟排行结算奖励
function GuildWGData:GetGuildSHWinList()
	local rank_reward_cfg = self.guild_rank_reawrd_cfg
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if self.guild_fb_data then
		local rank_pos = self.guild_sh_rank_pos
		for k,v in pairs(rank_reward_cfg) do
			if cur_day >= v.opengame_day_min and cur_day <= v.opengame_day and rank_pos >= v.rank_min and rank_pos <= v.rank_max then
				return v.rank_reward_item
			end
		end
	end
	return {}
end

------------------------------仙盟新功能优化部分------------------------------

--仙盟签到信息
function GuildWGData:SetGuildSignInfo(protocol)
	self.sign_num = protocol.sign_num							-- 签到人数
	self.sign_redpaper_flag = protocol.sign_redpaper_flag		-- 签到红包发放标记
end

--仙盟签到 个人信息
function GuildWGData:SetGuildRoleSignInfo(protocol)
	self.sign_flag = protocol.sign_flag							-- 签到标记
end

--仙盟砍价
function GuildWGData:SetGuildGiftBargainInfo(protocol)
	self.gift_bargain_times = protocol.gift_bargain_times		-- 砍价次数
	self.gift_bargain_value = protocol.gift_bargain_value		-- 已砍价格
end

--仙盟商店
function GuildWGData:SetGuildRoleShopInfo(protocol)
	self.guild_coin = protocol.guild_coin						-- 仙盟货币
	self.gift_bargain_flag = protocol.gift_bargain_flag			-- 砍价标记
	self.gift_buy_flag = protocol.gift_buy_flag					-- 砍价礼包购买标记
	self.shop_buy_list = protocol.shop_buy_list					-- 商店购买列表
end

--仙盟成员砍价信息更新
function GuildWGData:UpdateGuildMemberGiftStatus(protocol)
	local item = self:GetGuildMemberRole(protocol.uid)
	if item then
		item.gift_bargain_flag = protocol.gift_bargain_flag
		item.gift_buy_flag = protocol.gift_buy_flag
	end
end

--仙盟成员砍价信息更新
function GuildWGData:SetGuildMemberGiftBargainInfo(protocol)
	self.bargain_record_list = protocol.item_list
	table.sort(self.bargain_record_list, SortTools.KeyUpperSorter("time"))
end

--仙盟成员砍价信息更新-单条
function GuildWGData:SetGuildMemberGiftBargainUpdate(protocol)
	if not self.bargain_record_list then
		self.bargain_record_list = {}
	end
	table.insert(self.bargain_record_list, 1, protocol.item)
end

-------------------------------------

-- 砍价礼包配置
function GuildWGData:GetCurBargainGiftCfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for i, v in ipairs(self.bargain_gift_cfg) do
		if open_day >= v.min_open_day and open_day <= v.max_open_day then
			return v
		end
	end
	return nil
end

-- 仙盟签到红包配置
function GuildWGData:GetSignRedPaperCfg()
	return self.sign_redpaper_cfg
end

-- 仙盟签到红包配置
function GuildWGData:GetSignRewardList()
	if not self.sign_reward_show_list then
		self.sign_reward_show_list = {}
		local must_reward = self.guild_other_config.sign_reward_item
		local random_reward = self.guild_other_config.random_sign_reward_item
		if must_reward and random_reward then
			for i = 0, #must_reward do
				local data = {}
				data.item_id = must_reward[i].item_id
				data.is_bind = must_reward[i].is_bind
				data.num = must_reward[i].num
				data.drop_type = 1
				table.insert(self.sign_reward_show_list, data)
			end
			for i = 0, #random_reward do
				local data = {}
				data.item_id = random_reward[i].item_id
				data.is_bind = random_reward[i].is_bind
				data.num = random_reward[i].num
				data.drop_type = 2
				table.insert(self.sign_reward_show_list, data)
			end
		end
	end
	return self.sign_reward_show_list
end

-- 仙盟签到人数
function GuildWGData:GetGuildSignNum()
	return self.sign_num or 0
end

-- 仙盟红包发放状态
function GuildWGData:GetGuildSignRedPaperFlag(seq)
	return (self.sign_redpaper_flag or {})[seq] or 0
end

-- 仙盟签到
function GuildWGData:GetGuildSignFlag()
	return self.sign_flag or 0
end

-- 仙盟币数量
function GuildWGData:GetGuildCoin()
	return self.guild_coin or 0
end

-- 商品购买次数
function GuildWGData:GetGuildShopBuyCount(seq)
	return (self.shop_buy_list or {})[seq] or 0
end

-- 仙盟商店数据
function GuildWGData:GetGuildShopList()
	local shop_list = {}
	for k, cfg in pairs(self.guild_shop_cfg) do
		local data = {}
		data.cfg = cfg
		data.seq = cfg.seq
		data.price = cfg.price
		data.reset_type = cfg.reset_type
		data.buy_times = cfg.buy_times
		data.guild_level_limit = cfg.guild_level_limit
		data.reward_item = cfg.reward_item

		local buy_count = self:GetGuildShopBuyCount(cfg.seq)
		data.buy_count = buy_count
		data.can_buy_time = cfg.buy_times - buy_count
		data.is_sold_out = buy_count >= cfg.buy_times
		data.sort  = (data.is_sold_out and 10000 or 0) + cfg.seq
		table.insert(shop_list, data)
	end
	table.sort(shop_list, SortTools.KeyLowerSorter("sort"))
	return shop_list
end

-- 礼包已砍金额
function GuildWGData:GetGuildGiftBargainValue()
	return self.gift_bargain_value or 0
end

-- 砍价次数
function GuildWGData:GetGuildBargainCount()
	local m_list = GuildDataConst.GUILD_MEMBER_LIST
	local bargain_count = 0
	for i, v in ipairs(GuildDataConst.GUILD_MEMBER_LIST.list) do
		if v.gift_bargain_flag == 1 then
			bargain_count = bargain_count + 1
		end
	end
	return bargain_count, GuildDataConst.GUILD_MEMBER_LIST.count
end

-- 砍价记录
function GuildWGData:GetGuildGiftBargainRecordList()
	return self.bargain_record_list or {}
end

function GuildWGData:GetMyBargainFlag()
	return self.gift_bargain_flag
end

function GuildWGData:GetBargainGiftBuyFlag()
	return self.gift_buy_flag
end