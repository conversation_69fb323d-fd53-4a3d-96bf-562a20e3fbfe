﻿using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

using UnityEngine.Rendering;

public class YYFullScreenEffectClouds : YYFullScreenEffect
{
    protected override WeatherCommandBuffer CreateCommandBuffer(Camera camera, YYTemporalReprojectionState reprojState, WeatherDownsampleScale downsampleScale, Action<WeatherCommandBuffer> preSetupCommandBuffer)
    {
        if (YYWeather.Instance == null)
        {
            Debug.Log("Cannot create command buffer, WeatherScript.Instance is null");
            return null;
        }

        WeatherCommandBuffer weatherCommandBuffer = GetOrCreateWeatherMakerCommandBuffer(camera);

        if (weatherCommandBuffer != null && weatherCommandBuffer.CommandBuffer != null)
        {
            camera.RemoveCommandBuffer(RenderQueue, weatherCommandBuffer.CommandBuffer);
            weatherCommandBuffer.CommandBuffer.Clear();
        }

        RenderTextureFormat defaultFormat = (camera.allowHDR ? RenderTextureFormat.DefaultHDR : RenderTextureFormat.Default);
        CommandBuffer commandBuffer = weatherCommandBuffer.CommandBuffer;
        int postSourceId = -1;
        RenderTargetIdentifier postSource = postSourceId;
        WeatherCameraType cameraType = weatherCommandBuffer.CameraType;


        int scale = (int)downsampleScale;

        commandBuffer.SetGlobalFloat(WMS._WeatherDownsampleScale, scale);


        /////绘制天上的 物体
        if (preSetupCommandBuffer != null)
        {
            preSetupCommandBuffer.Invoke(weatherCommandBuffer);
        }
        ///////
       

        ////////


        weatherCommandBuffer.Material = clonedMaterial;
        weatherCommandBuffer.ReprojectionState = reprojState;
        weatherCommandBuffer.RenderQueue = RenderQueue;

        ///开始
        camera.AddCommandBuffer(RenderQueue, commandBuffer);

        // 维护一个
        if (YYCommandBufferManager.Instance != null)
        {
            YYCommandBufferManager.Instance.AddCommandBuffer(weatherCommandBuffer);
        }


        return weatherCommandBuffer;

        //return base.CreateCommandBuffer(camera, reprojState, downsampleScale, preSetupCommandBuffer);
    }
}
