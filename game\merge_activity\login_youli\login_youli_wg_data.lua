LoginYouLiWGData = LoginYouLiWGData or BaseClass()

function LoginYouLiWGData:__init()
	if LoginYouLiWGData.Instance then 
		ErrorLog("[LoginYouLiWGData] Attemp to create a singleton twice !")
	end

	LoginYouLiWGData.Instance = self

	self.logindaydata = {}
    MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LOGIN_GIFT, {[1] = MERGE_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowLoginRedPoint, self))
    RemindManager.Instance:Register(RemindName.MergeLoginRward, BindTool.Bind(self.IsShowLoginRedPoint, self))

    self:LoadConfig()
end

function LoginYouLiWGData:__delete()
	LoginYouLiWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.MergeLoginRward)
end

function LoginYouLiWGData:LoadConfig()
	self.login_auto = ConfigManager.Instance:GetAutoConfig("combineserve_activity_new_login_reward_auto")
    self.login_reward_cfg = ListToMapList(self.login_auto.login_reward, "grade")
	self.login_activity_cfg = ListToMap(self.login_auto.activity_param, "grade")
end

function LoginYouLiWGData:SetLoginRewardInfo(protocol)
	self.logindaydata.day_list = {}
    self.logindaydata.login_day_num = protocol.login_day_num
    self.logindaydata.grade = protocol.grade
	self.reward_state = protocol.reward_state
end

function LoginYouLiWGData:GetCurGrade()
	return self.logindaydata.grade or -1
end

function LoginYouLiWGData:GetLoginNum()
	return self.logindaydata.login_day_num or 0
end

function LoginYouLiWGData:GetCommonRewardState(login_day)
	return ((self.reward_state or {})[login_day] or {})["common_gift_state"]
end

function LoginYouLiWGData:GetSpecialRewardState(login_day)
	return ((self.reward_state or {})[login_day] or {})["special_gift_state"]
end

function LoginYouLiWGData:GetActCanOpen()
	local grade = self:GetCurGrade()
	local cfg = self.login_activity_cfg[grade]
	if cfg then
		local vo = GameVoManager.Instance:GetMainRoleVo()
		if vo.level >= cfg.open_role_level then
			return true
		end
	end

	return false
end

function LoginYouLiWGData:GetGradeRewardList()
	local grade = self:GetCurGrade()
	return self.login_reward_cfg[grade] or {}
end

function LoginYouLiWGData:GetGradeRewardListByDay(day)
	local grade = self:GetCurGrade()
	return (self.login_reward_cfg[grade] or {})[day] or {}
end

function LoginYouLiWGData:GetLoginRedPointDay()
	if not MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LOGIN_GIFT) then
		return 0
	end

	local reward_list = self:GetGradeRewardList()
	if IsEmptyTable(reward_list) then
		return 0
	end

	local common_state, special_state
	for k, v in ipairs(reward_list) do
		common_state = self:GetCommonRewardState(v.day_index)
		if common_state == LoginYouLiRewardState.KLQ then
			return v.day_index
		end

		return 0
	end
end

function LoginYouLiWGData:IsShowLoginRedPoint()
	if not MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LOGIN_GIFT) then
		return 0
	end
	
	local reward_list = self:GetGradeRewardList()
	if IsEmptyTable(reward_list) then
		return 0
	end

	local common_state, special_state
	-- local login_day = self:GetLoginNum()
	-- local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
	for k,v in ipairs(reward_list) do
		common_state = self:GetCommonRewardState(v.day_index)
		if common_state == LoginYouLiRewardState.KLQ then
			return 1
		end

		-- special_state = self:GetSpecialRewardState(v.day_index)
		-- if special_state == LoginYouLiRewardState.KLQ then
		-- 	return 1
		-- elseif special_state == LoginYouLiRewardState.BKL then
		-- 	if v.day_index <= login_day then
		-- 		if v.vip_lv ~= "" and v.vip_lv > 0 and cur_vip_level >= v.vip_lv then
		-- 			return 1
		-- 		end
		-- 	end
		-- end
	end

	return 0
end

function LoginYouLiWGData:GetActivityEndTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_LOGIN_GIFT)
	if activity_info ~= nil then
		return activity_info.end_time
	end
	return  0
end

--获取活动提示
function LoginYouLiWGData:GetActivityTip()
	return Language.MergeActivity.TipsActivityHint, Language.MergeActivity.TipsActivityHintShow
end