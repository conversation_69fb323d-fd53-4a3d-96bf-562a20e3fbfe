FestivalActivityWGData = FestivalActivityWGData or BaseClass()

function FestivalActivityWGData:__init()
	if FestivalActivityWGData.Instance then
		ErrorLog("[FestivalActivityWGData] Attemp to create a singleton twice !")
	end

	FestivalActivityWGData.Instance = self

    self.festival_activity_config = ConfigManager.Instance:GetAutoConfig("festival_activity_config_auto").festival_activity_dec
    self.festival_activity_list = ListToMap(self.festival_activity_config, "activity_type")
    self.festival_other_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_config_auto").other[1]

	self.activity_open_callback = {} --运用活动开启回调
	self.alearday_activity_open = {} -- 已经开启的运营活动（仅限于服务器做的通知，不包括客户端的自身拦截）
	self.can_activity_open = {} -- 已经开启的运营活动（真正意义上的开启所有条件都满足）
	self.rember_activity_event_type = {} --记录的监听事件类型
	self.remind_callback = {} --红点回调方法判断
	self.index_activity_cfg = {}--索引当做键值，活动号是value
	self.need_monitor_item = {}--需要被监听的物品
	self.btn_one_remind = {}--按钮1的红点状态
	self.new_flag_callback = {}

	self:SetTabIndex()

	RemindManager.Instance:Register(RemindName.Festival_Activity, BindTool.Bind(self.IsShowMainViewRedPoint, self))

	--角色等级改变
	self.role_level_change = GlobalEventSystem:Bind(OtherEventType.ROLE_LEVEL_UP, BindTool.Bind(self.RoleLevelChange, self))

	--背包物品改变监听
	-- self.item_data_event = BindTool.Bind1(self.BagItemDataChange, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)

	--人物信息下发监听打开（打开后对所有活动进行一次开启判断，因为有些开启参数在活动协议后下发导致判断的开启不准确所以游戏完全登陆后在全面进行一次判断）
	--这个监听也许需要根据实际情况考究
	self.main_role_info = GlobalEventSystem:Bind(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind(self.OnLoadingComplete, self, MERGE_EVENT_TYPE.LEVEL))
	--背包物品首次下发
	self.bag_info = GlobalEventSystem:Bind(OtherEventType.BAG_FIRST_INFO, BindTool.Bind(self.OnLoadingComplete, self, MERGE_EVENT_TYPE.ITEM))

	self.btn_one_remind_change = BindTool.Bind(self.BtnOneRemindChangeCallBack, self)


	self:SetRemindEventList()
end

function FestivalActivityWGData:__delete()
	FestivalActivityWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.Festival_Activity)

	self.activity_open_callback = nil
	self.alearday_activity_open = nil
	self.can_activity_open = nil
	self.rember_activity_event_type = nil
	self.remind_callback = nil
	self.index_activity_cfg = nil
	self.need_monitor_item = nil
	self.new_flag_callback = nil

	if self.role_level_change then
		GlobalEventSystem:UnBind(self.role_level_change)
		self.role_level_change = nil
	end

	-- if self.item_data_event then
	-- 	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	-- 	self.item_data_event = nil
	-- end

	if self.main_role_info then
		GlobalEventSystem:UnBind(self.main_role_info)
		self.main_role_info = nil
	end

	if self.bag_info then
		GlobalEventSystem:UnBind(self.bag_info)
		self.bag_info = nil
	end

	RemindManager.Instance:UnBind(self.btn_one_remind_change)

end

function FestivalActivityWGData:IsActivityLastDay(tab_index)
    local act_type = self:GetCurSelectActivityType(tab_index)
	local end_time = self:GetActivityInValidTime(act_type)
	local end_date = os.date("*t", end_time)
    local cur_date = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	if cur_date.month == end_date.month and cur_date.day == (end_date.day -1) then --因为是第二天的0点结束
		return true
	end
	return false
end

----------------------------------------------------------

--设置界面信息
function FestivalActivityWGData:SetTabIndex()
	self.tab_index_name = {}
	self.remind_tab = {}

	local activity_cfg = self.festival_activity_config
	table.sort(activity_cfg, SortTools.KeyLowerSorter("rank_id"))

	for k,v in ipairs(activity_cfg) do
		local tab_index = v.rank_id * 10
        --定义格子界面tabindex
        --festival_activity_活动号
        TabIndex["festival_activity_" .. v.activity_type] = tab_index

		table.insert( self.tab_index_name, v.active_name)
		self.index_activity_cfg[tab_index] = v.activity_type

		--红点收集(就算不需要红点也要占一个坑位)
		local tab = {}
		if nil ~= v.remind_name or "" ~= v.remind_name then
			table.insert( tab, v.remind_name)
		end
		table.insert( self.remind_tab, tab)
	end
end

function FestivalActivityWGData:GetOperationViewInfo()
	return self.tab_index_name or {}, self.remind_tab or {}
end

--根据活动号读取活动名字
function FestivalActivityWGData:GetActivityNameByActivityNum(activity_type)
	if nil == activity_type then
		return
	end

	local cfg = self.festival_activity_list[activity_type]
	if cfg then
		return cfg.active_name
	end
end

--获取活动是否开启
function FestivalActivityWGData:GetActivityState(activity_type)
	return self.can_activity_open[activity_type]
end

--获取活动的结束时间
function FestivalActivityWGData:GetActivityInValidTime(activity_type)
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(activity_type)
	if activity_data ~= nil then
		return activity_data.end_time, activity_data.start_time
	end
	return 0, 0
end

--获取第一个开启的功能(如果有红点红点优先)
function FestivalActivityWGData:GetOneOpenTabIndex()
	local activity_cfg = self.festival_activity_config
	table.sort(activity_cfg, SortTools.KeyLowerSorter("rank_id"))

	local first_index = 0
	for k,v in ipairs(activity_cfg) do
		if self:GetActivityState(v.activity_type) then
			if 0 == first_index then
				first_index = v.rank_id * 10
			end

			if self.remind_callback[v.activity_type] and 1 == self.remind_callback[v.activity_type]() then
				return v.rank_id * 10
			end	
		end
	end

	return first_index
end

--收集各个活动的开启条件(event_type,是个表，考虑到多种条件，但是NONE一定不能与其他条件同时出现)
function FestivalActivityWGData:SetActivityOpenCallBack(activity, event_type, call_back, remind_callback)
	self.rember_activity_event_type[activity] = event_type
	self.remind_callback[activity] = remind_callback

	--类型0不需要做记录
	for k,v in pairs(event_type) do
		if v == MERGE_EVENT_TYPE.NONE then
			return
		end
	end

	for k,v in pairs(event_type) do
		if nil == self.activity_open_callback[v]then
			self.activity_open_callback[v] = {}
		end

		self.activity_open_callback[v][activity] = call_back
	end
end

--人物角色监听
function FestivalActivityWGData:RoleLevelChange()
	local alearday_activity_open = self.alearday_activity_open
	local type = MERGE_EVENT_TYPE.LEVEL --监听类型

	if alearday_activity_open and self.can_activity_open then
		for k,v in pairs(alearday_activity_open) do
			--活动已经开启了还在监听移除掉
			if self.can_activity_open[k] then
				self.alearday_activity_open[k] = nil

			--属于等级监听并且开启记录活动状态开启
			elseif self.activity_open_callback[type] and self.activity_open_callback[type][k] and self.activity_open_callback[type][k]()then
				self.alearday_activity_open[k] = nil
				self.can_activity_open[k] = true
				self:CreatMainViewBtn()
				self:SetRemindHint(k)
			end
		end
	end
end

--背包物品发生改变
function FestivalActivityWGData:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if not self.need_monitor_item[change_item_id] then
		return
	end

	local alearday_activity_open = self.alearday_activity_open
	local type = MERGE_EVENT_TYPE.ITEM --监听类型

	if alearday_activity_open and self.can_activity_open then
		for k,v in pairs(alearday_activity_open) do
			--活动已经开启了还在监听移除掉
			if self.can_activity_open[k] then
				self.alearday_activity_open[k] = nil

			--属于等级监听并且开启记录活动状态开启
			elseif self.activity_open_callback[type] and self.activity_open_callback[type][k] and self.activity_open_callback[type][k]() then
				self.alearday_activity_open[k] = nil
				self.can_activity_open[k] = true
				self:CreatMainViewBtn()
				self:SetRemindHint(k)
			end
		end
	end
end

--设置已经开启的活动并且需要做监听(只是服务器的一个状态不包含客户端的判断)
function FestivalActivityWGData:SetAleardayOpenActivity(activity, status)
	--服务器开启客户端还未开，记录做监听
	if status == ACTIVITY_STATUS.OPEN and not self.can_activity_open[activity] then
		--这一步是为了防止活动已经开了但还没有做记录
		local event_type_list = self.rember_activity_event_type[activity]
        local event_type = event_type_list and event_type_list[1] --取一个回调就可以

		if event_type and self.activity_open_callback[event_type] and self.activity_open_callback[event_type][activity]
			and self.activity_open_callback[event_type][activity]() then
			self.can_activity_open[activity] = true
			self:CreatMainViewBtn()
			self:SetRemindHint(activity)
			return
		--类型0只受服务的控制
		elseif event_type and event_type == MERGE_EVENT_TYPE.NONE then
			self.can_activity_open[activity] = true
			self:CreatMainViewBtn()
			self:SetRemindHint(activity)
			return
		end
		self.alearday_activity_open[activity] = true

	--两端都开的是时候记录活动真实的状态
	elseif status == ACTIVITY_STATUS.OPEN and self.can_activity_open[activity] then
			self.can_activity_open[activity] = true
			self:CreatMainViewBtn()
			self:SetRemindHint(activity)

	--不管那种情况只要活动关闭那就直接关闭
	elseif status == ACTIVITY_STATUS.CLOSE then
		self.alearday_activity_open[activity] = nil
		self.can_activity_open[activity] = nil
		FestivalActivityWGCtrl.Instance:ChangeSelectIndex()	
		self:SetActivityCloseRemindHint(activity)
	end
end

--获取已经开启的活动列表
function FestivalActivityWGData:GetOpenActivityList()
	return self.can_activity_open
end

--获取当前索引对应的活动号
function FestivalActivityWGData:GetCurSelectActivityType(index)
	if nil == index then
		return 0
	end
	return self.index_activity_cfg[index] or 0
end

--当功能开启的时候需要触发一次红点（断线时有时活动协议下发比活动开启协议还要早）
function FestivalActivityWGData:SetRemindHint(activity_type)
	if nil == activity_type then
		return
	end

	local cfg = self.festival_activity_list[activity_type]
	if cfg and cfg.remind_name and "" ~= cfg.remind_name then
		RemindManager.Instance:Fire(cfg.remind_name)
	end
end

--当功能关闭的的时候需要触发一次红点用来关闭主界面的红底那
function FestivalActivityWGData:SetActivityCloseRemindHint(activity_type)
	if nil == activity_type then
		return
	end

	local cfg = self.festival_activity_list[activity_type]
	if cfg and cfg.remind_name and "" ~= cfg.remind_name then
		self:BtnOneRemindChangeCallBack(cfg.remind_name, 0)
	end
end


------------------------- 各活动红点数据-------------------------

--主界面红点提示按钮1
function FestivalActivityWGData:BtnOneRemindChangeCallBack(remind_name, num)
	if self.btn_one_remind[remind_name] then
		self.btn_one_remind[remind_name] = num
	end

	RemindManager.Instance:Fire(RemindName.Festival_Activity)
end

--主界面红点提示按钮1
function FestivalActivityWGData:IsShowMainViewRedPoint()
	
	for k,v in pairs(self.btn_one_remind) do
		if v > 0 then
			return 1
		end
	end

	return 0
end


--活动开启开服天数
function FestivalActivityWGData:GetOpenDayByAct(act_type)
	local day = 0
	if act_type == nil then
		return day
	end
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_info == nil then
		return day
	end

	if act_info.status == ACTIVITY_STATUS.CLOSE then
		return day
	end

	local day_value = 60 * 60 * 24
	local open_start_time = TimeWGCtrl.Instance:GetServerRealStartTime()
	local time_tab = os.date("*t", open_start_time)
	local open_time = open_start_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec
	day = math.floor((act_info.start_time - open_time) / day_value) + 1
	return day
end

--主界面加载完堆当前收集的开启信息做开启判断
function FestivalActivityWGData:OnLoadingComplete(event_type)
	local alearday_activity_open = self.alearday_activity_open

	if alearday_activity_open and self.can_activity_open then
		for k,v in pairs(alearday_activity_open) do
			--活动已经开启了还在监听移除掉
			if self.can_activity_open[k] then
				self.alearday_activity_open[k] = nil
			--属于等级监听并且开启记录活动状态开启
			else
				local is_can_open = self:GetCurActivityOpenEvent(event_type, k)
				if is_can_open then
					self.alearday_activity_open[k] = nil
					self.can_activity_open[k] = true
					ActivityWGData.Instance:CreakMainViewFestivalActivityBtn()
					self:SetRemindHint(k)
				end
			end
		end
	end
end

--根据监听事件来判断当前活动是否开启
function FestivalActivityWGData:GetCurActivityOpenEvent(event_type, activity_type)
	if self.activity_open_callback[event_type] and self.activity_open_callback[event_type][activity_type] and self.activity_open_callback[event_type][activity_type]() then
		return true
	end

	return false
end

--自己活动中需要被监听的物品列表（当做功能开启依据的物品）
function FestivalActivityWGData:SetNeedBeMonitorItemList(item_list)
	if not item_list then
		return
	end

	for k,v in pairs(item_list) do
		self.need_monitor_item[v.item_id] = true
	end
end

--对监听的红点进行监听
function FestivalActivityWGData:SetRemindEventList()
	local activity_cfg = self.festival_activity_config
	table.sort(activity_cfg, SortTools.KeyLowerSorter("rank_id"))

	for k,v in pairs(activity_cfg) do
		if v.remind_name and "" ~= v.remind_name then
			self.btn_one_remind[v.remind_name] = 0
		end
	end

	for k,v in pairs(self.btn_one_remind) do
		RemindManager.Instance:Bind(self.btn_one_remind_change, k)
	end
end

--获取活动是否存在已开启
function FestivalActivityWGData:GetActivityIsClose()
	if self.can_activity_open then
		for k,v in pairs(self.can_activity_open) do
			if v then
				return false
			end
		end
	end

	return true
end

--获取活动当前合服第几天
function FestivalActivityWGData:GetMergeDayInfo()
	local day = 0
	local day_value = 60 * 60 * 24
	
	local server_real_combine_time = TimeWGCtrl.Instance:GetServerRealCombineTime()
	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local time_tab = os.date("*t", server_real_combine_time)
	local open_time = server_real_combine_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec
	day = math.floor((cur_time - open_time) / day_value) + 1
	return day
end

--获取活动当前合服 - 母服开服天数
function FestivalActivityWGData:GetMergeDayFromOpenDay()
	local day = 0
	local day_value = 60 * 60 * 24
	local server_real_combine_time = TimeWGCtrl.Instance:GetServerRealCombineTime()
	local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local time_tab = os.date("*t", server_real_combine_time)
    local cur_time = TimeWGCtrl.Instance:GetServerTime()
	local open_time = server_real_combine_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec
	day = math.floor((cur_time - open_time) / day_value) + open_server_day
	return day
end

--获取活动配置
function FestivalActivityWGData:GetFestivalActivityCfg(activity_type)
	local cfg = self.festival_activity_list[activity_type]
	return cfg
end


function FestivalActivityWGData:GetFestivalActivityAllCfg()
	return self.festival_activity_config or {}
end

function FestivalActivityWGData:GetActivityIsEvent(activity_type)
	if self.alearday_activity_open[activity_type] and not self.can_activity_open[activity_type] then
		self:SetAleardayOpenActivity(activity_type, ACTIVITY_STATUS.OPEN)
	end
end

--活动可以开启的天数
function FestivalActivityWGData:GetActCanOpenDay(act_type)
	local day = 0
	if act_type == nil then
		return day
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_info == nil then
		return day
	end

	if act_info.status == ACTIVITY_STATUS.CLOSE then
		return day
	end

	local day_value = 60 * 60 * 24
	local open_time_tab = os.date("*t", act_info.start_time)
	local open_time = act_info.start_time - open_time_tab.hour * 3600 - open_time_tab.min * 60 - open_time_tab.sec
	local end_time_tab = os.date("*t", act_info.end_time)
	local end_time = act_info.end_time - end_time_tab.hour * 3600 - end_time_tab.min * 60 - end_time_tab.sec

	day = math.floor((end_time - open_time) / day_value) + 1
	return day
end

--活动已经开启的时间
function FestivalActivityWGData:GetActOpenDay(act_type)
	local day = 0
	if act_type == nil then
		return day
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_info == nil then
		return day
	end

	if act_info.status == ACTIVITY_STATUS.CLOSE then
		return day
	end

	local day_value = 60 * 60 * 24
	local time_tab = os.date("*t", act_info.start_time)
	local open_time = act_info.start_time - time_tab.hour * 3600 - time_tab.min * 60 - time_tab.sec
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	day = math.floor((server_time - open_time) / day_value) + 1

	return day
end

function FestivalActivityWGData:CreatMainViewBtn()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.FESTIVA_ACTIVITY) then
		ActivityWGData.Instance:CreakMainViewFestivalActivityBtn()
	end
end

function FestivalActivityWGData:SetNewFalgCallBack(act_type, callback)
	if not self.new_flag_callback[act_type] then
		self.new_flag_callback[act_type] = callback
	end
end

function FestivalActivityWGData:GetIsShowMainBtnNewTxt()
	local is_show_new_flag = 0

	if not self.new_flag_callback or IsEmptyTable(self.new_flag_callback) then
		return is_show_new_flag
	end

	for act_type, call_back in pairs(self.new_flag_callback) do
		local is_show = call_back()
		if is_show then
			is_show_new_flag = 1
			return is_show_new_flag, act_type
		end
	end

	return is_show_new_flag
end

function FestivalActivityWGData:GetIsShowNewFalgByActType(act_type)
	if self.new_flag_callback[act_type] then
		return self.new_flag_callback[act_type]()
	end

	return false
end

function FestivalActivityWGData:GetCommonColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.common_color
	return color --"#6c342c"--"#B5BEDDFF"
end

--倒计时文字颜色
function FestivalActivityWGData:GetCountDownColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.count_down_color
	return color
end

function FestivalActivityWGData:GetOtherCfg()
    local other_cfg = self.festival_other_cfg
	return other_cfg
end

-- 消费排名颜色
function FestivalActivityWGData:GetRankTextColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.rank_text_color
	return color
end

--倒计时数字颜色
function FestivalActivityWGData:GetCommonGreenColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.green_color
	return color
end

--界面小标签按钮颜色
function FestivalActivityWGData:GetCommonTabTextColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.tab_text_color
	return color
end

--界面小标签按钮颜色高亮
function FestivalActivityWGData:GetCommonTabTextColorHL()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.tab_text_color_hl
	return color
end

-- 转盘描述字色
function FestivalActivityWGData:GeTurnTableTextColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.turntable_text_color
	return color
end

-- 探宝字色
function FestivalActivityWGData:GetChuMiddleTextColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.middle_text_color
	return color
end

-- 秒杀字色
function FestivalActivityWGData:GetMiaoShaTextColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.miaosha_text_color
	return color
end

-- 秒杀折扣字色
function FestivalActivityWGData:GetDiscountColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.discount_color
	return color
end

-- 达人字色
function FestivalActivityWGData:GetHuntNameColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.hunt_name_color
	return color
end

-- 首创字色
function FestivalActivityWGData:GetHuntInitiateColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.hunt_initiate_color
	return color
end

--通用倒计时位置
function FestivalActivityWGData:GetCountPosition()
	local other_cfg = self.festival_other_cfg
	local pos_x, pos_y = 0, 0
    if other_cfg.count_position and other_cfg.count_position ~= "" then
		local pos_list = string.split(other_cfg.count_position, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	return pos_x, pos_y
end

-- 探宝列表位置
function FestivalActivityWGData:GetHuntPosition()
	local other_cfg = self.festival_other_cfg
	local pos_x, pos_y = 0, 0
    if other_cfg.hunt_position and other_cfg.hunt_position ~= "" then
		local pos_list = string.split(other_cfg.hunt_position, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	return pos_x, pos_y
end

-- 探宝文本渐变
function FestivalActivityWGData:GetHuntGradient()
	local other_cfg = self.festival_other_cfg
	local gradient1, gradient2 = "#FFE157", "#FFFCDB"
	if other_cfg.hunt_gradient and other_cfg.hunt_gradient ~= "" then
		local gradient_list = string.split(other_cfg.hunt_gradient, "|")
		gradient1 = gradient_list[1] or gradient1
		gradient2 = gradient_list[2] or gradient2
	end

	return gradient1, gradient2
end

-- 探宝文本位置
function FestivalActivityWGData:GetHuntTextPos()
	local other_cfg = self.festival_other_cfg
	local pos_x, pos_y = 0, 0
    if other_cfg.hunt_text_pos and other_cfg.hunt_text_pos ~= "" then
		local pos_list = string.split(other_cfg.hunt_text_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	return pos_x, pos_y
end

-- 探宝文本大小
function FestivalActivityWGData:GetHuntTextSize()
    local other_cfg = self.festival_other_cfg
	local size = other_cfg.hunt_text_size
	return size
end

-- 探宝文本描边
function FestivalActivityWGData:GetHuntOutColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.hunt_text_outline
	return color
end

-- 开始探宝字色
function FestivalActivityWGData:GetStartHuntColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.start_hunt_color
	return color
end

-- 探宝时间字色
function FestivalActivityWGData:GetHuntTimeColor()
    local other_cfg = self.festival_other_cfg
	local color = other_cfg.hunt_time_color
	return color
end

function FestivalActivityWGData.GetResPath()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_config_auto").other[1]
	local res_path = other_cfg.res_path
	return res_path
end

--活动开启时间
function FestivalActivityWGData:GetOpenTimeByAct(act_type)
	local time = 0
	if act_type == nil then
		return time
	end
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(act_type)
	if act_info == nil then
		return time
	end

	if act_info.status == ACTIVITY_STATUS.CLOSE then
		return time
	end
    time = act_info.start_time
	return time
end