NewComposeView = NewComposeView or BaseClass(SafeBaseView)
local TOGGLE_MAX = 6
local EXTRA_ID = 4 --额外消耗材料的cell_index
local CUR_EQUIP_INDEX_LIST = { 0, 1, 2, 4, 5, 7 }	--基础装备六件索引.

local equipment_effect = nil
local check_nor_equip_part_list = {
	[1] = GameEnum.EQUIP_INDEX_TOUKUI,      -- = 0,                         --头盔
    [2] = GameEnum.EQUIP_INDEX_YIFU,        -- = 1,                         --衣服
    [3] = GameEnum.EQUIP_INDEX_KUZI,        -- = 2,                         --裤子
    [4] = GameEnum.EQUIP_INDEX_XIEZI,       -- = 4,                         --鞋子
    [5] = GameEnum.EQUIP_INDEX_WUQI,        -- = 5,                         --武器 剑 镰 琴 伞
    [6] = GameEnum.EQUIP_INDEX_XIANFU,      -- = 7,                         -- 匕首
}

local check_shiping_equip_part_list = {
	[1] = GameEnum.EQUIP_INDEX_XIANLIAN,
	[2] = GameEnum.EQUIP_INDEX_XIANZHUI,
}
-- 装备合成
function NewComposeView:InitHeChengView()
	self.equip_hecheng_item_count = 15
	self.node_list["eqhc_splite_toggle"].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickEQHCSpliteToggle, self))
	self.node_list["layout_hecheng_show_frame_EQ"]:SetActive(false)

	if nil == self.hecheng_select_item_grid then
		local bundle, asset = "uis/view/equipment_suit_ui_prefab", "EquipHeChengItem"
		self.hecheng_select_item_grid = AsyncBaseGrid.New()
		self.hecheng_select_item_grid:CreateCells({col = 3, change_cells_num = 1, itemRender = HechengPartRender,
			list_view = self.node_list["ph_hecheng_select_item_list_EQ"], assetBundle = bundle, assetName = asset,})
		self.hecheng_select_item_grid:SetSelectCallBack(BindTool.Bind(self.OnClickSelectEquipment, self))
		self.hecheng_select_item_grid:SetDataList({}, 0)
	end

	if not self.hecheng_cur_equip_list then
		self.hecheng_cur_equip_list = {}
		for key, value in pairs(CUR_EQUIP_INDEX_LIST) do
			self.hecheng_cur_equip_list[value] = ItemCell.New(self.node_list.hecheng_cur_equip_list:FindObj("cur_equip_cell_" .. value))
		end
	end

	self.curr_select_item = nil
	self.demand_data = nil
	self.compose_data = nil

	self.first_open_compose_equip = false

	if nil == self.accordion_list_equip then
		self.accordion_list_equip = {}
		self.cell_list = {}
		self.loaded_cell_num = 0
		for i=1, TOGGLE_MAX do
			self.accordion_list_equip[i] = {}
			self.accordion_list_equip[i].text_name = self.node_list["text_btn_EQ_" .. i]
			self.accordion_list_equip[i].list = self.node_list["List"..i.."_EQ" ]
			self:LoadHeChengListCell(i)
			self.node_list["SelectBtn" .. i.."_EQ"].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickExpandHandler, self, i))
		end
	end
end

function NewComposeView:LoadHeChengListCell(index)
	local res_async_loader = AllocResAsyncLoader(self, "equipment_hecheng_item_equip" .. index)
	res_async_loader:Load("uis/view/equipment_suit_ui_prefab", "equipment_hecheng_item", nil,
		function(new_obj)
			local tab_index = self:GetShowIndex()
			local item_vo = {}
			self.loaded_cell_num = self.loaded_cell_num + 1
			for i=1, 17 do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.accordion_list_equip[index].list.transform, false)
				obj:GetComponent("Toggle").group = self.accordion_list_equip[index].list.toggle_group
				local item_render = EquipmentHechengItemRender.New(obj)
				item_render.parent_view = self
				item_vo[i] = item_render
				if self.loaded_cell_num == TOGGLE_MAX and i == 17 then
					self.load_equip_cell_complete = true
				end
				obj:SetActive(false)
			end

			self.cell_list[index] = item_vo
			if self.load_equip_cell_complete then
				self:ShowFlushHecheng(tab_index, self.tranfer_parent_index or 1, self:GetTranferChildIndex() or 1)
			end
		end)
end

function NewComposeView:HeChengDeleteMe()
	self.load_equip_cell_complete = nil

	if self.hecheng_must_item_list then
		for k, v in pairs(self.hecheng_must_item_list) do
			v:DeleteMe()
		end
		self.hecheng_must_item_list = nil
	end

	if self.hecheng_select_item_grid ~= nil then
		self.hecheng_select_item_grid:DeleteMe()
		self.hecheng_select_item_grid = nil
	end

	if self.hecheng_equipment_item then
		for k,v in pairs(self.hecheng_equipment_item) do
			v:DeleteMe()
		end
		self.hecheng_equipment_item = nil
	end

	if self.accordion_list_equip then
		self.accordion_list_equip = nil
	end
	self.loaded_cell_num = 0

	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			for k1,v1 in pairs(v) do
				v1:DeleteMe()
			end
			self.cell_list[k] = nil
		end
		self.cell_list = nil
	end

	if self.hecheng_target_item then
		self.hecheng_target_item:DeleteMe()
		self.hecheng_target_item = nil
	end

	if self.hecheng_probability_alert then
		self.hecheng_probability_alert:DeleteMe()
		self.hecheng_probability_alert = nil
	end

	if self.hecheng_cur_equip_list then
		for k,v in pairs(self.hecheng_cur_equip_list) do
			v:DeleteMe()
		end
		self.hecheng_cur_equip_list = nil
	end

	self.curr_select_item = nil
	self.btn_enabled = nil
	self.demand_data = nil
	self.compose_data = nil
	equipment_effect = nil
	self.eqhc_cur_item = nil
	self.old_splite_toggle_state = nil
end

function NewComposeView:ShowEQHCSpliteToggle()
	self.node_list.eqhc_splite_toggle:SetActive(self.show_index == COMPOSE_TYPE.EQ_HECHENG or self.show_index == COMPOSE_TYPE.EQ_HECHENG_TWO)
	self.node_list.hecheng_cur_equip:SetActive(self.show_index == COMPOSE_TYPE.EQ_HECHENG or self.show_index == COMPOSE_TYPE.EQ_HECHENG_TWO)
end

function NewComposeView:GetEQHCSpliteToggleState()
	return self.node_list.eqhc_splite_toggle.toggle.isOn and self.node_list.eqhc_splite_toggle:GetActive()
end

function NewComposeView:OnClickEQHCSpliteToggle(is_on)
	if self.old_splite_toggle_state ~= is_on then
		self.old_splite_toggle_state = is_on
		self:OnClickAccordionHechongChild(self.eqhc_cur_item)
	end
end

--创建装备合成组格子
function NewComposeView:CreateEquipHeChengScheme()
	if self.hecheng_target_item ~= nil then
	  return
	end

	self.hecheng_target_item = ItemCell.New(self.node_list["ph_target_item_EQ"])

	self.hecheng_must_item_list = {}
	for i = 1, 2 do
		local table_str = string.format("ph_must_item_%s_EQ", i)
		self.hecheng_must_item_list[i] = ItemCell.New(self.node_list[table_str])
		self.hecheng_must_item_list[i]:SetCellBgEnabled(false)
		self.hecheng_must_item_list[i]:SetNeedItemGetWay(true)
	end

	self.hecheng_equipment_item = {}
	for i = 1, 5 do
		self.hecheng_equipment_item[i] = ItemCell.New(self.node_list["ph_equipment_item_" .. i.."_EQ"])
		self.hecheng_equipment_item[i]:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
		self.hecheng_equipment_item[i]:SetIsShowTips(false)
		self.hecheng_equipment_item[i]:SetClickCallBack(BindTool.Bind(self.OnClickSelectEquipmentItem, self))
	end

	self.node_list["btn_hecheng_hecheng_EQ"].button:AddClickListener(BindTool.Bind1(self.OnEquipComposeOperaReq, self))
	self.node_list["btn_hecheng_add_onekey_EQ"].button:AddClickListener(BindTool.Bind1(self.OnEquipComposeOperaAddOnekey, self))
	self.node_list["btn_hecheng_once_hecheng"].button:AddClickListener(BindTool.Bind1(self.OnEquipComposeOperaOnce, self))
	--问号按钮
	--self.node_list["btn_hecheng_tips_EQ"].button:AddClickListener(BindTool.Bind1(self.OnClickBtnHeChengTip, self))
	XUI.AddClickEventListener(self.node_list.open_vip_btn, BindTool.Bind1(self.OnClickOpenVipBtn, self))
end

--点击选择装备项
function NewComposeView:OnClickSelectEquipment(cell)
	if cell == nil then
		return
	end

	local data = cell:GetData()
	if data == nil then
		return
	end


	self.equip_selected_item = cell
	self:SetEquipSelectedItemData(data)
	self:SetHechongLayoutVisible(false)
	self.node_list["hecheng_cur_equip"]:SetActive(false)

	local index = self:GetShowIndex()
	if index == COMPOSE_TYPE.EQ_HECHENG or
		index == COMPOSE_TYPE.EQ_HECHENG_TWO or
		index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW  or
		index == COMPOSE_TYPE.LING_CHONG or
		index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		self:SelectCommonEquipment(data)
	end
end

function NewComposeView:FlushEQHCTargetItem()
	if IsEmptyTable(self.select_data) then
		return
	end

	local data = self.select_data
	local param_t = {}
	param_t.star_level = data.star_level and data.star_level or data.compose_equip_best_attr_num
	self.hecheng_target_item:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
		self.hecheng_target_item:SetUpFlagIconVisible(true)
	self.hecheng_target_item:SetData({item_id = data.item_id, param = param_t})
end

--选择装备
function NewComposeView:SelectCommonEquipment(data)
	self.select_data = data
	self:FlushHechengEquipmentItem()

	local demand_data, compose_data

	-- local param_t = {}
	if self.hecheng_target_item then
		self.hecheng_target_item:DeleteMe()
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	self.node_list.hecheng_show_target_name.text.text = item_cfg and item_cfg.name or ""

	self.hecheng_target_item = ItemCell.New(self.node_list["ph_target_item_EQ"])
	-- param_t.star_level = data.star_level and data.star_level or data.compose_equip_best_attr_num
	self.node_list["ph_target_button"]:SetActive(false)

	-- if data.zhizun == 1 then
	--  	demand_data = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
	--  	self.hecheng_target_item:SetData({item_id = data.item_id, param = param_t, zhizun = data.zhizun})
	-- else
	if data.level then--神兽\灵宠\坐骑 装备独有 level
		local index = self:GetShowIndex()

		if index == COMPOSE_TYPE.LING_CHONG then --灵宠
			demand_data = ItemWGData.Instance:GetItemConfig(data.item_id)
			compose_data = EquipmentWGData.Instance:GetLingChongComposeByVItemId(data.item_id)
			self.hecheng_target_item:SetData({item_id = data.item_id})
			local bundle_name, asset_name = ResPath.GetItem(demand_data.icon_id)
			self.hecheng_target_item:SetItemIcon(bundle_name, asset_name)
			local color = demand_data.quality
			local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_"..color)
			self.hecheng_target_item:SetCellBg(bundle, asset)
			self.hecheng_target_item:SetLeftTopImg(compose_data.give_start_num)
			self.hecheng_target_item:SetUpFlagIconVisible(false)
		elseif index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then --神兽
			demand_data = ShenShouWGData.Instance:GetShenShouEqCfg(data.item_id)	-- 获取神兽装备配置
			compose_data = EquipmentWGData.Instance:GetComposeByVItemIdAndStar(data.item_id, data.compose_equip_best_attr_num)

			if self.hecheng_target_item then
				self.hecheng_target_item:DeleteMe()
			end

			self.hecheng_target_item = ShenShouEquipCell.New(self.node_list["ph_target_item_EQ"])
			self.hecheng_target_item:SetItemTipFrom(ShenShouEquipTip.FROM_EQUIMENT_HECHENG)

			local color = demand_data.quality
			local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_"..color)
			self.hecheng_target_item:SetCellBg(bundle, asset)
			--self.hecheng_target_item:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
			-- self.hecheng_target_item:SetUpFlagIconVisible(true)
			self.hecheng_target_item:SetData({item_id = data.item_id,star_count = compose_data.give_start_num})
			self.hecheng_target_item:SetLeftTopImg(compose_data.give_start_num)

			self.node_list["ph_target_button"]:SetActive(true)

			local cfg = {}
			cfg.strength_level = 0
			cfg.item_id = data.item_id
			cfg.star_count = data.compose_equip_best_attr_num
			self.node_list["ph_target_button"].button:AddClickListener(BindTool.Bind(self.OnClickTargetItem,self,cfg))
		end
	else
		demand_data = EquipmentWGData.Instance:GetEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
		compose_data = demand_data
		self:FlushEQHCTargetItem()
	end

	if demand_data == nil then
		self.demand_data = nil
		return
	end

	self:FlushEQHCStuffCost(compose_data)
	self:FlushEQHCOnceCost(compose_data)

	--刷新提示
	-- if data.zhizun == 1 then
	-- 	self.node_list["lbl_materials_tip_EQ"].text.text = Language.Compose.NeedStuffStr .. demand_data.equip_number
	-- else
	if self.tab_index == COMPOSE_TYPE.EQ_HECHENG or self.tab_index == COMPOSE_TYPE.EQ_HECHENG_TWO
		or self.tab_index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		
		if item_cfg.sub_type == GameEnum.EQUIP_TYPE_XIANZHUO or item_cfg.sub_type == GameEnum.EQUIP_TYPE_XIANJIE then
			self.node_list["lbl_materials_tip_EQ"].text.text = string.format(Language.Equip.LingChongZuoQiTip3, self.cur_data.item_tip2)
		else
			self.node_list["lbl_materials_tip_EQ"].text.text = string.format(Language.Equip.LingChongZuoQiTip3, self.cur_data.item_tip)
		end

		-- local need_more_type_stuff = Split(demand_data.consume_condition or "", "|")
		-- self.node_list.btn_hecheng_add_onekey_EQ:SetActive(#need_more_type_stuff <= 1)
	else
		self:FlushTip(demand_data, demand_data.order, demand_data.best_attr_count, demand_data.color, compose_data)--物品，装备阶数,装备星级，装备颜色
	end


	self.demand_data = demand_data
	self.compose_data = compose_data
	local is_enough = ComposeWGData.Instance:GetIsEnoughStuff(data)
	local btn_text_idx = is_enough and 1 or 2
	self.node_list["add_btn_text"].text.text = Language.Compose.ComposeBtnText[btn_text_idx]
end

function NewComposeView:FlushEQHCStuffCost(compose_data)
	if IsEmptyTable(self.hecheng_must_item_list) then
		return
	end

	local function stuff_set_data(stuff_list)
		if self.hecheng_must_item_list then
			local have_cost_data = false
			for k, v in pairs(self.hecheng_must_item_list) do
				local effect_state = false
				local have_data = not IsEmptyTable(stuff_list[k])
				local item_id = have_data and stuff_list[k].item_id or 0
				local need_num = have_data and stuff_list[k].need_num or 0

				if have_data and item_id > 0 and need_num > 0 then
					local had_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
					local is_enough = had_num >= need_num
					local str = ToColorStr(had_num .. "/" .. need_num,
											is_enough and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
					v:SetData({item_id = item_id, compose_is_enough = is_enough})
					v:SetRightBottomColorText(str)
					v:SetRightBottomTextVisible(true)
					effect_state = true
					have_cost_data = true
				else
					v:ClearData()
				end

				if equipment_effect and equipment_effect[5 + k] then
					equipment_effect[5 + k]:SetActive(effect_state)
				end
			end

			if equipment_effect and equipment_effect[#equipment_effect] then
				equipment_effect[#equipment_effect]:SetActive(have_cost_data)
			end
		end
	end

	if IsEmptyTable(compose_data) then
		stuff_set_data({})
		return
	end


	local stuff_list = {}
	local data = self:GetEquipSelectedItemData()

	if data.level then--神兽特有
		if self.tab_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			local temp_data = {}
			temp_data.item_id = compose_data.item_id
			temp_data.need_num = compose_data.item_num
			table.insert(stuff_list, temp_data)
		end
	else
		for i = 1, 2 do
			local stuff_id = compose_data and compose_data["stuff" .. i .. "_id"]
			local stuff_num = compose_data and compose_data["stuff" .. i .. "_num"]
			if stuff_id and stuff_num and stuff_id > 0 and stuff_num > 0 then
				local temp_data = {}
				temp_data.item_id = stuff_id
				temp_data.need_num = stuff_num
				table.insert(stuff_list, temp_data)
			end
		end
	end

	stuff_set_data(stuff_list)
end

function NewComposeView:FlushEQHCOnceCost(compose_data)
	self.node_list.btn_hecheng_once_hecheng:SetActive(false)	
	local role_sex = RoleWGData.Instance:GetRoleSex()

	local is_can_once_compose = false
	if (self.tab_index == COMPOSE_TYPE.EQ_HECHENG and role_sex == GameEnum.MALE) or 
		(self.tab_index ~= COMPOSE_TYPE.EQ_HECHENG_TWO and role_sex == GameEnum.FEMALE) or 
		self.tab_index == COMPOSE_TYPE.EQ_HECHENG_THREE then
			is_can_once_compose = true
	end

	if not is_can_once_compose then
		return
	end

	local item_config, item_type = ItemWGData.Instance:GetItemConfig(compose_data.compose_equip_id)
	if not item_config then
		return
	end

	-- 红色装备合成才显示
	if item_config.color ~= GameEnum.ITEM_COLOR_RED then
		return
	end

	-- 移除特殊饰品
	local part = ItemWGData.GetEquipTypeName(item_config.sub_type)
	if part == GameEnum.EQUIP_INDEX_XIANJIE or part == GameEnum.EQUIP_INDEX_XIANZHUO then
		return
	end

	local is_meet_show_condition = true
	local check_equip_list = {}
	if self.tab_index == COMPOSE_TYPE.EQ_HECHENG or self.tab_index == COMPOSE_TYPE.EQ_HECHENG_TWO then
		-- 普通装备一键合成显示（判断身上穿的所有普通装备）
		check_equip_list = check_nor_equip_part_list
	elseif self.tab_index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		-- 饰品一键合成
		check_equip_list = check_shiping_equip_part_list
	end

	if IsEmptyTable(check_shiping_equip_part_list) then
		return
	end
	
	for k, v in pairs(check_shiping_equip_part_list) do
		local equip_type = EquipWGData.GetEquipTypeByIndex(v)
		local equip_body_index = EquipBodyWGData.Instance:CalRoleEquipWearIndex(equip_type, item_config.order)
		local data = EquipWGData.Instance:GetGridData(equip_body_index)
		if not data or data.param.star_level < compose_data.compose_equip_best_attr_num then
			is_meet_show_condition = false
			break
		end
	end

	self.node_list.btn_hecheng_once_hecheng:SetActive(is_meet_show_condition)	
	self:FlushEQHCOnceRemind()
end

function NewComposeView:FlushEQHCOnceRemind()
	self.node_list.once_hecheng_remind:SetActive(false)
	local data = self:GetEquipSelectedItemData()
	if data == nil then
		return
	end

	local compose_cfg = EquipmentWGData.Instance:GetEquipComposeCfgByID(data.item_id, data.compose_equip_best_attr_num)
	if not compose_cfg then
		return
	end

	local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(data.item_id, data.compose_equip_best_attr_num)

	if can_use_num >= compose_cfg.max_consume_num then
		self.node_list.once_hecheng_remind:SetActive(true)
	end
end

function NewComposeView:OnClickTargetItem(data)
	if IsEmptyTable(data) then
		return
	end

	ShenShouWGCtrl.Instance:OpenShenShouEquipTip(data, ShenShouEquipTip.FROM_EQUIMENT_HECHENG)
end

--刷新材料提示
function NewComposeView:FlushTip(item_data, equip_order, equip_star_level, equip_color, compose_data)
	if self.tab_index == COMPOSE_TYPE.LING_CHONG then
		-- local str = compose_data.type == 1 and "坐骑" or "灵宠"
		local item_cfg = EquipmentWGData.Instance:GetLingChongComposeByVItemId(item_data.item_id)--灵宠
		if not item_cfg then
			item_cfg = ItemWGData.Instance:GetItemConfig(item_data.stuff1_id)
		end

		if compose_data.item_id ~= 0 and compose_data.item_id ~= nil then
			local need_item_cfg = ItemWGData.Instance:GetItemConfig(compose_data.item_id)
			local is_fense = compose_data.give_quality == 6 and "*3" or "≥3"
			self.node_list["lbl_materials_tip_EQ"].text.text = string.format(Language.Equip.LingChongZuoQiTip2,
							Language.Common.ColorName[equip_color - 1], is_fense, need_item_cfg.name, compose_data.item_num)--,str
		else
			self.node_list["lbl_materials_tip_EQ"].text.text = string.format(Language.Equip.LingChongZuoQiTip1,
							Language.Common.ColorName[equip_color - 1])--,str
		end
	else
		local item_cfg = EquipmentWGData.Instance:GetComposeByVItemIdAndStar(item_data.item_id, self.select_data.compose_equip_best_attr_num)--神兽
		if not item_cfg then
			item_cfg = ItemWGData.Instance:GetItemConfig(item_data.stuff1_id)
		end

		local equip_count = 3 --装备数量最低为3件

		if item_cfg and item_cfg.v_item_id then --神兽    color 0~5
			-- local color_str = Language.Common.ColorName[item_cfg.need_qualit]
			local color_str = Language.Equip.ShenShouHeChengEquipColor[item_cfg.need_qualit] or ""

			if item_cfg.item_num > 0 then
				local need_item_cfg = ItemWGData.Instance:GetItemConfig(item_cfg.item_id)
				self.node_list["lbl_materials_tip_EQ"].text.text = string.format(Language.Equip.ShenShouHeChengTip1,
							item_cfg.need_start_num, color_str, 3, need_item_cfg.name, item_cfg.item_num)
			else
				self.node_list["lbl_materials_tip_EQ"].text.text = string.format(Language.Equip.ShenShouHeChengTip2,
							item_cfg.need_start_num, color_str, 3)--,item_cfg.item_num
			end
		elseif item_data.stuff1_id ~= 0 and item_data.stuff1_id ~= nil then
			local need_item_cfg = ItemWGData.Instance:GetItemConfig(item_data.compose_equip_id)
			self.node_list["lbl_materials_tip_EQ"].text.text = string.format(Language.Equip.HcItemCountTip1,
						equip_order, equip_star_level, Language.Common.ColorName[equip_color], equip_count, item_cfg.name, item_data.stuff1_num)

		else
			local need_item_cfg = ItemWGData.Instance:GetItemConfig(item_data.compose_equip_id)
			if need_item_cfg then
				self.node_list["lbl_materials_tip_EQ"].text.text = string.format(Language.Equip.HcItemCountTip2,
						equip_order, equip_star_level, Language.Common.ColorName[equip_color], equip_count)
			end
		end
	end
end

--点击装备材料格(是否空格子)
function NewComposeView:OnClickSelectEquipmentItem(item)
	local data = item:GetData()
	if IsEmptyTable(data) then
		local equip_selected_item_data = self:GetEquipSelectedItemData()
		if IsEmptyTable(equip_selected_item_data) then
			print_error("-----【疑难】【装备合成】没有选择数据 -----")
			return
		end

		if not self:CheckHasEquip(equip_selected_item_data) then
			--获取途径
			local equip_stuff_id = 0
			if self.demand_data and self.demand_data.equip_stuff_id then
				equip_stuff_id = self.demand_data.equip_stuff_id
			elseif self.compose_data then
				equip_stuff_id = self.compose_data.equip_stuff_id
			end
			TipWGCtrl.Instance:OpenEquipGetWayView(equip_stuff_id)
			return
		end

		self.curr_select_item = item
		EquipmentWGCtrl.Instance:HeChengUpLevelBagOpen(BindTool.Bind(self.BagClickCallBack, self), equip_selected_item_data)
	else
		item:ClearData()
		item:SetItemIcon(ResPath.GetCommonImages("a3_ty_jia"))

		item:SetButtonComp(true)
		local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
		item:SetCellBg(bundle, asset)
		self:FlushEquipHeCheng()
	end
end

function NewComposeView:CheckHasEquip(data)
	if data == nil then
		return false
	end

	-- if data.zhizun == 1 then
	-- 	--至尊装备
	-- 	local demand_data = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
	-- 	if demand_data ~= nil then
	-- 		local equip_list = EquipmentWGData.Instance:GetHechengZhiZunEquipmentItemList(demand_data)
	-- 		return #equip_list > 0
	-- 	end
	-- else
	if data.level then
		local index = ComposeWGCtrl.Instance:GetShowIndex()
		if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			local demand_data = EquipmentWGData.Instance:GetSSEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
			if demand_data ~= nil then
				local equip_list = EquipmentWGData.Instance:GetSSHechengEquipmentItemList(demand_data)
				return #equip_list > 0
			end
		end
	else
		--普通装备
		local demand_data = EquipmentWGData.Instance:GetEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
		if demand_data ~= nil then
		 	local equip_list = EquipmentWGData.Instance:GetHechengEquipmentItemList(demand_data, true)
		 	return #equip_list > 0
		end
	end
	return false
end

function NewComposeView:BagClickCallBack(data)
	if not self.curr_select_item then
		return
	end

	self.curr_select_item:SetData(data)
	local item_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(data.item_id)	-- 获取神兽装备配置
	if item_cfg then
		if self.show_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW and data and data.strength_level > 0 then
			--策划需求，只有神兽装备才需要在已选中材料显示等级
			self.curr_select_item:SetRightTopImageText("+" .. data.strength_level)
		end
		self.curr_select_item:SetButtonComp(true)
		self.curr_select_item:SetLeftTopTextVisible(false)
		local bundle_name, asset = ResPath.GetItem(item_cfg.icon_id)
		self.curr_select_item:SetItemIcon(bundle_name, asset)

		local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_"..item_cfg.quality)
		self.curr_select_item:SetCellBg(bundle, asset)
		self.curr_select_item:SetLeftTopImg(data.star_count)
	end

	self:FlushEquipHeCheng()
end

function NewComposeView:OnClickExpandHandler(index, isOn)
	if index == nil or not isOn then
		return
	end

	if self.cell_list ~= nil and self.cell_list[index] ~= nil then
		for k,v in ipairs(self.cell_list[index]) do
			if k == 1 then
				if self.cell_list[index][k].view.toggle.isOn then
					self.cell_list[index][k]:OnClickItem(true)
				else
					self.cell_list[index][k].view.toggle.isOn = true
				end
			else
				self.cell_list[index][k].view.toggle.isOn = false
			end
		end
	end
end

--切换装备层
function NewComposeView:SetHechongLayoutVisible(is_visible)
	self.node_list["ph_hecheng_list_bg"]:SetActive(is_visible)
	self.node_list["ph_hecheng_select_item_list_EQ"]:SetActive(is_visible)

	local index = self.tab_index
	if index == COMPOSE_TYPE.EQ_HECHENG or
		index == COMPOSE_TYPE.EQ_HECHENG_TWO or
		index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW  or
		index == COMPOSE_TYPE.LING_CHONG or
		index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		self.node_list["layout_hecheng_show_frame_EQ"]:SetActive(not is_visible)
	-- elseif index == COMPOSE_TYPE.EQ_HECHENG_THREE then
	-- 	self.node_list["layout_hecheng_special_show_frame_EQ"]:SetActive(not is_visible)
	end
end

-- 选择子项
function NewComposeView:OnClickAccordionHechongChild(item)
	self:FlushHechengEquipmentItem()
	self:FlushHechengCurEquipment(item.data)
	if nil ~= item and nil ~= item.data then
		self:SetHechongLayoutVisible(true)
		local show_num = 0
		local data_list = {}
		self.cur_data = item.data
		self.eqhc_cur_item = item

		local role_prof = RoleWGData.Instance:GetRoleProf()
		local show_self_prof = self:GetEQHCSpliteToggleState()

		for index = 1, self.equip_hecheng_item_count do
			if item.data["cao" .. index] and item.data["cao" .. index] > 0 and show_num <= self.equip_hecheng_item_count then
				-- if item.data.zhizun == 1 then
				-- 	local item_cfg = EquipmentWGData.Instance:GetZhiZunEquipCfg(item.data["cao" .. index])
				-- 	local item_data = item.data
				-- 	if item_cfg then
				-- 		data_list[show_num] = {item_id = item_data["cao" .. index], compose_equip_best_attr_num = item_cfg.compose_equip_best_attr_num,
				-- 								type = item_data.type, zhizun = item_data.zhizun}
				--
				-- 		if item_data.star_level then
				-- 			data_list[show_num].star_level = item_data.star_level
				-- 		end
				-- 		show_num = show_num + 1
				-- 	end
				-- else
				if item.data.level then--神兽特有level
					data_list[show_num] = {item_id = item.data["cao" .. index], compose_equip_best_attr_num = item.data.compose_equip_best_attr_num,
											type = item.data.type, level = item.data.level, need_start_num = item.data.need_start_num,
											need_qualit = item.data.need_qualit, star_level = item.data.give_start_num}
					show_num = show_num + 1
				else
					local item_id = item.data["cao" .. index]
					local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)

					if item_cfg then
						local prof_meet = item_cfg.limit_prof == role_prof or item_cfg.limit_prof == GameEnum.ROLE_PROF_NOLIMIT
						if (show_self_prof and prof_meet) or (not show_self_prof) then
							data_list[show_num] = {item_id = item_id, compose_equip_best_attr_num = item.data.compose_equip_best_attr_num,
													type = item.data.type, zhizun = item.data.zhizun}

							if item.data.star_level then
								data_list[show_num].star_level = item.data.star_level
							end
							show_num = show_num + 1
						end
					end
				end
			end
		end

		self:SetSuccessRootEnable(false)
		self.hecheng_select_item_grid:SetDataList(data_list)
	end
end

function NewComposeView:XianQiJump()
	local data_list = self.hecheng_select_item_grid:GetDataList()
	if not IsEmptyTable(data_list) then
		local jump_to_equip_data = self:GetXianQiEquipJumpData()
		if not IsEmptyTable(jump_to_equip_data) then
			local jump_index = -1
			local jmup_data_cfg = ItemWGData.Instance:GetItemConfig(jump_to_equip_data.item_id)

			if not IsEmptyTable(data_list) then
				for k, v in pairs(data_list) do
					local cur_item_data = ItemWGData.Instance:GetItemConfig(v.item_id)
			
					-- if v.item_id == jump_to_equip_data.item_id then
					if cur_item_data.sub_type == jmup_data_cfg.sub_type then
						jump_index = k
						break
					end
				end
			end

			if jump_index >= 0 then

				self:SetEquipSelectedItemData(data_list[jump_index])
				self:SetHechongLayoutVisible(false)
				self.node_list["hecheng_cur_equip"]:SetActive(false)
			
				local index = self:GetShowIndex()
				if index == COMPOSE_TYPE.EQ_HECHENG or
					index == COMPOSE_TYPE.EQ_HECHENG_TWO or
					index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW  or
					index == COMPOSE_TYPE.LING_CHONG or
					index == COMPOSE_TYPE.EQ_HECHENG_THREE then
					self:SelectCommonEquipment(data_list[jump_index])
					self:SetHechongLayoutVisible(false)

					-- 如果有装备，点一下
					local is_enough = ComposeWGData.Instance:GetIsEnoughStuff(self.select_data)

					if is_enough then
						self:OnEquipComposeOperaAddOnekey()
					end
				end
			end
	
			self:SetXianQiEquipJumpData(nil)
		end
	end
end

--刷新显示装备合成
function NewComposeView:ShowFlushHecheng(index, tranfer_parent_index, tranfer_child_index)
	if not self.load_equip_cell_complete then
		return
	end

	local acc_data_list = nil
	self:SetHechongLayoutVisible(true)
	--self:PlayComposeEquipEffect()
	--问号按钮
	--self.node_list["btn_hecheng_tips_EQ"]:SetActive(true)
	if index == COMPOSE_TYPE.EQ_HECHENG then
		acc_data_list = EquipmentWGData.Instance:GetEquinHeChengAccordionDataList(EquipmentWGData.COMPOSE_EQUIP_TYPE.MALE) --参数 1 男剑客
	elseif index == COMPOSE_TYPE.EQ_HECHENG_TWO then
		acc_data_list = EquipmentWGData.Instance:GetEquinHeChengAccordionDataList(EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE) --参数 2 女剑客
	elseif index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		acc_data_list = EquipmentWGData.Instance:GetEquinHeChengAccordionDataList(EquipmentWGData.COMPOSE_EQUIP_TYPE.JEWELRY)
	elseif index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		acc_data_list = EquipmentWGData.Instance:GetSSEquipHeChengAccordionDataList()

	elseif index == COMPOSE_TYPE.LING_CHONG then
		--问号按钮
		--self.node_list["btn_hecheng_tips_EQ"]:SetActive(false)
		acc_data_list = EquipmentWGData.Instance:GetLingChongEquipHeChengAccordionDataList()
	end

	if acc_data_list == nil then
		return
	end

	self.acc_data = acc_data_list
	self.node_list.ToggleSV_EQ.scroll_rect.verticalNormalizedPosition = 1
	for i=1, TOGGLE_MAX do
		self.node_list["SelectBtn" .. i .. "_EQ"].accordion_element.isOn = false
		self.node_list["SelectBtn" .. i .. "_EQ"]:SetActive(i <= #self.acc_data)
		self.node_list["List" .. i .. "_EQ"]:SetActive(false)--(TOGGLE_MAX + 1 - i <= #self.acc_data)
		if self.cell_list[i] ~= nil then
			for k,v in pairs(self.cell_list[i]) do
				v:SetActive(i <= #self.acc_data)
			end
		end
	end

	self:FlushEquipHechengListData()

	for i=1, TOGGLE_MAX do
		if self.cell_list[i] ~= nil then
			if tranfer_parent_index ~= nil then
				self:RealSelectBtnEq(tranfer_parent_index, tranfer_child_index)
				break
			else
				local flag = self:RealSelectBtnEq(i)
				if flag then
					break
				end
			end
		end
	end
end

function NewComposeView:FlushEquipHechengListData()
	if self.acc_data == nil then
		return
	end

	local index = self:GetShowIndex()
	for i=1, TOGGLE_MAX do
		if self.cell_list[i] ~= nil then
			if nil ~= self.acc_data[i] then
				local data = self.acc_data[i]
				local btn_name = ""

				if index == COMPOSE_TYPE.LING_CHONG then
					btn_name = Language.Equip.ZhuZhanItemFatherName[data.pet_flag]
				elseif index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
					btn_name = Language.Equip.ShenShouHeChengItemCellName[data.name_type]
				elseif index == COMPOSE_TYPE.EQ_HECHENG or index == COMPOSE_TYPE.EQ_HECHENG_TWO then
					btn_name = CommonDataManager.GetDaXie(data.star_level) .. Language.Equip.HeChengItemFatherName[data.name_type]
				elseif index == COMPOSE_TYPE.EQ_HECHENG_THREE then
					btn_name = CommonDataManager.GetDaXie(data.star_level) .. Language.Equip.HeChengItemJewelryName[data.name_type]
				end

				self.node_list["text_btn_EQ_" .. i].text.text = btn_name
				self.node_list["text_btn_hl_" .. i].text.text = btn_name
				local remind = false
				if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
					remind = ComposeWGData.Instance:GetComposeShenShouBigTypeRemind(data)
				else
					remind = EquipmentWGData.Instance:GetComposeEquipRemindByMidType(data.title_index, data.star_level, data.name_type)
				end
				self.node_list["eq_remind" .. i]:SetActive(remind)
			end


			for k,v in pairs(self.cell_list[i]) do
				if nil == self.acc_data[i] then
					break
				end
				v:SetActive(k <= #self.acc_data[i]["child"])
				if k <= #self.acc_data[i]["child"] then
					v:SetData(self.acc_data[i]["child"][k])
				end
			end
		end
	end
end

-- 先直接enable去触发特效重置
function NewComposeView:PlayComposeEquipEffect()
	self.node_list["Hecheng_root_normal"]:SetActive(false)
	self.node_list["Hecheng_root_normal"]:SetActive(true)
end

function NewComposeView:RealSelectBtnEq(tmp_parent_index, tranfer_child_index)
	local key = "SelectBtn" .. tmp_parent_index .. "_EQ"
	if self.node_list[key] and self.node_list[key]:GetActive() then
		self.node_list[key].accordion_element.isOn = true

		if tranfer_child_index then
			GlobalTimerQuest:AddDelayTimer(function ()
				if self.cell_list[tmp_parent_index] and self.cell_list[tmp_parent_index][tranfer_child_index] then
					local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.canvas.worldCamera,
					self.cell_list[tmp_parent_index][tranfer_child_index].view.transform.position)
					local flag = UnityEngine.RectTransformUtility.RectangleContainsScreenPoint(self.node_list.ToggleSV_EQ.rect,
					screen_pos_tbl, self.canvas.worldCamera)
					if not flag then
						self.node_list.ToggleSV_EQ.scroll_rect.verticalNormalizedPosition = 0
					end
				end

				self:SetTranferChildIndex(nil)
			end, 0.1)
		end

		tranfer_child_index = tranfer_child_index or 1
		if self.cell_list[tmp_parent_index] and self.cell_list[tmp_parent_index][tranfer_child_index] then
			self.cell_list[tmp_parent_index][tranfer_child_index]:OnClickItem(true)
			self.cell_list[tmp_parent_index][tranfer_child_index].view.toggle.isOn = true
		end

		return true
	end

	return false
end


--刷新合成按钮
function NewComposeView:FlushEquipHeCheng()
	local btn_enabled = false
	local equip_selected_item_data = self:GetEquipSelectedItemData()
	local show_index = self:GetShowIndex()

	if show_index == COMPOSE_TYPE.EQ_HECHENG or show_index == COMPOSE_TYPE.EQ_HECHENG_TWO
		or show_index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		self.node_list["yunshi_add"]:SetActive(true)
		local yunshi_addition_cfg = QifuYunShiWGData.Instance:GetHasYunShiAdditionIdCfg(QifuYunShiWGData.ADDITION_TYPE.COMPOSE_TYPE)
		local addition_value = 0
		if yunshi_addition_cfg then
			addition_value = yunshi_addition_cfg.addition_value
		end
		self.node_list["yunshi_add_text"].text.text = string.format("(运势+%s%%)", addition_value)
	else
		self.node_list["yunshi_add"]:SetActive(false)
	end

	if equip_selected_item_data ~= nil then
		local data = equip_selected_item_data
		-- if data.zhizun == 1 then
		-- 	local index_list = self:GetEquipHeChengSacrificeList()
		-- 	if #index_list == 2 then
		-- 		self.node_list["Txt_hecheng_probability_EQ"].text.text = "100%"			--至尊装备合成成功率固定为100%
		-- 		local demand_data = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
		-- 		if demand_data ~= nil and demand_data.stuff1_id ~= 0 then
		-- 			btn_enabled = ItemWGData.Instance:GetItemNumInBagById(demand_data.stuff1_id) >= demand_data.stuff1_num
		-- 		else
		-- 			btn_enabled = true
		-- 		end
		-- 	else
		-- 		return
		-- 	end
		-- else
		if data.level then--神兽装备特有level
			local index_list = self:GetEquipHeChengSacrificeList()
			local successful_num
			local ling_chong_demand_data
			if self.tab_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
				successful_num = EquipmentWGData.Instance:GetShenShouEquineSuccessItemNum()
			else
				ling_chong_demand_data = EquipmentWGData.Instance:GetLingChongEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
				if ling_chong_demand_data then
					local is_fense = ling_chong_demand_data.give_quality == 6
					successful_num = EquipmentWGData.Instance:GetLingChongEquineSuccessItemNum(is_fense)
				end
			end

			local probability = 0
			if successful_num and successful_num <= #index_list then
				local index = self:GetShowIndex()
				if index == COMPOSE_TYPE.LING_CHONG then
					local demand_data = EquipmentWGData.Instance:GetLingChongEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
					-- if demand_data ~= nil and demand_data.item_id ~= 0 then
					-- 	btn_enabled = ItemWGData.Instance:GetItemNumInBagById(demand_data.item_id) >= demand_data.item_num
					-- else
						btn_enabled = true
					-- end
					probability = 100--写死

				elseif index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
					local demand_data = EquipmentWGData.Instance:GetSSEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
					-- if demand_data ~= nil and demand_data.item_id ~= 0 then
					-- 	btn_enabled = ItemWGData.Instance:GetItemNumInBagById(demand_data.item_id) >= demand_data.item_num
					-- else
						btn_enabled = true
					-- end
					probability = 100--写死
				end
			else
				local index = self:GetShowIndex()
				if index == COMPOSE_TYPE.LING_CHONG then
					local is_fense = ling_chong_demand_data.give_quality == 6
					if is_fense then
						probability = 0
					else
						btn_enabled = #index_list >= 3
						probability = EquipmentWGData.Instance:GetLingChongSuccessProbability2(#index_list)
					end
				end

			end
		 	local color = probability <= 0 and COLOR3B.PINK or COLOR3B.GREEN
			self.node_list["Txt_hecheng_probability_EQ"].text.text = Language.Compose.Success .. "" .. ToColorStr(probability .. "%" , color)
		else --普通装备
			local index_list = self:GetEquipHeChengSacrificeList()
			local probability = EquipmentWGData.Instance:GetEquineSuccessProbability(data.item_id, data.compose_equip_best_attr_num, #index_list)
			if probability > 0 then
				local demand_data = EquipmentWGData.Instance:GetEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)

				if demand_data ~= nil and demand_data.stuff1_id ~= 0 then
					self.btn_enabled = ItemWGData.Instance:GetItemNumInBagById(demand_data.stuff1_id) >= demand_data.stuff1_num
				end

				--[[
				if demand_data and demand_data.colorful_equip_extra_id > 0 then
					local is_have_one, is_have_two
					for k,v in ipairs(self.hecheng_equipment_item) do
						if v:GetData() then
							if v.data.item_id == demand_data.colorful_equip_extra_id and not is_have_one then
								is_have_one = true
							elseif v.data.item_id == demand_data.colorful_equip_extra_id then --表示放入了多件特殊物品
								is_have_two = true
							end
						end
					end
					if is_have_one and not is_have_two then
						btn_enabled = true
					elseif is_have_one and is_have_two then --表示放入了多件特殊物品
						btn_enabled = false
						probability = 0
					end
				else
				]]
					btn_enabled = true
				-- end
			end
			local color = probability <= 0 and COLOR3B.PINK or COLOR3B.GREEN
			self.node_list["Txt_hecheng_probability_EQ"].text.text = Language.Compose.Success .. "" .. ToColorStr(probability .. "%" , color)

		end
	end

	XUI.SetButtonEnabled(self.node_list["btn_hecheng_hecheng_EQ"], btn_enabled)
	-- self:FlushAddBtnText()
end

function NewComposeView:FlushAddBtnText()
	if self.select_data then
		local is_enough = ComposeWGData.Instance:GetIsEnoughStuff(self.select_data)
		if self.node_list["add_btn_text"] then
			self.node_list["add_btn_text"].text.text = is_enough and Language.Compose.ComposeBtnText[1] or Language.Compose.ComposeBtnText[2]
		end
	end
end

--刷新合成材料数量
function NewComposeView:FlushHechengMaterialNum(change_item_id, force_flush)
	if self.hecheng_must_item_list == nil then
		return
	end

	local can_flush = false
	if not force_flush then
		for k, v in pairs(self.hecheng_must_item_list) do
			local data = v:GetData()
			if data and data.item_id == change_item_id then
				can_flush = true
				break
			end
		end
	end

	if (not force_flush and not can_flush) then
		return
	end

	local data = self:GetEquipSelectedItemData()
	if data == nil then
		return
	end

	local demand_data
	-- if data.zhizun == 1 then
	-- 	demand_data = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
	-- else
	if data.level then--神兽特有
		if self.tab_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			demand_data = EquipmentWGData.Instance:GetComposeByVItemIdAndStar(data.item_id, data.compose_equip_best_attr_num)
		else
			demand_data = EquipmentWGData.Instance:GetLingChongComposeByVItemId(data.item_id)
		end
	else
		demand_data = EquipmentWGData.Instance:GetEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
	end

	self:FlushEQHCStuffCost(demand_data)
	self:FlushEquipHeCheng()
	self:FlushEQHCOnceCost(demand_data)
end

--获取装备合成的（装备部位）
--已经选中的装备
function NewComposeView:GetEquipHeChengSacrificeList()
	if self.hecheng_equipment_item == nil then
		return {}, 0
	end

	local index_list = {}
	local exist_bind_num = 0
	-- local tmp_idx_1
	-- local can_add_num
	-- local colorful_equip_extra_id = 0
	-- if self.demand_data then
	-- 	colorful_equip_extra_id = self.demand_data.colorful_equip_extra_id or 0
	-- 	can_add_num = EquipmentWGData.Instance:GetEquineSuccessItemNum(self.demand_data.item_id, self.demand_data.compose_equip_best_attr_num)
	-- end

	for k,v in ipairs(self.hecheng_equipment_item) do
		local data = v:GetData()
		if data then
			if data.is_bind == 1 then
				exist_bind_num = exist_bind_num + 1
			end

			if data.is_role_equip then
				-- if self.demand_data and colorful_equip_extra_id > 0 and data.item_id == colorful_equip_extra_id then
				-- 	tmp_idx_1 = data.index + data.is_role_equip
				-- end
			else
				local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
				if item_cfg then
					local tem_data = {}
					tem_data.index = data.index
					tem_data.color = item_cfg.color
					tem_data.star = data.param and data.param.star_level or 0
					tem_data.is_wear = data.frombody and 1 or 0
					index_list[#index_list + 1] = tem_data
				end

				-- if self.demand_data and colorful_equip_extra_id > 0 and data.item_id == colorful_equip_extra_id then
				-- 	tmp_idx_1 = v:GetData().index
				-- end
			end
		end
	end

	--[[
	if tmp_idx_1 and can_add_num and #index_list == can_add_num then --服务端要求，colorful_equip_extra_id这个字段的参数要放在param4中
		local idx
		for i, v in ipairs(index_list) do
			if v == tmp_idx_1 then
				idx = i
				break
			end
		end

		local tmp_idx_2
		if idx and idx ~= EXTRA_ID then
			tmp_idx_2 = index_list[EXTRA_ID]
			index_list[idx] = tmp_idx_2
			index_list[EXTRA_ID] = tmp_idx_1
		end
	end
	]]
	return index_list, exist_bind_num
end

--获取合成装备的ID （主要用于至尊装备）
function NewComposeView:GetHeChengEquipId()
	if self.hecheng_equipment_item == nil then
		return {}
	end

	local index_list = {}
	for k,v in ipairs(self.hecheng_equipment_item)do
		if v:GetData() ~= nil then
			index_list[#index_list + 1] = v:GetData().select_index
		end
	end
	return index_list
end

--彩装合成处理
function NewComposeView:GetHeChengEquipCellList( )
	local cell_data = {}
	for k,v in pairs(self.hecheng_equipment_item) do
		if v:GetData() ~= nil then
			table.insert(cell_data, v.data)
		end
	end

	return cell_data
end

--记录合成目标装备item_id
function NewComposeView:RecordCurEquipProduct(item_id)
	self.cur_product_item_id = item_id
	-- return self.cur_product_item_id
end
function NewComposeView:GetProductItemId()
	return self.cur_product_item_id
end

--点击合成
function NewComposeView:OnEquipComposeOperaReq()
	if self.hecheng_must_item_list == nil then
		return
	end

	for k, v in ipairs(self.hecheng_must_item_list) do
		local data = v:GetData()
		if data and not data.compose_is_enough then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = data.item_id})
			return
		end
	end

	local data = self:GetEquipSelectedItemData()

	if data == nil then
		return
	end

	local tab_index = self.tab_index
	if data.level then--------神兽特有level
		if tab_index == COMPOSE_TYPE.LING_CHONG then
			self:OnLingChongMountEquipComposeOperaReq()
		elseif tab_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			self:OnSSEquipComposeOperaReq()
		end
		return
	end

	local index_list , exist_bind_num = self:GetEquipHeChengSacrificeList()
	local probability = 0
	-- if data.zhizun == 1 then
	-- 	 probability = 100
	-- else
		probability = EquipmentWGData.Instance:GetEquineSuccessProbability(data.item_id, data.compose_equip_best_attr_num, #index_list)
	-- end
    local yunshi_addition_cfg = QifuYunShiWGData.Instance:GetHasYunShiAdditionIdCfg(QifuYunShiWGData.ADDITION_TYPE.COMPOSE_TYPE)
    local addition_value = 0
    if yunshi_addition_cfg then
        addition_value = yunshi_addition_cfg.addition_value
    end
    probability = probability + addition_value

	local function gotoHecheng()
		self:RecordCurEquipProduct(data.item_id)
		local real_index_list = {}

		if tab_index == COMPOSE_TYPE.EQ_HECHENG or tab_index == COMPOSE_TYPE.EQ_HECHENG_TWO or tab_index == COMPOSE_TYPE.EQ_HECHENG_THREE then
			local temp_table = {}
			if not IsEmptyTable(index_list) then
				for k,v in ipairs(index_list) do
					local data = {is_bag = v.is_wear == 1 and 0 or 1,
								index = v.index,
								sort = v.is_wear and v.color * 10 + 1 or v.color * 10,}
					table.insert(temp_table, data)
				end

				-- 后端要前端将材料按颜色从小到大排序好
				table.sort(temp_table, SortTools.KeyLowerSorter("sort"))
			end

			EquipmentWGCtrl.Instance:SendNewEquipComposeReq(data.item_id, data.compose_equip_best_attr_num, temp_table)
		else
			if not IsEmptyTable(index_list) then
				-- 后端要前端将材料按颜色从小到大排序好
				table.sort(index_list, SortTools.KeyLowerSorter("color"))
				for k, v in ipairs(index_list) do
					real_index_list[#real_index_list + 1] = v.index
				end
			end
			EquipmentWGCtrl.Instance:SendEquipComposeOperaReq(COMPOSE_EQUIP_OPERA_TYPE.EQUIP_OPERA_TYPE_COMPOSE, data.item_id, data.compose_equip_best_attr_num, real_index_list)
		end

		self:FlushHechengEquipmentItem()
	end

	local function open_alert()
		if next(index_list) ~= nil and data ~= nil and probability > 0 then
			if probability >= 100 then
				gotoHecheng()
			else
				if nil == self.hecheng_probability_alert then
					self.hecheng_probability_alert = Alert.New()
				end
				self.hecheng_probability_alert:SetOkFunc(gotoHecheng)
				self.hecheng_probability_alert:SetLableString(string.format(Language.Equip.HeChengProbabilityText, probability))
				self.hecheng_probability_alert:Open()
			end
		end
	end

	local no_tip = true
	if self.alert then
		self.alert:SetOkFunc(open_alert)
		no_tip = self.alert:GetIsNolongerTips()
	end

	for k, v in ipairs(self.hecheng_must_item_list) do
		local data = v:GetData()
		if data then
			local have_bind_item = ComposeWGData.Instance:IsHaveBindItemByEquip(data)
			if (exist_bind_num > 0 or have_bind_item) and not no_tip then
				self.alert:Open()
				return
			end
		end
	end

	open_alert()
end

function NewComposeView:OnSSEquipComposeOperaReq()
	local data = self:GetEquipSelectedItemData()
	local index_list , exist_bind_num = self:GetEquipHeChengSacrificeList()
	local successful_num = EquipmentWGData.Instance:GetShenShouEquineSuccessItemNum()

	local function hecheng()
		if successful_num <= #index_list and data ~= nil then
			self:RecordCurEquipProduct(data.item_id)
			local param_1 = index_list[1] and index_list[1].index or 0
			local param_2 = index_list[2] and index_list[2].index or 0
			local param_3 = index_list[3] and index_list[3].index or 0
			ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_COMPOSE, data.item_id, param_1, param_2, param_3)
			self:FlushHechengEquipmentItem()
		end
	end

	local no_tip = true
	if self.alert then
		self.alert:SetOkFunc(hecheng)
		no_tip = self.alert:GetIsNolongerTips()
	end

	for k, v in ipairs(self.hecheng_must_item_list) do
		local data = v:GetData()
		if data then
			local have_bind_item = ComposeWGData.Instance:IsHaveBindItemByEquip(data)
			if (exist_bind_num > 0 or have_bind_item) and not no_tip then
				self.alert:Open()
				return
			end
		end
	end

	hecheng()
end

function NewComposeView:OnTSEquipComposeOperaReq()
	local data = self:GetEquipSelectedItemData()
	local index_list , exist_bind_num = self:GetEquipHeChengSacrificeList()
	local successful_num = EquipmentWGData.Instance:GetTSEquipSuccessItemNum()

	local function hecheng()
		if successful_num <= #index_list and data ~= nil then
			self:RecordCurEquipProduct(data.item_id)
			local param_1 = index_list[1] and index_list[1].index or 0
			local param_2 = index_list[2] and index_list[2].index or 0
			local param_3 = index_list[3] and index_list[3].index or 0
			TianShenWGCtrl.Instance:SendTianshenComposeReq(data.item_id, data.star_level, {param_1, param_2, param_3})
			self:FlushHechengEquipmentItem()
		end
	end

	local no_tip = true
	if self.alert then
		self.alert:SetOkFunc(hecheng)
		no_tip = self.alert:GetIsNolongerTips()
	end

	for k, v in ipairs(self.hecheng_must_item_list) do
		local data = v:GetData()
		if data then
			local have_bind_item = ComposeWGData.Instance:IsHaveBindItemByEquip(data)
			if (exist_bind_num > 0 or have_bind_item) and not no_tip then
				self.alert:Open()
				return
			end
		end
	end

	hecheng()
end

function NewComposeView:OnLingChongMountEquipComposeOperaReq()
	local data = self:GetEquipSelectedItemData()
	local index_list ,exist_bind_num = self:GetEquipHeChengSacrificeList()
	local successful_num = EquipmentWGData.Instance:GetShenShouEquineSuccessItemNum()

	local function hecheng()
		if successful_num <= #index_list and data ~= nil then
			-- 后端要前端将材料按颜色从小到大排序好
			local real_index_list = {}
			if not IsEmptyTable(index_list) then
				table.sort(index_list, SortTools.KeyLowerSorter("color"))
				for k, v in ipairs(index_list) do
					real_index_list[#real_index_list + 1] = v.index
				end
			end

			self:RecordCurEquipProduct(data.item_id)
			EquipmentWGCtrl.Instance:SendEquipComposeOperaReq(COMPOSE_EQUIP_OPERA_TYPE.LING_CHONG_TYPE_COMPOSE, data.item_id, data.type, real_index_list)
			self:FlushHechengEquipmentItem()
		end
	end

	local no_tip = true
	if self.alert then
		self.alert:SetOkFunc(hecheng)
		no_tip = self.alert:GetIsNolongerTips()
	end

	for k, v in ipairs(self.hecheng_must_item_list) do
		local data = v:GetData()
		if data then
			local have_bind_item = ComposeWGData.Instance:IsHaveBindItemByEquip(data)
			if (exist_bind_num > 0 or have_bind_item) and not no_tip then
				self.alert:Open()
				return
			end
		end
	end

	hecheng()
end

function NewComposeView:EQHCCheckHaveEnoughEquipCost()
	if IsEmptyTable(self.hecheng_equipment_item) or IsEmptyTable(self.demand_data) then
		return true
	end

	local select_num = 0
	for k,v in ipairs(self.hecheng_equipment_item) do
		if v:GetData() then
			select_num = select_num + 1
		end
	end
	local max_consume_num = self.demand_data.max_consume_num or 3
	return select_num >= max_consume_num
end

function NewComposeView:OnEquipComposeOperaAddOnekey()
	local is_enough = ComposeWGData.Instance:GetIsEnoughStuff(self.select_data)

	if not is_enough then
		if self.tab_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			ViewManager.Instance:Open(GuideModuleName.WorldServer,TabIndex.worserv_boss_mh)
			return
		end

		--获取途径
		local equip_stuff_id = 0
		if self.demand_data and self.demand_data.equip_stuff_id then
			equip_stuff_id = self.demand_data.equip_stuff_id
		elseif self.compose_data then
			equip_stuff_id = self.compose_data.equip_stuff_id
		end

		TipWGCtrl.Instance:OpenEquipGetWayView(equip_stuff_id)
		return
	end

	local data = self:GetEquipSelectedItemData()
	if self.hecheng_equipment_item == nil or data == nil then
		return
	end

	local is_select_max = self:EQHCCheckHaveEnoughEquipCost()
	if is_select_max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Equip.HcItemCountTip5)
		return
	end

	local equip_list
	local demand_data
	-- if data.zhizun == 1 then
	-- 	demand_data = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
	-- 	equip_list = EquipmentWGData.Instance:GetHechengZhiZunEquipmentItemList(demand_data)
	-- else
	if data.level then--神兽特有level
		local index = self.tab_index
		if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			demand_data = EquipmentWGData.Instance:GetSSEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
			equip_list = EquipmentWGData.Instance:GetSSHechengEquipmentItemList(demand_data)
		end

	else
		demand_data = EquipmentWGData.Instance:GetEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
		equip_list = EquipmentWGData.Instance:GetHechengEquipmentItemList(demand_data, true, true)
	end

	-- local colorful_equip_extra_id = demand_data and demand_data.colorful_equip_extra_id or 0--彩装额外消耗
	local equip_list_new = {}

	local tip_flag = true
	local remove_idx 	--如果筛选列表中有这件装备，则移除 equip_list 中改位置数据
	if equip_list then
		local is_have_one = false 		--确保只有一件额外消耗的同部位装备
		local item_data, item_cfg, bundle_name, asset, color, compose_data
		--[[
		if colorful_equip_extra_id > 0 then
			for k, v in pairs(self.hecheng_equipment_item) do
				if v:GetData() ~= nil then
					if v.data.item_id == colorful_equip_extra_id and not is_have_one then --已经找到了一件
						tip_flag = false
						is_have_one = true
					elseif v.data.item_id == colorful_equip_extra_id then --找到第二件，就移除data
						self:OnClickSelectEquipmentItem(v)
					end
				end
			end

			if not is_have_one then --玩家主动操作时没有放入额外消耗同部位的装备时
				local extra_data = self.hecheng_equipment_item[EXTRA_ID].data
				for k, v in pairs(equip_list) do
					if extra_data == nil and v.item_id == colorful_equip_extra_id and not remove_idx then --表示一件装备都没有加入，确保第四个格子放入的是粉装
						remove_idx = true
						is_have_one = true
						self.hecheng_equipment_item[EXTRA_ID]:SetData(v)
					elseif v.item_id == colorful_equip_extra_id and not remove_idx then
						remove_idx = true
						is_have_one = true
						table.insert(equip_list_new, 1, v)
					elseif v.item_id ~= colorful_equip_extra_id then
						table.insert(equip_list_new, v)
					end
				end
				equip_list = {}
			else
				equip_list_new = equip_list
			end
		else
		]]
			equip_list_new = equip_list
		-- end

		for k,v in ipairs(self.hecheng_equipment_item) do
			if v:GetData() == nil and #equip_list_new > 0 and v:GetButtonComp() then
				tip_flag = false
				item_data = table.remove(equip_list_new, 1)
				v:SetData(item_data)
				--v:SetUpFlagIconVisible(false)
				item_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(item_data.item_id)	-- 获取神兽装备配置
				if item_cfg then
					v:SetButtonComp(true)
					v:SetLeftTopTextVisible(false)
					local bundle_name, asset = ResPath.GetItem(item_cfg.icon_id)
					v:SetItemIcon(bundle_name, asset)
					color = item_cfg.quality
					local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_"..color)
					v:SetCellBg(bundle, asset)
					compose_data = EquipmentWGData.Instance:GetComposeByVItemIdAndStar(data.item_id, data.compose_equip_best_attr_num)

					v:SetLeftTopImg(item_data.star_count)
				end
			end
		end

		-- if not is_have_one and colorful_equip_extra_id > 0 and self.hecheng_equipment_item[EXTRA_ID]:GetData() ~= nil and count == 0 then --没有添加额外消耗材料
		-- 	self:OnClickSelectEquipmentItem(self.hecheng_equipment_item[EXTRA_ID])
		-- end
	end

	self:FlushEquipHeCheng()

	if tip_flag then
		local is_select_max = self:EQHCCheckHaveEnoughEquipCost()
		if not is_select_max then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Equip.HcItemCountTip4)
		end
	end
end


function NewComposeView:OnEquipComposeOperaOnce()
	local data = self:GetEquipSelectedItemData()
	if data == nil then
		return
	end

	local compose_cfg = EquipmentWGData.Instance:GetEquipComposeCfgByID(data.item_id, data.compose_equip_best_attr_num)
	if not compose_cfg then
		return
	end

	local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(data.item_id, data.compose_equip_best_attr_num)

	if can_use_num >= compose_cfg.max_consume_num then
		self:FlushHechengEquipmentItem()
		ComposeWGCtrl.Instance:OpenCompseEquipOnceView(data.item_id, data.compose_equip_best_attr_num)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Compose.ComposeOnceError[1])
	end
end

--刷新当前装备.
function NewComposeView:FlushHechengCurEquipment(data)
	local order = data and data.order or 1

	for key, value in pairs(CUR_EQUIP_INDEX_LIST) do
		local equip_type = EquipWGData.GetEquipTypeByIndex(value)
		local equip_body_index = EquipBodyWGData.Instance:CalRoleEquipWearIndex(equip_type, order)
		local data = EquipWGData.Instance:GetGridData(equip_body_index)

		if self.hecheng_cur_equip_list[value] then
			if data and data.item_id > 0 then
				self.hecheng_cur_equip_list[value]:SetData(data)
			else
				self.hecheng_cur_equip_list[value]:ClearData()
				self.hecheng_cur_equip_list[value]:SetItemIcon(ResPath.GetEquipIcon(value))  -- 设置物品图标
			end
		end
	end
end

--刷新合成材料格子
function NewComposeView:FlushHechengEquipmentItem()
	local data = self:GetEquipSelectedItemData()
	-- if self.equip_selected_item ~= nil then
	-- 	local data = self:GetEquipSelectedItemData()

	if not IsEmptyTable(data) then
		local successful_num = 0
		local bundle, asset
		-- if data ~= nil and data.zhizun == 1 then
		-- 	item_cfg = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
		-- 	successful_num = item_cfg.consume_num
		-- 	for k = 1, #self.hecheng_equipment_item do --相反
		-- 		local v = self.hecheng_equipment_item[k]
		-- 		v:ClearData()
		-- 		if successful_num > 0 then
		-- 			v:SetButtonComp(true)
		-- 			v.button.enabled = true
		-- 			v:SetItemIcon(ResPath.GetCommonButton("a1_anniu_jia2"))
		-- 			v:SetButtonComp(true)
		-- 			if equipment_effect[k] then
		-- 				equipment_effect[k]:SetActive(true)
		-- 			end
		-- 		else
		-- 			v.button.enabled = false
		-- 			v:SetItemIcon(ResPath.GetCommonImages("a1_suo_bb2"))
		-- 			v:SetButtonComp(false)
		-- 			if equipment_effect[k] then
		-- 				equipment_effect[k]:SetActive(false)
		-- 			end
		-- 		end
		--
		-- 		bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
		-- 		v:SetCellBg(bundle, asset)
		-- 		successful_num = successful_num - 1
		-- 	end
		-- else
	 		successful_num = EquipmentWGData.Instance:GetEquineSuccessItemNum(data.item_id, data.compose_equip_best_attr_num)

			if data.level then--神兽特有level
				if self.tab_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
					successful_num = EquipmentWGData.Instance:GetShenShouEquineSuccessItemNum()
				else
					-- print_error(data.compose_equip_best_attr_num, data.item_id)
					local ling_chong_demand_data = EquipmentWGData.Instance:GetLingChongEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
					if ling_chong_demand_data then
						local is_fense = ling_chong_demand_data.give_quality == 6
						-- print_error(ling_chong_demand_data.give_quality)
						successful_num = EquipmentWGData.Instance:GetLingChongEquineSuccessItemNum(is_fense)
					else
						successful_num = EquipmentWGData.Instance:GetLingChongEquineSuccessItemNum(false)
					end
				end
			end

			if successful_num then
				for k = 1, 5 do
					local v = self.hecheng_equipment_item[k]
					v:ClearData()
					if successful_num > 0 then
						v:SetItemIcon(ResPath.GetCommonImages("a3_ty_jia"))
						v:SetButtonComp(true)
						if equipment_effect and equipment_effect[k] then
							equipment_effect[k]:SetActive(true)
						end
					else
						if equipment_effect and equipment_effect[k] then
							equipment_effect[k]:SetActive(false)
						end
						v.button.enabled = false
						v:SetItemIcon(ResPath.GetCommonImages("a3_ty_suo"))
						v:SetButtonComp(false)
					end
					bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
					v:SetCellBg(bundle, asset)
					successful_num = successful_num - 1
				end
			end
		-- end
	end

	
	self.node_list["Txt_hecheng_probability_EQ"].text.text = Language.Compose.Success .. "" .. ToColorStr("0%", COLOR3B.PINK)
	XUI.SetButtonEnabled(self.node_list["btn_hecheng_hecheng_EQ"], false)
	self:FlushAddBtnText()
end

-- function NewComposeView:ClearHeChengEquipmentItem()
-- 	for k = 1, 5 do
-- 		local v = self.hecheng_equipment_item[k]
-- 		v:ClearData()
-- 		v:SetCellBg()
-- 	end
-- end

--点击合成提
function NewComposeView:OnClickBtnHeChengTip()
	local item_data = self.cur_data
	if IsEmptyTable(item_data) then
		return
	end

	local content = ""
	local show_index = self:GetShowIndex()
	local index = 0
	if show_index == COMPOSE_TYPE.EQ_HECHENG then
		index = 1
	elseif show_index == COMPOSE_TYPE.EQ_HECHENG_TWO then
		index = 2
	elseif show_index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		index = 3
	elseif show_index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		index = 4
	elseif show_index == COMPOSE_TYPE.LING_CHONG then
		index = 5
	end

	if show_index == COMPOSE_TYPE.EQ_HECHENG or show_index == COMPOSE_TYPE.EQ_HECHENG_TWO or show_index == COMPOSE_TYPE.EQ_HECHENG_THREE then
		if self.demand_data and item_data.item_tip then
			local new_tips_str = ""
			local rate_num = self.demand_data.max_consume_num
			local tips_str_list = Split(item_data.item_tip, "\n")
			for k, v in pairs(tips_str_list) do
				local add_huanhang = k == 1 and "" or "\n"
				new_tips_str = new_tips_str .. add_huanhang .. ToColorStr(v, '#79fa82')
			end
			content = string.format(Language.Equip.TipsHeChengText2, new_tips_str, rate_num)
		else
			content = item_data.item_tip
		end
	elseif show_index == COMPOSE_TYPE.LING_CHONG then
		content = Language.Equip.TipsHeChengText[tonumber(index .. item_data.color .. item_data.type)] or ""      -- 5 品质 类型
	else
		content = Language.Equip.TipsHeChengText[tonumber(index .. item_data.compose_equip_best_attr_num .. item_data.type)] or ""
	end

	local title_name = Language.Equip.TipsHeChengTitle[index] or ""

	local rule_tip = RuleTip.Instance
	rule_tip:SetTitle(title_name)
	rule_tip:SetContent(content)
end

function NewComposeView:OnClickOpenVipBtn()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_month_card)
end

----------------------------------------------------------------------------
-- EquipmentHechengItemRender
----------------------------------------------------------------------------

EquipmentHechengItemRender = EquipmentHechengItemRender or BaseClass(BaseRender)
function EquipmentHechengItemRender:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
	self:CreateChild()
end

function EquipmentHechengItemRender:OnClickItem(is_on)
	if nil == is_on then
		return
	end

	if true == is_on then
		--这里要把其他所有的全部取消选中
		for k,v in pairs(self.parent_view.cell_list) do
			for k1,v1 in pairs(v) do
				v1:OnSelectChange(false)
			end
		end
		self.parent_view:OnClickAccordionHechongChild(self)
		self.parent_view:ShowEQHCSpliteToggle()
		self:OnSelectChange(true)
	else
		self:OnSelectChange(false)
	end
end

function EquipmentHechengItemRender:__delete()
	self.text_name = nil
	self.parent_view = nil
end

function EquipmentHechengItemRender:GetHeight()
	-- if self:IsChild() then
	-- 	self.height = 80
	-- end
	-- return self.height
end

function EquipmentHechengItemRender:CreateChild()

end

function EquipmentHechengItemRender:OnFlush()
	local data = self:GetData()
	if data == nil then
		return
	end

	local index = ComposeWGCtrl.Instance:GetShowIndex()
	if data.level then --神兽特有level
		if index == COMPOSE_TYPE.LING_CHONG then
			self.node_list["text_name"].text.text = Language.Equip.LingChongHeChengItemFatherName[data.color]
			self.node_list["text_name_hl"].text.text = Language.Equip.LingChongHeChengItemFatherName[data.color]

		elseif index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			self.node_list["text_name"].text.text = Language.Equip.TianShenHeChengItemName[data.compose_equip_best_attr_num]
			self.node_list["text_name_hl"].text.text = Language.Equip.TianShenHeChengItemName[data.compose_equip_best_attr_num]
		end
	else
		self.node_list["text_name"].text.text = data.order .. Language.Equip.HeChengItemChileName1
		self.node_list["text_name_hl"].text.text = data.order .. Language.Equip.HeChengItemChileName1
	end
	local remind = false
	if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		remind = ComposeWGData.Instance:GetComposeShenShouSmallTypeRemind(data)
	else
		remind = EquipmentWGData.Instance:GetComposeEquipRemindBySmallType(data.title_index,
						data.compose_equip_best_attr_num, data.type, data.order)
	end
	self.node_list.remind:SetActive(remind)
end

function EquipmentHechengItemRender:CreateSelectEffect()
end

function EquipmentHechengItemRender:OnSelectChange(is_select)
	if self.node_list["img_bg"] then
		self.node_list["img_bg"]:SetActive(not is_select)
	end
	if self.node_list["img_bg_hl"] then
		self.node_list["img_bg_hl"]:SetActive(is_select)
	end
end

----------------------------------------------------
-- HechengPartRender
----------------------------------------------------
HechengPartRender = HechengPartRender or BaseClass(BaseRender)
function HechengPartRender:__init()

end

function HechengPartRender:__delete()
	if self.item_tip then
		self.item_tip:DeleteMe()
		self.item_tip = nil
	end
end
function HechengPartRender:LoadCallBack()
	self:CreateChild()
	self.node_list["EquipHeChengItem"].button:AddClickListener(BindTool.Bind(self.clickcell, self))
end

function HechengPartRender:CreateChild()
	self.item_tip = ItemCell.New(self.node_list["ph_hecheng_item_pos"])
	self.item_tip:SetIsShowTips(false)
end

function HechengPartRender:clickcell()
	BaseRender.OnClick(self)
end

function HechengPartRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	self.item_tip:ClearAllParts()
	self.node_list.remind:SetActive(false)
	if data.level then--神兽才有level
		self:OnShenShouFlush()
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if item_cfg == nil then
		return
	end

	self.node_list["lbl_hecheng_item_name"].text.text = item_cfg.name
	local param_t = {}
	param_t.star_level = data.star_level and data.star_level or data.compose_equip_best_attr_num
	self.item_tip:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
	self.item_tip:SetFlushCallBack()			-- 不能删，实现将神兽的那边的回调删除
	self.item_tip:SetData({item_id = data.item_id, param = param_t})
	-- if data.zhizun == 1 then
	-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	-- 	if item_cfg.color == GameEnum.ITEM_COLOR_RED then
	-- 		-- self.item_tip:SetQualityEffect(524)
	-- 	elseif item_cfg.color == GameEnum.ITEM_COLOR_PINK then
	-- 		-- self.item_tip:SetQualityEffect(525)
	-- 	end
	-- end
	self:SetCanComposeNum()

	self.item_tip:SetButtonComp(false)
	self.item_tip:SetCellBgEnabled(false)
	local remind = EquipmentWGData.Instance:GetCESingleRemindByData(data.item_id, data.compose_equip_best_attr_num)
	self.node_list.remind:SetActive(remind)
end

function HechengPartRender:OnShenShouFlush()
	if not self.data then
	  return
	end

	self.item_tip:ClearAllParts()
	self.item_tip:SetFlushCallBack(nil)
	local index = ComposeWGCtrl.Instance:GetShowIndex()

	if index == COMPOSE_TYPE.LING_CHONG then
		local shenshou_equip_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)	-- 获取神兽装备配置
		self.shenshou_equip_cfg = shenshou_equip_cfg
		if nil == shenshou_equip_cfg then
		  return
		end

		self.node_list["lbl_hecheng_item_name"].text.text = shenshou_equip_cfg.name
		local bundle_name, asset = ResPath.GetItem(shenshou_equip_cfg.icon_id)
		self.item_tip:SetItemTipFrom(ShenShouEquipTip.FROM_EQUIMENT_HECHENG)
		self.item_tip:SetData({item_id = self.data.item_id})
		self.item_tip:SetButtonComp(false)
		self.item_tip:SetCellBgEnabled(false)
		self.item_tip:SetUpFlagIconVisible(false)
		self.item_tip.root_node.image.raycastTarget = false
		self:SetCanComposeNum()
	elseif index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
		local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(self.data.item_id)	-- 获取神兽装备配置
		self.shenshou_equip_cfg = shenshou_equip_cfg
		if nil == shenshou_equip_cfg then
		  return
		end

		self.node_list["lbl_hecheng_item_name"].text.text = shenshou_equip_cfg.name
		local bundle_name, asset = ResPath.GetItem(shenshou_equip_cfg.icon_id)
		self.item_tip:SetItemIcon(bundle_name, asset)
		local compose_data = EquipmentWGData.Instance:GetComposeByVItemIdAndStar(self.data.item_id,self.data.compose_equip_best_attr_num)
		local real_item_id = ShenShouWGData.Instance:GetShenShouEqRealItemId(self.data.item_id)

		local color = shenshou_equip_cfg.quality
		local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_"..color)
		self.item_tip:SetCellBg(bundle, asset)

		self.item_tip:SetButtonComp(false)
		self.item_tip:SetFlushCallBack(function()
			self.item_tip.root_node.image.raycastTarget = false
			self.item_tip:SetLeftTopImg(compose_data.give_start_num)
			self.item_tip:SetButtonComp(false)
			self.item_tip:SetCellBgEnabled(false)
		end)
		self.item_tip:SetUpFlagIconVisible(true)
		self.item_tip:SetData({item_id = real_item_id,star_count = compose_data.give_start_num})

		self:SetCanComposeNum()

		local remind = ComposeWGData.Instance:GetComposeShenShouComposeWayRemind(self.data)
		self.node_list.remind:SetActive(remind)
	end
end


function HechengPartRender:CreateSelectEffect()
end

function HechengPartRender:GridItemOnCLick(index)
	-- local item_index = index + (self.rows - 1) * self.columns
	-- if nil ~= self.grid_call_back then
	-- 	self.grid_call_back(self.item_tip[index] ,item_index)
	-- end
	-- if nil ~= self.select_call_back then
	-- 	self.select_call_back(self.rows, index)
	-- end
end

function HechengPartRender:SetCanUseNumLbl(text_str)
	self.node_list["lbl_can_use_num"].text.text = text_str
end

function HechengPartRender:SetCanComposeNum()
	if not self.data then
	  return
	end

	local data = self.data
	-- if data.zhizun == 1 then
	-- 	--至尊装备
	-- 	local demand_data = EquipmentWGData.Instance:GetZhiZunEquipCfg(data.item_id)
	-- 	if demand_data ~= nil then
	-- 		local equip_list = EquipmentWGData.Instance:GetHechengZhiZunEquipmentItemList(demand_data)
	-- 		if equip_list ~= nil then
	-- 			-- self.node_list["lbl_can_use_num"]:SetActive(#equip_list>0)
	-- 			if #equip_list>0 then
	-- 				self:SetCanUseNumLbl(Language.Equip.HCItemContentTip5 .. #equip_list)
	-- 			else
	-- 				self:SetCanUseNumLbl(Language.Equip.HCItemContentTip5 .. "0")
	-- 			end
	-- 		else
	-- 			self:SetCanUseNumLbl(Language.Equip.HCItemContentTip5 .. "0")
	-- 		end
	-- 	end
	-- else
	local can_use_num = 0
	if data.level then
		local index = ComposeWGCtrl.Instance:GetShowIndex()
		if index == COMPOSE_TYPE.EQ_HECHENG_SHENSHOW then
			local demand_data = EquipmentWGData.Instance:GetSSEquinHechengItemData(data.compose_equip_best_attr_num, data.item_id)
			if demand_data ~= nil then
				local equip_list = EquipmentWGData.Instance:GetSSHechengEquipmentItemList(demand_data)
				can_use_num = equip_list ~= nil and #equip_list or 0
				self:SetCanUseNumLbl(Language.Equip.HCItemContentTip5 .. can_use_num)
			end
		end

		-- local compose_data = EquipmentWGData.Instance:GetComposeByVItemId(data.item_id)
		-- local shenshou_bag_filter_list = __TableCopy(ShenShouWGData.Instance:FilterShenShouEq(compose_data.need_qualit, compose_data.need_start_num))
		-- shenshou_bag_filter_list[0] = table.remove(self.shenshou_bag_filter_list, 1)
		-- self.item_list_view:SetDataList(shenshou_bag_filter_list, 3)
	else
		--普通装备
		can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(data.item_id, data.compose_equip_best_attr_num)
		self:SetCanUseNumLbl(Language.Equip.HCItemContentTip5 .. can_use_num)
	end
end
