----------------------------------------------------
-- 进度条，对原始进度条的封装，实现动画 2018-10-12 11:59:16
----------------------------------------------------
KfPVPProgressBar = KfPVPProgressBar or BaseClass()
KfPVPProgressBar.CountDownId = 0
function KfPVPProgressBar:__init()
	self.view = nil
	self.tail_effect = nil
	self.tail_effect_two = nil
	self.complete_callback = nil
	self.update_callback = nil
	self.is_tween = false
	self.width = 0
	self.height = 0
	self.start_percent = 0							-- 开始百分比
	self.cur_percent = 0							-- 当前百分比
	self.target_percent = 0							-- 目标百分比
	self.distance = 0								-- target_percent - start_percent
	self.cache_target_percent = nil
	self.max_val = 100

	self.total_time = 1								-- 0~100 总时间
	self.effect_offset_x = -10							-- 特效偏移值
	self.effect_offset_y = 0							-- 特效偏移值
end

function KfPVPProgressBar:__delete()
	self:RemoveCountDown()
	self.view = nil
end

function KfPVPProgressBar:SetSelfView( view,max_val )
	self.view = view
	if max_val then
		self:SetSliderMaxVal(max_val)
	else
		self:SetSliderMaxVal(100)
	end
end

function KfPVPProgressBar:GetView(view)
	return self.view
end

--删除尾部特效
function KfPVPProgressBar:DeleteTailEffect()
	if self.tail_effect then
		self.tail_effect:removeFromParent()
		self.tail_effect = nil
	end
end


function KfPVPProgressBar:SetTotalTime(total_time)
	self.total_time = total_time
end

function KfPVPProgressBar:GetCurPercent()
	return self.cur_percent
end

function KfPVPProgressBar:SetPercent(percent, is_tween, retreat)
	if nil == is_tween then is_tween = true end
	self.target_percent = percent
	if is_tween and self.total_time > 0 then
		self.cache_target_percent = nil
		if self.target_percent < self.cur_percent and not retreat then
			self.cache_target_percent = self.target_percent
			self.target_percent = 100
		end

		self:StartTween()
	else
		self:setPercent(percent)
		self:SetCurPercent(percent)
		-- self:UpdateTailEffectPosition()
	end
end

function KfPVPProgressBar:SetCurPercent( val )
	self.cur_percent = val
end

--设置slider进度条
function KfPVPProgressBar:setPercent(percent)
	percent = math.floor(percent)
	self.view.slider.value = percent

	if nil ~= self.update_callback then
		self.update_callback(percent)
	end

	if percent == self.max_val - 0.5 then
		if self.max_val_callback then
			self.max_val_callback()
		end
	end
end

function KfPVPProgressBar:SetMaxValueChangeCallBack( callback )
	self.max_val_callback = callback
end

function KfPVPProgressBar:SetSliderMaxVal( max_val )
	self.view.slider.maxValue = max_val
	self.max_val = max_val
end

function KfPVPProgressBar:SetCompleteCallback(complete_callback)
	self.complete_callback = complete_callback
end

function KfPVPProgressBar:SetUpdateCallback(update_callback)
	self.update_callback = update_callback
end

function KfPVPProgressBar:StartTween()
	self.start_percent = self.cur_percent
	self.distance = self.target_percent - self.cur_percent
	if math.abs(self.distance) <= 1 and self.cache_target_percent == nil then	-- 间隔太小不处理处画
		self:StopTween(true)
	else
		local tween_time = self.distance / 100 * self.total_time
		if tween_time <= 0 then tween_time = 0.1 end

		self:RemoveCountDown()
		KfPVPProgressBar.CountDownId = KfPVPProgressBar.CountDownId + 1
		self.countdown_id = KfPVPProgressBar.CountDownId

		CountDownManager.Instance:AddCountDown("KfPVPprogressbar" .. self.countdown_id, 
			BindTool.Bind1(self.OnTweening, self),
			BindTool.Bind1(self.StopTween, self), nil, tween_time, 0.01)

		self:IsPlayingAction(true)
	end

	-- self:UpdateTailEffectPosition()
end

--是否正在播放动画
function KfPVPProgressBar:IsPlayingAction( enable )
	if enable == nil then
		return self.is_playing_acion
	end
	self.is_playing_acion = enable
end

function KfPVPProgressBar:StopTween(is_ignore_limit)
	if nil == self.countdown_id and not is_ignore_limit then
		return
	end

	self:IsPlayingAction(false)
	self:RemoveCountDown()
	self:SetCurPercent(self.target_percent)
	if self.cache_target_percent ~= nil then --继续从头开始
		self.cur_percent = 0
		self.target_percent = self.cache_target_percent
		self.cache_target_percent = nil
		self:setPercent(0)
		-- self:UpdateTailEffectPosition()
		if self.target_percent > 0 then
			self:StartTween()
		end
	else
		self:setPercent(self.target_percent)
		-- self:UpdateTailEffectPosition()
		if self.complete_callback then
			self.complete_callback()
		end
	end
end

function KfPVPProgressBar:RemoveCountDown()
	if self.countdown_id ~= nil then
		CountDownManager.Instance:RemoveCountDown("KfPVPprogressbar" .. self.countdown_id)
		self.countdown_id = nil
	end
end

function KfPVPProgressBar:OnTweening(elapse_time, total_time)
	self:SetCurPercent(self.start_percent + elapse_time / total_time * self.distance)
	if (self.distance > 0 and self.cur_percent >= self.target_percent) 
		or (self.distance < 0 and self.cur_percent <= self.target_percent)  then
		self:StopTween()
	else
		self:setPercent(self.cur_percent)
		-- self:UpdateTailEffectPosition()
	end
end


function KfPVPProgressBar:UpdateTailEffectPosition()

end