FeedBackView = FeedBackView or BaseClass(SafeBaseView)

function FeedBackView:__init()
	self:SetMaskBg(false)
	--self:SetMaskBgAlpha(245 / 255)
	self.view_style = ViewStyle.Half
	self:AddViewResource(0, "uis/view/player_feedback_prefab", "layout_feedback")
end

function FeedBackView:__delete()

end

function FeedBackView:LoadCallBack(index, loaded_times)
	self.node_list["player_input"].input_field.onValueChanged:AddListener(BindTool.Bind(self.OnInputChange, self))
	self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.OnClickCloseView, self))
	self.node_list["btn_send"].button:AddClickListener(BindTool.Bind(self.OnClickSend, self))
	self.node_list["player_input"].input_field.text = ""
end

function FeedBackView:ReleaseCallBack()
	if self.feed_back_cell then
		self.feed_back_cell:DeleteMe()
		self.feed_back_cell = nil
	end
end

function FeedBackView:ShowIndexCallBack()
end

function FeedBackView:CloseCallBack()
	SettingWGData.Instance:ShowMainUiButton()
end

function FeedBackView:OnClickCloseView()
	self:Close()
end

function FeedBackView:OnInputChange(text)
	local max = self.node_list["player_input"].input_field.characterLimit
	local length = StringUtil.GetCharacterCount(self.node_list["player_input"].input_field.text)
	if length > max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.TextTooLong)
		return
	end
end

-- 发送
function FeedBackView:OnClickSend()
	local issue_subject = self.node_list["title_text"].text.text
	local issue_content = self.node_list["label_content"].text.text
	if "" == issue_subject then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.InputTitle)
		return
	end

	if "" == issue_content or " " == issue_content then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.InputContent)
		return
	end

	local list = {}
	local vo = GameVoManager.Instance:GetMainRoleVo()
	list.zone_id = GLOBAL_CONFIG.local_package_info.config.agent_id
	list.server_id = GameVoManager.Instance:GetUserVo().plat_server_id
	list.user_id = vo.role_id
	list.role_id = vo.role_id
	list.role_name = vo.role_name
	list.role_level = vo.level
	list.role_gold = vo.gold
	list.role_scene = vo.scene_id
	list.issue_type = 1
	list.issue_subject = issue_subject
	list.issue_content = issue_content
	SettingWGCtrl.Instance:SendRequest(list)
	self.node_list["player_input"].input_field.text = ""
	self:PlayerCloseAnim()
end

function FeedBackView:PlayerCloseAnim()
	self:OnClickCloseView()
	if self.is_first_send then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.FirstFeedBack)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Setting.FeedBackSucc)
	end

	SettingWGCtrl.Instance:RequestFeedBackReward()
end

function FeedBackView:OnFlush()
	local role_name = GameVoManager.Instance:GetMainRoleVo().role_name or ""
	self.node_list["player_name"].text.text = Language.Setting.SendLetterName .. role_name
	self.is_first_send = SettingWGData.Instance:GetFeedBackGetFlag()
	if not self.feed_back_cell then
		self.feed_back_cell = ItemCell.New(self.node_list["reward_cell"])
	end

	local feed_back_cfg = SettingWGData.Instance:GetFeedBackRewared()
	if IsEmptyTable(feed_back_cfg) then
		self.node_list["reward_cell"]:SetActive(false)
	else
		self.node_list["reward_cell"]:SetActive(true)
		self.feed_back_cell:SetData(feed_back_cfg)
	end

	if not self.is_first_send then
		self.node_list["had_get"]:SetActive(true)
	else
		self.node_list["had_get"]:SetActive(false)
	end
end
