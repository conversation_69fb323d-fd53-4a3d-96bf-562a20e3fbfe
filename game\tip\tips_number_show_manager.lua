TipsNumberShowManager = TipsNumberShowManager or BaseClass()

local MAXCOUNT = 6

function TipsNumberShowManager:__init()
	if TipsNumberShowManager.Instance ~= nil then
		print_error("[TipsNumberShowManager] attempt to create singleton twice!")
		return
	end

	TipsNumberShowManager.Instance = self
	self.next_time = 0.1
	self.pos_list = {}
	self.tips_list = {}
	self.index = 1
end

function TipsNumberShowManager:__delete()
	self.pos_list = {}

	for k,v in pairs(self.tips_list) do
		v:DeleteMe()
	end
	self.tips_list = {}

	if self.system_tips ~= nil then
		self.system_tips:DeleteMe()
		self.system_tips = nil
	end

	if self.system_getitem_tips ~= nil then
		self.system_getitem_tips:DeleteMe()
		self.system_getitem_tips = nil
	end

	TipsNumberShowManager.Instance = nil
end

function TipsNumberShowManager:RunShow()
	if #self.pos_list == 0 then
		GlobalTimerQuest:CancelQuest(self.show_num_tips)
		self.show_num_tips = nil
		return
	end

	local count = 0
	for i,v in ipairs(self.pos_list) do
		count = count + (#v.msg_list > 0 and 1 or 0)
	end

	if count > 0 and #self.tips_list > 19 and self.tips_list[count] and self.tips_list[count]:IsOpen() then
		for i = 1, 5 do
			if self.tips_list[i] then
				self.tips_list[i]:CloseTips()
			end
		end
		return
	end

	local cells = {}
	local cell
	local len = math.min(count, #self.tips_list)
	for i = 1, len do
		cell = self.tips_list[1]
		if cell and not cell:IsOpen() then
			table.insert(cells, cell)
			table.remove(self.tips_list, 1)
			table.insert(self.tips_list, cell)
		else
			break
		end
	end

	if #cells < count then
		local tips_cell
		local canvas_transform = TipsNumberShowManager.GetTipsNumberCanvas().transform
		local num = count - #cells
		for i = 1, num do
			local obj = ResPoolMgr:TryGetGameObject("uis/view/miscpre_load_prefab", "SystemNumberView", canvas_transform)
			tips_cell = TipsNumberShowView.New(obj)
			table.insert(self.tips_list, tips_cell)
			table.insert(cells, tips_cell)
		end
	end

	local has_cell
	for i,v in ipairs(self.pos_list) do
		if #v.msg_list > 0 then
			local msg = table.remove(v.msg_list, 1)
			local show_cell = table.remove(cells, 1)
			self:ChangeLayout(v.ui_layout)
			show_cell:Show(msg, nil, v.pos, 0)
			for k,v in pairs(v.show_list) do
				if show_cell == v then
					has_cell = true
				elseif v:SystemTipsVis() then
					v:AddIndex()
				end
			end

			if not has_cell then
				table.insert(v.show_list, show_cell)
			end
			return
		end
	end

	self:CheckRemivePosList()
end

function TipsNumberShowManager:CheckRemivePosList()
	local has_cell
	for i = 1, #self.pos_list do
		if self.pos_list[i] then
			has_cell = false
			for k,v in pairs(self.pos_list[i].show_list) do
				if v:SystemTipsVis() then
					has_cell = true
					break
				end
			end

			if not has_cell then
				table.remove(self.pos_list, i)
			end
		end
	end
end

function TipsNumberShowManager:DeleteCell(cell)
	for i,v in ipairs(self.tips_list) do
		if cell == v then
			table.remove(self.tips_list, i)
		end
		break
	end
end

--是否需要设置延时
function TipsNumberShowManager:ShowSystemTips(msg, speed, pos, fontSize, from, is_has_bg, ui_layout)
	if from then
		if not IsNil(MainCamera) and not MainCamera.enabled then
        	return
        end
    end

	if not self.show_num_tips then
		self.show_num_tips = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RunShow,self),speed or self.next_time)
	end

	pos = pos or Vector2(0, 0)
	for i,v in ipairs(self.pos_list) do
		if u3d.v2Sub(v.pos,pos).x == 0 and u3d.v2Sub(v.pos,pos).y == 0 then
			if #v.msg_list > 20 then
				table.remove(v.msg_list, 1)
			end
			table.insert(v.msg_list, {msg, fontSize, from, is_has_bg})
			return
		end
	end

	local data = {}
	data.pos = pos
	data.ui_layout = ui_layout
	data.msg_list = {{msg, fontSize, from, is_has_bg}}
	data.show_list = {}
	table.insert(self.pos_list,data)
end

function TipsNumberShowManager:CancleTeamPos(pos)
	if self.pos_list then
		local index
		for k,v in pairs(self.pos_list) do
			if u3d.v2Sub(v.pos,pos).x == 0 and u3d.v2Sub(v.pos,pos).y == 0 then
				index = k
			end
		end
		if index then
			self.pos_list[index] = nil
		end
	end
end

function TipsNumberShowManager:ChangeLayout(ui_layout)
	ui_layout = ui_layout or UiLayer.MainUIHigh
	local canvas = TipsNumberShowManager.GetTipsNumberCanvas()
	canvas.sortingOrder = ui_layout * 1000
end

function TipsNumberShowManager.GetTipsNumberCanvas()
	if nil == TipsNumberShowManager.floating_canvas then
		local obj = ResMgr:Instantiate(SafeBaseView.GetBaseViewParentTemplate())
		obj.name = "TipsNumberCanvas"
		obj:SetActive(true)

		local canvas = obj:GetComponent(typeof(UnityEngine.Canvas))
		canvas.overrideSorting = true
		canvas.sortingOrder = UiLayer.MainUIHigh * 1000
		canvas.worldCamera = UICamera

		local canvas_transform = canvas.transform
		obj:GetComponent(typeof(UnityEngine.UI.GraphicRaycaster)).enabled = false

		canvas_transform:SetParent(UILayer.transform, false)
		canvas_transform:SetLocalScale(1, 1, 1)

		local rect = canvas_transform:GetComponent(typeof(UnityEngine.RectTransform))
		rect.anchorMax = Vector2(1, 1)
		rect.anchorMin = Vector2(0, 0)
		rect.anchoredPosition3D = Vector3(0, 0, 0)
		rect.sizeDelta = Vector2(0, 0)

		TipsNumberShowManager.floating_canvas = canvas
	end

	return TipsNumberShowManager.floating_canvas
end