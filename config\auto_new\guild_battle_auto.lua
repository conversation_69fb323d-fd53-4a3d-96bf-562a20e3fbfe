-- X-仙盟战.xls
local item_table={
[1]={item_id=27740,num=50,is_bind=1},
[2]={item_id=30423,num=4,is_bind=1},
[3]={item_id=26349,num=8,is_bind=1},
[4]={item_id=26360,num=1,is_bind=1},
[5]={item_id=26415,num=8,is_bind=1},
[6]={item_id=36537,num=3500,is_bind=1},
[7]={item_id=27740,num=35,is_bind=1},
[8]={item_id=30423,num=3,is_bind=1},
[9]={item_id=26349,num=6,is_bind=1},
[10]={item_id=36537,num=2800,is_bind=1},
[11]={item_id=27740,num=25,is_bind=1},
[12]={item_id=26358,num=2,is_bind=1},
[13]={item_id=26415,num=6,is_bind=1},
[14]={item_id=36537,num=3000,is_bind=1},
[15]={item_id=27740,num=15,is_bind=1},
[16]={item_id=30423,num=2,is_bind=1},
[17]={item_id=26349,num=4,is_bind=1},
[18]={item_id=26358,num=1,is_bind=1},
[19]={item_id=36537,num=2400,is_bind=1},
[20]={item_id=27740,num=10,is_bind=1},
[21]={item_id=26415,num=5,is_bind=1},
[22]={item_id=36537,num=2000,is_bind=1},
[23]={item_id=27740,num=6,is_bind=1},
[24]={item_id=30423,num=1,is_bind=1},
[25]={item_id=26349,num=2,is_bind=1},
[26]={item_id=36537,num=1600,is_bind=1},
[27]={item_id=27740,num=100,is_bind=1},
[28]={item_id=26415,num=10,is_bind=1},
[29]={item_id=36537,num=3200,is_bind=1},
[30]={item_id=27740,num=70,is_bind=1},
[31]={item_id=36537,num=2600,is_bind=1},
[32]={item_id=26349,num=5,is_bind=1},
[33]={item_id=36537,num=2200,is_bind=1},
[34]={item_id=26349,num=3,is_bind=1},
[35]={item_id=36537,num=1900,is_bind=1},
[36]={item_id=26349,num=1,is_bind=1},
[37]={item_id=36537,num=1300,is_bind=1},
[38]={item_id=50076,num=2,is_bind=1},
[39]={item_id=26191,num=5,is_bind=1},
[40]={item_id=48186,num=4,is_bind=1},
[41]={item_id=48186,num=3,is_bind=1},
[42]={item_id=50076,num=1,is_bind=1},
[43]={item_id=26191,num=4,is_bind=1},
[44]={item_id=48186,num=2,is_bind=1},
[45]={item_id=26191,num=3,is_bind=1},
[46]={item_id=48186,num=1,is_bind=1},
[47]={item_id=26191,num=2,is_bind=1},
[48]={item_id=26191,num=1,is_bind=1},
[49]={item_id=27713,num=1,is_bind=1},
[50]={item_id=22895,num=1,is_bind=1},
[51]={item_id=22915,num=1,is_bind=1},
[52]={item_id=22000,num=1,is_bind=1},
[53]={item_id=23628,num=288,is_bind=0},
[54]={item_id=29409,num=1,is_bind=1},
[55]={item_id=22733,num=1,is_bind=1},
[56]={item_id=30423,num=5,is_bind=1},
[57]={item_id=26349,num=10,is_bind=1},
[58]={item_id=26360,num=2,is_bind=1},
[59]={item_id=36537,num=4000,is_bind=1},
[60]={item_id=50076,num=3,is_bind=1},
[61]={item_id=26191,num=6,is_bind=1},
[62]={item_id=48186,num=5,is_bind=1},
[63]={item_id=37306,num=1,is_bind=1},
[64]={item_id=27740,num=1,is_bind=1},
[65]={item_id=26415,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
monster_cfg={
{},
{side=1,npc_id=1001,npc_pos_x=72,npc_pos_y=156,}
},

monster_cfg_meta_table_map={
},
win_reward_show={
{},
{seq=1,day_min=30,day_max=60,},
{seq=2,day_min=60,day_max=90,},
{seq=3,day_min=90,day_max=130,},
{seq=4,day_min=130,day_max=190,},
{seq=5,day_min=190,day_max=270,},
{seq=6,day_min=270,day_max=9999,}
},

win_reward_show_meta_table_map={
},
rank_reward={
{win_jingyan=10,failure_jingyan=8,},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},show_reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},win_banggong=3500,failure_reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[4],[4]=item_table[5]},failure_banggong=2800,show_failure_reward_item={[0]=item_table[7],[1]=item_table[8],[2]=item_table[9],[3]=item_table[4],[4]=item_table[5],[5]=item_table[10]},},
{min_rank=3,max_rank=3,reward_item={[0]=item_table[11],[1]=item_table[8],[2]=item_table[9],[3]=item_table[12],[4]=item_table[13]},show_reward_item={[0]=item_table[11],[1]=item_table[8],[2]=item_table[9],[3]=item_table[12],[4]=item_table[13],[5]=item_table[14]},win_banggong=3000,failure_reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[13]},failure_banggong=2400,show_failure_reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[13],[5]=item_table[19]},},
{min_rank=4,max_rank=120,reward_item={[0]=item_table[20],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[21]},show_reward_item={[0]=item_table[20],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[21],[5]=item_table[22]},win_banggong=2000,win_jingyan=6,failure_reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[25],[3]=item_table[18],[4]=item_table[21]},failure_banggong=1600,failure_jingyan=5,show_failure_reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[25],[3]=item_table[18],[4]=item_table[21],[5]=item_table[26]},},
{zone=1,reward_item={[0]=item_table[27],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[28]},show_reward_item={[0]=item_table[27],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[28],[5]=item_table[29]},win_banggong=3200,failure_reward_item={[0]=item_table[30],[1]=item_table[8],[2]=item_table[9],[3]=item_table[4],[4]=item_table[28]},failure_banggong=2600,show_failure_reward_item={[0]=item_table[30],[1]=item_table[8],[2]=item_table[9],[3]=item_table[4],[4]=item_table[28],[5]=item_table[31]},},
{min_rank=2,max_rank=2,reward_item={[0]=item_table[1],[1]=item_table[8],[2]=item_table[9],[3]=item_table[12],[4]=item_table[5]},show_reward_item={[0]=item_table[1],[1]=item_table[8],[2]=item_table[9],[3]=item_table[12],[4]=item_table[5],[5]=item_table[10]},win_banggong=2800,failure_reward_item={[0]=item_table[7],[1]=item_table[16],[2]=item_table[32],[3]=item_table[18],[4]=item_table[5]},failure_banggong=2200,show_failure_reward_item={[0]=item_table[7],[1]=item_table[16],[2]=item_table[32],[3]=item_table[18],[4]=item_table[5],[5]=item_table[33]},},
{zone=1,min_rank=3,max_rank=3,reward_item={[0]=item_table[11],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[13]},show_reward_item={[0]=item_table[11],[1]=item_table[16],[2]=item_table[17],[3]=item_table[18],[4]=item_table[13],[5]=item_table[19]},win_banggong=2400,win_jingyan=6,failure_reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[34],[3]=item_table[18],[4]=item_table[13]},failure_banggong=1900,failure_jingyan=5,show_failure_reward_item={[0]=item_table[15],[1]=item_table[16],[2]=item_table[34],[3]=item_table[18],[4]=item_table[13],[5]=item_table[35]},},
{zone=1,min_rank=4,max_rank=120,reward_item={[0]=item_table[20],[1]=item_table[24],[2]=item_table[25],[3]=item_table[18],[4]=item_table[21]},show_reward_item={[0]=item_table[20],[1]=item_table[24],[2]=item_table[25],[3]=item_table[18],[4]=item_table[21],[5]=item_table[26]},win_banggong=1600,win_jingyan=5,failure_reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[36],[3]=item_table[18],[4]=item_table[21]},failure_banggong=1300,failure_jingyan=4,show_failure_reward_item={[0]=item_table[23],[1]=item_table[24],[2]=item_table[36],[3]=item_table[18],[4]=item_table[21],[5]=item_table[37]},}
},

rank_reward_meta_table_map={
[6]=5,	-- depth:1
[2]=1,	-- depth:1
},
guild_rank_reward={
{},
{rank_min=2,rank_max=2,reward_id=2,show_reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[40]},},
{rank_min=3,rank_max=3,reward_id=3,show_reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[41]},},
{rank_min=4,rank_max=4,reward_id=4,show_reward_item={[0]=item_table[42],[1]=item_table[43],[2]=item_table[44]},},
{rank_min=5,rank_max=5,reward_id=5,show_reward_item={[0]=item_table[42],[1]=item_table[45],[2]=item_table[46]},},
{rank_min=6,rank_max=6,reward_id=6,show_reward_item={[0]=item_table[42],[1]=item_table[47]},},
{rank_min=7,rank_max=7,reward_id=7,show_reward_item={[0]=item_table[47]},},
{rank_min=8,rank_max=8,reward_id=8,show_reward_item={[0]=item_table[48]},}
},

guild_rank_reward_meta_table_map={
},
guild_rank_reward_id={
{},
{reward_id=2,reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[40]},},
{reward_id=3,reward_item={[0]=item_table[38],[1]=item_table[39],[2]=item_table[41]},},
{reward_id=4,reward_item={[0]=item_table[42],[1]=item_table[43],[2]=item_table[44]},},
{reward_id=5,reward_item={[0]=item_table[42],[1]=item_table[45],[2]=item_table[46]},},
{reward_id=6,reward_item={[0]=item_table[42],[1]=item_table[47]},},
{reward_id=7,reward_item={[0]=item_table[47]},},
{reward_id=8,reward_item={[0]=item_table[48]},}
},

guild_rank_reward_id_meta_table_map={
},
keep_win_reward_new={
{},
{win_count=3,},
{win_count=4,},
{win_count=5,},
{win_count=6,},
{win_count=7,},
{win_count=8,},
{win_count=9,win_reward_item={},},
{win_count=10,},
{win_count=11,},
{win_count=12,},
{win_count=13,},
{win_count=14,},
{win_count=15,},
{win_count=16,},
{win_count=17,},
{win_count=18,},
{win_count=19,},
{win_count=20,},
{opengame_day=9999,},
{win_count=3,},
{win_count=4,},
{win_count=5,},
{win_count=6,},
{win_count=7,},
{win_count=8,},
{win_count=9,},
{win_count=10,},
{opengame_day=9999,},
{win_count=12,},
{win_count=13,},
{win_count=14,},
{win_count=15,},
{win_count=16,},
{win_count=17,},
{opengame_day=9999,},
{win_count=19,},
{win_count=20,}
},

keep_win_reward_new_meta_table_map={
[36]=17,	-- depth:1
[34]=36,	-- depth:2
[32]=34,	-- depth:3
[30]=32,	-- depth:4
[28]=30,	-- depth:5
[26]=28,	-- depth:6
[24]=26,	-- depth:7
[22]=24,	-- depth:8
[38]=22,	-- depth:9
[2]=8,	-- depth:1
[10]=2,	-- depth:2
[6]=10,	-- depth:3
[12]=6,	-- depth:4
[4]=12,	-- depth:5
[14]=4,	-- depth:6
[16]=14,	-- depth:7
[18]=16,	-- depth:8
[29]=10,	-- depth:3
[21]=29,	-- depth:4
[31]=21,	-- depth:5
[25]=31,	-- depth:6
[33]=25,	-- depth:7
[35]=33,	-- depth:8
[37]=35,	-- depth:9
[27]=37,	-- depth:10
[23]=27,	-- depth:11
},
title={
{},
{title_name="神一般的存在",title_res=9011,}
},

title_meta_table_map={
},
show_reward={
{auction_reward_item={[0]={item_id=5128,num=1,is_bind=1,param0=1,param1=4},[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},},
{opengame_day_min=3,opengame_day_max=4,},
{opengame_day_min=4,opengame_day_max=5,},
{opengame_day_min=5,opengame_day_max=7,auction_reward_item={[0]={item_id=5134,num=1,is_bind=1,param0=1,param1=4},[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},},
{opengame_day_min=7,opengame_day_max=14,auction_reward_item={[0]={item_id=5140,num=1,is_bind=1,param0=1,param1=4},[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},},
{opengame_day_min=14,opengame_day_max=15,auction_reward_item={[0]={item_id=5146,num=1,is_bind=1,param0=1,param1=4},[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},},
{opengame_day_min=15,opengame_day_max=21,},
{opengame_day_min=21,opengame_day_max=22,auction_reward_item={[0]={item_id=5152,num=1,is_bind=1,param0=1,param1=4},[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},},
{opengame_day_min=22,opengame_day_max=28,},
{opengame_day_min=28,opengame_day_max=35,},
{opengame_day_min=35,opengame_day_max=36,},
{opengame_day_min=36,opengame_day_max=42,auction_reward_item={[0]={item_id=5158,num=1,is_bind=1,param0=1,param1=4},[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},},
{opengame_day_min=42,opengame_day_max=49,auction_reward_item={[0]={item_id=5164,num=1,is_bind=1,param0=1,param1=4},[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},},
{opengame_day_min=49,opengame_day_max=50,},
{opengame_day_min=50,opengame_day_max=56,},
{opengame_day_min=56,opengame_day_max=63,},
{opengame_day_min=63,opengame_day_max=70,},
{opengame_day_min=70,opengame_day_max=77,},
{opengame_day_min=77,opengame_day_max=84,},
{opengame_day_min=84,opengame_day_max=91,},
{opengame_day_min=91,opengame_day_max=98,},
{opengame_day_min=98,opengame_day_max=9999,}
},

show_reward_meta_table_map={
[9]=8,	-- depth:1
[10]=8,	-- depth:1
[14]=13,	-- depth:1
[15]=13,	-- depth:1
[16]=13,	-- depth:1
[3]=4,	-- depth:1
[2]=4,	-- depth:1
[7]=6,	-- depth:1
[11]=12,	-- depth:1
},
battle_rule={
{rule_perent_index=1,title="参赛规则",},
{rule_index=2,child_title="二跨服争霸",content="由每个服仙盟战力排名<color=#99ffbb>前2名</color>的仙盟获得。",},
{rule_index=3,child_title="四跨服争霸",content="由每个服仙盟排名<color=#99ffbb>第1名</color>的仙盟获得。",},
{rule_index=4,child_title="八跨服争霸",content="由每个服仙盟排名<color=#99ffbb>前2名</color>的仙盟获得。",},
{child_title="BOSS",content="蓝方守护BOSS于战场开启<color=#99ffbb>1分钟</color>刷新，红方守护BOSS于战场开启后<color=#99ffbb>第7分钟</color>刷新，BOSS存活<color=#99ffbb>4分钟</color>\n本方BOSS到时间未被击杀，则本方增加100争夺值\n本方BOSS被成功击杀，则敌方增加100争夺值",img_id=1,},
{rule_index=2,child_title="灵石",content="场景内的固定点会刷新出灵石，成功采集灵石可增加争夺值，被击杀时打断当前采集",img_id=2,},
{rule_index=3,child_title="个人积分",content="攻击BOSS，采集灵石，击杀小怪，击杀敌对玩家均可获得个人积分，提升战场排名",img_id=4,},
{rule_index=4,child_title="争霸奖励",content="活动结束后，根据个人积分排名，获得奖励，排名越高，奖励越丰厚哦！\n仙盟排名奖励和连胜奖励将通过拍卖的方式进行分配",},
{rule_index=5,child_title="胜负判断",content="优先达到<color=#99ffbb>350争夺值</color>或者活动结束争夺值高的仙盟获胜\n争夺值通过守护本方BOSS，击杀敌方BOSS，采集灵石获得",img_id=1,}
},

battle_rule_meta_table_map={
[2]=1,	-- depth:1
[3]=2,	-- depth:2
[4]=2,	-- depth:2
},
continue_kill_cfg={
{},
{kill_count=5,kill_desc="%s已完成五杀！",},
{kill_count=7,kill_desc="%s七杀绝世，疯魔！",},
{kill_count=10,kill_desc="%s超神十连，杀戮死神！",}
},

continue_kill_cfg_meta_table_map={
},
lingshi={
[2023]={lingshi_gather=2023,},
[2024]={lingshi_gather=2024,commit_reward_value=30,hs_reward_value=2,name="时空灵石",stone_icon="xmz_che_stone2",lingshi_icon="x_jingshi2",icon="xcheng",color=5,hs_xs_id=3,blue_hs_xs_resid=6240001,red_hs_xs_resid=6241001,effect_bunble="effects2/prefab/misc/effect_6312001_chuchang_cheng_01_prefab",effect_asset="effect_6312001_chuchang_cheng_01",}
},

lingshi_meta_table_map={
},
lingshi_pos={
{},
{lingshi_gather=2024,pos_y=190,}
},

lingshi_pos_meta_table_map={
},
one_match_reward={
{},
{zone=1,match_win_guild_exp=2000,match_win_red_level=1,match_fail_guild_exp=1000,}
},

one_match_reward_meta_table_map={
},
boss_cfg={
{zone=0,boss_id=45007,refresh_time=60,},
{zone=1,boss_id=45008,attack_score=4,kill_score=100,kill_value=100,defend_value=100,refresh_time=420,live_time=240,revive_time=9999,},
{boss_pos_y=43,},
{boss_pos_y=50,},
{boss_pos_y=52,},
{boss_pos_x=132,boss_pos_y=56,},
{boss_pos_x=128,},
{boss_pos_x=134,},
{boss_pos_y=32,},
{boss_pos_y=37,},
{boss_pos_y=39,},
{boss_pos_y=44,},
{boss_pos_y=47,},
{boss_pos_y=54,},
{boss_pos_x=148,boss_pos_y=35,},
{boss_pos_x=140,boss_pos_y=29,},
{boss_pos_x=143,boss_pos_y=260,},
{boss_pos_x=139,boss_pos_y=256,},
{boss_pos_x=146,boss_pos_y=255,},
{boss_pos_x=151,boss_pos_y=258,},
{boss_pos_x=152,boss_pos_y=264,},
{boss_pos_x=148,},
{boss_pos_x=136,boss_pos_y=270,},
{boss_pos_x=130,boss_pos_y=273,},
{boss_pos_x=126,boss_pos_y=261,},
{boss_pos_x=133,boss_pos_y=248,},
{boss_pos_x=141,boss_pos_y=242,},
{boss_pos_x=154,boss_pos_y=247,},
{boss_pos_x=162,},
{boss_pos_x=160,boss_pos_y=271,}
},

boss_cfg_meta_table_map={
[22]=23,	-- depth:1
[29]=18,	-- depth:1
[14]=21,	-- depth:1
[13]=14,	-- depth:2
[12]=28,	-- depth:1
[11]=13,	-- depth:3
[10]=22,	-- depth:2
[8]=15,	-- depth:1
[7]=3,	-- depth:1
[5]=10,	-- depth:3
[4]=8,	-- depth:2
[1]=2,	-- depth:1
},
other_default_table={role_level=160,round_interval_min=12,round_rest_min=4,rank_update_interval_s=1,show_papertime="20|57",first_round="20:30-20:42",second_round="20:46-20:58",win_directly_reward_item={[0]=item_table[52]},forward_day=3,forward_item=item_table[53],show_reward_item={[0]=item_table[54],[1]=item_table[36],[2]=item_table[55],[3]=item_table[24],[4]=item_table[46]},foreshow_level=110,activity_prepare_time=180,get_reward_count=5,cap_rank_count=20,skill_list="922|920|921",bianshen_count=2,bianshen_monster_id=1,kill_robot_score=16,win_score=350,gather_effect_dur_times=3,gather_effect_flush_times=3,cancel_car_skill=922,rule_desc="1.采集灵石，击杀BOSS，可获得争夺值\n3.争夺值决定仙盟的胜负，若一方争夺值达到350分则直接胜利\4.击杀敌方玩家、采集灵石、攻击BOSS均可以获得个人积分，个人积分越高奖励越好",caiji_skill_icon="xmz_car_common2",tijiao_skill_icon="xmz_car_common1",blue_hs_kong_resid=6243001,red_hs_kong_resid=6242001,red_caiji_resid=6244001,blue_caiji_resid=6245001,},

monster_cfg_default_table={side=0,npc_id=1002,npc_pos_x=222,npc_pos_y=158,guaji_pos_x=142,guaji_pos_y=148,bs_npc_talk="<color=#fffc00>获得减伤：</color>受到所有伤害为<color=#95d12b>固定伤害</color>\n<color=#fffc00>眩晕技能：</color>使周围目标眩晕<color=#95d12b>1</color>秒，冷却<color=#95d12b>60</color>秒\n<color=#fffc00>回血技能：</color>每秒恢复<color=#95d12b>10%</color>血量，冷却<color=#95d12b>30</color>秒\n<color=#95d12b>（建议低战玩家变身）</color>",give_npc_talk="上交灵石可增加仙盟争夺值，离仙盟获胜更进一步！",not_stone_npc_talk="请仙友优先采集神灵石，成功采集之后再来上交！",},

win_reward_show_default_table={seq=0,day_min=0,day_max=30,},

rank_reward_default_table={opengame_day=9999,zone=0,min_rank=1,max_rank=1,reward_item={[0]=item_table[27],[1]=item_table[56],[2]=item_table[57],[3]=item_table[58],[4]=item_table[28]},show_reward_item={[0]=item_table[27],[1]=item_table[56],[2]=item_table[57],[3]=item_table[58],[4]=item_table[28],[5]=item_table[59]},win_bind_gold=0,win_banggong=4000,win_guild_exp=0,win_shengwang=0,win_jingyan=8,failure_reward_item={[0]=item_table[30],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[28]},failure_bind_gold=0,failure_banggong=3200,failure_guild_exp=0,failure_shengwang=0,failure_jingyan=6,show_failure_reward_item={[0]=item_table[30],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[28],[5]=item_table[29]},},

guild_rank_reward_default_table={fuzhu=1,opengame_day=999,guild_level=1,rank_min=1,rank_max=1,reward_id=1,show_reward_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[62]},},

guild_rank_reward_id_default_table={reward_id=1,reward_item={[0]=item_table[60],[1]=item_table[61],[2]=item_table[62]},},

keep_win_reward_new_default_table={opengame_day=7,win_count=2,win_reward_item={[0]=item_table[63]},},

title_default_table={title_name="无人能挡",title_res=9007,},

show_reward_default_table={opengame_day_min=1,opengame_day_max=3,auction_reward_item={[0]={item_id=5170,num=1,is_bind=1,param0=1,param1=4},[1]=item_table[49],[2]=item_table[50],[3]=item_table[51]},show_reward_item={[0]=item_table[64],[1]=item_table[4],[2]=item_table[18],[3]=item_table[65],[4]=item_table[24],[5]=item_table[36]},},

battle_rule_default_table={rule_perent_index=2,rule_index=1,title="争霸规则",child_title="本服争霸",content="本服仙盟争霸战参赛资格由全服仙盟总战力排名<color=#99ffbb>前4名</color>获得。",img_id=3,},

continue_kill_cfg_default_table={type=1,kill_count=3,reward_score=0,is_news=0,kill_desc="%s已完成三杀！",},

lingshi_default_table={lingshi_gather=2023,commit_reward_value=20,hs_reward_value=1,name="虚空灵石",stone_icon="xmz_che_stone1",lingshi_icon="x_jingshi1",icon="x_zi",color=4,hs_xs_id=2,blue_hs_xs_resid=6238001,red_hs_xs_resid=6239001,effect_bunble="effects2/prefab/misc/effect_6313001_chuchang_zi_01_prefab",effect_asset="effect_6313001_chuchang_zi_01",},

lingshi_pos_default_table={lingshi_gather=2023,pos_x=142,pos_y=108,},

one_match_reward_default_table={zone=0,match_win_guild_exp=4000,match_win_red_level=0,match_fail_guild_exp=2000,match_fail_red_level=-1,},

boss_cfg_default_table={zone=2,boss_id=45009,boss_pos_x=142,boss_pos_y=148,attack_score=0,kill_score=8,kill_value=0,defend_value=0,refresh_time=0,live_time=9999,revive_time=5,}

}

