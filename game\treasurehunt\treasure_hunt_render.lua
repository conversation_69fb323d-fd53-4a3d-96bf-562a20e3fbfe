--寻宝世界记录render
RecordItem = RecordItem or BaseClass(BaseRender)
function RecordItem:__init()

end

function RecordItem:__delete()
end

function RecordItem:OnFlush()
	if not self.data then
		return
    end
    local shop_type_str = Language.TreasureHunt.TreasureNameByMode[self.data.shop_mode + 2]
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)
    if item_cfg and item_cfg.name then
        self.node_list.rich_content.text.text = string.format(Language.TreasureHunt.WorldRecordDes,
        self.data.role_name, shop_type_str, ITEM_COLOR[item_cfg.color], item_cfg.name, self.data.item_data.num)
    else
        self.node_list.rich_content.text.text = ""
        print_error("the itemcfg is nil, item_id = ", self.data.item_data.item_id)
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.rich_content.rect)
    self.node_list.ph_record_show_item.rect.sizeDelta = Vector2(296, self.node_list.rich_content.rect.sizeDelta.y) 
end

RecordPersonItem = RecordPersonItem or BaseClass(BaseRender)
function RecordPersonItem:__init()

end

function RecordPersonItem:__delete()
end

function RecordPersonItem:OnFlush()
	if not self.data then
		return
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)
    self.node_list.rich_content.text.text = string.format(Language.TreasureHunt.PersonRecordDes, ITEM_COLOR[item_cfg.color], item_cfg.name ,self.data.item_data.num)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.rich_content.rect)
    self.node_list.ph_record_show_item.rect.sizeDelta = Vector2(296, self.node_list.rich_content.rect.sizeDelta.y) 
end


--寻宝秘闻render
RecordMiwenItem = RecordMiwenItem or BaseClass(BaseRender)
function RecordMiwenItem:__init()

end

function RecordMiwenItem:__delete()
end

function RecordMiwenItem:OnFlush()
	if not self.data then
		return
    end
    local shop_type_str = Language.TreasureHunt.TreasureNameByMode[self.data.shop_mode + 2]
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_data.item_id)
    if self.data.role_id then
        if item_cfg and item_cfg.name then
            self.node_list.rich_content.text.text = string.format(Language.TreasureHunt.WorldRecordDes,
            self.data.role_name, shop_type_str, ITEM_COLOR[item_cfg.color], item_cfg.name, self.data.item_data.num)
        else
            self.node_list.rich_content.text.text = ""
            --print_error("the itemcfg is nil, item_id = ", self.data.item_data.item_id)
        end
    else
        self.node_list.rich_content.text.text = ""
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.rich_content.rect)
    self.node_list.miwen_list_render.rect.sizeDelta = Vector2(296, self.node_list.rich_content.rect.sizeDelta.y) 
end


--寻宝周奖励render
RecordRewardItem = RecordRewardItem or BaseClass(BaseRender)
function RecordRewardItem:__init()

end

function RecordRewardItem:ReleaseCallBack()
    if self.item_reward then
        self.item_reward:DeleteMe()
        self.item_reward = nil
    end
end

function RecordRewardItem:LoadCallBack()
    self.node_list.btn_get.button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
end

function RecordRewardItem:OnFlush()
	if not self.data then
		return
    end
    if not self.node_list.des_text then
		return
    end
    if not self.item_reward then
        self.item_reward = ItemCell.New(self.node_list.cell_pos)
    end
    self.item_reward:SetData({item_id = self.data.reward_item_id, num = self.data.num, is_bind = self.data.is_bind})
    local cur = self.data.total_week_count
    if self.data.total_week_count <  self.data.count then
        cur = ToColorStr(self.data.total_week_count, COLOR3B.RED)
    end
    self.node_list.des_text.text.text = string.format(Language.TreasureHunt.RewardGetDes, cur, self.data.count)
    XUI.SetButtonEnabled(self.node_list.btn_get, self.data.is_get == 0 and self.data.total_week_count >= self.data.count)
    self.node_list.remind:SetActive(false)
    if self.data.is_get == 0 then
        if self.data.total_week_count >= self.data.count then
            self.node_list.remind:SetActive(true)
            self.node_list.btn_text.text.text = Language.TreasureHunt.CanGet
        else            
            self.node_list.btn_text.text.text = Language.TreasureHunt.Get
        end
    else
        self.node_list.btn_text.text.text = Language.TreasureHunt.HadGet
    end
end


function RecordRewardItem:OnClickGet()
    TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_FETCH_WEEK_REWARD, self.data.shop_type -1, self.index - 1)
end



--铭纹寻宝周奖励render
MingWenRewardItem = MingWenRewardItem or BaseClass(BaseRender)
function MingWenRewardItem:__init()

end

function MingWenRewardItem:ReleaseCallBack()
    if self.item_reward then
        self.item_reward:DeleteMe()
        self.item_reward = nil
    end
end

function MingWenRewardItem:LoadCallBack()
    self.node_list.button.button:AddClickListener(BindTool.Bind1(self.OnClickItem, self))
end


function MingWenRewardItem:OnFlush()
	if not self.data then
		return
    end
    if not self.node_list.cell_pos then
		return
    end
    if not self.item_reward then
        self.item_reward = ItemCell.New(self.node_list.cell_pos)
        self.item_reward:SetIsShowTips(false)
    end
    self.item_reward:SetData(self.data.item)
    self.item_reward:SetBindIconVisible(false)

    if self.data.is_get == 1 then
        self.item_reward:SetDefaultEff(false)
    end
    self.node_list.times.text.text = string.format(Language.TreasureHunt.CiShu, self.data.count)
    self.node_list.had_get:SetActive(self.data.is_get == 1)
    self.node_list.red:SetActive(self.data.is_get == 0 and self.data.total_week_count >= self.data.count)
end

function MingWenRewardItem:OnClickItem()
    if self.data.is_get == 0 then
        if self.data.total_week_count >= self.data.count then
            TreasureHuntWGCtrl.Instance:DoMingWenOpera(TreasureHuntWGData.POSY_OPERATOR_TYPE.POSY_SHOP_OPERATOR_TYPE_WEEK_REWARD, self.data.index)
        else
            --SysMsgWGCtrl.Instance:ErrorRemind(Language.TreasureHunt.TreasureTimesNotEnough)
            TipWGCtrl.Instance:OpenItem({item_id = tonumber(self.data.item.item_id)})
        end
    else
        TipWGCtrl.Instance:OpenItem({item_id = tonumber(self.data.item.item_id)})
    end
end

local effect_list = {
    [GameEnum.ITEM_COLOR_ORANGE] = "UI_fuwen_kuang_00", --橙色
    [GameEnum.ITEM_COLOR_RED] = "UI_fuwen_kuang_01", --红色
}

--铭文寻宝世界记录render
MingwenRecordItem = MingwenRecordItem or BaseClass(BaseRender)

function MingwenRecordItem:OnFlush()
	if not self.data then
		return
    end
    -- self.node_list.bg:SetActive((self:GetIndex() % 2) == 1)
    local shop_type_str = Language.TreasureHunt.TreasureNameByMode[1]
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg and item_cfg.name then
        self.node_list.rich_content.tmp.text = string.format(Language.TreasureHunt.MingwenWorldRecordDes,
        self.data.role_name, shop_type_str, ITEM_COLOR[item_cfg.color], item_cfg.name, 1)
    else
        self.node_list.rich_content.tmp.text = ""
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.rich_content.rect)
end



MingwenRecordPersonItem = MingwenRecordPersonItem or BaseClass(BaseRender)

function MingwenRecordPersonItem:OnFlush()
	if not self.data then
		return
    end
    --  self.node_list.bg:SetActive((self:GetIndex() % 2) == 1)
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)

    if item_cfg and item_cfg.name then
        local time = os.date("*t",self.data.timestamp)
        local time_str = string.format("%02d-%02d %02d:%02d ", time.month, time.day, time.hour, time.min)
        self.node_list.rich_content.tmp.text = time_str.. string.format(Language.TreasureHunt.PersonRecordDes, 
        ITEM_COLOR[item_cfg.color], item_cfg.name, 1)
    else
        self.node_list.rich_content.tmp.text = ""
    end
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.rich_content.rect)
end


-- --寻宝展示模型
-- TreasureHuntModelRender = TreasureHuntModelRender or BaseClass(BaseRender)
-- function TreasureHuntModelRender:LoadCallBack()
--     self.node_list["click_btn1"].button:AddClickListener(BindTool.Bind(self.OnClickModel,self))
-- end

-- function TreasureHuntModelRender:OnFlush()
--     if self.data and not IsEmptyTable(self.data) then
--         local item_cfg = ItemWGData.Instance:GetItemConfig(tonumber(self.data[2]))
--         local bundle, asset = ResPath.GetTreasurehuntIcon("a2_xb_" .. tonumber(self.data[2]))
--         self.node_list.icon.image:LoadSprite(bundle, asset, function()
--             self.node_list.icon.image:SetNativeSize()
--         end)
--         local eff_bundle, eff_asset = ResPath.GetWuPinKuangEffectUi(BaseCell_Ui_Effect[item_cfg.zhengui_effect])
--         if eff_bundle and eff_asset then
--             self.node_list["effect"]:SetActive(true)
--             self.node_list.effect:ChangeAsset(eff_bundle, eff_asset)
--         else
--             self.node_list["effect"]:SetActive(false)
--         end
--     end
-- end

-- function TreasureHuntModelRender:OnClickModel()
--     if self.data and self.data[2] then
--         local item_cfg,item_type = ItemWGData.Instance:GetItemConfig(tonumber(self.data[2]))
--         if item_type == GameEnum.ITEM_BIGTYPE_GIF then
--             local is_special_gift = ItemWGData.Instance:IsSpecialRandGift(item_cfg.id)
--             if is_special_gift then
--                 local change_data = ItemWGData.Instance:GetGiftItemConvertibleData({item_id = item_cfg.id,num = 1})
--                 TipWGCtrl.Instance:OpenItem(change_data)
--                 return
--             end
--         end
--         TipWGCtrl.Instance:OpenItem({item_id = tonumber(self.data[2])})
--     end 
-- end

