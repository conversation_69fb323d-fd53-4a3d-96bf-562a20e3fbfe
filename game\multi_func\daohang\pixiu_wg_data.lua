MultiFunctionWGData = MultiFunctionWGData or BaseClass()

function MultiFunctionWGData:InitPiXiuCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("taoist_cfg_auto")
	self.daohang_zhigou_cfg = cfg.rmb_buy_privilege
	self.daohang_dati_cfg = cfg.question
	self.daohang_privilege_cfg = cfg.privilege
end

function MultiFunctionWGData:DeletePiXiuData()

end

function MultiFunctionWGData:GetDaoHangHolySealRemind()
	local rmb_cfg = MultiFunctionWGData.Instance:GetDaoHangCurRmbBuyCfg()
	local have_num = ItemWGData.Instance:GetItemNumInBagById(rmb_cfg.cost_item_id or 0) or 0
	local buy_cfg_num = MultiFunctionWGData.Instance:GetDaoHangRmbBuyCfgNum() or 0
	local fumo_flag = self.fumo_max - self.touch_num
	if have_num >= (rmb_cfg.cost_item_num or 0) and buy_cfg_num ~= self.rmb_buy_level and self.rmb_buy_level > 0 then
		return true
	end

	if fumo_flag ~= 0 and self.rmb_buy_level > 0 then
		return true
	end

	return false
end

function MultiFunctionWGData:SetPiXiuLv(value)
	self.rmb_buy_level = value
end

function MultiFunctionWGData:GetPiXiuLv()
	return self.rmb_buy_level
end

function MultiFunctionWGData:GetDaoHangPrivilege()
	return self.daohang_privilege_cfg
end

function MultiFunctionWGData:GetDaoHangPrivilegeCfg()
	return self.daohang_zhigou_cfg or {}
end

function MultiFunctionWGData:GetNextDaoHangCurRmbBuyCfg()
	local index = 1
	return self.daohang_rmb_buy_cfg[self.rmb_buy_level + index]
end

function MultiFunctionWGData:GetDaoHangDaTiCfg(index)
	return self.daohang_dati_cfg[index]
end

function MultiFunctionWGData:GetDaoHangDaTiNum()
	return #self.daohang_dati_cfg
end

function MultiFunctionWGData:SetDaoHangDaTiInfo(protocol)
	self.dati_info = protocol
end

function MultiFunctionWGData:GetDaoHangDaTiInfo()
	return self.dati_info or {}
end

function MultiFunctionWGData:GetDaoHangRmbBuyFlag()
	return self.rmb_buy_flag
end

function MultiFunctionWGData:GetDaoHangRmbBuyCfgNum()
	return #self.daohang_rmb_buy_cfg
end

function MultiFunctionWGData:SetQuestionId(value)
	self.m_qu_id = value
end

function MultiFunctionWGData:GetQuestionId()
	return self.m_qu_id
end

function MultiFunctionWGData:SetFuMoTime(value)
	self.touch_rmb_time = value
end

function MultiFunctionWGData:GetFuMoTime()
	return self.touch_rmb_time or 0
end

function MultiFunctionWGData:IsDaoHangHolySealItem(item_id)
	local cfg_id = self:GetDaoHangCurRmbBuyCfg().cost_item_id
	return item_id == cfg_id
end

function MultiFunctionWGData:GetTouchNum()
	return self.touch_num
end

function MultiFunctionWGData:SetFuMoNum(num)
	self.touch_num = num
end

function MultiFunctionWGData:ClearFuMoNum()
	self.touch_num = 0
end

function MultiFunctionWGData:GetPrivilegeNum()
	local buy_flag = self:GetDaoHangRmbBuyFlag()
	local privilege_cfg = self:GetDaoHangPrivilege()
	local pri1 = privilege_cfg[1].privilege1
	local pri2 = privilege_cfg[1].privilege2
	local pri3 = privilege_cfg[1].privilege3
	for i = 2, 4 do
		if buy_flag[i - 2] == 1 then
			pri1 = pri1 + privilege_cfg[i].privilege1
			pri2 = pri2 + privilege_cfg[i].privilege2
			pri3 = pri3 + privilege_cfg[i].privilege3
		end
	end
	self.fumo_max = pri1
	local pri = { pri1, pri2, pri3 }
	return pri
end
