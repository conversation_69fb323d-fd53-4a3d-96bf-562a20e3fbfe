CrossConsumeRankView = CrossConsumeRankView or BaseClass(SafeBaseView)

function CrossConsumeRankView:__init()
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop

    --代替一下
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -20), sizeDelta = Vector2(970, 614)})
    self:AddViewResource(0, "uis/view/cross_consume_ui_prefab", "cross_consume_rank_view")
end

function CrossConsumeRankView:OpenCallBack()
    --请求信息的协议
    CrossConsumeRankWGCtrl.Instance:SendCrossConsumeReq(CROSS_CONSUME_RANK_OPERATE_TYPE.RANK_INFO)
end

function CrossConsumeRankView:LoadCallBack()
  self.node_list["title_view_name"].text.text = Language.CorssConsume.ConsumeRankTitle
    
  self.consume_rank_list = AsyncListView.New(ConsumeRankItemRender, self.node_list["ph_item_list"])
end

function CrossConsumeRankView:ReleaseCallBack()
    if self.consume_rank_list then
        self.consume_rank_list:DeleteMe()
        self.consume_rank_list = nil
    end
end

function CrossConsumeRankView:OnFlush()
    self:FlushViewShow()
end

function CrossConsumeRankView:FlushViewShow()
    --获取排行榜信息
    local data_list = CrossConsumeRankWGData.Instance:GetRankInfo()
    if data_list then
        self.consume_rank_list:SetDataList(data_list)
    end

    --获取自己的上榜信息
    local my_rank_data = CrossConsumeRankWGData.Instance:GetMyRankData()
    --获取到自己的消费金额
    local my_rank_value = CrossConsumeRankWGData.Instance:GetSelfRankValue()
    --获取当前轮次
    local grade = CrossConsumeRankWGData.Instance:GetConsumeGrade()
    --根据轮次拿到对应配置数据
    local consume_rank_cfg = CrossConsumeRankWGData.Instance:GetConsumeRankCfg(grade)

    if IsEmptyTable(consume_rank_cfg) then
        return 
    end
    --拿到配置表信息的最低上榜条件的消费数
    local min_reach_value = consume_rank_cfg[#consume_rank_cfg].reach_value
    --最低上榜条件的显示
    self.node_list["my_rank"].text.text = string.format(Language.CorssConsume.ConsumeRankCondition, min_reach_value)

    --我的累计消费的显示
    self.node_list["total_value"].text.text = string.format(Language.CorssConsume.ConsumeRankMoney, my_rank_value)

    --判断自己是否上榜
    local consume_money = 0  --还需消费数
    local next_ranking = 0
    --判断自己是否上榜  小于等于0是未上榜
    if my_rank_data <= 0  then
        --拿到上榜最后一名的信息
        next_ranking = consume_rank_cfg[#consume_rank_cfg].max_rank
        local monery_num = CrossConsumeRankWGData.Instance:GetMoneyNum(next_ranking)
        if monery_num <= 0 then
            consume_money = consume_rank_cfg[#consume_rank_cfg].reach_value - my_rank_value
        else
            consume_money = monery_num - my_rank_value + 1
        end
    elseif my_rank_data == 1 then
        self.node_list["btn_prompt"]:CustomSetActive(not my_rank_data == 1)
        return
    else
        --上一名的信息
        next_ranking = my_rank_data - 1
        --获取到上一名在哪个区间
        local qujian_cfg
        if data_list then
             qujian_cfg = CrossConsumeRankWGData.Instance:GetRankItemData(data_list[next_ranking].rank_data) 
        end
        local monery_num = CrossConsumeRankWGData.Instance:GetMoneyNum(next_ranking)
        if monery_num <= 0 then
            if qujian_cfg then
                consume_money = qujian_cfg.reach_value - my_rank_value
            end
        else
            consume_money = monery_num - my_rank_value + 1
        end
    end

    --显示上榜需求信息
    self.node_list["ascension_txt"].text.text = string.format(Language.CorssConsume.ConsumeRankPromote, consume_money, next_ranking)

end







---------列表格子
ConsumeRankItemRender = ConsumeRankItemRender or BaseClass(BaseRender)
function ConsumeRankItemRender:OnFlush()
    if not self.data then
        return
    end

    local is_top_3 = self.data.index <= 3
    local player_rank = self.data.index
    self.node_list["need_cap_value"].text.text = Language.CorssConsume.BaoMi
    self.node_list["need_cap_value2"].text.text = Language.CorssConsume.BaoMi

     local user_name = self.data.rank_data.name
     local name_data = Split(self.data.rank_data.name, "_")
     if self.data.no_true_rank then
         user_name = Language.CorssConsume.XuWeiYiDai
     else
        user_name = "["..name_data[2].."]"..name_data[1]
     end

    self.node_list["player_name"].text.text = user_name
	self.node_list["player_name2"].text.text = user_name

	self.node_list["player_name"].text.enabled = not is_top_3
	self.node_list["player_name2"].text.enabled = is_top_3

    self.node_list["need_cap_value"].text.enabled = not is_top_3
	self.node_list["need_cap_value2"].text.enabled = is_top_3

    self.node_list["img_rank"].image.enabled = is_top_3

    if not is_top_3 then
        self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_ty_xxd_bt_2"))
        self.node_list.rank.text.text = player_rank
    else
        self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. player_rank))
        self.node_list.root_bg.image:LoadSprite(ResPath.GetCommonImages("a2_pm_di_" .. player_rank))
        self.node_list.rank.text.text = ""
    end

end