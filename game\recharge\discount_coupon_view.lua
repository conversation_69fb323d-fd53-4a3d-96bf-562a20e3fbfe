
DiscountCouponView = DiscountCouponView or BaseClass(SafeBaseView)
function DiscountCouponView:__init()
    self:SetMaskBg()
    self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/coupon_ui_prefab", "layout_coupon")
end

function DiscountCouponView:__delete()
end

function DiscountCouponView:SetDataAndOpen(data)
    self.data = data
    if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end

function DiscountCouponView:OpenCallBack()
end

function DiscountCouponView:CloseCallBack()
    self.cur_select_discount_item_id = nil
end

function DiscountCouponView:ReleaseCallBack()
    self.data = nil
    self.cur_discount_data = nil

    if self.coupon_list then
        self.coupon_list:DeleteMe()
        self.coupon_list = nil
    end
end

function DiscountCouponView:LoadCallBack()
    self.cur_discount_data = nil
    self.cur_select_discount_item_id = nil
    if not self.coupon_list then
        self.coupon_list = DiscountCouponGrid.New()
        self.coupon_list:SetStartZeroIndex(false)
        self.coupon_list:SetIsMultiSelect(false)
        self.coupon_list:CreateCells({
            col = 2,
            list_view = self.node_list["coupon_list"],
            itemRender = DiscountCouponItem,
            assetBundle = "uis/view/coupon_ui_prefab",
            assetName = "coupon_item",
            change_cells_num = 1,
        })

        self.coupon_list:IsCanDeselect(true)
        self.coupon_list:SetSelectCallBack(BindTool.Bind(self.OnSelectDiscountCB, self))
    end


    XUI.AddClickEventListener(self.node_list.cancel_btn, BindTool.Bind(self.OnClickCancel, self))
    XUI.AddClickEventListener(self.node_list.ok_btn, BindTool.Bind(self.OnClickOk, self))
end

function DiscountCouponView:OnSelectDiscountCB(cell, is_select)
    if not cell or not cell.data then
        return
    end

    local data = cell.data
    if is_select then
        self.cur_discount_data = data
        self.cur_select_discount_item_id = data.cfg.item_id
    else
        self.cur_discount_data = nil
        self.cur_select_discount_item_id = nil
    end

    self:FlushDiscountStr()
end

function DiscountCouponView:OnFlush(param_t, index)
    if not self.data then
        return
    end

    local discount_list = RechargeWGData.Instance:GetRmbDiscountList(self.data.money)
    self.coupon_list:SetDataList(discount_list)
    self.coupon_list:CancleAllSelectCell()
    self.cur_discount_data = nil

    if self.cur_select_discount_item_id then
        local cur_time = TimeWGCtrl.Instance:GetServerTime()
        local select_index = nil
        for k,v in ipairs(discount_list) do
            if v.cfg.item_id == self.cur_select_discount_item_id and not v.limit_use and (v.limit_cd - cur_time) <= 0 then
                select_index = k
                break
            end
        end

        if select_index then
            self.coupon_list:SetSelectCellIndex(select_index)
        end
    else
        self:FlushDiscountStr()
    end
end

function DiscountCouponView:FlushDiscountStr()
    local recharge_type = self.data.recharge_type
    local recharge_seq = self.data.gold_flag
    local old_price = self.data.money
    local old_price_str = RoleWGData.GetPayMoneyStr(old_price, recharge_type, recharge_seq)

    if self.cur_discount_data then
        local new_price = self.cur_discount_data.discount_num
        local new_price_str = RoleWGData.GetPayMoneyStr(new_price, recharge_type, recharge_seq)
        local sub_price_str = RoleWGData.GetPayMoneyStr(old_price - new_price, recharge_type, recharge_seq)

        self.node_list.use_item_des.text.text = string.format(Language.Recharge.DiscountUsedStr, new_price_str, old_price_str, sub_price_str,  self.data.desc)
    else

        local first_discount_data = self.data.discount_list[1]
        if not first_discount_data then
            return
        end

        local rest_time = first_discount_data.limit_cd - TimeWGCtrl.Instance:GetServerTime()
        if rest_time > 0 or first_discount_data.limit_use then
            self.node_list.use_item_des.text.text = string.format(Language.Recharge.DiscountNotUsedStr1, old_price_str, self.data.desc)
        else
            local sub_price_str = RoleWGData.GetPayMoneyStr(old_price - first_discount_data.discount_num, recharge_type, recharge_seq)
            self.node_list.use_item_des.text.text = string.format(Language.Recharge.DiscountNotUsedStr2, old_price_str, sub_price_str, self.data.desc)
        end
    end
end

function DiscountCouponView:OnClickOk()
    if not self.data then
        return
    end

    local money = 0
    local discount_item_id = 0
    if self.cur_discount_data then
        money = self.cur_discount_data.discount_num
        discount_item_id = self.cur_discount_data.cfg.item_id
    else
        money = self.data.money
    end

    RechargeWGCtrl.Instance:AddRmbDiscountUsedCD(discount_item_id)
    AgentAdapter.Instance:Pay(self.data.product_id, money, self.data.desc, self.data.recharge_type, self.data.gold_flag, discount_item_id)
    self:Close()
end

function DiscountCouponView:OnClickCancel()
    self:Close()
end







DiscountCouponGrid = DiscountCouponGrid or BaseClass(AsyncBaseGrid)
function DiscountCouponGrid:IsSelectMultiNumLimit(cell_index)
	local data = self.cell_data_list[cell_index]
    if not data then
        return true
    end

    local rest_time = data.limit_cd - TimeWGCtrl.Instance:GetServerTime()
    if rest_time > 0 or data.limit_use then
        return true
    end

    return false
end






DiscountCouponItem = DiscountCouponItem or BaseClass(BaseRender)
function DiscountCouponItem:__delete()
    self:CleanLimitCD()
end

function DiscountCouponItem:CleanLimitCD()
    if self.limit_cd_timer and CountDown.Instance:HasCountDown(self.limit_cd_timer) then
        CountDown.Instance:RemoveCountDown(self.limit_cd_timer)
        self.limit_cd_timer = nil
    end
end

function DiscountCouponItem:OnFlush()
    self:CleanLimitCD()
    local cfg = self.data.cfg

    local discount_type = cfg.discount_type
    self.node_list.sub_price_panel:SetActive(discount_type == 1)
    self.node_list.discount_panel:SetActive(discount_type == 2)
    if discount_type == 1 then
        self.node_list.sub_price.text.text = cfg.discount_value
        self.node_list.sub_price_param.text.text = string.format(Language.Recharge.DiscountNeedStr, cfg.discount_limit)
        self.node_list.bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a2_zgdkq_mjj" .. cfg.bg_res))
    elseif discount_type == 2 then
        self.node_list.discount_price.text.text = string.format(Language.Recharge.DiscountText, cfg.discount_value * 0.1)
        self.node_list.bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("a2_zgdkq_zkj" .. cfg.bg_res))
    end
    
    local rest_time = self.data.limit_cd - TimeWGCtrl.Instance:GetServerTime()
    XUI.SetGraphicGrey(self.node_list.content, self.data.limit_use or rest_time > 0)
    if rest_time > 0 then
        self.node_list.limit_cd_str:SetActive(true)
        self.node_list.limit_cd_str.text.text = string.format(Language.Recharge.DiscountLimitStr, math.ceil(rest_time))
        self.limit_cd_timer = CountDown.Instance:AddCountDown(rest_time, 0.5,
        function(elapse_time, total_time)
            local time = math.ceil(total_time - elapse_time)
            if self.node_list.limit_cd_str then
                if time > 0 then
                    self.node_list.limit_cd_str.text.text = string.format(Language.Recharge.DiscountLimitStr, time)
                else
                    self.node_list.limit_cd_str:SetActive(false)
                end
            end
        end,

        function()
            if self.node_list.limit_cd_str then
                self.node_list.limit_cd_str:SetActive(false)
            end
        end
    )
    else
        self.node_list.limit_cd_str:SetActive(false)
    end
end

function DiscountCouponItem:OnSelectChange(is_select)
    self.node_list.select_img:SetActive(is_select)
end