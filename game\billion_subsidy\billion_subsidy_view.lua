-----------------------------------
-- 百亿补贴
-----------------------------------
BillionSubsidyView = BillionSubsidyView or BaseClass(SafeBaseView)

local VER_TAB = {
	[TabIndex.billion_subsidy_bybt] = "billion_subsidy_bybt",
	[TabIndex.billion_subsidy_dezg] = "billion_subsidy_dezg",
	[TabIndex.billion_subsidy_vip] = "billion_subsidy_vip",
	--[TabIndex.billion_subsidy_drpt] = "billion_subsidy_drpt",
	[TabIndex.billion_subsidy_yiyuan] = "billion_subsidy_yiyuan",
	--[TabIndex.billion_subsidy_mrsy] = "billion_subsidy_mrsy",
}

local TAB_INDEX = {
	[TabIndex.billion_subsidy_bybt] = "billion_subsidy_bybt",
	[TabIndex.billion_subsidy_xdzk] = "billion_subsidy_xdzk",
	[TabIndex.billion_subsidy_fyms] = "billion_subsidy_fyms",
	[TabIndex.billion_subsidy_jkjzc] = "billion_subsidy_jkjzc",
	[TabIndex.billion_subsidy_lysd] = "billion_subsidy_lysd",
	[TabIndex.billion_subsidy_dezg] = "billion_subsidy_dezg",
	[TabIndex.billion_subsidy_vip] = "billion_subsidy_vip",
	[TabIndex.billion_subsidy_drpt] = "billion_subsidy_drpt",
	[TabIndex.billion_subsidy_mrsy] = "billion_subsidy_mrsy",
	[TabIndex.billion_subsidy_rapidly] = "billion_subsidy_rapidly",
	[TabIndex.billion_subsidy_dailygift] = "billion_subsidy_dailygift",
	[TabIndex.billion_subsidy_yiyuan] = "billion_subsidy_yiyuan",
}

function BillionSubsidyView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	self:SetMaskBg()
	self.default_index = TabIndex.billion_subsidy_vip

	self.hor_show_tab = {
		TabIndex.billion_subsidy_bybt,				--百亿补贴-百亿补贴.
		TabIndex.billion_subsidy_xdzk,				--百亿补贴-限定折扣.
		TabIndex.billion_subsidy_fyms,				--百亿补贴-付1买3.
		TabIndex.billion_subsidy_jkjzc,				--百亿补贴-九块九专场.
		TabIndex.billion_subsidy_lysd,				--百亿补贴-灵玉商店.
		TabIndex.billion_subsidy_rapidly,			--百亿补贴-秒杀.
		TabIndex.billion_subsidy_dailygift,			--百亿补贴-礼包.
	}
	local bundle = "uis/view/billion_subsidy_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.billion_subsidy_bybt, bundle, "layout_ten_billion_subsidy")
	self:AddViewResource(TabIndex.billion_subsidy_xdzk, bundle, "layout_limit_discount")
	self:AddViewResource(TabIndex.billion_subsidy_fyms, bundle, "layout_billion_subsidy_b1g3")
	self:AddViewResource(TabIndex.billion_subsidy_jkjzc, bundle, "layout_billion_subsidy_jkjzc")
	self:AddViewResource(TabIndex.billion_subsidy_lysd, bundle, "layout_lingyu_shop")
	self:AddViewResource(TabIndex.billion_subsidy_dezg, bundle, "layout_billion_subsidy_dezg_view")
	self:AddViewResource(TabIndex.billion_subsidy_vip, bundle, "layout_billion_subsidy_vip_view")
	self:AddViewResource(TabIndex.billion_subsidy_drpt, bundle, "layout_billion_subsidy_drpt_view")
	self:AddViewResource({TabIndex.billion_subsidy_bybt, TabIndex.billion_subsidy_xdzk, TabIndex.billion_subsidy_jkjzc, TabIndex.billion_subsidy_fyms, TabIndex.billion_subsidy_dezg},
		bundle, "layout_billion_subsidy_discount_coupon_view")
	self:AddViewResource({TabIndex.billion_subsidy_bybt, TabIndex.billion_subsidy_xdzk, TabIndex.billion_subsidy_fyms, TabIndex.billion_subsidy_jkjzc, TabIndex.billion_subsidy_lysd},
		bundle, "layout_billion_subsidy_shop_cart_entrance_view")
	self:AddViewResource(TabIndex.billion_subsidy_mrsy, bundle, "layout_everyday_try_ticket")
	self:AddViewResource(TabIndex.billion_subsidy_dailygift, bundle, "layout_billion_subsidy_libao_view")
	self:AddViewResource(TabIndex.billion_subsidy_rapidly, bundle, "layout_billion_subsidy_rapidly_view")
	self:AddViewResource(TabIndex.billion_subsidy_yiyuan, bundle, "layout_billion_subsidy_yiyuan")

	self.red_pocket_need_flush_tab = {
		--屏蔽红包
		-- [TabIndex.billion_subsidy_bybt] = true,
		-- [TabIndex.billion_subsidy_xdzk] = true,
		-- [TabIndex.billion_subsidy_fyms] = true,
		-- [TabIndex.billion_subsidy_jkjzc] = true,
		-- [TabIndex.billion_subsidy_dezg] = true,
	}

	self.vip_show_need_flush_tab = {
		[TabIndex.billion_subsidy_bybt] = true,
		[TabIndex.billion_subsidy_xdzk] = true,
		[TabIndex.billion_subsidy_fyms] = true,
		[TabIndex.billion_subsidy_jkjzc] = true,
		[TabIndex.billion_subsidy_lysd] = true,
	}

	self.open_source_view = "btn_billion_subsidy"
	self:AddViewResource(0, bundle, "layout_enough_amount_red_pocket")
	--self:AddViewResource(0, bundle, "layout_billion_subsidy_show_vip")
	self:AddViewResource(0, bundle, "HorizontalTabbar")
	self:AddViewResource(0, bundle, "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function BillionSubsidyView:__delete()

end

function BillionSubsidyView:LoadCallBack()
	self.is_first_open = false

	local bundle, asset = ResPath.GetRawImagesPNG("a3_bybt_bj")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
	self.node_list.title_view_name.text.text = Language.BillionSubsidy.ViewName

	self:InitTabbar()
	self:InitMoneyBar()

	XUI.AddClickEventListener(self.node_list.red_pocket, BindTool.Bind(self.OnClickRedPocket, self))
	--XUI.AddClickEventListener(self.node_list.btn_open_buy_vip_show, BindTool.Bind(self.OnClickOpenBuyShowVipBtn, self))

	--首次进入
	local is_first_open = BillionSubsidyWGData.Instance:GetIsFirstOpenView()
	if is_first_open == 0 then
		self.is_first_open = true
		local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
		BillionSubsidyWGCtrl.Instance:PlayMemberUplevelEffect(member_level)
		BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FIRSTOPENVIEW)
		BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.GuideTicket)
	end

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.BillionSubsidy, self.get_guide_ui_event)

	BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.PARTICIPATE_Info)
end

--打开会员购买界面
function BillionSubsidyView:OnClickOpenBuyShowVipBtn()
    BillionSubsidyWGCtrl.Instance:OpenActiceVipView()
end

function BillionSubsidyView:InitTabbar()
	if not self.tabbar then
		self.remind_tab = {
			{ RemindName.BillionSubsidyBYBT, RemindName.BillionSubsidyXDZK, RemindName.BillionSubsidyFYMS, RemindName.BillionSubsidyJKJZC },
			{ RemindName.BillionSubsidyDEZG },
			{ RemindName.BillionSubsidyVIP },
			{ RemindName.BillionSubsidyDRPT },
			{},
			{ RemindName.DailyRecharge_Libao, RemindName.DailyRecharge_Rapidly },
			{ RemindName.BillionSubsidyYYHD},
		}
		self.tab_sub = { Language.BillionSubsidy.TabSub, nil, nil, nil, nil, Language.BillionSubsidy.TabSub1 }
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar.IsVerLimitSelected = function(tabbar, index)
			if VER_TAB[index] then
				local tab_is_open = FunOpen.Instance:GetFunIsOpenedByTabName(VER_TAB[index])
				if tab_is_open then
					return false
				else
					TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.FunNoOpen)
					return true
				end
			else
				return false
			end
		end
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabState, self))
		self.tabbar:SetCreateHorCallBack(BindTool.Bind1(self.SetTabState, self))
		self.tabbar:SetVerTabbarNameImgRes(ResPath.GetBillionSubsidyImg(), "a3_bybt_dtb")
		local assetbundle = "uis/view/billion_subsidy_ui_prefab"
		self.tabbar:Init(Language.BillionSubsidy.TabGroup, self.tab_sub, assetbundle, assetbundle, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		--FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.BillionSubsidy, self.tabbar)
	end
end

-- function BillionSubsidyView:TabbarSelectCallback(index)
-- 	if VER_TAB[index] then
-- 		local tab_is_open = FunOpen.Instance:GetFunIsOpenedByTabName(VER_TAB[index])
-- 		if tab_is_open then
-- 			self:ChangeToIndex(index)
-- 		else
-- 			-- self:ChangeToIndex(11)
-- 			local toggle = self.tabbar:GetToggleByIndex(10)
-- 			toggle.isOn = false
-- 			toggle.isOn = true
-- 			TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.FunNoOpen)
-- 		end
-- 	else
-- 		self:ChangeToIndex(index)
-- 	end
-- end

function BillionSubsidyView:SetTabState()
	local str_list = {}
	for k, v in pairs(TAB_INDEX) do
		local tab_is_open = FunOpen.Instance:GetFunIsOpenedByTabName(v)
		if not VER_TAB[k] then
			self.tabbar:SetToggleVisible(k, tab_is_open)
		else
			if not tab_is_open then
				str_list[math.floor(k / 10)] = Language.BillionSubsidy.FunNoOpen
			end
		end
	end
	self.tabbar:SetVerTabbarStr(str_list)
end

function BillionSubsidyView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
			show_cash_point = true
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function BillionSubsidyView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if BillionSubsidyWGData.Instance then
		BillionSubsidyWGData.Instance:SetSelectDCData(nil)
	end

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.BillionSubsidy, self.get_guide_ui_event)
	self.get_guide_ui_event = nil

	self:BYBTReleaseCallBack()
	self:XDZKReleaseCallBack()
	self:DEZGReleaseCallBack()
	self:JKJZCReleaseCallBack()
	self:VIPReleaseCallBack()
	self:DCReleaseCallBack()
	self:B1G3ReleaseCallBack()
	self:DRPTReleaseCallBack()
	self:MRSYReleaseCallBack()
	self:LYSDReleaseCallBack()
	self:ReleaseRapidlyRechargeView()
	self:ReleseDailyGiftRechargeView()
	self:ReleaseYiYuanView()
	self:SCReleaseCallBack()
end

function BillionSubsidyView:CloseCallBack()
	self:DEZGCloseCallBack()
	self:JKJZCCloseCallBack()
	self:VIPCloseCallBack()
	self:DCCloseCallBack()
	self:B1G3CloseCallBack()
	self:DRPTCloseCallBack()
end

function BillionSubsidyView:LoadIndexCallBack(index)
	if index == TabIndex.billion_subsidy_bybt then
		self:BYBTLoadCallBack()
	elseif index == TabIndex.billion_subsidy_xdzk then
		self:XDZKLoadCallBack()
	elseif index == TabIndex.billion_subsidy_dezg then
		self:DEZGLoadIndexCallBack()
	elseif index == TabIndex.billion_subsidy_jkjzc then
		self:JKJZCLoadIndexCallBack()
	elseif index == TabIndex.billion_subsidy_fyms then
		self:B1G3LoadIndexCallBack()
	elseif index == TabIndex.billion_subsidy_vip then
		self:VIPLoadIndexCallBack()
	elseif index == TabIndex.billion_subsidy_drpt then
		self:DRPTLoadIndexCallBack()
	elseif index == TabIndex.billion_subsidy_mrsy then
		self:MRSYLoadCallBack()
	elseif index == TabIndex.billion_subsidy_lysd then
		self:LYSDLoadCallBack()
	elseif index == TabIndex.billion_subsidy_rapidly then
		self:InitRapidlyRechargeView()
	elseif index == TabIndex.billion_subsidy_dailygift then
		self:InitDailyGiftRechargeView()
	elseif index == TabIndex.billion_subsidy_yiyuan then
		self:LoadYiYuanView()
	end

	if index == TabIndex.billion_subsidy_bybt or index == TabIndex.billion_subsidy_xdzk
	or index == TabIndex.billion_subsidy_jkjzc or index == TabIndex.billion_subsidy_dezg then
		self:DCLoadIndexCallBack()
	end

	if index == TabIndex.billion_subsidy_bybt or index == TabIndex.billion_subsidy_xdzk or index == TabIndex.billion_subsidy_fyms
	or index == TabIndex.billion_subsidy_jkjzc or index == TabIndex.billion_subsidy_lysd then
		self:SCLoadIndexCallBack()
	end
end

function BillionSubsidyView:OpenIndexCallBack(index)
	if index == TabIndex.billion_subsidy_vip then
		self:VIPOpenIndexCallBack()
	end
end

function BillionSubsidyView:ShowIndexCallBack(index)
	if self.tabbar then
		self.tabbar.hor_list:SetActive(false)
		for i, v in ipairs(self.hor_show_tab) do
			if index == v then
				self.tabbar.hor_list:SetActive(true)
			end
		end
	end

	if index == TabIndex.billion_subsidy_bybt then
	elseif index == TabIndex.billion_subsidy_xdzk then
		self:XDZKShowIndexCallBack()
	elseif index == TabIndex.billion_subsidy_dezg then
		self:DEZGShowIndexCallBack()
	elseif index == TabIndex.billion_subsidy_jkjzc then
		self:JKJZCShowIndexCallBack()
	elseif index == TabIndex.billion_subsidy_fyms then
		self:B1G3ShowIndexCallBack()
	elseif index == TabIndex.billion_subsidy_vip then
		self:VIPShowIndexCallBack()
	elseif index == TabIndex.billion_subsidy_drpt then
		self:DRPTShowIndexCallBack()
	elseif index == TabIndex.billion_subsidy_dailygift then
		self:ShowIndexDailyGiftView()
	elseif index == TabIndex.billion_subsidy_yiyuan then
		self:ShowIndexYiYuanView()
	end

	if index == TabIndex.billion_subsidy_bybt or index == TabIndex.billion_subsidy_xdzk
		or index == TabIndex.billion_subsidy_jkjzc or index == TabIndex.billion_subsidy_dezg then
		self:DCShowIndexCallBack()
	end
end

function BillionSubsidyView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			if index == TabIndex.billion_subsidy_bybt then
				self:BYBTOnFlush()
			elseif index == TabIndex.billion_subsidy_xdzk then
				self:XDZKOnFlush()
			elseif index == TabIndex.billion_subsidy_dezg then
				self:DEZGOnFlush(param_t)
			elseif index == TabIndex.billion_subsidy_jkjzc then
				self:JKJZCOnFlush(param_t)
			elseif index == TabIndex.billion_subsidy_fyms then
				self:B1G3OnFlush(param_t)
			elseif index == TabIndex.billion_subsidy_vip then
				self:VIPOnFlush(param_t)
			elseif index == TabIndex.billion_subsidy_drpt then
				self:DRPTOnFlush(param_t)
			elseif index == TabIndex.billion_subsidy_mrsy then
				self:MRSYOnFlush()
			elseif index == TabIndex.billion_subsidy_lysd then
				self:LYSDYOnFlush()
			elseif index == TabIndex.billion_subsidy_rapidly then
				self:FlushRapidlyRechargeView()
			elseif index == TabIndex.billion_subsidy_dailygift then
				self:FlushDailyGiftRechargeView()
			elseif index == TabIndex.billion_subsidy_yiyuan then
				self:FlushYiYuanView()
			end

			if index == TabIndex.billion_subsidy_bybt or index == TabIndex.billion_subsidy_xdzk
				or index == TabIndex.billion_subsidy_jkjzc or index == TabIndex.billion_subsidy_dezg then
				self:DCOnFlush(param_t)
			end
		elseif "jump_tab" == k then
			if index == TabIndex.billion_subsidy_dezg then
				self:DEZGOnFlush(param_t)
			elseif index == TabIndex.billion_subsidy_drpt then
				self:DRPTOnFlush(param_t)
			end
		end
	end

	self.node_list.red_pocket:CustomSetActive(self.red_pocket_need_flush_tab[index])
	if self.red_pocket_need_flush_tab[index] then
		self:RedPocketFlush()
	end

	-- self.node_list.btn_open_buy_vip_show:CustomSetActive(self.vip_show_need_flush_tab[index])
	-- if self.vip_show_need_flush_tab[index] then
	-- 	self:VIPShowFlush()
	-- end
end






--vip等级展示和购买
function BillionSubsidyView:VIPShowFlush()
	local member_level = BillionSubsidyWGData.Instance:GetMemberLevel()
	self.node_list.show_vip_name.text.text = Language.BillionSubsidy.VipNameList[member_level]
    local bundle, asset = ResPath.GetBillionSubsidyImg("a3_bybt_hz" .. member_level)
    self.node_list.show_vip_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.show_vip_icon.image:SetNativeSize()
    end)
end


--------- 满额返利start
function BillionSubsidyView:RedPocketFlush()
	local is_show_redpoket = true
	local red_pocket_des
	local first_pay_reward_flag = BillionSubsidyWGData.Instance:GetFirstPayRewardFlag()
	local is_show_remind = first_pay_reward_flag == BillionSubsidyWGData.RedPocketState.Complete
	if first_pay_reward_flag ~= BillionSubsidyWGData.RedPocketState.Fetched then
		local other_cfg = BillionSubsidyWGData.Instance:GetOtherCfg()
		red_pocket_des = string.format(Language.BillionSubsidy.RedPocketDesList[1], other_cfg.first_pay_single_min_quota, other_cfg.return_reward_price)
	else
		local member_level_cfg = BillionSubsidyWGData.Instance:GetMemberLevelCfg()
		if not IsEmptyTable(member_level_cfg) and member_level_cfg.return_times > 0 then
			local quato, fetch_count = BillionSubsidyWGData.Instance:GetTodayReturnRewardInfo()
			if fetch_count < member_level_cfg.return_times then
				local can_fetch_count = math.floor(quato / member_level_cfg.return_need_quota)
				is_show_remind = can_fetch_count > fetch_count
			end

			if member_level_cfg.return_times == fetch_count then		-- 每日次数领完
				red_pocket_des = Language.BillionSubsidy.RedPocketDesList[3]
			else
				local show_quato = is_show_remind and member_level_cfg.return_need_quota or quato % member_level_cfg.return_need_quota
				local color = is_show_remind and COLOR3B.C8 or COLOR3B.C10
				red_pocket_des = string.format(Language.BillionSubsidy.RedPocketDesList[2], member_level_cfg.return_need_quota, member_level_cfg.return_reward_price, color, show_quato, member_level_cfg.return_need_quota)
			end
		else
			is_show_redpoket = false
		end
	end

	self.node_list.red_pocket:CustomSetActive(is_show_redpoket)
	if is_show_redpoket then
		self.node_list.des_txt.text.text = red_pocket_des or ""
		self.node_list.red_pocket_remind:CustomSetActive(is_show_remind)
	end
end

function BillionSubsidyView:OnClickRedPocket()
	local first_pay_reward_flag = BillionSubsidyWGData.Instance:GetFirstPayRewardFlag()
	if first_pay_reward_flag == BillionSubsidyWGData.RedPocketState.NotComplete then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.NotFeatchRedPocket)
		return
	elseif first_pay_reward_flag == BillionSubsidyWGData.RedPocketState.Complete then
        BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_FIRST_PAY_SINGLE_REWARD)
		return
	end

	local member_level_cfg = BillionSubsidyWGData.Instance:GetMemberLevelCfg()
	if not member_level_cfg then
		return
	end

	local quato, fetch_count = BillionSubsidyWGData.Instance:GetTodayReturnRewardInfo()
	if fetch_count >= member_level_cfg.return_times then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.FeatchAllRedPocket)
		return
	end

	local can_fetch_count = math.floor(quato / member_level_cfg.return_need_quota)
	if can_fetch_count < fetch_count then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.NotFeatchRedPocket)
		return
	end

	BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.FETCH_RETURN_REWARD)
end

--------- 满额返利end

function BillionSubsidyView:GetNodeInScreenPos(node)
    if nil == node then return nil end
    local main_view = ViewManager.Instance:GetView(GuideModuleName.BillionSubsidy)
	if nil == main_view or not main_view:IsOpen() then
		return
	end

    local parent_rect= main_view.root_node.transform:GetComponent(typeof(UnityEngine.RectTransform))
    local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, node.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    local screen_pos_tbl_target = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.node_list.btn_open_shop_cart.transform.position)
	local _, local_position_tbl_target = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos_tbl_target, UICamera, Vector2(0, 0))

    return Vector2(local_position_tbl.x, local_position_tbl.y), Vector2(local_position_tbl_target.x, local_position_tbl_target.y)
end

function BillionSubsidyView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.BillionSubsidyDCListFirst then
		if nil == self.discount_coupon_list then
			return
		end

		BillionSubsidyWGData.Instance:SetDiscountCouponGuideState(true)
		BillionSubsidyWGData.Instance:SetDiscountCouponAllDataList()
		local dc_all_data_list = BillionSubsidyWGData.Instance:GetDiscountCouponAllDataList()
		self.discount_coupon_list:SetDataList(dc_all_data_list)
		return
	elseif ui_name == GuideUIName.BillionSubsidyDCList then
		if nil == self.discount_coupon_list then
			return
		end

		local cell_index = ui_param
		if cell_index >= 0 then
			local item = self.discount_coupon_list:GetItemAt(cell_index)
			if item then
				return item:GetView()
			end
		end
	elseif ui_name == GuideUIName.BillionSubsidyTenList then
		if nil == self.bybt_shop_list then
			return
		end


		BillionSubsidyWGData.Instance:SetDiscountCouponGuideState(false)
		local cell_index = ui_param
		if cell_index >= 0 then
			local item = self.bybt_shop_list:GetCell(cell_index)
			if item then
				return item.node_list.btn_buy
			end
		end
	elseif ui_name == GuideUIName.BillionSubsidyVerTab then
		if not self.tabbar then
			return
		end

		local item = self.tabbar:GetVerCell(ui_param)
		if item then
			return item:GetView()
		end
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end