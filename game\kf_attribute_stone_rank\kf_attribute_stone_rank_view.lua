KFAttributeStoneRankView = KFAttributeStoneRankView or BaseClass(SafeBaseView)

local SHOW_TYPE = {
    Reward = 1,
    Rank = 2,
    Yesterday_Rank = 3,
}

function KFAttributeStoneRankView:__init()
    self.view_style = ViewStyle.Half
    self.is_safe_area_adapter = true

    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/kf_attribute_stone_rank_ui_prefab", "layout_kf_attribute_stone_rank")
end

function KFAttributeStoneRankView:OpenCallBack()
    KFAttributeStoneRankWGCtrl.Instance:SendKFRankReq(KF_ATTR_STONE_RANK.KF_ATTR_STONE_RANK_INFO)
    KFAttributeStoneRankWGCtrl.Instance:SendKFRankReq(KF_ATTR_STONE_RANK.KF_ATTR_STONE_RANK_PERSON_SCORE_REWARD_FLAG)
    KFAttributeStoneRankWGCtrl.Instance:SendKFRankReq(KF_ATTR_STONE_RANK.LAST_INFO)
end

function KFAttributeStoneRankView:LoadCallBack()
    self.cur_select_type = 0
    if not self.rank_reward_list then
        self.rank_reward_list = AsyncListView.New(KFAttriStoneRankRewardItem, self.node_list.rank_reward_list)
    end

    if not self.rank_list then
        self.rank_list = AsyncListView.New(KFAttriStoneRankRender, self.node_list.rank_list)
    end

    if not self.last_rank_list then
        self.last_rank_list = AsyncListView.New(KFAttriStoneRankRender, self.node_list.last_rank_list)
    end

    if not self.score_item_list then
        self.score_item_list = AsyncListView.New(KFAttriStoneRankScoreItem, self.node_list.score_item_list)
    end

    if not self.show_model then
        self.show_model = OperationActRender.New(self.node_list.model_root)
        self.show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    for i = 1, 3 do
        XUI.AddClickEventListener(self.node_list["openpanel_btn" .. i], BindTool.Bind(self.OnClickGotoActivityBtn, self, i))
    end

    self:LoginTimeCountDown()

	self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
	RemindManager.Instance:Bind(self.remind_callback, RemindName.KFAttributeStoneRank)

    XUI.AddClickEventListener(self.node_list.get_score_btn, BindTool.Bind(self.OnClickGetScoreBtn, self))
    XUI.AddClickEventListener(self.node_list.score_reward_btn, BindTool.Bind(self.OnClickOpenScoreRewardBtn, self))
    XUI.AddClickEventListener(self.node_list.rank_reward_tog, BindTool.Bind(self.SwitchShowPanel, self, 1))
    XUI.AddClickEventListener(self.node_list.rank_list_tog, BindTool.Bind(self.SwitchShowPanel, self, 2))
    XUI.AddClickEventListener(self.node_list.yes_rank_tog, BindTool.Bind(self.SwitchShowPanel, self, 3))
    XUI.AddClickEventListener(self.node_list.rule_btn, BindTool.Bind(self.OnClickRuleBtn, self))
end

function KFAttributeStoneRankView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("kf_attribute_stone_rank") then
		CountDownManager.Instance:RemoveCountDown("kf_attribute_stone_rank")
	end

    if self.rank_reward_list then
        self.rank_reward_list:DeleteMe()
        self.rank_reward_list = nil
    end

    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end

    if self.last_rank_list then
        self.last_rank_list:DeleteMe()
        self.last_rank_list = nil
    end

    if self.score_item_list then
        self.score_item_list:DeleteMe()
        self.score_item_list = nil
    end

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

	RemindManager.Instance:UnBind(self.remind_callback)
end

function KFAttributeStoneRankView:OnFlush(parma_t)
    for k, v in pairs(parma_t) do
        if k == "all" then
            self:FlushModel()
            self:FlushGotoBtnInfo()
            self:FlushScoreItemList()
            if self.cur_select_type == 0 then
                self:SwitchShowPanel(SHOW_TYPE.Reward)
            end
        elseif k == "rank_update" then
            self:FlushRankPanelInfo()
        elseif k == "score_reward" then
            self:FlushSelfRankInfo()
        elseif k == "last_rank_update" then
            self:FlushYesterdayRankInfo()
        end
    end
end

function KFAttributeStoneRankView:FlushModel()
    local model_data = KFAttributeStoneRankWGData.Instance:GetModelData()
    if IsEmptyTable(model_data) then
        return
    end

    local display_data = model_data.display_data
    if display_data.render_type == -1 then
        return
    end

    self.node_list.title_txt.text.text = model_data.model_name
    self.show_model:SetData(display_data)

    local transform_info = model_data.transform_info
    RectTransform.SetAnchoredPositionXY(self.node_list.model_root.rect, transform_info.pos_x, transform_info.pos_y)
    self.node_list.model_root.rect.rotation = Quaternion.Euler(transform_info.rot_x, transform_info.rot_y, transform_info.rot_z)
    Transform.SetLocalScaleXYZ(self.node_list.model_root.transform, transform_info.scale, transform_info.scale, transform_info.scale)
end

function KFAttributeStoneRankView:FlushGotoBtnInfo()
    local score_cfg = KFAttributeStoneRankWGData.Instance:GetScoreCfg()
    if IsEmptyTable(score_cfg) then
        return
    end

    for i = 1, 3 do
        if score_cfg["act_type" .. i] ~= "" and score_cfg["act_type" .. i] ~= 0 then
            self.node_list["openpanel_btn" .. i]:SetActive(true)
            self.node_list["openpanel_btn_des" .. i].text.text = score_cfg["button_name" .. i]
        else
            self.node_list["openpanel_btn" .. i]:SetActive(false)
        end
    end
end

function KFAttributeStoneRankView:FlushSelfRankInfo()
    local self_rank = KFAttributeStoneRankWGData.Instance:GetSelfRank()
    local self_score = KFAttributeStoneRankWGData.Instance:GetSelfScore()
    self.node_list.my_rank.text.text = self_rank > 0 and self_rank or Language.KFAttributeStoneRank.NoRank
    self.node_list.my_cap.text.text = self_score
end

function KFAttributeStoneRankView:FlushRankRewardPanelInfo()
    local reward_data_list = KFAttributeStoneRankWGData.Instance:GetRewardCfg()
    if IsEmptyTable(reward_data_list) then
        return
    end

    self:FlushSelfRankInfo()
    self.rank_reward_list:SetDataList(reward_data_list)
end

function KFAttributeStoneRankView:FlushRankPanelInfo()
    self:FlushSelfRankInfo()
    local rank_data_list = KFAttributeStoneRankWGData.Instance:GetRankList()
    if IsEmptyTable(rank_data_list) then
        return
    end

    self.rank_list:SetDataList(rank_data_list)
end

function KFAttributeStoneRankView:FlushYesterdayRankInfo()
    local last_rank_data_list = KFAttributeStoneRankWGData.Instance:GetLastRankList()
    if IsEmptyTable(last_rank_data_list) then
        return
    end

    self.last_rank_list:SetDataList(last_rank_data_list)
end

function KFAttributeStoneRankView:FlushScoreItemList()
    local item_list = KFAttributeStoneRankWGData.Instance:GetShowScoreItemList()
    if IsEmptyTable(item_list) then
        return
    end

    self.score_item_list:SetDataList(item_list)
end

function KFAttributeStoneRankView:LoginTimeCountDown()
    if CountDownManager.Instance:HasCountDown("kf_attribute_stone_rank") then
        return
	end

    local server_tiem = TimeWGCtrl.Instance:GetServerTime()
    local now_day_end_time = TimeWGCtrl.Instance:NowDayTimeEnd(server_tiem)
    self.node_list["act_time"].text.text = TimeUtil.FormatSecondDHM6(now_day_end_time - server_tiem)
    CountDownManager.Instance:AddCountDown("kf_attribute_stone_rank", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), now_day_end_time, nil, 1)
end

function KFAttributeStoneRankView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 and self.node_list and self.node_list.act_time then
		self.node_list.act_time.text.text = TimeUtil.FormatSecondDHM6(valid_time)
	end
end

function KFAttributeStoneRankView:OnComplete()
    if self.node_list and self.node_list.act_time then
        self.node_list.act_time.text.text = ""
    end

	self:Close()
end

function KFAttributeStoneRankView:OnClickGotoActivityBtn(index)
    local score_cfg = KFAttributeStoneRankWGData.Instance:GetScoreCfg()
    if IsEmptyTable(score_cfg) then
        return
    end

    local act_type = score_cfg["act_type" .. index]
    local open_panel = score_cfg["open_panel" .. index]
    if act_type == "" or act_type == 0 or open_panel == "" then
        return
    end

    local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(act_type)
    if not act_is_open then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOpenAct)
        return
    end

    ViewManager.Instance:Open(open_panel)
end

function KFAttributeStoneRankView:OnClickGetScoreBtn()
    local score_cfg = KFAttributeStoneRankWGData.Instance:GetScoreCfg()
    if IsEmptyTable(score_cfg) then
        return
    end


    if score_cfg.jump_parma ~= "" then
        FunOpen.Instance:OpenViewNameByCfg(score_cfg.jump_parma)
    end
end

function KFAttributeStoneRankView:OnClickOpenScoreRewardBtn()
    KFAttributeStoneRankWGCtrl.Instance:OpenScoreRewardView()
end

function KFAttributeStoneRankView:SwitchShowPanel(show_type)
    if show_type == self.cur_select_type then
        return
    end

    self.cur_select_type = show_type
    if SHOW_TYPE.Reward == show_type then
        self:FlushRankRewardPanelInfo()
    elseif SHOW_TYPE.Rank == show_type then
        self:FlushRankPanelInfo()
    elseif SHOW_TYPE.Yesterday_Rank == show_type then
        self:FlushYesterdayRankInfo()
    end
end

function KFAttributeStoneRankView:OnClickRuleBtn()
    RuleTip.Instance:SetContent(Language.KFAttributeStoneRank.RuleContent, Language.KFAttributeStoneRank.RuleTitle)
end

function KFAttributeStoneRankView:OnRemindChange(remind_name, num)
    self.node_list.score_reward_remind:SetActive(num > 0)
end


-----------------KFAttriStoneRankScoreItem积分道具item-----------------
KFAttriStoneRankScoreItem = KFAttriStoneRankScoreItem or BaseClass(BaseRender)
function KFAttriStoneRankScoreItem:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.cell_pos)
    end
end

function KFAttriStoneRankScoreItem:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function KFAttriStoneRankScoreItem:OnFlush()
    if not self.data then
        return
    end

    self.item_cell:SetData({item_id = self.data.item_id})
    self.node_list.score.text.text = self.data.score
end

-----------------KFAttriStoneRankRewardItem排名奖励item-----------------
KFAttriStoneRankRewardItem = KFAttriStoneRankRewardItem or BaseClass(BaseRender)
function KFAttriStoneRankRewardItem:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.rank_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function KFAttriStoneRankRewardItem:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function KFAttriStoneRankRewardItem:OnFlush()
    if not self.data then
        return
    end

    local min_rank = self.data.min_rank
    local max_rank = self.data.max_rank
    local rank_str
    if min_rank == max_rank then
        rank_str = min_rank
    else
        rank_str = string.format(Language.KFAttributeStoneRank.RankRewardDes2, min_rank, max_rank)
    end

    self.node_list.rank_desc.text.text = string.format(Language.KFAttributeStoneRank.RankRewardDes, rank_str, self.data.reach_value)
    self.reward_list:SetDataList(self.data.reward_item)
end

-----------------KFAttriStoneRankRender排名render-----------------
KFAttriStoneRankRender = KFAttriStoneRankRender or BaseClass(BaseRender)

function KFAttriStoneRankRender:OnFlush()
    if not self.data then
        return
    end

    local is_top_3
    local player_rank = self.data.index
    if self.data.no_true_rank then  --未上榜
        self.node_list.cap.text.text = Language.KFImproveCapRank.BaoMi
        is_top_3 = self.data.index <= 3
	else
		self.node_list.cap.text.text = self.data.rank_data.rank_value
        is_top_3 = self.data.index <= 3
	end

    local user_name = self.data.rank_data.name
	if self.data.no_true_rank then
		user_name = Language.KFImproveCapRank.XuWeiYiDai
	end

    self.node_list.name.text.text = user_name
    self.node_list.rank_icon:CustomSetActive(is_top_3)

    if not is_top_3 then
		self.node_list.rank_desc.text.text = player_rank
	else
		self.node_list.rank_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. player_rank))
		self.node_list.rank_desc.text.text = ""
	end

    local is_top_one = self.data.index == 1
    self.node_list.bg:CustomSetActive(is_top_one)

    if not is_top_one and is_top_3 then
        local bundle, asset = ResPath.GetCommonImages("a2_xt_pm" .. self.data.index)
        self.node_list.bg_oth.image:LoadSprite(bundle, asset)
    end

    self.node_list.bg_oth:CustomSetActive(not is_top_one and is_top_3)
    self.node_list.bg_zi:CustomSetActive(not is_top_3 and self.data.index % 2 ~= 0)
end