﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_QualityControlActiveWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.QualityControlActive), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("SetOverrideLevel", SetOverrideLevel);
		<PERSON><PERSON>RegFunction("ResetOverrideLevel", ResetOverrideLevel);
		<PERSON><PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("isBestFlagByArt", get_isBestFlagByArt, set_isBestFlagByArt);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetOverrideLevel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.QualityControlActive obj = (Nirvana.QualityControlActive)ToLua.CheckObject(L, 1, typeof(Nirvana.QualityControlActive));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetOverrideLevel(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetOverrideLevel(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.QualityControlActive obj = (Nirvana.QualityControlActive)ToLua.CheckObject(L, 1, typeof(Nirvana.QualityControlActive));
			obj.ResetOverrideLevel();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isBestFlagByArt(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.QualityControlActive obj = (Nirvana.QualityControlActive)o;
			bool ret = obj.isBestFlagByArt;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isBestFlagByArt on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isBestFlagByArt(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.QualityControlActive obj = (Nirvana.QualityControlActive)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isBestFlagByArt = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isBestFlagByArt on a nil value");
		}
	}
}

