﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FancyScrollView_FancyScrollRectCell_FancyScrollView_ContextWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(FancyScrollView.FancyScrollRectCell<FancyScrollView.Context>), typeof(FancyScrollView.FancyCell<FancyScrollView.Context>), "FancyScrollRectCell_FancyScrollView_Context");
		<PERSON><PERSON>RegFunction("UpdatePosition", UpdatePosition);
		<PERSON>.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdatePosition(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.FancyScrollRectCell<FancyScrollView.Context> obj = (FancyScrollView.FancyScrollRectCell<FancyScrollView.Context>)ToLua.CheckObject<FancyScrollView.FancyScrollRectCell<FancyScrollView.Context>>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.UpdatePosition(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

