﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Events_UnityEvent_UnityEngine_Vector2Wrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Events.UnityEvent<UnityEngine.Vector2>), typeof(UnityEngine.Events.UnityEventBase), "UnityEvent_UnityEngine_Vector2");
		<PERSON><PERSON>ction("AddListener", AddListener);
		<PERSON><PERSON>unction("RemoveListener", RemoveListener);
		L.RegFunction("Invoke", Invoke);
		L.RegFunction("New", _CreateUnityEngine_Events_UnityEvent_UnityEngine_Vector2);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Events_UnityEvent_UnityEngine_Vector2(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Events.UnityEvent<UnityEngine.Vector2> obj = new UnityEngine.Events.UnityEvent<UnityEngine.Vector2>();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Events.UnityEvent<UnityEngine.Vector2>.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Events.UnityEvent<UnityEngine.Vector2> obj = (UnityEngine.Events.UnityEvent<UnityEngine.Vector2>)ToLua.CheckObject<UnityEngine.Events.UnityEvent<UnityEngine.Vector2>>(L, 1);
			UnityEngine.Events.UnityAction<UnityEngine.Vector2> arg0 = (UnityEngine.Events.UnityAction<UnityEngine.Vector2>)ToLua.CheckDelegate<UnityEngine.Events.UnityAction<UnityEngine.Vector2>>(L, 2);
			obj.AddListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveListener(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Events.UnityEvent<UnityEngine.Vector2> obj = (UnityEngine.Events.UnityEvent<UnityEngine.Vector2>)ToLua.CheckObject<UnityEngine.Events.UnityEvent<UnityEngine.Vector2>>(L, 1);
			UnityEngine.Events.UnityAction<UnityEngine.Vector2> arg0 = (UnityEngine.Events.UnityAction<UnityEngine.Vector2>)ToLua.CheckDelegate<UnityEngine.Events.UnityAction<UnityEngine.Vector2>>(L, 2);
			obj.RemoveListener(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Invoke(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Events.UnityEvent<UnityEngine.Vector2> obj = (UnityEngine.Events.UnityEvent<UnityEngine.Vector2>)ToLua.CheckObject<UnityEngine.Events.UnityEvent<UnityEngine.Vector2>>(L, 1);
			UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 2);
			obj.Invoke(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

