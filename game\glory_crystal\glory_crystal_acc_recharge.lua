-- 天裳豪礼-累计充值
function GloryCrystalHaoLiView:ReleaseAccRecharge()
    self:RemoveAccRechargeCountDown()

    if self.acc_recharge_list_view then
        self.acc_recharge_list_view:DeleteMe()
        self.acc_recharge_list_view = nil
    end

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end
    
    self.cur_show_model_seq = nil
end

function GloryCrystalHaoLiView:LoadAccRechargeCallBack()
    if not self.acc_recharge_list_view then
        self.acc_recharge_list_view = AsyncListView.New(GloryCrystalAccRechargeCell, self.node_list.acc_recharge_list_view)
    end

	if not self.show_model then
        self.show_model = OperationActRender.New(self.node_list.acc_recharge_model_node)
        self.show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end
    
    self.cur_show_model_seq = nil

    self:SetAccRechargeCountDown()
end

function GloryCrystalHaoLiView:FlushAccRecharge()
    local list = GloryCrystalWGData.Instance:GetAccRecharge()
    -- 排序
    local show_list = {}
    local cur_value = GloryCrystalWGData.Instance:GetCurAccRecharge()
    for _, value in pairs(list) do
        local data = {}
        data.cfg = value
        local is_got = GloryCrystalWGData.Instance:GetAccRechargeFlag(value.seq) == 1
        local is_enough = cur_value >= value.real_recharge_num
        data.seq = value.seq
        data.sort = 1
        if is_got then
            data.sort = 2
        elseif is_enough then
            data.sort = 0
        end
        table.insert(show_list, data)
    end
    table.sort(show_list, SortTools.KeyLowerSorters("sort", "seq"))
    self.acc_recharge_list_view:SetDataList(show_list)

    self:FlushShowModel()
end

function GloryCrystalHaoLiView:FlushAccRechargeTime(left_time)
	if self.node_list and self.node_list.acc_recharge_time_text then
		local time = TimeUtil.FormatSecondDHM9(left_time)
		self.node_list.acc_recharge_time_text.text.text = string.format(Language.GloryCrystal.HaoLiTime, time)
	end
end

function GloryCrystalHaoLiView:RemoveAccRechargeCountDown()
	if self.acc_recharge_count_down then
		CountDown.Instance:RemoveCountDown(self.acc_recharge_count_down)
		self.acc_recharge_count_down = nil
	end
end

function GloryCrystalHaoLiView:SetAccRechargeCountDown()
    self:RemoveAccRechargeCountDown()
    
    -- 活动结束时间
	local all_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GLORY_CRYSTAL)
	local timer_func = function(elapse_time, total_time)
		self:FlushAccRechargeTime(total_time - elapse_time)
	end

	timer_func(0, all_time)
	self.acc_recharge_count_down = CountDown.Instance:AddCountDown(all_time, 1, timer_func, end_call_back)
end

function GloryCrystalHaoLiView:FlushShowModel()
    local cur_show_model_cfg = GloryCrystalWGData.Instance:GetCurShowModelCfg()
    if not cur_show_model_cfg or (cur_show_model_cfg and self.cur_show_model_seq == cur_show_model_cfg.seq)  then 
        return 
    end

    self.cur_show_model_seq = cur_show_model_cfg.seq

    self:FlushShowModelPower(cur_show_model_cfg)

    local display_data = {}
	display_data.should_ani = true
	if cur_show_model_cfg.model_show_itemid ~= 0 and cur_show_model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(cur_show_model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = cur_show_model_cfg.model_show_itemid
        end
        
        display_data.model_click_func = function ()
            TipWGCtrl.Instance:OpenItem({item_id = cur_show_model_cfg["model_show_itemid"]})
        end
	end
	
	display_data.bundle_name = cur_show_model_cfg["model_bundle_name"]
    display_data.asset_name = cur_show_model_cfg["model_asset_name"]
    local model_show_type = tonumber(cur_show_model_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

	self.show_model:SetData(display_data)
	local scale = 1
	if cur_show_model_cfg.display_scale and cur_show_model_cfg.display_scale ~= "" then
		scale = cur_show_model_cfg["display_scale"]
	end
	Transform.SetLocalScaleXYZ(self.node_list.acc_recharge_model_node.transform, scale, scale, scale)
    local pos_x, pos_y = 0, 0
	if cur_show_model_cfg.display_pos and cur_show_model_cfg.display_pos ~= "" then
		local pos_list = string.split(cur_show_model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end
	RectTransform.SetAnchoredPositionXY(self.node_list.acc_recharge_model_node.rect, pos_x, pos_y)

	if cur_show_model_cfg.display_rotation and cur_show_model_cfg.display_rotation ~= "" then
		local display_rotation = string.split(cur_show_model_cfg.display_rotation,"|")
		self.node_list.acc_recharge_model_node.transform.rotation = Quaternion.Euler(display_rotation[1], display_rotation[2], display_rotation[3])
    end
end

function GloryCrystalHaoLiView:FlushShowModelPower(cur_show_model_cfg)
    if cur_show_model_cfg.model_show_itemid then
		local cap = ItemShowWGData.Instance:OnlyGetCapability(cur_show_model_cfg.model_show_itemid)
		self.node_list.common_capability:CustomSetActive(cap ~= nil and cap > 0)
		self.node_list.cap_value.text.text = cap
	end
end

--------------------------------------- GloryCrystalAccRechargeCell ------------------------------------
GloryCrystalAccRechargeCell = GloryCrystalAccRechargeCell or BaseClass(BaseRender)
function GloryCrystalAccRechargeCell:ReleaseCallBack()
	if self.award_item_list then
		self.award_item_list:DeleteMe()
		self.award_item_list = nil
	end
end

function GloryCrystalAccRechargeCell:LoadCallBack()
	if not self.award_item_list then
		self.award_item_list = AsyncListView.New(ItemCell, self.node_list.award_list)
        self.award_item_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_goto, BindTool.Bind(self.GoTo, self))
    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.GetAward, self))
end

function GloryCrystalAccRechargeCell:OnFlush()
    local data = self:GetData()
    data = data and data.cfg
    if IsEmptyTable(data) then
        return
    end

    self.award_item_list:SetDataList(data.reward_item)
    
    local cur_value = GloryCrystalWGData.Instance:GetCurAccRecharge()
    cur_value = math.min(cur_value, data.real_recharge_num)
    local is_enough = cur_value >= data.real_recharge_num
    local desc = is_enough and Language.GloryCrystal.AccRecharge1 or Language.GloryCrystal.AccRecharge2
    self.node_list.progress.text.text = string.format(desc, cur_value, data.real_recharge_num)

    local is_got = GloryCrystalWGData.Instance:GetAccRechargeFlag(data.seq) == 1
    self.node_list.btn_goto:CustomSetActive(not is_enough)
    self.node_list.btn_get:CustomSetActive(is_enough and not is_got)
    self.node_list.sold_out:CustomSetActive(is_got)
end

function GloryCrystalAccRechargeCell:GoTo()
    local data = self:GetData()
    data = data and data.cfg
	if data.open_panel then
		ViewManager.Instance:OpenByCfg(data.open_panel)
	end
end

function GloryCrystalAccRechargeCell:GetAward()
    GloryCrystalWGCtrl.Instance:ReqGloryCrystalInfo(OA_GLORY_CRYSTAL_OPERATE_TYPE.TOTAL_RECHARGE_REWARD, self.data.seq)
end