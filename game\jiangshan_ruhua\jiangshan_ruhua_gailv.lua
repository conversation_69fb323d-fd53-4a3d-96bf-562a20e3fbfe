--灵核概率展示面板
JiangShanRuHuaProbabilityView = JiangShanRuHuaProbabilityView or BaseClass(SafeBaseView)

function JiangShanRuHuaProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(706, 486)})
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_treasure_probability") --复用寻宝的
    self:SetMaskBg(true, true)
end

function JiangShanRuHuaProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function JiangShanRuHuaProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TianShenLingHe.ProbabilityTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(JiangShanRuHuaProItemRender, self.node_list.ph_pro_list) 
    end
end

function JiangShanRuHuaProbabilityView:OnFlush()
    local info = JiangShanRuHuaWGData.Instance:GetProbabilityInfo()
    self.probability_list:SetDataList(info)
end

----------------------------------------------------------------------------------
JiangShanRuHuaProItemRender = JiangShanRuHuaProItemRender or BaseClass(BaseRender)
function JiangShanRuHuaProItemRender:__delete()
    
end

function JiangShanRuHuaProItemRender:LoadCallBack()
    
end

function JiangShanRuHuaProItemRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
