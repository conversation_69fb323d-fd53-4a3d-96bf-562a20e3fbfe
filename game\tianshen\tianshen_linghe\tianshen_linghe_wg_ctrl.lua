require("game/tianshen/tianshen_linghe/tianshen_linghe_wg_data")
require("game/tianshen/tianshen_linghe/tianshen_linghe_draw_wg_data")
require("game/tianshen/tianshen_linghe/base_linghe_cell")
--require("game/tianshen/tianshen_linghe/tianshen_linghe_view")
require("game/tianshen/tianshen_linghe/tianshen_linghe_uplevel")
--require("game/tianshen/tianshen_linghe/tianshen_linghe_resolve")
--require("game/tianshen/tianshen_linghe/tianshen_linghe_compose")
require("game/tianshen/tianshen_linghe/tianshen_linghe_bag_view")
require("game/tianshen/tianshen_linghe/tianshen_linghe_draw_view")
require("game/tianshen/tianshen_linghe/tianshen_linghe_draw_reward")
require("game/tianshen/tianshen_linghe/linghe_record_view")
require("game/tianshen/tianshen_linghe/linghe_hunt_gailv")
require("game/tianshen/tianshen_linghe/linghe_library_view")
require("game/tianshen/tianshen_linghe/tianshen_shenshi_resove_view") -- 神饰分解
-- require("game/tianshen/tianshen_linghe/tianshen_shenshi_compose_view") -- 神饰融合

TianShenLingHeWGCtrl = TianShenLingHeWGCtrl or BaseClass(BaseWGCtrl)
function TianShenLingHeWGCtrl:__init()
	if TianShenLingHeWGCtrl.Instance then
		error("[TianShenLingHeWGCtrl]:Attempt to create singleton twice!")
	end

	TianShenLingHeWGCtrl.Instance = self

    self.data = TianShenLingHeWGData.New()
    --self.view = TianShenLingHeView.New(GuideModuleName.TianShenLingHeView)
    self.bag_view = TianShenLingHeBagView.New()
    self.draw_view = TianShenLingHeDrawView.New(GuideModuleName.TianShenLingHeDrawView)
    self.draw_reward_view = TianShenLingHeDrawRewardView.New()
    self.linghe_record_view = LingHeRecordView.New()
    self.linghe_hunt_gailv_view = LingHeHuntProbabilityView.New()
    self.linghe_library_view = LingHeLibraryView.New()

    self.shenshi_resove_view = TianShenShenShiResoveView.New() -- 神饰分解
    -- self.shenshi_compose_view = TianShenShenShiComposeView.New() -- 神饰融合

    self:RegisterAllProtocols()
end

function TianShenLingHeWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	-- self.view:DeleteMe()
	-- self.view = nil

    self.bag_view:DeleteMe()
    self.bag_view = nil

    self.draw_view:DeleteMe()
    self.draw_view = nil

    self.draw_reward_view:DeleteMe()
    self.draw_reward_view = nil

    self.linghe_record_view:DeleteMe()
    self.linghe_record_view = nil

    self.linghe_hunt_gailv_view:DeleteMe()
    self.linghe_hunt_gailv_view = nil

    self.linghe_library_view:DeleteMe()
    self.linghe_library_view = nil

    if self.shenshi_resove_view then
		self.shenshi_resove_view:DeleteMe()
		self.shenshi_resove_view = nil
	end

	-- if self.shenshi_compose_view then
	-- 	self.shenshi_compose_view:DeleteMe()
	-- 	self.shenshi_compose_view = nil
	-- end

    TianShenLingHeWGCtrl.Instance = nil
end

function TianShenLingHeWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSTianShenLingHeOpera)
    self:RegisterProtocol(CSTianShenLingHeResloveOpera)
    self:RegisterProtocol(CSTianShenLingHeDraw)
    self:RegisterProtocol(CSTianShenLingHeQuickUse)
	self:RegisterProtocol(SCTianShenLingHeAllBagInfo, "OnSCTianShenLingHeAllBagInfo")
    self:RegisterProtocol(SCTianShenLingHeAllSlotInfo, "OnSCTianShenLingHeAllSlotInfo")
    self:RegisterProtocol(SCTianShenLingHeSingleItemInfo, "OnSCTianShenLingHeSingleItemInfo")
    self:RegisterProtocol(SCTianShenLingHeSingleSlotInfo, "OnSCTianShenLingHeSingleSlotInfo")
    self:RegisterProtocol(SCTianShenLingHeDrawShopInfo, "OnSCTianShenLingHeDrawShopInfo")
    self:RegisterProtocol(SCTianShenLingHeDrawRecordInfo, "OnSCTianShenLingHeDrawRecordInfo")
    self:RegisterProtocol(SCTianShenLingHeDrawShopOpenTime, "OnSCTianShenLingHeDrawShopOpenTime")
end

-- function TianShenLingHeWGCtrl:GetView()
--     return self.view
-- end

function TianShenLingHeWGCtrl:FlushView(index, key, param_t)
	-- if self.view:IsOpen() then
	-- 	self.view:Flush(index, key, param_t)
	-- end
    ViewManager.Instance:FlushView(GuideModuleName.TianShenView, index, key, param_t)
end

-- 操作请求
function TianShenLingHeWGCtrl:SendOperateReq(operate_type, param_1, param_2, param_3)
    -- print_error("----操作请求----", operate_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenLingHeOpera)
	protocol.operate_type = operate_type or 0
	protocol.param_1 = param_1 or -1
	protocol.param_2 = param_2 or -1
    protocol.param_3 = param_3 or -1
	protocol:EncodeAndSend()
end

--一键穿戴请求
function TianShenLingHeWGCtrl:SendLingHeOneClick(tianshen_index, best_bag_list)
    local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenLingHeQuickUse)
    protocol.tianshen_index = tianshen_index or -1
    protocol.best_bag_list = best_bag_list or {}
    protocol:EncodeAndSend()
end

-- 分解请求
function TianShenLingHeWGCtrl:SendResloveReq(reslove_list)
    -- print_error("----分解请求----", reslove_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenLingHeResloveOpera)
	protocol.reslove_list = reslove_list or {}
	protocol:EncodeAndSend()
end

-- 全部背包信息
function TianShenLingHeWGCtrl:OnSCTianShenLingHeAllBagInfo(protocol)
    -- print_error("----全部背包信息----", protocol.change_reason)
    self.data:SetAllBagInfo(protocol)

    if protocol.change_reason == TianShenLingHeWGData.BAG_CHANGE_TYPE.ALL_INFO then
        RemindManager.Instance:Fire(RemindName.TianShenLingHeUpLevel)
        -- RemindManager.Instance:Fire(RemindName.TianShenLingHeReslove)
        -- RemindManager.Instance:Fire(RemindName.TianShenLingHeCompose)
    end
end

-- 全部槽位信息
function TianShenLingHeWGCtrl:OnSCTianShenLingHeAllSlotInfo(protocol)
    -- print_error("----全部槽位信息----")

    self.data:SetAllSlotInfo(protocol)
    self:FlushView(TabIndex.tianshen_linghe_uplevel)
    RemindManager.Instance:Fire(RemindName.TianShenLingHeUpLevel)
end

-- 单个物品信息
function TianShenLingHeWGCtrl:OnSCTianShenLingHeSingleItemInfo(protocol)
    -- print_error("----单个物品信息----")
    self.data:SetSingleItemInfo(protocol)

    -- self:FlushView(TabIndex.tianshen_linghe_compose, "protocol_change")
    -- if self.shenshi_compose_view:IsOpen() then
    --     self.shenshi_compose_view:Flush()
    -- end
    --RemindManager.Instance:Fire(RemindName.TianShenLingHeCompose)
    RemindManager.Instance:Fire(RemindName.TianShenLingHeUpLevel)
end

-- 单个槽位信息
function TianShenLingHeWGCtrl:OnSCTianShenLingHeSingleSlotInfo(protocol)
    -- print_error("----单个槽位信息----")
    self.data:SetSingleSlotInfo(protocol)
    self:FlushView(TabIndex.tianshen_linghe_uplevel)
    RemindManager.Instance:Fire(RemindName.TianShenLingHeUpLevel)
end

-- 物品变化
function TianShenLingHeWGCtrl:OnLingHeItemDataChange(change_item_id, change_item_index, change_reason, old_num, new_num)
    -- 物品数量增加
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
        local item_cfg = ItemWGData.Instance:GetItemConfig(change_item_id)
        if item_cfg then
            local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, new_num - old_num))
        end

        self:FlushView(TabIndex.tianshen_linghe_uplevel)
        --self:FlushView(TabIndex.tianshen_linghe_reslove)
        if self.shenshi_resove_view:IsOpen() then
            self.shenshi_resove_view:Flush()
        end
        -- RemindManager.Instance:Fire(RemindName.TianShenLingHeReslove)
        RemindManager.Instance:Fire(RemindName.TianShenLingHeUpLevel)
	end
end

function TianShenLingHeWGCtrl:OpenLingHeBagView(bag_list, ts_index, slot_index)
	self.bag_view:SetDataAndOpen(bag_list, ts_index, slot_index)
end

-- 有新天神激活
function TianShenLingHeWGCtrl:NewTianShenActDo()
    self.data:ChangeUpLevelShowList()
    self:FlushView(TabIndex.tianshen_linghe_uplevel)
    RemindManager.Instance:Fire(RemindName.TianShenLingHeUpLevel)
end

--灵核抽奖使用道具并弹窗
function TianShenLingHeWGCtrl:ClickUseLingHeDrawItem(index, func)
   local cfg = TianShenLingHeWGData.Instance:GetDrawConsumeCfg()
   local cur_cfg = cfg[index]
   if cur_cfg == nil then
       return
   end

   local cost = (cur_cfg.cost_gold / cur_cfg.stuff_num)

   local tips_data = {}
   tips_data.item_id = cur_cfg.stuff_id
   tips_data.price = cost
   tips_data.draw_count = cur_cfg.stuff_num 
   tips_data.has_checkbox = true
   tips_data.checkbox_str = string.format("tianshen_linghe_draw%d", index)
   TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, func, nil)
end

--灵核抽奖操作请求
function TianShenLingHeWGCtrl:SendLingHeDrawReq(operate_type, param_1, param_2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenLingHeDraw)
    protocol.operate_type = operate_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
    protocol:EncodeAndSend()
end

-- 灵核抽奖商品购买信息
function TianShenLingHeWGCtrl:OnSCTianShenLingHeDrawShopInfo(protocol)
    self.data:SetLingHeDrawShopInfo(protocol)
    if self.draw_view:IsOpen() then
       self.draw_view:Flush()
    end
    --RemindManager.Instance:Fire(RemindName.TianShenLingHeDraw)
end

--灵核抽奖结果信息
function TianShenLingHeWGCtrl:OnSCTianShenLingHeDrawRecordInfo(protocol)
    self.data:SetLingHeDrawResultInfo(protocol)
    if not self.draw_reward_view:IsOpen() then
        self.draw_reward_view:Open()
    else
        self.draw_reward_view:Flush()
    end
    RemindManager.Instance:Fire(RemindName.TianShenLingHeDraw)
end

--灵核抽奖商店开启时间
function TianShenLingHeWGCtrl:OnSCTianShenLingHeDrawShopOpenTime(protocol)
    self.data:SetLingHeShopOpenTime(protocol)
    if self.draw_view:IsOpen() then
        self.draw_view:Flush()
    end
    MainuiWGCtrl.Instance:FlushView(0, "LingHeDrawMainBtnFlush")
end

function TianShenLingHeWGCtrl:OpenLingHeRecordView()
    self.linghe_record_view:Open()
end

function TianShenLingHeWGCtrl:OpenLingHeGailvView()
    self.linghe_hunt_gailv_view:Open()
end

function TianShenLingHeWGCtrl:OpenLingHeLibrayView()
    self.linghe_library_view:Open()
end

-- 打开文物分解
function TianShenLingHeWGCtrl:OpenShenShiResoveView()
    self.shenshi_resove_view:Open()
end

-- 设置要合成的目标
-- function TianShenLingHeWGCtrl:SetShenShiComposeData(item_index)
--     self.shenshi_compose_view:SetComposeItem(item_index)
-- end

-- 打开神饰融合
-- function TianShenLingHeWGCtrl:OpenShenShiComposeView()
--     self.shenshi_compose_view:Open()
-- end