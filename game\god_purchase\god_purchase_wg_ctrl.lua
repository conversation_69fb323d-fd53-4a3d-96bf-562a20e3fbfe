require("game/god_purchase/god_purchase_view")
require("game/god_purchase/god_purchase_wg_data")
require("game/god_purchase/god_purchase_gift_show_view")
GodPurchaseWGCtrl = GodPurchaseWGCtrl or BaseClass(BaseWGCtrl)

function GodPurchaseWGCtrl:__init()
	if GodPurchaseWGCtrl.Instance then
		ErrorLog("[GodPurchaseWGCtrl] attempt to create singleton twice!")
		return
	end

	GodPurchaseWGCtrl.Instance = self
	self.data = GodPurchaseWGData.New()
    self.view = GodPurchaseView.New(GuideModuleName.GodPchaseView)
    self.gift_view = GodPurchaseGiftShowView.New()

    self:RegisterAllProtocols()

	self.enter_open_flag = false
    self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
	self.open_view_fun = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.CheckViewOpen, self))
end

function GodPurchaseWGCtrl:__delete()
	GodPurchaseWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil

	self.gift_view:DeleteMe()
	self.gift_view = nil

	ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
	self.activity_change_callback = nil

	-- 注销角色属性改变监听
    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	if self.open_view_fun then
		GlobalEventSystem:UnBind(self.open_view_fun)
		self.open_view_fun = nil
	end
end

function GodPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOAGodRmbBuyInfo,"OnSCOAGodRmbBuyInfo")
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
	self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

-- 活动信息改变
function GodPurchaseWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY then
		local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
		if activity_info == nil then
			return
		end

		self:ReqGodPurchaseInfo(OA_GOD_RMB_BUY_OPERATE_TYPE.INFO)
		MainuiWGCtrl.Instance:FlushView(0, "hm_act_notice")
	end
end

function GodPurchaseWGCtrl:ReqGodPurchaseInfo(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function GodPurchaseWGCtrl:OnSCOAGodRmbBuyInfo(protocol)
	--print_error("========深藏直购========",protocol)
	self.data:SetAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.GodPchaseView)
	MainuiWGCtrl.Instance:FlushView(0, "flush_god_purchase_tip")
	MainuiWGCtrl.Instance:FlushView(0, "hm_act_notice")
end

--跨天，请求一下信息
function GodPurchaseWGCtrl:OnPassDay()
	GlobalTimerQuest:AddDelayTimer(function()
		self:ReqGodPurchaseInfo(OA_GOD_RMB_BUY_OPERATE_TYPE.INFO)
	end, 1)
	
	self.data:SetIsShowTip(true)
	MainuiWGCtrl.Instance:FlushView(0, "flush_god_purchase_tip")
end

-- 角色属性改变
function GodPurchaseWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
		local other_cfg = self.data:GetOtherCfg()
     	local role_level = GameVoManager.Instance:GetMainRoleVo().level
     	if role_level >= other_cfg.open_level then
     		MainuiWGCtrl.Instance:FlushView(0, "flush_god_purchase_tip")
     	end
	end
end

function GodPurchaseWGCtrl:ShowGiftView(data_list)
	self.gift_view:Flush(0, "gift_info", {data_list = data_list})
	self.gift_view:Open()
end

function GodPurchaseWGCtrl:CheckViewOpen()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local is_not_can_open = PlayerPrefsUtil.GetInt("GodPurchaseWGCtrl" .. main_role_id) == open_day

	if self.enter_open_flag or is_not_can_open then return end

	self.enter_open_flag = true

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local activity_cfg = ActivityWGData.Instance:GetDailyActivityDailyCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_GOD_RMB_BUY)
	local role_level = GameVoManager.Instance:GetMainRoleVo().level

	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN and role_level >= tonumber(activity_cfg.level) then
		local shop_info = GodPurchaseWGData.Instance:GetCurShopCfg()
		local info_1 = shop_info and shop_info[1]
		local bug_flag1 = false
		if info_1 then
			bug_flag1 = GodPurchaseWGData.Instance:GetShopIsBuyFlag(info_1.seq)
		end

		if self.view and not bug_flag1 then
			self.view:Open()
			PlayerPrefsUtil.SetInt("GodPurchaseWGCtrl" .. main_role_id, open_day)
		end
	end

	if self.open_view_fun then
		GlobalEventSystem:UnBind(self.open_view_fun)
		self.open_view_fun = nil
	end
end