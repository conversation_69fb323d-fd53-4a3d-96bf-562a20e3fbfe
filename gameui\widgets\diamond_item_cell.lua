---------------------------------------
-- 2024.1.15 物品菱形格子， 按需加载子节点 --
---------------------------------------
DiamondItemCell = DiamondItemCell or BaseClass(BaseRender)

DiamondItemCell.CountDownIndex = 0

local CELL_SIZE = Vector2(COMMON_CONSTS.ItemCellSize, COMMON_CONSTS.ItemCellSize)
local ITEM_ICON_SIZE = Vector2(COMMON_CONSTS.ItemCellSize, COMMON_CONSTS.ItemCellSize)
local ITEM_ICON_SCALE = Vector3(1, 1, 1)
local nodes_prefab_dic = nil
local TypeNodePrefabList = typeof(NodePrefabList)
local TypeUINameTable = typeof(UINameTable)
local TypeRect = typeof(UnityEngine.RectTransform)
local TypeText = typeof(TMPro.TextMeshProUGUI)

function DiamondItemCell:__init(instance)
	-- self.is_use_objpool = true
	if nil == self.root_node then
		local bundle, asset = ResPath.GetWidgets("DiamondItemCell")
        local u3dobj = U3DObject(ResPoolMgr:TryGetGameObject(bundle, asset))
        if self.instance_parent then
            u3dobj.transform:SetParent(self.instance_parent)
        end

		self:SetInstance(u3dobj)
		self.is_use_objpool = true
	end

	if instance then
		self:SetInstanceParent(instance)
	end

	self.data = nil
	self.is_value = false
	self.need_default_eff = true
	self.show_quality = true  --显示品质
	self.use_new_select_effect = false
	self.show_can_trade = false
	self.need_item_get_way = false	--显示获取方式
	--获取UI
	self.root_node = self.node_list["DiamondItemCell"]

	self.use_button = nil
	self.flush_callback = nil
	self.item_effect = {}
	self.tip_callback = nil
	self.is_use_round_quality_bg = false
	self.item_icon_custom_scale = nil
	self.is_show_special_title_ui = false
	if self.is_use_objpool then
		self:Reset()
		self:ListenClick()
	end

	self.button = self:Nodes("button")
end

function DiamondItemCell:__delete()
	if self.__gameobj_loaders then
		ReleaseGameobjLoaders(self)
	end

	if self.__res_loaders then
		ReleaseResLoaders(self)
	end
	self:Reset(true)
	self.root_node = nil
	self.button = nil
	self.flush_callback = nil
	self.is_value = false
	self.need_default_eff = nil
	self.is_show_coin_num = nil --是否显示元宝数量("左上角")
	self.need_item_get_way = false
	self.tip_callback = nil
	self.is_show_special_title_ui = false
	self.item_icon_scale = nil

	if self.item_desc and self.item_desc.gameObject then
		ResMgr:Destroy(self.item_desc.gameObject)
		self.item_desc = nil
	end

	if self.is_use_objpool and not self:IsNil() then
		ResPoolMgr:Release(self.view.gameObject, 99999)
	end

	self.nodes_gameobject = nil
	self.nodes = {}
	self.loaded_nodes = {}
end

local color_white = Color.New(1,1,1,1)
function DiamondItemCell:Reset(is_delete)
	self.hide_numtxt_less_num = 1 					-- 低于X数量时不显示数字
	self.hide_right_down_cell_num = 1    			-- 低于X数量时不显示右下角图片
	self.data_from_index = -1						-- 数据来自于哪个格子
	self.is_lock = false							-- 是否锁住，锁住后不可变更
	self.is_showtip = true							-- 是否显示物品信息提示
	self.is_select_effect = false 					-- 是否显示选择特效
	self.handler = nil
	self.item_tip_from = nil
	self.special_text = ""							-- 右下角字体（不受原逻辑影响）
	self.special_color = nil
	self.flush_callback = nil
	self.use_button = nil
	self.need_default_eff = true
	self.is_show_coin_num = false 					--是否显示元宝数量("左上角")
	self.use_new_select_effect = false
	self.show_quality = true
	self.show_can_trade = false                     -- 是否显示左下角的秤砣
	self.need_item_get_way = false
	self.is_use_round_quality_bg = false
	self.is_value = false
	self.is_show_special_title_ui = false
	self.item_icon_scale = nil

	if not is_delete then
		self:InitResetNodes()
		self.root_node.rect.anchorMin = u3dpool.vec2(0.5,0.5)
		self.root_node.rect.anchorMax = u3dpool.vec2(0.5,0.5)
		self.root_node.localScale = u3dpool.vec3(1,1,1)
		self.root_node.image.color = color_white
		self.root_node.rect.sizeDelta = CELL_SIZE
		self.root_node.image.enabled = true
		self:ClearAllParts()
		local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
		self:SetCellBg(bundle, asset, true)
		self:SetSelectEffectImage(true)
		self:ResetSelectEffect()
	else
		self:SetGraphicGreyCualityBg(false)
		if self.node_list["DiamondItemCell"] then
			XUI.SetGraphicGrey(self.node_list["DiamondItemCell"], false)
			self.node_list["DiamondItemCell"].image.color = color_white
		end
	end

	self.item_effect = {}

	if self.item_count_down then
		if CountDownManager.Instance:HasCountDown(self.item_count_down) then
			CountDownManager.Instance:RemoveCountDown(self.item_count_down)
		end
		self.item_count_down = nil
	end

	self.tip_callback = nil
end

---------------------------------------------------- 子节点管理Begin ----------------------------------------------------
-- 初始化并重置子节点列表
function DiamondItemCell:InitResetNodes()
	self.item_cell_node_list = self.root_node:GetComponent(TypeNodePrefabList).nodeList
	self:InitNodesPrefabDic()

	self.nodes_gameobject = self.root_node.transform:Find("nodes")
	self.nodes = {} 												-- 用于储存已加载的子节点
	self.loaded_nodes = {} 											-- 用于记录已加载的子节点下标

	if self.nodes_gameobject then
		if self.nodes_gameobject.transform.childCount > 0 then
			-- 查找所有已加载的子节点，进行记录并重置节点
			for i = 0, self.nodes_gameobject.transform.childCount - 1 do
				local go_transform = self.nodes_gameobject.transform:GetChild(i)
				local go = go_transform.gameObject
				local u3dobj = U3DObject(go, go_transform, self)
				self.nodes[go.name] = {}
		        self.nodes[go.name].obj = u3dobj
				local name_table = u3dobj:GetComponent(TypeUINameTable)
		        if name_table then
					self.nodes[go.name].node_list = U3DNodeList(name_table, self)
				end

				self.loaded_nodes[nodes_prefab_dic[go.name].index] = true

				-- 重置节点
				self:ResetNode(go, nodes_prefab_dic[go.name].prefab)
			end
		end
	else
		self.nodes_gameobject = GameObject.New("nodes", TypeRect)
		self.nodes_gameobject.transform:SetParent(self.root_node.transform, false)
	end
	self.nodes_gameobject.transform.localPosition = Vector3.zero
	self.nodes_gameobject.transform.sizeDelta = CELL_SIZE
end

local function LoadSpriteCallBack(cbdata)
	local obj = cbdata[1]
	DiamondItemCell.ReleaseCBData(cbdata)

	if not IsNil(obj.gameObject) then
		obj:SetActive(true)
		obj.image:SetNativeSize()
	end
end

local function LoadSpriteCallBackSize(cbdata)
	local obj = cbdata[1]
	local self = cbdata[2]
	DiamondItemCell.ReleaseCBData(cbdata)

	if not IsNil(obj.gameObject) then
		-- obj:SetActive(true)
		if self ~= nil and nil ~= self.item_icon_custom_scale then
			obj.rect.sizeDelta = self.item_icon_custom_scale
			self.item_icon_custom_scale = nil
		else
			obj.image:SetNativeSize()
			--RectTransform.SetSizeDeltaXY(obj.rect, ITEM_ICON_SIZE.x, ITEM_ICON_SIZE.y)
		end
	end
end

-- 根据预制体重置节点状态（配合对象池）
local reset_fun = function(gameObject, prefab)
	if IsNil(gameObject) then
		return
	end

	-- 重置显示状态
	gameObject:SetActive(prefab.activeSelf)

	-- 重置RectTransform
	local go_rect = gameObject:GetComponent(TypeRect)
	local prefab_rect = prefab:GetComponent(TypeRect)
	go_rect.anchoredPosition = prefab_rect.anchoredPosition
	go_rect.localScale = prefab_rect.localScale
	-- go_rect.rotation = prefab_rect.rotation
	go_rect.localRotation = prefab_rect.localRotation

	-- 重置Text组件
	local text_com = gameObject:GetComponent(TypeText)
	local prefab_text_com = prefab:GetComponent(TypeText)
	if text_com and prefab_text_com then
		text_com.text = prefab_text_com.text
		text_com.fontSize = prefab_text_com.fontSize
	end
end

-- 根据预制体重置节点状态（配合对象池）
function DiamondItemCell:ResetNode(gameObject, prefab)
	reset_fun(gameObject, prefab)

	local transforms = gameObject:GetComponentsInChildren(TypeRect, true)
	local prefab_transforms = prefab:GetComponentsInChildren(TypeRect, true)
	for i = 0, transforms.Length - 1 do
		if i < prefab_transforms.Length then -- 某些节点下的子节点是动态加载的，所以这里要判断预制体子节点数目
			local go = transforms[i].gameObject
			local prefab = prefab_transforms[i].gameObject
			reset_fun(go, prefab)
		else
			break
		end
	end
end

-- 初始化子节点字典（key为子节点名称）
function DiamondItemCell:InitNodesPrefabDic()
	if not nodes_prefab_dic then
		nodes_prefab_dic = {}
		for i = 0, self.item_cell_node_list.Count - 1 do
			local node_data = self.item_cell_node_list[i]
			if not IsNil(node_data) then
				nodes_prefab_dic[node_data.prefab.name] = {}
				nodes_prefab_dic[node_data.prefab.name].prefab = node_data.prefab
				nodes_prefab_dic[node_data.prefab.name].index = i
			end
		end
	end
end

-- 获取子节点（node_name：子节点名称，sub_node_name：子节点NameTable里的key值）
function DiamondItemCell:Nodes(node_name, sub_node_name)
	if not self.nodes[node_name] then
		local index = nodes_prefab_dic[node_name].index
		local prefab = nodes_prefab_dic[node_name].prefab
		local go = ResMgr:Instantiate(prefab)
		go.name = prefab.name
		local u3dobj = U3DObject(go, go.transform, self)
		u3dobj.transform:SetParent(self.nodes_gameobject.transform, false)
		u3dobj.transform:SetSiblingIndex(self:CalSiblingIndex(index))

		self.nodes[node_name] = {}
        self.nodes[node_name].obj = u3dobj
		local name_table = u3dobj:GetComponent(typeof(UINameTable))
        if name_table then
			self.nodes[node_name].node_list = U3DNodeList(name_table, self)
		end

        self.loaded_nodes[index] = true
	end

	return self:TryGetNodes(node_name, sub_node_name)
end

-- 获取已加载的子节点
function DiamondItemCell:TryGetNodes(node_name, sub_node_name)
	if self.nodes[node_name] then
		if not sub_node_name then
			local obj = self.nodes[node_name].obj
			if not IsNil(obj.gameObject) then 		-- 某些面板ReleaseCallBack的时候会由于其他报错，导致itemcell没有Delete，但是gameObject被销毁掉了，这里容错一下
				return obj
			end
		else
			if self.nodes[node_name].node_list then
				local obj = self.nodes[node_name].node_list[sub_node_name]
				if not IsNil(obj.gameObject) then
					return obj
				end
			end
		end
	end
	return nil
end

function DiamondItemCell:TrySetActive(node_name, sub_node_name, active)
	if active then
		self:Nodes(node_name, sub_node_name):SetActive(true)
	else
		local node = self:TryGetNodes(node_name, sub_node_name)
		if node then
			node:SetActive(false)
		end
	end
end

function DiamondItemCell:CalSiblingIndex(index)
	local loaded_sum = 0
	for i = index, 0, -1 do
		if self.loaded_nodes[i] then
			loaded_sum = loaded_sum + 1
		end
	end
	return loaded_sum
end
---------------------------------------------------- 子节点管理End ----------------------------------------------------

function DiamondItemCell:SetIndex(index)
	self.index = index
end

function DiamondItemCell:GetIndex()
	return self.index
end

function DiamondItemCell:SetData(data, is_from_bag)
	if data and data.item_id and data.is_bind == nil then  -- 不传默认非绑
	data.is_bind = 0
	end
	if data and data.tips_from_view then
		self:SetItemTipFrom(data.tips_from_view)
	end
	BaseRender.SetData(self, data)
end

function DiamondItemCell:SetFlushCallBack(callback)
	self.flush_callback = callback
end

function DiamondItemCell:SetItemCellBg(bunble, asset)
	if self.item_cell_bg then
		self.item_cell_bg:SetAsset(bunble, asset)
	end
end

function DiamondItemCell:SetRedPoint(value)

end

-- 设置可以显示秤砣
function DiamondItemCell:SetCanTrade(flag)
	self.show_can_trade = flag
end

function DiamondItemCell:OnFlush()
	self:ClearAllParts()
	self:SetItemIconValue(self.is_value)
	self:SetSelectEffect(self.is_select_effect)
	self:SetNeedUselessModdal(true)
	self:SetDuoBeiShow(false)

	if nil == self.data then
		self.data_from_index = -1
		if self.flush_callback then
			self.flush_callback()
		end
		return
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	-- 部分道具(随机礼包里的) 根据限制改变显示数据
	local is_special_gift = ItemWGData.Instance:IsSpecialRandGift(self.data.item_id)
	if is_special_gift and self.item_tip_from ~= ItemTip.FROM_BAG then
		local old_itemid = self.data.item_id
		local change_data = ItemWGData.Instance:GetGiftItemConvertibleData(self.data)
		if change_data and old_itemid ~= change_data.item_id then
			self.data = change_data
			self:Flush()
			return
		end
	end

	self:SetLingQuVisible(self.data.is_ylq)
	if self.need_default_eff then
		self:SetDefaultEff(true)
	end

	self.item_big_type = big_type or 0
	if nil ~= item_cfg then
		self:SetButtonComp(true)
		if item_cfg.icon_id and item_cfg.icon_id > 0 then
			local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
			if item_cfg.is_man_or_woman == 1 and RoleWGData.Instance.role_vo.sex == 0 then
				bundle, asset = ResPath.GetItem(item_cfg.woman_icon)
			end

			if item_cfg.is_other_prof == RoleWGData.Instance:GetRoleProf() then--剑 琴职业区分
				bundle, asset = ResPath.GetItem(item_cfg.other_prof_icon)
			end

			if self.data.equip and item_cfg.sub_type == GameEnum.E_TYPE_SHENJI then
                local up_icon = HiddenWeaponWGData.Instance:GetEquipIcon(self.data.item_id, self.data.equip.base_color) or item_cfg.icon_id
                bundle, asset = ResPath.GetItem(up_icon)
            end

			self:SetItemIcon(bundle, asset)
			self:TrySetActive("ta_bg", nil, false)
		else
			self:TrySetActive("item_icon", nil, false)
		end

		self:SetChipIsShow(item_cfg)
		self:SetGiftSystemName(item_cfg)
		self:ShowEquipWearIcon()
		self:ShowEquipYuLingIcon(item_cfg)

		-- 数量
		if self:NeedShowNum() then
			self:SetRightBottomColorText(CommonDataManager.NotConverExtend(self.data.num))
			self:SetRightBottomTextVisible(true)
		elseif self.data.param and self.data.param.strengthen_level ~= nil then
			local strength_level = self.data.param.strengthen_level
			if EquipWGData.IsJLType(item_cfg.sub_type) and 0 == strength_level then
				strength_level = 1
			end

			self:SetRightBottomColorText("")
			self:SetRightBottomTextVisible(false)
		elseif self.data.stren_level and 0 < self.data.stren_level and self.item_tip_from ~= ItemTip.FROM_TIANSHEN_QIANGHUA then
			self:SetRightBottomColorText(self.data.stren_level)
			self:SetRightBottomTextVisible(true)
		else
			self:SetRightBottomColorText("")
			self:SetRightBottomTextVisible(false)
		end

		self:SetTopRightIcon(item_cfg.top_right_icon)
		self:ShowNewYinJi(item_cfg.sub_type)

		-- 绑定标记
		self:SetBindIconVisible(0 ~= self.data.is_bind)
		if big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT then
			-- 上升箭头刷新
			self:FlushUpFlagIcon(self.data)
		elseif big_type == GameEnum.ITEM_BIGTYPE_OTHER then
			local is_meet = self:CheckProfLimit(item_cfg)
			self:SetGradumalMaskEnable(not is_meet)
			self:SetRefiningRareFlag()

			if MultiFunctionWGData.Instance:IsDaoHangEquip(self.data.item_id) then
				self:ResetStar()
				local equip_cfg = MultiFunctionWGData.Instance:GetDaoHangEquipByItemId(self.data.item_id)
				local show_star = equip_cfg and equip_cfg.star_level or 0
				self:SetLeftTopImg(show_star)
			end
		end

		local bundle, asset = "", ""
		if item_cfg.color >= GameEnum.ITEM_COLOR_GREEN then
			local item_color = self.data.color and self.data.color or item_cfg.color
			if item_cfg.color >= GameEnum.ITEM_COLOR_GREEN then
				local item_bg_type = self.is_use_round_quality_bg and "a3_sc_btn_bg_%d" or "a3_ty_wpk_big_%d"
				if item_cfg.sub_type == GameEnum.E_TYPE_SHENJI then
					if self.data.equip then
						item_color = self.data.equip.base_color
					else
						local equip = self:ParseShenJiEquip()
						item_color = equip and equip.color or item_cfg.color
					end
				end
				bundle, asset = ResPath.GetCommonImages(string.format(item_bg_type, item_color))
			end
		end

		if self.show_quality and "" ~= bundle and "" ~= asset then
			self:SetQualityIcon(bundle, asset)
			self:Nodes("quality_icon"):SetActive(true)
		else
			self:TrySetActive("quality_icon", nil, false)
		end

		-- 左上角文字
		if big_type == GameEnum.ITEM_BIGTYPE_EXPENSE then
			local is_conver_num = self.data.is_conver_num or ItemWGData.Instance:IsConstItemByUseType(item_cfg.use_type)
			if self.data.num and is_conver_num then
				local conver_num = item_cfg.param1 * self.data.num
				if conver_num > 1 then
					self:SetRightBottomColorText(CommonDataManager.NotConverExtend(conver_num))
					self:SetRightBottomTextVisible(true)
				else
					self:SetRightBottomColorText("")
					self:SetRightBottomTextVisible(false)
				end
			end

		elseif big_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT and
			item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_JINGLING and GameEnum.EQUIP_TYPE_XIAOGUI ~= item_cfg.sub_type then

			if (item_cfg.sub_type >= GameEnum.EQUIP_TYPE_TOUKUI and item_cfg.sub_type <= GameEnum.EQUIP_TYPE_DIAOZHUI)
				or item_cfg.sub_type == GameEnum.EQUIP_TYPE_HUNJIE
				or TianShenWGData.Equip_Pos[item_cfg.sub_type]
				or (item_cfg.sub_type >= GameEnum.E_TYPE_SHENSHOU_PART_0
				and item_cfg.sub_type ~= GameEnum.E_TYPE_SIXIANG
				and item_cfg.sub_type ~= GameEnum.E_TYPE_SIXIANG_BONE
				and item_cfg.sub_type ~= GameEnum.E_TYPE_XIANJIE_EQUI
				and item_cfg.sub_type ~= GameEnum.E_TYPE_SHENJI) then
				local legend_num = self.data.param and self.data.param.star_level or 0
				if legend_num > 0 and item_cfg.sub_type ~= GameEnum.EQUIP_TYPE_HUNJIE then
					self:SetLeftTopImg(legend_num)
				end

				if self.data.star_level ~= nil and self.data.star_level >= 1 then
					self:ResetStar()
					self:SetLeftTopImg(self.data.star_level)
				elseif self.data.param0 and self.data.param0 > 0 then
					self:ResetStar()
					self:SetLeftTopImg(self.data.param0)
				end

				local specail_order_str = EquipBodyWGData.Instance:GetEquipSpecailOrderSignStr(item_cfg.order)

				if TianShenWGData.Equip_Pos[item_cfg.sub_type] then
					self:SetRightTopImageNumText(self.data.grade_level,item_cfg)
				elseif (item_cfg.sub_type >= GameEnum.EQUIP_TYPE_TOUKUI and item_cfg.sub_type <= GameEnum.EQUIP_TYPE_DIAOZHUI) 
					and (specail_order_str and "" ~= specail_order_str) then
					-- 特殊肉身装备策划需要显示特殊文本
					self:SetRightTopImageNumText(nil, specail_order_str)
				elseif item_cfg.sub_type < GameEnum.E_TYPE_SHENSHOU_PART_0 or item_cfg.sub_type > GameEnum.E_TYPE_SHENSHOU_PART_4 then
					self:SetRightTopImageNumText(item_cfg.order, item_cfg)
				end

			-- 四象道具
			elseif item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG then
				self:ResetStar()
				local show_star = self.data.star or FightSoulWGData.Instance:GetFightSoulItemStar(self.data.item_id)
				self:SetLeftTopImg(show_star)
				-- self:SetLeftBottomColorText(string.format("+%s", self.data.level))
				-- self:SetLeftBottomColorTextVisible(true)
				if self.data.level and self.data.level > 0 then
					self:SetRightBottomColorText(string.format("+%s", self.data.level))
					self:SetRightBottomTextVisible(true)
				else
					self:SetRightBottomTextVisible(false)
				end
			-- 四象魂骨道具
			elseif item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then
				self:ResetStar()

				local param_star = self.data.param and self.data.param.star_level
				local show_star = self.data.star or param_star or 0
				self:SetLeftTopImg(show_star)
				local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(self.data.item_id)
				self:SetSiXiangSuit(fight_soul_type, suit_type)
				if self.item_tip_from == ItemTip.FROM_FIGHT_SOUL_BONE_WEAR then
					local is_wear, slot = FightSoulWGData.Instance:GetTypeIsWear(fight_soul_type)
					local part_data = FightSoulWGData.Instance:GetBonePartDataBySlotPart(slot, bone_part)
					local strength_level = part_data and part_data.slot_level or 0
					if strength_level > 0 then
						self:SetRightBottomColorText(string.format("+%s", strength_level))
						self:SetRightBottomTextVisible(true)
					else
						self:SetRightBottomTextVisible(false)
					end
				end
			elseif item_cfg.sub_type == GameEnum.E_TYPE_SHENJI then
				self:SetShenJiSixStar()
				self:SetRareFlag()
				self:SetRightTopImageNumText(nil, "")
				self:SetXqTypeImg()
			elseif item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then
				self:ResetStar()
				if self.data.is_wear then
					local star_num = 0
					local limit_color = FairyLandEquipmentWGData.Instance:GetEvolveUpColorLimit()
					if self.data.star then
						star_num = item_cfg.color >= limit_color and self.data.star or 0
					else
						local slot = self.data.slot
						local part = self.data.part
						star_num = FairyLandEquipmentWGData.Instance:GetHEEvolveStarNum(slot, part)
						star_num = item_cfg.color >= limit_color and star_num or 0
					end
					self:SetLeftTopImg(star_num)
				end

				local temp_data = FairyLandEquipmentWGData.Instance:HolyEquipItemData(item_cfg.id)
				local slot = temp_data.slot
				if slot then
					local slot_cfg = FairyLandEquipmentWGData.Instance:GetGodBodyCfg(slot)
					self:SetRightBottomText(slot_cfg.short_name)
					self:SetRightBottomTextVisible(true)
				end
			else
				self:SetLeftTopText(CommonDataManager.GetDaXie(item_cfg.order) .. Language.Common.Jie, ITEM_COLOR[item_cfg.color])
			end

		elseif big_type == GameEnum.ITEM_BIGTYPE_VIRTUAL then
			self:SetRightTopImageNumText(item_cfg.order, item_cfg)
			if item_cfg.star_level and item_cfg.star_level ~= "" and item_cfg.star_level > 0 then
				self:ResetStar()
				self:SetLeftTopImg(item_cfg.star_level)
			end
		end
		
        self:SetMountPetEquipFlag(self.data.item_id)
		if EquipmentWGData.Instance:IsBaoShiItem(self.data.item_id) then
			local stone_cfg = EquipmentWGData.Instance:GetBaoShiCfgByItemId(self.data.item_id)
			if stone_cfg and stone_cfg.level and stone_cfg.level > 0 then
				self:SetDaXieGradeInfo(CommonDataManager.GetDaXie(stone_cfg.level))
			end
		end

		if EquipmentLingYuWGData.Instance:IsLingYuItem(self.data.item_id) then
			local lingyu_cfg = EquipmentLingYuWGData.Instance:GetLingYuCfgByItemId(self.data.item_id)
			if lingyu_cfg and lingyu_cfg.level and lingyu_cfg.level > 0 then
				self:SetDaXieGradeInfo(CommonDataManager.GetDaXie(lingyu_cfg.level))
			end
		end

		if ShiTianSuitStrengthenWGData.Instance:IsShiTianStone(self.data.item_id) then
			local stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(self.data.item_id)
			if stone_cfg and stone_cfg.level and stone_cfg.level > 0 then
				self:SetDaXieGradeInfo(CommonDataManager.GetDaXie(stone_cfg.level))
			end
		end

		-- 使用CD
		if big_type == GameEnum.ITEM_BIGTYPE_EXPENSE then
			if item_cfg.colddown_id > 0 and item_cfg.client_colddown > 0 then
				local cd_end_time = ItemWGData.Instance:GetColddownEndTime(item_cfg.colddown_id)
				self:SetCD(cd_end_time, item_cfg.client_colddown)
			end
		end

		if item_cfg.time_length and item_cfg.time_length > 0 and GameEnum.EQUIP_TYPE_XIAOGUI ~= item_cfg.sub_type then
			self:SetTopLeftFlag(true, ResPath.GetLoadingPath("a3_ty_bq_xian"))
		-- elseif item_cfg.need_gold and item_cfg.need_gold > 0 and GameEnum.EQUIP_TYPE_XIAOGUI ~= item_cfg.sub_type then
		-- 	self:SetTopLeftFlag(true, ResPath.GetLoadingPath("a3_ty_bq_shen"))
		else
			self.righttop_text_isvisible = false
		end

		if big_type == GameEnum.ITEM_BIGTYPE_GIF then
			if item_cfg.item_cell_star_level and item_cfg.item_cell_star_level > 0 then
				self:SetLeftTopImg(item_cfg.item_cell_star_level)
			-- 自选显示
			-- elseif item_cfg.max_open_num and item_cfg.max_open_num > 0 then
			-- 	self:SetRightTopImageNumText(nil,Language.Common.ChooseMyself)
			end
			if item_cfg.jieshu and item_cfg.jieshu > 0 then
				self:SetRightTopImageNumText(item_cfg.jieshu, item_cfg)
			end
		end

		---限时道具倒计时显示
		self:FlushItemCountDown()

		--被动消耗类型
		if big_type == GameEnum.ITEM_BIGTYPE_OTHER and self.item_tip_from == ItemTip.FROM_BAOSHI then
			self:FlushUpFlagIcon(self.data)
		end

		local subscrip_flag_txt = item_cfg.subscrip_flag
		if not item_cfg.subscrip_flag or item_cfg.subscrip_flag == "" then
			subscrip_flag_txt = self.data.subscrip_flag--有些标签显示仅限于某个界面,自己传过来显示
		end
		self:SetSubscriptFlag(subscrip_flag_txt, self.data.subscrip_biaoqian_flag)

		self:FlushBeastItemIcon(self.data)
	end

	if self.flush_callback then
		self.flush_callback()
	end

	self:SetDuoBeiInfo()
end

function DiamondItemCell:SetDuoBeiShow(b)
	self:TrySetActive("duobei", nil, b)
end

function DiamondItemCell:SetChipIsShow(item_cfg)
	local is_show_chip = false
	if item_cfg and item_cfg.is_chip and 1 == item_cfg.is_chip then
		is_show_chip = true
	end

	self:TrySetActive("item_chip", nil, is_show_chip)
end

--设置骑宠装备标记
function DiamondItemCell:SetMountPetEquipFlag(item_id)
	local is_show_flag = MountLingChongEquipWGData.Instance:GetIsMountLingChongEquip(item_id)
    if is_show_flag then
        local this_cfg, show_type, part = MountLingChongEquipWGData.Instance:GetMountLingChongEquipData(item_id)
        local str = "a3_mount_equip_" .. show_type
        local bundle, asset = ResPath.GetLoadingPath(str)
        if bundle and asset then
        	self:SetMountEquipIconIsShow(true)
        	local cbdata = DiamondItemCell.GetCBData()
        	cbdata[1] = self:Nodes("mount_equip_flag")
            self:Nodes("mount_equip_flag").image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
        end
    else
        self:SetMountEquipIconIsShow(false)
    end
end

function DiamondItemCell:SetMountEquipIconIsShow(is_visible)
	self:TrySetActive("mount_equip_flag", nil, is_visible)
end


function DiamondItemCell:SetEqSuitIconIsShow(is_visible)
	self:TrySetActive("eq_suit", nil, is_visible)
end

function DiamondItemCell:SetEqSuitIcon(bundle, asset, is_visible)
	self:TrySetActive("eq_suit", nil, is_visible)
	self:Nodes("eq_suit").image:LoadSpriteAsync(bundle, asset)
end

function DiamondItemCell:SetSiXiangSuit(fight_soul_type, suit_type)
	if fight_soul_type == nil or suit_type == nil or suit_type <= 0 then
		self:TrySetActive("suit_flag", nil, false)
		return
	end

	local bundle, asset = ResPath.GetLoadingPath("a3_ty_db" .. suit_type)
	self:Nodes("suit_flag").image:LoadSpriteAsync(bundle, asset)
	self:TrySetActive("suit_flag", nil, true)
	self:Nodes("suit_flag","suit_text").text.text = Language.FightSoul.SuitStr[suit_type]
end

function DiamondItemCell:ShowEquipWearIcon()
	local is_show_icon = false
	if self.data and self.data.frombody and self.item_tip_from == ItemTip.FROM_EQUIMENT_HECHENG then
		is_show_icon = true
	end

	self:TrySetActive("icon_is_equip", nil, is_show_icon)
end

-- 限时道具倒计时显示
function DiamondItemCell:FlushItemCountDown()
	if self.item_count_down and CountDownManager.Instance:HasCountDown(self.item_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.item_count_down)
	end

	-- 不是来自背包、没有失效时间直接返回
	if self.item_tip_from ~= ItemTip.FROM_BAG or not self.data or not self.data.invalid_time then
		return
	end

	if self.data.invalid_time <= 0 then
		return
	end

	-- 过期显示
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local valid_time = self.data.invalid_time - server_time

	--小鬼超过6个小时不显示倒计时
	if ItemWGData.GetIsXiaogGui(self.data.item_id) and valid_time >= 21600 then
		return
	end

	if valid_time <= 0 then
		-- 如果显示了物品数量，则把倒计时显示到左下角, 避免覆盖掉物品数量
		if self:NeedShowNum() then
			self:SetLeftBottomColorText(Language.Bag.Invaild_2, ITEM_NUM_COLOR.NOT_ENOUGH)
			self:SetLeftBottomColorTextVisible(true)
		else
			self:SetRightBottomColorText(Language.Bag.Invaild_2, ITEM_NUM_COLOR.NOT_ENOUGH)
			self:SetRightBottomTextVisible(true,false)
		end
		self:SetGradumalMaskEnable(true)
		return
	end

	if not self.item_count_down then
		DiamondItemCell.CountDownIndex = DiamondItemCell.CountDownIndex + 1
		self.item_count_down = "diamond_item_count_down_" .. DiamondItemCell.CountDownIndex
	end
	self:UpdateItemCountDown(0, valid_time)
	CountDownManager.Instance:AddCountDown(self.item_count_down, BindTool.Bind1(self.UpdateItemCountDown, self), BindTool.Bind1(self.OnFlush, self), self.data.invalid_time, nil, 1)
end

function DiamondItemCell:UpdateItemCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time

	if valid_time > 0 then
		-- 如果显示了物品数量，则把倒计时显示到左下角, 避免覆盖掉物品数量
		-- 2022/02/20 策划说 超过1天，显示X天，不够1天显示XX小时XX分XX秒  --FormatSecondDHM9
		if self:NeedShowNum() then
			self:SetLeftBottomColorText(TimeUtil.FormatSecondDHM5(valid_time), ITEM_NUM_COLOR.ENOUGH)
			self:SetLeftBottomColorTextVisible(true)
		else
			self:SetRightBottomColorText(TimeUtil.FormatSecondDHM5(valid_time), ITEM_NUM_COLOR.ENOUGH) 
			self:SetRightBottomTextVisible(true)
		end
	end
end

function DiamondItemCell:SetTopLeftFlag(value, bundle, asset, flag_txt_str)
	self:TrySetActive("top_left_flag", nil, value)
	if value then
		local cbdata = DiamondItemCell.GetCBData()
		cbdata[1] = self:Nodes("top_left_flag")
		self:Nodes("top_left_flag").image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
		self:Nodes("top_left_flag", "top_left_flag_txt").text.text = flag_txt_str or ""
	end
end

function DiamondItemCell:ListenClick()
	self:Nodes("button").button:AddClickListener(BindTool.Bind(self.OnClick, self))
end

function DiamondItemCell:SetNeedItemGetWay(state)
	self.need_item_get_way = state
end

--设置默认显示购买的数量
function DiamondItemCell:SetDefShowBuyCount(num)
    self.show_buy_num = num
end

-- 设置物品Tips右边按钮的名称和点击回调
function DiamondItemCell:SetItemTipsBtnClickCallback(btn_click_callback)
	self.item_tips_btn_click_callback = btn_click_callback
end

-- 点击格子
function DiamondItemCell:OnClick()
	if self.tip_callback ~= nil then
		local is_black = self.tip_callback(self.data)
		if is_black == true then
			return
		end
	end

	if self.data and self.is_showtip then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if nil == item_cfg then return end

		TipWGData.Instance:SetDefShowBuyCount(self.show_buy_num or 1)
		if self.need_item_get_way then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
		else
			TipWGCtrl.Instance:OpenItem(self.data, self.item_tip_from or ItemTip.FROM_NORMAL, nil, nil, self.item_tips_btn_click_callback)
		end
	end
	BaseRender.OnClick(self)

	-- if not self.is_showtip then return end
	-- local from_view = data.from_view
	-- local param_t = data.param_t
	-- local close_call_back = data.close_call_back or function() self:SetHighLight(false) end
end

--设置背景
function DiamondItemCell:SetCellBg(bundle, asset, need_set_native)
	self.root_node.image:LoadSpriteAsync(bundle, asset, function()
		if need_set_native then
			self.root_node.image:SetNativeSize()
		end
	end)
end

function DiamondItemCell:SetCellBgEnabled(bool)
	self.root_node.image.enabled = bool
end

-- 设置物品图标
function DiamondItemCell:SetItemIcon(bundle, asset)
	self:Nodes("item_icon"):SetActive(true)
	local cbdata = DiamondItemCell.GetCBData()
	cbdata[1] = self:Nodes("item_icon")
	cbdata[2] = self
	self:Nodes("item_icon").image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBackSize, cbdata)
	self:Nodes("item_icon").image:SetNativeSize()

	if self.item_icon_scale then
		self:Nodes("item_icon").transform.localScale = self.item_icon_scale
	else
		self:Nodes("item_icon").transform.localScale = ITEM_ICON_SCALE
	end
end

function DiamondItemCell:SetItemTitleIcon(bundle, asset)
	self:Nodes("item_title_icon"):SetActive(true)
	local cbdata = DiamondItemCell.GetCBData()
	cbdata[1] = self:Nodes("item_title_icon")
	self:Nodes("item_title_icon").image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
end

function DiamondItemCell:SetItemIconScale(width, height)
	self.item_icon_custom_scale = Vector2(width, height)
end

-- 装备格子背景图标
function DiamondItemCell:SetItemIconValue(value)
	self.is_value = value
	self:TrySetActive("item_icon", nil, self.is_value)
end

-- 图标透明度
function DiamondItemCell:SetItemIconAlpha(color)
	local item_icon = self:TryGetNodes("item_icon")
	if item_icon then
		item_icon.image.color = color
	end
end

-- 图标透明度
function DiamondItemCell:SetItemIconLocalScale(scale)
	self.item_icon_scale = Vector3(scale, scale, scale)

	local item_icon = self:TryGetNodes("item_icon")
	if item_icon then
		item_icon.transform.localScale = Vector3(scale, scale, scale)
	end
end

--元宝显示额度
function DiamondItemCell:SetCoinNumShow(enable)
	self.is_show_coin_num = enable
end

function DiamondItemCell:ClearAllParts()
	for k,v in pairs(self.nodes) do
		if v.obj and not IsNil(v.obj.gameObject) then
			v.obj.gameObject:SetActive(false)
		end
	end

	local item_icon = self:TryGetNodes("item_icon")
	if item_icon and item_icon.rect then
		item_icon.rect.sizeDelta = ITEM_ICON_SIZE
	end

	self:SetItemIconAlpha(color_white)

	if self.use_button then
		self:TrySetActive("button", nil, self.use_button)
	else
		self:SetButtonComp(false)
	end
	self:MakeGray(false)

	-- 通过代码创建的需要判断是否创建过
	if self.item_desc then self.item_desc:SetActive(true) end
	self:SetDefaultEff(false)

	if self.item_count_down and CountDownManager.Instance:HasCountDown(self.item_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.item_count_down)
	end
end


function DiamondItemCell:SetEffectRootEnable(enable)
	self:TrySetActive("EffectRoot", nil, enable)
end

function DiamondItemCell:ResetStar()
	self:SetLeftTopImg(0)
end

function DiamondItemCell:SetHideNumTxtLessNum(less_num)
	self.hide_numtxt_less_num = less_num
end

-- 设置左下角文字
function DiamondItemCell:SetLeftBottomColorText(text, color)
	self:Nodes("left_bottom_text").text.text = ToColorStr(text, color)
end

-- 设置左下角文字显隐
function DiamondItemCell:SetLeftBottomColorTextVisible(enable)
	self:TrySetActive("left_bottom_text", nil, enable)
end

local TypeCanvas = typeof(UnityEngine.Canvas)
function DiamondItemCell:SetRightBottomText(text)
	local bottom_text = self:Nodes("right_bg_img", "text_bottom_text").transform
	local parent = bottom_text.parent

	bottom_text.localScale = Vector3(1, 1, 1)
	for i = 1, 5 do --格子缩小，文字不缩小，暂时处理
		if parent.localScale.x < 1 and parent.localScale.x >= 0.5 then
			bottom_text.localScale = Vector3(1 / parent.localScale.x, 1 / parent.localScale.y, 1 / parent.localScale.z)
			break
		else
			parent = parent.parent
			if parent and parent.gameObject:GetComponent(TypeCanvas) then
				break
			end
		end
	end

	self:Nodes("right_bg_img", "text_bottom_text").text.text = text
end

-- 设置右下角文字
-- ITEM_NUM_COLOR.DEFAULT	-- 默认数量描述(白)
-- ITEM_NUM_COLOR.ENOUGH	-- 数量足够 (绿)
-- ITEM_NUM_COLOR.NOT_ENOUGH-- 数量不够（红）
function DiamondItemCell:SetRightBottomColorText(text, color)
	color = color or ITEM_NUM_COLOR.DEFAULT
	self.text_bottom_right_text = text or ""
	self:SetRightBottomText(ToColorStr(self.text_bottom_right_text, color))
end

-- 设置右下角文字对齐方式
function DiamondItemCell:SetRightBottomColorTextAlignment()
	self:Nodes("right_bg_img", "text_bottom_text").text.alignment = UnityEngine.TextAnchor.MiddleCenter
end

function DiamondItemCell:SetRightBottomTextVisible(is_visible, need_bg)
	if need_bg == nil then
		need_bg = true
	end

	if is_visible or need_bg then
		-- self:Nodes("right_bg_img").image.enabled = need_bg
		self:Nodes("right_bg_img"):SetActive(is_visible)
	else
		local node = self:TryGetNodes("right_bg_img")
		if node then
			-- node.image.enabled = need_bg
			node:SetActive(is_visible)
		end
	end
end

function DiamondItemCell:GetRightBottomTextVisible()
	return self:Nodes("right_bg_img").gameObject.activeInHierarchy
end

-- 设置绑定图标
function DiamondItemCell:SetBindIconVisible(is_visible)
	-- 可交易标识
	local can_trade_flag = false -- MarketWGData.Instance:CheckIsCanMarket(self.data) 策划需求不再显示交易标志
	-- 不可交易  不绑定
	if not can_trade_flag and not is_visible then
		self:TrySetActive("bind_icon", nil, false)
		self:TrySetActive("CanTrade", nil, false)
	end
	-- 需要显示可交易秤砣
	if self.show_can_trade or can_trade_flag then
		if is_visible then
			self:TrySetActive("bind_icon", nil, is_visible)
			self:TrySetActive("CanTrade", nil, false)
		else
			self:TrySetActive("bind_icon", nil, false)
			if self.item_tip_from == ItemTip.FROM_BAG or self.item_tip_from == ItemTip.FROM_MARKET_JISHOU or self.item_tip_from == ItemTip.FROM_MARKET_SHANGJIA
				or self.item_tip_from == ShenShouEquipTip.FROM_MARKET_SHANGJIA or self.item_tip_from == ItemTip.FROM_CELL_CHATEXT then
				self:TrySetActive("CanTrade", nil, can_trade_flag)
			end
		end
	else
		self:TrySetActive("CanTrade", nil, false)
		self:TrySetActive("bind_icon", nil, is_visible)
	end
end

function DiamondItemCell:SetHideRightDownBgLessNum(less_num)
	self.hide_right_down_cell_num = less_num
end

-- 设置是否需要不可用遮罩
function DiamondItemCell:SetNeedUselessModdal(need_useless_modal)
	self.need_useless_modal = need_useless_modal
end

-- 设置不可用遮罩
function DiamondItemCell:SetUselessModalVisible(is_visible)
	self:SetUselessModalActive(is_visible)

	if is_visible then
		self:SetGradumalMaskEnable(false)
	end

	-- if self.need_useless_modal and is_visible then
	-- 	self:SetGradumalMaskEnable(true)
	-- end
end

function DiamondItemCell:SetUselessModalActive(active)
	self:TrySetActive("useless_modal", nil, active)
end

function DiamondItemCell:SetRightTopImageText(str)
	self:SetRightTopImageTextActive(str ~= nil and str ~= "")
	self:Nodes("right_top_imgnum_txt").text.text = str
end

function DiamondItemCell:SetRightTopImageTextActive(active)
	self:TrySetActive("right_top_imgnum_txt", nil, active)
end

--设置右上角阶数数字
function DiamondItemCell:SetRightTopImageNumText(num, item_cfg)
	if num ~= nil then
		if item_cfg ~= nil and item_cfg.is_zhizun == 1 then
			self:Nodes("right_top_imgnum_txt").text.text = string.format(Language.Role.XXJieZun, num)
		else
			local show_str = Language.Role.XXJie
			if self.data and self.data.item_id and FairyLandEquipmentWGData.Instance:GetGBEquipVirtualCfgByItemId(self.data.item_id) then
				--仙界装备虚拟物品,显示为"X转"
				show_str = Language.Role.XXZhuan
			end

			self:Nodes("right_top_imgnum_txt").text.text = string.format(show_str, num)
		end

		self:TrySetActive("right_top_imgnum_txt", nil, 0 ~= num)
	end

	-- if num ~= nil and num ~= 0 then
	-- 	print_error(item_cfg.is_zhizun == 1)
	-- 	if item_cfg ~= nil and item_cfg.is_zhizun == 1 then
	-- 		self:Nodes("right_top_imgnum_txt").text.text = string.format(Language.Role.XXJieZun, num)
	-- 	else
	-- 		local show_str = Language.Role.XXJie
	-- 		if self.data and self.data.item_id and FairyLandEquipmentWGData.Instance:GetGBEquipVirtualCfgByItemId(self.data.item_id) then
	-- 			--仙界装备虚拟物品,显示为"X转"
	-- 			show_str = Language.Role.XXZhuan
	-- 		end
	-- 		self:Nodes("right_top_imgnum_txt").text.text = string.format(show_str, num)
	-- 	end
	-- end

	-- if num ~= nil then
	-- 	if item_cfg then
	-- 		if item_cfg.is_zhizun == 1 then
	-- 			self:Nodes("right_top_imgnum_txt").text.text = string.format(Language.Role.XXJieZun, num)
	-- 		end
	-- 	else
	-- 		self:Nodes("right_top_imgnum_txt").text.text = string.format(Language.Role.XXJie, num)
	-- 	end

	-- 	self:TrySetActive("right_top_imgnum_txt", nil, 0 ~= num)
	-- end

	if num == nil and type(item_cfg) == "string" then
		self:Nodes("right_top_imgnum_txt"):SetActive(true)
		self:Nodes("right_top_imgnum_txt").text.text = item_cfg
	end
end

-- 设置品质图标
function DiamondItemCell:SetQualityIcon(bundle, asset)
	self:Nodes("quality_icon"):SetActive(true)
	local cbdata = DiamondItemCell.GetCBData()
	cbdata[1] = self:Nodes("quality_icon")
	self:Nodes("quality_icon").image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
end

-- 设置品质图标
function DiamondItemCell:SetQualityIconVisible(is_visible)
	self:TrySetActive("quality_icon", nil, is_visible)
end

--是否需要显示品质图标
function DiamondItemCell:SetShowCualityBg(enable)
	self.show_quality = enable
end

--是否使用圆形道具品质背景icon
function DiamondItemCell:SetIsUseRoundQualityBg(is_use_round_quality_bg)
	self.is_use_round_quality_bg = is_use_round_quality_bg
end

--品质图标置灰
function DiamondItemCell:SetGraphicGreyCualityBg(is_grey)
	if is_grey or nil ~= self:TryGetNodes("quality_icon") then
		XUI.SetGraphicGrey(self:Nodes("quality_icon"), is_grey)
	end

	if is_grey or nil ~= self:TryGetNodes("item_icon") then
		XUI.SetGraphicGrey(self:Nodes("item_icon"), is_grey)
	end
end

-- 置灰图标
function DiamondItemCell:SetIconGrey(is_grey)
	if is_grey or nil ~= self:TryGetNodes("item_icon") then
		XUI.SetGraphicGrey(self:Nodes("item_icon"), is_grey)
	end
end

-- 设置左上角文字
function DiamondItemCell:SetLeftTopText(text, color)
	color = color or COLOR3B.D_WHITE
	self:Nodes("top_left_text").text.text = ToColorStr(text, color)
	self:Nodes("top_left_text"):SetActive(true)
end

function DiamondItemCell:SetLeftTopTextVisible(is_visible)
	self:TrySetActive("top_left_text", nil, is_visible)
end

--第5星变成皇冠   Star5 是皇冠
function DiamondItemCell:SetLeftTopImg(legend_num)
	if nil == legend_num or nil == tonumber(legend_num) or legend_num == 0 then
		self:TrySetActive("Star", nil, false)
		return
	end

	local min_value = math.max(0, legend_num - GameEnum.ITEM_MAX_STAR)
	for i = 1, 10 do
		local star = self:Nodes("Star", "Star_" .. i)
		if star then
			star:SetActive(i >= min_value and i <= legend_num)
		end
	end

	self:TrySetActive("Star", nil, true)
end

function DiamondItemCell:MakeGray(is_enable)
	if self.node_list["DiamondItemCell"] and not IsNil(self.node_list["DiamondItemCell"].gameObject) then
		XUI.SetGraphicGrey(self.node_list["DiamondItemCell"], is_enable)
	end

	if self.need_default_eff then
		self:SetDefaultEff(not is_enable)
	end
end

--设置品质特效
function DiamondItemCell:SetQualityEffect(effect_id, scale)

end

--设置上升标记
function DiamondItemCell:SetUpFlagIconVisible(is_visible)
	self:TrySetActive("upflag_icon", nil, is_visible)
end

--設置上升標記圖片
function DiamondItemCell:SetUpFlagIcon(bundle, asset)
	local cbdata = DiamondItemCell.GetCBData()
	cbdata[1] = self:Nodes("upflag_icon")
	self:Nodes("upflag_icon").image:LoadSpriteAsync(bundle, asset, LoadSpriteCallBack, cbdata)
end

function DiamondItemCell:SetCanOperateIconVisible(is_visible)
	self:TrySetActive("canoperate_icon", nil, is_visible)
end

function DiamondItemCell:SetItemTipFrom(item_tip_from)
	self.item_tip_from = item_tip_from
end

function DiamondItemCell:SetIsShowTips(flag)
	self.is_showtip = flag
end

function DiamondItemCell:GetIsShowTips()
	return self.is_showtip
end

function DiamondItemCell:SetLingQuVisible(is_visible)
	self:TrySetActive("select_effect_new", nil, is_visible)
end

function DiamondItemCell:SetDuoBeiInfo()
	if self.data.show_duobei then
		if self.data.task_type ~= nil then
			local is_duobei, duobei_count = ActivityWGData.Instance:GetItemDuoBeiTimes(self.data.task_type, self.data.item_id)
			if is_duobei then
				self:SetDuoBeiMsg(duobei_count)
			end
		end
	end
end

function DiamondItemCell:SetDuoBeiMsg(duobei_count)
    if duobei_count then
    	local bundle, asset
    	if duobei_count <= 2 then
       		bundle, asset = ResPath.GetLoadingPath("a2_ty_wpk_sb")
       	else
       		bundle, asset = ResPath.GetLoadingPath("a2_ty_wpk_db")
       	end

        self:Nodes("duobei"):SetActive(true)
        self:Nodes("duobei", "duobei_value").text.text = string.format(Language.FuBenPanel.DuoBei2, duobei_count)
        self:Nodes("duobei").image:LoadSpriteAsync(bundle, asset, function()
        	self:Nodes("duobei").image:SetNativeSize()
    	end)
	else
		self:TrySetActive("duobei", nil ,false)
    end
end

function DiamondItemCell:SetDaXieGradeInfo(daxie_str)
	if daxie_str and daxie_str ~= "" then
		self:Nodes("daxie_garde").text.text = daxie_str
		self:Nodes("daxie_garde"):SetActive(true)
	else
		self:Nodes("daxie_garde"):SetActive(false)
	end
end

--添加判断：原因--哪怕没有数据，按钮也可以点击
function DiamondItemCell:SetButtonComp(is_visible)
	if self.use_button == nil then
		self:TrySetActive("button", nil, is_visible)
	end
end

function DiamondItemCell:SetUseButton(val)
	self.use_button = val
end

function DiamondItemCell:GetButtonComp()
	local node = self:TryGetNodes("button")
	if node then
		return node.gameObject.activeSelf
	end
	return false
end

function DiamondItemCell:UseNewSelectEffect(enable)
	self.use_new_select_effect = enable
end

function DiamondItemCell:SetSelectEffect(is_visible)
	self.is_select_effect = is_visible
	if self.use_new_select_effect then
		self:TrySetActive("select_effect_new", nil, is_visible)
	else
		self:TrySetActive("select_effect", nil, is_visible)
	end
end

function DiamondItemCell:ResetSelectEffect()
	self:TrySetActive("select_effect_new", nil, false)
	self:TrySetActive("select_effect", nil, false)
end

--用basgride生成basecell，点击调用该方法关闭image组件，不要点击选中效果，上述方法会有问题
function DiamondItemCell:SetSelectEffectImage(is_visible)
	local node = self:TryGetNodes("select_effect")
	if node then
		node.image.enabled = is_visible
	end
end

--size大小  width, height
function DiamondItemCell:SetSelectEffectImageRes(bundle, asset, size)
    self:Nodes("select_effect").image:LoadSpriteAsync(bundle, asset, function()
		if not IsEmptyTable(size) and size.width and size.height then
			self:Nodes("select_effect").image.rectTransform.sizeDelta = Vector2.New(size.width, size.height)
		else
			self:Nodes("select_effect").image:SetNativeSize()
		end
    end)
	-- local node = self:TryGetNodes("select_effect")
	-- if node then
	-- 	node.image:LoadSpriteAsync(bundle, asset)
	-- end
end

-- 设置背景文字
function DiamondItemCell:SetItemDesc(text, color, size)
	color = color or COLOR3B.D_WHITE
	if nil == self.item_desc then

		self.item_desc = U3DObject(GameObject.New("ItemDesc"))
		local item_desc_transform = self.item_desc.transform
		item_desc_transform:SetParent(self.root_node.transform, false)

		local text_component = self.item_desc.gameObject:AddComponent(TypeText)
		text_component.text = text
		-- text_component.textFont = text_component.font.ToString().Replace("(UnityEngine.Font)", string.Empty).Trim()
		text_component.textFontSize = size or 20
		text_component.raycastTarget = false

		local rect = self.item_desc.rect
		rect.anchorMin = Vector2(0, 0)
		rect.anchorMax = Vector2(1, 1)
		rect.anchoredPosition3D = Vector3(0, 0, 0)
		rect.sizeDelta = Vector2(0, 0)
	else
		self.item_desc:SetActive(true)
		self.item_desc.text.text = ToColorStr(text, color)
	end
end

function DiamondItemCell:SetPartName(str)
	if str and str ~= "" then
		self:Nodes("partname"):SetActive(true)
	end
	self:Nodes("partname").text.text = str
end

-- 装备能力上升标志
function DiamondItemCell:FlushUpFlagIcon(data)
	local item_id = data and data.item_id or 0
    local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		self:SetUpFlagIconVisible(false)
		return
    end

    if ItemWGData.Instance:GetIsQingyuanEquip(item_id) then --同心锁特殊处理
        self:SetUpFlagIconVisible(false)
        return
    end

	if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG then --四象特殊处理
		self:SetUpFlagIconVisible(false)
		return
	end

	if item_cfg.sub_type == GameEnum.E_TYPE_XIANJIE_EQUI then --圣装特殊处理
		if self.item_tip_from == ItemTip.FROM_HOLY_EQUIP_BAG or self.item_tip_from == ItemTip.FROM_BAG then
			local is_better = FairyLandEquipmentWGData.Instance:HolyEquipIsBetterWear(self.data)
			self:SetUpFlagIconVisible(is_better)
		else
			self:SetUpFlagIconVisible(false)
		end
		return
	end

	if item_cfg.sub_type == GameEnum.E_TYPE_SIXIANG_BONE then --四象魂骨特殊处理
		if self.item_tip_from == ItemTip.FROM_FIGHT_SOUL_BONE or
			self.item_tip_from == ItemTip.FROM_FIGHT_SOUL_BONE_CONTRAST then
			local is_better = FightSoulWGData.Instance:GetIsBetterBone(data)
			self:SetUpFlagIconVisible(is_better)
		else
			self:SetUpFlagIconVisible(false)
		end
		return
	end

	local mainrole_level = GameVoManager.Instance:GetMainRoleVo().level
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	-- 来自背包
	if self.item_tip_from == ItemTip.SHANGGU_JINGLING_EQUIP
		or self.item_tip_from == ItemTip.FROM_BAG
		or self.item_tip_from == ItemTip.FROM_MARKET_JISHOU
		or self.item_tip_from == ShenShouEquipTip.FROM_MARKET_SHANGJIA
		or self.item_tip_from == ItemTip.FROM_STORGE_ON_GUILD_STORGE
		or self.item_tip_from == ItemTip.FROM_BAG_ON_GUILD_STORGE
		or self.item_tip_from ==ItemTip.FROM_EQUIMENT_HECHENG
		or self.item_tip_from ==ItemTip.FROM_ROLE_BAG_TRANSSEX
		or self.item_tip_from == ItemTip.FROM_MARKET_SHANGJIA then
		local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
		local mix_limit_prof = EquipWGData.GetEquipProfLimit(equip_part, item_cfg.order)
		local is_level_meet = mainrole_level >= item_cfg.limit_level
		local is_prof_level_meet = (not item_cfg.is_limit_zhuanzhi_prof or item_cfg.is_limit_zhuanzhi_prof == 0) or (prof_level >= mix_limit_prof)

		if not self:CheckProfLimit(item_cfg) then
			self:SetUpFlagIconVisible(false)
			self:SetUselessModalVisible(true)
			return
		else
			self:SetUselessModalVisible(false)
		end

		self:SetUpFlagIconVisible(true)
		local is_up = EquipWGData.Instance:GetIsBetterEquip(self.data, true)

		if ItemWGData.GetIsXiaogGui(item_id) then
			local is_overdue = false
			if 0 ~= self.data.invalid_time then
				local time = math.max(self.data.invalid_time - TimeWGCtrl.Instance:GetServerTime(), 0)
				is_overdue = time <= 0 and true or false
			end

			if is_overdue then -- 过期
				self:SetUpFlagIconVisible(false)
				return
			else
				local guard_info = EquipWGData.Instance:GetmpGuardInfo()
				if guard_info then
					local cfg = EquipmentWGData.GetXiaoGuiCfg(self.data.item_id)
					local guard_type = cfg and cfg.impguard_type
					if guard_info.used_imp_type_2 == -1 then
						if guard_info.used_imp_type_1 == 0 then
							is_up = true
						else
							local capability_1 = AttributeMgr.GetAttributteByClass(cfg)
							capability_1 = AttributeMgr.GetCapability(capability_1)
							local cfg_bag = EquipmentWGData.GetXiaoGuiCfg(guard_info.item_wrapper[1].item_id)
							local capability_2 = AttributeMgr.GetAttributteByClass(cfg_bag)
							capability_2 = AttributeMgr.GetCapability(capability_2)
							local sub = capability_1 - capability_2
							if sub > 0 then
								is_up = true
							elseif sub == 0 then
								self:SetUpFlagIconVisible(false)
								return
							else
								is_up = false
							end
						end
					else   -- 两个格子都开启了
						local has_sametype = nil
						for i=1,2 do
							local _,temp_type = math.modf(guard_info['used_imp_type_' .. i]/2)
							temp_type = temp_type > 0 and 1 or 2
							if temp_type == guard_type then
								has_sametype = i
								break
							end
						end
						if has_sametype then   -- 有相同类型的小鬼
							local capability_1 = AttributeMgr.GetAttributteByClass(cfg)
							capability_1 = AttributeMgr.GetCapability(capability_1)
							local cfg_bag = EquipmentWGData.GetXiaoGuiCfg(guard_info.item_wrapper[has_sametype].item_id)
							local capability_2 = AttributeMgr.GetAttributteByClass(cfg_bag)
							capability_2 = AttributeMgr.GetCapability(capability_2)
							local sub = capability_1 - capability_2
							if sub > 0 then
								is_up = true
							elseif sub == 0 then
								self:SetUpFlagIconVisible(false)
								return
							else
								is_up = false
							end
						else
							is_up = true
						end
					end
				end
			end
		elseif ItemWGData.Instance:GetIsQingyuanEquip(item_id) then --情缘装备
			is_up = MarryWGData.Instance:GetIsBatterEquip(item_id)
		elseif item_cfg.sub_type == GameEnum.E_TYPE_SHENJI then --仙器装备特殊处理
			is_up = HiddenWeaponWGData.Instance:CheckEquipCompare(data)
		elseif item_cfg.sub_type == GameEnum.E_TYPE_SHENSHOU_PART_0
			or item_cfg.sub_type == GameEnum.E_TYPE_SHENSHOU_PART_1
			or item_cfg.sub_type == GameEnum.E_TYPE_SHENSHOU_PART_2
			or item_cfg.sub_type == GameEnum.E_TYPE_SHENSHOU_PART_3
			or item_cfg.sub_type == GameEnum.E_TYPE_SHENSHOU_PART_4 then
			if data and data.param then -- 具体装备
				is_up =	ShenShouWGData.Instance:GetShenShouEquipHaveUpFlag(data)
			else--合成目标神兽装备
				is_up = ShenShouWGData.Instance:GetComposeProductUpFlag(data)
			end
		end

		local b,a = "",""
		if is_up and is_level_meet and is_prof_level_meet then
			b,a = ResPath.GetCommonImages("a3_ty_up_1")
		elseif is_up then
			b,a = ResPath.GetCommonImages("a3_ty_up_1")
		else
			self:SetUpFlagIconVisible(false)
			return
		end
		self:SetUpFlagIcon(b, a)

	elseif self.item_tip_from == ItemTip.FROM_BAOSHI then
		if self.data.to_up_level then
			local b,a = ResPath.GetCommonImages("a3_ty_up_1")
			self:SetUpFlagIconVisible(true)
			self:SetUpFlagIcon(b,a)
		else
			self:SetUpFlagIconVisible(false)
		end
	elseif self.item_tip_from == ItemTip.FROM_TIANSHEN_SHENSHI_BAG then
		if not self.data.up_flag_list then
			self:SetUpFlagIconVisible(false)
		else
			local up_flag = false
			for k,v in pairs(self.data.up_flag_list) do
				if v.bag_index == self.data.bag_index then
					up_flag = true
				end
			end
			self:SetUpFlagIconVisible(up_flag)
		end
	elseif self.item_tip_from == ItemTip.FROM_BAG_MELTING then
		if not self:CheckProfLimit(item_cfg) then
			self:SetUpFlagIconVisible(false)
			self:SetUselessModalVisible(true)
		else
			self:SetUselessModalVisible(false)
		end
	end
end

function DiamondItemCell:CheckProfLimit(item_cfg)
	-- 是否是可穿戴职业  5不限制职业
	local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()
	local prof_meet = item_cfg.limit_prof == prof or item_cfg.limit_prof == GameEnum.ROLE_PROF_NOLIMIT
	local sex_meet = item_cfg.limit_sex == sex or item_cfg.limit_sex == GameEnum.SEX_NOLIMIT
	local is_sure_prof = prof_meet and sex_meet
	--同性别，不同职业的道具，只限制右下角的符号，不做遮罩处理
	self:SetNeedUselessModdal(not sex_meet)
	return is_sure_prof
end

function DiamondItemCell:IsCanDJ(is_can_DJ)
	self.root_node.image.raycastTarget = is_can_DJ
end

function DiamondItemCell:NeedDefaultEff(value)
	-- body
	self.need_default_eff = value
end

function DiamondItemCell:SetLockImgEnable(enable)
	self:TrySetActive("lock_img", nil, enable)
end

--渐变模板是否显示
function DiamondItemCell:SetGradumalMaskEnable(enable)
	self:TrySetActive("gradumal_mask", nil, enable)
end

--设置默认特效
function DiamondItemCell:SetDefaultEff(enable)
	local ui_effect_t = self.is_use_round_quality_bg and BaseCell_Ui_Circle_Effect or BaseCell_Ui_Effect
	for k,v in pairs(ui_effect_t) do
		self:SetEffectEnable(false, v)
	end
	
	if not self.data then
		return
	end
	
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if not item_cfg then
		return
	end
	if enable then
		-- local color = self.data.effect_color or item_cfg.zhengui_effect
		local color = self:GetEffColor(item_cfg)
		local asset_name = ui_effect_t[color]
		local scale = 1
		if asset_name then
			self:SetEffectRootEnable(true)
			self:SetEffectEnable(true, asset_name, {0, 0}, self:Nodes("EffectRoot"), scale)
		end
	end
end

function DiamondItemCell:GetEffColor(item_cfg)
	local color = 1
	if item_cfg.sub_type == GameEnum.E_TYPE_SHENJI then
		if self.data.equip then
			color = self.data.equip.base_color
		else
			local equip = self:ParseShenJiEquip()
			color = equip and equip.base_color or item_cfg.color
		end

		if color > 3 then
			color = color + 5--紫色以上用zbbk
		end
	else
		color = self.data.effect_color or item_cfg.zhengui_effect
	end
	return color
end

function DiamondItemCell:CreateEffectChildObj(t)
	local item_key = t.name .. t.asset_bundle.asset
	self.item_effect[t.name].key = self.item_effect[t.name].key or {}
	if self.item_effect[t.name].key[item_key] then   -- 一个标识避免重复创建相同特效
		return
	end
	self.item_effect[t.name].key[item_key] = item_key

	local root_node = t.root or self.root_node
	local loader = AllocAsyncLoader(self, item_key)
	loader:SetParent(root_node.transform)
	loader:SetIsUseObjPool(true)
	loader:SetLoadPriority(ResLoadPriority.mid)
	loader:Load(t.asset_bundle.bundle, t.asset_bundle.asset, function(obj)
		if nil == obj or nil == root_node or IsNil(root_node.transform) or not self.item_effect[t.name] then
			return
		end

		obj.name = t.name
		obj.transform.localScale = t.scale and Vector3(t.scale, t.scale, t.scale)or Vector3(1, 1, 1)
		obj.transform.anchoredPosition3D = t.pos and Vector3(t.pos[1], t.pos[2], 0) or Vector3(0, 0, 0)
		self.item_effect[t.name].obj = obj
		obj:SetActive(self.item_effect[t.name].enable and not self.is_gray_icon)
		obj.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
		if t.call_back then
			t.call_back()
		end
	end)
end

function DiamondItemCell:SetEffectEnable(enable, asset_name, pos, root, scale)
	self.item_effect[asset_name] = self.item_effect[asset_name] or {}
	self.item_effect[asset_name].enable = enable

	if not IsNil(self.item_effect[asset_name].obj) then
		self.item_effect[asset_name].obj:SetActive(enable)
	else
		if enable then
			pos = pos or {0, 0}
			local bundle_name, asset_name = ResPath.GetWuPinKuangEffectUi(asset_name)
			self:CreateEffectChildObj({name = asset_name, pos = pos,
				asset_bundle = {bundle = bundle_name, asset = asset_name},
				root = root, scale = scale})
		end
	end
end

function DiamondItemCell:SetTipClickCallBack(callback)
	self.tip_callback = callback
end

-- 是否需要显示物品数量
function DiamondItemCell:NeedShowNum()
	return self.data.num and self.data.num > self.hide_right_down_cell_num
end

-- 右上角通用图标
function DiamondItemCell:SetTopRightIcon(top_right_icon_str)
	if not top_right_icon_str or top_right_icon_str == "" then
		self:TrySetActive("top_right_icon", nil, false)
		return
	end

	self:TrySetActive("top_right_icon", nil, true)
	local new_b, new_a = ResPath.GetLoadingPath(top_right_icon_str)
	self:Nodes("top_right_icon").image:LoadSpriteAsync(new_b, new_a, function ()
		self:Nodes("top_right_icon").image:SetNativeSize()
	end)
end


function DiamondItemCell:ParseShenJiEquip()
	local info = HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(self.data.item_id)
	return info
end

function DiamondItemCell:RoleRefiningInfo()
	local info = AlchemyWGData.Instance:GetPelletInfo(self.data.item_id)
	return info
end

function DiamondItemCell:SetShenJiSixStar()
	-- local equip = self:ParseShenJiEquip()
	-- if nil == self.data or (nil == self.data.equip and nil == equip) then
	-- 	self:SetLeftTopImg(0)
	-- 	return
	-- end

	-- local num = self.data.equip and self.data.equip.base_star or equip.base_star
	-- self:SetLeftTopImg(num)
	local equip = self:ParseShenJiEquip()
	if nil == self.data or (nil == self.data.equip and nil == equip) then
		self:TrySetActive("shenji_star", nil, false)
		return
	end

	local num = self.data.equip and self.data.equip.base_star or equip.base_star
	self:Nodes("shenji_star"):SetActive(num ~= 0)
	if num == 0 then
		return
	end


	if not self.shenji_star_ui then
		self.shenji_star_ui = {}
		for i = 1, 2 * GameEnum.SHENJI_MAX_RAND_NUM do
			table.insert(self.shenji_star_ui, self:Nodes("shenji_star", "Star_" .. i))
		end
	end

	for i,v in ipairs(self.shenji_star_ui) do
		v:SetActive(i <= num)
	end
end

function DiamondItemCell:SetRareFlag()
	local equip = self:ParseShenJiEquip()
	if nil == self.data or (nil == self.data.equip and nil == equip) then
		self:TrySetActive("img_rare", nil, false)
		return
	end

	local special_flag = self.data.equip and self.data.equip.special_flag or equip.special_flag
	self:Nodes("img_rare"):SetActive(special_flag ~= 0)
end

--丹药
function DiamondItemCell:SetRefiningRareFlag()
	local refining = self:RoleRefiningInfo()
	if nil == self.data or (nil == self.data.refining and nil == refining) then
		self:TrySetActive("img_rare", nil, false)
		return
	end

	local special_flag = self.data.refining and self.data.refining.is_rare or refining.is_rare
	self:Nodes("img_rare"):SetActive(special_flag ~= 0)
end

function DiamondItemCell:SetRightBottomImg(bundle, asset)
	if nil == bundle or nil == asset then
		self:TrySetActive("right_bottom_img", nil, false)
		return
	end

	self:TrySetActive("right_bottom_img", nil, true)
	self:Nodes("right_bottom_img").image:LoadSpriteAsync(bundle, asset, function()
        self:Nodes("right_bottom_img").image:SetNativeSize()
    end)
end

function DiamondItemCell:ShowNewYinJi(sub_type, is_show)
	local show_param = true
	if is_show ~= nil then
		show_param = is_show
	end

	--已装备的
	local equip_index = EquipWGData.Instance:GetEquipIndexByType(sub_type)
	local yinji_info = NewYinJiJiChengWGData.Instance:GetYinJiInfoByEquipPart(equip_index)

	if show_param == false then
		self:TrySetActive("new_yinji", nil, false)
		return
	end

	if self.data.zbyj_info then
		if self.data.zbyj_info.has_act_yinji_type > 0 then
			
			local title_str = Language.NewEquipYinJi.YinJiQuality[self.data.zbyj_info.has_act_yinji_type] or ""
			self:Nodes("new_yinji", "yinji_txt").text.text = title_str
			self:Nodes("new_yinji"):SetActive(true)
			local bundle, asset = ResPath.GetLoadingPath("a3_zbyj_tb" .. self.data.zbyj_info.has_act_yinji_type)
			self:Nodes("new_yinji").image:LoadSpriteAsync(bundle, asset, function ()
				self:Nodes("new_yinji").image:SetNativeSize()
			end)
		else
			self:TrySetActive("new_yinji", nil, false)
		end
	else
		if self.data.is_wear and yinji_info and yinji_info.has_act_yinji_type > 0 then
			self:Nodes("new_yinji"):SetActive(true)
			local bundle, asset = ResPath.GetLoadingPath("a3_zbyj_tb" .. yinji_info.has_act_yinji_type)
			self:Nodes("new_yinji").image:LoadSpriteAsync( bundle, asset, function ()
				self:Nodes("new_yinji").image:SetNativeSize()
			end)
			local title_str = Language.NewEquipYinJi.YinJiQuality[yinji_info.has_act_yinji_type] or ""
			self:Nodes("new_yinji", "yinji_txt").text.text = title_str
		else
			self:TrySetActive("new_yinji", nil, false)
		end
	end
end

function DiamondItemCell:SetXqTypeImg()
	local equip = self:ParseShenJiEquip()
	if nil == self.data or (nil == self.data.equip and nil == equip) then
		self:TrySetActive("img_xq_type", nil, false)
		return
	end
	local xq_icon_buule,xq_icon_name = ResPath.GetLoadingPath("a3_xqzi_icon_" .. equip.big_type .."_" ..  equip.node_type)
	self:TrySetActive("img_xq_type", nil, true)
	self:Nodes("img_xq_type").image:LoadSpriteAsync(xq_icon_buule, xq_icon_name)
end

function DiamondItemCell:ResetXqTypeImg()
	self:TrySetActive("img_xq_type", nil, false)
end

--设置礼包的系统名称显示
function DiamondItemCell:SetGiftSystemName(item_cfg)
	if not item_cfg or not item_cfg.id then
		self:TrySetActive("system_name_type_bg", nil, false)
		return
	end

	local gift_cfg = ItemWGData.Instance:GetItemConfig(item_cfg.id)
	if not gift_cfg or not gift_cfg.system_type_name then
		self:TrySetActive("system_name_type_bg", nil, false)
		return
	end

	local is_show_bg = gift_cfg.system_type_name ~= 0 and gift_cfg.system_type_name ~= ""
	self:Nodes("system_name_type_bg"):SetActive(is_show_bg)
	if is_show_bg then
		local width = 20--保朋要求特殊处理
		if type(gift_cfg.system_type_name) == "number" then
			width = 15
		end
		
		self:Nodes("system_name_type_bg", "txt_system_name").rect.sizeDelta = Vector2(width, self:Nodes("system_name_type_bg", "txt_system_name").rect.sizeDelta.y)
		self:Nodes("system_name_type_bg", "txt_system_name").text.text = gift_cfg.system_type_name
	end
end

function DiamondItemCell:IsShowSpecialTitleUI(is_show_special_title_ui)
	self.is_show_special_title_ui = is_show_special_title_ui
end

DiamondItemCell.cbdata_list = {}
function DiamondItemCell.GetCBData()
    local cbdata = table.remove(DiamondItemCell.cbdata_list)
    if nil == cbdata then
        cbdata = {true, true, true}
    end

    return cbdata
end

function DiamondItemCell.ReleaseCBData(cbdata)
    cbdata[1] = true
    cbdata[2] = true
    cbdata[3] = true
    table.insert(DiamondItemCell.cbdata_list, cbdata)
end

function DiamondItemCell:SetSubscriptFlag(text_str, biaoqian_flag)
	if not text_str or text_str == "" then
		self:TrySetActive("item_subscript_flag", nil, false)
		return
	end

	self:TrySetActive("item_subscript_flag", nil, true)
	self:Nodes("item_subscript_flag","subscript_text").text.text = text_str

	if biaoqian_flag then
		local new_b, new_a = ResPath.GetCommonImages("biaoqian_" .. biaoqian_flag)
		if new_b and new_a then
			self:Nodes("item_subscript_flag").image:LoadSpriteAsync(new_b, new_a)
		end
	end
end

-- 锻造御灵外框
function DiamondItemCell:ShowEquipYuLingIcon(item_cfg)
	local sub_type = item_cfg.sub_type or 0
	local yuling_data = self.data.yuling or {}

	if sub_type >= GameEnum.EQUIP_TYPE_TOUKUI and sub_type <= GameEnum.EQUIP_TYPE_XIANZHUO and self.data.frombody and not IsEmptyTable(yuling_data) then
		local can_show_yuling_icon = yuling_data.can_show_yuling_icon or false
		local equip_yuling_icon = yuling_data.equip_yuling_icon or 0

		-- 2024/1/18 策划lj为了背包界面菱形格子，丢掉御灵外框，实则已经使御灵系统失去意义
		if self.item_tip_from ~= ItemTip.FROM_BAG_EQUIP then
			if yuling_data.can_show_yuling_icon and equip_yuling_icon > 0 then
				local bundle, asset = ResPath.GetCommonImages("a2_ty_wpk_bk_" .. equip_yuling_icon)
				self:Nodes("yuling_icon").image:LoadSpriteAsync(bundle, asset, function()
					self:Nodes("yuling_icon").image:SetNativeSize()
				end)
				self:TrySetActive("yuling_icon", nil, true)
			else
				self:TrySetActive("yuling_icon", nil, false)
			end
		end
	end
end

-- 驭兽信息
function DiamondItemCell:FlushBeastItemIcon(data)
	local beast_data = ControlBeastsWGData.Instance:GetBeastCfgById(data.item_id)
	if beast_data then
		self:TrySetActive("beast_icon", nil, true)
		self.nodes["beast_icon"].node_list.beast_battle_icon:SetActive(false)

        local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(data.item_id)
        if beast_cfg then
			self:SetLeftTopImg(beast_cfg.beast_star)
			self:SetTopRightIcon(string.format("a3_ty_xpz%d", beast_cfg.beast_color))

			local new_b, new_a = ResPath.GetLoadingPath(string.format("a3_hs_bq_small_%d", beast_cfg.beast_element))
			self.nodes["beast_icon"].node_list.beast_element_icon.image:LoadSpriteAsync(new_b, new_a)
        end
	else
		self:TrySetActive("beast_icon", nil, false)
	end
end

-- 驭兽信息
function DiamondItemCell:SetBeastBattleTypeIcon(stand_by_slot)
	if stand_by_slot then
		if stand_by_slot == -1 then
			return
		end
		
		self:TrySetActive("beast_icon", nil, true)
		self.nodes["beast_icon"].node_list.beast_battle_icon:SetActive(true)

		local battle_type_str = "a3_ty_hs_2"
		if stand_by_slot > 2 then
			battle_type_str = "a3_ty_hs_3"
		end
		local bundle, asset = ResPath.GetLoadingPath(battle_type_str)
		self.nodes["beast_icon"].node_list.beast_battle_icon.image:LoadSprite(bundle, asset)
	else
		self:TrySetActive("beast_icon", nil, false)
	end
end

-- 驭兽信息
function DiamondItemCell:FlushBeastItemRedVisible(is_visible)
	if self.nodes["beast_icon"] and self.nodes["beast_icon"].node_list and self.nodes["beast_icon"].node_list.beast_red then
		self.nodes["beast_icon"].node_list.beast_red:SetActive(is_visible)
	end
end

-- 驭兽资质信息
function DiamondItemCell:SetBeastFlairScoreIcon(score_index)
end