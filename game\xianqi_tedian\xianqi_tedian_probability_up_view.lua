XianQiTeDianProUpView = XianQiTeDianProUpView or BaseClass(SafeBaseView)

function XianQiTeDianProUpView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(896, 540)})
    self:AddViewResource(0, "uis/view/sixiang_call_prefab", "xqzl_probability_view")
	self:SetMaskBg(true, true)
end

function XianQiTeDianProUpView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitTeDianUpList()
end

function XianQiTeDianProUpView:ReleaseCallBack()
	if self.xianqi_item_list then
		for k,v in pairs(self.xianqi_item_list) do
			v:DeleteMe()
		end
	end
	self.xianqi_item_list = nil
end

function XianQiTeDianProUpView:OnFlush(param_t)
	self:RefreshView()
end

function XianQiTeDianProUpView:InitParam()
	self.save_now_cycle = -1
end

function XianQiTeDianProUpView:InitPanel()
	self.node_list["title_view_name"].text.text = Language.SiXiangCall.XQZLViewName_ProbabilityUpView
	self.node_list["title_lbl_1"].text.text = Language.SiXiangCall.XQZLProbabilityUpTitle
end

function XianQiTeDianProUpView:RefreshView()
	self:InitTeDianUpList()
end

function XianQiTeDianProUpView:InitTeDianUpList()
	local cycle = XianQiTeDianWGData.Instance:GetCycle()
	if self.save_now_cycle ~= cycle then
		self.save_now_cycle = cycle
		self:InitXianQiItemList()
	end
end

function XianQiTeDianProUpView:InitXianQiItemList()
	local xianqi_cfg_list = XianQiTeDianWGData.Instance:GetProUpCfgList()
	local xianqi_item_list = {}
	local parent_obj = self.node_list.item_list_1
	for i = #xianqi_item_list + 1, #xianqi_cfg_list do
		xianqi_item_list[i] = XianQiTeDianProUpItem.New()
		xianqi_item_list[i]:DoLoad(parent_obj)
	end

	for i=1,#xianqi_item_list do
		xianqi_item_list[i]:SetData(xianqi_cfg_list[i])
	end

	parent_obj:SetActive(not IsEmptyTable(xianqi_cfg_list))

	self.xianqi_item_list = xianqi_item_list
end

----------------------------------------------------------------

XianQiTeDianProUpItem = XianQiTeDianProUpItem or BaseClass(XianQiZhenLianItem)

function XianQiTeDianProUpItem:DoLoad(parent)
	self:LoadAsset("uis/view/sixiang_call_prefab", "xqzl_proup_render", parent.transform)
end

function XianQiTeDianProUpItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)
	self:SetItemID(data.shenji_id)
	self:SetItemStar(data.base_star)
	self:FlushItemBg()
    self:FlushItemIcon()
    self:FlushItemStar()
    self:FlushItemName()
end