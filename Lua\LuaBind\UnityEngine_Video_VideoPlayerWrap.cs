﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Video_VideoPlayerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Video.VideoPlayer), typeof(UnityEngine.Behaviour));
		<PERSON><PERSON>RegFunction("Prepare", Prepare);
		<PERSON><PERSON>Function("Play", Play);
		<PERSON><PERSON>RegFunction("Pause", Pause);
		L<PERSON>RegFunction("Stop", Stop);
		<PERSON><PERSON>RegFunction("StepForward", StepForward);
		<PERSON><PERSON>RegFunction("GetAudioLanguageCode", GetAudioLanguageCode);
		<PERSON><PERSON>RegFunction("GetAudioChannelCount", GetAudioChannelCount);
		<PERSON><PERSON>Function("GetAudioSampleRate", GetAudioSampleRate);
		<PERSON>.RegFunction("EnableAudioTrack", EnableAudioTrack);
		<PERSON><PERSON>RegFunction("IsAudioTrackEnabled", IsAudioTrackEnabled);
		<PERSON><PERSON>RegFunction("GetDirectAudioVolume", GetDirectAudioVolume);
		<PERSON><PERSON>RegFunction("SetDirectAudioVolume", SetDirectAudioVolume);
		L.RegFunction("GetDirectAudioMute", GetDirectAudioMute);
		L.RegFunction("SetDirectAudioMute", SetDirectAudioMute);
		L.RegFunction("GetTargetAudioSource", GetTargetAudioSource);
		L.RegFunction("SetTargetAudioSource", SetTargetAudioSource);
		L.RegFunction("New", _CreateUnityEngine_Video_VideoPlayer);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("source", get_source, set_source);
		L.RegVar("url", get_url, set_url);
		L.RegVar("clip", get_clip, set_clip);
		L.RegVar("renderMode", get_renderMode, set_renderMode);
		L.RegVar("targetCamera", get_targetCamera, set_targetCamera);
		L.RegVar("targetTexture", get_targetTexture, set_targetTexture);
		L.RegVar("targetMaterialRenderer", get_targetMaterialRenderer, set_targetMaterialRenderer);
		L.RegVar("targetMaterialProperty", get_targetMaterialProperty, set_targetMaterialProperty);
		L.RegVar("aspectRatio", get_aspectRatio, set_aspectRatio);
		L.RegVar("targetCameraAlpha", get_targetCameraAlpha, set_targetCameraAlpha);
		L.RegVar("targetCamera3DLayout", get_targetCamera3DLayout, set_targetCamera3DLayout);
		L.RegVar("texture", get_texture, null);
		L.RegVar("isPrepared", get_isPrepared, null);
		L.RegVar("waitForFirstFrame", get_waitForFirstFrame, set_waitForFirstFrame);
		L.RegVar("playOnAwake", get_playOnAwake, set_playOnAwake);
		L.RegVar("isPlaying", get_isPlaying, null);
		L.RegVar("isPaused", get_isPaused, null);
		L.RegVar("canSetTime", get_canSetTime, null);
		L.RegVar("time", get_time, set_time);
		L.RegVar("frame", get_frame, set_frame);
		L.RegVar("clockTime", get_clockTime, null);
		L.RegVar("canStep", get_canStep, null);
		L.RegVar("canSetPlaybackSpeed", get_canSetPlaybackSpeed, null);
		L.RegVar("playbackSpeed", get_playbackSpeed, set_playbackSpeed);
		L.RegVar("isLooping", get_isLooping, set_isLooping);
		L.RegVar("canSetTimeSource", get_canSetTimeSource, null);
		L.RegVar("timeSource", get_timeSource, set_timeSource);
		L.RegVar("timeReference", get_timeReference, set_timeReference);
		L.RegVar("externalReferenceTime", get_externalReferenceTime, set_externalReferenceTime);
		L.RegVar("canSetSkipOnDrop", get_canSetSkipOnDrop, null);
		L.RegVar("skipOnDrop", get_skipOnDrop, set_skipOnDrop);
		L.RegVar("frameCount", get_frameCount, null);
		L.RegVar("frameRate", get_frameRate, null);
		L.RegVar("length", get_length, null);
		L.RegVar("width", get_width, null);
		L.RegVar("height", get_height, null);
		L.RegVar("pixelAspectRatioNumerator", get_pixelAspectRatioNumerator, null);
		L.RegVar("pixelAspectRatioDenominator", get_pixelAspectRatioDenominator, null);
		L.RegVar("audioTrackCount", get_audioTrackCount, null);
		L.RegVar("controlledAudioTrackMaxCount", get_controlledAudioTrackMaxCount, null);
		L.RegVar("controlledAudioTrackCount", get_controlledAudioTrackCount, set_controlledAudioTrackCount);
		L.RegVar("audioOutputMode", get_audioOutputMode, set_audioOutputMode);
		L.RegVar("canSetDirectAudioVolume", get_canSetDirectAudioVolume, null);
		L.RegVar("sendFrameReadyEvents", get_sendFrameReadyEvents, set_sendFrameReadyEvents);
		L.RegVar("prepareCompleted", get_prepareCompleted, set_prepareCompleted);
		L.RegVar("loopPointReached", get_loopPointReached, set_loopPointReached);
		L.RegVar("started", get_started, set_started);
		L.RegVar("frameDropped", get_frameDropped, set_frameDropped);
		L.RegVar("errorReceived", get_errorReceived, set_errorReceived);
		L.RegVar("seekCompleted", get_seekCompleted, set_seekCompleted);
		L.RegVar("clockResyncOccurred", get_clockResyncOccurred, set_clockResyncOccurred);
		L.RegVar("frameReady", get_frameReady, set_frameReady);
		L.RegFunction("FrameReadyEventHandler", UnityEngine_Video_VideoPlayer_FrameReadyEventHandler);
		L.RegFunction("TimeEventHandler", UnityEngine_Video_VideoPlayer_TimeEventHandler);
		L.RegFunction("EventHandler", UnityEngine_Video_VideoPlayer_EventHandler);
		L.RegFunction("ErrorEventHandler", UnityEngine_Video_VideoPlayer_ErrorEventHandler);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Video_VideoPlayer(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Video.VideoPlayer obj = new UnityEngine.Video.VideoPlayer();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Video.VideoPlayer.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Prepare(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			obj.Prepare();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Play(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			obj.Play();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Pause(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			obj.Pause();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			obj.Stop();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StepForward(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			obj.StepForward();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAudioLanguageCode(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			string o = obj.GetAudioLanguageCode(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAudioChannelCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			ushort o = obj.GetAudioChannelCount(arg0);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAudioSampleRate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			uint o = obj.GetAudioSampleRate(arg0);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int EnableAudioTrack(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.EnableAudioTrack(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsAudioTrackEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			bool o = obj.IsAudioTrackEnabled(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDirectAudioVolume(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			float o = obj.GetDirectAudioVolume(arg0);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDirectAudioVolume(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.SetDirectAudioVolume(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDirectAudioMute(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			bool o = obj.GetDirectAudioMute(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDirectAudioMute(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.SetDirectAudioMute(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTargetAudioSource(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.AudioSource o = obj.GetTargetAudioSource(arg0);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTargetAudioSource(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			UnityEngine.AudioSource arg1 = (UnityEngine.AudioSource)ToLua.CheckObject(L, 3, typeof(UnityEngine.AudioSource));
			obj.SetTargetAudioSource(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_source(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoSource ret = obj.source;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index source on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_url(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			string ret = obj.url;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index url on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_clip(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoClip ret = obj.clip;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index clip on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_renderMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoRenderMode ret = obj.renderMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_targetCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Camera ret = obj.targetCamera;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_targetTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.RenderTexture ret = obj.targetTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_targetMaterialRenderer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Renderer ret = obj.targetMaterialRenderer;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetMaterialRenderer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_targetMaterialProperty(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			string ret = obj.targetMaterialProperty;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetMaterialProperty on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_aspectRatio(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoAspectRatio ret = obj.aspectRatio;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index aspectRatio on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_targetCameraAlpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			float ret = obj.targetCameraAlpha;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetCameraAlpha on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_targetCamera3DLayout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.Video3DLayout ret = obj.targetCamera3DLayout;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetCamera3DLayout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_texture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Texture ret = obj.texture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index texture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isPrepared(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.isPrepared;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isPrepared on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_waitForFirstFrame(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.waitForFirstFrame;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index waitForFirstFrame on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_playOnAwake(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.playOnAwake;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playOnAwake on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isPlaying(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.isPlaying;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isPlaying on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isPaused(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.isPaused;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isPaused on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_canSetTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.canSetTime;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index canSetTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_time(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			double ret = obj.time;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index time on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_frame(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			long ret = obj.frame;
			LuaDLL.tolua_pushint64(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index frame on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_clockTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			double ret = obj.clockTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index clockTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_canStep(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.canStep;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index canStep on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_canSetPlaybackSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.canSetPlaybackSpeed;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index canSetPlaybackSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_playbackSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			float ret = obj.playbackSpeed;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playbackSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isLooping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.isLooping;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isLooping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_canSetTimeSource(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.canSetTimeSource;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index canSetTimeSource on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_timeSource(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoTimeSource ret = obj.timeSource;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeSource on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_timeReference(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoTimeReference ret = obj.timeReference;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeReference on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_externalReferenceTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			double ret = obj.externalReferenceTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index externalReferenceTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_canSetSkipOnDrop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.canSetSkipOnDrop;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index canSetSkipOnDrop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_skipOnDrop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.skipOnDrop;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index skipOnDrop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_frameCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			ulong ret = obj.frameCount;
			LuaDLL.tolua_pushuint64(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index frameCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_frameRate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			float ret = obj.frameRate;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index frameRate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_length(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			double ret = obj.length;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index length on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_width(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			uint ret = obj.width;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index width on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_height(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			uint ret = obj.height;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index height on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pixelAspectRatioNumerator(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			uint ret = obj.pixelAspectRatioNumerator;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pixelAspectRatioNumerator on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pixelAspectRatioDenominator(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			uint ret = obj.pixelAspectRatioDenominator;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pixelAspectRatioDenominator on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_audioTrackCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			ushort ret = obj.audioTrackCount;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index audioTrackCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_controlledAudioTrackMaxCount(IntPtr L)
	{
		try
		{
			LuaDLL.lua_pushnumber(L, UnityEngine.Video.VideoPlayer.controlledAudioTrackMaxCount);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_controlledAudioTrackCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			ushort ret = obj.controlledAudioTrackCount;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index controlledAudioTrackCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_audioOutputMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoAudioOutputMode ret = obj.audioOutputMode;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index audioOutputMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_canSetDirectAudioVolume(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.canSetDirectAudioVolume;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index canSetDirectAudioVolume on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_sendFrameReadyEvents(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool ret = obj.sendFrameReadyEvents;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sendFrameReadyEvents on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_prepareCompleted(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(UnityEngine.Video.VideoPlayer.EventHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_loopPointReached(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(UnityEngine.Video.VideoPlayer.EventHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_started(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(UnityEngine.Video.VideoPlayer.EventHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_frameDropped(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(UnityEngine.Video.VideoPlayer.EventHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_errorReceived(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(UnityEngine.Video.VideoPlayer.ErrorEventHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_seekCompleted(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(UnityEngine.Video.VideoPlayer.EventHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_clockResyncOccurred(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(UnityEngine.Video.VideoPlayer.TimeEventHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_frameReady(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(UnityEngine.Video.VideoPlayer.FrameReadyEventHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_source(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoSource arg0 = (UnityEngine.Video.VideoSource)ToLua.CheckObject(L, 2, typeof(UnityEngine.Video.VideoSource));
			obj.source = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index source on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_url(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.url = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index url on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_clip(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoClip arg0 = (UnityEngine.Video.VideoClip)ToLua.CheckObject(L, 2, typeof(UnityEngine.Video.VideoClip));
			obj.clip = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index clip on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_renderMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoRenderMode arg0 = (UnityEngine.Video.VideoRenderMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Video.VideoRenderMode));
			obj.renderMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index renderMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_targetCamera(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			obj.targetCamera = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetCamera on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_targetTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.RenderTexture arg0 = (UnityEngine.RenderTexture)ToLua.CheckObject<UnityEngine.RenderTexture>(L, 2);
			obj.targetTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_targetMaterialRenderer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Renderer arg0 = (UnityEngine.Renderer)ToLua.CheckObject<UnityEngine.Renderer>(L, 2);
			obj.targetMaterialRenderer = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetMaterialRenderer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_targetMaterialProperty(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.targetMaterialProperty = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetMaterialProperty on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_aspectRatio(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoAspectRatio arg0 = (UnityEngine.Video.VideoAspectRatio)ToLua.CheckObject(L, 2, typeof(UnityEngine.Video.VideoAspectRatio));
			obj.aspectRatio = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index aspectRatio on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_targetCameraAlpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.targetCameraAlpha = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetCameraAlpha on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_targetCamera3DLayout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.Video3DLayout arg0 = (UnityEngine.Video.Video3DLayout)ToLua.CheckObject(L, 2, typeof(UnityEngine.Video.Video3DLayout));
			obj.targetCamera3DLayout = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index targetCamera3DLayout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_waitForFirstFrame(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.waitForFirstFrame = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index waitForFirstFrame on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_playOnAwake(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.playOnAwake = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playOnAwake on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_time(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			double arg0 = (double)LuaDLL.luaL_checknumber(L, 2);
			obj.time = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index time on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_frame(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			long arg0 = LuaDLL.tolua_checkint64(L, 2);
			obj.frame = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index frame on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_playbackSpeed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.playbackSpeed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index playbackSpeed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isLooping(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isLooping = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isLooping on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_timeSource(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoTimeSource arg0 = (UnityEngine.Video.VideoTimeSource)ToLua.CheckObject(L, 2, typeof(UnityEngine.Video.VideoTimeSource));
			obj.timeSource = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeSource on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_timeReference(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoTimeReference arg0 = (UnityEngine.Video.VideoTimeReference)ToLua.CheckObject(L, 2, typeof(UnityEngine.Video.VideoTimeReference));
			obj.timeReference = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index timeReference on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_externalReferenceTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			double arg0 = (double)LuaDLL.luaL_checknumber(L, 2);
			obj.externalReferenceTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index externalReferenceTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_skipOnDrop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.skipOnDrop = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index skipOnDrop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_controlledAudioTrackCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			ushort arg0 = (ushort)LuaDLL.luaL_checknumber(L, 2);
			obj.controlledAudioTrackCount = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index controlledAudioTrackCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_audioOutputMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			UnityEngine.Video.VideoAudioOutputMode arg0 = (UnityEngine.Video.VideoAudioOutputMode)ToLua.CheckObject(L, 2, typeof(UnityEngine.Video.VideoAudioOutputMode));
			obj.audioOutputMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index audioOutputMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_sendFrameReadyEvents(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.sendFrameReadyEvents = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index sendFrameReadyEvents on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_prepareCompleted(IntPtr L)
	{
		try
		{
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Video.VideoPlayer.prepareCompleted' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Video.VideoPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.prepareCompleted += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.prepareCompleted -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_loopPointReached(IntPtr L)
	{
		try
		{
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Video.VideoPlayer.loopPointReached' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Video.VideoPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.loopPointReached += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.loopPointReached -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_started(IntPtr L)
	{
		try
		{
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Video.VideoPlayer.started' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Video.VideoPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.started += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.started -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_frameDropped(IntPtr L)
	{
		try
		{
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Video.VideoPlayer.frameDropped' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Video.VideoPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.frameDropped += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.frameDropped -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_errorReceived(IntPtr L)
	{
		try
		{
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Video.VideoPlayer.errorReceived' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Video.VideoPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				UnityEngine.Video.VideoPlayer.ErrorEventHandler ev = (UnityEngine.Video.VideoPlayer.ErrorEventHandler)arg0.func;
				obj.errorReceived += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				UnityEngine.Video.VideoPlayer.ErrorEventHandler ev = (UnityEngine.Video.VideoPlayer.ErrorEventHandler)arg0.func;
				obj.errorReceived -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_seekCompleted(IntPtr L)
	{
		try
		{
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Video.VideoPlayer.seekCompleted' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Video.VideoPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.seekCompleted += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				UnityEngine.Video.VideoPlayer.EventHandler ev = (UnityEngine.Video.VideoPlayer.EventHandler)arg0.func;
				obj.seekCompleted -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_clockResyncOccurred(IntPtr L)
	{
		try
		{
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Video.VideoPlayer.clockResyncOccurred' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Video.VideoPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				UnityEngine.Video.VideoPlayer.TimeEventHandler ev = (UnityEngine.Video.VideoPlayer.TimeEventHandler)arg0.func;
				obj.clockResyncOccurred += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				UnityEngine.Video.VideoPlayer.TimeEventHandler ev = (UnityEngine.Video.VideoPlayer.TimeEventHandler)arg0.func;
				obj.clockResyncOccurred -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_frameReady(IntPtr L)
	{
		try
		{
			UnityEngine.Video.VideoPlayer obj = (UnityEngine.Video.VideoPlayer)ToLua.CheckObject(L, 1, typeof(UnityEngine.Video.VideoPlayer));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'UnityEngine.Video.VideoPlayer.frameReady' can only appear on the left hand side of += or -= when used outside of the type 'UnityEngine.Video.VideoPlayer'");
			}

			if (arg0.op == EventOp.Add)
			{
				UnityEngine.Video.VideoPlayer.FrameReadyEventHandler ev = (UnityEngine.Video.VideoPlayer.FrameReadyEventHandler)arg0.func;
				obj.frameReady += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				UnityEngine.Video.VideoPlayer.FrameReadyEventHandler ev = (UnityEngine.Video.VideoPlayer.FrameReadyEventHandler)arg0.func;
				obj.frameReady -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnityEngine_Video_VideoPlayer_FrameReadyEventHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<UnityEngine.Video.VideoPlayer.FrameReadyEventHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<UnityEngine.Video.VideoPlayer.FrameReadyEventHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnityEngine_Video_VideoPlayer_TimeEventHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<UnityEngine.Video.VideoPlayer.TimeEventHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<UnityEngine.Video.VideoPlayer.TimeEventHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnityEngine_Video_VideoPlayer_EventHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<UnityEngine.Video.VideoPlayer.EventHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<UnityEngine.Video.VideoPlayer.EventHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnityEngine_Video_VideoPlayer_ErrorEventHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<UnityEngine.Video.VideoPlayer.ErrorEventHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<UnityEngine.Video.VideoPlayer.ErrorEventHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

