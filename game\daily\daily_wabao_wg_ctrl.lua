require("game/daily/daily_wabao_wg_data")

-- 挖宝
DailyWGCtrl = DailyWGCtrl or BaseClass(BaseWGCtrl)

function DailyWGCtrl:WabaoInit()
	self.wb_data = DailyWaBaoWGData.New()
	self:RegisterWabaoProtocals()
end

function DailyWGCtrl:DeleteWaBao()
	self.wb_data:DeleteMe()
	self.wb_data = nil

	if nil ~= self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end

	DailyWGCtrl.Instance = nil
end

-- function DailyWGCtrl:Open(param_t)
-- 	self:SendWabaoOperaReq(WABAO_OPERA_TYPE.INFO)
-- 	self.view:Open()
-- end

-- function DailyWGCtrl:Close()
-- 	self.view:Close()
-- end

function DailyWGCtrl:RegisterWabaoProtocals()
	-- self:RegisterProtocol(SCWabaoInfo, "OnWabaoInfo") 				--
	-- self:RegisterProtocol(CSWabaoOperaReq) 			--
end

function DailyWGCtrl:OnWabaoInfo(protocol)
	local wabaovo = self.wb_data:GetWaBaoVo()
	wabaovo.complete_times = protocol.complete_times
	wabaovo.buy_join_times = protocol.buy_join_times
	wabaovo.reserve_sh = protocol.reserve_sh
	wabaovo.baozang_scene_id = protocol.baozang_scene_id
	wabaovo.baozang_pos_x = protocol.baozang_pos_x
	wabaovo.baozang_pos_y = protocol.baozang_pos_y
	self.wb_data:UpdateDailyWaBaoData()

	if wabaovo.status == WABAO_STATUS.DIG then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WABAO, 1, BindTool.Bind1(function ()
			-- self:Open(DailyViewIndex.Catch)
		end, self))
	else
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.WABAO, 0)
	end
end

function DailyWGCtrl:SendWabaoOperaReq(opera_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWabaoOperaReq)
	protocol.opera_type = opera_type
	protocol:EncodeAndSend()
end

function DailyWGCtrl:FlyToPos(x, y, scene_id)
	self:UnBindMoveEvent()
	self.move_event = self:BindGlobalEvent(ObjectEventType.MAIN_ROLE_MOVE_END, BindTool.Bind1(self.OnMainRoleMoveEnd, self))
	self.end_pos = {x = x, y = y}
	Scene.Instance:ClearAllOperate()
	Scene.Instance:GetSceneLogic():FlyToPos(x, y, scene_id, nil, false)
end

function DailyWGCtrl:OnMainRoleMoveEnd()
	local wabaovo = self.wb_data:GetWaBaoVo()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	if wabaovo.baozang_pos_x == x and wabaovo.baozang_pos_y == y and
		Scene.Instance:GetSceneId() == wabaovo.baozang_scene_id then
		self:UnBindMoveEvent()
		self:OpenDigView()
	end
end

function DailyWGCtrl:UnBindMoveEvent()
	if nil ~= self.move_event then
		GlobalEventSystem:UnBind(self.move_event)
		self.move_event = nil
	end
end

function DailyWGCtrl:OpenDigView()
	self.pop_alert = self.pop_alert or Alert.New()
	self.pop_alert:SetLableString(Language.WaBao.BeginRob)
	self.pop_alert:SetIaAnyClickClose(false)
	self.pop_alert:SetOkString(Language.Common.Confirm)
	self.pop_alert:SetCancelString(Language.Common.Cancel)
	self.pop_alert:Open()
	self.pop_alert:SetShowCheckBox(false)
	self.pop_alert:SetOkFunc(BindTool.Bind1(function ()
		self:SendWabaoOperaReq(WABAO_OPERA_TYPE.DIG)
		self:Close()
	end, self))
end
