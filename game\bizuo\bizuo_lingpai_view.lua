BiZuoLPView = BiZuoLPView or BaseClass(SafeBaseView)

function BiZuoLPView:__init()
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:AddViewResource(0, "uis/view/bizuo_ui_prefab", "layout_daily_lingpai")
	self.show_res_id = nil
	self.effect_obj = nil
end

function BiZuoLPView:__delete()
	
end

function BiZuoLPView:ReleaseCallBack()
	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end
	if self.effect_obj then
		ResPoolMgr:Release(self.effect_obj)
		self.effect_obj = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
	
	self.show_res_id = nil
	--self.jingmai_image = {}
	if self.xianqi_model then
		self.xianqi_model:DeleteMe()
		self.xianqi_model = nil
	end
end

function BiZuoLPView:LoadCallBack()
	self.node_list["btn_up"].button:AddClickListener(BindTool.Bind1(self.OnClickLevelUp, self))

	if not self.arrow_tweener then
		local tween_root = self.node_list["arrow_list"].rect
		self.arrow_tweener = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 5, 0.8)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
	-- self.jingmai_image = {}
	-- for i = 1, 9 do
	-- 	self.jingmai_image[i] = self.node_list["jingmai_ball_" .. i]
	-- end
	local model_id,model_scale =  BiZuoWGData.Instance:GetModelId()
	self.node_list.xianqi_model.transform.localScale = Vector3(model_scale,model_scale,model_scale)
	if nil == self.xianqi_model then
		self.xianqi_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["xianqi_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.xianqi_model:SetRenderTexUI3DModel(display_data)
	end
	if self.xianqi_model then
		self.xianqi_model:SetMainAsset(ResPath.GetFaBaoModel(model_id))
	end
end

function BiZuoLPView:ShowIndexCallBack()
	self:FlushLeftView()
end

function BiZuoLPView:FlushLeftView()
	local self_type = 0
	local bizuo_data = BiZuoWGData.Instance
	local bizuo_info = bizuo_data:GetAllInfo()
	local level_info = bizuo_data:GetLevelCfgByLevel(bizuo_info.level or 0)
	-- 	if bizuo_info and bizuo_info.level then
	-- 	local image_id = bizuo_data:GetBiZuoNameByLevel(bizuo_info.level)
	-- 	print_error("image_id", image_id)
	-- 	if image_id then
	-- 		self_type = image_id
	-- 	end
	-- end
	if nil == next(level_info) then return end
	--self:PlayLingPaiEffect(level_info.effect_id)
	local next_level_info = bizuo_data:GetLevelCfgByLevel((bizuo_info.level or 0) + 1)
	local add_attribute = AttributeMgr.LerpAttributeAttr(AttributeMgr.GetAttributteByClass(level_info), AttributeMgr.GetAttributteByClass(next_level_info))
	-- 刷新进度条
	local cap = math.min(bizuo_info.exp, level_info.need_exp)
	local percent = (cap / level_info.need_exp)
	self.node_list["prog9_process"].slider.value = percent
	-- 刷新等级
	local max_level = bizuo_data:GetMaxLevel()
	local text = ""
	if bizuo_info.level >= max_level then
		text = Language.Advanced.SkillManJi
		self.node_list["lbl_now_level"].text.text = ("LV." .. bizuo_info.level)
		self.node_list["btn_up_text"].text.text = text --zzz
		XUI.SetButtonEnabled(self.node_list["btn_up"], false)
		self.node_list["layout_bizuo_attri_text"].rect.anchoredPosition = Vector2(236,-21)
	else
		text = string.format(Language.Exchange.Expend, bizuo_info.exp, level_info.need_exp)
		self.node_list["lbl_now_level"].text.text = ("LV." .. bizuo_info.level)
	end
	self.node_list["lbl_proess"].text.text = text
	-- if bizuo_data:GetBiZuoNameByName(bizuo_info.level) then
	-- 	self.node_list["lbl_mount_name"].text.text = bizuo_data:GetBiZuoNameByName(bizuo_info.level)
	-- end

	--刷新属性数据

	AttributeMgr.FlushAttrView(self.node_list["layout_bizuo_attri_text"], AttributeMgr.GetAttributteByClass(level_info))
	AttributeMgr.FlushNextAttrView(self.node_list["layout_bizuo_attri_text"], add_attribute, nil)

	self:GetATK(level_info)
	self.node_list.Img_up_icon:SetActive(bizuo_data:CanLevelUpRemind() ~= 0)

	--self:FlushJingMaiBall()
end

-- function BiZuoLPView:FlushJingMaiBall()
-- 	local bizuo_info = BiZuoWGData.Instance:GetAllInfo()
-- 	local cur_lev = bizuo_info.level
-- 	for i = 1, 9 do
-- 		local bundle, asset = ResPath.GetDailyTypeIcon("jingmai_zhuzi_hui")
-- 		local modf, _ = math.modf(cur_lev / 9) --除后整数，得到当前总轮数
-- 		local modf_mod = math.fmod(modf, 10) --取模，得到当前第几轮
-- 		local mod = math.fmod(cur_lev, 9) --取模，得到当前升级到第几颗珠

-- 		if modf >= 10 and modf_mod == 0 then
-- 			if mod == 0 then
-- 				asset = "jingmai_10"
-- 			elseif mod >= i then
-- 				asset = "jingmai_1"
-- 			end
-- 		else
-- 			modf = modf > 10 and modf_mod or modf
-- 			if modf == 0 then
-- 				if mod >= i then
-- 					asset = "jingmai_1"
-- 				end
-- 			else
-- 				if mod >= i then
-- 					modf = modf + 1
-- 					asset = "jingmai_" .. modf
-- 				elseif mod == 0 then
-- 					asset = "jingmai_" .. modf
-- 				end
-- 			end
-- 		end
-- 		self.jingmai_image[i].image:LoadSprite(bundle, asset)
-- 	end
-- end

function BiZuoLPView:OnClickLevelUp()
	BiZuoWGCtrl.Instance:SendBiZuoOperate(DAILY_WORK_OPERA_REQ_TYPE.DW_OPERA_REQ_TYPE_UPLEVEL)
end

function BiZuoLPView:GetATK(level_info)
	local list = AttributeMgr.GetAttributteByClass(level_info)
	local power = AttributeMgr.GetCapability(list)
	-- local power = tonumber(self.node_list.lbl_gongji_val.text.text)*10 + tonumber(self.node_list.lbl_pojia_val.text.text)*10 + tonumber(self.node_list.lbl_fangyu_val.text.text)*10 + tonumber(self.node_list.lbl_hp_val.text.text)*0.5
	self.node_list.power_num.text.text = power
end
function BiZuoLPView:OnFlush()

end

function BiZuoLPView:PlayEffect()
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0),
		parent_node = self.node_list["effect_obj"]})
end

function BiZuoLPView:PlayLingPaiEffect(effect_res_id)
	if effect_res_id == nil or self.show_res_id == effect_res_id then
		return
	end

	--local bundle_name, asset_name = ResPath.GetEffectUi(effect_res_id)
	-- self.node_list.effect:ChangeAsset(bundle_name, asset_name, false)
	-- self.node_list.effect.transform.localScale = Vector3(0.6,0.6,0.6)
end
