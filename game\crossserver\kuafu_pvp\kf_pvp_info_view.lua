KfPVPInfo = KfPVPInfo or BaseClass(SafeBaseView)
KfPVPInfo.Width = 272
KfPVPInfo.Height = 370

function KfPVPInfo:__init()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_pvp_info")
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_pvp_info_top")
	--self.view_layer = UiLayer.Pop
	self.view_layer = UiLayer.MainUILow
	self.order = 1
	self.is_click = false
end

function KfPVPInfo:__delete()

end

function KfPVPInfo:ReleaseCallBack()
	if self.progressbar_list then
		for k,v in pairs(self.progressbar_list) do
			v:DeleteMe()
		end
		self.progressbar_list = {}
	end
	self.progressbg_list = {"","",""}
	if CountDownManager.Instance:HasCountDown("kf3v3_open_timer") then
		CountDownManager.Instance:RemoveCountDown("kf3v3_open_timer")
	end
end

--按钮点击
function KfPVPInfo:OnClickLeft()
	self.is_click = not self.is_click
	if self.is_click then
		self.action_timer = GlobalTimerQuest:AddDelayTimer(function()
			self.action_timer = nil
			self.btn_right:setVisible(true)
			self.mt_layout_follow:setVisible(false)
		end, 0.3)
		self.mt_layout_follow:MoveTo(0.3, -KfPVPInfo.Width, self.mt_layout_follow:getPositionY())
	else
		self.btn_right:setVisible(false)
		self.mt_layout_follow:setVisible(true)
		self.mt_layout_follow:MoveTo(0.3, 0, self.mt_layout_follow:getPositionY())
	end
end

function KfPVPInfo:LoadCallBack()
	local prepare_info = KuafuPVPWGData.Instance:GetPrepareInfo()
	local mul_time = prepare_info.next_state_time - TimeWGCtrl.Instance:GetServerTime()
	if CountDownManager.Instance:HasCountDown("kf3v3_open_timer") then
		CountDownManager.Instance:RemoveCountDown("kf3v3_open_timer")
	end
	if mul_time > 0 then
		self:UpdateOpenCountDownTime(1, mul_time)
		CountDownManager.Instance:AddCountDown("kf3v3_open_timer", BindTool.Bind1(self.UpdateOpenCountDownTime, self), nil, prepare_info.next_state_time , nil, 1)
	else
		self.node_list.lbl_time.text.text = string.format(Language.KuafuPVP.ShnegyuTime,TimeUtil.FormatSecond2MS(0))
	end
	XUI.AddClickEventListener(self.node_list.btn_left,BindTool.Bind1(self.OnClickLeft, self)) --收起-展开
	XUI.AddClickEventListener(self.node_list.btn_play_guild_info,BindTool.Bind(self.PlayDescInfo, self))

	self.progressbar_list = {}

	XUI.AddClickEventListener(self.node_list.item_zhanchang_flag, BindTool.Bind(self.OnClickStrongholdFlagPos, self))
	self.start_gather_event = BindTool.Bind1(self.StartGather, self) --xxxx
	GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.TopPanelAni, self))
	local cfg = KuafuPVPWGData.Instance:GetKaFuTvTCfg()
	local kfpvp_other_cfg = cfg.other[1]
	self.node_list.victery_score.text.text = string.format(Language.KuafuPVP.KfPvPSceneGradeScore,kfpvp_other_cfg.finish_match_score)
	local flag_pos = ConfigManager.Instance:GetAutoConfig("kuafu_tvt_auto").flag_pos[1]
	if flag_pos.flag_name ~= nil then
		self.node_list.lbl_name.text.text = flag_pos.flag_name
	end
	self:GoToPosCallBack()
end

function KfPVPInfo:TopPanelAni(ison)
	local max_move ,min_move = 40 , -36

	--self.node_list.root_layout_inspire:SetActive(not is_on)
	local move_vaule = ison == true and max_move or min_move
	if nil == self.node_list.topbg then return end
	local tween = self.node_list.topbg.rect:DOAnchorPosY(move_vaule, 0.3)
	tween:SetEase(DG.Tweening.Ease.Linear)
	--tween:SetDelay(delay_time)
	tween:OnUpdate(function()
		self.node_list.topbg.canvas_group.alpha = ((max_move - min_move) - (self.node_list.topbg.rect.anchoredPosition.y - min_move)) / (max_move - min_move)
	end)
end

function KfPVPInfo:OpenCallBack()

end

function KfPVPInfo:PlayDescInfo()
	RuleTip.Instance:SetContent(Language.KuafuPVP.KfPvPAfterTips, Language.KuafuPVP.KfPvPTips)
end

function KfPVPInfo:UpdateOpenCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		local time = TimeUtil.FormatSecond2MS(total_time - elapse_time)
		self.node_list.lbl_time.text.text = string.format(Language.KuafuPVP.ShnegyuTime,time)
	end

end

function KfPVPInfo:CloseCallBack()
	self.is_click = false
end

function KfPVPInfo:SetProgressUpVal(val)
	self.node_list.lbl_red_progress.text.text = val
	-- self.node_list.lbl_red_progress.text.text = val .. "/600"
	-- self.node_list.prog_red_progress.slider.value = (val / 600)
end

function KfPVPInfo:SetProgressDownVal(val)
	self.node_list.lbl_blue_progress.text.text = val
	-- self.node_list.lbl_blue_progress.text.text = val .. "/600"
	-- self.node_list.prog_blue_progress.slider.value = (val / 600)
end

function KfPVPInfo:SetProgressFlagVal(info)
	if info.flag_id then
		--self.node_list.lbl_flag_progress.text.text = info.flag_curhp .."/".. info.flag_maxhp
		self.node_list.img_item_bg.image.fillAmount = (info.flag_curhp / info.flag_maxhp)
		--self.node_list.prog_flag_progress.slider.value = (info.flag_curhp / info.flag_maxhp)
		if info.flag_side == -1 then
			self.node_list.rich_battle_occupy.text.text = Language.KuafuPVP.NoOccupySide
		else
			self.node_list.rich_battle_occupy.text.text = info.flag_side == 1 and Language.KuafuPVP.BlueSideOccupy or Language.KuafuPVP.RedSideOccupy
			self.node_list.img_icon.image:LoadSprite(ResPath.GetKf1V1(info.flag_side == 1 and "liujie_blue_qizi" or "red_qizi"))
			self.node_list.img_item_bg.image:LoadSprite(ResPath.GetKf1V1(info.flag_side == 1 and "liujie_iten_bg_blue_blue" or "liujie_iten_bg_red"))
		end
	else
		print_error(info)
	end
end

function KfPVPInfo:OnFlush(param_t)
	if not self.progressbg_list then
		self.progressbg_list = {}
	end
	local stronghold_info = KuafuPVPWGData.Instance:GetStrongHoldInfo()
	-- for  i = 1, 3 do
	-- 	local sh = stronghold_info.stronghold_list[i] or {}

	-- 	local sh_img = "kf_jing_0"
	-- 	local camp = nil
	-- 	if sh.owner_side == KuafuPVPWGData.KF3V3_SIDE.SIDE_0 then
	-- 		sh_img = "kf_jing_1"
	-- 		camp = "camp_0"
	-- 	elseif sh.owner_side == KuafuPVPWGData.KF3V3_SIDE.SIDE_1 then
	-- 		sh_img = "kf_jing_2"
	-- 		camp = "camp_1"
	-- 	end

	-- 	self.node_list["Image_" .. i]:SetActive(camp ~= nil)
	-- 	self:ChangeItemProgressBg(i,sh_img)
	-- 	if camp then
	-- 		self.node_list["Image_" .. i].image:LoadSprite(ResPath.GetKf3V3Old(camp))
	-- 	end

	-- 	--进度条
	-- 	if sh then
	-- 		local cur_gather_side = sh.cur_gather_side
	-- 		local cur_gather_end_time = sh.cur_gather_end_time
	-- 		local remain_time = cur_gather_end_time - TimeWGCtrl.Instance:GetServerTime()
	-- 		local path = ""
	-- 		if cur_gather_side == KuafuPVPWGData.KF3V3_SIDE.SIDE_0 then
	-- 			path = "kf_jing_1"
	-- 		elseif cur_gather_side == KuafuPVPWGData.KF3V3_SIDE.SIDE_1 then
	-- 			path = "kf_jing_2"
	-- 		end

	-- 		if -1 ~= cur_gather_side and remain_time > 1 then
	-- 			if nil == self.progressbar_list[i] then
	-- 				self.progressbar_list[i] = KfPVPProgressBar.New()
	-- 				self.progressbar_list[i]:SetSelfView(self.node_list["img_ml_item_hight_bg" .. i])
	-- 				self.progressbar_list[i]:SetMaxValueChangeCallBack(BindTool.Bind(self.CompleteLoading, self, i))
	-- 			end

	-- 			if 0 == self.progressbar_list[i]:GetCurPercent() then
	-- 				self:ChangeItemProgressFillRectImage(i,path)
	-- 			end

	-- 			if 0 == self.progressbar_list[i]:GetCurPercent() then
	-- 				self.progressbar_list[i]:SetTotalTime(remain_time)
	-- 				self.progressbar_list[i]:SetPercent(0, false)
	-- 				self.progressbar_list[i]:SetPercent(100, true)
	-- 			end
	-- 		else
	-- 			if nil ~= self.progressbar_list[i] then
	-- 				self.progressbar_list[i]:SetPercent(0, false)
	-- 			end
	-- 		end
	-- 	end
	-- end

	local side1 = stronghold_info.side_score_list[1] or 0
	local side2 = stronghold_info.side_score_list[2] or 0
	local flag_info = stronghold_info.flag_info or {}
	self:SetProgressUpVal(side1)
	self:SetProgressDownVal(side2)
	self:SetProgressFlagVal(flag_info)
end

function KfPVPInfo:CompleteLoading(i)
	if nil ~= self.progressbar_list[i] then
		-- print_error("CompleteLoading",i)
		self:ChangeItemProgressBg(i,"kf_jing_0")
		-- self.progressbar_list[i]:SetPercent(0, false)
	end
end

function KfPVPInfo:StartGather()

end

function KfPVPInfo:OnClickStrongholdFlagPos()
	local show_cfg = KuafuPVPWGData.Instance:GetKaFuTvTCfg().flag_pos[1]
	local scene_id = Scene.Instance:GetSceneId()
	local pos_x = show_cfg.pos_x
	local pos_y = show_cfg.pos_y
	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
   		self:GoToPosCallBack()
   	end)
	GuajiWGCtrl.Instance:MoveToPos(scene_id,pos_x, pos_y)
end

function KfPVPInfo:GoToPosCallBack()
	-- local flag_info = KuafuPVPWGData.Instance:GetStrongHoldInfo()
	-- print_error("挂机回调，999999",flag_info)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

--进度条背景图片
function KfPVPInfo:ChangeItemProgressBg(index,sh_img_name)
	if sh_img_name and sh_img_name ~= "" then
		self.node_list["img_ml_item_hight_bg" .. index].image:LoadSprite(ResPath.GetKf3V3Old(sh_img_name))
	else
		self.node_list["img_ml_item_hight_bg" .. index].image:LoadSprite(ResPath.GetKf3V3Old(self.progressbg_list[index]))
	end
end

--进度条填充图片
function KfPVPInfo:ChangeItemProgressFillRectImage(index,img_name)
	self.node_list["img_ml_item_bg" .. index].image:LoadSprite(ResPath.GetKf3V3Old(img_name))
end

--判断敌我双方
-- local role_info = KuafuPVPWGData.Instance:GetRoleInfo()
-- local self_side = role_info.self_side
-- local red_sth = self_side == KuafuPVPWGData.KF3V3_SIDE.SIDE_0 and Language.KuafuPVP.SideToRole[1] or Language.KuafuPVP.SideToRole[2]
-- local blue_sth = self_side == KuafuPVPWGData.KF3V3_SIDE.SIDE_0 and Language.KuafuPVP.SideToRole[2] or Language.KuafuPVP.SideToRole[1]