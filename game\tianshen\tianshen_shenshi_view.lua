local TOGGLE_MAX = 5
local SHENSHI_BTN_TYPE = {
	UPGRADE = 1,
	SUCCINCT = 2,
}

function TianShenView:ShenShiInit()
	self.shenshi_ts_select_index = -1
end

function TianShenView:ShenShiLoadCallBack()
	TianShenWGData.Instance:ReCacularShenShiPartCap()
	self.shenshi_ts_select_index = -1
	self.ts_dh_cell_list = {}
	self.can_up_grade = false
	self.dh_is_need_jump = nil
	self.force_flush_tsdh_list = false
	
	if not self.shenshi_role_model then
		self.shenshi_role_model = RoleModel.New()
		self.shenshi_role_model:SetUISceneModel(self.node_list["ph_shenshi_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.shenshi_role_model, TabIndex.tianshen_shenshi)
	end

	self.dh_render_list = {}
	for i = 1, 4 do
		local cell = TianShenEquipCell.New(self.node_list["dh_cell_list"]:FindObj("ts_dh_item_" .. i))
		cell:SetIndex(i)
		self.dh_render_list[i] = cell
	end

	if not self.dh_skill_list then
        self.dh_skill_list = {}
        local num = self.node_list.dh_zhu_skill.transform.childCount
        for i = 1, num do
            local cell = TianShenSkillItem.New(self.node_list.dh_zhu_skill:FindObj("info_skill_" .. i))
            cell:SetIndex(i)
            self.dh_skill_list[i] = cell
        end
    end

	-- 灵韵
	if not self.succinct_slider_list_view then
		self.succinct_slider_list_view = AsyncListView.New(SuccinctSliderRender, self.node_list.succinct_slider_list_view)
		self.succinct_slider_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectSuccinctSliderCB, self))
		self.succinct_slider_list_view:SetLimitSelectFunc(BindTool.Bind1(self.OnSelectSuccinctLimit, self))
	end

	if not self.succinct_spend_item_cell then
		self.succinct_spend_item_cell = ItemCell.New(self.node_list.succinct_spend_item_cell)
	end

	if not self.succinct_spend_money then
		self.succinct_spend_money = ItemCell.New(self.node_list.succinct_spend_money)
	end

	XUI.AddClickEventListener(self.node_list["shenshi_equip_btn"], BindTool.Bind(self.OnQuickEquip, self))
	XUI.AddClickEventListener(self.node_list["shenshi_go_active"], BindTool.Bind(self.OnClickGoToActive, self))
	XUI.AddClickEventListener(self.node_list["btn_shenshi_upgrade"], BindTool.Bind(self.OnClickShenShiUpgrade, self))
	XUI.AddClickEventListener(self.node_list["btn_shenshi_succinct"], BindTool.Bind(self.OnClickShenShiSuccinct, self))
	XUI.AddClickEventListener(self.node_list["succinct_up_btn"], BindTool.Bind(self.OnClickShenShiSuccinctUp, self))
	XUI.AddClickEventListener(self.node_list["succinct_up_check_btn"], BindTool.Bind(self.OnClickShenShiSuccinctCheckBtn, self))
	XUI.AddClickEventListener(self.node_list["jingshen_info_tips_btn"], BindTool.Bind(self.OnClickShenShiJingShenTipsBtn, self))
	
	self.node_list["shenshi_bag_btn"].button:AddClickListener(function()--打开神饰背包
		TianShenWGCtrl.Instance:OpenTianShenBaoXiaView()
	end)

	self:CreateActivationAccordion(TianShenView.TabIndex.ShenShi)
	self:FlushActivationAccordionTable()
end

function TianShenView:ShenShiReleaseCallBack()
	if self.dh_render_list then
		for k, v in pairs(self.dh_render_list) do
			v:DeleteMe()
		end

		self.dh_render_list = nil
	end

	if self.dh_skill_list then
        for k,v in pairs(self.dh_skill_list) do
            v:DeleteMe()
        end
        self.dh_skill_list = nil
    end

	if self.shenshi_role_model then
		self.shenshi_role_model:DeleteMe()
		self.shenshi_role_model = nil
	end

	if self.shenshi_show_effect_loader then
		self.shenshi_show_effect_loader:Destroy()
		self.shenshi_show_effect_loader = nil
	end

    if self.ts_dh_cell_list then
		for k,v in pairs(self.ts_dh_cell_list) do
			for k1,v1 in pairs(v) do
				v1:DeleteMe()
			end
		end

		self.ts_dh_cell_list = nil
	end

	if self.succinct_slider_list_view then
		self.succinct_slider_list_view:DeleteMe()
		self.succinct_slider_list_view = nil
	end

	if self.succinct_spend_item_cell then
		self.succinct_spend_item_cell:DeleteMe()
		self.succinct_spend_item_cell = nil
	end

	if self.succinct_spend_money then
		self.succinct_spend_money:DeleteMe()
		self.succinct_spend_money = nil
	end

	if self.succinct_up_tips then
		self.succinct_up_tips:DeleteMe()
		self.succinct_up_tips = nil
	end

	if self.succinct_up_tips2 then
		self.succinct_up_tips2:DeleteMe()
		self.succinct_up_tips2 = nil
	end

	self.shenshi_ts_select_index = -1
	self.can_up_grade = false
	self.dh_is_need_jump = nil
	self.force_flush_tsdh_list = nil
	self.succinct_jump_index = nil
	self.succinct_select_data = nil
	self.succinct_use_item = nil
end

function TianShenView:FlushShenShiBtnType()
	if self.shenshi_btn_type == nil then
		self.shenshi_btn_type = SHENSHI_BTN_TYPE.UPGRADE

		local has_red,compose_red = TianShenWGData.Instance:CheckEquipShenShiRed(self.select_data and self.select_data.index)
		local show_red = (has_red > 0 and compose_red == 0) or (has_red > 0 and compose_red == 1)
		local succinct_red = TianShenWGData.Instance:GetTianShenShenShiRefineRed(self.select_data and self.select_data.index)
		if (not show_red) and succinct_red then
			self.shenshi_btn_type = SHENSHI_BTN_TYPE.SUCCINCT
		end
	else
		if self.shenshi_btn_type == SHENSHI_BTN_TYPE.SUCCINCT then
			local tianshen_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.select_data.index)
			if not tianshen_data then 
				SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TianShenNoActive)
				self.shenshi_btn_type = SHENSHI_BTN_TYPE.UPGRADE
				return 
			end
			local is_lock = tianshen_data.jingshen <= 0
			local js_next_data = TianShenWGData.Instance:GetShenShiJinShengDataByLevel(self.select_data.index, tianshen_data.jingshen + 1)
			local need_name = js_next_data and js_next_data.lv_name or ""
			
			if is_lock then
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.TianShen.TSShenShiQulityLvUp2, need_name))
				self.shenshi_btn_type = SHENSHI_BTN_TYPE.UPGRADE
			end
		end
	end

	self:FlushShenShiBtnStatus()
end

-- 按钮切换状态展示
function TianShenView:FlushShenShiBtnStatus()
	self.node_list.shenshi_upgrade:CustomSetActive(self.shenshi_btn_type == SHENSHI_BTN_TYPE.UPGRADE)
	self.node_list.shenshi_succinct:CustomSetActive(self.shenshi_btn_type == SHENSHI_BTN_TYPE.SUCCINCT)
	self.node_list.btn_shenshi_upgrade_hl:CustomSetActive(self.shenshi_btn_type == SHENSHI_BTN_TYPE.UPGRADE)
	self.node_list.btn_shenshi_succinct_nor:CustomSetActive(self.shenshi_btn_type == SHENSHI_BTN_TYPE.UPGRADE)
	self.node_list.btn_shenshi_succinct_hl:CustomSetActive(self.shenshi_btn_type == SHENSHI_BTN_TYPE.SUCCINCT)
	self.node_list.btn_shenshi_upgrade_nor:CustomSetActive(self.shenshi_btn_type == SHENSHI_BTN_TYPE.SUCCINCT)
end

function TianShenView:ShenShiOnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.open_param then
				self.shenshi_btn_type = tonumber(v.open_param) or 1
			end
		end
	end

	self:FlushShenShiTsIconList()
	self:FlushEquipLists()
	self:FlushAllRedDot()
end

-- 刷新天神列表
function TianShenView:FlushShenShiTsIconList()
	self.force_flush_tsdh_list = true
	local data_list = TianShenWGData.Instance:GetTianShenInfoDataList(TianShenView.TabIndex.ShenShi)
	self:RefreshAccordionData(TianShenView.TabIndex.ShenShi)
	self:RefreshAccordionRed(TianShenView.TabIndex.ShenShi)

	local jump_cell_index = nil
	if self.dh_is_need_jump then
		for i, v in ipairs(data_list) do
			local has_red, compose_red = TianShenWGData.Instance:CheckEquipShenShiRed(v.index)
			local show_red = (has_red > 0 and compose_red == 0) or (has_red > 0 and compose_red == 1)
			local succinct_red = TianShenWGData.Instance:GetTianShenShenShiRefineRed(v.index)

			if show_red or succinct_red then
				jump_cell_index = v.index
				break
			end
		end
	end
	self.dh_is_need_jump = false

	if not jump_cell_index and self.shenshi_ts_select_index >= 0 then
		for i, v in ipairs(data_list) do
			if v.index == self.shenshi_ts_select_index then
				jump_cell_index = v.index
				break
			end
		end
	end

	jump_cell_index = jump_cell_index or -1
	self:AccordionJumpToSelectForIndex(jump_cell_index)
end

function TianShenView:ShenShiItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	self:FlushShenShiTsIconList()
	self:FlushEquipLists()
	self:FlushAllRedDot()
end

-- 刷新所有红点
function TianShenView:FlushAllRedDot()
	self:DoFLushShenShiBagRedPoint()
end

function TianShenView:PlayShenShiEffect()
	local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_tiansheng_huohua)
	self.node_list.ss_effect:ChangeAsset(bundle_name, asset_name, false)
end

function TianShenView:EquipCallBack(item)
	local data = item:GetData()
	if not data then return end
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		local item_id = TianShenWGData.Instance.virtual_item_id[item.index]
		if item_id then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id})
		end
		return
	end

	TipWGCtrl.Instance:OpenItem(item.data, ItemTip.FROM_TIANSHEN_SHENSHI_EQUIP, nil,nil,nil)
end

function TianShenView:FlushEquipLists()
	if not self.node_list or not self.node_list.shenshi_capability_label or not self.dh_render_list or self.select_data == nil then
		return
	end

	local tianshen_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.select_data.index)
	if not tianshen_data then return end
	local equip_list = tianshen_data.shenshi_part

	local bonus = 0
	if tianshen_data.shenshi_refine_value_list then
		for i, v in ipairs(tianshen_data.shenshi_refine_value_list) do
			bonus = bonus + v
		end
	end

	bonus = (bonus / 10000) + 1

	local eat_cap = 0
	if tianshen_data.jingshen >= 1 then
		local eat_attr = TianShenWGData.Instance:GetColorLimitAttrTab(tianshen_data.jingshen)
		for k, v in pairs(eat_attr) do
			eat_attr[k] = eat_attr[k] * bonus
		end

		local attr_list, capability = ItemShowWGData.Instance:OutStrAttrListAndCapality(eat_attr)
		eat_cap = capability
	end
	self.node_list.shenshi_capability_label.text.text = tianshen_data.zhanli2 + eat_cap
	self.can_up_grade = true
	for k, v in pairs(self.dh_render_list) do
		local data = {}
		if equip_list[k].item_id == 0 then
			self.can_up_grade = false
		end
		data.item_id = equip_list[k].item_id
		data.tianshen_index = self.select_data.index
        v:SetData(data)
    end

	self.is_max_rank = TianShenWGData.Instance:ShenShiIsMaxRank(self.select_data.index, tianshen_data.jingshen)
	local is_active = TianShenWGData.Instance:IsActivation(self.select_data.index)

	if not is_active then
		self.node_list.shenshi_go_active:SetActive(true)
		self.node_list.shenshi_equip_btn:SetActive(false)
		self.node_list.shenshi_max_rank:SetActive(false)
		self.node_list.shenshi_bag_btn:SetActive(true)
		self.shenshi_icon_effect_is_on = false
	elseif self.is_max_rank and self.can_up_grade then
		self.node_list.shenshi_equip_btn:SetActive(false)
		self.node_list.shenshi_max_rank:SetActive(true)
		self.node_list.shenshi_go_active:SetActive(false)
		self.node_list.shenshi_bag_btn:SetActive(false)
		self.shenshi_icon_effect_is_on = false
	else
		self.node_list.shenshi_max_rank:SetActive(false)
		self.node_list.shenshi_equip_btn:SetActive(true)
		self.node_list.shenshi_go_active:SetActive(false)
		self.node_list.shenshi_bag_btn:SetActive(true)
		if self.can_up_grade then
			if not self.shenshi_icon_effect_is_on then
				self.shenshi_icon_effect_is_on = true
			end
			self.node_list.equip_btn_text.text.text = Language.TianShen.ShenShiEquipBtn[2]
		else
			self.node_list.equip_btn_text.text.text = Language.TianShen.ShenShiEquipBtn[1]
			self.shenshi_icon_effect_is_on = false
		end
	end
	
	local has_red,compose_red = TianShenWGData.Instance:CheckEquipShenShiRed(self.select_data.index)
	local show_red = (has_red > 0 and compose_red == 0) or (has_red > 0 and compose_red == 1)
	self.node_list.shenshi_equip_red:SetActive(show_red)
	self.node_list.shenshi_upgrade_remind:SetActive(show_red)
	if not show_red then
		self.node_list.equip_btn_text.text.text = Language.TianShen.ShenShiEquipBtn[3]
	end

	-- 神格品质
	local js_data = TianShenWGData.Instance:GetShenShiJinShengDataByLevel(self.select_data.index, tianshen_data.jingshen)
	local js_next_data = TianShenWGData.Instance:GetShenShiJinShengDataByLevel(self.select_data.index, tianshen_data.jingshen + 1)
	local next_color = ITEM_COLOR[tianshen_data.jingshen + 1]
	local now_color = ITEM_COLOR[tianshen_data.jingshen]

	if not IsEmptyTable(js_next_data) then
		local lv_str = ToColorStr(Language.TianShen.TSShenShiQulityLv[tianshen_data.jingshen], next_color)
		local next_lv_name = js_next_data.lv_name or ""
		local tips_str = string.format(Language.TianShen.TSShenShiQulityLvUpTips, lv_str, ToColorStr(next_lv_name, next_color))
		local show_str = is_active and tips_str or Language.TianShen.TSShenShiQulityError
		
		self.node_list.jingshen_info_tips.text.text = show_str
	else
		self.node_list.jingshen_info_tips.text.text = Language.TianShen.TSShenShiQulityLvFull
	end

	self.node_list.ss_jingshen_name.text.text = ToColorStr(js_data.lv_name, now_color)
	local bonus = 0
	if tianshen_data.shenshi_refine_value_list then
		for i, v in ipairs(tianshen_data.shenshi_refine_value_list) do
			bonus = bonus + v
		end
	end

	local normal_str = string.format("a3_ts_succinct_0%d", tianshen_data.jingshen)
	self.node_list.ss_img_jingshen.image:LoadSprite(ResPath.GetF2TianShenImage(normal_str))
	self.node_list.ss_lbl_all_attr_value.text.text = string.format("%s%%", string.format("%.2f", (bonus / 100)))
	self.node_list.succinct_attr_txt.text.text = string.format(Language.TianShen.TSShenShiTotalBouns, string.format("%.2f", (bonus / 100)))
	self:FlushShenshiSuccinctList()
end

function TianShenView:FlushShenshiSuccinctList()
	local succinct_list = TianShenWGData.Instance:GetTianShenShenShiRefineList(self.select_data.index)
	if succinct_list then
		self.succinct_slider_list_view:SetDataList(succinct_list)

		if self.succinct_jump_index == nil then
			local jump_index = 1

			for i, v in ipairs(succinct_list) do
				if v and v.cfg then
					local base_data, baodi_data = TianShenWGData.Instance:GetTianShenShenShiRefineInterval(v.cfg)
					local baodi_min_value = (baodi_data.min_value or 0) / 100
					local curr_vaule = v.bonus_value or 0
					local item_num = ItemWGData.Instance:GetItemNumInBagById(v.cfg.baodi_cost_item_id)
					local is_red = curr_vaule == 0
					is_red = is_red or (curr_vaule < baodi_min_value and item_num >= v.cfg.baodi_cost_item_num)
					
					if is_red and v.is_unlock then
						jump_index = i
						break
					end
				end
			end

			self.succinct_slider_list_view:JumpToIndex(jump_index, 6)
		else
			self.succinct_slider_list_view:JumpToIndex(self.succinct_jump_index, 6)
		end

		local succinct_red = TianShenWGData.Instance:GetTianShenShenShiRefineRed(self.select_data.index)
		self.node_list.shenshi_succinct_remind:CustomSetActive(succinct_red)
	end
end

function TianShenView:OnSelectSuccinctSliderCB(succinct_item, cell_index, is_default, is_click)
    if nil == succinct_item or nil == succinct_item.data then
		return
	end

	self.succinct_select_data = succinct_item.data
	self:FlushSuccinctSliderStatus()
	if self.succinct_jump_index == cell_index then
		return
	end

	self.succinct_jump_index = cell_index
end

function TianShenView:OnSelectSuccinctLimit(succinct_item)
    if nil == succinct_item then
		return false
	end

	if not succinct_item.data.is_unlock then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.ClickSkillTip2)
		return true
	end

	return false
end

function TianShenView:FlushSuccinctSliderStatus()
    if nil == self.succinct_select_data then
		return
	end

	local cfg = self.succinct_select_data.cfg

	if not cfg then
		return
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.baodi_cost_item_id)
	local color = item_num >= cfg.baodi_cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
	self.succinct_spend_item_cell:SetData({item_id = cfg.baodi_cost_item_id})
	self.succinct_spend_item_cell:SetRightBottomTextVisible(true)
	self.succinct_spend_item_cell:SetRightBottomColorText(string.format("%d/%d", item_num, cfg.baodi_cost_item_num), color)

	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
	self.succinct_spend_money:SetData({item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD})
	color = role_gold >= cfg.base_cost_gold and COLOR3B.D_GREEN or COLOR3B.D_RED
	self.succinct_spend_money:SetRightBottomTextVisible(true)
	self.succinct_spend_money:SetRightBottomColorText(string.format("%d", cfg.base_cost_gold), color)
	self:FlushSuccinctUpCheckStatus(false)
end

function TianShenView:FlushSuccinctUpCheckStatus(is_click)
    if nil == self.succinct_select_data then
		return
	end

	local cfg = self.succinct_select_data.cfg

	if not cfg then
		return
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.baodi_cost_item_id)
	if item_num < cfg.baodi_cost_item_num then
		self.succinct_use_item = false

		if is_click then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
		end
	end

	self.node_list.succinct_up_check:CustomSetActive(self.succinct_use_item == true)
	local base_data, baodi_data = TianShenWGData.Instance:GetTianShenShenShiRefineInterval(cfg)
	local base_min_value = (base_data.min_value or 0) / 100
	local baodi_min_value = (baodi_data.min_value or 0) / 100
	local baodi_max_value = (baodi_data.max_value or 0) / 100

	local lerp_value = baodi_min_value - base_min_value
	local have_use_item_str = self.succinct_use_item and ToColorStr(string.format(Language.TianShen.TSShenShiTotalBouns4, lerp_value), COLOR3B.GREEN) or ""
	local tips_str = string.format(Language.TianShen.TSShenShiTotalBouns3, base_min_value, have_use_item_str, baodi_max_value)
	self.node_list.succinct_up_tips.text.text = tips_str

	local curr_vaule = self.succinct_select_data.bonus_value or 0
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.baodi_cost_item_id)
	local is_red = curr_vaule == 0
	is_red = is_red or (curr_vaule < baodi_min_value and item_num >= cfg.baodi_cost_item_num)
	self.node_list.succinct_up_remind:CustomSetActive(is_red and self.succinct_select_data.is_unlock)
end

function TianShenView:ShenShiShowIndexCallback()
	self.dh_is_need_jump = true
end

function TianShenView:FlushXingXiangRenderClick(cell, cell_index, is_default, is_click)
	if cell == nil then
		return
	end

	local data = cell:GetData()
	-- TianShenWGData.Instance:ShenShiTsSelectIndex(data.index)
	if self.shenshi_ts_select_index == data.index and not self.force_flush_tsdh_list then
		return
	end

	self.force_flush_tsdh_list = false
	self.can_up_grade = true
	local need_flushmodel = self.shenshi_ts_select_index ~= data.index
	self.shenshi_ts_select_index = data.index

	if need_flushmodel then
		self.succinct_jump_index = nil
		self.succinct_select_data = nil
	end

	TianShenWGData.Instance:SetCurSelectTianShenIndex(data.index)
	self:GetCurSelectListData(TianShenWGData.Instance:GetTianShenCfg(data.index))
	if need_flushmodel then
		self:FlushShenShiModelDisPlay()
	end

	self:FlushShenShiBtnType()
	self:FlushEquipLists()
	self:FlushShenShiPasvSkill()
	self:FlushShenShiBtnStatus()
end

-- 刷新天神技能
function TianShenView:FlushShenShiPasvSkill()
	local cur_selected_ts_index = self.shenshi_ts_select_index
	local ts_active_status = TianShenWGData.Instance:GetTianshenInfoStatus(cur_selected_ts_index)
    local zhu_skill = TianShenWGData.Instance:GetTianShenZhuSkill(cur_selected_ts_index)
    for k,v in pairs(self.dh_skill_list) do
        local skill_id = tonumber(zhu_skill[k])
		local skill_data = {
			skill_id = skill_id,
			tianshen_index = cur_selected_ts_index,
			from_view = FROM_TIANSHEN_UPGRADE,
			is_open_skill = ts_active_status == 1,
			is_tianshen_select_view = false
		}
        v:SetData(skill_data)
    end
end

function TianShenView:ResetSelect()
	self.select_color_index = 1
	self.select_pingjie_index = 1
	self.select_buwei_index = 1
	self.node_list["text_color"].text.text = Language.TianShen.FenJiePingZhiList2[self.select_color_index]
	self.node_list["text_buwei"].text.text = Language.TianShen.BuWeiList[self.select_buwei_index]
end

--刷新模型展示
function TianShenView:FlushShenShiModelDisPlay()
	local data = self:GetCurSelectListData()
	if data then
		local audio = self.tianshen_shenshi_audio_play_t[data.index] == nil and data.show_audio or nil
		self.tianshen_shenshi_audio_play_t[data.index] = 1
		if self.shenshi_show_effect_loader then
			self.shenshi_show_effect_loader:Destroy()
			self.shenshi_show_effect_loader = nil
		end

		---添加化魔展示
		local appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(data.index) or data.appe_image_id

		if self.shenshi_role_model then
			self.shenshi_role_model:SetTianShenModel(appe_image_id, data.index, true, audio, SceneObjAnimator.Rest)
		end
	end
end

function TianShenView:FlushShenShiView()
	if not self:IsLoadedIndex(TianShenView.TabIndex.ShenShi) then
		return
	end
end

function TianShenView:OnShenShiTipsBack()
	RuleTip.Instance:SetContent(Language.TianShen.ShenShiTips2, Language.TianShen.ShenShiTips1)
end

function TianShenView:ShenShiOpenCallBack()
	--self:FlushShenShiView()
end

function TianShenView:ShenShiCloseCallBack()
	self.tianshen_shenshi_audio_play_t = {}
end

function TianShenView:OnQuickEquip()
	if self.can_up_grade then
		TianShenWGCtrl.Instance:SendShenShiOpera(TIANSHEN_SHENSHI_REQ.TIANSHEN_SHENSHI_REQ_JIN_SHENG,self.select_data.index,0)
	else
		local have_equip = false
		local click_return = false
		local is_compose = false

		for k,v in pairs(self.dh_render_list) do
			click_return = v:OnClickQuick()
			have_equip = have_equip or click_return
		end

		if not click_return then
			for i = 1, 4 do
				local need_item = TianShenWGData.Instance:CheckNeedShenShiItem(self.select_data.index,i)
				local can_compose = TianShenWGData.Instance:CheckWayofComposeID(need_item)
				if can_compose then
					is_compose = true
					TianShenWGCtrl.Instance:SendShenShiOpera(TIANSHEN_SHENSHI_REQ.TIANSHEN_SHENSHI_REQ_COMPOSE,need_item, self.select_data.index)
				end
			end
		end

		if not have_equip and not is_compose then
			TianShenWGCtrl.Instance:OpenTianShenBaoXiaView()
			-- TipWGCtrl.Instance:ShowSystemMsg(Language.TianShen.CanNotequip)
		end
	end
end

function TianShenView:OnClickShowSkillTip()
	TianShenWGCtrl.Instance:OpenShenShiShiSkillTip(self.select_data.index)
end

function TianShenView:OnClickGoToActive()
	local param_t = {}
	param_t.to_ui_param = self.select_data.index
	ViewManager.Instance:Open(GuideModuleName.TianShenView, tonumber(TabIndex.tianshen_activation),"all", param_t)
end

--道痕背包红点
function TianShenView:DoFLushShenShiBagRedPoint()
	local flag = TianShenWGData.Instance:CheckTianShenBoxRemind() > 0
	if self.node_list["shenshi_bag_red"] then
		self.node_list["shenshi_bag_red"]:SetActive(flag)
	end
end

-- 切换升阶
function TianShenView:OnClickShenShiUpgrade()
	if self.shenshi_btn_type == SHENSHI_BTN_TYPE.UPGRADE then
		return
	end

	self.shenshi_btn_type = SHENSHI_BTN_TYPE.UPGRADE
	self:FlushShenShiBtnStatus()
end

-- 切换灵韵
function TianShenView:OnClickShenShiSuccinct()
	if self.shenshi_btn_type == SHENSHI_BTN_TYPE.SUCCINCT then
		return
	end

	local tianshen_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.select_data.index)
	if not tianshen_data then return end
	local is_lock = tianshen_data.jingshen <= 0
	local js_next_data = TianShenWGData.Instance:GetShenShiJinShengDataByLevel(self.select_data.index, tianshen_data.jingshen + 1)
	local need_name = js_next_data and js_next_data.lv_name or ""
	
	if is_lock then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.TianShen.TSShenShiQulityLvUp2, need_name))
		return
	end

	self.shenshi_btn_type = SHENSHI_BTN_TYPE.SUCCINCT
	self:FlushShenShiBtnStatus()
end

-- 灵韵洗练
function TianShenView:OnClickShenShiSuccinctUp()
    if nil == self.succinct_select_data then
		return
	end

	local cfg = self.succinct_select_data.cfg
	local is_base = self.succinct_use_item and 0 or 1

	if not cfg then
		return
	end

	local curr_vaule = self.succinct_select_data.bonus_value or 0
	local curr_vaule_pro = curr_vaule * 100 / cfg.progress_max

	if curr_vaule_pro > 0.9 then
		if not self.succinct_up_tips then
			self.succinct_up_tips = Alert.New()
		end

		self.succinct_up_tips:SetLableString(string.format(Language.TianShen.TSShenShiSuccinctTips5, ToColorStr(">90%", COLOR3B.PURPLE)))
		self.succinct_up_tips:SetOkFunc(function()
			self:OnClickShenShiSuccinctUpConfirm(self.select_data.index, cfg.hole, is_base, cfg.base_cost_gold)
		end)
		self.succinct_up_tips:SetCancelFunc(nil)
		self.succinct_up_tips:Open()
	else
		self:OnClickShenShiSuccinctUpConfirm(self.select_data.index, cfg.hole, is_base, cfg.base_cost_gold)
	end
end

-- 灵韵洗练
function TianShenView:OnClickShenShiSuccinctUpConfirm(index, hole, is_base, base_cost_gold)
	if not self.succinct_up_tips2 then
		self.succinct_up_tips2 = Alert.New()
	end

	self.succinct_up_tips2:SetShowCheckBox(true, "use_gold_succinct_up_confirm")
	self.succinct_up_tips2:SetCheckBoxText(Language.TreasureHunt.NoRemind)
	self.succinct_up_tips2:SetLableString(string.format(Language.TianShen.TSShenShiSuccinctTips7, ToColorStr(base_cost_gold, COLOR3B.GREEN)))
	self.succinct_up_tips2:SetOkFunc(function()
		TianShenWGCtrl.Instance:SendTianShenOperaReq(TianShenWGCtrl.Opera_Type.Opera_Type27, index, hole, is_base)
	end)
	self.succinct_up_tips2:SetCancelFunc(nil)
	self.succinct_up_tips2:Open()
end

-- 切换使用道具
function TianShenView:OnClickShenShiSuccinctCheckBtn()
	self.succinct_use_item = not self.succinct_use_item
	self:FlushSuccinctUpCheckStatus(true)
end

-- 神格晋升灵韵提示
function TianShenView:OnClickShenShiJingShenTipsBtn()
	if self.select_data == nil then
		return
	end

	TianShenWGCtrl.Instance:OpenTipsShenShiSuccinctView(self.select_data.index)
end
---------------------------------------- 
-- TSShenShiItemRender
----------------------------------------
TSShenShiItemRender = TSShenShiItemRender or BaseClass(TianShenItemRender)

function TSShenShiItemRender:OnFlush()
	TianShenItemRender.OnFlush(self)
	if self.data == nil then return end

	local flag = self.data.can_active_status == 1
	local flag_text = flag and Language.TianShen.FlagTipTxt.CanActive or ""
	self.node_list.flag_tip:SetActive(flag)
	self.node_list.flag_tip_text.text.text = flag_text

	local has_red, compose_red = TianShenWGData.Instance:CheckEquipShenShiRed(self.data.index)
	self.node_list.remind:SetActive(has_red > 0 or compose_red == 1)
end

-- 灵韵
SuccinctSliderRender = SuccinctSliderRender or BaseClass(TianShenItemRender)
function SuccinctSliderRender:OnFlush()
	if self.data == nil then return end
	self.node_list.normal:CustomSetActive(self.data.is_unlock)
	self.node_list.lock:CustomSetActive(not self.data.is_unlock)

	local cfg = self.data.cfg
	if not cfg then
		return
	end

	local normal_str = string.format("a3_ts_succinct_0%d", cfg.need_level)
	self.node_list.normal_icon.image:LoadSprite(ResPath.GetF2TianShenImage(normal_str))
	local bg_name = self.data.is_unlock and "a3_zs_sg_di1" or "a3_zs_sg_di2"
	self.node_list["bg"].image:LoadSprite(ResPath.GetF2TianShenImage(bg_name))
	local js_next_data = TianShenWGData.Instance:GetShenShiJinShengDataByLevel(cfg.index, cfg.need_level)
	local need_name = js_next_data and js_next_data.lv_name or ""

	local fill_index = 1
	local progress = self.data.bonus_value * 100 / cfg.progress_max
	if not self.data.is_unlock then
		fill_index = 0
	elseif progress >= 1 then
		fill_index = 3
	elseif progress >= 0.5 and progress < 1 then
		fill_index = 2
	end

	local fill_image_str = string.format("a3_zs_sg_jdt%d", fill_index)
	self.node_list.fill_image.image:LoadSprite(ResPath.GetF2TianShenImage(fill_image_str))

	if self.data.is_unlock then
		self.node_list.progress.slider.value = progress
		self.node_list.attr_value.text.text = string.format(Language.TianShen.TSShenShiTotalBouns2, string.format("%.2f", self.data.bonus_value))
	else
		self.node_list.progress.slider.value = 0
		self.node_list.attr_value.text.text = ToColorStr(string.format("%s%s", need_name, Language.Role.JieSuo), COLOR3B.RED)
	end

	local base_data, baodi_data = TianShenWGData.Instance:GetTianShenShenShiRefineInterval(cfg)
	local baodi_min_value = (baodi_data.min_value or 0) / 100
	local curr_vaule = self.data.bonus_value or 0
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.baodi_cost_item_id)
	local is_red = curr_vaule == 0
	is_red = is_red or (curr_vaule < baodi_min_value and item_num >= cfg.baodi_cost_item_num)
	self.node_list.remind:CustomSetActive(is_red and self.data.is_unlock)
end

function SuccinctSliderRender:OnClick(isOn)
	if self.data == nil then return end

	if not self.data.is_unlock then
		return
	end

	if nil ~= self.click_callback then
		self.click_callback(self, isOn)
	end
end

function SuccinctSliderRender:OnSelectChange(is_select)
	if self.data == nil then return end
	self.node_list.select:CustomSetActive(self.data.is_unlock and is_select)
end

-----------------------图案下孔位render
TianShenEquipCell = TianShenEquipCell or BaseClass(BaseRender)

function TianShenEquipCell:__init()
	self.node_list["click_btn"].button:AddClickListener(BindTool.Bind(self.OnClickEquipCell,self))
	self.select_tianshen_index = 0
	self.level = 0
	self.is_equip = false
	self.can_equip = false
	self.can_compose = false
	self.can_get = false
	self.is_active = true
	self.com_way_table = {}
	self.old_item_id = -100
	self.old_tianshen_index = -100
	self:ShenShiArrowTween()
end

function TianShenEquipCell:ReleaseCallBack()
	if self.arrow_shenshi_tweener then
		self.arrow_shenshi_tweener:Kill()
        self.arrow_shenshi_tweener = nil
    end
end

function TianShenEquipCell:OnFlush()
	self.is_equip = false
	self.can_equip = false
	self.can_compose = false
	self.can_get = false
	self.is_active = TianShenWGData.Instance:IsActivation(self.data.tianshen_index)
	if self.old_tianshen_index == -100 or self.old_tianshen_index ~= self.data.tianshen_index then
		self.old_tianshen_index = self.data.tianshen_index
		self.old_item_id = self.data.item_id
	else
		if self.old_item_id ~= self.data.item_id and self.data.item_id >0 then
			self.old_item_id = self.data.item_id
		end
	end

	self.node_list.icon_img:SetActive(false)
	self.node_list.quality_bg:SetActive(false)
	local need_equip_item = TianShenWGData.Instance:CheckNeedShenShiItem(self.data.tianshen_index, self.index)
	if self.is_active and self.data.item_id == 0 then
		local have_num = TianShenWGData.Instance:GetShenShiEquipInBag(need_equip_item).num or 0
		self.can_equip = have_num > 0
		if not self.can_equip then
			self.can_compose, self.com_way_table = TianShenWGData.Instance:CheckWayofComposeID(need_equip_item)
			self.node_list["can_compose"]:SetActive(self.can_compose and self.is_active)
			self.node_list["can_get"]:SetActive(not self.can_compose and self.is_active)
		else
			self.node_list["can_compose"]:SetActive(false)
			self.node_list["can_get"]:SetActive(false)
		end
		self.node_list["red"]:SetActive(self.can_equip)
		self.node_list["plus"]:SetActive(self.can_equip)
	else
		self.can_equip = false
		self.can_compose = false
		self.can_get = false
		self.node_list["can_compose"]:SetActive(false)
		self.node_list["can_get"]:SetActive(false)
		self.node_list["plus"]:SetActive(false)
		self.node_list["red"]:SetActive(false)

		self.is_equip = self.is_active
	end

	self:LoadBGAndIconSprite()
	local is_gray = not self.is_active or self.data.item_id == 0
	XUI.SetGraphicGrey(self.node_list.icon_img, is_gray)
	XUI.SetGraphicGrey(self.node_list.quality_bg, is_gray)

	local item_id = self.data.item_id == 0 and need_equip_item or self.data.item_id
	self:LoadAttributeTab(item_id)
end

function TianShenEquipCell:LoadAttributeTab(item_id)
 	local attr_cfg = TianShenWGData.Instance:GetTianShenAttributeTab(item_id)
	local attr_num = 0
	for k,v in pairs(attr_cfg) do
		if v > 0 then
			if Language.Common.AttrNameList[k] then
				attr_num = attr_num + 1
				self.node_list["attr_name"..attr_num].text.text = Language.Common.AttrNameList[k]
				local color = (not self.is_active or self.data.item_id == 0) and COLOR3B.C6 or COLOR3B.DEFAULT_NUM
				self.node_list["attr"..attr_num].text.text = ToColorStr(v, color)
			end
		end
	end

	for i = 1, 2 do
		self.node_list["attr"..i]:SetActive(i <= attr_num)
	end
 end 

function TianShenEquipCell:LoadBGAndIconSprite()
	if not self.can_equip then
		local need_equip_item = TianShenWGData.Instance:CheckNeedShenShiItem(self.data.tianshen_index, self.index)
		local cfg = ItemWGData.Instance:GetItemConfig(need_equip_item)
		if cfg then
			local bundle2, asset2 = ResPath.GetF2TianShenIcon(cfg.icon_id)
			self.node_list.icon_img.image:LoadSpriteAsync(bundle2, asset2, function()
				self.node_list.icon_img.image:SetNativeSize()
				self.node_list.icon_img:SetActive(true)
			end)

			local bundle, asset = ResPath.GetCommonImages("a3_sc_btn_bg_" .. cfg.color)
			self.node_list.quality_bg.image:LoadSpriteAsync(bundle, asset, function()
				self.node_list.quality_bg.image:SetNativeSize()
				self.node_list.quality_bg:SetActive(true)
			end)
		end
	end
end

function TianShenEquipCell:OnClickEquipCell()
	local need_equip_item = TianShenWGData.Instance:CheckNeedShenShiItem(self.data.tianshen_index, self.index)
	if self.can_equip and self.is_active then
		local bag_equip_info = TianShenWGData.Instance:GetShenShiEquipInBag(need_equip_item)
		local force_index_tab = ItemWGData.Instance:GetItemListIndexByStuffBag(need_equip_item)
		TianShenWGCtrl.Instance:SendShenShiOpera(TIANSHEN_SHENSHI_REQ.TIANSHEN_SHENSHI_REQ_PUT, self.data.tianshen_index,force_index_tab[1])
	elseif self.can_compose and self.is_active then
		local data = {}
		data.target_item = need_equip_item
		data.com_way_table = self.com_way_table
		local compose_func = function ()
			TianShenWGCtrl.Instance:SendShenShiOpera(TIANSHEN_SHENSHI_REQ.TIANSHEN_SHENSHI_REQ_COMPOSE,need_equip_item, self.data.tianshen_index)
		end
		data.compose_func = compose_func
		TianShenWGCtrl.Instance:OpenShenShiComposeTip(data)
	else
		TipWGCtrl.Instance:OpenItem({item_id = need_equip_item,num = 1})
	end
end

function TianShenEquipCell:OnClickQuick()
	local need_equip_item = TianShenWGData.Instance:CheckNeedShenShiItem(self.data.tianshen_index, self.index)
	if self.can_equip then
		local bag_equip_info = TianShenWGData.Instance:GetShenShiEquipInBag(need_equip_item)
		local force_index_tab = ItemWGData.Instance:GetItemListIndexByStuffBag(need_equip_item)	
		TianShenWGCtrl.Instance:SendShenShiOpera(TIANSHEN_SHENSHI_REQ.TIANSHEN_SHENSHI_REQ_PUT, self.data.tianshen_index, force_index_tab[1])
		return true
	end
	return false
end

function TianShenEquipCell:PlayEquipFlushLight()
	--[[self.node_list.equip_light_effect:SetActive(true)
	GlobalTimerQuest:AddDelayTimer(function ()
		self.node_list.equip_light_effect:SetActive(false)
	end,1)]]
end

function TianShenEquipCell:SetLevel(cur_level)
	self.level = cur_level
end

function TianShenEquipCell:GetIsEquip()
	return self.is_equip
end

function TianShenEquipCell:ShenShiArrowTween()
	if self.arrow_shenshi_tweener then
		self.arrow_shenshi_tweener:Kill()
        self.arrow_shenshi_tweener = nil
    end

	local tween_time = 0.8
    local node = self.node_list["can_compose"]
		if node then
	        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -25)
	        self.arrow_shenshi_tweener = node.rect:DOAnchorPosY(-17, tween_time)
	        self.arrow_shenshi_tweener:SetEase(DG.Tweening.Ease.InOutSine)
	        self.arrow_shenshi_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		end
end