FestivalMoWangYouliWGData = FestivalMoWangYouliWGData or BaseClass()

function FestivalMoWangYouliWGData:__init()
	if FestivalMoWangYouliWGData.Instance then
		ErrorLog("[FestivalMoWangYouliWGData] Attemp to create a singleton twice !")
	end
	FestivalMoWangYouliWGData.Instance = self

	FestivalActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_OA_MOWANGYOULI_2, {[1] = OPERATION_EVENT_TYPE.LEVEL},
    	BindTool.Bind(self.GetActCanOpen, self))

	self:LoadConfig()

	--boss数据监听
	self.monitor_boss_times_list = {}
	--初始化数据
	for i = 1, BOSS_TASK_TYPE.MAX do
		self.monitor_boss_times_list[i] = {enter_times = 0, max_times = 0}
	end
end

function FestivalMoWangYouliWGData:__delete()
	FestivalMoWangYouliWGData.Instance = nil
end

function FestivalMoWangYouliWGData:LoadConfig()
	self.big_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_mowangyouli_auto")
	self.config_param_cfg = self.big_cfg.config_param --配置参数
	self.mowangyouli_task_map = ListToMapList(self.big_cfg.mowangyouli_task, "grade") --魔王有礼-任务
	self.interface_map = ListToMap(self.big_cfg.interface, "interface")


    self.mowangyouli_drop_map = ListToMapList(self.big_cfg.mowangyouli_drop_limit, "grade") --魔王有礼-掉落显示

	local boss_world_cfg = ListToMap(self.big_cfg.world_boss_show, "grade", "boss_id")
	local boss_vip_cfg = ListToMap(self.big_cfg.vipBoss_show, "grade", "boss_id")
	local boss_person_cfg = ListToMap(self.big_cfg.personboss_show, "grade", "boss_id")
	local boss_dabao_cfg = ListToMap(self.big_cfg.dabao_boss_show, "grade", "boss_id")
	self.boss_reward_item_cfg_map = {
		[TabIndex.boss_world] = boss_world_cfg,
		[TabIndex.boss_vip] = boss_vip_cfg,
		[TabIndex.boss_personal] = boss_person_cfg,
		[TabIndex.boss_dabao] = boss_dabao_cfg,
	}
end

--根据开服天数获取档次配置
function FestivalMoWangYouliWGData:GetParamCfgByServerOpenDay()
	local return_cfg = {}
	local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.FESTIVAL_ACT_OA_MOWANGYOULI_2)--TimeWGCtrl.Instance:GetCurOpenServerDay()
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.FESTIVAL_ACT_OA_MOWANGYOULI_2)
    local param_cfg = self.big_cfg.config_param
	for i = #param_cfg, 1, -1 do
		if param_cfg[i] and open_day >= param_cfg[i].start_server_day and open_day < param_cfg[i].end_server_day then
			return_cfg = param_cfg[i]
			break
		end
	end
	return return_cfg
end

--获取界面显示配置
function FestivalMoWangYouliWGData:GetViewCfg(grade)
	if self.interface_map and self.interface_map[grade] then
		return self.interface_map[grade]
	end
	return {}
end

function FestivalMoWangYouliWGData:GetMWTaskList()
	local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return {}
	end

	local cfg = self.mowangyouli_task_map[param_cfg.grade]
	if not cfg or IsEmptyTable(cfg) then
		return {}
	end

	local list = {}
	for k,v in ipairs(cfg) do
		local item = {}
		item.ID = v.ID
		item.grade = v.grade
		item.task_name = v.task_name
		item.mowang_level = v.mowang_level
		item.panel = v.panel
		item.icon = v.icon
		item.task_item_bg = v.task_item_bg
		item.task_item_icon_bg = v.task_item_icon_bg
		item.task_item_btn_bg = v.task_item_btn_bg
		item.task_item_btn_text = v.task_item_btn_text
		item.complete_flag = v.complete_flag
		item.complete_text = v.complete_text
		item.is_show_condition = v.is_show_condition
		table.insert(list, item)
	end
	return list
end

function FestivalMoWangYouliWGData:GetActivityEndTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_OA_MOWANGYOULI_2)
	if activity_info ~= nil then
		return activity_info.end_time
	end
	return  0
end

function FestivalMoWangYouliWGData:GetActCanOpen()
	local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return false
	end
	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level >= param_cfg.open_level then
		return true
	end
	return false
end

function FestivalMoWangYouliWGData:GetBossItemDataList(boss_id, boss_tab_index)
	if not boss_tab_index then
		return {}
	end

	local map = self.boss_reward_item_cfg_map[boss_tab_index]
	if not map then
		return {}
	end

	local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return {}
	end

	if not map[param_cfg.grade] or not map[param_cfg.grade][boss_id] then
		return {}
	end

	local cfg = map[param_cfg.grade][boss_id]
	local return_item_list = {}
	for i = 0, #cfg.show_item_2 do
		table.insert(return_item_list, cfg.show_item_2[i])
	end

	return return_item_list
end

function FestivalMoWangYouliWGData:SetBossTimesInfo(boss_type, enter_times, max_times)
	self.monitor_boss_times_list[boss_type].enter_times = enter_times
	self.monitor_boss_times_list[boss_type].max_times = max_times
end

function FestivalMoWangYouliWGData:GetBossTimesInfo(boss_type)
	return self.monitor_boss_times_list[boss_type]
end

function FestivalMoWangYouliWGData:SetItemInfoData(protocol)
	self.item_info_list = protocol.item_list 
end

function FestivalMoWangYouliWGData:GetCurCountByItemid(itemid)
	return self.item_info_list and self.item_info_list[itemid] or 0
end


function FestivalMoWangYouliWGData:GetMWPreViewReward()
    local param_cfg = self:GetParamCfgByServerOpenDay()
	if IsEmptyTable(param_cfg) or not param_cfg.grade then
		return {}
	end

	local cfg = self.mowangyouli_drop_map[param_cfg.grade]
	if not cfg or IsEmptyTable(cfg) then
		return {}
	end

    local list = {}
    local key
    for k,v in ipairs(cfg) do
        key = v.item_id
        if not list[key] then
            list[key] = {}
            list[key].item_id = v.item_id
            list[key].is_bind = 1
            list[key].activity_person_day_limit = v.activity_person_day_limit
            list[key].cur_num = self:GetCurCountByItemid(key)
            list[key].grade = v.grade
        else
            list[key].activity_person_day_limit = list[key].activity_person_day_limit + v.activity_person_day_limit
        end
    end
    local tb = {}
    for k,v in pairs(list) do
        tb[#tb+1] = v
    end
    tb = SortDataByItemColor(tb)
	return tb
end

