﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class SpiritChangePlayerWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(SpiritChangePlayer), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("PlayScreenEffectZone", PlayScreenEffectZone);
		<PERSON><PERSON>Function("SetScreenEffectZoneEffect", SetScreenEffectZoneEffect);
		<PERSON><PERSON>unction("UpdateScreenEffectZone", UpdateScreenEffectZone);
		<PERSON><PERSON>RegFunction("PauseScreenEffectZone", PauseScreenEffectZone);
		<PERSON><PERSON>RegFunction("CanPlayScreenEffectZone", CanPlayScreenEffectZone);
		L.RegFunction("ContinueScreenEffectZone", ContinueScreenEffectZone);
		<PERSON>.RegFunction("StopScreenEffectZone", StopScreenEffectZone);
		<PERSON><PERSON>unction("ShowWangQ<PERSON>", ShowWangQi);
		<PERSON><PERSON>ction("CloseWangQi", CloseWangQi);
		<PERSON><PERSON>ction("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		L.RegVar("m_Zone", get_m_Zone, set_m_Zone);
		L.RegVar("m_WangQi", get_m_WangQi, set_m_WangQi);
		L.RegVar("m_PlayWangQiPlaying", get_m_PlayWangQiPlaying, set_m_PlayWangQiPlaying);
		L.RegVar("m_PlayablePlaying", get_m_PlayablePlaying, set_m_PlayablePlaying);
		L.RegVar("CenterTrans", get_CenterTrans, set_CenterTrans);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PlayScreenEffectZone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			float arg2 = (float)LuaDLL.luaL_checknumber(L, 4);
			obj.PlayScreenEffectZone(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetScreenEffectZoneEffect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.SetScreenEffectZoneEffect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateScreenEffectZone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.UpdateScreenEffectZone(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int PauseScreenEffectZone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			obj.PauseScreenEffectZone();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CanPlayScreenEffectZone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			bool o = obj.CanPlayScreenEffectZone();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ContinueScreenEffectZone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			obj.ContinueScreenEffectZone();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopScreenEffectZone(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			obj.StopScreenEffectZone();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ShowWangQi(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.ShowWangQi(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CloseWangQi(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			SpiritChangePlayer obj = (SpiritChangePlayer)ToLua.CheckObject<SpiritChangePlayer>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.CloseWangQi(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_Zone(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			ScreenEffects.ScreenEffect ret = obj.m_Zone;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Zone on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_WangQi(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			ScreenEffects.ScreenEffect ret = obj.m_WangQi;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_WangQi on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_PlayWangQiPlaying(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			bool ret = obj.m_PlayWangQiPlaying;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_PlayWangQiPlaying on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_PlayablePlaying(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			bool ret = obj.m_PlayablePlaying;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_PlayablePlaying on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CenterTrans(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			UnityEngine.Transform ret = obj.CenterTrans;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CenterTrans on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_Zone(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			ScreenEffects.ScreenEffect arg0 = (ScreenEffects.ScreenEffect)ToLua.CheckObject<ScreenEffects.ScreenEffect>(L, 2);
			obj.m_Zone = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_Zone on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_WangQi(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			ScreenEffects.ScreenEffect arg0 = (ScreenEffects.ScreenEffect)ToLua.CheckObject<ScreenEffects.ScreenEffect>(L, 2);
			obj.m_WangQi = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_WangQi on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_PlayWangQiPlaying(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.m_PlayWangQiPlaying = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_PlayWangQiPlaying on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_PlayablePlaying(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.m_PlayablePlaying = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_PlayablePlaying on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CenterTrans(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SpiritChangePlayer obj = (SpiritChangePlayer)o;
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.CenterTrans = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CenterTrans on a nil value");
		}
	}
}

