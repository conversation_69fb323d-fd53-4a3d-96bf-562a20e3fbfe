require("game/battlefield/guildbattleranked/guild_battle_ranked_wg_data")
require("game/battlefield/guildbattleranked/guild_battle_ranked_view")
require("game/battlefield/guildbattleranked/guild_battle_ranked_end_view")
require("game/battlefield/guildbattleranked/guild_battle_top_view")
require("game/battlefield/guildbattleranked/guild_battle_changebody_view")
-- (新)帮派战
GuildBattleRankedWGCtrl = GuildBattleRankedWGCtrl or BaseClass(BaseWGCtrl)

function GuildBattleRankedWGCtrl:__init()
	if GuildBattleRankedWGCtrl.Instance then
		error("[GuildBattleRankedWGCtrl]:Attempt to create singleton twice!")
	end
	GuildBattleRankedWGCtrl.Instance = self

	self.data = GuildBattleRankedWGData.New()
	self.view = GuildBattleRankedView.New(GuideModuleName.GuildBattleRankedView)
	self.end_view = GuildBattleRankedEndView.New()
	self.top_view = GuildBattleTopView.New()
	self.guild_change_view = GuildBattleChageView.New()
	self:RegisterAllProtocals()
	self.mainui_open_complete_handle = GlobalEventSystem:Bind(OtherEventType.PASS_DAY2, BindTool.Bind(self.InitGuildBattleCallBack, self))
	-- self.attr_change = BindTool.Bind(self.RoleAttrChange, self)
	-- RoleWGData.Instance:NotifyAttrChange(self.attr_change, {"level"})

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
end

function GuildBattleRankedWGCtrl:__delete()
	
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil

	self.top_view:DeleteMe()
	self.top_view = nil

	self.guild_change_view:DeleteMe()
	self.guild_change_view = nil

	self.end_view:DeleteMe()
	self.end_view = nil

	self.is_end = nil

	GuildBattleRankedWGCtrl.Instance = nil
	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
		self.mainui_open_complete_handle = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	-- RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
end

function GuildBattleRankedWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCGuildBattleSceneUserInfo, "OnGuildBattleSceneUserInfo")
	self:RegisterProtocol(SCGuildBattleSceneGlobalInfo, "OnGuildBattleSceneGlobalInfo")
	self:RegisterProtocol(SCGuildBattleForwardInfo,"OnSCGuildBattleForwardInfo")
	self:RegisterProtocol(SCGuildBattleBuffInfo,"OnSCGuildBattleBuffInfo")
	self:RegisterProtocol(SCGuildBattleTopInfo,"OnSCGuildBattleTopInfo")

	self:RegisterProtocol(SCGuildBattleRankRewardInfo,"OnSCGuildBattleRankRewardInfo")
	self:RegisterProtocol(SCGuildBattleEndKeepWinInfo,"OnSCGuildBattleEndKeepWinInfo")
	self:RegisterProtocol(SCGuildBattleKillInfo,"OnSCGuildBattleKillInfo")
	self:RegisterProtocol(SCGuildBattleContinueKillMSG,"OnSCGuildBattleContinueKillMSG")
	self:RegisterProtocol(SCTianShen3v3KillNews,"OnSCTianShen3v3KillNews")
	self:RegisterProtocol(SCTianXiaDiYiContinueKillInfo,"OnSCTianXiaDiYiContinueKillInfo")

	self:RegisterProtocol(CSGuildBattleBuyMachineReq)
	self:RegisterProtocol(CSGuildBattleOPReq)
	self:RegisterProtocol(SCCrossGuildBattleHusongRoleInfo,"OnSCCrossGuildBattleHusongRoleInfo")
	self:RegisterProtocol(SCCrossGuildBattleLingshiInfo,"OnSCCrossGuildBattleLingshiInfo")
	self:RegisterProtocol(SCCrossGuildBattleNotify,"OnSCCrossGuildBattleNotify")
end

local VECTOR2_POS = Vector2(245, 300)
function GuildBattleRankedWGCtrl:OnGuildBattleSceneUserInfo(protocol)
	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local old_score = role_info_list.score or 0
	local gather_id = role_info_list and role_info_list.gather_id
	local protocol_info = protocol.role_info_list
	local new_score = protocol_info.score
	local add_score = new_score  - old_score
	self.data:SetGuildBattleSceneUserInfo(protocol)
	if gather_id and gather_id ~= protocol_info.gather_id then
		MainuiWGCtrl.Instance:FlushSkillList(true)
	end
	self.view:Flush()
	if add_score > 0 then
		local str = string.format(Language.GuildBattleRanked.ScoreAdd, add_score)
		TipWGCtrl.Instance:ShowNumberMsg(str, 0.1, VECTOR2_POS, nil, nil, true)
	end
	-- self.top_view:Flush()
	self:UpdataGuildBattleRankedBossInfo(protocol.boss_state_info)
end

function GuildBattleRankedWGCtrl:OnGuildBattleSceneGlobalInfo(protocol)
	self.data:SetGuildBattleSceneGlobalInfo(protocol)
	self.view:Flush(0,"lingshi_value_change",{"lingshi_value_change"})
	self.top_view:Flush()
	if protocol.notify_reason == 1 and protocol.delay_kickout_timestamp > 0 then
		local timr = TimeWGCtrl.Instance:GetServerTime()
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.delay_kickout_timestamp)
		FuBenWGCtrl.Instance:SetOutFbTime(protocol.delay_kickout_timestamp)
		self.is_end = true
	end
end

function GuildBattleRankedWGCtrl:ClearWorshipData()
	self:SetWorshipPos(nil, nil, nil)
end

function GuildBattleRankedWGCtrl:SetWorshipPos(x,y,range)
	self.pos_x = x
	self.pos_y = y
	self.range = range
end

function GuildBattleRankedWGCtrl:GetWorshipPos()
	return self.pos_x, self.pos_y, self.range

end

function GuildBattleRankedWGCtrl:SetIsEnd(is_end)
	self.is_end = is_end
end
function GuildBattleRankedWGCtrl:GetIsEnd()
	return self.is_end
end

function GuildBattleRankedWGCtrl:SendGuildBattleBuyMachineReq(machine_type)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildBattleBuyMachineReq)
	protocol.machine_type = machine_type
	protocol:EncodeAndSend()
end

-- function GuildBattleRankedWGCtrl:IsShowCanacelChangeBtn(is_show)
-- 	if self.view:IsOpen()then
-- 		self.view:ShowCancleBtn(is_show)
-- 	end
-- end
function GuildBattleRankedWGCtrl:OpenGuildBattleRankedView()
	if not self.view:IsOpen()then
		self.view:Open()
	end
end

function GuildBattleRankedWGCtrl:OpenGuildBattleRankedEndView(data_list,is_win)
	--胜利失败界面的弹窗
	if self.end_view and is_win ~= -1 then
		GuajiWGCtrl.Instance:StopGuaji()
		-- self.end_view:SetDataList(data_list, is_win)
		self.end_view:Open()
	end
end

function GuildBattleRankedWGCtrl:CloseGuildBattleRankedView()
	if self.view:IsOpen()then
		self.view:Close()
	end
end

function GuildBattleRankedWGCtrl:OpenGuildBattleTopView()
	self.top_view:Open()
end

function GuildBattleRankedWGCtrl:CloseGuildBattleTopView()
	self.top_view:Close()
end

function GuildBattleRankedWGCtrl:OpenGuildChangeView()
	self.guild_change_view:Open()
end

function GuildBattleRankedWGCtrl:CloseGuildChangeView()
	if self.guild_change_view:IsOpen() then
		self.guild_change_view:Close()
	end
end

function GuildBattleRankedWGCtrl:RoleAttrChange(attr_name,value)
	if attr_name == "level" then
		self:InitGuildBattleCallBack()
	end
end

function GuildBattleRankedWGCtrl:InitGuildBattleCallBack()
	--仙盟争霸模型服务端下发数据  为了显示开服前3天的预告
	--不在需要显示开服前3天的预告
	-- local time = self:CheckIsCanOpenActivity()
	-- if time <= 0 then return end
	-- local server_time = TimeWGCtrl.Instance:GetServerTime()
	-- local protocol = {is_broadcast = 1, activity_type = ACTIVITY_TYPE.XIANMENGZHAN,  next_status_switch_time = server_time + time, param_1 = 0, open_type = 0, param_2 = 0, msg_type = 9303, status = 1}
	-- ActivityWGData.Instance:SetActivityStatus(protocol.activity_type, protocol.status, protocol.next_status_switch_time, protocol.param_1, protocol.param_2, protocol.open_type)
	-- MainuiWGCtrl.Instance.view:ActivityChangeCallBack(ACTIVITY_TYPE.XIANMENGZHAN, 1, server_time + time, 0)
	
end


function GuildBattleRankedWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_GUILD_INVITE and status == ACTIVITY_STATUS.CLOSE then
		self:InitGuildBattleCallBack()
	elseif activity_type == ACTIVITY_TYPE.KF_XIANMENGZHAN then
		local info = GuildBattleRankedWGData.Instance:GetGuildBattleEndKeepWinInfo()
		if info then
			local reward_info = GuildBattleRankedWGData.Instance:GetGuildRoleOffsetCfg(info.is_shut_down, info.keep_win_times)
			local fight_state = GuildWGData.Instance:GetGuildBattleFightState()
			if reward_info and fight_state == GUILD_BATTLE_STATE_TYPE.GUILD_BATTLE_STATE_TYPE_SECOND_FIGHT_END then
				GuildWGCtrl.Instance:OpenLisnShengPasnel()
			end
		end
		if status == ACTIVITY_STATUS.OPEN then
			local end_data = GuildBattleRankedWGData.Instance:GetGuildBattleEndData()
			local time = end_data and end_data.delay_kickout_timestamp or next_time
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(time)
		end
		self.view:Flush(0,"activity",{"activity"})
	end
end

function GuildBattleRankedWGCtrl:CheckDingJiIsCloseActivity()
	local guild_battle_limit_level,limit_day = GuildBattleRankedWGData.Instance:GetActivityLimitLevel()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local role_level =  RoleWGData.Instance:GetRoleLevel()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local time_table = os.date("*t", server_time)
	local start_hour,start_min,during_time = GuildBattleRankedWGData.Instance:GetGuildBattleOpenTime()
	local has_pass_time = time_table.hour * 3600 + time_table.min * 60 + time_table.sec
	local end_time1 = 0
	local dingji_close_time = GuildBattleRankedWGData.Instance:GetDingJiCloseTime()
	if role_level >= guild_battle_limit_level and open_day <= limit_day and server_time >= dingji_close_time then
	-- if role_level >= guild_battle_limit_level and open_day <= limit_day then
		end_time1 = start_hour * 3600 - has_pass_time + start_min * 60
	end
	
	return end_time1 > 0 and end_time1 or 0 
end

function GuildBattleRankedWGCtrl:CheckIsCanOpenActivity()
	local guild_battle_limit_level,limit_day = GuildBattleRankedWGData.Instance:GetActivityLimitLevel()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local role_level =  RoleWGData.Instance:GetRoleLevel()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local time_table = os.date("*t", server_time)
	local start_hour,start_min,during_time = GuildBattleRankedWGData.Instance:GetGuildBattleOpenTime()
	local has_pass_time = time_table.hour * 3600 + time_table.min * 60 + time_table.sec
	local all_time1 = limit_day*24*3600
	local end_time = 0
	local end_time1 = 0

	if role_level >= guild_battle_limit_level and open_day <= limit_day then
		if open_day == 1 then
			end_time1 = all_time1 - has_pass_time 
		elseif open_day == 2 then
			end_time1 = all_time1 - has_pass_time - 24*3600
		elseif open_day == 3 then
			end_time1 = start_hour * 3600 - has_pass_time + start_min * 60
		end
	end
	
	return end_time1 > 0 and end_time1 or 0 
end

function GuildBattleRankedWGCtrl:CheckZBActivity()
	local guild_battle_limit_level,limit_day = GuildBattleRankedWGData.Instance:GetActivityLimitLevel()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local all_time1 = limit_day*24*3600
	local time_table = os.date("*t", server_time)
	local one_sta_hour, one_sta_min, _, two_sta_hour, two_sta_min, _, _, _, two_over_hour, two_over_min, one_over_hour, one_over_min= GuildBattleRankedWGData.Instance:GetGuildBattleOpenTime()
	local has_pass_time = time_table.hour * 3600 + time_table.min * 60 + time_table.sec
	local one_sta = 0
	local one_over = 0
	local two_sta = 0
	local two_over = 0

	if open_day == 1 then
		one_sta = all_time1 - has_pass_time 
		one_over = all_time1 - has_pass_time 
		two_sta = all_time1 - has_pass_time 
		two_over = all_time1 - has_pass_time 
	elseif open_day == 2 then
		one_sta = all_time1 - has_pass_time - 24*3600
		one_over = all_time1 - has_pass_time - 24*3600
		two_sta = all_time1 - has_pass_time - 24*3600
		two_over = all_time1 - has_pass_time - 24*3600
	elseif open_day == 3 then
		one_sta = one_sta_hour * 3600 - has_pass_time + one_sta_min * 60
		one_over = one_over_hour * 3600 - has_pass_time + one_over_min * 60
		two_sta = two_sta_hour * 3600 - has_pass_time + two_sta_min * 60
		two_over = two_over_hour * 3600 - has_pass_time + two_over_min * 60
	end

	return one_sta, one_over, two_sta, two_over
end


function GuildBattleRankedWGCtrl:OnSCGuildBattleForwardInfo(protocol)
	GuildBattleRankedWGData.Instance:SetPreviousRewardFlag(protocol)
end
function GuildBattleRankedWGCtrl:OnSCGuildBattleBuffInfo( protocol )
	GuildBattleRankedWGData.Instance:SetSceneBuffInfo(protocol)
	-- self.view:Flush()
end
function GuildBattleRankedWGCtrl:OnSCGuildBattleTopInfo( protocol )
	GuildBattleRankedWGData.Instance:SetSceneTitleInfo(protocol)
	self:FlushGuildBattleTitle()
end
function GuildBattleRankedWGCtrl:SetMarkTitle(is_set,info)
	self.title_mark = is_set
	self.title_mark_info = info
end
function GuildBattleRankedWGCtrl:GetMarkTitle()
	return self.title_mark ,self.title_mark_info
end
function GuildBattleRankedWGCtrl:FlushGuildBattleTitle()
	if not self.title_mark then
		self.title_mark = true
		local main_role = Scene.Instance:GetMainRole()
		local vo = main_role and main_role.vo
		if vo then
			self.title_mark_info = vo.used_title_list
		end
	end
	local protocol = GuildBattleRankedWGData.Instance:GetSceneTitleInfo()
	local title_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").title
	local mvp_res = title_cfg[1].title_res
	local detory_res = title_cfg[2].title_res
	local role_list = Scene.Instance:GetRoleList()
	local main_role = Scene.Instance:GetMainRole()
	local main_vo = main_role and main_role.vo
	local main_uid = RoleWGData.Instance:InCrossGetOriginUid()
		main_role:SetAttr("used_title_list", {})
	if protocol[1].mvp_uid == main_uid or protocol[2].mvp_uid == main_uid then
		-- print_error("mvp",mvp_res)
		main_role:SetAttr("used_title_list", {mvp_res})
	elseif protocol[1].max_destroy_tower_uid == main_uid or protocol[2].max_destroy_tower_uid == main_uid then
--		print_error("max_destroy_tower_uid",mvp_res)
		main_role:SetAttr("used_title_list", {detory_res})
	end
	for k,v in pairs(role_list) do
		v:SetAttr("used_title_list", {})
	end
	for k,v in pairs(role_list) do
		if protocol[1].mvp_uid == v.vo.role_id or protocol[2].mvp_uid == v.vo.role_id then
			v:SetAttr("used_title_list", {mvp_res})
		elseif protocol[1].max_destroy_tower_uid == v.vo.role_id or protocol[2].max_destroy_tower_uid == v.vo.role_id then
			v:SetAttr("used_title_list", {detory_res})
		end
	end
end
function GuildBattleRankedWGCtrl:IsShowTitle(uid,role)
	local protocol = GuildBattleRankedWGData.Instance:GetSceneTitleInfo()
	if IsEmptyTable(protocol) then return end
	local title_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").title
	local mvp_res = title_cfg[1].title_res
	local detory_res = title_cfg[2].title_res
	if protocol[1].mvp_uid == uid or protocol[2].mvp_uid == uid then
		role:SetAttr("used_title_list", {mvp_res})

	elseif protocol[1].max_destroy_tower_uid == uid or protocol[2].max_destroy_tower_uid == uid then
		role:SetAttr("used_title_list", {detory_res})
	end
end

function GuildBattleRankedWGCtrl:OnSCGuildBattleRankRewardInfo(protocol)
	self.data:SetGuildRankPaiMingInfo(protocol)
end
function GuildBattleRankedWGCtrl:OnSCGuildBattleEndKeepWinInfo(protocol)
	self.data:SetGuildBattleEndKeepWinInfo(protocol)
end


function GuildBattleRankedWGCtrl:OnSCGuildBattleKillInfo(protocol)
	--print_error("击杀boss",protocol)
	local info = protocol.kill_info_data
	if info.monster_id == 0 then
		AvatarManager.Instance:SetAvatarKey(info.killer_uid, protocol.killer_avatar_key_big, protocol.killer_avatar_key_small)
		AvatarManager.Instance:SetAvatarKey(info.be_killer_uid, protocol.be_killer_avatar_key_big, protocol.be_killer_avatar_key_small)
	end
	local t = {}
	t.broadcast_type = -1
	t.killer_uid = info.killer_uid
	t.killer_name = info.killer_role_name
    t.killer_sex = info.killer_sex
    t.killer_prof = info.killer_prof
	t.killer_is_red_side =  info.killer_side == 1 and 1 or 2
	t.target_uid = info.be_killer_uid
	t.target_name = info.be_killer_role_name
    t.target_sex = info.be_killer_sex
    t.target_prof = info.be_killer_prof
	t.target_is_red_side = info.killer_side == 1 and 2 or 1
	local kill_num = 1
	t.param = info.monster_id > 0 and -1 or kill_num
	t.monster_id = info.monster_id
	TipWGCtrl.Instance:AddZCRuneMsgCenter(t)
	-- if info.monster_id > 0 or kill_num <= 1 then
	-- 	TipWGCtrl.Instance:AddZCRuneMsgCenter(t)
	-- end
end

function GuildBattleRankedWGCtrl:TestGuildBattleContinueKillMSG(cw_type,kill_count)
	-- GuildBattleRankedWGCtrl.Instance:TestGuildBattleContinueKillMSG(cw_type)
	local main_role = Scene.Instance:GetMainRole()
	local vo = main_role and main_role.vo
	local data = {}
	data.chuangwen_type = cw_type or 1
	data.kill_count = kill_count or 3
	data.is_news = 1
	data.kill_sex = 1
	data.be_killer_sex = 1
	data.kill_uid = vo.role_id
	data.be_kill_uid = vo.role_id
	data.side = 1
	data.killer_avatar_key_big = 1111111111
	data.killer_avatar_key_small = 1111111111
	data.be_killer_avatar_key_big = 1111111111
	data.be_killer_avatar_key_small = 1111111111
	data.kill_name = "name 11"
	data.be_kill_name = "name 222"
	self:OnSCGuildBattleContinueKillMSG(data)
end

function GuildBattleRankedWGCtrl:OnSCGuildBattleContinueKillMSGPotocol(protocol)
	self:OnSCGuildBattleContinueKillMSG(protocol, "xmz")
end

-- 天神3v3
function GuildBattleRankedWGCtrl:OnSCTianShen3v3KillNews(protocol)
	self:OnSCGuildBattleContinueKillMSG(protocol, "tianshen_3v3")
end

-- 天下第一
function GuildBattleRankedWGCtrl:OnSCTianXiaDiYiContinueKillInfo(protocol)
	self:OnSCGuildBattleContinueKillMSG(protocol, "worlds_no1")
end

function GuildBattleRankedWGCtrl:OnSCGuildBattleContinueKillMSG(protocol, param)
	AvatarManager.Instance:SetAvatarKey(protocol.kill_uid, protocol.killer_avatar_key_big, protocol.killer_avatar_key_small)
	AvatarManager.Instance:SetAvatarKey(protocol.be_kill_uid, protocol.be_killer_avatar_key_big, protocol.be_killer_avatar_key_small)
	
	local cfg = nil
	if param == "tianshen_3v3" then
	 	cfg = TianShen3v3WGData.Instance:GetChuanWenCfg(protocol.chuangwen_type, protocol.kill_count)
	elseif param == "worlds_no1" then
	 	cfg = WorldsNO1WGData.Instance:GetChuanWenCfg(protocol.chuangwen_type, protocol.kill_count)
	else
		cfg = GuildBattleRankedWGData.Instance:GetContinueKillCfg(protocol.chuangwen_type,protocol.kill_count)
	end

	local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
    local kill_name = protocol.kill_name
    local kill_name_list = Split(protocol.kill_name, "_")
    if is_cross_server_stage and not IsEmptyTable(kill_name_list) then
        kill_name = kill_name_list[1]
    end
	
    local be_kill_name = protocol.be_kill_name
    local be_kill_name_list = Split(protocol.be_kill_name, "_")
    if is_cross_server_stage and not IsEmptyTable(be_kill_name_list) then
        be_kill_name = be_kill_name_list[1]
    end

	local str = ""
	if cfg and cfg.type == 1 and (cfg.is_news == nil or cfg.is_news == 0) then
		str = string.format(cfg.kill_desc,kill_name,protocol.kill_count)
		TipWGCtrl.Instance:ShowZCRuneMsg(str)
	elseif cfg and cfg.type == 2 and (cfg.is_news == nil or cfg.is_news == 0) then
		local t = {}
		t.killer_uid = protocol.kill_uid
		t.killer_name = kill_name
        t.killer_sex = protocol.kill_sex
        t.killer_prof = protocol.kill_prof
		t.killer_is_red_side =  protocol.side == 1 and 1 or 2
		t.target_uid = protocol.be_kill_uid
		t.target_name = be_kill_name
        t.target_sex = protocol.be_killer_sex
        t.target_prof = protocol.be_killer_prof
		t.target_is_red_side = protocol.side == 1 and 2 or 1
		t.param = protocol.kill_count
		t.kill_msg = Language.ZCResult.KillNumStr[cfg.kill_count]
		t.killer_tianshen_index = protocol.killer_tianshen_index
		t.be_killer_tianshen_index = protocol.be_killer_tianshen_index
		TipWGCtrl.Instance:AddZCRuneMsgCenter(t)
		str = string.format(cfg.kill_desc, kill_name)
	elseif protocol.chuangwen_type == 3 then
		local t = {}
		t.killer_uid = protocol.kill_uid
		t.killer_name = kill_name
		t.killer_sex = protocol.kill_sex
		t.killer_is_red_side =  protocol.side == 1 and 1 or 2
		t.killer_prof = protocol.kill_prof
		t.target_uid = protocol.be_kill_uid
		t.target_name = be_kill_name
		t.target_sex = protocol.be_killer_sex
		t.target_is_red_side = protocol.side == 1 and 2 or 1
		t.target_prof = protocol.be_killer_prof
		t.killer_tianshen_index = protocol.killer_tianshen_index
		t.be_killer_tianshen_index = protocol.be_killer_tianshen_index
		t.param = 11
		t.kill_msg = Language.ZCResult.ShutDown
		TipWGCtrl.Instance:AddZCRuneMsgCenter(t)
	end
	
	if str ~= "" then
		local msg_info = ChatWGData.CreateMsgInfo()
		msg_info.from_uid = 0
		msg_info.username = ""
		msg_info.sex = 0
		msg_info.camp = 0
		msg_info.prof = 0
		msg_info.authority_type = 0
		msg_info.tuhaojin_color = 0
		msg_info.level = 0
		msg_info.vip_level = 0
		msg_info.msg_reason = CHAT_MSG_RESSON.NORMAL
		msg_info.channel_type = CHANNEL_TYPE.CHUAN_WEN
		msg_info.content = str
		msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
		msg_info.is_add_team = false
		msg_info.title_text = Language.ChannelColor2[10]
		ChatWGCtrl.Instance:AddChannelMsg(msg_info, true)
		GlobalEventSystem:Fire(MainUIEventType.CHAT_CHANGE)
	end
end

function GuildBattleRankedWGCtrl:SendCSGuildBattleOPReq(opera_type,param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGuildBattleOPReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function GuildBattleRankedWGCtrl:OnSCCrossGuildBattleHusongRoleInfo(protocol)
	self.data:SetSCCrossGuildBattleHusongRoleInfo(protocol)
	self.view:Flush(0,"che",{"che"})
end

function GuildBattleRankedWGCtrl:OnSCCrossGuildBattleLingshiInfo(protocol)
	self.data:SetSCCrossGuildBattleLingshiInfo(protocol)
	self.view:Flush(0,"stone",{"stone"})
	GlobalEventSystem:Fire(Guill_XMZ_FIGHT_STATE.LINGSHI_CHANGE)
end

function GuildBattleRankedWGCtrl:SetGuildBattleEndRankInfo(protocol)
	self.data:SetGuildBattleEndRankInfo(protocol)
end

local GuildSideColor = {
	[0] = {out_line = "#264294",gradient1 = "#6debdb",gradient2 = "#ffffff"},
	[1] = {out_line = "#960e0e",gradient1 = "#ffa093",gradient2 = "#ffffff"}
}

local DefColor = {out_line = "#BB5726",gradient1 = "#F9F535",gradient2 = "#EDFDDE"}

function GuildBattleRankedWGCtrl:OnSCCrossGuildBattleNotify(protocol)  -- param_0是队伍标志      param_1是采集物id
	local info = {}
	local reason_type = protocol.reason_type
	local side = protocol.param_0
	info.text_list = {}
	info.img_list = {}
	local side_bundel,side_asset = ResPath.GetMainUIIcon(side == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.BLUETEAM and "a2_zjm_lf"  or "a2_zjm_hf")
	--策划要求屏蔽旧传闻
	--info.need_color = {}
	--local side = role_info_list and role_info_list.side or 0
	-- if reason_type == GuildBattleRankedWGData.ChuangWenType.CaiJi then
	-- 	-- local gather_str = Language.GuildBattleRanked.LingShiName[protocol.param_1] or ""
	-- 	-- info.text_list[1] = Language.GuildBattleRanked.GuildSideList[side]
	-- 	-- info.text_list[2] = gather_str
	-- 	-- info.need_color[1] = GuildSideColor[side]
	-- 	-- info.need_color[2] = DefColor
	-- 	-- for i=1,2 do
	-- 	-- 	local img_path = "x_changwenzhi" .. reason_type .. "_" .. i  
	-- 	-- 	local img_b,img_a = ResPath.GetXMZNewImg(img_path)
	-- 	-- 	info.img_list[i] = {bundle = img_b,asset = img_a} --x_changwenzhi1_1   x_changwenzhi1_2
	-- 	-- end
	-- elseif reason_type == GuildBattleRankedWGData.ChuangWenType.DiaoLuo then
	-- 	-- local gather_str = Language.GuildBattleRanked.LingShiName[protocol.param_1] or ""
	-- 	-- info.text_list[1] = Language.GuildBattleRanked.GuildSideList[side]
	-- 	-- info.text_list[2] = gather_str
	-- 	-- info.need_color[1] = GuildSideColor[side]
	-- 	-- info.need_color[2] = DefColor
	-- 	-- for i=1,2 do
	-- 	-- 	local img_path = "x_changwenzhi" .. reason_type .. "_" .. i
	-- 	-- 	local img_b,img_a = ResPath.GetXMZNewImg(img_path)
	-- 	-- 	info.img_list[i] = {bundle = img_b,asset = img_a}
	-- 	-- end
	-- elseif reason_type == GuildBattleRankedWGData.ChuangWenType.ErrorGive then
	-- 	-- local other_side = side == 0 and 1 or 0
	-- 	-- info.text_list[1] = Language.GuildBattleRanked.GuildSideList[side]
	-- 	-- info.text_list[2] = protocol.name
	-- 	-- info.text_list[3] = Language.GuildBattleRanked.GuildSideList[other_side]
	-- 	-- info.text_list[4] = Language.GuildBattleRanked.GuildSideList[other_side]
	-- 	-- info.text_list[5] = "+" .. protocol.param_1
	-- 	-- info.need_color[1] = GuildSideColor[side]
	-- 	-- info.need_color[2] = DefColor
	-- 	-- info.need_color[3] = GuildSideColor[other_side]
	-- 	-- info.need_color[4] = GuildSideColor[other_side]
	-- 	-- info.need_color[5] = DefColor
	-- 	-- for i=1,4 do
	-- 	-- 	local img_path = "x_changwenzhi" .. reason_type .. "_" .. i
	-- 	-- 	local img_b,img_a = ResPath.GetXMZNewImg(img_path)
	-- 	-- 	info.img_list[i] = {bundle = img_b,asset = img_a}
	-- 	-- end
	-- else
	if reason_type == GuildBattleRankedWGData.ChuangWenType.GUILD_BATTLE_NOTIFY_4 then
		local img_path = "a2_guild_battle_notice" .. reason_type .. "_" .. 1
		local img_b,img_a = ResPath.GetXMZNewImg(img_path)
		info.img_list[1] = {bundle = img_b,asset = img_a}
	elseif reason_type == GuildBattleRankedWGData.ChuangWenType.GUILD_BATTLE_NOTIFY_5 then
		local img_path = "a2_guild_battle_notice" .. reason_type .. "_" .. 1
		local img_b,img_a = ResPath.GetXMZNewImg(img_path)
		info.img_list[1] = {bundle = img_b,asset = img_a}
	elseif reason_type == GuildBattleRankedWGData.ChuangWenType.GUILD_BATTLE_NOTIFY_6 then
		info.img_list[1] =  {bundle = side_bundel,asset = side_asset}
		info.text_list[2] = protocol.param_1
		for i=2,3 do
			local img_path = "a2_guild_battle_notice" .. reason_type .. "_" .. i
			local img_b,img_a = ResPath.GetXMZNewImg(img_path)
			info.img_list[i] = {bundle = img_b,asset = img_a}
		end
	elseif reason_type == GuildBattleRankedWGData.ChuangWenType.GUILD_BOSS_WAIT_TO_FLUSH then
		info.img_list[1] =  {bundle = side_bundel,asset = side_asset}
		local img_path = "a2_guild_battle_notice" .. reason_type .. "_" .. 1
		local img_b,img_a = ResPath.GetXMZNewImg(img_path)
		info.img_list[2] = {bundle = img_b,asset = img_a}
	elseif reason_type == GuildBattleRankedWGData.ChuangWenType.GUILD_BOSS_IS_FLUSH then
		info.img_list[1] =  {bundle = side_bundel,asset = side_asset}
		local other_side_bound, other_bound_asset =  ResPath.GetMainUIIcon(side == GuildBattleRankedWGData.GUILD_BATTlE_CAMP.BLUETEAM and "a2_zjm_hf"  or "a2_zjm_lf")
		info.img_list[3] =  {bundle = other_side_bound,asset = other_bound_asset}

		for i = 1, 2 do
			local img_path = "a2_guild_battle_notice" .. reason_type .. "_" .. i
			local img_b,img_a = ResPath.GetXMZNewImg(img_path)
			info.img_list[2 * i] = {bundle = img_b,asset = img_a}
		end

	end

	self.view:ShowChuangWenPanel(info)
	--TipWGCtrl.Instance:ShowImgSystemMsg(info)
end

--变身采集车 点普通技能 如果没灵石就去采集距离最近的灵石， 如果有灵石就去npc提交灵石
function GuildBattleRankedWGCtrl:XMZGoNpcSkill()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local is_standy = act_info and act_info.status == ACTIVITY_STATUS.STANDY
	local xiusai_state = GuildWGData.Instance:GetIsPrepareRestTime()
	local not_guaji = is_standy or xiusai_state
	if not_guaji then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.ActNotOpenGuaji)
		return
	end

	local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
	local gather_id = role_info_list.gather_id
	local scene_id = Scene.Instance:GetSceneId()

	if gather_id and gather_id <= 0 then
		local lingshi_list = GuildBattleRankedWGData.Instance:GetLingShiList()
		local not_lingshi = IsEmptyTable(lingshi_list)
		local side = role_info_list and role_info_list.side or 0
		local pos_x,pos_y =  GuildBattleRankedWGData.Instance:GetGuildGuaJiPos(side)
		local x, y = Scene.Instance:GetMainRole():GetLogicPos()

		if not_lingshi then
			if x >= pos_x - 3 and  x <= pos_x + 3 and y >= pos_y - 3 and  y <= pos_y + 3 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildBattleRanked.LingShiListFlush)
				return
			else
				GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
				-- GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
				-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				-- end)
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y,3)
			end
		else
			local min_dis = 9999999
			local main_role = Scene.Instance:GetMainRole()
			local stone_data = {}
			for k,v in pairs(lingshi_list) do
				local dis = main_role:GetLogicDistance(v.pos, false)
				if min_dis > dis then
					min_dis = dis
					stone_data = v
				end
			end
			if not IsEmptyTable(stone_data) then
				GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
				MoveCache.SetEndType(MoveEndType.GatherById)
				MoveCache.param1 = stone_data.gather_id
				-- GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
				-- 	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				-- end)
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
				GuajiWGCtrl.Instance:MoveToPos(scene_id, stone_data.pos.x, stone_data.pos.y,3)		
			end
		end

		return
	end

	local side = role_info_list.side
	local pos_x,pos_y =  GuildBattleRankedWGData.Instance:GetGuildChangePos(side)
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	if x >= pos_x - 3 and  x <= pos_x + 3 and y >= pos_y - 3 and  y <= pos_y + 3 then
		local npc_id = GuildBattleRankedWGData.Instance:GetGuildNpcId(side)
		ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = npc_id})
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	else
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(function ()
			local npc_id = GuildBattleRankedWGData.Instance:GetGuildNpcId(side)
			ViewManager.Instance:Open(GuideModuleName.TaskDialog, 0, "all", {npc_id = npc_id})
			GuajiWGCtrl.Instance:StopGuaji()
		end)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		GuajiWGCtrl.Instance:MoveToPos(scene_id, pos_x, pos_y,3)
	end
end

function GuildBattleRankedWGCtrl:ShowGetLingShiVlaue(add_value)
	-- GuildBattleRankedWGCtrl.Instance:ShowGetLingShiVlaue(50)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true,false,true)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	if self.view and self.view:IsOpen() then
		self.view:Flush(0,"add_lingshi_value",{add_lingshi_value = add_value})
	end
end

function GuildBattleRankedWGCtrl:FlushGuildBattleRankedBossInfo()
	if self.view:IsOpen() then
		self.view:Flush(0,"boss_info")
	end
end

function GuildBattleRankedWGCtrl:UpdataGuildBattleRankedBossInfo(boss_state_info)
	local is_state_change, notice_boss_side = self.data:UpdateGuildBossInfo(boss_state_info)
	if notice_boss_side ~= -1 then
		local protocol = {	
			reason_type = GuildBattleRankedWGData.ChuangWenType.GUILD_BOSS_IS_FLUSH,
		    param_0 = notice_boss_side,
		}
		self:OnSCCrossGuildBattleNotify(protocol)
	end
	if is_state_change then
		self:FlushGuildBattleRankedBossInfo()
	end
end

function GuildBattleRankedWGCtrl:GetGuildBattleRankedView()
	return self.view
end