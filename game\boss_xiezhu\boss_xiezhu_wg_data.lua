
BossXiezhuWGData = BossXiezhuWGData or BaseClass()

BossXiezhuWGData.XiezhuState = {
	Zhaoji = 1,			--召集者  有boss次数且对boss造成伤害才能发起召集
	Xiezhu = 1,			--协助者  需要有协助次数才能进行协助
}

function BossXiezhuWGData:__init()
	if BossXiezhuWGData.Instance then
		error("[BossXiezhuWGData] Attempt to create singleton twice!")
		return
	end
	BossXiezhuWGData.Instance = self

	self.help_me_list = {}
	self.help_me_uid_list = {}
	self.status_info = {}      --当前的协助状态信息
	self.invoke_list_count = 0   --协助信息的数量
	self.invoke_list = {}
	self.show_invoke_list = {}
end

function BossXiezhuWGData:__delete()
	BossXiezhuWGData.Instance = nil
end

function BossXiezhuWGData:SetSCMonsterAssistHelpmeRole(protocol)
	self.help_me_list = protocol.help_me_list
	self.help_me_uid_list = protocol.help_me_uid_list
	self.monster_key = protocol.monster_key
	self.monster_id = protocol.monster_id
end

-- XIEZHU_REASON_TYPE =
-- {
-- 	REASON_TYPE_ADD = 0,				-- 增加召集信息 p0(uid)
-- 	REASON_TYPE_REMOVE_ROLE = 1,		-- 移除召集信息 p0(uid)
-- 	REASON_TYPE_REMOVE_MONSTER = 2,		-- 移除BOSS信息 
-- 	REASON_TYPE_DISCONNECT = 3,			-- 协助信息全部断开
--  REASON_TYPE_CHANGE = 4,				-- 召集变更
-- }
function BossXiezhuWGData:SetSCMonsterAssistInvokeChange(protocol)
    --print_error("Boss协助协议信息 reason_type",protocol.reason_type, protocol)
	local node = protocol.node
	local reason_type = protocol.reason_type
	if reason_type == XIEZHU_REASON_TYPE.REASON_TYPE_ADD then
		self.invoke_list[node.uid] = node
		self.invoke_list_count = self.invoke_list_count + 1
		self:SetAddNewInvokeFlag(true)
	elseif reason_type == XIEZHU_REASON_TYPE.REASON_TYPE_REMOVE_ROLE then
		local uid = protocol.param_0
		self.invoke_list[uid] = nil
		self.invoke_list_count = self.invoke_list_count - 1
	elseif reason_type == XIEZHU_REASON_TYPE.REASON_TYPE_REMOVE_MONSTER then
		local monster_id = node.monster_id
		local monster_key = node.monster_key
		local scene_id = node.scene_id
		local scene_key = node.scene_key
		for k,v in pairs(self.invoke_list) do
			if v.monster_id == monster_id and v.monster_key == monster_key and v.scene_id == scene_id and v.scene_key == scene_key then
				self.invoke_list[k] = nil
				self.invoke_list_count = self.invoke_list_count - 1
			end
		end
	elseif reason_type == XIEZHU_REASON_TYPE.REASON_TYPE_DISCONNECT then
		self.invoke_list = {}
		self.status_info = {}
		self.invoke_list_count = 0
		self:ClearShowInvokeList()
	elseif reason_type == XIEZHU_REASON_TYPE.REASON_TYPE_CHANGE then
		self.invoke_list[node.uid] = node
	end
	self:UpdateShowInvokeList()
end

function BossXiezhuWGData:SetSCMonsterAssistInvokeListInfo(protocol)
    self.invoke_list = protocol.invoke_list
    --print_error("Boss协助协议信息 ",self.invoke_list_count, protocol.invoke_list)
	self.invoke_list_count = protocol.invoke_list_count
	self:UpdateShowInvokeList()
end

-- 协助状态
-- ASSIST_STATUS =
-- {
-- 	ASSIST_STATUS_NOTHING = 0,				-- 正常状态
-- 	ASSIST_STATUS_INVOKE = 1,				-- 状态召集
-- 	ASSIST_STATUS_HELP_OTHER = 2,			-- 协助状态
-- }
function BossXiezhuWGData:SetSCMonsterAssistRoleStatus(protocol)
	self.status_info.status = protocol.status
	self.status_info.role_node = protocol.role_node
	self:UpdateShowInvokeList()
	if protocol.status == ASSIST_STATUS.ASSIST_STATUS_NOTHING or protocol.status == ASSIST_STATUS.ASSIST_STATUS_INVOKE then
		BossXiezhuWGData.Instance:SetGoXiezhuRoleId(0)
	else
		self:SetGoXiezhuRoleId(protocol.role_node.uid)
	end
end

function BossXiezhuWGData:SetSCMonsterAssistRoleHurt(protocol)
	self.xiezhu_hurt = protocol.hurt
end

--协助者对boss的伤害
function BossXiezhuWGData:GetMyXiezhuHurt()
	return self.xiezhu_hurt or 0
end

function BossXiezhuWGData:ClearXiezhuHurt()
	self.xiezhu_hurt = 0
end

function BossXiezhuWGData:UpdateShowInvokeList()
	self.show_invoke_list = {}
	local target_info = self:GetTargetRoleNode()
	local status = self:GetXiezhuStatus()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	for k,v in pairs(self.invoke_list) do
		local boss_type = v.boss_type
		local scene_id = v.scene_id
		v.sort = 0
		if boss_type == ASSIST_BOSS_TYPE.ASSIST_BOSS_TYPE_CROSS_BOSS then 	--蛮荒神兽
			local boss_cfg = BossWGData.Instance:GetBossInfoByBossId(v.monster_id)
			local layer_cfg = BossWGData.Instance:GetKFNewGatherSceneCfgBySceneId(scene_id)
			if boss_cfg and role_level >= boss_cfg.need_role_level and role_id ~= v.uid and layer_cfg.level_limit and role_level >= layer_cfg.level_limit then
				if not IsEmptyTable(target_info) and target_info.uid == v.uid and status == ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER then
					v.sort = 1
				end
				table.insert(self.show_invoke_list,v)
			end
		end
	end
	table.sort(self.show_invoke_list,SortTools.KeyUpperSorter("sort"))
end

function BossXiezhuWGData:ClearShowInvokeList()
	self.show_invoke_list = {}
end

function BossXiezhuWGData:GetHelpMeList()
	return self.help_me_list
end

function BossXiezhuWGData:ClearHelpMeList()
	self.help_me_list = {}
	self.help_me_uid_list = {}
end

function BossXiezhuWGData:GetHasHelpMeUid(uid)
	return self.help_me_uid_list[uid] ~= nil
end

function BossXiezhuWGData:GetHelpMeUidList()
	return self.help_me_uid_list
end

function BossXiezhuWGData:GetAssistCount()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").other[1]
	local assist_count = other_cfg and other_cfg.assist_count or 1
	return assist_count
end

function BossXiezhuWGData:GetXiezhuReward()
	local boss_aissit_reward_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").boss_aissit_reward
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cfg = boss_aissit_reward_cfg[1]
	for i,v in ipairs(boss_aissit_reward_cfg) do
		if role_level >= v.role_level then
			cfg = v
		end
	end
	local drop_item_list = cfg and cfg.drop_item_list
	local reward_data = {}
	if not IsEmptyTable(drop_item_list) then
		for i=0,#drop_item_list do
			table.insert(reward_data,drop_item_list[i])
		end
	end
	return reward_data
end

function BossXiezhuWGData:GetThankReward()
	local boss_aissit_reward_cfg = ConfigManager.Instance:GetAutoConfig("other_config_auto").boss_aissit_reward
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cfg = boss_aissit_reward_cfg[1]
	for i,v in ipairs(boss_aissit_reward_cfg) do
		if role_level >= v.role_level then
			cfg = v
		end
	end
	local assist_reward = cfg and cfg.assist_reward
	local reward_data = {}
	if not IsEmptyTable(assist_reward) then
		for i=0,#assist_reward do
			table.insert(reward_data,assist_reward[i])
		end
	end
	return reward_data
end

function BossXiezhuWGData:GetShowInvokeList()
	return self.show_invoke_list
end

function BossXiezhuWGData:GetIsEmptyInvokeList()
	return IsEmptyTable(self.show_invoke_list)
end

function BossXiezhuWGData:RemoveInvokeRoleInfo(uid,index)
	if self.invoke_list[uid] then
		self.invoke_list[uid] = nil
		self.invoke_list_count = self.invoke_list_count - 1
		if index then
			table.remove(self.show_invoke_list,index)
		else
			self:UpdateShowInvokeList()
		end
	end
end

function BossXiezhuWGData:GetInvokeInfoByRoleId(role_id)
	return self.invoke_list[role_id]
end

function BossXiezhuWGData:GetXiezhuStatus()
	return self.status_info.status or ASSIST_STATUS.ASSIST_STATUS_NOTHING
end

function BossXiezhuWGData:GetTargetRoleNode()
	return self.status_info.role_node
end

function BossXiezhuWGData:SetGoXiezhuRoleId(role_id)
	self.go_xiezhu_role_id = role_id
end

function BossXiezhuWGData:GetGoXiezhuRoleId()
	return self.go_xiezhu_role_id or 0
end

function BossXiezhuWGData:IsGotoXiezhu()
	local status = self:GetXiezhuStatus()
	local xiezhu_role_id = self:GetGoXiezhuRoleId()
	return xiezhu_role_id > 0 and status ==  ASSIST_STATUS.ASSIST_STATUS_HELP_OTHER
end

function BossXiezhuWGData:IsXiezhuCurBoss(target_obj)
	local role_node = self:GetTargetRoleNode()
	if not IsEmptyTable(target_obj) and target_obj:IsBoss() and not IsEmptyTable(role_node) then
		local target_vo = target_obj:GetVo()
		if target_vo.monster_id and target_vo.monster_id == role_node.monster_id 
			and target_vo.monster_key and target_vo.monster_key == role_node.monster_key then
			return true
		end
	end
	return false
end

function BossXiezhuWGData:GetSendTimer()
	return self.send_timer or 0
end

function BossXiezhuWGData:SetSendTimer(timer)
	self.send_timer = timer
end

function BossXiezhuWGData:GetAddNewInvokeFlag()
	return self.add_new_invoke_flag or false
end

function BossXiezhuWGData:SetAddNewInvokeFlag(flag)
	self.add_new_invoke_flag = flag
end

function BossXiezhuWGData:IsInXieZhuScene()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.KF_BOSS then
		return true
	end
	return false
end

function BossXiezhuWGData:GetPrintLog()
	local tab_str = "BossXiezhuWGData:GetPrintLog\n"
	tab_str = tab_str .. "协助状态:".."\n"
	
	if not IsEmptyTable(self.status_info) then
		for k,v in pairs(self.status_info) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end
	tab_str = tab_str .. "协助列表:".."\n"
	if not IsEmptyTable(self.show_invoke_list) then
		for k,v in pairs(self.show_invoke_list) do
			if type(v) == "table" then
				tab_str = tab_str .. k .. " = " ..  TableToChatStr(v,1) .. "\n"
			else
				tab_str = tab_str .. k .. " = " ..  v .. "  "
			end
		end
	end
	
	return tab_str
end