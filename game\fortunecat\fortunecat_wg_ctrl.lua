require("game/fortunecat/fortunecat_wg_data")
require("game/fortunecat/fortunecat_view")
require("game/fortunecat/fortunecat_draw_record_view")
require("game/fortunecat/fortunecat_target_view")

FortuneCatWGCtrl = FortuneCatWGCtrl or BaseClass(BaseWGCtrl)

function FortuneCatWGCtrl:__init()
	if FortuneCatWGCtrl.Instance then
        error("[FortuneCatWGCtrl]:Attempt to create singleton twice!")
	end
	FortuneCatWGCtrl.Instance = self

	self.data = FortuneCatWGData.New()
    self.view = FortuneCatView.New(GuideModuleName.FortuneCatView)
    self.draw_record_view = FortunecatDrawRecordView.New()
    self.fortunecat_target_view = FortunecatTargetView.New()

    self.create_role_event = GlobalEventSystem:Bind(LoginEventType.RECV_MAIN_ROLE_INFO, BindTool.Bind(self.ReqProtocol,self))
	self:RegisterAllProtocals()
end

function FortuneCatWGCtrl:__delete()
    FortuneCatWGCtrl.Instance = nil
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.draw_record_view then
		self.draw_record_view:DeleteMe()
		self.draw_record_view = nil
	end

    if self.fortunecat_target_view then
		self.fortunecat_target_view:DeleteMe()
		self.fortunecat_target_view = nil
	end
    

    if self.create_role_event then
		GlobalEventSystem:UnBind(self.create_role_event)
		self.create_role_event = nil
	end
end

function FortuneCatWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(SCOAFortuneCatPersonInfo, "OnSCOAFortuneCatPersonInfo")
    self:RegisterProtocol(SCOAFortuneCatRecordInfo, "OnSCOAFortuneCatRecordInfo")
    self:RegisterProtocol(SCOAFortuneCatRewardInfo, "OnSCOAFortuneCatRewardInfo")
end

function FortuneCatWGCtrl:ReqProtocol()
    FortuneCatWGCtrl.Instance:SendOperaReq(FortuneCatWGData.OA_FORTUNECAT_OPER.OA_FORTUNECAT_TYPE_SEND_PERSON_INFO)
end

function FortuneCatWGCtrl:OnSCOAFortuneCatPersonInfo(protocol)
    self.data:SetFortuneCatPersonData(protocol)
    if self.view:IsOpen() and self.view:IsLoaded()  then
        self.view:FlushBtnStates()
        self.view:FlushDetails()
    else
        local role_id = RoleWGData.Instance:InCrossGetOriginUid()
        local day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        if not self.draw_times then
            self.draw_times = protocol.person_remain_times
        end
        local value = PlayerPrefsUtil.GetString("fortunetcat_openview_flag")
        local set_value = role_id.."fortunetcat_openview_flag"..day
        if self.draw_times < protocol.person_remain_times and value ~= set_value then --每天第一次抽奖次数增加.打开界面
            self.view:Open()
            PlayerPrefsUtil.SetString("fortunetcat_openview_flag", set_value)
        else
            self.draw_times = protocol.person_remain_times
        end
    end

    RemindManager.Instance:Fire(RemindName.FortuneCatRemind)
end

function FortuneCatWGCtrl:OnSCOAFortuneCatRecordInfo(protocol)
    self.data:SetFortuneCatRecordData(protocol)
    if self:GetIsDotween() then
        self.view:SetDelayFlushRecord(true)
    else
        self:FlushView()
    end
end

function FortuneCatWGCtrl:OnSCOAFortuneCatRewardInfo(protocol)
    local item_id
    local type = protocol.consume_gold_type
    if type == FORTUNECAT_HUOBI_TYPE.CANGJING_SCORE then
        item_id = COMMON_CONSTS.VIRTUAL_ITEM_CANG_JIN_SCORE
    else
        item_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
    end

    local show_item_list = {}
    show_item_list[1] = {item_id = item_id, num = protocol.get_num}

    local is_skip = self.data:GetSkipAniFlag()
    local time = is_skip and 0 or 4.2
    if protocol.cur_index > 0 then
        self.data:SetFortuneCatRewardInfo(protocol)
        self.view:ReceiveProtocol()
        ReDelayCall(self, function()
            TipWGCtrl.Instance:ShowGetCommonReward(show_item_list)
        end, time, "FortuneCatReward")
    end
end

function FortuneCatWGCtrl:SendOperaReq(opera_type,param_1,param_2,param_3,param_4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
  	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT
  	protocol.opera_type = opera_type or 0
  	protocol.param_1 = param_1 or 0
  	protocol.param_2 = param_2 or 0
  	protocol.param_3 = param_3 or 0
  	protocol.param_4 = param_4 or 0
  	protocol:EncodeAndSend()
end

function FortuneCatWGCtrl:FlushView()
    if self.view:IsOpen() then
        self.view:Flush()
    end

    if self.draw_record_view:IsLoaded() and self.draw_record_view:IsOpen() then
		self.draw_record_view:Flush()
	end
end

function FortuneCatWGCtrl:GetIsDotween()
	return self.view:GetIsDotween()
end

function FortuneCatWGCtrl:ActivityChangeCallBack(status)
    if status == ACTIVITY_STATUS.CLOSE then
        PlayerPrefsUtil.SetString("fortunetcat_openview_flag", "")
    else
        RemindManager.Instance:Fire(RemindName.FortuneCatRemind)
    end
end

function FortuneCatWGCtrl:OpenTreasureRecordView(all_list, person_list)
	self.draw_record_view:SetData(all_list, person_list)
	if self.draw_record_view:IsLoaded() and self.draw_record_view:IsOpen() then
		self.draw_record_view:Flush()
	else
		self.draw_record_view:Open()
	end
end

function FortuneCatWGCtrl:OpenTargetView()
    if self.fortunecat_target_view then
        self.fortunecat_target_view:Open()
    end
end