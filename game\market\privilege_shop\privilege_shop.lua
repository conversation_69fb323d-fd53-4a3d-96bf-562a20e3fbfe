PrivilegeShopView = PrivilegeShopView or BaseClass(SafeBaseView)

local MAX_VISIABLE_ROW = 2 --最多可视区域行数
function PrivilegeShopView:__init(view_name)
    self.view_name = GuideModuleName.PrivilegeShop
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.default_index = TabIndex.season_privilege_shop
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/shop_ui_prefab", "layout_privilege_shop")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
    self:SetMaskBg()
end

function PrivilegeShopView:LoadCallBack()
    local bundle, asset = ResPath.GetCommonButton("a3_ty_hretutn")
	self.node_list["btn_close_window_img"].image:LoadSprite(bundle, asset, function()
		self.node_list["btn_close_window_img"].image:SetNativeSize()
	end)
	self.node_list.title_view_name.text.color = Str2C3b('#FCF8DD')

    --屏蔽特效.
    -- local game_obj_attach = self.node_list["RawImage_tongyong"].gameObject:GetOrAddComponent(typeof(Game.GameObjectAttach))
	-- if game_obj_attach then
	-- 	local bundle1, asset1 = ResPath.GetUIEffect("UI_jiaoyi_guang")
	-- 	game_obj_attach.BundleName = bundle1
	-- 	game_obj_attach.AssetName = asset1
	-- end

    XUI.AddClickEventListener(self.node_list["btn_recharge"], BindTool.Bind(self.OnGoBtnClick, self))
    XUI.AddClickEventListener(self.node_list["mystery_shop_btn"], BindTool.Bind(self.OnShopBtnClick, self))

    self.grid_list = AsyncBaseGrid.New()
    self.grid_list:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list.ph_grid_list,
            assetBundle = "uis/view/shop_ui_prefab", assetName = "cel_shop_item", itemRender = ShopItemRender})
    self.grid_list:SetStartZeroIndex(false)

    self.money_bar = MoneyBar.New()
    local bundle, asset = ResPath.GetWidgets("MoneyBar")
    local show_params = {
        show_gold = true, show_bind_gold = true,
        show_coin = true, show_silver_ticket = true,
    }
    self.money_bar:SetMoneyShowInfo(0, 0, show_params)
    self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    self.shop_type_list_view = AsyncListView.New(ShopTypeRender,self.node_list.shop_type_list)

    self.node_list.ph_grid_list.scroller.scrollerEndScrolled = BindTool.Bind(self.ShopScrollerEndScrolled, self)
end

function PrivilegeShopView:ShowIndexCallBack(index)
    self:FlushTypeLsit()

    ShopWGData.Instance:SetCurIndex(index)
    self:SendShopItemInfo(index)

    local bundle, asset = ResPath.GetRawImagesJPG("a3_jy_bg")
    self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)
end

function PrivilegeShopView:ReleaseCallBack()
    if self.grid_list then
        self.grid_list:DeleteMe()
        self.grid_list = nil
    end
    
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if nil ~= self.shop_type_list_view then
        self.shop_type_list_view:DeleteMe()
        self.shop_type_list_view = nil
    end
end

function PrivilegeShopView:CloseCallBack()
    ShopWGData.Instance:ClearIndex()
end

function PrivilegeShopView:FlushTypeLsit()
    local type_data = ShopWGData.Instance:GetPrivilegeShopBigType()
    for k,v in pairs(type_data) do
        v.select_call_back = BindTool.Bind(self.TypeCellClickCallBack,self)
    end

    self.shop_type_list_view:SetDataList(type_data, 0)
end

--tab页签的箭头显示.
function PrivilegeShopView:ShopScrollerEndScrolled()
	local val = self.node_list.ph_grid_list.scroll_rect.verticalNormalizedPosition
	local cell_row = self.grid_list:GetListViewNumbers()
	self.node_list.privilege_shop_more_img:SetActive(val ~= 0 and cell_row > MAX_VISIABLE_ROW and val > 0.1)
end

function PrivilegeShopView:TypeCellClickCallBack(data)
    if not IsEmptyTable(data) then
        ShopWGData.Instance:SetCurIndex(data.shop_type)
        self:ChangeToIndex(data.shop_type)
    end
end

function PrivilegeShopView:SendShopItemInfo(index)
    local shop_type = index
    ShopWGCtrl.Instance:SendReqShopItemInfo(shop_type)
end

function PrivilegeShopView:OnFlush(param_t, index)
    self:RefreshView(index)
    self:FlushBtnState(index)
end

function PrivilegeShopView:RefreshView(index)
    if not index then
        return
    end
    local data_list = ShopWGData.Instance:GetItemDataList(index, true)
    self.grid_list:SetDataList(data_list, 0)
    self.node_list["img_no"]:SetActive(0 == #data_list)
    if self.node_list["img_no"].gameObject.activeInHierarchy then
        local tab_cfg = ShopWGData.Instance:GetPageType(index)
        if tab_cfg then
            self.node_list["text_des"].text.text = string.format(Language.Shop.NoShopItem, tab_cfg.min_show_level)
        end
    end
end

function PrivilegeShopView:FlushBtnState(index)
    local active = false
    if index == TabIndex.season_privilege_shop then
        active = RechargeWGData.Instance:IsHasWeekCard()
    else
        local super_dragon_seal_data = LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL)
        active = super_dragon_seal_data.is_active ~= 0
    end

	local res = active and "a3_market_recharge" or "a3_market_jihuo"
	local bundle, asset = ResPath.GetMarketImg(res)
	self.node_list.btn_recharge_txt.image:LoadSprite(bundle, asset, function()
		self.node_list.btn_recharge_txt.image:SetNativeSize()
	end)
end

function PrivilegeShopView:OnGoBtnClick()
    if self.show_index == TabIndex.season_privilege_shop then
        ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_week_card)
    else
        ViewManager.Instance:Open(GuideModuleName.LongXiView, TabIndex.super_dragon_seal)
    end
end

function PrivilegeShopView:OnShopBtnClick()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Market, TabIndex.shop_limit)
end

------------------------------------商城列表------------------------
ShopTypeRender = ShopTypeRender or BaseClass(BaseRender)

function ShopTypeRender:LoadCallBack()
	self.node_list["click_area"].button:AddClickListener(BindTool.Bind(self.OnClickTypeCell,self))
end

function ShopTypeRender:OnFlush()
	if nil == self.data then
		return
	end

	local shop_title = self.data.type_data.shop_title
	self.node_list["shop_type_cell_text"].text.text = shop_title
	self.node_list["shop_type_cell_text_hl"].text.text = shop_title
	--self.node_list["shop_type_cell_icon"].image:LoadSprite(ResPath.GetF2ShopUi("s_shop_icon"..self.data.type_data.shop_type))
	local hl_index = ShopWGData.Instance:GetCurIndex()
	hl_index = hl_index >= TabIndex.season_privilege_shop and hl_index or TabIndex.season_privilege_shop
	self:FlushHL(hl_index)
end

function ShopTypeRender:FlushHL(index)
	if not self.data or not self.data.type_data then return end
	local shop_type = self.data.type_data.shop_type
	local select = shop_type == index

	self.node_list["shop_type_cell_bg"]:SetActive(not select)
	self.node_list["shop_type_cell_text"]:SetActive(not select)
	self.node_list["shop_type_cell_text_hl"]:SetActive(select)
	self.node_list["shop_type_cell_selected"]:SetActive(select)
end

function ShopTypeRender:OnClickTypeCell()
	if self.data and self.data.select_call_back then
		self.data.select_call_back(self.data.type_data)
	end
end