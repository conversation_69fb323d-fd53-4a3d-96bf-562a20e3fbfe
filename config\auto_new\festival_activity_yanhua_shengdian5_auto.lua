-- J-节日活动-墨染轩辕.xls
local item_table={
[1]={item_id=26198,num=10,is_bind=1},
[2]={item_id=26198,num=45,is_bind=1},
[3]={item_id=38053,num=1,is_bind=1},
[4]={item_id=38734,num=1,is_bind=1},
[5]={item_id=26459,num=1,is_bind=1},
[6]={item_id=26460,num=1,is_bind=1},
[7]={item_id=26461,num=1,is_bind=1},
[8]={item_id=26569,num=1,is_bind=1},
[9]={item_id=48441,num=1,is_bind=1},
[10]={item_id=44185,num=1,is_bind=1},
[11]={item_id=44184,num=1,is_bind=1},
[12]={item_id=44180,num=1,is_bind=1},
[13]={item_id=26193,num=1,is_bind=1},
[14]={item_id=26191,num=1,is_bind=1},
[15]={item_id=26377,num=1,is_bind=1},
[16]={item_id=26203,num=1,is_bind=1},
[17]={item_id=26380,num=1,is_bind=1},
[18]={item_id=26379,num=1,is_bind=1},
[19]={item_id=26376,num=1,is_bind=1},
[20]={item_id=26378,num=1,is_bind=1},
[21]={item_id=26443,num=1,is_bind=1},
[22]={item_id=26441,num=1,is_bind=1},
[23]={item_id=26442,num=1,is_bind=1},
[24]={item_id=26448,num=1,is_bind=1},
[25]={item_id=26438,num=1,is_bind=1},
[26]={item_id=26439,num=1,is_bind=1},
[27]={item_id=26440,num=1,is_bind=1},
[28]={item_id=26445,num=1,is_bind=1},
[29]={item_id=26446,num=1,is_bind=1},
[30]={item_id=26447,num=1,is_bind=1},
[31]={item_id=37224,num=1,is_bind=1},
[32]={item_id=37056,num=1,is_bind=1},
[33]={item_id=26345,num=1,is_bind=1},
[34]={item_id=26369,num=1,is_bind=1},
[35]={item_id=26368,num=1,is_bind=1},
[36]={item_id=26344,num=1,is_bind=1},
[37]={item_id=26367,num=1,is_bind=1},
[38]={item_id=38054,num=1,is_bind=1},
[39]={item_id=26198,num=1,is_bind=1},
[40]={item_id=26570,num=1,is_bind=1},
[41]={item_id=26174,num=5,is_bind=1},
}

return {
config_param={
{},
{start_server_day=6,end_server_day=9999,grade=1,}
},

config_param_meta_table_map={
},
grade={
[0]={grade=0,},
[1]={grade=1,consume=2,reward_unbind=2,rebate=2,}
},

grade_meta_table_map={
},
consume={
{},
{onekey_lotto_num=10,consume_count=400,yanhua_item=item_table[1],},
{onekey_lotto_num=50,consume_count=1800,yanhua_item=item_table[2],discount_text=9,},
{consume=2,},
{consume=2,},
{consume=2,}
},

consume_meta_table_map={
[5]=2,	-- depth:1
[6]=3,	-- depth:1
},
reward={
{reward_item=item_table[3],reward_type=1,player_guarantee=7501,guarantee_reward_limit=5,broadcast=1,reward_show=1,},
{reward_id=2,reward_item=item_table[4],player_guarantee=4001,},
{reward_id=3,reward_type=1,player_guarantee=2000,guarantee_reward_limit=10,broadcast=1,reward_show=1,},
{reward_id=4,reward_item=item_table[5],player_guarantee=3000,},
{reward_id=5,reward_item=item_table[6],},
{reward_id=6,reward_item=item_table[7],},
{reward_id=7,reward_item=item_table[8],player_guarantee=1000,},
{reward_id=8,reward_item=item_table[9],player_guarantee=400,},
{reward_id=9,reward_item=item_table[10],player_guarantee=200,guarantee_reward_limit=50,},
{reward_id=10,reward_item=item_table[11],player_guarantee=100,guarantee_reward_limit=150,},
{reward_id=11,reward_item=item_table[12],reward_type=2,reward_show=2,},
{reward_id=12,reward_item=item_table[13],guarantee_reward_limit=5,broadcast=1,},
{reward_id=13,reward_item=item_table[14],},
{reward_id=14,reward_item=item_table[15],reward_show=2,},
{reward_id=15,reward_item=item_table[16],},
{reward_id=16,reward_item=item_table[17],},
{reward_id=17,reward_item=item_table[18],},
{reward_id=18,reward_item=item_table[19],},
{reward_id=19,reward_item=item_table[20],},
{reward_id=20,reward_item=item_table[21],},
{reward_id=21,reward_item=item_table[22],},
{reward_id=22,reward_item=item_table[23],},
{reward_id=23,reward_item=item_table[24],},
{reward_id=24,reward_item=item_table[25],},
{reward_id=25,reward_item=item_table[26],},
{reward_id=26,reward_item=item_table[27],},
{reward_id=27,reward_item=item_table[28],},
{reward_id=28,reward_item=item_table[29],},
{reward_id=29,reward_item=item_table[30],reward_show=0,},
{reward=2,reward_item=item_table[31],player_guarantee=10001,guarantee_reward_limit=1,},
{reward_id=2,reward_item=item_table[32],player_guarantee=7501,},
{reward=2,},
{reward=2,player_guarantee=1500,},
{reward=2,},
{reward=2,},
{reward=2,player_guarantee=3000,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward_id=12,reward_item=item_table[13],guarantee_reward_limit=5,},
{reward=2,reward_id=13,reward_item=item_table[14],guarantee_reward_limit=5,},
{reward_id=14,reward_item=item_table[33],},
{reward=2,},
{reward=2,reward_item=item_table[34],},
{reward=2,reward_item=item_table[35],},
{reward=2,reward_item=item_table[36],},
{reward=2,reward_item=item_table[37],},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward_id=28,reward_item=item_table[29],},
{reward_id=29,reward_item=item_table[30],}
},

reward_meta_table_map={
[44]=15,	-- depth:1
[46]=17,	-- depth:1
[47]=18,	-- depth:1
[48]=19,	-- depth:1
[49]=20,	-- depth:1
[50]=21,	-- depth:1
[51]=22,	-- depth:1
[52]=23,	-- depth:1
[53]=24,	-- depth:1
[54]=25,	-- depth:1
[55]=26,	-- depth:1
[56]=27,	-- depth:1
[45]=16,	-- depth:1
[43]=45,	-- depth:2
[58]=45,	-- depth:2
[57]=45,	-- depth:2
[28]=29,	-- depth:1
[42]=14,	-- depth:1
[40]=11,	-- depth:1
[12]=11,	-- depth:1
[13]=12,	-- depth:2
[41]=40,	-- depth:2
[9]=1,	-- depth:1
[2]=3,	-- depth:1
[4]=3,	-- depth:1
[5]=4,	-- depth:2
[6]=4,	-- depth:2
[7]=3,	-- depth:1
[8]=1,	-- depth:1
[30]=1,	-- depth:1
[32]=3,	-- depth:1
[10]=1,	-- depth:1
[39]=10,	-- depth:2
[33]=4,	-- depth:2
[34]=5,	-- depth:3
[35]=6,	-- depth:3
[36]=7,	-- depth:2
[31]=30,	-- depth:2
[37]=8,	-- depth:2
[38]=9,	-- depth:2
},
rebate={
{},
{index=2,lotto_num=30,},
{index=3,lotto_num=50,reward_item={[0]=item_table[11]},},
{index=4,lotto_num=888,reward_item={[0]=item_table[38]},},
{rebate=2,},
{rebate=2,},
{rebate=2,},
{rebate=2,}
},

rebate_meta_table_map={
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
},
role_sp_guarantee={
{},
{sp_player_guarantee_id=2,reward_id=2,sp_guarantee_weight="1,2,0|3,3,2|4,4,80|5,5,20",},
{sp_player_guarantee_id=3,reward_id=3,sp_guarantee_weight="1,1,0|2,2,80|3,3,20|4,4,20|5,5,20",},
{sp_player_guarantee_id=4,reward_id=4,sp_guarantee_weight="1,1,0|2,2,20|3,3,80|4,5,20",},
{sp_player_guarantee_id=5,reward_id=5,sp_guarantee_weight="1,1,100|2,5,20",},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=2,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=3,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=4,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=5,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=6,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=7,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,},
{reward=8,}
},

role_sp_guarantee_meta_table_map={
[27]=2,	-- depth:1
[28]=3,	-- depth:1
[29]=4,	-- depth:1
[30]=5,	-- depth:1
[34]=29,	-- depth:2
[33]=28,	-- depth:2
[35]=30,	-- depth:2
[37]=27,	-- depth:2
[38]=33,	-- depth:3
[32]=37,	-- depth:3
[25]=35,	-- depth:3
[20]=25,	-- depth:4
[23]=38,	-- depth:4
[22]=32,	-- depth:4
[39]=34,	-- depth:3
[19]=39,	-- depth:4
[18]=23,	-- depth:5
[17]=22,	-- depth:5
[15]=20,	-- depth:5
[14]=19,	-- depth:5
[13]=18,	-- depth:6
[12]=17,	-- depth:6
[10]=15,	-- depth:6
[9]=14,	-- depth:6
[8]=13,	-- depth:7
[7]=12,	-- depth:7
[24]=9,	-- depth:7
[40]=10,	-- depth:7
},
item_random_desc={
{item_id=38053,random_count=0.01,},
{number=1,item_id=38734,random_count=0.02,},
{number=2,random_count=0.03,},
{number=3,item_id=26459,random_count=0.13,},
{number=4,item_id=26460,random_count=0.15,},
{number=5,item_id=26461,random_count=0.31,},
{number=6,item_id=26569,},
{number=7,item_id=48441,},
{number=8,item_id=44185,random_count=0.52,},
{number=9,item_id=44184,},
{number=10,item_id=44180,random_count=5.15,},
{number=11,item_id=26193,},
{number=12,item_id=26191,},
{number=13,item_id=26377,random_count=3.09,},
{number=14,item_id=26203,random_count=3.11,},
{number=15,item_id=26380,},
{number=16,item_id=26379,},
{number=17,item_id=26376,},
{number=18,item_id=26378,random_count=11.22,},
{number=19,item_id=26443,random_count=6.19,},
{number=20,item_id=26441,},
{number=21,item_id=26442,},
{number=22,item_id=26448,},
{number=23,item_id=26438,},
{number=24,item_id=26439,random_count=10.31,},
{number=25,item_id=26440,},
{number=26,item_id=26445,},
{number=27,item_id=26446,},
{number=28,item_id=26447,},
{grade=1,item_id=37224,},
{grade=1,item_id=37056,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,},
{grade=1,}
},

item_random_desc_meta_table_map={
[56]=27,	-- depth:1
[55]=26,	-- depth:1
[53]=24,	-- depth:1
[52]=23,	-- depth:1
[51]=22,	-- depth:1
[37]=8,	-- depth:1
[32]=3,	-- depth:1
[30]=1,	-- depth:1
[57]=28,	-- depth:1
[58]=29,	-- depth:1
[13]=25,	-- depth:1
[12]=25,	-- depth:1
[17]=11,	-- depth:1
[16]=11,	-- depth:1
[18]=11,	-- depth:1
[21]=11,	-- depth:1
[7]=11,	-- depth:1
[10]=11,	-- depth:1
[49]=20,	-- depth:1
[48]=19,	-- depth:1
[54]=25,	-- depth:1
[47]=18,	-- depth:2
[50]=21,	-- depth:2
[46]=17,	-- depth:2
[41]=12,	-- depth:2
[44]=15,	-- depth:1
[43]=14,	-- depth:1
[42]=13,	-- depth:2
[40]=11,	-- depth:1
[39]=10,	-- depth:2
[38]=9,	-- depth:1
[36]=7,	-- depth:2
[35]=6,	-- depth:1
[34]=5,	-- depth:1
[31]=2,	-- depth:1
[45]=16,	-- depth:2
[33]=4,	-- depth:1
},
config_param_default_table={start_server_day=1,end_server_day=5,grade=0,open_level=100,},

grade_default_table={grade=0,consume=1,reward_unbind=1,sp_guarantee_x=50,sp_guarantee_n=0,sp_guarantee_finish=0,rebate=1,},

consume_default_table={consume=1,onekey_lotto_num=1,consume_count=40,yanhua_item=item_table[39],discount_text="",},

reward_default_table={reward=1,reward_id=1,reward_item=item_table[40],reward_type=3,player_guarantee=0,guarantee_reward_limit=0,broadcast=0,reward_show=3,rewrad_rare_show=0,},

rebate_default_table={rebate=1,index=1,lotto_num=10,reward_item={[0]=item_table[41]},rebate_icon=1,},

role_sp_guarantee_default_table={reward=1,sp_player_guarantee_id=1,reward_id=1,sp_guarantee_weight="1,3,0|4,4,2|5,5,80",show_icon=1,},

item_random_desc_default_table={grade=0,number=0,item_id=26570,random_count=1.03,}

}

