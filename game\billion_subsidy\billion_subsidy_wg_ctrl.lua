require("game/billion_subsidy/billion_subsidy_wg_data")
require("game/billion_subsidy/billion_subsidy_view")
require("game/billion_subsidy/billion_subsidy_dezg_view")
require("game/billion_subsidy/billion_subsidy_jkjzc_view")
require("game/billion_subsidy/billion_subsidy_b1g3_view")
require("game/billion_subsidy/billion_subsidy_vip_view")
require("game/billion_subsidy/billion_subsidy_active_vip_view")
require("game/billion_subsidy/billion_subsidy_discount_coupon_view")
require("game/billion_subsidy/billion_subsidy_drpt_view")
require("game/billion_subsidy/billion_subsidy_discount_coupon_bag")
require("game/billion_subsidy/billion_subsidy_uplevel_effect_view")
require("game/billion_subsidy/billion_subsidy_bybt_view")
require("game/billion_subsidy/billion_subsidy_xdzk_view")
require("game/billion_subsidy/billion_subsidy_mrsy_view")
require("game/billion_subsidy/billion_subsidy_buy_tip_view")
require("game/billion_subsidy/billion_subsidy_receive_dc_effect_view")
require("game/billion_subsidy/billion_subsidy_lysd_view")
require("game/billion_subsidy/billion_subsidy_rapidly")
require("game/billion_subsidy/billion_subsidy_dailygift_view")
require("game/billion_subsidy/billion_subsidy_shop_cart_entrance_view")
require("game/billion_subsidy/billion_subsidy_shop_cart_view")
require("game/billion_subsidy/billion_subsidy_yiyuan_view")

BillionSubsidyWGCtrl = BillionSubsidyWGCtrl or BaseClass(BaseWGCtrl)

function BillionSubsidyWGCtrl:__init()
	if nil ~= BillionSubsidyWGCtrl.Instance then
		ErrorLog("[BillionSubsidyWGCtrl] attempt to create singleton twice!")
		return
	end
	BillionSubsidyWGCtrl.Instance = self

	self.data = BillionSubsidyWGData.New()
	self.view = BillionSubsidyView.New(GuideModuleName.BillionSubsidy)
	self.dc_bag_view = BillionSubsidyDCBag.New()
	self.uplevel_effect_view = BillionSubsidyUplevelEffectView.New()
	self.active_vip_view = BillionSubsidyActiveVIPView.New(GuideModuleName.BillionSubsidyActiveVip)
	self.buy_tip_view = BillionSubsidyBuyTipView.New(GuideModuleName.BillionSubsidyBuyTip)
	self.receive_dc_effect_view = BillionSubsidyReceiveDCEffectView.New()
	self.shop_cart_view = BillionSubsidyShopCartView.New()

	self:RegisterAllProtocols()
	self.day_pass_event = GlobalEventSystem:Bind(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayPassEvent, self))
	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
end

function BillionSubsidyWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if nil ~= self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if nil ~= self.dc_bag_view then
		self.dc_bag_view:DeleteMe()
		self.dc_bag_view = nil
	end

	if self.uplevel_effect_view then
		self.uplevel_effect_view:DeleteMe()
		self.uplevel_effect_view = nil
	end

	if self.receive_dc_effect_view then
		self.receive_dc_effect_view:DeleteMe()
		self.receive_dc_effect_view = nil
	end

	if self.active_vip_view then
		self.active_vip_view:DeleteMe()
		self.active_vip_view = nil
	end

	if nil ~= self.day_pass_event then
		GlobalEventSystem:UnBind(self.day_pass_event)
		self.day_pass_event = nil
	end

	if self.open_fun_change then
		GlobalEventSystem:UnBind(self.open_fun_change)
	end

	if self.buy_tip_view then
		self.buy_tip_view:DeleteMe()
		self.buy_tip_view = nil
	end

	if self.shop_cart_view then
		self.shop_cart_view:DeleteMe()
		self.shop_cart_view = nil
	end

	if self.yiyuan_view then
		self.yiyuan_view:DeleteMe()
		self.yiyuan_view = nil
	end

	BillionSubsidyWGCtrl.Instance = nil
end

-- 注册协议
function BillionSubsidyWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCTenBillionSubsidyAllInfo, "OnSCTenBillionSubsidyAllInfo")
	self:RegisterProtocol(SCTenBillionSubsidyHighPriceShareHelpTimesInfo, "OnSCTenBillionSubsidyHighPriceShareHelpTimesInfo")
	self:RegisterProtocol(SCTenBillionSubsidyParticipateAllInfo, "OnSCTenBillionSubsidyParticipateAllInfo")
	self:RegisterProtocol(CSTenBillionSubsidyClientOperate)
	self:RegisterProtocol(SCTenBillionSubsidyFirstPayAndReturnRewardInfo, "OnSCTenBillionSubsidyFirstPayAndReturnRewardInfo")
	self:RegisterProtocol(SCTenBillionSubsidyAllTicketInfoUpdate, "OnSCTenBillionSubsidyAllTicketInfoUpdate")
	self:RegisterProtocol(SCTenBillionSubsidySingleDiscountTicketInfoUpdate, "OnSCTenBillionSubsidySingleDiscountTicketInfoUpdate")
	self:RegisterProtocol(SCMRSYTryTicketShopInfoUpdate, "OnSCMRSYTryTicketShopInfoUpdate")
	self:RegisterProtocol(SCXDZKStartDiscountTimestampUpdate, "OnSCXDZKStartDiscountTimestampUpdate")
	self:RegisterProtocol(SCXDZKSingleItemInfoUpdate, "OnSCXDZKSingleItemInfoUpdate")
	self:RegisterProtocol(SCBYBTSingleItemInfoUpdate, "OnSCBYBTSingleItemInfoUpdate")
	self:RegisterProtocol(SCTenBillionSubsidyDailyShopSingleItemInfoUpdate, "OnSCTenBillionSubsidyDailyShopSingleItemInfoUpdate")
	self:RegisterProtocol(SCTenBillionSubsidyHighPriceShopBuyFlagInfoUpdate, "OnSCTenBillionSubsidyHighPriceShopBuyFlagInfoUpdate")
	self:RegisterProtocol(SCTenBillionSubsidyTicketUseLimitInfoUpdate, "OnSCTenBillionSubsidyTicketUseLimitInfoUpdate")
	self:RegisterProtocol(SCLYSDSingleItemInfoUpdate, "OnSCLYSDSingleItemInfoUpdate")

	self:RegisterProtocol(SCTenBillionSubsidyParticipateInfoUpdate, "OnSCTenBillionSubsidyParticipateInfoUpdate")
	self:RegisterProtocol(SCTenBillionSubsidyMemberDailyRewardUpdate, "OnSCTenBillionSubsidyMemberDailyRewardUpdate")
	self:RegisterProtocol(SCTenBillionSubsidyCumulateOrderNumInfoUpdate, "SCTenBillionSubsidyCumulateOrderNumInfoUpdate")

	self:RegisterProtocol(SCTenBillionSubsidyPayOneGetMoreShopInfoUpdate, "OnSCTenBillionSubsidyPayOneGetMoreShopInfoUpdate") -- 买一付三
	self:RegisterProtocol(SCTenBillionSubsidyQuotaShopSingleItemInfoUpdate, "OnSCTenBillionSubsidyQuotaShopSingleItemInfoUpdate")
	self:RegisterProtocol(SCTenBillionSubsidyFirstOpenViewFlagUpdate, "OnSCTenBillionSubsidyFirstOpenViewFlagUpdate")
	----------- 购物车
	self:RegisterProtocol(SCTenBillionSubSidyTrolleyInfo, "OnSCTenBillionSubSidyTrolleyInfo")
	-- 一元活动
	self:RegisterProtocol(SCTenBillionSubsidyOneYuanInfo, "OnSCTenBillionSubsidyOneYuanInfo")
end


--百亿补贴总信息.
function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyAllInfo(protocol)
	self.data:SetAllInfo(protocol)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyBYBT)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyXDZK)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyVIP)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyFYMS)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyJKJZC)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyDEZG)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyDRPT)

	self:FlushShopCart()
end

--百亿补贴大额直购分享助力次数信息.
function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyHighPriceShareHelpTimesInfo(protocol)
	self.data:SetDEZGShareHelpTimesInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

--百亿补贴参团商品总信息.
function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyParticipateAllInfo(protocol)
	-- print_error("百亿补贴参团商品总信息： ", protocol.item_info_list)
	for key, cfg in pairs(protocol.item_info_list) do
		for k, v in pairs(cfg.participate_info) do
			AvatarManager.Instance:SetAvatarKey(v.uid, v.avatar_key_big, v.avatar_key_small)
		end
	end

	self.data:SetDRPTShopAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyDRPT)
end

--操作协议.
function BillionSubsidyWGCtrl:SendTenBillionSubsidyClientOperate(operate, param1, param2, param3, param4, param5)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTenBillionSubsidyClientOperate)
	protocol.operate = operate or 0
	protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
    protocol.param4 = param4 or 0
	protocol.param5 = param5 or 0
	--print_error("16640 ",protocol.operate,protocol.param1,protocol.param2)
	protocol:EncodeAndSend()
end

function BillionSubsidyWGCtrl:OnDayPassEvent()
	-- print_error("---天数改变----", TimeWGCtrl.Instance:GetCurOpenServerDay())
	RemindManager.Instance:Fire(RemindName.BillionSubsidyBYBT)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyXDZK)

	self:FlushShopCart()
end

function BillionSubsidyWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
	if fun_name == FunName.billion_subsidy_rapidly and is_open then
		RemindManager.Instance:Fire(RemindName.DailyRecharge_Rapidly)
    end

    if fun_name == FunName.billion_subsidy_dailygift and is_open then
		--屏蔽
		--self:SetEverydayRechargeDailyGiftIcon()
		RemindManager.Instance:Fire(RemindName.DailyRecharge_Libao)
    end
end

function BillionSubsidyWGCtrl:SetEverydayRechargeDailyGiftIcon()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local flag = PlayerPrefsUtil.GetInt("everyday_recharge_dailygift" .. main_role_id .. open_day)

	if flag ~= 1 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EVERYDAY_RECHARGE_DAILYGIFT, 1, function ()
			ViewManager.Instance:Open(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_dailygift)
			return true
			end)
	end
end

-- 播放会员升级效果
function BillionSubsidyWGCtrl:PlayMemberUplevelEffect(member_level)
	self.uplevel_effect_view:SetDataAndOpen(member_level)
end

function BillionSubsidyWGCtrl:SetIsFirstOpen(bool)
	self.view.is_first_open = bool
end

-- 播放领取折扣券特效
function BillionSubsidyWGCtrl:PlayReceiveDCEffect(callback)
	if self.view.show_index == TabIndex.billion_subsidy_vip then
		self.receive_dc_effect_view:SetDataAndOpen(callback)
	end
end

-- 播放进界面领取折扣券特效
function BillionSubsidyWGCtrl:PlayOpenReceiveDCEffect(callback)
	if self.view.show_index == TabIndex.billion_subsidy_bybt then
		self.receive_dc_effect_view:SetDataAndOpen(callback)
	end
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyFirstPayAndReturnRewardInfo(protocol)
	self.data:SetFirstPayAndReturnRewardInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyBYBT)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyXDZK)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyFYMS)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyJKJZC)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyDEZG)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyDRPT)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyAllTicketInfoUpdate(protocol)
	self.data:SetAllTicketInfoUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyVIP)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidySingleDiscountTicketInfoUpdate(protocol)
	self.data:SetSingleTicketInfoUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyVIP)
end

function BillionSubsidyWGCtrl:OnSCMRSYTryTicketShopInfoUpdate(protocol)
	self.data:SetTryTicketShopInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

function BillionSubsidyWGCtrl:OnSCXDZKStartDiscountTimestampUpdate(protocol)
	self.data:SetXDZKStartDiscountTimestamp(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

function BillionSubsidyWGCtrl:OnSCXDZKSingleItemInfoUpdate(protocol)
	self.data:SetXDZKSingleItemUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

function BillionSubsidyWGCtrl:OnSCBYBTSingleItemInfoUpdate(protocol)
	self.data:SetBYBTShopSingleItemUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyDailyShopSingleItemInfoUpdate(protocol)
	self.data:SetBYBTVIPDailyShopSingleItemUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyTicketUseLimitInfoUpdate(protocol)
	self.data:SetTicketUseLimitInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

function BillionSubsidyWGCtrl:OnSCLYSDSingleItemInfoUpdate(protocol)
	self.data:SetLYSDSingleItemData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyHighPriceShopBuyFlagInfoUpdate(protocol)
	self.data:SetDEZGShopItemUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyDEZG)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyParticipateInfoUpdate(protocol)
	self.data:SetParticipateInfoUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyMemberDailyRewardUpdate(protocol)
	local member_level = self.data:GetMemberLevel()
	if member_level < protocol.member_level then
		self:PlayMemberUplevelEffect(protocol.member_level)
	end
	self.data:SetMemberDailyRewardUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
	if self.active_vip_view:IsOpen() then
		self.active_vip_view:Flush()
	end


	RemindManager.Instance:Fire(RemindName.BillionSubsidyVIP)
	self:FlushShopCart()
end

function BillionSubsidyWGCtrl:SCTenBillionSubsidyCumulateOrderNumInfoUpdate(protocol)
	self.data:SetCumulateOrderNumInfoUpdate(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy)
	RemindManager.Instance:Fire(RemindName.BillionSubsidyVIP)
end

-- 付一买三商品信息
function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyPayOneGetMoreShopInfoUpdate(protocol)
	self.data:SetPayOneGetMoreShopInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_fyms)
end

-- 定额商店单个商品
function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyQuotaShopSingleItemInfoUpdate(protocol)
	self.data:SetJKJZCShopSingleItemInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_jkjzc)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyFirstOpenViewFlagUpdate(protocol)
	self.data:SetFirstOpenViewFlag(protocol.first_open_view_flag)
end

function BillionSubsidyWGCtrl:OnSCTenBillionSubSidyTrolleyInfo(protocol)
	self.data:SetShopCartData(protocol)
	self:FlushShopCart()
	self:FlushCurShowIndex()
end

-- 一元活动
function BillionSubsidyWGCtrl:OnSCTenBillionSubsidyOneYuanInfo(protocol)
	self.data:SetYiYuanInfo(protocol)
	self:FlushCurShowIndex()
	RemindManager.Instance:Fire(RemindName.BillionSubsidyYYHD)
end

function BillionSubsidyWGCtrl:FlushShopCart()
	if self.shop_cart_view:IsOpen() then
		self.shop_cart_view:Flush()
	end
end


function BillionSubsidyWGCtrl:FlushCurShowIndex()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function BillionSubsidyWGCtrl:OpenActiceVipView()
	if self.active_vip_view:IsOpen() then
		self.active_vip_view:Flush()
	else
		self.active_vip_view:Open()
	end
end

function BillionSubsidyWGCtrl:OpenDCBag()
	if self.dc_bag_view:IsOpen() then
		self.dc_bag_view:Flush()
	else
		self.dc_bag_view:Open()
	end
end

function BillionSubsidyWGCtrl:GetCurShowIndex()
	if self.view:IsOpen() then
		return self.view.show_index
	end
end

function BillionSubsidyWGCtrl:OpenBuyTipView(shop_type, item_seq, custom_param)
	if not self.buy_tip_view:IsOpen() then
		self.buy_tip_view:SetDataAndOpen(shop_type, item_seq, custom_param)
	end
end

function BillionSubsidyWGCtrl:SetVipBlankTipsShow(bool)
	if self.view.show_index == TabIndex.billion_subsidy_vip then
		self.view:SetVipBlankTipsShow(bool)
	end
end

function BillionSubsidyWGCtrl:PlaySCAddItemAnim(item_id, start_pos, target_pos)
	if self.view:IsOpen() then
		self.view:PlaySCAddItemAnim(item_id, start_pos, target_pos)
	end
end

function BillionSubsidyWGCtrl:GetNodeInScreenPos(node)
	if self.view:IsOpen() then
		return self.view:GetNodeInScreenPos(node)
	end
end

function BillionSubsidyWGCtrl:OpenShopCartView()
	self.shop_cart_view:Open()
end

function BillionSubsidyWGCtrl:ClearAllShopCart()
	self:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.SHOP_CART, -1, 0, 0)
end

-- 添加商品到购物车
function BillionSubsidyWGCtrl:AddItemToShopCart(shop_type, item_seq, count, buy_limit, is_tips)
	if self.data:IsCanAddShopToCart(shop_type, item_seq, count, buy_limit, is_tips) then
		local have_count = self.data:GetShopNumInCart(shop_type, item_seq)
		self:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.SHOP_CART, shop_type, item_seq, have_count + count)
		return true
	end
	return false
end

-- 从购物车减少商品
function BillionSubsidyWGCtrl:RemoveItemToShopCart(shop_type, item_seq, count)
	local lower_count = 0
	local num = self.data:GetShopNumInCart(shop_type, item_seq)
	if num - count >= 0 then
		lower_count = num - count
	end
	self:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.SHOP_CART, shop_type, item_seq, lower_count)
end

-- 设置物品选择状态
function BillionSubsidyWGCtrl:SetShopItemChoose(shop_type, item_seq, is_choose)
	self:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.SHOP_CART_CHOOSE, shop_type, item_seq, is_choose)
end

-- 购物车锁定 lock_state:1 锁定 0解锁
function BillionSubsidyWGCtrl:ShopCartLock(lock_state)
	self:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.SHOP_CART_Lock, lock_state)
end