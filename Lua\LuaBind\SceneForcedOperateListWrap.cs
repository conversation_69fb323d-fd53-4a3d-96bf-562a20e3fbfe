﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class SceneForcedOperateListWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(SceneForcedOperateList), typeof(Sirenix.OdinInspector.SerializedMonoBehaviour));
		<PERSON><PERSON>unction("SetObjectActive", SetObjectActive);
		L<PERSON>Function("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("m_sceneObjGroup", get_m_sceneObjGroup, set_m_sceneObjGroup);
		<PERSON><PERSON>Class();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetObjectActive(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			SceneForcedOperateList obj = (SceneForcedOperateList)ToLua.CheckObject<SceneForcedOperateList>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.SetObjectActive(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_m_sceneObjGroup(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneForcedOperateList obj = (SceneForcedOperateList)o;
			System.Collections.Generic.Dictionary<int,System.Collections.Generic.List<UnityEngine.GameObject>> ret = obj.m_sceneObjGroup;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_sceneObjGroup on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_m_sceneObjGroup(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			SceneForcedOperateList obj = (SceneForcedOperateList)o;
			System.Collections.Generic.Dictionary<int,System.Collections.Generic.List<UnityEngine.GameObject>> arg0 = (System.Collections.Generic.Dictionary<int,System.Collections.Generic.List<UnityEngine.GameObject>>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.Dictionary<int,System.Collections.Generic.List<UnityEngine.GameObject>>));
			obj.m_sceneObjGroup = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index m_sceneObjGroup on a nil value");
		}
	}
}

