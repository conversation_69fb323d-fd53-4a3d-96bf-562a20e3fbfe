--连续充值
ContinuousXiaoFeiView = ContinuousXiaoFeiView or BaseClass(SafeBaseView)

function ContinuousXiaoFeiView:__init()
	-- self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/continuous_consume_ui_prefab", "layout_recharge")
	self.open_tween = nil
	self.close_tween = nil
end

function ContinuousXiaoFeiView:__delete()
	
end

function ContinuousXiaoFeiView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("continuous_recharge_left_time") then
		CountDownManager.Instance:RemoveCountDown("continuous_recharge_left_time")
	end
	if nil ~= self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if nil ~= self.reward_grid then
		self.reward_grid:DeleteMe()
		self.reward_grid = nil
	end

	if self.extra_reward then
		for k,v in pairs(self.extra_reward) do
			v:DeleteMe()
		end
		self.extra_reward = {}
	end
	self.has_load_callback = nil
	self.need_refresh = nil
	self.is_first_set_data = nil
end

function ContinuousXiaoFeiView:LoadCallBack()
	self:CreateRewardList()
	self.is_first_set_data = true
	--self.node_list.activity_state.text.text = Language.ContinuousRecharge.TipsText --zzz
	--self:GetActiveTime()

	local open_act_cfg = ServerActivityWGData.Instance:GetClientActCfg(177)
	if self.node_list.version_act_time ~= nil and open_act_cfg ~= nil then
		local act_info = ActivityWGData.Instance:GetActivityStatuByType(open_act_cfg.open_type)
		local star_str = ""
		local end_str = ""
		if act_info then
			star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
			end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time - 1)
		end
		self.node_list.version_act_time.text.text = (star_str .. "----" .. end_str)
	end

	if self.node_list.version_act_des ~= nil and open_act_cfg ~= nil then
		self.node_list.version_act_des.text.text = Language.OpenServer.ActShuoMing .. (open_act_cfg.top_desc)
	end

	self.node_list.label_days.text.text = Language.ContinuousConsume.RechargeTips4
	self.has_load_callback = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function ContinuousXiaoFeiView:GetActiveTime()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.TotalConsume)
	local star_str = ""
	local end_str = ""
	if act_info then
		star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
		end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time)
	end
	local str = (star_str .. "----" .. end_str)
	self.node_list.activity_state.text.text = string.format(Language.ContinuousRecharge.TipsText,str)
end


function ContinuousXiaoFeiView:OpenCallBack()
	self:SendAllInfoReq()

	if CountDownManager.Instance:HasCountDown("continuous_recharge_left_time") then
		CountDownManager.Instance:RemoveCountDown("continuous_recharge_left_time")
	end
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.TotalConsume) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		local next_time = act_cornucopia_info.next_time or 0
		CountDownManager.Instance:AddCountDown("continuous_recharge_left_time", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), next_time, nil, 1)	
	else
		self:CompleteRollerTime()
	end
end

function ContinuousXiaoFeiView:PlayTween()
	self.open_tween = UITween.ShowFadeUp
end

function ContinuousXiaoFeiView:CloseCallBack()
	self.open_tween = nil
end

function ContinuousXiaoFeiView:CreateRewardList()
	self.reward_list = AsyncListView.New(ContinuousXiaoFeiListItemRender,self.node_list.ph_list_reward)
end

function ContinuousXiaoFeiView:SendAllInfoReq()
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.TotalConsume,
		opera_type =TOTAL_CONSUME_OP_TYPE.TOTAL_CONSUME_OP_TYPE_INFO,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ContinuousXiaoFeiView:UpdataRollerTime(elapse_time, next_time)
	local time = next_time - elapse_time
	if self.node_list.label_remain_day ~= nil then
		if time > 0 then
			local format_time = TimeUtil.Format2TableDHMS(time) 
			local str_list = Language.Common.TimeList
			local time_str = ""
			if format_time.day > 0 then--sss
				time_str = string.format(Language.ConsumeDiscount.tiemshow1,format_time.day,format_time.hour,format_time.min,format_time.s)
			else
				time_str = string.format(Language.ConsumeDiscount.tiemshow2,format_time.hour,format_time.min,format_time.s)
			end
			self.node_list.label_remain_day.text.text = time_str
		end
	end
end

function ContinuousXiaoFeiView:CompleteRollerTime()
	if self.node .label_days ~= nil then
		self.node_list.label_remain_day.text.text = "0"
	end
end


function ContinuousXiaoFeiView:OnClickBtnReward()

end

-- flush func ---------------------------------------------------------
function ContinuousXiaoFeiView:RefreshView()
	if not self.has_load_callback then
		self.need_refresh = true
		return
	end



	--local today_chongzhi = ContinuousRechargeData.Instance:GetTodayChongzhi()
	--local continue_chongzhi_days = ContinuousRechargeData.Instance:GetContinueChongzhiDays()
	--local continue_chongzhi_fetch_extra_reward_need_days = ContinuousRechargeData.Instance:GetContinueChongzhiFetchDays()
	--local cur_day_index = ContinuousRechargeData.Instance:GetContinueChongzhiDays()

	local total_consume = ContinuousXiaoFeiWGData.Instance:GetTotalConsume()

	--string.format(Language.ContinuousConsume.RechargeTips4,total_consume)
	self.node_list.consume_value.text.text = total_consume

	local data_list = ContinuousXiaoFeiWGData.Instance:GetRewardData()
	self.reward_list:SetDataList(data_list, 0)
end


-- ContinuousXiaoFeiListItemRender -----------------------------------
ContinuousXiaoFeiListItemRender = ContinuousXiaoFeiListItemRender or BaseClass(BaseRender)
function ContinuousXiaoFeiListItemRender:__init()

end

function ContinuousXiaoFeiListItemRender:__delete()
	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
end

function ContinuousXiaoFeiListItemRender:LoadCallBack()
	-- BaseRender.CreateChild(self)
	self.cell_list = {}
	for i = 1, 4 do
		self.cell_list[i] = ItemCell.New()
		self.cell_list[i]:SetInstanceParent(self.node_list["ph_cell_" .. i])
	end
 	self.node_list.btn_receive.button:AddClickListener(BindTool.Bind1(self.OnClickBtnReceive, self))
end

function ContinuousXiaoFeiListItemRender:OnFlush()
	if nil == self.data then return end
	local is_can_lingqu = self.data.cur_consume >= self.data.total_consume
	local color = is_can_lingqu and COLOR3B.GREEN or COLOR3B.RED
	self.node_list.label_recharge_gold.text.text = string.format(Language.ContinuousConsume.RechargeTips2, color, self.data.cur_consume, self.data.total_consume)

	if self.data.is_fetch then
		self.node_list["btn_receive"]:SetActive(false)
		self.node_list["Img_YLQ"]:SetActive(true)
		self.node_list["btn_receive_text"]:SetActive(false)
	else
		self.node_list["btn_receive_text"]:SetActive(true)
		if is_can_lingqu then
			self.node_list["btn_receive"]:SetActive(true)
			self.node_list["Img_YLQ"]:SetActive(false)
			self.node_list["btn_receive_text"].text.text = Language.ContinuousConsume.Fetch
		else
			self.node_list["btn_receive"]:SetActive(true)
			self.node_list["Img_YLQ"]:SetActive(false)
			self.node_list["btn_receive_text"].text.text = Language.ContinuousConsume.GoToConsume
		end
	end

	for k,v in pairs(self.cell_list) do
		v:SetData(self.data[k])
	end
end

function ContinuousXiaoFeiListItemRender:OnClickBtnReceive()
	if self.data.cur_consume < self.data.total_consume then
		ViewManager.Instance:Open(GuideModuleName.Shop)
		return
	end

	local param_t = {}
	param_t = {
		rand_activity_type = ACTIVITY_TYPE.TotalConsume,
		opera_type = TOTAL_CONSUME_OP_TYPE.TOTAL_CONSUME_OP_TYPE_FETCH,
		param_1 = self.data.seq
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
	AudioService.Instance:PlayRewardAudio()
end

function ContinuousXiaoFeiListItemRender:CreateSelectEffect()
end

-----------------------------------------------------------------------
