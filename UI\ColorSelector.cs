using System.Collections.Generic;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

public class ColorSelector : <PERSON>o<PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IDragHandler
{
    public SubColorSelector m_SubColorSelector;     // 取色板
    public RectTransform m_ColorWheel;                      //色环
    public Image m_GetColorWheel;                   //取色环
    public RawImage m_ColorSaturation;              //调色板
    public RectTransform m_SelectorWheel;           //选择图标
    public float m_radius_offset = 0;               //半径偏移
    public Vector3 m_canvas_offset_pos;             //画布偏移位置

    //调色板图片
    private Texture2D saturationTexture2D;
    private readonly float piexlWidth = 256.0f;
    private readonly float piexlHeight = 256.0f;
    private readonly float huePiexWidth = 50.0f;
    //当前HSV
    private Vector4 currentColorHSV = new Vector4(0, 1, 1, 1);
    private Canvas m_canvas = null;
    private CanvasScaler m_canvas_scaler = null;
    private bool is_soft_color = true;
    private float soft_factor = 1f;

    public float angle;
    private Vector2 newV2 = Vector2.zero;

    void Awake()
    {
        saturationTexture2D = new Texture2D((int)piexlWidth, (int)piexlHeight);
        m_ColorSaturation.texture = saturationTexture2D;
        m_canvas = this.transform.GetComponentInParent<Canvas>();
        m_canvas_scaler = this.transform.GetComponentInParent<CanvasScaler>();
    }

    void Start()
    {
        FulshNowPosColor();
    }

    public void FulshNowPosColor()
    {
        if (this.m_canvas == null)
        {
            Debug.LogError("画布没找到");
            return;
        }

        Vector2 rootCanvasScale = this.m_canvas.GetComponent<RectTransform>().lossyScale;
        //计算选色环的位置
        Vector2 select_pos = new Vector2((m_SelectorWheel.position.x - m_canvas_offset_pos.x) / rootCanvasScale.x, (m_SelectorWheel.position.y - m_canvas_offset_pos.y) / rootCanvasScale.y);
        CalculateWheelPos(select_pos);
        m_SubColorSelector.UpdateColor();
    }

    public void SetSelectorWheel(Image Ima)
    {
        this.UpdateColorSelectorWheel(Ima.color);
    }

    public void UpdateHexColorSelectorWheel(string hex)
    {
        Color aim_color = Color.white;
        ColorUtility.TryParseHtmlString(hex, out aim_color);
        UpdateColorSelectorWheel(aim_color);
    }

    public void UpdateColorSelectorWheel(Color input_color)
    {
        //计算传入颜色的HSV
        Color.RGBToHSV(input_color, out float hue, out float saturation, out float brightness);
        //获取内部色盘的最亮色
        RGBToHSV(Color.HSVToRGB(hue, 1, 1));
        //计算色环小圈的位置
        Vector2 o = new Vector2(m_ColorWheel.rect.width / 2, m_ColorWheel.rect.height / 2);
        float r = m_ColorWheel.rect.width / 2 - m_SelectorWheel.rect.width / 2;
        newV2.x = o.x + r * Mathf.Cos(hue * 360 * Mathf.Deg2Rad);
        newV2.y = o.y + r * Mathf.Sin(hue * 360 * Mathf.Deg2Rad);
        m_SelectorWheel.localPosition = newV2;
        //更新内部色盘图片
        UpdateSaturation(currentColorHSV);
        //计算内部色盘小圈位置
        m_SubColorSelector.m_SelectorWheel.transform.localPosition = new Vector2(saturation * m_SubColorSelector.RectTrans.rect.width, brightness * m_SubColorSelector.RectTrans.rect.height);
    }

    public void SetIsSoft(bool is_soft_value, float soft_factor_value)
    {
        this.is_soft_color = is_soft_value;
        this.soft_factor = soft_factor_value;
        UpdateSaturation(currentColorHSV);
        m_SubColorSelector.UpdateSoftLocalPos(soft_factor_value);
    }

    public void qingrou()
    {
        this.SetIsSoft(true, 0.7f);
    }

    public void quanshu()
    {
        this.SetIsSoft(false, 1f);
    }


    public void OnPointerDown(PointerEventData eventData)
    {
        CircleMove(eventData);
    }


    public void OnDrag(PointerEventData eventData)
    {
        CircleMove(eventData);
    }

    public void CircleMove(PointerEventData eventData)
    {
        CalculateWheelPos(eventData.position);
        m_SubColorSelector.UpdateColor(eventData);
    }

    public Vector3 GetCanvasFinalPos(Vector3 aim_pos)
    {
        var return_pos = Vector3.zero;
        if (this.m_canvas == null || this.m_canvas_scaler == null)
        {
            Debug.LogError("画布没找到");
            return return_pos;
        }

        var height_radio = this.m_canvas_scaler.referenceResolution.y / Screen.height;
        var whidth_radio = this.m_canvas_scaler.referenceResolution.x / Screen.width;

        if (this.m_canvas_scaler.matchWidthOrHeight == 1)// -- 等高缩放
            return_pos = new Vector3(aim_pos.x * height_radio, aim_pos.y * height_radio, 0);
        else// -- 等宽缩放
            return_pos = new Vector3(aim_pos.x * whidth_radio, aim_pos.y * whidth_radio, 0);

        return return_pos;
    }

    // 计算位置信息
    public void CalculateWheelPos(Vector3 pos)
    {
        if (this.m_canvas == null || this.m_canvas_scaler == null)
        {
            Debug.LogError("画布没找到");
            return;
        }

        // 将左下角原点坐标系转换为中心点为原点的坐标系
        pos = GetCanvasFinalPos(pos);
        Vector3 center_pos = new Vector3(Screen.width / 2, Screen.height / 2, 0);
        center_pos = GetCanvasFinalPos(center_pos);
        Vector3 mouse_pos = pos - center_pos;
        Vector2 rootCanvasScale = this.m_canvas.GetComponent<RectTransform>().lossyScale;
        //计算选色环的位置
        Vector2 o = new Vector2(m_ColorWheel.rect.width / 2, m_ColorWheel.rect.height / 2);
        Vector2 point_center = new Vector2((transform.position.x - m_canvas_offset_pos.x) / rootCanvasScale.x, (transform.position.y - m_canvas_offset_pos.y) / rootCanvasScale.y);
        //鼠标位置 - 圆形位置 = 圆心指向鼠标位置的向量
        Vector3 mousepos = transform.parent.InverseTransformPoint(mouse_pos) -
                           transform.parent.InverseTransformPoint((Vector3)point_center + (Vector3)o);
        //半径
        float r = m_ColorWheel.rect.width / 2 - m_SelectorWheel.rect.width / 2;
        // 弧度
        float radians = Mathf.Atan2(mousepos.y, mousepos.x);
        //角度
        float degress = Mathf.Rad2Deg * radians;

        newV2.x = o.x + r * Mathf.Cos(degress * Mathf.Deg2Rad);
        newV2.y = o.y + r * Mathf.Sin(degress * Mathf.Deg2Rad);
        m_SelectorWheel.localPosition = newV2;


        // 计算偏移数值(这里需要拿偏移的色值，不拿表现图的色值)
        //偏移半径
        float offset_r = m_ColorWheel.rect.width / 2 - m_SelectorWheel.rect.width / 2 - m_radius_offset;
        Vector2 new_offset_v2 = Vector2.zero;
        new_offset_v2.x = o.x + offset_r * Mathf.Cos(degress * Mathf.Deg2Rad);
        new_offset_v2.y = o.y + offset_r * Mathf.Sin(degress * Mathf.Deg2Rad);

        //获取调色板图片
        Texture2D tex = m_GetColorWheel.sprite.texture;
        //1.计算选色环在Image上的位置占比 (0~1)
        //2.通过位置占比,映射获取到要获取的贴图像素位置
        int x = (int)(new_offset_v2.x / m_ColorWheel.rect.width * tex.width);
        int y = (int)(new_offset_v2.y / m_ColorWheel.rect.height * tex.height);
        //通过Texture2D.GetPixel这个方法来获取该位置下的像素的颜色值
        //Debug.Log(m_SelectorWheel.localPosition + "!!!!!!!!!!!!!!!!");
        Color color = tex.GetPixel(x, y);
        //Debug.Log("+++++++图片中当前像素的颜色值：" + color);
        RGBToHSV(color);
        UpdateSaturation(currentColorHSV);
    }
    private void RGBToHSV(Color color)
    {
        Color.RGBToHSV(color, out currentColorHSV.x, out currentColorHSV.y, out currentColorHSV.z);
        // 更新调色板的位置
    }

    /// <summary>
    /// 更新调色板
    /// </summary>
    /// <param name="hsv"></param>
    private void UpdateSaturation(Vector4 hsv)
    {
        for (int y = 0; y < piexlHeight; y++)
        {
            for (int x = 0; x < piexlWidth; x++)
            {
                Color pixColor = GetSaturation(hsv, x / piexlWidth, y / piexlHeight);
                saturationTexture2D.SetPixel(x, y, pixColor);
            }
        }

        saturationTexture2D.Apply();
        if (!m_ColorSaturation.enabled)
        {
            m_ColorSaturation.enabled = true;
        }
        m_ColorSaturation.texture = saturationTexture2D;
    }

    /// <summary>
    /// 根据设置分辨率转化HSV
    /// </summary>
    /// <param name="hsv"></param>
    /// <param name="x"></param>
    /// <param name="y"></param>
    /// <returns></returns>
    private Color GetSaturation(Vector4 hsv, float x, float y)
    {
        Vector4 saturationHSV = hsv;
        //saturationHSV.x = CheckIsSoftVlue(hsv.x);
        saturationHSV.y = CheckIsSoftVlue(x);
        saturationHSV.z = CheckIsSoftVlue(y);
        saturationHSV.w = 1;
        return HSVToRGB(saturationHSV);
    }

    private float CheckIsSoftVlue(float aim = 0)
    {
        if (this.is_soft_color)
        {
            aim *= soft_factor;
        }

        return aim;
    }

    private Color HSVToRGB(Vector4 hsv)
    {
        Color color = Color.HSVToRGB(hsv.x, hsv.y, hsv.z);
        color.a = hsv.w;
        return color;
    }
}

