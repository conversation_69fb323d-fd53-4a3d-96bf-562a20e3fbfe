require("game/new_appearance/multi_mount/multi_mount_wg_data")
require("game/new_appearance/multi_mount/multi_mount_view")

MultiMountWGCtrl = MultiMountWGCtrl or BaseClass(BaseWGCtrl)
function MultiMountWGCtrl:__init()
	if MultiMountWGCtrl.Instance then
		error("[MultiMountWGCtrl]:Attempt to create singleton twice!")
	end

	MultiMountWGCtrl.Instance = self
	self.data = MultiMountWGData.New()

	self:RegisterProtocol(CSDoubleMountOperate)
	self:RegisterProtocol(SCDoubleMountBaseInfo, "OnSCDoubleMountBaseInfo")
	self:RegisterProtocol(SCDoubleMountItemInfo, "OnSCDoubleMountItemInfo")
	self:RegisterProtocol(SCDoubleMountItemUpdate, "OnSCDoubleMountItemUpdate")
	self:RegisterProtocol(SCDoubleMountInvite, "OnSCDoubleMountInvite")
	self:RegisterProtocol(SCDoubleMountRideItemInfo, "OnSCDoubleMountRideItemInfo")
	self:RegisterProtocol(SCDoubleMountRideDown, "OnSCDoubleMountRideDown")

	if not self.role_enter_event then
		self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnter, self))
	end

	if not self.obj_leave_event then
		self.obj_leave_event = GlobalEventSystem:Bind(SceneEventType.OBJ_LEVEL_ROLE, BindTool.Bind(self.OnObjLeave, self))
	end

	if not self.item_change_callback then
		self.item_change_callback = BindTool.Bind(self.OnItemChangeCallBack, self)
		ItemWGData.Instance:NotifyDataChangeCallBack(self.item_change_callback)
	end

	-- 双人坐骑驾驶
	self.multi_mount_drive_list = {}
	-- 双人坐骑乘坐
	self.multi_mount_take_list = {}
	-- 等待刷新列表
	self.wait_Update_multi_mount_list = {}
end

function MultiMountWGCtrl:__delete()
    MultiMountWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil

	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end

	if self.obj_leave_event then
		GlobalEventSystem:UnBind(self.obj_leave_event)
		self.obj_leave_event = nil
	end

	if self.item_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_change_callback)
		self.item_change_callback = nil
	end
end

---------------------------------------------PROTOCOL_START-----------------------------------------
function MultiMountWGCtrl:SendDoubleMountOperate(operate_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSDoubleMountOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or -1
	protocol.param2 = param2 or -1
	protocol.param3 = param3 or -1
	protocol.param4 = param4 or -1
	protocol:EncodeAndSend()
end

function MultiMountWGCtrl:OnSCDoubleMountBaseInfo(protocol)
	-- print_error("-------------OnSCDoubleMountBaseInfo-----------", protocol)
	self.data:SetDoubleMountBaseInfo(protocol)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Multi_Mount)
	ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_multi_mount)
end

function MultiMountWGCtrl:OnSCDoubleMountItemInfo(protocol)
	-- print_error("-------------OnSCDoubleMountItemInfo-----------", protocol)
	self.data:SetDoubleMountItemInfo(protocol)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Multi_Mount)
	ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_multi_mount)
end

function MultiMountWGCtrl:OnSCDoubleMountItemUpdate(protocol)
	-- print_error("-------------OnSCDoubleMountItemUpdate-----------", protocol)
	self.data:UpdateDoubleMountItem(protocol)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Multi_Mount)
	ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_multi_mount)
end

function MultiMountWGCtrl:OnSCDoubleMountInvite(protocol)
	-- print_error("-------------OnSCDoubleMountInvite-----------", protocol)

	local content_str = string.format(Language.MultiMount.InviteToSitMount, protocol.invite_name)
	local plat_type = protocol.invite_uuid.temp_high
	local role_id = protocol.invite_uuid.temp_low

	local ok_func = function()
		self:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.INVITE_RESULT, plat_type, role_id, 1)
	end

	local cancle_func = function()
		self:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.INVITE_RESULT, plat_type, role_id, 0)
	end

	TipWGCtrl.Instance:OpenAlertTips(content_str, ok_func, cancle_func, nil, cancle_func, 10)
end

-- 信息变更，更新玩家的vo里面的数据
-- 先确保主角生成了，然后同步底下搭乘玩家得数据
function MultiMountWGCtrl:OnSCDoubleMountRideItemInfo(protocol)
	-- print_error("-------------OnSCDoubleMountRideItemInfo-----------", protocol)

	local multi_info_data = protocol.multi_info_data
	self.data:AddMultiMountDriveData(multi_info_data.main_obj_id, multi_info_data)

	-- self.multi_mount_drive_list[multi_info_data.main_obj_id] = multi_info_data
	local main_obj = Scene.Instance:GetObj(multi_info_data.main_obj_id)
	local main_role_active = ((nil ~= main_obj) and main_obj:IsRole())

	local ride_item_list = multi_info_data.ride_item_list
	if not IsEmptyTable(ride_item_list) then
		for k, v in pairs(ride_item_list) do
			if v.obj_id ~= multi_info_data.main_obj_id then
				local scene_obj = Scene.Instance:GetObj(v.obj_id)

				if scene_obj and scene_obj:IsRole() then
					self.data:AddMultiMountTakeData(v.obj_id, multi_info_data)

					if main_role_active then
						scene_obj:UpdateMultiMount()
					end
				end
			end
		end
	end

	if main_role_active then
		main_obj:UpdateMultiMount()
		main_obj:UpdateMultiMountSpecialApperaence()
	elseif nil == main_obj then
		self.wait_Update_multi_mount_list[multi_info_data.main_obj_id] = multi_info_data
	end
end

function MultiMountWGCtrl:OnSCDoubleMountRideDown(protocol)
	-- print_error("-------------OnSCDoubleMountRideDown-----------", protocol)
	self.data:DoubleMountRideDown(protocol)

	local scene_obj = Scene.Instance:GetObj(protocol.obj_id)
	if scene_obj and scene_obj:IsRole() then
		scene_obj:UpdateMultiMount()
		scene_obj:OutMultiMountUpdateJointAction(protocol)
	end

	local main_obj = Scene.Instance:GetObj(protocol.main_obj_id)

	if main_obj and main_obj:IsRole() then
		main_obj:UpdateMultiMountSpecialApperaence()
	end
end

function MultiMountWGCtrl:OnRoleEnter(obj_id)
	if nil ~= self.wait_Update_multi_mount_list[obj_id] then
		local scene_obj = Scene.Instance:GetObj(obj_id)
		
		if scene_obj and scene_obj:IsRole() then
			scene_obj:UpdateMultiMount()

			local ride_item_list = (self.wait_Update_multi_mount_list[obj_id] or {}).ride_item_list or {}
			if not IsEmptyTable(ride_item_list) then
				for k, v in pairs(ride_item_list) do
					local scene_obj = Scene.Instance:GetObj(v.obj_id)
					
					if scene_obj and scene_obj:IsRole() then
						scene_obj:UpdateMultiMount()
					end
				end
			end
		end
	end
end

function MultiMountWGCtrl:OnObjLeave(obj_id)
	if self.wait_Update_multi_mount_list[obj_id] then
		self.wait_Update_multi_mount_list[obj_id] = nil
	end
end

function MultiMountWGCtrl:OnItemChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		if self.data:IsUpLevelCostItem(change_item_id) then
			RemindManager.Instance:Fire(RemindName.NewAppearance_Multi_Mount)
			ViewManager.Instance:FlushView(GuideModuleName.NewAppearanceWGView, TabIndex.new_appearance_multi_mount)
		end
	end
end

----------------------------------------------PROTOCOL_END------------------------------------------