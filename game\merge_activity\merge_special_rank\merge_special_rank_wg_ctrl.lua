require("game/merge_activity/merge_special_rank/merge_special_rank_wg_data")
require("game/merge_activity/merge_special_rank/merge_special_rank_tip")

MergeSpecialRankWGCtrl = MergeSpecialRankWGCtrl or BaseClass(BaseWGCtrl)
function MergeSpecialRankWGCtrl:__init()
    if nil ~= MergeSpecialRankWGCtrl.Instance then
        ErrorLog("[MergeSpecialRankWGCtrl]:Attempt to create singleton twice!")
    end
    MergeSpecialRankWGCtrl.Instance = self

    self.data = MergeSpecialRankWGData.New()
    self.rank_tip = MergeSpecialRankTip.New()

    self:RegisterProtocol(SCCSAConsumeGoldInfo, "OnSCCSAConsumeGoldInfo")
end

function MergeSpecialRankWGCtrl:__delete()
    self.data:DeleteMe()
    self.data = nil

    self.rank_tip:DeleteMe()
    self.rank_tip = nil

    MergeSpecialRankWGCtrl.Instance = nil
end

function MergeSpecialRankWGCtrl:OnSCCSAConsumeGoldInfo(protocol)
    --print_error("OnSCCSAConsumeGoldInfo 《《《 ", protocol)
    self.data:SetInfo(protocol)

    self:FlushView()
    self:FlushTip()
end

local function MergeSpecialRankOperate(opera_type, param_1, param_2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_RANK
	protocol.opera_type = opera_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol:EncodeAndSend()
end

function MergeSpecialRankWGCtrl:RequestRankInfo()
    self:RequestGoldRankInfo()
end

function MergeSpecialRankWGCtrl:RequestGoldRankInfo()
    MergeSpecialRankOperate(MERGE_SPECITAL_RANK_OPERATE.GOLD_RANK_INFO)
end


function MergeSpecialRankWGCtrl:FlushView()
    ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2129)
end

function MergeSpecialRankWGCtrl:OpenTip()
    if not self.rank_tip:IsOpen() then
        self.rank_tip:Open()
    end
end

function MergeSpecialRankWGCtrl:FlushTip()
    if self.rank_tip:IsOpen() then
        self.rank_tip:Flush()
    end
end