local MONNEY_TYPE = {
	[1] = "a3_huobi_xianyu",
	[2] = "a3_huobi_bangyu",
}

-- local LIST_VIEW_HIGHT = {
-- 	[1] = Vector2(1050, 362),			--3*3
-- 	[2] = Vector2(1050, 530),			--3*4
-- }

function MergeActivityView:InitHeFuMiaoSha()

end

function MergeActivityView:LoadIndexCallBackHeFuMiaoSha()
	self.select_type = 1
	self.is_need_slider = true
	HeFuMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)

	if nil == self.miaosha_list_lingyu_view then
		local bundle, asset = "uis/view/merge_activity_ui/hefu_miaosha_ui_prefab", "hefu_miaosha_cell"
		self.miaosha_list_lingyu_view = AsyncBaseGrid.New()
		self.miaosha_list_lingyu_view:CreateCells({col = 4, itemRender = HeFuMiaoShaCell,
			list_view = self.node_list.miaosha_list_lingyu_view, assetBundle = bundle, assetName = asset, change_cells_num = 1})
		self.miaosha_list_lingyu_view:SetStartZeroIndex(false)
	end

	if nil == self.miaosha_list_yuanbao_view then
		local bundle, asset = "uis/view/merge_activity_ui/hefu_miaosha_ui_prefab", "hefu_miaosha_cell"
		self.miaosha_list_yuanbao_view = AsyncBaseGrid.New()
		self.miaosha_list_yuanbao_view:CreateCells({col = 6, itemRender = HeFuMiaoShaCell,
			list_view = self.node_list.miaosha_list_yuanbao_view, assetBundle = bundle, assetName = asset, change_cells_num = 1})
		self.miaosha_list_yuanbao_view:SetStartZeroIndex(false)
	end

	if not self.top_btn_list then
		self.top_btn_list = {}

		for i = 1, 2 do
			self.top_btn_list[i] = self.node_list["miaosha_top_btn_" .. i]
			self.top_btn_list[i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTopToggle, self, i))
		end

		self.top_btn_list[self.select_type].toggle.isOn = true
	end

	if not self.gift_list then
		self.gift_list = {}

		for i = 1, 5 do
			self.gift_list[i] = ItemCell.New(self.node_list["miaosha_item_cell_pos_" .. i])
			self.gift_list[i]:SetClickCallBack(BindTool.Bind(self.OnclickGiftItem, self, i))
			self.gift_list[i]:SetIsShowTips(false)
			self.gift_list[i]:SetCellBgEnabled(false)
			self.gift_list[i]:SetShowCualityBg(false)
			self.gift_list[i]:SetRightBottomTextVisible(true)
		end
	end

	if not self.discount_list then
		self.discount_list = {}

		for i = 1, 3 do
			self.discount_list[i] = HeFuMiaoShaDiscountCell.New(self.node_list["miaosha_discount_" .. i])
			self.discount_list[i]:SetIndex(i)
		end
	end


	--8.1 策划说把秒杀提醒屏蔽
	--self.node_list.miaosha_tips_toggle:SetActive(false)
	-- self.node_list.miaosha_tips_toggle.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickTipsToggle, self))

	-- local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	-- local value = PlayerPrefsUtil.GetInt("hefu_miaosha_toggle_" .. role_id)
	-- self.node_list.miaosha_tips_toggle.toggle.isOn = (value == 0)

	-- self.node_list.miaosha_tips_btn.button:AddClickListener(BindTool.Bind(self.OnClickTipsBtn, self))
	XUI.AddClickEventListener(self.node_list.miaosha_btn_recharge, BindTool.Bind(self.OnMiaoShaBtnRechargeClickHandler,self))

	self.node_list.hefu_miaosha_desc.text.text = Language.HeFuMiaoShaDesc.HefuMiaoshaDesc
end

function MergeActivityView:DeleteHeFuMiaoSha()
	if self.miaosha_list_lingyu_view then
		self.miaosha_list_lingyu_view:DeleteMe()
		self.miaosha_list_lingyu_view = nil
	end

	if self.miaosha_list_yuanbao_view then
		self.miaosha_list_yuanbao_view:DeleteMe()
		self.miaosha_list_yuanbao_view = nil
	end

	if self.gift_list then
		for k,v in pairs(self.gift_list) do
			v:DeleteMe()
		end
		self.gift_list = nil
	end

	if self.discount_list then
		for k,v in pairs(self.discount_list) do
			v:DeleteMe()
		end
		self.discount_list = nil
	end

	self.top_btn_list = nil
	self.select_type = nil
	self.is_need_slider = nil
	self.is_clicked_miaosha = false

	self:ClearMiaoShaCountDown()
end

function MergeActivityView:ShowIndexCallHeFuMiaoSha()
	self.is_clicked_miaosha = true
	for i=1,3 do
		local data = HeFuMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(i)
		if data.shop_id ~= 0 then
			local shop_lib_cfg = HeFuMiaoshaWGData.Instance:GetShopLibConfigByType(i, data.shop_id)

			if shop_lib_cfg then
				self.select_type = i
                self.top_btn_list[self.select_type].toggle.isOn = true
                HeFuMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)
				break
			end
		end
	end
end

function MergeActivityView:FlushHeFuMiaoSha()
    self:SetOutsideRuleTips(Language.HeFuMiaoShaDesc.TipsDesc)
	self:SetRuleInfo(Language.HeFuMiaoShaDesc.TipsContent, Language.HeFuMiaoShaDesc.TipsTitle)
	self.miaosha_data = HeFuMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(self.select_type)
	self:FlushBottomSlider()
	self:RefreshListView()
	self:FlushTopBtn()

	-- if self.select_type == 1 then
	-- 	self.node_list["tab_buttons"].transform.anchoredPosition = u3dpool.vec3(566, 37, 0)
	-- 	self.node_list["miaosha_list_lingyu_view"].transform.anchoredPosition = u3dpool.vec3(83, -60, 0)
	-- elseif self.select_type == 2 then
	-- 	self.node_list["tab_buttons"].transform.anchoredPosition = u3dpool.vec3(557, 82, 0)
	-- 	self.node_list["miaosha_list_lingyu_view"].transform.anchoredPosition = u3dpool.vec3(72, -30, 0)
	-- end

	self.node_list["miaosha_discount_content"]:SetActive(self.select_type == 1)
	self.node_list.miaosha_bottom_slider:SetActive(self.select_type == 1)
	local discount_cfg = HeFuMiaoshaWGData.Instance:GetMiaoShaDiscountCfgByGrade()
	for k, v in pairs(discount_cfg) do
		self.discount_list[k]:SetData(v)
	end
end

--刷新listview
function MergeActivityView:RefreshListView()
	--self.node_list.miaosha_mask_bg:SetActive(false)
	--self.node_list.miaosha_flush_time:SetActive(true)
	local data_list = HeFuMiaoshaWGData.Instance:GetHeFuMiaoShaListData(self.select_type)

	-- -- 备货期间不展示进度条、展示虚拟物品、展示遮罩
	-- if IsEmptyTable(data_list) then
	-- 	--self.node_list.miaosha_mask_bg:SetActive(true)
	-- 	self.node_list.miaosha_list_lingyu_view:SetActive(false)
	-- 	self.node_list.miaosha_bottom_slider:SetActive(false)
	-- 	--self.node_list.miaosha_flush_time:SetActive(false)
	-- 	return
	-- end

	-- self.node_list.miaosha_list_lingyu_view:SetActive(true)
	-- if self.is_need_slider then
	-- 	self.node_list.miaosha_list_lingyu_view.rect.sizeDelta = LIST_VIEW_HIGHT[1]
	-- else
	-- 	self.node_list.miaosha_list_lingyu_view.rect.sizeDelta = LIST_VIEW_HIGHT[2]
	-- end

	if self.select_type == 1 then
		self.miaosha_list_lingyu_view:SetDataList(data_list)
		self.node_list.miaosha_list_lingyu_view:SetActive(true)
		self.node_list.miaosha_list_yuanbao_view:SetActive(false)
	else
		self.miaosha_list_yuanbao_view:SetDataList(data_list)
		self.node_list.miaosha_list_yuanbao_view:SetActive(true)
		self.node_list.miaosha_list_lingyu_view:SetActive(false)
	end
end

local list_progress = {0.135, 0.353, 0.573, 0.792, 1}
--刷新进度条
function MergeActivityView:FlushBottomSlider()
	if not self.miaosha_data then
		return
	end

	local shop_lib_cfg = HeFuMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.miaosha_data.shop_id)
	if not shop_lib_cfg then
		--self.node_list.miaosha_bottom_slider:SetActive(false)
		return
	end

	-- 赠送额度为0不展示进度条
	if IsEmptyTable(shop_lib_cfg.gift_item) then
		--self.node_list.miaosha_bottom_slider:SetActive(false)
		self.is_need_slider = false
		return
	end
    local gift_quota_list_str = Split(shop_lib_cfg.gift_quota, ",")
    local gift_quota_list = {}
    for k, v in pairs(gift_quota_list_str) do
        gift_quota_list[k] = tonumber(v)
    end
	self.is_need_slider = true
	--self.node_list.miaosha_bottom_slider:SetActive(true)
    local progress_idx = 0
    if self.miaosha_data.total_quota == 0 then
        self.node_list["miaosha_slider_1"].slider.value = list_progress[1]
    elseif self.miaosha_data.total_quota >= gift_quota_list[#gift_quota_list] then
        self.node_list["miaosha_slider_1"].slider.value = 1
    else
        for k, v in pairs(gift_quota_list) do
            if self.miaosha_data.total_quota < v then
                progress_idx = k - 1
                break
            end
        end
        local start = gift_quota_list[progress_idx] or 0
        local start_pro = list_progress[progress_idx -1] or 0
        local add_pergress = (list_progress[progress_idx] - start_pro) * (self.miaosha_data.total_quota - start) / (gift_quota_list[progress_idx + 1] -  start) 
        self.node_list["miaosha_slider_1"].slider.value = list_progress[progress_idx] + add_pergress
    end

	for i = 1, 5 do
		self.node_list["miaosha_item_remind_" .. i]:SetActive(false)
		self.node_list["miaosha_item_can_get_bg_" .. i]:SetActive(false)
		self.node_list["miaosha_item_ylq_bg_" .. i]:SetActive(false)

		-- 物品
		if self.gift_list[i] and shop_lib_cfg.gift_item[i - 1] then
			--self.gift_list[i]:SetData(shop_lib_cfg.gift_item[i - 1])
			self.gift_list[i]:SetData({item_id = shop_lib_cfg.gift_item[i - 1].item_id})
			self.gift_list[i]:SetEffectRootEnable(false)
		end
		-- 额度
		if gift_quota_list[i] then
			local gift_quota = tonumber(gift_quota_list[i])
			self.node_list["miaosha_value_" .. i].text.text = gift_quota

			if self.miaosha_data.total_quota >= gift_quota and self.miaosha_data.quota_reward_tag[i] == 0 then
				--可领取未领取
				self.node_list["miaosha_item_remind_" .. i]:SetActive(true)
				self.node_list["miaosha_item_can_get_bg_" .. i]:SetActive(true)
			elseif self.miaosha_data.total_quota >= gift_quota and self.miaosha_data.quota_reward_tag[i] == 1 then
				--已领取
				--self.gift_list[i]:SetLingQuVisible(true)
				self.node_list["miaosha_item_ylq_bg_" .. i]:SetActive(true)
			else
				--self.gift_list[i]:SetLingQuVisible(false)
			end
		end
	end

	-- local other_cfg = HeFuMiaoshaWGData.Instance:GetOtherCfg()

	-- if other_cfg then
	-- 	self.node_list.miaosha_money_count.text.text = other_cfg.price
	-- end
	-- self.node_list.miaosha_raw_bg_2:SetActive(self.is_need_slider)
	self.node_list.miaosha_total_value.text.text = self.miaosha_data.total_quota

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE)

	if not activity_info or activity_info.end_time == -1 then
		return
	end

	self:SetActRemainTime(TabIndex.merge_activity_2128, activity_info.end_time)

end


--专属类型切换
function MergeActivityView:OnClickTopToggle(index, is_on)
	if is_on then
		if self.select_type == index then
			return
		end	
		self.select_type = index
		HeFuMiaoshaWGData.Instance:SetMiaoShaSelectType(self.select_type)
		self:FlushHeFuMiaoSha()
	end
end

--刷新顶部按钮
function MergeActivityView:FlushTopBtn()
	self:ClearMiaoShaCountDown()

	if not self.miaosha_data then
		return
	end

	for i = 1, 2 do
		local data = HeFuMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(i)
		if data.shop_id ~= 0 then
			local shop_lib_cfg = HeFuMiaoshaWGData.Instance:GetShopLibConfigByType(i, data.shop_id)

			if shop_lib_cfg then
				self.node_list["miaosha_top_btn_" .. i]:SetActive(true)
			else
				self.node_list["miaosha_top_btn_" .. i]:SetActive(false)
			end
		else
			self.node_list["miaosha_top_btn_" .. i]:SetActive(false)
		end

		self.node_list["miaosha_remind_" .. i]:SetActive(HeFuMiaoshaWGData.Instance:GetMiaoShaRemindByType(i) == 1)
		--self.node_list["txt_new_" .. i]:SetActive(false)--HeFuMiaoshaWGData.Instance:GetIsShowNewFalgByType(i) == 1)
	end

	local time = self.miaosha_data.refresh_time - TimeWGCtrl.Instance:GetServerTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE)

	if not activity_info or activity_info.end_time == -1 then
		--self.node_list.miaosha_flush_time.text.text = ""
		return
	end

	local end_time = activity_info.end_time - TimeWGCtrl.Instance:GetServerTime()

	--8.1 策划说把刷新时间屏蔽
	-- if time > 0 and time <= end_time then
	-- 	CountDownManager.Instance:AddCountDown("hefu_miaosha_countdown", BindTool.Bind1(self.MiaoShaChangeTime, self), BindTool.Bind1(self.MiaoShaCompleteTime, self), nil, time, 1)
	-- 	self:MiaoShaChangeTime(0, time)
	-- else
		--self.node_list.miaosha_flush_time.text.text = ""
	-- end
end

--计时器
function MergeActivityView:MiaoShaChangeTime(elapse_time, total_time)
	local time = total_time - elapse_time

	if time > 0 then
		if self.node_list.miaosha_flush_time then
			self.node_list.miaosha_flush_time.text.text = string.format(Language.HeFuMiaoShaDesc.RefreshTime, TimeUtil.FormatSecond(time, 3))
		end

		local time_list = TimeUtil.Format2TableDHM2(time)

		-- if self.node_list.miaosha_flush_time_2 then
		-- 	self.node_list.miaosha_flush_time_2.text.text = string.format("%02d：%02d：%02d", time_list.hour, time_list.min, time_list.sec)
		-- end
	else
		--self.node_list.miaosha_flush_time.text.text = ""
	end
end

--计时完成
function MergeActivityView:MiaoShaCompleteTime()
	self:ClearMiaoShaCountDown()
	self:MiaoShaChangeTime(0, 0)
end

--清除计时器
function MergeActivityView:ClearMiaoShaCountDown()
	if CountDownManager.Instance:HasCountDown("hefu_miaosha_countdown") then
		CountDownManager.Instance:RemoveCountDown("hefu_miaosha_countdown")
	end
end

-- 秒杀提醒勾选
function MergeActivityView:OnClickTipsToggle(is_on)
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	local value = (is_on == true) and 0 or 1
	PlayerPrefsUtil.SetInt("hefu_miaosha_toggle_" .. role_id, value)
end

-- 累计奖励点击事件
function MergeActivityView:OnclickGiftItem(index)
	if not self.miaosha_data then
		return
	end

	if not self.miaosha_data.quota_reward_tag[index] then
		return
	end

	local shop_lib_cfg = HeFuMiaoshaWGData.Instance:GetShopLibConfigByType(self.select_type, self.miaosha_data.shop_id)

	if not shop_lib_cfg then
		return
	end

	if shop_lib_cfg.gift_item == 0 or shop_lib_cfg.gift_quota == 0 then
		return
	end

	local gift_quota_list = Split(shop_lib_cfg.gift_quota, ",")

	if not gift_quota_list[index] then
		return
	end

	if self.miaosha_data.total_quota >= tonumber(gift_quota_list[index]) and self.miaosha_data.quota_reward_tag[index] == 0 then
		--请求
		ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_RECEIVE_GIFT, self.select_type, index - 1)

		if shop_lib_cfg and shop_lib_cfg.gift_item[index - 1] then
			local reward_list = {[1] = shop_lib_cfg.gift_item[index - 1]}
			TipWGCtrl.Instance:ShowGetReward(nil, reward_list, false)
		end
	else
		--提示
		TipWGCtrl.Instance:OpenItem(shop_lib_cfg.gift_item[index - 1])
	end

end

function MergeActivityView:OnMiaoShaBtnRechargeClickHandler()
	FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end


-- function MergeActivityView:OnClickTipsBtn()
-- 	local theme_cfg = HeFuMiaoshaWGData.Instance:GetThemeCfgByTabIndex(TabIndex.merge_activity_2128)
-- 	if theme_cfg then
-- 		TipWGCtrl.Instance:SetRuleContent(theme_cfg.rule_desc, theme_cfg.tab_name)
-- 	end
-- end

-----------------------------------------------------------HeFuMiaoShaCell-------------------------------------------------------------------------------

HeFuMiaoShaCell = HeFuMiaoShaCell or BaseClass(BaseGridRender)

function HeFuMiaoShaCell:LoadCallBack()
	self.node_list.buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self))

	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function HeFuMiaoShaCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function HeFuMiaoShaCell:OnFlush()
	if not self.data then
		return
	end
	local select_type = HeFuMiaoshaWGData.Instance:GetMiaoShaSelectType()

	local item_lib_cfg = HeFuMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
	if not item_lib_cfg then
		return
	end

	local cur_discount, cur_price, discount_value
	if select_type == 1 then
		cur_discount = HeFuMiaoshaWGData.Instance:GetMiaoShaCurDiscountDisplay()
		discount_value = HeFuMiaoshaWGData.Instance:GetMiaoShaCurDiscount()
		cur_price = math.floor(item_lib_cfg.original_price * discount_value)
	else
		cur_discount = item_lib_cfg.discount
		cur_price = item_lib_cfg.current_price
	end

	local bundel ,asset = ResPath.GetCommonIcon(MONNEY_TYPE[select_type])
	self.node_list.cur_money_icon.image:LoadSprite(bundel ,asset, function ()
		self.node_list.cur_money_icon.image:SetNativeSize()
	end)

	self.node_list.original_price_value.text.text = item_lib_cfg.original_price
	self.node_list.original_money_icon.image:LoadSprite(ResPath.GetCommonIcon(MONNEY_TYPE[select_type]))
	self.node_list.original_money_icon.image:SetNativeSize()

    self.node_list.cur_price_value.text.text = cur_price

	self.item_cell:SetData({item_id = item_lib_cfg.item_id, num = 1, is_bind = item_lib_cfg.is_bind})

	self.node_list.discount_desc.text.text =  string.format(Language.HeFuMiaoShaDesc.DisCount, cur_discount)

	local is_max_discount = HeFuMiaoshaWGData.Instance:GetMiaoShaIsMaxDiscount()
	local buy_discount_flag = select_type == 1 and item_lib_cfg.discount == 1 and not is_max_discount

	self.node_list.buy_discount_flag:SetActive(buy_discount_flag)
	self.node_list.original_price:SetActive(not buy_discount_flag and cur_discount > 0)

	local vip_level = VipWGData.Instance:IsVip() and RoleWGData.Instance.role_vo.vip_level or 0
	local role_level = RoleWGData.Instance:GetRoleLevel()

	self.node_list.top_bg:SetActive(false)
	if vip_level < item_lib_cfg.vip_level_limit then
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(false)
		--self.node_list.limit_desc:SetActive(true)
		--self.node_list.limit_desc_bg:SetActive(true)
		--self.node_list.limit_desc.text.text = string.format(Language.HeFuMiaoShaDesc.LimitDesc1, item_lib_cfg.vip_level_limit)
	elseif role_level < item_lib_cfg.level_limit then
		self.node_list.sellout_flag:SetActive(false)
		self.node_list.buy_btn:SetActive(false)
		--self.node_list.limit_desc:SetActive(true)
		--self.node_list.limit_desc_bg:SetActive(true)
		--self.node_list.limit_desc.text.text = string.format(Language.HeFuMiaoShaDesc.LimitDesc2, item_lib_cfg.level_limit)
	else
		local can_buy = self.data.last_num > 0 and self.data.bought_times < item_lib_cfg.buy_limit_value
		--self.node_list.limit_desc:SetActive(false)
		--self.node_list.limit_desc_bg:SetActive(false)
		self.node_list.buy_btn:SetActive(can_buy)
		self.node_list.sellout_flag:SetActive(not can_buy)
		self.node_list.top_bg:SetActive(can_buy and cur_discount > 0)

		-- self.node_list.cur_money_icon:SetActive(can_buy)
		self.node_list.buy_btn_text.text.text = cur_price
	end

	local has_vip_limit = item_lib_cfg.vip_level_limit > 0
	self.node_list.vip_limit_bg:SetActive(has_vip_limit)
	self.node_list.vip_limit_txt.text.text = string.format(Language.HeFuMiaoShaDesc.LimitDesc1, item_lib_cfg.vip_level_limit)
	-- local btn_pos_y = has_vip_limit and -96 or -128
	-- RectTransform.SetAnchoredPositionXY(self.node_list.buy_btn.rect, 0, btn_pos_y)

	-- local new_falg = HeFuMiaoshaWGData.Instance:GetNewFlagByShopId(self.data.id) or 0

	-- self.node_list.txt_new:SetActive(false)--new_falg == 1) 

	-- self.node_list.surplus_desc:SetActive(item_lib_cfg.is_show == 1)
	-- local res_name = item_lib_cfg.is_show == 1 and "miaosha_img_2" or "miaosha_img_6"
	-- local bundle = "uis/view/merge_activity_ui/hefu_miaosha_ui/images_atlas"
	-- self.node_list.top_bg.image:LoadSprite(bundle, res_name, function ()
	-- 	self.node_list.top_bg.image:SetNativeSize()
	-- end)

	local str = string.format(Language.HeFuMiaoShaDesc.PresonCount, self.data.bought_times, item_lib_cfg.buy_limit_value)
	--self.node_list.limit_bg:SetActive(item_lib_cfg.buy_limit_type == 1 or item_lib_cfg.buy_limit_type == 5)

	-- local color = self.data.bought_times >= item_lib_cfg.buy_limit_value and COLOR3B.RED or COLOR3B.C5
	-- self.node_list.limit_count_desc.text.text = ToColorStr(str, color)
	self.node_list.limit_count_desc.text.text = str
	self.item_cell:SetBindIconVisible(false)
	self.item_cell:SetRightBottomTextVisible(false)
	-- self.item_cell:SetRightDownCellBVisible(false)

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_lib_cfg.item_id)
    if IsEmptyTable(item_cfg) then
		return
    end

	self.node_list["item_name"].text.text = item_cfg.name
end

function HeFuMiaoShaCell:OnClickBuyBtn()
    local item_lib_cfg = HeFuMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
	if not item_lib_cfg then
		return
	end

    local buy_func = function(num)
        local select_type = HeFuMiaoshaWGData.Instance:GetMiaoShaSelectType()
        local hefu_miaosha_data = HeFuMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(select_type)
        if not hefu_miaosha_data then
            return
        end
        local item_lib_cfg = HeFuMiaoshaWGData.Instance:GetItemLibConfigById(self.data.id)
        if not item_lib_cfg then
            return
        end
        local state = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE)
        if not state then
            TipWGCtrl.Instance:ShowSystemMsg(Language.HeFuMiaoShaDesc.ActCloseTips)
            return
        end

        local shop_lib_cfg = HeFuMiaoshaWGData.Instance:GetShopLibConfigByType(select_type, hefu_miaosha_data.shop_id)
        if not shop_lib_cfg then
            return
        end
        
        --local cur_price = item_lib_cfg.current_price
        --local select_type = HeFuMiaoshaWGData.Instance:GetMiaoShaSelectType()
		local cur_discount, cur_price, discount_value
		if select_type == 1 then
			cur_discount = HeFuMiaoshaWGData.Instance:GetMiaoShaCurDiscountDisplay()
			discount_value = HeFuMiaoshaWGData.Instance:GetMiaoShaCurDiscount()
			cur_price = math.floor(item_lib_cfg.original_price * discount_value)
		else
			cur_discount = item_lib_cfg.discount
			cur_price = item_lib_cfg.current_price
		end

        if select_type == 2 then --绑玉
            local gold = RoleWGData.Instance:GetRoleInfo().gold
            local bind_gold = RoleWGData.Instance:GetRoleInfo().bind_gold
            if bind_gold < cur_price and (bind_gold + gold) >= cur_price then
                local ok_func = function()
                    ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_BUY, hefu_miaosha_data.shop_id, self.data.id, num)
                end
                TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
                return
            end
        end
        ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_TIMED_SPIKE, OA_TIMED_SPIKE_OPERA_TYPE.OA_TIMED_SPIKE_OPERA_TYPE_BUY, hefu_miaosha_data.shop_id, self.data.id, num)
    end

    if item_lib_cfg.buy_limit_value - self.data.bought_times > 1 then
        HeFuMiaoShaWGCtrl.Instance:OpenBatchBuyView(item_lib_cfg.item_id, 1, 1,  item_lib_cfg.buy_limit_value - self.data.bought_times, buy_func)
    else
        buy_func(1)
    end
end

HeFuMiaoShaDiscountCell = HeFuMiaoShaDiscountCell or BaseClass(BaseRender)
function HeFuMiaoShaDiscountCell:LoadCallBack()

end

function HeFuMiaoShaDiscountCell:__delete()

end

function HeFuMiaoShaDiscountCell:OnFlush()
	if self.data == nil then
		return
	end

	self.node_list.title_name1.text.text = string.format(Language.HeFuMiaoShaDesc.DiscountDesc, NumberToChinaNumber(self.data.buy_count))
	self.node_list.title_name2.text.text = string.format(Language.HeFuMiaoShaDesc.DiscountDesc, NumberToChinaNumber(self.data.buy_count))

	local display_discount = self.data.discount
	while display_discount % 10 == 0 do
		display_discount = display_discount / 10
	end

	self.node_list.discount_num1.text.text = display_discount
	self.node_list.discount_num2.text.text = display_discount

	local data = HeFuMiaoshaWGData.Instance:GetHeFuMiaoShaInfoByType(1)

	local is_select = (data.buy_count == self.data.buy_count) or (data.buy_count > self.data.buy_count and self.index == 3)
	self.node_list.select_bg:SetActive(is_select)
	self.node_list.reach_bg:SetActive(data.buy_count >= self.data.buy_count)
	self.node_list.not_reach_bg:SetActive(data.buy_count < self.data.buy_count)
end