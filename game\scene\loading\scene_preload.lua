ScenePreload = ScenePreload or BaseClass()

ScenePreload.main_scene_bundle_name = ""
ScenePreload.main_scene_asset_name = ""
ScenePreload.last_load_bundle_name = ""
ScenePreload.last_load_asset_name = ""
ScenePreload.first_load = true
ScenePreload.cache_cg_t = {}
ScenePreload.loaded_ui_scenes = {}	-- 已加载的UI场景列表，用于避免重复加载

local is_loading_pre_fuben_scene = false
local LOAD_TYPE =
{
	DOWNLOAD = 1,				 	-- 下载bundle
	LOAD_MAIN_SCENE = 2,			-- 加载主场景
	LOAD_UNFREE_GAMEOBJECT = 3,		-- 加载不释放的gameobject(即长久在对象池中)
	LOAD_CG = 4,					-- 预加载CG
	REDUCE_MEM = 5,					-- 清理内存
	COMBINE_SCENE = 6,				-- 合并场景
	GC_REDUCE_MEM = 7,				-- GC方式清理内存
	LOAD_UI_SCENE = 8,				-- 加载UI场景
	LOAD_ROLE_SKILL_VIDEO = 9,		-- 预加载角色技能视频
	LOAD_FUBEN_SCENE = 10,			-- 预加载副本场景
}

local Profiler = UnityEngine.Profiling.Profiler
local SceneManager = UnityEngine.SceneManagement.SceneManager
local load_mode_single = UnityEngine.SceneManagement.LoadSceneMode.Single
local load_mode_additive = UnityEngine.SceneManagement.LoadSceneMode.Additive
function ScenePreload:__init()
	ScenePreload.Instance = self
	self.progress_fun = nil
	self.main_complete_fun = nil
	self.complete_fun = nil
	self.load_list = {}
	self.load_num_once = 1		-- 每次加载个数
	self.loading_num = 0		-- 正在加载的数量
	self.update_retry_times = 0
	self.load_retry_times = 0
	self.load_ui_scene_retry_times = 0
	self.download_scene_id = 0
	self.cache_gameobj_list = {}
	self.is_stop_load = false 	-- 是否停止加载

	self.bytes_total = 0
	self.bytes_loaded = 0
	self.bytes_vir_loaded = 0 	-- 虚拟加载，为了让加载本地的资源时，进度条更平滑
	self.cur_load_scene_id = 0
	self.load_logs = {}
	self.date_stamp = os.date("%Y-%m-%d", os.time())
	self.time_stamp = os.date("%H-%M-%S", os.time())

	Runner.Instance:AddRunObj(self, 8)
end

function ScenePreload:__delete()
	self.is_stop_load = true
	for _, v in ipairs(self.cache_gameobj_list) do
		ResPoolMgr:Release(v)
	end
	self.cache_gameobj_list = {}

	Runner.Instance:RemoveRunObj(self)
	self:RemoveDelayReloadLevelTimer()
	self:RemoveDelayUISceneReloadLevelTimer()
	self:StopPreviousFuBenSceneLoading()
end

function ScenePreload:Update(now_time, elapse_time)
	if self.is_stop_load then
		return
	end

	if self.bytes_vir_loaded > 0 then
		local inc_bytes = self.bytes_vir_loaded
		self.bytes_vir_loaded = 0
		self.bytes_loaded = self.bytes_loaded + inc_bytes
		self:CheckLoadComplete()
		return
	end

	if #self.load_list <= 0 then
		return
	end

	if self.loading_num > 0 then
		return
	end

	local num = math.min(self.load_num_once, #self.load_list)
	for i = 1, num do
		local t = table.remove(self.load_list, 1)

		if LOAD_TYPE.DOWNLOAD == t.load_type then
			self:DownloadBundle(t.bundle, t.bytes_total)

		elseif LOAD_TYPE.LOAD_MAIN_SCENE == t.load_type then
			self:LoadUnityMainScene(t.bundle_name, t.asset_name, t.bytes_total)

		elseif LOAD_TYPE.LOAD_UNFREE_GAMEOBJECT == t.load_type then
			self:LoadUnFreeGameObject(t.bundle_name, t.asset_name, t.bytes_total)

		elseif LOAD_TYPE.LOAD_CG == t.load_type then
			self:LoadCG(t.bundle_name, t.asset_name, t.bytes_total)

		elseif LOAD_TYPE.LOAD_ROLE_SKILL_VIDEO == t.load_type then
			self:LoadRoleSkillVideo(t.bundle_name, t.asset_name, t.bytes_total)

		elseif LOAD_TYPE.REDUCE_MEM == t.load_type then
			self:ReduceMem(t.bytes_total)

		elseif LOAD_TYPE.GC_REDUCE_MEM == t.load_type then
			self:ReduceMem(t.bytes_total)

		elseif LOAD_TYPE.COMBINE_SCENE == t.load_type then
			self:CombineScene(t.bytes_total)

		elseif LOAD_TYPE.LOAD_UI_SCENE == t.load_type then
			self:LoadUIScene(t.bundle_name, t.asset_name, t.bytes_total)

		elseif LOAD_TYPE.LOAD_FUBEN_SCENE == t.load_type then
			self:LoadFuBenScene(t.bytes_total)
		end
	end
end

function ScenePreload:StartLoad(scene_id, load_list, download_scene_id, progress_fun, main_complete_fun, complete_fun)
	self.progress_fun = progress_fun
	self.main_complete_fun = main_complete_fun
	self.complete_fun = complete_fun
	self.update_retry_times = 0
	self.load_retry_times = 0
	self.is_stop_load = false

	-- 停止之前正在进行的副本场景预加载，避免loading_num计数不匹配
	self:StopPreviousFuBenSceneLoading()

	self.load_list = load_list or {}
	self.download_scene_id = download_scene_id
	self.loading_num = 0
	self.bytes_loaded = 0
	self.bytes_vir_loaded = 0
	self.bytes_total = self:GetBytesTotal()
	self.cur_load_scene_id = scene_id or 0

	CgManager.Instance:DelCacheCgs()

	-- 只有在load_list为空时才立即检查完成，否则等待Update方法处理
	if #self.load_list == 0 then
		self:CheckLoadComplete()
	end

	if ScenePreload.first_load then
		ScenePreload.first_load = false
		LuaGC:StartSample()
	end
end

function ScenePreload:GetBytesTotal()
	local bytes_total = 0
	for _, v in ipairs(self.load_list) do
		bytes_total = bytes_total + v.bytes_total
	end

	return bytes_total
end

-- 预加载技能进对象池
function ScenePreload:LoadUnFreeGameObject(bundle_name, asset_name, bytes)
	self.loading_num = self.loading_num + 1
	self.bytes_vir_loaded = self.bytes_vir_loaded + bytes

	ResPoolMgr:GetEffectAsync(bundle_name, asset_name, function(obj)
		if nil ~= obj then
			table.insert(self.cache_gameobj_list, obj)
		end

		self.loading_num = self.loading_num - 1
		self:CheckLoadComplete()
	end)
end

-- 预加载CG到内存（在用完之前不释放）
function ScenePreload:LoadCG(bundle_name, asset_name, bytes)
	self.loading_num = self.loading_num + 1
	self.bytes_vir_loaded = self.bytes_vir_loaded + bytes

	CgManager.Instance:PreloadCacheCg(bundle_name, asset_name, function ()
		self.loading_num = self.loading_num - 1
		self:CheckLoadComplete()
	end)
end

-- 预加载角色技能到内存（在用完之前不释放）
function ScenePreload:LoadRoleSkillVideo(bundle_name, asset_name, bytes)
	self.loading_num = self.loading_num + 1
	self.bytes_vir_loaded = self.bytes_vir_loaded + bytes

	ResPoolMgr:GetVideoClip(bundle_name, asset_name,function ()
		self.loading_num = self.loading_num - 1
		self:CheckLoadComplete()
	end)
end

-- 获取设备配置分级
function ScenePreload:GetDeviceMemoryTier()
	local sysInfo = UnityEngine.SystemInfo
	local systemMemorySize = sysInfo.systemMemorySize  -- MB
	local graphicsMemorySize = sysInfo.graphicsMemorySize  -- MB
	
	-- 物理内存分级标准（单位：MB）
	local MEMORY_TIER = {
		LOW = 3072,     -- 3GB以下为低配
		MID = 6144,     -- 3-6GB为中配  
		HIGH = 12288    -- 6-12GB为高配，12GB以上为旗舰
	}
	
	local GRAPHICS_TIER = {
		LOW = 1024,     -- 1GB显存以下为低配
		MID = 2048,     -- 1-2GB为中配
		HIGH = 4096     -- 2-4GB为高配，4GB以上为旗舰
	}
	
	-- 综合评估设备等级
	local memoryTier = 1
	local graphicsTier = 1
	
	if systemMemorySize <= MEMORY_TIER.LOW then
		memoryTier = 1  -- 低配
	elseif systemMemorySize <= MEMORY_TIER.MID then
		memoryTier = 2  -- 中配
	elseif systemMemorySize <= MEMORY_TIER.HIGH then
		memoryTier = 3  -- 高配
	else
		memoryTier = 4  -- 旗舰
	end
	
	if graphicsMemorySize <= GRAPHICS_TIER.LOW then
		graphicsTier = 1  -- 低配
	elseif graphicsMemorySize <= GRAPHICS_TIER.MID then
		graphicsTier = 2  -- 中配
	elseif graphicsMemorySize <= GRAPHICS_TIER.HIGH then
		graphicsTier = 3  -- 高配
	else
		graphicsTier = 4  -- 旗舰
	end
	
	-- 取内存和显存的较低等级作为最终等级，避免木桶效应
	local finalTier = math.min(memoryTier, graphicsTier)
	
	return finalTier, systemMemorySize, graphicsMemorySize
end

-- 减少内存（细化版本）
function ScenePreload:ReduceMem(bytes)
	self.bytes_vir_loaded = self.bytes_vir_loaded + bytes
	self.loading_num = self.loading_num + 1

	-- 预防lua和C#相互有引用，清不掉
	collectgarbage("collect")
	if nil ~= System.GC then -- 兼容旧版本
		System.GC.Collect()
	end

	-- 获取设备等级
	local deviceTier, systemMem, graphicsMem = self:GetDeviceMemoryTier()
	
	-- 在内存过高时再清理缓存，根据设备等级设定不同阈值
	-- 已分配内存
	local reserver_value = tonumber(tostring(Profiler.GetTotalAllocatedMemoryLong()))
	local all_reserver = math.floor(reserver_value / 1024 / 1024)

	local clear_value = 0
	if deviceTier == 1 then
		-- 低配设备：更激进的内存管理
		clear_value = UNITY_IOS and 512 or 600
		if LOW_GRAPHICS_MEMORY then
			clear_value = UNITY_IOS and 384 or 450
		end
	elseif deviceTier == 2 then
		-- 中配设备：适中的内存管理
		clear_value = UNITY_IOS and 768 or 900
		if LOW_GRAPHICS_MEMORY then
			clear_value = UNITY_IOS and 600 or 700
		end
	elseif deviceTier == 3 then
		-- 高配设备：相对宽松的内存管理
		clear_value = UNITY_IOS and 1200 or 1400
		if LOW_GRAPHICS_MEMORY then
			clear_value = UNITY_IOS and 900 or 1100
		end
	else
		-- 旗舰设备：更宽松的内存管理
		clear_value = UNITY_IOS and 1600 or 1800
		if LOW_GRAPHICS_MEMORY then
			clear_value = UNITY_IOS and 1200 or 1400
		end
	end

	local is_need_clear = all_reserver >= clear_value
	if is_need_clear then
		ResPoolMgr:Clear()
		BundleCache:Clear()
	end
	
	UnityEngine.Resources.UnloadUnusedAssets()
	self.loading_num = self.loading_num - 1
	self:CheckLoadComplete()
end

function ScenePreload:CombineScene(bytes)
	-- 预留内存
	local reserver_value = tonumber(tostring(Profiler.GetTotalReservedMemoryLong()))
	local all_reserver = math.floor(reserver_value / 1024 / 1024)
	
	-- 获取设备等级
	local deviceTier, systemMem, graphicsMem = self:GetDeviceMemoryTier()
	local max_vertex_same_mesh = 0
	local max_vertex_one_mesh = 5000
	local opera_mem = 600
	
	-- 根据设备等级设定不同的合批参数
	if deviceTier == 1 then
		-- 低配设备：保守的合批策略
		max_vertex_one_mesh = 3000
		opera_mem = 400
		max_vertex_same_mesh = 8000
	elseif deviceTier == 2 then
		-- 中配设备：适中的合批策略
		max_vertex_one_mesh = 5000
		opera_mem = 700
		max_vertex_same_mesh = 15000
	elseif deviceTier == 3 then
		-- 高配设备：积极的合批策略
		max_vertex_one_mesh = 8000
		opera_mem = 1200
		max_vertex_same_mesh = 25000
	else
		-- 旗舰设备：最积极的合批策略
		max_vertex_one_mesh = 12000
		opera_mem = 1800
		max_vertex_same_mesh = 40000
	end
	
	-- win包不处理
	if not IS_LOCLA_WINDOWS_DEBUG_EXE and not UNITY_EDITOR then
		if all_reserver >= opera_mem then
			local cur_reserver = math.floor(all_reserver - opera_mem)
			local reduction_factor = deviceTier == 1 and 8000 or 
								   deviceTier == 2 and 6000 or 
								   deviceTier == 3 and 4000 or 3000
			
			max_vertex_same_mesh = math.floor(max_vertex_same_mesh - (cur_reserver / 100) * reduction_factor)
			local min_vertex = deviceTier == 1 and 5000 or 
							  deviceTier == 2 and 8000 or 
							  deviceTier == 3 and 12000 or 15000
			max_vertex_same_mesh = max_vertex_same_mesh <= min_vertex and min_vertex or max_vertex_same_mesh
		end
	end

	SceneOptimizeMgr.SetStaticBatchThreadshold(max_vertex_same_mesh, max_vertex_one_mesh)

	self.loading_num = self.loading_num + 1
	self.bytes_vir_loaded = self.bytes_vir_loaded + bytes
	SceneOptimizeMgr.StaticBatch()
	self.loading_num = self.loading_num - 1
end

-- 下载bundle
function ScenePreload:DownloadBundle(bundle, bytes)
	self.loading_num = self.loading_num + 1
	local old_progress = self.bytes_loaded

	ResMgr:UpdateBundle(bundle,
		function(progress, download_speed, bytes_downloaded, content_length)

		end,

		function(error_msg)
			if self.is_stop_load then
				return
			end
			if error_msg ~= nil and error_msg ~= "" then
				self.loading_num = self.loading_num - 1
				print_log("下载: ", bundle, " 失败: ", error_msg, os.time())

				self.bytes_loaded = old_progress
				self:OnDownloadBundleFail(bundle, bytes)
			else
				if self.update_retry_times > 0 then
					self.update_retry_times = 0
					ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url)
				end

				self.bytes_loaded = old_progress + bytes
				self.loading_num = self.loading_num - 1
				self:CheckLoadComplete()
			end
		end)
end

-- 下载bundle失败后再尝试
function ScenePreload:OnDownloadBundleFail(bundle, bytes)
	if self.update_retry_times < 8 then
		self.update_retry_times = self.update_retry_times + 1
		if GLOBAL_CONFIG.param_list.cdn_url2 ~= nil then -- 切换下载地址
			if self.update_retry_times % 2 == 1
				and nil ~= GLOBAL_CONFIG.param_list.cdn_url2
				and "" ~= GLOBAL_CONFIG.param_list.cdn_url2 then
				ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url2)
			else
				ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url)
			end
		end

		print_log("retry download ", bundle, ", retry times=", self.update_retry_times, ", url=", ResMgr.downloading_url)
		self:DownloadBundle(bundle, bytes)
	else
		self.is_stop_load = true
		TipWGCtrl.Instance:OpenConfirmAlertTips(Language.Common.LoadSceneFail, function ()
			AgentAdapter.Instance:Logout()
		end, nil, nil, true)
	end
end

-- 加载unity主场景
function ScenePreload:LoadUnityMainScene(bundle_name, asset_name, bytes)
	self.loading_num = self.loading_num + 1
	self.bytes_vir_loaded = self.bytes_vir_loaded + bytes
	self.main_has_compelet = false

	local is_preload_fu_ben_scene = self.CheckAndOperaPreScene(bundle_name, asset_name, true)

	-- 相同场景跳过加载
	local is_same_scene = ScenePreload.main_scene_bundle_name == bundle_name and ScenePreload.main_scene_asset_name == asset_name
							and
							ScenePreload.last_load_bundle_name == bundle_name and ScenePreload.last_load_asset_name == asset_name

	if is_same_scene or is_preload_fu_ben_scene then
		-- 直接跳过虚拟加载进度条
		self.bytes_vir_loaded = self.bytes_vir_loaded - bytes
		self.bytes_loaded = self.bytes_loaded + bytes

		-- 检查并跳过已加载的资源，但保留未加载的资源
		local remove_indices = {}
		for k,v in ipairs(self.load_list) do
			local should_skip = false

			if LOAD_TYPE.COMBINE_SCENE == v.load_type then
				-- 场景合批跳过（主场景相同）
				should_skip = true
			elseif LOAD_TYPE.LOAD_UI_SCENE == v.load_type then
				-- UI场景是否已经加载过
				local scene = SceneManager.GetSceneByName(v.asset_name)
				should_skip = scene ~= nil and not IsNil(scene) and scene:IsValid()
				if ScenePreload.loaded_ui_scenes[v.asset_name] and not should_skip then
					print_error("-----LoadUnityMainScene 跳过UI场景加载异常-----", asset_name)
				end
			elseif LOAD_TYPE.LOAD_FUBEN_SCENE == v.load_type then
				-- 检查是否有预加载的副本场景
				should_skip = Scene.Instance and Scene.Instance:HasAnyPreloadedFuBenScene()
			end

			if should_skip then
				self.bytes_loaded = self.bytes_loaded + v.bytes_total
				table.insert(remove_indices, k)
			end
		end

		for i = #remove_indices, 1, -1 do
			table.remove(self.load_list, remove_indices[i])
		end

		self:OnUnityMainSceneLoadComplete()
		return
	end

	ScenePreload.last_load_bundle_name = bundle_name
	ScenePreload.last_load_asset_name = asset_name

	ResMgr:LoadLevelSync("scenesempytscene", "empytscene", load_mode_single,
		function()
			self:TryLoadLevelSync(bundle_name, asset_name, load_mode_single)
		end)

	-- self:TryLoadLevelSync(bundle_name, asset_name, load_mode_single)
end

-- 尝试多次加载场景，加载场景因为有可能本地资源损坏（但会触发重新下载）。
-- function ScenePreload:TryLoadLevelAsync(bundle_name, asset_name, load_mode)
-- 	ResMgr:LoadLevelAsync(bundle_name, asset_name, load_mode,
-- 		function (is_succ)
-- 			if is_succ then
-- 				ScenePreload.main_scene_bundle_name = bundle_name
-- 				ScenePreload.main_scene_asset_name = asset_name
-- 				self:OnUnityMainSceneLoadComplete()
-- 			else
-- 				if self.load_retry_times < 8 then
-- 					self.load_retry_times = self.load_retry_times + 1
-- 					self:TryLoadLevelAsync(bundle_name, asset_name, load_mode)
-- 				else
-- 					TipWGCtrl.Instance:OpenConfirmAlertTips(Language.Common.LoadSceneFail, function ()
-- 						AgentAdapter.Instance:Logout()
-- 					end, nil, nil, true)
-- 				end
-- 			end
-- 		end)
-- end

function ScenePreload:TryLoadLevelSync(bundle_name, asset_name, load_mode)
	self:RemoveDelayReloadLevelTimer()

	Scene.Instance:ClearPreloadedFuBenSceneInfo()
	ScenePreload.mini_camera = nil
	is_loading_pre_fuben_scene = false

	ResMgr:LoadLevelSync(bundle_name, asset_name, load_mode,
		function (is_succ)
			if self.is_stop_load then
				return
			end

			if is_succ then
				ScenePreload.main_scene_bundle_name = bundle_name
				ScenePreload.main_scene_asset_name = asset_name
				Scene.CurShowSceneBundle = bundle_name
				Scene.CurShowSceneAsset = asset_name
				-- 清理UI场景记录，因为Single模式会卸载所有已加载的场景
				ScenePreload.loaded_ui_scenes = {}
				self:OnUnityMainSceneLoadComplete()
			else
				if self.load_retry_times < 8 then
					self.load_retry_times = self.load_retry_times + 1

					self.delay_reload_level = GlobalTimerQuest:AddDelayTimer(function ()
						self.delay_reload_level = nil
						self:TryLoadLevelSync(bundle_name, asset_name, load_mode)
					end, 0)
				else
					self.is_stop_load = true
					TipWGCtrl.Instance:OpenConfirmAlertTips(Language.Common.LoadSceneFail, function ()
						AgentAdapter.Instance:Logout()
					end, nil, nil, true)
				end
			end
		end)
end

function ScenePreload:RemoveDelayReloadLevelTimer()
	if self.delay_reload_level then
        GlobalTimerQuest:CancelQuest(self.delay_reload_level)
        self.delay_reload_level = nil
    end
end

function ScenePreload:OnUnityMainSceneLoadComplete()
	self.loading_num = self.loading_num - 1
	self.main_has_compelet = true

	-- 同步当前使用的场景小地图相机
	GlobalTimerQuest:AddDelayTimer(function()
		local scene = SceneManager.GetSceneByName(Scene.CurShowSceneAsset)
		if not IsNil(scene) and scene:IsValid() then
			local root_objects = scene:GetRootGameObjects()
			for i = 0, root_objects.Length - 1 do
				if root_objects[i].name == "Main" then
					local scene_main_node = root_objects[i]
					if not IsNil(scene_main_node) then
						local mini_camera = scene_main_node.transform:Find("MinimapCamera")
						if not IsNil(mini_camera) then
							ScenePreload.mini_camera = mini_camera:GetComponent(typeof(TopViewMapCamera))
							break
						end
					end
				end
			end
		end
	end, 0.5)
	
	self:CheckLoadComplete()
end

function ScenePreload:CheckLoadComplete(tip)
	if nil ~= self.progress_fun and self.bytes_total > 0 then
		local precent = math.ceil(self.bytes_loaded / self.bytes_total * 100)
		-- 加载成功之前，不能通知外部100%，否则会引起bug
		local max_percent = 0 == self.loading_num and 100 or 99
		self.progress_fun(math.min(precent, max_percent), tip or Language.Common.MapReading)
	end

	if (self.bytes_loaded >= self.bytes_total) and 0 == self.loading_num then
		self:OnLoadComplete()
	end
end

function ScenePreload:OnLoadComplete()
	-- if self.download_scene_id > 0 then
		-- ReportManager:Step(Report.STEP_UPDATE_SCENE_COMPLETE, self.download_scene_id)
	-- end

	for _, v in ipairs(self.cache_gameobj_list) do
		ResPoolMgr:Release(v)  -- 进入对象池，下次取出会很快
	end
	self.cache_gameobj_list = {}

	if self.main_has_compelet and self.main_complete_fun ~= nil then
		Trycall(self.main_complete_fun)
		self.main_complete_fun = nil
	end

	if nil ~= self.complete_fun then
		Trycall(self.complete_fun)
		self.complete_fun = nil
	end
end

function ScenePreload:GetLoadLogs()
	return self.load_logs
end

-- 获得场景预加载列表
function ScenePreload.GetLoadList(scene_id)
	local list = {}
	local download_scene_id = 0

	-- 首先释放内存
	if GAME_ASSETBUNDLE then
		table.insert(list, {load_type = LOAD_TYPE.GC_REDUCE_MEM, bytes_total = 20})
	end

	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	local scene_type = scene_cfg and scene_cfg.scene_type or 0
	if nil ~= scene_cfg then
		-- 加载网络上的场景
		local name_list = {scene_cfg.bundle_name}
		for _, v in ipairs(name_list) do
			local uncached_bundles = ResMgr:GetBundlesWithoutCached(v)
			if uncached_bundles ~= nil then
				for v in pairs(uncached_bundles) do
					table.insert(list, {load_type = LOAD_TYPE.DOWNLOAD, bundle = v, bytes_total = 30})
					download_scene_id = scene_id
				end
			end
		end

		-- 加载本地场景
		table.insert(list, {load_type = LOAD_TYPE.LOAD_MAIN_SCENE, bundle_name = scene_cfg.bundle_name, asset_name = scene_cfg.asset_name, bytes_total = 100})
	end

	-- 如果不是低内存机才进行合批（重要）
	if not IsLowMemSystem then
		table.insert(list, {load_type = LOAD_TYPE.COMBINE_SCENE, bytes_total = 30})

		-- 首次进入指定要加载的prefab
		if GAME_ASSETBUNDLE then
			ScenePreload.GetLoadCgList(list, scene_id, scene_type) 	-- 预加载CG
		end

		-- 因为玩家会频繁进出某个副本场景，玩家在新手期间，需要提前预加载（非低内存才缓存副本场景）
		local is_need_preload, _ = ScenePreload.CheckIsNeedPreFuBenScene(scene_cfg)
		if is_need_preload then
			table.insert(list, {load_type = LOAD_TYPE.LOAD_FUBEN_SCENE, bytes_total = 30})
		end
	end

	-- 加载场景之后再次释放内存(合批之后的Mesh可以直接清理掉)
	if UNITY_IOS then
		table.insert(list, {load_type = LOAD_TYPE.GC_REDUCE_MEM, bytes_total = 20})
	else
		table.insert(list, {load_type = LOAD_TYPE.REDUCE_MEM, bytes_total = 20})
	end

	local ui_scene_list = ScenePreload.GetUISceneLoadList()
	for k,v in ipairs(ui_scene_list) do
		table.insert(list, v)
	end

	-- ScenePreload.GetLoadRoleSkillVideoList(list) 	-- 预加载角色技能视频
	return list, download_scene_id
end

-- 预加载CG列表
function ScenePreload.GetLoadCgList(list, scene_id, scene_type)
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local cfg_list = ConfigManager.Instance:GetAutoConfig("story_auto")["normal_scene_story"] or {}
	for k, v in pairs(cfg_list) do
		if scene_id == v.scene_id and v.operate_param and v.operate_param ~= "" and v.operate == S_STEP_OPERATE.CG_START and v.preload and v.preload == 1 then
			-- 某些cg是随着主线任务触发的，可以剔除掉
			if v.trigger_param and v.trigger_param ~= "" then
				if v.trigger == S_STEP_TRIGGER.ENTER_SCENE or v.trigger == S_STEP_TRIGGER.CLOSE_LOADING_VIEW then
					local param_t = Split(v.trigger_param, "##")
					if #param_t > 0 and scene_id ~= tonumber(param_t[1]) then
						return
					end

					for i, v in ipairs(param_t) do
						-- 主角等级超过触发等级剔除掉
						if param_t[i] == "role_level" and tonumber(param_t[i + 1]) < role_level then
							return
						end

						if param_t[i] == "task_id" then
							-- 主线任务已经完成过剔除掉
							local task_id = tonumber(param_t[i + 1])
							if ScenePreload.CheckTaskIsComplete(task_id) then
								return
							end
						end
					end
				elseif v.trigger == S_STEP_TRIGGER.RECEIVED_TASK or v.trigger == S_STEP_TRIGGER.TASK_CAN_COMMIT then
					local param_t = Split(v.trigger_param, "##")
					local trigger_task_id = tonumber(param_t[1])
					if trigger_task_id then
						-- 主线任务已经完成过剔除掉
						if ScenePreload.CheckTaskIsComplete(trigger_task_id) then
							return
						end
					end
				end
			end

			local tab = Split(v.operate_param, "##")
			if #tab == 2 then
				table.insert(list, {load_type = LOAD_TYPE.LOAD_CG, bundle_name = tab[1], asset_name = tab[2], bytes_total = 20})
			end
		end
	end
end

-- 预加载场景跳跃CG列表
function ScenePreload.GetLoadJumpCgList(list, jumppoints)
	local prof = GameVoManager.Instance:GetMainRoleVo().prof or 1

	local jump_cg_list = {}
	if jumppoints ~= nil and #jumppoints > 0 then
		for k,v in pairs(jumppoints) do
			if v.cgs ~= nil and #v.cgs > 0 then
				for k1, v1 in pairs(v.cgs) do
					if v1.prof == prof and jump_cg_list[v1.bundle_name .. v1.asset_name] == nil then
						jump_cg_list[v1.bundle_name .. v1.asset_name] = 1
						table.insert(list, {load_type = LOAD_TYPE.LOAD_CG, bundle_name = v1.bundle_name, asset_name = v1.asset_name, bytes_total = 20})
					end
				end
			end
		end
	end
end

-- 预加载角色技能视频
function ScenePreload.GetLoadRoleSkillVideoList(list)
	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local skill_id_list = SkillWGData.Instance:GetSkillIdList(sex, prof)
	local role_id = RoleWGData.Instance.GetJobModelId(sex, prof)

	if skill_id_list then
		for i, v in ipairs(skill_id_list) do
			local bundle, asset = ResPath.GetRoleSkillVideoPath(role_id, v)
			table.insert(list, {load_type = LOAD_TYPE.LOAD_ROLE_SKILL_VIDEO, bundle_name = bundle, asset_name = asset, bytes_total = 20})
		end
	end
end

-- 检查任务是否已经完成过
-- 已完成列表为空时，说明是刚刚上线，协议还未下发，也以完成任务来处理
-- 因为这些主线cg只有新手期才会触发，玩家如果是一路随着主线玩过来，任务列表肯定不为空。
function ScenePreload.CheckTaskIsComplete(task_id)
	local completed_list = TaskWGData.Instance:GetTaskCompletedList()
	if nil ~= completed_list[task_id] or nil == next(completed_list) then
		return true
	end

	return false
end

function ScenePreload.GetUISceneLoadList()
	local list = {}
	local level = RoleWGData.Instance:GetRoleLevel()
	local cfg = ConfigManager.Instance:GetAutoConfig("ui_scene_config_auto").ui_scene or {}
	for k,v in pairs(cfg) do
		if level >= v.load_level then
			table.insert(list, {load_type = LOAD_TYPE.LOAD_UI_SCENE, bundle_name = v.bundle_name, asset_name = v.asset_name, bytes_total = 30})
		end
	end

	return list
end

function ScenePreload:RemoveDelayUISceneReloadLevelTimer()
	if self.delay_ui_scene_reload_level then
        GlobalTimerQuest:CancelQuest(self.delay_ui_scene_reload_level)
        self.delay_ui_scene_reload_level = nil
    end
end

function ScenePreload:LoadUIScene(bundle_name, asset_name, bytes)
	-- 检查是否已经加载过相同的UI场景，避免重复加载
	if ScenePreload.loaded_ui_scenes[asset_name] then
		self.bytes_vir_loaded = self.bytes_vir_loaded + bytes
		return
	end

	self.loading_num = self.loading_num + 1
    self.bytes_vir_loaded = self.bytes_vir_loaded + bytes

	ResMgr:LoadLevelAsync(bundle_name, asset_name, load_mode_additive,
        function(is_succ)
            if is_succ then
				ScenePreload.loaded_ui_scenes[asset_name] = true
                self:OnUISceneLoadComplete(bundle_name, asset_name)
            else
				self.loading_num = self.loading_num - 1
                if self.load_ui_scene_retry_times < 8 then
                    self.load_ui_scene_retry_times = self.load_ui_scene_retry_times + 1
                    self.delay_ui_scene_reload_level = GlobalTimerQuest:AddDelayTimer(function ()
                        self.delay_ui_scene_reload_level = nil
                        self:LoadUIScene(bundle_name, asset_name, bytes)
                    end, 0)
                else
					self:CheckLoadComplete()
                end
            end
        end
	)
end

function ScenePreload:OnUISceneLoadComplete(bundle_name, asset_name)
	Scene.Instance:ChangeSceneShow(bundle_name, asset_name, false, nil)
	self.loading_num = self.loading_num - 1
	self:CheckLoadComplete()
end


---[[ ========================== 预加载副本场景 ================================
ScenePreload.mini_camera = nil

-- 可配置的预加载副本场景参数列表
ScenePreload.PreloadFuBenConfigList = {
	{
		bundle_name = "scenes/map/a3_fb_tianwaizhimo_main",
		asset_name = "A3_FB_TianWaiZhiMo_Main",
		min_level = 50,
		max_level = 250,
		required_scene_type_list = {SceneType.Common, SceneType.WorldBoss, SceneType.VIP_BOSS, SceneType.KF_BOSS},
		priority = 1, -- 优先级，数字越小优先级越高
		weight = 100, -- 权重，用于相同优先级下的排序
	},
	-- 跨服中转场景
	{
		bundle_name = "scenes/map/a3_cross_trans_main",
		asset_name = "A3_Cross_Trans_Main",
		min_level = 110,
		max_level = 250,
		required_scene_type_list = {SceneType.Common, SceneType.WorldBoss, SceneType.VIP_BOSS, SceneType.KF_BOSS},
		priority = 5,
		weight = 80,
	},
	{
		bundle_name = "scenes/map/a3_fb_1v1_main",
		asset_name = "A3_FB_1V1_Main",
		min_level = 110,
		max_level = 250,
		required_scene_type_list = {SceneType.Common, SceneType.WorldBoss, SceneType.VIP_BOSS, SceneType.KF_BOSS},
		priority = 10,
		weight = 80,
	},
}

-- 获取需要预加载的副本场景列表
function ScenePreload.GetValidPreloadFuBenConfigs(scene_cfg)
	local valid_configs = {}
	if not scene_cfg then
		return valid_configs
	end

	local scene_id = scene_cfg.id
	-- 跨服中转场景不预加载副本场景
	if scene_id == 4600 then
		return valid_configs
	end

	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local scene_type = scene_cfg.scene_type
	
	-- 遍历预加载副本场景配置列表
	for _, config in ipairs(ScenePreload.PreloadFuBenConfigList) do
		-- 检查是否与当前要加载的主场景相同
		local is_same_as_main = config.bundle_name == scene_cfg.bundle_name and config.asset_name == scene_cfg.asset_name
		
		if not is_same_as_main then
			-- 检查场景类型是否符合要求
			local is_type_meet = false
			for _, required_type in ipairs(config.required_scene_type_list) do
				if required_type == scene_type then
					is_type_meet = true
					break
				end
			end
			
			-- 检查等级范围和场景类型都满足的情况下才添加
			if is_type_meet and role_level >= config.min_level and role_level <= config.max_level then
				table.insert(valid_configs, config)
			end
		end
	end
	
	-- 按优先级排序，优先级相同时按权重排序
	table.sort(valid_configs, function(a, b)
		if a.priority ~= b.priority then
			return a.priority < b.priority
		else
			return a.weight > b.weight
		end
	end)
	
	return valid_configs
end

-- 是否要预加载副本场景
function ScenePreload.CheckIsNeedPreFuBenScene(scene_cfg)
	local valid_configs = ScenePreload.GetValidPreloadFuBenConfigs(scene_cfg)
	return #valid_configs > 0, valid_configs
end

-- 停止之前正在进行的副本场景预加载
function ScenePreload:StopPreviousFuBenSceneLoading()
	if is_loading_pre_fuben_scene then
		is_loading_pre_fuben_scene = false
		self.loaded_fuben_count = 0
		self.total_fuben_count = 0
	end
end

-- 预加载副本场景
function ScenePreload:LoadFuBenScene(bytes)
	if is_loading_pre_fuben_scene then
		self.bytes_vir_loaded = self.bytes_vir_loaded + bytes
		return
	end

	-- 获取当前场景配置
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(self.cur_load_scene_id)
	local is_need_load, valid_configs = ScenePreload.CheckIsNeedPreFuBenScene(scene_cfg)
	
	if not is_need_load or #valid_configs == 0 then
		self.bytes_vir_loaded = self.bytes_vir_loaded + bytes
		return
	end

	-- 根据设备性能限制预加载数量
	local device_tier = self:GetDeviceMemoryTier()
	local max_preload_count = 2 -- 默认只预加载2个(如果需要预加载跨服中转场景，它必须是配置的前两个)
	if device_tier == 2 then
		max_preload_count = 3 -- 中配设备最多预加载3个
	elseif device_tier >= 3 then
		max_preload_count = 4 -- 高配以上最多预加载4个
	end
	
	-- 限制预加载数量
	local configs_to_load = {}
	for i = 1, math.min(max_preload_count, #valid_configs) do
		table.insert(configs_to_load, valid_configs[i])
	end

	self.loading_num = self.loading_num + 1
	self.bytes_vir_loaded = self.bytes_vir_loaded + bytes
	self.loaded_fuben_count = 0
	self.total_fuben_count = #configs_to_load

	is_loading_pre_fuben_scene = true
	
	-- 批量加载副本场景
	for _, config in ipairs(configs_to_load) do
		self:LoadSingleFuBenScene(config)
	end
end

-- 加载单个副本场景
function ScenePreload:LoadSingleFuBenScene(config)
	ResMgr:LoadLevelAsync(config.bundle_name, config.asset_name, load_mode_additive,
		function(is_succ)
			if is_succ then
				Scheduler.Delay(function()
					local scene = SceneManager.GetSceneByName(config.asset_name)
					if scene == nil or IsNil(scene) then
						return
					end

					if Scene.Instance then
						-- 添加到预加载副本场景列表
						Scene.Instance:AddPreloadedFuBenScene(config.bundle_name, config.asset_name, config.priority)
						-- 预加载的副本场景默认隐藏
						Scene.Instance:ChangeSceneShow(config.bundle_name, config.asset_name, false, nil)
					end
				end)
			end

			-- 检查是否仍在进行副本场景预加载，如果已被中断则不处理
			if not is_loading_pre_fuben_scene then
				return
			end

			-- 更新加载计数
			self.loaded_fuben_count = self.loaded_fuben_count + 1
			if self.loaded_fuben_count >= self.total_fuben_count then
				is_loading_pre_fuben_scene = false
				self.loading_num = self.loading_num - 1
				self:CheckLoadComplete()
			end
		end
	)
end

function ScenePreload.CheckAndOperaPreScene(bundle_name, asset_name, trans_operate)
	if not Scene.Instance then
		return false
	end
	
	-- 检查是否匹配任何预加载的副本场景
	local matched_scene = Scene.Instance:FindPreloadedFuBenScene(bundle_name, asset_name)
	if matched_scene then
		local scene = SceneManager.GetActiveScene()
		if scene == nil or IsNil(scene) then
			return false
		end

		-- 检查当前活动场景是否已经是目标场景
		if scene.name == asset_name then
			return true
		end

		if trans_operate then
			-- 显示匹配的预加载副本场景
			Scene.Instance:ShowPreloadedFuBenScene(bundle_name, asset_name)
		end
		return true

	-- 检查是否要从预加载副本场景切换回主场景
	elseif Scene.Instance:HasAnyPreloadedFuBenScene() and 
		ScenePreload.main_scene_bundle_name == bundle_name and ScenePreload.main_scene_asset_name == asset_name and
		ScenePreload.last_load_bundle_name == bundle_name and ScenePreload.last_load_asset_name == asset_name then

		local scene = SceneManager.GetActiveScene()
		if scene == nil or IsNil(scene) then
			return false
		end

		-- 检查当前活动场景是否已经是主场景
		if scene.name == ScenePreload.main_scene_asset_name then
			return true
		end

		if trans_operate then
			-- 隐藏当前显示的预加载副本场景，显示主场景
			Scene.Instance:HideAllPreloadedFuBenScenes()
		end
		return true
	end

	return false
end
--]]