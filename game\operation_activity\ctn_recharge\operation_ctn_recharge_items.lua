local item_list_width = {
    --[1] = 100,
    --[2] = 200,
    --[3] = 300,
    --[4] = 400,
    ["default"] = 450,
}

--下拉列表 连续充值
OperationContinueChongZhiRewardRender = OperationContinueChongZhiRewardRender or BaseClass(BaseRender)
function OperationContinueChongZhiRewardRender:__init()
    self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    XUI.AddClickEventListener(self.node_list.btn_fetch, BindTool.Bind(self.OnClickFetch, self))
    XUI.AddClickEventListener(self.node_list.btn_goto_recharge, BindTool.Bind(self.OnClickGoToRecharge, self))

    self.is_load_complete = true
    self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.item_list)
	if self.parent_scroll_rect then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end

function OperationContinueChongZhiRewardRender:__delete()
    self.parent_scroll_rect = nil
    if self.nested_scroll_rect then
        self.nested_scroll_rect:DeleteMe()
        self.nested_scroll_rect = nil
    end
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function OperationContinueChongZhiRewardRender:SetParentScrollRect(scroll_rect)
	self.parent_scroll_rect = scroll_rect

	if self.is_load_complete then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end


function OperationContinueChongZhiRewardRender:SortRechargeDataList(data_list)
	if data_list and not IsEmptyTable(data_list) then
		for k,v in pairs(data_list) do
			if v.item_id and v.item_id > 0 then
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)       
                v.color = item_cfg and item_cfg.color or 1
			else
                v.color = 0
            end
		end
		table.sort(data_list, SortTools.KeyUpperSorters("color"))
    end
    return data_list
end

function OperationContinueChongZhiRewardRender:OnFlush()
    if not self.data then
        return
    end

    local param_cfg = OperationCtnRechargeWGData.Instance:GetParamCfgByServerOpenDay()
	if not IsEmptyTable(param_cfg) and param_cfg.grade then
        local bundle, asset = ResPath.GetOperationActCtnRecharge(param_cfg.reward_list)
        self.node_list.bg.image:LoadSprite(bundle, asset)
	end

    -- local desc_tab = Split(self.data.describe, "#")
    -- self.node_list.desc_left.text.text = desc_tab[1]
    -- self.node_list.desc_right.text.text = desc_tab[2]
    self.node_list.desc_mid.text.text = self.data.describe_mid
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.desc_mid.rect)
    --格子
    local data_list = {}
    for i = 0, 14 do
        if self.data.reward_item[i] ~= nil then
            table.insert(data_list, self.data.reward_item[i])
        else
            break
        end
    end
    data_list = self:SortRechargeDataList(data_list)
    self.item_list:SetDataList(data_list, 3)
    local count = #data_list
    -- if item_list_width[count] then
    --     self.node_list.item_list.rect.sizeDelta = Vector2(item_list_width[count], self.node_list.item_list.rect.sizeDelta.y)
    -- else
    --     self.node_list.item_list.rect.sizeDelta = Vector2(item_list_width["default"], self.node_list.item_list.rect.sizeDelta.y)
    -- end

    --reset
    self.node_list.flag_img:SetActive(false)
    self.node_list.btn_fetch:SetActive(false)
    self.node_list.btn_goto_recharge:SetActive(false)
    self.node_list.btn_fetch_remind:SetActive(false)
    self.node_list.btn_effect:SetActive(false)

    local open_day = OperationCtnRechargeWGData.Instance:GetCtnRechargeOpenDay()
    local open_day_chongzhi_value = OperationCtnRechargeWGData.Instance:GetCtnDayMoneyByActDay(self.data.activity_day)
    --是否达到充值金额
    local chongzhi_flag = open_day_chongzhi_value >= self.data.money

    --领取标记
    local has_fetch = OperationCtnRechargeWGData.Instance:GetDayRechargeIsFetch(self.data.seq + 1) --self.data.seq

    if chongzhi_flag then
        if has_fetch then
            --已领取
            self.node_list.flag_img:SetActive(true)
            self.node_list.flag_img.image:LoadSprite(ResPath.GetF2CommonImages("biaoqian_1"))
            self.node_list.flag_img_text.text.text = Language.OpertionAcitvity.HasFetch
        else
            --可领取
            self.node_list.btn_fetch:SetActive(true)
            self.node_list.btn_fetch_remind:SetActive(true)
            self.node_list.btn_effect:SetActive(true)
            --self.node_list.btn_effect:ChangeAsset(ResPath.GetEffectUi("UI_anniu_tongyong"))
        end
    else
        local is_today = open_day == self.data.activity_day
        local is_not_open_day = open_day < self.data.activity_day
        local is_pass_day = open_day > self.data.activity_day
        if is_today then
            --前往充值
            self.node_list.btn_goto_recharge:SetActive(true)
        elseif is_not_open_day then
            --前往充值
            self.node_list.btn_goto_recharge:SetActive(true)
        elseif is_pass_day then
            --已错过
            self.node_list.flag_img:SetActive(true)
            self.node_list.flag_img.image:LoadSprite(ResPath.GetF2CommonImages("biaoqian_2"))
            self.node_list.flag_img_text.text.text = Language.OpertionAcitvity.YiCuoGuo
        end
    end

    local color = chongzhi_flag and COLOR3B.GREEN or COLOR3B.RED
    local str = ToColorStr(open_day_chongzhi_value .. "/" .. self.data.money, color)
    self.node_list.cur_chongzhi_value.text.text = str
end

--点击领取
function OperationContinueChongZhiRewardRender:OnClickFetch()
    OperationCtnRechargeWGCtrl.Instance:OperaActCtnRechargeOpera(ACT_RECAHREGE_OPERA_TYPE.ACT_RECAHREGE_OPERA_DAY_REWARD, self.data.seq)
end

--点击前往充值
function OperationContinueChongZhiRewardRender:OnClickGoToRecharge()
    local open_day = OperationCtnRechargeWGData.Instance:GetCtnRechargeOpenDay()
    local is_not_open_day = open_day < self.data.activity_day
    if is_not_open_day then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.ActNotOpen)
        return
    end
    FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
end









--上下滑动列表里面的物品格子 连续充值
OperationCtnDayRechargeItemCellRender = OperationCtnDayRechargeItemCellRender or BaseClass(BaseRender)
function OperationCtnDayRechargeItemCellRender:__init()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)
end

function OperationCtnDayRechargeItemCellRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function OperationCtnDayRechargeItemCellRender:OnFlush()
    self.item_cell:SetData(self.data)
end








--天数按钮格子
OperationCtnRechargeDayButtonRender = OperationCtnRechargeDayButtonRender or BaseClass(BaseRender)
function OperationCtnRechargeDayButtonRender:__init()
    XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind1(self.OnClickEvent, self))
end

function OperationCtnRechargeDayButtonRender:__delete()
    self.parent_view = nil
    self.click_event = nil
end

function OperationCtnRechargeDayButtonRender:OnFlush()
    self:SelectChange()
    --self.node_list.line:SetActive(self.data.day < self.data.max_day)


    local is_show_red_point = false
    local param_cfg = OperationCtnRechargeWGData.Instance:GetParamCfgByServerOpenDay()
    if not IsEmptyTable(param_cfg) and param_cfg.grade then
        is_show_red_point = OperationCtnRechargeWGData.Instance:GetDayButtonIsShowRedPoint(self.data.day, param_cfg.grade)

        local bundle, asset = ResPath.GetOperationActCtnRecharge(param_cfg.tag)
        self.node_list.normal.image:LoadSprite(bundle, asset, function()
            XUI.ImageSetNativeSize(self.node_list.normal)
        end)

        bundle, asset = ResPath.GetOperationActCtnRecharge(param_cfg.selected_tag)
        self.node_list.hl.image:LoadSprite(bundle, asset, function()
            XUI.ImageSetNativeSize(self.node_list.hl)
        end)

        -- bundle, asset = ResPath.GetOperationActCtnRecharge(param_cfg.rope)
        -- self.node_list.line.image:LoadSprite(bundle, asset, function()
        --     XUI.ImageSetNativeSize(self.node_list.line)
        -- end)

        --day_button_text_color
        local str = string.format(Language.OpertionAcitvity.DiJiTian, self.data.day)
        if self.parent_view and self.parent_view.ctn_select_day_index == self.my_index then
            self.node_list.text_day.text.text = ToColorStr(str, COLOR3B.RED)
        else
            self.node_list.text_day.text.text = ToColorStr(str, COLOR3B.WHITE)
        end
    else
        self.node_list.text_day.text.text = string.format(Language.OpertionAcitvity.DiJiTian, self.data.day)
    end
    self.node_list.img_red_point:SetActive(is_show_red_point)

    local real_act_day = OperationCtnRechargeWGData.Instance:GetCtnRechargeOpenDay()
    XUI.SetGraphicGrey(self.node_list.can_gray_container, self.data.day < real_act_day)
end

function OperationCtnRechargeDayButtonRender:SetClickEvent(call_back)
	self.click_event = call_back
end

function OperationCtnRechargeDayButtonRender:OnClickEvent()
    ----未开启天数判断
    --if self.data.day > self.data.cur_act_day then
    --    SysMsgWGCtrl.Instance:ErrorRemind(Language.OpertionAcitvity.ActNotOpen)
    --    return
    --end
    if self.parent_view.ctn_select_day_index == self.my_index then
        return
    end

	if self.click_event ~= nil then
		self.click_event(self, self.my_index)
	end
end

function OperationCtnRechargeDayButtonRender:SetParentView(parent_view)
    self.parent_view = parent_view
end

function OperationCtnRechargeDayButtonRender:SetMyIndex(index)
    self.my_index = index
end

function OperationCtnRechargeDayButtonRender:SelectChange()
    self.node_list.hl:SetActive(self.parent_view and self.parent_view.ctn_select_day_index == self.my_index)
    self.node_list.normal:SetActive(not self.parent_view or self.parent_view.ctn_select_day_index ~= self.my_index)
end








--卡片
OperationContinueChongZhiPageRender = OperationContinueChongZhiPageRender or BaseClass(BaseRender)
function OperationContinueChongZhiPageRender:__init()
    self.item_list = {}
    for i = 1, 3 do
        self.item_list[i] = OperationCtnPageItemCellRender.New(self.node_list["total_sub_item" .. i])
    end
end

function OperationContinueChongZhiPageRender:__delete()
    for i, v in ipairs(self.item_list) do
        v:DeleteMe()
    end
    self.item_list = {}
end

function OperationContinueChongZhiPageRender:OnFlush()
    if not self.data or not self.data[1] then
        return
    end

    local count = 0
    for i = 1, 3 do
        if self.data[i] then
            if self.item_list[i] then
                self.item_list[i]:SetData(self.data[i])
                count = count + 1
            end
        else
            break
        end
    end
    for i, v in ipairs(self.item_list) do
        v:SetActive(i <= count)
        if count == 2 then
            self.node_list["total_sub_item" .. i].transform:SetParent(self.node_list["two_item_list"].transform, false)
        else
            self.node_list["total_sub_item" .. i].transform:SetParent(self.node_list["three_item_list"].transform, false)
        end
    end

    self.node_list.top_desc.text.text = self.data[1].money
    if self.data[1].card_img then
        self.node_list.bg.image:LoadSprite(ResPath.GetOperationActCtnRecharge(self.data[1].card_img))
    end
end










--卡片里面的物品格子 连续充值
OperationCtnPageItemCellRender = OperationCtnPageItemCellRender or BaseClass(BaseRender)
function OperationCtnPageItemCellRender:__init()
    self.item_cell = ItemCell.New(self.node_list.cell_pos)

    XUI.AddClickEventListener(self.node_list.click, BindTool.Bind(self.OnClickFetch, self))
end

function OperationCtnPageItemCellRender:__delete()
    XUI.SetGraphicGrey(self.node_list.cell_pos, false)
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function OperationCtnPageItemCellRender:OnFlush()
    self.item_cell:SetData(self.data.reward_item[0])

    local has_fetch = OperationCtnRechargeWGData.Instance:GetTotalRechargeIsFetch(self.data.seq + 1)
    local total_recharge_day_count = OperationCtnRechargeWGData.Instance:GetLeiJiRechargeDayCount(self.data.money)
    local can_fetch = total_recharge_day_count >= self.data.total_day

    local color = can_fetch and COLOR3B.D_GREEN or COLOR3B.D_GREEN
    local str = ToColorStr(total_recharge_day_count .. "/" .. self.data.total_day, color)
    self.node_list.text_day.text.text = string.format(Language.OpertionAcitvity.LeiJiChongZhiDayCount, str)

    self.node_list.has_fetch_img:SetActive(has_fetch)
    self.node_list.can_fetch_img:SetActive(can_fetch and not has_fetch)
    self.node_list.effect:SetActive(can_fetch and not has_fetch)
    XUI.SetGraphicGrey(self.node_list.cell_pos, has_fetch)
    self.item_cell:SetIsShowTips(not can_fetch or has_fetch)
    self.node_list.click:SetActive(can_fetch and not has_fetch)
end

function OperationCtnPageItemCellRender:OnClickFetch()
    OperationCtnRechargeWGCtrl.Instance:OperaActCtnRechargeOpera(ACT_RECAHREGE_OPERA_TYPE.ACT_RECAHREGE_OPERA_TOTAL_DAY_REWARD, self.data.seq)
end