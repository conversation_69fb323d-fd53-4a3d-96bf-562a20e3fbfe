﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_AudioGVoiceWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("AudioGVoice");
		<PERSON><PERSON>RegFunction("InitFeesVoice", InitFeesVoice);
		<PERSON><PERSON>Function("SpeechToText", SpeechToText);
		<PERSON><PERSON>unction("StartRecorder", StartRecorder);
		<PERSON><PERSON>unction("StartRecorder2", StartRecorder2);
		<PERSON><PERSON>RegFunction("StopRecorder", StopRecorder);
		<PERSON><PERSON>RegFunction("StartPlay", StartPlay);
		<PERSON><PERSON>Function("StopPlay", StopPlay);
		<PERSON>.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitFeesVoice(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				string arg2 = ToLua.CheckString(L, 3);
				Nirvana.AudioGVoice.InitFeesVoice(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 4)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				string arg2 = ToLua.CheckString(L, 3);
				string arg3 = ToLua.CheckString(L, 4);
				Nirvana.AudioGVoice.InitFeesVoice(arg0, arg1, arg2, arg3);
				return 0;
			}
			else if (count == 5)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				string arg2 = ToLua.CheckString(L, 3);
				string arg3 = ToLua.CheckString(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				Nirvana.AudioGVoice.InitFeesVoice(arg0, arg1, arg2, arg3, arg4);
				return 0;
			}
			else if (count == 6)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				string arg2 = ToLua.CheckString(L, 3);
				string arg3 = ToLua.CheckString(L, 4);
				int arg4 = (int)LuaDLL.luaL_checknumber(L, 5);
				System.Action<bool,string,string> arg5 = (System.Action<bool,string,string>)ToLua.CheckDelegate<System.Action<bool,string,string>>(L, 6);
				Nirvana.AudioGVoice.InitFeesVoice(arg0, arg1, arg2, arg3, arg4, arg5);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.AudioGVoice.InitFeesVoice");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SpeechToText(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				Nirvana.AudioGVoice.SpeechToText(arg0);
				return 0;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				System.Action<bool,string,string> arg1 = (System.Action<bool,string,string>)ToLua.CheckDelegate<System.Action<bool,string,string>>(L, 2);
				Nirvana.AudioGVoice.SpeechToText(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.AudioGVoice.SpeechToText");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StartRecorder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Nirvana.AudioGVoice.StartRecorder();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StartRecorder2(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = Nirvana.AudioGVoice.StartRecorder2();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopRecorder(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.AudioGVoice.StopRecorder();
				return 0;
			}
			else if (count == 1)
			{
				System.Action<bool,string,string> arg0 = (System.Action<bool,string,string>)ToLua.CheckDelegate<System.Action<bool,string,string>>(L, 1);
				Nirvana.AudioGVoice.StopRecorder(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.AudioGVoice.StopRecorder");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StartPlay(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				Nirvana.AudioGVoice.StartPlay(arg0);
				return 0;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				System.Action<bool,string,string> arg1 = (System.Action<bool,string,string>)ToLua.CheckDelegate<System.Action<bool,string,string>>(L, 2);
				Nirvana.AudioGVoice.StartPlay(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.AudioGVoice.StartPlay");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopPlay(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Nirvana.AudioGVoice.StopPlay();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

