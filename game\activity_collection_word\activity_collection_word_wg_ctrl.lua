require("game/activity_collection_word/activity_collection_word_view")
require("game/activity_collection_word/activity_collection_word_wg_data")

ActivityCollectionWordWGCtrl = ActivityCollectionWordWGCtrl or BaseClass(BaseWGCtrl)

function ActivityCollectionWordWGCtrl:__init()
    if ActivityCollectionWordWGCtrl.Instance then
        error("[ActivityCollectionWordWGCtrl]:Attempt to create singleton twice!")
    end
    --单例
    ActivityCollectionWordWGCtrl.Instance = self

    self.view = ActivityCollectionWordView.New(GuideModuleName.CollectionWordView)
    self.data = ActivityCollectionWordWGData.New()

    --注册协议
    self:RegisterAllProtocols()

    --活动关闭和开启的监听
    self.act_change = BindTool.Bind(self.OnActivityChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
end

function ActivityCollectionWordWGCtrl:__delete()
    if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

    self.data:DeleteMe()
    self.data = nil

    self.view:DeleteMe()
    self.view = nil

    ActivityCollectionWordWGCtrl.Instance = nil
end

function ActivityCollectionWordWGCtrl:RegisterAllProtocols()
    --接受协议
    self:RegisterProtocol(SCExchangeShopInfo, "OnSCExchangeShopInfo")
end

--接受协议
function ActivityCollectionWordWGCtrl:OnSCExchangeShopInfo(protocol)
 --   print_error("protocol", protocol)
    --数据存储
    self.data:SetCollWordInfo(protocol)

    --界面刷新
    if self.view then
        self.view:Flush()
    end

    --刷新红点
    RemindManager.Instance:Fire(RemindName.RemindCollectionWord)
end

function ActivityCollectionWordWGCtrl:OnActivityChange(activity_type)
    if activity_type == ACTIVITY_TYPE.ACTIVITY_TYPE_OA_EXCHANGE_SHOP then
        RemindManager.Instance:Fire(RemindName.RemindCollectionWord)
    end
end

function ActivityCollectionWordWGCtrl:OpenCollectionWord()
    if self.view then
        self.view:Open()
    end
end