local NUMBER_COUNT = 6

RebateDiscountView = RebateDiscountView or BaseClass(SafeBaseView)
function RebateDiscountView:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/rebate_gift_ui_prefab", "rebate_discount_view")
    self.toggle_select_index = -1
    self.ani_time = 0
end

function RebateDiscountView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["discount_btn_draw"], BindTool.Bind1(self.OnClickDraw, self)) --立减
    XUI.AddClickEventListener(self.node_list["discount_btn_buy"], BindTool.Bind1(self.OnClickBuy, self)) --购买礼包
    XUI.AddClickEventListener(self.node_list["discount_btn_jump"], BindTool.Bind1(self.OnClickJumpBuy, self))

    self:InitBoxToggleList()
    RebateGiftActivityWGData.Instance:SetNumberAniIsPlay(false)

    if not self.gift_reward_grid then
        self.gift_reward_grid = AsyncBaseGrid.New()
        self.gift_reward_grid:SetStartZeroIndex(true)
        self.gift_reward_grid:CreateCells({col = 2, change_cells_num = 1, list_view = self.node_list["discount_reward_list"]})
    end
end


function RebateDiscountView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("rebate_discount_down") then
		CountDownManager.Instance:RemoveCountDown("rebate_discount_down")
	end

    if nil ~= self.gift_reward_grid then
        self.gift_reward_grid:DeleteMe()
        self.gift_reward_grid = nil
    end

    if self.play_time_quest then
        GlobalTimerQuest:CancelQuest(self.play_time_quest)
        self.play_time_quest = nil 
    end

    if self.toggle_list then
		for k,v in pairs(self.toggle_list) do
			v:DeleteMe()
		end
		self.toggle_list = nil
	end

end

function RebateDiscountView:OpenCallBack()
    RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS, RECHARGE_DISCOUNTS_OPERATE_TYPE.INFO)
end

function RebateDiscountView:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushBoxRrward()
            self:FlushBoxRewardInfo()
		end
	end

    self:LoginTimeCountDown()
end

function RebateDiscountView:InitBoxToggleList()
	local toggle_list = {}
	local toggle_root = self.node_list["discount_toggle_root"]
	for i = 1, 8 do
		local item_toggle = DiscountToggleRender.New(toggle_root.transform:Find("box_" .. i))
		item_toggle:AddClickEventListener(BindTool.Bind(self.OnClickToggle, self), true)
		item_toggle:SetIndex(i)
		toggle_list[i] = item_toggle
	end
	self.toggle_list = toggle_list
end

function RebateDiscountView:OnClickToggle(item)
    local select_index = item:GetIndex()
    if self.toggle_select_index ~= select_index then
		self.toggle_select_index = select_index
		self:FlushBoxRrward()
	end
end

function RebateDiscountView:FlushBoxRrward()
    local box_reward_list = RebateGiftActivityWGData.Instance:GetDiscountDataList()
    if box_reward_list ~= nil and box_reward_list[self.toggle_select_index] then
        local is_play = RebateGiftActivityWGData.Instance:GetNumberAniIsPlay()
        self.select_data = box_reward_list[self.toggle_select_index]
        self.gift_reward_grid:SetDataList(self.select_data.item_list)
        self.node_list["cur_gift_gold"].text.text = self.select_data.base_gold - self.select_data.random_gold--当前礼包的价格
        
        --充值总数
        local chongzhi_gold = RebateGiftActivityWGData.Instance:GetDiscountChongZhiGold()
        local gold_str = CommonDataManager.ConverGoldByThousand(chongzhi_gold)
        self.node_list["discount_gold_value"].text.text = gold_str

        local can_draw = self.select_data.is_random > 0      --已抽过奖
        local can_buy = self.select_data.is_buy > 0          --已购买过
        self.node_list["discount_btn_draw"]:SetActive(not can_draw)                 --抽奖按钮
        self.node_list["cur_buy_gold"]:SetActive(can_draw and (not can_buy) and (not is_play))  --价格显示
        self.node_list["discount_btn_buy"]:SetActive(can_draw and (not can_buy))    --购买按钮
        self.node_list["is_buy"]:SetActive(can_buy)                                 --是否已购买
        self.node_list["discount_btn_draw_red"]:SetActive((not can_draw) and self.select_data.is_lock) --抽奖红点

        if can_draw and (not is_play) then
            self:GetCurNumberShow(self.select_data)
        end

        if not is_play then
            for i = 1, NUMBER_COUNT do
                self.node_list["discount_wenhao" .. i]:SetActive(self.select_data.is_random < 1)
                self.node_list["num_ver_list" .. i]:SetActive(self.select_data.is_random > 0)
            end
        end
        
    end
end

--刷新宝箱信息
function RebateDiscountView:FlushBoxRewardInfo()
    local chongzhi_gold = RebateGiftActivityWGData.Instance:GetDiscountChongZhiGold()
    local cur_Progress = RebateGiftActivityWGData.Instance:GetDiscountCurProgress(chongzhi_gold)
    self.node_list["discount_all_progress"].slider.value = cur_Progress

    --宝箱显示
    local box_reward_list = RebateGiftActivityWGData.Instance:GetDiscountDataList()
    if not self.toggle_list and box_reward_list == nil then
		return
	end

    for i = 1, #self.toggle_list do
        if box_reward_list[i] then
            self.toggle_list[i]:SetData(box_reward_list[i])
        end
    end

    local toggle_item = self.toggle_list[1]
	for _,v in pairs(self.toggle_list) do
		if v:GetIsRemind() then
			toggle_item = v
			break
		end
	end

    local is_play = RebateGiftActivityWGData.Instance:GetNumberAniIsPlay()
    if not is_play then
        toggle_item:SetToggleIsOn(true)
	    self:OnClickToggle(toggle_item)
    end
end

--播放数字动画
function RebateDiscountView:PlayNumberTween(number_num)
    local is_play = RebateGiftActivityWGData.Instance:GetNumberAniIsPlay()
    if is_play then
        TipWGCtrl.Instance:ShowSystemMsg(Language.RebateGiftAct.IsWorkIng)
        return
    end

    if number_num <= 0 then
        return
    end

    RebateGiftActivityWGData.Instance:SetNumberAniIsPlay(true)
    self.node_list["cur_buy_gold"]:SetActive(false)        --价格显示
    self.node_list["btn_mask"]:SetActive(true)

    if self.tween_list then
        for i = 1, NUMBER_COUNT * 2 do
            if self.tween_list[i] then
                self.tween_list[i]:Kill()
                self.tween_list[i] = nil
            end
        end
    end

    local perent_list = {}
    local pos_list = {}
    for i = 1, NUMBER_COUNT do
        local num = self:GetDetailNum(i, number_num)
        perent_list[i] = 1 - (num) / 9-- num * 100 + 50
        pos_list[i] = 1
        if perent_list[i] > 0.5 then
            pos_list[i] = 0
        else
            pos_list[i] = 1
        end
    end

    -- for i = 1, 6 do
    --     RectTransform.SetAnchoredPositionXY(self.node_list["content_list" .. i].rect, 0, 50)
    -- end
    
    -- if self.tween_list then
    --     for k,v in pairs(self.tween_list) do
    --         v:Kill()
    --     end
    --     self.tween_list = {}
    -- end

    self.tween_list = {}
    -- local old_time = Status.NowTime
    -- local time_list = {
    --     0.6,
    --     0.7,
    --     0.8,
    --     0.9,
    --     2,
    --     2,
    -- }
    -- for i = 6, 1, -1 do
    --     if not self.tween_list[i] then
    --         self.tween_list[i] = DG.Tweening.DOTween.Sequence()
    --         self.tween_list[i]:Append(self.node_list["content_list" .. i].rect:DOAnchorPosY(950, 1.5)):SetLoops(7 - i):OnComplete(
    --             function()
    --                 print_error("---OnComplete111---", i, Status.NowTime - old_time)
    --                 RectTransform.SetAnchoredPositionXY(self.node_list["content_list" .. i].rect, 0, 50)
    --                 self.node_list["content_list" .. i].rect:DOAnchorPosY(perent_list[i], num_list[i] * 0.1 + 0.1)
    --                             :OnComplete(function ()
    --                                 print_error("----OnComplete222----", i, num_list[i] * 0.3 + 0.3)
    --                                 if i == 1 then
    --                                     RebateGiftActivityWGData.Instance:SetNumberAniIsPlay(false)
    --                                     self.node_list["btn_mask"]:SetActive(false)
    --                                     -- self:FlushBoxRrward()
    --                                     print_error("OnComplete", Status.NowTime - old_time)
    --                                 end
    --                             end)
    --             end
    --         )
    --     end
    -- end

    ----[[

        self.tween_list[5] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list3"], 1, 0, 1, function ()
        self.tween_list[6] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list3"], pos_list[3], perent_list[3], 1.9)
    end)

    self.tween_list[7] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list4"], 1, 0, 1, function ()
        self.tween_list[8] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list4"], pos_list[4], perent_list[4], 1.7)
    end)

    self.tween_list[9] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list5"], 1, 0, 1, function ()
        self.tween_list[10] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list5"], pos_list[5], perent_list[5], 1.4)
    end)
    
    self.tween_list[11] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list6"], 1, 0, 1, function ()
        self.tween_list[12] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list6"], pos_list[6], perent_list[6], 1.1)
    end)

    
    ReDelayCall(self, function ()
        self.tween_list[3] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list2"], 1, 0, 1, function ()
            self.tween_list[4] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list2"], pos_list[2], perent_list[2], 2.2)
        end)
    end, 0.3,"discount_scroll_num2")

    ReDelayCall(self, function ()
        self.tween_list[1] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list1"], 1, 0, 1, function ()
            self.tween_list[2] = UITween.DoScrollRectVerticalPosition(self.node_list["num_ver_list1"], pos_list[1], perent_list[1], 2.5, function ()
                RebateGiftActivityWGData.Instance:SetNumberAniIsPlay(false)
                self.node_list["btn_mask"]:SetActive(false)
                self:FlushBoxRrward()
            end)
        end)
    end, 0.5,"discount_scroll_num1")
    --]]
end

--试试手气
function RebateDiscountView:OnClickDraw()
    -- if true then
    --     for i = 1, NUMBER_COUNT do
    --         self.node_list["discount_wenhao" .. i]:SetActive(false)
    --         self.node_list["num_ver_list" .. i]:SetActive(true)
    --     end

    --     self:PlayNumberTween(034675)
    --     return
    -- end
    if self.select_data == nil then
        return
    end

    if not self.select_data.is_lock then
        TipWGCtrl.Instance:ShowSystemMsg(Language.RebateGiftAct.DiscountDrawDesc)
        return
    end

    if self.select_data.is_random < 1 then
        for i = 1, NUMBER_COUNT do
            self.node_list["discount_wenhao" .. i]:SetActive(false)
            self.node_list["num_ver_list" .. i]:SetActive(true)
        end

        RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(
        ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS, 
        RECHARGE_DISCOUNTS_OPERATE_TYPE.GIFT_RANDOM,
        self.select_data.seq)
    end
end

function RebateDiscountView:GetDetailNum(index, number_num)
	local num = number_num or 0
	local data = {}
	data[1] = math.floor(num / 100000) >= 9 and 9 or math.floor(num / 100000)
	data[2] = math.floor(num / 10000 % 10)
	data[3] = math.floor(num / 1000 % 10)
	data[4] = math.floor(num / 100 % 10)
	data[5] = math.floor(num / 10 % 10)
	data[6] = num % 10
    return data[index]
end

--抽奖机数字显示
function RebateDiscountView:GetCurNumberShow(select_data)
    if select_data == nil then
        return
    end

    for i = 1, NUMBER_COUNT do
        local num = self:GetDetailNum(i, select_data.random_gold)
        self.node_list["num_ver_list" .. i].scroll_rect.verticalNormalizedPosition = 1 - (num) / 9
    end

end

--购买礼包
function RebateDiscountView:OnClickBuy()
    if self.select_data == nil then
        return
    end

    local is_play = RebateGiftActivityWGData.Instance:GetNumberAniIsPlay()
    if is_play then
        TipWGCtrl.Instance:ShowSystemMsg(Language.RebateGiftAct.IsWorkIng)
        return
    end

    local consume = self.select_data.base_gold - self.select_data.random_gold
    --检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        if self.select_data.is_buy > 0 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.RebateGiftAct.DiscountBuyDesc)
            return
        end

        local ok_func = function ()
            RebateGiftActivityWGCtrl.Instance:SendRechargeInfo(
        ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS, 
        RECHARGE_DISCOUNTS_OPERATE_TYPE.GIFT_BUY,
        self.select_data.seq)
        end

        local str = string.format(Language.RebateGiftAct.DiscountBuyTips, consume)
        local check_string = "discount_buy"
        TipWGCtrl.Instance:OpenCheckAlertTips(str, ok_func, check_string, nil)
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

--前往充值
function RebateDiscountView:OnClickJumpBuy()
    RechargeWGCtrl.Instance:Open()
end

------------------------------------活动时间倒计时
function RebateDiscountView:LoginTimeCountDown()
 --    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_RECHARGE_DISCOUNTS)
 --    if activity_data ~= nil then
 -- 	   local invalid_time = activity_data.end_time
 --        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
 --            self.node_list["discount_time_down"].text.text = TimeUtil.FormatSecondDHM(invalid_time - TimeWGCtrl.Instance:GetServerTime())
 --            print_error("invalid_time",invalid_time)
 --            CountDownManager.Instance:AddCountDown("rebate_discount_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
 --        end
	-- end
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("rebate_discount_down") then
            CountDownManager.Instance:RemoveCountDown("rebate_discount_down")
        end

        CountDownManager.Instance:AddCountDown("rebate_discount_down", 
            BindTool.Bind(self.UpdateCountDown, self), 
            BindTool.Bind(self.OnComplete, self), 
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function RebateDiscountView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list["discount_time_down"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
	end
end

function RebateDiscountView:OnComplete()
    self.node_list["discount_time_down"].text.text = ""
end
--------------------------------------------------------

DiscountToggleRender = DiscountToggleRender or BaseClass(BaseRender)
function DiscountToggleRender:__init()
    self.is_remind = false
end

function DiscountToggleRender:OnFlush()
    if self.data == nil then
        return
    end

    local index = self:GetIndex()
    local bundle, asset = ResPath.GetNoPackPNG("a2_czzy_bx" .. index)
    self.node_list["box_img_an"].image:LoadSprite(bundle, asset, function()
        self.node_list["box_img_an"].image:SetNativeSize()
    end)

    XUI.SetGraphicGrey(self.node_list["normal_img"], not (self.data.is_lock))
    XUI.SetGraphicGrey(self.node_list["box_img_an"], not (self.data.is_lock))
    --self.node_list["box_toggle"].toggle.interactable = self.data.is_lock
    local gold_str = CommonDataManager.ConverGoldByThousand(self.data.need_gold)
    self.node_list["gold_num"].text.text = gold_str
    self.node_list["is_buy"]:SetActive(self.data.is_buy > 0)
    self.is_remind = self.data.is_lock and (self.data.is_random < 1 or self.data.is_buy < 1)
    self.node_list["red"]:SetActive(self.is_remind)
end

function DiscountToggleRender:SetToggleIsOn(_bool)
	local view = self:GetView()
	if view then
		view.toggle.isOn = _bool
	end
end

function DiscountToggleRender:GetIsRemind()
    return self.is_remind
end