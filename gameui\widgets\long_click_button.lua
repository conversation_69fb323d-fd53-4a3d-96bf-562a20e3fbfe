--长按按钮
LongClickButton = LongClickButton or BaseClass()

function LongClickButton:__init(event_trigger_listener)
    if event_trigger_listener == nil then
        print_error("[LongClickButton] event_trigger_listener is nil")
    end

    self.event_listener = event_trigger_listener
    self:InitListener()

    self.last_click_time = 0
    self.long_click_time = 0.3
    self.is_long_click = false
end

function LongClickButton:__delete()
    self.event_listener = nil
end

function LongClickButton:InitListener()
    self.on_point_click = BindTool.Bind1(self.OnPointClick, self)
    self.on_point_down = BindTool.Bind1(self.OnPointDown, self)
    self.on_point_up = BindTool.Bind1(self.OnPointUp, self)
    if self.event_listener ~= nil then
         self.event_listener:AddPointerClickListener(self.on_point_click)
        self.event_listener:AddPointerDownListener(self.on_point_down)		--按下
        self.event_listener:AddPointerUpListener(self.on_point_up)
    end
end

--添加点击抬起监听
function LongClickButton:AddPointerClickListener(callback)
    self.on_point_click_callback = callback
end

--添加长按抬起监听
function LongClickButton:AddLongClickUpListener(callback)
    self.long_click_up_callback = callback
end

--添加按下监听
function LongClickButton:AddPointDownListener(callback)
    self.point_down_callback = callback
end


function LongClickButton:OnPointClick(event_data)
    if self.is_long_click then
        return
    end
    if self.on_point_click_callback ~= nil then
        self.on_point_click_callback(event_data)
    end
end

function LongClickButton:OnPointDown(event_data)
    self.is_long_click = false
    self.last_click_time = Status.NowTime
    if self.point_down_callback ~= nil then
        self.point_down_callback(event_data)
    end
end

function LongClickButton:OnPointUp(event_data)
    if Status.NowTime - self.last_click_time > self.long_click_time then
        self.is_long_click = true
        if self.long_click_up_callback ~= nil then
            self.long_click_up_callback(event_data)
        end
    end
end