FestivalLoginYouLiWGData = FestivalLoginYouLiWGData or BaseClass()

function FestivalLoginYouLiWGData:__init()
	if FestivalLoginYouLiWGData.Instance then 
		ErrorLog("[FestivalLoginYouLiWGData] Attemp to create a singleton twice !")
	end

	FestivalLoginYouLiWGData.Instance = self
    FestivalActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LOGINGIFT, {[1] = MERGE_EVENT_TYPE.LEVEL}, 
    	BindTool.Bind(self.GetActCanOpen, self), BindTool.Bind(self.IsShowLoginRedPoint, self))
    RemindManager.Instance:Register(RemindName.FestivalLoginRward, BindTool.Bind(self.IsShowLoginRedPoint, self))

    self:LoadConfig()
end

function FestivalLoginYouLiWGData:__delete()
	FestivalLoginYouLiWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.FestivalLoginRward)
end

function FestivalLoginYouLiWGData:LoadConfig()
	self.login_auto = ConfigManager.Instance:GetAutoConfig("festival_activity_new_login_reward_auto")
    self.login_reward = self.login_auto.login_reward
    self.login_reward_cfg = ListToMapList(self.login_reward, "grade")
	self.login_activity_param = self.login_auto.activity_param
    self.login_activity_cfg = ListToMap(self.login_auto.activity_param, "grade")
    self.login_interface_activity_cfg = self.login_auto.interface
end

function FestivalLoginYouLiWGData:GetActCanOpen()
	if nil == self.logindaydata or nil == self.logindaydata.grade then
        return false
    end

	local grade = self.logindaydata.grade
	local cfg = self.login_activity_cfg[grade]

	if nil == cfg then
		return false
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level >= cfg.open_role_level then
		return true
	end

	return false
end


function FestivalLoginYouLiWGData:GetInterfaceCfg()
    local grade = self.logindaydata.grade
    for k, v in pairs(self.login_interface_activity_cfg) do
        if v.grade == grade then
            return v
        end
    end
    return {}
end

--根据配置档次获取奖励配置
function FestivalLoginYouLiWGData:GetOpenDays()
    if nil == self.logindaydata or nil == self.logindaydata.grade then
        return {}
    end

	local grade = self.logindaydata.grade
    local reward = self.login_reward_cfg[grade]

    if reward and not IsEmptyTable(reward) then
        table.sort(reward, SortTools.KeyLowerSorter("day_index"))
	end
	
	return reward or {}
end


------------------------------------  登录奖励 -------------------------------------------
-- 获取登录奖励配置
function FestivalLoginYouLiWGData:GetLoginRewardCfgByDay(day)
	local reward = self:GetOpenDays()
	if IsEmptyTable(reward) then 
		return 
	end

	--奖励与对应天数相等时出现
	for k,v in ipairs(reward) do
		if day == v.day_index then
			return v
		end
	end
end

--获取活动提示
function FestivalLoginYouLiWGData:GetActivityTip()
	return Language.MergeActivity.TipsActivityHint, Language.MergeActivity.TipsActivityHintShow
end

function FestivalLoginYouLiWGData:SetLoginRewardInfo(protocol)
	if self.logindaydata == nil then
		self.logindaydata = {}
	end
	self.logindaydata.day_list = {}
    self.logindaydata.login_day_num = protocol.login_day_num
    self.logindaydata.grade = self:GetCurLoginGrde() --protocol.grade
	self.logindaydata.len = 0	
	local cfg = self:GetOpenDays()
	if IsEmptyTable(cfg) then 
		return
	end
	for k,v in pairs(cfg) do
		local data = {}
		data.index = v.day_index
		data.vip_level = v.vip_lv or 0
		data.common_gift_state = protocol.reward_state[v.day_index].common_gift_state 	--是否领取
		data.special_gift_state = protocol.reward_state[v.day_index].special_gift_state
		data.login_day_num = protocol.login_day_num
		table.insert(self.logindaydata.day_list,data)
		self.logindaydata.len = self.logindaydata.len + 1
	end
	RemindManager.Instance:Fire(RemindName.FestivalLoginRward)
end


function FestivalLoginYouLiWGData:GetCurLoginGrde()
	local had_login_day = self.logindaydata.login_day_num
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local open_day = cur_day - had_login_day + 1
	for k,v in pairs(self.login_activity_cfg) do
		if v.start_server_day <= open_day and v.end_server_day > open_day then
			return k
		end
	end
	return 1
end

--存储选择的天数item
function FestivalLoginYouLiWGData:SetSelectDayItem(item, select_bg)
	self.cur_day_item_select = item
	self.cur_day_item_select_bg = select_bg
end

function FestivalLoginYouLiWGData:GetDyaItemSelect()
	return self.cur_day_item_select, self.cur_day_item_select_bg
end

function FestivalLoginYouLiWGData:DeleteItemSelect()
	self.cur_day_item_select = nil
	self.cur_day_item_select_bg = nil
end

function FestivalLoginYouLiWGData:GetLoginDayData()
	return self.logindaydata
end

function FestivalLoginYouLiWGData:GetLoginDyaIndex()
	if self.logindaydata == nil or self.logindaydata.login_day_num == nil then
		return 0
	end
	return self.logindaydata.login_day_num
end

function FestivalLoginYouLiWGData:GetActivityEndTime()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LOGINGIFT)
	if activity_info ~= nil then
		return activity_info.end_time
	end
	return  0
end

function FestivalLoginYouLiWGData:IsShowLoginRedPoint()
	if not FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_LOGINGIFT) then
		return 0
	end

	if self.logindaydata ~= nil and self.logindaydata.day_list ~= nil then
		local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
		for k,v in ipairs(self.logindaydata.day_list) do
			local cfg = self:GetLoginRewardCfgByDay(v.index)
			if not cfg then 
				return 0
			end
			if v.common_gift_state == LoginYouLiRewardState.KLQ then
				return 1
			end

			if v.special_gift_state == LoginYouLiRewardState.KLQ then
				return 1
			else
				if v.special_gift_state == LoginYouLiRewardState.BKL and v.index <= self.logindaydata.login_day_num then
					if cfg.vip_lv ~= "" and cfg.vip_lv > 0 and cur_vip_level >= cfg.vip_lv then
						return 1
					end
				end
			end
		end
	end
	return 0
end

--领取时获取当前可领取的天数，如果前面有则跳转index-1，如果后面有则跳转index+1，如果当前没有可领则index+1刷新一次，
--若当前的index为最后的index则不跳转
function FestivalLoginYouLiWGData:GetCanRewardDay()
	local canday = {}
	if self.logindaydata ~= nil and self.logindaydata.day_list ~= nil then 
		for i,v in ipairs(self.logindaydata.day_list) do
			--返回所有的领取状态
			table.insert(canday,v.common_gift_state)
		end
		return canday
	end
	return canday 
end

function FestivalLoginYouLiWGData:PrintMsg()
    --/cmd InsertLua FestivalLoginYouLiWGData.Instance:PrintMsg()
    local data = self:GetLoginDayData()
    print_error("协议数据", data)
end

function FestivalLoginYouLiWGData:GetDayInfoByDayIndex(day_index)
	if self.logindaydata and self.logindaydata.day_list then
		for k,v in pairs(self.logindaydata.day_list) do
			if v.index == day_index then
				return v
			end
		end
	end
	return {}
end