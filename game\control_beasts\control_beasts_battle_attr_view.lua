local ATTR_COUNT = 13
local MAX_DISPLAY_NUM = 11

ControlBeastsBattleAttrView = ControlBeastsBattleAttrView or BaseClass(SafeBaseView)

function ControlBeastsBattleAttrView:__init()
	self:SetMaskBg(true, true)
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_attr_tip")
end

function ControlBeastsBattleAttrView:__delete()

end

function ControlBeastsBattleAttrView:ReleaseCallBack()
	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}
end

function ControlBeastsBattleAttrView:LoadCallBack()
	self.node_list.attr_title_name.text.text = Language.ContralBeasts.TitleName12

	self.attr_list = {}
	self.attr_name_list = {}
	self.attr_value_list = {}

	for i=1,ATTR_COUNT do
		self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
		if self.attr_list[i] then
			self.attr_name_list[i] = self.attr_list[i]:FindObj("attr_name")
			self.attr_value_list[i] = self.attr_list[i]:FindObj("attr_value")
		end
	end
	
	self:Flush()
end


function ControlBeastsBattleAttrView:OnFlush(param_t)
	local data_list = self:GetAllAttrData()
	local length = #data_list

	local index = 1
	if not IsEmptyTable(data_list) then
		for i = 1, ATTR_COUNT do
			local attr_data = data_list[i]
			if attr_data and self.attr_list[i] then
				---设置属性
				self.attr_name_list[i].text.text = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_data.attr_str, true, true)
				---设置属性
				local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_data.attr_str)
				local per_desc = is_per and attr_data.attr_value / 100 .. "%" or attr_data.attr_value 
				---基础属性
				if attr_data.attr_value then
					self.attr_value_list[i].text.text = "+" .. per_desc
				end
		   end
			if i <= length then
			   index = index + 1
			end
		end
	end

	if index < MAX_DISPLAY_NUM then
        self.node_list.attr_scroll.scroll_rect.enabled = false
    else
        self.node_list.attr_scroll.scroll_rect.enabled = true
    end

	if not IsEmptyTable(data_list) then
		for i = 1, ATTR_COUNT do
			self.attr_list[i]:SetActive(i <= length)
		end
	end
end

function ControlBeastsBattleAttrView:GetAllAttrData()
	local attr_tab = {}
	local main_list_data = ControlBeastsWGData.Instance:GetHoleMainData()
    local assist_list_data = ControlBeastsWGData.Instance:GetHoleAssistData()

	local function add_tab(attr_list)
		if not attr_list then
			return
		end

		for index, attr_cell in ipairs(attr_list) do
			if attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
				if attr_tab[attr_cell.attr_str] then
					attr_tab[attr_cell.attr_str].attr_value = attr_tab[attr_cell.attr_str].attr_value + attr_cell.attr_value
				else
					attr_tab[attr_cell.attr_str] = {}
					attr_tab[attr_cell.attr_str].attr_str = attr_cell.attr_str
					attr_tab[attr_cell.attr_str].attr_value = attr_cell.attr_value
				end
			end
		end
	end

    -- 主战位
    for _, main_data in ipairs(main_list_data) do
		if main_data.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(main_data.beasts_bag_id)
            if beast_data then
                local base_attr, bast_base_attr = ControlBeastsWGData.Instance:GetBeastsCapValue(beast_data.server_data, nil, nil, main_data.hole_id, main_data.hole_level, true)
				add_tab(base_attr)
				add_tab(bast_base_attr)
            end
        end
    end

    -- 辅战位
    for _, assist_data in ipairs(assist_list_data) do
        if assist_data.beasts_bag_id ~= -1 then
			local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(assist_data.beasts_bag_id)
            if beast_data then
                local base_attr, bast_base_attr = ControlBeastsWGData.Instance:GetBeastsCapValue(beast_data.server_data, nil, nil, assist_data.hole_id, assist_data.hole_level, true)
				add_tab(base_attr)
				add_tab(bast_base_attr)
            end
        end
    end

	local return_table = {}

	for _, attr_data in pairs(attr_tab) do
		if attr_data and attr_data.attr_str and attr_data.attr_str < 1000 then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end
