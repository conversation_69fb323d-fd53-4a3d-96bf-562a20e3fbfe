FEBagCell = FEBagCell or BaseClass(BaseRender)
function FEBagCell:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.cell)
		self.item_cell:SetIsShowTips(false)
		self.item_cell:SetClickCallBack(BindTool.Bind(self.OnItemClick, self))
		self.item_cell:SetCellBg(ResPath.GetCommon("a3_ty_wpk_0"))
	end	

	XUI.AddClickEventListener(self.node_list.get_way, BindTool.Bind(self.OnClickGetWay, self))
end

function FEBagCell:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FEBagCell:OnFlush()
	if IsEmptyTable(self:GetData()) or self:GetData().item_id < 0 or self:GetData().num < 0 then
		if self:GetIndex() == 1 then
			self.node_list.get_way:SetActive(true)
		else
			self.node_list.get_way:SetActive(false)	
		end

		self.item_cell:ClearData()
		--self.item_cell:SetCellBg(ResPath.GetFiveElementsImg("a1_wx_tbdi"))
		return
	end

	self.node_list.get_way:SetActive(false)
	local data = self:GetData()
	local item_data = {item_id = data.item_id, num = data.num, is_bind = data.is_bind}
	self.item_cell:SetData(item_data)

	local part_id = FiveElementsWGData.Instance:GetPartDefaultSelectId()
	local up_flag = FiveElementsWGData.Instance:GetStoreUpFlag(data.item_id, data.color, part_id)
	self.item_cell:SetUpFlagIconVisible(up_flag)
end

function FEBagCell:OnItemClick()
	local cell_data = self:GetData()
	if IsEmptyTable(cell_data) then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local btn_callback_event

	if not IsEmptyTable(item_cfg) then
		btn_callback_event = {}
		btn_callback_event[1] = {btn_text = Language.FiveElements.XiangQian, callback = function()
			local part = FiveElementsWGData.Instance:GetPartDefaultSelectId()
			local hole = FiveElementsWGData.Instance:GetStoreInfoByItemId(self.data.item_id).hole
			local index = self.data.index
			local can_ware = FiveElementsWGData.Instance:IsStoreCanWare(hole, self.data.color)

			if can_ware then
				TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_xiangqian,
	    		is_success = true, pos = Vector2(0, 0)})
				FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_WEAR_STONE, part, hole, index)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElements.InlaidHigherLevel)
			end
		end}
	end

	TipWGCtrl.Instance:OpenItem(cell_data, ItemTip.HANDLE_FIVEELEMENTS_INLAY, nil, nil, btn_callback_event)
end

function FEBagCell:OnClickGetWay()
	FiveElementsWGCtrl.Instance:OpenFiveELementsTreasuryView()
end