require("game/limited_time_offer/limited_time_offer_wg_data")
require("game/limited_time_offer/limited_time_offer_view")

LimitedTimeOfferWGCtrl = LimitedTimeOfferWGCtrl or BaseClass(BaseWGCtrl)

function LimitedTimeOfferWGCtrl:__init()
	if LimitedTimeOfferWGCtrl.Instance then
		<PERSON><PERSON>r<PERSON><PERSON>("[LimitedTimeOfferWGCtrl] attempt to create singleton twice!")
		return
	end
	LimitedTimeOfferWGCtrl.Instance = self

	self.view = LimitedTimeOfferView.New(GuideModuleName.LimitedTimeOffer)
    self.data = LimitedTimeOfferWGData.New()

    self:RegisterAllProtocols()

    self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function LimitedTimeOfferWGCtrl:__delete()
	LimitedTimeOfferWGCtrl.Instance = nil

    if self.role_data_change and RoleWGData.Instance then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
    end
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.data then
		self.data:DeleteMe()
		self.data = nil
    end
end

function LimitedTimeOfferWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOaTimeLimitDiscountInfo, "OnSCOaTimeLimitDiscountInfo")
end

function LimitedTimeOfferWGCtrl:OnSCOaTimeLimitDiscountInfo(protocol)
    self.data:SetActData(protocol)
    self.view:Flush()
    RemindManager.Instance:Fire(RemindName.RemindLimitedTimeOffer)
    self:CheckNeedCloseAct()
end

--领取每日免费物品
function LimitedTimeOfferWGCtrl:SendLimitedTimeOfferOper()
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.LIMITEDTIMEOFFER, TIME_LIMITED_OFFER_OPERA_TYPE.TIME_LIMITED_OFFER_GET_REWARD, 0, 0, 0)
end

--领取额外物品
function LimitedTimeOfferWGCtrl:SendLimitedTimeOfferExtra()
	ServerActivityWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.LIMITEDTIMEOFFER, TIME_LIMITED_OFFER_OPERA_TYPE.TIME_LIMITED_OFFER_GET_EXTRA_REWARD, 0, 0, 0)
end

function LimitedTimeOfferWGCtrl:CheckNeedCloseAct()
	if TimeWGCtrl.Instance:GetCurOpenServerDay() < 0 then
		-- print_error("FFF====== 时间没下来")
		return
    end 
	if self:CheckAllRewardIsGet() then--奖励领取完了,关闭入口
		ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.LIMITEDTIMEOFFER, ACTIVITY_STATUS.CLOSE)
	end
end

function LimitedTimeOfferWGCtrl:CheckAllRewardIsGet()
	return self.data:GetRemainCanBuyPrice() <= 0 and not self.data:GetCanGetFreeItem() and not self.data:GetCanGetExtraItem()
end

function LimitedTimeOfferWGCtrl:SetRealAcivityStatus(status, activity_info)
    self.status = status
    self.activity_info = activity_info
end

function LimitedTimeOfferWGCtrl:RoleLevelChange()
    LimitedTimeOfferWGData.Instance:GetItemShowList(true)
    --处理那些等级不够不能购买，然后现在等级达到可以购买的情况
    if self.status and self.status == ACTIVITY_STATUS.OPEN and not self:CheckAllRewardIsGet() and self.activity_info then
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.LIMITEDTIMEOFFER, ACTIVITY_STATUS.OPEN, self.activity_info.next_status_switch_time, 
        self.activity_info.param_1, self.activity_info.param_2, self.activity_info.open_type)
    end
end