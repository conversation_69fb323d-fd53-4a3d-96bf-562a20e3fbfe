ShenShouQiangHuaView = ShenShouQiangHuaView or BaseClass(SafeBaseView)

local index_map_color_list = {
	5,  --红色
	4, 	--橙色
	3,	--紫色
	2,	--蓝色
	1,	--绿色
}

function ShenShouQiangHuaView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",{vector2 = Vector2(0, 10), sizeDelta = Vector2(1080, 612)})
	self:AddViewResource(0, "uis/view/shenshou_ui_prefab", "layout_shenshou_eq")
end

function ShenShouQiangHuaView:LoadCallBack()
	if not self.soul_ring_list then
		self.soul_ring_list = AsyncListView.New(ShenShouSthSoulRingCellRender, self.node_list.soul_ring_list)
		self.soul_ring_list:SetSelectCallBack(BindTool.Bind(self.SelectSoulRingItemCallBack, self))
	end

	self.select_equip_cell_index = -1
	self.shenshou_cells = {}
	for i = 0, 4 do
		self.shenshou_cells[i] = ShenShouEquipCell.New(self.node_list["soul_ring_equip_" .. i])
		self.shenshou_cells[i]:SetIsShowTips(false)
		self.shenshou_cells[i]:SetIndex(i)
		self.shenshou_cells[i]:AddClickEventListener(BindTool.Bind(self.SelectSoulRingEquipItemCallBack, self, self.shenshou_cells[i], true))
	end

	-- if not self.sth_stuff_bag_grid then
	-- 	self.sth_stuff_bag_grid = ShenShouGrid.New()
	-- 	self.sth_stuff_bag_grid:CreateCells({
	-- 		col = 4,
	-- 		cell_count = 16,
	-- 		change_cells_num = 2,
	-- 		list_view = self.node_list["sth_stuff_bag_grid"],
	-- 		itemRender = ShenShouSthBagCell
	-- 	})
	-- 	self.sth_stuff_bag_grid:SetIsMultiSelect(true)
	-- 	self.sth_stuff_bag_grid:SetIsShowTips(false)
	-- 	self.sth_stuff_bag_grid:SetStartZeroIndex(false)
	-- 	self.sth_stuff_bag_grid:SetSelectCallBack(BindTool.Bind(self.SelectShenShouBagCellCallBack, self))
	-- end

	if not self.cureq_cell then
		self.cureq_cell = ShenShouEquipCell.New(self.node_list["ph_cureq_cell"])
	end

	-- if not self.color_list then
	-- 	self.color_list = AsyncListView.New(ShenShouSthPinZhiListRender, self.node_list.color_list)
	-- 	self.color_list:SetSelectCallBack(BindTool.Bind(self.SelectColorCallBack, self))

	-- 	self.cur_select_color_index = RoleWGData.GetRolePlayerPrefsInt("shenshou_sth_select_pinzhi_color")
	-- 	if self.cur_select_color_index < 1 then
	-- 		self.cur_select_color_index = 1 -- 默认
	-- 		RoleWGData.SetRolePlayerPrefsInt("shenshou_sth_select_pinzhi_color", self.cur_select_color_index)
	-- 	end
	
	-- 	self.color_list:SetDefaultSelectIndex(self.cur_select_color_index)
	-- 	self.color_list:SetDataList(Language.ShenShou.SoulRingEquipSTHNameList2)
	-- 	self.node_list.sth_lbl_select.text.text = Language.ShenShou.SoulRingEquipSTHNameList2[self.cur_select_color_index]
	-- end

	-- self.is_show_color_part = false
	-- self:OnClickSelectColor()
	-- XUI.AddClickEventListener(self.node_list.btn_select_equip, BindTool.Bind(self.OnClickSelectColor, self))
	-- XUI.AddClickEventListener(self.node_list.close_order_list_part, BindTool.Bind(self.OnClickSelectColor, self))
	-- XUI.AddClickEventListener(self.node_list.btn_use_stone, BindTool.Bind(self.OnClickUseStoneBtn, self))
	-- XUI.AddClickEventListener(self.node_list.btn_sue_lingyu, BindTool.Bind(self.OnClickUseLingyu, self))
	XUI.AddClickEventListener(self.node_list.btn_sth_qianghua, BindTool.Bind(self.OnClickShenShouQh, self))
	XUI.AddClickEventListener(self.node_list.btn_de_compose, BindTool.Bind(self.OnClickShenShouDeCompose, self))
	XUI.AddClickEventListener(self.node_list.up_cost_icon, BindTool.Bind(self.OnClickTargetIconBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_one_key, BindTool.Bind(self.OnClickOneKeyBtn, self))

	local other_cfg = ShenShouWGData.Instance:GetShenShouOtherCfg()
	if other_cfg and other_cfg.spar_id then
		local bundle, asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(other_cfg.spar_id))
		self.node_list.up_cost_icon.image:LoadSprite(bundle, asset, function ()
			self.node_list.up_cost_icon.image:SetNativeSize()
		end)
	end

	self.select_equip_data = {}
	self.select_slot_index = -1
	self.select_soul_ring_seq = -1
	self.select_soul_ring_index = -1
	self.select_soul_ring_equip_index = -1
	self.select_soul_ring_data = {}
	-- self.need_gold = 0
	self.is_auto_up = false

	self:StopAutoEquipStrength()
	self.node_list.title_view_name.text.text = Language.ShenShou.DeComposeViewTitleViewName
end

function ShenShouQiangHuaView:CloseCallBack()
	self:StopAutoEquipStrength()
end

function ShenShouQiangHuaView:ReleaseCallBack()
	if self.cureq_cell then
		self.cureq_cell:DeleteMe()
		self.cureq_cell = nil
	end

	if self.soul_ring_list then
		self.soul_ring_list:DeleteMe()
		self.soul_ring_list = nil
	end

	-- if self.color_list then
	-- 	self.color_list:DeleteMe()
	-- 	self.color_list = nil
	-- end

	-- if self.sth_stuff_bag_grid then
	-- 	self.sth_stuff_bag_grid:DeleteMe()
	-- 	self.sth_stuff_bag_grid = nil
	-- end

	if self.shenshou_cells then
		for k, v in pairs(self.shenshou_cells) do
			v:DeleteMe()
		end

		self.shenshou_cells = nil
	end
end

function ShenShouQiangHuaView:OnFlush()
	local data_list = ShenShouWGData.Instance:GetStrengthSoulRingDataList()
	self.soul_ring_list:SetDataList(data_list)
	self.soul_ring_list:JumpToIndex(self:CalSoulRingSelect(data_list))

	-- self:FlushBagInfo()
end

function ShenShouQiangHuaView:CalSoulRingSelect(data_list)
	if self.select_soul_ring_index >= 1 then
		if ShenShouWGData.Instance:IsSoulRingHasCanStrengthEquip(self.select_soul_ring_seq) > 0 then
			return self.select_soul_ring_index
		end
	end

	for k, v in pairs(data_list) do
		if ShenShouWGData.Instance:IsSoulRingHasCanStrengthEquip(v.soul_ring_seq) > 0 then
			return k
		end
	end

	return self.select_soul_ring_index >= 1 and self.select_soul_ring_index or 1
end

-- 选中魂环
function ShenShouQiangHuaView:SelectSoulRingItemCallBack(item)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	
	if self.select_soul_ring_seq ~= data.soul_ring_seq then
		self:StopAutoEquipStrength()
	end

	self.select_soul_ring_seq = data.soul_ring_seq
	self.select_soul_ring_data = data
	self.select_soul_ring_index = item.index

	local equip_list = ShenShouWGData.Instance:GetShenShouEquipInfoList(data.soul_ring_seq)
	for i = 0, 4 do
		self.shenshou_cells[i]:SetData(equip_list[i])
		if equip_list[i].item_id <= 0 then
			local alpha_color = Color.New(1, 1, 1, 0.7)
			self.shenshou_cells[i]:SetItemIcon(ResPath.GetShenShouImages("a3_thsq_icon_" .. i))
			self.shenshou_cells[i]:SetItemIconAlpha(alpha_color)
		end
	end

	-- 选中装备
	self.select_equip_cell_index = self:CalSoulRingEquipSelect(equip_list)
	self:SelectSoulRingEquipItemCallBack(self.shenshou_cells[self.select_equip_cell_index], false)

	local is_all_equip_max_level = ShenShouWGData.Instance:IsSoulRingAllEquipMaxLevel(self.select_soul_ring_seq)
	self.node_list.flag_max_level:CustomSetActive(is_all_equip_max_level)
	self.node_list.btn_one_key:CustomSetActive(not is_all_equip_max_level)
	self.node_list.up_cost_root:CustomSetActive(not is_all_equip_max_level)
	self.node_list.btn_one_key_remind:CustomSetActive(ShenShouWGData.Instance:IsSoulRingHasCanStrengthEquip(self.select_soul_ring_seq) > 0)
end

-- 魂环装备选中
function ShenShouQiangHuaView:CalSoulRingEquipSelect(equip_list)
	if self.select_equip_cell_index >= 0 then
		local item_id = (equip_list[self.select_equip_cell_index] or {}).item_id or 0
		if item_id > 0 then
			if ShenShouWGData.Instance:IsShenShouEquipStrengthUp(self.select_soul_ring_seq, self.select_slot_index) then
				return self.select_equip_cell_index
			end
		end
	end

	local default_k = -1
	for k, v in pairs(equip_list) do
		local item_id = v.item_id or 0
		if item_id > 0 then
			if ShenShouWGData.Instance:IsShenShouEquipStrengthUp(self.select_soul_ring_seq, v.slot_index) then
				return k
			end

			if default_k < 0 then
				default_k = k
			end
		end
	end

	local item_id = (equip_list[self.select_equip_cell_index] or {}).item_id or 0
	if item_id > 0 then
		return self.select_equip_cell_index
	else
		return default_k
	end
end

-- 选中装备
function ShenShouQiangHuaView:SelectSoulRingEquipItemCallBack(item, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data

	if data.item_id <= 0 then
		return 
	end

	self.select_equip_data = data
	self.select_equip_cell_index = item.index
	self.select_slot_index = data.slot_index
	self.select_soul_ring_equip_index = item.index

	for k, v in pairs(self.shenshou_cells) do
		v:SetSelectIndex(self.select_equip_cell_index)
	end

	self.cureq_cell:SetData(data)
	self:FlushInfo()
end

-- function ShenShouQiangHuaView:CalAddShuLianDu()
-- 	local add_shuliandu = 0   
-- 	local strength_shuliandu = 0
-- 	local select_list = self.sth_stuff_bag_grid:GetAllSelectCell()

-- 	if not IsEmptyTable(select_list) then
-- 		for k, v in pairs(select_list) do
-- 			local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(v.item_id)

-- 			if not IsEmptyTable(shenshou_equip_cfg) then
-- 				local is_use_lingyu = ShenShouWGData.Instance:GetUseLingyuFlag()

-- 				if shenshou_equip_cfg.is_equip == 0 then
-- 					-- 灵石
-- 					add_shuliandu = add_shuliandu + shenshou_equip_cfg.contain_shulian * (is_use_lingyu and 2 or 1)
-- 				else
-- 					-- 装备  装备是带强化等级得 等不不在槽位上
-- 					local strength_shenshou_cfg = ShenShouWGData.Instance:GetShenshouLevelList(shenshou_equip_cfg.slot_index, v.strength_level)
-- 					if strength_shenshou_cfg then
-- 						if v.strength_level < 1 then
-- 							if is_use_lingyu then
-- 								add_shuliandu = add_shuliandu + (shenshou_equip_cfg.contain_shulian + strength_shenshou_cfg.contain_shulian + v.shuliandu) * 2
-- 							else
-- 								add_shuliandu = add_shuliandu + shenshou_equip_cfg.contain_shulian + strength_shenshou_cfg.contain_shulian + v.shuliandu
-- 							end
-- 						else
-- 							strength_shuliandu = strength_shuliandu + shenshou_equip_cfg.contain_shulian + strength_shenshou_cfg.contain_shulian + v.shuliandu
-- 						end
-- 					end
-- 				end
-- 			end
-- 		end
-- 	end

-- 	return add_shuliandu, strength_shuliandu
-- end

function ShenShouQiangHuaView:FlushInfo()
	if IsEmptyTable(self.select_equip_data) then
		return 
	end

	local strength_level = self.select_equip_data.strength_level
	local next_strength_cfg = ShenShouWGData.Instance:GetShenshouLevelList(self.select_slot_index, strength_level + 1)
	local is_max_level = IsEmptyTable(next_strength_cfg)
	self.node_list.img_equip_ss_left_level.text.text = string.format(Language.Common.Level, strength_level)
	local shenshou_exp = ShenShouWGData.Instance:GetShenShouExp()

	self.node_list.img_strength_lifting_arrow:CustomSetActive(not is_max_level)
	self.node_list.img_equip_ss_right_level:CustomSetActive(not is_max_level)
	-- self.node_list.btn_one_key:CustomSetActive(not is_max_level)
	-- self.node_list.up_cost_root:CustomSetActive(not is_max_level)
	-- self.node_list.flag_max_level:CustomSetActive(is_max_level)

	local cur_strength_cfg = ShenShouWGData.Instance:GetShenshouLevelList(self.select_equip_data.slot_index, strength_level)
	
	if is_max_level then
		self.node_list.lbl_attr_title.text.text = Language.ShenShou.MaxAttr
	else
		self.node_list.lbl_attr_title.text.text = Language.ShenShou.NextAttr
		self.node_list.img_equip_ss_right_level.text.text = string.format(Language.Common.Level, next_strength_cfg.strength_level)

		local color = shenshou_exp >= (cur_strength_cfg.upgrade_need_shulian - self.select_equip_data.shuliandu) and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.node_list.up_costt_text.text.text = ToColorStr(shenshou_exp, color) .. "/" ..  (cur_strength_cfg.upgrade_need_shulian - self.select_equip_data.shuliandu)
	end

	local cur_content = {}
	local cur_content = EquipWGData.GetSortAttrListHaveNextByCfg(cur_strength_cfg, next_strength_cfg)

	for i = 1, 2 do
		local data = cur_content[i]
		local has_data = not IsEmptyTable(data)
		self.node_list["sth_attr_" .. i]:CustomSetActive(has_data)

		if has_data then
			self.node_list["sth_attr_name" .. i].text.text = data.attr_name or EquipmentWGData.Instance:GetAttrName(data.attr_str, true, false)
			self.node_list["sth_attr_value" .. i].text.text = data.attr_value
			self.node_list["sth_add_value" .. i].text.text = data.add_value > 0 and data.add_value or ""
			self.node_list["sth_add_flag" .. i]:CustomSetActive(data.add_value and data.add_value > 0)
		end
	end

	-- self.node_list.btn_one_key_remind:CustomSetActive(not is_max_level and (shenshou_exp > ((cur_strength_cfg.upgrade_need_shulian - self.select_equip_data.shuliandu))))
	-- self:StartAutoEquipStrength(not is_max_level and (shenshou_exp > ((cur_strength_cfg.upgrade_need_shulian - self.select_equip_data.shuliandu))))
	self:StartAutoEquipStrength()

	-- if is_max_level then
		-- self.node_list.pre_progress_bar.slider.value = 0
		-- self.node_list.progress_txt.text.text = "--/--"
		-- self:FlushShenShouEqAttr(true, strength_level, strength_level)
	-- else
	-- 	local other_cfg = ShenShouWGData.Instance:GetShenShouOtherCfg()
	-- 	local strength_cfg = ShenShouWGData.Instance:GetShenshouLevelList(self.select_equip_data.slot_index, self.select_equip_data.strength_level)

	-- 	if IsEmptyTable(strength_cfg) then
	-- 		return 
	-- 	end

		-- local add_shuliandu, strength_shuliandu = self:CalAddShuLianDu()
		-- local str = ""

		-- if add_shuliandu > 0 or strength_shuliandu > 0 then
		-- 	str = string.format(Language.ShenShou.ShuLianDu, self.select_equip_data.shuliandu, add_shuliandu + strength_shuliandu) .. "/".. strength_cfg.upgrade_need_shulian
		-- else
		-- 	str = self.select_equip_data.shuliandu .. "/".. strength_cfg.upgrade_need_shulian
		-- end

		-- self.node_list.pre_progress_bar.slider.value = (self.select_equip_data.shuliandu + add_shuliandu + strength_shuliandu)/strength_cfg.upgrade_need_shulian
		-- self.node_list.progress_txt.text.text = str

		-- local use_lingyu = 0
		-- local is_use_lingyu = ShenShouWGData.Instance:GetUseLingyuFlag()
		-- if is_use_lingyu then
		-- 	local role_gold = RoleWGData.Instance.role_info.gold
		-- 	local need_gold = math.ceil(add_shuliandu / (is_use_lingyu and other_cfg.equip_double_shulian_per_gold * 2 or other_cfg.equip_double_shulian_per_gold))
		-- 	local color = role_gold >= need_gold and "#9DF5A7" or COLOR3B.RED
		-- 	self.node_list.rich_use_gold.text.text = ToColorStr(need_gold, color)
		-- 	self.need_gold = need_gold
		-- end

		-- 能够提升到的等级
		-- local can_qh_lv = ShenShouWGData.Instance:GetCanQhLv(self.select_equip_data, add_shuliandu + strength_shuliandu)
		-- can_qh_lv = is_max_level and self.select_equip_data.strength_level or (can_qh_lv > self.select_equip_data.strength_level and can_qh_lv or self.select_equip_data.strength_level + 1)
		-- self.node_list["img_equip_ss_right_level"].text.text = string.format(Language.Common.Level, can_qh_lv)

		-- self:FlushShenShouEqAttr(false, strength_level, can_qh_lv)
	-- end
end

-- function ShenShouQiangHuaView:FlushShenShouEqAttr(is_max_level, cur_level, next_level)
-- 	-- self.node_list.layout_strength:CustomSetActive(not is_max_level)
-- 	-- XUI.SetButtonEnabled(self.node_list.btn_qianghua, not is_max_level)

-- 	local cur_strength_cfg = ShenShouWGData.Instance:GetShenshouLevelList(self.select_equip_data.slot_index, cur_level)
-- 	local next_strength_cfg = ShenShouWGData.Instance:GetShenshouLevelList(self.select_equip_data.slot_index, next_level)

-- 	if is_max_level then
-- 		self.node_list.prog_sseq_progress.slider.value = 1
-- 		self.node_list.lbl_attr_title.text.text = Language.ShenShou.MaxAttr
-- 	else
-- 		self.node_list.lbl_attr_title.text.text = Language.ShenShou.NextAttr

-- 		local cur_cfg = ShenShouWGData.Instance:GetShenshouLevelList(self.select_equip_data.slot_index, self.select_equip_data.strength_level)
-- 		local percent = self.select_equip_data.shuliandu / cur_cfg.upgrade_need_shulian * 100
-- 		local cur_progress = percent / 100
-- 		self.node_list.prog_sseq_progress.slider.value = cur_progress
-- 	end

-- 	local cur_eq_struct = AttributeMgr.GetAttributteByClass(cur_strength_cfg)
-- 	local next_eq_struct = AttributeMgr.GetAttributteByClass(next_strength_cfg)
-- 	local add_attribute = AttributeMgr.LerpAttributeAttr(cur_eq_struct, next_eq_struct)
-- 	local attr_keys = AttributeMgr.SortAttribute()
-- 	local cur_content = {}
-- 	local index = 1
-- 	local is_per

-- 	for k,v in pairs(attr_keys) do
-- 		if cur_eq_struct[v] and add_attribute[v] then
-- 			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
-- 			local cur_value = math.floor(cur_eq_struct[v])
-- 			local eq_add_value = math.floor(add_attribute[v])
-- 			if cur_value > 0 then
-- 				cur_value = is_per and (cur_value / 100 .. '%') or cur_value
-- 				if eq_add_value > 0 then
-- 					eq_add_value = is_per and (eq_add_value / 100 .. '%') or eq_add_value
-- 					table.insert(cur_content, {attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v, true), attr_value = cur_value, add_value = eq_add_value + cur_value})
-- 				else
-- 					table.insert(cur_content, {attr_name =EquipmentWGData.Instance:GetAttrNameByAttrStr(v, true), attr_value = cur_value, add_value = 0} )
-- 				end
-- 				index = index + 1
-- 			else
-- 				if eq_add_value > 0 then
-- 					eq_add_value = is_per and (eq_add_value / 100 .. '%') or eq_add_value
-- 					table.insert(cur_content, {attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v, true), attr_value = cur_value, add_value = eq_add_value + cur_value})
-- 					index = index + 1
-- 				end
-- 			end
-- 		end
-- 		if index > 4 then break end
-- 	end

-- 	for i = 1, 2 do
-- 		local data = cur_content[i]
-- 		local has_data = not IsEmptyTable(data)
-- 		self.node_list["sth_attr_" .. i]:CustomSetActive(has_data)

-- 		if has_data then
-- 			self.node_list["sth_attr_name" .. i].text.text = data.attr_name
-- 			self.node_list["sth_attr_value" .. i].text.text = data.attr_value
-- 			self.node_list["sth_add_value" .. i].text.text = data.add_value > 0 and data.add_value or ""
-- 		end
-- 	end
-- end

-- -- 选中消耗装备
-- function ShenShouQiangHuaView:SelectShenShouBagCellCallBack(cell)
-- 	self:FlushInfo()
-- end

-- function ShenShouQiangHuaView:FlushBagInfo()
-- 	local bag_data_list = ShenShouWGData.Instance:GetShenShouBagDataList()
-- 	local has_data = not IsEmptyTable(bag_data_list)

-- 	self.node_list.no_stuff_tip:CustomSetActive(not has_data)
-- 	self.node_list.sth_stuff_bag_grid:CustomSetActive(has_data)
-- 	self.node_list.btn_get_equip:CustomSetActive(not has_data)
-- 	self.node_list.qianghua_root:CustomSetActive(has_data)

-- 	if has_data then
-- 		self.sth_stuff_bag_grid:SetDataList(bag_data_list)
-- 		self.sth_stuff_bag_grid:CancleAllSelectCell()
-- 	end

-- 	self:FlushUseStoneInfo()
-- 	self:FlushUseLingyuInfo()
-- end

-- function ShenShouQiangHuaView:SelectColorCallBack(cell)
-- 	local data = cell and cell:GetData()
-- 	if data == nil then
-- 		return
-- 	end

-- 	local index = cell:GetIndex()
-- 	self.cur_select_color_index = index
-- 	self.node_list.sth_lbl_select.text.text = Language.ShenShou.SoulRingEquipSTHNameList2[self.cur_select_color_index]

-- 	self:OnClickSelectColor()

-- 	local color = index_map_color_list[self.cur_select_color_index] or 0
-- 	-- self.sth_stuff_bag_grid:SetMeltingOneKeyColorSelcet(color)
-- 	self.sth_stuff_bag_grid:GetAllCellExceptCondition(color)
-- 	self:FlushInfo()
-- end

-- function ShenShouQiangHuaView:OnClickSelectColor()
-- 	self.is_show_color_part = not self.is_show_color_part
-- 	self.node_list["order_list_part"]:SetActive(self.is_show_color_part)
-- 	self.node_list["sth_arrow_top"]:SetActive(self.is_show_color_part)
-- 	self.node_list["sth_arrow_right"]:SetActive(not self.is_show_color_part)
-- end

-- function ShenShouQiangHuaView:OnClickUseStoneBtn()
-- 	local use_stone_flag = ShenShouWGData.Instance:GetUseStoneFlag()
-- 	ShenShouWGData.Instance:SetUseStoneFlag(not use_stone_flag)

-- 	if not use_stone_flag then
-- 		if not ShenShouWGData.Instance:IsEquipBagHasStoneFlag() then
-- 			SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.SoulRingEquipNoStoneTip)

-- 			local other_cfg = ShenShouWGData.Instance:GetShenShouOtherCfg()

-- 			if other_cfg and other_cfg.spar_id then
-- 				TipWGCtrl.Instance:OpenItem({item_id = other_cfg.spar_id})
-- 			end
-- 		end
-- 	end

-- 	self:FlushUseStoneInfo()
-- end

-- function ShenShouQiangHuaView:FlushUseStoneInfo()
-- 	local use_stone_flag = ShenShouWGData.Instance:GetUseStoneFlag()
-- 	self.node_list.use_stone_flag:CustomSetActive(use_stone_flag)
-- 	self.sth_stuff_bag_grid:SetAllStoneCellSelectState(use_stone_flag)

-- 	self:FlushInfo()
-- end

-- function ShenShouQiangHuaView:OnClickUseLingyu()
-- 	local use_lingyu_flag = ShenShouWGData.Instance:GetUseLingyuFlag()

-- 	local function use_lingyu()
-- 		ShenShouWGData.Instance:SetUseLingyuFlag(not use_lingyu_flag)
-- 		self:FlushUseLingyuInfo()
-- 	end

-- 	if not use_lingyu_flag then
-- 		TipWGCtrl.Instance:OpenCheckTodayAlertTips(Language.ShenShou.DoubleCheckTip, function ()
-- 			use_lingyu()
-- 		end, "ShenShouQiangHuaUseLingYu", Language.ShenShou.CheckBoxText, nil, Language.ShenShou.CancelText)
-- 	else
-- 		use_lingyu()
-- 	end
-- end

-- function ShenShouQiangHuaView:FlushUseLingyuInfo()
-- 	local use_lingyu_flag = ShenShouWGData.Instance:GetUseLingyuFlag()
-- 	self.node_list.use_lingyu_flag:CustomSetActive(use_lingyu_flag)
-- 	self.node_list.need_gold_root:CustomSetActive(use_lingyu_flag)
-- 	self:FlushInfo()
-- end

function ShenShouQiangHuaView:OnClickShenShouQh()
	local selected_cells = self.sth_stuff_bag_grid:GetAllSelectCell()

	if IsEmptyTable(selected_cells) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.ExtraTip6)
		return
	end

	local quality_tip = false
	local equip_index_list = {}
	for k, v in pairs(selected_cells) do
		equip_index_list[#equip_index_list + 1] = v.index

		local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(v.item_id)
		local star_count = ShenShouWGData.Instance:GetStarCount(v, shenshou_equip_cfg)
		if star_count >= 3 and shenshou_equip_cfg.quality >= 3 then
			quality_tip = true
		end
	end

	local use_lingyu_flag = ShenShouWGData.Instance:GetUseLingyuFlag()
	local double_flag = use_lingyu_flag and 1 or 0

	if quality_tip then
		TipWGCtrl.Instance:OpenCheckTodayAlertTips(Language.ShenShou.ChectQHEquip, function ()
			ShenShouWGCtrl.Instance:SendSHenshouReqStrength(self.select_soul_ring_seq, self.select_equip_data.slot_index, double_flag, #selected_cells, equip_index_list)
		end, "ShenShouQiangHuaOPA", Language.ShenShou.CheckBoxText, nil, Language.ShenShou.CancelText)

		return
	end

	if use_lingyu_flag then
		local str = string.format(Language.ShenShou.UseDoubleShuLianDu, self.need_gold)
		TipWGCtrl.Instance:OpenCheckTodayAlertTips(str, function ()
			ShenShouWGCtrl.Instance:SendSHenshouReqStrength(self.select_soul_ring_seq, self.select_equip_data.slot_index, double_flag, #selected_cells, equip_index_list)
		end, "ShenShouQiangHuaUseLingYu", Language.ShenShou.CheckBoxText, nil, Language.ShenShou.CancelText)

		return
	end

	ShenShouWGCtrl.Instance:SendSHenshouReqStrength(self.select_soul_ring_seq, self.select_equip_data.slot_index, double_flag, #selected_cells, equip_index_list)
end

function ShenShouQiangHuaView:OnClickShenShouDeCompose()
	ShenShouWGCtrl.Instance:OpenShenShouDeComposeView()
end

function ShenShouQiangHuaView:OnClickTargetIconBtn()
	local other_cfg = ShenShouWGData.Instance:GetShenShouOtherCfg()
	if other_cfg and other_cfg.spar_id then
		TipWGCtrl.Instance:OpenItem({item_id = other_cfg.spar_id})
	end
end

function ShenShouQiangHuaView:OnClickOneKeyBtn()
	-- if self.is_auto_up then
	-- 	self:StopAutoEquipStrength()
	-- else
	-- 	if IsEmptyTable(self.select_equip_data) then
	-- 		self:StopAutoEquipStrength()
	-- 	else
	-- 		self.is_auto_up = true

	-- 		if self.node_list.desc_btn_one_key then
	-- 			self.node_list.desc_btn_one_key.text.text = Language.Common.UpLevelOneKeyBtnText[2]
	-- 		end

	-- 		self:OnStrengthHander(true)
	-- 	end
	-- end

	if self.is_auto_up then
		self:StopAutoEquipStrength()
	else
		if IsEmptyTable(self.select_soul_ring_data) then
			self:StopAutoEquipStrength()
		else
			self.is_auto_up = true

			if self.node_list.desc_btn_one_key then
				self.node_list.desc_btn_one_key.text.text = Language.Common.UpLevelOneKeyBtnText[2]
			end

			self:OnStrengthHander(true)
		end
	end
end

function ShenShouQiangHuaView:StopAutoEquipStrength()
	self.is_auto_up = false

	if self.node_list.desc_btn_one_key then
		self.node_list.desc_btn_one_key.text.text = Language.Common.UpLevelOneKeyBtnText[1]
	end
end

function ShenShouQiangHuaView:StartAutoEquipStrength()
	if self.is_auto_up then
		local can_up = ShenShouWGData.Instance:IsSoulRingHasCanStrengthEquip(self.select_soul_ring_seq)

		if can_up > 0 then
			TryDelayCall(self, function ()
				self:OnStrengthHander()
			end, 0.3, "StartAutoShenShowEquipStrength")
		else
			self:StopAutoEquipStrength()
		end
	else
		self:StopAutoEquipStrength()
	end
end

function ShenShouQiangHuaView:OnStrengthHander(need_tip)
	if IsEmptyTable(self.select_soul_ring_data) then
		self:StopAutoEquipStrength()
		return
	end

	local remind = ShenShouWGData.Instance:IsSoulRingHasCanStrengthEquip(self.select_soul_ring_seq)

	if remind > 0 then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(-165, -120)})
		-- ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.EQUIP_UPGRADE, self.select_soul_ring_seq, self.select_equip_data.slot_index)
		ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.EQUIP_UPGRADE, self.select_soul_ring_seq, -1)
	else
		if ShenShouWGData.Instance:IsSoulRingAllEquipMaxLevel(self.select_soul_ring_seq) then
		
		else
			if need_tip then
				self:OnClickTargetIconBtn()
			end
		end
		self:StopAutoEquipStrength()
	end

	-- local strength_level = self.select_equip_data.strength_level
	-- local next_strength_cfg = ShenShouWGData.Instance:GetShenshouLevelList(self.select_slot_index, strength_level + 1)
	-- local is_max_level = IsEmptyTable(next_strength_cfg)

	-- if is_max_level then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.EquipUpMaxLevelStr)
	-- 	self:StopAutoEquipStrength()
	-- 	return
	-- end

	-- local shenshou_exp = ShenShouWGData.Instance:GetShenShouExp()
	-- local cur_strength_cfg = ShenShouWGData.Instance:GetShenshouLevelList(self.select_slot_index, strength_level)
	-- local need_exp = cur_strength_cfg.upgrade_need_shulian - self.select_equip_data.shuliandu

	-- if shenshou_exp >= need_exp then
	-- 	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(-165, -120)})
	-- 	ShenShouWGCtrl.Instance:SendShenshouOperaReq(SHENSHOU_REQ_TYPE.EQUIP_UPGRADE, self.select_soul_ring_seq, self.select_equip_data.slot_index)
	-- else
	-- 	if need_tip then
	-- 		self:OnClickTargetIconBtn()
	-- 	end

	-- 	self:StopAutoEquipStrength()
	-- end
end

------------------------------------ShenShouSthSoulRingCellRender-----------------------------------
ShenShouSthSoulRingCellRender = ShenShouSthSoulRingCellRender or BaseClass(BaseRender)

function ShenShouSthSoulRingCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local bundle, asset = ResPath.GetShenShouImages("a3_soul_ring_icon_hl_" .. self.data.icon)
	self.node_list.icon.image:LoadSprite(bundle, asset, function()
		self.node_list.icon.image:SetNativeSize()
	end)

	local remind = ShenShouWGData.Instance:IsSoulRingHasCanStrengthEquip(self.data.soul_ring_seq)
	self.node_list.remind:CustomSetActive(remind > 0)
end

function ShenShouSthSoulRingCellRender:OnSelectChange(is_select)
	self.node_list.select:CustomSetActive(is_select)
end

-----------------------------ShenShouSthBagCell-------------------------------
-- ShenShouSthBagCell = ShenShouSthBagCell or BaseClass(ShenShouEquipCell)
-- function ShenShouSthBagCell:__init()
-- 	--self:NeedDefaultEff(false)
-- 	self:UseNewSelectEffect(true)
-- 	--self:SetFlagIcon(false)
-- end

-- function ShenShouSthBagCell:SetSelect(is_select, item_call_back)
-- 	if is_select and IsEmptyTable(self.data) then
-- 		return
-- 	end
-- 	ShenShouEquipCell.SetSelectEffect(self, is_select)
-- end

----------------------------------------------ShenShouSthPinZhiListRender------------------------------------------
-- ShenShouSthPinZhiListRender = ShenShouSthPinZhiListRender or BaseClass(BaseRender)
-- function ShenShouSthPinZhiListRender:OnFlush()
-- 	self.node_list.lbl_pinjie_name.text.text = self.data
-- 	self.node_list.select_pinjie_bg:SetActive(self.is_select)
-- end

-- function ShenShouSthPinZhiListRender:OnSelectChange(is_select)
-- 	if self.node_list.select_pinjie_bg then
-- 		self.node_list.select_pinjie_bg:SetActive(is_select)
-- 	end
-- end
--------------------------------------------------------------------------------