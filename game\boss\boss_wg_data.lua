BossWGData = BossWGData or BaseClass()

BossWGData.MAP_WIDTH = 671
BossWGData.MAP_HEIGHT = 384
BossWGData.TUTENGMAX = 12

BossWGData.HMSY_INDEX = 100000	--分开入侵服同bossid不同信息

BossWGData.ShowOpenDayMax = 10	--按照天数显示不同的展示道具
local CountDownKey = "OpenBossQuickRebirthShow"
--boss首杀
BossWGData.CS_BOSS_FIRST_KILL_TYPE =
{
    CS_BOSS_FIRST_KILL_TYPE_INFO = 0,			-- 请求全服信息
    CS_BOSS_FIRST_KILL_TYPE_FETCHPERSON = 1,	-- param_0(boss_id) 个人
    CS_BOSS_FIRST_KILL_TYPE_FETCHTOTAL = 2,		-- param_0(boss_id) 全服
};

BossWGData.BossType = {
	WORLD_BOSS = 1,
	VIP_BOSS = 2,
	DABAO_BOSS = 3,
	PERSON_BOSS = 4,
	KF_BOSS = 5 ,
    SG_BOSS = 6 ,
    HMSY_BOSS = 7,
    <PERSON><PERSON>uanBoss = 8,
	EverydayrechargeBoss = 9,
}

BossWGData.BossOfferType = {
	WORLD_BOSS = 0,
	PERSON_BOSS = 1,
	VIP_BOSS = 2,
	DABAO_BOSS = 3,
	SG_BOSS = 4,
}

BossWGData.BossOpenType = {
	WORLD_BOSS = 0,
	DABAO_BOSS = 1,
	VIP_BOSS = 2,
    SG_BOSS = 3,
    KF_BOSS = 6,
    SY_BOSS = 8,
	PERSON_BOSS = 12,
}

BossWGData.MonsterType ={
	Boss = 0,
	Monster = 1,
	Gather = 2,
	HideBoss = 3,
}

BossWGData.SceneType = {
    [COMMON_CONSTS.XianJieBossIdx] = SceneType.XianJie_Boss, --仙界boss
	[0] = SceneType.WorldBoss,
	[1] = SceneType.DABAO_BOSS,
	[2] = SceneType.VIP_BOSS,
	[3] = SceneType.SG_BOSS,
	[4] = SceneType.MJ_BOSS,
    [5] = SceneType.Shenyuan_boss,   --九幽魔镜
	[6] = SceneType.KF_BOSS,         -- 魔域空间
	[7] = SceneType.MJ_KF_BOSS,
	[8] = SceneType.KFSHENYUN_FB,
	[9] = SceneType.HONG_MENG_SHEN_YU,
    [10] = SceneType.TIANSHEN_JIANLIN,
    [11] = SceneType.XINGTIANLAIXI_FB,
    [12] = SceneType.MOWU_JIANLIN,
    [14] = SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS,
	[15] = SceneType.WORLD_TREASURE_JIANLIN,
}

BossWGData.BossViewScene = {
	[SceneType.WorldBoss] = BossViewIndex.WorldBoss,
	[SceneType.DABAO_BOSS] = BossViewIndex.DabaoBoss,
	[SceneType.VIP_BOSS] = BossViewIndex.VipBoss,
	[SceneType.PERSON_BOSS] = BossViewIndex.PersonalBoss,
	[SceneType.SG_BOSS] = BossViewIndex.SGBOSS,
	[SceneType.MJ_BOSS] = BossViewIndex.MiJingBoss,
	[SceneType.SHENGYU_BOSS] = BossViewIndex.ShengYuBoss,
	[SceneType.KF_BOSS] = BossViewIndex.KFBoss,
    [SceneType.HONG_MENG_SHEN_YU] = TabIndex.worserv_boss_hmsy,
    [SceneType.Shenyuan_boss] = TabIndex.world_new_shenyuan_boss,
}

function BossWGData:__init()
	if BossWGData.Instance then
		error("[BossWGData] Attempt to create singleton twice!")
		return
	end
	BossWGData.Instance = self

	self.config_actor_bounds_cfg = require("config/config_actor_bounds")

	self.all_boss_info = {}
	self.notify_boss_info_change_callback_list = {}
	self.notify_boss_cave_info_change_callback_list = {}

	self.worldboss_hurt_list = {}
	self.worldboss_user_hurt_list = {}
	self.worldboss_throw_dicet_info = {}
    self.all_shenyuan_boss_info = {}
    self.person_hurt_info = {
        hurt = 0,
        rank_count = 0,
    }
	self.worldboss_auto = ConfigManager.Instance:GetAutoConfig("worldboss_auto")
    self.worldboss_buy_times_auto = self.worldboss_auto.world_boss_vip_buy_enter_times
	--self.feixian_boss_cfg = ConfigManager.Instance:GetAutoConfig("feixianboss_auto")
	self.kf_boss_cfg = ConfigManager.Instance:GetAutoConfig("cross_boss_auto")
	self.personal_cfg = ConfigManager.Instance:GetAutoConfig("personboss_auto")
	self.copy_personal_cfg = {}
	self.personal_scene_cfg = ListToMap(self.personal_cfg.boss_scene_cfg, "scene_id")
    self.mj_boss_cfg = ConfigManager.Instance:GetAutoConfig("secret_boss_auto")

    self.shenyuan_boss_cfg = ConfigManager.Instance:GetAutoConfig("shenyuan_boss_auto")

	-- 臻充boss
	self.cross_real_charge_boss_scenc = {}
	self.cross_real_charge_scenc_kill_info = {}
	self.erb_monster_info_list = {}
	self.erb_monster_id_info_list = {}   -- id = value
	self.erb_monster_scene_info_list = {} -- [scene_id][scene_index] = value
	self.erb_scene_special_boss_list = {}
	self.erb_special_boss_cfg_cache = {}
	self.erb_boss_id_cfg_cache = {}
	self.erb_record_list = {}

	local everyday_recharge_boss_cfg = ConfigManager.Instance:GetAutoConfig("real_charge_boss_auto")
	self.erb_layer_relation_cfg = ListToMap(everyday_recharge_boss_cfg.layer, "scene_group")
	self.erb_condition_cfg = ListToMap(everyday_recharge_boss_cfg.condition, "boss_group", "boss_group_index")
	self.erb_boss_list_cfg = everyday_recharge_boss_cfg.boss
	self.erb_every_layer_bosslist_cfg = ListToMap(everyday_recharge_boss_cfg.boss, "scene_index", "boss_index")

	--个人boss
	self.person_boss_day_kill_times = 0
	self.person_boss_day_buy_kill_times = 0
	self.person_boss_first_kill_flag = {}

	self.shen_yu_index = -1
	self.tired_vip_buy_times = 0
	self.vip_boss_task_list = {}


	self.dabaoboss_list = self.worldboss_auto.dabao_boss_boss_info
    self.worldboss_list = self.worldboss_auto.world_boss_boss_info
    self.vipBoss_xianli_receive = self.worldboss_auto.vipBoss_xianli_receive
    self.vip_boss_xianli_level = self.worldboss_auto.vipboss_tili
	self.vipboss_list = self.worldboss_auto.vipBoss_boss_info
	self.world_boss_enter_cfg = ListToMap(self.worldboss_auto.world_boss_enter_cfg, "layer")
	self.xianqi_get_way_cfg = self.worldboss_auto.xianqi_get_way
	
	self.worldboss_id_list = ListToMap(self.worldboss_list, "boss_id")
	self.dabaoboss_id_list = ListToMap(self.dabaoboss_list, "boss_id")
	self.vipboss_id_list = ListToMap(self.vipboss_list, "boss_id")
	self.personalboss_id_list = ListToMap(self.personal_cfg.boss_scene_cfg, "boss_id")

	self.vip_layer_boss_list = {}
	for k,v in pairs(self.vipboss_list) do
		if self.vip_layer_boss_list[v.level] == nil then
			self.vip_layer_boss_list[v.level] = {}
		end

		if self.vip_layer_boss_list[v.level][v.boss_id] == nil then
			self.vip_layer_boss_list[v.level][v.boss_id] = v.boss_jie
		end
	end

    self.all_boss_list = {}
    self.all_boss_sceneid_list = {}
	self.world_boss_list = {}
	self.dabao_boss_list = {}
	self.vip_boss_list = {}
	self.drop_list = {}
	self.mj_boss_list = {}
	self.other_cfg = self.worldboss_auto.other

	local cross_boss_auto = ConfigManager.Instance:GetAutoConfig("cross_boss_auto")
	self.crossboss_list = cross_boss_auto.boss_cfg
	local cross_boss_auto_1 = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list
	self.crossboss_monster_list = cross_boss_auto_1
	local shenyun_boss_auto = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto")
	self.shenyunboss_list = shenyun_boss_auto.Interface_show
	self.cross_other_cfg = cross_boss_auto.other[1]
	self.crystal_refresh_cfg = ListToMap(cross_boss_auto.crystal_refresh,"layer","seq")
	self.crossmonster_list = cross_boss_auto.monster_cfg
	self.crosscrytal_lsit = cross_boss_auto.layer_cfg
	self.crosscrytal_scene_id_list = ListToMap(self.crosscrytal_lsit,"scene_id")
	local monster_shield_auto = ConfigManager.Instance:GetAutoConfig("monster_shield_auto")
	self.shield_base_list = monster_shield_auto.base
	self.clear_delay_time = monster_shield_auto.other[1].clear_delay_time or 0
	self.monster_shield_other_cfg = monster_shield_auto.other[1]
	self.monster_shield_sec_kill_cfg = ListToMap(monster_shield_auto.sec_kill,"type")
	self.cross_boss_all_list = {}
	self.shenyun_boss_fb_list = {}
	self.cross_boss_info = {}
	self.cross_boss_list = {}
	self.new_cross_boss_list = {}
	self.leftmonsterandtreasure = {}
	self.personal_boss_list = {}
	self.own_boss_list = {}
	self.own_boss_all_list = {}

	self.sgyj_cfg = ConfigManager.Instance:GetAutoConfig("shanggu_yiji_auto")
	self.sg_boss_all_list = {}
	self.sg_boss_list = {}
	self.sg_angry_list = {}
	self.can_get_item_tab = {}
    self.hmsy_drop_list = {}
	self.sg_scene_info = {}
	for i = 1, GameEnum.SGYJ_LAYER_NUM do
		table.insert(self.sg_scene_info, {
			role_num = 0,
			boss_count = 0,
			box_count = {0,0},
			hidden_boss_id_list = {},
		})
	end

	self.totem_list = {}
	self.get_physical_flag = {}
	self.concern_flag = {}

	self.angry_value = 0
	self.cur_bossType = -1
	self.cur_bossLayer = -1
	self.cur_bossId = -1
	self.cur_boss_reason = SELECT_BOSS_REASON.VIEW
	self.dabao_enter_times = 0
	self.fuhuo_time = 0
	self.relive_times = 0
	self.tire_end_time = 0
	-- self.tire_can_relive_time = 0
	self.relive_times_cross = 0
	self.tire_end_time_cross = 0
	-- self.tire_can_relive_time_cross = 0
	self.personal_boss_enter_num = 0
	self.sg_tire_value = 0
	self.sg_enter_times = 0
	self.role_num_sg = 0
	self.item_index = 1
	self.client_sort = 1
	self.boss_tire = 0
	self.last_vip_boss_id = 0
	self.last_world_boss_id = 0
	self.last_kfboss_id = 0
	self.is_open_to_boss = true
    self.total_jingli = 1
    self.had_treasure_crystal_gather_times = 0
	self.sec_kill_uuid = {}
	self.sec_kill_start_time = 0
	self.sec_kill_end_time = 0
	self.sec_kill_type = 0
	self.sec_kill_monster_obj_id = 0
----------------------------

	 self.bosstujian_len = 0
	 self:FormatMenuCfg()

	 self.is_noreward_view = false -- 是否不在提示等级过高面板


	local dabao_boss_vip_enter_cfg = self.worldboss_auto.dabao_boss_vip_enter_times
	local tmp_value = 0
	for i,v in pairs(dabao_boss_vip_enter_cfg) do
		if tmp_value < v.vip_level then
			tmp_value = v.vip_level
		end
	end
	self.dabao_max_vip_up = tmp_value

	self.worldboss_times_info = {
		world_boss_day_kill_times = 0,
		world_boss_day_extra_kill_times = 0,
		world_boss_flush_can_kill_times = 0,
		world_boss_last_flush_times_time = 0,
	}

	self.vipboss_times_info = {
		boss_home_day_kill_times = 0,
		boss_home_day_item_add_kill_times = 0,
		boss_home_day_vip_buy_kill_times = 0,
    }
    self.role_realive_info = {
        realive_count = 0,
	    realive_time = 0,
	    die_time = 0,
    }

    self.select_owner_name = {}

	self.hmsy_cfg = ConfigManager.Instance:GetAutoConfig("hongmeng_boss_auto")
	self.hmsy_scene_list = {}
	self.hmsy_scene_info = {}
	self.hmsy_boss_info = {}
	self.hmsy_boss_list = {}
	self.boss_concern_flag_origin = 0
	self.boss_concern_flag_invade = 0
	self.boss_concern_flag_public = 0
	self.already_show_notice = false

	local rare_item_cfg = ConfigManager.Instance:GetAutoConfig("zhenguiitem_auto").itemlist
	self.rare_item_cfg = ListToMap(rare_item_cfg, "item_id")

	self.reset_vip_bosslist = BindTool.Bind(self.GetVipBossList, self, true)
	self.reset_per_bosslist = BindTool.Bind(self.GetPersonalBossList, self, true)
	self.reset_cross_bosslist = BindTool.Bind(self.GetCrossAllBoss, self, true)
	self.reset_sgyj_bosslist = BindTool.Bind(self.GetSGAllBoss, self, true)
	self.reset_hmsy_bosslist = BindTool.Bind(self.GetHMSYCommonBossList, self, true)

	self.act_drop_listener = {}
	self.act_change = BindTool.Bind(self.ListenActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

	self.attr_change = BindTool.Bind(self.RoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.attr_change, {"level", "vip_level"})

	RemindManager.Instance:Register(RemindName.Boss_Per, BindTool.Bind(self.GetPersonBossRemindNum, self))
	RemindManager.Instance:Register(RemindName.Boss_World, BindTool.Bind(self.GetWorldBossRed, self))
	RemindManager.Instance:Register(RemindName.Boss_Vip, BindTool.Bind(self.GetVipBossRed, self))
	RemindManager.Instance:Register(RemindName.Boss_Dabao, BindTool.Bind(self.GetDabaoBossRed, self))


	RemindManager.Instance:Register(RemindName.WorldServer_MHSS, BindTool.Bind(self.GetMHSSRed, self))
	RemindManager.Instance:Register(RemindName.WorldServer_HMSY, BindTool.Bind(self.GetHMSYRed, self))
	RemindManager.Instance:Register(RemindName.WorldServer_SGYJ, BindTool.Bind(self.GetSGYJRed, self))
    RemindManager.Instance:Register(RemindName.WorldServer_SYMW, BindTool.Bind(self.GetSYMWRed, self))
	RemindManager.Instance:Register(RemindName.WorldServer_EveryDay_Recharge_Boss, BindTool.Bind(self.GetEveryDayRechargeBossRed, self))

	self:RegisterSGYJRemindInBag(RemindName.WorldServer_SGYJ)
    self:RegisterDabaoBossRemindInBag(RemindName.Boss_Dabao)
    self:RegisterPerBossRemindInBag(RemindName.Boss_Per)

    self.boss_reward_thanks_giving_list = {}
	self.boss_tkmsg_open = true
end

function BossWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.Boss_Per)
    RemindManager.Instance:UnRegister(RemindName.Boss_World)
	RemindManager.Instance:UnRegister(RemindName.Boss_Vip)
	RemindManager.Instance:UnRegister(RemindName.Boss_Dabao)

	RemindManager.Instance:UnRegister(RemindName.WorldServer_MHSS)
	RemindManager.Instance:UnRegister(RemindName.WorldServer_HMSY)
	RemindManager.Instance:UnRegister(RemindName.WorldServer_SGYJ)
    RemindManager.Instance:UnRegister(RemindName.WorldServer_SYMW)

	ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
	RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
    self.shenyuan_boss_reward_tb = nil
	self.act_change = nil
	self.attr_change = nil
	self.notify_boss_info_change_callback_list = nil
	self.notify_boss_cave_info_change_callback_list = nil
	self.shen_yu_index = nil
	self.menu_list = nil
	self.old_level = nil
	self.cur_bossType = -1
	self.cur_bossLayer = -1
	self.cur_bossId = -1
	self.cur_boss_reason = SELECT_BOSS_REASON.VIEW
	self.cur_boss_x = nil
	self.cur_boss_y = nil
	self.cur_dabao_boss_layer = nil
	self:ClearCacheConveneInfo()
	BossWGData.Instance = nil

	if CountDownManager.Instance:HasCountDown(CountDownKey) then
		CountDownManager.Instance:RemoveCountDown(CountDownKey)
	end
end

function BossWGData:RegisterPerBossRemindInBag(remind_name)
    local cfg = BossWGData.Instance:GetPersonBossOtherCfg()
    if cfg and cfg.enter_item_id then
        local item_id_list = {cfg.enter_item_id}
        BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
    end
end

function BossWGData:RegisterSGYJRemindInBag(remind_name)
    local tiky_id = self:GetSGOtherCfg().boss_ticket
    local item_id_list = {tiky_id}
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function BossWGData:RegisterDabaoBossRemindInBag(remind_name)
    local tiky_id = self.other_cfg[1].dabao_tiky_item_id
    local item_id_list = {tiky_id}
    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function BossWGData:GetBossShieldData(shield_index)
	return self.shield_base_list[shield_index] or {}
end

function BossWGData:GetBossShieldDelayTime()
	return self.clear_delay_time
end

function BossWGData:GetBossAngry(boss_id)
	local scene_type = Scene.Instance:GetSceneType()
	local kill_add_angry = 0
	if SceneType.DABAO_BOSS == scene_type then
		for k,v in pairs(self.dabaoboss_list) do
			if v.boss_id == boss_id then
				kill_add_angry = v.kill_add_angry
			end
		end
	elseif SceneType.SG_BOSS == scene_type then
		local angry = self:GetSGBossAngryById(boss_id)
		kill_add_angry = angry and angry.kill_tire or 0
	end
	return kill_add_angry
end

function BossWGData:GetGatherBoxAngry(gather_id)
	local kill_value_angry = 0
	if self.other_cfg[1].shanggu_max_box_id == gather_id then
		kill_value_angry = self.other_cfg[1].shanggu_angry_max_box_add
	elseif self.other_cfg[1].shanggu_min_box_id == gather_id then
		kill_value_angry = self.other_cfg[1].shanggu_angry_min_box_add
	end
	return kill_value_angry
end

function BossWGData:GetIsGatherBoxAngry(gather_id)
	local is_angry = false
	if self.other_cfg[1].shanggu_max_box_id == gather_id then
		is_angry = true
	elseif self.other_cfg[1].shanggu_min_box_id == gather_id then
		is_angry = true
	end
	return is_angry
end

function BossWGData:IsWorldBossScene(scene_id)
	local worldboss_auto = ConfigManager.Instance:GetAutoConfig("worldboss_auto").world_boss_boss_info
	for _, v in pairs(worldboss_auto) do
		if scene_id == v.scene_id then
			return true, v
		end
	end

	return false
end

-- 根据boss_id获取boss状态   1.可击杀   0.未刷新
function BossWGData:GetBossStatusByBossId(boss_id)
	local next_refresh_time = (self.all_boss_info[boss_id] and self.all_boss_info[boss_id].next_refresh_time) or 999
	if next_refresh_time > 0 then
		return 0
	else
		return 1
	end

	return 0
end

-- 根据boss_id获取boss信息
function BossWGData:GetBossInfoById(boss_id)
	local cur_info
	for k,v in pairs(self.all_boss_list) do
		if boss_id == v.boss_id then
			cur_info = v
			break
		end
	end
	if nil == cur_info then return end

	local boss_info = {}
	boss_info.boss_name = cur_info.boss_name
	boss_info.boss_level = cur_info.boss_level
	boss_info.boss_id = cur_info.boss_id
	boss_info.scene_id = cur_info.scene_id
	boss_info.map_name = Config_scenelist[boss_info.scene_id].name
	boss_info.refresh_time = cur_info.refresh_time
	boss_info.recommended_power = cur_info.recommended_power
    if type(cur_info.drop_item_list) ~= "table" then
        boss_info.is_item_list = false
        boss_info.drop_item_list = Split(cur_info.drop_item_list, "|")
    else
        boss_info.is_item_list = true
		boss_info.drop_item_list = self:AddOpenDayItemList(cur_info)
    end
	if nil ~= self.all_boss_info[cur_info.boss_id] then
		boss_info.status = self.all_boss_info[cur_info.boss_id].status or 0
		boss_info.last_kill_name = self.all_boss_info[cur_info.boss_id].last_killer_name or ""
	end

	return boss_info
end

-- 注册boss回调
function BossWGData:NotifyBossChangeCallBack(callback)
	self.notify_boss_info_change_callback_list[#self.notify_boss_info_change_callback_list + 1] = callback
end

------------------------------------------------------------
-- WorldBoss,VipBoss,DabaoBoss
-------------------------------------------------------------
-- 所有boss服务端数据
function BossWGData:GetBossRefreshInfoByBossId(boss_id, scene_index)
	boss_id = boss_id + (scene_index or 0) * BossWGData.HMSY_INDEX
	return self.all_boss_info[boss_id]
end

-- 怪物掉落
function BossWGData:SetBossDropNewsInfo(protocol)
	-- local list = {}
	-- self.kf_sg_mj_drop_list = {}
	-- for k,v in pairs(protocol.history_list) do
	-- 	local cfg = ConfigManager.Instance:GetSceneConfig(v.scene_id)
	-- 	if cfg and (cfg.scene_type == SceneType.SG_BOSS or cfg.scene_type == SceneType.MJ_BOSS) then
	-- 		table.insert(self.kf_sg_mj_drop_list,v)
	-- 	else
	-- 		table.insert(list,v)
	-- 	end
	-- end
	self.drop_list = protocol.history_list
end

-- 怪物击杀信息
function BossWGData:SetBossKillRecordData(protocol)
	self.boss_kill_record_list = protocol.record_list
end

-- 怪物击杀信息
function BossWGData:GetBossKillRecordData(protocol)
	if self.boss_kill_record_list ~= nil or not IsEmptyTable(self.boss_kill_record_list) then
		local record_list = {}
		for k,v in pairs(self.boss_kill_record_list) do
			if v.killer_role_name ~= "" or v.kill_timestamp ~= 0 then
				record_list[k] = v
			end
		end
		return record_list
	end
	return {}
end

-- 获取怪物掉落信息
function BossWGData:GetNewsList()
	local list = self.drop_list or {}
	local t
	for i, v in pairs(self.drop_list) do
		t = self.rare_item_cfg[v.item_id]
		if t then
			v.is_top = t.is_top
		else
			v.is_top = 0
		end
	end
	table.sort(self.drop_list, SortTools.KeyUpperSorters("is_top", "pickup_timestamp", "index"))
	return self.drop_list
end

-- 获取怪物掉落信息
function BossWGData:GetXianjieDropList()
    local list = XianJieBossWGData.Instance:GetBossDropHistory()
    local temp_list = {}
	local t
    for i, v in pairs(list) do
        t = self.rare_item_cfg[v.item_id]
		if t then
			v.is_top = t.is_top
		else
			v.is_top = 0
        end
        table.insert(temp_list, v)
	end
	table.sort(temp_list, SortTools.KeyUpperSorters("is_top", "pickup_timestamp", "index"))
	return temp_list
end


-- boss死亡更新数据
function BossWGData:UpdateBossInfoWhenBossDie(protocol)
	local boss_info = self:GetBossRefreshInfoByBossId(protocol.boss_id)
	if boss_info == nil then return end
	boss_info.next_refresh_time = protocol.next_refresh_time
	local killer_vo = {}
	killer_vo.kill_boss_time = protocol.kill_boss_time
	killer_vo.last_killer_uid = protocol.killer_uid
	killer_vo.last_killer_name = protocol.killer_name
	table.insert(boss_info.kill_vo, killer_vo)
end

function BossWGData:SetCurSelectBossID(boss_type, boss_layer, boss_id, reason,pos_x,pos_y)
	self.cur_bossType = boss_type
	self.cur_bossLayer = boss_layer
	self.cur_bossId = boss_id
	self.cur_boss_reason = reason or SELECT_BOSS_REASON.VIEW
	self.cur_boss_x = pos_x
	self.cur_boss_y = pos_y
end

function BossWGData:GetCurSelectBossID()
	return self.cur_bossType, self.cur_bossLayer, self.cur_bossId, self.cur_boss_reason,self.cur_boss_x,self.cur_boss_y
end

function BossWGData:ClearCurSelectBossID()
	self.cur_bossType = -1
	self.cur_bossLayer = -1
	self.cur_bossId = -1
	self.cur_boss_reason = SELECT_BOSS_REASON.VIEW
	self.cur_boss_x = nil
	self.cur_boss_y = nil
end

function BossWGData:GetSetBossHelpPos(pos_x, pos_y, call_back)
	if pos_x and pos_y then
		self.boss_help_posx = pos_x
		self.boss_help_posy = pos_y
		self.boss_help_call_back = call_back
		return
	end
	return self.boss_help_posx, self.boss_help_posy, self.boss_help_call_back
end

-- 客户端缓存一下关注
function BossWGData:SetBossIsConcernByBossId(boss_id, is_concern)
	if self.all_boss_info[boss_id] == nil then
		return
	end
	self.all_boss_info[boss_id].is_concern = is_concern
end

-- 所有配置表数据根据boss_id拿数据
function BossWGData:GetBossInfoByBossId(boss_id)
	local all_boss_list = self:GetAllBossInfo()
	return all_boss_list[boss_id]
end

function BossWGData:GetBossAllInfoByBossId(boss_id)
	local cfg = self:GetBossInfoByBossId(boss_id)
	if cfg then return cfg end
	cfg = self:GetSGAllBossByBossId(boss_id)
	if cfg then return cfg end
	cfg = self:GetCrossBossInfoByBossId(boss_id)
	if cfg then return cfg end
    cfg = self:GetHMSYBossCfgByBossId(boss_id)
    if cfg then return cfg end
    cfg = XianJieBossWGData.Instance:GetBossCfgById(boss_id)
	if cfg then return cfg end
    cfg = TreasureBossWGData.Instance:GetTreasureDataByBossId(boss_id) --有毒，会返回{} 新加的记得放在前面
	if not IsEmptyTable(cfg) then return cfg end
end

-- 所有boss配置表数据
function BossWGData:GetAllBossInfo()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if next(self.all_boss_list) == nil or role_level ~= self.old_level or self.need_act_change then
		self.old_level = role_level
        self.need_act_change = false
        local world_boss_list = self:GetWorldBossListHasLayer()
        for k, v in ipairs(world_boss_list) do
            for k1, v1 in pairs(v) do
                self.all_boss_list[v1.boss_id] = v1
                self.all_boss_list[v1.boss_id].boss_type = 0
                self.all_boss_list[v1.boss_id].layer = v1.layer
                self.all_boss_list[v1.boss_id].index = k
                self.all_boss_list[v1.boss_id].boss_view_index = k1
            end
        end

        local dabao_boss_list = self:GetDabaoBossList()
		for k, v in ipairs(dabao_boss_list) do
			for k1, v1 in ipairs(v) do
				self.all_boss_list[v1.boss_id] = v1
				self.all_boss_list[v1.boss_id].boss_type = 1
				self.all_boss_list[v1.boss_id].layer = v1.level
				self.all_boss_list[v1.boss_id].index = k
				self.all_boss_list[v1.boss_id].boss_view_index = k1
			end
        end

        local vip_boss_list = self:GetVipBossList()
		for k, v in pairs(vip_boss_list) do
			for k1, v1 in pairs(v) do
				self.all_boss_list[v1.boss_id] = v1
				self.all_boss_list[v1.boss_id].boss_type = 2
				self.all_boss_list[v1.boss_id].layer = v1.level
				self.all_boss_list[v1.boss_id].index = k
				self.all_boss_list[v1.boss_id].boss_view_index = k1
			end
        end

        local person_boss_list = self:GetPersonalBossList()
		for k, v in ipairs(person_boss_list) do
			self.all_boss_list[v.boss_id] = v
			self.all_boss_list[v.boss_id].boss_type = 12
			self.all_boss_list[v.boss_id].layer = v.layer
			self.all_boss_list[v.boss_id].index = k
			self.all_boss_list[v.boss_id].boss_view_index = k
        end

        local cross_boss_list = self:GetCrossBossList()
		for k,v in ipairs(cross_boss_list) do
			self.all_boss_list[v.boss_id] = v
			self.all_boss_list[v.boss_id].boss_type = 6
			self.all_boss_list[v.boss_id].layer = v.layer
			self.all_boss_list[v.boss_id].index = v.boss_index
			self.all_boss_list[v.boss_id].boss_view_index = k
        end

        local shenyuan_boss_list = self:GetShenyuanBossData()
        for k,v in ipairs(shenyuan_boss_list) do
			self.all_boss_list[v.boss_id] = v
			self.all_boss_list[v.boss_id].boss_type = 8
			self.all_boss_list[v.boss_id].layer = 1
			self.all_boss_list[v.boss_id].index = k
			self.all_boss_list[v.boss_id].boss_view_index = k
		end

		local erb_boss_list = self:GetERBBossData()
		for k,v in ipairs(erb_boss_list) do
			self.all_boss_list[v.boss_id] = v
			self.all_boss_list[v.boss_id].boss_type = 9
			self.all_boss_list[v.boss_id].layer = 1
			self.all_boss_list[v.boss_id].index = v.boss_index
			self.all_boss_list[v.boss_id].boss_view_index = k
		end
	end
	return self.all_boss_list
end

-- 所有boss配置表scene_id
function BossWGData:GetBossTypeBySceneId(scene_id)
	if next(self.all_boss_sceneid_list) == nil then
        for k, v in pairs(self.worldboss_list) do
            if not self.all_boss_sceneid_list[v.scene_id] then
                self.all_boss_sceneid_list[v.scene_id] = {}
                self.all_boss_sceneid_list[v.scene_id].view_tab_index = TabIndex.boss_world
                self.all_boss_sceneid_list[v.scene_id].boss_view_layer = v.layer
                self.all_boss_sceneid_list[v.scene_id].view_name = GuideModuleName.Boss
            end
        end
		for k, v in pairs(self.dabaoboss_list) do
            if not self.all_boss_sceneid_list[v.scene_id] then
                self.all_boss_sceneid_list[v.scene_id] = {}
                self.all_boss_sceneid_list[v.scene_id].view_tab_index = TabIndex.boss_dabao
                self.all_boss_sceneid_list[v.scene_id].boss_view_layer = v.level
                self.all_boss_sceneid_list[v.scene_id].view_name = GuideModuleName.Boss
            end
        end
		for k, v in pairs(self.worldboss_auto.vipBoss_boss_info) do
			if not self.all_boss_sceneid_list[v.scene_id] then
                self.all_boss_sceneid_list[v.scene_id] = {}
                self.all_boss_sceneid_list[v.scene_id].view_tab_index = TabIndex.boss_vip
                self.all_boss_sceneid_list[v.scene_id].boss_view_layer = v.level + 1
                self.all_boss_sceneid_list[v.scene_id].view_name = GuideModuleName.Boss
            end
        end

		for k, v in pairs(self.personal_cfg.boss_scene_cfg) do
			if not self.all_boss_sceneid_list[v.scene_id] then
                self.all_boss_sceneid_list[v.scene_id] = {}
                self.all_boss_sceneid_list[v.scene_id].view_tab_index = TabIndex.boss_personal
                self.all_boss_sceneid_list[v.scene_id].boss_view_layer = 1
                self.all_boss_sceneid_list[v.scene_id].view_name = GuideModuleName.Boss
            end
        end

		for k,v in pairs(self.crossboss_list) do
			if not self.all_boss_sceneid_list[v.scene_id] then
                self.all_boss_sceneid_list[v.scene_id] = {}
                self.all_boss_sceneid_list[v.scene_id].view_tab_index = TabIndex.worserv_boss_mh
                self.all_boss_sceneid_list[v.scene_id].boss_view_layer = v.layer
                self.all_boss_sceneid_list[v.scene_id].view_name = GuideModuleName.WorldServer
            end
        end

        for k,v in pairs(self.shenyuan_boss_cfg.boss_cfg) do
			if not self.all_boss_sceneid_list[v.scene_id] then
                self.all_boss_sceneid_list[v.scene_id] = {}
                self.all_boss_sceneid_list[v.scene_id].view_tab_index = TabIndex.world_new_shenyuan_boss
                self.all_boss_sceneid_list[v.scene_id].boss_view_layer = 1
                self.all_boss_sceneid_list[v.scene_id].view_name = GuideModuleName.WorldServer
            end
        end
        local xianjie_layer_cfg = XianJieBossWGData.Instance:GetBossLayerCfg()
        for k,v in pairs(xianjie_layer_cfg) do
			if not self.all_boss_sceneid_list[v.scene_id] then
                self.all_boss_sceneid_list[v.scene_id] = {}
                self.all_boss_sceneid_list[v.scene_id].view_tab_index = TabIndex.xianjie_boss
                self.all_boss_sceneid_list[v.scene_id].boss_view_layer = v.layer
                self.all_boss_sceneid_list[v.scene_id].view_name = GuideModuleName.WorldServer
            end
        end

		for k, v in pairs(self.erb_boss_list_cfg) do
			if not self.all_boss_sceneid_list[v.scene_id] then
				self.all_boss_sceneid_list[v.scene_id] = {}
                self.all_boss_sceneid_list[v.scene_id].view_tab_index = TabIndex.worserv_everyday_recharge_boss
                self.all_boss_sceneid_list[v.scene_id].boss_view_layer = 1
                self.all_boss_sceneid_list[v.scene_id].view_name = GuideModuleName.WorldServer
			end
		end
	end
	return self.all_boss_sceneid_list[scene_id]
end

function BossWGData:GetOtherCfg()
	return self.other_cfg[1]
end

function BossWGData:GetOtherCfgByKey(key)
	return self.other_cfg[1][key]
end

function BossWGData:GetIsExperience(scene_id)
    local cfg = self:GetWorldBossEnterCfg()
    if not IsEmptyTable(cfg) then
        for k,v in ipairs(cfg) do
            if v.scene_id == scene_id then
                if v.is_experience == 1 then
                    return true
                else
                    return false
                end
            end
        end
    else
        print_error("WorldBossEnterCfg is nil")
    end
    return true
end

function BossWGData:GetBossListBySceneId(scene_id)
	if not self.scene_boss_list then
		self.scene_boss_list = {}
	end
	if not self.scene_boss_list[scene_id] then
		local all_boss_list = self:GetAllBossInfo()
		local boss_list = {}
		for k, v in pairs(all_boss_list) do
			if v.scene_id == scene_id then
				table.insert(boss_list, v)
			end
		end
		table.sort(boss_list, function (a, b)
			return a.boss_id < b.boss_id
		end)

		self.scene_boss_list[scene_id] = boss_list
	end

	return self.scene_boss_list[scene_id]
end

function BossWGData:GetEliteListBySceneType(scene_type)
	local elite_list = {}
	local list_flag = {}
	local cfg
	if scene_type == SceneType.SG_BOSS then
		--cfg = self.worldboss_auto.shanggu_boss_info
	else
		local scene_id =  Scene.Instance:GetSceneId()
		cfg = self:GetEliteListBySceneId(scene_id)
	end
	local cur_bossLayer = 0
	if Scene.Instance:GetSceneId() == 2201 then
		cur_bossLayer = 1
	end
	if cfg then
		for k,v in pairs(cfg) do
			if scene_type == SceneType.SG_BOSS then
				if v.layer == cur_bossLayer and v.type == 1 and list_flag[v.boss_id] == nil then
					list_flag[v.boss_id] = 1
					table.insert(elite_list, v)
				end
			else
				elite_list = cfg
			end
		end
	end
	return elite_list
end

function BossWGData:GetEliteListBySceneId(scene_id)
	local cfg_list = {}
	local cfg = self.worldboss_auto.elite_monster
	for k,v in ipairs(cfg) do
		if scene_id == v.scene_id then
			table.insert(cfg_list, v)
		end
    end
	return cfg_list
end

function BossWGData:GetWorldEliteListBySceneId(scene_id)
	local cfg_list = {}
    local cfg = self:GetWorldBossMonsterList()
    for k,v in pairs(cfg) do
        if scene_id == v.scene_id then
            table.insert(cfg_list, v)
        end
    end
	return cfg_list
end


function BossWGData:IsInBossScene()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.DABAO_BOSS or scene_type == SceneType.WorldBoss or scene_type == SceneType.VIP_BOSS then
		return true
	else
		return false
	end
end

function BossWGData:IsInBossSceneByBossId(boss_id)
	local scene_id = Scene.Instance:GetSceneId()
	local boss_info = self:GetBossInfoByBossId(boss_id)
	if boss_info == nil then
		boss_info = self:GetSGAllBossByBossId(boss_id)
	end
	if boss_info and boss_info.scene_id == scene_id then
		return true
	end
	return false
end

function BossWGData:GetMonsterInfo(boss_id)
	if not boss_id then return end
	
	local info = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
	if not info then
		print_error("monster_auto表 monster_list 取不到怪物配置 monster_id =",boss_id)
	end
	return info
end
------------  世界BOSS  --------------
-- 世界boss进入次数信息
function BossWGData:SetWorldBossEnterTimeInfo(info)
	self.worldboss_times_info.world_boss_day_kill_times = info.world_boss_day_kill_times
	self.worldboss_times_info.world_boss_day_extra_kill_times = info.world_boss_day_extra_kill_times
	self.worldboss_times_info.world_boss_flush_can_kill_times = info.world_boss_flush_can_kill_times
    self.worldboss_times_info.world_boss_last_flush_times_time = info.world_boss_last_flush_times_time
	self.worldboss_times_info.world_boss_tired_day_add_count = info.world_boss_tired_day_add_count
end
-- 世界boss进入次数信息
function BossWGData:GetWorldBossEnterTimeInfo()
	return self.worldboss_times_info
end

-- 每一层，index为当前层数
function BossWGData:GetWorldBossListByLayer(layer)
	local boss_list = self:GetWorldBossListHasLayer()
	return boss_list[layer]
end

-- 获取世界boss列表旧的
function BossWGData:GetWorldBossList(need_reset)
    if IsEmptyTable(self.world_boss_list) or need_reset then
        self.world_boss_list = {}
        local boss_data = {}
        local index = 1
        local act_callback = BindTool.Bind(self.GetWorldBossList, self, true)
        local is_item_list = type(self.worldboss_list[1].drop_item_list) == "table"
        for i = 1, #self.worldboss_list do
            boss_data = self:GetMonsterInfo(self.worldboss_list[i].boss_id)
            local need_role_level = BossWGData.GetEnterWorldCfgbySceneId(self.worldboss_list[i].scene_id)
            -- if need_role_level and need_role_level > RoleWGData.Instance.role_vo.level and boss_data then
            -- 	limit_count = limit_count + 1
            -- end
            if boss_data and self.worldboss_list[i].monster_type == BossWGData.MonsterType.Boss then
                self.world_boss_list[index] = {}
                self:ResetIndexBossCfg(self.world_boss_list[index], self.worldboss_list[i])

                self.world_boss_list[index].is_item_list = is_item_list
                self.world_boss_list[index].status = self:GetBossStatusByBossId(self.worldboss_list[i].boss_id)
                if is_item_list then
                    self.world_boss_list[index].drop_item_list = self:AddOpenDayItemList(self.worldboss_list[i], act_callback, TabIndex.boss_world)
                else
                    self.world_boss_list[index].drop_item_list = Split(self.world_boss_list[index].drop_item_list, "|")
                end
                self.world_boss_list[index].need_role_level = need_role_level
                self.world_boss_list[index].show_duobei = true
                self.world_boss_list[index].task_type = RATSDUOBEI_TASK.SHIJIEMOWANG
                self.world_boss_list[index].boss_level = boss_data.level
                self.world_boss_list[index].boss_name = boss_data.name
                self.world_boss_list[index].big_icon = boss_data.small_icon
                self.world_boss_list[index].view_resouce = self.worldboss_list[i].boss_id
                index = index + 1
            end
        end
    end
	return self.world_boss_list
end

-- 获取世界boss列表
function BossWGData:GetWorldBossListHasLayer(need_reset)
    if IsEmptyTable(self.world_boss_list_has_layer) or need_reset then
        self.world_boss_list_has_layer = {}
        local boss_data = {}
        local limit_count = 0
        local act_callback = BindTool.Bind(self.GetWorldBossListHasLayer, self, true)
        local is_item_list = type(self.worldboss_list[1].drop_item_list) == "table"
        for i = 1, #self.worldboss_list do
            if nil ~= self.worldboss_list[i] then
                local j = self.worldboss_list[i].layer
                boss_data = self:GetMonsterInfo(self.worldboss_list[i].boss_id)
                local need_role_level = BossWGData.GetEnterWorldCfgbySceneId(self.worldboss_list[i].scene_id)
                -- if need_role_level and need_role_level > RoleWGData.Instance.role_vo.level and boss_data then
                --     limit_count = limit_count + 1
                -- end
                if boss_data and self.worldboss_list[i].monster_type == BossWGData.MonsterType.Boss then --and limit_count <= 4
                    local boss_vo = {}
                    self:ResetIndexBossCfg(boss_vo, self.worldboss_list[i])
                    boss_vo.is_item_list = is_item_list
                    boss_vo.status = self:GetBossStatusByBossId(self.worldboss_list[i].boss_id)
                    if is_item_list then
                        boss_vo.drop_item_list = self:AddOpenDayItemList(self.worldboss_list[i], act_callback, TabIndex.boss_world)
                    else
                        boss_vo.drop_item_list = Split(boss_vo.drop_item_list, "|")
                    end
                    boss_vo.need_role_level = need_role_level
                    boss_vo.show_duobei = true
                    boss_vo.task_type = RATSDUOBEI_TASK.SHIJIEMOWANG
                    boss_vo.boss_level = boss_data.level
                    boss_vo.boss_name = boss_data.name
					boss_vo.boss_jieshu = boss_data.boss_jieshu
                    boss_vo.world_firstkill_times = self.worldboss_list[i].world_firstkill_times
                    boss_vo.person_firstkill_reward = self.worldboss_list[i].person_firstkill_reward
                    boss_vo.big_icon = boss_data.small_icon
                    boss_vo.view_resouce = self.worldboss_list[i].boss_id
                    boss_vo.is_drop_jewelry = self.worldboss_list[i].is_drop_jewelry
                    if self.world_boss_list_has_layer[j] == nil then
                        self.world_boss_list_has_layer[j] = {}
                    end
                    table.insert(self.world_boss_list_has_layer[j], boss_vo)
                end
            end
        end
    end
	return self.world_boss_list_has_layer
end

-- 获取世界boss小怪列表
function BossWGData:GetWorldBossMonsterList()
    if IsEmptyTable(self.world_boss_monster_list) then
        self.world_boss_monster_list = {}
        local monster_data = {}
        for i = 1, #self.worldboss_list do
            if nil ~= self.worldboss_list[i] then
                monster_data = self:GetMonsterInfo(self.worldboss_list[i].boss_id)
                if monster_data and self.worldboss_list[i].monster_type == BossWGData.MonsterType.Monster then --and limit_count <= 4
                    local monster_vo = {}
                    monster_vo.scene_id = self.worldboss_list[i].scene_id
                    monster_vo.layer = self.worldboss_list[i].layer
                    monster_vo.boss_id = self.worldboss_list[i].boss_id
                    monster_vo.x_pos = self.worldboss_list[i].x_pos
                    monster_vo.y_pos = self.worldboss_list[i].y_pos
                    table.insert(self.world_boss_monster_list, monster_vo)
                end
            end
        end
    end
	return self.world_boss_monster_list
end

function BossWGData:GetWorldBossCfgByBossId(boss_id)
	for i, v in pairs(self.worldboss_auto.world_boss_boss_info) do
		if v.boss_id == boss_id then
			return v
		end
	end
end

function BossWGData:GetWorldBossCfgsBySceneId(scene_id, monster_type)
	local cfg = {}
	for i, v in pairs(self.worldboss_auto.world_boss_boss_info) do
		if v.scene_id == scene_id and (monster_type == nil or v.monster_type == monster_type) then
			table.insert(cfg, v)
		end
	end
	return cfg
end

function BossWGData:GetWorldBossPos(boss_id)
	local boss_info = {}
	for i=1,#self.worldboss_list do
		if boss_id == self.worldboss_list[i].boss_id then
			boss_info.x_pos = self.worldboss_list[i].x_pos
			boss_info.y_pos = self.worldboss_list[i].y_pos
			return boss_info
		end
	end
	return nil
end

--遍历所有世界boss配置
function BossWGData:GetWorldBossCfgInAllBoss(boss_id)
	for i=1,#self.worldboss_list do
		if boss_id == self.worldboss_list[i].boss_id then
			return self.worldboss_list[i]
		end
	end
end

function BossWGData:SetWorldBossRoleRealiveInfo(protocol)
	self.role_realive_info = protocol
end

--是否处于世界boss5层复活疲劳状态
function BossWGData:IsWorldBossTired()
	return self.role_realive_info.realive_count == 5
end

--是否处于世界boss复活疲劳状态
function BossWGData:IsInWorldBossTiredState()
	return self.role_realive_info.realive_count > 0
end

function BossWGData:GetWorldBossRoleRealiveInfo()
	return self.role_realive_info
end

--获取复活疲劳后的复活疲劳持续时间
function BossWGData:GetWorldBossRoleRealiveTime()
	return self.other_cfg[1].world_boss_realive_count_time
end

--获取复活疲劳后的复活倒计时时间
function BossWGData:GetWorldBossRoleFuhuoTime()
	return self.other_cfg[1].world_boss_realive_time
end

-- 世界Boss服务端数据
function BossWGData:SetWorldBossInfo(protocol)
	local boss_list = protocol.boss_list
	self.tire_value = protocol.tire_value
	for k, v in pairs(boss_list) do
		self.all_boss_info[v.boss_id] = v
	end

	for k, v in pairs(self.notify_boss_info_change_callback_list) do
		v()
	end
end

function BossWGData:SetWorldBossTiredByDayCount(tire_value)
	self.tire_value = tire_value
end

--世界boss当前次数
function BossWGData:GetWorldBossTimes()
    local world_times_info = self:GetWorldBossEnterTimeInfo()
    local cur_times, max_times = 0, 1
    local wb_other_cfg = self.other_cfg[1]
    max_times = wb_other_cfg.world_boss_day_default_times + world_times_info.world_boss_day_extra_kill_times
    cur_times = max_times - world_times_info.world_boss_day_kill_times + world_times_info.world_boss_flush_can_kill_times
    return cur_times, max_times
end
-- 世界boss疲劳值
function BossWGData:GetWorldBossTire()
	local max_tire_value = self.other_cfg[1].day_max_tire
	return self.tire_value or 0, max_tire_value
end

function BossWGData:SetBossReliveTire(protocol)
	self.relive_times = protocol.relive_times
	self.tire_end_time = protocol.tire_end_time
	-- self.tire_can_relive_time = protocol.tire_can_relive_time
end

function BossWGData:SetCrossReliveTire(protocol)
	self.relive_times_cross = protocol.relive_times
	self.tire_end_time_cross = protocol.tire_end_time
	-- self.tire_can_relive_time_cross = protocol.tire_can_relive_time
end

function BossWGData:GetBossReliveTire()
	if Scene.Instance:GetSceneType() == SceneType.KF_BOSS then
		return self.relive_times_cross, self.tire_end_time_cross, self.tire_can_relive_time_cross
	else
		return self.relive_times, self.tire_end_time, self.tire_can_relive_time
	end
end

-- 世界boss秘境boss进入条件
function BossWGData.GetEnterWorldCfgbySceneId(scene_id)
	local world_enter_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").world_boss_enter_cfg
	for i,v in ipairs(world_enter_cfg) do
		if scene_id == v.scene_id then
			return v.need_role_level
		end
	end
	local mj_boss_enter_cfg = ConfigManager.Instance:GetAutoConfig("secret_boss_auto").enter_cfg
	for i,v in ipairs(mj_boss_enter_cfg) do
		if scene_id == v.scene_id then
			return v.need_role_level
		end
	end
	-- mj_kf_boss_enter_cfg = ConfigManager.Instance:GetAutoConfig("cross_secret_boss_auto").enter_cfg
	-- for i,v in ipairs(mj_kf_boss_enter_cfg) do
	-- 	if scene_id == v.scene_id then
	-- 		return v.need_role_level
	-- 	end
	-- end
end
------------   打宝BOSS(BOSS洞窟)   ------------
-- 打宝Boss列表按层存放
function BossWGData:GetDabaoBossList(need_reset)
	if next(self.dabao_boss_list) == nil or need_reset then
		self.dabao_boss_list = {}
		-- for i = 1, 2 do
		-- 	self.dabao_boss_list[i] = {}
		-- end
		local boss_data = {}
		local act_callback = BindTool.Bind(self.GetDabaoBossList, self, true)
		for i = 1, #self.dabaoboss_list do
			if nil ~= self.dabaoboss_list[i] then
				local j = self.dabaoboss_list[i].level

				local boss_vo = {}
				self:ResetIndexBossCfg(boss_vo, self.dabaoboss_list[i])
                if type(boss_vo.drop_item_list) ~= "table" then
                    boss_vo.is_item_list = false
                    boss_vo.drop_item_list = Split(self.dabaoboss_list[i].drop_item_list, "|")
                else
                    boss_vo.is_item_list = true
					boss_vo.drop_item_list = self:AddOpenDayItemList(self.dabaoboss_list[i], act_callback, TabIndex.boss_dabao)
                end
				boss_data = self:GetMonsterInfo(self.dabaoboss_list[i].boss_id)
				if boss_data ~= nil then
					boss_vo.boss_level = boss_data.level
					boss_vo.boss_name = boss_data.name
					boss_vo.view_resouce = boss_vo.boss_id
					boss_vo.big_icon = boss_data.small_icon
					boss_vo.boss_jieshu = boss_data.boss_jieshu
					if self.dabao_boss_list[j] == nil then
						self.dabao_boss_list[j] = {}
					end
					table.insert(self.dabao_boss_list[j], boss_vo)
				end
			end
		end
	end
	return self.dabao_boss_list
end


-- 每一层Boss，index为当前层数
function BossWGData:GetDabaoBossListByIndex(index)
	local boss_list = self:GetDabaoBossList()
	return boss_list[index]
end

function BossWGData:GetDaBaoLevelLayerCfg()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local layer_cfg = self.worldboss_auto.dabao_boss_enter_cfg
    local index = 1
    for i = #layer_cfg, 1, -1  do
		local enter_flag = CultivationWGData.Instance:GetDaboBossIsCanEneter(layer_cfg[i].min_order_num, layer_cfg[i].min_quality_num)
		if role_level >= layer_cfg[i].tuijian_role_level and enter_flag then
			index = i
			break
		end
    end
	
	return index
end

function BossWGData:GetCurrentLocalServerShowIndex()
    return self.cur_local_show_index
end

function BossWGData:SetCurrentLocalServerShowIndex(index)
    self.cur_local_show_index = index
end

function BossWGData:GetCurrentShijieServerShowIndex()
    return self.cur_Shijie_show_index
end

function BossWGData:SetCurrentShijieServerShowIndex(index)
    self.cur_Shijie_show_index = index
end

function BossWGData:GetWorldBossEnterCfg()
   return self.worldboss_auto.world_boss_enter_cfg or {}
end

function BossWGData:GetWorldBossEnterCfgByLayer(layer)
	return self.world_boss_enter_cfg[layer] or {}
end

function BossWGData:GetXianqiGetWayCfg()
	return self.xianqi_get_way_cfg
end

function BossWGData:GetWorldBossSceneIdByIndex(index)
    local enter_cfg = self:GetWorldBossEnterCfg()
    for k, v in pairs(enter_cfg) do
        if index == v.layer then
            return v.scene_id
        end
    end
    return 0
end


function BossWGData:GetWorldBossDefLayer()
    local level = RoleWGData.Instance:GetRoleLevel()
    local cfg = self:GetWorldBossEnterCfg()
    local layer = 1
    for k,v in pairs(cfg) do
    	if v.role_lv_min <= level and v.role_lv_max >= level then
    		layer = v.layer
    		break
    	end
    end

    return layer
end
-- 服务端数据
function BossWGData:SetDabaoBossAllInfo(protocol)
	self.dabao_enter_times = protocol.enter_times
	local layer_count = protocol.layer_count
	local all_boss_list = protocol.all_boss_list
	for i = 1, layer_count do
		for k, v in pairs(all_boss_list[i].boss_list) do
			if not self.all_boss_info[v.boss_id] then
				self.all_boss_info[v.boss_id] = {}
			end
			self.all_boss_info[v.boss_id].is_concern = v.is_concern
			self.all_boss_info[v.boss_id].killer_count = v.killer_count
			self.all_boss_info[v.boss_id].kill_vo = v.kill_vo
			self.all_boss_info[v.boss_id].next_refresh_time = v.next_refresh_time
			self.all_boss_info[v.boss_id].is_near_death = v.is_near_death
			self.all_boss_info[v.boss_id].last_kill_name = v. last_kill_name
			self.all_boss_info[v.boss_id].belong_name = v.belong_name
		end
	end
	self.dabao_all_list = protocol.all_boss_list
	self.all_cave_boss_info = protocol.all_boss_list
	-- self:FlushCaveBossAllInfo()
end

function BossWGData:GetAlRoleEnterInfo(index)
	if not self.dabao_all_list then return 0 end
	return self.dabao_all_list[index] and self.dabao_all_list[index].role_num or 0
end

-- 单层数据
function BossWGData:SetDabaoBossLayer(protocol)
	if not self.dabao_all_list then
		self.dabao_all_list = {}
	end
	if self.dabao_all_list[protocol.cur_layer] then
		self.dabao_all_list[protocol.cur_layer].role_num = protocol.cur_role_num
	end
	local boss_list = protocol.boss_list
	for k, v in pairs(boss_list) do
		if not self.all_boss_info[v.boss_id] then
			self.all_boss_info[v.boss_id] = {}
		end
		self.all_boss_info[v.boss_id].is_concern = v.is_concern
		self.all_boss_info[v.boss_id].killer_count = v.killer_count
		self.all_boss_info[v.boss_id].kill_vo = v.kill_vo
		self.all_boss_info[v.boss_id].next_refresh_time = v.next_refresh_time
		self.all_boss_info[v.boss_id].is_near_death = v.is_near_death
		self.all_boss_info[v.boss_id].last_kill_name = v.last_kill_name
		self.all_boss_info[v.boss_id].belong_name = v.belong_name
		
	end

	self.cur_dabao_boss_layer = protocol.cur_layer
end

-- 打宝boss进入消耗
function BossWGData:GetDaBaoBossEnterComsun()
	local dabao_comsun_cfg = self.worldboss_auto.dabao_boss_consume_cfg
	for k, v in pairs(dabao_comsun_cfg) do
		if v.enter_times == self.dabao_enter_times + 1 then
			return v.need_consume_item_num
		end
	end
	return 0
end

function BossWGData:GetDaBaoBossCurLayer()
	return self.cur_dabao_boss_layer
end

-- 打宝boss剩余进入次数
function BossWGData:GetDaBaoBossRemainEnterTimes()
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local dabao_boss_vip_enter_cfg = self.worldboss_auto.dabao_boss_vip_enter_times
	local dabao_day_default_times = self.worldboss_auto.other[1].dabao_day_default_times
	local vip_info = {}
	for k, v in ipairs(dabao_boss_vip_enter_cfg) do
		if role_vip_level < v.vip_level then
			break
		else
			vip_info = v
		end
	end

	return self.dabao_enter_times, dabao_day_default_times + (vip_info.extra_enter_times or 0)
end

function BossWGData:GetIsShowUpVip()
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	return role_vip_level < self.dabao_max_vip_up
end

function BossWGData:GetIsShowDabaoVipTip()
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local dabao_boss_vip_enter_cfg = self.worldboss_auto.dabao_boss_vip_enter_times
	local dabao_day_default_times = self.worldboss_auto.other[1].dabao_day_default_times
	local enter_times, next_vip_level
	for k, v in ipairs(dabao_boss_vip_enter_cfg) do
		if v.vip_level > role_vip_level then
			next_vip_level = v.vip_level
			enter_times = v.extra_enter_times
			break
		end
	end

	if not enter_times then
		return nil, nil
	end

	return dabao_day_default_times + enter_times, next_vip_level
end

function BossWGData:GetIsShowUpVipCommon()
	local max_level = VipWGData.Instance:GetMaxVIPLevel()
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	return role_vip_level < max_level
end

-- 打宝boss怒气值
function BossWGData:GetDabaoBossMaxAngry(boss_id)
	local boss = self:GetBossInfoByBossId(boss_id)
	if boss == nil then
		boss = self:GetSGBossAngryById(boss_id)
	end
	if boss == nil then return 0 end
	return boss.kill_add_angry
end

function BossWGData:SetDabaoBossAngryValue(protocol)
	self.angry_value = protocol.angry_value
	self.kick_out_time = protocol.kick_out_time
end

function BossWGData:GetDabaoBossAngryValue()
	return self.angry_value, self.kick_out_time
end

-- 打宝boss开启等级
function BossWGData:GetDabaoBossIsOpen()
	local role_level = RoleWGData.Instance.role_vo.level
	return role_level >= self.other_cfg[1].open_param
end

function BossWGData:GetDabaoBossTikyId()
	return self.worldboss_auto.other[1].dabao_tiky_seq
end

function BossWGData:GetDaboBossIsEneter(layer)
	layer = layer or 1
	local role_level = RoleWGData.Instance.role_vo.level
	local limit_info = self.worldboss_auto.dabao_boss_enter_cfg
	if not limit_info[layer] then return true, nil end
	return role_level >= limit_info[layer].need_role_level, limit_info[layer].need_role_level, limit_info[layer].min_order_num, limit_info[layer].min_quality_num
end

function BossWGData:GetDaboBossNextIsEneter(layer)
	layer = layer or 1
	local limit_info = self.worldboss_auto.dabao_boss_enter_cfg
	if not limit_info[layer] then return true, nil end
	return limit_info[layer].next_min_order_num, limit_info[layer].next_min_quality_num 
end

function BossWGData:GetDaboBossShowIsEneter(layer)
	layer = layer or 1
	local limit_info = self.worldboss_auto.dabao_boss_enter_cfg
	if not limit_info[layer] then return true, nil end
	return limit_info[layer].show_order_num, limit_info[layer].show_quality_num
end

function BossWGData:GetDabaoIndexCfgByLayerAndBossId(layer, boss_id)
    if self.worldboss_auto.dabao_boss_enter_cfg[layer] then
        local role_level = RoleWGData.Instance:GetRoleLevel()
        local need_level = self.worldboss_auto.dabao_boss_enter_cfg[layer].need_role_level
		if role_level < need_level then
			return false, need_level
		end

		local boss_list = self:GetDabaoBossListByIndex(layer)
		if not boss_list then
			return false, nil
		end

		local boss_index = 0
		for i, v in pairs(boss_list) do
			boss_index = boss_index + 1
			if v.boss_id == boss_id then
				break
			end
		end

		return true, boss_index
    end
    return false, nil
end

----------------------------------vip-----------------------------------
--boss之家次数信息
function BossWGData:SetBossHomeEnterTimeInfo(info)
	self.vipboss_times_info.boss_home_day_kill_times = info.boss_home_day_kill_times
	self.vipboss_times_info.boss_home_day_item_add_kill_times = info.boss_home_day_item_add_kill_times
	self.vipboss_times_info.boss_home_day_vip_buy_kill_times = info.boss_home_day_vip_buy_kill_times
end

function BossWGData:GetBossHomeEnterTimeInfo()
	return self.vipboss_times_info
end

--boss之家购买花费
function BossWGData:GetVIPBossBuyTimesExtra(times)
	for k,v in pairs(self.worldboss_auto.vipBoss_boss_buy_info) do
		if v.vip_buy_enter_times == times then
			return v.need_gold
		end
	end
	return 0
end

--function BossWGData:UpdateVipBossDropList()
--	for
--end

-- VIPBoss
function BossWGData:GetVipBossList(need_reset)
	if next(self.vip_boss_list) == nil or need_reset then
		self.vip_boss_list = {}
		-- for i = 0, 6 do
		-- 	self.vip_boss_list[i] = {}
		-- end
        local bossvip_list = self.worldboss_auto.vipBoss_boss_info
        for i = 1, #bossvip_list do
            if nil ~= bossvip_list[i] then
				local j = bossvip_list[i].level
				local boss_vo = {}
				self:ResetIndexBossCfg(boss_vo, bossvip_list[i])
                if type(boss_vo.drop_item_list) ~= "table" then
                    boss_vo.is_item_list = false
                    boss_vo.drop_item_list = Split( bossvip_list[i].drop_item_list, "|")
                else
                    boss_vo.is_item_list = true
					boss_vo.drop_item_list = self:AddOpenDayItemList(bossvip_list[i], self.reset_vip_bosslist, TabIndex.boss_vip)
                end
                local boss_data = self:GetMonsterInfo(bossvip_list[i].boss_id)
                local need_role_level = self:GetEnterBossCfgbySceneId(bossvip_list[i].scene_id)
                boss_vo.boss_level = boss_data.level
                boss_vo.need_role_level = need_role_level
				boss_vo.boss_name = boss_data.name
				boss_vo.view_resouce = bossvip_list[i].boss_id
				boss_vo.big_icon = boss_data.small_icon
				boss_vo.boss_jieshu = boss_data.boss_jieshu
                boss_vo.show_duobei = true
                boss_vo.world_firstkill_times = bossvip_list[i].world_firstkill_times
                boss_vo.person_firstkill_reward = bossvip_list[i].person_firstkill_reward
                boss_vo.task_type = RATSDUOBEI_TASK.MOWANGCHAOXUE
                boss_vo.is_drop_jewelry = bossvip_list[i].is_drop_jewelry

                boss_vo.kill_reduce_xianli = bossvip_list[i].kill_reduce_xianli
				if nil == self.vip_boss_list[j] then
					self.vip_boss_list[j] = {}
				end
				table.insert(self.vip_boss_list[j], boss_vo)
			end
		end
	end
	return self.vip_boss_list
end

--获取初始仙力
function BossWGData:GetBossXianliMax()
	return self.other_cfg[1].xianli_init_value
end

--获取初始仙力
function BossWGData:GetBossXianliAddItem()
	return self.other_cfg[1].xianli_potion, self.other_cfg[1].xianli_potion_2
end

--是否显示灵力(开关)
function BossWGData:GetIsShowLingLi()
	return self.other_cfg[1].is_show_lingli
end

--是否能领取仙力
function BossWGData:CanGetBossXianliLevel()
    local level = 0
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    for k, v in pairs(self.vipBoss_xianli_receive) do
        level = v.need_level
    end
    return role_lv >= level
end

--获取仙力
function BossWGData:GetBossXianliCfg()
	return self.vipBoss_xianli_receive
end

function BossWGData:GetVipXianliFetchTimeCfg()
    local cfg = self:GetBossXianliCfg()
    if not self.vip_xianli_get_time_tb then
        self.vip_xianli_get_time_tb = {}
        for k, v in pairs(cfg) do
            local temp = {}
            temp.start_time = TimeUtil.FormatCfgTimestamp(v.start_time)
            temp.end_time = TimeUtil.FormatCfgTimestamp(v.end_time)
            self.vip_xianli_get_time_tb[#self.vip_xianli_get_time_tb + 1] = temp
        end
    end
    return self.vip_xianli_get_time_tb
end

--是否可以获取仙力
function BossWGData:JudgeCanGetBossXianli()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local fetch_time_tb = self:GetVipXianliFetchTimeCfg()
    local can_get = false
    local seq
    local cfg = self:GetBossXianliCfg()
    for i = 1, 2 do
        local temp = fetch_time_tb and fetch_time_tb[i]
        local v = cfg and cfg[i]
        if not IsEmptyTable(temp) and not IsEmptyTable(v) then
            local start_time = temp.start_time
            local end_time = temp.end_time
            local flag = BossAssistWGData.Instance:GetXianliFlagTbIndex(v.seq)
            if start_time <= server_time and server_time <= end_time and flag ~= 1 then
                can_get = true
                seq = v.seq
                break
            end
        end
    end
    if not self:CanGetBossXianliLevel() then
        can_get = false
    end
	return can_get, seq
end

function BossWGData:QifuCanGetBossXianli(index)
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local fetch_time_tb = self:GetVipXianliFetchTimeCfg()
    local can_get = false
    local cfg = self:GetBossXianliCfg()
    local temp = fetch_time_tb and fetch_time_tb[index]
    local v = cfg and cfg[index]
    if not IsEmptyTable(temp) and not IsEmptyTable(v) then
        local start_time = temp.start_time
        local end_time = temp.end_time
        local flag = BossAssistWGData.Instance:GetXianliFlagTbIndex(v.seq)
        if start_time <= server_time and server_time <= end_time and flag ~= 1 then
            can_get = true
        end
    end

	local level = 0
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    level = self.vipBoss_xianli_receive[index].need_level

    if role_lv < level then
        can_get = false
    end
	return can_get
end

function BossWGData:GetQiFiGetEnergyTimeRemaining()
	local timer = 0
	local has_reward ,seq = BossWGData.Instance:JudgeCanGetBossXianli()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if has_reward then
	    local fetch_time_tb = self:GetVipXianliFetchTimeCfg()
		local temp = fetch_time_tb and fetch_time_tb[seq + 1]
		timer = temp.end_time - server_time
	end
	return has_reward ,timer
end

function BossWGData:JudgeHasEnoughXianli()
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local cur_xianli = BossAssistWGData.Instance:GetBossXianli()
    local need_xianli = 5
    for k, v in pairs(self.vip_boss_xianli_level) do
        if role_lv >= v.min_level and role_lv <= v.max_level then
            need_xianli = v.tili
            break
        end
    end
    print_log("JudgeHasEnoughXianli need_xianli", need_xianli , cur_xianli)
    return cur_xianli >= need_xianli
end
--
function BossWGData:GetVipbossidByBossLv(boss_lv)
    local vip_boss_list = self:GetVipBossList()
    for k, v in pairs(vip_boss_list) do
        for k1,v1 in pairs(v) do
            if v1.boss_level == boss_lv then
                return k+1, k1
            end
        end
    end
    print_error("没有在魔界纵横配置中找到等级为"..boss_lv .."的boss")
end

-- Vipboss进入条件
function BossWGData:GetEnterVipCfgbyIndex(index)
	local enter_cfg = self.worldboss_auto.vipBoss_enter_cfg
	return enter_cfg[index]
end

function BossWGData:GetEnterVipCfgbySceneid(sceneid)
	local enter_cfg = self.worldboss_auto.vipBoss_enter_cfg
    for k,v in pairs(enter_cfg) do
        if v.scene_id ==  sceneid then
            return v
        end
    end
end

-- 个人boss进入最低vip等级
function BossWGData:GetEnterPersonCfgbyIndex()
    local vip_cfg = VipPower.Instance:GetPowerCfg(VipPowerId.vat_person_boss_times)
    local max_level = VipWGData.Instance:GetMaxVIPLevel()
    for i = 0, max_level do
		local vip_param = vip_cfg["param_".. i] or 0
		if vip_param >= 1 then
			return i
		end
	end
	return 1
end

function BossWGData:GetBossJieByRoleLevel()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	for k,v in pairs(self.worldboss_auto.lv_to_jie) do
		if v.role_lv_min <= role_level and v.role_lv_max >= role_level then
			return v.boss_jie
		end
	end
end

-- 拿到vipboss默认层数
-- 找到能打的有首杀的就直接选中
-- 先找能打的最高的并且BOSS存活并且能免费进的那一层
-- 不然就找能打的最高的并且有boss存活的那一层
function BossWGData:GetVipBossDefaultLayer(btn_list)
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local role_vip_lv = VipWGData.Instance:GetRoleVipLevel()
    local index = nil
    local free_index = nil
    local not_free_index = nil
    local min_index = nil
    local default_index = nil
    local first_index = nil
    local idx = #btn_list
    local boss_jie = self:GetBossJieByRoleLevel() or 0
	-- for i = #btn_list, 1, -1  do
	-- 	if role_level >= btn_list[i].tuijian_role_level then
	-- 		index = i
	-- 		break
	-- 	end
 --    end
    -- for i = 1, #btn_list do --策划说不要根据vip等级了
    --     if btn_list[i].free_vip_level >= role_vip_lv then
    --         idx = i
    --         break
    --     end
    -- end
 	for i = #btn_list, 1, -1  do
		if boss_jie >= btn_list[i].boss_jie_min and role_level >= btn_list[i].need_role_level then
			local layer_info = self.vip_layer_boss_list[i - 1]
			if layer_info ~= nil then
				-- 找免费并且存活的
				if role_vip_lv >= btn_list[i].free_vip_level and boss_jie <= btn_list[i].boss_jie_max then
					for k,v in pairs(layer_info) do
						if v == boss_jie and self:GetBossStatusByBossId(k) == 1 then
							if free_index == nil then
								free_index = i
							end
							
							if self:GetIsWorldLeftFirstKilledByBossId(v) then
								default_index = i
								break
							end
						end
					end
				else
					-- 找能打的最高的并且有BOSS存活的
					if boss_jie <= btn_list[i].boss_jie_max then
						for k,v in pairs(layer_info) do
							if v == boss_jie and self:GetBossStatusByBossId(v) == 1 then
								if not_free_index == nil then
									not_free_index = i
								end
								
								if self:GetIsWorldLeftFirstKilledByBossId(v) then
									default_index = i
									break
								end
							end
						end
					end
				end

				-- 能打的里面最小层
				if not_free_index == nil then
					if min_index == nil or boss_jie <= btn_list[i].boss_jie_max then
						min_index = i
					end
				end

				if default_index ~= nil then
					break
				end
			end
		end
	end

	if default_index == nil then
		default_index = free_index or not_free_index
		default_index = default_index or min_index
		default_index = default_index or #btn_list
	end

    idx = math.min(idx, default_index)
	return idx
end

-- 每一层，index为当前层数
function BossWGData:GetVipBossListByIndex(index)
	local boss_list = self:GetVipBossList()
	return boss_list[index - 1]
end

function BossWGData:GetVipBossIndexByLayerAndBossId(layer, boss_id)
    local boss_list = self:GetVipBossListByIndex(layer)
    if not boss_list then
        return false, nil
    end

    local boss_index = 0
    for i, v in ipairs(boss_list) do
		boss_index = boss_index + 1
        if v.boss_id == boss_id then
            break
        end
    end
    return true, boss_index
end

-- 服务端数据
function BossWGData:SetVipBossAllInfo(protocol)
	local all_boss_list = protocol.all_boss_list
	local layer_count = protocol.layer_count
	-- self.last_vip_boss_id = protocol.last_enter_boss_id
	for i = 0, layer_count - 1 do
		for k,v in pairs(all_boss_list[i].boss_list) do
			self.all_boss_info[v.boss_id] = v
		end
	end
	-- self.all_vip_boss_info = protocol.all_boss_list
end

function BossWGData:SetVipBossLayer(boss_list)
	local index = 0
	for k,v in pairs(boss_list) do
		self.all_boss_info[v.boss_id] = v

		index = index + 1
		self.vip_boss_task_list[index] = v
	end
end

-- 是否在VipBoss场景中
function BossWGData:GetIsBossVipScene(scene_id)
	local vipboss_auto = ConfigManager.Instance:GetAutoConfig("worldboss_auto").vipBoss_boss_info
	for k, v in pairs(vipboss_auto) do
		if v.scene_id == scene_id then
			return true, v
		end
	end
	return false
end

-- 获取当前场景的vipbossBossID
function BossWGData:GetVipBossListByScene(scene_id)
	local vip_boss_cfg_list = {}
	local vipboss_auto = ConfigManager.Instance:GetAutoConfig("worldboss_auto").vipBoss_boss_info
	for k, v in pairs(vipboss_auto) do
		if v.scene_id == scene_id then
			table.insert(vip_boss_cfg_list, v)
		end
	end
	return vip_boss_cfg_list
end

function BossWGData:GetVipBossCfgBossId(boss_id)
	local cfg = {}
	for i, v in pairs(self.worldboss_auto.vipBoss_boss_info) do
		if v.boss_id == boss_id then
			cfg.boss_id = v.boss_id
			cfg.level = v.level
			cfg.scene_id = v.scene_id
			cfg.max_delta_level = v.max_delta_level
		end
	end
	if cfg.level then
		local layer_info = self:GetEnterVipCfgbyIndex(cfg.level + 1)
		cfg.need_role_level = layer_info.need_role_level
	else
		cfg.need_role_level = 0
		cfg.max_delta_level = 0
	end
	return cfg
end

function BossWGData:GetVipBossCfgByBossIdAndSceneId(boss_id, scene_id)
	local cfg = nil
	for i, v in pairs(self.worldboss_auto.vipBoss_boss_info) do
		if v.boss_id == boss_id and v.scene_id == scene_id then
			cfg = v
			break
		end
	end

	return cfg
end

function BossWGData:GetVipEnoughTimes()
	local wb_other_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
	local hb_times_info = BossWGData.Instance:GetBossHomeEnterTimeInfo()
	local has_times = wb_other_cfg.boss_home_day_default_times + hb_times_info.boss_home_day_item_add_kill_times + hb_times_info.boss_home_day_vip_buy_kill_times
			- hb_times_info.boss_home_day_kill_times
	return has_times > 0
end

--------------  跨服boss  ------------
---跨服小怪 已屏蔽
function BossWGData:GetOneMonsterByLayer(index)
	local vo = {}
	local other = ConfigManager.Instance:GetAutoConfig("cross_boss_auto").other[1]
	for i = 1, #self.crossmonster_list do
		if self.crossmonster_list[i].layer == index then
			vo.layer = self.crossmonster_list[i].layer
			vo.boss_index = 0
			vo.boss_id = self.crossmonster_list[i].monster_id
			vo.x_pos = self.crossmonster_list[i].pos_x
			vo.y_pos = self.crossmonster_list[i].pos_y
			vo.drop_item_list = Split(other.drop_item_list,"|")
			vo.boss_level = 1
			vo.boss_name = self.cross_other_cfg.monster_name
			vo.boss_hp = 0
			vo.boss_atk = 0
			vo.boss_defen = 0
			vo.damage_type = 0
			vo.boss_magdef = 0
			vo.boss_pojia = 0
			vo.big_icon = other.big_icon
            vo.view_resouce = other.monster_img
			--vo.adjust_x = 0
			--vo.adjust_height = 0
			vo.type = BossWGData.MonsterType.Monster
			vo.max_delta_level = COMMON_CONSTS.MaxRoleLevel
			vo.scene_id = 0
			vo.level = self.crossboss_monster_list[vo.boss_id].level
			vo.scale = 2
			return vo
		end
	end
end

---跨服小怪 已屏蔽
function BossWGData:GetCrossMonsterList()
	if not self.corss_monster_list then
		self.corss_monster_list = {}
		local cross_boss_cfg = ListToMap(self.crossboss_list,"layer")
		local other = ConfigManager.Instance:GetAutoConfig("cross_boss_auto").other[1]
		for i = 1, #self.crossmonster_list do
			local vo = {}
			vo.layer = self.crossmonster_list[i].layer
			vo.boss_index = 0
			vo.boss_id = self.crossmonster_list[i].monster_id
			vo.x_pos = self.crossmonster_list[i].pos_x
			vo.y_pos = self.crossmonster_list[i].pos_y
			vo.drop_item_list = Split(other.drop_item_list,"|")
			vo.name = self.cross_other_cfg.monster_name
			vo.boss_hp = 0
			vo.boss_atk = 0
			vo.boss_defen = 0
			vo.damage_type = 0
			vo.boss_magdef = 0
			vo.boss_pojia = 0
			vo.big_icon = other.big_icon
            vo.view_resouce = other.monster_img
			--vo.adjust_x = 0
			--vo.adjust_height = 0
			vo.type = BossWGData.MonsterType.Monster
			vo.max_delta_level = COMMON_CONSTS.MaxRoleLevel
			vo.scene_id = cross_boss_cfg and cross_boss_cfg[self.crossmonster_list[i].layer].scene_id or 0
			vo.scale = 2
			vo.boss_level = self.crossboss_monster_list[vo.boss_id].level
			vo.boss_name = self.cross_other_cfg.monster_name

			self.corss_monster_list[vo.boss_id] = vo
		end
	end
	return self.corss_monster_list
end

function BossWGData:GetCrossAllBoss(need_reset)
	if next(self.cross_boss_all_list) == nil or need_reset then
		self.cross_boss_all_list = {}
		-- for i = 1, 2 do
		-- 	self.cross_boss_all_list[i] = {}
		-- end
		-- 水晶--2019.11.21策划要求去掉水晶独立
		---- 精英怪--2019.10.25策划要求去掉精英怪
		--for i = 1, #self.cross_boss_all_list do
		--	local vo = self:GetOneMonsterByLayer(i)
		--	table.insert(self.cross_boss_all_list[i], vo)
		--end
		-- boss
		local boss_data = {}
		for i = 1, #self.crossboss_list do
			local vo = {}
			vo.layer = self.crossboss_list[i].layer
			vo.boss_index = self.crossboss_list[i].boss_index
			vo.boss_id = self.crossboss_list[i].boss_id
			vo.x_pos = self.crossboss_list[i].flush_pos_x
			vo.y_pos = self.crossboss_list[i].flush_pos_y
            if type(self.crossboss_list[i].drop_item_list) ~= "table" then
                vo.is_item_list = false
                vo.drop_item_list = Split( self.crossboss_list[i].drop_item_list, "|")
            else
                vo.is_item_list = true
				vo.drop_item_list = self:AddOpenDayItemList(self.crossboss_list[i], self.reset_cross_bosslist)
            end
			vo.reward_item = self.crossboss_list[i].reward_item
			boss_data = self:GetMonsterInfo(self.crossboss_list[i].boss_id)
			vo.boss_level = boss_data.level
			vo.boss_name = boss_data.name
			vo.boss_jieshu = boss_data.boss_jieshu
			vo.big_icon = boss_data.small_icon
            vo.view_resouce = self.crossboss_list[i].boss_id
			vo.max_delta_level = self.crossboss_list[i].max_delta_level
			vo.scene_id = self.crossboss_list[i].scene_id
			vo.type = BossWGData.MonsterType.Boss
			vo.scale = self.crossboss_list[i].scale
			vo.position = self.crossboss_list[i].position
			vo.rotation = self.crossboss_list[i].rotation
			vo.bottom_color = self.crossboss_list[i].bottom_color
			self.cross_boss_list[vo.boss_id] = vo
			self.new_cross_boss_list[vo.boss_id] = vo
			if not self.cross_boss_all_list[vo.layer] then
				self.cross_boss_all_list[vo.layer] = {}
			end
			table.insert(self.cross_boss_all_list[vo.layer], vo)
		end
	end
	return self.cross_boss_all_list
end

function BossWGData:GetKFGatherInfo(layer)
	if not self.kf_boss_gather_list then
		self.kf_boss_gather_list = {}
		for i = 1, #self.crosscrytal_lsit do
			local vo = {}
			vo.layer = self.crosscrytal_lsit[i].layer_index
			vo.boss_index = 0
			vo.boss_id = self.crosscrytal_lsit[i].treasure_crystal_gather_id
			vo.x_pos = self.crosscrytal_lsit[i].entry_x
			vo.y_pos = self.crosscrytal_lsit[i].entry_y
			vo.drop_item_list = Split(self.crosscrytal_lsit[i].drop_item_list, "|")
			vo.reward_item = self.crossboss_list[i].reward_item
			vo.boss_level = 1
			vo.boss_name = self.crosscrytal_lsit[i].treasure_crystal_name
			vo.big_icon = self.crosscrytal_lsit[i].big_icon
			vo.view_resouce = self.crosscrytal_lsit[i].effect_id
			--vo.adjust_x = self.crosscrytal_lsit[i].adjust_x
			--vo.adjust_height = self.crosscrytal_lsit[i].adjust_y
			vo.type = BossWGData.MonsterType.Gather
			vo.max_delta_level = COMMON_CONSTS.MaxRoleLevel
			vo.scale = 1
			vo.scene_id = self.crosscrytal_lsit[i].scene_id

			vo.position = self.crosscrytal_lsit[i].position
			vo.rotation = self.crosscrytal_lsit[i].rotation
			self.new_cross_boss_list[vo.boss_id] = vo
			--if not self.cross_boss_all_list[vo.layer] then
			--	self.cross_boss_all_list[vo.layer] = {}
			--end
			--table.insert(self.cross_boss_all_list[vo.layer], vo)
			self.kf_boss_gather_list[i] = vo
		end
	end
	return self.kf_boss_gather_list[layer]
end

function BossWGData:GetSYBossAtt(boss_id)
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto").monster_cfg
	for k,v in ipairs(cfg) do
		if v.monsterid == boss_id then
			return v
		end
	end
	return {}
end
function BossWGData:GetShenYunBosscfg()
	local data = {}
	for k,v in ipairs(self.shenyunboss_list) do
			 if v.ui_show == 1 then
			 	table.insert(data, v)
			 end
	end
	return data or {}
end

--陨落之地屏蔽种
function BossWGData:GetShenYunKfFb()
	local  syboss_cfg  = self:GetShenYunBosscfg()
	-- print_error(#syboss_cfg)
	if next(self.shenyun_boss_fb_list) == nil then
		-- for i = 1, 3 do
		-- 	self.shenyun_boss_fb_list[i] = {}
		-- end
		local boss_data = {}
		for i = 1, #syboss_cfg do
			local vo = {}
			vo.layer = syboss_cfg[i].level
			vo.boss_index = syboss_cfg[i].boss_index
			vo.boss_id = syboss_cfg[i].boss_id
			vo.x_pos = syboss_cfg[i].x_pos
			vo.y_pos = syboss_cfg[i].y_pos
			vo.name = syboss_cfg[i].name
			vo.drop_item_list = Split( syboss_cfg[i].drop_item_list, "|")
			boss_data = self:GetSYBossAtt(syboss_cfg[i].boss_id)
			vo.yaolichuantou = boss_data.yaolichuantou
			vo.yaolidikang = boss_data.yaolidikang
			vo.molichuantou = boss_data.molichuantou
			vo.molidikang = boss_data.molidikang
			vo.shenlichuantou = boss_data.shenlichuantou
			vo.damage_type = boss_data.damage_type
			vo.shenlidikang = boss_data.shenlidikang
			vo.gongji = boss_data.gongji
			vo.shengming = boss_data.shengming
			vo.big_icon = syboss_cfg[i].big_icon
            vo.view_resouce = syboss_cfg[i].view_resouce
			--vo.adjust_x = syboss_cfg[i].adjust_x
			--vo.adjust_height = syboss_cfg[i].adjust_height
			-- vo.max_delta_level = syboss_cfg[i].max_delta_level
			vo.scene_id = syboss_cfg[i].scene_id
			-- vo.type = BossWGData.MonsterType.Boss
			vo.scale = syboss_cfg[i].scale
			-- self.cross_boss_list[vo.boss_id] = vo
			if not self.shenyun_boss_fb_list[vo.layer] then
				self.shenyun_boss_fb_list[vo.layer] = {}
			end
			table.insert(self.shenyun_boss_fb_list[vo.layer], vo)
		end
	end
	return self.shenyun_boss_fb_list
end

function BossWGData:GetShenYunBylayer(index)
	local all_list = self:GetShenYunKfFb()
	return all_list[index]
end


function BossWGData:GetMapBossCfg(scene_id)
	local cfg  = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto").Interface_show
	local data = {}
	for k,v in ipairs(cfg) do
		if v.scene_id == scene_id then
			table.insert(data, v)
		end
	end
	return data or {}
end

function BossWGData:SetShenYinFbDayCount(fb_count)
	self.shenyin_count = fb_count or 0
end
function BossWGData:GetShenYinFbDayCount()
	return self.shenyin_count
end

function BossWGData:GetShenYinMapInfo(protocol)
	self.sy_jinli = protocol.jingli
	self.total_jingli = protocol.total_jingli
	self.totem_id = protocol.totem_id
end

function BossWGData:OnSyFBTimeFush(protocol)
	self.status = protocol.status
	self.next_timestamp = protocol.next_timestamp

	-- local time = self.next_timestamp - TimeWGCtrl.Instance:GetServerTime()
	-- print_error(self.next_timestamp, TimeUtil.FormatSecond(time, 3))
end

function BossWGData:GetShenyingShuxin()
	local data_list = {}
	local myself_att = __TableCopy(RoleBagWGData.Instance:GetSealTotalAttr())
	for k,v in ipairs(myself_att) do
		data_list[v[1]] = v[2]
	end
  return data_list
end

function BossWGData:SetSYBossRender()
	local boss_cfg = BossWGData.Instance:GetShenyingShuxin()
	local SuitAttrinfoList = {}
		if boss_cfg then
			SuitAttrinfoList.yaolichuantou = boss_cfg.yaolichuantou or 0
			SuitAttrinfoList.yaolidikang =  boss_cfg.yaolidikang or 0
			SuitAttrinfoList.molichuantou = boss_cfg.molichuantou or 0
			SuitAttrinfoList.molidikang = boss_cfg.molidikang or 0
			SuitAttrinfoList.shenlichuantou = boss_cfg.shenlichuantou or 0
			SuitAttrinfoList.shenlidikang = boss_cfg.shenlidikang or 0
		end
		return SuitAttrinfoList
end

function BossWGData:GetSYBossRender(att)
	local x_cfg = BossWGData.Instance:SetSYBossRender()
	local data = {}
	if data then
		data.yaolichuantou = x_cfg.yaolichuantou - att.yaolichuantou
		data.yaolidikang = x_cfg.yaolidikang - att.yaolidikang
		data.molichuantou = x_cfg.molichuantou - att.molichuantou
		data.molidikang = x_cfg.molidikang - att.molidikang
		data.shenlichuantou = x_cfg.shenlichuantou - att.shenlichuantou
		data.shenlidikang = x_cfg.shenlidikang - att.shenlidikang
	end
	return data
end

function BossWGData:ShenYunTuTengObj(protocol)
	local totem_count = protocol.totem_count
	if totem_count > 0 then
		local list = {}
		for k,v in pairs(protocol.totem_list) do
			local state = true
			for i,j in pairs(self.totem_list) do
				if j.pos_index == v.pos_index then
					j.totem_objid = v.totem_objid
					j.owner_id = v.owner_id
					j.totem_type = v.totem_type
					state = false
				end
			end
			if state then
				table.insert(list,v)
			end
		end
		for k,v in pairs(list) do
			table.insert(self.totem_list,v)
		end
	else
		self.totem_list = protocol.totem_list
	end
	-- if self.totem_count == BossWGData.TUTENGMAX then
	-- self.totem_list = protocol.totem_list
	-- elseif self.totem_count > 0 then
		-- for k,v in pairs(protocol.totem_list) do
		-- 	for i,j in pairs(self.totem_list) do
		-- 		if v.totem_objid == j.totem_objid then
		-- 			j.owner_id = v.owner_id
		-- 		end
		-- 	end
		-- end
	-- else
	-- 	self.totem_list = protocol.totem_list
	-- end
end

function BossWGData:GetTutengType(index)
	if not IsEmptyTable(self.totem_list) and next(self.totem_list) then
		for k,v in ipairs(self.totem_list) do
			if v.pos_index == index then
				return v.totem_type or 0
			end
		end
	else
		return 0
	end
end

function BossWGData:GetTuTengObj(obj_id)
	for  k,v in ipairs(self.totem_list) do
		if v.totem_objid  == obj_id then
			return v
		end
	end
	return {}
end

function BossWGData:GetSYFulshTime()
	return self.next_timestamp  or 0
end

function BossWGData:GetSYTiemStatus()
	return self.status
end

function BossWGData:GetTuTengRoleObj(obj_id)
	for  k,v in pairs(self.totem_list) do
		if v.owner_id  == obj_id then
			return true
		end
	end
	return false
end

function BossWGData:GetSHenYinJinLi()
	return self.sy_jinli or 0, self.total_jingli or 1
end

function BossWGData:GetTicketNeed()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto").ticket_cfg
	local num  = self.shenyin_count
	if num ~= #cfg then
		num = self.shenyin_count + 1
	else
		num = self.shenyin_count
	end
	for k,v in ipairs(cfg) do
		if v.enter_count == num then
			return v or {}
		end
	end
end

function BossWGData:MaxGetintoNum()
	local cfg = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto").enter_cfg
	local role_vip_level = RoleWGData.Instance.role_vo.vip_level
	local max_num = 0
	for k,v in pairs(cfg) do
		if v.vip_level == role_vip_level then
			max_num = v.enter_times
		end
	end
	if self.shenyin_count == max_num  then
  		return false
	end
	return true
end

function BossWGData:SetShenYunTicket(vip_level)
	local cfg  = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto").enter_cfg
	for k,v in pairs(cfg) do
		if v.vip_level == vip_level then
			return v.enter_times or 0
		end
	end
end

function BossWGData:GetShenYunTicketID()
	local cfg  = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto").other
	return cfg[1].ticketid
end

function BossWGData:GetShenYunOther()
	if self.shen_yun_other == nil then
		self.shen_yun_other = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto").other
	end
	return self.shen_yun_other
end

function BossWGData:GetCrossLayerBossBylayer(index)
	local all_list = self:GetCrossAllBoss()
	return all_list[index]
end

function BossWGData:GetCrossLayerBossAndGatherBylayer(index)
	local gather = self:GetKFGatherInfo(index)
	local list = {[1] = gather}
	local cfg = self:GetCrossLayerBossBylayer(index)
	if not cfg then
		return list
	end

	for i, v in ipairs(cfg) do
		table.insert(list, v)
	end

	return list
end

function BossWGData:GetCrossBossInfoByBossId(boss_id)
	if next(self.cross_boss_list) == nil then
		self:GetCrossAllBoss()
	end
	return self.cross_boss_list[boss_id]
end

function BossWGData:SetCrossBossSceneInfo(protocol)
	self.leftmonsterandtreasure[protocol.layer] = {protocol.left_treasure_crystal_num, protocol.left_monster_count}
	local treasure_crystal_gather_id = protocol.treasure_crystal_gather_id
	local monster_next_flush_timestamp = protocol.monster_next_flush_timestamp
	local treasure_crystal_next_flush_timestamp = protocol.treasure_crystal_next_flush_timestamp
	for k, v in pairs(protocol.boss_list) do
		local vo = {}
		vo.boss_id = v.boss_id
		vo.is_exist = v.is_exist
		vo.next_refresh_time = v.next_flush_time
		vo.left_num = 0
		vo.kill_vo = {}
		if v.last_killer_name ~= "" then
			table.insert(vo.kill_vo, {last_killer_name = v.last_killer_name})
		end
		self.cross_boss_info[v.boss_id] = vo
		self:SetAllBossInfo(v.boss_id, vo)
	end
	local treasure_crystal_vo = {}
	treasure_crystal_vo.boss_id = treasure_crystal_gather_id
	treasure_crystal_vo.exist = treasure_crystal_next_flush_timestamp > 0 and 1 or 0
	treasure_crystal_vo.next_refresh_time = treasure_crystal_next_flush_timestamp
	treasure_crystal_vo.left_num = protocol.left_treasure_crystal_num
	self.cross_boss_info[treasure_crystal_gather_id] = treasure_crystal_vo
	self.cur_bossLayer = protocol.layer
	-- local monster_info = self:GetOneMonsterByLayer(protocol.layer)
	-- local monster_vo = {}
	-- monster_vo.boss_id = monster_info.boss_id
	-- monster_vo.exist = monster_next_flush_timestamp > 0 and 1 or 0
	-- monster_vo.next_refresh_time = monster_next_flush_timestamp
	-- monster_vo.left_num = protocol.left_monster_count
	--self.cross_boss_info[monster_info.boss_id] = monster_vo
end

function BossWGData:GetCrossBossById(boss_id)
	return self.cross_boss_info[boss_id]
end

function BossWGData:GetTreasureGatherTimes()
    local max = self:GetTreasureGatherMaxTimes()
	return max - self.had_treasure_crystal_gather_times, self.left_ordinary_crystal_gather_times
end

function BossWGData:SetTreasureGatherTimes(times)
	--self.had_treasure_crystal_gather_times = times
end

function BossWGData:OnSCCrossBossCrystalGathertTimes(protocol)
	self.new_cross_boss_gather_info = protocol.gather_seq_times
	local have_gather_num = 0
	if self.crystal_refresh_cfg then
		for k,v in pairs(self.crystal_refresh_cfg[1]) do
			if self.new_cross_boss_gather_info[v.seq + 1] == 1 then
				have_gather_num = have_gather_num + 1
			end
		end
	end
	--采集改变次数
	if self.had_treasure_crystal_gather_times < have_gather_num then
		local scene_type = Scene.Instance:GetSceneType()
		if SceneType.KF_BOSS == scene_type then
			-- local scene_id = Scene.Instance:GetSceneId()
			-- local cur_gather_info = self:GetKFNewGatherSceneCfgBySceneId(scene_id)
			-- if cur_gather_info then
			-- 	local new_gather_info = self:GetNewKFBossGatherInfo()
			-- 	if new_gather_info and  new_gather_info[1]  then
			-- 		local num1 = 1 - new_gather_info[1] or 0
			-- 		local num2 = 1 - new_gather_info[2] or 0
			-- 		local num3 = 1 - new_gather_info[3] or 0
			-- 		local color1 = num1 > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
			-- 		local color2 = num2 > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
			-- 		local color3 = num3 > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
			--		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.Boss.NewGatherLeftTime2,cur_gather_info.treasure_crystal_name,color1,num1,cur_gather_info.treasure_crystal_name,color2,num2,cur_gather_info.treasure_crystal_name,color3,num3))
				--end
			--end
			TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.NewGatherLeftTime3)
		end
	end
	self.had_treasure_crystal_gather_times = have_gather_num
end

function BossWGData:GetNewGatherLayerCfg(layer)
	return self.crystal_refresh_cfg[layer] or {}
end

function BossWGData:GetNewKFBossGatherInfo()
	return self.new_cross_boss_gather_info or {}
end

function BossWGData:GetKFNewGatherSceneCfgBySceneId(scene_id)
	if self.crosscrytal_scene_id_list then
		return self.crosscrytal_scene_id_list[scene_id] or {}
	end
	return {}
end

function BossWGData:GetTreasureGatherMaxTimes()
	return self.cross_other_cfg.treasure_crystal_gather_times or 0
end

function BossWGData:SetCrossBossPalyerInfo(protocol)
	self.left_ordinary_crystal_gather_times = protocol.left_ordinary_crystal_gather_times
	self.kill_boss_num = protocol.kill_boss_num
	--self.had_treasure_crystal_gather_times = protocol. had_treasure_crystal_gather_times 使用daycount 163
    self.tired_buy_times = protocol.tired_buy_times
    local length = #self.kf_boss_cfg.layer_cfg or 5
	for i = 1, length do
		local flag = bit:d2b(protocol.concern_flag[i])
		self.concern_flag[i] = flag
	end

	if not self.corss_boss_list then
		self:GetCrossAllBoss()
	end
	for i, v in pairs(self.cross_boss_list) do
		local flag_list = self.concern_flag[v.layer]
		v.is_concern = flag_list[33 - v.boss_index]
		self:SetAllBossInfo(v.boss_id, {is_concern = v.is_concern})
	end
end

function BossWGData:GetCrossBossTire()
	local max_tire_value = self.cross_other_cfg.daily_boss_num
	return self.kill_boss_num or 0, max_tire_value + (self.tired_buy_times or 0) + (self.tired_vip_buy_times or 0)
end

function BossWGData:GetCrossBossIsConcern(layer, boss_index)
	if not layer or not boss_index then return 0 end
	local flag_list = self.concern_flag[layer]
	return flag_list and flag_list[33 - boss_index] or 0
end

function BossWGData:SetCrossBossBuyTime(value)
	self.tired_vip_buy_times = value
end

function BossWGData:GetCrossBossBuyTime()
	return self.tired_vip_buy_times or 0
end

-- 缓存
function BossWGData:SetCrossBossConcern(layer, boss_id, is_concern, show_index)
	if show_index == BossViewIndex.SGBOSS then
		local boss_data = self:GetBossRefreshInfoByBossId(boss_id)
		boss_data.is_concern = is_concern and 1 or 0
		return
	end
	local boss_index = self:GetCrossBossInfoByBossId(boss_id).boss_index
	local flag_list = self.concern_flag[layer]
	flag_list[33 - boss_index] = is_concern and 1 or 0
end

function BossWGData:SetCrossBossBossKillRecord(protocol)
	self.cross_killer_record = {}
	for i = 1, protocol.record_count do
		local killer_vo = {}
		killer_vo.last_killer_name = protocol.killer_record_list[i].killer_name
		killer_vo.kill_boss_time = protocol.killer_record_list[i].dead_timestamp
		killer_vo.last_killer_uid = protocol.killer_record_list[i].uuid
		self.cross_killer_record[i] = killer_vo
		--self:SetAllBossInfo()
	end
end

function BossWGData:GetCrossBossBossKillRecord()
	return self.cross_killer_record
end

function BossWGData:SetCrossBossDropRecord(protocol)
	self.cross_boss_record = protocol.dorp_record_list
end

function BossWGData:GetCrossBossDropRecord()
	if self.hmsy_drop_list then
		local t
		for i, v in pairs(self.hmsy_drop_list) do
			t = self.rare_item_cfg[v.item_id]
			if t then
				v.is_top = t.is_top
			else
				v.is_top = 0
			end
		end
	end
	table.sort(self.hmsy_drop_list, SortTools.KeyUpperSorters("is_top", "drop_timestamp"))
	return self.hmsy_drop_list
end

------------   个人BOSS   ------------

-- 个人boss首杀信息
function BossWGData:GetPersonBossCfg(scene_id)
	return self.personal_scene_cfg[scene_id]
end

-- 个人boss首杀信息
function BossWGData:SetPersonBossFirstKillRecordInfo(protocol)
	self.person_boss_day_kill_times = protocol.person_boss_day_kill_times
	self.person_boss_day_buy_kill_times = protocol.person_boss_day_buy_kill_times
	self.person_boss_first_kill_flag = bit:d2b(protocol.person_boss_first_kill_flag)
end

-- 是否首杀
function BossWGData:IsPersonBossFirstKill(index)
	return self.person_boss_first_kill_flag[32 - index] ~= nil and self.person_boss_first_kill_flag[32 - index] == 0
end

-- 个人boss购买次数
function BossWGData:GetPersonBossBuyCount()
	return self.person_boss_day_buy_kill_times
end

-- 个人boss今日击杀次数
function BossWGData:PersonBossDayKillTimes(index)
	return self.person_boss_day_kill_times
end


-- 个人boss购买消耗
function BossWGData:GetPersonBossBuyTimesExtra(times)
	for k,v in pairs(self.personal_cfg.extra_time_cfg) do
		if v.extra_times == times then
			return v.cost_gold
		end
	end
	return 0
end

function BossWGData:GetPersonBossOtherCfg()
	return self.personal_cfg.other[1]
end

function BossWGData:GetPersonalBossList(need_reset)
	if IsEmptyTable(self.personal_boss_list) or need_reset then
		if need_reset then
			self.personal_boss_list = {}
		end

		local role_level = RoleWGData.Instance:GetRoleLevel()
		local limit_count = 0
		local boss_data = {}
		local is_item_list = type(self.personal_cfg.boss_scene_cfg[1].drop_item_list) == "table"
		local boss_cfg

		for i = 1, #self.personal_cfg.boss_scene_cfg do
			boss_cfg = self.personal_cfg.boss_scene_cfg[i]
			if boss_cfg.need_level > role_level then
				limit_count = limit_count + 1
			end
			if limit_count <= 4  then
				boss_data = self:GetMonsterInfo(boss_cfg.boss_id)
				self.personal_boss_list[i] = {}
				self:ResetIndexBossCfg(self.personal_boss_list[i], boss_cfg)

				self.personal_boss_list[i].is_item_list = is_item_list
				self.personal_boss_list[i].need_role_level = boss_cfg.need_level
				self.personal_boss_list[i].index = i
				self.personal_boss_list[i].boss_level = boss_data.level
				self.personal_boss_list[i].boss_name = boss_data.name
				self.personal_boss_list[i].view_resouce = boss_cfg.boss_id
				self.personal_boss_list[i].big_icon = boss_data.small_icon
				self.personal_boss_list[i].is_per = true

				if is_item_list then
					self.personal_boss_list[i].drop_item_list = self:AddOpenDayItemList(self.personal_boss_list[i], self.reset_per_bosslist, TabIndex.boss_personal)
                end
			end
		end
	end
    return self.personal_boss_list
end

function BossWGData:GetPersonalBossEnterInfo()
    local vip_level_cfg = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.PERSONBOSS_ENTER_TIMES)
    local role_vip = VipWGData.Instance:GetRoleVipLevel()
    local s_vip = role_vip < 4 and 4 or role_vip --策划让不足4，按v4显示
	local free_num = vip_level_cfg["param_" .. s_vip]
    local max_enter_num = free_num + self.person_boss_day_buy_kill_times
	return max_enter_num - self.person_boss_day_kill_times, max_enter_num
end

--遍历所有个人boss配置
function BossWGData:GetPerBossCfgInAllInfo(boss_id)
	for i, v in pairs(self.personal_cfg.boss_scene_cfg) do
		if v.boss_id == boss_id then
			return v
		end
	end
end

function BossWGData:SetKillBossSucc(is_succ)
	self.is_succ = is_succ
end

function BossWGData:GetKillBossSucc()
	return self.is_succ
end

function BossWGData:SetFubenPassTime(passtime)
	self.pass_time = passtime
end

function BossWGData:GetFubenPassTime()
	return self.pass_time or 0
end

function BossWGData:GetPersonBossRemindNum()
	local left_enter_num, _ = self:GetPersonalBossEnterInfo()
    if left_enter_num > 0 then  --有次数
        local cfg = BossWGData.Instance:GetPersonBossOtherCfg()
        if cfg and cfg.enter_item_id then
            local num = ItemWGData.Instance:GetItemNumInBagById(cfg.enter_item_id)
            if num >= cfg.enter_item_num then
                return 1
            end
        end
		return 0
	end
	-- local role_level = GameVoManager.Instance:GetMainRoleVo().level
	-- for i,v in ipairs(self.personal_cfg.boss_scene_cfg) do
	-- 	local mmonster_level = self:GetMonsterCfgByid(v.boss_id).level
	-- 	if role_level >= v.need_level and role_level - mmonster_level <= v.max_delta_level
	-- 			and self:IsPersonBossFirstKill(v.layer) then --有新boss
	-- 		return 1
	-- 	end
	-- end
	return 0
end

function BossWGData:GetHasNewPersonBoss()
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	for i,v in ipairs(self.personal_cfg.boss_scene_cfg) do
		if role_level >= v.need_level and self:IsPersonBossFirstKill(v.layer) then --有新boss
			return 1
		end
	end
	return 0
end
------------   上古遗迹   ------------

function BossWGData:GetSGHideMonsterByLayer(scene_id)
	local layer_cfg = self.sgyj_cfg.hidden_boss
	local list = {}
	for i,v in ipairs(layer_cfg) do
		if v.scene_id == scene_id then
			table.insert(list, v)
		end
	end
	return list
end

function BossWGData:GetSGHideMonsterOnceByLayer(scene_id)
	local layer_cfg = self.sgyj_cfg.hidden_boss
	for i,v in ipairs(layer_cfg) do
		if v.scene_id == scene_id then
			return v
		end
	end
end

function BossWGData:GetSgLayerCfg(layer)
	local layer_cfg = self.sgyj_cfg.scene
	for k, v in pairs(layer_cfg) do
		if v.layer == layer then
			return v
		end
	end
end

function BossWGData:GetSgLayerIsEnter(layer)
	local level = RoleWGData.Instance.role_vo.level
	local layer_cfg = self.sgyj_cfg.scene
	if not layer_cfg[layer+1] then return true, nil end
	local temp = layer_cfg[layer+1].enter_level_limit
	return level >= temp,temp
end

function BossWGData:GetCurSgLayerBySceneId(scene_id)
	scene_id = scene_id or Scene.Instance:GetSceneId()
	local layer_cfg = self.sgyj_cfg.scene
	for k, v in pairs(layer_cfg) do
		if v.scene_id == scene_id then
			return v.layer
		end
	end
	return -1
end

function BossWGData:GetSGSceneCfg()
	return self.sgyj_cfg.scene
end

function BossWGData:GetSGOtherCfg()
	return self.sgyj_cfg.other[1]
end

--2019.11.1黄金怪已删除
function BossWGData:GetSGMonsterListMonster()
	return {}
end

function BossWGData:GetSGGatherCfg()
	return self.sgyj_cfg.gather
end

function BossWGData:GetSGGatherShow()
	local cfg = self.sgyj_cfg.gather
	return cfg[2].gather_id
end

function BossWGData:GetSGAllBoss(need_reset)--zzz、
	if next(self.sg_boss_all_list) == nil or need_reset then
		self.sg_boss_all_list = {}
		-- 宝箱
		local other = self:GetSGOtherCfg()
		local layer_cfg = self:GetSGSceneCfg()
		local layer_count = #layer_cfg
		local gather_cfg = self:GetSGGatherCfg()
		gather_cfg = gather_cfg[2]
		local gather_info = MapWGData.Instance:GetGatherById(gather_cfg.gather_id)
		for i = 1, layer_count do
			self.sg_boss_all_list[i] = {}
			local cfg = layer_cfg[i]
			local vo = {}
			vo.is_baoxiang = true
			vo.layer = i - 1
			vo.boss_index = 0
			vo.boss_id = gather_cfg.gather_id
            if type(gather_cfg.drop_item_list) ~= "table" then
                vo.is_item_list = false
                vo.drop_item_list = Split(gather_cfg.drop_item_list, "|")
            else
                vo.is_item_list = true
				vo.drop_item_list = self:AddOpenDayItemList(gather_cfg, self.reset_sgyj_bosslist)
            end
			vo.boss_level = 1
			if gather_info then
				vo.boss_name = gather_info.name
				vo.big_icon = gather_cfg.big_icon
				vo.view_resouce = gather_cfg.gather_id
				vo.scale = gather_cfg.scale
			end
			vo.scene_id = cfg.scene_id
			vo.max_delta_level = COMMON_CONSTS.MaxRoleLevel
			vo.type = BossWGData.MonsterType.Gather
			table.insert(self.sg_boss_all_list[i], vo)
			self.sg_boss_list[vo.boss_id] = vo
		end
		-- 黄金怪
		--for i = 1, layer_count do
		--	local vo = self:GetSGMonsterByLayer(i - 1)
		--	table.insert(self.sg_boss_all_list[i], vo)
		--end
		-- 隐藏Boss
		for i = 1, layer_count do
			local cfg = layer_cfg[i]
			local boss_info = {}
			local vo = self:GetSGHideMonsterOnceByLayer(cfg.scene_id)
			boss_info.boss_id = vo.boss_id
			local boss_data = self:GetMonsterInfo(vo.boss_id)
            if type(vo.drop_item_list) ~= "table" then
                boss_info.is_item_list = false
                boss_info.drop_item_list = Split(vo.drop_item_list, "|")
            else
                boss_info.is_item_list = true
				vo.drop_item_list = self:AddOpenDayItemList(vo, self.reset_sgyj_bosslist)
            end
			local boss_data = self:GetMonsterCfgByid(vo.boss_id)
			boss_info.boss_level = boss_data.level
			boss_info.boss_name = Language.Boss.HideBoss
			boss_info.x_pos = vo.born_x
			boss_info.y_pos = vo.born_y
			boss_info.max_delta_level = 999
			boss_info.type = BossWGData.MonsterType.HideBoss
			boss_info.scene_id = vo.scene_id
			boss_info.level = vo.level
			boss_info.big_icon = boss_data.small_icon
			boss_info.scale = vo.scale
			boss_info.layer = i - 1
			boss_info.view_resouce = boss_info.boss_id
			table.insert(self.sg_boss_all_list[i], boss_info)
			self.sg_boss_list[vo.boss_id] = boss_info
		end
		-- boss
		local shanggu_boss_info = self.sgyj_cfg.boss
		for i = 1, #shanggu_boss_info do
			local vo = {}
			self:ResetIndexBossCfg(vo, shanggu_boss_info[i])
			if type(vo.drop_item_list) ~= "table" then
				vo.is_item_list = false
				vo.drop_item_list = Split(vo.drop_item_list, "|")
			else
				vo.is_item_list = true
				vo.drop_item_list = self:AddOpenDayItemList(vo, self.reset_sgyj_bosslist)
			end
			--vo.drop_item_list = Split( shanggu_boss_info[i].drop_item_list, "|")
			local boss_data = self:GetMonsterInfo(shanggu_boss_info[i].boss_id)
			vo.boss_level = boss_data.level
			vo.boss_name = boss_data.name
			vo.x_pos = vo.born_pos_x
			vo.y_pos = vo.born_pos_y
			vo.type = BossWGData.MonsterType.Boss
			vo.min_delta_level = COMMON_CONSTS.MaxRoleLevel--无击杀限制
			vo.max_delta_level = COMMON_CONSTS.MaxRoleLevel--无击杀限制
			vo.view_resouce = vo.boss_id
			vo.big_icon = boss_data.small_icon
			table.insert(self.sg_boss_all_list[vo.layer + 1], vo)
			self.sg_boss_list[vo.boss_id] = vo
			self.new_cross_boss_list[vo.boss_id] = vo
		end
	end
	return self.sg_boss_all_list
end

function BossWGData:GetSGAllBossByLayer(layer)
	local sg_boss_list = self:GetSGAllBoss()
	return sg_boss_list[layer]
end

function BossWGData:GetSGAllBossByBossId(boss_id)
	if next(self.sg_boss_list) == nil then
		self:GetSGAllBoss()
	end
	return self.sg_boss_list[boss_id]
end

-- 服务端数据--2019.11.1跨服本服混搭旧需求已删除
function BossWGData:SetSgBossAllInfo(protocol)
	self.sg_next_refresh_time = protocol.next_refresh_time
	self.sg_next_box_time = protocol.box_reflush_time
	local layer_list = protocol.layer_list

	for i = 1, GameEnum.SGYJ_LAYER_NUM do
		for k,v in pairs(layer_list[i].boss_info_list) do
			if v.boss_id ~= 0 then
				local vo = v
				local time = v.is_exist == 1 and 0 or self.sg_next_refresh_time
				vo.next_refresh_time = time
				vo.kill_vo = {}
				if vo.last_killer_name ~= "" then
					table.insert(vo.kill_vo, {last_killer_name = v.last_killer_name})
				end
				self:SetAllBossInfo(v.boss_id, vo)
			end
		end
		self.sg_scene_info[i].role_num = layer_list[i].role_num
		self.sg_scene_info[i].boss_count = layer_list[i].boss_count
		self.sg_scene_info[i].box_count = layer_list[i].box_count
		self.sg_scene_info[i].hidden_boss_id_list = layer_list[i].hidden_boss_id_list
	end
end

function BossWGData:GetSGBoxFlushTime()
	return self.sg_next_box_time or 0
end

function BossWGData:GetSgEnterRoleNum(index)
	if not self.sg_scene_info then return 0 end
	return self.sg_scene_info[index] and self.sg_scene_info[index].role_num or 0
end

function BossWGData:GetSGBossInfoByBossId( boss_id )
	if not boss_id then return end
	return self:GetBossRefreshInfoByBossId(boss_id)
end

function BossWGData:SetSgBossLayer(protocol)
	self.sg_next_refresh_time = protocol.next_refresh_time
	self.sg_next_box_time = protocol.box_reflush_time
	for k,v in pairs(protocol.boss_list) do
		local vo = v
		local time = v.is_exist == 1 and 0 or self.sg_next_refresh_time
		vo.next_refresh_time = time
		self:SetAllBossInfo(v.boss_id, vo)
	end

	local layer = self:GetCurSgLayerBySceneId()
	layer = layer and layer + 1 or 0
	if self.sg_scene_info[layer] and self.sg_scene_info[layer].box_count then
		self.sg_scene_info[layer].box_count[1] = protocol.box_count[1]
		self.sg_scene_info[layer].box_count[2] = protocol.box_count[2]
		self.sg_scene_info[layer].hidden_boss_id_list = protocol.hidden_boss_id_list
	end
end

function BossWGData:GetSgBossTire()
	local shanggu_cfg = self:GetSGOtherCfg()
	local vip_cfg = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.SG_TIRE_TIMES)
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local vip_time = vip_cfg["param_" .. vip_level]
	return self.sg_tire_value, shanggu_cfg.reward_time + vip_time
end

function BossWGData:GetSgBossEnterTimes()
	local shanggu_cfg = self:GetSGOtherCfg()
	local vip_cfg = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.SG_ENTER_TIMES)
	local vip_level = VipWGData.Instance:GetRoleVipLevel()
	local vip_time = vip_cfg["param_" .. vip_level]
	return self.sg_enter_times, shanggu_cfg.enter_time + vip_time
end

function BossWGData:GetSGBossEnterComsun()
	local enter_cfg = self.sgyj_cfg.ticket_consume
	local enter_times = self:GetSgBossEnterTimes()
	for k, v in pairs(enter_cfg) do
		if enter_times + 1 == v.enter_time then
			return v.consume_count
		end
	end
	return enter_cfg[#enter_cfg].consume_count
end

function BossWGData:GetSGBossTikyId()
	local other = self:GetSGOtherCfg()
	return other.boss_ticket
end

function BossWGData:SetShangGuBossSceneOtherInfo(protocol)
	self.sg_tire_value = protocol.reward_count
	self.sg_enter_times = protocol.enter_count
	self.sg_concern_list = protocol.concern_list
	for i = 1, GameEnum.SGYJ_LAYER_NUM do
		local flag = self.sg_concern_list[i]
		local boss_cfg = self.sgyj_cfg.boss

		for _, v in pairs(boss_cfg) do
			if v.layer == i - 1 then
				local vo = {}
				vo.is_concern = bit:_and(flag, bit:_lshift(1, v.boss_index)) ~= 0 and 1 or 0
				self:SetAllBossInfo(v.boss_id, vo)
			end
		end
	end
end

function BossWGData:GetShangGuBossSceneOtherInfo(layer, type)
	local sg_other_cfg = self.sg_scene_info[layer]
	if IsEmptyTable(sg_other_cfg) then
		return 0
	end

	if BossWGData.MonsterType.Boss == type then
		return 1
	elseif BossWGData.MonsterType.Monster == type then
		return sg_other_cfg.gold_monster_num or 0
	elseif BossWGData.MonsterType.Gather == type then
		return sg_other_cfg.box_count and
				(sg_other_cfg.box_count[1] + sg_other_cfg.box_count[2]) or 0
	elseif BossWGData.MonsterType.HideBoss == type then
		local count = 0
		local list = sg_other_cfg.hidden_boss_id_list or {}
		for i, v in pairs(list) do
			if v > 0 then
				count = count + 1
			end
		end
		return count
	end
end

function BossWGData:GetSGBoxNum(layer)
	local sg_other_cfg = self.sg_scene_info and self.sg_scene_info[layer]
	if sg_other_cfg then
		return sg_other_cfg.box_count[2], sg_other_cfg.box_count[1]
	else
		return 0,0
	end
end

function BossWGData:GetSGBossFlushTimeStr()
	local time_cfg = self.sgyj_cfg.boss_reflush_time
	local time_t = {}
	for k, v in ipairs(time_cfg) do
		local time = v.reflush_time / 100
		table.insert(time_t, time)
	end
	return time_t
end

function BossWGData:GetSGBossAngryById(boss_id)
	if next(self.sg_angry_list) == nil then
		local shanggu_boss_info = self.sgyj_cfg.boss
		for i, v in ipairs(shanggu_boss_info) do
			self.sg_angry_list[v.boss_id] = v
		end
		local hide_boss_info = self.sgyj_cfg.hidden_boss
		for i, v in ipairs(hide_boss_info) do
			self.sg_angry_list[v.boss_id] = v
		end
	end
	return self.sg_angry_list[boss_id]
end

function BossWGData:GetSGBossListBySceneId(scene_id)
	if next(self.sg_boss_list) == nil then
		self:GetSGAllBoss()
	end
	local t_list = {}
	for k, v in pairs(self.sg_boss_list) do
		if v.scene_id == scene_id  and v.type ~= BossWGData.MonsterType.HideBoss then-- 又又又要加上采集物
			table.insert(t_list, v)
		end
	end

	local sg_layer = self:GetCurSgLayerBySceneId(scene_id)
	local hide_num = self:GetShangGuBossSceneOtherInfo(sg_layer + 1, BossWGData.MonsterType.HideBoss)

	local hide_boss = {}
	hide_boss.boss_id = -1
	hide_boss.boss_name = Language.Boss.HideBoss
	hide_boss.type = BossWGData.MonsterType.HideBoss
	hide_boss.boss_level = 2
	hide_boss.layer = sg_layer
	hide_boss.left_num = hide_num
	table.insert(t_list, hide_boss)

	table.sort(t_list, function (a, b)
		return a.boss_level < b.boss_level
	end)

	return t_list
end

function BossWGData:GetIsShowSGVipTip()
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	local next_enter_times = 0
	local next_boss_times = 0
	local next_vip_level = 0

	local enter_config = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.SG_ENTER_TIMES)
	local tire_config = VipWGData.Instance:GetVipSpecPermissions(VIP_LEVEL_AUTH_TYPE.SG_TIRE_TIMES)
	local enter_times = enter_config["param_" .. role_vip_level]
	local boss_times = tire_config["param_" .. role_vip_level]
	local param_level


	for i = role_vip_level, COMMON_CONSTS.MAX_VIP_LEVEL do
		param_level = "param_" .. i

		if enter_config[param_level] and enter_times < enter_config[param_level] then
			next_enter_times = enter_config[param_level]
		end

		if tire_config[param_level] and boss_times < tire_config[param_level] then
			next_boss_times = tire_config[param_level]
		end

		if next_enter_times ~= 0 or next_boss_times ~= 0 then
			next_vip_level = i
            break
		end
	end

    next_enter_times = next_enter_times == 0 and enter_times or next_enter_times
    next_boss_times = next_boss_times == 0 and boss_times or next_boss_times

	return next_enter_times, next_boss_times, next_vip_level
end

function BossWGData:GetHideMossByLayer(layer)
	layer = layer or 0
	local layer_cfg = self:GetSgLayerCfg(layer)
	if not layer_cfg then
		return
	end

	local sg_other_cfg = self.sg_scene_info and self.sg_scene_info[layer+1] or {}
	local hide_boss_id_list = sg_other_cfg.hidden_boss_id_list or {}
	local scene_id = layer_cfg.scene_id
	local hide_cfg = self.sgyj_cfg.hidden_boss
	local list = {}
	local hide_num_list = {}
	local boss_lv_seq = 0
	hide_num_list[scene_id] = {}
	local cache_boss_id = 0
	for i, v in ipairs(hide_cfg) do
		for _, hide_boss_id in pairs(hide_boss_id_list) do
			if hide_boss_id == v.boss_id and cache_boss_id ~= hide_boss_id then
				cache_boss_id = hide_boss_id
				hide_num_list[scene_id][v.level] = hide_num_list[scene_id][v.level] and hide_num_list[scene_id][v.level] + 1 or 1
			end
		end

		if v.scene_id == scene_id and boss_lv_seq ~= v.level then
			boss_lv_seq = v.level
			local t = {}
			t.layer = layer
			t.boss_lv_seq = v.level
			t.boss_id = v.boss_id
			t.scene_id = v.scene_id
			t.type = BossWGData.MonsterType.HideBoss
            if type(t.drop_item_list) ~= "table" then
                t.is_item_list = false
                t.drop_item_list = Split(v.drop_item_list, "|")
            else
                t.is_item_list = true
				t.drop_item_list = self:AddOpenDayItemList(v)
            end
			--t.drop_item_list = Split(v.drop_item_list, "|")
			t.view_resouce = v.view_resouce or v.boss_id
			t.scale = v.scale
			table.insert(list, t)
		end
	end

	for i, v in pairs(list) do
		local t = hide_num_list[v.scene_id]
		v.num = t and t[v.boss_lv_seq]
	end
	return list
end

function BossWGData:GetSGYJLimitLayer(btn_list)
	local sgyj_btn_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	for i, v in ipairs(self.sgyj_cfg.scene) do
		if role_level >= v.enter_level_limit then
			table.insert(sgyj_btn_list, {name = btn_list[i]})
		else
			table.insert(sgyj_btn_list, {name = btn_list[i], limit = v.enter_level_limit})
			break
		end
	end
	return sgyj_btn_list
end

------------   精英BOSS   ------------
-----------2019.12.12删除旧代码-----------
------------打宝地图------------
------------野外BOSS------------
------------普通阵营夺宝----------
------------师徒boss----------
------------仙童boss----------
------------蛮荒boss----------
------------飞仙boss---------
------------------------------------
function BossWGData:GetCrossBossList()
	local temp_cfg = __TableCopy(self.crossboss_list)
	return temp_cfg
end

function BossWGData:GetKfLayerIsEnter(layer)
	local level = RoleWGData.Instance.role_vo.level
	if not self.kf_boss_cfg.layer_cfg[layer] then return true, nil end
	local temp = self.kf_boss_cfg.layer_cfg[layer].level_limit
	return level >= temp,temp
end

function BossWGData:GetKfLayerCfgByLayer(layer)
    return self.kf_boss_cfg.layer_cfg[layer]
end

function BossWGData:GetKfLayerLevel()
	local layer = #self.kf_boss_cfg.layer_cfg
	local level = RoleWGData.Instance:GetRoleLevel()
	for i,v in ipairs(self.kf_boss_cfg.layer_cfg) do
		if level < v.level_limit then
			return v.layer_index - 1 > 0 and v.layer_index - 1 or 1
		end
	end
	return layer
end

function BossWGData:GetSGLayerLevel()
	local layer = 1
	local level = RoleWGData.Instance:GetRoleLevel()
	for i,v in ipairs(self.sgyj_cfg.scene) do
		if level > v.enter_level_limit then
			layer = v.layer+1
		else
			break
		end
	end
	return layer
end

function BossWGData:GetIsKfBossScene(scene_id)
	local cfg = self.crossboss_list
	for k,v in pairs(cfg) do
		if v.scene_id == scene_id then
			return v
		end
	end
	return nil
end

--------------------------------洞窟-----------------------------------
--2019.12.12删除一片旧洞窟代码

-- 根据当前层获取打宝配置
function BossWGData:GetDabaoCfgByLayer(cur_layer)
	local dabaoboss_list = {}
	for k, v in pairs(self.dabaoboss_list) do
		if v.level == cur_layer then
			table.insert(dabaoboss_list, v)
		end
	end
	return dabaoboss_list
end

-- 设置当前查询boss数量
function BossWGData:SetRecordCurLayerBossInfo(protocol)
	local count = 0
	for k,v in pairs(protocol.boss_list) do
		if 0 == v.next_refresh_time then
			count = count + 1
		end
	end
	self.cur_boss_num = count
end

-- 获取当前记录的boss数量
function BossWGData:GetRecordCurLayerBossNum()
	return self.cur_boss_num or 0
end


--------------召唤boss 2019.12.12已删除

function BossWGData:SetAllBossInfo(boss_id, vo)
	if not self.all_boss_info[boss_id] then
		self.all_boss_info[boss_id] = {}
	end
	vo = vo or {}
	for i, v in pairs(vo) do
		self.all_boss_info[boss_id][i] = v
	end
end

function BossWGData:SetAllBossActiveFlagInfo(monster_seq)
	if monster_seq ~= nil and self.tab ~= nil and self.tab[monster_seq] then
		return self.tab[monster_seq]
	else
		return  {
				can_active = 0,
				has_active = 0,
			}
	end
end

-- 格式化菜单数据
function BossWGData:FormatMenuCfg()
	if self.menu_list ~= nil then
		return self.menu_list
	end
	local bosscardcfg_auto = ConfigManager.Instance:GetAutoConfig("bosscardcfg_auto")
	local bosscard_cfg = {}
	for k,v in ipairs(bosscardcfg_auto.bosscard_cfg)do
		bosscard_cfg[v.monster_id] = v
	end
	local boss_cfg  =  ConfigManager.Instance:GetAutoConfig("bosscardcfg_auto").bosscard_cfg
	self.bosstujian_len = boss_cfg[#boss_cfg].monster_seq
	self.menu_list = {}
	for i = 1, #bosscardcfg_auto.bosstype_cfg do
		local item = bosscardcfg_auto.bosstype_cfg[i]
		local menu = self.menu_list[item.scene_type]
		local boss_id_list = Split(item.boss_id, "|")
		if nil == menu then
			self.menu_list[item.scene_type] = {}
			self.menu_list[item.scene_type].scene_type = item.scene_type
			self.menu_list[item.scene_type].map_type = item.map_type
			self.menu_list[item.scene_type].child = {}
		end

		local boss_list = {}
		for k,v in ipairs(boss_id_list)do
			if bosscard_cfg[tonumber(v)] ~= nil then
				local data = {}
				data.list = bosscard_cfg[tonumber(v)]
				data.map  = item.map_name
				data.open_level = item.open_level
				data.map_type  = item.map_type
				data.scene_type  = item.scene_type
				data.scene_id = item.scene_id
				table.insert(boss_list, data)
			end
		end
		table.insert(self.menu_list[item.scene_type].child, {bossid_id = boss_list, name=item.map_name,
		map_id = item.map_type,map_icon = item.icon_id,open_level = item.open_level})
	end
	return self.menu_list
end

function BossWGData:SetBossAllInfo(scence_type,map_type)
	local list = self:FormatMenuCfg()
	local cfg  = list[scence_type]
	local bosscardcfg_auto = ConfigManager.Instance:GetAutoConfig("bosscardcfg_auto")
	for i, v in ipairs(cfg.child) do
		if v.map_id == map_type then
			return v.bossid_id
		end
	end
end

function BossWGData:FormatMenu()
	local list = self:FormatMenuCfg()
	if IsEmptyTable(list) then
		return
	end

	local cfg = __TableCopy(list)
	local flag_info = nil
	local active_num = 0
	for i = 1 , #cfg do
		cfg[i].can_activef = 0
		for k,v in ipairs(cfg[i].child) do
			v.can_active = 0
			active_num = 0
			for _, boss_data in ipairs(v.bossid_id)do
				flag_info = self:SetAllBossActiveFlagInfo(boss_data.list.monster_seq)
				if flag_info then
					boss_data.can_active  = flag_info.can_active
					boss_data.has_active = flag_info.has_active
				end

				-- if boss_data.can_active == 1 and (boss_data.has_active == 0 or boss_data.has_active == 1) then
				-- 	active_num = active_num + 1
				-- end
				if boss_data.has_active == 1 then
					active_num = active_num + 1
				end
				if boss_data.can_active == 1 and boss_data.has_active == 0  then
					v.can_active = 1
					cfg[i].can_activef = 1
				end
			end
			v.progress = math.ceil((active_num / #v.bossid_id)*100)
		end
	end
	return cfg
end

function BossWGData:GetBossCanGetItem()
	local boss_list = self:FormatMenu()
	local boss_cfg  = __TableCopy(boss_list)
	for k,v in ipairs(boss_cfg) do
		if v.can_activef == 1 then
			self.item_index = k
			return k
		end
	end
	return self.item_index
end

function BossWGData:GetBossTujianRemind()
	local boss_list = self:FormatMenu()
	 local boss_cfg  = boss_list
	 for k,v in ipairs(boss_cfg) do
	 	if v.can_activef == 1 then
	 		return 1
	 	end
	 end
	 return 0
end

function BossWGData:GetBossCanGetleftItem()
	local boss_list = self:FormatMenu()
	local boss_cfg  = TableCopy(boss_list)
	if IsEmptyTable(boss_cfg) then
		return
	end

	for  i = 1 , #boss_cfg do
		for k,v in ipairs(boss_cfg[i].child) do
			if v.can_active == 1 then
				self.client_sort =  k
				return  k
			end
		end
	end
	return self.client_sort
end



---------------------------圣域boss-------------------------------------------------
--圣域BOSS疲劳值
function BossWGData:SacredBossTireInfo(protocol)
	if protocol == nil then
		return self.boss_tire
	end
	self.boss_tire = protocol.boss_tire
end

function BossWGData:GetSacreBossTire()
	return self.boss_tire
end

function BossWGData:SetSacredBossSceneInfo(protocol)
	self:ClearShengyuCache()
	self:SetShengyuBossBossLayer(protocol.layer)
	self.left_monster_count = protocol.left_monster_count 	-- 剩余小怪数量
	self.shengyu_boss_list = protocol.boss_info
end

function BossWGData:GetSacredBossLeftMonsterCount()
	return self.left_monster_count or 0
end

function BossWGData:SetSacredBossBossKillRecord(protocol)
	self.shengyu_killer_record = {}
	for i = 1, protocol.record_count do
		local killer_vo = {}
		killer_vo.last_killer_name = protocol.killer_record_list[i].killer_name
		killer_vo.kill_boss_time = protocol.killer_record_list[i].dead_timestamp
		killer_vo.last_killer_uid = protocol.killer_record_list[i].uuid
		self.shengyu_killer_record[i] = killer_vo
	end
end

function BossWGData:SetSacredBossDropRecord(protocol)
	self.shengyu_boss_record = protocol.dorp_record_list
end

function BossWGData:GetNextFlushSacredBossTurnTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local open_time_cfg = self:GetSacredCfg().client_activity_open_time
	if open_time_cfg == nil or IsEmptyTable(open_time_cfg) then
		return nil
	end

	local len = #open_time_cfg
	local return_time = nil
	for i = 1, len - 1 do
		local str_per = string.format("%02d:%02d",open_time_cfg[i].activity_start_time / 100, open_time_cfg[i].activity_start_time % 100)
		local tmp_per = TimeToSecond(str_per)	--今天的时间戳

		local str_next = string.format("%02d:%02d",open_time_cfg[i + 1].activity_start_time / 100, open_time_cfg[i + 1].activity_start_time % 100)
		local tmp_next = TimeToSecond(str_next)	--今天的时间戳
		-- print_error(os.date("%H:%M", tmp_per),os.date("%H:%M", tmp_next),os.date("%H:%M", server_time))
		if server_time < tmp_per then
			return_time = os.date("%H:%M", tmp_per)
			break
		elseif server_time > tmp_per and server_time < tmp_next then
			return_time = os.date("%H:%M", tmp_next)
			break
		end
	end
	if return_time == nil then
		return_time = string.format("%02d:%02d",open_time_cfg[1].activity_start_time / 100, open_time_cfg[1].activity_start_time % 100)
	end
	-- print_error("return",return_time)
	return return_time
end

function BossWGData:GetShengYuBossTire()
	local daily_count = self:GetSacredCfg().other[1].daily_boss_tire
	local cur_count = self:SacredBossTireInfo() or 0
	-- local color = cur_count >= daily_count and COLOR3B.RED or COLOR3B.WHITE
	local str = string.format("%s/%s",cur_count, daily_count)
	return str
end

function BossWGData:GetSacredCfg()
	if nil == self.m_sacred_boss_auto then
		self.m_sacred_boss_auto = ConfigManager.Instance:GetAutoConfig("sacred_boss_auto")
	end
	return self.m_sacred_boss_auto

end

function BossWGData:GetSacredBossCfgList()
	return self:GetSacredCfg().layer_cfg
end

--圣域boss屏蔽中，所以这个tablecopy暂不处理
function BossWGData:GetSacredBossInfo(boss_id)
	local cfg = self:GetSacredCfg().boss_cfg
	for k,v in pairs(cfg) do
		if boss_id == v.boss_id then
			local boss_vo = __TableCopy(v)
			local boss_data = self:GetMonsterInfo(boss_id)
			boss_vo.boss_level = boss_data.level
			boss_vo.boss_name = boss_data.name
			return boss_vo
		end
	end
end

function BossWGData:GetSacredBossCfgByBossId(boss_id)
	local cfg = self:GetSacredCfg().boss_cfg
	local boss_vo = nil
	for k,v in pairs(cfg) do
		if boss_id == v.boss_id then
			boss_vo = {}
			local boss_data = self:GetMonsterInfo(boss_id)
			boss_vo.boss_id = boss_id
			boss_vo.name = boss_data.name
			boss_vo.level = boss_data.level
			boss_vo.x_pos = v.flush_pos_x
			boss_vo.y_pos = v.flush_pos_y
			break
		end
	end
	return boss_vo
end

function BossWGData:GetSacredBossAllCfgByBossId( boss_id )
	local cfg = self:GetSacredCfg().boss_cfg
	local boss_vo = nil
	for k,v in pairs(cfg) do
		if boss_id == v.boss_id then
			boss_vo = {}
			local boss_data = self:GetMonsterInfo(boss_id)
			boss_vo.boss_id = boss_id
			boss_vo.boss_name = boss_data.name
			boss_vo.boss_level = boss_data.level
			boss_vo.x_pos = v.flush_pos_x
			boss_vo.y_pos = v.flush_pos_y
			boss_vo.scene_id = v.scene_id
			boss_vo.type = BossWGData.MonsterType.Boss
			break
		end
	end
	return boss_vo
end

function BossWGData:GetBossOfferCfg(boss_type, boss_id)
	local boss_cfg = {}
	local boss_data = {}
	local boss_vo = {}
	local boss_cfg1 = {}
	local is_open
	local need_role_level = 0
	if boss_type == BossWGData.BossOfferType.WORLD_BOSS then
		is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_world")
		boss_cfg1 = self.worldboss_id_list --ListToMap(self.worldboss_list, "boss_id")
		need_role_level = self:GetEnterBossCfgbySceneId(boss_cfg1[boss_id].scene_id)

	elseif boss_type == BossWGData.BossOfferType.DABAO_BOSS then
		is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_dabao")
		boss_cfg1 = self.dabaoboss_id_list --ListToMap(self.dabaoboss_list, "boss_id")
		need_role_level = self:GetEnterBossCfgbySceneId(boss_cfg1[boss_id].scene_id)

	elseif boss_type == BossWGData.BossOfferType.VIP_BOSS then
		is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_vip")
		boss_cfg1 = self.vipboss_id_list --ListToMap(self.vipboss_list, "boss_id")
		need_role_level = self:GetEnterBossCfgbySceneId(boss_cfg1[boss_id].scene_id)

	elseif boss_type == BossWGData.BossOfferType.PERSON_BOSS then
		is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_personal")
		boss_cfg1 = self.personalboss_id_list--ListToMap(self.personal_cfg.boss_scene_cfg, "boss_id")
		need_role_level = self:GetEnterBossCfgbySceneId(boss_cfg1[boss_id].scene_id)

	elseif boss_type == BossWGData.BossOfferType.SG_BOSS then
		is_open = FunOpen.Instance:GetFunIsOpenedByTabName("worserv_boss_sgyj")
		--boss_cfg1 = ListToMap(self.sgyj_cfg.boss, "boss_id")
		--need_role_level = self:GetEnterBossCfgbySceneId(boss_cfg1[boss_id].scene_id)
	end

	boss_data = self:GetMonsterInfo(boss_id)
	boss_vo.boss_level = boss_data.level
	boss_vo.boss_name = boss_data.name
	boss_vo.big_icon = boss_data.small_icon
	boss_vo.boss_id = boss_id
	boss_vo.is_open = is_open
	boss_vo.need_role_level = need_role_level
	table.insert(boss_cfg, boss_vo)
	return boss_cfg
end

function BossWGData:GetEnterBossCfgbySceneId(scene_id)
	local world_enter_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").world_boss_enter_cfg
	for i,v in ipairs(world_enter_cfg) do
		if scene_id == v.scene_id then
			return v.need_role_level
		end
	end

	local mj_boss_enter_cfg = ConfigManager.Instance:GetAutoConfig("secret_boss_auto").enter_cfg
	for i,v in ipairs(mj_boss_enter_cfg) do
		if scene_id == v.scene_id then
			return v.need_role_level
		end
	end

	local dabao_boss_enter_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").dabao_boss_enter_cfg
	for i,v in ipairs(dabao_boss_enter_cfg) do
		if scene_id == v.scene_id then
			return v.need_role_level
		end
	end

	local per_boss_enter_cfg = ConfigManager.Instance:GetAutoConfig("personboss_auto").boss_scene_cfg
	for i,v in ipairs(per_boss_enter_cfg) do
		if scene_id == v.scene_id then
			return v.need_level
		end
	end

	local vip_boss_enter_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").vipBoss_enter_cfg
	for i,v in ipairs(vip_boss_enter_cfg) do
		if scene_id == v.scene_id then
			return v.need_role_level
		end
	end
end

function BossWGData:GetSacredBossCfg()
	local shengyu_boss_list = self:GetShengyuBossBossList()
	local sacred_boss_data = nil
	if shengyu_boss_list and not IsEmptyTable(shengyu_boss_list) then
		sacred_boss_data = {}
		for k,v in pairs(shengyu_boss_list) do
			local tmp = self:GetSacredBossCfgByBossId(v.boss_id)
			-- local replace_tmp = self.crossboss_monster_list[v.replace_boss_id]
			if tmp then
				tmp.is_exist = v.is_exist
				-- tmp.name = replace_tmp.name
				-- tmp.level = replace_tmp.level
				tmp.boss_id = v.replace_boss_id
				table.insert(sacred_boss_data, tmp)
			end
		end
	end
	return sacred_boss_data
end

function BossWGData:GetSacredBossInfoList()
	local sacred_boss_data = self:GetSacredBossCfg()
	local monster_count = self:GetSacredBossLeftMonsterCount()
	local finally_sacred_boss_data = {}
	if sacred_boss_data == nil then
		sacred_boss_data = {}
	end
	if IsEmptyTable(sacred_boss_data) then
		--table.insert(sacred_boss_data, {boss_id = 0, monster_count = monster_count, is_exist = nil})
		table.insert(finally_sacred_boss_data, {boss_id = 0, monster_count = monster_count, is_exist = nil})
	else
		finally_sacred_boss_data[1] = {boss_id = 0, monster_count = monster_count, is_exist = nil}
		for k,v in pairs(sacred_boss_data) do
			table.insert(finally_sacred_boss_data,v)
		end
	end
	--return sacred_boss_data
	return finally_sacred_boss_data
end

function BossWGData:GetSacredBossOtherAuto()
	return self:GetSacredCfg().other[1]
end

function BossWGData:SetShenYuIndex(index)
	self.shen_yu_index = index
end
function BossWGData:GetShenYuIndex()
	return self.shen_yu_index
end

function BossWGData:GetShengyuBossBossKillRecord()
	return self.shengyu_killer_record
end

function BossWGData:GetShengyuBossBossList()
	return self.shengyu_boss_list
end

function BossWGData:SetShengyuBossBossLayer(layer)
	self.shengyu_layer = layer
end

function BossWGData:GetShengyuBossBossLayer()
	return self.shengyu_layer
end

function BossWGData:SetSacredBossOwnBossInfoChange(protocol)
	self.shengyu_monster_hp_per = protocol.monster_hp_per
	self.hurter_list = protocol.hurter_list
end

function BossWGData:GetSacredBossOwnBossInfoChange()
	local hurt_list = {}
	for i=1,3 do
		hurt_list[i] = {}
		hurt_list[i].index = i
		hurt_list[i].hurt_per = 0
		hurt_list[i].user_name = ""
	end
	if self.hurter_list then
		for k,v in pairs(self.hurter_list) do
			if v.index == hurt_list[k].index then
				hurt_list[k] = v
			end
		end
	end
	table.sort(hurt_list, SortTools.KeyUpperSorters("hurt_per"))
	return hurt_list
end

function BossWGData:GetSacredBossOwnBossHpChange()
	return self.shengyu_monster_hp_per
end

-- 超过boss等级
function BossWGData:SetExceedBossLevel(level)
	self.exceed_boss_level = level
end

function BossWGData:GetExceedBossLevel()
	return self.exceed_boss_level or 0
end

--获取圣域boss活动状态
function BossWGData:GetShengYuBossState()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KUAFUSACREDBOSS)
	if act_info == nil then
		return false
	end
	return act_info.status == ACTIVITY_STATUS.OPEN
end

--------------------------------秘境boss--------------------------------------------------
function BossWGData:GetMiJingBossListByIndex( index )
	local boss_list = self:GetMiJingBossList()
	return boss_list[index]
end

--秘境boss屏蔽种
function BossWGData:GetMiJingBossList()
	if next(self.mj_boss_list) == nil then
		local len = #self.mj_boss_cfg.enter_cfg
		local mj_boss = self.mj_boss_cfg.layer_cfg
		for i=1,len do
			self.mj_boss_list[i] = {}
		end
		for i=1, #mj_boss do
			if mj_boss[i] then
				local j = mj_boss[i].level
				local boss_vo = __TableCopy(mj_boss[i])
				boss_vo.drop_item_list = Split(mj_boss[i].drop_item_list,"|")
				local boss_data = self:GetMonsterInfo(mj_boss[i].boss_id)
				boss_vo.boss_level = boss_data.level
				boss_vo.boss_name = boss_data.name
				boss_vo.boss_hp = boss_data.hp
				boss_vo.boss_atk = boss_data.gongji
				boss_vo.boss_defen = boss_data.fangyu
				boss_vo.boss_pojia = boss_data.pojia
				boss_vo.damage_type = boss_data.damage_type
				boss_vo.boss_magdef = boss_data.fa_fangyu
				table.insert(self.mj_boss_list[j],boss_vo)
			end
		end
	end
	return self.mj_boss_list
end

function BossWGData:GetMiJingTime()
	local time = TimeWGCtrl.Instance:GetServerTime()
	if time == nil then return false end
	local str = os.date("*t",time)
	local str_time = tonumber(string.format("%02d%02d",str.hour,str.min))
	for k,v in ipairs(self.mj_boss_cfg.physical_get_time) do
		if str_time > v.get_start_time and str_time < v.get_end_time then
			return true
		end
	end
	return false
end

function BossWGData:GetNowRightTime()
	local is_right_time = self:GetMiJingTime()
	if IsEmptyTable(self.get_physical_flag) then return false end
	if self.mj_key == 2 and self.get_physical_flag[1] == 0 and self.get_physical_flag[2] ~= 0 and is_right_time then
		return true
	else
		return false
	end
end

function BossWGData:GetMiJingTimeShowCfg()
	local list = {}
	local cfg = self.mj_boss_cfg.physical_get_time
	for k,v in ipairs(cfg) do
		local temp = {}
		for i,j in pairs(v) do
			local hour = math.floor(j/100)
			local second = j % 100
			second = second == 0 and "00" or second
			table.insert(temp, hour)
			table.insert(temp, second)
		end
		table.insert(list,temp)
	end
	return list
end

function BossWGData:GetNowOwnPower()
	return self.own_power or 0
end

function BossWGData:GetCanGainPowerNum( )
	--读配置
	local get_num = self.mj_boss_cfg.other[1].free_get_physical
	local limit_num = self.mj_boss_cfg.other[1].max_physical
	return get_num,limit_num
end

function BossWGData:GetRemedyCost()
	local count = 0
	if IsEmptyTable(self.get_physical_flag) then return 0, 0 end
	for i=1,self.mj_key do
		if self.get_physical_flag[i] == 0 then
			count = count + 1
		end
	end

	local remedy_power = self.mj_boss_cfg.other[1].free_get_physical * count
	local remedy_cost = self.mj_boss_cfg.other[1].repair_physical_need_gold * count
	return remedy_cost,remedy_power
end

function BossWGData:GetAddCost()
	local remedy_power = self.mj_boss_cfg.other[1].buy_get_physical
	local remedy_cost = self.mj_boss_cfg.other[1].buy_physical_need_gold
	return remedy_cost,remedy_power
end

function BossWGData:GetRestExTimes()
	if self.mj_ex_times == nil then return end
	local role_vip = RoleWGData.Instance.role_vo.vip_level
	for i,v in ipairs(self.mj_boss_cfg.buy_physical_time) do
		if role_vip == v.vip_level then
			return v.buy_time - self.mj_ex_times
		end
	end
	return 0
end

function BossWGData:GetCrossMJBossByLayer( layer )
	local all_list = self:GetMiJingBossList()
	return all_list[layer]
end

function BossWGData:SetNowPhysicalInfo( protocol )
	self.own_power = protocol.physical
	self.mj_ex_times = protocol.buy_physical_times
	for i=1,2 do
		self.get_physical_flag[i] = bit:_and(protocol.get_physical_flag,bit:_lshift(1,i-1))
	end
end

function BossWGData:GetPhysicalFlag()
	return self.get_physical_flag
end

function BossWGData:GetNowRightFlag()
	return self.mj_key
end

function BossWGData:GetMiJingPower()
	local time = TimeWGCtrl.Instance:GetServerTime()
	if time == nil then return false end
	local str = os.date("*t",time)
	self.mj_key = 0
	local str_time = tonumber(string.format("%02d%02d",str.hour,str.min))
	local list = self.mj_boss_cfg.physical_get_time
	for i=#list,1,-1 do
		if str_time > list[i].get_start_time then
			self.mj_key = i
			break
		end
	end
	local flag = false
	if IsEmptyTable(self.get_physical_flag) then return flag end
	for i=1,self.mj_key do
		if 0 == self.get_physical_flag[i] then
			flag = true
			break
		end
	end
	return flag
end

function BossWGData:GetMiJingBtnList()
	if self.mj_btn_list then
		return self.mj_btn_list
	end
	local len = #self.mj_boss_cfg.enter_cfg
	self.mj_btn_list = {}
	for i=1,len do
		-- btn_list[i] = string.format(Language.FbWushuang.RankValue,Language.Common.UpNum[i])
		local vo = {}
		vo.name = string.format(Language.FbWushuang.RankValue,Language.Common.UpNum[i])
		vo.is_cross = self.mj_boss_cfg.enter_cfg[i].is_cross
		table.insert(self.mj_btn_list,vo)
	end
	self.mj_btn_list[1].name = Language.Boss.LayerBtnName3
	return self.mj_btn_list
end

function BossWGData:GetIsCrossByLayer( layer )
	return self.mj_btn_list[layer].is_cross
end

function BossWGData:GetMiJingVipByLayer()
	local mj_ex_times = self.mj_ex_times or 0
	local role_vip = RoleWGData.Instance.role_vo.vip_level
	for i,v in ipairs(self.mj_boss_cfg.buy_physical_time) do
		if v.buy_time == mj_ex_times + 1 then
			return v.vip_level
		end
	end
	return role_vip
end

function BossWGData:SetMiJingBossInfo( protocol )
	self.own_power = protocol.physical
	local mj_all_boss_list = protocol.layer_list
	for i=1,protocol.layer_count do
		for k,v in pairs(mj_all_boss_list[i].boss_list) do
			self.all_boss_info[v.boss_id] = v
		end
	end
	local no_cross_num = self:GetMiJingBossKFLayerNum()
	if not self.all_cave_boss_info then
		self.all_cave_boss_info = {}
	end
	if protocol.is_cross == 0 then
		for i=1,protocol.layer_count do
			self.all_cave_boss_info[i] = protocol.layer_list[i]
		end
	else
		for i=1,protocol.layer_count do
			self.all_cave_boss_info[i+no_cross_num] = protocol.layer_list[i]
		end
	end
	-- self.all_cave_boss_info = protocol.layer_list
	-- self.mijng_local_boss_info = true
end

function BossWGData:GetMiJingBossKFLayerNum()
	if not self.mj_boss_no_kf_layer_num then
		local count = 0
		for k,v in pairs(self.mj_boss_cfg.enter_cfg) do
			if v.is_cross == 0 then
				count = count + 1
			end
		end
		self.mj_boss_no_kf_layer_num = count
	end
	return self.mj_boss_no_kf_layer_num
end

function BossWGData:SetMIJiBossTiredByDaycount(tire_value)
	self.own_power = tire_value
end

function BossWGData:SetMiJingBossLayer( boss_list )
	local index = 0
	for k,v in pairs(boss_list) do
		self.all_boss_info[v.boss_id] = v
		index = index + 1
	end
end

function BossWGData:SetCrossSecretBossPalyerInfo(protocol)
	self.own_power = protocol.physical
	self.mj_concern_flag = {}
	for i=1,5 do
		local flag = bit:d2b(protocol.concern_flag[i])
		self.mj_concern_flag[i] = flag
	end
end

function BossWGData:SetCrossSecretBossSceneInfo(protocol)
	self.own_power = protocol.physical
	for k,v in pairs(protocol.boss_list) do
		local vo = {}
		vo.boss_id = v.boss_id
		vo.is_exist = v.is_exist
		vo.next_refresh_time = v.next_flush_time
		vo.last_kill_name = v.last_kill_name
		vo.belong_name = v.belong_name
		vo.is_near_death = v.is_near_death
		self.all_boss_info[v.boss_id] = vo
	end
end

function BossWGData:SetCrossSecretBossDropRecord(protocol)
	self.mj_boss_record = protocol.dorp_record_list
end

function BossWGData:GetMJBossDefaultLevel( level )
	level = level or RoleWGData.Instance:GetRoleLevel()
	for i=#self.mj_boss_cfg.enter_cfg,1,-1 do
		if level > self.mj_boss_cfg.enter_cfg[i].need_role_level then
			return self.mj_boss_cfg.enter_cfg[i].level
		end
	end
	return 1
end

function BossWGData:GetMJLayerCfg(layer)
	local level = RoleWGData.Instance.role_vo.level
	local temp = self.mj_boss_cfg.enter_cfg[layer].need_role_level
	return level >= temp,temp
end

function BossWGData:GetMJCrossLayer()
	-- return self.mj_boss_cfg.other[1].start_level - 1
	return #self.mj_boss_cfg.layer_cfg
end

function BossWGData:GetCurBossListBySceneId(scene_id)
	local all_boss_list = self:GetAllBossInfo()
	if IsEmptyTable(all_boss_list) then return end
	local boss_list = {}
	for k, v in pairs(all_boss_list) do
		if v.scene_id == scene_id then
		table.insert(boss_list, v)
		end
	end
	-- local sort_list = __TableCopy(boss_list)

	table.sort(boss_list, function (a, b)
		return a.boss_id < b.boss_id
	end)
	return boss_list
end

function BossWGData:SetCrossSecretBossBossKillRecord(protocol)
	self.mj_boss_kill_record = {}
	for i=1,protocol.record_count do
		local killer_vo = {}
		killer_vo.last_killer_name = protocol.killer_record_list[i].killer_name
		killer_vo.kill_boss_time = protocol.killer_record_list[i].dead_timestamp
		killer_vo.last_killer_uid = protocol.killer_record_list[i].uuid
		self.mj_boss_kill_record[i] = killer_vo
	end
end

function BossWGData:GetCrossSecretBossKillRecord()
	return self.mj_boss_kill_record
end

function BossWGData:GetSecretBossInfoByBossId( id )
	if id == nil then return end
	if not self.secert_boss_info then
		local cfg = ConfigManager.Instance:GetAutoConfig("secret_boss_auto").layer_cfg
		self.secert_boss_info = ListToMap(cfg,"boss_id")
	end
	return self.secert_boss_info[id]
end

function BossWGData:CanGetExPhysical()
	for i,v in ipairs(self.mj_boss_cfg.buy_physical_time) do
		if v.buy_time > 0 then
			return v.vip_level
		end
	end
end

function BossWGData:GetBossLayerById(boss_id)
	for k,v in pairs(self.all_boss_list) do
		if v.boss_id == boss_id then
			return v.level
		end
	end
	return 0
end

function BossWGData:GetSetLastSelectInfo(layer, index)
	if index ~= nil then
		self.last_select_layer = layer
		self.last_select_index = index
		return
	end
	local t = {}
	t.layer = self.last_select_layer
	t.index = self.last_select_index
	return t
end

function BossWGData:GetIsInBossScene(scene_id)
	local view_index = 0
	local param = {}
	local scene_type = Scene.Instance:GetSceneType()
	param = self:GetSetLastSelectInfo()

	if scene_type == SceneType.WorldBoss then
		view_index = BossViewIndex.WorldBoss
		--if self.last_world_boss_id == 0 then
		--	local _, temp = self:IsWorldBossScene(scene_id)
		--	param.open_boss_id = temp and temp.boss_id or 0
		--else
		--	param.open_boss_id = self.last_world_boss_id
		--end
	elseif scene_type == SceneType.VIP_BOSS then
		view_index = BossViewIndex.VipBoss
		--if self.last_vip_boss_id == 0 then
		--	local _, temp2 = self:GetIsBossVipScene(scene_id)
		--	param.open_boss_id = temp2 and temp2.boss_id or 0
		--else
		--	param.open_boss_id = self.last_vip_boss_id
		--end
	elseif scene_type == SceneType.KF_BOSS then
		view_index = TabIndex.worserv_boss_mh
		--if self.last_kfboss_id == 0 then
		--	local temp3 = self:GetIsKfBossScene(scene_id)
		--	param.open_boss_id = temp3 and temp3.boss_id or 0
		--else
		--	param.open_boss_id = self.last_kfboss_id
		--end
	elseif scene_type == SceneType.PERSON_BOSS then
		view_index = BossViewIndex.PersonalBoss
		--if self.last_perboss_id == 0 then
		--	local temp3 = self:GetIsKfBossScene(scene_id)
		--	param.open_boss_id = temp3 and temp3.boss_id or 0
		--else
		--	param.open_boss_id = self.last_kfboss_id
		--end
	elseif scene_type == SceneType.DABAO_BOSS then
		view_index = BossViewIndex.DabaoBoss
	elseif scene_type == SceneType.SG_BOSS then
		view_index = TabIndex.worserv_boss_sgyj
	elseif scene_type == SceneType.HONG_MENG_SHEN_YU then
        view_index = TabIndex.worserv_boss_hmsy
    elseif scene_type == SceneType.Shenyuan_boss then
		view_index = TabIndex.world_new_shenyuan_boss
	end
	param.view_index = view_index
	return param
end

---数据：
function BossWGData:GetDabaoBossAngry(boss_id, scene_type)
	local kill_add_angry = 0
	if SceneType.DABAO_BOSS == scene_type then
		kill_add_angry = self.other_cfg[1].dabao_angry_kill_monster_add
	elseif SceneType.SG_BOSS == scene_type then
		kill_add_angry = self:GetSGOtherCfg().monster_tire
	end
	return kill_add_angry
end

function BossWGData:GetWorldBossEnterLayerAndId()
	return self.last_world_boss_layer or 1,  self.last_world_boss_id or 0
end

function BossWGData:SetOldBossID(index, bossid_id,boss_layer)
    if index == BossViewIndex.WorldBoss then
        self.last_world_boss_layer = boss_layer
        self.last_world_boss_id = bossid_id
    elseif index == BossViewIndex.VipBoss then
        self.last_vip_boss_id = bossid_id
    elseif index == BossViewIndex.PersonalBoss then
        self.last_perboss_id = bossid_id
    elseif index == BossViewIndex.DabaoBoss then
        self.last_dabao_id = bossid_id
    elseif index == TabIndex.worserv_boss_sgyj + 100 then
        self.last_sgyj_id = bossid_id
    elseif index == TabIndex.worserv_boss_hmsy + 100 then
        self.last_hmsy_id = bossid_id
    elseif index == TabIndex.worserv_boss_mh + 100 then
        self.last_kfboss_id = bossid_id
	end
end

function BossWGData:SetBossEnterFlag(flag)
	self.is_open_to_boss = flag
end

function BossWGData:GetIsEnterInScene()
	return self.is_open_to_boss
end

function BossWGData:CheckPerOutSceneOpenViewLevel()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local per_other = self.personal_cfg.other[1]
	local out_open_level = per_other.out_open_level or per_other.limit_level

	return role_level >= out_open_level
end

function BossWGData:GetSetNoOpenViewFlag(flag)
	if flag then
		self.no_open_flag = flag
		return
	end
	local t_flag = self.no_open_flag
	self.no_open_flag = nil
	return t_flag
end

--------新增方法
function BossWGData:GetVipBossLevelByBossId(boss_id)
	if self.vipboss_list then
		for k,v in pairs(self.vipboss_list) do
			if v.boss_id == boss_id then
				return v.level
			end
		end
	end
	return 0
end

function BossWGData:GetVipBossCfgByBossId(boss_id)
	if boss_id ~=nil then
		return self.vipboss_id_list[boss_id]
	end
	return nil
end

--设置从图鉴跳转过来的索引(界面,层数,卡片索引)
function BossWGData:SetBossTuJianIndex(view_index, num_index, card_index, scene_type)
	self.view_index = view_index
	self.number_index = num_index
	self.card_index = card_index
end
--返回应该跳转的索引
function BossWGData:GetBossTuJianIndex()
	return self.view_index, self.number_index, self.card_index
end

function BossWGData:GetVipBossLayerLimit(btn_list)
	self.vip_btn_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local limit_count = 0
	for k,v in ipairs(self.worldboss_auto.vipBoss_enter_cfg) do
        local list = {}
        list.need_role_level = v.need_role_level
        if v.need_role_level > role_level then
            limit_count = limit_count + 1
        end
        list.level = v.level + 1
        list.name = btn_list[k]
        list.pay_vip_level = v.pay_vip_level
        list.free_vip_level = v.free_vip_level
        list.tuijian_role_level = v.tuijian_role_level
        list.boss_jie_min = v.boss_jie_min
        list.boss_jie_max = v.boss_jie_max
        if limit_count <= 1 then
            table.insert(self.vip_btn_list,list)
        end
	end
	return self.vip_btn_list
end

--过滤vip低等级层数
function BossWGData:GetNowVipIndexByLayer(layer)
    local role_level = RoleWGData.Instance:GetRoleLevel()
    -- for k,v in ipairs(self.worldboss_auto.vipBoss_enter_cfg) do
    --     if role_level > v.max_role_level_limit then
    --         layer = layer - 1
    --     else
    --         break
    --     end
    -- end
    return layer
end

function BossWGData:GetAllBossListBySceneId( scene_id )
	local all_boss_list = self:GetAllBossInfo()

	local boss_list = {}

	if not IS_ON_CROSSSERVER and Scene.Instance:GetSceneType() ~= SceneType.HONG_MENG_SHEN_YU then
		for k, v in pairs(all_boss_list) do
			if v.scene_id == scene_id and v.boss_level then--sort排序字段，添加判空
				table.insert(boss_list, v)
			end
		end
	else
		self:GetCrossAllBoss()
		self:GetSGAllBoss()
		self:GetHMSYBossListById(scene_id)

		for i, v in pairs(self.new_cross_boss_list) do
			if v.scene_id == scene_id and v.boss_level then--sort排序字段，添加判空
				table.insert(boss_list, v)
			end
		end
	end

	return boss_list
end

function BossWGData:AddToNewCrossBossList(boss_id, boss_info)
    self.new_cross_boss_list[boss_id] = boss_info
end

function BossWGData:GetCurSceneAllMonster( monster_list ,  monster_type)
	monster_type = monster_type or BossWGData.MonsterType.Boss
	local scene_id = Scene.Instance:GetSceneId()
	local temp_list = self:GetAllBossListBySceneId(scene_id)
	local boss_list = {}
	SortTools.SortAsc(temp_list, "boss_level")
	for k,v in ipairs(temp_list) do
		if v.type == nil or v.type == monster_type then
			local list = {}
			list.x = v.x_pos
			list.y = v.y_pos
			list.id = v.boss_id
			list.name = v.boss_name
			list.level = v.boss_level
			table.insert(boss_list,list)
		end
	end

	if monster_list then
		for k,v in ipairs(monster_list) do
			table.insert(boss_list,v)
		end
	end
	return boss_list
end

function BossWGData:GetCurSceneAllBoss()
	local scene_id = Scene.Instance:GetSceneId()
	local temp_list = self:GetAllBossListBySceneId(scene_id)
	local boss_list = {}
	SortTools.SortAsc(temp_list, "boss_level")
	for k,v in ipairs(temp_list) do
		if v.type == nil or v.type == BossWGData.MonsterType.Boss then
			local list = {}
			list.x = v.x_pos
			list.y = v.y_pos
			list.boss_id = v.boss_id
			list.name = v.boss_name
			table.insert(boss_list,list)
		end
	end

	return boss_list
end

function BossWGData.IsBossScene(scene_type)
	for k,v in pairs(BossWGData.SceneType) do
		if v == scene_type then
			return true
		end
	end
end

-- function BossWGData:GetSGAllMonsterList(monsters_list)
-- 	-- print_error(#monsters_list)

-- end

function BossWGData:GetSGMonsterList(monsters_list)
	local list = self:GetSGMonsterListMonster()
	local scene_id = Scene.Instance:GetSceneId()
	if monsters_list == nil then
		monsters_list = {}
	end
	for k,v in pairs(list) do
		if v.type == BossWGData.MonsterType.Monster and scene_id == v.scene_id then
			local cache = true
			for i,j in pairs(monsters_list) do
				if v.id == j.id then
					cache = false
					break
				end
			end
			if cache then
				local temp = {}
				temp.x = v.x_pos
				temp.y = v.y_pos
				temp.id = v.boss_id
				temp.level =v.boss_level
				-- local cfg = self.crossboss_monster_list[v.boss_id]
				temp.name = v.boss_name
				table.insert(monsters_list,temp)
			end
		end
	end
	SortTools.SortAsc(monsters_list,"level")
	return monsters_list
end

function BossWGData:GetShengYuAllMonsterList()
	if not self.map_shengyu_boss_cache then
		self.map_shengyu_boss_cache = true
		local shengyu_boss_list = self:GetShengyuBossBossList()
		if shengyu_boss_list and not IsEmptyTable(shengyu_boss_list) then
			for k,v in pairs(shengyu_boss_list) do
				local tmp = self:GetSacredBossAllCfgByBossId(v.boss_id)--替换boss
				-- if self.new_cross_boss_list[tmp.boss_id] ~= nil then
				-- 	print_error("重复boss_id",tmp.boss_id)
				-- end
				self.new_cross_boss_list[tmp.boss_id] = tmp
			end
		end
		local shengyu_layer_cfg = ListToMap(self:GetSacredBossCfgList(),"layer_index")
		local cfg = self:GetSacredCfg().monster_cfg
		for k,v in pairs(cfg) do
			local vo = {}
			vo.x_pos = v.pos_x
			vo.y_pos = v.pos_y
			vo.boss_id = v.monster_id
			vo.scene_id = shengyu_layer_cfg[v.layer].scene_id
			vo.monster_index = v.monster_index
			vo.boss_name = self.crossboss_monster_list[vo.boss_id].name
			vo.boss_level = self.crossboss_monster_list[vo.boss_id].level
			vo.type = BossWGData.MonsterType.Monster
			self.new_cross_boss_list[vo.boss_id] = vo
		end
	end
end

function BossWGData:ClearShengyuCache()
	self.map_shengyu_boss_cache = nil
	self.new_cross_cache = nil
end

function BossWGData:GetShenYunMonsterCfg()
	local syboss_cfg = self.shenyunboss_list
	if not self.shenyun_monster_cache then
		self.shenyun_monster_list = {}
		self.shenyun_monster_cache = true
		for i = 1, #syboss_cfg do
			local vo = {}
			vo.index = i
			vo.layer = syboss_cfg[i].level
			vo.boss_id = syboss_cfg[i].boss_id
			vo.x_pos = syboss_cfg[i].x_pos
			vo.y_pos = syboss_cfg[i].y_pos
			vo.boss_name = syboss_cfg[i].name
			local boss_data = self:GetSYBossAtt(syboss_cfg[i].boss_id)
			vo.scene_id = syboss_cfg[i].scene_id
			vo.type = BossWGData.MonsterType.Monster
			vo.boss_level = self.crossboss_monster_list[vo.boss_id].level
			table.insert(self.shenyun_monster_list, vo)
			self.new_cross_boss_list[vo.boss_id] = vo
		end
	end
end

function BossWGData:FilterMapShenYunMonster()
	local scene_id = Scene.Instance:GetSceneId()
	if not self.shenyun_monster_list then
		self:GetShenYunMonsterCfg()
	end
	local boss_list = {}
	for k,v in ipairs(self.shenyun_monster_list) do
		if v.scene_id == scene_id then
			local list = {}
			list.x = v.x_pos
			list.y = v.y_pos
			list.id = v.boss_id
			list.name = v.boss_name
			list.level = v.boss_level
			list.index = v.index
			table.insert(boss_list,list)
		end
	end
	return boss_list
end

function BossWGData:GetCurCanFlush(is_cross,show_index)
	local _,layer,_ = self:GetCurSelectBossID()
	if show_index == BossViewIndex.SGBOSS then
		local cfg = self:GetSgLayerCfg(layer - 1)
		if cfg then
			return cfg.is_cross == is_cross
		end
	elseif show_index == BossViewIndex.MiJingBoss then
		if self.mj_boss_cfg.enter_cfg[layer] then
			return self.mj_boss_cfg.enter_cfg[layer].is_cross == is_cross
		end
	end

end

function BossWGData:SetSGMJBossDrop( protocol )
	self.kf_sg_mj_drop_list = protocol.history_list
end

function BossWGData:GetBossLayerCfg( show_index )
	local btn_list = {}
	local count = self:GetLayerNumByShowIndex(show_index)

	--if show_index == BossViewIndex.VipBoss then
        --2019.11.15策划又要求去掉和平层
		--btn_list[1] = Language.Boss.LayerBtnName2
		--for i=1,count do
		--	table.insert(btn_list,string.format(Language.Boss.LayerBtnName1, Language.ChinaNub.hzNum[i + 1]))
		--end
	if show_index == TabIndex.worserv_boss_hmsy+1 then
		btn_list[1] = Language.HMSY.LayerList[1]
		for i=1,count-1 do
			table.insert(btn_list,string.format(Language.HMSY.LayerList[2], Language.Boss.hzNum[i+1]))
		end
	else
		for i=1,count do
			table.insert(btn_list,string.format(Language.Boss.LayerBtnName1, Language.Boss.hzNum[i+1]))
		end
	end

	return btn_list
end

function BossWGData:GetLayerNumByShowIndex( index )
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local limit_count = 0
    if index == BossViewIndex.WorldBoss then
		for k,v in ipairs(self.worldboss_auto.world_boss_enter_cfg) do
            if v.need_role_level > role_level then
                limit_count = limit_count + 1
            end
        end
        if limit_count == 0 then
            limit_count = 1
        end
		return #self.worldboss_auto.world_boss_enter_cfg - limit_count + 1
    elseif index == BossViewIndex.VipBoss then
		for k,v in ipairs(self.worldboss_auto.world_boss_enter_cfg) do
            if v.need_role_level > role_level then
                limit_count = limit_count + 1
            end
        end
        if limit_count == 0 then
            limit_count = 1
        end
		return #self.worldboss_auto.vipBoss_enter_cfg - limit_count + 1
	elseif index == BossViewIndex.PersonalBoss then
		return 0
    elseif index == BossViewIndex.DabaoBoss then
		for k,v in ipairs(self.worldboss_auto.dabao_boss_enter_cfg) do
            if v.need_role_level > role_level then
                limit_count = limit_count + 1
            end
        end
        if limit_count == 0 then
            limit_count = 1
        end
		return #self.worldboss_auto.dabao_boss_enter_cfg - limit_count + 1
	elseif index == BossViewIndex.SGBOSS+1 then
		return #self.sgyj_cfg.scene
	-- elseif index == BossViewIndex.MiJingBoss then
	-- 	return {}
    elseif index == BossViewIndex.KFBoss+1 then
        for k,v in ipairs(self.kf_boss_cfg.layer_cfg) do
            if v.level_limit > role_level then
                limit_count = limit_count + 1
            end
        end
        if limit_count == 0 then
            limit_count = 1
        end
        return #self.kf_boss_cfg.layer_cfg - limit_count + 1

	elseif index == BossViewIndex.ShengYuBoss+1 then
        return 0
    elseif index == BossViewIndex.ShenYuanBoss+1 then
		return 0
	elseif index == TabIndex.worserv_boss_hmsy + 1 then
		local list = {}
		--local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()--又要显示层数了
		for i, v in pairs(self.hmsy_cfg.scene) do
			if list[v.layer] == nil then--and open_day >= v.opengame_day
				list[v.layer] = 1
			end
		end
		return #list
	else
		return 0
	end
end

function BossWGData:GetTuJianCfg(boss_id)
	local boss_cfg  =  ConfigManager.Instance:GetAutoConfig("bosscardcfg_auto").bosscard_cfg
	for i, v in pairs(boss_cfg) do
		if boss_id == v.monster_id then
			return v
		end
	end
	return nil
end

function BossWGData:CheckCanShowGuildHelpInfo(scene_id)
	if scene_id == nil then
		return
	end
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	if scene_cfg == nil then
		return
	end
	scene_id = tonumber(scene_id)

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local need_level = self:GetBossLevelLimitBySceneId(scene_id)
	if role_level < need_level then
		return false
	end

	if scene_cfg.scene_type == SceneType.WorldBoss then
		if self:GetWorldBossRed() == 0 then
			return false
		end
	elseif scene_cfg.scene_type == SceneType.VIP_BOSS then
		local layer_cfg = self:GetVipBossLayerCfgBySceneId(scene_id)
		local role_vip = VipWGData.Instance:GetRoleVipLevel()
		if layer_cfg.vip_level > role_vip then
			return false
		end

		if not self:GetVipEnoughTimes() then
			return false
		end
	elseif scene_cfg.scene_type == SceneType.HONG_MENG_SHEN_YU then
		if self:GetHMSYRed() == 0 then
			return false
		end
	elseif scene_cfg.scene_type == SceneType.KF_BOSS then
		local times, max_times = self:GetCrossBossTire()
		if times >= max_times then
			return false
		end
	end

	return true
end

--
function BossWGData:GetGuildHelpBossInfo(scene_id)
	if scene_id == nil then
		return
	end
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	if scene_cfg == nil then
		return
	end
	scene_id = tonumber(scene_id)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	if scene_cfg.scene_type == SceneType.WorldBoss then
				return scene_cfg.scene_type
	elseif scene_cfg.scene_type == SceneType.VIP_BOSS then
		return scene_cfg.scene_type
	elseif scene_cfg.scene_type == SceneType.MJ_BOSS then
				return scene_cfg.scene_type
	elseif scene_cfg.scene_type == SceneType.KF_BOSS then
				return scene_cfg.scene_type

	elseif scene_cfg.scene_type == SceneType.DABAO_BOSS then
		for k,v in pairs(self.worldboss_auto.dabao_boss_enter_cfg) do
			if v.scene_id == scene_id then
				return scene_cfg.scene_type, v.level
			end
		end
	elseif scene_cfg.scene_type == SceneType.SG_BOSS then
		for k,v in pairs(self.sgyj_cfg.scene) do
			if v.scene_id == scene_id then
				return scene_cfg.scene_type, v.layer
			end
		end
	elseif scene_cfg.scene_type == SceneType.SHENGYU_BOSS then
		local list = self:GetSacredBossCfgList()
		for k,v in pairs(list) do
			if v.scene_id == scene_id then
				return scene_cfg.scene_type, v.layer_index
			end
		end
	elseif scene_cfg.scene_type == SceneType.KFSHENYUN_FB then
		local list = ConfigManager.Instance:GetAutoConfig("cross_shengyin_auto")
		for k,v in pairs(list.layer_cfg) do
			if v.scene_id == scene_id then
				return scene_cfg.scene_type, v.layer_index
			end
        end
    elseif scene_cfg.scene_type == SceneType.Shenyuan_boss then
        local boss_list = {}
		local list = self.shenyuan_boss_cfg.boss_cfg
		for k,v in pairs(list) do
			if v.scene_id == scene_id then
				return scene_cfg.scene_type, v.seq
			end
		end
	end
end

-- 根据场景id获取进入等级限制
function BossWGData:GetBossLevelLimitBySceneId(scene_id)
	if scene_id == nil then
		return
	end
	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
	if scene_cfg == nil then
		return
	end
	if scene_cfg.scene_type == SceneType.WorldBoss then
		local worldboss_auto = ConfigManager.Instance:GetAutoConfig("worldboss_auto").world_boss_boss_info
		for k,v in pairs(worldboss_auto) do
			if scene_id == v.scene_id then
				return v.need_role_level
			end
		end
	elseif scene_cfg.scene_type == SceneType.VIP_BOSS then
		local vipBoss_enter_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").vipBoss_enter_cfg
		for k,v in pairs(vipBoss_enter_cfg) do
			if scene_id == v.scene_id then
				return v.need_role_level
			end
		end
	elseif scene_cfg.scene_type == SceneType.KF_BOSS then
		for i, v in pairs(self.crosscrytal_lsit) do
			if v.scene_id == scene_id then
				return v.level_limit
			end
		end
	elseif scene_cfg.scene_type == SceneType.HONG_MENG_SHEN_YU then
		local cfg = self:GetHMSYLayerBySceneIdAndIndex(scene_id)
		return cfg and cfg.open_level
	end
end

function BossWGData:GetVipBossLayerCfgBySceneId( scene_id )
	for k,v in pairs(self.worldboss_auto.vipBoss_enter_cfg) do
		if v.scene_id == scene_id then
			return v
		end
	end
end

function BossWGData:SetEnterPersonBossData(bossId)
    self.enter_person_boss_id = bossId
end

function BossWGData:GetEnterPersonBossId()
    return self.enter_person_boss_id
end

--单boss奖励信息
function BossWGData:GetSingleBossInfo()
	local scene = Scene.Instance
	local info = {
		boss_id = 0,
		reward_t = {}
	}
	if scene:GetSceneType() == SceneType.PERSON_BOSS then
		local cfg = self.personal_scene_cfg[scene:GetSceneId()]
		if cfg then
			info.boss_id = cfg.boss_id
            info.reward_t = self:AddOpenDayItemList(cfg)
        end
    else--防止在普通场景弹出了结算面板
        info.boss_id = self.enter_person_boss_id
        -- local cfg
        -- for k, v in pairs(self.personal_scene_cfg) do
        --     if v.boss_id ==  info.boss_id then
        --         cfg = v
        --     end
        -- end
        -- if not
        -- info.reward_t = self:AddOpenDayItemList(cfg)
	end
	return info
end

function BossWGData:GetMonsterCfgByid(monster_id)
	return self.crossboss_monster_list[monster_id]
end

function BossWGData:GetMonsterRangeByid(monster_id)
	local monster_cfg = self:GetMonsterCfgByid(monster_id)
	local range = COMMON_CONSTS.GUAJI_MAX_RANGE
	if monster_cfg ~= nil then
		range = monster_cfg.checks and math.ceil(monster_cfg.checks / 2) or COMMON_CONSTS.GUAJI_MAX_RANGE
	end

	return range
end

function BossWGData:GetActorBoundsCfg(bundle)
	if self.config_actor_bounds_cfg then
		return self.config_actor_bounds_cfg[bundle]
	end
end

-- 屏蔽本次登录感谢信弹出
local ignore_tkmsg_key = "ignore_tkmsg_key"
function BossWGData:GetIsIgnoreThankMsg()
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local key = ignore_tkmsg_key .. uuid_str

	if self.boss_tkmsg_open then
		self.boss_tkmsg_open = false
		return 0
	elseif PlayerPrefsUtil.HasKey(key) then
		return PlayerPrefsUtil.GetInt(key)
	end

	return 0
end

function BossWGData:SetIsIgnoreThankMsg(is_ignore)
	local uuid_str = RoleWGData.Instance:GetUUIDStr()
	local key = ignore_tkmsg_key .. uuid_str
	local is_save = true
	if PlayerPrefsUtil.HasKey(key) then
		is_save = PlayerPrefsUtil.GetInt(key) ~= is_ignore
	end

	if not is_save then
		return
	end

	PlayerPrefsUtil.SetInt(key, is_ignore)
end

------------------------------------鸿蒙神域----------------------------------------------
function BossWGData:SetHMSYSelfInfo(protocol)
	self.hmsy_times = protocol.reward_time
	self.hmsy_add_time = protocol.add_time
	self.boss_concern_flag_origin = protocol.boss_concern_flag_origin
	self.boss_concern_flag_invade = protocol.boss_concern_flag_invade
	self.boss_concern_flag_public = protocol.boss_concern_flag_public
	local ori_list = self:GetHMSYCommonBossList()
	for i, v in pairs(ori_list) do
		local is_concern = self:GetHMSYORIBossFlag(v.boss_index) and 1 or 0
		v.is_concern = is_concern
		self:SetAllBossInfo(v.boss_id, {is_concern = is_concern})
	end
end

function BossWGData:SetHMSYDropInfo(protocol)
	self.hmsy_drop_list = protocol.drop_list
end

function BossWGData:SetHMSYSceneInfo(protocol)
	if not self.hmsy_scene_info[protocol.scene_id] then
		self.hmsy_scene_info[protocol.scene_id] = {}
	end
	local t = {}
	t.hmsy_scene_id = protocol.scene_id
	t.hmsy_scene_index = protocol.scene_index
	t.hmsy_plat_type = protocol.plat_tpe
	t.hmsy_server_id = protocol.server_id
	t.hmsy_boss_list = protocol.boss_list
	self.hmsy_scene_info[protocol.scene_id][protocol.scene_index] = t
	local scene_index = protocol.scene_index * BossWGData.HMSY_INDEX
	for i, v in pairs(protocol.boss_list) do
		self.hmsy_boss_info[v.boss_id] = v
		--self.all_boss_info[v.boss_id + scene_index] = v
		self:SetAllBossInfo(v.boss_id + scene_index, v)
	end
end

function BossWGData:GetHMSYSceneInfoBySceneIdIndex(scene_id, scene_index)
	local info = self.hmsy_scene_info[scene_id] or {}
	return info[scene_index] or {}
end

function BossWGData:SetHMSYSelectScene(scene_id, scene_index, layer)
	self.hmsy_select_scene_id = scene_id
	self.hmsy_select_scene_index = scene_index
	self.hmsy_select_layer = layer
end

function BossWGData:GetHMSYSelectScene()
	return self.hmsy_select_scene_id, self.hmsy_select_scene_index, self.hmsy_select_layer
end

function BossWGData:GetHMSYCurSelectSceneBossList()
	return self:GetHMSYSceneInfoBySceneIdIndex(self.hmsy_select_scene_id, self.hmsy_select_scene_index)
end

function BossWGData:GetHMSYCurSelectSceneType()
	return self:GetHMSYLayerBySceneIdAndIndex(self.hmsy_select_scene_id)
end

function BossWGData:GetHMSYBossInfoByBossId(boss_id)
	return self.hmsy_boss_info[boss_id] or {}
end

function BossWGData:GetHMSYOtherCfg()
	return self.hmsy_cfg.other[1]
end

function BossWGData:GetHMSYTimes()
	return (self.hmsy_times or 0) - (self.hmsy_add_time or 0), (self.hmsy_add_time or 0)
end

function BossWGData:GetHMSYAllTimesInfo()
	return self.hmsy_times or 0, self.hmsy_add_time or 0
end

function BossWGData:GetHMSYLayerIsEnter(layer)
	local cfg
	for i, v in ipairs(self.hmsy_cfg.scene) do
		if v.layer == layer then
			cfg = v
			break
		end
	end

	if not cfg then
		return false, nil ,nil
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if cfg.open_level > role_level then
		return false, cfg.open_level, nil
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if cfg.opengame_day > open_day then
		return false, nil, cfg.opengame_day
	end

	return true
end

function BossWGData:GetHMSYSceneIdIndexByLayer(layer)
	local list = {}
	for i, v in ipairs(self.hmsy_cfg.scene) do
		if v.layer == layer then
			if v.scene_type == WorldServerView.SCENETYPE.COMMON then
				table.insert(list, 1, v)
			else
				table.insert(list, v)
			end
		end
	end
	return list
end

function BossWGData:GetHMSYSceneInfoByLayer(layer)
	local list = {}
	local scene_list = self:GetHMSYSceneIdIndexByLayer(layer)
	for i, v in ipairs(scene_list) do
		local index_list = self.hmsy_scene_list[v.scene_id] or {}
		for i=0,#index_list+1 do
			if v.scene_type == WorldServerView.SCENETYPE.COMMON then
				if index_list[i] then
					table.insert(list, 1, index_list[i])
				end
			else
				table.insert(list, index_list[i])
			end
		end
	end
	return list
end

function BossWGData:GetHMSYLayerBySceneIdAndIndex(scene_id)
	for i, v in ipairs(self.hmsy_cfg.scene) do
		if v.scene_id == scene_id then
			return v
		end
	end
end

function BossWGData:SetHMSYTotalInfo(protocol)
	for i, v in pairs(protocol.scene_list) do
		if not self.hmsy_scene_list[v.scene_id] then
			self.hmsy_scene_list[v.scene_id] = {}
		end
		self.hmsy_scene_list[v.scene_id][v.scene_index] = v
	end
end

function BossWGData:GetHMSYTotalInfoBySceneIdIndex(scene_id, scene_index)
	local info = self.hmsy_scene_list[scene_id] or {}
	return info[scene_index] or {}
end

--用于显示鸿蒙神域点击头像奖励按钮的奖励面板
function BossWGData:GetHMSYBossList()
    local boss_list = {}
    local scene_list = self:GetHMSYSceneIdIndexByLayer(WorldServerView.SCENETYPE.ORI)
    for m, boss_info in ipairs(self.hmsy_cfg.boss) do
        --if v.scene_id == boss_info.scene_id then
            local boss_cfg = self.crossboss_monster_list[boss_info.boss_id]
            local t = {}
            self:ResetIndexBossCfg(t, boss_info)
            t.boss_name = boss_cfg.name
            t.boss_level = boss_cfg.level
            t.boss_id = boss_info.boss_id
            t.need_role_level = t.role_level_limit
            local drop_list = boss_info.drop_item_list or ""
            local drop_item_list = Split(drop_list, "|")
            t.drop_item_list = {}
            for l,v in pairs(drop_item_list) do
                if tonumber(v) then
                    table.insert(t.drop_item_list,{item_id = tonumber(v)})
                end
            end
            t.type = BossWGData.MonsterType.Boss
            t.show_duobei = true
            t.task_type = RATSDUOBEI_TASK.HONGMENGSHENYU
            boss_list[t.boss_id] = t
    end
    return boss_list
end

function BossWGData:GetRewardListById(boss_id)
    local cfg
    if Scene.Instance:GetSceneType() == SceneType.HONG_MENG_SHEN_YU then
        cfg = self:GetHMSYBossList()[boss_id]
    elseif Scene.Instance:GetSceneType() == SceneType.Shenyuan_boss then
        cfg = self:GetShenyuanDropRewardCfgById(boss_id)
    elseif Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_CROSS_CHESTSHOP_BOSS then
        cfg = TreasureBossWGData.Instance:GetBossDropShowData(boss_id)
    elseif Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
    	cfg = XianJieBossWGData.Instance:GetBossCfgById(boss_id)
	elseif Scene.Instance:GetSceneType() == SceneType.CROSS_EVERYDAY_RECHARGE_BOSS then
		cfg = self:GetERBBossRewardShowListInfo(boss_id)
    else
        cfg = self:GetBossInfoByBossId(boss_id)
    end
    return cfg
end


function BossWGData:GetHMSYCommonBossList(need_reset)
	if not self.hmsy_common_boss_list or need_reset then
		local boss_list = {}
		local scene_list = self:GetHMSYSceneIdIndexByLayer(WorldServerView.SCENETYPE.ORI)
		for i, v in ipairs(scene_list) do
			for m, boss_info in ipairs(self.hmsy_cfg.boss) do
				if v.scene_id == boss_info.scene_id then
					local boss_cfg = self.crossboss_monster_list[boss_info.boss_id]
					local t = {}
					self:ResetIndexBossCfg(t, boss_info)
					t.boss_name = boss_cfg.name
					t.boss_level = boss_cfg.level
					t.big_icon = boss_cfg.small_icon
					--t.need_role_level = v.open_level
					t.need_role_level = t.role_level_limit
					t.resid = boss_cfg.resid
					t.scale = t.scale or 1
					t.view_resouce = boss_info.boss_id
					local drop_list = t.drop_item_list or ""
                    if type(drop_list) ~= "table" then
                        t.is_item_list = false
                        t.drop_item_list = Split(drop_list, "|")
                    else
                        t.is_item_list = true
						t.drop_item_list = self:AddOpenDayItemList(boss_info, self.reset_hmsy_bosslist)
                    end
					t.type = BossWGData.MonsterType.Boss
					t.show_duobei = true
					t.task_type = RATSDUOBEI_TASK.HONGMENGSHENYU
					table.insert(boss_list, t)
				end
			end
		end
		self.hmsy_common_boss_list = boss_list
	end

	for i, v in pairs(self.hmsy_common_boss_list) do
		local info = self:GetHMSYBossInfoByBossId(v.boss_id)
		v.next_refresh_time = info.next_refresh_time
		v.last_dead_time = info.last_dead_time
		v.last_killer_name = info.last_killer_name
		v.kill_vo = {}
		if v.last_killer_name ~= "" and v.last_killer_name ~= nil then
			table.insert(v.kill_vo, {last_killer_name = v.last_killer_name})
		end
	end
	return self.hmsy_common_boss_list
end

function BossWGData:GetHMSYDefaultLayer()
	local layer_list = Language.HMSY.LayerList
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for i = #layer_list, 1, -1 do
		for k, v in pairs(self.hmsy_cfg.scene) do
			if v.layer == i then
				if role_level >= v.open_level and open_day >= v.opengame_day then
					return i
				else
					break
				end
			end
		end
	end
	return 1
end

function BossWGData:GetHMSYORIBossFlag(index)
	return bit:_and(self.boss_concern_flag_origin, bit:_lshift(1,index)) ~= 0
end

function BossWGData:GetHMSYInvadeBossFlag(layer, id_num, index)
	local flag = self.boss_concern_flag_invade[layer][id_num]
	return bit:_and(flag, bit:_lshift(1,index)) ~= 0
end

function BossWGData:GetHMSYCommonBossFlag(layer, index)
	local flag = self.boss_concern_flag_public[layer]
	return bit:_and(flag, bit:_lshift(1,index)) ~= 0
end

function BossWGData:GetHMSYBossListById(scene_id)
	if not self.hmsy_boss_list[scene_id] then
		self.hmsy_boss_list[scene_id] = {}
		for i, v in ipairs(self.hmsy_cfg.boss) do
			if v.scene_id == scene_id then
				local t = {}
				local cfg = self:GetMonsterInfo(v.boss_id)
				t.name = cfg.name
				t.boss_name = cfg.name
				t.boss_level = cfg.level
				t.x_pos = v.born_pos_x
				t.y_pos = v.born_pos_y
				t.boss_id = v.boss_id
				t.scene_id = v.scene_id
				t.big_icon = cfg.small_icon
				table.insert(self.hmsy_boss_list[scene_id], t)
				self.new_cross_boss_list[t.boss_id] = t
			end
		end
	end
	return self.hmsy_boss_list[scene_id]
end

function BossWGData:SetCurHMSYSceneIndex(scene_index)
	self.cur_hmsy_scene_index = scene_index
end

function BossWGData:GetCurHMSYSceneIndex()
	return self.cur_hmsy_scene_index
end

function BossWGData:SetHMSYInsideInfo(protocol)
    self:SetCurHMSYSceneIndex(protocol.scene_index)
    local scene_id = Scene.Instance:GetSceneId()
    local scene_index = protocol.scene_index * BossWGData.HMSY_INDEX
    for i, v in pairs(protocol.boss_list) do
        self.hmsy_boss_info[v.boss_id] = v
        self.all_boss_info[v.boss_id + scene_index] = v
    end
end

function BossWGData:GetHMSYBossCfgByBossId(boss_id)
	if not self.new_cross_boss_list[boss_id] then
		for i, v in pairs(self.hmsy_cfg.boss) do
			if v.boss_id == boss_id then
				self:GetHMSYBossListById(v.scene_id)
			end
		end
	end
	return self.new_cross_boss_list[boss_id]
end

function BossWGData:NewGetHMSYBossCfgByBossId(boss_id)
	for i, v in pairs(self.hmsy_cfg.boss) do
		if v.boss_id == boss_id then
			return v
		end
	end
	return {}
end

function BossWGData:HasHMSYEnoughTimes()
    local times = self:GetHMSYTimes()
	return times - self:GetHMSYOtherCfg().reward_times < 0
end

function BossWGData:GetHMSYLimitLayer(btn_list)
	local hmsy_btn_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	for i, v in ipairs(btn_list) do
		local scene_list = self:GetHMSYSceneIdIndexByLayer(i)
		if role_level >= scene_list[1].open_level then
			table.insert(hmsy_btn_list, {name = v})
		else
			table.insert(hmsy_btn_list, {name = v, limit = v.open_level})
			break
		end
	end
	return hmsy_btn_list
end

function BossWGData:GetWorldBossDefaultIndex(default_index, list_data, need_max_level)
    if list_data then
        if self:GetBossIndexByList(list_data) ~= - 1 then
            return self:GetBossIndexByList(list_data)
        end
        default_index = 1
        local idx = 1
		local role_level = RoleWGData.Instance:GetRoleLevel()
        local level_require = 0
        for i = #list_data, 1, -1 do
            local data = list_data[i]
			level_require = need_max_level and data.need_role_level or data.boss_level
			if level_require <= role_level and role_level >= data.boss_level then
				idx = i
				break
			end
		end
        local is_all_died = true
        for i = idx, 1, -1 do
            if level_require <= role_level then
                local data = list_data[i]
                local boss_server_info = BossWGData.Instance:GetBossRefreshInfoByBossId(data.boss_id)
                local fresh_time = boss_server_info and boss_server_info.next_refresh_time or 0
                local time = fresh_time - TimeWGCtrl.Instance:GetServerTime()
                if time <= 0 then
                    default_index = i
                    is_all_died = false
                    break
                end
            end
        end
        if is_all_died then
            default_index = idx
        end
    end
	return default_index or 1
end

function BossWGData:GetBossCellInfo(data, boss_info)
	data.show_duobei = boss_info.show_duobei
	data.task_type = boss_info.task_type
	data.cell_scale = 0.9
	return data
end
-------------------------------------------掉落归属-----------------------------------------------

-------------------------------------------------------------------------------
function BossWGData:SetOwnBossList(protocol)
	local list = self.own_boss_list[protocol.monster_obj_id] or {}
    for k, v in pairs(list) do
        local obj =  v > 0 and Scene.Instance:GetObjByOriId(v)or nil
        if obj ~= nil and obj:IsRole() then
            --print_error("set0")
            obj:SetAttr("own_drop_boss_count", 0)
             --GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION,nil)
        end
    end
    local scene_type = Scene.Instance:GetSceneType()

    self.own_boss_list[protocol.monster_obj_id] = nil
    if scene_type ~= SceneType.WorldBoss and scene_type ~= SceneType.VIP_BOSS then
    	for k, v in pairs(protocol.owner_list) do
    	    local obj = v.owner_uid > 0 and Scene.Instance:GetObjByOriId(v.owner_uid)or nil
    	    --print_error("obj", obj~=nil, obj and obj:IsRole(), v.owner_uid)
    	    if obj ~= nil and obj:IsRole() then
    	        --print_error("set")
    	        obj:SetAttr("own_drop_boss_count", 1)
    	        if not self.own_boss_list[protocol.monster_obj_id] then
    	            self.own_boss_list[protocol.monster_obj_id] = {}
    	        end
    	        table.insert(self.own_boss_list[protocol.monster_obj_id], v.owner_uid)
    	    end
    	end
    end
	self.own_boss_all_list = protocol.owner_list
	--self.select_monster_obj_id = protocol.monster_obj_id
end

function BossWGData:GetIsOwnBossByUid(obj)
    for i, v in pairs(self.own_boss_all_list) do
		if obj.vo.role_id == v.owner_uid then
			obj:SetAttr("own_drop_boss_count", 1)
		end
    end
end

function BossWGData:GetBossOwnInfoBySelect(obj)
    local vo = obj:GetVo() or {}
    GlobalEventSystem:Fire(MainUIEventType.SHOW_ASCRIPTION, self.select_owner_name[vo.obj_id])
end

function BossWGData:SetOwnBossListName(obj_id, name)
	self.select_owner_name[obj_id] = name
end

function BossWGData:GetOwnBossListName(obj_id)
	return self.select_owner_name and self.select_owner_name[obj_id]
end

function BossWGData:ClearOnwerInfo()
	self.own_boss_all_list = {}
    self.select_owner_name = {}
end
-------------------------------------------------------------------------------
------------------------------------------------------------------------------------------
--添加活动额外道具
function BossWGData:AddOpenDayItemList(cfg, callback, tab_index)
	local ori_list = {}
	for i = 0, #cfg.drop_item_list do
		ori_list[i] = cfg.drop_item_list[i]
	end

	local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if cur_open_day == -1 then
		return ori_list
	end

	local act_type
	local act_item_list
	for i = 1, BossWGData.ShowOpenDayMax do
		act_type = cfg["show_act_" .. i]
		if not act_type then
			break
		end

		if callback then
			self:SetActListener(act_type, callback)
		end

		if act_type and ActivityWGData.Instance:GetSomeActivityIsOpen(act_type) then
			act_item_list = cfg["show_item_" .. i]
			if act_item_list then
				for i = 0, #act_item_list do
					table.insert(ori_list, act_item_list[i])
				end
			end
		end
	end
	return ori_list
end


function BossWGData:ListenActChange(act_type)
	if self.act_drop_listener[act_type] then
		--多个活动下发，每秒刷新一个活动的改变信息，目前有5个刷新列表
		if self.drop_list_delay then
			GlobalTimerQuest:AddDelayTimer(function()
				self:ListenActChange(act_type)
			end, 1)
			return
		end

		local list = self.act_drop_listener[act_type].callback_list
		local len = #list
		local count = 1
		if len > 0 then
			self.drop_list_delay = GlobalTimerQuest:AddTimesTimer(function()
				list[count]()
				count = count + 1
				if count == len then
					self.drop_list_delay = nil
				end
			end, 0, len)
		end
	end
end

function BossWGData:SetActListener(act_id, callback)
	if not self.act_drop_listener[act_id] then
		self.act_drop_listener[act_id] = {}
		self.act_drop_listener[act_id].callback_list = {}
	end

	if not self.act_drop_listener[act_id][callback] then
		self.act_drop_listener[act_id][callback] = 1
		table.insert(self.act_drop_listener[act_id].callback_list, callback)
	end
end

function BossWGData:ChangeDayPassAct()
	for i, v in pairs(self.act_drop_listener) do
		self:ListenActChange(i)
	end
end

function BossWGData:RoleAttrChange(attr)
	if attr == "level" then--level监听太多，加个0.5的延时再刷新
		GlobalTimerQuest:AddDelayTimer(function()
            self:GetPersonalBossList(true)
            RemindManager.Instance:Fire(RemindName.Boss_Vip)
        end, 0.5)
    elseif attr == "vip_level" then
        ViewManager.Instance:FlushView(GuideModuleName.Boss)
        ViewManager.Instance:FlushView(GuideModuleName.WorldServer)
        RemindManager.Instance:Fire(RemindName.WorldServer_Xianjie)
        local scene_type = Scene.Instance and Scene.Instance:GetSceneType()
        if scene_type == SceneType.XianJie_Boss then --刷新显示进入次数变化
            GlobalEventSystem:Fire(OtherEventType.SHOW_TASK_CHANGE,nil,true)
        end
	end
end

function BossWGData:ResetIndexBossCfg(t1, cfg)
	setmetatable(t1, {__index = cfg})
end

--存储boss跳转信息，过滤vip低等级层数
function BossWGData:SetBossJumpInfo(view_index, layer, index)
	if view_index == TabIndex.boss_vip then
		layer = self:GetNowVipIndexByLayer(layer)
	end

	self:SetBossTuJianIndex(view_index, layer, index)
end

--判断世界，个人，巢穴，打宝4个boss的跳转信息
function BossWGData:CheckBossJumpInfo(view_index, layer, boss_id)
    local boss_index
	if view_index == TabIndex.boss_world then
		local boss_info = self:GetBossInfoByBossId(boss_id)
		if boss_info == nil then
			local boss_cfg = self:GetWorldBossCfgInAllBoss(boss_id)
			return false, boss_cfg and boss_cfg.need_role_level
		end
        boss_index = boss_info.boss_index
	elseif view_index == TabIndex.boss_personal then
		local boss_info = self:GetBossInfoByBossId(boss_id)
		if boss_info == nil then
			local boss_cfg = self:GetPerBossCfgInAllInfo(boss_id)
			return false, boss_cfg and boss_cfg.need_level
		end
        boss_index = boss_info.layer
	elseif view_index == TabIndex.boss_vip then
		local role_level = RoleWGData.Instance:GetRoleLevel()
		local vip_enter_cfg = self:GetEnterVipCfgbyIndex(layer)
		if not vip_enter_cfg then
			return false, nil
		end


		if role_level < vip_enter_cfg.need_role_level then
			return false, vip_enter_cfg.need_role_level
		end

        local status, vip_boss_index = self:GetVipBossIndexByLayerAndBossId(layer, boss_id)
		if not status then
			return false, nil
		end

		boss_index = vip_boss_index
	elseif view_index == TabIndex.boss_dabao then
		local status, info = self:GetDabaoIndexCfgByLayerAndBossId(layer, boss_id)
		if not status then
			return false, info
		end
		boss_index = info
	end
	return true, boss_index
end

function BossWGData:GetGuWuAddPer(count)
	count = count or 0
	return self.other_cfg[1].guwu_add_per / 100 * count
end
function BossWGData.IsSingleBoss(scene_type)
	scene_type = scene_type or Scene.Instance:GetSceneType()
	return Scene.Instance:GetSceneType() == SceneType.PERSON_BOSS
end

function BossWGData:GetBossSceneTip(scene_id, same_scene)
	if scene_id == nil then
		return
	end

	local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)

	if not scene_cfg then
		return
	end

	local now_type = Scene.Instance:GetSceneType()

	if self.IsSingleBoss(now_type) then
		if same_scene then
			return Language.Boss.InSameScene
		else
			return Language.Boss.OutFb
		end
	else
		if now_type == SceneType.DABAO_BOSS or now_type == SceneType.SG_BOSS then
			return Language.Boss.OutFb
		else
			return Language.Boss.OutFbAndGo
		end
	end
end

function BossWGData:GetBossTimesInfo(boss_type)
	if BOSS_TASK_TYPE.WORLD == boss_type then
		local wb_other_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
		local world_times_info = self:GetWorldBossEnterTimeInfo()
		if world_times_info then
			local max_times = wb_other_cfg.world_boss_day_default_times + world_times_info.world_boss_day_extra_kill_times
			local cur_times = max_times - world_times_info.world_boss_day_kill_times + world_times_info.world_boss_flush_can_kill_times
			return cur_times, max_times
		end
	elseif BOSS_TASK_TYPE.PERSON == boss_type then
		local left_enter_num, max_enter_num = self:GetPersonalBossEnterInfo()
		return left_enter_num, max_enter_num
	elseif BOSS_TASK_TYPE.DABAO == boss_type then
		local enter_time, max_times = self:GetDaBaoBossRemainEnterTimes()
		return max_times - enter_time, max_times
	elseif BOSS_TASK_TYPE.VIP == boss_type then
        local wb_other_cfg = ConfigManager.Instance:GetAutoConfig("worldboss_auto").other[1]
        if wb_other_cfg.boss_home_day_default_times == - 1 then
            return -1,-1
        end
		local vipboss_times_info = self:GetBossHomeEnterTimeInfo()
		if vipboss_times_info then
			local max_times = wb_other_cfg.boss_home_day_default_times + vipboss_times_info.boss_home_day_item_add_kill_times + vipboss_times_info.boss_home_day_vip_buy_kill_times
			local cur_times = max_times - vipboss_times_info.boss_home_day_kill_times
			return cur_times, max_times
		end
	elseif BOSS_TASK_TYPE.MANHUANG == boss_type then
		local tire_value, max_tire_value = self:GetCrossBossTire()
		return max_tire_value - tire_value, max_tire_value
	end
	return 0, 0
end

function BossWGData:GetWorldBossRed()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.boss_world)
	if not is_open then
		return 0
	end

    local boss_list = self:GetWorldBossList()
    for k,v in pairs(boss_list) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            return 1
        end
    end

    local cur_times = self:GetBossTimesInfo(BOSS_TASK_TYPE.WORLD)
    if cur_times > 0 then
        return 1
    end
    return 0
end

function BossWGData:InvateBossFirstKillTip()
    local is_vip_boss = false
    for k,v in pairs(self.worldboss_auto.vipBoss_boss_info) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            is_vip_boss = true
        end
    end
    local is_world = false
    local boss_list = self:GetWorldBossList()
    for k,v in pairs(boss_list) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            is_world = true
        end
    end

	local is_invate = false

	local tab_Index
	if is_vip_boss then
		local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_vip")
		if is_open then
			tab_Index = TabIndex.boss_vip
			is_invate = true
		end
	end

	if not is_invate and is_world then
		local is_open = FunOpen.Instance:GetFunIsOpenedByTabName("boss_world")
		if is_open then
			tab_Index = TabIndex.boss_world
			is_invate = true
		end
	end

    if is_invate then
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Boss_FirstKill, 1, function ()
            ViewManager.Instance:Open(GuideModuleName.Boss, tab_Index)
            return true
        end)
    else
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.Boss_FirstKill, 0)
    end
end


function BossWGData:GetVipBossRed()
    --首杀红点
    for k,v in pairs(self.worldboss_auto.vipBoss_boss_info) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            return 1
        end
    end

    -- --仙力领取红点
    -- if BossWGData.Instance:JudgeCanGetBossXianli() then
    --     return 1
    -- end

    -- --仙力充足可以打的红点
    -- if BossWGData.Instance:JudgeHasEnoughXianli() then
    --     return 1
    -- end

	local has_effect = VipWGData.Instance:IsVip()
	if not has_effect then
		return 0
    end

	local enter_cfg = self:GetEnterVipCfgbyIndex(1)--取第一层的vip等级
    if VipWGData.Instance:GetRoleVipLevel() < enter_cfg.free_vip_level then
		return 0
	end

    local cur_times = self:GetBossTimesInfo(BOSS_TASK_TYPE.VIP)
	return cur_times > 0 and 1 or 0
end

function BossWGData:GetDabaoBossRed()
	local enter_comsun = BossWGData.Instance:GetDaBaoBossEnterComsun()
	local tiky_id = self.other_cfg[1].dabao_tiky_item_id
	local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(tiky_id)

	if has_tiky_num < enter_comsun then
		return 0
	end

	local cur_times = self:GetBossTimesInfo(BOSS_TASK_TYPE.DABAO)
	return cur_times > 0 and 1 or 0
end

function BossWGData:GetMHSSRed()
    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.KuafuBoss)
    if not is_open then
        return 0
    end

	local num = BossWGData.Instance:GetTreasureGatherTimes()
	if num > 0 then
		return 1
	end

	local times, max_times = self:GetCrossBossTire()
	return times < max_times and 1 or 0
end

function BossWGData:GetHMSYRed()
    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.HMSYBoss)
    if not is_open then
        return 0
    end
	local cfg = self:GetHMSYOtherCfg()
    local times = self:GetHMSYTimes()
	return (cfg.reward_times - times) > 0 and 1 or 0
end

function BossWGData:GetSYMWRed()
    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.ShenYuanBoss)
    if not is_open then
        return 0
    end
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    local shenyuan_list = self:GetShenyuanBossData()
    for k ,v in pairs(shenyuan_list) do
        if v.status == 1 and v.need_role_level <= role_lv then
            return 1
        end
    end
    return 0
end

function BossWGData:GetEveryDayRechargeBossRed()
	local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.EveryDayRechargeBoss)
    if not is_open then
        return 0
    end

	local erb_type_data_list = self:GetCurERBTypeList()
	if not IsEmptyTable(erb_type_data_list) then
		for k, v in pairs(erb_type_data_list) do
			if self:IsShowEveryDayRechargeRedPoint(v.boss_group, v.boss_group_index) then
				return 1
			end
		end
	end

	return 0
end

function BossWGData:IsShowEveryDayRechargeRedPoint(boss_group, boss_group_index)
	local condition_data = self:GetERBConditionData(boss_group, boss_group_index)
	if IsEmptyTable(condition_data) or nil == condition_data.need_consume_rmb_today then
		return false
	end

	local today_recharge_num = RechargeWGData.Instance:GetToDayRealChongZhiRmb()
	if today_recharge_num < condition_data.need_consume_rmb_today then
		return false
	end

	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s%s%s", uuid.temp_low, uuid.temp_high, "EveryDayRecharge", boss_group, boss_group_index)
	local remind_day = PlayerPrefsUtil.GetInt(key)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if cur_day ~= remind_day then
		return true
	end
end

function BossWGData:SetEveryDayRechargeRedPoint(boss_group, boss_group_index)
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s%s%s", uuid.temp_low, uuid.temp_high, "EveryDayRecharge", boss_group, boss_group_index)
	PlayerPrefsUtil.SetInt(key, cur_day)
end

function BossWGData:GetSGYJRed()
    local is_open = FunOpen.Instance:GetFunIsOpenedByTabName(FunName.ShangGuBoss)
    if not is_open then
        return 0
    end
	local enter_comsun = BossWGData.Instance:GetSGBossEnterComsun()
	local tiky_id = BossWGData.Instance:GetSGOtherCfg().boss_ticket
	local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(tiky_id)
	if has_tiky_num < enter_comsun then
		return 0
	end

	--判断是否可以进入第一层.
	local can_enter = BossWGData.Instance:GetKfLayerIsEnter(1)
	print_error("can_enter: ", can_enter)
	if not can_enter then
		return 0
	end

	local enter_times, max_enter_times = BossWGData.Instance:GetSgBossEnterTimes()
	return enter_times >= max_enter_times and 0 or 1
end

function BossWGData.IsTeamBossScene(scene_type)
	scene_type = scene_type or Scene.Instance:GetSceneType()
	return scene_type == SceneType.WorldBoss or
			scene_type == SceneType.VIP_BOSS or
			scene_type == SceneType.KF_BOSS or
			scene_type == SceneType.HONG_MENG_SHEN_YU
end


function BossWGData:ShenYuanBossOtherCfg()
	return self.shenyuan_boss_cfg.other[1]
end

function BossWGData:GetShenYuanBossList()
	return (self.shenyuan_boss_cfg or {}).boss_cfg or {}
end

function BossWGData:GetShenYuanBossIsConcern(seq)
	local is_concern = (self.concern_boss_list or {})[seq] or 0
	return is_concern == 1
end

function BossWGData:GetShenYuanRefreshTime()
    return TimeUtil.ParsingTimeStr(self.shenyuan_boss_cfg.refresh_time[1].refresh_time)
end

function BossWGData:GetShenyuanBossData(need_reset)
    if not self.shenyuan_common_boss_list or need_reset then
    	local boss_list = {}
        for m, boss_info in ipairs(self.shenyuan_boss_cfg.boss_cfg) do
            local boss_cfg = self.crossboss_monster_list[boss_info.boss_id]
            local t = {}
            self:ResetIndexBossCfg(t, boss_info)
            t.boss_name = boss_cfg.name
            t.boss_level = boss_cfg.level
            t.big_icon = boss_cfg.small_icon
            --t.need_role_level = v.open_level
            t.need_role_level = t.enter_level
            t.resid = boss_cfg.resid
            t.scale = t.scale or 1
            t.boss_pos = boss_info.boss_pos
            local pos = Split(boss_info.boss_pos, ",")
            t.x_pos = tonumber(pos[1])
            t.y_pos = tonumber(pos[2])
            t.drop_item_list1 = Split(boss_info.drop_item_list, ",") --归属展示
            t.drop_item_list2 = Split(boss_info.join_drop_item_list, ",") --参与展示
            t.box_drop_item_list = Split(boss_info.box_drop_item_list, ",") --宝箱展示
            t.view_resouce = boss_info.view_resouce
            t.type = BossWGData.MonsterType.Boss
            t.server_count_limit = boss_cfg.server_count_limit
            table.insert(boss_list, t)
		end
		self.shenyuan_common_boss_list = boss_list
	end

	for i, v in pairs(self.shenyuan_common_boss_list) do --击杀信息，刷新时间等
        local info = self:GetShenYuanBossInfoById(v.boss_id)
        v.next_refresh_time = info.next_refresh_time or 0
        v.status = info.status or 1
        v.kill_history = info.kill_history or {}
        if not IsEmptyTable(info.kill_history) then
            v.last_killer_name = info.kill_history[1].killer_role_name or ""
            v.server_id = info.kill_history[1].server_id or 0
            v.plat_type = info.kill_history[1].plat_type or 0
            v.box_country_index = info.kill_history[1].box_country_index or -1
        end
	end
	return self.shenyuan_common_boss_list
end

function BossWGData:GetShenYuanBossDataInfoByid(boss_id)
    local data_list = self:GetShenyuanBossData()
    for m, boss_info in ipairs(data_list) do
        if boss_info.boss_id == boss_id then
            return boss_info
        end
    end
    return nil
end

function BossWGData:GetShenYuanBossCfgByid(boss_id)
    for m, boss_info in ipairs(self.shenyuan_boss_cfg.boss_cfg) do
        if boss_info.boss_id == boss_id then
            return boss_info
        end
    end
    return nil
end

function BossWGData:GetShenYuanBossCfgSceneId(scene_id)
    local boss_list = {}
    for m, boss_info in ipairs(self.shenyuan_boss_cfg.boss_cfg) do
        if boss_info.scene_id == scene_id then
            table.insert(boss_list, boss_info)
        end
    end
    return boss_list
end

function BossWGData:GetShenYuanBossSceneidByIndex(index)
    for m, boss_info in pairs(self.shenyuan_boss_cfg.boss_cfg) do
        if boss_info.seq == index then
            return boss_info.scene_id
        end
    end
    return 0
end


function BossWGData:CheckShenyuanBossIsOpen()
	local is_open = false
    local level = RoleWGData.Instance:GetRoleLevel()
    local count = #self.shenyuan_boss_cfg.boss_cfg
    for i = count ,1 , -1 do
        local info = self.shenyuan_boss_cfg.boss_cfg[i]
		if level >= info.enter_level then
			is_open = true
			break
		end
	end
	return is_open
end

function BossWGData:GetShenYuanBossDefaultIndex()
	local default_index = 1
    local level = RoleWGData.Instance:GetRoleLevel()
    local idx = 1
    local count = #self.shenyuan_boss_cfg.boss_cfg
    for i = count ,1 , -1 do
        local info = self.shenyuan_boss_cfg.boss_cfg[i]
        local boss_cfg = self.crossboss_monster_list[info.boss_id]
        if level >= info.enter_level then
            idx = i
			break
		end
    end
    local is_all_died = true
    for i = idx, 1, -1 do
        local info = self.shenyuan_boss_cfg.boss_cfg[i]
        local boss_server_info = self:GetShenYuanBossInfoById(info.boss_id)
        local fresh_time = boss_server_info and boss_server_info.next_refresh_time or 0
        local time = fresh_time - TimeWGCtrl.Instance:GetServerTime()
        if (boss_server_info.status and boss_server_info.status == 1) or time <= 0 then
            default_index = i
            is_all_died = false
            break
        end
    end
    if is_all_died then
        default_index = idx
    end
	return default_index
end

function BossWGData:SetShenYuanBossData(protocol)
    for k, v in pairs(protocol.all_shenyuan_boss_info) do
        self.all_shenyuan_boss_info[v.boss_id] = v
    end
end

function BossWGData:GetShenYuanBossInfo()
    return self.all_shenyuan_boss_info or {}
end

function BossWGData:GetShenYuanBossInfoBySceneId(sceneid)
    for k,v in pairs(self.shenyuan_boss_cfg.boss_cfg) do
        if v.scene_id == sceneid then
            return v
        end
    end
    return {}
end

function BossWGData:GetShenYuanBossInfoById(boss_id)
    return self.all_shenyuan_boss_info[boss_id] or {}
end

function BossWGData:SetShenYuanSingleBossData(protocol)
   self.all_shenyuan_boss_info[protocol.boss_info.boss_id] = protocol.boss_info
end

function BossWGData:SetShenYuanHurtInfo(protocol)
    self.shenyuan_hurt_info = protocol.boss_hurt_info
end

function BossWGData:GetShenYuanHurtInfo()
    return self.shenyuan_hurt_info or {}
end

function BossWGData:SetShenYuanCountryHurtInfo(protocol)
    self.shenyuan_country_hurt_info = protocol.country_hurt_list
end

function BossWGData:SetShenYuanBossConcernList(protocol)
	self.concern_boss_list = protocol.concern_boss_list
end

function BossWGData:GetShenYuanCountryHurtInfo()
    return self.shenyuan_country_hurt_info or {}
end

function BossWGData:GetShenYuanlCountryHurtInfoMaxValue()
    local max = 1
    if not IsEmptyTable(self.shenyuan_country_hurt_info) then
        for k, v in pairs(self.shenyuan_country_hurt_info) do
            max = v.country_hurt > max and v.country_hurt or max
        end
    end
    return max
 end

function BossWGData:ResetShenYuanHurtInfo()
    self.shenyuan_hurt_info = {}
end

function BossWGData:GetShenYuanSingleHurtInfo()
   return self.person_hurt_info
end

function BossWGData:SetShenYuanSingleHurtInfo(protocol)
    self.person_hurt_info.hurt = protocol.hurt
    self.person_hurt_info.rank_count = protocol.rank_count
end

function BossWGData:GetShenYuanlHurtInfoMaxValue()
    local max = 1
    if not IsEmptyTable(self.shenyuan_hurt_info) then
        for k, v in pairs(self.shenyuan_hurt_info) do
            max = v.hurt > max and v.hurt or max
        end
    end
    return max
 end

 function BossWGData:SetShenYuanKillTimes(v)
    self.shenyuan_kill_times = v
 end

 function BossWGData:SetShenYuanJoinTimes(v)
    self.shenyuan_join_times = v
 end

function BossWGData:SetShenYuanBuyJoinTimes(v)
    self.shenyuan_buy_join_times = v
end

function BossWGData:GetShenYuanBuyJoinTimes()
    return self.shenyuan_buy_join_times or 0
end

 function BossWGData:SetWorldBossBuyTimes(v)
    self.world_boss_buy_times = v
 end

 function BossWGData:GetWorldBossBuyTimes()
    return self.world_boss_buy_times or 0
 end

 function BossWGData:GetWorldBossBuyConsumeByTimes(times)
    if not IsEmptyTable(self.worldboss_buy_times_auto) then
        times = times > #self.worldboss_buy_times_auto and #self.worldboss_buy_times_auto or times
        for k, v in pairs(self.worldboss_buy_times_auto) do
            if v.vip_buy_enter_times == times then
                return v.need_gold
            end
        end
    else
        print_error("取不到世界boss购买次数配置")
    end
 end

 --返回参与次数/总次数
function BossWGData:GetShenYuanTimesInfo()
    local other_cfg = self:ShenYuanBossOtherCfg()
    local max_join_time = other_cfg.join_reward_times
    local kill_time, join_time = self:GetShenYuanTimes()
    local buy_times = self:GetShenYuanBuyJoinTimes()
    return join_time, max_join_time + buy_times
end

--深渊购买花费
function BossWGData:GetShenyuanBossBuyTimesExtra(times)
	for k,v in pairs(self.shenyuan_boss_cfg.vip_buy_cost) do
		if v.vip_buy_times == times then
			return v.cost_price
		end
	end
	return 0
end


function BossWGData:GetShenYuanTimes()
    return self.shenyuan_kill_times or 0, self.shenyuan_join_times or 0
end

function BossWGData:GetShenyuanDropRewardCfgById(boss_id)
    if not IsEmptyTable(self.shenyuan_boss_reward_tb) and not IsEmptyTable(self.shenyuan_boss_reward_tb[boss_id]) then
        return self.shenyuan_boss_reward_tb[boss_id]
    end
    for k,v in pairs(self.shenyuan_boss_cfg.boss_cfg) do
       if v.boss_id == boss_id then
            local info = {}
            info.boss_id = boss_id
            local cfg = Split(v.drop_item_list, ",")
            info.drop_item_list = {}
            for i, v1 in ipairs(cfg) do
                local str = Split(v1, ":")
                local data = {item_id = tonumber(str[1]), num = tonumber(str[2]), is_bind = tonumber(str[3]), }
                table.insert(info.drop_item_list, data)
            end
            local cfg1 = Split(v.join_drop_item_list, ",")
            info.join_drop_item_list = {}
            for i, v2 in ipairs(cfg1) do
                local str = Split(v2, ":")
                local data = {item_id = tonumber(str[1]), num = tonumber(str[2]), is_bind = tonumber(str[3]), }
                table.insert(info.join_drop_item_list, data)
            end
            info.reward_tips_des = v.reward_tips_des
            if not  self.shenyuan_boss_reward_tb then
                self.shenyuan_boss_reward_tb = {}
            end
            self.shenyuan_boss_reward_tb[boss_id] = info
            return info
       end
    end
    return {}
end

	-- 是否可旋转
function BossWGData:GetMonsterRotable(monster_id)
 	local cfg = BossWGData.Instance:GetMonsterCfgByid(monster_id)
	if cfg and cfg.unrotable == 1 then
		return false
	end
	return true
end

--boss首杀

function BossWGData:SetFirstKillPersonInfo(protocol)
    self.person_info_list = protocol.person_info_list
end

function BossWGData:GetFirstKillPersonInfo()
    return self.person_info_list or {}
end

function BossWGData:SetFirstKillInfinitePersonInfo(protocol)
    self.infinite_person_info_list = protocol.infinite_person_info_list
end

function BossWGData:GetFirstKillInfinitePersonInfo()
    return self.infinite_person_info_list or {}
end

function BossWGData:GetFirstKillWorldInfo()
    return self.world_info_list or {}
end


function BossWGData:SetFirstKillWorldInfo(protocol)
    self.world_info_list = protocol.world_info_list
end

function BossWGData:SetSingleFirstKillWorldInfo(protocol)
    if not self.world_info_list then
        self.world_info_list = {}
    end
    local is_exist = false
    for k, v in pairs(self.world_info_list) do
        if v.boss_id == protocol.boss_id then
            is_exist = true
            v.cur_kill_count = protocol.cur_kill_count
            v.killer_role_list = protocol.killer_role_list
            break
        end
    end
    if not is_exist then
        local data = {boss_id = protocol.boss_id, boss_type = protocol.boss_type,
        cur_kill_count = protocol.cur_kill_count, killer_role_list = protocol.killer_role_list}
        self.world_info_list[#self.world_info_list + 1] = data
    end
end


--个人首杀按钮是否显示,是否可以领取奖励
function BossWGData:GetIsPersonFirstKilledByBossId(boss_id)
    local info_list = self:GetFirstKillPersonInfo()
    if not IsEmptyTable(info_list) then
        for k, v in pairs(info_list) do
            if v.boss_id == boss_id then
                return v.reward == 0, v.reward == 0
            end
        end
    end
    return true, false
end

--世界首杀按钮是否显示,是否可以领取奖励
function BossWGData:GetIsWorldFirstKilledByBossId(boss_id)
    local boss_info = self:GetBossInfoByBossId(boss_id)
    if IsEmptyTable(boss_info) then return true, false end
    local data_list
    if boss_info.world_firstkill_times == -1 then
        data_list = self:GetFirstKillInfinitePersonInfo()
        if not IsEmptyTable(data_list) then
            for k, v in pairs(data_list) do
                if v.boss_id == boss_id then
                    return v.reward == 0, v.reward == 0
                end
            end
            return true, false
        end
    else
        data_list = self:GetFirstKillWorldInfo()
        if not IsEmptyTable(data_list) then
            for k, v in pairs(data_list) do
                if v.boss_id == boss_id then
                    for k1, v1 in pairs(v.killer_role_list) do
                        if v1.role_uuid == RoleWGData.Instance:GetUUIDStr() then
                            return v1.reward == 0, v1.reward == 0
                        end
                    end
                    if v.cur_kill_count < boss_info.world_firstkill_times then
                        return true, false
                    elseif v.cur_kill_count >= boss_info.world_firstkill_times then
                        return false, false
                    end
                end
            end
        end
    end
    return true, false
end

--世界首杀历史记录
function BossWGData:GetWorldFirstKilledHistoryByBossId(boss_id)
    local boss_info = self:GetFirstKillWorldInfo()
    if not IsEmptyTable(boss_info) then
        for k, v in pairs(boss_info) do
            if v.boss_id == boss_id then
               return v.killer_role_list
            end
        end
    end
    return {}
end

function BossWGData:SetCurFirstChoosedData(boss_id, boss_type)
    self.cur_firstkill_seleted_boss_id = boss_id
    self.cur_firstkill_seleted_boss_type = boss_type
end

function BossWGData:GetCurFirstChoosedData()
    return self.cur_firstkill_seleted_boss_id, self.cur_firstkill_seleted_boss_type
end

function BossWGData:GetWorldFirstKilledProgress(boss_id)
    local boss_info = self:GetBossInfoByBossId(boss_id)
    if IsEmptyTable(boss_info) then
        print_error("取不到boss配置 boss_id", boss_id)
        return 0, 0
    end
    
    local data_list = self:GetFirstKillWorldInfo()
    if not IsEmptyTable(data_list) then
        for k, v in pairs(data_list) do
            if v.boss_id == boss_id then
                if v.cur_kill_count == boss_info.world_firstkill_times then
                    for i,j in pairs(v.killer_role_list) do
                        if j.role_uuid == RoleWGData.Instance:GetUUIDStr() and j.reward == 0 then --特殊处理，如果自己没领取显示1/x
                            return v.cur_kill_count - 1, boss_info.world_firstkill_times
                        end
                    end
                end
                return v.cur_kill_count, boss_info.world_firstkill_times
            end
        end
    end
    return 0, boss_info.world_firstkill_times or 0
end

function BossWGData:IsShowRedPoint(boss_id)
    local _, can_get = self:GetIsWorldFirstKilledByBossId(boss_id)
    if can_get then
        return true
    end
	-- 屏蔽个人红包
    -- local _1, can_get1 = self:GetIsPersonFirstKilledByBossId(boss_id)
    -- if can_get1 then
    --     return true
    -- end
    return false
end

--boss列表左侧是否显示
function BossWGData:GetIsWorldLeftFirstKilledByBossId(boss_id)
    local boss_info = self:GetBossInfoByBossId(boss_id)
    if IsEmptyTable(boss_info) then
        return false
    end
    if boss_info.world_firstkill_times == -1 then
        local data_list = self:GetFirstKillInfinitePersonInfo()
        if not IsEmptyTable(data_list) then
            for k, v in pairs(data_list) do
                if v.boss_id == boss_id then
                    return v.reward == 0
                end
            end
            return true
        end
    else
        local data_list = self:GetFirstKillWorldInfo()
        if not IsEmptyTable(data_list) then
            for k, v in pairs(data_list) do
                if v.boss_id == boss_id then
                    for k1, v1 in pairs(v.killer_role_list) do
                        if v1.role_uuid == RoleWGData.Instance:GetUUIDStr() then
                            return v1.reward == 0
                        end
                    end
                    if v.cur_kill_count < boss_info.world_firstkill_times then
                        return true
                    else
                        return false
                    end
                end
            end
        end
    end
    return true
end

--boss首杀可领取时世界boss列表层数
function BossWGData:GetWorldBossLayerIndex()
    local boss_list = self:GetWorldBossList()
    for k,v in pairs(boss_list) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            return v.layer
        end
    end
    return -1
end

function BossWGData:GetWorldBossLayerList()
    local boss_list = self:GetWorldBossList()
	local red_list = {}
    for k,v in pairs(boss_list) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
			table.insert(red_list, v.layer)
        end
    end
    return red_list
end

--boss首杀可领取时Vipboss列表层数和index
function BossWGData:GetVipBossLayerIndex()
    for k,v in pairs(self.worldboss_auto.vipBoss_boss_info) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            return v.level + 1
        end
    end
    return -1
end

--boss首杀可领取时boss列表index
function BossWGData:GetBossIndexByList(list_data)
    for k,v in pairs(list_data) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            return k
        end
    end
    return -1
end

--深渊boss下层是否可击杀
function BossWGData:GetShenyuanNextLayerCanKill(cur_boss_id)
    local cfg = self:GetShenyuanBossData()
    local role_lv = RoleWGData.Instance:GetRoleLevel()
    for k,v in pairs(cfg) do
       if v.boss_id == cur_boss_id then
            if cfg[k+1] then
                if role_lv >= cfg[k+1].enter_level and cfg[k+1].status == 1 then
                    return true, cfg[k+1]
                end
            else
                return false, {}
            end
        end
    end
    return false, {}
end

--个人boss下层是否可击杀
function BossWGData:GetPersonNextLayerCanKill()
    local times = self:GetPersonalBossEnterInfo()
    if times <= 0 then
        return false
    else
        return true
    end
end


function BossWGData:GetFirstKillLog(boss_id)
    --世界首杀按钮是否显示,是否可以领取奖励
    local str = ""
    local boss_info = self:GetBossInfoByBossId(boss_id)
    if IsEmptyTable(boss_info) then
        return "取不到boss配置，boss_id" + boss_id
    end
    local data_list
    if boss_info.world_firstkill_times == -1 then
        data_list = self:GetFirstKillInfinitePersonInfo()
        str = "首杀无限次数：\n"
        local is_exist = false
        if not IsEmptyTable(data_list) then
            for k, v in pairs(data_list) do
                if v.boss_id == boss_id then
                    str = str .. "该boss协议内容" + TableToChatStr(v, 1)
                    is_exist = true
                    break
                end
            end
        end
        if not is_exist then
            str = str ..  "协议无该boss内容！\n" ..  "协议内容\n" ..  TableToChatStr(data_list, 1)
        end
    else
        data_list = self:GetFirstKillWorldInfo()
        local is_exist = false
        if not IsEmptyTable(data_list) then
            for k, v in pairs(data_list) do
                if v.boss_id == boss_id then
                    str = str ..  "是否超过首杀次数  "..  tostring(v.cur_kill_count >= boss_info.world_firstkill_times) ..
                    "\n该boss协议内容:\n" ..  TableToChatStr(v, 3) ..  "\n"
                    is_exist = true
                    for k1, v1 in pairs(v.killer_role_list) do
                        if v1.role_uuid == RoleWGData.Instance:GetUUIDStr() then
                            str = str ..  "是否可领取奖励" ..  tostring(v1.reward == 0) ..  "\n"
                        end
                    end

                end
            end
        end
        if not is_exist then
            str = str ..  "协议无该boss内容！\n" .. "协议内容\n" ..  TableToChatStr(data_list, 1)
        end
    end
    return str
end


function BossWGData:GetCrossBossVipBuyCost()
	return self.cross_other_cfg.buy_enter_times_need_gold or 0
end

function BossWGData:OnSCRefreshBossRewardInfo(protocol)
	if IsEmptyTable(self.boss_reward_thanks_giving_list) then
		self.boss_reward_thanks_giving_list = {}
	end
	table.insert(self.boss_reward_thanks_giving_list, self:GetBossThsGivenReward(protocol))
end

function BossWGData:GetBossThsGivenReward(protocol)
	local t = {}
	t.boss_type = protocol.boss_type
	t.boss_id = protocol.boss_id
	t.plat_type = protocol.plat_type
	t.user_id = protocol.user_id
	t.is_send_me = protocol.is_send_me
    t.reward_id = protocol.reward_id
	t.item_ids = {}
	local item_list = protocol.item_ids
	for k,v in pairs(item_list) do
		if v.item_id > 0 then
			t.item_ids[#t.item_ids + 1] = v
		end
	end
	return t
end

function BossWGData:GetBossThsGivenRewardData()
	return not IsEmptyTable(self.boss_reward_thanks_giving_list) and table.remove(self.boss_reward_thanks_giving_list, 1) or nil
end

function BossWGData:GetBossThsGivenRewardCfg(index)
	if IsEmptyTable(self.boss_ths_given_reward_cfg) then
		self.boss_ths_given_reward_cfg = {}
		local cfg = ConfigManager.Instance:GetAutoConfig("scene_common_cfg_auto").boss_rewards
		for k,v in pairs(cfg) do
			self.boss_ths_given_reward_cfg[v.index] = v
		end
	end

	return self.boss_ths_given_reward_cfg[index]
end

function BossWGData:GetVipBossLayerIndexByBossId(boss_id)
    local vip_boss_list = self:GetVipBossList()
    for k, v in pairs(vip_boss_list) do
        for k1,v1 in pairs(v) do
            if v1.view_resouce == boss_id then
                return k+1, k1
            end
        end
    end
   	return -1,-1
end

function BossWGData:TryGoToConveneBoss(params)
	self.cache_convene_scene_id = tonumber(params[3])
    self.cache_convene_boss_id = tonumber(params[7])
    self.cache_convene_boss_key = tonumber(params[8])
    self.cache_convene_scene_key = tonumber(params[9])
	--local boss_info = self:GetBossAllInfoByBossId(self.cache_convene_boss_id)
	--local boss_cfg = self:GetBossInfoByBossId(self.cache_convene_boss_id)
--{world_firstkill_times = 3, view_resouce = 45203, boss_type = 2, boss_view_index = 3, show_duobei = true, is_drop_jewelry = 0, layer = 5, boss_name = 审判天魔, big_icon = 1220, boss_level = 720, need_role_level = 620, kill_reduce_xianli = 40, index = 5, drop_item_list = {0 = {is_bind = 0, item_id = 37429, num = 1}, 1 = {is_bind = 0, item_id = 37406, num = 1}, 2 = {is_bind = 0, item_id = 37405, num = 1}, 3 = {is_bind = 0, item_id = 29226, num = 1}, 4 = {is_bind = 0, item_id = 26358, num = 1}, 5 = {is_bind = 0, item_id = 26359, num = 1}, 6 = {is_bind = 0, item_id = 43366, num = 1}, 7 = {is_bind = 0, item_id = 43381, num = 1}, 8 = {is_bind = 0, item_id = 43396, num = 1}, 9 = {is_bind = 0, item_id = 43411, num = 1}, 10 = {is_bind = 0, item_id = 43426, num = 1}, 11 = {is_bind = 0, item_id = 43441, num = 1}}, task_type = 12, person_firstkill_reward = 40, is_item_list = true}
	self.cache_convene_scene_x = tonumber(params[4]) or 0
    self.cache_convene_scene_y = tonumber(params[5]) or 0
   	BossWGCtrl.Instance:RequestBossExit(self.cache_convene_scene_id,self.cache_convene_scene_key,self.cache_convene_boss_id,self.cache_convene_boss_key) 	
end

function BossWGData:OnSCAckMonsterIsExsit(protocol)
	if not self.cache_convene_scene_id or
		not self.cache_convene_boss_id or
		not self.cache_convene_boss_key or
		not self.cache_convene_scene_key then
		return
	end

	if protocol.scene_id == self.cache_convene_scene_id and
		protocol.scene_key == self.cache_convene_scene_key and
		protocol.monster_id == self.cache_convene_boss_id and
		protocol.monster_key == self.cache_convene_boss_key then

		self.cache_convene_scene_x = protocol.monster_pos_x or self.cache_convene_scene_x
		self.cache_convene_scene_y = protocol.monster_pos_y or self.cache_convene_scene_y
		if protocol.is_exsit == 1 then
			local ok_func = function ()
    				--BossWGCtrl.Instance:RequestBossExit(self.cache_convene_scene_id,self.cache_convene_scene_key,self.cache_convene_boss_id,self.cache_convene_boss_key)
				BossWGData.Instance:ClearCurSelectBossID()
        		local scene_logic = Scene.Instance:GetSceneLogic()
        		if scene_logic then
        		   	scene_logic:ClearGuaJiInfo()
        		end
        		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None) --设置不自动挂机，防止寻路过程中回去打怪
				--HandleClickPoint.CheckSceneIdOperate(self.cache_convene_scene_id, {0,self.cache_convene_scene_id,self.cache_convene_scene_x,self.cache_convene_scene_y})
				local cur_scene_id = Scene.Instance:GetSceneId()
				local cfg = ConfigManager.Instance:GetSceneConfig(self.cache_convene_scene_id)
				local target_scene_type = cfg.scene_type
	
				if cur_scene_id == self.cache_convene_scene_id then
					BossWGCtrl.Instance:MoveToBoss(self.cache_convene_boss_id, SELECT_BOSS_REASON.CONVENE,self.cache_convene_scene_x,self.cache_convene_scene_y)
				elseif target_scene_type == SceneType.WorldBoss then
					local boss_info = self.worldboss_id_list[self.cache_convene_boss_id]
					BossWGData.Instance:SetCurSelectBossID(BossViewIndex.WorldBoss , boss_info.layer, self.cache_convene_boss_id,SELECT_BOSS_REASON.CONVENE,self.cache_convene_scene_x,self.cache_convene_scene_y)
					BossWGCtrl.Instance:SendWorldBossReq(BossView.ReqType.ENTER, boss_info.layer)
				elseif target_scene_type == SceneType.VIP_BOSS then
					local layer, index1 = BossWGData.Instance:GetVipBossLayerIndexByBossId(self.cache_convene_boss_id)
					BossWGData.Instance:SetCurSelectBossID(BossViewIndex.VipBoss , layer, self.cache_convene_boss_id,SELECT_BOSS_REASON.CONVENE,self.cache_convene_scene_x,self.cache_convene_scene_y)
					BossWGCtrl.Instance:OnEnterVipBoss()
				end
				self:ClearCacheConveneInfo()
    		end

			local cfg = ConfigManager.Instance:GetSceneConfig(self.cache_convene_scene_id)
			local cur_scene_id = Scene.Instance:GetSceneId()
			local monster_cfg = self:GetMonsterCfgByid(self.cache_convene_boss_id)
			local target_scene_type = cfg.scene_type

			if target_scene_type == SceneType.WorldBoss then
				local boss_cfg = self:GetBossOfferCfg(BossWGData.BossOfferType.WORLD_BOSS, self.cache_convene_boss_id)

				if not boss_cfg[1].is_open then
					return
				end
				local role_level = RoleWGData.Instance:GetRoleLevel()
				local layer_need_level = boss_cfg[1].need_role_level
				if role_level < layer_need_level then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.BossConveneLimite[1])
					--ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_world)
					return
				end

				local boss_info = self.worldboss_id_list[self.cache_convene_boss_id]
				if role_level - monster_cfg.level > boss_info.max_delta_level then
            		--BossWGData.Instance:SetBossTuJianIndex(TabIndex.boss_world, boss_info.layer, boss_info.boss_index)
           	 		--ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_world)
            		TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.BossConveneLimite[2])
					TipWGCtrl.Instance:OpenConfirmAlertTips(Language.Boss.KillLevelHighTips, ok_func)
					return
				end

				-- local cur_times, max_times = BossWGData.Instance:GetWorldBossTimes()
				-- if cur_times < 1 then
				-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.BossConveneLimite[4])
				-- 	BossWGData.Instance:SetBossTuJianIndex(TabIndex.boss_world, boss_info.layer, boss_info.boss_index)
		  --           ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_world)
		  --           return
				-- end
				ok_func()
 			elseif target_scene_type == SceneType.VIP_BOSS then
 				local boss_cfg = self:GetBossOfferCfg(BossWGData.BossOfferType.VIP_BOSS, self.cache_convene_boss_id)

 				if not boss_cfg[1].is_open then
 				end

 				local open_select_func = function ()
 					local layer, index1 = BossWGData.Instance:GetVipBossLayerIndexByBossId(self.cache_convene_boss_id)
 					if layer ~= -1 then
        		    	BossWGData.Instance:SetBossTuJianIndex(TabIndex.boss_vip, layer, index1)
        		    	ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
        		    else
        		    	ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
        		    	print_error("vip_boss without this boss_id")
        		    end
 				end

 				local role_level = RoleWGData.Instance:GetRoleLevel()
				local layer_need_level = boss_cfg[1].need_role_level
				if role_level < layer_need_level then
					TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.BossConveneLimite[1])
					--ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
					return
				end
		
				local boss_info = self.vipboss_id_list[self.cache_convene_boss_id]
				if role_level - monster_cfg.level > boss_info.max_delta_level then
					--open_select_func()
				TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.BossConveneLimite[2])
				TipWGCtrl.Instance:OpenConfirmAlertTips(Language.Boss.KillLevelHighTips, ok_func)
				return
			end

			local enter_vip_cfg = BossWGData.Instance:GetEnterVipCfgbyIndex(boss_info.level + 1)
			local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
			-- local xian_li = BossAssistWGData.Instance:GetBossXianli()
			-- if xian_li < boss_info.kill_reduce_xianli then
			-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.BossConveneLimite[3])
			-- 	open_select_func()
			-- 	return
			-- end

			if cur_scene_id == self.cache_convene_scene_id then
				ok_func()
				return
			end
	
			if role_vip_level < enter_vip_cfg.pay_vip_level then --vip等级不满足进入等级
				open_select_func()
				ok_func()
				return
			elseif role_vip_level < enter_vip_cfg.free_vip_level then
				open_select_func()
				ok_func()
				return
			end
				ok_func()
 			else
 				return
 			end
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Boss.BossConveneDead)
		end
	end
end

function BossWGData:ClearCacheConveneInfo()
	self.cache_convene_scene_id = nil
    self.cache_convene_boss_id = nil
    self.cache_convene_boss_key = nil
    self.cache_convene_scene_key = nil
    self.cache_convene_scene_x = nil
    self.cache_convene_scene_y = nil
end

function BossWGData:GetIsAlreadyGuide()
	if self.already_show_notice then
		return self.already_show_notice
	end
	local role_uuid = RoleWGData.Instance:GetUUIDStr()
	local flag = PlayerPrefsUtil.GetInt(role_uuid.."boss_notice")
	if flag and flag == 1 then
		self.already_show_notice = true
	end
	return self.already_show_notice
end

function BossWGData:SetIsAlredyGuide()
	local role_uuid = RoleWGData.Instance:GetUUIDStr()
	PlayerPrefsUtil.SetInt(role_uuid.."boss_notice", 1)
end


function BossWGData:GetIsShowBossNoticeGuide(boss_id)
	local cur_suit_index = BossNoticeTargetTipsWGData.Instance:GetCurGradeStatus2()
	if cur_suit_index == -1 then
		return false
	end

	if self:GetIsAlreadyGuide() then
		return false
	end

	local need_cfg = BossNoticeTargetTipsWGData.Instance:GetNeedEquipCfg()
    local need_equip_grade = need_cfg.c_param2
    local boss_cfg = self:GetBossInfoByBossId(boss_id)
    if boss_cfg.boss_jie ~= need_equip_grade then
    	return false
    end

    local need_xianli = need_cfg.kill_reduce_xianli
    local have_xianli = BossAssistWGData.Instance:GetBossXianli()
    if need_xianli > have_xianli then
    	return false
    end
	local cur_layer = boss_cfg.level
	local best_boss_id = -1
	local best_boss_index = -1
	for k,v in pairs(self.vip_layer_boss_list[cur_layer]) do
		if v == need_equip_grade then
			local boss_info = self:GetBossRefreshInfoByBossId(k)
    		if boss_info then
    		    if boss_info.next_refresh_time <= TimeWGCtrl.Instance:GetServerTime() then
    		    	local boss_cfg1 = self:GetBossInfoByBossId(k)
    		    	if best_boss_id == -1 and boss_cfg1 then
    		    		best_boss_id = k
    		    		best_boss_index = boss_cfg1.boss_index
    		    	elseif boss_cfg1 and best_boss_index > boss_cfg1.boss_index then
    		    		best_boss_id = k
    		    		best_boss_index = boss_cfg1.boss_index
    		    	end
    		    end
    		end
    	end
	end
	if boss_id == best_boss_id then
		return true
	end
	return false
end

function BossWGData:GetConveneOpenLevel()
    return self.other_cfg[1].convene_level or 0
end

function BossWGData:SetCurFirstKillShowView(show_index)
	self.cur_first_show_index = show_index
end

function BossWGData:GetCurFirstKillShowView()
	return self.cur_first_show_index
end

function BossWGData:SetCurFirstkillLayer(value)
    self.cur_firstkill_layer = value
end

function BossWGData:GetCurFirstkillLayer()
	return self.cur_firstkill_layer
end

function BossWGData:GetWorldBossFirstKillRed()
	local boss_list = self:GetWorldBossList()
	if boss_list == nil then
		return 0, nil
	end

    for k,v in pairs(boss_list) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            return 1, BossViewIndex.WorldBoss

        end
    end

	return 0, nil
end

function BossWGData:GetVipBossFirstKillRed()
	for k,v in pairs(self.worldboss_auto.vipBoss_boss_info) do
        local _, can_get = self:GetIsWorldFirstKilledByBossId(v.boss_id)
        --local _1, person_can_get = self:GetIsPersonFirstKilledByBossId(v.boss_id)
        if can_get then
            return 1, BossViewIndex.VipBoss
        end
    end

	return 0, nil
end

function BossWGData:GetBossFirstKillRed()
	local world_boss_red, _ = BossWGData.Instance:GetWorldBossFirstKillRed()
    local vip_boss_red, _ = BossWGData.Instance:GetVipBossFirstKillRed()

	if world_boss_red > 0 or vip_boss_red > 0 then
		return 1
	end

	return 0
end

function BossWGData:SetPersonSpecialBossSceneInfo(protocol)
	self.personal_special_boss_info = protocol.info
end

function BossWGData:GetPersonSpecialBossSceneInfo()
	return self.personal_special_boss_info
end

function BossWGData:GetPersonSpecialBossSceneCfg()
	local info = self:GetPersonSpecialBossSceneInfo()
	if not info then
		return nil
	end

	local cfg = ConfigManager.Instance:GetAutoConfig("person_special_boss_cfg_auto").monster
	for k,v in ipairs(cfg) do
		if v.seq == info.seq and v.monster_id == info.monster_id then
			return v
		end
	end

	return nil
end

-- 根据怪物id 获取模型展示需要的信息
function BossWGData.GetMonsterResByMonsterId(monster_id)
	local monster_list = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list
	local monster_cfg = monster_list and monster_list[monster_id]
	local bundle, asset, model_type, res_id = nil, nil, 0, 0

	if not monster_cfg then
		return model_type, bundle, asset, res_id, nil
	end

	model_type = monster_cfg.boss_type2
	res_id = monster_cfg.resid
	if model_type == ClientBossType.TSBoss then
		bundle, asset = ResPath.GetBianShenModel(monster_cfg.resid)
	elseif model_type == ClientBossType.WeaponAniTSBoss then
		local tianshen_cfg = TianShenWGData.Instance:GetImageModelByAppeId(monster_cfg.resid)
		if tianshen_cfg then
			return model_type, bundle, asset, res_id, tianshen_cfg.index
		end
	else
		bundle, asset = ResPath.GetMonsterModel(monster_cfg.resid)
	end

	return model_type, bundle, asset, res_id, nil
end

function BossWGData:BossDataTableEquals(table1, table2)
    if type(table1) ~= "table" or type(table2) ~= "table" then
        return false
    end
    for key1, value1 in pairs(table1) do
        local value2 = table2[key1]
        if type(value1) == "table" and type(value2) == "table" then
            if not self:BossDataTableEquals(value1, value2) then
                return false
            end
        elseif value1 ~= value2 then
            return false
        end
    end

    for key2, _ in pairs(table2) do
        if table1[key2] == nil then
            return false
        end
    end
    return true
end

------------------------------------臻充boss----------------------------------------
function BossWGData:SetCrossRealChargeBossAllInfo(protocol)  -- 场景击杀boss数量 20个长度
	self.cross_real_charge_boss_scenc = protocol.cross_real_charge_boss_scenc             --data {{scene_id = , kill_boss_num = },{scene_id = , kill_boss_num = }}
	self.cross_real_charge_scenc_kill_info = protocol.cross_real_charge_scenc_kill_info  -- data   {[scene_id] = kill_boss_num,[scene_id] = kill_boss_num}
end

function BossWGData:UpdateCrossRealChargeBossMonster(protocol)  --更新怪物信息，刷新时间
	local erb_monster_info = protocol.monster_info
	self.erb_monster_id_info_list[erb_monster_info.monster_id] = erb_monster_info
	self.erb_monster_scene_info_list[erb_monster_info.scene_id] = self.erb_monster_scene_info_list[erb_monster_info.scene_id] or {}
	self.erb_monster_scene_info_list[erb_monster_info.scene_id][erb_monster_info.scene_index] = erb_monster_info
end

function BossWGData:SetCrossRealChargeBossMonsterList(protocol) -- 20长度的怪物信息   刷新时间
	local monster_info_list = protocol.monster_info_list
	self.erb_monster_info_list = monster_info_list

	for k, v in pairs(monster_info_list) do
		self.erb_monster_id_info_list[v.monster_id] = v
		self.erb_monster_scene_info_list[v.scene_id] = self.erb_monster_scene_info_list[v.scene_id] or {}
		self.erb_monster_scene_info_list[v.scene_id][v.scene_index] = v
	end
end

function BossWGData:GetCurErbLayerCfg()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cur_scene_group = 0

	for k, v in pairs (self.erb_layer_relation_cfg) do
		if role_level >= v.need_role_level_min and role_level <= v.need_role_level_max then
			cur_scene_group = v.scene_group
			break
		end
	end
	local cur_layer_relation_cfg = self.erb_layer_relation_cfg[cur_scene_group] or {}
	local next_layer_relation_cfg = self.erb_layer_relation_cfg[cur_scene_group + 1] or {}

	return cur_layer_relation_cfg, next_layer_relation_cfg
end

function BossWGData:GetCurERBTypeList()
	local data_list = {}
	
	local role_level = RoleWGData.Instance:GetRoleLevel()
	for k, v in pairs (self.erb_layer_relation_cfg) do
		if role_level >= v.need_role_level_min and role_level <= v.need_role_level_max then
			return self:GetERBConditionDataList(v.boss_group)
		end
	end

	return data_list
end

function BossWGData:GetERBConditionDataList(boss_group)
	return self.erb_condition_cfg[boss_group] or {}
end

function BossWGData:GetERBConditionData(boss_group, boss_group_index)
	return (self.erb_condition_cfg[boss_group] or {})[boss_group_index]
end

function BossWGData:GetERBBossListBySceneIndex(scene_index)
	return self.erb_boss_layer_index_list[scene_index] or {}
end

function BossWGData:GetRechargeBossTypeList()
	return self.erb_condition_cfg
end

function BossWGData:GetERBBossInfoBySceneIdAndSceneIndex(scene_id, scene_index)
	return (self.erb_monster_scene_info_list[scene_id] or {})[scene_index] or {}
end

function BossWGData:GetERBBossInfoByBossId(boss_id)
	return self.erb_monster_id_info_list[boss_id] or {}
end

function BossWGData:GetERBSceneKillBossNumBySceneId(scene_id)
	return (self.cross_real_charge_scenc_kill_info[scene_id] or {}).kill_boss_num or 0
end

function BossWGData:GetSceneSpecialBossCfgBySceneId(scene_id)
	return self.erb_scene_special_boss_list[scene_id]
end

function BossWGData:IsERBSpecialBoss(boss_id)
	return nil ~= self.erb_special_boss_cfg_cache[boss_id]
end

function BossWGData:GetERBSpecialBossCfgByBossId(boss_id)
return self.erb_special_boss_cfg_cache[boss_id]
end

function BossWGData:GetERBBossData(need_reset)
    if not self.erb_common_boss_list or need_reset then
    	local boss_list = {}
        for m, boss_info in ipairs(self.erb_boss_list_cfg) do
            local boss_cfg = self.crossboss_monster_list[boss_info.boss_id]
            local t = {}
            self:ResetIndexBossCfg(t, boss_info)

            t.boss_name = boss_cfg.name
            t.boss_level = boss_cfg.level
            t.big_icon = boss_cfg.small_icon
            t.resid = boss_cfg.resid
            t.scale = t.scale or 1
            t.x_pos = boss_info.x_pos
            t.y_pos = boss_info.y_pos
            t.drop_item_list = boss_info.drop_item_list --归属展示
            t.type = BossWGData.MonsterType.Boss
            t.server_count_limit = boss_cfg.server_count_limit
			t.max_delta_level = COMMON_CONSTS.MaxRoleLevel
            table.insert(boss_list, t)
		end
		self.erb_common_boss_list = boss_list
	end

	local erb_boss_layer_index_list = {}
	local erb_scene_special_boss_list = {}
	local erb_special_boss_cfg_cache = {}
	local erb_boss_id_cfg_cache = {}
	for i, v in pairs(self.erb_common_boss_list) do --击杀信息，刷新时间等
		local info = self:GetERBBossInfoBySceneIdAndSceneIndex(v.scene_id, v.scene_index)
		v.next_refresh_time = info.next_refresh_time or 0
		v.alive = info.alive
		erb_boss_layer_index_list[v.scene_index] = erb_boss_layer_index_list[v.scene_index] or {}
		erb_boss_layer_index_list[v.scene_index][v.boss_index] = v

		if v.is_special_boss == 1 then
			erb_scene_special_boss_list[v.scene_id] = v
			erb_special_boss_cfg_cache[v.boss_id] = v
		end

		erb_boss_id_cfg_cache[v.boss_id] = v
	end
	
	self.erb_boss_layer_index_list = erb_boss_layer_index_list
	self.erb_scene_special_boss_list = erb_scene_special_boss_list
	self.erb_special_boss_cfg_cache = erb_special_boss_cfg_cache
	self.erb_boss_id_cfg_cache = erb_boss_id_cfg_cache

	return self.erb_common_boss_list
end

function BossWGData:GetERBBossBaseCfgByBossId(boss_id)
	return self.erb_boss_id_cfg_cache[boss_id]
end

function BossWGData:GetERBBossRewardShowListInfo(boss_id)
	local boss_reward_show_list_info = {}
	local boss_cfg = self:GetERBBossBaseCfgByBossId(boss_id)

	if IsEmptyTable(boss_cfg) then
		return boss_reward_show_list_info
	end

	boss_reward_show_list_info.boss_id = boss_id
	boss_reward_show_list_info.drop_item_list = boss_cfg.boss_reward_show_list
	boss_reward_show_list_info.reward_tips_des = boss_cfg.reward_tips_des

	return boss_reward_show_list_info
end

function BossWGData:SetCrossRealChangeBossRecordList(protocol)
	self.erb_record_list = protocol.record_list
end

function BossWGData:GetERBDropList()
	return self.erb_record_list
end

function BossWGData:IsBossQuickRebirthNotCountDown()
	if CountDownManager.Instance:HasCountDown(CountDownKey) then
		return false
	else
		CountDownManager.Instance:AddCountDown(CountDownKey,
		nil,
		-- 倒计时完成回调方法
		function()
			if CountDownManager.Instance:HasCountDown(CountDownKey) then
				CountDownManager.Instance:RemoveCountDown(CountDownKey)
			end
		end,
		nil, 180)
		return true
	end
end

--获取Boss首杀信息.
function BossWGData:GetBossFirstKiller(boss_id)
	local boss_server_info = self:GetBossRefreshInfoByBossId(boss_id)
	if boss_server_info and boss_server_info.first_killer then
		return boss_server_info.first_killer
	end
end

--判断Boss是否需要死亡效率评分.
function BossWGData:CheackNeedBossDeadEfficiency(boss_id)
	local boss_data = self:GetMonsterInfo(boss_id)
	return boss_data and boss_data.calc_monster_hp_record and boss_data.calc_monster_hp_record == 1 or false
end

--设置boss秒杀的信息
function BossWGData:SetSecKillBossInfo(protocol)
	self.sec_kill_uuid = protocol.sec_kill_uuid								-- 触发了boss秒杀的玩家uuid
	self.sec_kill_start_time = protocol.sec_kill_start_time					-- 触发秒杀的开始时间
	self.sec_kill_end_time = protocol.sec_kill_end_time						-- 可以秒杀的结束时间
	self.sec_kill_type = protocol.sec_kill_type								-- 秒杀类型
	self.sec_kill_monster_obj_id = protocol.monster_obj_id							-- 秒杀的boss objid
end

-- 是否是主角触发了秒杀
function BossWGData:IsSelfTriggerSecSkill()
	local main_role_uuid = RoleWGData.Instance:GetUUid()
	if IsEmptyTable(main_role_uuid) or IsEmptyTable(self.sec_kill_uuid)  then
		return false
	end

	return main_role_uuid.temp_low == self.sec_kill_uuid.temp_low and main_role_uuid.temp_high == self.sec_kill_uuid.temp_high
end

function BossWGData:GetSecKillBossTimeData()
	return self.sec_kill_start_time, self.sec_kill_end_time
end

function BossWGData:GetSecKillBossType()
	return self.sec_kill_type
end

function BossWGData:GetSecKillBossObjId()
	return self.sec_kill_monster_obj_id
end

function BossWGData:GetMonsterShieldOtherCfg()
	return self.monster_shield_other_cfg
end

function BossWGData:GetMonsterShieldSecKillCfgByType(sec_kill_type)
	return self.monster_shield_sec_kill_cfg[sec_kill_type]
end

--获得悬赏所有可领取奖励的列表.
function BossWGData:GetBossFirstKillCanGetRewardListByLayer(tab_index, layer)
	local can_get_list = {}
	local list_data = {}
    local cur_layer = layer
	if cur_layer == nil then
		return can_get_list
	end

    if tab_index == BossViewIndex.VipBoss then --混沌魔域
        list_data = BossWGData.Instance:GetVipBossListByIndex(cur_layer)
    elseif tab_index == BossViewIndex.WorldBoss then --蛮荒魔谷
        list_data = BossWGData.Instance:GetWorldBossListByLayer(cur_layer)
    end

	if list_data then
		for k, v in pairs(list_data) do
			if BossWGData.Instance:IsShowRedPoint(v.boss_id) then
				table.insert(can_get_list, v)
			end
		end
	end

	return can_get_list
end
