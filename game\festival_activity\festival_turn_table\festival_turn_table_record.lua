
FestivalActivityTurnRecord = FestivalActivityTurnRecord or BaseClass(SafeBaseView)

function FestivalActivityTurnRecord:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "FestivalTurnRecord"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(940, 590)})
    self:AddViewResource(0, "uis/view/festival_activity_ui/trun_table_prefab", "layout_fastival_turn_record")
    self:SetMaskBg(true, true)
end

function FestivalActivityTurnRecord:ReleaseCallBack()
    if self.world_record_list then
		self.world_record_list:DeleteMe()
		self.world_record_list = nil
	end

end

function FestivalActivityTurnRecord:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ZhouYiYunCheng.BigRewardTiele
	self.cur_result_index = 1

	if nil == self.world_record_list then
		self.world_record_list = AsyncListView.New(FestivalTableListCell, self.node_list.turn_table_record_list1)
	end
	for i = 1 ,2 do
		self.node_list["turn_table_toggle_btn"..i].button:AddClickListener(BindTool.Bind(self.TurnTableOnClickRecordChange, self, i))
	end
end


function FestivalActivityTurnRecord:TurnTableOnClickRecordChange(index)
	self.node_list.bg_HL_1:SetActive(index == 1)
    self.node_list.bg_HL_2:SetActive(index == 2)
	if index == 1 and self.cur_result_index ~= index then
		self.cur_result_index = index
		self:TurnTableFlushWorld()
	elseif index == 2 and self.cur_result_index ~= index then
		self.cur_result_index = index
		self:TurnTableFlushPersonal()
	end
end

function FestivalActivityTurnRecord:TurnTableFlushWorld()
	if self.cur_result_index ~= 1 then return end
	local world_data = FestivalTurnTableWGData.Instance:GetWorldRecord()
	self:TurnTableSetInfoBg(world_data)
	if nil == self.world_record_list then return end
	self.world_record_list:SetDataList(world_data)
end

function FestivalActivityTurnRecord:TurnTableFlushPersonal()
	if self.cur_result_index ~= 2 then return end
	local personal_data = FestivalTurnTableWGData.Instance:GetPersonalRecord()
	self:TurnTableSetInfoBg(personal_data)
	if nil == self.world_record_list then return end
	self.world_record_list:SetDataList(personal_data)
end

function FestivalActivityTurnRecord:TurnTableSetInfoBg(data)
	local is_have_info = not IsEmptyTable(data)
	self.node_list.turn_table_not_info:SetActive(not is_have_info)
end

function FestivalActivityTurnRecord:OnFlush()
    if FestivalTurnTableWGData.Instance:CheckNeedReqInfo() then return end

	self:TurnTableFlushWorld()
	self:TurnTableFlushPersonal()
end


