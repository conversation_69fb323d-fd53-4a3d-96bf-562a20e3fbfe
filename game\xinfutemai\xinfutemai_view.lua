XinFuTeMaiView = XinFuTeMaiView or BaseClass(SafeBaseView)

function XinFuTeMaiView:__init()
    self:SetMaskBg(false,false)
    self:AddViewResource(0, "uis/view/xinfutemai_prefab", "layout_xinfutemai")
end

function XinFuTeMaiView:__delete()

end

function XinFuTeMaiView:ReleaseCallBack()
    if CountDown.Instance:HasCountDown(self.quest) then
        CountDown.Instance:RemoveCountDown(self.quest)
        self.quest = nil
    end
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    if nil ~= self.buy_alert then						
        self.buy_alert:DeleteMe()
        self.buy_alert = nil
    end
end

function XinFuTeMaiView:LoadCallBack()
    self.item_list = AsyncListView.New(XinFuTeMaiItemRender, self.node_list.item_list)
    XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind1(self.OnClickShouXiaBaoBei, self))
    XUI.AddClickEventListener(self.node_list.yuanpan_bg, BindTool.Bind1(self.OnClickShouXiaBaoBei, self))
    XUI.AddClickEventListener(self.node_list.btn_canel, BindTool.Bind1(self.OnClickCancel, self))
end

function XinFuTeMaiView:ShowIndexCallBack(index)
    self:Flush()
end

function XinFuTeMaiView:OnFlush()
    local cfg = XinFuTeMaiWGData.Instance:GetCfg()
    if not cfg then return end
    local data_list = {}
    for i, v in pairs(cfg.reward_item) do
        table.insert(data_list, v)
    end
    --print_error(data_list)
    self.item_list:SetDataList(data_list, 3)
    self.node_list["yuanjia_text"].text.text = string.format(Language.XinFuTeMai.YuanJia, cfg.rmb_show)
    self.node_list["xianjia_text"].text.text = cfg.rmb_price

    if CountDown.Instance:HasCountDown(self.quest) then
        CountDown.Instance:RemoveCountDown(self.quest)
        self.quest = nil
    end

    local info = XinFuTeMaiWGData.Instance:GetTeMaiInfo()

    if info.reward_active_flag == 0 then
        self.node_list["btn_pay_text"].text.text = Language.XinFuTeMai.BaoWu
    elseif info.reward_active_flag == 1 and info.reward_fetch_flag == 0 then
        self.node_list["btn_pay_text"].text.text = Language.XinFuTeMai.LingQu
    elseif info.reward_active_flag == 1 and info.reward_fetch_flag == 1 then
        self.node_list["btn_pay_text"].text.text = Language.XinFuTeMai.YiLingQu
    end

    local remain_time = info.end_time_stamp - TimeWGCtrl.Instance:GetServerTime()
    self:ChangeTime(0, remain_time)
    self.quest = CountDown.Instance:AddCountDown(remain_time, 1, BindTool.Bind(self.ChangeTime, self),BindTool.Bind(self.CompleteTime, self))
end

function XinFuTeMaiView:ChangeTime(elapse_time, total_time)
    self.node_list["remain_time_text"].text.text = string.format(Language.XinFuTeMai.Time, TimeUtil.MSTime(math.floor(total_time - elapse_time)))
end

function XinFuTeMaiView:CompleteTime()
    self:Close()
    --隐藏主界面按钮
end

function XinFuTeMaiView:OnClickCancel()
    self:Close()
end

function XinFuTeMaiView:OnClickShouXiaBaoBei()
    local info = XinFuTeMaiWGData.Instance:GetTeMaiInfo()
    if info.reward_active_flag == 0 then
        if self.buy_alert == nil then
            self.buy_alert = Alert.New()
            self.buy_alert:SetOkFunc(BindTool.Bind(self.OnOkClick, self))
        end
        local cfg = XinFuTeMaiWGData.Instance:GetCfg()
        if not cfg then return end
        self.buy_alert:SetLableString(string.format(Language.XinFuTeMai.ChongZhiRMB, cfg.rmb_price))
        self.buy_alert:Open()
    elseif info.reward_active_flag == 1 and info.reward_fetch_flag == 0 then
        --发送领取
        XinFuTeMaiWGCtrl.Instance:NewShopFetchRewardReq()
    elseif info.reward_active_flag == 1 and info.reward_fetch_flag == 1 then
        XUI.SetButtonEnabled(self.node_list["btn_buy"], false)
        MainuiWGCtrl.Instance:DelTempActIcon(ACTIVITY_TYPE.XinFuTeMai)
    end
end

function XinFuTeMaiView:OnOkClick()
    RechargeWGCtrl.Instance:Recharge(XinFuTeMaiWGData.Instance:GetCfg().rmb_price)
end


------------------------- XinFuTeMaiItemRender ----------------------
XinFuTeMaiItemRender = XinFuTeMaiItemRender or BaseClass(BaseRender)
function XinFuTeMaiItemRender:__init()
    self.base_cell = ItemCell.New(self.node_list["item_slot"])
end

function XinFuTeMaiItemRender:__delete()
    if self.base_cell ~= nil then
        self.base_cell:DeleteMe()
        self.base_cell = nil
    end
end

function XinFuTeMaiItemRender:LoadCallBack()
    --XUI.AddClickEventListener(self.node_list.img_btn, BindTool.Bind1(self.OnClickBuy, self), true)
    --XUI.AddClickEventListener(self.node_list.img_item, BindTool.Bind1(self.OnClickShowItemInfo, self), true)
end
function XinFuTeMaiItemRender:OnFlush()
    if not self.data then return end
    local item_config = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    self.base_cell:SetData({item_id = self.data.item_id, num = self.data.num})
    self.node_list.item_name.text.text = item_config.name
    --self.node_list.item_num.text.text = self.data.num
    --local b,a = ResPath.GetItem(item_config.icon_id)
    --self.node_list.item_img.image:LoadSprite(b,a, function()
    --    XUI.ImageSetNativeSize(self.node_list.item_img)
    --end)
end