function NewFestivalActivityView:ReleaseCallBackRank()
	if CountDownManager.Instance:HasCountDown("cross_consume_rank_down") then
		CountDownManager.Instance:RemoveCountDown("cross_consume_rank_down")
	end

	if self.jrph_show_model then
		self.jrph_show_model:DeleteMe()
		self.jrph_show_model = nil
	end

	if self.rank_reward_list then
		self.rank_reward_list:DeleteMe()
		self.rank_reward_list = nil
	end

	if self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function NewFestivalActivityView:LoadIndexCallBackRank()
	if not self.jrph_show_model then
		self.jrph_show_model = OperationActRender.New(self.node_list.jrph_model_pos)
		self.jrph_show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.rank_reward_list then
		self.rank_reward_list = AsyncListView.New(NewFesRankRewardList, self.node_list["dr_rank_reward_list"])
	end

	if not self.rank_list then
		self.rank_list = AsyncListView.New(NewFesRankList, self.node_list["dr_rank_list"])
	end

	self.node_list.dr_reward_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnTogValueChange, self, 1))
	self.node_list.dr_rank_tog.toggle:AddValueChangedListener(BindTool.Bind(self.OnTogValueChange, self, 2))
	XUI.AddClickEventListener(self.node_list.go_collect_btn, BindTool.Bind1(self.OnClickGoCollect, self))
	self.node_list.dr_reward_tog.toggle.isOn = true

	self:LoadRankUi()
	self:FlushDREndTime()
end

function NewFestivalActivityView:ShowIndexCallBackRank()
	NewFestivalRankWGCtrl.Instance:CSReqNewFesRankInfo(OA_NEW_FES_ACT_RANK_OPERATE_TYPE.INFO)
end

function NewFestivalActivityView:LoadRankUi()
	self:FlushRankModel()

	local dr_bg_bundle, dr_bg_asset = ResPath.GetNewFestivalRawImages("jrsc_bg")
	self.node_list["dr_bg"].raw_image:LoadSprite(dr_bg_bundle, dr_bg_asset, function()
		self.node_list["dr_bg"].raw_image:SetNativeSize()
	end)

	local dr_title_bundle, dr_title_asset = ResPath.GetNewFestivalRawImages("dr_title")
	self.node_list["dr_title"].raw_image:LoadSprite(dr_title_bundle, dr_title_asset, function()
		self.node_list["dr_title"].raw_image:SetNativeSize()
	end)

	local dr_act_bg_bundle, dr_act_bg_asset = ResPath.GetNewFestivalRawImages("jrsc_time_bg")
	self.node_list["dr_act_bg"].raw_image:LoadSprite(dr_act_bg_bundle, dr_act_bg_asset, function()
		self.node_list["dr_act_bg"].raw_image:SetNativeSize()
	end)

	local dr_rank_bg_bundle, dr_rank_bg_asset = ResPath.GetNewFestivalRawImages("dr_rank_bg")
	self.node_list["dr_rank_bg"].raw_image:LoadSprite(dr_rank_bg_bundle, dr_rank_bg_asset, function()
		self.node_list["dr_rank_bg"].raw_image:SetNativeSize()
	end)

	local top_bundle, top_asset = ResPath.GetNewFestivalActImages("a3_jrhd_rank_top")
	self.node_list["top"].image:LoadSprite(top_bundle, top_asset)

	local go_collect_bundle, go_collect_asset = ResPath.GetNewFestivalActImages("a3_jrhd_dr_btn")
	self.node_list["go_collect_btn"].image:LoadSprite(go_collect_bundle, go_collect_asset, function()
		self.node_list["go_collect_btn"].image:SetNativeSize()
	end)

	local btn_bundle, btn_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xcqy_btn1")
	self.node_list["dr_reward_tog_nor_img"].image:LoadSprite(btn_bundle, btn_asset, function()
		self.node_list["dr_reward_tog_nor_img"].image:SetNativeSize()
	end)

	self.node_list["dr_rank_tog_nor_img"].image:LoadSprite(btn_bundle, btn_asset, function()
		self.node_list["dr_rank_tog_nor_img"].image:SetNativeSize()
	end)

	local btn_hl_bundle, btn_hl_asset = ResPath.GetNewFestivalActImages("a3_jrhd_xcqy_btn1_hl")
	self.node_list["dr_reward_tog_hl_img"].image:LoadSprite(btn_hl_bundle, btn_hl_asset, function()
		self.node_list["dr_reward_tog_hl_img"].image:SetNativeSize()
	end)

	self.node_list["dr_rank_tog_hl_img"].image:LoadSprite(btn_hl_bundle, btn_hl_asset, function()
		self.node_list["dr_rank_tog_hl_img"].image:SetNativeSize()
	end)

	local dr_info_cfg = NewFestivalActivityWGData.Instance:GetNewFesActDRCfg()
	self.node_list["dr_activity_time"].text.color = Str2C3b(dr_info_cfg.act_color)
	self.node_list["dr_rank_txt"].text.color = Str2C3b(dr_info_cfg.my_rank_color)
	self.node_list["dr_collect_num"].text.color = Str2C3b(dr_info_cfg.collect_color)
	for i = 1, 3 do
		self.node_list["dr_top_text" .. i].text.color = Str2C3b(dr_info_cfg.top_color)
	end
end

function NewFestivalActivityView:FlushDREndTime()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.NEW_JRHD_JRPH)
	if activity_data ~= nil then
		--结束时间戳
		local invalid_time = activity_data.end_time
		--记录时间戳
		if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
			-- self.node_list["dr_activity_time"].text.text = TimeUtil.FormatSecondDHM2(invalid_time -
			-- 	TimeWGCtrl.Instance:GetServerTime())
			CountDownManager.Instance:AddCountDown("cross_consume_rank_down", BindTool.Bind1(self.UpdateDRTimeStr, self),
				BindTool.Bind1(self.OnComplete, self), invalid_time, 1)
		else
			self:OnComplete()
		end
	end
end

function NewFestivalActivityView:UpdateDRTimeStr(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		local dr_info_cfg = NewFestivalActivityWGData.Instance:GetNewFesActDRCfg()
		local time_part_color = dr_info_cfg and dr_info_cfg.time_part_color or COLOR3B.D_GREEN
		self.node_list["dr_activity_time"].text.text = string.format(Language.NewFestivalActivity.ActTime, time_part_color,
			TimeUtil.FormatSecondDHM2(valid_time))
	end
end

function NewFestivalActivityView:OnComplete()
	self.node_list["dr_activity_time"].text.text = Language.Common.ActivityIsEnd
	self:Close()
end

function NewFestivalActivityView:OnFlushRank(param_t)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
		end
	end
end

function NewFestivalActivityView:FlushView()
	self:OnTogValueChange(1, true)
end

function NewFestivalActivityView:OnTogValueChange(index, is_on)
	if is_on then
		if index == 1 then
			self:FlushRankRewardPanel()
		else
			self:FlushRankListPanel()
		end
	end
end

function NewFestivalActivityView:FlushMyRankInfo()
	if not self.node_list.dr_rank_root:GetActive() then
		self.node_list.dr_rank_root:SetActive(true)
	end

	local my_uuid = RoleWGData.Instance:GetUUid()
	local my_rank = 0
	local rank_data_list = NewFestivalRankWGData.Instance:GetRankList()
	for k, v in pairs(rank_data_list) do
		if v.rank_data.uuid and my_uuid == v.rank_data.uuid then
			my_rank = v.rank_data.rank
		end
	end

	local rank_str = my_rank > 0 and my_rank or Language.NewFestivalActivity.NoRank
	self.node_list.dr_rank_txt.text.text = string.format(Language.NewFestivalActivity.DRMyRank, rank_str)

	local rank_value = NewFestivalRankWGData.Instance:GetMyRankValue()
	self.node_list.dr_collect_num.text.text = string.format(Language.NewFestivalActivity.DRCollectNum, rank_value)
end

function NewFestivalActivityView:FlushRankRewardPanel()
	local reward_cfg = NewFestivalRankWGData.Instance:GetRewardCfg(1)

	self:FlushMyRankInfo()

	if reward_cfg then
		self.rank_reward_list:SetDataList(reward_cfg)
	end
end

function NewFestivalActivityView:FlushRankListPanel()
	local rank_data_list = NewFestivalRankWGData.Instance:GetRankList()
	if IsEmptyTable(rank_data_list) then
		return
	end

	self:FlushMyRankInfo()
	self.rank_list:SetDataList(rank_data_list)
end

function NewFestivalActivityView:FlushRankModel()
	local cur_grade = NewFestivalRankWGData.Instance:GetCurGrade()
	local model_data = NewFestivalRankWGData.Instance:GetModelCfg(cur_grade)
	if IsEmptyTable(model_data) then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if model_data.model_show_itemid ~= 0 and model_data.model_show_itemid ~= "" then
		local split_list = string.split(model_data.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_data.model_show_itemid
		end
	end

	display_data.bundle_name = model_data["model_bundle_name"]
	display_data.asset_name = model_data["model_asset_name"]
	local model_show_type = tonumber(model_data["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1
	if display_data.render_type == OARenderType.Mecha then
		display_data.mecha_seq = model_data.model_show_itemid
	end

	local pos_x, pos_y, pos_z = 0, 0, 0
	if model_data.display_pos and model_data.display_pos ~= "" then
		local pos_list = string.split(model_data.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		pos_z = tonumber(pos_list[3]) or pos_z
	end
	RectTransform.SetAnchoredPosition3DXYZ(self.node_list.jrph_model_pos.rect, pos_x, pos_y, pos_z)

    local rot_x, rot_y, rot_z = 0, 0, 0
	if model_data.display_rotation and model_data.display_rotation ~= "" then
        local rot_list = Split(model_data.display_rotation, "|")
        rot_x = tonumber(rot_list[1]) or rot_x
        rot_y = tonumber(rot_list[2]) or rot_y
        rot_z = tonumber(rot_list[3]) or rot_z
	end
    display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)

	local scale = model_data["display_scale"]
    display_data.scale = (scale and scale ~= "" and scale > 0) and scale or 1
    display_data.model_adjust_root_local_scale = scale
    display_data.model_rt_type = ModelRTSCaleType.M

	self.jrph_show_model:SetData(display_data)
end

function NewFestivalActivityView:OnClickGoCollect()
	local cur_grade = NewFestivalRankWGData.Instance:GetCurGrade()
	local grade_cfg = NewFestivalRankWGData.Instance:GetCurGradeCfg(cur_grade)
	if IsEmptyTable(grade_cfg) then
		return
	end

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(grade_cfg.act_type)
	if is_open then
		ViewManager.Instance:Open(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2352)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.NotOpenAct)
	end
end

---------------------NewFesRankRewardList---------------------
NewFesRankRewardList = NewFesRankRewardList or BaseClass(BaseRender)
function NewFesRankRewardList:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end
end

function NewFesRankRewardList:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function NewFesRankRewardList:OnFlush()
	if not self.data then
		return
	end

	local min_rank = self.data.min_rank
	local max_rank = self.data.max_rank
	local desc = ""
	if min_rank == max_rank then
		desc = min_rank
	else
		desc = string.format(Language.NewFestivalActivity.RankRewardDes2, min_rank, max_rank)
	end

	self.node_list["rank_desc"].text.text = string.format(Language.NewFestivalActivity.DRRankTitle, desc,
		self.data.reach_value)
	self.reward_list:SetDataList(self.data.reward_item)
end

NewFesRankList = NewFesRankList or BaseClass(BaseRender)
function NewFesRankList:OnFlush()
	if not self.data then
		return
	end

	local is_top_3
	local player_rank = self.data.index
	if self.data.no_true_rank then --未上榜
		self.node_list.score.text.text = Language.KFAttributeStoneRank.BaoMi
		is_top_3 = self.data.index <= 3
	else
		self.node_list.score.text.text = self.data.rank_data.rank_value
		is_top_3 = self.data.index <= 3
	end

	local user_name = self.data.rank_data.name
	if self.data.no_true_rank then
		user_name = Language.KFAttributeStoneRank.XuWeiYiDai
	end

	self.node_list.name.text.text = user_name
	self.node_list.rank_icon:CustomSetActive(is_top_3)

	local bundle, asset
	if not is_top_3 then
		self.node_list.rank_text.text.text = player_rank
		bundle, asset = ResPath.GetNewFestivalActImages("a3_jrhd_rank_di4")
	else
		bundle, asset = ResPath.GetNewFestivalActImages("a3_jrhd_rank_di" .. player_rank)

		local icon_bundle, icon_asset = ResPath.GetNewFestivalActImages("a3_jrhd_rank" .. player_rank)
		self.node_list.rank_icon.image:LoadSprite(icon_bundle, icon_asset, function()
			self.node_list.rank_icon.image:SetNativeSize()
		end)

		self.node_list.rank_text.text.text = ""
	end

	self.node_list.bg.image:LoadSprite(bundle, asset)
end
