--首冲提示
VipFourBuyTips = VipFourBuyTips or BaseClass(SafeBaseView)
function VipFourBuyTips:__init()
	--self:SetMaskBg(true,true,false)
	self.view_layer = UiLayer.MainUIHigh
	self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "v4_buy_tips")
	
	--self.active_close = false
end

function VipFourBuyTips:__delete()
end

function VipFourBuyTips:ReleaseCallBack()
	if self.role_arena_display_1 then
		self.role_arena_display_1:DeleteMe()
		self.role_arena_display_1 = nil
	end
end

function VipFourBuyTips:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["add_rexcharge_btn"], BindTool.Bind1(self.OpenRechargeFun, self))
	XUI.AddClickEventListener(self.node_list["btn_model"], BindTool.Bind1(self.OpenFirstRecharge, self))

	if self.role_arena_display_1 == nil then
		self.role_arena_display_1 = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["SiutDisplay1"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}

		self.role_arena_display_1:SetRenderTexUI3DModel(display_data)
	end

	local bundle, asset = ResPath.GetMountModel(7036)
	self.role_arena_display_1:SetMainAsset(bundle,asset)
	local id_left = ServerActivityWGData.Instance:GetFirstRechargeTipsCapability()
	self:CreateCapabilityAtlasNumLeft(id_left)

end
function VipFourBuyTips:CreateCapabilityAtlasNumLeft(id)
	--local prof = RoleWGData.Instance:GetRoleProf()
	--local item_list = ServerActivityWGData.Instance:GetFirstRechargeRewardItemList(1,prof)
	local res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(id)
	if attr_cfg then
		local tmp_attr_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(part_type, attr_cfg.index, 1)
		if tmp_attr_cfg then
			local attr = AttributeMgr.GetAttributteByClass(tmp_attr_cfg)
			local capability = AttributeMgr.GetCapability(attr)
			self.node_list["zhanli_text_left"].text.text = capability
		end
	end
end
function VipFourBuyTips:OpenRechargeFun()
	self:Close()
	ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_cz)
end
function VipFourBuyTips:OpenFirstRecharge()
	self:Close()
	ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_cz)
end

