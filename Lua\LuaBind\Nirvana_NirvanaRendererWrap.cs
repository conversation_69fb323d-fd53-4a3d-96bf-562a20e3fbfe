﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_NirvanaRendererWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(Nirvana.NirvanaRenderer), typeof(UnityEngine.MonoBehaviour));
		L<PERSON>RegFunction("FindAllEffect", FindAllEffect);
		<PERSON><PERSON>Function("SetDefaultMaterial", SetDefaultMaterial);
		L.RegFunction("AddDefaultMaterial", AddDefaultMaterial);
		L.RegFunction("ClearPropertyBlock", ClearPropertyBlock);
		L.RegFunction("SetKeyword", SetKeyword);
		<PERSON>.RegFunction("UnsetKeyword", UnsetKeyword);
		<PERSON><PERSON>Function("SetCustomBounds", SetCustomBounds);
		L.RegFunction("ClearCustomBounds", ClearCustomBounds);
		L.RegFunction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Materials", get_Materials, set_Materials);
		<PERSON><PERSON>("CustomBounds", get_CustomBounds, null);
		<PERSON><PERSON>ar("RenderQueue", get_RenderQueue, set_RenderQueue);
		L.RegVar("UnityRenderer", get_UnityRenderer, null);
		L.RegVar("IsStatic", get_IsStatic, set_IsStatic);
		L.RegVar("PropertyBlock", get_PropertyBlock, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FindAllEffect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)ToLua.CheckObject(L, 1, typeof(Nirvana.NirvanaRenderer));
			Nirvana.RenderEffect[] o = obj.FindAllEffect();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDefaultMaterial(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)ToLua.CheckObject(L, 1, typeof(Nirvana.NirvanaRenderer));
			obj.SetDefaultMaterial();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddDefaultMaterial(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)ToLua.CheckObject(L, 1, typeof(Nirvana.NirvanaRenderer));
			obj.AddDefaultMaterial();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearPropertyBlock(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)ToLua.CheckObject(L, 1, typeof(Nirvana.NirvanaRenderer));
			obj.ClearPropertyBlock();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetKeyword(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)ToLua.CheckObject(L, 1, typeof(Nirvana.NirvanaRenderer));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetKeyword(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnsetKeyword(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)ToLua.CheckObject(L, 1, typeof(Nirvana.NirvanaRenderer));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.UnsetKeyword(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetCustomBounds(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)ToLua.CheckObject(L, 1, typeof(Nirvana.NirvanaRenderer));
			UnityEngine.Bounds[] arg0 = ToLua.CheckStructArray<UnityEngine.Bounds>(L, 2);
			obj.SetCustomBounds(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ClearCustomBounds(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)ToLua.CheckObject(L, 1, typeof(Nirvana.NirvanaRenderer));
			obj.ClearCustomBounds();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Materials(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			UnityEngine.Material[] ret = obj.Materials;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Materials on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CustomBounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			UnityEngine.Bounds[] ret = obj.CustomBounds;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CustomBounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_RenderQueue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			int ret = obj.RenderQueue;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RenderQueue on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_UnityRenderer(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			UnityEngine.Renderer ret = obj.UnityRenderer;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index UnityRenderer on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsStatic(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			bool ret = obj.IsStatic;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsStatic on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PropertyBlock(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			UnityEngine.MaterialPropertyBlock ret = obj.PropertyBlock;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PropertyBlock on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Materials(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			UnityEngine.Material[] arg0 = ToLua.CheckObjectArray<UnityEngine.Material>(L, 2);
			obj.Materials = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Materials on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_RenderQueue(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.RenderQueue = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index RenderQueue on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsStatic(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.NirvanaRenderer obj = (Nirvana.NirvanaRenderer)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsStatic = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsStatic on a nil value");
		}
	}
}

