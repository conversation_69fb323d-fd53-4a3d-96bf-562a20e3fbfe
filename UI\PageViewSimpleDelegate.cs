﻿//------------------------------------------------------------------------------
// Copyright (c) 2018-2018 Nirvana Technology Co. Ltd.
// All Right Reserved.
// Unauthorized copying of this file, via any medium is strictly prohibited.
// Proprietary and confidential.
//------------------------------------------------------------------------------

using System;
using LuaInterface;
using Nirvana;
using UnityEngine;
using UnityEngine.EventSystems;
using UnityEngine.UI;

/// <summary>
/// The description of PageViewSimpleDelegate.
/// </summary>
[RequireComponent(typeof(ListView))]
public sealed class PageViewSimpleDelegate : Mono<PERSON><PERSON><PERSON><PERSON>, IEnd<PERSON><PERSON><PERSON><PERSON><PERSON>
{
    [SerializeField]
    [Tooltip("The cell prefab")]
    private GameObject cellPrefab;

    private ScrollRect scrollRect;
    private ListView listView;

    [SerializeField]
    private bool highSensitivity;

    public delegate int NumberOfCellsDelegate();
    public delegate void CellRefreshDelegate(int index, GameObject go);
    public delegate void CellRecycleDelegate(int index, GameObject go);

    public NumberOfCellsDelegate NumberOfCellsDel { get; set; }
    public CellRefreshDelegate CellRefreshDel { get; set; }
    public CellRecycleDelegate CellRecycleDel { get; set; }

    /// <inheritdoc/>
    [NoToLua]
    public void OnEndDrag(PointerEventData eventData)
    {
        if (!this.highSensitivity)
        {
            return;
        }


        var pageView = this.listView as PageView;
        if (pageView == null)
        {
            return;
        }

        if (this.listView.IsJumping)
        {
            return;
        }

        if (this.scrollRect == null)
        {
            this.scrollRect = this.GetComponent<ScrollRect>();
        }

        if (this.scrollRect.velocity.x > 5.0f)
        {
            var current = pageView.ActiveCellsMiddleIndex;
            var index = current - 1;
            pageView.JumpToIndex(index, 0.0f, 1.0f);
        }
        else if (this.scrollRect.velocity.x < -5.0f)
        {
            var current = pageView.ActiveCellsMiddleIndex;
            var index = current + 1;
            pageView.JumpToIndex(index, 0.0f, 1.0f);
        }
    }

    private void Awake()
    {
        this.listView = this.GetComponent<ListView>();
        this.listView.CellCountDel = () => this.NumberOfCellsDel();
        this.listView.GetCellDel = index =>
        {
            var go = GameObjectPool.Instance.Spawn(this.cellPrefab, null);
            try
            {
                this.CellRefreshDel(index, go);
            }
            catch (Exception e)
            {
                Debug.LogError(e);
            }

            return go;
        };
        this.listView.RecycleCellDel = (index, go) =>
        {
            if (this.CellRecycleDel != null)
            {
                try
                {
                    this.CellRecycleDel(index, go);
                }
                catch (Exception e)
                {
                    Debug.LogError(e);
                }
            }

            GameObjectPool.Instance.Free(go);
        };
    }
}
