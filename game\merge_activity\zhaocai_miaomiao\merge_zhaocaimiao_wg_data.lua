MergeZhaoCaiMiaoWGData = MergeZhaoCaiMiaoWGData or BaseClass()

function MergeZhaoCaiMiaoWGData:__init()
	if MergeZhaoCaiMiaoWGData.Instance then
		ErrorLog("[MergeZhaoCaiMiaoWGData] Attemp to create a singleton twice !")
	end

	MergeZhaoCaiMiaoWGData.Instance = self
	MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.MERGE_ACT_ZHAOCAIMAO, {[1] = MERGE_EVENT_TYPE.LEVEL},
															BindTool.Bind(self.MiaoMiaoOpenLimit, self), BindTool.Bind(self.IsShowMiaoMiaoRedPoint, self))
	RemindManager.Instance:Register(RemindName.MergeZhaoCaiMiaoMiao, BindTool.Bind(self.IsShowMiaoMiaoRedPoint, self))


	-- self.chushen_item_data_event = BindTool.Bind1(self.Chu<PERSON>henItemDataChange, self)
	-- ItemWGData.Instance:NotifyDataChangeCallBack(self.chushen_item_data_event)

	-- self.ani_mark = 0
	-- self.chushen_rank_data_list = {}
	-- self.chushen_add_list = {}
	-- self.menu_list = {}
	self.oa_index = 0
	self.tot_choujiang_times = -1
	self.cur_choujiang_times = 0
	self.login_reward_status = 0
	self.tot_xianyun = 0
	self.chongzhi_task_status = {}
	self.miaomiao_record_list = {}
	-- self.unlock_all = 0

	self:LoadConfig()
end

function MergeZhaoCaiMiaoWGData:__delete()
	MergeZhaoCaiMiaoWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.MergeZhaoCaiMiaoMiao)
	self.oa_index = nil
	self.tot_choujiang_times = nil
	self.cur_choujiang_times = nil
	self.login_reward_status = nil
	self.chongzhi_task_status = nil
	self.tot_xianyun = nil
	self.miaomiao_record_list = nil

end

function MergeZhaoCaiMiaoWGData:LoadConfig()
	self.zhaocai_miaomiao = ConfigManager.Instance:GetAutoConfig("merge_activity_zhaocaimiaomiao_auto")
	self.recharge_task = ListToMapList(self.zhaocai_miaomiao.recharge_task, "cycle")
	self.reward_range = ListToMapList(self.zhaocai_miaomiao.reward_range, "cycle")
	self.config_param = ListToMap(self.zhaocai_miaomiao.config_param, "cycle")
	self.interface = ListToMap(self.zhaocai_miaomiao.interface, "cycle")
	--ItemWGData.Instance:UnNotifyDataChangeCallBack(self.chushen_item_data_event)
end

function MergeZhaoCaiMiaoWGData:GetActName()
	local other_cfg = self.interface[self.oa_index] or self.interface[0]
	return other_cfg.activity_name
end

function MergeZhaoCaiMiaoWGData:GetPanelUiInterface()
	return self.interface[self.oa_index] or self.interface[0]
end

function MergeZhaoCaiMiaoWGData:GetMiaoMiaoTaskList()
	if self.recharge_task then
		return self.recharge_task[self.oa_index] or {} 
	end
	return {}
end

function MergeZhaoCaiMiaoWGData:SetInfo(proto)
	self.oa_index = proto.cycle
	self.tot_xianyun = proto.tot_xianyun
	self.tot_choujiang_times = proto.tot_choujiang_times
	self.cur_choujiang_times = proto.cur_choujiang_times
	self.login_reward_status = proto.login_reward_status
    self.chongzhi_task_status = proto.chongzhi_task_status
    self.draw_xianyu = proto.draw_xianyu
    if self.draw_xianyu > 0 then
        self.old_draw_xianyu = self.draw_xianyu
    end
end

function MergeZhaoCaiMiaoWGData:GetDrawXianYu()
	return self.draw_xianyu or 0
end

function MergeZhaoCaiMiaoWGData:GetOldDrawXianyu()
	return self.old_draw_xianyu or 0
end

function MergeZhaoCaiMiaoWGData:GetTaskRechargeState(index)
	return self.chongzhi_task_status[index] or 0
end

function MergeZhaoCaiMiaoWGData:GetMiaoMiaoActInfo()
	local tot_choujiang_times = self.tot_choujiang_times >= 0 and self.tot_choujiang_times or 0
	return tot_choujiang_times, self.cur_choujiang_times, self.login_reward_status, self.chongzhi_task_status
end

function MergeZhaoCaiMiaoWGData:GetMiaoMiaoTotalCountActInfo()
	return self.tot_choujiang_times
end

function MergeZhaoCaiMiaoWGData:GetOtherParam()
	return self.config_param[self.oa_index] or self.config_param[0]
end

function MergeZhaoCaiMiaoWGData:GetAciOaIndex()
	return self.oa_index
end

function MergeZhaoCaiMiaoWGData:SetResultMoney(money_num)
	self.cur_draw_xianyu = money_num
end

function MergeZhaoCaiMiaoWGData:GetResultMoney()
	return self.cur_draw_xianyu or 0
end

function MergeZhaoCaiMiaoWGData:GetMiaoMiaoMoneyCount()
	return self.tot_xianyun 
end

function MergeZhaoCaiMiaoWGData:MiaoMiaoOpenLimit()
	local cfg = self.zhaocai_miaomiao.other[1]
	local vo = GameVoManager.Instance:GetMainRoleVo()
	return vo.level >= cfg.open_role_level, nil
end

function MergeZhaoCaiMiaoWGData:IsShowMiaoMiaoRedPoint()
	local is_open = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.MERGE_ACT_ZHAOCAIMAO)
	if not is_open then
		return 0
	end

	if self.cur_choujiang_times > 0 or self.login_reward_status == 0 then
		return 1
	end
    local draw_num = MergeZhaoCaiMiaoWGData.Instance:GetDrawXianYu()
    if draw_num > 0 then
        return 1
    end
	for k,v in pairs(self.chongzhi_task_status) do
		if v == 1 then
			return 1
		end
	end

end

function MergeZhaoCaiMiaoWGData:GetRechargePanelMark(money_num)
	local is_open = MergeActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.MERGE_ACT_ZHAOCAIMAO)
	local level_limit = self:MiaoMiaoOpenLimit()
	if not is_open or not level_limit then
		return false
	end

	local cfg = self.recharge_task[self.oa_index]
	for k,v in pairs(cfg) do
		if v.grade == money_num and self.chongzhi_task_status[k] and self.chongzhi_task_status[k] == 0 then
			return true
		end
	end
	return false
end

function MergeZhaoCaiMiaoWGData:GetMiaoMiaoRechargeTips(money_num)
	if not money_num then
		return 0
	end
	for k,v in pairs(self.recharge_task[self.oa_index]) do
		if v.grade == money_num then
			return v.virtual_item
		end
	end
	return 0
end

function MergeZhaoCaiMiaoWGData:GetPartiDelayTime()
	local other_cfg = self.interface[self.oa_index] or self.interface[0]
	local circle_time = other_cfg.roll_circle or 3
	local roll_circle = other_cfg.each_circle_time or 0.5
	return (circle_time + 3) * roll_circle + 1.5 + 0.2
end

function MergeZhaoCaiMiaoWGData:SetRecordInfo(protocol)
	self.miaomiao_record_list = protocol.record_list
end

function MergeZhaoCaiMiaoWGData:GetMiaoMiaoRecord()
	return self.miaomiao_record_list 
end

function MergeZhaoCaiMiaoWGData:GetMiaoMiaoToggleIsJump()
	return self.miaomiao_is_jump_ani 
end

function MergeZhaoCaiMiaoWGData:SetMiaoMiaoToggleIsJump(is_on)
	self.miaomiao_is_jump_ani = is_on
end