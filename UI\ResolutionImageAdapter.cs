﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

#if UNITY_EDITOR
using UnityEditor;
#endif
using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Used to adapt the difference resolution.
/// </summary>
//[RequireComponent(typeof(RectMask2D))]

//[ExecuteInEditMode]
public sealed class ResolutionImageAdapter : MonoBehaviour
{
    [SerializeField]
    private RawImage rawimage;

    [SerializeField]
    [Tooltip("是否根据大小来缩放比例(如果勾上那么图片会根据分辨率来对图片进行缩放，如果不勾那么图片超过分辨率大小不进行缩放，默认1倍)")]
    private bool is_limit_scale;


    private RectTransform image_size;
    private RectTransform parent_size;

    private CanvasScaler canvas_scaler;
    private CanvasGroup canvas_group;
    private RectTransform canvas_group_rect;

    private void Start()
    {
        if (this.rawimage != null)
        {
            image_size = this.rawimage.GetComponent<RectTransform>();
            image_size.anchorMin = new Vector2(0.5f, 0.5f);
            image_size.anchorMax = new Vector2(0.5f, 0.5f);
            image_size.pivot = new Vector2(0.5f, 0.5f);
            rawimage.SetNativeSize();
        }

        parent_size = this.GetComponent<RectTransform>();
        parent_size.anchorMin = new Vector2(0.0f, 0.0f);
        parent_size.anchorMax = new Vector2(1.0f, 1.0f);
        parent_size.pivot = new Vector2(0.5f, 0.5f);


        canvas_scaler = this.GetComponentInParent<CanvasScaler>();
        canvas_group = this.GetComponentInParent<CanvasGroup>();

        if (canvas_group != null)
        {
            UGUIRawimageScale ugui_rawimage_scale = canvas_group.GetComponent<UGUIRawimageScale>();
            if (ugui_rawimage_scale != null)
            {
                Transform parent_root = ugui_rawimage_scale.transform.parent;
                if (parent_root != null)
                    canvas_group = parent_root.GetComponentInParent<CanvasGroup>();
            }
            canvas_group_rect = canvas_group.GetComponent<RectTransform>();
        }

        float offset_x = this.MoveImageOffset();
        this.AdaptResolution(offset_x);
    }

//#if UNITY_EDITOR
    private void Update()
    {
        float offset_x = this.MoveImageOffset();
        this.AdaptResolution(offset_x);
    }
//#endif

    private void AdaptResolution(float offset_x)
    {
#if UNITY_EDITOR
        var prefabType = PrefabUtility.GetPrefabType(this.gameObject);
        if (prefabType == PrefabType.Prefab)
        {
            return;
        }
#endif

        if (this.rawimage == null)
        {
            return;
        }

        if (this.rawimage.texture != null)
        {
            this.rawimage.SetNativeSize();
        }

        if (image_size == null)
        {
            return;
        }

        if (image_size.rect.width == 0 || image_size.rect.height == 0)
        {
            return;
        }

        if (parent_size.rect.width == 0 || parent_size.rect.height == 0)
        {
            return;
        }

        Rect resolution_size = parent_size.rect;

        float scale_x = 1.0f;
        float scale_y = 1.0f;
        if (this.is_limit_scale)
        {
            scale_x = (resolution_size.width + offset_x * 2) / image_size.rect.width;
            scale_y = resolution_size.height / image_size.rect.height;
        }
        else
        {
            if (image_size.rect.width < resolution_size.width + offset_x * 2)
            {
                scale_x = (resolution_size.width + offset_x * 2) / image_size.rect.width;
            }

            if (image_size.rect.height < resolution_size.height)
            {
                scale_y = resolution_size.height / image_size.rect.height;
            }
        }

        float scale = Mathf.Max(scale_x, scale_y);
        if (scale != image_size.localScale.x || scale != image_size.localScale.y)
        {
            image_size.localScale = new Vector3(scale, scale, scale);
        }
    }

    private float MoveImageOffset()
    {
        if (this.rawimage == null)
        {
            return 0;
        }

        canvas_scaler = this.GetComponentInParent<CanvasScaler>();
        canvas_group = this.GetComponentInParent<CanvasGroup>();

        if (canvas_group == null || image_size == null || canvas_group_rect == null)
        {
            return 0;
        }

        UGUIRawimageScale ugui_rawimage_scale = canvas_group.GetComponent<UGUIRawimageScale>();
        if (ugui_rawimage_scale != null)
        {
            Transform parent_root = ugui_rawimage_scale.transform.parent;
            if (parent_root != null)
                canvas_group = ugui_rawimage_scale.GetComponentInParent<CanvasGroup>();
        }

        if (canvas_group == null || image_size == null || canvas_group_rect == null)
        {
            return 0;
        }

        float offset_x = canvas_group_rect.localPosition.x;

        if (null != canvas_scaler && image_size.localPosition.x != -offset_x)
        {
            image_size.localPosition = new Vector2(-offset_x, image_size.localPosition.y);
        }

        return Mathf.Ceil(offset_x);
    }
}
