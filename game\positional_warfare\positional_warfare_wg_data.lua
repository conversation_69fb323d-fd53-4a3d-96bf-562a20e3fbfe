-- 现在有三个东西
-- 贡献积分=击杀boss，根据造成伤害百分比获得的 个人积分，用于领取 奖励/战令奖励
-- 占领积分=击杀boss后，归属门派获得的积分，用于占领城池
-- 武斗货币=击杀boss后，跟随掉落获得的货币

-- 占领分为  
-- 1.完全占领  归属权是上次争夺结束后结算是自己的
-- 2.临时占领  争夺的时候临时占领，也是预示结算后城池归属  临时占领也可连线攻击

PositionalWarfareWGData = PositionalWarfareWGData or BaseClass(BaseWGCtrl)

function PositionalWarfareWGData:__init()
	if PositionalWarfareWGData.Instance then
		print_error("[PositionalWarfareWGData] attempt to create singleton twice!")
		return
	end

	PositionalWarfareWGData.Instance = self

	local cfg = ConfigManager.Instance:GetAutoConfig("cross_land_war_auto")
	self.map_cfg = cfg.map
	self.shop_cfg = cfg.shop
	self.other_cfg = cfg.other[1]
	self.devote_reward_cfg = cfg.devote_reward
	self.vie_time_group_cfg = cfg.vie_time_group
	self.group_cfg = ListToMap(cfg.group, "index")
	self.des_cfg = ListToMap(cfg.des, "sep", "show_pos")
	self.vie_time_cfg = ListToMapList(cfg.vie_time, "index")
	self.land_cfg = ListToMap(cfg.land, "group_index", "seq")
	self.camp_cfg = ListToMap(cfg.camp, "group_index", "camp")
	self.order_cfg = ListToMap(cfg.order, "group_index", "seq")
	self.order_exp_cfg = ListToMapList(cfg.order_exp, "group_index")
	self.monster_group_cfg = ListToMapList(cfg.monster_group, "group_index")
	self.monster_cfg = ListToMap(cfg.monster, "monster_group", "land_seq", "seq")
	self.capture_reward_cfg = ListToMapList(cfg.capture_reward, "group_index", "land_type")
	self.person_rank_reward_cfg = ListToMapList(cfg.person_rank_reward, "group_index", "rank_type")

	self.tired = 0                           -- 疲劳值
	self.devote = 0                          -- 贡献值
	self.room_id = -1  						 -- 房间ID 大于0即已经匹配
	self.open_time = 0                       -- 主服开服时间   
	self.shop_score = 0                      -- 商城使用积分
	self.my_camp = -999                      -- 我的阵营
	self.group_index = 0                     -- 轮次 < 0 标识本服阶段（宗门内斗）
	self.zhanling_level = 0
	self.enter_land_seq = -1
	self.cycle_reset_time = 0               -- 重置贡献和战令的时间  
	self.cycle_group_index = 0              -- 战令得档次group_index
	self.devote_reward_flag = 0              -- 贡献值领取到的挡位
	self.main_server_open_day = 0            -- 当前主服开服时间 = math.ceil((open_time - server_time)/24 * 60 * 60)
	self.enter_scene_land_seq_cache = -1
	self.enter_scene_boss_seq_cache = -1

	self.map_cach = {}
	self.map_cache = {}
	self.land_cache = {}
	self.special_flag = {}                   --第一个位 贡献积分额外奖励   锁标记  
	self.shop_buy_list = {}                  -- 兑换次数
	self.city_boss_info = {}   	
	self.camp_info_list = {}
	self.rank_data_list = {}                -- 排行数据
	self.land_info_list = {}   				-- 领地数据
	self.city_boss_state = {}  				-- 城市怪物死亡状态
	self.boss_record_list = {}  			-- boss被击杀记录
	self.monster_info_list = {}   			-- 怪物刷新数据
	self.order_reward_flag = {}
	self.capture_reward_flag = {}            -- 按位跟领地下表  占领奖励
	self.link_base_city_cache = {}          -- 我的连接了基地的城市缓存
	self.vie_time_group_cache = {}
	self.zhanling_max_level_cfg = {}
	self.order_added_reward_flag = {}
	self.war_situation_data_list = {}
	self.city_camp_total_num_info = {}  	-- 每个城市进入本门派人口
	self.land_camp_total_player_num_list = {}

	self.call_cd_time = 0
	self.call_land_seq = -1

	self:InitDataCache()

	RemindManager.Instance:Register(RemindName.PositionalWarfare, BindTool.Bind(self.GetPositionalWarfareRemind, self))
end

function PositionalWarfareWGData:__delete()
	PositionalWarfareWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.PositionalWarfare)
end

-------------------------------------INIT_CACHE_START-----------------------------------------
function PositionalWarfareWGData:InitDataCache()
	local land_cache = {}
	for group_index, group_data in pairs(self.land_cfg) do
		land_cache[group_index] = land_cache[group_index] or {}

		for k, v in pairs(group_data) do
			land_cache[group_index][v.seq] = land_cache[group_index][v.seq] or {}
			land_cache[group_index][v.seq].connect_camp = Split(v.connect_camp, "|")
			land_cache[group_index][v.seq].pre_land = Split(v.pre_land, "|")
			land_cache[group_index][v.seq].pre_city = Split(v.pre_city, "|")
		end
	end

	self.land_cache = land_cache

	local camp_cache = {}
	for group_index, group_data in pairs(self.camp_cfg) do
		camp_cache[group_index] = camp_cache[group_index] or {}

		for k, v in pairs(group_data) do
			camp_cache[group_index][v.camp] = v
		end
	end

	self.camp_cache = camp_cache

	local map_cache = {}
	for k, v in pairs(self.map_cfg) do
		map_cache[v.group_index] = v.map_id
	end

	self.map_cache = map_cache

	local zhanling_max_level_cfg = {}
	for k, v in pairs(self.order_cfg) do
		zhanling_max_level_cfg[k] = zhanling_max_level_cfg[k] or {}

		local max_level_cfg = {}
		for i, u in pairs(v) do
			if IsEmptyTable(max_level_cfg) or u.need_devote > max_level_cfg.need_devote then
				max_level_cfg = u
			end
		end

		zhanling_max_level_cfg[k] = max_level_cfg
	end

	self.zhanling_max_level_cfg = zhanling_max_level_cfg

	local vie_time_group_cache = {}
	for k, v in pairs(self.vie_time_group_cfg) do
		local week_data = {}
		local open_weekday_data = Split(v.open_weekday, "|")
		for i, u in pairs(open_weekday_data) do
			week_data[tonumber(u)] = true
		end
		
		vie_time_group_cache[v.index] = week_data
	end

	self.vie_time_group_cache = vie_time_group_cache

	local tired_drug_item_list = {}
	local tired_drag_list = Split(self.other_cfg.tired_drug, "|")

	for k, v in pairs(tired_drag_list) do
		table.insert(tired_drug_item_list, tonumber(v))
	end
	self.tired_drug_item_list = tired_drug_item_list
end
-------------------------------------INIT_CACHE_END-----------------------------------------

-------------------------------------REMIND_GET_START-----------------------------------------
function PositionalWarfareWGData:GetPositionalWarfareRemind()
	local stage_type = self:GetMapId()
	
	if stage_type == 1 then
		if self:GetDevoteRewardRemind() == 1 then
			return 1
		end
	else
		if self:GetZhanLingRemind() == 1 then
			return 1
		end
	end

	if self:GetMallExchangeRemind() == 1 then
		return 1
	end

	if self:GetCityRewardRemind() == 1 then
		return 1
	end

	return 0
end

function PositionalWarfareWGData:GetZhanLingRemind()
    local reward_list = self:GetCurZhanLingRewardCfgList()

	if not IsEmptyTable(reward_list) then
		for k, v in pairs(reward_list) do
			local nor_can_get, high_can_get = self:IsCanGetZhanLingRewardBySeq(v.seq)
	
			if nor_can_get or high_can_get then
				return 1
			end
		end
	end

	return 0
end

function PositionalWarfareWGData:GetDevoteRewardRemind()
	local score = self:GetDevote()
	
	if score <= 0 then
		return 0
	end

	local data_list = self:GetPersonScoreRewardCfg()

	for k, v in pairs(data_list) do
    	local is_get = self:IsDevoteRewardIsGet(v.seq)

		if not is_get then
			local score_enough = v.need_devote <= score
			local unlock_flag = self:GetPeosonScoreRewardUnLockFlag()
			local devote_reward_flag = self:GetDevoteRewardFlag()
			local sort_get = (v.seq - devote_reward_flag) == 1
			local can_get
    
			if v.is_added == 1 then
				can_get = unlock_flag and score_enough and sort_get
			else
				can_get = score_enough and sort_get
			end

			if can_get then
				return 1
			end
		end
	end

	return 0
end

function PositionalWarfareWGData:GetMallExchangeRemind()
	local shop_score = self:GetShopExchangeScore()

	if shop_score <= 0 then
		return 0
	end

	local data_list = self:GetShowShopDataList()

	for k, v in pairs(data_list) do
		local cfg = v.cfg
		local score_enough = shop_score >= cfg.need_shop_score

		if score_enough then
			local has_exchange_limit = v.has_limit_type
			local exchange_time = self:GetShopItemExchangeTimeBySeq(cfg.seq)
			local exchange_time_enough = not has_exchange_limit or (has_exchange_limit and exchange_time < cfg.times_limit)

			if exchange_time_enough then
				return 1
			end
		end
	end

	return 0
end

function PositionalWarfareWGData:GetCityRewardRemind()
	if self:IsInFightTime() then
		return 0
	end

	local my_camp = self:GetMyCamp()
	if my_camp < 0 then
		return 0
	end

	local group_index = self:GetMyGroupIndex()
	local nor_city_data = self:GetLandDataListCfgByGroupIndex(group_index)

	if not IsEmptyTable(nor_city_data) then
        for k, v in pairs(nor_city_data) do
			if self:CanGetLandCaptureRewrd(v.group_index, v.seq) then
				return 1
			end
		end
	end

	return 0
end

-------------------------------------REMIND_GET_START-----------------------------------------

-------------------------------------PROTOCOL_SET_START-----------------------------------------
function PositionalWarfareWGData:SetCrossLandWarBaseInfo(protocol)
	self.tired = protocol.tired
	self.devote = protocol.devote             				-- 贡献积分    战令积分  左下角 奖励
	self.shop_score = protocol.shop_score     				-- 商店使用积分
	self.special_flag = protocol.special_flag               -- 特殊标记--第一个位 贡献积分额外奖励   锁标记     第二位 战令标记
	self.shop_buy_list = protocol.shop_buy_list
	self.cycle_reset_time = protocol.cycle_reset_time
	self.cycle_group_index = protocol.cycle_group_index   	-- 战令得档次group_index
	self.devote_reward_flag = protocol.devote_reward_flag  	-- 贡献积分  左下角 奖励标记 领取到那一档位了
	self.capture_reward_flag = protocol.capture_reward_flag -- 按位跟领地下表  占领奖励

	local function cal_order_reward_flag(data)
		local index = 0
		local bit_list = {}
		local order_reward_flag = {}

		for i,v in ipairs(data) do
			bit_list = bit:d2b(v, bit_list)
			for ii = 1, 8 do
				order_reward_flag[index] = bit_list[33 - ii]
				index = index + 1
			end
		end

		return order_reward_flag
	end

	self.order_reward_flag = cal_order_reward_flag(protocol.order_reward_flag)
	self.order_added_reward_flag = cal_order_reward_flag(protocol.order_added_reward_flag)

	self:CalZhanLingLevel()

	return self.order_reward_flag, self.order_added_reward_flag
end

function PositionalWarfareWGData:SetCrossLandWarRoomInfo(protocol)
	self.room_id = protocol.room_id  	-- 房间ID 大于0即已经匹配
	self.open_time = protocol.open_time
	self.group_index = protocol.group_index
	self.land_info_list = protocol.land_info_list

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self.main_server_open_day = math.ceil((server_time - self.open_time) / 86400)

	self:CalZhanLingLevel()
	self:CalLinkBaseCityCache()
end

function PositionalWarfareWGData:CalZhanLingLevel()
	local level = 0
	local devote = self:GetDevote()

	if devote > 0 then
		local zhanling_reward_cfg = self:GetCurZhanLingRewardCfgList()

		if not IsEmptyTable(zhanling_reward_cfg) then
			for k, v in pairs(zhanling_reward_cfg) do
				if devote >= v.need_devote and v.seq > level then
					level = v.seq
				end
			end
		end
	end

	self.zhanling_level = level
end

function PositionalWarfareWGData:SetCrossLandWarLandInfo(protocol)
	local land_seq = protocol.land_seq
	self.city_boss_info[land_seq] = protocol.monster_info_list
	self.city_boss_state[land_seq] = protocol.monster_die_flag
	self.city_camp_total_num_info[land_seq] = protocol.total_num
	self.land_camp_total_player_num_list[land_seq] = protocol.camp_total_num_list
end

-- 怪物是击杀记录
function PositionalWarfareWGData:SetLandWarMonsterRecordInfo(protocol)
	local land_seq = protocol.land_seq
	local monster_seq = protocol.monster_seq
	local record_list = protocol.record_list
	
	self.boss_record_list[land_seq] = self.boss_record_list[land_seq] or {}
	self.boss_record_list[land_seq][monster_seq] = record_list
end

-- 场景内怪物数据
function PositionalWarfareWGData:SetCrossLandWarMonsterInfo(protocol)
	self.enter_land_seq = protocol.land_seq
	self.monster_info_list = protocol.monster_info_list
end

-- 场景内怪物数据更新
function PositionalWarfareWGData:SetCrossLandWarMonsterUpdate(protocol)
	local land_seq = protocol.land_seq
	local monster_info = protocol.monster_info
	self.monster_info_list[monster_info.seq] = monster_info
end

function PositionalWarfareWGData:SetLandWarCampInfo(protocol)
	self.camp_info_list = protocol.camp_info_list
	local group_index = self:GetMyGroupIndex()
	local my_server_id = RoleWGData.Instance:GetOriginServerId()
	local guild_id = RoleWGData.Instance:GetAttr("guild_id")

	for k, v in pairs(self.camp_info_list) do
		-- 服Id
		if group_index > 0 then
			if my_server_id == v.param.temp_low then
				self.my_camp = v.camp
				break
			end
		else
			-- 宗门Id
			if guild_id == v.guild_id then
				self.my_camp = v.camp
				break
			end
		end
	end

	self:CalLinkBaseCityCache()
end

function PositionalWarfareWGData:SetCrossLandWarRankInfo(protocol)
	local rank_type = protocol.rank_type
	self.rank_data_list[rank_type] = protocol.rank_item_list
end

function PositionalWarfareWGData:SetWarSituationInfo(protocol)
	self.war_situation_data_list = protocol.item_list
end

function PositionalWarfareWGData:SetEnterSceneSelectDataCache(land_seq, boss_seq)
	self.enter_scene_land_seq_cache = land_seq or -1
	self.enter_scene_boss_seq_cache = boss_seq or -1
end

function PositionalWarfareWGData:SetCrossLandWarCallInfo(protocol)
	self.call_cd_time = protocol.call_cd_time  -- 下次可以点击时间
	self.call_land_seq = protocol.call_land_seq  
end
-------------------------------------PROTOCOL_SET_END-----------------------------------------

-------------------------------------PROTOCOL_GET_START-----------------------------------------
-- 商店商品兑换次数   seq < 0 不限购 未记录购买次数
function PositionalWarfareWGData:GetShopItemExchangeTimeBySeq(seq)
	if seq < 0 then
		return 0
	end

	return self.shop_buy_list[seq] or 0
end

-- 商店使用的积分
function PositionalWarfareWGData:GetShopExchangeScore()
	return self.shop_score
end

-- 个人奖励积分/战令积分  贡献积分 
function PositionalWarfareWGData:GetDevote()
	return self.devote
end

-- 是否解锁个人积分支付奖励
function PositionalWarfareWGData:GetPeosonScoreRewardUnLockFlag()
	return self:GetSpecialFlag(CROSS_LAND_WAR_SPECIAL_FLAG_TYPE.DEVOTE_ADDED)
end

-- 是否解锁高级战令
function PositionalWarfareWGData:GetHigerOrderRewardFlag()
	return self:GetSpecialFlag(CROSS_LAND_WAR_SPECIAL_FLAG_TYPE.ORDER_ADDED)
end

-- 是否获得个人积分奖励
function PositionalWarfareWGData:IsGetPeosonScoreReward(seq)
	return false
end

function PositionalWarfareWGData:GetMyGroupIndex()
	return self.group_index
end

function PositionalWarfareWGData:GetCampInfoDataList()
	return self.camp_info_list
end

function PositionalWarfareWGData:GetWarSituationDataList()
	return self.war_situation_data_list
end

function PositionalWarfareWGData:IsDevoteRewardIsGet(seq)
	return seq <= self.devote_reward_flag
end

function PositionalWarfareWGData:GetDevoteRewardFlag()
	return self.devote_reward_flag
end

function PositionalWarfareWGData:GetSpecialFlag(seq)
	return self.special_flag[seq] == 1
end

function PositionalWarfareWGData:GetTired()
	return self.tired
end

function PositionalWarfareWGData:GetMainServerOpenTime()
	return self.main_server_open_day
end

function PositionalWarfareWGData:GetZhanLingResetTime()
	return self.cycle_reset_time
end

function PositionalWarfareWGData:GetRankDataListByRankType(rank_type)
	return self.rank_data_list[rank_type]
end

function PositionalWarfareWGData:GetRankInfoData(rank_type, rank_id)
	return (self.rank_data_list[rank_type] or {})[rank_id] or {}
end

function PositionalWarfareWGData:GetLandInfoBySeq(seq)
	return self.land_info_list[seq]
end

function PositionalWarfareWGData:GetBossActive(land_seq, boss_seq)   -- boss只存活于争夺时间段内   -- 0 存活   1死亡
	local is_in_fight_time = self:IsInFightTime()

	if not is_in_fight_time then
		return false
	end

	return ((self.land_info_list[land_seq] or {}).monster_die_flag or {})[boss_seq] == 0
end

-- 普通奖励 高级奖励能否领取
function PositionalWarfareWGData:IsCanGetZhanLingRewardBySeq(seq)
	local can_get_nor, can_get_highr = false, false
	-- 没领取
	local is_get_nor, is_get_highr = self:IsGetZhanLingRewardBySeq(seq)

	local cur_reward_cfg = self:GetZhanLingCfg(seq)
	
	if IsEmptyTable(cur_reward_cfg) then
		return can_get_nor, can_get_highr
	end

	local cur_devote = self:GetDevote()
	local is_open_higer_zhanling = self:GetHigerOrderRewardFlag()
	can_get_nor = not is_get_nor and (cur_devote >= cur_reward_cfg.need_devote)
	can_get_highr = not is_get_highr and (cur_devote >= cur_reward_cfg.need_devote) and is_open_higer_zhanling

	return can_get_nor, can_get_highr
end

-- 普通奖励 高级奖励 领取标记
function PositionalWarfareWGData:IsGetZhanLingRewardBySeq(seq)
	return self.order_reward_flag[seq] == 1, self.order_added_reward_flag[seq] == 1
end

function PositionalWarfareWGData:CheckGetZhanLingRewardDataList(order_reward_flag_cache, order_added_reward_flag_cache)
	local data_list = {}
	
	if IsEmptyTable(order_reward_flag_cache) or IsEmptyTable(order_added_reward_flag_cache) then
		return
	end

	local reward_list = self:GetCurZhanLingRewardCfgList()
	
	if not IsEmptyTable(reward_list) then
		for k, v in pairs(reward_list) do
			local old_is_get_nor, old_is_get_highr = order_reward_flag_cache[v.seq] == 1, order_added_reward_flag_cache[v.seq] == 1
			local is_get_nor, is_get_highr = self:IsGetZhanLingRewardBySeq(v.seq)

			if not old_is_get_nor and is_get_nor then
				for i, u in pairs(v.item) do
					table.insert(data_list, u)
				end
			end

			if not old_is_get_highr and is_get_highr then
				for i, u in pairs(v.added_item) do
					table.insert(data_list, u)
				end
			end
		end
	end

	return data_list
end

function PositionalWarfareWGData:GetMyCamp()
	return self.my_camp
end

function PositionalWarfareWGData:GetMOnsterDefeatRecordDataList(land_seq, monster_seq)
	return (self.boss_record_list[land_seq] or {})[monster_seq] or {}
end

-- 入口boss血量等信息
function PositionalWarfareWGData:GetMonsterCityBossInfo(land_seq, monster_seq)
	return (self.city_boss_info[land_seq] or {})[monster_seq] or {}
end

function PositionalWarfareWGData:GetCityEnterTotalNum(land_seq)
	return self.city_camp_total_num_info[land_seq] or 0
end

function PositionalWarfareWGData:GetCityEnterCampTotalNum(land_seq, camp)
	return (self.land_camp_total_player_num_list[land_seq] or {})[camp] or 0
end

function PositionalWarfareWGData:GetCurSceneBossDataList(need_sort)
	local target_data = self:GetCurMonsterListCfg(self.enter_land_seq)

	if need_sort then
		local target_data_list = {}
		local nav_data_list = {}

		if not IsEmptyTable(target_data) then
			local target_data_list = TableCopy(target_data)
			target_data_list = ListIndexFromZeroToOne(target_data_list)
	
			for k, v in pairs(target_data_list) do
				local boss_active = self:GetCityBossActiveState(v.land_seq, v.seq)
				local sort = 0
				local color_value = - v.monster_color * 100 
	
				if boss_active then
					-- local boss_info = self:GetMonsterCityBossInfo(v.land_seq, v.seq)
					-- local camp = boss_info and boss_info.owner_camp or -1
	
					-- if camp > 0 then
					-- 	sort = 10000 + color_value - v.seq
					-- else
					-- 	sort = 1000000 + color_value - v.seq
					-- end

					sort = 10000 + color_value - v.seq
				else
					sort = color_value - v.seq
				end
				
				v.sort = sort
			end
	
			table.sort(target_data_list, SortTools.KeyUpperSorter("sort"))
	
			for i = 1, #target_data_list do
				table.insert(nav_data_list, target_data_list[i])
			end

			return nav_data_list
		end
	end

	return target_data
end

function PositionalWarfareWGData:GetCurSceneBossInfo(seq)
	return self.monster_info_list[seq]
end

function PositionalWarfareWGData:GetEnterSceneSelectDataCache()
	return self.enter_scene_land_seq_cache, self.enter_scene_boss_seq_cache
end

function PositionalWarfareWGData:GetZhanLingLevel()
	return self.zhanling_level
end

function PositionalWarfareWGData:GetCityBossActiveState(land_seq, seq)
	if not self:IsInFightTime() then
		return false
	end

	return (self.city_boss_state[land_seq] or {})[seq] == 0
end

function PositionalWarfareWGData:IsGetLandCaptureRewrd(land_seq)
	return self.capture_reward_flag[land_seq] == 1
end

-- 占领奖励
function PositionalWarfareWGData:CanGetLandCaptureRewrd(group_index, land_seq)
	if self:IsInFightTime() then
		return false
	end

	local my_camp = self:GetMyCamp()
	if my_camp < 0 then
		return false
	end

	if not self:IsLinkMyBaseCity(land_seq) then
		return false
	end
	
	local cur_city_info = self:GetLandInfoBySeq(land_seq)
	if not IsEmptyTable(cur_city_info) then
		if cur_city_info.owner_camp == my_camp then
			local is_get_reward = self:IsGetLandCaptureRewrd(land_seq)

			if not is_get_reward then
				return true
			end
		end
	end
	
	return false
end

function PositionalWarfareWGData:GetCampCfgByServerIdAndGUildId(server_id, guild_id)
	local camp_data_list = self:GetCampInfoDataList()
	local group_index = self:GetMyGroupIndex()
	local cfg = {}
	local camp_id = -1

	if not IsEmptyTable(camp_data_list) then
		for k, v in pairs(camp_data_list) do
			if (group_index > 0 and v.param.temp_low == server_id) or (group_index <= 0 and v.guild_id == guild_id) then
			-- if v.param.temp_low == server_id or v.param == guild_id then
				camp_id = v.camp
				break
			end
		end
	end

	if camp_id >= 0 then
		cfg = self:GetCurCampCfg(camp_id)
	end

	return cfg
end

function PositionalWarfareWGData:GetCallInfo()
	return self.call_land_seq, self.call_cd_time
end
-------------------------------------PROTOCOL_GET_END-----------------------------------------

-------------------------------------CFG_GET_START-----------------------------------------
function PositionalWarfareWGData:GetShopCfg()
	return self.shop_cfg
end

-- 骚操作 配置表 seq 复数 标识不限购    后端为了省服务器资源，不需记录不限购的商品兑换次数
function PositionalWarfareWGData:GetShowShopDataList()
	local data_list = {}
	local shop_score = self:GetShopExchangeScore()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for k, v in pairs(self:GetShopCfg()) do
		if open_day >= v.open_day_limit then
			local has_limit_type = v.seq >= 0
			local score_enough = shop_score >= v.need_shop_score
			local sort_value = 999
	
			if has_limit_type then
				local exchange_time = self:GetShopItemExchangeTimeBySeq(v.seq)
				local exchange_time_enough = exchange_time < v.times_limit
				local sell_out = exchange_time >= v.times_limit
				local can_exchange = exchange_time_enough and score_enough 
				sort_value = can_exchange and v.seq or (sell_out and (1000 + v.seq) or (100 + v.seq))
			else
				sort_value = score_enough and v.seq or (100 + v.seq)
			end
	
			local data = {}
			data.sort = sort_value
			data.has_limit_type = has_limit_type
			data.cfg = v
			table.insert(data_list, data)
		end
	end

	table.sort(data_list, SortTools.KeyLowerSorter("sort"))

	return data_list
end

function PositionalWarfareWGData:GetCampCfg()
	return self.camp_cfg
end

function PositionalWarfareWGData:GetCampCfgByGroupIndex(group_index)
	return self.camp_cfg[group_index]
end

function PositionalWarfareWGData:GetCurCampListCfg()
	local group_index = self:GetMyGroupIndex()
	return self.camp_cfg[group_index]
end

function PositionalWarfareWGData:GetCampCfg(group_index, camp)
	return (self.camp_cfg[group_index] or {})[camp]
end

function PositionalWarfareWGData:GetCurCampCfg(camp)
	local group_index = self:GetMyGroupIndex()
	return (self.camp_cfg[group_index] or {})[camp]
end

function PositionalWarfareWGData:GetPersonScoreRankRewardCfg(rank_id)
	local group_index = self:GetMyGroupIndex()
	local group_data_list = (self.person_rank_reward_cfg[group_index] or {})[CROSS_LAND_WAR_RANK_TYPE.DEVOTE]

	if not IsEmptyTable(group_data_list) then
		for k, v in pairs(group_data_list) do
			if rank_id >= v.min_rank and rank_id <= v.max_rank then
				return v
			end
		end		
	end
end

function PositionalWarfareWGData:GetPersonScoreRankRewardDataListCfg()
	local group_index = self:GetMyGroupIndex()
	local group_data_list = (self.person_rank_reward_cfg[group_index] or {})[CROSS_LAND_WAR_RANK_TYPE.DEVOTE]
	local data_list = {}

	if not IsEmptyTable(group_data_list) then
		for k, v in pairs(group_data_list) do
			for i = v.min_rank, v.max_rank, 1 do
				data_list[i] = {rank_id = i, cfg = v}
			end
		end		
	end

	return data_list
end

function PositionalWarfareWGData:GetPersonKillRankRewardCfg(rank_id)
	local group_index = self:GetMyGroupIndex()
	local group_data_list = (self.person_rank_reward_cfg[group_index] or {})[CROSS_LAND_WAR_RANK_TYPE.KILL]

	if not IsEmptyTable(group_data_list) then
		for k, v in pairs(group_data_list) do
			if rank_id >= v.min_rank and rank_id <= v.max_rank then
				return v
			end
		end		
	end
end

function PositionalWarfareWGData:GetPersonKillRankRewardDataListCfg()
	local group_index = self:GetMyGroupIndex()
	local group_data_list = (self.person_rank_reward_cfg[group_index] or {})[CROSS_LAND_WAR_RANK_TYPE.KILL]
	local data_list = {}

	if not IsEmptyTable(group_data_list) then
		for k, v in pairs(group_data_list) do
			for i = v.min_rank, v.max_rank, 1 do
				data_list[i] = {rank_id = i, cfg = v}
			end
		end		
	end

	return data_list
end

function PositionalWarfareWGData:GetPersonScoreRewardCfg()
	return self.devote_reward_cfg
end

function PositionalWarfareWGData:GetOtherAttrValue(attr_name)
	return self.other_cfg[attr_name]
end

function PositionalWarfareWGData:GetPersonScoreRewardDataList()
	local target_data_list = {}
	local data_list = self:GetPersonScoreRewardCfg()

	for k, v in pairs(data_list) do
		if v.is_added == 1 then
			target_data_list[#target_data_list + 1] = v.item
		end
	end

	return target_data_list
end

function PositionalWarfareWGData:GetLandDataListCfgByGroupIndex(group_index)
	return self.land_cfg[group_index]
end

function PositionalWarfareWGData:GetLandCfg(seq)
	local group_index = self:GetMyGroupIndex()
	return (self.land_cfg[group_index] or {})[seq]
end

-- 阵营点cfg  基础城市点cfg
function PositionalWarfareWGData:GetCityCfgDataList()
	local group_index = self:GetMyGroupIndex()
	return self:GetCampCfgByGroupIndex(group_index), self:GetLandDataListCfgByGroupIndex(group_index)
end

function PositionalWarfareWGData:GetLandDataCache(group_index, seq)
	return (self.land_cache[group_index] or {})[seq]
end

function PositionalWarfareWGData:GetCampCfgByCamp(camp)
	local group_index = self:GetMyGroupIndex()
	return (self.camp_cache[group_index] or {})[camp]
end

-- function PositionalWarfareWGData:GetMonsterCfg(group_index, land_seq)
-- 	return (self.monster_cfg[group_index] or {})[land_seq] 
-- end

function PositionalWarfareWGData:GetCurMonsterListCfg(land_seq)
	local group_index = self:GetCurMonsterCfgIndex()
	return (self.monster_cfg[group_index] or {})[land_seq] 
end

function PositionalWarfareWGData:GetCurMonsterCfg(land_seq, monster_seq)
	local group_index = self:GetCurMonsterCfgIndex()
	return ((self.monster_cfg[group_index] or {})[land_seq] or {})[monster_seq]
end

function PositionalWarfareWGData:GetCurMonsterCfgData(group_index, land_seq, monster_seq)
	return ((self.monster_cfg[group_index] or {})[land_seq] or {})[monster_seq]
end

function PositionalWarfareWGData:GetMapId()
	local group_index = self:GetMyGroupIndex()
	return self.map_cache[group_index]
end

function PositionalWarfareWGData:GetZhanLingBuyLevelCfg()
	local group_index = self:GetMyGroupIndex()
	return self.order_exp_cfg[group_index]
end

function PositionalWarfareWGData:GetGroupCfgByGroupIndex(group_index)
	return self.group_cfg[group_index]
end

function PositionalWarfareWGData:GetCurGroupCfg()
	local group_index = self:GetMyGroupIndex()
	return self.group_cfg[group_index]
end

function PositionalWarfareWGData:GetCurZhanLingRewardCfgList()
	local group_index = self:GetMyGroupIndex()
	return self.order_cfg[group_index]
end

function PositionalWarfareWGData:GetZhanLingCfg(seq)
	local group_index = self:GetMyGroupIndex()
	return (self.order_cfg[group_index] or {})[seq]
end

function PositionalWarfareWGData:GetZhanLingMaxLevelCfg()
	local group_index = self:GetMyGroupIndex()
	return self.zhanling_max_level_cfg[group_index]
end

function PositionalWarfareWGData:GetCaptureRewardCfg(land_seq)
	local group_index = self:GetMyGroupIndex()
	local land_cfg = self:GetLandCfg(land_seq)

	return (self.capture_reward_cfg[group_index] or {})[land_cfg.type] or {}
end

function PositionalWarfareWGData:GetActivityTimeInfo()
	-- 根据主服开服天数获得 周限制
	local week_open, week_no_open_tip, index = self:CheckWeekDay()
	-- local is_in_fight_time = false

	if not week_open then
		return week_no_open_tip, false
	end

	return self:CheckDayTime(index)
end

function PositionalWarfareWGData:CheckWeekDay()
	local main_open_day = self:GetMainServerOpenTime()

	local week_index = 0
	for k, v in pairs(self.vie_time_group_cfg) do
		if main_open_day >= v.min_open_day and main_open_day <= v.max_open_day then
			week_index = v.index
			break
		end
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
    local cur_week_day = TimeUtil.FormatSecond3MYHM1(server_time)

	if cur_week_day == 7 then
		cur_week_day = 0
	end

	local week_cache = self.vie_time_group_cache[week_index]
	local week_open = ((week_cache or {})[cur_week_day]) == true
	return week_open, Language.PositionalWarfare.ACTTimeTypeStr[0], week_index
end

function PositionalWarfareWGData:CheckDayTime(index)
	local vie_time_data = self.vie_time_cfg[index]

	if IsEmptyTable(vie_time_data) then
		return Language.PositionalWarfare.ACTTimeTypeStr[1], false
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_tab = TimeUtil.FormatUnixTime2Date(server_time)
	local cur_hour, cur_min, cur_sec = time_tab.hour, time_tab.minute, time_tab.second
	local cur_time = cur_hour * 60 *60 + cur_min * 60 + cur_sec

	for k, v in pairs(vie_time_data) do
		local min_hour = math.floor(v.activity_start_time / 100)
		local min_min = math.floor(v.activity_start_time % 100)
		local max_hour = math.floor(v.activity_end_time / 100)
		local max_min = math.floor(v.activity_end_time % 100)

		-- 游戏中
		if (cur_time >= (min_hour * 60 * 60 + min_min * 60)) and (cur_time <= (max_hour * 60 * 60 + max_min * 60)) then
			local time_count = (max_hour * 60 * 60 + max_min * 60) - cur_time
			time_count = time_count > 0 and time_count or 0
			local time_str = TimeUtil.FormatSecondDHM9(time_count)
			return string.format(Language.PositionalWarfare.ACTTimeTypeStr[2], time_str), true
		elseif cur_time < (min_hour * 60 * 60 + min_min * 60) then
			--等下下轮
			local hour_str = min_hour < 10 and "0" .. min_hour or min_hour
			local min_str = min_min < 10 and "0" .. min_min or min_min
			return string.format(Language.PositionalWarfare.ACTTimeTypeStr[3], hour_str, min_str), false
		end
	end

	return Language.PositionalWarfare.ACTTimeTypeStr[4], false
end

function PositionalWarfareWGData:IsCanEnter(land_data)
	local tired = self:GetTired()
	
	local city_info = PositionalWarfareWGData.Instance:GetLandInfoBySeq(land_data.seq)
	if IsEmptyTable(city_info) then
		return false
	end

	local my_camp = self:GetMyCamp()

	local is_link_base_city = self:IsLinkMyBaseCity(land_data.seq)
	if not is_link_base_city then
		return false
	end

	local owner_camp = city_info.owner_camp
	local tem_owner_camp = city_info.tem_owner_camp

	if tem_owner_camp < 0 then
		if owner_camp == my_camp then
			return true
		end
	else
		if tem_owner_camp == my_camp then
			return true
		end
	end

	-- 已经是我的门派占领的城市
	-- if tem_owner_camp == my_camp or owner_camp == my_camp then
	-- 	return true
	-- end

	local cache_data = self:GetLandDataCache(land_data.group_index, land_data.seq)

	if not IsEmptyTable(cache_data) then
		local connect_camp_data = cache_data.connect_camp
		if not IsEmptyTable(connect_camp_data) then
			-- 有营地连接
			for i, u in pairs(connect_camp_data) do
				if tonumber(u) == my_camp then
					return true
				end
			end
		end 

		-- 前置营地有我占领的
		local pre_land_data = cache_data.pre_land
		if not IsEmptyTable(pre_land_data) then
			for i, u in pairs(pre_land_data) do
				local index = tonumber(u)
				local pre_city_info = PositionalWarfareWGData.Instance:GetLandInfoBySeq(index)
				local owner_camp = (pre_city_info or {}).owner_camp or -1
				local tem_owner_camp = (pre_city_info or {}).tem_owner_camp or -1

				if tem_owner_camp < 0 then
					if owner_camp == my_camp then
						return true
					end
				else
					if tem_owner_camp == my_camp then
						return true
					end
				end

				-- if tem_owner_camp == my_camp or owner_camp == my_camp then
				-- 	return true
				-- end
			end
		end
	end
	
	return false
end

function PositionalWarfareWGData:GetZhanLingLevelByDevote(devote)
	local cur_zhanling_cfg = self:GetCurZhanLingRewardCfgList()
	local level = 0

	for k, v in pairs(cur_zhanling_cfg) do
		if devote < v.need_devote then
			break
		end

		level = v.seq
	end

	return level
end

function PositionalWarfareWGData:GetBossNextRefreshTime()
	local week_open, str, week_index = self:CheckWeekDay()

	if not week_open then
		return false
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_tab = TimeUtil.FormatUnixTime2Date(server_time)
	local cur_hour, cur_min, cur_sec = time_tab.hour, time_tab.minute, time_tab.second
	local cur_time = cur_hour * 60 *60 + cur_min * 60 + cur_sec
	local vie_time_data = self.vie_time_cfg[week_index]

	for k, v in pairs(vie_time_data) do
		local min_hour = math.floor(v.activity_start_time / 100)
		local min_min = math.floor(v.activity_start_time % 100)
		local max_hour = math.floor(v.activity_end_time / 100)
		local max_min = math.floor(v.activity_end_time % 100)
		max_min = (max_min - 1)
		max_min = max_min > 0 and max_min or 0

		if cur_time < min_hour * 60 * 60 + min_min * 60 then
			return true, (TimeWGCtrl.Instance:NowDayTimeStart(server_time) + min_hour * 60 * 60 + min_min * 60)
		end
	end

	return false
end

function PositionalWarfareWGData:IsInFightTime()
	local week_open, str, week_index = self:CheckWeekDay()

	if not week_open then
		return false
	end

	local vie_time_data = self.vie_time_cfg[week_index]

	if IsEmptyTable(vie_time_data) then
		return false
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local time_tab = TimeUtil.FormatUnixTime2Date(server_time)
	local cur_hour, cur_min, cur_sec = time_tab.hour, time_tab.minute, time_tab.second
	local cur_time = cur_hour * 60 *60 + cur_min * 60 + cur_sec

	for k, v in pairs(vie_time_data) do
		local min_hour = math.floor(v.activity_start_time / 100)
		local min_min = math.floor(v.activity_start_time % 100)
		local max_hour = math.floor(v.activity_end_time / 100)
		local max_min = math.floor(v.activity_end_time % 100)
		-- max_min = max_min - 1
		-- max_min = max_min > 0 and max_min or 0

		if (cur_time >= (min_hour * 60 * 60 + min_min * 60)) and (cur_time <= (max_hour * 60 * 60 + max_min * 60)) then
			return true
		-- elseif cur_time < (min_hour * 60 * 60 + min_min * 60) then
		-- 	return false
		end
	end

	return false
end

function PositionalWarfareWGData:GetCurMonsterCfgIndex()
	local index = 0
	local group_index = self:GetMyGroupIndex()
	local group_data = self.monster_group_cfg[group_index]

	if not IsEmptyTable(group_data) then
		local open_day = self:GetMainServerOpenTime()

		for k, v in pairs(group_data) do
			if open_day >= v.min_open_day and open_day <= v.max_open_day then
				index = v.index
				break
			end
		end
	end

	return index
end

function PositionalWarfareWGData:CalLinkBaseCityCache()
	self.link_base_city_cache = {}
	local my_camp = self:GetMyCamp()

	if my_camp < 0 then
		return
	end

	-- 获取领地列表   阵营数据  领地数据
	local base_city_data, nor_city_data = self:GetCityCfgDataList()
	local base_land_seq = -1

	if not IsEmptyTable(nor_city_data) then
        for k, v in pairs(nor_city_data) do
			-- 获取 
			local link_cache_data = self:GetLandDataCache(v.group_index, v.seq)

			if not IsEmptyTable(link_cache_data) then
				local connect_camp_data = link_cache_data.connect_camp
				if not IsEmptyTable(connect_camp_data) then
					for k, v in pairs(connect_camp_data) do
						if tonumber(v) >= 0 then
							self:AddLinkBaseCity(tonumber(v))
						end
					end
				end
			end
        end
    end
end

function PositionalWarfareWGData:AddLinkBaseCity(land_seq)
	self.link_base_city_cache[land_seq] = land_seq

	local add_land = function(link_cache)
		local pre_city_data = link_cache.pre_city

		if not IsEmptyTable(pre_city_data) then
			for k, v in pairs(pre_city_data) do
				if tonumber(v) > 0 then
					self:AddLinkBaseCity(tonumber(v))
				end
			end
		end
	end

	local land_cfg = self:GetLandCfg(land_seq)
	if IsEmptyTable(land_cfg) then
		return
	end

	local link_cache_data = self:GetLandDataCache(land_cfg.group_index, land_cfg.seq)

	local city_info = self:GetLandInfoBySeq(land_seq)
	local owner_camp = city_info and city_info.owner_camp or -1
	local tem_owner_camp = city_info and city_info.tem_owner_camp or -1
	local my_camp = self:GetMyCamp()

	if tem_owner_camp < 0 then
		if owner_camp >= 0 and my_camp == owner_camp then
			add_land(link_cache_data)
		end
	else
		if tem_owner_camp == my_camp then
			add_land(link_cache_data)
		end
	end

	-- if owner_camp >= 0 and my_camp == owner_camp then
	-- 	add_land(link_cache_data)
	-- end
end

function PositionalWarfareWGData:IsLinkMyBaseCity(land_seq)
	return nil ~= self.link_base_city_cache[land_seq]
end

function PositionalWarfareWGData:GetGamePlayDesCfg()
	return self.des_cfg
end

function PositionalWarfareWGData:GetTiredDragItemList()
	return self.tired_drug_item_list
end
-------------------------------------CFG_GET_END-------------------------------------------