TransferGiftRender = TransferGiftRender or BaseClass(BaseRender)

function TransferGiftRender:__delete()
	if self.item then
		self.item:DeleteMe()
		self.item = nil
	end
end

function TransferGiftRender:LoadCallBack()
	if not self.item then
		self.item = ItemCell.New(self.node_list.item_pos)
	end
end

function TransferGiftRender:OnFlush()
	self.item:SetData(self.data)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	self.node_list.shizhuang_name.text.text = item_cfg.name
end


--------------------------------------------------------------
-- 一键转职提示选择
--------------------------------------------------------------
TipAlert = TipAlert or BaseClass(SafeBaseView)
function TipAlert:__init()
	self:SetMaskBg(true)
	self.ui_config = {"uis/view/dialog_ui_prefab", "TansferUi"}
	self:AddViewResource(0, "uis/view/dialog_ui_prefab", "layout_alert_three")

end
-- 设置内容
function TipAlert:SetLableString(str)
	if nil ~= str then
		self.content_str = str
		if nil ~= self.node_list["rich_dialog"] then

			self.node_list["rich_dialog"].text.text = self.content_str
		end
	end
end
function TipAlert:LoadCallBack()
	self.rich_dialog_param = {}
	self:SetLableString(self.content_str)
	self.node_list["lbl_maxhp_add_value"].text.text = self.attr_cfg.maxhp * self.multiple
	self.node_list["lbl_gongji_add_value"].text.text = self.attr_cfg.gongji * self.multiple
	self.node_list["lbl_fangyu_add_value"].text.text = self.attr_cfg.fangyu * self.multiple
	self.node_list["lbl_fa_fangyu_add_value"].text.text = self.attr_cfg.fa_fangyu * self.multiple
	self.node_list["lbl_pojia_add_value"].text.text = self.attr_cfg.pojia * self.multiple
	local role_data = RoleWGData.Instance.role_vo

	self.node_list["lbl_maxhp_value"].text.text = role_data.max_hp
	self.node_list["lbl_gongji_value"].text.text = role_data.gong_ji
	self.node_list["lbl_fangyu_value"].text.text = role_data.fang_yu
	self.node_list["lbl_fa_fangyu_value"].text.text = role_data.fa_fang_yu
	self.node_list["lbl_pojia_value"].text.text = role_data.po_jia
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind1(self.OnClickOK, self))
	self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind1(self.OnClickCancel, self))

end

function TipAlert:OnClickOK()--sss
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_ZHUANZHI3_TASK_ONE_KEY_FINISH)
	self:Close()
	TransFerWGCtrl.Instance.view:DestroyTransferPrefab()
end

function TipAlert:OnClickCancel()
	self:Close()
end
function TipAlert:SetAddAttr(attr_cfg , index)
	if nil == attr_cfg  then return end
	self.attr_cfg = attr_cfg
	self.multiple = index
end

-----------------------------TransFerSlotItem-----------------------------

TransFerSlotItem = TransFerSlotItem or BaseClass(BaseRender)

function TransFerSlotItem:__init()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	XUI.AddClickEventListener(self.node_list["skill_bg"], BindTool.Bind(self.ClickSkill,self))
end

function TransFerSlotItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TransFerSlotItem:CheckShowSlotItem()
	local is_skill = self.data.slot_type == TransFerWGData.SlotType.Skill
	self.node_list["skill_bg"]:SetActive(is_skill)
	self.node_list["name"]:SetActive(is_skill)
	self.node_list["slot_itemcell"]:SetActive(not is_skill)
	self.node_list["desc"]:SetActive(not is_skill)
end

function TransFerSlotItem:OnFlush()
	if not self.data then return end
	local slot_type = self.data.slot_type or 0
	if slot_type == 0 then return end

	self:CheckShowSlotItem()

	self.node_list["name"].text.text = "" --self.data.slot_name
	self.node_list["desc"].text.text = "" --self.data.slot_desc

	if slot_type == TransFerWGData.SlotType.Skill then
		local skill_cfg = SkillWGData.Instance:GetPassiveSkillByIndex(self.data.slot_param)
		if skill_cfg then
			self.node_list["skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon))
		end
	elseif slot_type == TransFerWGData.SlotType.Equip then
		local param_list = Split(self.data.slot_param,"|")
		local role_sex = RoleWGData.Instance:GetRoleSex()
		local item_id = role_sex == GameEnum.MALE and param_list[1] or param_list[2]
		item_id = tonumber(item_id)
		local item_data = {item_id = item_id}
		self.item_cell:SetData(item_data)
	elseif slot_type == TransFerWGData.SlotType.Item then
		local item_data = {item_id = self.data.slot_param}
		self.item_cell:SetData(item_data)

	end
	self.node_list.flag:CustomSetActive(self.data.slot_subtype == 2)
end

function TransFerSlotItem:ClickSkill()
	if not self.data then return end

    local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(self.data.slot_param)
    local capability = 0
    local limit_text = skill_data.des_1
    local show_data = {
        icon = skill_data.icon,
        top_text = skill_data.name,
        body_text = skill_data.drak_desc,
        limit_text = limit_text,
        show_bg_kunag = true,
		hide_level = true,
        x = 0,
        y = -190,
        set_pos = true,
        capability = capability,
    }

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)

end

-----------------------------TransferTaskStateOneItem-----------------------------
TransferTaskStateOneItem = TransferTaskStateOneItem or BaseClass(BaseRender)
function TransferTaskStateOneItem:__init()
	XUI.AddClickEventListener(self.node_list["task_btn"], BindTool.Bind(self.ClickTaskBtn, self))
	XUI.AddClickEventListener(self.node_list["task_get_btn"], BindTool.Bind(self.ClickTaskBtn, self))
	self.view.rect_fancy_cell.RefreshPosDel = BindTool.Bind(self.UpdatePosition, self)

	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function TransferTaskStateOneItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function TransferTaskStateOneItem:OnFlush()
	if not self.data then return end

	local task_cfg = TaskWGData.Instance:GetTaskConfig(self.data.task_id)
	local task_info = TaskWGData.Instance:GetTaskInfo(task_cfg.task_id)
	local is_complete = TaskWGData.Instance:GetTaskIsCompleted(self.data.task_id)
	local is_can_get = false
	local per = ""
	local progress_num = 0
	if is_complete then
		per = ToColorStr(string.format("（%s/%s）", task_cfg.c_param2, task_cfg.c_param2) , COLOR3B.C7)
	else
		if task_info then
			progress_num = task_info.progress_num
		end

		if progress_num >= task_cfg.c_param2 then
			per = ToColorStr(string.format("（%s/%s）", progress_num, task_cfg.c_param2),  COLOR3B.DEFAULT_NUM)
			is_can_get = true
		else
			per = ToColorStr(string.format("（%s/%s）", progress_num, task_cfg.c_param2), COLOR3B.PINK)
		end
	end


	
	-- local desc = XmlUtil.RelaceTagContent(self.data.progress_desc, "per", per)
	self.item_cell:SetData({item_id = 101})

	local can_commit = not is_complete and is_can_get

	self.node_list["task_desc"].text.text = ToColorStr(self.data.progress_desc, (is_complete or can_commit) and COLOR3B.C7 or COLOR3B.C4)
	self.node_list["task_num"].text.text = per

	self.node_list["task_finish"]:SetActive(is_complete)
	self.node_list["task_get_btn"]:SetActive(can_commit)
	self.node_list["task_btn"]:SetActive(not is_complete and not is_can_get)

	local level = RoleWGData.Instance:GetAttr('level')
	self.node_list["remind"]:SetActive(task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 and level >= task_cfg.min_level)
	self.node_list["get_remind"]:SetActive(task_cfg.condition ~= GameEnum.TASK_COMPLETE_CONDITION_3 and can_commit and level >= task_cfg.min_level)
end

function TransferTaskStateOneItem:ClickTaskBtn()
	local task_cfg = TaskWGData.Instance:GetTaskConfig(self.data.task_id)
	if not task_cfg then return end

	local level = RoleWGData.Instance:GetAttr('level')
	if task_cfg and level < task_cfg.min_level then
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.TransFer.AcceptTip_1, RoleWGData.Instance:TransToDianFengLevelStr(task_cfg.min_level)))
		return
	end

	if YunbiaoWGData.Instance:GetIsHuShong() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.YunBiao.CanNotDO)
		return
	end

	local task_status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
	local open_panel_name = self.data.open_panel_name
	if task_status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
		if open_panel_name ~= nil and open_panel_name ~= "" then
			if string.find(open_panel_name, "ItemTipGetWay") then
				--根据物品id打开 物品tips的获取路径
				local t = Split(open_panel_name, "#")
				if t[2] then
					local item_id = tonumber(t[2])
					local task_info = TaskWGData.Instance:GetTaskInfo(task_cfg.task_id)
					local need_num = 1
					if task_info then
						local progress_num = task_info.progress_num
						need_num = progress_num < task_cfg.c_param2 and (task_cfg.c_param2 - progress_num) or 0
						TipWGData.Instance:SetDefShowBuyCount(need_num)
					end
					TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = item_id })
				end
			elseif string.find(open_panel_name, "TaskGainExpTipView") then
				TaskWGCtrl.Instance:OpenTaskGainExpTipView(Vector2(-305, -220), Vector2(1, 1))
			elseif string.find(open_panel_name, "JumToByID") then
				local t = Split(open_panel_name, "#")
				if t[2] then
					local boss_id = tonumber(t[2])
					BossOfferWGCtrl.Instance:JumToByID(boss_id)
					ViewManager.Instance:Close(GuideModuleName.ZhuanSheng)
				end
			else
				FunOpen.Instance:OpenViewNameByCfg(open_panel_name)
				ViewManager.Instance:Close(GuideModuleName.ZhuanSheng)
			end
			return
		end
	end

	if task_cfg.condition == GameEnum.TASK_COMPLETE_CONDITION_3 then
		local status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
		if status == GameEnum.TASK_STATUS_COMMIT then
			TaskWGCtrl.Instance:OperateFollowTask(task_cfg)
		else
			FuBenWGCtrl.Instance:SendEnterFB(task_cfg.c_param1, task_cfg.c_param3)
		end
		return
	end

	TaskWGCtrl.Instance:OperateFollowTask(task_cfg)
end

function TransferTaskStateOneItem:UpdatePosition(normalizedPosition)
	local wave = (math.sin((normalizedPosition/0.2164+0.5) * math.pi)) * 41;
	self.view.transform.localPosition = self.view.transform.localPosition + Vector3(1 * wave, 0, 0)
end

----------------------------------------------------------------------------------------------
TransFerSkillTips = TransFerSkillTips or BaseClass(SafeBaseView)

function TransFerSkillTips:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/tansfer_ui_prefab", "layout_transfer_skill_tip")
end

function TransFerSkillTips:__delete()

end

function TransFerSkillTips:ReleaseCallBack()

end

function TransFerSkillTips:SetSkillData(index)
	self.index = index
end

function TransFerSkillTips:LoadCallBack()

end

function TransFerSkillTips:ShowIndexCallBack()
	self:Flush()
end

function TransFerSkillTips:OnFlush()
	local skill_cfg = SkillWGData.Instance:GetPassiveSkillByIndex(self.index)
	self.node_list.ph_ml_skill_item.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon))
	self.node_list.skill_name.text.text = "【"..skill_cfg.name.."】"
	self.node_list.skill_dsc.text.text = skill_cfg.drak_desc
	self.node_list.limit_text.text.text = skill_cfg.des_1
end