EquipmentWGData = EquipmentWGData or BaseClass()

local MAX_BAOSHI_LEVEL = 10			--宝石最大等级
local MAX_STAR_LEVEL = 3 			--装备高升至3星
local MAX_QUALITY = 7 				--装备最高品质

local ATTACK_TYPE = 0 				--装备觉醒：攻击类型
local DEFENSE_TYPE = 1 				--装备觉醒：防御类型

--期限小鬼id
local QIXIAN_XIAOGUI = 10101

function EquipmentWGData:__init()
	if EquipmentWGData.Instance ~= nil then
		ErrorLog("[EquipmentWGData] attempt to create singleton twice!")
		return
	end

	EquipmentWGData.Instance = self

	local equipforge_cfg = EquipmentWGData.GetConfig()
	self.equip_shenping_attr_cfg = ListToMap(equipforge_cfg.equip_attr_god, "equip_id")
	self.refine_max_count_cfg = equipforge_cfg.refine_max_count
	self.shenzhu_cfg = equipforge_cfg.shenzhu
	self.compose_cfg = equipforge_cfg.compose
	self.total_shenzhu_cfg = equipforge_cfg.total_shenzhu
	self.strength_grade_level = equipforge_cfg.strength_grade_level
	self.new_strength = self:InitEquipmentCfgList(equipforge_cfg.new_strength, "equip_index", "strength_level")
	self.total_strength = equipforge_cfg.total_strength
	self.xianqi_total_strength = equipforge_cfg.xianqi_total_strength
	self.equip_total_star_attr_cfg = ListToMapList(equipforge_cfg.equip_total_star_attr, "body_seq")

	self.impguard_cfg = ConfigManager.Instance:GetAutoConfig("impguard_auto")
	self.imp_cfg = ListToMap(self.impguard_cfg.imp, "item_id")
	self.impguard_compose_show_cfg = ListToMapList(self.impguard_cfg.compose_show, "scene_guard_type")
	self.equip_rand_attr_cfg_map = ListToMap(equipforge_cfg.equip_rand_attr, "color", "big_type", "attr_type")

	self.equipment_cfg = equipforge_cfg

	--新锻造升品配置
	local tmp_cfg = EquipmentWGData.GetConfig()
	self.new_equip_sp_cfg = ListToMap(tmp_cfg.equip_upquality, "equip_part", "quality_grade_star")
	self.new_equip_sp_attr_cfg = ListToMap(tmp_cfg.equip_upquality_attr, "equip_part", "quality_grade_star")
	self.new_equip_sp_suit_cfg = tmp_cfg.common_equip_upquality_suit
	self.new_equip_sp_xianqi_suit_cfg = tmp_cfg.xianqi_equip_upquality_suit

	--仙器特殊属性
	self.equip_rand_num_cfg = ListToMap(EquipmentWGData.GetConfig().equip_rand_num, "color")

	--装备特殊属性
	local special_attr_cfg = ConfigManager.Instance:GetAutoConfig("equip_extra_baseattr_auto")
	self.equip_special_attr_p_cfg = ListToMap(special_attr_cfg.extra_attr_p, "equip_id")
	self.equip_special_attr_g_cfg = ListToMap(special_attr_cfg.extra_attr_g, "equip_id")
	self.equip_special_attr_c_cfg = ListToMap(special_attr_cfg.extra_attr_c, "equip_id")

	--装备分解
	self.equip_decompose_cfg = ListToMap(tmp_cfg.equip_decompose, "decompose_equip_id", "decompose_equip_best_attr_num")

	--仙器合成--升阶
	self.xq_compose_upgrade_origin = ListToMap(equipforge_cfg.xianqi_upgrade, "origin_equip_id", "origin_equip_star")
	self.xq_compose_upgrade_target = ListToMap(equipforge_cfg.xianqi_upgrade, "target_equip_id", "target_equip_star")
	--仙器合成--升星
	self.xq_compose_upstar_origin = ListToMap(equipforge_cfg.xianqi_upstar, "origin_equip_id", "origin_equip_star")
	self.xq_compose_upstar_target = ListToMap(equipforge_cfg.xianqi_upstar, "target_equip_id", "target_equip_star")

	-- 缓存装备数据(通过 子类型 阶数 品质 性别 职业 划分配置)
	self.cahce_all_wear_equip_data = self:InitAllWearEquipSplitData()

	-- 装备技能
	self.equip_skill_map_cfg = ListToMap(equipforge_cfg.equip_skill, "equip_id")

	-- 属性名
	self.attr_id_map_name_cfg = ConfigManager.Instance:GetAutoConfig("shuxing_name_cfg_auto").base_attr_name
	self.attr_name_map_name_cfg = {}
	for k,v in pairs(self.attr_id_map_name_cfg) do
		self.attr_name_map_name_cfg[v.attr_str] = v
	end

	self.attr_name_show_cfg = {}
	for k,v in pairs(self.attr_id_map_name_cfg) do
		self.attr_name_show_cfg[v.name_show_no_space] = v
		self.attr_name_show_cfg[v.name_show_with_space] = v
	end


	-- 缓存装备道具属性列表
	local item_cfg = ItemWGData.Instance:GetItemConfig(100)
	self.cache_equip_item_attr_key_list = {}
	local cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(item_cfg)
	for k,v in pairs(cfg_map_attr_list) do
		local data = {attr_str = v, attr_id = 0}
		data.attr_id = self:GetAttrIdByAttrStr(v)
		self.cache_equip_item_attr_key_list[k] = data
	end

	self.select_equip_qh_index = 0
	self.select_equip_bs_index = 0
	self.select_equip_sz_index = 0
	self.select_equip_cs_index = 0
	self.select_equip_jl_index = 0

	self.grid_list = {}							-- 装备锻造格子列表
	self.hecheng_cell_index = 0					-- 合成选择的格子 0左边 1右边
	self.hecheng_bag_cell_data = {}				-- 合成背包选择的格子数据
	self.shuliandu_list = {}					-- 强化熟练度列表


	self.equip_sp_star_info = {}


	self.is_show_upgrade_remind = true

	self.is_select_sp_special_item = false

	self.xiaogui_index = 1

	self:InitBaptizeData()
	self:InitSuitData()
	self:InitHechengData()
	self:InitBaoShiData()
	self:InitTranssexCfg()
	self:InitImperialSpiritCfg()
	self:InitZhuShenData()
	self:InitEquipmengStrengthData()

	--------------------------------------------------
	local remind_manager_instance = RemindManager.Instance
	remind_manager_instance:Register(RemindName.Equipment_Baptize, BindTool.Bind(self.GetEquipBaptizeRemindNum, self))

	remind_manager_instance:Register(RemindName.Equipment_Suit, BindTool.Bind(self.GetEquipSuitRemind, self))
	-- remind_manager_instance:Register(RemindName.Equipment_Suit_ZhuXian, BindTool.Bind(self.GetSuitRemindByIndex, self, TabIndex.equipment_suit_one))
	-- remind_manager_instance:Register(RemindName.Equipment_Suit_ZhuShen, BindTool.Bind(self.GetSuitRemindByIndex, self, TabIndex.equipment_suit_two))
	-- remind_manager_instance:Register(RemindName.Equipment_Suit_ZhuMo, BindTool.Bind(self.GetSuitRemindByIndex, self, TabIndex.equipment_suit_zhumo))
	remind_manager_instance:Register(RemindName.Equipment_Stone_Inlay, BindTool.Bind(self.GetEquipBaoShiRemindNum, self))
	remind_manager_instance:Register(RemindName.Equipment_Stone_Refine, BindTool.Bind(self.GetEquipBSJinglianRemindNum, self))
	remind_manager_instance:Register(RemindName.Equipment_ShengPin, BindTool.Bind(self.GetEquipShengPinRemindNum, self))
	remind_manager_instance:Register(RemindName.Equipment_Stone_Active, BindTool.Bind(self.GetEquipStoneActiveRemind, self)) --宝石强化属性激活
	-- remind_manager_instance:Register(RemindName.Equipment_Level_Active, BindTool.Bind(self.GetEquipLevelRemind, self)) --装备强化等级属性激活

	self:RegisterBaptizeRemindInBag(RemindName.Equipment_Baptize)
	-- self:RegisterSuitRemindInBag(RemindName.Equipment_Suit)
	-- self:RegisterSuitRemindInBag(RemindName.Equipment_Suit_ZhuXian, TabIndex.equipment_suit_one)
	-- self:RegisterSuitRemindInBag(RemindName.Equipment_Suit_ZhuShen, TabIndex.equipment_suit_two)
	-- self:RegisterSuitRemindInBag(RemindName.Equipment_Suit_ZhuMo, TabIndex.equipment_suit_zhumo)
	self:RegisterBaoShiRemindInBag(RemindName.Equipment_Stone_Inlay)
	self:RegisterJinglianRemindInBag(RemindName.Equipment_Stone_Refine)
	self:RegisterShengPinRemindInBag(RemindName.Equipment_ShengPin)
	self:RegisterStrengthRemindInBag(RemindName.Equipment_Strength)
end

function EquipmentWGData:__delete()
	EquipmentWGData.Instance = nil

	local remind_manager_instance = RemindManager.Instance
	remind_manager_instance:UnRegister(RemindName.Equipment_Baptize)
	remind_manager_instance:UnRegister(RemindName.Equipment_Suit)
	-- remind_manager_instance:UnRegister(RemindName.Equipment_Suit_ZhuXian)
	-- remind_manager_instance:UnRegister(RemindName.Equipment_Suit_ZhuShen)
	-- remind_manager_instance:UnRegister(RemindName.Equipment_Suit_ZhuMo)
	remind_manager_instance:UnRegister(RemindName.Equipment_Stone_Inlay)
	remind_manager_instance:UnRegister(RemindName.Equipment_Stone_Refine)
	remind_manager_instance:UnRegister(RemindName.Equipment_ShengPin)
	remind_manager_instance:UnRegister(RemindName.Equipment_Stone_Active)
	-- remind_manager_instance:UnRegister(RemindName.Equipment_Level_Active)

	self:DeleteImperialSpiritCfg()
	self:DeleteZhuShenCfg()
	self:DeleteEquipmengStrengthData()
end

function EquipmentWGData:InitEquipmentCfgList(cfg_list, key_1, key_2)
	local data_list = {}
	for k,v in pairs(cfg_list) do
		local data_key = v[key_1]
		if nil == data_list[data_key] then
			data_list[data_key] = {}
		end
		data_list[data_key][v[key_2]] = v
	end
	return data_list
end

-- 道具配置里用到的属性字段
function EquipmentWGData:GetBaseCfgAttrKey()
	return self.cache_equip_item_attr_key_list
end

-- 获得战斗属性名字
function EquipmentWGData.GetFightAttrNameByAttrId(attr_id, space_name)
	local name = ""
	local attr_name_cfg = ConfigManager.Instance:GetAutoConfig("shuxing_name_cfg_auto").fight_attr_name
	if attr_name_cfg[attr_id] then
		name = space_name and attr_name_cfg[attr_id].name_show or attr_name_cfg[attr_id].name
	end

	return name
end

-- 获取配置表的属性展示名
function EquipmentWGData:GetAttrName(attr_str, space_name, fen_hao)
	if type(attr_str) == "string" then
		return self:GetAttrNameByAttrStr(attr_str, space_name, fen_hao)
	elseif type(attr_str) == "number" then
		return self:GetAttrNameByAttrId(attr_str, space_name, fen_hao)
	end

	return ""
end

-- 获取配置表的属性展示名 by attr_id
function EquipmentWGData:GetAttrNameByAttrId(attr_id, space_name, fen_hao)
	local cfg = self.attr_id_map_name_cfg[attr_id]
	local str = ""
	if cfg then
		str = space_name and cfg.name_show_with_space or cfg.name_show_no_space
	else
		-- 兼容旧逻辑
		local attr_str = self:GetAttrStrByAttrId(attr_id)
		str = space_name and Language.Common.AttrNameList[attr_str] or Language.Common.TipsAttrNameList[attr_str]
	end

	return string.format("%s%s", str or "", fen_hao and "：" or "")
end

-- 获取配置表的属性展示名 by attr_str
function EquipmentWGData:GetAttrNameByAttrStr(attr_str, space_name, fen_hao)
	attr_str = AttributeMgr.GetAttributteKey(attr_str)
	local cfg = self.attr_name_map_name_cfg[attr_str]
	local str = ""
	if cfg then
		str = space_name and cfg.name_show_with_space or cfg.name_show_no_space
	else
		-- 兼容旧逻辑
		str = space_name and Language.Common.AttrNameList[attr_str] or Language.Common.TipsAttrNameList[attr_str]
	end

	return string.format("%s%s", str or "", fen_hao and "：" or "")
end

-- 获取配置表的前端定义英文名 by attr_id
function EquipmentWGData:GetAttrStrByAttrId(attr_id)
	local cfg = self.attr_id_map_name_cfg[attr_id]
	return cfg and cfg.attr_str
end

-- 获取属性id		by 前端定义英文名
function EquipmentWGData:GetAttrIdByAttrStr(attr_str)
	attr_str = AttributeMgr.GetAttributteKey(attr_str)
	local cfg = self.attr_name_map_name_cfg[attr_str]
	-- 兼容旧逻辑
	if not cfg then
		return RoleWGData.Instance:GetRoleAttrTypeByName(attr_str)
	end

	return cfg.attr_id
end

-- 获取是否万分比
function EquipmentWGData:GetAttrIsPer(attr_str)
	if type(attr_str) == "string" then
		return self:GetAttrIsPerByAttrStr(attr_str)
	elseif type(attr_str) == "number" then
		return self:GetAttrIsPerByAttrId(attr_str)
	end

	return false
end

-- 获取是否万分比		by attr_id
function EquipmentWGData:GetAttrIsPerByAttrId(attr_id)
	local cfg = self.attr_id_map_name_cfg[attr_id]
	local attr_str = cfg and cfg.attr_str or ""
	-- 兼容旧逻辑
	if not cfg then
		return IsPerAttr[attr_str]
	end

	return cfg.is_per == 1
end

-- 获取是否万分比		by 前端定义英文名
function EquipmentWGData:GetAttrIsPerByAttrStr(attr_str)
	attr_str = AttributeMgr.GetAttributteKey(attr_str)
	local cfg = self.attr_name_map_name_cfg[attr_str]
	-- 兼容旧逻辑
	if not cfg then
		return AttributeMgr.IsPerAttr(attr_str)
	end

	return cfg.is_per == 1
end

 -- 获取是基础属性
function EquipmentWGData:GetAttrIsBase(attr_str)
	if type(attr_str) == "string" then
		return self:GetAttrIsBaseByAttrStr(attr_str)
	elseif type(attr_str) == "number" then
		return self:GetAttrIsBaseByAttrId(attr_str)
	end

	return false
end


-- 获取是否基础属性		by attr_id
function EquipmentWGData:GetAttrIsBaseByAttrId(attr_id)
	local cfg = self.attr_id_map_name_cfg[attr_id]
	if not cfg then
		return false
	end

	return cfg.is_base_attr == 1
end

-- 获取是否基础属性		by 前端定义英文名
function EquipmentWGData:GetAttrIsBaseByAttrStr(attr_str)
	attr_str = AttributeMgr.GetAttributteKey(attr_str)
	local cfg = self.attr_name_map_name_cfg[attr_str]
	if not cfg then
		return false
	end

	return cfg.is_base_attr == 1
end

-- 获取是否基础属性		by name_show
function EquipmentWGData:GetAttrIsBaseByAttrName(attr_name)
	local cfg = self.attr_name_show_cfg[attr_name]
	if not cfg then
		return false
	end

	return cfg.is_base_attr == 1
end






function EquipmentWGData:RegisterBaptizeRemindInBag(remind_name)
	local map = {}
	for k,v in pairs(self.new_baptize_consume) do
		map[v.consume_item_id] = true
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list)
end

function EquipmentWGData:RegisterSuitRemindInBag(remind_name, index)
	index = (index % 10) - 1

	local map = {}
	local one_equip_act = ConfigManager.Instance:GetAutoConfig("suit_cfg_auto").one_equip_act
	for _, suit_item_cfg in pairs(one_equip_act) do
		if suit_item_cfg.suit_index == index then
			for k = 1, 3 do
				if suit_item_cfg["stuff_".. k .."_id"] ~= 0 then
					map[suit_item_cfg["stuff_".. k .."_id"]] = true
				end
			end
		end
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	local gift_item_id = 48071 -- 强化套装石自选包(写死不想给你遍历自选礼包表解析礼包找出材料)
	table.insert(item_id_list, gift_item_id)

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function EquipmentWGData:RegisterRongLianRemindInBag(remind_name)
	local map = {}
	local suit_smelt = ConfigManager.Instance:GetAutoConfig("suit_cfg_auto").suit_smelt
	for _, suit_item_cfg in pairs(suit_smelt) do
		for k = 1, 3 do
			if suit_item_cfg["stuff_id_".. k] ~= 0 then
				map[suit_item_cfg["stuff_id_".. k]] = true
			end
		end
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function EquipmentWGData:RegisterBaoShiRemindInBag(remind_name)
	local map = {}
	-- for _, v in pairs(self.stone_refine_stuff) do
	-- 	for _, v2 in pairs(v)do
	-- 		map[v2.stuff_id] = true
	-- 	end
	-- end

	for _, v in pairs(self.stone_level_up_cfg) do
		map[v.old_stone_item_id] = true
		map[v.new_stone_item_id] = true
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function EquipmentWGData:RegisterJinglianRemindInBag(remind_name)
	local stoneconfig_cfg = ConfigManager.Instance:GetAutoConfig("newstoneconfig_auto")
	local refine_cfg = stoneconfig_cfg.stone_refine2_consume or {}
	local map = {}
	for _, v in pairs(refine_cfg) do
		map[v.stuff_id] = true
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function EquipmentWGData:RegisterShengPinRemindInBag(remind_name)
	local map = {}
	local equip_cfg = EquipmentWGData.GetConfig()
	local tmp_cfg = equip_cfg.equip_upquality

	for k, equip_sp_cfg in pairs(tmp_cfg) do
		local stuff_list = Split(equip_sp_cfg.stuff_id, "|")
		for _, v in ipairs(stuff_list) do
			map[tonumber(v)] = true
		end

		map[equip_sp_cfg.succ_rate_stuff_id] = true
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function EquipmentWGData:RegisterStrengthRemindInBag(remind_name)
	local map = {}
	for k,v in pairs(self.equipment_cfg.new_strength) do
		map[v.stuff_id] = true
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end



-- 根据装备Index获取装备锻造格子列表数据
-- function EquipmentWGData:GetEquipForgeGridListById(equip_id)
-- 	return EquipWGData.Instance:GetGridData(equip_id)
-- end

-----------------------------------------------------
-- 读配置
function EquipmentWGData.GetConfig()
	local equipforge_auto = ConfigManager.Instance:GetAutoConfig("equipforge_auto")
	return equipforge_auto
end

---------------------------------------------------------------
-- 为了优化remind性能问题单独分开处理下
-- 获取人物身上的装备强化数据列表(带remind功能的)
function EquipmentWGData:GetRoleEquipStrengthRemind(need_jump, view_cur_data)
	local data_list = EquipWGData.Instance:GetDataList()
	local equip_data = {}
	local jump_equip_data = {}
	local view_jump_index = -1

	local equip_body_index = view_cur_data.index
	local equip_body_seq, _ = EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)

	for index = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		local data = data_list[index]
		if data ~= nil then
			-- data.can_strength = self:GetIsGoodsStrengthEquip(index)

			local target_equip_body_index = equip_body_seq * 15 + index
			data.can_strength = self:IsCanStrengthEquip(equip_body_seq, target_equip_body_index)
			data.sort_index = EquipmentWGData.GetSortIndex(index)
			local strengthen_level = self:GetStrengthLevelByIndex(index)
			data.strengthen_level = strengthen_level
			data.param.strengthen_level = strengthen_level

			if need_jump then
				table.insert(jump_equip_data, data)
			end
		end

		equip_data[index] = data
	end

	if not need_jump then
		return equip_data, view_jump_index
	end

	if not IsEmptyTable(jump_equip_data) then
		table.sort(jump_equip_data, SortTools.KeyLowerSorter("sort_index"))
	end

	if not IsEmptyTable(view_cur_data) then
		-- 当前可强化返回当前 不跳
		for i, v in ipairs(jump_equip_data) do
			if v.item_id == view_cur_data.item_id then
				view_jump_index = v.index
				if v.can_strength then
					return equip_data, view_jump_index
				end
			end
		end
	end
	-- 跳装备列表中能强化 等级最小
	local min_strong_level = nil
	for i, v in ipairs(jump_equip_data) do
			if view_jump_index == -1 then
				view_jump_index = v.index
			end
	
		 if (not min_strong_level or (min_strong_level > v.strengthen_level)) and v.can_strength then
		 	min_strong_level = v.strengthen_level
	 		view_jump_index = v.index
		 end
	end

	return equip_data, view_jump_index
end

-------------------------------------------------------------------------------------------------------



-------------------------------------------------------------------------------------------------------
---------------------------------------------------------------
-- 根据装备ID和装备强化等级来获取装备强化配置数据
function EquipmentWGData:GetEquipStrengthBaseCfgByIndexAndLevel(equip_index, strength_level)
	return self:GetNewStrengthCfgByLevel(equip_index, strength_level)
end

-- 根据装备ID来获取装备强化配置数据(最大等级)
-- function EquipmentWGData:GetEquipStrengthMaxLevel(equip_index)
-- 	local equip_data = self:GetEquipForgeGridListById(equip_index)
-- 	if nil == equip_data then
-- 		return 999999
-- 	end

-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(equip_data.item_id)
-- 	if nil == item_cfg then
-- 		return 999999
-- 	end

-- 	return self:GetMaxStrengthLevelByOrder(item_cfg.order, item_cfg.color, equip_index)
-- end

-- 获取是否有物品可以强化装备  equip_index 为装备肉身equip_index 
-- function EquipmentWGData:GetIsGoodsStrengthEquip(equip_index)
-- 	if not self:GetIsEquipStrengthMaxLevel(equip_index) then
-- 		local equip_cfg, next_equip_cfg = self:GetNewStrengthCfgByIndex(equip_index)
-- 		local is_have_stuff = true
-- 		if equip_cfg.stuff_num > 0 then --需要材料
-- 			local num = ItemWGData.Instance:GetItemNumInBagById(equip_cfg.stuff_id)
-- 			if num < equip_cfg.stuff_num then
-- 				is_have_stuff = false
-- 			end
-- 		end

-- 		local enough_coin = RoleWGData.Instance:GetIsEnoughUseCoin(equip_cfg.coin)
-- 		return is_have_stuff and enough_coin
-- 	end

-- 	return false
-- end

-- function EquipmentWGData:GetNewStrengthMaxLevelByPart(equip_index)
-- 	local cfg = self.new_strength[equip_index % 15]
-- 	if not IsEmptyTable(cfg) then
-- 		return #cfg
-- 	end

-- 	return 0
-- end

function EquipmentWGData:GetEquipStrengthUPBaoji()
	return self.is_baoji or 0
end

function EquipmentWGData:SetEquipStrengthUPBaoji()
	self.is_baoji = 0
end

-- function EquipmentWGData:GetMaxStrengthLevelByOrder(order, color, equip_index)
-- 	for k,v in pairs(self.strength_grade_level)do
-- 		if v.equip_order == order then
-- 			if (equip_index % 15) == GameEnum.EQUIP_INDEX_HUNJIE then
-- 				return v["tongxinsuo_limit"] or 0
-- 			else
-- 				return v["color_" .. color] or 0
-- 			end
-- 		end
-- 	end
-- 	return 0
-- end

function EquipmentWGData:GetQHNextCanUpLevelOrder(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then
		return 0
	end

	local order = item_cfg.order
	for k,v in ipairs(self.strength_grade_level) do
		if v.equip_order > order then
			return v.equip_order
		end
	end

	return  0
end

-- function EquipmentWGData:GetNewStrengthCfgByIndex(equip_index)
-- 	local now_level = self:GetStrengthLevelByIndex(equip_index)
-- 	return self:GetNewStrengthCfgByLevel(equip_index % 15, now_level), self:GetNewStrengthCfgByLevel(equip_index % 15, now_level + 1)
-- end

---------------------------------------------------------------------------
function EquipmentWGData:GetStoneConfigOther()
	local stoneconfig_cfg = ConfigManager.Instance:GetAutoConfig("stoneconfig_auto")
	return stoneconfig_cfg.other[1]
end

-- 获取小鬼配置
function EquipmentWGData.GetXiaoGuiCfg(item_id)
	if item_id == QIXIAN_XIAOGUI then
		local xiaogui_compose_show_cfg = EquipmentWGData.Instance:GetGuardComposeShowCfg()
		item_id = xiaogui_compose_show_cfg[1][1].stuff_id_1
	end

	return EquipmentWGData.Instance:GetGuardCfgByItemID(item_id)
end

function EquipmentWGData.GetXiaoGuiCfgType(imp_type)
	local cfg = EquipmentWGData.Instance.impguard_cfg.imp
	for k,v in pairs(cfg) do
		if imp_type == v.imp_type then
			return v
		end
	end
	return nil
end

function EquipmentWGData:GetGuardCfgByItemID(item_id)
	return self.imp_cfg[item_id]
end

--获取小鬼合成配置映射表.
function EquipmentWGData:GetGuardComposeShowCfg()
	return self.impguard_compose_show_cfg
end

--获取最低级的小鬼.
function EquipmentWGData:GetDefultGuardComposeCfg()
	local defult_remind_guard = {}
	for k, v in pairs(self.impguard_compose_show_cfg) do
		for k1, v1 in pairs(v) do
			if k1 == 1 then
				table.insert(defult_remind_guard, v1.stuff_id_1)
			end
		end
	end

	return defult_remind_guard
end

--获取小鬼合成seq列表.
function EquipmentWGData:GetGuardComposeSeqList()
	local other_cfg = EquipWGData.Instance:GetImpguardOtherConfig()
	local seq_list = {}

	local str_list = Split(other_cfg.producd_seq_list, "|")
	for k, v in pairs(str_list) do
		table.insert(seq_list, tonumber(v))
	end

	return seq_list
end

--守护第一档配置
function EquipmentWGData:GetGuardFirstCfg()
	local cfg = self.impguard_cfg.imp
	for k,v in pairs(cfg) do
		if k == 1 then
			return v
		end
	end
	return nil
end

-- 根据ID获取所需类型的配置
function EquipmentWGData:GetMatchCfgByIdAndType(impguard_type, item_id)
	local cfg = self.impguard_cfg.imp
	for k, v in pairs(cfg) do
		if v.item_id == item_id and v.impguard_type == impguard_type then
			return v
		end
	end
	return nil
end

-- 判断是否已穿戴已拥有的符合类型且品质最高的小鬼(要求的类型,已穿戴id)
function EquipmentWGData:IsDressUpSuitImpguard(impguard_type, item_id)
	local single_imp_cfg = self:GetMatchCfgByIdAndType(impguard_type, item_id)
	local target_list = self:GetHighestColorImp(impguard_type)

	if not IsEmptyTable(single_imp_cfg) then
		if IsEmptyTable(target_list) then 		-- 背包未装备impguard_type类型的小鬼
			return true, item_id
		else
			local highest_color_itemid = single_imp_cfg.color >= target_list.color and single_imp_cfg.item_id or target_list.item_id
			return single_imp_cfg.color >= target_list.color, highest_color_itemid
		end
	end
	return false, 0
end

-- 获取背包中符合类型品质最高的小鬼
function EquipmentWGData:GetHighestColorImp(impguard_type)
	local xiaogui_list = ItemWGData.Instance:GetXiaoGuiList(impguard_type)
	local target_list = {}
	local color_imp = 0
	if not IsEmptyTable(xiaogui_list) then
		for k, v in pairs(xiaogui_list) do
			if v.color > color_imp then
				color_imp = v.color
				target_list = v
			end
		end
	end
	return target_list
end

function EquipmentWGData:GetXiaoGuiAllCfg()
	local data_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local impguard_auto = self.impguard_cfg.imp
	local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()
	local jum_key = {}
	local jum_key_list = {[4] = 2,[3] = 1}
	local item_num_list = {0,0,0,0}
	local equip_index = 0

	for k = #impguard_auto, 1, -1 do
		local v = impguard_auto[k]
		if jum_key[k] == nil and v.level_limit <= role_level then
			table.insert(data_list, v)
			jum_key[k] = 1
			if jum_key_list[k] ~= nil then
				jum_key[jum_key_list[k]] = 1
			end
		end
		local item_time_data = ItemWGData.Instance:GetItemTimeLastOne(v.item_id)
		if ItemWGData.Instance:GetItemNumInBagById(v.item_id) > 0 and item_time_data.invalid_time > TimeWGCtrl.Instance:GetServerTime() then
			item_num_list[k] = 1
		end
		if imp_guard_info.item_wrapper.item_id == v.item_id then
			equip_index = k
		end
	end
	table.sort(data_list, SortTools.KeyUpperSorters("add_per_exp", "per_mianshang"))
	if item_num_list[4] <= 0 and equip_index ~= 4 and (item_num_list[2] > 0 or equip_index == 2) then
		data_list[2] = impguard_auto[2]
	end
	if item_num_list[3] <= 0 and equip_index ~= 3 and (item_num_list[1] > 0 or equip_index == 1) then
		data_list[1] = impguard_auto[1]
	end

	if imp_guard_info.item_wrapper.item_id and imp_guard_info.item_wrapper.item_id == 0 and item_num_list[2] <= 0 and item_num_list[4] <= 0 then
		table.remove(data_list, 2)
	end

	return data_list
end

function EquipmentWGData:GetXiaoGuiChooseIndexByEquip()
	local imp_guard_info = EquipWGData.Instance:GetmpGuardInfo()
	for k,v in pairs(self:GetXiaoGuiAllCfg()) do
		if imp_guard_info.item_wrapper.item_id == v.item_id then
			self.xiaogui_index = k
			break
		end
	end
end

function EquipmentWGData:GetXiaoGuiChooseIndex(click_count)
	if click_count then
		self.xiaogui_index = self.xiaogui_index + 1
		if self.xiaogui_index > #self:GetXiaoGuiAllCfg() then
			self.xiaogui_index = 1
		end
	end

	return self.xiaogui_index
end

function EquipmentWGData:ChangeXiaoGuiChooseIndex(index)
	self.xiaogui_index = index
end

function EquipmentWGData:GetEquipTotalStarCfg(equip_body_seq, star_level)
	local curr_cfg = nil
	local next_cfg = nil

	-- local star_cfg = EquipmentWGData.GetConfig().equip_total_star_attr
	local star_cfg = self.equip_total_star_attr_cfg[equip_body_seq] or {}

	for i = #star_cfg, 1, -1 do
		if star_cfg[i].total_star_level <= star_level then
			curr_cfg = star_cfg[i] and star_cfg[i]
			next_cfg = star_cfg[i + 1]
			break
		end
		if i == 1 then
			curr_cfg = nil
			next_cfg = star_cfg[i]
		end
	end

	return curr_cfg, next_cfg
end

function EquipmentWGData:GetEquipRandAttrCfg(item_color)
	local cfg = self.equip_rand_num_cfg[item_color]
	if cfg then
		return cfg
	end
	return 0
end

function EquipmentWGData:GetEquipDecomposeByID(equip_id, star_level)
	if self.equip_decompose_cfg[equip_id] and
		self.equip_decompose_cfg[equip_id][star_level] then
		return self.equip_decompose_cfg[equip_id][star_level], true
	end

	return nil, false
end

-- 升品
function EquipmentWGData:SetEquipShengPinInfo( protocol,equip_part )
	if not self.shengpin_info_list then
		self.shengpin_info_list = {}
	end
	if equip_part then
		self.shengpin_info_list[equip_part] = self.shengpin_info_list[equip_part] or {}
		self.shengpin_info_list[equip_part].equip_part = protocol.equip_part
		self.shengpin_info_list[equip_part].quality_level = protocol.quality_level
	else
		self.shengpin_info_list = protocol.info_list
	end
end

function EquipmentWGData:GetEquipShengPinInfo(equip_part)
	return self.shengpin_info_list and self.shengpin_info_list[equip_part]
end

function EquipmentWGData:GetEquipShengPinCfg(quality_level,part)
	if not self.shengpin_cfg then
		self.shengpin_cfg = ConfigManager.Instance:GetAutoConfig("equip_shengpin_cfg_auto").shengpin_levelup
	end
	for k,v in pairs(self.shengpin_cfg) do
		if v.quality_level == quality_level and v.part == part then
			return v
		end
	end
end

function EquipmentWGData:GetEquipShengPinSuitAttrCfg(quality_level,view_type)
	if not self.shengpin_attr_cfg then
		self.shengpin_attr_cfg = ConfigManager.Instance:GetAutoConfig("equip_shengpin_cfg_auto").shengpin_suit_attr
	end
	for k,v in pairs(self.shengpin_attr_cfg) do
		if v.quality_level == quality_level and v.suit_type == view_type then
			return v
		end
	end
end

function EquipmentWGData:GetEquipShengPinLimit(part)
	if not self.shengpin_limit_cfg then
		self.shengpin_limit_cfg = ConfigManager.Instance:GetAutoConfig("equip_shengpin_cfg_auto").shengpin_open_limit
	end
	for k,v in pairs(self.shengpin_limit_cfg) do
		if v.part == part then
			return v.order
		end
	end
end

function EquipmentWGData:GetEquipShengPinDataList()
	local data_list = EquipWGData.Instance:GetDataList()
	local data = {}
	for k,v in pairs(data_list) do
		local tab = v
		local can_uplevel = false
		local limit_order = EquipmentWGData.Instance:GetEquipShengPinLimit(v.index) --上限order
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		local tmp = self:GetEquipShengPinInfo(v.index)
		local quality_level = tmp and tmp.quality_level or 0
		if item_cfg and limit_order and item_cfg.order >= limit_order then
			local next_cfg = EquipmentWGData.Instance:GetEquipShengPinCfg(quality_level + 1, v.index)
			if self:GetEquipShengPinRemindNumByPart(quality_level, v.index) == 1 then
				can_uplevel = true
			end
			tab.sort_index = can_uplevel and EquipmentWGData.GetSortIndex(v.index) * 111 or EquipmentWGData.GetSortIndex(v.index) * 1222 --第二档
			if nil == next_cfg then
				tab.sort_index = EquipmentWGData.GetSortIndex(v.index) * 13333 --第三档
			end
		else
			--不可升品
			tab.sort_index = EquipmentWGData.GetSortIndex(v.index) * 144444 --第四档
		end
		data[#data + 1] = tab
	end

	table.sort(data, function(a, b)
		if nil == a or nil == b or a == b then
			return false
		end
		return a.sort_index < b.sort_index
	end )
	return data
end

function EquipmentWGData:GetEquipShengPinSuitLevel(view_type)
	local level_list = {}
	for k,v in pairs(self.shengpin_info_list) do
		if k < 6 and view_type == ShengPinAttrTip.ViewType.BaseEquipView then
			for i=1,v.quality_level do
				if level_list[i] then
					level_list[i] = level_list[i] + 1
				else
					level_list[i] = 1
				end
			end
		elseif k >= 6 and view_type == ShengPinAttrTip.ViewType.SpeEquipView then
			for i=1,v.quality_level do
				if level_list[i] then
					level_list[i] = level_list[i] + 1
				else
					level_list[i] = 1
				end
			end
		end
	end

	local count = 0
	local level = 0
	for k,v in pairs(level_list) do
		if v == 6 and view_type == ShengPinAttrTip.ViewType.BaseEquipView then
			count = v
			level = k
		elseif v == 4 and view_type == ShengPinAttrTip.ViewType.SpeEquipView then
		 	count = v
			level = k
		end
	end
	local nextcount = level_list[level + 1] or 0
	return level ,count,nextcount
end

function EquipmentWGData.GetEquipShengPinName(quality_level)
	return Language.Equip.shengPin_itemTip[quality_level] or ""
end

function EquipmentWGData:GetEquipShengPinRemindNumByPart(quality_level,part)
	if quality_level >= 8 then
		return 0
	end
	local data_list = EquipWGData.Instance:GetDataList()
	if not data_list[part] then   --没穿装备直接返回0
		return 0
	end
	if data_list and data_list[part] and data_list[part].item_id then
		local item_cfg = ItemWGData.Instance:GetItemConfig(data_list[part].item_id)
		if item_cfg.order < 8 then
			return 0
		end
	end

	local data = self:GetEquipShengPinCfg(quality_level + 1,part)
	if data then
		local num = ItemWGData.Instance:GetItemNumInBagById(data.stuff_id_1)
		if num >= data.stuff_count_1 then
			return 1
		end
	end
	return 0
end

------------------------------------------------------装备印记--
--装备印记红点
function EquipmentWGData:GetEquipImpressionRemind( )
	return 0
end

--获取装备列表
function EquipmentWGData:GetYinJiJiChengEquipList( )
	local data_list = EquipWGData.Instance:GetDataList()
	local return_tab = {}
	for k,v in pairs(data_list) do
		if k ~= GameEnum.EQUIP_INDEX_HUNJIE then
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_cfg.color > GameEnum.ITEM_COLOR_ORANGE then
				v.yinji_sort_index = EquipmentWGData.GetSortIndex(v.index) * 111
			else
				v.yinji_sort_index = EquipmentWGData.GetSortIndex(v.index) * 1222
			end
			table.insert(return_tab, v)
		end
	end
	table.sort(return_tab, function ( a, b )
		return a.yinji_sort_index < b.yinji_sort_index
	end)
	return return_tab
end

function EquipmentWGData:GetimpressionCfg( )
	if nil == self.impression_cfg then
		local tmp_cfg = EquipmentWGData.GetConfig()
		self.impression_cfg = ListToMapList(tmp_cfg.impression_attr, "part")
	end
	return self.impression_cfg
end

function EquipmentWGData:GetXianPingAttrDesc()
	return self.xianpin_suiji_attr
end

function EquipmentWGData:GetXianPingAttrDescByType(type)
	local cfg = self:GetXianPingAttrDesc()
	if cfg and cfg[type] then
		return cfg[type]
	end
	return nil
end

--获取装备印记
function EquipmentWGData:GetEquipImpressionCfg( item_data, impression )
	impression = impression or item_data.param and item_data.param.impression
	if nil == impression then
		return
	end
	--special_item_id是特殊加的所以要优先处理
	local item_id = item_data.special_item_id or item_data.item_id
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	local tmp_cfg = EquipmentWGData.GetConfig()
	local impression_cfg = self:GetimpressionCfg()
	local attr_cfg = nil
	if nil == impression_cfg[equip_part] then
		return
	end
	for k,v in pairs(impression_cfg[equip_part]) do
		if v.impression == impression and v.color == item_cfg.color and v.order == item_cfg.order then
			attr_cfg = v
			break
		end
	end
	if nil == attr_cfg then
		return
	end
	local xianpin_auto = self:GetXianPingAttrDesc()
	local attr_tab = {}
	for i = 0, 9 do
		if xianpin_auto and attr_cfg["attr_" .. i] and attr_cfg["attr_value_" .. i] > 0 and xianpin_auto[attr_cfg["attr_" .. i]] then
			local tmp = xianpin_auto[attr_cfg["attr_" .. i]]
			table.insert(attr_tab, {desc = tmp.desc, value = attr_cfg["attr_value_" .. i], attr_type = tmp.attr_type, is_per = tmp.is_per})
		else
			break
		end
	end
	return attr_tab
end

--获取同部位装备
function EquipmentWGData:GetBagYinJiEquipList( data )
	local tab = {}

	if not data then
		return tab
	end

	local bag_item_data = ItemWGData.Instance:GetBagItemDataList()
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	local equip_part = EquipWGData.Instance:GetEquipIndexByType(item_cfg.sub_type)
	for k,v in pairs(bag_item_data) do
			local item_config, item_type = ItemWGData.Instance:GetItemConfig(v.item_id)
			--修改筛选条件
			if nil ~= item_config then
				local equip_index = EquipWGData.Instance:GetEquipIndexByType(item_config.sub_type)
				if item_type == GameEnum.ITEM_BIGTYPE_EQUIPMENT
					and equip_index == equip_part
					and v.param and v.param.impression > 0
					and (item_cfg.limit_prof == item_config.limit_prof and item_cfg.limit_sex == item_config.limit_sex
						or item_config.limit_prof == GameEnum.ROLE_PROF_NOLIMIT and item_cfg.limit_sex == item_config.limit_sex)
					and item_config.color ~= GameEnum.EQUIP_COLOR_ORANGE
				then
					table.insert(tab, v)
				end
			end
	end
	table.sort(tab, function (a, b)
		return a.param.impression > b.param.impression
	end)
	return tab
end

-------------------------------------------
--根据宝石ID获取宝石属性
function EquipmentWGData:GetBaoShiNatrue(item_id)
	local stone_cfg = self:GetBaoShiCfgByItemId(item_id)
	if nil == stone_cfg then
		return "", ""
	end

	local attr_num = GameEnum.EQUIP_BAOSHI_ATTR_NUM
	local name_str = ItemWGData.Instance:GetItemName(item_id, nil, true)
	local attr_str = ""
	for i = 1, attr_num do
		local type = stone_cfg["attr_type" .. i]
		local value = stone_cfg["attr_val" .. i]
		if value and value > 0 then
			local name = self:GetAttrNameByAttrId(type, true)
			name = DeleteStrSpace(name)
			local is_per = self:GetAttrIsPer(type)
			value = is_per and (value * 0.01 .. "%") or value
			local attr_desc = name .. " " .. ToColorStr(value, COLOR3B.GREEN)
			local huan_hang = 1 < i and "\n" or ""
			attr_str = attr_str .. huan_hang .. attr_desc
		end
	end

	return name_str, attr_str
end

-- function EquipmentWGData:GetEquipLevelRemind()
-- 	local shenzhu_total_level = self:GetTotalStrengthLevel()
-- 	local active_level = self:GetEquipStrengthLV()
-- 	local total_cfg, next_total_cfg = self:GetEquipTotalStrengthCfg(active_level)
-- 	if next_total_cfg and next_total_cfg.total_level <= shenzhu_total_level and active_level <= next_total_cfg.total_level then
-- 		local open_func = function ()
-- 			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_strength)
-- 			RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STREHGTH_TIP)
-- 			RoleWGCtrl.Instance:OpenEquipAttr()
-- 		end
-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STRENGTH_ATTR_ACTIVE , 1,open_func)
-- 		return 1
-- 	end

-- 	local shenzhu_total_level2 = self:GetTotalXianQiStrengthLevel()
-- 	local active_level2 =  self:GetXianQiStrengthLV()
-- 	local total_cfg2, next_total_cfg2 = self:GetEquipXianQiTotalStrengthCfg(active_level2)
-- 	if next_total_cfg2 and next_total_cfg2.total_level <= shenzhu_total_level2 and active_level2 <= next_total_cfg2.total_level then
-- 		local open_func = function ()
-- 			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_strength)
-- 			RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.STREHGTH_TIP)
-- 			RoleWGCtrl.Instance:OpenEquipAttr()
-- 		end
-- 		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STRENGTH_ATTR_ACTIVE , 1,open_func)
-- 		return 1
-- 	end
-- 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.STRENGTH_ATTR_ACTIVE , 0)
-- 	return 0
-- end

--------------------------------------------------------------------仙器升阶、升星------------------------------------------------------------------

function EquipmentWGData:GetXQComposeDataListByType(index)
	local data_list
	if index == TabIndex.other_compose_shengjie then
		data_list = self:XQComposeUpgardeDataList()
	elseif index == TabIndex.other_compose_shengxing then
		data_list = self:XQComposeUpStarDataList()
	end
	return data_list
end

--获取升阶的基础属性.
function EquipmentWGData:GetXQComposeBaseAttr(item_data, is_preview)
	local data_list = self:GetXQComposeUGBaseAttrList(item_data, is_preview)
	return data_list or {}
end

--获取升阶的仙品属性.
function EquipmentWGData:GetXQComposeLegendAttr(item_data, is_preview)
	local data_list = self:GetXQComposeUGShowAttrDescList(item_data, is_preview)
	return data_list or {}
end

--获取升星的仙品属性.
function EquipmentWGData:GetXQComposeUSLegendAttr(item_data, is_preview)
	local data_list = self:GetXQComposeUSShowAttrDescList(item_data, is_preview)
	return data_list or {}
end

function EquipmentWGData:GetXQComposeShowData(equip_data, show_cfg, is_max)
	local temp_data = {}
	local show_data = {}

	if IsEmptyTable(equip_data) then
		return {}
	end

	temp_data.sort_index = equip_data.item_id > 0 and equip_data.index or (equip_data.index + 999)

	show_cfg = show_cfg or {}
	temp_data.equip_data = equip_data
	temp_data.target_equip_data = {}
	local target_preview_id = show_cfg.target_preview_id or 0
	local target_preview_star = show_cfg.target_preview_star or 0
	local target_equip_id = show_cfg.target_equip_id or 0
	local target_equip_star = show_cfg.target_equip_star or 0
	target_equip_id = (target_preview_id > 0 and equip_data.item_id == target_equip_id) and target_preview_id or target_equip_id
	target_equip_star = (target_preview_id > 0 and equip_data.item_id == target_equip_id) and target_preview_star or target_equip_star

	if (not is_max) or (target_preview_id > 0) then
		temp_data.target_equip_data = {item_id = target_equip_id or 0,
									index = equip_data.index,
									param = {star_level = target_equip_star or 0}}
	end

	show_data.stuff_id1 = show_cfg.stuff_id or 0
	show_data.stuff_num1 = show_cfg.stuff_num or 0
	show_data.stuff_id2 = show_cfg.stuff_id2 or 0
	show_data.stuff_num2 = show_cfg.stuff_num2 or 0
	show_data.stuff_id3 = show_cfg.stuff_id3 or 0
	show_data.stuff_num3 = show_cfg.stuff_num3 or 0
	show_data.origin_equip_id = show_cfg.origin_equip_id or equip_data.item_id or 0
	show_data.target_equip_id = target_equip_id or equip_data.item_id or 0

	temp_data.show_data = show_data
	temp_data.is_max = is_max

	show_data.have_stuff1 = ItemWGData.Instance:GetItemNumInBagById(show_data.stuff_id1)
	show_data.have_stuff2 = ItemWGData.Instance:GetItemNumInBagById(show_data.stuff_id2)
	show_data.have_stuff3 = ItemWGData.Instance:GetItemNumInBagById(show_data.stuff_id3)

	if show_data.stuff_id1 == 65531 then
		show_data.have_stuff1 = RoleWGData.Instance:GetRoleInfo().silver_ticket
	elseif show_data.stuff_id2 == 65531 then
		show_data.have_stuff2 = RoleWGData.Instance:GetRoleInfo().silver_ticket
	elseif show_data.stuff_id3 == 65531 then
		show_data.have_stuff3 = RoleWGData.Instance:GetRoleInfo().silver_ticket
	end

	temp_data.show_remind = show_data.have_stuff1 >= show_data.stuff_num1 and
							show_data.have_stuff2 >= show_data.stuff_num2 and
							show_data.have_stuff3 >= show_data.stuff_num3 and
							equip_data.item_id > 0 and not is_max
	return temp_data
end

function EquipmentWGData:GetXQComposeUpgradeCfgByOrigin(item_id, star_level)
	return (self.xq_compose_upgrade_origin[item_id] or {})[star_level]
end

function EquipmentWGData:GetXQComposeUpgradeCfgByTarget(item_id, star_level)
	return (self.xq_compose_upgrade_target[item_id] or {})[star_level]
end

function EquipmentWGData:XQComposeUpgardeDataList()
	local data_list = {}
	local grid_data_list = EquipWGData.Instance:GetDataList()

	for k,v in pairs(grid_data_list) do
		local star_level = v.param and v.param.star_level or 0
		local temp_data = {}
		local equip_index = v.index
		if (equip_index == GameEnum.EQUIP_INDEX_XIANJIE or equip_index == GameEnum.EQUIP_INDEX_XIANZHUO) then	-- 部位限制
			-- 穿戴升阶原始装备
			local origin_data = self:GetXQComposeUpgradeCfgByOrigin(v.item_id, star_level)
			-- 穿戴升阶目标装备
			local target_data = self:GetXQComposeUpgradeCfgByTarget(v.item_id, star_level)
			if origin_data then
				temp_data = self:GetXQComposeShowData(v, origin_data, false)
			elseif target_data then
				temp_data = self:GetXQComposeShowData(v, target_data, true)
			-- 穿戴非符合的装备
			else
				local equip_data = {item_id = 0, index = equip_index}
				temp_data = self:GetXQComposeShowData(equip_data, nil, false)
				--写死 策划需求
				temp_data.limit_desc = Language.Compose.ComposeXianQiLimit[1]
			end
		end

		if not IsEmptyTable(temp_data) then
			data_list[#data_list + 1] = temp_data
		end
	end

	local show_list = {
		-- [GameEnum.EQUIP_INDEX_XIANLIAN] = false,
		[GameEnum.EQUIP_INDEX_XIANZHUO] = false,
		[GameEnum.EQUIP_INDEX_XIANJIE] = false,
		-- [GameEnum.EQUIP_INDEX_XIANZHUI] = false,
	}

	for k, v in ipairs(data_list) do
		if v.equip_data then
			local equip_index = v.equip_data.index
			if show_list[equip_index] ~= nil then
				show_list[equip_index] = true
			end
		end
	end

	for k, v in pairs(show_list) do
		if not v then
			local equip_data = {item_id = 0, index = k}
			data_list[#data_list + 1] = self:GetXQComposeShowData(equip_data, nil, false)
		end
	end

	if not IsEmptyTable(data_list) then
		table.sort(data_list, SortTools.KeyLowerSorter("sort_index"))
	end

	return data_list
end

-- is_preview 预览属性
function EquipmentWGData:GetXQComposeUGBaseAttrList(item_data, is_preview)
	local show_attr_list = {}

	if IsEmptyTable(item_data) then
		return show_attr_list
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
	local base_attri_butte = AttributeMgr.GetAttributteByClass(item_cfg)
	for k, v in pairs(base_attri_butte) do
		if type(v) == "number" and v > 0 then
			-- 同步item_tip 那边逻辑
			local value_str = ""
			if k == "ming_zhong" or k == "shan_bi" then
				value_str = v / 100 .. "%"
			elseif k == "per_pofang" or k == "per_mianshang" or k == "per_baoji" then
				value_str = v * 100 .. "%"
			else
				value_str = math.floor(v)
			end
			-- 属性名需要加空格 GetAttrNameByAttrStr(name, true)
			local attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(k, true)
			table.insert(show_attr_list, {name = attr_name, value = value_str, is_preview = is_preview, possible = 0})
		end
	end

	return show_attr_list
end

-- is_preview 预览属性
function EquipmentWGData:GetXQComposeUGShowAttrDescList(item_data, is_preview)
	local show_attr_list = {}

	if IsEmptyTable(item_data) then
		return show_attr_list
	end

	-- 仙品属性
	local star_level = item_data.param and item_data.param.star_level or 0
	local legend_attr_list = {}
	if is_preview then
		legend_attr_list = EquipWGData.GetPreviewLegendAttr(item_data, star_level)
	else
		legend_attr_list = EquipWGData.GetLegendAttr(item_data, false)
	end

	if not IsEmptyTable(legend_attr_list) then
		for k, v in ipairs(legend_attr_list) do
			local value = v.is_per == 1 and v.value * 0.01 or v.value
			value = math.ceil(value)
			local possible = 0
			if is_preview then
				possible = v.must_get and 1 or 2
			end

			table.insert(show_attr_list, {name = v.desc, value = value, is_preview = is_preview, possible = possible})
		end
	end

	return show_attr_list
end

--根据类型获取属性
function EquipmentWGData:GetEquipRandAttrByType(color, equip_type, attr_type)
	local cfg_map = self.equip_rand_attr_cfg_map
	if cfg_map and cfg_map[color] and cfg_map[color][equip_type] then
		return cfg_map[color][equip_type][attr_type]
	end
	return nil
end

--根据类型 获取当前随机属性列表
function EquipmentWGData:GetEquipRandAttrListByType(color, equip_type)
	local cfg_map = self.equip_rand_attr_cfg_map
	if cfg_map and cfg_map[color] and cfg_map[color][equip_type] then
		return cfg_map[color][equip_type]
	end
	return nil
end

function EquipmentWGData:GetXQComposeUpStarCfgByOrigin(item_id, star_level)
	return (self.xq_compose_upstar_origin[item_id] or {})[star_level]
end

function EquipmentWGData:GetXQComposeUpStarCfgByTarget(item_id, star_level)
	return (self.xq_compose_upstar_target[item_id] or {})[star_level]
end

--仙器升星
function EquipmentWGData:XQComposeUpStarDataList()
	local data_list = {}
	local grid_data_list = EquipWGData.Instance:GetDataList()

	for k,v in pairs(grid_data_list) do
		local star_level = v.param and v.param.star_level or 0
		local temp_data = {}
		local equip_index = v.index
		if (equip_index == GameEnum.EQUIP_INDEX_XIANJIE or equip_index == GameEnum.EQUIP_INDEX_XIANZHUO) then	-- 部位限制
			-- 穿戴升阶原始装备
			local origin_data = self:GetXQComposeUpStarCfgByOrigin(v.item_id, star_level)
			-- 穿戴升阶目标装备
			local target_data = self:GetXQComposeUpStarCfgByTarget(v.item_id, star_level)

			if origin_data then
				temp_data = self:GetXQComposeShowData(v, origin_data, false)
			elseif target_data then
				temp_data = self:GetXQComposeShowData(v, target_data, true)

				-- 升星会断层  此处是预览提示
				local target_preview_data = self.xq_compose_upstar_target[target_data.target_preview_id]
										and self.xq_compose_upstar_target[target_data.target_preview_id][target_data.target_preview_star]

				if target_preview_data then
					local need_item_cfg = ItemWGData.Instance:GetItemConfig(target_preview_data.origin_equip_id)
					if need_item_cfg then
						local preview_index = EquipWGData.Instance:GetEquipIndexByType(need_item_cfg.sub_type)
						local order_str = string.format(Language.Tip.Order, need_item_cfg.order)
						local color_str = Language.Common.ColorName[need_item_cfg.color]
						local star_str = string.format("%s%s", target_preview_data.origin_equip_star, Language.Common.Star)
						local part_str = Language.Stone[preview_index]
						temp_data.limit_desc = string.format(Language.Compose.ComposeXQUpStarLimitStr, order_str, color_str, star_str, part_str)
					end
				end
			-- 穿戴非符合的装备
			else
				local equip_data = {item_id = 0, index = equip_index}
				temp_data = self:GetXQComposeShowData(equip_data, nil, false)
				--写死 策划需求
				temp_data.limit_desc = Language.Compose.ComposeXianQiLimit[2]
			end
		end

		if not IsEmptyTable(temp_data) then
			data_list[#data_list + 1] = temp_data
		end
	end

	local show_list = {
		-- [GameEnum.EQUIP_INDEX_XIANLIAN] = false,
		[GameEnum.EQUIP_INDEX_XIANZHUO] = false,
		[GameEnum.EQUIP_INDEX_XIANJIE] = false,
		-- [GameEnum.EQUIP_INDEX_XIANZHUI] = false,
	}

	for k, v in ipairs(data_list) do
		if v.equip_data then
			local equip_index = v.equip_data.index
			if show_list[equip_index] ~= nil then
				show_list[equip_index] = true
			end
		end
	end

	for k, v in pairs(show_list) do
		if not v then
			local equip_data = {item_id = 0, index = k}
			data_list[#data_list + 1] = self:GetXQComposeShowData(equip_data, nil, false)
		end
	end

	if not IsEmptyTable(data_list) then
		table.sort(data_list, SortTools.KeyLowerSorter("sort_index"))
	end

	return data_list
end

function EquipmentWGData:GetEquipSpecialAttr(item_id)
	local function SetAttrData(data, cfg, type)
		if data and cfg then
			for i = 1, 10 do
				local attr_id = cfg["special_type" .. i]
				local attr_value = cfg["special_val" .. i]
				if attr_id and attr_value and attr_value > 0 then
					local temp = {}
					temp.type = type
					temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_id, true)
					local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_id)
					temp.attr_value = is_per and (attr_value * 0.01 .. "%") or attr_value
					data[#data + 1] = temp
				end
			end
		end

		return data
	end

	local special_attr_list = {}
	local special_item_cfg_ping = self:GetEquipSpecialAttrCfgByIdAndType(item_id, EQUIP_SPECIAL_ATTR_TYPE.PINK)
	local special_item_cfg_gold = self:GetEquipSpecialAttrCfgByIdAndType(item_id, EQUIP_SPECIAL_ATTR_TYPE.GOLD)
	local special_item_cfg_color = self:GetEquipSpecialAttrCfgByIdAndType(item_id, EQUIP_SPECIAL_ATTR_TYPE.COLOR)
	SetAttrData(special_attr_list, special_item_cfg_ping, EQUIP_SPECIAL_ATTR_TYPE.PINK)
	SetAttrData(special_attr_list, special_item_cfg_gold, EQUIP_SPECIAL_ATTR_TYPE.GOLD)
	SetAttrData(special_attr_list, special_item_cfg_color, EQUIP_SPECIAL_ATTR_TYPE.COLOR)

	return special_attr_list
end

function EquipmentWGData:GetXQComposeUSShowAttrDescList(item_data, is_preview)
	local show_attr_list = {}

	if IsEmptyTable(item_data) then
		return show_attr_list
	end

	-- 仙品属性
	local star_level = item_data.param and item_data.param.star_level or 0
	local legend_attr_list = {}
	if is_preview then
		legend_attr_list = EquipWGData.GetPreviewLegendAttr(item_data, star_level)
	else
		legend_attr_list = EquipWGData.GetLegendAttr(item_data, false)
	end

	if not IsEmptyTable(legend_attr_list) then
		for k, v in ipairs(legend_attr_list) do
			local value = v.is_per == 1 and v.value * 0.01 or v.value
			value = math.ceil(value)
			local possible = 0
			if is_preview then
				possible = v.must_get and 1 or 2
			end

			table.insert(show_attr_list, {name = v.desc, value = value, is_preview = is_preview, possible = possible})
		end
	end

	--特殊属性.
	local special_attr_list = self:GetEquipSpecialAttr(item_data.item_id)
	if not IsEmptyTable(special_attr_list) then
		for k, v in pairs(special_attr_list) do
			table.insert(show_attr_list, {type = v.type, name = v.attr_name, value = v.attr_value, is_preview = is_preview, possible = 0})
		end
	end

	return show_attr_list
end

--检查装备物品是否存在特殊属性.
function EquipmentWGData:CheackHasEquipSpecialAttr(item_id)
	local special_attr_list = self:GetEquipSpecialAttr(item_id)
	return not IsEmptyTable(special_attr_list)
end

function EquipmentWGData:GetWearEquipCanXQCompose(equip_body_index)
	local wear_data = EquipWGData.Instance:GetGridData(equip_body_index)
	if wear_data == nil or wear_data.item_id <= 0 then
		return false, nil
	end

	local item_id = wear_data.item_id
	local star_level = wear_data.param and wear_data.param.star_level or 0
	local upgrade_cfg = self:GetXQComposeUpgradeCfgByOrigin(item_id, star_level)
	if upgrade_cfg ~= nil then
		return true, "other_compose_shengjie"
	end

	local upstar_cfg = self:GetXQComposeUpStarCfgByOrigin(item_id, star_level)
	if upgrade_cfg ~= nil then
		return true, "other_compose_shengxing"
	end

	return false, nil
end


--排序因子
local equip_index_sort_list = {
	[GameEnum.EQUIP_INDEX_WUQI] = 1,
	[GameEnum.EQUIP_INDEX_XIANFU] = 2,
	[GameEnum.EQUIP_INDEX_TOUKUI] = 3,
	[GameEnum.EQUIP_INDEX_YIFU] = 4,
	[GameEnum.EQUIP_INDEX_KUZI] = 5,
	[GameEnum.EQUIP_INDEX_XIEZI] = 6,
	[GameEnum.EQUIP_INDEX_XIANLIAN] = 7,
	[GameEnum.EQUIP_INDEX_XIANZHUI] = 8,
	[GameEnum.EQUIP_INDEX_XIANJIE] = 9,
	[GameEnum.EQUIP_INDEX_XIANZHUO] = 10,
}
function EquipmentWGData.GetSortIndex(equip_part)
	return equip_index_sort_list[equip_part] or 1
end

local equip_sort_index_list = {}
function EquipmentWGData.GetIndexSort(sort)
	if IsEmptyTable(equip_sort_index_list) then
		for k,v in pairs(equip_index_sort_list) do
			equip_sort_index_list[v] = k
		end
	end

	return equip_sort_index_list[sort]
end

----------------------------------------------------------------------------新锻造----------------------------------------------------------------------
-- self.new_equip_sp_cfg
-- self.new_equip_sp_attr_cfg
-- self.new_equip_sp_suit_cfg
-- self.new_equip_sp_xianqi_suit_cfg

function EquipmentWGData:SetEquipShengPinStarLevelInfo(protocol)
	self.equip_sp_star_info = protocol.star_info_list
end

function EquipmentWGData:GetNewEquipShengPinCfg(quality_grade_star, equip_part)
	return self.new_equip_sp_cfg[equip_part] and self.new_equip_sp_cfg[equip_part][quality_grade_star]
end

function EquipmentWGData:GetNewEquipShengPinDataList(need_jump, view_index)
	local data_list = EquipWGData.Instance:GetDataList()
	local data = {}
	local equip_data = {}
	local jump_equip_data = {}

	for index = 0, GameEnum.EQUIP_INDEX_XIANZHUO do
		local index_data = data_list[index]
		if index_data ~= nil then
			index_data.sort_index = EquipmentWGData.GetSortIndex(index)
			if need_jump then
				table.insert(jump_equip_data, index_data)
			end
		end
		equip_data[index] = index_data
	end
	for k,v in pairs(equip_data) do
		if v.index <= GameEnum.EQUIP_INDEX_XIANZHUO then
			local tab = v
			local can_uplevel = false
			local star_count = self:GetNewEquipShengPinStarInfoByPart(v.index) or 0
			local cfg = self:GetNewEquipShengPinCfg(star_count, v.index)
			if cfg and not IsEmptyTable(self.equip_sp_star_info) then
				local limit_order = cfg.order_limit
				local limit_color = cfg.color_limit
				local limit_star = cfg.star_limit
				local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
				tab.is_remind = self:GetEquipNewShengPinRemindNumByPart(v.index)
				tab.is_meet_condition = item_cfg.order >= cfg.order_limit and item_cfg.color >= cfg.color_limit and v.param.star_level >= cfg.star_limit
				tab.limit_desc = ""
				tab.ShengPin_limit_desc = ""
				local can_uplevel = false
				if item_cfg.order < cfg.order_limit or item_cfg.color < cfg.color_limit or v.param.star_level < cfg.star_limit then
					if cfg.star_limit > 0 then
						tab.limit_desc = string.format(Language.Equip.ShengPinLimitText, cfg.order_limit, Language.Common.ColorName4[cfg.color_limit], cfg.star_limit)
					else
						tab.limit_desc = Language.Equip.NoShengPin
						tab.ShengPin_limit_desc = string.format(Language.Equip.ShengPinLimitText10, cfg.order_limit)
					end
					--tab.sort_index = EquipmentWGData.GetSortIndex(v.index) * 144444 --第四档

				elseif self.equip_sp_star_info[v.index + 1] == 0 then
					can_uplevel = true
					tab.limit_desc = Language.Equip.NoShengPin
					--tab.sort_index = (can_uplevel and is_remind == 1) and EquipmentWGData.GetSortIndex(v.index) * 111 or EquipmentWGData.GetSortIndex(v.index) * 1222 --第二档

				elseif self.equip_sp_star_info[v.index + 1] > 0 and self.equip_sp_star_info[v.index + 1] < 99 then
					can_uplevel = true
					tab.limit_desc = string.format(Language.Equip.ShengPinLimitText2,EQUIP_SHENGPIN_COLOR[cfg.star_color], Language.Equip.ShengPin[cfg.quality_grade  +  1],
										self.equip_sp_star_info[v.index + 1] % 10)
					--tab.sort_index = (can_uplevel and is_remind == 1) and EquipmentWGData.GetSortIndex(v.index) * 111 or EquipmentWGData.GetSortIndex(v.index) * 1222 --第二档

				elseif self.equip_sp_star_info[v.index + 1] >= 99 then
					tab.limit_desc = string.format(Language.Equip.ShengPinLimitText3, EQUIP_SHENGPIN_COLOR[cfg.star_color],Language.Equip.ShengPin[cfg.quality_grade  +  1],
					 					self.equip_sp_star_info[v.index + 1] % 10)
					--tab.sort_index = EquipmentWGData.GetSortIndex(v.index) * 13333 --第三档
				end
				data[v.index] = tab
			end
		end
	end

	-- table.sort(data, function(a, b)
	-- 	if nil == a or nil == b or a == b then
	-- 		return false
	-- 	end
	-- 	return a.sort_index < b.sort_index
	-- end )
	if not need_jump then
		return data, view_index
	end

	if data[view_index] then
		local remind = self:GetEquipNewShengPinRemindNumByPart(view_index)
		if remind then
			return data, view_index
		end
	end

	if not IsEmptyTable(jump_equip_data) then
		table.sort(jump_equip_data, SortTools.KeyLowerSorter("sort_index"))
	end

	for k, v in ipairs(jump_equip_data) do
		local remind = self:GetEquipNewShengPinRemindNumByPart(v.index)
		if remind == 1 then
			return data, v.index
		end
		if view_index == nil then
			view_index = v.index
		end
	end

	return data, view_index
end

-- GameEnum.EQUIP_BIG_TYPE_XIANQI 仙器 GameEnum.EQUIP_BIG_TYPE_NORMAL 普通装备
function EquipmentWGData:GetNewEquipShengPinDataByTy(equip_type)
	if not equip_type then return end
	local quality_list = {}
	local index = 0
	local qual_num
	local data_list = __TableCopy(EquipWGData.Instance:GetDataList()) --获取角色身上的装备
	for k,v in pairs(data_list) do
		if EquipmentWGData.GetEquipSuitTypeByPartType(v.index) == equip_type then
			index = index + 1
			qual_num = self.equip_sp_star_info[v.index + 1] or 0
			quality_list[index] = math.floor((qual_num) / 10)
		end
	end
	table.sort( quality_list, function (a,b)
		return a < b
	end )

	return quality_list
end

function EquipmentWGData:GetNewShengPinLowestCount(equip_type, quality)
	local quality_list = self:GetNewEquipShengPinDataByTy(equip_type)
	if not quality_list or IsEmptyTable(quality_list) then
		return 0
	end

	local count = 0
	for k,v in pairs(quality_list) do
		if v >= quality then
			count = count + 1
		end
	end
	return count
end

function EquipmentWGData:GetShengPinAddSliderStrAndValue(equip_type)
	equip_type = equip_type or 0
	local cur_level = 0
	local max_num = 0
	local next_quality_count = 0
	local need_str, nomax_str, max_str = "", "", ""
	local next_cfg
	if equip_type == GameEnum.EQUIP_BIG_TYPE_NORMAL then
		cur_level = self:GetEquipUpQualityLV()
		max_num = GameEnum.EQUIP_BIG_TYPE_NORMAL_COUNT
		next_cfg = self:GetNewNormalEquipShengPinSuitCfg(cur_level + 1)
		nomax_str = Language.Equip.CurSPLevelText_1
		max_str = Language.Equip.CurSPLevelText_3
	else
		cur_level = self:GetXianQiUpQualityLV()
		max_num = GameEnum.XIANQI_SHENPIN_ADD_NEED_COUNT
		next_cfg = self:GetNewXianQiEquipShengPinSuitCfg(cur_level + 1)
		nomax_str = Language.Equip.CurSPLevelText_2
		max_str = Language.Equip.CurSPLevelText_4
	end

	if next_cfg then
		next_quality_count = self:GetNewShengPinLowestCount(equip_type, next_cfg.quality)
		local quality_str = Language.Equip.ShengPin[next_cfg.quality + 1]
		if next_quality_count >= max_num then
			need_str = string.format(max_str, quality_str) .. Language.Equip.JiaChengCanAct
		else
			need_str = string.format(nomax_str, quality_str,"#ff9292", next_quality_count, max_num)
		end
	else
		next_quality_count = max_num
		local quality_str = Language.Equip.ShengPin[cur_level]
		need_str = string.format(max_str, quality_str) .. Language.Equip.JiaChengIsAct
	end

	return need_str, next_quality_count / max_num
end

function EquipmentWGData:GetNewNormalEquipShengPinSuitCfg(quality)
	local cfg = self.new_equip_sp_suit_cfg

	for k,v in pairs(cfg) do
		if v.quality == quality then
			return v
		end
	end
end

function EquipmentWGData:GetNewXianQiEquipShengPinSuitCfg(quality)
	local cfg = self.new_equip_sp_xianqi_suit_cfg

	for k,v in pairs(cfg) do
		if v.quality == quality then
			return v
		end
	end
end

function EquipmentWGData:GetEquipNewShengPinRemindNumByPart(part)
	--满星
	local star_count = self:GetNewEquipShengPinStarInfoByPart(part) or 0
	if star_count and star_count >= 99 then
		return 0
	end
	local data_list = EquipWGData.Instance:GetDataList()
	if not data_list[part] then   --没穿装备直接返回0
		return 0
	end
	local cfg = self:GetNewEquipShengPinCfg(star_count,part)
	if data_list and data_list[part] and data_list[part].item_id and cfg then
		local item_cfg = ItemWGData.Instance:GetItemConfig(data_list[part].item_id)
		if not item_cfg then
			return 0
		end

		if item_cfg.order < cfg.order_limit or item_cfg.color < cfg.color_limit or data_list[part].param.star_level < cfg.star_limit then
			return 0
		end
	end

	if cfg then
		local stuff_list = Split(cfg.stuff_id,"|")
		local stuff_count_list = Split(cfg.stuff_count,"|")
		for i,v in ipairs(stuff_list) do
			local num = ItemWGData.Instance:GetItemNumInBagById(tonumber(stuff_list[i]))
			if num < tonumber(stuff_count_list[i]) then
				return 0
			end
		end
		if self.is_select_sp_special_item then
			local special_stuff_num = ItemWGData.Instance:GetItemNumInBagById(cfg.succ_rate_stuff_id)
			if special_stuff_num < cfg.succ_rate_stuff_count then
				return 0
			end
		end
		return 1
	end
	return 0
end

function EquipmentWGData:GetNewEquipShengPinStarInfoByPart(part)
	return self.equip_sp_star_info[part + 1] or 0
end

local shengpin_quality_grade_list = {
	"base_attr_per",
	"zhuagnbei_sm_jc_per",
	"zhuagnbei_fy_jc_per",
	"zhuagnbei_gj_jc_per",
	"zhuagnbei_pj_jc_per",
}
function EquipmentWGData:GetNewShengPinAttrCfgByQuality(equip_part, quality)
	local attr_per = 0
	local attr_type = ""
	local temp_quality = quality * 10
	local cfg = self.new_equip_sp_attr_cfg[equip_part] and self.new_equip_sp_attr_cfg[equip_part][temp_quality]
	if IsEmptyTable(cfg) then
		return nil, attr_per, attr_type
	end

	for k, v in pairs(cfg) do
		for temp_k, temp_v in ipairs(shengpin_quality_grade_list) do
			if k == temp_v and v > 0 then
				attr_per = v
				attr_type = temp_v
			end
		end
	end

	return cfg, attr_per, attr_type
end

function EquipmentWGData:SetIsSelectSpecialItem(is_select)
	self.is_select_sp_special_item = is_select
end

function EquipmentWGData:GetEquipShengPinRemindNum()
	local data_list = EquipWGData.Instance:GetDataList()
	--排除同心锁
	local active_num = self:GetShenPinLevelRemind()
	for k,v in pairs(data_list) do
		if self:GetEquipNewShengPinRemindNumByPart(v.index) == 1 then
			MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_SHENGPIN, 1, function ()
				FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_shengpin)
			end)
			return 1
		end
	end


	if active_num > 0 then
		return 1
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EQUIP_SHENGPIN, 0)

	return 0
end
--------------------------------------------------------------------------------------------------------------------------------

function EquipmentWGData:GetEuipJiChengGetWayData(list)
	local get_way_cfg = ConfigManager.Instance:GetAutoConfig("getway_auto").get_way
	local role_level = RoleWGData.Instance.role_vo.level
	local cur_openserver_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local data = {}
	local get_way_list = Split(list,"|")
	if #get_way_list > 0 then
		for k,v in ipairs(get_way_list) do
			if get_way_cfg[tonumber(v)] and role_level >= get_way_cfg[tonumber(v)].limit_level then
				local temp_lsit = __TableCopy(get_way_cfg[tonumber(v)])
				temp_lsit.flag = false
				if get_way_cfg[tonumber(v)].id == 184 and cur_openserver_day <= 7 then
					table.insert(data,temp_lsit)
				elseif get_way_cfg[tonumber(v)].id ~= 184 then
					table.insert(data,temp_lsit)
				end
			end
		end
	end
	return data
end

function EquipmentWGData:GetShenPinLevelRemind()
	local equip_active_level = self:GetEquipUpQualityLV()
	local reach_noraml_equip_count = EquipmentWGData.Instance:GetNewShengPinLowestCount(GameEnum.EQUIP_BIG_TYPE_NORMAL, equip_active_level + 1)
	if reach_noraml_equip_count >= GameEnum.EQUIP_BIG_TYPE_NORMAL_COUNT then
		local open_func = function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_shengpin)
			local view_type = ShengPinAttrTip.ViewType.BaseEquipView
			EquipmentWGCtrl.Instance:OpenShengPinSuitAttr(view_type)
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHENGPIN_ATTR_ACTIVE , 1,open_func)
		return 1
	end

	local equip_active_level2 = self:GetXianQiUpQualityLV()
	local reach_xianqi_equip_count = EquipmentWGData.Instance:GetNewShengPinLowestCount(GameEnum.EQUIP_BIG_TYPE_XIANQI, equip_active_level2 + 1)
	if reach_xianqi_equip_count >= GameEnum.XIANQI_SHENPIN_ADD_NEED_COUNT then
		local open_func = function ()
			FunOpen.Instance:OpenViewByName(GuideModuleName.Equipment, TabIndex.equipment_shengpin)
			local view_type = ShengPinAttrTip.ViewType.SpeEquipView
			EquipmentWGCtrl.Instance:OpenShengPinSuitAttr(view_type)
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHENGPIN_ATTR_ACTIVE , 1,open_func)
		return 1
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHENGPIN_ATTR_ACTIVE , 0)
	return 0
end

function EquipmentWGData:GetXianQiComposeFocusItemList(index)
	local equipment_cfg
	if index == TabIndex.other_compose_shengjie then
		equipment_cfg = self.equipment_cfg.xianqi_upgrade
	elseif index == TabIndex.other_compose_shengxing then
		equipment_cfg = self.equipment_cfg.xianqi_upstar
	end

	local map = {}
	for k, cfg in pairs(equipment_cfg) do
		if cfg["stuff_id"] and cfg["stuff_id"] > 0 then
			map[cfg["stuff_id"]] = true
		end

		if cfg["stuff_id2"] and cfg["stuff_id2"] > 0 then
			map[cfg["stuff_id2"]] = true
		end

		if cfg["stuff_id3"] and cfg["stuff_id3"] > 0 then
			map[cfg["stuff_id3"]] = true
		end

		if cfg["origin_equip_id"] and cfg["origin_equip_id"] > 0 then
			map[cfg["origin_equip_id"]] = true
		end

		if cfg["target_equip_id"] and cfg["target_equip_id"] > 0 then
			map[cfg["target_equip_id"]] = true
		end
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end

	return item_id_list
end

function EquipmentWGData:GetEquipKillCfgByItemId(item_id)
	return self.equip_skill_map_cfg[item_id]
end

function EquipmentWGData:GetEquipSpecialAttrCfgByIdAndType(item_id, attr_type)
	if attr_type == EQUIP_SPECIAL_ATTR_TYPE.PINK then
		return self.equip_special_attr_p_cfg and self.equip_special_attr_p_cfg[item_id]
	elseif attr_type == EQUIP_SPECIAL_ATTR_TYPE.GOLD then
		return self.equip_special_attr_g_cfg and self.equip_special_attr_g_cfg[item_id]
	elseif attr_type == EQUIP_SPECIAL_ATTR_TYPE.COLOR then
		return self.equip_special_attr_c_cfg and self.equip_special_attr_c_cfg[item_id]
	end

	return nil
end

function EquipmentWGData:GetEquipSpecialAttrScore(item_id, attr_type)
	local score = 0
	local cfg = self:GetEquipSpecialAttrCfgByIdAndType(item_id, attr_type)

	if cfg then
		for i = 1, 10 do
			local attr_id = cfg["special_type" .. i]
			local attr_value = cfg["special_val" .. i]
			if attr_id and attr_value and attr_value > 0 then
				local pingfen = TipWGData.Instance:GetXianPingSpecialAttrByOrder(attr_id, attr_value)
				score = score + pingfen
			end
		end
	end
	return score
end

-- 计算背包里一个装备的评分
function EquipmentWGData:GetEquipPingfen(bag_data)
	if not bag_data or not bag_data.item_id or bag_data.item_id == 0 then
		return 0
	end

	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(bag_data.item_id)
	if not item_cfg or big_type ~= GameEnum.ITEM_BIGTYPE_EQUIPMENT then
		return 0
	end

	-- 基础评分
	local pingfen = TipWGData.Instance:GetCfgBasePingFenByAttrList(item_cfg)

	-- 仙品属性评分
	local legend_attr_list = nil
	if bag_data.param then
		if bag_data.param.xianpin_type_list then
			legend_attr_list = EquipWGData.GetLegendAttr(bag_data, false)
		else
			-- (预览属性也要评分计算)
			local legend_num = bag_data.param.star_level or 0
			legend_attr_list = EquipWGData.GetPreviewLegendAttr(bag_data, legend_num)
		end
	end

	if not IsEmptyTable(legend_attr_list) then
		for i, v in ipairs(legend_attr_list) do
			local pingfen_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(item_cfg.order, v.attr_type, v.value, v.is_star_attr)
			pingfen = pingfen + pingfen_num
		end
	end

	-- 特殊属性评分
	pingfen = pingfen + self:GetEquipSpecialAttrScore(bag_data.item_id, EQUIP_SPECIAL_ATTR_TYPE.PINK)
	pingfen = pingfen + self:GetEquipSpecialAttrScore(bag_data.item_id, EQUIP_SPECIAL_ATTR_TYPE.GOLD)
	pingfen = pingfen + self:GetEquipSpecialAttrScore(bag_data.item_id, EQUIP_SPECIAL_ATTR_TYPE.COLOR)

	pingfen = math.ceil(pingfen)
	return pingfen
end

--通过装备部位判断是装备类型：普通装备/仙器装备
function EquipmentWGData.GetEquipSuitTypeByPartType(part_type)
	if part_type == GameEnum.EQUIP_INDEX_XIANLIAN or	--3仙链
		part_type == GameEnum.EQUIP_INDEX_XIANZHUI or 	--6仙坠
		part_type == GameEnum.EQUIP_INDEX_XIANJIE or 	--8仙戒
		part_type == GameEnum.EQUIP_INDEX_XIANZHUO then --9仙镯
		-- part_type == GameEnum.EQUIP_INDEX_HUNJIE then	--10同心锁
		return GameEnum.EQUIP_BIG_TYPE_XIANQI
	end

	return GameEnum.EQUIP_BIG_TYPE_NORMAL
end

-- 装备肉身index 0 - 375  转部位index  0 - 15
function EquipmentWGData.GetEquipPartByEquipBodyIndex(equip_body_index)
	return equip_body_index % GameEnum.MAX_EQUIP_BODY_EQUIP_NUM
end

function EquipmentWGData.EquipBodyIndexToEquipBodySeqAndPart(equip_body_index)
	local equip_body_seq = math.modf(equip_body_index / GameEnum.MAX_EQUIP_BODY_EQUIP_NUM)
	return equip_body_seq, EquipmentWGData.GetEquipPartByEquipBodyIndex(equip_body_index)
end

function EquipmentWGData.EquipBodySeqAndPartToEquipBodyIndex(equip_body_seq, equip_part)
	return equip_body_seq * GameEnum.MAX_EQUIP_BODY_EQUIP_NUM + equip_part
end

-- 通过 子类型 阶数 品质 性别 职业 划分配置
function EquipmentWGData:InitAllWearEquipSplitData()
	local cfg = ConfigManager.Instance:GetAutoItemConfig("equipment_auto")
	if IsEmptyTable(cfg) then
		return {}
	end

	-- local sub_to_index = {}
	-- for i = GameEnum.EQUIP_TYPE_TOUKUI, GameEnum.EQUIP_TYPE_XIANZHUO do
	-- 	sub_to_index[i] = EquipWGData.Instance:GetEquipIndexByType(i)
	-- end

	local cache_list = {}
	for k, v in pairs(cfg) do
		if v.sub_type <= GameEnum.EQUIP_TYPE_XIANZHUO then
			-- local equip_index = sub_to_index[v.sub_type] or -1
			cache_list[v.sub_type] = cache_list[v.sub_type] or {}
			cache_list[v.sub_type][v.order] = cache_list[v.sub_type][v.order] or {}
			cache_list[v.sub_type][v.order][v.color] = cache_list[v.sub_type][v.order][v.color] or {}
			cache_list[v.sub_type][v.order][v.color][v.limit_sex] = cache_list[v.sub_type][v.order][v.color][v.limit_sex] or {}
			cache_list[v.sub_type][v.order][v.color][v.limit_sex][v.limit_prof] = v
		end
	end

	return cache_list
end

function EquipmentWGData:GetAllWearEquipSplitDataByData(sub_type, order, color, limit_sex, limit_prof)
	local empty_table = {}
	return (((((self.cahce_all_wear_equip_data or empty_table)[sub_type] or empty_table)[order]
		or empty_table)[color] or empty_table)[limit_sex] or empty_table)[limit_prof]
end

function EquipmentWGData:GetAllEquipSetengthenAttr()
	local now_level = 0
	local equip_cfg = {}
	local attr_tab = {
		maxhp = 0,
		gongji = 0,
		fangyu = 0,
		pojia = 0,
	}

	for i = 0, GameEnum.SUIT_ROLE_INDEX_MAX - 1 do
		now_level = self:GetStrengthLevelByIndex(i)
		if now_level > 0 then
			equip_cfg = self:GetNewStrengthCfgByLevel(i, now_level)
			attr_tab.maxhp = attr_tab.maxhp + (equip_cfg and equip_cfg.max_hp or 0)
			attr_tab.gongji = attr_tab.gongji + (equip_cfg and equip_cfg.gongji or 0)
			attr_tab.fangyu = attr_tab.fangyu + (equip_cfg and equip_cfg.fangyu or 0)
			attr_tab.pojia = attr_tab.pojia + (equip_cfg and equip_cfg.pojia or 0)
		end
	end
	return attr_tab
end

function EquipmentWGData:GetWearEquipHaveInlayFogre(equip_body_index)
	-- 宝石
	if self:GetEquipPartIsInlayStone(equip_body_index) then
		return true
	end

	-- 灵石
	if EquipmentLingYuWGData.Instance:GetEquipPartIsInlayLingYu(equip_body_index) then
		return true
	end

	-- 套装
	if self:GetEquipSuitOrderFlagByIndex(equip_body_index) > 0 then
		return true
	end

	return false
end

function EquipmentWGData:GetEquipShenPingAttrFromCfgByEquipId(equip_id)
	local cfg = self.equip_shenping_attr_cfg[equip_id]
	if not cfg then
		return {}
	end

	local val = cfg.base_attr_per or 0
	if type(val) ~= "number" then
		print_error("神品属性配置表 字段类型错误 不是一个number")
	end

	local tAttr = {}
	local keyinfu_attr_per_val = cfg.keyinfu_attr_per or 0
	local yinji_attr_per_val = cfg.yinji_attr_per or 0
	tAttr[1] = {nAttrId = GameEnum.BASE_ATTR_ADD_PRE, nVal = val, szAttrId = "base_attr_per"}
	tAttr[2] = {nAttrId = GameEnum.KEYINFU_ATTR_ADD_PRE, nVal = keyinfu_attr_per_val, szAttrId = "keyinfu_attr_per"}
	tAttr[3] = {nAttrId = GameEnum.YINJI_ATTR_ADD_PRE, nVal = yinji_attr_per_val, szAttrId = "yinji_attr_per"}
	
	return tAttr
end

function EquipmentWGData:GetEquipShenPingAttrFromCfgByEquipIdTwo(equip_id)
	if self.equip_shenping_attr_cfg[equip_id] then
		return self.equip_shenping_attr_cfg[equip_id]
	end
end

function EquipmentWGData.SortEquipTabForAwake(data_list)
	if data_list == nil or IsEmptyTable(data_list) then
		return {}
	end

	local tmp = {}
	local index = 1
	for k,v in pairs(data_list) do
		tmp[index] = v
		index = index + 1
	end

	local sort_func = function ( a,b )
			if a == nil or b == nil or a == b then
				return false
			end
			return EquipmentWGData.GetSortIndex(a.index) < EquipmentWGData.GetSortIndex(b.index)
		end

	table.sort(tmp, sort_func)
	return tmp
end

---------------------------------------属性读取-------------------------------------
-- 配置为属性id 输出列表和战力(属性id字符串， 属性value字符串, 属性数量读取开始索引， 属性数量读取结束索引)
function EquipmentWGData:OutStrAttrListAndCapalityByAttrId(cfg, type_key_str, value_key_str, type_out_key_str, value_out_key_str, read_start, read_end, allow_value_zero)
	local attr_list = {}
    local capability = 0
    if IsEmptyTable(cfg) or type_key_str == nil or value_key_str == nil then
        return attr_list, capability
    end

	read_start = read_start or 1
	read_end = read_end or 5
	type_out_key_str = type_out_key_str or "attr_name"
	value_out_key_str = value_out_key_str or "value_str"
	allow_value_zero = allow_value_zero or false

	local attr_list = {}
	local attribute = AttributePool.AllocAttribute()
	for i = read_start, read_end do
		local attr_id = cfg[type_key_str .. i]
		local attr_value = cfg[value_key_str .. i]
		
		if attr_id and attr_id > 0 and attr_value then
			if allow_value_zero or (not allow_value_zero and attr_value > 0) then
				local data = {}
				local attr_str = self:GetAttrStrByAttrId(tonumber(attr_id))
				data[type_out_key_str] = self:GetAttrNameByAttrStr(attr_str, true)
				data[value_out_key_str] = AttributeMgr.PerAttrValue(attr_str, attr_value)
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
				table.insert(attr_list, data)

				if attr_value > 0 and attribute[attr_str] then
					attribute[attr_str] = attribute[attr_str] + attr_value
				end
			end
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end
    
	capability = AttributeMgr.GetCapability(attribute)

	return attr_list, capability
end

function EquipmentWGData:OutZeroStrAttrListByAttrId(cfg, type_key_str, value_key_str, type_out_key_str, value_out_key_str, read_start, read_end)
	local attr_list = {}
    if IsEmptyTable(cfg) or type_key_str == nil or value_key_str == nil then
        return attr_list
    end

	read_start = read_start or 1
	read_end = read_end or 5
	type_out_key_str = type_out_key_str or "attr_name"
	value_out_key_str = value_out_key_str or "value_str"

	local attr_list = {}
	for i = read_start, read_end do
		local attr_id = cfg[type_key_str .. i]
		local attr_value = 0
		
		if attr_id and attr_id > 0 and attr_value then
			local data = {}
			local attr_str = self:GetAttrStrByAttrId(tonumber(attr_id))
			data[type_out_key_str] = self:GetAttrNameByAttrStr(attr_str, true)
			data[value_out_key_str] = AttributeMgr.PerAttrValue(attr_str, attr_value)
			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end

function EquipmentWGData:OutAttrInfoByAttrId(cfg, read_start, read_end, per)
	local attr_list = {}
	local attr_id, attr_value = 0, 0
	read_start = read_start or 1
	read_end = read_end or 5
	per = per or 1

	for i = read_start, read_end do
		attr_id = cfg["attr_id" .. i]
		attr_value = cfg["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = self:GetAttrStrByAttrId(attr_id)
			attr_list[attr_str] = attr_value * per
		end
	end

	return attr_list
end

function EquipmentWGData:OutSortAttrListHaveNextByTypeCfg(cur_cfg, next_cfg, cur_zero_value, type_key_str, value_key_str, type_out_key_str, value_out_key_str, next_value_out_key_str, type_out_key_name, value_out_add_name, read_start, read_end)
	read_start = read_start or 1
	read_end = read_end or 5
	type_key_str = type_key_str or "attr_id"
	value_key_str = value_key_str or "attr_value"

	type_out_key_str = type_out_key_str or "attr_str"
	type_out_key_name = type_out_key_name or "attr_name"
	value_out_key_str = value_out_key_str or "attr_value"
	value_out_add_name = value_out_add_name or "add_value" 
	next_value_out_key_str = next_value_out_key_str or "next_attr_value"
	
	cur_zero_value = cur_zero_value or false

	local attr_str
	local attr_list = {}
	local cur_attr_id, cur_attr_value, next_attr_id, next_attr_value, attr_id, attr_value = 0, 0, 0, 0, 0, 0
	for i = read_start, read_end do
		cur_attr_id = cur_cfg and cur_cfg[type_key_str .. i] or 0
		cur_attr_value = cur_zero_value and 0 or cur_cfg and cur_cfg[value_key_str .. i] or 0
		next_attr_id = next_cfg and next_cfg[type_key_str .. i] or 0
		next_attr_value = next_cfg and next_cfg[value_key_str .. i] or 0

		attr_id = cur_attr_id > 0 and cur_attr_id or next_attr_id
		attr_value = cur_attr_value > 0 and cur_attr_value or next_attr_value
		if attr_id > 0 and (attr_value > 0 or next_attr_value > 0) then
			local data = {}
			attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
			data[type_out_key_str] = attr_str
			data[type_out_key_name] = self:GetAttrNameByAttrStr(attr_str, true)
			data[value_out_key_str] = AttributeMgr.PerAttrValue(attr_str, cur_attr_value)
			data[next_value_out_key_str] = AttributeMgr.PerAttrValue(attr_str, next_attr_value)
			local add_value = next_attr_value - cur_attr_value
			data[value_out_add_name] = AttributeMgr.PerAttrValue(attr_str, add_value)
			data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			data.cur_attr_value = cur_attr_value
			data.next_attr_value = next_attr_value
			data.add_attr_value = add_value
			table.insert(attr_list, data)
		end
	end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end