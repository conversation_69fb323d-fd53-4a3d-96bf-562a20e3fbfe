LieMoDaRenWGData = LieMoDaRenWGData or BaseClass()

local PersonRankMaxCount = 100        --排行榜显示个数
local GuildRankMaxCount = 100        --排行榜显示个数

function LieMoDaRenWGData:__init()
	if LieMoDaRenWGData.Instance ~= nil then
		ErrorLog("[LieMoDaRenWGData] Attemp to create a singleton twice !")
	end
    LieMoDaRenWGData.Instance = self

    self.liemo_info = {
        score = 0,
        grade = -1,
        fetch_flag = {},
        guild_score = -1,
        guild_is_reward = 0,
        person_is_reward = 0,
    }

    self.person_rank = 0                --个人排行名次
    self.person_rank_value = 0          --个人猎魔值

    self.guild_rank = 0                 --仙盟排行名次
    self.guild_rank_value = 0           --仙盟猎魔值

    self.person_rank_list = {}          --个人排行列表
    self.guild_rank_list = {}           --仙盟排行列表

    self:InitConfig()

    RemindManager.Instance:Register(RemindName.MergeActivity_LieMoDaRen, BindTool.Bind(self.IsShowLieMoDaRenRedPoint, self))

    MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_BOSS_HUNTER, {[1] = MERGE_EVENT_TYPE.LEVEL},
    	BindTool.Bind(self.GetActIsOpen, self))
end

function LieMoDaRenWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.MergeActivity_LieMoDaRen)

    LieMoDaRenWGData.Instance = nil
end

function LieMoDaRenWGData:InitConfig()
    local all_config = ConfigManager.Instance:GetAutoConfig("combineserve_activity_boss_hunter_cfg_auto")
    self.config_param_map = ListToMap(all_config.config_param, "grade")

    self:MakeDataPersonRewardCfg(all_config.reward_cfg)
    self:MakeDataPersonRankReward(all_config.rank_cfg)
    self:MakeDataGuildRewardCfg(all_config.guild_reward_cfg)
end


function LieMoDaRenWGData:MakeDataPersonRewardCfg(reward_cfg)
    self.person_reward_grade_map = {}
    self.person_reward_grade_seq_map = {}

    for i, v in ipairs(reward_cfg) do
        if not self.person_reward_grade_map[v.grade] then
            self.person_reward_grade_map[v.grade] = {}
        end
        if not self.person_reward_grade_seq_map[v.grade] then
            self.person_reward_grade_seq_map[v.grade] = {}
        end

        local data = {}
        data.grade = v.grade
        data.seq = v.seq
        data.score = v.score
        data.item_list = {}
        for i = 0, #v.reward_item do
            table.insert(data.item_list, v.reward_item[i])
        end

        self.person_reward_grade_seq_map[v.grade][v.seq] = data
        table.insert(self.person_reward_grade_map[v.grade], data)
    end
end

function LieMoDaRenWGData:MakeDataPersonRankReward(cfg)
    self.person_rank_reward_map = {}
    for i, v in ipairs(cfg) do
        if not self.person_rank_reward_map[v.grade] then
            self.person_rank_reward_map[v.grade] = {}
        end

        local data = {}
        data.rank_hight = v.rank_hight
        data.rank_low = v.rank_low
        data.reach_value = v.reach_value    --排行达成值
        data.item_list = {}
        for i = 0, #v.reward_item do
            table.insert(data.item_list, v.reward_item[i])
        end
        table.insert(self.person_rank_reward_map[v.grade], data)
    end
end

function LieMoDaRenWGData:CheckIndex(index, grade)
    local list = self.person_rank_reward_map[grade]
    for i, v in ipairs(list) do
        if v.rank_hight <= index and index <= v.rank_low then
            return v
        end
    end

    return nil
end


--获取指定档次数据
function LieMoDaRenWGData:_GetGuildCfgList(grade)
    if not self.guild_reward_grade_map[grade] then
        print_error("LieMoDaRenWGData _GetGuildCfgList 获取配置错误 >>>>> 档次：", grade)
        return {}
    end

    return self.guild_reward_grade_map[grade]
end

--获取当前仙盟档次数据
function LieMoDaRenWGData:GetCurGuildCfgList()
    local grade = self:GetGrade()
    return self:_GetGuildCfgList(grade)
end


function LieMoDaRenWGData:MakeDataGuildRewardCfg(reward_cfg)
    self.guild_reward_grade_map = {}
    self.guild_reward_grade_seq_map = {}

    for i, v in ipairs(reward_cfg) do
        if not self.guild_reward_grade_map[v.grade] then
            self.guild_reward_grade_map[v.grade] = {}
        end
        if not self.guild_reward_grade_seq_map[v.grade] then
            self.guild_reward_grade_seq_map[v.grade] = {}
        end

        local data = {}
        data.grade = v.grade
        data.seq = v.seq
        data.score = v.score
        data.item_list = {}
        for i = 0, #v.reward_item do
            table.insert(data.item_list, v.reward_item[i])
        end

        self.guild_reward_grade_seq_map[v.grade][v.seq] = data
        table.insert(self.guild_reward_grade_map[v.grade], data)
    end
end

--获取指定档次数据
function LieMoDaRenWGData:_GetPersonCfgList(grade)
    if not self.person_reward_grade_map[grade] then
        print_error("LieMoDaRenWGData 获取配置错误 >>>>> 档次：", grade)
        return {}
    end

    return self.person_reward_grade_map[grade]
end

--获取当前档次数据 need_sort:是否需要排序
function LieMoDaRenWGData:GetCurPersonCfgList(need_sort)
    local grade = self:GetGrade()
    local list = self:_GetPersonCfgList(grade)
    if not need_sort then
        return list
    end
    local sort_list = {}
    --可领取 > 未领取 > 已领取
    for i, v in ipairs(list) do
        v.sort_value = 0
        if self:CanFetch(v.seq) then
            v.sort_can_fetch = 1000
        else
            v.sort_can_fetch = 1000000
        end

        if self:GetIsFetchReward(v.seq) then
            v.sort_is_fetch_value = 100000
        else
            v.sort_is_fetch_value = 1000
        end

        table.insert(sort_list, v)
    end

    table.sort(sort_list, SortTools.KeyLowerSorters("sort_can_fetch", "sort_is_fetch_value", "seq"))
    return sort_list
end

--获取当前档次单条数据
function LieMoDaRenWGData:GetPersonOnceCfg(seq)
    local grade = self:GetGrade()
    if not self.person_reward_grade_seq_map[grade] then
        return nil
    end

    return self.person_reward_grade_seq_map[grade][seq]
end


function LieMoDaRenWGData:GetGrade()
    return self.liemo_info.grade
end

function LieMoDaRenWGData:SetLieMoInfo(protocol)
    self.liemo_info.score = protocol.score                      --个人猎魔值
    self.liemo_info.grade = protocol.grade                      --档次
    local tab = bit:d2b_two(protocol.fetch_flag)
    self.liemo_info.fetch_flag = tab                            --个人领取标记
    --print_error("个人领取标记:",tab)
    self.liemo_info.guild_score = protocol.guild_score          --仙盟猎魔值
    self.liemo_info.guild_is_reward = protocol.guild_is_reward  --仙盟是否奖励
    self.liemo_info.person_is_reward = protocol.person_is_reward --个人是否奖励
end

function LieMoDaRenWGData:GetLieMoInfo()
    return self.liemo_info
end

function LieMoDaRenWGData:GetIsFetchReward(seq)
    return self.liemo_info.fetch_flag[seq] == 1
end

--是否可领取
function LieMoDaRenWGData:CanFetch(seq)
    local cfg = self:GetPersonOnceCfg(seq)
    if not cfg then
        return false
    end

    --已经领了返回false
    if self:GetIsFetchReward(seq) then
        return false
    end

    return self.liemo_info.score >= cfg.score
end

function LieMoDaRenWGData:IsShowLieMoDaRenRedPoint()
    local list = self:GetCurPersonCfgList()
    if not IsEmptyTable(list) then
        for i, v in ipairs(list) do
            if self:CanFetch(v.seq) then
                return 1
            end
        end
    end

    return 0
end

function LieMoDaRenWGData:GetActIsOpen()
    self.liemo_info.grade = 0
    if self.liemo_info.grade == -1 then
        return false
    end

    local cfg = self.config_param_map[self.liemo_info.grade]
    if not cfg then
        return false
    end

    local vo = GameVoManager.Instance:GetMainRoleVo()
    if vo.level >= cfg.open_role_level then
        return true
    end

    return false
end



--设置排行数据
function LieMoDaRenWGData:SetRankData(protocol)
    --if PersonRankType.PERSON_RANK_TYPE_BOSS_HUNTER == protocol.rank_type then
    --    self.person_rank_list = protocol.rank_list
    --    self.person_rank = protocol.self_rank
    --    self.person_rank_value = protocol.self_value
    --end
    if GuildRankType.GUILD_RANK_TYPE_CSA_BOSS_HUNTER == protocol.rank_type then
        self.guild_rank_list = protocol.rank_list
        self.guild_rank = protocol.self_rank
        self.guild_rank_value = protocol.self_value
    end
end

--设置个人排行数据
function LieMoDaRenWGData:SetPersonRankData(protocol)
    self.person_rank_list = protocol.rank_list
    self.person_rank = protocol.self_rank
    self.person_rank_value = protocol.self_value
    GlobalEventSystem:Fire(CHONGBANG_TIP_EVENT.ACT_CHECK,ChongBangTipWGData.ACT_TIP_TYPE.ACT,ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CSA_BOSS_HUNTER)
end

function LieMoDaRenWGData:GetPersonRankInfo()
    return self.person_rank, self.person_rank_value
end

function LieMoDaRenWGData:GetGuildRankInfo()
    return self.guild_rank, self.guild_rank_value
end


--获取个人排行列表
function LieMoDaRenWGData:GetLieMoPersonRankDataList()
    local list = {}
    if #self.person_rank_list >= PersonRankMaxCount then
        for i, v in ipairs(self.person_rank_list) do
            if i <= PersonRankMaxCount then
                if v.user_id > 0 then
                    local data = self:CheckIndex(i, self.liemo_info.grade)
                    if data then
                        v.item_list = data.item_list
                        v.reach_value = data.reach_value
                        table.insert(list, v)
                    end
                else
                    local data = self:CheckIndex(i, self.liemo_info.grade)
                    if data then
                        --构造假数据
                        local item = v
                        item.rank_value = 0
                        item.is_empty_value = true
                        item.item_list = data.item_list
                        item.reach_value = data.reach_value
                        table.insert(list, item)
                    end
                end
            else
                break
            end
        end
    else
        for i, v in ipairs(self.person_rank_list) do
            if v.user_id > 0 then
                local data = self:CheckIndex(i, self.liemo_info.grade)
                if data then
                    v.item_list = data.item_list
                    v.reach_value = data.reach_value
                    table.insert(list, v)
                end
            else
                local data = self:CheckIndex(i, self.liemo_info.grade)
                if data then
                    --构造假数据
                    local item = v
                    item.rank_value = 0
                    item.is_empty_value = true
                    item.item_list = data.item_list
                    item.reach_value = data.reach_value
                    table.insert(list, item)
                end
            end
        end
        local remain_start_index = #self.person_rank_list + 1
        for i = remain_start_index, PersonRankMaxCount do
            local data = self:CheckIndex(i, self.liemo_info.grade)
            if data then
                --构造假数据
                local item = {}
                item.rank_value = 0
                item.is_empty_value = true
                item.item_list = data.item_list
                item.reach_value = data.reach_value
                table.insert(list, item)
            end
        end
    end
    return list
end

--获取仙盟排行列表
function LieMoDaRenWGData:GetLieMoGuildRankDataList()
    local list = {}
    if #self.guild_rank_list >= GuildRankMaxCount then
        for i, v in ipairs(self.guild_rank_list) do
            if i <= GuildRankMaxCount then
                if v.guild_id > 0 then
                    table.insert(list, v)
                else
                    --构造假数据
                    local data = v
                    data.rank_value = 0
                    data.is_empty_value = true
                    table.insert(list, data)
                end
            else
                break
            end
        end
    else
        for i, v in ipairs(self.guild_rank_list) do
            if v.guild_id > 0 then
                table.insert(list, v)
            else
                --构造假数据
                local data = v
                data.rank_value = 0
                data.is_empty_value = true
                table.insert(list, data)
            end
        end
        local remain_start_index = #self.guild_rank_list + 1
        for i = remain_start_index, GuildRankMaxCount do
            local data = {}
            data.rank_value = 0
            data.is_empty_value = true
            table.insert(list, data)
        end
    end
    return list
end

function LieMoDaRenWGData:GetPersonMaxRank()
    return PersonRankMaxCount
end

function LieMoDaRenWGData:GetGuildMaxRank()
    return GuildRankMaxCount
end