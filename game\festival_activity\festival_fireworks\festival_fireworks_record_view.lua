FestivalFireworksRecord = FestivalFireworksRecord or BaseClass(SafeBaseView)

function FestivalFireworksRecord:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
                        {vector2 = Vector2(0, 0), sizeDelta = Vector2(850,500)})
    self:AddViewResource(0, "uis/view/festival_activity_ui/niudan_ui_prefab", "layout_merge_fireworks_record")
	self:SetMaskBg(true, true)
end

function FestivalFireworksRecord:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
    self.data_list = nil
end

function FestivalFireworksRecord:ShowIndexCallBack()
    FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.RECORD)
    FestivalFireworksWGData.Instance:UpdateRecordCount()
end

function FestivalFireworksRecord:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.OAFish.RewardListTitle
    self.record_list = AsyncListView.New(FestivalFireworksRecordRender, self.node_list["record_list"])
    self.record_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize,self))

    local other_cfg = FestivalFireworksWGData.Instance:GetGradeCfg()
    self.node_list.no_flag_text.text.color = Str2C3b(other_cfg.zanwu_text_color)

end

local LINE_SPACING = 20
function FestivalFireworksRecord:ChangeCellSize(data_index)
    local data = self.data_list and self.data_list[data_index + 1] 
    if not data then return 0 end

    local cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',data.item_id)
        return 0
    end
    local str = string.format(Language.FestivalFireworks.Record, data.role_name, ITEM_COLOR[cfg.color], cfg.name, data.num)

    self.node_list["TestText"].text.text = str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

    local hight = math.ceil(self.node_list["TestText"].rect.rect.height) + LINE_SPACING
    return hight or 0
end

function FestivalFireworksRecord:OnFlush()
    local data = FestivalFireworksWGData.Instance:GetRecordInfo() or {}
    self.data_list = data
    self.record_list:SetDataList(data, 3)
    self.node_list["no_flag"]:SetActive(IsEmptyTable(data))
end

function FestivalFireworksRecord:CloseCallBack()
    FestivalFireworksWGData.Instance:UpdateRecordCount()
    ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2270, "all", {[2] = "record"})
end
-------------------------------------------------------------------------------------
FestivalFireworksRecordRender = FestivalFireworksRecordRender or BaseClass(BaseRender)

function FestivalFireworksRecordRender:LoadCallBack()
    local common_color = FestivalActivityWGData.Instance:GetCommonColor()
    self.node_list["info"].text.color = Str2C3b(common_color)
end

function FestivalFireworksRecordRender:OnFlush()
    if not self.data then
        return
    end

    --self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(self.data.draw_time)
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',self.data.item_id)
        return
    end
    local str = string.format(Language.FestivalFireworks.Record, self.data.role_name, ITEM_COLOR[cfg.color], cfg.name, self.data.num)
    self.node_list["info"].text.text = str
    self.node_list.bg:SetActive(self:GetIndex() % 2 == 0)
end