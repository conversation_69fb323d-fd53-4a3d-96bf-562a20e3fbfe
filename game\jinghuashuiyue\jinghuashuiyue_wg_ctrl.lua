require("game/jinghuashuiyue/jinghuashuiyue_wg_data")
require("game/jinghuashuiyue/jinghuashuiyue_view")

JingHuaShuiYueWGCtrl = JingHuaShuiYueWGCtrl or BaseClass(BaseWGCtrl)
function JingHuaShuiYueWGCtrl:__init()
	if JingHuaShuiYueWGCtrl.Instance then
		error("[JingHuaShuiYueWGCtrl]:Attempt to create singleton twice!")
	end

    JingHuaShuiYueWGCtrl.Instance = self

    self.data = JingHuaShuiYueWGData.New()
    self.view = JingHuaShuiYueView.New(GuideModuleName.JingHuaShuiYueView)

    self:RegisterAllProtocols()
end

function JingHuaShuiYueWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

    JingHuaShuiYueWGCtrl.Instance = nil
end

function JingHuaShuiYueWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSJingHuaShuiYueOperate)
	self:RegisterProtocol(SCJingHuaShuiYueInfo, "OnSCJingHuaShuiYueInfo")
	self:RegisterProtocol(SCJingHuaShuiYueActiveInfo, "OnSCJingHuaShuiYueActiveInfo")
	self:RegisterProtocol(SCJingHuaShuiYueUpgradeInfo, "OnSCJingHuaShuiYueUpgradeInfo")
	self:RegisterProtocol(SCJingHuaShuiYuePosInfo, "OnSCJingHuaShuiYuePosInfo")
end

function JingHuaShuiYueWGCtrl:SendJingHuaShuiYueOperateReq(operate_type, param1, param2, param3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSJingHuaShuiYueOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function JingHuaShuiYueWGCtrl:OnSCJingHuaShuiYueInfo(protocol)
    self.data:SetJingHuaShuiYueAllInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.JingHuaShuiYueView)
    RemindManager.Instance:Fire(RemindName.JingHuaShuiYue)
end

function JingHuaShuiYueWGCtrl:OnSCJingHuaShuiYueActiveInfo(protocol)
    self.data:UpdateActiveInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.JingHuaShuiYueView, nil, "level_up", {update_suit = protocol.suit_id})
    RemindManager.Instance:Fire(RemindName.JingHuaShuiYue)
end

function JingHuaShuiYueWGCtrl:OnSCJingHuaShuiYueUpgradeInfo(protocol)
    self.data:UpdateUpStarInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.JingHuaShuiYueView, nil, "level_up", {update_suit = protocol.suit_id})
    RemindManager.Instance:Fire(RemindName.JingHuaShuiYue)
end

function JingHuaShuiYueWGCtrl:OnSCJingHuaShuiYuePosInfo(protocol)
    self.data:UpdatePosInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.JingHuaShuiYueView, nil, "data_update")
    RemindManager.Instance:Fire(RemindName.JingHuaShuiYue)
end