function PrivilegeCollectionView:SQCYLoadCallBack()
    if not self.holy_weapon_attr_list then
        self.holy_weapon_attr_list = {}
		local node_num = self.node_list["holy_weapon_attr_list"].transform.childCount
        for i = 1, node_num do
            self.holy_weapon_attr_list[i] = CommonAddAttrRender.New(self.node_list["attr_" .. i])
			self.holy_weapon_attr_list[i]:SetIndex(i)
			self.holy_weapon_attr_list[i]:SetAttrNameNeedSpace(true)
            -- self.holy_weapon_attr_list[i]:SetAttrArrowTween(true)
            -- self.holy_weapon_attr_list[i]:SetAttrNeedMaoHao(true)
        end
    end

    if not self.holy_weapon_layer_attr_list then
        self.holy_weapon_layer_attr_list = {}

        for i = 1, 2 do
            self.holy_weapon_layer_attr_list[i] = CommonAddAttrRender.New(self.node_list["layer_attr_" .. i])
            self.holy_weapon_layer_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

    if not self.holy_weapon_list then
        self.holy_weapon_list = AsyncListView.New(RechargeHolyWeaponRender, self.node_list["holy_weapon_list"])
		self.holy_weapon_list:SetSelectCallBack(BindTool.Bind1(self.OnClickHolyWeaponCell, self))
    end

    if not self.sqcy_model then
        self.sqcy_model = RoleModel.New()
        self.sqcy_model:SetUISceneModel(self.node_list["sqcy_model"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
        self:AddUiRoleModel(self.sqcy_model, TabIndex.pri_col_sqcy)
    end

    -- UITween.MoveLoop(self.node_list.holy_weapon_img.gameObject, u3dpool.vec3(0, 20, 0), u3dpool.vec3(0, 0, 0), 2)

    XUI.AddClickEventListener(self.node_list.holy_weapon_buy_btn, BindTool.Bind(self.OnHolyWeaponBuyBtnClick, self))
    XUI.AddClickEventListener(self.node_list.holy_weapon_rule_btn, BindTool.Bind(self.OnHolyWeaponClickRuleBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_preview, BindTool.Bind(self.OnHolyWeaponPreviewBtnClick, self))

    local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local save_open_day = PlayerPrefsUtil.GetInt("PrivilegeCollectionSQCY" .. main_role_id)
    if save_open_day ~= open_server_day then
        PlayerPrefsUtil.SetInt("PrivilegeCollectionSQCY" .. main_role_id, open_server_day)
    	RemindManager.Instance:Fire(RemindName.PrivilegeCollection_SQCY)
    end

    self.holy_weapon_cache = {}
end

function PrivilegeCollectionView:SQCYShowIndexCallBack()
    self.holy_weapon_cache = {}
end

function PrivilegeCollectionView:SQCYReleaseCallBack()
    if self.holy_weapon_attr_list then
        for k, v in pairs(self.holy_weapon_attr_list) do
            v:DeleteMe()
        end
        self.holy_weapon_attr_list = nil
    end

    if self.holy_weapon_list then
        self.holy_weapon_list:DeleteMe()
        self.holy_weapon_list = nil
    end

    if self.holy_weapon_layer_attr_list then
        for k, v in pairs(self.holy_weapon_layer_attr_list) do
            v:DeleteMe()
        end

        self.holy_weapon_layer_attr_list = nil
    end

    if self.sqcy_model then
        self.sqcy_model:DeleteMe()
        self.sqcy_model = nil
    end

    self.holy_weapon_select_index = nil
    self.holy_weapon_cache = nil

    if self.delay_holy_weapon_tip then
        GlobalTimerQuest:CancelQuest(self.delay_holy_weapon_tip)
        self.delay_holy_weapon_tip = nil
    end
end

function PrivilegeCollectionView:SQCYOnFlush()
    local data_list = PrivilegeCollectionWGData.Instance:GetHolyWeaponDataList()
    if IsEmptyTable(data_list) then
		return {}
	end

	self.holy_weapon_list:SetDataList(data_list)

    local select_data = self:GetCurSelectHolyItemData()
    if not IsEmptyTable(select_data) then
        self:FlushHolyWeaponSelectData(select_data)
    end
end

function PrivilegeCollectionView:OnHolyWeaponClickRuleBtn()
    RuleTip.Instance:SetContent(Language.PrivilegeCollection.HolyTipsContent, Language.PrivilegeCollection.HolyTipsTitle)
end

function PrivilegeCollectionView:OnClickHolyWeaponCell(item)
    if self.holy_weapon_select_index ~= item.index or self.holy_weapon_select_index == nil then
        self:FlushHolyWeaponSelectData(item:GetData())
        self.holy_weapon_select_index = item.index
    end
end

function PrivilegeCollectionView:FlushHolyWeaponSelectData(data)
    local info = PrivilegeCollectionWGData.Instance:GetHolyWeaponInfoById(data and data.cfg.id)
    if IsEmptyTable(data) or IsEmptyTable(info) then
        return
    end
    self:FlushSqcyViewSet(data)

    --更新技能描述
    self.node_list.holy_weapon_skill_desc.text.text = data.skill_desc
    self.node_list.desc_sqcy_progress.text.text = data.skill_desc2
    self.node_list.desc_sqcy_skill_title2.text.text = data.cfg.desc3

    --更新属性
    local attr_list = PrivilegeCollectionWGData.Instance:GetHolyWeaponAttrList(data.cfg.id)
	if not IsEmptyTable(attr_list) then
		for i = 1, #self.holy_weapon_attr_list do
			self.holy_weapon_attr_list[i]:SetRealHideNext(false)
			self.holy_weapon_attr_list[i]:SetData(attr_list[i])
            -- self.holy_weapon_attr_list[i].node_list.arrow2:CustomSetActive(attr_list[i].add_value and attr_list[i].add_value > 0)
		end
	end

    local skill_attr_list = PrivilegeCollectionWGData.Instance:GetHolyWeaponSkillAttrList(data.cfg.skill_id)
    for i = 1, 2 do
        self.holy_weapon_layer_attr_list[i]:SetRealHideNext(false)
        self.holy_weapon_layer_attr_list[i]:SetData(skill_attr_list[i])
    end
    
    --刷新购买按钮状态
    local purchase_cfg = data.purchase_cfg
    local is_limit = IsEmptyTable(purchase_cfg) and true or purchase_cfg.max_purchase_num <= info.purchase_times
    XUI.SetButtonEnabled(self.node_list.holy_weapon_buy_btn, not is_limit)
    -- self.node_list.holy_weapon_original_price:CustomSetActive(not is_limit)
    if is_limit then
        self.node_list.holy_weapon_buy_btn_text.text.text = Language.PrivilegeCollection.MaxLevel
    else
        local str = RoleWGData.GetPayMoneyStr(purchase_cfg.price, purchase_cfg.rmb_type, purchase_cfg.rmb_seq)
        local language = info.level > 0 and Language.PrivilegeCollection.HolyUpgrade or Language.PrivilegeCollection.HolyBuy
        self.node_list.holy_weapon_buy_btn_text.text.text = string.format(language, str)
    end

    if not is_limit then
        if data.cfg.id == self.holy_weapon_cache.id then
            local next_level_cfg = PrivilegeCollectionWGData.Instance:GetHolyWeaponCfg(data.cfg.id, data.cfg.level + 1)
            if not IsEmptyTable(next_level_cfg) then
                if (data.cfg.level > self.holy_weapon_cache.level) and (next_level_cfg.max_process > data.cfg.max_process) then
                    self:ShowOrHideHolyWeaponTip(true)
                end
            end
        else
            self:ShowOrHideHolyWeaponTip(false)
        end
    else
        self:ShowOrHideHolyWeaponTip(false)
    end

    self.holy_weapon_cache = {id = data.cfg.id, level = data.cfg.level}

    --设置主图片
	-- local bundle, asset = ResPath.GetRawImagesPNG("a2_mrcz_sqcy_sq" .. data.cfg.id)
	-- self.node_list.holy_weapon_img.raw_image:LoadSpriteAsync(bundle, asset, function ()
	-- 	self.node_list.holy_weapon_img.raw_image:SetNativeSize()
    -- end)

    --设置文字图片
    -- bundle, asset = ResPath.GetRawImagesPNG("a2_mrcz_sqcy_ggwz" .. data.cfg.id)
	-- self.node_list.holy_weapon_title_img.raw_image:LoadSpriteAsync(bundle, asset, function ()
	-- 	self.node_list.holy_weapon_title_img.raw_image:SetNativeSize()
    -- end)

    --设置战力
    local cap = PrivilegeCollectionWGData.Instance:GetHolyWeaponCap(data.cfg.id)
	self.node_list.holy_weapon_cap_value.text.text = cap

    --原价
	self.node_list.holy_weapon_original_price.text.text = string.format(Language.PrivilegeCollection.OriginalPrice2, 
                                                                                purchase_cfg.original_price or 0)
                                                                                
    -- --等级
    -- local str = Language.PrivilegeCollection.HolySkillName
    -- if info.level > 0 then
    --     str = string.format(Language.PrivilegeCollection.HolySkillLv, info.level)
    -- end
    -- self.node_list.holy_weapon_lv_text.text.text = str
end

function PrivilegeCollectionView:ShowOrHideHolyWeaponTip(state)
	if self.delay_holy_weapon_tip then
		GlobalTimerQuest:CancelQuest(self.delay_holy_weapon_tip)
		self.delay_holy_weapon_tip = nil
	end

    if self.node_list.flag_holy_weapon_buy_tip then
        self.node_list.flag_holy_weapon_buy_tip:CustomSetActive(state)
    end

    if state then
        self.delay_holy_weapon_tip = GlobalTimerQuest:AddDelayTimer(function ()
            if self.node_list.flag_holy_weapon_buy_tip then
                self.node_list.flag_holy_weapon_buy_tip:CustomSetActive(false)
            end
        end, 3)
    end
end

function PrivilegeCollectionView:FlushSqcyViewSet(data)
    local id = ((data or {}).cfg or {}).id or -1

    if id > 0 then
        local view_cfg = PrivilegeCollectionWGData.Instance:GetHolyWeaponViewShowCfg(data.cfg.id)
        if view_cfg then
            self:FlushSQCYModel(view_cfg)

            local bundle, asset = "", ""
            bundle, asset = ResPath.GetRawImagesPNG(view_cfg.weapon_title_img)
            self.node_list.holy_weapon_title_img.raw_image:LoadSprite(bundle, asset, function ()
                self.node_list.holy_weapon_title_img.raw_image:SetNativeSize()
            end)

            self.node_list.title_desc.text.text = view_cfg.title_desc or ""

            bundle, asset = ResPath.GetRawImagesPNG(view_cfg.holy_weapon_img)
            self.node_list.holy_weapon_img.raw_image:LoadSprite(bundle, asset, function ()
                self.node_list.holy_weapon_img.raw_image:SetNativeSize()
            end)
            
            bundle, asset = ResPath.GetPrivilegeCollectionImg(view_cfg.holy_weapon_name_img)
            self.node_list.holy_weapon_name_img.image:LoadSprite(bundle, asset, function ()
                self.node_list.holy_weapon_name_img.image:SetNativeSize()
            end)

            for i = 1, 2 do
                bundle, asset = ResPath.GetPrivilegeCollectionImg(view_cfg.skill_title_bg)
                self.node_list["skill_title_bg" .. i].image:LoadSprite(bundle, asset, function ()
                    self.node_list["skill_title_bg" .. i].image:SetNativeSize()
                end)

                bundle, asset = ResPath.GetPrivilegeCollectionImg(view_cfg.skill_title_bg_arrow)
                self.node_list["skill_title_bg_arrow_left" .. i .."_1"].image:LoadSprite(bundle, asset, function ()
                    self.node_list["skill_title_bg_arrow_left" .. i .."_1"].image:SetNativeSize()
                end)

                self.node_list["skill_title_bg_arrow_right" .. i .."_2"].image:LoadSprite(bundle, asset, function ()
                    self.node_list["skill_title_bg_arrow_right" .. i .. "_2"].image:SetNativeSize()
                end)
            end

            bundle, asset = ResPath.GetRawImagesPNG(view_cfg.holy_weapon_buy_btn)
            self.node_list["holy_weapon_buy_btn"].raw_image:LoadSprite(bundle, asset, function ()
                self.node_list["holy_weapon_buy_btn"].raw_image:SetNativeSize()
            end)

            bundle, asset = ResPath.GetPrivilegeCollectionImg(view_cfg.holy_weapon_btn_tip_bg)
            self.node_list["flag_holy_weapon_buy_tip"].image:LoadSprite(bundle, asset, function ()
                self.node_list["flag_holy_weapon_buy_tip"].image:SetNativeSize()
            end)
            self.ui_scene_change_config_index = view_cfg.ui_scene_config_index
            Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.PRI_COL_SQCY, view_cfg.ui_scene_config_index)
        end
    end
end

function PrivilegeCollectionView:FlushSQCYModel(view_cfg)
    -- 模型
    local model_bundle, model_asset = view_cfg.model_bundle, view_cfg.model_asset
    if self.sqcy_model then
        self.sqcy_model:SetMainAsset(model_bundle, model_asset)
    end

    if view_cfg.display_pos and view_cfg.display_pos ~= "" then
        local pos = Split(view_cfg.display_pos, "|")
        self.sqcy_model:SetUSAdjustmentNodeLocalPosition(pos[1] or 0, pos[2] or 0, pos[3] or 0)
    end

    local rotate_str = view_cfg.rotation
    if rotate_str and rotate_str ~= "" then
        local rot = Split(rotate_str, "|")
        self.sqcy_model:SetUSAdjustmentNodeLocalRotation(rot[1] or 0, rot[2] or 0, rot[3] or 0)
    end

    local scale = view_cfg.model_scale
    if scale and scale ~= "" then
        self.sqcy_model:SetUSAdjustmentNodeLocalScale(scale)
    end
end

function PrivilegeCollectionView:OnHolyWeaponBuyBtnClick()
    local data = self:GetCurSelectHolyItemData()
	local info = PrivilegeCollectionWGData.Instance:GetHolyWeaponInfoById(data.cfg and data.cfg.id)
    if IsEmptyTable(data) or IsEmptyTable(info) then 
        return 
    end
    
    local purchase_cfg = data.purchase_cfg
	if info.purchase_times >= purchase_cfg.max_purchase_num or IsEmptyTable(purchase_cfg) then
		return
	end

    self:ShowOrHideHolyWeaponTip(false)

	-- 发送协议
    --print_error("Recharge", purchase_cfg.price, purchase_cfg.rmb_type,purchase_cfg.rmb_seq)
	RechargeWGCtrl.Instance:Recharge(purchase_cfg.price, purchase_cfg.rmb_type,purchase_cfg.rmb_seq)
end

function PrivilegeCollectionView:GetCurSelectHolyItemData()
    if IsEmptyTable(self.holy_weapon_list.data_list) then
		return {}
	end

	return self.holy_weapon_list:GetCurItemData() or {}
end

function PrivilegeCollectionView:OnHolyWeaponPreviewBtnClick()
    local data = self:GetCurSelectHolyItemData()
    PrivilegeCollectionWGCtrl.Instance:OpenSQCYTip(data)
end

-------------------------------------------RechargeHolyWeaponRender----------------------------------------------
RechargeHolyWeaponRender = RechargeHolyWeaponRender or BaseClass(BaseRender)

function RechargeHolyWeaponRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

    local view_cfg = PrivilegeCollectionWGData.Instance:GetHolyWeaponViewShowCfg(self.data.cfg.id)
    if view_cfg then
        local bundle, asset = "", ""
        bundle, asset = ResPath.GetPrivilegeCollectionImg(view_cfg.holy_weapon_icon)
        self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
            self.node_list.icon.image:SetNativeSize()
        end)

        bundle, asset = ResPath.GetPrivilegeCollectionImg(view_cfg.holy_weapon_item_name_img)
        self.node_list.img_name.image:LoadSpriteAsync(bundle, asset, function ()
            self.node_list.img_name.image:SetNativeSize()
        end)
    end

    local info = PrivilegeCollectionWGData.Instance:GetHolyWeaponInfoById(self.data.cfg.id)
    self.node_list.flag_lock:CustomSetActive(info and info.level <= 0)
    self.node_list.img_bg:CustomSetActive(info and info.level > 0)
end

function RechargeHolyWeaponRender:OnSelectChange(is_select)
    local color = is_select and StrToColor(COLOR3B.WHITE) or StrToColor(COLOR3B.GRAY)
    self.node_list.icon.image.color = color
    self.node_list.img_bg.image.color = color
    self.node_list.img_name.image.color = color

    local scale_vec3 = is_select and Vector3(1, 1, 1) or Vector3(0.82, 0.82, 0.82)
    self.node_list.icon.transform.localScale = scale_vec3
    self.node_list.img_bg.transform.localScale = scale_vec3

    local lock_scale_vec3 = is_select and Vector3(0.8, 0.8, 0.8) or Vector3(0.6, 0.6, 0.6)
    self.node_list.flag_lock.transform.localScale = lock_scale_vec3
end