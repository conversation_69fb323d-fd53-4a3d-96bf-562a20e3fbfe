function CustomizedRumorsView:BroadcastLoadIndexCallBack()
    if not self.regional_barrage_area then
        self.regional_barrage_area = RegionalBarrage.New(self.node_list.regional_barrage_area)
        self.regional_barrage_area:SetGetDanMuInfoFunc(BindTool.Bind(function ()
            return CustomizedRumorsWGData.Instance:GetABarrageMessage()
        end, self))
    end

    if not self.br_specail_rumiors then
        self.br_specail_rumiors = BaseSpecialRumorsCell.New(self.node_list.br_special_rumior_root)
    end

    if not self.br_rumors_list then
        self.br_rumors_list = AsyncListView.New(BRRumorslistItemRender, self.node_list.br_rumors_list)
    end

    if not self.br_reward_show_list then
        self.br_reward_show_list = AsyncListView.New(ItemCell, self.node_list.br_reward_show_list)
        self.br_reward_show_list:SetStartZeroIndex(true)
    end

    if not self.broadcast_left_list then
        self.broadcast_left_list = AsyncListView.New(BRRumorLeftItemRender, self.node_list.broadcast_left_list)
        self.broadcast_left_list:SetDefaultSelectIndex(nil)
        self.broadcast_left_list:SetSelectCallBack(BindTool.Bind(self.OnSelectBRRumorsHandler, self))
    end

    local other_cfg = CustomizedRumorsWGData.Instance:GetOtherCfg()
    self.node_list.desc_plan_name.text.text = other_cfg.plan_name or ""
    self.br_reward_show_list:SetDataList(other_cfg.reward_item)

    self.cur_select_grade = -1

    XUI.AddClickEventListener(self.node_list.btn_br_rxchange, BindTool.Bind(self.OnClickBrExChange, self))
end

-- function CustomizedRumorsView:CloseCallBack()
--     if self.regional_barrage_area then
--         self.regional_barrage_area:StopAndClearDanMu()
--     end
-- end

function CustomizedRumorsView:BroadcastShowIndexCallBack()
    if self.regional_barrage_area then
        self.regional_barrage_area:StartShowDanMu()
    end

    self:RightPanleShowTween(self.node_list.br_right_tween_root, self.node_list.br_right_tween_root)
end

function CustomizedRumorsView:BroadcastChangeIndexCallBack()
    -- if self.regional_barrage_area then
    --     self.regional_barrage_area:StopAndClearDanMu()
    -- end
end

function CustomizedRumorsView:BroadcastReleaseCallBack()
    if self.regional_barrage_area then
        self.regional_barrage_area:DeleteMe()
        self.regional_barrage_area = nil
    end

    if self.br_specail_rumiors then
        self.br_specail_rumiors:DeleteMe()
        self.br_specail_rumiors = nil 
    end

    if self.br_rumors_list then
        self.br_rumors_list:DeleteMe()
        self.br_rumors_list = nil
    end

    if self.br_reward_show_list then
        self.br_reward_show_list:DeleteMe()
        self.br_reward_show_list = nil
    end

    if self.broadcast_left_list then
        self.broadcast_left_list:DeleteMe()
        self.broadcast_left_list = nil
    end
end

function CustomizedRumorsView:OnSelectBRRumorsHandler(item)
    if nil == item or IsEmptyTable(item.data) then
        return
    end
    local data = item.data
    self.br_select_rumors_data = data
    self.cur_select_grade = data.grade
    self:FlushBRRumorsInfo(data)
end

function CustomizedRumorsView:FlushBRRumorsInfo(data)
    if IsEmptyTable(data) then
        return
    end

	local server_id = RoleWGData.Instance:GetCurServerId()
    local role_name = RoleWGData.Instance:GetAttr("name")
    local desc_content = string.format(data.broadcast_content, server_id, role_name, data.need_rmb_num)

    local rumor_data = {
        rumor_type = data.rumor_type,
        desc_rumors_content = desc_content,
        show_yoyo_tween = true,
    }
    self.br_specail_rumiors:SetData(rumor_data)

    local attr_list, cap = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(data, "attr_id", "attr_value")
    self.node_list.br_cap_value.text.text = cap

    self.node_list.desc_br_explain.text.text = string.format(Language.CustomizedRumors.BroadCastExplain, data.need_rmb_num)

    local remind_info = CustomizedRumorsWGData.Instance:GetBroadCastGradeRemind(data.grade)
    self.node_list.btn_br_rxchange_remind:CustomSetActive(remind_info.remind)
end

function CustomizedRumorsView:BroadcastOnFlush()
    local nav_data_list = CustomizedRumorsWGData.Instance:GetRumorBRNavDataList()
    self.broadcast_left_list:SetDataList(nav_data_list)
    self.broadcast_left_list:JumpToIndex(self:GetBRRumorsListDefaultSelectIndex(nav_data_list))
    self.br_rumors_list:SetDataList(nav_data_list)
end

function CustomizedRumorsView:GetBRRumorsListDefaultSelectIndex(nav_data_list)
    if self.cur_select_grade > 0 then
        local remind_info = CustomizedRumorsWGData.Instance:GetBroadCastGradeRemind(self.cur_select_grade)
        if remind_info.remind then
            return self.cur_select_grade
        end
    end

    for k, v in pairs(nav_data_list) do
        local remind_info = CustomizedRumorsWGData.Instance:GetBroadCastGradeRemind(v.grade)

        if remind_info.remind then
            return v.grade
        end
    end

    return self.cur_select_grade > 0 and self.cur_select_grade or 1
end

function CustomizedRumorsView:OnClickBrExChange()
    CustomizedRumorsWGCtrl.Instance:OpenRumorsExchangeView(self.br_select_rumors_data.grade)
end

-------------------------------BRRumorslistItemRender----------------------------
BRRumorslistItemRender = BRRumorslistItemRender or BaseClass(BaseRender)

function BRRumorslistItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_name.text.text = self.data.broadcast_name

    local bg_bundle, bg_asset = ResPath.GetCustomizedRumorsImg(self.data.state_bg)
    self.node_list.bg.image:LoadSprite(bg_bundle, bg_asset, function ()
        self.node_list.bg.image:SetNativeSize()
    end)

    local cur_rmb_num = CustomizedRumorsWGData.Instance:GetRumorRealRecharge()
    local is_active = cur_rmb_num >= self.data.need_rmb_num
    local desc_id = is_active and 1 or 0
    self.node_list.desc_content.text.text = Language.CustomizedRumors.BroadCastState[desc_id]
end

BRRumorLeftItemRender = BRRumorLeftItemRender or BaseClass(BaseRender)

function BRRumorLeftItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_name.text.text = self.data.broadcast_name
    local cur_rmb_num = CustomizedRumorsWGData.Instance:GetRumorRealRecharge()
    self.node_list.desc_content.text.text = string.format(self.data.unlock_desc, cur_rmb_num, self.data.need_rmb_num)
    local unlock = cur_rmb_num >= self.data.need_rmb_num
    self.node_list.flag_lock:CustomSetActive(not unlock)
    self.node_list.flag_unlock:CustomSetActive(unlock)

    local remind_info = CustomizedRumorsWGData.Instance:GetBroadCastGradeRemind(self.data.grade)
    self.node_list.remind:CustomSetActive(remind_info.remind)
end

function BRRumorLeftItemRender:OnSelectChange(is_select)
    self.node_list.bg_hl:CustomSetActive(is_select)
end