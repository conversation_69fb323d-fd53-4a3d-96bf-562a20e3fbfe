XiuZhenDaLianView = XiuZhenDaLianView or BaseClass(SafeBaseView)

function XiuZhenDaLianView:__init()
    self.view_layer = UiLayer.Pop

    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/xiuzhen_road_ui_prefab", "layout_xiuzhen_dalian")
end

function XiuZhenDaLianView:ReleaseCallBack()
    if self.reward_list then
        for k, v in pairs(self.reward_list) do
            if v then
                v:DeleteMe()
            end
        end
        self.reward_list = {}
    end

    if CountDownManager.Instance:HasCountDown("xiuzhe_act_end_time") then
        CountDownManager.Instance:RemoveCountDown("xiuzhe_act_end_time")
    end

    if self.display_model then
        self.display_model:DeleteMe()
        self.display_model = nil
    end

    CountDownManager.Instance:RemoveCountDown("xz_dalian_down_time")
end

function XiuZhenDaLianView:CloseCallBack()
    if self.node_list.not_tip_toggle then
        local is_not_tip = self.node_list.not_tip_toggle.toggle.isOn
        if is_not_tip then
            local uuid = RoleWGData.Instance:GetUUid()
            local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "xiuzhen_dalian_flag")
            PlayerPrefsUtil.SetInt(key, 1)
        end
    end
end

function XiuZhenDaLianView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.OnClickGotoBtn, self))
    self.reward_list = {}
    local dasdal_reward_item = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuOtherCfg().dasdal_reward_item
    if dasdal_reward_item then
        for k, v in pairs(dasdal_reward_item) do
            self.reward_list[k] = ItemCell.New(self.node_list.item_list)
            self.reward_list[k]:SetData(dasdal_reward_item[k])
        end
    end

    self:InitModel()
end

function XiuZhenDaLianView:OnFlush()
    self:RefreshView()
    self:AutoCloseView()
end

function XiuZhenDaLianView:OnClickGotoBtn()
    self:Close()
    ViewManager.Instance:Open(GuideModuleName.XiuZhenRoadView)
end

function XiuZhenDaLianView:InitModel()
    local other_cfg = XiuZhenRoadWGData.Instance:GetXiuZhenZhiLuOtherCfg()
    if nil == self.display_model then
        self.display_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["ph_bianshen_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.display_model:SetRenderTexUI3DModel(display_data)
        -- self.display_model:SetUI3DModel(self.node_list["ph_bianshen_display"].transform,
        --     self.node_list["ph_bianshen_display"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
    end

    if self.display_model then
        local is_weapon_anim = 0
        local bundle, asset = ResPath.GetBianShenModel(other_cfg.pet_id) --ResPath.GetPetModel(pet_res_id)
        if not other_cfg.index then return end
        self.display_model:SetTianShenModel(other_cfg.pet_id, other_cfg.index, false, nil, SceneObjAnimator.Rest)
    end
end

function XiuZhenDaLianView:RefreshView()
    local end_time = XiuZhenRoadWGData.Instance:GetActivityEndTime()

    if end_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
        self:UpdataEndTime(TimeWGCtrl.Instance:GetServerTime(), end_time)
        if CountDownManager.Instance:HasCountDown("xiuzhe_act_end_time") then
            CountDownManager.Instance:RemoveCountDown("xiuzhe_act_end_time")
        end
        CountDownManager.Instance:AddCountDown("xiuzhe_act_end_time", BindTool.Bind1(self.UpdataEndTime, self),
            BindTool.Bind1(self.EndTimeCallBack, self), end_time, nil, 1)
    end
end

function XiuZhenDaLianView:UpdataEndTime(elapse_time, total_time)
    local time = total_time - elapse_time
    local format_time = TimeUtil.Format2TableDHMS(time)
    local str = ""

    if format_time.day > 0 then
        str = string.format(Language.XiuZhenRoad.ActivityEndDay[1], format_time.day)
    elseif format_time.hour > 0 then
        str = string.format(Language.XiuZhenRoad.ActivityEndDay[2], format_time.hour, format_time.min)
    elseif format_time.min > 0 then
        str = string.format(Language.XiuZhenRoad.ActivityEndDay[3], format_time.min)
    else
        str = string.format(Language.XiuZhenRoad.ActivityEndDay[4], format_time.s)
    end
    self.node_list.activity_time.text.text = str
end

function XiuZhenDaLianView:EndTimeCallBack()
    self.node_list["activity_time"].text.text = ""
    self:Close()
end

function XiuZhenDaLianView:AutoCloseView()
    CountDownManager.Instance:RemoveCountDown("xz_dalian_down_time")
    self.node_list.close_tip.text.text = string.format(Language.TianShenRoad.DaLianStr3, 30)
    CountDownManager.Instance:AddCountDown("xz_dalian_down_time",
        function(elapse_time, total_time)
            self.node_list.close_tip.text.text = string.format(Language.TianShenRoad.DaLianStr3,
                math.ceil(total_time - elapse_time))
        end,
        function()
            self:Close()
        end, nil, 30, 1)
end
