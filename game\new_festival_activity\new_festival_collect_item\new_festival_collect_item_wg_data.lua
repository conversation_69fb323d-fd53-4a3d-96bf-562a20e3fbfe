NewFestivalCollectItemWGData = NewFestivalCollectItemWGData or BaseClass()
NewFestivalCollectItemWGData.REWARD_TYPE = {
    Person = 1,
    JieYi = 2,
}
function NewFestivalCollectItemWGData:__init()
	if NewFestivalCollectItemWGData.Instance then
		error("[NewFestivalCollectItemWGData] Attempt to create singleton twice!")
		return
	end

    NewFestivalCollectItemWGData.Instance = self

    self:InitParam()
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.NewFestivalCollectItem, BindTool.Bind(self.GetNewJRCollectItemRemind, self))
end

function NewFestivalCollectItemWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.NewFestivalCollectItem)
    NewFestivalCollectItemWGData.Instance = nil
end

function NewFestivalCollectItemWGData:InitParam()
    self.save_model_list = {}

    self.personal_item_count = 0				-- 个人奖励道具数量
	self.jieyi_item_count = 0					-- 结义奖励道具数量
	self.personal_reward_flag = {}				-- 个人奖励标记
	self.jieyi_reward_flag = {}					-- 结义奖励标记
end

function NewFestivalCollectItemWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("new_festival_activity_item_collect_config_auto")
    self.grade_cfg = cfg.open_day
    self.self_reward_cfg = ListToMap(cfg.personal_reward, "grade", "seq")
    self.jieyi_reward_cfg = ListToMap(cfg.jieyi_reward, "grade", "seq")
    self.model_cfg = ListToMap(cfg.model_show, "grade")
end

function NewFestivalCollectItemWGData:GetNewJRCollectItemRemind()
    for k, v in pairs(NewFestivalCollectItemWGData.REWARD_TYPE) do
        if self:GetRewardRemindByType(v) then
            return 1
        end
    end

    return 0
end

function NewFestivalCollectItemWGData:GetRewardRemindByType(reward_type)
    local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.NEW_JRHD_JFSC)
    if not act_is_open then
        return false
    end

    local reward_cfg = self:GetRewardCfgByType(reward_type)
    local item_num = self:GetItemCountByType(reward_type)
    for k, v in pairs(reward_cfg) do
        local flag = self:GetRewardFlagByTypeAndSeq(reward_type, v.seq)
        if flag == 0 and item_num >= v.need_num then
            return true
        end
    end

    return false
end

--------------协议相关start
function NewFestivalCollectItemWGData:SetNewJRCollectItemAllInfo(protocol)
    self.personal_item_count = protocol.personal_item_count
	self.jieyi_item_count = protocol.jieyi_item_count
	self.personal_reward_flag = bit:d2b_l2h(protocol.personal_reward_flag, nil, true)
	self.jieyi_reward_flag = bit:d2b_l2h(protocol.jieyi_reward_flag, nil, true)
end

function NewFestivalCollectItemWGData:SetNewJRCollectItemCountInfo(protocol)
    self.personal_item_count = protocol.personal_item_count
	self.jieyi_item_count = protocol.jieyi_item_count
end

function NewFestivalCollectItemWGData:GetItemCountByType(reward_type)
    if reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.Person then
        return self.personal_item_count
    elseif reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.JieYi then
        return self.jieyi_item_count
    end

    return 0
end

function NewFestivalCollectItemWGData:GetRewardFlagByTypeAndSeq(reward_type, seq)
    if reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.Person then
        return self.personal_reward_flag[seq] or 0
    elseif reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.JieYi then
        return self.jieyi_reward_flag[seq] or 0
    end

    return 0
end

--------------协议相关end

function NewFestivalCollectItemWGData:GetCurGrade()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    for k, v in pairs(self.grade_cfg) do
        if open_day >= v.start_day and open_day <= v.end_day then
            return v.grade
        end
    end

    return 0
end

-- 无排序，直接获取配置表
function NewFestivalCollectItemWGData:GetRewardCfgByType(reward_type)
    local cur_grade = self:GetCurGrade()
    if reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.Person then
        return self.self_reward_cfg[cur_grade] or {}
    elseif reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.JieYi then
        return self.jieyi_reward_cfg[cur_grade] or {}
    end

    return {}
end

-- 有排序，用于界面显示
function NewFestivalCollectItemWGData:GetRewardListByType(reward_type)
    local cur_grade = self:GetCurGrade()
    local reward_list = {}
    local reward_cfg
    if reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.Person then
        reward_cfg = self.self_reward_cfg[cur_grade]
    elseif reward_type == NewFestivalCollectItemWGData.REWARD_TYPE.JieYi then
        reward_cfg = self.jieyi_reward_cfg[cur_grade]
    end

    if reward_cfg then
        for k, v in pairs(reward_cfg) do
            table.insert(reward_list, v)
        end

        table.sort(reward_list, function(a, b)
            local item_count = self:GetItemCountByType(reward_type)
            local a_sort = 10000 + a.seq
            local b_sort = 10000 + b.seq
            local a_is_get = self:GetRewardFlagByTypeAndSeq(reward_type, a.seq) == 1
            a_sort = a_is_get and a_sort or a_sort + 1000
            if not a_is_get then
                a_sort = item_count >= a.need_num and a_sort + 100 or a_sort
            end

            local b_is_get = self:GetRewardFlagByTypeAndSeq(reward_type, b.seq) == 1
            b_sort = b_is_get and b_sort or b_sort + 1000
            if not b_is_get then
                b_sort = item_count >= b.need_num and b_sort + 100 or b_sort
            end

            return a_sort > b_sort
        end)
    end

    return reward_list
end

function NewFestivalCollectItemWGData:GetModelData()
    local cur_grade = self:GetCurGrade()
    if self.save_model_list[cur_grade] then
        return self.save_model_list[cur_grade]
    else
        self.save_model_list[cur_grade] = {}
    end

    local new_model_data = self:CreateModelData(cur_grade)
    if not IsEmptyTable(new_model_data) then
        self.save_model_list[cur_grade] = new_model_data
    end

    return new_model_data
end

function NewFestivalCollectItemWGData:CreateModelData(grade)
    local show_data = self.model_cfg[grade]
    local new_mode_data = {}
    if not show_data then
        return new_mode_data
    end

    local display_data = {}
    local model_show_type = show_data.model_show_type

    if model_show_type == 0 then
        return new_mode_data
    end

    local model_show_itemid = show_data.model_show_itemid
    if model_show_itemid ~= 0 and model_show_itemid ~= "" then
        local split_list = Split(model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
            display_data.item_id = model_show_itemid
        end
    end

    display_data.should_ani = true
    display_data.hide_model_block = false
    display_data.bundle_name = show_data.model_bundle_name
    display_data.asset_name = show_data.model_asset_name
    display_data.render_type = model_show_type - 1
    display_data.need_wp_tween = true

    if model_show_type == 1 and show_data.model_bundle_name and show_data.model_bundle_name ~= "" then
        display_data.need_wp_tween = false
    end

    if show_data.display_root_width and show_data.display_root_width ~= "" then
        display_data.display_root_width = show_data.display_root_width
    end

    if show_data.display_root_height and show_data.display_root_height ~= "" then
        display_data.display_root_height = show_data.display_root_height
    end

    local transform_info = {}
    local pos_x, pos_y, pos_z = 0, 0, 0
    if show_data.display_pos and show_data.display_pos ~= "" then
        local pos_list = Split(show_data.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
        pos_z = tonumber(pos_list[3]) or pos_z
    end
    transform_info.pos_x = pos_x
    transform_info.pos_y = pos_y
    transform_info.pos_z = pos_z

    if show_data.model_pos and show_data.model_pos ~= "" then
        local pos_list = string.split(show_data.model_pos, "|")
        local posx = tonumber(pos_list[1]) or 0
        local posy = tonumber(pos_list[2]) or 0
        local posz = tonumber(pos_list[3]) or 0

        display_data.model_adjust_root_local_position = Vector3(posx, posy, posz)
    end

    local rot_x, rot_y, rot_z = 0, 0, 0
    if show_data.display_rotation and show_data.display_rotation ~= "" then
        local rot_list = Split(show_data.display_rotation, "|")
        rot_x = tonumber(rot_list[1]) or rot_x
        rot_y = tonumber(rot_list[2]) or rot_y
        rot_z = tonumber(rot_list[3]) or rot_z
    end

    transform_info.rot_x = rot_x
    transform_info.rot_y = rot_y
    transform_info.rot_z = rot_z
    display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)

    local scale = show_data.display_scale
    display_data.scale = (scale and scale ~= "" and scale > 0) and scale or 1
    display_data.model_adjust_root_local_scale = scale
    display_data.model_rt_type = ModelRTSCaleType.M

    new_mode_data = {}
    new_mode_data.display_data = display_data
    new_mode_data.transform_info = transform_info

    return new_mode_data
end