DrawGiftWGData = DrawGiftWGData or BaseClass()

function DrawGiftWGData:__init()
	if DrawGiftWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[DrawGiftWGData] attempt to create singleton twice!")
		return
	end

	DrawGiftWGData.Instance = self

	self:InitCfg()
    self.draw_gift_count_list = {}
	self.baodi_reward_list = {}
	self.draw_gift_show_reward_list = {}
end

function DrawGiftWGData:__delete()
	DrawGiftWGData.Instance = nil
end

function DrawGiftWGData:InitCfg()
    local cfg = ConfigManager.Instance:GetAutoConfig("draw_gift_display_auto")
    self.draw_gift_info_cfg = ListToMap(cfg.draw_gift_info, "draw_gift_id")
end

function DrawGiftWGData:IsDrawGift(item_id)
	return self.draw_gift_info_cfg[item_id] ~= nil
end

function DrawGiftWGData:GetDrawGiftCfgById(item_id)
    return self.draw_gift_info_cfg[item_id]
end

function DrawGiftWGData:SetAllDrawGiftInfo(protocol)
    self.draw_gift_count_list = protocol.draw_gift_count_list
end

function DrawGiftWGData:SetSingleDrawGiftInfo(protocol)
    local data = protocol.change_data
    self.draw_gift_count_list[data.item_id] = data.count
end

function DrawGiftWGData:GetDrawGiftCountById(item_id)
	return self.draw_gift_count_list[item_id] or 0
end

function DrawGiftWGData:GetDrawGiftBaoDiRewardInfo(item_id)
	if not self.baodi_reward_list[item_id] then
		local baodi_reward_list = {}
		local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
		if item_cfg == nil then
			return baodi_reward_list
		end
	
		if not item_cfg.guarantee then
			return baodi_reward_list
		end

		local t_list = Split(item_cfg.guarantee, "#")
		for k, v in ipairs(t_list) do
			local data = {}
			local info_list = Split(v, "|")
			data.need_count = tonumber(info_list[1]) or 0
			data.item_id = tonumber(info_list[2]) or 0
			data.is_bind = tonumber(info_list[3]) or 0
			data.num = tonumber(info_list[4]) or 0
			data.gift_id = item_id
			table.insert(baodi_reward_list, data)
		end

		if not IsEmptyTable(baodi_reward_list) then
			table.sort(baodi_reward_list, SortTools.KeyLowerSorters("need_count", "item_id"))
		end

		self.baodi_reward_list[item_id] = baodi_reward_list
	end

	return self.baodi_reward_list[item_id]
end

function DrawGiftWGData:GetDrawGiftBaoDiNextNeedCount(item_id)
	if not self.baodi_reward_list[item_id] then
		return 0
	end

	local draw_count = self:GetDrawGiftCountById(item_id)
	for k, v in ipairs(self.baodi_reward_list[item_id]) do
		if v.need_count > draw_count then
			return v.need_count, k
		end
	end

	return 0
end

function DrawGiftWGData:GetDrawGiftShowRewardList(item_id)
	if not self.draw_gift_show_reward_list[item_id] then
		local reward_list = {}
		if not self.draw_gift_info_cfg[item_id] then
			return reward_list
		end

		local cfg = self.draw_gift_info_cfg[item_id]
		local t_list = Split(cfg.show_reward_info, "#")
		for k, v in ipairs(t_list) do
			local data = {}
			local info_list = Split(v, "|")
			data.prob = tonumber(info_list[1]) or 0
			data.item_id = tonumber(info_list[2]) or 0
			data.num = tonumber(info_list[3]) or 0
			data.is_bind = tonumber(info_list[4]) or 0
			table.insert(reward_list, data)
		end

		self.draw_gift_show_reward_list[item_id] = reward_list
	end

	return self.draw_gift_show_reward_list[item_id]
end