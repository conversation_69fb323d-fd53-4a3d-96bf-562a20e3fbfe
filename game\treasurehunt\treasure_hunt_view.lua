--合W6的寻宝 符文、装备、巅峰、至尊  --装备、巅峰、至尊通用一个逻辑
TreasureHuntView = TreasureHuntView or BaseClass(SafeBaseView)

TreasureHuntView.TabIndex = {
    Fuwen = TabIndex.treasurehunt_fuwen,    -- 符文
    Equipment = TabIndex.treasurehunt_equip, -- 装备
    DianFeng = TabIndex.treasurehunt_dianfeng, -- 巅峰
    ZhiZun = TabIndex.treasurehunt_zhizun,  -- 至尊
    Thunder = TabIndex.treasurehunt_thunder,  -- 雷法
}

TreasureHuntView.TreasureType = {
    Equipment = 1, -- 装备
    DianFeng = 2, -- 巅峰
    ZhiZun = 3, -- 至尊
    Fuwen = 4,
    Thunder = 5,
}

TreasureHuntView.IsQiZhenYiBaoType = {
    [TreasureHuntView.TreasureType.Equipment] = true, -- 装备
    [TreasureHuntView.TreasureType.DianFeng] = true, -- 巅峰
    [TreasureHuntView.TreasureType.ZhiZun] = true, -- 至尊
}


TreasureHuntView.TableIndexByMode = {
    [1] = TabIndex.treasurehunt_equip, -- 装备
    [2] = TabIndex.treasurehunt_dianfeng, -- 巅峰
    [3] = TabIndex.treasurehunt_zhizun, -- 至尊
}

--购买按钮上的标题资源名称
TreasureHuntView.BuyBtnMoneyTitleResTb = {
    [TabIndex.treasurehunt_equip] = {
        [1] = Language.TreasureHunt.XunBaoBtnText1[1], --1万
        [2] = Language.TreasureHunt.XunBaoBtnText1[2], --10万
        [3] = Language.TreasureHunt.XunBaoBtnText1[3], --50万
    },
    [TabIndex.treasurehunt_dianfeng] = {
        [1] = Language.TreasureHunt.XunBaoBtnText2[1], --2万
        [2] = Language.TreasureHunt.XunBaoBtnText2[2], --20万
        [3] = Language.TreasureHunt.XunBaoBtnText2[3], --100万
    },
    [TabIndex.treasurehunt_zhizun] = {
        [1] = Language.TreasureHunt.XunBaoBtnText3[1], --3万
        [2] = Language.TreasureHunt.XunBaoBtnText3[2], --30万
        [3] = Language.TreasureHunt.XunBaoBtnText3[3], --150万
    },
}

TreasureHuntView.BG_CFG = {
    [TabIndex.treasurehunt_fuwen]    = "a3_xb_bg_hy",
    [TabIndex.treasurehunt_equip]    = "a3_xb_bg_zb",
    [TabIndex.treasurehunt_dianfeng] = "a3_xb_bg_zb",
    [TabIndex.treasurehunt_zhizun]   = "a3_xb_bg_zb",
    [TabIndex.treasurehunt_thunder]   = "a3_xb_bg_lf",
}

TreasureHuntView.ZHANSHICELLNUM = 10
TreasureHuntView.REWARD_MAX_COUNT = 8

function TreasureHuntView:__init()
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.view_name = GuideModuleName.TreasureHunt
    self.default_index = TabIndex.treasurehunt_equip
    self:SetMaskBg()
    local bundle_name = "uis/view/treasurehunt_ui_prefab"
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(TreasureHuntView.TabIndex.Fuwen, bundle_name, "layout_fuwen_xunbao")
    self:AddViewResource(TreasureHuntView.TabIndex.Equipment, bundle_name, "layout_qizhenyibao")
    self:AddViewResource(TreasureHuntView.TabIndex.DianFeng, bundle_name, "layout_qizhenyibao")
    self:AddViewResource(TreasureHuntView.TabIndex.ZhiZun, bundle_name, "layout_qizhenyibao")
    self:AddViewResource(TreasureHuntView.TabIndex.ZhiZun, bundle_name, "layout_qizhenyibao")
    self:AddViewResource(TreasureHuntView.TabIndex.Thunder, bundle_name, "layout_thunder_xunbao")
    self:AddViewResource(0, bundle_name, "VerticalTabbar")
    self:AddViewResource(TreasureHuntView.TabIndex.Fuwen, bundle_name, "layout_fuwen_xunbao_of_entrance")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

    self.tab_sub = {}
    self.cur_show_index = 1
    self.delay_click_show = false

    self.remind_tab = {
        { RemindName.TreasureHunt_Equip },
        { RemindName.TreasureHunt_FuWen },
        { RemindName.TreasureHunt_DianFeng },
        { RemindName.TreasureHunt_Zhizun },
        { RemindName.TreasureHunt_Thunder },
    }
end

function TreasureHuntView:OpenCallBack()
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.UiXunBao, nil, true))
    TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_REQ_STORGE_INFO)

    if PlayerPrefsUtil.GetString("FirstOpenTreasureHuntView") ~= "FirstOpenTreasureHuntView" then
        PlayerPrefsUtil.SetString("FirstOpenTreasureHuntView", "FirstOpenTreasureHuntView")
        TalkCache.StopCurIndexAudio()
        AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Sound21, nil, true))
    end
end

function TreasureHuntView:CloseCallBack(is_all)
    if FunctionGuide.Instance:GetCurrentGuideName() == "TreasureHunt" then
		FunctionGuide.Instance:EndGuide()
	end
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.TreasureHunt, self.get_guide_ui_event)
    self.get_guide_ui_event = nil
end

function TreasureHuntView:LoadCallBack()
    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end

    if nil == self.tabbar then
        self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.TabbarLoadCallBack, self))
        self.tabbar:Init(Language.TreasureHunt.TabGrop, self.tab_sub, "uis/view/treasurehunt_ui_prefab", nil,
            self.remind_tab)
        self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
    end

    self.node_list.title_view_name.text.text = Language.ViewName.xunBao
    self.is_init_commonm_view = false
    self:BindMoneyEvents()

    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.TreasureHunt, self.get_guide_ui_event)
end

function TreasureHuntView:ReleaseCallBack()
    if nil ~= self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    self.delay_click_show = false
    self.first_open = false

    if self.show_model then
        self.show_model:DeleteMe()
        self.show_model = nil
    end

    if self.show_hunt_model_list then
        for k, v in pairs(self.show_hunt_model_list) do
            v:DeleteMe()
        end
        self.show_hunt_model_list = nil
    end

    if nil ~= self.week_target_list_view then
        self.week_target_list_view:DeleteMe()
        self.week_target_list_view = nil
    end

    -- if self.zhanshi_cell_list then
    --     for k,v in ipairs(self.zhanshi_cell_list) do
    --         if v then
    --             v:DeleteMe()
    --         end
    --     end
    -- end

    -- if self.cambered_list then
    -- 	self.cambered_list:DeleteMe()
    -- 	self.cambered_list = nil
    -- end

    if self.qzyb_reward_item_list then
        for k, v in ipairs(self.qzyb_reward_item_list) do
            if v then
                v:DeleteMe()
            end
        end

        self.qzyb_reward_item_list = nil
    end

    if self.door_tween_left then
        self.door_tween_left:Kill()
        self.door_tween_left = nil
    end

    if self.door_tween_right then
        self.door_tween_right:Kill()
        self.door_tween_right = nil
    end

    if self.delay_next_show_timer then
        GlobalTimerQuest:CancelQuest(self.delay_next_show_timer)
        self.delay_next_show_timer = nil
    end

    -- if self.treasure_hunt_boss_list then
    --     self.treasure_hunt_boss_list:DeleteMe()
    --     self.treasure_hunt_boss_list = nil
    -- end

    if TreasureHuntWGData.Instance then
        TreasureHuntWGData.Instance:SetOpenSelectEffect(false)
    end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    -- self.zhanshi_cell_list = nil
    self.is_init_commonm_view = nil

    self:ReleaseMingwen()
    self:ReleaseThunder()
end

function TreasureHuntView:LoadIndexCallBack(index)
    if index == TreasureHuntView.TabIndex.Fuwen then
        self:InitMingwenView()
    elseif index == TreasureHuntView.TabIndex.Equipment then
        self:InitXunBaoModel()
        self:InitCommonView()
    elseif index == TreasureHuntView.TabIndex.DianFeng then
        self:InitXunBaoModel()
        self:InitCommonView()
    elseif index == TreasureHuntView.TabIndex.ZhiZun then
        self:InitXunBaoModel()
        self:InitCommonView()
    elseif index == TreasureHuntView.TabIndex.Thunder then
        self:InitThunderView()
    end
end

function TreasureHuntView:ShowIndexCallBack(index)
    TreasureHuntWGData.Instance:SetCurShowIndex(index)
    --self:FlushMoneyBar()
    --TreasureHuntWGCtrl.Instance:DoSendBaseInfo()
    local TreasureType = TreasureHuntView.GetTypeByShowIndex(index)
    -- if TreasureType then
    --     TreasureHuntWGCtrl.Instance:DoOperation(TreasureHuntWGData.CHESTSHOP_REQ.CHESTSHOP_RECORD_REQ, TreasureType - 1)
    --     self.node_list.big_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("xb_beijing"..TreasureType))
    -- end
    self.is_change_index = true
    --self.node_list["btn_rule"]:SetActive(true)
    if index == TreasureHuntView.TabIndex.Fuwen then
        self:FlushMingwenView()
        TreasureHuntWGData.Instance:SetOpenFlag(4)
    elseif index == TreasureHuntView.TabIndex.Equipment then
        self:FlushCommonView(index)
        self.cur_show_index = 1
        TreasureHuntWGData.Instance:SetOpenFlag(TreasureType)
    elseif index == TreasureHuntView.TabIndex.DianFeng then
        self:FlushCommonView(index)
        self.cur_show_index = 1
        TreasureHuntWGData.Instance:SetOpenFlag(TreasureType)
    elseif index == TreasureHuntView.TabIndex.ZhiZun then
        self:FlushCommonView(index)
        self.cur_show_index = 1
        TreasureHuntWGData.Instance:SetOpenFlag(TreasureType)
    elseif index == TreasureHuntView.TabIndex.Thunder then
        self:FlushThunderView()
    end
    if TreasureType ~= TreasureHuntView.TreasureType.Fuwen then
        --self:OnToggleBtn(1)
    end

    local bg_asset = TreasureHuntView.BG_CFG[index]
    if bg_asset then
        local bundle, asset = ResPath.GetRawImagesJPG(bg_asset)
        self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
            self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
        end)
    end

    self:FlushSkipSpine()
end

function TreasureHuntView:OnDisplayClick()
    -- local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    -- local show_model_data = TreasureHuntWGData.Instance:GetHuntCfg(treasure_type) or {}
    -- local item_id = show_model_data.model_item_id or 0
    -- TipWGCtrl.Instance:OpenItem({ item_id = item_id })
end

function TreasureHuntView:InitXunBaoModel()
    if self.show_model == nil then
        self.show_model = OperationActRender.New(self.node_list["display_pos"])
        self.show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end
    if self.node_list.EventTriggerListener then
        self.node_list.EventTriggerListener.event_trigger_listener:AddPointerClickListener(BindTool.Bind(
        self.OnDisplayClick, self))
    end
end

function TreasureHuntView:TabbarLoadCallBack()
    FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.TreasureHunt, self.tabbar)
end

-- function TreasureHuntView:CreateZhanShiCell()
--     if self.zhanshi_cell_list == nil then
--         self.zhanshi_cell_list = {}
--     end
--     for i = 1, TreasureHuntView.ZHANSHICELLNUM do
--         if self.node_list["ph_zhanshi_cell"..i] then
--             self.zhanshi_cell_list[i] = ItemCell.New(self.node_list["ph_zhanshi_cell"..i])
--         end
--     end
-- end

function TreasureHuntView:CreateRewardShowListItem()
    -- local cambered_list_data = {
    -- 	item_render = THZhuanShiCellRender,
    -- 	asset_bundle = "uis/view/treasurehunt_ui_prefab",
    -- 	asset_name = "ph_zhuanshi_cell",
    -- 	scroll_list = self.node_list.item_root,
    -- 	center_x = 0, center_y = -20,
    -- 	radius_x = 410, radius_y = 210,-- x 椭圆半长轴,y 椭圆半短轴
    -- 	angle_delta = Mathf.PI / 4,
    -- 	origin_rotation = Mathf.PI,
    -- 	is_drag_horizontal = true,
    -- 	scale_min = 0.8,			-- 最小缩放比例
    -- 	need_change_scale = true,
    -- 	is_assist = true,
    -- 	behind = self.node_list.behind,
    -- 	front = self.node_list.front,
    -- }

    -- self.cambered_list = CamberedList.New(cambered_list_data)
    -- self.cambered_list:CreateCellList(TreasureHuntView.REWARD_MAX_COUNT)
    -- self.cambered_list:AutoMove()

    if not self.qzyb_reward_item_list then
        self.qzyb_reward_item_list = {}

        for i = 1, 8 do
            local item = ItemCell.New(self.node_list["qzyb_reward_item" .. i])
            item:SetIsUseRoundQualityBg(true)
            item:SetCellBgEnabled(false)
            -- item:SetSelectEffectImageRes(ResPath.GetTreasurehuntIcon("a3_xb_dj_wk"))
            -- item:SetSelectEffect(true)
            self.qzyb_reward_item_list[i] = item
        end
    end
end

function TreasureHuntView:InitCommonView()
    if self.is_init_commonm_view then
        return
    end

    self.is_init_commonm_view = true
    self.node_list.btn_zhuangbei_chu.button:AddClickListener(BindTool.Bind(self.OnClickBtn, self, 1))
    self.node_list.btn_zhuangbei_gao.button:AddClickListener(BindTool.Bind(self.OnClickBtn, self, 2))
    self.node_list.btn_zhuangbei_zhi.button:AddClickListener(BindTool.Bind(self.OnClickBtn, self, 3))
    -- self.node_list.btn_zhanshi_1.button:AddClickListener(BindTool.Bind(self.OnToggleBtn, self, 1))
    -- self.node_list.btn_zhanshi_2.button:AddClickListener(BindTool.Bind(self.OnToggleBtn, self, 2))
    --self.node_list.btn_rule.button:AddClickListener(BindTool.Bind(self.OnClickRule, self)) --规则

    self.node_list.btn_store.button:AddClickListener(BindTool.Bind(self.OnClickStore, self, 3))                --积分商城
    self.node_list.btn_open_cangku.button:AddClickListener(BindTool.Bind(self.OnClickStorage, self))           --仓库
    self.node_list.gailv_btn.button:AddClickListener(BindTool.Bind(self.OnClickGaiLvBtn, self))                --概率显示
    self.node_list.zhufuzhi_btn.button:AddClickListener(BindTool.Bind(self.OnClickzhufu, self))                --z祝福显示
    self.node_list.btn_hunt_record.button:AddClickListener(BindTool.Bind(self.OnClickOpenHuntRecord, self))    --寻宝记录
    self.node_list.btn_xukongliefeng.button:AddClickListener(BindTool.Bind(self.OnClickXuKongLieFeng, self, 3)) --积分商城
	FunOpen.Instance:RegisterFunUi(FunName.TreasurehuntBoss, self.node_list["btn_xukongliefeng"])
    --self.node_list.show_model_left_btn.button:AddClickListener(BindTool.Bind(self.OnClickNextShowModel,self,-1))
    --self.node_list.show_model_right_btn.button:AddClickListener(BindTool.Bind(self.OnClickNextShowModel,self,1))
    self.first_open = true
    for i = 1, 3 do
        self.node_list["img_zbitem_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickCommonKey, self))
    end

    self.node_list["skip_spine_check"].button:AddClickListener(BindTool.Bind(self.OnClickSkipSpine, self))

    --    self.node_list.flush_desc.text.text = Language.TreasureHunt.BossFlushText2
end

function TreasureHuntView:OnClickCommonKey()
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    if treasure_type == nil then
        return
    end
    local key_id = self.keys_list[treasure_type]
    TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = key_id })
end

function TreasureHuntView:OnClickRule()
    local index = self:GetShowIndex()
    local indx = math.floor(index / 10)
    RuleTip.Instance:SetContent(Language.TreasureHunt["RuleDes" .. indx], Language.TreasureHunt.TabGrop[indx])
end

function TreasureHuntView:FlushBtnRemind(index)
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(index)
    if treasure_type then
        for i = 1, 3 do
            self.node_list["btn_remind" .. i]:SetActive(TreasureHuntWGData.Instance:GetBtnRemind(treasure_type, i) == 1)
        end
    end
end

function TreasureHuntView:FlushCommonView(index)
    
    --刷新消耗物品
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(index)
    -- 大奖名字显示
    local pool_level = TreasureHuntWGData.Instance:GetPoolLevelByType(treasure_type)
    
    self.node_list.text_name.text.text = string.format(Language.TreasureHunt.NameText[treasure_type], NumberToChinaNumber(pool_level))
    --特效显示隐藏
    local treasure_cfg = TreasureHuntWGData.Instance:GetTreasureCfgByType(treasure_type)
    local money_title_res_tb = TreasureHuntView.BuyBtnMoneyTitleResTb[index]
    for i = 1, 3 do
        local bundle, asset = ResPath.GetItem(treasure_cfg[i].stuff_id)
        self.node_list["img_zbitem_" .. i].image:LoadSprite(bundle, asset, function ()
            self.node_list["img_zbitem_" .. i].image:SetNativeSize()
        end)
        local has_num = ItemWGData.Instance:GetItemNumInBagById(treasure_cfg[i].stuff_id) --拥有的数量

        -- vip特权处理
        local cfg_need_num = treasure_cfg[i].stuff_num
        local vip_mode_cfg = TreasureHuntWGData.Instance:GetTreasureVipModeCfgByType(treasure_cfg[i].mode)
        local need_num = not IsEmptyTable(vip_mode_cfg) and vip_mode_cfg.vip_stuff_num or cfg_need_num
        local color = has_num >= need_num and COLOR3B.DEFAULT_NUM or "#ff9393"
        self.node_list["lbl_glodzb_" .. i].text.text = ToColorStr(has_num .. "/" .. need_num, color)

        -- if not IsEmptyTable(vip_mode_cfg) and self.node_list.zhekou_text then
        --     self.node_list.zhekou_text.text.text = vip_mode_cfg.zhekou
        -- end

        if not IsEmptyTable(money_title_res_tb) and money_title_res_tb[i] then
            self.node_list["bug_money_img" .. i].text.text = money_title_res_tb[i]
        end
    end

    self:FlushBtnRemind(index)
    --刷新展示物品
    local cfg = TreasureHuntWGData.Instance:GetZhanshiCfgByType(index)
    if IsEmptyTable(cfg) then
        print_error("GetZhanshiCfgByType error:", index)
    else
        -- if not self.zhanshi_cell_list then
        --     self:CreateZhanShiCell()
        -- end
        -- for k,v in ipairs(self.zhanshi_cell_list) do
        --     if cfg[k] then
        --         v:SetData({item_id = cfg[k].rare_item_id})
        --     end
        -- end


        -- if not self.cambered_list then
        --     self:CreateRewardShowListItem()
        -- end

        -- local btn_item_list = self.cambered_list:GetRenderList()

        -- for k, item_cell in ipairs(btn_item_list) do
        --     if cfg[k] then
        --         item_cell:SetData({item_id = cfg[k].rare_item_id})
        --     end
        -- end

        if not self.qzyb_reward_item_list then
            self:CreateRewardShowListItem()
        end

        for i = 1, 8 do
            if cfg[i] and self.qzyb_reward_item_list[i] then
                self.qzyb_reward_item_list[i]:SetData({ item_id = cfg[i].rare_item_id })
            end
        end

        -- local btn_item_list = self.cambered_list:GetRenderList()
        -- for k, item_cell in ipairs(btn_item_list) do
        --     if cfg[k] and self.qzyb_reward_item_list[k] then
        --         self.qzyb_reward_item_list[k]:SetData({item_id = cfg[k].rare_item_id})
        --     end
        -- end
    end
    self.node_list["img_btn_openzb_exchange_remind"]:SetActive(TreasureHuntWGData.Instance:ConvertRemind(treasure_type))
    -- local desc_t = TreasureBossWGData.Instance:GetBestFightTimeTreasureSrc(2)
    -- self.node_list.flush_desc.text.text = desc_t[1] or ""
    -- self.node_list.flush_next_desc.text.text = desc_t[2] or ""
    -- self:FlushRecordView()
    self:FlushBaodiInfo()
    self:FlushBaseInfoView()
    self:FlushStorageRemind()
    self:FlushShowModel()
    -- self:FlushBossList()
end

function TreasureHuntView:FlushBaodiInfo()
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    if treasure_type == nil then
        return
    end
    local cur_times, total = TreasureHuntWGData.Instance:GetBaodiTimes(treasure_type)

    self.node_list.baodi_times.text.text = string.format(Language.TreasureHunt.BaoDiTimesDesc, cur_times, total)    -- cur_times .. "/" .. total

    self.node_list.common_slider_image.slider.value = cur_times / total

    self.node_list.hunt_normal_des.text.text = string.format(Language.TreasureHunt.TreasureHuntBaoDi, total - cur_times)
    self.node_list.bd_raw.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_xb_txt_" .. treasure_type))
    -- self.node_list.desc_raw.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a2_xb_desc_" .. treasure_type))

    local data_list = TreasureHuntWGData.Instance:GetHuntBossDataListByType(treasure_type)
    local xklf_data = data_list[1]
    local draw_count = TreasureHuntWGData.Instance:GetHuntTotalCountByType(xklf_data.chestshop_type)
    local open_count = xklf_data.chestshop_num
    self.node_list.xklf_info:CustomSetActive(draw_count < open_count)
    self.node_list.btn_xukongliefeng_effect:CustomSetActive(draw_count >= open_count)

    if draw_count < open_count then
        self.node_list.desc_xklf_info.text.text = string.format(Language.TreasureHunt.TreasureDrawTime,ToColorStr(draw_count,COLOR3B.RED).."/"..open_count)
    end
end

-- function TreasureHuntView:TryFlushBossList()
--     if self.node_list and self.node_list.boss_list then
--         self:FlushBossList()
--     end
-- end

-- function TreasureHuntView:FlushBossList()
--     if not self.treasure_hunt_boss_list then
--         self.treasure_hunt_boss_list = AsyncListView.New(TreasureHunBossCell, self.node_list.boss_list)
--     end

--     local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
--     local data_list = TreasureHuntWGData.Instance:GetHuntBossDataListByType(treasure_type)
--     self.treasure_hunt_boss_list:SetDataList(data_list)

--     local max_kill_time = TreasureBossWGData.Instance:GetMaxKillTimes()
--     local left_kill_time = max_kill_time - TreasureBossWGData.Instance:GetHaveKillTimes()
--     left_kill_time = left_kill_time >= 0 and left_kill_time or 0

--     local max_join_time = TreasureBossWGData.Instance:GetMaxJoinTimes()
--     local left_join_time = max_join_time - TreasureBossWGData.Instance:GetHaveJoinTimes()
--     left_join_time = left_join_time >= 0 and left_join_time or 0

--     local color1 = left_join_time > 0 and COLOR3B.GREEN or COLOR3B.PINK
--     local color2 = left_kill_time > 0 and COLOR3B.GREEN or COLOR3B.PINK
--     self.node_list.kill_text.text.text = string.format(Language.TreasureHunt.BossKillText,COLOR3B.GREEN,ToColorStr(left_kill_time,color2).."/"..max_kill_time)
--     self.node_list.join_text.text.text = string.format(Language.TreasureHunt.BossJoinText,COLOR3B.GREEN,ToColorStr(left_join_time,color1).."/"..max_join_time)
-- end

function TreasureHuntView:OnFlush(param_t, index)
    if index == 0 then
        return
    end
    for k, v in pairs(param_t) do
        if "all" == k then
            if index == TreasureHuntView.TabIndex.Fuwen then
                self:FlushMingwenView()
            elseif index == TreasureHuntView.TabIndex.Equipment then
                self:FlushCommonView(index)
            elseif index == TreasureHuntView.TabIndex.DianFeng then
                self:FlushCommonView(index)
            elseif index == TreasureHuntView.TabIndex.ZhiZun then
                self:FlushCommonView(index)
            elseif index == TreasureHuntView.TabIndex.Thunder then
                self:FlushThunderView()
            end
            -- elseif "record" == k then
            --     self:FlushRecordView()
        elseif "base" == k then
            self:FlushBaseInfoView()
        elseif "storage" == k then
            self:FlushStorageRemind()
            -- elseif "boss_info_change" == k then
            --     self:TryFlushBossList()
        end
    end
end

function TreasureHuntView.GetTypeByShowIndex(index)
    if index == TreasureHuntView.TabIndex.Equipment then
        return TreasureHuntView.TreasureType.Equipment
    elseif index == TreasureHuntView.TabIndex.DianFeng then
        return TreasureHuntView.TreasureType.DianFeng
    elseif index == TreasureHuntView.TabIndex.ZhiZun then
        return TreasureHuntView.TreasureType.ZhiZun
    elseif index == TreasureHuntView.TabIndex.Fuwen then
        return TreasureHuntView.TreasureType.Fuwen
    elseif index == TreasureHuntView.TabIndex.Thunder then
        return TreasureHuntView.TreasureType.Thunder
    end
end

function TreasureHuntView:FlushStorageRemind()
    local rmind = TreasureHuntWGData.Instance:GetStorageBtnRemind()
    if self.node_list.img_btn_openzb_cangku_remind then
        self.node_list.img_btn_openzb_cangku_remind:SetActive(rmind == 1)
    end
end

function TreasureHuntView:OnClickBtn(btn_index)
    TreasureHuntWGCtrl.Instance:OnClickBtn(btn_index)
end


function TreasureHuntView:GetBtnOneNode()
	if self.node_list.btn_zhuangbei_chu then
		return self.node_list.btn_zhuangbei_chu, BindTool.Bind(self.OnClickBtn, self, 1)
	end
end

function TreasureHuntView:GetGuideUiCallBack(ui_name, ui_param)
	if not self:IsLoaded() then
		return
	end

	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			self:ShowIndex(tab_index)
			return
		end
	elseif ui_name == GuideUIName.XunBaoChuJi then
		return self:GetBtnOneNode()
	elseif ui_name == GuideUIName.CloseBtn then
		return self.node_list.btn_close_window, BindTool.Bind(self.Close, self)
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

-- --index 1 全服 2个人
-- function TreasureHuntView:OnToggleBtn(index) --发送请求
--     self.record_index = index
--     if self.node_list.bg_HL_1 and self.node_list.bg_HL_2 then
--         self.node_list.bg_HL_1:SetActive(self.record_index == 1)
--         self.node_list.bg_HL_2:SetActive(self.record_index == 2)
--     end
--     self:FlushRecordView()
-- end

-- function TreasureHuntView:FlushRecordView()
-- if self.node_list.ph_xunbao_show_list == nil then
--     return
-- end
-- if self.record_list_view == nil then
--     self:CreateRecordList()
-- end

-- local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
-- if treasure_type == nil then
--     return
-- end
-- local data = TreasureHuntWGData.Instance:GetRecordViewByMode(self.record_index, treasure_type - 1)
-- if data then

--     if self.record_index == 1 then
--         self.record_list_view:SetDataList(data)
--     else
--         self.record_person_list_view:SetDataList(data)
--     end
-- end
-- self.node_list.all_no_record:SetActive(IsEmptyTable(data))

-- self.node_list.ph_xunbao_show_list:SetActive(self.record_index == 1)
-- self.node_list.ph_xunbao_person_list:SetActive(self.record_index ~= 1)

-- local data_miwen = TreasureHuntWGData.Instance:GetWorldBigRecord(treasure_type - 1)
-- if data_miwen then
--     self.miwen_list:SetDataList(data_miwen)
-- end
--self.node_list.miwen_no_record:SetActive(IsEmptyTable(data_miwen))
-- end

--刷新周奖励和积分
function TreasureHuntView:FlushBaseInfoView()
    if self.node_list.rich_jifenx_num == nil then
        return
    end
    -- if self.week_target_list_view == nil then
    --     self:CreateWeekRewardList()
    -- end
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    if not treasure_type then
        return
    end
    -- local data_list = TreasureHuntWGData.Instance:GetWeekRewardInfo(treasure_type)
    -- self.week_target_list_view:SetDataList(data_list)
    local data = TreasureHuntWGData.Instance:GetBaseData()
    local info = TreasureHuntWGData.Instance:GetConvertShopCfgByType(treasure_type - 1)
    local item_cfg = ItemWGData.Instance:GetItemConfig(info[1] and info[1].show_item)
    if item_cfg then
        self.node_list.rich_jifenx_num.text.text = string.format(Language.TreasureHunt.ScoreDes, item_cfg.name,
            data.chestshop_score_list[treasure_type] or 0)
    end
end

-- function TreasureHuntView:CreateWeekRewardList()
-- if self.node_list.week_target_list then
--     self.week_target_list_view = AsyncListView.New(RecordRewardItem, self.node_list.week_target_list)
-- end
-- end

-- function TreasureHuntView:CreateRecordList()
-- if self.node_list.ph_xunbao_show_list then
--     self.record_list_view = AsyncListView.New(RecordItem, self.node_list.ph_xunbao_show_list)
-- end
-- if self.node_list.ph_xunbao_person_list then
--     self.record_person_list_view = AsyncListView.New(RecordPersonItem, self.node_list.ph_xunbao_person_list)
-- end
-- if self.node_list.miwen_list_view then
--     self.miwen_list = AsyncListView.New(RecordMiwenItem, self.node_list.miwen_list_view)
-- end
-- end

function TreasureHuntView:OnClickStorage()
    TreasureHuntWGCtrl.Instance:OpenStroageView()
end

function TreasureHuntView:OnClickStore()
    TreasureHuntWGCtrl.Instance:OpenStoreView()
end

function TreasureHuntView:OnClickXuKongLieFeng()
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    TreasureHuntWGCtrl.Instance:OpenXuKongLieFengView(treasure_type)
end

function TreasureHuntView:OnClickGaiLvBtn()
    TreasureHuntWGCtrl.Instance:OpenProbabilityView()
end

function TreasureHuntView:OnClickzhufu()
    TreasureHuntWGCtrl.Instance:OpenBaodiTips()
end

function TreasureHuntView:OnClickOpenHuntRecord()
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    TreasureHuntWGCtrl.Instance:OpenTreasureRecordTip(treasure_type)
end

function TreasureHuntView:FlushShowModel()
    if not self.show_hunt_model_list then
        self.show_hunt_model_list = {}
        for i = 1, 2 do
            self.show_hunt_model_list[i] = ItemCell.New(self.node_list["hunt_model_cell" .. i])
            self.show_hunt_model_list[i]:SetIsUseRoundQualityBg(true)
            self.show_hunt_model_list[i]:SetCellBgEnabled(false)
            -- self.show_hunt_model_list[i]:SetSelectEffectImageRes(ResPath.GetTreasurehuntIcon("a3_xb_dj_wk"))
            -- self.show_hunt_model_list[i]:SetSelectEffect(true)
        end
    end

    local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    self:FlushModel()
    -- local show_model_data = TreasureHuntWGData.Instance:GetHuntCfg(treasure_type)
    -- if show_model_data then
    --     local data = {}
    --     data.item_id = show_model_data.model_item_id
    --     data.render_type = 0

    --     -- if show_model_data.model_pos ~= nil and show_model_data.model_pos ~= "" then
    --     --     local pos = Split(show_model_data.model_pos, "|")
    --     --     data.position = Vector3(tonumber(pos[1]), tonumber(pos[2]), tonumber(pos[3]))
    --     -- end

    --     -- if show_model_data.model_scale ~= nil and show_model_data.model_scale ~= "" then
    --     --     local scale = show_model_data.model_scale
    --     --     data.scale = Vector3(scale, scale, scale)
    --     -- end

    --     -- if show_model_data.model_rot ~= nil and show_model_data.model_rot ~= "" then
    --     --     local rota = Split(show_model_data.model_rot, "|")
    --     --     data.rotation = Quaternion.Euler(tonumber(rota[1]), tonumber(rota[2]), tonumber(rota[3]))
    --     -- end
    --     data.model_rt_type = ModelRTSCaleType.L
    --     data.event_trigger_listener_node = self.node_list["EventTriggerListener"]

    --     self.show_model:SetData(data)
    -- end

    local show_model_data2 = TreasureHuntWGData.Instance:GetHuntShowModelCfg(treasure_type)
    if self.show_hunt_model_list then
        for k, v in pairs(show_model_data2) do
            local temp = k == 1 and 1 or 2
            if self.show_hunt_model_list[temp] then
                self.show_hunt_model_list[temp]:SetData({ item_id = tonumber(v[2]) })
            end
        end
    end
end

function TreasureHuntView:FlushModel()
    local treasure_type = TreasureHuntView.GetTypeByShowIndex(self:GetShowIndex())
    local model_data = TreasureHuntWGData.Instance:GetShowModelDataByType(treasure_type)

    if IsEmptyTable(model_data) then
		return
	end

    local display_data = {}
	display_data.should_ani = true
	if model_data.model_show_itemid ~= 0 and model_data.model_show_itemid ~= "" then
		local split_list = string.split(model_data.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_data.model_show_itemid
		end
	end
	
	display_data.bundle_name = model_data["model_bundle_name"]
    display_data.asset_name = model_data["model_asset_name"]
    local model_show_type = tonumber(model_data["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1

	if model_data.model_pos and model_data.model_pos ~= "" then
		local pos_list = string.split(model_data.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

    local prof = RoleWGData.Instance:GetRoleProf()
	local sex = RoleWGData.Instance:GetRoleSex()

    local rotate_str = model_data["main_rot_" .. prof.."_"..sex] or model_data.model_rot
	if rotate_str and rotate_str ~= "" then
		local rot_list = string.split(rotate_str, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0

		display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_data.model_scale and model_data.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_data.model_scale
	end

    display_data.model_rt_type = ModelRTSCaleType.L
    self.show_model:SetData(display_data)
end

function TreasureHuntView:OnClickSkipSpine()
    local show_type = TreasureHuntWGData.Instance:GetCurShowType()
    local is_skip = TreasureHuntWGData.Instance:GetSkipSpineStatusByType(show_type)
    is_skip = not is_skip
    TreasureHuntWGData.Instance:SetSkipSpineStatusByType(show_type, is_skip)
    self:FlushSkipSpine()
end

function TreasureHuntView:FlushSkipSpine()
    local show_type = TreasureHuntWGData.Instance:GetCurShowType()
    
    if show_type and show_type > 0 and TreasureHuntView.IsQiZhenYiBaoType[show_type] then
        local is_skip = TreasureHuntWGData.Instance:GetSkipSpineStatusByType(show_type)
        self.node_list.skip_spine_yes:SetActive(is_skip)
    end
end

-- THZhuanShiCellRender = THZhuanShiCellRender or BaseClass(BaseRender)

-- function THZhuanShiCellRender:LoadCallBack()
--     if not self.item then
--         self.item = ItemCell.New(self.node_list.item_pos)
--     end
-- end

-- function THZhuanShiCellRender:__delete()
--     if self.item then
--         self.item:DeleteMe()
--         self.item = nil
--     end
-- end

-- function THZhuanShiCellRender:OnFlush()
--     if IsEmptyTable(self.data) then
--         return
--     end

--     self.item:SetData(self.data)
-- end