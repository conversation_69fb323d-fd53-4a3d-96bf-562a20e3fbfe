﻿//------------------------------------------------------------------------------
// This file is part of MistLand project in Nirvana.
// Copyright © 2016-2016 Nirvana Technology Co., Ltd.
// All Right Reserved.
//------------------------------------------------------------------------------

namespace Nirvana
{
    using Game;
    using LuaInterface;
    using System;
    using System.Collections.Generic;
    using UnityEngine;
    using UnityObject = UnityEngine.Object;

    [Serializable]
    public struct ControlItem
    {
        public UnityObject Target;
        public bool[] EnabledLevels;
    }

    public sealed class QualityControlActive : MonoBehaviour
    {
        [SerializeField]
        [Tooltip("The quality control items.")]
        private ControlItem[] controls;

        private int overrideLevel = -1;
        private LinkedListNode<Action> listenNode;
        private bool isProcessing = false;

        public bool isBestFlagByArt;

        public void SetOverrideLevel(int level)
        {
            this.overrideLevel = level;
            if (QualityConfig.QualityLevel != this.overrideLevel)
            {
                this.OnQualityLevelChanged(level);
            }
        }

#if UNITY_EDITOR
        [NoToLua]
        public void SetOverrideLevelInEditor(int level)
        {
            this.OnQualityLevelChanged(level);
        }
#endif

        /// <summary>
        /// Reset the override level and use the global quality level.
        /// </summary>
        public void ResetOverrideLevel()
        {
            var level = QualityConfig.QualityLevel;
            if (this.overrideLevel != level)
            {
                this.OnQualityLevelChanged(level);
            }

            this.overrideLevel = -1;
        }

        private void Awake()
        {
            this.listenNode = QualityConfig.ListenQualityChanged(this.OnQualityLevelChanged);
            this.OnQualityLevelChanged();
        }

        private void OnDestroy()
        {
            if (this.listenNode != null)
            {
                QualityConfig.UnlistenQualtiy(this.listenNode);
                this.listenNode = null;
            }
        }

        private void OnQualityLevelChanged()
        {
            if (this.overrideLevel < 0)
            {
                var level = QualityConfig.QualityLevel;
                this.OnQualityLevelChanged(level);
            }
        }

        private void OnQualityLevelChanged(int level)
        {
            if (null == this.controls) return;
            
            // 防止重入调用
            if (this.isProcessing) return;
            this.isProcessing = true;

            try
            {
                foreach (var control in this.controls)
                {
                    bool enabled = false;
                    if (level < control.EnabledLevels.Length)
                    {
                        enabled = control.EnabledLevels[level];
                    }

                    if (control.Target != null)
                    {
                        var behaviour = control.Target as Behaviour;
                        if (behaviour != null)
                        {
                            if (behaviour && behaviour.enabled != enabled)
                            {
                                behaviour.enabled = enabled;
                            }
                        }
                        else
                        {
                            var gameObject = control.Target as GameObject;
                            if (gameObject && gameObject.activeInHierarchy != enabled)
                            {
                                gameObject.SetActive(enabled);
                            }
                        }
                    }
                }
            }
            finally
            {
                this.isProcessing = false;
            }
        }

#if UNITY_EDITOR
        [NoToLua]
        public void OnValidate()
        {
            if (!GameRoot.Instance)
            {
                this.OnQualityLevelChanged();
            }
        }

        [NoToLua]
        public void AutoFetch(bool isSeriousCost)
        {
            controls = this.GetActiveList(isSeriousCost).ToArray();
        }

        [NoToLua]
        public ControlItem[] GetControls()
        {
            return controls;
        }

        [NoToLua]
        public void SetControls(ControlItem[] _controls)
        {
            controls = _controls;
        }

        [NoToLua]
        public bool HasInvalidControl()
        {
            List<ControlItem> list = this.GetActiveList();

            if (null == controls && list.Count == 0)
            {
                return false;
            }

            if (null == controls && list.Count > 0)
            {
                return true;
            }

            if (controls.Length != list.Count)
            {
                return true;
            }

            foreach (var item in list)
            {
                if (item.Target == null)
                {
                    return true;
                }

                int disableCount = 0;
                for (int i = 0; i < item.EnabledLevels.Length; i++)
                {
                    if (!item.EnabledLevels[i]) ++disableCount;
                }

                if (disableCount == item.EnabledLevels.Length)
                {
                    return true;
                }
            }

            return false;
        }

        [NoToLua]
        public List<ControlItem> GetActiveList(bool isSeriousCost = false)
        {
            List<ControlItem> list = new List<ControlItem>();
            HashSet<UnityObject> hashSet = new HashSet<UnityObject>();
            if (null != controls)
            {
                foreach (var item in controls)
                {
                    if (null != item.Target)
                    {
                        hashSet.Add(item.Target);
                        list.Add(item);
                    }
                }
            }

            List<GameObject> controlGameObjList = new List<GameObject>();
            GameObjectAttach[] attachs = this.GetComponentsInChildren<GameObjectAttach>();
            foreach (var attacher in attachs)
            {
                controlGameObjList.Add(attacher.gameObject);
            }

            MeshRenderer[] meshRenders = this.GetComponentsInChildren<MeshRenderer>();
            foreach (var item in meshRenders)
            {
                controlGameObjList.Add(item.gameObject);
            }

            SkinnedMeshRenderer[] skinnedMeshes = this.GetComponentsInChildren<SkinnedMeshRenderer>();
            foreach (var item in skinnedMeshes)
            {
                bool isActorMat = false;
                foreach (var material in item.sharedMaterials)
                {
                    if (material && material.HasProperty("_MaterialStyle") && material.GetFloat("_MaterialStyle") == 1)
                    {
                        isActorMat = true;
                    }
                }

                if (!isActorMat)
                {
                    controlGameObjList.Add(item.gameObject);
                }
            }

            ParticleSystem[] particles = this.GetComponentsInChildren<ParticleSystem>();
            foreach (var item in particles)
            {
                if (!item.GetComponentInParent<GameObjectAttach>())
                {
                    if (isSeriousCost)
                    {
                        if (ParticleSystemUtil.CalcParticlePlanMaxSize(item) >= 20)
                        {
                            controlGameObjList.Add(item.gameObject);
                        }
                    }
                    else
                    {
                        controlGameObjList.Add(item.gameObject);
                    }
                }
            }

            TrailRenderer[] trailRenderers = this.GetComponentsInChildren<TrailRenderer>();
            foreach (var item in trailRenderers)
            {
                controlGameObjList.Add(item.gameObject);
            }

            foreach (var gameObject in controlGameObjList)
            {
                if (!hashSet.Contains(gameObject))
                {
                    hashSet.Add(gameObject);
                    ControlItem item = new ControlItem();
                    item.Target = gameObject;
                    item.EnabledLevels = new bool[4];
                    item.EnabledLevels[0] = true;
                    list.Add(item);
                }
            }

            return list;
        }
#endif
    }
}
