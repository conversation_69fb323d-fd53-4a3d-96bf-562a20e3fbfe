ConsumeRankThreeView = ConsumeRankThreeView or BaseClass(ActBaseViewTwo)
function ConsumeRankThreeView:__init()
	self.ui_config = {"uis/view/consume_rank_three_ui_prefab","ConsumeRankThreeView"}
	self.config_tab = {{"layout_chongzhi_rank_three",{0}}}
	self.act_id = ServerActClientId.RAND_CONSUME_GOLD_RANK
	self.open_tween = nil
	self.close_tween = nil
end

function ConsumeRankThreeView:__delete()
end

function ConsumeRankThreeView:ReleaseCallBack()
	-- if self.reward_list then
	-- 	self.reward_list:DeleteMe()
	-- 	self.reward_list = nil
	-- end
	if nil ~= self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
	self.label_time = nil
	self.label_consume_gold = nil
	self.has_load_callback = nil
	self.need_refresh = nil
end

function ConsumeRankThreeView:LoadCallBack()
	self.label_time = self.node_list.label_time
	self.label_consume_gold = self.node_list.label_consume_gold

    -- 排行榜列表
	self.rank_list = AsyncListView.New(ConsumeRankThreeItemRender,self.node_list.ph_rank)

	XUI.AddClickEventListener(self.node_list.btn_recharge, BindTool.Bind1(self.OnClickBtnRecharge, self))
	self.has_load_callback = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function ConsumeRankThreeView:OnClickBtnRecharge()
	RechargeWGCtrl.Instance:Open()
end

function ConsumeRankThreeView:__delete()
	if CountDownManager.Instance:HasCountDown("continuously_recharge") then
		CountDownManager.Instance:RemoveCountDown("continuously_recharge")
	end
end

function ConsumeRankThreeView:ShowIndexCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()

	if CountDownManager.Instance:HasCountDown("continuously_recharge") then
		CountDownManager.Instance:RemoveCountDown("continuously_recharge")
	end
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_CONSUME_GOLD_RANK) or {}

	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		local next_time = act_cornucopia_info.next_time or 0
		CountDownManager.Instance:AddCountDown("continuously_recharge", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), next_time, nil, 1)	
	else
		self:CompleteRollerTime()
	end
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_CONSUME_GOLD_RANK,
		opera_type = RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE.RA_TOTAL_CHONGZHI_DAY_OPERA_TYPE_QUERY_INFO,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ConsumeRankThreeView:CloseCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	ActBaseViewTwo.CloseCallBack(self)

	if CountDownManager.Instance:HasCountDown("continuously_recharge") then
		CountDownManager.Instance:RemoveCountDown("continuously_recharge")
	end
end

function ConsumeRankThreeView:RefreshView()
	if not self.has_load_callback then
		self.need_refresh = true
		return
	end
	local consume_gold = ServerActivityWGData.Instance:GetSecondCurrentValue(self.act_id)
	self.label_consume_gold.text.text = (consume_gold)

	-- local reward_cfg_list = ConsumeRankThreeWGData.Instance:GetRewardShowData()
	-- self.reward_list:SetData(reward_cfg_list,3)
	local rank_list, my_rank = ConsumeRankThreeWGData.Instance:GetRankListData(self.act_id)
	self.rank_list:SetDataList(rank_list,3)
	local rank_data_list = ServerActivityWGData.Instance:GetCacheActRewardData(self.act_id)
	local content = rank_data_list.cur_value_t[1]
	-- content = type(content) == "number" and "<color=#006a25>" .. my_rank .. "</color>" or "<color=#ff0000>" .. content .. "</color>"
	content = type(content) == "number" and my_rank or content
	self.node_list.rich_my_rank.text.text = content
	self:RefreshTopDesc()
end

function ConsumeRankThreeView:UpdataRollerTime(elapse_time, total_time)
	local time = total_time - elapse_time
	if self.label_time ~= nil then
		if time > 0 then
			self.label_time.text.text = TimeUtil.FormatSecondDHM8(time)
			-- local format_time = TimeUtil.Format2TableDHM(time)
			-- local str_list = Language.Common.TimeList
			-- local time_str = ""
			-- if format_time.day > 0 then
			-- 	time_str = format_time.day .. str_list.d
			-- end
			-- if format_time.hour > 0 then
			-- 	time_str = time_str .. format_time.hour .. str_list.h
			-- end
			-- time_str = time_str .. format_time.min .. str_list.min

			-- self.label_time.text.text = time_str
		end
	end
end

function ConsumeRankThreeView:CompleteRollerTime()
	if self.label_time ~= nil then
		self.label_time.text.text = "0"
	end
end

---------------itemRender-------------------------
ConsumeRewardThreeRender = ConsumeRewardThreeRender or BaseClass(BaseRender)

function ConsumeRewardThreeRender:__init()
	self:CreateChild()
end

function ConsumeRewardThreeRender:__delete()
	if self.single_recharge_cell then
		for k,v in pairs(self.single_recharge_cell) do
			v:DeleteMe()
		end
	end
	self.single_recharge_cell = {}
end

function ConsumeRewardThreeRender:CreateChild()
	self.single_recharge_cell = {}
	local ph = self.node_list.ph_item_list
	for i = 1, 5 do
		local cell = ItemCell.New(ph)
		self.single_recharge_cell[i] = cell
	end
end

function ConsumeRewardThreeRender:OnFlush()
	if self.data == nil then return end
	local item_list = ItemWGData.Instance:GetGiftConfig(self.data.reward_item.item_id)
	for i = 1,#item_list.item_data do
		if item_list.item_data ~= nil then 
			local item_list2 = ItemWGData.Instance:GetGiftConfig(item_list.item_data[i].id)
			if item_list2 then
				self.single_recharge_cell[i]:SetData({item_id = item_list2.item_data[i].id, num = item_list2.item_data[i].num, is_bind = item_list2.item_data[i].isbind, param = item_list2.item_data[i].param})
			else
				self.single_recharge_cell[i]:SetData({item_id = item_list.item_data[i].id, num = item_list.item_data[i].num, is_bind = item_list.item_data[i].isbind, param = item_list.item_data[i].param})
			end
			self.single_recharge_cell[i]:SetVisible(true)
		end
	end

	if self.index == self.data.seq + 1 then
		local recharge_rank_info = string.format(Language.DailyRank.ConsumeRank, self.data.need_rank, self.data.min_gold)
		self.node_list["rich_reward_rank"].text.text = recharge_rank_info
	end
end

function ConsumeRewardThreeRender:CreateSelectEffect()
	
end
------------------itemRender排行榜-------------------
ConsumeRankThreeItemRender = ConsumeRankThreeItemRender or BaseClass(BaseRender)
function ConsumeRankThreeItemRender:__init()
	self:CreateChild()
end

function ConsumeRankThreeItemRender:__delete()
	self.label_rank = nil
	self.label_name = nil
	self.lbl_totol_xiaofei = nil

	if self.single_recharge_cell then
		for k,v in pairs(self.single_recharge_cell) do
			v:DeleteMe()
		end
	end
	self.single_recharge_cell = {}
end

function ConsumeRankThreeItemRender:CreateChild()
	self.label_rank = self.node_list.label_rank
	self.label_name = self.node_list.label_name
	self.lbl_totol_xiaofei = self.node_list.lbl_totol_xiaofei
	self.single_recharge_cell = {}
	local ph = self.node_list.ph_item_list
	for i = 1, 5 do
		local cell = ItemCell.New(ph)
		self.single_recharge_cell[i] = cell
	end
end

function ConsumeRankThreeItemRender:OnFlush()
	if nil == self.data then return end
	local item_list = ItemWGData.Instance:GetGiftConfig(self.data.reward_item.item_id)
	for i = 1,#item_list.item_data do
		if item_list.item_data ~= nil and self.single_recharge_cell[i] then
			local item_list2 = ItemWGData.Instance:GetGiftConfig(item_list.item_data[i].id)
			if item_list2 and item_list2.item_data[i] then
				self.single_recharge_cell[i]:SetData({item_id = item_list2.item_data[i].id, num = item_list2.item_data[i].num, is_bind = item_list2.item_data[i].isbind, param = item_list2.item_data[i].param})
			else
				self.single_recharge_cell[i]:SetData({item_id = item_list.item_data[i].id, num = item_list.item_data[i].num, is_bind = item_list.item_data[i].isbind, param = item_list.item_data[i].param})
			end
			self.single_recharge_cell[i]:SetVisible(true)
		end
	end

	self.label_rank.text.text = (self.data.rank_index)
	self.label_name.text.text = (self.data.user_name)
	self.lbl_totol_xiaofei.text.text = (self.data.rank_value)
	if self.data.rank_index > 0 and self.data.rank_index <= 3 then
		if self.data.rank_index ~= 3 and nil ~= self.data then
			if tonumber(self.data.rank_value) ~= nil and self.data.rank_index == 1 and tonumber(self.data.rank_value) > 0 then 
				self.lbl_totol_xiaofei.text.text = (Language.Activity.GoldConsumeRankSecrecy)
			end
		end
		self:CreateRankMedal(self.data.rank_index)
		return
	end
	self.node_list.img_rank.image.enabled = false
end

function ConsumeRankThreeItemRender:CreateRankMedal(rank)
	if rank and rank <=3 then 
		self.node_list.img_rank.image.enabled = true
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonOthers('rank_num_' .. rank))
	end
end

function ConsumeRankThreeItemRender:CreateSelectEffect()
	
end