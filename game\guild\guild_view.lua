-- 仙盟
GuildView = GuildView or BaseClass(SafeBaseView)

local Fun_TabIndex = {
	guild_info = TabIndex.guild_info,
	guild_history = TabIndex.guild_history,
	guild_member = TabIndex.guild_member,
	guild_guildlist = TabIndex.guild_guildlist,
	guild_redpacket = TabIndex.guild_redpacket,
	guild_cangku = TabIndex.guild_cangku,
	guild_wage = TabIndex.guild_wage,
	guild_shop = TabIndex.guild_shop,
	guild_sign = TabIndex.guild_sign,
	-- guild_boss = TabIndex.guild_boss,
	-- guild_dati = TabIndex.guild_dati,
	-- guild_shouhu = TabIndex.guild_shouhu,
	guild_act = TabIndex.guild_act,
	-- guild_task = TabIndex.guild_task,
	-- guild_liekun = TabIndex.guild_liekun,

	guild_War = TabIndex.guild_War,
	guild_skill = TabIndex.guild_skill,
	--guild_activity = TabIndex.guild_activity,
	guild_baoxiang = TabIndex.guild_baoxiang,
	guild_assign = TabIndex.guild_assign,
	guild_my_assign = TabIndex.guild_my_assign,
	-- guild_longhui = TabIndex.guild_longhui,
}

function GuildView:__init()
	self.view_style = ViewStyle.Full

	self:SetMaskBg()
	self.is_align_right = true -- 是否向右对齐
	self.is_safe_area_adapter = true
	self.default_index = TabIndex.guild_info
	self.tab_sub = {nil, Language.Guild.TabSub1, Language.Guild.TabSub2, nil, nil, Language.Guild.TabSub7   } -- 第六个是技能 暂时还没有 把雇佣放在了第六个
	self.remind_tab = {
		{ RemindName.Guild_ZongLan_GaiKuang, nil, nil, nil },
		{nil},
		{ RemindName.Guild_Wage, RemindName.Guild_ZongLan_RedBag, RemindName.BiZuoBaoXiangRemind, nil, RemindName.Guild_Skill },
		{ RemindName.Guild_Activity },
		{ RemindName.Guild_Activit_ZhengBa },
		{nil},
	}

	self:CreateFuncList()
	self.click_color = 0
	self.item_cd_event = BindTool.Bind1(self.ItemCDChangeCallback, self)
	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack, self)

	local bundle_name = "uis/view/guild_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.guild_info, bundle_name, "layout_gaikuang")
	self:AddViewResource(TabIndex.guild_member, bundle_name, "layout_menber_list")
	self:AddViewResource(TabIndex.guild_guildlist, bundle_name, "layout_guildlist")
	self:AddViewResource(TabIndex.guild_history, bundle_name, "layout_lishijilu")
	self:AddViewResource(TabIndex.guild_wage, bundle_name, "layout_guild_wage")
	self:AddViewResource(TabIndex.guild_redpacket, bundle_name, "layout_red_packet")
	self:AddViewResource(TabIndex.guild_baoxiang, bundle_name, "layout_baoxiang")
	self:AddViewResource(TabIndex.guild_cangku, bundle_name, "layout_guild_ck")
	self:AddViewResource(TabIndex.guild_shop, bundle_name, "layout_guild_shop")
	self:AddViewResource(TabIndex.guild_sign, bundle_name, "layout_guild_sign")
	-- self:AddViewResource(TabIndex.guild_cangku, bundle_name, "layout_ck_tempbag")
	--self:AddViewResource(TabIndex.guild_activity, bundle_name, "layout_guild_activity")
	self:AddViewResource(TabIndex.guild_act, bundle_name, "layout_guild_activity_new")
	self:AddViewResource(TabIndex.guild_skill, bundle_name, "layout_guild_skill_view")
	self:AddViewResource(TabIndex.guild_War, bundle_name, "layout_guild_war")
	self:AddViewResource(TabIndex.guild_assign, bundle_name, "layout_guild_rent")
	self:AddViewResource(TabIndex.guild_my_assign, bundle_name, "layout_guild_my_rent")
	-- self:AddViewResource(TabIndex.guild_liekun, bundle_name, "layout_liekun")
	self:AddViewResource(0, common_bundle_name, "VerticalTabbar")
	self:AddViewResource(0, common_bundle_name, "HorizontalTabbar", { vector2 = Vector2(596, -90) })
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function GuildView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:DeleteIntroView()
	self:DeleteHistoryView()
	self:DeleteMemberView()
	self:DeleteGuildListView()
	self:DeleteBossView()
	self:DeleteLieKunView()
	self:DeleteGuildckView()
	self:DeleteGuildShouHu()
	self:DeleteRedPacketView()
	self:DeleteGuildWageView()
	self:DeleteGuildWar()
	self:DeleteGuildSkillView()
	-- self:DeleteGuildActView()
	self:DeleteGuildActivityView()
	-- self:DeleteGuildActBossView()
	-- self:DeleteGuildActDatiView()
	-- self:DeleteGuildActShouHuView()
	self:DeleteBaoXiangView()
	self:GuildBagReleaseCallBack()
	self:DeleteGuildRentView()
	self:DeleteGuildMyRentView()
	self:GuildShopReleaseCallBack()
	self:GuildSignReleaseCallBack()

	if self.pop_alert then
		self.pop_alert:DeleteMe()
		self.pop_alert = nil
	end

	if self.btn_list_create then
		self.btn_list_create = nil
	end
	if self.layout_list_unenterbtn then
		self.layout_list_unenterbtn = nil
	end
	if self.guild_role_name then
		self.guild_role_name = nil
	end
	if self.layout_list_enterbtn then
		self.layout_list_enterbtn = nil
	end
	if self.btn_list_look then
		self.btn_list_look = nil
	end
	if self.guild_name then
		self.guild_name = nil
	end
	if self.btn_list_ulook then
		self.btn_list_ulook = nil
	end
	if self.btn_list_find then
		self.btn_list_find = nil
	end

	self.last_index = nil
end


function GuildView:CreateFuncList()
	self.init_func_list = {
		[TabIndex.guild_info] = "InitIntroView",
		[TabIndex.guild_history] = "InitHistoryView",
		[TabIndex.guild_member] = "InitMemberView",
		[TabIndex.guild_guildlist] = "InitGuildListView",
		[TabIndex.guild_cangku] = "InitGuildckView",
		[TabIndex.guild_wage] = "InitGuildWageView",
		[TabIndex.guild_shop] = "InitGuildShopView",
		[TabIndex.guild_sign] = "InitGuildSignView",
		--[TabIndex.guild_active_task] = "InitGuildActiveTaskView",
		[TabIndex.guild_redpacket] = "InitRedPacketView",
		[TabIndex.guild_skill] = "InitGuildSkillView",
		--[TabIndex.guild_activity] = "InitGuildActView",
		[TabIndex.guild_act] = "InitGuildActivityView",
		[TabIndex.guild_War] = "InitWARattleView",
		[TabIndex.guild_baoxiang] = "InitBaoXiangView",
		[TabIndex.guild_assign] = "InitGuildRentView",
		[TabIndex.guild_my_assign] = "InitGuildMyRentView",
	}
	self.flush_func_list = {
		[TabIndex.guild_info] = "OnFlushIntroView",
		[TabIndex.guild_history] = "OnFlushHistoryView",
		[TabIndex.guild_member] = "OnFlushMember",
		[TabIndex.guild_guildlist] = "OnFlushGuildList",
		[TabIndex.guild_cangku] = "OnFlushGuildckView",
		[TabIndex.guild_wage] = "OnFlushGuildWageView",
		[TabIndex.guild_shop] = "OnFlushGuildShopView",
		[TabIndex.guild_sign] = "OnFlushGuildSignView",
		--[TabIndex.guild_active_task] = "OnFlushGuildActiveTaskView",
		[TabIndex.guild_redpacket] = "OnFlushRedPacketView",
		[TabIndex.guild_skill] = "OnFlushGuildSkillView",
		--[TabIndex.guild_activity] = "OnFlushGuildActView",
		[TabIndex.guild_act] = "OnFlushGuildActivityView",

		[TabIndex.guild_War] = "OnFlushWar",
		[TabIndex.guild_baoxiang] = "FlushBaoXiangView",
		[TabIndex.guild_assign] = "FlushGuidRentView",
		[TabIndex.guild_my_assign] = "FlushGuidMyRentView",
	}
end

function GuildView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("guild") --设置图标
		self.tabbar:Init(Language.Guild.TabGrop, self.tab_sub, nil, nil, self.remind_tab)
		self:SetTabVisible()
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		if guild_id > 0 then
			FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.Guild, self.tabbar)
		end
	end

	self.node_list.title_view_name.text.text = Language.Guild.GuildName
end

function GuildView:LoadIndexCallBack(index, loaded_times)
	if index == TabIndex.guild_skill then
		-- self:LoadGuildSkillCallBack()
		-- GuildWGData.Instance:RemberEnterViewJiHuoSkill()
	elseif index == TabIndex.guild_cangku then
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		if guild_id > 0 then
			GuildWGCtrl.Instance:SendSorgeReqInfo()
		end
	end

	if self.init_func_list[index] and self[self.init_func_list[index]] then
		self[self.init_func_list[index]](self)
	end
end

function GuildView:SetTabVisible()
	if nil == self.tabbar then return end
	local guild_id = RoleWGData.Instance.role_vo.guild_id

	for k, v in pairs(Fun_TabIndex) do
		if guild_id <= 0 then
			self.tabbar:SetVerToggleVisble(v, false)
			self.tabbar:SetToggleVisible(v, false)
			self.tabbar:SetVerBGVisble(false)
		else
			self.tabbar:SetVerBGVisble(true)
			self.tabbar:SetVerToggleVisble(v, true)
			self.tabbar:SetToggleVisible(v, FunOpen.Instance:GetFunIsOpened(k))
		end
	end
end

function GuildView:OpenCallBack()
	self:SetTabVisible()
	GuildWGCtrl.Instance:CSApplyForTuanZhangAgentReq(0)
	ItemWGData.Instance:NotifyColddownChangeCallBack(self.item_cd_event)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)
	self.select_liekun_index = nil
end

function GuildView:ShowIndexCallBack(index)
	local bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg4")
	if index == TabIndex.guild_info then
		bundle, asset = ResPath.GetRawImagesPNG("a3_ty_bg1")
		self.is_load_intro_model = false
		local info_type = GuildDataConst.GUILD_INFO_TYPE
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		GuildWGCtrl.Instance:SendGetGuildInfoReq(info_type.ALL_GUILD_BASE_INFO, guild_id)
	end

	if index == TabIndex.guild_history then --历史
		-- bundle, asset = ResPath.GetRawImagesPNG("a3_xm_tybj")
		local info_type = GuildDataConst.GUILD_INFO_TYPE
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		GuildWGCtrl.Instance:SendGetGuildInfoReq(info_type.GUILD_EVENT_LIST, guild_id)
	elseif index == TabIndex.guild_redpacket then
		self:ShowRedPacketCallBack()
		GuildWGData.Instance:SetGuildRedFlush(true)
		GuildWGCtrl.Instance:SendGuildRedPocketOperate(GUILD_RED_POCKET_OPERATE_TYPE.GUILD_RED_POCKET_OPERATE_INFO_LIST,
			0, 0)
	elseif index == TabIndex.guild_War then
		self:ShowGuildWarCallBack()
		GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE
		.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_MATCH_STATE)
		GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE
		.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_GUILD_RANK)
	elseif index == TabIndex.guild_act then
		bundle, asset = ResPath.GetRawImagesPNG("a3_xm_bg_hd")
		self:ShowGuildActivityCallBack()
	elseif index == TabIndex.guild_guildlist then
		-- bundle, asset = ResPath.GetRawImagesPNG("a3_xm_tybj")
		self:CreateGuildList()
		local info_type = GuildDataConst.GUILD_INFO_TYPE
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		GuildWGCtrl.Instance:SendGetGuildInfoReq(info_type.ALL_GUILD_BASE_INFO, guild_id)
		GuildWGCtrl.Instance:OnSendFakeGuildOprate(1)
	elseif index == TabIndex.guild_member then
		-- bundle, asset = ResPath.GetRawImagesPNG("a3_xm_tybj")
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		if 0 ~= role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, role_vo.guild_id)
		end

		self:InItJump()
		self:CloseView_member()
	elseif index == TabIndex.guild_skill then
		-- self:ShowGuildSkillCallBack()
		-- elseif index == TabIndex.guild_activity then
		-- 	self:ShowGuildACTCallBack()
	elseif index == TabIndex.guild_baoxiang then
		bundle, asset = ResPath.GetRawImagesPNG("a2_xm_bg_4_1")
		self:BaoXiangShowIndex()
	elseif index == TabIndex.guild_shop then
		self:GuildShopShowIndexCallBack()
	elseif index == TabIndex.guild_sign then
		self:GuildSignShowIndexCallBack()
	elseif index == TabIndex.guild_assign then
		self:ShowGuildRentCallBack()
	elseif index == TabIndex.guild_my_assign then
		self:ShowGuildMyRentCallBack()
	end

	if index ~= TabIndex.guild_boss then
		if CountDownManager.Instance:HasCountDown("guild_boss_info") then
			CountDownManager.Instance:RemoveCountDown("guild_boss_info")
		end
	end

	local shield_ver = { TabIndex.guild_info, TabIndex.guild_wage }
	local shield_hor = { TabIndex.guild_boss, TabIndex.guild_dati, TabIndex.guild_shouhu, TabIndex.guild_War }
	if index == TabIndex.guild_liekun and IS_ON_CROSSSERVER then
		local fun = function()
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
		end
		for i, v in ipairs(shield_ver) do
			self.tabbar:SetOtherBtn(v, true, fun)
		end

		for i, v in ipairs(shield_hor) do
			self.tabbar:SetHorOtherBtn(v, true, fun)
		end
	else
		for i, v in ipairs(shield_ver) do
			self.tabbar:SetOtherBtn(v, false, nil)
		end

		for i, v in ipairs(shield_hor) do
			self.tabbar:SetHorOtherBtn(v, false, nil)
		end
	end

	if self.last_index then
		if self.last_index == TabIndex.guild_wage and index ~= TabIndex.guild_wage then
			if nil == GuildWGCtrl.Instance:GetWeekTaskClickFlag() and GuildWGCtrl.Instance:GetWeekTaskRemindState() == 1 then
				GuildWGCtrl.Instance:SetWeekTaskClickFlag(1)
				RemindManager.Instance:Fire(RemindName.Guild_Wage)
			end
		end
	end
	self.last_index = index

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle1, asset1 = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle1, asset1, self.node_list["money_tabar_pos"].transform)
	end

	self.old_lh_level = nil

	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function GuildView:CloseCallBack()
	ItemWGData.Instance:UnNotifyColddownChangeCallBack(self.item_cd_event)
	ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
	GuildWGData.Instance:RemberEnterViewJiHuoSkill()
	GuildWGData.Instance:RemberSelectSkillId()
	GuildBaoXiangWGData.Instance:SetNeedPlayAnim(false)
	self.select_liekun_index = nil

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.last_index and self.last_index == TabIndex.guild_wage then
		if nil == GuildWGCtrl.Instance:GetWeekTaskClickFlag() and GuildWGCtrl.Instance:GetWeekTaskRemindState() == 1 then
			GuildWGCtrl.Instance:SetWeekTaskClickFlag(1)
			RemindManager.Instance:Fire(RemindName.Guild_Wage)
		end
	end
end

function GuildView:CloseAllEffect()
	-- self:CloseEffect()
end

function GuildView:ItemDataListChangeCallback()
	-- print_error("ItemDataListChangeCallback")
	-- self:Flush(math.floor(TabIndex.guild_cangku), "itemdata_list_change")
end

function GuildView:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
	-- self:Flush({GuildView.TABINDEX.JX}, "itemdata_change", {[index] = {item_id = item_id, index = index, reason = reason, put_reason = put_reason, old_num = old_num, new_num = new_num}})
end

function GuildView:ItemCDChangeCallback(colddown_id)
	local bag_item_list = ItemWGData.Instance:GetBagItemDataList()
	local item_cfg = nil
	for k, v in pairs(bag_item_list) do
		item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if nil ~= item_cfg and item_cfg.colddown_id and item_cfg.colddown_id > 0 and (item_cfg.colddown_id == colddown_id or 0 == colddown_id) then
			local end_time = ItemWGData.Instance:GetColddownEndTime(colddown_id)
			self:Flush(math.floor(TabIndex.guild_cangku), "itemcd_change",
				{ [v.index] = { item_id = v.item_id, index = v.index, cd_end_time = end_time, client_colddown = item_cfg
				.client_colddown } })
		end
	end
end

function GuildView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if self.flush_func_list[index] and self[self.flush_func_list[index]] then
			self[self.flush_func_list[index]](self, param_t, index)
		end
	end
	-- 打开仙盟自动签到
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	if guild_id > 0 then
		-- self:SendSignRequest()
	end
end

function GuildView:OnTabChangeHandler(index)
	self:ChangeToIndex(index)
end

-- 权限设置
function GuildView:SetGuildAuthority()

end

-- 获取当前选项卡索引
function GuildView:GetSelectTab()
	return self.tabbar_select_index
end

function GuildView:GetGuideUiCallBack(ui_name, ui_param)
	local ui, callback
	if ui_name == GuideUIName.CloseBtn then
		return self.node_t_list.btn_close_window.node, BindTool.Bind1(self.OnClosePanelAndHall, self)
	elseif ui_name == GuideUIName.GuildItem then
		ui = self:GetGuildItemByGuide(tonumber(ui_param[1]))
		callback = self:GetGuildItemCallback(tonumber(ui_param[1]))
		return ui, callback
	elseif ui_name == GuideUIName.GuildOneKeyBtn then
		return self:GetOneKeyShenQing()
	end

	return nil, nil
end

function GuildView:OnClosePanelAndHall()
	self:OnCloseHandler()
	-- ActHallWGCtrl.Instance:Close()
end

function GuildView:RoleDataChangeCallback(key, value)
	-- if key == "longhun" then
	-- 	GuildWGCtrl.Instance:UpdataRemindNum()
	-- 	self:Flush(TabIndex.guild_longhui)
	-- end
end

function GuildView:ActivityChangeCallBack(act_type)
	if act_type == ACTIVITY_TYPE.GUILD_FB or
		act_type == ACTIVITY_TYPE.GUILD_ANSWER or
		act_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS then
		if self:IsLoadedIndex(TabIndex.guild_act) and self:GetShowIndex() == TabIndex.guild_act then
			self:ReqGuildFbRankInfo()
			-- self:FlushAllPanel()
		end
	end
end

function GuildView:GetPopAlert()
	if not self.pop_alert then
		self.pop_alert = Alert.New()
		self.pop_alert:SetTextIsLeft(false)
	end
	return self.pop_alert
end

function GuildView:GetBtnListCreate(btn_create_guild)
	if btn_create_guild == nil then
		return self.btn_list_create
	else
		self.btn_list_create = btn_create_guild
	end
end

function GuildView:GetLayoutListUnenterbtn(layout_list_unenterbtn)
	if layout_list_unenterbtn == nil then
		return self.layout_list_unenterbtn
	else
		self.layout_list_unenterbtn = layout_list_unenterbtn
	end
end

function GuildView:GetGuildRoleName(guild_role_name)
	if guild_role_name == nil then
		return self.guild_role_name
	else
		self.guild_role_name = guild_role_name
	end
end

function GuildView:GetGuildName(guild_name)
	if guild_name == nil then
		return self.guild_name
	else
		self.guild_name = guild_name
	end
end

function GuildView:GetBtnListUlook(btn_list_ulook)
	if btn_list_ulook == nil then
		return self.btn_list_ulook
	else
		self.btn_list_ulook = btn_list_ulook
	end
end

function GuildView:GetBtnListFind(btn_list_find)
	if btn_list_find == nil then
		return self.btn_list_find
	else
		self.btn_list_find = btn_list_find
	end
end

function GuildView:GetOldLhLevel()
	return self.old_lh_level
end

function GuildView:GetSelectLieKunIndex()
	return self.select_liekun_index
end

function GuildView:SendSignRequest()
	local sign_flag = GuildWGData.Instance:GetGuildSignFlag()
	if sign_flag == 1 then
		return
	end
	GuildWGCtrl.Instance:SendCSGuildOperateRequest(GUILD_OPERATE_TYPE.SIGN)
end
