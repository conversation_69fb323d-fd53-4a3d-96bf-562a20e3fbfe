--------锻造 进化--------

function FairyLandEquipmentView:EvolveLoadIndexCallBack()
	self.evolve_attr_group = {}
	local attr_num = 10
	for i = 1, attr_num do
		local obj = ResMgr:Instantiate(self.node_list["evolve_attr_item"].gameObject)
		local obj_transform = obj.transform
		obj_transform:SetParent(self.node_list["evolve_attr_group"].transform, false)
		self.evolve_attr_group[i] = EvolveAttrItem.New(obj)
		self.evolve_attr_group[i]:SetIndex(i)
	end

	self.evolve_cur_item = ItemCell.New(self.node_list["evolve_cur_item"])
	self.evolve_next_item = ItemCell.New(self.node_list["evolve_next_item"])
	self.evolve_need_stuff = ItemCell.New(self.node_list["evolve_need_stuff"])
	self.evolve_need_equip = ItemCell.New(self.node_list["evolve_need_equip_pos"])
	self.evolve_cur_item:SetItemTipFrom(ItemTip.FROM_HOLY_EQUIP_EVOLVE)
	self.evolve_next_item:SetItemTipFrom(ItemTip.FROM_HOLY_EQUIP_EVOLVE)

	XUI.AddClickEventListener(self.node_list["evolve_up_btn"], BindTool.Bind(self.ClickEvolveUpBtn, self))
	XUI.AddClickEventListener(self.node_list["evolve_need_equip_add"], BindTool.Bind(self.ClickEvolveNeedEquipAdd, self))
	XUI.AddClickEventListener(self.node_list["evolve_master_btn"], BindTool.Bind(self.ClickEvolveMasterBtn, self))

	local color_limit = FairyLandEquipmentWGData.Instance:GetEvolveUpColorLimit()
	local color_name = Language.Common.ColorName[color_limit]
	self.node_list["not_evolve_desc"].text.text = string.format(Language.FairyLandEquipment.EvolveNotDesc, color_name)
end

function FairyLandEquipmentView:EvolveReleaseCallBack()
	if not IsEmptyTable(self.evolve_attr_group) then
		for k,v in pairs(self.evolve_attr_group) do
			v:DeleteMe()
		end
		self.evolve_attr_group = {}
	end

	if self.evolve_cur_item then
		self.evolve_cur_item:DeleteMe()
		self.evolve_cur_item = nil
	end

	if self.evolve_next_item then
		self.evolve_next_item:DeleteMe()
		self.evolve_next_item = nil
	end

	if self.evolve_need_stuff then
		self.evolve_need_stuff:DeleteMe()
		self.evolve_need_stuff = nil
	end

	if self.evolve_need_equip then
		self.evolve_need_equip:DeleteMe()
		self.evolve_need_equip = nil
	end

	if self.evolve_stuff_alert then
		self.evolve_stuff_alert:DeleteMe()
		self.evolve_stuff_alert = nil
	end
end

function FairyLandEquipmentView:EvolveShowIndexCallBack()
	FairyLandEquipmentWGData.Instance:ClearEvolveSelectBagParam()
end

function FairyLandEquipmentView:EvolveFlushContent()
	local slot = self:GetCurSelectSlot()
	if slot < 0 then
		return
	end

	local part = self.select_equip_part
	local equip_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)

	if IsEmptyTable(equip_info) or equip_info.item_id <= 0 then
		self.node_list["can_evolve_panel"]:SetActive(false)
		self.node_list["not_evolve_panel"]:SetActive(true)
		return
	end

	local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(slot, part)

	local upgrade_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipUpgradeCfg(slot, part, grade + 1)
	if not IsEmptyTable(upgrade_cfg) then
		local attr_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipAttrListCfg(upgrade_cfg.attr_index)
		if attr_cfg.is_star == 1 then
			equip_info.star = equip_info.star + 1
			self.evolve_next_item:SetData(equip_info)
			equip_info.star = equip_info.star - 1
		else
			self.evolve_next_item:SetData(equip_info)
		end
	end

	self.evolve_cur_item:SetData(equip_info)
	self.node_list["evolve_cur_level"].text.text = string.format(Language.FairyLandEquipment.EvolveGradeDesc, grade)
	self.node_list["evolve_next_level"].text.text = string.format(Language.FairyLandEquipment.EvolveGradeDesc, grade + 1)

	local max_grade = FairyLandEquipmentWGData.Instance:GetEvolveMaxGrade()
	local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(slot, part)
	local is_max = grade >= max_grade

	local color_limit = FairyLandEquipmentWGData.Instance:GetEvolveUpColorLimit()
	local equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(equip_info.item_id)
	self.node_list["evolve_item_name"].text.text = ItemWGData.Instance:GetItemName(equip_info.item_id)
	self.node_list["can_evolve_panel"]:SetActive(equip_cfg.color >= color_limit)
	self.node_list["not_evolve_panel"]:SetActive(equip_cfg.color < color_limit)

	--self.node_list["evolve_max_title"]:SetActive(is_max)
	--self.node_list["evolve_desc_title"]:SetActive(not is_max)
	self.node_list["evolve_is_max"]:SetActive(is_max)
	self.node_list["evolve_buttom"]:SetActive(not is_max)
	self.node_list["evolve_next"]:SetActive(not is_max)
	self.node_list["evolve_cur_txt"].text.text = string.format(Language.FairyLandEquipment.EvolveGradeDesc, grade)
	self.node_list["evolve_next_txt"].text.text = string.format(Language.FairyLandEquipment.EvolveGradeDesc, grade + 1)
	self.node_list["evolve_slider_grade"].text.text = string.format(Language.FairyLandEquipment.EvolveGradeNum, grade)
	local data_list = FairyLandEquipmentWGData.Instance:GetGBEquipAttrGroupAttrList(slot, part)

	-- local height = is_max and #data_list * 26 + 10 or 160
	-- height = height <= 160 and 160 or 222
	-- RectTransform.SetSizeDeltaXY(self.node_list["evolve_attr_group_bg"].rect, 360, height)

	-- local rand_attr = FairyLandEquipmentWGData.Instance:GetPartRandAttr(slot, part)
	for k,v in pairs(self.evolve_attr_group) do
		-- local data = {}
		-- data.attr_index = tonumber(attr_split[k])
		-- data.is_act = FairyLandEquipmentWGData.Instance:GetPartRandAttrIsAct(slot, part,data.attr_index)
		v:SetData(data_list[k])
	end

	if not is_max then
		local upgrade_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipUpgradeCfg(slot, part, grade + 1)
		local stuff_id = upgrade_cfg and upgrade_cfg.stuff_id or 0
		local stuff_data = {item_id = stuff_id, is_bind = 0}
		self.evolve_need_stuff:SetData(stuff_data)
		local need_stuff_num = upgrade_cfg and upgrade_cfg.stuff_num or 1
		local has_stuff_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
		self.evolve_need_stuff:SetRightBottomTextVisible(true)
		local str = ToColorStr(tostring(has_stuff_num) .. "/" .. need_stuff_num, has_stuff_num >= need_stuff_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
		self.evolve_need_stuff:SetRightBottomColorText(str)

		local init_per = upgrade_cfg and upgrade_cfg.init_per or 0
		init_per = init_per / 10000
		self.node_list["evolve_nor_slider"].slider.value = init_per

		local select_list, add_per, count = FairyLandEquipmentWGData.Instance:GetEvolveSelectBagParam()
		self.node_list["evolve_add_slider"].slider.value = init_per + (add_per / 100)
		-- self.node_list["evolve_slider_txt"].text.text = (init_per * 100) .. "%" .. "+" .. add_per .. "%"
		self.node_list["evolve_slider_txt"].text.text = ((init_per * 100) + add_per) .. "%"
		self.node_list["evolve_tip"].text.text = string.format(Language.FairyLandEquipment.EvolveTipButtomDesc2, init_per * 100, add_per)

		local per_enough = ((init_per * 100) + add_per) >= 100

		if select_list and select_list[1] then
			local info = FairyLandEquipmentWGData.Instance:GetHolyEquipBagItem(select_list[1])
			if info then
				self.evolve_need_equip:SetData(info)
				self.evolve_need_equip:SetRightBottomTextVisible(true,true)
				self.evolve_need_equip:SetRightBottomColorText("x" .. count)
			end
			self.node_list["evolve_need_equip_add_img"]:SetActive(not per_enough)
		else
			self.node_list["evolve_need_equip_add_img"]:SetActive(true)
			self.evolve_need_equip:ClearData()
			local bunble, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
			self.evolve_need_equip:SetCellBg(bunble, asset)
		end
		
		self.node_list["evolve_up_remind"]:SetActive(has_stuff_num >= need_stuff_num and per_enough)
		local cur_can_envolve = FairyLandEquipmentWGData.Instance:GetEquipNeedAddPer(slot, part) == 1
		self.node_list["evolve_add_remind"]:SetActive(cur_can_envolve and not per_enough and has_stuff_num >= need_stuff_num)
	end

	local master_remind = FairyLandEquipmentWGData.Instance:GetEvolveMasterRemind(slot)
	self.node_list["evolve_master_red"]:SetActive(master_remind)
end

function FairyLandEquipmentView:ClickEvolveUpBtn()
	local slot = self:GetCurSelectSlot()
	if slot < 0 then
		return
	end

	local part = self.select_equip_part
	local equip_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)
	if IsEmptyTable(equip_info) or equip_info.item_id <= 0 then
		return
	end

	local max_grade = FairyLandEquipmentWGData.Instance:GetEvolveMaxGrade()
	local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(slot, part)
	local is_max = grade >= max_grade
	if is_max then
		return
	end
	
	local upgrade_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipUpgradeCfg(slot, part, grade + 1)
	local init_per = upgrade_cfg and upgrade_cfg.init_per or 0
	local stuff_id = upgrade_cfg and upgrade_cfg.stuff_id or 0
	local need_stuff_num = upgrade_cfg.stuff_num
	local has_stuff_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id)
	init_per = init_per / 100
	local select_list, add_per, count = FairyLandEquipmentWGData.Instance:GetEvolveSelectBagParam()
	local send_fun = function ()
		FairyLandEquipmentWGCtrl.Instance:SendCSXianjieEquipShenZhuangUpGrade(slot, part, count, select_list)
	end

	local succes_fun = function ()
		local lerp_num = need_stuff_num - has_stuff_num
		local single_price = ShopWGData.Instance:GetShopCfgItemIdPrice(stuff_id, Shop_Money_Type.Type1)
		local need_gold	= single_price * lerp_num
		local enough_gold = RoleWGData.Instance:GetIsEnoughUseGold(need_gold)
		if not enough_gold then
			UiInstanceMgr.Instance:ShowChongZhiView()
			return
		end

		if init_per + add_per < 100 then
			-- FairyLandEquipmentWGCtrl.Instance:OpenEvolvePopEquipBag(slot, part)
			if not self.evolve_not_succes_alert then
		        self.evolve_not_succes_alert = Alert.New()
		    end

		    local str = string.format(Language.FairyLandEquipment.EvolveSuccessAlertDesc,init_per + add_per)
		    self.evolve_not_succes_alert:SetShowCheckBox(true, "evolve_not_succes_alert3")
		    self.evolve_not_succes_alert:SetOkFunc(send_fun)
		    self.evolve_not_succes_alert:SetCancelString(Language.FairyLandEquipment.EvolveCancelAlertBtn)
		    self.evolve_not_succes_alert:SetCancelFunc(function ()
		    	self:ClickEvolveNeedEquipAdd()
		    end)
		    self.evolve_not_succes_alert:SetLableString(str)
		    self.evolve_not_succes_alert:SetCheckBoxDefaultSelect(false)
		    self.evolve_not_succes_alert:Open()
			return
		end

		-- if IsEmptyTable(select_list) then 
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.EvolveSelectBagTip)
		-- 	-- return
		-- end

		send_fun()
	end

	--进化进度条没有满
	-- if add_per <= 0 then
		-- self:ClickEvolveNeedEquipAdd()
		-- return
	-- end

	--所需材料不足
	-- if need_stuff_num > has_stuff_num then
	-- 	local lerp_num = need_stuff_num - has_stuff_num
	-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(stuff_id)
	-- 	local single_price = ShopWGData.Instance:GetShopCfgItemIdPrice(stuff_id, Shop_Money_Type.Type1)
	-- 	local need_gold	= single_price * lerp_num
	-- 	local enough_gold = RoleWGData.Instance:GetIsEnoughUseGold(need_gold)
	-- 	if not enough_gold then
	-- 		UiInstanceMgr.Instance:ShowChongZhiView()
	-- 		return
	-- 	else
	-- 		if not self.evolve_stuff_alert then
	-- 	        self.evolve_stuff_alert = Alert.New()
	-- 	    end
	-- 	    local item_name = ToColorStr(item_cfg and item_cfg.name or "",ITEM_COLOR[item_cfg.color])
	-- 	    local str = string.format(Language.FairyLandEquipment.EvolveStuffAlertTxt,lerp_num,item_name,need_gold)
	-- 	    self.evolve_stuff_alert:SetShowCheckBox(true, "evolve_stuff_alert3")
	-- 	    self.evolve_stuff_alert:SetOkFunc(succes_fun)
	-- 	    self.evolve_stuff_alert:SetLableString(str)
	-- 	    self.evolve_stuff_alert:SetCheckBoxDefaultSelect(false)
	-- 	    self.evolve_stuff_alert:Open()
	-- 	    return
	-- 	end
	-- end

	--所需材料不足
	if need_stuff_num > has_stuff_num then
		if not self.evolve_stuff_alert then
	        self.evolve_stuff_alert = Alert.New()
	    end

	    local lerp_num = need_stuff_num - has_stuff_num
		local single_price = ShopWGData.Instance:GetShopCfgItemIdPrice(stuff_id, Shop_Money_Type.Type1)
		local need_gold	= single_price * lerp_num
	    local item_cfg = ItemWGData.Instance:GetItemConfig(stuff_id)
		if item_cfg then
			local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			local str = string.format(Language.FairyLandEquipment.EvolveStuffAlertTxt, lerp_num, item_name, need_gold)
			self.evolve_stuff_alert:SetLableString(str)
		end

	    self.evolve_stuff_alert:SetShowCheckBox(true, "evolve_stuff_alert3")
	    self.evolve_stuff_alert:SetOkFunc(succes_fun)
	    self.evolve_stuff_alert:SetCheckBoxDefaultSelect(false)
	    self.evolve_stuff_alert:Open()
	    return
	end

	-- if IsEmptyTable(select_list) then 
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.EvolveSelectBagTip)
	-- 	-- return
	-- end

	succes_fun()

end

function FairyLandEquipmentView:ClickEvolveNeedEquipAdd()
	local slot = self:GetCurSelectSlot()
	if slot < 0 then
		return
	end

	local part = self.select_equip_part
	local has_item = FairyLandEquipmentWGData.Instance:EvolveEquipBagIsHasItem(slot, part)
	if has_item then
		FairyLandEquipmentWGCtrl.Instance:OpenEvolvePopEquipBag(slot, part)
	else
		local get_way_id_list = FairyLandEquipmentWGData.Instance:GetHolyEquipGetWayList()
		TipWGCtrl.Instance:OpenEquipGetWayView(nil, get_way_id_list)
	end
end

function FairyLandEquipmentView:ClickEvolveMasterBtn()
	local slot = self:GetCurSelectSlot()
	if slot < 0 then
		return
	end

    FairyLandEquipmentWGCtrl.Instance:OpenEvolveStarMaster(slot)
end

function FairyLandEquipmentView:ShowEvolveSuccess()
	if self.node_list["evolve_success_eff"] then
		local bundle, asset = ResPath.GetUIEffect("UI_juguang_baokai")
		EffectManager.Instance:PlayAtTransform(bundle, asset, self.node_list["evolve_success_eff"].transform, 3)
	end
end

-----------------------------EvolveAttrItem------------------
EvolveAttrItem = EvolveAttrItem or BaseClass(BaseRender)

function EvolveAttrItem:__init()
	
end

function EvolveAttrItem:__delete()
	
end

function EvolveAttrItem:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local attr_cfg = self.data.attr_cfg
	local is_act = self.data.is_act == 1
	local color = is_act and COLOR3B.GREEN or COLOR3B.C6
	if not IsEmptyTable(attr_cfg) then
		self.node_list["star"]:SetActive(attr_cfg.is_star == 1)
		local attr_name = ""
		local attr_value = 0
		if attr_cfg.gb_attr_add_per and attr_cfg.gb_attr_add_per > 0 then
			attr_name = Language.FairyLandEquipment.EvolveGodBodyAddPer
			attr_value = (attr_cfg.gb_attr_add_per / 100) .. "%"
		else
			attr_name = EquipmentWGData.Instance:GetAttrNameByAttrId(attr_cfg.attr_type, true)
			local type_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cfg.attr_type)
			local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(type_str)
			attr_value = attr_cfg.attr_value
			if is_per then
				attr_value = (attr_cfg.attr_value / 100) .. "%"
			end
		end

		self.node_list["attr"].text.text = ToColorStr(string.format("%s  %s", attr_name, attr_value), color)
		-- self.node_list["attr_name"].text.text = ToColorStr(attr_name, color)
		-- self.node_list["attr_value"].text.text = ToColorStr(attr_value, color)
	end

	local get_str = is_act and Language.FairyLandEquipment.EvolveHasGet or string.format(Language.FairyLandEquipment.EvolveShuiJiGet, self.data.grade)
	self.node_list["get_txt"].text.text = ToColorStr(get_str, color)
end