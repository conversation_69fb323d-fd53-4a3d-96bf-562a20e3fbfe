﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TMPro_TMP_InputFieldWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(TMPro.TMP_InputField), typeof(UnityEngine.UI.Selectable));
		<PERSON><PERSON>RegFunction("SetTextWithoutNotify", SetTextWithoutNotify);
		<PERSON><PERSON>RegFunction("MoveTextEnd", MoveTextEnd);
		<PERSON><PERSON>Function("MoveTextStart", MoveTextStart);
		<PERSON><PERSON>RegFunction("MoveToEndOfLine", MoveToEndOfLine);
		<PERSON><PERSON>RegFunction("MoveToStartOfLine", MoveToStartOfLine);
		<PERSON><PERSON>Function("OnBeginDrag", OnBeginDrag);
		<PERSON><PERSON>RegFunction("OnDrag", OnDrag);
		<PERSON><PERSON>RegFunction("OnEndDrag", OnEndDrag);
		<PERSON><PERSON>RegFunction("OnPointerDown", OnPointerDown);
		<PERSON><PERSON>RegFunction("ProcessEvent", ProcessEvent);
		<PERSON><PERSON>RegFunction("OnUpdateSelected", OnUpdateSelected);
		<PERSON><PERSON>RegFunction("OnScroll", OnScroll);
		L.RegFunction("ForceLabelUpdate", ForceLabelUpdate);
		L.RegFunction("Rebuild", Rebuild);
		L.RegFunction("LayoutComplete", LayoutComplete);
		L.RegFunction("GraphicUpdateComplete", GraphicUpdateComplete);
		L.RegFunction("ActivateInputField", ActivateInputField);
		L.RegFunction("OnSelect", OnSelect);
		L.RegFunction("OnPointerClick", OnPointerClick);
		L.RegFunction("OnControlClick", OnControlClick);
		L.RegFunction("ReleaseSelection", ReleaseSelection);
		L.RegFunction("DeactivateInputField", DeactivateInputField);
		L.RegFunction("OnDeselect", OnDeselect);
		L.RegFunction("OnSubmit", OnSubmit);
		L.RegFunction("CalculateLayoutInputHorizontal", CalculateLayoutInputHorizontal);
		L.RegFunction("CalculateLayoutInputVertical", CalculateLayoutInputVertical);
		L.RegFunction("SetGlobalPointSize", SetGlobalPointSize);
		L.RegFunction("SetGlobalFontAsset", SetGlobalFontAsset);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("shouldHideMobileInput", get_shouldHideMobileInput, set_shouldHideMobileInput);
		L.RegVar("shouldHideSoftKeyboard", get_shouldHideSoftKeyboard, set_shouldHideSoftKeyboard);
		L.RegVar("text", get_text, set_text);
		L.RegVar("isFocused", get_isFocused, null);
		L.RegVar("caretBlinkRate", get_caretBlinkRate, set_caretBlinkRate);
		L.RegVar("caretWidth", get_caretWidth, set_caretWidth);
		L.RegVar("textViewport", get_textViewport, set_textViewport);
		L.RegVar("textComponent", get_textComponent, set_textComponent);
		L.RegVar("placeholder", get_placeholder, set_placeholder);
		L.RegVar("verticalScrollbar", get_verticalScrollbar, set_verticalScrollbar);
		L.RegVar("scrollSensitivity", get_scrollSensitivity, set_scrollSensitivity);
		L.RegVar("caretColor", get_caretColor, set_caretColor);
		L.RegVar("customCaretColor", get_customCaretColor, set_customCaretColor);
		L.RegVar("selectionColor", get_selectionColor, set_selectionColor);
		L.RegVar("onEndEdit", get_onEndEdit, set_onEndEdit);
		L.RegVar("onSubmit", get_onSubmit, set_onSubmit);
		L.RegVar("onSelect", get_onSelect, set_onSelect);
		L.RegVar("onDeselect", get_onDeselect, set_onDeselect);
		L.RegVar("onTextSelection", get_onTextSelection, set_onTextSelection);
		L.RegVar("onEndTextSelection", get_onEndTextSelection, set_onEndTextSelection);
		L.RegVar("onValueChanged", get_onValueChanged, set_onValueChanged);
		L.RegVar("onTouchScreenKeyboardStatusChanged", get_onTouchScreenKeyboardStatusChanged, set_onTouchScreenKeyboardStatusChanged);
		L.RegVar("onValidateInput", get_onValidateInput, set_onValidateInput);
		L.RegVar("characterLimit", get_characterLimit, set_characterLimit);
		L.RegVar("pointSize", get_pointSize, set_pointSize);
		L.RegVar("fontAsset", get_fontAsset, set_fontAsset);
		L.RegVar("onFocusSelectAll", get_onFocusSelectAll, set_onFocusSelectAll);
		L.RegVar("resetOnDeActivation", get_resetOnDeActivation, set_resetOnDeActivation);
		L.RegVar("restoreOriginalTextOnEscape", get_restoreOriginalTextOnEscape, set_restoreOriginalTextOnEscape);
		L.RegVar("isRichTextEditingAllowed", get_isRichTextEditingAllowed, set_isRichTextEditingAllowed);
		L.RegVar("contentType", get_contentType, set_contentType);
		L.RegVar("lineType", get_lineType, set_lineType);
		L.RegVar("lineLimit", get_lineLimit, set_lineLimit);
		L.RegVar("inputType", get_inputType, set_inputType);
		L.RegVar("keyboardType", get_keyboardType, set_keyboardType);
		L.RegVar("characterValidation", get_characterValidation, set_characterValidation);
		L.RegVar("inputValidator", get_inputValidator, set_inputValidator);
		L.RegVar("readOnly", get_readOnly, set_readOnly);
		L.RegVar("richText", get_richText, set_richText);
		L.RegVar("multiLine", get_multiLine, null);
		L.RegVar("asteriskChar", get_asteriskChar, set_asteriskChar);
		L.RegVar("wasCanceled", get_wasCanceled, null);
		L.RegVar("caretPosition", get_caretPosition, set_caretPosition);
		L.RegVar("selectionAnchorPosition", get_selectionAnchorPosition, set_selectionAnchorPosition);
		L.RegVar("selectionFocusPosition", get_selectionFocusPosition, set_selectionFocusPosition);
		L.RegVar("stringPosition", get_stringPosition, set_stringPosition);
		L.RegVar("selectionStringAnchorPosition", get_selectionStringAnchorPosition, set_selectionStringAnchorPosition);
		L.RegVar("selectionStringFocusPosition", get_selectionStringFocusPosition, set_selectionStringFocusPosition);
		L.RegVar("minWidth", get_minWidth, null);
		L.RegVar("preferredWidth", get_preferredWidth, null);
		L.RegVar("flexibleWidth", get_flexibleWidth, null);
		L.RegVar("minHeight", get_minHeight, null);
		L.RegVar("preferredHeight", get_preferredHeight, null);
		L.RegVar("flexibleHeight", get_flexibleHeight, null);
		L.RegVar("layoutPriority", get_layoutPriority, null);
		L.RegFunction("OnValidateInput", TMPro_TMP_InputField_OnValidateInput);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTextWithoutNotify(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			obj.SetTextWithoutNotify(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MoveTextEnd(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.MoveTextEnd(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MoveTextStart(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.MoveTextStart(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MoveToEndOfLine(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.MoveToEndOfLine(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int MoveToStartOfLine(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 3);
			obj.MoveToStartOfLine(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnBeginDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnBeginDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnEndDrag(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnEndDrag(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerDown(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerDown(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ProcessEvent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.Event arg0 = (UnityEngine.Event)ToLua.CheckObject(L, 2, typeof(UnityEngine.Event));
			obj.ProcessEvent(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnUpdateSelected(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnUpdateSelected(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnScroll(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnScroll(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceLabelUpdate(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			obj.ForceLabelUpdate();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Rebuild(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.UI.CanvasUpdate arg0 = (UnityEngine.UI.CanvasUpdate)ToLua.CheckObject(L, 2, typeof(UnityEngine.UI.CanvasUpdate));
			obj.Rebuild(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int LayoutComplete(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			obj.LayoutComplete();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GraphicUpdateComplete(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			obj.GraphicUpdateComplete();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ActivateInputField(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			obj.ActivateInputField();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnSelect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnSelect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnPointerClick(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.PointerEventData arg0 = (UnityEngine.EventSystems.PointerEventData)ToLua.CheckObject<UnityEngine.EventSystems.PointerEventData>(L, 2);
			obj.OnPointerClick(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnControlClick(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			obj.OnControlClick();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ReleaseSelection(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			obj.ReleaseSelection();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DeactivateInputField(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
				obj.DeactivateInputField();
				return 0;
			}
			else if (count == 2)
			{
				TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
				bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
				obj.DeactivateInputField(arg0);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: TMPro.TMP_InputField.DeactivateInputField");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDeselect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnDeselect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnSubmit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			UnityEngine.EventSystems.BaseEventData arg0 = (UnityEngine.EventSystems.BaseEventData)ToLua.CheckObject<UnityEngine.EventSystems.BaseEventData>(L, 2);
			obj.OnSubmit(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CalculateLayoutInputHorizontal(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			obj.CalculateLayoutInputHorizontal();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CalculateLayoutInputVertical(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			obj.CalculateLayoutInputVertical();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetGlobalPointSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.SetGlobalPointSize(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetGlobalFontAsset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)ToLua.CheckObject<TMPro.TMP_InputField>(L, 1);
			TMPro.TMP_FontAsset arg0 = (TMPro.TMP_FontAsset)ToLua.CheckObject<TMPro.TMP_FontAsset>(L, 2);
			obj.SetGlobalFontAsset(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shouldHideMobileInput(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.shouldHideMobileInput;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shouldHideMobileInput on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_shouldHideSoftKeyboard(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.shouldHideSoftKeyboard;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shouldHideSoftKeyboard on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_text(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			string ret = obj.text;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index text on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isFocused(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.isFocused;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isFocused on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_caretBlinkRate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.caretBlinkRate;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index caretBlinkRate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_caretWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.caretWidth;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index caretWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textViewport(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.RectTransform ret = obj.textViewport;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textViewport on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_textComponent(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_Text ret = obj.textComponent;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textComponent on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_placeholder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.UI.Graphic ret = obj.placeholder;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index placeholder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_verticalScrollbar(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.UI.Scrollbar ret = obj.verticalScrollbar;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index verticalScrollbar on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_scrollSensitivity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.scrollSensitivity;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollSensitivity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_caretColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.Color ret = obj.caretColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index caretColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_customCaretColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.customCaretColor;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index customCaretColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_selectionColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.Color ret = obj.selectionColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onEndEdit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.SubmitEvent ret = obj.onEndEdit;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onEndEdit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onSubmit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.SubmitEvent ret = obj.onSubmit;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onSubmit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onSelect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.SelectionEvent ret = obj.onSelect;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onSelect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onDeselect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.SelectionEvent ret = obj.onDeselect;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onDeselect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onTextSelection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.TextSelectionEvent ret = obj.onTextSelection;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onTextSelection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onEndTextSelection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.TextSelectionEvent ret = obj.onEndTextSelection;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onEndTextSelection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onValueChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.OnChangeEvent ret = obj.onValueChanged;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onValueChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onTouchScreenKeyboardStatusChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.TouchScreenKeyboardEvent ret = obj.onTouchScreenKeyboardStatusChanged;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onTouchScreenKeyboardStatusChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onValidateInput(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.OnValidateInput ret = obj.onValidateInput;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onValidateInput on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_characterLimit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.characterLimit;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterLimit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pointSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.pointSize;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pointSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_fontAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_FontAsset ret = obj.fontAsset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_onFocusSelectAll(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.onFocusSelectAll;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onFocusSelectAll on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_resetOnDeActivation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.resetOnDeActivation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index resetOnDeActivation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_restoreOriginalTextOnEscape(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.restoreOriginalTextOnEscape;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index restoreOriginalTextOnEscape on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_isRichTextEditingAllowed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.isRichTextEditingAllowed;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isRichTextEditingAllowed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_contentType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.ContentType ret = obj.contentType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index contentType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lineType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.LineType ret = obj.lineType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_lineLimit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.lineLimit;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineLimit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_inputType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.InputType ret = obj.inputType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index inputType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_keyboardType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.TouchScreenKeyboardType ret = obj.keyboardType;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index keyboardType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_characterValidation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.CharacterValidation ret = obj.characterValidation;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterValidation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_inputValidator(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputValidator ret = obj.inputValidator;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index inputValidator on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_readOnly(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.readOnly;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index readOnly on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_richText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.richText;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index richText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_multiLine(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.multiLine;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index multiLine on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_asteriskChar(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			char ret = obj.asteriskChar;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index asteriskChar on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_wasCanceled(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool ret = obj.wasCanceled;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index wasCanceled on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_caretPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.caretPosition;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index caretPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_selectionAnchorPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.selectionAnchorPosition;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionAnchorPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_selectionFocusPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.selectionFocusPosition;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionFocusPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_stringPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.stringPosition;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index stringPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_selectionStringAnchorPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.selectionStringAnchorPosition;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionStringAnchorPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_selectionStringFocusPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.selectionStringFocusPosition;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionStringFocusPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_minWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.minWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index minWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_preferredWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.preferredWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index preferredWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_flexibleWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.flexibleWidth;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index flexibleWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_minHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.minHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index minHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_preferredHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.preferredHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index preferredHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_flexibleHeight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float ret = obj.flexibleHeight;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index flexibleHeight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_layoutPriority(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int ret = obj.layoutPriority;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index layoutPriority on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shouldHideMobileInput(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.shouldHideMobileInput = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shouldHideMobileInput on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_shouldHideSoftKeyboard(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.shouldHideSoftKeyboard = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index shouldHideSoftKeyboard on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_text(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.text = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index text on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_caretBlinkRate(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.caretBlinkRate = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index caretBlinkRate on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_caretWidth(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.caretWidth = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index caretWidth on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_textViewport(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.RectTransform arg0 = (UnityEngine.RectTransform)ToLua.CheckObject(L, 2, typeof(UnityEngine.RectTransform));
			obj.textViewport = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textViewport on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_textComponent(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_Text arg0 = (TMPro.TMP_Text)ToLua.CheckObject<TMPro.TMP_Text>(L, 2);
			obj.textComponent = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index textComponent on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_placeholder(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.UI.Graphic arg0 = (UnityEngine.UI.Graphic)ToLua.CheckObject<UnityEngine.UI.Graphic>(L, 2);
			obj.placeholder = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index placeholder on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_verticalScrollbar(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.UI.Scrollbar arg0 = (UnityEngine.UI.Scrollbar)ToLua.CheckObject<UnityEngine.UI.Scrollbar>(L, 2);
			obj.verticalScrollbar = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index verticalScrollbar on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_scrollSensitivity(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.scrollSensitivity = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index scrollSensitivity on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_caretColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.caretColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index caretColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_customCaretColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.customCaretColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index customCaretColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_selectionColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.selectionColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onEndEdit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.SubmitEvent arg0 = (TMPro.TMP_InputField.SubmitEvent)ToLua.CheckObject<TMPro.TMP_InputField.SubmitEvent>(L, 2);
			obj.onEndEdit = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onEndEdit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onSubmit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.SubmitEvent arg0 = (TMPro.TMP_InputField.SubmitEvent)ToLua.CheckObject<TMPro.TMP_InputField.SubmitEvent>(L, 2);
			obj.onSubmit = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onSubmit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onSelect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.SelectionEvent arg0 = (TMPro.TMP_InputField.SelectionEvent)ToLua.CheckObject<TMPro.TMP_InputField.SelectionEvent>(L, 2);
			obj.onSelect = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onSelect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onDeselect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.SelectionEvent arg0 = (TMPro.TMP_InputField.SelectionEvent)ToLua.CheckObject<TMPro.TMP_InputField.SelectionEvent>(L, 2);
			obj.onDeselect = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onDeselect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onTextSelection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.TextSelectionEvent arg0 = (TMPro.TMP_InputField.TextSelectionEvent)ToLua.CheckObject<TMPro.TMP_InputField.TextSelectionEvent>(L, 2);
			obj.onTextSelection = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onTextSelection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onEndTextSelection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.TextSelectionEvent arg0 = (TMPro.TMP_InputField.TextSelectionEvent)ToLua.CheckObject<TMPro.TMP_InputField.TextSelectionEvent>(L, 2);
			obj.onEndTextSelection = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onEndTextSelection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onValueChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.OnChangeEvent arg0 = (TMPro.TMP_InputField.OnChangeEvent)ToLua.CheckObject<TMPro.TMP_InputField.OnChangeEvent>(L, 2);
			obj.onValueChanged = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onValueChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onTouchScreenKeyboardStatusChanged(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.TouchScreenKeyboardEvent arg0 = (TMPro.TMP_InputField.TouchScreenKeyboardEvent)ToLua.CheckObject<TMPro.TMP_InputField.TouchScreenKeyboardEvent>(L, 2);
			obj.onTouchScreenKeyboardStatusChanged = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onTouchScreenKeyboardStatusChanged on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onValidateInput(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.OnValidateInput arg0 = (TMPro.TMP_InputField.OnValidateInput)ToLua.CheckDelegate<TMPro.TMP_InputField.OnValidateInput>(L, 2);
			obj.onValidateInput = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onValidateInput on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_characterLimit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.characterLimit = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterLimit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pointSize(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.pointSize = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pointSize on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_fontAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_FontAsset arg0 = (TMPro.TMP_FontAsset)ToLua.CheckObject<TMPro.TMP_FontAsset>(L, 2);
			obj.fontAsset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index fontAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_onFocusSelectAll(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.onFocusSelectAll = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index onFocusSelectAll on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_resetOnDeActivation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.resetOnDeActivation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index resetOnDeActivation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_restoreOriginalTextOnEscape(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.restoreOriginalTextOnEscape = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index restoreOriginalTextOnEscape on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_isRichTextEditingAllowed(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.isRichTextEditingAllowed = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index isRichTextEditingAllowed on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_contentType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.ContentType arg0 = (TMPro.TMP_InputField.ContentType)ToLua.CheckObject(L, 2, typeof(TMPro.TMP_InputField.ContentType));
			obj.contentType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index contentType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lineType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.LineType arg0 = (TMPro.TMP_InputField.LineType)ToLua.CheckObject(L, 2, typeof(TMPro.TMP_InputField.LineType));
			obj.lineType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_lineLimit(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.lineLimit = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index lineLimit on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_inputType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.InputType arg0 = (TMPro.TMP_InputField.InputType)ToLua.CheckObject(L, 2, typeof(TMPro.TMP_InputField.InputType));
			obj.inputType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index inputType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_keyboardType(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			UnityEngine.TouchScreenKeyboardType arg0 = (UnityEngine.TouchScreenKeyboardType)ToLua.CheckObject(L, 2, typeof(UnityEngine.TouchScreenKeyboardType));
			obj.keyboardType = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index keyboardType on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_characterValidation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputField.CharacterValidation arg0 = (TMPro.TMP_InputField.CharacterValidation)ToLua.CheckObject(L, 2, typeof(TMPro.TMP_InputField.CharacterValidation));
			obj.characterValidation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index characterValidation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_inputValidator(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			TMPro.TMP_InputValidator arg0 = (TMPro.TMP_InputValidator)ToLua.CheckObject<TMPro.TMP_InputValidator>(L, 2);
			obj.inputValidator = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index inputValidator on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_readOnly(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.readOnly = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index readOnly on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_richText(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.richText = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index richText on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_asteriskChar(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			char arg0 = (char)LuaDLL.luaL_checknumber(L, 2);
			obj.asteriskChar = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index asteriskChar on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_caretPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.caretPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index caretPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_selectionAnchorPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.selectionAnchorPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionAnchorPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_selectionFocusPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.selectionFocusPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionFocusPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_stringPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.stringPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index stringPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_selectionStringAnchorPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.selectionStringAnchorPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionStringAnchorPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_selectionStringFocusPosition(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			TMPro.TMP_InputField obj = (TMPro.TMP_InputField)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.selectionStringFocusPosition = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index selectionStringFocusPosition on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int TMPro_TMP_InputField_OnValidateInput(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<TMPro.TMP_InputField.OnValidateInput>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<TMPro.TMP_InputField.OnValidateInput>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

