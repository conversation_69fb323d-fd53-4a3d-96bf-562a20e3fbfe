DaoHangSuitFashionPreviewView = DaoHangSuitFashionPreviewView or BaseClass(SafeBaseView)

function DaoHangSuitFashionPreviewView:__init()
    self.view_style = ViewStyle.Full
    self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/multi_function_ui/daohang_prefab", "layout_daohang_suit_fashion")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function DaoHangSuitFashionPreviewView:LoadCallBack()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_root"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.suit_fashion_list then
        self.suit_fashion_list = AsyncListView.New(DaoHangSuitFashionListItemRender, self.node_list.suit_fashion_list)
    end

    self.node_list.title_view_name.text.text = Language.Charm.DaoHangSuitFishionTitle
end

function DaoHangSuitFashionPreviewView:ReleaseCallBack()
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

    if self.suit_fashion_list then
        self.suit_fashion_list:DeleteMe()
        self.suit_fashion_list = nil
    end
end

function DaoHangSuitFashionPreviewView:OnFlush()
    local model_item_list, suit_icon_list, skill_data_list = MultiFunctionWGData.Instance:GetDaoHangFashionAllShowItem()
    self:FlushModel(model_item_list)

    self.suit_fashion_list:SetDataList(suit_icon_list)
    -- 技能
    if not IsEmptyTable(skill_data_list) then
        for i = 1, 3 do
            local data = skill_data_list[i]

            if not IsEmptyTable(data) then
                local bundle, asset = ResPath.GetSkillIconById(data.fashion_icon)
                self.node_list["skill_icon" .. i].image:LoadSprite(bundle, asset)
                self.node_list["skill_desc" .. i].text.text = data.fashion_txt or ""
                self.node_list["active_desc" .. i].text.text = string.format(Language.Charm.DaoHangFashionActiveTip, data.active_num , data.fashion_num)
                XUI.SetGraphicGrey(self.node_list["skill_icon" .. i], data.active_num < data.fashion_num)
            end
        end
    end
end

function DaoHangSuitFashionPreviewView:FlushModel(model_item_list)
    if not IsEmptyTable(model_item_list) then
        local pos_x, pos_y = 0, 0
        local other_cfg = MultiFunctionWGData.Instance:GetDaoHangOtherCfg()

	    if other_cfg.display_pos and other_cfg.display_pos ~= "" then
		    local pos_list = string.split(other_cfg.display_pos, "|")
		    pos_x = tonumber(pos_list[1]) or pos_x
		    pos_y = tonumber(pos_list[2]) or pos_y
	    end

        local display_data = {}
        display_data.model_item_id_list = model_item_list
        display_data.render_type = 0
        display_data.position = Vector3(pos_x, pos_y, 0)
        -- display_data.event_trigger_listener_node = self.node_list["model_root"]
        self.model_display:SetData(display_data)

	    if other_cfg["display_scale"] then
		    local scale = other_cfg["display_scale"]
		    RectTransform.SetLocalScale(self.node_list["model_root"].transform, scale)
	    end

        if other_cfg.rotation and other_cfg.rotation ~= "" then
		    local rotation_tab = string.split(other_cfg.rotation,"|")
		    self.node_list["model_root"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	    end
    end
end

DaoHangSuitFashionListItemRender = DaoHangSuitFashionListItemRender or BaseClass(BaseRender)

function DaoHangSuitFashionListItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local active = self.data.active
    local icon = self.data.icon
    local bg_bundle, bg_asset = ResPath.GetMultiFunctionImg("a2_snsy_tbk1")
    local icon_bundle, icon_asset = ResPath.GetMultiFunctionImg("a2_dctz_icon_" .. icon)

    if active then
        bg_bundle, bg_asset = ResPath.GetMultiFunctionImg("a2_snsy_tbk")
        icon_bundle, icon_asset = ResPath.GetMultiFunctionImg("a2_dctz_icon_" .. icon .. "_hl")
    end

    self.node_list["suit_item_bg"].image:LoadSprite(bg_bundle, bg_asset, function ()
        self.node_list["suit_item_bg"].image:SetNativeSize()
    end)

    self.node_list["suit_icon"].image:LoadSprite(icon_bundle, icon_asset, function ()
        self.node_list["suit_icon"].image:SetNativeSize()
    end)

    self.node_list["suit_lock"]:CustomSetActive(not active)
    self.node_list["name"].text.text = self.data.name or ""
end