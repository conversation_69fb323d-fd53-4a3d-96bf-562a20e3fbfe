--GodOfWealthView = GodOfWealthView or BaseClass(SafeBaseView)

local grade_reward_item_width = 82
local LUCKY_DRAW_TYPE = {
    ONE = 1,
    TEN = 10,
}
--[[
function GodOfWealthView:LuckyStarInit()
    self:SetMaskBg(false)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
    self.is_safe_area_adapter = true
    self.view_style = ViewStyle.Full

    self:AddViewResource(0, "uis/view/god_of_wealth_ui_prefab", "layout_god_of_wealth_luckystar")
end
]]
function GodOfWealthView:LuckyStarLoadCallBack()
    if nil == self.model_display then
        self.model_display = OperationActRender.New(self.node_list.model0)
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    --[[
    if nil == self.model_display1 then
        self.model_display1 = OperationActRender.New(self.node_list.model1)
        self.model_display1:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    if not self.money_bar then
        self.money_bar = MoneyBar.New()
        local bundle, asset = ResPath.GetWidgets("MoneyBar")
        local show_params = {
            show_gold = true,
            show_bind_gold = true,
            show_coin = true,
            show_silver_ticket = false,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
    end
    ]]--
    
    if not self.lucky_grade_reward_list then
        self.lucky_grade_reward_list = AsyncListView.New(LuckyStarRewardRender, self.node_list.reward_item_list)
        self.node_list.reward_item_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(
        self.OnScrollRectValueChange, self))
    end

    if not self.rare_reward_list then
        self.rare_reward_list = {}

        for i = 1, 5 do
            self.rare_reward_list[i] = ItemCell.New(self.node_list["reward_item" .. i])
            self.rare_reward_list[i]:SetShowCualityBg(false)
            self.rare_reward_list[i]:SetCellBgEnabled(false)
        end
    end

    --[[
    if not self.normal_reward_list then
        self.normal_reward_list = {}

        for i = 1, 9 do
            self.normal_reward_list[i] = LuckyStarRewardItemRender.New(self.node_list["ph_luckystar_cell" .. i])
        end
    end
    ]]
    
    self.luckyStar_draw_list = {}
    self.cache_model_item_list0 = 0
    self.cache_model_item_list1 = 0
    self.luckystar_ignore_anim = false
    self.is_init_in = true

    local other_cfg = GodOfWealthWGData.Instance:GetLuckyStarOtherCfg()
    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(other_cfg.cost_item_id)
    self.node_list.lucky_draw_more_itemcost_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.lucky_draw_more_itemcost_icon.image:SetNativeSize()
    end)

    self.node_list.lucky_draw_itemcost_icon.image:LoadSprite(bundle, asset, function()
        self.node_list.lucky_draw_itemcost_icon.image:SetNativeSize()
    end)

    XUI.AddClickEventListener(self.node_list.luckystar_anim_check, BindTool.Bind(self.OnClickLuckyStarBtnCheck, self))
    --XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind(self.OnClickRule, self))
    XUI.AddClickEventListener(self.node_list.btn_lucky_draw, BindTool.Bind(self.OnClickLuckyDraw, self))
    XUI.AddClickEventListener(self.node_list.btn_lucky_draw_more, BindTool.Bind(self.OnClickLuckyDrawMore, self))
    XUI.AddClickEventListener(self.node_list.lucky_draw_more_itemcost, BindTool.Bind(self.OnClickLuckyDrawIcon, self))
    XUI.AddClickEventListener(self.node_list.lucky_draw_itemcost, BindTool.Bind(self.OnClickLuckyDrawIcon, self))
end

function GodOfWealthView:LuckyStarReleaseCallBack()
    --[[
    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end
    ]]
    
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    --[[
    if self.model_display1 then
        self.model_display1:DeleteMe()
        self.model_display1 = nil
    end
    ]]--

    if self.lucky_grade_reward_list then
        self.lucky_grade_reward_list:DeleteMe()
        self.lucky_grade_reward_list = nil
    end

    if self.final_reward_item then
        self.final_reward_item:DeleteMe()
        self.final_reward_item = nil
    end

    if self.rare_reward_list then
        for k, v in pairs(self.rare_reward_list) do
            v:DeleteMe()
        end

        self.rare_reward_list = nil
    end

    --[[
    if self.normal_reward_list then
        for k, v in pairs(self.normal_reward_list) do
            v:DeleteMe()
        end

        self.normal_reward_list = nil
    end
    ]]

    if self.luckyStar_draw_list then
        for k, v in pairs(self.luckyStar_draw_list) do
            v:DeleteMe()
        end

        self.luckyStar_draw_list = nil
    end

    self.cache_model_item_list1 = nil
    self.cache_model_item_list0 = nil
    self.is_init_in = nil
end

function GodOfWealthView:LuckyStarOnFlush()
    self:FlushModel()

    local lucky_value = GodOfWealthWGData.Instance:GetLuckyStarDrawTime()
    self.node_list.lucky_value.text.text = lucky_value

    local record_list = GodOfWealthWGData.Instance:GetRoleFuXingGaoZhaoWorldRecord()
    local has_data = not IsEmptyTable(record_list)
    self.node_list.lucky_draw_record_nodata:CustomSetActive(not has_data)
    self:FlushRecordDataList(record_list)

    --local rare_data_list, normal_data_list = GodOfWealthWGData.Instance:GetLuckyStarCurRewardPoorCfg()
    local show_data_list = GodOfWealthWGData.Instance:GetLuckyStarRewardPoolShowCfg()
    if not IsEmptyTable(show_data_list) then
        for i = 1, 5 do
            local data = show_data_list[i] or {}
            local has_data = not IsEmptyTable(data)
            self.node_list["reward_item" .. i]:CustomSetActive(has_data)

            if has_data then
                self.rare_reward_list[i]:SetData(data.item)
                self.rare_reward_list[i]:SetRightBottomTextVisible(false)
                self.rare_reward_list[i]:SetBindIconVisible(false)
                self.rare_reward_list[i]:SetEffectRootEnable(false)
            end
        end
    end

    --[[
    if not IsEmptyTable(normal_data_list) then
        for i = 1, 9 do
            local data = normal_data_list[i] or {}
            local has_data = not IsEmptyTable(data)
            self.node_list["ph_luckystar_cell" .. i]:CustomSetActive(false)
            self.normal_reward_list[i]:SetData(data.item)
        end
    end
    ]]
    
    local data_list, _, final_reward_data, slider_value = GodOfWealthWGData.Instance:GetLuckyStarGradeRewardInfo()
    local jump_index
    for i, v in ipairs(data_list) do
        if v.can_get then
            jump_index = i
            break
        end
    end
    self.lucky_grade_reward_list:SetDataList(data_list)
    if jump_index then
        self.lucky_grade_reward_list:JumpToIndex(jump_index)
    end
    
    if self.is_init_in then
        local lucky_value = GodOfWealthWGData.Instance:GetLuckyStarDrawTime()
        local init_index = 1
        for k_i, k_v in ipairs(data_list) do
            local need_lucky_value = k_v.reward_info and k_v.reward_info.times or 0
            if k_v.reward_flag == 0 and lucky_value >= need_lucky_value then
                init_index = k_i
                break
            end
        end

        local content = self.node_list.reward_item_list.scroller.ScrollRect.content
        if content then
            local pos_x = init_index == 1 and 0 or 8 - (init_index - 1) * 98
            local rect = content.transform:GetComponent(typeof(UnityEngine.RectTransform))
            ReDelayCall(self, function()
                if self:IsOpen() then
                    RectTransform.SetAnchoredPositionXY(rect, pos_x, 0)
                end
            end, 0.2, "jump_to_wealth_slider")
        end
    end
    self.is_init_in = nil

    self:FlushFinalLuckyGradeReward(final_reward_data)
    self.node_list.slider.slider.value = slider_value

    local other_cfg = GodOfWealthWGData.Instance:GetLuckyStarOtherCfg()
    local has_item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    local lucky_draw_remind = has_item_num >= 1
    local lucky_draw_more_remind = has_item_num >= 10
    local lucky_draw_color = lucky_draw_remind and COLOR3B.D_GREEN or COLOR3B.D_RED
    local lucky_draw_more_color = lucky_draw_more_remind and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list.lucky_draw_more_itemcost_num.text.text = ToColorStr(has_item_num .. "/" .. 10, lucky_draw_more_color)
    self.node_list.lucky_draw_itemcost_num.text.text = ToColorStr(has_item_num .. "/" .. 1, lucky_draw_color)
    self.node_list.btn_lucky_draw_more_remind:CustomSetActive(lucky_draw_more_remind)
    self.node_list.btn_lucky_draw_remind:CustomSetActive(lucky_draw_remind)
end

function GodOfWealthView:FlushRecordDataList(record_list)
    for i, v in ipairs(self.luckyStar_draw_list) do
        v:SetActive(false)
    end

    if IsEmptyTable(record_list) then
        return
    end

    for i = 1, #record_list do
        if not self.luckyStar_draw_list[i] then
            local bundle = "uis/view/god_of_wealth_ui_prefab"
            local asset = "lucky_draw_record_item"
            local lucky_draw_record_item_async_loader = AllocResAsyncLoader(self,
                "lucky_draw_record_item_async_loader" .. i)

            if lucky_draw_record_item_async_loader then
                lucky_draw_record_item_async_loader:Load(bundle, asset, nil, function(new_obj)
                    local obj = ResMgr:Instantiate(new_obj)
                    obj.transform:SetParent(self.node_list.lucky_draw_record_list_content.transform, false)
                    self.luckyStar_draw_list[i] = LuckyStarRecordRender.New(obj)
                    self.luckyStar_draw_list[i]:SetData(record_list[i])
                    self.luckyStar_draw_list[i]:SetActive(true)
                end)
            end
        else
            self.luckyStar_draw_list[i]:SetData(record_list[i])
            self.luckyStar_draw_list[i]:SetActive(true)
        end
    end
end

function GodOfWealthView:OnScrollRectValueChange()
    local rect = self.node_list.reward_item_list.scroll_rect.content
    self.node_list.slider.rect.sizeDelta = Vector2(rect.rect.size.x + grade_reward_item_width / 2, 10)
    local pos_x = RectTransform.GetAnchoredPositionXY(self.node_list.reward_item_list.scroll_rect.content)
    RectTransform.SetAnchoredPositionXY(self.node_list.slider.rect, pos_x, 0)
end

function GodOfWealthView:FlushFinalLuckyGradeReward(data_info)
    if IsEmptyTable(data_info) then
        return
    end

    data_info.is_final_reward = true
    if not self.final_reward_item then
        local bundle = "uis/view/god_of_wealth_ui_prefab"
        local asset = "reward_item"
        local final_reward_async_loader = AllocResAsyncLoader(self, "luckystar_reward_async_loader")

        if final_reward_async_loader then
            final_reward_async_loader:Load(bundle, asset, nil, function(new_obj)
                local obj = ResMgr:Instantiate(new_obj)
                obj.transform:SetParent(self.node_list.final_reward_item_pos.transform, false)
                obj.transform.localPosition = Vector3.zero
                self.final_reward_item = LuckyStarRewardRender.New(obj)
                self.final_reward_item:SetData(data_info)
            end)
        end
    else
        self.final_reward_item:SetData(data_info)
    end
end

function GodOfWealthView:FlushModel()
    local model_data = GodOfWealthWGData.Instance:GetLuckyStarCurModelShowCfg()

    if self.cache_model_item_list0 ~= model_data["model_show_itemid"] then
        self:SetModelData(self.node_list.model0, self.model_display, model_data)
        self.cache_model_item_list0 = model_data["model_show_itemid"]
    end
    
    --[[
    local bom_data = (model_data or {})[1] or {}
    if self.cache_model_item_list1 ~= bom_data["model_show_itemid"] then
        self:SetModelData(self.node_list.model1, self.model_display1, bom_data)
        self.cache_model_item_list1 = bom_data["model_show_itemid"]
    end
    ]]--
end

function GodOfWealthView:SetModelData(node, render, model_data)
    if IsEmptyTable(model_data) then
        return
    end

    local display_data = {}
    local model_show_type = tonumber(model_data["model_show_type"]) or 1

    if model_data["model_show_itemid"] ~= 0 and model_data["model_show_itemid"] ~= "" then
        local split_list = string.split(model_data["model_show_itemid"], "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
            display_data.item_id = model_data["model_show_itemid"]
        end
    end

    display_data.should_ani = true
    display_data.bundle_name = model_data["model_bundle_name"]
    display_data.asset_name = model_data["model_asset_name"]
    display_data.render_type = model_show_type - 1
    -- display_data.model_click_func = function ()
    -- 	TipWGCtrl.Instance:OpenItem({item_id = model_data["model_show_itemid"]})
    -- end

	if model_data.model_pos and model_data.model_pos ~= "" then
		local pos_list = string.split(model_data.model_pos, "|")
		local pos_x = tonumber(pos_list[1]) or 0
		local pos_y = tonumber(pos_list[2]) or 0
		local pos_z = tonumber(pos_list[3]) or 0

		display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

	if model_data.model_rot and model_data.model_rot ~= "" then
		local rot_list = string.split(model_data.model_rot, "|")
		local rot_x = tonumber(rot_list[1]) or 0
		local rot_y = tonumber(rot_list[2]) or 0
		local rot_z = tonumber(rot_list[3]) or 0
		--display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
		display_data.role_rotation = Vector3(rot_x, rot_y, rot_z)
	end

	if model_data.model_scale and model_data.model_scale ~= "" then
		display_data.model_adjust_root_local_scale = model_data.model_scale
	end

	display_data.model_rt_type = ModelRTSCaleType.L

	render:SetData(display_data)
	
	local pos_x, pos_y = 0, 0
	if model_data.whole_display_pos and model_data.whole_display_pos ~= "" then
		local pos_list = string.split(model_data.whole_display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
		RectTransform.SetAnchoredPositionXY(node.rect, pos_x, pos_y)
	end
	
	local rot_x, rot_y, rot_z = 0, 0, 0
	if model_data.whole_display_rot and model_data.whole_display_rot ~= "" then
		local rot_list = string.split(model_data.whole_display_rot, "|")
		rot_x = tonumber(rot_list[1]) or rot_x
		rot_y = tonumber(rot_list[2]) or rot_y
		rot_z = tonumber(rot_list[3]) or rot_z
		node.transform.localRotation = Quaternion.Euler(rot_x, rot_y, rot_z)
	end

	if model_data.whole_display_scale and model_data.whole_display_scale ~= "" then
		local scale = tonumber(model_data.whole_display_scale)
		Transform.SetLocalScaleXYZ(node.transform, scale, scale, scale)
	end

    --[[
    local pos_x, pos_y, pox_z = 0, 0, 0
    if model_data.display_pos and model_data.display_pos ~= "" then
        local pos_list = string.split(model_data.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
        pox_z = tonumber(pos_list[3]) or pox_z
    end

    RectTransform.SetAnchoredPosition3DXYZ(node.rect, pos_x, pos_y, pox_z)

    if model_data["display_scale"] then
        local scale = model_data["display_scale"]
        Transform.SetLocalScaleXYZ(node.transform, scale, scale, scale)
    end

    if model_data.rotation and model_data.rotation ~= "" then
        local rotation_tab = string.split(model_data.rotation, "|")
        node.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
    end
    ]]
end

--跳过动画
function GodOfWealthView:OnClickLuckyStarBtnCheck()
    self.luckystar_ignore_anim = not self.luckystar_ignore_anim
    self.node_list["luckystar_anim_yes"]:SetActive(self.luckystar_ignore_anim)
end

function GodOfWealthView:OnClickRule()
    RuleTip.Instance:SetContent(Language.GodOfWealth.LuckyStarTipsContent, Language.GodOfWealth.LuckyStarTipsTitle)
end

function GodOfWealthView:OnClickLuckyDraw()
    self:LuckyStarDraw(LUCKY_DRAW_TYPE.ONE)
end

function GodOfWealthView:OnClickLuckyDrawMore()
    self:LuckyStarDraw(LUCKY_DRAW_TYPE.TEN)
end

function GodOfWealthView:LuckyStarDraw(luckydraw_type)
    if not GodOfWealthWGData.Instance:GetLuckyDrawState() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.GodOfWealth.LuckyStarDrawNowError)
        return
    end

    local other_cfg = GodOfWealthWGData.Instance:GetLuckyStarOtherCfg()
    local item_id = other_cfg.cost_item_id
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)

    if item_num >= luckydraw_type then
        if self.luckystar_ignore_anim == false then
            local skeleton = self.node_list["luckystar_spine"].gameObject:GetComponent("SkeletonGraphic")
            skeleton.AnimationState:SetAnimation(0, "open", false)
            skeleton.AnimationState:AddAnimation(0, "idle", true, 3.5)
            ReDelayCall(self, function()
                self.node_list["effect_box_open"]:SetActive(true)
            end, 2.2, "LuckyStarDrawBoxEffect1")
            ReDelayCall(self, function()
                self.node_list["effect_box_open"]:SetActive(false)
            end, 4, "LuckyStarDrawBoxEffect2")
            GodOfWealthWGData.Instance:SetLuckyDrawState(false)
            ReDelayCall(self, function()
                GodOfWealthWGCtrl.Instance:SendRoleFuXingGaoZhaoReq(FUXINGGAOZHAO_OPERA_TYPE.DRAW, luckydraw_type)
            end, 3, "LuckyStarDraw")
        else
            GodOfWealthWGData.Instance:SetLuckyDrawState(false)
            GodOfWealthWGCtrl.Instance:SendRoleFuXingGaoZhaoReq(FUXINGGAOZHAO_OPERA_TYPE.DRAW, luckydraw_type)
        end
    else
        TipWGCtrl.Instance:OpenItem({ item_id = item_id })
    end
end

function GodOfWealthView:OnClickLuckyDrawIcon()
    local other_cfg = GodOfWealthWGData.Instance:GetLuckyStarOtherCfg()
    TipWGCtrl.Instance:OpenItem({ item_id = other_cfg.cost_item_id })
end

-------------------------------LuckyStarRecordRender-----------------------------
LuckyStarRecordRender = LuckyStarRecordRender or BaseClass(BaseRender)

function LuckyStarRecordRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    local item_desc = (item_cfg and item_cfg.name or "") .. "*" .. self.data.num
    self.node_list.desc.text.text = string.format(Language.GodOfWealth.LuckyStarDrawRecordDesc, self.data.role_name,
        item_desc)
end

-------------------------------LuckyStarRewardRender-----------------------------
LuckyStarRewardRender = LuckyStarRewardRender or BaseClass(BaseRender)

function LuckyStarRewardRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.item_cell_pos)
        self.item_cell:SetIsShowTips(false)
        self.item_cell:SetShowCualityBg(false)
        self.item_cell:SetCellBgEnabled(false)
    end
    self.item_cell:AddClickEventListener(BindTool.Bind1(self.OnClick, self))
end

function LuckyStarRewardRender:ReleaseCallBack()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function LuckyStarRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local reward_info = self.data.reward_info
    self.node_list.value.text.text = reward_info.times
    self.item_cell:SetData(reward_info.itemlist)
    self.item_cell:SetRightBottomTextVisible(false)
    self.item_cell:SetBindIconVisible(false)
    self.item_cell:SetEffectRootEnable(false)

    local state = self:GetGradeRewardState()
    self.node_list.lingqu_flag:SetActive(state == REWARD_STATE_TYPE.FINISH)
    self.node_list.kuang:SetActive(self.data.is_final_reward)
    self.node_list.red_point:SetActive(state == REWARD_STATE_TYPE.CAN_FETCH)
end

function LuckyStarRewardRender:GetGradeRewardState()
    local lucky_value = GodOfWealthWGData.Instance:GetLuckyStarDrawTime()
    local need_lucky_value = self.data.reward_info.times

    if self.data.reward_flag == 0 and lucky_value >= need_lucky_value then
        return REWARD_STATE_TYPE.CAN_FETCH
    elseif self.data.reward_flag == 1 then
        return REWARD_STATE_TYPE.FINISH
    else
        return REWARD_STATE_TYPE.UNDONE
    end
end

function LuckyStarRewardRender:OnClick()
    if not IsEmptyTable(self.data) then
        if self:GetGradeRewardState() == REWARD_STATE_TYPE.CAN_FETCH then
            GodOfWealthWGCtrl.Instance:SendRoleFuXingGaoZhaoReq(FUXINGGAOZHAO_OPERA_TYPE.ACQUIRE,
                self.data.reward_info.index)
        else
            TipWGCtrl.Instance:OpenItem(self.data.reward_info.itemlist)
        end
    end
end


---------------------------------------LuckyStarRewardItemRender------------------------------------
--[[
LuckyStarRewardItemRender = LuckyStarRewardItemRender or BaseClass(BaseRender)

function LuckyStarRewardItemRender:LoadCallBack()
    XUI.AddClickEventListener(self.view, BindTool.Bind1(self.ClickHandler, self))
end

function LuckyStarRewardItemRender:ClickHandler()
    if IsEmptyTable(self.data) then
        return
    end

    TipWGCtrl.Instance:OpenItem(self.data)
end

function LuckyStarRewardItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(self.data.item_id)
    self.node_list.icon.image:LoadSprite(bundle, asset, function()
        self.node_list.icon.image:SetNativeSize()
    end)
end
]]--