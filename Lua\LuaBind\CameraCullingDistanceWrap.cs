﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CameraCullingDistanceWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(CameraCullingDistance), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("UpdateDistances", UpdateDistances);
		<PERSON><PERSON>unction("SetDistance", SetDistance);
		L<PERSON>RegFunction("__eq", op_Equality);
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>ar("cullDistances", get_cullDistances, set_cullDistances);
		L<PERSON>EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UpdateDistances(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			CameraCullingDistance obj = (CameraCullingDistance)ToLua.CheckObject<CameraCullingDistance>(L, 1);
			obj.UpdateDistances();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDistance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CameraCullingDistance obj = (CameraCullingDistance)ToLua.CheckObject<CameraCullingDistance>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.SetDistance(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cullDistances(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraCullingDistance obj = (CameraCullingDistance)o;
			float[] ret = obj.cullDistances;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cullDistances on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cullDistances(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CameraCullingDistance obj = (CameraCullingDistance)o;
			float[] arg0 = ToLua.CheckNumberArray<float>(L, 2);
			obj.cullDistances = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cullDistances on a nil value");
		}
	}
}

