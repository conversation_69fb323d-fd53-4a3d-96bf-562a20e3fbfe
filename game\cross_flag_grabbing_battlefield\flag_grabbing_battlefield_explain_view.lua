FlagGrabbingBattleFieldExplainView = FlagGrabbingBattleFieldExplainView or BaseClass(SafeBaseView)

function FlagGrabbingBattleFieldExplainView:__init()
	self:SetMaskBg()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 5), sizeDelta = Vector2(964, 662)})
    self:AddViewResource(0, "uis/view/country_map_ui/flag_grabing_battlefield_ui_prefab", "layout_fgb_explain_view")
end

function FlagGrabbingBattleFieldExplainView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FlagGrabbingBattlefield.ExplainTitle
    local need_jump = CrossFlagGrabbingBattleFieldWGData.Instance:NeedJumpFGBExplainView()
    self.node_list.btn_fgb_focus_gou:CustomSetActive(need_jump)
    self.end_jump = need_jump
    self.node_list["btn_fgb_know_text"].text.text = string.format(Language.Common.Confirm1)

    for i = 0, 3 do
        self.node_list["fgb_explain_text" .. i].text.text = Language.FlagGrabbingBattlefield.FGBExplainDesc[i] or ""
    end

    XUI.AddClickEventListener(self.node_list.btn_fgb_focus, BindTool.Bind(self.OnClickFGBFocusBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_fgb_know, BindTool.Bind(self.OnClickFGBKnowBtn, self))
end

function FlagGrabbingBattleFieldExplainView:ReleaseCallBack()
    self.end_jump = nil
    CountDownManager.Instance:RemoveCountDown("fgb_explain_time")
end

function FlagGrabbingBattleFieldExplainView:OnFlush(param_t, key)
    -- for i = 0, 3 do
    --     local bundle, asset = ResPath.GetCrossFGBPathImg("a1_fgb_explain_" .. i)
    --     self.node_list["fgb_explain_img" .. i].image:LoadSprite(bundle, asset)
    -- end

    for k, v in pairs(param_t) do
        if k == "flush_count_dowm" then
            local need_count_down = v.need_count_down or false
            self:FlushCountDown(need_count_down)
        end
    end
end

function FlagGrabbingBattleFieldExplainView:FlushCountDown(need_count_down)
    CountDownManager.Instance:RemoveCountDown("fgb_explain_time")
    self.node_list["btn_fgb_know_text"].text.text = string.format(Language.Common.Confirm1)

    if need_count_down then
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local auto_close_time = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBShowExplainTipTime()
        local close_time = server_time + auto_close_time
        self.node_list["btn_fgb_know_text"].text.text = string.format(Language.FlagGrabbingBattlefield.FGBExplainBtnOK, auto_close_time)

        CountDownManager.Instance:AddCountDown("fgb_explain_time",
            function (elapse_time, total_time)
                if not self.node_list["btn_fgb_know_text"] then
                    return
                end
        
                local last_time = math.floor(total_time - elapse_time)
                self.node_list["btn_fgb_know_text"].text.text = string.format(Language.FlagGrabbingBattlefield.FGBExplainBtnOK, last_time)
                self.node_list.fgb_explain_close_tip.text.text = string.format(Language.Task.AutoClose2, last_time)
            end,
            function ()
                self.node_list.fgb_explain_close_tip.text.text = ""
                CrossFlagGrabbingBattleFieldWGData.Instance:SetFGBExplainFocusState(self.end_jump)
                self:Close()
            end,
            close_time, nil, 1)
    end
end

function FlagGrabbingBattleFieldExplainView:OnClickFGBFocusBtn()
    local new_state = not self.end_jump
    self.node_list.btn_fgb_focus_gou:CustomSetActive(new_state)
    self.end_jump = new_state
end

function FlagGrabbingBattleFieldExplainView:OnClickFGBKnowBtn()
    CrossFlagGrabbingBattleFieldWGData.Instance:SetFGBExplainFocusState(self.end_jump)
    self:Close()
end