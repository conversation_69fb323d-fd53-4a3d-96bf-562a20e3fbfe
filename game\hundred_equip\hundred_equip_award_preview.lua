HundredEquipAwardPreView = HundredEquipAwardPreView or BaseClass(SafeBaseView)

function HundredEquipAwardPreView:__init()
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 560)})
	self:AddViewResource(0, "uis/view/hundred_equip_ui_prefab", "hundred_equip_award_preview")
	self.view_layer = UiLayer.Pop
end

function HundredEquipAwardPreView:__deelete()
end

function HundredEquipAwardPreView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function HundredEquipAwardPreView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.HundredEquip.RewardPreview
	self.reward_list = AsyncListView.New(HundredEquipAwardCell, self.node_list.list_1)
end

function HundredEquipAwardPreView:OnFlush()
	local data = HundredEquipWGData.Instance:GetPreAwards()
	self.reward_list:SetDataList(data)

	local hour = HundredEquipWGData.Instance:GetOtherCfgByName("hour")
	local minutes = HundredEquipWGData.Instance:GetOtherCfgByName("minutes")
	self.node_list.txt_rank.text.text = string.format(Language.HundredEquip.RankTips, hour, minutes)
end



-------------------------------------------------------------------------------------
-- 排名奖励item
----------------------------------------------------------------------------------------------
HundredEquipAwardCell = HundredEquipAwardCell or BaseClass(BaseRender)

function HundredEquipAwardCell:__delete()
	if self.rewarditem_cells then
		self.rewarditem_cells:DeleteMe()
		self.rewarditem_cells = nil
	end
end

function HundredEquipAwardCell:LoadCallBack()
	self.rewarditem_cells = AsyncListView.New(ItemCell, self.node_list.reward_list)
	self.rewarditem_cells:SetStartZeroIndex(true)
end

function HundredEquipAwardCell:OnFlush()
	local data = self.data
	self.rewarditem_cells:SetDataList(data.reward_item)
	local str = ""
	if data.min_rank == data.max_rank then
		str = tostring(data.min_rank)
	else
		str = string.format("%s~%s", data.min_rank, data.max_rank)
	end
	self.node_list["desc"].text.text = str
end