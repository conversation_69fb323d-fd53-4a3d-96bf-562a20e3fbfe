NewFestivalPrayerBuyView = NewFestivalPrayerBuyView or BaseClass(SafeBaseView)

function NewFestivalPrayerBuyView:__init()
	self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/new_festival_activity_ui_prefab", "layout_prayer_buy_view")
end

function NewFestivalPrayerBuyView:LoadCallBack()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
            show_gold = true, show_bind_gold = true,
            show_coin = true, show_silver_ticket = true,
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

    if not self.buy_prayer_list then
        self.buy_prayer_list = AsyncListView.New(NfaPrayerBuyItem, self.node_list["buy_prayer_list"])
    end
end

function NewFestivalPrayerBuyView:ReleaseCallBack()
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

    if self.buy_prayer_list then
		self.buy_prayer_list:DeleteMe()
		self.buy_prayer_list = nil
	end
end

function NewFestivalPrayerBuyView:OnFlush()
    local buy_list = NewFestivalPrayerWGData.Instance:GetPrayerBuyList()
    self.buy_prayer_list:SetDataList(buy_list)
end