-- 灵宠选择吞噬选择界面
ControlBeastsSwallowSelect = ControlBeastsSwallowSelect or BaseClass(SafeBaseView)

function ControlBeastsSwallowSelect:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
                        {sizeDelta = Vector2(706, 488)})
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "layout_beasts_swallow_select")
    self.view_name = "ControlBeastsSwallowSelect"
    self:SetMaskBg(true)
end

function ControlBeastsSwallowSelect:ReleaseCallBack()
	if nil ~= self.batch_item_grid then
		self.batch_item_grid:DeleteMe()
    end
    self.batch_item_grid = nil

    if nil ~= self.star_list_view then
		self.star_list_view:DeleteMe()
        self.star_list_view = nil
    end

    if nil ~= self.pinzhi_list_view then
		self.pinzhi_list_view:DeleteMe()
        self.pinzhi_list_view = nil
    end

    self.show_data = nil
    self.ok_callback = nil
    self.need_num = nil
    self.cur_select_num = nil
    self.select_pinzhi_index = nil
    self.select_pinzhi_data = nil
    self.select_pinzhi_status = nil
end

function ControlBeastsSwallowSelect:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName2
    
    self.batch_item_grid = BeastSwallowBatchGrid.New(self)
    self.batch_item_grid:SetStartZeroIndex(false)
    self.batch_item_grid:SetIsShowTips(false)
    self.batch_item_grid:SetNoSelectState(false)
    self.batch_item_grid:SetIsMultiSelect(true)                       --  ,change_cells_num = 2
    self.batch_item_grid:CreateCells({col = 7 ,change_cells_num = 2, cell_count = 35,
    list_view = self.node_list["ph_item_grid"], itemRender = BeastBatchSelectRender})   ---这里使用了批量选取的那个render
    self.batch_item_grid:SetSelectCallBack(BindTool.Bind(self.SelectShenShouBagCellCallBack, self))

    -- 品质
	self.pinzhi_list_view = AsyncListView.New(BeastsSwallowPinZhiListRender, self.node_list["ph_pinzhi_list"])
	self.pinzhi_list_view:SetSelectCallBack(BindTool.Bind1(self.SelectPinZhiItemCallBack, self))
	self.pinzhi_list_view:SetDataList(Language.ContralBeasts.SelectSwallowPinJieName)
    
    self.node_list["btn_sure"].button:AddClickListener(BindTool.Bind(self.OnClickSure, self))
    self.node_list["close_pinzhi"].button:AddClickListener(BindTool.Bind(self.OnClickClosePinzhi, self))
    self.node_list["layout_pinzhi"].button:AddClickListener(BindTool.Bind(self.OnClickLayoutPinzhi, self))
end

function ControlBeastsSwallowSelect:SetOkBack(callback)
	self.ok_callback = callback
end

function ControlBeastsSwallowSelect:SelectShenShouBagCellCallBack(cell)
	self:FlushAddNum()
end

-- 点击选择品阶
function ControlBeastsSwallowSelect:SelectPinZhiItemCallBack(item, cell_index, is_default, is_click)
    if not item.data then
        return
    end

    if cell_index == self.select_pinzhi_index then
        return
    end

    self.select_pinzhi_index = cell_index
    self.select_pinzhi_data = item.data
    self.batch_item_grid:CancleAllSelectCell()
    self:OnClickClosePinzhi()
    self:Flush()
end

function ControlBeastsSwallowSelect:SetData(show_data)
    self.show_data = show_data

    if self:IsLoaded() then
        self:Flush()
    end
end

function ControlBeastsSwallowSelect:OnFlush()
    self:FlusPinZhiStatus()
    self:FlushDataList()
    self:FlushAddNum()
end

-- 检测是否为已选择的特殊灵兽
function ControlBeastsSwallowSelect:CheckisSpecial(bag_id, is_egg)
    if self.show_data and self.show_data.special_list then
        for _, special_data in ipairs(self.show_data.special_list) do
            if special_data.beast_data and special_data.beast_data.bag_id == bag_id and special_data.is_egg == is_egg then
                return true
            end
        end
    end

    return false
end

-- 刷新按钮状态
function ControlBeastsSwallowSelect:FlusPinZhiStatus()
    self.node_list.jiantou_pinzhi_down:CustomSetActive(not self.select_pinzhi_status)
    self.node_list.jiantou_pinzhi_up:CustomSetActive(self.select_pinzhi_status)
    self.node_list.layout_pinzhi_list:CustomSetActive(self.select_pinzhi_status)
end

-- 刷新列表
function ControlBeastsSwallowSelect:FlushDataList()
    if not self.show_data then
        return
    end

    if self.select_pinzhi_index == nil then
        self.select_pinzhi_index = 4
        self.select_pinzhi_data = Language.ContralBeasts.SelectSwallowPinJieName[self.select_pinzhi_index]
        self.select_pinzhi_status = false
    end

    self.pinzhi_list_view:JumpToIndex(self.select_pinzhi_index)
    self.node_list.pinzhi_text.text.text = self.select_pinzhi_data.name
    self:FlusPinZhiStatus()

    local grid_list = {}
    local beasts_list = ControlBeastsWGData.Instance:GetCanSelectBeastsList(self.show_data.aim_beast_id, self.show_data.is_special, self.show_data.is_all)
    for _, beasts_data in ipairs(beasts_list) do
        if beasts_data and beasts_data.beast_data and beasts_data.beast_data.server_data then
            local cur_data = beasts_data.beast_data
            if cur_data.bag_id ~= self.show_data.aim_bag_id then        -- 去除自身
                if self.select_pinzhi_data ~= nil then
                    local data  = ControlBeastsWGData.Instance:GetBeastDataById(cur_data.bag_id)
                    if data and data.server_data then
                        local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(data.server_data.beast_id)
                        if beast_cfg and beast_cfg.beast_star >= self.select_pinzhi_data.min and beast_cfg.beast_star <= self.select_pinzhi_data.max then
                            table.insert(grid_list, beasts_data)
                        end
                    end
                end
            end
        end
    end

    for index, grid_data in ipairs(grid_list) do
        if grid_data then
            local cur_data = grid_data.beast_data
            if self:CheckisSpecial(cur_data.bag_id, grid_data.is_egg) then
                self.batch_item_grid.select_tab[1][index] = true --选中之前选择的
            end
        end
    end

    local new_data = {}
    new_data.is_plus = true
    table.insert(grid_list, new_data)

    if #grid_list <= 1 then
        self.batch_item_grid:CancleAllSelectCell()
    end

    self.batch_item_grid:SetDataList(grid_list)
    self.node_list["ph_item_grid"].scroll_rect.verticalNormalizedPosition = 1
end

-- 刷新数量
function ControlBeastsSwallowSelect:FlushAddNum()
    if not self.show_data then
        return
    end

    self.need_num = self.show_data.need_num
    self.node_list.tip_text:CustomSetActive(self.need_num > 1)

    if self.need_num > 1 then  -- 这个为选取吞噬
        -- 计算吞噬概率
        local pro = 0
        local aim_star = 0
    
        local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.show_data.aim_beast_id)
        if beast_cfg then
            aim_star = beast_cfg.beast_star
        end

        local data_list = self.batch_item_grid:GetAllSelectCell()

        if data_list and #data_list > 0 and aim_star ~= 0 then
            for _, temp_data in ipairs(data_list) do
                if temp_data and temp_data.beast_data and temp_data.beast_data.server_data then
                    local server_data = temp_data.beast_data.server_data
                    local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(server_data.beast_id)
                    if beast_cfg then
                        local temp_por = ControlBeastsWGData.Instance:GetBeastStarFlairPerByStar(aim_star, beast_cfg.beast_star)
                        pro = pro + temp_por / 100
                    end
                end
            end
        end

        local _, decimal = math.modf(pro)
        local str = tostring(pro)
        if decimal > 0 then
            str = string.format(string.format("%.3f", pro))
        end
        self.node_list.tip_text.text.text = string.format(Language.ContralBeasts.CultureText3, str)
    end

    if self.need_num == 1 then
        self.batch_item_grid:SetIsMultiSelect(false)
    end
end

-- 获取数量
function ControlBeastsSwallowSelect:GetNeedNum()
    return self.need_num
end

-- 关闭回调
function ControlBeastsSwallowSelect:CloseCallBack()
    if self.batch_item_grid then
        self.batch_item_grid:CancleAllSelectCell()
    end
end

-- 选择完成
function ControlBeastsSwallowSelect:OnClickSure()
    local data_list = self.batch_item_grid:GetAllSelectCell()
    if self.ok_callback then
        self.ok_callback(self.show_data.is_special, data_list)
    end

    self:Close()
end

-- 关闭选择
function ControlBeastsSwallowSelect:OnClickClosePinzhi()
    self.select_pinzhi_status = false
    self:FlusPinZhiStatus()
end

-- 选择品质
function ControlBeastsSwallowSelect:OnClickLayoutPinzhi()
    self.select_pinzhi_status = true
    self:FlusPinZhiStatus()
end

-------------------------------------------------------------------------------------------------
BeastSwallowBatchGrid = BeastSwallowBatchGrid or BaseClass(ShenShouGrid)

function BeastSwallowBatchGrid:__init(parent)
    self.parent_view = parent
    AsyncBaseGrid.__init(self)
end


function BeastSwallowBatchGrid:__delete()
	for i, v in pairs(self.cell_list) do
		v:DeleteMe()
	end
    self.cell_list = {}
    self.parent_view = nil
    self.old_select_cell = nil
end

-- 选择某个格子回调
function BeastSwallowBatchGrid:SelectCellHandler(cell)
    local select_num = #self:GetAllSelectCell()
	self.cur_index = cell:GetIndex()
    local cell_index = self.cur_index

    if self.is_multi_select then
        if IsEmptyTable(cell:GetData()) or cell:GetData().item_id == 0 then
            return
        end
        if not self.select_tab[1][cell_index] then
            if select_num == self.parent_view:GetNeedNum() then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.HadSelectEnough)
                return
            end
			if self.no_select then
				self.select_callback(cell, true)
				cell:SetSelect(false, true)
				return
			end
			self.select_tab[1][cell_index] = true
		elseif self.select_tab[1][cell_index] then
			self.select_tab[1][cell_index] = nil
		else
			if self.no_select then
				self.select_callback(cell, true)
				cell:SetSelect(false, true)
				return
            end
            if select_num == self.parent_view:GetNeedNum() then
                SysMsgWGCtrl.Instance:ErrorRemind(Language.MountPetEquip.HadSelectEnough)
                return
            end
			self.select_tab[1][cell_index] = true
		end
    else
		for k, v in pairs(self.select_tab[1]) do
			if cell_index == k and v then
				return
			end
		end

		self.select_tab[1] = {}
		self.select_tab[1][cell_index] = true
	end

	if nil ~= self.select_callback then
		self.select_callback(cell)
	end

	-- self.list_view.scroller:RefreshActiveCellViews()
	self:RefreshSelectCellState()
end

function BeastSwallowBatchGrid:SetMultiSelectEffect(num)
	self.select_tab[1] = {}
	for i=1,num do
		self.select_tab[1][i] = true
	end
	self:RefreshSelectCellState()
end

----------------BeastsSwallowPinZhiListRender-------------------
BeastsSwallowPinZhiListRender = BeastsSwallowPinZhiListRender or BaseClass(BaseRender)
function BeastsSwallowPinZhiListRender:OnFlush()
	if not self.data then return end
	self.node_list.lbl_pinzhi_name.text.text = self.data.name
end

-- 选择状态改变
function BeastsSwallowPinZhiListRender:OnSelectChange(is_select)
	if self.node_list.selectpinzhi_bg then
		self.node_list.selectpinzhi_bg:SetActive(is_select)
	end
end
