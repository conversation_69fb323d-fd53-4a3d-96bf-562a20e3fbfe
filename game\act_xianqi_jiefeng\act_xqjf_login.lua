

function ActXianQiJieFengView:InitLoginRewarView()
	self.login_reward_item_list = AsyncListView.New(XianQiJieFengLoginRewardItem, self.node_list.login_reward_list)
	XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnBtnTipClickHnadler, self))
	
	self.role_vip_level_change_event = BindTool.Bind1(self.FlushLoginView, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_vip_level_change_event, {"vip_level"})

	self:LoginTimeCountDown()
end

function ActXianQiJieFengView:ReleaseLoginView()
	if self.login_reward_item_list then
		self.login_reward_item_list:DeleteMe()
		self.login_reward_item_list = nil
	end
	
	if self.role_vip_level_change_event then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_vip_level_change_event)
		self.role_vip_level_change_event = nil
	end

	if self.login_count_down and CountDownManager.Instance:HasCountDown(self.login_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.login_count_down)
	end
end

function ActXianQiJieFengView:LoginShowIndexCallBack()
	self:FlushLoginView()
end

function ActXianQiJieFengView:FlushLoginView()
	self:FlushLoginRewardList()
	local theme_cfg = ActXianQiJieFengWGData.Instance:GetActivityThemeCfg(TabIndex.xianqi_jiefeng_login)
	if theme_cfg ~= nil then
		self.node_list.tip_label.text.text = theme_cfg.rule_tip
		self.node_list.tip_desc.text.text = theme_cfg.rule_desc
	end
end

function ActXianQiJieFengView:OnBtnTipClickHnadler()
	local role_tip = RuleTip.Instance
	if role_tip then
		local title,desc = ActXianQiJieFengWGData.Instance:GetActivityTip(TabIndex.xianqi_jiefeng_login)
		if title ~= nil and desc ~= nil then
			role_tip:SetTitle(title)
			role_tip:SetContent(desc)
		end
	end
end

function ActXianQiJieFengView:FlushLoginRewardList()
	ActXianQiJieFengWGData.Instance:FlushLoginRewardInfo()
	if self.login_reward_item_list then
		local cfg_list = ActXianQiJieFengWGData.Instance:GetLoginRewardCfg()
		local info_list = ActXianQiJieFengWGData.Instance:GetLoginDayData()
		if info_list then
			local day_list = info_list.day_list
			local data_list = {}
			local ylq_status = ActivityRewardState.YLQ
			local klq_status = ActivityRewardState.KLQ
			for k,v in pairs(cfg_list) do
				if day_list[v.day_index].common_gift_state == klq_status or day_list[v.day_index].special_gift_state == klq_status then
					data_list[v.day_index] = v
				elseif day_list[v.day_index].common_gift_state == ylq_status then
					if day_list[v.day_index].special_gift_state == ylq_status then
						data_list[v.day_index + 10000] = v
					else
						data_list[v.day_index + 1000] = v
					end
				else
					data_list[v.day_index + 100] = v
				end
			end
			data_list = SortTableKey(data_list)
			self.login_reward_item_list:SetDataList(data_list)
		elseif cfg_list then
			self.login_reward_item_list:SetDataList(cfg_list)
		end
	end
end

--有效时间倒计时
function ActXianQiJieFengView:LoginTimeCountDown()
	self.login_count_down = "login_count_down"
	local invalid_time = ActXianQiJieFengWGData.Instance:GetActivityInValidTime(TabIndex.xianqi_jiefeng_login)
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list.time_label.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown(self.login_count_down, BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function ActXianQiJieFengView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.login_act_date = TimeUtil.FormatUnixTime2Date()
		self.node_list.time_label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function ActXianQiJieFengView:OnComplete()
	self.node_list.time_label.text.text = Language.Activity.TianshenRoadLoginTime
end

----------------------------------------------------------------------------------------

XianQiJieFengLoginRewardItem = XianQiJieFengLoginRewardItem or BaseClass(BaseRender)

function XianQiJieFengLoginRewardItem:__delete()
	self.special_reward_list:DeleteMe()
	self.special_reward_list = nil
	if self.normal_reward_list then
		for k,v in pairs(self.normal_reward_list) do
			v:DeleteMe()
		end
		self.normal_reward_list = nil
	end
end

function XianQiJieFengLoginRewardItem:LoadCallBack()
	self.save_day_index = nil
	self.normal_reward_list = {}
	self.special_reward_list = AsyncListView.New(ItemCell, self.node_list.special_reward_list)
	XUI.AddClickEventListener(self.node_list.get_btn, BindTool.Bind(self.OnClickGetBtn, self))
end

function XianQiJieFengLoginRewardItem:OnFlush()
	self:FlushRewardItem()
	self:FlushBtnStatus()
end

function XianQiJieFengLoginRewardItem:FlushRewardItem()
	local data = self:GetData()
	if self.save_day_index == data.day_index then
		return
	end
	self.save_day_index = data.day_index

	if data.vip_lv > 0 then
		local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(data.special_reward_item)
		self.special_reward_list:SetDataList(reward_list)
	end

	local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(data.reward_item)
	self:SetNormalRewardList(reward_list)
	
	self.node_list.normal_tag:SetActive(data.vip_lv > 0)
	self.node_list.special_group:SetActive(data.vip_lv > 0)
	--self.node_list.day_label.text.text = string.format(Language.XianQiJieFengAct.LoginStr1, data.day_index)
	--self.node_list.day_label.image:LoadSprite(ResPath.GetXianQiJieFengImagePath("qfbz_tian_" .. data.day_index))
	self.node_list.day_label.text.text = string.format(Language.XianQiJieFengAct.LoginDay, data.day_index)
	self.node_list.special_tag_label.text.text = string.format(Language.XianQiJieFengAct.Vipstr3, data.vip_lv)
end

-- ******要自适应一个底图
function XianQiJieFengLoginRewardItem:SetNormalRewardList(reward_list)
	local root = self.node_list.normal_reward_list
	local cell_list = self.normal_reward_list or {}
	for i = #cell_list + 1, #reward_list do
		local cell = ItemCell.New(root)
		cell:SetData(reward_list[i])
		cell_list[i] = cell
	end
	self.normal_reward_list = cell_list
end

function XianQiJieFengLoginRewardItem:FlushBtnStatus()
	local data = self:GetData()
	local day_info = ActXianQiJieFengWGData.Instance:GetLoginRewardInfo(data.day_index)
	if not day_info then
		return
	end

	self.node_list.btn_label.text.text = Language.XianQiJieFengAct.BtnStr2

	if day_info.common_gift_state == ActivityRewardState.YLQ then
		if day_info.special_gift_state == ActivityRewardState.YLQ or data.vip_lv <= 0 then
			self.node_list.get_btn:SetActive(false)
			self.node_list.ylg_img:SetActive(true)
			return
		elseif day_info.special_gift_state == ActivityRewardState.BKL then
			self.node_list.btn_label.text.text = string.format(Language.XianQiJieFengAct.Vipstr2, data.vip_lv)
		end
	end
	self.node_list.get_btn:SetActive(true)
	self.node_list.ylg_img:SetActive(false)

	local can_get = day_info.common_gift_state == ActivityRewardState.KLQ or day_info.special_gift_state == ActivityRewardState.KLQ
	self.node_list.red_point:SetActive(can_get)
	XUI.SetButtonEnabled(self.node_list.get_btn, can_get)
end

function XianQiJieFengLoginRewardItem:OnClickGetBtn()
	local data = self:GetData()
	local day_info = ActXianQiJieFengWGData.Instance:GetLoginRewardInfo(data.day_index)
	if not day_info then
		return
	end

	if day_info.common_gift_state == ActivityRewardState.KLQ then
		ActXianQiJieFengWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DENGLUYOULI, TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE.COMMON_REWARD, data.day_index)
	end

	if day_info.special_gift_state == ActivityRewardState.KLQ then
		ActXianQiJieFengWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DENGLUYOULI, TIANSHEN_THEME_LOGIN_GIFT_OP_TYPE.SPECIAL_REWARD, data.day_index)
	end
end