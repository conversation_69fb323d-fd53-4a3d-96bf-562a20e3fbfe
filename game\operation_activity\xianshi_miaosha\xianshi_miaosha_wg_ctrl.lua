require("game/operation_activity/xianshi_miaosha/xianshi_miaosha_view")
require("game/operation_activity/xianshi_miaosha/xianshi_miaosha_tips")
require("game/operation_activity/xianshi_miaosha/xianshi_miaosha_wg_data")
XianShiMiaoShaWGCtrl = XianShiMiaoShaWGCtrl or BaseClass(BaseWGCtrl)

function XianShiMiaoShaWGCtrl:__init()
	if XianShiMiaoShaWGCtrl.Instance then
		ErrorLog("[XianShiMiaoShaWGCtrl] Attemp to create a singleton twice !")
	end
	XianShiMiaoShaWGCtrl.Instance = self
	self.xianshi_miaosha_data = XianshiMiaoshaWGData.New()
	self.xianshi_miaosha_tips = XianShiMiaoShaTips.New()

	self:RegisterAllProtocols()

	self.refresh_remind_flag_list = {}

	OperationActivityWGCtrl.Instance:ListenHotUpdate(XianshiMiaoshaWGData.ConfigPath, BindTool.Bind(self.OnHotUpdate, self))

end

function XianShiMiaoShaWGCtrl:__delete()
	XianShiMiaoShaWGCtrl.Instance = nil

	if self.xianshi_miaosha_data ~= nil then
		self.xianshi_miaosha_data:DeleteMe()
		self.xianshi_miaosha_data = nil
	end

	if self.xianshi_miaosha_tips ~= nil then
		self.xianshi_miaosha_tips:DeleteMe()
		self.xianshi_miaosha_tips = nil
	end
end

function XianShiMiaoShaWGCtrl:RegisterAllProtocols()
	-----------------------------------限时秒杀协议------------------------------------
	-- self:RegisterProtocol(CSOATimedSpikeOpera)
	self:RegisterProtocol(SCOATimedSpikeInfo, "OnSCOATimedSpikeInfo")
	self:RegisterProtocol(SCOATimedSpikeItemUpdate, "OnSCOATimedSpikeItemUpdate")
	self:RegisterProtocol(SCOATimedSpikeQuotaUpdate, "OnSCOATimedSpikeQuotaUpdate")
	self:RegisterProtocol(SCOATimedSpikeBuyTimesInfo, "OnSCOATimedSpikeBuyTimesInfo")
	-----------------------------------------------------------------------------------
end

-----------------------------------------------------------限时秒杀----------------------------------------------------------------

--2624// 运营活动-限时秒杀信息
function XianShiMiaoShaWGCtrl:OnSCOATimedSpikeInfo(protocol)
	-- print_error("--2624// 运营活动-限时秒杀信息",protocol)
	self.xianshi_miaosha_data:SetSCOATimedSpikeInfo(protocol)

	self:CreateOpenMiaoShaTipsCountDown()

	self:CretaeRemindDelayFunc(protocol.type)

	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_xianshi_miaosha)
	RemindManager.Instance:Fire(RemindName.OperationXianshiMiaosha)
end

--2625// 运营活动-限时秒杀商品更新
function XianShiMiaoShaWGCtrl:OnSCOATimedSpikeItemUpdate(protocol)
	-- print_error("--2625// 运营活动-限时秒杀商品更新",protocol)
	self.xianshi_miaosha_data:SetSCOATimedSpikeItemUpdate(protocol)
	self:CreateOpenMiaoShaTipsCountDown()
	self:CretaeRemindDelayFunc(protocol.type)

	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_xianshi_miaosha)
	RemindManager.Instance:Fire(RemindName.OperationXianshiMiaosha)
end
	
--2626// 运营活动-限时秒杀额度奖励更新
function XianShiMiaoShaWGCtrl:OnSCOATimedSpikeQuotaUpdate(protocol)
	-- print_error("--2626// 运营活动-限时秒杀额度奖励更新",protocol)
	self.xianshi_miaosha_data:SetSCOATimedSpikeQuotaUpdate(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_xianshi_miaosha)
	RemindManager.Instance:Fire(RemindName.OperationXianshiMiaosha)
end

--2627,// 运营活动-限时秒杀购买次数更新
function XianShiMiaoShaWGCtrl:OnSCOATimedSpikeBuyTimesInfo(protocol)
	-- print_error("--2627,// 运营活动-限时秒杀购买次数更新",protocol)
	self.xianshi_miaosha_data:SetSCOATimedSpikeBuyTimesInfo(protocol)

	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_xianshi_miaosha)
	RemindManager.Instance:Fire(RemindName.OperationXianshiMiaosha)
end

--创建红点延迟，用于红点显示
function XianShiMiaoShaWGCtrl:CretaeRemindDelayFunc(type)
	if self.refresh_remind_flag_list[type] then
		GlobalTimerQuest:CancelQuest(self.refresh_remind_flag_list[type])
		self.refresh_remind_flag_list[type] = nil
	end
	local refresh_time_list = self.xianshi_miaosha_data:GetRefreshTimeList()

	if refresh_time_list[type] == nil or refresh_time_list[type] <= 0 then
		return
	end

	--延迟到刷新时间把红点标记设置为true
	if self.refresh_remind_flag_list[type] == nil then
		self.refresh_remind_flag_list[type] = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.DelayRefreshRemindFlag, self, type), refresh_time_list[type] - 1)
	end
end


function XianShiMiaoShaWGCtrl:DelayRefreshRemindFlag(type)
	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	PlayerPrefsUtil.SetInt("refresh_remind_flag" .. type .. role_id, 1)
end

--延迟剩余30秒内打开提示框
function XianShiMiaoShaWGCtrl:CreateOpenMiaoShaTipsCountDown()
	if self.miaosha_tips_quset then
		GlobalTimerQuest:CancelQuest(self.miaosha_tips_quset)
		self.miaosha_tips_quset = nil
	end

	local time

	local refresh_time_list = self.xianshi_miaosha_data:GetRefreshTimeList()
	--拿到最小的时间进行计时
	for i=1,3 do
		if refresh_time_list[i] ~= nil and refresh_time_list[i] > 0 then
			if time == nil then
				time = refresh_time_list[i]
			elseif time > refresh_time_list[i] then
				time = refresh_time_list[i]
			end
		end
	end

	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	--是否勾选今日不在展示提示框 :1(勾选) 0(未勾选)
	local value = PlayerPrefsUtil.GetInt("miaosha_tips_view_" .. role_id .. cur_day)
	--是否勾选界面内的提醒选项 :0(勾选) 1(未勾选)
	local value1 = PlayerPrefsUtil.GetInt("miaosha_tips_toggle_" .. role_id)
	if value1 == 1 or value == 1 then
		return
	end

	if time == nil or time <= 0 then
		return
	end

	local temp_time = 0
	local count_down_time = 0

	if time <= 30 then
		temp_time = 0
		count_down_time = time
	else
		temp_time = time - 30
		count_down_time = 30
	end

	--延迟剩余30秒内打开提示框
	if self.miaosha_tips_quset == nil then
		self.miaosha_tips_quset = GlobalTimerQuest:AddDelayTimer(BindTool.Bind(self.OpenXianShiMiaoshaTips, self, count_down_time), temp_time)
	end
end

--打开限时秒杀提示框
function XianShiMiaoShaWGCtrl:OpenXianShiMiaoshaTips(time)
	if IS_ON_CROSSSERVER or CgManager.Instance:IsCgIng() then
		return
	end

	local state = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_XIANSHI_MIAOSHA)

	if not state then
		return
	end

	local interface_cfg = self.xianshi_miaosha_data:GetInterfaceCfg()

	local yes_callback = function ()
		--打开限时秒杀界面
		OperationActivityWGCtrl.Instance:Open(TabIndex.operation_act_xianshi_miaosha)
	end

	if self.xianshi_miaosha_tips and not self.xianshi_miaosha_tips:IsOpen() and not OperationActivityWGCtrl.Instance:GetViewIsOpen() and interface_cfg then
		self.xianshi_miaosha_tips:SetData(interface_cfg.desc_12, time, yes_callback)
		self.xianshi_miaosha_tips:Open()
	end
end

function XianShiMiaoShaWGCtrl:OnHotUpdate()
	self.xianshi_miaosha_data:FlushXianShiMiaoShaCfg()
	ViewManager.Instance:FlushView(GuideModuleName.OperationActivityView, TabIndex.operation_act_xianshi_miaosha, "xianshi_hot_update")
end

-----------------------------------------------------------------------------------------------------------------------------------