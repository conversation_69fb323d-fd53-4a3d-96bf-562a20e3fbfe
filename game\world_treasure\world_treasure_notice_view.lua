WorldTreasureNoticeView = WorldTreasureNoticeView or BaseClass(SafeBaseView)

local count_down_key = "world_treasure_notice_count_down"

function WorldTreasureNoticeView:__init()
    self:SetMaskBg(true, true)
    self.view_layer = UiLayer.Pop
    self.view_style = ViewStyle.Window

    self:AddViewResource(0, "uis/view/world_treasure_ui_prefab", "layout_world_treasure_notice")
end

function WorldTreasureNoticeView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["today_status"], BindTool.Bind2(self.ChangeTodayTipsCurrStatus, self))
    XUI.AddClickEventListener(self.node_list["btn_jump"], BindTool.Bind(self.OnClickJumpBtn, self))
end

function WorldTreasureNoticeView:ReleaseCallBack()
    CountDownManager.Instance:RemoveCountDown(count_down_key)
end

function WorldTreasureNoticeView:OnFlush()
    self:DBTimeCountDown()
    self:FlushCurrStatus()
end

function WorldTreasureNoticeView:OnClickJumpBtn()
    ViewManager.Instance:Open(GuideModuleName.WorldTreasureView)
    self:Close()
end

-- 改变今日提示状态
function WorldTreasureNoticeView:ChangeTodayTipsCurrStatus()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	local today_notips_open_day = PlayerPrefsUtil.GetInt("tcdb_notice_view_open" .. main_role_id)

    if today_notips_open_day == open_day then
        today_notips_open_day = 0
    else
        today_notips_open_day = open_day
    end

    PlayerPrefsUtil.SetInt("tcdb_notice_view_open" .. main_role_id, today_notips_open_day)
    self:FlushCurrStatus()
end

-- 刷新状态
function WorldTreasureNoticeView:FlushCurrStatus()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local has_today_flag = PlayerPrefsUtil.GetInt("tcdb_notice_view_open" .. main_role_id) == open_day
    self.node_list["today_status_select"]:CustomSetActive(has_today_flag)
end

--有效时间倒计时
function WorldTreasureNoticeView:DBTimeCountDown()
	CountDownManager.Instance:RemoveCountDown(count_down_key)
	local invalid_time = WorldTreasureWGData.Instance:GetCurGradeEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if invalid_time > server_time then
		self.node_list["txt_cur_grade_end_time"].text.text = string.format(Language.WorldTreasure.LimitRefreshTime, TimeUtil.FormatSecondDHM9(invalid_time - server_time))
		CountDownManager.Instance:AddCountDown(count_down_key, BindTool.Bind1(self.UpdateGradeCountDown, self), BindTool.Bind1(self.DBTimeCountDown, self), invalid_time, nil, 1)
	end
end

function WorldTreasureNoticeView:UpdateGradeCountDown(elapse_time, total_time)
	self.node_list["txt_cur_grade_end_time"].text.text = string.format(Language.WorldTreasure.LimitRefreshTime, TimeUtil.FormatSecondDHM9(total_time - elapse_time))
end
