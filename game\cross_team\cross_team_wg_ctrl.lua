require("game/cross_team/cross_team_view")
require("game/cross_team/cross_team_my_team")
require("game/cross_team/cross_team_wg_data")
require("game/cross_team/cross_team_invite_view")
require("game/cross_team/cross_team_apply_view")
require("game/cross_team/cross_team_change_goal_view")
require("game/cross_team/cross_quick_invite_team")
require("game/cross_team/cross_team_invite_list_view")
require("game/cross_team/cross_team_hebing_view")

CrossTeamWGCtrl = CrossTeamWGCtrl or BaseClass(BaseWGCtrl)

function CrossTeamWGCtrl:__init()
	if CrossTeamWGCtrl.Instance then
        ErrorLog("[CrossTeamWGCtrl]:Attempt to create singleton twice!")
        return
	end
	CrossTeamWGCtrl.Instance = self
    self.request_callback_list = {}
    self.invite_callback_list = {}
	self.data = CrossTeamWGData.New()
    self.view = CrossTeamView.New(GuideModuleName.CrossTeamView)
    self.cross_team_invite_view = CrossTeamInviteView.New()
    self.cross_team_apply_view = CrossTeamApplyView.New()
    self.cross_team_change_goal_view = CrossTeamChangeGoalView.New()
    self.cross_quick_invite_team = CrossQuickInviteTeam.New()
    self.team_invite_list_view = CrossTeamInviteListView.New()
    self.hebing_view = CrossTeamHeBingView.New()
    self:RegisterAllProtocals()

end

function CrossTeamWGCtrl:__delete()
    CrossTeamWGCtrl.Instance = nil
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end
    self.request_callback_list = {}
    self.invite_callback_list = {}
    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

    if self.cross_team_invite_view then
        self.cross_team_invite_view:DeleteMe()
        self.cross_team_invite_view = nil
    end

    if self.cross_team_apply_view then
        self.cross_team_apply_view:DeleteMe()
        self.cross_team_apply_view = nil
    end
    
    if self.cross_team_change_goal_view then
        self.cross_team_change_goal_view:DeleteMe()
        self.cross_team_change_goal_view = nil
    end

    if self.cross_quick_invite_team then
        self.cross_quick_invite_team:DeleteMe()
        self.cross_quick_invite_team = nil
    end

    if nil ~= self.team_invite_list_view then
		self.team_invite_list_view:DeleteMe()
		self.team_invite_list_view = nil
	end
end

function CrossTeamWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(CSCrossTeamOperate)
    self:RegisterProtocol(SCCrossTeamList, "OnSCCrossTeamList") --队伍列表
    self:RegisterProtocol(SCCrossTeamAdd, "OnSCCrossTeamAdd")   --队伍列表增加
    self:RegisterProtocol(SCCrossTeamRemove, "OnSCCrossTeamRemove")   --队伍列表减少
    self:RegisterProtocol(CSCrossTeamCreate)    --创建队伍
    self:RegisterProtocol(SCCrossTeamOut, "OnSCCrossTeamOut")   --离开队伍
    self:RegisterProtocol(SCCrossTeamJoin, "OnSCCrossTeamJoin")   --加入队伍
    self:RegisterProtocol(CSCrossTeamInviteUser)  --邀请玩家
    self:RegisterProtocol(SCCrossTeamInviteUserTransmit, "OnSCCrossTeamInviteUserTransmit") -- 被邀请玩家收到的信息
    self:RegisterProtocol(CSCrossTeamInviteUserRet)     -- 邀请玩家玩家操作
    self:RegisterProtocol(SCCrossTeamReqJoinTransmit, "OnSCCrossTeamReqJoinTransmit") -- 队长 收到的入队申请
    self:RegisterProtocol(CSCrossTeamReqJoinRet)  --邀请玩家-队长操作
    self:RegisterProtocol(CSCrossTeamChangeLimit) --改变队伍条件
    self:RegisterProtocol(SCCrossTeamInfo, "OnSCCrossTeamInfo") 	--自己队伍信息
    self:RegisterProtocol(SCCrossTeamInviteChannelChat, "OnSCCrossTeamInviteChannelChat") 	--组队喊话信息返回，只返回当前场景
    self:RegisterProtocol(SCCrossTeamNearRoleList, "OnSCCrossTeamNearRoleList") 	--附近的人的列表
    self:RegisterProtocol(CSQueryRoleCrossTeamInfo)
    self:RegisterProtocol(SCQueryRoleCrossTeamRet, "OnSCQueryRoleCrossTeamRet")
    
    self:RegisterProtocol(CSCrossTeamReqLeaderRet)
    self:RegisterProtocol(SCCrossTeamReqLeaderTransmit, "OnSCCrossTeamReqLeaderTransmit")

    self:RegisterProtocol(CSCrossTeamMergeReq)
    self:RegisterProtocol(CSCrossTeamOneKeyMergeReq)
    self:RegisterProtocol(SCCrossTeamMergeReqTransmit, "OnSCCrossTeamMergeReqTransmit")
    
    self:RegisterProtocol(CSCrossTeamNoLongerOperateReq)
end

-- CROSS_TEAM_OPERATE_TYPE_TEAM_LIST,						//1 队伍列表
-- CROSS_TEAM_OPERATE_TYPE_DISMISS,						    //2 解散队伍
-- CROSS_TEAM_OPERATE_TYPE_KICK_OUT,						//3 踢出队友		param1:member_index
-- CROSS_TEAM_OPERATE_TYPE_CHANGE_LEADER,					//4 变更队长		param1:member_index
-- CROSS_TEAM_OPERATE_TYPE_CHANGE_MUST_CHECK,				//5 入队验证变更 param1:is_check
-- CROSS_TEAM_OPERATE_TYPE_CHANGE_MEMBER_CAN_INVITE,		//6 队员邀请变更 param1:can_invite
-- CROSS_TEAM_OPERATE_TYPE_EXIT,							//7 退出队伍
-- CROSS_TEAM_OPERATE_TYPE_JOIN,							//8 加入队伍		param:team_index
-- CROSS_TEAM_OPERATE_TYPE_TEAM_INFO                        //9 队伍信息
-- CROSS_TEAM_OPERATE_TYPE_TEAM_INVITE_CHANNEL_CHAT         //10 组队喊话
-- CROSS_TEAM_OPERATE_TYPE_NEAR_ROLE                        //11 附近的人
-- CROSS_TEAM_OPERATE_TYPE_REQ_LEADER = 12,                    -- 申请队长
-- CROSS_TEAM_OPERATE_TYPE_INVITE_SHOUTING = 13,               -- 组队喊话
function CrossTeamWGCtrl:SendTeamOperaReq(operate_type, param1, param2, param3, param4)
    --print_error(">>>SendTeamOperaReq>>>>>>>>",operate_type, param1, param2, param3, param4)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamOperate)
  	protocol.operate_type = operate_type or 0
  	protocol.param1 = param1 or 0
  	protocol.param2 = param2 or 0
    protocol.param3 = param3 or 0
    protocol.param4 = param4 or 0
  	protocol:EncodeAndSend()
end

function CrossTeamWGCtrl:OnSCCrossTeamList(protocol)
    --print_error(">>>OnSCCrossTeamList 队伍列表>>>>>>>>",protocol)
    self.data:SaveTeamList(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
    self:ForceFlushInviteView()
end

function CrossTeamWGCtrl:OnSCCrossTeamAdd(protocol)
    --print_error(">>>OnSCCrossTeamAdd 队伍列表增加>>>>>>>>",protocol)
    self.data:AddToTeamList(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
    self:ForceFlushInviteView()
end

function CrossTeamWGCtrl:OnSCCrossTeamRemove(protocol)
    --print_error(">>>OnSCCrossTeamRemove 队伍列表减少>>>>>>>>",protocol)
    self.data:RemoveToTeamList(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
    self:ForceFlushInviteView()
end

function CrossTeamWGCtrl:SendCreateTeamReq(must_check, member_can_invite, capability_limit, min_level_limit, max_level_limit)
    --print_error(">>>SendCreateTeamReq 创建队伍>>>>>>>>",must_check, member_can_invite, capability_limit, min_level_limit, max_level_limit)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamCreate)
  	protocol.must_check = must_check or 0
  	protocol.member_can_invite = member_can_invite or 1
  	protocol.capability_limit = capability_limit or 0
    protocol.min_level_limit = min_level_limit or 1
    protocol.max_level_limit = max_level_limit or COMMON_CONSTS.MaxRoleLevel
  	protocol:EncodeAndSend()
end

--enum TEAM_OUT_REASONTEAM_OUT_REASON_DISMISS = 0,	//0 队伍解散 
--TEAM_OUT_REASON_KICK_OFF,		                    //1 被踢出队伍
--TEAM_OUT_REASON_SECEDE,			                //2 自己退出
--TEAM_OUT_REASON_TEAM_MERGE,		                //3 队伍合并
function CrossTeamWGCtrl:OnSCCrossTeamOut(protocol)
    --print_error(">>>OnSCCrossTeamOut 离开队伍>>>>>>>>",protocol)
    local reason = protocol.reason
    local uuid = protocol.uuid
    local leader_uuid = protocol.leader_uuid
    local leader_name = protocol.leader_name
    local who = ""
    local my_uuid = RoleWGData.Instance:GetUUid()
    if uuid == my_uuid then
        if reason == TEAM_OUT_REASON.TEAM_OUT_REASONTEAM_OUT_REASON_DISMISS or reason == TEAM_OUT_REASON.TEAM_OUT_REASON_KICK_OFF
        or reason == TEAM_OUT_REASON.TEAM_OUT_REASON_SECEDE then
            self.data:ClearTeamData()
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, 0)
            NewTeamWGCtrl.Instance:CloseInviteView()
            local role = Scene.Instance:GetMainRole()
            role:SetAttr("is_team_leader", 0)
            who = Language.Society.You
        end
    else
        local old_info = self.data:GetTeamMemberList()
        for k, v in pairs(old_info) do
            if v.uuid == uuid then
                who = v.name
            end
        end
        --请求一下队伍信息 TODO
        CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_INFO)
    end

    local text_format = ""
    if TEAM_OUT_REASON.TEAM_OUT_REASONTEAM_OUT_REASON_DISMISS == protocol.reason then
		text_format = Language.Society.JieSanTeam
	elseif TEAM_OUT_REASON.TEAM_OUT_REASON_KICK_OFF == protocol.reason then
		text_format = Language.Society.TiChuTeam
	else
		text_format = Language.Society.LeaveTeam
    end

    if TEAM_OUT_REASON.TEAM_OUT_REASON_SECEDE == protocol.reason and leader_uuid.temp_low > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Society.LeaveTeamNewLeader, who, leader_name))
	else
		--合并不弹提示，服务器弹
		if TEAM_OUT_REASON.TEAM_OUT_REASON_TEAM_MERGE ~= protocol.reason then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(text_format, who))
		end
	end

    --退出队伍的时候, 如果正在全部队伍页面，请求一下当前列表
    if self.view:IsOpenTeamList() then
        CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_LIST)
    end

    ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
    GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)
    self:TeamInfoChangeCallBack(uuid)
end
-- TEAM_JOIN_REASON_Invalid = 0,
-- TEAM_JOIN_REASON_NoReason, 1
-- TEAM_JOIN_REASON_CrossInviteAutoAddMember,2			// 邀请跨服自动组队
-- TEAM_JOIN_REASON_TeamMergeMember,3					// 队伍合并
function CrossTeamWGCtrl:OnSCCrossTeamJoin(protocol)
    --print_error(">>>OnSCCrossTeamJoin 加入队伍>>>>>>>>",protocol)
    if RoleWGData.Instance:GetUUid() == protocol.uuid then
		--合并加入的队伍不弹提示
		if protocol.reason ~= TEAM_JOIN_REASON.TEAM_JOIN_REASON_TeamMergeMember then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.MyselfJoinTeamTis)
		end

		--跨服邀请进队，服务器拉进队伍的，不打开界面
		if protocol.reason and protocol.reason ~= TEAM_JOIN_REASON.TEAM_JOIN_REASON_CrossInviteAutoAddMember then
			--关闭当前界面
			--ViewManager.Instance:Close(GuideModuleName.NewTeamQuickView)
            ViewManager.Instance:Open(GuideModuleName.CrossTeamView)
		end
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, 0)
	else
		if protocol.reason ~= TEAM_JOIN_REASON.TEAM_JOIN_REASON_TeamMergeMember then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Society.JoinTeam, protocol.user_name))
		end

        --请求一下队伍信息 TODO
        CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_INFO)

		if self.view:IsOpenTeamList() then
            CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_TEAM_LIST)
		end
    end
    GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)
    self:TeamInfoChangeCallBack(protocol.uuid)
end

function CrossTeamWGCtrl:SendInviteTeamReq(uuid)
    --print_error(">>>SendInviteTeamReq 邀请玩家>>>>>>>>",uuid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamInviteUser)
  	protocol.uuid = uuid or 0
  	protocol:EncodeAndSend()
end

-- 被邀请玩家收到的信息
function CrossTeamWGCtrl:OnSCCrossTeamInviteUserTransmit(protocol)
    --print_error(">>>OnSCCrossTeamInviteUserTransmit 被邀请玩家收到的信息>>>>>>>>",protocol)
    self.data:SaveInviteUserInfo(protocol)
	--MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, num, function ()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, self.data:GetInviteListSize(), function ()
		self.team_invite_list_view:Open()
		return true
	end)

    --打开被邀请界面，显示红点。。。
    self.data:GetQuickInviteFromUserTransmit(protocol)
end

function CrossTeamWGCtrl:DeleteReq(role_id)
	if self.team_invite_list_view:IsOpen() then
		self.team_invite_list_view:DeleteReq(role_id)
	end
end

function CrossTeamWGCtrl:OpenCustomMenu(buff,info,pos)
    self.view:OpenCustomMenu(buff,info,pos)
end

function CrossTeamWGCtrl:GetTodayCheckActive()
	return self.team_invite_list_view:GetTodayCheckActive()
end

function CrossTeamWGCtrl:GetApplayTodayCheckActive()
	return self.cross_team_apply_view:GetTodayCheckActive()
end

function CrossTeamWGCtrl:OpenApplyView()
	self.cross_team_apply_view:Open()
end
--处理申请
function CrossTeamWGCtrl:RemoveTeamJoinReq(uuid)
	if nil == uuid then
		return
	end

	CrossTeamWGData.Instance:RemoveTeamJoinReq(uuid)
	if CrossTeamWGData.Instance:GetReqTeamListSize() <= 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)
		ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
		MainuiWGCtrl.Instance:FlushView(0, "team_cell_flush")
		if self.cross_team_apply_view:IsOpen() then
			self.cross_team_apply_view:Close()
		end
		return
	end

	self.cross_team_apply_view:Flush()
end

function CrossTeamWGCtrl:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
	self.team_invite_list_view:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
end

function CrossTeamWGCtrl:RefuseInviteTeam(uuid)
	self:SendInviteUserRet(uuid, 0)
	GlobalTimerQuest:AddDelayTimer(function ()
		self:DeleteReq(uuid)
	end, 0)
end

 -- 邀请玩家玩家操作
function CrossTeamWGCtrl:SendInviteUserRet(inviter_uuid, result)
    --print_error(">>>SendInviteUserRet 邀请玩家玩家的操作>>>>>>>result>",result, inviter_uuid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamInviteUserRet)
  	protocol.inviter_uuid = inviter_uuid or 0
  	protocol.result = result or 0
  	protocol:EncodeAndSend()
end

-- 队长 收到的入队申请
function CrossTeamWGCtrl:OnSCCrossTeamReqJoinTransmit(protocol)
    --print_error(">>>OnSCCrossTeamReqJoinTransmit 队长收到的入队申请>>>>>>>result>",protocol)
    self.data:SaveReqJoinUserInfo(protocol)
    --显示请求入队信息，显示红点
    local num = self.data:GetReqTeamListSize() > 0 and 1 or 0
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, num, function ()
		CrossTeamWGCtrl.Instance:OpenApplyView()
		return true
	end)
	MainuiWGCtrl.Instance:ReloadTeamList()
	self.cross_team_apply_view:Flush()
	ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
end

-- 邀请玩家队长操作
function CrossTeamWGCtrl:SendTeamReqJoinRet(req_uuid, result)
    --print_error(">>>SendTeamReqJoinRet 邀请玩家队长操作>>>>req_uuid>>>result>",req_uuid, result)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamReqJoinRet)
  	protocol.req_uuid = req_uuid or 0
  	protocol.result = result or 0
  	protocol:EncodeAndSend()
end

function CrossTeamWGCtrl:OpenChangeGoalView(is_pingtai)
	self.cross_team_change_goal_view:SetIsPingTai(is_pingtai)
	self.cross_team_change_goal_view:Open()
end

function CrossTeamWGCtrl:SendChangeTeamLimit(capability_limit, min_level_limit, max_level_limit)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamChangeLimit)
  	protocol.capability_limit = capability_limit or 0
    protocol.min_level_limit = min_level_limit or 0
    protocol.max_level_limit = max_level_limit or 0
  	protocol:EncodeAndSend()
end

function CrossTeamWGCtrl:OnSCCrossTeamInfo(protocol)
    --print_error(">>>OnSCCrossTeamInfo 我的队伍>>>>>>>>",protocol)
    local old_member_list = CrossTeamWGData.Instance:GetTeamMemberList()
    self.data:SaveMyTeamInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView, nil, "jump_my_team")

    local team_tag = CrossTeamWGData.Instance:GetIsInTeam() --记录上一次是否有组队
	--如果有队伍，判断上一次的数量与现在的数量的差 是否大于 0
	--如果大于0，说明有人加入了队伍

	local new_member_list = CrossTeamWGData.Instance:GetTeamMemberList()

	local check_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	if check_role_id ~= nil then
		local check_flag = false
		for k,v in pairs(old_member_list) do
			if v.uuid.temp_low == check_role_id then
				check_flag = true
				break
			end
		end

		if not check_flag then
			for k,v in pairs(new_member_list) do
				if v.uuid.temp_low  == check_role_id then
					MainuiWGCtrl.Instance:StopAutoRevenge(true, Language.Fight.RevengeObjAlly, v.role_id)
					break
				end
			end
		end
	end
	local leader_is_change = false

    local old_leader_uid
    for k,v in pairs(old_member_list) do
        if v.is_leader == 1 then
            old_leader_uid = v.uuid
            break
        end
    end
  
	if team_tag == 1 then
		local role_vo = RoleWGData.Instance:GetRoleVo()
		for k,v in pairs(new_member_list) do
            if v.is_leader == 1 then
				if v.uuid ~= old_leader_uid and v.uuid == role_vo.uuid then
					local content
					local name = v.name
					local level = v.level
					content = string.format(Language.NewTeam.LeaderChange2, name, RoleWGData.GetLevelString(level))
                    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
					--在组队做特殊处理 避免频繁刷 聊天功能90级开放 提示
					if main_role_vo.level >= COMMON_CONSTS.CHAT_LEVEL_LIMIT then
		      			ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT,1, nil, true)
					end
	      			leader_is_change = true
	      		else
	      			if v.uuid ~= old_leader_uid then
	      				leader_is_change = true
	      			end
				end
			end
		end
	end

	if team_tag == 1 and (#new_member_list-#old_member_list) > 0 then
		local role_vo = RoleWGData.Instance:GetRoleVo()
		for k,v in pairs(new_member_list) do
			if role_vo.uuid == v.uuid then
				if new_member_list[#new_member_list] then
			        local name = new_member_list[#new_member_list].name
			        local level = new_member_list[#new_member_list].level

					-- 不发协议走聊天，直接插入数据
					local msg_info = ChatWGData.CreateMsgInfo()
					msg_info.channel_type = CHANNEL_TYPE.TEAM
					msg_info.content = string.format(Language.NewTeam.JoinTeamMsg, name, RoleWGData.GetLevelString(level))
					msg_info.msg_reason = CHAT_MSG_RESSON.TEAM_TIPS
					msg_info.is_add_team = true
					msg_info.send_time_str = TimeWGCtrl.Instance:GetServerTime()
					ChatWGCtrl.Instance:AddChannelMsg(msg_info, true)
				end
			end
		end
	elseif team_tag == 0 then
		
	end
	for k, v in pairs(protocol.team.member_list) do
		if v.uuid.temp_low == RoleWGData.Instance.role_vo.lover_uid then
			MarryWGCtrl.Instance:LoverJoinTeam()
		end
	end
    for k, v in pairs(new_member_list) do
        local role
        if v.uuid == RoleWGData.Instance:GetUUid() then
            role = Scene.Instance:GetMainRole()
        else
            role = Scene.Instance:GetRoleByUUID(v.uuid)
        end
        if role and role.vo then
            role:SetAttr("is_team_leader", v.is_leader)
        end
    end

	--新创建队伍逻辑
	if self.data:GetIsInTeam() == 1 and self.data:GetIsTeamLeader() == 1 and self.data:GetTeamMemberCount() == 1 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, 0)
    end
    
	CrossTeamWGCtrl.Instance:ForceFlushInviteView()
	
	GlobalEventSystem:Fire(TeamInfoQuery.TEAM_INFO_BACK)
	GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)

    self:ClearQuickInviteTeamList()
    self:TeamInfoChangeCallBack()
end

function CrossTeamWGCtrl:ExitTeam()
	local content = ""
    local main_role = Scene.Instance:GetMainRole()
    content = string.format(Language.NewTeam.QuitTeamStr, main_role.vo.name, main_role.vo.level)
    ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.TEAM, content, CHAT_CONTENT_TYPE.TEXT,1,nil,true,false)
	CrossTeamWGCtrl.Instance:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_EXIT)
	self.data:ClearTeamData()
end

function CrossTeamWGCtrl:OnSCCrossTeamNearRoleList(protocol)
    --print_error(">>>OnSCCrossTeamNearRoleList 附近玩家列表信息>>>>>>>>",protocol)
    self.data:SetNearRoleList(protocol)
    self:FlushInviteView()
end

function CrossTeamWGCtrl:ForceFlushInviteView()
    self.cross_team_invite_view:ForceFlushInviteList()
end

function CrossTeamWGCtrl:OpenInviteView()
	self.cross_team_invite_view:Open()
end

function CrossTeamWGCtrl:FlushTextInvite()
	if self.cross_team_invite_view:IsOpen() and self.cross_team_invite_view:IsLoaded() then
	    self.cross_team_invite_view:FlushTextInvite()
    end
end

function CrossTeamWGCtrl:FlushInviteView()
	if self.cross_team_invite_view:IsOpen() then
		self.cross_team_invite_view:Flush()
	end
end

function CrossTeamWGCtrl:CloseInviteView()
	if self.cross_team_invite_view:IsOpen() then
		self.cross_team_invite_view:Close()
	end
end

function CrossTeamWGCtrl:CreateInviteRoleHeadCell(uuid, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
    self.cross_team_invite_view:CreateRoleHeadCell(uuid, role_name, prof, sex, is_online, node,plat_type, server_id, plat_name)
end

function CrossTeamWGCtrl:UpadateMemberHpBar(uuid)
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
        if not v:IsDead() and not v:IsDeleted() and not v:IsMainRole() then
            v:ReloadUINameColor()
            local vo = v:GetVo()
            if vo and vo.uuid then
                local is_friend = CrossTeamWGData.Instance:GetTargetIsTeamMember(vo.uuid)
                if is_friend or vo.uuid == uuid then
                    local follow_ui = v:GetFollowUi()
                    follow_ui:GetHpBar():UpdateProgress()     
                end
            end
		end
	end
end

function CrossTeamWGCtrl:ClearQuickInviteTeamList()
	self.data:ClearQuickInviteTeamList()
	if self.cross_quick_invite_team:IsOpen() then
		self.cross_quick_invite_team:Close()
	end
end

function CrossTeamWGCtrl:OpenQuickInviteTeam()
    if not self.cross_quick_invite_team:IsOpen() then
		self.cross_quick_invite_team:Open()
	else
		self.cross_quick_invite_team:Flush()
	end
end

function CrossTeamWGCtrl:OnClickSpeak()
    local name = RoleWGData.Instance:GetRoleVo().name
	CrossTeamWGCtrl.Instance:ShowTalkView(name)
end

--显示喊话界面
function CrossTeamWGCtrl:ShowTalkView(role_name)
	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.level < COMMON_CONSTS.CREATE_TEAM_LIMIT then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Chat.LevelDeficient, COMMON_CONSTS.CREATE_TEAM_LIMIT))
		return
	end

	if self.data:GetTeamMemberCount() >= 3 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ManyTower.TeamIsMaxMember)
		return
	end

	local tmp_time = self.data:GetRecruitCdTime()
	if tmp_time > 0 and CountDownManager.Instance:HasCountDown("team_world_talk") then
		--SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CDWorldTalk)
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.NewTeam.CDWorldTalk2, math.ceil(tmp_time)))
		return
    end

    local teamIndex = CrossTeamWGData.Instance:GetTeamIndex()
    if teamIndex then
        self:SendWordTalk()
    end
end

function CrossTeamWGCtrl:OnSCCrossTeamInviteChannelChat(protocol)
    self.data:OnSCTeamInviteChannelChat(protocol)
end

function CrossTeamWGCtrl:SendNoLongerOperateReq(no_longer_type, uuid)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamNoLongerOperateReq)
	send_protocol.no_longer_type = no_longer_type
	send_protocol.uuid = uuid
	send_protocol:EncodeAndSend()
end

--发送喊话
function CrossTeamWGCtrl:SendWordTalk()
	self.data:SetRecruitCdEndTime()
    self:SendTeamOperaReq(CROSS_TEAM_OPERATE_TYPE.CROSS_TEAM_OPERATE_TYPE_INVITE_SHOUTING) --服务器处理
    SysMsgWGCtrl.Instance:ErrorRemind(Language.CrossTeam.WorldInvite1)
    GlobalEventSystem:Fire(TeamWorldTalk.MAIN_WORLD_TALK)
end

--进入跨服组队的场景，清除原服组队信息
function CrossTeamWGCtrl:ClearOriginTeamInfo()
    NewTeamWGCtrl.Instance:ClearQuickInviteTeamList() --清除原服的快速组队邀请
    SocietyWGData.Instance:ClearTeamData()
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, 0)
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM, 0)
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, 0)
    local role = Scene.Instance:GetMainRole()
    if role then
        role:SetAttr("is_team_leader", 0)
    end
    ChatWGData.Instance:RemoveGetChannel(CHANNEL_TYPE.ZUDUI) --清除组队喊话消息
    ChatWGData.Instance:RemoveMainUIChannel(CHANNEL_TYPE.ZUDUI)
end
--从跨服组队的场景退出，清除跨服组队信息
function CrossTeamWGCtrl:ClearCrossTeamInfo()
    --print_error("清除跨服组队信息>" )
    self:ClearQuickInviteTeamList() --清除快速组队邀请
    SocietyWGData.Instance:ClearTeamData()
    TipWGCtrl.Instance:CloseAlertTips()
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_INVITE, 0)
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM, 0)
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, 0)
    self.data:ClearNearRoleList()
    self.data:ClearTeamData()
    self.data:ClearTeamList()
    local role = Scene.Instance:GetMainRole()
    if role then
        role:SetAttr("is_team_leader", 0)
    end
    ViewManager.Instance:Close(GuideModuleName.CrossTeamView)
    self.invite_callback_list = {}
    if self.cross_team_invite_view and self.cross_team_invite_view:IsOpen() then
        self.cross_team_invite_view:Close()
    end
    if self.cross_team_apply_view and self.cross_team_apply_view:IsOpen() then
        self.cross_team_apply_view:Close()
    end
    if self.cross_team_change_goal_view and self.cross_team_change_goal_view:IsOpen() then
        self.cross_team_change_goal_view:Close()
    end
    if self.cross_quick_invite_team and self.cross_quick_invite_team:IsOpen() then
        self.cross_quick_invite_team:Close()
    end
    if self.team_invite_list_view and self.team_invite_list_view:IsOpen() then
        self.team_invite_list_view:Close()
    end
    if self.hebing_view and self.hebing_view:IsOpen() then
        self.hebing_view:Close()
    end
    ChatWGData.Instance:RemoveGetChannel(CHANNEL_TYPE.ZUDUI)
    ChatWGData.Instance:RemoveMainUIChannel(CHANNEL_TYPE.ZUDUI)
    GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)
    MainuiWGCtrl.Instance:ReloadTeamList()
end

--查询玩家队伍信息
function CrossTeamWGCtrl:QueryCrossTeamInfo(uuid, callback)
    --print_error("查询玩家队伍信息", uuid)
	if uuid == nil or uuid.temp_low < 0 or nil == callback then
		return
	end

	for k, v in pairs(self.request_callback_list) do
		if v.callback == callback then
			return
		end
	end

	table.insert(self.request_callback_list, {["uuid"] = uuid, ["callback"] = callback,})

	local protocol = ProtocolPool.Instance:GetProtocol(CSQueryRoleCrossTeamInfo)
	protocol.uuid = uuid
	protocol:EncodeAndSend()
end

function CrossTeamWGCtrl:OnSCQueryRoleCrossTeamRet(protocol)
	self:TeamInfoReqCallBack(protocol)
end

function CrossTeamWGCtrl:TeamInfoReqCallBack(protocol)
    --print_error("查询玩家队伍信息返回", protocol.uuid, protocol.team_index)
	local count = #self.request_callback_list
	if count > 0 then
		local info = nil
		for i = count, 1, -1 do
			info = self.request_callback_list[i]
			if info.uuid == protocol.uuid then
				info.callback(protocol)
				table.remove(self.request_callback_list, i)
			end
		end
	end
end

function CrossTeamWGCtrl:OnSCCrossTeamReqLeaderTransmit(protocol)
	local desc = string.format(Language.Society.ApplyToBeTeamLeader, protocol.role_name)
	local ok_func = function ()
		self:SendAckMemChangeTeamLeader(protocol.uuid, 1)
	end
	local cancel_func = function ()
		self:SendAckMemChangeTeamLeader(protocol.uuid, 0)
	end
	TipWGCtrl.Instance:OpenAlertTips(desc, ok_func, cancel_func, nil, cancel_func, nil, nil, "同意", "拒绝")
end

function CrossTeamWGCtrl:SendAckMemChangeTeamLeader(uuid, is_accept)
    --print_error("申请队长。队长操作返回",is_accept)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamReqLeaderRet)
	protocol.uuid = uuid or 0
	protocol.is_accept = is_accept or 0
	protocol:EncodeAndSend()
end


-- 请求合并
function CrossTeamWGCtrl:SendTeamMergeReq(uuid)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamMergeReq)
	protocol.uuid = uuid
	protocol:EncodeAndSend()
end

function CrossTeamWGCtrl:SendTeamOneKeyMergeReq(count, merge_team_list)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossTeamOneKeyMergeReq)
	send_protocol.count = count
	send_protocol.role_uuid = merge_team_list or {}
	send_protocol:EncodeAndSend()
end

--收到合并消息
function CrossTeamWGCtrl:OnSCCrossTeamMergeReqTransmit(protocol)
    --print_error("收到合并消息>>>OnSCCrossTeamMergeReqTransmit>>", protocol)
	self.data:AddHeBingInfo(protocol)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, self.data:GetHeBingCount(), function ()
		self:OpenHeBingView()
		return true
	end)
	GlobalEventSystem:Fire(OtherEventType.TeamHeBingInfo)
end

--打开合并界面
function CrossTeamWGCtrl:OpenHeBingView()
	if not self.hebing_view:IsOpen() then
		self.hebing_view:Open()
	end
end
--关闭合并界面
function CrossTeamWGCtrl:CloseHeBingView()
	if self.hebing_view:IsOpen() then
		self.hebing_view:Close()
	end
end

--合并界面移除一条消息
function CrossTeamWGCtrl:RemoveHeBingItem(role_id)
	self.data:RemoveHeBingItem(role_id)
	if self.data:GetHeBingCount() <= 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_HEBING, 0)
		if self.hebing_view:IsOpen() then
			self.hebing_view:Close()
		end
		return
	end
	if self.hebing_view:IsOpen() then
		self.hebing_view:Flush()
	end
end

function CrossTeamWGCtrl:TeamInfoChangeCallBack(uuid)
    self:UpadateMemberHpBar(uuid)
    if Scene.Instance:GetIsOpenCrossViewByScene() then
        BossWGCtrl.Instance:ReloadOtherFollowXianJieEquipImg()
        if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
            local member_list = self.data:GetTeamMemberList()
            local my_uuid = RoleWGData.Instance:GetUUid()
            for k, v in pairs(member_list) do
                if my_uuid ~= v.uuid and v.uuid.temp_low > 0 then
                    local data = {}
                    data.remove_uuid = v.uuid
                    BossWGCtrl.Instance:OnSCCrossFairylandBossBeKillRecordRemove(data)
                end
            end
        end
    end
end

function CrossTeamWGCtrl:GetHeBingTodayCheckActive()
	return self.hebing_view:GetTodayCheckActive()
end

function CrossTeamWGCtrl:CreateHeBingRoleHeadCell(uuid, role_name, prof,sex, is_online, node,plat_type, server_id, plat_name)
    self.hebing_view:CreateRoleHeadCell(uuid, role_name, prof,sex, is_online, node,plat_type, server_id, plat_name)
end


--组队邀请接口
function CrossTeamWGCtrl:ITeamInvite(uuid)
	local function callback()
		if uuid then
			self:ITeamInvite1(uuid)
		end
	end
	OperateFrequency.Operate(callback, "teaminvite")
end

--创建队伍返回1成功 0失败
function CrossTeamWGCtrl:DoOpearte(result)
    if result == 0 then
        local count = #self.invite_callback_list
        for i = count, 1, -1 do
			table.remove(self.invite_callback_list, i)
		end
    elseif result == 1 then
        local count = #self.invite_callback_list
        local info = nil
        for i = count, 1, -1 do
			info = self.invite_callback_list[i]
			info.callback(info.uuid, info.type)
			table.remove(self.invite_callback_list, i)
		end
    end
end

--type1 对应 TEAM_INVITE_TYPE
function CrossTeamWGCtrl:ITeamInvite1(uuid)
	if 1 == SocietyWGData.Instance:GetIsInTeam() then
		if 0 == CrossTeamWGData.Instance:GetIsTeamLeader() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.DontInviety)
			return
		end
    else
        CrossTeamWGCtrl.Instance:SendCreateTeamReq()
        local callback = function(uuid)
            CrossTeamWGCtrl.Instance:SendInviteTeamReq(uuid)
        end
        local info = {["uuid"] = uuid, ["callback"] = callback,}
        if IsEmptyTable(self.invite_callback_list) then
            self.invite_callback_list[1] = info
        else
            for k, v in pairs(self.invite_callback_list) do
                if v.uuid ~= uuid then
                    table.insert(self.invite_callback_list, info)
                end
            end
        end
	    return
	end
	CrossTeamWGCtrl.Instance:SendInviteTeamReq(uuid)
end

function CrossTeamWGCtrl:OpenAllTeam()
    --CrossTeamWGCtrl.Instance:Open()
    ViewManager.Instance:Open(GuideModuleName.CrossTeamView)
    ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView, nil, "jump_all_team")
end