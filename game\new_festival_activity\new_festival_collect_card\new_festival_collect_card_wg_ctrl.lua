require("game/new_festival_activity/new_festival_collect_card/new_festival_collect_card_wg_data")
require("game/new_festival_activity/new_festival_collect_card/nfa_collect_card_info_tip")

NewFestivalCollectCardWGCtrl = NewFestivalCollectCardWGCtrl or BaseClass(BaseWGCtrl)

function NewFestivalCollectCardWGCtrl:__init()
	if NewFestivalCollectCardWGCtrl.Instance then
		error("[NewFestivalCollectCardWGCtrl]:Attempt to create singleton twice!")
	end

    NewFestivalCollectCardWGCtrl.Instance = self

    self.data = NewFestivalCollectCardWGData.New()
    self.info_view = NewFestivalCollectCardInfoTip.New()

    self:RegisterAllProtocols()
end

function NewFestivalCollectCardWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

    self.info_view:DeleteMe()
    self.info_view = nil

    NewFestivalCollectCardWGCtrl.Instance = nil
end

function NewFestivalCollectCardWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOACollectCardSelfInfo, "OnSCOACollectCardSelfInfo")
    self:RegisterProtocol(SCOACollectCardFriendInfo, "OnSCOACollectCardFriendInfo")
    self:RegisterProtocol(SCOACollectCardUpdateInfo, "OnSCOACollectCardUpdateInfo")
end

function NewFestivalCollectCardWGCtrl:OnSCOACollectCardSelfInfo(protocol)
    self.data:SetNewJRCollectCardSelfInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2350)
    self:FlushCollectCardInfoView()
    RemindManager.Instance:Fire(RemindName.NewFestivalCollectCard)
end

function NewFestivalCollectCardWGCtrl:OnSCOACollectCardFriendInfo(protocol)
    self.data:SetNewJRCollectCardFriendInfo(protocol)
    self:FlushCollectCardInfoView()
end

function NewFestivalCollectCardWGCtrl:OnSCOACollectCardUpdateInfo(protocol)
    self.data:SetNewJRCollectCardUpdateInfo(protocol)
    self:FlushCollectCardInfoView()
end

function NewFestivalCollectCardWGCtrl:FlushCollectCardInfoView()
    if self.info_view:IsOpen() then
        self.info_view:Flush()
    end
end

function NewFestivalCollectCardWGCtrl:OpenCollectCardInfoView(show_panel_type, item_id)
    if not self.info_view:IsOpen() then
        self.info_view:SetData(show_panel_type, item_id)
        self.info_view:Open()
    end
end