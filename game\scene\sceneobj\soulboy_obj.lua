SoulBoyObj = SoulBoyObj or BaseClass(FollowObj)

function SoulBoyObj:__init(vo)
    self.obj_type = SceneObjType.SoulBoyObj
    self.draw_obj:SetObjType(self.obj_type)
    self.shield_obj_type = ShieldObjType.SoulBoy

    self.use_baby_id = -1
    self.soulboy_res_id = 0
    self.soulboy_wing_id = 0
    self.soulboy_lg_id = 0
    self.is_goddess = true
    self:SetObjId(vo.obj_id)
    self.is_visible = true

    self.follow_offset = -2
    self.is_wander = true
    self.mass = 0.5
    self.wander_cd = 7

    self.record_time = self.now_time or 0
    self.record_talk_time = 0
    self.is_talk = false
    self:SetMaxForce(50)
    self.can_bubble = false

    for part, detail_level in pairs(SoulBoyDetailLevel) do
        self.draw_obj:SetPartNeedDetailLevel(part, detail_level)
    end
    self.draw_obj:SetCurDetailLevel(SceneObjDetailLevel.Low)

    SceneObjLODManager.Instance:Insert(self)
end

function SoulBoyObj:__delete()
    if SceneObjLODManager.Instance then
        SceneObjLODManager.Instance:Remove(self)
    end

    if self.bobble_timer_quest then
        GlobalTimerQuest:CancelQuest(self.bobble_timer_quest)
        self.bobble_timer_quest = nil
    end

    if self.release_timer then
        GlobalTimerQuest:CancelQuest(self.release_timer)
        self.release_timer = nil
    end

    if self.parent_scene and self.vo and self.vo.owner_obj_id then
        local owner_obj = self.parent_scene:GetObjectByObjId(self.vo.owner_obj_id)
        if owner_obj then
            owner_obj:ReleaseSoulBoyObj()
        end
    end
end

function SoulBoyObj:InitAppearance()
    if self.draw_obj:IsDeleted() then return end
    if self.vo then
        self.soulboy_lg_id = self.vo.soulboy_lg_id
    end

    self:UpdateModelResId()
    self:UpdateWingResId()
    self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Goddess", 11101))
    local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    if main_role_vo and main_role_vo.role_id ~= self.vo.owner_role_id and self.soulboy_lg_id == 1 then
        self:UpdateShenGongResId()
    end
    local follow_ui = self:GetFollowUi()
    if follow_ui then
        follow_ui:SetHpVisiable(false)
        follow_ui:SetNameVis(false)
    end

    if self.soulboy_res_id ~= nil and self.soulboy_res_id ~= 0 then
        self:ChangeModel(SceneObjPart.Main, ResPath.GetSoulBoyModel(self.soulboy_res_id))
    end

    -- if self.soulboy_wing_id ~= nil and self.soulboy_wing_id ~= 0 then
    --     self:ChangeModel(SceneObjPart.Wing, ResPath.GetSoulBoyWingModel(self.soulboy_wing_id))
    -- end

    -- if self.soulboy_lg_id ~= nil and self.soulboy_lg_id ~= 0 and self.soulboy_lg_id ~= 1 then
    --     self:ChangeModel(SceneObjPart.Weapon, ResPath.GetSoulBoyWeaponModel(self.soulboy_lg_id))
    -- end

    -- self:UpdateMount()
    local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
    if main_part then
        local complete_func = function(part, obj)
            if part == SceneObjPart.Main then
                local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
                if main_part then
                    -- main_part:SetTrigger("ShowSceneIdle")
                    main_part:CrossFade(SceneObjAnimator.Idle)
                end
                -- local transform = self.draw_obj:GetRoot().transform
                -- transform.localScale = Vector3(0.9, 0.9, 0.9)
            end

            self:OnModelLoaded(part, obj)
        end
        self.draw_obj:SetLoadComplete(complete_func)
    end

    self.exist_time = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].exist_time
    self.interval = ConfigManager.Instance:GetAutoConfig("bubble_list_auto").other[1].interval
    self.record_time = self.record_time + self.interval
end

function SoulBoyObj:OnEnterScene()
    FollowObj.OnEnterScene(self)
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    self.can_bubble = fb_scene_cfg.is_bubble and fb_scene_cfg.is_bubble == 1 or false
end

function SoulBoyObj:SetAttr(key, value)
    FollowObj.SetAttr(self, key, value)
    -- if key == "use_baby_id" then
        -- self:UpdateModelResId()
        -- self:ChangeModel(SceneObjPart.Main, ResPath.GetHaiZiModel(self.use_baby_id))
    -- end
    if key == "soulboy_lt_id" then
        self:UpdateModelResId()
        self:ChangeModel(SceneObjPart.Main, ResPath.GetSoulBoyModel(self.soulboy_res_id))
    -- elseif key == "goddess_wing_id" then
    --     self:UpdateWingResId()
    --     self:ChangeModel(SceneObjPart.Wing, ResPath.GetSoulBoyWingModel(self.soulboy_wing_id))
    -- elseif key == "soulboy_lg_id" then
    --     self.soulboy_lg_id = value
    --     local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
    --     if main_role_vo and main_role_vo.role_id ~= self.vo.owner_role_id and self.soulboy_lg_id == 1 then
    --         self:UpdateShenGongResId()
    --     end
        self:ChangeModel(SceneObjPart.Weapon, ResPath.GetSoulBoyWeaponModel(self.soulboy_lg_id))
    elseif key == "name" then
        self:ReloadUIName()
    elseif key == "xiannv_huanhua_id" then
        self:UpdateHuanhuaModelResId()
        self:ChangeModel(SceneObjPart.Main, ResPath.GetSoulBoyModel(self.soulboy_res_id))
    elseif key == "soulboy_ls_id" then
        self:UpdateMount()
    end
end

function SoulBoyObj:UpdateMount()
end

-- 战斗屏蔽随机移动
function SoulBoyObj:ShieldWadnerForce(target)
    return target.IsFightState and target:IsFightState() or false
end

function SoulBoyObj:Update(now_time, elapse_time)
    FollowObj.Update(self, now_time, elapse_time)
    if not self.can_bubble or not self:GetVisiable() then
        return
    end
    if nil ~= self.follow_ui and self:OwnerIsMainRole() then
        if now_time > self.record_time and self.is_talk == false then
            self.is_talk = true
            self:UpdataBubble()
            self.follow_ui:ForceSetVisible(true)
            self.follow_ui:ShowBubble()
            self.bobble_timer_quest = GlobalTimerQuest:AddDelayTimer(function()
                self.follow_ui:CancelForceSetVisible()
                self.follow_ui:HideBubble()
                self.is_talk = false
                self.record_time = now_time + self.interval
            end,self.exist_time)
        end
    end
end

function SoulBoyObj:GetIsTalk()
    -- body
    return self.is_talk
end

function SoulBoyObj:UpdateModelResId()
    if self.vo.soulboy_lt_id ~= self.soulboy_res_id then
        self.soulboy_res_id = self.vo.soulboy_lt_id
        self:SetAttr("name", NewAppearanceWGData.Instance:GetQiChongNameByAppeId(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG, self.soulboy_res_id))
    end

    if self.draw_obj then
        self.draw_obj:SetName(self.vo.name)
    end
end

function SoulBoyObj:UpdateHuanhuaModelResId()

end

function SoulBoyObj:UpdateWingResId()
    if self.vo.goddess_wing_id and self.vo.goddess_wing_id ~= 0 then
        local res_id = 0
        self.soulboy_wing_id = res_id
    end
end

function SoulBoyObj:UpdateShenGongResId()
    self.soulboy_lg_id = self.vo.soulboy_lg_id
    if self.soulboy_lg_id == nil or self.soulboy_lg_id == 1 or self.soulboy_lg_id == 0 then
        self.soulboy_lg_id = AppearanceWGData.GetLingTongBaseWeaponModelID()
    end
end

function SoulBoyObj:IsCharacter()
    return false
end

function SoulBoyObj:GetOwerRoleId()
    return self.vo.owner_role_id
end

function SoulBoyObj:SetTrigger(key)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        -- local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetTrigger(key)
        end
        -- if weapon_part then
        --     weapon_part:SetTrigger(key)
        -- end
    end
end

function SoulBoyObj:SetBool(key, value)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetBool(key, value)
        end
        if weapon_part then
            weapon_part:SetBool(key, value)
        end
    end
end

function SoulBoyObj:SetInteger(key, value)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetInteger(key, value)
        end
        if weapon_part then
            weapon_part:SetInteger(key, value)
        end
    end
end

function SoulBoyObj:DoAttack(...)
    Character.DoAttack(self, ...)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if weapon_part then
            weapon_part:SetTrigger(SceneObjAnimator.Atk1)
        end
    end
end

function SoulBoyObj:EnterStateAttack()
    local anim_name = SceneObjAnimator.Fight
    if self:IsRiding() then
        anim_name = SceneObjAnimator.Mount_Fight
    end
    Character.EnterStateAttack(self, anim_name)
end

function SoulBoyObj:IsSoulBoy()
    -- return true
    return self.soulboy_res_id > 0
end

function SoulBoyObj:IsBaby()
    -- body
    return self.use_baby_id > -1
end

function SoulBoyObj:GetRandBubbletext()
    local bubble_cfg = NewAppearanceWGData.Instance:GetLingChongBubbleList(Scene.Instance:GetSceneType())
    if #bubble_cfg > 0 then
        math.randomseed(os.time())
        local bubble_text_index = math.random(1, #bubble_cfg)
        return bubble_cfg[bubble_text_index].pet_talk
    else
        return ""
    end
end

function SoulBoyObj:GetFirstBubbleText()
    local bubble_cfg = NewAppearanceWGData.Instance:GetLingChongBubbleList(Scene.Instance:GetSceneType())
    return bubble_cfg[1].pet_talk
end

function SoulBoyObj:UpdataBubble()
    if nil ~= self.follow_ui then
        local text = self:GetRandBubbletext()
        self.follow_ui:ChangeBubble(text)
    end
end

function SoulBoyObj:GetActionTimeRecord(anim_name)
    -- body
    local atr
    if self:IsSoulBoy() then
        atr = SoulActionConfig
        if SoulActionConfig[self.soulboy_res_id] then
            return SoulActionConfig[self.soulboy_res_id][anim_name]
        end
    elseif self:IsBaby() then
        atr = BabyActionConfig
        if BabyActionConfig[self.use_baby_id] then
            return BabyActionConfig[self.use_baby_id][anim_name]
        end
    end
    return atr[0][anim_name]
end

--坐骑
function SoulBoyObj:IsRiding()
    local vo = self.vo
    if not vo then return false end

    return 0 < vo.soulboy_ls_id
end

function SoulBoyObj:ChangeModel(part, bundle, name, callback)
    local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    if not fb_scene_cfg.pb_lingtong or 1 == fb_scene_cfg.pb_lingtong then return end
    SceneObj.ChangeModel(self, part, bundle, name, callback)
end


function SoulBoyObj:OwnerIsMainRole()
    return self.vo.owner_is_mainrole or false
end