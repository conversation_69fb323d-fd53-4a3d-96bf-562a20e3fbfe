
RechargeReturnRewardView = RechargeReturnRewardView or BaseClass(SafeBaseView)
function RechargeReturnRewardView:__init()
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_bg")
	self:AddViewResource(0, "uis/view/rechargereturn_ui_prefab", "RechargeReturnReward")
	--self:AddViewResource(0, "uis/view/common_panel_prefab", "activity_common_panel_title")
	self.reward_count = 4
	self:SetMaskBg(true,true)
end

function RechargeReturnRewardView:__delete()
end

function RechargeReturnRewardView:ReleaseCallBack()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function RechargeReturnRewardView:LoadCallBack(index, loaded_times)
	self.node_list["BtnDress"].button:AddClickListener(BindTool.Bind(self.OpenRecharge, self))
	XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind(self.OnClickTips, self))
end

function RechargeReturnRewardView:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_CRAZY_REBATE, opera_type = RendActOperaType.QUERY_INFO})
end

function RechargeReturnRewardView:CloseCallBack()
end

function RechargeReturnRewardView:OpenRecharge()
	FunOpen.Instance:DoOpenViewByName(GuideModuleName.Recharge, TabIndex.recharge_cz)
end

function RechargeReturnRewardView:OnFlush(param_t, index)
	if self.time_quest == nil then
		self.time_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushNextTime, self), 1)
		self:FlushNextTime()
	end

	local config = RechargeReturnRewardWGData.Instance:GetActConfig()

	if not IsEmptyTable(config) then
		for i = 1, self.reward_count do
			local cfg = config[i]
			if not IsEmptyTable(cfg) then
				local desc = ""
				if i <= 3 then
					local low_limit = CommonDataManager.ConverGoldByThousand(cfg.gold_low_limit)
					local high_limit = CommonDataManager.ConverGoldByThousand(cfg.gold_high_limit)
					desc = low_limit .. "-" .. high_limit
				else
					local low_limit = CommonDataManager.ConverGoldByThousand(cfg.gold_low_limit)
					desc = low_limit
				end
	
				self.node_list["TxtDesc" .. i].text.text = desc
				if i == self.reward_count then
					self.node_list["TxtDesc" .. i].text.text = desc .. Language.RechargeCapacity.YiShang
				end
				self.node_list["TxtDesc" .. (i + 4)].text.text = cfg.reward_precent .. "%"
			end
		end
	end

	local num = RechargeReturnRewardWGData.Instance:GetRechargeNum() or 0
	self.node_list["TxtTopUp"].text.text = string.format(Language.RechargeCapacity.RechargeQuantuty, num)
end

function RechargeReturnRewardView:FlushNextTime()
	local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CHONGZHI_CRAZY_REBATE)
	if time <= 0 then
		if self.time_quest then
			GlobalTimerQuest:CancelQuest(self.time_quest)
			self.time_quest = nil
		end
	end
	local time_type = 1
	if time > 3600 * 24 then
		time_type = 6
	elseif time > 3600 then
		time_type = 1
	else
		time_type = 2
	end
	self.node_list["TxtTime"].text.text =string.format(Language.RechargeCapacity.ReturnActTime, ToColorStr(TimeUtil.FormatSecond(time, time_type), COLOR3B.D_GREEN))
end

function RechargeReturnRewardView:OnClickTips()
	RuleTip.Instance:SetContent(Language.RechargeCapacity.Rile_Desc, Language.RechargeCapacity.Rule_Title)
end
