﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class FancyScrollView_RectScrollViewWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(FancyScrollView.RectScrollView), typeof(FancyScrollView.FancyScrollRect<FancyScrollView.Context>));
		<PERSON><PERSON>("SetDataCount", SetDataCount);
		<PERSON><PERSON>unction("OnCellClicked", OnCellClicked);
		<PERSON><PERSON>RegFunction("ScrollTo", ScrollTo);
		<PERSON>.RegFunction("JumpTo", JumpTo);
		<PERSON><PERSON>Function("SelectCell", SelectCell);
		<PERSON><PERSON>ction("SelectNextCell", SelectNextCell);
		<PERSON>.RegFunction("SelectPrevCell", SelectPrevCell);
		<PERSON><PERSON>unction("SetContent", SetContent);
		<PERSON><PERSON>RegFunction("__eq", op_Equality);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("DataCount", get_DataCount, null);
		<PERSON><PERSON>("PaddingTop", get_PaddingTop, set_PaddingTop);
		L.RegVar("PaddingBottom", get_PaddingBottom, set_PaddingBottom);
		L.RegVar("Spacing", get_Spacing, set_Spacing);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDataCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetDataCount(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnCellClicked(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
			System.Action<int> arg0 = (System.Action<int>)ToLua.CheckDelegate<System.Action<int>>(L, 2);
			obj.OnCellClicked(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ScrollTo(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 4)
			{
				FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				EasingCore.Ease arg2 = (EasingCore.Ease)ToLua.CheckObject(L, 4, typeof(EasingCore.Ease));
				obj.ScrollTo(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 5)
			{
				FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
				EasingCore.Ease arg2 = (EasingCore.Ease)ToLua.CheckObject(L, 4, typeof(EasingCore.Ease));
				FancyScrollView.Alignment arg3 = (FancyScrollView.Alignment)ToLua.CheckObject(L, 5, typeof(FancyScrollView.Alignment));
				obj.ScrollTo(arg0, arg1, arg2, arg3);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FancyScrollView.RectScrollView.ScrollTo");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int JumpTo(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				obj.JumpTo(arg0);
				return 0;
			}
			else if (count == 3)
			{
				FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
				FancyScrollView.Alignment arg1 = (FancyScrollView.Alignment)ToLua.CheckObject(L, 3, typeof(FancyScrollView.Alignment));
				obj.JumpTo(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: FancyScrollView.RectScrollView.JumpTo");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SelectCell(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SelectCell(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SelectNextCell(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
			obj.SelectNextCell();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SelectPrevCell(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
			obj.SelectPrevCell();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetContent(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)ToLua.CheckObject<FancyScrollView.RectScrollView>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			obj.SetContent(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DataCount(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)o;
			int ret = obj.DataCount;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index DataCount on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PaddingTop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)o;
			float ret = obj.PaddingTop;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PaddingTop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_PaddingBottom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)o;
			float ret = obj.PaddingBottom;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PaddingBottom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Spacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)o;
			float ret = obj.Spacing;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Spacing on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PaddingTop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.PaddingTop = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PaddingTop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_PaddingBottom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.PaddingBottom = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index PaddingBottom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Spacing(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			FancyScrollView.RectScrollView obj = (FancyScrollView.RectScrollView)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Spacing = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Spacing on a nil value");
		}
	}
}

