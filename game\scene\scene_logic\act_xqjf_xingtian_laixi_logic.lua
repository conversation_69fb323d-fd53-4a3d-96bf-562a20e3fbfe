ActXQJFXingTianLaiXiLogic = ActXQJFXingTianLaiXiLogic or BaseClass(CommonFbLogic)
function ActXQJFXingTianLaiXiLogic:__init()
	self.save_boss_posx = 0
	self.save_boss_posy = 0
end

function ActXQJFXingTianLaiXiLogic:__delete()
	self.save_boss_posx = 0
	self.save_boss_posy = 0
end

function ActXQJFXingTianLaiXiLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()

	local other_cfg = ActXianQiJieFengWGData.Instance:GetJiangLinOtherCfg()
	if other_cfg and other_cfg[1] then
		other_cfg = other_cfg[1]
		self.save_boss_posx = other_cfg.pos_x
		self.save_boss_posy = other_cfg.pos_y
		local scene_id = Scene.Instance:GetSceneId()
		GuajiWGCtrl.Instance:MoveToPos(scene_id, other_cfg.pos_x, other_cfg.pos_y, 0, 0, 1)
		local move_to_pos_call_back = function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(move_to_pos_call_back)
	end

	MainuiWGCtrl.Instance:AddInitCallBack(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD, function()
		MainuiWGCtrl.Instance:SetButtonModeClick(false)
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.QuanMinBeiZhan.LaiXiStr8)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		ActXianQiJieFengWGCtrl.Instance:XingTianLaiXiCountDown()
		ActXianQiJieFengWGCtrl.Instance:OpenLaiXiFbView()
	end)
end

function ActXQJFXingTianLaiXiLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)
	
	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetButtonModeClick(true)
	UiInstanceMgr.Instance:ColseFBStartDown()
	FuBenPanelCountDown.Instance:CloseViewHandler()
	ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD)
end

function ActXQJFXingTianLaiXiLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function ActXQJFXingTianLaiXiLogic:GetGuajiPos()
	return self.save_boss_posx, self.save_boss_posy
end