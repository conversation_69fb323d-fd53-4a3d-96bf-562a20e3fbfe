EquipGetWayView = EquipGetWayView or BaseClass(SafeBaseView)

function EquipGetWayView:__init()
	self:SetMaskBg(true, true)
	self.view_name = "EquipGetWayView"
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_tips_bg_panel1", {vector2 = Vector2(0, 4), sizeDelta = Vector2(236,300)})
	self:AddViewResource(0, "uis/view/itemtip_ui_prefab", "layout_equip_getway_view")
end

function EquipGetWayView:LoadCallBack()
	self.list_view = AsyncListView.New(EquipGetWayCell, self.node_list.list_view)
	self.node_list.title_view_name.text.text = Language.Compose.HouQuTuJing
	self.node_list.cancel_btn.button:AddClickListener(BindTool.Bind(self.OnClickCancelBtn,self))
	self.node_list.btn_close_window.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtn,self))
end

-- 切换标签调用
function EquipGetWayView:ShowIndexCallBack()
	self:Flush()
end

function EquipGetWayView:SetData(data)
	self.data = data
end

function EquipGetWayView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
	self.data = nil
end

function EquipGetWayView:OnFlush()
	if not self.data or IsEmptyTable(self.data) then return end
	self.list_view:SetDataList(self.data,3)
end

function EquipGetWayView:OnClickCancelBtn()
	self:Close()
end

function EquipGetWayView:OnClickCloseBtn()
	self:Close()
end

-------------------------------------EquipGetWayCell---------------------------------

EquipGetWayCell = EquipGetWayCell or BaseClass(BaseRender)

function EquipGetWayCell:__init()
	self.node_list.bg.button:AddClickListener(BindTool.Bind(self.OnClickGoto,self))
end

function EquipGetWayCell:__delete()

end

function EquipGetWayCell:OnFlush()
	if not self.data then return end
	self.node_list.name.text.text = self.data.open_name
	--self.node_list.flag_img:SetActive(self.data.flag)

--[[ 	local bundle, asset = ResPath.GetCommonIcon(self.data.icon)
	--local bundle, asset = ResPath.GetCommonButton(self.data.icon)
	self.node_list.icon.image:LoadSprite(bundle, asset) ]]
end

function EquipGetWayCell:OnClickGoto()
	if not self.data then return end
	local open_panel = self.data.open_panel
	--print_error(open_panel,self.data)
	if open_panel == GuideModuleName.Compose then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, nil, {is_stuff = false, item_id = self.data.item_id})
	elseif open_panel == "guaji" then  						--挂机按钮点击去挂机
		TaskWGData.Instance:GuaJiGetMonster()
	elseif open_panel == "guild_juanxian" then
		GuildWGCtrl.Instance:OpenGuildJuanXinaView()
	elseif open_panel == "shop#shop_moyu" then
		ShopWGCtrl.Instance:ShopJumpToItemByID(self.data.item_id)
		--ViewManager.Instance:Open(GuideModuleName.Shop, nil, "select_item_id", {item_id = self.data.item_id})
	elseif open_panel == "shop#shop_hot" then
		ShopWGCtrl.Instance:ShopJumpToItemByID(self.data.item_id)
		--ViewManager.Instance:Open(GuideModuleName.Shop, nil, "select_item_id", {item_id = self.data.item_id})
	elseif open_panel == "market#market_buy" then
		if self.data.stuff_item_id == 90706 then
			ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.market_buy)
			return
		end

		local is_level_enough = MarketWGData.Instance:GetMarketItemNeedLevel(self.data.stuff_item_id)
		if is_level_enough then
			ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.market_buy, "select_item_id", {item_id = self.data.stuff_item_id})
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Market.LevelLimitMarket)
		end
	else
		FunOpen.Instance:OpenViewNameByCfg(open_panel)
	end
	TipWGCtrl.Instance:ClsoeEquipGetWayView()
end
