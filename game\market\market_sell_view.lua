function MarketView:SellLoadCallBack()
	self:CreateBagGrid()
	-- self.my_sell_goods_list = {}

	if not self.my_goods_grid then
		self.my_goods_grid = AsyncBaseGrid.New()
		self.my_goods_grid:SetStartZeroIndex(false)
		self.my_goods_grid:CreateCells({
			col = 2,
			list_view = self.node_list["my_goods_grid"],
			itemRender = MarketSellMyGoodsItem,
			assetBundle = "uis/view/market_ui_prefab",
			assetName = "my_goods_item_prefab",
			change_cells_num = 1,
			complement_col_item = true,
		})
		self.my_goods_grid:SetSelectCallBack(BindTool.Bind(self.OnClickMyGoods, self))
	end

	if not self.sell_money_change_event then
		self.sell_money_change_event = GlobalEventSystem:Bind(OtherEventType.Gold_Change_Event,BindTool.Bind(self.SellMoneyChangeEvent, self))
	end
end

function MarketView:SellReleaseCallBack()
	if nil ~= self.bag_grid then
		self.bag_grid:DeleteMe()
		self.bag_grid = nil
	end

	-- if self.my_sell_goods_list then
	-- 	for i,v in ipairs(self.my_sell_goods_list) do
	-- 		v:DeleteMe()
	-- 	end
	-- end

	-- self.my_sell_goods_list = {}

	if self.my_goods_grid then
		self.my_goods_grid:DeleteMe()
		self.my_goods_grid = nil
	end

	if self.sell_money_change_event then
        GlobalEventSystem:UnBind(self.sell_money_change_event)
    end
    self.sell_money_change_event = nil
end

function MarketView:SellShowIndexCallBack()

end

function MarketView:CreateBagGrid()
	self.bag_grid = ScrollGrid.New()
	self.bag_grid:CreateCells({col = 4,cell_count = 400, itemRender = MarketBagItem, list_view = self.node_list["sell_bag_grid"]})
	self.bag_grid:SetSelectCallBack(BindTool.Bind1(self.OnBagItemClick, self))
end

function MarketView:SellOnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushSellAllPart()
		elseif k == "Part" then
			if v.flush_sell_bag then
				self:FlushSellBag()
			end
		end
	end
end

function MarketView:FlushSellAllPart()
	self:FlushSellBag()
	self:FlushMySellGoods()
	self:FlushSellOthers()
end

function MarketView:FlushSellOthers()
	-- 税率提示
	self.node_list["sell_tips_text"].text.text = string.format(Language.Market.TaxStrTips, MarketWGData.Instance:GetTax() * 100)
end

function MarketView:FlushSellBag()
	self.sell_bag_data_list = MarketWGData.Instance:GetMarketSellBagInfoList()
	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	local bangyu_money = RoleWGData.Instance:GetMoney(MoneyType.BangYu)
	--插一个元宝兑换
	local data = {}
	data.knapsack_type = -1
	data.num = bangyu_money
	data.is_bind = 0
	data.item_id = other_cfg.gold_bind_id
	data.index = -1
	data.is_bangyu = true

	table.insert(self.sell_bag_data_list, 0, data)
	self.bag_grid:SetDataList(self.sell_bag_data_list, 2)
end

-- 刷新自己上架的商品
function MarketView:FlushMySellGoods()
	local my_sell_goods_list_info = MarketWGData.Instance:GetMyGoodsListInfo()
	self.my_goods_grid:SetDataList(my_sell_goods_list_info)

	self.node_list["sell_empty_tips"]:SetActive(IsEmptyTable(my_sell_goods_list_info))
end

-- 点击背包物品回调
function MarketView:OnBagItemClick(item_index)
	if not self.sell_bag_data_list then
		return
	end

	local data = self.sell_bag_data_list[item_index - 1]
	if not data then
		return
	end

	if nil == data.num or data.num < 1 then
		data.num = 1
	end

	if data.knapsack_type == KNAPSACK_TYPE.SHENSHOU then
		ShenShouWGCtrl.Instance:OpenShenShouEquipTip(data, ShenShouEquipTip.FROM_MARKET_SHANGJIA)
	elseif data.knapsack_type == -1 and data.is_bangyu then
		MarketWGCtrl.Instance:OpenMarketTipItemView(data)
	else
		TipWGCtrl.Instance:OpenItem(data, ItemTip.FROM_MARKET_SHANGJIA)
	end
end

-- 点击自己上架的商品
function MarketView:OnClickMyGoods(cell)
	if not cell or IsEmptyTable(cell:GetData()) then
		return
	end

	MarketWGCtrl.Instance:OpenMarketRemoveSellTips(cell:GetData())
end

function MarketView:SellMoneyChangeEvent(protocol)
	if self.show_index == MarketViewIndex.Sell then
		local main_role_vo = RoleWGData.Instance:GetRoleInfo()
		if protocol.bind_gold and main_role_vo["bind_gold"] ~= protocol.bind_gold then
			GlobalTimerQuest:AddDelayTimer(function ()
				self:FlushSellBag()
			end, 0.5)
		end
	end
end
----------------------------------------------------
-- 市场出售bagitem
----------------------------------------------------
MarketBagItem = MarketBagItem or BaseClass(BaseGridRender)
function MarketBagItem:__init()
	self.item_list = {}
	for i = 1, 5 do
		local cell = ItemCell.New(self.node_list["ItemRender_"..i])
		cell:SetClickCallBack(BindTool.Bind(self.ClickCell, self, i))
		cell:SetItemTipFrom(ItemTip.FROM_MARKET_SHANGJIA)
		cell:SetHideRightDownBgLessNum(-1)
		table.insert(self.item_list, cell)
	end
end

function MarketBagItem:__delete()
	for i,v in ipairs(self.item_list) do
		v:DeleteMe()
	end
	self.item_list = nil
end

function MarketBagItem:ClickCell(i)
	self:GridItemOnCLick(i)
end

function MarketBagItem:OnFlush()
	if self.data == nil then 
		return 
	end

	for i = 1, 5 do
		if self.data[i] and self.data[i].is_bangyu then
			self.item_list[i]:SetData({item_id = self.data[i].item_id, num = 0, is_bind = 0})
			local bangyu_money = RoleWGData.Instance:GetMoney(MoneyType.BangYu)
			self.item_list[i]:SetRightBottomColorText(CommonDataManager.ConverPowerByThousand(bangyu_money))
			self.item_list[i]:SetRightBottomTextVisible(true)
		else
			self.item_list[i]:SetData(self.data[i])
		end

		self.item_list[i].is_showtip = false
	end
end
----------------------------------------------------
-- 自己上架的商品item
----------------------------------------------------
MarketSellMyGoodsItem = MarketSellMyGoodsItem or BaseClass(BaseRender)
function MarketSellMyGoodsItem:__init()

end

function MarketSellMyGoodsItem:LoadCallBack()
    self.item_cell = ItemCell.New(self.node_list["item_cell"])
	XUI.AddClickEventListener(self.node_list["yell_btn"], BindTool.Bind1(self.OnClickYell, self))
end

function MarketSellMyGoodsItem:__delete()
	self:CancelRunner()
	if self.item_cell then
		self.item_cell:DeleteMe()
	end
	self.item_cell = nil
end

function MarketSellMyGoodsItem:ClickCell(i)
	self:GridItemOnCLick(i)
end

function MarketSellMyGoodsItem:OnFlush()
	self.node_list["fake_hide"]:CustomSetActive(not IsEmptyTable(self.data))

	if IsEmptyTable(self.data) then return end
	local password
	-- 物品名称
	self.node_list["item_name"].text.text = ItemWGData.Instance:GetItemName(self.data.item_data.item_id)
	self.node_list["password"]:SetActive(self.data.password > 0)
	self.node_list["password"].text.text = string.format(Language.Market.PassWord, self.data.password)
	-- 物品格子

	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	local item_data = {}
	item_data.item_id = self.data.item_data.item_id
	item_data.is_bind = self.data.item_data.is_bind
	local item_count = (other_cfg.gold_bind_id == self.data.item_data.item_id) and (self.data.item_data.num * other_cfg.min_shelves_gond_bind_num) or self.data.item_data.num
	item_data.num = item_count
	item_data.star_level = ((self.data.item_data or {}).param or {}).star_level or 0
	self.item_cell:SetData(item_data)
	-- 总价
	self.node_list["price"].text.text = self.data.total_price

	-- 刷新吆喝倒计时
	self:CancelRunner()
	self:FlushYellTime()
	self.yell_timer = GlobalTimerQuest:AddRunQuest(function()
		self:FlushYellTime()
	end, 1)

	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(self.data.item_data.item_id)
	local auction_price_type = cfg.auction_price_type
	local icon_name = AUCTION_PRICE_TYPE_ICON[auction_price_type]
	if icon_name then
		local bundel, asset = ResPath.GetCommonIcon(icon_name)
		self.node_list.price_type_icon.image:LoadSprite(bundel, asset, function ()
			self.node_list.price_type_icon.image:SetNativeSize()
		end)
	end
end

-- 刷新吆喝倒计时
function MarketSellMyGoodsItem:FlushYellTime()
	if not self.node_list["yell_btn_text"] then
		return
	end

	local other_cfg = MarketWGData.Instance:GetOtherCfg()
	self.node_list["yell_btn"]:SetActive(RoleWGData.Instance:GetRoleLevel() >= other_cfg.yell_open_level)
	local next_yell_time = self.data.next_yell_time or 0
	if next_yell_time > TimeWGCtrl.Instance:GetServerTime() then
		self.node_list["yell_btn_text"].text.text = math.floor(next_yell_time - TimeWGCtrl.Instance:GetServerTime())
	end
	
	self.node_list["yell_btn_text"]:SetActive(next_yell_time > TimeWGCtrl.Instance:GetServerTime())
	self.node_list["blue_btn"]:SetActive(next_yell_time <= TimeWGCtrl.Instance:GetServerTime())
end

-- 吆喝
function MarketSellMyGoodsItem:OnClickYell()
	MarketWGCtrl.Instance:SendYell(self.data.auction_index)
end

function MarketSellMyGoodsItem:CancelRunner()
	if self.yell_timer then
		GlobalTimerQuest:CancelQuest(self.yell_timer)
		self.yell_timer = nil
	end
end