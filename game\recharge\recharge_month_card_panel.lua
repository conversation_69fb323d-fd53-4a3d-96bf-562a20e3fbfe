-- F2月卡投资

local SPECIAL_POS_X = 32
local NORMAL_POS_X = 252

function RechargeView:InitMonthCardPanel()
	self.node_list.mc_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickMonthCardBuyBtn, self))
	self.node_list.reward_btn.button:AddClickListener(BindTool.Bind(self.OnClickMonthCardRewardBtn, self))

	self.cur_show_card_type = INVEST_CARD_TYPE.MonthCard
	local month_card_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.MonthCard)
	self.month_card_cost = month_card_cfg and month_card_cfg.need_rmb or 0
	self.month_card_cost_type = month_card_cfg and month_card_cfg.rmb_type or 0
	local price_str = RoleWGData.GetPayMoneyStr(self.month_card_cost, self.month_card_cost_type, INVEST_CARD_TYPE.MonthCard)
	self.node_list.mc_buy_cost.text.text = price_str --string.format(Language.Recharge.MonthCardBuy, self.month_card_cost)

	self.item_cell_list = {}
	for i = 0, 2 do
		self.item_cell_list[i] = ItemCell.New(self.node_list["Item_cell" .. i])
	end

	self.vip_mc_reward_list = AsyncListView.New(ItemCell, self.node_list.vip_mc_reward_list)
	self.vip_mc_reward_list:SetStartZeroIndex(true)
end

function RechargeView:DeleteMonthCardPanel()
	if self.item_cell_list then
		for k,v in pairs(self.item_cell_list) do
			v:DeleteMe()
		end
		self.item_cell_list = nil
	end

	if self.vip_mc_reward_list then
		self.vip_mc_reward_list:DeleteMe()
		self.vip_mc_reward_list = nil
	end

	self.month_card_cost = nil
	self.month_card_cost_type = nil
end

function RechargeView:ShowMonthCardIndexCallBack()
	if RechargeWGData.Instance:GetInvestCardEnterNeedRemind() then
		RechargeWGData.Instance:SetInvestCardEnterNeedRemind()
	end
end

function RechargeView:OnClickFreeMopUp()
	RuleTip.Instance:SetContent(Language.Recharge.MonthCardFreeMopUpContent, Language.Recharge.MonthCardFreeMopUpTitle)
end

function RechargeView:OnClickFreeMerge()
	RuleTip.Instance:SetContent(Language.Recharge.MonthCardFreeMergeContent, Language.Recharge.MonthCardFreeMergeTitle)
end

function RechargeView:OnClickMonthCardBuyBtn()
	-- 策划xzp说不再跳转，直接进藏金商铺
	if self.month_card_cost > 0 then
		RechargeWGCtrl.Instance:Recharge(self.month_card_cost, self.month_card_cost_type, self.cur_show_card_type)
	end

	-- local no_buy = PierreDirectPurchaseWGData.Instance:GetBuyTypeAllNotBuy(1)
	-- local data = PierreDirectPurchaseWGData.Instance:GetDirectPurchaseCfgByType(1)
	-- local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	-- local zpzg_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.ACT_PIERRE_DIRECT_PURCHASE)
	-- local suitable_day = cur_day >= data.open_day and cur_day <= data.close_day
	-- if self.month_card_cost > 0 then
	-- 	if suitable_day and no_buy and not IsEmptyTable(data) and zpzg_isopen then
	-- 		local ok_func = function ()
	-- 			FunOpen.Instance:OpenViewByName(GuideModuleName.PierreDirectPurchaseView)
	-- 			self:Close()
	-- 		end
	
	-- 		local can_func = function ()
	-- 			RechargeWGCtrl.Instance:Recharge(self.month_card_cost, self.month_card_cost_type, self.cur_show_card_type)
	-- 		end
	
	-- 		local price_str = RoleWGData.GetPayMoneyStr(data.buy_all_price, data.rmb_type, data.buy_type)
	-- 		local team_desc = string.format(Language.Recharge.MCTeamDesc, price_str)
	-- 		TipWGCtrl.Instance:OpenAlertTips(team_desc, ok_func, can_func, nil, nil, nil, nil, Language.Recharge.MCTipOkText, Language.Recharge.MCTipCanText)
	-- 	else
	-- 		RechargeWGCtrl.Instance:Recharge(self.month_card_cost, self.month_card_cost_type, self.cur_show_card_type)
	-- 	end
	-- end
end

function RechargeView:OnClickMonthCardRewardBtn()
	local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
	RechargeWGCtrl.Instance:SendFetchInvestCardReward(INVEST_CARD_TYPE.MonthCard, card_info.cur_day)
end

function RechargeView:FlushMonthCardPanel()

	-- 设置显示的月卡类型
	local mc_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
	local mc_can_get_reward, mc_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, mc_info.cur_day)

	local sc_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)
	local sc_can_get_reward, sc_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.StorehouseCard, sc_info.cur_day)

	if mc_state == INVEST_CARD_TIPS.NoInvest and sc_state ~= INVEST_CARD_TIPS.NoInvest then
		self.cur_show_card_type = INVEST_CARD_TYPE.SpreadMonthCard
	else
		self.cur_show_card_type = INVEST_CARD_TYPE.MonthCard
	end


	-- 根据显示的月卡类型显示
	local month_card_cfg = RechargeWGData.Instance:GetTZCardCfg(self.cur_show_card_type)
	self.month_card_cost = month_card_cfg and month_card_cfg.need_rmb or 0
	self.month_card_cost_type = month_card_cfg and month_card_cfg.rmb_type or 0
	local can_active = RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.MonthCard)
	local card_cfg_list = RechargeWGData.Instance:GetTZCardRewardCfg(INVEST_CARD_TYPE.MonthCard, mc_info.cur_day)

	self.node_list.reward_get.text.text = mc_can_get_reward and Language.Marry.LingQu or Language.Common.YiLingQu
	local _cfg = RechargeWGData.Instance:GetZhuanXiangMonthCardCfg()

	-- --奖励格子数据
	-- for k, v in pairs(self.item_cell_list) do
	-- 	v:SetData(card_cfg_list.reward_item)
	-- end
	for i = 0, 2 do
		self.item_cell_list[i]:SetData(card_cfg_list.reward_item[i])
		self.item_cell_list[i]:SetLingQuVisible(mc_state ~= INVEST_CARD_TIPS.NoInvest and not mc_can_get_reward)
	end

	local vip_mc_reward = RechargeWGData.Instance:GetTZJHOtherCfg("reward_item")
	self.vip_mc_reward_list:SetDataList(vip_mc_reward)
	self.vip_mc_reward_list:SetRefreshCallback(function(item_cell, cell_index)
		if item_cell then
			item_cell:SetLingQuVisible(mc_state ~= INVEST_CARD_TIPS.NoInvest)
		end
	end)

	self.node_list["fanhuan_money"].text.text = month_card_cfg.return_gold --返利
	self.node_list.mc_buy_cost:SetActive(can_active)
	self.node_list.fanhuan_bg:SetActive(can_active)
	self.node_list.mc_buy_btn:SetActive(can_active)
	self.node_list.get_reward_remind:SetActive(mc_can_get_reward)
	self.node_list.reward_flag:SetActive(not mc_can_get_reward)

	if not can_active then
		self.node_list.reward_btn:SetActive(mc_can_get_reward)
		self.node_list.reward_flag:SetActive(not mc_can_get_reward)
	else
		self.node_list.reward_btn:SetActive(false)
		self.node_list.reward_flag:SetActive(false)
	end

	local price_str = RoleWGData.GetPayMoneyStr(self.month_card_cost, self.month_card_cost_type, self.cur_show_card_type)
	self.node_list.mc_buy_cost.text.text = price_str

	self.node_list.desk_img:SetActive(mc_state ~= INVEST_CARD_TIPS.NoInvest)

	self.node_list.vip_month_card_text.text.text = Language.Recharge.VIPTeQuanFanLi[1]
	self.node_list.vip_month_card_text_2.text.text = Language.Recharge.VIPTeQuanFanLi[2]
	self.node_list.vip_month_card_title_text.text.text = Language.Recharge.VIPTeQuanFanLi[3]
end

--------------------------------------------------MonthCardItemRender--------------------------------------------------------------------------------
MonthCardItemRender = MonthCardItemRender or BaseClass(BaseRender)

function MonthCardItemRender:__init()
	self.can_get_reward = false
end

function MonthCardItemRender:__delete()
	self.item_cell:DeleteMe()
	self.item_cell = nil
end

function MonthCardItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_cell_root)
	self.item_cell:SetData({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_GOLD})
	self.node_list.get_reward_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function MonthCardItemRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end
	self.node_list.name_label.text.text = string.format(Language.Welfare.diXtian, data.day + 1)
	self.node_list.xianyu_label.text.text = data.reward_gold_bind
	self.node_list.tongbi_label.text.text = data.reward_coin
	self.node_list.desc_label.text.text = data.desc or ""
	
	local can_get_reward,card_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, data.day)

	self.node_list.get_reward_img:SetActive(card_state == INVEST_CARD_TIPS.GetReward)
	self.node_list.pass_reward_img:SetActive(card_state == INVEST_CARD_TIPS.PassDay)
	self.node_list.get_reward_btn:SetActive(can_get_reward or card_state == INVEST_CARD_TIPS.NoInvest or card_state == INVEST_CARD_TIPS.NoDay)
	self.node_list.remind:SetActive(can_get_reward)
	XUI.SetGraphicGrey(self.node_list.get_reward_btn, not can_get_reward)
end

function MonthCardItemRender:OnClickGetReward()
	local can_get_reward,card_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, self.data.day)
	if not can_get_reward then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.InvestCardTips[card_state])
		return
	end
	RechargeWGCtrl.Instance:SendFetchInvestCardReward(INVEST_CARD_TYPE.MonthCard, self.data.day)
end