
function BossAssistView:Kan<PERSON>iaInit()
	self.kanjia_flush_count_down = "kanjia_flush_count_down"
	self.is_wait_flush = false

	XUI.AddClickEventListener(self.node_list.flush_btn, BindTool.Bind(self.OnFlushBtn<PERSON><PERSON><PERSON><PERSON><PERSON>, self))
	self.kajia_list = AsyncListView.New(KanJiaItemRender, self.node_list.kajia_list)

	BossAssistWGCtrl.Instance:CSRoleOpKanJiaItem(KANJIA_OP_TYPE.TYPE_INFO)
end

function BossAssistView:KanJiaReleaseCallBack()
	if self.kanjia_flush_count_down and CountDownManager.Instance:HasCountDown(self.kanjia_flush_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.kanjia_flush_count_down)
	end

	if self.kajia_list then
		self.kajia_list:DeleteMe()
		self.kajia_list = nil
	end
end

function BossAssistView:KanJiaFlush()
	local left_count = BossAssistWGData.Instance:GetKanJiaLeftTime()
	if left_count > 0 then
		self.node_list.left_count.text.text = string.format(Language.BossAssist.KanJiaStr1, left_count)
	else
		self.node_list.left_count.text.text = Language.BossAssist.KanJiaStr2
	end
	
	local list = BossAssistWGData.Instance:GetKanJiaInfoSortList()
	if list ~= nil then
		self.kajia_list:SetDataList(list)
	end
end

function BossAssistView:OnFlushBtnClickHnadler()
	if not self.is_wait_flush then
		self:KanJiaFlush()
		self.is_wait_flush = true
		XUI.SetGraphicGrey(self.node_list.flush_btn, true)
		CountDownManager.Instance:AddCountDown(self.kanjia_flush_count_down, nil, function()
			self.is_wait_flush = false
			XUI.SetGraphicGrey(self.node_list.flush_btn, false)   
		end, TimeWGCtrl.Instance:GetServerTime() + 5, nil, 5)
	end
end

------------------------------------------------------------------------------------------------

KanJiaItemRender = KanJiaItemRender or BaseClass(BaseRender)

function KanJiaItemRender:LoadCallBack()
	self.node_list.assist_btn.button:AddClickListener(BindTool.Bind(self.OnAssistBtnClick, self))
	self.kanjia_head_cell = BaseHeadCell.New(self.node_list["head_icon"])
	self.reward_item_list = AsyncListView.New(QuanMinCommonItemRender, self.node_list.item_list)
end

function KanJiaItemRender:__delete()
	if self.kanjia_head_cell then
		self.kanjia_head_cell:DeleteMe()
		self.kanjia_head_cell = nil
	end

	if self.reward_item_list ~= nil then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end

	if self.kanjia_remain_timer then
		CountDownManager.Instance:RemoveCountDown("kanjia_remain_timer")
		self.kanjia_remain_timer = nil
	end
end

function KanJiaItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	local item_cfg = BossAssistWGData.Instance:GetKanJiaItemInfo(data.kanjia_item_id)
	if item_cfg ~= nil then
		local head_info = {role_id = data.owner_id, sex = data.sex, prof = data.prof, fashion_photoframe = data.fashion_photoframe}
		self.kanjia_head_cell:SetData(head_info)

		self.node_list.old_price.text.text = data.price
		if data.kanjia_cnt > 0 then
			self.node_list.new_price.text.text = math.floor(data.price * (100 - item_cfg.once_sub_percent * data.kanjia_cnt * 1.0) / 100)
		else
			self.node_list.new_price.text.text = ""
		end
		self.node_list.red_line:SetActive(data.kanjia_cnt > 0)

		self.node_list.cost_icon.image:LoadSprite(ResPath.GetF2CommonIcon(ResPath.GetF2MoneyIcon(data.price_type)))

		if data.is_mainrole then
			self.node_list.username.text.text = string.format("<color=#bb01c3>%s</color>" , data.owner_name)
		else
			self.node_list.username.text.text = string.format("<color=#432912>%s</color>" , data.owner_name)
		end

		if data.left_time > 0 then
			if self.kanjia_remain_timer == nil then
				self.node_list.time_label.text.text = string.format("<color=#009621>%s%s</color>", math.ceil(data.left_time / 60.0) ,Language.BossAssist.KanJiaStr3)
				self.kanjia_remain_timer = CountDownManager.Instance:AddCountDown("kanjia_remain_timer", function(elapse_time, total_time)
					if nil == self:GetView() then
						CountDownManager.Instance:RemoveCountDown("kanjia_remain_timer")
						return
					end
					local remain_time = total_time - elapse_time
					local min = math.ceil(remain_time / 60.0)
					self.node_list.time_label.text.text = string.format("<color=#009621>%s%s</color>", min ,Language.BossAssist.KanJiaStr3)
				end, 
				function()
					RemindManager.Instance:Fire(RemindName.KanJiaXieZhu)
					MainuiWGCtrl.Instance:SetXieZhuRemind()
					CountDownManager.Instance:RemoveCountDown("kanjia_remain_timer")
					self.kanjia_remain_timer = nil
					BossAssistWGCtrl.Instance:FlushKanJia()
				end, nil, data.left_time + 2, 1)
			end
		else
			if self.kanjia_remain_timer then
				CountDownManager.Instance:RemoveCountDown("kanjia_remain_timer")
				self.kanjia_remain_timer = nil
			end
			self.node_list.time_label.text.text = string.format("<color=#a5492d>%s</color>", Language.BossAssist.KanJiaStr4)
		end	
		
		if data.is_mainrole then
			self.node_list.myself:SetActive(true)
			self.node_list.assist_btn:SetActive(false)
			self.node_list.yibangman_btn:SetActive(false)
			self.node_list.my_kanjia_count.text.text = string.format("%d/%d", data.kanjia_cnt, item_cfg.max_kanjia_cnt)
		else
			self.node_list.myself:SetActive(false)
			self.node_list.assist_btn:SetActive(data.has_helped == 0)
			self.node_list.yibangman_btn:SetActive(data.has_helped == 1)
		end

		self.reward_item_list:SetDataList(SortTableKey(item_cfg.reward_item))
		local left_count = BossAssistWGData.Instance:GetKanJiaLeftTime()
		XUI.SetGraphicGrey(self.node_list.assist_btn, left_count == 0)
	end
end

function KanJiaItemRender:OnAssistBtnClick()
	BossAssistWGCtrl.Instance:CSRoleOpKanJiaItem(KANJIA_OP_TYPE.TYPE_BANGZHU_KANJIA, self.data.kanjia_item_id, self.data.owner_id)
end

-------------------------------------------------------------------------
KanJiaTipView = KanJiaTipView or BaseClass(SafeBaseView)

function KanJiaTipView:__init()
	self.view_name = "KanJiaTipView"
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(740, 384)})
	self:AddViewResource(0, "uis/view/boss_assist_ui_prefab", "layout_assist_success")
end

function KanJiaTipView:ReleaseCallBack()
	if self.reward_item_list ~= nil then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function KanJiaTipView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.BossAssist.SuccessKanJia
	self.node_list.receive_btn.button:AddClickListener(BindTool.Bind(self.OnReceiveBtnClick, self))
	self.node_list.btn_open.button:AddClickListener(BindTool.Bind(self.OnTipBtnClick, self))
	self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.ph_item_list)
end

function KanJiaTipView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "ser_info" then
			self:RefreshView(v.info)
		end
	end
end

function KanJiaTipView:RefreshView(data)
	if not data then
		return
	end
	
	local item_cfg = BossAssistWGData.Instance:GetKanJiaItemInfo(data.kanjia_item_id)
	if item_cfg ~= nil then
		local zhekou = 10 - item_cfg.once_sub_percent * 1.0 * data.kanjia_cnt / 10
		self.node_list.desc.text.text =  string.format(Language.BossAssist.KanJiaStr5, data.owner_name) 
		self.node_list.zk_label.text.text = string.format(Language.BossAssist.KanJiaStr6, zhekou)
		self.node_list.shengwang.text.text = string.format(Language.BossAssist.KanJiaStr7, item_cfg.help_shengwang_reward)
		self.node_list.old_price.text.text = data.price
		self.node_list.new_price.text.text = math.ceil(data.price * zhekou / 10)
		self.node_list.old_cost_icon.image:LoadSprite(ResPath.GetF2CommonIcon(ResPath.GetF2MoneyIcon(data.price_type)))
		self.node_list.new_cost_icon.image:LoadSprite(ResPath.GetF2CommonIcon(ResPath.GetF2MoneyIcon(data.price_type)))

		self.reward_item_list:SetDataList(SortTableKey(item_cfg.reward_item))
	end
end

function KanJiaTipView:OnReceiveBtnClick()
	self:Close()
end

function KanJiaTipView:OnTipBtnClick()
	QuanMinBeiZhanWGCtrl.Instance:Open(TabIndex.quanmin_beizhan_cap3)
	self:Close()
end