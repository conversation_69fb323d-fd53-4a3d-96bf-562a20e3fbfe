NiuDanRecord = NiuDanRecord or BaseClass(SafeBaseView)

function NiuDanRecord:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
                        {vector2 = Vector2(0, 0), sizeDelta = Vector2(850,500)})
    self:AddViewResource(0, "uis/view/niudan_ui_prefab", "layout_niudan_record")
	self:SetMaskBg(true, true)
end

function NiuDanRecord:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
    self.data_list = nil
end

function NiuDanRecord:ShowIndexCallBack()
    NiuDanWGCtrl.Instance:SendReq(CSA_YANHUA_OP.RECORD)
    NiuDanWGData.Instance:UpdateRecordCount()
end

function NiuDanRecord:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.OAFish.RewardListTitle
    self.record_list = AsyncListView.New(NiuDanRecordRender, self.node_list["record_list"])
    self.record_list:SetCellSizeDel(BindTool.Bind(self.ChangeCellSize,self))

    -- local other_cfg = NiuDanWGData.Instance:GetGradeCfg()
    -- self.node_list.no_flag_text.text.color = Str2C3b(other_cfg.zanwu_text_color)

end

local LINE_SPACING = 20
function NiuDanRecord:ChangeCellSize(data_index)
    local data = self.data_list and self.data_list[data_index + 1] 
    if not data then return 0 end

    local cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',data.item_id)
        return 0
    end
    local str = string.format(Language.FestivalFireworks.Record, data.role_name, ITEM_COLOR[cfg.color], cfg.name, data.num)

    self.node_list["TestText"].text.text = str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)

    local hight = math.ceil(self.node_list["TestText"].rect.rect.height) + LINE_SPACING
    return hight or 0
end

function NiuDanRecord:OnFlush()
    local data = NiuDanWGData.Instance:GetRecordInfo() or {}
    self.data_list = data
    self.record_list:SetDataList(data, 3)
    self.node_list["no_flag"]:SetActive(IsEmptyTable(data))
end

function NiuDanRecord:CloseCallBack()
    NiuDanWGData.Instance:UpdateRecordCount()
    ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2270, "all", {[2] = "record"})
end
-------------------------------------------------------------------------------------
NiuDanRecordRender = NiuDanRecordRender or BaseClass(BaseRender)

function NiuDanRecordRender:LoadCallBack()
    -- local common_color = FestivalActivityWGData.Instance:GetCommonColor()
    -- self.node_list["info"].text.color = Str2C3b(common_color)
end

function NiuDanRecordRender:OnFlush()
    if not self.data then
        return
    end

    --self.node_list['time'].text.text = TimeUtil.FormatYMDHMS(self.data.draw_time)
    local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if not cfg then
        print_error('物品配置读取不到   item_id:',self.data.item_id)
        return
    end
    local str = string.format(Language.FestivalFireworks.Record, self.data.role_name, ITEM_COLOR[cfg.color], cfg.name, self.data.num)
    self.node_list["info"].text.text = str
    self.node_list.bg:SetActive(self:GetIndex() % 2 == 0)
end