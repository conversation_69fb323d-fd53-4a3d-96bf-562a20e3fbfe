XunBaoCellRender = XunBaoCellRender or BaseClass(BaseRender)

function XunBaoCellRender:__init()
    self.tween_list = {}

end

function XunBaoCellRender:__delete()
    if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end

    self:CancelDelay()
    self.tween_list = {}

    self.ori_parent = nil
    self.ori_pos = nil
    self.fun_list = nil
    self.load_callback = nil
    self.is_tween = nil
    self.open_type = nil
    self.rot2 = nil
end

function XunBaoCellRender:AddClickEventListener(callback)
    if not callback then
    	return
    end
    self.need_click_listen = false
	self.click_callback = callback
	local fun = function()
		self:OnClick()
	end
	if self.node_list["card"] then
		self.node_list["card"].button:AddClickListener(fun)
	end
end

function XunBaoCellRender:LoadCallBack()
    --self.node_list["card"].button:AddClickListener(function()
    --    print_error("123")
    --end)
    self.load_callback = true
    self:ExcuteDelayFunc()
end

function XunBaoCellRender:SetOpenType(status, rot)
    self.open_type = status
    self.node_list["card"]:SetActive(status)
    self:GetCell()
    self.cell:SetActive(not status)
    rot = rot or self.node_list["anim"].rect.eulerAngles.y
    --self.node_list["anim"].rect.localRotation = Quaternion.Euler(0, rot, 0)
    self:SetCellRot(math.abs(rot%360 - 0) < 30 and 0 or 180)
end

function XunBaoCellRender:OnFlush()
    if self.data.is_prepare then
        self:GetCell()
        self:SetOpenType(false)
        self.cell:SetData(self.data.reward_item)
        self:SetCellSelectEffect(false)
    else
        self:SetOpenType(self.data.id == 0)
        if self.data.id ~= 0 then
            self:GetCell()
            self:FlushCell()
            self:SetCellSelectEffect(true)
        --else
            --self.cell:SetSelectEffect(false)
        end
    end
    self.node_list["card"].image:LoadSprite(ResPath.GetXunBaoImg("card_" .. self.data.layer))
end

function XunBaoCellRender:SetCellSelectEffect(state)
    self.cell:SetSelectEffect(state)
end

function XunBaoCellRender:FlushCell()
    local info = NewXunbaoWGData.Instance:GetTBLongInfoById(self.data.id ,self.data.layer)
    self.cell:SetData(info.reward_item)
end

function XunBaoCellRender:GetCell()
    if not self.load_callback then
        self:AddDelayFunc(BindTool.Bind(self.GetCell, self))
        return
    end
    if not self.cell then
        self.cell = ItemCell.New(self.node_list["cell"])
        self.cell:UseNewSelectEffect(true)
    end
end

function XunBaoCellRender:SetCellRot(val)
    self.node_list["cell"].rect.localRotation = Quaternion.Euler(0, val, 0)
    self.node_list["card"].rect.localRotation = Quaternion.Euler(0, val, 0)
    self.node_list["shadow"].rect.localRotation = Quaternion.Euler(0, val, 0)
end

function XunBaoCellRender:PlaySelectAnim()
    self:FlushCell()
    self.is_tween = true
    local other_cfg = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg()
    local select_scale = other_cfg.select_scale or 1.3
    local scale_time = other_cfg.scale_time or 0.3
    local shadow_dis = other_cfg.shadow_dis or 5
    local shadow_alpha = other_cfg.shadow_alpha or 0.2
    local delay_time = other_cfg.delay_time or 0.5
    local card_rot_time = other_cfg.card_rot_time

    local tween = DG.Tweening.DOTween.Sequence()
    local tween1 = self.node_list["anim"].rect:DOScale(Vector3(select_scale, select_scale, select_scale),scale_time)
    tween1:SetEase(DG.Tweening.Ease.InQuad)
    local tween2 = self.node_list["shadow"].shadow:DoEffectDistance(Vector2(0,0), Vector2(shadow_dis,-shadow_dis), scale_time, function()end)
    tween2:SetEase(DG.Tweening.Ease.InQuad)
    local tween3 = self.node_list["shadow"].shadow:DoEffectColor(self.node_list["shadow"].shadow.effectColor, Color.New(0,0,0,shadow_alpha), scale_time, function()end)
    tween3:SetEase(DG.Tweening.Ease.InQuad)
    tween:Join(tween1)
    tween:Join(tween2)
    tween:Join(tween3)

    tween:AppendInterval(scale_time + delay_time)
    tween:AppendCallback(function()
        self:PlayRotAnim(true)
    end)

    tween:AppendInterval(delay_time + card_rot_time * 2)
    tween:AppendCallback(function()
        self:OnSelectEndAnim()
    end)

    tween:OnComplete(function()
        tween:Kill()
        tween = nil
        self.tween_list[tween] = nil
    end)
    self.tween_list[tween] = tween
end

function XunBaoCellRender:OnSelectEndAnim()
    local other_cfg = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg()
    local fall_time = other_cfg.fall_time or 0.1
    local shadow_dis = other_cfg.shadow_dis or 5

    local tween = DG.Tweening.DOTween.Sequence()
    local tween1 = self.node_list["anim"].rect:DOScale(Vector3(0.9, 0.9, 0.9),fall_time)
    tween1:SetEase(DG.Tweening.Ease.InQuad)
    local tween2 = self.node_list["shadow"].shadow:DoEffectDistance(Vector2(shadow_dis,shadow_dis), Vector2(0,0), fall_time, function()end)
    tween2:SetEase(DG.Tweening.Ease.InQuad)
    local tween3 = self.node_list["shadow"].shadow:DoEffectColor(self.node_list["shadow"].shadow.effectColor, Color.New(0,0,0,0.5), fall_time, function()end)
    tween3:SetEase(DG.Tweening.Ease.InQuad)

    tween:Join(tween1)
    tween:Join(tween2)
    tween:Join(tween3)
    tween:OnComplete(function()
        local info = NewXunbaoWGData.Instance:GetTBLongInfoById(self.data.id ,self.data.layer)
        if info.is_shake == 1 then
            NewXunbaoWGCtrl.Instance:ShakeRoot()
        end
        if info.effect_level > 0 then
            GlobalEventSystem:Fire(OtherEventType.TAOBAO_EFFECT, info.effect_level,
                    self.data.ori_pos, BindTool.Bind(self.SelectEndCell, self))
        else
            self:SelectEndCell()
        end
        tween:Kill()
        tween = nil
        self.tween_list[tween] = nil
    end)
    self.tween_list[tween] = tween
end

function XunBaoCellRender:SelectEndCell()
    self:ResetParent()
    local cfg = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg()
    self.select_end_delay_flush = GlobalTimerQuest:AddDelayTimer(function()
        self:Flush()
        self.is_tween = false
    end, cfg.check_yes_time or 1)
end

function XunBaoCellRender:PlayCellStartAnim(center_node)
    if not self.load_callback then
        self:AddDelayFunc(BindTool.Bind(self.PlayCellStartAnim, self, center_node))
        return
    end
    self.is_tween = true
    local toCenterAnim = function()
        self:SetNewParent(center_node)
        self:PlayToCenterAnim()
    end
    self:PlayRotAnim(false, toCenterAnim)
end

function XunBaoCellRender:PlayRotAnim(is_open, callback)
    local card_rot_time = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg().card_rot_time
    card_rot_time = card_rot_time and card_rot_time/2 or 0.25
    --local rot = 180
    local rot = -180
    local rot1 = self.node_list["anim"].rect.eulerAngles.y + rot/2
    self.rot2 = self.node_list["anim"].rect.eulerAngles.y + rot

    local tween = DG.Tweening.DOTween.Sequence()

    tween:AppendCallback(function()
        local tween = self.node_list["anim"].rect:DORotate(Vector3(0,rot1,0), card_rot_time)
        tween:OnComplete(function()
            tween:Kill()
            tween = nil
            self.tween_list[tween] = nil
        end)
        self.tween_list[tween] = tween
    end)

    tween:AppendInterval(card_rot_time)
    tween:AppendCallback(function()
        self:SetOpenType(not is_open, self.rot2)
        local tween = self.node_list["anim"].rect:DORotate(Vector3(0,self.rot2,0), card_rot_time)
        tween:OnComplete(function()
            tween:Kill()
            tween = nil
            self.tween_list[tween] = nil
        end)
        self.tween_list[tween] = tween
    end)

    tween:AppendInterval(card_rot_time)

    tween:OnComplete(function()
        if callback then
            callback()
        end
        tween:Kill()
        tween = nil
        self.tween_list[tween] = nil
    end)

    self.tween_list[tween] = tween
end

function XunBaoCellRender:PlayToCenterAnim()
    local dif_pixel = NewXunbaoWGData.Instance:GetAnimDifValue(self.data.layer, self.data.pos)
    local target_pos = Vector3(0, 0 + dif_pixel, 0)
    local to_center_time = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg().to_center_time or 1.5
    local tween = self.node_list["root_cell"].rect:DOAnchorPos(target_pos, to_center_time):SetEase(DG.Tweening.Ease.InExpo)
    tween:OnComplete(function()
        tween:Kill()
        tween = nil
        self.tween_list[tween] = nil
    end)
    self.tween_list[tween] = tween
end

function XunBaoCellRender:PlayMoveBackAnim()
    self:ResetParent()
    local to_center_time = NewXunbaoWGData.Instance:GetTBLongPerformanceCfg().to_center_time or 1.5
    local tween = self.node_list["root_cell"].rect:DOAnchorPos(Vector3(0,0,0), to_center_time)
    tween:SetEase(DG.Tweening.Ease.InExpo)
    tween:OnComplete(function()
       self.is_tween = false
       tween:Kill()
       tween = nil
       self.tween_list[tween] = nil
   end)

    self:GetCell()
    self:SetCellSelectEffect(false)
    self.tween_list[tween] = tween
end

function XunBaoCellRender:SetAnimActive(status)
    if not self.load_callback then
        self:AddDelayFunc(BindTool.Bind(self.SetAnimActive, self, status))
        return
    end
    self.node_list["anim"]:SetActive(status)
    self.is_tween = false
end

--function XunBaoCellRender:SetResetAnim(callback)
--    if not self.load_callback then
--        self:AddDelayFunc(BindTool.Bind(self.SetResetAnim, self))
--        return
--    end
--    if self.data.id ~= 0 then
--        self:GetCell()
--        self.cell:SetSelectEffect(false)
--        GlobalTimerQuest:AddDelayTimer(function()
--            self:PlayRotAnim(false, callback)
--        end, 0.5)
--    end
--end

function XunBaoCellRender:AddDelayFunc(fun)
    if not self.fun_list then
        self.fun_list = {}
    end
    table.insert(self.fun_list, fun)
end

function XunBaoCellRender:ExcuteDelayFunc()
    if not self.fun_list then
        return
    end
    for i, v in ipairs(self.fun_list) do
        v()
    end
    self.fun_list = {}
end

function XunBaoCellRender:SetNewParent(node)
    if not self.load_callback then
        self:AddDelayFunc(BindTool.Bind(self.SetNewParent, self, node))
        return
    end
    self.ori_parent = self.node_list["root_cell"].transform.parent
    self.node_list["root_cell"].transform:SetParent(node.transform, true)
end

function XunBaoCellRender:ResetParent()
    if self.ori_parent then
        self.node_list["root_cell"].transform:SetParent(self.ori_parent, true)
        self.ori_parent = nil
    end
end

function XunBaoCellRender:GetIsTween()
    return self.is_tween
end

function XunBaoCellRender:PlayCardEffect(status)
    if not self.load_callback then
        self:AddDelayFunc(BindTool.Bind(self.PlayCardEffect, self, status))
        return
    end
    self.node_list["effect"]:SetActive(status)
end

function XunBaoCellRender:ResetCell(node)
    if not self.data then
        return
    end

    if not self.load_callback then
        self:AddDelayFunc(BindTool.Bind(self.ResetCell, self, node))
        return
    end

    for i, v in pairs(self.tween_list) do
        v:Kill()
    end

    self:CancelDelay()

    local rect = self.node_list["root_cell"].transform
    local shadow = self.node_list["shadow"].shadow
    local anim = self.node_list["anim"].rect

    rect:SetParent(node.transform, true)
    rect.anchoredPosition = Vector2(0,0)

    anim.localScale = Vector3(0.9,0.9,0.9)
    if self.rot2 then
        anim.localRotation = Quaternion.Euler(0, self.rot2, 0)
        self.rot2 = nil
    end

    shadow.effectColor = Color.New(0,0,0,0.5)
    shadow.effectDistance = Vector2(0,0)

    --self:Flush()
end

function XunBaoCellRender:CancelDelay()
    if self.select_end_delay_flush then
        GlobalTimerQuest:CancelQuest(self.select_end_delay_flush)
        self.select_end_delay_flush = nil
    end
end

function XunBaoCellRender:PlayCardAudio()
    local info = NewXunbaoWGData.Instance:GetTBLongInfoById(self.data.id ,self.data.layer)
    local str = info.is_shake == 1 and "effect_zhenlong_fanpai_teshu" or "effect_zhenlong_fanpai_putong"
    AudioManager.PlayAndForget(ResPath.UiseRes(str))
end