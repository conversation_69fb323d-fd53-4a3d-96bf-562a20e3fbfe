function NewAppearanceWGView:ActLoadCallBack()
    XUI.AddClickEventListener(self.node_list["btn_cb"], BindTool.Bind(self.OnBtnCP, self))
    XUI.AddClickEventListener(self.node_list["btn_zl"], BindTool.Bind(self.OnBtnZL, self))
    XUI.AddClickEventListener(self.node_list["fs_btn_fetter"], BindTool.Bind(self.OpenHuanHuaFetterView, self))

    FunOpen.Instance:RegisterFunUi(FunName.NewHuanHuaFetterView, self.node_list["fs_btn_fetter"])  
end

function NewAppearanceWGView:ActReleaseCallBack()
    CountDownManager.Instance:RemoveCountDown("act_end_time")
    self.rush_type = -1

    if self.box_tween ~= nil then
        self.box_tween:Kill()
        self.box_tween = nil
    end
end

function NewAppearanceWGView:ActShowIndexCallBack(rush_type)
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ACTIVITY)
    if not is_open or not rush_type then
        return
    end

    local is_act_open = NewAppearanceWGData.Instance:CheckBPActIsOpenByRushType(rush_type)
    self.rush_type = rush_type
    if is_act_open then
        local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
        RankWGCtrl.Instance:SendActRankListReq(opengame_cfg.rank_type)
    end
end

function NewAppearanceWGView:ActOnFlush(rush_type)
	local role_level = RoleWGData.Instance:GetAttr('level')
    local level_limit = NewAppearanceWGData.Instance:GetFashionOtherCfgByKey("level_show2") or 0

    if role_level < level_limit then
        self.node_list.bp_act_root:SetActive(false)
        return
    end

    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ACTIVITY)
    if not is_open or not rush_type then
        self.node_list.bp_act_root:SetActive(false)
        return
    end

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local is_act_open = NewAppearanceWGData.Instance:CheckBPActIsOpenByRushType(rush_type)
    local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
    self.node_list.bp_act_root:SetActive(is_act_open)
    if not is_act_open then
        return
    end

    local show_red_point = ServerActivityWGData.Instance:OpenServerCompetitonSingleReward(rush_type)
    self.node_list.cb_remind:SetActive(show_red_point)
    self.node_list.act_name.text.text = opengame_cfg.ranking_name
    self:FlushActCountDownTime(rush_type)
    if show_red_point then
        if self.box_tween == nil then
            self.box_tween = DG.Tweening.DOTween.Sequence()
            UITween.ShakeAnimi(self.node_list.btn_cb.transform, self.box_tween, 2)
        end
    else
        if self.box_tween ~= nil then
            self.box_tween:Kill()
            self.box_tween = nil
        end
        
        self.node_list.btn_cb.transform.localRotation = Quaternion.Euler(0, 0, 0)
    end
end

function NewAppearanceWGView:ActFlushMyRank(data, rush_type)
    if data.protocol.self_rank > 0 then
        local rank_str = data.protocol.self_rank
        self.node_list.bp_act_rank_index.text.text = string.format(Language.NewAppearance.MyRank, rank_str)
    else
        self.node_list.bp_act_rank_index.text.text = string.format(Language.NewAppearance.NoRank)
    end
end

function NewAppearanceWGView:FlushActCountDownTime(rush_type)
    CountDownManager.Instance:RemoveCountDown("act_end_time")
    local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)
    local close_day = opengame_cfg.close_day_index
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local format_time = os.date("*t", now_time)
    local end_time = os.time({
        year = format_time.year,
        month = format_time.month,
        day = format_time.day + close_day - open_day + 1,
        hour = 0,
        min = 0,
        sec = 0
    })
    local act_is_jiesuan = now_time < end_time and now_time > end_time - 3600
    local act_is_over = now_time > end_time
    local count_down_time = end_time - 3600 -- 23:00结算
    if now_time < count_down_time then
        self:ActUpdateCountDown(now_time, count_down_time)
        CountDownManager.Instance:AddCountDown(
            "act_end_time",
            BindTool.Bind(self.ActUpdateCountDown, self),
            BindTool.Bind(self.ActCompleteCountDown, self),
            count_down_time,
            nil,
            1
        )
    end
    
    if act_is_jiesuan then
        --self.node_list.bp_act_time.text.text = Language.OpenServer.BiPinMailReward
    elseif act_is_over then
        --self.node_list.bp_act_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
    end
    --self.node_list.bp_act_time.text.color = act_is_over and Str2C3b(COLOR3B.D_PINK) or Str2C3b(COLOR3B.DEFAULT_NUM)
end

function NewAppearanceWGView:ActUpdateCountDown(elapse_time, total_time)
    local str = TimeUtil.FormatDToHAndMS(total_time - elapse_time)
    self.node_list.bp_act_time.text.text = string.format(Language.NewAppearance.ActTimeDesc, str)
end

function NewAppearanceWGView:ActCompleteCountDown()
    ServerActivityWGCtrl.Instance:SendCSOGARushRankOp(OGA_RUSH_RANK_OP_TYPE.RUSH_RANK_REWARD_TYPE_GOAL_FETCH)
end

function NewAppearanceWGView:TabbleActShow()
    
end

function NewAppearanceWGView:OnBtnCP()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ACTIVITY)
    if is_open then
        ViewManager.Instance:Open(GuideModuleName.KfActivityView, nil, "jump_to", {rush_type = self.rush_type})
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
    end
end

function NewAppearanceWGView:OnBtnZL()
    local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
    if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
        ViewManager.Instance:Open(GuideModuleName.HelpRankView)
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
    end
end

function NewAppearanceWGView:OpenHuanHuaFetterView()
    ViewManager.Instance:Open(GuideModuleName.NewHuanHuaFetterView)
end

function NewAppearanceWGView:OnNewAppearanceACTRemindChange(remind_name, num)
    if remind_name == RemindName.NewHuanHuaFetterView then
        self.node_list.fetter_btn_remind:CustomSetActive(num > 0)
    end
end