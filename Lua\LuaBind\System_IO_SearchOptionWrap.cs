﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class System_IO_SearchOptionWrap
{
	public static void Register(LuaState L)
	{
		L.Begin<PERSON>num(typeof(System.IO.SearchOption));
		<PERSON><PERSON>("TopDirectoryOnly", get_TopDirectoryOnly, null);
		<PERSON><PERSON>("AllDirectories", get_AllDirectories, null);
		<PERSON><PERSON>unction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<System.IO.SearchOption>.Check = CheckType;
		StackTraits<System.IO.SearchOption>.Push = Push;
	}

	static void Push(IntPtr L, System.IO.SearchOption arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(System.IO.SearchOption), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TopDirectoryOnly(IntPtr L)
	{
		ToLua.Push(L, System.IO.SearchOption.TopDirectoryOnly);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AllDirectories(IntPtr L)
	{
		ToLua.Push(L, System.IO.SearchOption.AllDirectories);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		System.IO.SearchOption o = (System.IO.SearchOption)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

