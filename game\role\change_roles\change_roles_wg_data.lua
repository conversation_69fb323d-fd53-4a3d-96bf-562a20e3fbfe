ChangeRolesWGData = ChangeRolesWGData or BaseClass()
function ChangeRolesWGData:__init()
	if ChangeRolesWGData.Instance then
		error("[ChangeRolesWGData] Attempt to create singleton twice!")
		return
	end

	ChangeRolesWGData.Instance = self

    self.next_change_timestamp = 0
    self.role_act_flag = {}
    self.default_diy_list = {}

    local cfg = ConfigManager.Instance:GetAutoConfig("zhuanzhicfg_auto")
    self.rmb_buy_cfg = ListToMap(cfg.zhuanzhi_rmb_buy, "sex", "prof")
end

function ChangeRolesWGData:__delete()
    ChangeRolesWGData.Instance = nil
end

function ChangeRolesWGData:GetRMBBuyCfg(sex, prof)
    return (self.rmb_buy_cfg[sex] or {})[prof]
end

function ChangeRolesWGData:SetChangeRoleInfo(protocol)
	self.next_change_timestamp = protocol.next_change_timestamp
	self.role_act_flag = bit:d2b_l2h(protocol.role_act_flag, nil, true)
    self.default_diy_list = protocol.default_diy_list
end

function ChangeRolesWGData:GetRoleIsAct(sex, prof)
    local cfg = self:GetRMBBuyCfg(sex, prof)
    if cfg then
        return self.role_act_flag[cfg.seq] == 1
    end

    return false
end

function ChangeRolesWGData:GetNextChangeCD()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local time = self.next_change_timestamp - server_time + 1750
    return time > 0 and time or 0
end

function ChangeRolesWGData:GetRoleSetDIYInfo(sex, prof)
    local cfg = self:GetRMBBuyCfg(sex, prof)
    local default_face, default_hair, default_body = RoleWGData.Instance:GetAllDefaultPartID(sex, prof)
    if cfg == nil then
        return default_face, default_hair, default_body
    end

    local info = self.default_diy_list[cfg.seq]
    if info == nil or info.is_set == 0 then
        return default_face, default_hair, default_body
    end

    return info.default_face_res_id, info.default_hair_res_id, info.default_body_res_id
end

