ControlBeastsWGView = ControlBeastsWGView or BaseClass(SafeBaseView)

function ControlBeastsWGView:__init()
	self:SetMaskBg(false, true)
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.default_index = TabIndex.beasts_culture
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)

    local common_bundle = "uis/view/common_panel_prefab"
	local bundle_name = "uis/view/control_beasts_ui_prefab"
	--self:AddViewResource({TabIndex.beasts_culture, TabIndex.beasts_compose, TabIndex.beasts_book}, bundle_name, "layout_beasts_bg_panel")
	self:AddViewResource(TabIndex.beasts_culture, bundle_name, "layout_beasts_culture")
	self:AddViewResource(TabIndex.beasts_battle, bundle_name, "layout_beasts_battle")
	self:AddViewResource(TabIndex.beasts_alchemy, bundle_name, "layout_beasts_alchemy")
	self:AddViewResource(TabIndex.beasts_alchemy, bundle_name, "layout_beasts_alchemy_bag_grid")
	-- self:AddViewResource(TabIndex.beasts_refining, bundle_name, "layout_beasts_root_refining")
	-- self:AddViewResource(TabIndex.beasts_contract, bundle_name, "control_beasts_contract_view")
	-- self:AddViewResource(TabIndex.beasts_stable, bundle_name, "layout_beasts_stable")
	self:AddViewResource(TabIndex.beasts_compose, bundle_name, "layout_beasts_compose")
	self:AddViewResource(TabIndex.beasts_book, bundle_name, "control_beasts_contract_hand_book")
	-- self:AddViewResource(TabIndex.beasts_depose, bundle_name, "layout_beasts_despose")
	self:AddViewResource(0, bundle_name, "layout_beasts_bag_grid")
	self:AddViewResource(0, common_bundle, "VerticalTabbar")
    self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
	-- self:AddViewResource(TabIndex.beasts_stable, bundle_name, "layout_beasts_stable_bag")
	self:AddViewResource(0, bundle_name, "layout_beasts_effect_view")

	self.remind_tab = {
		{RemindName.BeastsCulture},--, RemindName.BeastsKing
		{RemindName.BeastsBattle},
		-- {RemindName.BeastsRefining},
		-- {RemindName.BeastsContract},
		-- {RemindName.BeastsStable},
		{RemindName.BeastsInnerAlchemy},
		{RemindName.BeastsCompose},
		{RemindName.BeastsHandBook},
	}

	self:SetTabShowUIScene(TabIndex.beasts_culture, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BEASTS_BASE_INFO})
	self:SetTabShowUIScene(TabIndex.beasts_battle, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BEASTS_BASE_INFO})
	self:SetTabShowUIScene(TabIndex.beasts_alchemy, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BEASTS_BASE_INFO})
	self:SetTabShowUIScene(TabIndex.beasts_compose, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BEASTS_BASE_INFO})
	self:SetTabShowUIScene(TabIndex.beasts_book, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.BEASTS_BASE_INFO})
end

function ControlBeastsWGView:__delete()

end

function ControlBeastsWGView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind(self.SetMaskBtnsActive, self))
		self.tabbar:Init(Language.ContralBeasts.TabGrop, nil, nil, nil, self.remind_tab, BeastsVerticalItemRender)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar.IsVerLimitSelected = function(tabbar, index)
			-- 选中内丹时如果不存在上阵幻兽不给予切换
			if index == TabIndex.beasts_alchemy and ControlBeastsWGData.Instance:GetBattleBeastsAllLevel() == 0 then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.AlchemyTabSelectError)
				return true
			end
		end
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.ControlBeastsView, self.tabbar)
	end

	-- 模块名称
	self.node_list.title_view_name.text.text = Language.ContralBeasts.TitleName
	-- 创建货币栏
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_beast = true, show_gold = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self:InitAllBeastGrid()
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ControlBeastsView, self.get_guide_ui_event)
end

function ControlBeastsWGView:SetMaskBtnsActive()
    if self:IsOpen() and self:IsLoaded() then
        local ver_list = self.tabbar and self.tabbar:GetVerCellList()
        --是否穿戴装备
        local has_beasts = ControlBeastsWGData.Instance:GetCultureBeastsListCount() >= 1
        if not IsEmptyTable(ver_list) then
            for k, v in pairs(ver_list) do
                if k == 6 then
                    v:SetMaskBtnActive(not has_beasts)
                end
            end
        end
    end
end

function ControlBeastsWGView:ReleaseCallBack()
	self:ReleaseBattleViewCallBack()
	self:ReleaseCultureViewCallBack()
	-- self:ReleaseFefiningViewCallBack()
	self:ReleaseComposeViewCallBack()
	-- self:ReleaseStableViewCallBack()
	-- self:ReleaseDesposeViewCallBack()
	-- self:ReleaseBeastsContractCallBack()
	self:ReleaseAllBeastGrid()
	self:ReleaseBeastHandBookCallBack()
	self:ReleaseAlchemyViewCallBack()
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ControlBeastsView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
	-- 销毁 MoneyBar
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self.culture_list_data = nil
	self.beast_bag_type_index = nil
	self.select_beast_index = nil
	self.select_beast_data = nil
end

function ControlBeastsWGView:LoadIndexCallBack(index)
	if index == TabIndex.beasts_battle then
		self:LoadBattleViewCallBack()
	elseif index == TabIndex.beasts_culture then
		self:LoadCultureViewCallBack()
	-- elseif index == TabIndex.beasts_refining then
	-- 	self:LoadFefiningViewCallBack()
	-- elseif index == TabIndex.beasts_stable then
	-- 	self:LoadStableViewCallBack()
	-- elseif index == TabIndex.beasts_contract then
	-- 	self:LoadContractViewCallBack()
	elseif index == TabIndex.beasts_alchemy then
		self:LoadAlchemyViewCallBack()
	elseif index == TabIndex.beasts_compose then
		self:LoadComposeViewCallBack()
	-- elseif self.show_index == TabIndex.beasts_depose then
	-- 	self:LoadDesposeViewCallBack()
	elseif index == TabIndex.beasts_book then
		self:LoadBookViewCallBack()
	end
end

function ControlBeastsWGView:OpenCallBack()
	TaskGuide.Instance:SideTOStopTask(true)
	if self.show_index == TabIndex.beasts_battle then
		self:OpenBattleViewCallBack()
	elseif self.show_index == TabIndex.beasts_culture then
		self:OpenCultureViewCallBack()
	-- elseif self.show_index == TabIndex.beasts_refining then
	-- 	self:OpenFefiningViewCallBack()
	-- elseif self.show_index == TabIndex.beasts_stable then
	-- 	self:OpenStableViewCallBack()
	-- elseif self.show_index == TabIndex.beasts_contract then
	-- 	self:OpenContractViewCallBack()
	elseif index == TabIndex.beasts_alchemy then
		self:OpenAlchemyViewCallBack()
	elseif self.show_index == TabIndex.beasts_compose then
		self:OpenComposeViewCallBack()
	-- elseif self.show_index == TabIndex.beasts_depose then
	-- 	self:OpenDesposeViewCallBack()
	elseif self.show_index == TabIndex.beasts_book then
		self:OpenBookViewCallBack()
	end
end

function ControlBeastsWGView:CloseCallBack()
	TaskGuide.Instance:SideTOStopTask(false)
	self:CloseBattleViewCallBack()
	self:CloseCultureViewCallBack()
	self:CloseStableViewCallBack()
	self:CloseComposeViewCallBack()
	self:CloseBookViewCallBack()
	self:CloseAlchemyViewCallBack()

	self.culture_list_data = nil
	self.beast_bag_type_index = nil
	self.select_beast_index = nil
	self.select_beast_data = nil
	self.now_star_up_beast_id = nil
end

function ControlBeastsWGView:ShowIndexCallBack(index)
	local bg_str = "a3_hs_bj_1"

	if index == TabIndex.beasts_battle then
		self:ShowBattleViewCallBack()
	elseif index == TabIndex.beasts_culture then
		self:ShowCultureViewCallBack()
	-- elseif index == TabIndex.beasts_refining then
	-- 	self:ShowFefiningViewCallBack()
	-- 	bg_str = "a3_hs_bj_2"
	-- elseif index == TabIndex.beasts_stable then
	-- 	ControlBeastsWGCtrl.Instance:SendOperateTypeArrangeEggBag()
	-- 	self:ShowStableViewCallBack()
	-- elseif index == TabIndex.beasts_contract then
	-- 	bg_str = "a3_hs_bj_3"
	-- 	self:ShowContractViewCallBack()
	elseif index == TabIndex.beasts_compose then		-- 合成时整理一下孵化背包
		-- ControlBeastsWGCtrl.Instance:SendOperateTypeSortBeastBag()
		self:ShowComposeViewCallBack()
		bg_str = "a3_hs_bj_2"
	-- elseif index == TabIndex.beasts_depose then
	-- 	self:ShowDesposeViewCallBack()
	-- 	bg_str = "a3_hs_bj_2"
	elseif index == TabIndex.beasts_book then
		self:ShowBookViewCallBack()
	elseif index == TabIndex.beasts_alchemy then
		self:ShowAlchemyViewCallBack()
	end

	if index ~= TabIndex.beasts_culture then		--or index ~= TabIndex.beasts_refining 
		self:ChangeCutureTableIndex()
		self:ChangeRefiningTableIndex()
	end

	if self.node_list and self.node_list.beasts_bag_grid_content then
		self.node_list.beasts_bag_grid_content:CustomSetActive(index == TabIndex.beasts_culture or index == TabIndex.beasts_compose)
	end

	-- -- 这里切换背景
	-- local bundle, asset = ResPath.GetControlBeastsBgImg(bg_str)
	-- if bundle and asset then
	-- 	self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
	-- 		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- 	end)
	-- end
end

function ControlBeastsWGView:FlushCurShowView(param_t, key)
	self:Flush(self.show_index, key, param_t)
end

-- 设置是否为分解
function ControlBeastsWGView:SetBeastBreakStatus(is_beast_break)
	self.now_beast_break = is_beast_break
end

function ControlBeastsWGView:OnFlush(param_t, index)
	if self.now_beast_break then
		self.select_beast_index = nil
		self.select_beast_data = nil
		self.now_beast_break = false
	end

	if (index == TabIndex.beasts_culture and self.show_index == TabIndex.beasts_culture) then
		self:FlushAllBeastGridData()
	-- 升星特殊处理，整理背包第一次刷新一下，后面整理背包不做刷新
	elseif (index == TabIndex.beasts_compose and self.show_index == TabIndex.beasts_compose) then
		for k,v in pairs(param_t) do
			if k == "star_up" then
				self:FlushAllBeastGridData(v.bag_id)
				break
			else
				self:FlushAllBeastGridData()
			end
		end
	elseif (index == TabIndex.beasts_alchemy and self.show_index == TabIndex.beasts_alchemy) then
		self:FlushAllBeastGridData()
	end

	if index == TabIndex.beasts_battle then
		self:FlushBattleViewCallBack(param_t)
	elseif index == TabIndex.beasts_culture then
		self:FlushCultureViewCallBack(param_t)
	-- elseif index == TabIndex.beasts_refining then
	-- 	self:FlushFefiningViewCallBack(param_t)
	-- elseif index == TabIndex.beasts_stable then
	-- 	self:FlushStableViewCallBack(param_t)
	-- elseif index == TabIndex.beasts_contract then
	-- 	self:FlushContractViewCallBack(param_t)
	elseif index == TabIndex.beasts_compose then
		self:FlushComposeViewCallBack(param_t)
	-- elseif index == TabIndex.beasts_depose then
	-- 	self:FlushDesposeViewCallBack(param_t)
	elseif index == TabIndex.beasts_book then
		self:FlushBookViewCallBack(param_t)
	elseif index == TabIndex.beasts_alchemy then
		self:FlushAlchemyViewCallBack(param_t)
	end
end


-- 升级成功或突破成功特效
function ControlBeastsWGView:BeastOperateFinalEffect(ui_effect_type, is_center)
	local node_root = self.node_list["operate_effect_root"]
	if is_center then
		node_root = self.node_list["layout_a2_common_top_panel"]
	end
	TipWGCtrl.Instance:ShowEffect({effect_type = ui_effect_type,
						is_success = true, pos = Vector2(0, 0), parent_node = node_root})

	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
end


function ControlBeastsWGView:ShowBeastKingLevelChangeEffect()
	if self.show_index == TabIndex.beasts_king then
		self:ExecuteBeastKingLevelChangeEffect()
	end
end

function ControlBeastsWGView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == "btn_culture_upgrade" then
		return self.node_list[ui_name], BindTool.Bind(self.OperateAutoCulture, self)
	elseif ui_name == "battle_render_01" then
		return self.node_list[ui_name], BindTool.Bind(self.OperateChangeBattleSlotOne, self)
	elseif ui_name == "beasts_bag_grid_change_battle" then
		return self.node_list[ui_name], function()
			self:ChangeToIndex(TabIndex.beasts_battle)
		end
	else
		return self.node_list[ui_name]
	end
end
----------------------------------------------新修改增加一个总孵化的背包---------------------------------------------------------------
-- 创建已孵化背包
function ControlBeastsWGView:InitAllBeastGrid()
	if not self.beasts_list_grid then
        self.beasts_list_grid = BeastsBagGird.New()
        self.beasts_list_grid:CreateCells({
										col = 2, 
										cell_count = BEAST_DEFINE.BEAST_BORN_COUNT_MAX, 
										list_view = self.node_list["beasts_bag_grid"], 
										itemRender = BeastsMessageItem,
										change_cells_num = 0,
										assetBundle = "uis/view/control_beasts_ui_prefab",
										assetName = "layout_beasts_bag_item",
		})
		self.beasts_list_grid:SetStartZeroIndex(false)
        self.beasts_list_grid:SetSelectCallBack(BindTool.Bind(self.SelectBeastCellCallBack, self))
	end

	if not self.compose_bag_type_list then
		self.compose_bag_type_list = {}
			
		for i = 0, 5 do
			local incubate_obj = self.node_list.compose_bag_type_list:FindObj(string.format("compose_bag_type_%d", i))
			if incubate_obj then
				local cell = BeastsBagTypeItemRender.New(incubate_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.SelectBeastsBagTypeCallBack, self))
				self.compose_bag_type_list[i] = cell
			end
		end
	end
end

-- 设置背包数据
function ControlBeastsWGView:ReleaseAllBeastGrid()
	if self.beasts_list_grid then
		self.beasts_list_grid:DeleteMe()
		self.beasts_list_grid = nil
	end

	if self.compose_bag_type_list and #self.compose_bag_type_list > 0 then
		for i = 0, 5 do
			if self.compose_bag_type_list[i] then
				self.compose_bag_type_list[i]:DeleteMe()
				self.compose_bag_type_list[i] = nil
			end
		end

		self.compose_bag_type_list = nil
	end
end

-- 设置背包数据
-- 是否排序背包了， 排序了背包只做更新不做跳转选中
function ControlBeastsWGView:FlushAllBeastGridData(bag_id)
	self.culture_list_data = ControlBeastsWGData.Instance:GetCultureBeastsList(self.show_index == TabIndex.beasts_compose
				, true, self.show_index == TabIndex.beasts_compose, self.show_index == TabIndex.beasts_culture)

	self:FlushBeastsBagTypeCallBack(self.beast_bag_type_index or 0, bag_id)
end

-- 选中某个类型回调
function ControlBeastsWGView:SelectBeastsBagTypeClick(is_need_jump, bag_id)
	if not self.culture_list_data then
		return
	end

	local aim_table = {}

	for i, beasts_data in ipairs(self.culture_list_data) do
		if beasts_data then -- and beasts_data.server_data then
			if self.beast_bag_type_index == 0 then
				table.insert(aim_table, beasts_data)
			else
				local server_data = beasts_data.server_data
				local beast_element = ControlBeastsWGData.Instance:GetCurBeastElementByBeastId(server_data.beast_id)
				if beast_element == self.beast_bag_type_index then
					table.insert(aim_table, beasts_data)
				end
			end
		end
	end

	if #aim_table <= 0 and self.beast_bag_type_index == 0 then
		local pre_data = ControlBeastsWGData.Instance:GetPreviewBeast()
		if pre_data then
			table.insert(aim_table, pre_data)
		end
	end

	if self.beasts_list_grid then
		self.beasts_list_grid:SetDataList(aim_table)
	end

	if #aim_table > 0 and is_need_jump then
		self.select_beast_index = nil
		self.select_beast_data = nil
		self:BeastGridJumpToIndex(aim_table, 1)
	else
		-- 新增一种特殊选中，当最后一个被吃掉的话，当前下标就成了最后一个导致界面没刷新
		if self.select_beast_index ~= nil and self.select_beast_data ~= nil and aim_table ~= nil then
			local sp_jump = false
			local jump_index = 1

			if bag_id == nil then
				local holy_beast_cache = {} -- 缓存，用于节省性能
				local target_bag_id			-- 圣兽跳转目标
				for i, beast_data in ipairs(aim_table) do
					if beast_data.bag_id == target_bag_id then
						jump_index = i
						sp_jump = true
						break
					end
					if beast_data.is_holy_beast then
						holy_beast_cache[beast_data.bag_id] = {index = i, beast_data = beast_data}
					end
					if self.now_star_up_beast_id ~= nil then
						if beast_data.server_data.beast_id == self.now_star_up_beast_id then
							jump_index = i
							self.now_star_up_beast_id = nil
							sp_jump = true

							if jump_index == self.select_beast_index then
								self:FlushSelectBeastCellCallBack()
							end
							break
						end
					else
						if beast_data == self.select_beast_data and i ~= self.select_beast_index then
							-- 因为被缔结不能选中，需要特殊处理，直接选中缔结的圣兽
							if not beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index ~= -1 then
								local target_data = holy_beast_cache[beast_data.server_data.holy_spirit_link_index + 1]
								if target_data then
									jump_index = target_data.index
									sp_jump = true

									if jump_index == self.select_beast_index and self.select_beast_data ~= target_data.beast_data then
										self.select_beast_data = target_data.beast_data
										self:FlushSelectBeastCellCallBack()
									end
									break
								else -- 记录bag_id
									target_bag_id = beast_data.server_data.holy_spirit_link_index + 1
								end
							else
								jump_index = i
								sp_jump = true
								break
							end
						end
					end
				end
			else
				for i, beast_data in ipairs(aim_table) do
					if bag_id ~= nil and beast_data.bag_id == bag_id then
						if not beast_data.is_holy_beast and beast_data.server_data.holy_spirit_link_index ~= -1 then
							local new_bag_id = beast_data.server_data.holy_spirit_link_index + 1
							for j, beast_data in ipairs(aim_table) do
								if beast_data.bag_id == new_bag_id then
									local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_data.server_data.beast_id)
									local star_up_beast_id = beast_cfg and beast_cfg.starup_beast_id
									jump_index = j
									self.now_star_up_beast_id = star_up_beast_id
									sp_jump = true
								end
							end
						else
							local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_data.server_data.beast_id)
							local star_up_beast_id = beast_cfg and beast_cfg.starup_beast_id
							jump_index = i
							self.now_star_up_beast_id = star_up_beast_id
							sp_jump = true
						end

						if jump_index == self.select_beast_index then
							self:FlushSelectBeastCellCallBack()
						end
						break
					end
				end
			end

			if sp_jump then
				self:BeastGridJumpToIndex(aim_table, jump_index)
			end
		end
	end
end

-- 选中某个格子
function ControlBeastsWGView:BeastGridJumpToIndex(aim_table, beast_index)
	if aim_table and #aim_table > 0 and beast_index <= #aim_table then
		self.beasts_list_grid:JumpToIndexAndSelect(beast_index, 15, BEAST_DEFINE.BEAST_BORN_COUNT_MAX)
		-- self.beasts_list_grid:JumpToIndexAndSelect(28, 18, BEAST_DEFINE.BEAST_BORN_COUNT_MAX)
	else
		local data = ControlBeastsWGData.Instance:GetPreviewBeast()
		if data then
			self.select_beast_index = nil
			self.select_beast_data = data
			self:FlushSelectBeastCellCallBack()
		end
	end
end

-- 刷新界面
function ControlBeastsWGView:FlushSelectBeastCellCallBack()
	-- 刷新界面
	if self.show_index == TabIndex.beasts_culture then
		self:OnSelectCultureBeastCB()
	elseif self.show_index == TabIndex.beasts_compose then
		self:SelectComposeBeastCellCallBack()
	end
end

-- 已孵化背包类型点击
function ControlBeastsWGView:SelectBeastsBagTypeCallBack(beast_bag_type_cell)
	if self.beast_bag_type_index == beast_bag_type_cell.index then
		return
	end

	self.beast_bag_type_index = beast_bag_type_cell.index
	self:SelectBeastsBagTypeClick(true)

	for index = 0, 5 do
		if self.compose_bag_type_list[index] then
			self.compose_bag_type_list[index]:FlushSelectHl(index == self.beast_bag_type_index)
		end
	end
end

-- 已孵化背包类型刷新
function ControlBeastsWGView:FlushBeastsBagTypeCallBack(beast_bag_type_index, bag_id)
	for index = 0, 5 do
		if self.compose_bag_type_list[index] then
			self.compose_bag_type_list[index]:FlushSelectHl(index == beast_bag_type_index)
		end
	end

	self.beast_bag_type_index = beast_bag_type_index
	local preview = ControlBeastsWGData.Instance:GetPreviewBeast()
	local is_need_jump = self.select_beast_index == nil or self.select_beast_data == preview
	self:SelectBeastsBagTypeClick(is_need_jump, bag_id)
end

-- 已孵化背包点击
function ControlBeastsWGView:SelectBeastCellCallBack(beast_cell)
    if not beast_cell.data then return end

	if self.select_beast_index == beast_cell.index then
		if beast_cell.data.is_preview then
			if ControlBeastsWGData.Instance:GetBeastsEggListIsHaveBeast() then
				self:ChangeToIndex(TabIndex.beasts_stable)
			else
				self:ChangeToIndex(TabIndex.beasts_contract)
			end
		end
		return
	end

    self.select_beast_index = beast_cell.index
    self.select_beast_data = beast_cell.data
	self:FlushSelectBeastCellCallBack()
end

-- 选择缔结幻兽
function ControlBeastsWGView:OnClickBeastsContractBtn()
    if not self:CheckHaveDataAndServerData(self.select_beast_data) then
        return
    end
    local holy_beast_data = ControlBeastsWGData.Instance:GetHolyBeastData(self.select_beast_data.beast_type)
    ControlBeastsWGCtrl.Instance:OpenHolyBeastContractView(holy_beast_data)
end
----------------------------------------------------------------------
BeastsMessageItem = BeastsMessageItem or BaseClass(BaseRender)
function BeastsMessageItem:LoadCallBack()
	if not self.beast_item then
        self.beast_item = ItemCell.New(self.node_list.item_pos)
		self.beast_item:SetIsShowTips(false)
		self.beast_item:SetClickCallBack(BindTool.Bind(self.OnClick, self))
    end
end

--删除写在ReleaseCallBack里的东西
function BeastsMessageItem:ReleaseCallBack()
    if self.beast_item then
        self.beast_item:DeleteMe()
        self.beast_item = nil
    end
end

function BeastsMessageItem:OnFlush()
    if not self.data then
        return
    end

	self.beast_item:SetData(self.data)
	self.beast_item:FlushBeastItemRedVisible(self.data.need_show_red ~= nil and self.data.need_show_red or false)

    local server_data = self.data.server_data
    if not self.data.is_egg and server_data and (not self.data.is_preview) then
        self.beast_item:SetBeastBattleTypeIcon(server_data.stand_by_slot)
		local flair_score, score_index = ControlBeastsWGData.Instance:GetFlairScoreByServerData(server_data)
		-- self.beast_item:SetBeastFlairScoreIcon(score_index)
		self.beast_item:SetBeastLevel(server_data.beast_level)
    end

	local show_link = self.data.is_have_beast and not self.data.is_holy_beast and server_data.holy_spirit_link_index ~= -1
	self.node_list["img_link"]:SetActive(show_link)
end

function BeastsMessageItem:OnClick()
	--当有的礼包需要消耗仙玉时，显示的红点 一天只显示一次
    if (not self.data) or (not self.data.server_data) then
        return
    end
    local server_data = self.data.server_data

    if server_data.beast_id <= 0 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip9)
        return
    end

	BaseRender.OnClick(self)
end

-- 设置是否选中
function BeastsMessageItem:SetSelect(is_select, item_call_back)
	local bundle, asset = ResPath.GetCommon("a3_ty_xz1")
	self.beast_item:SetSelectSpEffectImageRes(bundle, asset)
	self.beast_item:SetSelectEffectSp(is_select)	
end
----------------------------------------------新修改增加一个总孵化的背包---------------------------------------------------------------
-----------VerticalItemRender-------------------------------------------
BeastsVerticalItemRender = BeastsVerticalItemRender or BaseClass(VerItemRender)

function BeastsVerticalItemRender:SetMaskBtnActive(active)
    self:SetOtherBtn(active, BindTool.Bind(self.OnClickMaskBtn,self))
end

function BeastsVerticalItemRender:OnClickMaskBtn()
    SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip8)
end

--------------------------------背包幻兽类型item-----------------------
BeastsBagTypeItemRender = BeastsBagTypeItemRender or BaseClass(BaseRender)

-- 刷新选中状态
function BeastsBagTypeItemRender:FlushSelectHl(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

---------------------------------背包列表------------------------------
BeastsBagGird = BeastsBagGird or BaseClass(AsyncBaseGrid)
function BeastsBagGird:IsSelectMultiNumLimit(cell_index)
	local cell_data = self.cell_data_list[cell_index]
	if not cell_data then
		return true
	end
	local link_index = cell_data.server_data and cell_data.server_data.holy_spirit_link_index or -1
	if not cell_data.is_holy_beast and link_index ~= -1 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ContralBeasts.BeastLinked)
		return true
	end
	return false
end