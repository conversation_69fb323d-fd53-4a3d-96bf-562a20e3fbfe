JingMaiAttrView = JingMaiAttrView or BaseClass(SafeBaseView)
function JingMaiAttrView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/role_ui/jingjie_ui_prefab", "layout_jingjie_attr_view")
end

function JingMaiAttrView:LoadCallBack()
	if not self.jingjie_attr_list then
		self.jingjie_attr_list = AsyncListView.New(JingMaiAttrListRender, self.node_list.jingjie_attr_list)
	end
end

function JingMaiAttrView:ReleaseCallBack()
	if self.jingjie_attr_list then
		self.jingjie_attr_list:DeleteMe()
		self.jingjie_attr_list = nil
	end
end

function JingMaiAttrView:OnFlush()
	local data_list = JingJieWGData.Instance:GetAllMeridiansAttr()
	self.node_list.no_attr_tip:CustomSetActive((not data_list) or (#data_list <= 0))
	self.node_list.jingjie_attr_list:CustomSetActive(data_list and (#data_list > 0))

	if data_list and (#data_list > 0) then
		self.jingjie_attr_list:SetDataList(data_list)
	end
end

------------------------------------------------------------------------------------
JingMaiAttrListRender = JingMaiAttrListRender or BaseClass(BaseRender)
function JingMaiAttrListRender:OnFlush()
	if not self.data then
		return
	end

	local is_per = EquipmentWGData.Instance:GetAttrIsPer(self.data.attr_str)
	local per_desc = is_per and "%" or ""
	local value_str = is_per and self.data.attr_value / 100 or self.data.attr_value
	self.node_list.name.text.text = EquipmentWGData.Instance:GetAttrName(self.data.attr_str, true, false)
	self.node_list.value.text.text = value_str .. per_desc
end
