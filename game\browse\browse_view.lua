BrowseView = BrowseView or BaseClass(SafeBaseView)
function BrowseView:__init()
	-- self:SetMaskBg(false,true)
	self.view_style = ViewStyle.Full
    self.view_name = "BrowseView"
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_background_common_panel")
	self:AddViewResource(0, "uis/view/browse_ui_prefab", "layout_left")
	self:AddViewResource(0, "uis/view/browse_ui_prefab", "layout_attr")
	self:AddViewResource({20,30,40,50,60,70,80,90}, "uis/view/browse_ui_prefab", "layout_borwse_shengzhuang")
	-- self:AddViewResource(0, "uis/view/browse_ui_prefab", "layout_common_button_list")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.OTHER_JUESE_INFO})
	self.default_index = 10
	self.show_slot_index = -1
	self.view_cache_time = 0
	self.background_loader = nil
	self.is_safe_area_adapter = true
end

function BrowseView:ReleaseCallBack()
	self:ReleaseCallBackAttr()

	if self.tabbar_scroll then
		self.tabbar_scroll = nil
	end

	if self.type_listview then
		self.type_listview:DeleteMe()
		self.type_listview = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.shengzhuang_equip_list then
		for k, v in pairs(self.shengzhuang_equip_list) do
			v:DeleteMe()
		end
		self.shengzhuang_equip_list = nil
	end

	if self.background_loader then
        self.background_loader:Destroy()
		self.background_loader = nil
	end
end

function BrowseView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		local role_info = BrowseWGData.Instance:GetRoleInfo()
		self.tabbar:SetVerTabbarIconStr("browse_tab")
  		self.tabbar:Init(Language.Browse.NameList)
  		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	end
	self:InitPanel()
	self:InitListener()
	self:InitAttrView()
	self:OnFlushAttr()
	self:FlushBackground()

	-- local bundle, asset = ResPath.GetRawImagesPNG("a2_ty_bg1")
	-- self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- end)
end

function BrowseView:InitPanel()
	self.node_list.title_view_name.text.text = Language.ViewName[GuideModuleName.Browse]
	local cap_com_open = FunOpen.Instance:GetFunIsOpened(FunName.CapabilityContrastView)
	
	-- 是否跨服
	local role_info = BrowseWGData.Instance:GetRoleInfo()
	local main_role_server_id = RoleWGData.Instance:GetMergeServerId()
	local main_role_plat_type = RoleWGData.Instance:GetPlatType()
	local server_id = role_info.server_id or main_role_server_id
	local plat_type = role_info.plat_type or main_role_plat_type
	local is_same_server = server_id == main_role_server_id and plat_type == main_role_plat_type
	self.node_list.btn_cap_com:SetActive(is_same_server and cap_com_open)
end

function BrowseView:InitListener()
	XUI.AddClickEventListener(self.node_list.add_friend, BindTool.Bind(self.OnClickAddFriend, self))
	XUI.AddClickEventListener(self.node_list.btn_cap_com, BindTool.Bind(self.OnClickCapCom, self))
end

function BrowseView:ShowIndexCallBack(index)
	if self.node_list["common_root"] then
		self.node_list["common_root"]:SetActive(index == 10)
	end 

	--if index == 10 then
	--	self.node_list["btn_group"].transform:SetLocalPosition(-371, -7, 0)
	--else
	--	self.node_list["btn_group"].transform:SetLocalPosition(-520, -7, 0)
	--end

	-- self.show_slot_index = math.floor(index /10) - 2
	-- if self.show_slot_index >= 0 then
	-- 	local role_info = BrowseWGData.Instance:GetRoleInfo()
	-- 	local shengzhuang_num = role_info.xianjie_act_max_slot or 0
	-- 	shengzhuang_num = shengzhuang_num
	-- 	self.show_slot_index = shengzhuang_num -1 >= self.show_slot_index and self.show_slot_index or shengzhuang_num -1
	-- 	self.shengzhuang_data = BrowseWGData.Instance:GetPlayerXianJieEquipByUUID(role_info.plat_type,role_info.role_id,self.show_slot_index)
	-- 	if IsEmptyTable(self.shengzhuang_data) then

	-- 	else
	-- 		self:FlushShengZhuangPanel()
	-- 	end
	-- end
	self:OnFlushAttr()
	self:Flush()
end

function BrowseView:OnFlush()
	--self:FLushShengZhuangInfo()
	--self:FlushShengZhuangPanel()
end

function BrowseView:FlushShengZhuangPanel()
	if not self.node_list["shengzhaung_contant"] then return end
	if self.show_slot_index >= 0 then
		local role_info = BrowseWGData.Instance:GetRoleInfo()
		-- local uuid = TwoUIntToLL(role_info.plat_type, role_info.role_id)
		self.shengzhuang_data = BrowseWGData.Instance:GetPlayerXianJieEquipByUUID(role_info.plat_type,role_info.role_id,self.show_slot_index)
		if IsEmptyTable(self.shengzhuang_data) then 
			return
		end
		if not self.shengzhuang_equip_list then
			self.shengzhuang_equip_list = {}
			for i = 0, SHENG_ZHUANG_BROWSE.MAX_XIANJIE_EQUIP_SHENZHUANG_PART do
				self.shengzhuang_equip_list[i] = BrowserHolyEquipEquipCell.New(self.node_list["equip_"..i])
				self.shengzhuang_equip_list[i]:SetIndex(i)
				self.shengzhuang_equip_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickShengZhuangCell,self))
			end
		end
		for i = 0, SHENG_ZHUANG_BROWSE.MAX_XIANJIE_EQUIP_SHENZHUANG_PART do
			self.shengzhuang_equip_list[i]:SetData(self.shengzhuang_data.equip_list[i])
		end
	end
end

-- function BrowseView:OnClickShengZhuangCell(cell)
-- 	print_error("cell_click",cell.data)
-- end

-- 点击装备格子回调
function BrowseView:OnClickShengZhuangCell(cell)
    if cell == nil or cell.data == nil then
        return
    end

    local fle_data = FairyLandEquipmentWGData.Instance
    local data = cell.data
    if data.item_id == nil or data.item_id <= 0 then
        if fle_data:GetIsSpecialType(cell.index) then
            local show_cfg = fle_data:GetGBEquipVirtualCfg(self.show_slot_index, cell.index)
            if show_cfg then
                -- local get_way_list = TipWGData.Instance:GetGetWayList(show_cfg.item_id)
                -- if not IsEmptyTable(get_way_list) then
        		-- 	FightSoulWGCtrl.Instance:OpenFightSoulGetWayTips(get_way_list)
        		-- end
                local show_data = {item_id = show_cfg.item_id}
    			TipWGCtrl.Instance:OpenItem(show_data)
            end
        end
        return
    end

    local btn_callback_event = {}
    -- btn_callback_event[1] = {btn_text = Language.Tip.ButtonLabel[7], callback = function()
			 --                    FairyLandEquipmentWGCtrl.Instance:SendOperateReq(GOD_BODY_OP_TYPE.TAKE_OFF, data.slot, data.part)
		  --                   end}
    TipWGCtrl.Instance:OpenItem(data, ItemTip.FROME_BROWSE_ROLE, nil, nil, btn_callback_event)
end


function BrowseView:FLushShengZhuangInfo()
	-- local role_info = BrowseWGData.Instance:GetRoleInfo()
	-- local shengzhuang_num = role_info.xianjie_act_max_slot or 0
	-- shengzhuang_num = shengzhuang_num + 1
	-- print_error("shengzhuang_num",shengzhuang_num + 1)
	-- for i = 1 , 9 do
	-- 	if shengzhuang_num >= i then
	-- 		self.tabbar:SetVerToggleVisble(i*10, true)
	-- 	else
	-- 		self.tabbar:SetVerToggleVisble(i*10, false)
	-- 	end
	-- end
end

function BrowseView:SelectTabCallback(index)
	self.record_cur_index = index
	self:ChangeToIndex(index)
end

function BrowseView:OnClickAddFriend()
    local role_info = BrowseWGData.Instance:GetRoleInfo()
    if role_info.role_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then

        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
        return
    end
	SocietyWGCtrl.Instance:IAddFriend(role_info.role_id)
end

function BrowseView:OnClickCapCom()
	local role_info = BrowseWGData.Instance:GetRoleInfo()
	if role_info.role_id == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotOnline)
		return
	end

	CapabilityContrastWGCtrl:ReqCapabilityCmpCoress(role_info.plat_type, role_info.role_id)
end

function BrowseView:FlushBackground()
	local role_info = BrowseWGData.Instance:GetRoleInfo()
	if role_info.back_id then
		local background_id = role_info.back_id
		local asset, bundle = nil, nil
		local back_data = BackgroundWGData.Instance:GetBigDataByID(background_id)

		self.node_list["background_root"]:SetActive(background_id ~= 0 and back_data ~= nil)

		if background_id ~= 0 and back_data then
			asset, bundle = ResPath.BackgroundShow(back_data.item_id)

			if not self.background_loader then
				local background_loader = AllocAsyncLoader(self, "base_tip_back_cell")
				background_loader:SetIsUseObjPool(true)
				background_loader:SetParent(self.node_list["background_root"].transform)
				self.background_loader = background_loader
			end
			self.background_loader:Load(asset, bundle)
		end
	end
end

-- 圣装装备格子
BrowserHolyEquipEquipCell = BrowserHolyEquipEquipCell or BaseClass(BaseRender)
function BrowserHolyEquipEquipCell:LoadCallBack()
    if nil == self.star_list then
        self.star_list = {}
        for i = 1, 5 do
            self.star_list[i] = self.node_list.star_list:FindObj("star" .. i)
        end
    end

    XUI.AddClickEventListener(self.node_list.btn_click, BindTool.Bind(self.OnClick, self))
end

function BrowserHolyEquipEquipCell:__delete()
    self.star_list = nil
    self.equip_res_bundle = nil
    self.equip_res_asset = nil
end


local part_model_res_list = {[0] = 17, [1] = 7, [2] = 1, [3] = 0,
                        [4] = 2, [5] = 13, [6] = 6, [7] = 4,
                        [8] = 9, [9] = 14, [10] = 3, [11] = 10,}
function BrowserHolyEquipEquipCell:OnFlush()
    if self.data == nil or self.data.item_id == nil or self.data.item_id <= 0 then
        self.equip_res_bundle = nil
        self.equip_res_asset = nil
       -- self.node_list.img_add:SetActive(true)
       	self.node_list.show_icon.raw_image.enabled = false
        self.node_list.item_icon.image.enabled = true
        -- self.node_list.remind.image.enabled = false
        for k,v in ipairs(self.star_list) do
            v.image.enabled = false
        end

        local bundle, asset = ResPath.GetFairyLandBrowseEquipImages("he_color_0")
        self.node_list.color_bg.image:LoadSprite(bundle, asset)
        self.node_list["item_eff_pos"]:SetActive(false)
        return
    end

    --self.node_list.img_add:SetActive(false)
    if not self.node_list or not self.node_list.item_icon then
    	return
    end
    self.node_list.item_icon.image.enabled = false
    -- self.node_list.remind.image.enabled = false

    -- 星级
    local star = self.data.star
    for k,v in ipairs(self.star_list) do
        v.image.enabled = k <= star
    end

    -- 品质背景
    local color = self.data.color
    local part = self.data.part
    local is_special_equip = self.data.special_equip
    local color_bg_res
    if is_special_equip then
        color_bg_res = "he_s_color_" .. color
    else
        local str = ""
        if color >= GameEnum.ITEM_COLOR_COLOR_FUL then
            if part == XIANJIE_EQUIP_TYPE.TOUKUI or part == XIANJIE_EQUIP_TYPE.XIEZI then
                str = "1"
            elseif part == XIANJIE_EQUIP_TYPE.YIFU or part == XIANJIE_EQUIP_TYPE.KUZI then
                str = "3"
            else
                str = "2"
            end
        end
        color_bg_res = "he_color_" .. color .. str
    end

    local bundle, asset = ResPath.GetFairyLandBrowseEquipImages(color_bg_res)
    self.node_list.color_bg.image:LoadSprite(bundle, asset)

    self.node_list.show_icon.raw_image.enabled = false
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg then
        local i_bundle, i_asset = ResPath.GetF2RawImagesPNG(item_cfg.icon_id)
        self.node_list.show_icon.raw_image:LoadSprite(i_bundle, i_asset, function()
            self.node_list.show_icon.raw_image:SetNativeSize()
            self.node_list.show_icon.raw_image.enabled = true
        end)
    end

    -- 扫光特效
    local eff_bundle, eff_asset = FairyLandEquipmentWGData.Instance:GetEquipGridEffectByPartColor(part, color)
    if eff_bundle and eff_asset then
        self.node_list["item_eff_pos"]:SetActive(true)
        self.node_list["item_eff_pos"]:ChangeAsset(eff_bundle, eff_asset)
    end

end
