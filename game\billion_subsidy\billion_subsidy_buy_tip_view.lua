-----------------------------------
-- 百亿补贴 购买弹窗
-----------------------------------
BillionSubsidyBuyTipView = BillionSubsidyBuyTipView or BaseClass(SafeBaseView)

local MAX_VISIABLE_ROW = 2 --最多可视区域行数

function BillionSubsidyBuyTipView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Window
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/billion_subsidy_ui_prefab", "layout_billion_subsidy_buy_tip")
end

function BillionSubsidyBuyTipView:LoadCallBack()
    if not self.discount_coupon_select_grid then
        self.discount_coupon_select_grid = BillionSubsidyBuyTipDSTicketGird.New()
        self.discount_coupon_select_grid:SetStartZeroIndex(false)
        self.discount_coupon_select_grid:SetSelectCallBack(BindTool.Bind(self.OnSelectDiscountTicket, self))
		self.discount_coupon_select_grid:CreateCells({col = 4, itemRender = BillionSubsidyBuyTipDSTicketSelectCell,
			list_view = self.node_list.discount_coupon_select_grid, assetBundle = "uis/view/billion_subsidy_ui_prefab", assetName = "discount_coupon_select_cell", change_cells_num = 1})
    end

    if not self.grid_scroller_fun then
		self.grid_scroller_fun = BindTool.Bind(self.ShopScrollerEndScrolled, self)
		self.node_list.discount_coupon_select_grid.scroller.scrollerEndScrolled = self.grid_scroller_fun
	end

    XUI.AddClickEventListener(self.node_list.discount_ticket_buy_btn, BindTool.Bind(self.OnClickDiscountTicketBuyBtn, self))
    XUI.AddClickEventListener(self.node_list.original_price_buy_btn, BindTool.Bind(self.OnClickOriginalPriceBuyBtn, self))

    self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.BillionSubsidyBuyTip, self.get_guide_ui_event)
end

function BillionSubsidyBuyTipView:ReleaseCallBack()
    if self.discount_coupon_select_grid then
        self.discount_coupon_select_grid:DeleteMe()
        self.discount_coupon_select_grid = nil
    end

    self.grid_scroller_fun = nil

    FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.BillionSubsidy, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function BillionSubsidyBuyTipView:ShowIndexCallBack()
    self.select_discount_ticket_data = nil
    self.node_list.use_ticket_price.text.text = ""
end

function BillionSubsidyBuyTipView:OnFlush()
    local is_show_list = false
    if self.cur_shop_type and self.cur_item_seq then
        local dc_all_data_list = BillionSubsidyWGData.Instance:GetAllCanUseDiscountTicketBySeq(self.cur_shop_type, self.cur_item_seq, true)
        local has_empty = not IsEmptyTable(dc_all_data_list)
        is_show_list = has_empty
        if has_empty then
            self.discount_coupon_select_grid:SetDataList(dc_all_data_list)
            self.discount_coupon_select_grid:JumpToIndexAndSelect(1, 8)
        end
    end

    self.node_list.blank_tips:CustomSetActive(not is_show_list)
    self.node_list.discount_coupon_select_grid:CustomSetActive(is_show_list)
end

-- 点击相关
function BillionSubsidyBuyTipView:OnSelectDiscountTicket(cell)
    local cell_data = cell:GetData()
    if IsEmptyTable(cell_data) then
        return
    end

    local shop_cfg = BillionSubsidyWGData.Instance:GetShopCfgByTypeAndSeq(self.cur_shop_type, self.cur_item_seq)
    if IsEmptyTable(shop_cfg) then
        return
    end

    self.select_discount_ticket_data = cell_data
    local price = shop_cfg.price
    if cell_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
        price = 0
    else
        if self.cur_shop_type == BillionSubsidyWGData.ShopType.XSSD then
            local start_timestamp = BillionSubsidyWGData.Instance:GetXDZKStartDiscountTimestamp()
            local end_time = start_timestamp + shop_cfg.limit_time_discount_duration
            if end_time > TimeWGCtrl.Instance:GetServerTime() then
                price = shop_cfg.limit_time_discount_price
            end
        elseif self.cur_shop_type == BillionSubsidyWGData.ShopType.DEZG then
            local reduce_value = BillionSubsidyWGData.Instance:GetDEZGShopCurReduceValue()
            price = shop_cfg.member_price - reduce_value
        end

        local reduce_quota = cell_data.reduce_quota or 0
        price = price - reduce_quota
    end

    price = math.max(price, 0)
    local pay_str = RoleWGData.GetPayMoneyStr(price, shop_cfg.rmb_type, shop_cfg.rmb_seq)
    self.node_list.use_ticket_price.text.text = string.format(Language.BillionSubsidy.CurDiscountPrice, pay_str)

    if cell_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON then
        self.node_list.select_discount_ticket_tip.text.text = string.format(Language.BillionSubsidy.SelectDiscountTicketTip[1], cell_data.quota_limit, cell_data.reduce_quota)
    elseif cell_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON then
        self.node_list.select_discount_ticket_tip.text.text = string.format(Language.BillionSubsidy.SelectDiscountTicketTip[2], cell_data.reduce_quota)
    elseif cell_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
        self.node_list.select_discount_ticket_tip.text.text = Language.BillionSubsidy.SelectDiscountTicketTip[3]
    end
end

function BillionSubsidyBuyTipView:OnClickDiscountTicketBuyBtn()
    if not self.cur_shop_type or not self.cur_item_seq or not self.select_discount_ticket_data then
        return
    end

    local shop_cfg = BillionSubsidyWGData.Instance:GetShopCfgByTypeAndSeq(self.cur_shop_type, self.cur_item_seq)
    if IsEmptyTable(shop_cfg) then
        return
    end

    local simulate_price = shop_cfg.price                   -- 模拟价格，最终价格大于0走通用recharge接口，否则走百亿补贴本系统内的购买接口
    local real_price = shop_cfg.price
    local reduce_value = BillionSubsidyWGData.Instance:GetDEZGShopCurReduceValue()
    if self.select_discount_ticket_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
        simulate_price = 0
    else
        if self.cur_shop_type == BillionSubsidyWGData.ShopType.BYBT or self.cur_shop_type == BillionSubsidyWGData.ShopType.XSSD then
            local start_timestamp = BillionSubsidyWGData.Instance:GetXDZKStartDiscountTimestamp()
            local end_time = start_timestamp + shop_cfg.limit_time_discount_duration
            if end_time > TimeWGCtrl.Instance:GetServerTime() then
                simulate_price = shop_cfg.limit_time_discount_price
                real_price = shop_cfg.limit_time_discount_price
            end
        elseif self.cur_shop_type == BillionSubsidyWGData.ShopType.DEZG then
            simulate_price = shop_cfg.member_price - reduce_value
            real_price = shop_cfg.member_price
        end

        local reduce_quota = self.select_discount_ticket_data.reduce_quota or 0
        simulate_price = simulate_price - reduce_quota
    end

    if simulate_price > 0 then
        RechargeWGCtrl.Instance:Recharge(real_price, shop_cfg.rmb_type, shop_cfg.rmb_seq, self.select_discount_ticket_data.data_seq, self.cur_custom_param)
    else
        local ticket_type = self.select_discount_ticket_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON and 1 or 2
        BillionSubsidyWGCtrl.Instance:SendTenBillionSubsidyClientOperate(TEN_BILLION_SUBSIDY_OPERATE_TYPE.USE_FREE_OR_DISCOUNT_TICKET_RMB_BUY, shop_cfg.rmb_type, shop_cfg.rmb_seq, ticket_type, self.select_discount_ticket_data.data_seq, self.cur_custom_param)
    end

    self:Close()
end

function BillionSubsidyBuyTipView:OnClickOriginalPriceBuyBtn()
    if not self.cur_shop_type or not self.cur_item_seq then
        return
    end

    local shop_cfg = BillionSubsidyWGData.Instance:GetShopCfgByTypeAndSeq(self.cur_shop_type, self.cur_item_seq)
    if IsEmptyTable(shop_cfg) then
        return
    end

    local price = shop_cfg.price
    if self.cur_shop_type == BillionSubsidyWGData.ShopType.BYBT or self.cur_shop_type == BillionSubsidyWGData.ShopType.XSSD then
        local start_timestamp = BillionSubsidyWGData.Instance:GetXDZKStartDiscountTimestamp()
        local end_time = start_timestamp + shop_cfg.limit_time_discount_duration
        if end_time > TimeWGCtrl.Instance:GetServerTime() then
            price = shop_cfg.limit_time_discount_price
        end
    elseif self.cur_shop_type == BillionSubsidyWGData.ShopType.DEZG then
        price = shop_cfg.member_price
    end

    RechargeWGCtrl.Instance:Recharge(price, shop_cfg.rmb_type, shop_cfg.rmb_seq, nil, self.cur_custom_param)
    self:Close()
end

-- 其他
function BillionSubsidyBuyTipView:SetDataAndOpen(shop_type, item_seq, custom_param)
    if not shop_type or not item_seq then
        return
    end

    local shop_cfg = BillionSubsidyWGData.Instance:GetShopCfgByTypeAndSeq(shop_type, item_seq)
    local buy_count = BillionSubsidyWGData.Instance:GetShopBuyCountByTypeAndSeq(shop_type, item_seq)
    local limit_count = shop_cfg and shop_cfg.buy_limit or 0
    if (limit_count > 0 and buy_count >= limit_count) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.BYBTShopBuyDone)
        return
    end

    self.cur_shop_type = shop_type
    self.cur_item_seq = item_seq
    self.cur_custom_param = custom_param
    local dc_all_data_list = BillionSubsidyWGData.Instance:GetAllCanUseDiscountTicketBySeq(shop_type, item_seq, false)
    if IsEmptyTable(dc_all_data_list) then
        self:OnClickOriginalPriceBuyBtn()
        return
    end

    self:Open()
end

function BillionSubsidyBuyTipView:ShopScrollerEndScrolled()
    local val = self.node_list.discount_coupon_select_grid.scroll_rect.verticalNormalizedPosition
	local cell_row = self.discount_coupon_select_grid:GetListViewNumbers()
    local is_show_arrow = val ~= 0 and cell_row > MAX_VISIABLE_ROW and val > 0.2
    if self.node_list.arrow_img:GetActive() ~= is_show_arrow then
        self.node_list.arrow_img:SetActive(is_show_arrow)
    end
end

function BillionSubsidyBuyTipView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.BillionSubsidyBuyTipList then
		if nil == self.discount_coupon_select_grid then
			return
		end

		local cell_index = ui_param
		if cell_index >= 0 then
			local item = self.discount_coupon_select_grid:GetCell(cell_index)
			if item then
				return item:GetView()
			end
		end
	end

	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

-------------- BillionSubsidyBuyTipDSTicketGird 购买界面的券列表
BillionSubsidyBuyTipDSTicketGird = BillionSubsidyBuyTipDSTicketGird or BaseClass(AsyncBaseGrid)
function BillionSubsidyBuyTipDSTicketGird:IsSelectMultiNumLimit(cell_index)
    local cell_data = self.cell_data_list[cell_index]
    if not cell_data then
        return true
    end

    if (cell_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON and not BillionSubsidyWGData.Instance:GetHasUseFreeTicketChance())
    or (cell_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON and not BillionSubsidyWGData.Instance:GetHasUseNoLimitDiscountTicketChance())
    or (cell_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON and not BillionSubsidyWGData.Instance:GetHasUseLimitDiscountTicketChance()) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.BillionSubsidy.NoUseTicketChance)
        return true
    end

    return false
end


-------------- BillionSubsidyBuyTipDSTicketSelectCell 购买界面的券item
BillionSubsidyBuyTipDSTicketSelectCell = BillionSubsidyBuyTipDSTicketSelectCell or BaseClass(BaseRender)
function BillionSubsidyBuyTipDSTicketSelectCell:OnFlush()
    XUI.SetGraphicGrey(self.view, false)
	if IsEmptyTable(self.data) then
		return
	end

    local need_set_grey = false
    if self.data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FREE_DISCOUNT_COUPON then
        self.node_list.free_discount_coupon_content:SetActive(true)
        self.node_list.other_discount_coupon_content:SetActive(false)
        self.node_list.limit_use_text.text.text = Language.BillionSubsidy.FreeDCQuotaLimit2
        need_set_grey = not BillionSubsidyWGData.Instance:GetHasUseFreeTicketChance()
    else
        self.node_list.free_discount_coupon_content:SetActive(false)
        self.node_list.other_discount_coupon_content:SetActive(true)
        self.node_list.name.text.text = Language.BillionSubsidy.DCNameList[self.data.type]
        self.node_list.save_money_text.text.text = string.format(Language.BillionSubsidy.DCReduceQuota, self.data.reduce_quota)
        if self.data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON then
            self.node_list.limit_use_text.text.text = Language.BillionSubsidy.DCNoQuotaLimit
            need_set_grey = not BillionSubsidyWGData.Instance:GetHasUseNoLimitDiscountTicketChance()
        else
            self.node_list.limit_use_text.text.text = string.format(Language.BillionSubsidy.DCQuotaLimit, self.data.quota_limit)
            need_set_grey = not BillionSubsidyWGData.Instance:GetHasUseLimitDiscountTicketChance()
        end

    end

    XUI.SetGraphicGrey(self.view, need_set_grey)
end

function BillionSubsidyBuyTipDSTicketSelectCell:OnSelectChange(is_select)
    self.node_list.hl_img:CustomSetActive(is_select)
end