ThunderManaSelectView = ThunderManaSelectView or BaseClass(SafeBaseView)

function ThunderManaSelectView:__init()
    self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
    self:SetMaskBg()

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/thunder_mana_ui_prefab", "layout_thunder_mana_select")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")
end

function ThunderManaSelectView:__delete()
end

function ThunderManaSelectView:OpenCallBack()

end

function ThunderManaSelectView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesJPG("a3_lf_bj_1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
	self.node_list.title_view_name.text.text = Language.ThunderMana.ViewName

    XUI.AddClickEventListener(self.node_list["supreme_btn"], BindTool.Bind(self.OnClickSupremeBtn, self))
    XUI.AddClickEventListener(self.node_list["halo_btn"], BindTool.Bind(self.ClickHaloBtn, self))

    XUI.AddClickEventListener(self.node_list["shady_click_btn"], BindTool.Bind(self.OnClickShadyBtn, self))
    XUI.AddClickEventListener(self.node_list["sun_click_btn"], BindTool.Bind(self.ClickSunBtn, self))
	XUI.AddClickEventListener(self.node_list["mask_ui"], BindTool.Bind(self.ClickMaskBtn, self))
    -- 绑定红点
	self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
    RemindManager.Instance:Bind(self.remind_change, RemindName.SupremeFields)
	RemindManager.Instance:Bind(self.remind_change, RemindName.NewAppearance_WaiGuan_Halo)
	RemindManager.Instance:Bind(self.remind_change, RemindName.ThunderManaAllShadyView)
	RemindManager.Instance:Bind(self.remind_change, RemindName.ThunderManaAllSunView)
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ThunderManaSelectView, self.get_guide_ui_event)
end

function ThunderManaSelectView:ReleaseCallBack()
	if self.delay_show_1 then
		GlobalTimerQuest:CancelQuest(self.delay_show_1)
		self.delay_show_1 = nil
	end

	if self.delay_show_2 then
		GlobalTimerQuest:CancelQuest(self.delay_show_2)
		self.delay_show_2 = nil
	end

	if self.remind_change  then
		RemindManager.Instance:UnBind(self.remind_change)
	end
	self.remind_change = nil

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ThunderManaSelectView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function ThunderManaSelectView:ShowIndexCallBack(index)
	self.node_list.panel_group.transform.localPosition = Vector3(0, 0, 0)
	self.node_list.panel_group:SetActive(false)
	self.node_list.btn_group:SetActive(false)
	self.node_list.leifa_effect_1:SetActive(false)
	self.node_list.jump_text:SetActive(false)
	self.node_list.mask_ui:SetActive(false)

	self:PlayOpenEfffect()
end

function ThunderManaSelectView:OnFlush(param_t, index)
	
end

function ThunderManaSelectView:SetInitPos()
	self.node_list.panel_group.transform.localPosition = Vector3(0, 0, 0)
	self.node_list.panel_group:SetActive(true)
	self.node_list.btn_group:SetActive(true)
	
	self.node_list.leifa_effect_1:SetActive(false)
	self.node_list.jump_text:SetActive(false)
	self.node_list.mask_ui:SetActive(false)
end

function ThunderManaSelectView:PlayOpenEfffect()
	self.node_list.mask_ui:SetActive(true)
	self.node_list.leifa_effect_1:SetActive(true)
	self.node_list.jump_text:SetActive(true)

	self.delay_show_1 = GlobalTimerQuest:AddDelayTimer(function()
		self.node_list.panel_group:SetActive(true)
		self.node_list.btn_group:SetActive(true)
		self.node_list.jump_text:SetActive(false)
	end, 1)

	self.delay_show_2 = GlobalTimerQuest:AddDelayTimer(function()
		self.node_list.mask_ui:SetActive(false)
		self.node_list.leifa_effect_1:SetActive(false)
	end, 2)
end

function ThunderManaSelectView:OnClickSupremeBtn()
    ViewManager.Instance:Open(GuideModuleName.SupremeFieldsWGView)
end

function ThunderManaSelectView:ClickHaloBtn()
	ViewManager.Instance:Open(GuideModuleName.NewAppearanceHaloWGView)
end

function ThunderManaSelectView:OnClickShadyBtn()
	local tweener = self.node_list.panel_group.transform:DOLocalMove(Vector3(320, 0, -336), 0.5):SetEase(DG.Tweening.Ease.Linear):OnComplete(function ()
		ViewManager.Instance:Open(GuideModuleName.ShadyThunderView)
		self:SetInitPos()
	end)
end

function ThunderManaSelectView:ClickSunBtn()
	local tweener = self.node_list.panel_group.transform:DOLocalMove(Vector3(-320, 0, -336), 0.5):SetEase(DG.Tweening.Ease.Linear):OnComplete(function ()
		ViewManager.Instance:Open(GuideModuleName.SunThunderView)
		self:SetInitPos()
	end)
end

function ThunderManaSelectView:RemindChangeCallBack(remind_name, num)
    if remind_name == RemindName.SupremeFields then
		self.node_list.supreme_red:SetActive(num > 0)
	elseif remind_name == RemindName.NewAppearance_WaiGuan_Halo then
		self.node_list.halo_red:CustomSetActive(num > 0)
	elseif remind_name == RemindName.ThunderManaAllShadyView then
		self.node_list.shady_red:CustomSetActive(num > 0)
	elseif remind_name == RemindName.ThunderManaAllSunView then
		self.node_list.sun_red:CustomSetActive(num > 0)
	end
end

function ThunderManaSelectView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	return self.node_list[ui_name]
end

function ThunderManaSelectView:ClickMaskBtn()
	if self.delay_mask_show then
		if self.delay_show_1 then
			GlobalTimerQuest:CancelQuest(self.delay_show_1)
			self.delay_show_1 = nil
		end
	
		if self.delay_show_2 then
			GlobalTimerQuest:CancelQuest(self.delay_show_2)
			self.delay_show_2 = nil
		end

		self.node_list.panel_group:SetActive(true)
		self.node_list.btn_group:SetActive(true)
		self.node_list.mask_ui:SetActive(false)
		self.node_list.leifa_effect_1:SetActive(false)
		self.node_list.jump_text:SetActive(false)
		self:RemoveMaskDelayTimer()
		return
	end

	self.delay_mask_show = GlobalTimerQuest:AddDelayTimer(function ()
		self:RemoveMaskDelayTimer()
	end, 0.5)
end

function ThunderManaSelectView:RemoveMaskDelayTimer()
	if self.delay_mask_show then
		GlobalTimerQuest:CancelQuest(self.delay_mask_show)
		self.delay_mask_show = nil
	end
end