function WorldTreasureView:ReleaseCallBack_TotalRecharge()
    if self.tr_display_model then
        self.tr_display_model:DeleteMe()
        self.tr_display_model = nil
    end

    if self.tr_task_list_view then
        self.tr_task_list_view:DeleteMe()
        self.tr_task_list_view = nil
    end
end

function WorldTreasureView:LoadIndexCallBack_TotalRecharge()
    self.total_recharge_grade_change = true

    if not self.tr_task_list_view then
        self.tr_task_list_view = AsyncListView.New(WorldTreasureTrTaskItem, self.node_list.tr_task_list_view)
    end

    if not self.tr_display_model then
        self.tr_display_model = OperationActRender.New(self.node_list["tr_display_model"])
        self.tr_display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end
    --self.node_list.tr_btn_rule.button:AddClickListener(BindTool.Bind(self.OnClickTrRule, self))
end

function WorldTreasureView:ShowIndexCallBack_TotalRecharge()
    self:DoTRCellsAnim()
end

function WorldTreasureView:OnFlush_TotalRecharge(param_t, index)
    self:UpdateTotalRechargeView()
    self:FlushTotalRechargeModel()
end

function WorldTreasureView:UpdateTotalRechargeView()
    local data_list = WorldTreasureWGData.Instance:GetLeiChongDataList()
    if #data_list > 4 then
        self.node_list.tr_task_list_view:FindObj("Container").rect.pivot = Vector2(0.5, 1)
    else
        self.node_list.tr_task_list_view:FindObj("Container").rect.pivot = Vector2(0.5, 0.5)
    end

    self.tr_task_list_view:SetDataList(data_list)

    -- 档位图片
    local bundle, asset = WorldTreasureWGData.Instance:FormatGradeImage("a3_lchl_db_")
    self.node_list["tr_task_list_bg"].image:LoadSprite(bundle, asset)
    bundle, asset = WorldTreasureWGData.Instance:FormatGradeRawImage("a3_lchl_bt_")
    self.node_list["img_title_total_recharge"].raw_image:LoadSprite(bundle, asset)
end

function WorldTreasureView:FlushTotalRechargeModel()
    local model_cfg = WorldTreasureWGData.Instance:GetLeiChongModelCfg()
    if IsEmptyTable(model_cfg) then
		return
	end

	local display_data = {}
	display_data.should_ani = true
	if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
		local split_list = string.split(model_cfg.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg.model_show_itemid
		end
	end
	display_data.bundle_name = model_cfg["model_bundle_name"]
    display_data.asset_name = model_cfg["model_asset_name"]
    local model_show_type = tonumber(model_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    if model_cfg["model_show_itemid"] ~= "" then
        display_data.model_click_func = function ()
            TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
        end
    end

    self.tr_display_model:SetData(display_data)
    local scale = model_cfg["display_scale"]
    if scale and scale ~= "" then
        Transform.SetLocalScaleXYZ(self.node_list["tr_display_model"].transform, scale, scale, scale)
	end

	local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
        RectTransform.SetAnchoredPositionXY(self.node_list.tr_display_model.rect, pos_x, pos_y)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rotation_tab = string.split(model_cfg.rotation,"|")
		self.node_list["tr_display_model"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

function WorldTreasureView:DoTRCellsAnim()
	local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender
	self.node_list["tr_task_list_view"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["tr_task_list_view"]:SetActive(true)
        local list =  self.tr_task_list_view:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyItemAnim(count)
        end
    end, tween_info.DelayDoTime, "total_recharge_cell_tween")
end

-- function WorldTreasureView:OnClickTrRule()
--     RuleTip.Instance:SetContent(Language.WorldTreasure.TotalRechargeDesc, Language.WorldTreasure.DescTitle)
-- end

WorldTreasureTrTaskItem = WorldTreasureTrTaskItem or BaseClass(BaseRender)

function WorldTreasureTrTaskItem:LoadCallBack()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    self.reward_list:SetStartZeroIndex(true)
    XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind1(self.OnClickBtnReceive, self))
    XUI.AddClickEventListener(self.node_list["btn_recharge"], BindTool.Bind1(self.OnClickBtnRecharge, self))
end

function WorldTreasureTrTaskItem:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function WorldTreasureTrTaskItem:OnFlush()
    if not self.data then
        return
    end

    local bundle, asset = WorldTreasureWGData.Instance:FormatGradeRawImage("a3_lchl_djdb_")
    self.node_list["bg"].raw_image:LoadSprite(bundle, asset)

    local cur_xianyu = WorldTreasureWGData.Instance:GetLeiChongCurXianYu()
    local color = cur_xianyu >= self.data.stage_value and "#3C8652" or "#A93C3C"
    self.node_list.recharge_num.text.text = string.format(Language.WorldTreasure.TotalRechargeBili, ToColorStr(cur_xianyu, color), self.data.stage_value)

    self.reward_list:SetRefreshCallback(function(item_cell, cell_index)
        if item_cell then
            item_cell:SetLingQuVisible(self.data.state == TREASURE_OPERATE_TYPE.HAS_LINGQU)
            item_cell:SetRedPointEff(self.data.state == TREASURE_OPERATE_TYPE.CAN_LINGQU)
        end
    end)
    self.reward_list:SetDataList(self.data.reward_item)
    self.node_list.btn_recharge:SetActive(self.data.state == TREASURE_OPERATE_TYPE.NOT_OVER)
    self.node_list.get_flag:SetActive(self.data.state == TREASURE_OPERATE_TYPE.HAS_LINGQU)
    self.node_list.btn_receive:SetActive(self.data.state == TREASURE_OPERATE_TYPE.CAN_LINGQU)
    self.node_list.remind:SetActive(self.data.state == TREASURE_OPERATE_TYPE.CAN_LINGQU)

    -- local bundle, asset = ResPath.GetWorldTreasureRawImages("lchl_di01")
	-- self.node_list.bg.raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list.bg.raw_image:SetNativeSize()
	-- end)

    -- local cur_grade = WorldTreasureWGData.Instance:GetTreasureCurGrade()
    -- bundle, asset = ResPath.GetWorldTreasureImg("a3_hs_lchl_di02" .. cur_grade)
	-- self.node_list.bg2.image:LoadSprite(bundle, asset, function()
	-- 	self.node_list.bg2.image:SetNativeSize()
	-- end)
end

function WorldTreasureTrTaskItem:OnClickBtnReceive()
    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_LEICHONG_FETCH, self.data.grade, self.data.ID)
end

function WorldTreasureTrTaskItem:OnClickBtnRecharge()
    ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
end

function WorldTreasureTrTaskItem:PalyItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.WorldTreasureView, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "tr_task_item_" .. wait_index)
end