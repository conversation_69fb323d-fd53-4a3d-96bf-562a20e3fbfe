require("game/kf_cap_rank/kf_cap_rank_view")
require("game/kf_cap_rank/kf_cap_rank_wg_data")

KFCapRankWGCtrl = KFCapRankWGCtrl or BaseClass(BaseWGCtrl)
function KFCapRankWGCtrl:__init()
	if KFCapRankWGCtrl.Instance then
		ErrorLog("[KFCapRankWGCtrl] Attemp to create a singleton twice !")
	end

	KFCapRankWGCtrl.Instance = self
    self.data = KFCapRankWGData.New()
	self.view = KFCapRankView.New(GuideModuleName.KFCapRankView)
    self:RegisterProtocol(SCCrossCapRankInfo, 'OnSCCrossCapRankInfo')
end

function KFCapRankWGCtrl:__delete()
	KFCapRankWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
end

function KFCapRankWGCtrl:SendCrossCapRankReq(opera_type,param_1,param_2,param_3)
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_KF_CAP_RANK
	param_t.opera_type = opera_type or 0
	param_t.param_1 = param_1 or 0
	param_t.param_2 = param_2 or 0
	param_t.param_3 = param_3 or 0

	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end

function KFCapRankWGCtrl:OnSCCrossCapRankInfo(protocol)
	self.data:SetSwornRankSort(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end
