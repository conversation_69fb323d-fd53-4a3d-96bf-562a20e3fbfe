TianShenShuangShengWGData = TianShenShuangShengWGData or BaseClass()

function TianShenShuangShengWGData:__init()
	if TianShenShuangShengWGData.Instance then
		print_error("[TianShenShuangShengWGData] Attempt to create singleton twice!")
		return
	end

	TianShenShuangShengWGData.Instance = self
    self:InitConfig()
    self:InitShuangShengData()

    RemindManager.Instance:Register(RemindName.TianShenShuangSheng, BindTool.Bind(self.GetRemind,self))
end

function TianShenShuangShengWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.TianShenShuangSheng)
    TianShenShuangShengWGData.Instance = nil

    self.tianshen_avatar_map_cfg = nil
    self.tianshen_avatar_lv_map_cfg = nil
    self.tianshen_avatar_image_id_cfg = nil
    self.show_data = nil
    self.level_preview_data = {}
    self.preview_show_data = nil
end

-- 初始化配置
function TianShenShuangShengWGData:InitConfig()
    local tianshen_cfg = ConfigManager.Instance:GetAutoConfig("tianshen_cfg_auto")
    if tianshen_cfg then
        self.tianshen_avatar_map_cfg = ListToMap(tianshen_cfg.tianshen_avatar, "index")
        self.tianshen_avatar_lv_map_cfg = ListToMap(tianshen_cfg.tianshen_avatar_upgrade, "index", "star_level")
        self.tianshen_avatar_image_id_cfg = tianshen_cfg.tianshen_avatar_list
        self:InitPreviewShowData()
    end
end

-- 初始化预览位置大小配置
function TianShenShuangShengWGData:InitPreviewShowData()
    self.preview_show_data = {}
    if not self.tianshen_avatar_image_id_cfg then
        return
    end

    for avatar_id, avatar_data in pairs(self.tianshen_avatar_image_id_cfg) do
        local data = {}
        if avatar_data.preview_pos then
            local pos_str = Split(avatar_data.preview_pos, "|")
            local pos_data = {}
            pos_data.x = tonumber(pos_str[1]) or 0
            pos_data.y = tonumber(pos_str[2]) or 0
            pos_data.z = tonumber(pos_str[3]) or 0
            data.pos = pos_data
        end

        if avatar_data.preview_rotation then
            local rot_str = Split(avatar_data.preview_rotation, "|")
            local rot_data = {}
            rot_data.x = tonumber(rot_str[1]) or 0
            rot_data.y = tonumber(rot_str[2]) or 0
            rot_data.z = tonumber(rot_str[3]) or 0
            data.rot = rot_data
        end

        data.preview_scale = avatar_data.preview_scale
        self.preview_show_data[avatar_id] = data
    end
end

--检测是否是技能书道具id
function TianShenShuangShengWGData:CheckisActiveOrUpgradeItemId(item_id)
	if (not self.tianshen_avatar_map_cfg) and (not self.tianshen_avatar_lv_map_cfg) then
		return false
	end

	for _, skill_data in pairs(self.tianshen_avatar_map_cfg) do
		if skill_data and skill_data.active_item and skill_data.active_item.item_id == item_id then
			return true
		end
	end

    for _, index_cfg in pairs(self.tianshen_avatar_lv_map_cfg) do
        if index_cfg then
            for _, skill_data in pairs(index_cfg) do
                if skill_data and skill_data.consume_item and skill_data.consume_item.item_id == item_id then
                    return true
                end
            end
        end
	end

	return false
end

-- 初始化所有的当前配置的数据
function TianShenShuangShengWGData:InitShuangShengData()
    if not self.tianshen_avatar_map_cfg then
        return
    end
    
    for _, avatar_data in pairs(self.tianshen_avatar_map_cfg) do
        if avatar_data and avatar_data.index then
            self:CreateShuangShengData(avatar_data)
            self:CreateShuangShengPreviewData(avatar_data.index, avatar_data)
        end
    end
end

-- 创建一份双生神灵数据
function TianShenShuangShengWGData:CreateShuangShengData(cfg_data)
    if not self.show_data then
        self.show_data = {}
    end

    local info = {}
    info.index = cfg_data.index
    info.star_level = 0
    info.curr_aura_id = -1
    info.remind = false
    info.is_unlock = false
    self.show_data[cfg_data.index] = info
end

function TianShenShuangShengWGData:GetRemind()
    if not self.show_data then
        return 0
    end

    	-- 开启判断
	if not FunOpen.Instance:GetFunIsOpened(FunName.TianShenShuangShengView) then
		return 0
	end

    for _, data in pairs(self.show_data) do
        if data.remind then
            return 1
        end
    end

    return 0
end

function TianShenShuangShengWGData:CreateShuangShengPreviewData(tianshen_index, cfg_data)
    if not cfg_data then
        return
    end

    if not self.level_preview_data then
        self.level_preview_data = {}
    end

    if not self.level_preview_data[tianshen_index] then
        self.level_preview_data[tianshen_index] = {}
    end

    local level_preview_list = Split(cfg_data.level_preview, ",")
    for index, preview_str in ipairs(level_preview_list) do
        local value = tonumber(preview_str) or 0
        self.level_preview_data[tianshen_index][index] = value
    end
end

-- 获取一份双生神灵配置
function TianShenShuangShengWGData:GetTianShenAvatarCfgData(index)
    local empty = {}
    return (self.tianshen_avatar_map_cfg or empty)[index] or nil
end

-- 判断是否存在这个双生神灵配置
function TianShenShuangShengWGData:IsHaveTianShenAvatarCfg(index)
    local data = self:GetTianShenAvatarCfgData(index)
    if data == nil then
        return false
    end

    return true
end

-- 获取一份双生神灵的等级配置
function TianShenShuangShengWGData:GetTianShenAvatarLvCfgData(index, star_level)
    local empty = {}
    return ((self.tianshen_avatar_lv_map_cfg or empty)[index] or empty)[star_level] or nil
end

-- 获取一份双生神灵形象配置
function TianShenShuangShengWGData:GetTianShenAvatarAppImageIdCfgData(avatar_id)
    local empty = {}
    return (self.tianshen_avatar_image_id_cfg or empty)[avatar_id] or nil
end

-- 获取一份双生神灵预览形象展示位置配置
function TianShenShuangShengWGData:GetTianShenAvatarPreviewShowData(avatar_id)
    local empty = {}
    return (self.preview_show_data or empty)[avatar_id] or nil
end

-- 获取单个的双生神灵缓存
function TianShenShuangShengWGData:GetTianShenAvatarData(index)
    local empty = {}
    return (self.show_data or empty)[index] or nil
end

-- 获取单个的双生神灵缓存
function TianShenShuangShengWGData:GetTianShenAvatarModelList(tianshen_index, aim_star_level)
    local level_preview_list = self:GetTianShenAvatarPreviewList(tianshen_index)

    if not level_preview_list then
        return nil
    end

    local model_list = {}
    local best_index = 1

    for index, star_level in ipairs(level_preview_list) do
        local data = {}
        data.star_level = star_level
        data.is_lock = true

        table.insert(model_list, data)    -- 这里先插入确保多一个前置预览

        if star_level > aim_star_level then
            break
        end

        best_index = index
        data.is_lock = false
    end

    return model_list, best_index
end

-- 获取当前列表当前选中的位置
function TianShenShuangShengWGData:GetCurModelListIndex(show_model_list, aim_star_level)
    if not show_model_list then
        return 1
    end

    local return_index = 1
    for index, show_model_data in ipairs(show_model_list) do
        if show_model_data and show_model_data.star_level > aim_star_level  then
            break
        end
        
        return_index = index    -- 这里后赋值确定最优解
    end

    return return_index
end

-- 获取单个的双生神灵缓存
function TianShenShuangShengWGData:GetTianShenAvatarPreviewList(tianshen_index)
    local empty = {}
    return (self.level_preview_data or empty)[tianshen_index] or nil
end

-- 获取单个的双生神灵缓存
function TianShenShuangShengWGData:GetTianShenAvatarPreviewByIndex(tianshen_index, preview_index)
    local empty = {}
    return ((self.level_preview_data or empty)[tianshen_index] or empty)[preview_index] or nil
end

-- 获取所有的双生神灵是否存在红点
function TianShenShuangShengWGData:GetTianShenAvatarRemind()
    local is_open_func = FunOpen.Instance:GetFunIsOpened(FunName.TianShenShuangShengView)
    -- 未开启
    if not is_open_func then
        return 0
    end
    -- 不存在配置
    if not self.show_data then
        return 0
    end

    for _, info in pairs(self.show_data) do
        if info.remind then
            return 1
        end
    end

    return 0
end


-- 获取一个双生神灵的红点
function TianShenShuangShengWGData:GetOneTianShenAvatarRemind(index)
    local info = self:GetTianShenAvatarData(index)
    if info then
        return info.remind
    end
    
    return false
end

-- 刷新所有的双生神灵的数据(服务器)
function TianShenShuangShengWGData:FlushAllTianShenAvatar(server_list)
    if not server_list then
        return
    end

    for index, server_data in ipairs(server_list) do
        local server_index = index - 1
        self:FlushOneTianShenAvatar(server_index, server_data)
    end
end

-- 刷新单个的双生神灵数据(服务器)
function TianShenShuangShengWGData:FlushOneTianShenAvatar(index, server_data)
    local info = self:GetTianShenAvatarData(index)
    
    if not info then
        return
    end

    -- 这里这样赋值时方便未激活时能取到一个0级的基本数据
    info.star_level = server_data.star_level > -1 and server_data.star_level or 0
    info.curr_aura_id = server_data.curr_aura_id
    info.is_unlock = server_data.star_level > -1      --- 初始化是-1, 0级以上表示已解锁

    if not info.is_unlock then      -- 检测可激活
        info.remind = self:CheckCanActive(info.index) 
    else
        info.remind = self:CheckCanUpGrade(info.index, info)     --检测可升星
    end
end

--刷新所有的数据红点(自检，物品变化)
function TianShenShuangShengWGData:FlushAllTianShenRemind()
    for _, info in pairs(self.show_data) do
        if not info.is_unlock then      -- 检测可激活
            info.remind = self:CheckCanActive(info.index) 
        else
            info.remind = self:CheckCanUpGrade(info.index, info)     --检测可升星
        end
    end
end

-- 检测是否可以激活
function TianShenShuangShengWGData:CheckCanActive(index)
	local is_active = TianShenWGData.Instance:GetTianshenInfoStatus(index) == 1
    if not is_active then
        return false
    end

	local cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarCfgData(index)
	if cfg and cfg.active_item then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.active_item.item_id)
		if item_num < cfg.active_item.num then
			return false
		else
            return true
		end
	end

    return false
end

-- 检测是否可以激活
function TianShenShuangShengWGData:CheckCanUpGrade(index, info)
	local level_cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarLvCfgData(index, info.star_level)
    local level_next_cfg = TianShenShuangShengWGData.Instance:GetTianShenAvatarLvCfgData(index, info.star_level + 1)
    if not level_cfg then
        return false
    end

    if level_cfg.need_rumo_level > 0 then
        local sub_data = TianShenHuamoWGData.Instance:GetHuaMoSubDataById(index, level_cfg.need_rumo_level)
        local is_active = sub_data and (not sub_data.lock)
        if not is_active then
            return false
        end
    end

    if level_cfg.consume_item then
        local item_num = ItemWGData.Instance:GetItemNumInBagById(level_cfg.consume_item.item_id)
        if not level_next_cfg then      -- 满星了
            return false
        end

        if item_num < level_cfg.consume_item.num then
            return false
        else
            return true
        end
    end

    return false
end

-- 获取当前的属性配置（是否包含下一级属性（可选））
function TianShenShuangShengWGData:GetTianShenAvatarAttrList(index, star_level, need_next)
    local cfg = self:GetTianShenAvatarLvCfgData(index, star_level)
    local attr_list = {}
    local next_cfg = nil

    if need_next then
        next_cfg = self:GetTianShenAvatarLvCfgData(index, star_level + 1)
    end

    if not cfg then
        return attr_list
    end

	for i = 1, 7 do
		local key = cfg["attr_id" .. i]
		if key then
			if not attr_list[key] then
				attr_list[key] = {}
				attr_list[key].attr_str = key
			end

            attr_list[key].attr_value = cfg["attr_value" .. i]

            if next_cfg and need_next and next_cfg["attr_id" .. i] == key then
                attr_list[key].add_value = next_cfg["attr_value" .. i] - cfg["attr_value" .. i]
            end
		end
	end

	local return_table = {}

	for _, attr_data in pairs(attr_list) do
		if attr_data and attr_data.attr_str then
			table.insert(return_table, attr_data)
		end
	end

	table.sort(return_table, function (a, b)
		return a.attr_str < b.attr_str
	end)

	return return_table
end

-- 获取当前的战斗力
function TianShenShuangShengWGData:GetTianShenAvatarCapValue(index, star_level, attr_list)
    local temp_attr_list = attr_list

    if not temp_attr_list then
        temp_attr_list = self:GetTianShenAvatarAttrList(index, star_level)
    end

    local attribute = AttributePool.AllocAttribute()
    local function add_tab(attr_str, value)
        if attr_str == nil or value == nil then
            return
        end
        if attribute[attr_str] then
            attribute[attr_str] = attribute[attr_str] + value
        end
    end

    -- 加入基础属性
    for _, attr_cell in ipairs(temp_attr_list) do
        if attr_cell.attr_str > 0 and attr_cell.attr_value > 0 then
            local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_cell.attr_str)
            add_tab(attr_str, attr_cell.attr_value)
        end
    end

    -- 计算技能属性战力
    local cap = AttributeMgr.GetCapability(attribute)
    return cap
end

--获取一个双生物品的属性信息配置
function TianShenShuangShengWGData:GetShuangShengTianShenAttrByItemId(item_id)
	if not self.tianshen_avatar_lv_map_cfg then
		return nil
	end

    for _, index_cfg in pairs(self.tianshen_avatar_lv_map_cfg) do
        if index_cfg then
            for _, data in pairs(index_cfg) do
                if data and data.consume_item and data.consume_item.item_id == item_id then
                    return data
                end
            end
        end
	end

	return nil
end

--获取一个双生物品的属性信息配置
function TianShenShuangShengWGData:IsShuangshengActive(cfg)
    if not cfg then
        return false
    end

    local data = self:GetTianShenAvatarData(cfg.index)
    if data then
        return data.is_unlock
    end

    return false
end
