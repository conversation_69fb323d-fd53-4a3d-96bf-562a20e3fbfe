-----------------------------------
-- 百亿补贴-折扣卷选择入口
-----------------------------------
function BillionSubsidyView:DCLoadIndexCallBack()
    XUI.AddClickEventListener(self.node_list.btn_open_discount_coupon_bag, BindTool.Bind(self.OnClickOpenDCBagBtn, self))
    --XUI.AddClickEventListener(self.node_list.btn_switch_discount_coupon, BindTool.Bind(self.OnClickSwitchDCBtn, self))
end

function BillionSubsidyView:DCReleaseCallBack()

end

function BillionSubsidyView:DCCloseCallBack()

end

function BillionSubsidyView:DCShowIndexCallBack()
    self:FlushSwitchDCBtnStatus()
end

function BillionSubsidyView:DCOnFlush(param_t, index)
    --self:FlushDCAllConditionDesc()
    local show_index =  BillionSubsidyWGCtrl.Instance:GetCurShowIndex()
    local dc_all_data_list = BillionSubsidyWGData.Instance:GetDiscountCouponAllDataList(show_index)
    self.node_list.btn_open_discount_coupon_bag_desc.text.text = string.format(Language.BillionSubsidy.DCBagDesc, #dc_all_data_list)
end

function BillionSubsidyView:FlushDCAllConditionDesc()
    local select_dc_data = BillionSubsidyWGData.Instance:GetSelectDCData()
    local default_select_dc_data = {}

    if IsEmptyTable(select_dc_data) then
        if self.show_index == TabIndex.billion_subsidy_bybt then
            local shop_cfg = BillionSubsidyWGData.Instance:GetBYBTShopItemGradeCfg()
            if shop_cfg then
                default_select_dc_data = BillionSubsidyWGData.Instance:GetMaxDiscountDCData(TabIndex.billion_subsidy_bybt, shop_cfg)
            end
        elseif self.show_index == TabIndex.billion_subsidy_xdzk then
            local shop_cfg = BillionSubsidyWGData.Instance:GetLimitTimeShopItemGradeCfg()
            if shop_cfg then
                default_select_dc_data = BillionSubsidyWGData.Instance:GetMaxDiscountDCData(TabIndex.billion_subsidy_xdzk, shop_cfg)
            end
        elseif self.show_index == TabIndex.billion_subsidy_jkjzc then
            local shop_cfg = BillionSubsidyWGData.Instance:GetJKJZCShopCfg()
            if shop_cfg then
                default_select_dc_data = BillionSubsidyWGData.Instance:GetMaxDiscountDCData(TabIndex.billion_subsidy_jkjzc, shop_cfg)
            end
        elseif self.show_index == TabIndex.billion_subsidy_dezg then
            local dezg_cfg = BillionSubsidyWGData.Instance:GetDEZGCfgBySeq(self.select_tab_seq)
            if dezg_cfg then
                local data = {dezg_cfg}
                default_select_dc_data = BillionSubsidyWGData.Instance:GetMaxDiscountDCData(TabIndex.billion_subsidy_dezg, data)
            end
        end

        if IsEmptyTable(default_select_dc_data) then
            self.node_list.discount_coupon_display_desc.text.text = Language.BillionSubsidy.NoDCCanUseDesc
        else
            BillionSubsidyWGData.Instance:SetSelectDCData(default_select_dc_data)
            self:FlushDCDisplayDesc()
        end
    else
        self:FlushDCDisplayDesc()
    end
end

function BillionSubsidyView:FlushDCDisplayDesc()
    local select_dc_data = BillionSubsidyWGData.Instance:GetSelectDCData()
    local str = ""

    if select_dc_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.FULL_DISCOUNT_COUPON then
        str = string.format(Language.BillionSubsidy.FullDCSelectDesc, select_dc_data.quota_limit, select_dc_data.reduce_quota)
    elseif select_dc_data.type == TEN_BILLION_SUBSIDY_DISCOUNT_COUPON_TYPE.DIRECT_DISCOUNT_COUPON then
        str = string.format(Language.BillionSubsidy.DirectDCSelectDesc, select_dc_data.reduce_quota)
    else
        str = Language.BillionSubsidy.FreeDCSelectDesc
    end
    self.node_list.discount_coupon_display_desc.text.text = str
end

function BillionSubsidyView:OnClickOpenDCBagBtn()
    BillionSubsidyWGCtrl.Instance:OpenDCBag()
end

function BillionSubsidyView:OnClickSwitchDCBtn()
    local is_use = BillionSubsidyWGData.Instance:GetUseDCStatus()
    is_use = not is_use
    BillionSubsidyWGData.Instance:SetUseDCStatus(is_use)
    self:FlushSwitchDCBtnStatus()
end

function BillionSubsidyView:FlushSwitchDCBtnStatus()
	local is_use = BillionSubsidyWGData.Instance:GetUseDCStatus()
    self.node_list["use_discount_coupon_yes"]:SetActive(is_use)
end