GuildInviteSceneView = GuildInviteSceneView or BaseClass(SafeBaseView)

GuildInviteSceneView.LEFTPANEL = {
	RANK = 1,
	PINGJI = 2
}

function GuildInviteSceneView:__init()
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_war_invite_info")
	-- self.view_layer = UiLayer.MainUI
	-- 
end

function GuildInviteSceneView:__delete()
end

function GuildInviteSceneView:ReleaseCallBack()
	if self.hurt_list then
		self.hurt_list:DeleteMe()
		self.hurt_list = nil
	end

	if self.pingji_list then
		self.pingji_list:DeleteMe()
		self.pingji_list = nil
	end
end

function GuildInviteSceneView:LoadCallBack()
	
end

function GuildInviteSceneView:CloseCallBack()
	self:ReleaseTaskPanel()
end

function GuildInviteSceneView:ShowIndexCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function ( )
		self:InitInviteSceneCallBack()
	end)
end

function GuildInviteSceneView:InitInviteSceneCallBack()
	-- local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	-- MainuiWGCtrl.Instance:ChangeTeamBtnName(Language.GuildInvite.BtnName2)
	-- MainuiWGCtrl.Instance:SetTaskContents(false)
	-- MainuiWGCtrl.Instance:SetOtherContents(false)

	XUI.AddClickEventListener(self.node_list["TaskButton1"],BindTool.Bind(self.OnClickBtnTask, self))
	XUI.AddClickEventListener(self.node_list["TaskButton2"],BindTool.Bind(self.OnClickBtnTeam, self))

	-- MainuiWGCtrl.Instance:SetTaskAndTeamCallBack(BindTool.Bind(self.OnClickBtnTask, self),
		-- BindTool.Bind(self.OnClickBtnTeam, self))
	-- parent:SetActive(true)
	-- self.node_list["layout_root"].transform:SetParent(parent.transform, false)
	self:OnClickBtnTask(true)
end

--还原
function GuildInviteSceneView:ReleaseTaskPanel()
	
end

function GuildInviteSceneView:OnFlush()
	self:FlushRankPanel()
	self:FlushPingJiPanel()
end

function GuildInviteSceneView:OnClickBtnTask(isOn)
	self.right_panel = GuildInviteSceneView.LEFTPANEL.RANK
	self.node_list["layout_rank_info"]:SetActive(isOn)
	self.node_list["layout_pingji_info"]:SetActive(not isOn)
	self:FlushRankPanel()
end

function GuildInviteSceneView:OnClickBtnTeam(isOn)
	self.right_panel = GuildInviteSceneView.LEFTPANEL.PINGJI
	self.node_list["layout_rank_info"]:SetActive(not isOn)
	self.node_list["layout_pingji_info"]:SetActive(isOn)
	self:FlushPingJiPanel()
end

function GuildInviteSceneView:FlushRankPanel()
	if self.right_panel ~= GuildInviteSceneView.LEFTPANEL.RANK then
		return
	end
	if not self.hurt_list then
		self.hurt_list = AsyncListView.New(GuildInviteHurtItem, self.node_list["hurt_list"])
	end
	local data_list = GuildInviteWGData.Instance:GetGuildInfoList()
	self.hurt_list:SetDataList(data_list)
	local my_damate = GuildInviteWGData.Instance:GetSelfGuildHurt()
	local max_hurt = GuildInviteWGData.Instance:GetGuildMaxHurt()
	max_hurt = max_hurt ~= 0 and max_hurt or 1 
	self.node_list["my_damate"].text.text = CommonDataManager.ConverNumber(my_damate)
	self.node_list["hunt_slider"].slider.value = my_damate / max_hurt
end

function GuildInviteSceneView:FlushPingJiPanel()
	if self.right_panel ~= GuildInviteSceneView.LEFTPANEL.PINGJI then
		return
	end
	if not self.pingji_list then
		self.pingji_list = AsyncListView.New(GuildInvitePingJiItem, self.node_list["pingji_list"])
	end
	local data_list = GuildWGData.Instance:GetGuildBattleRanklist()
	self.pingji_list:SetDataList(data_list)
end


--------------------
GuildInviteHurtItem = GuildInviteHurtItem or BaseClass(BaseRender)
function GuildInviteHurtItem:__init()
end
function GuildInviteHurtItem:__delete()
end

function GuildInviteHurtItem:OnFlush()
	if self.index <= 3 then
        local bundle, asset = ResPath.GetCommonImages("icon_paiming"..self.index)
        self.node_list["rank_img"].image:LoadSprite(bundle, asset)
    else
        local num = self.data.rank or self.index
        self.node_list["num"].text.text = num
    end
    self.node_list["rank_img"]:SetActive(self.index <= 3)
    self.node_list["num"]:SetActive(self.index > 3)
	local name = self.data.guild_name or ""
	local hurt_val = self.data.hurt_value
	local role_info= RoleWGData.Instance:GetRoleInfo()
	local damage_info = CommonDataManager.ConverNumber(hurt_val)
	self.node_list.name.text.text = name
	self.node_list.damage.text.text = damage_info

	self:CellImageFillAmount()
end

function GuildInviteHurtItem:CellImageFillAmount()
	local hurt_rank_list = GuildInviteWGData.Instance:GetGuildInfoList()
	if hurt_rank_list then
		local hurt_val = hurt_rank_list[1].hurt_value
		if hurt_val <= 0 or hurt_val == nil then
			hurt_val = 1
		end
		self.node_list.hunt_slider.slider.value = self.data.hurt_value / hurt_val
	else
		self.node_list.hunt_slider.slider.value = 0
	end
end


------------------
local SaiQu_Num = 4
GuildInvitePingJiItem = GuildInvitePingJiItem or BaseClass(BaseRender)
function GuildInvitePingJiItem:__init()
end
function GuildInvitePingJiItem:__delete()
end

function GuildInvitePingJiItem:OnFlush()
	if not self.data then return end
	self.node_list["zone_img"].image:LoadSprite(ResPath.GetGuildSystemImage("rank_render_"..self.index))
	local num_1 = (self.index - 1) * SaiQu_Num + 1
	local num_2 = (self.index - 1) * SaiQu_Num + SaiQu_Num
	self.node_list["rank_text"].text.text = string.format(Language.GuildInvite.SaiQuStr,num_1,num_2)
end