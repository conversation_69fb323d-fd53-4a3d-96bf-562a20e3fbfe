require("game/boss/boss_godwar_wg_data")
require("game/boss/boss_godwar_view")

BossGodWarWGCtrl = BossGodWarWGCtrl or BaseClass(BaseWGCtrl)
function BossGodWarWGCtrl:__init()
    if BossGodWarWGCtrl.Instance ~= nil then
		print_error("[BossGodWarWGCtrl] attempt to create singleton twice!")
		return
	end

	BossGodWarWGCtrl.Instance = self
    self.data = BossGodWarWGData.New()
    self.view = BossGodWarView.New(GuideModuleName.BossGodWarView) --激活特权view
    self:RegisterAllProtocals()
end

function BossGodWarWGCtrl:__delete()
    BossGodWarWGCtrl.Instance = nil

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end
end

function BossGodWarWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSRoleZhanshenPrivilegeReq)
	self:RegisterProtocol(SCRoleZhanshenPrivilegeInfo, "OnSCRoleZhanshenPrivilegeInfo")
end

--请求操作
function BossGodWarWGCtrl:SendBossPrivilegeReq(operate_type, param1)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRoleZhanshenPrivilegeReq)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

--特权信息
function BossGodWarWGCtrl:OnSCRoleZhanshenPrivilegeInfo(protocol)
    -- print_error("战神特权信息", protocol)
    self.data:SetPrivilegeInfo(protocol)
    if self.view:IsOpen() then
        self.view:Flush()
    end

    -- 不做操作了
    -- self:CheckOpenFindBoss(protocol)
    MainuiWGCtrl.Instance:FlushView(0, "boss_god_war_info")
end

-- 检测是否查找boss
function BossGodWarWGCtrl:CheckOpenFindBoss(protocol)
    -- 开启了(进行检测)
    local god_war_data = self.data:GetPrivilegeInfo()
	local scene_cfg = Scene.Instance:GetCurFbSceneCfg()

    if god_war_data.level ~= -1 and protocol.open_status == 1 and scene_cfg.scene_type == SceneType.VIP_BOSS then
        if protocol.total_count <= 0 then
            self:SendBossPrivilegeReq(ZHANSHENPRIVILEGE_OPERA_TYPE.SET_STATUS, 0)
        end
    end
end

--打开特权激活界面
function BossGodWarWGCtrl:OpenBossGodWarPrivilegeView()
    self.view:Open()
end

--打开特权升级界面
function BossGodWarWGCtrl:OpenBossPrivilegeUpgradeView()
    -- self.view:Open()
end

function BossGodWarWGCtrl:OpenSceneView()
    -- local is_open = BossPrivilegeWGData.Instance:GetActivateState() --特权开启状态
    -- if is_open < 1 then
    --     self.activate_view:Open()
    -- end
end

--进入场景回调
function BossGodWarWGCtrl:EnterBossPrivilegeSceneCallBack()
    -- local is_open = BossPrivilegeWGData.Instance:GetActivateState() --特权开启状态
    -- if is_open < 1 then
    --     if not CountDownManager.Instance:HasCountDown("boss_privilege_time") then
    --         CountDownManager.Instance:AddCountDown("boss_privilege_time", 
    --             nil, 
    --             BindTool.Bind(self.OpenSceneView, self), 
    --             nil, GameEnum.BOSS_FB_COUNTDOWN_TIME, 1)
    --     end
    -- end
end

--离开场景
function BossGodWarWGCtrl:OutBossPrivilegeSceneCallBack()
    -- if CountDownManager.Instance:HasCountDown("boss_privilege_time") then
    --     CountDownManager.Instance:RemoveCountDown("boss_privilege_time")
    -- end
end

