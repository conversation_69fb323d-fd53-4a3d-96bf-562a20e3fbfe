KfPVPFisish = KfPVPFisish or BaseClass(SafeBaseView)

function KfPVPFisish:__init()
	self.is_modal = true
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false,false)
	self:LoadConfig()
end

function KfPVPFisish:__delete()

end

function KfPVPFisish:ReleaseCallBack()
	if self.result_list then
		self.result_list:DeleteMe()
		self.result_list = nil
	end

	if nil ~= self.kafu3v3_finish_delay then
		GlobalTimerQuest:CancelQuest(self.kafu3v3_finish_delay)
		self.kafu3v3_finish_delay = nil
	end
	if CountDownManager.Instance:HasCountDown("pvp_finish_down") then
		CountDownManager.Instance:RemoveCountDown("pvp_finish_down")
	end
end

function KfPVPFisish:LoadConfig()
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "layout_kf3v3_finish")
end

function KfPVPFisish:LoadCallBack()
	GuajiWGCtrl.Instance:StopGuaji()
	--self.kafu3v3_finish_delay = GlobalTimerQuest:AddDelayTimer(function () self:Close() end, 10) --添加关闭倒计时
	--FuBenPanelWGCtrl.Instance:SetCountDowmTimer(10)
	if CountDownManager.Instance:HasCountDown("pvp_finish_down") then
		CountDownManager.Instance:RemoveCountDown("pvp_finish_down")
	end
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), TimeWGCtrl.Instance:GetServerTime() + 9)
	CountDownManager.Instance:AddCountDown("pvp_finish_down", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.QuiteScene, self),  TimeWGCtrl.Instance:GetServerTime() + 10, nil, 1)
	
	self:CreateList()
	self:Flush()
	XUI.AddClickEventListener(self.node_list.btn_quit_scene, BindTool.Bind1(self.QuiteScene, self))
	-- XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.QuiteScene, self))	--不需要关闭按钮
end
function KfPVPFisish:UpdateTime(elapse_time, total_time)
	local temp_seconds = GameMath.Round(total_time - elapse_time)
	if self.node_list.lbl_btn_quit then
		self.node_list.lbl_btn_quit.text.text = "退出场景("..temp_seconds.."s)"
	end
end

function KfPVPFisish:CreateList()
	if not self.result_list then
		self.result_list = AsyncListView.New(Kf3V3ResultItemRender,self.node_list.ph_result_list)
	end
end

function KfPVPFisish:OpenCallBack()
	

end
function KfPVPFisish:Close()
	-- CrossServerWGCtrl.ConnectBack()
	-- FuBenWGCtrl.Instance:SendLeaveFB()
	Field1v1WGCtrl.Instance:LeaveZhanChangScene()
	SafeBaseView.Close(self)
end

function KfPVPFisish:QuiteScene()
	self:Close()
	-- CrossServerWGCtrl.ConnectBack()
	-- FuBenWGCtrl.Instance:SendLeaveFB()
end

function KfPVPFisish:OnFlush(param_t)
	local role_info = KuafuPVPWGData.Instance:GetRoleInfo()
	local macth_info = KuafuPVPWGData.Instance:GetPrepareInfo()
	local data = KuafuPVPWGData.Instance:GetResultList()
	local img = "kf_lose_bg"
	local img_down = "pvp_lose_down"
	if role_info.self_side == macth_info.win_side then
		img = "kf_win_bg"
		img_down = "pvp_win_down"
	end
	if macth_info.win_side == -1 then
		img = "kf_pingju"
		img_down = "pvp_win_down"
	end
	self.node_list.img_flag_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(img))
	self.node_list.pvp_down.image:LoadSprite(ResPath.GetKf1V1(img_down))
	--print_error(macth_info.user_info_list)
	self.result_list:SetDataList(data)
end


----------------Kf3V3ResultItemRender-----------------------
Kf3V3ResultItemRender = Kf3V3ResultItemRender or BaseClass(BaseRender)
function Kf3V3ResultItemRender:__init()
	
end


--{index = 6, dead = 0, add_gongxun = 90, occupy = 1, sex = 0, prof = 3, assist = 0, obj_id = 65535, plat_type = 2, origin_score = 60, 
--add_score = 60, add_honor = 0, is_mvp = 1, role_id = 5244199, name = 欧阳寄山, kills = 0}
function Kf3V3ResultItemRender:OnFlush()
	if not self.data then
		return
	end
	self.node_list.label_name.text.text = self.data.name

	if self.data.index <= 3 then
		self.node_list.img_rank_bg.image:LoadSprite(ResPath.GetKf1V1("pvp_red_bg"))
		self.node_list.img_mvp.image:LoadSprite(ResPath.GetKf1V1("pvp_red_flag"))
	else
		self.node_list.img_rank_bg.image:LoadSprite(ResPath.GetKf1V1("pvp_blue_bg"))
		self.node_list.img_mvp.image:LoadSprite(ResPath.GetKf1V1("pvp_blue_flag"))
	end

	self.node_list.label_kill.text.text = self.data.kills .. "/" ..self.data.dead
	if self.data.add_score < 0 then
		self.node_list.label_jifen.text.text = self.data.origin_score.." (" .. self.data.add_score ..")"
	else
		self.node_list.label_jifen.text.text = self.data.origin_score.." (+" .. self.data.add_score ..")"
	end
	if self.data.is_win then
		self.node_list.is_win.image:LoadSprite(ResPath.GetKf1V1("pvp_result_"..self.data.is_win))
	end
	local macth_info = KuafuPVPWGData.Instance:GetPrepareInfo()
	if macth_info.win_side == -1 then
		self.node_list.is_win.image:LoadSprite(ResPath.GetKf1V1("pvp_result_3"))
	end
	self.node_list.label_gongxun.text.text = "+" .. self.data.add_gongxun
	self.node_list.label_assist.text.text = self.data.assist
	self.node_list.img_mvp:SetActive(self.data.is_mvp == 1)
end