HuMoBeTouchHintView = HuMoBeTouchHintView or BaseClass(SafeBaseView)

function HuMoBeTouchHintView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_behumo_tips_view")
end

function HuMoBeTouchHintView:ReleaseCallBack()
	self.friend_info = nil
	if nil ~= self.hint_alert then
		self.hint_alert:DeleteMe()
		self.hint_alert = nil
	end
end

function HuMoBeTouchHintView:LoadCallBack()
	self.node_list["btn_feiwen"].button:AddClickListener(BindTool.Bind1(self.OnClickFeiWen, self))
	self.node_list["btn_huimo"].button:AddClickListener(BindTool.Bind1(self.OnClickHuiMo, self))
end

function HuMoBeTouchHintView:ShowIndexCallBack()
	self:SetViewInfo()
end

function HuMoBeTouchHintView:OnFlush()
	self:SetViewInfo()
end

function HuMoBeTouchHintView:SetFriendInfo(data)
	self.friend_info = data
end

function HuMoBeTouchHintView:SetViewInfo()
	if self.friend_info then
		local name = self.friend_info.gamename
		local level = self.friend_info.level
		local humo_cfg = SocietyWGData.Instance:GetHuMoRewardInfo(level)
		if humo_cfg then
			--被摸奖励
			local be_reward = humo_cfg.touched_reward_item
			for i=1,2 do
				if be_reward[i - 1] then
					self.node_list["reward_"..i]:SetActive(true)
					local item_cfg = ItemWGData.Instance:GetItemConfig(be_reward[i - 1].item_id)
					if nil == item_cfg then
						return
					end
					--local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
					--self.node_list["reward_icon_"..i].image:LoadSpriteAsync(bundle, asset)
					self.node_list["reward_num_"..i].text.text = item_cfg.param1 * self.friend_info.touch_me_total_count
				else
					self.node_list["reward_"..i]:SetActive(false)
				end
			end

			local reward = humo_cfg.touch_reward_item
			if reward[0] then
				local item_cfg = ItemWGData.Instance:GetItemConfig(reward[0].item_id)
				if nil == item_cfg then
					return
				end
				--local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
				--self.node_list["reward_icon_3"].image:LoadSpriteAsync(bundle, asset)
				self.node_list["reward_num_3"].text.text = item_cfg.param1
			end

		end
		self.node_list["reward_hint"].text.text = string.format(Language.Society.HuMoTextHint,name)
	end

	if self.friend_info.today_touch_friend_flag == 1 then
		self.node_list["shengyu_humo_num"].text.text = string.format(Language.Society.DayYmHint)
		XUI.SetButtonEnabled(self.node_list["btn_huimo"], false)
	else
		-- local be_shengyu_num = SocietyWGData.Instance:GetDayBeTouchShengYuNum(self.friend_info.vip_level,self.friend_info.today_total_touched_count)
		local shengyu_num = SocietyWGData.Instance:GetDayTouchShengYuNum()
		local color = shengyu_num <= 0 and "#ff0000FF" or "#28e53aFF"
		--shengyu_num = ToColorStr(shengyu_num,color)
		self.node_list["shengyu_humo_num"].text.text = string.format(Language.Society.DayShengYuNum, color,shengyu_num)
		XUI.SetButtonEnabled(self.node_list["btn_huimo"], true)
	end
end

function HuMoBeTouchHintView:OnClickFeiWen()
	if nil == self.friend_info then
		return
	end
	local text = Language.Society.FeiWenInfo
	local len = string.len(text)
	--判断是否是是gm命令
	if len >= 6 and string.sub(text, 1 , 6) == "/jy_gm" then
		local blank_begin, blank_end = string.find(text, " ")
		local colon_begin, colon_end = string.find(text, ":")
		if blank_begin and blank_end and colon_begin and colon_end then
			local type = string.sub(text, blank_end + 1, colon_begin - 1)
			local command = string.sub(text, colon_end + 1, -1)
			SysMsgWGCtrl.SendGmCommand(type, command)
		end
		return
	elseif len >= 7 and string.sub(text, 1 , 7) == "/jy_cmd" then
		local blank_begin, blank_end = string.find(text, " ")
		if blank_begin and blank_end then
			ClientCmdWGCtrl.Instance:Cmd(string.sub(text, blank_end + 1, len))
		end
		return
	end

	if len <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end
	if len >= COMMON_CONSTS.MAX_CHAT_MSG_LEN then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.ContentToLong)
		return
	end

	if ChatWGData.ExamineEditText(text, 0) == false then return end

	-- 聊天内容检测
	local message = ChatWGData.Instance:FormattingMsg(text, CHAT_CONTENT_TYPE.TEXT)
	if "" == message then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Chat.NilContent)
		return
	end
	ChatWGCtrl.Instance:SendPrivateChatMsg(self.friend_info.user_id, message, CHAT_CONTENT_TYPE.TEXT, nil, false, true)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.FeiWenSucess)
	self:Close()
end

function HuMoBeTouchHintView:OnClickHuiMo()
	if self.friend_info then
		local be_shengyu_num = SocietyWGData.Instance:GetDayBeTouchShengYuNum(self.friend_info.vip_level,self.friend_info.today_total_touched_count)
		local shengyu_num = SocietyWGData.Instance:GetDayTouchShengYuNum()
		if self.friend_info.today_touch_friend_flag == 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.DayYmHint)
			return
		elseif be_shengyu_num <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.HuMoNumLimitHint)
			return
		elseif shengyu_num <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Society.HuMoNumLimitHint2)
			return
		else
			local total_exp = BiZuoWGData.Instance:GetTotalExp() or 0
			local other_cfg = SocietyWGData.Instance:GetHuMoOtherCfg()
			local limit_huoyue = other_cfg.daily_liveness_limit or 0
			if total_exp < limit_huoyue then
				self:NoCanHuMoHint(limit_huoyue)
				return
			end
		end

		SocietyWGCtrl.Instance:SendTouchOperReq(HUMO_TYPE.HM_FRIEND,self.friend_info.user_id)
		--请求成功后播放特效
		--TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_humo, is_success = true, pos = Vector2(0, 0)})
	end
	self:Close()
end

function HuMoBeTouchHintView:NoCanHuMoHint(limit_huoyue)
	if not self.hint_alert then
		self.hint_alert = Alert.New()
	end
	self.hint_alert:SetLableString(string.format(Language.Society.HuMoHuoYueLimint,limit_huoyue))
	self.hint_alert:SetOkFunc(function()
		ViewManager.Instance:Open(GuideModuleName.BiZuo,"bizuo_bizuo")
		self:Close()
	end)
	self.hint_alert:Open()
end