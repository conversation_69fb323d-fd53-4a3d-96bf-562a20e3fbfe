WuHunPrerogativeLibrary = WuHunPrerogativeLibrary or BaseClass(SafeBaseView)

function WuHunPrerogativeLibrary:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel",
		{ vector2 = Vector2(0, 0), sizeDelta = Vector2(750, 550) })
	self:AddViewResource(0, "uis/view/wuhunzhenshen_prefab", "layout_wuhun_prerogative_library")
end

function WuHunPrerogativeLibrary:ReleaseCallBack()
	if self.library_grid_list then
		self.library_grid_list:DeleteMe()
		self.library_grid_list = nil
	end
end

function WuHunPrerogativeLibrary:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.WuHunPrerogative.LibraryTitle
	local bundle = "uis/view/wuhunzhenshen_prefab"
	local asset = "wuhun_prerogative_library_cell"
	if self.library_grid_list == nil then
		self.library_grid_list = AsyncBaseGrid.New()
		self.library_grid_list:Create<PERSON>ells({
			col = 5,
			change_cells_num = 1,
			list_view = self.node_list["library_list"],
			assetBundle = bundle,
			assetName = asset,
			itemRender = WuHunPrerogativeLibraryItemRender
		})
		self.library_grid_list:SetStartZeroIndex(false)
	end
end

function WuHunPrerogativeLibrary:OnFlush(param_t)
	local list_data = WuHunWGData.Instance:GetWuhunPrerogativeDrawAllItemList()
	self.library_grid_list:SetDataList(list_data)
end

-----------------------------------WuHunPrerogativeLibraryItemRender-----------------------------------
WuHunPrerogativeLibraryItemRender = WuHunPrerogativeLibraryItemRender or BaseClass(BaseRender)

function WuHunPrerogativeLibraryItemRender:LoadCallBack()
	self.show_cell = ItemCell.New(self.node_list.item_pos)
end

function WuHunPrerogativeLibraryItemRender:__delete()
	if self.show_cell then
		self.show_cell:DeleteMe()
		self.show_cell = nil
	end
end

function WuHunPrerogativeLibraryItemRender:OnFlush()
	if not self.data then
		return
	end
	self.show_cell:SetData({ item_id = self.data.item.item_id, num = self.data.item.num })
	local color = ItemWGData.Instance:GetItemColor(self.data.item.item_id)
	local item_name = ItemWGData.Instance:GetItemName(self.data.item.item_id)
	self.node_list.name.text.text = ToColorStr(item_name, color)

	local cur_lv = WuHunWGData.Instance:GetWuhunPrerogativeLevel()
	self.node_list.mask:SetActive(cur_lv < self.data.min_level)

	if cur_lv < self.data.min_level then
		self.node_list.lv_text.text.text = string.format(Language.WuHunPrerogative.DrawLVText, self.data.min_level)
	end
end
