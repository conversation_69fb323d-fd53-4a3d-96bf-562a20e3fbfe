LandWarFbPersonBossInfoView = LandWarFbPersonBossInfoView or BaseClass(SafeBaseView)

function LandWarFbPersonBossInfoView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_land_war_fb_boss_info_view")
end

function LandWarFbPersonBossInfoView:SetDataAndOpen(data)
	self.show_data = data

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function LandWarFbPersonBossInfoView:LoadCallBack()
    if not self.left_nav_list then
        self.left_nav_list = AsyncListView.New(LandWarFbPersonBossInfoCell, self.node_list.left_nav_list)
        self.left_nav_list:SetStartZeroIndex(false)
        --self.left_nav_list:SetSelectCallBack(BindTool.Bind(self.OnSelectLeftNavHandler, self))
    end

    if not self.drop_item_list then
        self.drop_item_list = AsyncListView.New(ItemCell, self.node_list.drop_item_list)
        self.drop_item_list:SetStartZeroIndex(true)
    end

    if not self.oner_item_list then
        self.oner_item_list = AsyncListView.New(ItemCell, self.node_list.oner_item_list)
        self.oner_item_list:SetStartZeroIndex(true)
    end

    XUI.AddClickEventListener(self.node_list.btn_tip, BindTool.Bind(self.OnClickTipBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_tiaozhan, BindTool.Bind(self.OnClickTiaoZhanBtn, self))

    self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
    FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.LandWarFbPersonBossInfoView, self.get_guide_ui_event)
end

function LandWarFbPersonBossInfoView:ReleaseCallBack()
    if  self.left_nav_list then
        self.left_nav_list:DeleteMe()
        self.left_nav_list = nil
    end

    if self.drop_item_list then
        self.drop_item_list:DeleteMe()
        self.drop_item_list = nil
    end

    if self.oner_item_list then
        self.oner_item_list:DeleteMe()
        self.oner_item_list = nil
    end

    if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.LandWarFbPersonBossInfoView, self.get_guide_ui_event)
        self.get_guide_ui_event = nil
	end
end

function LandWarFbPersonBossInfoView:OnFlush()
    if IsEmptyTable(self.show_data) then
        return
    end

    self.node_list.title_view_name.text.text = self.show_data.land_name

    local nav_data_list = {}
    table.insert(nav_data_list, self.show_data)

    local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
    local monster_list = LandWarFbPersonWGData.Instance:GetCurStageMonsterListData(cur_stage + 1)
    if not IsEmptyTable(monster_list) then
        for k, v in pairs(monster_list) do
            table.insert(nav_data_list, v)
        end
    end

    self.left_nav_list:SetDataList(nav_data_list)

    self:FlushMidInfo()
end

function LandWarFbPersonBossInfoView:FlushMidInfo()
    self.node_list.desc_city_name.text.text = self.show_data.land_name
    --珍惜掉落
    self.drop_item_list:SetDataList(self.show_data.rare_drop_reward)
    -- 归属掉落
    self.oner_item_list:SetDataList(self.show_data.attribution_drop_reward)
end

function LandWarFbPersonBossInfoView:OnClickTipBtn()
    RuleTip.Instance:SetContent(Language.LandWarFbPersonView.InformationTipContent, Language.LandWarFbPersonView.InformationTipTitle)
end

function LandWarFbPersonBossInfoView:OnClickTiaoZhanBtn()
    if IsEmptyTable(self.show_data) then
        return
    end

    local cur_stage = LandWarFbPersonWGData.Instance:GetCurStage()
    local land_cfg = LandWarFbPersonWGData.Instance:GetCurStageLandCfg(cur_stage + 1)
    if not land_cfg then
        return
    end

    LandWarFbPersonWGCtrl.Instance:SendLandWarFBOperate(LAND_WAR_FB_OPERATE_TYPE.ENTER_SCENE, land_cfg.scene_id) 
    self:Close()
end

function LandWarFbPersonBossInfoView:GetGuideUiCallBack(ui_name, ui_param)
    if ui_name == "guide_1" then
        return self.node_list["guide_1"], BindTool.Bind1(self.OnClickTiaoZhanBtn, self)
    elseif ui_name == "guide_2" then
        return self.node_list["guide_2"], BindTool.Bind1(self.OnClickTiaoZhanBtn, self)
    elseif ui_name == "guide_3" then
        return self.node_list["guide_3"], BindTool.Bind1(self.OnClickTiaoZhanBtn, self)
    end

    return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end
---------------------------------LandWarFbPersonBossInfoCell-----------------------
LandWarFbPersonBossInfoCell = LandWarFbPersonBossInfoCell or BaseClass(BaseRender)

function LandWarFbPersonBossInfoCell:__delete()

end

function LandWarFbPersonBossInfoCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local is_city = self.data.land_icon ~= nil
    local bg_bundle, bg_asset

    if is_city then
        bg_bundle, bg_asset = ResPath.GetRawImagesPNG("a3_zdz_gkyq_1")
        self.node_list.area_name.text.text = self.data.land_name
    else
        bg_bundle, bg_asset = ResPath.GetRawImagesPNG("a3_zdz_gkyq_" .. self.data.bg)
        local boss_cfg = BossWGData.Instance:GetMonsterCfgByid(self.data.monster_id)
        local boss_name_str = ""
        if boss_cfg then
            boss_name_str = boss_cfg.name .. "  " .. string.format(Language.Common.Level1, boss_cfg.level)
        end

        self.node_list.boss_name.text.text = boss_name_str

        local sign_bundle, sign_asset = ResPath.GetPositionalWarfareImg("a3_zdz_bs_" .. self.data.sign)
        self.node_list.sign.image:LoadSprite(sign_bundle, sign_asset)
        self.node_list.desc_sign.text.text = Language.PositionalWarfare.InfomationBossSignName[self.data.sign]
    end

    self.node_list.bg.raw_image:LoadSprite(bg_bundle, bg_asset)
    self.node_list.boss_icon:CustomSetActive(not is_city)
    self.node_list.area_name:CustomSetActive(is_city)
    self.node_list.boss_name:CustomSetActive(not is_city)
    self.node_list.sign:CustomSetActive(not is_city)
    self.node_list.flag_no_owner:CustomSetActive(true)
end

function LandWarFbPersonBossInfoCell:OnSelectChange(is_select)
    self.node_list.select:CustomSetActive(is_select)
end