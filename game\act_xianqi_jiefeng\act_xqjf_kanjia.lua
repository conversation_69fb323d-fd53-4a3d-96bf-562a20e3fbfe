
function ActXianQiJieFengView:InitKanJia()
	-- local theme_cfg = ActXianQiJieFengWGData.Instance:GetActivityThemeCfg(TabIndex.xianqi_jiefeng_cap)

	self.my_cap = RoleWGData.Instance:GetMainRoleCap()
	self.node_list.cap_kj_cap.text.text = string.format(Language.XianQiJieFengAct.CapStr17, self.my_cap)
	self.kanjia_reward_list = AsyncListView.New(XQJFKanJiaItemRender, self.node_list.cap_kanjia_reward_list)
	self.node_list.cap_record_btn.button:AddClickListener(BindTool.Bind(self.OnClickCapRecordBtn, self))

	self:FluahKanJia()
end

function ActXianQiJieFengView:ReleaseKanJia()
	if self.kanjia_reward_list then
		self.kanjia_reward_list:DeleteMe()
		self.kanjia_reward_list = nil
	end
end

function ActXianQiJieFengView:FluahKanJia()
	local list = ActXianQiJieFengWGData.Instance:GetKanJiaList()
	if list and self.kanjia_reward_list then
		self.kanjia_reward_list:SetDataList(list)
	end
end

function ActXianQiJieFengView:OnClickCapRecordBtn()
	if GuildDataConst.GUILDVO.guild_id <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.XianQiJieFengAct.CapStr14)
		return
	end
	ViewManager.Instance:Open(GuideModuleName.BossAssist, TabIndex.kajia_xiezhu)
end

------------------------------------------------------------------

XQJFKanJiaItemRender = XQJFKanJiaItemRender or BaseClass(BaseRender)

function XQJFKanJiaItemRender:LoadCallBack()
	self.node_list.btn_buy.button:AddClickListener(BindTool.Bind(self.OnBuyBtnClick, self))
	self.node_list.btn_buy2.button:AddClickListener(BindTool.Bind(self.OnBuyBtnClick, self))
	self.node_list.btn_kanjia.button:AddClickListener(BindTool.Bind(self.OnKanJiaBtnClick, self))
	self.alert_window = Alert.New(nil, nil, nil, nil, true)
end

function XQJFKanJiaItemRender:__delete()
	if self.cell_list ~= nil then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
			v = nil
		end
	end

	if self.item_list ~= nil then
		self.item_list:DeleteMe()
		self.item_list = nil
	end

	if self.cap_item_count_down and CountDownManager.Instance:HasCountDown(self.cap_item_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.cap_item_count_down)
	end

	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
end

function XQJFKanJiaItemRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.dazhe:SetActive(self.data.zhekou < 1)
	if self.data.zhekou < 1 then
		local zekou = self.data.zhekou * 10
		if zekou % 1 == 0 then
			self.node_list.zhekou.image:LoadSprite(ResPath.GetXianQiJieFengImagePath("qfbz_kanjia_"..zekou))
    		self.node_list.zhekou.image:SetNativeSize()
		end
	end

	self.node_list.old_price.text.text = self.data.cfg.price
	self.node_list.new_price.text.text = math.floor(self.data.cfg.price * self.data.zhekou)
	self.node_list.old_cost_icon.image:LoadSprite(ResPath.GetF2CommonIcon(Shop_Money[self.data.cfg.price_type].url))
	self.node_list.new_cost_icon.image:LoadSprite(ResPath.GetF2CommonIcon(Shop_Money[self.data.cfg.price_type].url))

	self.node_list.name.text.text = self.data.cfg.libao_name
	self.node_list.cap_condition:SetActive(false)
	self.node_list.btn_ground:SetActive(false)
	self.node_list.yilingqu:SetActive(false)

	local is_cap_enougth = RoleWGData.Instance:GetMainRoleCap() >= self.data.cfg.zhanli_need

	if not is_cap_enougth then
		self.node_list.cap_condition:SetActive(true)
		self.node_list.cap_condition.text.text = string.format(Language.XianQiJieFengAct.CapStr8, self.data.cfg.zhanli_need)
	else

		if self.data.state == ActivityRewardState.YLQ then
			self.node_list.yilingqu:SetActive(true)
		else
			self.node_list.btn_ground:SetActive(true)
			self.node_list.btn_buy:SetActive(false)
			self.node_list.btn_buy2:SetActive(false)
			self.node_list.btn_kanjia:SetActive(false)
			if self.data.zhekou <= 0.1 then
				self.node_list.btn_buy:SetActive(true)
			else
				self.node_list.btn_buy2:SetActive(true)
				self.node_list.btn_kanjia:SetActive(true)
			end
		end
	end

	self:FlushItemList()
	self:CheckDownTime()
end

function XQJFKanJiaItemRender:FlushItemList()
	local list = {}
	for k,v in pairs(self.data.cfg.reward_item) do
		table.insert(list, v)
	end

	self.node_list.grid:SetActive(false)
	self.node_list.item_list:SetActive(false)
	local len = #list
	if len <= 2 then
		self.node_list.grid:SetActive(true)
		if self.cell_list == nil then
			self.cell_list = {}
			for i=1,2 do
				self.cell_list[i] = XQJFCommonItemRender.New(self.node_list["ph_cell_"..i].gameObject)
			end
		end

		for i=1,2 do
			if i <= len then
				self.node_list["ph_cell_"..i]:SetActive(true)
				self.cell_list[i]:SetItemData(list[i])
			else
				self.node_list["ph_cell_"..i]:SetActive(false)
			end
		end
	else
		if not self.item_list then
			self.item_list = AsyncListView.New(XQJFCommonItemRender, self.node_list.item_list)
		end
		self.node_list.item_list:SetActive(true)
		self.item_list:SetDataList(list)
	end
end

function XQJFKanJiaItemRender:CheckDownTime()
	local end_time = BossAssistWGData.Instance:GetKanJiaGiftInvalidTime(self.data.cfg.libao_item_id)
	if end_time ~= nil and end_time > TimeWGCtrl.Instance:GetServerTime() then
		XUI.SetButtonEnabled(self.node_list.btn_kanjia, false)
		self:CapTimeCountDown(end_time)
	else
		XUI.SetButtonEnabled(self.node_list.btn_kanjia, true)
		self.node_list.label.text.text = Language.XianQiJieFengAct.CapStr18
	end
end

function XQJFKanJiaItemRender:OnBuyBtnClick()
	if self.data.zhekou > 0.1 then
		self.alert_window:SetLableString(Language.XianQiJieFengAct.CapStr11)
		self.alert_window:SetOkFunc(function ()
			ActXianQiJieFengWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_ZHANLIBIPIN, ZHANLIBIPIN_OP_TYPE.TYPE_ZHANLILIBAO, self.data.cfg.libao_item_id)
		end)
		self.alert_window:Open()
		return
	else
		ActXianQiJieFengWGCtrl.Instance:SendActivityRewardOp(ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_ZHANLIBIPIN, ZHANLIBIPIN_OP_TYPE.TYPE_ZHANLILIBAO, self.data.cfg.libao_item_id)
	end
end

function XQJFKanJiaItemRender:OnKanJiaBtnClick()
	if GuildDataConst.GUILDVO.guild_id > 0 then
		BossAssistWGCtrl.Instance:CSRoleOpKanJiaItem(KANJIA_OP_TYPE.TYPE_FAQI_KANJIA, self.data.cfg.libao_item_id, RoleWGData.Instance:GetRoleVo().role_id, self.data.cfg.price_type, self.data.cfg.price)
		local other_cfg = ConfigManager.Instance:GetAutoConfig("kanjiaconfig_auto").other
		if other_cfg ~= nil then
			XUI.SetButtonEnabled(self.node_list.btn_kanjia, false)
			self:CheckDownTime()
			ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, "{openLink;530}", CHAT_CONTENT_TYPE.TEXT)
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.XianQiJieFengAct.CapStr14)
	end
end

--有效时间倒计时
function XQJFKanJiaItemRender:CapTimeCountDown(end_time)
	if self.cap_item_count_down and CountDownManager.Instance:HasCountDown(self.cap_item_count_down) then
		CountDownManager.Instance:RemoveCountDown(self.cap_item_count_down)
	end

	self.cap_item_count_down = "cap_item_count_down" .. self.data.cfg.libao_item_id
	self.node_list.label.text.text = TimeUtil.FormatSecondDHM2(end_time - TimeWGCtrl.Instance:GetServerTime())
	CountDownManager.Instance:AddCountDown(self.cap_item_count_down, BindTool.Bind1(self.UpdateItemCapCountDown, self), BindTool.Bind1(self.OnCapItemComPlete, self), end_time, nil, 1)
end

function XQJFKanJiaItemRender:UpdateItemCapCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self.node_list.label.text.text = TimeUtil.FormatSecondDHM2(valid_time)
	end
end

function XQJFKanJiaItemRender:OnCapItemComPlete()
	self.node_list.label.text.text = Language.XianQiJieFengAct.CapStr18
	XUI.SetButtonEnabled(self.node_list.btn_kanjia, true)
end
