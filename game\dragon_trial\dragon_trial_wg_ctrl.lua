require("game/dragon_trial/dragon_trial_wg_data")
require("game/dragon_trial/dragon_trial_view")
require("game/dragon_trial/dragon_trial_task_view")
require("game/dragon_trial/dragon_trial_jiesuan_view")

-- 龙魂试炼
DragonTrialWGCtrl = DragonTrialWGCtrl or BaseClass(BaseWGCtrl)

function DragonTrialWGCtrl:__init()
	if DragonTrialWGCtrl.Instance ~= nil then
		ErrorLog("[DragonTrialWGCtrl] attempt to create singleton twice!")
		return
	end
	DragonTrialWGCtrl.Instance = self

    self.data = DragonTrialWGData.New()
    self.view = DragonTrialView.New(GuideModuleName.DragonTrialView)
    self.dragon_trial_task_view = DragonTrialTaskView.New(GuideModuleName.DragonTrialTaskView)
    self.dragon_trial_jiesuan_view = DragonTrialJieSuanView.New(GuideModuleName.DragonTrialJieSuanView)

    self:RegisterAllProtocols()

	-- 活动改变
	self.act_change = BindTool.Bind(self.CheckActicityOpen, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)

    self.role_data_change_callback = BindTool.Bind1(self.RoleDataChangeCallback, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"capability", "level"})
end

function DragonTrialWGCtrl:__delete()
    if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

    if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

    if self.dragon_trial_task_view then
		self.dragon_trial_task_view:DeleteMe()
		self.dragon_trial_task_view = nil
	end

    if self.dragon_trial_jiesuan_view then
		self.dragon_trial_jiesuan_view:DeleteMe()
		self.dragon_trial_jiesuan_view = nil
	end

    -- 注销活动改变监听
    if self.act_change then
        ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
        self.act_change = nil
    end

    RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)

    DragonTrialWGCtrl.Instance = nil
end

function DragonTrialWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCOADragonTrialInfo, "OnSCOADragonTrialInfo")
    self:RegisterProtocol(SCOADragonTrialSceneInfo, "OnSCOADragonTrialSceneInfo")
end

-----------------------------------------龙神试炼-------------------------------------
function DragonTrialWGCtrl:RoleDataChangeCallback(attr_name, value, old_value)
	if attr_name == "capability" then				--战斗力
		RemindManager.Instance:Fire(RemindName.DragonTrial)
	elseif attr_name == "level" then
		RemindManager.Instance:Fire(RemindName.DragonTrial)
	end
end

-- 龙神试炼操作请求
function DragonTrialWGCtrl:SendDragonTrialOperate(opera_type, param1, param2, param3)
	-- print_error("FFFF=====龙神试炼活动请求 activity_type", ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL, opera_type, param1, param2, param3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
    protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL
    protocol.opera_type = opera_type or 0
    protocol.param_1 = param1 or 0
    protocol.param_2 = param2 or 0
    protocol.param_3 = param3 or 0
    protocol:EncodeAndSend()
end

-- 龙神试炼信息
function DragonTrialWGCtrl:OnSCOADragonTrialInfo(protocol)
	-- print_error("龙神试炼信息", protocol)
	self.data:SetDragonTrialInfo(protocol)
    RemindManager.Instance:Fire(RemindName.DragonTrial)
	ViewManager.Instance:FlushView(GuideModuleName.DragonTrialView)
    
    local cur_pass_seq = DragonTrialWGData.Instance:GetCurrentPassSeq()
    local level_cfg = DragonTrialWGData.Instance:GetLevelMonsterCfgList()
    if level_cfg and cur_pass_seq >= #level_cfg then
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL, ACTIVITY_STATUS.CLOSE)
    end
end

-- 龙神试炼场景信息
function DragonTrialWGCtrl:OnSCOADragonTrialSceneInfo(protocol)
    -- print_error("龙神试炼场景信息", protocol)
	self.data:SetDragonTrialSceneInfo(protocol)
	self:FlushDragonTrialTaskView()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)

	if protocol then
		self:CheckDragonTrialResult(protocol.is_end, protocol.is_pass, protocol.seq)
        -- if protocol.is_end ~= 1 then
        --     self:SceneEnterCallback()
        -- end
	end
end

-- 进入场景回调
function DragonTrialWGCtrl:SceneEnterCallback()
	local scene_info = self.data:GetDragonTrialSceneInfo()
	if scene_info then
		UiInstanceMgr.Instance:DoFBStartDown(TimeWGCtrl.Instance:GetServerTime() + 5, function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			MainuiWGCtrl.Instance:SetFbIconEndCountDown(scene_info.fb_end_time)
		end)
	end
end

-- 龙魂试炼场景结算(一键直达不走这个副本)
function DragonTrialWGCtrl:CheckDragonTrialResult(is_end, is_pass, seq)
	-- print_error("龙魂试炼场景结算(一键直达不走这个副本)", is_end, is_pass, seq)
	if is_end == 1 then
		-- MainuiWGCtrl.Instance:RemoveLevelTimeCountDown()

		if is_pass == 1 then	--- 挑战胜利
			self:OpenDragonTrialJieSuanView(true, true, seq)
		else					--- 挑战失败
			-- self:OpenDragonTrialJieSuanView(false, true, seq)
			FuBenWGCtrl.Instance:OpenLose(Scene.Instance:GetSceneType())
		end
	end
end

-- 龙魂试炼场景结算(一键直达)
function DragonTrialWGCtrl:QuickFinishDragonTrialTower(result, start_seq, seq)
	-- print_error("龙魂试炼场景结算(一键直达)", result, start_seq, seq)
	if result == 1 then	--- 挑战胜利
		self:OpenDragonTrialJieSuanView(true, false, seq, start_seq)
	end
end

-- 挑战副本
function DragonTrialWGCtrl:ClickDragonTrialGoTo()
	if self.view:IsOpen() then
		self.view:OnClickChallengeBtn()
	end
end

-- 打开副本场景面板
function DragonTrialWGCtrl:OpenDragonTrialTaskView()
	if not self.dragon_trial_task_view:IsOpen() then
		self.dragon_trial_task_view:Open()
	end
end

-- 刷新副本场景面板
function DragonTrialWGCtrl:FlushDragonTrialTaskView()
	if self.dragon_trial_task_view:IsOpen() then
		self.dragon_trial_task_view:Flush()
	end
end

-- 关闭副本场景面板
function DragonTrialWGCtrl:CloseDragonTrialTaskView()
	if self.dragon_trial_task_view:IsOpen() then
		self.dragon_trial_task_view:Close()
	end
end

-- 打开武魂塔副本结算界面
-- param: 是否表示是单个通关
function DragonTrialWGCtrl:OpenDragonTrialJieSuanView(is_win, is_once, seq, start_seq)
	local show_data = {}
	show_data.is_win = is_win
	show_data.is_once = is_once
	show_data.seq = seq
	show_data.start_seq = start_seq

	self.dragon_trial_jiesuan_view:SetShowData(show_data)

	if not self.dragon_trial_jiesuan_view:IsOpen() then
		self.dragon_trial_jiesuan_view:Open()
	else
		self.dragon_trial_jiesuan_view:Flush()
	end
end

-- 检查是否开启
function DragonTrialWGCtrl:CheckActicityOpen(activity_type, status, next_time, open_type)
    if activity_type == ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL and status == ACTIVITY_STATUS.OPEN then
        local cur_pass_seq = DragonTrialWGData.Instance:GetCurrentPassSeq()
        local level_cfg = DragonTrialWGData.Instance:GetLevelMonsterCfgList()
        if level_cfg and cur_pass_seq >= #level_cfg then
            ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_DRAGON_TRIAL, ACTIVITY_STATUS.CLOSE)
        end
    end
end