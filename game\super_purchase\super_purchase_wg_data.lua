SuperPurchaseWGData = SuperPurchaseWGData or BaseClass()

function SuperPurchaseWGData:__init()
	if SuperPurchaseWGData.Instance ~= nil then
		ErrorLog("[SuperPurchaseWGData] attempt to create singleton twice!")
		return
	end

	SuperPurchaseWGData.Instance = self

	self:InitCfg()
	self.grade = 1 -- 档次
	self.rmb_buy_times_list = {}
end

function SuperPurchaseWGData:__delete()
	SuperPurchaseWGData.Instance = nil
	self.grade = nil
	self.rmb_buy_times_list = nil
end

function SuperPurchaseWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_limit_rmb_buy3_auto")
	self.rmb_buy_seq_cfg = ListToMapList(cfg.rmb_buy, "grade", "activity_day")
end

function SuperPurchaseWGData:SetAllBuyInfo(protocol)
	self.grade = protocol.grade
	self.rmb_buy_times_list = protocol.rmb_buy_times_list
end

function SuperPurchaseWGData:GetCurDayBuyList()
	local cur_grade_cfg = self.rmb_buy_seq_cfg[self.grade]
	if not IsEmptyTable(cur_grade_cfg) then
		local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY3)
		if cur_grade_cfg[act_day] then
			return cur_grade_cfg[act_day]
		else
			print("===========配置天数不存在day",act_day)
			return {}
		end
	else
		print("===========配置档次不存在self.grade=",self.grade)
		return {}
	end
end

function SuperPurchaseWGData:GetGradeRewardList()
	local data_list = {}
	local data_cfg = self:GetCurDayBuyList()
	if data_cfg == nil then
		return data_list
	end

	for i = 1, 3 do
		table.insert(data_list, data_cfg[i])
	end

	return data_list
end

--获取一键购买的配置
function SuperPurchaseWGData:GetGradeAllBuyCfg()
	local data_cfg = self:GetCurDayBuyList()
	if data_cfg == nil then
		return {}
	end

	for k, v in pairs(data_cfg) do
		if v.is_all_buy == 1 then
			return v
		end
	end
end

function SuperPurchaseWGData:GetRewardListCfg(index)
	local data_list = self:GetGradeRewardList()
	return data_list and data_list[index] or {}
end

function SuperPurchaseWGData:GetBuyCountBySeq(seq)
	return self.rmb_buy_times_list[seq] or 0
end

-- 一键购买总价格
-- function SuperPurchaseWGData:GetAllBuyTimesPrice()
-- 	local total_price = 0
-- 	local reward_list = self:GetGradeRewardList()
-- 	if reward_list == nil then
-- 		return total_price
-- 	end
	
-- 	for k, v in pairs(reward_list) do
-- 		-- local already_buy_times = self:GetBuyCountBySeq(v.seq)
-- 		-- local times = v.buy_times - already_buy_times

-- 		local price = RoleWGData.GetPayMoneyChange(v.rmb_price, v.rmb_type, v.rmb_seq)
-- 		total_price = total_price + price * v.buy_times
-- 	end
	
-- 	return total_price
-- end

function SuperPurchaseWGData:GetBuyStateBySeq(index)
	local reward_cfg = self:GetRewardListCfg(index)
	if reward_cfg == nil then
		return false
	end
	
	local pro_buy_times = self.rmb_buy_times_list[reward_cfg.seq] or 0
	return reward_cfg.buy_times <= pro_buy_times
end

--是否全部购买完
function SuperPurchaseWGData:GetAllBuyStateBySeq()
	local all_buy_cfg = self:GetGradeAllBuyCfg()
	if all_buy_cfg == nil then
		return false
	end

	-- for i = 1, 3 do
	-- 	if not self:GetBuyStateBySeq(i) then
	-- 		return false
	-- 	end
	-- end

	return self:GetBuyCountBySeq(all_buy_cfg.seq) > 0
end

--是否购买过
function SuperPurchaseWGData:GetRewardIsBuy()
	for i = 0, 2 do
		if self:GetBuyCountBySeq(i) > 0 then
			return true
		end
	end

	return false
end
