﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class EnhancedUI_EnhancedScroller_EnhancedScrollerCellViewWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(EnhancedUI.EnhancedScroller.EnhancedScrollerCellView), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>("RefreshCellView", RefreshCellView);
		<PERSON><PERSON>unction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		<PERSON>.Reg<PERSON>ar("cellIdentifier", get_cellIdentifier, set_cellIdentifier);
		L.<PERSON>("cellIndex", get_cellIndex, set_cellIndex);
		L.<PERSON>ar("dataIndex", get_dataIndex, set_dataIndex);
		<PERSON><PERSON>("active", get_active, set_active);
		<PERSON><PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshCellView(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScrollerCellView>(L, 1);
			obj.RefreshCellView();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cellIdentifier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)o;
			string ret = obj.cellIdentifier;
			LuaDLL.lua_pushstring(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellIdentifier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cellIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)o;
			int ret = obj.cellIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_dataIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)o;
			int ret = obj.dataIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index dataIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_active(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)o;
			bool ret = obj.active;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index active on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cellIdentifier(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)o;
			string arg0 = ToLua.CheckString(L, 2);
			obj.cellIdentifier = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellIdentifier on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cellIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.cellIndex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_dataIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.dataIndex = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index dataIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_active(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView obj = (EnhancedUI.EnhancedScroller.EnhancedScrollerCellView)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.active = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index active on a nil value");
		}
	}
}

