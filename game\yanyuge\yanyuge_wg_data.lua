-- /jy_gm CangJinShangPuGmOperate:999 1000 0 0  加积分

--/jy_cmd gmopenview YanYuGePrivilegeView

YanYuGeWGData = YanYuGeWGData or BaseClass(BaseWGCtrl)
function YanYuGeWGData:__init()
	if YanYuGeWGData.Instance then
		ErrorLog("[YanYuGeWGData] Attemp to create a singleton twice !")
	end
	YanYuGeWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("cangjinshangpu_cfg_auto")
    self.week_show_cfg = ListToMap(cfg.week, "week", "week_day")
    self.init_score_cfg = cfg.init_score
    self.extra_score_cfg = ListToMap(cfg.extra_score, "week", "week_day", "seq")
    self.other_cfg = cfg.other[1]
    self.tequan_cfg = cfg.tequan
    self.yytq_tooltip_cfg = ListToMap(cfg.yytq_tooltip, "type", "index")
    self.limit_shop_cfg = cfg.limit_shop
    self.suit_shop_cfg = ListToMap(cfg.suit_shop, "grade", "seq")
    self.choose_attr_cfg = cfg.choose_attr
    self.onekey_tequan_cfg = cfg.onekey_tequan

    self.show_model_cfg = ListToMapList(cfg.show_model, "seq")
    self.show_model_theme_cfg = ListToMap(cfg.show_model_theme, "seq")

    self.nobility_level_cfg = ListToMap(cfg.nobility_level, "level")
    self.nobility_task_cfg = ListToMap(cfg.nobility_task, "seq")
    self.nobility_task_only_cfg = ListToMap(cfg.nobility_task, "only_seq")
    self.open_task_cfg = ListToMap(cfg.open_task, "ID")

    self.invest_week = 1    -- 投资周期
	self.already_invest_num = 0  --已投资数
	self.invest_limit = 0  --投资额度
	self.invest_interest = 0  --投资利息
	self.invest_task_flag = {}  -- 投资任务完成标记
	self.active_tequan_reward_flag = {}  --激活特权奖励标记

    self.score = 0           -- 积分
    self.tequan_flag = {}
    self.normal_tequan_cfg = {}
    self.nezha_tequan_cfg = {}
    self.zanzhu_tequan_cfg = {}

    self.cur_grade = 0
    -- self.limit_shop_flag = {}
    self.limit_shop = {}
    self.refresh_limit_count = 0
    self.limit_shop_refresh_count = 0

    self.suit_shop = {}

    self.tequan_show_list = {}
	self.choose_left_attr = {}
	self.choose_right_attr = {}
    self.tequan_every_day_reward = {}
	self.tequan_choose_attr1_flag = {}
	self.tequan_choose_attr2_flag = {}
    self.tequan_active_reward_flag = {}

    self.real_recharge_num = 0   --真充数
    self.nobility_level = -1
    self.nobility_exp = 0
    self.nobility_daily_add_score = 0
    self.nobility_task = {}

    self:InitCfgCache()

    RemindManager.Instance:Register(RemindName.YanYuGe_Privilege_WLTZ, BindTool.Bind(self.GetPrivilegeWLTZRedPoint, self))
    RemindManager.Instance:Register(RemindName.YanYuGe_Privilege_YYTQ, BindTool.Bind(self.GetPrivilegYYTQRedPoint, self))
    RemindManager.Instance:Register(RemindName.YanYuGe_Privilege_NZTQ, BindTool.Bind(self.GetPrivilegeNZTQRedPoint, self))
    -- RemindManager.Instance:Register(RemindName.YanYuGe_Privilege_ZZTQ, BindTool.Bind(self.GetPrivilegeZZTQRedPoint, self))
    RemindManager.Instance:Register(RemindName.YanYuGe_Shop_NWSD, BindTool.Bind(self.GetShopNWSDRedPoint, self))
    -- RemindManager.Instance:Register(RemindName.YanYuGe_Shop_TZSD, BindTool.Bind(self.GetShopTZSDRedPoint, self))
    RemindManager.Instance:Register(RemindName.YanYuGe_Noble_Privilege, BindTool.Bind(self.GetNoblePrivilegeRedPoint, self))
end

function YanYuGeWGData:__delete()
	YanYuGeWGData.Instance = nil

    RemindManager.Instance:UnRegister(RemindName.YanYuGe_Privilege_WLTZ)
    RemindManager.Instance:UnRegister(RemindName.YanYuGe_Privilege_NZTQ)
    RemindManager.Instance:UnRegister(RemindName.YanYuGe_Privilege_YYTQ)
    RemindManager.Instance:UnRegister(RemindName.YanYuGe_Shop_NWSD)
    -- RemindManager.Instance:UnRegister(RemindName.YanYuGe_Shop_TZSD)
end

function YanYuGeWGData:InitCfgCache()
    for k, v in pairs(self.tequan_cfg) do
        if v.privilege_type == YANYUGE_TEQUAN_TYPE.NOEMAL then
            table.insert(self.normal_tequan_cfg, v)
        elseif v.privilege_type == YANYUGE_TEQUAN_TYPE.NEZHA then
            self.nezha_tequan_cfg = v
        else
            table.insert(self.zanzhu_tequan_cfg, v)
        end
    end
end

---------------------------------------------REMIND_START----------------------------------------------

function YanYuGeWGData:GetPrivilegeWLTZRedPoint()
    local cur_invest_week = self:GetInvestWeek()
    local cfg_list = self:GetWeekShowCfgByWeek(cur_invest_week)

    if IsEmptyTable(cfg_list) then
        return 0
    end

    if self:IsCanGetTouZiMoney() then
        return 1
    end

    return 0
end

function YanYuGeWGData:GetPrivilegYYTQRedPoint()
    local tequan_list = self:GetNormalTeQuanCfg()
    local score = self:GetCurScore()
    local is_get_all_privilege = true

    if not IsEmptyTable(tequan_list) then
        for k, v in pairs(tequan_list) do
            if self:IsTeQuanUnLockBySeq(v.seq) then
                if not self:IsGetTequanReward(v.seq) then
                    return 1
                end

                if self:IsTequanHasDayReward(v.seq) and not self:IsGetTeQuanEveryDayReward(v.seq) then
                    return 1
                end
            else
                is_get_all_privilege = false
            end

            -- if not self:IsTeQuanUnLockBySeq(v.seq) then
            --     is_get_all_privilege = false

            --     if v.last_seq < 0 or self:IsTeQuanUnLockBySeq(v.last_seq) then
            --         if v.buy_type == 2 and v.price <= score then
            --             return 1
            --         end
            --     end
            -- end
        end
    end

    if is_get_all_privilege then
        if not self:IsGetTeQuanReward() then
            return 1
        end
    end

    return 0
end

function YanYuGeWGData:GetPrivilegeNZTQRedPoint()
    local tequan_cfg = self:GetNeZhaTeQuanCfg()
    if IsEmptyTable(tequan_cfg) then
        return 0
    end

    -- if not self:IsTeQuanUnLockBySeq(tequan_cfg.seq) then
    --     if tequan_cfg.last_seq < 0 or self:IsTeQuanUnLockBySeq(tequan_cfg.last_seq) then
    --         local score = self:GetCurScore()

    --         if tequan_cfg.buy_type == 2 and tequan_cfg.price <= score then
    --             return 1
    --         end
    --     end
    -- end

    if self:IsTeQuanUnLockBySeq(tequan_cfg.seq) then
        if not self:IsGetTequanReward(tequan_cfg.seq) then
            return 1
        end

        if self:IsTequanHasDayReward(tequan_cfg.seq) and not self:IsGetTeQuanEveryDayReward(tequan_cfg.seq) then
            return 1
        end
    end

    return 0
end

-- function YanYuGeWGData:GetPrivilegeZZTQRedPoint()
--     local tequan_list = self:GetZZTQDataList()
--     local score = self:GetCurScore()

--     if not IsEmptyTable(tequan_list) then
--         for k, v in pairs(tequan_list) do
--             if self:IsTeQuanUnLockBySeq(v.seq) then
--                 if not self:IsGetTeQuanEveryDayReward(v.seq) then
--                     return 1
--                 end
--             end
--         end

--         -- for k, v in pairs(tequan_list) do
--         --     if not self:IsTeQuanUnLockBySeq(v.seq) then
--         --         if v.last_seq < 0 or self:IsTeQuanUnLockBySeq(v.last_seq) then
--         --             if v.buy_type == 2 and v.price <= score then
--         --                 return 1
--         --             end
--         --         end
--         --     end
--         -- end
--     end

--     return 0
-- end

function YanYuGeWGData:GetNoblePrivilegeRedPoint()
    if not FunOpen.Instance:GetFunIsOpened(FunName.YanYuGeNobleView) then
        return 0
    end
    local is_show_open_server = self:GetYYGZIsCanShowOpenServerTask()
    local cur_batch = self:GetOpenServerTaskCurBatch()
    for k, v in pairs(self:GetNobilityTaskCfgOnlySeq()) do
        local info = self:GetNobilityTaskBySeq(v.seq)
        local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        if not IsEmptyTable(info) and (open_day >= v.open_day and open_day <= v.end_day) then
            if is_show_open_server then
                if v.show_type == YANYUGE_NOBLE_SHOW_TYPE.OPEN_SERVER and (v.show_batch == "" or v.show_batch == cur_batch)
                    and (info.progress >= v.param2) and ((v.complete_limit <= 0) or (info.complete_count < v.complete_limit)) then
                    return 1
                end
            else
                if v.show_type ~= YANYUGE_NOBLE_SHOW_TYPE.OPEN_SERVER and (info.progress >= v.param2) and
                    ((v.complete_limit <= 0) or (info.complete_count < v.complete_limit)) then
                    return 1
                end
            end
        end
    end

    return 0
end

function YanYuGeWGData:GetShopNWSDRedPoint()
    -- local score = self:GetCurScore()
    -- if score < 0 then
    --     return 0
    -- end
 
    -- local show_data_list = self:GetNWSDGridDataList()
    -- if not IsEmptyTable(show_data_list) then
    --     for k, v in pairs(show_data_list) do
    --         local refresh_type = v.cfg.refresh_type or 0
        
    --         if refresh_type ~= 0 then
    --             local remain_times = v.cfg.buy_limit - v.buy_times
    --             if remain_times > 0 and  score >= v.cfg.consume_score then
    --                 return 1
    --             end
    --         else
    --             if score >= v.cfg.consume_score then
    --                 return 1
    --             end
    --         end
    --     end
    -- end

    return 0
end

-- function YanYuGeWGData:GetShopTZSDRedPoint()
--     -- local score = self:GetCurScore()
--     -- local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
--     -- local cur_suit_data_list = self:GetCurSuitShopDataList()

--     -- if not IsEmptyTable(cur_suit_data_list) then
--     --     for k, v in pairs(cur_suit_data_list) do
--     --         if server_day >= v.min_open_server_day then
--     --             local buy_times = self.suit_shop[v.seq] or 0
--     --             local buy_limit = v.buy_limit
                
--     --             if v.buy_type == 2 and buy_limit > buy_times and score >= v.price then
--     --                 return 1
--     --             end
--     --         end
--     --     end 
--     -- end

--     return 0
-- end
---------------------------------------------REMIND_END------------------------------------------------

---------------------------------------------PROTOCOL_START--------------------------------------------
function YanYuGeWGData:SetCangJingShangPuInvestInfo(protocol)
    self.invest_week = protocol.invest_week    -- 投资周期
	self.already_invest_num = protocol.already_invest_num  --已投资数(总本金)         总的钱 * 利息 = 显示的累计
	self.invest_limit = protocol.invest_limit  -- 当天的限制额度投资额度              
	self.invest_interest = protocol.invest_interest  --投资利息           总的利息   减去配置 = 额外的利息         其他表 初始利息
	self.invest_task_flag = bit:d2b_l2h(protocol.invest_task_flag, nil, true)  -- 投资任务完成标记
	self.active_tequan_reward_flag = protocol.active_tequan_reward_flag  --激活特权奖励标记  0 / 1完成  戳一戳 标记
    self.can_fetch_invest_flag = protocol.can_fetch_invest_flag          -- 是否可以领取投资标记  0不可领取  1能领取  2已领取 
end

function YanYuGeWGData:SetCangJinShopAllInfo(protocol)
	self.score = protocol.score
    self.tequan_flag = protocol.tequan_flag
    self.tequan_every_day_reward = protocol.tequan_every_day_reward
	self.tequan_choose_attr1_flag = protocol.tequan_choose_attr1_flag
	self.tequan_choose_attr2_flag = protocol.tequan_choose_attr2_flag
    self.suit_shop = protocol.suit_shop
    -- self.limit_shop_flag = protocol.limit_shop_flag
    self.refresh_limit_count = protocol.refresh_limit_count
    self.limit_shop = protocol.limit_shop

	self.cur_grade = protocol.grade
    self.limit_shop_refresh_count = protocol.limit_shop_refresh_count
    self.real_recharge_num = protocol.real_recharge_num
    self.tequan_active_reward_flag = protocol.tequan_active_reward_flag

    self:UpdataChooseAtrrList()
	self:UpdataShowTeQuanList()
end

function YanYuGeWGData:SuitShopChange(protocol)
    self.suit_shop = protocol.suit_shop
end

function YanYuGeWGData:IsGetTeQuanEveryDayReward(seq)
    return self.tequan_every_day_reward[seq] == 1
end

function YanYuGeWGData:LimitShopInfoChange(protocol)
    -- self.limit_shop_flag = protocol.limit_shop_flag
    self.refresh_limit_count = protocol.refresh_limit_count
    self.limit_shop = protocol.limit_shop
    self.limit_shop_refresh_count = protocol.limit_shop_refresh_count
end

function YanYuGeWGData:TeQuanInfoChange(protocol)
    self.tequan_flag = protocol.tequan_flag
    self.tequan_every_day_reward = protocol.tequan_every_day_reward
	self.tequan_choose_attr1_flag = protocol.tequan_choose_attr1_flag
	self.tequan_choose_attr2_flag = protocol.tequan_choose_attr2_flag
    self.tequan_active_reward_flag = protocol.tequan_active_reward_flag

    self:UpdataChooseAtrrList()
	self:UpdataShowTeQuanList()
end

function YanYuGeWGData:ScoreInfoChange(protocol)
	self.score = protocol.score
end

function YanYuGeWGData:SetConvertInfo(protocol)
    
end

function YanYuGeWGData:SetCangJingShangPuNobilityInfo(protocol)
    self.nobility_level = protocol.nobility_level
    self.nobility_exp = protocol.nobility_exp
    self.nobility_daily_add_score = protocol.nobility_daily_add_score
    self.nobility_task = protocol.nobility_task
end

function YanYuGeWGData:CangJingShangPuNobilityTaskUpdate(protocol)
    self.nobility_task[protocol.seq] = protocol.nobility_task
end

function YanYuGeWGData:GetInvestWeek() 
    return self.invest_week
end

function YanYuGeWGData:GetAlreadyInvestNum()
    return self.already_invest_num
end

function YanYuGeWGData:GetInvestLimit()
    return self.invest_limit
end

-- 当日累计的钱  = 当日投入的钱 * 总利息(万分比)
function YanYuGeWGData:GetCurDayTotalQuota()
    return math.floor(self.already_invest_num  * (1 + self.invest_interest / 10000))
end

function YanYuGeWGData:GetInvestInterest()
    return self.invest_interest
end

function YanYuGeWGData:GetCurScore()
	return self.score
end

function YanYuGeWGData:IsTeQuanUnLockBySeq(seq)
    return self.tequan_flag[seq] == 1
end

--赞助特权是否全部解锁
function YanYuGeWGData:IsAllZanZhuTeQuanUnLock()
    local cfg_list = self.zanzhu_tequan_cfg
    for k, v in pairs(cfg_list) do
        if self.tequan_flag[v.seq] ~= 1 then
            return false
        end
    end

    return true
end

function YanYuGeWGData:IsGetTeQuanReward()
    return self.active_tequan_reward_flag == 1
end

function YanYuGeWGData:UpdataShowTeQuanList()
	local tequan_list = {}
	local already_choose_attr = self:IsAlreadyChooseAttr()
	for k, v in pairs(self.tequan_cfg) do
		local data = {}
		local seq = v.seq
		data.seq = seq
		data.cfg = v
		data.act_flag = self:GetTeQuanActFlag(seq)
		data.day_reward_flag = self.tequan_every_day_reward[seq] or 0
		local is_can_get = v.is_can_get or 0
		data.reward_red = data.act_flag == 1 and is_can_get == 1 and data.day_reward_flag == 0
		data.attr_red = data.act_flag == 1 and v.is_can_choose == 1 and (not already_choose_attr)
		data.remind = data.reward_red or data.attr_red
		table.insert(tequan_list, data)
	end

	if not IsEmptyTable(tequan_list) then
		table.sort(tequan_list, SortTools.KeyLowerSorters("seq"))
	end

	self.tequan_show_list = tequan_list
end

function YanYuGeWGData:GetShowTeQuanList()
	return self.tequan_show_list
end

function YanYuGeWGData:GetTeQuanActFlag(seq)
	return self.tequan_flag[seq] or 0
end

function YanYuGeWGData:GetRealRechargeNum()
    return self.real_recharge_num
end

function YanYuGeWGData:GetNobilityLevel()
    return self.nobility_level
end

function YanYuGeWGData:GetNobilityExp()
    return self.nobility_exp
end

function YanYuGeWGData:GetNobilityDailyAddScore()
    return self.nobility_daily_add_score
end

function YanYuGeWGData:GetNobilityTask()
    return self.nobility_task
end

function YanYuGeWGData:GetNobilityTaskBySeq(seq)
    return self.nobility_task[seq]
end

function YanYuGeWGData:IsGetTequanReward(seq)
    return self.tequan_active_reward_flag[seq] == 1
end

-- 今日可获得 = 额外积分表 get_type = 2  reward相加        多领 = （初始积分表init_score） * 额外利息（今日可获得 前面）

-- 存入
-- 今日存额  =  初始积分 的 init_score            + = 额外积分表   get_type = 1 的 reward （完成的任务）的相加

-- 今日可提升 =  所有 额外积分表   get_type = 1 的 reward 的相加

-- 提升额度： 文本固定   额外积分表 task_type = 1 param1 


-- 弹窗 额外利息 = 投的钱 * 总利息       总获得 = 投入 + 投入 * 利息  

---------------------------------------------PROTOCOL_END----------------------------------------

---------------------------------------------CFG_STE_START---------------------------------------
function YanYuGeWGData:GetInitScoreCfgByWeek(week)
    return self.init_score_cfg[week]
end

function YanYuGeWGData:GetCurInitScoreCfgByWeek()
    return self.init_score_cfg[self:GetInvestWeek()]
end

function YanYuGeWGData:GetCurExtraScoreCfg()
    local w_day = self:GetTodayWeekDay()
    return (self.extra_score_cfg[self:GetInvestWeek()] or {})[w_day]
end

function YanYuGeWGData:GetOtherCfg()
    return self.other_cfg
end

function YanYuGeWGData:GetOtherCfgAttrValue(attr_name)
    return self.other_cfg[attr_name]
end

function YanYuGeWGData:GetWeekShowCfgByWeek(week)
    return self.week_show_cfg[week]
end

function YanYuGeWGData:GetCurWeekShowCfg()
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local w_day = self:GetTodayWeekDay()
    return (self.week_show_cfg[self:GetInvestWeek()] or {})[w_day]
end

function YanYuGeWGData:GetNormalTeQuanCfg()
    return self.normal_tequan_cfg
end

function YanYuGeWGData:GetActiveSortTeQuanCfg()
    local target_data_list = {}
    local cfg_list = self.normal_tequan_cfg

    for k, v in pairs(cfg_list) do
        local data = TableCopy(v)
        data.sort = 100000 + v.seq

        if self:IsTeQuanUnLockBySeq(v.seq) then
            if self:IsGetTeQuanEveryDayReward(v.seq) then
                data.sort = data.sort + 10000
            else
                data.sort = data.sort - 10000
            end
        end

        table.insert(target_data_list, data)
    end

    table.sort(target_data_list, SortTools.KeyLowerSorter("sort"))

    return target_data_list
end

function YanYuGeWGData:GetTeQuanCfgBySeq(seq)
    return self.tequan_cfg[seq]
end

function YanYuGeWGData:GetNeZhaTeQuanCfg()
    return self.nezha_tequan_cfg
end

function YanYuGeWGData:GetShowLimitAllShopInfo()
	return self.limit_shop_cfg
end

function YanYuGeWGData:GetShowTZSDDataList()
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local score = self:GetCurScore()

	local suit_list = {}
    local cur_suit_data_list = self:GetCurSuitShopDataList()

	for k, v in pairs(cur_suit_data_list) do
		if server_day >= v.min_open_server_day then
			local data = {}
			local seq =  v.seq
            data.sort = 10000 - seq
			data.seq = seq
			data.cfg = v
			data.buy_times = self.suit_shop[seq] or 0
            local sell_out = v.buy_limit <= data.buy_times
			-- data.is_all_buy = (v.buy_limit - data.buy_times) <= 0 and 1 or 0
            data.remind = score >= v.price and not sell_out

            if data.remind then
                data.sort = data.sort + 1000
            elseif sell_out then
                data.sort = data.sort - 1000
            end

			table.insert(suit_list, data)
		end
	end 	

	if not IsEmptyTable(suit_list) then
		table.sort(suit_list, SortTools.KeyUpperSorter("sort"))
	end

	return suit_list
end

function YanYuGeWGData:GetCurSuitShopDataList()
    return self.suit_shop_cfg[self.cur_grade]
end

function YanYuGeWGData:GetCurSuitShopDataBySeq(seq)
    return (self.suit_shop_cfg[self.cur_grade] or {})[seq]
end

function YanYuGeWGData:GetZZTQDataList()
    local target_data_list = {}
    local cfg_list = self.zanzhu_tequan_cfg

    for k, v in pairs(cfg_list) do
        local data = TableCopy(v)
        data.sort = 100000 + v.seq

        if self:IsTeQuanUnLockBySeq(v.seq) then
            if self:IsGetTeQuanEveryDayReward(v.seq) then
                data.sort = data.sort + 10000
            else
                data.sort = data.sort - 10000
            end
        end

        table.insert(target_data_list, data)
    end

    table.sort(target_data_list, SortTools.KeyLowerSorter("sort"))

    return target_data_list
end

function YanYuGeWGData:GetZZTQDataListNoSort()
    return self.zanzhu_tequan_cfg
end

function YanYuGeWGData:GetNobilityLevelCfgByLevel(level)
    return self.nobility_level_cfg[level]
end

function YanYuGeWGData:GetNobilityTaskCfg()
    return self.nobility_task_cfg
end

function YanYuGeWGData:GetNobilityTaskCfgOnlySeq()
    return self.nobility_task_only_cfg
end

function YanYuGeWGData:GetNobilityTaskCfgBySeq(seq)
    return self.nobility_task_cfg[seq]
end

function YanYuGeWGData:GetNobilityTaskCfgByDayAndSeq(open_day, end_day, seq)
    if self.nobility_task_day_cfg[open_day] and self.nobility_task_day_cfg[open_day][end_day] then
        return self.nobility_task_day_cfg[open_day][end_day][seq]

    end
    return nil
end


function YanYuGeWGData:GetShowModelCfgListBySeq(seq)
    return self.show_model_cfg[seq]
end

function YanYuGeWGData:GetShowModelThemeCfgBySeq(seq)
    return self.show_model_theme_cfg[seq]
end

function YanYuGeWGData:GetOneKeyTequanCfgByIndex(index)
    return self.onekey_tequan_cfg[index]
end
---------------------------------------------CFG_GET_END-----------------------------------------

---------------------------------------------CAL_START-------------------------------------------
function YanYuGeWGData:GetCurWeekShowCfgList()
    local data_list = {}
    local cur_invest_week = self:GetInvestWeek()
    local cfg_list = self:GetWeekShowCfgByWeek(cur_invest_week)

    if cfg_list then
        for k, v in pairs(cfg_list) do
            table.insert(data_list, v)
        end
    end

    return data_list
end

-- 是否开服周期
function YanYuGeWGData:IsOpenServerWeek()
    return self:GetInvestWeek() == 1
end

function YanYuGeWGData:GetTodayWeekDay()
    local w_day
    if self:IsOpenServerWeek() then
        w_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    else
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        w_day = TimeUtil.FormatSecond3MYHM1(server_time)
    end

    return w_day
end


function YanYuGeWGData:GetTiShengErDuStr()
    local cur_extra_score_cfg = self:GetCurExtraScoreCfg()
    local str = ""

    if not IsEmptyTable(cur_extra_score_cfg) then
        for k, v in pairs(cur_extra_score_cfg) do
            -- 积分任务
            if v.get_type == 1 then
                --已完成
                if not self:IsExtraScoreTaskComplete(v.seq) then
                    str = v.desc_up
                    break
                end

                str = v.desc_up
            end
        end
    end

    return str
end

function YanYuGeWGData:GetTaskExtraScore()
    local cur_extra_score_cfg = self:GetCurExtraScoreCfg()
    local extra_score = 0
    local can_add_extra_score = 0

    if not IsEmptyTable(cur_extra_score_cfg) then
        for k, v in pairs(cur_extra_score_cfg) do
            -- 积分任务
            if v.get_type == 1 then
                --已完成
                if self:IsExtraScoreTaskComplete(v.seq) then
                    extra_score = extra_score + v.reward
                end

                can_add_extra_score = can_add_extra_score + v.reward
            end
        end
    end

    return extra_score, can_add_extra_score
end

function YanYuGeWGData:IsExtraScoreTaskComplete(seq)
    return self.invest_task_flag[seq] == 1
end

function YanYuGeWGData:GetWLTZGetRewardTime()
    local time = 0
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local data_time = 24 * 60 * 60
    local day_start_time = TimeWGCtrl:NowDayTimeStart(server_time)
    
    -- 开服天数
    if self:IsOpenServerWeek() then
        local invest_end_day = self:GetOtherCfgAttrValue("invest_end_day") -- 结算日期
        local cur_open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        invest_end_day = invest_end_day + 1
        if cur_open_server_day < invest_end_day then
            time = (invest_end_day - cur_open_server_day) * data_time + day_start_time 
        end
    else
        --星期几 
        local invest_end_week_day = self:GetOtherCfgAttrValue("invest_end_week_day")
        local invest_end_week_time = self:GetOtherCfgAttrValue("invest_end_week_time")
        local invest_end_hour, invest_end_sec = invest_end_week_time / 100, invest_end_week_time % 100
        local cur_week_day = TimeUtil.FormatSecond3MYHM1(server_time)

        invest_end_week_day = invest_end_week_day + 1
        if invest_end_week_day > cur_week_day then
            time =  (invest_end_week_day - cur_week_day) * data_time + day_start_time + invest_end_hour * 60 * 60 + invest_end_sec * 60
        elseif invest_end_week_day == cur_week_day then
            time = day_start_time + invest_end_hour * 60 * 60 + invest_end_sec * 60
        end
    end

    return time
end

-- 能否获得投资  有能领取的钱  未领取过  
function YanYuGeWGData:IsCanGetTouZiMoney()
    return self.can_fetch_invest_flag == 1
end

function YanYuGeWGData:IsGetTouZiMoney()
    return self.can_fetch_invest_flag == 2
end

function YanYuGeWGData:IsJoinInvest()
    return self.already_invest_num > 0
end

function YanYuGeWGData:IsGetAllYYTQ()
    local tequan_list = self:GetNormalTeQuanCfg()
    if not IsEmptyTable(tequan_list) then
        for k, v in pairs(tequan_list) do
            if not self:IsTeQuanUnLockBySeq(v.seq) then
                return false
            end
        end
    else
        return false
    end

    return true
end

function YanYuGeWGData:IsGetOneYYTQ()
    local tequan_list = self:GetNormalTeQuanCfg()
    if not IsEmptyTable(tequan_list) then
        for k, v in pairs(tequan_list) do
            if self:IsTeQuanUnLockBySeq(v.seq) then
                return true
            end
        end
    else
        return false
    end

    return false
end

function YanYuGeWGData:GetSingleYYTQToolTip(old_data)
    local is_get_all_privilege = self:IsGetAllYYTQ()
    local target_data_list = is_get_all_privilege and self.yytq_tooltip_cfg[1] or self.yytq_tooltip_cfg[2]
    local len = #target_data_list
    local random = math.random(1, len)

    if IsEmptyTable(old_data) then
        return target_data_list[random]
    else
        local target_data = target_data_list[random]

        if old_data.type == target_data.type and old_data.index == target_data.index then
            return self:GetSingleYYTQToolTip(old_data)
        else
            return target_data
        end
    end
end

function YanYuGeWGData:GetNWSDGridDataList(need_sort)
	local shop_list = {}

    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local role_level = RoleWGData.Instance:GetRoleLevel()

    for k, v in pairs(self.limit_shop_cfg) do
        if open_day >= v.min_open_server_day then
            if role_level >= v.level_show then
                local data = {}
                local seq = self.limit_shop_cfg[k].seq
                data.seq = seq
                data.cfg = self.limit_shop_cfg[k]
                data.buy_times = self.limit_shop[k] or 0
                data.sort = v.display_order or seq
        
                if data.cfg.refresh_type ~= 0 then
                    if data.cfg.buy_limit <= data.buy_times then
                        data.sort = data.sort + 10000
                    end
                end
    
                table.insert(shop_list, data)
            end
        end
    end

	-- for k, v in pairs(self.limit_shop_flag) do
	-- 	if v == 1 and self.limit_shop_cfg[k] then
	-- 		local data = {}
	-- 		local seq = self.limit_shop_cfg[k].seq
	-- 		data.seq = seq
	-- 		data.cfg = self.limit_shop_cfg[k]
	-- 		data.buy_times = self.limit_shop[k] or 0

    --         local remain_times = data.cfg.buy_limit - data.buy_times
    --         data.sort = data.cfg.buy_limit <= data.buy_times and (10000 + seq) or seq
	-- 		table.insert(shop_list, data)
	-- 	end
	-- end

	if need_sort and not IsEmptyTable(shop_list) then
		table.sort(shop_list, SortTools.KeyLowerSorter("sort"))
	end

	return shop_list
end

--判断是否已经选择属性了
function YanYuGeWGData:IsAlreadyChooseAttr()
	for _, v in ipairs(self.choose_left_attr) do
		if v.is_choose then
			return true
		end
	end

	return false
end

function YanYuGeWGData:UpdataChooseAtrrList()
	local left_attr_list = {}
	local right_attr_list = {}

	local insert_attr_func = function (list, cfg, is_left)
		local data = {}
		data.seq = cfg.seq
		if is_left then
			if cfg.base_choose_attr and cfg.base_choose_attr ~= "" then
				data.attr_list = self:SplitChooseAttr(cfg.base_choose_attr)
			end
		else
			if cfg.per_choose_attr and cfg.per_choose_attr ~= "" then
				data.attr_list = self:SplitChooseAttr(cfg.per_choose_attr)
			end
		end

		data.is_choose = self:GetChooseAttrFlag(is_left, data.seq) == 1
		table.insert(list, data)
    end

	for k, v in pairs(self.choose_attr_cfg) do
		insert_attr_func(left_attr_list, v, true)
		insert_attr_func(right_attr_list, v, false)
	end

	if not IsEmptyTable(left_attr_list) then
		table.sort(left_attr_list, SortTools.KeyLowerSorters("seq"))
	end

	if not IsEmptyTable(right_attr_list) then
		table.sort(right_attr_list, SortTools.KeyLowerSorters("seq"))
	end

	self.choose_left_attr = left_attr_list
	self.choose_right_attr = right_attr_list
end

--- 获取属性列表
function YanYuGeWGData:GetChooseAttrList(is_left)
	return is_left and self.choose_left_attr or self.choose_right_attr
end

--是否能选择属性
function YanYuGeWGData:GetIsActChooseAttrTeQuan()
    if not IsEmptyTable(self.tequan_show_list) then
        for k, v in pairs(self.tequan_show_list) do
            if v.cfg.is_can_choose == 1 and v.act_flag == 1 then
                return true
            end
        end
    end

	return false
end

--获得当前选择所有属性（格式化）
function YanYuGeWGData:GetAllChooseAttrList()
	local all_attr_list = {}
	local attr_info = AttributePool.AllocAttribute()
	local capability = 0

	local get_attr_func = function (list, choose_attr)
		for k, v in pairs(choose_attr) do
			if v.is_choose  then
				local attr_str = EquipmentWGData.Instance:GetAttrStrByAttrId(v.attr_list.attr_str)
				local attr_value = v.attr_list.attr_value
				list[attr_str] = list[attr_str] and list[attr_str] + attr_value or attr_value
			end
		end
	end

	get_attr_func(all_attr_list, self.choose_left_attr)
	get_attr_func(all_attr_list, self.choose_right_attr)

	for k, v in pairs(all_attr_list) do
		attr_info[k] = attr_info[k] + v
	end

	capability = AttributeMgr.GetCapability(attr_info)
	return all_attr_list, capability
end


---拆解属性
function YanYuGeWGData:SplitChooseAttr(str)
	local attr_all_data = {}

	local attr_list = Split(str, "|")
	for _, info in ipairs(attr_list) do
		if info then
			local list_info = Split(info, ":")
			attr_all_data.attr_str = tonumber(list_info[1])
			attr_all_data.attr_value = tonumber(list_info[2])
		end
	end

	return attr_all_data
end

function YanYuGeWGData:GetChooseAttrFlag(is_left, seq)
	if is_left then
		return self.tequan_choose_attr1_flag[seq] or 0
	else
		return self.tequan_choose_attr2_flag[seq] or 0
	end
end

function YanYuGeWGData:GetYYGZShowTaskDataList()
    local act_data_list, scene_data_list, open_server_data_list = {}, {}, {}
    local cur_batch = self:GetOpenServerTaskCurBatch()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    for k, v in pairs(self:GetNobilityTaskCfgOnlySeq()) do
        local info = self:GetNobilityTaskBySeq(v.seq)

        if info and open_day >= v.open_day and open_day <= v.end_day then --
            local data = {}
            local sort = v.seq
            local is_complete = v.complete_limit > 0 and info.complete_count >= v.complete_limit
            if is_complete then
                sort = sort + 100000
            end

            data.cfg = v
            data.info = info
            data.sort = sort
            data.is_complete = is_complete

            if v.show_type == YANYUGE_NOBLE_SHOW_TYPE.SCENE then
                table.insert(scene_data_list, data)
            elseif v.show_type == YANYUGE_NOBLE_SHOW_TYPE.ACTIVITY then
                table.insert(act_data_list, data)
            elseif v.show_type == YANYUGE_NOBLE_SHOW_TYPE.OPEN_SERVER and (v.show_batch == "" or cur_batch == v.show_batch) then
                table.insert(open_server_data_list, data)
            end
        end
    end

    table.sort(act_data_list, SortTools.KeyLowerSorter("sort"))
    table.sort(scene_data_list, SortTools.KeyLowerSorter("sort"))
    table.sort(open_server_data_list, SortTools.KeyLowerSorter("sort"))
    return scene_data_list, act_data_list, open_server_data_list
end

function YanYuGeWGData:GetOpenServerTaskCurBatch()
    local cur_batch = 1
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local task_cfg = self:GetNobilityTaskCfgOnlySeq()
    for i = 1, 2 do
        for k, v in pairs(task_cfg) do
            local info = self:GetNobilityTaskBySeq(v.seq)
            if info and v.show_type == YANYUGE_NOBLE_SHOW_TYPE.OPEN_SERVER and cur_batch == v.show_batch
                and open_day >= v.open_day and open_day <= v.end_day then
                local is_complete = v.complete_limit > 0 and info.complete_count >= v.complete_limit
                if not is_complete then
                    return cur_batch
                end
            end
        end
        cur_batch = cur_batch + 1
    end
    return cur_batch
end

function YanYuGeWGData:GetOpenTaskCfg(id)
	return self.open_task_cfg[id] or {}
end

function YanYuGeWGData:GetYYGOpenTaskData()
	local open_task_list = {}
	local task_cfg = self:GetNobilityTaskCfgOnlySeq()
	local is_has_open_task = false
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for k, v in pairs(task_cfg) do
		if v.task_type == 8 and open_day >= v.open_day and open_day <= v.end_day then
			local data = self:GetOpenTaskCfg(v.param1)
			is_has_open_task = true
			table.insert(open_task_list, data)
		end
	end
	return is_has_open_task, open_task_list
end

--开服任务是否能显示
function YanYuGeWGData:GetYYGZIsCanShowOpenServerTask()
    local is_all_complete = true
    local noble_level = self:GetNobilityLevel()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    for k, v in pairs(self:GetNobilityTaskCfgOnlySeq()) do
        local info = self:GetNobilityTaskBySeq(v.seq)
        if v.show_type == YANYUGE_NOBLE_SHOW_TYPE.OPEN_SERVER and open_day >= v.open_day and open_day <= v.end_day
            and info and (v.complete_limit <= 0 or info.complete_count < v.complete_limit) then
            is_all_complete = false
        end
    end

    local close_level = self:GetOtherCfgAttrValue("open_server_task_close_level")
    local cur_level = RoleWGData.Instance:GetRoleLevel()
    local is_show = not is_all_complete and cur_level < close_level
    return is_show
end

--     不是休赛期 / 未休赛 有能领取的奖励
function YanYuGeWGData:IsCanShowWLTZFaceSlapView()
    local cur_invest_week = self:GetInvestWeek()
    local cfg_list = self:GetWeekShowCfgByWeek(cur_invest_week)

    if IsEmptyTable(cfg_list) then
        return false
    end

    local other_cfg = self:GetOtherCfg()

    if self:IsOpenServerWeek() then
        local open_server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
       
        if open_server_day >= other_cfg.invest_open_day and open_server_day <= other_cfg.invest_end_day then
            return true
        end
    else
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local w_day = TimeUtil.FormatSecond3MYHM1(server_time)

        if w_day >= other_cfg.invest_open_week_day and w_day <= other_cfg.invest_end_week_day then
            return true
        end
    end

    if self:IsCanGetTouZiMoney() then
        return true
    end
end

function YanYuGeWGData:IsTequanHasDayReward(seq)
    local tequan_cfg = self:GetTeQuanCfgBySeq(seq)

    if not IsEmptyTable(tequan_cfg) then
        return not IsEmptyTable(tequan_cfg.day_reward_item)
    end

    return false
end

function YanYuGeWGData:IsSuitSeqIsUnLock(seq)
    local cfg = self:GetCurSuitShopDataBySeq(seq)

    if cfg and cfg.last_seq and cfg.last_seq ~= -1 then
        local last_cfg = self:GetCurSuitShopDataBySeq(cfg.last_seq)

        if last_cfg then
            local last_buy_time = self.suit_shop[cfg.last_seq] or 0
            return last_cfg.buy_limit <= last_buy_time
        end
    end

    return true
end

-- 任务是否需要进行格式化显示
function YanYuGeWGData:IsNeedFormatType(task_type)
    if task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE10 or task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE11 or task_type == YANYUGE_NOBLE_TASK_TYPE.TYPE13 then
        return true
    end
    return false
end
---------------------------------------------CAL_END--------------------------------------------