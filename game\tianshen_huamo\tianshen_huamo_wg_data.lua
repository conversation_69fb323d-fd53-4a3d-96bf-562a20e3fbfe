TianShenHuamoWGData = TianShenHuamoWGData or BaseClass()

function TianShenHuamoWGData:__init()
	if TianShenHuamoWGData.Instance then
		print_error("[TianShenHuamoWGData] Attempt to create singleton twice!")
		return
	end

	TianShenHuamoWGData.Instance = self

    self:InitParam()
    self:InitConfig()
    self:InitHuaMoData()
    self:RegisterTianShenHuaMoRemindInBag()

    RemindManager.Instance:Register(RemindName.TianShenHuaMo, BindTool.Bind(self.GetRemind,self))
end

function TianShenHuamoWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.TianShenHuaMo)
    TianShenHuamoWGData.Instance = nil
    self.rumo_map_cfg = nil
    self.rumo_item_map_cfg = nil
    self.skill_map_cfg = nil
    self.uplevel_map_cfg = nil
    self.rumo_image_map_cfg = nil
    self.rumo_star_map_cfg = nil
    ---缓存数据
    self.tianshen_rumo_data = nil
end

function TianShenHuamoWGData:InitParam()
    self.rumo_map_cfg = nil
    self.rumo_item_map_cfg = nil
    self.skill_map_cfg = nil
    self.uplevel_map_cfg = nil
    self.tianshen_rumo_data = nil
    self.rumo_image_map_cfg = nil
    self.rumo_star_map_cfg = nil
end

function TianShenHuamoWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_rumo_auto")
    if cfg then
        self.rumo_map_cfg = ListToMap(cfg.demon_active, "index", "demon_level")
        self.rumo_item_map_cfg = ListToMap(cfg.demon_active, "item_id")
        self.rumo_image_map_cfg = ListToMap(cfg.demon_active, "appe_image_id")
        self.skill_map_cfg = ListToMap(cfg.skill, "demon_level", "skill_type")
        self.uplevel_map_cfg = ListToMap(cfg.uplevel, "index", "level")
        self.rumo_star_map_cfg = ListToMap(cfg.upgrade, "index", "demon_level", "level")
    end
end

---初始化数据记录红点条件的其他信息
function TianShenHuamoWGData:InitHuaMoData()
    if not self.rumo_map_cfg then
        return
    end

    if not self.tianshen_rumo_data then
       self.tianshen_rumo_data = {} 
    end

    for index, rumo_data in pairs(self.rumo_map_cfg) do
        self.tianshen_rumo_data[index] = {}
        self.tianshen_rumo_data[index].rumo_level = 0
        ---入魔激活数据
        self.tianshen_rumo_data[index].sub_datas = {}
        for sub_index, item_cfg in ipairs(rumo_data) do
            self.tianshen_rumo_data[index].sub_datas[sub_index] = self:InitHuaMoItem(item_cfg)
        end
    end
end

---初始化数据记录红点条件的其他信息
function TianShenHuamoWGData:InitHuaMoItem(item_cfg)
    if not item_cfg then
        return nil
    end

    if item_cfg.appe_image_id and item_cfg.weapons_use then
        local rumo_data = {}
        rumo_data.lock = true
        rumo_data.is_can_active = false
        rumo_data.demon_level = item_cfg.demon_level
        rumo_data.huanhua_id = 0
        rumo_data.is_show = item_cfg.is_show
        rumo_data.is_activate = false           ---当前天神激活
        rumo_data.shenqi_active = false         ---当前天神神器激活
        rumo_data.front_active = false          ---当前前置条件激活
        rumo_data.star_up = false               ---是否可升星
        rumo_data.is_remind = false
        rumo_data.skills = {}
        for i = 1, 5 do
            rumo_data.skills[i] = {}
            rumo_data.skills[i].demon_level = item_cfg.demon_level
            rumo_data.skills[i].lock = true
            rumo_data.skills[i].skill_id = 0
            rumo_data.skills[i].skill_lv = 0
            rumo_data.skills[i].is_remind = false
        end

        rumo_data.star_level = 0
        return rumo_data
    end
    return nil
end

---注册背包物品变化红点信息
function TianShenHuamoWGData:RegisterTianShenHuaMoRemindInBag()
	if (not self.rumo_map_cfg) or (not self.uplevel_map_cfg) then
		print_error(" TianShenHuamoWGData RegisterBackgroundRemindInBag error")
		return
	end

	local item_id_list = {}
    for _, rumo_data in pairs(self.rumo_map_cfg) do
        if rumo_data then
            for _, item_cfg in ipairs(rumo_data) do
                if item_cfg then
                    table.insert(item_id_list, item_cfg.item_id)
                end
            end
        end
    end

    local item_map = {}
    for _, level_data in pairs(self.uplevel_map_cfg) do
        if level_data then
            for _, data in ipairs(level_data) do
                if not item_map[data.item] then
                    item_map[data.item] = true
                end
            end
        end
    end

    for item_id, _ in pairs(item_map) do
        table.insert(item_id_list, item_id)
    end
    BagWGCtrl.Instance:RegisterRemindByItemChange(RemindName.TianShenHuaMo, item_id_list, nil)
end


---同步服务器数据(服务器)
function TianShenHuamoWGData:SetAllRuMoInfo(protocol)
    if not protocol.tianshen_data then
        return
    end

    for _, info in ipairs(protocol.tianshen_data) do
        self:SyncRuMoInfo(info)
    end

    self:SyncAllRuMoInfoRemind()
end

---同步单个入魔数据(服务器)
function TianShenHuamoWGData:SyncRuMoInfo(info)
    if (not info) or (not info.tianshen_id) or (not self.tianshen_rumo_data) or (not self.tianshen_rumo_data[info.tianshen_id]) then
        return
    end
    local tianshen_data = self.tianshen_rumo_data[info.tianshen_id]
    tianshen_data.rumo_level = info.demon_level
    tianshen_data.huanhua_id = info.huanhua_id

    local sub_datas = tianshen_data.sub_datas
    if not sub_datas then
        return
    end

    for sub_index, sub_data in ipairs(sub_datas) do
        sub_data.lock = not (info.demon_level >= sub_data.demon_level)
        local skill_data = info.skills and info.skills[sub_index] or nil
        if sub_data and skill_data then
            for i = 1, #sub_data.skills do
                local local_skill = sub_data.skills[i]
                local_skill.lock = sub_data.lock
                local server_skill = skill_data[i]
                if local_skill and server_skill then
                    local_skill.skill_id = server_skill.skill_id
                    local_skill.skill_lv = server_skill.skill_lv
                end
            end
        end

        sub_data.star_level = info.star_data and info.star_data[sub_index] or 0
    end

    self.tianshen_rumo_data[info.tianshen_id].sub_datas = sub_datas
end

---刷新所有的红点信息
function TianShenHuamoWGData:SyncAllRuMoInfoRemind()
    if not self.tianshen_rumo_data then
        return
    end

    for tianshen_id, info in pairs(self.tianshen_rumo_data) do
        self:SyncRuMoInfoRemind(tianshen_id, info)
    end
end

---刷新某个仙灵的红点信息
function TianShenHuamoWGData:SyncRuMoInfoRemind(tianshen_id, info)
    if (not info) or (not info.sub_datas) then
        return
    end

    for sub_index, sub_data in ipairs(info.sub_datas) do
        local cfg = self.rumo_map_cfg[tianshen_id][sub_index]

        for i, skill in ipairs(sub_data.skills) do
            if skill.skill_id == 0 then
                skill.is_remind = not skill.lock
            else
                ---判断升级条件
                local uplevel_data = self:GetSkillLevelCfgById(tianshen_id, skill.skill_lv + 1)
                if uplevel_data then
                    local item_num = ItemWGData.Instance:GetItemNumInBagById(uplevel_data.item)
                    skill.is_remind = item_num >= uplevel_data.stuff_num and (not skill.lock)
                else
                    skill.is_remind = false
                end
            end
        end

        local is_skill_remind = false
        for i, skill in ipairs(sub_data.skills) do
            if skill.is_remind then
                is_skill_remind = true
                break
            end
        end

        --计算升星
        if sub_data.star_level and (not sub_data.lock) then
            local hua_mo_star_up_cfg = self:GetStarlevelCfgById(tianshen_id, sub_data.demon_level, sub_data.star_level + 1)
            if hua_mo_star_up_cfg then
                local item_num = ItemWGData.Instance:GetItemNumInBagById(hua_mo_star_up_cfg.item_id)
                sub_data.star_up = item_num > hua_mo_star_up_cfg.item_num
            else
                sub_data.star_up = false
            end
        else
            sub_data.star_up = false
        end

        if cfg then
            sub_data.is_activate = TianShenWGData.Instance:GetTianshenInfoStatus(tianshen_id) == 1        ---天神激活
            sub_data.shenqi_active = TianShenWGData.Instance:IsShenQiActivation(cfg.weapons_use)      ---天神神器激活
            sub_data.front_active = sub_data.demon_level == 1 and true or info.rumo_level >= sub_data.demon_level - 1
            local item_num = ItemWGData.Instance:GetItemNumInBagById(cfg.item_id)
            sub_data.is_can_active = item_num >= 1 and sub_data.lock
            sub_data.is_remind = sub_data.is_activate and sub_data.shenqi_active and sub_data.front_active and sub_data.is_can_active
            sub_data.is_remind = sub_data.is_remind or is_skill_remind or sub_data.star_up
        end
    end
end

---获取单个入魔数据(本地数据)
function TianShenHuamoWGData:GetHuaMoDataById(tianshen_id)
    if (not tianshen_id) then
        return nil
    end
    return self.tianshen_rumo_data[tianshen_id]
end

---获取单个入魔子数据(本地数据)
function TianShenHuamoWGData:GetHuaMoSubDataById(tianshen_id, sub_data_index)
    if (not tianshen_id) or (not sub_data_index) then
        return nil
    end

    local data = self:GetHuaMoDataById(tianshen_id)
    if data and data.sub_datas then
        return data.sub_datas[sub_data_index]
    end

    return nil
end

function TianShenHuamoWGData:CheckSubDataHaveSkill(sub_data)
    if (not sub_data) or (not sub_data.skills) or (#sub_data.skills <= 0) then
        return false
    end

    for _, skill in ipairs(sub_data.skills) do
        if skill.skill_id == 0 then
            return false
        end
    end

    return true
end

---获取单个入魔属性(表)
function TianShenHuamoWGData:GetHuaMoDataByIdAndLevel(tianshen_id, demon_level)
    local aim_cfg = {}
    return ((self.rumo_map_cfg or aim_cfg)[tianshen_id] or aim_cfg)[demon_level] or nil
end

---获取入口的开启状态
function TianShenHuamoWGData:GetHuaMoEnterButonState(tianshen_id)
    local data = self:GetHuaMoDataById(tianshen_id)

    if data and data.sub_datas then
        for _, sub_data in ipairs(data.sub_datas) do
            if sub_data.is_show == 1 then
                return true
            end
        end
    end
    return false
end

---获取化魔红点(全部)
function TianShenHuamoWGData:GetRemind()
	-- 开启判断
	if not FunOpen.Instance:GetFunIsOpened(FunName.TianShenHuamoView) then
		return 0
	end

    for tianshen_id, info in pairs(self.tianshen_rumo_data) do
        if self:GetSubRemind(tianshen_id, info) == 1 then
            return 1
        end
    end
    return 0
end

---获取化魔技能红点
function TianShenHuamoWGData:GetSubRemind(tianshen_id, var_info)
    local info = var_info
    if not var_info then
        info = self:GetHuaMoDataById(tianshen_id)
    end

    self:SyncRuMoInfoRemind(tianshen_id, info)
    local is_open = self:GetHuaMoEnterButonState(tianshen_id)
    if (not info) or (not info.sub_datas) or (not is_open) then
        return 0
    end

    for sub_index, sub_data in ipairs(info.sub_datas) do
        if sub_data.is_remind then
            return 1
        end
        for i, skill in ipairs(sub_data.skills) do
            if skill.is_remind then
                return 1
            end
        end
    end
    return 0
end

---获取当前拥有红点的类型
function TianShenHuamoWGData:GetTypeIndex(tianshen_id, active_ts_select_type)
    local info = self:GetHuaMoDataById(tianshen_id)

    if (not info) or (not info.sub_datas) then
        return 1
    end

    if active_ts_select_type then
        return active_ts_select_type
    end

    for sub_index, sub_data in ipairs(info.sub_datas) do
        if sub_data.is_remind then
            return sub_index
        end
    end

    for sub_index, sub_data in ipairs(info.sub_datas) do
        if sub_index == info.huanhua_id then
            return sub_index
        end
    end

    return 1
end

---获取当前选中的类型的技能类型
function TianShenHuamoWGData:GetSkillIndex(tianshen_id, type_index)
    local info = self:GetHuaMoDataById(tianshen_id)

    if (not info) or (not info.sub_datas) or (not info.sub_datas[type_index]) or (not info.sub_datas[type_index].skills) then
        return 1
    end

    for i, skill in ipairs(info.sub_datas[type_index].skills) do
        if skill.is_remind then
            return i
        end
    end
    return 1
end

---获取技能池
function TianShenHuamoWGData:GetSkillPool(demon_level)
    if (not self.skill_map_cfg) or (not demon_level) or (not self.skill_map_cfg[demon_level]) then
        return nil
    end

    local skill_pool = {}
    for _, skill in pairs(self.skill_map_cfg[demon_level]) do
        local temp_data = skill
        table.insert(skill_pool, temp_data)
    end

    table.sort(skill_pool,function (a, b)
        return a.skill_type < b.skill_type
    end)

    return skill_pool
end

---获取技能
function TianShenHuamoWGData:GetSkillCfgById(demon_level, skill_id)
    local aim_cfg = {}
    return ((self.skill_map_cfg or aim_cfg)[demon_level] or aim_cfg)[skill_id] or nil
end

---获取技能相关的配置
function TianShenHuamoWGData:GetSkillLevelCfgById(tinshen_id, skill_lv)
    local aim_cfg = {}
    return ((self.uplevel_map_cfg or aim_cfg)[tinshen_id] or aim_cfg)[skill_lv] or nil
end

---获取技能相关的配置
function TianShenHuamoWGData:GetStarlevelCfgById(tinshen_id, demon_lv, star_lv)
    local aim_cfg = {}
    return (((self.rumo_star_map_cfg or aim_cfg)[tinshen_id] or aim_cfg)[demon_lv] or aim_cfg)[star_lv] or nil
end

---获取当前入魔的属性
function TianShenHuamoWGData:GetMainShowAttribute(tianshen_id, select_type, star_lv)
    local list = {}
    local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(tianshen_id)
    if not tianshen_info then
        return list
    end

    local up_star_cfg = TianShenWGData.Instance:GetTianShenStar1(tianshen_id, tianshen_info.star)
    local shenqi_cfg = TianShenWGData.Instance:GetShenQiByTianShenIndex(tianshen_id)
    local shenqi_attr_add_per = 0

	if shenqi_cfg and shenqi_cfg.index then
		local shenqi_active = TianShenWGData.Instance:IsShenQiActivation(shenqi_cfg.index)
		if shenqi_active then
			local shenqi_info = TianShenWGData.Instance:GetTianShenShenQiInfo(shenqi_cfg.index)
    		local star = shenqi_info and shenqi_info.star or 0
			local shenqi_attr_cfg = TianShenWGData.Instance:GetShenQiStarCfg(shenqi_cfg.index, star)
			shenqi_attr_add_per = shenqi_attr_cfg["tianshen_att_per"] or 0
			shenqi_attr_add_per = shenqi_attr_add_per / 10000 
		end
	end

    --- 天神被动技能加成
    local pasv_attr_list = TianShenWGData.Instance:GetPasvAttr(tianshen_id)
	local cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(up_star_cfg)
    local hua_mo_attr_list = self:GetHuaMoAttribute(tianshen_id, select_type, star_lv)
    local Hua_mo_star_attr_list = self:GetHuaMoStarAttribute(tianshen_id, select_type, star_lv, false)
    local Hua_mo_star_next_attr_list = self:GetHuaMoStarAttribute(tianshen_id, select_type, star_lv, true)


    if up_star_cfg and cfg_map_attr_list and hua_mo_attr_list then
        for key, attr_str in pairs(cfg_map_attr_list) do
            local tab = {}
            tab.attr_str = attr_str
            tab.attr_value = up_star_cfg[key] or 0

            ---本身天神属性
            if pasv_attr_list[attr_str] then
				tab.attr_value = math.floor(tab.attr_value * (pasv_attr_list[attr_str] + shenqi_attr_add_per) + 0.5)
			elseif tab.attr_value and tab.attr_value > 0 then
				tab.attr_value = math.floor(tab.attr_value * (1 + shenqi_attr_add_per) + 0.5)
			end

            ---下一个属性
            tab.add_value = hua_mo_attr_list[attr_str] or 0
            tab.star_value = Hua_mo_star_attr_list and Hua_mo_star_attr_list[attr_str] or 0
            tab.star_next_value = Hua_mo_star_next_attr_list and Hua_mo_star_next_attr_list[attr_str] or 0
            table.insert(list, tab)
        end
    end

    list = self:SortAttr(list)
    return list
end

function TianShenHuamoWGData:SortAttr(list)
	local attr_sort_index_list = AttributeMgr.GetAttrSortIndexList()
	local function sort_func(a, b)
		local a_attr_type = AttributeMgr.GetAttributteKey(a.attr_str)
		local b_attr_type = AttributeMgr.GetAttributteKey(b.attr_str)
		if not attr_sort_index_list[a_attr_type] or not attr_sort_index_list[b_attr_type] then
			return false
		end
		
		return attr_sort_index_list[a_attr_type] < attr_sort_index_list[b_attr_type]
	end

	table.sort(list, sort_func)
	return list
end

---化魔被动技能加成
function TianShenHuamoWGData:GetHuaMoSkillAttr(tianshen_id, select_type)
    local skill_add_per = {}
    local skill_add_per_str = {}
    local hua_mo_data = self:GetHuaMoSubDataById(tianshen_id, select_type)

    if (not hua_mo_data) or (not hua_mo_data.skills) then
        return skill_add_per_str, skill_add_per
    end
    
    local attr_type = nil
	for _, skill in pairs(hua_mo_data.skills) do
        if (not skill.lock) and (skill.skill_id ~= 0) and self.skill_map_cfg and self.skill_map_cfg[select_type] 
            and self.skill_map_cfg[select_type][skill.skill_id] then

            local attr_id = self.skill_map_cfg[select_type][skill.skill_id]["attr_id1"]
            local attr_value = self.skill_map_cfg[select_type][skill.skill_id]["attr_value1"] or 0
            attr_type = EquipmentWGData.Instance:GetAttrStrByAttrId(attr_id)
            local skill_level_cfg = self:GetSkillLevelCfgById(tianshen_id, skill.skill_lv)
            if skill_level_cfg then
                skill_add_per_str[attr_type] = math.floor(attr_value * (1 + (skill_level_cfg.add_per / 10000))) 
                skill_add_per[attr_id] = math.floor(attr_value * (1 + (skill_level_cfg.add_per / 10000)))
            end
        end
	end
	return skill_add_per_str, skill_add_per
end

---化魔基础属性
function TianShenHuamoWGData:GetHuaMoAttribute(tianshen_id, select_type, star_lv)
    local hua_mo_attr_str = {}

    local hua_mo_cfg = self:GetHuaMoDataByIdAndLevel(tianshen_id, select_type)
    if not hua_mo_cfg then
        return hua_mo_attr_str, nil
    end

    local cfg_map_attr_list_str = AttributeMgr.GetUsefulAttributteByClass(hua_mo_cfg)
	local cfg_map_attr_list = AttributeMgr.GetAttributteByClass(hua_mo_cfg)
    local skill_attr_str, skill_attr = self:GetHuaMoSkillAttr(tianshen_id, select_type)
    local star_attr_str = self:GetHuaMoStarAttribute(tianshen_id, select_type, star_lv, false)

    for key, attr_str in pairs(cfg_map_attr_list_str) do
        hua_mo_attr_str[attr_str] = hua_mo_cfg[key]

        if skill_attr_str[attr_str] then
            hua_mo_attr_str[attr_str] = hua_mo_attr_str[attr_str] + skill_attr_str[attr_str]
        end
    end

    for key, attr_str in pairs(cfg_map_attr_list) do
        -- 加上技能战斗力
        if skill_attr_str[key] then
            cfg_map_attr_list[key] = cfg_map_attr_list[key] + skill_attr_str[key]
        end

        -- 加上升星的战斗力
        if star_attr_str[key] then
            cfg_map_attr_list[key] = cfg_map_attr_list[key] + star_attr_str[key]
        end
    end

    return hua_mo_attr_str, cfg_map_attr_list
end

---化魔升星属性
function TianShenHuamoWGData:GetHuaMoStarAttribute(tianshen_id, select_type, star_lv, is_next)
    local hua_mo_star_attr = {}
    local temp_lv = is_next and star_lv + 1 or star_lv

    local hua_mo_star_up_cfg = self:GetStarlevelCfgById(tianshen_id, select_type, temp_lv)
    if not hua_mo_star_up_cfg then
        return hua_mo_star_attr
    end

    local cfg_map_attr_list_str = AttributeMgr.GetUsefulAttributteByClass(hua_mo_star_up_cfg)
    
    for key, attr_str in pairs(cfg_map_attr_list_str) do
        hua_mo_star_attr[attr_str] = hua_mo_star_up_cfg[key]
    end

    return hua_mo_star_attr
end

---获取一份化魔配置根据物品id
function TianShenHuamoWGData:GetHuaMoAttributeCfgByItem(item_id)
    local aim_cfg = {}
    return (self.rumo_item_map_cfg or aim_cfg)[item_id] or nil
end

---获取一份化魔配置根据物品id
function TianShenHuamoWGData:IsHuaMoActive(aim_cfg)
    if aim_cfg then
        local sub_data = self:GetHuaMoSubDataById(aim_cfg.index, aim_cfg.demon_level)
        if sub_data then
            return (not sub_data.lock)
        end
    end
    return false
end

---获取一个化魔形象
function TianShenHuamoWGData:GetHuaMoAppeImage(tianshen_id, select_type)
    local hua_mo_cfg = self:GetHuaMoDataByIdAndLevel(tianshen_id, select_type)
    if not hua_mo_cfg then
        return nil
    end
    return hua_mo_cfg.appe_image_id
end

---获取一个化魔形象(外部接口，给予一个天神id)
function TianShenHuamoWGData:GetHuaMoAppeImageById(tianshen_id)
    local data = self:GetHuaMoDataById(tianshen_id)
    if (not data) or (not data.huanhua_id) or (data.huanhua_id == 0) then
        return nil, 0
    end
    return self:GetHuaMoAppeImage(tianshen_id, data.huanhua_id), data.huanhua_id
end

---获取一个化魔形象(外部接口，给予一个天神id)
function TianShenHuamoWGData:GetHuaMoAppeImageByImageId(image_id)
    local aim_cfg = {}

    if not image_id then
        return nil
    end

    return (self.rumo_image_map_cfg or aim_cfg)[image_id] or nil
end
