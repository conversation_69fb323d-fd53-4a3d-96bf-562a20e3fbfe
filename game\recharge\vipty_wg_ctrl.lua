require("game/recharge/recharge_vip_ty_view")
require("game/recharge/shou_ci_chongzhi_tips")
require("game/recharge/second_chongzhi_tips")
require("game/recharge/v4_buy_tips")
require("game/recharge/chongzhi_pre_show_view")

VipTyWGCtrl = VipTyWGCtrl or BaseClass(BaseWGCtrl)

VIP_EXPERICENCE_FLAG = {
	ZERO_FLAG = 0, --未体验过任何vip类型
	ONE_FLAG = 1, --一日体验卡
	TWO_FLAG = 2, --三日体验卡
}

VIP_TYPE_NUM = {
	TYPE_ZERO = -1,
	TYPE_ONE = 1,
	TYPE_TWO = 2,
	TYPE_THREE = 3,
	TYPE_FOUR = 4,
	TYPE_FIVE = 5,
}

function VipTyWGCtrl:__init()
	if VipTyWGCtrl.Instance then
		ErrorLog("[VipTyWGCtrl] Attemp to create a singleton twice !")
	end
	VipTyWGCtrl.Instance = self
	self.recharge_vip_ty_view = RechargeVipTyView.New(GuideModuleName.RechargeVipTyView)
	self.shou_ci_chongzhi_tips = FirstRechargeTips.New(GuideModuleName.FirstRechargeTips)
	self.second_chongzhi_tips = SecondRechargeTips.New(GuideModuleName.SecondRechargeTips)
	self.v4_buy_tips = VipFourBuyTips.New(GuideModuleName.VipFourBuyTips)
	self.recharge_pre_show_view = FirstRechargePreShow.New()
	-- self.is_two_day_ty = false
	self.VipType = 0

	self.task_change_handle = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE, BindTool.Bind(self.OpenTyView, self))
end

function VipTyWGCtrl:__delete()
	-- self.is_two_day_ty = nil
	self.VipType = nil
	VipTyWGCtrl.Instance = nil
	if self.recharge_vip_ty_view ~= nil then
		self.recharge_vip_ty_view:DeleteMe()
		self.recharge_vip_ty_view = nil
	end

	if self.shou_ci_chongzhi_tips ~= nil then
		self.shou_ci_chongzhi_tips:DeleteMe()
		self.shou_ci_chongzhi_tips = nil
	end

	if self.second_chongzhi_tips ~= nil then
		self.second_chongzhi_tips:DeleteMe()
		self.second_chongzhi_tips = nil
	end

	if self.v4_buy_tips ~= nil then
		self.v4_buy_tips:DeleteMe()
		self.v4_buy_tips = nil
	end

	if self.recharge_pre_show_view ~= nil then
		self.recharge_pre_show_view:DeleteMe()
		self.recharge_pre_show_view = nil
	end
	if self.task_change_handle then
		GlobalEventSystem:UnBind(self.task_change_handle)
		self.task_change_handle = nil
	end
end

function VipTyWGCtrl:Open()
	self.recharge_vip_ty_view:Open()
end
function VipTyWGCtrl:CheckRoleLevel(value,old_value) --获取当前人物等级
	-- self:IsOpenFistRechargeTips(value,old_value)
end

function VipTyWGCtrl:IsOpenFistRechargeTips()
	local has_recharge_money = RechargeWGData.Instance:GetHistoryRecharge()
	-- local recharge_count = RechargeWGData.Instance:GetHistoryRechargeCount()
	-- 首充
	if has_recharge_money == 0 then
		if self.shou_ci_chongzhi_tips:IsOpen() then return end
		self.shou_ci_chongzhi_tips:Open()
	-- elseif has_recharge_money > 0 and has_recharge_money < 36 then
	-- 	-- 次充
	-- 	if self.second_chongzhi_tips:IsOpen() then return end
	-- 	self.second_chongzhi_tips:Open()
	-- 	-- vip
	-- elseif has_recharge_money >= 36
	--  and RoleWGData.Instance.role_vo.vip_level < 4 then
	-- 	local data_list = LayoutZeroBuyWGData.Instance:GetAllZeroBuyData()
	-- 	local info_cfg = data_list[1]
	-- 	if not info_cfg or info_cfg.state >= 3 or self.v4_buy_tips:IsOpen() then return end
	-- 	self.v4_buy_tips:Open()
	end
end

function VipTyWGCtrl:CheckIsFirstRechargeCloseTips()
	if not RechargeWGData.Instance:GetIsFirstRecharge() then return end
	if not self.shou_ci_chongzhi_tips:IsOpen() then return end
	self.shou_ci_chongzhi_tips:Close()
end

function VipTyWGCtrl:GetVipCardType(vip_type) --获取当前人物等级
	self.vip_type = vip_type
	self:OpenTyView()
end

function VipTyWGCtrl:OpenTyView(change, task_id) --根据人物等级和卡片类型确定是否打开
	-- print_error(self.role_level, self.vip_type)
	local experience_flag = VipWGData.Instance:IsVipTy()
	if self.vip_type == VIP_TYPE_NUM.TYPE_ZERO and experience_flag == VIP_EXPERICENCE_FLAG.ZERO_FLAG
	and VipWGData.Instance:GetRoleVipLevel() < 1 then --1 表示没有开通任何vip类型
		local vip_temp_card_task = ConfigManager.Instance:GetAutoConfig("vip_auto").other[1].vip_temp_card_task
		if TaskWGData.Instance:GetTaskIsCompleted(vip_temp_card_task) then
			self:OpenVipTyView(1)

			if self.auto_task_timer then
				GlobalTimerQuest:CancelQuest(self.auto_task_timer)
				self.auto_task_timer = nil
			end
			self.auto_task_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.CloseVipTyView,self), 10)--延时处理
		end
	end
end
function VipTyWGCtrl:OpenVipTyView(type) --打开Vip体验界面
	self.VipType = type
	if not self.recharge_vip_ty_view:IsOpen() then
		self.recharge_vip_ty_view:Open()
	end
end

function VipTyWGCtrl:CloseVipTyView() --关闭Vip体验界面
	local experience_flag = VipWGData.Instance:IsVipTy()
	if experience_flag == VIP_EXPERICENCE_FLAG.ZERO_FLAG then
	    local card_cfg = VipWGData.Instance:GetVipCardCfg()
	    local card_type = card_cfg[5].card_type
		VipWGCtrl.Instance:SendBuyVipTimeCard(card_type)
		self.recharge_vip_ty_view:Close()
	end
end

function VipTyWGCtrl:OnVipExperience2Notice(protocol) --协议1443VIP次日体验
	-- print_error("收到协议1443======================")
	self.is_two_day_ty = true
	if VipWGCtrl.Instance.viptip_view:IsOpen() then
		VipWGCtrl.Instance.viptip_view:Close()
	end
	self:OpenVipTyView(2)
	if self.auto_task_timer then
		GlobalTimerQuest:CancelQuest(self.auto_task_timer)
		self.auto_task_timer = nil
	end
	self.auto_task_timer = GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.CloseVipTyView,self), 10)--延时处理
end

function VipTyWGCtrl:SetPreShowRootNodeActive(is_active)
	self.recharge_pre_show_view:OvSetRootNodeActive(is_active)
end

function VipTyWGCtrl:IsShouCiTipOpen()
	return self.shou_ci_chongzhi_tips:IsOpen()
end
