TianShuGetSkillView = TianShuGetSkillView or BaseClass(SafeBaseView)

function TianShuGetSkillView:__init()
	self:SetMaskBg(true, true, false, BindTool.Bind(self.BlockClick, self))
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/tianshu_ui_prefab", "TianShuGetNewSkillTips")
	self.play_audio = true
end

function TianShuGetSkillView:__delete()

end

function TianShuGetSkillView:LoadCallBack()

end

function TianShuGetSkillView:ReleaseCallBack()

end

function TianShuGetSkillView:ShowView(skill_id)
	self.id_value = skill_id
	local skill_cfg = SkillWGData.GetSkillPassiveCfg(self.id_value)

	if skill_cfg == nil then
		return
	end
	self:Open()
end

function TianShuGetSkillView:BlockClick()
	if self.timer > 0 then
		if self.timer_hide_quest then
			GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
			self.timer_hide_quest = nil
		end
		self:MoveToTarget()
	end
end

function TianShuGetSkillView:ShowIndexCallBack()
	local skill_cfg = SkillWGData.GetSkillPassiveCfg(self.id_value)
	self.node_list.Frame:SetActive(true)
	if not skill_cfg then
		self:Close()
		return
	end
	self.node_list["SkillIcon"].transform.localPosition = Vector3(-153.7,18.1,0)
	self.node_list["SkillName"].text.text = skill_cfg.name
	self.node_list["SkillDesc"].text.text = skill_cfg.desc
	self.node_list["SkillIcon"].image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon))
	self.node_list["SkillIcon"]:SetActive(true)
	self:CalTimeToHideBg()
end

function TianShuGetSkillView:CloseCallBack()
	if self.timer_hide_quest then
		GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
		self.timer_hide_quest = nil
	end
end

function TianShuGetSkillView:MoveToTarget()
	self.node_list.Frame:SetActive(false)
	local timer = 1
	local target = self.node_list.Icon_parent
	self.time_quest = GlobalTimerQuest:AddDelayTimer(function()
		local item = self.node_list["SkillIcon"]
		local path = {}
		self.target_pos = target.transform.position
		table.insert(path, self.target_pos)
		local tweener = item.transform:DOPath(
			path,
			timer,
			DG.Tweening.PathType.Linear,
			DG.Tweening.PathMode.TopDown2D,
			1,
			nil)
		tweener:SetEase(DG.Tweening.Ease.Linear)
		tweener:SetLoops(0)
		local close_view = function()
			self:CloseView()
		end
		tweener:OnComplete(close_view)
		item.loop_tweener = tweener
	end, 0)
end

function TianShuGetSkillView:CloseView()
	self:Close()
	self.node_list["SkillIcon"]:SetActive(false)
	self.target_icon.gameObject:SetActive(true)
	GlobalTimerQuest:CancelQuest(self.time_quest)
end

function TianShuGetSkillView:CalTimeToHideBg()
	if self.timer_hide_quest then
	   GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
	   self.timer_hide_quest = nil
	end
	
	self.timer = 2
	self.timer_hide_quest = GlobalTimerQuest:AddRunQuest(function()
		self.timer = self.timer - UnityEngine.Time.deltaTime

		if self.timer <= 0 then
			GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
			self.timer_hide_quest = nil
			self:MoveToTarget()
		end
	end, 0)
end


