require("game/merge_activity/merge_fireworks/merge_fireworks_wg_data")
require("game/merge_activity/merge_fireworks/merge_fireworks_view")
require("game/merge_activity/merge_fireworks/merge_fireworks_reward")
require("game/merge_activity/merge_fireworks/merge_fireworks_select_baodi_view")

MergeFireworksWGCtrl = MergeFireworksWGCtrl or BaseClass(BaseWGCtrl)

function MergeFireworksWGCtrl:__init()
	if MergeFireworksWGCtrl.Instance then
		ErrorLog("[MergeFireworksWGCtrl] Attemp to create a singleton twice !")
	end
	MergeFireworksWGCtrl.Instance = self
	self:RegisterAllProtocols()

	self.data = MergeFireworksWGData.New()
    self.reward_view = MergeFireworksReward.New()
    self.select_baodi_view = MergeFireworksSelectBaoDiView.New()

	self.act_change = BindTool.Bind(self.ActChange, self)
    ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function MergeFireworksWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

    self.reward_view:DeleteMe()
	self.reward_view = nil

    self.select_baodi_view:DeleteMe()
	self.select_baodi_view = nil

	ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
	self.act_change = nil

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    self.main_ui_load = nil
	MergeFireworksWGCtrl.Instance = nil
end

function MergeFireworksWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCCSAYanHuaShengDianInfo,'OnSCCSAYanHuaShengDianInfo')
    self:RegisterProtocol(SCCSAYanHuaShengDianRecordListInfo,'OnSCCSAYanHuaShengDianRecordListInfo')
    self:RegisterProtocol(SCCSAYanHuaShengDianDrawRewardInfo,'OnSCCSAYanHuaShengDianDrawRewardInfo')
    self:RegisterProtocol(CSCSAYanHuaShengDianChooseBaodiReward)
    self:RegisterProtocol(SCCSAYanHuaShengDianBaodiRewardInfo,'OnSCCSAYanHuaShengDianBaodiRewardInfo')
end

function MergeFireworksWGCtrl:MainuiOpenCreate()
    self.main_ui_load = true
    if self.data:GetActIsOpen() then
        MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
    end
end

--活动信息
function MergeFireworksWGCtrl:OnSCCSAYanHuaShengDianInfo(protocol)
	--print_error("info",protocol)
	self.data:SetInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2246)
    self.reward_view:Flush("ignore")
    RemindManager.Instance:Fire(RemindName.Merge_Fireworks)
end

--日志
function MergeFireworksWGCtrl:OnSCCSAYanHuaShengDianRecordListInfo(protocol)
	--print_error("record", protocol.record_count, protocol)
	self.data:SetRecord(protocol.record_list)
    ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2246, "all", {[2] = "record"})
end

--奖励
function MergeFireworksWGCtrl:OnSCCSAYanHuaShengDianDrawRewardInfo(protocol)
    -- print_error("reward", protocol.reward_count, protocol)
    ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2246, "all", {[2] = "play_ani"})
    function delay_callback()
        local draw_type = MergeFireworksWGData.Instance:CacheOrGetDrawIndex()
        local btn_cfg = MergeFireworksWGData.Instance:GetConsumeCfg()
        btn_cfg = btn_cfg[draw_type]
        local str = string.format(Language.MergeFireworks.BtnStr, btn_cfg.onekey_lotto_num)
        local ok_func = function ()
            ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2246)
            MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.BUY, btn_cfg.onekey_lotto_num, 1)
        end
    
        local data_list, item_ids = self.data:CalDrawRewardList(protocol)
        local price = btn_cfg.consume_count / btn_cfg.yanhua_item.num 	-- 计算单价
        local other_info = {}
        other_info.again_text = str
        other_info.stuff_id = btn_cfg.yanhua_item.item_id
        other_info.times = btn_cfg.yanhua_item.num
        other_info.spend = price
        local best_data = {}
        if IsEmptyTable(item_ids) then
            best_data = nil
        else
            best_data.item_ids = item_ids
        end
        other_info.best_data = best_data
    
        TipWGCtrl.Instance:ShowGetCommonReward(data_list, ok_func, other_info)
    end
    -- local delay_time = self.data:GetDelayTime()
    -- GlobalTimerQuest:AddDelayTimer(delay_callback, delay_time)
    delay_callback()
end

--自选奖励信息
function MergeFireworksWGCtrl:OnSCCSAYanHuaShengDianBaodiRewardInfo(protocol)
    --print_error("自选奖励", protocol.reward_id_list, protocol)
    self.data:SetSelectedRewardList(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2246, "all", {[2] = "sel_reward"})
end

--请求
function MergeFireworksWGCtrl:SendReq(opera_type, param1, param2)
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.MERGE_ACT_FIREWORKS,
		opera_type = opera_type,
        param_1 = param1,
        param_2 = param2,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

-- 自选奖励请求
function MergeFireworksWGCtrl:SendRewrdId(reward_id_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCSAYanHuaShengDianChooseBaodiReward)
	protocol.rewad_id_list = reward_id_list or {0, 0, 0}
	protocol:EncodeAndSend()
end

--奖励弹窗
function MergeFireworksWGCtrl:OpenRewardView(data)
    self.reward_view:SetData(data)
    self.reward_view:Open()
end


--监听活动状态改变，清除缓存标记，添加道具监听，
function MergeFireworksWGCtrl:ActChange(act_type, status)
    if act_type ~= ACTIVITY_TYPE.MERGE_ACT_FIREWORKS then
        return
    end

	self.data:ClearCache()
    if status == ACTIVITY_STATUS.OPEN then
        if self.main_ui_load then
            MergeFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
        end
		self:ListeneItem(true)--添加道具监听
    elseif status == ACTIVITY_STATUS.CLOSE then
		self:ListeneItem(false)
        self.is_fire_map = false
    end
end

--物品监听
function MergeFireworksWGCtrl:ListeneItem(status)
	if status then
		if not self.item_data_change then
			self.item_data_change = BindTool.Bind(self.OnFwsItemChange, self)
		end
    	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
	else
		if self.item_data_change then
			ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
			self.item_data_change = nil
		end
	end
end

--物品改变返回
function MergeFireworksWGCtrl:OnFwsItemChange(item_id)
	local check_list = MergeFireworksWGData.Instance:GetItemDataChangeList()
    for i, v in pairs(check_list) do
        if v == item_id then
            RemindManager.Instance:Fire(RemindName.Merge_Fireworks)
            return
        end
    end
end

--去放烟花的地方
function MergeFireworksWGCtrl:GotoFire(callback, range)
	callback()
end

--使用道具并弹窗
function MergeFireworksWGCtrl:ClickUse(index, func)
     --数量检测
    local cfg = MergeFireworksWGData.Instance:GetConsumeCfg()
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg[index].yanhua_item.item_id)

    --不足弹窗
    if num < cfg[index].yanhua_item.num then
        if not self.alert then
            self.alert = Alert.New()
        end
        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "merge_fireworks")
        self.alert:SetCheckBoxDefaultSelect(false)
        local item_cfg = ItemWGData.Instance:GetItemConfig(cfg[index].yanhua_item.item_id)
        local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        local cost = cfg[1].consume_count * (cfg[index].yanhua_item.num - num)
        local str = string.format(Language.MergeFireworks.CostStr, name, cost)

        self.alert:SetLableString(str)
        self.alert:SetOkFunc(func)
        self.alert:Open()
    else
        --使用
        func()
    end
end


function MergeFireworksWGCtrl:GetIsFireMap()
    return self.is_fire_map
end

-- 选择保底弹窗
function MergeFireworksWGCtrl:OpenSelectBaoDiView()
    if self.select_baodi_view:IsOpen() then
        self.select_baodi_view:Flush()
    else
        self.select_baodi_view:Open()
    end
end