require("game/guild/guild_protocol")
require("game/guild/guild_boss/guild_boss_wg_ctrl")
require("game/guild/guild_wg_data")
require("game/guild/guild_view")
require("game/guild/guild_battle_end_rank_view")
require("game/guild/guild_jiushedaofb_follow")
require("game/guild/guild_child_view/liekun_bosslist_view")
require("game/guild/guild_fb_guwu_btn_view")
require("game/guild/guild_guwu_view")
require("game/guild/guild_shouhu_jiesuan")
require("game/guild/guild_task_view")
require("game/guild/guild_child_view/guild_intro_view")
require("game/guild/guild_child_view/guild_history_view")
require("game/guild/guild_child_view/guild_member_view")
require("game/guild/guild_child_view/guild_list_view")
require("game/guild/guild_child_view/guild_juanxian_view")
require("game/guild/guild_child_view/guild_wage_view")
require("game/guild/guild_child_view/guild_skill_view")
require("game/guild/guild_child_view/guild_list_view_hebing")--帮派合并
require("game/guild/guild_child_view/guild_hebing_show")
require("game/guild/guild_child_view/guild_redpacket")
require("game/guild/guild_child_view/guild_war_view")
require("game/guild/guild_child_view/liekun_view")
require("game/guild/guild_child_view/liekun_follow")
require("game/guild/guild_child_view/guild_war_zhanbao_view")
require("game/guild/guild_history_item")
require("game/guild/guild_member_item")
require("game/guild/guild_list_item")
require("game/guild/guild_shouhu")
require("game/guild/guild_child_view/guild_boss_view")
require("game/guild/pop_info")
require("game/guild/pop_guild_info_view")--帮派详细信息弹窗
require("game/guild/pop_setting")--申请设置界面
require("game/guild/pop_create")
require("game/guild/pop_mail") --邮件群发
require("game/guild/pop_applyfor") --帮派申请
require("game/guild/pop_appoint")
require("game/guild/guild_redpacket_tips")
require("game/guild/guild_vipred_tips")
require("game/guild/guild_day_reward_view")
require("game/guild/guild_child_view/guild_juanxian_view")
require("game/guild/guild_create_view")
require("game/guild/guild_cangku/guild_cangku_wg_data")
require("game/guild/guild_cangku/guild_cangku_view")
require("game/guild/guild_cangku/pop_tempbag")
require("game/guild/guild_cangku/cangku_convert_tips")
require("game/guild/guild_activity/guild_activity_view")
require("game/guild/guild_activity/guild_activity_wg_data")
require("game/guild/guild_activity/guild_activity_shouhu_rank")
require("game/guild/guild_activity/guild_activity_boss_rank")
require("game/guild/guild_activity/guild_activity_dati_rank")
require("game/guild/guild_activity/guild_activity_boss_view")
require("game/guild/guild_activity/guild_activity_dati_view")
require("game/guild/guild_activity/guild_activity_shouhu_view")
require("game/guild/guild_activity/guild_activity_view_new")
require("game/guild/guild_activity/guild_activity_merge_view")

require("game/guild/guild_child_view/guild_war_duizheng_view")
require("game/guild/guild_child_view/guild_war_rule_view")
require("game/guild/guild_child_view/guild_war_fenpei_view")
require("game/guild/guild_child_view/guild_rename_view")
require("game/guild/guild_welcome_view")
require("game/guild/guild_dalian_view")

--每日宝箱
require("game/guild/baoxiang/guild_baoxiang_view")
require("game/guild/baoxiang/guild_baoxiang_wg_data")
require("game/guild/baoxiang/baoxiang_code_view")
require("game/guild/baoxiang/baoxiang_flush_request_view")
require("game/guild/baoxiang/baoxiang_flush_view")
require("game/guild/baoxiang/baoxiang_reward_yulang_view")
require("game/guild/baoxiang/baoxiang_get_result_view")
require("game/guild/baoxiang/baoxiang_get_reward_view")
require("game/guild/baoxiang/baoxiang_refresh_alert")
require("game/guild/baoxiang/baoxiang_thank_tips")
require("game/guild/baoxiang/baoxiang_tujie_view")
require("game/guild/baoxiang/lingmai_reward_view")

require("game/guild/guild_child_view/guild_shop_view")
require("game/guild/guild_child_view/guild_sign_view")
require("game/guild/guild_child_view/guild_sign_reward_view")

-- 仙盟
GuildWGCtrl = GuildWGCtrl or BaseClass(BaseWGCtrl)

function GuildWGCtrl:__init()
	if GuildWGCtrl.Instance then
		ErrorLog("[GuildWGCtrl]:Attempt to create singleton twice!")
	end
	GuildWGCtrl.Instance = self

	self.guild_boss_ctrl = GuildBossWGCtrl.New()
	self.data = GuildWGData.New()
	self.view = GuildView.New(GuideModuleName.GuildView)
	self.pop_create_view = GuildPopCreate.New()
	self.pop_info_view = GuildPopInfo.New()
	self.pop_guild_info_view = GuildPopInfoView.New(GuideModuleName.GuildPopInfoView)

	self.pop_setting_view = GuildPopSetting.New()--申请设置
	self.pop_mail_view = GuildPopMail.New() --群发邮件
	self.pop_apply_for_view = GuildPopApplyfor.New()
	self.pop_appoint_view = GuildPopAppoint.New()

	--self.pop_tempbag_view = GuildPopTempBag.New()
	self.guild_list_hebing_view = GuildListHeBing.New()--帮派合并界面
	self.liekun_bosslist_view = LieKunBossListView.New()
	self.guild_hebing_show = GuildHeBingShow.New()
	self.guild_redpacket_tips = GuildRedPacketTips.New()
	self.guild_vipred_tips = GuildVIPRedTips.New()
	self.guild_battle_end_rank_view = GuildBattleEndRankView.New()
	self.guild_battle_end_zongjie_view = GuildBattleEndRankRewardView.New() --总结/连胜奖励显示面板
	self.guild_jiushedaofb_follow_view = GuildJiuSheDaoFBFollow.New()
	self.guild_day_reward_view = GuildDayRewardView.New()
	self.guild_juanxian_view = GuildJuanXianView.New(GuideModuleName.GuildJuanXian)
	self.liekun_follow_view = LieKunFollow.New()
	self.liekun_singlerank_view = LieKunSingleOutPutTips.New(GuideModuleName.LieKunSingleOutPutTips)
	self.liekun_guiderank_view = LieKunOutPutTips.New(GuideModuleName.LieKunGuideOutPutTips)
	self.guild_build_task_view = GuildBuildTaskView.New(GuideModuleName.guild_task)

	self.guild_fb_guwu_btn_view = GuildFbGuWuBtnView.New(GuideModuleName.GuildFbGuWuBtnView) 				--鼓舞按钮
	self.guild_guwu_view = GuildFbGuWuView.New(GuideModuleName.GuildFbGuWuView) 			 				--鼓舞界面
	self.guild_shouhu_jiesuan = GuildShouHuJieSuanView.New(GuideModuleName.GuildFbShouHuJieSuanView) 		--仙盟试炼结算界面
	self.apply_tuanzhang_agent = ApplyTuanZhangAgent.New() --代理盟主界面
	self.select_limit_menmber_panel = GuildMemberListSelectPanel.New()
	self.guild_create_view = GuildCreateView.New()

	self.guild_cangku_data = GuildCangKuWGData.New()
	self.cangku_convert_tips = CangKuConvertTips.New()
	--仙盟活动预览
	self.guild_activity_data = GuildActivityWGData.New()
	self.guild_activity_shouhu_rank = GuildActivityShouHuRank.New(GuideModuleName.GuildActivityShouHuRank)
	self.guild_activity_boss_rank = GuildActivityBossRank.New(GuideModuleName.GuildActivityBossRank)
	self.guild_activity_dati_rank = GuildActivityDaTiRank.New(GuideModuleName.GuildActivityDaTiRank)

	self.guild_war_rule_view = GuildWarRuleView.New(GuideModuleName.GuildWarRuleView)

	self.guild_war_duizheng_view = GuildWarDuiZhengView.New(GuideModuleName.GuildWarDuiZhengView)

	self.guild_war_fenpei_view = GuildWarFeiPeiView.New(GuideModuleName.GuildWarFeiPeiView)

    self.guild_welcome_view = GuildWelcomeView.New()

    self.guild_dalian_view = GuildDaLianView.New()
    self.guild_rename_view = GuildRenameView.New(GuideModuleName.GuildRenameView)

    self.guild_war_zhanbao_view = GuildWarZhanBaoView.New(GuideModuleName.GuildWarZhanBaoView)
    
	self.guild_sign_reward_view = GuildSignRewardView.New()
	self.guild_activity_view = GuildActivityView.New(GuideModuleName.GuildActivityView)

    --每日宝箱
	self.guild_baoxiang_data = GuildBaoXiangWGData.New()
	self.baoxiang_code_view = BaoXiangCodeView.New(GuideModuleName.BaoXiangCodeView)
	self.baoxiang_flush_request_view = BaoXiangFlushRequestView.New(GuideModuleName.BaoXiangFlushRequestView)
	self.baoxiang_flush_view = BaoXiangFlushView.New(GuideModuleName.BaoXiangFlushView)
	self.baoxiang_reward_yulang_view = BaoXiangRewardYuLianView.New(GuideModuleName.BaoXiangRewardYuLianView)
	self.baoxiang_get_result_view = BaoXiangGetResultView.New(GuideModuleName.BaoXiangGetResultView)
	self.baoxiang_get_reward_view = BaoXiangGetRewardView.New(GuideModuleName.BaoXiangGetRewardView)
	self.baoxiang_refresh_alert = BaoXiangRefreshAlert.New(GuideModuleName.BaoXiangRefreshAlert)
	self.baoxiang_thank_tips = BaoXiangThankTips.New(GuideModuleName.BaoXiangThankTips)
	self.baoxiang_tujie_view = BaoXiangTuJieView.New()
	self.lingmai_reward_view = LingMaiRecord.New()
	self.check_box_tip = Alert.New()
	self.alert_qiujiu = nil
	self.is_can_get_tax = false
	self.guild_reward  = false
	self.guild_day_reward = false --帮派每日奖励
	self:RegisterAllProtocols()
	self:RegisterAllEvents()

	self.role_narure_event = BindTool.Bind(self.RoleAttrChange, self)
	RoleWGData.Instance:AddListener(RoleWGData.ATTR_EVENT, self.role_narure_event)

	self.activity_change_callback = BindTool.Bind(self.ActivityChangeCallBack,self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

	self.week_task_click_flag = nil

	self.auto_destory_storge_item_timer = nil
end

--根据名字获取觉得的信息
function GuildWGCtrl:SeqPeopleInfo(id)
	GlobalEventSystem:Fire(OtherEventType.RoleInfo, id)
end

function GuildWGCtrl:__delete()
	self.guild_boss_ctrl:DeleteMe()
	self.guild_boss_ctrl = nil
	self.pop_enlist_view = nil
	self.chat_view = nil

	self.pop_create_view:DeleteMe()
	self.pop_create_view = nil

	self.is_has_scmember_info = nil

	self.pop_info_view:DeleteMe()
	self.pop_info_view = nil

	self.pop_guild_info_view:DeleteMe()
	self.pop_guild_info_view = nil

	self.pop_setting_view:DeleteMe()
	self.pop_setting_view = nil

	if self.apply_tuanzhang_agent then
		self.apply_tuanzhang_agent:DeleteMe()
		self.apply_tuanzhang_agent = nil
	end

	if self.guild_battle_end_zongjie_view then
		self.guild_battle_end_zongjie_view:DeleteMe()
		self.guild_battle_end_zongjie_view = nil
	end

	if self.select_limit_menmber_panel then
		self.select_limit_menmber_panel:DeleteMe()
		self.select_limit_menmber_panel = nil
	end
	self.pop_mail_view:DeleteMe()
	self.pop_mail_view = nil

	self.liekun_follow_view:DeleteMe()
	self.liekun_follow_view = nil

	self.liekun_singlerank_view:DeleteMe()
	self.liekun_singlerank_view = nil

	self.liekun_guiderank_view:DeleteMe()
	self.liekun_guiderank_view = nil

	self.pop_apply_for_view:DeleteMe()
	self.pop_apply_for_view = nil

	self.pop_appoint_view:DeleteMe()
	self.pop_appoint_view = nil

	--self.pop_tempbag_view:DeleteMe()
	--self.pop_tempbag_view = nil

	self.liekun_bosslist_view:DeleteMe()
	self.liekun_bosslist_view = nil

	self.bonfire_view = nil

	self.guild_list_hebing_view:DeleteMe()
	self.guild_list_hebing_view = nil

	self.guild_hebing_show:DeleteMe()
	self.guild_hebing_show = nil

	self.guild_redpacket_tips:DeleteMe()
	self.guild_redpacket_tips = nil

	self.guild_vipred_tips:DeleteMe()
	self.guild_vipred_tips = nil

	self.guild_fb_guwu_btn_view:DeleteMe()
	self.guild_fb_guwu_btn_view = nil

	self.guild_guwu_view:DeleteMe()
	self.guild_guwu_view = nil

	self.guild_shouhu_jiesuan:DeleteMe()
	self.guild_shouhu_jiesuan = nil

	if self.guild_day_reward_view ~= nil then
		self.guild_day_reward_view:DeleteMe()
		self.guild_day_reward_view = nil
	end

	if self.guild_juanxian_view ~= nil then
		self.guild_juanxian_view:DeleteMe()
		self.guild_juanxian_view = nil
	end

	if self.guild_battle_end_rank_view then
		self.guild_battle_end_rank_view:DeleteMe()
		self.guild_battle_end_rank_view = nil
	end

	if self.guild_jiushedaofb_follow_view then
		self.guild_jiushedaofb_follow_view:DeleteMe()
		self.guild_jiushedaofb_follow_view = nil
	end

	if self.guild_build_task_view then
		self.guild_build_task_view:DeleteMe()
		self.guild_build_task_view = nil
	end

	if self.invite_alert then
		self.invite_alert:DeleteMe()
		self.invite_alert = nil
	end

	if self.guild_create_view then
		self.guild_create_view:DeleteMe()
		self.guild_create_view = nil
	end

	if self.guild_cangku_data then
		self.guild_cangku_data:DeleteMe()
		self.guild_cangku_data = nil
	end

	if self.cangku_convert_tips then
		self.cangku_convert_tips:DeleteMe()
		self.cangku_convert_tips = nil
	end

	if self.guild_activity_data then
		self.guild_activity_data:DeleteMe()
		self.guild_activity_data = nil
	end

	if self.guild_activity_shouhu_rank then
		self.guild_activity_shouhu_rank:DeleteMe()
		self.guild_activity_shouhu_rank = nil
	end

	if self.guild_activity_boss_rank then
		self.guild_activity_boss_rank:DeleteMe()
		self.guild_activity_boss_rank = nil
	end

	if self.guild_activity_dati_rank then
		self.guild_activity_dati_rank:DeleteMe()
		self.guild_activity_dati_rank = nil
	end

	if self.guild_war_duizheng_view then
		self.guild_war_duizheng_view:DeleteMe()
		self.guild_war_duizheng_view = nil
	end

	if self.guild_war_rule_view then
		self.guild_war_rule_view:DeleteMe()
		self.guild_war_rule_view = nil
	end

	if self.guild_war_fenpei_view then
		self.guild_war_fenpei_view:DeleteMe()
		self.guild_war_fenpei_view = nil
    end
    
    if self.guild_welcome_view then
		self.guild_welcome_view:DeleteMe()
		self.guild_welcome_view = nil
	end

	if self.guild_dalian_view then
		self.guild_dalian_view:DeleteMe()
		self.guild_dalian_view = nil
	end
	
    if self.guild_rename_view then
		self.guild_rename_view:DeleteMe()
		self.guild_rename_view = nil
	end
    
	if self.guild_baoxiang_data then
		self.guild_baoxiang_data:DeleteMe()
		self.guild_baoxiang_data = nil
	end


	if self.baoxiang_code_view then
		self.baoxiang_code_view:DeleteMe()
		self.baoxiang_code_view = nil
	end

	if self.baoxiang_flush_request_view then
		self.baoxiang_flush_request_view:DeleteMe()
		self.baoxiang_flush_request_view = nil
	end

	if self.baoxiang_flush_view then
		self.baoxiang_flush_view:DeleteMe()
		self.baoxiang_flush_view = nil
	end

	if self.baoxiang_reward_yulang_view then
		self.baoxiang_reward_yulang_view:DeleteMe()
		self.baoxiang_reward_yulang_view = nil
	end

	if self.baoxiang_get_result_view then
		self.baoxiang_get_result_view:DeleteMe()
		self.baoxiang_get_result_view = nil
	end

	if self.baoxiang_get_reward_view then
		self.baoxiang_get_reward_view:DeleteMe()
		self.baoxiang_get_reward_view = nil
	end

	if self.baoxiang_refresh_alert then
		self.baoxiang_refresh_alert:DeleteMe()
		self.baoxiang_refresh_alert = nil
	end

	if self.baoxiang_thank_tips then
		self.baoxiang_thank_tips:DeleteMe()
		self.baoxiang_thank_tips = nil
	end

	if self.guild_war_zhanbao_view then
		self.guild_war_zhanbao_view:DeleteMe()
		self.guild_war_zhanbao_view = nil
	end

	if self.role_narure_event then
		RoleWGData.Instance:RemoveListener(RoleWGData.ATTR_EVENT, self.role_narure_event)
		self.role_narure_event = nil
	end

	if self.check_box_tip then
		self.check_box_tip:DeleteMe()
		self.check_box_tip = nil
	end

	if self.baoxiang_tujie_view then
		self.baoxiang_tujie_view:DeleteMe()
		self.baoxiang_tujie_view = nil
	end

	if self.lingmai_reward_view then
		self.lingmai_reward_view:DeleteMe()
		self.lingmai_reward_view = nil
	end

	if self.guild_sign_reward_view then
		self.guild_sign_reward_view:DeleteMe()
		self.guild_sign_reward_view = nil
	end

	if self.guild_activity_view then
		self.guild_activity_view:DeleteMe()
		self.guild_activity_view = nil
	end

	self:CreatLeventSceneEvent(false)
	self.view:DeleteMe()
	self.view = nil

	self.data:DeleteMe()
	self.data = nil
	GuildWGCtrl.Instance = nil
	if self.delete_member_auto then
		GlobalTimerQuest:CancelQuest(self.delete_member_auto)
		self.delete_member_auto = nil
	end

	if self.activity_change_callback then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
		self.activity_change_callback = nil
	end

	self.week_task_click_flag = nil

	if self.auto_destory_storge_item_timer then
		GlobalTimerQuest:CancelQuest(self.auto_destory_storge_item_timer)
		self.auto_destory_storge_item_timer = nil
	end
end

-- 注册事件
function GuildWGCtrl:RegisterAllEvents()
	self:UpdataRemindNum()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function GuildWGCtrl:UpdataRemindNum()

end

function GuildWGCtrl:MainuiOpenCreate()
	--local guild_id = RoleWGData.Instance.role_vo.guild_id
	--self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, guild_id)
	--if RoleWGData.Instance.role_vo.guild_id ~= 0 then
	--	self:SendSorgeReqInfo()
	--	self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_APPLY_FOR_INFO, guild_id)
	--	self:SendGetAllGuildMemberLuckyInfo()
	--	self:UpdataRemindNum()
	--end
	--
	--local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	--local info_type = GuildDataConst.GUILD_INFO_TYPE
	--if mainrolevo.guild_id > 0 then
	--	self:SendGetGuildInfoReq(info_type.GUILD_INFO, mainrolevo.guild_id)
	--	self:SendGetGuildInfoReq(info_type.ALL_GUILD_BASE_INFO, mainrolevo.guild_id)
	--	self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_APPLY_FOR_INFO, mainrolevo.guild_id)
	--	self:SendGetAllGuildMemberLuckyInfo()
	--	self:UpdataRemindNum()
		if GuildWGData.Instance:GetIsEnterCrossBiPing() then
			GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GUILD_FB_OPERA_TYPE_RANK_CROSS)
		else
			GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GUILD_FB_OPERA_TYPE_RANK)
		end
		GuildWGCtrl.Instance:SendGuildShouHuOperate(GUILD_SHOUHU_OPERA_TYPE.GUILD_FB_OPERA_TYPE_DETAIL_INFO)
	--end
	--RankWGCtrl.Instance:SendRankListReq(RankClientType.XIANMENGZHAN, BindTool.Bind1(self.AckGuildRankList, self))
	--
	--GuildWGCtrl.Instance:SendGuildRedPocketOperate(GUILD_RED_POCKET_OPERATE_TYPE.GUILD_RED_POCKET_OPERATE_INFO_LIST, 0, 0)
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, RoleWGData.Instance.role_vo.guild_id)
	-- self:CheckNeedOpenDaLian()
end

-- 打开仙盟界面
function GuildWGCtrl:Open(tab_index, param_t)
	self:SendGetGuildRankInfo()
	-- RankWGCtrl.Instance:SendRankListReq(RankClientType.XIANMENG_ZHANLI, BindTool.Bind1(self.ZahnLiGuildRankList, self))
	-- 先请求仙盟战 匹配信息 如果是 活动结束 再请求 排行信息 迷之操作 GuildWGCtrl:OnGuildBattleMatchInfo(protocol)
	-- ZhuZaiShenDianWGCtrl.Instance:SendCSGuildBattleRankInfoReq(GUILD_BATTLE_INFO_REQ_TYPE.GUILD_BATTLE_INFO_REQ_TYPE_MATCH)
	local boo = GuildWGData.Instance:GetGuideGuildFlag()

	self:GetAllGuildData()
	local is_quit = param_t and param_t.is_quit or false
	if RoleWGData.Instance.role_vo.guild_id == 0 and not is_quit then
		tab_index = math.floor(TabIndex.guild_guildlist)
	end
	if self.view and self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_War)
	end

	if boo ~= true then
		self.view:Open(tab_index)
		if self.view and self.view:IsOpen() then
			self.view:Flush(TabIndex.guild_cangku)
		end
	else
		GuildWGData.Instance:SetGuideGuild(false)
	end

	if param_t ~= nil then
		if param_t.sub_view_name == SubViewName.CreateGuild then  --这里创建仙盟界面需要同时显示仙盟主界面。不需要则要self.view:Close()
			if RoleWGData.Instance.role_vo.guild_id ~= 0 then
				self.view:Close()
				SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.HasInGuild)
				return
			end
			self:OpenCreateGuildView()
		elseif tab_index == TabIndex.guild_act and param_t.sel_index ~= nil then
			self.view:Flush(TabIndex.guild_act, "select_index", param_t)
		end
	end
end

function GuildWGCtrl:SendGetGuildRankInfo()
	RankWGCtrl.Instance:SendRankListReq(RankClientType.XIANMENG_ZHANLI, BindTool.Bind1(self.ZahnLiGuildRankList, self))
end

function GuildWGCtrl:OpenCreateGuildView()
	self.guild_create_view:Open()
end

function GuildWGCtrl:CloseCreateGuildView()
	self.pop_create_view:Close()
end

function GuildWGCtrl:OpenGuWuBtnView()
	if not self.guild_fb_guwu_btn_view:IsOpen() then
		self.guild_fb_guwu_btn_view:Open()
		self.guild_fb_guwu_btn_view:Flush()
	end
end
function GuildWGCtrl:CloseGuWuBtnView()
	if self.guild_fb_guwu_btn_view:IsOpen() then
		self.guild_fb_guwu_btn_view:Close()
	end
end

function GuildWGCtrl:FlushGuWuBtnView()
	ViewManager.Instance:FlushView(GuideModuleName.GuildFbGuWuBtnView)
end

function GuildWGCtrl:OpenGuWuView()
	if not self.guild_guwu_view:IsOpen() then
		ViewManager.Instance:Open(GuideModuleName.GuildFbGuWuView)
	end
end
function GuildWGCtrl:CloseGuWuView()
	if self.guild_guwu_view:IsOpen() then
		self.guild_guwu_view:Close()
	end
end

function GuildWGCtrl:FlushGuWuView()
	ViewManager.Instance:FlushView(GuideModuleName.GuildFbGuWuView)
end

function GuildWGCtrl:OpenJiwSuanView(data)
	self.guild_shouhu_jiesuan:SetData(data)
	if not self.guild_shouhu_jiesuan:IsOpen() then
		ViewManager.Instance:Open(GuideModuleName.GuildFbShouHuJieSuanView)
	end
end


function GuildWGCtrl:OpenInfoGuildView(guildvo)
	self.pop_info_view:Open(guildvo)
end

function GuildWGCtrl:OpenGuildInfoView( guild_data )
	self.pop_guild_info_view:SetData(guild_data)
	ViewManager.Instance:Open(GuideModuleName.GuildPopInfoView)
	-- self.pop_guild_info_view:Open(guild_data)
end

function GuildWGCtrl:FlushGuildListShow(guildvo)
	--仙盟合并相关，先保留
	-- if self.guild_list_hebing_view:IsOpen() then
		--self.guild_list_hebing_view:FlushGuildListShow(guildvo)
	-- end
	-- if self.guild_hebing_show:IsOpen() then
	-- 	self.guild_hebing_show:FlushGuildListShow(guildvo)
	-- end
end

--function GuildWGCtrl:OpenTempBagView()
--	self.pop_tempbag_view:Open()
--end

-- 这界面不要再用了。用了就坑，玩家会起个仙盟名字是系统公告之类的，然后发招募就GG
function GuildWGCtrl:OpenEnlistView()
	-- self.pop_enlist_view:Open()
end

--申请设置面板
function GuildWGCtrl:OpenSettingView()
	self.pop_setting_view:Open()
end

--发送邮件
function GuildWGCtrl:OpenMailView()
	self.pop_mail_view:Open()
end

--申请列表
function GuildWGCtrl:OpenApplyForView()
	self.pop_apply_for_view:Open()
end

function GuildWGCtrl:IsApplyForViewOpen()
	return self.pop_apply_for_view:IsOpen()
end

function GuildWGCtrl:CloseApplyForView()
	if self.pop_apply_for_view:IsOpen() then
		self.pop_apply_for_view:Close()
	end
end

function GuildWGCtrl:OpenAppointView(data)
	self.pop_appoint_view:Open(data)
end

function GuildWGCtrl:OpenBonfireSelectView(target_obj)
	-- self.bonfire_view:Open(target_obj)
end

function GuildWGCtrl:OpenGuildListHeBingView()
	-- print_error("进入打开帮合并程序")
	if not self.guild_list_hebing_view:IsOpen() then
		self.guild_list_hebing_view:Open()
		--print_error("进入判断帮合并程序")
	end

	if not self.guild_list_hebing_view:IsOpen() then
		self.guild_list_hebing_view:Open()
		-- print_error("进入判断帮合并程序")
	end

end

function GuildWGCtrl:OpenGuildHeBingShowView()
	if not self.guild_hebing_show:IsOpen() then
		self.guild_hebing_show:Open()
	end
end

function GuildWGCtrl:OpenJiuhuiView()
	local is_done = true
	if self.view and not self.view:IsOpen() then
		is_done  =  FunOpen.Instance:OpenViewNameByCfg("guild#guild_activity#uin=guild_act_item,uip=4")  ---????? who write, should modify
	end
	return is_done
end

function GuildWGCtrl:Close()
	self.view:Close()
end

-- 获取仙盟界面
function GuildWGCtrl:GetGuildView()
	return self.view
end

function GuildWGCtrl:GuildInfoFlush(index)

	if index == 0 then
		self.my_guild_lv_rank = RankWGData.Instance:GetMyRank(RankKind.Guild, 5)
		if self.view and self.view:IsOpen() then
			self.view:Flush(TabIndex.guild_info)
		end
	elseif index == 1 then
		self.my_guild_lingdi_lv_rank = RankWGData.Instance:GetMyRank(RankKind.Guild, 1)
		if self.view and self.view:IsOpen() then
			self.view:Flush()
		end
	end
end

function GuildWGCtrl:MyGuildRankLevel()
	if self.my_guild_lv_rank ~= nil then
		return self.my_guild_lv_rank
	end
	return 0
end


function GuildWGCtrl:MyGuildRank()
	if self.my_guild_rank2 ~= nil then
		return self.my_guild_rank2
	end
	return 0
end

function GuildWGCtrl:MyGuildLingDiRankLevel()
	if self.my_guild_lingdi_lv_rank ~= nil then
		return self.my_guild_lingdi_lv_rank
	end
	return 0
end

function GuildWGCtrl:GuildViewFlush(type)
	if self.view and self.view:IsOpen() then
		self.view:Flush(type)
	end
end

-- 提供外部打开仙盟信息界面接口
function GuildWGCtrl:IOpenGuildInfoView(guild_id)
	if nil ~= guild_id then
		GuildDataConst.GUILD_IOPEN.InfoView = true
		self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, guild_id)
	end
end

-- 打开仙盟申请列表界面
function GuildWGCtrl:IOpenGuildApplyforView()

	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	if nil == mainrolevo then
		return
	end
	self:OpenApplyForView()
	self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_APPLY_FOR_INFO, mainrolevo.guild_id)
end

-- 主界面提示仙盟申请
function GuildWGCtrl:MainuiTipApplyfor(is_show)
	-- print_error("主界面提示仙盟申请的按钮")
	local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
	if nil == mainrolevo then
		return
	end

	if not self.data:IsMemberHasPowerToDealApply(mainrolevo.guild_post) then
		is_show = 0
	end

	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.GUILD, is_show, BindTool.Bind1(self.IOpenGuildApplyforView, self))
end

--=================仙盟聊天=================--
function GuildWGCtrl:GetGuildChatView()
	return self.chat_view
end

-- 打开仙盟聊天界面 初始化用
function GuildWGCtrl:OpenGuildChatView()
	-- self.chat_view:Open()
end
-- 拖动聊天面板
function GuildWGCtrl:OpenGuildChatSetViewX(x)
	-- self.chat_view:SetViewPositionX(x)
end
-- 显示 隐藏 仙盟聊天面板
function GuildWGCtrl:SetGuildChatIsShow(is_show)
	-- self.chat_view:SetIsShow(is_show)
end

-- 提供外部接口刷新仙盟聊天信息
function GuildWGCtrl:IRefreshGuildChat()
	-- self.chat_view:RefreshChatContent()
end

-- 提供外部接口获取输入框
function GuildWGCtrl:GetEditText()
	-- return self.chat_view.edit
end

-- 提供外部接口打开仙盟神兽
function GuildWGCtrl:IOpenGuildMonsterView()
	self.view:IOpenGuildMonsterView()
end

-- 提供外部接口打开仙盟守护
function GuildWGCtrl:IOpenGuildGuardView()
	self.view:IOpenGuildGuardView()
end

-- 仙盟聊天界面是否显示
function GuildWGCtrl:IsGuildChatOpen()
	-- return self.chat_view:GetIsShow()
end

--=================仙盟任务=================--
--前往仙盟任务
function GuildWGCtrl:GoToTaskTarget(task_id)
	local task_data = TaskWGData.Instance
	local task_ctrl = TaskWGCtrl.Instance
	if nil == task_data or nil == task_ctrl then
		return
	end
	local task_cfg = task_data:GetTaskConfig(task_id)
	if nil == task_cfg then
		return
	end
	task_ctrl:OperateFollowTask(task_cfg)
end

--战力的回调函数
function GuildWGCtrl:ZahnLiGuildRankList(rank_type, rank_list, protocol)
	if rank_type == RankClientType.XIANMENG_ZHANLI then
		-- local guild_rank = nil
		self.guildvo = GuildDataConst.GUILDVO
		self.my_guild_rank2 = nil

		local self_rank = protocol and protocol.self_rank or -1

		if self_rank > 0 then
			self.my_guild_rank2 = self_rank
		end
	
		if nil == self.my_guild_rank2 then
			for k,v in pairs(rank_list) do
				if v.guild_id == RoleWGData.Instance.role_vo.guild_id then
					self.my_guild_rank2 = k
					break
				end
			end
		end

		ViewManager.Instance:FlushView(GuideModuleName.GuildView)
	end
end

function GuildWGCtrl:AckGuildRankList(rank_type, rank_list)
	local my_guild_rank = nil
	local daty_counter = DayCounterWGData.Instance:GetDayCount(DAY_COUNT.DAYCOUNT_ID_XIANMENGZHAN_RANK_REWARD_TIMES)
	local count = 0
	for i, v in ipairs(rank_list) do
		if i > 5 then
			break
		end
		if v.guild_id == RoleWGData.Instance.role_vo.guild_id then
			if daty_counter < 1 then
				my_guild_rank = i
			end
			self.guild_reward = true
		else
			self.guild_reward = false
		end
	end
	if nil ~= my_guild_rank then
		self.is_can_get_tax = true
	else
		self.is_can_get_tax = false
	end
end

function GuildWGCtrl:SetXianMengZhanRewardCounter(count)
	RankWGCtrl.Instance:SendRankListReq(RankClientType.XIANMENGZHAN, BindTool.Bind1(self.AckGuildRankList, self))
end

-- 每次打开仙盟界面重新拉取数据
function GuildWGCtrl:GetAllGuildData()
	local info_type = GuildDataConst.GUILD_INFO_TYPE
	local guild_id = RoleWGData.Instance.role_vo.guild_id
	self:SendGetGuildInfoReq(info_type.GUILD_INFO, guild_id)
	-- self:SendGetGuildInfoReq(info_type.GUILD_MEMBER_LIST, guild_id)
	-- self:SendGetGuildInfoReq(info_type.ALL_GUILD_BASE_INFO, guild_id)
	if guild_id > 0 then
		-- self:SendSorgeReqInfo()
		self:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_APPLY_FOR_INFO, guild_id)
		-- 暂时先屏蔽
		-- -- GongChengWGCtrl.Instance:SendGongchengzhanOperate(SIEGE_OPERA_TYPE.REQ_INFO)
		self:UpdataRemindNum()
	end
end

function GuildWGCtrl:RefreshView()
	if self.view:IsOpen() then
		self.view:OnFlushGBattleView()
	end
end

function GuildWGCtrl:RefreshChengZhuDisplayView()
	if self.view:IsOpen() then
		self.view:OnFlushChengZhuDisplay()
	end
end

--function GuildWGCtrl:RefreshTempBagView()
	--if self.pop_tempbag_view:IsOpen() then
	--	self.pop_tempbag_view:Flush()
	--end
--end

function GuildWGCtrl:OpenGuildRedPacketView(data,paper_type)
	--if not self.guild_redpacket_tips:IsOpen() then
	self.guild_redpacket_tips:SetDataDetailRecord(data,paper_type)
	--end
end
function GuildWGCtrl:GuildRedPacketTipsFlush()
	self.guild_redpacket_tips:Flush()
end


function GuildWGCtrl:OpenGuildVIPRedView(paper_type,small_type)
	if not self.guild_vipred_tips:IsOpen() then
		self.guild_vipred_tips:SetVIPDataOpen(paper_type,small_type)
	end
end

function GuildWGCtrl:BattleRanklist(protocol)
	self.data:OnGuildBattleRanklist(protocol)
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.guild_War)
	end
end

function GuildWGCtrl:SetOnekeyRequestVisible(is_visible)
	self.view:SetOnekeyRequestVisible(is_visible)
end

function GuildWGCtrl:SetOnekeyZhufuVisible(is_visible)
	self.view:SetOnekeyZhufuVisible(is_visible)
end

function GuildWGCtrl:OpenGuildJiuSheDaoFBFollowView()
	self.guild_jiushedaofb_follow_view:Open()
end

function GuildWGCtrl:CloseGuildJiuSheDaoFBFollowView()
	self.guild_jiushedaofb_follow_view:Close()
end

function GuildWGCtrl:OpenGuildDayRewardView(protocol)
	if self.guild_day_reward then
		self.guild_day_reward_view:SetRewardData(protocol.item_id)
		self.guild_day_reward_view:Open()
		self.guild_day_reward = false
	end
end

--全民庆典开启奖励界面
function GuildWGCtrl:OpenGuildDayRewardViewOther(item_id)
	if self.guild_day_reward_view then
		self.guild_day_reward_view:SetRewardData(item_id)
		self.guild_day_reward_view:Open()
	end
end

function GuildWGCtrl:OpenGuildJuanXinaView()
	self.guild_juanxian_view:Open()
end

function GuildWGCtrl:CloseGuildJuanXinaView()
	self.guild_juanxian_view:Close()
end

function GuildWGCtrl:OpenLkBossListView()
	if self.liekun_bosslist_view then
		self.liekun_bosslist_view:Open()
	end
end

function GuildWGCtrl:OpenLkSingleRankView(select_index,bossmaxhp)
	if self.liekun_singlerank_view then
		ViewManager.Instance:Open(GuideModuleName.LieKunSingleOutPutTips)
		self.liekun_singlerank_view:SetData(select_index,bossmaxhp)
	end
end

function GuildWGCtrl:OpenLkGuildRankView(select_index,bossmaxhp)
	if self.liekun_guiderank_view then
		ViewManager.Instance:Open(GuideModuleName.LieKunGuideOutPutTips)
		self.liekun_guiderank_view:SetData(select_index,bossmaxhp)
	end
end

-- 打开副本逻辑UI
function GuildWGCtrl:OpenLieKunFollow()
	self.liekun_follow_view:Open()
end

function GuildWGCtrl:CloseLieKunFollow()
	self.liekun_follow_view:Close()
end

function GuildWGCtrl:OpenGuildBattleRankedEndView()
	local data ,is_win = GuildWGData.Instance:GetGuildEndDataInfo()
	if data == nil or is_win == nil or is_win == -1 then return end
	if not self.guild_battle_end_rank_view:IsOpen()then

		GuildBattleRankedWGCtrl.Instance:CloseGuildChangeView()
		self.guild_battle_end_rank_view:SetDataList(data, is_win)
		self.guild_battle_end_rank_view:Open()
	end
end

--仙盟聊天主界面按钮
function GuildWGCtrl:OpenMainUiGuildBtn(guild_id)
	if guild_id == 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.CHAT_GUILD,0)
		MainuiWGCtrl.Instance.is_can_show_guildeffect = false
	else
		-- MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.CHAT_GUILD,0,BindTool.Bind1(self.OpenGuildTalk, self))
	end
end

function GuildWGCtrl:OpenGuildTalk()
	local ChatTabIndexGuild = 60 --聊天仙盟
	ChatWGCtrl.Instance:OpenChatWindow(ChatTabIndexGuild)
	MainuiWGCtrl.Instance.is_can_show_guildeffect = false
	MainuiWGCtrl.Instance:FlushGuildIcon()
end

--生成仙盟主界面按钮,按钮类型,界面索引,是否创建
function GuildWGCtrl:CreatMainUiGuildBtn(mainbtn_type,table_index,is_creat)
	local guild_id = GameVoManager.Instance:GetMainRoleVo().guild_id
	if is_creat == 0 then
		MainuiWGCtrl.Instance:InvateTip(mainbtn_type,0)
	else
		if  guild_id == 0 then return end
		MainuiWGCtrl.Instance:InvateTip(mainbtn_type , 1,function ()
	  			FunOpen.Instance:OpenViewByName(GuideModuleName.Guild, table_index)
	  			return true
			  end)
	end
end

--仙盟守护结算面版
function GuildWGCtrl:IsCanShowJieSuanView()
	self.data:IsCanShowJieSuanView()
end

--仙盟资金提醒
function GuildWGCtrl:GuildExeChange(exp)
	if nil ~= exp and  exp > 0  then
		local add_exp = string.format(Language.Bag.GetItemTxt, Language.Bag.Guild_Exp, exp)
		SysMsgWGCtrl.Instance:ErrorRemind(add_exp)
		ChatWGCtrl.Instance:AddMoneyChangeMsg(MoneyType.GuildJianSheDu, exp)
	end
end

--打开仙盟建设面板
function GuildWGCtrl:OpenGuildBuildTaskView()
	if not self.guild_build_task_view:IsOpen() then
		self.guild_build_task_view:Open()
	end
end

--刷新仙盟建设面板
function GuildWGCtrl:FlushGuildBuildTaskView(from)
	if self.guild_build_task_view:IsOpen() then
		self.guild_build_task_view:OnFlushGuildTaskView(from)
	end
end

--刷新仙盟技能面板
function GuildWGCtrl:SelectSkillItemCallBack(data)
	if self.view:IsOpen() then
		self.view:SelectSkillItemCallBack(data)
	end
end

--角色属性变化
function GuildWGCtrl:RoleAttrChange(key)
	if key == "longhun" then
		RemindManager.Instance:Fire(RemindName.Guild_Skill)--帮派技能
	end

end

function GuildWGCtrl:OpenAgentPanel()
	if self.apply_tuanzhang_agent:IsOpen() then
		self.apply_tuanzhang_agent:Flush()
	else
		self.apply_tuanzhang_agent:Open()
	end
end

function GuildWGCtrl:OpenSelectLimitPanel()
	if self.select_limit_menmber_panel:IsOpen() then
		self.select_limit_menmber_panel:Flush()
	else
		self.select_limit_menmber_panel:Open()
	end

end

function GuildWGCtrl:FlushSelectLimitPanel()
	self.select_limit_menmber_panel:Flush()
end

function GuildWGCtrl:OpenLisnShengPasnel()
	self.guild_battle_end_zongjie_view:Open()
end

function GuildWGCtrl:CreatLeventSceneEvent(is_creat)
	--开启一个场景退出的监听事件
	if is_creat and not self.leave_scene_event then
		self.leave_scene_event = GlobalEventSystem:Bind(OtherEventType.FUBEN_QUIT, BindTool.Bind(self.OnRoleLeaveScene, self))
	elseif not is_creat and self.leave_scene_event then
		GlobalEventSystem:UnBind(self.leave_scene_event)
		self.leave_scene_event = nil
	end
end

--仙盟建设s级任务需要在退出场景后，弹出界面
function GuildWGCtrl:OnRoleLeaveScene(scene_type)
	local rember_scene_type = self.data:GetRemberOutScenrType()
	if rember_scene_type and scene_type == rember_scene_type then
		FunOpen.Instance:OpenViewByName(GuideModuleName.guild_task)
		self.data:RemberOutScenrType()
	end

end

function GuildWGCtrl:DeleteMemberDefault()
	if RoleWGData.Instance.role_vo.guild_post ~= GUILD_POST.TUANGZHANG and RoleWGData.Instance.role_vo.guild_post ~= GUILD_POST.JiaMengZhu then
		return
	end
	local m_list = GuildDataConst.GUILD_MEMBER_LIST
	local member_datasource = {}
	for i = 1, m_list.count do
		local item = m_list.list[i]
		local datasource = {uid = item.uid, role_name = item.role_name, level = item.level, sex = item.sex, prof = item.prof,
			post = item.post, vip_type = item.vip_type, vip_level = item.vip_level, is_online = item.is_online, join_time = item.join_time,
			last_login_time = item.last_login_time, gongxian = item.gongxian, total_gongxian = item.total_gongxian, capability = item.capability, photframe = item.photframe,
			sort_index = GuildDataConst.GUILD_POST_SORTINDEX_LIST[item.post],chongzhi_num = item.chongzhi_num}
		table.insert(member_datasource, datasource)
	end
	local baseInfo = GuildWGData.Instance:GetMenmberSelectLimitCfg()
	local limit_list = baseInfo[#baseInfo]

	local temp_list = {}
	local level = limit_list.level or 0
	local capability = limit_list.capability or 0
	local sec = limit_list.time or 0

	local server_time = TimeWGCtrl.Instance:GetServerTime()
--	print_error(level,capability,sec)
	for k,v in pairs(member_datasource) do
		if (v.level <= level or level == 0) and (v.capability <= capability or capability == 0) and ((server_time - v.last_login_time) >= sec * 3600 or sec == 0) and v.chongzhi_num <= 0 then
		--	print_error(limit_list,v.level,v.capability)
			table.insert(temp_list,v)
		end
	end
	if #temp_list > 0 then
		local kickout_list = {}
		for k,v in pairs(temp_list) do
			table.insert(kickout_list,v.uid)
		end
		GuildWGCtrl.Instance:SendKickoutGuildReq(GuildDataConst.GUILDVO.guild_id, #kickout_list, kickout_list)
	end
	self.delete_member_auto = GlobalTimerQuest:AddDelayTimer(function()
		self:DeleteMemberDefault()
	end,900)
end

function GuildWGCtrl:GetAlertQiuJiu()
	return self.alert_qiujiu
end

function GuildWGCtrl:GetIsHasScmemberInfo()
	return self.is_has_scmember_info
end

function GuildWGCtrl:NeedAutoTask()
	if self.data then
		self.data:NeedAutoTask()
	end
end

--邀请加入仙盟提示
function GuildWGCtrl:OpenGuildInviteHint()
	local data = self.data:GetOnInviteNotify()
	if IsEmptyTable(data) then
		return
	end

	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.Guild)
	if not is_open then
		return
	end

	if self.invite_alert == nil then
		self.invite_alert = Alert.New()
	end

	local lable = string.format(Language.Guild.GUILDINVITE, data.invite_name, data.guild_name)
	self.invite_alert:SetLableString(lable)

	self.invite_alert:SetOkFunc(function()
		self:InviteHandler(data, GUILD_INVITE_TYPE.YES_REQ)
	end)

	self.invite_alert:SetCancelFunc(function()
		self:InviteHandler(data, GUILD_INVITE_TYPE.NO_REQ)
	end)

	self.invite_alert:Open()
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.GUILD_INVITE_HINT, 0)
end

--活动状态监听红点
function GuildWGCtrl:ActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type then
		local remind_name =  self.data:IsNeedMonitorRed(activity_type)
		if remind_name then
			RemindManager.Instance:Fire(remind_name)
		end
	end
	if activity_type == ACTIVITY_TYPE.GUILD_FB and status == ACTIVITY_STATUS.STANDY then
		GuildWGData.Instance:ClearGuildFbPass()
	end
	if activity_type == ACTIVITY_TYPE.KF_XIANMENGZHAN then
		GuildWGData.Instance:RemoveKFXianmengzhanTimeCount()
		GuildWGData.Instance:ClearKFXianmengzhanNotice()
		--仙盟战活动结束打开仙盟战报
		local guild_war_is_open = FunOpen.Instance:GetFunIsOpened(FunName.GuildWar)
		local old_status = GuildBattleRankedWGData.Instance:GetCurActStatus()
		GuildBattleRankedWGData.Instance:SetCurActStatus(status)
		if old_status and old_status == ACTIVITY_STATUS.OPEN and status == ACTIVITY_STATUS.CLOSE and guild_war_is_open then
		-- if status == ACTIVITY_STATUS.CLOSE and guild_war_is_open then
			local scene_type = Scene.Instance:GetSceneType()
			if scene_type == SceneType.Common then
				GuildWGCtrl.Instance:ReqCrossXianmengzhanOpera(CROSS_XIANMENGZHAN_OPERA_TYPE.CROSS_XIANMENGZHAN_OPERA_TYPE_QUERY_GUILD_RANK)
				ViewManager.Instance:Open(GuideModuleName.GuildWarZhanBaoView,nil,nil,{auto_close_text = true})
			end
		end
	end
end

function GuildWGCtrl:OpenCangKuConvertTips(convery_id)
	self.cangku_convert_tips:SetData(convery_id)
	if self.cangku_convert_tips:IsOpen() then
		self.cangku_convert_tips:Flush()
	else
		self.cangku_convert_tips:Open()
	end
end

function GuildWGCtrl:GetWeekTaskRemindState()
	if self.data then
		return self.data:GetWeekTaskRemindState()
	end
end

--设置仙盟周任务,未完成时 每次登陆点击状态
function GuildWGCtrl:SetWeekTaskClickFlag(value)
	self.week_task_click_flag = value
end

function GuildWGCtrl:GetWeekTaskClickFlag()
	return self.week_task_click_flag
end

function GuildWGCtrl:OpenGuildWelcomeView()
    --GuildWGCtrl.Instance:OpenGuildWelcomeView()
    self.guild_welcome_view:Open()
end

-- 自动销毁仙盟仓库里面橙色1星及以下的装备
function GuildWGCtrl:AutoDestoryStorgeItem()
	if not self.auto_destory_storge_item_timer then
		self.auto_destory_storge_item_timer = GlobalTimerQuest:AddRunQuest(function()
			self:DestoryStorgeItem()
		end, 180)
	end
end

function GuildWGCtrl:DestoryStorgeItem()
	if not RoleWGData.Instance or not RoleWGData.Instance.role_vo then
		return
	end

	if RoleWGData.Instance.role_vo.guild_post ~= GUILD_POST.TUANGZHANG and RoleWGData.Instance.role_vo.guild_post ~= GUILD_POST.FU_TUANGZHANG then -- 判断是否是盟主或者副盟主
		return 
	end

	if not GuildCangKuWGData.Instance:GetNeedAutoDestory() then
		return 
	end

	local eq_data_list = GuildCangKuWGData.Instance:GetItemList()--获得仓库里的所有物品
	if not eq_data_list then
		return
	end

	local need_destory_list = {}

	for i,v in ipairs(eq_data_list) do
		if v.color < GameEnum.ITEM_COLOR_ORANGE then
			table.insert(need_destory_list, v)
		elseif v.color == GameEnum.ITEM_COLOR_ORANGE and v.star_level <= 1 then
			table.insert(need_destory_list, v)
		end
	end

	local destroy_item_list = {}
	for k,v in ipairs(need_destory_list) do
		destroy_item_list[#destroy_item_list + 1] = {storge_index = v.index, item_id = v.item_id}
    end
    table.sort(destroy_item_list, SortTools.KeyUpperSorter("storge_index"))
	GuildWGCtrl.Instance:SendStorgetDiscardItem(#destroy_item_list, destroy_item_list)
end

function GuildWGCtrl:RoleLevelChange()
	-- self:CheckNeedOpenDaLian()
end

function GuildWGCtrl:CheckNeedOpenDaLian()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "guild_dalian_flag")
	local flag = PlayerPrefsUtil.GetInt(key)
	if flag == 1 then
		return
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	if not act_info or act_info.status ~= ACTIVITY_STATUS.OPEN then
		return
	end

	local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	if not is_open then
		return
	end

	if RoleWGData.Instance.role_vo.guild_id <= 0 then
        return
    end

	local is_over = GuildWGData.Instance:GetMyGuildWarIsOver()
	if is_over then
		return
	end

	local is_guild_war = GuildWGData.Instance:GetMyCanJoinGuildWar()
	if not is_guild_war then
		return
	end

	local my_guild_opponent_guild = GuildWGData.Instance:GetKfMyGuildPpponentGuild()
	if my_guild_opponent_guild and my_guild_opponent_guild.guild_id <= 0 then
		return
	end

	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	if not act_cfg then
		return
	end

	local role_level = RoleWGData.Instance:GetAttr("level")
	if role_level < act_cfg.level or role_level > act_cfg.level_max then
		if not self.attr_change then
			self.attr_change = BindTool.Bind(self.RoleLevelChange, self)
			RoleWGData.Instance:NotifyAttrChange(self.attr_change, {"level"})
		end
		return
	elseif self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end
	
	ViewManager.Instance:OpenByQueue(self.guild_dalian_view)
	--self.guild_dalian_view:Open()
end

function GuildWGCtrl:SetSendFlushCountDown()
	if self.view and self.view:IsOpen() and self.view:IsLoadedIndex(TabIndex.guild_baoxiang) then
		self.view:SetSendFlushCountDown()
	end
end

function GuildWGCtrl:OpenKFBossGatherShareTip(gather_id,left_gather_time)
	if gather_id and gather_id > 0 and left_gather_time and left_gather_time > 1 then
		local gather_config = ConfigManager.Instance:GetAutoConfig("gather_auto").gather_list[gather_id]
		local main_role = Scene.Instance.main_role
		if nil ~= main_role then
			local x, y = main_role:GetLogicPos()
			local scene_key = 1 or 0
			local open_line = 1 or 0
			-- 如果此场景不能分线
			if open_line <= 0 then
				scene_key = -1
			end
			local main_role_vo = main_role.vo
			local scene_id = Scene.Instance:GetSceneId()

			local ok_func = function ()
				-- local pos_msg = string.format(Language.Chat.PosFormat, Scene.Instance:GetSceneName(), x, y,main_role_vo.cur_plat_name,main_role_vo.current_server_id)
				-- ChatWGData.Instance:InsertPointTab(Scene.Instance:GetSceneName(), x, y, scene_id, scene_key,main_role_vo.cur_plat_name,main_role_vo.current_server_id)
				-- local message = string.format(Language.Boss.FindShuiJing,pos_msg,gather_config.name)
				-- message = ChatWGData.Instance:FormattingMsg(message, CHAT_CONTENT_TYPE.TEXT)

				local pos_msg = string.format(Language.Boss.FindPos, Scene.Instance:GetSceneName(), x, y)
				local message = string.format(Language.Boss.FindShuiJing,pos_msg,gather_config.show_name)
				message = message..string.format(Language.Boss.FindShuiJingLink,scene_id,x, y,main_role_vo.cur_plat_name)
				ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, message, CHAT_CONTENT_TYPE.TEXT)
			end

			local cancle_func = function ()
				local pos_msg = string.format(Language.Boss.FindPos, Scene.Instance:GetSceneName(), x, y)
				local message = string.format(Language.Boss.FindShuiJing,pos_msg,gather_config.show_name)
				message = message..string.format(Language.Boss.FindShuiJingLink,scene_id,x, y,main_role_vo.cur_plat_name)
				ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD, message, CHAT_CONTENT_TYPE.TEXT)
			end

			if not self.check_box_tip then
				self.check_box_tip = Alert.New()
			end

			local text_dec = Language.Boss.CheckTellGuild
			local text_dec2 = Language.Boss.CheckTellGuild2
			local today_str = "gather_shuijing"
			local chect_text_str = Language.TreasureHunt.NoRemind
			self.check_box_tip:SetCheckBoxDefaultSelect(false,today_str)
			self.check_box_tip:SetShowCheckBox(true)
			self.check_box_tip:SetCheckBoxText(chect_text_str)

			if not self.data:HasGuild() then
				self.check_box_tip:SetLableString(text_dec2)
				self.check_box_tip:SetUseOneSign(true)
				self.check_box_tip:SetOkString(Language.Boss.WorldShare)
				self.check_box_tip:SetOkFunc(cancle_func)
			else
				self.check_box_tip:SetLableString(text_dec)
				self.check_box_tip:SetUseOneSign(false)
				self.check_box_tip:SetOkString(Language.Boss.GuildShare)
				self.check_box_tip:SetCancelString(Language.Boss.WorldShare)
				self.check_box_tip:SetOkFunc(ok_func)
				self.check_box_tip:SetCancelFunc(cancle_func)
			end
			self.check_box_tip:Open()
		end
	end
end

function GuildWGCtrl:OpenBaoXiangTuJieView()
	if not self.baoxiang_tujie_view:IsOpen() then
		self.baoxiang_tujie_view:Open()
	else
		self.baoxiang_tujie_view:Flush()
	end
end

function GuildWGCtrl:OpenLingMaiRewardView()
	if not self.lingmai_reward_view:IsOpen() then
		self.lingmai_reward_view:Open()
	else
		self.lingmai_reward_view:Flush()
	end
end

-- {reward_list = {}}
function GuildWGCtrl:OpenGuildSignRewardView(show_data)
	self.guild_sign_reward_view:SetDataAndOpen(show_data)
end