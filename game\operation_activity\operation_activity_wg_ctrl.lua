require("game/operation_activity/operation_activity_wg_data")
require("game/operation_activity/operation_activity_view")
require("game/operation_activity/operation_act_render")
require("game/operation_activity/operation_activity_btn_view")

require("game/operation_activity/ctn_recharge/operation_ctn_recharge_items")
require("game/operation_activity/ctn_recharge/operation_ctn_recharge_view")
require("game/operation_activity/task_chain/operation_task_chain_view")
require("game/operation_activity/operation_activity_first_recharge/activity_first_recharge_view")
require("game/operation_activity/operation_activity_leichong_recharge/activity_leichong_recharge_view")
require("game/operation_activity/fengzheng_duobao/fengzheng_get_reward_view")
require("game/operation_activity/mowu_jianglin/mowu_jianglin_view")
require("game/operation_activity/mowang_youli/operation_mowang_view")
require("game/operation_activity/operation_quanfu_juanxian/operation_juanxian_view")
require("game/operation_activity/duobei/activity_duobei_view")
require("game/operation_activity/denglu_youli/login_reward_view")
require("game/operation_activity/denglu_youli/login_reward_item")
require("game/operation_activity/turntable/oa_turntable_view")
require("game/operation_activity/fish/oa_fish_view")
require("game/operation_activity/exchange_shop/exchange_shop_view")
require("game/operation_activity/watering_flowers/watering_flower_view")
require("game/operation_activity/operation_happy_kuanghuan/operation_happy_kuanghuan_view")
require("game/operation_activity/watering_flowers/watering_reward_view")
require("game/operation_activity/recharge_rank/operation_activity_recharge_rank")

OperationActivityWGCtrl = OperationActivityWGCtrl or BaseClass(BaseWGCtrl)
function OperationActivityWGCtrl:__init()
	if OperationActivityWGCtrl.Instance then
		ErrorLog("[OperationActivityWGCtrl] Attemp to create a singleton twice !")
	end
	OperationActivityWGCtrl.Instance = self
	
	self.operation_activity_data = OperationActivityWGData.New()
	self.operation_activity_view = OperationActivityView.New(GuideModuleName.OperationActivityView)
	self.watering_reward_View = WateringRewardView.New()
	self.hot_update_list = {}

	self:BindGlobalEvent(HotUpdateEvent.HOT_UPDATE_FINISH, BindTool.Bind1(self.HotUpdate, self))

	self:ListenHotUpdate("config/auto_new/operation_activity_config_auto", BindTool.Bind(self.HotUpdateAoperationZhuTi, self))

	self:RegisterAllProtocols()

	self.item_data_event = BindTool.Bind1(self.BagItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function OperationActivityWGCtrl:__delete()
	OperationActivityWGCtrl.Instance = nil

	if self.operation_activity_data ~= nil then
		self.operation_activity_data:DeleteMe()
		self.operation_activity_data = nil
	end

	if self.operation_activity_view ~= nil then
		self.operation_activity_view:DeleteMe()
		self.operation_activity_view = nil
	end
	if self.watering_reward_View ~= nil then
		self.watering_reward_View:DeleteMe()
		self.watering_reward_View = nil
	end
	self.hot_update_list = {}

	if self.item_data_event then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
		self.item_data_event = nil
	end
end

function OperationActivityWGCtrl:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	OperationActivityWGData.Instance:BagItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	OperationJuanXianWGData.Instance:JxOnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	ChuShenWGData.Instance:ChuShenItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
end

function OperationActivityWGCtrl:RegisterAllProtocols()
	self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind(self.OnDayChange, self))
end

-- 热更新
function OperationActivityWGCtrl:HotUpdate(lua_file_list)
	for k, v in pairs(lua_file_list) do
		if self.hot_update_list[v] then
			UnRequire(v)
			ConfigManager.Instance:DelCacheConfig(v)
			require(v)
			self.hot_update_list[v]()
		end
	end
end

function OperationActivityWGCtrl:ListenHotUpdate(key, func)
	self.hot_update_list[key] = func
end

function OperationActivityWGCtrl:Open(tab_index, param_t, open_type)
	local tab_index = tab_index
	if nil == tab_index and open_type then
		self.operation_activity_data:RemberCurOpenViewType(open_type)
		tab_index = self:GetFirstOpenActivity()
	elseif nil == tab_index then --这一步只是为了防止出错而已为了兼容报错的情况下可走
		tab_index = self:GetFirstOpenActivity()
	end

	local activity_type = self.operation_activity_data:GetCurSelectActivityType(tab_index)
	local state = self.operation_activity_data:GetActivityState(activity_type)
	if not state then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OperationActivity.ActivityNoOpenHint)
		return
	end

	if nil == open_type then
		--设置当前界面打开的类型
		local activity_type = OperationActivityWGData.Instance:GetCurSelectActivityType(tab_index)
		local cfg = ActivityWGData.Instance:GetCanLookActivityInfo(activity_type)
		if cfg then
			--界面打开时如果强制跳转到另一种类型那么就重新创建tabbar
			if self.operation_activity_view:IsOpen() then
				local cur_type = self.operation_activity_data:GetCurOpenViewType()
				self.operation_activity_data:RemberCurOpenViewType(cfg.btn_type)
				if cur_type ~= cfg.btn_type then
					self.operation_activity_view:CreatTabbar()
				end
			end
			self.operation_activity_data:RemberCurOpenViewType(cfg.btn_type)
		end
	end
	
	self.operation_activity_view:Open(tab_index)
end

-- 获取排序后第一个开启的活动
function OperationActivityWGCtrl:GetFirstOpenActivity()
	local first_tab_index =self.operation_activity_data:GetOneOpenTabIndex()
	return first_tab_index
end

function OperationActivityWGCtrl:FlushView(...)
	-- 防止为空
	if self.operation_activity_view then
		self.operation_activity_view:Flush(...)
	end
end

function OperationActivityWGCtrl:GetViewIsOpen()
	if self.operation_activity_view then
		return self.operation_activity_view:IsOpen()
	end

	return false
end

function OperationActivityWGCtrl:OnDayChange()
	ExchangeShopWGCtrl.Instance:OnDayChange()
end

function OperationActivityWGCtrl:GetViewInShowIndex(tab_index)
	if self.operation_activity_view and self.operation_activity_view:IsOpen() and self.operation_activity_view:GetShowIndex() == tab_index then
		return self.operation_activity_view
	end
end

function OperationActivityWGCtrl:ChangeSelectIndex()
	--活动关闭的时候对页签进行一次刷新并且重新做选中
	if self.operation_activity_view:IsOpen() then
		self.operation_activity_view:SetTabSate()
		--如果当前选择的活动是开启状态那么返回
		local index = self.operation_activity_view:GetShowIndex() or 0
		local activity_type = self.operation_activity_data:GetCurSelectActivityType(index)
		local is_open = self.operation_activity_data:GetActivityState(activity_type)
		if is_open then
			return
		end
		self:Open() --有意传空让他做选择
	end
end

function OperationActivityWGCtrl:GetActivityIsOpen(tab_index)
	if not tab_index or not TabIndex[tab_index] then
		return false
	end

	local activity_type = self.operation_activity_data:GetCurSelectActivityType(TabIndex[tab_index])
	local state = self.operation_activity_data:GetActivityState(activity_type)

	if state then
		return true
	end

	return false
end

function OperationActivityWGCtrl:HotUpdateAoperationZhuTi()
	if self.operation_activity_view:IsOpen() then
		self.operation_activity_view:SetBottomInfo()
	end
end

function OperationActivityWGCtrl:GetIsDoTween()
	if self.operation_activity_view:IsOpen() and self.operation_activity_view:IsLoadedIndex(TabIndex.operation_act_fish) then
		return self.operation_activity_view.fish_is_draw_tweening
	end
end

function OperationActivityWGCtrl:GetFishDrawFlag()
	return self.operation_activity_view and self.operation_activity_view:GetFishDrawFlag()
end

function OperationActivityWGCtrl:OnClickFishDrawFlag()
    if self.operation_activity_view then
        self.operation_activity_view:OnClickFishDrawFlag()
    end
end

function OperationActivityWGCtrl:ShowWateringReward(data_list, status, call_back)
	self.watering_reward_View:Flush(0, "gift_info", {data_list = data_list, status = status, call_back = call_back})
	self.watering_reward_View:Open()
end