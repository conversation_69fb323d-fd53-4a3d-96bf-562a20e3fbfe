-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

local ResUtil = require "lib/resmanager/res_util"
local UnitySceneManager = UnityEngine.SceneManagement.SceneManager
local UnityLoadSceneMode = UnityEngine.SceneManagement.LoadSceneMode
local SceneSingleLoadMode = UnityLoadSceneMode.Single
local SceneAdditiveLoadMode = UnityLoadSceneMode.Additive

local M = ResUtil.create_class()

function M:_init()
	self.v_is_loading = false
	self.v_bundle_name = nil
	self.v_asset_name = nil
	self.v_loaded_bundle_name = nil
end

function M:Update()
	if not self.v_is_loading then
		if self.not_need_load then
			if self.v_callback then
				local callback = self.v_callback
				self.v_callback = nil

				callback(true)
			end

			self.not_need_load = nil
		end

		return
	end

	if self.v_loadsceneop and self.v_loadsceneop.isDone then
		self.v_loadsceneop = nil
		self:OnLoadLevelComplete(self.v_bundle_name)
	end
end

function M:OnLoadLevelComplete(bundle_name)
	self.v_loaded_bundle_name = bundle_name
	self.v_is_loading = false
	if self.v_callback then
		local callback = self.v_callback
		self.v_callback = nil

		callback(true)
	end

	self:TryNextLoad()
end

function M:TryNextLoad()
	if nil == self.v_bundle_name then
		return
	end

	if self.v_need_reload then
		self.v_need_reload = false
		if self.v_sync then
			self:LoadLevelSync(self.v_bundle_name, self.v_asset_name, self.v_load_mode, self.v_next_callback, true)
		else
			self:LoadLevelAsync(self.v_bundle_name, self.v_asset_name, self.v_load_mode, self.v_next_callback, true)
		end
	end
end

function M:LoadLevelAsync(bundle_name, asset_name, load_mode, callback, force)
	if not force and not self:_CheckNeedLoad(bundle_name, asset_name) then
		self.not_need_load = true
		self.v_callback = callback
		return
	end

	self.v_bundle_name = bundle_name
	self.v_asset_name = asset_name
	self.v_load_mode = load_mode
	self.v_next_callback = nil
	self.v_need_reload = false
	self.v_sync = false

	if self.v_is_loading then
		self.v_next_callback = callback
		self.v_need_reload = true
		return
	end

	self.v_callback = callback
	self.v_is_loading = true
	self.v_loadsceneop = nil

	BundleCache:SetOverrideCacheTime(bundle_name, 0)
	ResMgr:LoadUnitySceneAsync(bundle_name, asset_name, load_mode, function(loadscene_op)
		if not self:IsSameScene(bundle_name, asset_name) then
			ResMgr:UnloadScene(bundle_name)
			return
		end

		if nil == loadscene_op then
			self.v_bundle_name = nil
			self.v_asset_name = nil
			self.v_is_loading = false

			print_error("[SceneLoader]异步加载场景失败:", bundle_name, asset_name)
			if nil ~= self.v_callback then
				self.v_callback(false)
				self.v_callback = nil
			end
			return
		end

		self.v_loadsceneop = loadscene_op
	end)
end

function M:LoadLevelSync(bundle_name, asset_name, load_mode, callback, force)
	if not force and not self:_CheckNeedLoad(bundle_name, asset_name) then
		self.not_need_load = true
		self.v_callback = callback
		return
	end

	self.v_bundle_name = bundle_name
	self.v_asset_name = asset_name
	self.v_load_mode = load_mode
	self.v_next_callback = nil
	self.v_need_reload = false
	self.v_sync = true

	if self.v_is_loading then
		self.v_next_callback = callback
		self.v_need_reload = true
		return
	end

	self.v_callback = callback
	self.v_is_loading = true
	self.v_loadsceneop = nil

	BundleCache:SetOverrideCacheTime(bundle_name, 0)
	ResMgr:LoadUnitySceneSync(bundle_name, asset_name, load_mode, function(is_succ)
		if not is_succ then
			self.v_bundle_name = nil
			self.v_asset_name = nil
			self.v_is_loading = false
			print_error("[SceneLoader]同步加载场景失败:", bundle_name, asset_name)
			if nil ~= self.v_callback then
				self.v_callback(false)
				self.v_callback = nil
			end
			return
		end

		-- 如果加载完成的场景不是最新请求的
		if not self:IsSameScene(bundle_name, asset_name) then
			if is_succ then
				ResMgr:UnloadScene(bundle_name)
			end
			self.v_is_loading = false
			self:TryNextLoad()
			return
		end

		self:OnLoadLevelComplete(bundle_name)
	end)

end

function M:Destroy()
	if nil ~= self.v_loaded_bundle_name then
		ResMgr:UnloadScene(self.v_loaded_bundle_name)
	end
	self.v_loaded_bundle_name = nil

	self.v_bundle_name = nil
	self.v_asset_name = nil
	self.v_need_reload = nil
end

function M:IsSameScene(bundle_name, asset_name)
	return self.v_bundle_name == bundle_name and self.v_asset_name == asset_name
end

function M:_CheckNeedLoad(bundle_name, asset_name)
	if self.v_bundle_name == bundle_name and self.v_asset_name == asset_name then
		return false
	end

	return true
end

return M