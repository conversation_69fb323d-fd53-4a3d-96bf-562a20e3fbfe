FuBenTeamCommonBossWGData = FuBenTeamCommonBossWGData or BaseClass()

function FuBenTeamCommonBossWGData:__init()
	if FuBenTeamCommonBossWGData.Instance ~= nil then
		<PERSON><PERSON><PERSON><PERSON><PERSON>("[FuBenTeamCommonBossWGData] attempt to create singleton twice!")
		return
	end

	FuBenTeamCommonBossWGData.Instance = self

	self:InitCfg()
    self.fb_info_list = {}
    self.fb_reward_info= {}
    self.fuben_enter_times_list = {}
    self.fuben_hurt_list = {}
    self.show_mvp_info = {}
    self.mvp_like_list = {}
    self.spe_send_flower_list = {}
end

FuBenTeamCommonBossWGData.TaskType = {
    Monster = 1,
    Gather = 2,
    Npc = 3,
}

function FuBenTeamCommonBossWGData:__delete()
	FuBenTeamCommonBossWGData.Instance = nil
end

function FuBenTeamCommonBossWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("fb_team_commom_boss_auto")
    self.fb_seq_cfg = ListToMap(cfg.fb, "seq")
    self.fb_type_model_cfg = ListToMap(cfg.fb, "team_type","team_mode")
    self.scene_cfg = ListToMap(cfg.fb, "scene_id")
    self.team_type_cfg = ListToMap(cfg.team_type, "team_type")
    self.stage_cfg = ListToMap(cfg.stage, "fb_seq", "stage")
    self.monster_cfg = ListToMap(cfg.monster, "seq")
    self.gather_cfg = ListToMap(cfg.gather, "seq", "index")
    self.pass_reward_cfg = ListToMapList(cfg.pass_reward, "fb_seq")
    self.npc_task_cfg = ListToMap(cfg.npc_task, "npc_id")
    self.other_cfg = cfg.other[1]
end

function FuBenTeamCommonBossWGData:SetTeamCommonBossFBInfo(protocol)
    local data = {}
    data.fb_seq = protocol.fb_seq
    data.stage = protocol.stage
    data.next_stage_time = protocol.next_stage_time
    data.start_stage_time = protocol.start_stage_time
    data.kill_monster_count = protocol.kill_monster_count
    data.is_end = protocol.is_end
    data.is_pass = protocol.is_pass
    data.kick_out_time = protocol.kick_out_time
    data.fb_end_time = protocol.fb_end_time
    data.fb_start_time = protocol.fb_start_time
    data.fb_finish_time = protocol.fb_finish_time
    data.need_gather_times = protocol.need_gather_times
    data.has_gather_times = protocol.has_gather_times
    self.fb_info_list[protocol.fb_seq] = data
end

function FuBenTeamCommonBossWGData:SetTeamCommonBossPlayerInfo(protocol)
    local data = {}
    data.fb_seq = protocol.fb_seq
    data.get_exp = protocol.get_exp
    data.is_help = protocol.is_help
    data.reward_item_list = protocol.reward_item_list
    data.team_leader_reward_list = protocol.team_leader_reward_list
    self.fb_reward_info[protocol.fb_seq] = data
end

function FuBenTeamCommonBossWGData:SetTeamCommonBossFBHurtInfo(protocol)
    self.fuben_hurt_list = protocol.team_hurt_list
    if not IsEmptyTable(self.fuben_hurt_list) then
        table.sort(self.fuben_hurt_list, SortTools.KeyUpperSorters("hurt_count", "count"))
    end
end

function FuBenTeamCommonBossWGData:SetTeamCommonBossFBShowMvpInfo(protocol)
    self.show_mvp_info = protocol.mvp_player_info
end

function FuBenTeamCommonBossWGData:SetTeamCommonBossFBMvpLike(protocol)
    local data = {}
    data.type = protocol.type
    data.player_uuid = protocol.player_info.player_uuid
    data.player_name = protocol.player_info.player_name
    data.player_is_robot = protocol.player_info.player_is_robot
    data.sex = protocol.player_info.sex
    data.avatar_timestamp = protocol.player_info.avatar_timestamp
    data.shizhuang_photoframe = protocol.player_info.shizhuang_photoframe
    data.prof = protocol.player_info.prof

    table.insert(self.mvp_like_list, data)

    -- 送花
    if protocol.type == 2 then
        self:AddSpeSendFlowerList(data)
    end
end

function FuBenTeamCommonBossWGData:RestTeamCommonBossFBMvpLike()
    self.mvp_like_list = {}
end

function FuBenTeamCommonBossWGData:GetTeamCommonBossFBInfo(fb_seq)
    return self.fb_info_list[fb_seq]
end

function FuBenTeamCommonBossWGData:GetTeamCommonBossPlayerInfo(fb_seq)
    return self.fb_reward_info[fb_seq]
end

function FuBenTeamCommonBossWGData:GetAllTeamCommonBossHurtInfo()
    return self.fuben_hurt_list
end

function FuBenTeamCommonBossWGData:GetTeamCommonShowMvpInfo()
    return self.show_mvp_info
end

function FuBenTeamCommonBossWGData:GetTeamCommonMvpLikeInfo()
    if IsEmptyTable(self.mvp_like_list) then
        return false, {}
    else
        local info = table.remove(self.mvp_like_list, 1)
        return true, info
    end
end

--设置副本每天已进入次数
function FuBenTeamCommonBossWGData:SetFuBenEnterTimes(team_type, times)
	self.fuben_enter_times_list[team_type] = times
end

--获取副本每天已进入次数
function FuBenTeamCommonBossWGData:GetFuBenEnterTimes(team_type)
	return self.fuben_enter_times_list[team_type] or 0
end

function FuBenTeamCommonBossWGData:GetFuBenTypeModelCfg(team_type, team_mode)
    if team_mode then
        return (self.fb_type_model_cfg[team_type] or {})[team_mode]
    else
        return self.fb_type_model_cfg[team_type]
    end
end

function FuBenTeamCommonBossWGData:GetFuBenCfgBySeq(fb_seq)
    return self.fb_seq_cfg[fb_seq]
end

function FuBenTeamCommonBossWGData:GetFuBenTeamTypeCfg(team_type)
    return self.team_type_cfg[team_type]
end

function FuBenTeamCommonBossWGData:GetFuBenSceneCfg(scene_id)
    return self.scene_cfg[scene_id]
end

function FuBenTeamCommonBossWGData:GetFuBenPassRewardCfg(fb_seq)
    return self.pass_reward_cfg[fb_seq]
end

function FuBenTeamCommonBossWGData:GetFuBenStageCfg(fb_seq, stage)
    if stage then
        return (self.stage_cfg[fb_seq] or {})[stage]
    else
        return self.stage_cfg[fb_seq]
    end
end

function FuBenTeamCommonBossWGData:GetFuBenMonsterCfg(monster_seq)
   return self.monster_cfg[monster_seq]
end

function FuBenTeamCommonBossWGData:GetFuBenGatherCfg(gather_seq, index)
    if index then
        return (self.gather_cfg[gather_seq] or {})[index]
    else
        return self.gather_cfg[gather_seq]
    end
end

function FuBenTeamCommonBossWGData:GetFuBenNpcTaskTCfg(npc_id)
    return self.npc_task_cfg[npc_id]
end

function FuBenTeamCommonBossWGData:GetFuBenOtherCfg()
    return self.other_cfg
end

function FuBenTeamCommonBossWGData:SelectRandGatherObj()
    local main_role = Scene.Instance:GetMainRole()
    if main_role == nil then
        return nil
    end

    local obj_list = {}
    local gather_obj_list = Scene.Instance:GetObjListByType(SceneObjType.GatherObj)
    for _, v in pairs(gather_obj_list) do
        if not v:IsDeleted() then
            table.insert(obj_list, v)
        end
    end

    if #obj_list > 0 then
        return obj_list[1]
    end

    return nil
end

function FuBenTeamCommonBossWGData:SetGetIsHelpState(is_help)
    if is_help then
        self.win_show_is_help = is_help
    else
       return self.win_show_is_help or 0
    end
end

function FuBenTeamCommonBossWGData:SetGetLeaderReward(team_leader_reward_list)
    if team_leader_reward_list then
        self.team_leader_reward_list = team_leader_reward_list
    else
       return self.team_leader_reward_list or {}
    end
end

--获取最大伤害值
function FuBenTeamCommonBossWGData:GetNormalHurtInfoMaxValue()
	local hurt = 1
	if self.fuben_hurt_list[1] and self.fuben_hurt_list[1].hurt_count > hurt then
		hurt = self.fuben_hurt_list[1].hurt_count
	end

	return hurt
end

----送花骚操作
function FuBenTeamCommonBossWGData:AddSpeSendFlowerList(data)
    if #self.spe_send_flower_list >= 4 then
        table.remove(self.spe_send_flower_list, 1)
    end

    table.insert(self.spe_send_flower_list, data)
end

function FuBenTeamCommonBossWGData:GetSpeSendFlowerList()
    return self.spe_send_flower_list
end

function FuBenTeamCommonBossWGData:CleanSpeSendFlowerList()
    self.spe_send_flower_list = {}
end

function FuBenTeamCommonBossWGData:GetSpeSendFlowerInfoByIndex(index)
    local info = {}
    if self.spe_send_flower_list[index] then
        info = table.remove(self.spe_send_flower_list, index)
    end

    return info
end