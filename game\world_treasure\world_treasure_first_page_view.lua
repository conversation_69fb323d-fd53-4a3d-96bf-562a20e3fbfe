-- 通天降临-首页

function WorldTreasureView:FirstPageReleaseCallBack()

end

function WorldTreasureView:FirstPageLoadCallBack()
	for i = 1, 4 do
		XUI.AddClickEventListener(self.node_list["btn_jump_" .. i], BindTool.Bind(self.OnClickFirstPageJumpBtn, self, i))
	end
end

function WorldTreasureView:FirstPageShowIndexCallBack()

end

function WorldTreasureView:FirstPageOnFlush(param_t)
	local cfg_list = WorldTreasureWGData.Instance:GetFirstPageJumpCfgList()
	local bundle, asset = ResPath.GetMainUIIcon(cfg_list)
	for i = 1, 4 do
		if cfg_list[i] then
			bundle, asset = ResPath.GetMainUIIcon(cfg_list[i].image_icon)
			self.node_list["btn_jump_" .. i].image:LoadSprite(bundle, asset)
		end
	end
end

function WorldTreasureView:OnClickFirstPageJumpBtn(index)
	local jump_path = WorldTreasureWGData.Instance:GetFirstPageJumpPath(index)
	if jump_path and jump_path ~= "" then
		FunOpen.Instance:OpenViewNameByCfg(jump_path)
	end
end