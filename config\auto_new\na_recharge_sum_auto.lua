-- X-新节日活动-累充活动.xls
local item_table={
[1]={item_id=26193,num=1,is_bind=1},
[2]={item_id=22753,num=1,is_bind=1},
[3]={item_id=44085,num=1,is_bind=1},
[4]={item_id=27612,num=5,is_bind=1},
[5]={item_id=22076,num=50,is_bind=1},
[6]={item_id=48120,num=1,is_bind=1},
[7]={item_id=44050,num=1,is_bind=1},
[8]={item_id=48089,num=1,is_bind=1},
[9]={item_id=27612,num=8,is_bind=1},
[10]={item_id=22076,num=250,is_bind=1},
[11]={item_id=48443,num=1,is_bind=1},
[12]={item_id=27613,num=1,is_bind=1},
[13]={item_id=27612,num=10,is_bind=1},
[14]={item_id=22076,num=500,is_bind=1},
[15]={item_id=48442,num=1,is_bind=1},
[16]={item_id=26507,num=1,is_bind=1},
[17]={item_id=27612,num=15,is_bind=1},
[18]={item_id=22076,num=1000,is_bind=1},
[19]={item_id=27800,num=1,is_bind=1},
[20]={item_id=26508,num=1,is_bind=1},
[21]={item_id=27613,num=5,is_bind=1},
[22]={item_id=22076,num=2500,is_bind=1},
[23]={item_id=48442,num=3,is_bind=1},
[24]={item_id=26463,num=1,is_bind=1},
[25]={item_id=26194,num=1,is_bind=1},
[26]={item_id=27613,num=8,is_bind=1},
[27]={item_id=45310,num=1,is_bind=1},
[28]={item_id=26462,num=1,is_bind=1},
[29]={item_id=27613,num=10,is_bind=1},
[30]={item_id=22076,num=3000,is_bind=1},
[31]={item_id=45309,num=1,is_bind=1},
[32]={item_id=26464,num=1,is_bind=1},
[33]={item_id=27613,num=12,is_bind=1},
[34]={item_id=45308,num=1,is_bind=1},
[35]={item_id=26463,num=2,is_bind=1},
[36]={item_id=27613,num=15,is_bind=1},
[37]={item_id=45307,num=1,is_bind=1},
[38]={item_id=26464,num=2,is_bind=1},
[39]={item_id=26462,num=2,is_bind=1},
[40]={item_id=27613,num=20,is_bind=1},
[41]={item_id=22076,num=5000,is_bind=1},
[42]={item_id=45306,num=1,is_bind=1},
[43]={item_id=26560,num=3,is_bind=1},
[44]={item_id=26561,num=3,is_bind=1},
[45]={item_id=23354,num=1,is_bind=1},
[46]={item_id=26104,num=1,is_bind=1},
[47]={item_id=26464,num=3,is_bind=1},
[48]={item_id=26554,num=3,is_bind=1},
[49]={item_id=58440,num=1,is_bind=1},
[50]={item_id=26464,num=5,is_bind=1},
[51]={item_id=26547,num=3,is_bind=1},
[52]={item_id=26548,num=3,is_bind=1},
[53]={item_id=26191,num=1,is_bind=1},
[54]={item_id=23683,num=3,is_bind=1},
[55]={item_id=26505,num=1,is_bind=1},
[56]={item_id=46048,num=2,is_bind=1},
[57]={item_id=22076,num=10,is_bind=1},
}

return {
condition_award={
{},
{seq=1,condition=9998,reward_item_list={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{seq=2,condition=49998,reward_item_list={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10]},},
{seq=3,condition=99998,reward_item_list={[0]=item_table[11],[1]=item_table[7],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},},
{seq=4,condition=199998,reward_item_list={[0]=item_table[15],[1]=item_table[16],[2]=item_table[12],[3]=item_table[17],[4]=item_table[18]},},
{seq=5,condition=499998,reward_item_list={[0]=item_table[19],[1]=item_table[20],[2]=item_table[1],[3]=item_table[21],[4]=item_table[22]},},
{seq=6,condition=999998,reward_item_list={[0]=item_table[23],[1]=item_table[24],[2]=item_table[25],[3]=item_table[26],[4]=item_table[22]},},
{seq=7,condition=1999998,reward_item_list={[0]=item_table[27],[1]=item_table[28],[2]=item_table[24],[3]=item_table[29],[4]=item_table[30]},},
{seq=8,condition=2999998,reward_item_list={[0]=item_table[31],[1]=item_table[32],[2]=item_table[28],[3]=item_table[33],[4]=item_table[30]},},
{seq=9,condition=4999998,reward_item_list={[0]=item_table[34],[1]=item_table[32],[2]=item_table[35],[3]=item_table[36],[4]=item_table[30]},},
{seq=10,condition=6999998,reward_item_list={[0]=item_table[37],[1]=item_table[38],[2]=item_table[39],[3]=item_table[40],[4]=item_table[41]},},
{seq=11,condition=9999998,reward_item_list={[0]=item_table[42],[1]=item_table[38],[2]=item_table[43],[3]=item_table[44],[4]=item_table[41]},},
{seq=12,condition=18888888,reward_item_list={[0]=item_table[45],[1]=item_table[46],[2]=item_table[47],[3]=item_table[48],[4]=item_table[41]},},
{seq=13,condition=28888888,reward_item_list={[0]=item_table[49],[1]=item_table[50],[2]=item_table[51],[3]=item_table[52],[4]=item_table[41]},}
},

condition_award_meta_table_map={
},
model_display={
{},
{grade=1,}
},

model_display_meta_table_map={
},
config_param={
{}
},

config_param_meta_table_map={
},
condition_award_default_table={grade=0,seq=0,condition=1998,reward_item_list={[0]=item_table[53],[1]=item_table[54],[2]=item_table[55],[3]=item_table[56],[4]=item_table[57]},},

model_display_default_table={grade=0,model_show_type=1,model_bundle_name="",model_asset_name="",model_show_itemid=38422,model_name="起源麒麟",display_pos="-225|-156",display_scale=1,display_rotation="0|0|0",},

config_param_default_table={start_server_day=1,end_server_day=9999,grade=0,open_role_level=100,}

}

