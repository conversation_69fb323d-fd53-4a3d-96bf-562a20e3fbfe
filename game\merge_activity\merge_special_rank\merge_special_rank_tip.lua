MergeSpecialRankTip = MergeSpecialRankTip or BaseClass(SafeBaseView)
function MergeSpecialRankTip:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/merge_activity_ui/sp_rank_prefab", "layout_second_panel", {sizeDelta = Vector2(786, 540)})
	self:AddViewResource(0, "uis/view/merge_activity_ui/sp_rank_prefab", "sp_rank_tip")
end

function MergeSpecialRankTip:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.MergeSPRank.RankTipTilte
	self.rank_log_list = AsyncListView.New(MergeSpecialRankTipRender, self.node_list["ph_item_list"])
	self:FlushView()
end

function MergeSpecialRankTip:ReleaseCallBack()
	if self.rank_log_list then
		self.rank_log_list:DeleteMe()
		self.rank_log_list = nil
	end
end

function MergeSpecialRankTip:OpenCallBack()

end

function MergeSpecialRankTip:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
		end
	end
end

function MergeSpecialRankTip:FlushView()
	local data_list = MergeSpecialRankWGData.Instance:GetRankList()
	if data_list then
        self.rank_log_list:SetDataList(data_list)
	end

    local my_rank, my_rank_value, rank_max_count = MergeSpecialRankWGData.Instance:GetGoldRankInfo()
	if my_rank <= 0 or my_rank > rank_max_count then
		self.node_list["my_rank"].text.text = Language.MergeSPRank.TipNoRank
	else
		self.node_list["my_rank"].text.text = string.format(Language.MergeSPRank.TipRank, my_rank)
	end

	self.node_list["total_value"].text.text = string.format(Language.MergeSPRank.TipDrawCount, my_rank_value)
end

-------------------- MergeSpecialRankTipRender
MergeSpecialRankTipRender = MergeSpecialRankTipRender or BaseClass(BaseRender)
function MergeSpecialRankTipRender:OnFlush()
	if not self.data then
		return
	end

    local is_top_3
    local player_rank
    if self.data.no_true_rank then  --未上榜
        self.node_list.need_cap_value.text.text = ""
		self.node_list.need_cap_value2.text.text = ""
		self.node_list.player_name.text.text = ""
        is_top_3 = self.data.rank <= 3
        player_rank = self.data.rank
	else
		self.node_list.need_cap_value.text.text = self.data.rank_value
		self.node_list.need_cap_value2.text.text = self.data.rank_value
        is_top_3 = self.data.rank <= 3
        player_rank = self.data.rank
	end

    local user_name = self.data.user_name
	if self.data.no_true_rank then
		user_name = Language.MergeSPRank.XuWeiYiDai
	end

    self.node_list.player_name.text.text = user_name
	self.node_list.player_name2.text.text = user_name

	self.node_list.player_name.text.enabled = not is_top_3
	self.node_list.player_name2.text.enabled = is_top_3

    self.node_list.need_cap_value.text.enabled = not is_top_3
	self.node_list.need_cap_value2.text.enabled = is_top_3

    self.node_list.img_rank.image.enabled = is_top_3

    if not is_top_3 then
		self.node_list.rank.text.text = player_rank
	else
		self.node_list.rank.text.text = ""
	end

	if self.index %2 == 0 then
		self.node_list.bg_normal.image:LoadSprite(ResPath.GetCommon("a3_kfcb_di_phxx2"))
	else
		self.node_list.bg_normal.image:LoadSprite(ResPath.GetCommon("a3_kfcb_di_phxx3"))
	end

	self.node_list.img_bg:SetActive(is_top_3)
	if is_top_3 then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank))
		self.node_list.img_bg.image:LoadSprite(ResPath.GetNoPackPNG("a3_zqxz_di_" .. self.data.rank))
	end
end