function EquipmentWGCtrl:InitZhuShiCtrl()
    self.zhushen_skill_tips = EquipmentZhuShenSkillTips.New()
	self:RegisterZhuShiProtocel()
end


function EquipmentWGCtrl:DeleteZhuShiCtrl()
    if self.zhushen_skill_tips then
        self.zhushen_skill_tips:DeleteMe()
        self.zhushen_skill_tips = nil
    end
        
    if self.role_data_event then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_event)
        self.role_data_event = nil
    end
end


function EquipmentWGCtrl:RegisterZhuShiProtocel()
    self:RegisterProtocol(CSCastSoulOperate)
    self:RegisterProtocol(SCCastSoulInfo, "OnSCCastSoulInfo")
end

-- 请求装备操作
function EquipmentWGCtrl:SendEquipZhuShenOperate(operate_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCastSoulOperate)
	protocol.operate_type = operate_type
    protocol.param_1 = param_1 or 0
    protocol.param_2 = param_2 or 0
    protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function EquipmentWGCtrl:OnSCCastSoulInfo(protocol)
    --print_error("铸神信息", protocol)
    self.data:SetZhuShenLevelList(protocol)
    
    self:FlushZhuShenView()
    if self.zhushen_skill_tips:IsOpen() then
        self.zhushen_skill_tips:Flush()
    end

    RemindManager.Instance:Fire(RemindName.Equipment_ZhuShen)
    RemindManager.Instance:Fire(RemindName.Equipment_ZhuShenTai)

end

function EquipmentWGCtrl:FlushZhuShenView()
	local is_load_zhushen = (self.view.show_index == TabIndex.equipment_zhushen) and self.view:IsLoadedIndex(TabIndex.equipment_zhushen)
	local is_load_zhushentai = (self.view.show_index == TabIndex.equipment_zhushentai) and self.view:IsLoadedIndex(TabIndex.equipment_zhushentai)
	if self.view:IsOpen() and (is_load_zhushen or is_load_zhushentai) then
		self.view:Flush()
	end
end

function EquipmentWGCtrl:OpenZhuShenSkillTips(showdata)
    if showdata == nil then
        return
    end

    self.zhushen_skill_tips:SetShowData(showdata)
    self.zhushen_skill_tips:Open()
end