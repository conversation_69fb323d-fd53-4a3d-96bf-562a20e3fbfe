YangLongSiTaskView = YangLongSiTaskView or BaseClass(SafeBaseView)

function YangLongSiTaskView:__init()
	self:AddViewResource(0, "uis/view/country_map_ui/yanglonsi_ui_prefab", "layout_yanglongsi_task_view")
    self.is_safe_area_adapter = true
	self.view_layer = UiLayer.MainUIHigh
	self.select_boss_seq = nil
end

function YangLongSiTaskView:LoadCallBack()
	if not self.boss_list then
		self.boss_list = AsyncListView.New(YLSBossItemRender, self.node_list.boss_list)
		self.boss_list:SetStartZeroIndex(true)
	end
	
	if not self.hart_team_list then
		self.hart_team_list = AsyncListView.New(HurtTeamRender, self.node_list.TaskList1)
	end

	self.node_list.hurt_desc.text.text = string.format(Language.YangLongSi.SceneTaskDesc, 10)
	self.node_list.myname.text.text = Language.YangLongSi.MyTeamName
	self.node_list.hurt_desc.text.text = string.format(Language.YangLongSi.BeKillDesc, YangLongSiaWGData.Instance:GetBeKillReduce())

	XUI.AddClickEventListener(self.node_list.btn_rob, BindTool.Bind(self.OnClickQiangduo, self))
	XUI.AddClickEventListener(self.node_list.go_get_reward, BindTool.Bind(self.OnClickGoGetRewardBtn, self))

	local limit_world_level = YangLongSiaWGData.Instance:GetOtherCfgData("force_peace_cross_world_level") or 0
	local world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
	self.node_list.btn_rob:SetActive(world_level >= limit_world_level)
end

function YangLongSiTaskView:ReleaseCallBack()
	if self.boss_list then
		self.boss_list:DeleteMe()
		self.boss_list = nil
	end

	if self.hart_team_list then 
		self.hart_team_list:DeleteMe()
		self.hart_team_list = nil
	end

	self.select_boss_seq = nil
	self:KillBoxinImgShakeAnim()
end

function YangLongSiTaskView:CloseCallBack()
end

function YangLongSiTaskView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView(v)
		end
	end
end

function YangLongSiTaskView:FlushView(data_state)
	local boss_data_list = YangLongSiaWGData.Instance:GetBossDataList()
	if not IsEmptyTable(boss_data_list) then
		self.boss_list:SetDataList(boss_data_list)

		-- 默认选择存活的
		if not self.select_boss_seq then
			for i = 0, #boss_data_list do
				local data = boss_data_list[i]
				local boss_info = YangLongSiaWGData.Instance:GetBossStateInfoBySeq(data.seq)
				if boss_info and boss_info.active then
					self.select_boss_seq = data.seq
					break
				end
			end
		end

		-- 选中正在攻击中的BOSS
		local current_boss_seq = YangLongSiaWGData.Instance:GetCurrentHurtBossSeq()
		if self.select_boss_seq and current_boss_seq ~= self.select_boss_seq then
			local mian_role = Scene.Instance:GetMainRole()
			if mian_role then
				local target = mian_role:GetAttackTarget()
				if target and target:IsBoss() and target.vo.monster_id and target.vo.monster_id > 0 then
					local target_id = target.vo.monster_id
					for k,v in pairs(boss_data_list) do
						if v.seq == current_boss_seq and v.monster_id == target_id then
							self.select_boss_seq = v.seq
							break
						end
					end
				end
			end
		end
		
		local cur_select = self.boss_list:GetSelectIndex()
		if self.select_boss_seq and cur_select ~= self.select_boss_seq then
			self.boss_list:SelectIndex(self.select_boss_seq)
		end
	end

	local my_team_data = YangLongSiaWGData.Instance:GetMyTeamData()
	local team_data_list = YangLongSiaWGData.Instance:GetBossHurtTeamDataList()
	local my_data_active = not IsEmptyTable(my_team_data)
	local has_team_data_list = not IsEmptyTable(team_data_list)

	self.node_list.no_rank_data:SetActive(not has_team_data_list)
	local my_slider_value = my_data_active and 1 or 0
	if self.node_list.hunt_slider.slider.value ~= my_slider_value then
		self.node_list.hunt_slider.slider:DOValue(my_slider_value, 0.2)
	end

	self.hart_team_list:SetDataList(team_data_list)
	
	if my_data_active then
		local top_three = my_team_data.rank <= 3
		if top_three then
			local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. my_team_data.rank)
			self.node_list["rank_img"].image:LoadSpriteAsync(bundle, asset, function ()
				self.node_list["rank_img"].image:SetNativeSize()
			end)
		else
			self.node_list.paiming.text.text = my_team_data.rank
		end

		self.node_list.rank_img:SetActive(top_three)
		self.node_list.paiming:SetActive(not top_three)
		self.node_list.my_damate.text.text = CommonDataManager.ConverNumber(my_team_data.total_hurt)
	else
		self.node_list.rank_img:SetActive(false)
		self.node_list.paiming.text.text = "--"
		self.node_list.my_damate.text.text = "0"
	end

	if not IsEmptyTable(data_state) then
		if data_state.hurt_add then
			self.node_list["TaskButton2"].toggle.isOn = true
		elseif data_state.hurt_del then
			self.node_list["TaskButton1"].toggle.isOn = true
		end
	end

	self:FlushRewardBos()
end

function YangLongSiTaskView:FlushRewardBos()
	local flag = YangLongSiaWGData.Instance:GetYangLongSiRemind() == 1
	self.node_list.reward_box_path:SetActive(flag)

	if flag then
		if not self.box_img_tween_shake then
            self.box_img_tween_shake = DG.Tweening.DOTween.Sequence()
            UITween.ShakeAnimi(self.node_list["reward_box_path"].transform, self.box_img_tween_shake, 1)
        end
	else
		self:KillBoxinImgShakeAnim()
	end
	
end

function YangLongSiTaskView:KillBoxinImgShakeAnim()
    if self.box_img_tween_shake then
		self.box_img_tween_shake:Kill(true)
		self.box_img_tween_shake = nil
    end
end

function YangLongSiTaskView:OnClickGoGetRewardBtn()
	ViewManager.Instance:Open(GuideModuleName.CountryMapActView, TabIndex.country_map_yanglongsi)
end

function YangLongSiTaskView:SetCurIndex(index)
	if self.boss_list then
		self.boss_list:SelectIndex(index)
		self.select_boss_seq = index
	end
end

-- 点击后改变攻击目标 攻击排名第一的团队的人  策划说只攻击排名第一的
function YangLongSiTaskView:OnClickQiangduo()
	local my_jieyi_id = SwornWGData.Instance:GetMyJieYiId()
	local top_team_data = YangLongSiaWGData.Instance:GetCurrentBossRankTopJieYiId()

	if IsEmptyTable(top_team_data) then
		return
	end

	local top_team_jieyi_id = top_team_data.jieyi_id
	local top_uuid = top_team_data.max_hurt_uuid
	local role_uuid = RoleWGData.Instance:GetUUid()
	if (top_team_jieyi_id > 0 and top_team_jieyi_id == my_jieyi_id) or top_uuid == role_uuid then
		return
	end

	local obj_list = Scene.Instance:GetRoleList()
	if IsEmptyTable(obj_list) then
		return
	end

	local select_obj
	for k, v in pairs(obj_list) do
		local obj_vo = v:GetVo()
		local other_jieyi_id = obj_vo.special_param
		if top_team_jieyi_id > 0 then
			if top_team_jieyi_id == other_jieyi_id and obj_vo.uuid ~= role_uuid then
				select_obj = v
				break
			end
		elseif obj_vo.uuid == top_uuid then
			select_obj = v
			break
		end
	end

	if select_obj then
		local is_enemy, reason = Scene.Instance:IsEnemy(select_obj)
		if is_enemy then
			GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
		else
			if reason and reason ~= "" then
				TipWGCtrl.Instance:ShowSystemMsg(reason)
			else
				TipWGCtrl.Instance:ShowSystemMsg(Language.Fight.ModeCantAttack)
			end
		end
	end
end

---------------------------------------------------------------------------------------------------------
YLSBossItemRender = YLSBossItemRender or BaseClass(BaseRender)
function YLSBossItemRender:__init()
	XUI.AddClickEventListener(self.node_list.BtnSelf, BindTool.Bind(self.OnClickBossRender, self))
end

function YLSBossItemRender:OnClickBossRender()
	if IsEmptyTable(self.data) then
		return 
	end

	local sence_id = Scene.Instance:GetSceneId()
    local role = Scene.Instance:GetMainRole()
	if role == nil then
		return
	end

    local role_x, role_y = role:GetLogicPos()
	local pos_data = Split(self.data.monster_pos, ",")
	local pos_x, pos_y = pos_data[1],  pos_data[2]

	if role_x == pos_x and role_y == pos_y then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	else
		GuajiWGCtrl:StopGuaji()
		AtkCache.target_obj = nil

		local boss_active = false
		local boss_info = YangLongSiaWGData.Instance:GetBossStateInfoBySeq(self.data.seq)
		if not IsEmptyTable(boss_info) and boss_info.active then
			boss_active = boss_info.active
		end

		if boss_active then
			MoveCache.SetEndType(MoveEndType.FightByMonsterId)
		else
			MoveCache.SetEndType(MoveEndType.Normal)
		end
		
		MoveCache.param1 = self.data.monster_id
		GuajiCache.monster_id = self.data.monster_id
		local range = BossWGData.Instance:GetMonsterRangeByid(self.data.monster_id)
		GuajiWGCtrl.Instance:MoveToPos(sence_id, pos_x, pos_y, range)
	end

	YangLongSiWGCtrl.Instance:SetCurBossIndex(self.index)
end

function YLSBossItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return 
	end

	local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.monster_id]
	self.node_list.jieshu_text.text.text = string.format(Language.Boss.JieShu, NumberToChinaNumber(cfg.boss_jieshu))
	self.node_list.TextDesc.text.text = cfg.name

	local boss_active = false
	local boss_info = YangLongSiaWGData.Instance:GetBossStateInfoBySeq(self.data.seq)

	if not IsEmptyTable(boss_info) and boss_info.active then
		boss_active = boss_info.active
	end

	self.node_list.TimeDesc.text.text = boss_active and Language.YangLongSi.IsActive or Language.YangLongSi.IsKill
end

function YLSBossItemRender:OnSelectChange(is_select)
	if self.node_list["SelectLigth"] then
		self.node_list["SelectLigth"]:SetActive(is_select)
	end
end

---------------------------------------------------------------------------------------------------------------
HurtTeamRender = HurtTeamRender or BaseClass(BaseRender)
function HurtTeamRender:OnFlush()
	if IsEmptyTable(self.data) then
		return 
	end

	local top_three = self.data.rank <= 3
	if top_three then
		local bundle, asset = ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank)
		self.node_list["rank_icon"].image:LoadSpriteAsync(bundle, asset, function ()
			self.node_list["rank_icon"].image:SetNativeSize()
		end)
	else
		self.node_list.num.text.text = self.data.rank
	end

	self.node_list.num:SetActive(not top_three)
	self.node_list.rank_icon:SetActive(top_three)

	self.node_list.name.text.text = string.format(Language.YangLongSi.TeamName, self.data.name)
	self.node_list.damage.text.text = CommonDataManager.ConverNumber(self.data.total_hurt)
end