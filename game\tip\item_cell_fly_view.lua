------------------------------------------------------------
-- 物品格子飞行面板
------------------------------------------------------------
ItemCellFlyView = ItemCellFlyView or BaseClass(SafeBaseView)

function ItemCellFlyView:__init()
	self.active_close = false
	self.view_layer = UiLayer.Pop
	self.open_tween = nil
	self.close_tween = nil
	self:AddViewResource(0, "uis/view/tips/itemcellflyview_prefab", "item_cell_fly_layout")

	self.item_data = nil
	self.start_pos = Vector3.zero
	self.end_pos = Vector3.zero
end

function ItemCellFlyView:SetData(item_data, start_pos, end_pos, fly_end_callback)
	self.item_data = item_data
	self.start_pos = start_pos
	self.end_pos = end_pos
	self.fly_end_callback = fly_end_callback
	if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end

function ItemCellFlyView:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
end

function ItemCellFlyView:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ItemCellFlyView:OpenCallBack()
end

function ItemCellFlyView:CloseCallBack()
	self.item_data = nil
	self.fly_end_callback = nil
	self.start_pos = Vector3.zero
	self.end_pos = Vector3.zero
end

function ItemCellFlyView:OnFlush()
	self.item_cell:SetData(self.item_data)
	self.node_list["item_cell"].transform.position = self.start_pos
	local screen_pos = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.end_pos)
	local parent_rect = self.node_list["item_cell"].transform.parent.gameObject:GetComponent(typeof(UnityEngine.RectTransform))
    local _, anchored_pos = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(parent_rect, screen_pos, UICamera, Vector2(0, 0))
    	
    UITween.CleanScaleShow("ItemCellFlyView")
	UITween.DoScaleMoveShow("ItemCellFlyView", self.node_list["item_cell"], 
		{FromScale = Vector3(1, 1, 1), ToScale = Vector3(0.5, 0.5, 0.5), ScaleTweenTime = 0.6, MoveTweenTime = 0.6, EndPosition = u3dpool.vec2(anchored_pos.x, anchored_pos.y)}, BindTool.Bind(self.FlyEndCallBack, self))
end

function ItemCellFlyView:FlyEndCallBack()
	if self.fly_end_callback then
		self.fly_end_callback()
	end
	self:Close()
end