﻿using System;
using UnityEngine;

// 弹道或抛射物的基类
public abstract class Projectile : MonoBehaviour
{
    /// <summary>
    /// 弹道的播放
    /// </summary>
    /// <param name="sourceScale">弹道的源缩放比例</param>
    /// <param name="target">弹道的目标位置</param>
    /// <param name="layer">弹道的层级</param>
    /// <param name="hited">当弹道命中目标时执行的回调</param>
    /// <param name="complete">当弹道播放完成时执行的回调</param>
    public abstract void Play(
        Vector3 sourceScale,
        Transform target,
        int layer,
        Action hited,
        Action complete);
}
