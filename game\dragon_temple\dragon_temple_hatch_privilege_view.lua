DragonTempleHatchPrivilegeView = DragonTempleHatchPrivilegeView or BaseClass(SafeBaseView)

function DragonTempleHatchPrivilegeView:__init()
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/dragon_temple_ui_prefab", "layout_dragon_temple_hatch_privilege")
end

function DragonTempleHatchPrivilegeView:LoadCallBack()
	local cfg =	DragonTempleWGData.Instance:GetOtherCfg()
	if not IsEmptyTable(cfg) then
		self.node_list.hatch_privilege_day.text.text = cfg.hatch_rmb_buy_day
		self.node_list.skill_name.text.text = cfg.skill_name

		local bundle, asset = ResPath.GetSkillIconById(cfg.skill_ico)
		self.node_list.skill_icon.image:LoadSprite(bundle, asset, function()
			self.node_list.skill_icon.image:SetNativeSize()
		end)
	end

	if not self.hatch_reward_list then
		self.hatch_reward_list = AsyncListView.New(ItemCell, self.node_list.hatch_reward_list)
		self.hatch_reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.skill_icon, BindTool.Bind1(self.OnClickSkillBtn, self))
end

function DragonTempleHatchPrivilegeView:ReleaseCallBack()
	if self.hatch_reward_list then
		self.hatch_reward_list:DeleteMe()
		self.hatch_reward_list = nil
	end

	self:RemoveTimeCountDown()
end

function DragonTempleHatchPrivilegeView:OnFlush()
	local cfg =	DragonTempleWGData.Instance:GetOtherCfg()

	if not IsEmptyTable(cfg) then
		self.hatch_reward_list:SetDataList(cfg.hatch_daily_reward_item)

		local has_privilege = DragonTempleWGData.Instance:IsHatchHasPrivilege()
		self.node_list.time:SetActive(has_privilege)
		local can_get_daily_reward = DragonTempleWGData.Instance:CanGetHatchDailyReward()
		
		local btn_str
		if has_privilege then
			btn_str = can_get_daily_reward and Language.DragonTemple.HatchPrivilegeCanGetReward or Language.DragonTemple.HatchPrivilegeHasGetReward
			self:StartTimeCountDown()
		else
			btn_str = RoleWGData.GetPayMoneyStr(cfg.hatch_price, cfg.hatch_rmb_type, cfg.hatch_rmb_seq)
		end

		self.node_list.buy_btn_text.text.text = btn_str
		XUI.SetButtonEnabled(self.node_list.buy_btn, not has_privilege or (has_privilege and can_get_daily_reward))
	end
end

function DragonTempleHatchPrivilegeView:OnClickBuyBtn()
	local cfg =	DragonTempleWGData.Instance:GetOtherCfg()

	if not IsEmptyTable(cfg) then
		local has_privilege = DragonTempleWGData.Instance:IsHatchHasPrivilege()

		if has_privilege then
			local can_get_daily_reward = DragonTempleWGData.Instance:CanGetHatchDailyReward()

			if can_get_daily_reward then
				DragonTempleWGCtrl.Instance:SendCSDragonTempleRequest(DRAGON_TEMPLE_OPERATE_TYPE.FETCH_HATCH_DAILY_REWARD)
			end
		else
			RechargeWGCtrl.Instance:Recharge(cfg.hatch_price, cfg.hatch_rmb_type, cfg.hatch_rmb_seq)
		end
	end
end

function DragonTempleHatchPrivilegeView:OnClickSkillBtn()
	local cfg =	DragonTempleWGData.Instance:GetOtherCfg()
	if not IsEmptyTable(cfg) then

		local show_data = {
			icon = cfg.skill_ico,
			top_text = cfg.skill_name,
			body_text = cfg.skill_des,
			x = 0,
			y = 0,
			set_pos2 = true,
			hide_level = true,
		}
	
		NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
	end
end

function DragonTempleHatchPrivilegeView:StartTimeCountDown()
	self:RemoveTimeCountDown()

	local time = TimeWGCtrl.Instance:GetServerTime()
	local end_time = DragonTempleWGData.Instance:GetHatchPrivilegeEndTime()
	local rest_time = end_time - time
	if rest_time > 0 then
		self.node_list.time.text.text = string.format(Language.DragonTemple.PrivilegeTime, TimeUtil.FormatSecondDHM9(rest_time))
		self.time_countdown = CountDown.Instance:AddCountDown(rest_time, 1,
		function(elapse_time, total_time)
			if self.node_list.time then
				self.node_list.time.text.text = string.format(Language.DragonTemple.PrivilegeTime, TimeUtil.FormatSecondDHM9(total_time - elapse_time))
			end
		end,
		function()
			if self.node_list then
				self:Flush()
			end
		end)
	end
end

function DragonTempleHatchPrivilegeView:RemoveTimeCountDown()
	if CountDown.Instance:HasCountDown(self.time_countdown) then
        CountDown.Instance:RemoveCountDown(self.time_countdown)
        self.time_countdown = nil
    end
end