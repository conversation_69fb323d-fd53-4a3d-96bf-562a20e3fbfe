﻿
Shader "Game/PostEffect/MotionBlurPass" {
	Properties
	{
		_MainTex("Base (RGB)", 2D) = "white" {}
		_fSampleDist("SampleDist", Float) = 1 //采样距离
		_fSampleStrength("SampleStrength", Float) = 1.5 //采样力度
	}
		
	SubShader
	{
		Tags { "RenderPipeline" = "UniversalRenderPipeline" }


		HLSLINCLUDE
		#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"

		TEXTURE2D(_MainTex);
		SAMPLER(sampler_MainTex);

		float _fSampleDist;
		float _fSampleStrength;

		struct appdata_t 
		{
			float4 vertex : POSITION;
			float2 texcoord : TEXCOORD;
		};

		struct v2f 
		{
			float4 vertex : POSITION;
			float2 texcoord : TEXCOORD;
		};

		float4 _MainTex_ST;

		v2f vert(appdata_t v)
		{
			v2f o;
			o.vertex = TransformObjectToHClip(v.vertex);
			o.texcoord = TRANSFORM_TEX(v.texcoord, _MainTex);
			return o;
		}

		// some sample positions  
		static const float samples[6] = {
            -0.05, -0.03, -0.01, 0.01, 0.03, 0.05,
        };

		half4 frag(v2f i) : SV_Target
		{
			//0.5,0.5屏幕中心
			float2 dir = float2(0.5, 0.5) - i.texcoord;//从采样中心到uv的方向向量
			float2 texcoord = i.texcoord;
				float dist = length(dir);
			dir = normalize(dir);
			float4 color = SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, texcoord);
			

			float4 sum = color;
			// 6次采样
			for (int j = 0; j < 6; ++j) {
                sum += SAMPLE_TEXTURE2D(_MainTex, sampler_MainTex, texcoord + dir * samples[j] * _fSampleDist);
            }

			//求均值
			sum /= 7.0f;

			//越离采样中心近的地方，越不模糊
			float t = saturate(dist * _fSampleStrength);

			//插值
			return lerp(color, sum, t);
		}
		ENDHLSL


		Pass
		{
			ZTest Always
			Cull Off
			ZWrite Off

			Fog{ Mode off }

			HLSLPROGRAM
			#pragma vertex vert
			#pragma fragment frag
			ENDHLSL
		}
	}
	Fallback off
}