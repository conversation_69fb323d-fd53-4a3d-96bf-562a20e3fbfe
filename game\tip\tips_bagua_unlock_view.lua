TipsBaGuaUnLockView = TipsBaGuaUnLockView or BaseClass(SafeBaseView)

function TipsBaGuaUnLockView:__init()
	self:SetMaskBg(true, true, false, BindTool.Bind(self.BlockClick, self))
	self:AddViewResource(0, "uis/view/appearance_ui_prefab", "layout_bague_get_new")
	self.view_name = "BaGuaGetNewSkillTips"
	self.delay_time = 1.5
	self.fade_speed = 1.5
	self.move_speed = 90
	self.play_audio = true
	self.view_layer = UiLayer.Pop
	self.is_auto_task = false
	self.view_cache_time = 0
	self.can_do_fade = false
end

function TipsBaGuaUnLockView:__delete()

end

function TipsBaGuaUnLockView:LoadCallBack()
	self.node_list["bagua_btn_get"].button:AddClickListener(BindTool.Bind(self.OnClickGet, self))
	self.skill_button_pos_list = MainuiWGCtrl.Instance:GetSkillButtonPosition()
	if not self.bagua_model then
		self.bagua_model = {}
		for i=1,8 do
			self.bagua_model[i] = RoleModel.New()
			local display_data = {
				parent_node = self.node_list["bagua_mode"..i],
				camera_type = MODEL_CAMERA_TYPE.BASE,
				-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
				rt_scale_type = ModelRTSCaleType.S,
				can_drag = false,
			}
			
			self.bagua_model[i]:SetRenderTexUI3DModel(display_data)
			-- self.bagua_model[i]:SetUI3DModel(self.node_list["bagua_mode"..i].transform, nil, 1, true, MODEL_CAMERA_TYPE.BASE)
			self:AddUiRoleModel(self.bagua_model[i])
		end
	end
end

function TipsBaGuaUnLockView:ReleaseCallBack()
	-- 清理变量和对象
	self.target_icon = nil
	self.from_mark  = nil

	if self.bagua_model then
		for k,v in pairs(self.bagua_model) do
			v:DeleteMe()
		end
		self.bagua_model = nil
	end

end

function TipsBaGuaUnLockView:ShowViewByCfg(skill_cfg)
	self.skill_cfg = skill_cfg
	self:Open()
end

function TipsBaGuaUnLockView:ShowView(skill_id,from_mark)

	self.id_value = skill_id
	self.from_mark = from_mark
	local skill_cfg
	if from_mark and from_mark == XINMO_FUBEN_SKILL_POS.XINMO_SKILL then
		skill_cfg = SkillWGData.Instance:GetPassiveSkillByIndex(self.id_value)
	else
		skill_cfg = SkillWGData.GetSkillFunOpenCfg(self.id_value)
	end

	if skill_cfg == nil then
		return
	end
	self.index = skill_cfg.pos_index or 0
	if self.index >= 5 and nil == from_mark then
		return
	end

	self:Open()
end

function TipsBaGuaUnLockView:OnClickGet()
	self.node_list["bagua_btn_get"]:SetActive(false)
	self:BlockClick()
end

function TipsBaGuaUnLockView:BlockClick()
	if self.fly_flag == false then
		self.fly_flag = true
		if self.timer > 0 then
			if self.timer_hide_quest then
			   GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
			   self.timer_hide_quest = nil
			end

			self:DoMoveToTargetTween()
		end
	end
end

function TipsBaGuaUnLockView:ShowIndexCallBack(index)
	local view_manager = ViewManager.Instance
	if view_manager:IsOpen(GuideModuleName.TaskDialog) then
		view_manager:Close(GuideModuleName.TaskDialog)
	end
	self.fly_flag = false

	if self.mask_bg then
		self.hide_mask = false
		self.mask_bg:SetActive(true)
	end
	self.node_list["bagua_btn_get"]:SetActive(true)
	self.is_auto_task = TaskGuide.Instance.can_auto_all_task
	TaskGuide.Instance:CanAutoAllTask(false)		--停止接受任务
	-- ViewManager.Instance:CloseAll()				--关闭所有界面

	local skill_cfg
	if self.skill_cfg then
		skill_cfg = self.skill_cfg
	else
		if self.from_mark then
			skill_cfg = SkillWGData.Instance:GetPassiveSkillByIndex(self.id_value)
		else
			skill_cfg = SkillWGData.GetSkillFunOpenCfg(self.id_value)
		end
	end

	if not skill_cfg or not skill_cfg.cur_select_bagua then
		self:Close()
		return
	end
	self.node_list["active_shake_node"]:SetActive(true)
	local m_bundle, m_asset = "", ""
	local model_id = TianShenBaGuaWGData.Instance:GetBaGuaModelID(self.skill_cfg.cur_select_bagua)
	for i =1,8 do
		local load_call_back = function ()
			-- self:BaGuaModelLoadComplete(i)
		end
		m_bundle, m_asset = ResPath.GetTianShenBaGuaModel(model_id,Language.TianShen.BaGuaModelNum[i])
		self.bagua_model[i]:SetMainAsset(m_bundle,m_asset,load_call_back)
	end
	local name_info = TianShenBaGuaWGData.Instance:GetBaGuaNameInfo(self.skill_cfg.cur_select_bagua)
	self.node_list["obj_name"].text.text = name_info.name

	UITween.DoViewDuangOpenTween(self.node_list.Frame, self.node_list["SkillIcon"])

	local res_fun = skill_cfg.res_fun or ResPath.GetSkillIcon
	if self.from_mark or self.skill_cfg then
		self.node_list["SkillName"].text.text = skill_cfg.name
		self.node_list["SkillDesc"].text.text = skill_cfg.desc
		self.node_list["SkillIcon"].image:LoadSprite(res_fun(skill_cfg.icon))
	else
		self.node_list["SkillName"].text.text = skill_cfg.skill_name
		self.node_list["SkillIcon"].image:LoadSprite(res_fun(SkillWGData.Instance:GetSkillIconId(skill_cfg.skill_id)))
		self.node_list["SkillDesc"].text.text = skill_cfg.description
	end
    local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_jiNeng_up)
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["SkillIcon"].transform, nil)
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.XinJiNengKaiQi, nil, true))
	self.node_list["SkillIcon"]:SetActive(true)
	self:CalTimeToHideBg()

	if self.skill_cfg or (nil == self.skill_button_pos_list and skill_cfg.pos_index == 0 and nil == self.from_mark) then
		return
	end
	local target
	if self.from_mark then
		target = MainuiWGCtrl.Instance:GetSkillBottomButton()
	else
		target = self.skill_button_pos_list[skill_cfg.pos_index]
	end

	if target then
		if self.from_mark then
			self.target_icon = target.transform:FindHard("Image")
			MainuiWGCtrl.Instance:SetMenuIconIsOn(true)
		else
			self.target_icon = target.transform:FindHard("Icon")
			self.target_icon.gameObject:SetActive(false)
			MainuiWGCtrl.Instance:SetMenuIconIsOn(false)
		end

	end
end

function TipsBaGuaUnLockView:CloseCallBack()
	self.skill_cfg = nil
	if self.is_auto_task then
		if Scene.Instance:GetSceneType() == SceneType.Common then
			GuajiWGCtrl.Instance:StopGuaji()
		end

		TaskGuide.Instance:CanAutoAllTask(true)		--继续自动做任务
	end
	self.fly_flag = false
	if self.timer_hide_quest then
		GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
		self.timer_hide_quest = nil
	end
	GlobalEventSystem:Fire(MainUIEventType.ROLE_SKILL_LIST)
end

function TipsBaGuaUnLockView:MoveToTarget()
	if self.mask_bg then
		self.mask_bg:SetActive(false)
		self.hide_mask = true
	end

	local timer = 0.7
	if nil == self.from_mark then
		if nil == self.skill_button_pos_list and nil == self.skill_button_pos_list[self.index] or self.index == 0 then
			if self.skill_cfg and self.skill_cfg.need_move then
			else
				self:Close()
				return
			end
		end
	end

	if self.skill_cfg and not self.skill_cfg.need_move then
		self:Close()
		return
	end

	local target
	if self.from_mark then
		target = MainuiWGCtrl.Instance:GetSkillBottomButton()
	elseif self.skill_cfg and self.skill_cfg.need_move then
		target = self.skill_cfg.target
	else
		target = self.skill_button_pos_list[self.index]
	end

	if target == nil then
		self:Close()
		return
	end

	self.time_quest = GlobalTimerQuest:AddDelayTimer(function()
		local item = self.node_list["SkillIcon"]
		if not item then
			return
		end
		local path = {}
		self.target_pos = target.transform.position
		table.insert(path, self.target_pos)
		local tweener = item.transform:DOPath(
			path,
			timer,
			DG.Tweening.PathType.Linear,
			DG.Tweening.PathMode.TopDown2D,
			0.7,
			nil)
		tweener:SetEase(DG.Tweening.Ease.Linear)
		tweener:SetLoops(0)
		local close_view = function()
			self:CloseView()
		end
		tweener:OnComplete(close_view)
		item.loop_tweener = tweener
	end, 0)
end

function TipsBaGuaUnLockView:CloseView()
	self:Close()
	self.node_list["SkillIcon"]:SetActive(false)
	if self.target_icon then
		self.target_icon.gameObject:SetActive(true)
	end
	GlobalTimerQuest:CancelQuest(self.time_quest)
end

function TipsBaGuaUnLockView:CalTimeToHideBg()
	if self.timer_hide_quest then
	   GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
	   self.timer_hide_quest = nil
	end
	self.timer = 5

	self.timer_hide_quest = GlobalTimerQuest:AddRunQuest(function()
		if not self:IsLoaded() then
			return
		end

		self.timer = self.timer - UnityEngine.Time.deltaTime

		if self.timer <= 0 then
			GlobalTimerQuest:CancelQuest(self.timer_hide_quest)
			self.timer_hide_quest = nil

			local menu_ison = MainuiWGCtrl.Instance.view:GetMenuButtonIsOn()
			if self.from_mark then
				self:DoMoveToTargetTween()
			elseif self.skill_cfg and self.skill_cfg.need_move then
				self:DoMoveToTargetTween()
			else
				if not menu_ison then
					self:DoMoveToTargetTween()
				else
					self:CloseView()
				end
			end
		end
	end, 0)
end

function TipsBaGuaUnLockView:DoMoveToTargetTween()
	self.node_list["active_shake_node"]:SetActive(false)
	self.node_list["bagua_btn_get"]:SetActive(false)
	UITween.DoViewDuangCloseTween(self.node_list.Frame, nil, function()
		self:MoveToTarget()
	end)
end
