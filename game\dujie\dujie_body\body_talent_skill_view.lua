BodyTalentSkillView = BodyTalentSkillView or BaseClass(SafeBaseView)

function BodyTalentSkillView:__init()
	self:SetMaskBg(true,true)
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:AddViewResource(0, "uis/view/dujie_body_ui_prefab", "layout_talent_skill_view")
end

function BodyTalentSkillView:LoadCallBack()
	if not self.talent_skill_list then
		self.talent_skill_list = AsyncListView.New(BodySkillItemRender, self.node_list.talent_attr_list)
	end
end

function BodyTalentSkillView:ReleaseCallBack()
    if self.talent_skill_list then

        self.talent_skill_list:DeleteMe()
        self.talent_skill_list = nil
    end

end

function BodyTalentSkillView:CloseCallBack()
    self.data  = nil
    DujieWGCtrl.Instance:OpenBodyTalentShowNumView()
end

function BodyTalentSkillView:OnFlush()
    if not self.data then
        return 
    end
    
    local cfg = self.data.cfg
    local info = self.data.info
    -- 前置是否激活
    local is_active_pre = DujieWGData.Instance:IsActivePreTalent(cfg.body_seq,cfg.seq)

    local skill_data_list = DujieWGData.Instance:GetSkillTalentSkillDataList(cfg.body_seq,cfg.seq)
    local skill_cfg_list = {}
    for k, v in pairs(skill_data_list) do
        local skill_cfg = DujieWGData.Instance:GetTalentSkillCfg(v.skill_seq)
        local data = {}
        data.cfg = skill_cfg
        data.select_seq = info.skill_seq
        data.talent_cfg = cfg
        data.cost_data = v.cost_data
        data.is_active_pre = is_active_pre
        table.insert(skill_cfg_list,data)
    end

    self.node_list.text_desc.tmp.text = is_active_pre and Language.Dujie.SkillCanSelect or Language.Dujie.SkillNotCanSelect

    self.talent_skill_list:SetDataList(skill_cfg_list)
end

function BodyTalentSkillView:SetData(data)
    self.data = data
end

---------------------------------------------------------
BodySkillItemRender = BodySkillItemRender or BaseClass(BaseRender)
function BodySkillItemRender:__init()

    -- 天赋注灵消耗 
    if self.cost_cell == nil then
        self.cost_cell = ItemCell.New(self.node_list.cost_item_node)
        self.cost_cell:SetShowCualityBg(false)
        self.cost_cell:NeedDefaultEff(false)
        self.cost_cell:SetCellBgEnabled(false)
    end

    XUI.AddClickEventListener(self.node_list.btn_select, BindTool.Bind(self.OnClickBtnSelect, self))

    
end

function BodySkillItemRender:__delete()
    if self.cost_cell then
        self.cost_cell:DeleteMe()
        self.cost_cell = nil
    end
end

function BodySkillItemRender:LoadCallBack()

end

function BodySkillItemRender:ReleaseCallBack()

end

function BodySkillItemRender:OnFlush()
    if not self.data then return end

    self.node_list.text_name.tmp.text = self.data.cfg.skill_name

    self.node_list.img_select:SetActive(self.data.cfg.seq == self.data.select_seq)
    self.node_list.effect:SetActive(self.data.cfg.seq == self.data.select_seq)
    self.node_list.select_group:SetActive(self.data.cfg.seq ~= self.data.select_seq and self.data.is_active_pre)

    local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_2_"..self.data.cfg.skill_icon)
    self.node_list.img_skill_icon.image:LoadSprite(bundle, asset)    


    
    local item_id = DujieWGData.Instance:GetOtherCfg("body_item_"..self.data.cost_data.type)
    
    self.cost_cell:SetData({item_id = item_id})
    self.node_list.text_cost.tmp.text = "X"..self.data.cost_data.num

    self.node_list.text_desc.tmp.text = DujieWGData.Instance:GetTalentSkillDesc(self.data.cfg.seq)
end

function BodySkillItemRender:OnClickBtnSelect()
    if DujieWGData.Instance:IsReachMoney(self.data.cost_data.type, self.data.cost_data.num) then
        DujieWGCtrl.Instance:SelectTalentSkill(self.data.talent_cfg.body_seq,self.data.talent_cfg.seq,self.data.cfg.seq)
    else
        local item_id = DujieWGData.Instance:GetOtherCfg("body_item_"..self.data.cost_data.type)
        TipWGCtrl.Instance:OpenItem({item_id = item_id})
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.SkillTalentActiveCostNotReach)
    end
    
end









