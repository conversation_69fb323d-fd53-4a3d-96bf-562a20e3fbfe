ActivityPrivilegeBuyWGData = ActivityPrivilegeBuyWGData or BaseClass()
local empty_table = {}
function ActivityPrivilegeBuyWGData:__init()
    if ActivityPrivilegeBuyWGData.Instance then
        error("[ActivityPrivilegeBuyWGData] Attempt to create singleton twice!")
    end
    ActivityPrivilegeBuyWGData.Instance = self

    self.grade = 0
    self.act_open_day = 0
    self:InitConfig()

    --红点注册
    RemindManager.Instance:Register(RemindName.RemindPrivilegeBuy, BindTool.Bind(self.GetRemind, self))
end

function ActivityPrivilegeBuyWGData:__delete()
    --红点注销
    RemindManager.Instance:UnRegister(RemindName.RemindPrivilegeBuy)

    ActivityPrivilegeBuyWGData.Instance = nil
end

--初始化配置表
function ActivityPrivilegeBuyWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_privilege_rmb_buy_auto")
    self.rmb_buy_map_cfg = ListToMapList(cfg.rmb_buy, "grade")
    self.grade_seq_cfg = ListToMapList(cfg.reward, "grade", "seq")  --根据两个索引找到活跃天数和对应的奖励
end

--存储协议下发的数据
function ActivityPrivilegeBuyWGData:SetInfo(protocol)
    self.grade = protocol.grade                      --用于判断当前档次
    self.rmb_buy_flag = protocol.rmb_buy_flag        --判断特权是否购买的标记
    self.reward_flag_list = protocol.reward_flag_list   --用于判断物品是否领取的标记
    self:ReCalcActDay()
end

function ActivityPrivilegeBuyWGData:ReCalcActDay()
    self.act_open_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.PRIVILEGEBUY)
end

function ActivityPrivilegeBuyWGData:GetActDay()
    return self.act_open_day
end

function ActivityPrivilegeBuyWGData:GetTeQuanIsBuy(seq)  --判断特权是否购买
    return self.rmb_buy_flag[seq] == 1
end

function ActivityPrivilegeBuyWGData:GetRewardIsGet(seq, day)  --判断物品是否可领取
    return (self.reward_flag_list[seq] or empty_table)[day] == 1
end

-- 全领取
function ActivityPrivilegeBuyWGData:GetRewardIsAllGet(seq)
    if self.reward_flag_list[seq]  == nil then
        return true
    end

    for k, v in ipairs(self.reward_flag_list[seq]) do
        if not self:GetRewardIsGet(seq, k) then
            return false
        end
    end
    return true
end

function ActivityPrivilegeBuyWGData:GetTeQuanShowList()
    local show_list = {}
    local cfg_list = self.rmb_buy_map_cfg[self.grade] or empty_table
    local cur_act_day = self:GetActDay()
    for k,v in ipairs(cfg_list) do
        local is_buy = self:GetTeQuanIsBuy(v.seq)
        if (v.open_day <= cur_act_day and cur_act_day <= v.close_day)
        or (is_buy and not self:GetRewardIsAllGet(v.seq)) then
            local data = __TableCopy(v)
            data.is_remind = self:GetCurTeQuanRewardIsCanGet(v.seq)
            table.insert(show_list, data)
        end
    end

    for k, v in ipairs(show_list) do
        v.end_judge = k == #show_list
    end
    return show_list
end

function ActivityPrivilegeBuyWGData:GetRewardList(seq)
    return (self.grade_seq_cfg[self.grade] or empty_table)[seq] or empty_table
end

-- 特权的某一天是否可领取(判断对应传入的索引和天数)
function ActivityPrivilegeBuyWGData:GetCurDayRewardIsCanGet(seq, day)
    local is_buy = self:GetTeQuanIsBuy(seq)
    if not is_buy then
        return false
    end

    if day > self.act_open_day or self:GetRewardIsGet(seq, day) then
        return false
    end

    return true
end

-- 特权是否可领取
function ActivityPrivilegeBuyWGData:GetCurTeQuanRewardIsCanGet(seq)
    local cfg_list = (self.grade_seq_cfg[self.grade] or empty_table)[seq] or empty_table
    for k,v in ipairs(cfg_list) do
        if self:GetCurDayRewardIsCanGet(seq, v.activity_day) then
            return true
        end
    end

    return false
end

--红点
function ActivityPrivilegeBuyWGData:GetRemind()
    local cfg_list = self.rmb_buy_map_cfg[self.grade] or empty_table
    for k,v in ipairs(cfg_list) do
        if self:GetCurTeQuanRewardIsCanGet(v.seq) then
            return 1
        end
    end
    
    return 0
end



