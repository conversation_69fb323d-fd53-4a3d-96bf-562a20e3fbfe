ArenaTianTiChallengeTip = ArenaTianTiChallengeTip or BaseClass(SafeBaseView)

function ArenaTianTiChallengeTip:__init()
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(600, 425)})
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "tianti_challenge_tip")
end

function ArenaTianTiChallengeTip:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_fight, BindTool.Bind(self.ClickFightBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_close, BindTool.Bind(self.Close, self))

    self.role_head_cell = BaseHeadCell.New(self.node_list["node_head_parent"])
end

function ArenaTianTiChallengeTip:ReleaseCallBack()
    self.show_data = nil

    if self.role_head_cell then
        self.role_head_cell:DeleteMe()
        self.role_head_cell = nil
    end
end

function ArenaTianTiChallengeTip:SetDataAndOpen(data)
    self.show_data = data
    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function ArenaTianTiChallengeTip:OnFlush()
    self.node_list.text_score.text.text = self.show_data.opponent_score
    self.node_list.text_name.text.text = self.show_data.target_name .. "_s" .. self.show_data.server_id
    self.node_list.text_enemy_fight.text.text = self.show_data.opponent_capability
    local my_fight = RoleWGData.Instance:GetMainRoleCap()
    local color_str = my_fight >= self.show_data.opponent_capability and "#CBFBFD" or IS_CAN_COLOR.NOT_ENOUGH
    self.node_list.text_my_fight.text.text = string.format("<color=%s>%s</color>", color_str, my_fight)

    local data = {}
    data.role_id = self.show_data.target_uuid
    data.prof = self.show_data.prof
    data.sex = self.show_data.sex
    data.fashion_photoframe = self.show_data.fashion_photoframe
    self.role_head_cell:SetData(data)
end

function ArenaTianTiChallengeTip:ClickFightBtn()
    local my_fight = RoleWGData.Instance:GetMainRoleCap()
    local fight_func = function ()
        if not self:IsOpen() then
            return
        end

        local end_time = ArenaTianTiWGData.Instance:GetChallengeEndTime()
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        if end_time <= server_time then
            TipWGCtrl.Instance:ShowSystemMsg(Language.Field1v1.TianTiTxt17)
            return
        end

        if ArenaTianTiWGData.Instance:GetResidueTiaoZhanNum() <= 0 then
            local join_item_id = ArenaTianTiWGData.Instance:GetChallengeFieldOtherCfgByName("join_item_id")
            TipWGCtrl.Instance:OpenItem({item_id = join_item_id})
            return
        end

        local data = {}
        data.opponent_index = 0
        data.ignore_rank_pos = 1
        data.rank_pos = 0
        data.is_skip = 1
        data.opponent_uuid = self.show_data.target_uuid
        ArenaTiantiWGCtrl.Instance:ResetFieldFightReq(data, 1)
        self:Close()
        ArenaTiantiWGCtrl.Instance:CloseRecordTip()
    end

    if my_fight >= self.show_data.opponent_capability then
        fight_func()
    else
        TipWGCtrl.Instance:OpenAlertTips(Language.Field1v1.TianTiTxt13, fight_func)
    end
end