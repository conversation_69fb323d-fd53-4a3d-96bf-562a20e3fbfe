local CheckResManager = {}
local loader_load_t = {}
local loader_load_pool_warned = {}
local gameobj_t = {}

function CheckResManager:OnLuaCall(event, ...)
	local params = {...}

	if event == "new_gameobj_loader" then
		self:OnNewGameobjLoader(params[1])
	elseif event == "del_gameobj_loader" then
		self:OnDelGameobjLoader(params[1])
	elseif event == "gameobj_loader_load" then
		self:OnGameobjLoaderLoad(params[1], params[2], params[3])
	elseif event == "gameobj_loader_load_complete" then
		self:OnGameobjLoaderLoadComplete(params[1], params[2], params[3], params[4])
	elseif event == "push_gameobj_pool" then
		self:OnPushGameobjPool(params[1], params[2])
	elseif event == "pop_gameobj_pool" then
		self:OnPopGameobjPool(params[1], params[2])
	elseif event == "instantiate_gameobj" then
		self:OnInstantiateGameobj(params[1], params[2])
	elseif event == "destroy_gameobj" then
		self:OnDestroyGameobj(params[1])
	end
end

function CheckResManager:Update(now_time, elapse_time)
	if self:IsGameing() then
		self:CheckLoaderUsePool()
	end
end

function CheckResManager:IsGameing()
	if ViewManager and ViewManager.Instance and ViewManager.Instance:IsOpen(ViewName.Main) then
		return true
	end

	return false
end

function CheckResManager:OnNewGameobjLoader(loader)
	-- body
end

function CheckResManager:OnDelGameobjLoader(loader)
	-- body
end

function CheckResManager:OnGameobjLoaderLoad(loader, bundle_name, asset_name)
	-- if not loader.is_use_objpool then
	-- 	local key = bundle_name .. " " .. asset_name
	-- 	if not loader_load_pool_warned[key] then
	-- 		if nil == loader_load_t[key] then
	-- 			loader_load_t[key] = {}
	-- 			loader_load_t[key].first_load_time = GlobalUnityTime
	-- 			loader_load_t[key].load_count = 0
	-- 			loader_load_t[key].bundle_name = bundle_name
	-- 			loader_load_t[key].asset_name = asset_name
	-- 			loader_load_t[key].trackback = nil
	-- 		end

	-- 		loader_load_t[key].load_count = loader_load_t[key].load_count + 1
	-- 		if loader_load_t[key].load_count >= 3 and nil == loader_load_t[key].trackback then
	-- 			loader_load_t[key].trackback = debug.traceback()
	-- 		end
	-- 	end
	-- end
end

-- 检测loader加载频繁对象却没有使用对象池
function CheckResManager:CheckLoaderUsePool()
	-- local del_list = {}
	-- for k,v in pairs(loader_load_t) do
	-- 	if v.load_count >= 3 then
	-- 		table.insert(del_list, k)
	-- 		print_error(string.format("频繁加载的对象 %s %s %s次, 请使用对象池，请设置SetIsUseObjPool(true)。修改成对象池一定要小心！！！ \n %s", v.bundle_name, v.asset_name, v.load_count, v.trackback))
	-- 		GameRoot.AddLuaWarning(string.format("【Lua性能问题】频繁加载的对象%s %s %s次, 使用对池将有助于提升性能？修改成对象池一定要小心！！！不懂修改找主程！！！(查看控制台日志)", v.bundle_name, v.asset_name, v.load_count), "Normal")
	-- 	end
	-- 	if v.first_load_time + 4 < GlobalUnityTime then
	-- 		table.insert(del_list, k)
	-- 	end
	-- end

	-- for i,v in ipairs(del_list) do
	-- 	loader_load_t[v] = nil
	-- 	loader_load_pool_warned[v] = true
	-- end
end

function CheckResManager:OnGameobjLoaderLoadComplete(loader, bundle_name, asset_name, gameobj)
	if nil == gameobj then return end

	if nil == gameobj_t[gameobj] then
		local particystem_objs = gameobj:GetComponentsInChildren(typeof(UnityEngine.ParticleSystem))
		if particystem_objs.Length > 0 then
			if not loader.is_use_objpool then
				print_error("加载特效请使用对象池，设置SetIsUseObjPool为true", particystem_objs[0].name, bundle_name, asset_name)
			end
			-- 多个粒子的非UI特效需要加品控
			-- if particystem_objs.Length > 1 then
			-- 	local gameobject_attach = gameobj:GetComponent(typeof(QualityControlActive))
			-- 	local ui_effect = gameobj:GetComponent(typeof(UIEffect))
			-- 	local ui_particle = gameobj:GetComponentInChildren(typeof(UiParticles))
			-- 	if nil == ui_effect and nil == ui_particle and nil == gameobject_attach then
			-- 		print_error("非UI特效必须让美术用QualityControlActive加品质控制", bundle_name, asset_name)
			-- 	end
			-- end
		end
	end

	gameobj_t[gameobj] = {owner = "gameobject_loader", loader = loader, bundle_name = bundle_name, asset_name = asset_name}
end

function CheckResManager:OnPushGameobjPool(pool, gameobj)
	if nil ~= gameobj_t[gameobj] then
		gameobj_t[gameobj].owner = "gameobj_pool"
	end
end

function CheckResManager:OnPopGameobjPool(pool, gameobj)
	if nil ~= gameobj_t[gameobj] then
		gameobj_t[gameobj].owner = nil
	end
end

function CheckResManager:OnInstantiateGameobj(prefab, gameobj)
	-- print_error("instantiate_gameobj", prefab.name, gameobj.name)
end

function CheckResManager:OnDestroyGameobj(gameobj)
	local t = gameobj_t[gameobj]
	if nil == t then
		return
	end

	if t.owner == "gameobject_loader" then
		print_error("使用GameLoader来加载对象，必须使用loader中的DeleteMe或者Destroy（使用区别问主程）来释放，马上处理！！！", t.bundle_name, t.asset_name)
	end

	if t.owner == "gameobj_pool" then
		print_error("在对象池里的对象禁止直接移除，将引起严重bug。马上处理!!!", t.bundle_name, t.asset_name)
	end 
	
	gameobj_t[gameobj] = nil
end

return CheckResManager