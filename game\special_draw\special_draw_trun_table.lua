SpecialDrawTrunTable = SpecialDrawTrunTable or BaseClass(SafeBaseView)

function SpecialDrawTrunTable:__init()
    self:SetMaskBg(false, true)
    self:AddViewResource(0, "uis/view/special_draw_ui_prefab", "layout_special_trun_table")
    self.ani_is_playing = false
end

function SpecialDrawTrunTable:LoadCallBack()
    self.cur_cfg = SpecialActivityWGData.Instance:GetDrawAllInfo(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)

    for i = 2, 3 do
        self.node_list["btn_draw_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickRecord, self, i))
        self.node_list["btn_icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowDrawItemTips, self))
    end

    self.node_list["btn_one_draw"].button:AddClickListener(BindTool.Bind2(self.OnClickRecord, self, 1))
    XUI.AddClickEventListener(self.node_list["turn_table_jump_ani"], BindTool.Bind(self.AniOnClickJump, self)) --跳过动画
    XUI.AddClickEventListener(self.node_list["rule_tips"], BindTool.Bind(self.OnClickTipsBtn, self))
    XUI.AddClickEventListener(self.node_list["click_mask"], BindTool.Bind(self.OnClickMask, self)) --遮罩点击

    self.special_draw_item_data_change = BindTool.Bind(self.OnDrawItemChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.special_draw_item_data_change)

    if nil == self.reward_show_cell then
        self.reward_show_cell = {}
        for i = 1, 7 do
            self.reward_show_cell[i] = ItemCell.New(self.node_list["turn_table_item_pos" .. i])
        end
    end

    if not self.all_reward_list then
        self.all_reward_list = AsyncListView.New(SpecialDrawAllRewardRender,self.node_list["special_reward_list"])
    end

    if not self.turn_table_task_list then
        self.turn_table_task_list = AsyncListView.New(SpecialDrawTaskRewardRender,self.node_list["turn_table_task_list"])
    end
end

function SpecialDrawTrunTable:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("special_draw_down") then
		CountDownManager.Instance:RemoveCountDown("special_draw_down")
	end

    if self.special_draw_item_data_change then
        ItemWGData.Instance:UnNotifyDataChangeCallBack(self.special_draw_item_data_change)
        self.special_draw_item_data_change = nil
    end

    if self.reward_show_cell then
        for k, v in pairs(self.reward_show_cell) do
            v:DeleteMe()
        end
        self.reward_show_cell = nil
    end

    if self.all_reward_list then
        self.all_reward_list:DeleteMe()
        self.all_reward_list = nil
    end

    if self.turn_table_task_list then
        self.turn_table_task_list:DeleteMe()
        self.turn_table_task_list = nil
    end

    if self.tween then
        self.tween:Kill()
        self.tween = nil
    end

    self.cur_cfg = nil
    self.ani_is_playing = false
end

function SpecialDrawTrunTable:OpenCallBack()
    SpecialActivityWGCtrl.Instance:SendDrawReq(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1, OA_SPECIAL_DRAW_OPERATE_TYPE.INFO)
end

function SpecialDrawTrunTable:OnFlush(param_t)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushshowView()
        elseif k == "play_ani" then --播放  动画
            self.ani_is_playing = true
            self:PlayAnimation()
        end
	end

    self:LoginTimeCountDown()
end

function SpecialDrawTrunTable:PlayAnimation()
    if self.is_jump_ani then
        self.ani_is_playing = false
        SpecialActivityWGCtrl.Instance:OpenRewardView(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
        return
    end

    local ani_time = SpecialActivityWGData.Instance:GetDelayTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    self.node_list["click_mask"]:SetActive(true)
    self.tween = DG.Tweening.DOTween.Sequence()
    local tween_rotate = self.node_list.turn_table_roll_father.transform:DORotate(Vector3(0, 0, -360 * 2.5),
    2.5, DG.Tweening.RotateMode.FastBeyond360)
    tween_rotate:SetEase(DG.Tweening.Ease.OutCubic)
    self.tween:Append(tween_rotate)
    self.tween:OnUpdate(function()
        local rotate_z = self.node_list.turn_table_roll_father.transform.localEulerAngles.z
        local tween_idx = self:GetCurPointIndex(rotate_z)
        for i = 1, 7 do
            self.node_list["turn_table_highlight"..i]:SetActive(i == tween_idx)
        end
    end)
    self.tween:OnComplete(function()
        self.tween:Kill()
        self.tween = nil
        for i = 1, 7 do
            self.node_list["turn_table_highlight"..i]:SetActive(false)
        end
    end)

    self.play_ani = GlobalTimerQuest:AddDelayTimer(function()
        self.node_list["click_mask"]:SetActive(false)
        GlobalTimerQuest:CancelQuest(self.play_ani)
        self.play_ani = nil
        self.ani_is_playing = false
        SpecialActivityWGCtrl.Instance:OpenRewardView(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    end, ani_time)
    
end

function SpecialDrawTrunTable:GetCurPointIndex(rotate_z)
    local rotate = 360 / 7
    return 7 - math.floor((rotate_z / rotate))
end


function SpecialDrawTrunTable:OnClickMask()
    if self.ani_is_playing then
        TipWGCtrl.Instance:ShowSystemMsg(Language.SpecialDraw.IsWorkIng)
        return 
    end
end

--跳过抽奖动画按钮
function SpecialDrawTrunTable:AniOnClickJump()
	SpecialActivityWGData.Instance:SetJumpAni(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
	self:FlushAniStatus()
end

--跳过动画按钮状态
function SpecialDrawTrunTable:FlushAniStatus()
	self.is_jump_ani = SpecialActivityWGData.Instance:GetJumpAni(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
	self.node_list["turn_table_jump_toggle"]:SetActive(self.is_jump_ani)
end

function SpecialDrawTrunTable:FlushshowView()
    self:FlushDrawBtnShow()
    self:FlushAniStatus()
    local show_all_list, zhenxi_list = SpecialActivityWGData.Instance:GetRewardShowCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    --珍稀奖励展示
    if zhenxi_list then
        for i = 1, #zhenxi_list do
            if i < 8 then
                self.reward_show_cell[i]:SetData(zhenxi_list[i].item)
            end
        end
    end

    if show_all_list then
        self.all_reward_list:SetDataList(show_all_list)
    end

    local task_reward_list = SpecialActivityWGData.Instance:GetDrawTimesRewardList(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    if task_reward_list then
        self.turn_table_task_list:SetDataList(task_reward_list)
    end

    -- local baodi_times = SpecialActivityWGData.Instance:GetNextBigRewardTimes()
    -- self.node_list["draw_times"].text.text = baodi_times
end

function SpecialDrawTrunTable:OnClickTipsBtn()
    local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.SpecialDraw.RuleTitle)
	role_tip:SetContent(Language.SpecialDraw.RuleDesc)
end

--抽奖
function SpecialDrawTrunTable:OnClickRecord(draw_type) --抽奖
    local cfg = SpecialActivityWGData.Instance:GetDrawConsumeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    cfg = cfg[draw_type]
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg.cost_item_id)
    --检查道具数量
    if num >= cfg.cost_item_num then
        SpecialActivityWGData.Instance:CacheOrGetDrawIndex(draw_type)
        --发送协议
        SpecialActivityWGCtrl.Instance:SendDrawReq(
            ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1,
            OA_SPECIAL_DRAW_OPERATE_TYPE.DRAW,
            cfg.mode
        )
    else
        SpecialActivityWGCtrl.Instance:ClickUseDrawItem(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1, draw_type, function ()
            self:OnClickDrawBuy(draw_type)
        end)
    end
end

function SpecialDrawTrunTable:FlushDrawBtnShow(is_flush_num) --刷新抽奖次数
    local cfg = SpecialActivityWGData.Instance:GetDrawConsumeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    if cfg == nil then
        return
    end

    local item_cfg
    local mode_cfg = SpecialActivityWGData.Instance:GetDrawItem(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    local item_id = mode_cfg.cost_item_id
    local count
    for i = 2, 3 do
        if cfg[i] then
            if not is_flush_num then
                item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
                if not IsEmptyTable(item_cfg) then
                    --道具图标
                    self.node_list["btn_icon_" .. i].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
                end
                --抽几次
                self.node_list["txt_buy_" .. i].text.text = string.format(Language.SpecialDraw.DrawBtnDesc, cfg[i].times)
                -- --折扣
                --local is_zhekou = cfg[i].count ~= cfg[i].cost_item_num
                --self.node_list["btn_discount_" .. i]:SetActive(is_zhekou)
                -- if is_zhekou then
                --     self.node_list["txt_lh_discount_" .. i].text.text = cfg[i].cost_item_num .. "折"
                -- end
            end
            --道具红点数量
            count = ItemWGData.Instance:GetItemNumInBagById(item_id)
            self.node_list["btn_red_" .. i]:SetActive(count >= cfg[i].cost_item_num)
            local color = count >= cfg[i].cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
            local left_str = ToColorStr(count, color) 
            self.node_list["btn_red_num_" .. i].text.text = left_str .."/".. cfg[i].cost_item_num
            self.node_list["btn_red_num_"..i].text.color = Str2C3b(color)

        end
    end

    if cfg[1] then
        count = ItemWGData.Instance:GetItemNumInBagById(item_id)
        self.node_list["btn_one_red"]:SetActive(count >= cfg[1].cost_item_num)
    end
end

function SpecialDrawTrunTable:ShowDrawItemTips()
    local cfg = SpecialActivityWGData.Instance:GetDrawItem(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.cost_item_id})
end

--点击购买
function SpecialDrawTrunTable:OnClickDrawBuy(draw_type)
    local cfg = SpecialActivityWGData.Instance:GetDrawConsumeCfg(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    local item_cfg = SpecialActivityWGData.Instance:GetDrawItem(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    local cur_cfg = cfg[draw_type]
    if cur_cfg == nil then
        return
    end

    local num = ItemWGData.Instance:GetItemNumInBagById(item_cfg.cost_item_id)
    local consume = item_cfg.cost_gold * (cur_cfg.cost_item_num - num)
	--检查仙玉
	local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
	--足够购买，不足弹窗
    if enough then
        SpecialActivityWGData.Instance:CacheOrGetDrawIndex(draw_type)
		--发送协议
        SpecialActivityWGCtrl.Instance:SendDrawReq(
            ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1,
            OA_SPECIAL_DRAW_OPERATE_TYPE.DRAW,
            cur_cfg.mode
        )
	else
		VipWGCtrl.Instance:OpenTipNoGold()
	end
end

--物品监听刷新按钮显示
function SpecialDrawTrunTable:OnDrawItemChange(item_id)
    local check_list = SpecialActivityWGData.Instance:GetItemDataChangeList(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    if check_list ~= nil then
        for i, v in pairs(check_list) do
            if v == item_id then
                self:FlushDrawBtnShow(true)
                return
            end
        end
    end
end

------------------------------------活动时间倒计时
function SpecialDrawTrunTable:LoginTimeCountDown()
       local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
       if time > 0 then
           if CountDownManager.Instance:HasCountDown("special_draw_down") then
               CountDownManager.Instance:RemoveCountDown("special_draw_down")
           end
   
           CountDownManager.Instance:AddCountDown("special_draw_down", 
               BindTool.Bind(self.UpdateCountDown, self), 
               BindTool.Bind(self.OnComplete, self), 
               nil, time, 1)
       else
           self:OnComplete()
       end
   end
   
   function SpecialDrawTrunTable:UpdateCountDown(elapse_time, total_time)
       local valid_time = total_time - elapse_time
       if valid_time > 0 then
           self.login_act_date = TimeUtil.FormatUnixTime2Date()
           self.node_list["time_down"].text.text = TimeUtil.FormatSecondDHM8(valid_time)
       end
   end
   
   function SpecialDrawTrunTable:OnComplete()
       self.node_list["time_down"].text.text = ""
   end



SpecialDrawAllRewardRender = SpecialDrawAllRewardRender or BaseClass(BaseRender) 

function SpecialDrawAllRewardRender:LoadCallBack()
    self.show_item = ItemCell.New(self.node_list.pos)
end

function SpecialDrawAllRewardRender:__delete()
    if self.show_item then
        self.show_item:DeleteMe()
    end
    self.show_item = nil
    self.item_id = 0
end

function SpecialDrawAllRewardRender:SetData(data)
    self.data = data

    if not IsEmptyTable(self.data) and not IsEmptyTable(self.data.item) then
        if self.item_id ~= self.data.item.item_id then
            self.item_id = self.data.item.item_id
            self.show_item:SetData(self.data.item)
        end
    end
end

SpecialDrawTaskRewardRender = SpecialDrawTaskRewardRender or BaseClass(BaseRender)
function SpecialDrawTaskRewardRender:LoadCallBack()
    self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
    self.item_cell = ItemCell.New(self.node_list["item_pos"])
end

function SpecialDrawTaskRewardRender:ReleaseCallBack()
    if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
    end
end

function SpecialDrawTaskRewardRender:OnFlush()
    if not self.data then
        return
    end
    
    local data = self.data.data
    if data == nil then
        return
    end

    local draw_times = SpecialActivityWGData.Instance:GetCurDrawTimes(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    local color = draw_times >= data.need_times and COLOR3B.L_GREEN or COLOR3B.L_RED
    local str = draw_times .. "/" .. data.need_times
    local desc = ToColorStr(str, color)
    self.node_list["task_desc"].text.text = string.format(Language.SpecialDraw.TxtTimes, desc)

    local is_get = SpecialActivityWGData.Instance:GetTimesRewardIsGet(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1, data.seq)
    local is_show = data.need_times <= draw_times and (not is_get)

    self.node_list["red"]:SetActive(is_show)
    self.node_list["have_get"]:SetActive(is_get)
    self.node_list["btn_lingqu"]:SetActive(not is_get)
    XUI.SetButtonEnabled(self.node_list["btn_lingqu"], is_show)

    self.item_cell:SetData(data.itemlist[0])
end

function SpecialDrawTaskRewardRender:OnClickGet()
    if not self.data or not self.data.data then
        return
    end

    local draw_times = SpecialActivityWGData.Instance:GetCurDrawTimes(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1)
    local data = self.data.data
    local is_get = SpecialActivityWGData.Instance:GetTimesRewardIsGet(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1, data.seq)
    if draw_times >= data.need_times and (not is_get) then
        SpecialActivityWGCtrl.Instance:SendDrawReq(
            ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_SPECIAL_DRAW1,
            OA_SPECIAL_DRAW_OPERATE_TYPE.TIMES_REWARD,
            data.seq
        )
    end
end