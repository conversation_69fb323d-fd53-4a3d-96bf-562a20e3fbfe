﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Text;
using LuaInterface;
using Nirvana;
using UnityEngine;

public class LuaMonitor
{
    private class CallInfo
    {
        public string file;
        public int line;
        public string name;
        public string symbol;
        public int statckLayer = 0;
    }

    private class StatisticsInfo
    {
        public string fileName;
        public string funName;
        public int line;
        public int callMethodTimes = 0;
        public bool isDirty = false;

        public List<string> childStatisticsList = new List<string>();
    }

    private const int LUA_HOOKCALL = 0;
    private const int LUA_HOOKRET = 1;
    private const int ProfileMask = (1 << LUA_HOOKCALL) | (1 << LUA_HOOKRET);

    private static int callIndex = 0;
    private static int callMaxCount = 10000;
    private static CallInfo[] callStack = new CallInfo[callMaxCount];
    private static Dictionary<string, StatisticsInfo> statisticsDic = new Dictionary<string, StatisticsInfo>();
    private static int curStatckLayer = 0;
    private static int warningMaxLayer = 8000;

    public static bool IsValid
    {
        get
        {
            return GameRoot.Instance != null && GameRoot.Instance.LuaState != null;
        }
    }

    public static bool Profiling { get; private set; }

    public static void Start()
    {
        var luaState = GameRoot.Instance.LuaState;
        luaState.LuaSetHook(TraceHandler, ProfileMask, 0);
        Scheduler.AddFrameListener(Update);
    }

    public static void Stop()
    {
        var luaState = GameRoot.Instance.LuaState;
        luaState.LuaSetHook(null, ProfileMask, 0);
    }

    private static void TraceHandler(IntPtr L, ref Lua_Debug debugInfo)
    {
        LuaDLL.lua_getinfo(L, "nS", ref debugInfo);

        if (callIndex >= callMaxCount)
        {
            return;
        }

        if (debugInfo.eventcode == LUA_HOOKCALL)
        {
            CallInfo callInfo = callStack[callIndex];
            if (null == callStack[callIndex])
            {
                callInfo = new CallInfo();
                callStack[callIndex] = callInfo;
            }

            callInfo.file = debugInfo.short_src;
            callInfo.line = debugInfo.linedefined;
            callInfo.name = debugInfo.name;
            ++callIndex;

            callInfo.statckLayer = curStatckLayer;
            ++curStatckLayer;
        }
        else if (debugInfo.eventcode == LUA_HOOKRET)
        {
            while (callIndex > 0)
            {
                --callIndex;
                CallInfo callInfo = callStack[callIndex];
                if (callInfo.line == debugInfo.linedefined && callInfo.file == debugInfo.short_src && callInfo.name == debugInfo.name)
                {
                    int layer = curStatckLayer - callInfo.statckLayer;
                    if (layer >= warningMaxLayer)
                    {
                        StatisticsInfo statisticsInfo;
                        var key = debugInfo.short_src + debugInfo.linedefined;
                        if (!statisticsDic.TryGetValue(key, out statisticsInfo))
                        {
                            statisticsInfo = new StatisticsInfo();
                            statisticsInfo.fileName = debugInfo.short_src;
                            statisticsInfo.funName = debugInfo.name;
                            statisticsInfo.line = debugInfo.linedefined;
                            statisticsDic.Add(key, statisticsInfo);
                        }

                        // 把造成该方法复杂度过大的原因记录
                        int tempIndex = callIndex;
                        HashSet<string> set = new HashSet<string>();
                        for (int i = 0; i < layer && tempIndex < callMaxCount; i++)
                        {
                            CallInfo tempCallInfo = callStack[tempIndex++];
                            if (null == tempCallInfo || set.Contains(tempCallInfo.name))
                            {
                                continue;
                            }

                            set.Add(tempCallInfo.name);
                            if (curStatckLayer - tempCallInfo.statckLayer > 1000)
                            {
                                string statistcs = string.Format("{0},{1},{2}:{3}", tempCallInfo.file, tempCallInfo.line, tempCallInfo.name, curStatckLayer - tempCallInfo.statckLayer);
                                statisticsInfo.childStatisticsList.Add(statistcs);
                            }
                        }

                        statisticsInfo.callMethodTimes = layer;
                        statisticsInfo.isDirty = true;
                    }
                    
                    break;
                }
            }
        }
    }

    private static void Update()
    {
        callIndex = 0;
        curStatckLayer = 0;

        foreach (var kv in statisticsDic)
        {
            if (kv.Value.isDirty)
            {
                string reason = "";
                foreach (var item in kv.Value.childStatisticsList)
                {
                    reason += item + "   ";
                }
                Debug.LogErrorFormat("lua代码写得太复杂：{0}, 费性能! 请及时处理! {1}, {2}, {3}, {4}", kv.Value.callMethodTimes, kv.Value.fileName, kv.Value.line, kv.Value.funName, reason);
               

                kv.Value.childStatisticsList.Clear();
                kv.Value.isDirty = false;
            }
        }
    }
}
