MysteryBoxWGData = MysteryBoxWGData or BaseClass()

MysteryBoxWGData.CARD_PACKAGE_NUM = 4
MysteryBoxWGData.BOX_TOGGLE_NUM = 3
MysteryBoxWGData.MAX_CONVERT_NUM = 32 --协议已兑换次数数组最大长度
MysteryBoxWGData.MAX_CARD_REWARD_NUM = 16 --协议会发的大奖道具数量最大长度

--1：图鉴任务 2：抽奖次数任务
MysteryBoxWGData.TASK_TYPE = {
    HAND_BOOK = 1,
    DRAW_NUM = 2
}

--1：大奖 2：普通
MysteryBoxWGData.DRAW_REWARD_TYPE = {
    REWARD = 1,
    NORMAL = 2
}

--toggleindex与抽奖次数映射
MysteryBoxWGData.DRAW_NUM_TABLE= {
    [1] = 1,
    [2] = 10,
    [3] = 50,
}
MysteryBoxWGData.DRAW_NUM_TO_INDEX= {
    [1] = 1,
    [10] = 2,
    [50] = 3,
}

function MysteryBoxWGData:__init()
	if MysteryBoxWGData.Instance then
		ErrorLog("[MysteryBoxWGData]: attempt to create singleton twice!")
		return
	end
	MysteryBoxWGData.Instance = self

    self:InitConfig()

    RemindManager.Instance:Register(RemindName.MysteryBox, BindTool.Bind(self.GetMysteryBoxRemind, self))
end

function MysteryBoxWGData:__delete()
	MysteryBoxWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.MysteryBox)
end

---------------------------------------配置方法 start----------------------------------
-- 初始化配置
function MysteryBoxWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("blind_box_card_config_auto")
    if cfg then
        self.draw_item_cfg = ListToMap(cfg.reward, "reward_lib", "reward_id")
        self.draw_package_cfg = ListToMap(cfg.active, "grade", "seq")
        self.draw_task_cfg_list = ListToMap(cfg.count_reward, "grade", "seq")
        self.handbook_task_cfg_list = ListToMap(cfg.handbook_task, "grade", "seq")
        self.covert_cfg_list = ListToMap(cfg.convert, "grade", "seq")
        self.grade_cfg = ListToMap(cfg.grade, "grade")
        self.gailv_cfg = ListToMap(cfg.item_random_desc, "grade", "seq", "number")
        self.handbook_suit_cfg = ListToMapList(cfg.handbook_suit, "grade")
        self.other_cfg = cfg.other[1]
        self.consume_item_cfg = ListToMap(cfg.active, "consume_item_id")
    end
    
end

function MysteryBoxWGData:GetGradeCfg()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    for k, v in pairs(self.grade_cfg) do
        if open_day >= v.start_server_day and open_day <= v.end_server_day then
            return v
        end
    end
end

function MysteryBoxWGData:GetGrade()
    return (self:GetGradeCfg() or {}).grade or 0
end

--获取所有奖池的配置列表
function MysteryBoxWGData:GetDrawItemCfg()
    return self.draw_item_cfg
end

--根据卡包seq获取奖池的配置列表
function MysteryBoxWGData:GetRewardItemListCfg(seq)
    local reward_lib = (self:GetDrawPackageCfgBySeq(seq) or {}).reward_lib or 0
    return self.draw_item_cfg and self.draw_item_cfg[reward_lib]
end

--获取卡包的配置列表
function MysteryBoxWGData:GetDrawPackageCfg()
    local grade = self:GetGrade()
    return (self.draw_package_cfg or {})[grade]
end

--根据卡包seq获取卡包的配置
function MysteryBoxWGData:GetDrawPackageCfgBySeq(seq)
    local grade = self:GetGrade()
    return ((self.draw_package_cfg or {})[grade] or {})[seq]
end

--获取抽奖消耗道具
function MysteryBoxWGData:GetDrawConsumeItemId(seq)
    local package_cfg = self:GetDrawPackageCfgBySeq(seq)
    return package_cfg and package_cfg.consume_item_id or 0
end

--获取积分商店数据
function MysteryBoxWGData:GetShopDataList()
    local grade = self:GetGrade()
    return (self.covert_cfg_list or {})[grade]
end

--获取积分道具ID
function MysteryBoxWGData:GetCoverItemId()
    return self.other_cfg and self.other_cfg.score_item or 0
end

--是否抽奖道具
function MysteryBoxWGData:IsMysteryBoxConsumeId(item_id)
    return (self.consume_item_cfg and self.consume_item_cfg[item_id]) ~= nil
end

---------------------------------------配置方法 end----------------------------------

---------------------------------------协议相关数据更新 start----------------------------------
--协议所有数据更新
function MysteryBoxWGData:SetMysterBoxAllData(protocol)
    self.draw_task_flag_list = protocol.draw_task_flag_list
    self.handbook_task_flag_list = protocol.handbook_task_flag_list
    self.draw_num_list = protocol.draw_num_list
    self.convert_score = protocol.convert_score
    self.shop_covert_num_list = protocol.shop_covert_num_list
    self.draw_reward_num_list = protocol.draw_reward_num_list

    self:CalculateBaoDiTips()
    self:SetTotalDrawNum()
    self:SetAllTaskInfoList()
end

--协议抽奖数据更新
function MysteryBoxWGData:SetDrawInfo(protocol)
    self.convert_score = protocol.convert_score

    self.cur_package_id = protocol.card_package_id
    local draw_num = protocol.draw_num
    self.draw_num_list[self.cur_package_id] = draw_num
    self.draw_reward_num_list[self.cur_package_id] = protocol.draw_rewards

    local draw_result_list = protocol.draw_result_list

    self:CalculateBaoDiTips()
    self:SetTotalDrawNum()
    self:SetDrawResultList(draw_result_list)
    self:SetTaskInfoList(MysteryBoxWGData.TASK_TYPE.DRAW_NUM)
end

--计算是否有保底提示
function MysteryBoxWGData:CalculateBaoDiTips()
    local draw_cfg_list = self:GetDrawItemCfg()
    local package_cfg_list = self:GetDrawPackageCfg()
    local draw_num_list = self.draw_num_list
    if IsEmptyTable(draw_cfg_list) or IsEmptyTable(package_cfg_list) then
        return
    end

    local cfg_list, package_cfg, draw_info, reward_num_list, mod_num, get_num
    local need_tips = false
    self.draw_info_list = {}
    for i = 0, MysteryBoxWGData.CARD_PACKAGE_NUM - 1 do
        package_cfg = package_cfg_list[i]--获取卡池配置
        cfg_list = draw_cfg_list[package_cfg.reward_lib]
        reward_num_list = (self.draw_reward_num_list or {})[i] or {}--大奖道具已经抽到次数列表
        need_tips = false

        draw_info = {}
        draw_info.seq = i
        --此卡池已抽奖次数
        draw_info.draw_num = draw_num_list[i] or 0

        --保底提示计算
        for k, v in pairs(cfg_list) do     
            mod_num = draw_info.draw_num % v.player_guarantee
            get_num = reward_num_list[v.reward_id] or 0
            if (v.player_guarantee - mod_num) <= package_cfg_list[i]["guarantee_remain_num"] 
                and v.player_guarantee > 0 and get_num < v.reward_limit then--有保底的道具需要计算保底提示

                need_tips = true
                break
               
            end
        end
        draw_info.need_tips = need_tips

        self.draw_info_list[i] = draw_info
    end
end

--计算全部卡池的抽奖数总和
function MysteryBoxWGData:SetTotalDrawNum()
    local total = 0
    for i = 0, MysteryBoxWGData.CARD_PACKAGE_NUM - 1 do
        total = total + self.draw_num_list[i]
    end
    self.draw_num_list.total = total
end

--卡池抽奖 恭喜获得界面 的数据更新
function MysteryBoxWGData:SetDrawResultList(list)
    local card_package_id = self.cur_package_id
    local draw_cfg = self:GetRewardItemListCfg(card_package_id)
    if IsEmptyTable(draw_cfg) or IsEmptyTable(list) then
        return
    end

    self.draw_result_list = {}
    self.result_bao_di_list = {}
    local item_id
    for i = 1, #list do
        item_id = list[i]
        for k, v in pairs(draw_cfg) do
            if v.reward_item[0].item_id == item_id and v.reward_type == MysteryBoxWGData.DRAW_REWARD_TYPE.REWARD then
                table.insert(self.result_bao_di_list, v.reward_item[0].item_id)
            elseif v.reward_item[0].item_id == item_id and v.reward_type == MysteryBoxWGData.DRAW_REWARD_TYPE.NORMAL then
                table.insert(self.draw_result_list, v.reward_item[0])
            end
        end
    end
end

--协议图鉴任务状态更新
function MysteryBoxWGData:SetHandbookTaskFlagList(protocol)
    self.handbook_task_flag_list = protocol.handbook_task_flag_list
    self:SetTaskInfoList(MysteryBoxWGData.TASK_TYPE.HAND_BOOK)
end

--协议抽奖数任务状态更新
function MysteryBoxWGData:SetDrawTaskFlagList(protocol)
    self.draw_task_flag_list = protocol.draw_task_flag_list
    self:SetTaskInfoList(MysteryBoxWGData.TASK_TYPE.DRAW_NUM)
end

function MysteryBoxWGData:SetAllTaskInfoList()
    for k, v in pairs(MysteryBoxWGData.TASK_TYPE) do
        self:SetTaskInfoList(v)
    end
end

--图鉴任务与抽奖次数任务数据合并
function MysteryBoxWGData:SetTaskInfoList(type)
    local grade = self:GetGrade()

    local cfg_list =  self.draw_task_cfg_list and self.draw_task_cfg_list[grade]
    local flag_list = self.draw_task_flag_list 
    local current_num = self.draw_num_list and self.draw_num_list.total or 0
    if type == MysteryBoxWGData.TASK_TYPE.HAND_BOOK then
        cfg_list = self.handbook_task_cfg_list and self.handbook_task_cfg_list[grade]
        flag_list = self.handbook_task_flag_list
        current_num = self:GetHandbookSuitActNum()
    end

    if IsEmptyTable(cfg_list) or IsEmptyTable(flag_list) then
        return
    end

    if self.task_info_list == nil then
        self.task_info_list = {}
    end
    local info_list = {}
    local info--每个任务cell的数据
    for k, v in pairs(cfg_list) do
        info = {}
        info.task_type = type
        info.is_get_flag = (flag_list or {})[v.seq] or 0
        info.cur_num = current_num
        info.reward_item = v.reward_item
        
        info.need_num = v.need_count
        if type == MysteryBoxWGData.TASK_TYPE.HAND_BOOK then
            info.need_num = v.need_suit_num
        end

        info.seq = v.seq
        info_list[v.seq + 1] = info
    end

    table.sort(info_list, SortTools.KeyLowerSorter("is_get_flag", "seq"))
    self.task_info_list[type] = info_list
end

--获取图鉴组合里已经激活的组合数量
function MysteryBoxWGData:GetHandbookSuitActNum()
    local grade = self:GetGrade()
    local cfg_list = (self.handbook_suit_cfg or {})[grade]
    if IsEmptyTable(cfg_list) then
        return 0
    end

    local active_num = 0
    local handbook_id_list,all_active
    for i = 1, #cfg_list do
        handbook_id_list = Split(cfg_list[i].need_handbook_id, ",")
        all_active = true
        for k, v in pairs(handbook_id_list) do
            if not self:IsHandbookActiveById(tonumber(v)) then
                all_active = false
                break
            end
        end

        if all_active then
            active_num = active_num + 1
        end
    end

    return active_num
end

function MysteryBoxWGData:IsHandbookActiveById(id)
    local single_info = StrangeCatalogWGData.Instance:GetQWSingleServerInfo(id)
    return not IsEmptyTable(single_info) and single_info.level > 0
end

--协议抽奖记录数据更新
function MysteryBoxWGData:SetLogList(protocol)
    self.log_list = protocol.log_list
end

--协议更新积分与商店已兑换数量
function MysteryBoxWGData:SetCovertInfo(protocol)
    self.convert_score = protocol.convert_score
    self.shop_covert_num_list = protocol.shop_covert_num_list
end
---------------------------------------协议相关数据更新 end----------------------------------



---------------------------------红点 start----------------------------------------
function MysteryBoxWGData:GetMysteryBoxRemind()
    if self:GetPointShopRemind() == 1 then
        return 1
    end

    if self:GetAllTaskRemind() == 1 then
        return 1
    end

    return 0
end

--有能够兑换的点数就显示红点
function MysteryBoxWGData:GetPointShopRemind()
    local data_list = self:GetShopDataList()
    if IsEmptyTable(data_list) then
        return 0
    end

    local score = self:GetScore()
    for k, v in pairs(data_list) do
        if score >= v.need_score then
            return 1
        end
    end

    return 0
end

--有红点的任务显示红点
function MysteryBoxWGData:GetAllTaskRemind()
    if IsEmptyTable(self.task_info_list) then
        return 0
    end

    for k, v in pairs(self.task_info_list) do
        for type, info in pairs(v) do
            if info.is_get_flag == 0 and info.cur_num >= info.need_num then
                return 1
            end
        end
    end

    return 0
end

function MysteryBoxWGData:GetTaskRemindByType(type)
    if IsEmptyTable(self.task_info_list) and IsEmptyTable(self.task_info_list[type]) then
        return 0
    end

    for k, v in pairs(self.task_info_list[type]) do
        if v.is_get_flag == 0 and v.cur_num >= v.need_num then
            return 1
        end
    end

    return 0
end
---------------------------------红点 end----------------------------------------


--获取抽奖记录列表数据
function MysteryBoxWGData:GetLogList()
    return self.log_list or {}
end

--获取展开窗口的任务列表数据
function MysteryBoxWGData:GetFoldListData(type)
    return self.task_info_list and self.task_info_list[type]
end

--获取抽奖数据
function MysteryBoxWGData:GetDrawData()
    return self.draw_info_list
end

--获取积分
function MysteryBoxWGData:GetScore()
    return self.convert_score or 0
end

--获取积分商店数据
function MysteryBoxWGData:GetShopCovertNum(seq)
    return self.shop_covert_num_list[seq] or 0
end

--获取抽奖到的普通奖品展示
function MysteryBoxWGData:GetDrawResultList()
    return self.draw_result_list or {}
end

--获取抽奖到的保底物品展示
function MysteryBoxWGData:GetDrawBaodiList()
    return self.result_bao_di_list or {}
end 

--获取当前抽的卡池seq
function MysteryBoxWGData:GetCurPackageId()
    return self.cur_package_id or 0
end

--获取概率面板组装数据
function MysteryBoxWGData:GetGailvShowData()
    if IsEmptyTable(self.gailv_show_data) then
        local grade_cfg = self:GetGradeCfg()
        local gailv_cfg_list = self.gailv_cfg and self.gailv_cfg[grade_cfg.grade]
        local package_cfg = self:GetDrawPackageCfg()
        if IsEmptyTable(gailv_cfg_list) or IsEmptyTable(package_cfg) then
            return
        end

        self.gailv_show_data = {}
        self.gailv_show_data.title_name = grade_cfg.grade_name
        local info_list = {}
        local info, gailv_cfg
        for k, v in pairs(package_cfg) do
            gailv_cfg = gailv_cfg_list[v.seq]
            if not IsEmptyTable(gailv_cfg) then
                info = {}
                info.hl_text = v.seq_name
                info.normal_text = v.seq_name
                info.gailv_data = gailv_cfg
            end
            info_list[v.seq] = info
        end
        self.gailv_show_data.data_list = info_list
    end

    
    return self.gailv_show_data
end