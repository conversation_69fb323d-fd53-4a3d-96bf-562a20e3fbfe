--废弃的
CopperTaskView = CopperTaskView or BaseClass(SafeBaseView)

function CopperTaskView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fuben_info")
	
	self.active_close = false
	self.fb_star = -1
	self.fb_wave = -1
end

function CopperTaskView:__delete()
end

function CopperTaskView:ReleaseCallBack()
	if self.info then
		self.info:SetInstanceParent(self.node_list.layout_fuben)
		self.info:DeleteMe()
		self.info = nil
	end

	self.star = -1
	self.wave = -1
	if CountDownManager.Instance:HasCountDown("star_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("star_time_countdown")
	end

	if self.mainui_open_complete_handle then
		GlobalEventSystem:UnBind(self.mainui_open_complete_handle)
	end

	self.star_effect = nil
	self.is_init = false
	self.node_list.layout_star = nil
end

function CopperTaskView:LoadCallBack()
	-- FuBenPanelWGCtrl.Instance:WelkinClose()
	FuBenPanelWGCtrl.Instance:GetFuBenPanemView():SetActive(false)
	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
	-- if self.info then
	-- 	local mainui_ctrl = MainuiWGCtrl.Instance
	-- 	self.info:SetInstanceParent(parent)
	-- 	self.info:AnchorPosition(151,23.5)

	-- 	self.task_panel_change = true
	-- 	mainui_ctrl:SetTaskPanel(false,152,-179)
	-- 	mainui_ctrl:ChangeTaskBtnName(Language.Task.task_text3)
	-- 	mainui_ctrl:SetAutoGuaJi(true)

	-- 	self.is_init = true
	-- 	self:Flush()
	-- end
	self.star_move_pos_1 = self.node_list.pos_1.transform.position.x
	self.star_move_pos_2 = self.node_list.pos_2.transform.position.x
	self.star_move_pos_3 = self.node_list.pos_3.transform.position.x
	self.node_list.layout_star.transform.position = self.node_list.pos_3.transform.position
	if self.is_need_playani then
		self:FirstMoveAni()
		self.node_list.layout_star = nil
	end
end

--第一段位移
function CopperTaskView:FirstMoveAni()
	if  not self:IsLoaded()  then self.is_need_playani = true return end 
	self.node_list.layout_star.transform:DOMoveX(self.star_move_pos_2, 2):OnComplete(function()
		-- self:WaitForMonment()
	end):SetEase(DG.Tweening.Ease.Linear)
end

function CopperTaskView:WaitForMonment()
	GlobalTimerQuest:AddDelayTimer(function()
				self:SecondMoveAni()
			end,1.5)
	
end

--第二段位移
function CopperTaskView:SecondMoveAni()
	self.node_list.layout_star.transform:DOMoveX(self.star_move_pos_1, 2):OnComplete(function()
		
	end):SetEase(DG.Tweening.Ease.Linear)
end

function CopperTaskView:InitCallBack()
	self.node_list["layout_star"]:SetActive(true)
	local mainui_ctrl = MainuiWGCtrl.Instance

	local parent = mainui_ctrl:GetTaskOtherContent()
	self.info = CopperTaskList.New(self.node_list.root_fuben_info)
	self.info:SetInstanceParent(parent)
	--self.info:AnchorPosition(151,23.7)
		self.info:AnchorPosition(0,0)
	self.task_panel_change = true
	mainui_ctrl:SetTaskPanel(false,152,-179)
	-- mainui_ctrl:ChangeTaskBtnName(Language.Task.task_text3)
	mainui_ctrl:SetAutoGuaJi(true)

	self.is_init = true
	self:Flush()
end

function CopperTaskView:ShowIndexCallBack()
	self:InitStartPosition()
end

function CopperTaskView:InitStartPosition()
	if nil == self.node_list.pos_1 then return end
	self.star_move_pos_1 = self.node_list.pos_1.transform.position.x
	self.star_move_pos_2 = self.node_list.pos_2.transform.position.x
	self.star_move_pos_3 = self.node_list.pos_3.transform.position.x
	self.node_list.layout_star.transform.position = self.node_list.pos_3.transform.position
end

function CopperTaskView:ResetTaskPanel()
	if self.task_panel_change then
		MainuiWGCtrl.Instance:ResetTaskPanel()
		self.task_panel_change = false
	end
end

function CopperTaskView:SetTaskActive()
	-- MainuiWGCtrl.Instance:SetFubenTaskList()
end

function CopperTaskView:CloseCallBack()
	self.fb_star = -1
	MainuiWGCtrl.Instance:SetAutoGuaJi(false)

	if self.info then
		-- self.info:SetInstanceParent(self.node_list.layout_fuben)
		-- self.info:DeleteMe()
		self.info.view.gameObject:SetActive(false)
	end
end

function CopperTaskView:OnFlush()
	if not self.is_init then
		return
	end
	local copper_scence_info = FuBenPanelWGData.Instance:GetCopperScenceInfo()
	if copper_scence_info then
		-- if copper_scence_info.is_pass == 1 and copper_scence_info.is_leave_fb == 0 then
		-- 	if self.node_list["layout_star"] then
		-- 		self.node_list["layout_star"]:SetActive(false)
		-- 	end
		-- 	return
		-- end
		if self.fb_star ~= copper_scence_info.cur_star_num then
			if copper_scence_info.cur_star_num ~= 3 then
				self:InitStartPosition()
				self:FirstMoveAni()
			end
			if copper_scence_info.cur_star_num <= 0 then
	 				self:CompleteTime()
	 		else
			 	local time = copper_scence_info.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime()
				self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), copper_scence_info.next_star_timestamp)
				if CountDownManager.Instance:HasCountDown("star_time_countdown") then
					CountDownManager.Instance:RemoveCountDown("star_time_countdown")
				end
				CountDownManager.Instance:AddCountDown("star_time_countdown", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime, self), nil,time, 1)
			end
			--self.node_list["layout_star"]:SetActive(true)

			local index = 3 == copper_scence_info.cur_star_num and 2 or 1

			local str = Language.FuBenPanel.DropTip .. Language.FuBenPanel.DropDesc[index]
			self.node_list["rich_star_tips"].text.text = str
		end

		if copper_scence_info.cur_wave then
			if self.fb_star == copper_scence_info.cur_star_num and self.fb_wave == copper_scence_info.cur_wave then return end
			self.fb_star = copper_scence_info.cur_star_num
			self.fb_wave = copper_scence_info.cur_wave

			self.info:SetBoCount(string.format("[%s/%d]",copper_scence_info.cur_wave,4))
		end


		self.node_list["img_wu"]:SetActive(false)
		self.node_list.layout_star_1:SetActive(copper_scence_info.cur_star_num > 0)
	

		local time = copper_scence_info.next_star_timestamp - TimeWGCtrl.Instance:GetServerTime()

		if copper_scence_info.cur_star_num > 0 then

			if 1 == copper_scence_info.cur_star_num then
				--self.node_list["img_star_" .. 1]:LoadSprite(ResPath.GetCommon("start_big"))
			elseif 2 == copper_scence_info.cur_star_num then
			end
		else
			self.node_list["img_wu"]:SetActive(true)
		end
	end
end

function CopperTaskView:UpdateTime(elapse_time, total_time)
	local copper_scence_info = FuBenPanelWGData.Instance:GetCopperScenceInfo()
	local prob_str = ""
	if total_time - elapse_time > 0 then
		if Scene.Instance:GetSceneType() == SceneType.COPPER_FB then
			local copper_scence_info = FuBenPanelWGData.Instance:GetCopperScenceInfo()
			local cur_info = FuBenPanelWGData.Instance:GetCopperItemByLayer(copper_scence_info.cur_layer, copper_scence_info.cur_star_num)
			--prob_str = string.format(Language.FuBenPanel.CopperStarTask2, cur_info.three_star_Prob)
			prob_str = ""
		end
		local str = ""
		if copper_scence_info.cur_star_num > 0 then
			str = string.format(Language.FuBenPanel.CopperStarTask, TimeUtil.FormatSecond((total_time - elapse_time), 2), copper_scence_info.cur_star_num - 1)
		else
			str = string.format(Language.FuBenPanel.CopperStarTask1, TimeUtil.FormatSecond((total_time - elapse_time), 2))
		end
		self.node_list["rich_star_tips"].text.text = prob_str..str
	end
end

function CopperTaskView:CompleteTime()
	--self.node_list["layout_star"]:SetActive(false) 7CFFB7FF
	local pet_scence_info = FuBenPanelWGData.Instance:GetCopperScenceInfo()
	if pet_scence_info.cur_star_num > 0 then return end

	local temp_time = FuBenPanelWGData.Instance:GetOutTimer()
	local time = temp_time - TimeWGCtrl.Instance:GetServerTime()
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(temp_time)
	self:UpdateTime(TimeWGCtrl.Instance:GetServerTime(), temp_time)
	if CountDownManager.Instance:HasCountDown("star_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("star_time_countdown")
	end
	CountDownManager.Instance:AddCountDown("star_time_countdown", BindTool.Bind1(self.UpdateTime, self), BindTool.Bind1(self.CompleteTime111, self), nil, time, 1)

end

function CopperTaskView:CompleteTime111( ... )
	-- body
end


CopperTaskList = CopperTaskList or BaseClass(BaseRender)
function CopperTaskList:__init()
	-- body
end

function CopperTaskList:__delete()
	-- body
end

function CopperTaskList:AnchorPosition( x,y )
	self.view.rect.anchoredPosition = Vector2(x,y)
end

function CopperTaskList:SetBoCount( value )
	self.node_list.lbl_boshu.text.text = value
end

function CopperTaskList:SetDesc( str )
	self.node_list.rich_txt.text.text = str
end

function CopperTaskList:LoadCallBack()
	self:SetDesc(Language.FuBenPanel.CopperTask)
end

	-- if self.info then
	-- 	local obj = self.info.node_list.root_info.gameObject
	-- 	self.info:DeleteMe()
	-- 	ResMgr:Destroy(obj)
	-- 	self.info = nil
	-- end

	-- ResMgr:LoadGameobjSync("uis/view/fubenpanel_prefab","layout_fuben_info_2",
	-- 	function (obj)
	-- 		obj.transform:SetParent(parent.transform,false)
	-- 		obj = U3DObject(obj)
	-- 		self.info = CopperTaskList.New(obj)

	-- 		self.info:AnchorPosition(0,-63)
	-- 		self.is_load_info_2 = true
	-- 		if self.need_flush then
	-- 			self.need_flush = false
	-- 			self:Flush()
	-- 		end
	-- 	end)