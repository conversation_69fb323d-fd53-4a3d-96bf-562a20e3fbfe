-- 组队邀请
NewTeamInviteView = NewTeamInviteView or BaseClass(SafeBaseView)

function NewTeamInviteView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(814, 580)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_invite")
	self:SetMaskBg()
	self.select_index = 0
	self.data_list = {}
end

function NewTeamInviteView:__delete()
end

function NewTeamInviteView:ReleaseCallBack()
	if self.invite_list then
		self.invite_list:DeleteMe()
		self.invite_list = nil
	end
	if self.role_head_cell ~= nil then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end
end

--打开界面请求一大波数据
function NewTeamInviteView:OpenCallBack()
	SocietyWGCtrl.Instance:SendFriendInfoReq()
	NewTeamWGCtrl.Instance:SendGetNearRoleList()
	if 0 ~= RoleWGData.Instance.role_vo.guild_id then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
	end
end

function NewTeamInviteView:LoadCallBack()
	--self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(812,520)
    -- self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	self:SetSecondView(nil, self.node_list["size"])
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleInvite
	self.node_list["layout_blank_tip"]:SetActive(false)
	-- self.node_list["lbl_bg"]:SetActive(false)

	self.invite_list = AsyncListView.New(InviteListItem, self.node_list["ph_invite_list"])
    self.node_list["ph_btn_" .. 4]:SetActive(false)
	for i=1,3 do
		self.node_list["ph_btn_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickInviteSelect, self, i))
	end
	self.node_list.btn_invite_all.button:AddClickListener(BindTool.Bind(self.OnClickInviteAll,self))
end

function NewTeamInviteView:OnClickInviteAll()
    local is_invite = false
    local is_merge = false
    local merge_user_id_list = {}
	for k,v in pairs(self.data_list) do
        if v.is_zhandui_index then
            if NewTeamWGData.Instance:GetCacheCDByRoleid(v.uid) <= 0 then
                is_invite = true
                NewTeamWGData.Instance:AddCacheCDList(v.uid)
                NewTeamWGCtrl.Instance:SendInviteUser(v.uid, 0, 0)
            end
        elseif v.team_index <= 0 then
            if NewTeamWGData.Instance:GetCacheCDByRoleid(v.user_id) <= 0 then
                is_invite = true
                NewTeamWGData.Instance:AddCacheCDList(v.user_id)
                NewTeamWGCtrl.Instance:SendInviteUser(v.user_id, self.select_index, 0)
            end
        elseif v.team_index > 0 then
            if NewTeamWGData.Instance:GetCacheCDByRoleid(v.user_id) <= 0 then
            	is_merge = true
	        	NewTeamWGData.Instance:AddCacheCDList(v.user_id)
	        	table.insert(merge_user_id_list, v.user_id)
           end     	      	
		end
	end

    if is_merge and #merge_user_id_list > 0 then
		if #merge_user_id_list == 1 then
			NewTeamWGCtrl.Instance:SendTeamMergeReq(merge_user_id_list[1])
		else
			NewTeamWGCtrl.Instance:SendTeamOneKeyMergeReq(#merge_user_id_list, merge_user_id_list)
		end
	end
	if is_invite or is_merge then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.AutoInviteSucessTip)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OperateFrequencyTip)
	end
end

function NewTeamInviteView:OnClickConfirm()
end

function NewTeamInviteView:ShowIndexCallBack()
	self.node_list["ph_btn_1"].toggle.isOn = true
	self:OnClickInviteSelect(1)

	self:Flush()
end

function NewTeamInviteView:OnFlush()
    for i = 1, 4 do
		self.node_list["HightLight" .. i]:SetActive(i == self.select_index)
	end
	local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
	min_level = min_level or 0
	local team_type,fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
	local target_cfg = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, fb_mode)
	local sex_limit = GameEnum.SEX_NOLIMIT
	local main_role_sex = RoleWGData.Instance:GetRoleAttr("sex")

	if not IsEmptyTable(target_cfg) and target_cfg.fb_type == 9 then
		sex_limit = main_role_sex == 1 and GameEnum.FEMALE or GameEnum.MALE
	end


	local data_list = {}
	if 1 == self.select_index then
		local temp_list = __TableCopy(NewTeamWGData.Instance:GetFriendList())
		for i, v in pairs(temp_list) do
			if v.is_online == 1 and not SocietyWGData.Instance:GetTargetIsTeamMember(v.user_id) and v.level >= min_level then -- v.team_index <= 0 and
				local data = v
				data.team_type = TEAM_INVITE_TYPE.FRIEND
				if sex_limit == GameEnum.SEX_NOLIMIT or sex_limit == v.sex then
					table.insert(data_list, data)
				end
			end
		end
	elseif 2 == self.select_index then
		local temp_list = NewTeamWGData.Instance:GetNearRoleList()
		for k,v in pairs(temp_list) do
			if not SocietyWGData.Instance:GetTargetIsTeamMember(v.orig_uid) and v.level >= min_level then --v.team_index <= 0
				v.team_type = TEAM_INVITE_TYPE.NEAR
				if sex_limit == GameEnum.SEX_NOLIMIT or sex_limit == v.sex then
					table.insert(data_list, v)
				end
			end
		end
	elseif 3 == self.select_index then
		local m_list = GuildDataConst.GUILD_MEMBER_LIST
		local my_uid = RoleWGData.Instance:InCrossGetOriginUid()
		for i = 1, m_list.count do
			local item = m_list.list[i]
			if item.uid ~= my_uid and 1 == item.is_online and not SocietyWGData.Instance:GetTargetIsTeamMember(item.uid) and item.level >= min_level then --and item.team_index <= 0
				if sex_limit == GameEnum.SEX_NOLIMIT or sex_limit == item.sex then
					local datasource = {user_id = item.uid, gamename = item.role_name, level = item.level, sex = item.sex,
						 prof = item.prof, team_index = item.team_index, post = item.post,  is_online = item.is_online,
						 join_time = item.join_time,capability = item.capability, vip_level = item.vip_level , team_type = TEAM_INVITE_TYPE.GUILD
						 , relation_flag = item.relation_flag, fashion_photoframe = item.photframe, shield_vip_flag = item.shield_vip_flag}
					table.insert(data_list, datasource)
				end
			end
		end
	elseif 4 == self.select_index then
		local list = ZhanDuiWGData.Instance:GetZhanDuiOtherMemberList()
		for i, v in ipairs(list) do
			if v.scene_id ~= 0 and not SocietyWGData.Instance:GetTargetIsTeamMember(v.uid) and v.level >= min_level then
				if sex_limit == GameEnum.SEX_NOLIMIT or sex_limit == v.sex then
					local data = v
					data.is_zhandui_index = true
					table.insert(data_list, data)
				end
			end
		end
	end
	
	self.data_list = data_list

	if nil ~= self.data_list and nil ~= self.invite_list then
		self.invite_list:SetDataList(self.data_list)
	end

	if #self.data_list == 0 then
		self.node_list["layout_blank_tip"]:SetActive(true)
		-- self.node_list["lbl_bg"]:SetActive(false)
		-- self.node_list["btn_invite_all"]:SetActive(false)
		if self.select_index == 1 then
			self.node_list["lbl_tips"].tmp.text = (Language.NewTeam.Friends)
		elseif self.select_index == 2 then
			self.node_list["lbl_tips"].tmp.text = (Language.NewTeam.Nearby)
		elseif self.select_index == 3 then
			if 0 ~= RoleWGData.Instance.role_vo.guild_id then
				self.node_list["lbl_tips"].tmp.text = (Language.NewTeam.FairyUnion)
			else
				self.node_list["lbl_tips"].tmp.text = (Language.Common.PleaseJoinGuild)
			end
		elseif self.select_index == 4 then
			if ZhanDuiWGData.Instance:GetIsInZhanDui() then
				self.node_list["lbl_tips"].tmp.text = (Language.NewTeam.NotZhanDuiMemberOnline)
			else
				self.node_list["lbl_tips"].tmp.text = (Language.Common.PleaseJoinZhanDui)
			end
		end
	else
		-- self.node_list["btn_invite_all"]:SetActive(true)
		self.node_list["layout_blank_tip"]:SetActive(false)
		-- self.node_list["lbl_bg"]:SetActive(true)
	end

end

function NewTeamInviteView:ForceFlushInviteList()
	if not self:IsOpen() then return end
	if SocietyWGData.Instance:GetCurTeamMemberNum() == 3 then
		self:Close()
	end
	local index = self.select_index or 2
	if 1 == index then
		SocietyWGCtrl.Instance:SendFriendInfoReq()
	elseif 2 == index then
		NewTeamWGCtrl.Instance:SendGetNearRoleList()
	elseif 3 == index then
		if 0 ~= RoleWGData.Instance.role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
		--else
		--	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
		end
	elseif 4 == index then
		--ZhanDuiWGCtrl.Instance:ReqZhanDuiInfo()
	end
end

function NewTeamInviteView:OnClickInviteSelect(index , force_flush)
	if index == self.select_index and not force_flush then return end
	if 1 == index then
		SocietyWGCtrl.Instance:SendFriendInfoReq()
	elseif 2 == index then
		NewTeamWGCtrl.Instance:SendGetNearRoleList()
	elseif 3 == index then
		if 0 ~= RoleWGData.Instance.role_vo.guild_id then
			GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, RoleWGData.Instance.role_vo.guild_id)
		--else
		--	SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.PleaseJoinGuild)
		end
	elseif 4 == index then
		--ZhanDuiWGCtrl.Instance:ReqZhanDuiInfo()
    end

	self.select_index = index
	self:Flush()
end

function NewTeamInviteView:CreateRoleHeadCell(role_id, role_name, prof, sex, is_online, node, plat_type, server_id, plat_name)
	if self.role_head_cell == nil then
		self.role_head_cell = RoleHeadCell.New(false)
	end
	NewTeamWGCtrl.Instance:QueryTeamInfo(role_id, function(protocol)
		if self:IsLoaded() and self:IsOpen() then
			local main_role = Scene.Instance:GetMainRole()
			local main_role_vo = main_role.vo
			local role_info = {
				role_id = role_id,
				role_name = role_name,
				prof = prof,
				sex = sex,
				is_online = is_online,
				team_index = protocol.team_index,
				team_type = TEAM_INVITE_TYPE.CHAT,
				plat_type = main_role_vo.plat_type,
				plat_name = main_role_vo.plat_name,
				server_id =  main_role_vo.merge_server_id,
			}
			self.role_head_cell:SetRoleInfo(role_info)
			self.role_head_cell:OpenMenu(node)
		end
	end)
end

function NewTeamInviteView:FlushTextInvite()
    if not IsEmptyTable(self.invite_list.cell_list) then
        for k, v in pairs(self.invite_list.cell_list) do
            v:FlushTextInvite()
        end
    end
end

------------------itemRender-----------------
InviteListItem = InviteListItem or BaseClass(BaseRender)

function InviteListItem:__init()
	self.is_myself = false
	XUI.AddClickEventListener(self.node_list["btn_invite"], BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list["head_click"], BindTool.Bind1(self.OnClickHead, self))
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function InviteListItem:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function InviteListItem:FlushTextInvite()
    local role_id = (self.data.orig_uid and self.data.orig_uid > 0) and self.data.orig_uid or self.data.user_id
	if self.data.is_zhandui_index then
		role_id = self.data.uid
	end
    if NewTeamWGData.Instance:GetCacheCDByRoleid(role_id) > 0 then
		local cd = NewTeamWGData.Instance:GetCacheCDByRoleid(role_id)
        self.node_list["text_invite"].tmp.text = string.format(Language.NewTeam.RemainTime, cd)
        XUI.SetButtonEnabled(self.node_list["btn_invite"], false)
        return
    end
    XUI.SetButtonEnabled(self.node_list["btn_invite"], true)
	self.node_list["text_invite"].tmp.text = (Language.NewTeam.Invite)
    -- if self.data.team_index then
	-- 	if 0 >= self.data.team_index then
	-- 		self.node_list["text_invite"].tmp.text = (Language.NewTeam.Invite)
	-- 	else
	-- 		if SocietyWGData.Instance:GetIsInTeam() == 1 then
	-- 			self.node_list["text_invite"].tmp.text = (Language.NewTeam.Merge)
	-- 		end
	-- 	end
	-- else
	-- 	self.node_list["text_invite"].tmp.text = (Language.NewTeam.Invite)
	-- end
end

function InviteListItem:OnFlush()
	if nil == self.data then
		return
	end

	self:FlushTextInvite()

	local name = self.data.gamename
	local role_id = (self.data.orig_uid and self.data.orig_uid > 0) and self.data.orig_uid or self.data.user_id
	if self.data.is_zhandui_index then
		role_id = self.data.uid
		name = self.data.name
	end
	--self.node_list["text_invite"].tmp.text = (Language.NewTeam.Invite)

	if self.data.vip_level and self.data.vip_level > 0 then
		self.node_list["vip_level"].tmp.text = "V" .. self.data.vip_level
	else
		self.node_list["vip_level"].tmp.text = ""
	end

	local level_str = string.format(Language.NewTeam.PTLevel3, self.data.level)
	EmojiTextUtil.ParseRichText(self.node_list["lbl_level"].emoji_text, level_str, 20, COLOR3B.WHITE)

	self.node_list.lbl_role_name.tmp.text = name
	self.node_list.power_icon.image:LoadSprite(ResPath.GetCommonImages(RoleWGData.GetProfIcon(self.data.prof, self.data.sex)))
	
	self.node_list.power_right.tmp.text = self.data.capability
	local relation_flag = bit:d2b_two(self.data.relation_flag or 0)
	local index = -1
	for k,v in pairs(relation_flag) do
		if v == 1 then
			index = k
			break
		end
	end
	local relation_str = Language.NewTeam.TeamMateRelation[index + 2]
	if index + 2 == 1 then
		relation_str = ToColorStr(relation_str, COLOR3B.DEFAULT)
	end
	self.node_list.lbl_relation.tmp.text = relation_str

	local data = {}
	data.role_id = role_id
	data.prof = self.data.prof
	data.sex = self.data.sex
	data.fashion_photoframe = self.data.shizhuang_photoframe or self.data.fashion_photoframe
    self.head_cell:SetData(data)
end

function InviteListItem:OnClickInvite()
	local role_id = (self.data.orig_uid and self.data.orig_uid > 0) and self.data.orig_uid or self.data.user_id

	if Scene.Instance:GetSceneType() == SceneType.Kf_PvP_Prepare then
		local is_my_zhandui = ZhanDuiWGData.Instance:GetTargetIsMyZhanDui(role_id)
		if not is_my_zhandui then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.DifferentZhandui2)
			return
		end
	end
    --战队
	if self.data.is_zhandui_index then
		if 0 == SocietyWGData.Instance:GetIsInTeam() then
			local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
			NewTeamWGCtrl.Instance:SendCreateTeam(0, 1, min_level, max_level)
        end
        NewTeamWGData.Instance:AddCacheCDList(self.data.uid)
		NewTeamWGCtrl.Instance:SendInviteUser(self.data.uid, 0, 1)
	else
		if self.data.team_index > 0 then
			-- if 0 == SocietyWGData.Instance:GetIsInTeam() then
			-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CurNotTeam)
			-- 	return
			-- end

			-- if 0 == SocietyWGData.Instance:GetIsTeamLeader() then
			-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.OnlyLeaderCanHeBing)
			-- 	return
			-- end
            -- NewTeamWGData.Instance:AddCacheCDList(role_id)
			-- --合并
			-- NewTeamWGCtrl.Instance:SendTeamMergeReq(role_id)
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.CantInviteInTeam)
		else
			if 0 == SocietyWGData.Instance:GetIsInTeam() then
				local team_type, fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
				local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
				NewTeamWGCtrl.Instance:SendCreateTeam(team_type, fb_mode, min_level, max_level)
            end
            NewTeamWGData.Instance:AddCacheCDList(self.data.user_id)
			NewTeamWGCtrl.Instance:SendInviteUser(self.data.user_id, self.data.team_type, 1)
		end
	end
end

function InviteListItem:OnClickHead()
	local name = self.data.gamename
	local role_id = (self.data.orig_uid and self.data.orig_uid > 0) and self.data.orig_uid or self.data.user_id
	if self.data.is_zhandui_index then
		role_id = self.data.uid
		name = self.data.name
	end

	BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
		if not self.node_list then
			return
		end

		NewTeamWGCtrl.Instance:CreateInviteRoleHeadCell(param_protocol.role_id,
						name, param_protocol.prof, param_protocol.sex,
						param_protocol.is_online, self.node_list.head_click,
						param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
	end)
end
