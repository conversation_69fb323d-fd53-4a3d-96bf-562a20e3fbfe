function TreasurePalaceView:ZBKGiftLoadIndexCallBack()
    if not self.zbd_gift_list then
        self.zbd_gift_list = AsyncBaseGrid.New()

        local bundle = "uis/view/treasure_palace_ui_prefab"
		local asset = "zbd_gift_list_item"
		self.zbd_gift_list:Create<PERSON><PERSON>s({col = 3, change_cells_num = 1, list_view = self.node_list.zbd_gift_list,
			assetBundle = bundle, assetName = asset, itemRender = ZBKGiftItemRender})
		self.zbd_gift_list:SetStartZeroIndex(false)
    end
end

function TreasurePalaceView:ZBKGiftReleaseCallBack()
    if self.zbd_gift_list then
        self.zbd_gift_list:DeleteMe()
        self.zbd_gift_list = nil
    end
end

function TreasurePalaceView:ZBKGiftShowIndexCallBack()
    
end

function TreasurePalaceView:ZBKGiftOnFlush()
    local data_list = TreasurePalaceData.Instance:GetGiftDataList()
    self.zbd_gift_list:SetDataList(data_list)
end

-----------------------------------------------------------------------
ZBKGiftItemRender = ZBKGiftItemRender or BaseClass(BaseRender)

function ZBKGiftItemRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item)
    end

    XUI.AddClickEventListener(self.node_list.btn_get, BindTool.Bind(self.OnClickGetBtn, self))
end

function ZBKGiftItemRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function ZBKGiftItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.node_list.desc_vip.text.text = string.format(Language.Vip.NameList, self.data.cfg.vip_level_limit)
    local show_item_cfg = self.data.cfg.vip_gift_item[0]
    if show_item_cfg then
        self.item:SetData(show_item_cfg)
        -- local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_cfg.item_id)
        -- self.node_list.desc_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    end
    self.node_list.desc_name.text.text = self.data.cfg.vip_gift_title

    self.node_list.btn_get:CustomSetActive(self.data.can_get)
    self.node_list.flag_sell_out:CustomSetActive(self.data.sell_out)
    self.node_list.flag_is_get:CustomSetActive(self.data.is_get_flag)
    self.node_list.flag_exchange_limit:CustomSetActive(self.data.can_change and not self.data.can_get)

    if self.data.can_change and not self.data.can_get then
        local level = RoleWGData.Instance:GetRoleLevel()
        local vip_level = VipWGData.Instance:GetRoleVipLevel()
        local desc_level_limit = ""

        local is_vip_limit = vip_level < self.data.cfg.vip_level_limit
        if is_vip_limit then
            desc_level_limit = string.format(Language.Vip.NameList, self.data.cfg.vip_level_limit)
        end
        local is_level_limit = level <= self.data.cfg.level_limit

        if is_level_limit then
            local level_str = RoleWGData.GetLevelString2(self.data.cfg.level_limit)
            desc_level_limit = desc_level_limit ~= "" and desc_level_limit .. "," or desc_level_limit
            desc_level_limit = level_str
        end
        self.node_list.desc_level_limit.text.text = string.format(Language.TreasurePalace.GiftLevelLimit, ToColorStr(desc_level_limit, COLOR3B.L_GREEN))
        self.node_list.desc_level_limit:CustomSetActive(is_vip_limit or is_level_limit)

        local has_exchange_count = TreasurePalaceData.Instance:GetVipGiftServerGetCount(self.data.cfg.seq)
        local is_count_limit = self.data.cfg.server_count > 0 -- and has_exchange_count >= self.data.cfg.server_count
        self.node_list.desc_count_limit:CustomSetActive(is_count_limit)
        if is_count_limit then
            self.node_list.desc_count_limit.text.text = string.format(Language.TreasurePalace.GiftAllServerCanChangeCount, self.data.cfg.server_count- has_exchange_count, self.data.cfg.server_count)
        end
    end
end

function ZBKGiftItemRender:OnClickGetBtn()
    if IsEmptyTable(self.data) then
        return
    end

    if self.data.can_get then
        TreasurePalaceCtrl.Instance:SendZhenBaoDianClientReq(ZHENBAODIAN_OPERA_TYPE.BUY_VIP_GIFT, self.data.cfg.seq)
    end
end