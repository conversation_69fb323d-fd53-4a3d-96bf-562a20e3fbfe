#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using UnityEditor;
using UnityEngine;

namespace ProceduralLOD
{
    
    [ShaderBinding("JYShaders/StylizedScene")]
    class JYShaders_StylizedScene : MaterialFactory
    {
        public override bool Instantiate(Material material, out Material lodMaterial)
        {
            Texture2D baseMap = material.GetTexture("_BaseMap") as Texture2D;
            Texture2D normalMap = material.GetTexture("_NormalMap") as Texture2D;
            Texture2D emissionMap = material.GetTexture("_EmissionMap") as Texture2D;
            lodMaterial = null;
            
            if (baseMap == null)
                return false;

            if (material.IsKeywordEnabled("_ENABLE_WEIGHT_BLEND"))
                return false;
                
            Texture2D mainTex = LODTextureUtility.MakeMipTexture(MergeSceneTexture, baseMap, normalMap, emissionMap);
            if (mainTex == null)
                return false;
                
            lodMaterial = new Material(material);
            lodMaterial.shader = Shader.Find("JYShaders/StylizedScene_Basic");
            lodMaterial.SetTexture("_BaseMap", mainTex);
            mainTex.name = material.name;
            return true;
        }
        
        static Texture2D MergeSceneTexture(Texture2D[] textures)
        {
            Texture2D diffuseTex = textures[0];
            Texture2D normalTex = textures[1];
            Texture2D emissionTex = textures[2];

            Debug.Assert(diffuseTex != null);

            Texture2D texture = new Texture2D(diffuseTex.width, diffuseTex.height, diffuseTex.format, true, true);
            texture.filterMode = FilterMode.Point;
            for (int x = 0; x < diffuseTex.width; x++)
            {
                for (int y = 0; y < diffuseTex.height; y++)
                {
                    float u = (float)x / (diffuseTex.width - 1);
                    float v = (float)y / (diffuseTex.height - 1);
                    
                    Color diffuseColor = diffuseTex.GetPixel(x, y, 0);
                    Color normalColor = Color.black;
                    if (normalTex != null)
                    {
                        int coordx = normalTex.width == diffuseTex.width ? x : Mathf.RoundToInt(u * normalTex.width);
                        int coordy = normalTex.height == diffuseTex.height ? y : Mathf.RoundToInt(v * normalTex.height);
                        normalColor = normalTex.GetPixel(coordx, coordy);
                    }
                    Color emissonColor = Color.black;
                    if (emissionTex != null)
                    {
                        int coordx = emissionTex.width == diffuseTex.width ? x : Mathf.RoundToInt(u * emissionTex.width);
                        int coordy = emissionTex.height == diffuseTex.height ? y : Mathf.RoundToInt(v * emissionTex.height);
                        emissonColor = emissionTex.GetPixel(coordx, coordy);
                    }

                    //float metallic = Mathf.GammaToLinearSpace(normalColor.r);
                    //float emission = Mathf.GammaToLinearSpace(emissonColor.r);  
                    float metallic = normalColor.r;
                    float emission = emissonColor.r;
                    metallic = metallic > 0.5f ? metallic : 0;
                    emission = metallic == 0 ? emission : 0;

                    Color finalColor = new Color
                    (
                        diffuseColor.r,
                        diffuseColor.g,
                        diffuseColor.b,
                        (metallic > 0.01f ? metallic * 0.5f : 0) +
                         (emission > 0.01f ? emission * 0.5f + 0.5f : 0)
                    );
                    texture.SetPixel(x, y, finalColor, 0);
                }
            }
            texture.Apply();

            return texture;
        }
    }
    public class MaterialFactory
    {
        private static Dictionary<string, MaterialFactory> s_ShaderToFactoryDict;
            
        static MaterialFactory()
        {
            Type baseType = typeof(MaterialFactory);
            Type[] subClassTypes = baseType.Assembly.GetTypes().Where(t => t.IsSubclassOf(baseType)).ToArray();
            s_ShaderToFactoryDict = new Dictionary<string, MaterialFactory>();
            foreach (var type in subClassTypes)
            {
                var attributes = type.GetCustomAttributes(typeof(ShaderBindingAttribute), false);
                if (attributes.Length > 0)
                {
                    ShaderBindingAttribute shaderBinding = attributes[0] as ShaderBindingAttribute;
                    s_ShaderToFactoryDict.TryAdd(shaderBinding.shaderName, Activator.CreateInstance(type) as MaterialFactory);
                }
            }
        }

        public static bool InstantiateMaterial(Material material, out Material lodMaterial)
        {
            lodMaterial = null;
            if (s_ShaderToFactoryDict.TryGetValue(material.shader.name, out var factory))
            {
                return factory.Instantiate(material, out lodMaterial);
            }

            return false;
        }

        public virtual bool Instantiate(Material material, out Material lodMaterial)
        {
            lodMaterial = null;
            return false;
        }
    }
    class ShaderBindingAttribute : Attribute
    {
        public string shaderName;
        public ShaderBindingAttribute(string shaderName)
        {
            this.shaderName = shaderName;
        }
    }
}

#endif