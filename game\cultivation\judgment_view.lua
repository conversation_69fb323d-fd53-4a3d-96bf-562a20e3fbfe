JudgmentView = JudgmentView or BaseClass(SafeBaseView)


function JudgmentView:__init()
	self:SetMaskBg()
    self.view_style = ViewStyle.Full

    local view_bundle = "uis/view/cultivation_ui_prefab"
    local common_path = "uis/view/common_panel_prefab"

    self:AddViewResource(0, common_path, "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_judgment_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

    self.tab_sub = {}
    self.remind_tab = {}
    self.pos_list = {}

    self.other_cfg = CultivationWGData.Instance:GetXiuWeiOtherCfg()
    self.stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
end

function JudgmentView:LoadCallBack()

    self.cur_hp = 0
    self.total_hp = 1

    XUI.AddClickEventListener(self.node_list.btn_start, BindTool.Bind(self.OnClickStart, self))
    XUI.AddClickEventListener(self.node_list.btn_back, BindTool.Bind(self.OnClickBack, self))
    XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind(self.OnClickGiveUp, self))
    
    for i = 1, 7 do
        self.node_list["btn_thunder_"..i].button:AddClickListener(BindTool.Bind(self.OnClickBtnThunder, self, i))
    end
    -- 雷劫名字 数量
    local cur_stage_cfg = CultivationWGData.Instance:GetCurXiuWeiStageCfg()
    if IsEmptyTable(cur_stage_cfg) then
        return
    end

    local stage_preview_cfg = CultivationWGData.Instance:GetCultivationPreviewCfgByStage(cur_stage_cfg.client_stage)
    self.node_list.text_stage.text.text = string.format(Language.Cultivation.JudgmentStageStr, stage_preview_cfg.stage_name)
end

function JudgmentView:ShowIndexCallBack()

end

function JudgmentView:ReleaseCallBack()
    
    self:ClearAllTimer()
    self.pos_list = {}

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

end

function JudgmentView:CloseCallBack()
    self:ClearAllTimer()
end

function JudgmentView:OnFlush()
    self:FlushModel()
    self:FlushJudgment()
end

function JudgmentView:FlushModel()
	if not self:IsLoaded() then
		return
	end

	if nil == self.role_model then
		local node = self.node_list.role_model
		self.role_model = RoleModel.New()
        local display_data = {
			parent_node = node,
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}

		self.role_model:SetRenderTexUI3DModel(display_data)
		-- self.role_model:SetUI3DModel(node.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.role_model)

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_halo = true, ignore_weapon = true}

		self.role_model:SetModelResInfo(role_vo, special_status_table, nil, SceneObjAnimator.Sit_Idle)
		
	end

	
	if self.role_model  then
		self.role_model:PlaySitAction()
		self.role_model:SetRotation({ x = -10, y = -180, z = 0 })
		self.role_model:FixToOrthographic(self.root_node_transform)
	end

end


--返回修炼界面
function JudgmentView:OnClickBack()
    self:Close()
end

-- 点击开始按钮
function JudgmentView:OnClickStart()
    CultivationWGCtrl.Instance:OpenJudgmentReadyView()
end

-- 渡劫开始
function JudgmentView:JudgmentStart(hp)
    self.cur_hp = hp
    self.total_hp = hp
    self:FlushJudgmentLayer()
    self:FlushJudgment(true)
    self:InitJudgment()
    self:FlushTimer()
    self:CreateThunder()
end


--放弃渡劫
function JudgmentView:OnClickGiveUp()
    TipWGCtrl.Instance:OpenAlertTips(Language.Cultivation.CancelJudgmentTips, function()
		self:FlushJudgment(false)
        self:ClearAllTimer()
	end)
end


function JudgmentView:FlushJudgment(is_judgment)
    self.node_list.ready_layer:CustomSetActive(not is_judgment)
    self.node_list.ready_bg:CustomSetActive(not is_judgment)
    self.node_list.judgment_layer:CustomSetActive(is_judgment)
    self.node_list.judgment_bg:CustomSetActive(is_judgment)

end

function JudgmentView:FlushJudgmentLayer()
    
    -- self.node_list.text_count.text = "共".."".."道"
    -- 分数
    self.node_list.text_score.text = 0

    -- 血量
    
    self.node_list.text_hp.text.text = string.format(Language.Cultivation.JudgmentHpStr,self.cur_hp)
    if self.total_hp ~= 0 then
        self.node_list.slider_hp.slider.value = self.cur_hp/self.total_hp
    end
    
end

function JudgmentView:JudgmentEnd()
    self:ClearAllTimer()
    -- 渡劫成功
    
    CultivationWGCtrl.Instance:JudgmentResult(self.cur_hp > 0)
    if self.cur_hp > 0 then
        self:Close()
    else
        self:FlushJudgment(false)
        CultivationWGCtrl.Instance:JudgmentResultShow(false)
    end
end

function JudgmentView:HideAllBtnAndEffect()
    for index = 1, 7 do
        self.node_list["effect_"..index]:CustomSetActive(false)
        self.node_list["effect_d_"..index]:CustomSetActive(false)
        self.node_list["btn_thunder_"..index]:CustomSetActive(false)
    end

end

function JudgmentView:FlushTimer()
    local total_time = self.other_cfg.judgment_duration  --持续时间
    local interval = self.other_cfg.judgment_thunder_interval    -- 回调间隔

    -- 计时器的创建刷新，需要将旧的计时器进行销毁移除
    -- 倒计时器1 (见count_down.lua)
    self:CleanTimer1()
    self.timer_1 = CountDown.Instance:AddCountDown(total_time, interval,
        -- 回调方法
        BindTool.Bind1(self.CreateThunder, self),
        -- 倒计时完成回调方法
        BindTool.Bind1(self.JudgmentEnd, self)
    )
    self.timer_2 = CountDown.Instance:AddCountDown(total_time, 1,
    -- 回调方法
    function(elapse_time, total_time)
        local time = math.floor(total_time - elapse_time)
        self:UpdateTime(time)
    end,
    -- 倒计时完成回调方法
    function()
        self:UpdateTime(0)
    end
    )

    self:UpdateTime(total_time)
end

function JudgmentView:UpdateTime(time)
    self.node_list.text_count.text.text = string.format(Language.Cultivation.JudgmentTime,TimeUtil.FormatTimeLanguage2(time))
end

-- 清除倒计时器1
function JudgmentView:CleanTimer1()
    if self.timer_1 and CountDown.Instance:HasCountDown(self.timer_1) then
        CountDown.Instance:RemoveCountDown(self.timer_1)
        self.timer_1 = nil
    end

    if self.timer_2 and CountDown.Instance:HasCountDown(self.timer_2) then
        CountDown.Instance:RemoveCountDown(self.timer_2)
        self.timer_2 = nil
    end
end

-- 清除预警倒计时器
function JudgmentView:CleanWarringTimer(index)
    if self.pos_list[index] then
        GlobalTimerQuest:CancelQuest(self.pos_list[index])
        self.pos_list[index] = nil
    end
end

function JudgmentView:ClearAllTimer()
    if self.pos_list then
        for key, value in pairs(self.pos_list) do
            if value then
                GlobalTimerQuest:CancelQuest(value)
                self.pos_list[key] = nil
            end
        end
    end

    self:CleanTimer1()
end

-- 渡劫初始化
function JudgmentView:InitJudgment()
    self.pos_list = {}
    self.warning_duration = self.other_cfg.judgment_warning_duration

    self:HideAllBtnAndEffect()
end

function JudgmentView:RandomThunderPos()
    local random = math.random(1,7)

    -- 如果随机的地方已经有雷就顺序取一个位置
    if self.pos_list[random] then
        for index = 1, 7 do
            if self.pos_list[random] == nil then
                return index
            end
        end
        return 0
    else
        return random
    end
end

function JudgmentView:CreateThunder(elapse_time, total_time)
    local pos = self:RandomThunderPos()
    if pos ~= 0 then
        self:CleanWarringTimer(pos)
        local delay_time = self.warning_duration       -- 延迟时间
        local do_count = 1         -- 执行次数
        local callback = function() 
            self.pos_list[pos] = nil
            self.cur_hp = self.cur_hp - self.stage_cfg.hp_reduce
            if self.cur_hp <= 0 then
                self:JudgmentEnd()
            else
                
                self:FlushJudgmentLayer()
                self:SetBtnAndEffectActiva(pos,false)
                self:SetDianEffectActiva(pos)
            end
            
        end
        -- 相当于一秒后执行一次
        self.pos_list[pos] = GlobalTimerQuest:AddTimesTimer(callback, delay_time, do_count)
        self:SetBtnAndEffectActiva(pos,true)
    end

end


function JudgmentView:OnClickBtnThunder(index)
    self:CleanWarringTimer(index)
    self:SetBtnAndEffectActiva(index,false)
end

function JudgmentView:SetBtnAndEffectActiva(index,is_active)
    self.node_list["btn_thunder_"..index]:CustomSetActive(is_active)
    self.node_list["effect_"..index]:CustomSetActive(is_active)
end

function JudgmentView:SetDianEffectActiva(index)
    self.node_list["effect_d_"..index]:CustomSetActive(false)
    self.node_list["effect_d_"..index]:CustomSetActive(true)
end

