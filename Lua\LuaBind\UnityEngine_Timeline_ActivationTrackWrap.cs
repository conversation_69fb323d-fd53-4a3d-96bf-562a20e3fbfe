﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_Timeline_ActivationTrackWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(UnityEngine.Timeline.ActivationTrack), typeof(UnityEngine.Timeline.TrackAsset));
		<PERSON><PERSON>RegFunction("CreateTrackMixer", CreateTrackMixer);
		<PERSON><PERSON>unction("GatherProperties", GatherProperties);
		<PERSON><PERSON>unction("New", _CreateUnityEngine_Timeline_ActivationTrack);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("postPlaybackState", get_postPlaybackState, set_postPlaybackState);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateUnityEngine_Timeline_ActivationTrack(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Timeline.ActivationTrack obj = new UnityEngine.Timeline.ActivationTrack();
				ToLua.Push(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: UnityEngine.Timeline.ActivationTrack.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateTrackMixer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			UnityEngine.Timeline.ActivationTrack obj = (UnityEngine.Timeline.ActivationTrack)ToLua.CheckObject<UnityEngine.Timeline.ActivationTrack>(L, 1);
			UnityEngine.Playables.PlayableGraph arg0 = StackTraits<UnityEngine.Playables.PlayableGraph>.Check(L, 2);
			UnityEngine.GameObject arg1 = (UnityEngine.GameObject)ToLua.CheckObject(L, 3, typeof(UnityEngine.GameObject));
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			UnityEngine.Playables.Playable o = obj.CreateTrackMixer(arg0, arg1, arg2);
			ToLua.PushValue(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GatherProperties(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			UnityEngine.Timeline.ActivationTrack obj = (UnityEngine.Timeline.ActivationTrack)ToLua.CheckObject<UnityEngine.Timeline.ActivationTrack>(L, 1);
			UnityEngine.Playables.PlayableDirector arg0 = (UnityEngine.Playables.PlayableDirector)ToLua.CheckObject<UnityEngine.Playables.PlayableDirector>(L, 2);
			UnityEngine.Timeline.IPropertyCollector arg1 = (UnityEngine.Timeline.IPropertyCollector)ToLua.CheckObject<UnityEngine.Timeline.IPropertyCollector>(L, 3);
			obj.GatherProperties(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_postPlaybackState(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.ActivationTrack obj = (UnityEngine.Timeline.ActivationTrack)o;
			UnityEngine.Timeline.ActivationTrack.PostPlaybackState ret = obj.postPlaybackState;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index postPlaybackState on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_postPlaybackState(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			UnityEngine.Timeline.ActivationTrack obj = (UnityEngine.Timeline.ActivationTrack)o;
			UnityEngine.Timeline.ActivationTrack.PostPlaybackState arg0 = (UnityEngine.Timeline.ActivationTrack.PostPlaybackState)ToLua.CheckObject(L, 2, typeof(UnityEngine.Timeline.ActivationTrack.PostPlaybackState));
			obj.postPlaybackState = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index postPlaybackState on a nil value");
		}
	}
}

