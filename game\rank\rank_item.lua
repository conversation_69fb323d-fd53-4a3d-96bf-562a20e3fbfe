-------------------------------------统一排行render-------------------------------------------
RankNewItem = RankNewItem or BaseClass(BaseRender)

function RankNewItem:__delete()
	if nil ~= self.role_avatar then
		self.role_avatar:DeleteMe()
		self.role_avatar = nil
	end

	--if nil ~= self.role_head_cell then
	--	self.role_head_cell:DeleteMe()
	--	self.role_head_cell = nil
	--end
end


function RankNewItem:LoadCallBack()
	self:CreateRoleAvater()
end

function RankNewItem:CreateRoleAvater()
	if not self.role_avatar then
	    self.role_avatar = RoleHeadCell.New(false)
	    self.role_avatar:RemoveItems(Language.Menu.PrivateChat)
	    self.role_avatar:RemoveItems(Language.Menu.SendMail)
	    self.role_avatar:RemoveItems(Language.Menu.Blacklist)
		if self.node_list.ph_rankitem_role_root then
			self.node_list.ph_rankitem_role_root.rect.anchoredPosition = Vector2(0,0)
		end
	end
end

function RankNewItem:OnFlush()
	if not self.data then
		return
	end

	-- 是否为仙盟榜
	self.is_guild_rank = self.data.kind == RankKind.Guild

	self:OnSelectChange(self:IsSelectIndex())

	if self.data.vip_level then   -- 设置VIP等级
		local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(self.data.shield_vip_flag)
		self.node_list.vip_text:SetActive(self.data.vip_level >= 1)
		self.node_list.vip_text.text.text = is_hide_vip and "V" or "V".. self.data.vip_level
	else
		self.node_list.vip_text:SetActive(false)
	end

	self:FlushInfo()

	-- 黑名单操作
	local is_black_flag = ChatWGData.Instance:InBlacklist(self.data.user_id)

	self:CreateRoleAvater()
    self.role_avatar:RemoveItemsList(Language.Menu.BlackRemove_2)
    self.role_avatar:RemoveItemsList(Language.Menu.Blacklist)

	if not is_black_flag then
	    -- self.role_avatar:AddItems(Language.Menu.Blacklist)
	    self.role_avatar:RemoveItems(Language.Menu.BlackRemove_2)
	    self.role_avatar:RemoveItemsList(Language.Menu.InviteTeam)
	else
	    self.role_avatar:RemoveItems(Language.Menu.Blacklist)
	    self.role_avatar:RemoveItems(Language.Menu.InviteTeam)
	    self.role_avatar:AddItems(Language.Menu.BlackRemove_2)
	end
end

function RankNewItem:FlushInfo()
	self.node_list.label_1.text.text = self.data.rank_index
	local color = self.data.rank_index <= 3 and "#FFFFFF" or "#FFFFFF"
	local zhiye_text, zhanli_text = RankWGData.Instance:GetRankItemStrengthInfomation(self.data)

	if self.data.rank_type == PersonRankType.CrossCompetition then
		local name = self.data.user_name
		if self.data.reserved3 and self.data.reserved3 ~= 0 then
			name = ToColorStr(name .. "_s" .. (self.data.reserved3 or 0), color)
		else
			name = ToColorStr(name, color)
		end
		self.node_list.name.text.text = name
	else
		self.node_list.name.text.text = ToColorStr(self.data.user_name or self.data.guild_name, color)
	end

	self.node_list.label_3.text.text = ToColorStr(zhiye_text, color)
	self.node_list.label_4.text.text = ToColorStr(zhanli_text, color)
	-- TODO 需要改为新版处理方式
	-- self.node_list.name.shadow.enabled = self.data.rank_index <= 3
	-- self.node_list.label_3.shadow.enabled = self.data.rank_index <= 3
	-- self.node_list.label_4.shadow.enabled = self.data.rank_index <= 3
	self.node_list.label_1:SetActive(self.data.rank_index > 3)
end

function RankNewItem:OnClickEvent()
	if not self.data then
		return
	end

    if self.is_guild_rank then
        if self.data.tuan_zhang_uid == COMMON_CONSTS.GUILD_MENGZHU_ROBOT_ID then
            self.role_avatar:SetRobotInfo(self.data.tuan_zhang_name, self.data.tuan_zhang_prof, self.data.tuan_zhang_sex)
        else
			local role_info = {
				role_id = self.data.tuan_zhang_uid,
				role_name = self.data.tuan_zhang_name,
				prof = self.data.tuan_zhang_prof,
				sex = self.data.tuan_zhang_sex,
			}
            self.role_avatar:SetRoleInfo(role_info)
        end
	--机器人
	elseif self.data.user_id == 0 then
		self.role_avatar:SetRobotInfo(self.data.user_name, self.data.prof, self.data.sex)
	elseif self.data.kind == RankKind.Person then
		local role_info = {
			role_id = self.data.user_id,
			role_name = self.data.user_name,
			prof = self.data.prof,
			sex = self.data.sex,
			is_online = true,
		}
		self.role_avatar:SetRoleInfo(role_info)
	elseif self.data.kind == RankKind.Cross then
		local role_info = {
			role_id = self.data.user_id,
			role_name = self.data.user_name,
			prof = self.data.prof,
			sex = self.data.sex,
			is_online = true,
			plat_type = self.data.plat_type,
			server_id = self.data.server_id,
		}
		self.role_avatar:SetRoleInfo(role_info)
	end

	self.role_avatar:OpenMenu()
end


function RankNewItem:OnSelectChange(is_select)
	if not self.data then return end

	self.node_list.img_bg:SetActive(true)
	local rank_index = self.data.rank_index
	local is_top_three = rank_index <= 3
	self.node_list.img_rank:SetActive(is_top_three)
	self.node_list.img_rank_bg:SetActive(is_top_three)

	if is_top_three then
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp"..rank_index))
		self.node_list.img_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_list_"..rank_index))
	else
		self.node_list.img_bg:SetActive(false)
		--self.node_list.img_bg.image:LoadSprite(ResPath.GetCommonImages("a3_ty_bg2"))
	end

	if is_select then
		self.node_list.img_select:SetActive(true)
	else
		self.node_list.img_select:SetActive(false)
	end
end

function RankNewItem:PalyItemAnimator(item_index)
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.RankCellTween

	UITween.FakeHideShow(self.node_list["tween_root"])

	ReDelayCall(self, function()
		if self.node_list and self.node_list["tween_root"] then
			UITween.RotateAlphaShow(GuideModuleName.Rank,self.node_list["tween_root"], tween_info)
		end

	end, tween_info.NextDoDelay * wait_index, "RankCell_" .. wait_index)
end
