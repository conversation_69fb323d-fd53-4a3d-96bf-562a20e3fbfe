----------------------------------------------------------
-- 背包格子 or 仓库格子 扩展
----------------------------------------------------------
AlertIcon = AlertIcon or BaseClass(SafeBaseView)

function AlertIcon:__init()
	self.view_name = "AlertIcon"
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(600, 420)})
	self:AddViewResource(0, "uis/view/dialog_ui_prefab", "layout_alerticon_dialog")
end

function AlertIcon:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
end

function AlertIcon:CloseCallBack()
	self.open_num = 0
end

function AlertIcon:LoadCallBack()
    self:InitParam()
    self:InitPanel()
    --self:SetSecondView(nil, self.node_list.bg_root)
end

function AlertIcon:InitParam()
	self.select_index = 0
	self.toggle_index = 0
	self.open_num = 0
	self.use_gold = 0
	self.item_id = 0
	self.item_name = ""
	-- self.cell_item_id = {COMMON_CONSTS.OpenBagItemId, COMMON_CONSTS.OpenStorgeItemId}
	self.cell_item_id = {COMMON_CONSTS.OpenBagItemId, COMMON_CONSTS.OpenBagItemId}
end

function AlertIcon:InitPanel()
	self.item_cell = ItemCell.New(self.node_list.cell_pos)
	self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind(self.OnClickOK, self))
	self.node_list["toggle_1"].toggle:AddValueChangedListener(BindTool.Bind(self.ClickHookHandler, self, 1))
	self.node_list["toggle_2"].toggle:AddValueChangedListener(BindTool.Bind(self.ClickHookHandler, self, 2))
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function AlertIcon:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "tip_info" then
			self.open_num = v.open_num
			self.select_index = v.select_index
		end
	end
	self:FlushToggle()
end

function AlertIcon:FlushToggle()
	local index = self.select_index or 1
	index = math.max(index, 1)
	self.node_list.toggle_1.toggle.isOn = index == 1
	self.node_list.toggle_2.toggle.isOn = index == 2
	self:ClickHookHandler(index)
end

function AlertIcon:ClickHookHandler(index)
	self.toggle_index = index
	self:FlushView()
end

function AlertIcon:FlushView()
	local item_id = self.cell_item_id[self.toggle_index] or 0
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if not item_cfg then
		return
	end
	local open_num = self.open_num or 0
	local has_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	open_num = open_num > 0 and open_num or math.max(has_num, 1)

	self.item_id = item_id
	self.item_name = item_cfg.name
	self.open_num = open_num
	self.node_list.rich_text_num.text.text = open_num
	self.node_list.title_view_name.text.text = Language.Bag.ExtendBagTitle[self.toggle_index] or ""

	self:FlushXianYuBuqiDesc(open_num, has_num)
	self:FlushBoxCell(open_num, has_num)
end

function AlertIcon:FlushXianYuBuqiDesc(open_num, has_num)
	-- local common_panel = self.node_list.layout_commmon_second_root
	local is_can_unlock = has_num >= open_num
	-- local size_y = is_can_unlock and 420 or 480
	-- local pos_y = is_can_unlock and 24 or 0
	-- RectTransform.SetSizeDeltaXY(common_panel.rect, common_panel.rect.sizeDelta.x, size_y)
	-- RectTransform.SetAnchoredPositionXY(common_panel.rect, 0, pos_y)

	if is_can_unlock then
		self.use_gold = 0
		self.node_list.xianyu_buqi:SetActive(false)
		return
	end
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	local can_use_gold = role_vo and role_vo.gold or 0
	local can_use_bingold = role_vo and role_vo.bind_gold or 0

	local price = ShopWGData.Instance:GetShopCfgItemIdPrice(self.item_id, Shop_Money_Type.Type1)
	local use_gold = price * (open_num - has_num)
	local color = (can_use_bingold >= use_gold or can_use_gold >= use_gold) and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
	local desc = string.format(Language.Bag.ExtendBagTip, color, use_gold)

	self.use_gold = use_gold
	self.node_list.rich_text_tip.text.text = desc
	self.node_list.rich_text_tip2.text.text = string.format(Language.Bag.ExtendBagTip2, self.item_name)
	self.node_list.xianyu_buqi:SetActive(true)
end

function AlertIcon:FlushBoxCell(open_num, has_num)
	local num_str = string.format("%d/%d", has_num, open_num)
	local color = open_num <= has_num and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
	self.item_cell:SetData({item_id = self.item_id})
	self.item_cell:SetRightBottomColorText(num_str, color)
	self.item_cell:SetRightBottomTextVisible(true)
end

function AlertIcon:OnClickOK()
	local unlock_type = self.toggle_index - 1
	if self.use_gold > 0 then
		local desc = string.format(Language.Bag.OpenBoxDec, self.item_name, self.use_gold)
		TipWGCtrl.Instance:OpenAlertTipsByRoleBag(desc, function ()
				BagWGCtrl.Instance:SendKnapsackStorageExtendGridNum(unlock_type, self.open_num, 1)
				self:Close()
			end)
	else
		BagWGCtrl.Instance:SendKnapsackStorageExtendGridNum(unlock_type, self.open_num, 1)
		self:Close()
	end
end

function AlertIcon:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
	if item_id == self.item_id then
		self:FlushView()
	end
end