-- require("game/crossserver/kuafu_onevone/kf_onevone_rank_gongxun")
require("game/crossserver/kuafu_onevone/kf_onevone_rank_match_aim")
require("game/crossserver/kuafu_onevone/kf_onevone_rank_ranking")
require("game/field1v1/act_jjc_bar")

local TEST_LOADTIME = 5

Field1v1View = Field1v1View or BaseClass(SafeBaseView)

KUAFU_TAB_TYPE = {
	ENTER = TabIndex.arena_enter, -- 战场
	--ARENA = TabIndex.arena_field1v1, -- 竞技场
	TIANTI = TabIndex.arena_tianti, -- 天梯争霸
	ONEVSONE = TabIndex.arena_kf1v1, -- 跨服1v1
	-- ONEREWARD = TabIndex.arena_1v1jfreward,			-- 1v1积分奖励
	-- ONEDUAN = TabIndex.arena_1v1paragraph,			-- 1v1段位
	-- ONERANK = TabIndex.arena_1v1rank,  				-- 1v1排行
	PVP = TabIndex.arena_kf3v3, -- 跨服3V3
	-- PVPREWARED = TabIndex.arena_3v3jfreward, 		-- 3V3积分奖励
	-- PVPDUAN = TabIndex.arena_3v3paragraph,  			-- 3V3段位
	-- PVPRANK =  TabIndex.arena_3v3rank,  				-- 3V3排位
	TIANSHEN_3V3 = TabIndex.tianshen_3v3, -- 天神3v3
}

TabbarTipType = {
	--[TabIndex.arena_field1v1] = 'arena_field1v1',
	[TabIndex.arena_tianti] = 'arena_tianti',
	[TabIndex.arena_kf1v1] = 'arena_kf1v1',
	[TabIndex.tianshen_3v3] = 'tianshen_3v3',
	[TabIndex.arena_kf3v3] = 'arena_kf3v3',
	[TabIndex.kf_guild_battle] = 'kf_guild_battle',
	[TabIndex.worlds_no1] = 'worlds_no1_view',
}

function Field1v1View:__init()
	self:SetMaskBg()
	self.view_style = ViewStyle.Full
	self.view_name = GuideModuleName.ActJjc -- 是否向右对齐
	self.default_index = KUAFU_TAB_TYPE.ENTER
	self.is_safe_area_adapter = true

	

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	--self:AddViewResource(KUAFU_TAB_TYPE.ARENA, "uis/view/field1v1_ui_prefab", "layout_field1v1_tz")
	self:AddViewResource(KUAFU_TAB_TYPE.ONEVSONE, "uis/view/field1v1_ui_prefab", "layout_kf_onevone")
	self:AddViewResource(KUAFU_TAB_TYPE.ENTER, "uis/view/field1v1_ui_prefab", "layout_arena_enter")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")

	self.cur_jifen_seq = -1
	self.role_list = {}
	self.mate_list = {}
	self.Rewarditem_cells = {}
	-- self:SetIsDelayOnFlush(true)
	self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
	self.open_tween = nil
	-- self.close_tween = nil
	self.need_visble_callback = true
end

function Field1v1View:__delete()
	RemindManager.Instance:UnBind(self.remind_change)
end

function Field1v1View:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if nil ~= self.matching_effects then
		self.matching_effects:stopAllActions()
		self.matching_effects = nil
	end
	self.is_from_normal_open = nil

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:DeleteTiaoZhan()
	self:Deletekfonevone()

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ActJjc, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end
	RemindManager.Instance:UnBind(self.remind_change)
end

function Field1v1View:LoadCallBack()
	--功能引导注册
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ActJjc, self.get_guide_ui_event)
	self:CreatMoneyBar()
end

function Field1v1View:CreatMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function Field1v1View:LoadIndexCallBack(index)
	if index == KUAFU_TAB_TYPE.ARENA then
		self:RegisterDefaultClose()
		self:Initfieldtiaozhan()
	elseif index == KUAFU_TAB_TYPE.ONEVSONE then
		self:IntkuafuOnevsOne()
		--绑定红点提醒
	elseif index == KUAFU_TAB_TYPE.DZ then
		self:InitTeamEquip()
	elseif index == KUAFU_TAB_TYPE.DZH then
		self:InitHighTeamEquip()
		--elseif index == KUAFU_TAB_TYPE.PVP then
		--	self:InitTeamEquipView()
	elseif index == KUAFU_TAB_TYPE.ENTER then
		self:InitTabbar(index)
	end
end

function Field1v1View:CreateRooNdeCallBack()
	self:SetRootUnVisible()
	GlobalTimerQuest:AddDelayTimer(function()
		self:ResetRootNode()
	end, 1)
end

function Field1v1View:InitTabbar(index)
	if index == KUAFU_TAB_TYPE.ENTER and not self.tabbar then
		self.is_from_normal_open = true

		local remind_tab = {
			--{ RemindName.ActJjcArena },
			{ RemindName.ActTianTi },
			{ RemindName.ActOneVSOnebArena, nil },
			{ RemindName.WorldsNO1Remind },
			{ RemindName.TianShen3v3Remind },
			{ RemindName.ActPVPbArena },
		}

		self.tabbar = ActJjcBar.New(self.node_list)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.FieldTabbarCallBack, self))
		self.tabbar:Init(Language.Field1v1.TabGrop, nil, true, nil, remind_tab)
		-- FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.ActJjc, self.tabbar)
	end
end

function Field1v1View:SetTabVisible()
	if nil == self.tabbar then
		return
	end

	local show_1v1 = true
	self.tabbar:SetVerToggleVisble(TabIndex.arena_kf1v1, show_1v1)
	self.tabbar:SetVerToggleVisble(TabIndex.worlds_no1, not show_1v1)
	self.tabbar:SetVerToggleVisble(TabIndex.tianshen_3v3, false)
	self.tabbar:SetVerToggleVisble(TabIndex.kf_guild_battle, false) --屏蔽掉
end

function Field1v1View:FieldTabbarCallBack(index)
	if not index then
		return
	end

	if index / 10 - 1 > #Language.Field1v1.TabGrop then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen_2)
		return
	end

	if index == TabIndex.arena_kf3v3 then
		-- 暂时屏蔽跨服3v3
		-- TipWGCtrl.Instance:ShowSystemMsg(Language.Field1v1.JingQingQiDai)

		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local limit_open_day = KF3V3WGData.Instance:GetOpenDay()
		if open_day < limit_open_day then
			local str = string.format(Language.Common.SystemOpenDay, limit_open_day)
			SysMsgWGCtrl.Instance:ErrorRemind(str)
			return
		end
	end

	local bool, str = FunOpen.Instance:GetFunIsOpened(TabbarTipType[index], true)
	if not bool then
		SysMsgWGCtrl.Instance:ErrorRemind(str)
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Kf_OneVOne_Prepare then
		if index ~= TabIndex.arena_kf1v1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
			return
		end
	elseif scene_type == SceneType.Kf_PvP_Prepare then
		if index ~= TabIndex.arena_kf3v3 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.OnCrossServerTip)
			return
		end
	end

	if index == TabIndex.arena_kf1v1 then
		-- local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_ONEVONE)
		-- if is_open and scene_type ~= SceneType.Kf_OneVOne_Prepare then
		-- 	Field1v1WGCtrl.Instance:EnterFieldPrepareScene(ACTIVITY_TYPE.KF_ONEVONE)
		-- return
		-- end
	elseif index == TabIndex.arena_kf3v3 then
		local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_PVP)
		if is_open and scene_type ~= SceneType.Kf_PvP_Prepare then
			-- 活动要队伍里是同战队的才能进入，否则就需要退队(进去后还可以组队)
			local change_index , member_list = NewTeamWGData.Instance:GetTeamMemberList()
			local not_my_zhandui_id_list = {}
			if #member_list > 1 then
				for k,v in ipairs(change_index) do
					if v and member_list[v] then
						local is_my_zhandui = ZhanDuiWGData.Instance:GetTargetIsMyZhanDui(member_list[v].role_id)
						if not is_my_zhandui then
							table.insert(not_my_zhandui_id_list, member_list[v].role_id)
						end
					end
				end
			end
			if #not_my_zhandui_id_list > 0 then
				if SocietyWGData.Instance:GetIsTeamLeader() == 1 then
					TipWGCtrl.Instance:OpenAlertTips(Language.KuafuPVP.LeaderEnterPrepareSceneTip, function ()
						for i, v in ipairs(not_my_zhandui_id_list) do
							SocietyWGCtrl.Instance:SendKickOutOfTeam(v)
						end
						KF3V3WGCtrl.Instance:EnterPrepareScene()
					end)
				else
					TipWGCtrl.Instance:OpenAlertTips(Language.KuafuPVP.MemberEnterPrepareSceneTip, function ()
						NewTeamWGCtrl.Instance:ExitTeam()
						KF3V3WGCtrl.Instance:EnterPrepareScene()
					end)
				end
				return
			end
			KF3V3WGCtrl.Instance:EnterPrepareScene()
			return
		end
	elseif index == KUAFU_TAB_TYPE.TIANSHEN_3V3 then
		ViewManager.Instance:Open(GuideModuleName.TianShen3v3View)
		return
	elseif index == TabIndex.worlds_no1 then
		ViewManager.Instance:Open(GuideModuleName.WorldsNO1View)
		return
	elseif index == TabIndex.kf_guild_battle then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Field1v1.JingQingQiDai)
		return
	end

	self:ChangeToIndex(index)
end

function Field1v1View:CloseCallBack(is_all)
	-- if not is_all then
	-- 	FunctionGuide.Instance:StopGuideByModuleName(GuideModuleName.ActJjc)--强制停止引导，不强制加入(如仙盟)
	-- end
	-- KuafuOnevoneWGCtrl.Instance:SendCross1v1MatchQuery(CROSS_1V1_MATCH_REQ_TYPE.CROSS_1V1_MATCH_REQ_CANCEL)
	-- if self.tabbar then
	-- 	self.tabbar:ResetAnimPos()
	-- end
end

function Field1v1View:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.Tab and self.tabbar then
		local tab_index = math.floor(TabIndex[ui_param])
		if tab_index == self:GetShowIndex() then
			return NextGuideStepFlag
		else
			local ui = self.tabbar:GetToggleByIndex(tab_index)
			if ui ~= nil then
				local effect_pos = ui.transform:Find("guide_pos")
				return effect_pos, BindTool.Bind(self.ChangeToIndex, self, tab_index)
			end
		end
	elseif ui_name == GuideUIName.Field1v1SelectBtn then
		-- 	Field1v1WGData.Instance:SetGuideFlag(true)
		return self.node_list["enter_arena_btn_2"], BindTool.Bind(self.OnClickTiaoZhan, self, 2)
	elseif ui_name == GuideUIName.Field1v1RolePanel then
		return self.node_list["role_panel"]
	elseif ui_name == GuideUIName.KF3V3GotoBtn then
		return self.node_list["btn_join_zhandui"], BindTool.Bind(self.OnClickJoinZhanDui3V3, self)
	elseif ui_name == GuideUIName.BtnActJjcView then
		local special_fun = function()
			Field1v1WGCtrl.Instance:PickEnemyFieldFight()
			Field1v1WGData.Instance:SetNeedLeave1V1Scene(true)
		end
		return self.node_list["BtnActJjcView"], special_fun
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

function Field1v1View:OnClosePanelAndHall()
	self:OnCloseHandler()
	-- ActHallWGCtrl.Instance:Close()
end

function Field1v1View:OpenCallBack()
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	Field1v1WGCtrl.Instance:ResetOpponentList()
	Field1v1WGCtrl.Instance:ReqOtherRoleInfo(0)
	RankWGCtrl.Instance:SendGetCross1V1RankList()
	RoleWGCtrl.Instance:ReqCommonOpreate(COMMON_OPERATE_TYPE.COT_CROSS_1V1_SELF_INFO)
	GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST,
		RoleWGData.Instance.role_vo.guild_id)                                                                                     --请求成员列表信息
	KuafuOnevoneWGCtrl.Instance:SendCross1v1MatchQuery(CROSS_1V1_MATCH_REQ_TYPE.CROSS_1V1_MATCH_REQ_RESULT)
	KuafuPVPWGCtrl.Instance:SendCrossPVPInfo(CROSS_3V3OPERA_TYPE.CROSS_3V3OPERA_TYPE_INFO)
end

function Field1v1View:ShowIndexCallBack(index)
	self:SetTabVisible()

	if index == KUAFU_TAB_TYPE.ONEVSONE then
		-- RankWGCtrl.Instance:SendGetCross1V1RankList()
		self:ShowOneVOneCallBack()
	elseif index == KUAFU_TAB_TYPE.PVP then
		ZhanDuiWGCtrl.Instance:ReqZhanDuiInfo()
		RankWGCtrl.Instance:SendCrossGetRankListReq(CROSS_PERSON_RANK_TYPE.CROSS_PERSON_RANK_TYPE_3V3_SCORE)
		self:ChangeSpecialRawImage()
		KF3V3WGCtrl.Instance:Open3V3View(TabIndex.kf_pvp_3v3info)
	elseif index == KUAFU_TAB_TYPE.ARENA then
		self:ShowIndexTiaoZhan()
	elseif index == KUAFU_TAB_TYPE.ENTER then
		self:ShowIndexEnter()
		if self.tabbar:GetCellLength() == 0 then
			self:InitTabbar()
		end
		self.is_from_normal_open = true
		self.tabbar:PlayAnim(TEST_LOADTIME, function()
			-- self:ResetRootNode()
		end)
		-- self.tabbar:SetMovVisble()
		TEST_LOADTIME = TEST_LOADTIME + 1
	elseif index == KUAFU_TAB_TYPE.TIANTI then
		local key = string.format("OpenViewOnTimes_%s_%s", GuideModuleName.ArenaTianTi, RoleWGData.Instance:GetOriginUid())
		local open_time = PlayerPrefsUtil.GetInt(key, 0)
		if open_time == 0 then
			open_time = open_time + 1
			PlayerPrefsUtil.SetInt(key, open_time)
			FunOpen.Instance:OpenViewByName(GuideModuleName.ArenaTianTi)
			Field1v1WGCtrl.Instance:PickEnemyFieldFight()
			Field1v1WGData.Instance:SetNeedLeave1V1Scene(true)
			self:Close()
		else
			FunOpen.Instance:OpenViewByName(GuideModuleName.ArenaTianTi)
			self:Close()
		end
		return
	end

	KuafuPVPWGCtrl.Instance:SendCrossMultiuserChallengeGetBaseSelfSideInfo()
end

function Field1v1View:ShowIndexEnter()
	self.node_list.title_view_name.text.text = Language.Field1v1.Enter
	if self.node_list.RawImage_tongyong then
		local bundle, asset = ResPath.GetRawImagesPNG("a3_jjc_bg_xz")
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function Field1v1View:ChangeSpecialRawImage()
	if self.node_list.RawImage_tongyong then
		local bundle, asset = ResPath.GetRawImagesPNG("a3_fb_bj_4")
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end
end

function Field1v1View:OnFlush(param_t, index)
	if index ~= 0 then
		self.default_index = index
	else
		index = self.default_index
	end

	for k, v in pairs(param_t) do
		if "all" == k then
			if index == KUAFU_TAB_TYPE.ARENA then
				self:OnFlushTiaoZhan()
			elseif index == KUAFU_TAB_TYPE.ONEVSONE then
				self:OnFlushKuafuOneVOne()
			elseif index == KUAFU_TAB_TYPE.PVP then
				--self:OnFlushPVPTeamEquipView(param_t)
			end
		elseif "flush_onevone_model" and index == KUAFU_TAB_TYPE.ONEVSONE then
			self:FlushOneVOneModel()
		end
	end
end

function Field1v1View:OnClickCloseWindow()
	SafeBaseView.OnClickCloseWindow(self)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Field1v1 then
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

function Field1v1View:Close()
	local scene_type = Scene.Instance:GetSceneType()

	local show_index = self:GetShowIndex()
	if not self.is_from_normal_open or scene_type == SceneType.Field1v1 then
		SafeBaseView.Close(self)
		return
	end

	if not IS_ON_CROSSSERVER and show_index ~= TabIndex.arena_enter and show_index ~= TabIndex.arena_kf3v3 then
		--self:InitTabbar(TabIndex.arena_enter)
		if show_index == TabIndex.arena_kf1v1 then
			local complete_fun = function()
				self:FieldTabbarCallBack(TabIndex.arena_enter)
			end
			if self.node_list["layout_kf_1v1"] then
				self:ChildViewHideFadeUp(self.node_list["layout_kf_1v1"], complete_fun)
			end
		else
			self:FieldTabbarCallBack(TabIndex.arena_enter)
		end
		--self.cur_click_index = nil
	else
		SafeBaseView.Close(self)
		self.is_from_normal_open = nil
	end
end

function Field1v1View:ChildViewHideFadeUp(obj, complete_fun)
	local MOVE_TIME = 0.3
	local MOVE_DISTANCE = 25
	local canvas_group = obj:GetComponent(typeof(UnityEngine.CanvasGroup))
	canvas_group.alpha = 1
	obj.transform.anchoredPosition = u3dpool.vec3(0, 0, 0)

	local tween = obj.transform:DOAnchorPosY(MOVE_DISTANCE, MOVE_TIME)
	tween:SetEase(DG.Tweening.Ease.Linear)

	local on_tween_updata = function()
		canvas_group.alpha = canvas_group.alpha - UnityEngine.Time.deltaTime / MOVE_TIME
	end

	local on_tween_complete = function()
		canvas_group.alpha = 1
		obj.transform.anchoredPosition = u3dpool.vec3(0, 0, 0)
		if complete_fun then
			complete_fun()
		end
	end

	tween:OnUpdate(on_tween_updata)
	tween:OnComplete(on_tween_complete)

	return tween, on_tween_updata, on_tween_complete
end

function Field1v1View:ItemDataChangeCallback()
	self:Flush()
end
