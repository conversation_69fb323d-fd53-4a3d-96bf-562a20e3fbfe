FuBenRuneTowerDrawGetRewardView = FuBenRuneTowerDrawGetRewardView or BaseClass(SafeBaseView)

function FuBenRuneTowerDrawGetRewardView:__init(view_name)
	self.view_name = "FuBenRuneTowerDrawGetRewardView"
	self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Half
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fuben_rune_tower_draw_get_reward")
    
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
end

function FuBenRuneTowerDrawGetRewardView:SetDataAndOpen(show_reward_list)
	self.show_reward_list = show_reward_list
    if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
    end
end

function FuBenRuneTowerDrawGetRewardView:LoadCallBack()
	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	end

    XUI.AddClickEventListener(self.node_list.close_btn, BindTool.Bind(self.Close, self))
end

function FuBenRuneTowerDrawGetRewardView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

    if CountDownManager.Instance:HasCountDown("rune_tower_draw_reward_end_time") then
        CountDownManager.Instance:RemoveCountDown("rune_tower_draw_reward_end_time")
    end
end

function FuBenRuneTowerDrawGetRewardView:OnFlush(param_t)
    if not self.show_reward_list then
        return
    end

    self.reward_list:SetDataList(self.show_reward_list)
    self:FlushEndTime()
end

function FuBenRuneTowerDrawGetRewardView:FlushEndTime()
    local time = 3
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("rune_tower_draw_reward_end_time") then
            CountDownManager.Instance:RemoveCountDown("rune_tower_draw_reward_end_time")
        end

		CountDownManager.Instance:AddCountDown("rune_tower_draw_reward_end_time", 
                        BindTool.Bind1(self.UpdateCountDownTime, self), 
                        BindTool.Bind1(self.CompleteCountDownTime, self), 
                        nil, time, 1)
    else
        self:CompleteCountDownTime()
    end
end

function FuBenRuneTowerDrawGetRewardView:UpdateCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
        self.node_list.end_time.text.text = string.format(Language.RuneTower.DrawEndTime, math.floor(total_time - elapse_time))
	end
end

function FuBenRuneTowerDrawGetRewardView:CompleteCountDownTime()
	self:Close()
end