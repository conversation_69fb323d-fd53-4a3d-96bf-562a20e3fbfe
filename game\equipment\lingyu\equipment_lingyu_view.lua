--装备灵玉

function EquipmentView:InitLingYuView()
	if not self.equip_lingyu_list then
		self.equip_lingyu_list = AsyncListView.New(EquipLingYuItemRender, self.node_list["equip_lingyu_list"])
		self.equip_lingyu_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectEquipLingYuItemHandler, self))
	end

	if not self.lingyu_slot_list then
		self.lingyu_slot_list = {}
		for i = 1, GameEnum.MAX_LINGYU_COUNT do
			local group_obj = self.node_list["lingyu_part"]:FindObj("lingyu_" .. i)
		    local obj = ResMgr:Instantiate(self.node_list["lingyu_item_prafab"].gameObject)
		    local obj_transform = obj.transform
			obj_transform:SetParent(group_obj.transform, false)
			self.lingyu_slot_list[i] = LingYuXQItem.New(obj)
			self.lingyu_slot_list[i]:SetIndex(i - 1)
		end
	end

	if not self.lingyu_eq_item then
		self.lingyu_eq_item = ItemCell.New(self.node_list["lingyu_eq_item"])
		self.lingyu_eq_item:SetItemTipFrom(ItemTip.FROM_EQUIPMENT)
	end

	if not self.bsly_equip_body_list then
		self.bsly_equip_body_list = AsyncListView.New(BSLYEquipBodyListCellRender, self.node_list.bsly_equip_body_list)
		self.bsly_equip_body_list:SetStartZeroIndex(false)
		self.bsly_equip_body_list:SetSelectCallBack(BindTool.Bind(self.OnSelectXJXQEquipBodyHandler, self))
		self.bsly_equip_body_list:SetEndScrolledCallBack(BindTool.Bind(self.BSLYEquipBodyListSetEndScrollCallBack, self))
	end

	self.lingyu_select_equip_data = nil
	self.ly_old_progress_value = nil
	self.lingyu_select_equip_part = -1
	self.lingyu_select_equip_index = -1

	self.lingyu_jump_equip_body_seq = -1
	self.lingyu_jump_equip_body_equip_data = {}
	self.bsly_need_equip_body_tween = true

	XUI.AddClickEventListener(self.node_list["btn_lingyu_add"], BindTool.Bind1(self.OnBtnLingYuAddHandler, self))
	XUI.AddClickEventListener(self.node_list["btn_ly_onekey_inlay"], BindTool.Bind1(self.OnBtnLingYuOneKeyInlay, self))
	XUI.AddClickEventListener(self.node_list["btn_bsly_equip_body"], BindTool.Bind1(self.OnBaoShiLingYuEquipBodyBtnClick, self))
end

function EquipmentView:LingYuDeleteMe()
	if self.equip_lingyu_list then
		self.equip_lingyu_list:DeleteMe()
		self.equip_lingyu_list = nil
	end

	if self.lingyu_slot_list then
		for k,v in pairs(self.lingyu_slot_list) do
			v:DeleteMe()
		end
		self.lingyu_slot_list = nil
	end

	if self.lingyu_eq_item then
		self.lingyu_eq_item:DeleteMe()
		self.lingyu_eq_item = nil
	end

	if self.bsly_equip_body_list then
		self.bsly_equip_body_list:DeleteMe()
		self.bsly_equip_body_list = nil
	end

	self.lingyu_select_equip_data = nil
	self.lingyu_select_equip_part = nil
	self.ly_old_progress_value = nil

	self.lingyu_jump_equip_body_seq = nil
	self.lingyu_jump_equip_body_equip_data = nil
	self.bsly_need_equip_body_tween = nil

	EquipmentWGCtrl.Instance:DeleteLingShiUpGradeAlertTips()
end

function EquipmentView:ShowEquipLingYuView()
	self.bsly_select_equip_body_seq = nil
end

function EquipmentView:FlushEquipLingYuView()
	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZXJXQEquipBodyDataList()
	self.bsly_equip_body_list:SetDataList(total_equip_body_data_list)
	self.bsly_equip_body_list:JumpToIndex(self:GetXJXQSelectEquipBodySeq(total_equip_body_data_list))
end

function EquipmentView:LingYuChangeToTargetEquipBody(data)
	if IsEmptyTable(data) then
		return
	end

	local total_equip_body_data_list = EquipBodyWGData.Instance:GetDZXJXQEquipBodyDataList()
	if IsEmptyTable(total_equip_body_data_list) then
		return
	end

	self.lingyu_jump_equip_body_seq = data.equip_body_seq
	self.lingyu_jump_equip_body_equip_data = data.selct_part_data

	for k, v in pairs(total_equip_body_data_list) do
		if v.seq == self.lingyu_jump_equip_body_seq then
			if self.bsly_equip_body_list then
				self.bsly_equip_body_list:JumpToIndex(k)
				self.lingyu_jump_equip_body_seq = -1
			end

			break
		end
	end
end

function EquipmentView:GetXJXQSelectEquipBodySeq(total_equip_body_data_list)
	if self.bsly_select_equip_body_seq then
		return self.bsly_select_equip_body_index
	end

	local default_seq = -1
	local default_index = -1
	if not IsEmptyTable(total_equip_body_data_list) then
		for i = #total_equip_body_data_list, 1, -1 do
			local data = total_equip_body_data_list[i]

			if EquipmentLingYuWGData.Instance:GetEquipBodyLingYuRemind(data.seq) > 0 then
				return i
			end

			if default_seq < 0 or data.seq > default_seq  then
				local can_duanzao, is_unlock, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(data.seq)
				
				if is_unlock and is_wear_equip then
					default_seq = data.seq
					default_index = i
				end
			end
		end
	end

	return default_index
end

-- function EquipmentView:GetXJXQSelectEquipBodySeq(total_equip_body_data_list)
-- 	if self.bsly_select_equip_body_seq then
-- 		if EquipmentLingYuWGData.Instance:GetEquipBodyLingYuRemind(self.bsly_select_equip_body_seq) > 0 then
-- 			return self.bsly_select_equip_body_index
-- 		end
-- 	end

-- 	if not IsEmptyTable(total_equip_body_data_list) then
-- 		for k, v in pairs(total_equip_body_data_list) do
-- 			if EquipmentLingYuWGData.Instance:GetEquipBodyLingYuRemind(v.seq) > 0 then
-- 				return k
-- 			end
-- 		end
-- 	end

-- 	return self.bsly_select_equip_body_index or 1
-- end

function EquipmentView:OnSelectXJXQEquipBodyHandler(item, cell_index, is_default, is_click)
	if nil == item or IsEmptyTable(item.data) then
		return
	end

	local data = item.data
	local bsly_seq_change = self.bsly_select_equip_body_seq ~= data.seq
	self.bsly_select_equip_body_seq = data.seq
	self.bsly_select_equip_body_index = item.index


	self:FlushEquipLingYuInfo()

	--self:FlushEquipLingYuSlotList()

	local lingyu_equip_list = EquipmentLingYuWGData.Instance:GetEquipLingYuShowList(self.bsly_select_equip_body_seq)
	self.equip_lingyu_list:SetDataList(lingyu_equip_list)

	if not IsEmptyTable(self.lingyu_jump_equip_body_equip_data) then
		local select_index = -1
		for k, v in pairs(lingyu_equip_list) do
			if v.index == self.lingyu_jump_equip_body_equip_data.index then
				select_index = k
				break
			end
		end

		if select_index >= 0 then
			self.equip_lingyu_list:JumpToIndex(select_index)
			return
		end
	end

	local equip_index = self:LingYuCalculateDefaultIndex(lingyu_equip_list, bsly_seq_change)
	self.equip_lingyu_list:JumpToIndex(equip_index)
end

-- 得到第一个有红点的装备在listview里的下标
function EquipmentView:LingYuCalculateDefaultIndex(equip_list, bsly_seq_change)
	if not bsly_seq_change and self.lingyu_select_equip_index > 0 then
		local equip_remind = EquipmentLingYuWGData.Instance:GetEquipLingYuRemind(self.lingyu_select_equip_part)

		if equip_remind then
			return self.lingyu_select_equip_index
		end
	end

	local default_index = -1
	for k, v in pairs(equip_list) do
		if default_index < 0 then
			default_index = k
		end

		local equip_remind = EquipmentLingYuWGData.Instance:GetEquipLingYuRemind(v.index)

		if equip_remind then
			return k
		end
	end

	if self.lingyu_select_equip_index >= 0 and not IsEmptyTable(equip_list[self.lingyu_select_equip_index]) then
		return self.lingyu_select_equip_index
	else
		return default_index
	end
end

function EquipmentView:FlushEquipLingYuInfo()
	local next_total_cfg
	local next_level, active_level
	local cur_progress
	local tem_ratio = "<color=%s>(%s/%s)</color>"

	local baoshi_total_level = EquipmentLingYuWGData.Instance:GetTotalLingYuLevel(self.bsly_select_equip_body_seq)
	active_level = EquipmentLingYuWGData.Instance:GetLingYuActiveLevel(self.bsly_select_equip_body_seq)
	next_total_cfg = EquipmentLingYuWGData.Instance:GetLingYuTotalStoneCfg(self.bsly_select_equip_body_seq, active_level, true)
	if(next_total_cfg) then
		local is_enough = (next_total_cfg.lingyu_level - baoshi_total_level) <= 0
		local color = is_enough and "#99ffbb" or "#ff9292"
		cur_progress = baoshi_total_level / next_total_cfg.lingyu_level
		if(cur_progress < 1) then
			next_level = string.format(tem_ratio, color, baoshi_total_level, next_total_cfg.lingyu_level)
		else
			next_level = Language.Equip.JiaChengCanAct
			cur_progress = 1
		end
	else
		next_level = Language.Equip.JiaChengIsMax
		cur_progress = 1
	end

	if self.ly_old_progress_value == nil then
		self.node_list.ly_slider.slider.value = cur_progress
	else
		self.node_list.ly_slider.slider:DOValue(cur_progress, 0.5)
	end

	self.ly_old_progress_value = cur_progress
	self.node_list.ly_next_level.text.text = next_level

	local active_remind = EquipmentLingYuWGData.Instance:GetEquipLingYuActiveRemind(self.bsly_select_equip_body_seq)
    self.node_list["btn_lingyu_add_remind"]:SetActive(active_remind > 0)
	self.node_list["btn_lingyu_add_effect"]:SetActive(active_remind > 0)
end

-- 选择装备列表项回调
function EquipmentView:OnSelectEquipLingYuItemHandler(item)
	if nil == item or nil == item.data then
		return
	end

	if item.data.index ~= self.lingyu_select_equip_part then
		self.node_list["effect_root_lingyu"]:SetActive(false)
		self.ly_old_progress_value = nil

		if self.lingyu_slot_list then
			for k, v in pairs(self.lingyu_slot_list) do
				v:StopInalyTween()
			end
		end
	end

	self.lingyu_select_equip_data = item.data
	self.lingyu_select_equip_part = item.data.index
	self.lingyu_select_equip_index = item.index

	if self.lingyu_select_equip_data then
		self.lingyu_eq_item:SetData(self.lingyu_select_equip_data)
	end

	self:FlushEquipLingYuSlotList()
end

function EquipmentView:FlushEquipLingYuSlotList()
	local lingyu_info = EquipmentLingYuWGData.Instance:GetLingYuInfoListByIndex(self.lingyu_select_equip_part)
	for k, v in pairs(self.lingyu_slot_list) do
		v:SetCurEquipPart(self.lingyu_select_equip_part)
		v:SetData(lingyu_info[k - 1])
	end

	local is_have_batter = false
	for slot = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
		is_have_batter = EquipmentLingYuWGData.Instance:GetLingYuSlotHaveBatterState(self.lingyu_select_equip_part, slot)
		if is_have_batter then
			break
		end
	end

	local equip_is_all_inlay = EquipmentLingYuWGData.Instance:GetEquipPartIsAllInlayLingYu(self.lingyu_select_equip_part)
	local string_index = equip_is_all_inlay and 2 or 1
	self.node_list.btn_ly_onekey_inlay_text.text.text = Language.Equipment.BaoShiAndLingYuBtnStr[string_index]
	local has_can_up = EquipmentLingYuWGData.Instance:GetEquipPartCanUpLingYu(self.lingyu_select_equip_part)
	self.node_list["btn_ly_onekey_inlay_remind"]:CustomSetActive(is_have_batter or has_can_up)
end

function EquipmentView:OnBtnLingYuOneKeyInlay()
	local order = EquipmentWGData.EquipBodySeqAndPartToEquipBodyIndex(self.bsly_select_equip_body_seq, self.lingyu_select_equip_part)
	local equip_is_all_inlay = EquipmentLingYuWGData.Instance:GetEquipPartIsAllInlayLingYu(self.lingyu_select_equip_part)
	local list = EquipmentLingYuWGData.Instance:GetAllLingYuOneKeyInlay(self.bsly_select_equip_body_seq, order)
	if not IsEmptyTable(list) then
		EquipmentWGCtrl.Instance:SendLingYuOneKeyInlay(list)
	else
		if not equip_is_all_inlay then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Equipment.InlayLYNoBetterTips)
		end
	end

	if equip_is_all_inlay then
		local has_can_up = EquipmentLingYuWGData.Instance:GetEquipPartCanUpLingYu(self.lingyu_select_equip_part)
		if has_can_up then
			EquipmentWGCtrl.Instance:SendLingYuOperate(LINGYU_OPERA_TYPE.LINGYU_AUTO_LEVEL_UP, self.lingyu_select_equip_part)
		else
			local min_level_index = EquipmentLingYuWGData.Instance:GetEquipPartMinLevelLingYu(self.lingyu_select_equip_part)
			if min_level_index > -1 then
				EquipmentWGCtrl.Instance:LingYuUpGradeOpen(self.lingyu_select_equip_part, min_level_index)
			end
		end
	end
end

function EquipmentView:OpenLingYuShowEquipTips()
	if self.lingyu_select_equip_data then
		TipWGCtrl.Instance:OpenItem(self.lingyu_select_equip_data, ItemTip.FROM_EQUIPMENT)
	end
end

function EquipmentView:OnBtnLingYuAddHandler()
	RoleWGCtrl.Instance:SetEquipTextData(ROLE_EQUIP_ATTR_TIPS.LINGYU_TIP, self.bsly_select_equip_body_seq)
	RoleWGCtrl.Instance:OpenEquipAttr()
end

function EquipmentView:OnBaoShiLingYuEquipBodyBtnClick()
	EquipmentWGCtrl.Instance:OpenLingYuOverviewView()
end

-- -- 中间显示模型(已屏蔽)
-- function EquipmentView:FlushLingYuEquipImg()
-- 	self.node_list["lingyu_equip_img"]:SetActive(false)

-- 	if self.lingyu_select_equip_data then
-- 		-- local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(self.lingyu_select_equip_data.item_id, true)
-- 		-- if bundle and asset then
-- 		-- 	self.node_list["lingyu_equip_img"].raw_image:LoadSprite(bundle, asset, function ()
-- 		-- 		self.node_list["lingyu_equip_img"]:SetActive(true)
-- 		-- 	end)
-- 		-- end
-- 		local item_cfg = ItemWGData.Instance:GetItemConfig(self.lingyu_select_equip_data.item_id)
-- 		local sub_type = item_cfg.sub_type or 0

-- 		self.lingyu_equip_model:ClearModel()
-- 		local is_weapon_model = false
-- 		if sub_type >= GameEnum.EQUIP_TYPE_TOUKUI and sub_type <= GameEnum.EQUIP_TYPE_XIANZHUO then 			-- 普通装备
-- 			local res_id = sub_type - 100
-- 			-- 武器的特殊处理拿一把时装武器模型显示
-- 			if res_id == 5 then
-- 				local weapon_res_index = 12
-- 				local weapon_res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(weapon_res_index)
-- 				local bundle,asset = ResPath.GetWeaponModelRes(weapon_res_id)
-- 				self.lingyu_equip_model:SetMainAsset(bundle, asset)
-- 				is_weapon_model = true
-- 			else
-- 				local bundle,asset = ResPath.GetRareEquipItemModel(res_id)
-- 				self.lingyu_equip_model:SetMainAsset(bundle, asset)
-- 			end
-- 		end
-- 		if is_weapon_model then
-- 			self.node_list.lingyu_equip_model.transform:SetLocalScale(0.7, 0.7, 0.7)
-- 		else
-- 			self.node_list.lingyu_equip_model.transform:SetLocalScale(1.5, 1.5, 1.5)
-- 		end
-- 		self:PlayLingYuEquipModelTween()
-- 	end
-- end

-- -- 上下浮动动画
-- function EquipmentView:PlayLingYuEquipModelTween()
-- 	local data = self.lingyu_select_equip_data
-- 	if nil == data then
-- 		return
-- 	end

-- 	if self.lingyu_equip_model_tween then
-- 		self.lingyu_equip_model_tween:Kill()
-- 		self.lingyu_equip_model_tween = nil
-- 	end

-- 	if not self.lingyu_equip_model_tween then
-- 		self.node_list["lingyu_equip_model"].transform.anchoredPosition = u3dpool.vec3(-2, 45, 0)
-- 		self.lingyu_equip_model_tween = self.node_list["lingyu_equip_model"].transform:DOAnchorPosY(55, 1):SetLoops(-1, DG.Tweening.LoopType.Yoyo):SetEase(DG.Tweening.Ease.Linear)
-- 	end
-- end

-- 镶嵌成功特效
function EquipmentView:ShowLingYuInlayEffect(equip_part, slot_index)
	slot_index = slot_index or 0

	if self.show_index == TabIndex.equipment_lingyu and self.lingyu_select_equip_part == equip_part then
		if self.lingyu_slot_list and self.lingyu_slot_list[slot_index + 1] then
			self.lingyu_slot_list[slot_index + 1]:DoInalyTween()
		end
	end
end

-- 升级成功特效
function EquipmentView:PlayerLingYuUpgradeEffect()
	local effect_root
	if self.node_list["effect_root_lingyu"] and self.show_index == TabIndex.equipment_lingyu then
		effect_root = self.node_list["effect_root_lingyu"]
	end

	if effect_root then
		effect_root:SetActive(true)
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji,
							is_success = true, pos = Vector2(0, 0), parent_node = effect_root})

		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect("Advanced"))
	end
end

function EquipmentView:BSLYEquipBodyListSetEndScrollCallBack()
	if self.bsly_need_equip_body_tween then
		self.bsly_need_equip_body_tween = false

		local tween_info = UITween_CONSTS.EquipBody

		local cell_list = self.bsly_equip_body_list:GetAllItems()
		for i = 1, #cell_list do
			cell_list[i]:PlayItemTween()
		end
	end
end

--=================  灵玉槽位 render ===========================================
LingYuXQItem = LingYuXQItem or BaseClass(BaseRender)
function LingYuXQItem:__init()
	self.is_init = false
	self.is_inlay = true

	if self.node_list["inlay_effect"] then
		self.node_list["inlay_effect"]:SetActive(false)
	end

	self.attr_part_list = {}
	for i = 1, GameEnum.EQUIP_LINGYU_ATTR_NUM do
		self.attr_part_list[i] = self.node_list["attr_part"]:FindObj("attr_" .. i)
	end

	XUI.AddClickEventListener(self.node_list["btn_lingyu_add"], BindTool.Bind(self.OnBtnLingYuAddHandler, self))
	XUI.AddClickEventListener(self.node_list["img_lingyu_suo"], BindTool.Bind(self.OnBtnLingYuLockHandler, self))
	self.cur_equip_part = 0
	self:CreatLingYuItem()
end

function LingYuXQItem:__delete()
	if self.lingyu_item ~= nil then
		self.lingyu_item:DeleteMe()
		self.lingyu_item = nil
	end

	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end

	self.is_jinlian_item = nil
	self.attr_part_list = nil
end

function LingYuXQItem:CreatLingYuItem()
	self.lingyu_item = ItemCell.New(self.node_list["lingyu_item"])
	self.lingyu_item:SetShowCualityBg(false)
	self.lingyu_item:SetCellBgEnabled(false)
	self.lingyu_item:NeedDefaultEff(false)
	self.lingyu_item:SetIsShowTips(false)
	self.lingyu_item:SetClickCallBack(BindTool.Bind2(self.OnBtnLingYuAddHandler, self))
	self.node_list["img_lingyu_suo"]:SetActive(false)
	self.node_list["lingyu_suo_bg"]:SetActive(false)
	self.node_list["lingyu_add_bg"]:SetActive(false)
	self:SetLingYuRemind(false)
	self:SetLingYuEffect(false)
end

--宝石口点击
function LingYuXQItem:OnBtnLingYuAddHandler()
	if not self.data then
		return
	end

	local select_list = EquipmentLingYuWGData.Instance:GetLingYuListByLingYu(self.cur_equip_part, self.index)
	if IsEmptyTable(select_list) then
		local menu_data = EquipmentLingYuWGData.Instance:GetLingYuMenu(self.cur_equip_part)

		if not IsEmptyTable(menu_data) then
			if self.data.is_inlay then
				-- 打开升级界面
				local next_lingyu = EquipmentLingYuWGData.Instance:GetLingYuLevelUpCfgByOldId(self.data.item_id)
				if next_lingyu then
					EquipmentWGCtrl.Instance:LingYuUpGradeOpen(self.cur_equip_part, self.index)
				else
					TipWGCtrl.Instance:ShowSystemMsg(Language.EquipLingYu.MaxLevelTips)
				end
			else
				-- 商店购买
				local num = ShopWGData.Instance:GetMaxBuyNum(menu_data.shop_seq)
				local item_cfg = ItemWGData.Instance:GetItemConfig(menu_data.default_lingyu)
				ShopTip.Instance:SetData(item_cfg, 1, GameEnum.SHOP, nil, menu_data.shop_seq, nil, num)
			end
		end
	else
		-- 打开宝石列表
		EquipmentWGCtrl.Instance:SelectLingYuOpen(select_list, self.cur_equip_part, self.index)
	end
end

function LingYuXQItem:OnBtnLingYuLockHandler()
	if IsEmptyTable(self.data) then
		return
	end

	local is_open = self.data.is_open == 1
	local limit_lingyu_cfg = EquipmentLingYuWGData.Instance:GetLimitLingYuOpenCfg(self.cur_equip_part, self.index)

	if limit_lingyu_cfg and not is_open then
		if limit_lingyu_cfg.vip_level_limit > 0 then
			FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge, "recharge_vip")
		end
	end
end

function LingYuXQItem:SetCurEquipPart(part)
	self.cur_equip_part = part or 0
end

function LingYuXQItem:SetLingYuRemind(enable)
	self.node_list["img_lingyu_remind"]:SetActive(enable)
	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end

	if enable then
		self.node_list["img_lingyu_remind"].gameObject.transform.localPosition = Vector3(58, -10, 0)
		self.arrow_tweener = self.node_list["img_lingyu_remind"].gameObject.transform:DOLocalMoveY(0, 0.45, true)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InCubic)
	end
end

function LingYuXQItem:SetLingYuEffect(enable)
	self.node_list["effect"]:SetActive(enable)
end

function LingYuXQItem:StopInalyTween()
	self.can_do_inlay_tween = false
	self.node_list["inlay_effect"]:SetActive(false)
end

function LingYuXQItem:DoInalyTween()
	if not self.node_list.inlay_effect then
		return
	end

	self.can_do_inlay_tween = true
	self.node_list["inlay_effect"]:SetActive(false)
	self.node_list["inlay_effect"]:SetActive(true)

	GlobalTimerQuest:AddDelayTimer(BindTool.Bind(function ()
		if self.node_list.inlay_effect and self.can_do_inlay_tween then
			self.node_list["inlay_effect"]:SetActive(false)
		end
	end, self), 2)
end

function LingYuXQItem:NoDataHideShow()
	self.node_list["img_lingyu_suo"]:SetActive(true)
	self.node_list["lingyu_suo_bg"]:SetActive(true)
	self.node_list["ph_lingyu_item_bg"]:SetActive(false)
	self.node_list["name_part"]:SetActive(false)
	self.node_list["attr_part"]:SetActive(false)
	self.node_list["limit_part"]:SetActive(false)
	self.node_list["lingyu_add_bg"]:SetActive(false)
--[[ 	local bundle, asset = ResPath.GetCommonImages("a1_item_round_bg")
	self.node_list["quality_bg"].image:LoadSprite(bundle, asset, function()
		self.node_list["quality_bg"].image:SetNativeSize()
	end) ]]
	self:SetLingYuRemind(false)
	self:SetLingYuEffect(false)
end

function LingYuXQItem:OnFlush()
	if IsEmptyTable(self.data) then
		self:NoDataHideShow()
		return
	end

	local bg_idx = 0
	local is_open = self.data.is_open == 1
	local is_inlay = self.data.is_inlay

	if self.is_init and is_inlay == true and self.is_inlay == false then
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.StrenthenFail, nil, true))
	end

	self.is_init = true
	self.is_inlay = is_inlay

	self.node_list["attr_part"]:SetActive(is_open and is_inlay)
	self.node_list["limit_part"]:SetActive(not is_inlay)
	self.node_list["ph_lingyu_item_bg"]:SetActive(is_open and is_inlay)
	self.node_list["img_lingyu_suo"]:SetActive(not is_open)
	self.node_list["name_part"]:SetActive(false)--is_open and is_inlay)
	self.node_list["lingyu_suo_bg"]:SetActive(not is_open)
	self.node_list["lingyu_add_bg"]:SetActive(is_open and not is_inlay)-- and not self.is_jinlian_item)

	local is_have_batter = EquipmentLingYuWGData.Instance:GetLingYuSlotHaveBatterState(self.cur_equip_part, self.index)
	local is_can_upgrade = EquipmentLingYuWGData.Instance:GetLingYuSlotCanUpgradeState(self.cur_equip_part, self.index)
	-- self:SetLingYuEffect(is_have_batter)
	-- self:SetLingYuRemind(not is_have_batter and is_can_upgrade)
	self:SetLingYuEffect(is_have_batter or is_can_upgrade)
	self:SetLingYuRemind(is_have_batter or is_can_upgrade)

	if is_open and is_inlay then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		bg_idx = item_cfg.color
		local show_color = ITEM_COLOR[item_cfg.color]
		local item_name = item_cfg.name
		self.node_list["lingyu_name_txt"].text.text = ToColorStr(item_name, show_color)
		self.lingyu_item:SetData({item_id = self.data.item_id})
		local lingyu_cfg = EquipmentLingYuWGData.Instance:GetLingYuCfgByItemId(self.data.item_id)

		if lingyu_cfg then
			for i = 1, GameEnum.EQUIP_LINGYU_ATTR_NUM do
				local type = lingyu_cfg["attr_type" .. i]
				local value = lingyu_cfg["attr_val" .. i]
				if value and value > 0 then
					local name = EquipmentWGData.Instance:GetAttrNameByAttrId(type, true)
					local is_per = EquipmentWGData.Instance:GetAttrIsPer(type)
					value = is_per and (value * 0.01 .. "%") or value
					self.node_list["attr_name" .. i].text.text = name --ToColorStr(DeleteStrSpace(name) .. ":", show_color)
					self.node_list["attr_value" .. i].text.text = value --ToColorStr(value, show_color)
					self.attr_part_list[i]:SetActive(true)
				else
					self.attr_part_list[i]:SetActive(false)
				end
			end
		end

	elseif is_open and not is_inlay then
		self.node_list["act_desc"].text.text = ToColorStr(Language.EquipLingYu.KeXiangQian, COLOR3B.GREEN)
	else
		local limit_cfg = EquipmentLingYuWGData.Instance:GetLimitLingYuOpenCfg(self.cur_equip_part, self.index)
		if limit_cfg then
			local lingyu_open_text = string.format(Language.EquipLingYu.LimitLingYuOpen, limit_cfg.order_limit)
			if limit_cfg.vip_level_limit > 0 then
				lingyu_open_text = string.format(Language.EquipLingYu.LimitLingYuVipOpen, limit_cfg.vip_level_limit)
			end
			self.node_list["act_desc"].text.text = lingyu_open_text
		else
			self.node_list["act_desc"].text.text = ""
		end
	end

	--[[ local quality_bg = bg_idx > 0 and "_" .. bg_idx or "_9"
	local bundle, asset = ResPath.GetCommonImages("a1_item_round_bg" .. quality_bg)
	self.node_list["quality_bg"].image:LoadSprite(bundle, asset, function()
		self.node_list["quality_bg"].image:SetNativeSize()
	end) ]]

end
--=================  灵玉槽位 render  END===========================================

-- 灵玉中装备itemrender--------------------------------------------------

EquipLingYuItemRender = EquipLingYuItemRender or BaseClass(BaseRender)
function EquipLingYuItemRender:__init()
	self.old_item_id = 0
	self:CreateChild()
end

function EquipLingYuItemRender:__delete()
	self.old_item_id = 0
	self.old_item_level = -1
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
	self.lbl_name = nil
	self.hl_lbl_name = nil
	self.upflag_icon = nil
	self.is_jinlian_item = nil
end

function EquipLingYuItemRender:CreateChild()
	self.item_cell = ItemCell.New(self.node_list["ph_item"])
	self.item_cell:SetIsShowTips(false)

	if self:IsSelect() then
		self:OnSelectChange(true)
	end
end

function EquipLingYuItemRender:OnFlush()
	if nil == self.data or nil == self.data.param then
		return
	end
	self:SetUpDownIconVisible(false)

	if self.item_cell and self.data.item_id ~= self.old_item_id then
		self.item_cell:SetData(self.data)
	end
	self.old_item_id = self.data.item_id
	local equipmentdata_instance = EquipmentLingYuWGData.Instance

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if self.node_list["lbl_name"] and item_cfg then
		local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		if self.is_jinlian_item then
			local refine_level = equipmentdata_instance:GetLingYuRefineLevelByPart(self.data.index)
			local level_str = refine_level > 0 and "  +" .. refine_level or ""
			item_name = item_name .. level_str
		end
		self.node_list["lbl_name"].text.text = item_name
	end

	local lingyu_info = equipmentdata_instance:GetLingYuInfoListByIndex(self.data.index)
	if nil == lingyu_info then
		return
	end

	local lingyu_remind_list = {}
	local show_lingyu_list = {}
	for i= 0, GameEnum.MAX_LINGYU_COUNT - 1 do
		local is_open = equipmentdata_instance:LingYuSlotIsOpen(self.data.index, i)
		if is_open then
			table.insert(show_lingyu_list, lingyu_info[i])
		end

		if not self.is_jinlian_item then
			local is_have_batter = equipmentdata_instance:GetLingYuSlotHaveBatterState(self.data.index, i)
			local is_can_upgrade = equipmentdata_instance:GetLingYuSlotCanUpgradeState(self.data.index, i)
			lingyu_remind_list[i] = is_have_batter or is_can_upgrade
		end
	end

	if self.is_jinlian_item then
		-- local remind_refine = EquipmentLingYuWGData.Instance:GetBSJLRemindByPart(self.data.index)
		local remind_refine = false
		self:SetUpDownIconVisible(remind_refine)
	else
		for i = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
			if lingyu_remind_list[i] then
				self:SetUpDownIconVisible(true)
				break
			end
		end
	end

	if not IsEmptyTable(show_lingyu_list) then
		table.sort(show_lingyu_list, SortTools.KeyUpperSorter("item_id"))
	end

	for i = 0, GameEnum.MAX_LINGYU_COUNT - 1 do
		if show_lingyu_list[i + 1] then
			local data = show_lingyu_list[i + 1]
			local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
			local bundle, asset = "", ""
			if item_cfg then
				local lingyu_cfg = equipmentdata_instance:GetLingYuCfgByItemId(data.item_id)
				local lingyu_type = lingyu_cfg.lingyu_type
				bundle, asset = ResPath.GetEquipmentIcon("a2_ls_" .. (lingyu_type or 1))
				self.node_list["icon_" .. i].image:LoadSprite(bundle, asset, function()
					self.node_list["icon_" .. i].image:SetNativeSize()
				end)
				self.node_list["icon_" .. i]:SetActive(true)
			else
				-- bundle, asset = ResPath.GetF2CommonIcon("i_gem_bg")
				-- self.node_list["icon_" .. i].image:LoadSprite(bundle, asset, function()
				-- 	self.node_list["icon_" .. i].image:SetNativeSize()
				-- end)
				self.node_list["icon_" .. i]:SetActive(false)
			end

			self.node_list["Img_" .. i]:SetActive(true)
			self.node_list["img_suo_" .. i]:SetActive(false)
		else
			self.node_list["Img_" .. i]:SetActive(false)
			self.node_list["img_suo_" .. i]:SetActive(true)
		end
	end
end

--设置上下浮动图标
function EquipLingYuItemRender:SetUpDownIconVisible(is_visible)
	self.node_list.img_remind:SetActive(is_visible)
end

function EquipLingYuItemRender:OnSelectChange(is_select)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if self.node_list["hl_lbl_name"] and item_cfg then
	local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
	self.node_list["img9_item_bg_hl"]:SetActive(is_select)
	self.node_list["img9_item_bg"]:SetActive(not is_select)
	self.node_list["hl_lbl_name"].text.text = item_name
	end
end

-- 灵玉中装备itemrender  END--------------------------------------------------


---------------------------------------BSLYEquipBodyListCellRender------------------------------------------
BSLYEquipBodyListCellRender = BSLYEquipBodyListCellRender or BaseClass(BaseRender)

function BSLYEquipBodyListCellRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["btn_tip"], BindTool.Bind(self.OnClickTipBtn, self))
end

function BSLYEquipBodyListCellRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local icon_bundle, icon_asset = ResPath.GetEquipBodyIcon(self.data.show_icon)
	self.node_list.icon.image:LoadSprite(icon_bundle, icon_asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local hl_icon_bundle, hl_icon_asset = ResPath.GetEquipBodyIcon(self.data.show_hl_icon)
	self.node_list.icon_hl.image:LoadSprite(hl_icon_bundle, hl_icon_asset, function ()
		self.node_list.icon_hl.image:SetNativeSize()
	end)

	self.node_list.name.text.text = self.data.name
	self.node_list.name_hl.text.text = self.data.name
	self.node_list.specall_name.text.text = self.data.name

	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)
	self.node_list.flag_lock:CustomSetActive(not is_unlocak and not is_wear_equip)
	self.node_list.no_equip_tip:CustomSetActive(is_unlocak and not is_wear_equip)
	self.node_list.btn_tip:CustomSetActive(not can_duanzao)

	local remind = EquipmentLingYuWGData.Instance:GetEquipBodyLingYuRemind(self.data.seq) > 0
	self.node_list.remind:CustomSetActive(remind)
end

function BSLYEquipBodyListCellRender:OnSelectChange(is_select)
	local is_special = self.data.type == 1
	self.node_list.icon:CustomSetActive(is_special or (not is_special and not is_select))
	self.node_list.icon_hl:CustomSetActive(not is_special and is_select)
	self.node_list.special_select:CustomSetActive(is_special and is_select)
	self.node_list.name:CustomSetActive(not is_special and not is_select)
	self.node_list.name_hl:CustomSetActive(not is_special and is_select)
	self.node_list.specall_name:CustomSetActive(is_special)
end

function BSLYEquipBodyListCellRender:OnClickTipBtn()
	local can_duanzao, is_unlocak, is_wear_equip = EquipBodyWGData.Instance:IsEquipBodyCanDuanZao(self.data.seq)

	if not can_duanzao then
		if not is_unlocak then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyLockCanDuanZao)
		elseif not is_wear_equip then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.RoleEquipBody.EquipBodyNotWearEquipCanDuanZao)
		end
	end
end

function BSLYEquipBodyListCellRender:PlayItemTween()
	UITween.FakeHideShow(self.node_list.root)
	local index = self:GetIndex()
	local tween_info = UITween_CONSTS.EquipBody.ListCellTween
	self.cell_delay_key = "BSLYEquipBodyItemCellRender" .. index
	ReDelayCall(self, function()
		if self.node_list and self.node_list.root then
			UITween.FakeToShow(self.node_list.root)
		end
	end, tween_info.NextDoDelay * (index - 1), self.cell_delay_key)
end