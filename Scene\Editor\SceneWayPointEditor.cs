﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using UnityEditor;
using UnityEditorInternal;

[CustomEditor(typeof(SceneWayPoint))]
public class SceneWayPointEditor : Editor {
    private SerializedProperty id;
    private SerializedProperty targetPoints;
    private ReorderableList targetPointsList;

    public override void OnInspectorGUI()
    {
        this.serializedObject.Update();
        EditorGUILayout.PropertyField(id, new GUIContent("ID: "));
        EditorGUILayout.Space();
        this.targetPointsList.DoLayoutList();
        this.serializedObject.ApplyModifiedProperties();
    }

    private void OnEnable()
    {
        var serObj = this.serializedObject;
        id = serObj.FindProperty("id");
        targetPoints = serObj.FindProperty("targetPoints");
        targetPointsList = new ReorderableList(serObj, targetPoints);
        targetPointsList.drawHeaderCallback =
                rect => GUI.Label(rect, "TargetPoints:");
        targetPointsList.elementHeightCallback =
            index => { return 2 * EditorGUIUtility.singleLineHeight; };
        targetPointsList.drawElementCallback =
            (rect, index, selected, focused) =>
            {
                DrawTargetPoints(targetPoints, rect, index, selected, focused);
            };
    }


    private void DrawTargetPoints(
            SerializedProperty property,
            Rect rect,
            int index,
            bool selected,
            bool focused)
    {
        var element = property.GetArrayElementAtIndex(index);

        var targetSceneWayPoint =
            element.FindPropertyRelative("sceneWayPoint");
        var doubleDirection =
            element.FindPropertyRelative("doubleDirection");

        var rectLine = new Rect(
            rect.x,
            rect.y,
            rect.width,
            EditorGUIUtility.singleLineHeight);

        EditorGUI.PropertyField(
            rectLine,
            targetSceneWayPoint,
            new GUIContent("TargetSceneWayPoint: "));

        rectLine.y += EditorGUIUtility.singleLineHeight;
        EditorGUI.PropertyField(
            rectLine,
            doubleDirection,
            new GUIContent("Is DoubleDirection: "));
    }
}
