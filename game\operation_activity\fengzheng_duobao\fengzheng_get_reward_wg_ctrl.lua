require("game/operation_activity/fengzheng_duobao/fengzheng_get_reward_wg_data")
require("game/operation_activity/fengzheng_duobao/fengzheng_kite_render")
require("game/operation_activity/fengzheng_duobao/fengzheng_get_reward_record")
FZGetRewardWGCtrl = FZGetRewardWGCtrl or BaseClass(BaseWGCtrl)

function FZGetRewardWGCtrl:__init()
    if FZGetRewardWGCtrl.Instance ~= nil then
		ErrorLog("[FZGetRewardWGCtrl] attempt to create singleton twice!")
		return
	end
	FZGetRewardWGCtrl.Instance = self
	self.record_view = FZGetRewardRecord.New()
	self.data = FZGetRewardWGData.New()
    -- self:RegisterAllProtocols()
    OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/oa_fengzhengduobao_auto", BindTool.Bind(self.OnHotUpdate, self))
end

function FZGetRewardWGCtrl:__delete()
	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	FZGetRewardWGCtrl.Instance = nil
end

function FZGetRewardWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCRAFengZhengDuoBaoLayerInfo,'OnSCRAFengZhengDuoBaoLayerInfo')
    self:RegisterProtocol(SCRAFengZhengDuoBaoDrawRecord,'OnSCRAFengZhengDuoBaoDrawRecord')
    self:RegisterProtocol(SCRAFengZhengDuoBaoDrawResult,'OnSCRAFengZhengDuoBaoDrawResult')
end

function FZGetRewardWGCtrl:SendAllLayerRequire()
	local layer_cfg = self.data:GetDangWeiCfgByGrade()
	local layer_num = #layer_cfg
	for i = 1, layer_num do
		self:SendOpera(TS_XUNBAO_OPERA_TYPE.LAYER_INFO, i)
	end
end

function FZGetRewardWGCtrl:OnSCRAFengZhengDuoBaoLayerInfo(protocol)
	local protocol_layer = protocol.layer
	self.data:ClearLayerCache(protocol_layer)
	local flag = self.data:GetResultFlagByLayer(protocol_layer)
    self.data:SetLayerInfo(protocol)
	if flag ~= 0 and protocol.draw_result_flag == 0 then
		OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_fengzheng)
	end
	RemindManager.Instance:Fire(RemindName.FZGetReward)
end

function FZGetRewardWGCtrl:OnSCRAFengZhengDuoBaoDrawRecord(protocol)
    self.data:SetRecordInfo(protocol)
	self.data:CalNewRecordNum()
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_fengzheng, "FZRecord", {FZRecord = true})
	if self.record_view:IsOpen() then
		self.record_view:Flush()
	end
end

function FZGetRewardWGCtrl:OnSCRAFengZhengDuoBaoDrawResult(protocol)
    self.data:SetResultInfo(protocol)
    OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_fengzheng, "FZAnim", {FZAnim = true})
	RemindManager.Instance:Fire(RemindName.FZGetReward)
end

function FZGetRewardWGCtrl:OpenRecordView()
	if not self.record_view:IsOpen() then
   	 	self.record_view:Open()
	end
end

--2257请求
--param_1 层数(减1)
--param_2 是否自动购买
function FZGetRewardWGCtrl:SendOpera(opera_type, param_1, param_2, param_3)
	-- print_error(">>>>>>发送协议",opera_type, param_1 - 1, param_2, param_3)
	if param_1 then
		param_1 = param_1 - 1
	end
    local t = {}
    t.rand_activity_type = ACTIVITY_TYPE.OPERA_ACT_FENGZHENG
    t.opera_type = opera_type
    t.param_1 = param_1
    t.param_2 = param_2
    t.param_3 = param_3
    ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(t)
end

function FZGetRewardWGCtrl:OnHotUpdate()
	self.data:LoadFZCfg()
 	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_fengzheng)
end