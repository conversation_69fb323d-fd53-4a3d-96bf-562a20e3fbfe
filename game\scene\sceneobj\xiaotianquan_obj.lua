XiaoTianQuan = XiaoTianQuan or BaseClass(Character)

local FOLLOW_DISTANCE = 200
function XiaoTianQuan:__init(vo)
    self.obj_type = SceneObjType.XiaoTianQuan
    self.draw_obj:SetObjType(self.obj_type)
    self:SetObjId(vo.obj_id)

    self.is_move_to_owner = false
    self.owner_logic_pos = nil

    if self.parent_scene then
        self.owner_obj = self.parent_scene:GetObjectByObjId(self.vo.owner_obj_id)
    end

    self.is_owner_moving = false
    self.owner_logic_pos = nil

end

function XiaoTianQuan:__delete()
    self:RemoveActionDelayTime()
    if self.owner_obj then
        self.owner_obj:ReleaseXiaoTianQuan()
    elseif self.parent_scene and self.parent_scene and self.vo and self.vo.owner_obj_id then
        self.parent_scene:GetObjectByObjId(self.vo.owner_obj_id):ReleaseXiaoTianQuan()
    end
end

function XiaoTianQuan:Update(now_time, elapse_time)
    Character.Update(self, now_time, elapse_time)

    if self.is_owner_moving then
        if self:IsAtkPlaying() then
            return
        end

        if not self.owner_obj and self.parent_scene then
            self.owner_obj = self.parent_scene:GetObjectByObjId(self.vo.owner_obj_id)
        end

        if self.owner_obj then
            local target_pos_x, target_pos_y
            if self.owner_logic_pos then
                target_pos_x = self.owner_logic_pos.x
                target_pos_y = self.owner_logic_pos.y
            else
                target_pos_x, target_pos_y = self.owner_obj:GetLogicPos()
            end

            local dis = GameMath.GetDistance(self.logic_pos.x, self.logic_pos.y, target_pos_x, target_pos_y)
            if dis >= FOLLOW_DISTANCE then
                self:DoMove(target_pos_x, target_pos_y)
            else
                self.is_owner_moving = false
                self.owner_logic_pos = nil
                self:ChangeState(SceneObjState.Stand)
            end
        end
    end
end

function XiaoTianQuan:UpdateStateMove(elapse_time)
    Character.UpdateStateMove(self, elapse_time)
end

function XiaoTianQuan:InitAppearance()
    if self.draw_obj:IsDeleted() then return end
    local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.vo.xiaotianquan_id]
    if not cfg then return end
    self:ChangeModel(SceneObjPart.Main, ResPath.GetMonsterModel(cfg.resid))
end

function XiaoTianQuan:IsCharacter()
    return false
end

function XiaoTianQuan:IsXiaoTianQuan()
    return true
end

function XiaoTianQuan:GetOwerRoleId()
    return self.vo.owner_role_id
end

function XiaoTianQuan:SetTrigger(key)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        if main_part then
            main_part:SetTrigger(key)
        end
    end
end

function XiaoTianQuan:SetBool(key, value)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetBool(key, value)
        end
        if weapon_part then
            weapon_part:SetBool(key, value)
        end
    end
end

function XiaoTianQuan:SetInteger(key, value)
    local draw_obj = self:GetDrawObj()
    if draw_obj then
        local main_part = draw_obj:GetPart(SceneObjPart.Main)
        local weapon_part = draw_obj:GetPart(SceneObjPart.Weapon)
        if main_part then
            main_part:SetInteger(key, value)
        end
        if weapon_part then
            weapon_part:SetInteger(key, value)
        end
    end
end

function XiaoTianQuan:EnterStateAttack()
    local anim_name = SceneObjAnimator.Atk1
    Character.EnterStateAttack(self, anim_name)

    local part = self.draw_obj:GetPart(SceneObjPart.Main)
    if not part or not part.obj then return end

    local anim = part.obj.animator
    if not anim then return end

    local clip = anim:GetAnimationClip(SceneObjAnimator.D_Attack1)
    if not clip then return end
    local action_end_time = clip.length or 0.1
    action_end_time = 0.5
    --恢复为idle状态
    self:RemoveActionDelayTime()
    self.cancel_quest_of_action = GlobalTimerQuest:AddDelayTimer(function ()
        self:SetIsAtkPlaying(false)
    end,action_end_time)
end

--移除回调
function XiaoTianQuan:RemoveActionDelayTime()
    if self.cancel_quest_of_action then
        GlobalTimerQuest:CancelQuest(self.cancel_quest_of_action)
        self.cancel_quest_of_action = nil
    end
end

function XiaoTianQuan:AttackActionEndHandle()
    self:ChangeToCommonState()
end

function XiaoTianQuan:OwnerEnterStateMove(owner_logic_pos)
    self.owner_logic_pos = owner_logic_pos
    self.is_owner_moving = true
end