FbWelkinSceneLogic = FbWelkinSceneLogic or BaseClass(CommonFbLogic)

function FbWelkinSceneLogic:__init()
	FbWelkinSceneLogic.Instance = self
	self.open_view = false
	self.is_pass = 1
end

function FbWelkinSceneLogic:__delete()
end

function FbWelkinSceneLogic:Enter(old_scene_type, new_scene_type, is_ignore_load)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
    FuBenWGData.Instance:SetEnterFb(FUBEN_TYPE.FBCT_WELKIN)
    
    FuBenPanelWGData.Instance:SetEnterXZLLevel(GameVoManager.Instance:GetMainRoleVo().level)

	FuBenPanelWGCtrl.Instance:OpenFanRenTaskView()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
			MainuiWGCtrl.Instance:SetTaskContents(false)
			MainuiWGCtrl.Instance:SetOtherContents(true)
			MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
			MainuiWGCtrl.Instance:SetTeamBtnState(false)
		end
	end)

	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	local max_level = #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").reward_show
	local level = 1
	if pass_level < max_level and pass_level > 0 then
		level = pass_level + 1
	end

	if FuBenWGData.Instance:GetTXGEnter() then
		FuBenWGData.Instance:SetTXGEnter(false)
		FuBenWGCtrl.Instance:OpenFuBenLayerView(level)
		FuBenWGCtrl.Instance:SendFBReqNextLevel()
	else
		FuBenWGCtrl.Instance:OpenFuBenLayerView(level)
	end

	local scene_id = Scene.Instance:GetSceneId()
	local param_t = {scene_id = scene_id}

	local open_guide_data = FuBenPanelWGData.Instance:GetFuBenJiangHuGuideFlag()
	if open_guide_data ~= nil then
		local guide_data = Split(open_guide_data, "|")
		if guide_data[3] ~= nil and tonumber(guide_data[3]) == 2 then
			FuBenPanelWGData.Instance:SetFuBenJiangHuGuideFlag(guide_data[1] .. "|" .. guide_data[2] .. "|4")
		end
	end

	if is_ignore_load then
		self:CloseLoadingCallBack()
	end

    if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.CloseLoadingCallBack, self))
	end
end

function FbWelkinSceneLogic:CloseLoadingCallBack()
    Scene.Instance:SendFBLoadedScene()
end

function FbWelkinSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)

	if old_scene_type ~= new_scene_type then
		FuBenPanelCountDown.Instance:CloseViewHandler()
		UiInstanceMgr.Instance:ColseFBStartDown()

		if self.start_down_event then
			GlobalEventSystem:UnBind(self.start_down_event)
			self.start_down_event = nil
		end

		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
				MainuiWGCtrl.Instance:SetTaskContents(true)
			end
		end)

		FuBenPanelWGCtrl.Instance:CloseFanRenTaskView()
		if FuBenWGCtrl.Instance.next_view:IsOpen() then
			FuBenWGCtrl.Instance:CloseFuBenNextView()
		end

		FuBenPanelWGCtrl.Instance:CheckOpenCloseTip()
        FuBenWGCtrl.Instance:CheckLeaveOpenView(old_scene_type, TabIndex.fubenpanel_welkin)
        if self.close_loading_view_event then
            GlobalEventSystem:UnBind(self.close_loading_view_event)
            self.close_loading_view_event = nil
        end
	end
end

function FbWelkinSceneLogic:OpenFbSceneCd()

end
