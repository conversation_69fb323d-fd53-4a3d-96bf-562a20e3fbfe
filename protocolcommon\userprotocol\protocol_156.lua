-------------仙界boss -----------
CSCrossFairylandBossOperate = CSCrossFairylandBossOperate or BaseClass(BaseProtocolStruct)
function CSCrossFairylandBossOperate:__init()
	self.msg_type = 15610
end

function CSCrossFairylandBossOperate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.operate_type)
    MsgAdapter.WriteInt(self.param1)
    MsgAdapter.WriteInt(self.param2)
    MsgAdapter.WriteInt(self.param3)
end

SCCrossFairylandBossInfo = SCCrossFairylandBossInfo or BaseClass(BaseProtocolStruct)

function SCCrossFairylandBossInfo:__init()
	self.msg_type = 15611
end

function SCCrossFairylandBossInfo:Decode()
    self.layer = MsgAdapter.ReadInt()
	self.count = MsgAdapter.ReadInt()
    self.boss_info_list = {}
    for i = 1, self.count do
        local temp = {}
        temp.boss_id = MsgAdapter.ReadInt()
        temp.next_refresh_time = MsgAdapter.ReadUInt()
        temp.is_concern = MsgAdapter.ReadChar()
        MsgAdapter.ReadChar()
        MsgAdapter.ReadShort()
        self.boss_info_list[i] = temp
    end
end

SCCrossFairylandBossInfoUpdate = SCCrossFairylandBossInfoUpdate or BaseClass(BaseProtocolStruct)

function SCCrossFairylandBossInfoUpdate:__init()
	self.msg_type = 15612
end

function SCCrossFairylandBossInfoUpdate:Decode()
    self.layer = MsgAdapter.ReadInt()
    self.boss_id = MsgAdapter.ReadInt()
    self.next_refresh_time = MsgAdapter.ReadUInt()
    self.is_concern = MsgAdapter.ReadChar()
    MsgAdapter.ReadChar()
    MsgAdapter.ReadShort()
end

SCCrossFairylandBossDropHistory = SCCrossFairylandBossDropHistory or BaseClass(BaseProtocolStruct)

function SCCrossFairylandBossDropHistory:__init()
	self.msg_type = 15613
end

function SCCrossFairylandBossDropHistory:Decode()
	self.count = MsgAdapter.ReadInt()
    self.drop_history_item_list = {}
    for i = 1, self.count do
        self.drop_history_item_list[i] = ProtocolStruct.ReadXianjieDropItem()
        self.drop_history_item_list[i].index = i
    end
end


SCCrossFairylandBossDropHistoryAdd = SCCrossFairylandBossDropHistoryAdd or BaseClass(BaseProtocolStruct)

function SCCrossFairylandBossDropHistoryAdd:__init()
	self.msg_type = 15614
end

function SCCrossFairylandBossDropHistoryAdd:Decode()
    self.drop_history_item = ProtocolStruct.ReadXianjieDropItem()
end



SCCrossFairylandBossForenotice = SCCrossFairylandBossForenotice or BaseClass(BaseProtocolStruct)

function SCCrossFairylandBossForenotice:__init()
	self.msg_type = 15615
end

function SCCrossFairylandBossForenotice:Decode()
    self.layer = MsgAdapter.ReadInt()
    self.boss_id = MsgAdapter.ReadInt()
end

SCCrossFairylandBossSceneInfo = SCCrossFairylandBossSceneInfo or BaseClass(BaseProtocolStruct)

function SCCrossFairylandBossSceneInfo:__init()
	self.msg_type = 15616
end

function SCCrossFairylandBossSceneInfo:Decode()
    self.tired = MsgAdapter.ReadInt()
    self.kick_out_time = MsgAdapter.ReadUInt()
    self.area_camp = MsgAdapter.ReadInt()
end

SCCrossFairylandBaseInfo = SCCrossFairylandBaseInfo or BaseClass(BaseProtocolStruct)
function SCCrossFairylandBaseInfo:__init()
	self.msg_type = 15617
end

function SCCrossFairylandBaseInfo:Decode()
    self.open_time = MsgAdapter.ReadUInt()  --开启时间
end



SCCrossFairylandBossPickFallingItem = SCCrossFairylandBossPickFallingItem or BaseClass(BaseProtocolStruct)
function SCCrossFairylandBossPickFallingItem:__init()
	self.msg_type = 15618
end

function SCCrossFairylandBossPickFallingItem:Decode()
    self.item_id = MsgAdapter.ReadUShort()
    MsgAdapter.ReadShort()
	self.pos_x = MsgAdapter.ReadInt()
	self.pos_y = MsgAdapter.ReadInt()
end


SCCrossFairylandBossBeKillRecordInfo = SCCrossFairylandBossBeKillRecordInfo or BaseClass(BaseProtocolStruct)
function SCCrossFairylandBossBeKillRecordInfo:__init()
	self.msg_type = 15619
end

function SCCrossFairylandBossBeKillRecordInfo:Decode()
    self.count = MsgAdapter.ReadInt()
    self.record_enemy_list = {}
    for i = 1, self.count do
        self.record_enemy_list[i] = ProtocolStruct.ReadXianjieEnemyItem()
    end
end

SCCrossFairylandBossBeKillRecordAdd = SCCrossFairylandBossBeKillRecordAdd or BaseClass(BaseProtocolStruct)
function SCCrossFairylandBossBeKillRecordAdd:__init()
	self.msg_type = 15620
end

function SCCrossFairylandBossBeKillRecordAdd:Decode()
    self.add_item = ProtocolStruct.ReadXianjieEnemyItem()
end

SCCrossFairylandBossBeKillRecordRemove = SCCrossFairylandBossBeKillRecordRemove or BaseClass(BaseProtocolStruct)
function SCCrossFairylandBossBeKillRecordRemove:__init()
	self.msg_type = 15621
end

function SCCrossFairylandBossBeKillRecordRemove:Decode()
    self.remove_uuid = MsgAdapter.ReadUUID()
end


-- 地藏红包请求
CSJizoGiftOperate = CSJizoGiftOperate or BaseClass(BaseProtocolStruct)
function CSJizoGiftOperate:__init()
	self.msg_type = 15623
end

function CSJizoGiftOperate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.operate_type)
    MsgAdapter.WriteInt(self.param1)
    MsgAdapter.WriteInt(self.param2)
    MsgAdapter.WriteInt(self.param3)
end

-- 地藏红包信息
SCJizoGiftInfo = SCJizoGiftInfo or BaseClass(BaseProtocolStruct)
function SCJizoGiftInfo:__init()
	self.msg_type = 15624
end

function SCJizoGiftInfo:Decode()
    self.seq = MsgAdapter.ReadShort()
    self.has_fetch = MsgAdapter.ReadChar()
    MsgAdapter.ReadChar()
    self.accept_time = MsgAdapter.ReadUInt()
    local count = MsgAdapter.ReadInt()
    self.task_list = {}
    for i = 1, count do
        local data = {}
        data.task_id = MsgAdapter.ReadUShort()
        data.is_finish = MsgAdapter.ReadChar()
        MsgAdapter.ReadChar()
        data.progress_num = MsgAdapter.ReadLL()
        self.task_list[data.task_id] = data
    end
end

local ReadDiZangRedPackRecord = function ()
    local data = {}
    data.uid = MsgAdapter.ReadInt()
    data.reward_seq = MsgAdapter.ReadInt()
    data.name = MsgAdapter.ReadStrN(32)
    data.level = MsgAdapter.ReadInt()
    data.vip_level = MsgAdapter.ReadShort()
    data.sex = MsgAdapter.ReadChar()
    data.is_hide_vip = MsgAdapter.ReadChar()
    data.item_id = MsgAdapter.ReadUShort()
    data.is_bind = MsgAdapter.ReadChar()
    MsgAdapter.ReadChar()
    data.num = MsgAdapter.ReadInt()
    data.record_time = MsgAdapter.ReadUInt()
    return data
end

-- 地藏红包记录
SCJizoGiftRecordInfo = SCJizoGiftRecordInfo or BaseClass(BaseProtocolStruct)
function SCJizoGiftRecordInfo:__init()
	self.msg_type = 15625
end

function SCJizoGiftRecordInfo:Decode()
    local count = MsgAdapter.ReadInt()
    self.record_list = {}
    for i = 1, count do
        self.record_list[i] = ReadDiZangRedPackRecord()
    end
end

-- 地藏红包记录 - 增加
SCJizoGiftRecordAdd = SCJizoGiftRecordAdd or BaseClass(BaseProtocolStruct)
function SCJizoGiftRecordAdd:__init()
	self.msg_type = 15626
end

function SCJizoGiftRecordAdd:Decode()
    self.record_info = ReadDiZangRedPackRecord()
end


--烟花抽奖2
SCOAFireworksDraw2Info = SCOAFireworksDraw2Info or BaseClass(BaseProtocolStruct)
function SCOAFireworksDraw2Info:__init()
    self.msg_type = 15627
end

function SCOAFireworksDraw2Info:Decode()
    self.grade = MsgAdapter.ReadInt() --档次
    self.draw_times = MsgAdapter.ReadLL()
end

--抽奖结果返回
SCOAFireworksDraw2Result = SCOAFireworksDraw2Result or BaseClass(BaseProtocolStruct)
function SCOAFireworksDraw2Result:__init()
    self.msg_type = 15628
end

function SCOAFireworksDraw2Result:Decode()
    self.mode = MsgAdapter.ReadInt()
    self.count = MsgAdapter.ReadInt()

    local item_list = {}
    for i = 1, self.count do
        local item = {
            seq = MsgAdapter.ReadInt(),
            item_id = MsgAdapter.ReadUShort(),
            reversh = MsgAdapter.ReadChar(),
            is_bind = MsgAdapter.ReadChar(),
            num  = MsgAdapter.ReadInt(),
        }
        item_list[i] = item
    end
    self.record_list = item_list
end

--限时直购3
SCOALimitRmbBuy3Info = SCOALimitRmbBuy3Info or BaseClass(BaseProtocolStruct)
function SCOALimitRmbBuy3Info:__init()
    self.msg_type = 15629
end

function SCOALimitRmbBuy3Info:Decode()
    self.grade = MsgAdapter.ReadInt() --档次

    self.rmb_buy_times_list = {}
    for i = 0, 19 do
        self.rmb_buy_times_list[i] = MsgAdapter.ReadInt()
    end
end

-----------------跨服组队 -----------------------------
CSCrossTeamOperate = CSCrossTeamOperate or BaseClass(BaseProtocolStruct)
function CSCrossTeamOperate:__init()
	self.msg_type = 15630
end

function CSCrossTeamOperate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.operate_type)
    MsgAdapter.WriteInt(self.param1)
    MsgAdapter.WriteInt(self.param2)
    MsgAdapter.WriteInt(self.param3)
    MsgAdapter.WriteInt(self.param4)
end

SCCrossTeamList = SCCrossTeamList or BaseClass(BaseProtocolStruct)
function SCCrossTeamList:__init()
	self.msg_type = 15631
end

function SCCrossTeamList:Decode()
    self.count = MsgAdapter.ReadInt()
    self.team_list = {}
    for i = 1, self.count do
        self.team_list[i] = ProtocolStruct.ReadCrossTeamMsgInfo()
    end
end

SCCrossTeamAdd = SCCrossTeamAdd or BaseClass(BaseProtocolStruct)
function SCCrossTeamAdd:__init()
	self.msg_type = 15632
end

function SCCrossTeamAdd:Decode()
    self.add_item = ProtocolStruct.ReadCrossTeamMsgInfo()
end

SCCrossTeamRemove = SCCrossTeamRemove or BaseClass(BaseProtocolStruct)
function SCCrossTeamRemove:__init()
	self.msg_type = 15633
end

function SCCrossTeamRemove:Decode()
    self.index = MsgAdapter.ReadInt()
end

CSCrossTeamCreate = CSCrossTeamCreate or BaseClass(BaseProtocolStruct)
function CSCrossTeamCreate:__init()
	self.msg_type = 15634
end

function CSCrossTeamCreate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteChar(self.must_check)
    MsgAdapter.WriteChar(self.member_can_invite)
    MsgAdapter.WriteShort(0)
    MsgAdapter.WriteLL(self.capability_limit)
    MsgAdapter.WriteShort(self.min_level_limit)
    MsgAdapter.WriteShort(self.max_level_limit)
end

SCCrossTeamOut = SCCrossTeamOut or BaseClass(BaseProtocolStruct)
function SCCrossTeamOut:__init()
	self.msg_type = 15635
end

function SCCrossTeamOut:Decode()
    self.reason = MsgAdapter.ReadInt()
    self.uuid = MsgAdapter.ReadUUID()
    self.leader_uuid = MsgAdapter.ReadUUID()
    self.leader_name = MsgAdapter.ReadStrN(32)
end

SCCrossTeamJoin = SCCrossTeamJoin or BaseClass(BaseProtocolStruct)
function SCCrossTeamJoin:__init()
	self.msg_type = 15636
end

function SCCrossTeamJoin:Decode()
    self.reason = MsgAdapter.ReadInt()
    self.uuid = MsgAdapter.ReadUUID()
    self.user_name = MsgAdapter.ReadStrN(32)
end

CSCrossTeamInviteUser = CSCrossTeamInviteUser or BaseClass(BaseProtocolStruct)
function CSCrossTeamInviteUser:__init()
	self.msg_type = 15637
end

function CSCrossTeamInviteUser:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteUUID(self.uuid)
end

SCCrossTeamInviteUserTransmit = SCCrossTeamInviteUserTransmit or BaseClass(BaseProtocolStruct)
function SCCrossTeamInviteUserTransmit:__init()
	self.msg_type = 15638
end

function SCCrossTeamInviteUserTransmit:Decode()
    self.inviter_uuid = MsgAdapter.ReadUUID()
    self.inviter_usid = MsgAdapter.ReadUniqueServerID()
    self.inviter_name = MsgAdapter.ReadStrN(32)
    self.inviter_camp = MsgAdapter.ReadChar()
    MsgAdapter.ReadUChar()
    self.inviter_sex = MsgAdapter.ReadChar()
    self.inviter_vip_level = MsgAdapter.ReadChar()
    self.inviter_level = MsgAdapter.ReadInt()
    self.avatar_key_big = MsgAdapter.ReadUInt()
	self.avatar_key_small = MsgAdapter.ReadUInt()
    self.inviter_team_min_level = MsgAdapter.ReadInt()
    self.inviter_team_max_level = MsgAdapter.ReadInt()
    self.inviter_relation_flag = MsgAdapter.ReadUInt()

    self.inviter_shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    self.reserved1 = MsgAdapter.ReadChar()      --预留1
    self.reserved2 = MsgAdapter.ReadShort()     --预留2
    self.inviter_prof = MsgAdapter.ReadInt()

    AvatarManager.Instance:SetAvatarKey(self.inviter_uuid.temp_low, self.avatar_key_big, self.avatar_key_small)
end


CSCrossTeamInviteUserRet = CSCrossTeamInviteUserRet or BaseClass(BaseProtocolStruct)
function CSCrossTeamInviteUserRet:__init()
	self.msg_type = 15639
end

function CSCrossTeamInviteUserRet:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteUUID(self.inviter_uuid)
    MsgAdapter.WriteInt(self.result)
end

SCCrossTeamReqJoinTransmit = SCCrossTeamReqJoinTransmit or BaseClass(BaseProtocolStruct)
function SCCrossTeamReqJoinTransmit:__init()
	self.msg_type = 15640
end

function SCCrossTeamReqJoinTransmit:Decode()
    self.req_uuid = MsgAdapter.ReadUUID()
    self.req_usid = MsgAdapter.ReadUniqueServerID()
    self.req_role_name = MsgAdapter.ReadStrN(32)
    self.req_role_camp = MsgAdapter.ReadChar()
    MsgAdapter.ReadUChar()
    self.req_role_sex = MsgAdapter.ReadChar()
    self.req_role_vip_level = MsgAdapter.ReadChar()
    self.req_role_level = MsgAdapter.ReadInt()
    self.avatar_key_big = MsgAdapter.ReadUInt()
	self.avatar_key_small = MsgAdapter.ReadUInt()
    self.req_role_photoframe = MsgAdapter.ReadInt()
    self.req_role_capability = MsgAdapter.ReadInt()
    self.req_role_relation_flag = MsgAdapter.ReadUInt()

    self.req_shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    self.reserved1 = MsgAdapter.ReadChar()      --预留1
    self.reserved2 = MsgAdapter.ReadShort()     --预留2
    self.req_role_prof = MsgAdapter.ReadInt()

    AvatarManager.Instance:SetAvatarKey(self.req_uuid.temp_low, self.avatar_key_big, self.avatar_key_small)
end

--队长操作
CSCrossTeamReqJoinRet = CSCrossTeamReqJoinRet or BaseClass(BaseProtocolStruct)
function CSCrossTeamReqJoinRet:__init()
	self.msg_type = 15641
end

function CSCrossTeamReqJoinRet:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteUUID(self.req_uuid)
    MsgAdapter.WriteInt(self.result)
end

CSCrossTeamChangeLimit=  CSCrossTeamChangeLimit or BaseClass(BaseProtocolStruct)
function CSCrossTeamChangeLimit:__init()
	self.msg_type = 15642
end

function CSCrossTeamChangeLimit:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteLL(self.capability_limit)
    MsgAdapter.WriteInt(self.min_level_limit)
    MsgAdapter.WriteInt(self.max_level_limit)
end

SCCrossTeamInfo	= SCCrossTeamInfo or BaseClass(BaseProtocolStruct)
function SCCrossTeamInfo:__init()
	self.msg_type = 15643
end

function SCCrossTeamInfo:Decode()
    self.team = ProtocolStruct.ReadCrossTeamMsgInfo()
end

SCCrossTeamInviteChannelChat = SCCrossTeamInviteChannelChat or BaseClass(BaseProtocolStruct)
function SCCrossTeamInviteChannelChat:__init()
	self.msg_type = 15644
end

function SCCrossTeamInviteChannelChat:Decode()
    self.team_index = MsgAdapter.ReadInt()
    self.leader_info = ProtocolStruct.ReadCrossTeamMemberMsgInfo()
end

SCCrossTeamNearRoleList = SCCrossTeamNearRoleList or BaseClass(BaseProtocolStruct)
function SCCrossTeamNearRoleList:__init()
	self.msg_type = 15645
end

function SCCrossTeamNearRoleList:Decode()
    self.count = MsgAdapter.ReadInt()
    self.role_list = {}
    for i = 1, self.count do
		self.role_list[i] = ProtocolStruct.ReadCrossTeamMsgRoleInfo()
	end
end

--请求角色队伍信息
CSQueryRoleCrossTeamInfo = CSQueryRoleCrossTeamInfo or BaseClass(BaseProtocolStruct)
function CSQueryRoleCrossTeamInfo:__init()
	self.msg_type = 15646
end

function CSQueryRoleCrossTeamInfo:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteUUID(self.uuid)
end

--请求角色队伍信息返回
SCQueryRoleCrossTeamRet = SCQueryRoleCrossTeamRet or BaseClass(BaseProtocolStruct)
function SCQueryRoleCrossTeamRet:__init()
	self.msg_type = 15647
end

function SCQueryRoleCrossTeamRet:Decode()
	self.team_index = MsgAdapter.ReadInt()
	self.uuid = MsgAdapter.ReadUUID()
end

--队伍申请合并请求
CSCrossTeamMergeReq = CSCrossTeamMergeReq or BaseClass(BaseProtocolStruct)
function CSCrossTeamMergeReq:__init()
	self.msg_type = 15648
end

function CSCrossTeamMergeReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteUUID(self.uuid)
end

CSCrossTeamOneKeyMergeReq = CSCrossTeamOneKeyMergeReq or BaseClass(BaseProtocolStruct)
function CSCrossTeamOneKeyMergeReq:__init()
    self.msg_type = 15649
    self.count = 0
    self.role_uuid = {}
end

function CSCrossTeamOneKeyMergeReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.count)
    for i = 1, self.count do
		MsgAdapter.WriteUUID(self.role_uuid[i])
	end
end

SCCrossTeamMergeReqTransmit = SCCrossTeamMergeReqTransmit or BaseClass(BaseProtocolStruct)
function SCCrossTeamMergeReqTransmit:__init()
	self.msg_type = 15650
end

function SCCrossTeamMergeReqTransmit:Decode()
	self.team_index = MsgAdapter.ReadInt()
    self.uuid = MsgAdapter.ReadUUID()
    self.usid = MsgAdapter.ReadUniqueServerID()
    self.name = MsgAdapter.ReadStrN(32)
    self.vip_level = MsgAdapter.ReadShort()
    self.level = MsgAdapter.ReadShort()
    self.sex = MsgAdapter.ReadChar()
	MsgAdapter.ReadUChar()
	self.shizhuang_photoframe = MsgAdapter.ReadShort()
	self.avatar_key_big = MsgAdapter.ReadUInt()
	self.avatar_key_small = MsgAdapter.ReadUInt()
    self.capability = MsgAdapter.ReadLL()
	self.relation_flag = MsgAdapter.ReadUInt()
    
    self.shield_vip_flag = MsgAdapter.ReadChar()    --隐藏VIP等级信息
    self.reserved1 = MsgAdapter.ReadChar()      --预留1
    self.reserved2 = MsgAdapter.ReadShort()     --预留2
	self.prof = MsgAdapter.ReadInt()

    AvatarManager.Instance:SetAvatarKey(self.uuid.temp_low, self.avatar_key_big, self.avatar_key_small)
end

--申请队长请求下发（只有队长才能收到）
SCCrossTeamReqLeaderTransmit = SCCrossTeamReqLeaderTransmit or BaseClass(BaseProtocolStruct)
function SCCrossTeamReqLeaderTransmit:__init()
	self.msg_type = 15651
	self.role_name = ""
end

function SCCrossTeamReqLeaderTransmit:Decode()
	self.uuid = MsgAdapter.ReadUUID()
	self.role_name = MsgAdapter.ReadStrN(32)
end

--申请队长请求回复（申请者才能收到）
CSCrossTeamReqLeaderRet = CSCrossTeamReqLeaderRet or BaseClass(BaseProtocolStruct)
function CSCrossTeamReqLeaderRet:__init()
	self.msg_type = 15652
	self.is_accept = 0
end

function CSCrossTeamReqLeaderRet:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteUUID(self.uuid)
	MsgAdapter.WriteInt(self.is_accept)
end

CSCrossTeamNoLongerOperateReq = CSCrossTeamNoLongerOperateReq or BaseClass(BaseProtocolStruct)
function CSCrossTeamNoLongerOperateReq:__init()
	self.msg_type = 15653
	self.is_accept = 0
end

function CSCrossTeamNoLongerOperateReq:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.no_longer_type)
	MsgAdapter.WriteUUID(self.uuid)
end

----------------跨服组队end ---------------------------


--------------------------许愿仙池start---------------------------
SCRAYanHuaShengDian3Info = SCRAYanHuaShengDian3Info or BaseClass(BaseProtocolStruct)
function SCRAYanHuaShengDian3Info:__init()
    self.msg_type = 15654
end

function SCRAYanHuaShengDian3Info:Decode()
    self.grade = MsgAdapter.ReadInt()                   --阶段
    self.person_draw_count = MsgAdapter.ReadInt()       --个人抽奖次数
    self.leiji_reward_fetch_flag = MsgAdapter.ReadLL()  --累计奖励领取标记
    self.cur_cycle = MsgAdapter.ReadInt()               --当前周期？
    self.is_skip_comic = MsgAdapter.ReadInt()           --跳过动画？
    self.sp_guarantee_x = MsgAdapter.ReadInt()          --特殊保底次数？
    self.sp_guarantee_n = MsgAdapter.ReadInt()          --特殊保底轮数？
    self.sp_enter_num = MsgAdapter.ReadInt()            --进入保底库次数？
    self.gather_small_count = MsgAdapter.ReadInt()      --小宝箱采集次数
    self.gather_big_count = MsgAdapter.ReadInt()        --大宝箱采集次数
end

SCRAYanHuaShengDian3RecordListInfo = SCRAYanHuaShengDian3RecordListInfo or BaseClass(BaseProtocolStruct)
function SCRAYanHuaShengDian3RecordListInfo:__init()
    self.msg_type = 15655
end

function SCRAYanHuaShengDian3RecordListInfo:Decode()
    self.record_count = MsgAdapter.ReadInt()
    self.record_list = {}
    for i = 1, self.record_count do
        self.record_list[i] = {}
        self.record_list[i].draw_time = MsgAdapter.ReadUInt()
        self.record_list[i].role_name = MsgAdapter.ReadStrN(32)
        self.record_list[i].item_id = MsgAdapter.ReadInt()
        self.record_list[i].num = MsgAdapter.ReadInt()
    end
end

SCRAYanHuaShengDian3DrawRewardInfo = SCRAYanHuaShengDian3DrawRewardInfo or BaseClass(BaseProtocolStruct)
function SCRAYanHuaShengDian3DrawRewardInfo:__init()
    self.msg_type = 15656
end

function SCRAYanHuaShengDian3DrawRewardInfo:Decode()
    self.count = MsgAdapter.ReadInt()
    self.reward_list = {}
    for i = 1, self.count do
        self.reward_list[i] = {}
        self.reward_list[i].reward_flag = MsgAdapter.ReadShort() --0 普通，1保底
        self.reward_list[i].reward_pool_id = MsgAdapter.ReadShort()
        self.reward_list[i].reward_id = MsgAdapter.ReadInt()
        --self.reward_list[i].is_zhenxi = self.reward_list[i].reward_flag == 1
    end
end

SCRAYanHuaShengDian3BaoDiRewardDrawInfo = SCRAYanHuaShengDian3BaoDiRewardDrawInfo or BaseClass(BaseProtocolStruct)

function SCRAYanHuaShengDian3BaoDiRewardDrawInfo:__init()
    self.msg_type = 15657
end

function SCRAYanHuaShengDian3BaoDiRewardDrawInfo:Decode()
    self.count = MsgAdapter.ReadInt()
    self.reward_info = {}
    for i = 1, self.count do
        local temp = {}
        temp.reward_id = MsgAdapter.ReadShort()
        temp.get_count = MsgAdapter.ReadShort()
        self.reward_info[i] = temp
    end
end

--运营活动-每日累充
SCOADailyRechargeInfo = SCOADailyRechargeInfo or BaseClass(BaseProtocolStruct)
function SCOADailyRechargeInfo:__init()
    self.msg_type = 15658
end

function SCOADailyRechargeInfo:Decode()
    self.grade = MsgAdapter.ReadInt()
    self.chongzhi_gold = MsgAdapter.ReadLL()
    local reward_flag = MsgAdapter.ReadLL()  --奖励领取标记
    self.reward_flag = bit:d2b_l2h(reward_flag, nil, true)
end

-- 在线奖励
SCOAOnlineRewardInfo = SCOAOnlineRewardInfo or BaseClass(BaseProtocolStruct)
function SCOAOnlineRewardInfo:__init()
    self.msg_type = 15659
end

function SCOAOnlineRewardInfo:Decode()
    self.grade = MsgAdapter.ReadInt()
    self.seq = MsgAdapter.ReadInt()
    self.online_time = MsgAdapter.ReadLL()
end






----------------一元豪礼 start -------------------------
SConeMoneyBuyInfo = SConeMoneyBuyInfo or BaseClass(BaseProtocolStruct)
function SConeMoneyBuyInfo:__init()
    self.msg_type = 15660
    self.role_name = ""
end

function SConeMoneyBuyInfo:Decode()
    self.flag = MsgAdapter.ReadInt()
    self.rmb_seq = MsgAdapter.ReadInt()
    self.activity_time = MsgAdapter.ReadUInt()
end
----------------一元豪礼 end ---------------------------


--今日特惠2
SCOALimitRmbBuy2Info = SCOALimitRmbBuy2Info or BaseClass(BaseProtocolStruct)
function SCOALimitRmbBuy2Info:__init()
    self.msg_type = 15662
end

function SCOALimitRmbBuy2Info:Decode()
    self.grade = MsgAdapter.ReadInt() --档次

    self.rmb_buy_times_list = {}
    for i = 0, 19 do
        self.rmb_buy_times_list[i] = MsgAdapter.ReadInt()
    end
end


---荣耀水晶
SCOAGloryCrystalInfo = SCOAGloryCrystalInfo or BaseClass(BaseProtocolStruct)
function SCOAGloryCrystalInfo:__init()
    self.msg_type = 15663
end

local function MsgCollectItem()
    local collect_task = {}
    collect_task.process_flag = bit:d2b_l2h(MsgAdapter.ReadShort())      --任务进度 bit 0:未完成 1: 完成
    collect_task.flag = MsgAdapter.ReadChar()               --收集奖励标记 0: 未完成 1: 可领取 2: 已领取
    local re_ch = MsgAdapter.ReadChar()
    return collect_task
end

local function TaskItem(seq)
    local task_info = {}
    task_info.seq = seq
    task_info.process = MsgAdapter.ReadInt()        -- 任务进度
    task_info.fetch_flag = MsgAdapter.ReadChar()    -- 奖励领取标记
    task_info.re_ch = MsgAdapter.ReadChar()         -- 预留
    task_info.re_sh = MsgAdapter.ReadShort()        -- 预留
    return task_info
end

function SCOAGloryCrystalInfo:Decode()
    local max_convert_seq_count = 40
    local max_convert_type_count = 3
    local max_collect_task_count = 20
    local max_rmb_gift_count = 10       -- 直购礼包最大数量
    local max_daily_task_count = 20     -- 每日任务最大数量

    self.grade = MsgAdapter.ReadInt()
    self.draw_times = MsgAdapter.ReadLL()
    self.times_reward_flag = bit:d2b_l2h(MsgAdapter.ReadLL(), nil, true)
    self.free_draw_times = MsgAdapter.ReadInt()         --免费抽奖次数.

    local collect_task = {}
    for i = 0, max_collect_task_count - 1 do
        collect_task[i] = MsgCollectItem()
    end
    self.collect_task_list = collect_task               --收集任务列表.key:task_seq.

    self.convert_times_list = {}
    for i = 0, max_convert_seq_count - 1 do
        self.convert_times_list[i] = {}
        for j = 0, max_convert_type_count - 1 do
            self.convert_times_list[i][j] = MsgAdapter.ReadUChar()          --兑换次数 限购 0:每日 1:每周 2:终身.
        end
    end
    
    local daily_task = {}
    for i = 0, max_daily_task_count - 1 do
        daily_task[i] = TaskItem(i)
    end
    self.daily_task = daily_task   --每日任务列表.key:task_seq.

    local rmb_gift_count = {}
    for i = 0, max_rmb_gift_count - 1 do
        rmb_gift_count[i] = MsgAdapter.ReadUShort()
    end
    self.rmb_gift_count = rmb_gift_count   --直购礼包购买次数.key:seq.

    self.rmb_one_key_flag = MsgAdapter.ReadChar()           -- 直购一键打包标记
    self.rmb_one_key_reward_flag = MsgAdapter.ReadChar()    -- 直购一键打包奖励领取标记
    self.re_sh = MsgAdapter.ReadShort()  -- 预留
    self.total_recharge_num = MsgAdapter.ReadInt()          -- 累充数
    self.total_recharge_reward_flag = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)  -- 累充奖励领取标记
    
    -- 每档各任务的领取标记
    self.grade_collect_reward_flag = {}
    for i = 0, 31 do
        self.grade_collect_reward_flag[i] = bit:d2b_l2h(MsgAdapter.ReadInt(), nil, true)
    end
end

SCOAGloryCrystalDrawResult = SCOAGloryCrystalDrawResult or BaseClass(BaseProtocolStruct)
function SCOAGloryCrystalDrawResult:__init()
    self.msg_type = 15664
end

function SCOAGloryCrystalDrawResult:Decode()
    local function get_item_data()
        local item_data = {
            item_id = MsgAdapter.ReadUShort(),
            reversh = MsgAdapter.ReadChar(),
            is_bind = MsgAdapter.ReadChar(),
            num  = MsgAdapter.ReadInt(),
        }
        return item_data
    end 

    self.mode = MsgAdapter.ReadInt()

    local MAX_RESULT_RAODI_ITEM_COUNT = 10
    local baodi_item = {}
    for i = 1, MAX_RESULT_RAODI_ITEM_COUNT do
        baodi_item[i] = get_item_data()
    end
    self.baodi_item = baodi_item

    self.count = MsgAdapter.ReadInt()
    local item_list = {}
    for i = 1, self.count do
        item_list[i] = get_item_data()
    end

    self.result_reward_list = item_list
end

--  运营活动-限时优惠
SCOaTimeLimitDiscountInfo = SCOaTimeLimitDiscountInfo or BaseClass(BaseProtocolStruct)
function SCOaTimeLimitDiscountInfo:__init()
    self.msg_type = 15665
end

function SCOaTimeLimitDiscountInfo:Decode()
    self.day_reward_flag = MsgAdapter.ReadInt()             -- 每日礼包
    self.gift_list = {}
    --self.count = MsgAdapter.ReadInt()
    for i = 1, 60 do
        self.gift_list[i] = MsgAdapter.ReadInt()
    end

    self.is_get_extra_reward = MsgAdapter.ReadChar()        -- 额外的奖励
    self.is_buy_one = MsgAdapter.ReadChar()
    MsgAdapter.ReadShort()
end

--------------小猫探险----------------
SCOACatVentureInfo = SCOACatVentureInfo or BaseClass(BaseProtocolStruct)
function SCOACatVentureInfo:__init()
    self.msg_type = 15666
end

function SCOACatVentureInfo:Decode()
    self.grade = MsgAdapter.ReadInt()
    self.step = MsgAdapter.ReadInt()
    local step_reward_state = MsgAdapter.ReadLL()
    self.step_reward_flag = bit:d2b_l2h(step_reward_state, nil, true)
    self.count = MsgAdapter.ReadInt()
    self.task_list = {}
    for i = 1, self.count do
        local task_id = MsgAdapter.ReadShort()
        self.task_list[task_id] = {}
        self.task_list[task_id].task_id = task_id
        self.task_list[task_id].status = MsgAdapter.ReadChar()
        MsgAdapter.ReadChar()
        self.task_list[task_id].progress_num_flag = MsgAdapter.ReadLL()
    end
end

SCOACatVentureTaskUpdate = SCOACatVentureTaskUpdate or BaseClass(BaseProtocolStruct) -- 单个任务变化
function SCOACatVentureTaskUpdate:__init()
    self.msg_type = 15667
end

function SCOACatVentureTaskUpdate:Decode()
    local data = {}
    data.task_id = MsgAdapter.ReadShort()
    data.status = MsgAdapter.ReadChar()
    MsgAdapter.ReadChar()
    data.progress_num_flag = MsgAdapter.ReadLL()
    self.change_data = data
end


--------------小猫探险End----------------

--------------------------返利活动 start -----------------------------------
--充值立减礼包信息
SCOARechargeDiscountsInfo = SCOARechargeDiscountsInfo or BaseClass(BaseProtocolStruct)
function SCOARechargeDiscountsInfo:__init()
    self.msg_type = 15668
end

function SCOARechargeDiscountsInfo:Decode()
    self.grade = MsgAdapter.ReadInt() --档次
    self.chongzhi_gold = MsgAdapter.ReadLL()
    local item_list = {}
	for i = 1, 10 do
		local item = {
			is_buy = MsgAdapter.ReadChar(),
			is_random = MsgAdapter.ReadChar(),
			resvrvesh = MsgAdapter.ReadShort(),
			random_gold = MsgAdapter.ReadLL(),
		}
		item_list[i] = item
	end
	self.gift_item_list = item_list
end

--单个礼包更新
SCOARechargeDiscountsGiftUpdate = SCOARechargeDiscountsGiftUpdate or BaseClass(BaseProtocolStruct)
function SCOARechargeDiscountsGiftUpdate:__init()
    self.msg_type = 15669
end

function SCOARechargeDiscountsGiftUpdate:Decode()
    self.seq = MsgAdapter.ReadInt()

	local item = {
        is_buy = MsgAdapter.ReadChar(),
        is_random = MsgAdapter.ReadChar(),
        resvrvesh = MsgAdapter.ReadShort(),
        random_gold = MsgAdapter.ReadLL(),
    }
	self.gift_item = item
end

--烟花抽奖
SCOAFireworksDrawInfo = SCOAFireworksDrawInfo or BaseClass(BaseProtocolStruct)
function SCOAFireworksDrawInfo:__init()
    self.msg_type = 15670
end

function SCOAFireworksDrawInfo:Decode()
    self.grade = MsgAdapter.ReadInt() --档次
    self.draw_times = MsgAdapter.ReadLL()
end

--抽奖结果返回
SCOAFireworksDrawResult = SCOAFireworksDrawResult or BaseClass(BaseProtocolStruct)
function SCOAFireworksDrawResult:__init()
    self.msg_type = 15671
end

function SCOAFireworksDrawResult:Decode()
    self.mode = MsgAdapter.ReadInt()
    self.count = MsgAdapter.ReadInt()

    local item_list = {}
	for i = 1, self.count do
		local item = {
            seq = MsgAdapter.ReadInt(),
			item_id = MsgAdapter.ReadUShort(),
			reversh = MsgAdapter.ReadChar(),
            is_bind = MsgAdapter.ReadChar(),
            num  = MsgAdapter.ReadInt(),
		}
		item_list[i] = item
	end
	self.record_list = item_list
end

SCOAExtinctGiftInfo = SCOAExtinctGiftInfo or BaseClass(BaseProtocolStruct)
function SCOAExtinctGiftInfo:__init()
    self.msg_type = 15672
end

function SCOAExtinctGiftInfo:Decode()
    self.grade = MsgAdapter.ReadInt() --档次
    self.main_long_get = MsgAdapter.ReadChar() --龙的领取状态
    self.resvrvech = MsgAdapter.ReadChar()
    self.resvrvesh= MsgAdapter.ReadShort()
    local sub_reward_flag = MsgAdapter.ReadLL()
    self.sub_reward_flag = bit:d2b_l2h(sub_reward_flag, nil, true) --子任务的完成状态
    local main_status_flag = MsgAdapter.ReadLL()
    self.main_status_flag = bit:d2b_l2h(main_status_flag, nil, true) --大任务的领取状态

    self.sub_all_flag_list = {}
	local flag = 0
	for i = 0, 9 do
		flag = MsgAdapter.ReadLL()
		self.sub_all_flag_list[i] = bit:d2b_l2h(flag, nil, true)
	end
end

SCOALimitRmbBuyInfo = SCOALimitRmbBuyInfo or BaseClass(BaseProtocolStruct)
function SCOALimitRmbBuyInfo:__init()
    self.msg_type = 15673
end

function SCOALimitRmbBuyInfo:Decode()
    self.grade = MsgAdapter.ReadInt() --档次

    self.rmb_buy_times_list = {}
    for i = 0, 19 do
        self.rmb_buy_times_list[i] = MsgAdapter.ReadInt()
    end
end

-----------------------返利活动 end-----------------------------------------------

-----冲榜助力-----
SCOAHelpRankInfo = SCOAHelpRankInfo or BaseClass(BaseProtocolStruct)
function SCOAHelpRankInfo:__init()
    self.msg_type = 15674
end

function SCOAHelpRankInfo:Decode()
    self.grade = MsgAdapter.ReadInt() --档次

    self.every_day_flag = MsgAdapter.ReadUInt() --每日领取标识

    self.lingyu_shop_list = {} -- 灵玉商店
    for i = 0, 4 do
        local data = {}
        data.seq = MsgAdapter.ReadShort() -- 礼包索引
        data.buy_count = MsgAdapter.ReadShort() --已购买次数
        self.lingyu_shop_list[i] = data
    end

    self.score_shop_list = {} -- 积分商店
    for i = 0, 4 do
        local data = {}
        for j = 0, 9 do
            data[j] = MsgAdapter.ReadInt() -- 已购买次数
        end
        self.score_shop_list[i] = data
    end

    self.shop_list = {} -- 直购商店商品列表
    for i = 0, 4 do
        local data = {}
        for j = 0, 9 do
            data[j] = MsgAdapter.ReadInt() -- 已购买次数
        end
        self.shop_list[i] = data
    end

    self.all_buy_flag = MsgAdapter.ReadInt() -- 当天所有商品买完标记
end

-----神藏直购-----
SCOAGodRmbBuyInfo = SCOAGodRmbBuyInfo or BaseClass(BaseProtocolStruct)
function SCOAGodRmbBuyInfo:__init()
    self.msg_type = 15675
end

function SCOAGodRmbBuyInfo:Decode()
    self.grade = MsgAdapter.ReadInt() --档次

    local shop_buy_flag = MsgAdapter.ReadLL()  
    self.shop_buy_flag = bit:d2b_l2h(shop_buy_flag, nil, true)
end

-----武魂直购-----
SCOATianShenRmbBuyInfo = SCOATianShenRmbBuyInfo or BaseClass(BaseProtocolStruct)
function SCOATianShenRmbBuyInfo:__init()
    self.msg_type = 15676
end

function SCOATianShenRmbBuyInfo:Decode()
    self.grade = MsgAdapter.ReadInt() --档次

    self.shop_buy_count = {}
    for i = 0, 15 do
        self.shop_buy_count[i] = MsgAdapter.ReadInt()
    end
end

-------DIY1抽奖信息-------
SCOADiyDraw1Info = SCOADiyDraw1Info or BaseClass(BaseProtocolStruct)
function SCOADiyDraw1Info:__init()
    self.msg_type = 15677
end

function SCOADiyDraw1Info:Decode()
    self.grade = MsgAdapter.ReadInt() --档次
    self.draw_times = MsgAdapter.ReadLL() --抽奖次数
    self.choose_pool_seq_list = {}
    for i = 0, 9 do
        self.choose_pool_seq_list[i] = MsgAdapter.ReadShort()
    end
end

-------DIY2抽奖信息-------
SCOADiyDraw2Info = SCOADiyDraw2Info or BaseClass(BaseProtocolStruct)
function SCOADiyDraw2Info:__init()
    self.msg_type = 15678
end

function SCOADiyDraw2Info:Decode()
    self.grade = MsgAdapter.ReadInt() --档次
    self.draw_times = MsgAdapter.ReadLL() --抽奖次数
    self.choose_pool_seq_list = {}
    for i = 0, 9 do
        self.choose_pool_seq_list[i] = MsgAdapter.ReadShort()
    end
end

--DIY1抽奖结果
SCOADiyDraw1Result = SCOADiyDraw1Result or BaseClass(BaseProtocolStruct)
function SCOADiyDraw1Result:__init()
    self.msg_type = 15679
end

function SCOADiyDraw1Result:Decode()
    self.mode = MsgAdapter.ReadInt()
    self.count = MsgAdapter.ReadInt()

    self.result_item_list = {}
    for i = 1, self.count do
        local data = {
            item_id = MsgAdapter.ReadUShort(),
            reversh = MsgAdapter.ReadChar(),
            is_bind = MsgAdapter.ReadChar(),
            num = MsgAdapter.ReadInt(),
        }
        self.result_item_list[i] = data
    end
end

--DIY2抽奖结果
SCOADiyDraw2Result = SCOADiyDraw2Result or BaseClass(BaseProtocolStruct)
function SCOADiyDraw2Result:__init()
    self.msg_type = 15680
end

function SCOADiyDraw2Result:Decode()
    self.mode = MsgAdapter.ReadInt()
    self.count = MsgAdapter.ReadInt()

    self.result_item_list = {}
    for i = 1, self.count do
        local data = {
            item_id = MsgAdapter.ReadUShort(),
            reversh = MsgAdapter.ReadChar(),
            is_bind = MsgAdapter.ReadChar(),
            num = MsgAdapter.ReadInt(),
        }
        self.result_item_list[i] = data
    end
end

--DIY1抽奖日志
SCOADiyDraw1Record = SCOADiyDraw1Record or BaseClass(BaseProtocolStruct)
function SCOADiyDraw1Record:__init()
    self.msg_type = 15681
end

function SCOADiyDraw1Record:Decode()
    self.count = MsgAdapter.ReadInt()
    self.record_list = {}
    for i = 1, self.count do
        local data = {
            uid = MsgAdapter.ReadInt(),
            name = MsgAdapter.ReadStrN(32),
            level = MsgAdapter.ReadInt(),
            vip_level = MsgAdapter.ReadShort(),
            sex = MsgAdapter.ReadChar(),
            is_hide_vip = MsgAdapter.ReadChar(),
            item_id = MsgAdapter.ReadUShort(),
            is_bind = MsgAdapter.ReadChar(),
            reverch = MsgAdapter.ReadChar(),
            num = MsgAdapter.ReadInt(),
            record_time = MsgAdapter.ReadUInt(),
        }
        self.record_list[i] = data
    end
end

--DIY1抽奖日志更新
SCOADiyDraw1RecordAdd = SCOADiyDraw1RecordAdd or BaseClass(BaseProtocolStruct)
function SCOADiyDraw1RecordAdd:__init()
    self.msg_type = 15682
end

function SCOADiyDraw1RecordAdd:Decode()
    self.record_item = {
        uid = MsgAdapter.ReadInt(),
        name = MsgAdapter.ReadStrN(32),
        level = MsgAdapter.ReadInt(),
        vip_level = MsgAdapter.ReadShort(),
        sex = MsgAdapter.ReadChar(),
        is_hide_vip = MsgAdapter.ReadChar(),
        item_id = MsgAdapter.ReadUShort(),
        is_bind = MsgAdapter.ReadChar(),
        reverch = MsgAdapter.ReadChar(),
        num = MsgAdapter.ReadInt(),
        record_time = MsgAdapter.ReadUInt(),
    }
    
end

--圣器协议
CSRelicOperate = CSRelicOperate or BaseClass(BaseProtocolStruct)
function CSRelicOperate:__init()
    self.msg_type = 15683
end

function CSRelicOperate:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.opera_type)
    MsgAdapter.WriteInt(self.param_1)
    MsgAdapter.WriteInt(self.param_2)
    MsgAdapter.WriteInt(self.param_3)
end

--圣器装备槽位数据
local function GetRelicSlotData(relic_seq, slot_index) -- 圣器seq，装备槽位index
    local relic_slot_item = {}
    relic_slot_item.item_id = MsgAdapter.ReadUShort()   -- 装备id
    MsgAdapter.ReadShort()
    relic_slot_item.level = MsgAdapter.ReadInt()        -- 等级
    relic_slot_item.star_level = MsgAdapter.ReadInt()   -- 星级
    relic_slot_item.grade = MsgAdapter.ReadInt()        -- 阶数
    relic_slot_item.grade_power = MsgAdapter.ReadLL()   -- 当前经验值
    relic_slot_item.relic_seq = relic_seq
    relic_slot_item.slot_index = slot_index
    
    local color = GameEnum.ITEM_COLOR_WHITE
    if relic_slot_item.item_id > 0 then
        local cfg_data = ItemWGData.Instance:GetItemConfig(relic_slot_item.item_id)
        if not IsEmptyTable(cfg_data) and cfg_data.color then
            color = cfg_data.color
        end
    end

    relic_slot_item.color = color
    return relic_slot_item
end

local function GetRelicItemData(relic_seq)-- 圣器seq
    local relic_item = {}
    relic_item.active_flag = MsgAdapter.ReadChar()-- 圣器激活状态
    MsgAdapter.ReadChar()
    MsgAdapter.ReadShort()
    local seal_flag = MsgAdapter.ReadLL()          -- 4个封印状态
    relic_item.seal_flag = bit:d2b_l2h(seal_flag, nil, true)
    relic_item.skill_level = MsgAdapter.ReadInt() --技能等级
    relic_item.skill_exp = MsgAdapter.ReadLL() --技能经验
    relic_item.suit_level = MsgAdapter.ReadInt() -- 套装等级
    relic_item.relic_seq = relic_seq
    relic_item.relic_slot_list = {}

    for i = 0, 7 do
        relic_item.relic_slot_list[i] = GetRelicSlotData(relic_seq, i)
    end

    return relic_item
end

SCRelicItemInfo = SCRelicItemInfo or BaseClass(BaseProtocolStruct) -- 所有圣器信息
function SCRelicItemInfo:__init()
    self.msg_type = 15684
end

function SCRelicItemInfo:Decode()
    local relic_item_list = {}
    for i = 0, 19 do --目前上限20
        relic_item_list[i] = GetRelicItemData(i)
    end

    self.relic_item_list = relic_item_list
end

SCRelicItemUpdate = SCRelicItemUpdate or BaseClass(BaseProtocolStruct) -- 单个圣器变化
function SCRelicItemUpdate:__init()
    self.msg_type = 15685
end

function SCRelicItemUpdate:Decode()
    self.relic_seq = MsgAdapter.ReadInt()
    self.relic_item = GetRelicItemData(self.relic_seq)
end


local function GetRelicPowerItemData(power_type)
    local power_item = {}
    power_item.power_flag = MsgAdapter.ReadChar()--能量直购购买状态
    MsgAdapter.ReadChar()
    MsgAdapter.ReadShort()
    power_item.power_level = MsgAdapter.ReadInt()-- 能量等级
    power_item.total_power = MsgAdapter.ReadLL() --能量池数据 
    power_item.daily_power = MsgAdapter.ReadLL() --今天获取能量值
    power_item.power_list = {}
    for i = 0, 39 do
        power_item.power_list[i] = {}
        power_item.power_list[i].power_value = MsgAdapter.ReadChar()
        power_item.power_list[i].index = i
        power_item.power_list[i].power_type = power_type
    end

    return power_item
end

SCRelicPowerItemInfo = SCRelicPowerItemInfo or BaseClass(BaseProtocolStruct) --圣器/暗器自己的进阶能量信息
function SCRelicPowerItemInfo:__init()
    self.msg_type = 15686
end

function SCRelicPowerItemInfo:Decode()
    local power_item_list = {}
    for i = 0, 1 do
        power_item_list[i] = GetRelicPowerItemData(i)
    end

    self.power_item_list = power_item_list
end

SCRelicPowerItemUpdate = SCRelicPowerItemUpdate or BaseClass(BaseProtocolStruct) -- 圣器/暗器单个进阶能量信息
function SCRelicPowerItemUpdate:__init()
    self.msg_type = 15687
end

function SCRelicPowerItemUpdate:Decode()
    self.type = MsgAdapter.ReadInt()
    self.power_item = GetRelicPowerItemData(self.type)
end

CSRelicDecompsEquip = CSRelicDecompsEquip or BaseClass(BaseProtocolStruct) --技能分解装备
function CSRelicDecompsEquip:__init()
    self.msg_type = 15688
    self.seq = 0
    self.count = 0
    self.bag_list = {}
end

function CSRelicDecompsEquip:Encode()
    MsgAdapter.WriteBegin(self.msg_type)
    MsgAdapter.WriteInt(self.seq)
    MsgAdapter.WriteInt(self.count)
    for i = 1, self.count do
        MsgAdapter.WriteUShort(self.bag_list[i].item_id or 0)
        MsgAdapter.WriteShort(self.bag_list[i].index or -1)
        MsgAdapter.WriteInt(self.bag_list[i].num or 0)
    end
end


local function GetScRelicBagGridItem()
    local grid_item = {}
    grid_item.index = MsgAdapter.ReadInt()
    grid_item.item_id = MsgAdapter.ReadUShort()
    grid_item.reserve_sh = MsgAdapter.ReadShort()
    grid_item.num = MsgAdapter.ReadInt()
    grid_item.is_bind = MsgAdapter.ReadChar()
    grid_item.param1 = MsgAdapter.ReadChar()
    grid_item.param2 = MsgAdapter.ReadChar()
    grid_item.param3 = MsgAdapter.ReadChar()
    local equip_cfg = HolyDarkWeaponWGData.Instance:GetRelicEquipItemData(grid_item.item_id)
    grid_item.slot = 0
    grid_item.relic_seq_list = {}
    if not IsEmptyTable(equip_cfg) then
        grid_item.slot = equip_cfg.slot
        local relic_seq_list = Split(equip_cfg.relic_seq,"|")
        for k,v in pairs(relic_seq_list) do
            grid_item.relic_seq_list[k] = tonumber(v)
        end
    else
        if grid_item.item_id > 0 then
            print_error("item_id在圣器配置没有==",grid_item.item_id)
        end
    end

    grid_item.color = GameEnum.ITEM_COLOR_WHITE
    if grid_item.item_id > 0 then
        local cfg_data = ItemWGData.Instance:GetItemConfig(grid_item.item_id)
        if not IsEmptyTable(cfg_data) and cfg_data.color then
            grid_item.color = cfg_data.color
        end
    end

    return grid_item
end

SCRelicLightBagInfo = SCRelicLightBagInfo or BaseClass(BaseProtocolStruct) --圣器 - 背包信息
function SCRelicLightBagInfo:__init()
    self.msg_type = 15689
end

function SCRelicLightBagInfo:Decode()
    local count = MsgAdapter.ReadInt()
    self.relic_light_grid_list = {}
    for i = 1, count do
        local bag_data = GetScRelicBagGridItem()
        self.relic_light_grid_list[bag_data.index] = bag_data
    end
end

SCRelicLightBagChangeInfo = SCRelicLightBagChangeInfo or BaseClass(BaseProtocolStruct)-- 圣器 - 背包信息变化
function SCRelicLightBagChangeInfo:__init()
    self.msg_type = 15690
end

function SCRelicLightBagChangeInfo:Decode()
    self.change_info = GetScRelicBagGridItem()
end

SCRelicDarkBagInfo = SCRelicDarkBagInfo or BaseClass(BaseProtocolStruct) --暗器 - 背包信息
function SCRelicDarkBagInfo:__init()
    self.msg_type = 15691
end

function SCRelicDarkBagInfo:Decode()
    local count = MsgAdapter.ReadInt()
    self.relic_dark_grid_list = {}
    for i = 1, count do
        local bag_data = GetScRelicBagGridItem()
        self.relic_dark_grid_list[bag_data.index] = bag_data
    end
end

SCRelicDarkBagChangeInfo = SCRelicDarkBagChangeInfo or BaseClass(BaseProtocolStruct)-- 暗器 - 背包信息变化
function SCRelicDarkBagChangeInfo:__init()
    self.msg_type = 15692
end

function SCRelicDarkBagChangeInfo:Decode()
    self.change_info = GetScRelicBagGridItem()
end

-------------------------------------------养龙寺----------------------------------------------
CSCrossYangLongOperate = CSCrossYangLongOperate or BaseClass(BaseProtocolStruct)
function CSCrossYangLongOperate:__init()
	self.msg_type = 15693
end

function CSCrossYangLongOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
    MsgAdapter.WriteInt(self.param1)
    MsgAdapter.WriteInt(self.param2)
    MsgAdapter.WriteInt(self.param3)
end

local function GetMsgTeamInfo()
    local data = {}
    data.jieyi_id = MsgAdapter.ReadInt()
    data.max_hurt_uuid = MsgAdapter.ReadUUID()
    data.name = MsgAdapter.ReadStrN(32)
    data.total_hurt = MsgAdapter.ReadLL()
    data.rank = MsgAdapter.ReadInt()

    return data
end 

SCCrossYangLongTeamRankInfo = SCCrossYangLongTeamRankInfo or BaseClass(BaseProtocolStruct)
function SCCrossYangLongTeamRankInfo:__init()
    self.msg_type = 15695
end

function SCCrossYangLongTeamRankInfo:Decode()
    self.boss_seq = MsgAdapter.ReadInt()
    self.count = MsgAdapter.ReadInt()

    local team_info_list = {}
    for i = 1, self.count do
        team_info_list[i] = GetMsgTeamInfo()
    end

    self.team_info_list = team_info_list
end

SCCrossYangLongRewardInfo = SCCrossYangLongRewardInfo or BaseClass(BaseProtocolStruct)
function SCCrossYangLongRewardInfo:__init()
    self.msg_type = 15696
end

function SCCrossYangLongRewardInfo:Decode()
    local reward_item_list = {}
    for i = 0, 29 do
        reward_item_list[i] = {
            boss_seq = MsgAdapter.ReadShort(),
            rank = MsgAdapter.ReadShort(),
            index = i,  --0 开始 用于领取奖励定位
        }
    end

    self.reward_item_list = reward_item_list
end

local function GetBossItemData()
    local data = {}
    data.boss_seq = MsgAdapter.ReadInt()
    data.obj_id = MsgAdapter.ReadUShort()  
    data.active = data.obj_id ~= 65535   --65535无效值 后端说值不是65535 就标识boss存活
    data.short = MsgAdapter.ReadShort()
    
    return data.boss_seq, data
end

SCCrossYangLongSceneInfo = SCCrossYangLongSceneInfo or BaseClass(BaseProtocolStruct)
function SCCrossYangLongSceneInfo:__init()
    self.msg_type = 15697
end

function SCCrossYangLongSceneInfo:Decode()
    self.count = MsgAdapter.ReadInt()

    local boss_info_list = {}
    for i = 1, self.count do
        local seq, data = GetBossItemData()
        boss_info_list[seq] = data
    end

    self.boss_info_list = boss_info_list
end

------------------------------------------御灵--------------------------------------------
CSEquipYuLingOperate = CSEquipYuLingOperate or BaseClass(BaseProtocolStruct)
function CSEquipYuLingOperate:__init()
	self.msg_type = 15698
end

function CSEquipYuLingOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
    MsgAdapter.WriteInt(self.param1)
    MsgAdapter.WriteInt(self.param2)
    MsgAdapter.WriteInt(self.param3)
end

local function GetMsgEquipYuLingHoleItem()
    local data = {}
    data.is_unlock = MsgAdapter.ReadChar()
    data.reverch = MsgAdapter.ReadChar()
    data.forge_times = MsgAdapter.ReadUShort()
    data.add_per = MsgAdapter.ReadInt()

    return data
end

local function GetMsgEquipYuLingPartItem()
    local hole_item_list = {}
    for i = 0, 9 do
        hole_item_list[i] = GetMsgEquipYuLingHoleItem()
    end

    return hole_item_list
end

SCEquipYuLingPartInfo = SCEquipYuLingPartInfo or BaseClass(BaseProtocolStruct)
function SCEquipYuLingPartInfo:__init()
       self.msg_type = 15699
end

function SCEquipYuLingPartInfo:Decode()
    local part_item_list = {}
    for i = 0, 9 do
        part_item_list[i] = GetMsgEquipYuLingPartItem()
        part_item_list[i].equip_id = i
    end

    self.part_item_list = part_item_list
end

-----------------------------------------梦灵--------------------------------------------
local function GetMsgBagGrid()
	local grid_item = {}
	grid_item.index = MsgAdapter.ReadInt()
	grid_item.item_id = MsgAdapter.ReadUShort()
	grid_item.reserve_sh = MsgAdapter.ReadShort()
	grid_item.num = MsgAdapter.ReadInt()
	grid_item.is_bind = MsgAdapter.ReadChar()
	grid_item.param1 = MsgAdapter.ReadChar()
	grid_item.param2 = MsgAdapter.ReadChar()
	grid_item.param3 = MsgAdapter.ReadChar()

	return grid_item
end

SCMengLingBag = SCMengLingBag or BaseClass(BaseProtocolStruct)

function SCMengLingBag:__init()
    self.msg_type = 15604
end

function SCMengLingBag:Decode()
	self.grid_count = MsgAdapter.ReadInt()
    local grid_list = {}

	for i = 1, self.grid_count do
        local grid_item = GetMsgBagGrid()
		grid_list[grid_item.index] = grid_item
    end

    self.grid_list = grid_list
end

SCMengLingBagUpdate = SCMengLingBagUpdate or BaseClass(BaseProtocolStruct)

function SCMengLingBagUpdate:__init()
    self.msg_type = 15605
end

function SCMengLingBagUpdate:Decode()
    self.grid_info = GetMsgBagGrid()
end

CSDreamSpiritOperate = CSDreamSpiritOperate or BaseClass(BaseProtocolStruct)
function CSDreamSpiritOperate:__init()
	self.msg_type = 15600
end

function CSDreamSpiritOperate:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.operate_type)
    MsgAdapter.WriteInt(self.param1)
    MsgAdapter.WriteInt(self.param2)
    MsgAdapter.WriteInt(self.param3)
    MsgAdapter.WriteInt(self.param4)
end

SCDreamSpiritInfo = SCDreamSpiritInfo or BaseClass(BaseProtocolStruct)
function SCDreamSpiritInfo:__init()
    self.msg_type = 15601
end

function SCDreamSpiritInfo:Decode()
    self.score = MsgAdapter.ReadLL()          -- 梦灵升级使用的积分

    local stone_list = {}   --属性石头等级
    for i = 0, 3 do
        stone_list[i] = MsgAdapter.ReadInt()
    end
    self.stone_list = stone_list
end

local function GetMsgDreamSpiritHole(seq, slot, hole, item_id)
    local data = {}
    data.item_id = MsgAdapter.ReadUShort()
    data.level = MsgAdapter.ReadUShort()
    data.hole = hole
    data.seq = seq
    data.slot = slot
    data.equip_id = item_id
    return data
end

local function GetMsgDreamSpiritSlot(seq, slot)
    local data = {}
    data.seq = seq
    data.slot = slot
    data.item_id = MsgAdapter.ReadUShort()
    data.level = MsgAdapter.ReadUShort()

    return data
end

local function GetMsgDreamSpiritItem(seq)
    local data = {}

    for i = 0, 10 do
        data[i] = GetMsgDreamSpiritSlot(seq, i)
    end

    return data
end

SCDreamSpiritItemInfo = SCDreamSpiritItemInfo or BaseClass(BaseProtocolStruct)    -- 20套grade
function SCDreamSpiritItemInfo:__init()
    self.msg_type = 15602
end

function SCDreamSpiritItemInfo:Decode()
    local mengling_suit_item_list = {}

    for i = 0, 19 do
        mengling_suit_item_list[i] = GetMsgDreamSpiritItem(i)
    end

    self.mengling_suit_item_list = mengling_suit_item_list
end


SCDreamSpiritItemUpdate = SCDreamSpiritItemUpdate or BaseClass(BaseProtocolStruct)   -- 单更grade套
function SCDreamSpiritItemUpdate:__init()
    self.msg_type = 15603
end

function SCDreamSpiritItemUpdate:Decode()
    self.mengling_seq = MsgAdapter.ReadInt()
    self.mengling_suit_item = GetMsgDreamSpiritItem(self.mengling_seq)
end

CSDreamSpiritEquipDecompos = CSDreamSpiritEquipDecompos or BaseClass(BaseProtocolStruct)
function CSDreamSpiritEquipDecompos:__init()
	self.msg_type = 15606
end

function CSDreamSpiritEquipDecompos:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.count)

	for i = 1, self.count do
		MsgAdapter.WriteUShort(self.msg_item[i].item_id or 0)
		MsgAdapter.WriteShort(self.msg_item[i].bag_index or -1)
		MsgAdapter.WriteInt(self.msg_item[i].count or 0)
	end
end

CSDreamSpiritEquipCompos = CSDreamSpiritEquipCompos or BaseClass(BaseProtocolStruct)
function CSDreamSpiritEquipCompos:__init()
	self.msg_type = 15607
end

function CSDreamSpiritEquipCompos:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteUShort(self.item_id)
    MsgAdapter.WriteShort(self.count)

    for i = 1, self.count do
        MsgAdapter.WriteUShort(self.msg_item[i].item_id or 0)
		MsgAdapter.WriteShort(self.msg_item[i].bag_index or -1)
		MsgAdapter.WriteInt(self.msg_item[i].count or 0)
    end
end

CSDreamSpiritEquip = CSDreamSpiritEquip or BaseClass(BaseProtocolStruct)

function CSDreamSpiritEquip:__init()
	self.msg_type = 15608
end

function CSDreamSpiritEquip:Encode()
	MsgAdapter.WriteBegin(self.msg_type)
	MsgAdapter.WriteInt(self.seq)
    MsgAdapter.WriteInt(self.count)

    for i = 1, self.count do
        MsgAdapter.WriteUShort(self.msg_item[i].item_id or 0)
		MsgAdapter.WriteShort(self.msg_item[i].bag_index or -1)
		MsgAdapter.WriteInt(self.msg_item[i].slot or 0)
    end
end