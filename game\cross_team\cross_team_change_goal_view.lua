
-- 选择跨服组队目标
CrossTeamChangeGoalView = CrossTeamChangeGoalView or BaseClass(SafeBaseView)
function CrossTeamChangeGoalView:__init()
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -4), sizeDelta = Vector2(1100, 590)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_change_goal")
	self:SetMaskBg(true)
	self.select_index = 0
	self.min_level = 0
	self.max_level = 0
	self.list_view = {}
end

function CrossTeamChangeGoalView:__delete()
	self.list_view = {}
end

function CrossTeamChangeGoalView:ReleaseCallBack()
	if self.radio_btn then
		self.radio_btn:DeleteMe()
		self.radio_btn = nil
	end
	for k, v in pairs(self.list_view) do
		v:DeleteMe()
	end
    self.is_pingtai = false
end

function CrossTeamChangeGoalView:CloseCallBack()
end

function CrossTeamChangeGoalView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleChangeGoal
	XUI.AddClickEventListener(self.node_list["btn_confirm"], BindTool.Bind1(self.OnClickConfirm, self))
	XUI.AddClickEventListener(self.node_list["btn_sub"], BindTool.Bind2(self.OnClickSub, self, 1))
	XUI.AddClickEventListener(self.node_list["btn_add"], BindTool.Bind2(self.OnClickAdd, self, 1))
	XUI.AddClickEventListener(self.node_list["btn_sub1"], BindTool.Bind2(self.OnClickSub, self, 2))
	XUI.AddClickEventListener(self.node_list["btn_add1"], BindTool.Bind2(self.OnClickAdd, self, 2))

	self.node_list["slider"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnMinLevelValueChange, self))
	self.node_list["slider_1"].slider.onValueChanged:AddListener(BindTool.Bind(self.OnMaxLevelValueChange, self))

end

function CrossTeamChangeGoalView:ShowIndexCallBack()
	self:OnClickSelectGoal()
end

function CrossTeamChangeGoalView:SetIsPingTai(is_pingtai)
	self.is_pingtai = is_pingtai
end


function CrossTeamChangeGoalView:OnClickSub(index)
	local role_min_level, role_max_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
	if self.is_pingtai then
		role_min_level, role_max_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
    end
	local level_dis = self:GetLevelDis()
	if 1 == index then
		self.min_level = self.min_level - 1 <= role_min_level and role_min_level or self.min_level - 1
		self.node_list["slider"].slider.value = (self.min_level - role_min_level) / level_dis
	else
		self.max_level = self.max_level - 1 <= role_min_level and role_min_level or self.max_level - 1
		self.node_list["slider_1"].slider.value = (self.max_level - role_min_level) / level_dis
	end
end

function CrossTeamChangeGoalView:OnClickAdd(index)
	local role_min_level, role_max_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
	if self.is_pingtai then
		role_min_level, role_max_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
	end
	local level_dis = self:GetLevelDis()
	if 1 == index then
		self.min_level = self.min_level + 1 >= role_max_level and role_max_level or self.min_level + 1
		self.node_list["slider"].slider.value = (self.min_level - role_min_level) / level_dis
	else
		self.max_level = self.max_level + 1 >= role_max_level and role_max_level or self.max_level + 1
		self.node_list["slider_1"].slider.value = (self.max_level - role_min_level) / level_dis
	end
end

function CrossTeamChangeGoalView:OnMinLevelValueChange(value)
	local role_min_level, role_max_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
	if self.is_pingtai then
		role_min_level, role_max_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
	end
	local num = role_min_level + value * (role_max_level - role_min_level)
	self.min_level = num == 0 and role_min_level or num
	self.min_level = GameMath.Round(self.min_level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.min_level)
	self.node_list["lbl_role_level_min"].text.text = role_level
	self.node_list.feixian_image_min:SetActive(is_vis)
end

function CrossTeamChangeGoalView:OnMaxLevelValueChange(value)
	local role_min_level, role_max_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
	if self.is_pingtai then
		role_min_level, role_max_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
	end
	local num = role_min_level + value * (role_max_level - role_min_level)
	self.max_level = num == 0 and role_min_level or num
	self.max_level = GameMath.Round(self.max_level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.max_level)
	self.node_list["lbl_role_level_max"].text.text = role_level
	self.node_list.feixian_image_max:SetActive(is_vis)
end


function CrossTeamChangeGoalView:OnClickConfirm()
	if self.min_level <= self.max_level then
		if self.is_pingtai then
			CrossTeamWGData.Instance:SetPtTeamLimitLevel(self.min_level, self.max_level)
            self:Close()
            ViewManager.Instance:FlushView(GuideModuleName.CrossTeamView)
		else
			if 1 == CrossTeamWGData.Instance:GetIsInTeam() then
				CrossTeamWGCtrl.Instance:SendChangeTeamLimit(0, self.min_level, self.max_level)
			end
			self:Close()
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(string.format(Language.CrossTeam.Tips1))
	end
end

function CrossTeamChangeGoalView:GetLevelDis()
    local min_limit_level, max_limit_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
    return max_limit_level - min_limit_level
end

function CrossTeamChangeGoalView:OnClickSelectGoal()
	local role_min_level,role_max_level = CrossTeamWGData.Instance:GetTeamLimitLevel()
	local min_limit_level, max_limit_level = CrossTeamWGData.Instance:GetDefTeamLimitLevel()
	local level_dis = self:GetLevelDis()

	--local
	if self.is_pingtai then
		role_min_level, role_max_level = CrossTeamWGData.Instance:GetPtTeamLimitLevel()
	end

	self.node_list["slider"].slider.value = (role_min_level -  min_limit_level)/ level_dis
    self.node_list["slider_1"].slider.value = (role_max_level -  min_limit_level)/ level_dis
  
	self.min_level = role_min_level
    self.max_level = role_max_level
    local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(role_min_level)

	self.node_list["lbl_role_level_min"].text.text = role_level
	self.node_list.feixian_image_min:SetActive(is_vis)
	is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(role_max_level)
	self.node_list["lbl_role_level_max"].text.text = role_level
	self.node_list.feixian_image_max:SetActive(is_vis)
end

function CrossTeamChangeGoalView:GetLevelLimit()
	return self.min_level, self.max_level
end
