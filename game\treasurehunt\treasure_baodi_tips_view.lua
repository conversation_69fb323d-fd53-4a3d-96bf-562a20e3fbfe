--
TreasureBaodiTips = TreasureBaodiTips or BaseClass(SafeBaseView)

local ITEM_HEIGHT = 112

function TreasureBaodiTips:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "TreasureBaodiTips"
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_treasure_baodi_tips")
    self:SetMaskBg(true, true)
end

function TreasureBaodiTips:ReleaseCallBack()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    if self.ph_list then
        self.ph_list:DeleteMe()
        self.ph_list = nil
    end
end

function TreasureBaodiTips:LoadCallBack()
    self.is_first_open = false

    self.item_list = AsyncListView.New(ItemCell, self.node_list.ph_item_list)
    self.item_list:SetStartZeroIndex(true)

    if not self.ph_list then
		self.ph_list = AsyncListView.New(TreasureBaodiRender, self.node_list.ph_list)
        self.ph_list:SetStartZeroIndex(true)
	end

    XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind(self.Close, self))
end

function TreasureBaodiTips:ShowIndexCallBack()
    self.is_first_open = true
end


function TreasureBaodiTips:OnFlush()
    self:FlushItemList()

    local type = TreasureHuntWGCtrl.Instance:GetTreasureType()
    local pool_level = TreasureHuntWGData.Instance:GetPoolLevelByType(type)
    self.node_list.pool_level_text.text.text = string.format(Language.TreasureHunt.TreasurePoolLevel, pool_level)

    local total_times = TreasureHuntWGData.Instance:GetTreasureTotalTimes(type)
    self.node_list.baodi_des_txt.text.text = string.format(Language.TreasureHunt.TotalTreasureTimes, total_times)

    --local max_cfg = TreasureHuntWGData.Instance:GetTreasureMaxCfg(type)
    local cur_cfg = TreasureHuntWGData.Instance:GetXunBaoPoolLevelCfg(type, pool_level)
    self.item_list:SetDataList(cur_cfg.reward_item)

    -- local last_cfg = TreasureHuntWGData.Instance:GetXunBaoPoolLevelCfg(type, max_cfg.pool_level - 1)
    -- -- self.node_list.text_count.text.text = string.format(Language.TreasureHunt.TreasureLevelRange, last_cfg and last_cfg.uplevel_count or 0 , max_cfg.uplevel_count) 
    -- self.node_list.text_count.text.text = last_cfg.uplevel_count .. "+"

    local next_cfg = TreasureHuntWGData.Instance:GetXunBaoPoolLevelCfg(type, pool_level + 1)
    local start_count, end_count = TreasureHuntWGData.Instance:GetXunBaoPoolLevelCountCfg(type, pool_level)

    if IsEmptyTable(next_cfg) then
        self.node_list.text_count.text.text = start_count .. "+"
    else
        self.node_list.text_count.text.text = string.format(Language.TreasureHunt.TreasureLevelRange, start_count , end_count)
    end
end

function TreasureBaodiTips:FlushItemList()
    local type = TreasureHuntWGCtrl.Instance:GetTreasureType()
    local data_list = TreasureHuntWGData.Instance:GetXunBaoPoolLevelCfgBuType(type)
    local pool_level = TreasureHuntWGData.Instance:GetPoolLevelByType(type)
    self.ph_list:SetDataList(data_list)

    if self.is_first_open then
        self.is_first_open = false
        local target_height = ITEM_HEIGHT * (pool_level + 1)
        local node = self.node_list.ph_list:FindObj("Container")
        local list_height = self.node_list.ph_list.rect.rect.height
        local container_height = node.rect.rect.height
        local max_height = container_height - list_height
        if max_height > 0 then
            target_height = target_height <= max_height and target_height or max_height
        end

        local start_pos_y = node.rect.anchoredPosition.y
        UITween.CleanAllMoveToShowPanel(self.view_name)
	    UITween.MoveToShowPanel(self.view_name, node, Vector2(0, start_pos_y), Vector2(0, target_height), 0.1, DG.Tweening.Ease.Linear)
    end
end

------------------------------
TreasureBaodiRender = TreasureBaodiRender or BaseClass(BaseRender)

function TreasureBaodiRender:LoadCallBack()
    if not self.item_list then
        self.item_list = AsyncListView.New(ItemCell, self.node_list.ph_item_list)
        self.item_list:SetStartZeroIndex(true)
    end
end

function TreasureBaodiRender:__delete()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function TreasureBaodiRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self.item_list:SetDataList(self.data.reward_item)
    self.node_list.text_level.text.text = string.format(Language.TreasureHunt.TreasurePoolLevel, self.data.pool_level)
    local type = TreasureHuntWGCtrl.Instance:GetTreasureType()
    local next_cfg = TreasureHuntWGData.Instance:GetXunBaoPoolLevelCfg(type, self.data.pool_level + 1)
    local start_count, end_count = TreasureHuntWGData.Instance:GetXunBaoPoolLevelCountCfg(type, self.data.pool_level)

    if IsEmptyTable(next_cfg) then
        self.node_list.text_count.text.text = start_count .. "+"
    else
        self.node_list.text_count.text.text = string.format(Language.TreasureHunt.TreasureLevelRange, start_count , end_count) 
    end
end

