function MarryView:InitBaoXiaView()
	if not self.init_baoxia_view then
		self.insurance_alert = MarryAlert.New(nil, nil, nil, nil, false)

		XUI.AddClickEventListener(self.node_list["btn_baoxia_tips"], BindTool.Bind1(self.OpenBaoXiaTips, self))
		XUI.AddClickEventListener(self.node_list["btn_buy_get"], BindTool.Bind1(self.OnClickGetBuyReward, self))
		XUI.AddClickEventListener(self.node_list["btn_day_get"], BindTool.Bind1(self.OnClickGetDayReward, self))
		XUI.AddClickEventListener(self.node_list["btn_buytolover"], BindTool.Bind1(self.OnClickBuyToLover, self))
		XUI.AddClickEventListener(self.node_list["btn_loverreward"], BindTool.Bind1(self.OnClickGetLoverDayReward, self))
		self.init_baoxia_view = true
		self.lovel_box_state = nil

		-- local flush_time = MarryWGData.Instance:GetMarryBaoXiaFlushTime()
		-- self.node_list["rich_baoxia_tips"].text.text = string.format(Language.Marry.MyBaoXiaHint,flush_time)
	end
	if not MarryWGData.Instance.is_remind_red and RoleWGData.Instance:GetDayFirstLoginFlag() then
		RemindManager.Instance:Fire(RemindName.Marry_BaoXia)
		MarryWGData.Instance.is_remind_red = true
	end
	self:SetTongGuanShow()
end

function MarryView:DeleteBaoXiaView()
	if nil ~= self.baoxia_cell_list then
		for k,v in pairs(self.baoxia_cell_list) do
			v:DeleteMe()
		end
		self.baoxia_cell_list = nil
	end
	if nil ~= self.tongguan_cell_list then
		for k,v in pairs(self.tongguan_cell_list) do
			v:DeleteMe()
		end
		self.tongguan_cell_list = nil
	end

	if nil ~= self.baoxia_role then
	 	self.baoxia_role:DeleteMe()
	 	self.baoxia_role = nil
	end

	if nil ~= self.insurance_alert then
	 	self.insurance_alert:DeleteMe()
	 	self.insurance_alert = nil
	end

	if self.model_baoxia then
		self.model_baoxia:DeleteMe()
		self.model_baoxia = nil
	end

    self.set_baoxia_resid = false
	self.lovel_box_state = nil
	self.init_baoxia_view = false
	self.cur_day = nil --记录进入时候的天数
end

function MarryView:SetItemShow(day)
	if nil ~= self.cur_day and self.cur_day == day then
		return
	end
	local reward_list =  MarryWGData.Instance:GetBaoXiaRewardInfo(day)
	if nil == self.baoxia_cell_list then
		self.baoxia_cell_list = {}
		for i = 0, 1 do
			self.baoxia_cell_list[i] = ItemCell.New(self.node_list["ph_day_cell"..i])
			if reward_list[i] then
				self.baoxia_cell_list[i]:SetData(reward_list[i])
			end
		end
	end
end

function MarryView:SetTongGuanShow()
	if nil == self.tongguan_cell_list then
		self.tongguan_cell_list = {}
		for i = 0, 1 do
			self.tongguan_cell_list[i] = ItemCell.New(self.node_list["ph_kaitong_cell"..i])
		end
	end
	local bind_gold,bind_gold_id,reward_item = MarryWGData.Instance:GetBaoXiaRewardBindGold()
	self.tongguan_cell_list[0]:SetData({item_id = bind_gold_id,num = bind_gold})
	self.tongguan_cell_list[1]:SetData(reward_item[0])
end

--创建人物
function MarryView:SetBaoXiaRole()
	if nil == self.model_baoxia then
		self.model_baoxia = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["ph_baoxia_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.S,
			can_drag = true,
		}
		
		self.model_baoxia:SetRenderTexUI3DModel(display_data)
		-- self.model_baoxia:SetUI3DModel(self.node_list["ph_baoxia_display"].transform, self.node_list["ph_baoxia_display"].event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.model_baoxia)
	end

	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local role_sex = RoleWGData.Instance.role_vo.sex
	if lover_id > 0 then
		self.node_list["lbl_role_name"].text.text = RoleWGData.Instance.role_vo.lover_name
		self.node_list["img_baoxia_man_role"]:SetActive(false)
		self.node_list["img_baoxia_lady_role"]:SetActive(false)
		self.node_list["ph_baoxia_display"]:SetActive(true)
		BrowseWGCtrl.Instance:BrowRoelInfo(lover_id, BindTool.Bind1(self.BaoXiaVoAck, self))
	else
		if nil ~= self.model_baoxia then
			self.node_list["ph_baoxia_display"]:SetActive(false)
		end

		if role_sex == GameEnum.MALE then
			self.node_list["img_baoxia_man_role"]:SetActive(false)
			self.node_list["img_baoxia_lady_role"]:SetActive(true)
		else
			self.node_list["img_baoxia_man_role"]:SetActive(true)
			self.node_list["img_baoxia_lady_role"]:SetActive(false)
		end
		if self.model_baoxia then
			self.model_baoxia:SetVisible(false)
		end

		self.node_list["lbl_role_name"].text.text = Language.Marry.NoLover
	end
end

function MarryView:BaoXiaVoAck(protocol)
	if not protocol then
		return
    end
    if self.model_baoxia then
        self.model_baoxia:SetVisible(true)
    end

	MarryWGData.Instance:SetLoverInfo2(protocol)
	local role_base_info_ack = MarryWGData.Instance:GetLoverInfo2()
    local resource = MarryWGData.Instance:GetMarryOtherCfg().wedding_dress_show
    local res_id, _, __ = AppearanceWGData.GetFashionBodyResIdByResViewId(resource)
	local d_face_res = role_base_info_ack.appearance.default_face_res_id
    local d_hair_res = role_base_info_ack.appearance.default_hair_res_id
    local d_body_res = role_base_info_ack.appearance.default_body_res_id
    if nil ~= self.model_baoxia and not self.set_baoxia_resid then
		
		local extra_role_model_data = {
			prof = role_base_info_ack.prof,
			sex = role_base_info_ack.sex,
			d_face_res = d_face_res,
			d_hair_res = d_hair_res,
			d_body_res = d_body_res,
		}
        self.model_baoxia:SetRoleResid(res_id, nil, extra_role_model_data) --策划需求，显示结婚时装，而不是伴侣当前时装
		self.model_baoxia:SetWeaponModelFakeRemove()
	end

end

function MarryView:OpenBaoXiaTips()
	MarryView.OpenTips(Language.Marry.BaoXiaTips, Language.Marry.BaoXiaTipsTitle)
end

function MarryView:FlushBaoXiaView()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local love_box_bug = MarryWGData.Instance:GetLoveboxBuyRet() -- 自己
	local love_box_lover = MarryWGData.Instance:GetLoveboxBuyLoverRet()  -- 对方
	local buy_fetch = MarryWGData.Instance:GetLoveboxBuyFetchReward()
	local day_fetch = MarryWGData.Instance:GetLoveboxDayFetchReward()
	local day_lover_fetch = MarryWGData.Instance:GetLoveboxLoverDayFetchReward()
	local buy_times = MarryWGData.Instance:GetLoveboxBuyTimeRet()
	local buylover_times = MarryWGData.Instance:GetLoveboxBuyLoverTimeRet()
	local flush_time = MarryWGData.Instance:GetMarryBaoXiaFlushTime()

    if lover_id > 0 then
        self.node_list.btn_loverreward:SetActive(true)
        --self.node_list.no_lover_txt:SetActive(false)
        self.node_list["rich_buylover_passdays"].text.text = Language.Marry.BuyBaoXiaTip
        local color1 = love_box_lover > 0 and "#ffffff" or "#f2e6e6"
        if love_box_lover > 0 then
			local day =  math.ceil(buylover_times/86400) > 30 and 30 or math.ceil(buylover_times/86400)
			self.node_list["rich_buylover_passdays"].text.text = ToColorStr(string.format(Language.Marry.BuyLoverPassDays, 31 - day), COLOR3B.L_ORANGE)
            local str = day_lover_fetch == 0 and Language.Marry.MarryBntLq1 or string.format(Language.Marry.TomorrowZeroFlush,flush_time)
            --color1 =  day_lover_fetch == 0 and "#ffffff" or "#f2e6e6"
			self.node_list["btn_loverreward_text"].text.text = str
		else
			self.node_list["rich_buylover_passdays"].text.text = Language.Marry.BuyBaoXiaTip
			self.node_list["btn_loverreward_text"].text.text = Language.Marry.LoverNoBuy
		end
    else
        self.node_list.btn_loverreward:SetActive(false)
        --self.node_list.no_lover_txt:SetActive(true)
		--self.node_list["btn_loverreward_text"].text.text = Language.Marry.TempNoLover
		self.node_list["rich_buylover_passdays"].text.text = Language.Marry.BuyBaoXiaTip
	end
	if love_box_bug > 0 then
		local cur_day = math.ceil(buy_times/86400) > 30 and 30 or math.ceil(buy_times/86400)
		self.node_list["rich_buy_passdays"].text.text = ToColorStr(string.format(Language.Marry.BuyPassDays, 31 - cur_day))
		--配表是从0开始的
		self:SetItemShow(cur_day - 1)

	else
		self.node_list["rich_buy_passdays"].text.text = ""
		--没购买的话就默认展示第一天
		self:SetItemShow(0)

	end

	local flush_time = MarryWGData.Instance:GetMarryBaoXiaFlushTime()
	local str_desc = love_box_lover > 0 and Language.Marry.BaoXiaDes or Language.Marry.BaoXiaDes2
	local str1 = string.format(Language.Marry.MyBaoXiaHint,flush_time)
	self.node_list["lbl_baoxia_desc"].text.text = str_desc .. str1
	--self.node_list["buy_state"]:SetActive(not (love_box_lover > 0))

    --local color = buy_fetch == 0 and love_box_bug == 1 and "#ffffff" or "#f2e6e6"
	if buy_fetch == 0 then
		self.node_list["btn_buy_get_text"].text.text = Language.Marry.LingQu
	else
		self.node_list["btn_buy_get_text"].text.text = Language.Marry.YiLingQu 
	end
	if day_fetch == 0 then
		self.node_list["btn_day_get_text"].text.text = Language.Marry.LingQu
	else
		self.node_list["btn_day_get_text"].text.text = string.format(Language.Marry.TomorrowZeroFlush,flush_time)
	end

	if 1 ~= love_box_bug then
		self.node_list["btn_buy_get_text"].text.text = Language.Marry.NoBuy
		self.node_list["btn_day_get_text"].text.text = Language.Marry.NoBuy
	end

	if love_box_bug == 1 and 30 * 86400 > buy_times then
		if self.lovel_box_state == true then
			self:PlayBaoXiaUiEffect()
			self.lovel_box_state = false
		end
		self.node_list["btn_buytolover_text"].emoji_text.text = Language.Common.AlreadyPurchase
	else
		if nil == self.lovel_box_state then
			self.lovel_box_state = true
		end
		local need_gold = MarryWGData.Instance:GetBaoXiaInfo()
		self.node_list["btn_buytolover_text"].emoji_text.text = string.format(Language.Marry.GiveBuy, need_gold)
	end
    --local img_ = buy_fetch == 0 and love_box_bug == 1 and "jh_ann" or "jh_ann2"
    --self.node_list["btn_buy_get"].image:LoadSprite(ResPath.GetJieHunImg(img_))
    --local img_1 = day_fetch == 0 and love_box_bug == 1 and "jh_ann" or "jh_ann2"
    --self.node_list["btn_day_get"].image:LoadSprite(ResPath.GetJieHunImg(img_1))
	XUI.SetButtonEnabled(self.node_list["btn_buy_get"], buy_fetch == 0 and love_box_bug == 1)
	XUI.SetButtonEnabled(self.node_list["btn_day_get"], day_fetch == 0 and love_box_bug == 1)

	self.node_list.btn_buy_get_remind:SetActive(buy_fetch == 0 and love_box_bug == 1)
	self.node_list.btn_day_get_remind:SetActive(day_fetch == 0 and love_box_bug == 1)
	self.node_list.btn_loverreward_remind:SetActive(day_lover_fetch == 0 and love_box_lover == 1)

	--XUI.SetGraphicGrey(self.node_list["btn_buytolover"],lover_id <= 0 and love_box_bug == 0)
    --XUI.SetButtonEnabled(self.node_list["btn_loverreward"], love_box_lover == 1 and day_lover_fetch == 0)
    --local img_str1 = lover_id <= 0 and love_box_bug == 0
	--XUI.SetButtonEnabled(self.node_list["btn_buytolover"], img_str1)
    local img_str = love_box_lover == 1 and day_lover_fetch == 0
	XUI.SetButtonEnabled(self.node_list["btn_loverreward"], img_str)
	self.node_list["btn_lingqu_reward_parent"]:SetActive(love_box_bug == 1)
	self.node_list["btn_buytolover"]:SetActive(love_box_bug ~= 1)
	-- self.node_list["btn_buytolover_effect"]:SetActive(love_box_bug ~= 1 and lover_id > 0)
end

-- 领取每日礼包
function MarryView:OnClickGetDayReward()
	-- MarryWGCtrl.Instance:SendLoveboxFetchReward(self.get_everyday.type)
	MarryWGCtrl.Instance:SendLoveboxFetchReward(QINGYUAN_LOVEBOX_REWARD_TYPE.QINGYUAN_LOVEBOX_REWARD_TYPE_SELF_DAILY)
end

-- 领取购买礼包
function MarryView:OnClickGetBuyReward()
	-- MarryWGCtrl.Instance:SendLoveboxFetchReward(self.get_bug.type)
	MarryWGCtrl.Instance:SendLoveboxFetchReward(QINGYUAN_LOVEBOX_REWARD_TYPE.QINGYUAN_LOVEBOX_REWARD_TYPE_OPEN)
end

-- 领取伴侣的礼包
function MarryView:OnClickGetLoverDayReward()
	MarryWGCtrl.Instance:SendLoveboxFetchReward(QINGYUAN_LOVEBOX_REWARD_TYPE.QINGYUAN_LOVEBOX_REWARD_TYPE_LOVER_DAILY)
end


-- 购买宝匣
function MarryView:OnClickBuyToLover()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local love_box_bug = MarryWGData.Instance:GetLoveboxBuyRet() -- 自己
	if lover_id <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.NoMarryBuyBaoXia)
		return
	elseif love_box_bug > 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.AllreadyBuyBaoXia)
		return
	end
	local need_gold = MarryWGData.Instance:GetBaoXiaInfo()
	local str = string.format(Language.Marry.BuyBaoXia, need_gold)
	self.insurance_alert:SetLableString(str)
	self.insurance_alert:SetOkFunc(BindTool.Bind1(self.SetBuyBaoXiaAlert, self))
	self.insurance_alert:Open()
end

function MarryView:SetBuyBaoXiaAlert(num)
	MarryWGCtrl.Instance:SendLoveboxBuyReq()
end

function MarryView:OnClickRequestBuy()
	MarryWGCtrl.Instance:SendLoveboxBuyReq(1)
end

--播放购买成功特效
function MarryView:PlayBaoXiaUiEffect()
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_goumai, is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["Effect_baoxia"]})
	--local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_goumaichenggong)
	--EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["Effect_baoxia"].transform)
end
