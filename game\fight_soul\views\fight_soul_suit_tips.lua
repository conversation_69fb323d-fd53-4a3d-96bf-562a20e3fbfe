
FightSoulSuitTips = FightSoulSuitTips or BaseClass(SafeBaseView)
function FightSoulSuitTips:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_fight_soul_suit_tips")
end

function FightSoulSuitTips:__delete()

end

function FightSoulSuitTips:ReleaseCallBack()
	if self.suit_list then
		for k, v in pairs(self.suit_list) do
			if v.loaded_flag then
				v.cell:DeleteMe()
			end
		end
		self.suit_list = nil
	end

    self.suit_data = nil
end

function FightSoulSuitTips:LoadCallBack()
    self.suit_list = {}
end

function FightSoulSuitTips:SetDataAndOpen(suit_data)
    self.suit_data = suit_data
    self:Open()
end

function FightSoulSuitTips:OnFlush()
    if IsEmptyTable(self.suit_data) then
    	return
    end

    local show_list = {}
    local suit_cell_path = "uis/view/fight_soul_ui_prefab"
	local suit_cell_res = "fight_soul_suit_render"
	for i, v in ipairs(self.suit_data) do
        show_list[i] = true
		if self.suit_list[i] and self.suit_list[i].loaded_flag then
			self.suit_list[i].cell:SetData(v)
		else
            self.suit_list[i] = {}
            self.suit_list[i].loaded_flag = false
			local async_loader = AllocAsyncLoader(self, "fs_suit_cell" .. i)
			async_loader:SetParent(self.node_list["attr_list"].transform)
			async_loader:Load(suit_cell_path, suit_cell_res, function (obj)
				local cell = FightSoulSuitRender.New(obj)
				cell:SetIndex(i)
				cell:SetData(v)
				self.suit_list[i].cell = cell
                self.suit_list[i].loaded_flag = true
			end)
		end
	end

    for k, v in pairs(self.suit_list) do
        if v.loaded_flag then
            v.cell:SetActive(show_list[k])
        end
    end
end

--------------------------------------------------------------------------------
FightSoulSuitRender = FightSoulSuitRender or BaseClass(BaseRender)
function FightSoulSuitRender:__init()
    self.attr_list = {}
    -- for i = 1, 3 do
    --     self.attr_list[i] = FightSoulSuitAttrRender.New(self.node_list["act_cell_" .. i])
    -- end
end

function FightSoulSuitRender:__delete()
	if self.attr_list ~= nil then
		for k,v in pairs(self.attr_list) do
			v:DeleteMe()
		end
		self.attr_list = nil
	end
end

function FightSoulSuitRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local bg_res = FightSoulWGData.Instance:GetBoneSuitFlagBgRes(self.data.suit_type)
	local bundle, asset = ResPath.GetFightSoulImg("suit_color_" .. bg_res)
	self.node_list.suit_icon.image:LoadSprite(bundle, asset)

	local suit_name = self.data.suit_name or ""
	self.node_list.suit_name1.text.text = suit_name
	self.node_list.suit_name2.text.text = string.format(Language.FightSoul.SuitDesc, suit_name)

	local act_list = self.data.act_list
    for k,v in pairs(self.attr_list) do
        v:SetData(act_list[k])
    end
end

--------------------------------------------------------------------------------
FightSoulSuitSkillRender = FightSoulSuitSkillRender or BaseClass(BaseRender)

function FightSoulSuitSkillRender:__init()
    
end

function FightSoulSuitSkillRender:__delete()
    
end

function FightSoulSuitSkillRender:LoadCallBack()
    for i=1,3 do
        XUI.AddClickEventListener(self.node_list["skill_item_" .. i], BindTool.Bind(self.OnClickSkillBtn, self, i))
    end
end

function FightSoulSuitSkillRender:OnFlush()
    local data = self:GetData()
    if IsEmptyTable(data) or IsEmptyTable(data.info) then
        return
    end

    -- 被动 主动 Icon
    local skill_cfg = data.info.tip_skill_cfg[1]
    local type_img_list = {"a3_sl_bq_bd", "a3_sl_bq_zd"}
    self.node_list.skill_type_icon.image:LoadSprite(ResPath.GetF2TipImages(type_img_list[skill_cfg.skill_type]))
    self.node_list.skill_type_icon.image:SetNativeSize()
    -- 品质效果
    self.node_list.pz_title.text.text = data.info.same_num_str or ""

    -- 技能Icon
    local skill_img = nil
    for i=1,3 do
        skill_cfg = data.info.tip_skill_cfg[i]
        if skill_cfg then
            skill_img = self.node_list["skill_icon_" .. i]
            skill_img.image:LoadSprite(ResPath.GetSkillIconById(data.info.tip_skill_cfg[i].skill_icon))
            self.node_list.skill_type_icon.image:SetNativeSize()
            XUI.SetGraphicGrey(skill_img, not data.info.is_act)
        end
        self.node_list["skill_item_" .. i]:SetActive(skill_cfg ~= nil)
    end

    -- 技能等级
    for i=1,3 do
        self.node_list["level_" .. i].text.text = "lv." .. (data.info.skill_level or 0)
        self.node_list["level_bg_" .. i]:SetActive(data.info.is_act)
    end
end

function FightSoulSuitSkillRender:OnClickSkillBtn(index)
    local data = self:GetData()
    if IsEmptyTable(data) then
        return
    end

    FightSoulWGCtrl.Instance:OpenPassiveSkillTips(data.fs_type, data.suit_type, data.color, data.info.same_num, index)
end