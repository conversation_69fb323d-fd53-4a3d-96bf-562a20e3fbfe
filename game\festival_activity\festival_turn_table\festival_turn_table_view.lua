
FestivalActivityView = FestivalActivityView or BaseClass(SafeBaseView)

function FestivalActivityView:TurnTableReleaseCallBack()
	if self.turn_table_show_item then
		for k,v in pairs(self.turn_table_show_item) do
			v:DeleteMe()
		end
		self.turn_table_show_item = nil
	end

	if self.task_list then
		self.task_list:DeleteMe()
		self.task_list = nil
	end

	if self.turn_table_tween then
		self.turn_table_tween:Kill()
		self.turn_table_tween = nil
	end
	self:TurnTableCancleHLQuest()


	self.turn_table_is_turning = false
	if FestivalTurnTableWGData.Instance then
		FestivalTurnTableWGData.Instance:SetTurnTableIsTurning(self.turn_table_is_turning)
	end
end

function FestivalActivityView:TurnTableOpenCallBack()
	--self:SetRuleInfoActive(false)
end

function FestivalActivityView:TurnTableLoadCallBack()
	self:LoadTurnTableUi()
	self.turn_table_show_item = {}
	for i= 1, 8 do
		self.turn_table_show_item[i] = ItemCell.New(self.node_list["turn_table_item_pos"..i])
	end

	-- if nil == self.personal_record_list then
	-- 	self.personal_record_list = AsyncListView.New(FestivalTableListCell, self.node_list.turn_table_record_list2)
	-- end

	if nil == self.task_list then
		self.task_list = AsyncListView.New(FestivalTableTaskCell, self.node_list.turn_table_task_list)
	end

	self.node_list["turn_table_jump_ani"].button:AddClickListener(BindTool.Bind(self.TurnTableOnClickJump, self))
	self.node_list["turn_table_change"].button:AddClickListener(BindTool.Bind(self.TurnTableOnClickChangeRecord, self))
	self.node_list["turn_table_roll_btn"].button:AddClickListener(BindTool.Bind(self.TurnTableOnClickRoll, self))
	self.node_list["turn_table_record_btn"].button:AddClickListener(BindTool.Bind(self.TurnTableOnClickOpenRecord, self))

end

function FestivalActivityView:LoadTurnTableUi()
	local turn_table_zhuanpan_bundle, turn_table_zhuanpan_asset = ResPath.GetFestivalRawImages("hyzpzpd")
 	self.node_list["turn_table_zhuanpan"].raw_image:LoadSprite(turn_table_zhuanpan_bundle, turn_table_zhuanpan_asset, function ()
 		self.node_list["turn_table_zhuanpan"].raw_image:SetNativeSize()
  	end)

	local turn_table_title_bg_bundle, turn_table_title_bg_asset = ResPath.GetFestivalRawImages("xynddiban")
 	self.node_list["turn_table_title_bg"].raw_image:LoadSprite(turn_table_title_bg_bundle, turn_table_title_bg_asset, function ()
 		self.node_list["turn_table_title_bg"].raw_image:SetNativeSize()
  	end)

	local turn_table_list_bg_bundle, turn_table_list_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_di1")
 	self.node_list["turn_table_list_bg"].image:LoadSprite(turn_table_list_bg_bundle, turn_table_list_bg_asset)

	local turn_table_btn_text_bundle, turn_table_btn_text_asset = ResPath.GetFestivalActImages("a2_jrkh_zp2")
 	self.node_list["turn_table_btn_text"].image:LoadSprite(turn_table_btn_text_bundle, turn_table_btn_text_asset)
	
	local turn_table_change_img_qhdj_bundle, turn_table_change_img_asset = ResPath.GetFestivalActImages("a2_jrkh_qhdj")
 	self.node_list["turn_table_change_img"].image:LoadSprite(turn_table_change_img_qhdj_bundle, turn_table_change_img_asset)

	local turn_table_change_img_zp_bundle, turn_table_change_img_asset = ResPath.GetFestivalActImages("a2_jrkh_zp4")
 	self.node_list["turn_table_roll_pointer"].image:LoadSprite(turn_table_change_img_zp_bundle, turn_table_change_img_asset)

	local turn_table_change_img_tsan_bundle, turn_table_change_img_asset = ResPath.GetFestivalActImages("a2_jrkh_tsan")
 	self.node_list["turn_table_record_btn"].image:LoadSprite(turn_table_change_img_tsan_bundle, turn_table_change_img_asset)

	local turn_table_change_img_gth_bundle, turn_table_change_img_asset = ResPath.GetFestivalActImages("a2_jrkh_gth")
 	self.node_list["turn_table_desc_img"].image:LoadSprite(turn_table_change_img_gth_bundle, turn_table_change_img_asset)

	local turn_table_change_img_di_bundle, turn_table_change_img_asset = ResPath.GetFestivalActImages("a2_jrkh_tsdi2")
 	self.node_list["turn_table_list_top_bg"].image:LoadSprite(turn_table_change_img_di_bundle, turn_table_change_img_asset)

	local line_bg_bundle, line_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_btzs")
	for i = 1, 4 do
		self.node_list["line_" .. i].image:LoadSprite(line_bg_bundle, line_bg_asset)
	end

	local line_bundle, line_bg_asset = ResPath.GetFestivalActImages("a2_jrkh_zp3")
	for i = 1, 8 do
		self.node_list["turn_table_highlight" .. i].image:LoadSprite(line_bundle, line_bg_asset)
	end
end

function FestivalActivityView:TurnTableShowIndexCallBack()
	self:TurnTableViewAnimation()
	FestivalTrunTableWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_REQ_INFO)
end

function FestivalActivityView:TurnTableViewAnimation()
	-- local tween_info = UITween_CONSTS.ZhouYiCarnival
	-- local tween_root_1 = self.node_list.turn_table_content
	-- UITween.CleanAllTween(GuideModuleName.ZhouYiYunCheng)
	-- UITween.FakeHideShow(tween_root_1)
	-- --RectTransform.SetAnchoredPositionXY(self.node_list.turn_table_muban_l.rect, -900, -40)
	-- --RectTransform.SetAnchoredPositionXY(self.node_list.turn_table_muban_r.rect, 1000, -40)

	-- --self.node_list.turn_table_muban_l.rect:DOAnchorPos(Vector2(-406, -40), tween_info.MoveTime)
	-- --self.node_list.turn_table_muban_r.rect:DOAnchorPos(Vector2(473, -40), tween_info.MoveTime)

	-- ReDelayCall(self, function()
	-- 	UITween.AlphaShow(GuideModuleName.ZhouYiYunCheng,tween_root_1, 0, 1, tween_info.AlphaTime)
	-- end, tween_info.MoveTime, "zhou_yi_carnival_1")
end

function FestivalActivityView:TurnTableOnClickJump()
	FestivalTurnTableWGData.Instance:SetJumpAni()
	self:TurnTableFlushAniStatus()
end

function FestivalActivityView:TurnTableFlushAniStatus()
	self.turn_table_is_jump_ani = FestivalTurnTableWGData.Instance:GetJumpAni()
	self.node_list.turn_table_jump_toggle:SetActive(self.turn_table_is_jump_ani)
end

function FestivalActivityView:TurnTableOnClickChangeRecord()
	if self.turn_table_is_turning then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.IsWorkIng)
		return
	end
	FestivalTrunTableWGCtrl.Instance:OpenRewardSelectView()
end

function FestivalActivityView:TurnTableOnClickRoll()
	local had_times = FestivalTurnTableWGData.Instance:GetLeftRollTimes()
	if had_times <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.LackOfTimes)
		return
	end

	-- if not FestivalTurnTableWGData.Instance:IsAvalibaleTime() then
	-- 	TipWGCtrl.Instance:ShowSystemMsg(Language.ZhouYiYunCheng.TimeNotAvalibale)
	-- 	return
	-- end

	if self.turn_table_is_turning then return end
	self.turn_table_is_turning = true
	FestivalTurnTableWGData.Instance:SetTurnTableIsTurning(self.turn_table_is_turning)
	FestivalTrunTableWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_DRAW)
end

function FestivalActivityView:TurnTableOnClickOpenRecord()
	FestivalTrunTableWGCtrl.Instance:OpenRecord()
end

function FestivalActivityView:TurnTablePlayRollAnimal()
	local item_cfg,index = FestivalTurnTableWGData.Instance:GetRollResult()
	if index <= 0 then
		self.turn_table_is_turning = false
		FestivalTurnTableWGData.Instance:SetTurnTableIsTurning(self.turn_table_is_turning)
		return
	end
	local time = 5

	if self.turn_table_is_jump_ani then
		time = 0
	end

	self.turn_table_tween = self.node_list.turn_table_roll_father.transform:DORotate(Vector3(0, 0, -360 * time - 45 * (index - 1)),time,
		DG.Tweening.RotateMode.FastBeyond360)

	self.turn_table_tween:SetEase(DG.Tweening.Ease.OutQuart)
	self.turn_table_tween:OnComplete(function()
		self.turn_table_is_turning = false
		FestivalTurnTableWGData.Instance:SetTurnTableIsTurning(self.turn_table_is_turning)
		self:TurnTableShowGetItemView(item_cfg)
		self:TurnTableCancleHLQuest()
		self:TurnTableShowRollHightLight()
	end)

	self:TurnTableCancleHLQuest()

	if self.turn_table_is_jump_ani then
		return
	end

	self.show_hl_quest = GlobalTimerQuest:AddRunQuest(function ()
		self:TurnTableShowRollHightLight()
	end,0.02)
	self:TurnTableShowRollHightLight()
end


function FestivalActivityView:TurnTableCancleHLQuest()
	if self.show_hl_quest then
		GlobalTimerQuest:CancelQuest(self.show_hl_quest)
		self.show_hl_quest = nil
	end
end


function FestivalActivityView:TurnTableShowRollHightLight()
	local father_rotation = self.node_list.turn_table_roll_father.transform.localEulerAngles.z
	local index = (8 - math.floor((father_rotation + 22.5) / 45)) % 8 + 1
	for i =1,8 do
		self.node_list["turn_table_highlight"..i]:SetActive(i == index)
	end
end

function FestivalActivityView:TurnTableOnFlush()
	if FestivalTurnTableWGData.Instance:CheckNeedReqInfo() then return end
	local have_times,need_times = FestivalTurnTableWGData.Instance:GetLeftRollTimes()
	local boun_times = FestivalTurnTableWGData.Instance:GetBonusTimes()
	need_times = boun_times - need_times
	local str = ToColorStr(need_times,"#0f9c1cFF")
	local turn_color =  FestivalActivityWGData.Instance:GeTurnTableTextColor()
	self.node_list["turn_table_txt"].text.text = string.format(Language.FestivalTurnTableDesc.Remaining_times, turn_color)
	self.node_list["turn_table_times"].text.text = have_times
	self.node_list["turn_table_desc"].text.text = string.format(Language.FestivalTurnTableDesc.Need_times, turn_color, str)
	self.node_list["turn_table_title"].text.text = string.format(Language.FestivalTurnTableDesc.Turn_tile, turn_color)
	self:TurnTableFlushAniStatus()
	local all_rewared_list = FestivalTurnTableWGData.Instance:GetAllSelectItemList()
	if not IsEmptyTable(all_rewared_list) then
		for i = 1, 8 do
			if all_rewared_list[i] then
				local exchange_data = {}
				exchange_data.item_id = tonumber(all_rewared_list[i].item_id)
				exchange_data.num = tonumber(all_rewared_list[i].num)
				exchange_data.is_bind = tonumber(all_rewared_list[i].is_bind)
				self.turn_table_show_item[i]:SetData(exchange_data)
			end
		end
	end

	local view_cfg = FestivalTurnTableWGData.Instance:GetOtherCfg()
		if not view_cfg then
			return
		end

	self:SetRuleInfo(view_cfg.rule_desc, view_cfg.btn_rule_title)
	self:TurnTableFlushXianLingTask()
	-- if FestivalTurnTableWGData.Instance:GetLeftRollTimes() > 0 then --and FestivalTurnTableWGData.Instance:IsAvalibaleTime() then
	-- 	self.node_list["turn_table_effectpos"]:SetActive(true)
	-- else
	-- 	self.node_list["turn_table_effectpos"]:SetActive(false)
	-- end
end


function FestivalActivityView:TurnTableFlushXianLingTask()
	local all_task_data = FestivalTurnTableWGData.Instance:GetAllTaskDetail()
	self.task_list:SetDataList(all_task_data)
end

function FestivalActivityView:TurnTableShowGetItemView(cfg)
	-- print_error("ShowGetItemView",item_cfg)
	-- local item_cfg = ItemWGData.Instance:GetItemConfig(tonumber(cfg.item_id))
	-- local str = string.format(Language.Bag.GetItemTxt, ToColorStr(item_cfg.name, GET_TIP_ITEM_COLOR[item_cfg.color]), tomunber(item_cfg.num))
	-- TipWGCtrl.Instance:ShowSystemMsg(str)
end


FestivalTableListCell = FestivalTableListCell or BaseClass(BaseRender)

function FestivalTableListCell:__init()

end

function FestivalTableListCell:__delete()
end

function FestivalTableListCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if item_cfg == nil then
			return
		end
		self.node_list.bg:SetActive((self:GetIndex() % 2) == 1)

		local rewared_level = FestivalTurnTableWGData.Instance:GetRewaredLevel(self.data.item_id)
		local rewared_name = Language.ZhouYiYunCheng.ReWaredLevel[rewared_level]
		if self.data.draw_role then
			if rewared_name then
				self.node_list["contant"].tmp.text = self.data.draw_role..Language.ZhouYiYunCheng.Congratulations1..rewared_name..ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			else
				self.node_list["contant"].tmp.text = self.data.draw_role..Language.ZhouYiYunCheng.Congratulations1..ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
			end
		else
			local time_tab = os.date("*t", self.data.draw_timestamp)
			self.node_list["contant"].tmp.text = Language.ZhouYiYunCheng.Congratulations2..rewared_name..ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		end
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.contant.rect)
    	self.node_list.festival_turn_cell.rect.sizeDelta = Vector2(716, self.node_list.contant.rect.sizeDelta.y) 
	else
		self.node_list["contant"].tmp.text = ""
	end
end


FestivalTableTaskCell = FestivalTableTaskCell or BaseClass(BaseRender)

function FestivalTableTaskCell:__init()
    self.node_list["gobutton"].button:AddClickListener(BindTool.Bind(self.OnClickGo,self))

	self.node_list["gobutton"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_btn_huang"))
 	self.node_list["trun_table_cell_bg"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_lb"))
 	self.node_list["trun_table_desc_bg"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_lbbt"))
end

function FestivalTableTaskCell:__delete()
end

function FestivalTableTaskCell:OnFlush()
	if not IsEmptyTable(self.data) then
		local have_param = self.data.task_type_param
		local need_param = self.data.complete_param
		local color = have_param >= need_param and COLOR3B.L_GREEN or "#e5271DFF"
		local str = ""
		if need_param ~= 0 then
			str = "\n("..ToColorStr(have_param,color).."/"..need_param..")"
		end
		self.node_list["task_desc"].text.text = self.data.des..str

		self.node_list["have_get"]:SetActive(false)
		self.node_list.gobutton:SetActive(true)
		self.node_list["red"]:SetActive(false)
		if not self.data.finish_flag and not self.data.get_flag then
			self.node_list["btn_text"].text.text = Language.ZhouYiYunCheng.BtnText
		elseif not self.data.get_flag then
			self.node_list["btn_text"].text.text = Language.ZhouYiYunCheng.BtnText2
			self.node_list["red"]:SetActive(true)
		elseif self.data.get_flag then
			self.node_list["btn_text"].text.text = Language.ZhouYiYunCheng.BtnText3
			self.node_list["have_get"]:SetActive(true)
			self.node_list.gobutton:SetActive(false)
		end
	else

	end
end

function FestivalTableTaskCell:OnClickGo()
	if self.data.finish_flag and not self.data.get_flag then
		FestivalTrunTableWGCtrl.Instance:SendYunChengRollReq(RA_HAPPY_MONDAY_OP.RA_HAPPY_MONDAY_OP_FETCH_DRAW_TIMES,self.data.index,0,0,0)
		return
	elseif not self.data.finish_flag then
		if self.data.open_panel ~= "" then
			FunOpen.Instance:OpenViewNameByCfg(self.data.open_panel)
		end
	end
end
