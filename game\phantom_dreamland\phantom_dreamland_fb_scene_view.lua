--场景内view
PhantomDreamlandFBSceneView = PhantomDreamlandFBSceneView or BaseClass(SafeBaseView)

local WAVE_BUFF = {
	ADD_WAVE_TIME = 0,
	ADD_BUFF = 1,
	ADD_SKILL_HURT = 2,
	ADD_SKILL_POWER = 3,
}

function PhantomDreamlandFBSceneView:__init()
	self.view_layer = UiLayer.MainUI
	self.active_close = false
	self.is_safe_area_adapter = true
    self:AddViewResource(0, "uis/view/phantom_dreamland_ui_prefab", "layout_phantom_dreamland_fb_scene_view")
end

function PhantomDreamlandFBSceneView:OpenCallBack()
    if self.obj and self:IsLoadedIndex(0) then
		self.obj:SetActive(true)
	end
end

function PhantomDreamlandFBSceneView:LoadCallBack()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, BindTool.Bind(self.InitCallBack, self))

	if not self.item_list then
		self.item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
		self.item_list:SetStartZeroIndex(true)
	end

	if not self.select_buff_list then
		self.select_buff_list = {}

		for i = 1, 3 do
			self.select_buff_list[i] = PhantomDreamlandFBSelectBuff.New(self.node_list[string.format("select_buff_render_%d", i)])
			self.select_buff_list[i]:SetIndex(i)
			self.select_buff_list[i]:SetClickCallBack(BindTool.Bind1(self.SelectBuffIndex, self))
		end
	end

	XUI.AddClickEventListener(self.node_list.dreamland_skill, BindTool.Bind(self.OnClickUsePoserSkill, self))
	XUI.AddClickEventListener(self.node_list.attr_message_btn, BindTool.Bind(self.OnClickAttrMessage, self))
	XUI.AddClickEventListener(self.node_list.btn_add_times, BindTool.Bind(self.OnClickOpenAddTimes, self))

	self.alpha_tween = self.node_list.dreamland_times_tips:GetComponent(typeof(UGUITweenAlpha))
	self.shrinkbuttons_change = GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.ShrinkButtonsValueChange, self))
end

function PhantomDreamlandFBSceneView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if self.node_list.task_root then
		self.obj = self.node_list.task_root.gameObject
		self.obj.transform:SetParent(parent.gameObject.transform)
		self.obj.transform.localPosition = Vector3(0, 0, 0)
		self.obj.transform.localScale = Vector3.one
	end
	
	MainuiWGCtrl.Instance:AddBtnToGuajiLine(self.node_list.btn_add_times, 0)

    if self.is_out_fb then
        self.obj:SetActive(false)
    end

    self.is_out_fb = nil
end

function PhantomDreamlandFBSceneView:Close()
    SafeBaseView.Close(self)
	self.node_list["btn_add_times"].transform:SetParent(self.root_node_transform, false)
end

function PhantomDreamlandFBSceneView:CloseCallBack()
	if self.node_list and self.node_list.select_buff_root then
		self.node_list.select_buff_root:CustomSetActive(false)
	end

	self.is_out_fb = true
    if self.obj then
        self.obj:SetActive(false)
    end
end

function PhantomDreamlandFBSceneView:ReleaseCallBack()
	if self.shrinkbuttons_change then
		GlobalEventSystem:UnBind(self.shrinkbuttons_change)
		self.shrinkbuttons_change = nil
	end
	
	if self.obj then
        ResMgr:Destroy(self.obj)
        self.obj = nil
    end

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    if self.select_buff_list then
		for i, v in ipairs(self.select_buff_list) do
			if v then
				v:DeleteMe()
			end
		end

		self.select_buff_list = nil
    end

	self.card_list = nil
	self.total_use_time = nil
	self.is_start_count_down = nil
	self.old_wave = nil
	self.old_wave_end_time = nil
	self.alpha_tween = nil
	self:CleanTimeDown()
	self:StopTotalTimeCount()
end

function PhantomDreamlandFBSceneView:ShowIndexCallBack()
	if self.obj then
        self.obj:SetActive(true)
    end

	if self.addtimebtn_obj then
        self.addtimebtn_obj:SetActive(true)
    end
end

function PhantomDreamlandFBSceneView:ShrinkButtonsValueChange(isOn)
	local start_alpha = isOn and 1 or 0
	local end_alpha = isOn and 0 or 1
	self.node_list.fp_move_up_node.canvas_group:DoAlpha(start_alpha, end_alpha, 0.3)
	self.node_list.fp_move_up_node.canvas_group.blocksRaycasts = not isOn
end

function PhantomDreamlandFBSceneView:FlushEndTime()
	local room_end_time = CrossFlagGrabbingBattleFieldWGData.Instance:GetFGBRoomEndTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()

	if room_end_time > server_time then
		if CountDownManager.Instance:HasCountDown("fgb_room_time") then
			CountDownManager.Instance:RemoveCountDown("fgb_room_time")
		end

		CountDownManager.Instance:AddCountDown("fgb_room_time", 
				function (elapse_time, total_time)
					local valid_time = total_time - elapse_time

					if self.node_list.level_time then
						self.node_list.level_time.text.text = TimeUtil.FormatSecond(math.floor(valid_time), 2)
					end
				end, 
				function ()
					if self.node_list.act_time then
						self.node_list.act_time:CustomSetActive(false)
					end
				end, 
				room_end_time, 
				nil, 1)
	end
end

-- 选择buff
function PhantomDreamlandFBSceneView:SelectBuffIndex(render)
	self.node_list.select_buff_root:CustomSetActive(false)
	if render == nil or render.data == nil then
		return
	end

	local index = render:GetIndex()
	local server_index = index - 1
	PhantomDreamlandWGCtrl.Instance:RequestDreamlandChooseCard(render.data.wave, server_index)
end

function PhantomDreamlandFBSceneView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushWaveMessage()
			self:FlushSkillMessage()
			self:FlushCardMessage()
		elseif k == "flush_skill" then
			self:FlushSkillMessage()
		elseif k == "flush_wave" then
			self:FlushWaveMessage()
			self:FlushCardMessage()
		elseif k == "flush_card" then
			self:FlushCardMessage()
        end
    end
end

function PhantomDreamlandFBSceneView:FlushWaveMessage()
	local scene_info = PhantomDreamlandWGData.Instance:GetDreamlandSceneInfo()
	if not scene_info then
		return
	end

	local wave = scene_info.wave or 0
	local wave_end_time = scene_info.wave_end_time or 0
	local level = scene_info.level or 0

	local cfg = PhantomDreamlandWGData.Instance:GetWaveCfgByWave(wave)
	if not cfg then
		return
	end

	local monster_cfg = PhantomDreamlandWGData.Instance:GetMonsterCfgByWave(wave)
	local monster_total_num = #monster_cfg
	self.node_list["cur_wave_enemy"].text.text = string.format("%s/%s", scene_info.monster_count, monster_total_num)
	self.item_list:SetDataList(cfg.pass_reward_item)
	self.node_list["cur_wave_times"].text.text = string.format(Language.PhantomDreamland.FbWaveTips, wave)

	local total_wave_num = PhantomDreamlandWGData.Instance:GetTotalWaveNum()
	self.node_list["dreamland_times_wave"].text.text = string.format(Language.PhantomDreamland.FbWaveTips2, wave, total_wave_num)
	self.node_list["txt_harm_value"].text.text = string.format(Language.PhantomDreamland.HarmValue, scene_info.harm_value)

	-- 累计用时
	if scene_info.is_end == 1 then
		self:StopTotalTimeCount()
	else
		if not self.is_start_count_down then
			self.is_start_count_down = true
			self:StartTotalTimeCount()
		end
	end

	-- 战斗开始倒计时
	if self.old_wave ~= wave then
		local wave_interval = PhantomDreamlandWGData.Instance:GetBaseCfg().beast_time
		wave_interval = wave == 1 and wave_interval + 2 or wave_interval -- 第一波倒计时加两秒
		local count_down_time = TimeWGCtrl.Instance:GetServerTime() + wave_interval
		UiInstanceMgr.Instance:ShowFBStartDown4(cfg.is_show_boss == 1, count_down_time, function ()
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
		end)
		self.old_wave = wave
	end

	-- 刷新波数倒计时
	if self.old_wave_end_time ~= wave_end_time then
		self:FlushTimeCountDown(wave_end_time)
		if self.old_wave_end_time ~= nil and scene_info.info_type ~= 0 then
			local add_time = wave_end_time - self.old_wave_end_time
			if add_time > 0 then
				local add_str = TimeUtil.FormatSecond2MS(add_time)
				self.node_list.dreamland_times_tips.canvas_group.alpha = 1
				self.node_list.dreamland_times_tips:CustomSetActive(true)
				self.node_list.dreamland_times_tips.text.text = string.format("+%s", add_str)
				if self.alpha_tween then
					self.alpha_tween:ResetToBeginning()
				end
			end
		end
		self.old_wave_end_time = wave_end_time
	end
end

-- 累计用时计时
function PhantomDreamlandFBSceneView:StartTotalTimeCount()
	CountDownManager.Instance:AddCountDown("dreamland_total_time_countdown",
		BindTool.Bind(self.UpdateTotalUseTime, self), nil, nil, 999999, 1)
end

function PhantomDreamlandFBSceneView:StopTotalTimeCount()
	if CountDownManager.Instance:HasCountDown("dreamland_total_time_countdown") then
		CountDownManager.Instance:RemoveCountDown("dreamland_total_time_countdown")
	end
end

function PhantomDreamlandFBSceneView:UpdateTotalUseTime()
	if self.node_list and self.node_list["total_used_time"] then
		self.total_use_time = self.total_use_time or 0
		self.total_use_time = self.total_use_time + 1
		local time_str = TimeUtil.FormatSecond2MS(self.total_use_time)
		self.node_list["total_used_time"].text.text = time_str
	end
end

-- 刷新进度和能量
function PhantomDreamlandFBSceneView:FlushSkillMessage()
	local scene_info = PhantomDreamlandWGData.Instance:GetDreamlandSceneInfo()
	if not scene_info then
		return
	end

	local lv_cfg = PhantomDreamlandWGData.Instance:GetLevelCfg()
	if not lv_cfg then
		return
	end

	local need_skill_power = lv_cfg.need_skill_power or 1
	local skill_power = PhantomDreamlandWGData.Instance:GetDreamlandSkillPower()
	self.node_list.skill_slider.slider.value = skill_power / need_skill_power
	self.node_list.skill_progress.text.text = string.format("%s/%s", skill_power, need_skill_power)
	local skill_id = lv_cfg.skill_id or 0
	local icon_id = SkillWGData.Instance:GetSkillIconId(skill_id)
	local bundle, name = ResPath.GetSkillIconById(icon_id)
	self.node_list.dreamland_skill.image:LoadSprite(bundle, name)
end

-- 刷新卡组
function PhantomDreamlandFBSceneView:FlushCardMessage()
	local scene_info = PhantomDreamlandWGData.Instance:GetDreamlandSceneInfo()
	local card_list = PhantomDreamlandWGData.Instance:GetAllCardInfo()

	if (not scene_info) or (not card_list) then
		return
	end

	local wave = scene_info.wave or 0
	if wave <= 1 then	--第二波的选取的属于第一波通关的数据，第一关没有卡牌选取
		return
	end

	local lv_cfg = PhantomDreamlandWGData.Instance:GetLevelCfg()
	if not lv_cfg then
		return
	end

	local now_wave = wave - 1	-- 位上一波选取卡牌buff
	local card_data = nil
	local card_wave = 0
	for i = 1, now_wave do
		if card_list[i] and card_list[i].choose_seq == -1 then
			card_data = card_list[i]
			card_wave = i
			break
		end
	end

	if card_data ~= nil then	-- 组装三个数据
		self.node_list.select_buff_root:CustomSetActive(true)
		for i, v in ipairs(card_data.random_seq_list) do
			if self.select_buff_list[i] then
				local data = {}
				data.wave = card_wave
				data.card_pool = lv_cfg.card_pool or 0
				data.choose_seq = v.seq
				data.count = v.count
				self.select_buff_list[i]:SetData(data)
			end
		end
	end

end

function PhantomDreamlandFBSceneView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("dreamland_fb_scene_down") then
		CountDownManager.Instance:RemoveCountDown("dreamland_fb_scene_down")
	end
end

-- 倒计时
function PhantomDreamlandFBSceneView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanTimeDown()
		self:UpdateTimeCountDown(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		CountDownManager.Instance:AddCountDown("dreamland_fb_scene_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	end
end

function PhantomDreamlandFBSceneView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		self:UpdateTimeCountDown(valid_time)
	end
end

function PhantomDreamlandFBSceneView:OnComplete()
	self:CleanTimeDown()
end

function PhantomDreamlandFBSceneView:UpdateTimeCountDown(time)
	if self.node_list and self.node_list.dreamland_times then
		local time_str = ""
		if time > 0 then
			time_str = TimeUtil.FormatSecondDHM4(time)
			time_str = ToColorStr(time_str, COLOR3B.GREEN)
			--str = string.format(Language.OfflineRest.ExperienceFbSceneDesc, time_str)
		end 

		self.node_list.dreamland_times.text.text = time_str
	end
end

-- 使用技能
function PhantomDreamlandFBSceneView:OnClickUsePoserSkill()
	local scene_info = PhantomDreamlandWGData.Instance:GetDreamlandSceneInfo()
	if not scene_info then
		return
	end

	local lv_cfg = PhantomDreamlandWGData.Instance:GetLevelCfg()
	if not lv_cfg then
		return
	end

	local need_skill_power = lv_cfg.need_skill_power or 1
	local skill_power = PhantomDreamlandWGData.Instance:GetDreamlandSkillPower()
	if skill_power >= need_skill_power then
		PhantomDreamlandWGCtrl.Instance:RequestDreamlandUseSkill()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.OfflineRest.ExperienceFbUseSkillError)
	end
end

-- 所有加成属性
function PhantomDreamlandFBSceneView:OnClickAttrMessage()
	PhantomDreamlandWGCtrl.Instance:OpenDreamlandFbCardTipsView()
end

-- 打开购买时间
function PhantomDreamlandFBSceneView:OnClickOpenAddTimes()
	PhantomDreamlandWGCtrl.Instance:OpenDreamlandFbAddTimesView()
end

-------------------------------------PWSceneHurtItemCellRender---------------------------------
PhantomDreamlandFBSelectBuff = PhantomDreamlandFBSelectBuff or BaseClass(BaseRender)

function PhantomDreamlandFBSelectBuff:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local cfg = PhantomDreamlandWGData.Instance:GetCardCfgByIndexSeq(self.data.card_pool, self.data.choose_seq)

	if not cfg then
		return
	end	

	local bundle, asset = ResPath.GetRawImagesPNG("a3_zjm_buff_di_" .. cfg.color)
	self.node_list["buff_bg"].raw_image:LoadSprite(bundle, asset)

	--local str = string.format("%s\n", cfg.name)
	local desc = ""
	if cfg.type == WAVE_BUFF.ADD_WAVE_TIME then
		desc = string.format(cfg.desc, cfg.param1)
	elseif cfg.type == WAVE_BUFF.ADD_BUFF then
		desc = cfg.desc
	elseif cfg.type == WAVE_BUFF.ADD_SKILL_HURT then
		desc = string.format(cfg.desc, math.floor(cfg.param1 / 100))
	elseif cfg.type == WAVE_BUFF.ADD_SKILL_POWER then
		desc = string.format(cfg.desc, cfg.param1)
	end

	--str = string.format("%s%s", str, desc)
	self.node_list["buff_name"].text.text = cfg.name
	self.node_list["buff_desc"].text.text = desc

	local count_str = ""
	if self.data.count > 0 then
		count_str = string.format(Language.PhantomDreamland.SelectTimes, self.data.count)
	end
	self.node_list["buff_select_times"].text.text = count_str
	
	local sprite_str = string.format("a3_ll_card_icon_0%d", cfg.icon)
	self.node_list.buff_icon.image:LoadSprite(ResPath.GetOfflinerestImg(sprite_str))
end