MainUIFPAdvanceNotice = MainUIFPAdvanceNotice or BaseClass(BaseRender)

function MainUIFPAdvanceNotice:__init()
    self.cell_list = {}
end

function MainUIFPAdvanceNotice:__delete()
    self:CancelQuest()
    if self.cell_list then
        for k, v in pairs(self.cell_list) do
            v:DeleteMe()
        end
        self.cell_list = nil
    end

    self.page_toggle_list = nil
    self.old_data = nil
end

function MainUIFPAdvanceNotice:LoadCallBack()
    self.old_data = nil
    local list_delegate = self.node_list["adnt_list"].list_simple_delegate
	list_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
	list_delegate.CellRefreshDel = BindTool.Bind(self.RefreshCell, self)

    self.node_list["adnt_list"].event_trigger_listener:AddBeginDragListener(BindTool.Bind(self.OnBeginDrag, self))
    self.node_list["adnt_list"].event_trigger_listener:AddEndDragListener(BindTool.Bind(self.OnEndDrag, self))

    self.page_toggle_list = {}
    local node_num = self.node_list.adnt_page_point_root.transform.childCount
    for i = 1, node_num do
        self.page_toggle_list[i] = self.node_list.adnt_page_point_root:FindObj("page_point_" .. i)
    end

    local cfg = ConfigManager.Instance:GetAutoConfig("face_book_auto").other[1]
    self.change_time = cfg and cfg.change_time or 2
end

--表白框信息
function MainUIFPAdvanceNotice:GetNumberOfCells()
	return self.data and #self.data or 0
end

function MainUIFPAdvanceNotice:RefreshCell(cell, cell_index)
	local adnt_cell = self.cell_list[cell]
	if adnt_cell == nil then
		adnt_cell = MainUIFPAdvanceNoticeCell.New(cell.gameObject)
		self.cell_list[cell] = adnt_cell
	end

    local index = cell_index + 1
    adnt_cell:SetIndex(index)
	adnt_cell:SetData(self.data[index])
end

function MainUIFPAdvanceNotice:JumpPage(page)
	if not self.node_list["adnt_list"]
    or not self.node_list["adnt_list"].scroller
    or not self.node_list["adnt_list"].scroller.isActiveAndEnabled then
		return
	end

    if self.node_list["adnt_list"].list_page_scroll then
	    self.node_list["adnt_list"].list_page_scroll:JumpToPage(page)
    end
end

function MainUIFPAdvanceNotice:GetNowPage()
    if self.node_list["adnt_list"] and self.node_list["adnt_list"].list_page_scroll then
	    return self.node_list["adnt_list"].list_page_scroll:GetNowPage()
    end

    return 0
end

function MainUIFPAdvanceNotice:OnFlush()
    if self.data == nil then
        return
    end

	if not self.node_list["adnt_list"]
    or not self.node_list["adnt_list"].scroller
    or not self.node_list["adnt_list"].gameObject.activeInHierarchy then
		return
	end

    local page_num = self:GetNumberOfCells()
    -- 判断数据新旧
    if self.old_data and #self.old_data == page_num then
        local is_diff_data = false
        for k, v in ipairs(self.data) do
            if v.type ~= self.old_data[k].type then
                is_diff_data = true
                break
            end
        end

        if not is_diff_data then
            return
        end
    end

    self:CancelQuest()
    for k, v in ipairs(self.page_toggle_list) do
        v:SetActive(k <= page_num)
    end

    self.node_list["adnt_list"].list_page_scroll:SetPageCount(page_num)

    if self.old_data == nil then
        self.node_list["adnt_list"].scroller:ReloadData(0)
    elseif #self.old_data == page_num then
        self.node_list["adnt_list"].scroller:RefreshActiveCellViews()
    else
        self.node_list["adnt_list"].scroller:RefreshAndReloadActiveCellViews(true)
        self:JumpPage(0)
    end

    self.old_data = self.data
	self:StartQuest()
end

function MainUIFPAdvanceNotice:CancelQuest()
    if self.notice_timer_quest then
		GlobalTimerQuest:CancelQuest(self.notice_timer_quest)
		self.notice_timer_quest = nil
	end
end

function MainUIFPAdvanceNotice:StartQuest()
    if not self.notice_timer_quest then
		self.notice_timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.NoticeLoopShow, self), self.change_time)
	end
end

function MainUIFPAdvanceNotice:OnBeginDrag()
    self:CancelQuest()
end

function MainUIFPAdvanceNotice:OnEndDrag()
    self:StartQuest()
end

function MainUIFPAdvanceNotice:NoticeLoopShow()
    local cur_page = self:GetNowPage()
    local max_page = self:GetNumberOfCells() - 1
    local jump_page = cur_page < max_page and cur_page + 1 or 0
    self:JumpPage(jump_page)
end

MainUIFPAdvanceNoticeCell = MainUIFPAdvanceNoticeCell or BaseClass(BaseRender)
function MainUIFPAdvanceNoticeCell:LoadCallBack()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_display"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    XUI.AddClickEventListener(self.node_list.bg, BindTool.Bind(self.OnClickJump, self))
end

function MainUIFPAdvanceNoticeCell:__delete()
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end
end

function MainUIFPAdvanceNoticeCell:OnFlush()
    if self.data then
        self.node_list.bg.image:LoadSprite(ResPath.GetNoPackPNG("a3_zjm_xct_" .. self.data.image_id))
        self.node_list.image:SetActive(self.data.image_id == 6) --烟雨阁

        if self.data.model_show_type and self.data.model_show_type ~= "" then
            self.node_list.model_display:SetActive(true)
            self:FlushModel()
        else
            self.node_list.model_display:SetActive(false)
        end
    end
end

function MainUIFPAdvanceNoticeCell:FlushModel()
	if IsEmptyTable(self.data) then
		return
	end

	local display_data = {}
	if self.data.model_show_itemid ~= 0 and self.data.model_show_itemid ~= "" then
		local split_list = string.split(self.data.model_show_itemid, "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = self.data.model_show_itemid
		end
	end
	display_data.bundle_name = self.data["model_bundle_name"]
    display_data.asset_name = self.data["model_asset_name"]
    local model_show_type = tonumber(self.data["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.hide_model_block = true
    display_data.can_drag = false
    display_data.skip_rest_action = true
    display_data.model_rt_type = ModelRTSCaleType.PS_XXS

    local pos_x, pos_y, pos_z = 0, 0, 0
	if self.data.display_pos and self.data ~= "" then
		local pos_list = string.split(self.data.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
        pos_z = tonumber(pos_list[3]) or 0
        display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
	end

    if self.data.rotation and self.data.rotation ~= "" then
		local rotation_tab = string.split(self.data.rotation,"|")
		display_data.model_adjust_root_local_rotation = Vector3(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end

	if self.data.display_scale and self.data.display_scale ~= "" then
		display_data.model_adjust_root_local_scale = self.data.display_scale
	end

    self.model_display:ActModelPlayLastAction()
    self.model_display:SetData(display_data)
end

function MainUIFPAdvanceNoticeCell:OnClickJump()
    if self.data and self.data.open_param ~= "" then
        -- if self.data.type == 10 then
        --     -- 一元活动特殊处理，首次打开时，打开我的券包
        --     if BillionSubsidyWGData.Instance:GetIsFetchFirstOpenViewReward() == 0 then
        --         FunOpen.Instance:OpenViewNameByCfg(GuideModuleName.BillionSubsidy)
        --         return
        --     end
        -- end
        -- FunOpen.Instance:OpenViewNameByCfg(self.data.open_param)
        local t = Split(self.data.open_param, "#")
        local view_name = t[1]
        local is_first_open = BillionSubsidyWGData.Instance:GetIsFirstOpenView()
        if view_name == GuideModuleName.BillionSubsidy and is_first_open == 0 then
            FunOpen.Instance:OpenViewByName(GuideModuleName.BillionSubsidy, TabIndex.billion_subsidy_vip)
        else
            FunOpen.Instance:OpenViewNameByCfg(self.data.open_param)
        end
    end
end