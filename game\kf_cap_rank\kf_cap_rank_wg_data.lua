KFCapRankWGData = KFCapRankWGData or BaseClass()

function KFCapRankWGData:__init()
	if KFCapRankWGData.Instance then 
		ErrorLog("[KFCapRankWGData] Attemp to create a singleton twice !")
	end

	KFCapRankWGData.Instance = self

    local cfg = ConfigManager.Instance:GetAutoConfig("cross_capability_rank_auto")
    self.act_cfg = cfg.type
	self.act_reward_cfg = ListToMapList(cfg.reward, "grade")
    self.other_cfg = cfg.other[1]
end

function KFCapRankWGData:__delete()
	KFCapRankWGData.Instance = nil
end

function KFCapRankWGData:GetActOtherCfg()
    return self.other_cfg or {}
end

function KFCapRankWGData:GetActTypeCfg(act_type)
    return self.act_cfg[act_type] or {}
end

function KFCapRankWGData:GetActRewardCfg(type)
	return self.act_reward_cfg[type] or {}
end

function KFCapRankWGData:SetCurActRewardType(reward_type)
	self.cur_reward_type = reward_type
end

function KFCapRankWGData:GetCurActRewardType()
	return self.cur_reward_type or 0
end

function KFCapRankWGData:GetMyRankValue()
	return self.my_rank_value or nil
end

function KFCapRankWGData:GetKFCapRankType()
	local cfg_type = self:GetActTypeCfg(ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_KF_CAP_RANK)
	return cfg_type.rank_type or 0
end

function KFCapRankWGData:CreatNoRankItemData(nSectionIndex)
	return {rank = nSectionIndex, rank_value = 0}
end

function KFCapRankWGData:ExpandRankData(index)
	local rank_item = {}
	rank_item.no_true_rank = true
	rank_item.index = index
	rank_item.rank_data = self:CreatNoRankItemData(index)
	return rank_item 
end

function KFCapRankWGData:SetSwornRankSort(protocol)
	if protocol.self_rank_value then
		self.my_rank_value = protocol.self_rank_value
	end

	if protocol.total_capability then
		self.total_capability = protocol.total_capability
	end

	self.rank_list = {}
	local cur_reward_type = self:GetCurActRewardType()
	local cfg = self:GetActRewardCfg(cur_reward_type)
	local rank_cfg = cfg[#cfg]
	local max_rank = rank_cfg.max_rank
	for i = 1, max_rank do
		local rank_item = {}
		if protocol.rank_item_list[i] then
			rank_item.no_true_rank = false
			rank_item.index = i
			rank_item.rank_data = protocol.rank_item_list[i]
		else
			rank_item = self:ExpandRankData(i)
		end
		self.rank_list[i] = rank_item
	end
end

function KFCapRankWGData:GetRankInfo()
	return self.rank_list
end

function KFCapRankWGData:GetRankItemData(rank_zhanli)
	local cur_reward_type = self:GetCurActRewardType()
	local rank_cfg = self:GetActRewardCfg(cur_reward_type)
	local other_cfg = self:GetActOtherCfg()
	local suit_data_list = WardrobeWGData.Instance:GetSuitSingtonData(other_cfg.suit_ids)
	local real_act_num  = suit_data_list and suit_data_list.real_act_num or 0
	if not IsEmptyTable(rank_cfg) then
		for k ,v in ipairs(rank_cfg) do
			local suit_num_enough = real_act_num >= v.minimum_active_count
			if rank_zhanli >= v.reach_value and suit_num_enough then
				return k, v
			end
		end
	end

	return nil
end


function KFCapRankWGData:GetMyRankInfo()
	local rank_list = self:GetRankInfo()
	if rank_list == nil then
		return nil
	end
    
	local my_uid = RoleWGData.Instance:GetUUid()
	for k, v in pairs(rank_list) do
		if v.rank_data and v.rank_data.uuid then
			if v.rank_data.uuid == my_uid then
				return v
			end
		end
	end

	return nil
end

function KFCapRankWGData:GetMyCapRank()
	local all_cap = self.total_capability or 0
	local other_cfg = self:GetActOtherCfg()
	if all_cap >= other_cfg.minimum_capability then
		return true
	end

	return false
end

function KFCapRankWGData:GetMyTotalCap()
	return self.total_capability or 0 
end