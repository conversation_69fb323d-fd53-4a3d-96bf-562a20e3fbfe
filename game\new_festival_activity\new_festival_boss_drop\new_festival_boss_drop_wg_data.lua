NewFestivalBossDropWGData = NewFestivalBossDropWGData or BaseClass()

function NewFestivalBossDropWGData:__init()
	if NewFestivalBossDropWGData.Instance then
		error("[NewFestivalBossDropWGData] Attempt to create singleton twice!")
		return
	end

    NewFestivalBossDropWGData.Instance = self

    self:InitParam()
    self:InitConfig()
end

function NewFestivalBossDropWGData:__delete()
    NewFestivalBossDropWGData.Instance = nil
end

function NewFestivalBossDropWGData:InitParam()
    self.drop_count = 0
end

function NewFestivalBossDropWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("more_dropping_auto")
    self.other_cfg = cfg.other[1]
end

-- 协议相关start
function NewFestivalBossDropWGData:SetNewJRBossDropAllInfo(protocol)
    self.drop_count = protocol.drop_count
end

function NewFestivalBossDropWGData:GetDropCount()
    return self.drop_count
end

-- 协议相关end

function NewFestivalBossDropWGData:GetBossDropOtherCfg()
    return self.other_cfg
end