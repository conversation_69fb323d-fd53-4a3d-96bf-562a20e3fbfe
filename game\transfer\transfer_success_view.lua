TransferSuccessView = TransferSuccessView or BaseClass(SafeBaseView)
function TransferSuccessView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Tips)
	self:SetMaskBg(true, true)
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(598, 420)})
    self:AddViewResource(0, "uis/view/tansfer_ui_prefab", "layout_transfer_success")
end

function TransferSuccessView:LoadCallBack()
	

    XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind(self.Close, self))
	-- XUI.AddClickEventListener(self.node_list["btn_OK"], BindTool.Bind(self.OnClickBtnOK, self))

    if not self.reward_item_list then
		self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_scroll)
        -- self.reward_item_list:SetStartZeroIndex(true)
	end

    self.god_and_demons_type = TransFerWGData.Instance:GetGodAndDemonsType()

	if self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS then
		self:ChangeRes()
	end

    self.node_list.spine_root_god:SetActive(self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.GOD)
	self.node_list.spine_root_demons:SetActive(self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS)

    self.node_list.desc_bg_god:SetActive(self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.GOD)
    self.node_list.desc_bg_demons:SetActive(self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.DEMONS)


end

function TransferSuccessView:ReleaseCallBack()
    if self.tween_sequence then
		self.tween_sequence:Kill()
		self.tween_sequence = nil
	end

    if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end




function TransferSuccessView:SetProf(prof)
    self.prof = prof
end

function TransferSuccessView:ChangeRes()


	-- 背景
	local bundle2, asset2 = ResPath.GetF2RawImagesPNG("a3_zztpcg_cm_bg_1")
	self.node_list["img_bg"].raw_image:LoadSprite(bundle2, asset2)

    -- 标题
    bundle2, asset2 = ResPath.GetF2RawImagesPNG("a3_zztpcg_cs_bt_1")
	self.node_list["img_title"].raw_image:LoadSprite(bundle2, asset2)

    -- 描述底
    -- bundle2, asset2 = ResPath.GetF2RawImagesPNG("a3_zztpcg_cs_db_1")
	-- self.node_list["raw_image_1"].raw_image:LoadSprite(bundle2, asset2)
	-- self.node_list["raw_image_2"].raw_image:LoadSprite(bundle2, asset2)
	-- self.node_list["raw_image_3"].raw_image:LoadSprite(bundle2, asset2)

    -- 图标
    for i = 1, 3 do
        bundle2, asset2 = ResPath.GetTransFer(string.format("a3_zztpcg_cm_icon%s_1",i))
        self.node_list["img_icon_"..i].image:LoadSprite(bundle2, asset2)
    end
    
    bundle2, asset2 = ResPath.GetTransFer("a3_zztpcg_cm_db2_1")
	self.node_list["reward_bg"].image:LoadSprite(bundle2, asset2)
end


function TransferSuccessView:OnFlush()
    if self.prof == nil then
        self.prof = 1
    end
    local data_list = TransFerWGData.Instance:GetTransFerSingleRewardList(self.prof, true)
    self.reward_item_list:SetDataList(data_list)

    local level_name = TransFerWGData.Instance:GetRoleProfLevelName(self.prof)
    self.node_list.text_desc_1.text.text = string.format(Language.TransFer.SuccessDesc_1,level_name)
    local equip_order = EquipBodyWGData.Instance:GetEquipOrderByTransferLevel(self.prof)
    
    self.node_list.text_desc_2.text.text = string.format(Language.TransFer.SuccessDesc_2,NumberToChinaNumber(equip_order))
    self.node_list.text_desc_3.text.text = Language.TransFer.SuccessDesc_3


    -- 动画
    self:PlayAnim()
end

function TransferSuccessView:PlayAnim()

    local god_and_demons_type = self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.GOD and 0 or 1
    for i = 1, 3 do
        local node_name = string.format("effect_bg_%s_%s",god_and_demons_type ,i)
        self.node_list[node_name]:SetActive(false)
    end
    if self.tween_sequence then
		self.tween_sequence:Kill()
		self.tween_sequence = nil
	end
    local target_sizeDelta = Vector2(428,96)
    self.node_list.mask_1.rect.sizeDelta = Vector2(0,96)
    self.node_list.mask_2.rect.sizeDelta = Vector2(0,96)
    self.node_list.mask_3.rect.sizeDelta = Vector2(0,96)

    self.tween_sequence = DG.Tweening.DOTween.Sequence()
    -- 1
    self.tween_sequence:AppendCallback(function ()
		local node_name = string.format("effect_bg_%s_%s",god_and_demons_type ,1)
        self.node_list[node_name]:SetActive(true)
	end)
    self.tween_sequence:Insert(0, self.node_list.mask_1.rect:DOSizeDelta(target_sizeDelta,0.5):SetEase(DG.Tweening.Ease.OutCubic))
    -- 2
    self.tween_sequence:InsertCallback(0.3,function ()
		local node_name = string.format("effect_bg_%s_%s",god_and_demons_type ,2)
        self.node_list[node_name]:SetActive(true)
	end)
    self.tween_sequence:Insert(0.3, self.node_list.mask_2.rect:DOSizeDelta(target_sizeDelta,0.5):SetEase(DG.Tweening.Ease.OutCubic))
    -- 3
    self.tween_sequence:InsertCallback(0.6,function ()
        local node_name = string.format("effect_bg_%s_%s",god_and_demons_type ,3)
        self.node_list[node_name]:SetActive(true)
    end)
    self.tween_sequence:Insert(0.6, self.node_list.mask_3.rect:DOSizeDelta(target_sizeDelta,0.5):SetEase(DG.Tweening.Ease.OutCubic))

	-- self.tween_sequence:OnComplete(function ()
	-- 	self.sq_tween_sequence:Kill()
	-- 	self.sq_tween_sequence = nil
	-- end)
	self.tween_sequence:SetEase(DG.Tweening.Ease.Linear)
end


function TransferSuccessView:OnClickBtnCancel()
    self:Close()
end


function TransferSuccessView:OnClickBtnOK()
    TransFerWGCtrl.Instance:SendTransferOperate(ROLE_ZHUANZHI_OPER_TYPE.ROLE_ZHUANZHI_OPER_TYPE_CHOOSE_TYPE, self.type)
    self:Close()
end

function TransferSuccessView:CloseCallBack()
    -- ViewManager.Instance:Close(GuideModuleName.GodAndDemons)
end
