-- X-新节日活动-节日排行.xls
local item_table={
[1]={item_id=38536,num=1,is_bind=1},
[2]={item_id=32538,num=1,is_bind=1},
[3]={item_id=29976,num=100,is_bind=1},
[4]={item_id=32312,num=1,is_bind=1},
[5]={item_id=26464,num=2,is_bind=1},
[6]={item_id=26463,num=3,is_bind=1},
[7]={item_id=56405,num=1,is_bind=1},
[8]={item_id=56415,num=1,is_bind=1},
[9]={item_id=44185,num=50,is_bind=1},
[10]={item_id=29976,num=50,is_bind=1},
[11]={item_id=26464,num=1,is_bind=1},
[12]={item_id=26463,num=2,is_bind=1},
[13]={item_id=56404,num=1,is_bind=1},
[14]={item_id=56414,num=1,is_bind=1},
[15]={item_id=44185,num=40,is_bind=1},
[16]={item_id=29976,num=30,is_bind=1},
[17]={item_id=26463,num=1,is_bind=1},
[18]={item_id=26460,num=1,is_bind=1},
[19]={item_id=26509,num=1,is_bind=1},
[20]={item_id=26524,num=1,is_bind=1},
[21]={item_id=26673,num=1,is_bind=1},
[22]={item_id=44185,num=30,is_bind=1},
[23]={item_id=29976,num=20,is_bind=1},
[24]={item_id=26688,num=1,is_bind=1},
[25]={item_id=26672,num=1,is_bind=1},
[26]={item_id=44185,num=20,is_bind=1},
[27]={item_id=29976,num=15,is_bind=1},
[28]={item_id=26687,num=1,is_bind=1},
[29]={item_id=26671,num=1,is_bind=1},
[30]={item_id=44185,num=15,is_bind=1},
[31]={item_id=29901,num=1,is_bind=1},
[32]={item_id=29960,num=1,is_bind=1},
[33]={item_id=32313,num=1,is_bind=1},
[34]={item_id=26464,num=3,is_bind=1},
[35]={item_id=26463,num=5,is_bind=1},
[36]={item_id=56406,num=1,is_bind=1},
[37]={item_id=56416,num=1,is_bind=1},
[38]={item_id=44185,num=100,is_bind=1},
}

return {
open_day={
{}
},

open_day_meta_table_map={
},
rank_reward={
{},
{min_rank=2,max_rank=2,reach_value=100000,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6],[6]=item_table[7],[7]=item_table[8],[8]=item_table[9]},},
{min_rank=3,max_rank=3,reach_value=80000,reward_item={[0]=item_table[2],[1]=item_table[10],[2]=item_table[4],[3]=item_table[11],[4]=item_table[12],[5]=item_table[13],[6]=item_table[14],[7]=item_table[15]},},
{min_rank=4,max_rank=6,reach_value=60000,reward_item={[0]=item_table[16],[1]=item_table[17],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20],[5]=item_table[21],[6]=item_table[22]},},
{min_rank=7,max_rank=10,reach_value=40000,reward_item={[0]=item_table[23],[1]=item_table[17],[2]=item_table[18],[3]=item_table[21],[4]=item_table[24],[5]=item_table[25],[6]=item_table[26]},},
{min_rank=11,max_rank=15,reach_value=20000,reward_item={[0]=item_table[27],[1]=item_table[17],[2]=item_table[18],[3]=item_table[25],[4]=item_table[28],[5]=item_table[29],[6]=item_table[30]},}
},

rank_reward_meta_table_map={
},
model_display={
{}
},

model_display_meta_table_map={
},
open_day_default_table={start_day=1,end_day=9999,grade=1,open_panel="",act_type=2337,},

rank_reward_default_table={grade=1,min_rank=1,max_rank=1,reach_value=120000,reward_item={[0]=item_table[31],[1]=item_table[32],[2]=item_table[1],[3]=item_table[2],[4]=item_table[33],[5]=item_table[34],[6]=item_table[35],[7]=item_table[36],[8]=item_table[37],[9]=item_table[38]},},

model_display_default_table={grade=1,model_show_type=4,model_bundle_name="",model_asset_name="",model_show_itemid=0,model_name="初号机神",display_pos="-186|-86",display_scale=0.8,display_rotation="-10|0|0",}

}

