BaseTip = BaseTip or BaseClass(BaseRender)

local attr_node_info = {
	["normal"] = {sort_index = 0, path_key = "normal_attribute", parent = "center_content"},			-- 通常属性
	["long_zhu_skill"] = {sort_index = 11, path_key = "long_zhu_skill", parent = "center_content"},		-- 龙珠技能
    ["base"] = {sort_index = 20, path_key = "base_attribute", parent = "center_content"},				-- 基础属性
    ["star_attr"] = {sort_index = 21, path_key = "star_attribute", parent = "center_content"},			-- 骑宠装备星级属性
	["beast_alchemy_score"] = {sort_index = 22, path_key = "beast_alchemy_score", parent = "center_content"},-- 幻兽内丹装备评分词条
    ["desc"] = {sort_index = 23, path_key = "item_desc", parent = "center_content"},					-- 物品描述
	["beast_flair"] = {sort_index = 25, path_key = "beast_flair_attribute", parent = "center_content"},	--灵兽资质展示
    ["quality_addition"] = {sort_index = 24, path_key = "mount_pet_quality_addition", parent = "center_content"},	-- 骑宠装备品质加成
    ["equip_skill"] = {sort_index = 26, path_key = "equip_skill", parent = "center_content"},			-- 装备技能
	["pink_attr"] = {sort_index = 29, path_key = "pink_attribute", parent = "center_content"},			-- 神装属性
	["god_attr"] = {sort_index = 30, path_key = "god_attribute", parent = "center_content"},			-- 神装属性
	["daohang_keyin_attr"] = {sort_index = 31, path_key = "daohang_keyin_attribute", parent = "center_content"},  -- 道行刻印属性
	["charm_attr"] = {sort_index = 32, path_key = "charm_attr", parent = "center_content"},				--天道石属性
	["more_color_attr"] = {sort_index = 40, path_key = "more_color_attribute", parent = "center_content"},	-- 幻彩属性
	["special"] = {sort_index = 50, path_key = "special_attribute", parent = "center_content"},			-- 特殊属性
	["yinfu"] = {sort_index = 61, path_key = "yinfu_attribute", parent = "center_content"},				-- 印符属性
	["yinji"] = {sort_index = 62, path_key = "yinji_attribute", parent = "center_content"},				-- 印记属性
	["xianpin"] = {sort_index = 70, path_key = "xianpin_attribute", parent = "center_content"},			-- 仙品属性
	["hidden_star"] = {sort_index = 71, path_key = "hidden_star_attrs", parent = "center_content"},		-- 暗器星级属性
	["zhushen"] = {sort_index = 72, path_key = "zhushen_attribute", parent = "center_content"},			-- 铸神属性
	["xianqsuit"] = {sort_index = 80, path_key = "xianqsuit_attribute", parent = "center_content"},		-- 仙品套装属性  (缺资源需要处理)
	["xilian"] = {sort_index = 90, path_key = "xilian_attribute", parent = "center_content"},			-- 洗练属性
	["stone"] = {sort_index = 100, path_key = "stone_attribute", parent = "center_content"},			-- 宝石属性  (缺资源需要处理)
	["stone_refine"] = {sort_index = 101, path_key = "baoshi_refine_attribute", parent = "center_content"},			-- 宝石精炼属性
	["lingyu"] = {sort_index = 102, path_key = "lingyu_attribute", parent = "center_content"},			-- 灵玉属性  (缺资源需要处理)
	["yuling"] = {sort_index = 103, path_key = "yuling_attribute", parent = "center_content"},			-- 御灵属性  (缺资源需要处理)
	["shengpin"] = {sort_index = 110, path_key = "shengpin_attribute", parent = "center_content"},		-- 升品属性
	["strong"] = {sort_index = 111, path_key = "strong_attribute", parent = "center_content"},			-- 强化属性
	["item_skil"] = {sort_index = 121, path_key = "item_skill_panel", parent = "center_content"},		-- 物品技能  (缺资源需要处理)
	["shenbing_skill"] = {sort_index = 122, path_key = "shenbing_skill", parent = "center_content"},	-- 神兵技能
	["wardrobe_attribute"] = {sort_index = 123, path_key = "wardrobe_attribute", parent = "center_content"},	-- 衣橱  (缺资源需要处理)
	["fs_suit_attribute"] = {sort_index = 124, path_key = "fs_suit_attribute", parent = "center_content"},		-- 四象魂骨套装  (缺资源需要处理)
	["way_desc"] = {sort_index =125, path_key = "get_way_desc", parent = "center_content"},				-- 获取途径(缺资源需要处理)
	["exp_desc"] = {sort_index =126, path_key = "exp_desc", parent = "center_content"},				-- 经验丹次数限制描述
	["equip_compose_tips"] = {sort_index = 130, path_key = "tips_compose_panel", parent = "bottom_panel"},		-- 装备合成显示 (缺资源 需要处理)
	["btns"] = {sort_index = 131, path_key = "btn_panel", parent = "center_root"},						-- 右下按钮板
	["buy"] = {sort_index = 132, path_key = "buy_panle", parent = "bottom_panel"},						-- 购买面板(缺资源需要处理)
	["capability"] = {sort_index = 133, path_key = "capability_panel", parent = "bottom_panel"},		-- 战斗力面板
	["capability_max"] = {sort_index = 134, path_key = "capability_panel_max", parent = "bottom_panel"},		-- 满级战斗力面板
	["get_way"] = {sort_index = 135, path_key = "get_way_panel", parent = "center_root"},				-- 获取途径面板  (缺资源需要处理)
	["market_panel"] = {sort_index = 136, path_key = "market_panel", parent = "bottom_panel"},			-- 市场价格相关信息
	["xianmeng_cangku_info"] = {sort_index = 137, path_key = "xianmeng_cangku_info", parent = "bottom_panel"},	-- 仙盟仓库相关信息
    ["bagua_slot_info"] = {sort_index = 124, path_key = "bagua_slot_info", parent = "center_content"},	-- 八卦相关信息  (缺资源需要处理)
	["flef_evolve"] = {sort_index = 138, path_key = "flef_evolve_attribute", parent = "center_content"},			-- 神体 圣装 星级属性
	["shitian_suit"] = {sort_index = 140, path_key = "shitian_suit_attribute", parent = "center_content"},	--弑天套装tips信息  (缺资源需要处理)
	["shitian_suit_strengthen"] = {sort_index = 139, path_key = "shitian_suit_strengthen_info", parent = "center_content"},	--弑天套装拓展信息  (缺资源需要处理)
	["draw_gift"] = {sort_index = 141, path_key = "draw_gift_panel", parent = "bottom_panel"},	        -- 抽奖礼包信息
	["wuhun_front_gem_tip"] = {sort_index = 150, path_key = "wuhun_front_gem_tip", parent = "center_content"},	--武魂魂阵魂石的弹窗  (缺资源需要处理)
	["gift_item_list"] = {sort_index = 120, path_key = "gift_item_list", parent = "center_content"},	--礼包类道具展示具体奖励的列表,排序需要在获取途径上面
	["common_show_item_list"] = {sort_index = 152, path_key = "common_show_item_list", parent = "center_content"},	--通用道具列表展示
	["shanhaijing_zuhe"] = {sort_index = 123, path_key = "shanhaijing_zuhe", parent = "center_content"},	-- 图鉴羁绊
	["beast_alchemy_additional"] = {sort_index = 123, path_key = "beast_alchemy_additional", parent = "center_content"},	-- 幻兽装备词条附加

}

local left_noder_info = {
	["model"] = {is_active = false, parent = "tips_model_panel"},		-- 模型
	["head"] = {is_active = false, parent = "head_cell_root"},			-- 头像
	["bubble"] = {is_active = false, parent = "bubble_cell_root"},		-- 气泡
	["title"] = {is_active = false, parent = "title_cell_root"},		-- 称号
	["background"] = {is_active = false, parent = "background_cell_root"},		-- 背景 
	["spine"] = {is_active = false, parent = "spine_cell_root"},		--龙骨动画
	["mingwen"] = {is_active = false, parent = "mingwen_item_cell"},	-- 妖魂
	["display_effect"] = {is_active = false, parent = "display_effect_root"},
	["lingyu_model"] = {is_active = false, parent = "tips_model_panel"},		-- 领域模型
	["weapon"] = {is_active = false, parent = "weapon_cell_root"},				-- 装备
	["img_effect"] = {is_active = false, parent = "img_effect_root"},			--图片和特效一起展示
	["spe_model"] = {is_active = false, parent = "spe_model_panel"},	-- 特殊节点模型（通用方式实现出模型）
}

local key_str = {
	normal = "normal",
    base = "base",
    star_attr = "star_attr",
    quality_addition = "quality_addition",
	pink_attr = "pink_attr",
	god_attr = "god_attr",
	daohang_keyin_attr = "daohang_keyin_attr",
	more_color = "more_color_attr",
	special = "special",
	yinfu = "yinfu",
	yinji = "yinji",
	xianpin = "xianpin",
	hidden_star = "hidden_star",
	xianqsuit = "xianqsuit",
	xilian = "xilian",
	stone = "stone",
	stone_refine = "stone_refine",
	lingyu = "lingyu",
	yuling = "yuling",
	zhushen = "zhushen",
	shengpin = "shengpin",
	strong = "strong",
	desc = "desc",
	beast_alchemy_score = "beast_alchemy_score",
	btns = "btns",
	buy = "buy",
	way_desc = "way_desc",
	exp_desc = "exp_desc",
	capability = "capability",
	capability_max = "capability_max",
	item_skil = "item_skil",
	shenbing_skill = "shenbing_skill",
	wardrobe_attribute = "wardrobe_attribute",
	model = "model",
	head = "head",
	bubble = "bubble",
	background = "background",
	mingwen = "mingwen",
	display_effect = "display_effect",
	img_effect = "img_effect",
	get_way = "get_way",
	title = "title",
	market_panel = "market_panel",
	xianmeng_cangku_info = "xianmeng_cangku_info",
	equip_compose_tips = "equip_compose_tips",
	bagua_slot_info = "bagua_slot_info",
    equip_skill = "equip_skill",
	fs_suit_attribute = "fs_suit_attribute",
	long_zhu_skill = "long_zhu_skill",
	flef_evolve = "flef_evolve",
	shitian_suit = "shitian_suit",
	spine = "spine",
	beast_flair = "beast_flair",
	shitian_suit_strengthen = "shitian_suit_strengthen",
	wuhun_front_gem_tip = "wuhun_front_gem_tip",
	charm_attr = "charm_attr",
	lingyu_model = "lingyu_model",
	weapon = "weapon",
	gift_item_list = "gift_item_list",
	spe_model = "spe_model",
	draw_gift = "draw_gift",
	common_show_item_list = "common_show_item_list",
	shanhaijing_zuhe = "shanhaijing_zuhe",
	beast_alchemy_additional = "beast_alchemy_additional",
}

local scroll_max_height = 572

function BaseTip:__init(instance)
	if not instance then
		return
	end
	self.is_load = false
	self.tips_obj_list = {}
	self.tips_node_list = {}
	self.tips_load_list = {}
	self.attr_info_list = {}
	self.other_info_list = {}
	self.stone_attr_com_list = {}
	self.lingyu_attr_com_list = {}
    self.suit_attr_com_list = {}
    self.mount_suit_attr_com_list = {}
	self.item_skil_com_list = {}
	self.item_beast_flair_list = nil
	self.save_item_name_pos = nil
	self.role_model = nil
	self.lingyu_model = nil
	self.bubble_loader = nil
	self.background_loader = nil
	self.tween_yoyo = nil
	self.item_cell = nil
	self.head_cell = nil
	self.mingwen_item_cell = nil
	self.pet_cell = nil
	self.shenshou_equip_cell = nil
	self.tips_compose_list = nil
	self.hide_scroll_bar = nil
	self.item_id = nil
	self.spe_model_display = nil
	self.draw_gift_grid_list = nil
	self.draw_gift_baodi_list = nil
	self.wuhun_gem_item_tab = {}

	local bundle_name, asset_name = ResPath.GetF2TipPrefab("base_tips")
	self:LoadAsset(bundle_name, asset_name, instance.transform)
end

function BaseTip:__delete()
	if self.tips_obj_list then
		for _,v in pairs(self.tips_obj_list) do
			ResMgr:Destroy(v)
		end
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end
	
	if self.lingyu_model then
		self.lingyu_model:DeleteMe()
		self.lingyu_model = nil
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end

	if self.mingwen_item_cell then
		self.mingwen_item_cell:DeleteMe()
		self.mingwen_item_cell = nil
	end

	if self.pet_cell then
		self.pet_cell:DeleteMe()
		self.pet_cell = nil
	end

	if self.shenshou_equip_cell then
		self.shenshou_equip_cell:DeleteMe()
		self.shenshou_equip_cell = nil
	end

	if self.wardrobe_attr_list then
		for k, v in pairs(self.wardrobe_attr_list) do
			v:DeleteMe()
		end
		self.wardrobe_attr_list = nil
	end

	if self.fs_suit_skill_list then
		for k, v in pairs(self.fs_suit_skill_list) do
			v:DeleteMe()
		end
		self.fs_suit_skill_list = nil
	end

	if self.tween_yoyo then
		self.tween_yoyo:Kill()
		self.tween_yoyo = nil
    end

	if self.tips_compose_list then
		for k, v in pairs(self.tips_compose_list) do
			v:DeleteMe()
		end
		self.tips_compose_list = nil
	end

	if self.wuhun_gem_item_tab then
		for index, value in ipairs(self.wuhun_gem_item_tab) do
			value:DeleteMe()
		end
		self.wuhun_gem_item_tab = nil
	end

	if self.show_gift_item_list then
		for k, v in pairs(self.show_gift_item_list) do
			for index, value in pairs(v) do
				value:DeleteMe()
			end
		end

		self.show_gift_item_list = nil
	end

	if self.draw_gift_grid_list then
        self.draw_gift_grid_list:DeleteMe()
        self.draw_gift_grid_list = nil
    end

	if self.draw_gift_baodi_list then
        self.draw_gift_baodi_list:DeleteMe()
        self.draw_gift_baodi_list = nil
    end

	if self.spe_model_display then
        self.spe_model_display:DeleteMe()
        self.spe_model_display = nil
	end

	if self.common_show_item_list then
		for k, v in pairs(self.common_show_item_list) do
			v:DeleteMe()
		end
		self.common_show_item_list = nil
	end

	if self.alchemy_suit_list and #self.alchemy_suit_list > 0 then
		for _, alchemy_suit_cell in ipairs(self.alchemy_suit_list) do
			alchemy_suit_cell:DeleteMe()
			alchemy_suit_cell = nil
		end

		self.alchemy_suit_list = nil
	end

    self:CancelWeaponTween()

	self.is_load = nil
	self.center_ver_group = nil
	self.tips_obj_list = nil
	self.tips_node_list = nil
	self.tips_load_list = nil
	self.attr_info_list = nil
	self.other_info_list = nil
	self.stone_attr_com_list = nil
	self.lingyu_attr_com_list = nil
    self.suit_attr_com_list = nil
    self.mount_suit_attr_com_list = nil
	self.item_skil_com_list = nil
	self.item_beast_flair_list = nil
	self.save_item_name_pos = nil
	self.bubble_loader = nil
	self.background_loader = nil
	self.hide_scroll_bar = nil
	self.buy_item_1 = nil
	self.buy_item_2 = nil
	self.item_id = nil
end

function BaseTip:Reset()
	if not self.is_load then
		return
	end

	for _, obj in pairs(self.tips_obj_list) do
		obj:SetActive(false)
	end

	self.attr_info_list = {}
	self.other_info_list = {}
	self.node_list["item_name"].text.text = ""
	self.node_list["item_equip_socre"].text.text = ""
	self.node_list["item_synthetical_socre"].text.text = ""
	self.node_list["bottom_label"]:SetActive(false)
	self.node_list["get_way_btn"]:SetActive(false)
	self.node_list["top_left_icon"]:SetActive(false)
	self.node_list["left_root"]:SetActive(false)
	self.node_list["get_way_root"]:SetActive(false)
	self.node_list["tianshen_wuxing"]:SetActive(false)
	self.node_list["tianshendingwei_bg"]:SetActive(false)
	self.node_list["skill_show_btn"]:SetActive(false)
	self.node_list["long_effect_root"]:SetActive(false)
	self.node_list["tips_panel_top_effect"]:SetActive(false)
	self.node_list["tips_panel_bottom_effect"]:SetActive(false)
	self.node_list["left_root_show_model_di"]:SetActive(true)
	self.show_tianshen_skill_index = nil

	for k,v in pairs(left_noder_info) do
		v.is_active = false
		self.node_list[v.parent]:SetActive(false)
	end

	if self.item_cell then
		self.item_cell:Reset()
        self.item_cell:SetIsShowTips(false)
    end

	if self.role_model then
		self.role_model:ClearModel()
	end

	if self.lingyu_model then
		self.lingyu_model:ClearModel()
	end

	if self.head_cell then
		self.head_cell:Reset()
	end

	if self.tween_yoyo then
		self.tween_yoyo:Pause()
	end

	if self.spe_model_display then
        self.spe_model_display:Reset()
	end

	self.hide_scroll_bar = false
	self:SetTopColorBg(0)
	self:CancleShowCCEffect()
end

-- New时候传入父节点导致SubPanelView第14行和51行执行两次SetInstance,后面再想怎么改吧
function BaseTip:LoadCallBack()
	if not self.node_list["base_tips"] or self.is_load then
		return
	end

	self.center_ver_group = self.node_list["center_content"]:GetComponent(typeof(UnityEngine.UI.VerticalLayoutGroup))
	self.bottom_ver_group = self.node_list["bottom_panel"]:GetComponent(typeof(UnityEngine.UI.VerticalLayoutGroup))
	self.node_list["get_way_btn"].button:AddClickListener(BindTool.Bind(self.OnClickOpenGetWayPanel, self))
	self.node_list["skill_show_btn"].button:AddClickListener(BindTool.Bind(self.OnClickSkillShow, self))
	self.is_load = true
end

-- 出场特效
local show_effect_time_quest_key = "show_effect_time_quest_key"
function BaseTip:SetShowCCEffect(effect_id)
	if not self.is_load or not effect_id then
		return
	end

	local bundle_name, asset_name = ResPath.GetEffectUi("UI_tip_show_effect_" .. effect_id)
	self.node_list["cc_effect"]:ChangeAsset(bundle_name, asset_name)
	self.node_list["cc_effect"]:SetActive(true)
	ReDelayCall(self, function()
		if self.node_list["cc_effect"] then
			self.node_list["cc_effect"]:CustomSetActive(false)
		end
	end, 5.1, show_effect_time_quest_key)
end

function BaseTip:CancleShowCCEffect()
	CancleDelayCall(self, show_effect_time_quest_key)
	self.node_list["cc_effect"]:CustomSetActive(false)
end

function BaseTip:GetGetWayBtn()
    return self.node_list["get_way_btn"]
end

function BaseTip:GetTipsObjByType(obj_type)
    return self.tips_obj_list[obj_type]
end

function BaseTip:LoadAttributeItem(key, call_back)
	local load_info = attr_node_info[key]
	if not load_info or self.tips_load_list[key] then
		return
	end

	self.tips_load_list[key] = true
	local index = self:GetIndex()
	local res_async_loader = AllocResAsyncLoader(self, load_info.path_key .. index)
	res_async_loader:Load("uis/view/itemtip_ui_prefab", load_info.path_key, nil, function (new_obj)
		local obj = ResMgr:Instantiate(new_obj)
		obj.transform:SetParent(self.node_list[load_info.parent].transform, false)
		self.tips_obj_list[key] = obj
		local name_table = obj:GetComponent(typeof(UINameTable))
		self.tips_node_list[key] = U3DNodeList(name_table, self)
		self:FlushAttrSibling()
		if call_back then
			call_back(true)
		end
		self:FlushCenterScrollHeight()
	end)
end

function BaseTip:FlushAttrSibling()
	local obj_list = {}
	for k,v in pairs(self.tips_obj_list) do
		if attr_node_info[k] then
			obj_list[attr_node_info[k].sort_index] = v
		end
	end

	obj_list = SortTableKey(obj_list)
	for i = 1, #obj_list do
		obj_list[i].transform:SetSiblingIndex(i)
	end
end

function BaseTip:FlushCenterScrollHeight()
	if self.is_load then
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["center_content"].rect)
		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["bottom_panel"].rect)
		local ver_group_height = self.center_ver_group.preferredHeight
		if ver_group_height > 0 then
			local center_scroll = self.node_list["center_scroll"]
			local bottom_preferred_height = self.bottom_ver_group.preferredHeight
			local scroll_preferred_height = scroll_max_height - bottom_preferred_height
			center_scroll.layout_element.preferredHeight = (ver_group_height > scroll_preferred_height and scroll_preferred_height) or ver_group_height
			center_scroll.scroll_rect.enabled = ver_group_height > scroll_preferred_height
			self.node_list["scroll_bar"]:SetActive(ver_group_height > scroll_preferred_height)
			center_scroll.scroll_rect.verticalNormalizedPosition = 1
			self.node_list["scroll_handle"]:SetActive(not self.hide_scroll_bar)
		end
	end
end

function BaseTip:SetItemName(name)
	if self.is_load then
		local item_name = self.node_list["item_name"]
		---[[ 对齐效果图
		-- local special_str = string.find(name, "【", 1)
		-- if special_str and not self.save_item_name_pos then
		-- 	local pos_x, pos_y = RectTransform.GetAnchoredPositionXY(item_name.rect)
		-- 	self.save_item_name_pos = {pos_x, pos_y}
		-- 	RectTransform.SetAnchoredPositionXY(item_name.rect, pos_x - 8, pos_y)
		-- elseif self.save_item_name_pos then
		-- 	local pos = self.save_item_name_pos
		-- 	RectTransform.SetAnchoredPositionXY(item_name.rect, pos[1], pos[2])
		-- 	self.save_item_name_pos = nil
		-- end
		--]]
		item_name.text.text = name
	end
end

function BaseTip:SetEquipSocre(socre)
	if self.is_load then
		self.node_list["item_equip_socre"].text.text = socre
	end
end

function BaseTip:SetSyntheticalSocre(socre)
	if self.is_load then
		self.node_list["item_synthetical_socre"].text.text = socre
	end
end

function BaseTip:SetTopLeftIcon(name)
	if self.is_load then
		--self.node_list["top_left_icon"].image:LoadSprite(ResPath.GetF2TipImages(name))
		self.node_list["top_left_icon"]:SetActive(true)
	end
end

-- 设置最底部文本
function BaseTip:SetBottomLabel(str)
	str = str or ""
	self.node_list["bottom_label"].text.text = str
	self.node_list["bottom_label"]:SetActive(str ~= "")
end

function BaseTip:SetTopColorBg(color, show_condition)
	if self.is_load then
		color = color <= 0 and 1 or color
		self.node_list["top_color_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_ty_tips_top_" .. color))
	end
end

function BaseTip:SetOrnamentImage(color, special_border)
	local is_show = special_border ~= nil and special_border == 1
	self.node_list["ornament_panel"]:SetActive(is_show)
	if (is_show) then
		self.node_list["ornament_image"].image:LoadSprite(ResPath.GetNoPackPNG("a3_ty_tips_ts_" .. color))
	end
end

function BaseTip:SetLongZhuTopColorBg()
	if self.is_load then
		self.node_list["top_color_bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_ty_tips_top_4"))
	end
end

--Tips顶部的盘龙特效
function BaseTip:SetTopLongEffectShow(color, show_condition)
	if self.is_load then
		self.node_list["long_effect_root"]:SetActive(false)--color >= GameEnum.ITEM_COLOR_PINK or show_condition)
		-- self.node_list["long_move_eff"]:SetActive(color >= GameEnum.ITEM_COLOR_PINK or show_condition)
	end
end

--Tips面板特效
function BaseTip:SetTipsPanelEffectShow(bundle_name, asset_name)
	if self.is_load then
		self.node_list["tips_panel_top_effect"]:SetActive(true)
		self.node_list["tips_panel_top_effect"]:ChangeAsset(bundle_name, asset_name)
	end
end

--Tips面板特效
function BaseTip:SetTipsPanelBottomEffectShow(bundle_name, asset_name)
	if self.is_load then
		self.node_list["tips_panel_bottom_effect"]:SetActive(true)
		self.node_list["tips_panel_bottom_effect"]:ChangeAsset(bundle_name, asset_name)
	end
end

function BaseTip:SetHideScrollBar(value)
	self.hide_scroll_bar = value
end

function BaseTip:GetItemCell()
	if not self.item_cell and self.is_load then
		self.item_cell = ItemCell.New(self.node_list["cell_pos"])
		self.item_cell:SetIsShowTips(false)
	end
	return self.item_cell
end

function BaseTip:GetShenshouEquipCell()
	if not self.shenshou_equip_cell and self.is_load then
		self.shenshou_equip_cell = ShenShouEquipCell.New(self.node_list["cell_pos"])
	end
	return self.shenshou_equip_cell
end

function BaseTip:GetHeadCell()
	if not self.is_load then
		return
	end

	if not self.head_cell then
		self.head_cell = BaseHeadCell.New(self.node_list["head_cell_root"])
	end

	self:FlushLeftPanel(key_str.head)
	self:PlayYoyoTween()
	return self.head_cell
end

function BaseTip:GetWeaponCell(asset, bundle)
	self.node_list.weapon_cell_image.raw_image:LoadSprite(bundle, asset, function()
		self.node_list.weapon_cell_image.raw_image:SetNativeSize()
	end)

	self:FlushLeftPanel(key_str.weapon)
	self:PlayYoyoTween()
	return self.head_cell
end

function BaseTip:GetMingWenCell()
	if not self.is_load then
		return
	end

	if not self.mingwen_item_cell then
		self.mingwen_item_cell = MingWenItemRender.New(self.node_list["mingwen_item_cell"])
	end

	self:FlushLeftPanel(key_str.mingwen)
	self:PlayYoyoTween()
	return self.mingwen_item_cell
end

function BaseTip:GetModle(is_need_event_trigger, camera_type, offset_setting)
	if not self.is_load then
		return
	end

	if not self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["model_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.XL,
			can_drag = true,
			blur_bg = true,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)
		-- local event_trigger = is_need_event_trigger and self.node_list["model_event"].event_trigger_listener
		-- self.node_list["model_event"]:SetActive(is_need_event_trigger)
		-- self.role_model:SetUI3DModel(self.node_list["model_root"].transform, event_trigger, 1, nil, camera_type, offset_setting)
	end

	self:FlushLeftPanel(key_str.model)
	return self.role_model
end

function BaseTip:GetLingYuModle(camera_type)
	if not self.is_load then
		return
	end

	if not self.lingyu_model then
		self.lingyu_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["fazhen_model_root"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = true,
		}
		
		self.lingyu_model:SetRenderTexUI3DModel(display_data)
	end

	self:FlushLeftPanel(key_str.lingyu_model)
	return self.lingyu_model
end

-- 设置特殊的位置偏移
function BaseTip:SetModleRootPos(pos)
	local tween_root = self.node_list["model_root"].rect
	if tween_root then
		tween_root.anchoredPosition = pos
	end
end

function BaseTip:FixModelCameraPos()
	self.role_model:FixToOrthographic(self.view.transform)
end

function BaseTip:GetSpeModle()
	if not self.is_load then
		return
	end

	local model_root = self.node_list["spe_model_root"]
	if not self.spe_model_display then
		self.spe_model_display = OperationActRender.New(model_root)
        self.spe_model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	self:FlushLeftPanel(key_str.spe_model)
	return self.spe_model_display, model_root
end

function BaseTip:PlayWeaponTween()
	if not self.tween_weapon then
		local tween_root = self.node_list["model_root"].rect
        if tween_root then
            tween_root.anchoredPosition = Vector2(0, -74)
        end
		self.tween_weapon = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 50, 1)
		self.tween_weapon:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	else
		self.tween_weapon:Restart()
	end
end

function BaseTip:CancelWeaponTween()
	if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil
        local tween_root = self.node_list["model_root"].rect
        if tween_root then
            tween_root.anchoredPosition = Vector2(0, -74)
        end
	end
end

function BaseTip:SetBubbleCell(asset, bundle)
	if not self.bubble_loader then
		local bubble_loader = AllocAsyncLoader(self, "base_tip_bubble_cell")
		bubble_loader:SetIsUseObjPool(true)
		bubble_loader:SetParent(self.node_list["bubble_cell_root"].transform)
		self.bubble_loader = bubble_loader
	end

	self.bubble_loader:Load(asset, bundle)
	self:FlushLeftPanel(key_str.bubble)
	self:PlayYoyoTween()
end

function BaseTip:SetBackgroundCell(asset, bundle)
	if not self.background_loader then
		local background_loader = AllocAsyncLoader(self, "base_tip_back_cell")
		background_loader:SetIsUseObjPool(true)
		background_loader:SetParent(self.node_list["background_cell_root"].transform)
		self.background_loader = background_loader
	end

	self.background_loader:Load(asset, bundle)
	self:FlushLeftPanel(key_str.background)
	self:PlayYoyoTween()
end


function BaseTip:SetTitleCell(title_id, bundle, asset)
	if self.is_load then
		self.node_list["title_display"]:ChangeAsset(bundle, asset, false, function(obj)
			if IsNil(obj) then
				return
			end
		
			local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
			if not diy_cfg then
				return
			end
		
			local text_obj = obj.gameObject.transform:Find("Text")
			local title_cfg = TitleWGData.Instance:GetConfig(title_id)
			if text_obj == nil or not title_cfg then
				return
			end
			
			local title_text = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
			title_text.text = title_cfg.name
		end)

		self:FlushLeftPanel(key_str.title)
		self:PlayYoyoTween()
	end
end

function BaseTip:SetSpineCell(bundle, asset)
	if not self.spine_loader then
		local spine_loader = AllocAsyncLoader(self, "base_tip_spine_cell")
		spine_loader:SetIsUseObjPool(true)
		spine_loader:SetParent(self.node_list["spine_cell_root"].transform)
		self.spine_loader = spine_loader
	end

	self.spine_loader:Load(bundle, asset)
	self:FlushLeftPanel(key_str.spine)
end

function BaseTip:SetDisplayEffect(bundle, asset, scale)
	if not self.is_load then return end

	self:FlushLeftPanel(key_str.display_effect)
	self.node_list["display_effect_root"]:ChangeAsset(bundle, asset)
	
	if scale then
		RectTransform.SetLocalScale(self.node_list["display_effect_root"].transform, scale)
	end

	self:PlayYoyoTween()
end

function BaseTip:SetImgAndEffect(img_info, effect_info)
	if not self.is_load then return end

	self:FlushLeftPanel(key_str.img_effect)
	self.node_list.img_effect_root_img.raw_image:LoadSprite(img_info.bundle, img_info.asset, function()
		self.node_list.img_effect_root_img.raw_image:SetNativeSize()
	end)

	self.node_list["img_effect_root_effect"]:ChangeAsset(effect_info.bundle, effect_info.asset)

	if img_info.scale then
		RectTransform.SetLocalScale(self.node_list["img_effect_root_img"].transform, img_info.scale)
	end

	if effect_info.scale then
		RectTransform.SetLocalScale(self.node_list["img_effect_root_effect"].transform, effect_info.scale)
	end

	if img_info.pos_x and img_info.pos_y then
		RectTransform.SetAnchoredPositionXY(self.node_list["img_effect_root_img"].rect, img_info.pos_x, img_info.pos_y)
	end

	if effect_info.pos_x and effect_info.pos_y then
		RectTransform.SetAnchoredPositionXY(self.node_list["img_effect_root_effect"].rect, effect_info.pos_x, effect_info.pos_y)
	end

	self:PlayYoyoTween()
end

function BaseTip:FlushLeftPanel(mark)
	local is_active = false
	for k,v in pairs(left_noder_info) do
		is_active = k == mark
		if v.is_active ~= is_active then
			self.node_list[v.parent]:SetActive(is_active)
			v.is_active = is_active
		end
	end

	self.node_list["left_root"]:SetActive(true)
	self:CheckGetWayWhith()
end

function BaseTip:PlayYoyoTween()
	if not self.tween_yoyo then
		local tween_root = self.node_list["tween_root"].rect
		self.tween_yoyo = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 40, 1.5)
		self.tween_yoyo:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	else
		self.tween_yoyo:Restart()
	end
end


-- 设置通常属性
-- info = {{name = "", label = ""}}
function BaseTip:SetNormalAttribute(info)
	self.attr_info_list[key_str.normal] = info
	if not self.tips_obj_list[key_str.normal] then
		self:LoadAttributeItem(key_str.normal, BindTool.Bind(self.FlushNormalAttribute, self))
	else
		self:FlushNormalAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushNormalAttribute()
	local normal_node_list = self.tips_node_list[key_str.normal]
	local normal_attr_info = self.attr_info_list[key_str.normal]
	if not normal_node_list or not normal_attr_info then
		return
	end

	local normal_label = nil
	for i = 1, 3 do
		local info = normal_attr_info[i]
		normal_label = normal_node_list["normal_label_" .. i]
		if info then
			normal_label.text.text = info.name
			normal_node_list["normal_num_" .. i].text.text = info.label
		end
		normal_label:SetActive(info ~= nil)
	end
	self.tips_obj_list[key_str.normal]:SetActive(true)
end

-- 设置星级属性
-- info = {{attr_name = "", attr_value = ""}}
function BaseTip:SetStarAttribute(info)
	self.attr_info_list[key_str.star_attr] = info
	if not self.tips_obj_list[key_str.star_attr] then
		self:LoadAttributeItem(key_str.star_attr, BindTool.Bind(self.FlushStarAttribute, self))
	else
		self:FlushStarAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushStarAttribute()
	local base_node_list = self.tips_node_list[key_str.star_attr]
	local base_attr_info = self.attr_info_list[key_str.star_attr]
	if not base_node_list or not base_attr_info then
		return
	end

	local base_label = nil
	for i = 1, 6 do
		base_label = base_node_list["star_label_" .. i]
		if base_attr_info[i] then
			base_label.text.text = base_attr_info[i].attr_name or ""
			base_node_list["star_num_" .. i].text.text = base_attr_info[i].attr_value or ""
			base_label:SetActive(true)
		else
			base_label:SetActive(false)
		end
	end

	self.tips_obj_list[key_str.star_attr]:SetActive(true)
end

-- 设置基础属性
-- info = {{attr_name = "", attr_value = "", add_str = ""}}
function BaseTip:SetBaseAttribute(info)
	self.attr_info_list[key_str.base] = info
	if not self.tips_obj_list[key_str.base] then
		self:LoadAttributeItem(key_str.base, BindTool.Bind(self.FlushBaseAttribute, self))
	else
		self:FlushBaseAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushBaseAttribute()
	local base_node_list = self.tips_node_list[key_str.base]
	local base_attr_info = self.attr_info_list[key_str.base]
	if not base_node_list or not base_attr_info then
		return
	end

	local base_label = nil
	for i = 1, 6 do
		base_label = base_node_list["base_label_" .. i]
		if base_attr_info[i] then
			base_label.text.text = base_attr_info[i].attr_name or ""
			base_node_list["base_num_" .. i].text.text = base_attr_info[i].attr_value or ""
			base_node_list["base_add_num_" .. i].text.text = base_attr_info[i].add_str or ""
			base_label:SetActive(true)
		else
			base_label:SetActive(false)
		end
	end

	base_node_list["base_title_label"].text.text = base_attr_info.title_name or Language.Tip.CommonAttr

	self.tips_obj_list[key_str.base]:SetActive(true)
end

-- 设置粉装属性
-- info = {title_name = "" , attr_list = {attr_name = "", attr_value = ""}}
function BaseTip:SetPinkAttribute(info)
	self.attr_info_list[key_str.pink_attr] = info
	if not self.tips_obj_list[key_str.pink_attr] then
		self:LoadAttributeItem(key_str.pink_attr, BindTool.Bind(self.FlushPinkAttribute, self))
	else
		self:FlushPinkAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushPinkAttribute()
	local pink_node_list = self.tips_node_list[key_str.pink_attr]
	local pink_attr_info = self.attr_info_list[key_str.pink_attr]
	if not pink_node_list or not pink_attr_info then
		return
	end

	local pink_label = nil
	local attr_info = pink_attr_info.attr_list
	for i = 1, 10 do
		pink_label = pink_node_list["pink_label_" .. i]
		if attr_info[i] then
			pink_label.text.text = attr_info[i].attr_name or ""
			pink_node_list["pink_num_" .. i].text.text = attr_info[i].attr_value or ""
			pink_label:SetActive(true)
		else
			pink_label:SetActive(false)
		end
	end

	if pink_attr_info.title_name then
		pink_node_list["pink_title_label"].text.text = pink_attr_info.title_name
	end

	self.tips_obj_list[key_str.pink_attr]:SetActive(true)
end

-- 设置天道石属性
-- info = {title_name = "" , attr_list = {attr_name = "", attr_value = ""}}
function BaseTip:SetCharmAttribute(info)
	self.attr_info_list[key_str.charm_attr] = info
	if not self.tips_obj_list[key_str.charm_attr] then
		self:LoadAttributeItem(key_str.charm_attr, BindTool.Bind(self.FlushCharmAttribute, self))
	else
		self:FlushCharmAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushCharmAttribute()
	local charm_node_list = self.tips_node_list[key_str.charm_attr]
	local charm_attr_info = self.attr_info_list[key_str.charm_attr]
	if not charm_node_list or not charm_attr_info then
		return
	end

	local charm_label = nil
	local attr_info = charm_attr_info.attr_list
	for i = 1, 13 do
		charm_label = charm_node_list["charm_label_" .. i]
		if attr_info[i] then
			charm_label.text.text = attr_info[i].attr_name or ""
			charm_node_list["charm_num_" .. i].text.text = attr_info[i].attr_value or ""
			charm_label:SetActive(true)
		else
			charm_label:SetActive(false)
		end
	end

	if charm_attr_info.title_name then
		charm_node_list["charm_title_label"].text.text = charm_attr_info.title_name
	end

	self.tips_obj_list[key_str.charm_attr]:SetActive(true)
end

-- 设置道行刻印属性
-- info = {title_name = "" ,  1 = (attr_list = {attr_name = "", attr_value = ""} 2 = (attr_list = {attr_name = "", attr_value = ""}}
function BaseTip:SetDaoHangKeYinAttribute(info)
	self.attr_info_list[key_str.daohang_keyin_attr] = info
	if not self.tips_obj_list[key_str.daohang_keyin_attr] then
		self:LoadAttributeItem(key_str.daohang_keyin_attr, BindTool.Bind(self.FlushDaoHangKeYinAttribute, self))
	else
		self:FlushDaoHangKeYinAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushDaoHangKeYinAttribute()
	local keyin_node_list = self.tips_node_list[key_str.daohang_keyin_attr]
	local keyin_attr_info = self.attr_info_list[key_str.daohang_keyin_attr]
	
	if not keyin_node_list or not keyin_attr_info then
		return
	end

	local attr_info = keyin_attr_info.attr_list
	for i = 1, 3 do
		local item_node = keyin_node_list["keyin_item" .. i]

		if attr_info[i] then
			for j = 1, 6 do
				local lable_cell_node = keyin_node_list["label".. i .."_".. j]

				local attr_cell_data = (attr_info[i].attr_list or {})[j] or {}
				if not IsEmptyTable(attr_cell_data) then
					lable_cell_node.text.text = attr_cell_data.attr_name or ""
					keyin_node_list["label_right".. i .."_".. j].text.text = attr_cell_data.value_str or ""
					lable_cell_node:CustomSetActive(true)
				else
					lable_cell_node:CustomSetActive(false)
				end
			end

			keyin_node_list["title_label" .. i].text.text = attr_info[i].title_name
			item_node:CustomSetActive(true)
		else
			item_node:CustomSetActive(false)
		end
	end

	if keyin_attr_info.title_name then
		keyin_node_list["top_title_label"].text.text = keyin_attr_info.title_name
	end

	self.tips_obj_list[key_str.daohang_keyin_attr]:SetActive(true)
end

-- 设置神装属性
-- info = {{attr_name = "", attr_value = ""}}
function BaseTip:SetGodAttribute(info)
	self.attr_info_list[key_str.god_attr] = info
	if not self.tips_obj_list[key_str.god_attr] then
		self:LoadAttributeItem(key_str.god_attr, BindTool.Bind(self.FlushGodAttribute, self))
	else
		self:FlushGodAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushGodAttribute()
	local god_node_list = self.tips_node_list[key_str.god_attr]
	local god_attr_info = self.attr_info_list[key_str.god_attr]
	if not god_node_list or not god_attr_info then
		return
	end

	local god_label = nil
	local attr_info = god_attr_info.attr_list
	for i = 1, 10 do
		god_label = god_node_list["god_label_" .. i]
		if attr_info[i] then
			god_label.text.text = attr_info[i].attr_name or ""
			god_node_list["god_num_" .. i].text.text = attr_info[i].attr_value or ""
			god_label:SetActive(true)
		else
			god_label:SetActive(false)
		end
	end

	if god_attr_info.title_name then
		god_node_list["god_title_label"].text.text = god_attr_info.title_name
	end

	self.tips_obj_list[key_str.god_attr]:SetActive(true)
end

-- 设置幻彩属性
-- info = {{attr_name = "", attr_value = ""}}
function BaseTip:SetMoreColorAttribute(info)
	self.attr_info_list[key_str.more_color] = info
	if not self.tips_obj_list[key_str.more_color] then
		self:LoadAttributeItem(key_str.more_color, BindTool.Bind(self.FlushMoreColorAttribute, self))
	else
		self:FlushMoreColorAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushMoreColorAttribute()
	local more_color_node_list = self.tips_node_list[key_str.more_color]
	local more_color_info = self.attr_info_list[key_str.more_color]
	if not more_color_node_list or not more_color_info then
		return
	end

	local more_color_label = nil
	local attr_info = more_color_info.attr_list
	for i = 1, 10 do
		more_color_label = more_color_node_list["color_label_" .. i]
		if attr_info[i] then
			more_color_label.text.text = attr_info[i].attr_name or ""
			more_color_node_list["color_num_" .. i].text.text = attr_info[i].attr_value or ""
			more_color_label:SetActive(true)
		else
			more_color_label:SetActive(false)
		end
	end

	if more_color_info.title_name then
		more_color_node_list["color_title_label"].text.text = more_color_info.title_name
	end
	self.tips_obj_list[key_str.more_color]:SetActive(true)
end

-- 设置特殊属性
-- info = {{attr_name = "", attr_desc = ""}}
function BaseTip:SetSpecialAttribute(info)
	self.attr_info_list[key_str.special] = info
	if not self.tips_obj_list[key_str.special] then
		self:LoadAttributeItem(key_str.special, BindTool.Bind(self.FlushSpecialAttribute, self))
	else
		self:FlushSpecialAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushSpecialAttribute()
	local special_node_list = self.tips_node_list[key_str.special]
	local special_attr_info = self.attr_info_list[key_str.special]
	if not special_node_list or not special_attr_info then
		return
	end

	local special_label = nil
	for i = 1, 3 do
		local info = special_attr_info[i]
		special_label = special_node_list["special_label_" .. i]
		special_label:SetActive(info ~= nil)
		if info then
			special_label.text.text = info.attr_name
			special_node_list["special_desc_" .. i].text.text = info.attr_desc
		end
	end

	self.tips_obj_list[key_str.special]:SetActive(true)
end

-- 设置印符属性
-- info = {title = "", info_list = {attr_name = "", attr_value = ""}}
function BaseTip:SetYinFuAttribute(info)
	self.attr_info_list[key_str.yinfu] = info
	if not self.tips_obj_list[key_str.yinfu] then
		self:LoadAttributeItem(key_str.yinfu, BindTool.Bind(self.FlushYinFuAttribute, self))
	else
		self:FlushYinFuAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushYinFuAttribute()
	local yinfu_node_list = self.tips_node_list[key_str.yinfu]
	local yinfu_attr_info = self.attr_info_list[key_str.yinfu]
	if not yinfu_node_list or not yinfu_attr_info then
		return
	end

	if yinfu_attr_info.title then
		yinfu_node_list["yinfu_title_label"].text.text = yinfu_attr_info.title
	end

	local info_list = yinfu_attr_info.info_list
	local yinfu_label = nil
	local yinfu_info = nil
	for i = 1, 4 do
		yinfu_info = info_list[i]
		yinfu_label = yinfu_node_list["yinfu_label_" .. i]
		yinfu_label:SetActive(yinfu_info ~= nil)
		if yinfu_info then
			yinfu_label.text.text = yinfu_info.attr_name
			yinfu_node_list["yinfu_num_" .. i].text.text = yinfu_info.attr_value
		end
	end

	self.tips_obj_list[key_str.yinfu]:SetActive(true)
end

-- 设置印记属性
-- info = {title = "", info_list = {attr_name = "", attr_value = ""}}
function BaseTip:SetYinJiAttribute(info)
	self.attr_info_list[key_str.yinji] = info
	if not self.tips_obj_list[key_str.yinji] then
		self:LoadAttributeItem(key_str.yinji, BindTool.Bind(self.FlushYinJiAttribute, self))
	else
		self:FlushYinJiAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushYinJiAttribute()
	local yinji_node_list = self.tips_node_list[key_str.yinji]
	local yinji_attr_info = self.attr_info_list[key_str.yinji]
	if not yinji_node_list or not yinji_attr_info then
		return
	end

	if yinji_attr_info.title then
		yinji_node_list["yinji_title_label"].text.text = yinji_attr_info.title
	end

	local info_list = yinji_attr_info.info_list
	local yinji_label = nil
	local yinji_info = nil
	for i = 1, 6 do
		yinji_info = info_list[i]
		yinji_label = yinji_node_list["yinji_label_" .. i]
		yinji_label:SetActive(yinji_info ~= nil)
		if yinji_info then
			yinji_label.text.text = yinji_info.attr_name
			yinji_node_list["yinji_num_" .. i].text.text = yinji_info.attr_value
		end
	end

	self.tips_obj_list[key_str.yinji]:SetActive(true)
end

-- 设置仙品属性
-- info = {title_name = "", attr_list = {icon = "", label = ""}}
function BaseTip:SetXianpinAttribute(info)
	self.attr_info_list[key_str.xianpin] = info
	if not self.tips_obj_list[key_str.xianpin] then
		self:LoadAttributeItem(key_str.xianpin, BindTool.Bind(self.FlushXianpinAttribute, self))
	else
		self:FlushXianpinAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushXianpinAttribute()
	local xianpin_node_list = self.tips_node_list[key_str.xianpin]
	local xianpin_attr_info = self.attr_info_list[key_str.xianpin]
	if not xianpin_node_list or not xianpin_attr_info then
		return
	end

	local xianpin_value = nil
	local xianpin_label = nil
	local xianpin_star = nil
	local label_part = nil
	local attr_list = xianpin_attr_info.attr_list
	for i = 1,10 do
		label_part = xianpin_node_list["label_" .. i]
		xianpin_value = xianpin_node_list["xianpin_value_" .. i]
		xianpin_label = xianpin_node_list["xianpin_label_" .. i]
		xianpin_star = xianpin_node_list["xianpin_star_" .. i]
		if attr_list[i] then
			xianpin_label.text.text = attr_list[i].label
			xianpin_label:SetActive(true)
			label_part:SetActive(true)
			xianpin_value:SetActive(attr_list[i].value ~= nil)
			if attr_list[i].value then
				xianpin_value.text.text = attr_list[i].value
			end
		else
			xianpin_label:SetActive(false)
			label_part:SetActive(false)
			xianpin_value:SetActive(false)
		end
	end

	if xianpin_attr_info.title_name then
		xianpin_node_list["xianpin_title_label"].text.text = xianpin_attr_info.title_name
	end
	self.tips_obj_list[key_str.xianpin]:SetActive(true)
end


-- 神体 圣装 设置星级属性
-- info = {title_name = "", attr_list = {icon = "", label = ""},buttom_desc = ""}
function BaseTip:SetFLEFEvolveAttribute(info)
	self.attr_info_list[key_str.flef_evolve] = info
	if not self.tips_obj_list[key_str.flef_evolve] then
		self:LoadAttributeItem(key_str.flef_evolve, BindTool.Bind(self.FlushFLEFEvolveAttribute, self))
	else
		self:FlushFLEFEvolveAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushFLEFEvolveAttribute()
	local evolve_node_list = self.tips_node_list[key_str.flef_evolve]
	local evolve_attr_info = self.attr_info_list[key_str.flef_evolve]
	if not evolve_node_list or not evolve_attr_info then
		return
	end

	local evolve_label = nil
	local evolve_star = nil
	local label_part = nil
	local attr_list = evolve_attr_info.attr_list
	for i = 1,10 do
		label_part = evolve_node_list["label_" .. i]
		evolve_label = evolve_node_list["evolve_label_" .. i]
		evolve_star = evolve_node_list["evolve_star_" .. i]
		if attr_list[i] then
			evolve_label.text.text = attr_list[i].label
			evolve_star.image.enabled = attr_list[i].is_star == 1

			evolve_label:SetActive(true)
			label_part:SetActive(true)
		else
			evolve_label:SetActive(false)
			label_part:SetActive(false)
		end
	end

	if evolve_attr_info.title_name then
		evolve_node_list["evolve_title_label"].text.text = evolve_attr_info.title_name
	end

	self.tips_obj_list[key_str.flef_evolve]:SetActive(true)
end

--设置弑天套装信息
function BaseTip:SetShiTianSuitAttribute(info, suit_seq)
	self.attr_info_list[key_str.shitian_suit] = info
	if not self.tips_obj_list[key_str.shitian_suit] then
		self:LoadAttributeItem(key_str.shitian_suit, BindTool.Bind(self.FlushShiTianSuitAttribute, self, suit_seq))
	else
		self:FlushShiTianSuitAttribute(suit_seq)
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushShiTianSuitAttribute(suit_seq)
	local shitian_node_list = self.tips_node_list[key_str.shitian_suit]
	local shitian_attr_info = self.attr_info_list[key_str.shitian_suit]
	if not shitian_node_list or not shitian_attr_info then
		return
	end

	local suit_description_cfg = ShiTianSuitWGData.Instance:GetSuitTypeCfgBySeq(suit_seq)
	local info = ShiTianSuitWGData.Instance:GetSuitPartInfo(suit_seq)
    local reward_seq = info and info.reward_seq or 0
	for k, v in pairs(shitian_attr_info) do
		if v.seq < 4 then
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.reward_item[0].item_id)
			local item_col_str
			if item_cfg then
				item_col_str = ToColorStr("【" .. v.image_name .. "】", ITEM_COLOR[item_cfg.color])
			end
			local color_str = v.seq <= reward_seq and string.format(Language.ShiTianSuit.TipsSuitInfo, v.need_normal_part_count, item_col_str) or 
			string.format(Language.ShiTianSuit.TipsSuitNoColorInfo, v.need_normal_part_count, "【" .. v.image_name .. "】")
			shitian_node_list["shitian_suit_name_" .. v.seq].text.text = color_str
		end
	end

	shitian_node_list["shitian_suit_desc"].text.text = suit_description_cfg.description
	shitian_node_list["shitian_title_label"].text.text = suit_description_cfg.description_top
	self.tips_obj_list[key_str.shitian_suit]:SetActive(true)
end

function BaseTip:SetShiTianSuitStrengthenInfo(info, suit_seq, suit_part, is_from_shitianview)
	self.attr_info_list[key_str.shitian_suit_strengthen] = info
	if not self.tips_obj_list[key_str.shitian_suit_strengthen] then
		self:LoadAttributeItem(key_str.shitian_suit_strengthen, BindTool.Bind(self.FlushShiTianSuitStrengthenInfo, self, suit_seq, suit_part, is_from_shitianview))
	else
		self:FlushShiTianSuitStrengthenInfo(suit_seq, suit_part, is_from_shitianview)
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushShiTianSuitStrengthenInfo(suit_seq, suit_part, is_from_shitianview)
	local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, suit_part)
	if is_act and is_from_shitianview then
		local stsuit_node_list = self.tips_node_list[key_str.shitian_suit_strengthen]
		local stsuit_attr_info = self.attr_info_list[key_str.shitian_suit_strengthen]
		local stone_data_list = ShiTianSuitStrengthenWGData.Instance:GetStoneDataList(suit_seq, suit_part)
		local ep_bundle, ep_asset = ResPath.GetF2RawImagesPNG(string.format("a2_lhtz_epimg_%s_%s", suit_seq, suit_part))
		stsuit_node_list.shitian_ep_img.raw_image:LoadSprite(ep_bundle, ep_asset, function()
			stsuit_node_list.shitian_ep_img.raw_image:SetNativeSize()
		end)

		for i = 1, 6 do
			local stone_id = stone_data_list[i - 1] or 0
			local stone_cfg = ShiTianSuitStrengthenWGData.Instance:GetStoneCfg(stone_id)
			local stone_obj = stsuit_node_list["stone" .. i]
			stone_obj:SetActive(stone_cfg ~= nil)
			stsuit_node_list["attr_bg" .. i]:SetActive(stone_cfg ~= nil)

			local stone_level = stone_cfg and stone_cfg.level or -1
			stsuit_node_list["grade" .. i].text.text = CommonDataManager.GetDaXie(stone_level)

			if stone_cfg then
				local bundle, asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(stone_id))
				stone_obj.image:LoadSprite(bundle, asset, function()
					stone_obj.image:SetNativeSize()
				end)
			end

			local stone_attr_list = stsuit_attr_info.stone_attr_list[i - 1]
			for attr_index = 1, 2 do
				local attr_obj = stsuit_node_list["attr" .. i .. attr_index]
				local is_show = not IsEmptyTable(stone_cfg) and not IsEmptyTable(stone_attr_list)
				attr_obj:SetActive(is_show)
				if is_show then
					local stone_attr = stone_attr_list and stone_attr_list[attr_index] or ""
					attr_obj.text.text = stone_attr
				end
			end
		end

		local is_strengthen_none = IsEmptyTable(stsuit_attr_info.strengthen_attr_list)
		stsuit_node_list.strengthen_panel:SetActive(not is_strengthen_none)
		if not is_strengthen_none then
			for i = 1, 2 do
				local attr_info = stsuit_attr_info.strengthen_attr_list[i]
				stsuit_node_list["strengthen_attr_item" .. i]:SetActive(attr_info ~= nil)
				if attr_info then
					stsuit_node_list["qh_attr_name" .. i].text.text = EquipmentWGData.Instance:GetAttrName(attr_info.attr_str, true, true)
					stsuit_node_list["qh_attr_value" .. i].text.text = attr_info.attr_value
				end
			end
		end

		local is_infuse_soul_none = IsEmptyTable(stsuit_attr_info.infusesoul_attr_list)
		stsuit_node_list.infuse_soul_panel:SetActive(not is_infuse_soul_none)
		if not is_infuse_soul_none then
			for i = 1, 2 do
				local attr_info = stsuit_attr_info.infusesoul_attr_list[i]
				stsuit_node_list["infuse_soul_attr_item" .. i]:SetActive(attr_info ~= nil)
				if attr_info then
					local level, quality = ShiTianSuitStrengthenWGData.Instance:GetEpInfuseSoulCurLevelAndQuality(suit_seq, suit_part)
					quality = quality > 8 and 8 or quality
					local fumo_des = ShiTianSuitStrengthenWGData.Instance:GetInfuseSoulLevelCfg(suit_seq, suit_part).fumo_des or ""
					local value_str = string.format(Language.ShiTianSuit.InfuseSoulTips, ITEM_COLOR[quality], attr_info.attr_value, fumo_des)
					stsuit_node_list["rl_attr_name" .. i].text.text = EquipmentWGData.Instance:GetAttrName(attr_info.attr_str, true, true)
					stsuit_node_list["rl_attr_value" .. i].text.text = value_str
				end
			end
		end
	end

	self.tips_obj_list[key_str.shitian_suit_strengthen]:SetActive(is_act and is_from_shitianview)
end

--设置魂阵魂石的弹窗显示信息
function BaseTip:SetWuHunFrontGemInfo(attr_info, active_cfg, gem_data)
	self.attr_info_list[key_str.wuhun_front_gem_tip] = attr_info
	if not self.tips_obj_list[key_str.wuhun_front_gem_tip] then
		self:LoadAttributeItem(key_str.wuhun_front_gem_tip, BindTool.Bind(self.FlushWuHunFrontGemInfo, self, active_cfg, gem_data))
	else
		self:FlushWuHunFrontGemInfo(active_cfg, gem_data)
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushWuHunFrontGemInfo(active_cfg, gem_data, is_from_shitianview)
	local gem_node_list = self.tips_node_list[key_str.wuhun_front_gem_tip]
	local gem_attr_info = self.attr_info_list[key_str.wuhun_front_gem_tip]

	local wuhun_id = active_cfg.wuhun_id
	local front_index = active_cfg.soul_front_seq
	local gem_index = active_cfg.soul_stone_seq
	local gem_grade = gem_data.gem_grade or 0
	local engrave_list = WuHunFrontWGData.Instance:GetGemAllEngraveData(wuhun_id, front_index, gem_index)
	local grade_cfg = WuHunFrontWGData.Instance:GetFrontGemGradeCfg(wuhun_id, front_index, gem_index, gem_grade)
	local grade_attr_list = WuHunFrontWGData.Instance:GetAttrFunc(grade_cfg)

	for i = 1, 6 do
		if not self.wuhun_gem_item_tab[i] then
			self.wuhun_gem_item_tab[i] = WuHunBaoShiRender.New(gem_node_list.gemstone_parent:FindObj("layout_gemstone_render" .. i))
		end

		local attr_item_obj = gem_node_list.baoshi_attr_content:FindObj("baoshi_attr_item" .. i)
		local name_table = attr_item_obj.gameObject:GetComponent(typeof(UINameTable))
		local attr_node_list = U3DNodeList(name_table, self)

		local engrave_item = engrave_list[i]
		local data = {}
		data.empty_type = true
		data.hide_prop = true

		if engrave_item and engrave_item ~= 0 then
			local cfg = WuHunFrontWGData.Instance:GetEngraveCfgByItemId(engrave_item)
			data.empty_type = false
			data.level = cfg.level
			data.item_id = engrave_item

			data.prop_txt1 = ""
			data.prop_txt2 = ""

			local baoshi_item_cfg = ItemWGData.Instance:GetItemConfig(engrave_item) or {}
			local xp_name_color = ITEM_COLOR[baoshi_item_cfg.color] or "#A8A5A4FF"

			local txt_type = math.ceil(i / 2)
			local attr_value1, attr_name1 = WuHunFrontWGData.Instance:GetAttrChangeValue(cfg.attr_id1, cfg.attr_value1)
			local attr_value2, attr_name2 = WuHunFrontWGData.Instance:GetAttrChangeValue(cfg.attr_id2, cfg.attr_value2)
			local title = string.format("<color=%s>%s%s</color>", xp_name_color, cfg.level, Language.WuHunZhenShen.WuhunFrontGemNameType1[txt_type])
			attr_node_list.text_attr_title.text.text = title
			attr_node_list.text_attr_name1.text.text = ToColorStr(attr_name1, xp_name_color)
			attr_node_list.text_attr_value1.text.text = ToColorStr(attr_value1, xp_name_color)
			attr_node_list.text_attr_name2.text.text = ToColorStr(attr_name2, xp_name_color)
			attr_node_list.text_attr_value2.text.text = ToColorStr(attr_value2, xp_name_color)
		end

		data.show_type = math.ceil(i / 2)
		self.wuhun_gem_item_tab[i]:SetData(data)

		--宝石属性显示
		attr_item_obj.gameObject:SetActive(engrave_item and engrave_item ~= 0)

		--属性最多5个
		if i < 6 then
			local grade_attr_obj = gem_node_list.grade_attr_content:FindObj("grade_attr_item" .. i)
			if grade_attr_list[i] then
				local name_table1 = grade_attr_obj.gameObject:GetComponent(typeof(UINameTable))
				local grade_attr_node = U3DNodeList(name_table1, self)

				local show_value = gem_grade % 10
				show_value = show_value > GameEnum.ITEM_COLOR_XUAN_QING and GameEnum.ITEM_COLOR_XUAN_QING or show_value
				local name_color1 = ITEM_COLOR[show_value] or "#A8A5A4FF"

				local attr_value, attr_name = WuHunFrontWGData.Instance:GetAttrChangeValue(grade_attr_list[i].attr_str, grade_attr_list[i].attr_value)
				grade_attr_node.text_attr_name.text.text = ToColorStr(attr_name, name_color1)
				grade_attr_node.text_attr_value.text.text = ToColorStr(attr_value, name_color1)
			end
			grade_attr_obj.gameObject:SetActive(grade_attr_list[i] ~= nil)
		end
	end

	self.tips_obj_list[key_str.wuhun_front_gem_tip]:SetActive(true)
end

-- 设置暗器星级属性
-- info = {title_name = "", attr_list = {icon_bundle = "",icon_asset = "", attr_text = "", state_text = "", last_text = ""}}
function BaseTip:SetHiddenStarAttribute(info)
	self.attr_info_list[key_str.hidden_star] = info
	if not self.tips_obj_list[key_str.hidden_star] then
		self:LoadAttributeItem(key_str.hidden_star, BindTool.Bind(self.FlushHiddenStarAttribute, self))
	else
		self:FlushHiddenStarAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushHiddenStarAttribute()
	local hidden_star_node_list = self.tips_node_list[key_str.hidden_star]
	local hidden_star_attr_info = self.attr_info_list[key_str.hidden_star]
	if not hidden_star_node_list or not hidden_star_attr_info then
		return
	end

	local attr_text = nil
	local state_text = nil
	local last_text = nil
	local icon = nil
	local attr_part = nil
	local attr_list = hidden_star_attr_info.attr_list
	for i = 1,6 do
		attr_part = hidden_star_node_list["attr" .. i]
		attr_text = hidden_star_node_list["attr_text" .. i]
		state_text = hidden_star_node_list["state_text" .. i]
		last_text = hidden_star_node_list["last_text" .. i]
		icon = hidden_star_node_list["icon" .. i]
		if attr_list[i] then
			attr_text.text.text = attr_list[i].attr_text
			state_text.text.text = attr_list[i].state_text
			last_text.text.text = attr_list[i].last_text
			if attr_list[i].icon_bundle and attr_list[i].icon_asset then
				icon.image:LoadSprite(attr_list[i].icon_bundle,attr_list[i].icon_asset)
				icon:SetActive(true)
			else
				icon:SetActive(false)
			end
			attr_part:SetActive(true)
		else
			attr_part:SetActive(false)
		end
	end

	if hidden_star_attr_info.title_name then
		hidden_star_node_list["hidden_star_title"].text.text = hidden_star_attr_info.title_name
	end
	self.tips_obj_list[key_str.hidden_star]:SetActive(true)
end

-- 设置仙品套装属性
-- info = {suit_title = "", suit_attr_list[1] = {suit_name = "", attr_list = {attr_name = "", attr_value = ""} } }
function BaseTip:SetXianqSuitAttribute(info)
	self.attr_info_list[key_str.xianqsuit] = info
	if not self.tips_obj_list[key_str.xianqsuit] then
		self:LoadAttributeItem(key_str.xianqsuit, BindTool.Bind(self.FlushXianqSuitAttribute, self))
	else
		self:FlushXianqSuitAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushXianqSuitAttribute()
	local xianqsuit_node_list = self.tips_node_list[key_str.xianqsuit]
	local xianqsuit_attr_info = self.attr_info_list[key_str.xianqsuit]
	if not xianqsuit_node_list or not xianqsuit_attr_info then
		return
	end
	xianqsuit_node_list["xianqsuit_title_label"].text.text = xianqsuit_attr_info.suit_title or ""
	local suit_attr_list = xianqsuit_attr_info.suit_attr_list
	if suit_attr_list then
		for i = 1, 3 do
			if suit_attr_list[i] then
				self:SetXianqSuitAttrInfo(i, suit_attr_list[i])
			end
			xianqsuit_node_list["xianqsuit_attr_" .. i]:SetActive(suit_attr_list[i] ~= nil)
		end
	end
	self.tips_obj_list[key_str.xianqsuit]:SetActive(true)
end

function BaseTip:SetXianqSuitAttrInfo(index, info_list)
	local suit_com_list = self:GetXianqSuitAttrObjList(index)
	local attr_info_list = info_list.attr_list
	for i = 1, #suit_com_list do
		local info = attr_info_list[i]
		if info then
			suit_com_list[i].attr_name.text.text = info.attr_name
			suit_com_list[i].attr_value.text.text = info.attr_value
		end

		suit_com_list[i].attr_label:SetActive(info ~= nil)
	end

	local label_1 = suit_com_list[1]
	if label_1.suit_icon and label_1.suit_num then
		XUI.SetGraphicGrey(label_1.suit_icon, not info_list.is_open)
		label_1.suit_num.text.text = info_list.suit_name or ""
	end
end

function BaseTip:GetXianqSuitAttrObjList(index)
	local com_list = self.suit_attr_com_list[index]
	if not com_list then
		com_list = {}
		local format = string.format
		local suit_node_list = self.tips_node_list[key_str.xianqsuit]
		local suit_root = suit_node_list["xianqsuit_attr_" .. index]
		for i = 1, 6 do
			local temp = {}
			if i == 1 then
				temp.suit_icon = suit_root:FindObj(format("label_%d/suit_icon", i))
				temp.suit_num = suit_root:FindObj(format("label_%d/suit_icon/suit_num", i))
			end

			temp.attr_label = suit_root:FindObj(format("label_%d", i))
			temp.attr_name = suit_root:FindObj(format("label_%d/label_str", i))
			temp.attr_value = suit_root:FindObj(format("label_%d/label_num", i))
			com_list[i] = temp
		end
		self.suit_attr_com_list[index] = com_list
	end

	return com_list
end

-- 设置衣橱套装属性
-- info = {suit_title = "", attr_list = {}}
function BaseTip:SetWardrobeSuitAttribute(info)
	self.attr_info_list[key_str.wardrobe_attribute] = info
	if not self.tips_obj_list[key_str.wardrobe_attribute] then
		self:LoadAttributeItem(key_str.wardrobe_attribute, BindTool.Bind(self.FlushWardrobeSuitAttribute, self))
	else
		self:FlushWardrobeSuitAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushWardrobeSuitAttribute()
	local node_list = self.tips_node_list[key_str.wardrobe_attribute]
	local attr_info = self.attr_info_list[key_str.wardrobe_attribute]
	if not node_list or not attr_info then
		return
	end

	if not self.wardrobe_attr_list then
		self.wardrobe_attr_list = {}
		for i = 1, 5 do
			if attr_info.theme_type == WARDROBE_THEME_TYPE.HMGOD then
				self.wardrobe_attr_list[i] = HmGodAttrRender.New(node_list["act_cell_" .. i])
			elseif attr_info.theme_type == WARDROBE_THEME_TYPE.GUIXUDREAM or attr_info.theme_type == WARDROBE_THEME_TYPE.CUSTOMIZED_SUIT then
				self.wardrobe_attr_list[i] = GuiXuDreamAttrRender.New(node_list["act_cell_" .. i])
			else
				self.wardrobe_attr_list[i] = WardrobeAttrRender.New(node_list["act_cell_" .. i])
			end
			self.wardrobe_attr_list[i]:SetFromItemTip(true)
		end
	end

	for k, v in ipairs(self.wardrobe_attr_list) do
		local data = attr_info.attr_list[k]
		if data then
			node_list["act_cell_" .. k]:SetActive(true)
			v:SetData(data)
		else
			node_list["act_cell_" .. k]:SetActive(false)
		end
	end

	node_list.title_label.tmp.text = attr_info.suit_title
	self.tips_obj_list[key_str.wardrobe_attribute]:SetActive(true)
end

-- 四象魂骨套装
-- info = {fs_type = "", suit_type = "", color = "", suit_data = ""}
function BaseTip:SetFightSoulSuitAttribute(info)
	self.attr_info_list[key_str.fs_suit_attribute] = info
	if not self.tips_obj_list[key_str.fs_suit_attribute] then
		self:LoadAttributeItem(key_str.fs_suit_attribute, BindTool.Bind(self.FlushFightSoulSuitAttribute, self))
	else
		self:FlushFightSoulSuitAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushFightSoulSuitAttribute()
	local node_list = self.tips_node_list[key_str.fs_suit_attribute]
	local attr_info = self.attr_info_list[key_str.fs_suit_attribute]
	if not node_list or not attr_info then
		return
	end

	node_list.title_label.text.text = attr_info.suit_data.suit_title

	local item_list = self.fs_suit_skill_list
	if not item_list then
		item_list = {}
		for i = 1, 3 do
			item_list[i] = FightSoulSuitSkillRender.New(node_list["act_cell_" .. i])
			item_list[i]:SetIndex(i)
		end
		self.fs_suit_skill_list = item_list
	end

	local info_list = attr_info.suit_data.info_list
	for i = 1, #item_list do
		local data = {suit_type = attr_info.suit_type, fs_type = attr_info.fs_type, color = attr_info.color}
		data.info = info_list[i]
		item_list[i]:SetData(data)
	end
	
	self.tips_obj_list[key_str.fs_suit_attribute]:SetActive(true)
end

-- 设置洗练属性
-- info = {{attr_name = "", attr_value = ""}}
function BaseTip:SetXilianAttribute(info)
	self.attr_info_list[key_str.xilian] = info
	if not self.tips_obj_list[key_str.xilian] then
		self:LoadAttributeItem(key_str.xilian, BindTool.Bind(self.FlushXilianAttribute, self))
	else
		self:FlushXilianAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushXilianAttribute()
	local xilian_node_list = self.tips_node_list[key_str.xilian]
	local xilian_attr_info = self.attr_info_list[key_str.xilian]
	if not xilian_node_list or not xilian_attr_info then
		return
	end
	local xilian_label = nil
	for i = 1, 4 do
		local info = xilian_attr_info[i]
		xilian_label = xilian_node_list["xilian_label_" .. i]
		if info then
			local color, name = string.match(info.attr_name, "<color=(.+)>(.+)</color>")
			if color and name then
				xilian_label.text.text = name
				xilian_label.text.color = Str2C3b(color)
			else
				xilian_label.text.text = info.attr_name
			end
			xilian_node_list["xilian_num_" .. i].text.text = info.attr_value
		end
		xilian_label:SetActive(info ~= nil)
	end
	self.tips_obj_list[key_str.xilian]:SetActive(true)
end

-- 设置宝石属性
-- info = {{is_lock = true, icon = "", name = "", attr_list = {attr_str = "", attr_num = ""}}}
function BaseTip:SetStoneAttribute(info)
	self.attr_info_list[key_str.stone] = info
	if not self.tips_obj_list[key_str.stone] then
		self:LoadAttributeItem(key_str.stone, BindTool.Bind(self.FlushStoneAttribute, self))
	else
		self:FlushStoneAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushStoneAttribute()
	local stone_node_list = self.tips_node_list[key_str.stone]
	local stone_attr_info = self.attr_info_list[key_str.stone]
	if not stone_node_list or not stone_attr_info then
		return
	end
	for i = 1, GameEnum.MAX_STONE_COUNT do
		local info = stone_attr_info[i]
		local stone_img = stone_node_list["stone_img_" .. i]
		local suo_img = stone_node_list["stone_suo_" .. i]
		if info and info.name then
			self:SetStoneAttrInfo(i, info.attr_list or {})
			stone_node_list["stone_name_" .. i].text.text = info.name
			local bundle, asset = ResPath.GetF2TipImages(info.icon)
			stone_img.image:LoadSprite(bundle, asset, function()
				stone_img.image:SetNativeSize()
			end)
			stone_img:SetActive(true)
			suo_img:SetActive(false)
		elseif info and info.is_lock then
			suo_img:SetActive(true)
			stone_img:SetActive(false)
		else
			stone_img:SetActive(false)
			suo_img:SetActive(false)
		end
		stone_node_list["stone_cao_" .. i]:SetActive(info ~= nil)
		stone_node_list["stone_name_" .. i]:SetActive(info and info.name ~= nil)
	end
	self.tips_obj_list[key_str.stone]:SetActive(true)
end

function BaseTip:SetStoneAttrInfo(index, info_list)
	local stone_attr_com_list = self:GetStoneAttrObjList(index)
	for i,com_list in ipairs(stone_attr_com_list) do
		if info_list[i] then
			com_list.attr_name.text.text = info_list[i].attr_str or ""
			com_list.attr_value.text.text = info_list[i].attr_num or ""
			com_list.attr_name:SetActive(true)
		else
			com_list.attr_name:SetActive(false)
		end
	end
end

function BaseTip:GetStoneAttrObjList(index)
	local com_list = self.stone_attr_com_list[index]
	if not com_list then
		com_list = {}
		local stone_node_list = self.tips_node_list[key_str.stone]
		local stone_root = stone_node_list["stone_name_" .. index]
		for i = 1, 3 do
			local temp = {}
			temp.attr_name = stone_root:FindObj("stone_attr_" .. i)
			temp.attr_value = temp.attr_name:FindObj("attr_num")
			com_list[i] = temp
		end
		self.stone_attr_com_list[index] = com_list
	end
	return com_list
end

--御灵
function BaseTip:SetYuLingAttribute(info)
	self.attr_info_list[key_str.yuling] = info
	if not self.tips_obj_list[key_str.yuling] then
		self:LoadAttributeItem(key_str.yuling, BindTool.Bind(self.FlushYulingAttribute, self))
	else
		self:FlushYulingAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushYulingAttribute()
	local yuling_node_list = self.tips_node_list[key_str.yuling]
	local yuling_attr_info = self.attr_info_list[key_str.yuling]

	yuling_node_list["yuling_add_per_label"].text.text = yuling_attr_info.add_per .. "%"
	for i = GameEnum.EQUIP_INDEX_TOUKUI, GameEnum.EQUIP_INDEX_XIANZHUO do
		local data = yuling_attr_info[i]
		local img_index = i + 1
		local yuling_icon = yuling_node_list["yuling_icon" .. img_index]
		local yuling_suo_icon = yuling_node_list["yuling_suo_icon" .. img_index]
		local bundel, asset
		if data.is_lock then
			yuling_suo_icon:SetActive(true)
			yuling_icon:SetActive(false)
		else
			yuling_suo_icon:SetActive(false)
			yuling_icon:SetActive(true)
			bundel, asset = ResPath.GetF2TipImages(data.icon)
			yuling_icon.image:LoadSprite(bundel, asset, function()
				yuling_icon.image:SetNativeSize()
			end)
		end
	end

	for i = 1, 5 do
		local attr_data = yuling_attr_info.attr_list[i] or {}
		local has_data = not IsEmptyTable(attr_data)
		if has_data then
			yuling_node_list["yuling_attr" .. i].text.text = string.format(Language.EquipmentImperialSpirit.TipsAttr, attr_data.attr_name, attr_data.value_str)
		end

		yuling_node_list["yuling_attr" .. i]:SetActive(has_data)
	end

	self.tips_obj_list[key_str.yuling]:SetActive(true)
end

--铸神
function BaseTip:SetZhuShenAttribute(info)
	self.attr_info_list[key_str.zhushen] = info
	if not self.tips_obj_list[key_str.zhushen] then
		self:LoadAttributeItem(key_str.zhushen, BindTool.Bind(self.FlushZhuShenAttribute, self))
	else
		self:FlushZhuShenAttribute()
		self:FlushCenterScrollHeight()
	end

end

function BaseTip:FlushZhuShenAttribute()
	local zhushen_node_list = self.tips_node_list[key_str.zhushen]
	local zhushen_attr_info = self.attr_info_list[key_str.zhushen]

	if not zhushen_node_list or not zhushen_attr_info then
		return
	end

	local zhushen_label = nil
	local attr_info = zhushen_attr_info.attr_list
	for i = 1, 2 do
		local info = attr_info[i]
		zhushen_label = zhushen_node_list["zhushen_label_" .. i]
		if info then
			zhushen_label.text.text = info.attr_name
			zhushen_node_list["zhushen_num_" .. i].text.text = info.attr_value
		end
		zhushen_label:SetActive(info ~= nil)
	end
	if zhushen_attr_info.title_name then
		zhushen_node_list["zhushen_title_name"].text.text = zhushen_attr_info.title_name
	end
	self.tips_obj_list[key_str.zhushen]:SetActive(true)
end

function BaseTip:SetBSJLAttribute(info)
	self.attr_info_list[key_str.stone_refine] = info
	if not self.tips_obj_list[key_str.stone_refine] then
		self:LoadAttributeItem(key_str.stone_refine, BindTool.Bind(self.FlushBSJLAttribute, self))
	else
		self:FlushBSJLAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushBSJLAttribute()
	local node_list = self.tips_node_list[key_str.stone_refine]
	local attr_info = self.attr_info_list[key_str.stone_refine]
	node_list["refine_desc"].text.text = attr_info.desc
	node_list["bsjl_title_label"].text.text = attr_info.title
	self.tips_obj_list[key_str.stone_refine]:SetActive(true)
end

-- 设置灵玉属性
-- info = {{is_lock = true, icon = "", name = "", attr_list = {attr_str = "", attr_num = ""}}}
function BaseTip:SetLingYuAttribute(info)
	self.attr_info_list[key_str.lingyu] = info
	if not self.tips_obj_list[key_str.lingyu] then
		self:LoadAttributeItem(key_str.lingyu, BindTool.Bind(self.FlushLingYuAttribute, self))
	else
		self:FlushLingYuAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushLingYuAttribute()
	local lingyu_node_list = self.tips_node_list[key_str.lingyu]
	local lingyu_attr_info = self.attr_info_list[key_str.lingyu]
	if not lingyu_node_list or not lingyu_attr_info then
		return
	end
	for i = 1, GameEnum.MAX_LINGYU_COUNT do
		local info = lingyu_attr_info[i]
		local lingyu_img = lingyu_node_list["lingyu_img_" .. i]
		local suo_img = lingyu_node_list["lingyu_suo_" .. i]
		if info and info.name then
			self:SetLingYuAttrInfo(i, info.attr_list or {})
			lingyu_node_list["lingyu_name_" .. i].text.text = info.name
			local bundle, asset = ResPath.GetF2TipImages(info.icon)
			lingyu_img.image:LoadSprite(bundle, asset, function()
				lingyu_img.image:SetNativeSize()
			end)

			suo_img:SetActive(false)
			lingyu_img:SetActive(true)
		elseif info and info.is_lock then
			suo_img:SetActive(true)
			lingyu_img:SetActive(false)
		else
			suo_img:SetActive(false)
			lingyu_img:SetActive(false)
		end
		lingyu_node_list["lingyu_cao_" .. i]:SetActive(info ~= nil)
		lingyu_node_list["lingyu_name_" .. i]:SetActive(info and info.name ~= nil)
	end
	self.tips_obj_list[key_str.lingyu]:SetActive(true)
end

function BaseTip:SetLingYuAttrInfo(index, info_list)
	local lingyu_attr_com_list = self:GetLingYuAttrObjList(index)
	for i,com_list in ipairs(lingyu_attr_com_list) do
		if info_list[i] then
			com_list.attr_name.text.text = info_list[i].attr_str or ""
			com_list.attr_value.text.text = info_list[i].attr_num or ""
			com_list.attr_name:SetActive(true)
		else
			com_list.attr_name:SetActive(false)
		end
	end
end

function BaseTip:GetLingYuAttrObjList(index)
	local com_list = self.lingyu_attr_com_list[index]
	if not com_list then
		com_list = {}
		local lingyu_node_list = self.tips_node_list[key_str.lingyu]
		local lingyu_root = lingyu_node_list["lingyu_name_" .. index]
		for i = 1, 3 do
			local temp = {}
			temp.attr_name = lingyu_root:FindObj("lingyu_attr_" .. i)
			temp.attr_value = temp.attr_name:FindObj("attr_num")
			com_list[i] = temp
		end
		self.lingyu_attr_com_list[index] = com_list
	end
	return com_list
end

-- 设置升品属性 or 极品属性
-- info = {title_name = "", attr_list = {attr_name = "", attr_value = ""}}
function BaseTip:SetShengPinAttribute(info)
	self.attr_info_list[key_str.shengpin] = info
	if not self.tips_obj_list[key_str.shengpin] then
		self:LoadAttributeItem(key_str.shengpin, BindTool.Bind(self.FlushShengPinAttribute, self))
	else
		self:FlushShengPinAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushShengPinAttribute()
	local shengpin_node_list = self.tips_node_list[key_str.shengpin]
	local shengpin_attr_info = self.attr_info_list[key_str.shengpin]
	if not shengpin_node_list or not shengpin_attr_info then
		return
	end
	local shengpin_label = nil
	for i = 1, 5 do
		local info = shengpin_attr_info.attr_list[i]
		shengpin_label = shengpin_node_list["shengpin_label_" .. i]
		if info then
			shengpin_label.text.text = info.attr_name
			shengpin_node_list["shengpin_num_" .. i].text.text = info.attr_value
		end
		shengpin_label:SetActive(info ~= nil)
	end
	if shengpin_attr_info.title_name then
		shengpin_node_list["shengpin_title_label"].text.text = shengpin_attr_info.title_name
	end
	self.tips_obj_list[key_str.shengpin]:SetActive(true)
end

-- 设置强化属性
-- info = {title_name = "", attr_list = {attr_name = "", attr_value = ""}}
function BaseTip:SetStrongAttribute(info)
	self.attr_info_list[key_str.strong] = info
	if not self.tips_obj_list[key_str.strong] then
		self:LoadAttributeItem(key_str.strong, BindTool.Bind(self.FlushStrongAttribute, self))
	else
		self:FlushStrongAttribute()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushStrongAttribute()
	local strong_node_list = self.tips_node_list[key_str.strong]
	local strong_attr_info = self.attr_info_list[key_str.strong]
	if not strong_node_list or not strong_attr_info then
		return
	end
	local strong_label = nil
	local attr_info = strong_attr_info.attr_list
	for i = 1, 8 do
		local info = attr_info[i]
		strong_label = strong_node_list["strong_label_" .. i]
		if info then
			strong_label.text.text = info.attr_name
			strong_node_list["strong_num_" .. i].text.text = info.attr_value
		end
		strong_label:SetActive(info ~= nil)
	end
	if strong_attr_info.title_name then
		strong_node_list["strong_title_name"].text.text = strong_attr_info.title_name
	end
	self.tips_obj_list[key_str.strong]:SetActive(true)
end

-- 设置物品描述
-- info = {{title = "", desc = ""}}
function BaseTip:SetItemDesc(info)
	self.attr_info_list[key_str.desc] = info
	if not self.tips_obj_list[key_str.desc] then
		self:LoadAttributeItem(key_str.desc, BindTool.Bind(self.FlushItemDesc, self))
	else
		self:FlushItemDesc()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushItemDesc()
	local desc_node_list = self.tips_node_list[key_str.desc]
	local item_desc_info = self.attr_info_list[key_str.desc]
	if not desc_node_list or not item_desc_info then
		return
	end
	for i = 1, 7 do
		local info = item_desc_info[i]
		if not IsEmptyTable(info) then
			desc_node_list["title_" .. i].text.text = info.title
			desc_node_list["desc_label_" .. i].text.text = info.desc
			desc_node_list["desc_" .. i]:SetActive(true)
		else
			desc_node_list["desc_" .. i]:SetActive(false)
		end
	end
	self.tips_obj_list[key_str.desc]:SetActive(true)
end

function BaseTip:SetExpDesc(info)
	self.attr_info_list[key_str.exp_desc] = info
	if not self.tips_obj_list[key_str.exp_desc] then
		self:LoadAttributeItem(key_str.exp_desc, BindTool.Bind(self.FlushExpDesc, self))
	else
		self:FlushExpDesc()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushExpDesc()
	local exp_node_list = self.tips_node_list[key_str.exp_desc]
	local exp_info = self.attr_info_list[key_str.exp_desc]
	if not exp_node_list or not exp_info then
		return
	end

	exp_node_list["exp_title"].text.text = exp_info.title or ""
	exp_node_list["desc_label"].text.text = exp_info.desc or ""
	exp_node_list["xiushi_label"].text.text = exp_info.xiushici or ""
	--exp_node_list["exp_title"]:SetActive(exp_info.title ~= nil)
	exp_node_list["desc_label"]:SetActive(exp_info.desc ~= nil)
	exp_node_list["xiushi_label"]:SetActive(exp_info.xiushici ~= nil)
	self.tips_obj_list[key_str.exp_desc]:SetActive(true)
end

-- 设置获取途径描述
-- info = {title = "", desc = "", xiushici = ""}
function BaseTip:SetGetWayDesc(info)
	self.attr_info_list[key_str.way_desc] = info
	if not self.tips_obj_list[key_str.way_desc] then
		self:LoadAttributeItem(key_str.way_desc, BindTool.Bind(self.FlushGetWayDesc, self))
	else
		self:FlushGetWayDesc()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushGetWayDesc()
	local way_node_list = self.tips_node_list[key_str.way_desc]
	local get_way_info = self.attr_info_list[key_str.way_desc]
	if not way_node_list or not get_way_info then
		return
	end
	way_node_list["get_way_title"].text.text = get_way_info.title or ""
	way_node_list["desc_label"].text.text = get_way_info.desc or ""
	way_node_list["xiushi_label"].text.text = get_way_info.xiushici or ""
	way_node_list["get_way_title"]:SetActive(get_way_info.title ~= nil)
	way_node_list["desc_label"]:SetActive(get_way_info.desc ~= nil)
	way_node_list["xiushi_label"]:SetActive(get_way_info.xiushici ~= nil)
	self.tips_obj_list[key_str.way_desc]:SetActive(true)
end

-- 设置物品技能(1主动技能 2专属技能 3被动技能)
-- info = {title = "", skill_list_1 = {[1] = {bundle = "", asset = "", click_func = "", skill_level = ""},title_img = ""}}
function BaseTip:SetItemSkill(info)
	self.attr_info_list[key_str.item_skil] = info
	if not self.tips_obj_list[key_str.item_skil] then
		self:LoadAttributeItem(key_str.item_skil, BindTool.Bind(self.FlushItemSkill, self))
	else
		self:FlushItemSkill()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushItemSkill()
	local skill_node_list = self.tips_node_list[key_str.item_skil]
	local item_skill_info = self.attr_info_list[key_str.item_skil]
	if not skill_node_list or not item_skill_info then
		return
	end

	if item_skill_info.title then
		skill_node_list["title_label"].text.text = item_skill_info.title
	end

	for i = 1, 3 do
		local skill_list = item_skill_info["skill_list_" .. i]
		if not IsEmptyTable(skill_list) then
			self:SetItemSkillGroup(i, skill_list)
			skill_node_list["skill_group_" .. i]:SetActive(true)
		else
			skill_node_list["skill_group_" .. i]:SetActive(false)
		end
	end

	self.tips_obj_list[key_str.item_skil]:SetActive(true)
end

function BaseTip:SetItemSkillGroup(index, skill_list)
	local com_list = self:GetItemSkillGroupObjList(index)
	local info = nil
	local skill_item = nil

	local function set_square_skill(bg_obj, level_bg_obj, level_obj)
		local bundle, asset = ResPath.GetCommonImages("a3_ty_skill_bg")
		local b_bundle, b_asset = ResPath.GetCommonImages("a2_jn_mzdi")
		if level_obj then
			RectTransform.SetAnchoredPositionXY(level_obj.rect, 0, 4)
		end

		if bg_obj then
			bg_obj.image:LoadSprite(bundle, asset, function()
				bg_obj.image:SetNativeSize()
			end)
		end

		if level_bg_obj then
			RectTransform.SetAnchoredPositionXY(level_bg_obj.rect, 0, -1)
			level_bg_obj.image:LoadSprite(b_bundle, b_asset, function()
				level_bg_obj.image:SetNativeSize()
			end)
		end
	end

	local function set_circle_skill(bg_obj, level_bg_obj, level_obj)
		local bundle, asset = ResPath.GetCommonImages("a3_ty_skill_bg")
		local b_bundle, b_asset = ResPath.GetCommonImages("a3_ty_jinengkuang_d")
		if level_obj then
			RectTransform.SetAnchoredPositionXY(level_obj.rect, 0, 5)
		end

		if bg_obj then
			bg_obj.image:LoadSprite(bundle, asset, function()
				bg_obj.image:SetNativeSize()
			end)
		end

		if level_bg_obj then
			RectTransform.SetAnchoredPositionXY(level_bg_obj.rect, 0, 2)
			level_bg_obj.image:LoadSprite(b_bundle, b_asset, function()
				level_bg_obj.image:SetNativeSize()
			end)
		end
	end

	for i = 1, 5 do
		info = skill_list[i]
		skill_item = com_list["skill_" .. i]
		if skill_item then
			if info then
				local level_bg_obj = com_list["level_bg_" .. i]
				local level_obj = com_list["skill_level_" .. i]
				local icon_obj = com_list["skill_icon_" .. i]
				icon_obj.image:LoadSprite(info.bundle, info.asset, function()
					icon_obj.image:SetNativeSize()
					local size_delta = RectTransform.GetSizeDelta(icon_obj.rect)
					if size_delta and size_delta.x > 76 then
						set_circle_skill(skill_item, level_bg_obj, level_obj)
					else
						set_square_skill(skill_item, level_bg_obj, level_obj)
					end
				end)

				level_obj.text.text = info.skill_level or ""
				level_bg_obj:SetActive(info.skill_level ~= nil)
				if info.click_func then
					skill_item.button:AddClickListener(info.click_func)
				end
			end
			skill_item:SetActive(info ~= nil)
		end
	end
	if skill_list.title_img then
		local bundel, asset = ResPath.GetF2TipImages(skill_list.title_img)
		com_list["title_img"].image:LoadSprite(bundel, asset, function()
			com_list["title_img"].image:SetNativeSize()
		end)
	end

	-- 第四个起超出框
	--com_list["scroll_rect"].scroll_rect.enabled = skill_list[4] ~= nil
	com_list["scroll_rect"].scroll_rect.verticalNormalizedPosition = 1
end

function BaseTip:GetItemSkillGroupObjList(index)
	local com_list = self.item_skil_com_list[index]
	if not com_list then
		local skill_node_list = self.tips_node_list[key_str.item_skil]
		local group_root = skill_node_list["skill_group_" .. index]
		local name_table = group_root:GetComponent(typeof(UINameTable))
		com_list = U3DNodeList(name_table, self)
		self.item_skil_com_list[index] = com_list
	end
	return com_list
end

-- 设置装备技能
-- info = {title_label = "", skill_name = "", skill_icon_id = "",
		-- cur_desc_title = "", cur_skill_desc = "", have_next = false, show_special_kuang = false,
		-- next_desc_tilte = "", next_skill_desc = ""}
function BaseTip:SetEquipSkill(info)
	self.attr_info_list[key_str.equip_skill] = info
	if not self.tips_obj_list[key_str.equip_skill] then
		self:LoadAttributeItem(key_str.equip_skill, BindTool.Bind(self.FlushEquipSkill, self))
	else
		self:FlushEquipSkill()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushEquipSkill()
	local skill_node_list = self.tips_node_list[key_str.equip_skill]
	local equip_skill_info = self.attr_info_list[key_str.equip_skill]
	if not skill_node_list or not equip_skill_info then
		return
	end

	skill_node_list["cur_desc_tilte"].text.text = equip_skill_info.cur_desc_title
	skill_node_list["skill_name"].text.text = equip_skill_info.skill_name
	skill_node_list["cur_skill_desc"].text.text = equip_skill_info.cur_skill_desc

	local have_next = equip_skill_info.have_next
	if have_next then
		skill_node_list["next_desc_tilte"].text.text = equip_skill_info.next_desc_tilte
		skill_node_list["next_skill_desc"].text.text = equip_skill_info.next_skill_desc
	end

	XUI.SetSkillIcon(skill_node_list["skill_icon_bg"], skill_node_list["skill_icon"], equip_skill_info.skill_icon_id)

	skill_node_list["next_desc"]:SetActive(have_next)
	skill_node_list["special_kuang"]:SetActive(equip_skill_info.show_special_kuang)
	skill_node_list["title_label"].text.text = equip_skill_info.title_label or Language.F2Tip.ZhuanShuSkill
	self.tips_obj_list[key_str.equip_skill]:SetActive(true)
end

-- 设置龙珠技能
-- info = {title_label = "", skill_num = "", skill_desc = ""}
function BaseTip:SetLongZhuSkill(info)
	self.attr_info_list[key_str.long_zhu_skill] = info
	if not self.tips_obj_list[key_str.long_zhu_skill] then
		self:LoadAttributeItem(key_str.long_zhu_skill, BindTool.Bind(self.FlushLongZhuSkill, self))
	else
		self:FlushLongZhuSkill()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushLongZhuSkill()
	local long_zhu_node_list = self.tips_node_list[key_str.long_zhu_skill]
	local long_zhu_info = self.attr_info_list[key_str.long_zhu_skill]
	if not long_zhu_node_list or not long_zhu_info then
		return
	end

	long_zhu_node_list["skill_title"].text.text = long_zhu_info.title_label
	long_zhu_node_list["skill_num"].text.text = long_zhu_info.skill_num
	long_zhu_node_list["skill_desc"].text.text = long_zhu_info.skill_desc
	self.tips_obj_list[key_str.long_zhu_skill]:SetActive(true)
end

-- 设置神兵技能
-- info = {title = "", skill_name = "", skill_desc = "", skill_icon_bundle = "", skill_icon_asset = ""}
function BaseTip:SetShenBingSkill(info)
	self.attr_info_list[key_str.shenbing_skill] = info
	if not self.tips_obj_list[key_str.shenbing_skill] then
		self:LoadAttributeItem(key_str.shenbing_skill, BindTool.Bind(self.FlushShenBingSkill, self))
	else
		self:FlushShenBingSkill()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushShenBingSkill()
	local skill_node_list = self.tips_node_list[key_str.shenbing_skill]
	local shenbing_skill_info = self.attr_info_list[key_str.shenbing_skill]
	if not skill_node_list or not shenbing_skill_info then
		return
	end
	
	skill_node_list["title_label"].text.text = shenbing_skill_info.title
	skill_node_list["skill_name"].text.text = shenbing_skill_info.skill_name
	skill_node_list["skill_desc"].text.text = shenbing_skill_info.skill_desc
	skill_node_list["skill_icon"].image:LoadSprite(shenbing_skill_info.skill_icon_bundle, shenbing_skill_info.skill_icon_asset, function()
		skill_node_list["skill_icon"].image:SetNativeSize()
	end)
	self.tips_obj_list[key_str.shenbing_skill]:SetActive(true)
end



-------------------------------------------------------------------------------------------------------

-- 设置右下角按钮事件
-- info = {{btn_name = "", btn_click = "",btn_red = 0 or 1},}
function BaseTip:SetBtnsClick(info)
	self.other_info_list[key_str.btns] = info
	if not self.tips_obj_list[key_str.btns] then
		self:LoadAttributeItem(key_str.btns, BindTool.Bind(self.FlushBtnsClick, self))
	else
		self:FlushBtnsClick()
	end
end

function BaseTip:AddBtnsClick(info)
	local btn_click_info = self.other_info_list[key_str.btns]
	if not btn_click_info then
		self:SetBtnsClick({[1] = info})
	else
		local mark = true
		for i = 1, #btn_click_info do
			if btn_click_info[i].btn_name == info.btn_name then
				mark = false
				break
			end
		end
		if mark then
			table.insert(btn_click_info, info)
			self.other_info_list[key_str.btns] = btn_click_info
			self:FlushBtnsClick()
		end
	end
end

function BaseTip:RemoveBtnsClick(info)
	local btn_click_info = self.other_info_list[key_str.btns]
	if btn_click_info then
		for k,v in pairs(btn_click_info) do
			if v.btn_name == info.btn_name then
				table.remove(btn_click_info, k)
				self:FlushBtnsClick()
				break
			end
		end
	end
end

function BaseTip:FlushBtnsClick()
	local btn_click_info = self.other_info_list[key_str.btns]
	local btns_node_list = self.tips_node_list[key_str.btns]
	if not btns_node_list or not btn_click_info then
		return
	end
	for i = 1, 5 do
		local info = btn_click_info[i]
		local tip_btn = btns_node_list["tip_btn_" .. i]
		if info then
			btns_node_list["btn_name_" .. i].text.text = info.btn_name
			btns_node_list["btn_red" .. i]:SetActive(info.btn_red and info.btn_red == 1)
			btns_node_list["tip_btn_icon" .. i]:SetActive(info.spend_item ~= nil and info.spend_item ~= 0)

			if info.spend_item ~= nil and info.spend_item ~= 0 then
				btns_node_list["tip_btn_icon" .. i].image:LoadSprite(ItemWGData.Instance:GetTipsItemIcon(info.spend_item))
			end

			tip_btn.button:AddClickListener(info.btn_click)
		end
		tip_btn:SetActive(info ~= nil)
	end
	self.tips_obj_list[key_str.btns]:SetActive(true)
end

-- 设置购买面板
--[[
info = {price = "", buy_count = "", max_buy_count = "", add_click = "", sub_click = "", price_type = "",
	shop_cfg_one = "", shop_cfg_two = "", toggle_select = 1, toggle_name = {}, toggle_click = func}
--]]
function BaseTip:SetBuyPanel(info)
	self.other_info_list[key_str.buy] = info
	if not self.tips_obj_list[key_str.buy] then
		self:LoadAttributeItem(key_str.buy, BindTool.Bind(self.FlushBuyPanel, self))
	else
		self:FlushBuyPanel()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:SetBuyPanelHide()
	self.other_info_list[key_str.buy] = nil
	if self.tips_obj_list[key_str.buy] then
		self.tips_obj_list[key_str.buy]:SetActive(false)
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushBuyPanel(is_load)
	local buy_panel_info = self.other_info_list[key_str.buy]
	local buy_node_list = self.tips_node_list[key_str.buy]
	if not buy_node_list or not buy_panel_info then
		if self.tips_obj_list[key_str.buy] then
			self.tips_obj_list[key_str.buy]:SetActive(false)
		end
		return
	end

	if is_load then
		buy_node_list["buy_add_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBuyAddBtn, self))
		buy_node_list["buy_sub_btn"].button:AddClickListener(BindTool.Bind(self.OnClickBuySubBtn, self))
		buy_node_list["buy_count_bg"].button:AddClickListener(BindTool.Bind(self.OnClickBuyCount, self))
		buy_node_list["buy_one_key_max_btn"].button:AddClickListener(BindTool.Bind(self.OnClickOneKeyMaxBtn, self))
		for i = 1, 2 do
			buy_node_list["buy_toggle_" .. i].toggle:AddClickListener(BindTool.Bind(self.OnClickPriceTypeToggle, self, i))
		end

		local name_table_1 = buy_node_list["price_item_1"]:GetComponent(typeof(UINameTable))
		self.buy_item_1 = {index = 1, node_list = U3DNodeList(name_table_1, self)}
		local name_table_2 = buy_node_list["price_item_2"]:GetComponent(typeof(UINameTable))
		self.buy_item_2 = {index = 2, node_list = U3DNodeList(name_table_2, self)}
	end

	buy_node_list["buy_count_label"].text.text = buy_panel_info.buy_count

	if self.buy_item_1 then
		if buy_panel_info.shop_cfg_one then
			self:FlushBuyItem(self.buy_item_1, buy_panel_info.shop_cfg_one)
		else
			self:FlushBuyItem(self.buy_item_1, buy_panel_info)
		end
		self:FlushBuyItemPrice(self.buy_item_1)
	end

	if self.buy_item_2 then
		if buy_panel_info.shop_cfg_two then
			self:FlushBuyItem(self.buy_item_2, buy_panel_info.shop_cfg_two)
			self:FlushBuyItemPrice(self.buy_item_2)
			buy_node_list["price_item_2"]:SetActive(true)
		else
			buy_node_list["price_item_2"]:SetActive(false)
		end
	end

	---[[ 新增支付货币类型切换
	if not IsEmptyTable(buy_panel_info.toggle_name) then
		local name_list = buy_panel_info.toggle_name
		for i = 1, 2 do
			if name_list[i] then
				buy_node_list["toggle_name_" .. i].text.text = name_list[i]

				local item = buy_node_list["buy_toggle_" .. i]
				item.toggle.isOn = i == buy_panel_info.toggle_select
				item:SetActive(true)
			else
				buy_node_list["buy_toggle_" .. i]:SetActive(false)
			end
		end

		buy_node_list["buy_toggle_group"]:SetActive(true)
	else
		buy_node_list["buy_toggle_group"]:SetActive(false)
	end
	--]]

	buy_node_list["limit_buy_type_str"]:SetActive(false)
	if buy_panel_info.item_id then
		local buy_list = __TableCopy(TipWGData.Instance:GetShopBuyList(buy_panel_info.item_id))
		if buy_list and buy_list[1] then
			local buy_data = buy_list[1]
			local limit_str_list = ShopWGData.Instance:ExplainComposeStr(buy_data.seq)

			if limit_str_list[1] then
				local str = limit_str_list[1].str
				str = string.gsub(str, "<.->", "")
				-- str = string.gsub(str, Language.Shop.LimitBuyStr, "")
				buy_node_list["limit_buy_type_str"]:SetActive(true)
				buy_node_list["limit_buy_type_str"].text.text = str
			end
		end
	end

	self.tips_obj_list[key_str.buy]:SetActive(true)
end

function BaseTip:FlushBuyItem(item, info)
	if not item then
		return
	end

	-- 货币Icon
	local bundel, asset = ResPath.GetCommonIcon(ItemTip_Shop_Money[info.price_type].url)
	local scale = ItemTip_Shop_Money[info.price_type].scale
	item.node_list.sell_price_icon.image:LoadSprite(bundel, asset, function ()
		item.node_list.sell_price_icon.image:SetNativeSize()
		item.node_list.sell_price_icon.transform:SetLocalScale(scale, scale, scale)
	end)
	item.node_list.sell_all_icon.image:LoadSprite(bundel, asset, function ()
		item.node_list.sell_all_icon.image:SetNativeSize()
		item.node_list.sell_all_icon.transform:SetLocalScale(scale, scale, scale)
	end)

	-- 价格
	local price = info.price or 0
	local old_price = info.old_price or 0
	if old_price > 0 then
		item.node_list.sell_price.text.text = Language.F2Tip.OldPriceStr
		item.node_list.sell_all_price.text.text = Language.F2Tip.NowPriceStr
		item.node_list.sell_price_label.text.text = old_price
	else
		item.node_list.sell_price.text.text = Language.F2Tip.OnePriceStr
		item.node_list.sell_all_price.text.text = Language.F2Tip.AllPriceStr
		item.node_list.sell_price_label.text.text = price
	end
	item.price = price
	item.old_price = old_price

	-- 折扣
	local discount_str = info.discount_str or info.jiaobiao_text or ""
	if discount_str ~= "" then
		discount_str = string.format("(%s)", discount_str)
	end
	item.discount_str = discount_str
	item.node_list.red_line:SetActive(discount_str ~= "")

	local buy_node_list = self.tips_node_list[key_str.buy]
	buy_node_list["disccount_label_" .. item.index].text.text = discount_str
end

function BaseTip:FlushBuyItemPrice(item)
	if not item.price then
		return
	end

	local buy_panel_info = self.other_info_list[key_str.buy]
	if item.discount_str and item.discount_str ~= "" then
		item.node_list.sell_price_label.text.text = item.old_price * buy_panel_info.buy_count
		item.node_list.sell_all_price_label.text.text = item.price * buy_panel_info.buy_count
	else
		item.node_list.sell_all_price_label.text.text = item.price * buy_panel_info.buy_count
	end
end

function BaseTip:OnClickBuyAddBtn()
	local buy_info = self.other_info_list[key_str.buy]
	local max_buy_count = buy_info.max_buy_count or 999
	if buy_info.buy_count < max_buy_count then
		buy_info.buy_count = buy_info.buy_count + 1
		local buy_node_list = self.tips_node_list[key_str.buy]
		buy_node_list["buy_count_label"].text.text = buy_info.buy_count

		self:FlushBuyItemPrice(self.buy_item_1)
		self:FlushBuyItemPrice(self.buy_item_2)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MaxValue)
	end
	if buy_info.add_click then
		buy_info.add_click()
	end
end

function BaseTip:OnClickBuySubBtn()
	local buy_info = self.other_info_list[key_str.buy]
	local min_buy_count = buy_info.min_buy_count or 1
	if buy_info.buy_count > min_buy_count then
		buy_info.buy_count = buy_info.buy_count - 1
		local buy_node_list = self.tips_node_list[key_str.buy]
		buy_node_list["buy_count_label"].text.text = buy_info.buy_count

		self:FlushBuyItemPrice(self.buy_item_1)
		self:FlushBuyItemPrice(self.buy_item_2)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.MinValue1)
	end
	if buy_info.sub_click then
		buy_info.sub_click()
	end
end

function BaseTip:OnClickOneKeyMaxBtn()
	local buy_info = self.other_info_list[key_str.buy]
	buy_info.buy_count = self:GetTrueBuyMaxCount()
	local buy_node_list = self.tips_node_list[key_str.buy]
	buy_node_list["buy_count_label"].text.text = buy_info.buy_count
	self:FlushBuyItemPrice(self.buy_item_1)
	self:FlushBuyItemPrice(self.buy_item_2)
end

function BaseTip:OnClickPriceTypeToggle(index)
	local buy_info = self.other_info_list[key_str.buy]
	if buy_info.toggle_click then
		buy_info.toggle_click(index)
	end
end

--根据当前的货币类型,得出能购买的最大上限
function BaseTip:GetTrueBuyMaxCount()
	local buy_info = self.other_info_list[key_str.buy]
	local temp_max_num = buy_info.max_buy_count or 999
	local my_cur_type_money = 0
	my_cur_type_money = RoleWGData.Instance:GetMoney(buy_info.price_type)

	local true_num = math.floor(my_cur_type_money / buy_info.price)
	if temp_max_num < true_num then
		true_num = temp_max_num
	elseif true_num <= 0 then
		true_num = 1
	end
	return true_num
end

function BaseTip:OnClickBuyCount()
	local buy_info = self.other_info_list[key_str.buy]
	local buy_node_list = self.tips_node_list[key_str.buy]
	local pop_num_view = TipWGCtrl.Instance:GetPopNumView()
	pop_num_view:Open()
	pop_num_view:SetText(1)
	pop_num_view:SetMinValue(1)
	pop_num_view:SetMaxValue(buy_info.max_buy_count)
	pop_num_view:SetOkCallBack(function (num)
		buy_info.buy_count = num
		buy_node_list["buy_count_label"].text.text = buy_info.buy_count
		self:FlushBuyItemPrice(self.buy_item_1)
		self:FlushBuyItemPrice(self.buy_item_2)
	end)
end

function BaseTip:GetBuyCount()
	local buy_info = self.other_info_list[key_str.buy]
	local buy_count = buy_info and (buy_info.buy_count or 1)
	return buy_count or 1
end

function BaseTip:GetBuyInfo()
	return self.other_info_list[key_str.buy]
end

-- 设置战力面板
-- info = {capability = ""}
function BaseTip:SetCapabilityPanel(info)
	if info == nil or info.capability <= 0 then
		return
	end

	self.other_info_list[key_str.capability] = info
	if not self.tips_obj_list[key_str.capability] then
		self:LoadAttributeItem(key_str.capability, BindTool.Bind(self.FlushCapabilityPanel, self))
	else
		self:FlushCapabilityPanel()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushCapabilityPanel()
	local cap_panel_info = self.other_info_list[key_str.capability]
	local cap_node_list = self.tips_node_list[key_str.capability]
	if not cap_node_list or not cap_panel_info then
		return
	end
	cap_node_list["capability_label"].text.text = cap_panel_info.capability
	self.tips_obj_list[key_str.capability]:SetActive(true)
end

-- 设置战力面板
-- info = {capability = ""}
function BaseTip:SetMaxCapabilityPanel(info)
	if info == nil or info.capability <= 0 then
		return
	end

	self.other_info_list[key_str.capability_max] = info
	if not self.tips_obj_list[key_str.capability_max] then
		self:LoadAttributeItem(key_str.capability_max, BindTool.Bind(self.FlushMaxCapabilityPanel, self))
	else
		self:FlushMaxCapabilityPanel()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushMaxCapabilityPanel()
	local cap_panel_info = self.other_info_list[key_str.capability_max]
	local cap_node_list = self.tips_node_list[key_str.capability_max]
	if not cap_node_list or not cap_panel_info then
		return
	end
	cap_node_list["capability_label"].text.text = cap_panel_info.capability
	self.tips_obj_list[key_str.capability_max]:SetActive(true)
end

-- 设置获取途径跳转面板
-- info = {path_list = {{label = "", btn_click = ""}}, is_on = true}
function BaseTip:SetGetWayPanel(info)
	self.other_info_list[key_str.get_way] = info
	if not self.tips_obj_list[key_str.get_way] then
		self:LoadAttributeItem(key_str.get_way, BindTool.Bind(self.FlushGetWayPanel, self))
	else
		self:FlushGetWayPanel()
	end
end

function BaseTip:FlushGetWayPanel()
	local get_way_info = self.other_info_list[key_str.get_way]
	local way_node_list = self.tips_node_list[key_str.get_way]
	if not way_node_list or not get_way_info then
		return
	end
	local item_way = nil
	local path_list = get_way_info.path_list or {}
	for i = 1, 6 do
		item_way = way_node_list["item_way_" .. i]
		if path_list[i] then
			item_way.button:AddClickListener(path_list[i].btn_click)
			local color = (path_list[i].special_bg and path_list[i].special_bg > 0) and COLOR3B.C7 or COLOR3B.WHITE 
			way_node_list["way_label_" .. i].text.text = ToColorStr(path_list[i].label, color)

			local btn_bg_name = (path_list[i].special_bg and path_list[i].special_bg > 0) and "a3_ty_btn_24" or "a3_ty_btn_16"
			way_node_list["item_way_" .. i].image:LoadSprite(ResPath.GetCommonButton(btn_bg_name))

			local bundle, asset = ResPath.GetCommonImages((path_list[i].special_bg and path_list[i].special_bg > 0) and "a3_sz_jt_1" or "a3_ty_guide_1")
			way_node_list["arrow_img_" .. i].image:LoadSprite(bundle, asset, function()
				way_node_list["arrow_img_" .. i].image:SetNativeSize()
			end)
			--[[local bundle, asset = ResPath.GetCommonIcon(path_list[i].icon)
			way_node_list["way_icon_" .. i].image:LoadSprite(bundle, asset, function()
				way_node_list["way_icon_" .. i].image:SetNativeSize()
			end)--]]
		end

		item_way:SetActive(path_list[i] ~= nil)
	end
	self.node_list["get_way_btn"]:SetActive(true)
	self.tips_obj_list[key_str.get_way]:SetActive(get_way_info.is_on)
	self:CheckGetWayWhith()
end

function BaseTip:OnClickOpenGetWayPanel()
	local get_way_panel = self.tips_obj_list[key_str.get_way]
	if get_way_panel then
		get_way_panel:SetActive(not get_way_panel.activeSelf)
	end
	self:CheckGetWayWhith()
end

function BaseTip:CheckGetWayWhith()
	local get_way_panel = self.tips_obj_list[key_str.get_way]
    if get_way_panel then
		local left_active = self.node_list["left_root"]:GetActive()
		self.node_list["get_way_root"]:SetActive(left_active and get_way_panel.activeSelf)

		local item_cfg = ItemWGData.Instance:GetItemConfig(self.item_id)
		if item_cfg then
			RectTransform.SetAnchoredPositionXY(get_way_panel.transform, 13, 0)
		end

		if not item_cfg or not item_cfg.grade_tips_type or item_cfg.grade_tips_type == "" or item_cfg.grade_tips_type < 1 then
			return
		end

		if get_way_panel.activeSelf then
			RectTransform.SetAnchoredPositionXY(get_way_panel.transform, 13, -40)
		end
	end
end

function BaseTip:SetItemId(item_id)
	self.item_id = item_id
end

function BaseTip:GetIsGetWayActive()
	local get_way_panel = self.tips_obj_list[key_str.get_way]
    if get_way_panel then
		local left_active = self.node_list["left_root"]:GetActive()
		return left_active and get_way_panel.activeSelf
	end

	return false
end

function BaseTip:SetGetWayRootActive(active)
	self.node_list["get_way_root"]:SetActive(active)
end

-- 设置市场相关信息(售价等)
-- info = {{price = "", from_view = ""}}
function BaseTip:SetMarketPanel(info)
	if info == nil or not info.price or not info.item_id then
		return
	end

	self.other_info_list[key_str.market_panel] = info 								-- 子面板存储数据
	-- 判断子面板是否已加载
	if not self.tips_obj_list[key_str.market_panel] then
		-- 加载子面板
		self:LoadAttributeItem(key_str.market_panel, BindTool.Bind(self.FlushMarketPanel, self, info))
	else
		self:FlushMarketPanel(info)
	end
end

function BaseTip:FlushMarketPanel(info)
	local market_panel = self.tips_obj_list[key_str.market_panel]
	if market_panel then
		market_panel:SetActive(true)
	end

	local market_panel_node_list = self.tips_node_list[key_str.market_panel] 		-- 获得子面板结点列表

	-- 售价
	local item_id = info.item_id
	local need_price = info.price
	local has_money = 0

	local cfg = MarketWGData.Instance:GetAuctionCfgByItemId(info.item_id)
	local auction_price_type = cfg.auction_price_type
	if auction_price_type == AUCTION_PRICE_TYPE.GOLD then
		has_money = RoleWGData.Instance:GetMoney(MoneyType.XianYu)
	elseif auction_price_type == AUCTION_PRICE_TYPE.GOLD_BIND then
		has_money = RoleWGData.Instance:GetMoney(MoneyType.BangYu)
	end

	local price_str = string.format(Language.Market.TipsPrice, need_price)
	if has_money >= need_price then
		price_str = ToColorStr(price_str, TIPS_COLOR.ATTR_VALUE)
	else
		price_str = ToColorStr(price_str, COLOR3B.D_RED)
	end
	market_panel_node_list["price"].text.text = price_str

	local icon_name = AUCTION_PRICE_TYPE_ICON[auction_price_type]
	if icon_name then
		local bundel, asset = ResPath.GetCommonIcon(icon_name)
		market_panel_node_list["price_icon"].image:LoadSprite(bundel, asset, function ()
			market_panel_node_list["price_icon"].image:SetNativeSize()
		end)
	end
end


-- 设置仙盟仓库相关信息
-- info = {{price = "", from_view = ""}}
function BaseTip:SetXianMengCangKuPanel(info)
	if info == nil then
		return
	end
	self.other_info_list[key_str.xianmeng_cangku_info] = info 								-- 子面板存储数据
	-- 判断子面板是否已加载
	if not self.tips_obj_list[key_str.xianmeng_cangku_info] then
		-- 加载子面板
		self:LoadAttributeItem(key_str.xianmeng_cangku_info, BindTool.Bind(self.FlushXianMengCangKuPanel, self, info))
	else
		self:FlushXianMengCangKuPanel(info)
	end
end

function BaseTip:FlushXianMengCangKuPanel(info)
	local xmck_panel = self.tips_obj_list[key_str.xianmeng_cangku_info]
	if xmck_panel then
		xmck_panel:SetActive(true)
	end

	local xmck_panel_node_list = self.tips_node_list[key_str.xianmeng_cangku_info] 		-- 获得子面板结点列表

	-- 售价
	local has_money = GuildCangKuWGData.Instance:GetStorgeScore()
	local item_cfg = ItemWGData.Instance:GetItemConfig(info.item_id)
	local star_level = info.star_level or 0
	local need_price = GuildCangKuWGData.Instance:GetStorageAccessScore(item_cfg.order,item_cfg.color,star_level)
	local price_str = need_price
	if has_money >= need_price then
		price_str = ToColorStr(price_str, TIPS_COLOR.ATTR_VALUE)
	else
		price_str = ToColorStr(price_str, COLOR3B.D_RED)
	end
	xmck_panel_node_list["need_jifen_text"].text.text = price_str
end

-- 设置装备合成tips相关信息
-- info = {title_desc = "", equip_list = {}, show_add = false, capability = 0, bottom_desc = ""}
function BaseTip:SetEquipTipsComposePanel(info)
	if info == nil then
		return
	end
	self.other_info_list[key_str.equip_compose_tips] = info 								-- 子面板存储数据
	if not self.tips_obj_list[key_str.equip_compose_tips] then
		self:LoadAttributeItem(key_str.equip_compose_tips, BindTool.Bind(self.FlushEquipTipsComposePanel, self))
	else
		self:FlushEquipTipsComposePanel()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushEquipTipsComposePanel()
	local info = self.other_info_list[key_str.equip_compose_tips]
	local node_list = self.tips_node_list[key_str.equip_compose_tips]
	if not info or not node_list then
		return
	end

	if info.capability and info.capability > 0 then
		node_list["capability_part"]:SetActive(true)
		node_list["cap_value"].text.text = info.capability
	else
		node_list["capability_part"]:SetActive(false)
	end

	local show_num = 5
	if not self.tips_compose_list then
		self.tips_compose_list = {}
		for i = 1, show_num do
			self.tips_compose_list[i] = BTRComposeItemRender.New(node_list["equip_" .. i])
		end
	end

	if info.bottom_desc then
		node_list["default_desc"].text.text = info.bottom_desc
	end
	node_list["default_desc"]:SetActive(info.bottom_desc ~= nil)

	node_list["title_desc"].text.text = info.title_desc

	local equip_num = #info.equip_list
	node_list["list_view"].scroll_rect.enabled = equip_num > 3
	local cur_index = 1
	for i = 1, show_num do
		self.tips_compose_list[i]:SetData(info.equip_list[i])
		if info.equip_list[i] and info.equip_list[i].is_cur then
			cur_index = i
		end
		if node_list["arrow_" .. i] then
			local show = i < equip_num
			local show_add = info.show_add and i < (equip_num - 1)
			if show then
				local asset_name = show_add and "a3_ty_jia" or "a3_ty_jt_1"
				node_list["arrow_" .. i].image:LoadSprite(ResPath.GetCommonImages(asset_name))
			end
			node_list["arrow_" .. i]:SetActive(show)
		end
	end
	if equip_num > 3 and cur_index >= 3 then
		node_list["list_view"].scroll_rect:DoHorizontalPosition(0, 1, 0.1, nil)
	else
		node_list["list_view"].scroll_rect:DoHorizontalPosition(1, 0, 0.1, nil)
	end

	self.tips_obj_list[key_str.equip_compose_tips]:SetActive(true)
end

--info {addition_str = ""}
--骑宠装备品质加成
function BaseTip:SetMountPetEquipQualityAdditionInfo(info)
	if info == nil then
		return
	end
	self.other_info_list[key_str.quality_addition] = info
	if not self.tips_obj_list[key_str.quality_addition] then
		self:LoadAttributeItem(key_str.quality_addition, BindTool.Bind(self.FlushQualityAddition, self))
	else
		self:FlushQualityAddition()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushQualityAddition()
	local info = self.other_info_list[key_str.quality_addition]
    local node_list = self.tips_node_list[key_str.quality_addition]
	if not info or not node_list then
		return
	end
	node_list.pet_quality_addition.text.text = info.addition_str
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(node_list.pet_quality_addition.rect)
	self.tips_obj_list[key_str.quality_addition]:SetActive(true)
end

function BaseTip:SetWuXingInfo(index)
	if self.is_load then
		self.node_list["tianshen_wuxing"]:SetActive(index > 0)
		if index > 0 then
			local bundel,asset = ResPath.GetF2CommonImages("wuxing_small_"..index)
			self.node_list["tianshen_wuxing"].image:LoadSpriteAsync(bundel,asset)
			self.node_list["tianshen_wuxing"].image:SetNativeSize()
		end
	end
end


--天神八卦牌套装信息
function BaseTip:SetBaGuaSlotInfo(info)
	if info == nil then
		return
	end
	self.other_info_list[key_str.bagua_slot_info] = info 								-- 子面板存储数据
	if not self.tips_obj_list[key_str.bagua_slot_info] then
		self:LoadAttributeItem(key_str.bagua_slot_info, BindTool.Bind(self.FlushBaGuaSlotPanel, self))
	else
		self:FlushBaGuaSlotPanel()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushBaGuaSlotPanel()
	local info = self.other_info_list[key_str.bagua_slot_info]
	local node_list = self.tips_node_list[key_str.bagua_slot_info]
	if not info or not node_list then
		return
	end
	local cur_select_bagua = TianShenBaGuaWGData.Instance:GetBaGuaImageByBaGua()
	local asset1, bundle1 = ResPath.GetF2RawImagesPNG("a3_zs_ct_" .. cur_select_bagua)
	node_list.bagua_image.raw_image:LoadSprite(asset1, bundle1, function()
		node_list.bagua_image.raw_image:SetNativeSize()
	end)

	local bundel, asset
	local name = ""
	local bagua_equip_info = TianShenBaGuaWGData.Instance:GetTianShenBaGuaEquipInfo(info.bagua_index)
	for i = 1, 8 do
		if info.bagua_index then
			local part_base_info = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfoByindex(info.bagua_index, 1, i - 1)
			local item_cfg = ItemWGData.Instance:GetItemConfig(part_base_info.itemid)
			bundel,asset = ResPath.GetItem(item_cfg.icon_id)
			node_list["bagua_icon"..i].image:LoadSpriteAsync(bundel,asset)
			if info.activity_list then
				XUI.SetGraphicGrey(node_list["bagua_icon"..i], not info.activity_list[i])
			else
				XUI.SetGraphicGrey(node_list["bagua_icon"..i], true)
			end

			if bagua_equip_info[i] and bagua_equip_info[i].item_id > 0 then
				local base_cfg = TianShenBaGuaWGData.Instance:GetBaGuaBaseInfo(bagua_equip_info[i].item_id)
				node_list["star_bg_" .. i]:SetActive(true)
				for t=1,3 do
					node_list["star"..i..t]:SetActive(base_cfg.star >= t)--and base_cfg.star >=2)
				end
			else
				node_list["star_bg_" .. i]:SetActive(false)
				for t=1,3 do
					node_list["star"..i..t]:SetActive(false)
				end
			end
		else
			XUI.SetGraphicGrey(node_list["bagua_icon"..i], true)
		end
		node_list["bagua_hl"..i]:SetActive(i == info.bagua_part + 1)
	end
	node_list.solt_desc.text.text = info.active_part_str or ""
	node_list.slot_attr.text.text = info.skill_str or ""
	self.tips_obj_list[key_str.bagua_slot_info]:SetActive(true)
end

function BaseTip:SetDingWeiInfo(dingwei_text)
	if self.is_load then
		local is_dingwei = dingwei_text and dingwei_text ~= ""
		self.node_list["tianshendingwei_bg"]:SetActive(is_dingwei)
		if is_dingwei then
			self.node_list["tianshen_dingwei"].text.text = dingwei_text
		end
	end
end

function BaseTip:OnClickSkillShow()
	if self.show_tianshen_skill_index then
		self.show_tianshen_skill_index()
	end
end

function BaseTip:SetSkillShowInfo(click_func)
	self.node_list["skill_show_btn"]:SetActive(true)
	self.show_tianshen_skill_index = click_func
end

-- 灵兽资质展示
function BaseTip:SetBeastFlairShowInfo(info)
	self.attr_info_list[key_str.beast_flair] = info
	if not self.tips_obj_list[key_str.beast_flair] then
		self:LoadAttributeItem(key_str.beast_flair, BindTool.Bind(self.FlushItemBeastFlair, self))
	else
		self:FlushItemBeastFlair()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:GetBeastFlairGroupObjList()
	if not self.item_beast_flair_list then
		self.item_beast_flair_list = {}

		local skill_node_list = self.tips_node_list[key_str.beast_flair]
		if skill_node_list then
			for i = 1, 6 do
				local incubate_obj = skill_node_list.attribute_root:FindObj(string.format("flair_%d", i))
				local name_table = incubate_obj:GetComponent(typeof(UINameTable))
				local com_list = U3DNodeList(name_table, self)
				self.item_beast_flair_list[i] = com_list
			end
		end
	end
	return self.item_beast_flair_list
end

function BaseTip:FlushItemBeastFlair()
	self.tips_obj_list[key_str.beast_flair]:SetActive(true)
	local skill_node_list = self.tips_node_list[key_str.beast_flair]
	local item_skill_info = self.attr_info_list[key_str.beast_flair]
	local map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(item_skill_info.beast_id)
	if not skill_node_list or not item_skill_info or not map_data then
		self.tips_obj_list[key_str.beast_flair]:SetActive(false)
		return
	end

	if item_skill_info.title then
		skill_node_list["title_label"].text.text = item_skill_info.title
	end

	if item_skill_info.skill_title then
		skill_node_list["skill_title_label"].text.text = item_skill_info.skill_title
	end

	local beast_cfg = map_data.beast_data
    if map_data and map_data.beast_data then
		-- 普攻
		local skill_id = map_data.beast_data.normal_skill_id
		local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)

		skill_node_list.skill_normal:CustomSetActive(skill_id > 0)
		if skill_id > 0 then
			if client_cfg then
				skill_node_list["nor_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
			end
			skill_node_list["skill_normal"].button:AddClickListener(item_skill_info.nor_skill_fun)
		end
		
		-- 技能
		skill_id = map_data.beast_data.skill_id
		--去技能数据类查
		client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
		skill_node_list.skill:CustomSetActive(skill_id > 0)
		if skill_id > 0 then
			if client_cfg then
				skill_node_list["skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
			end
			skill_node_list["skill"].button:AddClickListener(item_skill_info.skill_fun)
		end

		-- 专属技能
		local be_skill_id = map_data.beast_data.be_skill_id or 0
		skill_node_list.skill_special:CustomSetActive(be_skill_id > 0)
		if be_skill_id > 0 then
			local be_skill_cfg = ControlBeastsWGData.Instance:GetBeastBeSkillCfgBySeq(be_skill_id)
			if be_skill_cfg then
				skill_node_list["special_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(be_skill_cfg.skill_icon))
			end

			skill_node_list["skill_special"].button:AddClickListener(item_skill_info.sp_skill_fun)
		end
    end

	local cell_list = self:GetBeastFlairGroupObjList()
	local refine_attr_list = {}
	local effort_value = 0
	if item_skill_info.bag_id then
		local beast_data = ControlBeastsWGData.Instance:GetBeastDataById(item_skill_info.bag_id)
		if beast_data and beast_data.server_data then
			refine_attr_list = beast_data.server_data.refine_attr_list
		end
	end


    local refine_seq = beast_cfg and beast_cfg.refine_seq or 0
	local list_data = ControlBeastsWGData.Instance:GetBeastRefineCfgListBySeq(refine_seq)
	skill_node_list.title_label:CustomSetActive(cell_list ~= nil and list_data ~= nil)
	skill_node_list.attribute_root:CustomSetActive(cell_list ~= nil and list_data ~= nil)

	if cell_list and list_data then
		for index, com_list in ipairs(cell_list) do
			local data = list_data[index - 1]
			local attr_name = EquipmentWGData.Instance:GetAttrName(data.attr_id, true)
			com_list.arrow:CustomSetActive(not item_skill_info.is_beast)
			com_list.add_value:CustomSetActive(not item_skill_info.is_beast)
			com_list.attr_name.text.text = attr_name

			if (not item_skill_info.is_beast) and map_data and map_data.beast_flair_preview then	-- 灵兽蛋展示
				-- 获取资质加成属性数据
				com_list.attr_value.text.text = data.min_attr_value
				com_list.add_value.text.text = data.max_attr_value
			else	--灵兽展示
				local refine_data = refine_attr_list[index]
				local value_str = ""
				if refine_data then
					local now_bouns_ten_thousand = refine_data.rand_value / BEAST_DEFINE.BEASTS_BOUNS_PER
					local now_bouns_value = math.floor(refine_data.attr_value * (now_bouns_ten_thousand + 1))
					value_str = now_bouns_value
				end

				com_list.attr_value.text.text = value_str
			end
		end
	end
end

-- 设置展示模型底的状态
function BaseTip:SetLeftShowModelDiVisible(visible)
	self.node_list["left_root_show_model_di"]:SetActive(visible)
end

-- 设置展示模型居中
function BaseTip:SetLeftShowModelMiddle(bool)
	if bool then
		RectTransform.SetAnchoredPositionXY(self.node_list.left_root_show_model_di.rect, -16, -172)
		RectTransform.SetAnchoredPositionXY(self.node_list.content.rect, -145, 46)
	else
		RectTransform.SetAnchoredPositionXY(self.node_list.left_root_show_model_di.rect, 49, -172)
		RectTransform.SetAnchoredPositionXY(self.node_list.content.rect, -80, 46)
	end
end

function BaseTip:SetGiftItemListShowInfo(info)
	self.other_info_list[key_str.gift_item_list] = info
	if not self.tips_obj_list[key_str.gift_item_list] then
		self:LoadAttributeItem(key_str.gift_item_list, BindTool.Bind(self.FlushGiftItemListPart, self))
	else
		self:FlushGiftItemListPart()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushGiftItemListPart()
	local info = self.other_info_list[key_str.gift_item_list]
	local node_list = self.tips_node_list[key_str.gift_item_list]
	if not info or not node_list then
		return
	end

	if not self.show_gift_item_list then
		self.show_gift_item_list = {}
	end

	local max_obj_num = 6			-- 预制体上的列表数量
	local real_num = #info
	local max_group_num = 0
	if not IsEmptyTable(info) then
		max_group_num = math.ceil((real_num / 5))
		max_group_num = math.max(max_group_num, 1)
	end

	for i = 1, max_obj_num do
		local group_node = node_list["item_group" .. i]
		if group_node then
			group_node:CustomSetActive(i <= max_group_num)
			local need_new_cell = false
			if not self.show_gift_item_list[i] then
				self.show_gift_item_list[i] = {}
				need_new_cell = true
			end

			for cell_index = 1, 5 do
				if need_new_cell then
					self.show_gift_item_list[i][cell_index] = TipShowGiftItem.New(group_node:FindObj("tip_show_gift_item" .. cell_index))
				end

				local data_index = i > 1 and (((i - 1) * 5) + cell_index) or cell_index
				self.show_gift_item_list[i][cell_index]:SetData(info[data_index])
			end
		end
	end

	self.tips_obj_list[key_str.gift_item_list]:SetActive(max_group_num > 0)
end

--设置抽奖礼包信息
function BaseTip:SetDeawGiftInfo(info)
	self.attr_info_list[key_str.draw_gift] = info
	if not self.tips_obj_list[key_str.draw_gift] then
		self:LoadAttributeItem(key_str.draw_gift, BindTool.Bind(self.FlushDeawGiftInfo, self))
	else
		self:FlushDeawGiftInfo()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushDeawGiftInfo()
	local draw_gift_node_list = self.tips_node_list[key_str.draw_gift]
	local draw_gift_info = self.attr_info_list[key_str.draw_gift]
	if not draw_gift_node_list or not draw_gift_info then
		return
	end

	local show_reward_cfg = DrawGiftWGData.Instance:GetDrawGiftShowRewardList(draw_gift_info.id)
	if IsEmptyTable(show_reward_cfg) then
		return
	end

	if not self.is_load then
		return
	end

	if not self.draw_gift_grid_list then
		self.draw_gift_grid_list = AsyncBaseGrid.New()
		self.draw_gift_grid_list:CreateCells({col = 3,change_cells_num = 1, list_view = draw_gift_node_list["draw_gift_reward_list"],
		assetBundle = "uis/view/draw_gift_ui_prefab", assetName = "reward_cell",  itemRender = DrawGiftRewardRender})
		self.draw_gift_grid_list:SetStartZeroIndex(false)
	end

	self.draw_gift_grid_list:SetDataList(show_reward_cfg)

	if not self.draw_gift_baodi_list then
		self.draw_gift_baodi_list = AsyncListView.New(DrawGiftBaoDiRender, draw_gift_node_list["draw_gift_baidi_list"])
		self.draw_gift_baodi_list:SetStartZeroIndex(false)
	end

	local baodi_reward_list = DrawGiftWGData.Instance:GetDrawGiftBaoDiRewardInfo(draw_gift_info.id)
	if IsEmptyTable(baodi_reward_list) then
		return
	end

	self.draw_gift_baodi_list:SetDataList(baodi_reward_list)
	local draw_count = DrawGiftWGData.Instance:GetDrawGiftCountById(draw_gift_info.id)
	local next_need_count = DrawGiftWGData.Instance:GetDrawGiftBaoDiNextNeedCount(draw_gift_info.id)

	draw_gift_node_list["draw_gift_baodi_text"].text.text = string.format(Language.DrawGift.BaoDi, draw_count, next_need_count)

	self.tips_obj_list[key_str.draw_gift]:SetActive(true)
end


-- 设置通用展示道具列表.
function BaseTip:SetShowItemListInfo(info)
	self.other_info_list[key_str.common_show_item_list] = info
	if not self.tips_obj_list[key_str.common_show_item_list] then
		self:LoadAttributeItem(key_str.common_show_item_list, BindTool.Bind(self.FlushShowItemListInfo, self))
	else
		self:FlushShowItemListInfo()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushShowItemListInfo()
	local node_list = self.tips_node_list[key_str.common_show_item_list]
	local info = self.other_info_list[key_str.common_show_item_list]
	if not node_list or not info then
		return
	end

	if not self.common_show_item_list then
		self.common_show_item_list = {}
		for i = 1, 2 do
			self.common_show_item_list[i] = AsyncListView.New(ItemCell, node_list["suit_" .. i])
			self.common_show_item_list[i]:SetStartZeroIndex(true)
		end
	end

	for i = 1, 2 do
		if info.data and info.data["suit_item_list_" .. i] then
			self.common_show_item_list[i]:SetDataList(info.data["suit_item_list_" .. i])
		end
	end

	node_list.show_item_list_title_label.text.text = info.title and  info.title or ""
	self.tips_obj_list[key_str.common_show_item_list]:SetActive(true)
end

-- 设置图鉴羁绊
-- info = {title = "", desc = "", xiushici = ""}
function BaseTip:SetShanHaiJingZuHe(info)
	self.attr_info_list[key_str.shanhaijing_zuhe] = info
	if not self.tips_obj_list[key_str.shanhaijing_zuhe] then
		self:LoadAttributeItem(key_str.shanhaijing_zuhe, BindTool.Bind(self.FlushShanHaiJingZuHe, self))
	else
		self:FlushShanHaiJingZuHe()
		self:FlushCenterScrollHeight()
	end
end

function BaseTip:FlushShanHaiJingZuHe()
	local shanhaijing_zuhe_list = self.tips_node_list[key_str.shanhaijing_zuhe]
	local get_way_info = self.attr_info_list[key_str.shanhaijing_zuhe]
	if not shanhaijing_zuhe_list or not get_way_info then
		return
	end

	shanhaijing_zuhe_list["text_item"].text.text = get_way_info.item_name or ""

	self.tips_obj_list[key_str.shanhaijing_zuhe]:SetActive(true)
end

-- 幻兽内丹装备评分词条
function BaseTip:SetBeastAlchemyScoreInfo(equip_info)
	self.attr_info_list[key_str.beast_alchemy_score] = equip_info
	if not self.tips_obj_list[key_str.beast_alchemy_score] then
		self:LoadAttributeItem(key_str.beast_alchemy_score, BindTool.Bind(self.FlushBeastAlchemyScoreInfo, self))
	else
		self:FlushBeastAlchemyScoreInfo()
		self:FlushCenterScrollHeight()
	end
end

-- 幻兽内丹装备评分词条
function BaseTip:FlushBeastAlchemyScoreInfo()
	local beast_alchemy_node_list = self.tips_node_list[key_str.beast_alchemy_score]
	local beast_alchemy_data_info = self.attr_info_list[key_str.beast_alchemy_score]
	if not beast_alchemy_node_list or not beast_alchemy_data_info then
		return
	end

	beast_alchemy_node_list.alchemy_score_title_label.text.text = Language.F2Tip.BeastAlchemyScoreTitle
	self.tips_obj_list[key_str.beast_alchemy_score]:SetActive(true)
	local equip_info = beast_alchemy_data_info.equip_info
	local words_list = equip_info.words_list or {}
	local is_show_tag = false
	if beast_alchemy_data_info.is_wear then
		self:SetTopLeftIcon()
	end

	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
		local ward_data = words_list[j]
		if ward_data then
			local random_value = ward_data.words_seq % 100
			local real_seq = math.floor(ward_data.words_seq / 100)

			local cfg = ControlBeastsCultivateWGData.Instance:GetWordCfg(real_seq)
			if cfg and cfg.simple_desc and cfg.simple_desc ~= "" then
				beast_alchemy_node_list["alchemy_tag_".. j]:CustomSetActive(true)
				is_show_tag = true
				local color = cfg.simple_desc_bg == 0 and COLOR3B.DEFAULT or "#fff9b8"
				local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_hsnd_bq_%d", cfg.simple_desc_bg or 0))
				beast_alchemy_node_list["alchemy_tag_".. j].image:LoadSprite(bundle, asset)
				beast_alchemy_node_list["alchemy_tag_txt_".. j].text.text = ToColorStr(cfg.simple_desc, color)
			else
				beast_alchemy_node_list["alchemy_tag_".. j]:CustomSetActive(false)
			end
		else
			beast_alchemy_node_list["alchemy_tag_".. j]:CustomSetActive(false)
		end
	end

	-- 计算装备评分
	beast_alchemy_node_list.alchemy_tag:CustomSetActive(is_show_tag)
	local score = ControlBeastsCultivateWGData.Instance:GetAlchemyBeastEquipScore(
		equip_info, 
		beast_alchemy_data_info.is_bag_equip,
		beast_alchemy_data_info.fight_slot, 
		beast_alchemy_data_info.equip_slot, 
		beast_alchemy_data_info.equip_slot_lv
	)
	beast_alchemy_node_list.alchemy_score_txt.text.text = score
end

-- 幻兽装备词条附加
function BaseTip:SetBeastAlchemyAdditionalInfo(equip_info)
	self.attr_info_list[key_str.beast_alchemy_additional] = equip_info
	if not self.tips_obj_list[key_str.beast_alchemy_additional] then
		self:LoadAttributeItem(key_str.beast_alchemy_additional, BindTool.Bind(self.FlushBeastAlchemyAdditionalInfo, self))
	else
		self:FlushBeastAlchemyAdditionalInfo()
	end
end

-- 幻兽装备词条附加
function BaseTip:FlushBeastAlchemyAdditionalInfo()
	local beast_alchemy_additional_node_list = self.tips_node_list[key_str.beast_alchemy_additional]
	local beast_alchemy_additional_data_info = self.attr_info_list[key_str.beast_alchemy_additional]
	if not beast_alchemy_additional_node_list or not beast_alchemy_additional_data_info then
		return
	end

	local equip_info = beast_alchemy_additional_data_info.equip_info
	local words_list = equip_info.words_list or {}
	self.tips_obj_list[key_str.beast_alchemy_additional]:SetActive(true)
	local is_show_additional = false

	for j = 0, BEAST_EQUIP_OPERATE_TYPE.MAX_BEAST_EQUIP_WORDS_SEQ - 1 do
		local ward_data = words_list[j]
		if ward_data then
			local random_value = ward_data.words_seq % 100
			local real_seq = math.floor(ward_data.words_seq / 100)
			local cfg = ControlBeastsCultivateWGData.Instance:GetWordCfg(real_seq)
	
			if cfg then
				is_show_additional = true
				beast_alchemy_additional_node_list["additional_" .. j]:CustomSetActive(true)
				beast_alchemy_additional_node_list["operate_btn_" .. j]:CustomSetActive(cfg.words_type == 2)
				
				if cfg.words_type == 1 then
					beast_alchemy_additional_node_list["additional_name_" .. j].text.text = EquipmentWGData.Instance:GetAttrName(cfg.param1, true, false)
					beast_alchemy_additional_node_list["additional_value_" .. j].text.text = math.floor(cfg.param2 * (random_value / 100))
				else
					local skill_cfg = ControlBeastsCultivateWGData.Instance:GetSkillDescBySkillId(cfg.param1)
					if skill_cfg then
						local skill_show_fun = function()
							local show_data = {
								icon = skill_cfg.skill_icon,
								top_text = skill_cfg.skill_name,
								body_text = skill_cfg.skill_des,
								x = -360,
								y = 0,
								set_pos2 = true,
								hide_next = true,
								is_active_skill = false,
								skill_level = skill_cfg.skill_level or 0,
							}
							NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
						end
	
						local skill_color = skill_cfg.color or COMMON_CONSTS.NUMBER_ZERO
						beast_alchemy_additional_node_list["additional_name_" .. j].text.text = ToColorStr(skill_cfg.skill_name, ITEM_COLOR[skill_color])
						if skill_cfg.skill_level ~= 0 then
							beast_alchemy_additional_node_list["additional_value_" .. j].text.text = string.format(Language.Common.Level1, skill_cfg.skill_level)
						else
							beast_alchemy_additional_node_list["additional_value_" .. j].text.text = ""
						end
	
						XUI.AddClickEventListener(beast_alchemy_additional_node_list["operate_btn_" .. j], skill_show_fun)               -- 属性详情
					end
				end
			else
				beast_alchemy_additional_node_list["additional_" .. j]:CustomSetActive(false)
			end
		else
			beast_alchemy_additional_node_list["additional_" .. j]:CustomSetActive(false)
		end
	end

	-- 计算装备评分
	beast_alchemy_additional_node_list.alchemy_additional_title_label:CustomSetActive(is_show_additional)
	beast_alchemy_additional_node_list.alchemy_additional_list:CustomSetActive(is_show_additional)
	local cfg = ControlBeastsCultivateWGData.Instance:GetEquipCfg(equip_info.item_id)
	local aim_suit_type = cfg and cfg.suit_type or -1
	local suit_now_num = 1

	if (not beast_alchemy_additional_data_info.is_bag_equip) and beast_alchemy_additional_data_info.fight_slot ~= nil then
		suit_now_num = ControlBeastsCultivateWGData.Instance:GetFightBeastAllEquipSuitNum(beast_alchemy_additional_data_info.fight_slot, aim_suit_type)
	end

    -- 套装属性
    if self.alchemy_suit_list == nil then
        self.alchemy_suit_list = {}
        for i = 1, 6 do
            local attr_obj = beast_alchemy_additional_node_list.alchemy_suit_root:FindObj(string.format("alchemy_suit_render_%d", i))
            if attr_obj then
                local cell = AlchemySuitTipsAttrRender.New(attr_obj)
                cell:SetIndex(i)
                self.alchemy_suit_list[i] = cell
            end
        end
    end

	local list = ControlBeastsCultivateWGData.Instance:GetSuitListCfg(aim_suit_type)
	beast_alchemy_additional_node_list.alchemy_suit_title_label:CustomSetActive(not IsEmptyTable(list))
	beast_alchemy_additional_node_list.alchemy_suit_root:CustomSetActive(not IsEmptyTable(list))

	if not IsEmptyTable(list) then
		for i, alchemy_suit_cell in ipairs(self.alchemy_suit_list) do
			alchemy_suit_cell:SetVisible(list[i] ~= nil)
	
			if list[i] ~= nil then
				alchemy_suit_cell:SetNumSuitNum(suit_now_num)
				alchemy_suit_cell:SetSuitStatusColor('#DCDCDCFF', '#A8A5A4FF')
				alchemy_suit_cell:SetData(list[i])
			end
		end
	end

	self:FlushCenterScrollHeight()
end


