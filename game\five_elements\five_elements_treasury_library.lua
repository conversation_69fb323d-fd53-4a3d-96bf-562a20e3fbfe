FiveElementsTreasuryLibraryView = FiveElementsTreasuryLibraryView or BaseClass(SafeBaseView)

function FiveElementsTreasuryLibraryView:__init()
	self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(932, 590)})
    self:AddViewResource(0, "uis/view/five_elements_ui_prefab", "five_elements_treasury_library")
end

function FiveElementsTreasuryLibraryView:__delete()

end

function FiveElementsTreasuryLibraryView:ReleaseCallBack()
	if self.library_grid_list then
        self.library_grid_list:DeleteMe()
        self.library_grid_list = nil
    end
end


function FiveElementsTreasuryLibraryView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.TianShenLingHe.LibraryTitle
	local bundle = "uis/view/five_elements_ui_prefab"
	local asset = "treasury_library_cell"
	if self.library_grid_list == nil then
		self.library_grid_list = AsyncBaseGrid.New()
		self.library_grid_list:CreateCells({col = 7, change_cells_num = 1, list_view = self.node_list["posy_library_list"],
				assetBundle = bundle, assetName = asset, itemRender = TreasuryLibraryItemRender})
		self.library_grid_list:SetStartZeroIndex(false)
	end
end

function FiveElementsTreasuryLibraryView:OnFlush(param_t)
	local list_data = FiveElementsWGData.Instance:GetStoreCfg()
	self.library_grid_list:SetDataList(list_data)
end

--TreasuryLibraryItemRender-----
TreasuryLibraryItemRender = TreasuryLibraryItemRender or BaseClass(BaseRender)

function TreasuryLibraryItemRender:LoadCallBack()
	self.linghe_show_cell = ItemCell.New(self.node_list.item_pos)
end

function TreasuryLibraryItemRender:__delete()
	if self.linghe_show_cell then
		self.linghe_show_cell:DeleteMe()
        self.linghe_show_cell = nil
    end
end

function TreasuryLibraryItemRender:OnFlush()
	if not self.data then
		return
	end
	self.linghe_show_cell:SetData({item_id = self.data.item_id})
	local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    self.node_list.name.text.text = ToColorStr(item_name, color) 
end