NewComposeEquipmentOnceView = NewComposeEquipmentOnceView or BaseClass(SafeBaseView)

function NewComposeEquipmentOnceView:__init()
	self:SetMaskBg()
	self.view_layer = UiLayer.Normal
	self.view_cache_time = 0
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -10), sizeDelta = Vector2(600, 474)})
	self:AddViewResource(0, "uis/view/compose_ui_prefab", "layout_hecheng_equip_once")
end


function NewComposeEquipmentOnceView:SetShowDataAndOpen(data)
	self.compose_cfg = EquipmentWGData.Instance:GetEquipComposeCfgByID(data.item_id, data.compose_equip_best_attr_num)
	if not self.compose_cfg then
		return
	end

	if not self:IsOpen() then
		self:Open()
	else
		self:Flush()
	end
end

function NewComposeEquipmentOnceView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Compose.ComposeOnceTitleName

	if not self.target_equip_cell then
		self.target_equip_cell = ItemCell.New(self.node_list["target_equip_cell"])
	end

	if not self.consume_equip_cell then
		self.consume_equip_cell = ItemCell.New(self.node_list["consume_equip_cell"])
	end

	XUI.AddClickEventListener(self.node_list["btn_plus"], BindTool.Bind1(self.OnClickPlus, self))
	XUI.AddClickEventListener(self.node_list["btn_minus"], BindTool.Bind1(self.OnClickMinus, self))
	XUI.AddClickEventListener(self.node_list["amount_btn"], BindTool.Bind1(self.OnClickSetAmount, self))

	
	--默认选择最大
	self.select_consume_num = self.compose_cfg.max_consume_num
	-- 一次最大合成数量
	self.max_once_compose_num = 20

	local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(self.compose_cfg.compose_equip_id, self.compose_cfg.compose_equip_best_attr_num)
	-- 当前数量
	self.cur_amount = math.min(math.floor(can_use_num / self.select_consume_num), self.max_once_compose_num)
	self.node_list.cur_select_num_text.text.text = string.format(Language.Compose.ComposeOnceSelectName, self.select_consume_num)
	
	self.is_show_select_num_part = true

	self.select_num_list_view = AsyncListView.New(NewComposeEquipmentOnceNumRender, self.node_list["select_num_list"])
	self.select_num_list_view:SetSelectCallBack(BindTool.Bind(self.SelectNumCallBack, self))
	local list_data = {}
	for i = self.compose_cfg.min_consume_num, self.compose_cfg.max_consume_num do
		local data = {}
		data.select_num = i
		table.insert(list_data, data)
	end
	
	self.node_list.select_num_list_part.rect.sizeDelta = u3dpool.vec2(178, 42 * (#list_data))
	if not IsEmptyTable(list_data) then
		self.select_num_list_view:SetDataList(list_data)
	end

	self:OnClickSelectNum()
	XUI.AddClickEventListener(self.node_list["btn_select_num"], BindTool.Bind(self.OnClickSelectNum, self))
	XUI.AddClickEventListener(self.node_list["close_select_num_list_part"], BindTool.Bind(self.OnClickSelectNum, self))
	XUI.AddClickEventListener(self.node_list["once_compose_btn"], BindTool.Bind(self.OnEquipComposeOperaReq, self))
end

function NewComposeEquipmentOnceView:ReleaseCallBack()
	if self.target_equip_cell then
		self.target_equip_cell:DeleteMe()
		self.target_equip_cell = nil
	end

	if self.consume_equip_cell then
		self.consume_equip_cell:DeleteMe()
		self.consume_equip_cell = nil
	end

	if self.select_num_list_view then
		self.select_num_list_view:DeleteMe()
		self.select_num_list_view = nil
	end

	self.compose_cfg = nil
end

function NewComposeEquipmentOnceView:ShowIndexCallBack()

end

function NewComposeEquipmentOnceView:OnFlush(param_t)
	if not self.compose_cfg then
		return
	end

	self:FlushCurAmount()
	self:FlushTargetEquipInfo()
	self:FlushConsumeEquipInfo()
	self:FlushOtherInfo()
end

function NewComposeEquipmentOnceView:FlushTargetEquipInfo()
	local param_t = {}
	param_t.star_level = self.compose_cfg.compose_equip_best_attr_num

	self.target_equip_cell:SetData({item_id = self.compose_cfg.compose_equip_id, param = param_t})
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.compose_cfg.compose_equip_id)
	if not item_cfg then
		return
	end 

	self.node_list.target_equip_name.text.text = string.format("%s*%s", item_cfg.name, self.cur_amount)
end

function NewComposeEquipmentOnceView:FlushConsumeEquipInfo()
	local equip_list = EquipmentWGData.Instance:GetHechengEquipmentItemList(self.compose_cfg, true)
	if IsEmptyTable(equip_list) then
		return
	end

	self.consume_equip_cell:SetData(equip_list[1])
	local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(self.compose_cfg.compose_equip_id, self.compose_cfg.compose_equip_best_attr_num)
	self.node_list.consume_equip_count.text.text = string.format(Language.Compose.ComposeOnceNum, can_use_num, (self.cur_amount * self.select_consume_num))
end

function NewComposeEquipmentOnceView:FlushOtherInfo()
	local probability = EquipmentWGData.Instance:GetEquineSuccessProbability(self.compose_cfg.compose_equip_id, self.compose_cfg.compose_equip_best_attr_num, self.select_consume_num)
	local color = probability <= 0 and COLOR3B.PINK or COLOR3B.GREEN
	self.node_list["hecheng_probability"].text.text = Language.Compose.Success .. "" .. ToColorStr(probability .. "%" , color)

	local yunshi_addition_cfg = QifuYunShiWGData.Instance:GetHasYunShiAdditionIdCfg(QifuYunShiWGData.ADDITION_TYPE.COMPOSE_TYPE)
	local addition_value = 0
	if yunshi_addition_cfg then
		addition_value = yunshi_addition_cfg.addition_value
	end
	self.node_list["yunshi_add_text"].text.text = string.format(Language.Compose.YuShiAdd, addition_value)

	self.node_list.cur_select_num_text.text.text = string.format(Language.Compose.ComposeOnceSelectName, self.select_consume_num)
end

function NewComposeEquipmentOnceView:FlushCurAmount()
	self.node_list["amount"].text.text = self.cur_amount
end

-- 展开品质筛选列表
function NewComposeEquipmentOnceView:OnClickSelectNum()
	self.is_show_select_num_part = not self.is_show_select_num_part
	self.node_list["select_num_list_part"]:SetActive(self.is_show_select_num_part)
	self.node_list["select_num_arrow_down"]:SetActive(self.is_show_select_num_part)
	self.node_list["select_num_arrow_up"]:SetActive(not self.is_show_select_num_part)
end

function NewComposeEquipmentOnceView:OnClickPlus()
	if not self.compose_cfg then
		return
	end

	local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(self.compose_cfg.compose_equip_id, self.compose_cfg.compose_equip_best_attr_num)
	local can_conver_num = math.min(math.floor(can_use_num / self.select_consume_num), self.max_once_compose_num)
	if self.cur_amount >= can_conver_num then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.MaxValue)
		return
	end
	
	self:ChangeAmount(math.min(self.cur_amount + 1, can_conver_num))
end

function NewComposeEquipmentOnceView:OnClickMinus()
	if not self.compose_cfg then
		return
	end

	if self.cur_amount <= 1 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.MinValue1)
		return
	end


	local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(self.compose_cfg.compose_equip_id, self.compose_cfg.compose_equip_best_attr_num)
	self:ChangeAmount(math.max(self.cur_amount - 1, 1))
end

function NewComposeEquipmentOnceView:ChangeAmount(new_amount)
	self.cur_amount = new_amount
	self:FlushCurAmount()
	self:FlushTargetEquipInfo()
	self:FlushConsumeEquipInfo()
end

-- 点击设置数量
function NewComposeEquipmentOnceView:OnClickSetAmount()
	if not self.compose_cfg then
		return
	end
	
	local function callback(input_num)
		self:ChangeAmount(input_num)
	end

	local num_keypad = TipWGCtrl.Instance:GetPopNumView()
	num_keypad:Open()
	num_keypad:SetNum(self.cur_amount)

	local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(self.compose_cfg.compose_equip_id, self.compose_cfg.compose_equip_best_attr_num)
	local can_conver_num = math.min(math.floor(can_use_num / self.select_consume_num), self.max_once_compose_num)
	num_keypad:SetMaxValue(can_conver_num)
	num_keypad:SetMinValue(1)
	num_keypad:SetOkCallBack(callback)
end

-- 选择回调
function NewComposeEquipmentOnceView:SelectNumCallBack(cell, cell_index, is_default, is_click)
	local data = cell and cell:GetData()
	if data == nil then
		return
	end

	if is_default then 
		return 
	end

	self.select_consume_num = data.select_num
	local can_use_num = EquipmentWGData.Instance:GetCEEquipCanUseNumByData(self.compose_cfg.compose_equip_id, self.compose_cfg.compose_equip_best_attr_num)
	self.cur_amount = math.min(math.floor(can_use_num / self.select_consume_num), self.max_once_compose_num)

	self:OnClickSelectNum()
	self:FlushCurAmount()
	self:FlushTargetEquipInfo()
	self:FlushConsumeEquipInfo()
	self:FlushOtherInfo()
end

function NewComposeEquipmentOnceView:OnEquipComposeOperaReq()
	if not self.compose_cfg then
		return
	end

	local equip_list = EquipmentWGData.Instance:GetHechengEquipmentItemList(self.compose_cfg, true)
	if IsEmptyTable(equip_list) then
		return
	end

	local stuff_list = {}
	local need_stuff_count = self.select_consume_num * self.cur_amount
	local cur_select_stuff_count = 0
	for k, v in pairs(equip_list) do
		if cur_select_stuff_count >= need_stuff_count then
			break
		end

		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			local data = {is_bag = v.frombody and 0 or 1,
						index = v.index,
						sort = v.frombody and item_cfg.color * 10 + 1 or item_cfg.color * 10,}
			table.insert(stuff_list, data)
			cur_select_stuff_count = cur_select_stuff_count + 1
		end
	end

	if IsEmptyTable(stuff_list) then
		return
	end

	-- 后端要前端将材料按颜色从小到大排序好
	table.sort(stuff_list, SortTools.KeyLowerSorter("sort"))
	EquipmentWGCtrl.Instance:SendNewEquipBatchComposeReq(self.compose_cfg.compose_equip_id, self.compose_cfg.compose_equip_best_attr_num,
		self.cur_amount, self.select_consume_num, stuff_list)

	self:Close()
end
--------------------------------NewComposeEquipmentOnceNumRender----------------
NewComposeEquipmentOnceNumRender = NewComposeEquipmentOnceNumRender or BaseClass(BaseRender)
function NewComposeEquipmentOnceNumRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.name.text.text = string.format(Language.Compose.ComposeOnceSelectName, self.data.select_num)
end
