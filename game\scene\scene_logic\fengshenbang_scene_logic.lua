FengShenBangSceneLogic = FengShenBangSceneLogic or BaseClass(CommonActivityLogic)

function FengShenBangSceneLogic:__init()
	
end

function FengShenBangSceneLogic:__delete()
	if self.close_loading_view_event then
		GlobalEventSystem:UnBind(self.close_loading_view_event)
		self.close_loading_view_event = nil
	end
end

function FengShenBangSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonActivityLogic.Enter(self, old_scene_type, new_scene_type)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		if Scene.Instance:GetSceneType() == SceneType.FengShenBang then
			MainuiWGCtrl.Instance:SetTaskContents(false)
			MainuiWGCtrl.Instance:SetOtherContents(true)
			MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
			MainuiWGCtrl.Instance:SetTeamBtnState(false)
		end
	end)
	local scene_id = Scene.Instance:GetSceneId()
	FengShenBangWGData.Instance:SetFSBSceneId(scene_id)
	FengShenBangWGCtrl.Instance:OpenFbView()
	MainuiWGCtrl.Instance:SetFbTimePos(0,12)
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)  --挂机

	if not self.close_loading_view_event then
		self.close_loading_view_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.FlushFBCountDown, self))
	end
	-- self:FlushFBCountDown()
end

function FengShenBangSceneLogic:Out()
	CommonActivityLogic.Out(self)
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
            MainuiWGCtrl.Instance:SetTaskContents(true)
            MainuiWGCtrl.Instance:SetOtherContents(false)
			MainuiWGCtrl.Instance:SetFBNameState(false)
			MainuiWGCtrl.Instance:SetTeamBtnState(true)
		end)
	GuajiWGCtrl.Instance:StopGuaji()
	FengShenBangWGCtrl.Instance:CloseFbView()
	ViewManager.Instance:Open(GuideModuleName.FengShenBang)
	GlobalTimerQuest:CancelQuest(self.out_timer)
	FengShenBangWGCtrl.Instance:CheckFail()
end

function FengShenBangSceneLogic:FlushFBCountDown()
	Scene.Instance:SendFBLoadedScene()
	local scene_info = FengShenBangWGData.Instance:GetSceneInfo()
	if not scene_info then
		return
	end
	local other_cfg = FengShenBangWGData.Instance:GetActCfgList("other")
	local limit_time = other_cfg and other_cfg.scene_limit_time or 0
	local now_time = TimeWGCtrl.Instance:GetServerTime()
	MainuiWGCtrl.Instance:SetFbIconEndCountDown(now_time + limit_time, false, GameEnum.FU_BEN_OUT_SCENE, true)
	GlobalTimerQuest:CancelQuest(self.out_timer)
	self.out_timer = GlobalTimerQuest:AddDelayTimer(function ()
		MainuiWGCtrl.Instance:LevelFB()
	end, limit_time)
end

function FengShenBangSceneLogic:GetGuajiPos()
	local scene_id = Scene.Instance:GetSceneId()
	local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgDataBySceneID(scene_id)
	if scene_cfg then
		return scene_cfg.boss_pos_x, scene_cfg.boss_pos_y
	end
end