LevelRechargeView = LevelRechargeView or BaseClass(SafeBaseView)

function LevelRechargeView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Half
    self:SetMaskBg()
	self:AddViewResource(0, "uis/view/level_recharge_ui_prefab", "level_recharge_view")
end

function LevelRechargeView:LoadCallBack()
	local bundle, asset = ResPath.GetRawImagesPNG("a2_zjm_djzs_bg1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	if not self.recharge_toggle_list then
		self.recharge_toggle_list = AsyncListView.New(RechargeToggleCell, self.node_list.recharge_toggle_list)
		self.recharge_toggle_list:SetSelectCallBack(BindTool.Bind(self.OnClickToggleCell, self))

		local data_list = LevelRechargeWGData.Instance:GetLevelRechargeCfg()
		if data_list then
			self.recharge_toggle_list:SetDefaultSelectIndex(#data_list)
			self.recharge_toggle_list:SetDataList(data_list)
		end
	end

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
	end

	if not self.reward_item1 then
		self.reward_item1 = ItemCell.New(self.node_list.reward_item1)
	end

	if not self.reward_item2 then
		self.reward_item2 = ItemCell.New(self.node_list.reward_item2)
	end

	XUI.AddClickEventListener(self.node_list["btn_buy"], BindTool.Bind(self.OnClickBuy, self))
end

function LevelRechargeView:ReleaseCallBack()
	if self.recharge_toggle_list then
		self.recharge_toggle_list:DeleteMe()
		self.recharge_toggle_list = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.reward_item1 then
		self.reward_item1:DeleteMe()
		self.reward_item1 = nil
	end

	if self.reward_item2 then
		self.reward_item2:DeleteMe()
		self.reward_item2 = nil
	end

end

function LevelRechargeView:OnClickToggleCell(cell)
	local data = cell:GetData()
	if not data then
		return
	end

	local bundle, asset = ResPath.GetRawImagesPNG("a3_zjm_level_" .. data.seq)
    self.node_list.rank.raw_image:LoadSprite(bundle, asset, function()
        self.node_list.rank.raw_image:SetNativeSize()
    end)

	--------------策划要求展示和奖励物品分两个字段--------------
	local table_reward = {}
	for key, value in pairs(data.show_rmb_buy_reward_item) do
		local item_data = value
		local item_config = ItemWGData.Instance:GetItemConfig(value.item_id)
		if item_config then
			item_data.color = item_config.color
		else
			item_data.color = 1
		end
		table.insert(table_reward, item_data)
	end
	table.sort(table_reward, SortTools.KeyUpperSorters("color"))
	self.reward_list:SetDataList(table_reward)
	self.reward_item1:SetData(data.show_reward_item[0])
	self.reward_item2:SetData(data.show_reward_item[1])
	self.cur_seq = data.seq

	local price = RoleWGData.GetPayMoneyStr(data.rmb_buy_price, data.rmb_buy_type, data.seq)
	self.node_list.upgrade_buy.text.text = string.format(Language.LevelRecharge.BuyBtnText, price)

	local str_list = Split(data.information, "&")
	for index, value in ipairs(str_list) do
		local desc_list = Split(value, "|")
		if desc_list[1] then
			self.node_list["title_" .. index].text.text = desc_list[1]
		end

		if desc_list[2] then
			self.node_list["desc_" .. index].text.text = desc_list[2]
		end
	end
end

-- 注意：VersionsAdvanceNoticeWGCtrl:CheckIsActivityNoticeClose 有特殊处理
function LevelRechargeView:OnClickBuy()
	local cur_grade = LevelRechargeWGData.Instance:GetCurPriceCfg(self.cur_seq)
	if IsEmptyTable(cur_grade) then
		return
	end

	local level = RoleWGData.Instance.role_vo.level
	if level < cur_grade.rmb_buy_level then
		RechargeWGCtrl.Instance:Recharge(cur_grade.rmb_buy_price, cur_grade.rmb_buy_type, cur_grade.seq)
		self:Close()
	end
end






-----------------------RechargeToggleCell-----------------------
RechargeToggleCell = RechargeToggleCell or BaseClass(BaseRender)
function RechargeToggleCell:LoadCallBack()
	-- UITween.MoveLoop(self.node_list["last_tip"], Vector2(-123, 20), Vector2(-123, 0), 0.9)
end

function RechargeToggleCell:OnFlush()
	if not self.data then
		return
	end

	local data = self.data

	local price = RoleWGData.GetPayMoneyStr(data.rmb_buy_price, data.rmb_buy_type, data.seq)
	self.node_list.normal_lbl.text.text = ToColorStr(string.format(Language.LevelRecharge.DerectPurchase, price), "#FFFFED")
	self.node_list.hight_lbl.text.text =  ToColorStr(string.format(Language.LevelRecharge.DerectPurchase, price), "#BF4C25")

	-- 第三档位无了 故屏蔽
	-- local data_list = LevelRechargeWGData.Instance:GetLevelRechargeCfg()
	-- if not IsEmptyTable(data_list) and data.seq == data_list[#data_list].seq then
	-- 	self.node_list.last_tip:SetActive(true)
	-- 	self.node_list.tip_desc.text.text = Language.LevelRecharge.LastGradeDesc
	-- end

end

function RechargeToggleCell:OnSelectChange(is_select)
	--策划zp 说这次界面不显示页签，以后配置多个再显示.
	-- self.node_list.hight_bg:SetActive(is_select)
end
