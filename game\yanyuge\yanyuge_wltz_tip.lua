YanYuGeWLTZTip = YanYuGeWLTZTip or BaseClass(SafeBaseView)

function YanYuGeWLTZTip:__init()
    self:SetMaskBg()
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel_2", {vector2 = Vector2(0, 0), sizeDelta = Vector2(564, 404)})
    self:AddViewResource(0, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_wltz_tip")
end

function YanYuGeWLTZTip:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.buy_sub_btn, BindTool.Bind(self.OnClickBuySubBtn, self))
    XUI.AddClickEventListener(self.node_list.buy_add_btn, BindTool.Bind(self.OnClickBuyAddBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_send, BindTool.Bind(self.OnClickSendBtn, self))
    XUI.AddClickEventListener(self.node_list.buy_count_bg, BindTool.Bind(self.OnClickBuyCountBtn, self))
    XUI.AddClickEventListener(self.node_list.buy_one_key_max_btn, BindTool.Bind(self.OnClickOneKeyMaxBtn, self))

    self.node_list.title_view_name.text.text = Language.YanYuGe.WLTZTipTitleName
end

function YanYuGeWLTZTip:SetDataAndOpen(max_value)
    self.max_value = max_value

    if nil == max_value or max_value < 0 then
        return
    end 

    if self:IsOpen() then
        self:Flush()
    else
        self:Open()
    end
end

function YanYuGeWLTZTip:ReleaseCallBack()
    self.max_value = nil
    self.buy_count = nil
end

function YanYuGeWLTZTip:ShowIndexCallBack()
    self.buy_count = 1
end

function YanYuGeWLTZTip:OnFlush()
    self:FlushBuyPanel()
end

function YanYuGeWLTZTip:FlushBuyPanel()
    local invest_interest = YanYuGeWGData.Instance:GetInvestInterest()
    local extra_value = math.floor(invest_interest / 10000 * self.buy_count)

    local already_invest_num = YanYuGeWGData.Instance:GetAlreadyInvestNum()
    local total_value = math.floor((already_invest_num + self.buy_count) * (1 + invest_interest / 10000))

    self.node_list.desc_content.tmp.text = string.format(Language.YanYuGe.WLTZTipContent, self.buy_count, total_value, extra_value)
    self.node_list["desc_value"].tmp.text = self.buy_count
end

function YanYuGeWLTZTip:OnClickBuySubBtn()
    if self.buy_count > 1 then
        self.buy_count = self.buy_count - 1
        self:FlushBuyPanel()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.WLTZTipMinBuyCount)
    end
end

function YanYuGeWLTZTip:OnClickBuyAddBtn()
    if self.buy_count < self.max_value then
        self.buy_count = self.buy_count + 1

        self:FlushBuyPanel()
    else
        SysMsgWGCtrl.Instance:ErrorRemind(Language.YanYuGe.WLTZTipMaxBuyCount)
    end
end

function YanYuGeWLTZTip:OnClickBuyCountBtn()
    local pop_num_view = TipWGCtrl.Instance:GetPopNumView()
	pop_num_view:Open()
	pop_num_view:SetText(1)
	pop_num_view:SetMinValue(1)
	pop_num_view:SetMaxValue(self.max_value)
	pop_num_view:SetOkCallBack(function (num)
		self.buy_count = num
		self:FlushBuyPanel()
	end)
end

function YanYuGeWLTZTip:OnClickOneKeyMaxBtn()
    self.buy_count = self.max_value
    self:FlushBuyPanel()
end

function YanYuGeWLTZTip:OnClickSendBtn()
    YanYuGeWGCtrl.Instance:SendOperateRequest(YANYUGE_OPERA_TYPE.INVEST, self.buy_count)
    self:Close()
end