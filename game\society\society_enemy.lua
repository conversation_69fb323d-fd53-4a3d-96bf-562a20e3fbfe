
require("game/society/society_items")

function SocietyView:InitEnemyView()
	--self.m_enemy_view = self.node_tree.layout_enemy_base  --仇人面板

	self.list_enemy_list = self.node_list["list_enemylist"]
	self.node_list["layout_blank_tip3"].gameObject:SetActive(false)
	self:CreateEnemyList()
	
	XUI.AddClickEventListener(self.node_list["label_menu_killcount"], BindTool.Bind1(self.OnClickLookKiller, self))
end

function SocietyView:DeleteEnemyView()
	if nil ~= self.enemy_list then
		self.enemy_list:DeleteMe()
		self.enemy_list = nil
	end

	self.list_enemy_list = nil
end

--创建仇人列表
function SocietyView:CreateEnemyList()
	self.enemy_list = AsyncListView.New(SocietyEnemyRender, self.list_enemy_list)
	-- self.enemy_list:SetSelectCallBack(BindTool.Bind1(self.EnemyListEventCallback, self))

	self:Flush(SocietyView.Tab_E, "enemy_list")
	-- self:FlushEnemyView()
end

function SocietyView:EnemyListEventCallback(cell, index)
	-- print_error("wowowowowowoowwowo____"..index)
end

function SocietyView:FlushEnemyView()
	local data = SocietyWGData.Instance:GetEnemyList()
	if #data == 0 then
		self.node_list["layout_blank_tip3"].gameObject:SetActive(true)
	else
		self.node_list["layout_blank_tip3"].gameObject:SetActive(false)
	end
	--pfloprint_error(data)
	if nil ~= data and nil ~= self.enemy_list then
		self.enemy_list:SetDataList(data, 0)
	end
end

--打开 layout_bekilled 面板
function SocietyView:OnClickLookKiller()
	-- print_error("OnClickLookKiller--------------  ")
	SocietyBeKilledView.Instance:OpenView()
end
