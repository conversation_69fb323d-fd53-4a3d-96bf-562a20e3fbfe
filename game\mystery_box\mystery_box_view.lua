MysteryBoxView = MysteryBoxView or BaseClass(SafeBaseView)

function MysteryBoxView:__init()
	self:SetMaskBg(false, true)
    self.select_index = nil
    self.fold_toggle_index = nil
    self.fold_group_state = false--折叠窗口展开状态
    self:AddViewResource(0, "uis/view/mystery_box_ui_prefab", "layout_mystery_box")
end

function MysteryBoxView:ReleaseCallBack()
    if self.card_package_list then
        for k, v in pairs(self.card_package_list) do
            v:DeleteMe()
        end
        self.card_package_list = nil
    end

    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.task_list then
        self.task_list:DeleteMe()
        self.task_list = nil
    end

    if self.log_list then
        self.log_list:DeleteMe()
        self.log_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    self.fold_toggle_index = nil
    self.select_index = nil
    self:CleanTimeDown()
end

function MysteryBoxView:OpenCallBack()
    local opera_type = MYSTERY_BOX_OPERATE_TYPE.INFO
    MysteryBoxWGCtrl.Instance:SendReq(opera_type)

    opera_type = MYSTERY_BOX_OPERATE_TYPE.LOG
    MysteryBoxWGCtrl.Instance:SendReq(opera_type)
end

function MysteryBoxView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.fold_btn, BindTool.Bind(self.OnFoldBtnClick, self))
    XUI.AddClickEventListener(self.node_list.point_shop_btn, BindTool.Bind(self.OnPointShopClick, self))
    XUI.AddClickEventListener(self.node_list.gailv_btn, BindTool.Bind(self.OnGailvBtnClick, self))
    XUI.AddClickEventListener(self.node_list.jihuo_btn, BindTool.Bind(self.OnClickOpenQWYLView, self))
    XUI.AddClickEventListener(self.node_list.rule_btn, BindTool.Bind(self.OnClickRuleBtn, self))
    for i = 0, MysteryBoxWGData.CARD_PACKAGE_NUM - 1 do
        XUI.AddClickEventListener(self.node_list["card_package_"..i], BindTool.Bind(self.OnClickDraw, self, i))
    end
    
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
    end

    for i = 1, MysteryBoxWGData.BOX_TOGGLE_NUM do
        self.node_list["times_toggle_"..i].button:AddClickListener(BindTool.Bind(self.OnClickToggle, self, i))
    end

    for k, v in pairs(MysteryBoxWGData.TASK_TYPE) do
        self.node_list["fold_toggle_"..v].button:AddClickListener(BindTool.Bind(self.OnClickFoldToggle, self, v))
    end

    if not self.task_list then
        self.task_list = AsyncListView.New(MysteryFoldListRender, self.node_list["task_list"])
    end

    if not self.log_list then
        self.log_list = AsyncListView.New(MysteryLogRender, self.node_list["log_list"])
    end

    if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list.display_root)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

    if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
            show_gold = true
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function MysteryBoxView:OnFlush(param_t)
    for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
            self:FlushShopRemind()
        elseif "FlushFoldView" == k then
            self:FlushFoldView()
        elseif "FlushDrawInfo" == k then
            self:FlushCardPackage()
            self:FlushFoldView()
            self:FlushShopRemind()
        elseif "FlushLog" == k then
            self:FlushLogList()
        elseif "FlushShopRemind" == k then
            self:FlushShopRemind()
        elseif "FlushConsumeItem" == k then
            self:FlushConsumeItem()
		end
	end
end

function MysteryBoxView:FlushShopRemind()
    local remind = MysteryBoxWGData.Instance:GetPointShopRemind() == 1
    self.node_list.point_shop_remind:CustomSetActive(remind)
end

function MysteryBoxView:FlushFoldRemind()
    local remind = MysteryBoxWGData.Instance:GetAllTaskRemind() == 1
    self.node_list.fold_btn_remind:CustomSetActive(remind)

    for k, v in pairs(MysteryBoxWGData.TASK_TYPE) do
        remind = MysteryBoxWGData.Instance:GetTaskRemindByType(v) == 1
        --print_error(MysteryBoxWGData.Instance:GetTaskRemindByType(v), v)
        self.node_list["fold_toggle_remind_"..v]:CustomSetActive(remind)
    end
end

function MysteryBoxView:FlushCardPackage()
    local draw_data = MysteryBoxWGData.Instance:GetDrawData()
	if IsEmptyTable(draw_data) then
		return
	end
    for i = 0, MysteryBoxWGData.CARD_PACKAGE_NUM - 1 do
        local need_tips = draw_data[i].need_tips
        local sprite_name = need_tips and "a2_mhkb_k2" or "a2_mhkb_k1"
        local bundle, asset = ResPath.GetMysteryBoxImg(sprite_name)
        self.node_list["package_image_"..i].image:LoadSprite(bundle, asset, function()
            self.node_list["package_image_"..i].image:SetNativeSize()
        end)

        self.node_list["package_effect_"..i]:CustomSetActive(need_tips)
    end 
end

function MysteryBoxView:FlushConsumeItem()
    local item_id = MysteryBoxWGData.Instance:GetDrawConsumeItemId(0)
    local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
    self.node_list["consume_num"].text.text = item_num
end

function MysteryBoxView:FlushView()
    local draw_data = MysteryBoxWGData.Instance:GetDrawData()
    local grade_cfg = MysteryBoxWGData.Instance:GetGradeCfg()
	if IsEmptyTable(draw_data) or IsEmptyTable(grade_cfg) then
		return
	end

    --刷新toggle勾选状态
    if not self.select_index then
        self:SetDefualToggle(1)
    end

    if not self.fold_toggle_index then
        self:SetDefualFoldToggle(1)
    end

     -- 刷新模型
	self:FlushModel(grade_cfg)

    -- 刷新倒计时
	self:FlushTimeCountDown()

    --刷新盲盒卡包状态
    self:FlushCardPackage()

    --刷新下方奖励列表
    local id_list = Split(grade_cfg.show_item, "|")
    local show_item_list = {}
    for k, v in pairs(id_list) do
        table.insert(show_item_list, {item_id = tonumber(v)})
    end

    self.reward_list:SetDataList(show_item_list)

    --刷新抽奖记录
    self:FlushLogList()

    --刷新抽奖道具数量
    self:FlushConsumeItem()
    
    --刷新抽奖道具图标
    local item_id = MysteryBoxWGData.Instance:GetDrawConsumeItemId(0)
    self.node_list.consume_icon.image:LoadSprite(ResPath.GetItem(item_id))

    --刷新副标题
    self.node_list.title_text.text.text = grade_cfg.grade_name
end

function MysteryBoxView:SetDefualToggle(type)
    self:OnClickToggle(type)
end

function MysteryBoxView:OnClickToggle(type)
    if self.select_index == type then
        return
    end

    if self.select_index then
        self.node_list["times_toggle_img_"..self.select_index]:CustomSetActive(false)
    end
    self.node_list["times_toggle_img_"..type]:CustomSetActive(true)
    self.select_index = type
    
end

--打开积分商店
function MysteryBoxView:OnPointShopClick()
    MysteryBoxWGCtrl.Instance:OpenPointShop()
end

--打开概率展示
function MysteryBoxView:OnGailvBtnClick()
    MysteryBoxWGCtrl.Instance:OpenGailvView()
end

function MysteryBoxView:OnClickDraw(seq)
    local draw_num = MysteryBoxWGData.DRAW_NUM_TABLE[self.select_index]
    MysteryBoxWGCtrl.Instance:ClickDraw(seq, draw_num)
end

--打开奇闻异录界面
function MysteryBoxView:OnClickOpenQWYLView()
	ViewManager.Instance:Open(GuideModuleName.StrangeCatalogView)
end

function MysteryBoxView:OnClickRuleBtn()
    RuleTip.Instance:SetContent(Language.MysteryBox.TipsStr, Language.MysteryBox.TipsTitle)
end

-- 刷新模型
function MysteryBoxView:FlushModel(chapter_data)
    if not chapter_data then
		return
	end

	local display_data = {}
	if chapter_data["model_show_itemid"] ~= 0 and chapter_data["model_show_itemid"] ~= "" then
		local split_list = string.split(chapter_data["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = chapter_data["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = chapter_data["model_bundle_name"]
	display_data.asset_name = chapter_data["model_asset_name"]
	local model_show_type = tonumber(chapter_data["model_show_type"]) or 1
	display_data.render_type = model_show_type - 1

	self.model_display:SetData(display_data)
	local scale = chapter_data["display_scale"]
	Transform.SetLocalScaleXYZ(self.node_list["display_root"].transform, scale, scale, scale)

	local pos_x, pos_y = 0, 0
	if chapter_data.display_pos and chapter_data.display_pos ~= "" then
		local pos_list = string.split(chapter_data.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.display_root.rect, pos_x, pos_y)

	if chapter_data.rotation and chapter_data.rotation ~= "" then
		local rotation_tab = string.split(chapter_data.rotation,"|")
		self.node_list.display_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

--刷新抽奖记录
function MysteryBoxView:FlushLogList()
    local log_list = MysteryBoxWGData.Instance:GetLogList()
    -- print_error(log_list)
    if not IsEmptyTable(log_list) then
        self.log_list:SetDataList(log_list)
    end
end

-----------------------------------------活动倒计时 start----------------------------------------------

function MysteryBoxView:FlushTimeCountDown()
	local time, invalid_time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_BLIND_BOX_CARD)
	if time > 0 then
		self:CleanTimeDown()
		if self.node_list["time_down"] then
			self.node_list["time_down"].text.text = string.format(Language.MysteryBox.ActiveRemainTime, TimeUtil.FormatTimeDHMS(time))
			CountDownManager.Instance:AddCountDown("mystery_box_down", BindTool.Bind1(self.UpdateCountDown, self), 
                                                            BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
		end
	end
end

function MysteryBoxView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 and self.node_list["time_down"] then
        self.node_list["time_down"].text.text = string.format(Language.MysteryBox.ActiveRemainTime, TimeUtil.FormatTimeDHMS(valid_time))
	end
end

function MysteryBoxView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("mystery_box_down") then
		CountDownManager.Instance:RemoveCountDown("mystery_box_down")
	end
end

function MysteryBoxView:OnComplete()
	if self.node_list["time_down"] then
    	self.node_list["time_down"].text.text = ""
	end
	self:Close()
end

-----------------------------------------活动倒计时 end----------------------------------------------

-----------------------------------------展开栏 start-----------------------------------------------

function MysteryBoxView:OnFoldBtnClick()
    self:SetFoldViewState()
    self.node_list["fold_img_hl"]:CustomSetActive(self.fold_group_state)
end

function MysteryBoxView:SetFoldViewState()
    UITween.CleanAllMoveToShowPanel(GuideModuleName.MysteryBoxView)
    if self.fold_group_state then
        UITween.MoveToShowPanel(GuideModuleName.MysteryBoxView, self.node_list["fold_group"], 
                                    Vector2(0, 0), Vector2(850, 0), 0.5, DG.Tweening.Ease.Linear)
    else
        UITween.MoveToShowPanel(GuideModuleName.MysteryBoxView, self.node_list["fold_group"],
                                    Vector2(850, 0), Vector2(0, 0), 0.5, DG.Tweening.Ease.Linear)
    end
    self.fold_group_state = not self.fold_group_state
end

function MysteryBoxView:FlushFoldView(type)
    type = type or self.fold_toggle_index
    local dataList = MysteryBoxWGData.Instance:GetFoldListData(type)
    if IsEmptyTable(dataList) then
        return
    end 
    --print_error("FlushFoldView", dataList)
    self.task_list:SetDataList(dataList)
    self:FlushFoldRemind()
end

function MysteryBoxView:SetDefualFoldToggle(type)
    for k, v in pairs(MysteryBoxWGData.TASK_TYPE) do
        self.node_list["fold_image_hl_"..v]:CustomSetActive(v == type)
    end
    self.fold_toggle_index = type
    self:FlushFoldView(type)
end

function MysteryBoxView:OnClickFoldToggle(type)
    if self.fold_toggle_index == type then
        return
    end

    if self.fold_toggle_index then
        self.node_list["fold_image_hl_"..self.fold_toggle_index]:CustomSetActive(false)
    end

    self.node_list["fold_image_hl_"..type]:CustomSetActive(true)

    self.fold_toggle_index = type
    self:FlushFoldView(type)
end

-----------------------------------------展开栏 end-----------------------------------------------




-----------------------------------------展开栏列表格子 MysteryFoldListRender start-----------------------------------------------

MysteryFoldListRender = MysteryFoldListRender or BaseClass(BaseRender)

function MysteryFoldListRender:LoadCallBack()
    if not self.item_list then
        self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    end

    XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind(self.OnClickBtn, self))
end

function MysteryFoldListRender:__delete()
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function MysteryFoldListRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end
    -- print_error(self.data)
    local task_title = Language.MysteryBox.TaskTitle[self.data.task_type]
    self.node_list.title.text.text = string.format(task_title, self.data.cur_num, self.data.need_num)
    self.item_list:SetDataList(self.data.reward_item)
    self.node_list.btn:CustomSetActive(self.data.is_get_flag == 0)
    self.node_list.yilingqu:CustomSetActive(self.data.is_get_flag == 1)

    XUI.SetButtonEnabled(self.node_list.btn, self.data.cur_num >= self.data.need_num)
    local remind = self.data.is_get_flag == 0 and self.data.cur_num >= self.data.need_num
    self.node_list.btn_remind:CustomSetActive(remind)
end

function MysteryFoldListRender:OnClickBtn()
    if IsEmptyTable(self.data) then
        return
    end
    local opera_type = MYSTERY_BOX_OPERATE_TYPE.HANGBOOK_TASK
    if self.data.task_type == MysteryBoxWGData.TASK_TYPE.DRAW_NUM then
        opera_type = MYSTERY_BOX_OPERATE_TYPE.DRAW_TASK
    end
    
    if self.data.cur_num >= self.data.need_num and self.data.is_get_flag == 0 then
        MysteryBoxWGCtrl.Instance:SendReq(opera_type, self.data.seq)
    end
end
-----------------------------------------展开栏列表格子 MysteryFoldListRender end-----------------------------------------------

-----------------------------------------log格子 MysteryLogRender end-----------------------------------------------

MysteryLogRender = MysteryLogRender or BaseClass(BaseRender)

function MysteryLogRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    self.node_list.text.text.text = string.format(Language.MysteryBox.LogStr, self.data.role_name, item_cfg and item_cfg.name)
end
-----------------------------------------log格子 MysteryLogRender start-----------------------------------------------