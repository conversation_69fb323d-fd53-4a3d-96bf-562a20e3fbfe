require("game/role/secret_record/secret_record_view")
require("game/role/secret_record/secret_record_tips_view")
require("game/role/secret_record/secret_record_wg_data")

SecretRecordWGCtrl = SecretRecordWGCtrl or BaseClass(BaseWGCtrl)
function SecretRecordWGCtrl:__init()
    if SecretRecordWGCtrl.Instance then
		ErrorLog("[SecretRecordWGCtrl] Attemp to create a singleton twice !")
	end

    SecretRecordWGCtrl.Instance = self
    self.view = SecretRecordView.New(GuideModuleName.SecretRecordView)
    self.tips_view = SecretRecordTipsView.New()
	self.data = SecretRecordWGData.New()

	self:RegisterAllProtocols()
end

function SecretRecordWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.tips_view:DeleteMe()
	self.tips_view = nil

	SecretRecordWGCtrl.Instance = nil
end

-- 注册协议
function SecretRecordWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(CSZiYunMiLuClientReq)
    self:RegisterProtocol(SCDailyWorkLevelInfo, "OnLevelInfo")
	self:RegisterProtocol(SCZiYunMiLuInfo, "OnZiYunMiLuInfo")
end

function SecretRecordWGCtrl:OpenTipsView()
	self.tips_view:Open()
end

function SecretRecordWGCtrl:SendSecretRecordReq(operate_type, param1)
	local send_protocol = ProtocolPool.Instance:GetProtocol(CSZiYunMiLuClientReq)
	send_protocol.operate_type = operate_type or 0
	send_protocol.param1 = param1 or 0
	send_protocol:EncodeAndSend()
end

function SecretRecordWGCtrl:OnBottleInfo(protocol)
	self.data:SetBottleInfo(protocol)

	if self.view:IsOpen() then
		self.view:Flush()
	end

    RemindManager.Instance:Fire(RemindName.SecretRecord)
end

function SecretRecordWGCtrl:OnLevelInfo(protocol)
	self.data:SetLevelInfo(protocol)

    if self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.SecretRecord)
	self:SendSecretRecordReq(SECRET_RECORD_OPERATE_TYPE.CHANGE_DAY, self.view.time_state)
end

function SecretRecordWGCtrl:OnZiYunMiLuInfo(protocol)
	self.data:SetSecretRecordInfo(protocol)

    if self.view:IsOpen() then
		self.view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.SecretRecord)
end