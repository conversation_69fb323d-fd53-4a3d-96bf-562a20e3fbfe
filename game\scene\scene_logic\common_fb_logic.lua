CommonFbLogic = CommonFbLogic or BaseClass(BaseFbLogic)
local FB_TRIGGER_TYPE = {
	ENTER_FB = 1
}

function CommonFbLogic:__init()

end

function CommonFbLogic:__delete()
	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end
end

function CommonFbLogic:Enter(old_scene_type, new_scene_type)
	BaseFbLogic.Enter(self, old_scene_type, new_scene_type)
	--当前场景进入下一个场景不是同一个场景的情况下关闭界面的面板
	if old_scene_type ~= new_scene_type then
		ViewManager.Instance:CloseAll()
	end
	                       		                                          --副本屏蔽
	self:OpenFbSceneCd()
	MainuiWGCtrl.Instance:ShowMainuiMenu(false)
	--  MainuiWGCtrl.Instance:PlayTaskButtonTween(false)
	-- if new_scene_type == SceneType.COPPER_FB or new_scene_type == SceneType.PET_FB then
	-- 	 MainuiWGCtrl.Instance:SetTaskActive(false)
	-- end
	if NewTeamWGData.Instance:GetIsInRoomScene(new_scene_type) then
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			--MainuiWGCtrl.Instance:SetRoomActive(true)
			GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)
		end)

		--策划说组队目标玩法强制屏蔽队友血条
		self.role_enter_event = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, BindTool.Bind(self.OnRoleEnterCallBack, self))
		local role_list = Scene.Instance:GetRoleList()
		for i, v in pairs(role_list) do
			if not v:IsMainRole() then
				local follow_ui = v:GetFollowUi()
				local hp_bar = follow_ui:GetHpBar()
				if hp_bar then
					hp_bar:AddShieldRule(ShieldRuleWeight.High, function()
						return true
					end)
				end
			end
		end
	end
	if BossWGData.IsBossScene(new_scene_type) then
		NewTeamWGCtrl.Instance:ChangeMustCheck(1) --设置成需要验证入队
	end
	for k,v in pairs(BossWGData.SceneType) do
		if v == new_scene_type and v ~= SceneType.KFSHENYUN_FB and v ~= SceneType.Shenyuan_boss then  --深渊boss不走通用的了，走深渊伤害面板的
			BossWGCtrl.Instance:InitGoToPos()
			break
		end
	end
	FuBenWGData.Instance:SetFuBenInitiativeToLeave(new_scene_type, nil)
	self:CheckLoseView()

	self.now_fb_time = 0
	self:CheckSceneTriggerSustainGuide()
	self:CheckSceneTriggerRamGuide()
end

function CommonFbLogic:CheckLoseView()
	FuBenWGCtrl.Instance:CloseLoseView()
end

function CommonFbLogic:OnLoadingComplete()
end

--退出
function CommonFbLogic:Out(old_scene_type, new_scene_type)
	BaseFbLogic.Out(self)

	-- MainuiWGCtrl.Instance:SetTaskActive(true)
	MainuiWGCtrl.Instance:ShowMainuiMenu(true)
	MainuiWGCtrl.Instance:CloseLevelFBAlert()
	FuBenWGCtrl.Instance:ClearFbSceneLogicInfo()
	MainuiWGCtrl:SetFbPrepareTimeTextMark(nil)
	--UiInstanceMgr.Instance:CloseCustomMenu()
	-- self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.OnLoadingComplete, self))
	--print_error(old_scene_type, new_scene_type)

	-- if new_scene_type == SceneType.DefenseFb or new_scene_type == SceneType.COPPER_FB or new_scene_type == SceneType.PET_FB
	-- 	or new_scene_type == SceneType.FakePetFb then
	-- 	 MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	-- end

	if NewTeamWGData.Instance:GetIsInRoomScene(old_scene_type) then
		MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
			MainuiWGCtrl.Instance:SetRoomActive(false)
			GlobalEventSystem:Fire(OtherEventType.TEAM_INFO_CHANGE)
		end)
	end
	if self.role_enter_event then
		GlobalEventSystem:UnBind(self.role_enter_event)
		self.role_enter_event = nil
	end
	if BossWGData.IsBossScene(old_scene_type) then
		NewTeamWGCtrl.Instance:ChangeMustCheck(0) --设置成不需要验证入队
	end
	if FuBenPanelCountDown.Instance then
		FuBenPanelCountDown.Instance:CloseViewHandler()
	end

	self.now_fb_time = nil
	self.scene_sustain_guide_cfg = nil
	self.skill_guide_trigger = nil
	FuBenPanelWGCtrl.Instance:CloseFuBenGuidePopDialog()
end

function CommonFbLogic:OpenFbSceneCd()
	local out_time = FuBenWGData.Instance:GetOutFbTime()
	if out_time then
		if out_time > TimeWGCtrl.Instance:GetServerTime() then
			-- MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_time)															 --副本屏蔽
		end
	end
end

function CommonFbLogic:OnClickHeadHandler(is_show)
	BaseFbLogic.OnClickHeadHandler(is_show)
end

function CommonFbLogic:OnRoleEnterCallBack(obj_id)
	local role = Scene.Instance:GetObj(obj_id)
	if role:IsMainRole() then return end
	local follow_ui = role:GetFollowUi()
	local hp_bar = follow_ui:GetHpBar()
	if hp_bar then
		hp_bar:AddShieldRule(ShieldRuleWeight.High, function()
			return true
		end)
	end
end

function CommonFbLogic:Update(now_time, elapse_time)
	BaseFbLogic.Update(self, now_time, elapse_time)

	if self.scene_sustain_guide_cfg ~= nil then
		self.now_fb_time = self.now_fb_time + elapse_time

		if self.now_fb_time >= self.scene_sustain_guide_cfg.guide_interval_time then
			self.now_fb_time = 0
			self:TriggerSustainGuide()
		end
	end
end

-- 新加副本进入引导
function CommonFbLogic:CheckSceneTriggerSustainGuide()
	local scene_type = self:GetSceneType()
	local cfg = ConfigManager.Instance:GetAutoConfig("fb_guide_auto")

	if cfg then
		self.scene_sustain_guide_cfg = cfg.scen_sustain_guide and cfg.scen_sustain_guide[scene_type]
	end
end

-- 获取副本引导配置
function CommonFbLogic:GetFBGuideByGuideId(guide_id)
	local cfg = ConfigManager.Instance:GetAutoConfig("fb_guide_auto")

	if cfg then
		return cfg.fb_guide_list and cfg.fb_guide_list[guide_id]
	end

	return nil
end

-- 获取当前副本随机配置
function CommonFbLogic:CheckSceneTriggerRamGuide()
	local scene_type = self:GetSceneType()
	local cfg = ConfigManager.Instance:GetAutoConfig("fb_guide_auto")

	if cfg then
		local scene_guide_cfg = cfg.scene_ram_guide and cfg.scene_ram_guide[scene_type]
		if scene_guide_cfg and scene_guide_cfg.trigger_type == FB_TRIGGER_TYPE.ENTER_FB then
			-- 直接触发引导
			local key = string.format("OpenViewOnTimes_%s_%s", scene_type, RoleWGData.Instance:GetOriginUid())
			local old_times = PlayerPrefsUtil.GetInt(key, 0)
			local new_times = old_times + 1
			PlayerPrefsUtil.SetInt(key, new_times)

			--触发进入副本引导
			if new_times <= scene_guide_cfg.trigger_param then
				local guide_cfg = self:GetFBGuideByGuideId(scene_guide_cfg.trigger_guide)
				if guide_cfg then
					FuBenPanelWGCtrl.Instance:OpenFuBenGuidePopDialog(guide_cfg)
				end
			end
		end
	end
end

-- 触发持续引导
function CommonFbLogic:TriggerSustainGuide()
	if not self.scene_sustain_guide_cfg then
		return
	end

	if self.scene_sustain_guide_cfg.trigger_guide_list ~= nil and self.scene_sustain_guide_cfg.trigger_guide_list ~= "" then
		local trigger_guide_list = Split(self.scene_sustain_guide_cfg.trigger_guide_list, "|")
		local rand_index = math.random(1, #trigger_guide_list)
		local guide_id = tonumber(trigger_guide_list[rand_index]) or 0

		local guide_cfg = self:GetFBGuideByGuideId(guide_id)
		if guide_cfg then
			FuBenPanelWGCtrl.Instance:OpenFuBenGuidePopDialog(guide_cfg)
		end
	end
end

-- 触发boss释放技能引导
function CommonFbLogic:TriggerBossSkillGuide(skill_id, var_scene_type, monster_id)
	if self.skill_guide_trigger == nil then
		self.skill_guide_trigger = {}
	end

	if self.skill_guide_trigger[skill_id] ~= nil then
		return
	end

	self.skill_guide_trigger[skill_id] = 1
	local scene_type = self:GetSceneType()
	local cfg = ConfigManager.Instance:GetAutoConfig("fb_guide_auto")

	if cfg and scene_type == var_scene_type then
		local boss_skill_guide_cfg = cfg.boss_skill_guide and cfg.boss_skill_guide[monster_id]

		if boss_skill_guide_cfg ~= nil and boss_skill_guide_cfg.trigger_guide then
			local guide_cfg = self:GetFBGuideByGuideId(boss_skill_guide_cfg.trigger_guide)
			local name, model = self:GetMosterNameAndModel(monster_id)

			if guide_cfg then
				FuBenPanelWGCtrl.Instance:OpenFuBenGuidePopDialog(guide_cfg, name, model)
			end
		end
	end
end

-- 触发boss出场引导
function CommonFbLogic:TriggerBossEnterGuide(monster_id, var_scene_type)
	local scene_type = self:GetSceneType()
	local cfg = ConfigManager.Instance:GetAutoConfig("fb_guide_auto")

	if cfg and scene_type == var_scene_type then
		local monster_enter_cfg = cfg.monster_enter and cfg.monster_enter[monster_id]

		if monster_enter_cfg ~= nil and monster_enter_cfg.trigger_guide then
			local guide_cfg = self:GetFBGuideByGuideId(monster_enter_cfg.trigger_guide)
			local name, model = self:GetMosterNameAndModel(monster_id)

			if guide_cfg then
				FuBenPanelWGCtrl.Instance:OpenFuBenGuidePopDialog(guide_cfg, name, model)
			end
		end
	end
end

-- 获取怪物名称和模型id
function CommonFbLogic:GetMosterNameAndModel(monster_id)
	local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[monster_id]
	if nil ~= cfg then
		return cfg.name, cfg.resid
	end

	return nil, nil
end