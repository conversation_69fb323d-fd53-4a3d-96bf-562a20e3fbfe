-- F2 每日充值

local Toggle_Vlaue = {
	[1] = 0,
	[2] = 0.15,
	[3] = 0.3,
	[4] = 0.45,
	[5] = 0.6,
	[6] = 0.75,
	[7] = 0.9,
	[8] = 1,
	[9] = 1,
	[10] = 1,
	[11] = 1,
}

EveryDayRechargeView = EveryDayRechargeView or BaseClass(SafeBaseView)

function EveryDayRechargeView:OpenCallBack()
	ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_LEICHONG_INFO)
	ServerActivityWGCtrl.Instance:SendEveryDayExpenseInfo()
end

function EveryDayRechargeView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.everyday_recharge_leichong, "uis/view/rechargereward_ui_prefab", "everyday_recharge")
	self:AddViewResource(TabIndex.everyday_recharge_zhigou, "uis/view/rechargereward_ui_prefab", "layout_recharge_zhigou")
	self:AddViewResource(TabIndex.everyday_recharge_xiaofei, "uis/view/rechargereward_ui_prefab", "everyday_xiaofei_recharge_view")
	self:AddViewResource(TabIndex.everyday_recharge_rapidly, "uis/view/rechargereward_ui_prefab", "everyday_rapidly_recharge_view")

	self:AddViewResource(TabIndex.everyday_recharge_lianchong, "uis/view/rechargereward_ui_prefab", "everyday_lianxu_recharge_view")
	self:AddViewResource(TabIndex.everyday_recharge_dailygift, "uis/view/rechargereward_ui_prefab", "everyday_libao_recharge_view")
	self:AddViewResource(TabIndex.everyday_recharge_yingji, "uis/view/rechargereward_ui_prefab", "everyday_yinji_turntable_view")
	self:AddViewResource(0, "uis/view/rechargereward_ui_prefab", "VerticalTabbar")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

	self.default_index = TabIndex.everyday_recharge_rapidly
	self.remind_tab = {
		{ RemindName.DailyRecharge_Rapidly },
		{ RemindName.DailyRecharge_Libao },
		{ RemindName.DailyRecharge_LeiChong },
		{ RemindName.DailyRecharge_LianChong },
		{ RemindName.DailyRecharge_YinJI },
		{ RemindName.DailyRecharge_ZhiGou },
		{ RemindName.DailyRecharge_XiaoFei },
	}
	self.tab_sub = {}

	self.def_rec_btn_index = 0

	self:SetMaskBg(false, false)
end

function EveryDayRechargeView:LoadCallBack()
	local bundle, asset = ResPath.GetCommonButton("a3_ty_hretutn")
	self.node_list["btn_close_window_img"].image:LoadSprite(bundle, asset, function()
		self.node_list["btn_close_window_img"].image:SetNativeSize()
	end)
	self.node_list.title_view_name.text.color = Str2C3b('#FCF8DD')

	self:LoadTabBar()
end

function EveryDayRechargeView:LoadTabBar()
	if not self.tabbar then
		local img_path = ResPath.GetF2RechargeIcon()
		local res_name = "a3_mrcz_tab"
		local tab_index_name = Language.Recharge.EveryDayRechargeToggleNameList
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabbarInfo, self))
		self.tabbar:SetVerTabbarNameImgRes(img_path, res_name)
		self.tabbar:Init(tab_index_name, nil, "uis/view/rechargereward_ui_prefab", nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.EveryDayRechargeView, self.tabbar)
		self.fun_change_call_back = BindTool.Bind(self.FunOpenChange, self)
		FunOpen.Instance:NotifyFunOpen(self.fun_change_call_back)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true, show_bind_gold = true, show_coin = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self.node_list.title_view_name.text.text = Language.EverydayRecharge.Title
end

function EveryDayRechargeView:FunOpenChange(func_name)
	if func_name == "everyday_recharge_yingji" then
		if FunOpen.Instance:GetFunIsOpenedByTabName("everyday_recharge_yingji") then
			self.tabbar:SetToggleVisible(TabIndex.everyday_recharge_yingji, true)
		else
			self.tabbar:SetToggleVisible(TabIndex.everyday_recharge_yingji, false)
		end
	end
end

function EveryDayRechargeView:SetTabbarInfo()
	--直购页签判断
	local is_show_zhigou = EveryDayRechargeRebateWGData.Instance:GetTabIsShow()
	self.tabbar:SetToggleVisible(TabIndex.everyday_recharge_zhigou, is_show_zhigou)
	--FunOpen.Instance:GetFunIsOpened("everyday_recharge_zhigou")
	local is_show_dailylibao = FunOpen.Instance:GetFunIsOpenedByTabName("everyday_recharge_dailygift")
	self.tabbar:SetToggleVisible(TabIndex.everyday_recharge_dailygift, is_show_dailylibao)

	if FunOpen.Instance:GetFunIsOpenedByTabName("everyday_recharge_yingji") then
		self.tabbar:SetToggleVisible(TabIndex.everyday_recharge_yingji, true)
	else
		self.tabbar:SetToggleVisible(TabIndex.everyday_recharge_yingji, false)
	end
end

function EveryDayRechargeView:LoadIndexCallBack(index)
	if index == TabIndex.everyday_recharge_leichong then
		self:InitParam()
		self:InitPanel()
		self:InitModel()
		self:InitListener()
		self:InitToggleList()
	elseif index == TabIndex.everyday_recharge_lianchong then
		self:InitLXRechargeView()
	elseif index == TabIndex.everyday_recharge_zhigou then
		self:InitEDRechargeView()
	elseif index == TabIndex.everyday_recharge_yingji then
		self:YJInitTurnTableView()
	elseif index == TabIndex.everyday_recharge_xiaofei then
		self:InitXFRechargeView()
	elseif index == TabIndex.everyday_recharge_rapidly then
		self:InitRapidlyRechargeView()
	elseif index == TabIndex.everyday_recharge_dailygift then
		self:InitDailyGiftRechargeView()
	end
end

function EveryDayRechargeView:ShowIndexCallBack(index)
	local bundle, assert = ResPath.GetF2RawImagesJPG("a3_mrlc_ty_bg")
	if index == TabIndex.everyday_recharge_leichong then
		self:ViewAnimation()
	elseif index == TabIndex.everyday_recharge_lianchong then
		self:ShowIndexLXRechargeView()
	elseif index == TabIndex.everyday_recharge_zhigou then
		self:ShowIndexEDRechargeView()
	elseif index == TabIndex.everyday_recharge_yingji then
		bundle, assert = ResPath.GetF2RawImagesPNG("a2_mrzc_bj1")
		self:YJShowIndexCallBack()
	elseif index == TabIndex.everyday_recharge_xiaofei then
		self:ExpenseDoAnimation()
	elseif index == TabIndex.everyday_recharge_dailygift then
		self:ShowIndexDailyGiftView()
	end

	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function EveryDayRechargeView:InitParam()
	self.open_day = 0
	self.toggle_select_index = -1
	self.dr_last_btn_type = nil
end

function EveryDayRechargeView:InitListener()
	XUI.AddClickEventListener(self.node_list.btn_lingqu, BindTool.Bind(self.OnClickLingQuBtn, self))
	XUI.AddClickEventListener(self.node_list["lc_login_get_btn"], BindTool.Bind(self.OnClickEveryDayLoginLingQuBtn, self))
end

function EveryDayRechargeView:InitPanel()
	local reward_item_list = {}
	local cell_root = self.node_list.cell_root
	for i = 1, 12 do
		reward_item_list[i] = ItemCell.New(cell_root)
	end

	self.reward_item_list = reward_item_list
	--self.everday_recharge_list = AsyncListView.New(EverdayRechargeItemRender, self.node_list.everday_recharge_list)
	self.recharge_box_list = {}
	self.recharge_box_red = {}
	self.recharge_box_txt = {}
	self.recharge_box_ylq = {}
	for i = 1, 3 do
		self.recharge_box_list[i] = self.node_list.recharge_box:FindObj("every_recharge_btn_" .. i)
		self.recharge_box_red[i] = self.recharge_box_list[i]:FindObj("every_recharge_red")
		self.recharge_box_txt[i] = self.recharge_box_list[i]:FindObj("every_recharge_text")
		self.recharge_box_ylq[i] = self.recharge_box_list[i]:FindObj("ylq_img")
		self.recharge_box_list[i]:SetActive(false)
		XUI.AddClickEventListener(self.recharge_box_list[i], BindTool.Bind2(self.OnClickGetRewardBtn, self, i))
	end
end

function EveryDayRechargeView:InitModel()
	self.tianshen_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["model_display_1"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.L,
		can_drag = true,
	}

	self.tianshen_model:SetRenderTexUI3DModel(display_data)
	self.tianshen_model:SetRTAdjustmentRootLocalScale(0.9)
	-- self.tianshen_model:SetUI3DModel(self.node_list["model_display_1"].transform,
	-- 	self.node_list["model_display_1"].event_trigger_listener, 1, nil, MODEL_CAMERA_TYPE.BASE)
	self:AddUiRoleModel(self.tianshen_model)

	local tianshen_id = ServerActivityWGData.Instance:GetLeiChongOtherCfg("show_fabao_id") -- 法宝改天神
	local tianshen_cfg = TianShenWGData.Instance:GetImageModelByAppeId(tianshen_id)
	if tianshen_cfg then
		self.tianshen_model:SetTianShenModel(tianshen_id, tianshen_cfg.index, false, nil, SceneObjAnimator.UiIdle)
		-- local tianshen_info = TianShenWGData.Instance:GetTianShenInfoByIndex(tianshen_cfg.index)
		-- self.node_list.cap_value.text.text = tianshen_info and tianshen_info.zhanli or 0
		local ts_item_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(tianshen_cfg.index)
		local item_cfg = ItemWGData.Instance:GetItemConfig(ts_item_cfg and ts_item_cfg.act_item_id)
		if item_cfg then
			local cap_value = ItemShowWGData.Instance.CalculateCapability(ts_item_cfg.act_item_id)
			self.node_list.cap_value.text.text = cap_value
		end
	end
end

function EveryDayRechargeView:InitToggleList()
	local toggle_list = {}
	local toggle_root = self.node_list.toggle_root
	for i = 1, 11 do
		local item_toggle = EverdayRechargeToggleRender.New(toggle_root.transform:Find("toggle_" .. i))
		item_toggle:AddClickEventListener(BindTool.Bind(self.OnClickToggle, self), true)
		item_toggle:SetIndex(i)
		toggle_list[i] = item_toggle
	end
	self.toggle_list = toggle_list
end

function EveryDayRechargeView:FlushDailyRechargeToggleRed()
	local red_type_1 = ServerActivityWGData.Instance:IsShowDailyRedEveryDay()
	local red_type_2 = ServerActivityWGData.Instance:IsShowDailyRedLeiJi()
	local red_login = ServerActivityWGData.Instance:IsShowDailyRedEveryDayLogin()

	self.node_list["lc_login_get_red"]:SetActive(red_login == 1)
	self.node_list.lc_login_ylq_img:SetActive(red_login == 2)
	local def_red_index = 0
	if red_type_1 == 1 then
		def_red_index = 1
	elseif red_type_2 == 1 then
		def_red_index = 2
	end
	def_red_index = self.def_rec_btn_index or def_red_index

	-- --宝箱动画
	-- if red_login == 1 then
	-- 	if not self.login_img_tween_shake then
	-- 		self.login_img_tween_shake = DG.Tweening.DOTween.Sequence()
	-- 		UITween.ShakeAnimi(self.node_list["lc_login_get_btn"].transform, self.login_img_tween_shake, 1)
	-- 	end
	-- else
	-- 	self:KillLoginImgShakeAnim()
	-- 	self.node_list["lc_login_get_btn"].transform.rotation = Quaternion.Euler(0, 0, 0)
	-- end
	-- XUI.SetGraphicGrey(self.node_list["lc_login_get_btn"], red_login == 2)
end

function EveryDayRechargeView:KillLoginImgShakeAnim()
	if self.login_img_tween_shake then
		self.login_img_tween_shake:Kill(true)
		self.login_img_tween_shake = nil
	end
end

function EveryDayRechargeView:ReleaseCallBack()
	if self.reward_item_list then
		for k, v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end
	if self.toggle_list then
		for k, v in pairs(self.toggle_list) do
			v:DeleteMe()
		end
		self.toggle_list = nil
	end
	if self.everday_recharge_list then
		self.everday_recharge_list:DeleteMe()
		self.everday_recharge_list = nil
	end
	if self.tianshen_model then
		self.tianshen_model:DeleteMe()
		self.tianshen_model = nil
	end

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	FunOpen.Instance:UnNotifyFunOpen(self.fun_change_call_back)
	self.recharge_box_list = {}
	self.recharge_box_red = {}
	self.recharge_box_txt = {}
	self.recharge_box_ylq = {}
	self:KillLoginImgShakeAnim()
	self:ReleaseLXRechargeView()
	self:ReleseEDRechargeView()
	self:YJReleaseCallBack()
	self:ReleseXFRechargeView()
	self:ReleaseRapidlyRechargeView()
	self:ReleseDailyGiftRechargeView()
end

function EveryDayRechargeView:Update(now_time, elapse_time)
	local show_index = self:GetShowIndex()
	if show_index == TabIndex.everyday_recharge_lianchong then
		self:LXRCUpdate(now_time, elapse_time)
	end
end

function EveryDayRechargeView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.everyday_recharge_leichong then
				if v.link_cfg_key then
					self.def_rec_btn_index = tonumber(v.link_cfg_key)
				end
				self:RefreshView()
			elseif index == TabIndex.everyday_recharge_lianchong then
				self:FlushLXRechargeView(true)
			elseif index == TabIndex.everyday_recharge_zhigou then
				self:FlushEDRechargeView()
			elseif index == TabIndex.everyday_recharge_yingji then
				self:FlushYJTurnTableView()
			elseif index == TabIndex.everyday_recharge_xiaofei then
				self:FlushXFRechargeView()
			elseif index == TabIndex.everyday_recharge_rapidly then
				self:FlushRapidlyRechargeView()
			elseif index == TabIndex.everyday_recharge_dailygift then
				self:FlushDailyGiftRechargeView()
			end
		elseif k == "ser_info" then
			self:RefreshView()
			self:RefreshLingQuBtnState()
		elseif k == "draw_reward" then
			if self:GetShowIndex() == TabIndex.everyday_recharge_yingji then
				self:DealWithDrawResultBack()
			end
		end
	end
end

--点击领取:发送档位请求
function EveryDayRechargeView:OnClickLingQuBtn()
	local data = ServerActivityWGData.Instance:GetLeiChongCfg(self.open_day, self.toggle_select_index)
	if not data then
		return
	end
	local can_get_flag = ServerActivityWGData.Instance:GetMeiRiFlagOne(data.id)
	if can_get_flag then
		ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_RECHARGE_REWARD, data.id)
	else
		ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
	end
end

function EveryDayRechargeView:OnClickEveryDayLoginLingQuBtn()
	local seq_index = 0
	local data = ServerActivityWGData.Instance:GetLeiChongCfg(self.open_day, seq_index)
	if not data then
		return
	end

	if self.node_list["lc_login_get_red"] and self.node_list["lc_login_get_red"].gameObject.activeSelf then
		ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_RECHARGE_REWARD, seq_index)
	else
		-- -- 弹Tip
		-- local reward_list = data and data.reward_item
		-- if reward_list or reward_list[0] then
		-- 	TipWGCtrl.Instance:OpenItem(reward_list[0])
		-- end
	end
end

function EveryDayRechargeView:OnClickToggle(item)
	local select_index = item:GetIndex()
	if self.toggle_select_index ~= select_index then
		self.toggle_select_index = select_index
		self:RefreshRewardPanel()
		self:RefreshLingQuBtnState()
		self:RefreshTodayRechargeNum()

		for k, v in pairs(self.toggle_list) do
			v:SetToggleIsOn(v:GetIndex() == select_index)
		end
	end
end

function EveryDayRechargeView:RefreshView()
	self:RefreshToggleList()
	self:RefreshRechargeList()
	self:RefreshTodayRechargeNum()
	self:FlushDailyRechargeToggleRed()
end

function EveryDayRechargeView:RefreshToggleList()
	if not self.toggle_list then
		return
	end

	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if open_day ~= self.open_day then
		self.open_day = open_day
		local data_list = ServerActivityWGData.Instance:GetLeiChongCfg(open_day)
		if not data_list then
			return
		end

		for i = 1, #self.toggle_list do
			if data_list[i] then
				self.toggle_list[i]:SetData(data_list[i])
			end
			self.toggle_list[i].node_list.root:SetActive(nil ~= data_list[i])
		end
	else
		for k, v in pairs(self.toggle_list) do
			v:FlushRemind()
		end
	end

	local toggle_item = self.toggle_list[1]
	for k, v in pairs(self.toggle_list) do
		if v:GetIsRemind() then
			toggle_item = v
			break
		end

		if not v:GetIsCanGet() then
			local data_list = ServerActivityWGData.Instance:GetLeiChongCfg(open_day)
			if not data_list then
				break
			end

			if data_list[k] then
				toggle_item = v
				break
			end
		end
	end
	toggle_item:SetToggleIsOn(true)
	self:OnClickToggle(toggle_item)

	-- if self.toggle_select_index <= #Toggle_Vlaue and self.node_list["recharge_toggle_rect"] then
	-- 	self.node_list["recharge_toggle_rect"].scroll_rect.horizontalNormalizedPosition = Toggle_Vlaue[self.toggle_select_index]
	-- end
end

function EveryDayRechargeView:RefreshTodayRechargeNum()
	if self.toggle_select_index ~= 0 then
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		-- local data_list = ServerActivityWGData.Instance:GetLeiChongCfg(open_day,self.toggle_select_index)
		local data = ServerActivityWGData.Instance:GetLeiChongCfg(open_day, self.toggle_select_index)
		-- local data = data_list and data_list[self.toggle_select_index]
		local total_recharge = data and data.recharge_num or 0
		local today_recharge = ServerActivityWGData.Instance:GetRechargeNum()
		local color = today_recharge >= total_recharge and COLOR3B.DEFAULT_NUM or COLOR3B.RED
		local recharge_str = CommonDataManager.ConverGoldByThousand(total_recharge)
		self.node_list.day_rechargenum.text.text = today_recharge
	else
		self.node_list.day_rechargenum.text.text = Language.EverydayRecharge.EveryDayLoginGet
	end
end

function EveryDayRechargeView:OnClickGetRewardBtn(index)
	local can_get_flag = ServerActivityWGData.Instance:GetLeiChongFlagOne(index - 1)
	local is_get_flag = ServerActivityWGData.Instance:GetLeiChongFlagTow(index - 1)
	if can_get_flag and not is_get_flag then
		ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_GET_LEIJI_CHONGZHI_REWARD,
			index - 1)
	elseif is_get_flag then

	else
		local count = ServerActivityWGData.Instance:GetEveryDayCurCycleCount()
		local data_list = ServerActivityWGData.Instance:GetLeiChongReward(count)
		if not data_list or not data_list[index] then
			return
		end

		local data_list =
		{
			view_type = RewardShowViewType.Normal,
			reward_item_list = data_list[index].reward_item
		}
		RewardShowViewWGCtrl.Instance:SetRewardShowData(data_list)
		SysMsgWGCtrl.Instance:ErrorRemind(Language.EverydayRecharge.NorNotRewardGet)
	end
end

function EveryDayRechargeView:RefreshRechargeList()
	local count = ServerActivityWGData.Instance:GetEveryDayCurCycleCount()
	local data_list = ServerActivityWGData.Instance:GetLeiChongReward(count)
	if not data_list then
		return
	end

	--self.everday_recharge_list:SetDataList(data_list)
	local cur_num = 0
	for k, v in pairs(data_list) do
		local is_get_flag = ServerActivityWGData.Instance:GetLeiChongFlagTow(v.index)
		if is_get_flag then
			cur_num = cur_num + 1
		end
	end

	local day_num = ServerActivityWGData.Instance:GetLeiChongDay()

	for k, v in pairs(data_list) do
		local can_get_flag = ServerActivityWGData.Instance:GetLeiChongFlagOne(v.index)
		local is_get_flag = ServerActivityWGData.Instance:GetLeiChongFlagTow(v.index)
		if self.recharge_box_list[k] then
			self.recharge_box_list[k]:SetActive(true)
			self.recharge_box_red[k]:SetActive(can_get_flag and not is_get_flag)
			self.recharge_box_txt[k].text.text = string.format(Language.EverydayRecharge.ToDayDesc2, day_num, v.index + 1)
			self.recharge_box_ylq[k]:SetActive(is_get_flag)
		end
	end

	local next_num = cur_num + 1
	if next_num > #data_list then
		next_num = #data_list
	end

	self.node_list.every_recharge_leichong_text.text.text = string.format(Language.EverydayRecharge.ReawardDesc1,
		Language.ChinaNub.hzNum[next_num + 1], data_list[next_num].leiji_recharge_num)
end

function EveryDayRechargeView:RefreshRewardPanel()
	local data = ServerActivityWGData.Instance:GetLeiChongCfg(self.open_day, self.toggle_select_index)
	local reward_list = data and data.reward_item
	if not reward_list then
		return
	end

	local is_get_flag = ServerActivityWGData.Instance:GetMeiRiFlagTow(data.id)

	local temp_list = {}
	local item_cfg = nil
	for k, v in pairs(reward_list) do
		item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			if item_cfg.color == 3 then -- **策划需求在这个界面紫色物品显示特效
				temp_list[k + 100 * item_cfg.color] = { item_id = v.item_id, num = v.num, is_bind = v.is_bind,
					effect_color = item_cfg.color }
			else
				temp_list[k + 100 * item_cfg.color] = v
			end
		end
	end
	temp_list = SortTableKey(temp_list, true)
	for k, v in pairs(self.reward_item_list) do
		if temp_list[k] then
			v:SetFlushCallBack(function ()
				v:SetLingQuVisible(is_get_flag)
			end)
			v:SetData(temp_list[k])
			v:SetActive(true)
		else
			v:SetActive(false)
		end
	end
end

function EveryDayRechargeView:RefreshLingQuBtnState()
	local data = ServerActivityWGData.Instance:GetLeiChongCfg(self.open_day, self.toggle_select_index)
	if not data then
		return
	end
	local can_get_flag = ServerActivityWGData.Instance:GetMeiRiFlagOne(data.id)
	local is_get_flag = ServerActivityWGData.Instance:GetMeiRiFlagTow(data.id)
	local can_get_reward = can_get_flag and not is_get_flag
	-- local bundle,asset = ResPath.GetF2RechargeIcon(can_get_reward and "mrcz_ann1" or "mrcz_ann2")
	-- self.node_list.btn_img.image:LoadSprite(bundle, asset)
	self.node_list["txt_btn_state"].text.text = can_get_reward and Language.EverydayRecharge.CanGetRewrad or
	Language.EverydayRecharge.NotCanGetRewrad

	self.node_list.btn_lq_red:SetActive(can_get_reward)
	self.node_list.btn_lingqu:SetActive(not is_get_flag)
	self.node_list.ylq_img:SetActive(is_get_flag)
end

function EveryDayRechargeView:ViewAnimation()
	local tween_info = UITween_CONSTS.EveryDayRechargeSys

	local tween_root_1 = self.node_list.recharge_right_root
	local tween_root_2 = self.node_list.recharge_left_root

	UITween.CleanAllTween(GuideModuleName.EveryDayRechargeView)
	--UITween.FakeHideShow(tween_root_1)
	--UITween.FakeHideShow(tween_root_2)
	if self.node_list.lc_login_get_btn then
		--UITween.DoUpDownCrashTween(self.node_list.lc_login_get_btn)
	end
	if self.node_list.common_capability then
		UITween.DoUpDownCrashTween(self.node_list.common_capability)
	end

	-- UITween.DoUpDownCrashTween(self.node_list.zhanli_root)

	-- ReDelayCall(self, function()
	-- 	--UITween.AlphaShow(GuideModuleName.EveryDayRechargeView,tween_root_2, 0, tween_info.ToAlpha, tween_info.AlphaTime)
	-- 	--UITween.AlphaShow(GuideModuleName.EveryDayRechargeView,tween_root_1, 0, tween_info.ToAlpha, tween_info.AlphaTime)
	-- 	-- local list = self.everday_recharge_list:GetAllItems()
	-- 	-- local sort_list = {}
	-- 	-- for i, v in pairs(list) do
	-- 	-- 	local data = {}
	-- 	-- 	data.index = v:GetIndex()
	-- 	-- 	data.item = v
	-- 	-- 	sort_list[#sort_list + 1] = data
	-- 	-- end
	-- 	-- table.sort(sort_list, SortTools.KeyLowerSorter("index"))

	-- 	-- local count = 0
	-- 	-- local cur_index = 0
	-- 	-- for k,v in ipairs(sort_list) do
	-- 	-- 	if 0 ~= v.index then
	-- 	-- 		count = count + 1
	-- 	-- 	end
	-- 	-- 	v.item:PalyItemAnimator(count)
	-- 	-- end
	-- end, tween_info.AlphaDelay, "everyday_recharge_tween_1")
end

--------------------EverdayRechargeToggleRender--------------------

EverdayRechargeToggleRender = EverdayRechargeToggleRender or BaseClass(BaseRender)

function EverdayRechargeToggleRender:__init()
	self.is_remind = false
	self.is_can_get = false
end

function EverdayRechargeToggleRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end
	local index = self:GetIndex()
	local recharge_str = CommonDataManager.ConverGoldByThousand(data.recharge_num)
	local str = index == 0 and Language.EverydayRecharge.TodayLogin or
	string.format(Language.EverydayRecharge.ToggleStr, recharge_str)
	self.node_list.normal_lbl.text.text = str

	local bg_name = data.special == 0 and "a3_mrlc_btn_dd" or "a3_mrlc_btn_gd"
	local bundle, asset = ResPath.GetF2RechargeIcon(bg_name)
	self.node_list.normal_bg.image:LoadSprite(bundle, asset, function()
		self.node_list.normal_bg.image:SetNativeSize()
	end)

	local reward_list = data.reward_item
	if not reward_list then
		return
	end
	local temp_list = {}
	local item_cfg = nil
	for k, v in pairs(reward_list) do
		item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		if item_cfg then
			v.icon_id = item_cfg.icon_id
			temp_list[k + 100 * item_cfg.color] = v
		end
	end
	temp_list = SortTableKey(temp_list, true)

	local icon_name = temp_list[1].icon_id
	local bundle, asset = ResPath.GetItem(icon_name)
	self.node_list.icon.image:LoadSprite(bundle, asset, function()
		self.node_list.icon.image:SetNativeSize()
	end)

	self:FlushRemind()
end

function EverdayRechargeToggleRender:FlushRemind()
	local data = self:GetData()
	if not data then
		return
	end
	self.is_can_get = ServerActivityWGData.Instance:GetMeiRiFlagOne(data.id)
	local is_get_flag = ServerActivityWGData.Instance:GetMeiRiFlagTow(data.id)
	self.is_remind = self.is_can_get and not is_get_flag
	self.node_list.can_get:SetActive(self.is_can_get and not is_get_flag)
	self.node_list.get_mask:SetActive(is_get_flag)
end

function EverdayRechargeToggleRender:SetToggleIsOn(_bool)
	local view = self:GetView()
	if view then
		view.toggle.isOn = _bool
		RectTransform.SetAnchoredPositionXY(self.node_list.root.rect, 0, _bool and 18 or 0)
	end
end

function EverdayRechargeToggleRender:GetIsRemind()
	return self.is_remind
end

function EverdayRechargeToggleRender:GetIsCanGet()
	return self.is_can_get
end

--------------------EverdayRechargeItemRender--------------------

EverdayRechargeItemRender = EverdayRechargeItemRender or BaseClass(BaseRender)

function EverdayRechargeItemRender:__delete()
	for _, v in pairs(self.item_cell_list) do
		v:DeleteMe()
	end
	self.item_cell_list = nil
end

function EverdayRechargeItemRender:LoadCallBack()
	local item_cell_list = {}
	for i = 1, 3 do
		item_cell_list[i] = ItemCell.New(self.node_list.cell_root)
	end
	self.item_cell_list = item_cell_list
	self.node_list.get_reward_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetRewardBtn, self))
	self.node_list.not_enough_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetNotFinishBtn, self))
end

function EverdayRechargeItemRender:OnFlush()
	local data = self:GetData()
	local day_num = ServerActivityWGData.Instance:GetLeiChongDay()
	local need_day_num = data.index + 1
	self.node_list.reward_desc.text.text = string.format(Language.EverydayRecharge.ReawardDesc, need_day_num, day_num,
		need_day_num, data.leiji_recharge_num)


	for i, v in ipairs(self.item_cell_list) do
		if data.reward_item[i - 1] then
			v:SetData(data.reward_item[i - 1])
			v:SetVisible(true)
		else
			v:SetVisible(false)
		end
	end

	local can_get_flag = ServerActivityWGData.Instance:GetLeiChongFlagOne(data.index)
	local is_get_flag = ServerActivityWGData.Instance:GetLeiChongFlagTow(data.index)
	self.node_list.not_enough_btn:SetActive(not can_get_flag and not is_get_flag)
	self.node_list.get_reward_btn:SetActive(can_get_flag and not is_get_flag)
	self.node_list.get_reward_img:SetActive(is_get_flag)
end

function EverdayRechargeItemRender:OnClickGetRewardBtn()
	local data = self:GetData()
	if data then
		ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_GET_LEIJI_CHONGZHI_REWARD,
			data.index)
	end
end

function EverdayRechargeItemRender:OnClickGetNotFinishBtn()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.EverydayRecharge.NorNotRewardGet)
end

function EverdayRechargeItemRender:PalyItemAnimator(item_index)
	local wait_index = item_index - 1
	wait_index = wait_index < 0 and 0 or wait_index
	local tween_info = UITween_CONSTS.EveryDayRechargeTween

	UITween.FakeHideShow(self.node_list["root_tween"])

	ReDelayCall(self, function()
		if self.node_list and self.node_list["root_tween"] then
			UITween.RotateAlphaShow(GuideModuleName.Rank, self.node_list["root_tween"], tween_info)
		end
	end, tween_info.NextDoDelay * wait_index, "EveryDayRechargeCell_" .. wait_index)
end
