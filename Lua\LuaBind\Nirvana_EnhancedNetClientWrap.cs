﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_EnhancedNetClientWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.EnhancedNetClient), typeof(System.Object));
		<PERSON><PERSON>Function("SetReadLength", SetReadLength);
		<PERSON><PERSON>unction("Clear", Clear);
		<PERSON><PERSON>unction("Connect", Connect);
		<PERSON><PERSON>RegFunction("Disconnect", Disconnect);
		<PERSON><PERSON>RegFunction("SetKeepAlive", SetKeepAlive);
		<PERSON><PERSON>RegFunction("SendMsg", SendMsg);
		<PERSON><PERSON>RegFunction("StartReceive", StartReceive);
		<PERSON><PERSON>RegFunction("ListenDisconnectWithReason", ListenDisconnectWithReason);
		L.RegFunction("UnlistenDisconnect", UnlistenDisconnect);
		<PERSON>.RegFunction("UnlistenMessage", UnlistenMessage);
		<PERSON><PERSON>Function("ListenMessage", ListenMessage);
		<PERSON><PERSON>unction("New", _CreateNirvana_EnhancedNetClient);
		<PERSON><PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("ReceiveTimeout", get_ReceiveTimeout, set_ReceiveTimeout);
		L.RegVar("SendTimeout", get_SendTimeout, set_SendTimeout);
		L.RegVar("DisconnectEvent", get_DisconnectEvent, set_DisconnectEvent);
		L.RegVar("ReceiveEvent", get_ReceiveEvent, set_ReceiveEvent);
		L.RegFunction("ConnectDelegate", Nirvana_EnhancedNetClient_ConnectDelegate);
		L.RegFunction("SendDelegate", Nirvana_EnhancedNetClient_SendDelegate);
		L.RegFunction("DisconnectWithReasonDelegate", Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate);
		L.RegFunction("DisconnectDelegate", Nirvana_EnhancedNetClient_DisconnectDelegate);
		L.RegFunction("ReceiveDelegate", Nirvana_EnhancedNetClient_ReceiveDelegate);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_EnhancedNetClient(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.EnhancedNetClient obj = new Nirvana.EnhancedNetClient();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.EnhancedNetClient.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetReadLength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SetReadLength(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Clear(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			obj.Clear();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Connect(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 3)
			{
				Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
				string arg0 = ToLua.CheckString(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				obj.Connect(arg0, arg1);
				return 0;
			}
			else if (count == 4)
			{
				Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
				string arg0 = ToLua.CheckString(L, 2);
				int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
				Nirvana.EnhancedNetClient.ConnectDelegate arg2 = (Nirvana.EnhancedNetClient.ConnectDelegate)ToLua.CheckDelegate<Nirvana.EnhancedNetClient.ConnectDelegate>(L, 4);
				obj.Connect(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.EnhancedNetClient.Connect");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Disconnect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			obj.Disconnect();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetKeepAlive(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			uint arg0 = (uint)LuaDLL.luaL_checknumber(L, 2);
			uint arg1 = (uint)LuaDLL.luaL_checknumber(L, 3);
			uint arg2 = (uint)LuaDLL.luaL_checknumber(L, 4);
			obj.SetKeepAlive(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SendMsg(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
				byte[] arg0 = ToLua.CheckByteBuffer(L, 2);
				obj.SendMsg(arg0);
				return 0;
			}
			else if (count == 3)
			{
				Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
				byte[] arg0 = ToLua.CheckByteBuffer(L, 2);
				Nirvana.EnhancedNetClient.SendDelegate arg1 = (Nirvana.EnhancedNetClient.SendDelegate)ToLua.CheckDelegate<Nirvana.EnhancedNetClient.SendDelegate>(L, 3);
				obj.SendMsg(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.EnhancedNetClient.SendMsg");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StartReceive(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			obj.StartReceive();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListenDisconnectWithReason(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate arg0 = (Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate)ToLua.CheckDelegate<Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate>(L, 2);
			Nirvana.EnhancedNetClient.DisconnectDelegate o = obj.ListenDisconnectWithReason(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnlistenDisconnect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			Nirvana.EnhancedNetClient.DisconnectDelegate arg0 = (Nirvana.EnhancedNetClient.DisconnectDelegate)ToLua.CheckDelegate<Nirvana.EnhancedNetClient.DisconnectDelegate>(L, 2);
			obj.UnlistenDisconnect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnlistenMessage(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			Nirvana.EnhancedNetClient.ReceiveDelegate arg0 = (Nirvana.EnhancedNetClient.ReceiveDelegate)ToLua.CheckDelegate<Nirvana.EnhancedNetClient.ReceiveDelegate>(L, 2);
			obj.UnlistenMessage(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListenMessage(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			NetClientExtensions.ReceiveMessageDelegate arg0 = (NetClientExtensions.ReceiveMessageDelegate)ToLua.CheckDelegate<NetClientExtensions.ReceiveMessageDelegate>(L, 2);
			Nirvana.EnhancedNetClient.ReceiveDelegate o = obj.ListenMessage(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ReceiveTimeout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)o;
			int ret = obj.ReceiveTimeout;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ReceiveTimeout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_SendTimeout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)o;
			int ret = obj.SendTimeout;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SendTimeout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_DisconnectEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EnhancedNetClient.DisconnectDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ReceiveEvent(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Nirvana.EnhancedNetClient.ReceiveDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ReceiveTimeout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.ReceiveTimeout = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ReceiveTimeout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_SendTimeout(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.SendTimeout = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index SendTimeout on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_DisconnectEvent(IntPtr L)
	{
		try
		{
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EnhancedNetClient.DisconnectEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EnhancedNetClient'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EnhancedNetClient.DisconnectDelegate ev = (Nirvana.EnhancedNetClient.DisconnectDelegate)arg0.func;
				obj.DisconnectEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EnhancedNetClient.DisconnectDelegate ev = (Nirvana.EnhancedNetClient.DisconnectDelegate)arg0.func;
				obj.DisconnectEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ReceiveEvent(IntPtr L)
	{
		try
		{
			Nirvana.EnhancedNetClient obj = (Nirvana.EnhancedNetClient)ToLua.CheckObject(L, 1, typeof(Nirvana.EnhancedNetClient));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Nirvana.EnhancedNetClient.ReceiveEvent' can only appear on the left hand side of += or -= when used outside of the type 'Nirvana.EnhancedNetClient'");
			}

			if (arg0.op == EventOp.Add)
			{
				Nirvana.EnhancedNetClient.ReceiveDelegate ev = (Nirvana.EnhancedNetClient.ReceiveDelegate)arg0.func;
				obj.ReceiveEvent += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Nirvana.EnhancedNetClient.ReceiveDelegate ev = (Nirvana.EnhancedNetClient.ReceiveDelegate)arg0.func;
				obj.ReceiveEvent -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Nirvana_EnhancedNetClient_ConnectDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.ConnectDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.ConnectDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Nirvana_EnhancedNetClient_SendDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.SendDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.SendDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Nirvana_EnhancedNetClient_DisconnectWithReasonDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.DisconnectWithReasonDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Nirvana_EnhancedNetClient_DisconnectDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.DisconnectDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.DisconnectDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Nirvana_EnhancedNetClient_ReceiveDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.ReceiveDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<Nirvana.EnhancedNetClient.ReceiveDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

