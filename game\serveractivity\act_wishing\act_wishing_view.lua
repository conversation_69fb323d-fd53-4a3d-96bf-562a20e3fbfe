ActwishingView = ActwishingView or BaseClass(SafeBaseView)

function ActwishingView:__init()
	--self.is_modal = true              --设置膜层
	self:SetMaskBg(false,true)
	self.view_layer = UiLayer.Pop
	
	self:LoadConfig()
	self.wishing_gift_list = {}
	self.set_wish = false
	self.title = nil
	self.common_display = nil
	self.treas_nem = 0
	self.is_reminder = false
end

function ActwishingView:LoadConfig()
	self:AddViewResource(0, "uis/view/act_promise_prefab", "layout_wishing_content")
end

function ActwishingView:__delete()

end

function ActwishingView:ReleaseCallBack()
	self.is_load_complete = false
	if nil ~= self.progress_bar then
		self.progress_bar:DeleteMe()
		self.progress_bar = nil
	end

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

	if nil ~= self.common_display then
		self.common_display:DeleteMe()
		self.common_display = nil
	end

	if nil ~= self.title then
		self.title:DeleteMe()
		self.title = nil
	end

	-- if nil ~= self.wishing_gift then
	-- 	self.wishing_gift:DeleteMe()
	-- 	self.wishing_gift = nil
	-- end

	if nil ~= self.lbl_zhandoiuli then
		self.lbl_zhandoiuli:DeleteMe()
		self.lbl_zhandoiuli = nil
	end

	for i=1,#self.wishing_gift_list do
		if nil ~= self.wishing_gift_list[i] then
			self.wishing_gift_list[i]:DeleteMe()
			self.wishing_gift_list[i] = nil
		end
	end

	if nil ~= self.add_val_alert then
		self.add_val_alert:DeleteMe()
		self.add_val_alert = nil
	end

	if CountDownManager.Instance:HasCountDown("wish_pool_left_time") then
		CountDownManager.Instance:RemoveCountDown("wish_pool_left_time")
	end

	self.img_btn_text = nil
	self.is_reminder = false
	self.active_day = nil
end

function ActwishingView:LoadCallBack()
	self.node_list.rich_rule_text_1.text.text = (Language.XuYuan.wish_rules)   -- 许愿规则
	XUI.AddClickEventListener(self.node_list.btn_wishing_one, BindTool.Bind1(self.OnClickBtnWishOne, self))
	XUI.AddClickEventListener(self.node_list.btn_wishing_more, BindTool.Bind1(self.OnClickBtnWishSelf, self))
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.OnCloseWiew, self))
	XUI.AddClickEventListener(self.node_list.btn_material, BindTool.Bind1(self.OnClickMaterial, self))
	self.add_val_alert = Alert.New(nil,nil,nil,nil,true)
	self.node_list.wish_slider.slider.maxValue = 1 -- other_cfg[1].wishpool_process_max
	-- 幸运奖励
	for i=1,7 do
		local wishing_gift = ItemCell.New(self.node_list["ph_wishing_gift_"..i])
		self.wishing_gift_list[i] = wishing_gift
		if i == 7 then
			self.is_load_complete = true
		end
	end

	if self.is_should_call then
		--请求服务器更新数据
		self:ShowWishData()
		local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL) or {}
		if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
			local next_time = act_cornucopia_info.next_time - (TimeWGCtrl.Instance:GetServerTime() or 0)
			self:UpdataRollerTime(1, next_time)
			CountDownManager.Instance:AddCountDown("wish_pool_left_time", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), nil, next_time, 1)
		else
			self:CompleteRollerTime()
		end
		self.is_should_call = false
	end
	self.node_list.rare_display:SetActive(false)

	self.display_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["SiutDisplay"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}
	
	self.display_model:SetRenderTexUI3DModel(display_data)
end

function ActwishingView:OnFlush()
	if not self.is_load_complete then
		self.is_should_call = true
		return
	end
	local wish_info = ActwishingWGData.Instance:GetCirculationWishInfo()
	local day_cfg = ActwishingWGData.Instance:GetWishPoolData()                      --获取表中数据（许愿池）
	local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()               --获取表中数据（其他）
	local active_day = ActwishingWGData.Instance:GetWishPoolDay()    --  获取天数
	local wish_pool_process = ActwishingWGData.Instance:GetWishPoolProcess()
	if not day_cfg or not next(day_cfg) then return end
	local zhanli = 0
	-- local zhanli_index = 0
	-- 奖励物品
	for i = 1,#self.wishing_gift_list do
		if nil ~= self.wishing_gift_list[i] then
			-- if day_cfg[i].type ~= 1 then -- and day_cfg[7].res_type == 0
			-- 	local gift_item = ItemWGData.Instance:GetGiftConfig(day_cfg[i].reward_item[0].item_id)
			-- 	if gift_item == nil then return end
			-- 	self.wishing_gift_list[i]:SetData({item_id = gift_item.item_data[1].id, num = gift_item.item_data[1].num, is_bind = gift_item.item_data[1].isbind, param = gift_item.item_data[1].param})
			-- 	self.wishing_gift_list[i]:SetItemTipFrom(ItemTip.FROM_COOL_BUY)
			-- else
				self.wishing_gift_list[i]:SetData(day_cfg[i].reward_item[0])
				if day_cfg[i].show_zhanli and day_cfg[i].show_zhanli > 0 then
					zhanli = day_cfg[i].show_zhanli
					-- zhanli_index = i
					self.display_item_id = day_cfg[i].reward_item[0].item_id
					self.display_cfg = day_cfg[i]
				end
			-- end
		end
	end

	-- local item_wpid = day_cfg[7].reward_item[0].item_id
	self:ChangeDisplay(active_day)

	if zhanli ~= 0 then
		--self.lbl_zhandoiuli:SetNumber(day_cfg[7].show_zhanli)
		self.node_list["power"].text.text = zhanli
	else
		self.node_list.layout_zhandouli_bg:SetActive(false)
	end
	-- local gold = RoleWGData.Instance:GetRoleInfo().gold or 0
	-- gold = CommonDataManager.ConverMoney(gold)
	-- self.node_list.rich_xiaohao_text.text.text = gold

	self.node_list.rich_consume_percent.text.text = string.format("%d/%d", wish_pool_process or 0, other_cfg[1].wishpool_process_max )

	self.node_list.wish_slider.slider.value = wish_pool_process/other_cfg[1].wishpool_process_max
	local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()
	local price_info = ShopWGData.GetItemPrice(other_cfg[1].wishpool_gift)
	self.add_val_alert:SetOkString(Language.Common.Confirm)
	self.add_val_alert:SetCancelString(Language.Common.Cancel)
	self.add_val_alert:SetLableString(string.format(Language.XuYuan.wish_do_that , price_info.gold))
	self.add_val_alert:SetOkFunc(function()
			if self.treas_nem == 1 and self.treas_nem ~= 0 then
				ActwishingWGCtrl.Instance:SendWishInfoReq()
				self.add_val_alert:Close()
			else
				self:OnClickBtnWishingSelf()
			end
		end)
end

function ActwishingView:ChangeDisplay(active_day )
	if not self.display_cfg then return end
	local item_wpid = self.display_cfg.reward_item[0].item_id
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_wpid)
	if self.active_day ~= active_day then
		self.active_day = active_day
		local display = self.node_list.rare_display
		display:SetActive(false)

		if self.display_cfg.res_type == 1 then
			self:ShowDisplay(item_wpid)
		end
	end
end

function ActwishingView:SetDisplayModel(path_fnc,res_id)
	if not path_fnc then
		print_error("can not use the path_fnc!")
		return
	end
	local bundle, asset = path_fnc(res_id)
	if not bundle or not asset then
		print_error("bundle or asset can not find!",bundle,asset)
		return
	end

	self.display_model:SetSceneModelPositionAndRotation(Vector3(-5, -3.4, 16), Quaternion.Euler(0, -227, 0))
	if self.display_model then
		self.display_model:SetMainAsset(bundle,asset)
	else
		print_error("ActwishingView","SetRightDisplayModel error!")
	end
end

-- 模型展示
function ActwishingView:ShowDisplay(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		print_error("ActwishingView","ShowDisplay() item_cfg is a nil value: item_id = ",item_id)
		return
	end
	if item_cfg.display_item_id and item_cfg.display_item_id > 0 then
		item_cfg = ItemWGData.Instance:GetItemConfig(item_cfg.display_item_id)
		if item_cfg == nil then
			print_error("ActwishingView","ShowDisplay() item_cfg is a nil value: display_item_id = ",item_cfg.display_item_id)
			return
		end
	end

	local display_type = item_cfg.is_display_role
	local path = nil
	local bundle = nil
	local asset = nil
	local res_id = nil
	local image_type = nil
	local attr_cfg = nil
	local animation_type = nil
	local part_type = nil

	if display_type == 15 then 				-- 15 ---> 小鬼
		--res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		--path, res_id,animation_type = self:GetFashionModlePathFun(part_type,item_cfg)			--TODO,返回资源暂时写死，后期删除 res_id
		--self.attr_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(part_type, attr_cfg.index, 1)
		res_id = self:GetXiaoGuiResId(item_cfg.id)
		local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(item_cfg.id)
		if xiaogui_cfg then
			self.model_display:SetModelScale(Vector3(xiaogui_cfg.scale, xiaogui_cfg.scale, xiaogui_cfg.scale))
		end
		path = ResPath.GetGuardModel
		animation_type = nil
		-- self.node_list["zhanli"]:SetActive(false)


	elseif display_type == DisplayItemTip.Display_type.FASHION then 				--时装
		res_id, part_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		if attr_cfg then
			path, res_id,animation_type = self:GetFashionModlePathFun(part_type,item_cfg)			--TODO,返回资源暂时写死，后期删除 res_id
			self.attr_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(part_type, attr_cfg.index, 1)
		end
	
		if not path then --xxxx
			self.display_model:SetWeaponModel(ResPath.GetShenBingModel(res_id))
		end
	elseif display_type == DisplayItemTip.Display_type.FABAO then 					--法宝
		-- print_error("法宝----")
		res_id, image_type, attr_cfg = NewAppearanceWGData.Instance:GetFashionResByItemId(item_cfg.id)
		path = ResPath.GetFaBaoModel
		if attr_cfg then
			self.attr_cfg = NewAppearanceWGData.Instance:GetFashionUpLevelAttrCfg(image_type, attr_cfg.index, 1)
		end
	elseif display_type == DisplayItemTip.Display_type.BABY then 					--宝宝 	--TODO
		res_id, attr_cfg = MarryWGData.Instance:GetBabyResByItemId(item_cfg.id)
		self.attr_cfg = attr_cfg
		path = ResPath.GetHaiZiModel

	elseif display_type == DisplayItemTip.Display_type.MultiMount then 				--双人坐骑


	elseif display_type == DisplayItemTip.Display_type.CHENGHAO then 				--称号
		-- print_error("称号---")
		local title_id = item_cfg.param1
		self.attr_cfg = TitleWGData.Instance.GetTitleConfig(item_cfg.param1)
		--self:SetDisplayShow(false)
		self.node_list.img_title_display.image:LoadSprite(ResPath.GetRoleTitle(title_id))
	end

	-- if self.attr_cfg then
	-- 	local attr = AttributeMgr.GetAttributteByClass(self.attr_cfg)
	-- 	local capability = AttributeMgr.GetCapability(attr)
	-- 	self.node_list.zhanli_text.text.text = capability
	-- end
	--print_log("DisplayItemTip","暂时使用模型代替==================================",path,res_id,animation_type)
	self:SetDisplayModel(path,res_id)
end

function ActwishingView:OpenCallBack()
	if not self.is_load_complete then
		self.is_should_call = true
		return
	else
		self:ShowWishData()   													--请求服务器更新数据
		local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL) or {}
		if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
			local next_time = act_cornucopia_info.next_time - (TimeWGCtrl.Instance:GetServerTime() or 0)
			self:UpdataRollerTime(1, next_time)
			CountDownManager.Instance:AddCountDown("wish_pool_left_time", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), nil, next_time, 1)
		else
			self:CompleteRollerTime()
		end
	end
end

function ActwishingView:UpdataRollerTime(elapse_time, next_time)
	if self.node_list.lable_remaining_time ~= nil then
		local format_time = TimeUtil.FormatSecondDHM2(next_time - elapse_time)
		self.node_list.lable_remaining_time.text.text = (format_time)
	end
end

function ActwishingView:CompleteRollerTime()
	if self.node_list.lable_remaining_time ~= nil then
		self.node_list.lable_remaining_time.text.text = ("0")
	end
end

function ActwishingView:ShowIndexCallBack()
	-- self:Flush()
end

function ActwishingView:OnClickBtnWishOne()
	if ItemWGData.Instance:GetEmptyNum() <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotBagRoom)
		return
	end
	if GlobalTimerQuest:GetRunQuest(self.bottom_on_off_state) then
		return
	end
	local is_finish = ActwishingWGData.Instance:GetIsFinish()
	if is_finish == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.XuYuan.HasGetReword)
		return
	end
	local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].wishpool_item_id)
	if item_num <= 0 then
		self.treas_nem = 1
		self.add_val_alert:Open()
	else
		ActwishingWGCtrl.Instance:SendAllInfoReq()
	end
	self.bottom_on_off_state = GlobalTimerQuest:AddDelayTimer(function()
		if nil ~= self.bottom_on_off_state then
			GlobalTimerQuest:CancelQuest(self.bottom_on_off_state)
			self.bottom_on_off_state = nil
		end
	end, 0.5)
end

function ActwishingView:ShowWishData()
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_WISH_POOL,
		opera_type = RA_WISH_POOL_TYPE.RA_WISH_POOL_TYPE_USE_GOLD_INFO
	}
	
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ActwishingView:OnClickBtnWishSelf()
	local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].wishpool_item_id)
	if ItemWGData.Instance:GetEmptyNum() <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotBagRoom)
		return
	end
	local is_finish = ActwishingWGData.Instance:GetIsFinish()
	if is_finish == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.XuYuan.HasGetReword)
		return
	end
	if item_num <= 0 and self.is_reminder == false  then
		self.treas_nem = 2
		self.add_val_alert:Open()
	else
		self:OnClickBtnWishingSelf()
	end

end

function ActwishingView:OnClickBtnWishingSelf()
	if self.set_wish == false then
		self.node_list.text_more_wish.text.text = (Language.XuYuan.wish_stop)
		self.is_reminder = true
		self:OnGlobalTimer()
	else
		if self.WishPool ~= nil then
			self.set_wish = false
			self.is_reminder = false
			self.node_list.text_more_wish.text.text = (Language.XuYuan.wish_start)
			GlobalTimerQuest:CancelQuest(self.WishPool)
		end
	end
end

function ActwishingView:OnGlobalTimer()
	self.set_wish = true
	local role_gold = RoleWGData.Instance.role_info.gold or 0  --人物信息
	local other_cfg = ActwishingWGData.Instance:GetWishPoolOtherData()
	local price_info = ShopWGData.GetItemPrice(other_cfg[1].wishpool_gift)
	if other_cfg == nil then return end
	local wish_pool_process = ActwishingWGData.Instance:GetWishPoolProcess() or 100000

	if self.WishPool ~= nil then
		GlobalTimerQuest:CancelQuest(self.WishPool)
	end

	ActwishingWGCtrl.Instance:SendWishInfoReq()
	if role_gold >= price_info.gold and wish_pool_process < other_cfg[1].wishpool_process_max then
		local is_finish = ActwishingWGData.Instance:GetIsFinish()
		if is_finish == 1 then
			self.set_wish = false
			self.node_list.text_more_wish.text.text = (Language.XuYuan.wish_start)
			GlobalTimerQuest:CancelQuest(self.WishPool)
		else
			self.WishPool= GlobalTimerQuest:AddDelayTimer(BindTool.Bind1(self.OnGlobalTimer, self), 0.5) -- 计时器
		end

	else
		self.set_wish = false
		self.node_list.text_more_wish.text.text = (Language.XuYuan.wish_start)
		GlobalTimerQuest:CancelQuest(self.WishPool)
	end
end

function ActwishingView:OnCloseWiew()
	if self.WishPool ~= nil then
		self.set_wish = false
		self.node_list.text_more_wish.text.text = (Language.XuYuan.wish_start)
		GlobalTimerQuest:CancelQuest(self.WishPool)   -- 取消计时器
	end
	ActTreasureWGData.Instance:SetAutoUseGiftFlag(false)
	self:Close()
end

function ActwishingView:OnClickMaterial()
	TipWGCtrl.Instance:OpenItem({item_id = 27511})
end
