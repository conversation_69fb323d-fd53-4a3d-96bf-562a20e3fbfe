-- Q-渠道显示区分.xls

return {
place_differentiate={
{},
{place_id="axl",},
{place_id="a39",},
{place_id="a40",},
{place_id="af2",},
{place_id="aft",},
{place_id="ax2",},
{place_id="i39",},
{place_id="if2",},
{place_id="ift",},
{place_id="its",},
{place_id="ix2",},
{place_id="ixl",}
},

place_differentiate_meta_table_map={
},
place_differentiate_default_table={place_id="dev",whether_show=0,is_show_dollar=1,}

}

