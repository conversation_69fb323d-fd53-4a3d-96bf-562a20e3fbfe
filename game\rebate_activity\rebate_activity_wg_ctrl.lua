require("game/rebate_activity/rebate_actvity_view")
require("game/rebate_activity/rebate_recharge_view")
require("game/rebate_activity/rebate_day_view")
require("game/rebate_activity/rebate_lianxu_recharge_view")--连续充值
require("game/rebate_activity/rebate_activity_wg_data")
require("game/rebate_activity/rebate_day_activity_wg_data")
require("game/rebate_activity/recharge_activity_wg_data")
require("game/rebate_activity/rebate_lianxu_rechage_wg_data")

RebateActivityWGCtrl = RebateActivityWGCtrl or BaseClass(BaseWGCtrl)

function RebateActivityWGCtrl:__init()
	if RebateActivityWGCtrl.Instance then
        error("[RebateActivityWGCtrl]:Attempt to create singleton twice!")
	end
	RebateActivityWGCtrl.Instance = self

	self.data = RebateActivityWGData.New()
    self.rebate_day_activity_data = RebateDayActivityWGData.New() --七日返利
    self.recharge_activity_data = RechargeActivityWGData.New() --直购返利
    self.rebate_lianxu_rechage_data = RebateLianXuRechargeWGData.New() --连续充值
    self.view = RebateActivityView.New(GuideModuleName.RebateActivityView)
	self:RegisterAllProtocals()
end

function RebateActivityWGCtrl:__delete()
    RebateActivityWGCtrl.Instance = nil
    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

    if self.recharge_activity_data then
        self.recharge_activity_data:DeleteMe()
        self.recharge_activity_data = nil
    end

    if self.rebate_day_activity_data then
        self.rebate_day_activity_data:DeleteMe()
        self.rebate_day_activity_data = nil
    end

    if self.rebate_lianxu_rechage_data then
        self.rebate_lianxu_rechage_data:DeleteMe()
        self.rebate_lianxu_rechage_data = nil
    end

    if self.view then
        self.view:DeleteMe()
        self.view = nil
    end

end

function RebateActivityWGCtrl:RegisterAllProtocals()

    
    -- self:RegisterProtocol(SCActZhiChongInfo, "OnSCActZhiChongInfo")
    self:RegisterProtocol(SCRebateBoxInfo, "OnSCRebateBoxInfo")
    -- self:RegisterProtocol(SCLianXuChongZhiInfo, "OnSCLianXuChongZhiInfo")
end

function RebateActivityWGCtrl:ChangeSelectIndex()
	--活动关闭的时候对页签进行一次刷新并且重新做选中
	if self.view:IsOpen() then
		--优先判断是否还有已开启的活动
		if self.data:GetActivityIsClose() then
			self.view:Close()
			return
		end

		self.view:SetTabSate()
		--如果当前选择的活动是开启状态那么返回
		local index = self.view:GetShowIndex() or 0
		local activity_type = self.data:GetCurSelectActivityType(index)
		local is_open = self.data:GetActivityState(activity_type)
		if is_open then
			return
		end
		self.view:Open() --有意传空让他做选择
	end
end

function RebateActivityWGCtrl:SetDelayShowTipsNum(gold_num)
    self.rebate_day_activity_data:SaveGoldNum(gold_num)
    ViewManager.Instance:FlushView(GuideModuleName.RebateActivityView, TabIndex.rebate_activity_2250, "rebate_day_play_xianyu")
end


function RebateActivityWGCtrl:OnSCRebateBoxInfo(protocol)
    self.rebate_day_activity_data:SaveData(protocol)
    self.view:Flush()
    RemindManager.Instance:Fire(RemindName.RebateDayRemind)
end

function RebateActivityWGCtrl:GetActivityIsOpen(tab_index)
	if not tab_index or not TabIndex[tab_index] then
		return false
	end

	local activity_type = self.data:GetCurSelectActivityType(TabIndex[tab_index])
	local state = self.data:GetActivityState(activity_type)

	if state then
		return true
	end

	return false
end

function RebateActivityWGCtrl:SendActivityRewardOp(activity_type, opera_type,param1, param2, param3)
    ServerActivityWGCtrl.Instance:SendActivityRewardOp(activity_type, opera_type,param1, param2, param3)
end

function RebateActivityWGCtrl:OnSCLianXuChongZhiInfo(protocol)
    self.rebate_lianxu_rechage_data:SaveData(protocol)
    self.view:Flush()
    RemindManager.Instance:Fire(RemindName.Rebate_LianXuChongZhi)
end

function RebateActivityWGCtrl:SendLianXuChongZhiReq(btn_type, reward_type, reward_index)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
    protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LIANXUCHONGZHI
    protocol.opera_type = 0
    protocol.param_1 = btn_type or 0
    protocol.param_2 = reward_type or 0
    protocol.param_3 = reward_index or 0
    protocol:EncodeAndSend()
end

-- function RebateActivityWGCtrl:SendOperaReq(opera_type,param_1,param_2,param_3,param_4)
-- 	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
--   	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_FORTUNE_CAT
--   	protocol.opera_type = opera_type or 0
--   	protocol.param_1 = param_1 or 0
--   	protocol.param_2 = param_2 or 0
--   	protocol.param_3 = param_3 or 0
--   	protocol.param_4 = param_4 or 0
--   	protocol:EncodeAndSend()
-- end

function RebateActivityWGCtrl:OnSCActZhiChongInfo(protocol)
    self.recharge_activity_data:OnSCActZhiChongInfo(protocol)
    self.view:Flush()
    RemindManager.Instance:Fire(RemindName.Rebate_ZhiGou)
end

function RebateActivityWGCtrl:SendActZhiGouOpera(opera_type,param1,param2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
    protocol.rand_activity_type = ACTIVITY_TYPE.REBATE_ZHIGOU_FANLI
    protocol.opera_type = opera_type
    protocol.param_1 = param1 or 0
    protocol.param_2 = param2 or 0
    protocol:EncodeAndSend()
end