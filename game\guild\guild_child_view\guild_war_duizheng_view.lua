GuildWarDuiZhengView = GuildWarDuiZhengView or BaseClass(SafeBaseView)

function GuildWarDuiZhengView:__init()
	self:SetMaskBg(true)
	local transform_cfg = {vector2 = Vector2(2, 10), sizeDelta = Vector2(1232, 586)}
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", transform_cfg)
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_duizhen")
end

function GuildWarDuiZhengView:__delete()
end

function GuildWarDuiZhengView:ReleaseCallBack()
	if not IsEmptyTable(self.duizheng_list) then
		for k,v in pairs(self.duizheng_list) do
			v:DeleteMe()
		end
		self.duizheng_list = {}
	end
end

function GuildWarDuiZhengView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.Guild.GuildDuiZhengTitle
	-- self:SetSecondView(nil,self.node_list["size"])
	self:InitGuildWarProgressList()

	local other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
	self.node_list["guild_state_text1"].text.text = Language.Guild.Round_1 .." ".. other_cfg.first_round
	self.node_list["guild_state_text2"].text.text = Language.Guild.Round_2 .." ".. other_cfg.second_round

end

function GuildWarDuiZhengView:ClickBtnTips()
end

function GuildWarDuiZhengView:OnFlush()
	self:SetGuildWarProgressList()
end

function GuildWarDuiZhengView:InitGuildWarProgressList()

	local parent_root = self.node_list["duizheng_list"].transform
	self.duizheng_list = {}
	local perfab_obj = self.node_list["zone_item"].gameObject
	for i = 1, 2 do
		local obj = ResMgr:Instantiate(perfab_obj)
		obj.transform:SetParent(parent_root, false)
		self.duizheng_list[i] = RanListItemRender.New(obj)
		self.duizheng_list[i]:SetIndex(i)
	end
	self:SetGuildWarProgressList()
end


function GuildWarDuiZhengView:SetGuildWarProgressList()
	if not self.duizheng_list then
		return
	end

	local list_data = GuildWGData.Instance:GetKfMatchTeamList() or {}
	local progress_list = self.duizheng_list
	local function check_has_info(data_list)
		for k=1,#data_list do
			if data_list[k].guild_id > 0 then
				return true
			end
		end
	end

	-- progress_list[i]:SetData(list_data[1])
	-- progress_list[i]:SetVisible(true)

	for i=1,#progress_list do
		if progress_list[i] then
			local data = list_data[i]
			progress_list[i]:SetData(data)
			-- if data and check_has_info(data) then
			-- 	progress_list[i]:SetData(data)
			-- 	progress_list[i]:SetVisible(true)
			-- else
			-- 	progress_list[i]:SetVisible(false)
			-- end
		end
	end
end

-----------------对战信息-------------------
RanListItemRender = RanListItemRender or BaseClass(BaseRender)
function RanListItemRender:__init()
	self.lunci_item1 = DuiZhengLunCiItem.New(self.node_list["lunci_item1"])
	self.lunci_item2 = DuiZhengLunCiItem.New(self.node_list["lunci_item2"])
	self.lunci_item1:SetIndex(1)
	self.lunci_item2:SetIndex(2)
end

function RanListItemRender:__delete()
	if self.lunci_item1 then
		self.lunci_item1:DeleteMe()
		self.lunci_item1 = nil
	end
	if self.lunci_item2 then
		self.lunci_item2:DeleteMe()
		self.lunci_item2 = nil
	end
end

function RanListItemRender:OnFlush()
	if not self.data then return end

	local function check_has_info(data_list)
		for k=1,#data_list do
			if data_list[k].guild_id > 0 then
				return true
			end
		end
	end
	local is_open = self.data and check_has_info(self.data)

	self.node_list["line_row"]:SetActive(not is_open)
	--self.node_list["line_row"]:SetActive(is_open)
	local index =  Language.Guild.CompetitionAreaIndex[self.index]
	self.node_list["text_no"].text.text = string.format(Language.Guild.CompetitionArea, index)

	self.lunci_item1:SetCurZone(self.index)
	self.lunci_item2:SetCurZone(self.index)
	self.lunci_item1:Flush()
	self.lunci_item2:Flush()
	self.lunci_item1:SetIsOpen(is_open)
	self.lunci_item2:SetIsOpen(is_open)
end


------------------对战轮次------------------
DuiZhengLunCiItem = DuiZhengLunCiItem or BaseClass(BaseRender)
function DuiZhengLunCiItem:__init()
	self.vs_item_list = {}
	for i=1,4 do
		local obj = {}
		obj.guild_name = self.node_list["lbl_guildname"..i]
		obj.win_img = self.node_list["win_img"..i]
		self.vs_item_list[i] = obj
	end
	self.cur_zone = 1
	self.is_open = false
end

function DuiZhengLunCiItem:__delete()

end

function DuiZhengLunCiItem:SetCurZone(zone)
	self.cur_zone = zone
end

function DuiZhengLunCiItem:SetIsOpen(flag)
	self.is_open = flag
end

function DuiZhengLunCiItem:OnFlush()
	local index = self.index
	local cur_state = GuildWGData.Instance:GetCurGuildWarState()
	local data = {}
	local show_win_list = {}
	local item_text = ""

	local activity_info =  ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
	local is_standy_open = false
	if activity_info then
		is_standy_open = activity_info.status == ACTIVITY_STATUS.STANDY or activity_info.status == ACTIVITY_STATUS.OPEN
	end
	if is_standy_open and self.is_open then
		if index == 1 then			--第一轮
			data = GuildWGData.Instance:GetKfGuildWarPKGameInfo(self.cur_zone)
			show_win_list = GuildWGData.Instance:GetGuildWarShowSecondWinData(self.cur_zone)
			item_text = Language.Guild.LunCiText1
		elseif index == 2 then		--第二轮
			if cur_state == GuildWGData.GuildWarState.XiuSaiState or cur_state == GuildWGData.GuildWarState.TwoState 
			or cur_state == GuildWGData.GuildWarState.EndState  then
				data = GuildWGData.Instance:GetKfGuildWarFinalGameInfo(self.cur_zone)
				show_win_list = GuildWGData.Instance:GetGuildWarShowChampionWinData(self.cur_zone)
			end
			item_text = Language.Guild.LunCiText2
		end
	end

	if not is_standy_open or not self.is_open then
		item_text = Language.Common.WeiKaiQi
	end

	local own_guild_id = RoleWGData.Instance:GetRoleVo().guild_id
	local own_plat_type = RoleWGData.Instance.role_vo.plat_type
	self.node_list["vs_item1"]:SetActive(not IsEmptyTable(data))
	self.node_list["vs_item2"]:SetActive(not IsEmptyTable(data))
	self.node_list["item_text"]:SetActive(IsEmptyTable(data))
	self.node_list["line_row"]:SetActive(not IsEmptyTable(data))
	self.node_list["item_text"].text.text = item_text
	local is_cross_server_stage = CrossServerWGData.Instance:GetIsEnterCrossSeverStage()
	if not IsEmptyTable(data) then
		for i=1,4 do
			local obj = self.vs_item_list[i]
			local show_win = show_win_list[i] or false
			local show_win_img = false
			local is_win = false
			if index == 1 then		
				is_win = GuildWGData.Instance:KfGuildWarIsSecondeWin(self.cur_zone,data[i].plat_type,data[i].guild_id)
			elseif index == 2 then
				is_win = GuildWGData.Instance:KfGuildWarIsChampionWin(self.cur_zone,data[i].plat_type,data[i].guild_id)
			end
			obj.win_img:SetActive(show_win)
			show_win_img = is_win and data[i].guild_id > 0
			local win_img_ab = show_win_img and "a2_xm_zb_sl" or "a2_xm_zb_sb"
			obj.win_img.image:LoadSprite(ResPath.GetGuildSystemImage(win_img_ab))

			if data[i] then
				local guild_name_color = data[i].guild_id == own_guild_id and data[i].plat_type == own_plat_type and COLOR3B.GREEN or "#ffffff"
				local server_str = ""
				if is_cross_server_stage then
					server_str = string.format(Language.Common.ServerIdFormat,data[i].server_id)
				end
				local is_all_nil = self:GetCurGuildIsAllNil(data,i)
				if is_all_nil then
					obj.guild_name.text.text = Language.KuafuGuildBattle.KfNoOccupy
				else
					local guild_name = ToColorStr(server_str..data[i].guild_name,guild_name_color)
					obj.guild_name.text.text = data[i].guild_name ~= "" and guild_name or Language.Guild.GuideWarProgressWuDuiShou
				end
			end
		end
	end

end

--判断这组对阵是否都轮空 两个轮空显示虚位以待
function DuiZhengLunCiItem:GetCurGuildIsAllNil(guild_datas,index)
	local other_index = index % 2 == 0 and index - 1 or index + 1
	local my_data = guild_datas[index]
	local other_data = guild_datas[other_index]
	if my_data and other_data and (my_data.guild_id > 0 or other_data.guild_id > 0) then
		return false
	end
	return true
end


