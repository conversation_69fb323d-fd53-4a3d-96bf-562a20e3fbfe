{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false, "hurtEffectFreeDelay": 0.0, "QualityCtrlList": []}, "actorTriggers": {"effects": [{"triggerEventName": "combo1_1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10011_skill", "effectAsset": {"BundleName": "effects/prefab/model/wuhun/10011/10011_skill_prefab", "AssetName": "10011_skill", "AssetGUID": "be72dfd045e880c4390698faeb69c130", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "combo1_1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10011_attack1", "effectAsset": {"BundleName": "effects/prefab/model/wuhun/10011/10011_attack1_prefab", "AssetName": "10011_attack1", "AssetGUID": "c2b08e4035fb65f41b414ca57a0fa768", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10011_attack2", "effectAsset": {"BundleName": "effects/prefab/model/wuhun/10011/10011_attack2_prefab", "AssetName": "10011_attack2", "AssetGUID": "4e4122de11cdb4149969d915e34b63b6", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": false}, {"triggerEventName": "rest/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "10011_rest", "effectAsset": {"BundleName": "effects/prefab/model/wuhun/10011/10011_rest_prefab", "AssetName": "10011_rest", "AssetGUID": "097b9302c3a1df7469ba5a53e1311fdf", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "root", "isAttach": true, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "rest", "playerAtPos": false, "ignoreParentScale": true}], "halts": [], "sounds": [{"soundEventName": "combo1_1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/wuhun_10011", "AssetName": "MingJiangattack1", "AssetGUID": "c808351fde4fb7d4a8e1b7782c44ac7d", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "combo1_1", "soundIsMainRole": false}, {"soundEventName": "attack1/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/wuhun_10011", "AssetName": "wuhun_10011attack1", "AssetGUID": "50aa428790404d642824c1fd40691c63", "IsEmpty": false}, "soundAudioGoName": "wuhun_10011attack1", "soundBtnName": "attack1", "soundIsMainRole": false}, {"soundEventName": "attack2/begin", "soundDelay": 0.0, "soundAudioAsset": {"BundleName": "audios/sfxs/roleskill/wuhun_10011", "AssetName": "MingJiangattack1", "AssetGUID": "046f3131666f04843a715df8c697ebd6", "IsEmpty": false}, "soundAudioGoName": "MingJiangattack1", "soundBtnName": "attack2", "soundIsMainRole": false}], "cameraShakes": [], "cameraFOVs": [], "sceneFades": [], "footsteps": []}, "actorBlinker": {"blinkFadeIn": 0.0, "blinkFadeHold": 0.0, "blinkFadeOut": 0.0}, "TimeLineList": []}