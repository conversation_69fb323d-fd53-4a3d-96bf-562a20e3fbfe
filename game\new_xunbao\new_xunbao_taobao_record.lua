XunBaoRecordView = XunBaoRecordView or BaseClass(SafeBaseView)

XunBaoRecordView.STATE = {
    ALL = 1,
    SELF = 2
}

function XunBaoRecordView:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, "uis/view/zhuangbeixunbao_ui_prefab", "layout_xunbao_record")
    self:SetMaskBg()
end

function XunBaoRecordView:ReleaseCallBack()
    if self.record_list then
        self.record_list:DeleteMe()
        self.record_list = nil
    end
end

function XunBaoRecordView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Xunbao.RecordTitle
    self:SetSecondView(nil, self.node_list["size"])

    self.node_list["btn_send"].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, XunBaoRecordView.STATE.ALL))
    self.node_list["btn_accept"].toggle:AddClickListener(BindTool.Bind(self.OnClickSwitch, self, XunBaoRecordView.STATE.SELF))

    self.record_list = AsyncListView.New(XunBaoRecordRender, self.node_list["role_list"])
end

function XunBaoRecordView:ShowIndexCallBack()
    self.node_list["btn_send"].toggle.isOn = false
    GlobalTimerQuest:AddDelayTimer(function()
        self.node_list["btn_send"].toggle.isOn = true
    end,0)
end

function XunBaoRecordView:OnFlush(param)
    for i, v in pairs(param) do
        if self.choose_state == i then
            local data
            if i == XunBaoRecordView.STATE.ALL then
                data = NewXunbaoWGData.Instance:GetTBLongServerRecordByLayer(self.layer)
            else
                data = NewXunbaoWGData.Instance:GetTbLongSelfRecordByLayer(self.layer)
            end
            self.node_list["no_invite"]:SetActive(IsEmptyTable(data))
            self.record_list:SetDataList(data, 3)
        end
    end
end

function XunBaoRecordView:OnClickSwitch(state)
    self.choose_state = state
    if state == XunBaoRecordView.STATE.ALL then
        NewXunbaoWGCtrl.Instance:SendTaoBaoOp(TAOBAO_OP_TYPE.RECORD, self.layer)
    else
        self:Flush(nil, XunBaoRecordView.STATE.SELF)
    end
end

function XunBaoRecordView:SetLayer(layer)
    self.layer = layer
end

-------------------------------------------------------------------------------------------------------
XunBaoRecordRender = XunBaoRecordRender or BaseClass(BaseRender)
function XunBaoRecordRender:OnFlush()
    local cfg = NewXunbaoWGData.Instance:GetTBLongInfoById(self.data.draw_reward_id, self.data.layer)
    local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.reward_item.item_id)
    local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
    local name = self.data.user_name or GameVoManager.Instance:GetMainRoleVo().name
    self.node_list["desc"].text.text = string.format(Language.Xunbao.TxtRecord, name, item_name, cfg.reward_item.num)

    self.node_list["img_root_bg"].image.enabled = (self.index % 2) == 1
end
