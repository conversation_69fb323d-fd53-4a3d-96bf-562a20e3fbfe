HundredEquipDropTimesRewardView = HundredEquipDropTimesRewardView or BaseClass(SafeBaseView)

local ITEM_HEIGHT = 96

function HundredEquipDropTimesRewardView:__init()
    self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(1078, 608)})
	self:AddViewResource(0, "uis/view/hundred_equip_ui_prefab", "hundred_equip_drop_times_reward_view")
end

function HundredEquipDropTimesRewardView:LoadCallBack()
	self.is_first_open = false
	self.node_list.title_view_name.text.text = Language.HundredEquip.RewardPreview
	self.drop_times_list = AsyncListView.New(HundredEquipDropTimesCell, self.node_list.drop_times_list)
	--self.drop_times_list:SetStartZeroIndex(true)
end

function HundredEquipDropTimesRewardView:ShowIndexCallBack()
    self.is_first_open = true
end

function HundredEquipDropTimesRewardView:ReleaseCallBack()
	if self.drop_times_list then
		self.drop_times_list:DeleteMe()
		self.drop_times_list = nil
	end
end

function HundredEquipDropTimesRewardView:OnFlush()
	local data = HundredEquipWGData.Instance:GetLevelCfg()
	local show_data = HundredEquipWGData.Instance:GetAddedDropTimesListShowCfg()
	self.drop_times_list:SetDataList(show_data)

	if self.is_first_open then
        self.is_first_open = false
		local level = HundredEquipWGData.Instance:GetLevelValue()
		local index = 0
		for k, v in pairs(show_data) do
			if v.level <= level then
				index = k - 1
			end
		end
        local target_height = ITEM_HEIGHT * (index)
        local node = self.node_list.drop_times_list:FindObj("Container")
        local list_height = self.node_list.drop_times_list.rect.rect.height
        local container_height = node.rect.rect.height
        local max_height = container_height - list_height
        if max_height > 0 then
            target_height = target_height <= max_height and target_height or max_height
        end

        local start_pos_y = node.rect.anchoredPosition.y
		ReDelayCall(self, function()
			if node then
				UITween.CleanAllMoveToShowPanel("HundredEquipDropTimesRewardView")
				UITween.MoveToShowPanel("HundredEquipDropTimesRewardView", node, Vector2(0, start_pos_y), Vector2(0, target_height), 0.1, DG.Tweening.Ease.Linear)
			end
		end, 0.1, "HundredEquipDropTimesRewardView")
    end
end

-------------------------------------------------------------------------------------
-- 爆率item
----------------------------------------------------------------------------------------------
HundredEquipDropTimesCell = HundredEquipDropTimesCell or BaseClass(BaseRender)

function HundredEquipDropTimesCell:__delete()
    if self.extra_reward_list then
		self.extra_reward_list:DeleteMe()
		self.extra_reward_list = nil
	end
end

function HundredEquipDropTimesCell:LoadCallBack()
    self.extra_reward_list = AsyncListView.New(HundredEquipDTRewardCell, self.node_list.extra_reward_list)
end

function HundredEquipDropTimesCell:OnFlush()
	local data = self.data
    self.extra_reward_list:SetDataList(data.reward_data)

	local level = HundredEquipWGData.Instance:GetLevelValue()
	local img_str = data.level <= level and "a3_xydt_d1" or "a3_xydt_d2"
	local color = data.level <= level and "#fff7bc" or "#a6d2eb"
	self.node_list["level"].text.text = string.format(Language.HundredEquip.DropTimesLevel, color, data.level)
	local bundle, asset = ResPath.GetHundredEquipImg(img_str)
	self.node_list["level_bg"].image:LoadSprite(bundle, asset, function()
		self.node_list["level_bg"].image:SetNativeSize()
	end)
end

-------------------------------------------------------------------------------------
-- 额外奖励item
----------------------------------------------------------------------------------------------
HundredEquipDTRewardCell = HundredEquipDTRewardCell or BaseClass(BaseRender)

function HundredEquipDTRewardCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function HundredEquipDTRewardCell:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function HundredEquipDTRewardCell:OnFlush()
    self.item_cell:SetData(self.data)
	local str = ""
	self.node_list.extra_label:SetActive(true)
	if self.data.drop_times and self.data.drop_times >= 100 then
		str = string.format(Language.HundredEquip.ExtraRewardDesc1, self.data.drop_times / 100)
	elseif self.data.drop_times and self.data.drop_times > 0 and self.data.drop_times < 100 then
		str = string.format(Language.HundredEquip.ExtraRewardDesc2, self.data.drop_times)
	else
		self.node_list.extra_label:SetActive(false)
	end

	self.node_list.extra_desc.text.text = str
end