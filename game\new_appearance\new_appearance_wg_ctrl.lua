require("game/new_appearance/new_appearance_enum")
require("game/new_appearance/new_appearance_wg_data")
require("game/new_appearance/new_appearance_advanced_wg_data")
require("game/new_appearance/new_appearance_qichong_wg_data")
require("game/new_appearance/new_appearance_colorful_wg_data")
require("game/new_appearance/new_appearance_render")
require("game/new_appearance/new_appearance_view")
require("game/new_appearance/new_appearance_advanced_wg_view")
require("game/new_appearance/new_appearance_upgrade_qichong_view")
require("game/new_appearance/new_appearance_fashion_view")
require("game/new_appearance/new_appearance_qichong_view")
require("game/mount_lingchong/huakun_bag_view")
require("game/mount_lingchong/mount_lingchong_display_box")
require("game/new_appearance/new_appearance_reslove_view")
require("game/new_appearance/new_appearance_hualing_wg_data")
require("game/new_appearance/new_appearance_act_view")
require("game/new_appearance/new_appreance_attr_store_view")
require("game/new_appearance/new_appearance_attr_store_wg_data")


NewAppearanceWGCtrl = NewAppearanceWGCtrl or BaseClass(BaseWGCtrl)
function NewAppearanceWGCtrl:__init()
	if NewAppearanceWGCtrl.Instance then
		error("[NewAppearanceWGCtrl]:Attempt to create singleton twice!")
	end

	NewAppearanceWGCtrl.Instance = self

    self.data = NewAppearanceWGData.New()
    self.view = NewAppearanceWGView.New(GuideModuleName.NewAppearanceWGView)
	self.huakun_bag_view = HuaKunBagView.New()
	self.display_box = MountLingChongDisplayBox.New(GuideModuleName.MountLingChongDisplayBox)
	self.reslove_view = NewAppearanceResloveView.New()
	self.attr_store_view = NewAppearanceAttrStoreView.New()
	self.colorful_wg_data = NewAppearanceColorfulWGData.New()

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function NewAppearanceWGCtrl:__delete()
    self:UnRegisterAllEvents()

    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.huakun_bag_view:DeleteMe()
	self.huakun_bag_view = nil

	self.display_box:DeleteMe()
	self.display_box = nil

	self.reslove_view:DeleteMe()
	self.reslove_view = nil

	self.attr_store_view:DeleteMe()
	self.attr_store_view = nil

	self.colorful_wg_data:DeleteMe()
	self.colorful_wg_data = nil
	
    NewAppearanceWGCtrl.Instance = nil
end

function NewAppearanceWGCtrl:RegisterAllProtocols()
	-- 进阶
	self:RegisterProtocol(SCUpgradeInfo, "OnUpgradeInfo")
	self:RegisterProtocol(SCRoleAppeChange, "OnRoleAppeChange")
	self:RegisterProtocol(CSUpgradeReq)

    -- 时装
	self:RegisterProtocol(CSShizhuangOperate)
	self:RegisterProtocol(SCShizhuangPartInfo, "OnSCShizhuangPartInfo")
	self:RegisterProtocol(SCShizhuangTimeItemInfo, "OnSCShizhuangTimeItemInfo")
	self:RegisterProtocol(SCShizhuangItemUpdate, "OnSCShizhuangItemUpdate")
	-- 坐骑
	self:RegisterProtocol(CSMountReq)
	self:RegisterProtocol(SCMountInfo, "OnMountInfo")
	self:RegisterProtocol(SCMountNewSkillActive, "OnMountNewSkillActive")
	-- 灵宠
	self:RegisterProtocol(CSLingChongReq)
	self:RegisterProtocol(SCLingChongInfo, "OnLingChongInfo")
	-- 仙鲲
	self:RegisterProtocol(CSKunOperaReq)
	self:RegisterProtocol(CSKunBreakDown)
	self:RegisterProtocol(SCKunAllInfo, "OnKunAllInfo")
	self:RegisterProtocol(SCKunChange, "OnKunChange")
	self:RegisterProtocol(SCKunUpgradeExp, "OnKunUpgradeExp")
	-- 回收
	self:RegisterProtocol(CSShizhuangDoMelt)
	self:RegisterProtocol(SCShizhuangMeltInfo, "OnSCShizhuangMeltInfo")

	--化灵
	self:RegisterProtocol(CSHualingOperate)
	self:RegisterProtocol(SCHualingInfo, "OnSCHualingInfo")
	self:RegisterProtocol(SCHuanlingUpdate, "OnSCHuanlingUpdate")

	-- --新属性丹
	self:RegisterProtocol(CSAttributeStoneOperate)
	self:RegisterProtocol(SCAttributeStoneInfo, "OnSCAttributeStoneInfo")
end

function NewAppearanceWGCtrl:RegisterAllEvents()
    -- 物品改变
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

    -- 角色属性改变
    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

    -- 天数改变
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnDayChange, self))

    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
end

-- 注销事件监听
function NewAppearanceWGCtrl:UnRegisterAllEvents()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	if self.open_fun_change then
        GlobalEventSystem:UnBind(self.open_fun_change)
        self.open_fun_change = nil
    end
end

-- 物品变化
function NewAppearanceWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    -- 物品数量增加
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		-- 珍稀骑宠，有些道具默认不显示，拥有或激活才显示
		local qc_type, _, qc_act_cfg = self.data:GetQiChongTypeByItemId(change_item_id)
		if qc_act_cfg then--and qc_act_cfg.is_default_show ~= 1 then
			self.data:ClearQiChongSpecialShowList(qc_type)
		end

		local advanced_change = self.data:CheckAdvancedStuffRemind(change_item_id)
		local fashion_change = self.data:CheckFashionStuffRemind(change_item_id)
		local qichong_change = self.data:CheckQiChongStuffRemind(change_item_id)
		local hualing_change = self.data:CheckHuaLingStuffRemind(change_item_id)
		if advanced_change or fashion_change or qichong_change or hualing_change then
			self:FlushView()
			ThunderManaWGCtrl.Instance:FlushAppearanceHaloView()
			ThunderManaWGCtrl.Instance:FlushAManaSelectView()
		end

		if advanced_change or qichong_change then
			if self.attr_store_view and self.attr_store_view:IsOpen() then
				self.attr_store_view:Flush()
			end
		end

		if self.data:GetKunStuffCfgByItemId(change_item_id) then
			if self.huakun_bag_view:IsOpen() then
				self.huakun_bag_view:Flush()
			end
		end

		if WardrobeWGData.Instance:GetStoneComposeByItemId(change_item_id) ~= nil or WardrobeWGData.Instance:GetIsStarOrGradeItem(change_item_id) then
			WardrobeWGCtrl.Instance:FlushCastingView(true)
			WardrobeWGCtrl.Instance:FlushCurShowView()
		end
	end
end

-- 角色属性改变
function NewAppearanceWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
	if attr_name == "level" then
		local flush_view = false
		if self.data:CheckFashionLevelIsFireRemind(old_value, value) then
			self.data:ClearFashionShowList()
			self:FireFashionRemind()
			flush_view = true
		end

		if self.data:CheckMountLevelIsFireRemind(old_value, value) then
			self.data:ClearQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
			RemindManager.Instance:Fire(RemindName.NewAppearance_Mount_Upstar)
			flush_view = true
		end

		if self.data:CheckLingChongLevelIsFireRemind(old_value, value) then
			self.data:ClearQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG)
			RemindManager.Instance:Fire(RemindName.NewAppearance_LingChong_Upstar)
			flush_view = true
		end

		if self.data:CheckKunLevelIsFireRemind(old_value, value) then
			self.data:ClearQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.KUN)
			RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_Upstar)
			flush_view = true
		end

		if flush_view then
			self:FlushView()
		end
	end
end

-- 天数改变
function NewAppearanceWGCtrl:OnDayChange()
    self.data:ClearFashionShowList()
	self.data:ClearQiChongSpecialShowList()
	self:FireFashionRemind()
	self:FlushView()
end

function NewAppearanceWGCtrl:OpenFunEventChange(check_all, fun_name)
    if check_all or fun_name == FunOpen.Instance:GetFunIsOpened(FunName.NewAppearanceLingChongUpstar)
	or FunOpen.Instance:GetFunIsOpened(FunName.NewAppearanceLingChongUpgrade)
	or fun_name == FunName.NewAppearanceUpgradeLingChong then
		self:FlushView(nil, "check_tabbar")
    end
end

function NewAppearanceWGCtrl:FlushView(index, key, param_t)
	if self.view:IsOpen() then
		self.view:Flush(index, key, param_t)
	end

	-- 刷新新衣橱
	WardrobeWGCtrl.Instance:FlushCurShowView(param_t, key)
	---升星后要通知定制套装更新
	CustomizedSuitWGCtrl.Instance:FlushView()
	ThunderManaWGCtrl.Instance:FlushAppearanceHaloView()
end

function NewAppearanceWGCtrl:FlushCheckUISceneShow()
	if self.view:IsOpen() then
		self.view:FlushCheckUISceneShow()
	end
end

--===========================================================
--==================    进阶    =============================
function NewAppearanceWGCtrl:OnUpgradeInfo(protocol)
	self.data:SetUpgradeInfo(protocol)

	self:FlushView()
	ViewManager.Instance:FlushView(GuideModuleName.MountLingChongDisplayBox)
	local rm = RemindManager.Instance
	rm:Fire(RemindName.NewAppearance_Upgrade_Wing)
	rm:Fire(RemindName.NewAppearance_Upgrade_FaBao)
	rm:Fire(RemindName.NewAppearance_Upgrade_ShenBing)
	rm:Fire(RemindName.NewAppearance_Upgrade_JianZhen)
end

function NewAppearanceWGCtrl:SendUpgradeReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSUpgradeReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or -1
	protocol.param2 = param2 or -1
	protocol.param3 = param3 or -1
	protocol:EncodeAndSend()
end

function NewAppearanceWGCtrl:OnRoleAppeChange(protocol)
	local obj = Scene.Instance:GetObjectByObjId(protocol.obj_id)
	if obj == nil or not obj:IsRole() then
		return
	end

	local obj_vo = obj:GetVo()
	if not obj_vo then
		return
	end

	local real_appe_change = true
	local appe_type = protocol.appe_type
	local appe_image_id = protocol.appe_image_id
	if appe_type == ROLE_APPE_TYPE.LINGCHONG then
		obj:SetAttr("lingchong_appeid", appe_image_id)
	elseif appe_type == ROLE_APPE_TYPE.MOUNT then
		local mount_appeid = protocol.param * COMMON_CONSTS.MOUNT_BOUNDARY + appe_image_id
		obj:SetAttr("mount_appeid", mount_appeid)

		MountWGCtrl.Instance:MountAppeChange(protocol)
		if appe_image_id > 0 and obj:IsMainRole() then
			obj:TryLeaveSit()
		end
	elseif appe_type == ROLE_APPE_TYPE.WING then
		obj_vo.appearance.wing_appeid = appe_image_id
		obj:SetAttr("wing_appeid", appe_image_id)
		obj:SetAttr("appearance", obj_vo.appearance)
	elseif appe_type == ROLE_APPE_TYPE.FABAO then
		obj_vo.appearance.fabao_appeid = appe_image_id
		obj:SetAttr("fabao_appeid", appe_image_id)
		obj:SetAttr("appearance", obj_vo.appearance)
	elseif appe_type == ROLE_APPE_TYPE.JIANZHEN then
		obj_vo.appearance.jianzhen_appeid = appe_image_id
		obj:SetAttr("jianzhen_appeid", appe_image_id)
		obj:SetAttr("appearance", obj_vo.appearance)
	elseif appe_type == ROLE_APPE_TYPE.SHENBING then
		obj_vo.appearance.fashion_wuqi = appe_image_id
        obj_vo.appearance.shenwu_appeid = appe_image_id
        obj_vo.shenwu_appeid = appe_image_id
        obj:SetAttr("appearance", obj_vo.appearance)
	elseif appe_type == ROLE_APPE_TYPE.BABY then
		obj:SetAttr("use_baby_id", appe_image_id)
	elseif appe_type == ROLE_APPE_TYPE.IMP then
		local cfg = EquipmentWGData.GetXiaoGuiCfgType(appe_image_id)
		if cfg then
			if obj:IsMainRole() then
				local old_id = GameVoManager.Instance:GetMainRoleVo().guard_id or 0
				if old_id > 0 and cfg.appe_image_id ~= old_id then
					SysMsgWGCtrl.Instance:ErrorRemind(Language.Bag.GuardHuanHuaSuccess)
				end
			end

			obj:SetAttr("guard_id", cfg.appe_image_id)
			if obj:IsMainRole() then
				RoleBagWGCtrl.Instance:Flush(TabIndex.rolebag_bag_all, "guard_change")
			end
		else
			obj:SetAttr("guard_id", -1)
		end

		RoleBagWGCtrl.Instance:FlushModel()
	elseif appe_type == ROLE_APPE_TYPE.LINGGONG then
		obj:SetAttr("soulboy_lg_id", appe_image_id)
	elseif appe_type == ROLE_APPE_TYPE.JINGJIE then
		obj:SetAttr("jingjie_level", appe_image_id)
		real_appe_change = false
	elseif appe_type == ROLE_APPE_TYPE.LINGQI then
		obj:SetAttr("soulboy_ls_id", appe_image_id)
	elseif appe_type == ROLE_APPE_TYPE.LINGYI then
		obj:SetAttr("lingtong_wing_id", appe_image_id)
	elseif appe_type == ROLE_APPE_TYPE.SHENQI_IMAGE then
		obj:SetAttr("tianshenshenqi_appeid",appe_image_id)
        obj:EquipDataChangeListen()
    elseif appe_type == ROLE_APPE_TYPE.XIANJIE_EQUIP_TIANSHU then
        if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss and not obj:IsMainRole() then
            local xianjie_boss_equip = {}
            xianjie_boss_equip.slot = protocol.special_param_tb.temp_low
            xianjie_boss_equip.page = protocol.special_param_tb.temp_high
            xianjie_boss_equip.active_tb = bit:d2b_two(protocol.param)
            obj_vo.xianjie_boss_equip = xianjie_boss_equip
            obj:SetAttr("xianjie_equip", xianjie_boss_equip)
        end

		real_appe_change = false
	elseif appe_type == ROLE_APPE_TYPE.WU_HUN_ZHEN_SHEN then
		obj:SetAttr("wuhun_zhen_shen", appe_image_id)
	elseif appe_type == ROLE_APPE_TYPE.BEAST then
		obj:SetAttr("beast_id", appe_image_id)
		obj:SetAttr("beast_skin", protocol.param)
	elseif appe_type == ROLE_APPE_TYPE.SHUANGSHENG_TIANSHEN then
		obj:SetAttr("shuangsheng_tianshen", appe_image_id)
	elseif appe_type == ROLE_APPE_TYPE.STAGELEVEL then
		obj:SetAttr("stage_level", protocol.param)
	elseif appe_type == ROLE_APPE_TYPE.GOD_OR_DEMON then
		local select_type = protocol.appe_image_id
		local level = protocol.special_param.temp_low
		local grade = protocol.special_param.temp_high
		obj:SetAttr("god_or_demon_type", select_type)
		obj:SetAttr("god_or_demon_level", level)
		obj:SetAttr("god_or_demon_grade", grade)
	elseif appe_type == ROLE_APPE_TYPE.WUHUNHUNZHEN then
		obj:SetAttr("wuhun_hunzhen", protocol.param)
	elseif appe_type == ROLE_APPE_TYPE.LONGZHU_SKILL_LEVEL then
		obj:SetAttr("longzhu_skill_level", protocol.param)
	end
	
	if real_appe_change then
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_APPERANCE_CHANGE, appe_type)
	end
end

--==================  进阶  end =============================
--===========================================================

--===========================================================
--==================    时装    =============================
--时装信息
function NewAppearanceWGCtrl:OnSCShizhuangPartInfo(protocol)
	-- print_error("ShizhuangInfo",protocol)
	self.data:SetFashionInfo(protocol)
	self:FlushView()

    -- LingZhiWGCtrl.Instance:FlushLingZhiView()
    self:FireFashionRemind()
	ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
	ViewManager.Instance:FlushView(GuideModuleName.NewHuanHuaFetterView)
end

--限时时装信息
function NewAppearanceWGCtrl:OnSCShizhuangTimeItemInfo(protocol)
	--print_error("TimeItemInfo=== ",protocol)
	self.data:SetFashionTimeItemInfo(protocol)
	self:FlushView()

	self:FireFashionRemind()
end

function NewAppearanceWGCtrl:OnSCShizhuangItemUpdate(protocol)
	--print_error("Update=====",protocol)
	self.data:SetSingleUpdateInfo(protocol)
	self:FlushView()

	self:FireFashionRemind()
	-- 光环放入了雷法
	ThunderManaWGCtrl.Instance:FlushAppearanceHaloView()
	ThunderManaWGCtrl.Instance:FlushAManaSelectView()
end

function NewAppearanceWGCtrl:FireFashionRemind()
	local rm = RemindManager.Instance
	-- 外观
    rm:Fire(RemindName.NewAppearance_WaiGuan_Body)
    rm:Fire(RemindName.NewAppearance_WaiGuan_Weapon)
    rm:Fire(RemindName.NewAppearance_WaiGuan_Halo)
    rm:Fire(RemindName.NewAppearance_WaiGuan_Wing)
    rm:Fire(RemindName.NewAppearance_WaiGuan_FaBao)
    rm:Fire(RemindName.NewAppearance_WaiGuan_ShenBing)
    rm:Fire(RemindName.NewAppearance_WaiGuan_JianZhen)
	-- 装扮
    rm:Fire(RemindName.NewAppearance_ZhuangBan_Mask)
    rm:Fire(RemindName.NewAppearance_ZhuangBan_Belt)
    rm:Fire(RemindName.NewAppearance_ZhuangBan_WeiBa)
    rm:Fire(RemindName.NewAppearance_ZhuangBan_ShouHuan)
    rm:Fire(RemindName.NewAppearance_ZhuangBan_Foot)
    rm:Fire(RemindName.NewAppearance_ZhuangBan_PhotoFrame)
    rm:Fire(RemindName.NewAppearance_ZhuangBan_Bubble)
end

function NewAppearanceWGCtrl:SendFashionOperate(operate, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShizhuangOperate)
	protocol.operate = operate
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 请求信息
function NewAppearanceWGCtrl:OnReqFashionInfo(part)
	self:SendFashionOperate(FASHION_OPERATE.REQ_INFO, part)
end

--使用请求
function NewAppearanceWGCtrl:OnUseFashion(part, index, level)
	self:SendFashionOperate(FASHION_OPERATE.USE, part, index, level)
end

-- 升级请求
function NewAppearanceWGCtrl:OnFashionUpLevel(part, index, id)
	self:SendFashionOperate(FASHION_OPERATE.UPLEVEL, part, index, id)
end

-- 购买时装
function NewAppearanceWGCtrl:OnFashionBuyPart(part, index, num)
	self:SendFashionOperate(FASHION_OPERATE.SHIZHUANG_BUY, part, index, num)
end

-- 使用方案
function NewAppearanceWGCtrl:OnFashionUseProject(project_id)
	self:SendFashionOperate(FASHION_OPERATE.SHIZHUANG_USE_PROJECT, project_id)
end

--==================  时装  end =============================
--===========================================================



--===========================================================
--==================    珍稀骑宠    ==========================
-- 坐骑信息
function NewAppearanceWGCtrl:OnMountInfo(protocol)
	local old_ride_state = self.data:GetMountRideState()
	if old_ride_state ~= protocol.mount_flag then
		MainuiWGCtrl.Instance:ChangeZuoQiState(protocol.mount_flag)
	end

	self.data:ClearQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.MOUNT)
	--print_error('坐骑信息-------------',protocol)

	self.data:SetMountInfo(protocol)
	-- 检测直升丹
	FunctionGuide.Instance:CheckZuoQiZhiShengDanKeyUse()
	AssignmentWGData.Instance:SetMyAssignFashionData(AssignmentWGData.FASHION_TYPE.MOUNT, protocol)

	self:FlushView()

	RemindManager.Instance:Fire(RemindName.NewAppearance_Upgrade_Mount)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Mount_Upstar)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Mount_Upgrade)
	ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)

	
	ViewManager.Instance:FlushView(GuideModuleName.NewHuanHuaFetterView)
end

-- 坐骑特殊技能激活 - 恭喜获得
function NewAppearanceWGCtrl:OnMountNewSkillActive(protocol)
	if protocol.param1 <= 0 then
		return
	end

	local mount_spec_skill_cfg = NewAppearanceWGData.Instance:GetSpecialMountSkillCfg(protocol.image_id, protocol.skill_id)
	if mount_spec_skill_cfg == nil then
		return
	end

	local data = {
		name = mount_spec_skill_cfg.skill_name,
		desc = mount_spec_skill_cfg.skill_describe,
		res_fun = ResPath.GetSkillIconById,
		icon = mount_spec_skill_cfg.skill_icon,
	}
	TipWGCtrl.Instance:ShowGetNewSkillView2(data)
end

-- 灵宠信息
function NewAppearanceWGCtrl:OnLingChongInfo(protocol)
	-- print_error("灵宠信息---", protocol)
	self.data:ClearQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG)
	self.data:SetLingChongInfo(protocol)
	AssignmentWGData.Instance:SetMyAssignFashionData(AssignmentWGData.FASHION_TYPE.PET, protocol)

	self:FlushView()

	RemindManager.Instance:Fire(RemindName.NewAppearance_Upgrade_LingChong)
	RemindManager.Instance:Fire(RemindName.NewAppearance_LingChong_Upstar)
	RemindManager.Instance:Fire(RemindName.NewAppearance_LingChong_Upgrade)
	ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
end

function NewAppearanceWGCtrl:SendMountReq(req_type, param1, param2)
	-- print_error('SendMountReq-----------',req_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSMountReq)
	protocol.req_type = req_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function NewAppearanceWGCtrl:SendLingChongReq(req_type, param1, param2)
	-- print_error('SendLingChongReq-----------',req_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSLingChongReq)
	protocol.req_type = req_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

-- 鲲操作
function NewAppearanceWGCtrl:SendKunOperaReq(opera_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSKunOperaReq)
	protocol.opera_type = opera_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 鲲分解材料
function NewAppearanceWGCtrl:SendKunBreakDown(count, bag_index)
	local protocol = ProtocolPool.Instance:GetProtocol(CSKunBreakDown)
	protocol.count = count or 0
	protocol.bag_index = bag_index or {}
	protocol:EncodeAndSend()
end

-- 鲲所有信息
function NewAppearanceWGCtrl:OnKunAllInfo(protocol)
	self.data:SetKunAllInfo(protocol)
	self:FlushView()
	RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_Upstar)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_Upgrade)
	ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
end

-- 鲲的信息发生改变
function NewAppearanceWGCtrl:OnKunChange(protocol)
	self.data:ClearQiChongSpecialShowList(MOUNT_LINGCHONG_APPE_TYPE.KUN)
	local id = protocol.info.id
	local info = self.data:GetKunInfo(id)
	self.data:SetKunChange(protocol)

	if info and  protocol.info.star > info.star then
		if self.view:IsOpen() then
			self.view:PlayUpStarEffect()
		end
	end

	if info and  protocol.info.level > info.level then
		if self.view:IsOpen() then
			self.view:PlayUpGradeEffect()
		end
	end

	self:FlushView()
	RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_Upstar)
	RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_Upgrade)
	ViewManager.Instance:FlushView(GuideModuleName.MultiFunctionView)
end

-- 鲲 - 进阶经验改变
function NewAppearanceWGCtrl:OnKunUpgradeExp(protocol)
	self.data:SetKunUpGradeExp(protocol.upgrade_exp)
	if self.huakun_bag_view:IsOpen() then
		self.huakun_bag_view:Flush()
	end

	self:FlushView()
	RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_Upgrade)
end

function NewAppearanceWGCtrl:OpenHuaKunBagView()
	if self.huakun_bag_view:IsOpen() then
		self.huakun_bag_view:Flush()
	else
		self.huakun_bag_view:Open()
	end
end

function NewAppearanceWGCtrl:StopUGQCAutoUpStar()
	if self.view and self.view:IsOpen() then
		self.view:StopUGQCAutoUpStar()
	end
end
--==================    珍稀骑宠  end  =======================
--===========================================================


function NewAppearanceWGCtrl:DisplayBoxOpen(show_data)
	if show_data == nil then
		return
	end
	
	self.display_box:SetData(show_data)
	self.display_box:Open()
end

function NewAppearanceWGCtrl:GetDisplayBox()
	return self.display_box
end

-- 外观回收信息
function NewAppearanceWGCtrl:OnSCShizhuangMeltInfo(protocol)
	-- print_error("-------外观回收信息--------", protocol)
	self.data:SetAppearanceResloveInfo(protocol)
	if self.reslove_view:IsOpen() then
		self.reslove_view:Flush()
	end
end

-- 外观回收操作
function NewAppearanceWGCtrl:SendAppearanceReslove(reslove_list)
	-- print_error("-------外观回收操作--------", reslove_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSShizhuangDoMelt)
	protocol.reslove_list = reslove_list or {}
	protocol:EncodeAndSend()
end

function NewAppearanceWGCtrl:OpenAppearanceResloveView()
	self.reslove_view:Open()
end

function NewAppearanceWGCtrl:OpenAppearanceAttrStoreView(advanced_type, qc_type)
	if self.attr_store_view then
		self.attr_store_view:SetData(advanced_type, qc_type)

		if not self.attr_store_view:IsOpen() then
			self.attr_store_view:Open()
		else
			self.attr_store_view:Flush()
		end
	end
end

------------------------------------------------化灵Start-----------------------------------------------
function NewAppearanceWGCtrl:SendHuaLingReq(operate_type, param1, param2, param3)
	-- print_error(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSHualingOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 后端骚操作 下发三次 一次发一种
function NewAppearanceWGCtrl:OnSCHualingInfo(protocol)
	-- print_error("化灵数据", protocol)
	self.data:SetHuaLingInfo(protocol)

	if protocol.type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		RemindManager.Instance:Fire(RemindName.NewAppearance_Mount_HuaLing)
	elseif protocol.type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
		RemindManager.Instance:Fire(RemindName.NewAppearance_LingChong_HuaLing)
	else
		RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_HuaLing)
	end
end

function NewAppearanceWGCtrl:OnSCHuanlingUpdate(protocol)
	-- print_error("化灵数据更新", protocol)
	self.data:UpdateHuaLingInfo(protocol)
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji, is_success = true, pos = Vector2(0, 0)})

	if protocol.type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
		RemindManager.Instance:Fire(RemindName.NewAppearance_Mount_HuaLing)
	elseif protocol.type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
		RemindManager.Instance:Fire(RemindName.NewAppearance_LingChong_HuaLing)
	else
		RemindManager.Instance:Fire(RemindName.NewAppearance_Kun_HuaLing)
	end

	self:FlushView()
end
-------------------------------------------------化灵End------------------------------------------------
------------------------------------------------新属性丹Start-----------------------------------------------
function NewAppearanceWGCtrl:SendAttributeStoneOperate(operate_type, param1, param2, param3)
	-- print_error(operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSAttributeStoneOperate)
	protocol.operate_type = operate_type
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function NewAppearanceWGCtrl:OnSCAttributeStoneInfo(protocol)
	self.data:UpdateAttrStoreInfo(protocol)

    local rm = RemindManager.Instance
	rm:Fire(RemindName.NewAppearance_Upgrade_Wing)
	rm:Fire(RemindName.NewAppearance_Upgrade_FaBao)
	rm:Fire(RemindName.NewAppearance_Upgrade_ShenBing)
	rm:Fire(RemindName.NewAppearance_Upgrade_JianZhen)
    rm:Fire(RemindName.NewAppearance_Upgrade_LingChong)
    rm:Fire(RemindName.NewAppearance_Upgrade_Mount)

	self:FlushView()

	if self.attr_store_view and self.attr_store_view:IsOpen() then
		self.attr_store_view:Flush()
	end
end

function NewAppearanceWGCtrl:OnClickAttributeStoneItem(data, is_quick)
	if IsEmptyTable(data) then
        return
    end

    local cfg = data.cfg
    if data.is_remind then
        if data.ad_type then
            NewAppearanceWGCtrl.Instance:SendAttributeStoneOperate(ATTRIBUTE_STONE_OPERATE_TYPE.UPGRADE_STONE, data.ad_type, cfg.slot_idx, data.had_num)
        elseif data.qc_type then
            if data.qc_type == MOUNT_LINGCHONG_APPE_TYPE.MOUNT then
                NewAppearanceWGCtrl.Instance:SendAttributeStoneOperate(ATTRIBUTE_STONE_OPERATE_TYPE.MOUNT_STONE, cfg.slot_idx, data.had_num)
            elseif data.qc_type == MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG then
                NewAppearanceWGCtrl.Instance:SendAttributeStoneOperate(ATTRIBUTE_STONE_OPERATE_TYPE.PET_STONE, cfg.slot_idx, data.had_num)
            end
        end
    else
		if not is_quick then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cfg.shuxingdan_id})
		end
    end

end
------------------------------------------------新属性丹end-----------------------------------------------