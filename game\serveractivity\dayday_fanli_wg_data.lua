DayDayFanLiData = DayDayFanLiData or BaseClass()
function DayDayFanLiData:__init()
	if DayDayFanLiData.Instance then
		ErrorLog("[DayDayFanLiData] Attemp to create a singleton twice !")
	end
	DayDayFanLiData.Instance = self

	-- 初始化配置表
	--self:GetDayDayFanLiCfg()

	self:RegisterRemind()
end

function DayDayFanLiData:__delete()
	--RemindManager.Instance:UnRegister(RemindName.DayDayFanLi)
	self.cur_reward_level = nil
	DayDayFanLiData.Instance = nil	
end

function DayDayFanLiData:RegisterRemind()
	--RemindManager.Instance:Register(RemindName.DayDayFanLi, BindTool.Bind(self.GetDayDayFanLiRemind, self))
end

function DayDayFanLiData:GetDayDayFanLiRemind()
	local list = self:GetLingQuList()
	for k,v in pairs(list) do
		if v.flag == 3 then
			return 1
		end
	end
	return 0
end

--获取天天返利配置
function DayDayFanLiData:GetDayDayFanLiCfg()
	--local actzhuanfutype = ServerActivityWGData.Instance:GetRandActZhuanFuType()
	--if self.actzhuanfutype ~= actzhuanfutype then
	--	self.actzhuanfutype = actzhuanfutype
	--	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig().chongzhi_day_reward --天天返利
	--	self.chongzhi_day_reward_cfg = ListToMap(cfg, "code")
	--end
	--return self.chongzhi_day_reward_cfg
end

function DayDayFanLiData:GetDayDayFanLiShowCfg(  )
	--return ServerActivityWGData.Instance:GetCurrentRandActivityConfig().chongzhi_day_reward_show --天天返利展示
end

--获取天天返利领取列表
function DayDayFanLiData:GetDayDayFanLiLingQuList( level )
	local level = level or RoleWGData.Instance:GetRoleVo().level
	local cfg = self:GetDayDayFanLiCfg()
	local level_1 = cfg[1].level
	local level_2 = cfg[#cfg].level
	local level_seq = level_1
	if level >= level_2 then
		level_seq = level_2
	end
	if self.cur_reward_level == level_seq then
		return self.lingqu_list
	end
	self.cur_reward_level = level_seq
	self.lingqu_list = {}
	for i,v in ipairs(cfg) do
		if v.level == level_seq then
			table.insert(self.lingqu_list, v)
		end
	end
	return self.lingqu_list
end

function DayDayFanLiData:GetDayDayFanLiRewardList( level )
	local lingqu_list = self:GetDayDayFanLiLingQuList(level)
	local reward_list = {}
	local add_count, col_count = 0, 5  --每行最多显示5个
	local index = 1
	for k,v in ipairs(lingqu_list) do
		if not IsEmptyTable(v.rare_reward_item) then
			if nil == reward_list[index] then
				reward_list[index] = {}
			end
			add_count = add_count + 1
			table.insert(reward_list[index], v.rare_reward_item[0])
			if add_count == col_count then
				add_count = 0
				index = index + 1
			end
		end
	end
	return reward_list
end

function DayDayFanLiData:SaveDayDayInfo( protocol )
	self.dayday_fanli_total_day = protocol.total_day
	self.fetch_flag = protocol.fetch_flag
end

function DayDayFanLiData:GetModelShowCfg()
	local level = RoleWGData.Instance:GetRoleVo().level
	local cfg = self:GetDayDayFanLiShowCfg()
	local level_1 = cfg[1].level
	local level_2 = cfg[#cfg].level
	local level_seq = level_1
	if level >= level_2 then
		level_seq = level_2
	end
	for i,v in ipairs(cfg) do
		if v.level == level_seq then
			return v
		end
	end
end

function DayDayFanLiData:GetDayDayFanLiTotalDay()
	return self.dayday_fanli_total_day or 0
end

function DayDayFanLiData:GetDayDayFAnLiInfo()
	-- if nil == self.fetch_flag then
	-- 	self.fetch_flag = {}
	-- 	for i = 1, 30 do
	-- 		self.fetch_flag[i] = 0
	-- 		if i == 1 or i == 2 or i == 3 then
	-- 			self.fetch_flag[i] = 1
	-- 		elseif i == 4 then
	-- 			self.fetch_flag[i] = 2
	-- 		end
	-- 	end
	-- end
	return self.fetch_flag
end

function DayDayFanLiData:GetLingQuList()
	local level = RoleWGData.Instance:GetRoleVo().level
	local chongzhi_day_reward_cfg = self:GetDayDayFanLiCfg()
	local len = #chongzhi_day_reward_cfg
	local level_1 = chongzhi_day_reward_cfg[1].level
	local level_2 = chongzhi_day_reward_cfg[len].level
	local level_seq = level_1
	local total_day = self:GetDayDayFanLiTotalDay() --一共充值了几天
	local model_show = self:GetModelShowCfg()

	local dangci = tonumber(model_show.day) 	 --两种档次的奖励 每种档次需要多少天才能领取
	if level >= level_2 then --筛选档次
		level_seq = level_2
	end
	local lingqu_code_list = {}
	local dazzle_gold = 0  --累计返利元宝

	local function func(code, level_idx, flag )
		local cfg = nil
		if level_idx == level_1 then
			cfg = chongzhi_day_reward_cfg[code]
		else
			cfg = chongzhi_day_reward_cfg[code + dangci] --第二档次
		end
		local temp = {}
		--模拟 table coppy
		temp.index = cfg.index
		temp.level = cfg.level
		temp.need_chongzhi_day = cfg.need_chongzhi_day
		temp.need_gold = cfg.need_gold
		temp.reward_item = cfg.reward_item
		temp.rare_reward_item = cfg.rare_reward_item
		temp.reward_bind_gold = cfg.reward_bind_gold
		temp.reward_gold = cfg.reward_gold
		temp.dazzle_gold = cfg.dazzle_gold
		temp.code = cfg.code
		temp.flag = flag --3表示可领取
		table.insert(lingqu_code_list, temp)
		if flag > 0 then
			dazzle_gold = dazzle_gold + cfg.dazzle_gold
		elseif total_day >= cfg.need_chongzhi_day then
			temp.flag = 3
		end
	end
	local fetch_flag = self:GetDayDayFAnLiInfo()
	for i,v in ipairs(fetch_flag) do
		if v == 0 then
			func(i, level_seq, v) --没有领取，展示当前等级对应的档次
		elseif v == 1 then
			func(i, level_1, v) --已经领取档次1的奖励
		elseif v == 2 then
			func(i, level_2, v) --已经领取档次二的奖励
		end
	end
	table.sort(lingqu_code_list, function(a,b)
		local order_a = 10000
		local order_b = 10000

		if a.flag == 1 or a.flag == 2 then
			order_a = order_a + 1000
		end
		if b.flag == 1 or b.flag == 2 then
			order_b = order_b + 1000
		end

		if a.index < b.index then
			order_b = order_b + 100
		else
			order_a = order_a + 100
		end
		return order_a < order_b 
	end)
	return lingqu_code_list, dazzle_gold
end