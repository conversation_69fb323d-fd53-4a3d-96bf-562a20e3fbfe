-- K-跨服空战.xls
local item_table={
[1]={item_id=63000,num=4,is_bind=1},
[2]={item_id=26503,num=1,is_bind=1},
[3]={item_id=26518,num=1,is_bind=1},
[4]={item_id=40212,num=4,is_bind=1},
[5]={item_id=26415,num=4,is_bind=1},
[6]={item_id=26503,num=4,is_bind=1},
[7]={item_id=63000,num=3,is_bind=1},
[8]={item_id=40212,num=3,is_bind=1},
[9]={item_id=26502,num=3,is_bind=1},
[10]={item_id=63000,num=2,is_bind=1},
[11]={item_id=26502,num=1,is_bind=1},
[12]={item_id=26517,num=1,is_bind=1},
[13]={item_id=40212,num=2,is_bind=1},
[14]={item_id=26415,num=3,is_bind=1},
[15]={item_id=26501,num=2,is_bind=1},
[16]={item_id=26415,num=2,is_bind=1},
[17]={item_id=26500,num=1,is_bind=1},
[18]={item_id=26509,num=10,is_bind=1},
[19]={item_id=26505,num=6,is_bind=1},
[20]={item_id=26519,num=5,is_bind=1},
[21]={item_id=26518,num=4,is_bind=1},
[22]={item_id=26517,num=3,is_bind=1},
[23]={item_id=26516,num=2,is_bind=1},
[24]={item_id=26515,num=1,is_bind=1},
[25]={item_id=26524,num=10,is_bind=1},
[26]={item_id=26523,num=9,is_bind=1},
[27]={item_id=26522,num=8,is_bind=1},
[28]={item_id=26521,num=7,is_bind=1},
[29]={item_id=26520,num=6,is_bind=1},
[30]={item_id=26508,num=9,is_bind=1},
[31]={item_id=26507,num=8,is_bind=1},
[32]={item_id=26506,num=7,is_bind=1},
[33]={item_id=30807,num=1,is_bind=1},
[34]={item_id=30805,num=1,is_bind=1},
[35]={item_id=26191,num=1,is_bind=1},
[36]={item_id=26193,num=1,is_bind=1},
[37]={item_id=30778,num=1,is_bind=1},
[38]={item_id=30779,num=1,is_bind=1},
[39]={item_id=30780,num=1,is_bind=1},
[40]={item_id=48071,num=1,is_bind=1},
[41]={item_id=48071,num=2,is_bind=1},
[42]={item_id=26520,num=1,is_bind=1},
[43]={item_id=26519,num=2,is_bind=1},
[44]={item_id=26505,num=1,is_bind=1},
[45]={item_id=26504,num=1,is_bind=1},
[46]={item_id=39988,num=1,is_bind=1},
[47]={item_id=39159,num=1,is_bind=1},
[48]={item_id=40212,num=1,is_bind=1},
[49]={item_id=26369,num=1,is_bind=1},
[50]={item_id=26363,num=1,is_bind=1},
[51]={item_id=26380,num=1,is_bind=1},
[52]={item_id=40215,num=1,is_bind=1},
[53]={item_id=30447,num=10,is_bind=1},
[54]={item_id=40211,num=1,is_bind=1},
[55]={item_id=30447,num=1,is_bind=1},
[56]={item_id=22532,num=1,is_bind=1},
[57]={item_id=30443,num=1,is_bind=1},
[58]={item_id=26200,num=2,is_bind=1},
[59]={item_id=26203,num=2,is_bind=1},
[60]={item_id=30447,num=5,is_bind=1},
[61]={item_id=63004,num=1,is_bind=1},
[62]={item_id=26345,num=5,is_bind=1},
[63]={item_id=26344,num=10,is_bind=1},
[64]={item_id=26367,num=1,is_bind=1},
[65]={item_id=26345,num=4,is_bind=1},
[66]={item_id=26344,num=8,is_bind=1},
[67]={item_id=26368,num=1,is_bind=1},
[68]={item_id=26345,num=3,is_bind=1},
[69]={item_id=26344,num=6,is_bind=1},
[70]={item_id=26378,num=1,is_bind=1},
[71]={item_id=26379,num=1,is_bind=1},
[72]={item_id=26361,num=1,is_bind=1},
[73]={item_id=26362,num=1,is_bind=1},
[74]={item_id=57833,num=1,is_bind=1},
[75]={item_id=57832,num=1,is_bind=1},
[76]={item_id=40043,num=1,is_bind=1},
[77]={item_id=22576,num=1,is_bind=1},
[78]={item_id=40044,num=1,is_bind=1},
[79]={item_id=40045,num=1,is_bind=1},
[80]={item_id=63000,num=1,is_bind=1},
[81]={item_id=26519,num=1,is_bind=1},
[82]={item_id=57834,num=1,is_bind=1},
[83]={item_id=26415,num=1,is_bind=1},
[84]={item_id=63000,num=6,is_bind=1},
[85]={item_id=40212,num=5,is_bind=1},
[86]={item_id=26415,num=5,is_bind=1},
[87]={item_id=26504,num=5,is_bind=1},
[88]={item_id=40042,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
stage={
{},
{stage=2,},
{stage=3,air_wall_id=2,air_wall="182##504##60##20",},
{stage=4,air_wall_id=3,air_wall="181##673##60##20",},
{stage=5,air_wall_id=4,air_wall="182##806##60##20",},
{stage=6,air_wall_id=5,air_wall="",}
},

stage_meta_table_map={
},
monster={
[0]={seq=0,monster_num=4,kill_add_bs_times=0,refrensh_time=0,is_boss=0,stage_status=3,},
[1]={seq=1,stage=2,show_stage=2,monster_id=3400,monster_pos="187,146",refrensh_time=9,vie_score=100,stage_name="暴动大圣",target_desc="暗黑大圣",detail_desc="大圣在丹炉受困太久，吸入了过量丹气导致神志不清，请各位修士唤醒大圣",monster_head="a3_boos_head_8018",stage_status=3,stage_dec="击败被魔气侵袭的大圣！",},
[2]={seq=2,stage=3,show_stage=3,monster_id=3401,monster_pos="196,424",vie_score=100,stage_name="大战龙台",target_desc="百里金龙",detail_desc="随大圣一起前往祖龙台，击败百里金龙。要小心金龙的神雷术法，中招者会陷入长久的眩晕",monster_head="a3_boos_head_8019",stage_dec="百里金龙出现在了祖龙台，快去击败他！",},
[3]={seq=3,stage=4,show_stage=4,monster_id=3403,monster_pos="201,660",vie_score=100,stage_name="深入天宫",target_desc="巨灵神",detail_desc="穿过祖龙台到达天宫区域的深处，穿过风暴区就可以看到真正的天宫了",monster_head="a3_boos_head_8011",stage_dec="金龙陨落天庭震怒，即将派出巨灵神前来讨伐！",},
[4]={seq=4,stage=5,show_stage=5,monster_id=3414,monster_pos="200,790",stage_name="神君降临",target_desc="晦月神君",detail_desc="晦月神君出现在了天宫的入口，他是除天帝外的天宫最强者，去击败他吧",monster_head="a3_boos_head_8029",stage_dec="晦月神君出现在了天宫入口，击败他进入天宫吧！",},
[5]={seq=5,stage=6,show_stage=6,monster_id=3405,monster_pos="200,864",stage_name="天宫之主",target_desc="帝君",detail_desc="天宫之主降临。小心！他拥有灭世之力，努力击败他，就可以获取天宫的机缘了",monster_head="a3_boos_head_8030",stage_dec="天宫主人从闭关中苏醒，小心！",}
},

monster_meta_table_map={
},
hurt_cap={
{},
{index=1,min_world_level=301,max_world_level=400,},
{index=2,min_world_level=401,max_world_level=500,},
{index=3,min_world_level=501,max_world_level=550,},
{index=4,min_world_level=551,max_world_level=600,},
{index=5,min_world_level=601,max_world_level=650,},
{index=6,min_world_level=651,max_world_level=700,},
{index=7,min_world_level=701,max_world_level=750,},
{index=8,min_world_level=751,max_world_level=800,},
{index=9,min_world_level=801,max_world_level=850,},
{index=10,min_world_level=851,max_world_level=900,},
{index=11,min_world_level=901,max_world_level=950,},
{index=12,min_world_level=951,max_world_level=1000,},
{index=13,min_world_level=1001,max_world_level=1050,},
{index=14,min_world_level=1051,max_world_level=1100,},
{index=15,min_world_level=1101,max_world_level=1150,},
{index=16,min_world_level=1151,max_world_level=1200,},
{index=17,min_world_level=1201,max_world_level=99999,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=1,drop_id=14021,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=2,drop_id=14022,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=3,drop_id=14034,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=4,drop_id=14023,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=5,drop_id=14024,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=6,drop_id=14035,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=7,drop_id=14025,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=8,drop_id=14036,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,},
{monster_seq=9,drop_id=14026,}
},

hurt_cap_meta_table_map={
[125]=17,	-- depth:1
[126]=18,	-- depth:1
[128]=2,	-- depth:1
[136]=10,	-- depth:1
[130]=4,	-- depth:1
[131]=5,	-- depth:1
[132]=6,	-- depth:1
[133]=7,	-- depth:1
[134]=8,	-- depth:1
[135]=9,	-- depth:1
[124]=16,	-- depth:1
[129]=3,	-- depth:1
[123]=15,	-- depth:1
[113]=131,	-- depth:2
[121]=13,	-- depth:1
[104]=14,	-- depth:1
[105]=123,	-- depth:2
[106]=124,	-- depth:2
[107]=125,	-- depth:2
[108]=126,	-- depth:2
[110]=128,	-- depth:2
[111]=129,	-- depth:2
[122]=104,	-- depth:2
[112]=130,	-- depth:2
[114]=132,	-- depth:2
[115]=133,	-- depth:2
[116]=134,	-- depth:2
[117]=135,	-- depth:2
[118]=136,	-- depth:2
[119]=11,	-- depth:1
[120]=12,	-- depth:1
[137]=119,	-- depth:2
[138]=120,	-- depth:2
[149]=131,	-- depth:2
[140]=104,	-- depth:2
[162]=126,	-- depth:2
[164]=128,	-- depth:2
[165]=129,	-- depth:2
[166]=130,	-- depth:2
[167]=131,	-- depth:2
[168]=132,	-- depth:2
[169]=133,	-- depth:2
[161]=125,	-- depth:2
[170]=134,	-- depth:2
[172]=136,	-- depth:2
[173]=119,	-- depth:2
[174]=120,	-- depth:2
[175]=121,	-- depth:2
[176]=104,	-- depth:2
[177]=123,	-- depth:2
[178]=124,	-- depth:2
[171]=135,	-- depth:2
[139]=121,	-- depth:2
[160]=124,	-- depth:2
[158]=104,	-- depth:2
[141]=123,	-- depth:2
[142]=124,	-- depth:2
[143]=125,	-- depth:2
[144]=126,	-- depth:2
[146]=128,	-- depth:2
[147]=129,	-- depth:2
[148]=130,	-- depth:2
[159]=123,	-- depth:2
[103]=121,	-- depth:2
[151]=133,	-- depth:2
[152]=134,	-- depth:2
[153]=135,	-- depth:2
[154]=136,	-- depth:2
[155]=119,	-- depth:2
[156]=120,	-- depth:2
[157]=121,	-- depth:2
[150]=132,	-- depth:2
[102]=120,	-- depth:2
[90]=126,	-- depth:2
[100]=136,	-- depth:2
[41]=131,	-- depth:2
[42]=132,	-- depth:2
[43]=133,	-- depth:2
[44]=134,	-- depth:2
[45]=135,	-- depth:2
[46]=136,	-- depth:2
[47]=119,	-- depth:2
[40]=130,	-- depth:2
[48]=120,	-- depth:2
[50]=104,	-- depth:2
[51]=123,	-- depth:2
[52]=124,	-- depth:2
[53]=125,	-- depth:2
[54]=126,	-- depth:2
[56]=128,	-- depth:2
[57]=129,	-- depth:2
[49]=121,	-- depth:2
[58]=130,	-- depth:2
[39]=129,	-- depth:2
[36]=126,	-- depth:2
[20]=128,	-- depth:2
[21]=129,	-- depth:2
[22]=130,	-- depth:2
[23]=131,	-- depth:2
[24]=132,	-- depth:2
[25]=133,	-- depth:2
[26]=134,	-- depth:2
[38]=128,	-- depth:2
[27]=135,	-- depth:2
[29]=119,	-- depth:2
[30]=120,	-- depth:2
[31]=121,	-- depth:2
[32]=104,	-- depth:2
[33]=123,	-- depth:2
[34]=124,	-- depth:2
[35]=125,	-- depth:2
[28]=136,	-- depth:2
[101]=119,	-- depth:2
[59]=131,	-- depth:2
[61]=133,	-- depth:2
[83]=119,	-- depth:2
[84]=120,	-- depth:2
[85]=121,	-- depth:2
[86]=104,	-- depth:2
[87]=123,	-- depth:2
[88]=124,	-- depth:2
[89]=125,	-- depth:2
[82]=136,	-- depth:2
[179]=125,	-- depth:2
[93]=129,	-- depth:2
[94]=130,	-- depth:2
[95]=131,	-- depth:2
[96]=132,	-- depth:2
[97]=133,	-- depth:2
[98]=134,	-- depth:2
[99]=135,	-- depth:2
[92]=128,	-- depth:2
[60]=132,	-- depth:2
[81]=135,	-- depth:2
[79]=133,	-- depth:2
[62]=134,	-- depth:2
[63]=135,	-- depth:2
[64]=136,	-- depth:2
[65]=119,	-- depth:2
[66]=120,	-- depth:2
[67]=121,	-- depth:2
[68]=104,	-- depth:2
[80]=134,	-- depth:2
[69]=123,	-- depth:2
[71]=125,	-- depth:2
[72]=126,	-- depth:2
[74]=128,	-- depth:2
[75]=129,	-- depth:2
[76]=130,	-- depth:2
[77]=131,	-- depth:2
[78]=132,	-- depth:2
[70]=124,	-- depth:2
[180]=126,	-- depth:2
},
rank_reward={
{},
{min_rank=2,max_rank=2,add_score=180,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},reward_show_item={[0]=item_table[6]},},
{min_rank=3,max_rank=3,add_score=150,reward_item={[0]=item_table[7],[1]=item_table[2],[2]=item_table[3],[3]=item_table[8],[4]=item_table[5]},reward_show_item={[0]=item_table[9]},},
{min_rank=4,max_rank=10,add_score=100,reward_item={[0]=item_table[10],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13],[4]=item_table[14]},reward_show_item={[0]=item_table[15]},},
{min_rank=11,max_rank=50,add_score=50,reward_item={[0]=item_table[10],[1]=item_table[11],[2]=item_table[12],[3]=item_table[13],[4]=item_table[16]},reward_show_item={[0]=item_table[17]},},
{monster_seq=2,reward_show_item={[0]=item_table[18]},},
{monster_seq=2,},
{monster_seq=2,},
{monster_seq=2,},
{monster_seq=2,reward_show_item={[0]=item_table[19]},},
{monster_seq=3,reward_show_item={[0]=item_table[20]},},
{monster_seq=3,reward_show_item={[0]=item_table[21]},},
{monster_seq=3,reward_show_item={[0]=item_table[22]},},
{monster_seq=3,reward_show_item={[0]=item_table[23]},},
{monster_seq=3,reward_show_item={[0]=item_table[24]},},
{monster_seq=4,reward_show_item={[0]=item_table[25]},},
{monster_seq=4,reward_show_item={[0]=item_table[26]},},
{monster_seq=4,reward_show_item={[0]=item_table[27]},},
{monster_seq=4,reward_show_item={[0]=item_table[28]},},
{monster_seq=4,reward_show_item={[0]=item_table[29]},},
{monster_seq=5,},
{monster_seq=5,},
{monster_seq=5,},
{monster_seq=5,},
{monster_seq=5,},
{monster_seq=6,},
{monster_seq=6,reward_show_item={[0]=item_table[30]},},
{monster_seq=6,reward_show_item={[0]=item_table[31]},},
{monster_seq=6,reward_show_item={[0]=item_table[32]},},
{monster_seq=6,}
},

rank_reward_meta_table_map={
[26]=6,	-- depth:1
[28]=3,	-- depth:1
[27]=2,	-- depth:1
[20]=5,	-- depth:1
[25]=5,	-- depth:1
[24]=4,	-- depth:1
[23]=3,	-- depth:1
[22]=2,	-- depth:1
[19]=4,	-- depth:1
[15]=5,	-- depth:1
[17]=2,	-- depth:1
[29]=4,	-- depth:1
[14]=4,	-- depth:1
[13]=3,	-- depth:1
[12]=2,	-- depth:1
[10]=5,	-- depth:1
[9]=29,	-- depth:2
[8]=28,	-- depth:2
[7]=27,	-- depth:2
[18]=3,	-- depth:1
[30]=10,	-- depth:2
},
auction_grade={
{},
{}
},

auction_grade_meta_table_map={
},
auction={
{},
{seq=1,weight="18,1000|0,1000|21,1000",},
{seq=2,weight="19,1000|10,1000|3,1000",},
{round=1,show_stage=2,weight="20,1000|9,1000|5,1000",},
{seq=1,weight="17,1000|1,1000|21,1000",},
{seq=2,weight="18,1000|11,1000|3,1000",},
{round=2,show_stage=3,weight="19,1000|8,1000|6,1000",},
{seq=1,weight="20,1000|2,1000|21,1000",},
{seq=2,weight="17,1000|12,1000|3,1000",},
{round=3,show_stage=4,weight="18,1000|9,1000|6,1000",},
{seq=1,weight="19,1000|0,1000|21,1000",},
{seq=2,weight="20,1000|13,1000|4,1000",},
{round=4,show_stage=5,weight="17,1000|8,1000|7,1000",},
{seq=1,weight="18,1000|1,1000|21,1000",},
{seq=2,weight="19,1000|10,1000|4,1000",},
{round=5,show_stage=6,weight="20,1000|9,1000|7,1000",},
{seq=1,weight="17,1000|2,1000|21,1000",},
{seq=2,weight="18,1000|12,1000|4,1000",},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,},
{grade=2,}
},

auction_meta_table_map={
[21]=3,	-- depth:1
[20]=2,	-- depth:1
[34]=16,	-- depth:1
[31]=13,	-- depth:1
[28]=10,	-- depth:1
[25]=7,	-- depth:1
[22]=4,	-- depth:1
[18]=16,	-- depth:1
[15]=13,	-- depth:1
[14]=13,	-- depth:1
[12]=10,	-- depth:1
[11]=10,	-- depth:1
[5]=4,	-- depth:1
[9]=7,	-- depth:1
[17]=16,	-- depth:1
[8]=7,	-- depth:1
[6]=4,	-- depth:1
[35]=17,	-- depth:2
[33]=15,	-- depth:2
[32]=14,	-- depth:2
[27]=9,	-- depth:2
[29]=11,	-- depth:2
[26]=8,	-- depth:2
[24]=6,	-- depth:2
[23]=5,	-- depth:2
[30]=12,	-- depth:2
[36]=18,	-- depth:2
},
auction_item={
[0]={seq=0,rarity=3,},
[1]={seq=1,auction_item=item_table[33],},
[2]={seq=2,auction_item=item_table[34],},
[3]={seq=3,auction_item=item_table[35],},
[4]={seq=4,auction_item=item_table[36],},
[5]={seq=5,auction_item=item_table[37],base_price=1782,origin_price=5940,},
[6]={seq=6,auction_item=item_table[38],base_price=4500,origin_price=15000,},
[7]={seq=7,auction_item=item_table[39],base_price=6750,origin_price=22500,},
[8]={seq=8,auction_item=item_table[40],base_price=500,auction_base_price=50,origin_price=1000,},
[9]={seq=9,auction_item=item_table[41],base_price=1000,origin_price=2000,},
[10]={seq=10,auction_item=item_table[42],},
[11]={seq=11,auction_item=item_table[43],base_price=2916,origin_price=9720,},
[12]={seq=12,auction_item=item_table[44],base_price=4374,auction_base_price=500,origin_price=14580,},
[13]={seq=13,auction_item=item_table[45],base_price=1458,auction_base_price=300,origin_price=4860,},
[14]={seq=14,auction_item=item_table[46],base_price=1500,origin_price=3000,},
[15]={seq=15,auction_item=item_table[47],base_price=150,auction_base_price=10,origin_price=300,rarity=1,},
[16]={seq=16,auction_item=item_table[48],base_price=2250,origin_price=4500,rarity=1,},
[17]={seq=17,auction_item=item_table[49],},
[18]={seq=18,auction_item=item_table[50],},
[19]={seq=19,auction_item=item_table[51],},
[20]={seq=20,auction_item=item_table[52],base_price=750,auction_base_price=50,origin_price=1500,rarity=1,},
[21]={seq=21,auction_item=item_table[53],base_price=588,origin_price=2940,}
},

auction_item_meta_table_map={
[1]=0,	-- depth:1
[2]=0,	-- depth:1
[14]=16,	-- depth:1
[10]=12,	-- depth:1
[7]=12,	-- depth:1
[6]=13,	-- depth:1
[11]=13,	-- depth:1
[21]=16,	-- depth:1
[17]=20,	-- depth:1
[18]=20,	-- depth:1
[19]=20,	-- depth:1
},
falling={

},

falling_meta_table_map={
},
gather={
[0]={seq=0,},
[1]={seq=1,},
[2]={seq=2,},
[3]={seq=3,},
[4]={seq=4,},
[5]={seq=5,},
[6]={seq=6,},
[7]={seq=7,},
[8]={seq=8,},
[9]={seq=9,},
[10]={seq=10,reward_item={[0]=item_table[54],[1]=item_table[54],[2]=item_table[54]},},
[11]={seq=11,reward_item={[0]=item_table[55],[1]=item_table[56],[2]=item_table[57]},},
[12]={seq=12,reward_item={[0]=item_table[55],[1]=item_table[55],[2]=item_table[55]},},
[13]={seq=13,reward_item={[0]=item_table[56],[1]=item_table[56],[2]=item_table[56]},},
[14]={seq=14,reward_item={[0]=item_table[57],[1]=item_table[57],[2]=item_table[57]},},
[15]={seq=15,reward_item={[0]=item_table[16],[1]=item_table[16],[2]=item_table[16]},},
[16]={seq=16,reward_item={[0]=item_table[58],[1]=item_table[58],[2]=item_table[58]},},
[17]={seq=17,reward_item={[0]=item_table[59],[1]=item_table[59],[2]=item_table[59]},},
[18]={seq=18,reward_item={[0]=item_table[49],[1]=item_table[60],[2]=item_table[60]},},
[19]={seq=19,},
[20]={seq=20,reward_item={[0]=item_table[51],[1]=item_table[60],[2]=item_table[60]},},
[21]={seq=21,},
[22]={seq=22,gather_id=3101,cost_item_id=63004,cost_item_num=1,gather_times=10,gather_time=1,add_score=100,reward_item={[0]=item_table[50],[1]=item_table[60],[2]=item_table[60]},},
[23]={seq=23,}
},

gather_meta_table_map={
[7]=16,	-- depth:1
[6]=15,	-- depth:1
[5]=14,	-- depth:1
[4]=13,	-- depth:1
[3]=12,	-- depth:1
[2]=11,	-- depth:1
[1]=10,	-- depth:1
[8]=17,	-- depth:1
[18]=22,	-- depth:1
[19]=22,	-- depth:1
[20]=22,	-- depth:1
[21]=18,	-- depth:2
[23]=20,	-- depth:2
},
scoring={
{time=50,reward_item={[0]=item_table[61],[1]=item_table[49],[2]=item_table[62],[3]=item_table[63],[4]=item_table[63]},},
{time=80,reward_item={[0]=item_table[64],[1]=item_table[65],[2]=item_table[66],[3]=item_table[66]},scoring_index=2,min_time=50,scoring_desc="SS",},
{reward_item={[0]=item_table[67],[1]=item_table[68],[2]=item_table[69],[3]=item_table[69]},scoring_index=3,min_time=80,scoring_desc="S",},
{monster_seq=1,time=70,reward_item={[0]=item_table[61],[1]=item_table[51],[2]=item_table[62],[3]=item_table[63],[4]=item_table[63]},},
{monster_seq=1,time=100,reward_item={[0]=item_table[70],[1]=item_table[65],[2]=item_table[66],[3]=item_table[66]},min_time=70,},
{monster_seq=1,reward_item={[0]=item_table[71],[1]=item_table[68],[2]=item_table[69],[3]=item_table[69]},min_time=100,},
{monster_seq=2,time=100,reward_item={[0]=item_table[61],[1]=item_table[50],[2]=item_table[62],[3]=item_table[63],[4]=item_table[63]},},
{monster_seq=2,time=130,reward_item={[0]=item_table[72],[1]=item_table[65],[2]=item_table[66],[3]=item_table[66]},min_time=100,},
{monster_seq=2,reward_item={[0]=item_table[73],[1]=item_table[68],[2]=item_table[69],[3]=item_table[69]},min_time=130,},
{monster_seq=3,time=130,},
{monster_seq=3,time=160,reward_item={[0]=item_table[74],[1]=item_table[65],[2]=item_table[66],[3]=item_table[66]},min_time=130,},
{monster_seq=3,min_time=160,},
{monster_seq=4,time=190,},
{monster_seq=4,time=220,min_time=190,},
{monster_seq=4,reward_item={[0]=item_table[75],[1]=item_table[68],[2]=item_table[69],[3]=item_table[69]},min_time=220,},
{monster_seq=5,time=250,},
{monster_seq=5,time=280,min_time=250,},
{monster_seq=5,min_time=280,}
},

scoring_meta_table_map={
[15]=3,	-- depth:1
[12]=15,	-- depth:2
[9]=3,	-- depth:1
[6]=3,	-- depth:1
[18]=15,	-- depth:2
[11]=2,	-- depth:1
[8]=2,	-- depth:1
[14]=11,	-- depth:2
[5]=2,	-- depth:1
[17]=11,	-- depth:2
},
score_reward={
[0]={seq=0,},
[1]={seq=1,need_score=100,},
[2]={seq=2,need_score=200,},
[3]={seq=3,need_score=300,reward_item={[0]=item_table[76],[1]=item_table[48],[2]=item_table[54],[3]=item_table[16],[4]=item_table[77]},},
[4]={seq=4,need_score=400,},
[5]={seq=5,need_score=500,},
[6]={seq=6,need_score=600,reward_item={[0]=item_table[78],[1]=item_table[48],[2]=item_table[54],[3]=item_table[16],[4]=item_table[77]},},
[7]={seq=7,need_score=700,},
[8]={seq=8,need_score=800,},
[9]={seq=9,need_score=1000,reward_item={[0]=item_table[79],[1]=item_table[48],[2]=item_table[54],[3]=item_table[16],[4]=item_table[77]},}
},

score_reward_meta_table_map={
[4]=3,	-- depth:1
[5]=3,	-- depth:1
[7]=6,	-- depth:1
[8]=6,	-- depth:1
},
dividends={
{},
{money_type=2,}
},

dividends_meta_table_map={
},
boss_drop_show={
[1]={seq=1,stage_show_txt="击杀四名守卫",},
[2]={seq=2,show_stage=2,stage_show_title="暗黑大圣",stage_show_txt="击败暗黑大圣",},
[3]={seq=3,show_stage=3,stage_show_title="大战天龙",},
[4]={seq=4,show_stage=4,stage_show_title="千军万马",stage_show_txt="抵挡镇龙台的进攻",},
[5]={seq=5,show_stage=5,stage_show_title="祖龙将军",},
[6]={seq=6,show_stage=6,stage_show_title="陨石之乱",}
},

boss_drop_show_meta_table_map={
},
guide={
{model=2065,name="逍遥观长老",hole_time=4,},
{step=2,word="等等，为何炉中突然冒出大量魔气！！",},
{id=2,trigger_param_1=3,word="感谢你们拯救了我，前方祖龙台有只金龙盘踞，要小心了！",hole_time=4,},
{id=3,trigger_type=2,word="小小孽龙还敢作乱，看打！",},
{id=4,trigger_type=2,trigger_param_1=3,word="哦，是巨灵神？原来是老熟人啊哈哈哈！",},
{id=5,trigger_param_1=5,word="神君？未曾听闻过此号人物，待俺前去探探！",},
{id=6,trigger_param_1=6,word="大家伙来了，要小心了！",},
{id=7,trigger_param_1=1,word="吼！好难受啊",hole_time=4,},
{id=7,step=2,trigger_param_1=1,word="我的眼睛，我的身体全被魔气侵蚀了！",}
},

guide_meta_table_map={
[2]=1,	-- depth:1
[8]=5,	-- depth:1
[9]=5,	-- depth:1
},
monster_cg={
[3400]={boss_id=3400,},
[3401]={boss_id=3401,operate_param="1##355##10",},
[3402]={boss_id=3402,interlude_name_img=4,},
[3403]={boss_id=3403,interlude_name_img=5,},
[3404]={boss_id=3404,},
[3405]={boss_id=3405,interlude_name_img=2,},
[3407]={boss_id=3407,operate_param="1##355##10",},
[3408]={boss_id=3408,},
[3409]={boss_id=3409,},
[3410]={boss_id=3410,},
[3411]={boss_id=3411,}
},

monster_cg_meta_table_map={
[3408]=3403,	-- depth:1
[3410]=3405,	-- depth:1
[3411]=3410,	-- depth:2
[3407]=3402,	-- depth:1
},
other_default_table={open_level=150,scene_id=661,kick_out_time=30,open_day=1,xiuwei_nuqi_get_per=2,xiuwei_bianshen_skill=1101,wait_time=15,auction_time=20,random_bs_type=0,init_bs_times=5,change_bs_cd=10,bs_effect_time=0,enterance_time=75600,enterance_title="御剑天宫，征战诸神",box_pos="200,863",enter_time_desc="每天21点开启",gameplay_desc="全民御剑战斗，与跨服玩家一起征战天宫\n全程入神姿态，随时切换神魔姿态\n击败真·诸神，开启天地异变，扭转天宫环境，并抢夺神藏奖赏",field_reward={[0]=item_table[80],[1]=item_table[45],[2]=item_table[81],[3]=item_table[82],[4]=item_table[74],[5]=item_table[75],[6]=item_table[49],[7]=item_table[50],[8]=item_table[51],[9]=item_table[48],[10]=item_table[83],[11]=item_table[79],[12]=item_table[55]},enterance_title="御剑天宫，征战诸神",wait_air_wall="179##79##60##10",},

stage_default_table={stage=1,air_wall_id=1,air_wall="160##224##100##20",},

monster_default_table={seq=0,stage=1,show_stage=1,monster_id=3412,monster_num=1,kill_add_bs_times=1,monster_pos="213,134|163,138|166,187|216,184",refrensh_time=8,is_boss=1,start_vie_seq=-1,vie_score=0,stage_name="解救大圣",target_desc="镇炉守卫",detail_desc="大圣擅闯天宫，被太上老君使用炼丹炉困住了，我们得击杀四个阵炉守卫救出大圣",monster_reward={[0]=item_table[84],[1]=item_table[45],[2]=item_table[81],[3]=item_table[85],[4]=item_table[86]},monster_head="",stage_status=2,stage_dec="封印解除，镇守丹炉的护卫出现了，快去消灭他们！",},

hurt_cap_default_table={monster_seq=0,index=0,min_world_level=1,max_world_level=300,drop_id=14033,},

rank_reward_default_table={monster_seq=1,min_rank=1,max_rank=1,add_score=200,reward_item={[0]=item_table[84],[1]=item_table[45],[2]=item_table[81],[3]=item_table[85],[4]=item_table[86]},reward_show_item={[0]=item_table[87]},},

auction_grade_default_table={},

auction_default_table={grade=1,round=0,seq=0,show_stage=1,weight="17,1000|8,1000|5,1000",},

auction_item_default_table={seq=0,auction_item=item_table[80],money_type=1,base_price=2000,auction_base_price=100,max_price=9999999,origin_price=4000,rarity=2,},

falling_default_table={},

gather_default_table={seq=0,gather_id=3100,cost_item_id=0,cost_item_num=0,gather_times=5,gather_time=2,duration_time=3000,add_score=10,reward_item={[0]=item_table[16],[1]=item_table[58],[2]=item_table[59]},},

scoring_default_table={monster_seq=0,time=99999,reward_item={[0]=item_table[61],[1]=item_table[82],[2]=item_table[62],[3]=item_table[63],[4]=item_table[63]},scoring_index=1,min_time=0,scoring_desc="SSS",},

score_reward_default_table={seq=0,need_score=50,reward_item={[0]=item_table[88],[1]=item_table[48],[2]=item_table[54],[3]=item_table[16],[4]=item_table[77]},},

dividends_default_table={money_type=1,max_limit=2000,},

boss_drop_show_default_table={seq=1,show_stage=1,stage_show_title="解救大圣",stage_show_txt="选择击杀神道boss",reward_item={[0]=item_table[84],[1]=item_table[45],[2]=item_table[81],[3]=item_table[85],[4]=item_table[86]},},

guide_default_table={id=1,step=1,trigger_type=1,trigger_param_1=2,trigger_param_2=0,model=2084,center_pos="0|-161",display_pos="-76|-175",model_pos="0.6|-0.8|0",model_scale=0.9,model_euler=20,name="黑暗大圣",word="镇炉守卫已经全数击杀，应该可以解救炉中的大圣了",hole_time=5,talk_audio="",},

monster_cg_default_table={boss_id=3400,operate_param="1##355##7",cg_time=4,camera_offset="0##7",boss_name_bundle="uis/view/interlude_pop_dialog_prefab",boss_name_assets="interlude_pop_boss_show",interlude_name_img=3,}

}

