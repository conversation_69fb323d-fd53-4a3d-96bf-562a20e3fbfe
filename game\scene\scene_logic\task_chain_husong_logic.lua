TaskChainHuSongLogic = TaskChainHuSongLogic or BaseClass(CommonFbLogic)
function TaskChainHuSongLogic:__init()
end

function TaskChainHuSongLogic:__delete()
end

function TaskChainHuSongLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	ViewManager.Instance:CloseAll()

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Scene.Instance:GetSceneName())
		MainuiWGCtrl.Instance:SetTeamBtnState(false)

		ViewManager.Instance:Open(GuideModuleName.OperationTaskChainFbInfoView)
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)

	ViewManager.Instance:Open(GuideModuleName.OperationTaskChainScheduleView)
end

function TaskChainHuSongLogic:Out(old_scene_type, new_scene_type)
	OperationTaskChainWGCtrl.Instance:RemoveDelay()
	
	CommonFbLogic.Out(self)
	
	ViewManager.Instance:CloseAll()
	MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)

	UiInstanceMgr.Instance:ColseFBStartDown()
	OperationTaskChainWGCtrl.Instance:ResetInfoView()
	OperationTaskChainWGData.Instance:ResetHuSongInfo()
	
	FuBenPanelCountDown.Instance:CloseViewHandler()

	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		main_role:SetUnderShowInfo(nil)
	end
end

function TaskChainHuSongLogic:IsRoleEnemy(target_obj, main_role)
	return false
end

function TaskChainHuSongLogic:GetGuajiCharacter()
	local is_need_stop = true

	local target_obj = self:GetMonster()
	if target_obj == nil then
		local is_need_follow, obj_id = OperationTaskChainWGData.Instance:GetHuSongIsNeedFollow()
		local main_role = Scene.Instance:GetMainRole()
		if is_need_follow and obj_id ~= nil and main_role ~= nil and not main_role:IsFollowState() then
			local follow_obj = Scene.Instance:GetObj(obj_id)
			if follow_obj ~= nil then
				main_role:StopMove()
				follow_obj:FollowMe(main_role, OperationTaskChainWGData.Instance:GetFollowDis(), OBJ_FOLLOW_TYPE.TASK_CHAIN)
			end
		end
	else
		local main_role = Scene.Instance:GetMainRole()
		if main_role ~= nil and main_role:IsFollowState() then
			main_role:SetIsFollowState(false, nil, nil, OBJ_FOLLOW_TYPE.TASK_CHAIN)
		end
	end

	return target_obj, nil, is_need_stop
end

function TaskChainHuSongLogic:GetMonster()
	local distance_limit = 1000000
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end
	
	return obj
end

-- 怪物是否是敌人
function TaskChainHuSongLogic:IsMonsterEnemy(target_obj, main_role)
	if target_obj ~= nil and not target_obj:IsDeleted() then
		local info = OperationTaskChainWGData.Instance:GetHuSongInfo()
		if info ~= nil then
			return target_obj:GetObjId() ~= info.objid
		end
	end

	return CommonFbLogic.IsMonsterEnemy(self, target_obj, main_role)
end

function TaskChainHuSongLogic:GetGuajiPos()
end

function TaskChainHuSongLogic:CanGetMoveObj()
	return false
end

function TaskChainHuSongLogic:CheckObjIsIgnoreShowMainSelect(target_obj, select_type)
	if SceneWGData:TargetSelectIsScene(select_type) then
		if target_obj ~= nil and not target_obj:IsDeleted() and target_obj:IsMonster() then
			return not self:IsEnemy(target_obj)
		else
			return false
		end
	else
		return false
	end
end