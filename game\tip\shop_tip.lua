------------------------------------------------------------
--商店tip
------------------------------------------------------------
ShopTip = ShopTip or BaseClass(DisplayItemTip)

local GuaJiItemId = 22537 --挂机小符人(6小时)道具id

function ShopTip:__init()
	if ShopTip.Instance then
		ErrorLog("[ShopTip] Attemp to create a singleton twice !")
	end
	ShopTip.Instance = self

	self.view_name = "ShopTip"
	self.item_cfg = nil
	self.item_num = 1
	self.consume_type = 0
	-- self:AddViewResource(0, bundle_name, "layout_shoptip")
end

function ShopTip:__delete()
	ShopTip.Instance = nil
end

function ShopTip:ReleaseCallBack()
	self:ReleaseTipsPanel()
	self.item_cfg = nil
	self.item_cell = nil
end

function ShopTip:LoadCallBack()
	self:InitTipsPanel()
end

function ShopTip:OpenCallBack()

end

function ShopTip:CloseCallBack()

end

function ShopTip:ShowIndexCallBack()

end

--						(物品配置，   消费类型，    商店类型，  物品数量，物品ID，  是否等级限制，当前可购      完成回调）
function ShopTip:SetData(item_cfg, consume_type, shop_type, item_num, item_seq, buy_limit, can_buy_num, complete_func)
	--print_error("SetData:::", item_cfg, consume_type, shop_type, item_num, item_seq, buy_limit, can_buy_num)
	if item_cfg == nil then
		print_error("shop_tip传入空物品配置", consume_type, shop_type, item_num, item_seq, buy_limit, can_buy_num)
	end
	self.data = {item_id = item_cfg.id}
	self.item_cfg = item_cfg
	self.item_num = item_num or 1 -- item_num or
	self.consume_type = consume_type
	self.shop_type = shop_type
	self.item_seq = item_seq
	self.buy_limit = buy_limit
	self.complete_func = complete_func
	self.limit_count = can_buy_num or 0

	local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.item_seq)
	if shop_cfg == nil then
		print_error("错误日志记录 错误的商店传值", self.item_seq, item_cfg.id)
		return
	end

	self:Open()
end

-- 检测VIP购买，满足情况
function ShopTip:CheckVip()
	local is_show, back_str, is_can_buy, limit_vip_level = ShopWGData.Instance:IsVipLimit(self.item_seq)
	if not is_show or not limit_vip_level or limit_vip_level <=0 then
		return false
	end
	
	local role_vip_level = VipWGData.Instance:GetRoleVipLevel()
	if role_vip_level >= limit_vip_level then
		return false
	end

	local vip_level = VipWGData.Instance:GetVipLevel()  -- 过期后的等级也会获取到
	local zero_buy_level = VipWGData.Instance:GetVIPZeroBuyCfg("arrive_level") or 0 -- 零元购能提升的等级

	local ok_func = nil
	local desc = ""
	local ok_btn_text = ""
	
	if zero_buy_level >= limit_vip_level and not RechargeWGData.Instance:IsBuyVipZeroBuy() then -- 有零元购优先弹
		desc = string.format(Language.Shop.ShopBuy01, limit_vip_level, limit_vip_level)
		ok_btn_text = string.format(Language.Shop.ShopBuy04, limit_vip_level)
		ok_func = function ()
			local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
    		ViewManager.Instance:Open(GuideModuleName.Vip, tab_index)
    	end
    elseif vip_level < limit_vip_level then -- 	VIP等级不足
    	desc = string.format(Language.Shop.ShopBuy03, limit_vip_level)
		ok_btn_text = Language.Shop.ShopBuy06
		ok_func = function ()
    		ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip)
    	end
    elseif vip_level > 0 and role_vip_level == 0 then -- VIP过期
    	desc = Language.Shop.ShopBuy02
		ok_btn_text = Language.Shop.ShopBuy05
		ok_func = function ()
    		-- VipWGCtrl.Instance:OpenVipRenewView()
			ViewManager.Instance:Open(GuideModuleName.Vip,TabIndex.recharge_vip)
    	end
    end

	if ok_func then
		TipWGCtrl.Instance:OpenConfirmAlertTips(desc, ok_func, ok_btn_text, false)
	end

	return nil ~= ok_func
end

function ShopTip:OnPayClickHandler()
	if not self.item_cfg then
		return
	end

	local buy_count = self.base_tips:GetBuyCount()
	if buy_count <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ChongXinShuRu)
		return
	end

	if self:CheckVip() then
		self:Close()
		return
	end

	if self.shop_type == GameEnum.SHOP then
		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.item_seq)
		if shop_cfg.shop_type == SHOP_BIG_TYPE.SHOP_TYPE_10 and not RechargeWGData.Instance:IsHasWeekCard() then
			ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_week_card)
			self:Close()
			return
		elseif shop_cfg.shop_type == SHOP_BIG_TYPE.SHOP_TYPE_11 and LongXiWGData.Instance:GetLongXiDataByType(LONGXI_ACTIVITY_TYPE.SUPERDRAGONSEAL).is_active == 0 then
			ViewManager.Instance:Open(GuideModuleName.LongXiView, TabIndex.super_dragon_seal)
			self:Close()
			return
		end

		local buy_info = self.base_tips:GetBuyInfo()
		local is_bind = 0
		local is_can_cell, str = ShopWGData.Instance:IsCanCell(self.item_seq, true)
		if not is_can_cell then
			if str then
				SysMsgWGCtrl.Instance:ErrorRemind(str)
			end
			self:Close()
			return
		end

		if not ShopWGData.Instance:CheckMoneyToBuy(self.item_seq, buy_count, true, false, buy_info.price) then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COIN_ITEM_ID[buy_info.price_type]})
			LimitTimeGiftWGCtrl.Instance:CheckNoGoldPopupGift()
			LimitTimeGiftWGCtrl.Instance:CheckNoGoldPopupGift2()
			self:Close()
			return
		end

		local function ok_func()
			if buy_info.price_type == Shop_Money_Type.Type1 and buy_count * buy_info.price >= COMMON_CONSTS.AlertConst then
				local name_str = ToColorStr(self.item_cfg.name, ITEM_COLOR[self.item_cfg.color])
				local item_id = self.item_cfg.id
				TipWGCtrl.Instance:OpenAlertTips(string.format(Language.Common.CommonAlertFormat1, buy_info.price * buy_count, name_str, buy_count), function ()
					ShopWGCtrl.Instance:SendShopBuy(item_id, buy_count, 0, 0, self.item_seq)
				end)
			else
				ShopWGCtrl.Instance:SendShopBuy(self.item_cfg.id, buy_count, 0, is_bind, self.item_seq)
			end

			self:Close()
		end

		if not ShopWGData.Instance:CheckBangYuToBuy(self.item_seq, buy_count, buy_info.price) then
			TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func, function()
				self:Close()
			end)
		else
			ok_func()
		end

		if self.complete_func then
			self.complete_func()
		end

		return

	elseif self.shop_type == GameEnum.TEHUI_SHUP then
		TehuiShopWGCtrl.Instance:SendRAPanicBuyOperaReq(RA_PANICBUY_OPERA_TYPE.RA_PANICBUY_OPERA_BUY, self.item_seq, buy_count)
	elseif self.shop_type == GameEnum.SPECIAL_SHOP then --绑元不足消耗元宝
		local is_bind = 0
		if ShopWGData.IsConsumeBind(self.consume_type) then
			is_bind = 1
		end
		if is_bind == 1 then
			local bind_gold = RoleWGData.Instance.role_vo.bind_gold
			local total_price = self:ComputedValue()
			if bind_gold < total_price then
				is_bind = 0
			end
		end
		ShopWGCtrl.Instance:SendShopBuy(self.item_cfg.id, buy_count, 0, is_bind)
	end
	self:Close()
end

function ShopTip:OnFlush()
	if not self.item_cfg then
		return
	end
	self.base_tips:Reset()

	local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.item_seq)
	self.item_cell:SetData({item_id = shop_cfg.itemid, num = 1, is_bind = shop_cfg.is_bind})

	local buy_info =
	{
		buy_count = self.item_num, 
		max_buy_count = self.limit_count, 
		price = shop_cfg.price, 
		price_type = shop_cfg.price_type, 
		item_seq = self.item_seq,
		item_id = shop_cfg.itemid
	}
	
	---[[ 活动期间打折
	local discount = ShopWGData.Instance:GetActDiscountByItemSeq(self.item_seq)
	if discount > 0 then
		buy_info.price = buy_info.price * discount / 10
	end
	--]]

	self.base_tips:SetBuyPanel(buy_info)
	self:ShowMoneyBar(true, {price_type = shop_cfg.price_type})

	local btn_info = {}
	btn_info[1] = {btn_name = Language.F2Tip.Buy, btn_click = BindTool.Bind1(self.OnPayClickHandler, self)}
	self.base_tips:SetBtnsClick(btn_info)

	if ItemWGData.GetIsXiaogGui(self.item_cfg.id) then 	--小鬼
		self:ParseXiaogui(self.item_cfg)
	end

	self:ShowTopPanelInfo()
	self:ShowItemDescription(self.item_cfg)
	self:ShowSkillObj()
	self:ShowSpecialSkillObj()
	self:ShowDisplay(self.item_cfg.id)
end

function ShopTip:ShowTopPanelInfo()
	local item_cfg = self.item_cfg
	local role_lv = RoleWGData.Instance:GetRoleLevel()
	local text_color = role_lv >= item_cfg.limit_level and TIPS_COLOR.SOCRE or COLOR3B.D_RED

	self.base_tips:SetItemName(ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color]))
	self.base_tips:SetEquipSocre(string.format(Language.Tip.ZhuangBeiLeiXing_1, TIPS_COLOR.SOCRE, Language.ShowItemType[item_cfg.goods_type]))
	self.base_tips:SetSyntheticalSocre(string.format(Language.Shop.UseLevel, ToColorStr(RoleWGData.GetLevelString(item_cfg.limit_level), text_color)))
	self.base_tips:SetTopColorBg(item_cfg.color)
	self.base_tips:SetOrnamentImage(item_cfg.color or 0, item_cfg.special_border or 0)
end

--显示描述description
function ShopTip:ShowItemDescription(item_cfg)
	if item_cfg == nil then
		return
	end

	local info_list = {}
	local description = ItemWGData.Instance:GetItemConstDesc(item_cfg, self.item_num)
	if 84 == item_cfg.use_type then               -- 使用类型为84的具体描述读配置
		description = string.format(description, CommonDataManager.ConverExp(self:GetLeveReward(item_cfg.param1)))
	end

	description = CommonDataManager.ParseGameName(description)
	if description and description ~= "" then
		info_list[#info_list + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = description}
	end

	local desc = nil
	for i = 2, 6 do
		desc = item_cfg["description" .. i]
		if desc and desc ~= "" then
			info_list[#info_list + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
		elseif i == 2 and item_cfg.search_type == 1500 then
			desc = ItemWGData.Instance:AutoGetGiftDropDesc(item_cfg.id)
			info_list[#info_list + 1] = {title = item_cfg["biaoti" .. i], desc = desc}
		end
	end

	if item_cfg.id == GuaJiItemId then
		local remain_time = OfflineRestWGData.Instance:GetRemainOfflineRestTime()
		local limit_time = 20 --挂机时间上限20小时，策划还未加配置，这里暂时先写死
		local desc = ""
		if remain_time == 0 then
			desc = string.format(Language.Common.GuajiItemRemainTimeStr, Language.F2Tip.ZeroTimeStr, limit_time)
		else
			desc = string.format(Language.Common.GuajiItemRemainTimeStr, TimeUtil.FormatSecondDHM(remain_time), limit_time)
		end
		info_list[#info_list + 1] = {title = Language.F2Tip.WuPinMiaoShu, desc = desc}
	end

	local attr_desc = ""
	local capability = 0
	local need_get_sys, sys_type, replace_idx, show_cap_type = ItemShowWGData.Instance:GetIsNeedSysAttr(item_cfg.id, item_cfg.sys_attr_cap_location)
	if need_get_sys then
		attr_desc, capability = ItemShowWGData.Instance:GetItemAttrDescAndCap(self.data, sys_type)
		if attr_desc ~= "" then
			local temp_title = Language.F2Tip.SysAttrTitle[sys_type] or Language.F2Tip.SysAttrTitle[0]
			local temp_data = {title = temp_title, desc = attr_desc}
			table.insert(info_list, replace_idx, temp_data)
		end
	end

	self.base_tips:SetItemDesc(info_list)

	if item_cfg.xiushici and item_cfg.xiushici ~= "" then
		self.base_tips:SetGetWayDesc({xiushici = item_cfg.xiushici})
	end

	local have_cfg_cap = item_cfg.capability_show and item_cfg.capability_show > 0
	if need_get_sys and show_cap_type == TIPS_CAP_SHOW.SHOW_CALC and capability > 0 then
		self.base_tips:SetCapabilityPanel({capability = capability})
	elseif (need_get_sys and show_cap_type == TIPS_CAP_SHOW.SHOW_CFG) or (not need_get_sys or not show_cap_type) then
		if have_cfg_cap then
			self.base_tips:SetCapabilityPanel({capability = item_cfg.capability_show})
		end
	end
end
