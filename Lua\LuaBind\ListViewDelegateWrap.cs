﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ListViewDelegateWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>ginClass(typeof(ListViewDelegate), typeof(System.Object));
		<PERSON><PERSON>unction("GetNumberOfCells", GetNumberOfCells);
		<PERSON><PERSON>unction("GetCellViewSize", GetCellViewSize);
		<PERSON><PERSON>unction("GetCellView", GetCellView);
		L<PERSON>RegFunction("New", _CreateListViewDelegate);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("numberOfCellsDel", get_numberOfCellsDel, set_numberOfCellsDel);
		L.<PERSON>ar("cellViewSizeDel", get_cellViewSizeDel, set_cellViewSizeDel);
		<PERSON><PERSON>("cellViewDel", get_cellViewDel, set_cellViewDel);
		<PERSON><PERSON>("CellViewDelegate", ListViewDelegate_CellViewDelegate);
		<PERSON><PERSON>unction("CellViewSizeDelegate", ListViewDelegate_CellViewSizeDelegate);
		L.RegFunction("NumberOfCellsDelegate", ListViewDelegate_NumberOfCellsDelegate);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateListViewDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				ListViewDelegate obj = new ListViewDelegate();
				ToLua.PushSealed(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: ListViewDelegate.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNumberOfCells(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewDelegate obj = (ListViewDelegate)ToLua.CheckObject(L, 1, typeof(ListViewDelegate));
			EnhancedUI.EnhancedScroller.EnhancedScroller arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 2);
			int o = obj.GetNumberOfCells(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCellViewSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ListViewDelegate obj = (ListViewDelegate)ToLua.CheckObject(L, 1, typeof(ListViewDelegate));
			EnhancedUI.EnhancedScroller.EnhancedScroller arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			float o = obj.GetCellViewSize(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCellView(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			ListViewDelegate obj = (ListViewDelegate)ToLua.CheckObject(L, 1, typeof(ListViewDelegate));
			EnhancedUI.EnhancedScroller.EnhancedScroller arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView o = obj.GetCellView(arg0, arg1, arg2);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_numberOfCellsDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewDelegate obj = (ListViewDelegate)o;
			ListViewDelegate.NumberOfCellsDelegate ret = obj.numberOfCellsDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index numberOfCellsDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cellViewSizeDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewDelegate obj = (ListViewDelegate)o;
			ListViewDelegate.CellViewSizeDelegate ret = obj.cellViewSizeDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellViewSizeDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_cellViewDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewDelegate obj = (ListViewDelegate)o;
			ListViewDelegate.CellViewDelegate ret = obj.cellViewDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellViewDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_numberOfCellsDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewDelegate obj = (ListViewDelegate)o;
			ListViewDelegate.NumberOfCellsDelegate arg0 = (ListViewDelegate.NumberOfCellsDelegate)ToLua.CheckDelegate<ListViewDelegate.NumberOfCellsDelegate>(L, 2);
			obj.numberOfCellsDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index numberOfCellsDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cellViewSizeDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewDelegate obj = (ListViewDelegate)o;
			ListViewDelegate.CellViewSizeDelegate arg0 = (ListViewDelegate.CellViewSizeDelegate)ToLua.CheckDelegate<ListViewDelegate.CellViewSizeDelegate>(L, 2);
			obj.cellViewSizeDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellViewSizeDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_cellViewDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewDelegate obj = (ListViewDelegate)o;
			ListViewDelegate.CellViewDelegate arg0 = (ListViewDelegate.CellViewDelegate)ToLua.CheckDelegate<ListViewDelegate.CellViewDelegate>(L, 2);
			obj.cellViewDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index cellViewDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListViewDelegate_CellViewDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<ListViewDelegate.CellViewDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<ListViewDelegate.CellViewDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListViewDelegate_CellViewSizeDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<ListViewDelegate.CellViewSizeDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<ListViewDelegate.CellViewSizeDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListViewDelegate_NumberOfCellsDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<ListViewDelegate.NumberOfCellsDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<ListViewDelegate.NumberOfCellsDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

