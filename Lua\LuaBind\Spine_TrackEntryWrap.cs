﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Spine_TrackEntryWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>BeginClass(typeof(Spine.TrackEntry), typeof(System.Object));
		<PERSON><PERSON>RegFunction("Reset", Reset);
		<PERSON><PERSON>RegFunction("SetMixDuration", SetMixDuration);
		<PERSON><PERSON>Function("ResetRotationDirections", ResetRotationDirections);
		<PERSON><PERSON>unction("ToString", ToString);
		L<PERSON>RegFunction("AllowImmediateQueue", AllowImmediateQueue);
		<PERSON><PERSON>RegFunction("New", _CreateSpine_TrackEntry);
		L<PERSON>Function("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("TrackIndex", get_TrackIndex, null);
		<PERSON><PERSON>ar("Animation", get_Animation, null);
		<PERSON><PERSON>RegVar("Loop", get_Loop, set_Loop);
		<PERSON><PERSON>("Delay", get_Delay, set_Delay);
		<PERSON><PERSON>("TrackTime", get_TrackTime, set_TrackTime);
		<PERSON><PERSON>("TrackEnd", get_TrackEnd, set_TrackEnd);
		<PERSON><PERSON>("TrackComplete", get_TrackComplete, null);
		L.RegVar("AnimationStart", get_AnimationStart, set_AnimationStart);
		L.RegVar("AnimationEnd", get_AnimationEnd, set_AnimationEnd);
		L.RegVar("AnimationLast", get_AnimationLast, set_AnimationLast);
		L.RegVar("AnimationTime", get_AnimationTime, null);
		L.RegVar("TimeScale", get_TimeScale, set_TimeScale);
		L.RegVar("Alpha", get_Alpha, set_Alpha);
		L.RegVar("InterruptAlpha", get_InterruptAlpha, null);
		L.RegVar("EventThreshold", get_EventThreshold, set_EventThreshold);
		L.RegVar("AlphaAttachmentThreshold", get_AlphaAttachmentThreshold, set_AlphaAttachmentThreshold);
		L.RegVar("MixAttachmentThreshold", get_MixAttachmentThreshold, set_MixAttachmentThreshold);
		L.RegVar("MixDrawOrderThreshold", get_MixDrawOrderThreshold, set_MixDrawOrderThreshold);
		L.RegVar("Next", get_Next, null);
		L.RegVar("Previous", get_Previous, null);
		L.RegVar("WasApplied", get_WasApplied, null);
		L.RegVar("IsNextReady", get_IsNextReady, null);
		L.RegVar("IsComplete", get_IsComplete, null);
		L.RegVar("MixTime", get_MixTime, set_MixTime);
		L.RegVar("MixDuration", get_MixDuration, set_MixDuration);
		L.RegVar("MixBlend", get_MixBlend, set_MixBlend);
		L.RegVar("MixingFrom", get_MixingFrom, null);
		L.RegVar("MixingTo", get_MixingTo, null);
		L.RegVar("HoldPrevious", get_HoldPrevious, set_HoldPrevious);
		L.RegVar("Reverse", get_Reverse, set_Reverse);
		L.RegVar("ShortestRotation", get_ShortestRotation, set_ShortestRotation);
		L.RegVar("IsEmptyAnimation", get_IsEmptyAnimation, null);
		L.RegVar("Start", get_Start, set_Start);
		L.RegVar("Interrupt", get_Interrupt, set_Interrupt);
		L.RegVar("End", get_End, set_End);
		L.RegVar("Dispose", get_Dispose, set_Dispose);
		L.RegVar("Complete", get_Complete, set_Complete);
		L.RegVar("Event", get_Event, set_Event);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateSpine_TrackEntry(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Spine.TrackEntry obj = new Spine.TrackEntry();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Spine.TrackEntry.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Reset(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject<Spine.TrackEntry>(L, 1);
			obj.Reset();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMixDuration(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject<Spine.TrackEntry>(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			float arg1 = (float)LuaDLL.luaL_checknumber(L, 3);
			obj.SetMixDuration(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetRotationDirections(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject<Spine.TrackEntry>(L, 1);
			obj.ResetRotationDirections();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ToString(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject<Spine.TrackEntry>(L, 1);
			string o = obj.ToString();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AllowImmediateQueue(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject<Spine.TrackEntry>(L, 1);
			obj.AllowImmediateQueue();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TrackIndex(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			int ret = obj.TrackIndex;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TrackIndex on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Animation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			Spine.Animation ret = obj.Animation;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Animation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Loop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool ret = obj.Loop;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Loop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Delay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.Delay;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Delay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TrackTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.TrackTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TrackTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TrackEnd(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.TrackEnd;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TrackEnd on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TrackComplete(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.TrackComplete;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TrackComplete on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AnimationStart(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.AnimationStart;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AnimationStart on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AnimationEnd(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.AnimationEnd;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AnimationEnd on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AnimationLast(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.AnimationLast;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AnimationLast on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AnimationTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.AnimationTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AnimationTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.TimeScale;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Alpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.Alpha;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Alpha on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_InterruptAlpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.InterruptAlpha;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index InterruptAlpha on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EventThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.EventThreshold;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EventThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_AlphaAttachmentThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.AlphaAttachmentThreshold;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AlphaAttachmentThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MixAttachmentThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.MixAttachmentThreshold;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixAttachmentThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MixDrawOrderThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.MixDrawOrderThreshold;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixDrawOrderThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Next(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			Spine.TrackEntry ret = obj.Next;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Next on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Previous(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			Spine.TrackEntry ret = obj.Previous;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Previous on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WasApplied(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool ret = obj.WasApplied;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index WasApplied on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsNextReady(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool ret = obj.IsNextReady;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsNextReady on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsComplete(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool ret = obj.IsComplete;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsComplete on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MixTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.MixTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MixDuration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float ret = obj.MixDuration;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixDuration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MixBlend(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			Spine.MixBlend ret = obj.MixBlend;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixBlend on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MixingFrom(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			Spine.TrackEntry ret = obj.MixingFrom;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixingFrom on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_MixingTo(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			Spine.TrackEntry ret = obj.MixingTo;
			ToLua.PushObject(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixingTo on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_HoldPrevious(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool ret = obj.HoldPrevious;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HoldPrevious on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Reverse(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool ret = obj.Reverse;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Reverse on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ShortestRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool ret = obj.ShortestRotation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ShortestRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsEmptyAnimation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool ret = obj.IsEmptyAnimation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsEmptyAnimation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Start(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Interrupt(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_End(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Dispose(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Complete(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Event(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(Spine.AnimationState.TrackEntryEventDelegate)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Loop(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.Loop = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Loop on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Delay(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Delay = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Delay on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TrackTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.TrackTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TrackTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TrackEnd(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.TrackEnd = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TrackEnd on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AnimationStart(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.AnimationStart = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AnimationStart on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AnimationEnd(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.AnimationEnd = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AnimationEnd on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AnimationLast(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.AnimationLast = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AnimationLast on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_TimeScale(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.TimeScale = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index TimeScale on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Alpha(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.Alpha = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Alpha on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_EventThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.EventThreshold = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index EventThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_AlphaAttachmentThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.AlphaAttachmentThreshold = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index AlphaAttachmentThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MixAttachmentThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MixAttachmentThreshold = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixAttachmentThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MixDrawOrderThreshold(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MixDrawOrderThreshold = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixDrawOrderThreshold on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MixTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MixTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MixDuration(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.MixDuration = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixDuration on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_MixBlend(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			Spine.MixBlend arg0 = (Spine.MixBlend)ToLua.CheckObject(L, 2, typeof(Spine.MixBlend));
			obj.MixBlend = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index MixBlend on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_HoldPrevious(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.HoldPrevious = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HoldPrevious on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Reverse(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.Reverse = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Reverse on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ShortestRotation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Spine.TrackEntry obj = (Spine.TrackEntry)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.ShortestRotation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ShortestRotation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Start(IntPtr L)
	{
		try
		{
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject(L, 1, typeof(Spine.TrackEntry));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.TrackEntry.Start' can only appear on the left hand side of += or -= when used outside of the type 'Spine.TrackEntry'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Start += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Start -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Interrupt(IntPtr L)
	{
		try
		{
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject(L, 1, typeof(Spine.TrackEntry));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.TrackEntry.Interrupt' can only appear on the left hand side of += or -= when used outside of the type 'Spine.TrackEntry'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Interrupt += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Interrupt -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_End(IntPtr L)
	{
		try
		{
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject(L, 1, typeof(Spine.TrackEntry));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.TrackEntry.End' can only appear on the left hand side of += or -= when used outside of the type 'Spine.TrackEntry'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.End += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.End -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Dispose(IntPtr L)
	{
		try
		{
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject(L, 1, typeof(Spine.TrackEntry));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.TrackEntry.Dispose' can only appear on the left hand side of += or -= when used outside of the type 'Spine.TrackEntry'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Dispose += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Dispose -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Complete(IntPtr L)
	{
		try
		{
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject(L, 1, typeof(Spine.TrackEntry));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.TrackEntry.Complete' can only appear on the left hand side of += or -= when used outside of the type 'Spine.TrackEntry'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Complete += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryDelegate ev = (Spine.AnimationState.TrackEntryDelegate)arg0.func;
				obj.Complete -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Event(IntPtr L)
	{
		try
		{
			Spine.TrackEntry obj = (Spine.TrackEntry)ToLua.CheckObject(L, 1, typeof(Spine.TrackEntry));
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'Spine.TrackEntry.Event' can only appear on the left hand side of += or -= when used outside of the type 'Spine.TrackEntry'");
			}

			if (arg0.op == EventOp.Add)
			{
				Spine.AnimationState.TrackEntryEventDelegate ev = (Spine.AnimationState.TrackEntryEventDelegate)arg0.func;
				obj.Event += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				Spine.AnimationState.TrackEntryEventDelegate ev = (Spine.AnimationState.TrackEntryEventDelegate)arg0.func;
				obj.Event -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

