-- 服务器通知热更新

Hotupdate = Hotupdate or BaseClass(BaseWGCtrl)
HotupdateProcess = {
	NONE = 0,
	REQUIRE_NEW_VERSION = 1,	-- 请求客户端AssetVersion
	DOWN_LOADING_MANIFEST = 2,	-- 下载LuaManifest
	DOWN_LOADING_BUNDLE = 3,	-- 下载AssetBundle
	DONE = 4,					-- 完成
}

HotupdateFailReason = {
	REQUIRE_NEW_VERSION_FAIL = 1,		-- 请求客户端AssetVersion失败
	DOWN_LOADING_MANIFEST_FAIL = 2,		-- 下载LuaManifest失败
	DOWN_LOADING_BUNDLE_FAIL = 3,		-- 下载AssetBundle失败
	LUA_FILE_NOT_MATCH_BUNDLE = 4,		-- Lua文件找不到对应的AB包
}


local LUA_ASSET_BUNDLE_PREFIX = ""
local LUA_ASSET_PREFIX = ""
if UnityEngine.Application.platform == UnityEngine.RuntimePlatform.IPhonePlayer or
	UnityEngine.Application.platform == UnityEngine.RuntimePlatform.WindowsPlayer then
	LUA_ASSET_BUNDLE_PREFIX = "^lua/.*"
	LUA_ASSET_PREFIX = "Assets/Game/LuaBundle/"
else
	LUA_ASSET_BUNDLE_PREFIX = "^luajit/.*"
	LUA_ASSET_PREFIX = "Assets/Game/LuaBundleJit/"
end

local function GetLuaFileFullPath(file_name)
	return LUA_ASSET_PREFIX .. file_name .. ".lua.bytes"
end

local IS_DEBUG_TEST = false

function Hotupdate:__init()
	if Hotupdate.Instance then
		ErrorLog("[Hotupdate] Attemp to create a singleton twice !")
	end

	Hotupdate.Instance = self

	self.process = HotupdateProcess.NONE
	self.bundle_changed_list = {}

	self.next_update_time = 0

	self.now_server_version = 0
	self.server_version = 0
	self.old_asset_version = 0

	self.hotupdate_list = {}
	self.lua_file_to_bundle_map = {}
	self.need_update_bundles = {}

	self.is_req_new_version_ing = false
	self.req_new_version_times = 0

	self.is_donwloading_manifest = false
	self.downloading_manifest_times = 0

	self.is_donwloading_bundles = false
	self.downloading_bundles_times = 0

	self.cdn_url = ""
	self.cdn_url2 = ""

	self:RegisterProtocol(SCServerReloadConfig, "OnRecvHotupdateCmd")
	Runner.Instance:AddRunObj(self, 8)
end

function Hotupdate:__delete()
	Hotupdate.Instance = nil
	Runner.Instance:RemoveRunObj(self)
end

-- 收到服务器热更命令
function Hotupdate:OnRecvHotupdateCmd(protocol)
	if self.server_version == protocol.version then
		return
	end

	self.server_version = protocol.version
	self:StartHotUpdate()
end

function Hotupdate:StartHotUpdate()
	if self.process ~= HotupdateProcess.NONE then
		return
	end

	-- ReportManager:Step(Report.STEP_START_HOT_UPDATE)
	self.now_server_version = self.server_version
	self:ReCalcHotupdateList()
	self.process = HotupdateProcess.REQUIRE_NEW_VERSION
end

function Hotupdate:Update(now_time, elapse_time)
	if now_time < self.next_update_time then
		return
	end

	self.next_update_time = now_time + 0.2

	if self.process == HotupdateProcess.REQUIRE_NEW_VERSION then
		self:RequireNewVersion()
	elseif self.process == HotupdateProcess.DOWN_LOADING_MANIFEST then
		self:DownloadLuaManifest()
	elseif self.process == HotupdateProcess.DOWN_LOADING_BUNDLE then
		self:DownloadAssetBundles()
	elseif self.process == HotupdateProcess.DONE then
		self:FinishHotUpdate()
	end
end

-- 获得所有需要热更新的文件配置
function Hotupdate:ReCalcHotupdateList()
	self.hotupdate_list = {}
	local need_update_configs = require("manager/hotupdate_config")
	for k, file_name in ipairs(need_update_configs) do
		local full_name = GetLuaFileFullPath(file_name)
		self.hotupdate_list[full_name] = file_name
	end
end

-- 请求Query，拿到最新资源版本号
function Hotupdate:RequireNewVersion()
	if self.is_req_new_version_ing then
		return
	end

	self.req_new_version_times = self.req_new_version_times + 1
	if self.req_new_version_times > 10 then
		self:HotupdateFaild(HotupdateFailReason.REQUIRE_NEW_VERSION_FAIL)
		return
	end

	self.is_req_new_version_ing = true

	local url = InitWGCtrl.Instance:GetQueryUrl("fast")
	HttpClient:Request(url, function(url, is_succ, data)
		self.is_req_new_version_ing = false
		if not is_succ then
			return
		end

		if nil == data or data == "login block" then
			return
		end

		local data_info = cjson.decode(data)
		local init_info = data_info.data
		if init_info == nil or nil == init_info.version_info or nil == init_info.version_info.assets_info then
			return
		end

		local old_asset_version = self.old_asset_version ~= 0 and self.old_asset_version or ResMgr:GetAssetLuaVersion()
		local new_asset_verion = init_info.version_info.assets_info.version

		-- 内网包测试
		if IS_DEBUG_TEST then
			new_asset_verion = "" .. math.random(1, 999999)
		end

		-- 注意:发版之后，cdn_url会发生变化
		self.cdn_url = init_info.param_list.cdn_url
		self.cdn_url2 = init_info.param_list.cdn_url2

		if nil == old_asset_version or "" == old_asset_version
			or nil == new_asset_verion or "" == new_asset_verion then
			return
		end

		-- 这里把旧的asset_version存起来，只有当manifest文件成功下载，才更新old_asset_version
		self.old_asset_version = old_asset_version
		if old_asset_version ~= new_asset_verion then
			ResMgr:SetAssetLuaVersion(new_asset_verion)
			self.process = HotupdateProcess.DOWN_LOADING_MANIFEST
		else
			self.process = HotupdateProcess.DOWN_LOADING_BUNDLE
		end
	end)
end

-- 开始下载LuaMainifest
function Hotupdate:DownloadLuaManifest()
	if self.is_donwloading_manifest then
		return
	end

	self.downloading_manifest_times = self.downloading_manifest_times + 1
	if self.downloading_manifest_times > 10 then
		self:HotupdateFaild(HotupdateFailReason.DOWN_LOADING_MANIFEST_FAIL)
		return
	end

	self.is_donwloading_manifest = true

	-- 切换到最新的url
	ResMgr:SetRuntimeDownloadingURL(self.cdn_url)

	local old_luafile_to_bundle_map = self:CalcLuaFileToBundleMap()
	ResMgr:RuntimeLoadRemoteLuaManifest(function(error_msg, request)
			self.is_donwloading_manifest = false

			if error_msg ~= nil then
				-- 切换下载地址
				if self.downloading_manifest_times % 2 == 1 then
					if self.cdn_url2 and "" ~= self.cdn_url2 then
						ResMgr:SetRuntimeDownloadingURL(self.cdn_url2)
					end
				else
					ResMgr:SetRuntimeDownloadingURL(self.cdn_url)
				end
			else
				self.old_asset_version = ResMgr:GetAssetLuaVersion()
				self.lua_file_to_bundle_map = self:CalcLuaFileToBundleMap()
				self:CalcBundleChangedList(old_luafile_to_bundle_map, self.lua_file_to_bundle_map)
				self.process = HotupdateProcess.DOWN_LOADING_BUNDLE
			end
		end)
end

-- 计算lua文件与对应的AB包的映射
function Hotupdate:CalcLuaFileToBundleMap()
	local lua_file_to_bundle_map = {}
	local lua_bundles = ResMgr:GetAllLuaManifestBundles()
	for bundle_name, info in pairs(lua_bundles) do
		if string.match(bundle_name, LUA_ASSET_BUNDLE_PREFIX) then
			for _, v in pairs(info.deps) do
				if self.hotupdate_list[v] then
					local bundle_hash = ResMgr:GetLuaBundleHash(bundle_name)
					lua_file_to_bundle_map[self.hotupdate_list[v]] = {bundle_name = bundle_name, bundle_hash = bundle_hash}
				end
			end
		end
	end

	return lua_file_to_bundle_map
end

-- 计算变化的AB包
-- 这里容易进一个误区，认为触发下载的文件才是有变化的。
-- 实际上存在一种可能，文件在之前的版本已经下载过了。
function Hotupdate:CalcBundleChangedList(old_map, new_map)
	for file_name, bundle_info in pairs(new_map) do
		local old_info = old_map[file_name]
		if nil == old_info or old_info.bundle_name ~= bundle_info.bundle_name
			or old_info.bundle_hash ~= bundle_info.bundle_hash then
			self.bundle_changed_list[bundle_info.bundle_name] = true
		end
	end
end

-- 计算出所有需要下载的AB包
function Hotupdate:CalcNeedDownloadBundles()
	self.need_update_bundles = {}
	for bundle_name, _ in pairs(self.bundle_changed_list) do
		-- 因为底层会做缓存，所以这里清理一次
		ResUtil.ClearBundleFileCache("LuaAssetBundle/".. bundle_name)
		if not ResMgr:IsLuaVersionCached(bundle_name) then
			table.insert(self.need_update_bundles, bundle_name)
		end
	end
end

-- 开始下载AssetBundle
function Hotupdate:DownloadAssetBundles()
	if self.is_donwloading_bundles then
		return
	end

	self.downloading_bundles_times = self.downloading_bundles_times + 1
	if self.downloading_bundles_times > 10 then
		self:HotupdateFaild(HotupdateFailReason.DOWN_LOADING_BUNDLE_FAIL)
		return
	end

	self.is_donwloading_bundles = true

	self:CalcNeedDownloadBundles()

	-- 切换到最新的url
	ResMgr:SetRuntimeDownloadingURL(self.cdn_url)

	self:DoDownLoad(self.need_update_bundles, 1, function (is_succ)
		self.is_donwloading_bundles = false

		if is_succ then
			self.process = HotupdateProcess.DONE
		else
			-- 切换下载地址
			if self.downloading_bundles_times % 2 == 1 then
				if self.cdn_url2 and "" ~= self.cdn_url2 then
					ResMgr:SetRuntimeDownloadingURL(self.cdn_url2)
				end
			else
				ResMgr:SetRuntimeDownloadingURL(self.cdn_url)
			end
		end
	end, true)
end

local update_callback = function ()
end
function Hotupdate:DoDownLoad(update_bundles, index, complete_callback, is_succ)
	if index > #update_bundles then
		complete_callback(is_succ)
		return
	end

	local bundle = update_bundles[index]
	ResMgr:RuntimeUpdateBundle(bundle, update_callback, function (error_msg)
		if error_msg ~= nil and error_msg ~= "" then
			is_succ = false
		end

		self:DoDownLoad(update_bundles, index + 1, complete_callback, is_succ)
	end)
end

-- 热更完成，通知外部
function Hotupdate:FinishHotUpdate()
	self:ResetState()

	-- 如果热更的过程中，服务器版本号再次变化，则重新启动检查
	if self.now_server_version ~= self.server_version then
		self:StartHotUpdate()
		return
	end

	-- 如果没有bundle变化，则忽略本次热更
	if nil == next(self.bundle_changed_list) then
		return
	end

	if not self:CheckIsSafeUpdate() then
		return
	end

	local changed_list = {}
	for file_name, bundle_info in pairs(self.lua_file_to_bundle_map) do
		local bundle_name = bundle_info.bundle_name
		if self.bundle_changed_list[bundle_name] then
			local bundle_hash = ResMgr:GetLuaBundleHash(bundle_name)
			local file_path = ResUtil.GetBundleFilePath("LuaAssetBundle/".. bundle_name, bundle_hash)
			-- 更新lua_look_up
			GameRoot.Instance:OverrideLuaBundle(file_name, file_path)
			table.insert(changed_list, file_name)
		end
	end

	self.bundle_changed_list = {}

	-- ReportManager:Step(Report.STEP_START_HOT_UPDATE_SUCC)
	GlobalEventSystem:Fire(HotUpdateEvent.HOT_UPDATE_FINISH, changed_list)
end

function Hotupdate:CheckIsSafeUpdate()
	for _, file_name in pairs(self.hotupdate_list) do
		if not self.lua_file_to_bundle_map[file_name] then
			print_error("[Hotupdate] Big Bug!!! Lua文件找不到对应的AB包", file_name)
			self:HotupdateFaild(HotupdateFailReason.LUA_FILE_NOT_MATCH_BUNDLE)
			return false
		end
	end

	return true
end

-- 重置状态机
function Hotupdate:ResetState()
	self.process = HotupdateProcess.NONE

	self.is_req_new_version_ing = false
	self.req_new_version_times = 0

	self.is_donwloading_manifest = false
	self.downloading_manifest_times = 0

	self.is_donwloading_bundles = false
	self.downloading_bundles_times = 0

	IS_DEBUG_TEST = false
end

function Hotupdate:HotupdateFaild(reason)
	self:ResetState()

	if reason == HotupdateFailReason.REQUIRE_NEW_VERSION_FAIL then
		-- ReportManager:Step(Report.STEP_HOT_UPDATE_REQUIRE_FAIL)
	elseif reason == HotupdateFailReason.DOWN_LOADING_MANIFEST_FAIL then
		-- ReportManager:Step(Report.STEP_HOT_UPDATE_DOWNLOADING_MANIFEST_FAIL)
	elseif reason == HotupdateFailReason.DOWN_LOADING_BUNDLE_FAIL then
		-- ReportManager:Step(Report.STEP_HOT_UPDATE_DOWNLOADING_BUNDLE_FAIL)
	elseif reason == HotupdateFailReason.LUA_FILE_NOT_MATCH_BUNDLE then
		-- ReportManager:Step(Report.STEP_HOT_UPDATE_LUA_FILE_NOT_MATCH_BUNDLE)
	end

	-- 如果热更的过程中，服务器版本号再次变化，则重新启动检查
	if self.now_server_version ~= self.server_version then
		self:StartHotUpdate()
	end
end

-- 热更新所有文件（只作测试用）
function Hotupdate:GMHotupdate(is_debug)
	IS_DEBUG_TEST = is_debug or false
	self:OnRecvHotupdateCmd({version = math.random(1, 100000)})
end