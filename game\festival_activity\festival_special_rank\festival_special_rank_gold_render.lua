FestivalSPRankGoldRender = FestivalSPRankGoldRender or BaseClass(BaseRender)

function FestivalSPRankGoldRender:LoadCallBack()
    self.item_list = AsyncListView.New(FestivalSPRankItem, self.node_list.item_list)
    self.item_list:SetIsDelayFlush(false)
    self.is_load_complete = true

	self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.item_list)
	if self.parent_scroll_rect then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end

    self.node_list["sp_gold_bg"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_lb"))
    self.node_list["sp_desc_bg"].image:LoadSprite(ResPath.GetFestivalActImages("a2_jrkh_lbbt"))
end
function FestivalSPRankGoldRender:__delete()
    if self.nested_scroll_rect then
		self.nested_scroll_rect:DeleteMe()
		self.nested_scroll_rect = nil
	end
    self.is_load_complete = nil
    self.parent_scroll_rect = nil

    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end
function FestivalSPRankGoldRender:OnFlush()
    if not self.data then
        return
    end

    --奖励格子列表
    local item_data = SortDataByItemColor(self.data.item_list)
    self.item_list:SetDataList(item_data)

    self.node_list.rank_value.text.text = string.format(
        Language.FestivalSPRank.RankReawrdDesc, 
        self.data.rank_hight, 
        self.data.rank_low, 
        self.data.reach_value)
end

function FestivalSPRankGoldRender:SetParentScrollRect(scroll_rect)
	self.parent_scroll_rect = scroll_rect

	if self.is_load_complete then
		self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
	end
end