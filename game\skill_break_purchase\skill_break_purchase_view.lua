-- 技能突破直购
SkillBreakPurchaseView = SkillBreakPurchaseView or BaseClass(SafeBaseView)

local VideoPlayer = typeof(UnityEngine.Video.VideoPlayer)

-- local PROF_IMAGE_NAME = {
-- 	[0] = {
-- 		[1] = "a3_jntp_jbck1",
-- 		[2] = "a3_jntp_yjyl",
-- 		[3] = "a3_jntp_wdwj",
-- 		[4] = ""
-- 	},
-- 	[1] = {
-- 		[1] = "a3_jntp_jbck",
-- 		[2] = "",
-- 		[3] = "a3_jntp_dfjy",
-- 		[4] = ""
-- 	},
-- }

function SkillBreakPurchaseView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
	self:SetMaskBg()
	self.is_safe_area_adapter = true
	-- self.default_index = TabIndex.skill_break_purchase

	local bundle = "uis/view/skill_break_purchase_ui_prefab"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
	self:AddViewResource(0, bundle, "layout_skill_break_purchase")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_top_panel")

	self.is_discount = false
end

function SkillBreakPurchaseView:__delete()
end

function SkillBreakPurchaseView:OpenCallBack()
	SkillBreakPurchaseWGCtrl.Instance:RequestInfo()
end

function SkillBreakPurchaseView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.SkillBreakPurchase.Title

	local bundle, assert = ResPath.GetRawImagesJPG("a3_qjjx_bj")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function ()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	self:LoadPurchaseViewCallback()
end

function SkillBreakPurchaseView:ShowIndexCallBack(index)
end

function SkillBreakPurchaseView:ReleaseCallBack()
	self.select_skill_index = nil
    self.is_playing = nil
    self.video_clips = nil
	self.skill_data_list = nil
	
	self:CleanTimer()
	self:ClearTweener()

	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.reward_show_list then
		self.reward_show_list:DeleteMe()
		self.reward_show_list = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	

	if self.skill_pre_item then
		self.skill_pre_item:DeleteMe()
		self.skill_pre_item = nil
	end

	if self.skill_item_list then
		for i, v in ipairs(self.skill_item_list) do
			v:DeleteMe()
		end
		self.skill_item_list = nil
	end

	self.is_discount = nil
	self.is_on_desc = nil
end

function SkillBreakPurchaseView:LoadPurchaseViewCallback()

	self.is_on_desc = false

	self.node_list.txt_show_desc.text.text = Language.SkillBreakPurchase.ShowDesc

	XUI.AddClickEventListener(self.node_list["btn_purchase"], BindTool.Bind(self.OnClickPurchaseBtn, self))
	XUI.AddClickEventListener(self.node_list["skill_layer"], BindTool.Bind(self.OnClickSkillPreviewBtn, self))
	XUI.AddClickEventListener(self.node_list["rect_desc"], BindTool.Bind(self.OnClickRectDesc, self))
	
	if not self.reward_show_list then
		self.reward_show_list = AsyncListView.New(ItemCell, self.node_list.reward_list_root)
		self.reward_show_list:SetStartZeroIndex(true)
	end

	-- 模型
	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local model_config = SkillBreakPurchaseWGData.Instance:GetModelShowConfig(sex, prof)
	if not self.model_display then
		self.model_display = OperationActRender.New(self.node_list["model_root"])
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
        self:FlushModel(model_config)
	end

	-- 职业标题图
	-- local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	-- local image_name = PROF_IMAGE_NAME[sex][prof]
	-- if string.len(image_name) > 0 then
	-- 	local bundle, asset = ResPath.GetRawImagesPNG(image_name)
	-- 	self.node_list.rawimg_prof_title.raw_image:LoadSprite(bundle, asset, function()
	-- 		self.node_list.rawimg_prof_title.raw_image:SetNativeSize()
	-- 	end)
	-- end
	
	-- 突破技能
	local skill_break_cfg_list = SkillWGData.Instance:GetSkillBreakCfgList()
	self.skill_item_list = {}
	for i = 1, 4 do
		local skill_item = SkillBreakItemRender.New(self.node_list["skill_item_" .. i])
		skill_item:SetData(skill_break_cfg_list[i])
		table.insert(self.skill_item_list, skill_item)
	end

	self.skill_pre_item = SkillBreakItemRender.New(self.node_list.skill_pre_item)


	self.select_skill_index = 1
	self.video_player = self.node_list.skill_pre_rawimage:GetComponent(VideoPlayer) 
	Runner.Instance:AddRunObj(self, 14)
	
end

function SkillBreakPurchaseView:OnFlush(param_t, index)
	-- if index == TabIndex.skill_break_purchase then
		-- self.skill_data_list = SkillWGData.Instance:GetSkillListByType(1)   -- 主动技能列表
		local is_purchase = SkillBreakPurchaseWGCtrl.Instance:CheckIsPurchase()
		self.node_list.img_buy:SetActive(is_purchase)
		self.node_list.btn_purchase:SetActive(not is_purchase)

		self.skill_data_list = SkillWGData.Instance:GetSkillBreakCfgList()

		local shop_cfg = SkillBreakPurchaseWGData.Instance:GetCurrentShopConfig()
		self.node_list.lbl_rmb_prise.text.text = string.format(Language.SkillBreakPurchase.ShowPrice, self.is_discount and shop_cfg.discount_price or shop_cfg.price)
		
		self.node_list.lbl_original_price.text.text = string.format(Language.SkillBreakPurchase.OriginalPriceDesc, shop_cfg.price)
		self.node_list.lbl_discount.text.text = string.format(Language.SkillBreakPurchase.ShowDiscount, shop_cfg.show_discount)

		if self.reward_show_list then
			self.reward_show_list:SetDataList(shop_cfg.reward_item)
		end

		-- 折扣剩余时间
		local end_time_stamp = SkillBreakPurchaseWGData.Instance:GetDiscountEndTime()
		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local second = end_time_stamp - server_time
		self.is_discount = second > 0
		if self.is_discount then
			self:CleanTimer()
			self:SetDiscountTimeShow(second)
			self.discount_timer = GlobalTimerQuest:AddTimesTimer(function ()
				second = second - 1
				self:SetDiscountTimeShow(second)
			end, 1, second)
		end

		local info = SkillBreakPurchaseWGData.Instance:GetPurchaseInfo()
		local is_buy = info.reward_flat == 1
		XUI.SetButtonEnabled(self.node_list.btn_purchase, not is_buy)
		self.node_list.lbl_discount_time:SetActive(self.is_discount)
		-- self.node_list.lbl_original_price:SetActive(self.is_discount)
		self.node_list.img_discount:SetActive(self.is_discount)

		self:FlushCurrSkillVideo()
	-- end
end

function SkillBreakPurchaseView:CleanTimer()
	if self.discount_timer then
        GlobalTimerQuest:CancelQuest(self.discount_timer)
        self.discount_timer = nil
    end
end

function SkillBreakPurchaseView:SetDiscountTimeShow(second)
	local time_str = TimeUtil.FormatSecondDHM8(second)
	self.node_list.lbl_discount_time.text.text = string.format(Language.SkillBreakPurchase.CountDown, time_str)
end


function SkillBreakPurchaseView:FlushModel(model_cfg)
	if IsEmptyTable(model_cfg) then
		return
	end

    local display_data = {}
	local model_show_type = tonumber(model_cfg["model_show_type"]) or 1
	if model_cfg["model_show_itemid"] ~= 0 and model_cfg["model_show_itemid"] ~= "" then
		local split_list = string.split(model_cfg["model_show_itemid"], "|")
		if #split_list > 1 then
			local list = {}
			for k, v in pairs(split_list) do
				list[tonumber(v)] = true
			end
			display_data.model_item_id_list = list
		else
			display_data.item_id = model_cfg["model_show_itemid"]
		end
	end

	display_data.should_ani = true
	display_data.bundle_name = model_cfg["model_bundle_name"]
	display_data.asset_name = model_cfg["model_asset_name"]
	display_data.render_type = model_show_type - 1
	-- display_data.model_click_func = function ()
	-- 	TipWGCtrl.Instance:OpenItem({item_id = model_cfg["model_show_itemid"]})
	-- end
	if model_cfg.model_bundle_name and model_cfg.model_bundle_name ~= ""
			and model_cfg.model_asset_name and model_cfg.model_asset_name ~= "" then

		display_data.model_rt_type = ModelRTSCaleType.M
		-- 模型位置
		if model_cfg.model_pos and model_cfg.model_pos ~= "" then
			local pos_list = string.split(model_cfg.model_pos, "|")
			local pos_x = tonumber(pos_list[1]) or 0
			local pos_y = tonumber(pos_list[2]) or 0
			local pos_z = tonumber(pos_list[3]) or 0
	
			display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
		end
		-- 模型旋转角度
		if model_cfg.model_rot and model_cfg.model_rot ~= "" then
            local rot_list = string.split(model_cfg.model_rot, "|")
            local rot_x = tonumber(rot_list[1]) or 0
            local rot_y = tonumber(rot_list[2]) or 0
            local rot_z = tonumber(rot_list[3]) or 0

            display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
        end
		-- 模型尺寸
		if model_cfg.model_scale then
			display_data.model_adjust_root_local_scale = model_cfg.model_scale
		end

		
	end
	display_data.can_drag = false
	self.model_display:SetData(display_data)

	local pos_x, pos_y = 0, 0
	if model_cfg.display_pos and model_cfg.display_pos ~= "" then
		local pos_list = string.split(model_cfg.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.model_root.rect, pos_x, pos_y)

	if model_cfg["display_scale"] then
		local scale = model_cfg["display_scale"]
		Transform.SetLocalScaleXYZ(self.node_list.model_root.transform, scale, scale, scale)
	end

	if model_cfg.rotation and model_cfg.rotation ~= "" then
		local rotation_tab = string.split(model_cfg.rotation,"|")
		self.node_list.model_root.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
	end
end

-- 购买
function SkillBreakPurchaseView:OnClickPurchaseBtn()
	local is_purchase = SkillBreakPurchaseWGCtrl.Instance:CheckIsPurchase()
	if is_purchase then
		return
	end

	local shop_cfg = SkillBreakPurchaseWGData.Instance:GetCurrentShopConfig()
	local cur_price = self.is_discount and shop_cfg.discount_price or shop_cfg.price
	RechargeWGCtrl.Instance:Recharge(cur_price, shop_cfg.rmb_type, shop_cfg.rmb_seq)
end

function SkillBreakPurchaseView:OnClickSkillPreviewBtn()
	-- local skill_break_cfg_list = SkillWGData.Instance:GetSkillBreakCfgList(true)
	-- CommonSkillShowCtrl.Instance:SetBreakSkillViewDataAndOpen(skill_break_cfg_list)

	SkillBreakPurchaseWGCtrl.Instance:OpenRoleSkillPreView(self.select_skill_index)
end

function SkillBreakPurchaseView:OnClickRectDesc()
	self.is_on_desc = not self.is_on_desc

	if self.is_on_desc then
		local text = self.node_list["txt_show_desc"].text
		local bounds = TMPUtil.GetTMPBounds(text, text.text, 436)
		self:SetItemScale(bounds.y, 270)
	else
		self:SetItemScale(24, 90)
	end
end

function SkillBreakPurchaseView:SetItemScale(height, angle)
	self:ClearTweener()
	local rect = self.node_list["rect_desc"].rect
	self.size_change_tweener = rect:DOSizeDelta(Vector2(436, height), 0.5)

	local arrow_rect = self.node_list["img_arrow_desc"].rect
	self.rotation_change_tweener = arrow_rect:DORotate(Vector3(0, 0, angle), 0.5)
	
end

function SkillBreakPurchaseView:ClearTweener()
    if self.size_change_tweener then
		self.size_change_tweener:Kill()
		self.size_change_timer = nil
    end

	if self.rotation_change_tweener then
		self.rotation_change_tweener:Kill()
		self.rotation_change_tweener = nil
    end
end

-- 更新
function SkillBreakPurchaseView:Update(now_time, elapse_time)
    if IsNil(self.video_player) or (not self.video_player.isPlaying) then
        return
    end

    self:FlushCurrPlayVideoProgress()
end

-- 更新进度
function SkillBreakPurchaseView:FlushCurrPlayVideoProgress()
    if IsNil(self.video_player) then
        return
    end
    local curr_time = self.video_player.time
    local total_time = self.video_player.clip.length
    local progress = curr_time / total_time
    if progress >= 0.97 then
		-- 下一条视频
		if self.select_skill_index >= #self.skill_data_list then
			self.select_skill_index = 1
		else
			self.select_skill_index = self.select_skill_index + 1
		end
		self:FlushCurrSkillVideo()
	end
end

-- 刷新视频
function SkillBreakPurchaseView:FlushCurrSkillVideo()
    if (self.select_skill_index == nil) or (self.skill_data_list == nil) then
        return
    end

    -- 准备更换视频
    if IsNil(self.video_player) then
        return
    end

    local play_video = function()
        if not IsNil(self.video_player) then
			-- 这里切换clip
			self.video_player.time = 0
			self.video_player:Play()
			self.is_playing = true
        end
    end

    if self.video_clips == nil then
        self.video_clips = {}
    end

	local skill_data = self.skill_data_list[self.select_skill_index]
	if not skill_data then
		return
	end

	self.skill_pre_item:SetData(skill_data)

    if self.video_clips[skill_data.skill_id] then
        self.video_player.clip = self.video_clips[skill_data.skill_id]
        play_video()
    else
        local sex, prof = RoleWGData.Instance:GetRoleSexProf()
        local role_id = RoleWGData.Instance.GetJobModelId(sex, prof)
        local bundle, asset = ResPath.GetSkillBreakPurchaseSkillVideoPath(role_id, skill_data.skill_id)

        ResPoolMgr:GetVideoClip(bundle, asset, function (asset)
			if asset ~= nil then
				if self.video_clips and skill_data ~= nil then
					self.video_clips[skill_data.skill_id] = asset
				end

				if not IsNil(self.video_player) then
					self.video_player.clip = asset
					play_video()
				end
			end
        end)
    end
end


------------------------------------------
-- 技能图标item
------------------------------------------
SkillBreakItemRender = SkillBreakItemRender or BaseClass(BaseRender)

function SkillBreakItemRender:__init()
	XUI.AddClickEventListener(self.node_list.img_skill_icon, BindTool.Bind(self.OnClick, self))
end

function SkillBreakItemRender:OnFlush()
	if self.data == nil then
		return
	end

	local bundle, asset = ResPath.GetSkillIconById(self.data.skill_icon_id)
	self.node_list.img_skill_icon.image:LoadSprite(bundle, asset)
	if self.node_list.lbl_skill_name then
		self.node_list.lbl_skill_name.text.text = self.data.skill_name
	end
end

function SkillBreakItemRender:OnClick()
	if self.data == nil then
		return
	end

	local desc = SkillWGData.Instance:GetSkillBreakDesc( self.data.skill_id,  self.data.desc, false)

	local show_data = {
		icon = self.data.skill_icon_id,
		top_text = self.data.skill_name,					-- 技能名
		body_text = desc,							-- 当前等级技能描述
		x = 0,
		y = 0,
		set_pos = true,
		is_active_skill = true,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end