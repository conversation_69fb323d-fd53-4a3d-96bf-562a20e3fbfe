FairyLandPlaneView = FairyLandPlaneView or BaseClass(SafeBaseView)

local PLANE_POS_LIST = { [0] = 0, 76, -86, 1 }

function FairyLandPlaneView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.is_safe_area_adapter = true

	local bundle = "uis/view/fairy_land_equipment_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, bundle, "layout_fairy_land_plane_view")
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")

    local data = {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.LINGYU}
	self:SetTabShowUIScene(0, data)
end

function FairyLandPlaneView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FairyLandEquipment.ViewTitle1

	if not self.plane_list then
        self.plane_list = AsyncListView.New(FairyLandPlaneItemRender, self.node_list.plane_list)
		self.plane_list:SetStartZeroIndex(true)
        self.plane_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectPlaneCell, self))
    end

    if not self.grid_scroller_fun then
		self.grid_scroller_fun = BindTool.Bind(self.ShopScrollerEndScrolled, self)
		self.node_list.plane_list.scroller.scrollerEndScrolled = self.grid_scroller_fun
	end

    XUI.AddClickEventListener(self.node_list.btn_open_equipment_view, BindTool.Bind(self.OnClickOpenEquipmentViewBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_quick_entry, BindTool.Bind(self.OnClickQuickEntryBtn, self))
end

function FairyLandPlaneView:ShopScrollerEndScrolled()
	local val = self.node_list.plane_list.scroll_rect.horizontalNormalizedPosition
	local cell_row = self.plane_list:GetListViewNumbers()
	self.node_list.right_arrow:SetActive(cell_row > 4 and val < 0.9)
    self.node_list.left_arrow:SetActive(cell_row > 4 and val > 0.1)
end

function FairyLandPlaneView:OnSelectPlaneCell(cell, cell_index, is_default, is_click)
    if (not cell) or (not cell.data) then
		return
	end

    local data = cell.data

	if is_click then
        local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
        local flag = PlayerPrefsUtil.GetInt("FairyLandPlaneQuickEntryFlag" .. main_role_id)
        local max_show_num = FairyLandEquipmentWGData.Instance:GetGodBodyMaxShowNum()
        local is_act = data.slot <= max_show_num
        if flag and flag == 1 and is_act then
            local bundle_name, asset_name = ResPath.GetEffectUi("UI_yuanshen_cs")
            self.node_list.block:SetActive(true)
            self.node_list.effect_pos:SetActive(true)
            EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos"].transform, 1.7, nil, nil, nil, nil, function ()
                self.node_list.block:SetActive(false)
                self.node_list.effect_pos:SetActive(false)
                FairyLandEquipmentWGCtrl.Instance:OpenRuleViewAndSetData(data.slot)
            end)
        else
            FairyLandEquipmentWGCtrl.Instance:OpenPlaneDetailViewAndSetData(data.slot)
        end
	end
end

function FairyLandPlaneView:ReleaseCallBack()
    if self.plane_list then
        self.plane_list:DeleteMe()
        self.plane_list = nil
    end

    self.grid_scroller_fun = nil
end

function FairyLandPlaneView:__delete()
    
end

function FairyLandPlaneView:OpenCallBack()

end

function FairyLandPlaneView:ShowIndexCallBack()
    self.is_play_effect = true
end

function FairyLandPlaneView:CloseCallBack()

end

function FairyLandPlaneView:OnFlush(param_t, index)
	local data_list = FairyLandEquipmentWGData.Instance:GetGodBodyAllCfg()
	self.plane_list:SetDataList(data_list)

    if self.is_play_effect then
        self.is_play_effect = false
        self.node_list.plane_list:SetActive(false)
        ReDelayCall(self, function()
            self.node_list.plane_list:SetActive(true)
            local list = self.plane_list:GetAllItems()
            for k, v in pairs(list) do
                v:PlayEffect()
            end
        end, 0.1, "FairyLandPlaneView")
    end

    local jieling_remind = FairyLandEquipmentWGData.Instance:GetJieLingRemind()
    self.node_list.btn_open_equipment_remind:SetActive(jieling_remind)

    local max_show_num = FairyLandEquipmentWGData.Instance:GetGodBodyMaxShowNum1()
    self.node_list.btn_open_equipment_view:SetActive(max_show_num >= 0)

    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
    local flag = PlayerPrefsUtil.GetInt("FairyLandPlaneQuickEntryFlag" .. main_role_id)
    self.node_list.quick_entry_select_flag:SetActive(flag == 1)
end

function FairyLandPlaneView:OnClickOpenEquipmentViewBtn()
    FairyLandEquipmentWGCtrl.Instance:OpenEquipmentView()
end

function FairyLandPlaneView:OnClickQuickEntryBtn()
    local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
    local flag = PlayerPrefsUtil.GetInt("FairyLandPlaneQuickEntryFlag" .. main_role_id)

    if flag and flag == 1 then
        self.node_list.quick_entry_select_flag:SetActive(false)
        PlayerPrefsUtil.SetInt("FairyLandPlaneQuickEntryFlag" .. main_role_id, 0)
    else
        self.node_list.quick_entry_select_flag:SetActive(true)
        PlayerPrefsUtil.SetInt("FairyLandPlaneQuickEntryFlag" .. main_role_id, 1)
    end
end
----------------------------------item-----------------------
FairyLandPlaneItemRender = FairyLandPlaneItemRender or BaseClass(BaseRender)
function FairyLandPlaneItemRender:__init()

end

function FairyLandPlaneItemRender:LoadCallBack()
    --self:PlayEffect()
end

function FairyLandPlaneItemRender:ReleaseCallBack()

end

function FairyLandPlaneItemRender:PlayEffect()
    local bundle_name, asset_name = ResPath.GetEffectUi("UI_yuanshen_csm_cx")
    self.node_list.content2:SetActive(false)
    self.node_list.effect_pos:SetActive(true)
    EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list["effect_pos"].transform, 0.5, nil, nil, nil, nil, function ()
        --self.node_list.effect:SetActive(false)
        self.node_list.effect_pos:SetActive(false)
        self.node_list.content2:SetActive(true)
    end)
end

function FairyLandPlaneItemRender:OnFlush()
    if not self.data then return end

    local pos_index = self.index % 4
    RectTransform.SetAnchoredPositionXY(self.node_list.content.rect, self.node_list.content.rect.anchoredPosition.x, PLANE_POS_LIST[pos_index])
    local book_remind = FairyLandEquipmentWGData.Instance:GeSlotGodBookRemind(self.data.slot)
    self.node_list.remind:SetActive(book_remind)

    local bundle, asset = ResPath.GetRawImagesPNG(self.data.bg_img)
    self.node_list.bg.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["bg"].raw_image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetFairyLandEquipImages(self.data.name_img)
    self.node_list.name_img.image:LoadSprite(bundle, asset, function ()
        self.node_list["name_img"].image:SetNativeSize()
    end)

    bundle, asset = ResPath.GetFairyLandEquipImages(self.data.name_img .. "2")
    self.node_list.lock_name_img.image:LoadSprite(bundle, asset, function ()
        self.node_list["lock_name_img"].image:SetNativeSize()
    end)

    local max_show_num = FairyLandEquipmentWGData.Instance:GetGodBodyMaxShowNum()
    local is_lock = self.data.slot > max_show_num
    self.node_list.lock_content:SetActive(is_lock)
    self.node_list.unlock_desc:SetActive(not is_lock)
end