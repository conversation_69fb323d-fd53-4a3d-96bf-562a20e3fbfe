
GuildGridRender = GuildGridRender or BaseClass(BaseRender)
function GuildGridRender:__init(instance, bundle, asset)
	self.grid_call_back = nil						-- grid回调函数
	self.select_call_back = nil
	self.parent = nil
	self.columns = nil
	self.rows = nil
	self.select_tab = {}
end

function GuildGridRender:__delete()
	
end

function GuildGridRender:SetColumns(columns)
	self.columns = columns
end

function GuildGridRender:SetRows(rows)
	self.rows = rows
end

function GuildGridRender:GetRow(rows)
	return self.rows
end
-- 选择表
function GuildGridRender:SetSelectTab(select_tab)
	self.select_tab = select_tab
end

function GuildGridRender:IsSelect(index)
	return self.select_tab[self.rows][index] or false
end

function GuildGridRender:SetGridData(data_tab)
	self.data_list = data_tab
end

-- 回调函数（scorll_grid）
function GuildGridRender:SetGridCallBack(callback)
	self.grid_call_back = callback
end

-- 回调返回下标
function GuildGridRender:GridItemOnCLick(index)
	local item_index = index + (self.rows - 1) * self.columns
	if nil ~= self.grid_call_back then
		self.grid_call_back(item_index)
	end
	if nil ~= self.select_call_back then
		self.select_call_back(self.rows, index)
	end
end

-- 回调函数（用于特效）
function GuildGridRender:SetSelectEffectCallBack(callback)
	self.select_call_back = callback
end

function GuildGridRender:CreateItemCells()
	for i = 1, self.columns do
		if self.node_list["ItemRender" .. i] and next(self.node_list["ItemRender" .. i]) then
			self.node_list["ItemRender" .. i].button:AddClickListener(BindTool.Bind(self.GridItemOnCLick, self, i))
		end
	end
end

--------- 重写清空数据
function GuildGridRender:ClearAllDatas()
	
end
