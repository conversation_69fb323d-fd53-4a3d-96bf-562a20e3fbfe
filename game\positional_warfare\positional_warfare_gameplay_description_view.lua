-- 玩法说明界面

PositionalWarfareGamePlayDescriptionView = PositionalWarfareGamePlayDescriptionView or BaseClass(SafeBaseView)

function PositionalWarfareGamePlayDescriptionView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_gameplay_description_view")
end

function PositionalWarfareGamePlayDescriptionView:OpenCallBack()
    PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.CAMP_INFO)
    -- GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_INFO, 22939004)--RoleWGData.Instance.role_vo.guild_id)
    GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.ALL_GUILD_BASE_INFO, RoleWGData.Instance.role_vo.guild_id)
end

function PositionalWarfareGamePlayDescriptionView:LoadCallBack()
    if not self.camp_description_list then
        self.camp_description_list = AsyncListView.New(PWCampDescriptionCellItemRender, self.node_list.camp_description_list)
        self.camp_description_list:SetStartZeroIndex(false)
    end

    self:SetPWMSgInfo()
end

function PositionalWarfareGamePlayDescriptionView:SetPWMSgInfo()
    self.node_list.desc_gameplay_description.text.text = Language.PositionalWarfare.GameplayDescription
    self.node_list.title_view_name.text.text = Language.PositionalWarfare.GameplayDescriptionView
    self.node_list.gameplay_desc.text.text = Language.PositionalWarfare.GameplayDescriptionBomDesc

    self.node_list.desc_title1.text.text = Language.PositionalWarfare.GPDescTitle1
    self.node_list.desc_title2.text.text = Language.PositionalWarfare.GPDescTitle2
    self.node_list.desc_title3.text.text = Language.PositionalWarfare.GPDescTitle3

    self.node_list.desc_title_content1.text.text = Language.PositionalWarfare.GPDescTitleContent1
    self.node_list.desc_title_content2.text.text = Language.PositionalWarfare.GPDescTitleContent2
    self.node_list.desc_title_content3.text.text = Language.PositionalWarfare.GPDescTitleContent3

    self.node_list.desc_content1.text.text = Language.PositionalWarfare.GPDescContent1
    self.node_list.desc_content2.text.text = Language.PositionalWarfare.GPDescContent2
    self.node_list.desc_content3.text.text = Language.PositionalWarfare.GPDescContent3

    if not self.boss_info_list then
        self.boss_info_list = {}
        local des_cfg = PositionalWarfareWGData.Instance:GetGamePlayDesCfg()

        for i = 1, 3 do
            self.boss_info_list[i] = PWCampDescriptionBossInfoCellRender.New(self.node_list["boss_info" .. i])
            self.boss_info_list[i]:SetData(des_cfg[i])
        end
    end
end

function PositionalWarfareGamePlayDescriptionView:ReleaseCallBack()
    if self.camp_description_list then
        self.camp_description_list:DeleteMe()
        self.camp_description_list = nil
    end

    if self.boss_info_list then
        for k, v in pairs(self.boss_info_list) do
            v:DeleteMe()
        end

        self.boss_info_list = nil
    end
end

function PositionalWarfareGamePlayDescriptionView:OnFlush()
    local camp_data_list = PositionalWarfareWGData.Instance:GetCampInfoDataList()

    if IsEmptyTable(camp_data_list) then
        self.node_list.camp_description_list:CustomSetActive(false)
        self.node_list.no_camp_data:CustomSetActive(true)
    else
        local camp_list = {}
        for k, v in pairs(camp_data_list) do
            camp_list[v.camp] = camp_list[v.camp] or {camp = v.camp}
            camp_list[v.camp].camp_list = camp_list[v.camp].camp_list or {}
            table.insert(camp_list[v.camp].camp_list, v)
        end

        local target_data_list = {}
        for k, v in pairs(camp_list) do
            table.insert(target_data_list, v)
        end

        self.camp_description_list:SetDataList(target_data_list)
        self.node_list.camp_description_list:CustomSetActive(true)
        self.node_list.no_camp_data:CustomSetActive(false)
    end
end

-------------------------------------------PWCampDescriptionCellItemRender--------------------------------------
PWCampDescriptionCellItemRender = PWCampDescriptionCellItemRender or BaseClass(BaseRender)

function PWCampDescriptionCellItemRender:LoadCallBack()
    if not self.camp_list then
        self.camp_list = AsyncListView.New(PWCampDescriptionServerCellItemRender, self.node_list.camp_list)
    end
end

function PWCampDescriptionCellItemRender:__delete()
    if self.camp_list then
        self.camp_list:DeleteMe()
        self.camp_list = nil
    end
end

function PWCampDescriptionCellItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local group_index = PositionalWarfareWGData.Instance:GetMyGroupIndex()
    local camp_cfg = PositionalWarfareWGData.Instance:GetCampCfg(group_index, self.data.camp)

    if not IsEmptyTable(camp_cfg) then
        local sign_bundle, sign_asset = ResPath.GetPositionalWarfareImg("a3_zdz_tt_" .. camp_cfg.camp_sign)
        self.node_list.camp_icon.image:LoadSprite(sign_bundle, sign_asset, function()
            self.node_list.camp_icon.image:SetNativeSize()
        end)

        self.node_list.camp_name.text.text = camp_cfg.camp_name or ""
    end

    self.camp_list:SetDataList(self.data.camp_list)
end

-------------------------------------------PWCampDescriptionServerCellItemRender--------------------------------------
PWCampDescriptionServerCellItemRender = PWCampDescriptionServerCellItemRender or BaseClass(BaseRender)

function PWCampDescriptionServerCellItemRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local group_index = PositionalWarfareWGData.Instance:GetMyGroupIndex()

    if group_index > 0 then
        local server_id = RoleWGData.Instance:GetCurServerId()
        local name_str = string.format(Language.PositionalWarfare.ServerName, self.index, self.data.param.temp_low)
        self.node_list.name.text.text = server_id == self.data.param.temp_low and ToColorStr(name_str, COLOR3B.GREEN) or name_str
    else
        local guild_data_list = GuildWGData.Instance:GetGuildListData()
        local guild_data = {}

        for k, v in pairs(guild_data_list) do
            if v.guild_id == self.data.guild_id then
                guild_data = v
                break
            end
        end

        if not IsEmptyTable(guild_data) then
            local guild_id = RoleWGData.Instance:GetAttr("guild_id")
            local name_str = string.format("%s.%s", self.index, guild_data.guild_name)
            self.node_list.name.text.text = (guild_id == guild_data.guild_id) and ToColorStr(name_str, COLOR3B.GREEN) or name_str
        end
    end
end

-------------------------------------------PWCampDescriptionBossInfoCellRender--------------------------------------
PWCampDescriptionBossInfoCellRender = PWCampDescriptionBossInfoCellRender or BaseClass(BaseRender)

function PWCampDescriptionBossInfoCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    for i = 1, 3 do
        local data = self.data[i]

        local icon_bundle, icon_asset = ResPath.GetBossIcon("wrod_boss_".. data.icon)
        self.node_list["boss_icon" .. i].image:LoadSprite(icon_bundle, icon_asset,function ()
            self.node_list["boss_icon" .. i].image:SetNativeSize()
        end)

        self.node_list["boss_sign" ..i].text.text = data.name
        self.node_list["boss_num" ..i].text.text = "x" .. data.num
        self.node_list["boss_score" ..i].text.text = string.format(Language.PositionalWarfare.GPDescBossScore, data.kill_capture_score)
    end
end