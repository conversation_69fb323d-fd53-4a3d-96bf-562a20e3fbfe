DailyDaojuFbSceneLogic = DailyDaojuFbSceneLogic or BaseClass(CommonFbLogic)

function DailyDaojuFbSceneLogic:__init()
	
end

function DailyDaojuFbSceneLogic:__delete()
	
end

function DailyDaojuFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)	
	FuBenWGCtrl.Instance:OpenTaskFollow()
	FuBenWGCtrl.Instance:OpenTowerDownView()
	
	-- XuiBaseView.CloseAllView()
end

function DailyDaojuFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

function DailyDaojuFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	FuBenWGCtrl.Instance:CloseTowerDownView()
	FuBenWGData.Instance:SetTeamTowerAutoRefresh(true)
end