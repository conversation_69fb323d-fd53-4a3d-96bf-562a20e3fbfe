TreasureHuntRewardView = TreasureHuntRewardView or BaseClass(SafeBaseView)
local ani_flag_t = {}
local ani_count = 1
local ani_ten_flag_t = {}
local baodi_ani_flag_t = {}
local ani_baodi_count = 1
local ani_ten_count = 1
local scroll_verticalNormalizedPosition = 1
local ANI_SPEED = 0.1
local col_num = 10 							 		--每行多少个
local row_num = 1								--显示3行
local lerp = 0.015--1 / (50 - (col_num * row_num)*0)* 0.5		--每次减少多少
XunbaoType = {
	Gold = 0,
	Score = 1,
}

local CELL_HEGIHT = 110
local MAX_CELL_HEGIHT = 930
local reword_count = 0 				--该次寻宝奖励物品个数
local baodi_count = 0
function TreasureHuntRewardView:__init()
    self.view_style = ViewStyle.Half
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
    self:SetMaskBg(true)
    self.view_layer = UiLayer.Pop
    self.view_name = "TreasureHuntRewardView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_treasure_hunt_reward")
end

function TreasureHuntRewardView:ReleaseCallBack()
	if nil ~= self.zhanshi_grid then
		self.zhanshi_grid:DeleteMe()
		self.zhanshi_grid = nil
	end
	if nil ~= self.zhanshi_ten_grid then
		self.zhanshi_ten_grid:DeleteMe()
		self.zhanshi_ten_grid = nil
	end

    if nil ~= self.zhanshi_mingwen_grid then
		self.zhanshi_mingwen_grid:DeleteMe()
		self.zhanshi_mingwen_grid = nil
	end

	if nil ~= self.zhanshi_mingwen_ten_grid then
		self.zhanshi_mingwen_ten_grid:DeleteMe()
		self.zhanshi_mingwen_ten_grid = nil
	end

    if nil ~= self.baodi_list then
		self.baodi_list:DeleteMe()
		self.baodi_list = nil
    end

    if self.baodi_time_quest then
		GlobalTimerQuest:CancelQuest(self.baodi_time_quest)
		self.baodi_time_quest = nil
	end

	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
	if self.ten_time_quest then
		GlobalTimerQuest:CancelQuest(self.ten_time_quest)
		self.ten_time_quest = nil
	end
	if self.move_scroll_quest then
		GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
		self.move_scroll_quest = nil
	end

    if self.rect_five_tween then
        self.rect_five_tween:Kill() 
    end

	-- if self.money_bar then
	-- 	self.money_bar:DeleteMe()
	-- 	self.money_bar = nil
	-- end
end

function TreasureHuntRewardView:LoadCallBack()
	-- if not self.money_bar then
	-- 	self.money_bar = MoneyBar.New()
	-- 	local bundle, asset = ResPath.GetWidgets("MoneyBar")
	-- 	local show_params = {
    --     show_gold = true, show_bind_gold = true,
    --     show_coin = true, show_silver_ticket = true,
    --     }
    --     self.money_bar:SetMoneyShowInfo(0, 0, show_params)
	-- 	self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	-- end
    self.cur_ten_grid = nil
    self.cur_grid = nil

    local asset_bundle = "uis/view/treasurehunt_ui_prefab"
    local asset_name = "zhanshi_ten_item_cell"
    local mingwen_asset_name = "zhanshi_mingwen_ten_item_cell"

	self.zhanshi_grid = TreasureGrid.New()
    local t = {
        col = col_num,
        change_cells_num = 1,
        assetBundle = asset_bundle,
        assetName = asset_name,
        list_view = self.node_list["ph_zhanshii_cell"],
        itemRender = XunbaoRewardCell
    }
    self.zhanshi_grid:CreateCells(t)
    self.zhanshi_grid:SetStartZeroIndex(false)

    self.zhanshi_ten_grid = TreasureGrid.New()
    local ten_t = {
        col = 5,
        change_cells_num = 1,
        assetBundle = asset_bundle,
        assetName = asset_name,
        list_view = self.node_list["ph_zhanshii_ten_cell"],
        itemRender = TenXunbaoRewardCell
    }
    self.zhanshi_ten_grid:CreateCells(ten_t)
    self.zhanshi_ten_grid:SetStartZeroIndex(false)

    self.zhanshi_mingwen_grid = TreasureGrid.New()
    local mingwen_t = {
        col = col_num,
        change_cells_num = 1,
        assetBundle = asset_bundle,
        assetName = mingwen_asset_name,
        list_view = self.node_list["ph_zhanshii_mingwen_cell"],
        itemRender = XunbaoRewardCell
    }
    self.zhanshi_mingwen_grid:CreateCells(mingwen_t)
    self.zhanshi_mingwen_grid:SetStartZeroIndex(false)

    self.zhanshi_mingwen_ten_grid = TreasureGrid.New()
    local mingwen_ten_t = {
        col = 5,
        change_cells_num = 1,
        assetBundle = asset_bundle,
        assetName = mingwen_asset_name,
        list_view = self.node_list["ph_zhanshii_mingwen_ten_cell"],
        itemRender = TenXunbaoRewardCell
    }
    self.zhanshi_mingwen_ten_grid:CreateCells(mingwen_ten_t)
    self.zhanshi_mingwen_ten_grid:SetStartZeroIndex(false)

    self.baodi_list = TreasureListView.New(BaodiRewardRender, self.node_list.ph_baodi_list)

    XUI.AddClickEventListener(self.node_list.btn_storage, BindTool.Bind1(self.OnEnterCangku, self))	--进入仓库
	XUI.AddClickEventListener(self.node_list.btn_one_more, BindTool.Bind1(self.OnQuchuAgain, self))		--再来一次
	XUI.AddClickEventListener(self.node_list.skip_anim_toggle, BindTool.Bind(self.OnClinkSkipAnim, self))
end

function TreasureHuntRewardView:OnClinkSkipAnim(is_on)
    TreasureHuntWGData.Instance:SetAnimToggleData(self.mode, not is_on)
end

-- 进入仓库
function TreasureHuntRewardView:OnEnterCangku()
    local is_mingwen = self.is_mingwen
    if is_mingwen then
        MingWenWGCtrl.Instance:OpenMingWenBag(0, true)
    else
        TreasureHuntWGCtrl.Instance:OpenStroageView()
    end
	self:Close()
end

function TreasureHuntRewardView:SetDataType(mode, cur_count, is_mingwen)
	self.mode = mode --配置的模式 1-9 代表装备，巅峰，至尊 101代表铭纹单抽，102代表铭纹10抽
    self.cur_count = cur_count
    self.is_mingwen = is_mingwen

    --用于非铭纹的类型
    if self.mode % 3 == 0 then
        self.treasure_type = self.mode / 3
    else
        self.treasure_type = math.floor(self.mode / 3) + 1
    end
end

-- 再来一次
function TreasureHuntRewardView:OnQuchuAgain()
    local is_mingwen = self.is_mingwen
    if not is_mingwen then
        local btn_index = self.mode % 3
        btn_index = btn_index == 0 and 3 or btn_index
        TreasureHuntWGCtrl.Instance:OnClickBtn(btn_index)
    else
        TreasureHuntWGCtrl.Instance:OnClickMingwenBtn(self.mode - 100)
    end
end

function TreasureHuntRewardView:ChangeState(is_ten)
    if self.is_mingwen then
        self.node_list["ph_zhanshii_mingwen_cell"]:SetActive(not is_ten)
        self.node_list["ph_zhanshii_mingwen_ten_cell"]:SetActive(is_ten)
        self.node_list["ph_zhanshii_cell"]:SetActive(false)
        self.node_list["ph_zhanshii_ten_cell"]:SetActive(false)
    else
        self.node_list["ph_zhanshii_cell"]:SetActive(not is_ten)
        self.node_list["ph_zhanshii_ten_cell"]:SetActive(is_ten)
        self.node_list["ph_zhanshii_mingwen_cell"]:SetActive(false)
        self.node_list["ph_zhanshii_mingwen_ten_cell"]:SetActive(false)
    end
end

--刷新数据
function TreasureHuntRewardView:OnFlush()
    local btn_index = 0
    local is_mingwen = self.is_mingwen
    local treasure_cfg

    self.cur_ten_grid = self.is_mingwen and self.zhanshi_mingwen_ten_grid or self.zhanshi_ten_grid
    self.cur_grid = self.is_mingwen and self.zhanshi_mingwen_grid or self.zhanshi_grid

    if is_mingwen then
        btn_index = self.mode - 100
        treasure_cfg = TreasureHuntWGData.Instance:GetMingwenModeCfg()
        self.node_list.left_btn_text.text.text = Language.TreasureHunt.MingWenBag
    else
        btn_index = self.mode % 3
        btn_index = btn_index == 0 and 3 or btn_index
        local treasure_type = self.treasure_type
        treasure_cfg = TreasureHuntWGData.Instance:GetTreasureCfgByType(treasure_type)
        self.node_list.left_btn_text.text.text = Language.TreasureHunt.StorageTitle
    end

    if not treasure_cfg then
        return
    end
    self.node_list.btn_again_txt.text.text = Language.TreasureHunt.BtnText[btn_index]
    local cur_cfg = treasure_cfg[btn_index]

    local stuff_id = cur_cfg.stuff_id
    local has_num = ItemWGData.Instance:GetItemNumInBagById(stuff_id) --拥有的数量

    if is_mingwen then
        local cfg_need_num = cur_cfg.stuff_num - has_num
        local mingwen_vip_mode_cfg = TreasureHuntWGData.Instance:GetTreasureVipModeCfgByType(cur_cfg.mode, true) 
        local need_num = not IsEmptyTable(mingwen_vip_mode_cfg) and mingwen_vip_mode_cfg.vip_stuff_num - has_num or cfg_need_num

        if need_num > 0 then
            self.node_list.one_more_cosume.text.text = need_num * treasure_cfg[1].cost_gold --再来一次底下的仙玉数目
        end
    else
        local cfg_need_gold = cur_cfg.cost_gold
        local vip_cfg = TreasureHuntWGData.Instance:GetTreasureVipModeCfgByType(cur_cfg.mode)
        local cosume_gold = not IsEmptyTable(vip_cfg) and ((vip_cfg.vip_stuff_num - has_num) * treasure_cfg[1].cost_gold) or cfg_need_gold

        if cosume_gold > 0 then
            self.node_list.one_more_cosume.text.text = cosume_gold  --再来一次底下的仙玉数目   
        end
    end

    --右边必得道具
    local item_cfg
    if is_mingwen then
        self.node_list["reward_tongqian_text"].text.text = cur_cfg.reward_mingyin
        item_cfg = ItemWGData.Instance:GetItemConfig(cur_cfg.show_mingyin)
        self.node_list["name_text"].text.text = item_cfg.name
    else
        item_cfg = ItemWGData.Instance:GetItemConfig(cur_cfg.get_item_id)
        self.node_list["reward_tongqian_text"].text.text = item_cfg.param1 / 10000 .. Language.TreasureHunt.Wan
        self.node_list["name_text"].text.text = Language.TreasureHunt.GetGold
    end

    self.node_list["reward_tongqian"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))

    --左边积分
    local info
    if is_mingwen then
        info = TreasureHuntWGData.Instance:GetConvertShopCfgByType(4)
    else
        local treasure_type = self.treasure_type
        info = TreasureHuntWGData.Instance:GetConvertShopCfgByType(treasure_type - 1)
    end
    local item_cfg1 = ItemWGData.Instance:GetItemConfig(info[1].show_item)
    self.node_list["left_name"].text.text = item_cfg1.name
    local asset, bundle = ResPath.GetItem(item_cfg1.icon_id)  --积分图标
    self.node_list["reward_score_icon"].image:LoadSprite(asset, bundle,function()
        --self.node_list.icon.image:SetNativeSize()
    end)

    if is_mingwen then
        self.node_list["reward_score_text"].text.text = TreasureHuntWGData.Instance:GetMingWenResultScore()
    else
        self.node_list["reward_score_text"].text.text = cur_cfg.reward_score
    end

    if stuff_id > 0 then
        local bundle, asset = ResPath.GetItem(stuff_id)
        self.node_list["img_zbitem"].image:LoadSprite(bundle, asset, function ()
            self.node_list["img_zbitem"].image:SetNativeSize()
        end)

        local cfg_need_num = cur_cfg.stuff_num
        local vip_cfg = TreasureHuntWGData.Instance:GetTreasureVipModeCfgByType(cur_cfg.mode, is_mingwen)
        local need_num = not IsEmptyTable(vip_cfg) and vip_cfg.vip_stuff_num or cfg_need_num

        self.node_list["zbitem_key_num"].text.text = has_num .. "/" .. need_num
        if has_num > 0 then
            self.node_list["zbitem_key"]:SetActive(true)
            self.node_list["xianyu_icon_root"]:SetActive(has_num < need_num)
            self.node_list["one_more_cosume"]:SetActive(has_num < need_num)
        else
            self.node_list["zbitem_key"]:SetActive(false)
            self.node_list["xianyu_icon_root"]:SetActive(true)
            self.node_list["one_more_cosume"]:SetActive(true)
        end
    else
        self.node_list["zbitem_key"]:SetActive(false)
        self.node_list["xianyu_icon_root"]:SetActive(true)
        self.node_list["one_more_cosume"]:SetActive(true)
    end

    self.baodi_list:SetDataList({}) --数据清一下
    self:AdjustPosition()
	self:ResetItemData()
end

-- 设置展示位置
function TreasureHuntRewardView:AdjustPosition()
    local rect = self.node_list["ph_zhanshii_cell"].rect --一抽 50抽
    local rect_ten = self.node_list["ph_zhanshii_ten_cell"].rect   -- 10抽
    local rect_uiblock = self.node_list["ph_zhanshii_cell"]:GetComponent("UIBlock")
    local baodi_rect = self.node_list.baodi_container.rect

    local is_mingwen = self.is_mingwen
    local data_baodi_list = TreasureHuntWGData.Instance:GetBaodiList()
    self.has_baodi = not IsEmptyTable(data_baodi_list) and not is_mingwen

    self.node_list.baodi_container:SetActive(self.has_baodi)
    self:ResetTotalValueRootScale()

    -- 无保底
    if not self.has_baodi then
        rect_uiblock.enabled = false
        if self.cur_count == 1 then   		--寻宝1次
            self.node_list["ph_zhanshii_cell"].mask2d.enabled = false
            self.node_list["ph_zhanshii_cell"].scroll_rect.enabled = false
            rect.anchoredPosition = Vector2(0, 13)
            rect.sizeDelta = Vector2(880, 230)
            self:ChangeState(false)
        elseif self.cur_count == 10 then 		--寻宝10次
            self.node_list["ph_zhanshii_cell"].mask2d.enabled = true
            self.node_list["ph_zhanshii_ten_cell"].scroll_rect.enabled = false
            rect_ten.anchoredPosition = Vector2(0, 52)
            self:ChangeState(true)
        elseif self.cur_count == 50 then		--寻宝45次
            self.node_list["ph_zhanshii_cell"].scroll_rect.enabled = true
            rect_uiblock.enabled = true
            self:ChangeState(false)
            rect.anchoredPosition = Vector2(0, 75)
            -- rect.sizeDelta = Vector2(880, 352)
            self.node_list["ph_zhanshii_cell"].mask2d.enabled = true
            self.is_mask = nil

            self:DoDataListRectTween(rect, 108, 880, 352)
        end
    else
        -- 有保底
        if self.cur_count == 1 then   		--寻宝1次
            self.node_list["ph_zhanshii_cell"].mask2d.enabled = false
            self.node_list["ph_zhanshii_cell"].scroll_rect.enabled = false
            -- rect.anchoredPosition = Vector2(0, 0)
            -- rect.sizeDelta = Vector2(880, 220)
            baodi_rect.anchoredPosition = Vector2(0, 80)
            self:ChangeState(false)
        elseif self.cur_count == 10 then 		--寻宝10次
            self.node_list["ph_zhanshii_cell"].mask2d.enabled = true
            self.node_list["ph_zhanshii_ten_cell"].scroll_rect.enabled = false
            rect_ten.anchoredPosition = Vector2(0, 4)
            -- rect.anchoredPosition = Vector2(0, 100)
            baodi_rect.anchoredPosition = Vector2(0, 227)
            self:ChangeState(true)
        elseif self.cur_count == 50 then		--寻宝45次
            self.node_list["ph_zhanshii_cell"].scroll_rect.enabled = true
            rect_uiblock.enabled = true
            self:ChangeState(false)
            rect.anchoredPosition = Vector2(0, 6)
            -- rect.sizeDelta = Vector2(880, 214)
            baodi_rect.anchoredPosition = Vector2(0, 227)
            self.node_list["ph_zhanshii_cell"].mask2d.enabled = true
            self.is_mask = nil

            self:DoDataListRectTween(rect, 108, 880, 214)
        end
    end
end

function TreasureHuntRewardView:DoDataListRectTween(rect, cell_hight, width, height)
    local is_not_skip = TreasureHuntWGData.Instance:GetAnimToggleData(self.mode)
    if is_not_skip then
        rect.sizeDelta = Vector2(width, cell_hight)

        if self.rect_five_tween then
            self.rect_five_tween:Kill() 
        end

        self.rect_five_tween = rect:DOSizeDelta(Vector2(width, height), 2):SetDelay(1.2):SetEase(DG.Tweening.Ease.Linear)
    else
        if self.rect_five_tween then
            self.rect_five_tween:Kill() 
        end

        rect.sizeDelta = Vector2(width, height)
    end
end

-- 设置数据
function TreasureHuntRewardView:ResetItemData()
    -- 是否跳过动画
	local is_not_skip = TreasureHuntWGData.Instance:GetAnimToggleData(self.mode)
    self.node_list.skip_anim_toggle.toggle.isOn = not is_not_skip
    self.node_list.skip_anim_toggle:SetActive(false)
    self.node_list.ph_zhanshii_cell.scroll_rect.enabled = true

	local xunbao_list = {}
	if self.mode > 100 then
		xunbao_list = TreasureHuntWGData.Instance:GetMingWenResultInfo() or {}  --符文寻宝
	else
		xunbao_list = TreasureHuntWGData.Instance:GetResultInfoList() or {}
    end
    reword_count = #xunbao_list

	if self.cur_count == 10 then
		if nil ~= self.cur_ten_grid then
            ani_ten_flag_t = {}
			self.cur_ten_grid:SetDataList(xunbao_list)
			--self.cur_ten_grid:CancleAllSelectCell()
			if self.ten_time_quest then
				GlobalTimerQuest:CancelQuest(self.ten_time_quest)
				self.ten_time_quest = nil
			end
            ani_ten_count = 1
            if self.has_baodi and not self.is_mingwen then
                self:ShowBaodiAni()
            end
			if is_not_skip then
				self.ten_time_quest = GlobalTimerQuest:AddTimesTimer(function()
					self:DoCellAnimTen(true)
					end, ANI_SPEED, reword_count )
			else
				for i = 1, reword_count do
					self:DoCellAnimTen(false)
				end
                
                self:DoTotalValueRootTween(false)
			end
		end
    else
		if nil ~= self.cur_grid then
            ani_flag_t = {}
			self.cur_grid:SetDataList(xunbao_list)
            self.cur_grid:JumptToPrecent(1)
			--self.cur_grid:CancleAllSelectCell()
			if self.time_quest then
				GlobalTimerQuest:CancelQuest(self.time_quest)
				self.time_quest = nil
			end
			ani_count = 1
            scroll_verticalNormalizedPosition = 1
            if reword_count == 0 then --只有一个的时候
                self:ShowBaodiAni()
                self:DoTotalValueRootTween(is_not_skip)
            else
                if self.has_baodi and not self.is_mingwen then
                    self:ShowBaodiAni()
                end
                if is_not_skip then
                    self.time_quest = GlobalTimerQuest:AddTimesTimer(function()
                        self:DoCellAnim(true)
                        end, ANI_SPEED, reword_count)
                else
                    for i = 1, reword_count do
                        self:DoCellAnim(false)
                    end

                    self:DoTotalValueRootTween(false)
                end
            end

			if self.move_scroll_quest then
				GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
				self.move_scroll_quest = nil
			end
            self.move_tween_complete = false

			if reword_count > 30 and is_not_skip then
				self.move_scroll_quest = GlobalTimerQuest:AddTimesTimer(function()
					self:MoveScroll()
					if scroll_verticalNormalizedPosition <= 0 then
						scroll_verticalNormalizedPosition = 0
						if self.move_scroll_quest then
							GlobalTimerQuest:CancelQuest(self.move_scroll_quest)
							self.move_scroll_quest = nil
						end
					end
				end, 0.03, 999999)
			end
		end
    end
end

function TreasureHuntRewardView:ShowBaodiAni()
    local is_mingwen = self.is_mingwen
    local data_baodi_list = TreasureHuntWGData.Instance:GetBaodiList()
    local is_not_skip = TreasureHuntWGData.Instance:GetAnimToggleData(self.mode)
    if not is_mingwen and nil ~= self.baodi_list and not IsEmptyTable(data_baodi_list) then
       
        baodi_ani_flag_t = {}
        self.baodi_list:SetDataList(data_baodi_list)
        if self.baodi_time_quest then
            GlobalTimerQuest:CancelQuest(self.baodi_time_quest)
            self.baodi_time_quest = nil
        end
        ani_baodi_count = 1
        baodi_count = #data_baodi_list
        if is_not_skip then
            self.baodi_time_quest = GlobalTimerQuest:AddTimesTimer(function()
                self:DoBaodiCellAnim(true)
                end, ANI_SPEED, baodi_count)
        else
            for i = 1, baodi_count do
                self:DoBaodiCellAnim(false)
            end
        end
    end
end

function TreasureHuntRewardView:ResetTotalValueRootScale()
    self.node_list.total_price_root.rect.localScale = Vector3(0, 0, 0)
end

function TreasureHuntRewardView:DoTotalValueRootTween(is_tween)
    local function set_total_value(is_tween)
        local data_baodi_list = TreasureHuntWGData.Instance:GetBaodiList()
        local xunbao_list
        if self.mode > 100 then
            xunbao_list = TreasureHuntWGData.Instance:GetMingWenResultInfo() or {}  --符文寻宝
        else
            xunbao_list = TreasureHuntWGData.Instance:GetResultInfoList() or {}
        end
    
        local total_value = 0
        if self.mode <= 100 and not IsEmptyTable(data_baodi_list) then
            for k, v in pairs(data_baodi_list) do
                total_value = total_value + ItemWGData.Instance:GetItemValueByItemId(v.item_id)
            end
        end

        if not IsEmptyTable(xunbao_list) then
            for k, v in pairs(xunbao_list) do
                total_value = total_value + ItemWGData.Instance:GetItemValueByItemId(v.item_id)
            end
        end

        if is_tween then
            UITween.DONumberTo(self.node_list.desc_total_price.text, 0, tonumber(total_value), 0.5, 
            function (num)
                self.node_list.desc_total_price.text.text = math.floor(num)
            end, function ()
                self.node_list.desc_total_price.text.text = total_value
            end)
        else
            self.node_list.desc_total_price.text.text = total_value
        end
    end

    if is_tween then
        self.node_list.total_price_root.rect.localScale = Vector3(2.5, 2.5, 2.5)
        self.node_list.desc_total_price.text.text = ""
        self.node_list.total_price_root.rect:DOScale(Vector3(1, 1, 1), 0.15):OnComplete(function()
            set_total_value(true)
        end)
    else
        self.node_list.total_price_root.rect.localScale = Vector3(1, 1, 1)
        set_total_value(false)
    end
end

function TreasureHuntRewardView:ShowAllCellImmediately()
	local grid = self.cur_count == 10 and self.cur_ten_grid or self.cur_grid
	if not grid then
		return
	end
	if not reword_count or reword_count <=0 then
		return
	end
	for i = 1, reword_count do
		local cell = self.cur_grid:GetCell(i)
		if cell ~= nil and cell:GetData() ~= nil then
			cell.view.transform.localScale = Vector3(1,1,1)
			cell:SetActive(true)
		end
	end
    reword_count = 0
    local is_mingwen = self.is_mingwen
    local data_baodi_list = TreasureHuntWGData.Instance:GetBaodiList()
    if not is_mingwen and nil ~= self.baodi_list and not IsEmptyTable(data_baodi_list) then
        for i = 1, baodi_count do
            local cell = self.baodi_list:GetItemAt(i)
            if cell ~= nil and cell:GetData() ~= nil then
                cell.view.transform.localScale = Vector3(1,1,1)
                cell:SetActive(true)
            end
        end
    end
    baodi_count = 0
end

function TreasureHuntRewardView:DoBaodiCellAnim(do_tween)
    local cell = self.baodi_list:GetItemAt(ani_baodi_count)
    baodi_ani_flag_t[ani_baodi_count] = true
    ani_baodi_count = ani_baodi_count + 1
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ShengJi, nil, true))
    if cell ~= nil and cell:GetData() ~= nil then
        if do_tween then
            cell.view.transform.localScale = Vector3(2.5,2.5,2.5)
            cell.view.transform:DOScale(Vector3(1,1,1),0.15)
        else
            cell.view.transform.localScale = Vector3(1, 1, 1)
        end
        --特效
        local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
        EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0),nil,nil)
        cell:SetActive(true)
    end
end

function TreasureHuntRewardView:DoCellAnimTen(do_tween)
    local is_mingwen = self.is_mingwen
    local cell = self.cur_ten_grid:GetCell(ani_ten_count)

    ani_ten_flag_t[ani_ten_count] = true
	ani_ten_count = ani_ten_count + 1
	if cell ~= nil and cell:GetData() ~= nil then
		if do_tween then
			cell.view.transform.localScale = Vector3(2.5,2.5,2.5)
			cell.view.transform:DOScale(Vector3(1,1,1),0.2)
		else
			cell.view.transform.localScale = Vector3(1, 1, 1)
		end
		--特效
		local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
		EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0),nil,nil)
		cell:SetActive(true)
    end

    if do_tween and ani_ten_count == reword_count + 1 then
        self:DoTotalValueRootTween(true)
    end

    -- if ani_ten_count == reword_count + 1 and self.has_baodi and not is_mingwen then
    --     self:ShowBaodiAni()
    -- end
end

function TreasureHuntRewardView:SetRootNodeActive(value)
	SafeBaseView.SetRootNodeActive(self, value)
	if value and reword_count > 0 then
		self:ShowAllCellImmediately()
	end
end

function TreasureHuntRewardView:DoCellAnim(do_tween)
    local is_mingwen = self.is_mingwen
    local cell = self.cur_grid:GetCell(ani_count)
    ani_flag_t[ani_count] = true
    ani_count = ani_count + 1
    if cell ~= nil and cell:GetData() ~= nil then
        cell:SetActive(true)
        if do_tween then
            cell.view.transform.localScale = Vector3(2.5,2.5,2.5)
            cell.view.transform:DOScale(Vector3(1,1,1),0.15)
        else
            cell.view.transform.localScale = Vector3(1, 1, 1)
        end
        --特效
        local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_choujiangbaodian)
        if do_tween or ani_baodi_count <= 31 then
            EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, cell.view.transform, 0.28, Vector3(0, 0, 0),nil,nil)
        end
    end

    if do_tween and ani_count == reword_count + 1 then
        self:DoTotalValueRootTween(true)
    end

    -- if ani_count == reword_count + 1 and self.has_baodi and not is_mingwen then
    --     print_error("ani_count",ani_count)
    --     self:ShowBaodiAni()
    -- end
end

-- local total = -70
function TreasureHuntRewardView:MoveScroll()
	if ani_count > (col_num * row_num) then --and self.move_tween_complete then --
		scroll_verticalNormalizedPosition = scroll_verticalNormalizedPosition - lerp
	else
		scroll_verticalNormalizedPosition = 1
	end
	if self.node_list.ph_zhanshii_cell then
		self.node_list.ph_zhanshii_cell.scroll_rect.verticalNormalizedPosition = scroll_verticalNormalizedPosition
	end
end

local effect_list = {
    [GameEnum.ITEM_COLOR_ORANGE] = "UI_fuwen_kuang_00", --橙色
    [GameEnum.ITEM_COLOR_RED] = "UI_fuwen_kuang_01", --红色
}

----------------------------------------------XunbaoRewardCell----------------------------------------------
XunbaoRewardCell = XunbaoRewardCell or BaseClass(BaseRender)

function XunbaoRewardCell:LoadCallBack()
    local is_mingwen = TreasureHuntWGData.Instance:GetIsMingwen()

    if not self.item then
        if is_mingwen then
            self.item = MingWenItemRender.New(self.node_list.mingwen_item_pos)
        else
            self.item = ItemCell.New(self.node_list.item_pos)
        end
    end
end

function XunbaoRewardCell:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function XunbaoRewardCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self:SetVisible(self.index == nil or ani_flag_t[self.index] == true)

    self.item:SetData(self.data)

    -- local is_mingwen = TreasureHuntWGData.Instance:GetIsMingwen()
    -- if is_mingwen then
    --     self.item:SetButtonComp(true)
    --     self.item:SetDefaultEff(false)
    --     self.item:SetQualityIconVisible(false)
    --     self.item:SetCellBgEnabled(false)
    -- end

    self.node_list.desc_value_str.text.text = ItemWGData.Instance:GetItemValueByItemId(self.data.item_id)
end



-- XunbaoRewardCell = XunbaoRewardCell or BaseClass(ItemCell)

-- function XunbaoRewardCell:LoadCallBack(instance)
-- 	-- override
-- end

-- function XunbaoRewardCell:OnFlush()
--     self:SetActive(ani_flag_t[self.index] == true)
--     local is_mingwen = TreasureHuntWGData.Instance:GetIsMingwen()
--     self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)
--     if is_mingwen then
-- 		ItemCell.ClearAllParts(self)
-- 		self:SetButtonComp(true)
--         self:SetDefaultEff(false)
--         self:SetQualityIconVisible(false)
--         self:SetCellBgEnabled(false)
-- 		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
-- 		if item_cfg then
-- 	        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
-- 	        self:SetItemIcon(bundle, asset)
-- 	        for k,v in pairs(BaseCell_Ui_Circle_Effect) do
-- 	            self:SetEffectEnable(false, v)
-- 	        end
-- 	        if BaseCell_Ui_Circle_Effect[item_cfg.color] then
-- 	            self:SetEffectEnable(true, BaseCell_Ui_Circle_Effect[item_cfg.color], nil, self:Nodes("EffectRoot"))
-- 	        end
--             self:Nodes("EffectRoot").transform.localScale = Vector3(0.9, 0.9, 0.9)
-- 		end
--     else
--         for k,v in pairs(BaseCell_Ui_Circle_Effect) do
--             self:SetEffectEnable(false, v)
--         end
--         ItemCell.OnFlush(self)
--     end
-- end

-- function XunbaoRewardCell:SetActive(value)
-- 	ItemCell.SetVisible(self, value and (self.index == nil or ani_flag_t[self.index]))
-- end

----------------------------------------------TenXunbaoRewardCell----------------------------------------------
TenXunbaoRewardCell = TenXunbaoRewardCell or BaseClass(BaseRender)

function TenXunbaoRewardCell:LoadCallBack()
    local is_mingwen = TreasureHuntWGData.Instance:GetIsMingwen()

    if not self.item then
        if is_mingwen then
            self.item = MingWenItemRender.New(self.node_list.mingwen_item_pos)
        else
            self.item = ItemCell.New(self.node_list.item_pos)
        end
    end
end

function TenXunbaoRewardCell:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function TenXunbaoRewardCell:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self:SetVisible(ani_ten_flag_t[self.index] == true)
    self.item:SetData(self.data)

    -- local is_mingwen = TreasureHuntWGData.Instance:GetIsMingwen()
    -- if is_mingwen then
    --     self.item:SetButtonComp(true)
    --     self.item:SetDefaultEff(false)
    --     self.item:SetQualityIconVisible(false)
    --     self.item:SetCellBgEnabled(false)
    -- end

    self.node_list.desc_value_str.text.text = ItemWGData.Instance:GetItemValueByItemId(self.data.item_id)
end

-- TenXunbaoRewardCell = TenXunbaoRewardCell or BaseClass(ItemCell)
-- function TenXunbaoRewardCell:OnFlush()
-- 	self:SetActive(ani_ten_flag_t[self.index] == true)
--     local is_mingwen = TreasureHuntWGData.Instance:GetIsMingwen()
--     self:Nodes("EffectRoot").transform.localScale = Vector3(1, 1, 1)
--     if is_mingwen then
--         ItemCell.ClearAllParts(self)
--         self:SetDefaultEff(false)
--         self:SetQualityIconVisible(false)
--         self:SetCellBgEnabled(false)
--         local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
-- 		if item_cfg then
-- 			self:SetButtonComp(true)
-- 	        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
-- 	        self:SetItemIcon(bundle, asset)
-- 	        for k,v in pairs(BaseCell_Ui_Circle_Effect) do
-- 	            self:SetEffectEnable(false, v)
-- 	        end
-- 	        if BaseCell_Ui_Circle_Effect[item_cfg.color] then
-- 	            self:SetEffectEnable(true, BaseCell_Ui_Circle_Effect[item_cfg.color], nil, self:Nodes("EffectRoot"))
-- 	        end
--             self:Nodes("EffectRoot").transform.localScale = Vector3(0.9, 0.9, 0.9)
-- 		end
--     else
--         for k,v in pairs(BaseCell_Ui_Circle_Effect) do
--             self:SetEffectEnable(false, v)
--         end
--         ItemCell.OnFlush(self)
--     end
-- end

-- function TenXunbaoRewardCell:SetActive(value)
-- 	ItemCell.SetActive(self, value and (self.index == nil or ani_ten_flag_t[self.index]))
-- end

----------------------------------------TreasureGrid----------------------------------
TreasureGrid = TreasureGrid or BaseClass(AsyncBaseGrid)

-- 获得指定的格子
function TreasureGrid:GetCell(index)
    for k, v in pairs(self.cell_list) do
        local row = math.floor((index - 1) / self.columns)
        if row == v:GetRows() and v:GetActive()  then
            for k1, v1 in pairs(v:GetAllCell()) do
                if v1:GetIndex() == index then
                    return v1
                end
            end
        end
    end
	return nil
end

----------------------------------------TreasureListView----------------------------------
TreasureListView = TreasureListView or BaseClass(AsyncListView)

-- 获得某个索引下的item
function TreasureListView:GetItemAt(cell_index)
    for k, v in pairs(self.cell_list) do
        if v:GetIndex() == cell_index then
            return v
        end
    end
    return nil
end

----------------------------------------BaodiRewardRender----------------------------------
BaodiRewardRender = BaodiRewardRender or BaseClass(BaseRender)

function BaodiRewardRender:LoadCallBack()
    if not self.item then
        self.item = ItemCell.New(self.node_list.item_pos)
    end
end

function BaodiRewardRender:__delete()
    if self.item then
        self.item:DeleteMe()
        self.item = nil
    end
end

function BaodiRewardRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    self:SetVisible(baodi_ani_flag_t[self.index] == true)
    self.item:SetData(self.data)
    self.node_list.desc_value.text.text = ItemWGData.Instance:GetItemValueByItemId(self.data.item_id)
end

-- BaodiRewardRender = BaodiRewardRender or BaseClass(ItemCell)

-- function BaodiRewardRender:LoadCallBack(instance)
-- 	-- override
-- end

-- function BaodiRewardRender:OnFlush()
--     self:SetActive(baodi_ani_flag_t[self.index] == true)
--     ItemCell.OnFlush(self)
-- end

-- function BaodiRewardRender:SetActive(value)
-- 	ItemCell.SetActive(self, value and (self.index == nil or baodi_ani_flag_t[self.index]))
-- end
