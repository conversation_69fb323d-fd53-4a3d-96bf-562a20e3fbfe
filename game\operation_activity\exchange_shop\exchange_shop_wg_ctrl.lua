require("game/operation_activity/exchange_shop/exchange_shop_wg_data")
require("game/operation_activity/exchange_shop/exchange_shop_item")

ExchangeShopWGCtrl = ExchangeShopWGCtrl or BaseClass(BaseWGCtrl)

function ExchangeShopWGCtrl:__init()
	if ExchangeShopWGCtrl.Instance ~= nil then
		ErrorLog("[ExchangeShopWGCtrl] Attemp to create a singleton twice !")
	end
	ExchangeShopWGCtrl.Instance = self

	self.data = ExchangeShopWGData.New()

	self:RegisterAllProtocols()
	
	OperationActivityWGCtrl.Instance:ListenHotUpdate("config/auto_new/act_common_convert_auto", BindTool.Bind(self.HotUpdate, self))

end

function ExchangeShopWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	ExchangeShopWGCtrl.Instance = nil
end

function ExchangeShopWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCActCommonConvertInfo, "SCActCommonConvertInfo")

	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))

end

function ExchangeShopWGCtrl:MainuiOpenCreate()
	-- RemindManager.Instance:Fire(RemindName.ExchangeShop)
	self:SendOperExchange(OA_ACT_COMMON_CONVERT_OPERA_TYPE.INFO)
end

function ExchangeShopWGCtrl:ItemDataCallBack()

end

function ExchangeShopWGCtrl:OnDayChange()
	self.data:CheckPeriod()
end

function ExchangeShopWGCtrl:SCActCommonConvertInfo(protocol)
	if protocol.cur_act_period == 0 then
		return
	end
	self.data:SaveData(protocol)
	OperationActivityWGCtrl.Instance:FlushView()
	RemindManager.Instance:Fire(RemindName.ExchangeShop)
end

function ExchangeShopWGCtrl:SendOperExchange(opera_type, param1, param2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.OPERA_ACT_EXCHANGE
	protocol.opera_type = opera_type
	protocol.param_1 = param1 or 0
	protocol.param_2 = param2 or 0
	protocol.param_3 = 0
	protocol:EncodeAndSend()
end

function ExchangeShopWGCtrl:HotUpdate()
	self.data:LoadConfig()
	self:SendOperExchange(OA_ACT_COMMON_CONVERT_OPERA_TYPE.INFO)
end