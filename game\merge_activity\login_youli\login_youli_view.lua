local MAX_SHOW_NUM = 4 --最大显示数量

function MergeActivityView:LoadIndexCallBackLoginRewarView()
    self.buy_click_time = 0
    self.cur_drag_index = 1
    self.data_length = 0
    self.item_list_num = 0
	self.select_day_index = 1
	self.is_jump_day_index = false

	self.mg_lr_list_view = AsyncListView.New(MergeLoginDayRender, self.node_list["lg_day_list"])
	self.mg_lr_double_list_view = AsyncListView.New(MergeLoginDoubleRender, self.node_list["lg_double_list"])
	self.mg_lr_reward_list_view = AsyncListView.New(MergeLoginRewardRender, self.node_list["lg_reward_list"])
	self.mg_lr_reward_list_view:SetStartZeroIndex(true)

	local cambered_list_data = {
		item_render = MergeLoginDayRender,
		asset_bundle = "uis/view/merge_activity_ui/login_youli_ui_prefab",
		asset_name = "merge_login_day_render",

		scroll_list = self.node_list.lg_day_list,
		-- center_x = 8,
		-- center_y = 1399,
		-- radius_x = 1400,
		-- radius_y = 1400,
		-- angle_delta = Mathf.PI / 25,
		-- origin_rotation = Mathf.PI * 0.9271,
		-- is_clockwise_list = false,
		-- arg_speed = 0.12,

		center_x = 8,
		center_y = 1399,
		radius_x = 1432,
		radius_y = 1400,
		angle_delta = Mathf.PI / 25,
		origin_rotation = Mathf.PI * 0.9271,
		is_clockwise_list = false,
		arg_speed = 0.12,

		click_item_cb = BindTool.Bind(self.OnClickDayBtn, self),
        drag_to_next_cb = BindTool.Bind(self.OnDragToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragToLastCallBack, self),
        on_drag_end_cb = BindTool.Bind(self.OnDragEndCallBack, self),
	}

	self.mg_lr_list_view = CamberedList.New(cambered_list_data)
    self.mg_lr_list_view.drag_dir = -1

	XUI.AddClickEventListener(self.node_list["lg_btn_receive"], BindTool.Bind(self.OnClickGetLoginReward, self))
end

function MergeActivityView:ReleaseLoginRewardView()
	if self.mg_lr_list_view then
		self.mg_lr_list_view:DeleteMe()
		self.mg_lr_list_view = nil
	end

	if self.mg_lr_reward_list_view then
		self.mg_lr_reward_list_view:DeleteMe()
		self.mg_lr_reward_list_view = nil
	end

	if self.mg_lr_double_list_view then
		self.mg_lr_double_list_view:DeleteMe()
		self.mg_lr_double_list_view = nil
	end
end

function MergeActivityView:OnClickDayBtn(item_cell, force_jump)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if (not force_jump and self.select_day_index == select_index) then
		return
	end

	self.select_day_index = select_index
	--self.cur_drag_index = select_index

	local btn_item_list = self.mg_lr_list_view:GetRenderList()
    for k, item in ipairs(btn_item_list) do
		item:SetSelectedHL(self.select_day_index)
    end

	self:FlushLoginReward()
end

function MergeActivityView:FlushLoginReward()
	local common_state = LoginYouLiWGData.Instance:GetCommonRewardState(self.select_day_index)
	self.node_list["lg_btn_receive"]:SetActive(common_state ~= LoginYouLiRewardState.YLQ)
	self.node_list["lg_btn_receive_remind"]:SetActive(common_state == LoginYouLiRewardState.KLQ)
	self.node_list["lg_btn_ylq"]:SetActive(common_state == LoginYouLiRewardState.YLQ)
	XUI.SetGraphicGrey(self.node_list.lg_btn_receive, common_state ~= LoginYouLiRewardState.KLQ)

	local cfg = LoginYouLiWGData.Instance:GetGradeRewardListByDay(self.select_day_index)
	self.mg_lr_reward_list_view:SetRefreshCallback(function(item, cell_index)
		if item then
			item:SetState(common_state)
		end
	end)
	self.mg_lr_reward_list_view:SetDataList(cfg.reward_item)
end

function MergeActivityView:OnDragToNextCallBack()
	self.cur_drag_index = self.cur_drag_index + 1
end

function MergeActivityView:OnDragToLastCallBack()
	self.cur_drag_index = self.cur_drag_index - 1
end

function MergeActivityView:OnDragEndCallBack()
	self:OnDragEndToIndex(nil, false, self.cur_drag_index)
end

function MergeActivityView:OnDragEndToIndex(callback, is_click, drag_index)
	if self.mg_lr_list_view == nil then
		return
	end

	local count = self.item_list_num - MAX_SHOW_NUM + 1
    local to_index
    if self.item_list_num <= MAX_SHOW_NUM then
        to_index = 1
    elseif drag_index > 1 then
        if (MAX_SHOW_NUM + drag_index) > self.item_list_num then
            to_index = count
        else
            to_index = drag_index
        end
    elseif drag_index <= 1 then
        to_index = 1
    end

    self.cur_drag_index = to_index
	-- if to_index <= 1 or to_index >= count then
	-- 	self.mg_lr_list_view:ScrollToIndex(to_index, callback, is_click)
	-- end
	self.mg_lr_list_view:ScrollToIndex(to_index, callback, is_click)
end

function MergeActivityView:SetLoginYouLiViewInfo()
    --问号样式和文本框
    LoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.INFO)
	self:SetOutsideRuleTips(Language.MergeActivity.LoginYouLiState)
	self:SetRuleInfo(Language.MergeActivity.TipsActivityHintShow, Language.MergeActivity.TipsActivityHint)
	self.node_list.title_desc.text.text = Language.MergeActivity.TipsActivityHintShow

	local cur_day = LoginYouLiWGData.Instance:GetLoginNum()
	self.select_day_index = cur_day
	self.is_jump_day_index = true
end

function MergeActivityView:OperationFlushLoginView()
	local data_list = LoginYouLiWGData.Instance:GetGradeRewardList()
	--self.mg_lr_list_view:ScrollToIndex(self.select_day_index, nil, false)

    self.item_list_num = #data_list
    self.mg_lr_list_view:CreateCellList(#data_list)
    local btn_item_list = self.mg_lr_list_view:GetRenderList()
    for k, item in ipairs(btn_item_list) do
        local item_data = data_list[k]
        item:SetData(item_data)

		if self.select_day_index == item:GetIndex() then
			self:OnClickDayBtn(item, true)
			if self.is_jump_day_index then
				self.is_jump_day_index = false
				local count = self.item_list_num - MAX_SHOW_NUM + 1
				local jump_index = (self.select_day_index - 2) > 0 and (self.select_day_index - 2) or 1 -- -2是为了在跳转的时候能让目标处于显示中中间的位置

				if self.item_list_num <= MAX_SHOW_NUM then
					jump_index = 1
				else
					jump_index = self.select_day_index >= count and count or jump_index
				end

				self.cur_drag_index = jump_index

				self.mg_lr_list_view:ScrollToIndex(jump_index, nil, false)
			end
		end
    end

	self:FlushLoginDouBle()
end

function MergeActivityView:FlushLoginDouBle()
    local cfg = MergeActDuoBeiWGData.Instance:GetDuoBeiInfo()
	if cfg ~= nil then
        self.mg_lr_double_list_view:SetDataList(cfg)
	end
end

function MergeActivityView:OnClickGetLoginReward()
	local common_state = LoginYouLiWGData.Instance:GetCommonRewardState(self.select_day_index)
	if common_state == LoginYouLiRewardState.KLQ then
		LoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.COMMON_REWARD, self.select_day_index)
	elseif common_state == LoginYouLiRewardState.BKL then
		TipWGCtrl.Instance:ShowSystemMsg(Language.MergeActivity.TipDesc1)
	end
end

MergeLoginDayRender = MergeLoginDayRender or BaseClass(BaseRender)
function MergeLoginDayRender:LoadCallBack()
	self.reward_list = {}
	XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind(self.OnClickGetReward, self))
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

function MergeLoginDayRender:__delete()
	if self.reward_list then
		for k,v in pairs(self.reward_list) do
			v:DeleteMe()
		end
		self.reward_list = nil
	end
end

function MergeLoginDayRender:OnFlush()
	if self.data == nil then
		return
	end

	local str = string.format(Language.MergeActivity.LoginStr1, ToColorStr(NumberToChinaNumber(self.data.day_index), COLOR3B.L_GREEN))
	self.node_list["reward_day"].text.text = str
	self.node_list["complete_reward_day"].text.text = str
	-- local special_state = LoginYouLiWGData.Instance:GetSpecialRewardState(self.data.day_index)
	local common_state = LoginYouLiWGData.Instance:GetCommonRewardState(self.data.day_index)
	-- self.node_list["btn_receive"]:SetActive(common_state ~= LoginYouLiRewardState.YLQ)
	self.node_list["btn_ylq"]:SetActive(common_state == LoginYouLiRewardState.YLQ)
	self.node_list["complete_bg"]:SetActive(common_state == LoginYouLiRewardState.YLQ)
	self.node_list["normal_bg"]:SetActive(common_state ~= LoginYouLiRewardState.YLQ)
	-- XUI.SetButtonEnabled(self.node_list["btn_receive"], common_state == LoginYouLiRewardState.KLQ)
	-- self.node_list["remind"]:SetActive(common_state == LoginYouLiRewardState.KLQ)

	local reward_item = self.data.reward_item
	local num = 0
	for i = 0, #reward_item do
		local data = reward_item[i]
		if data then
			if self.reward_list[i + 1] then
				self.reward_list[i + 1]:SetData(data)
			else
				local cell = ItemCell.New(self.node_list["reward_group"])
				cell:SetData(data)
				table.insert(self.reward_list, cell)
			end

			num = num + 1
		end
	end

	for i = 1, #self.reward_list do
		self.reward_list[i]:SetActive(i <= num)
	end
end

function MergeLoginDayRender:OnClickGetReward()
	if self.data == nil then
		return
	end

	LoginYouLiWGCtrl.Instance:SendActivityRewardOp(MERGE_LOGIN_GIFT_OP_TYPE.COMMON_REWARD, self.data.day_index)
end

-- 设置点击回调
function MergeLoginDayRender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function MergeLoginDayRender:OnClick()
	if self.click_callback then
		self.click_callback()
	end
end

function MergeLoginDayRender:SetSelectedHL(index)
	local is_select = self.index == index

	self.node_list.select_bg:SetActive(is_select)
	self.node_list.no_select_bg:SetActive(not is_select)
end

MergeLoginRewardRender = MergeLoginRewardRender or BaseClass(BaseRender)
function MergeLoginRewardRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_cell)
end

function MergeLoginRewardRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function MergeLoginRewardRender:OnFlush()
	if self.data == nil then
		return
	end

	self.item_cell:SetData(self.data)
end

function MergeLoginRewardRender:SetState(common_state)
	self.item_cell:SetLingQuVisible(common_state == LoginYouLiRewardState.YLQ)
	self.item_cell:SetRedPointEff(common_state == LoginYouLiRewardState.KLQ)
end

MergeLoginDoubleRender = MergeLoginDoubleRender or BaseClass(BaseRender)
function MergeLoginDoubleRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn, BindTool.Bind(self.OnBtnClickDuoBei, self))
end

function MergeLoginDoubleRender:__delete()

end

function MergeLoginDoubleRender:OnFlush()
	if self.data == nil then
		return
	end

	--self.node_list.title_name.text.text = self.data.cfg.wanfa_name
	local bundle, asset = ResPath.GetCommonImages(self.data.cfg.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)
end

function MergeLoginDoubleRender:OnBtnClickDuoBei()
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end