NewFightMountView = NewFightMountView or BaseClass(SafeBaseView)

function NewFightMountView:__init()
    self.view_style = ViewStyle.Full
    self.view_layer = UiLayer.Normal
    self.is_safe_area_adapter = true

    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/new_fight_mount_ui_prefab", "layout_new_fight_mount")
end

function NewFightMountView:__delete()
end

function NewFightMountView:OpenCallBack()

end

function NewFightMountView:LoadCallBack()
    if not self.fight_mount_type_list then
        self.fight_mount_type_list = AsyncListView.New(NewFightMountTypeRender, self.node_list["mount_type_list"])
        self.fight_mount_type_list:SetSelectCallBack(BindTool.Bind(self.OnMountTypeHandler, self))
    end

    if self.fight_mount_model == nil then
        self.fight_mount_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["fight_mount_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = true,
        }
        
        self.fight_mount_model:SetRenderTexUI3DModel(display_data)
        -- self.fight_mount_model:SetUI3DModel(self.node_list["fight_mount_display"].transform, nil, 1, true,
        --     MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.fight_mount_model)
    end

    if self.skill_level_attr_list == nil then
        self.skill_level_attr_list = {}
        local node_num = self.node_list["skill_attr"].transform.childCount
        for i = 1, node_num do
            self.skill_level_attr_list[i] = CommonAddAttrRender.New(self.node_list["skill_attr"]:FindObj("attr_" .. i))
        end
    end

    if not self.skill_level_cost_cell then
        self.skill_level_cost_cell = ItemCell.New(self.node_list["cost_item_cell"])
    end

    self.mount_skill_item_list = {}
    for i = 1, 4 do
        self.mount_skill_item_list[i] = NewFightMounSkillRender.New(self.node_list["mount_skill_" .. i])
        self.mount_skill_item_list[i]:SetIndex(i)
    end

    XUI.AddClickEventListener(self.node_list["lw_sj_btn"], BindTool.Bind(self.OnClickOpenLwsj, self))
    XUI.AddClickEventListener(self.node_list["skill_level_up_btn"], BindTool.Bind(self.OnClickSkillUpLevel, self))
    XUI.AddClickEventListener(self.node_list["right_move_back_btn"], BindTool.Bind(self.PlaySkillBackTween, self))
    XUI.AddClickEventListener(self.node_list["right_move_start_btn"], BindTool.Bind(self.PlaySkillStartTween, self))
    XUI.AddClickEventListener(self.node_list["skill_show_btn"], BindTool.Bind(self.OnClickSkillShow, self))
    XUI.AddClickEventListener(self.node_list["mount_skill_use_btn"], BindTool.Bind(self.OnClickSkillUse, self))
    XUI.AddClickEventListener(self.node_list["mount_skill_rest_btn"], BindTool.Bind(self.OnClickSkillRest, self))
end

function NewFightMountView:ReleaseCallBack()
    if self.fight_mount_type_list then
        self.fight_mount_type_list:DeleteMe()
        self.fight_mount_type_list = nil
    end

    if self.fight_mount_model then
        self.fight_mount_model:DeleteMe()
        self.fight_mount_model = nil
    end

    if self.skill_level_attr_list then
        for k, v in pairs(self.skill_level_attr_list) do
            v:DeleteMe()
        end
        self.skill_level_attr_list = nil
    end

    if self.skill_level_cost_cell then
        self.skill_level_cost_cell:DeleteMe()
        self.skill_level_cost_cell = nil
    end

    if self.mount_skill_item_list then
        for k, v in pairs(self.mount_skill_item_list) do
            v:DeleteMe()
        end
        self.mount_skill_item_list = nil
    end

    if self.play_state_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.play_state_timer)
    end
    self.play_state_timer = nil

    self.select_fight_mount_data = nil
    self.to_ui_param = nil
end

function NewFightMountView:ShowIndexCallBack()
    self:PlaySkillBackTween(true)

    self.play_state_timer = GlobalTimerQuest:AddDelayTimer(function()
        self:PlaySkillStartTween()
    end, 3.5)
end

function NewFightMountView:OnFlush(param_t, index)
    for k, v in pairs(param_t) do
        if k == "all" then
            self.to_ui_param = v.to_ui_param
            self:FlushMountTypeList()
            self:FlushMidInfoPanel()
            self:FlushSkillLevelPanel()
        end
    end
end

function NewFightMountView:FlushMountTypeList()
    local all_mount_cfg = NewFightMountWGData.Instance:GetAllFightMountTypeCfg()
    if IsEmptyTable(all_mount_cfg) then
        return
    end

    if self.fight_mount_type_list then
        self.fight_mount_type_list:SetDataList(all_mount_cfg)
    end

    local jump_index = 1
    if self.to_ui_param then
        local to_jump_index = tonumber(self.to_ui_param)
        for k, v in pairs(all_mount_cfg) do
            if to_jump_index == v.mount_seq then
                jump_index = k
                break
            end
        end

        self.fight_mount_type_list:JumpToIndex(jump_index)
    elseif not self.select_fight_mount_data then
        for i = 1, #all_mount_cfg do
            if NewFightMountWGData.Instance:GetSinggleMountRemind(all_mount_cfg[i].mount_seq) then
                jump_index = i
                break
            end
        end

        self.fight_mount_type_list:JumpToIndex(jump_index)
    end
end

function NewFightMountView:OnMountTypeHandler(item)
    if nil == item or nil == item.data then
        return
    end

    local cell_data = item.data
    if self.select_fight_mount_data and self.select_fight_mount_data == cell_data then
        return
    end

    self.select_fight_mount_data = cell_data

    self:FlushMidInfoPanel()
    self:FlushFightMountModel()
    self:FlushSkillLevelPanel()
end

function NewFightMountView:FlushMidInfoPanel()
    if self.select_fight_mount_data == nil then
        return
    end

    self.node_list.mount_name.text.text = self.select_fight_mount_data.mount_name or ""
    local mount_skill_level = NewFightMountWGData.Instance:GetMountSkillLevelBySeq(self.select_fight_mount_data
    .mount_seq)
    self.node_list.skill_level.text.text = mount_skill_level
    local is_remind = NewFightMountWGData.Instance:GetSinggleMountRemind(self.select_fight_mount_data.mount_seq)
    self.node_list.start_remind:SetActive(is_remind)

    -- 技能(不展示普攻)
    local skill_list = NewFightMountWGData.Instance:GetMainUISkillOrder(nil, self.select_fight_mount_data.mount_seq)
    local cur_level_cfg = NewFightMountWGData.Instance:GetUpSkillLevelCfg(self.select_fight_mount_data.mount_seq,
        mount_skill_level)
    local skill_info = {}
    if skill_list then
        for k, v in ipairs(skill_list) do
            local is_ultimate_skill = NewFightMountWGData.Instance:IsFightMountUltimateSkillId(v)
            local data = {}
            data.skill_id = v
            data.skill_level = cur_level_cfg.skill_level
            if is_ultimate_skill then
                table.insert(skill_info, 1, data)
            else
                table.insert(skill_info, data)
            end
        end
    end

    for k, v in ipairs(self.mount_skill_item_list) do
        v:SetData(skill_info[k])
    end

    local use_skill_id = NewFightMountWGData.Instance:GetUseSkillId()
    self.node_list.mount_skill_use_btn:SetActive(use_skill_id ~= self.select_fight_mount_data.mount_seq and
    mount_skill_level > 0)
    self.node_list.mount_skill_rest_btn:SetActive(use_skill_id == self.select_fight_mount_data.mount_seq)
end

function NewFightMountView:FlushSkillLevelPanel()
    if self.select_fight_mount_data == nil then
        return
    end

    self:FlushSkillLevelAttrAndCap()
    self:FlushSkillUpLevelInfo()
end

function NewFightMountView:FlushSkillLevelAttrAndCap()
    local mount_skill_level = NewFightMountWGData.Instance:GetMountSkillLevelBySeq(self.select_fight_mount_data
    .mount_seq)
    local level_attr = NewFightMountWGData.Instance:GetSkillLevelAttr(self.select_fight_mount_data.mount_seq,
        mount_skill_level)
    local need_show_attr_effect = false

    if nil ~= self.fight_mount_seq_cache and nil ~= self.fight_mount_level_cache then
        if (self.fight_mount_seq_cache == self.select_fight_mount_data.mount_seq) and (mount_skill_level - self.fight_mount_level_cache == 1) then
            need_show_attr_effect = true
        end
    end

    self.fight_mount_seq_cache = self.select_fight_mount_data.mount_seq
    self.fight_mount_level_cache = mount_skill_level

    for i = 1, #self.skill_level_attr_list do
        self.skill_level_attr_list[i]:SetData(level_attr[i])

        if need_show_attr_effect then
            self.skill_level_attr_list[i]:PlayAttrValueUpEffect()
        end

        if level_attr[i] and level_attr[i].attr_str == -100 then
            self.skill_level_attr_list[i]:ResetName(Language.NewFightMount.LwZhiLi, COLOR3B.D_GLOD)
        elseif level_attr[i] and level_attr[i].attr_str == -200 then
            self.skill_level_attr_list[i]:ResetName(Language.NewFightMount.YuLongTime, COLOR3B.D_GLOD)
        end
    end

    local cap = NewFightMountWGData.Instance:GetSkillLevelCap(self.select_fight_mount_data.mount_seq)
    self.node_list.skill_cap.text.text = cap
end

function NewFightMountView:FlushSkillUpLevelInfo()
    local mount_seq = self.select_fight_mount_data.mount_seq
    local mount_skill_level = NewFightMountWGData.Instance:GetMountSkillLevelBySeq(mount_seq)
    local cur_level_cfg = NewFightMountWGData.Instance:GetUpSkillLevelCfg(mount_seq, mount_skill_level)
    local next_level_cfg = NewFightMountWGData.Instance:GetUpSkillLevelCfg(mount_seq, mount_skill_level + 1)
    self.node_list.skill_cost_group:SetActive(next_level_cfg ~= nil)
    self.node_list.skill_level_max:SetActive(next_level_cfg == nil)
    if cur_level_cfg and next_level_cfg then
        self.skill_level_cost_cell:SetData({ item_id = cur_level_cfg.cost_item_id })
        local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
        local color = item_num >= cur_level_cfg.cost_item_num and COLOR3B.D_GREEN or COLOR3B.D_RED
        local up_level_str = item_num .. "/" .. cur_level_cfg.cost_item_num
        self.node_list.cost_item_num.text.text = ToColorStr(up_level_str, color)

        local hatch_item_info = DragonTempleWGData.Instance:GetHatchItemListInfoBySeq(mount_seq)
        local hatch_level = hatch_item_info and hatch_item_info.level or 0
        local color_1 = self.select_fight_mount_data.skill_need_level <= hatch_level and COLOR3B.D_GREEN or COLOR3B
        .D_RED
        local name_cfg = DragonTempleWGData.Instance:GetHatchNameCfgBySeq(mount_seq)
        local condition_str_1 = string.format(Language.NewFightMount.confition1, name_cfg and name_cfg.name,
            self.select_fight_mount_data.skill_need_level)
        self.node_list.skill_up_level_condition_1.text.text = ToColorStr(condition_str_1, color_1)

        local mount_level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(mount_seq)
        local color_2 = mount_level > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
        local condition_str_2 = string.format(Language.NewFightMount.confition2, self.select_fight_mount_data.mount_name)
        self.node_list.skill_up_level_condition_2.text.text = ToColorStr(condition_str_2, color_2)
    end

    local is_remind = NewFightMountWGData.Instance:GetSinggleUpLevelRemind(mount_seq)
    self.node_list.skill_level_up_remind:SetActive(is_remind)
    self.node_list.skill_up_level_tog_remind:SetActive(is_remind)
end

function NewFightMountView:FlushFightMountModel()
    if self.select_fight_mount_data == nil then
        return
    end

    local bundle, asset = ResPath.GetMountModel(self.select_fight_mount_data.appe_image_id or 0)
    self.fight_mount_model:SetMainAsset(bundle, asset, function()
        self.fight_mount_model:PlayMountAction()
    end)
end

function NewFightMountView:OnClickSkillUpLevel()
    if self.select_fight_mount_data == nil then
        return
    end

    local mount_seq = self.select_fight_mount_data.mount_seq
    local is_remind = NewFightMountWGData.Instance:GetSinggleUpLevelRemind(mount_seq)
    if is_remind then
        NewFightMountWGCtrl.Instance:SendCSNewFightMountOperateRequest(FIGHT_MOUNT2_OPERATE_TYPE.UPLEVEL, mount_seq)
    else
        TipWGCtrl.Instance:ShowSystemMsg(Language.NewFightMount.NoUpLevel)
    end
end

function NewFightMountView:PlaySkillBackTween(is_load)
    local time = is_load and 0 or 0.5
    UITween.CleanAllMoveToShowPanel(GuideModuleName.NewFightMountView)
    UITween.MoveToShowPanel(GuideModuleName.NewFightMountView, self.node_list["right_group"], Vector2(0, 0),
        Vector2(500, 0), time, DG.Tweening.Ease.Linear)
    UITween.MoveToShowPanel(GuideModuleName.NewFightMountView, self.node_list["skill_group"], Vector2(-107, 87),
        Vector2(0, 87), time, DG.Tweening.Ease.Linear)
    UITween.MoveToShowPanel(GuideModuleName.NewFightMountView, self.node_list["fight_mount_display"], Vector2(-136, -41),
        Vector2(0, -41), time, DG.Tweening.Ease.Linear, function()
        self.node_list["right_move_start_btn"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).alpha = 0
        self.node_list.right_move_start_btn:SetActive(true)
        UITween.AlphaShow(GuideModuleName.NewFightMountView, self.node_list["right_move_start_btn"], 0, 1, time)
    end)

    UITween.MoveToShowPanel(GuideModuleName.NewFightMountView, self.node_list["name_bg"], Vector2(-455, -224),
        Vector2(-134, -224), time, DG.Tweening.Ease.Linear)
end

function NewFightMountView:PlaySkillStartTween()
    if self.play_state_timer ~= nil then
        GlobalTimerQuest:CancelQuest(self.play_state_timer)
    end
    self.play_state_timer = nil

    UITween.CleanAllMoveToShowPanel(GuideModuleName.NewFightMountView)
    self.node_list.right_move_start_btn:SetActive(false)
    UITween.MoveToShowPanel(GuideModuleName.NewFightMountView, self.node_list["right_group"], Vector2(500, 0),
        Vector2(0, 0), 0.5, DG.Tweening.Ease.Linear)

    UITween.MoveToShowPanel(GuideModuleName.NewFightMountView, self.node_list["skill_group"], Vector2(0, 87),
        Vector2(-107, 87), 0.5, DG.Tweening.Ease.Linear)
    UITween.MoveToShowPanel(GuideModuleName.NewFightMountView, self.node_list["fight_mount_display"], Vector2(0, -41),
        Vector2(-136, -41), 0.5, DG.Tweening.Ease.Linear)
    UITween.MoveToShowPanel(GuideModuleName.NewFightMountView, self.node_list["name_bg"], Vector2(-134, -224),
        Vector2(-455, -224), 0.5, DG.Tweening.Ease.Linear)
end

function NewFightMountView:OnClickSkillShow()
    if self.select_fight_mount_data == nil then
        return
    end

    local mount_seq = self.select_fight_mount_data.mount_seq
    local mount_skill_level = NewFightMountWGData.Instance:GetMountSkillLevelBySeq(mount_seq)
    local data = {}
    data.mount_seq = mount_seq
    data.skill_level = mount_skill_level > 0 and mount_skill_level or 1

    CommonSkillShowCtrl.Instance:SetFightMountSkillViewDataAndOpen(data)
end

function NewFightMountView:OnClickSkillUse()
    if self.select_fight_mount_data == nil then
        return
    end

    NewFightMountWGCtrl.Instance:SendCSNewFightMountOperateRequest(FIGHT_MOUNT2_OPERATE_TYPE.USE_SKILL,
        self.select_fight_mount_data.mount_seq)
end

function NewFightMountView:OnClickSkillRest()
    if self.select_fight_mount_data == nil then
        return
    end

    NewFightMountWGCtrl.Instance:SendCSNewFightMountOperateRequest(FIGHT_MOUNT2_OPERATE_TYPE.USE_SKILL, -1)
end

function NewFightMountView:OnClickOpenLwsj()
    NewFightMountWGCtrl.Instance:OpenLwsjView()
end

-- 使用特效
function NewFightMountView:PlayUseEffect(effect_name)
    TipWGCtrl.Instance:ShowEffect({
        effect_type = effect_name,
        is_success = true,
        pos = Vector2(0, 0),
        parent_node = self.node_list["play_effect_pos"]
    })
end

----------NewFightMountTypeRender----------
local NameTextColor = {
    [0] = Color.New(168 / 255, 227 / 255, 1, 1),
    [1] = Color.New(254 / 255, 201 / 255, 201 / 255, 1),
    [2] = Color.New(255 / 255, 234 / 255, 188, 1),
    [3] = Color.New(167 / 255, 254 / 255, 241 / 255, 1),
    [4] = Color.New(237 / 255, 201 / 255, 254 / 255, 1),
}

local effect_name = {
    [0] = "UI_zdzq_lan",
    [1] = "UI_zdzq_hong",
    [2] = "UI_zdzq_jin",
    [3] = "UI_zdzq_hei",
    [4] = "UI_zdzq_bai",
}
NewFightMountTypeRender = NewFightMountTypeRender or BaseClass(BaseRender)
function NewFightMountTypeRender:__init()

end

function NewFightMountTypeRender:__delete()

end

function NewFightMountTypeRender:OnFlush()
    if not self.data then
        return
    end

    local data = self.data

    local bundle, asset = ResPath.GetNewFightMountImg("a2_zdzq_type_" .. data.mount_seq)
    self.node_list.bg.image:LoadSprite(bundle, asset, function()
        self.node_list.bg.image:SetNativeSize()
    end)

    self.node_list.name.text.text = data.mount_name
    self.node_list.name.text.color = NameTextColor[data.mount_seq] or COLOR3B.WHITE


    local mount_appimg_level = NewFightMountWGData.Instance:GetAppearanceLevelBySeq(data.mount_seq)
    local use_mount_id = NewFightMountWGData.Instance:GetUseMountId()

    self.node_list.use_img:SetActive(use_mount_id == data.mount_seq)
    self.node_list.no_act_img:SetActive(mount_appimg_level <= 0)

    local is_remind = NewFightMountWGData.Instance:GetSinggleMountRemind(data.mount_seq)
    self.node_list.remind:SetActive(is_remind)

    local effect_bundle, effect_asset = ResPath.GetA2Effect(effect_name[data.mount_seq])
    if effect_bundle and effect_asset then
        self.node_list.effect:ChangeAsset(effect_bundle, effect_asset)
    end
end

function NewFightMountTypeRender:OnSelectChange(is_select)
    if not self.data then
        return
    end

    self.node_list.select_img:SetActive(is_select)
end

-----------------战斗坐骑技能------------------------
NewFightMounSkillRender = NewFightMounSkillRender or BaseClass(BaseRender)
function NewFightMounSkillRender:LoadCallBack()
    XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClickSkill, self))
end

function NewFightMounSkillRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local icon_id = SkillWGData.Instance:GetSkillIconId(self.data.skill_id)
    local bundle, name = ResPath.GetSkillIconById(icon_id)
    self.node_list.icon.image:LoadSprite(bundle, name)
end

function NewFightMounSkillRender:OnClickSkill()
    if IsEmptyTable(self.data) then
        return
    end

    local skill_level = self.data.skill_level
    local data = SkillWGData.Instance:GetFightMountSkillCfg(self.data.skill_id)
    if not data then
        return
    end

    local limit_text = ""
    local next_skill_desc = ""
    local is_max = true
    if skill_level <= 0 then
        limit_text = Language.NewFightMount.SkillNoAct
        skill_level = 1 -- 强制显示1级属性
    end

    local skill_info = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id, skill_level)
    local next_skill_info = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id, skill_level + 1)
    if next_skill_info then
        next_skill_desc = next_skill_info.description
        is_max = false
    end

    if skill_info then
        local skill_name = data.skill_name or ""
        local show_data = {
            icon = skill_info.icon_resource,
            top_text = skill_name,
            body_text = skill_info.description,
            skill_id = skill_info.skill_id,
            x = 0,
            y = 0,
            set_pos2 = true,
            skill_level = skill_level,
            limit_text = limit_text,
        }

        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end
