QuanMinBeiZhanWGData = QuanMinBeiZhanWGData or BaseClass()
function QuanMinBeiZhanWGData:__init()
	if QuanMinBeiZhanWGData.Instance then
		ErrorLog("[QuanMinBeiZhanWGData] Attemp to create a singleton twice !")
	end
	QuanMinBeiZhanWGData.Instance = self
	self.theme_cfg_list = {}

	--龙魂冲榜信息
	self.longhun_rank_log_list = {}
	self.longhun_rank_role_info = {}
	self.longhunrank_join_flag = {}
	self.longhunrank_join_reward_flag = {}

	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan, BindTool.Bind(self.IsShowMainViewRedPoint, self))
	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_Login, BindTool.Bind(self.IsShowLoginRedPoint, self))

	RemindManager.Instance:Register(RemindName.QuanMinBei<PERSON><PERSON>_<PERSON>hou<PERSON><PERSON>, BindTool.Bind(self.IsShowShouChongRedPoint, self))
	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_Lei<PERSON>hong, BindTool.Bind(self.IsShowLeiChongRedPoint, self))

	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_LaiXi, BindTool.Bind(self.IsShowLaiXiRedPoint, self))
	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_HaoLi, BindTool.Bind(self.IsShowHaoLiRedPoint, self))
	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_JuanXian, BindTool.Bind(self.IsShowJuanXianRedPoint, self))
	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_LongHun, BindTool.Bind(self.IsShowLongHunRedPoint, self))
	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_Cap, BindTool.Bind(self.IsShowRoleCapRedPoint, self))
	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_Cap2, BindTool.Bind(self.IsShowServerCapRedPoint, self))
	--龙魂冲榜
	RemindManager.Instance:Register(RemindName.QuanMinBeiZhan_LongHunRank, BindTool.Bind(self.CheckLongHunRankRemind, self))

	local banzhan_all_cfg = ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto")
	self.long_hun_chong_bang_rank_cfgs = ListToMap(banzhan_all_cfg.longhun_chongbang_rank, "rank")
	self.long_hun_chong_bang_award_cfgs = ListToMap(banzhan_all_cfg.longhun_chongbang_reward, "ID")
	self:InitLongHunRankCfgList()
end

function QuanMinBeiZhanWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_Login)

	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_ShouChong)
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_LeiChong)

	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan)
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_LaiXi)
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_HaoLi)
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_JuanXian)
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_LongHun)
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_Cap)
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_Cap2)
	RemindManager.Instance:UnRegister(RemindName.QuanMinBeiZhan_LongHunRank)

	self.longhun_rank_cfg_list = nil
	QuanMinBeiZhanWGData.Instance = nil
end

function QuanMinBeiZhanWGData:GetLoginRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").login_reward
end

function QuanMinBeiZhanWGData:GetActivityThemeCfgList()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").beizhan_theme_dec
end

function QuanMinBeiZhanWGData:GetActivityThemeOtherCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").beizhan_theme_other
end

function QuanMinBeiZhanWGData:SetThemeCfgByTabIndex(tab_index, cfg)
	self.theme_cfg_list[tab_index] = cfg
end

function QuanMinBeiZhanWGData:GetThemeCfgByTabIndex(tab_index)
	return self.theme_cfg_list[tab_index]
end

function QuanMinBeiZhanWGData:GetActivityName(real_id)
	local cfg = self:GetActivityThemeCfgList()
	for k,v in ipairs(cfg) do
		if v.real_id == real_id then
			return v.active_name
		end
	end
	return ""
end

function QuanMinBeiZhanWGData:GetActivityDefultCfg(real_id)
	local cfg = self:GetActivityThemeCfgList()
	for k,v in ipairs(cfg) do
		if v.real_id == real_id then
			return v
		end
	end
	return nil
end

--获取活动界面配置
function QuanMinBeiZhanWGData:GetActivityThemeCfg(index)
	return self:GetThemeCfgByTabIndex(index)
end

--获取活动提示
function QuanMinBeiZhanWGData:GetActivityTip(index)
	local cfg = self:GetThemeCfgByTabIndex(index)
	if cfg ~= nil then
		return cfg.tab_name, cfg.rule_desc
	end
	return nil,nil
end

------------------------------------  登录奖励 -------------------------------------------
-- 获取登录奖励配置
function QuanMinBeiZhanWGData:GetLoginRewardCfgByDay(day)
	local cfg = self:GetLoginRewardCfg()
	for k,v in pairs(cfg) do
		if day == v.day_index then
			return v
		end
	end
end

function QuanMinBeiZhanWGData:SetLoginRewardInfo(protocol)
	if self.LoginDayData == nil then
		self.LoginDayData = {}
	end
	self.LoginDayData.day_list = {}
	self.LoginDayData.activity_day_index = protocol.activity_day_index
	self.LoginDayData.len = 0
	local cfg = self:GetLoginRewardCfg()
	for k,v in pairs(cfg) do
		local data = {}
		data.index = v.day_index
		data.vip_level = v.vip_lv or 0
		data.common_gift_state = protocol.reward_state[v.day_index].common_gift_state 	--是否领取
		data.special_gift_state = protocol.reward_state[v.day_index].special_gift_state
		table.insert(self.LoginDayData.day_list,data)
		self.LoginDayData.len = self.LoginDayData.len + 1
	end
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_Login)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

function QuanMinBeiZhanWGData:SetSelectDayItem(item, select_bg)
	self.cur_day_item_select = item
	self.cur_day_item_select_bg = select_bg
end

-- vip等级变化后端不推的
function QuanMinBeiZhanWGData:FlushLoginRewardInfo()
	if self.LoginDayData then
		local vip_level = VipWGData.Instance:GetRoleVipLevel()
		local cur_day = self.LoginDayData.activity_day_index or 0
		local day_list = self.LoginDayData.day_list or {}
		for i=1,#day_list do
			if day_list[i].special_gift_state == 0 and cur_day >= day_list[i].index and
				day_list[i].vip_level > 0 and vip_level >= day_list[i].vip_level then
				day_list[i].special_gift_state = 1
			end
		end
		self.LoginDayData.day_list = day_list
	end
end

function QuanMinBeiZhanWGData:GetLoginRewardInfo(day_index)
	if self.LoginDayData then
		return self.LoginDayData.day_list[day_index]
	end
end

function QuanMinBeiZhanWGData:GetLoginDayData()
	return self.LoginDayData
end

function QuanMinBeiZhanWGData:GetLoginDyaIndex()
	if self.LoginDayData == nil or self.LoginDayData.activity_day_index == nil then
		return 0
	end
	return self.LoginDayData.activity_day_index
end

function QuanMinBeiZhanWGData:GetCurSelectDay() 
	local day_index = self:GetFirstHasRewardDayIndex()
	if day_index == 0 and self.LoginDayData then
		if self:GetLoginRewardCfgByDay(self.LoginDayData.activity_day_index + 1)  then
			day_index = self.LoginDayData.activity_day_index + 1
		else
			day_index = self.LoginDayData.activity_day_index
		end
	end
	return day_index
end

function QuanMinBeiZhanWGData:GetFirstHasRewardDayIndex()
	if self.LoginDayData ~= nil and self.LoginDayData.day_list ~= nil then
		local cur_vip_level = VipWGData.Instance:GetRoleVipLevel()
		for k,v in ipairs(self.LoginDayData.day_list) do
			local cfg = self:GetLoginRewardCfgByDay(v.index)
			if v.common_gift_state == ActivityRewardState.KLQ then
				return k
			end

			if v.special_gift_state == ActivityRewardState.KLQ then
				return k
			else
				if v.special_gift_state == ActivityRewardState.BKL and v.index <= self.LoginDayData.activity_day_index then
					if cfg.vip_lv ~= "" and cfg.vip_lv > 0 and cur_vip_level >= cfg.vip_lv then
						return k
					end
				end
			end
		end
	end
	return 0
end



function QuanMinBeiZhanWGData:GetDyaItemSelect()
	return self.cur_day_item_select, self.cur_day_item_select_bg
end

-------------------------------充值---------------------------------
function QuanMinBeiZhanWGData:GetShouChongRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").shouchong_reward
end

function QuanMinBeiZhanWGData:GetLeiChongRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").leichonghaoli_reward
end

function QuanMinBeiZhanWGData:SetShouChongInfo(protocol)
	if self.ShouChongData == nil then
		self.ShouChongData = {}
	end
	self.ShouChongData.gift_state = protocol.gift_state -- 首充奖励领取状态
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_ShouChong)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

function QuanMinBeiZhanWGData:SetLeiChongInfo(protocol)
	if self.LeiChongData == nil then
		self.LeiChongData = {}
	end
	self.LeiChongData.cur_xianyu = protocol.cur_xianyu
	self.LeiChongData.cur_dangwei = protocol.cur_dangwei
	self.LeiChongData.dangwei_num = protocol.dangwei_num
	self.LeiChongData.dangwei_info = protocol.dangwei_info

	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_LeiChong)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

--获取累充奖励排行数据
function QuanMinBeiZhanWGData:GetLeiChongRewardList()
	local list = {}
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		local data = {}
		data.cfg = v
		data.ID = v.ID
		data.receive_state = self:GetLeiChongReceiveState(v.ID)
		if data.receive_state == TianShenRoadRewardState.KLQ then
			data.sort = 0
		elseif data.receive_state == TianShenRoadRewardState.BKL then
			data.sort = 1
		else
			data.sort = 2
		end
		table.insert(list, data)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort", "ID"))
	return list
end

-- 获取每日首充奖励配置
function QuanMinBeiZhanWGData:GetShouChongRewardCfgByDay(day)
	local cfg = self:GetShouChongRewardCfg()
	for k,v in pairs(cfg) do
		if day == v.day_index then
			return v
		end
	end
	return nil
end

--首充领取状态
function QuanMinBeiZhanWGData:GetShouChongGiftState()
	if self.ShouChongData ~= nil then
		return self.ShouChongData.gift_state
	end
	return 0
end

--当前充值仙玉
function QuanMinBeiZhanWGData:GetOwnXianYu()
	if self.LeiChongData ~= nil then
		return self.LeiChongData.cur_xianyu
	end
	return 0
end

--获取当前档需要充值的仙玉
function QuanMinBeiZhanWGData:GetNeedRechargeXianYU()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	for i,v in ipairs(cfg) do
		if cur_xianyu < v.stage_value then
			local pre_stage_value = 0
			if i > 1 then
				pre_stage_value = cfg[i-1].stage_value
			end
			return v.stage_value - cur_xianyu, v.stage_value, pre_stage_value
		end
	end
	return 0, cfg[#cfg].stage_value, cfg[#cfg].stage_value
end

function QuanMinBeiZhanWGData:GetLeiChongProgress(leichong_value)  --亲密度进度条
	local cur_progress = 0
	local cfg = self:GetLeiChongRewardCfg()
	local cur_leichong_value = tonumber(leichong_value)
	if next(cfg) == nil or leichong_value == nil then
		return cur_progress
	end
	local progress_list = {0.1, 0.3, 0.52, 0.74, 1}			--对应的进度条值

	for k, v in pairs(progress_list) do
		local seq = k - 1
		local length = #progress_list
		local cur_need = cfg[seq] and cfg[seq].stage_value or 0
		local next_need = cfg[seq + 1] and cfg[seq + 1].stage_value or cfg[#cfg].stage_value
		local cur_value = progress_list[seq] and progress_list[seq] or 0
		local next_value = progress_list[seq + 1] and progress_list[seq + 1] or progress_list[length]
		if leichong_value > cur_need and leichong_value <= next_need then
			cur_progress = (leichong_value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif leichong_value > cfg[#cfg].stage_value then
			cur_progress = progress_list[length]
			break
		end
	end
	return cur_progress
end

--是否已达成
function QuanMinBeiZhanWGData:IsRechargeTargetFinish()
	local cur_xianyu = self:GetOwnXianYu()
	local cfg = self:GetLeiChongRewardCfg()
	return cur_xianyu >= cfg[#cfg].stage_value
end

--获取领取状态
function QuanMinBeiZhanWGData:GetLeiChongReceiveState(index)
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil and self.LeiChongData.dangwei_info[index] ~= nil then
		return self.LeiChongData.dangwei_info[index]
	else
		return 0
	end
end

---------------------------------多倍 -------------------------------------

function QuanMinBeiZhanWGData:GetDuoBeiCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").duobeijiangli
end

function QuanMinBeiZhanWGData:SetDuoBeiInfo(protocol)
	self.duobei_data = protocol
	MainuiWGCtrl.Instance:FlushDuoBei()
end

function QuanMinBeiZhanWGData:GetDuoBeiInfo()
	if self.duobei_data ~= nil then
		local cfg = self:GetDuoBeiCfg()
		local list = {}
		for k,v in pairs(cfg) do
			if v.day == self.duobei_data.day then
				local info = {}
				info.cfg = v
				info.cur_finish_num = 0
				if self.duobei_data.task_info[v.task_type] then
					info.cur_finish_num = self.duobei_data.task_info[v.task_type].cur_finish_num
				end
				table.insert(list, info)
			end
		end
		return list
	end
	return nil
end

--获取副本是否有多倍活动开启
function QuanMinBeiZhanWGData:GetHasDuoBeiInCopy()
	for i=RATSDUOBEI_TASK.FANRENXIUXIAN, RATSDUOBEI_TASK.HAIDIFEIXU do
		if self:GetDuoBeiTimes(i) > 0 then
			return true
		end
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.WABAO) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANGUDIAN) > 0 then
		return true
	end
	
	return false
end

--获取魔王是否有boss开启
function QuanMinBeiZhanWGData:GetHasDuoBeiInBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.SHIJIEMOWANG) > 0 then
		return true
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.MOWANGCHAOXUE) > 0 then
		return true
	end 

	return false 
end

--获取世界服是否有boss开启
function QuanMinBeiZhanWGData:GetHasDuoBeiInWorldBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.HONGMENGSHENYU) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANSHENSHOU) > 0 then
		return true
	end

	return false 
end

function QuanMinBeiZhanWGData:GetDuoBeiTimes(task_type)
	if self.duobei_data ~= nil and self:GetActivityState(TabIndex.quanmin_beizhan_duobei) then
		local cfg = self:GetDuoBeiCfg()
		if cfg ~= nil then
			for k,v in pairs(cfg) do
				if v.day == self.duobei_data.day and v.task_type == task_type then
					if FunOpen.Instance:GetFunIsOpened(v.module_name) then
						return v.reward_mult
					end
				end
			end
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:GetItemDuoBeiTimes(task_type , item_id)
	if self.duobei_data ~= nil and self:GetActivityState(TabIndex.quanmin_beizhan_duobei) then
		local cfg = self:GetDuoBeiCfg()
		if not cfg then
			return 0
		end
		
		for k,v in pairs(cfg) do
			if v.day == self.duobei_data.day and v.task_type == task_type then
				for k2,v2 in pairs(v.reward_item) do
					if v2.item_id == item_id then
						return v.reward_mult
					end
				end
			end
		end
	end
	return 0
end

------------------------------------- 我要龙魂 --------------------------------------------
function QuanMinBeiZhanWGData:GetLHTaskCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").woyaolonghun_task
end

function QuanMinBeiZhanWGData:GetLHGiftCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").woyaolonghun_libao
end

function QuanMinBeiZhanWGData:GetLHRewardCfg()
	if self.lh_reward_cfg == nil then
		self.lh_reward_cfg = ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").woyaolonghun_stage_reward
	end
	return self.lh_reward_cfg
end

function QuanMinBeiZhanWGData:GetLHInfo()
	return self.LHData
end

function QuanMinBeiZhanWGData:GetLHTaskList()
	local list = {}
	local cfg = self:GetLHTaskCfg()
	for i,v in ipairs(cfg) do
		local item = {}
		item.ID = v.ID
		item.cfg = v
		item.receive_state = self:GetLHTaskState(v.ID)
		if item.receive_state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.receive_state == ActivityRewardState.BKL then
			item.sort = 1
		else
			item.sort = 2
		end
		table.insert(list, item)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort","ID"))
	return list
end

function QuanMinBeiZhanWGData:GetLHTaskState(task_id)
	if self.LHData ~= nil then
		for k,v in pairs(self.LHData.task_list) do
			if v.task_id == task_id then
				return v.gift_state
			end
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:SetLHInfo(protocol)
	self.LHData = {}
	self.LHData.dangwei_jifen = protocol.dangwei_jifen
	self.LHData.buy_libao_cnt = protocol.buy_libao_cnt
	self.LHData.longhun_frag_num = protocol.longhun_frag_num
	self.LHData.longhun_hecheng_state = protocol.longhun_hecheng_state

	self.LHData.dangwei_state = protocol.dangwei_state
	self.LHData.task_list = protocol.task_list
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_LongHun)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

function QuanMinBeiZhanWGData:GetLongHunComposeState()
	if self.LHData ~= nil then
		return self.LHData.longhun_hecheng_state  == ActivityRewardState.YLQ
	end
	return false
end

function QuanMinBeiZhanWGData:GetLHGiftIndex()
	if self.LHData ~= nil and self.LHData.buy_libao_cnt ~= nil then
		return self.LHData.buy_libao_cnt
	end
	return 0
end

--获取当前可以购买的礼包，全部买完展示最后一个
function QuanMinBeiZhanWGData:GetCanBuyGiftData()
	local cfg = self:GetLHGiftCfg()
	if self.LHData.buy_libao_cnt < #cfg then
		return cfg[self.LHData.buy_libao_cnt + 1], false
	else
		return cfg[#cfg], true
	end
end

function QuanMinBeiZhanWGData:GetDangweiScore()
	if self.LHData ~= nil then
		return self.LHData.dangwei_jifen
	end
	return 0
end

function QuanMinBeiZhanWGData:GetLongHunTaskParamNum(index)
	if self.LHData ~= nil and self.LHData.task_list ~= nil then
		return self.LHData.task_list[index].task_param
	end
	return 0
end

--获取最小未领取的阶段奖励
function QuanMinBeiZhanWGData:GetMinBXReward()
	if self.LHData ~= nil and self.LHData.dangwei_state ~= nil then
		local reward_cfg = self:GetLHRewardCfg()
		for i=1,#reward_cfg do
			if self.LHData.dangwei_state[i] ~= nil and self.LHData.dangwei_state[i] ~=2 then
				return i
			end
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:IsLastBX(id)
	local reward_cfg = self:GetLHRewardCfg()
	if reward_cfg ~= nil then
		return id == #reward_cfg
	end
	return false
end

function QuanMinBeiZhanWGData:IsNeedScroll(box_index)
	local reward_cfg = self:GetLHRewardCfg()
	if reward_cfg ~= nil then
		return (#reward_cfg - box_index) >= 5
	end
	return false
end

function QuanMinBeiZhanWGData:GetBXState(id)
	if self.LHData ~= nil and self.LHData.dangwei_state ~= nil then
		return self.LHData.dangwei_state[id]
	end
	return 0
end

function QuanMinBeiZhanWGData:GetBXTargetScore(id)
	local reward_cfg = self:GetLHRewardCfg()
	if reward_cfg ~= nil and reward_cfg[id] ~= nil then
		return reward_cfg[id].jifen
	end
	return 0
end

function QuanMinBeiZhanWGData:GetLHSuiPianCfg()
	local scompose_cfg = ConfigManager.Instance:GetAutoConfig("compose_auto").compose_list
	local other_cfg = self:GetActivityThemeOtherCfg()
	if other_cfg ~= nil and scompose_cfg ~= nil then
		return scompose_cfg[other_cfg[1].lh_frag_hecheng_seq], other_cfg[1]
	end
	return nil
end

-------------------------------------- 刑天来袭（降临） --------------------------------------
function QuanMinBeiZhanWGData:GetJiangLinRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi_cfg_auto").reward_config
end

function QuanMinBeiZhanWGData:GetJiangLinFlushTimeCfg()
	return ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi_cfg_auto").refresh_time
end

function QuanMinBeiZhanWGData:GetJiangLinOtherCfg(key)
	if key then
		local other_cfg = ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi_cfg_auto").other
		if other_cfg and other_cfg[1] then
			return other_cfg[1][key]
		end
	end
	return ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi_cfg_auto").other
end

function QuanMinBeiZhanWGData:GetJiangLinMonster_cfg()
	return ConfigManager.Instance:GetAutoConfig("randact_xingtianlaixi_cfg_auto").monster_refresh_cfg
end

function QuanMinBeiZhanWGData:GetJingLinData()
	return self.JiangLinData
end

function QuanMinBeiZhanWGData:SetXTLXInfo(protocol)
	if self.JiangLinData == nil then
		self.JiangLinData = {}
	end
	
	self.JiangLinData.world_level = protocol.world_lv
	self.JiangLinData.pass_times = protocol.pass_times
	self.JiangLinData.day_pass_times = protocol.day_pass_times
end

--刷新刑天来袭刷新数据
function QuanMinBeiZhanWGData:SetXTLXFlushInfo(protocol)
	if self.JiangLinData == nil then
		self.JiangLinData = {}
	end

	self.JiangLinData.next_change_time = protocol.next_change_time
    self.JiangLinData.cur_status = protocol.cur_status
    self.JiangLinData.refresh_times = protocol.refresh_times
	self.JiangLinData.is_final_boss_skill = protocol.is_final_boss_skill
end

function QuanMinBeiZhanWGData:SetXTLXFinishInfo(protocol)
	local finish_info = {}
	local reward_list = {}
	local partipate_list = protocol.partipate_list
	for i=1,#partipate_list do
		if partipate_list[i].item_id > 0 then
			reward_list[#reward_list + 1] = partipate_list[i]
		end
	end

	finish_info.partipate_list = reward_list
	finish_info.fall_item_list = protocol.fall_item_list

	self.xtlx_finish_info = finish_info
end

function QuanMinBeiZhanWGData:GetXTLXFinishInfo()
	return self.xtlx_finish_info
end

function QuanMinBeiZhanWGData:GetIsFinalBossSkill()
	return self.JiangLinData.is_final_boss_skill
end

function QuanMinBeiZhanWGData:GetQuanMinBeiZhanIsClose()
	if self.JiangLinData and self.JiangLinData.is_final_boss_skill then
		return self.JiangLinData.is_final_boss_skill ~= 0
	end

	return true
end
 
function QuanMinBeiZhanWGData:GetDayPassTimes()
	if self.JiangLinData ~= nil and self.JiangLinData.day_pass_times ~= nil then
		return self.JiangLinData.day_pass_times
	end
	return 0
end

function QuanMinBeiZhanWGData:GetJiangLinReward()
	local cfg = nil
	if self.JiangLinData and self.JiangLinData.world_level then
		local reward_cfg = self:GetJiangLinRewardCfg()
		for k,v in pairs(reward_cfg) do
			if self.JiangLinData.world_level >= v.min_lv and self.JiangLinData.world_level <= v.max_lv and self.JiangLinData.day_pass_times >= v.do_times_limit then
				cfg = v --取最后一个符合的数据
			end
		end
	end
	return cfg
end

function QuanMinBeiZhanWGData:XingTianLaiXiActivityState(state, next_time)
	if self.JiangLinData == nil then
		self.JiangLinData = {}
	end
	self.JiangLinData.activity_state = state
	self.JiangLinData.next_time = next_time
	
	if self:IsInXingTianLaiXiActivity() then
		ActivityWGCtrl.Instance:OpenActivityNoticeView(ACTIVITY_TYPE.XINGTIANLAIXI)
	end
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_LaiXi)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

--判断是否在活动开启时间内
function QuanMinBeiZhanWGData:IsInXingTianLaiXiActivity()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN)
	if act_cfg then
		local role_level = RoleWGData.Instance:GetAttr("level")
		if role_level < act_cfg.level or role_level > act_cfg.level_max then
			return false
		end
	end

	if self.JiangLinData ~= nil and self.JiangLinData.activity_state ~= nil then
		if self.JiangLinData.activity_state == ACTIVITY_STATUS.OPEN then
			return true
		end
	end
	return false
end

function QuanMinBeiZhanWGData:GetXTLXOpenTime()
	if self.time_pramstr == nil then
		self.time_pramstr = {}
		local cfg = self:GetJiangLinFlushTimeCfg()
		local index = 1
		if cfg ~= nil then
			for k,v in pairs(cfg) do
				local date = {}
				local arr = {}
				for i=1,4 do
					arr[i] = tonumber(string.sub(v.refresh_time,i,i))
				end
				date.hour = arr[1] * 10 + arr[2]
				date.min = arr[3] * 10 + arr[4]
				table.insert(self.time_pramstr, date)
			end
		end
	end

	return self.time_pramstr
end

--------------------------- 全民捐献 --------------------------------------

function QuanMinBeiZhanWGData:GetQFJXDayRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").qfjx_server_day_reward
end

function QuanMinBeiZhanWGData:GetQFRewardTypeCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").qfjx_reward_type
end

function QuanMinBeiZhanWGData:GetQFRoleRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").qfjx_role_lj_reward
end

function QuanMinBeiZhanWGData:GetQFRewardTypeCfgForIndex(index)
	local cfg = self:GetQFRewardTypeCfg()
	for i,v in ipairs(cfg) do
		if v.type == index then
			return v
		end
	end
	return nil
end

function QuanMinBeiZhanWGData:GetQFJXDayRewardCfgMaxCount()
	local cfg = self:GetQFJXDayRewardCfg()
	if cfg ~= nil then
		return #cfg
	end
	return 0
end

function QuanMinBeiZhanWGData:SetQuanFuJuanXianInfo(protocol)
	if self.JuanXianData == nil then
		self.JuanXianData = {}
	end
	self.JuanXianData.quanfu_juanxian_num = protocol.quanfu_juanxian_num        --全服捐献次数 
	self.JuanXianData.role_juanxian_num = protocol.role_juanxian_num			--个人捐献次数
	self.JuanXianData.server_reward_state_list = protocol.server_reward_state_list
	self.JuanXianData.role_reward_state_list = protocol.role_reward_state_list
	self.JuanXianData.server_reward_time_list = protocol.server_reward_time_list

	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_JuanXian)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

--获取全服礼包奖励状态
function QuanMinBeiZhanWGData:GetGiftState(index)
	if self.JuanXianData ~= nil and self.JuanXianData.server_reward_state_list ~= nil then
		return self.JuanXianData.server_reward_state_list[index]
	end
	return 0
end

--获取有多少个激活
function QuanMinBeiZhanWGData:GetActiveNum()
	local max_count = #self:GetQFJXDayRewardCfg()
	if max_count > 0 then
		local active_num = 0
		if self.JuanXianData ~= nil and self.JuanXianData.server_reward_state_list ~= nil then
			for i,v in ipairs(self.JuanXianData.server_reward_state_list) do
				if v > 0 then
					active_num = active_num + 1
				end
			end
		end
		return active_num, max_count
	end
	
	return 0, 1
end

--获取全服奖励生效时间
function QuanMinBeiZhanWGData:GetRewardTime(index)
	if self.JuanXianData ~= nil and self.JuanXianData.server_reward_time_list ~= nil then
		return self.JuanXianData.server_reward_time_list[index]
	end
	return 0
end

--个人奖励状态
function QuanMinBeiZhanWGData:GetRoleRewardState(index)
	if self.JuanXianData ~= nil and self.JuanXianData.role_reward_state_list ~= nil then
		return self.JuanXianData.role_reward_state_list[index]
	end
	return 0
end

--获取全服捐献次数和个人捐献次数
function QuanMinBeiZhanWGData:GetServerJuanXianCount()
	if self.JuanXianData ~= nil then
		return self.JuanXianData.quanfu_juanxian_num
	end
	return 0
end

--获取全服捐献次数和个人捐献次数
function QuanMinBeiZhanWGData:GetRoleJuanXianCount()
	if self.JuanXianData ~= nil then
		return self.JuanXianData.role_juanxian_num
	end
	return 0
end

--获取当前节点对应的百分比
function QuanMinBeiZhanWGData:IsActiveServerJXReward(index)
	local cfg = self:GetQFJXDayRewardCfg()
	local server_count = self:GetServerJuanXianCount()
	local need_count = 0
	local cur_count = 0
	if index == 1 then
		need_count = cfg[index].server_day_juanxian_num
		cur_count = server_count
	else
		need_count = cfg[index].server_day_juanxian_num - cfg[index-1].server_day_juanxian_num
		cur_count = server_count - cfg[index-1].server_day_juanxian_num
	end

	if cur_count > 0 then
		local rate = cur_count / need_count
		return rate > 1 and 1 or rate
	else
		return 0
	end
end
--------------------------------------------------------------

--------------------------- 备战好礼 ----------------------------------
function QuanMinBeiZhanWGData:GetHaoLiCfg( ... )
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").beizhanhaoli
end

function QuanMinBeiZhanWGData:SetBeiZhanHaoLiInfo(protocol)
	--print_error("----------------SetBeiZhanHaoLiInfo ------------------")
	if self.HaoLiData == nil then
		self.HaoLiData = {}
	end
	self.old_cur_day = self.HaoLiData.cur_day
	self.HaoLiData.cur_day = protocol.cur_day
	self.HaoLiData.buy_limit_type = protocol.buy_limit_type
	self.HaoLiData.libao_state = protocol.libao_statu
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_HaoLi)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

--获取页签主题
function QuanMinBeiZhanWGData:GetHaoLiTabName()
	local cfg = self:GetHaoLiCfg()
	local tab_name_list = {}
	if cfg ~= nil then
		for i,v in ipairs(cfg) do
			if tab_name_list[v.theme] == nil then
				if self.HaoLiData ~= nil and v.theme <= self.HaoLiData.cur_day then
					tab_name_list[v.theme] = v.tab_name
				else
					tab_name_list[v.theme] = "????"
				end
			end
		end
	end
	return tab_name_list
end

function QuanMinBeiZhanWGData:GetHaoLiDayNum()
	if self.HaoLiData ~= nil and self.HaoLiData.cur_day ~= nil then
		return self.HaoLiData.cur_day
	end
	return 0
end

function QuanMinBeiZhanWGData:GetHaoLiOldCurDay()
	if self.old_cur_day ~= nil then
		return self.old_cur_day
	end
end

function QuanMinBeiZhanWGData:GetHaoLigift(index)
	local cfg = self:GetHaoLiCfg()
	local list = {}
	if cfg ~= nil then
		for i,v in ipairs(cfg) do
			if v.theme == index then
				table.insert(list, v)
			end
		end
	end
	return list
end

function QuanMinBeiZhanWGData:GetThemeCount()
	local cfg = self:GetHaoLiCfg()
	local list = {}
	local index = 0
	if cfg ~= nil then
		for i,v in ipairs(cfg) do
			if list[v.theme] == nil then
				list[v.theme] = 1
				index = index + 1
			end
		end
	end
	return index
end

--获取礼包购买数量
function QuanMinBeiZhanWGData:GetBeiZanHaoliBuyCount(theme, gift_index)
	if self.HaoLiData ~= nil and self.HaoLiData.buy_limit_type then
		if self.HaoLiData.buy_limit_type[theme] ~= nil then
			if self.HaoLiData.buy_limit_type[theme][gift_index] ~= nil then
				return self.HaoLiData.buy_limit_type[theme][gift_index]
			end
		end
	end
	return 0
end

--获取可以购买的可购买的礼包页签
function QuanMinBeiZhanWGData:GetCanBuyGiftTabIndex()
	local cfg = self:GetHaoLiCfg()
	local has_buy_times = 0
	if cfg ~= nil and self.HaoLiData ~= nil and self.HaoLiData.cur_day ~= nil then
		for i,v in ipairs(cfg) do
			if v.theme <= self.HaoLiData.cur_day then
				has_buy_times = self:GetBeiZanHaoliBuyCount(v.theme, v.gift_index)
				if has_buy_times < v.role_buy_limit then
					return TabIndex.quanmin_beizhan_haoli + v.theme - 1
				end
			end
		end
	end
	return TabIndex.quanmin_beizhan_haoli
end

-------------------------------------------战力比拼----------------------------------
function QuanMinBeiZhanWGData:GetRoleCapRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").zlbp_role_reward
end

function QuanMinBeiZhanWGData:GetServerCapRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").zlbp_server_reward
end

function QuanMinBeiZhanWGData:GetKanJiaRewardCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").zlbp_zhanli_dali
end

function QuanMinBeiZhanWGData:GetBuffCfg()
	return ConfigManager.Instance:GetAutoConfig("beizhan_theme_config_auto").zlbp_ljzb_buffer
end

function QuanMinBeiZhanWGData:SetZhanLiBiPinInfo(protocol)
	if self.CapData == nil then
		self.CapData = {}
	end
	self.CapData.server_cap = protocol.server_cap
	self.CapData.role_reward_state = protocol.role_reward_state
	self.CapData.server_reward_state = protocol.server_reward_state
	self.CapData.kanjia_reward_state = protocol.kanjia_reward_state

	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_Cap)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_Cap2)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan)
end

--获取全服战力
function QuanMinBeiZhanWGData:GetServerCap()
	if self.CapData ~= nil and self.CapData.server_cap ~= nil then
		return self.CapData.server_cap
	end
	return 0
end

--获取个人战力奖励领取状态
function QuanMinBeiZhanWGData:GetRoleCapRewardState(index)
	if self.CapData ~= nil then
		if self.CapData.role_reward_state ~= nil and self.CapData.role_reward_state[index] then
			return self.CapData.role_reward_state[index]
		end
	end
	return 0
end

--获取全服战力奖励领取状态
function QuanMinBeiZhanWGData:GetServerCapRewardState(index)
	if self.CapData ~= nil then
		if self.CapData.server_reward_state ~= nil and self.CapData.server_reward_state[index] then
			return self.CapData.server_reward_state[index]
		end
	end
	return 0
end

--获取砍价奖励领取状态
function QuanMinBeiZhanWGData:GetKanjiaRewardState(index)
	if self.CapData ~= nil then
		if self.CapData.kanjia_reward_state ~= nil and self.CapData.kanjia_reward_state[index] then
			return self.CapData.kanjia_reward_state[index]
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:GetKanjiaRewardStateByItemID(item_id)
	local cfg_list = self:GetKanJiaRewardCfg()
	if cfg_list then
		for i=1,#cfg_list do
			if cfg_list[i].libao_item_id == item_id then
				return self:GetKanjiaRewardState(i)
			end
		end
	end
	return 0
end

--获取个人战力奖励列表
function QuanMinBeiZhanWGData:GetRoleCapList()
	local list = {}
	local cfg = self:GetRoleCapRewardCfg()
	for i,v in ipairs(cfg) do
		local item = {}
		item.cfg = v
		item.reward_id = v.reward_id
		item.state = self:GetRoleCapRewardState(v.reward_id)
		if item.state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.state == ActivityRewardState.BKL then
			item.sort = 1	
		else
			item.sort = 2
		end
		table.insert(list, item)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort","reward_id"))
	return list
end

--获取全服战力奖励列表
function QuanMinBeiZhanWGData:GetServerCapList()
	local list = {}
	local cfg = self:GetServerCapRewardCfg()
	for i,v in ipairs(cfg) do
		local item = {}
		item.cfg = v
		item.reward_id = v.reward_id
		item.state = self:GetServerCapRewardState(v.reward_id)
		if item.state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.state == ActivityRewardState.BKL then
			item.sort = 1	
		else
			item.sort = 2
		end
		table.insert(list, item)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort","reward_id"))
	return list
end

--获取砍价列表
function QuanMinBeiZhanWGData:GetKanJiaList()
	local list = {}
	local cfg = self:GetKanJiaRewardCfg()
	for i,v in ipairs(cfg) do
		local item = {}
		item.cfg = v
		item.index = i
		item.state = self:GetKanjiaRewardState(i)
		item.zhekou = BossAssistWGData.Instance:GetMainRoleGiftZheKou(v.libao_item_id)
		if item.state == ActivityRewardState.KLQ then
			item.sort = 0
		elseif item.state == ActivityRewardState.BKL then
			item.sort = 1	
		else
			item.sort = 2
		end
		table.insert(list, item)
	end
	table.sort(list, SortTools.KeyLowerSorter("sort","index"))
	return list
end

--获取当前buff激活等级
function QuanMinBeiZhanWGData:GetBuffLevel()
	local role_cap = RoleWGData.Instance:GetMainRoleCap()
	local buff_cfg = self:GetBuffCfg()
	for i,v in ipairs(buff_cfg) do
		if role_cap <= v.zhanli_need then
			return v.level
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:IsBuffFullLv(level)
	return level == #self:GetBuffCfg()
end

--获取当前礼包折扣
function QuanMinBeiZhanWGData:GetGiftZheKou(gift_id)
	-- body
end

function QuanMinBeiZhanWGData:SetGiftKanJiaDownTime(gift_id, end_time)
	if self.CapData == nil then
		self.CapData = {}
	end
	if self.CapData.DownTimeList == nil then
		self.CapData.DownTimeList = {}
	end
	self.CapData.DownTimeList[gift_id] = end_time
end

function QuanMinBeiZhanWGData:GetGiftKanJiaDownTime(gift_id)
	if self.CapData ~= nil and self.CapData.DownTimeList ~= nil then
		return self.CapData.DownTimeList[gift_id]
	end
	return nil
end
----------------------------------------------------------
--获取活动是否开启
function QuanMinBeiZhanWGData:GetActivityState(tab_index)
	local activity_type = 0
	if tab_index == TabIndex.quanmin_beizhan_login then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DENGLUYOULI
	elseif tab_index == TabIndex.quanmin_beizhan_shouchong then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_MEIRISHOUCHONG
	elseif tab_index == TabIndex.quanmin_beizhan_leichong then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LEICHONGHAOLI
	elseif tab_index == TabIndex.quanmin_beizhan_duobei then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DUOBEILAIXI
	elseif tab_index == TabIndex.quanmin_beizhan_longhun then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_WOYAOLONGHUN
	elseif tab_index == TabIndex.quanmin_beizhan_juanxian then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN
	elseif tab_index == TabIndex.quanmin_beizhan_cap then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_ZHANLIBIPIN
	elseif tab_index == TabIndex.quanmin_beizhan_haoli then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_BEIZHANHAOLI
	elseif tab_index == TabIndex.quanmin_beizhan_laixi then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_XINGTIANLAIXI
	elseif tab_index == TabIndex.quanmin_beizhan_turntable then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BZ_TURNTABLE
	elseif tab_index == TabIndex.quanmin_beizhan_longhun_rank then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK--龙魂冲榜
	end
	return ActivityWGData.Instance:GetActivityIsOpen(activity_type)
end

--获取活动的结束时间
function QuanMinBeiZhanWGData:GetActivityInValidTime(tab_index)
	local activity_type = 0
	if tab_index == TabIndex.quanmin_beizhan_login then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DENGLUYOULI
	elseif tab_index == TabIndex.quanmin_beizhan_shouchong then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_MEIRISHOUCHONG
	elseif tab_index == TabIndex.quanmin_beizhan_leichong then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LEICHONGHAOLI
	elseif tab_index == TabIndex.quanmin_beizhan_duobei then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DUOBEILAIXI
	elseif tab_index == TabIndex.quanmin_beizhan_longhun then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_WOYAOLONGHUN
	elseif tab_index == TabIndex.quanmin_beizhan_juanxian then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_QUANFUJUANXIAN
	elseif tab_index == TabIndex.quanmin_beizhan_cap then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_ZHANLIBIPIN
	elseif tab_index == TabIndex.quanmin_beizhan_haoli then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_BEIZHANHAOLI
	elseif tab_index == TabIndex.quanmin_beizhan_laixi then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_XINGTIANLAIXI
	elseif tab_index == TabIndex.quanmin_beizhan_longhun_rank then
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK--龙魂冲榜
	else
		activity_type = ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DALIAN	
	end

	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(activity_type)
	if activity_data ~= nil then
		return activity_data.end_time, activity_data.start_time
	end
	return  0
end

function QuanMinBeiZhanWGData:GetRemindNameMap()
	if self.remind_name_list == nil then
		self.remind_name_list = {}
		self.remind_name_list[10] = RemindName.QuanMinBeiZhan_Login
		self.remind_name_list[20] = RemindName.QuanMinBeiZhan_ShouChong
		self.remind_name_list[30] = RemindName.QuanMinBeiZhan_LeiChong
		self.remind_name_list[40] = RemindName.QuanMinBeiZhan_DuoBei
		self.remind_name_list[50] = RemindName.QuanMinBeiZhan_LongHun
		self.remind_name_list[60] = RemindName.QuanMinBeiZhan_JuanXian
		self.remind_name_list[71] = RemindName.QuanMinBeiZhan_Cap
		self.remind_name_list[72] = RemindName.QuanMinBeiZhan_Cap2
		self.remind_name_list[73] = RemindName.QuanMinBeiZhan_Cap3
		self.remind_name_list[81] = RemindName.QuanMinBeiZhan_HaoLi
		self.remind_name_list[82] = RemindName.QuanMinBeiZhan_HaoLi2
		self.remind_name_list[83] = RemindName.QuanMinBeiZhan_HaoLi3
		self.remind_name_list[84] = RemindName.QuanMinBeiZhan_HaoLi4
		self.remind_name_list[90] = RemindName.QuanMinBeiZhan_LaiXi
		self.remind_name_list[100] = RemindName.QuanMinBeiZhan_Turntable
		self.remind_name_list[110] = RemindName.QuanMinBeiZhan_LongHunRank--龙魂冲榜红点
	end

	return self.remind_name_list
end

--获取活动的奖励数据
function QuanMinBeiZhanWGData:GetActivityRewardState(tab_index)
	local state = 0
	if tab_index == TabIndex.quanmin_beizhan_login then
		state = self:IsShowLoginRedPoint()
	elseif tab_index == TabIndex.quanmin_beizhan_shouchong then
	elseif tab_index == TabIndex.quanmin_beizhan_leichong then
	elseif tab_index == TabIndex.quanmin_beizhan_duobei then
	elseif tab_index == TabIndex.quanmin_beizhan_longhun then
		state = self:IsShowLongHunRedPoint()
	elseif tab_index == TabIndex.quanmin_beizhan_juanxian then
		state = self:IsShowJuanXianRedPoint()
	elseif tab_index == TabIndex.quanmin_beizhan_cap then
		state = self:IsShowRoleCapRedPoint() + self:IsShowServerCapRedPoint()
	elseif tab_index == TabIndex.quanmin_beizhan_haoli then
		state = self:IsShowHaoLiRedPoint()
	elseif tab_index == TabIndex.quanmin_beizhan_laixi then
		state = self:IsShowLaiXiRedPoint()
	elseif tab_index == TabIndex.quanmin_beizhan_turntable then
		state = BZTurnTableWGData.Instance:IsShowTurnTableRed()
	end
	return  state
end

function QuanMinBeiZhanWGData:IsActivityLastDay(tab_index)
	local end_time = self:GetActivityInValidTime(tab_index)
	local end_date = os.date("*t", end_time)
	local cur_date = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	if cur_date.month == end_date.month and cur_date.day == end_date.day then
		return true
	end
	return false
end

------------------------- 各活动红点数据-------------------------
-- 主界面红点提示
function QuanMinBeiZhanWGData:IsShowMainViewRedPoint()
	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLoginRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowShouChongRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLeiChongRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLaiXiRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowHaoLiRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowJuanXianRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowLongHunRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowRoleCapRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == self:IsShowServerCapRedPoint() then
		return 1
	end

	if ShowRedPoint.SHOW_RED_POINT == BZTurnTableWGData.Instance:IsShowTurnTableRed() then
		return 1
	end

	return 0
end

function QuanMinBeiZhanWGData:IsShowLoginRedPoint()
	if self.LoginDayData ~= nil and self.LoginDayData.day_list ~= nil then
		for k,v in ipairs(self.LoginDayData.day_list) do
			if v.common_gift_state == ActivityRewardState.KLQ or v.special_gift_state == ActivityRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:IsShowShouChongRedPoint()
	if self.ShouChongData ~= nil then
		if self.ShouChongData.gift_state == ActivityRewardState.KLQ then
			return 1
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:IsShowLeiChongRedPoint()
	if self.LeiChongData ~= nil and self.LeiChongData.dangwei_info ~= nil then
		for k,v in ipairs(self.LeiChongData.dangwei_info) do
			if v == ActivityRewardState.KLQ then
				return 1
			end
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:IsShowLaiXiRedPoint()
	if self.has_show_laixi_red_point == nil then
		if self:IsInXingTianLaiXiActivity() then
			return 1
		end
	end
	return 0
end

function QuanMinBeiZhanWGData:SetLaiXiRedPoint()
	self.has_show_laixi_red_point = true
end

function QuanMinBeiZhanWGData:IsShowHaoLiRedPoint()
	-- if self.HaoLiData ~= nil and self.HaoLiData.libao_state ~= nil then
	-- 	if self.HaoLiData.libao_state ~= ActivityRewardState.YLQ then
	-- 		return 1
	-- 	end
	-- end
	return 0
end

--全民捐献红点
function QuanMinBeiZhanWGData:IsShowJuanXianRedPoint()
	if self.JuanXianData ~= nil then
		local cfg = self:GetQFJXDayRewardCfg()
		if self.JuanXianData.server_reward_state_list ~= nil then
			for k,v in ipairs(self.JuanXianData.server_reward_state_list) do
				if cfg[k] ~= nil and cfg[k].reward_type == 2 then  --只有礼包需要领取
					if v == ActivityRewardState.KLQ then
						return 1
					end
				end
			end
		end

		if self.JuanXianData.role_reward_state_list ~= nil then
			for k,v in ipairs(self.JuanXianData.role_reward_state_list) do
				if v == ActivityRewardState.KLQ then
					return 1
				end
			end
		end

		local other_cfg = self:GetActivityThemeOtherCfg()
		if other_cfg then
			local item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg[1].juanxian_stuff_item_id)
			if item_num > 0 then
				return 1
			end
		end
	end
	return 0
end

--龙魂红点
function QuanMinBeiZhanWGData:IsShowLongHunRedPoint()
	if self.LHData ~= nil and self.LHData.dangwei_state ~= nil and self.LHData.task_list ~= nil then
		for i,v in ipairs(self.LHData.dangwei_state) do
			if v == ActivityRewardState.KLQ then
				return 1
			end
		end

		for i,v in ipairs(self.LHData.task_list) do
			if v.gift_state == ActivityRewardState.KLQ then
				return 1
			end
		end

		--屏蔽合成红点
		-- local lhsp_cfg = self:GetLHSuiPianCfg()
		-- if 	lhsp_cfg ~= nil then
		-- 	local item_num = ItemWGData.Instance:GetItemNumInBagById(lhsp_cfg.stuff_id_1)
		-- 	if item_num >= lhsp_cfg.stuff_count_1 then
		--         return 1
		--     end
		-- end

	end
	return 0
end

--个人战力
function QuanMinBeiZhanWGData:IsShowRoleCapRedPoint()
	if self.CapData ~= nil then
		if self.CapData.role_reward_state ~= nil then
			for k,v in ipairs(self.CapData.role_reward_state) do
				if v == ActivityRewardState.KLQ then
					return 1
				end
			end
		end
	end
	return 0
end

--全服战力
function QuanMinBeiZhanWGData:IsShowServerCapRedPoint()
	-- if self.CapData ~= nil then
	-- 	if self.CapData.server_reward_state ~= nil then
	-- 		for k,v in ipairs(self.CapData.server_reward_state) do
	-- 			if v == ActivityRewardState.KLQ then
	-- 				return 1
	-- 			end
	-- 		end
	-- 	end
	-- end
	return 0
end

function QuanMinBeiZhanWGData:CalcIrregularProgress(value)
	local cur_progress = 0
	local progress_list = {1/5, 2/5, 3/5, 4/5, 1}
	local max_jifen = self:GetMaxJiFen()
	for k,v in pairs(progress_list) do
		local cur_need = self:GetBXTargetScore(k)
		local next_cfg = self:GetBXTargetCfg(k + 1)
		local next_need = 0
		if IsEmptyTable(next_cfg) then
			next_need = max_jifen
		else
			next_need = next_cfg.jifen
		end
		local cur_value = progress_list[k] and progress_list[k] or 0
		local next_value = progress_list[k + 1] and progress_list[k + 1] or progress_list[#progress_list]

		if value >= cur_need and value <= next_need then
			cur_progress = (value - cur_need) * (next_value - cur_value) / (next_need - cur_need) + cur_value
			break
		elseif value < cur_need then
			cur_progress =  value * cur_value / cur_need 
			break
		elseif value >= max_jifen then
			cur_progress = progress_list[#progress_list]
			break
		end
	end
	return cur_progress
end

function QuanMinBeiZhanWGData:GetMaxJiFen()
	local max_jifen = 0
	local cfg = self:GetLHRewardCfg()
	for k,v in pairs(cfg) do
		if v.jifen > max_jifen then
			max_jifen = v.jifen
		end
	end
	return max_jifen
end

function QuanMinBeiZhanWGData:GetBXTargetCfg(id)
	local reward_cfg = self:GetLHRewardCfg()
	if reward_cfg ~= nil and reward_cfg[id] ~= nil then
		return reward_cfg[id]
	end
	return {}
end

--活动按钮开启
function QuanMinBeiZhanWGData:OpenActivityIsOpen()
	local role_level = RoleWGData.Instance.role_vo.level
	local cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.GOD_QUANMIN_BEIZHAN) 
	if cfg and cfg.level <= role_level then
		return true
	end

	return false
end

--龙魂冲榜------- start -----------
--QuanMinBeiZhanWGData.Instance:InitLongHunRankCfgList()
function QuanMinBeiZhanWGData:InitLongHunRankCfgList()
	self.longhun_rank_cfg_list = {}
	local rank_cfgs = self.long_hun_chong_bang_rank_cfgs
	-- print_error("rank_cfgs ", rank_cfgs)
	if not IsEmptyTable(rank_cfgs) then
		for _, v in pairs(rank_cfgs) do
			if not IsEmptyTable(v) then
				local info = {
					id = -1,
					rank = v.rank,
					zhanli_cond = -1,
					min_zhanli = v.min_zhanli,
					reward_item = v.reward_item,
					condition_desc = v.condition_desc,
					rank_range = v.rank_range,
				}
				self.longhun_rank_cfg_list[#self.longhun_rank_cfg_list + 1] = info
			end
		end
	end
	
	local award_cfgs = self.long_hun_chong_bang_award_cfgs
	-- print_error("award_cfgs ", award_cfgs)
	if not IsEmptyTable(award_cfgs) then
		for _, v in pairs(award_cfgs) do
			if not IsEmptyTable(v) then
				local info = {
					id = v.ID,
					rank = -1,
					zhanli_cond = v.zhanli_cond,
					min_zhanli = -1,
					reward_item = v.reward_item,
					condition_desc = v.condition_desc,
					rank_range = "",
				}
				self.longhun_rank_cfg_list[#self.longhun_rank_cfg_list + 1] = info
			end
		end
	end
	table.sort(self.longhun_rank_cfg_list, SortTools.KeyLowerSorters("id", "rank"))
	-- for i, v in ipairs(self.longhun_rank_cfg_list) do
	-- 	print_error("vvvvvvvv = ", i, v.condition_desc, v)
	-- end
end

function QuanMinBeiZhanWGData:GetLongHunRankCfgList()
	return self.longhun_rank_cfg_list
end

--龙魂冲榜--排行榜列表信息
function QuanMinBeiZhanWGData:SetLongHunRankLogInfo(protocol)
	self.longhun_rank_log_list = protocol.longhun_rank_log_list or {}
	SortTools.SortAsc(self.longhun_rank_log_list, "rand_id")
	for i, v in ipairs(self.longhun_rank_log_list) do
		v.sort_index = i
	end
	QuanMinBeiZhanWGCtrl.Instance:FlushLongHunRankLogView()
end

function QuanMinBeiZhanWGData:GetLongHunRankLogInfo()
	return self.longhun_rank_log_list
end

--龙魂冲榜--活动相关信息
function QuanMinBeiZhanWGData:SetLongHunRankRoleInfo(protocol)
	local info = protocol.longhun_rank_role_info
	self.longhun_rank_role_info = {}
	self.longhun_rank_role_info.rank = info.rank
	self.longhun_rank_role_info.zhanli = info.zhanli

	self.longhunrank_join_flag = bit:d2b(info.ra_longhunrank_join_flag)
	self.longhunrank_join_reward_flag = bit:d2b(info.ra_longhunrank_join_reward_flag)
	RemindManager.Instance:Fire(RemindName.QuanMinBeiZhan_LongHunRank)
	GlobalEventSystem:Fire(CHONGBANG_TIP_EVENT.ACT_CHECK,ChongBangTipWGData.ACT_TIP_TYPE.ACT,ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LONGHUNRANK)
end

function QuanMinBeiZhanWGData:GetLongHunRankRoleInfo()
	return self.longhun_rank_role_info
end

--龙魂冲榜 奖励达成标记
function QuanMinBeiZhanWGData:GetLongHunRankJoinFlag(index)
	if not IsEmptyTable(self.longhunrank_join_flag) then
		-- print_error("FFF====111 奖励达成标记", 33 - index, index, self.longhunrank_join_flag[33 - index])
		return self.longhunrank_join_flag[33 - index] == 1
	end
	return false
end

--龙魂冲榜 奖励领取标记
function QuanMinBeiZhanWGData:GetLongHunRankJoinRewardFlag(index)
	if not IsEmptyTable(self.longhunrank_join_reward_flag) then
		-- print_error("FFF====222 奖励领取标记", 33 - index, index, self.longhunrank_join_reward_flag[33 - index])
		return self.longhunrank_join_reward_flag[33 - index] == 1
	end
	return false
end

function QuanMinBeiZhanWGData:CheckLongHunRankRemind()
	local total_step = #self.long_hun_chong_bang_award_cfgs or 2
	for i = 1, total_step do
		local is_finish = self:GetLongHunRankJoinFlag(i)
		local is_get = self:GetLongHunRankJoinRewardFlag(i)
		if is_finish and not is_get then
			return 1, self.long_hun_chong_bang_award_cfgs[i].ID
		end
	end
	return 0
end
--龙魂冲榜------- end -----------