﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class HUDProgramme_HUDAssetsManagerWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(HUDProgramme.HUDAssetsManager), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>unction("AddAtlaTexture", AddAtlaTexture);
		<PERSON><PERSON>unction("InitAltasCfg", InitAltasCfg);
		<PERSON><PERSON>RegFunction("GetAtlasTexture", GetAtlasTexture);
		<PERSON><PERSON>RegFunction("GetAnim", GetAnim);
		<PERSON><PERSON>RegFunction("GetHUDAnimLength", GetHUDAnimLength);
		<PERSON><PERSON>Function("GetHUDAnimList", GetHUDAnimList);
		<PERSON><PERSON>RegFunction("SetHUDMeshCamera", SetHUDMeshCamera);
		<PERSON><PERSON>RegFunction("SetHUDSceneCamera", SetHUDSceneCamera);
		<PERSON><PERSON>unction("ShowHurt", ShowHurt);
		<PERSON><PERSON>unction("__eq", op_Equality);
		<PERSON>.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("HUDAnimData", get_HUDAnimData, set_HUDAnimData);
		L.RegVar("HUDPrefabsData", get_HUDPrefabsData, set_HUDPrefabsData);
		L.RegVar("HUDTextAsset", get_HUDTextAsset, set_HUDTextAsset);
		L.RegVar("HUDTextAssetBin", get_HUDTextAssetBin, set_HUDTextAssetBin);
		L.RegVar("get_HUDTextAsset", get_get_HUDTextAsset, null);
		L.RegVar("get_HUDTextAssetBin", get_get_HUDTextAssetBin, null);
		L.RegVar("Instance", get_Instance, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddAtlaTexture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			UnityEngine.Texture arg1 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 3);
			obj.AddAtlaTexture(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int InitAltasCfg(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			obj.InitAltasCfg();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAtlasTexture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			UnityEngine.Texture o = obj.GetAtlasTexture(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAnim(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			HUDProgramme.HUDAnim o = obj.GetAnim(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHUDAnimLength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			int o = obj.GetHUDAnimLength();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetHUDAnimList(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			System.Collections.Generic.List<HUDProgramme.HUDObject> o = obj.GetHUDAnimList();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetHUDMeshCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			obj.SetHUDMeshCamera(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetHUDSceneCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 2, typeof(UnityEngine.Camera));
			obj.SetHUDSceneCamera(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ShowHurt(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 9);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)ToLua.CheckObject<HUDProgramme.HUDAssetsManager>(L, 1);
			UnityEngine.Transform arg0 = (UnityEngine.Transform)ToLua.CheckObject<UnityEngine.Transform>(L, 2);
			long arg1 = LuaDLL.tolua_checkint64(L, 3);
			string arg2 = ToLua.CheckString(L, 4);
			string arg3 = ToLua.CheckString(L, 5);
			string arg4 = ToLua.CheckString(L, 6);
			bool arg5 = LuaDLL.luaL_checkboolean(L, 7);
			bool arg6 = LuaDLL.luaL_checkboolean(L, 8);
			bool arg7 = LuaDLL.luaL_checkboolean(L, 9);
			obj.ShowHurt(arg0, arg1, arg2, arg3, arg4, arg5, arg6, arg7);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_HUDAnimData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			HUDProgramme.HUDObjectData ret = obj.HUDAnimData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HUDAnimData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_HUDPrefabsData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			HUDProgramme.HUDObjectData ret = obj.HUDPrefabsData;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HUDPrefabsData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_HUDTextAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			UnityEngine.TextAsset ret = obj.HUDTextAsset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HUDTextAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_HUDTextAssetBin(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			UnityEngine.TextAsset ret = obj.HUDTextAssetBin;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HUDTextAssetBin on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_get_HUDTextAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			UnityEngine.TextAsset ret = obj.get_HUDTextAsset;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index get_HUDTextAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_get_HUDTextAssetBin(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			UnityEngine.TextAsset ret = obj.get_HUDTextAssetBin;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index get_HUDTextAssetBin on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.Push(L, HUDProgramme.HUDAssetsManager.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_HUDAnimData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			HUDProgramme.HUDObjectData arg0 = (HUDProgramme.HUDObjectData)ToLua.CheckObject<HUDProgramme.HUDObjectData>(L, 2);
			obj.HUDAnimData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HUDAnimData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_HUDPrefabsData(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			HUDProgramme.HUDObjectData arg0 = (HUDProgramme.HUDObjectData)ToLua.CheckObject<HUDProgramme.HUDObjectData>(L, 2);
			obj.HUDPrefabsData = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HUDPrefabsData on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_HUDTextAsset(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			UnityEngine.TextAsset arg0 = (UnityEngine.TextAsset)ToLua.CheckObject<UnityEngine.TextAsset>(L, 2);
			obj.HUDTextAsset = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HUDTextAsset on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_HUDTextAssetBin(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HUDProgramme.HUDAssetsManager obj = (HUDProgramme.HUDAssetsManager)o;
			UnityEngine.TextAsset arg0 = (UnityEngine.TextAsset)ToLua.CheckObject<UnityEngine.TextAsset>(L, 2);
			obj.HUDTextAssetBin = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index HUDTextAssetBin on a nil value");
		}
	}
}

