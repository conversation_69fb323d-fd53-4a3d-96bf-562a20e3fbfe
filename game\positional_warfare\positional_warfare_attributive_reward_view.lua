-- 归属奖励界面
PositionalWarfareAttributiveRewardView = PositionalWarfareAttributiveRewardView or BaseClass(SafeBaseView)

function PositionalWarfareAttributiveRewardView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
    self:AddViewResource(0, "uis/view/positional_warfare_ui_prefab", "layout_positional_warfare_attributive_reward_view")
end

function PositionalWarfareAttributiveRewardView:SetDataAndOpen(data)
	self.show_data = data

	if self.show_data ~= nil then
        PositionalWarfareWGCtrl.Instance:SendCrossLandWarOperate(CROSS_LAND_WAR_OPERATE_TYPE.RANK_INFO, CROSS_LAND_WAR_RANK_TYPE.LAND_DEVOTE, self.show_data.seq)

		if not self:IsOpen() then
			self:Open()
		else
			self:Flush()
		end
	end
end

function PositionalWarfareAttributiveRewardView:LoadCallBack()
    if not self.attributive_reward_list then
        self.attributive_reward_list = AsyncListView.New(PWAttributiveRewardItemCellRender, self.node_list.attributive_reward_list)
    end

    if not self.defeat_rank_list then
        self.defeat_rank_list = AsyncListView.New(PWAttributiveDefeatItemCellRender, self.node_list.defeat_rank_list)
    end
end

function PositionalWarfareAttributiveRewardView:ReleaseCallBack()
    if self.attributive_reward_list then
        self.attributive_reward_list:DeleteMe()
        self.attributive_reward_list = nil
    end

    if self.defeat_rank_list then
        self.defeat_rank_list:DeleteMe()
        self.defeat_rank_list = nil
    end

    self.show_data = nil
end

function PositionalWarfareAttributiveRewardView:OnFlush()
    if IsEmptyTable(self.show_data) then
        return
    end

    local attributive_reward_list = PositionalWarfareWGData.Instance:GetCaptureRewardCfg(self.show_data.seq)
    self.attributive_reward_list:SetDataList(attributive_reward_list)
    self.node_list.score_menber_no_data:CustomSetActive(IsEmptyTable(attributive_reward_list))

    local defeat_rank_data_list = PositionalWarfareWGData.Instance:GetRankDataListByRankType(CROSS_LAND_WAR_RANK_TYPE.LAND_DEVOTE)
    self.defeat_rank_list:SetDataList(defeat_rank_data_list)
    self.node_list.defeat_menber_no_data:CustomSetActive(IsEmptyTable(defeat_rank_data_list))
end

----------------------------------------PWAttributiveRewardItemCellRender----------------------------------
PWAttributiveRewardItemCellRender = PWAttributiveRewardItemCellRender or BaseClass(BaseRender)

function PWAttributiveRewardItemCellRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end
end

function PWAttributiveRewardItemCellRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end
end

function PWAttributiveRewardItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local min_rank = self.data.min_rank
    local max_rank = self.data.max_rank
    local rank_str = (min_rank == max_rank) and min_rank or (min_rank .. "-" .. max_rank)
    
    self.node_list.rank_id.text.text = string.format(Language.PositionalWarfare.AttributiveRankStr, rank_str)
    self.reward_list:SetDataList(self.data.reward_item)
end

----------------------------------------PWAttributiveDefeatItemCellRender----------------------------------
PWAttributiveDefeatItemCellRender = PWAttributiveDefeatItemCellRender or BaseClass(BaseRender)

function PWAttributiveDefeatItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local bundle, asset
    local is_top_three = self.data.rank_id <= 3

    if is_top_three then
        bundle, asset = ResPath.GetCommonImages("a3_ty_list_" .. self.data.rank_id)
        self.node_list.rank_icon.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rank_id))
    else
        bundle, asset = ResPath.GetCommonImages("a3_ty_bg1_3")
    end

    self.node_list.bg.image:LoadSprite(bundle, asset)
    self.node_list.rank_id:CustomSetActive(not is_top_three)
    self.node_list.rank_icon:CustomSetActive(is_top_three)
    self.node_list.rank_id.text.text = self.data.rank_id
    self.node_list.role_name.text.text = self.data.name
    self.node_list.defeat_value.text.text = self.data.rank_value

    local camp_cfg = PositionalWarfareWGData.Instance:GetCurCampCfg(self.data.camp)
    self.node_list.camp.text.text = camp_cfg and camp_cfg.camp_name or ""
end