--新增 仙界魔王
local layer_cell_high = 440
function WorldServerView:InitXianjieView()
    self.can_see_high = UnityEngine.Screen.height
    self.node_list["btn_xianjie_kill"].button:AddClickListener(BindTool.Bind1(self.GoTo<PERSON>ill<PERSON>ianjie, self)) --前往击杀
    self.node_list["btn_xianjie_des"].button:AddClickListener(BindTool.Bind1(self.OnClickXianJieRule, self)) --规则
    self.node_list["xianjie_focus_btn"].button:AddClickListener(BindTool.Bind1(self.FouseOnXianjieBoss, self)) --关注
    self.node_list["xianjie_open_rule_btn"].button:AddClickListener(BindTool.Bind1(self.OpenRuleOnXianjieBoss, self)) --打开诸天万界
    --self.node_list["xianjie_right_tip1"].button:AddClickListener(BindTool.Bind1(self.OnClickGoToEquip, self)) --跳转天书
    --self.node_list["btn_xianjie_area"].button:AddClickListener(BindTool.Bind1(self.OnClickXianJieArea, self)) --跨服组
    --self.node_list["xianjie_right_tip3"].button:AddClickListener(BindTool.Bind1(self.OnClickXianJieArea, self)) --跨服组
    self.AddClickEventListener(self.node_list["xianjie_recode_btn"], BindTool.Bind(self.OnClickRecord, self))
    self.AddClickEventListener(self.node_list["xianjie_cost_item"], BindTool.Bind(self.OnClickMenPiao, self))
    self.AddClickEventListener(self.node_list["sub_btn"], BindTool.Bind(self.OnClickSub, self))
    self.AddClickEventListener(self.node_list["add_btn"], BindTool.Bind(self.OnClickAdd, self))

    self.page = 0
	self.xianjie_list_data = nil
	self.xianjie_cambered_list = nil
	self.xianjie_btn_select_index = nil
	self.xianjie_drag_select_index = -1

    self:CreateLayerList()
    self:CreateXianJieRewardCell()
    self:InitCreateXianJieCamberedList()

    self.node_list["xianjie_remind_tip"].text.text = Language.XianJieBoss.RemindTip
end

function WorldServerView:ReleaseXianjieView()
    if nil ~= self.xianjie_layer_list then
        self.xianjie_layer_list:DeleteMe()
        self.xianjie_layer_list = nil
    end

    if nil ~= self.xianjie_reward_cell_list then
        self.xianjie_reward_cell_list:DeleteMe()
        self.xianjie_reward_cell_list = nil
    end

    if self.xianjie_cambered_list then
		self.xianjie_cambered_list:DeleteMe()
		self.xianjie_cambered_list = nil
	end

    self.page = 0
    self.xianjie_layer = nil
    self.jump_xianjie_layer = nil
    self.old_xianjie_select_data = nil
    self.xianjie_layer_data = nil
    self.xianjie_list_data = nil
    self.xianjie_drag_select_index = -1
	self.xianjie_btn_select_index = nil

    if self.layer_list_tween then
        self.layer_list_tween:Kill()
        self.layer_list_tween = nil
    end

    if not IsEmptyTable(self.chips_item_list) then
        for k, v in pairs(self.chips_item_list) do
            v:DeleteMe()
        end
        self.chips_item_list = nil
    end
end

--打开界面
function WorldServerView:ShowIndexXianjieView()
    BossWGCtrl.Instance:SendXianJieBossReq(CROSS_FAIRYLAND_BOSS_OPERATE_TYPE.CROSS_FAIRYLAND_BOSS_OPERATE_TYPE_DROP_HISTORY)
    BossWGCtrl.Instance:RequestXianJieBossInfo()
    local cfg = XianJieBossWGData.Instance:GetOtherCfg()
    self:FlushBtnAct(true)
end

function WorldServerView:ClearXianjieInfo()
    self.jump_xianjie_layer = nil
    self.xianjie_layer = nil
end

function WorldServerView:XianjieJump(view_index, num_index, card_index)
    self.xianjie_btn_select_index = card_index
    self.jump_xianjie_layer = num_index
end

function WorldServerView:CreateLayerList()
    if self.xianjie_layer_list == nil then
        self.xianjie_layer_list = AsyncListView.New(BossXianJieLayerRender, self.node_list["ph_xianjie_layer_list"])
        self.xianjie_layer_list:SetSelectCallBack(BindTool.Bind1(self.XianJieBossLayerSelected, self))
    end
end

function WorldServerView:CreateXianJieRewardCell()
    if not self.xianjie_reward_cell_list then
        self.xianjie_reward_cell_list = AsyncBaseGrid.New()
        local t = {}
        t.col = 3
        t.change_cells_num = 1
        t.itemRender = BossRewardCell
        t.list_view = self.node_list["xianjie_cell_list"]
        self.xianjie_reward_cell_list:CreateCells(t)
        self.xianjie_reward_cell_list:SetStartZeroIndex(false)
    end

    self.chips_item_list = {}
    local list_node = self.node_list["chip_list"]
    for i = 0, 7 do
        self.chips_item_list[i] = XianJieBookEquipChipRender.New(list_node:FindObj("chip_" .. i))
        self.chips_item_list[i]:SetIndex(i)
    end
end

function WorldServerView:InitCreateXianJieCamberedList()
	local cambered_list_data = {
		item_render = BossXianJieItemRender,
		asset_bundle = "uis/view/boss_ui_prefab",
		asset_name = "ph_xianjie_boss_render",

		scroll_list = self.node_list.ph_xianjie_boss_cambered_list,
		center_x = 800,
		center_y = -230,
		radius_x = 800,
		radius_y = 800,
		angle_delta = Mathf.PI / BossView.ANGLE_DELTA,
		origin_rotation = Mathf.PI * 0.41,
		is_drag_horizontal = false,
		is_clockwise_list = false,
		speed = 1,
		arg_speed = 0.2,
		viewport_count = BossView.DRAG_COUNT,

		click_item_cb = BindTool.Bind(self.OnClickXianJieBtn, self),
		drag_to_next_cb = BindTool.Bind(self.OnDragXianJieToNextCallBack, self),
		drag_to_last_cb = BindTool.Bind(self.OnDragXianJieToLastCallBack, self),
		on_drag_end_cb = BindTool.Bind(self.OnDragXianJieEndCallBack, self),
	}

	self.xianjie_cambered_list = CamberedList.New(cambered_list_data)
end

function WorldServerView:OnClickXianJieBtn(item_cell, force_jump)
	if item_cell == nil then
		return
	end

	local select_index = item_cell:GetIndex()
	local select_data = item_cell:GetData()
	if select_data == nil then
		return
	end

	if (not force_jump and self.xianjie_btn_select_index == select_index) then
		return
	end

	self.xianjie_btn_select_index = select_index
	self.xianjie_drag_select_index = select_index

	self:OnXianJieSelectedBtnChange(function()
		self:XianJieBossSelected(item_cell)
	end, true)
end

function WorldServerView:OnDragXianJieToNextCallBack()
	local max_index = self.xianjie_list_data and #self.xianjie_list_data or 6
	self.xianjie_drag_select_index = self.xianjie_drag_select_index + 1
	self.xianjie_drag_select_index = self.xianjie_drag_select_index > max_index and max_index or self.xianjie_drag_select_index
end

function WorldServerView:OnDragXianJieToLastCallBack()
	self.xianjie_drag_select_index = self.xianjie_drag_select_index - 1
	self.xianjie_drag_select_index = self.xianjie_drag_select_index < 1 and 1 or self.xianjie_drag_select_index
end

function WorldServerView:OnDragXianJieEndCallBack()
	self:OnXianJieSelectedBtnChange(nil, false, self.xianjie_drag_select_index)
end

function WorldServerView:OnXianJieSelectedBtnChange(callback, is_click, drag_index)
	if self.xianjie_cambered_list == nil then
		return
	end

	local to_index = drag_index ~= nil and drag_index or self.xianjie_btn_select_index or 1
	self.xianjie_cambered_list:ScrollToIndex(to_index, callback, is_click)

	if is_click then
		local item_list = self.xianjie_cambered_list:GetRenderList()
		for k, item_cell in ipairs(item_list) do
			item_cell:SetSelectedHL(to_index)
		end
	end
end

function WorldServerView:GoToKillXianjie()
    if IsEmptyTable(self.xianjie_select_data) then
        return
    end
    local can_enter = XianJieBossWGData.Instance:GetIsCanEnterWithTime()
    if not can_enter then
        local other_cfg = XianJieBossWGData.Instance:GetOtherCfg()
        SysMsgWGCtrl.Instance:ErrorRemind(other_cfg.open_tips)
        return
    end

    if not IsEmptyTable(self.xianjie_select_data) then
        if Scene.Instance:GetSceneType() == SceneType.XianJie_Boss then
            if Scene.Instance:GetSceneId() == self.xianjie_select_data.scene_id then
                GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
                BossWGData.Instance:SetCurSelectBossID(0, 0, self.xianjie_select_data.boss_id)
                MainuiWGCtrl.Instance:SetLightBossId(self.xianjie_select_data.boss_id)
                BossWGCtrl.Instance:MoveToBoss(self.xianjie_select_data.boss_id)
                self:Close()
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.XianJieBoss.NotCanEnter)
            end
            return
        end
    end

    local level = RoleWGData.Instance:GetRoleLevel()
    if level < self.xianjie_select_data.role_level_limit then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.XianJieBoss.NeedRoleLv,
            RoleWGData.GetLevelString(self.xianjie_select_data.role_level_limit)))
        return
    end

    local cfg = XianJieBossWGData.Instance:GetBossCfgById(self.xianjie_select_data.boss_id)
    local slot_limit, slot_page_limit = cfg.slot_limit, cfg.slot_page_limit
    local cur_slot, cur_page = XianJieBossWGData.Instance:GetCurSlotAndPage()

    local stage_can_enter
    if cur_slot > slot_limit then
        stage_can_enter = true
    elseif cur_slot == slot_limit then
        stage_can_enter = cur_page >= slot_page_limit
    else
        stage_can_enter = false
    end

    local before_layer = self.xianjie_layer - 1
    local data = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(before_layer)
    if not data then
        data = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(self.xianjie_layer)
    end
    local page_str2 = XianJieBossWGData.Instance:GetPageStr(data.slot_page_limit, false)
    if not stage_can_enter then
        SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.XianJieBoss.CannotEnterTip, page_str2))
        return
    end

    local remain_time, total_time = XianJieBossWGData.Instance:GetXianjieEnterInfo()
    local other_cfg = XianJieBossWGData.Instance:GetOtherCfg()
    local enter_comsun = other_cfg.enter_cost_item_num
    local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.enter_cost_item_id)
    if remain_time <= 0 and has_tiky_num < enter_comsun then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.XianJieBoss.NoItemsTip)
        TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = other_cfg.enter_cost_item_id })
        return
    end
    BossWGCtrl.Instance:GoToKillXianjie(self.xianjie_select_data.boss_id, self.xianjie_layer)
end

function WorldServerView:OnClickXianJieArea()
    local cfg = XianJieBossWGData.Instance:GetOtherCfg()
    local des, title = cfg.shilimode_tips, Language.Boss.PlayTitle
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(title)
    role_tip:SetContent(des)
end

function WorldServerView:OnClickGoToEquip()
    local slot, page = XianJieBossWGData.Instance:GetCurSlotAndPage()
    ViewManager.Instance:Open(GuideModuleName.FairyLandRuleView, 0, "open_rule", {to_slot = slot, to_ui_param = page})
end

function WorldServerView:OnClickXianJieRule()
    local des, title = Language.XianJieBoss.XianjieRuleTip, Language.Boss.PlayTitle
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(title)
    role_tip:SetContent(des)
end

function WorldServerView:FouseOnXianjieBoss()
    local tabindex = self:GetShowIndex()
    local data = {}
    data.tabindex = tabindex
    data.cur_layer = self.xianjie_layer
    data.is_world_server = true
    BossWGCtrl.Instance:SetFocusViewDataAndOpen(data)
end

function WorldServerView:OpenRuleOnXianjieBoss()
    local shuye_cfg = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(self.xianjie_layer)
    local slot, page = shuye_cfg.slot_limit ,shuye_cfg.slot_page_limit
    ViewManager.Instance:Open(GuideModuleName.FairyLandRuleView, 0, "open_rule", {to_slot = slot, to_ui_param = page})
end

--选中层
function WorldServerView:XianJieBossLayerSelected(cell, cell_index, is_default, is_click)
    if cell and cell:GetData() then
        --如果点击切换层数，选中默认值
        local default_index = self.xianjie_btn_select_index or 1
        if self.xianjie_btn_select_index == nil then
            default_index = XianJieBossWGData.Instance:GetBossDefaultIndex()
            self.xianjie_btn_select_index = default_index
        end

        local data = cell:GetData()
        local boss_list = XianJieBossWGData.Instance:GetBossListByLayer(data.layer)
        self.xianjie_layer = data.layer
        self.xianjie_list_data = boss_list

		self.xianjie_cambered_list:CreateCellList(#boss_list)
		local btn_item_list = self.xianjie_cambered_list:GetRenderList()
		for k, item_cell in ipairs(btn_item_list) do
			local item_data = self.xianjie_list_data[k]
			item_cell:SetData(item_data)

            if default_index == item_cell:GetIndex() then
                self:OnXianJieSelectedBtnChange(function()
                    self:XianJieBossSelected(item_cell)
                end, true)
            end
		end
    end
end

--选中boss
function WorldServerView:PlayLayerListTween(index)
    if self.layer_list_tween then
        self.layer_list_tween:Kill()
        self.layer_list_tween = nil
    end

    local layer_data_list = XianJieBossWGData.Instance:GetBossListlayerForView()
    local max_layer = #layer_data_list
    local jump_index = index
    local cell_wight = 100
    local space = 0 --间隔
    local roll_x = space + cell_wight
    local list_width = self.node_list.ph_xianjie_layer_list.rect.rect.width
    local pos_x = -1
    if jump_index >= (max_layer - 1) then
        pos_x = (max_layer * cell_wight + (max_layer - 1) * space - list_width) --listView宽度
    elseif jump_index <= 2 then
        pos_x = 0
    elseif jump_index > 2 then
        pos_x = roll_x * (jump_index - 2)
    end

    if max_layer > 2 then
        if pos_x ~= -1 and pos_x >= 0 and self.node_list["ph_xianjie_layer_list"].scroll_rect.content then
            local rect_transform = self.node_list["ph_xianjie_layer_list"].scroll_rect.content:GetComponent(typeof(UnityEngine
                .RectTransform))
            self.layer_list_tween = rect_transform:DOAnchorPosX(-pos_x, 0.3)
            self.layer_list_tween:SetEase(DG.Tweening.Ease.OutCubic)
        end
    end
end

--选中boss
function WorldServerView:XianJieBossSelected(cell)
    if cell and cell:GetData() and self.xianjie_layer then
        self:ClearBossTuJianIndex()
        self.xianjie_select_data = cell.data
        self:OnFlushBossAnim()
        self:OnFlushXianJieReward()
    end
end

function WorldServerView:OnFlushXianJieReward()
    if IsEmptyTable(self.xianjie_reward_cell_list) then
        self:CreateXianJieRewardCell()
    end

    if not self.xianjie_select_data or not self.xianjie_layer then
        return
    else
       --[[ if self.old_xianjie_select_data == self.xianjie_select_data then
            return
        end--]]
    end
    local cfg = self.xianjie_select_data.drop_item_list
    local list = {}
    if not IsEmptyTable(cfg) then
        for i = 0, #cfg do
            if nil ~= cfg[i] then
                local v = cfg[i]
                local data = {
                    item_id = v.item_id,
                    num = v.num,
                    is_bind = v.is_bind,
                    cell_scale = 0.9,
                }
                table.insert(list, data)
            end
        end
    end
    self.old_xianjie_select_data = self.xianjie_select_data
    self.xianjie_reward_cell_list:SetDataList(list)
    local shuye_cfg = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(self.xianjie_layer)
    local slot, page = shuye_cfg.slot_limit ,shuye_cfg.slot_page_limit

    for k,v in pairs(self.chips_item_list) do
        v:SetData({slot = slot, page = page})
    end

    local cur_slot, cur_page = XianJieBossWGData.Instance:GetCurSlotAndPage()
    local slot_limit, slot_page_limit = self.xianjie_select_data.slot_limit, self.xianjie_select_data.slot_page_limit

    local color1, stage_can_enter
    if cur_slot > slot_limit then
        color1 = COLOR3B.DEFAULT_NUM
        stage_can_enter = true
    elseif cur_slot == slot_limit then
        color1 = cur_page >= slot_page_limit and COLOR3B.DEFAULT_NUM or "#CF0F1E"
        stage_can_enter = cur_page >= slot_page_limit
    else
        color1 = "#CF0F1E"
        stage_can_enter = false
    end

    self.node_list.xianjie_boss_name.text.text = self.xianjie_select_data.boss_name

    local can_enter = RoleWGData.Instance:GetRoleLevel() >= self.xianjie_select_data.need_role_level and stage_can_enter
    local color = RoleWGData.Instance:GetRoleLevel() >= self.xianjie_select_data.need_role_level and COLOR3B.D_GREEN or
        COLOR3B.D_RED
    self.node_list.xianjie_right_tip3.text.text = string.format(Language.XianJieBoss.CrossGroupNum,
        self.xianjie_select_data.group_num)
    if not self.xianjie_layer then
        print_error("xianjie_layer is nil")
        return
    end
    local before_layer = self.xianjie_layer - 1
    local data = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(before_layer)
    if not data then
        data = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(self.xianjie_layer)
    end

    if self.xianjie_layer == 1 then
        self.node_list.xianjie_right_tip1.text.text = Language.XianJieBoss.SlotFirstLayer
    else
        if before_layer % 5 == 0 then
            self.node_list.xianjie_right_tip1.text.text = Language.XianJieBoss.SlotFirstLayer2
        else
            local page_str2 = XianJieBossWGData.Instance:GetPageStr(data.slot_page_limit, false)
            self.node_list.xianjie_right_tip1.text.text = string.format(Language.XianJieBoss.SlotDes, color1, page_str2)
        end
    end

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["xianjie_right_tip1"].rect)
    self.node_list["xianjie_focus_btn"]:SetActive(can_enter)
end

function WorldServerView:GetXianJieLayerDefIndex()
    local layer_data_list = XianJieBossWGData.Instance:GetBossListlayerForView()
    local default_index = 1
    if self.xianjie_layer == nil then
        if self.jump_xianjie_layer == nil then
            self.jump_xianjie_layer = XianJieBossWGData.Instance:GetBossLayerDefaultIndex()
        end
        default_index = self.jump_xianjie_layer
    else
        default_index = self.xianjie_layer
    end

    return default_index
end

function WorldServerView:OnFlushXianjieView()
    if not self.node_list or not self:IsLoadedIndex(TabIndex.xianjie_boss) then
        return
    end
    
   --[[ local layer_data_list = XianJieBossWGData.Instance:GetLayerListBySlot(self.page)--XianJieBossWGData.Instance:GetBossListlayerForView()
    local default_index = self:GetXianJieLayerDefIndex()
    self.xianjie_layer_list:SetDataList(layer_data_list)
    self.xianjie_layer_list:SelectIndex(default_index)--]]
    --self:PlayLayerListTween(default_index)
    --boss次数
    local remain_time, total_time = XianJieBossWGData.Instance:GetXianjieEnterInfo()
    local color1 = remain_time <= 0 and COLOR3B.D_RED or COLOR3B.DEFAULT_NUM
    local str1 = ToColorStr(string.format("%d/%d", remain_time, total_time), color1)
    self.node_list.xianjie_times_txt.text.text = string.format(Language.XianJieBoss.EnterTimes, str1)
    self:FlushXianjieCostItem(remain_time)
    self:FlushXianJieBtnTxt()
end

function WorldServerView:FlushXianJieBossViewItem(item_id)
    if self:IsOpen() and self:IsLoadedIndex(TabIndex.xianjie_boss) then
        local other_cfg = XianJieBossWGData.Instance:GetOtherCfg()
        if other_cfg.enter_cost_item_id == item_id then
            self:FlushXianjieCostItem()
        end
    end
end

function WorldServerView:FlushXianjieCostItem(remain_time)
    remain_time = remain_time or XianJieBossWGData.Instance:GetXianjieEnterInfo()
    if remain_time > 0 then
        self.node_list.xianjie_times_txt:SetActive(true)
        self.node_list.xianjie_cost_container:SetActive(false)
        return
    end

    --self.node_list.xianjie_cost_container:SetActive(true)
    --self.node_list.xianjie_times_txt:SetActive(false)
    local other_cfg = XianJieBossWGData.Instance:GetOtherCfg()
    local enter_comsun = other_cfg.enter_cost_item_num
    local tiky_item_id = other_cfg.enter_cost_item_id

    local has_tiky_num = ItemWGData.Instance:GetItemNumInBagById(tiky_item_id)
    local color = has_tiky_num >= tonumber(enter_comsun) and COLOR3B.D_GREEN or COLOR3B.D_RED
    self.node_list.xianjie_cost_num.text.text = ToColorStr(has_tiky_num .. "/" .. enter_comsun, color)

    local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(tiky_item_id)
    self.node_list["xianjie_cost_item"].image:LoadSprite(bundle, asset)
end

function WorldServerView:FlushXianJieBtnTxt()
    local can_enter = XianJieBossWGData.Instance:GetIsCanEnterWithTime()
    local str = ""
    if not can_enter then
        local cfg = XianJieBossWGData.Instance:GetOtherCfg()
        local time = cfg and cfg.open_time or 0
        local timestr = string.format("%02d:%02d", time / 100, time % 100)
        str = string.format(Language.XianJieBoss.IsClosed, timestr)
    else
        str = Language.XianJieBoss.GoToKill
    end

    self.node_list.txt_xianjie_kill.text.text = str
end

function WorldServerView:OnClickAdd()
    local page, _ = XianJieBossWGData.Instance:GetCurSlotAndPage()
    if page <= self.page then
        TipWGCtrl.Instance:ShowSystemMsg(Language.FairyLandEquipment.ActGodBodyRemindDesc)
        return
    end

    self.page = self.page + 1
    self:FlushBtnAct()
end

function WorldServerView:OnClickSub()
    self.page = self.page - 1
    self:FlushBtnAct()
end

function WorldServerView:FlushBtnAct(is_first)
    local layer_default_index = 1
    local default_index = self.xianjie_btn_select_index or 1
    if is_first then
        if self.xianjie_btn_select_index == nil then
            default_index = XianJieBossWGData.Instance:GetBossDefaultIndex()
			self.xianjie_btn_select_index = default_index
        end

        local page, solt = XianJieBossWGData.Instance:GetCurSlotAndPage()
        layer_default_index = solt + 1
        self.page = XianJieBossWGData.Instance:GetCurSlotAndPage()
    end

    local max_num = XianJieBossWGData.Instance:GetLayerListNum()
    self.node_list["sub_btn"]:SetActive(self.page > 0)
    self.node_list["add_btn"]:SetActive(self.page < max_num)
    local layer_data_list = XianJieBossWGData.Instance:GetLayerListBySlot(self.page)
    self.xianjie_layer_list:SetDataList(layer_data_list)
     self.xianjie_layer_list:SelectIndex(layer_default_index)
    local boss_list = XianJieBossWGData.Instance:GetBossListByLayer(layer_data_list[1].layer)

    self.node_list.layer_text.text.text = layer_data_list[1].slot_name

    self.xianjie_list_data = boss_list

    self.xianjie_cambered_list:CreateCellList(#boss_list)
    local btn_item_list = self.xianjie_cambered_list:GetRenderList()
    for k, item_cell in ipairs(btn_item_list) do
        local item_data = self.xianjie_list_data[k]
        item_cell:SetData(item_data)

        if default_index == item_cell:GetIndex() then
            self:OnXianJieSelectedBtnChange(function()
                self:XianJieBossSelected(item_cell)
            end, true)
        end
    end
end

function WorldServerView:OnClickMenPiao()
    local other_cfg = XianJieBossWGData.Instance:GetOtherCfg()
    local tiky_item_id = other_cfg.enter_cost_item_id
    TipWGCtrl.Instance:OpenItem({ item_id = tiky_item_id })
end

--------------------------层Render --------------------------
BossXianJieLayerRender = BossXianJieLayerRender or BaseClass(BaseRender)

function BossXianJieLayerRender:__init()

end

function BossXianJieLayerRender:__delete()

end

function BossXianJieLayerRender:OnFlush()
    if not self.data then
        return
    end

    self.node_list["xj_men_img"].image:LoadSprite(ResPath.GetBossUI("a3_yyhj_icon_" .. (self.index % 5)))
    local slot, page = XianJieBossWGData.Instance:GetCurSlotAndPage()
    if self.data.slot_limit < slot then
        XUI.SetGraphicGrey(self.node_list.xj_men_img, false)
    else
        XUI.SetGraphicGrey(self.node_list.xj_men_img, self.index > page + 1)
    end
    
    --local cfg = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(self.data.index)
    --local need_lv = cfg.role_level_limit
    --local is_page_open = XianJieBossWGData.Instance:GetCurPageIsOpen(cfg.slot_limit, cfg.slot_page_limit)

    --local is_open = need_lv <= RoleWGData.Instance:GetRoleLevel() and is_page_open
    --XUI.SetGraphicGrey(self.node_list.layer_txt, not is_open)
    --self.node_list.layer_txt.text.text = string.format(Language.XianJieBoss.LayerTxt,
       -- NumberToChinaNumber(self.data.index))

    --local is_finish = false
    --self.node_list.had_finish:SetActive(is_finish)
    --local is_show = self:GetIsShowCurEffect()
    --XUI.SetGraphicGrey(self.node_list["xj_men_img"], not is_open)
end

function BossXianJieLayerRender:OnSelectChange(is_select)
    self.node_list.select_image:SetActive(is_select)
    local is_show = self:GetIsShowCurEffect()
    if is_show then
        return
    end

    local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_lock
    --local cfg = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(self.data.index)
    --local is_page_open = XianJieBossWGData.Instance:GetCurPageIsOpen(self.data.slot_limit, self.data.slot_page_limit)

    --[[if cfg.role_level_limit and role_level >= cfg.role_level_limit and is_page_open then
        is_lock = false
    else
        is_lock = true
    end--]]
end

function BossXianJieLayerRender:GetIsShowCurEffect()
    local is_show
    if self.data then
        is_show = XianJieBossWGData.Instance:GetBossLayerDefaultIndex() == self.data.index
    end
    return is_show
end

--------------------------Boss Render --------------------------
BossXianJieItemRender = BossXianJieItemRender or BaseClass(BaseRender)

function BossXianJieItemRender:__init()
    self.click_callback = nil
end

function BossXianJieItemRender:__delete()
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end

    self.click_callback = nil
end

function BossXianJieItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind(self.OnClick, self))
end

function BossXianJieItemRender:OnFlush()
    -- local bottom_color = self.data.bottom_color
    -- if bottom_color and bottom_color ~= "" and self.node_list.boss_bg then
    --     local bg_bundle, bg_asset = ResPath.GetBossUI("a1_boss_bg_" .. bottom_color)
    --     self.node_list["boss_bg"].image:LoadSprite(bg_bundle, bg_asset, function()
    --         self.node_list.boss_bg.image:SetNativeSize()
    --     end)
    -- end

	self.node_list["text_boss_name"].text.text = self.data.boss_name
	self.node_list.text_boss_lv.text.text = string.format(Language.Boss.LvText, self.data.boss_level)
	self.node_list["text_boss_state"].text.text = ""

	local monster_info = BossWGData.Instance:GetMonsterInfo(self.data.boss_id)
	self.node_list["text_boss_jie"].text.text = string.format(Language.Boss.JieShu,
		NumberToChinaNumber(monster_info.boss_jieshu))

	--self.node_list.boss_suo:SetActive(false)
    local bundle, asset = ResPath.GetBossIcon("wrod_boss_" .. self.data.big_icon)
    self.node_list["img_boss"].image:LoadSprite(bundle, asset, function()
        self.node_list.img_boss.image:SetNativeSize()
    end)
    local role_level = RoleWGData.Instance:GetRoleLevel()
    local is_page_open = XianJieBossWGData.Instance:GetCurPageIsOpen(self.data.slot_limit, self.data.slot_page_limit)
    if self.data.need_role_level and role_level >= self.data.need_role_level then
        self:ShowNormalInfo()
    else
        local is_level_limit = role_level < self.data.need_role_level
        self:ShowLockInfo(is_level_limit)
    end

    local is_cross = self.data.is_cross == 1
	-- self.node_list.gray_mask:SetActive(self.is_grey_show)
    local page_str2 = XianJieBossWGData.Instance:GetPageStr(self.data.slot_page_limit, true)
    -- self.node_list["ph_jieshu"]:SetActive(true)
    -- self.node_list["ph_jieshu"].text.text = string.format(Language.XianJieBoss.SlotSName, self.data.slot_name, page_str2)

    local server_info = XianJieBossWGData.Instance:GetBossCfgById(self.data.boss_id)
    if server_info and server_info.is_concern then
        self:RefreshConcernTag(server_info.is_concern > 0)
    else
        self:RefreshConcernTag(false)
    end
end

function BossXianJieItemRender:RefreshConcernTag(vis)
    self.node_list["focus_icon"]:SetActive(vis)
end

function BossXianJieItemRender:ShowLockInfo(is_level_limit)
    if self.refresh_event then
        GlobalTimerQuest:CancelQuest(self.refresh_event)
        self.refresh_event = nil
    end

	self.node_list["lbl_time"].text.text = ""

    if is_level_limit then
		--self.node_list.boss_suo:SetActive(true)
		self.node_list["text_boss_state"].text.text = string.format(ToColorStr(Language.Common.LevelUnLock3, COLOR3B.RED),
			RoleWGData.GetLevelString(self.data.need_role_level))
    else
        local layer = self.data.layer
        local before_layer = layer - 1
        local data = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(before_layer)
        if not data then
            data = XianJieBossWGData.Instance:GetBossLayerCfgByLayer(layer)
        end

        if layer == 1 then
            self.node_list.text_boss_state.text.text = Language.XianJieBoss.LockSlotFirst
        else
            local page_str2 = XianJieBossWGData.Instance:GetPageStr(data.slot_page_limit, true)
            self.node_list.text_boss_state.text.text = string.format(Language.XianJieBoss.LockSlotPage, self.data.slot_name,
                page_str2)
        end
    end
end

function BossXianJieItemRender:ShowNormalInfo()
    local color1 = COLOR3B.DEFAULT
    local role_level = RoleWGData.Instance:GetRoleLevel()
    if self.data.max_delta_level and role_level - self.data.boss_level >= self.data.max_delta_level then
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
        self.node_list["text_boss_state"].text.text = Language.Boss.LevelHighLimit
		--self.node_list.boss_suo:SetActive(true)
    else
        self:RefreshRemainTime()
        if self.refresh_event == nil then
            self.refresh_event = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.RefreshRemainTime, self), 1)
        end
    end
end

--刷新时间
function BossXianJieItemRender:RefreshRemainTime()
    local boss_server_info = XianJieBossWGData.Instance:GetBossCfgById(self.data.boss_id)
    local time = 0
    if not IsEmptyTable(boss_server_info) then
        time = (boss_server_info.next_refresh_time or 0) - TimeWGCtrl.Instance:GetServerTime()
    end

    local state = time > 1
	self.node_list["text_boss_state"].text.text = not state and Language.Boss.RefreshTime3 or ""
	-- self.node_list.gray_mask:SetActive(state)
    if state then
        self.node_list["lbl_time"].text.text = TimeUtil.FormatSecond(time)
    else
        self.node_list["lbl_time"].text.text = ""
        --self.node_list["lbl_time"].text.text = ToColorStr(Language.Boss.HasRefresh, COLOR3B.D_GREEN)
        if self.refresh_event then
            GlobalTimerQuest:CancelQuest(self.refresh_event)
            self.refresh_event = nil
        end
    end
end

function BossXianJieItemRender:OnSelectChange(is_select)
    self.node_list.select_image:SetActive(is_select)
end

-- 设置点击回调
function BossXianJieItemRender:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function BossXianJieItemRender:OnClick()
	if self.click_callback then
		self.click_callback()
	end
end

function BossXianJieItemRender:SetSelectedHL(index)
	local is_select = self.index == index

	self.node_list.select_image:SetActive(is_select)
end