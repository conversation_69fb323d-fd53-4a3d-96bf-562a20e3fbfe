
SpecialFuhuoView = SpecialFuhuoView or BaseClass(SafeBaseView)

SpecialFuhuoView.SPECIALCOUNTDOWN = "Special_fuhuo_countdown"

--世界boss五层复活疲劳buff后点击普通复活显示的面板
function SpecialFuhuoView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop

    self.view_name = "SpecialFuhuoView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(694, 440)})
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_special_fuhuo")
end

function SpecialFuhuoView:CloseCallBack()
	CountDownManager.Instance:RemoveCountDown(SpecialFuhuoView.SPECIALCOUNTDOWN)
end

function SpecialFuhuoView:ReleaseCallBack()

end

function SpecialFuhuoView:LoadCallBack()
    self.is_free_fuhuo = false

    self.node_list.title_view_name.text.text = Language.Boss.SpecialTishi

    self.node_list["btn_free_fuhuo"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoHere, self))
    self.node_list["btn_gold_fuhuo"].button:AddClickListener(BindTool.Bind(self.OnClickFuhuoHere, self))
    self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind(self.CancelClick, self))

    local item_config = ItemWGData.Instance:GetItemConfig(COMMON_CONSTS.RESURGENCE_STONE)
    if item_config then
        self.node_list["stone_icon"].image:LoadSprite(ResPath.GetItem(item_config.icon_id))
        self.node_list["text_stone_name"].text.text = item_config.name
    end
end

function SpecialFuhuoView:CancelClick()
    self:Close()
end

function SpecialFuhuoView:SetData(data)
    self.data = data
	self:Open()
end

function SpecialFuhuoView:OnFlush()
    if not self.data then 
        return
    end
    self:SetCountDown()
    self:FlushFuhuoBtnStatus()
    local coin_cost = FuhuoWGCtrl.Instance:GetFuhuoGold()
    if self.node_list and self.node_list.lbl_fuhuo_money2 then
        self.node_list.lbl_fuhuo_money2.text.text = coin_cost
    end
end

function SpecialFuhuoView:SetCountDown()
    local time = self.data.time or BossWGData.Instance:GetWorldBossRoleRealiveTime()
    if CountDownManager.Instance:HasCountDown(SpecialFuhuoView.SPECIALCOUNTDOWN) then
        self.node_list.lbl_tips.text.text = ""	
        CountDownManager.Instance:RemoveCountDown(SpecialFuhuoView.SPECIALCOUNTDOWN)
    end
    self.node_list.lbl_tips.text.text = string.format(Language.Boss.SpecialFuhuoDes, time - 1)
    if not CountDownManager.Instance:HasCountDown(SpecialFuhuoView.SPECIALCOUNTDOWN) then
        self:UpdateCountDownTime(0, time - 1)
        CountDownManager.Instance:AddCountDown(SpecialFuhuoView.SPECIALCOUNTDOWN,
        BindTool.Bind1(self.UpdateCountDownTime, self),BindTool.Bind1(self.CompleteTime, self),
        TimeWGCtrl.Instance:GetServerTime() + time, 0.5)
    end
end

function SpecialFuhuoView:FlushFuhuoBtnStatus()
    local is_active = RechargeWGData.Instance:IsActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
    local free_count = RechargeWGData.Instance:GetFreeFuHuoCount()
    local is_free_fuhuo = is_active and free_count > 0
    if is_free_fuhuo then
        self.node_list.free_fuhuo_count.text.text = string.format(Language.Fuhuo.FreeCountDesc, free_count)
    end
    self.is_free_fuhuo = is_free_fuhuo

    self.node_list.btn_free_fuhuo:SetActive(is_free_fuhuo)
    self.node_list.btn_gold_fuhuo:SetActive(not is_free_fuhuo)
    
    local fuhuo_stone = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.RESURGENCE_STONE)
    self.node_list.stone_fuhuo:SetActive(fuhuo_stone > 0)
    self.node_list.xianyu_fuhuo:SetActive(fuhuo_stone <= 0)
    self.node_list.stone_num.text.text = string.format(Language.Fuhuo.Stone, fuhuo_stone <= 999 and fuhuo_stone or 999)
end

-- 倒计时每次循环执行的函数
function SpecialFuhuoView:UpdateCountDownTime(elapse_time, total_time)
    local last_time = math.floor(total_time - elapse_time)
    self.node_list.lbl_tips.text.text = string.format(Language.Boss.SpecialFuhuoDes, last_time)
end

function SpecialFuhuoView:CompleteTime()
    self.node_list.lbl_tips.text.text = ""	
    CountDownManager.Instance:RemoveCountDown(SpecialFuhuoView.SPECIALCOUNTDOWN)
    self:Close()
end

function SpecialFuhuoView:OnClickFuhuoHere()
    if self.is_free_fuhuo then
        FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.TOZI_FREE)
    else
        local fuhuo_stone = ItemWGData.Instance:GetItemNumInBagById(COMMON_CONSTS.RESURGENCE_STONE)
        if fuhuo_stone > 0 then
            FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_STUFF, 0, -1)
        else
            local gold = FuhuoWGCtrl.Instance:GetFuhuoGold()
            if not RoleWGData.Instance:GetIsEnoughAllGold(gold) then
                VipWGCtrl.Instance:OpenTipNoGold()
            end

            FightWGCtrl.SendRoleReAliveReq(REALIVE_TYPE.HERE_ICON, 0, -1)
        end
    end
	
	--self.fuhuo_type = FuHuoType.Here
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

	-- 原地复活后需要默认挂机状态。增加攻击机制，优先级顺序如下
	-- 当前我攻击的玩家
	-- 当前攻击我的玩家（最近）
	-- 最近可攻击的玩家
	-- 攻击boss（怪物）

	local select_obj = nil
	if GuajiCache.target_obj and GuajiCache.target_obj:IsRole() then
		select_obj = GuajiCache.target_obj
	end
	GuajiCache.target_obj = nil
	MoveCache.target_obj = nil

	if not select_obj then
		select_obj = ReviveWGData.Instance:GetBeHitData()
	end

	if select_obj and select_obj:IsRole() and Scene.Instance:IsEnemy(select_obj) then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, select_obj, SceneTargetSelectType.SELECT)
    end
    self:Close()
end