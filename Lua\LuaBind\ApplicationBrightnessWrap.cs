﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class ApplicationBrightnessWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("ApplicationBrightness");
		<PERSON><PERSON>RegFunction("SetApplicationBrightnessTo", SetApplicationBrightnessTo);
		<PERSON><PERSON>RegFunction("GetApplicationBrightness", GetApplicationBrightness);
		L.End<PERSON>taticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetApplicationBrightnessTo(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			ApplicationBrightness.SetApplicationBrightnessTo(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetApplicationBrightness(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = ApplicationBrightness.GetApplicationBrightness();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

