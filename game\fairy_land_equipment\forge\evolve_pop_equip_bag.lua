EvolvePopEquipBag = EvolvePopEquipBag or BaseClass(SafeBaseView)

function EvolvePopEquipBag:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(836, 550)})
	self:AddViewResource(0, "uis/view/fairy_land_equipment_ui_prefab", "layout_evolve_equip_bag")
end

function EvolvePopEquipBag:__delete()
end

function EvolvePopEquipBag:ReleaseCallBack()
	if self.equip_bag_grid then
		self.equip_bag_grid:DeleteMe()
		self.equip_bag_grid = nil
	end

	if self.evolve_equip_sepcial_alert then
		self.evolve_equip_sepcial_alert:DeleteMe()
		self.evolve_equip_sepcial_alert = nil
	end
end


function EvolvePopEquipBag:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FairyLandEquipment.EvolvePopEquipTitle

	self.equip_bag_grid = ShenShouGrid.New()
	local cell_count = GOD_BODY_ENUM.BAG_GRID_NUM
	self.equip_bag_grid:CreateCells({col = 8, cell_count = cell_count, list_view = self.node_list["ph_equip_bag_grid"], itemRender = EvolvePopEquipCell})
	self.equip_bag_grid:SetSelectCallBack(BindTool.Bind1(self.SelectCellCallBack, self))
	self.equip_bag_grid:SetIsMultiSelect(true)
	self.equip_bag_grid:SetStartZeroIndex(false)

	--批量贡献
	self.node_list.btn_insert.button:AddClickListener(BindTool.Bind(self.OnClcikInsert,self))
	self.node_list.btn_auto_select.button:AddClickListener(BindTool.Bind(self.OnClickAutoSelect,self))

	self.node_list["insert_tip"].text.text = Language.FairyLandEquipment.EvolveAddEquipTip
end

--data内容 slot 当前所选的神体  part 当前装备部位 
function EvolvePopEquipBag:SetData(data)
	self.data = data
end

function EvolvePopEquipBag:OnFlush()
	self:FlushBagList()
end

function EvolvePopEquipBag:SelectCellCallBack(cell, can_not_select)
	if can_not_select then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.EvolveInsertBtnTip)--仓库容量不足，不可以捐献
		return
	end
	self:FlushConsume()
end

function EvolvePopEquipBag:FlushBagList(is_select)
	if IsEmptyTable(self.data) then
		return
	end

	local slot = self.data.slot
	local part = self.data.part
	local data = FairyLandEquipmentWGData.Instance:GetEvolveEquipBagList(slot, part)
    self.equip_bag_grid:SetDataList(data)
    self.equip_bag_grid:SetFLEQEvolvePopSelect()
	self:FlushConsume()
end

function EvolvePopEquipBag:ClearBagList(is_select)
	self.equip_bag_grid:CancleAllSelectCell()
	self:FlushBagList(is_select)
end

function EvolvePopEquipBag:OnClickAutoSelect()
	if IsEmptyTable(self.data) then
		return
	end

	local slot = self.data.slot
	local part = self.data.part
	local equip_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)
	if IsEmptyTable(equip_info) then
		return
	end

	local data = FairyLandEquipmentWGData.Instance:GetEvolveEquipBagList(slot, part)
	for k, v in pairs(data) do
		if self.equip_bag_grid:GetNoSelectState() then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.FairyLandEquipment.EvolveInsertBtnTip)
			return
		end
		self.equip_bag_grid:SetAddSelectCellIndex(k)
		self.equip_bag_grid:RefreshSelectCellState()
		self:FlushConsume()
	end
end


function EvolvePopEquipBag:FlushConsume()
	if IsEmptyTable(self.data) then
		return
	end

	local slot = self.data.slot
	local part = self.data.part
	local equip_info = FairyLandEquipmentWGData.Instance:GetHolyEquipWearPartInfo(slot, part)
	if IsEmptyTable(equip_info) then
		return
	end

	local select_data = self.equip_bag_grid:GetAllSelectCell()
	local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(slot, part)
	local upgrade_cfg = FairyLandEquipmentWGData.Instance:GetGBEquipUpgradeCfg(slot, part, grade + 1)
	local init_per = upgrade_cfg and (upgrade_cfg.init_per / 100) or 0
	local add_per = 0
	if not IsEmptyTable(select_data) then
		for k,v in pairs(select_data) do
			local equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(v.item_id)
			local is_special = FairyLandEquipmentWGData.Instance:GetIsSpecialType(part)
			local per_cfg = FairyLandEquipmentWGData.Instance:GetEvolveEquipAddGradePerCfg(equip_cfg.slot, equip_cfg.color, grade + 1)

			local add_per_key = "add_per_list"
			local add_specail_per_key = "add_specail_per_list"
			local select_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(v.part)
			if select_spe_part then
				add_per_key = "add_per_list_2"
				add_specail_per_key = "add_specail_per_list_2"
			end

			if is_special then
				if per_cfg and per_cfg[add_specail_per_key] then
					local add_per_list = per_cfg[add_specail_per_key]
					local slot_add_per = add_per_list[slot + 1] or 0
					add_per = add_per + (slot_add_per / 100)
				end
			else
				if per_cfg and per_cfg[add_per_key] then
					local add_per_list = per_cfg[add_per_key]
					local slot_add_per = add_per_list[slot + 1] or 0
					add_per = add_per + (slot_add_per / 100)
				end
			end
		end
	end

	if add_per > 0 then
		self.equip_bag_grid:SetNoSelectState(init_per + add_per >= 100)
		self.node_list["btn_insert_txt"].text.text = Language.FairyLandEquipment.EvolveAddEquipBtn2
		XUI.SetButtonEnabled(self.node_list["btn_insert"], true)
		XUI.SetGraphicGrey(self.node_list["btn_insert_txt"], false)
	else
		self.equip_bag_grid:SetNoSelectState(false)
		self.node_list["btn_insert_txt"].text.text = Language.FairyLandEquipment.EvolveAddEquipBtn1
		XUI.SetButtonEnabled(self.node_list["btn_insert"], false)
		XUI.SetGraphicGrey(self.node_list["btn_insert_txt"], true)
	end
	
	self.node_list["insert_preface_tip"].text.text = string.format(Language.FairyLandEquipment.EvolveAddEquipPrefaceTip,
													#select_data, init_per + add_per)
end


function EvolvePopEquipBag:OnClcikInsert()
	if IsEmptyTable(self.data) then
		return
	end

	local slot = self.data.slot
	local part_index = self.data.part

	local select_data = self.equip_bag_grid:GetAllSelectCell()
	local add_per = 0
	local grade = FairyLandEquipmentWGData.Instance:GetPartGrade(slot, part_index)
	if select_data and not next(select_data) then
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.JuanXianTips)
		return
	else
		local count = #select_data
		local bag_list = {}
		local select_has_special = false
		for i=1, count do
			local data = select_data[i]
			bag_list[i] = -1
			if not IsEmptyTable(data) then
				bag_list[i] = data.index
				local equip_cfg = FairyLandEquipmentWGData.Instance:GetHolyEquipItemCfg(data.item_id)
				local select_is_special = FairyLandEquipmentWGData.Instance:GetIsSpecialType(data.part)
				if not select_has_special and select_is_special then
					select_has_special = true
				end
				local is_special = FairyLandEquipmentWGData.Instance:GetIsSpecialType(part_index)
				local per_cfg = FairyLandEquipmentWGData.Instance:GetEvolveEquipAddGradePerCfg(equip_cfg.slot, equip_cfg.color, grade + 1)

				local add_per_key = "add_per_list"
				local add_specail_per_key = "add_specail_per_list"
				local select_spe_part = FairyLandEquipmentWGData.Instance:GetIsSpecialType(data.part)
				if select_spe_part then
					add_per_key = "add_per_list_2"
					add_specail_per_key = "add_specail_per_list_2"
				end

				if is_special then
					if per_cfg and per_cfg[add_specail_per_key] then
						local add_per_list = per_cfg[add_specail_per_key]
						local slot_add_per = add_per_list[slot + 1] or 0
						add_per = add_per + (slot_add_per / 100)
					end
				else
					if per_cfg and per_cfg[add_per_key] then
						local add_per_list = per_cfg[add_per_key]
						local slot_add_per = add_per_list[slot + 1] or 0
						add_per = add_per + (slot_add_per / 100)
					end
				end
				
			end
		end

		local ok_fun = function ()
			FairyLandEquipmentWGData.Instance:SetEvolveSelectBagParam(bag_list, add_per, count)
			FairyLandEquipmentWGCtrl.Instance:FlushEquipMentView(TabIndex.fl_eq_forge_evolve, "evolve_select_bag")
			self:Close()
		end

		if select_has_special then
			if not self.evolve_equip_sepcial_alert then
		        self.evolve_equip_sepcial_alert = Alert.New()
		    end
			
		    local str = Language.FairyLandEquipment.EvolveEquipSpecialAlert
		    self.evolve_equip_sepcial_alert:SetShowCheckBox(true, "evolve_equip_sepcial_alert")
		    self.evolve_equip_sepcial_alert:SetOkFunc(ok_fun)
		    self.evolve_equip_sepcial_alert:SetLableString(str)
		    self.evolve_equip_sepcial_alert:SetCheckBoxDefaultSelect(false)
		    self.evolve_equip_sepcial_alert:Open()
		else
			ok_fun()
		end
	end
end



EvolvePopEquipCell = EvolvePopEquipCell or BaseClass(ItemCell)
function EvolvePopEquipCell:__init()
	self.need_default_eff = true
	self:SetItemTipFrom(ItemTip.FROM_BAG_ON_GUILD_STORGE)
    self:SetIsShowTips(false)
    self:UseNewSelectEffect(true)
end

function EvolvePopEquipCell:SetSelect(is_select, item_call_back)
	ItemCell.SetSelectEffect(self, is_select)	
end

