FairyLandEquipmentWGData = FairyLandEquipmentWGData or BaseClass()
local this = FairyLandEquipmentWGData
function this:__init()
	if this.Instance then
		error("[FairyLandEquipmentWGData] Attempt to create singleton twice!")
		return
	end
	this.Instance = self
	self:InitParam()

	self.main_cfg = ConfigManager.Instance:GetAutoConfig("xianjie_equip_auto")
    self.god_body_other_cfg = self.main_cfg.other[1]

	self:InitGodBodyCfgData()
	self:InitHolyEquipData()
	self:InitStrengthenData()
	self:InitEvolveData()
	self:InitUpqualityData()
	self:InitGodBodyList()

	-- 【神体】
	RemindManager.Instance:Register(RemindName.FLE_GodBook, BindTool.Bind(self.GetGodBookRemind, self))
	RemindManager.Instance:Register(RemindName.FLE_GodBody, BindTool.Bind(self.GetGodBodyRemind, self))
	--【圣装】
	RemindManager.Instance:Register(RemindName.FLE_HolyEquip, BindTool.Bind(self.GetHolyEquipRemind, self))
	self:RegisterGodBodyRemindInBag()
end

function this:__delete()
	for k, v in pairs(self.fairy_land_god_body_list) do
		v:DeleteMe()
	end
	self.fairy_land_god_body_list = nil

	RemindManager.Instance:UnRegister(RemindName.FLE_GodBook)
	RemindManager.Instance:UnRegister(RemindName.FLE_GodBody)
	RemindManager.Instance:UnRegister(RemindName.FLE_HolyEquip)
	self:DeleteStrengthenData()
	self:DeleteEvolveData()
	self:DeleteUpqualityData()

	this.Instance = nil
end

function this:GetJieLingRemind()
	if self:GetGodBodyRemind() == 1 or self:GetHolyEquipRemind() == 1 or self:IsShowStrengthenRedPoint() == 1 or
	 self:IsShowEvolveRedPoint() == 1 or self:IsShowUpqualityRedPoint() == 1 then
		return true
	end

	return false
end

function this:InitParam()
	self:InitGodBodyParam()
	self:InitHolyEquipParam()
end

-- 初始化神体
function this:InitGodBodyList()
	self.fairy_land_god_body_list = {}
	local gb_cfg = self:GetGodBodyAllCfg()
	for k, v in pairs(gb_cfg) do
		local index = v.slot
		self.fairy_land_god_body_list[index] = FairyLandGodBody.New()
		self.fairy_land_god_body_list[index]:SetSlotIndex(index)
	end
end

-- 神体列表
function this:GetGodBodyList()
	return self.fairy_land_god_body_list
end

-- 当前可展示的神体列表
function this:GetCurShowGodBodyList()
	local ver_num = self:GetGodBodyMaxNum()
	local max_show_num = self:GetGodBodyMaxShowNum1()
	local data_list = {}
	for i = 0, ver_num do
		if i <= max_show_num then
			data_list[i] = self.fairy_land_god_body_list[i]
		else
			break
		end
	end

	return data_list
end

-- 获取神体数据
function this:GetGodBodyData(slot)
	return self.fairy_land_god_body_list[slot]
end

-- 更新部分数据 - 神体部分
function this:UpdatePartDataByGodBody(slot, info)
	local god_body = self:GetGodBodyData(slot)
	if god_body then
		god_body:SetGodBodyData(info)
	end
end

-- 获取标签展示列表
function this:GetViewIndexShowList(slot)
	local show_list = {}
	local slot_data = self:GetGodBodyData(slot)
	local is_act = slot_data and slot_data:GetIsAct()

	local index = TabIndex.fairy_land_eq_god_body
	local is_show = is_act
	show_list[index] = {index = index, is_show = is_show}

	index = TabIndex.fairy_land_eq_holy_equip
	is_show = is_act
	show_list[index] = {index = index, is_show = is_show}

	local had_wear = false
	local had_wear_special = false
	local wear_lsit = self:GetHolyEquipWearList(slot)
	if is_act and not IsEmptyTable(wear_lsit) then
		for part, data in pairs(wear_lsit) do
			if data.item_id and data.item_id > 0 then
				had_wear = true
				if part >= XIANJIE_EQUIP_TYPE.TEJIE and part <= XIANJIE_EQUIP_TYPE.XIANYIN then
					had_wear_special = true
				end
			end
		end
	end

	index = TabIndex.fl_eq_forge_strengthen
	show_list[index] = {index = index, is_show = had_wear}
	index = TabIndex.fl_eq_forge_evolve
	show_list[index] = {index = index, is_show = had_wear}
	index = TabIndex.fl_eq_forge_upquality
	show_list[index] = {index = index, is_show = had_wear_special}

	return show_list
end

-- 获取神体最大展示数
function this:GetGodBodyMaxShowNum()
	local slot = 0
	local is_act = false
	local list = self:GetGodBodyList()
	for i = 0, #list do
		local data = list[i]
		if data then
			is_act = data:GetIsAct()
			slot = data:GetSlotIndex()
			if not is_act then
				break
			end
		end
	end

	return slot
end

-- 获取神体最大展示数
function this:GetGodBodyMaxShowNum1()
	local slot = -1
	local is_act = false
	local list = self:GetGodBodyList()
	for i = 0, #list do
		local data = list[i]
		if data then
			is_act = data:GetIsAct()
			if not is_act then
				break
			else
				slot = data:GetSlotIndex()
			end
		end
	end

	return slot
end

-- 获取当前天书最大显示
function this:GetGodBookCurMaxShow()
	local slot = self:GetGodBodyMaxShowNum()
	local page = self:GetSlotMaxJumpPage(slot)

	return slot, page
end

-- 获取当前天书最大显示
function this:GetSlotMaxJumpPage(slot)
	local page
	local max_page = self:GetActBodyNeedNum()
	for i = 0, max_page - 1 do
		if self:GetPageActState(slot, i) == GOODS_STATE_TYPE.READY_ACT then
			page = i
			break
		end
	end

	local page_gather_num = self:GetPageGatherNum(slot)
	local max_jump_page = page_gather_num - 1 >= 0 and page_gather_num - 1 or 0
	page = page or max_jump_page
	return page
end

function this:GetOtherCfg()
	return self.god_body_other_cfg
end

function this:GetOtherCfgByKey(key)
	return self.god_body_other_cfg and self.god_body_other_cfg[key]
end

-- slot page
function this:GetGodBookJump()
	local cur_max_slot = self:GetCurMaxGodBody()
	local max_slot_num = self:GetGodBodyMaxNum()
	local max_page_num = self:GetActBodyNeedNum() - 1
	local jump_slot = cur_max_slot
	local jump_page = 0
	local gb_list = self:GetGodBodyList()
	local state = GOODS_STATE_TYPE.READY_ACT
	local break_flag = false
	for slot = cur_max_slot, #gb_list do
		local data = gb_list[slot]
		if not data:GetIsAct() then
			local page_gather_num = self:GetPageGatherNum(slot) - 1
			page_gather_num = page_gather_num > 0 and page_gather_num or 0
			for page = page_gather_num, max_page_num do
				if self:GetPageRemind(slot, page) then
					jump_slot = slot
					jump_page = page
					break
				elseif self:GetPageActState(slot, page) == state then
					jump_slot = slot
					jump_page = page
					break
				end
			end

			break_flag = true
		else
			jump_slot = slot
			jump_page = max_page_num
		end

		if break_flag then
			break
		end
	end

	jump_slot = jump_slot > 0 and jump_slot or 0
	jump_slot = jump_slot < max_slot_num and jump_slot or max_slot_num

	jump_page = jump_page > 0 and jump_page or 0
	jump_page = jump_page < max_page_num and jump_page or max_page_num
	return jump_slot, jump_page
end

function this:GetGodBodyJump(view_cache_index)
	local cur_max_slot = self:GetCurMaxGodBody()
	local default_slot = view_cache_index or cur_max_slot
	for slot = 0, cur_max_slot do
		if self:GetGodBodySlotRemind(slot) then
			default_slot = slot
			break
		end
	end

	return default_slot
end

function this:GetHolyEquipJump(view_cache_index)
	local cur_max_slot = self:GetCurMaxGodBody()
	local default_slot = view_cache_index or cur_max_slot
	for slot = 0, cur_max_slot do
		if self:GetSlotHolyEquipRemind(slot) then
			default_slot = slot
			break
		end
	end

	return default_slot
end

function this:ToBeStrengthen(remind, remind_type, view_index, view_key, view_param_t)
	MainuiWGCtrl.Instance:InvateTip(remind_type, remind, function()
		FairyLandEquipmentWGCtrl.Instance:OpenView(view_index, view_key, view_param_t)
		return true
	end)
end

function this:ToBeStrengthenRuleView(remind, remind_type, view_key, view_param_t)
	MainuiWGCtrl.Instance:InvateTip(remind_type, remind, function()
		FairyLandEquipmentWGCtrl.Instance:OpenRuleViewByParam(0, view_key, view_param_t)
		return true
	end)
end

-- 点击神体标签跳转功能标签
function this:GetSelectBodyJumpRemind(slot)
	if self:GetGodBodySlotRemind(slot) then
		return TabIndex.fairy_land_eq_god_body
	end

	if self:GetSlotHolyEquipRemind(slot) then
		return TabIndex.fairy_land_eq_holy_equip
	end

	if self:GetStrengthenRedPointBySlot(slot) then
		return TabIndex.fl_eq_forge_strengthen
	end

	if self:GetEvolveSlotRemind(slot) == 1 then
		return TabIndex.fl_eq_forge_evolve
	end

	if self:GetUpqualitySlotRemind(slot) == 1 then
		return TabIndex.fl_eq_forge_upquality
	end

	return 1
end

-- 点击神体标签跳转功能标签
function this:GetOpenViewJump()
	local cur_act_slot = -1
	local show_list = self:GetGodBodyList()
    for k,v in pairs(show_list) do
        if v:GetIsAct() then
            cur_act_slot = cur_act_slot + 1
        end
    end

	local max_slot_num = self:GetGodBodyMaxNum()
	local default_slot = cur_act_slot
	default_slot = default_slot >= max_slot_num and max_slot_num or default_slot
	local default_tab_index = TabIndex.fairy_land_eq_god_body

    for slot = 0, cur_act_slot do
		local data = show_list[slot]
		if data then
			local is_act = data:GetIsAct()
			if is_act then
				if self:GetGodBodySlotRemind(slot) then
					return slot, TabIndex.fairy_land_eq_god_body
				end
			
				if self:GetSlotHolyEquipRemind(slot) then
					return slot, TabIndex.fairy_land_eq_holy_equip
				end
			
				if self:GetStrengthenRedPointBySlot(slot) then
					return slot, TabIndex.fl_eq_forge_strengthen
				end
			
				if self:GetEvolveSlotRemind(slot) == 1 then
					return slot, TabIndex.fl_eq_forge_evolve
				end
			
				if self:GetUpqualitySlotRemind(slot) == 1 then
					return slot, TabIndex.fl_eq_forge_upquality
				end
			end
		end
	end

	return default_slot, default_tab_index
end

function this:GetForgeRemind(slot)
	local remind = self:GetStrengthenRedPointBySlot(slot)
    if remind then
        return true
    end
	
    remind = self:GetEvolveSlotRemind(slot) == 1
    if remind then
        return true
    end

    remind = self:GetUpqualitySlotRemind(slot) == 1
    if remind then
         return true
    end

    return false
end