require("game/new_festival_activity/new_festival_raffle/new_festival_raffle_view")
require("game/new_festival_activity/new_festival_raffle/new_festival_raffle_wg_data")
require("game/new_festival_activity/new_festival_raffle/new_fes_act_probability_view")

NewFestivalRaffleWGCtrl = NewFestivalRaffleWGCtrl or BaseClass(BaseWGCtrl)
function NewFestivalRaffleWGCtrl:__init()
	if NewFestivalRaffleWGCtrl.Instance then
		ErrorLog("[NewFestivalRaffleWGCtrl] Attemp to create a singleton twice !")
	end
	NewFestivalRaffleWGCtrl.Instance = self

	self.data = NewFestivalRaffleWGData.New()
	self.probability_view = NewFesActProbabilityView.New()

	self:RegisterAllProtocols()

	self.sd_item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.sd_item_data_change)
end

function NewFestivalRaffleWGCtrl:__delete()
	NewFestivalRaffleWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	self.probability_view:DeleteMe()
	self.probability_view = nil

	if self.sd_item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.sd_item_data_change)
		self.sd_item_data_change = nil
	end
end

function NewFestivalRaffleWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCNewFestivalSDInfo,'OnSCNewFestivalSDInfo')
	self:RegisterProtocol(SCNewFestivalSDRewardInfo, "OnSCNewFestivalSDRewardInfo")
end

--请求
function NewFestivalRaffleWGCtrl:SendReq(opera_type, param1, param2)
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.NEW_JRHD_JRSD,
		opera_type = opera_type,
		param_1 = param1,
		param_2 = param2,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function NewFestivalRaffleWGCtrl:OnSCNewFestivalSDInfo(protocol)
	-- print_error("====AllInfo=====",protocol)
	self.data:SetAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2346)

	RemindManager.Instance:Fire(RemindName.NewFestivalRaffle)
end

function NewFestivalRaffleWGCtrl:OnSCNewFestivalSDRewardInfo(protocol)
	-- print_error("====RewardInfo=====",protocol)
	local btn_index = self.data:CacheOrGetDrawIndex()
	if not btn_index then
		return
	end

	local cfg = self.data:GetDrawTypeCfg()
	local btn_cfg = cfg[btn_index]
	if not btn_cfg then
		return
	end

	local str = string.format(Language.NewFestivalActivity.BtnStr, btn_cfg.draw_num)
	local ok_func = function ()
		ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2346)

		self:ClickUse(btn_index, function()
			self:SendReq(OA_NEW_FES_ACT_SD_OPERATE_TYPE.DRAW, btn_cfg.draw_button)
		end)
	end

	local data_list = protocol.reward_list
	local big_reward_id = protocol.big_reward_id
	local reward_list = {}
	for k, v in ipairs(data_list) do
		if v.item_id ~= big_reward_id then
			table.insert(reward_list, v)
		end
	end

	local other_info = {}
	local consume_num = btn_cfg.draw_item.num
	other_info.again_text = str
	other_info.stuff_id = btn_cfg.draw_item.item_id
	other_info.times = consume_num
	other_info.spend = btn_cfg.consume_lingyu_num
	local best_data = {}
	if big_reward_id ~= 0 then
		best_data.item_id = big_reward_id
	else
		best_data = nil
	end
	other_info.best_data = best_data

	TipWGCtrl.Instance:ShowGetCommonReward(reward_list, ok_func, other_info)
end

function NewFestivalRaffleWGCtrl:ClickUse(index, func)
	local cfg = self.data:GetDrawTypeCfg()
	local num = ItemWGData.Instance:GetItemNumInBagById(cfg[index].draw_item.item_id)
	if num < cfg[index].draw_item.num then
		if not self.alert then
			self.alert = Alert.New()
		end
		self.alert:ClearCheckHook()
		self.alert:SetShowCheckBox(true, "new_fes_sd_fresh")
		self.alert:SetCheckBoxDefaultSelect(false)
		local item_cfg = ItemWGData.Instance:GetItemConfig(cfg[index].draw_item.item_id)
		local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		local cost = cfg[index].consume_lingyu_num * (cfg[index].draw_item.num - num)
		local str = string.format(Language.SunRainbow.CostStr, name, cost)

		self.alert:SetLableString(str)
		self.alert:SetOkFunc(func)
		self.alert:Open()
	else
		func()
	end
end

function NewFestivalRaffleWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE  then
		local check_list = self.data:GetItemDataChangeList()
		for i, v in pairs(check_list) do
			if v == change_item_id then
				ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_2346)
			end
		end
	end
end

function NewFestivalRaffleWGCtrl:OpenSDProbabilityView()
    self.probability_view:Open()
end