ConsumeDiscountView = ConsumeDiscountView or BaseClass(SafeBaseView)
function ConsumeDiscountView:__init()
	self:AddViewResource(0, "uis/view/consume_discount_ui_prefab", "layout_consume_discount")
end

function ConsumeDiscountView:__delete()
	
end

function ConsumeDiscountView:ReleaseCallBack()
	if self.consume_discount_list then
		self.consume_discount_list:DeleteMe()
		self.consume_discount_list = nil
	end
	if self.consume_gift then
		for k,v in pairs(self.consume_gift) do
			v:DeleteMe()
		end
		self.consume_gift = {}
	end
	self.need_refresh = nil
	self.has_load_callback = nil
end

function ConsumeDiscountView:LoadCallBack()
	self.consume_gift = {}
	self.node_list.btn_recharge.button:AddClickListener(BindTool.Bind1(self.OnClickBtnRecharge, self))
	self.consume_discount_list = AsyncListView.New(ConsumeDiscountRender,self.node_list.ph_item_list)
	self:GetActiveTime()
	self.has_load_callback = true
	if self.need_refresh then
		self.need_refresh = false
		self:RefreshView()
	end
end

function ConsumeDiscountView:__delete()
	if CountDownManager.Instance:HasCountDown("consume_discount") then
		CountDownManager.Instance:RemoveCountDown("consume_discount")
	end
end
function ConsumeDiscountView:GetActiveTime()
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME)
	local star_str = ""
	local end_str = ""
	if act_info then
		star_str = TimeUtil.FormatSecond2MYHM(act_info.start_time)
		end_str = TimeUtil.FormatSecond2MYHM(act_info.next_time)
	end
	local str = (star_str .. "----" .. end_str)
	self.node_list.activity_state.text.text = string.format(Language.ConsumeDiscount.DescTips,str)
end
function ConsumeDiscountView:OpenCallBack()
	if CountDownManager.Instance:HasCountDown("consume_discount") then
		CountDownManager.Instance:RemoveCountDown("consume_discount")
	end
	local act_cornucopia_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME) or {}
	if act_cornucopia_info.status == ACTIVITY_STATUS.OPEN then
		local next_time = act_cornucopia_info.next_time or 0
		CountDownManager.Instance:AddCountDown("consume_discount", BindTool.Bind1(self.UpdataRollerTime, self), BindTool.Bind1(self.CompleteRollerTime, self), next_time, nil, 1)	
	else
		self:CompleteRollerTime()
	end
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME,
		opera_type = RA_CONTINUE_CONSUME_OPERA_TYPE.RA_CONTINUME_CONSUME_OPERA_TYPE_QUERY_INFO,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ConsumeDiscountView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("consume_discount") then
		CountDownManager.Instance:RemoveCountDown("consume_discount")
	end
end

function ConsumeDiscountView:OnFlush()
	self:RefreshView()
end

function ConsumeDiscountView:RefreshView()
	if not self.has_load_callback then
		self.need_refresh = true
		return
	end
	local consume_info = ConsumeDiscountWGData.Instance:GetRAContinueConsumeInfo()

	local randact_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	if consume_info and randact_cfg then

		local rand_t = randact_cfg.continue_consume
		local continue_consume_cfg = ServerActivityWGData.Instance:GetRandActivityConfig(rand_t, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME)
		local continue_consume_fetch_extra_reward_need_days = randact_cfg.other[1].continue_consume_fetch_extra_reward_need_days
		self.node_list.label_succ_days.text.text = string.format(Language.ContinuousRecharge.RechargeTips3,consume_info.continue_days)
		local data_list = ConsumeDiscountWGData.Instance:GetRewardData()
		self.consume_discount_list:SetDataList(data_list)
	end
end

function ConsumeDiscountView:UpdataRollerTime(elapse_time, next_time)
	local time = next_time - elapse_time
	if self.node_list.shengyul_time ~= nil then
		if time > 0 then
			local format_time = TimeUtil.Format2TableDHMS(time)
			local str_list = Language.Common.TimeList
			local time_str = ""
			if format_time.day > 0 then
				time_str = string.format(Language.ConsumeDiscount.tiemshow1,format_time.day,format_time.hour,format_time.min,format_time.s)
			else
				time_str = string.format(Language.ConsumeDiscount.tiemshow2,format_time.hour,format_time.min,format_time.s)
			end
			self.node_list["shengyul_time"].text.text = time_str

		end
	end
end

function ConsumeDiscountView:CompleteRollerTime()
	if self.label_time ~= nil then
		self.label_time.text.tetx = "0"
	end
end

function ConsumeDiscountView:OnClickToGetReward()
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME,
		opera_type = RA_CONTINUE_CONSUME_OPERA_TYPE.RA_CONTINUE_CONSUME_OPEAR_TYPE_FETCH_EXTRA_REWARD,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ConsumeDiscountView:OnClickBtnTips()
	local str = string.format(Language.ConsumeDiscount.DescTips)
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.ConsumeDiscount.DescTipTitle)
		role_tip:SetContent(str)
	else
		print_error("ContinuousRechargeView:OnClickBtnTips()","OnClickBtnMLImageChongTip() can not find the get way!")
	end
end

function ConsumeDiscountView:OnClickBtnRecharge()
	RechargeWGCtrl.Instance:Open()
end

---------------itemRender-------------------------
ConsumeDiscountRender = ConsumeDiscountRender or BaseClass(BaseRender)

function ConsumeDiscountRender:__init()
	
end

function ConsumeDiscountRender:__delete()
	if self.consume_discount_cell then
		for k,v in pairs(self.consume_discount_cell) do
			v:DeleteMe()
		end
		self.consume_discount_cell = nil
	end
end

function ConsumeDiscountRender:LoadCallBack()
	self.consume_discount_cell = {}
	for i = 1, 4 do
		self.consume_discount_cell[i] = ItemCell.New()
		self.consume_discount_cell[i]:SetInstanceParent(self.node_list["ph_cell_" .. i])
	end
	self.node_list.btn_gift_receive.button:AddClickListener(BindTool.Bind2(self.OnClickGetReward, self, 1))	
end

function ConsumeDiscountRender:OnFlush()
	if nil == self.data then return end
	local consume_info = ConsumeDiscountWGData.Instance:GetRAContinueConsumeInfo()
	local has_fetch_flag = bit:d2b(consume_info.has_fetch_flag)
	local is_not_lingqu = has_fetch_flag[32 - self.data.day_index] == 0

	if self.data.day_index == consume_info.current_day_index and consume_info.cur_consume_gold >= self.data.need_consume_gold and is_not_lingqu then
		-- XUI.SetButtonEnabled(self.node_list["btn_gift_receive"], true)
		self.node_list["btn_gift_receive"]:SetActive(true)
		self.node_list["Img_YLQ"]:SetActive(false)
		self.node_list["Img_NLQ"]:SetActive(false)		--未达成
	else
		self.node_list["btn_gift_receive"]:SetActive(false)
		self.node_list["Img_YLQ"]:SetActive(false)
		self.node_list["Img_NLQ"]:SetActive(true)		--未达成
		-- XUI.SetButtonEnabled(self.node_list["btn_gift_receive"], false)
		-- self.node_list.btn_gift_receive_text.text.text = Language.ContinuousRecharge.Fetch
	end

	self.node_list.label_recharge_gold.text.text = string.format(Language.ConsumeDiscount.ConsumeGold,CommonDataManager.GetDaXie(self.data.day_index),self.data.need_consume_gold)
	if not is_not_lingqu then
		-- self.node_list["btn_gift_receive"]:SetActive(false)
		self.node_list["Img_YLQ"]:SetActive(true)
		self.node_list["Img_NLQ"]:SetActive(false)		--未达成
		-- self.node_list.btn_gift_receive_text.text.text = Language.Marry.YiLingQu
	else
		-- self.node_list["btn_gift_receive"]:SetActive(true)
		self.node_list["Img_YLQ"]:SetActive(false)
		-- self.node_list.btn_gift_receive_text.text.text = Language.Marry.LingQu
	end

	for k,v in pairs(self.consume_discount_cell) do
		v:SetData(self.data[k])
	end
end

function ConsumeDiscountRender:OnClickGetReward()
	if nil == self.data.day_index then return end
	local param_t = {
			rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_CONTINUE_CONSUME,
			opera_type = RA_CONTINUE_CONSUME_OPERA_TYPE.RA_CONTINUE_CONSUME_OPEAR_TYPE_FETCH_REWARD,
			param_1 = self.data.day_index
		}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

function ConsumeDiscountRender:CreateSelectEffect()
end