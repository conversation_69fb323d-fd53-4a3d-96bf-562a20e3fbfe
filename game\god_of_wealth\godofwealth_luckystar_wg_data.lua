GodOfWealthWGData = GodOfWealthWGData or BaseClass()

function GodOfWealthWGData:InitLuckyDrawData()
    local cfg = ConfigManager.Instance:GetAutoConfig("lucky_stars_shine_auto")
    self.luckystar_other_cfg = cfg.other[1]
    self.luckystar_reward_pool_cfg = ListToMapList(cfg.reward_pool, "grade")
    self.luckystar_baodi_cfg = ListToMapList(cfg.baodi, "grade")
    self.luckystar_model_show_cfg = ListToMap(cfg.model_show, "grade")
    self.luckystar_grade = 1
    self.lunckstar_draw_time = 0
    self.baodi_flag = {}
    self.record_list = {}
    self.result_item_list = {}
    self.lucky_draw_state = true
end

function GodOfWealthWGData:GetLuckyStarCurGrade()
    return self.luckystar_grade
end

function GodOfWealthWGData:GetLuckyStarCurRewardPoorCfg()
    local rare_data_list = {}
    local normal_data_list = {}
    local reward_poor_cfg = self.luckystar_reward_pool_cfg[self.luckystar_grade] or {}

    if not IsEmptyTable(reward_poor_cfg) then
        for k, v in pairs(reward_poor_cfg) do
            if v.is_rare == 1 then
                table.insert(rare_data_list, v)
            else
                table.insert(normal_data_list, v)
            end
        end
    end

    return rare_data_list, normal_data_list, reward_poor_cfg
end

-- 获取展示奖励
function GodOfWealthWGData:GetLuckyStarRewardPoolShowCfg()
    local show_data_list = {}
    local reward_poor_cfg = self.luckystar_reward_pool_cfg[self.luckystar_grade] or {}

    if not IsEmptyTable(reward_poor_cfg) then
        for k, v in pairs(reward_poor_cfg) do
            if v.is_show == 1 then
                table.insert(show_data_list, v)
            end
        end
    end

    return show_data_list
end

function GodOfWealthWGData:GetIsShowItem(item_id)
    local reward_poor_cfg = self.luckystar_reward_pool_cfg[self.luckystar_grade] or {}

    if not IsEmptyTable(reward_poor_cfg) then
        for k, v in pairs(reward_poor_cfg) do
            if item_id == v.item.item_id and v.is_show == 1 then
                return true
            end
        end
    end

    return false
end

function GodOfWealthWGData:GetLuckyStarCurBaoDiCfg()
    return self.luckystar_baodi_cfg[self.luckystar_grade] or {}
end

function GodOfWealthWGData:GetLuckyStarCurModelShowCfg()
    return self.luckystar_model_show_cfg[self.luckystar_grade] or {}
end

function GodOfWealthWGData:GetLuckyStarGradeRewardInfo()
	local data_list = {}
	local value_difference = {}

    local baidi_cfg = self:GetLuckyStarCurBaoDiCfg()
	for k, v in pairs(baidi_cfg) do
		local data = {}
        data.reward_info = v
        local reward_flag = self:GetBaiDiFlagByIndex(v.index)
        data.reward_flag = reward_flag
        data.can_get = reward_flag == 0 and self:GetLuckyStarDrawTime() >= v.times
		data.is_final_reward = false
		table.insert(data_list, data)

		local last_need_lucky_value = baidi_cfg[k - 1] and baidi_cfg[k - 1].times or 0
		local lucky_value_difference = v.times - last_need_lucky_value
		table.insert(value_difference, {times = lucky_value_difference >= 0 and lucky_value_difference or 0 })
	end

	local slider_value = self:GetLuckyStarGradeRewardProgressValue(data_list, #data_list, value_difference)
	local start_reward_data = data_list[1] or {}
	local final_reward_data = table.remove(data_list) or {}
	return data_list, start_reward_data, final_reward_data, slider_value
end

function GodOfWealthWGData:GetLuckyStarGradeRewardProgressValue(data_list, count, value_difference)
	local slider_value = 0
	local all_slider_item_count = 3 * (count - 1)
	local first_item_pop = 1 
	local dinal_item_pop = 2 
	local per_item_pop = 3
	local per_slider_value =  1 / all_slider_item_count
	local lucky_value = self:GetLuckyStarDrawTime()
	local lucky_value_grade = 1

	if lucky_value <=0 then
		return 0
	elseif lucky_value > data_list[#data_list].reward_info.times then
		return 1
	end

	for k, v in ipairs(data_list) do
		if lucky_value <= v.reward_info.times then
			lucky_value_grade = k
			break
		end
	end

	if lucky_value_grade == 1 then
		slider_value = per_slider_value * (lucky_value / data_list[1].reward_info.times)
	elseif lucky_value_grade == #data_list then
		local last_lucky_need_value = lucky_value - data_list[count - 1].reward_info.times
		local front_value = per_slider_value * (all_slider_item_count - dinal_item_pop)
		local current_grade_diff = value_difference[lucky_value_grade].times
		slider_value = front_value + per_slider_value * dinal_item_pop * last_lucky_need_value / current_grade_diff
	else
		local last_lucky_need_value = lucky_value - data_list[lucky_value_grade - 1].reward_info.times
		local current_grade_diff = value_difference[lucky_value_grade].times
		slider_value = per_slider_value * (1 + per_item_pop * (lucky_value_grade - 2 +  last_lucky_need_value / current_grade_diff))
	end
	
	return slider_value
end

function GodOfWealthWGData:GetLuckyStarDrawTime()
    return self.lunckstar_draw_time
end

function GodOfWealthWGData:GetLuckyStarRemind()
    local other_cfg = GodOfWealthWGData.Instance:GetLuckyStarOtherCfg()
    local has_item_num = ItemWGData.Instance:GetItemNumInBagById(other_cfg.cost_item_id)
    
    if has_item_num > 0 then
        return true
    end

    local baidi_cfg = self:GetLuckyStarCurBaoDiCfg()

    for k, v in pairs(baidi_cfg) do
        if self:GetBaiDiFlagByIndex(v.index) == 0 then
            local draw_times = self:GetLuckyStarDrawTime()
            local need_draw_times = v.times

            if need_draw_times <= draw_times then
                return true
            end
        end
	end

    return false
end

function GodOfWealthWGData:GetLuckyStarShowItemList()
    local data_list = {}

    -- local cfg_list = self:GetLuckyStarCurRewardPoorCfg()
    -- for k, v in pairs(cfg_list) do
    --     table.insert(data_list, v.item)
    -- end

    local baidi_cfg = self:GetLuckyStarCurBaoDiCfg()
	for k, v in pairs(baidi_cfg) do
        if v.flag == 1 then
            table.insert(data_list, v.itemlist)
        end
    end

    return data_list
end

function GodOfWealthWGData:GetLuckyStarOtherCfg()
    return self.luckystar_other_cfg
end

function GodOfWealthWGData:IsLuckyStarItem(change_item_id)
    local other_cfg = GodOfWealthWGData.Instance:GetLuckyStarOtherCfg()
    return change_item_id == other_cfg.cost_item_id
end

function GodOfWealthWGData:SetRoleFuXingGaoZhaoInfo(protocol)
    self.luckystar_grade = protocol.grade
    self.lunckstar_draw_time = protocol.has_draw_times
    self.baodi_flag = bit:d2b_l2h(protocol.baodi_flag, nil, false)
end

function GodOfWealthWGData:GetBaiDiFlagByIndex(index)
    return self.baodi_flag[index] or 0
end

function GodOfWealthWGData:SetRoleFuXingGaoZhaoDrawResult(protocol)
    self.result_item_list = protocol.result_item_list or {}
end

function GodOfWealthWGData:SetRoleFuXingGaoZhaoWorldRecord(protocol)
    self.record_list = protocol.record_list or {}
end

function GodOfWealthWGData:GetRoleFuXingGaoZhaoWorldRecord()
    return self.record_list
end

function GodOfWealthWGData:SetLuckyDrawState(state)
    self.lucky_draw_state = state
end

function GodOfWealthWGData:GetLuckyDrawState()
    return self.lucky_draw_state
end