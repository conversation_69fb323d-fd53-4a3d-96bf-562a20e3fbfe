require("game/chaotic_purchase/chaotic_purchase_wg_data")
require("game/chaotic_purchase/chaotic_purchase_view")

ChaoticPurchaseWGCtrl = ChaoticPurchaseWGCtrl or BaseClass(BaseWGCtrl)

function ChaoticPurchaseWGCtrl:__init()
	if ChaoticPurchaseWGCtrl.Instance then
		ErrorLog("[ChaoticPurchaseWGCtrl] attempt to create singleton twice!")
		return
	end

	ChaoticPurchaseWGCtrl.Instance = self
	self.data = ChaoticPurchaseWGData.New()
    self.view = ChaoticPurchaseView.New(GuideModuleName.ChaoticPurchaseView)
  
    self:RegisterAllProtocols()
end

function ChaoticPurchaseWGCtrl:__delete()
	ChaoticPurchaseWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil
end

function ChaoticPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCChaoticGiftRmbBuyInfo,"OnSCChaoticGiftRmbBuyInfo")
	self:RegisterProtocol(SCChaoticGiftRmbBuyUpdate,"OnSCChaoticGiftRmbBuyUpdate")
end

function ChaoticPurchaseWGCtrl:OnSCChaoticGiftRmbBuyInfo(protocol)
	--print_error("========数据========",protocol)
	self.data:SetAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ChaoticPurchaseView)
	MainuiWGCtrl.Instance:FlushView(0,"chaotic_purchase_over")
end

function ChaoticPurchaseWGCtrl:OnSCChaoticGiftRmbBuyUpdate(protocol)
	--print_error("========单个信息========",protocol)
	self.data:SetSingleInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.ChaoticPurchaseView)
	MainuiWGCtrl.Instance:FlushView(0,"chaotic_purchase_over")
end

function ChaoticPurchaseWGCtrl:ReqChaoticPurchaseInfo(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSChaoticGiftyOperate)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

