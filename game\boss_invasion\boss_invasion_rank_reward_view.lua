BOSSInvasionRankRewardView = BOSSInvasionRankRewardView or BaseClass(SafeBaseView)

function BOSSInvasionRankRewardView:__init()
    self:SetMaskBg(true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(830, 594)})
	self:AddViewResource(0, "uis/view/boss_invasion_ui_prefab", "layout_boss_invasion_rank_reward_view")
end

function BOSSInvasionRankRewardView:LoadCallBack()
    if not self.attributive_reward_list then
        self.attributive_reward_list = AsyncListView.New(BIRankRewardItemCellRender, self.node_list.attributive_reward_list)
    end

    self.node_list.desc_tip.text.text = Language.BOSSInvasion.RankRewardTipStr
    self.node_list.title_view_name.text.text = Language.BOSSInvasion.RankRewardViewName
end

function BOSSInvasionRankRewardView:ReleaseCallBack()
    if self.attributive_reward_list then
        self.attributive_reward_list:DeleteMe()
        self.attributive_reward_list = nil
    end
end

function BOSSInvasionRankRewardView:OnFlush()
    local rank_data_list = BOSSInvasionWGData.Instance:GetBossRankRewardCfg()
    self.attributive_reward_list:SetDataList(rank_data_list)
    self.node_list.desc_my_rank_str.text.text = BOSSInvasionWGData.Instance:GetBossRewardMyRankStr()
end

-------------------------------BIRankRewardItemCellRender-----------------------------------
BIRankRewardItemCellRender = BIRankRewardItemCellRender or BaseClass(BaseRender)

function BIRankRewardItemCellRender:LoadCallBack()
    if not self.reward_list then
        self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
        self.reward_list:SetStartZeroIndex(true)
    end

    if not self.best_reward_list then
        self.best_reward_list = AsyncListView.New(ItemCell, self.node_list.best_reward_list)
        self.best_reward_list:SetStartZeroIndex(true)
    end
end

function BIRankRewardItemCellRender:__delete()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.best_reward_list then
        self.best_reward_list:DeleteMe()
        self.best_reward_list = nil
    end
end

function BIRankRewardItemCellRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local min_rank = self.data.min_rank
    local max_rank = self.data.max_rank
    local rank_str = (min_rank == max_rank) and min_rank or (min_rank .. "-" .. max_rank)
    
    self.node_list.rank_id.text.text = string.format(Language.BOSSInvasion.RankRewardRankStr, rank_str)
    self.reward_list:SetDataList(self.data.reward_show_item)
    self.best_reward_list:SetDataList(self.data.rmb_reward_item)
end