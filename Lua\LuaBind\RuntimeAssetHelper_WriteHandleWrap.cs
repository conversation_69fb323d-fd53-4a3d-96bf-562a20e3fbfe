﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class RuntimeAssetHelper_WriteHandleWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(RuntimeAssetHelper.WriteHandle), null);
		<PERSON><PERSON>Function("Stop", Stop);
		<PERSON><PERSON>Function("New", _CreateRuntimeAssetHelper_WriteHandle);
		L<PERSON>RegFunction("__tostring", ToLua.op_ToString);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateRuntimeAssetHelper_WriteHandle(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Coroutine arg0 = (UnityEngine.Coroutine)ToLua.CheckObject(L, 1, typeof(UnityEngine.Coroutine));
				RuntimeAssetHelper.WriteHandle obj = new RuntimeAssetHelper.WriteHandle(arg0);
				ToLua.PushValue(L, obj);
				return 1;
			}
			else if (count == 0)
			{
				RuntimeAssetHelper.WriteHandle obj = new RuntimeAssetHelper.WriteHandle();
				ToLua.PushValue(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: RuntimeAssetHelper.WriteHandle.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeAssetHelper.WriteHandle obj = (RuntimeAssetHelper.WriteHandle)ToLua.CheckObject(L, 1, typeof(RuntimeAssetHelper.WriteHandle));
			obj.Stop();
			ToLua.SetBack(L, 1, obj);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

