CommonFightStartTips = CommonFightStartTips or BaseClass(SafeBaseView)

function CommonFightStartTips:__init()
	self:SetMaskBg(false, false)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self.view_layer = UiLayer.PopWhite
	self.view_name = "CommonFightStartTips"
	self:AddViewResource(0, "uis/view/tips/fightstarttip_prefab", "layout_common_fight_start_tips")
end

function CommonFightStartTips:LoadCallBack()
	self.node_list.root.animation_player:Play("Play", function()
		self:Close()
	end)
end
