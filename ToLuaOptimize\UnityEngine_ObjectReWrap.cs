﻿using LuaInterface;
using System;
using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class UnityEngine_ObjectReWrap : MonoBehaviour {

    public static void Register(LuaState L)
    {
        L.BeginClass(typeof(UnityEngine.Object), typeof(System.Object));
        <PERSON><PERSON>("name", get_name, set_name);
        L.EndClass();
    }

    [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
    static int set_name(IntPtr L)
    {
        object o = null;

        try
        {
            ToLuaProfile.AddProfileBeginSample("l2c:set name");
            o = ToLua.ToObject(L, 1);
            UnityEngine.Object obj = (UnityEngine.Object)o;
            string arg0 = ToLua.CheckString(L, 2);
            ObjectNameMgr.Instance.SetObjectName(obj, arg0);
            ToLuaProfile.AddProfileEndSample();
            return 0;
        }
        catch (Exception e)
        {
            return LuaDLL.toluaL_exception(L, e, o, "attempt to index name on a nil value");
        }
    }

    [MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
    static int get_name(IntPtr L)
    {
        object o = null;

        try
        {
            ToLuaProfile.AddProfileBeginSample("l2c:get name");
            o = ToLua.ToObject(L, 1);
            UnityEngine.Object obj = (UnityEngine.Object)o;
            string ret = ObjectNameMgr.Instance.GetObjectName(obj);
            LuaDLL.lua_pushstring(L, ret);
            ToLuaProfile.AddProfileEndSample();
            return 1;
        }
        catch (Exception e)
        {
            return LuaDLL.toluaL_exception(L, e, o, "attempt to index name on a nil value");
        }
    }

}
