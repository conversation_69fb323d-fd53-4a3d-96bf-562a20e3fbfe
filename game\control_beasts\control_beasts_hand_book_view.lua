function ControlBeastsWGView:ReleaseBeastHandBookCallBack()
	if self.hand_book_model then
        self.hand_book_model:DeleteMe()
        self.hand_book_model = nil
    end

	if self.hand_book_bag_type_list and #self.hand_book_bag_type_list > 0 then
		for i = 0, 5 do
			if self.hand_book_bag_type_list[i] then
				self.hand_book_bag_type_list[i]:DeleteMe()
				self.hand_book_bag_type_list[i] = nil
			end
		end

		self.hand_book_bag_type_list = nil
	end

	if self.beasts_color_type_list and #self.beasts_color_type_list > 0 then
		for i, beasts_color_type_cell in ipairs(self.beasts_color_type_list) do
			beasts_color_type_cell:DeleteMe()
			beasts_color_type_cell = nil
		end

		self.beasts_color_type_list = nil
	end

	if self.hand_book_attrlist and #self.hand_book_attrlist > 0 then
		for _, attr_cell in ipairs(self.hand_book_attrlist) do
			attr_cell:DeleteMe()
			attr_cell = nil
		end

		self.hand_book_attrlist = nil
	end

	if self.hand_book_flair_attr and #self.hand_book_flair_attr > 0 then
		for _, flair_cell in ipairs(self.hand_book_flair_attr) do
			flair_cell:DeleteMe()
			flair_cell = nil
		end

		self.hand_book_flair_attr = nil
	end

	if self.hand_book_skill_llist and #self.hand_book_skill_llist > 0 then
		for _, skill_cell in ipairs(self.hand_book_skill_llist) do
			skill_cell:DeleteMe()
			skill_cell = nil
		end

		self.hand_book_skill_llist = nil
	end

	self.beasts_star_list = nil
	self.curr_select_type = nil
	self.curr_select_beast_data = nil
	self.curr_select_beast_color = nil
	self.hand_book_model_res_id = nil
	self.show_list = nil
	self.show_book_cell = nil
end

function ControlBeastsWGView:LoadBookViewCallBack()
    -- 模型展示
	if not self.hand_book_model then
		self.hand_book_model = RoleModel.New()
		self.hand_book_model:SetUISceneModel(self.node_list["hand_book_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.hand_book_model, TabIndex.beasts_book)
	end
	
	if not self.hand_book_bag_type_list then
		self.hand_book_bag_type_list = {}
			
		for i = 0, 5 do
			local incubate_obj = self.node_list.hand_book_bag_type_list:FindObj(string.format("compose_bag_type_%d", i))
			if incubate_obj then
				local cell = BeastsHandTypeItemRender.New(incubate_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.SelectBookBagTypeCallBack, self))
				self.hand_book_bag_type_list[i] = cell
			end
		end
	end

	if not self.beasts_color_type_list then
		self.beasts_color_type_list = {}
			
		for i = 1, 8 do
			local incubate_obj = self.node_list.hand_book_bag_grid_count:FindObj(string.format("beasts_color_type_%d", i))
			if incubate_obj then
				local cell = BeastsHandColorTypeRender.New(incubate_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind(self.SelectBookShowBeast, self))
				self.beasts_color_type_list[i] = cell
			end
		end
	end

	if not self.beasts_star_list then
		self.beasts_star_list = {}
		for i = 1, 5 do
			self.beasts_star_list[i] = self.node_list["hand_book_star" .. i]
		end
	end

	-- 资质属性
	if self.hand_book_attrlist == nil then
		self.hand_book_attrlist = {}
		for i = 1, 6 do
			local attr_obj = self.node_list.hand_book_attrlist:FindObj(string.format("attr_%d", i))
			if attr_obj then
				local cell = CommonAddAttrRender.New(attr_obj)
				cell:SetAttrNameNeedSpace(true)
				cell:SetIndex(i)
				self.hand_book_attrlist[i] = cell
			end
		end
	end

	-- 资质属性
	if self.hand_book_flair_attr == nil then
		self.hand_book_flair_attr = {}
		for i = 1, 6 do
			local attr_obj = self.node_list.hand_book_flair_attr:FindObj(string.format("attr_%d", i))
			if attr_obj then
				local cell = BeststBookFlairItemRender.New(attr_obj)
				cell:SetIndex(i)
				self.hand_book_flair_attr[i] = cell
			end
		end
	end

	-- 技能相关
	if self.hand_book_skill_llist == nil then
		self.hand_book_skill_llist = {}
		for i = 1, 3 do
			local attr_obj = self.node_list.hand_book_skill_llist:FindObj(string.format("skill_render_0%d", i))
			if attr_obj then
				local cell = BeastsBookSkillItemRender.New(attr_obj)
				cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind1(self.ShowBeststItemBookSkill, self))
				self.hand_book_skill_llist[i] = cell
			end
		end
	end

	XUI.AddClickEventListener(self.node_list.hand_book_star_btn, BindTool.Bind1(self.HandBookEvolvePreview, self))
	XUI.AddClickEventListener(self.node_list.hand_book_skill_show, BindTool.Bind1(self.HandBookSkill, self))
	XUI.AddClickEventListener(self.node_list.hand_book_go_to_draw, BindTool.Bind(self.OpenBeastsPrizeDraw, self))
	XUI.AddClickEventListener(self.node_list.hand_book_get_reward, BindTool.Bind(self.OpenHandBookGetReward, self))
end

-- 已孵化背包类型点击
function ControlBeastsWGView:SelectBookBagTypeCallBack(beast_bag_type_cell)
	if self.curr_select_type == beast_bag_type_cell.index then
		return
	end

	self.curr_select_type = beast_bag_type_cell.index
	for index = 0, 5 do
		if self.hand_book_bag_type_list[index] then
			self.hand_book_bag_type_list[index]:FlushSelectHl(index == self.curr_select_type)
		end
	end

	self.curr_select_beast_data = nil
	self.curr_select_beast_color = nil
	self:SplitAllBookMessageByType()
	self:FlushBookCenterMessage()
end

-- 已孵化背包类型点击
function ControlBeastsWGView:ShowBeststItemBookSkill(book_skill_cell)
	if book_skill_cell == nil or book_skill_cell.data == nil then
		return
	end

	local data = book_skill_cell.data
	local show_data = {
		icon = data.skill_icon,
		top_text = data.skill_name,
		body_text = data.desc,
		x = 0,
		y = 0,
		set_pos2 = true,
		hide_next = true,
		is_active_skill = (not data.is_normal) and (not data.is_zhuan_shu),
		skill_level = data.skill_level or 0,
		passive_str = data.passive_str,
	}
	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

-- 图鉴点击
function ControlBeastsWGView:SelectBookShowBeast(type_cell, beast_cell)
	if beast_cell.data == nil then
		return
	end

	self.curr_select_beast_data = beast_cell.data
	self.curr_select_beast_color = beast_cell.data.beast_color
	self:ShowCurrBookCell()
end


function ControlBeastsWGView:ShowBookViewCallBack()
end
function ControlBeastsWGView:CloseBookViewCallBack()
end
function ControlBeastsWGView:OpenBookViewCallBack()
end

function ControlBeastsWGView:FlushBookViewCallBack(param_t)
	if self.curr_select_type == nil then
		self.curr_select_type = 0
	end

	for index = 0, 5 do
		if self.hand_book_bag_type_list[index] then
			self.hand_book_bag_type_list[index]:FlushSelectHl(index == self.curr_select_type)
		end
	end

	self:SplitAllBookMessageByType()
	self:FlushBookCenterMessage()
end

-- 拆分总数据
function ControlBeastsWGView:SplitAllBookMessageByType()
	self.show_list = {}

	local aim_table = ControlBeastsWGData.Instance:GetBeastsMapCfg()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for k, beast_data in pairs(aim_table) do
		local beast_map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(beast_data.beast_id)
		if beast_map_data and beast_map_data.beast_preview then
			local min_start_beast = beast_map_data.beast_preview[1]
			local min_star = min_start_beast and min_start_beast.beast_star or 0

			if beast_data.beast_star == min_star and beast_data.beast_color < 8 then
				local temp_data = {}
				temp_data.item_id = beast_data.beast_id
				temp_data.is_beast = true
				temp_data.beast_color = beast_data.beast_color

				if self.curr_select_type == 0 and open_day >= beast_data.show_day then
					if self.show_list[temp_data.beast_color] then
						table.insert(self.show_list[temp_data.beast_color], temp_data)
					else
						self.show_list[temp_data.beast_color] = {}
						table.insert(self.show_list[temp_data.beast_color], temp_data)
					end
				else
					if beast_data.beast_element == self.curr_select_type and open_day >= beast_data.show_day then
						if self.show_list[temp_data.beast_color] then
							table.insert(self.show_list[temp_data.beast_color], temp_data)
						else
							self.show_list[temp_data.beast_color] = {}
							table.insert(self.show_list[temp_data.beast_color], temp_data)
						end
					end
				end
			end
		end
	end

	for k, v in pairs(self.show_list) do
		if not IsEmptyTable(v) then
			table.sort(v, SortTools.KeyLowerSorter("beast_color"))
		end
	end
end

function ControlBeastsWGView:FlushBookCenterMessage()
	if not self.show_list then
		return
	end

	local now_type_count = #self.beasts_color_type_list

	for i, color_type_cell in ipairs(self.beasts_color_type_list) do
		local inverse_index = #self.beasts_color_type_list - i + 1
		color_type_cell:SetVisible(self.show_list[inverse_index] ~= nil and #self.show_list[inverse_index] > 0)

		if self.show_list[inverse_index] ~= nil and #self.show_list[inverse_index] > 0 then
			local defult_data = self.show_list[inverse_index][1]
			color_type_cell:SetNowTypeCount(now_type_count)
			color_type_cell:SetData(self.show_list[inverse_index])

			if self.curr_select_beast_data == nil then
				self.curr_select_beast_data = defult_data
				self.curr_select_beast_color = defult_data.beast_color
			end

			color_type_cell:FlushCellSelect(self.curr_select_beast_color)
		end
	end

	self:ShowCurrBookCell()
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.hand_book_bag_grid_count.rect)
end

-- 展示当前的数据
function ControlBeastsWGView:ShowCurrBookCell()
	self:FlushBookBeastModel()
	self:FlushBooklevelMessage()
	self:FlushBookBeastFlairMessage()
end

-- 刷新模型
function ControlBeastsWGView:FlushBookBeastModel()
	if not self.curr_select_beast_data then
		return
	end

	local beast_item_data = self.curr_select_beast_data
    local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(beast_item_data.item_id)

    if self.hand_book_model_res_id ~= res_id then
        self.hand_book_model_res_id = res_id
        local bundle, asset = ResPath.GetBeastsModel(res_id)

        self.hand_book_model:SetMainAsset(bundle, asset)
        self.hand_book_model:PlayRoleAction(SceneObjAnimator.Rest)
    end
end

-- 刷新中间当前选中目标
function ControlBeastsWGView:FlushBooklevelMessage()
	if not self.curr_select_beast_data then
		return
	end

	local beast_item_data = self.curr_select_beast_data
	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_item_data.item_id)
    if beast_cfg then
        local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
        self.node_list.hand_book_color_img.image:LoadSprite(bundle, asset, function()
            self.node_list.hand_book_color_img.image:SetNativeSize()
        end)

		if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
            bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[beast_cfg.beast_color])
            self.node_list.hand_book_color_img:ChangeAsset(bundle, asset)
        end

		local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
		local no_have_star = "a3_ty_xx_zc0"
		for k,v in pairs(self.beasts_star_list) do
			v:CustomSetActive(star_res_list[k] ~= no_have_star)
			v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
		end

        local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
        self.node_list.hand_book_element_img.image:LoadSprite(bundle, asset, function()
            self.node_list.hand_book_element_img.image:SetNativeSize()
        end)

        -- 刷新技能
        local map_data = ControlBeastsWGData.Instance:GetBeastDatMapaById(beast_item_data.item_id)
        if map_data then
            if map_data.beast_data then
                local skill_id = map_data.beast_data.skill_id
				self.node_list.hand_book_skill_show:CustomSetActive(skill_id ~= 0)

				if skill_id ~= 0 then
					local skill_level = map_data.beast_data.beast_star or 0
					--去技能数据类查
					local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
	
					if client_cfg then
						self.node_list.hand_book_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(client_cfg.icon_resource))
					end

					bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_jnd_%d", beast_cfg.skill_type_id))
					self.node_list.hand_book_skill_di.image:LoadSprite(bundle, asset, function()
						self.node_list.hand_book_skill_di.image:SetNativeSize()
					end)
			
					self.node_list.hand_book_skill_name.text.text = beast_cfg.skill_des
				end
            end
        end

        self.node_list.hand_book_name.text.text = beast_cfg.beast_name
		self.node_list.hand_book_get_reward_remind:CustomSetActive(ControlBeastsWGData.Instance:GetBeastsHandBookRewardRemind())
    end
end

-- 刷新右边资质预览
function ControlBeastsWGView:FlushBookBeastFlairMessage()
	if not self.curr_select_beast_data then
		return
	end

	local beast_item_data = self.curr_select_beast_data
    local attr_list = ControlBeastsWGData.Instance:GetBeastLevelAttrListNew(beast_item_data.item_id, 1)
	self.node_list.hand_book_go_to_draw_remind:CustomSetActive(ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRed())

	-- 属性值
	for i, attr_cell in ipairs(self.hand_book_attrlist) do
		attr_cell:SetVisible(attr_list[i] ~= nil)

		if attr_list[i] ~= nil then
			attr_cell:SetData(attr_list[i])
		end
	end

    local beast_beas_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_item_data.item_id)
    local refine_seq = beast_beas_cfg and beast_beas_cfg.refine_seq or 0

	self.node_list.hand_book_not_have_flair_attr:CustomSetActive(refine_seq == -1)
	self.node_list.hand_book_flair_attr:CustomSetActive(refine_seq ~= -1)

	if refine_seq ~= -1 then
		local list_data = ControlBeastsWGData.Instance:GetBeastRefineCfgListBySeq(refine_seq)

		for index, bag_flair_cell in ipairs(self.hand_book_flair_attr) do
			local data = list_data[index - 1]
	
			if data then
				bag_flair_cell:SetData(data)
			end
		end
	end

	--去技能数据类查
	local now_skill_list = {}

	local function add_skill_func(skill_id, is_normal, skill_level)
		local info = nil

		if skill_id and skill_id ~= 0 then
			info = {}
			local client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id)
			local beast_cfg = SkillWGData.Instance:GetBeastsSkillById(skill_id)
	
			if client_cfg and beast_cfg then
				info.skill_icon = client_cfg.icon_resource
				info.desc = client_cfg.description
				info.skill_name = beast_cfg.skill_name
				info.skill_id = skill_id
				info.is_normal = is_normal 
				info.passive_str = is_normal and Language.Common.PuGong or ""
				info.skill_level = skill_level
			end
		end

		return info
	end

	table.insert(now_skill_list, add_skill_func(beast_beas_cfg.skill_id, false, beast_beas_cfg.skill_level))
	--专属技能
	local be_skill_cfg = ControlBeastsWGData.Instance:GetBeastBeSkillCfgBySeq(beast_beas_cfg.be_skill_id)

	if be_skill_cfg ~= nil then
		local info = {}
		info.skill_id = beast_beas_cfg.be_skill_id
		info.is_zhuan_shu = true
		info.skill_icon = be_skill_cfg.skill_icon
		info.skill_name = be_skill_cfg.skill_name
		info.desc = be_skill_cfg.skill_des
		info.skill_level = beast_beas_cfg.be_skill_level or 0
		info.passive_str = Language.Common.ZhuanShu
		table.insert(now_skill_list, info)
	end

	table.insert(now_skill_list, add_skill_func(beast_beas_cfg.normal_skill_id, true, beast_beas_cfg.normal_skill_level))
	
	for i, skill_cell in ipairs(self.hand_book_skill_llist) do
		local data = now_skill_list[i]
		skill_cell:SetVisible(data ~= nil)

		if data ~= nil then
			skill_cell:SetData(data)
		end
	end
end

--------------------------------------------------------------------------------
-- 刷新右边资质预览
function ControlBeastsWGView:HandBookSkill()
	if not self.curr_select_beast_data then
		return
	end

	ControlBeastsWGData.Instance:ShowBeastSkill(self.curr_select_beast_data.item_id)
end

-- 进化预览
function ControlBeastsWGView:HandBookEvolvePreview()
	if not self.curr_select_beast_data then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip9)
        return
    end

    ControlBeastsWGCtrl.Instance:OpenBeastsEvolvePreview(self.curr_select_beast_data.item_id)
end

-- 展示图鉴奖励
function ControlBeastsWGView:OpenHandBookGetReward()
	if not self.curr_select_beast_data then
		return
	end

	local beast_item_data = self.curr_select_beast_data
	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(beast_item_data.item_id)
    if beast_cfg then
		ControlBeastsWGCtrl.Instance:OpenBeastHandBookRewardView(beast_cfg.beast_type)
	end
end
--------------------------------背包幻兽类型item-----------------------
BeastsHandTypeItemRender = BeastsHandTypeItemRender or BaseClass(BaseRender)
-- 刷新选中状态
function BeastsHandTypeItemRender:FlushSelectHl(is_select)
    self.node_list.select:CustomSetActive(is_select)
end

--------------------------------背包幻兽品质类型-----------------------
BeastsHandColorTypeRender = BeastsHandColorTypeRender or BaseClass(BaseRender)
function BeastsHandColorTypeRender:LoadCallBack()
    -- 资质属性
    if self.book_item_list == nil then
        self.book_item_list = {}
        for i = 1, 10 do
            local attr_obj = self.node_list.grid_root:FindObj(string.format("beasts_item_0%d", i))
            if attr_obj then
                local cell = BeastsBookItemRender.New(attr_obj)
                cell:SetIndex(i)
				cell:SetClickCallBack(BindTool.Bind1(self.OnSelectBeastsItemCB, self))
                self.book_item_list[i] = cell
            end
        end
    end
end

function BeastsHandColorTypeRender:ReleaseCallBack()
    if self.book_item_list and #self.book_item_list > 0 then
		for _, book_item_cell in ipairs(self.book_item_list) do
			book_item_cell:DeleteMe()
			book_item_cell = nil
		end

		self.book_item_list = nil
	end

	self.now_type_count = nil
end

function BeastsHandColorTypeRender:SetNowTypeCount(now_type_count)
	self.now_type_count = now_type_count
end

function BeastsHandColorTypeRender:OnSelectBeastsItemCB(beasts_item)
	if nil == beasts_item or nil == beasts_item.data then
		return
	end

	BaseRender.OnClick(self, beasts_item)
end

function BeastsHandColorTypeRender:OnFlush()
	if not self.data then
		return
	end

	local real_index = self:GetInverseIndex()
	local name = Language.ContralBeasts.HandBookType[real_index]
	self.node_list.beasts_color_type_name.text.text = ToColorStr(name, BEAST_ITEM_COLOR[real_index])
	local raw_str = string.format("a3_ty_lb_pz%d", real_index)
	self.node_list.beasts_color_type_image.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(raw_str))
	
	for i, book_item_cell in ipairs(self.book_item_list) do
		book_item_cell:SetVisible(self.data[i] ~= nil)

		if self.data[i] ~= nil then
			book_item_cell:SetData(self.data[i])
		end
	end

	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.grid_root.rect)
	UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.view.rect)
end

function BeastsHandColorTypeRender:FlushCellSelect(beasts_color)
	local real_index = self:GetInverseIndex()
	if real_index == beasts_color then
		if self.book_item_list and self.book_item_list[1] then
			self.book_item_list[1].view.toggle.isOn = true
		end
	end
end

function BeastsHandColorTypeRender:GetInverseIndex()
	local real_index = self.now_type_count or 1
	return self.now_type_count - self.index + 1
end

--------------------------------背包幻兽物品类型------------------------
BeastsBookItemRender = BeastsBookItemRender or BaseClass(BaseRender)
function BeastsBookItemRender:LoadCallBack()
    if not self.beast_item then
		self.beast_item = ItemCell.New(self.node_list.item_pos)
        self.beast_item:SetIsShowTips(false)
		self.beast_item:SetClickCallBack(BindTool.Bind(self.ItemClick, self))
	end
end

function BeastsBookItemRender:__delete()
	if self.beast_item then
		self.beast_item:DeleteMe()
		self.beast_item = nil
	end
end

function BeastsBookItemRender:ItemClick(is_on)
	if is_on then
		self.view.toggle.isOn = true
		BaseRender.OnClick(self)
	end
end

function BeastsBookItemRender:OnFlush()
	self.beast_item:SetData(self.data)
end

-- 刷新选中状态
function BeastsBookItemRender:OnSelectChange(is_select)
	self.beast_item:SetSelectEffectSp(is_select)
end

----------------------------------幻兽预览资质item-----------------------
BeststBookFlairItemRender = BeststBookFlairItemRender or BaseClass(BaseRender)
function BeststBookFlairItemRender:OnFlush()
    if not self.data then
        return
    end

    -- 获取资质加成属性数据
	local attr_name = EquipmentWGData.Instance:GetAttrName(self.data.attr_id, true)
    self.node_list.attr_name.text.text = attr_name
    local slider_value = 1
    local value_max_str = self.data.max_attr_value
	local value_min_str = self.data.min_attr_value
    self.node_list.attr_value.text.text = value_max_str--string.format("%d-%d", value_min_str, value_max_str)
end


----------------------------------幻兽预览资质item-----------------------
BeastsBookSkillItemRender = BeastsBookSkillItemRender or BaseClass(BaseRender)
function BeastsBookSkillItemRender:OnFlush()
    if not self.data then
        return
    end

	self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(self.data.skill_icon))
	self.node_list.skill_name.text.text = self.data.skill_name

	local str = "a3_bb_icon3pu"
	if self.data.is_zhuan_shu then
		str = "a3_bb_icon2bei"
	elseif not self.data.is_normal then
		str = "a3_bb_icon1jue"
	end

	local bundle, asset = ResPath.GetControlBeastsImg(str)
	self.node_list.skill_type_icon.image:LoadSprite(bundle, asset)
end