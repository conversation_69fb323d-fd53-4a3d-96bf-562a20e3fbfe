PlaySkillFloatWordView = PlaySkillFloatWordView or BaseClass(SafeBaseView)

local TWEEN_MODE = {
    RIGHT = 0,
    LEFT = 1,
}

local TWEEN_STATUS = {
    START = 0,
    MOVE = 1,
    KEEP = 2,
}

function PlaySkillFloatWordView:__init()
    self:SetMaskBg(false, false)
    self:SetMaskBgAlpha(0)

    self.float_tween_status = nil
    if self.view_name == GuideModuleName.PlaySkillFloatWordView then
        self.out_tween_key = GuideModuleName.PlaySkillFloatWordView
        self.view_layer = UiLayer.SkillFloatWord
        self.view_cache_time = 0
    else
        self.out_tween_key = "SkillShowFloatWordView"
        self.self_control_rendring = true
        self.view_layer = UiLayer.PopWhite
    end
    
    self:ClearViewTween()
    self:AddViewResource(0, "uis/view/play_skill_float_word_prefab", "layout_skill_float_word")
end

function PlaySkillFloatWordView:__delete()
end

function PlaySkillFloatWordView:OpenCallBack()

end

function PlaySkillFloatWordView:CloseCallBack()
    self.float_tween_status = nil
end

function PlaySkillFloatWordView:LoadCallBack()

end

function PlaySkillFloatWordView:ReleaseCallBack()
    self.float_word_skill_info_list = nil
    self:StopCountDown()
end

function PlaySkillFloatWordView:SetDataAndOpen(info)
    if self.float_word_skill_info_list == nil then
		self.float_word_skill_info_list = {}
	end

	table.insert(self.float_word_skill_info_list, info)
    if not self:IsOpen() then
        self:Open()
    else
        if self.float_tween_status == TWEEN_STATUS.KEEP then
            self:StopCountDown()
            self:Flush()
        end
    end
end

function PlaySkillFloatWordView:OnFlush()
    if IsEmptyTable(self.float_word_skill_info_list) then
		self:Close()
		return
	end

    self:SetPanelInfo()
end

function PlaySkillFloatWordView:SetPanelInfo()
    local info = table.remove(self.float_word_skill_info_list, 1)
    local tween_mode = info.tween_mode or 0
    local float_time = info.float_time or 1

    local bundle, asset
    if info.float_word_type then
        bundle, asset = info.bundle, info.asset
    else
        bundle, asset = ResPath.GetF2RawImagesPNG("a3_float_word_" .. info.skill_id)
    end
    
    self.node_list.tween_skill_bg.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list.tween_skill_bg.raw_image:SetNativeSize()
    end)

	self:StopCountDown()
    UITween.CleanAllMoveToShowPanel(self.out_tween_key)
    self.float_tween_status = TWEEN_STATUS.START
    if tween_mode == TWEEN_MODE.RIGHT then
        RectTransform.SetAnchorAllign(self.node_list.tween_skill_bg.rect, AnchorPresets.MiddleLeft)
        RectTransform.SetPivotAllign(self.node_list.tween_skill_bg.rect, PivotPresets.MiddleLeft)
        self.node_list["tween_skill_bg"].rect.anchoredPosition = Vector2(-900, 100)
    elseif tween_mode == TWEEN_MODE.LEFT then
        RectTransform.SetAnchorAllign(self.node_list.tween_skill_bg.rect, AnchorPresets.MiddleRight)
        RectTransform.SetPivotAllign(self.node_list.tween_skill_bg.rect, PivotPresets.MiddleRight)
        self.node_list["tween_skill_bg"].rect.anchoredPosition = Vector2(900, 100)
    end

    self:PlaySkillFloatWordTween(tween_mode, float_time)
end

function PlaySkillFloatWordView:PlaySkillFloatWordTween(tween_mode, float_time)
    self.float_tween_status = TWEEN_STATUS.MOVE
    if tween_mode == TWEEN_MODE.RIGHT then
        UITween.MoveToShowPanel(self.out_tween_key, self.node_list["tween_skill_bg"], Vector2(-900, -168), Vector2(200, -168), 0.5,
            DG.Tweening.Ease.OutExpo,
            function ()
                self:StartCountDown(float_time)
            end)
    elseif tween_mode == TWEEN_MODE.LEFT then
        UITween.MoveToShowPanel(self.out_tween_key, self.node_list["tween_skill_bg"], Vector2(900, 100), Vector2(-40, 100), 0.5,
            DG.Tweening.Ease.OutExpo,
            function ()
                self:StartCountDown(float_time)
            end)
    end
end

function PlaySkillFloatWordView:StopCountDown()
	if self.static_count_down then
		CountDown.Instance:RemoveCountDown(self.static_count_down)
		self.static_count_down = nil
	end
end

function PlaySkillFloatWordView:StartCountDown(float_time)
    self.float_tween_status = TWEEN_STATUS.KEEP
	local function time_func(elapse_time, total_time)
	end

    local function end_call_back()
        self:Flush()
	end

    local time = IsEmptyTable(self.float_word_skill_info_list) and float_time or 0
	self.static_count_down = CountDown.Instance:AddCountDown(time, 1, time_func, end_call_back)
end

