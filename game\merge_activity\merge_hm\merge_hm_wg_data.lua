MergeHMWGData = MergeHMWGData or BaseClass()

function MergeHMWGData:__init()
	if MergeHMWGData.Instance then
		ErrorLog("[MergeHMWGData] Attemp to create a singleton twice !")
	end
	MergeHMWGData.Instance = self

	self.hmwd_cfg = ConfigManager.Instance:GetAutoConfig("combineserve_activity_expbuy_cfg_auto")
	self.exp_cfg = ListToMap(self.hmwd_cfg.exp_cfg, "cross_world_level")
    self.param_cfg = ListToMap(self.hmwd_cfg.config_param, "grade")
    RemindManager.Instance:Register(RemindName.Merge_HMWD, BindTool.Bind(self.ShowRed, self))
    MergeActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.MERGE_ACT_HONGMENG_WUDAO, {[1] = MERGE_EVENT_TYPE.LEVEL},
    	BindTool.Bind(self.GetHMIsOpen, self), BindTool.Bind(self.ShowRed, self))

    self.is_skip_effect = false
end

function MergeHMWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.Merge_HMWD)
    MergeHMWGData.Instance = nil
end

function MergeHMWGData:ShowRed()
    if not self:GetHMIsOpen() then
        return 0
    end

    if self.red_flag == nil then
        return 1
    end

    return 0
end

function MergeHMWGData:SetSkipEffectStatus(is_skip)
    is_skip = is_skip or false
    self.is_skip_effect = is_skip
end

function MergeHMWGData:GetSkipEffectStatus()
    return self.is_skip_effect or false
end

function MergeHMWGData:SetRedFlag()
    self.red_flag = true
end

function MergeHMWGData:GetHMIsOpen()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.MERGE_ACT_HONGMENG_WUDAO)
    if not is_open then
        return false
    end

    local cur_cfg = self:GetHMCurCfg()
    if not cur_cfg then
        return false
    end

    return RoleWGData.Instance:GetRoleLevel() >= cur_cfg.open_role_level
end

function MergeHMWGData:GetHMCurCfg()
	--local open_day = MergeActivityWGData.Instance:GetMergeDayInfo()
	--for i, v in pairs(self.hmwd_cfg.config_param) do
	--	if open_day >= v.start_server_day and open_day <= v.end_server_day then
    --        return v
	--	end
	--end

    local cur_stage = self.cur_stage or 0
    return self.param_cfg[cur_stage]
end

function MergeHMWGData:SetExpBuyInfo(protocol)
    self.cur_stage = protocol.cur_stage
	self.buy_count = protocol.buy_count
    self.cross_world_level = protocol.cross_world_level
    self.end_timestamp = protocol.end_timestamp
end

function MergeHMWGData:GetBuyCount()
	return self.buy_count or 0
end

function MergeHMWGData:GetMidWorldLevel()
    return self.cross_world_level or 0
end

function MergeHMWGData:GetEndTime()
    return self.end_timestamp or 0
end

function MergeHMWGData:GetCanBuyTimes()
    local role_level = RoleWGData.Instance:GetRoleLevel()
    if role_level > (self.cross_world_level or 0) then
        return 0
    end

    local cur_cfg = self:GetHMCurCfg()
    return cur_cfg and cur_cfg.buy_limit_count or 0
end

function MergeHMWGData:GetShowBuyTimesLimit()
    local cur_cfg = self:GetHMCurCfg()
    return cur_cfg and cur_cfg.buy_limit_count or 0
end

function MergeHMWGData:GetBuyLimit()
    local cur_cfg = self:GetHMCurCfg()
    return cur_cfg and cur_cfg.buy_limit_count or 0
end

function MergeHMWGData:GetGanWuPrice()
	local cur_cfg = self:GetHMCurCfg()
    if cur_cfg == nil then
        return 0
    end

    local buy_count = (self.buy_count or 0) + 1
    local cfg = self.hmwd_cfg.price_cfg[buy_count] or {}
    return cfg.price or 0
end

function MergeHMWGData:GetGanWuExP()
    local data = {}
    if self.cross_world_level then
        local buy_count = (self.buy_count or 0) + 1
        local cfg = self.hmwd_cfg.price_cfg[buy_count] or {}
        local exp_per = cfg.exp_per and cfg.exp_per / 10000 or 1
        local cur_cfg = MergeHMWGData.Instance:GetHMCurCfg()
        if not cur_cfg then
            return data
	    end
        local level = self.cross_world_level - cur_cfg.correction_value
        local exp_cfg = RoleWGData.GetRoleExpCfgByLv(level)
        local exp = exp_cfg and exp_cfg.exp or 0
        data.exp = exp * exp_per
    end
    return data
end

