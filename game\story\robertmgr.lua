RobertMgr = RobertMgr or BaseClass()
require("game/story/auto_task_robert")
-- 自动跑任务机器人管理系统
function RobertMgr:__init()
	if RobertMgr.Instance ~= nil then
		ErrorLog("[RobertMgr] attempt to create singleton twice!")
		return
	end

	RobertMgr.Instance = self

	self.is_open_robert_system = true					-- 是否开启机器人系统
	self.max_history_pos_num = 10 						-- 主角行走位置的历史最大记录数量（机器人将从这些历史位置随机产生）
	self.record_role_pos_quency = 1 					-- 主角行走的记录频率

	self.check_del_robert_quency = 3 					-- 检查移除机器人的频率（机器人到目标点或离开角色自己太远将移除）
	self.check_roundrobert_born_quency = 2 				-- 检查周围机器人出生频率
	self.truly_role_count = 0 							-- 视野内真实玩家数量

	self.mount_speed = 1500								-- 机器人坐骑速度
	self.role_speed = 900								-- 人的速度

	self.obj_id_inc = 100000

	self.now_time = 0
	self.last_record_role_pos_time = 0
	self.last_check_born_time = 0
	self.last_check_del_robert_time = 0

	self.last_check_round_time = 0
	self.last_born_round_time = 0
	self.last_task_round_time = 0

	self.hangout_robert_count = 0 						-- 闲逛机器人数量
	self.ignore_distance_count = 0						-- 忽略距离限制的机器人数量

	self.role_history_pos_list = {}
	self.robert_t_list = {}

	self.change_scene = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind1(self.OnStartLoadScene, self))
	self.loding_comple = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnLoadingComplete, self))
	self.task_robert_cfg = ConfigManager.Instance:GetAutoConfig("task_robert_auto")
end

function RobertMgr:__delete()
	GlobalEventSystem:UnBind(self.change_scene)
	GlobalEventSystem:UnBind(self.loding_comple)
	Runner.Instance:RemoveRunObj(self)
	self:ClearAllRober()

	RobertMgr.Instance = nil
end

function RobertMgr:OnStartLoadScene()
	self.last_record_role_pos_time = 0
	self.last_check_born_time = 0
	self.last_check_del_robert_time = 0
	self.last_check_round_time = 0
	self.last_task_round_time = 0
	self.last_born_round_time = 0
	self.role_history_pos_list = {}

	self:ClearAllRober()
end

function RobertMgr:OnLoadingComplete()
	if self:CheckSceneNeedRobert() and self:CheckMainRoleLevel() then
		self.ignore_distance_count = 8
		Runner.Instance:AddRunObj(self, 8)
	else
		Runner.Instance:RemoveRunObj(self)
	end
end

function RobertMgr:Update(now_time, elapse_time)
	self.now_time = now_time

	if not self.is_open_robert_system then
		return
	end

	-- 记录主角位置
	if now_time >= self.last_record_role_pos_time then
		self.last_record_role_pos_time = now_time + self.record_role_pos_quency
		self:RecordRolePos()
	end

	-- 检查创建机器人
	if now_time >= self.last_task_round_time then
		self.last_task_round_time = now_time + self.check_roundrobert_born_quency
		if self:CheckNeedCreateTaskRobert() then
			if self:CheckNeedCreateHangOutRobert() then
				self:CheckBornHangOutRobert()
			else
				self:CheckBornTaskRobert()
			end
		end
	end

	-- 检查删除
	if 0 < #self.robert_t_list and now_time >= self.last_check_del_robert_time then
		self.last_check_del_robert_time = now_time + self.check_del_robert_quency
		self:CheckRemoveRobert()
	end

	for k,v in pairs(self.robert_t_list) do
		if v.obj then
			v.obj:Update(now_time, elapse_time)
		end
	end
end

function RobertMgr:RecordRolePos()
	local main_role = Scene.Instance:GetMainRole()
	-- 跳跃时不记录
	if main_role:IsJump() then
		return
	end

	local role_x, role_y = main_role:GetLogicPos()
	role_x = tonumber(role_x)
	role_y = tonumber(role_y)

	if AStarFindWay:IsBlock(role_x, role_y) then
		return
	end

	if #self.role_history_pos_list <= 1 then
		-- 出生时随机几个坐标
		if main_role.vo and main_role.vo.level and main_role.vo.level == 1 then
			local config = Scene.Instance:GetSceneConfig()
			for i = 1, 100 do
				if #self.role_history_pos_list >= 10 then
					break
				end

				local x = config.scenex + math.random(-6, 6)
				local y = config.sceney + math.random(-6, 6)
				if not AStarFindWay:IsBlock(x, y) then
					local t = {}
					t.logic_x = x
					t.logic_y = y
					t.scene_id = Scene.Instance:GetSceneId()
					table.insert(self.role_history_pos_list, t)
				end
			end
		end
	end

	local end_t = self.role_history_pos_list[#self.role_history_pos_list]
	if nil ~= end_t then
		local distance = GameMath.GetDistance(role_x, role_y, end_t.logic_x,  end_t.logic_y, false)
		if distance < 10 * 10 then
			return
		end
	end

	if role_x < 0 or role_y < 0 then
		return
	end

	if #self.role_history_pos_list >= self.max_history_pos_num then
		table.remove(self.role_history_pos_list, 1)
	end

	local t = {}
	t.logic_x = role_x
	t.logic_y = role_y
	t.scene_id = Scene.Instance:GetSceneId()
	table.insert(self.role_history_pos_list, t)
end

-- 创建机器人
function RobertMgr:BornRobert(born_x, born_y)
	if (born_x and born_x <= 0) or (born_y and born_y <= 0) then
		return nil
	end

	self.obj_id_inc = self.obj_id_inc + 1
	local role_vo = self:CreateRobertVO(born_x, born_y)
	local robert = AutoTaskRobert.New(role_vo)
	local t = {}
	t.scene_id = Scene.Instance:GetSceneId()
	t.obj_id = role_vo.obj_id
	t.obj = robert
	table.insert(self.robert_t_list, t)

	robert:Init(Scene.Instance)
	return robert
end

function RobertMgr:CreateRobertVO(born_x, born_y)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local role_vo = GameVoManager.Instance:CreateVo(RoleVo)
	role_vo.is_auto_task_robot = true
	role_vo.role_id = 99999
	role_vo.obj_id = self.obj_id_inc
	role_vo.origin_server_id = main_role_vo.current_server_id
	role_vo.merge_server_id = main_role_vo.merge_server_id
	role_vo.plat_type = main_role_vo.current_plat_type
	role_vo.merge_plat_type = main_role_vo.merge_plat_type
	role_vo.plat_name = main_role_vo.cur_plat_name

	role_vo.pos_x = born_x
	role_vo.pos_y = born_y
	role_vo.hp = main_role_vo.max_hp
	role_vo.max_hp = main_role_vo.max_hp
	role_vo.level = main_role_vo.level + math.random(-5, 5)

	local sex = 0
	local prof = 1

	-- 【职业改动修改】
	local function random_fun()
		sex = math.random(0, 1)
		prof = math.random(GameEnum.ROLE_PROF_1, GameEnum.ROLE_PROF_4)
		if RoleWGData.Instance:GetIsShieldSexAndProf(sex, prof) then
			random_fun()
		end
	end

	random_fun()
	role_vo.sex = sex
	role_vo.prof = prof
	role_vo.name = self:GetRandomName(role_vo.sex)

	-- 获取随机外观
	role_vo.appearance = ProtocolStruct.RoleAppearance()
	local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(sex, prof)
    if not IsEmptyTable(default_diy_data) then
        role_vo.role_diy_appearance = default_diy_data
    end

	local appeid_t = self:GetRobertRandomAppeidByLevel()

	local mount_appeid = tonumber(appeid_t.mount_appeid) or 0
	if mount_appeid > 0 then
		role_vo.mount_appeid = mount_appeid
		role_vo.move_speed = self.mount_speed
		role_vo.base_move_speed = self.mount_speed
	else
		role_vo.mount_appeid = 0
		role_vo.move_speed = self.role_speed
		role_vo.base_move_speed = self.role_speed
	end

	role_vo.appearance.qilinbi = -1
	role_vo.appearance.fashion_body = tonumber(appeid_t.fashion_body) or 0
	role_vo.appearance.fashion_wuqi = tonumber(appeid_t.fashion_wuqi) or 0
	role_vo.wing_appeid = tonumber(appeid_t.wing_appeid) or 0
	role_vo.lingchong_appeid = tonumber(appeid_t.lingchong_appeid) or 0
	role_vo.fabao_appeid = tonumber(appeid_t.fabao_appeid) or 0
	role_vo.appearance.fashion_guanghuan = tonumber(appeid_t.halo_appeid) or 0

	role_vo.vip_level = tonumber(appeid_t.vip_level)
	role_vo.jingjie_level = tonumber(appeid_t.jingjie_level)
	role_vo.guard_id = tonumber(appeid_t.guard_id)
	role_vo.used_title_list = {tonumber(appeid_t.title_id)}
	role_vo.stage_level = tonumber(appeid_t.stage_level)

	return role_vo
end

-- 根据权重，从普通外观和vip外观里面随机一套
function RobertMgr:GetRandomAppearanceCfg()
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local weight = 10
	for i = 1, #self.task_robert_cfg.random_appearance do
		local appearance_cfg = self.task_robert_cfg.random_appearance[i]
		if level <= appearance_cfg.level then
			weight = appearance_cfg.weight
			break
		end
	end

	local vip_weight = 10
	for i = 1, #self.task_robert_cfg.vip_random_appearance do
		local appearance_cfg = self.task_robert_cfg.vip_random_appearance[i]
		if level <= appearance_cfg.level then
			vip_weight = appearance_cfg.weight
			break
		end
	end

	if vip_weight <= 0 then
		return self.task_robert_cfg.random_appearance
	elseif weight <= 0 then
		return self.task_robert_cfg.vip_random_appearance
	end

	if math.random(0, weight + vip_weight) <= weight then
		return self.task_robert_cfg.random_appearance
	else
		return self.task_robert_cfg.vip_random_appearance
	end
end

function RobertMgr:GetRobertRandomAppeidByLevel()
	local random_appearance_cfg = self:GetRandomAppearanceCfg()
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local appeid_t = {}
	local cfg = {}

	for i = 1, #random_appearance_cfg do
		local appearance_cfg = random_appearance_cfg[i]
		if level <= appearance_cfg.level then
			cfg = appearance_cfg
			break
		end
	end

	for k,v in pairs(cfg) do
		appeid_t[k] = 0
		if v and v ~= "0" and v ~= 0 then
			local appeid_list = Split(v, ',', true)
			if k ~= "vip_level" then
				appeid_t[k] = self:RandomSelectAppid(appeid_list)
			else
				appeid_t[k] = appeid_list[math.random(1, #appeid_list)]
			end
		end
	end

	return appeid_t
end

---让策划暂定了五个算出来误差不大，5个以上或是奇数，计算有误差
function RobertMgr:RandomSelectAppid(appeid_list)
	local num = #appeid_list
	local center = math.ceil(num / 2)
	local moiety_num = 100 / num		--计算出平均值
	local divide = math.floor(moiety_num / 3) 	--计算出等分， 2+1等分为最高等分概率

	local divide_list = {}
	local count_num = 0
	for i = 1, num do
		local temp_num = 0
		if i < center then
			temp_num = moiety_num + ((center - i) * divide) 
		elseif i == center then
			temp_num = moiety_num
		else
			temp_num = moiety_num - ((i - center) * divide)
		end
		table.insert(divide_list, count_num)
		count_num = count_num + temp_num
	end

	local random_num = math.random(1, 100)
	local aim_index = 1		--默认为1 
	for index, value in ipairs(divide_list) do
		if random_num > value then
			aim_index = index
		end
	end

	if aim_index > num then
		aim_index = num
	end

	return appeid_list[aim_index]
end


function RobertMgr:GetRolePos()
	local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()

	return tonumber(role_x), tonumber(role_y)
end

-- 计算跟踪机器人出生位置
function RobertMgr:CalcFollowRobertBornPos(min_distance)
	min_distance = (min_distance or 10) * 10
	local pos_num = #self.role_history_pos_list
	if pos_num < 1 then
		return 0, 0
	end

	local role_x, role_y = self:GetRolePos()

	local loop = 0
	local max_loop = 250

	while loop < max_loop do
		loop = loop + 1

		local rand_index = math.random(1, #self.role_history_pos_list)
		local t = self.role_history_pos_list[rand_index]
		local distance = GameMath.GetDistance(role_x, role_y, t.logic_x, t.logic_y, false)
		if t.scene_id == Scene.Instance:GetSceneId() and distance >= min_distance then
			return t.logic_x, t.logic_y
		end
	end

	return 0, 0
end

-- 检查是否需要创建任务机器人
function RobertMgr:CheckNeedCreateTaskRobert()
	self:UpdateRoleCount()
	local flag = true
	-- 检查等级区间
	flag = self:CheckMainRoleLevel()
	-- 检查视野内真实玩家数量
	flag = flag and self:CheckRoleInViewCount()
	-- 检查机器人数量
	flag = flag and self:CheckRobertCount()

	return flag
end

-- 检查是否需要闲逛机器人
function RobertMgr:CheckNeedCreateHangOutRobert()
	local scene_id = Scene.Instance:GetSceneId()
	local flag = scene_id == 1003 or scene_id == 1008
	flag = flag and self.hangout_robert_count <= 2

	return flag
end

function RobertMgr:UpdateRoleCount()
	self.truly_role_count = 0
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		self.truly_role_count = self.truly_role_count + 1
	end
end

-- 检查等级区间
function RobertMgr:CheckMainRoleLevel()
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local level_limit = self.task_robert_cfg.level_limit[1]

	if level >= level_limit.min_level and level <= level_limit.max_level then
		return true
	end

	return false
end

-- 检查视野内真实玩家数量
function RobertMgr:CheckRoleInViewCount()
	return self.truly_role_count <= 16
end

-- 检查机器人数量
function RobertMgr:CheckRobertCount()
	return #self.robert_t_list + self.truly_role_count < 6
end

-- 检查当前场景是否需要创建机器人
function RobertMgr:CheckSceneNeedRobert()
	local scene_id = Scene.Instance:GetSceneId()
	return scene_id == 1001 or scene_id == 1002 or scene_id == 1003 or scene_id == 1008 or false
end

-- 创建跟踪角色任务的机器人
function RobertMgr:CheckBornTaskRobert()
	if #self.role_history_pos_list < 1 then
		return
	end

	local next_task_cfg = TaskWGData.Instance:GetNextZhuTaskConfig()
	if nil == next_task_cfg then
		return
	end

	-- 场景加载结束之后，无视距离在玩家附近创建机器人
	local min_distance = 10 * 10
	if self.ignore_distance_count > 0 then
		min_distance = 0
	end

	local born_x, born_y = self:CalcFollowRobertBornPos(min_distance)
	local flag = math.random() > 0.67
	local task_id = next_task_cfg.task_id

	-- 机器人任务进度领先玩家
	if flag then
		local npc = nil
		for i = 1, 2 do
			if nil ~= next_task_cfg.accept_npc.id then
				npc = next_task_cfg.accept_npc
			elseif nil ~= next_task_cfg.commit_npc.id then
				npc = next_task_cfg.commit_npc
			end

			if npc then
				-- npc周围随机一个出生点
				born_x = npc.x + math.random(-3, 3)
				born_y = npc.y + math.random(-3, 3)
				born_x, born_y = AStarFindWay:GetAroundVaildXY(born_x, born_y, 3)
			end

			next_task_cfg = TaskWGData.Instance:GetNextZhuTaskConfigById(next_task_cfg.task_id)
			if nil == next_task_cfg then
				break
			end

			task_id = next_task_cfg.task_id
		end
	else
		if nil ~= next_task_cfg.pretaskid and "" ~= next_task_cfg.pretaskid then
			task_id = next_task_cfg.pretaskid
		end
	end

	local robert = self:BornRobert(born_x, born_y)
	if robert then
		self.ignore_distance_count = self.ignore_distance_count - 1
		robert:SetAI(AutoTaskRobertAI.Task)
		robert:SetTaskID(task_id)
		robert:Start(flag)
	end
end

-- 创建闲逛的机器人
function RobertMgr:CheckBornHangOutRobert()
	if #self.role_history_pos_list < 1 then
		return
	end

	local born_x, born_y = self:CalcFollowRobertBornPos()
	local robert = self:BornRobert(born_x, born_y)
	if robert then
		robert:SetAI(AutoTaskRobertAI.HangOut)
		robert:Start()
		self.hangout_robert_count = self.hangout_robert_count + 1
	end
end

-- 检查机器人是否需要移除,以可以创建新的机器人
function RobertMgr:CheckRemoveRobert()
	local role_x, role_y = Scene.Instance:GetMainRole():GetLogicPos()
	role_x = tonumber(role_x)
	role_y = tonumber(role_y)

	local num = #self.robert_t_list
	for i = num, 1, -1 do
		local t = self.robert_t_list[i]
		local robert_obj = t.obj

		if nil ~= robert_obj then
			local is_del = robert_obj:CheckDestory()
			if is_del then
				if nil ~= t.obj then
					t.obj:DeleteMe()
					t.obj = nil
				end
				table.remove(self.robert_t_list, i)
			end
		end
	end
end

function RobertMgr:ClearAllRober()
	for k,v in pairs(self.robert_t_list) do
		if nil ~= v.obj then
			v.obj:ReleaseSoulBoyObj()
			v.obj:ReleaseGuardObj()
			v.obj:DeleteMe()
			v.obj = nil
		end
	end

	self.robert_t_list = {}
	self.hangout_robert_count = 0
end

function RobertMgr:GetRandomName(sex)
	local front = ""
	local middle = ""
	local back = ""
	local name_cfg = self:GetNameCfg()

	local name_first_list = {}	-- 前缀
	local name_last_list = {}	-- 后缀
	if sex == GameEnum.FEMALE then
		name_first_list = name_cfg.female_first
		name_last_list = name_cfg.female_last
	else
		name_first_list = name_cfg.male_first
		name_last_list = name_cfg.male_last
	end
	local rand_num = math.floor(math.random(1, 200))
	local name_first_index = (rand_num % #name_first_list) + 1
	local name_last_index = (rand_num % #name_last_list) + 1
	local first_name = name_first_list[name_first_index] or ""
	local last_name = name_last_list[name_last_index] or ""
	return first_name .. last_name
end

function RobertMgr:GetNameCfg()
	if self.name_cfg == nil then
		self.name_cfg = ConfigManager.Instance:GetAutoConfig("randname_auto").random_name[1]
	end

	return self.name_cfg
end

function RobertMgr:GetMaxCreateOfflineRoberCount()
	local level = GameVoManager.Instance:GetMainRoleVo().level
	local level_limit = self.task_robert_cfg.level_limit[1]
	if level > level_limit.max_level then
		return 20
	end

	return 10
end

function RobertMgr:Debug()
	for k,v in pairs(self.robert_t_list) do
		v.obj:Debug()
	end
end