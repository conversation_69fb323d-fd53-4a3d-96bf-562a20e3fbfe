SiXiangActivateView = SiXiangActivateView or BaseClass(SafeBaseView)

function SiXiangActivateView:__init(view_name)
    self.view_name = "SiXiangActivateView"
    self:SetMaskBg(true, true)

    self:AddViewResource(0, "uis/view/sixiang_call_prefab", "sixiang_activate_view")
end

function SiXiangActivateView:LoadCallBack()
    self.unlock_progress = 0
    self:InitPanel()
end

function SiXiangActivateView:ReleaseCallBack()
    if self.shouchong_reward_list then
        self.shouchong_reward_list:DeleteMe()
        self.shouchong_reward_list = nil
    end

    if self.role_data_change then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
        self.role_data_change = nil
    end
end

function SiXiangActivateView:OnFlush(param_t)
    self:RefreshView()
end

function SiXiangActivateView:InitPanel()
    self.shouchong_reward_list = AsyncListView.New(SiXiangActivateRender, self.node_list.item_list)

    local model_res = 4122001 -- SiXiangCallWGData.Instance:GetSiXiangOtherCfg("yugao_model")
    self.display_model = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["model_root"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = false,
    }
    
    self.display_model:SetRenderTexUI3DModel(display_data)
    -- self.display_model:SetUI3DModel(self.node_list.model_root.transform, nil, 0, false, MODEL_CAMERA_TYPE.BASE)
    self:AddUiRoleModel(self.display_model)
    self.display_model:SetMainAsset(ResPath.GetNpcModel(model_res))

    self.role_data_change = BindTool.Bind1(self.RoleLevelChange, self)
    RoleWGData.Instance:NotifyAttrChange(self.role_data_change, { "level" })

    XUI.AddClickEventListener(self.node_list.lock_img, BindTool.Bind1(self.OnClickUnLock, self))
end

function SiXiangActivateView:RefreshView()
    local data_list = SiXiangCallWGData.Instance:GetNoticeDataList()
    if not data_list then
        return
    end
    self.shouchong_reward_list:SetDataList(data_list)

    local reward_flag = 0
    local unlock_progress = 0
    for i = 1, #data_list do
        reward_flag = SiXiangCallWGData.Instance:GetSiXiangYuGaoRewardFlag(data_list[i].reward_index)
        if reward_flag == DAOHUN_YUGAO_REWARD_TYPE.HAD_FETCH then
            unlock_progress = unlock_progress + data_list[i].progress
        end
    end
    self.unlock_progress = unlock_progress

    local is_unlock = true
    if unlock_progress < 100 then
        self.node_list.lock_label.text.text = unlock_progress .. '%'
    elseif is_unlock then
        self.node_list.lock_label.text.text = Language.Role.JieSuo
    end

    self.node_list.lock_img:SetActive(not is_unlock)
end

function SiXiangActivateView:RoleLevelChange()
    if self.shouchong_reward_list then
        self.shouchong_reward_list:RefreshActiveCellViews()
    end
end

function SiXiangActivateView:OnClickUnLock()
    if self.unlock_progress < 100 then
        return
    end
    FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.YUGAO_UNLOCK)
end

---------------------------------------------------------------

SiXiangActivateRender = SiXiangActivateRender or BaseClass(BaseRender)

function SiXiangActivateRender:__init()

end

function SiXiangActivateRender:__delete()
    self.reward_list:DeleteMe()
    self.reward_list = nil
end

function SiXiangActivateRender:LoadCallBack()
    self.is_init = false
    self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    XUI.AddClickEventListener(self.node_list.btn_fetch, BindTool.Bind1(self.OnClickFetch, self))
end

function SiXiangActivateRender:OnFlush()
    self:InitRewardList()
    self:FlushNowLevel()
    self:FlushFetchBtn()
end

function SiXiangActivateRender:InitRewardList(data)
    if self.is_init then
        return
    end
    self.is_init = true
    local data = self:GetData()
    local reward_list = OperationActivityWGData.Instance:SortDataByItemColor(data.fakeitem)
    self.reward_list:SetDataList(reward_list)

    local is_dianfeng, need_level = RoleWGData.Instance:GetDianFengLevel(data.level_limit)
    self.node_list.need_dianfeng_icon:SetActive(is_dianfeng)
    self.node_list.need_level_label.text.text = need_level
end

function SiXiangActivateRender:FlushNowLevel()
    local data = self:GetData()
    local role_level = RoleWGData.Instance:GetAttr("level")
    local is_dianfeng, now_level = RoleWGData.Instance:GetDianFengLevel(role_level)
    local lbl_color = role_level >= data.level_limit and COLOR3B.GREEN or COLOR3B.RED
    self.node_list.now_dianfeng_icon:SetActive(is_dianfeng)
    self.node_list.now_level_label.text.text = ToColorStr(now_level .. '/', lbl_color)
end

function SiXiangActivateRender:FlushFetchBtn()
    local data = self:GetData()
    local flag = SiXiangCallWGData.Instance:GetSiXiangYuGaoRewardFlag(data.reward_index)
    if flag == DAOHUN_YUGAO_REWARD_TYPE.CAN_FETCH then
        self.node_list.btn_fetch_text.text.text = Language.Common.LingQu
        XUI.SetButtonEnabled(self.node_list.btn_fetch, true)
    elseif flag == DAOHUN_YUGAO_REWARD_TYPE.INVALID then
        local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
        if data.open_server_day_limit > open_day then
            self.node_list.btn_fetch_text.text.text = string.format(Language.SiXiangCall.LastDayCanGet,
                data.open_server_day_limit - open_day)
        else
            self.node_list.btn_fetch_text.text.text = Language.Common.LingQu
        end
        XUI.SetButtonEnabled(self.node_list.btn_fetch, false)
    end

    self.node_list.btn_fetch:SetActive(flag ~= DAOHUN_YUGAO_REWARD_TYPE.HAD_FETCH)
    self.node_list.btn_yiling:SetActive(flag == DAOHUN_YUGAO_REWARD_TYPE.HAD_FETCH)
end

function SiXiangActivateRender:OnClickFetch()
    local data = self:GetData()
    if data then
        local flag = SiXiangCallWGData.Instance:GetSiXiangYuGaoRewardFlag(data.reward_index)
        if flag == DAOHUN_YUGAO_REWARD_TYPE.CAN_FETCH then
            FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.YUGAO_FETCH, data.reward_index)
        end
    end
end
