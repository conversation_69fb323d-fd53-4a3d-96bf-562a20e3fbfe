function MengLingView:InitMengLingComposeView()
	if nil == self.hecheng_select_item_grid then
		self.select_compose_data = {}

		local bundle, asset = "uis/view/mengling_ui_prefab", "ml_eq_hc_item"
		self.hecheng_select_item_grid = AsyncBaseGrid.New()
		self.hecheng_select_item_grid:CreateCells({col = 3, change_cells_num = 1, itemRender = MengLingHechengPartRender,
			list_view = self.node_list["ph_hecheng_select_item_list_EQ"], assetBundle = bundle, assetName = asset,})
		self.hecheng_select_item_grid:SetSelectCallBack(BindTool.Bind(self.OnClickSelectEquipment, self))
		self.hecheng_select_item_grid:SetStartZeroIndex(false)
		self.hecheng_select_item_grid:SetDataList({}, 0)
	end

	if not self.cur_equip_item_list then
		self.cur_equip_item_list = AsyncListView.New(ItemCell, self.node_list.cur_equip_item_list)
	end

	if nil == self.accordion_list_equip then
		self.accordion_list_equip = {}
		self.cell_list = {}

		local mengling_big_type_cfg = MengLingWGData.Instance:GetComposeBigItemListCfg()

		for i = 1, 20 do
			local data = mengling_big_type_cfg[i]
			local has_data = not IsEmptyTable(data)

			if has_data then
				self.node_list["SelectBtn" .. i]:CustomSetActive(true)
				self.node_list["List"..i]:CustomSetActive(true)
				local big_type_data = data[0]
				self.node_list["text_btn" .. i].text.text = big_type_data.name
				self.node_list["text_btn" .. i .. "_hl"].text.text = big_type_data.name

				self.accordion_list_equip[i] = {}
				-- self.accordion_list_equip[i].text_name = self.node_list["text_btn" .. i]
				self.accordion_list_equip[i].list = self.node_list["List"..i]
				self:LoadHeChengListCell(i, #mengling_big_type_cfg)
				self.node_list["SelectBtn" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickBigTypeHandler, self, big_type_data.big_type))
			else
				self.node_list["SelectBtn" .. i]:CustomSetActive(false)
				self.node_list["List"..i]:CustomSetActive(false)
			end
		end
	end

	if nil == self.hecheng_target_item then
		self.hecheng_target_item = ItemCell.New(self.node_list["ph_target_item"])
	end

	if nil == self.hecheng_must_item_list then
		self.hecheng_must_item_list = {}

		for i = 1, 2 do
			self.hecheng_must_item_list[i] = ItemCell.New(self.node_list["ph_must_item_" .. i])
			self.hecheng_must_item_list[i]:SetCellBgEnabled(false)
			self.hecheng_must_item_list[i]:SetNeedItemGetWay(true)
		end
	end

	if nil == self.hecheng_equipment_item then
		self.hecheng_equipment_item = {}

		for i = 1, 5 do
			self.hecheng_equipment_item[i] = ItemCell.New(self.node_list["ph_equipment_item_" .. i])
			-- self.hecheng_equipment_item[i]:SetItemTipFrom(ItemTip.FROM_EQUIMENT_HECHENG)
			self.hecheng_equipment_item[i]:SetIsShowTips(false)
			self.hecheng_equipment_item[i]:SetClickCallBack(BindTool.Bind(self.OnClickSelectEquipmentItem, self))
		end
	end

	self.node_list["btn_hecheng_add_onekey_EQ"].button:AddClickListener(BindTool.Bind1(self.OnEquipComposeOperaAddOnekey, self))
	self.node_list["btn_hecheng_hecheng_EQ"].button:AddClickListener(BindTool.Bind1(self.OnEquipComposeOperaReq, self))

	self.need_stuff_num = 5
end

function MengLingView:ReleaseMengLingComposeView()
	if self.hecheng_select_item_grid then
		self.hecheng_select_item_grid:DeleteMe()
		self.hecheng_select_item_grid = nil
	end

	self.accordion_list_equip = nil

	if self.cell_list then
		for k, v in pairs(self.cell_list) do
			for i, u in pairs(v) do
				u:DeleteMe()
			end
		end

		self.cell_list = nil
	end

	if self.hecheng_target_item then
		self.hecheng_target_item:DeleteMe()
		self.hecheng_target_item = nil
	end

	if self.hecheng_must_item_list then
		for k, v in pairs(self.hecheng_must_item_list) do
			v:DeleteMe()
		end

		self.hecheng_must_item_list = nil
	end
	
	if self.hecheng_equipment_item then
		for k, v in pairs(self.hecheng_equipment_item) do
			v:DeleteMe()
		end

		self.hecheng_equipment_item = nil
	end

	if self.cur_equip_item_list then
		self.cur_equip_item_list:DeleteMe()
		self.cur_equip_item_list = nil
	end

	self.load_equip_cell_complete = nil
	self.select_compose_data = nil
	self.need_stuff_num = nil
end

function MengLingView:LoadHeChengListCell(index, length)
	local small_type_list = MengLingWGData.Instance:GetComposeBigItemTypeListCfg(index)
	local res_async_loader = AllocResAsyncLoader(self, "mengling_hecheng_item_equip" .. index)
	res_async_loader:Load("uis/view/mengling_ui_prefab", "ml_hecheng_item", nil,
		function(new_obj)
			local tab_index = self:GetShowIndex()
			local item_vo = {}

			for i=1, #small_type_list do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.accordion_list_equip[index].list.transform, false)
				obj:GetComponent("Toggle").group = self.accordion_list_equip[index].list.toggle_group
				local item_render = MengLingComposeSmallTypeRender.New(obj)
				item_render:SetValueChangedCallBack(BindTool.Bind(self.OnClickSmallTypeHandle, self))
				item_render:SetData(small_type_list[i])
				item_vo[i] = item_render

				if index == length and i == #small_type_list then
					self.load_equip_cell_complete = true
				end
				-- obj:SetActive(false)
			end

			self.cell_list[index] = item_vo
			if self.load_equip_cell_complete then
				self:OnFlushMengLingComposeView()
			end
		end)
end

function MengLingView:OnFlushMengLingComposeView()
	if not self.load_equip_cell_complete then
		return
	end

	local mengling_big_type_cfg = MengLingWGData.Instance:GetComposeBigItemListCfg()
	for k, v in pairs(mengling_big_type_cfg) do
		local big_type_remind = false

		for i, u in pairs(v) do
			if i >= 1 then
				local small_remind = MengLingWGData.Instance:GetMengLingComposeSmallTypeRemind(k, i)
				self.cell_list[k][i]:SetRemind(small_remind)

				if small_remind then
					big_type_remind = true
				end
			end
		end

		self.node_list["eq_remind" .. k]:SetActive(big_type_remind)
	end

	-- 选中大标题
	self:SelectToggle()
end

function MengLingView:SelectToggle()
	local select_bigtype = self:GetComposeSuitDefaultSelect()
	if self.node_list["SelectBtn" .. select_bigtype].accordion_element.isOn then
		self:OnClickBigTypeHandler(select_bigtype, true)
	else
		self.node_list["SelectBtn" .. select_bigtype].accordion_element.isOn = true
	end
end

function MengLingView:GetComposeSuitDefaultSelect()
	if self.big_type then
		if MengLingWGData.Instance:GetMengLingComposeBigTypeRemind(self.big_type) then 
			return self.big_type
		end
	end

	local mengling_big_type_cfg = MengLingWGData.Instance:GetComposeBigItemListCfg()
	local default_select_big_type= self.big_type or 1
	for k, v in pairs(mengling_big_type_cfg) do
		if MengLingWGData.Instance:GetMengLingComposeBigTypeRemind(k) then 
			default_select_big_type = k
			break
		end
	end

	return default_select_big_type
end

-- 选中大标题套装
function MengLingView:OnClickBigTypeHandler(index, isOn)
	local small_type = self:GetComposeSuitSmallTypeDefaultSelect()

	-- or (self.big_type == index and self.small_type == small_type)
	if nil == index or not isOn  then
		return
	end

	if self.big_type == index and self.small_type == small_type then
		self:FlushMidEquipList()
		return 
	end

	self.big_type = index

	if self.cell_list ~= nil and self.cell_list[index] ~= nil then
		if self.cell_list[index][small_type].view.toggle.isOn then
			self:OnClickSmallTypeHandle(self.cell_list[index][small_type])
		else
			self.cell_list[index][small_type].view.toggle.isOn = true
		end
	end
end

function MengLingView:GetComposeSuitSmallTypeDefaultSelect()
	local default_select_small_type = 1

	if self.small_type then
		default_select_small_type = self.small_type
		local small_remind = MengLingWGData.Instance:GetMengLingComposeSmallTypeRemind(self.big_type, default_select_small_type)

		if small_remind then
			return default_select_small_type
		end
	end

	local mengling_small_type_cfg = MengLingWGData.Instance:GetComposeBigItemTypeListCfg(self.big_type)

	if not IsEmptyTable(mengling_small_type_cfg) then
		for k, v in pairs(mengling_small_type_cfg) do
			local small_remind = MengLingWGData.Instance:GetMengLingComposeSmallTypeRemind(self.big_type, k)

			if small_remind then
				default_select_small_type = k
				break
			end
		end
	end

	return default_select_small_type
end

 -- 点击小标题
function MengLingView:OnClickSmallTypeHandle(item)

	if nil == item or nil == item.data then
		return 
	end

	local data = item.data
	self.small_type = data.small_type

	self:ChangeMidvisible(true)
end

function MengLingView:ChangeMidvisible(show_equip_list)
	self.node_list["ph_hecheng_list_bg"]:CustomSetActive(show_equip_list)
	self.node_list["ph_hecheng_select_item_list_EQ"]:CustomSetActive(show_equip_list)
	self.node_list["layout_hecheng_show_frame_EQ"]:CustomSetActive(not show_equip_list)
	self.node_list["hecheng_cur_equip"]:CustomSetActive(show_equip_list)

	if show_equip_list then
		self:FlushMidEquipList()
	else
		self:FlushMidComposeInfo()
	end
end

function MengLingView:FlushMidEquipList()
	if nil == self.big_type or nil == self.small_type then
		return
	end

	local equip_data_list = MengLingWGData.Instance:GetEquipComposeDataList(self.big_type, self.small_type)
	self.hecheng_select_item_grid:SetDataList(equip_data_list)

	local need_grade = -1

	for k, v in pairs(equip_data_list) do
		if need_grade < 0 and v.need_grade then
			need_grade = v.need_grade
			break
		end
	end

	local wear_equip_data_list = MengLingWGData.Instance:GetTotalWearSuitItemList(need_grade)
	local has_data = not IsEmptyTable(wear_equip_data_list)

	self.node_list.cur_no_wear_equip:CustomSetActive(not has_data)
	self.node_list.cur_equip_item_list:CustomSetActive(has_data)

	if has_data then
		self.cur_equip_item_list:SetDataList(wear_equip_data_list)
	end
end

function MengLingView:FlushMidComposeInfo()
	if IsEmptyTable(self.select_compose_data) then
		self.hecheng_target_item:ClearAllParts()
		self.node_list.hecheng_show_target_name.text.text = ""
		return
	end

	local item_id = self.select_compose_data.item_id

	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	self.node_list.hecheng_show_target_name.text.text = item_cfg and item_cfg.name or ""
	self.hecheng_target_item:SetData({item_id = item_id})

	self:FlushSeniorItemInfo()
	self:ReSetHechengEquipmentItemData()
end

-- 刷新必备特殊材料
function MengLingView:FlushSeniorItemInfo()
	local senior_item_id = self.select_compose_data.senior_item_id
	local senior_item_num = self.select_compose_data.senior_item_num
	local show_senior = senior_item_num > 0 and senior_item_id > 0
	self.node_list.ph_must_item_suo_1:CustomSetActive(not show_senior)

	if show_senior then
		local had_num = ItemWGData.Instance:GetItemNumInBagById(senior_item_id)
		local is_enough = had_num >= senior_item_num
		local str = ToColorStr(had_num .. "/" .. senior_item_num, is_enough and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
		self.hecheng_must_item_list[1]:SetData({item_id = senior_item_id})
		self.hecheng_must_item_list[1]:SetRightBottomColorText(str)
		self.hecheng_must_item_list[1]:SetRightBottomTextVisible(true)
	else
		self.hecheng_must_item_list[1]:ClearAllParts()
	end
end

function MengLingView:ReSetHechengEquipmentItemData()
	for k = 1, 5 do
		local v = self.hecheng_equipment_item[k]
		v:ClearData()
		v:SetItemIcon(ResPath.GetCommonImages("a2_ty_jia"))
		v:SetCellBg(ResPath.GetCommonImages("a3_ty_wpk_0"))
		v:SetButtonComp(true)
	end

	self:FlushEquipHeCheng()
end

--点击 装备列表的 装备项
function MengLingView:OnClickSelectEquipment(cell)
	if cell == nil then
		return
	end

	local data = cell:GetData()
	if data == nil then
		return
	end

	self.select_compose_data = data


	self:ChangeMidvisible(false)
end

--点击装备材料格(是否空格子)
function MengLingView:OnClickSelectEquipmentItem(item)
	local data = item:GetData()

	if IsEmptyTable(data) then
		local show_get_way = function ()
			local get_way = MengLingWGData.Instance:GetMengLingOtherCfg("get_way")
			TipWGCtrl.Instance:OpenEquipGetWayView(get_way)
		end

		local target_data_list = {}
		local stuff_list = MengLingWGData.Instance:GetMengLingEquipComposeStuffList(self.select_compose_data.need_grade, self.select_compose_data.need_color)

		if IsEmptyTable(stuff_list) then
			show_get_way()
			return
		end
		
		-- 过滤已经用上的
		local wear_data = {}
		for k = 1, 5 do
			local v = self.hecheng_equipment_item[k]
			local data = v:GetData()
	
			if not IsEmptyTable(data) and data.item_id > 0 then
				table.insert(wear_data, {item_id = data.item_id, index = data.index})
			end
		end
	
		local target_data_list = {}
		if IsEmptyTable(wear_data) then
			target_data_list = stuff_list 
		else
			for k, v in pairs(stuff_list) do
				local can_add = true
	
				for i, u in pairs(wear_data) do
					if v.item_id == u.item_id and v.index == u.index then
						wear_data[i] = nil
						can_add = false
						break
					end
				end
	
				if can_add then
					table.insert(target_data_list, v)
				end
			end
		end

		if IsEmptyTable(target_data_list) then
			show_get_way()
			return
		end

		self.curr_select_item = item

		MengLingWGCtrl.Instance:OpenMengLingStuffListView(target_data_list, item, BindTool.Bind(self.BagClickCallBack, self))
	else
		item:ClearData()
		item:SetItemIcon(ResPath.GetCommonImages("a2_ty_jia"))
		item:SetButtonComp(true)
		local bundle, asset = ResPath.GetCommonImages("a3_ty_wpk_0")
		item:SetCellBg(bundle, asset)
		self:FlushEquipHeCheng()
	end
end

function MengLingView:BagClickCallBack(data)
	if not self.curr_select_item then
		return
	end

	self.curr_select_item:SetData(data)
	self:FlushEquipHeCheng()
end

-- 刷新合成状态信息
function MengLingView:FlushEquipHeCheng()
	local need_num = self.select_compose_data.min_consume_num

	if self.select_compose_data.probability < 100 then
		need_num = need_num + math.ceil((100 - self.select_compose_data.probability) / self.select_compose_data.add_probability)
	end

	for k = 1, 5 do
		local v = self.hecheng_equipment_item[k]

		if k > need_num then
			v:ClearData()
			v.button.enabled = false
			v:SetItemIcon(ResPath.GetCommonImages("a3_ty_suo"))
			v:SetButtonComp(false)
		end
	end

	self.need_stuff_num = need_num

	local probability = 0
	local can_compose = false
	local stuff_str = ""
	local snior_str = ""
	-- local stuff_enough = true
	

	-- 特殊装备是否齐全
	local senior_item_id = self.select_compose_data.senior_item_id
	local senior_item_num = self.select_compose_data.senior_item_num
	local min_consume_num = self.select_compose_data.min_consume_num
	local show_senior = senior_item_num > 0 and senior_item_id > 0

	local get_probability = function ()
		local stuff_num = 0
		for i = 1, 5 do
			local data = self.hecheng_equipment_item[i]:GetData()

			if not IsEmptyTable(data) then
				stuff_num = stuff_num + 1
			end
		end

		if stuff_num >= min_consume_num then
			can_compose = true
			probability = self.select_compose_data.probability + (stuff_num - min_consume_num) * self.select_compose_data.add_probability
		end
	end

	-- if show_senior then
	-- 	local had_num = ItemWGData.Instance:GetItemNumInBagById(senior_item_id)
	-- 	local item_name = ItemWGData.Instance:GetItemName(senior_item_id)

	-- 	snior_str = item_name .. "*" .. senior_item_num

	-- 	if had_num >= senior_item_num then
	-- 		get_probability()
	-- 	-- else
	-- 	-- 	stuff_enough = false
	-- 	end
	-- else
	-- 	get_probability()
	-- end

	get_probability()

	-- 材料提醒
	local need_grade = self.select_compose_data.need_grade
	local suit_cfg = MengLingWGData.Instance:GetGradeCfgBySeq(need_grade)

	-- stuff_str = suit_cfg.name .. Language.Common.ColorName[ self.select_compose_data.need_color] .. Language.F2Tip.DisplayType[34] .. "*" .. self.select_compose_data.min_consume_num
	stuff_str = string.format(Language.MengLing.MengLingComposeStuffDesc, (self.select_compose_data.need_grade + 1), Language.Common.ColorName4[self.select_compose_data.need_color], self.select_compose_data.min_consume_num)
	if snior_str ~= "" then
		stuff_str = stuff_str .. " " ..snior_str
	end

	self.node_list["lbl_materials_tip"].text.text = string.format(Language.MengLing.MengLingComposeNeedStuff, stuff_str)

	XUI.SetButtonEnabled(self.node_list.btn_hecheng_hecheng_EQ, can_compose)

	probability = probability > 100 and 100 or probability
	local color = probability <= 0 and COLOR3B.PINK or COLOR3B.GREEN
	self.node_list["desc_compose_probability"].text.text = Language.MengLing.ComposeSuccess .. ToColorStr(probability .. "%", color)

	local stuff_list = MengLingWGData.Instance:GetMengLingEquipComposeStuffList(self.select_compose_data.need_grade, self.select_compose_data.need_color)

	-- if stuff_enough then
	-- 	local stuff_list = MengLingWGData.Instance:GetMengLingEquipComposeStuffList(self.select_compose_data.need_grade, self.select_compose_data.need_color)
	-- 	if #stuff_list < min_consume_num then
	-- 		stuff_enough = false
	-- 	end
	-- end

	--材料足够的情况下显示一键放入
	self.node_list.add_btn_text.text.text = (#stuff_list > 0) and Language.MengLing.ComposeBtnText[1] or Language.MengLing.ComposeBtnText[2]
end

-- 获得装备 or 一键放入
function MengLingView:OnEquipComposeOperaAddOnekey()
	if IsEmptyTable(self.select_compose_data) then
		return
	end

	-- 特殊装备是否齐全
	local senior_item_id = self.select_compose_data.senior_item_id
	local senior_item_num = self.select_compose_data.senior_item_num
	local show_senior = senior_item_num > 0 and senior_item_id > 0

	local show_get_way = function()
		local get_way = MengLingWGData.Instance:GetMengLingOtherCfg("get_way")
		TipWGCtrl.Instance:OpenEquipGetWayView(get_way)
	end

	local one_key_wear = function()
		local need_equip = false

		-- 过滤已经用上的
		local wear_data = {}
		for k = 1, 5 do
			local v = self.hecheng_equipment_item[k]
			local data = v:GetData()
	
			if not IsEmptyTable(data) and data.item_id > 0 then
				table.insert(wear_data, {item_id = data.item_id, index = data.index})
			else
				need_equip = true
			end
		end

		if not need_equip then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.MengLing.IsAddTargetEquip)
			return
		end

		local stuff_list = MengLingWGData.Instance:GetMengLingEquipComposeStuffList(self.select_compose_data.need_grade, self.select_compose_data.need_color)
	
		local target_data_list = {}
		if IsEmptyTable(wear_data) then
			target_data_list = stuff_list 
		else
			for k, v in pairs(stuff_list) do
				local can_add = true
	
				for i, u in pairs(wear_data) do
					if v.item_id == u.item_id and v.index == u.index then
						wear_data[i] = nil
						can_add = false
						break
					end
				end
	
				if can_add then
					table.insert(target_data_list, v)
				end
			end
		end

		if IsEmptyTable(target_data_list) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.MengLing.IsAddTargetEquip)
		else
			local index = 1
			for k = 1, 5 do
				local v = self.hecheng_equipment_item[k]
				local data = v:GetData()
				local target_data = target_data_list[index]
		
				if IsEmptyTable(data) and not IsEmptyTable(target_data) then
					v:SetData(target_data)
					index = index + 1
				end
			end

			self:FlushEquipHeCheng()
		end
	end

	local check_stuff_enough = function()
		local stuff_list = MengLingWGData.Instance:GetMengLingEquipComposeStuffList(self.select_compose_data.need_grade, self.select_compose_data.need_color)

		if IsEmptyTable(stuff_list) then
			show_get_way()
		else
			-- local min_consume_num = self.select_compose_data.min_consume_num

			-- if #stuff_list < min_consume_num then
			-- 	show_get_way()
			-- else
				one_key_wear()
			-- end
		end
	end

	check_stuff_enough()

	-- if show_senior then
	-- 	local had_num = ItemWGData.Instance:GetItemNumInBagById(senior_item_id)
	-- 	if had_num < senior_item_num then
	-- 		TipWGCtrl.Instance:OpenItem({item_id = senior_item_id})
	-- 		return
	-- 	else
	-- 		check_stuff_enough()
	-- 	end
	-- else
	-- 	check_stuff_enough()
	-- end
end

function MengLingView:OnEquipComposeOperaReq()
	if IsEmptyTable(self.select_compose_data) then
		return
	end

	local compose_item_id = self.select_compose_data.item_id
	local senior_item_id = self.select_compose_data.senior_item_id
	local senior_item_num = self.select_compose_data.senior_item_num
	local min_consume_num = self.select_compose_data.min_consume_num
	local base_probability = self.select_compose_data.probability
	local add_probability = self.select_compose_data.add_probability
	local show_senior = senior_item_num > 0 and senior_item_id > 0

	local show_get_way = function()
		local get_way = MengLingWGData.Instance:GetMengLingOtherCfg("get_way")
		TipWGCtrl.Instance:OpenEquipGetWayView(get_way)
	end

	local check_stuff = function()
		local probability = 0

		local data_list = {} -- 材料
		local index_list = {}
		local id_index = 1
		local stuff_num = 0

		for i = 1, 5 do
			local v = self.hecheng_equipment_item[i]
			local data = v:GetData()

			if not IsEmptyTable(data) then
				stuff_num = stuff_num + 1
				local index = data.index
				if index_list[index] then
					local id = index_list[index]
					data_list[id].count = data_list[id].count + 1
				else
					index_list[index] = id_index
					data_list[id_index] = {item_id = data.item_id, bag_index = index, count = 1}
					id_index = id_index + 1
				end
			end
		end

		if stuff_num < min_consume_num then
			show_get_way()
			return
		end

		probability = base_probability + (stuff_num - min_consume_num) * add_probability

		local do_compose = function()
			MengLingWGCtrl.Instance:OnCSDreamSpiritEquipCompos(compose_item_id, #data_list, data_list)
			self:ReSetHechengEquipmentItemData()
		end

		if probability >= 100 then
			do_compose()
		else

		local desc = string.format(Language.MengLing.ComposeProbabilityText, probability)
		TipWGCtrl.Instance:OpenAlertTips(desc, do_compose)
		end 
	end

	if show_senior then
		local had_num = ItemWGData.Instance:GetItemNumInBagById(senior_item_id)
		if had_num < senior_item_num then
			TipWGCtrl.Instance:OpenItem({item_id = senior_item_id})
			return
		else
			check_stuff()
		end
	else
		check_stuff()
	end
end

-------------------------------MengLingComposeSmallTypeRender---------------------------
--导航小类型
MengLingComposeSmallTypeRender = MengLingComposeSmallTypeRender or BaseClass(BaseRender)

function MengLingComposeSmallTypeRender:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function MengLingComposeSmallTypeRender:__delete()
	self.value_change_call_back = nil
end

function MengLingComposeSmallTypeRender:OnFlush()
	if nil == self.data then
		return
	end

	self.node_list.text_name.text.text = self.data.name
	self.node_list.text_name_hl.text.text = self.data.name
end

function MengLingComposeSmallTypeRender:SetValueChangedCallBack(call_back)
	self.value_change_call_back = call_back
end

function MengLingComposeSmallTypeRender:OnClickItem(is_on)
	if is_on then
		self.value_change_call_back(self)
	end	
end

function MengLingComposeSmallTypeRender:SetRemind(remind)
	if self.node_list.remind then
		self.node_list.remind:CustomSetActive(remind)
	end
end

function MengLingComposeSmallTypeRender:OnSelectChange(is_select)
	self.node_list.img_bg_hl:SetActive(is_select)
	self.node_list.img_bg:SetActive(not is_select)
	self.view.toggle.isOn = is_select
end

-------------------------MengLingHechengPartRender---------------------------
-- 梦灵合成装备导航
MengLingHechengPartRender = MengLingHechengPartRender or BaseClass(BaseRender)

function MengLingHechengPartRender:__delete()
	if self.item_tip then
		self.item_tip:DeleteMe()
		self.item_tip = nil
	end
end
function MengLingHechengPartRender:LoadCallBack()
	self.item_tip = ItemCell.New(self.node_list["ph_hecheng_item_pos"])
	self.item_tip:SetIsShowTips(false)
	self.item_tip:SetTipClickCallBack(BindTool.Bind(self.clickcell, self))
	self.node_list["EquipHeChengItem"].button:AddClickListener(BindTool.Bind(self.clickcell, self))
end

function MengLingHechengPartRender:clickcell()
	BaseRender.OnClick(self)
end

function MengLingHechengPartRender:OnFlush()
	if not self.data then
		return
	end

	local item_id = self.data.item_id
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	local name = item_cfg and item_cfg.name or ""
	local stuff_list = MengLingWGData.Instance:GetMengLingEquipComposeStuffList(self.data.need_grade, self.data.need_color)
	local has_num = #stuff_list
	self.item_tip:SetData({item_id = item_id})
	self.item_tip:SetButtonComp(false)
	self.item_tip:SetCellBgEnabled(false)
	self.node_list.lbl_hecheng_item_name.text.text = name
	self.node_list.lbl_can_use_num.text.text = string.format(Language.MengLing.MengLingComposeHasStuffNum, has_num)

	local remind = MengLingWGData.Instance:GetMengLingEquipComposeRemind(self.data)
	self.node_list.remind:CustomSetActive(remind)
end