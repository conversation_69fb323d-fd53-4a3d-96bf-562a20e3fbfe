
-- 市场-拍卖-拍卖纪录
local AUCTION_LOG_C_NUM = 7--小页签数量
function MarketView:AuctionRecordLoadCallBack()
	self.aiction_log_item_list = AsyncListView.New(MarketAuctionRecordRender, self.node_list.pm_log_item_list)
	self.aiction_log_tab_list = {}

	for i = 1, AUCTION_LOG_C_NUM do
		self.aiction_log_tab_list[i] = {}
		self.aiction_log_tab_list[i] = self.node_list.market_auction_log_group:FindObj("tab_log_type_" .. i)
		self.aiction_log_tab_list[i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickAuctionRecordLeftChild, self, i, false))
	end

	self.aiction_log_tab_list[1].toggle.isOn = true

	--tab页签的箭头显示.
	self.node_list.market_auction_log_viewport.event_trigger_listener:AddBeginDragListener(BindTool.Bind(self.AuctionLogOnScrollBeginDrag, self))
	self.node_list.market_auction_log_viewport.event_trigger_listener:AddDragListener(BindTool.Bind(self.AuctionLogOnScrollDrag, self))
	self.node_list.market_auction_log_viewport.event_trigger_listener:AddEndDragListener(BindTool.Bind(self.AuctionLogOnScrollEndDrag, self))
end

function MarketView:AuctionRecordReleaseCallBack()
	if self.aiction_log_item_list then
		self.aiction_log_item_list:DeleteMe()
		self.aiction_log_item_list = nil
	end

	self.auction_record_btn_index = nil

	self.aiction_log_tab_list = nil
end

function MarketView:AuctionRecordShowIndexCallBack()

end

function MarketView:AuctionRecordOnFlush(param_t)
	self:OnClickAuctionRecordLeftChild(self.auction_record_btn_index or 1, true)
end

----------------tab页签的箭头显示.
function MarketView:AuctionLogOnScrollBeginDrag(event_data)
	self.node_list.market_auction_log_list.scroll_rect:OnBeginDrag(event_data)
end

function MarketView:AuctionLogOnScrollDrag(event_data)
	self.node_list.market_auction_log_list.scroll_rect:OnDrag(event_data)
end

function MarketView:AuctionLogOnScrollEndDrag(event_data)
	self.node_list.market_auction_log_list.scroll_rect:OnEndDrag(event_data)

	local val = self.node_list.market_auction_log_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.market_auction_log_l_img:SetActive(val ~= 0 and val > 0.1)
	self.node_list.market_auction_log_r_img:SetActive(val ~= 0 and val < 0.9)
end
-------------------------------------

function MarketView:FlushAuctionRecordAllPart()

end

function MarketView:OnClickAuctionRecordLeftChild(index, force_flush)
	if self.auction_record_btn_index == index and not force_flush then
		return
	end

	self.auction_record_btn_index = index
	local data_list = MarketWGData.Instance:GetAuctionLogInfoByType(index)
	self.node_list.pm_log_empty_tips:SetActive(false)
	self.node_list.pm_log_item_list:SetActive(true)
	if data_list and not IsEmptyTable(data_list) then
		self.aiction_log_item_list:SetDataList(data_list)
		if not force_flush then
			self.aiction_log_item_list:JumpToTop()
		end
	else
		self.node_list.pm_log_empty_tips:SetActive(true)
		self.node_list.pm_log_item_list:SetActive(false)
	end
end


---------------MarketAuctionRecordRender-----start--------------------------------------------
MarketAuctionRecordRender = MarketAuctionRecordRender or BaseClass(BaseRender)
function MarketAuctionRecordRender:LoadCallBack()
	self.item_goods = ItemCell.New(self.node_list.item_pos)
end

function MarketAuctionRecordRender:ReleaseCallBack()
	if self.item_goods then
		self.item_goods:DeleteMe()
		self.item_goods = nil
	end
end

function MarketAuctionRecordRender:OnFlush()
	if not self.data then return end
	--商品格子信息
	self.item_goods:SetData({item_id = self.data.item_id})
	self.item_goods:SetRightBottomText(self.data.item_num)
	local item_name = ItemWGData.Instance:GetItemName(self.data.item_id, nil ,true)
	self.node_list.item_name.text.text = item_name
	-- print_error("FFF=== 记录", item_name, self.data)
	local is_auction_succ = self.data.auction_uid.temp_low > 0
	self.node_list.text_state.text.text = is_auction_succ and Language.Market.Auction_Succ or Language.Market.Auction_Unsucc
	local time_str = TimeUtil.GetBeforeDHMOnOne(TimeWGCtrl.Instance:GetServerTime() - self.data.log_time)
	self.node_list.text_time.text.text = time_str

	if is_auction_succ then
		local cfg = MarketWGData.Instance:GetNewAuctionCfgByType(self.data.type, self.data.item_id)
		if cfg then
			local is_one_price = self.data.auction_price >= cfg.one_price
			self.node_list.text_way.text.text = is_one_price and Language.Market.Auction_One_Price or Language.Market.Auction_Normal_Price
			self.node_list.price_text.text.text = self.data.auction_price
		end
	else
		self.node_list.text_way.text.text = Language.Market.Auction_Not_Price
		self.node_list.price_text.text.text = Language.Market.Auction_Not_Price
	end
	self.node_list.price_img:SetActive(is_auction_succ)
end
---------------MarketAuctionRecordRender-----end--------------------------------------------
