--仅用来弹出小鬼过期界面
XiaoGuiWGData = XiaoGuiWGData or BaseClass()

--期限小鬼id
local QIXIAN_XIAOGUI = 10101

local PUTONG_XIAOGUI = 10100
--过期小鬼类型
local guoqi_impguard_type = 1

function XiaoGuiWGData:__init()
    if XiaoGuiWGData.Instance then
		ErrorLog("[XiaoGuiWGData] Attemp to create a singleton twice !")
	end
	XiaoGuiWGData.Instance = self

    self.knapsack_xiaogui_list = {}
    self.equip_xiaogui_list = {}
    self.nolonger_need_check = false
    self.shouhu_youshi_time = 0
end

function XiaoGuiWGData:__delete()
    if self.delay_cancle_show then
        GlobalTimerQuest:CancelQuest(self.delay_cancle_show)
        self.delay_cancle_show = nil
    end
    self.shouhu_youshi_time = 0
    self.nolonger_need_check = false
    XiaoGuiWGData.Instance = nil
end

--背包小鬼信息
function XiaoGuiWGData:AddAllKnapsackXiaoGuiInfo(protocol)
    self.knapsack_xiaogui_list = {}
    for i, v in pairs(protocol.info_list) do
		if ItemWGData.GetIsXiaogGui(v.item_id) then
			self:AddKnapsackXiaoGuiInfo(v.index, v)
            self:AutoSellXiaoGui(v.item_id)
		end
	end
    self:SetKnapsackXiaoGuiInfoFlag(true)
    self:CheckLoginFirstOpenView()
end

function XiaoGuiWGData:AddKnapsackXiaoGuiInfo(index, info)
    self.knapsack_xiaogui_list[index] = info
end

function XiaoGuiWGData:RemoveKnapsackXiaoGuiInfo(index)
    self.knapsack_xiaogui_list[index] = nil
end

function XiaoGuiWGData:GetKnapsackXiaoGuiInfo(index)
    return self.knapsack_xiaogui_list[index]
end

--获取所有小鬼列表
function XiaoGuiWGData:GetKnapsackXiaoGuiList()
    return self.knapsack_xiaogui_list
end

function XiaoGuiWGData:GetXiaoGuiIsGuoQi()
    local temp_list = {}
    local server_time = TimeWGCtrl.Instance:GetServerTime()
    --身上
    local xiaogui_info = EquipWGData.Instance:GetmpGuardInfo()
    for k, v in pairs(xiaogui_info.item_wrapper) do
        if v.item_id > 0 and k == guoqi_impguard_type and v.invalid_time == 0 then
            self:SetIsLoginFirstOpenView(true)
            self:SetIsRuntimeFirstOpenView(true)
            return false, nil
        end
    end

    --背包
	for k,v in pairs(self.knapsack_xiaogui_list) do
        local cfg = EquipmentWGData.GetXiaoGuiCfg(v.item_id)
        -- 可能存在被干掉的精灵配置，再与服务器数据对比时候，无法正确获取配置，因此做容错
        if not cfg then
            return false, nil
        end
        --存在一个有效时间的，并且类型是1，就不弹
        if cfg.impguard_type == guoqi_impguard_type and v.invalid_time - server_time > 0 then
            return false, nil
        end
		if v.item_id == QIXIAN_XIAOGUI and v.invalid_time - server_time <= 0 then
            table.insert(temp_list, v)
		end
	end

    if #temp_list > 0 then
        return true, temp_list[1]
    end

    return false, nil
end

function XiaoGuiWGData:AutoSellXiaoGui(item_id)
     if item_id == PUTONG_XIAOGUI and ItemWGData.Instance:GetItemNumInBagById(QIXIAN_XIAOGUI) > 0 then 
        local item_num = ItemWGData.Instance:GetItemNumInBagById(QIXIAN_XIAOGUI)
        local index = ItemWGData.Instance:GetItemIndex(QIXIAN_XIAOGUI)
        BagWGCtrl.Instance:SendDiscardItem(index, 1, QIXIAN_XIAOGUI, item_num, 0)
    end
end

function XiaoGuiWGData:ChangeKnapsackXiaoGuiInfo(protocol)
    --移除
    self:CheckShowMainUiIcon()
    if protocol.item_id == 0 or protocol.num == 0 then
        self:RemoveKnapsackXiaoGuiInfo(protocol.index)
        return
    end

    local is_xiaogui = ItemWGData.GetIsXiaogGui(protocol.item_id)
    if not is_xiaogui then
        return
    end
    

    self:AutoSellXiaoGui(protocol.item_id)
    local xiaogui_info = self.knapsack_xiaogui_list[protocol.index]
    if xiaogui_info then
        xiaogui_info.num = protocol.num
        xiaogui_info.is_bind = protocol.is_bind
        xiaogui_info.invalid_time = protocol.invalid_time
    else
        local info = {}
        info.index = protocol.index
        info.num = protocol.num
        info.item_id = protocol.item_id
        info.is_bind = protocol.is_bind
        info.invalid_time = protocol.invalid_time
        self:AddKnapsackXiaoGuiInfo(protocol.index, info)
        --在线过期会走这里
        --self:CheckRuntimeFirstOpenView()
        self:CheckLoginFirstOpenView()
    end 
end

--背包小鬼信息初始化标记
function XiaoGuiWGData:SetKnapsackXiaoGuiInfoFlag(bo)
    self.is_init_xiaogui_info = bo
end

function XiaoGuiWGData:GetKnapsackXiaoGuiInfoFlag()
    return self.is_init_xiaogui_info
end

--登陆第一次弹窗标记
function XiaoGuiWGData:SetIsLoginFirstOpenView(bo)
    self.is_login_first_open_view = bo
end

function XiaoGuiWGData:GetIsLoginFirstOpenView()
    return self.is_login_first_open_view
end

--在线游戏第一次弹窗标记
function XiaoGuiWGData:SetIsRuntimeFirstOpenView(bo)
    self.is_runtime_open_view = bo
end

function XiaoGuiWGData:GetIsRuntimeFirstOpenView()
    return self.is_runtime_open_view
end

--上游戏检测小鬼过期，只弹一次窗口
function XiaoGuiWGData:CheckLoginFirstOpenView()
    --local item_num = ItemWGData.Instance:GetItemNumInBagById(QIXIAN_XIAOGUI)
    if not self:GetIsLoginFirstOpenView() and MainuiWGCtrl.Instance:GetXiaoGuiInfoFlag() == 1 and self:GetKnapsackXiaoGuiInfoFlag() then
		local is_guoqi = self:GetXiaoGuiIsGuoQi()
		if is_guoqi then
            MainuiWGCtrl.Instance:OpenGuradInvalidTimeView()
            self:CheckShowMainUiIcon()
			self:SetIsLoginFirstOpenView(true)
		end
	end
end

--游戏运行的时候小鬼过期了，只弹一次窗口
function XiaoGuiWGData:CheckRuntimeFirstOpenView()
    if not self:GetIsRuntimeFirstOpenView() and not self:GetIsLoginFirstOpenView() and MainuiWGCtrl.Instance:GetXiaoGuiInfoFlag() == 1 and self:GetKnapsackXiaoGuiInfoFlag() then
		local is_guoqi = self:GetXiaoGuiIsGuoQi()
		if is_guoqi then
            MainuiWGCtrl.Instance:OpenGuradInvalidTimeView()
			self:SetIsRuntimeFirstOpenView(true)
		end
	end
end

function XiaoGuiWGData:CheckShowMainUiIcon()
    local is_guoqi, guoqi_list = XiaoGuiWGData.Instance:GetXiaoGuiIsGuoQi()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    if open_day < 0 then
        GlobalTimerQuest:AddDelayTimer(function ()
            self:CheckShowMainUiIcon()
        end,5)
        return
    end

    if guoqi_list and guoqi_list.invalid_time then
        local server_time = TimeWGCtrl.Instance:GetServerTime()
        local today_guoqi_time = TimeWGCtrl.Instance:NowDayTimeStart(guoqi_list.invalid_time) --过期开始天时间
        local now_day_star_time = TimeWGCtrl.Instance:NowDayTimeStart(server_time)
        
        local youhui_time,day_require = EquipWGData.Instance:GetShouHuYouShiTime()
        youhui_time = youhui_time * 60
        youhui_time = youhui_time + guoqi_list.invalid_time
        local need_show = false
        if open_day <= day_require then
            if youhui_time > server_time then
                need_show = true
            end
        elseif today_guoqi_time < now_day_star_time - 500 and youhui_time > server_time then
            need_show = true
        end
        
        if need_show then
            local click_func =  function ()
                 MainuiWGCtrl.Instance:OpenGuradInvalidTimeView()
                 XiaoGuiWGData.Instance:CheckShowMainUiIcon()
            end
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHOU_HU_YOU_HUI, 1, click_func)
            MainuiWGCtrl.Instance:CheckGuradInvalidFlush()
            self.shouhu_youshi_time = youhui_time
            if self.delay_cancle_show then
                GlobalTimerQuest:CancelQuest(self.delay_cancle_show)
                self.delay_cancle_show = nil
            end
            self.delay_cancle_show = GlobalTimerQuest:AddDelayTimer(function ()
                MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHOU_HU_YOU_HUI, 0)
                GlobalTimerQuest:CancelQuest(self.delay_cancle_show)
                self.delay_cancle_show = nil
            end,youhui_time-server_time)
        else
           -- self.nolonger_need_check = true
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHOU_HU_YOU_HUI, 0)
            
            if self.delay_cancle_show then
                GlobalTimerQuest:CancelQuest(self.delay_cancle_show)
                self.delay_cancle_show = nil
            end

        end
    else
        MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.SHOU_HU_YOU_HUI, 0)
        if self.delay_cancle_show then
            GlobalTimerQuest:CancelQuest(self.delay_cancle_show)
            self.delay_cancle_show = nil
        end
    end
end

function XiaoGuiWGData:GetIsShowYouShiTime()
    if self.delay_cancle_show then
        return true,self.shouhu_youshi_time
    end
    return false,self.shouhu_youshi_time
end