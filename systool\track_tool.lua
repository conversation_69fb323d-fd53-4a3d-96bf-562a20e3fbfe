LogToReport = true

local flag_list = {}
function TestTrackLog(log_flag, tip, key)
	if not LogToReport then
		return
	end
	flag_list[log_flag] = flag_list[log_flag] or {}
	local key = tostring(key)
	if flag_list[log_flag][key] then
		return
	end
	flag_list[log_flag][key] = true
	print_error("错误码提示", tip, key, debug.traceback())
end

TrackObj = {}
if ClassPrintSwitch then
	TrackObj["tool"] = true
end
function TestTrackPrint(track_obj, ...)
	if TrackObj[track_obj] then
		print_error(...)
	else
		print_log(...)
	end
end