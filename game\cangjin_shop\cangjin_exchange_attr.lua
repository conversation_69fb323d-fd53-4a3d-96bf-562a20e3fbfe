CangJinExchangeAttr = CangJinExchangeAttr or BaseClass(SafeBaseView)

function CangJinExchangeAttr:__init()
	self:SetMaskBg(false, true)
	self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/cangjin_shop_prefab", "layout_cangjin_choose_attr")
end

function CangJinExchangeAttr:__delete()

end

function CangJinExchangeAttr:OpenCallBack()
end

function CangJinExchangeAttr:CloseCallBack()

end

function CangJinExchangeAttr:LoadCallBack()
    if not self.left_attr_list then
		self.left_attr_list = AsyncListView.New(CangJinExchangeAttrRender, self.node_list.left_attr_scroll)
		self.left_attr_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectLeftAttr<PERSON><PERSON><PERSON>, self))
		self.left_attr_list:SetRefreshCallback(BindTool.Bind1(self.OnLeftAttrRefreshCallback, self))
	end

	if not self.right_attr_list then
		self.right_attr_list = AsyncListView.New(CangJinExchangeAttrRender, self.node_list.right_attr_scroll)
		self.right_attr_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectRightAttrHandler, self))
		self.right_attr_list:SetRefreshCallback(BindTool.Bind1(self.OnRightAttrRefreshCallback, self))
	end

	XUI.AddClickEventListener(self.node_list["reset_btn"], BindTool.Bind(self.OnClickReset, self))
	XUI.AddClickEventListener(self.node_list["attr_select_btn"], BindTool.Bind(self.OnClickAttrSelect, self))

    self.left_select = {}
	self.right_select = {}
	--最大选择数量
	self.left_select_max = 4
	self.right_select_max = 1
end

function CangJinExchangeAttr:ReleaseCallBack()
	if self.left_attr_list then
		self.left_attr_list:DeleteMe()
		self.left_attr_list = nil
	end

	if self.right_attr_list then
		self.right_attr_list:DeleteMe()
		self.right_attr_list = nil
	end

    self.left_select = nil
	self.right_select = nil
end

function CangJinExchangeAttr:OnFlush(param_t, index)
	self.left_select = {}
	self.right_select = {}

    local left_data = CangJinShopWGData.Instance:GetChooseAttrList(true)
	local right_data = CangJinShopWGData.Instance:GetChooseAttrList(false)

    if self.left_attr_list then
        self.left_attr_list:SetDataList(left_data)
    end

    if self.right_attr_list then
        self.right_attr_list:SetDataList(right_data)
    end

	local already_choose_attr = CangJinShopWGData.Instance:IsAlreadyChooseAttr()
	self.node_list.attr_select_btn:SetActive(not already_choose_attr)
	self.node_list.reset_btn:SetActive(already_choose_attr)
	XUI.SetGraphicGrey(self.node_list["attr_select_btn"], #self.left_select ~= self.left_select_max or #self.right_select ~= self.right_select_max)

	self.node_list.common_capability:SetActive(already_choose_attr)
	local _, cap = CangJinShopWGData.Instance:GetAllChooseAttrList()
	self.node_list.cap_value.text.text = cap
end

function CangJinExchangeAttr:RefreshSelectBtnStatus()
	XUI.SetGraphicGrey(self.node_list["attr_select_btn"], #self.left_select ~= self.left_select_max or #self.right_select ~= self.right_select_max)
end


function CangJinExchangeAttr:OnSelectLeftAttrHandler(item, cell_index, is_default, is_click, is_init)
	if is_default then 
		return 
	end
	
	if nil == item or nil == item.data then
		return
	end

	local item_data = item.data
	local already_choose_attr = CangJinShopWGData.Instance:IsAlreadyChooseAttr()
	local is_can_choose_attr =  CangJinShopWGData.Instance:GetIsActChooseAttrTeQuan()
	if not is_can_choose_attr then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.NeedActTeQuan)
		return
	end

	if is_click and (not already_choose_attr) then
		self:CheckAddOrRemove(true, item_data)
		local is_have ,_ = self:CheckHaveSelect(self.left_select, item_data)
		item:SetSelect(is_have)
		self:RefreshSelectBtnStatus()
	end
end

function CangJinExchangeAttr:OnLeftAttrRefreshCallback(item_cell, cell_index)
	local already_choose_attr = CangJinShopWGData.Instance:IsAlreadyChooseAttr()
	if already_choose_attr then
		item_cell:SetSelect(item_cell.data.is_choose)
	else
		local is_have ,_ = self:CheckHaveSelect(self.left_select, item_cell.data)
		item_cell:SetSelect(is_have)
	end
end

function CangJinExchangeAttr:OnSelectRightAttrHandler(item, cell_index, is_default, is_click, is_init)
	if is_default then 
		return 
	end
	
	if nil == item or nil == item.data then
		return
	end

	local item_data = item.data
	local already_choose_attr = CangJinShopWGData.Instance:IsAlreadyChooseAttr()
	local is_can_choose_attr =  CangJinShopWGData.Instance:GetIsActChooseAttrTeQuan()
	if not is_can_choose_attr then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.NeedActTeQuan)
		return
	end

	if is_click and (not already_choose_attr) then
		self:CheckAddOrRemove(false, item_data)
		local is_have ,_ = self:CheckHaveSelect(self.right_select, item_data)
		item:SetSelect(is_have)
		self:RefreshSelectBtnStatus()
	end
end

function CangJinExchangeAttr:OnRightAttrRefreshCallback(item_cell, cell_index)
	local already_choose_attr = CangJinShopWGData.Instance:IsAlreadyChooseAttr()
	if already_choose_attr then
		item_cell:SetSelect(item_cell.data.is_choose)
	else
		local is_have ,_ = self:CheckHaveSelect(self.right_select, item_cell.data)
		item_cell:SetSelect(is_have)
	end
end


function CangJinExchangeAttr:CheckHaveSelect(operate_table, cell_data)
	for index, temp_data in ipairs(operate_table) do
		if temp_data.seq == cell_data.seq then
			return true, index
		end
	end

	return false, nil
end

function CangJinExchangeAttr:CheckAddOrRemove(is_left, cell_data)
	local max_count = is_left and self.left_select_max or self.right_select_max
	local operate_select = is_left and self.left_select or self.right_select
	local is_have ,index = self:CheckHaveSelect(operate_select, cell_data)
	if is_have then
		table.remove(operate_select, index)
		return
	end

	---检测最大上限
	if #operate_select == max_count then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.select_attr_limit)
		return
	end

	local data = {}
	data.seq = cell_data.seq
	table.insert(operate_select, data)
end

function CangJinExchangeAttr:OnClickReset()
	local already_choose_attr = CangJinShopWGData.Instance:IsAlreadyChooseAttr()
	if not already_choose_attr then
		return
	end

	CangJinShopWGCtrl.Instance:SendCangJinShopRequest(CANGJINSHANGPU_OPERA_TYPE.RESET_CHOOSE_ATTR)
end

function CangJinExchangeAttr:OnClickAttrSelect()
	if  #self.left_select < self.left_select_max or #self.right_select < self.right_select_max then
		TipWGCtrl.Instance:ShowSystemMsg(Language.CangJinShopView.select_attr_limit)
		return 
	end

	local flag_func = function (select_list)
		local index = 0
		local empty_tab = bit:ll2b(0, 0)
		for k, v in pairs(select_list) do
			index = v.seq
			empty_tab[64 - index] = 1
		end

		local temp_high, temp_low = bit:b2ll(empty_tab)
		local flag = TwoUIntToLL(temp_high, temp_low)
		return flag
	end

	local left_flag = flag_func(self.left_select)
	local right_flag = flag_func(self.right_select)

	CangJinShopWGCtrl.Instance:SendCangJinShopRequest(CANGJINSHANGPU_OPERA_TYPE.CHOOSE_ATTR, left_flag, right_flag)
end

------------CangJinExchangeAttrRender-----------
CangJinExchangeAttrRender = CangJinExchangeAttrRender or BaseClass(BaseRender)
function CangJinExchangeAttrRender:OnFlush()
	if not self.data then
        return
    end

	local attr_list = self.data.attr_list               
    local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_list.attr_str)
    local per_desc = is_per and "%" or ""
    local value_str = is_per and attr_list.attr_value / 100 or attr_list.attr_value
    self.node_list.desc.text.text = string.format("%s：%s%s", EquipmentWGData.Instance:GetAttrName(attr_list.attr_str, false, false), value_str, per_desc)
end

function CangJinExchangeAttrRender:SetSelect(is_select)
	self.node_list.select_img:SetActive(is_select)
end