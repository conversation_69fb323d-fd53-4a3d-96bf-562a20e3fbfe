MarryIntimacyView = MarryIntimacyView or BaseClass(SafeBaseView)

local INTIMACYCOLOR = {
	[0] = "#ffffff",			-- 通用色
	[1] = "#79fa82",			-- 绿
	[2] = "#8FD6FF",				-- 蓝
	[3] = "#cb79fa",			-- 紫
	[4] = "#fab379",			-- 橙
	[5] = "#f97878",				-- 红
	[6] = "#ff9292",				-- 闷骚粉色
	[7] = "#ff9292",				-- 闷骚粉色
	[8] = "#66c7ff",				-- 金
	[9] = "#66c7ff",				-- 金
	[10] = "#fa7994",		-- 炫彩
}

function MarryIntimacyView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(948, 592)})
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_intimacy")

	self.role_intimacy = -1
	self.friend_name = ""
end

function MarryIntimacyView:__delete()

end

function MarryIntimacyView:ReleaseCallBack()
	if self.intimacy_list_view then
		self.intimacy_list_view:RemoveAllItem()
		self.intimacy_list_view = nil
	end
end

function MarryIntimacyView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.Society.IntimacyTitle
	self:CreateIntimacyAttrList()
end

function MarryIntimacyView:CloseCallBack()
	self.role_intimacy = -1
	self.friend_name = ""
end

function MarryIntimacyView:CreateIntimacyAttrList()
	self.intimacy_list_view = AsyncListView.New(IntimacyAttrRender, self.node_list["ph_intimacy_list"])
end

function MarryIntimacyView:OnFlush()
	local intimacy_cfg = MarryWGData.Instance:GetIntimacyCfg()
	if self.intimacy_list_view then
		self.intimacy_list_view:SetDataList(intimacy_cfg)
	end
	if self.role_intimacy > -1 then
		local intimacy_level_cfg = MarryWGData.Instance:GetIntimacyLevelByIntimacy(self.role_intimacy)
		if intimacy_level_cfg then
			self.node_list.friend_name.text.text = string.format(Language.Marry.FriendIntimacy, self.friend_name)
			self.node_list["lbl_role_intimacy"].text.text = self.role_intimacy
			local level_str = string.format(Language.Marry.IntimacyLevel, intimacy_level_cfg.buff_level, intimacy_level_cfg.buff_name, self.role_intimacy)
			self.node_list["lbl_intimacy_level"].text.text = ToColorStr(level_str, INTIMACYCOLOR[intimacy_level_cfg.buff_level])
			
			local str = Language.Common.No
			-- if intimacy_level_cfg.gongji > 0 then
			-- 	str = string.format(Language.Marry.IntimacyAttr, intimacy_level_cfg.gongji, intimacy_level_cfg.maxhp, (intimacy_level_cfg.kill_monster_per_exp / 100))
			-- end
			-- self.node_list["lbl_intimacy_attr"].text.text = str
		end
		local slider_value = MarryWGData.Instance:GetCurIntimacyProgress(self.role_intimacy)
		self.node_list.intmacyslider.slider.value = slider_value
		for i,v in pairs(intimacy_cfg) do
			if self.node_list["yuan_hl_" .. v.buff_level] then
				self.node_list["yuan_hl_" .. v.buff_level]:SetActive(intimacy_level_cfg.buff_level >= v.buff_level)
			end
			if self.node_list["need_" .. v.buff_level] then
				self.node_list["need_" .. v.buff_level].text.text = v.intimacy
			end
		end
	end
end

function MarryIntimacyView:SetIntimacyView(intimacy, name)
	self.role_intimacy = intimacy
	self.friend_name = name 
	self:Open()
end

----- IntimacyAttrRender
IntimacyAttrRender = IntimacyAttrRender or BaseClass(BaseRender)
function IntimacyAttrRender:__init()
	
end

function IntimacyAttrRender:__delete()
	
end

function IntimacyAttrRender:CreateChild()
	BaseRender.CreateChild(self)

	
end

function IntimacyAttrRender:OnFlush()
	if not self.data then return end
	local color = INTIMACYCOLOR[self.data.buff_level]
	local level = string.format(Language.Marry.IntimacyLv, self.data.buff_level)
	self.node_list.lbl_level.text.text = ToColorStr(level, INTIMACYCOLOR[self.data.buff_level])

	local next_level_cfg =  MarryWGData.Instance:GetIntimacyCfgByLevel(self.data.buff_level + 1)
	local intimacy_str = ""
	if next_level_cfg then
		intimacy_str = string.format(Language.Marry.IntImacyname, self.data.buff_name, self.data.intimacy .. "-" .. next_level_cfg.intimacy)  
	else
		intimacy_str = string.format(Language.Marry.IntImacyname, self.data.buff_name, self.data.intimacy .. "+")
	end
	self.node_list["lbl_name"].text.text = ToColorStr(intimacy_str, INTIMACYCOLOR[self.data.buff_level])
	local des = ""
	if self.index > 1 then
		des = string.format(Language.Marry.MarryIntimacy, self.data.gongji, self.data.maxhp, self.data.kill_monster_per_exp / 100)
	else
		des = Language.Common.No
	end
	self.node_list["lbl_attr"].text.text = ToColorStr(des, INTIMACYCOLOR[self.data.buff_level])
end