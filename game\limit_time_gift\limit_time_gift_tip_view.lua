LimitTimeGiftTipView = LimitTimeGiftTipView or BaseClass(SafeBaseView)

function LimitTimeGiftTipView:__init(view_name)
	self.view_name = "LimitTimeGiftTipView"
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/limit_time_gift_prefab", "layout_gift_pass_time_tip")
	self:SetMaskBg(true, true)
end

function LimitTimeGiftTipView:LoadCallBack()
	self.discount_item_list = {}
	for i = 1 , 3 do
		if not self.discount_item_list[i] then
        	self.discount_item_list[i] = LimitRewardItemRander.New(self.node_list["discount_item_"..i])
    	end
	end

	XUI.AddClickEventListener(self.node_list.goto_btn, BindTool.Bind(self.OnClickGotoBtn, self))
end

function LimitTimeGiftTipView:ReleaseCallBack()
	CountDownManager.Instance:RemoveCountDown("limit_time_gift_tip_count_down")

	if self.discount_item_list then
        for i,v in ipairs(self.discount_item_list) do
            v:DeleteMe()
        end
        self.discount_item_list = {}
    end
end

function LimitTimeGiftTipView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "gift_info" then
			self:RefreshView(v)
		end
	end
end

function LimitTimeGiftTipView:RefreshView(gift_info)
	if IsEmptyTable(gift_info) then
		return
	end
	if gift_info.is_online then
		self.node_list.pass_time_desc.text.text = string.format(Language.LimitTimeGift.PassGiftTip2, gift_info.delay_time)
	else
		self:ShowLimitTimeTip(gift_info)
	end
	self:FlushGiftItemList()
	self.node_list.limit_gift_title:SetActive(not gift_info.is_online)
	self.node_list.limit_time_root:SetActive(not gift_info.is_online)
	self.node_list.pass_time_root:SetActive(gift_info.is_online)
end

function LimitTimeGiftTipView:ShowLimitTimeTip(gift_info)
	self.node_list.limit_time_desc.text.text = Language.LimitTimeGift.PassGiftTip1

	CountDownManager.Instance:RemoveCountDown("limit_time_gift_tip_count_down")
	if gift_info.delay_time > 0 then
		self.node_list.limit_time_lbl.text.text = TimeUtil.FormatSecondDHM8(gift_info.delay_time)
		CountDownManager.Instance:AddCountDown(
			"limit_time_gift_tip_count_down",
			BindTool.Bind(self.UpdateCountDown, self),
			BindTool.Bind(self.Close, self),
			nil,
			gift_info.delay_time,
			1
		)
	end
end

function LimitTimeGiftTipView:UpdateCountDown(elapse_time, total_time)
	if self.node_list.limit_time_lbl then
		self.node_list.limit_time_lbl.text.text = TimeUtil.FormatSecondDHM8(total_time - elapse_time)
	end
end

function LimitTimeGiftTipView:OnClickGotoBtn()
	ViewManager.Instance:Open(GuideModuleName.LimitTimeGift)
	self:Close()
end

function LimitTimeGiftTipView:FlushGiftItemList()
	local gift_info_list = LimitTimeGiftWGData.Instance:GetGiftInfoList()
	if not gift_info_list then
		return
	end
	local info_list = SortTableKey(gift_info_list)

	table.sort( info_list, function (a, b)
		local order_a = 100000
		local order_b = 100000

		if a.can_buy_num and b.can_buy_num then
			if a.can_buy_num > b.can_buy_num then
				order_a = order_a + 10000
			elseif a.can_buy_num < b.can_buy_num then
				order_b = order_b + 10000
			end
		end

		if a.is_eject and b.is_eject then
			if a.is_eject > b.is_eject then
				order_a = order_a + 1000
			elseif a.is_eject < b.is_eject then
				order_b = order_b + 1000
			end
		end

		if a.gift_timestamp and b.gift_timestamp then
			if a.gift_timestamp < b.gift_timestamp then
				order_a = order_a + 100
			elseif a.gift_timestamp > b.gift_timestamp then
				order_b = order_b + 100
			end
		end

		return order_a > order_b

	end )
	local itemcount = 0
    for k,v in ipairs(info_list) do
        if nil ~= v then
            itemcount = itemcount + 1
        end
    end
	self:FlushGiftItem(info_list, itemcount)
end

function LimitTimeGiftTipView:FlushGiftItem(info_list, itemcount)
	local show_item_num = 3

	for i = 1, show_item_num do
		if i <= itemcount then
			self.node_list["discount_item_"..i]:SetActive(true)
			self.discount_item_list[i]:SetIndex(i)
			self.discount_item_list[i]:SetData(info_list[i])
		else
			self.node_list["discount_item_"..i]:SetActive(false)

		end
	end
end

------------------------ 限时返回礼包ItemRander ---------------------------------------

LimitRewardItemRander = LimitRewardItemRander or BaseClass(BaseRender)
function LimitRewardItemRander:LoadCallBack()
    if not self.show_cell then
        self.show_cell = ItemCell.New(self.node_list["item_cell_node"])
    end

	self.count_down_key = nil
end

function LimitRewardItemRander:ReleaseCallBack()
    if self.show_cell then
        self.show_cell:DeleteMe()
        self.show_cell = nil
    end

	if self.count_down_key then
		CountDownManager.Instance:RemoveCountDown(self.count_down_key)
		self.count_down_key = nil
	end
end

function LimitRewardItemRander:OnFlush()
	local cfg_data = LimitTimeGiftWGData.Instance:GetGiftCfgByGiftID(self.data.gift_id)
	if not cfg_data then return end
	local item_num = cfg_data.reward_item
	self.show_cell:SetData(item_num[0]) --展示礼包的第一个item
	self.node_list.item_cell_node.rect.localScale = Vector3(0.8,0.8,0.8)
	local cn_str = Language.LimitTimeGift.Num2DiscountStr[cfg_data.gift_discount] or ""
	self.node_list.discount_text.text.text = cn_str .. Language.MustBuy.Discount
	self.node_list.gift_name.text.text = cfg_data.gift_name
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	server_time = math.ceil(server_time)
	local count_down_time = self.data.gift_timestamp - server_time
	self:FlushGiftItemCountDown(count_down_time)
end

function LimitRewardItemRander:FlushGiftItemCountDown(count_down_time)
	--CountDownManager.Instance:RemoveCountDown(self.count_down_key)
	self.count_down_key = "limit_time_gift_item_count_down_item" .. self:GetIndex()
	CountDownManager.Instance:RemoveCountDown(self.count_down_key)
	if count_down_time > 0 then
		self.node_list.limt_time_text.text.text = Language.MustBuy.InstantDeath .. TimeUtil.FormatSecond2HMS(count_down_time)
		CountDownManager.Instance:AddCountDown(
			self.count_down_key,
			BindTool.Bind(self.UpdateCountDown, self),
			nil,
			nil,
			count_down_time,
			1
		)
	end
end

function LimitRewardItemRander:UpdateCountDown(elapse_time, total_time)
	if self.node_list.limt_time_text then
		self.node_list.limt_time_text.text.text = Language.MustBuy.InstantDeath .. TimeUtil.FormatSecond2HMS(total_time - elapse_time)
	end
end
