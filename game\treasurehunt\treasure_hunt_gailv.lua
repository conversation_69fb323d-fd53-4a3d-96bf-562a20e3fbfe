--新寻宝概率展示面板
TreasureHuntProbabilityView = TreasureHuntProbabilityView or BaseClass(SafeBaseView)

function TreasureHuntProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self.view_name = "TreasureHuntProbabilityView"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(706, 486)})
    self:AddViewResource(0, "uis/view/treasurehunt_ui_prefab", "layout_treasure_probability")
    self:SetMaskBg(true, true)
end

function TreasureHuntProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end

end

function TreasureHuntProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.TreasureHunt.ProbabilityTitle
    if not self.probability_list then
        self.probability_list = AsyncListView.New(ProbabilityItemRender, self.node_list.ph_pro_list) 
    end
end

function TreasureHuntProbabilityView:OnFlush()
    local type = TreasureHuntWGCtrl.Instance:GetTreasureType()
    local info = TreasureHuntWGData.Instance:GetProbabilityInfo(type)
    self.probability_list:SetDataList(info, 2)
end

----------------------------------------------------------------------------------
ProbabilityItemRender = ProbabilityItemRender or BaseClass(BaseRender)
function ProbabilityItemRender:__delete()
    
end

function ProbabilityItemRender:LoadCallBack()
    
end

function ProbabilityItemRender:OnFlush()
    if not self.data then
        return
    end
    -- self.node_list.bg:SetActive(self.index % 2 == 1)
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    local item_name = ItemWGData.Instance:GetItemName(self.data.item_id) or ""
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count.."%"
end
