require("game/fuben_team_common_boss/fuben_team_common_boss_wg_data")
require("game/fuben_team_common_boss/fuben_team_common_boss_task_view")
require("game/fuben_team_common_boss/fuben_team_conmon_boss_flower_view")

FuBenTeamCommonBossWGCtrl = FuBenTeamCommonBossWGCtrl or BaseClass(BaseWGCtrl)

function FuBenTeamCommonBossWGCtrl:__init()
	if FuBenTeamCommonBossWGCtrl.Instance then
		ErrorLog("[FuBenTeamCommonBossWGCtrl] attempt to create singleton twice!")
		return
	end

	FuBenTeamCommonBossWGCtrl.Instance = self
	self.data = FuBenTeamCommonBossWGData.New()
	self.task_view = FuBenTeamCommonBossTaskView.New()
	self.flower_view = FuBenTeamCommonBossFlowerView.New()
    self:RegisterAllProtocols()
end

function FuBenTeamCommonBossWGCtrl:__delete()
	FuBenTeamCommonBossWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.task_view then
		self.task_view:DeleteMe()
		self.task_view = nil
	end

	if self.flower_view then
		self.flower_view:DeleteMe()
		self.flower_view = nil
	end
end

function FuBenTeamCommonBossWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCTeamCommonBossFBSceneInfo, "OnSCTeamCommonBossFBSceneInfo")
	self:RegisterProtocol(SCTeamCommonBossFBPlayerInfo, "OnSCTeamCommonBossFBPlayerInfo")
	self:RegisterProtocol(SCTeamCommonBossFBHurtInfo, "OnSCTeamCommonBossFBHurtInfo")
	self:RegisterProtocol(SCTeamCommonBossFBMvpInfo, "OnSCTeamCommonBossFBMvpInfo")
	self:RegisterProtocol(SCTeamCommonBossFBMvpLike, "OnSCTeamCommonBossFBMvpLike")
	self:RegisterProtocol(SCTeamCommonBossFBRoleBaseInfo, "OnSCTeamCommonBossFBRoleBaseInfo")
end

function FuBenTeamCommonBossWGCtrl:OnSCTeamCommonBossFBSceneInfo(protocol)
	self.data:SetTeamCommonBossFBInfo(protocol)

	MainuiWGCtrl.Instance:SetFbIconEndCountDown(protocol.fb_end_time)
	self:FlushTaskView()
	
	if protocol.is_end == 1 then
		local fuben_cfg = self.data:GetFuBenCfgBySeq(protocol.fb_seq)
		if not fuben_cfg then
			return
		end

		local scene_id = fuben_cfg.scene_id
		local scene_cfg = ConfigManager.Instance:GetSceneConfig(scene_id)
		local scene_type = scene_cfg.scene_type

		if protocol.is_pass == 1 then
			local fb_reward_info = self.data:GetTeamCommonBossPlayerInfo(protocol.fb_seq)
			local table_reward = {}
			for i= #fb_reward_info.reward_item_list, 1, -1 do
				local data = fb_reward_info.reward_item_list[i]
				local item_config = ItemWGData.Instance:GetItemConfig(data.item_id)
				if item_config then
					data.color = item_config.color
				else
					data.color = 1
				end
				data.star = data.star or 0
				table.insert(table_reward, data)
			end
			
			table.sort(table_reward, SortTools.KeyUpperSorters("color","star"))
			local cur_star_num = 0
			local pass_time = protocol.fb_finish_time - protocol.fb_start_time
			local pass_reward_cfg = FuBenTeamCommonBossWGData.Instance:GetFuBenPassRewardCfg(protocol.fb_seq)

			for k, v in pairs(pass_reward_cfg) do
				if v.pass_time >= pass_time then
					cur_star_num = v.star_num
					break
				end
			end

			self.data:SetGetIsHelpState(fb_reward_info.is_help)
			self.data:SetGetLeaderReward(fb_reward_info.team_leader_reward_list)
			FuBenWGCtrl.Instance:OpenWin(scene_type, table_reward, fb_reward_info.get_exp, 0, cur_star_num, 10)

		else
			FuBenWGCtrl.Instance:OpenLose(Scene.Instance:GetSceneType())
		end
		FuBenWGCtrl.Instance:SetOutFbTime(0, true, true)
	else
		FuBenWGCtrl.Instance:SetOutFbTime(protocol.fb_end_time, true, true)
	end
end

function FuBenTeamCommonBossWGCtrl:OnSCTeamCommonBossFBPlayerInfo(protocol)
	self.data:SetTeamCommonBossPlayerInfo(protocol)
end

function FuBenTeamCommonBossWGCtrl:OnSCTeamCommonBossFBHurtInfo(protocol)
	self.data:SetTeamCommonBossFBHurtInfo(protocol)
	if self.task_view:IsOpen() then
		self.task_view:Flush(0, "rank_info")
	end
end

function FuBenTeamCommonBossWGCtrl:OnSCTeamCommonBossFBMvpInfo(protocol)
	self.data:SetTeamCommonBossFBShowMvpInfo(protocol)
	if self.task_view:IsOpen() then
		self.task_view:Flush(0, "show_mvp_info")
	end
end

function FuBenTeamCommonBossWGCtrl:OnSCTeamCommonBossFBMvpLike(protocol)
	--print_error("======MvpLike======",protocol)
	self.data:SetTeamCommonBossFBMvpLike(protocol)
	if self.task_view:IsOpen() then
		self.task_view:Flush(0, "mvp_like")
	end

	MainuiWGCtrl.Instance:FlushSpeFbSendFlowerMsgHead()
end

function FuBenTeamCommonBossWGCtrl:OnSCTeamCommonBossFBRoleBaseInfo(protocol)
	--print_error("======OnSCTeamCommonBossFBRoleBaseInfo======",protocol)
end

function FuBenTeamCommonBossWGCtrl:OpenFBTaskView()
    if self.task_view then
		self.task_view:Open()
	end
end

function FuBenTeamCommonBossWGCtrl:CloseFBTaskView()
    if self.task_view and self.task_view:IsOpen() then
		self.task_view:Close()
	end
end

function FuBenTeamCommonBossWGCtrl:GetFBTaskView()
    return self.task_view
end

function FuBenTeamCommonBossWGCtrl:FlushTaskView()
	if self.task_view and self.task_view:IsOpen() then
		self.task_view:Flush()
	end
end


local FindNpcSceneType = {
	[SceneType.TEAM_COMMON_BOSS_FB_1] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_2] = true,
	[SceneType.TEAM_COMMON_BOSS_FB_3] = true,
}

function FuBenTeamCommonBossWGCtrl:FindTaskNpc()
	if not FindNpcSceneType[Scene.Instance:GetSceneType()] then
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	local scene_fuben_cfg = self.data:GetFuBenSceneCfg(scene_id)
	if not scene_fuben_cfg then
		return nil
	end

	local fb_seq = scene_fuben_cfg.seq
	local fb_scene_info = self.data:GetTeamCommonBossFBInfo(fb_seq)
	if not fb_scene_info then
		return nil
	end

    local stage_cfg = self.data:GetFuBenStageCfg(fb_seq, fb_scene_info.stage)
    if not stage_cfg then
        return nil
    end

	if stage_cfg.type == FuBenTeamCommonBossWGData.TaskType.Npc then
		GuajiWGCtrl.Instance:StopGuaji(nil, true)
		GuajiWGCtrl.Instance:ClearCurGuaJiInfo(nil, nil, true)
		GuajiWGCtrl.Instance:MoveToNpc(stage_cfg.param0, nil, Scene.Instance:GetSceneId())
	end
end

function FuBenTeamCommonBossWGCtrl:OpenSendFlowerView(index)
	local info = self.data:GetSpeSendFlowerInfoByIndex(index)
	MainuiWGCtrl.Instance:FlushSpeFbSendFlowerMsgHead()
	self.flower_view:SetDataAndOpen(info)
end