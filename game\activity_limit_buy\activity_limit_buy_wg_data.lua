ActivityLimitBuyWGData = ActivityLimitBuyWGData or BaseClass()
ActivityLimitBuyWGData.Max_Page_Count = 5-- page数量

function ActivityLimitBuyWGData:__init()
	if ActivityLimitBuyWGData.Instance ~= nil then
		<PERSON>rror<PERSON><PERSON>("[ActivityLimitBuyWGData] attempt to create singleton twice!")
		return
	end

	ActivityLimitBuyWGData.Instance = self

	self:InitCfg()
	self.grade = 1 -- 档次
	self.rmb_buy_times_list = {}
end

function ActivityLimitBuyWGData:__delete()
	ActivityLimitBuyWGData.Instance = nil
	self.grade = nil
	self.rmb_buy_times_list = nil
end

function ActivityLimitBuyWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_limit_rmb_buy_auto")
	self.rmb_buy_seq_cfg = ListToMapList(cfg.rmb_buy, "grade", "activity_day")
end

function ActivityLimitBuyWGData:SetAllBuyInfo(protocol)
	self.grade = protocol.grade
	self.rmb_buy_times_list = protocol.rmb_buy_times_list
end

function ActivityLimitBuyWGData:GetCurDayBuyList()
	local cur_grade_cfg = self.rmb_buy_seq_cfg[self.grade]
	if cur_grade_cfg then
		local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY)
		if cur_grade_cfg[act_day] then
			return cur_grade_cfg[act_day]
		else
			print("===========配置天数不存在day",act_day)
			return {}
		end
	else
		print("===========配置档次不存在self.grade=",self.grade)
		return {}
	end
end

function ActivityLimitBuyWGData:GetCurDayBuyInfoBySeq(seq)
	local cur_grade_cfg = self.rmb_buy_seq_cfg[self.grade]
	if cur_grade_cfg then
		local act_day = ActivityWGData.Instance:GetActivityCurOpenday(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY)
		if cur_grade_cfg[act_day] then
			if cur_grade_cfg[act_day][seq] then
				return cur_grade_cfg[act_day][seq]
			end
		else
			print("===========配置天数不存在day",act_day)
			return {}
		end
	else
		print("===========配置档次不存在self.grade=",self.grade)
		return {}
	end
end

function ActivityLimitBuyWGData:GetBuyCountBySeq(seq)
	return self.rmb_buy_times_list[seq] or 0
end

function ActivityLimitBuyWGData:GetBuyStateBySeq(data) -- 获取索引物品是否买完
	local seq_all_buy = false
	if not IsEmptyTable(data) and data.buy_times and data.seq then
		local pro_buy_times = self.rmb_buy_times_list[data.seq]
		if data.buy_times <= pro_buy_times then
			seq_all_buy = true
		end
	end

	return seq_all_buy
end
