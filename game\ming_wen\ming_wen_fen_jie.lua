MingWenView = MingWenView or BaseClass(SafeBaseView)

function MingWenView:InitFenJie()
	for i=1, 4 do
		self.node_list["rune_fenjie_color_btn"..i].button:AddClickListener(BindTool.Bind(self.ClickFenJieColor,self,i))
	end
	self.node_list["rune_fenjie_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFenJie,self))
	
	-- 分解奖励
	if not self.fenjie_reward_list then
		self.fenjie_reward_list = AsyncListView.New(ItemCell, self.node_list.fenjie_reward)
	end

	if not self.posy_fenjie_list then
        self.posy_fenjie_list = AsyncBaseGrid.New()
		local bundle = "uis/view/ming_wen_ui_prefab"
		local asset = "mingwen_fenjie_cell"
        self.posy_fenjie_list:CreateCells({col = 4, change_cells_num = 1, list_view = self.node_list["rune_fenjie_list"],
		assetBundle = bundle, assetName = asset, itemRender = MingWenFenJieCell})
		--self.posy_fenjie_list:SetSelectCallBack(BindTool.Bind(self.FenJieSelectCallBack, self))
        self.posy_fenjie_list:SetStartZeroIndex(false)
    end
	self.fenjie_list = nil
end

function MingWenView:DeleteFenJie()

end

function MingWenView:ReleaseFenJie()
	if self.posy_fenjie_list then
		self.posy_fenjie_list:DeleteMe()
		self.posy_fenjie_list = nil
	end
	self.need_flush_nexttime = nil
	if self.delay_flush_fenjie then
		GlobalTimerQuest:CancelQuest(self.delay_flush_fenjie)
		self.delay_flush_fenjie = nil
	end

	if self.delay_effect_close then
		GlobalTimerQuest:CancelQuest(self.delay_effect_close)
		self.delay_effect_close = nil
	end

	-- 分解列表
	if self.fenjie_reward_list then
        self.fenjie_reward_list:DeleteMe()
        self.fenjie_reward_list = nil
    end

	if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end
end

function MingWenView:OnClickFenJie()
	
	local all_select_item = MingWenWGData.Instance:GetAllFenJieSelectItem()
	local fenjie_list = {}
	local chaijie_list = {}
	local fenjie_num = 0
	local chaijie_num = 0
	local is_have_better = false
	for k,v in pairs(all_select_item) do
		if v.select == 1 then
			if MingWenWGData.Instance:CheckMingWenIsFromHeCheng(v.item_id) then
				chaijie_num = chaijie_num + 1
				chaijie_list[chaijie_num] = v.index
			else
				fenjie_num = fenjie_num +1
				fenjie_list[fenjie_num] = v.index
			end

			if not is_have_better then
				is_have_better = MingWenWGData.Instance:GetItemIsBetter(v.item_id, v.type, v.quality)
			end
		end
	end

	local func = function ()
		if (chaijie_num > 0 or fenjie_num > 0) and not self.delay_flush_fenjie then
			-- self:PlayEffectFire()
			self:PlayResloveEffect()
			if nil == self.delay_flush_fenjie then
				-- self.delay_flush_fenjie = GlobalTimerQuest:AddDelayTimer(function()
				-- 		MingWenWGData.Instance:SetDoingFenJie(false)
				-- 		GlobalTimerQuest:CancelQuest(self.delay_flush_fenjie)
				-- 		self.delay_flush_fenjie = nil
				-- 		-- self.delay_flush_fenjie = GetAttributeNameListByType;
				-- 		self:CheckNeedFlush()
				-- 	end,2)

				if chaijie_num > 0 then
					for k,v in pairs(chaijie_list) do
						MingWenWGCtrl.Instance:SendMingWenOperaReq(PPSY_OPERA_TYPE.POSY_OPERATOR_TYPE_DE_COMPOSE_INDEX, v)
					end
				end

				if fenjie_num > 0 then
					MingWenWGCtrl.Instance:SendMingWenDeCompose(fenjie_num, fenjie_list)
				end
				self:PlayerCellFenJieEffect()
			end
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.MingWenView.NoSelectItem)
		end
	end

	if is_have_better then
		if not self.alert then
			self.alert = Alert.New()
		end

		local str = string.format(Language.MingWenView.TipDesc)

		self.alert:SetLableString(str)
		self.alert:SetOkFunc(func)
		self.alert:Open()
	else
		func()
	end
end

function MingWenView:PlayEffectFire()
	--local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_fenjielu)
	--EffectManager.Instance:PlayAtTransform(bundle_name, asset_name,self.node_list["rune_fenjie_effect1"].transform,5)
	-- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_fenjie2)
	-- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name,self.node_list["rune_fenjie_effect3"].transform,2)
	if self.delay_effect_close then
		return
	end
	self.node_list["rune_fenjie_effect3"]:SetActive(true)
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.DecomPosition))
	self.delay_effect_close = GlobalTimerQuest:AddDelayTimer(function()
		self.node_list["rune_fenjie_effect3"]:SetActive(false)
		GlobalTimerQuest:CancelQuest(self.delay_effect_close)
		self.delay_effect_close = nil
	end, 1.9)
end

function MingWenView:ClickFenJieColor(color)
	local color_select = MingWenWGData.Instance:CheckFenJieColorSelect(color) -- 拿是否勾选
	color_select = color_select == 1 and 0 or 1
	MingWenWGData.Instance:SetFenJieColorSelect(color,color_select) -- 设置颜色选中
    MingWenWGData.Instance:FenJieFlushColorData(color,color_select)	-- 全选数据
	self:FlushColorSelectHook()
	self:ReFlushActiveCell()
	--self:FLushFenJieExp()
	self:FlushSelectFenJieItemReward()
end

function MingWenView:FenJieSelectCallBack(index)
	local is_select = MingWenWGData.Instance:CheckIndexIsSelect(index)
	local value = is_select and 0 or 1
	MingWenWGData.Instance:FenJieSelectByIndex(index, value)
	self:ReFlushActiveCell()
	--self:FLushFenJieExp()
	self:FlushSelectFenJieItemReward()
end

function MingWenView:CheckNeedFlush()
	if self.need_flush_nexttime then
		self:FlushFenJie(self.need_flush_nexttime)
		self.need_flush_nexttime = nil
	end
end

function MingWenView:FlushFenJie(param)
	if self.delay_flush_fenjie then
		self.need_flush_nexttime = param
		return
	end

	if param == "all" then
		self:FlushFenJieList()
		--self:FLushFenJieExp()
		self:FlushSelectFenJieItemReward()
	end
end

-- 播放分解特效
function MingWenView:PlayResloveEffect()
	local view_name = "MingWenView"
	local start_obj = self.node_list.start_eff_pos
	local end_obj = self.node_list.end_eff_pos
	local effect_count = 1
	local effect_name = "UI_hunyin_fenjie"
	local bundle, asset = ResPath.GetUIEffect(effect_name)
	TipWGCtrl.Instance:ShowFlyEffectManager(
		view_name, bundle, asset, 
		start_obj, end_obj, DG.Tweening.Ease.Linear,
		2.1, nil, nil, 
		effect_count, 150, nil, false, true)
end

function MingWenView:FLushFenJieExp()
	local cur_exp,cur_xinjing = MingWenWGData.Instance:GetMingWenExpAndXinJing()
	local add_exp,add_xinjing = MingWenWGData.Instance:GetFenJieExpAndXinJing()
	local str1 = ""
	if add_exp ~= 0 then
		str1 = ToColorStr(" +"..add_exp, COLOR3B.DEFAULT_NUM) --改用亮底绿
	end
	local str2 = ""
	if  add_xinjing ~= 0 then
		str2 = ToColorStr(" +"..add_xinjing, COLOR3B.DEFAULT_NUM)
	end
	self.node_list["rune_fenjie_exp"].text.text = cur_exp .. str1
	self.node_list["rune_fenjie_xinjing"].text.text = cur_xinjing .. str2

end

-- 分解奖励
function MingWenView:FlushSelectFenJieItemReward()
	local all_select_fenjie_item = MingWenWGData.Instance:GetAllFenJieSelectItem()
	self.fenjie_list = {}
	for k,v in pairs(all_select_fenjie_item) do
		if v.select == 1 then -- v.item_id就是选中铭文的id	
			local data = v.fenjie_data
			if data then
				self.fenjie_list = MingWenWGData.Instance:FlushFenJieReward(v.item_id, data, self.fenjie_list)
				--self.node_list.fenjie_reward:SetActive(true)
			end
		end
	end

	self.node_list.fenjie_reward:SetActive(not IsEmptyTable(all_select_fenjie_item))

	if not self.fenjie_list then
		return
	end

	local list = {}
	for _, item_data in pairs(self.fenjie_list) do
		table.insert(list, item_data)
	end

	self.fenjie_reward_list:SetDataList(list)
end

function MingWenView:FlushColorSelectHook()
	for i= 1,4 do
		local color_select = MingWenWGData.Instance:CheckFenJieColorSelect(i)
		self.node_list["rune_fenjie_color_hook"..i]:SetActive(color_select == 1)
	end
end

function MingWenView:FlushFenJieList()
	local all_data = MingWenWGData.Instance:GetFenJieListData1()
	for k,v in pairs(all_data) do
		v.select_callback = BindTool.Bind(self.FenJieSelectCallBack, self)
	end
	self.node_list["rune_fenjie_blank"]:SetActive(#all_data == 0)
	-- self.node_list["fire_bg"]:SetActive(num ~= 0)
	self.posy_fenjie_list:SetDataList(all_data)
	MingWenWGData.Instance:InitFenJieSelectColor()
	self:FlushColorSelectHook()
end

function MingWenView:ReFlushActiveCell()
	self.node_list["rune_fenjie_list"].scroller:RefreshActiveCellViews()
	--self.posy_fenjie_list:RefreshActiveCellViews() -- 只刷新已经创建了的格子（不额外创建）
end

function MingWenView:FlushFenjieBigHL()
	local fenjie_big_cell_list = self.posy_fenjie_list:GetDataList()
	for k,v in pairs(fenjie_big_cell_list) do
		v:FlushFenJieCellHL()
	end
end

function MingWenView:PlayerCellFenJieEffect()
	MingWenWGData.Instance:SetDoingFenJie(true)
	self:ReFlushActiveCell()
end

-------------------------------------------------------------------------------
MingWenFenJieCell = MingWenFenJieCell or BaseClass(BaseRender)

function MingWenFenJieCell:LoadCallBack()
	self.mingwen_cell = MingWenItemRender.New(self.node_list.mingwen_cell)
	self.mingwen_cell:SetIsShowItemTip(false)
	self.mingwen_cell:SetClickCallBack(BindTool.Bind1(self.OnClickBtnClick, self))
	--XUI.AddClickEventListener(self.node_list.button_click, BindTool.Bind1(self.OnClickBtnClick, self))
end

function MingWenFenJieCell:__delete()
	if self.mingwen_cell then
        self.mingwen_cell:DeleteMe()
        self.mingwen_cell = nil
    end
end

function MingWenFenJieCell:OnFlush()
	self.mingwen_cell:SetData(self.data)
	self.mingwen_cell:SetIndex(self.index)
	self:FlushFenJieCellHL()
end

function MingWenFenJieCell:OnClickBtnClick()
	self.data.select_callback(self.data.index)
end

function MingWenFenJieCell:FlushFenJieCellHL()
	if not IsEmptyTable(self.data) then
		local is_select = MingWenWGData.Instance:CheckIndexIsSelect(self.data.index)
		local index = is_select and self.index or -1
		self.mingwen_cell:FlushHL(index)
	end
end
-----------------------------------------------------------------------------------------------
MingWenFenJieBigCell = MingWenFenJieBigCell or BaseClass(BaseRender)

function MingWenFenJieBigCell:__init()
	self.fengjie_cell_list = {}
	for i=1,4 do 
		self.fengjie_cell_list[i] = MingWenFenJieSmallCell.New(self.node_list["fengjie_cell"..i])
		self.fengjie_cell_list[i]:SetSelectCallBack(BindTool.Bind(self.FenJieCellSelectCallBack,self))
	end
end

function MingWenFenJieBigCell:__delete()
	if self.fengjie_cell_list then
		for k,v in pairs(self.fengjie_cell_list) do
			v:DeleteMe()
		end
		self.fengjie_cell_list = nil
	end

	if self.delay_effect then
		GlobalTimerQuest:CancelQuest(self.delay_effect)
		self.delay_effect = nil
	end
end

function MingWenFenJieBigCell:FenJieCellSelectCallBack(index)
	if self.data then
		self.data.select_callback(index)
	end
end

function MingWenFenJieBigCell:OnFlush()
	for i= 1,4 do
		self.node_list["fengjie_panel"..i]:SetActive(true)
		if self.data and self.data[i] then
			--local now_data = self.fengjie_cell_list[i]:GetData()
			--if now_data and now_data.item_id == self.data[i].item_id 
			--	and now_data.index == self.data[i].index 
			--	and  now_data.level == self.data[i].level then
			--else
				self.fengjie_cell_list[i]:SetData(self.data[i])
			--end
			self.node_list["fengjie_cell"..i]:SetActive(true)
		else
			self.node_list["fengjie_cell"..i]:SetActive(false)
		end
	end
	self:FlushFenJieCellHL()
	local is_doing =  MingWenWGData.Instance:CheckIsDoingFenjie()
	if is_doing then
		self:FlushFenJieCellEffect()
	end

end

function MingWenFenJieBigCell:FlushFenJieCellHL()
	for k,v in pairs(self.fengjie_cell_list) do
		v:FlushFenJieSmallCellHL()
	end
end

function MingWenFenJieBigCell:FlushFenJieCellEffect()
	for k,v in pairs(self.fengjie_cell_list) do
		-- v:OnPlayerFenJieEffect()
	end
end

---------------------------------------------------------------------------------------------
MingWenFenJieSmallCell = MingWenFenJieSmallCell or BaseClass(BaseRender)

function MingWenFenJieSmallCell:__init()
	--self.fenjie_item = ItemCell.New(self.node_list["rune_fenjie_item"])
	self.node_list["rune_fenjie_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFenJieSmallCell, self))
	self.is_playing = false

end

function MingWenFenJieSmallCell:__delete()
	self:CancelTween()
end

function MingWenFenJieSmallCell:OnFlush()
	self.node_list["rune_fenjie_panel"]:SetActive(true)
	if not IsEmptyTable(self.data) then
		--self.fenjie_item:SetData(self.data)
		local cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
 		local bundle, asset = ResPath.GetItem(cfg.icon_id)
 		self.node_list["rune_fenjie_item"].image:LoadSpriteAsync(bundle, asset,function ()
			self.node_list["rune_fenjie_item"].image:SetNativeSize()
		end)

		local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(self.data.item_id)
		if base_cfg then
			local str = ""
			local is_select = MingWenWGData.Instance:CheckIndexIsSelect(self.data.index)
			local color = is_select and COLOR3B.WHITE or COLOR3B.DEFAULT
			if base_cfg.type > 1 then
				local lv_str = ToColorStr("Lv."..self.data.level, ITEM_COLOR[cfg.color])  --铭文等级
				self.node_list["rune_fenjie_name"].text.text = string.format("%s %s", ToColorStr(cfg.name, ITEM_COLOR[cfg.color]), lv_str)
				local attr_type,attr_value,add_value = MingWenWGData.Instance:GetEquipPosyAttrByItemId(self.data.item_id)
				local name_list,is_pre = MingWenWGData.Instance:GetAttributeNameListByType(attr_type)
                for k,v in pairs(name_list) do
                    local tail = k ~= #name_list and "\n" or ""
                    local name = ToColorStr(v, color)
                    if is_pre[k] then
						str = str..name.."  "..ToColorStr(((tonumber(attr_value[k]) + tonumber(add_value[k]) * (tonumber(self.data.level) -1))/100).."%" ,COLOR3B.DEFAULT)..tail
                    else
						str = str..name.."  "..ToColorStr((tonumber(attr_value[k]) + tonumber(add_value[k]) * (tonumber(self.data.level) -1)),COLOR3B.DEFAULT)..tail
					end
				end
			else
				self.node_list["rune_fenjie_name"].text.text = ToColorStr(cfg.name, ITEM_COLOR[cfg.color])
				local level_cfg = MingWenWGData.Instance:GetMingWenLvCfgByLv(1)
				if level_cfg then
					local fenjie_jingyan = level_cfg["get_jingyan_"..base_cfg.cost_type] or 0
					str = ToColorStr(Language.MingWenView.MingWenExpAdd, color) .. ":" .. ToColorStr(fenjie_jingyan, COLOR3B.DEFAULT_NUM)
				end
			end

			self.node_list["rune_fenjie_attr"].text.text = str
		else
			self.node_list["rune_fenjie_name"].text.text = ""
			self.node_list["rune_fenjie_attr"].text.text = ""
		end
	else
		self.node_list["rune_fenjie_panel"]:SetActive(false)
		self.node_list["rune_fenjie_name"].text.text = ""
		self.node_list["rune_fenjie_attr"].text.text = ""
	end
	self:FlushFenJieSmallCellHL()
end

function MingWenFenJieSmallCell:FlushFenJieSmallCellHL()
	if not IsEmptyTable(self.data) then
		local is_select = MingWenWGData.Instance:CheckIndexIsSelect(self.data.index)
		self.node_list["rune_fenjie_hl"]:SetActive(is_select)
		self.node_list["normal_bg"]:SetActive(not is_select)
	else
		self.node_list["rune_fenjie_hl"]:SetActive(false)
	end
end

function MingWenFenJieSmallCell:OnClickFenJieSmallCell()
	if not IsEmptyTable(self.data) then
		self.select_call_back(self.data.index)
	end
end

function MingWenFenJieSmallCell:OnPlayerFenJieEffect()
	local is_doing,id = MingWenWGData.Instance:CheckIsDoingFenjie()
	if is_doing then
		if not IsEmptyTable(self.data) then
			local is_select = MingWenWGData.Instance:CheckIndexIsSelect(self.data.index)
			if is_select then
				local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_fenjie1)
				local call_back = function ()
					MingWenWGData.Instance:SetDoingFenJie(false)
				end
				EffectManager.Instance:PlayAtTransform(bundle_name, asset_name,self.node_list["rune_fenjie_effect"].transform,0.3,nil,nil,nil,nil,call_back)
				local bossfluah_animator = self.node_list.rune_fenjie_panel.rect:DOScale(Vector3(0, 0, 0), 0.3)
				bossfluah_animator:OnComplete(function ()
					self.node_list["rune_fenjie_panel"]:SetActive(false)
					self.node_list.rune_fenjie_panel.rect.localScale = Vector3(1,1,1)
				end)

				self:CancelTween()
				local sequence = DG.Tweening.DOTween.Sequence()
				sequence:Join(bossfluah_animator)
			end
		end
	end
end


function MingWenFenJieSmallCell:CancelTween()
    if self.bossfluah_tween then
        self.bossfluah_tween:Kill()
        self.bossfluah_tween = nil
    end
end