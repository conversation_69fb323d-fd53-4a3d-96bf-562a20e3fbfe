function Field1v1View:Delete3V3()
	if self.mate_pvp_list ~= nil then
		for k,v in pairs(self.mate_pvp_list) do
			v:DeleteMe()
		end
		self.mate_pvp_list = nil
	end

	if self.alert_window then
  		self.alert_window:DeleteMe()
   		self.alert_window = nil
    end
    if self.confirm_buy_alert_pvp  then
    	self.confirm_buy_alert_pvp:DeleteMe()
    	self.confirm_buy_alert_pvp = nil
    end
	if self.pvp_rewarditem_cell_list then
		for k,v in pairs(self.pvp_rewarditem_cell_list) do
			v:DeleteMe()
		end
	end

	if self.pvp_activity_notify then
		ActivityWGData.Instance:UnNotifyActChangeCallback(self.pvp_activity_notify)
		self.pvp_activity_notify = nil
	end

	if self.team_info_event then
        GlobalEventSystem:UnBind(self.team_info_event)
        self.team_info_event = nil
    end

	self:CancelTweenList()
	self.pvp_rewarditem_cell_list = nil
	self.act_change_callback = nil
	self.role_id_list = {0,0,0}
end

--设置任务进度条长度
function Field1v1View:SetProgressVal(val)
	if self.node_list.prog_upstar_progress then
		self.node_list.prog_upstar_progress.slider.value = val
	end
end

--设置积分
function Field1v1View:SetIntegral( num )
	if self.node_list.lbl_pvp_jifen then
		self.node_list.lbl_pvp_jifen.text.text = num
	end
end

function Field1v1View:SetMatchBtnFalsePVP()
	if self.node_list.btn_matching_3v3 then
	XUI.SetButtonEnabled(self.node_list.btn_matching_3v3,false)
end
end
--初始化3V3
function Field1v1View:InitTeamEquipView()
	self.info_view = self.node_list.layout_3v3
	local scene_type = Scene.Instance:GetSceneType()
	-- if scene_type == SceneType.Common then
		XUI.AddClickEventListener(self.node_list.btn_matching_3v3, BindTool.Bind1(self.MatchingHandlerPvP, self))
	-- end
	XUI.AddClickEventListener(self.node_list.btn_time, BindTool.Bind1(self.MatchingTimeHandler, self)) 		
	XUI.AddClickEventListener(self.node_list.btn_pvp_tips, BindTool.Bind1(self.OpenRulePVP, self))

	XUI.AddClickEventListener(self.node_list.btn_season_reward3, BindTool.Bind(self.OpenPvPAimView, self, KFPVP_TYPE.MORE))
	XUI.AddClickEventListener(self.node_list.btn_hero3, BindTool.Bind(self.OpenPvPRankView, self, KFPVP_TYPE.MORE))
	--XUI.AddClickEventListener(self.node_list.btn_gongxun_reward3, BindTool.Bind(self.OpenPvPGongXunView, self, KFPVP_TYPE.MORE))
	XUI.AddClickEventListener(self.node_list.img_ring3, BindTool.Bind(self.OnOnClickWZCardCallBack, self)) 
	self.node_list["img_rank3"].button:AddClickListener(BindTool.Bind(self.OpenRankScoreView, self, KFPVP_TYPE.MORE))
	--self.node_list.btn_buy_times.button:AddClickListener(BindTool.Bind(self.OpenConfirmAlert,self))
	XUI.AddClickEventListener(self.node_list.pvp_btn_buy_times, BindTool.Bind(self.OpenConfirmAlertPVP, self, KFPVP_TYPE.MORE))
	for i=1,3 do
		XUI.AddClickEventListener(self.node_list["pvp_reward" .. i], BindTool.Bind(self.OnClickPvpTerraceCallBack, self,i)) 
	end

	self.act_change_callback = BindTool.Bind1(self.UpdataActState, self)

	--奖励物品格子,
	if not self.pvp_rewarditem_cell_list then
		self.pvp_rewarditem_cell_list = {}
		-- for i = 1, 3 do
		-- 	self.pvp_rewarditem_cell_list[i] = ItemCell.New(self.node_list["pvp_reward" .. i])
		-- 	self.pvp_rewarditem_cell_list[i]:SetClickCallBack(BindTool.Bind2(self.OnClickPvpTerraceCallBack, self,i))
		-- end
	end

	if not self.pvp_activity_notify then
		self.pvp_activity_notify = BindTool.Bind1(self.UpdateBtnEnable, self)
		ActivityWGData.Instance:NotifyActChangeCallback(self.pvp_activity_notify)
	end	

	--self.team_info_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind1(self.OnFlushPVPTeamEquipView, self)) --队伍信息发生改变
	self.team_info_event = GlobalEventSystem:Bind(OtherEventType.TEAM_INFO_CHANGE, BindTool.Bind1(self.UpDateTeamInfo, self)) --队伍信息发生改变
end
function Field1v1View:UpDateTeamInfo()
	KuafuPVPWGCtrl.Instance:SendCrossMultiuserChallengeGetBaseSelfSideInfo()
end
--至尊之令
function Field1v1View:OnOnClickWZCardCallBack()
	KuafuOnevoneWGCtrl.Instance:OpenWZCardView()
end
function Field1v1View:OpenConfirmAlertPVP()
	local max_buy_count,cost_num  =  KuafuPVPWGData.GetBuyTimeCost()
	-- local act_info = KuafuPVPWGData.Instance:GetActivityInfo() 
	-- if act_info.total_buy_count >= max_buy_count then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.PKBuyCountMax)
	-- 	return
	-- end
	if nil == self.confirm_buy_alert_pvp then
		self.confirm_buy_alert_pvp = Alert.New()
		self.confirm_buy_alert_pvp:SetCheckBoxText(Language.Common.DontTip)
		
		self.confirm_buy_alert_pvp:SetLableString(string.format(Language.Kuafu1V1.BuyChallengeTimesPVP,cost_num))
		self.confirm_buy_alert_pvp:SetOkFunc(BindTool.Bind(self.ClickToBuyTimesPVP, self))
		self.confirm_buy_alert_pvp:SetShowCheckBox(true)
	end
	self.confirm_buy_alert_pvp:Open()
end

function Field1v1View:ClickToBuyTimesPVP()
	if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_PVP) then
		KuafuPVPWGCtrl.Instance:SendCrossPVPInfo(CROSS_3V3OPERA_TYPE.CROSS_3V3OPERA_TYPE_BUY_TIMES)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.GUILDJIUHUINOOPEN)
	end

end
--点击奖励格子
function Field1v1View:OnClickPvpTerraceCallBack(index)
	local i = index
	local act_info = KuafuPVPWGData.Instance:GetActivityInfo() 
	local count_now = act_info.today_match_count
	local count = 5 * (i - 1) > 0 and 5 * (i - 1) or 1
		if KuafuPVPWGData.Instance:GetPvPJionTimesRewardIsGet(i) ~= 1 then
			
			if count_now >= count then
				KuafuPVPWGCtrl.Instance:SendCrossMultiuserChallengeFetchDaycountReward(index-1)
				return 
			end
		end
	local cfg = KuafuPVPWGData.Instance:GetRewardCfg()
	local data = cfg[index]
	TipWGCtrl.Instance:OpenItem({item_id = data.reward_display}, ItemTip.FROM_NORMAL, nil)
end


-- --更新：人物模型-区号-名字-战力
-- function Field1v1View:UpdateMateItemList()
-- 	--TODO
-- 	local mate_list = KuafuPVPWGData.Instance:GetMatesInfo()
-- 	--print_error("组队变化刷新界面",mate_list)
-- 	for i=1, 3 do
-- 		if mate_list[i] then
-- 			self:FlushPVPMateList(i,mate_list[i])
-- 		else
-- 			self:FlushPVPMateList(i,nil)
-- 		end
-- 	end
-- end


-- function Field1v1View:FlushPVPMateList(index,data)
-- 	if data == nil then
-- 		self.node_list["lbl_plat_sever" .. index].text.text = Language.KuafuPVP.NoMate 			--区服：1服
-- 		self.node_list["lbl_name" .. index].text.text = Language.KuafuPVP.NoMate				--玩家名字：狗子狗子狗子 人物职业：self.data.prof
-- 		self.node_list["lbl_cap" .. index].text.text = 0										--战斗力
-- 		self.node_list["lbl_duanwei" .. index].text.text = Language.KuafuPVP.NoMate 			--段位：倔强青铜V
-- 		self.node_list["img_role_dec" .. index]:SetActive(true) 								--剪影
-- 		if self.node_list["ph_display" .. index] then													
-- 			self.node_list["ph_display" .. index]:SetActive(false)								--人物模型
-- 		end
-- 	else
-- 		self.node_list["img_role_dec" .. index]:SetActive(false) 								--剪影

-- 		if self.node_list["ph_display" .. index] then													
-- 			self.node_list["ph_display" .. index]:SetActive(true)								--人物模型
-- 		end

-- 		local ps_str = data.server_id .. Language.Login.Fu
-- 		if RoleWGData.Instance.role_vo.plat_type ~= data.plat_type then
-- 			ps_str = Language.Common.WaiYu .. "-" .. ps_str
-- 		end

-- 		self.node_list["lbl_plat_sever" .. index].text.text = ps_str
-- 		self.node_list["lbl_name" .. index].text.text = data.user_name
-- 		self.node_list["lbl_cap" .. index].text.text = data.capability
-- 		local reward_id = KuafuPVPWGData.Instance:GetRewardBaseCell(data.challenge_score)
-- 		self.node_list["lbl_duanwei" .. index].text.text = reward_id.name
-- 	end
-- end

--刷新界面
--  local join_time = reward_cfg[1].jion_times 计算时间

--匹配按钮是否可用
function Field1v1View:UpdateBtnEnable()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_PVP)
	XUI.SetGraphicGrey(self.node_list.btn_matching_3v3,not (activity_info and ACTIVITY_STATUS.OPEN == activity_info.status))
end

function Field1v1View:OnFlushPVPTeamEquipView()
	-- self:UpdateMateItemList()
	self:UpdateTitleBar()
	self:RewardItemcell()
	self:UpdateBtnLbl()
	self:UpdateOther()
	self:UpdateBtnEnable()
	self:UpdatePVPRed()
end

function Field1v1View:UpdatePVPRed()
	--self.node_list["btn_gongxun_red3"]:SetActive(KuafuPVPWGData.Instance:RemindKFPvP() == 1)
end

--更新其他--奖励进度，剩余战斗次数，积分
function Field1v1View:UpdateOther()
	local act_info = KuafuPVPWGData.Instance:GetActivityInfo() 
	local ji_fen = KuafuPVPWGData.Instance:GetRewardIntegral()	
	self:SetIntegral(ji_fen)
	self:SetProgressVal(act_info.today_match_count / 10)
	local max_buy_count,_, free_count =  KuafuPVPWGData.GetBuyTimeCost()
	local content = free_count + act_info.total_buy_count  - act_info.today_reward_count 
	content = content > 0 and content or 0
	self.node_list.rich_pass_count.text.text = content --剩余战斗次数
	self.node_list.txt_reward_times3.text.text = string.format(Language.Field1v1.JoinTimes, act_info.today_match_count)

	
	self.node_list.add_btn_effect_pvp:SetActive(max_buy_count > act_info.total_buy_count )


	self.node_list["lbl_winstreak3"].text.text = act_info.challenge_mvp_count
	self.node_list["lbl_myfrequency3"].text.text = act_info.challenge_total_match_count
	self.node_list["lbl_mywinrate3"].text.text = act_info.win_percent .. "%"

	local should_select_season = act_info.season_count
	if act_info.season_count > 12 then
		should_select_season = 12 - (act_info.season_count - 12) % 2
	end
	self.node_list.img_ring3.image:LoadSprite(ResPath.GetKf3V3Old("img_pic" .. should_select_season))
end

--设置进度槽
function Field1v1View:SetTitlePVPBar(num)
	self.node_list.ph_title_pvp.image.fillAmount = num
end

--更新当前段位的进度槽 
function Field1v1View:UpdateTitleBar()
	local ji_fen = KuafuPVPWGData.Instance:GetRewardIntegral()
	local reward_id = KuafuPVPWGData.Instance:GetRewardBaseCell(ji_fen)
	-- reward_id.name
	self.node_list.img_duanwei_pvp.text.text = reward_id.name --段位：如 倔强青铜
	local seq = math.floor(reward_id.seq / 5) + 1
	self.node_list.img_rank3.image:LoadSprite(ResPath.GetKf1V1("Image_YiZhan_Grade" .. seq))
	--TODO
	local prog = KuafuPVPWGData.Instance:SetProgLevel(reward_id.grade)
	local prog_num = math.ceil(((ji_fen - reward_id.score) / (prog.score - reward_id.score)) * 100) or 0
	local cfg_num =  KuafuPVPWGData.Instance:GetGridNum() - 1 
	if reward_id.grade >= cfg_num then
		self:SetTitlePVPBar(1)
	else
		--self:SetTitlePVPBar((ji_fen - reward_id.score) / (prog.score - reward_id.score))
		local num = ji_fen / prog.score
		self:SetTitlePVPBar(num)
	end
	self.node_list["slider_text3"].text.text = ji_fen .. "/" .. prog.score
end

-- 更新组队按钮的文字状态：--"匹配对手"或"取消匹配"
function Field1v1View:UpdateBtnLbl()
    local match_info = KuafuPVPWGData.Instance:GetMatchStateInfo()
--print_error(match_info)
	if match_info.matching_state < 0 or match_info.matching_state == 3 then
		self.node_list.lbl_3v3.text.text = Language.KuafuPVP.MatchBtnTxt[1]
		-- self.node_list.pi_pei_zhong:SetActive(false)
		ViewManager.Instance:Close(GuideModuleName.KfOneVOneMatch)
		-- ViewManager.Instance:Open()
	else
		self.node_list.lbl_3v3.text.text = Language.KuafuPVP.MatchBtnTxt[2]
		-- self.node_list.pi_pei_zhong:SetActive(true)
		-- Field1v1WGData.Instance:SetCurMatchFlag(KFPVP_TYPE.MORE)
		-- ViewManager.Instance:Open(GuideModuleName.KfOneVOneMatch)
		-- ViewManager.Instance:FlushView(GuideModuleName.KfOneVOneMatch)
	end
end

--更新奖励格子数据
function Field1v1View:RewardItemcell()
	local reward_cfg = KuafuPVPWGData.Instance:GetRewardCfg()
	
	for i =  1 ,#reward_cfg do
		if KuafuPVPWGData.Instance:GetPvPJionTimesRewardIsGet(i)  == 1 then
			local b,a = ResPath.GetKf1V1("kf_1v1_reward_open")
			self.node_list["pvp_reward"..i].image:LoadSprite(b,a,function()
						XUI.ImageSetNativeSize(self.node_list["pvp_reward"..i])
					end)
			self.node_list["ph_reward_red_point_"..i]:SetActive(false)
			self.node_list["pvp_effect_"..i]:SetActive(false)
			self.node_list["ph_reward_group"..i].animator:SetBool("is_full",false)
		else
			local b,a = ResPath.GetCommonIcon("liuji_xiangzi")
			self.node_list["pvp_reward"..i].image:LoadSprite(b,a,function()
						XUI.ImageSetNativeSize(self.node_list["pvp_reward"..i])
					end)
			local act_info = KuafuPVPWGData.Instance:GetActivityInfo() 
			local count_now = act_info.today_match_count
			
			if KuafuPVPWGData.Instance:GetPvPJionTimesRewardIsGet(i) ~= 1 then
				local count = 5 * (i - 1) > 0 and 5 * (i - 1) or 1
				if count_now >= count then
					--self.node_list["ph_reward_red_point_"..i]:SetActive(true)
					self.node_list["pvp_effect_"..i]:SetActive(true)
					self.node_list["ph_reward_group"..i].animator:SetBool("is_full",true)
				else
					self.node_list["ph_reward_red_point_"..i]:SetActive(false)
					self.node_list["pvp_effect_"..i]:SetActive(false)
					self.node_list["ph_reward_group"..i].animator:SetBool("is_full",false)
				end
			end
			

		end
		--self.node_list["pvp_reward"..i].image:SetNativeSize()
	end

	-- --物品格子特效   --TODO
end

function Field1v1View:MatchingHandlerPvP()
	local match_info = KuafuPVPWGData.Instance:GetMatchStateInfo()
	if  match_info.matching_state >= 0 and match_info.matching_state ~= 3 then
		-- KuafuPVPWGData.Instance:SetMatesInfo({})
		if SocietyWGData.Instance:GetIsInTeam() == 1 and SocietyWGData.Instance:GetIsTeamLeader() ~= 1 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Field1v1.CancelMatchError)
			return
		end
		KuafuPVPWGCtrl.Instance:SendCrossMultiuerChallengeCancelMatching()
		return
	end

	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.KF_PVP)  then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ActivateNoOpen)
		return
	end
	
	local mate_list = KuafuPVPWGData.Instance:GetMatesInfo()
	if nil == self.alert_window then
		self.alert_window = Alert.New()
	end
	if #mate_list > 1 then
		if SocietyWGData.Instance:GetTeamMemberCount() > 3 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.KuafuPVP.MaxTeammateTips)
			return
		end
		local team_mate = ""
		for k,v in pairs(mate_list) do
			local role_id = RoleWGData.Instance.role_vo.role_id
			if v.uid ~= role_id then
				team_mate = team_mate .. "    " .. v.user_name .. " " .. v.level .. Language.Common.Ji .. "\n"
			end
		end
		self.alert_window:SetLableString(string.format(Language.KuafuPVP.TeamMatch, team_mate))
	else
		self.alert_window:SetLableString(Language.KuafuPVP.SelfMatch)
	end
	self.alert_window:SetOkFunc(BindTool.Bind1(self.SendMatchgingReq, self))
	self.alert_window:Open()
	-- self:OnFlushPVPTeamEquipView()
end

----组队
function Field1v1View:MatchingTimeHandler()
     ViewManager.Instance:Open(GuideModuleName.NewTeamView)
end

function Field1v1View:SendMatchgingReq()
	KuafuPVPWGCtrl.Instance:SendCrossMultiuserChallengeMatchgingReq()
end

function Field1v1View:OpenRulePVP()
	RuleTip.Instance:SetContent(Language.KuafuPVP.KfPvPAfterTips, Language.KuafuPVP.KfPvPTips)
end
function Field1v1View:StartBtnTween(index)
	local btn_tween = function (trans)
		local sequence = DG.Tweening.DOTween.Sequence()
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, -60),0.2):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:Append(trans:DOLocalRotate(Vector3(0, 0, 60),0.4):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:Append(trans:DOLocalRotate(Vector3(0,0,0),0.2):SetEase(DG.Tweening.Ease.InOutQuad))
		sequence:AppendInterval(1)
		return sequence
	end

	self:CancelTweenList()
	self.btn_tween_list["btn_efficiency"] = btn_tween(self.node_list.btn_efficiency.transform):SetLoops(1000)
	self.btn_tween_list["btn_inspire"] = btn_tween(self.node_list.btn_inspire.transform):SetLoops(1000)

	if self.is_linghunguanchang then
		self.btn_tween_list["btn_double_linghun"] = btn_tween(self.node_list.btn_double_linghun.transform):SetLoops(1000)
	end
end

function Field1v1View:FlushAction(index)
	for i = 1,3 do
		if self.btn_tween_list["pvp_reward"..index] then
			self.btn_tween_list["pvp_reward"..index]:Complete()
		end
	end
end

function Field1v1View:CancelTweenList()
	if self.btn_tween_list then
		for k, v in pairs(self.btn_tween_list) do
			if v then
				v:Kill()
			end
		end
	end

	self.btn_tween_list = nil
end

----------KfPVPMateItem--------------
--显示人物
KfPVPDisplayMateItemRender = KfPVPDisplayMateItemRender or BaseClass(BaseRender)
function KfPVPDisplayMateItemRender:__init()
end

function KfPVPDisplayMateItemRender:LoadCallBack()

end

function KfPVPDisplayMateItemRender:__delete()

end

--self.node_list 会变为空
function KfPVPDisplayMateItemRender:SetNodeList( node_list )
	if (not self.node_list) and node_list then
		self.node_list = node_list
	end
end

function KfPVPDisplayMateItemRender:OnFlush()
	if not self.data then return end
	if not self.node_list then return end
	
	if self.data.no_mate then
		self.node_list.lbl_plat_sever.text.text = Language.KuafuPVP.NoMate 			--区服：1服
		self.node_list.lbl_name.text.text = Language.KuafuPVP.NoMate				--玩家名字： 人物职业：self.data.prof
		self.node_list.lbl_cap.text.text = 0										--战斗力
		self.node_list.lbl_duanwei.text.text = Language.KuafuPVP.NoMate 			--段位：
		self.node_list.img_role_dec:SetActive(true) 								--剪影
		if self.node_list["ph_display"] then													
			self.node_list["ph_display"]:SetActive(false)							--人物模型
		end
	else
		self.node_list.img_role_dec:SetActive(false) 								
		
		local ps_str = self.data.server_id .. Language.Login.Fu
		if RoleWGData.Instance.role_vo.plat_type ~= self.data.plat_type then
			ps_str = Language.Common.WaiYu .. "-" .. ps_str
		end

		self.node_list.lbl_plat_sever.text.text = ps_str
		self.node_list.lbl_name.text.text = self.data.user_name
		self.node_list.lbl_cap.text.text = self.data.capability
		local reward_id = KuafuPVPWGData.Instance:GetRewardBaseCell(self.data.challenge_score)
		self.node_list.lbl_duanwei.text.text = reward_id.name
	end
end

function KfPVPDisplayMateItemRender:RoleInfoVo(role_vo)
	if not self.node_list then
		print_error("self.node_list is a nil value")
	 	return
	end
	if nil == role_vo then
		return
	end

	if self.data.no_mate then
		self.node_list.img_role_dec:SetActive(true)
		self.node_list["ph_display"]:SetActive(false)
	else
		self.node_list.img_role_dec:SetActive(false)
		self.node_list["ph_display"]:SetActive(true)
	end
end
