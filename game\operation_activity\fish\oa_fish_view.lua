local OA_FISH_STR = "oa_fish_str"
OperationActivityView.FISH_FANLI_SHOW_NUM = 5
local CELL_DIF_Y = {10, 75, 130}
local CELL_DIF_Y2 = {-10, -150, -75}
local Fish_Tween_List = {}
local TurnTable_Num = 8
local TurnTable_TurnCount = 2
local TurnTable_Arrow = -1

function OperationActivityView:ReleaseFishView()
    if self.fish_mid_cell_list then
        for i, v in pairs(self.fish_mid_cell_list) do
            v:DeleteMe()
        end
        self.fish_mid_cell_list = {}
    end

    if self.fish_both_cell_list then
        for i, v in pairs(self.fish_both_cell_list) do
            v:DeleteMe()
        end
        self.fish_both_cell_list = {}
    end

    if self.fish_baodi_list then
        for i, v in pairs(self.fish_baodi_list) do
            v:DeleteMe()
        end
        self.fish_baodi_list = nil
    end

    if self.turntable_itemcell_list then
        for i, v in pairs(self.turntable_itemcell_list) do
            v:DeleteMe()
        end
        self.turntable_itemcell_list = {}
    end

    if self.fish_cell_anim then
        GlobalTimerQuest:CancelQuest(self.fish_cell_anim)
        self.fish_cell_anim = nil
    end

    if self.fish_zhenxi_list then
        self.fish_zhenxi_list:DeleteMe()
        self.fish_zhenxi_list = nil
    end

    if self.fanli_list then
        self.fanli_list:DeleteMe()
        self.fanli_list = nil
    end

    if self.pao_rotate_tween then
        self.pao_rotate_tween:Kill()
        self.pao_rotate_tween = nil
    end

    if CountDownManager.Instance:HasCountDown(OA_FISH_STR) then
        CountDownManager.Instance:RemoveCountDown(OA_FISH_STR)
    end
    self.is_dofish_pao_tween = false

    self:RemoveDelayTimer()

    -- ItemWGData.Instance:UnNotifyDataChangeCallBack(self.fish_item_change)
    -- self.fish_item_change = nil

    self.fish_ignore_anim = nil
    self.fish_notice_tween = nil
    self.fish_is_draw_tweening = nil
    self.fish_notice_list = {}
    self.cache_both_cell_list = {}
end

function OperationActivityView:LoadFishView()
    self.fish_mid_cell_list = {}
    self.fish_both_cell_list = {}
    self.fish_notice_list = {}
    self.cache_both_cell_list = {}

    self.node_list["fish_anim_check"].button:AddClickListener(BindTool.Bind(self.OnClickFishAnim, self))
    self.node_list["fish_record"].button:AddClickListener(BindTool.Bind(self.OnClickFishRecord, self))
    self.node_list["fish_rule_btn"].button:AddClickListener(BindTool.Bind(self.OnClickFishRule, self))
    self.node_list["fiish_gailv"].button:AddClickListener(BindTool.Bind(self.OnClickFishGaiLv, self))
    self.node_list["fiish_yulan"].button:AddClickListener(BindTool.Bind(self.OnClickFishRewardPreview, self))
    
    self:FlushFishStyle()
    self:FlushAllFishInfo(true)
    self:LoadTurnTableReward()

    -- if nil == self.fish_item_change then
    --     self.fish_item_change = BindTool.Bind(self.FishItemChange,self)
    --     ItemWGData.Instance:NotifyDataChangeCallBack(self.fish_item_change)
    -- end
end

function OperationActivityView:LoadTurnTableReward()
    local data_list = OAFishWGData.Instance:GetTurnTableReward()
    local turntable_list = {}
    for i = 1, TurnTable_Num do
        turntable_list[i] =  ItemCell.New(self.node_list["turntable_item_"..i])
        turntable_list[i]:SetData(data_list[i].reward_item[0])
    end
    self.turntable_itemcell_list = turntable_list
end

function OperationActivityView:FlushAllFishInfo(is_load)
    local grade_cfg, has_next, end_time = OAFishWGData.Instance:GetCurActGradeCfg()
    self:LoadFishBtnShow(grade_cfg)
    self:FlushFishAnimCheck()
    self:FlushFishFanli(grade_cfg, is_load)
    self:FlushFishCell(grade_cfg)
    self:FlushFishTime(has_next, end_time)
end

function OperationActivityView:LoadFishBtnShow(grade_cfg)
    local btn_list = OAFishWGData.Instance:GetCurConsumeCfg(grade_cfg)
    if not btn_list then
        return
    end

    local show_discount
    local is_enough, color
    local has_num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.consume_item)
    local cfg = ItemWGData.Instance:GetItemConfig(grade_cfg.consume_item)
    if IsEmptyTable(cfg) then
        print_error("取不到物品配置，item_id = ",grade_cfg.consume_item)

    end
    local bundle, asset = ResPath.GetItem(cfg.icon_id)
    local gold = RoleWGData.Instance:GetRoleVo().gold
    local need_count
    local sp_guarantee_x,sp_guarantee_n,sp_enter_num = OAFishWGData.Instance:GetSpGuarantee()
    local reward_pool_id = grade_cfg.reward
    local sp_max_num = OAFishWGData.Instance:GetGuaranteeListCount(reward_pool_id)

    for i = 1, 3 do
        if btn_list[i] then
            need_count = nil
            if grade_cfg.sp_guarantee_finish == 1 and sp_guarantee_n >= (grade_cfg.sp_guarantee_n - 1) and
                sp_enter_num == (sp_max_num - 1) and grade_cfg.sp_guarantee_x - sp_guarantee_x < btn_list[i].consume_count then
                need_count = grade_cfg.sp_guarantee_x - sp_guarantee_x
            end

            self.node_list["fish_btn_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickFishBtn, self, btn_list[i], i))
            self.node_list["fish_btn_txt_" .. i].text.text = btn_list[i].lotto_btn
            need_count = need_count or btn_list[i].consume_count
            is_enough = has_num >= btn_list[i].consume_count or has_num >= need_count
            --2020/04/25 22:35 策划需求去掉钱够显示红点
            -- gold_enough = gold >= ((btn_list[i].consume_count - has_num) * grade_cfg.complement_num)
            self.node_list["fish_btn_red_" .. i]:SetActive(is_enough)

            color = is_enough and COLOR3B.C8 or COLOR3B.C10
            local has_num_str = ToColorStr(has_num, color)
            if need_count < btn_list[i].onekey_lotto_num then
                self.node_list["fish_btn_cost_" .. i].text.text = has_num_str .. "/" .. need_count
            else
                self.node_list["fish_btn_cost_" .. i].text.text = has_num_str .. "/" .. btn_list[i].consume_count
            end
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["fish_btn_cost_" .. i].rect)

            self.node_list["fish_cost_icon_" .. i].image:LoadSprite(bundle, asset, function()
                self.node_list["fish_cost_icon_" .. i].image:SetNativeSize()
            end)
            self.node_list["fish_cost_icon_" .. i].button:AddClickListener(BindTool.Bind(self.OnClickFishCostBtn, self, grade_cfg.consume_item))

            show_discount = btn_list[i].discount_text ~= ""
            self.node_list["fish_btn_discount_" .. i]:SetActive(show_discount)
            if show_discount then
                self.node_list["fish_txt_discount_" .. i].text.text = btn_list[i].discount_text
            end
        end
        self.node_list["fish_btn_" .. i]:SetActive(btn_list[i] ~= nil)
    end
end

--刷新动画显示状态
function OperationActivityView:FlushFishAnimCheck()
    local no_tween = OAFishWGData.Instance:GetNoTweenFlag() == 1
	self.node_list["fish_anim_yes"]:SetActive(no_tween)
	self.fish_ignore_anim = no_tween
end

--ruletip边上的小字
function OperationActivityView:FlushFishTipText()
    local grade_cfg = OAFishWGData.Instance:GetCurActTimeCfg()
    local interface_cfg = OAFishWGData.Instance:GetCurInterfaceCfg(grade_cfg) or {}
    self:SetOutsideRuleTips(interface_cfg.rule_1 or "") 

    local grade_cfg = OAFishWGData.Instance:GetCurActGradeCfg()
    local btn_list = OAFishWGData.Instance:GetCurConsumeCfg(grade_cfg)
    if not btn_list then
        return
    end

    local show_discount = false
    local sp_guarantee_x,sp_guarantee_n,sp_enter_num = OAFishWGData.Instance:GetSpGuarantee()
    local reward_pool_id = grade_cfg.reward
    local sp_max_num = OAFishWGData.Instance:GetGuaranteeListCount(reward_pool_id)
    local send_num = grade_cfg.sp_guarantee_x - sp_guarantee_x

    for i = 1, 3 do
        if btn_list[i] then   
            show_discount = btn_list[i].discount_text ~= ""
            self.node_list["fish_btn_discount_" .. i]:SetActive(show_discount)
            
            if grade_cfg and grade_cfg.sp_guarantee_n > 0 then
                if grade_cfg.sp_guarantee_finish == 1 and sp_guarantee_n >= (grade_cfg.sp_guarantee_n - 1) and sp_enter_num == (sp_max_num - 1) then
                    if send_num < btn_list[i].onekey_lotto_num then
                        local str = string.format(Language.OAFish.CI_1, send_num)                        
                        self.node_list["fish_btn_txt_" .. i].text.text = str
                        self.node_list["fish_btn_discount_" .. i]:SetActive(false)
                    else
                        self.node_list["fish_btn_txt_" .. i].text.text = btn_list[i].lotto_btn
                    end                
                else
                    self.node_list["fish_btn_txt_" .. i].text.text = btn_list[i].lotto_btn
                end
            else
                self.node_list["fish_btn_txt_" .. i].text.text = btn_list[i].lotto_btn
            end
        end
    end

    if grade_cfg and grade_cfg.sp_guarantee_finish == 1 and grade_cfg.sp_guarantee_n > 0 and
        sp_guarantee_n >= (grade_cfg.sp_guarantee_n - 1) and sp_enter_num == (sp_max_num - 1) and send_num < 50 then
        local str = string.format(Language.OAFish.CI_3,send_num)
        self:SetOutsideRuleTips(str) 
    elseif grade_cfg and grade_cfg.sp_guarantee_finish == 1 and grade_cfg.sp_guarantee_n > 0 and
        sp_guarantee_n >= grade_cfg.sp_guarantee_n then
        local str = string.format(Language.OAFish.CI_3,0)
        self:SetOutsideRuleTips(str) 
    end
end

--刷新主题图片
function OperationActivityView:FlushFishStyle()
    local grade_cfg = OAFishWGData.Instance:GetCurActTimeCfg()
    local interface_cfg = OAFishWGData.Instance:GetCurInterfaceCfg(grade_cfg)

    if not interface_cfg then
        return
    end

end

--刷新返利部分
function OperationActivityView:FlushFishFanli(grade_cfg, is_load)

    if not self.fanli_list then
        self.fanli_list = AsyncListView.New(FanliItemRender, self.node_list.fanli_list)
    end
    local fanli_list = OAFishWGData.Instance:GetFanliList(grade_cfg)
    self.fanli_list:SetDataList(fanli_list)
    local draw_num = OAFishWGData.Instance:GetCurDrawNum()
    self.node_list["fanli_title"].text.text = string.format(Language.OAFish.FanliTime, draw_num)
    self:FlushBaoDiInfo()
end

-- 刷新保底库
function OperationActivityView:FlushBaoDiInfo()
    local grade_cfg = OAFishWGData.Instance:GetCurActGradeCfg()
    local sp_guarantee_x,sp_guarantee_n,sp_enter_num = OAFishWGData.Instance:GetSpGuarantee()

    if not grade_cfg or grade_cfg.sp_guarantee_n <= 0 then
        self.node_list.fish_layout_bide:SetActive(false)
        return
    end

    if grade_cfg and grade_cfg.sp_guarantee_n > 0 then
        if grade_cfg.sp_guarantee_finish == 1 and sp_guarantee_n >= grade_cfg.sp_guarantee_n then
            --鱼抓完了提示
            self.node_list.fish_layout_bide:SetActive(false)
        else
            self.node_list.fish_layout_bide:SetActive(true)
        end
    end

    local cur_num = OAFishWGData.Instance:GetCurDrawNum()
    local need_num = grade_cfg.sp_guarantee_x - cur_num % grade_cfg.sp_guarantee_x
    self.node_list["fish_bide_count_1"].text.text = string.format(Language.OAFish.TurnAgain, need_num)

    local reward = grade_cfg.reward
    local data_list = OAFishWGData.Instance:GetGuaranteeList(reward)

    if not self.fish_baodi_list then
        self.fish_baodi_list = {}
        for i=1,12 do
            self.fish_baodi_list[i] = ItemCell.New(self.node_list.fish_bide_list)
        end
    end
    local index = 0
    for i,v in ipairs(self.fish_baodi_list) do
        if data_list[i] then
            v:SetVisible(true)
            v:SetData(data_list[i])
            index = i
        else
            v:SetVisible(false)
        end
    end
    local spacing = (index - 1) * 10
    self.node_list.fish_bide_list.rect.sizeDelta = Vector2(index * COMMON_CONSTS.ItemCellSize + spacing, 100)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.fish_bide_list.rect)
    self.node_list.baodi_scroll.scroll_rect.horizontalNormalizedPosition = 0
end

function OperationActivityView:FlushFishCell(grade_cfg)
    local mid_cell_list, both_cell_list = OAFishWGData.Instance:GetCellList(grade_cfg)
    if not self.fish_zhenxi_list then
        self.fish_zhenxi_list = AsyncListView.New(FishZhenXiCell, self.node_list.fish_zhenxi_list)
    end
    self.fish_zhenxi_list:SetDataList(mid_cell_list)
end

function OperationActivityView:PlayBothCellAnim(cell, data, pos_count, high, i)
    cell:SetData(data)
    cell:ResetScale()
    local tb = i == 1 and CELL_DIF_Y or CELL_DIF_Y2
    local offset = i == 1 and 0 or -60
    cell:SetAnimPos(high/2 + offset, tb[pos_count + 1])
    cell:StartAnim(high, i)
end

function OperationActivityView:FlushFishRecord()
    local num = OAFishWGData.Instance:GetNewRecordCount()
	if num > 0 then
		self.node_list["fish_record_show"]:SetActive(false)
		self.node_list["fish_record_num"].text.text = num
	else
		self.node_list["fish_record_show"]:SetActive(false)
	end
end

function OperationActivityView:FlushFishTime(has_next, end_time)
    if CountDownManager.Instance:HasCountDown(OA_FISH_STR) then
        CountDownManager.Instance:RemoveCountDown(OA_FISH_STR)
    end

    if end_time == nil then
        return
    end

    if end_time - TimeWGCtrl.Instance:GetServerTime() < 0 then
        return
    end

    CountDownManager.Instance:AddCountDown(OA_FISH_STR, BindTool.Bind(self.UpdateFishTimer, self, has_next),
        BindTool.Bind(self.CompleteFishTime, self, has_next), end_time, nil, 0.3)
end

local shield_pos = Vector2(10000,10000)
function OperationActivityView:ShowIndexFishView(index)
    if index == TabIndex.operation_act_fish then
        self:SetRuleInfoActive(false)
        UITween.CleanAllTween(OAFishWGData.Mark_Tween_Str)

        local draw_num = OAFishWGData.Instance:GetCurDrawNum()
        if draw_num < 0 then
            OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.INFO)
        end

        OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.RECORD)
        self:SetRemainTimePos(shield_pos)
    end
end

function OperationActivityView:CloseFishCallBack()
    if self.pao_rotate_tween then
        self.pao_rotate_tween:Kill()
        self.pao_rotate_tween = nil
    end
    if self.fish_cell_anim then
        GlobalTimerQuest:CancelQuest(self.fish_cell_anim)
        self.fish_cell_anim = nil
    end
end

function OperationActivityView:FlushFishView(info)
    if info.record then
        self:FlushFishRecord()
    end

    if info.flushview then
        self:FlushAllFishInfo()
    end

    if info.draw then
        local count = info.count
        self:StartFishAnim(count)
    end

    if info.normal then
        self:FlushFishFanli()
    end

    if info.param == "set_draw_flag" then
        self.fish_is_draw_tweening = false
    elseif info.param == "oa_get_draw" then
        self:OnClickFishDrawFlag()
    end

    self:FlushFishTipText()
    self:FlushFishAnimCheck()
end

function OperationActivityView:OnClickFanli(index)
    local fanli_list = OAFishWGData.Instance:GetFanliList()
    local draw_num = OAFishWGData.Instance:GetCurDrawNum()
    if fanli_list[index].cfg.lotto_num > draw_num or fanli_list[index].has_receive then
        -- ViewManager.Instance:Open(GuideModuleName.OAFishFanLiView)
        -- 点击弹出道具tips
        local reward_item = fanli_list[index].cfg.reward_item
        TipWGCtrl.Instance:OpenItem(reward_item[0], ItemTip.FROM_NORMAL)
    else
        OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.LEIJI_REWARD, fanli_list[index].cfg.index)
    end
end

function OperationActivityView:OnClickFishCostBtn(item_id)
    if not item_id then
        return
    end 
    TipWGCtrl.Instance:OpenItem({item_id = item_id, num = 1, is_bind = 1},ItemTip.FROM_NORMAL)
end

function OperationActivityView:OnClickFishBtn(cfg, btn_index)
    local grade_cfg = OAFishWGData.Instance:GetCurActGradeCfg()
    if not grade_cfg then
		return
	end
    OAFishWGCtrl.Instance:OnClickBtn(btn_index)
end

function OperationActivityView:OnClickFishDrawFlag()
    self:SetFishDrawFlag(true)
    local time = OAFishWGData.Instance:GetDrawFishAnimTime()
    AddDelayCall(self,function ()
        self:SetFishDrawFlag(false)
    end, time)
end

function OperationActivityView:GetFishDrawFlag()
    return self.fish_is_draw_tweening
end

function OperationActivityView:SetFishDrawFlag(flag)
    self.fish_is_draw_tweening = flag
end

--忽略动画
function OperationActivityView:OnClickFishAnim()
    if self.is_dofish_pao_tween then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.IsDoingTween)
        return
    end
    local fish_ignore_anim = not self.fish_ignore_anim
    OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.NoTween, fish_ignore_anim and 1 or 0)
end

function OperationActivityView:OnClickFishRule()
    local grade_cfg = OAFishWGData.Instance:GetCurActGradeCfg()
	if not grade_cfg then
		return
	end

	local cfg = ActivityWGData.Instance:GetCanLookActivityInfo(ACTIVITY_TYPE.OPERA_ACT_FISH)
	local name = cfg and cfg.active_name or ""
	local desc = grade_cfg.rule_2
    local probility_str = OAFishWGData.Instance:GetItemsProbility()
    desc = desc .. probility_str
    RuleTip.Instance:SetContent(desc, name)
end

function OperationActivityView:OnClickFishRecord()
    -- ViewManager.Instance:Open(GuideModuleName.OAFishRecord)
    OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.RECORD)
    local data_list = OAFishWGData.Instance:GetDrawRecord()
    TipWGCtrl.Instance:OpenTipsRewardRecordView(data_list)
    OATurnTableWGData.Instance:ClearRecordCount()
    self:FlushFishRecord()
end

function OperationActivityView:OnClickFishGaiLv()
    local data_list = OAFishWGData.Instance:GetFishGaiLvList()
    TipWGCtrl.Instance:OpenTipsRewardProView(data_list)
end

function OperationActivityView:OnClickFishRewardPreview()
    local data_list = OAFishWGData.Instance:GetFishRewardPreviewList()
    RewardShowViewWGCtrl.Instance:SetRewardShowData({ reward_item_list = data_list })
end

--抽奖协议返回动画
function OperationActivityView:DoFishLaoTween(count)
    local ani_time = OAFishWGData.Instance:GetNoticeDelayTime()
    local end_angle = self:GetTurnTableRotationAngle()
    self.is_dofish_pao_tween = true
    local fish_pao_tween = self.node_list.oa_fish_point_root.transform:DOLocalRotate(Vector3(0, 0, end_angle * 2), ani_time,DG.Tweening.RotateMode.FastBeyond360)
    fish_pao_tween:SetEase(DG.Tweening.Ease.OutCubic)
    fish_pao_tween:OnComplete(function()
        self:RemoveDelayTimer()
    end)
    self.show_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
        fish_pao_tween:Kill()
        self.node_list.oa_fish_point_root.rect.rotation = Quaternion.Euler(0, 0, 0)

        self.is_dofish_pao_tween = false
        self.fish_is_draw_tweening = false
    end, ani_time - 1.5)
end

--移除回调
function OperationActivityView:RemoveDelayTimer()
    if self.show_delay_timer then
        GlobalTimerQuest:CancelQuest(self.show_delay_timer)
        self.show_delay_timer = nil
    end
end

function OperationActivityView:GetTurnTableRotationAngle()
    local angle = self.node_list.oa_fish_point_root.transform.localEulerAngles.z
    angle = angle + 360 * TurnTable_TurnCount * TurnTable_Arrow
    return angle
end

function OperationActivityView:FreezeFishTween(count)
    local pos_tb = {}
    if not IsEmptyTable(Fish_Tween_List) then
        local index = 0
        for k, v in pairs(Fish_Tween_List) do
            index = index + 1
        end
        if count > index then
            for k, v in pairs(Fish_Tween_List) do
                k:DelayHide()
            end
            return
        end
        for k, v in pairs(Fish_Tween_List) do
            pos_tb[#pos_tb + 1] = k
        end
        table.sort(pos_tb, function(a, b)
            local x1 = a.node_list["anim"].rect.anchoredPosition.x
            local x2 = b.node_list["anim"].rect.anchoredPosition.x
            return math.abs(x1) < math.abs(x2)
        end)
        for k, v in pairs(pos_tb) do
            if k <= count then
                v:DelayHide()
            end
        end
    end
end

OperationActivityView.FishAniTime = 1
function OperationActivityView:StartFishAnim(count)
    if self.fish_ignore_anim then
        self.fish_is_draw_tweening = false
        self:FlushFishAnimEnd()
    else
        self:DoFishLaoTween(count)
        ReDelayCall(self, function()
            self:FlushFishAnimEnd()
        end, OperationActivityView.FishAniTime, "oa_fish_draw_anim")
    end
end

function OperationActivityView:FlushFishAnimEnd()
    self.is_dofish_pao_tween = false
    --self.node_list["fish_show"]:SetActive(true)
    --弹窗奖励，8690？
    local grade_cfg = OAFishWGData.Instance:GetCurActGradeCfg()
    self:FlsuhFishBtnCost(grade_cfg)
    self:FlushFishFanli()
end

function OperationActivityView:FishItemChange()
    if not self:IsLoadedIndex(TabIndex.operation_act_fish) then
		return
	end

    local grade_cfg = OAFishWGData.Instance:GetCurActGradeCfg()
    self:FlsuhFishBtnCost(grade_cfg)
end

function OperationActivityView:FlsuhFishBtnCost(grade_cfg)
    local btn_list = OAFishWGData.Instance:GetCurConsumeCfg(grade_cfg)
    if not btn_list then
        return
    end

    local sp_guarantee_x,sp_guarantee_n,sp_enter_num = OAFishWGData.Instance:GetSpGuarantee()
    local reward_pool_id = grade_cfg.reward
    local sp_max_num = OAFishWGData.Instance:GetGuaranteeListCount(reward_pool_id)
    local need_count

    local is_enough, color
    local has_num = ItemWGData.Instance:GetItemNumInBagById(grade_cfg.consume_item)
    for i = 1, 3 do
        if btn_list[i] then
            need_count = nil
            if grade_cfg.sp_guarantee_finish == 1 and sp_guarantee_n >= (grade_cfg.sp_guarantee_n - 1) and
                sp_enter_num == (sp_max_num - 1) and grade_cfg.sp_guarantee_x - sp_guarantee_x < btn_list[i].consume_count then
    
                need_count = grade_cfg.sp_guarantee_x - sp_guarantee_x
            end

            need_count = need_count or btn_list[i].consume_count
            is_enough = has_num >= btn_list[i].consume_count or has_num >= need_count
            if self.node_list["fish_btn_red_" .. i] then
                self.node_list["fish_btn_red_" .. i]:SetActive(is_enough)
            end
            color = is_enough and COLOR3B.C8 or COLOR3B.C10
            local has_num_str = ToColorStr(has_num, color)
            if need_count < btn_list[i].onekey_lotto_num then
                self.node_list["fish_btn_cost_" .. i].text.text = has_num_str .. "/" .. need_count
            else
                self.node_list["fish_btn_cost_" .. i].text.text = has_num_str .. "/" .. btn_list[i].consume_count
            end
            UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["fish_btn_cost_" .. i].rect)
        end
        self.node_list["fish_btn_" .. i]:SetActive(btn_list[i] ~= nil)
    end
end

function OperationActivityView:UpdateFishRecord(str)
    local count =  OAFishWGData.Instance:GetNewRecordCount()
    OAFishWGData.Instance:SetNewRecordCount(count + 1)
    self:FlushFishRecord()

    TryDelayCall(self, function()
        OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.RECORD)
    end, 3,"oa_fish_delay_notice")

end

function OperationActivityView:UpdateFishTimer(has_next, elapse_time, total_time)
    local str
    local time = TimeUtil.FormatTimeDHMS(total_time - elapse_time)
    str = string.format(Language.Common.ActTimeEnd, time)

    if self.node_list["fish_act_time"] then
        self.node_list["fish_act_time"].text.text = str
    end
end

function OperationActivityView:CompleteFishTime(has_next)
    if has_next then
        AddDelayCall(self, function()
            self:FlushAllFishInfo()
        end,1)
    end
end

FishZhenXiCell = FishZhenXiCell or BaseClass(BaseRender)
function FishZhenXiCell:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.tween = nil
end

function FishZhenXiCell:OnFlush()
    if not self.data then
        return
    end
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["root"])
    end
    self.item_cell:SetData(self.data.reward_item)
end

-------------------------------------------------返利ItemCell-----------------------------------------------------
FanliItemRender = FanliItemRender or BaseClass(BaseRender)
function FanliItemRender:__init()
    XUI.AddClickEventListener(self.node_list.fanli_item_cell, BindTool.Bind(self.OnClickFanliItem, self))
end

function FanliItemRender:__delete()
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
end

function FanliItemRender:OnFlush()
    if not self.data then
        return
    end
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["itemcell_pos"])
    end

    local data = self.data
    local item_data = data.cfg.reward_item[0]
    self.item_cell:SetData(item_data)

    self.node_list.fanli_yilingqu:SetActive(data.has_receive)
    local draw_num = OAFishWGData.Instance:GetCurDrawNum()
    -- self.node_list.fanli_kelingqu:SetActive(not data.has_receive and draw_num >= data.cfg.lotto_num)
    local can_get = not data.has_receive and draw_num >= data.cfg.lotto_num
    self.item_cell:SetRedPointEff(can_get)
    self.node_list.remind:SetActive(can_get)
    local color = (data.has_receive or draw_num >= data.cfg.lotto_num) and COLOR3B.C2 or COLOR3B.C3
    self.node_list.fanli_need_count.text.text = string.format(Language.OAFish.TurnCount, color, draw_num, data.cfg.lotto_num)
    self.node_list.fanli_item_cell.button.interactable = can_get
end

function FanliItemRender:OnClickFanliItem()
    local data = self.data
    local draw_num = OAFishWGData.Instance:GetCurDrawNum()
    if data.cfg.lotto_num > draw_num or data.has_receive then
        local reward_item = data.cfg.reward_item
        TipWGCtrl.Instance:OpenItem(reward_item[0], ItemTip.FROM_NORMAL)
    else
        OAFishWGCtrl.Instance:SendOpera(OA_LUCK_CHARM_OP_TYPE.LEIJI_REWARD, data.cfg.index)
    end
end