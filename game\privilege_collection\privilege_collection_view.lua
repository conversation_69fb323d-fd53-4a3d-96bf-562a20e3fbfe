PrivilegeCollectionView = PrivilegeCollectionView or BaseClass(SafeBaseView)

function PrivilegeCollectionView:__init()
    self:SetMaskBg(false, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
    
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    self.open_source_view = "BtnPrivilegeCollection"
    
    self.default_index = TabIndex.pri_col_zztq
    self:AddViewResource(TabIndex.pri_col_nstq, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/privilege_collection_ui_prefab", "VerticalTabbar")
    self:AddViewResource(TabIndex.pri_col_zztq, "uis/view/privilege_collection_ui_prefab", "layout_privilege_collection_zztq_view")
    self:AddViewResource(TabIndex.pri_col_nstq, "uis/view/privilege_collection_ui_prefab", "layout_privilege_collection_nstq_view")
    self:AddViewResource(TabIndex.pri_col_zjtq, "uis/view/privilege_collection_ui_prefab", "layout_privilege_collection_zjtq_view")
    self:AddViewResource(TabIndex.pri_col_sqcy, "uis/view/privilege_collection_ui_prefab", "layout_privilege_collection_sqcy_view")
    self:AddViewResource(TabIndex.pri_col_shtq, "uis/view/privilege_collection_ui_prefab", "layout_privilege_collection_shtq_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")

	self:SetTabShowUIScene(TabIndex.pri_col_zztq, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.PRI_COL_ZZTQ})
	self:SetTabShowUIScene(TabIndex.pri_col_zjtq, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.PRI_COL_ZJTQ})
    self:SetTabShowUIScene(TabIndex.pri_col_sqcy, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.PRI_COL_SQCY})
    self:SetTabShowUIScene(TabIndex.pri_col_shtq, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.PRI_COL_SHTQ})
end

function PrivilegeCollectionView:LoadCallBack()
	local remind_tab = {
		{RemindName.PrivilegeCollection_ZZTQ},
		{RemindName.PrivilegeCollection_NSTQ},
        {RemindName.PrivilegeCollection_SHTQ},
		{RemindName.PrivilegeCollection_ZJTQ},
        {RemindName.PrivilegeCollection_SQCY},
	}

    if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
        self.tabbar:SetVerTabbarIconStr("a3_tq_yq_")
        self.tabbar:SetVerTabbarIconPath(ResPath.GetPrivilegeCollectionImg)
        self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetCreatVerCallBack, self))
		self.tabbar:Init(Language.PrivilegeCollection.TabbleGroup, nil, "uis/view/privilege_collection_ui_prefab", nil ,remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))

        FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.PrivilegeCollectionView, self.tabbar)
	end
end

function PrivilegeCollectionView:ReleaseCallBack()
    if nil ~= self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

    self:ZZTQReleaseCallBack()
    self:NSTQReleaseCallBack()
    self:ZJTQReleaseCallBack()
    self:SQCYReleaseCallBack()
    self:SHTQReleaseCallBack()
end

function PrivilegeCollectionView:LoadIndexCallBack(index)
    local bundle, assert

    if index == TabIndex.pri_col_zztq then
        self:ZZTQLoadIndecCallBack()
    elseif index == TabIndex.pri_col_nstq then
        self:NSTQLoadIndecCallBack()
        bundle, assert = ResPath.GetRawImagesJPG("a3_tq_bj")
    elseif index == TabIndex.pri_col_zjtq then
        self:ZJTQLoadIndecCallBack()
    elseif index == TabIndex.pri_col_sqcy then
        self:SQCYLoadCallBack()
    elseif index == TabIndex.pri_col_shtq then
        self:SHTQLoadIndecCallBack()
        -- bundle, assert = ResPath.GetRawImagesJPG("a3_shtq_bg")
    end

    if self.node_list.RawImage_tongyong and nil ~= bundle and nil ~= assert then
        self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
            self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
        end)
    end
end

function PrivilegeCollectionView:ShowIndexCallBack(index)
    if index == TabIndex.pri_col_zztq then
        self:ZZTQShowIndecCallBack()
    elseif index == TabIndex.pri_col_nstq then
        self:NSTQShowIndecCallBack()
    elseif index == TabIndex.pri_col_zjtq then
        self:ZJTQShowIndecCallBack()
    elseif index == TabIndex.pri_col_sqcy then
        self:SQCYShowIndexCallBack()
    elseif index == TabIndex.pri_col_shtq then
        self:SHTQShowIndecCallBack()
    end
end

function PrivilegeCollectionView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
            if index == TabIndex.pri_col_zztq then
                self:ZZTQOnFlush(param_t, index)
            elseif index == TabIndex.pri_col_nstq then
                self:NSTQOnFlush(param_t, index)
            elseif index == TabIndex.pri_col_zjtq then
                self:ZJTQOnFlush(param_t, index)
            elseif index == TabIndex.pri_col_sqcy then
                self:SQCYOnFlush()
            elseif index == TabIndex.pri_col_shtq then
                self:SHTQOnFlush()
            end
        elseif k == "flush_model" then
            if index == TabIndex.pri_col_zztq then
                self:ZZTQPlayLastAction()
            elseif index == TabIndex.pri_col_zjtq then
                self:ZJTQPlayLastAction()
            end
        end
    end

    self:SetCreatVerCallBack()
end

function PrivilegeCollectionView:SetCreatVerCallBack()
    -- 终极特权需要至尊特权全部激活后再显示
    -- self.tabbar:SetToggleVisible(TabIndex.pri_col_zjtq, PrivilegeCollectionWGData.Instance:IsCanShowZJTQ())
end