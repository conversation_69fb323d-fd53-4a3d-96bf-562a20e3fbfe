-- Y-运营活动-每日首充.xls
local item_table={
[1]={item_id=46556,num=3,is_bind=1},
[2]={item_id=46040,num=1,is_bind=1},
[3]={item_id=39144,num=1000,is_bind=1},
[4]={item_id=39142,num=1000,is_bind=1},
[5]={item_id=46558,num=3,is_bind=1},
[6]={item_id=46555,num=2,is_bind=1},
[7]={item_id=46039,num=1,is_bind=1},
[8]={item_id=46560,num=2,is_bind=1},
[9]={item_id=46042,num=1,is_bind=1},
[10]={item_id=46560,num=3,is_bind=1},
[11]={item_id=46558,num=2,is_bind=1},
}

return {
config_param={
{grade=0,},
{week_index=5,grade=1,},
{start_server_day=20,end_server_day=27,grade=0,interface=2,},
{week_index=5,grade=1,},
{start_server_day=27,end_server_day=34,},
{week_index=5,grade=1,},
{start_server_day=34,end_server_day=41,},
{start_server_day=34,end_server_day=41,},
{start_server_day=41,end_server_day=48,},
{week_index=5,grade=1,},
{start_server_day=48,end_server_day=55,},
{start_server_day=48,end_server_day=55,},
{start_server_day=55,end_server_day=62,},
{start_server_day=55,end_server_day=62,},
{start_server_day=62,end_server_day=69,},
{start_server_day=62,end_server_day=69,},
{start_server_day=69,end_server_day=76,},
{start_server_day=69,end_server_day=76,},
{start_server_day=76,end_server_day=83,},
{start_server_day=76,end_server_day=83,},
{start_server_day=83,end_server_day=90,},
{start_server_day=83,end_server_day=90,},
{start_server_day=90,end_server_day=97,},
{start_server_day=90,end_server_day=97,},
{start_server_day=97,end_server_day=104,},
{start_server_day=97,end_server_day=104,},
{start_server_day=104,end_server_day=111,},
{week_index=5,grade=3,},
{start_server_day=111,end_server_day=118,},
{start_server_day=111,end_server_day=118,},
{start_server_day=118,end_server_day=125,},
{start_server_day=118,end_server_day=125,},
{start_server_day=125,end_server_day=132,},
{start_server_day=125,end_server_day=132,},
{start_server_day=132,end_server_day=139,},
{start_server_day=132,end_server_day=139,},
{start_server_day=139,end_server_day=146,},
{week_index=5,grade=3,},
{start_server_day=146,end_server_day=153,},
{start_server_day=146,end_server_day=153,},
{start_server_day=153,end_server_day=160,},
{start_server_day=153,end_server_day=160,},
{start_server_day=160,end_server_day=167,interface=2,},
{start_server_day=160,end_server_day=167,},
{start_server_day=167,end_server_day=174,},
{start_server_day=167,end_server_day=174,},
{start_server_day=174,end_server_day=181,},
{start_server_day=174,end_server_day=181,},
{start_server_day=181,end_server_day=188,},
{start_server_day=181,end_server_day=188,},
{start_server_day=188,end_server_day=999,},
{start_server_day=188,end_server_day=999,}
},

config_param_meta_table_map={
[27]=43,	-- depth:1
[51]=43,	-- depth:1
[23]=43,	-- depth:1
[35]=43,	-- depth:1
[9]=1,	-- depth:1
[19]=43,	-- depth:1
[47]=43,	-- depth:1
[31]=43,	-- depth:1
[39]=43,	-- depth:1
[5]=9,	-- depth:2
[15]=43,	-- depth:1
[38]=37,	-- depth:1
[34]=38,	-- depth:2
[50]=38,	-- depth:2
[46]=38,	-- depth:2
[42]=38,	-- depth:2
[26]=38,	-- depth:2
[22]=38,	-- depth:2
[6]=5,	-- depth:3
[18]=38,	-- depth:2
[7]=3,	-- depth:1
[14]=38,	-- depth:2
[30]=38,	-- depth:2
[11]=3,	-- depth:1
[10]=9,	-- depth:2
[28]=27,	-- depth:2
[4]=3,	-- depth:1
[48]=28,	-- depth:3
[8]=4,	-- depth:2
[12]=4,	-- depth:2
[40]=28,	-- depth:3
[16]=28,	-- depth:3
[36]=28,	-- depth:3
[20]=28,	-- depth:3
[24]=28,	-- depth:3
[32]=28,	-- depth:3
[44]=28,	-- depth:3
[52]=28,	-- depth:3
},
reward={
{grade=0,},
{grade=0,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5]},},
{reward_item={[0]=item_table[6],[1]=item_table[7],[2]=item_table[8],[3]=item_table[3],[4]=item_table[4]},},
{day_index=2,reward_item={[0]=item_table[6],[1]=item_table[9],[2]=item_table[3],[3]=item_table[4],[4]=item_table[10]},},
{day_index=3,reward_item={[0]=item_table[6],[1]=item_table[2],[2]=item_table[10],[3]=item_table[3],[4]=item_table[4]},},
{grade=2,},
{grade=2,},
{grade=3,},
{grade=3,},
{grade=3,}
},

reward_meta_table_map={
[8]=3,	-- depth:1
[2]=4,	-- depth:1
[7]=2,	-- depth:2
[9]=4,	-- depth:1
[10]=5,	-- depth:1
},
interface={
{},
{interface=1,},
{interface=2,pic_1="day_first_juanzoudi_1",pic_2="day_first_kuang_1",pic_3="day_first_meinv_1",pic_4="day_first_shouchong_1",pic_5="day_first_xuanchuanyu_1",},
{interface=3,}
},

interface_meta_table_map={
[4]=3,	-- depth:1
},
config_param_default_table={start_server_day=10,end_server_day=20,week_index=2,grade=2,interface=0,open_level=100,},

reward_default_table={grade=1,day_index=1,reward_item={[0]=item_table[1],[1]=item_table[9],[2]=item_table[11],[3]=item_table[3],[4]=item_table[4]},chaozhi="0:0:0:0:0",},

interface_default_table={interface=0,pic_1="day_first_juanzoudi",pic_2="day_first_kuang",pic_3="day_first_meinv",pic_4="day_first_shouchong",pic_5="day_first_xuanchuanyu",rule_1="充值任意金额就能领取豪礼哦！",rule_2="1.活动期间，每日充值<color=#99ffbb>任意</color>金额，即可领取超值好礼\n2.活动结束后，未领取的奖励将通过邮件发放\n",btn_1_1="领取豪礼",btn_1_2="领取豪礼",btn_1_3="明日可领取",btn_1_4="已领取",}

}

