--爱的表白积分榜
ProfessLovelRankView = ProfessLovelRankView or BaseClass(SafeBaseView)
function ProfessLovelRankView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "profess_love_rank_view")
    self:SetMaskBg()
    self.view_name = "ProfessLovelRankView"
end

function ProfessLovelRankView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.OpenServer.ScoreRank
    self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition

	self.rank_list = AsyncListView.New(ActProfessLoveRender, self.node_list["ph_rank_list"])
end

function ProfessLovelRankView:ReleaseCallBack()
	if nil ~= self.rank_list then
		self.rank_list:DeleteMe()
		self.rank_list = nil
	end
end

function ProfessLovelRankView:OnFlush()
	local data_list = ActivePerfertQingrenWGData.Instance:GetProfessLoveRankInfo()
    local self_rank_index, rank_score = ActivePerfertQingrenWGData.Instance:GetProfessForLoveOwenerInfo()
    self.node_list.rank_value.text.text = rank_score

    if not data_list or IsEmptyTable(data_list) then
        self.node_list.img_no_record:SetActive(true)
	end
    self.node_list.img_no_record:SetActive(false)
	
	if -1 == self_rank_index then
		self.node_list.rank_img:SetActive(false)
		self.node_list.rank_num:SetActive(true)
		local _, _, _, rank_value  = ActivePerfertQingrenWGData.Instance:GetLoveBiaoBaiScore()
		self.node_list.rank_num.text.text = Language.Rank.NoRank
		self.node_list.rank_value.text.text = rank_value or 0
	else
		self.node_list.rank_img:SetActive(self_rank_index <= 3)
		self.node_list.rank_num:SetActive(self_rank_index > 3)
		if self_rank_index > 0 and self_rank_index <= 3 then
			local b, a = ResPath.GetCommonImages("icon_paiming" .. self_rank_index)
			self.node_list.rank_img.image:LoadSprite(b, a, function()
				XUI.ImageSetNativeSize(self.node_list.rank_img)
			end)
		end
		self.node_list.rank_num.text.text = self_rank_index
	end
	self.rank_list:SetDataList(data_list)
end


ActProfessLoveRender = ActProfessLoveRender or BaseClass(BaseRender)
function ActProfessLoveRender:__init()

end

function ActProfessLoveRender:__delete()

end

function ActProfessLoveRender:OnFlush()
	if nil == self.data then return end
	if self.data.rank_index <= 3 then
		local b,a = ResPath.GetCommonImages("icon_paiming" .. self.data.rank_index)
		self.node_list.icon.image:LoadSprite(b,a,function()
			XUI.ImageSetNativeSize(self.node_list.icon)
		end)
    end
    
	self.node_list.render_bg:SetActive(self.index % 2 ~= 0)
	self.node_list.icon:SetActive(self.data.rank_index <= 3)
	self.node_list.paiming.text.text = self.data.rank_index
	if self.data.score_value > 0 then
		local user_id = RoleWGData.Instance:InCrossGetOriginUid()
		self.node_list.score.text.text = self.data.score_value
		if user_id == self.data.w_uid then
			self.node_list.name_1.text.text = ToColorStr(self.data.w_name, COLOR3B.PURPLE)
		else
			self.node_list.name_1.text.text = self.data.w_name
		end
		if user_id == self.data.h_uid then
			self.node_list.name_2.text.text = ToColorStr(self.data.h_name, COLOR3B.PURPLE)
		else
			self.node_list.name_2.text.text = self.data.h_name
		end
	else
		self.node_list.score.text.text = string.format(Language.Activity.XuQiuJiFen,self.data.limit_value) --"需求积分："..self.data.limit_value
	end
end