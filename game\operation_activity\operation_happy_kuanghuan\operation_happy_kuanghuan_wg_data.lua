OperationHappy<PERSON>uangHuanWGData = OperationHappyKuangHuanWGData or BaseClass()

function OperationHappyKuangHuanWGData:__init()
	if OperationHappyKuangHuanWGData.Instance ~= nil then
		ErrorLog("[OperationHappyKuangHuanWGData] attempt to create singleton twice!")
		return
	end

	OperationHappyKuangHuanWGData.Instance = self
	self.remind_hint = false

	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN, {[1] = OPERATION_EVENT_TYPE.LEVEL},
		BindTool.Bind(self.GetActCanOpen, self) , BindTool.Bind(self.IsShowKuangHuanRedPoint, self))

	self:UpdataConfig()

	RemindManager.Instance:Register(RemindName.OperationHappyKuangHuan, BindTool.Bind(self.IsShowKuangHuanRedPoint, self))
end

function OperationHappyKuangHuanWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.OperationHappyKuangHuan)
	OperationHappyKuangHuanWGData.Instance = nil
	self.old_fetch_reward_flag = nil

end

function OperationHappyKuangHuanWGData:SetHappyKhInfo(protocol)
	self.remind_hint = false
	self.fetch_reward_flag = bit:d2b(protocol.fetch_reward_flag)
	self.task_info = protocol.task_info

	self:SetHappyKhViewInfo()
end

--设置狂欢信息
function OperationHappyKuangHuanWGData:SetHappyKhViewInfo()
	local task_cfg = self:GetTaskCfg()
	local get_task_info = {}
	self.get_task_info = {}
	local remove_id = {} --需要到时候被移除展示的任务
	local can_show_id = {} -- 可以展示的任务
	local task_status = {}
	self.all_bonus_points = 0 --累积的总狂欢数值
	for k,v in pairs(task_cfg) do
		if self.task_info[v.task_id] then
			local cfg = {}
			cfg["tab"] = v --表的原信息
			cfg["task_sort"] = v.task_sort
			cfg["sucess_count"] = self.task_info[v.task_id].sucess --成功次数

			cfg["sucess"] = self.task_info[v.task_id].progress >= v.progress and 1 or 0 --成功标识
			cfg["progress"] = cfg.sucess == 1 and v.progress or self.task_info[v.task_id].progress --单次任务进度
			
			table.insert(get_task_info, cfg)
			task_status[v.task_id] = cfg["sucess"]
		else
			local cfg = {}
			cfg["tab"] = v --表的原信息
			cfg["task_sort"] = v.task_sort
			cfg["sucess_count"] = 0
			cfg["sucess"] = 0
			cfg["progress"] = 0
			table.insert(get_task_info, cfg)
			task_status[v.task_id] = cfg["sucess"]
		end
	end

	for k,v in pairs(get_task_info) do
		if v.tab.pre_task_id > 0 and task_status[v.tab.pre_task_id] and task_status[v.tab.pre_task_id] == 1 then
			remove_id[v.tab.pre_task_id] = v.sucess == 1
			table.insert(can_show_id, v)
			self.all_bonus_points = self.all_bonus_points + v.sucess_count * v.tab.bonus_points
		elseif v.tab.pre_task_id < 0 then
			table.insert(can_show_id, v)
			self.all_bonus_points = self.all_bonus_points + v.sucess_count * v.tab.bonus_points
		end
	end

	if not IsEmptyTable(can_show_id) then
		for k,v in pairs(can_show_id) do
			if not remove_id[v.tab.task_id] then
				table.insert(self.get_task_info, v)
			end
		end
		table.sort(self.get_task_info, SortTools.KeyLowerSorter("sucess","task_sort"))
	end

	local reward_cfg = self:GetRewardCfg()
	self.get_reward_info_0 = {} --非预览
	local get_reward_info_1 = {} --预览
	self.get_reward_info_1 = {} --预览

	for k,v in pairs(reward_cfg) do
		local cfg = {}
		cfg["tab"] = v
		cfg["bonus_id"] = v.bonus_id
		if v.bonus_points <= self.all_bonus_points and self.fetch_reward_flag[32 - v.bonus_id] and 1 == self.fetch_reward_flag[32 - v.bonus_id] then
			cfg["status"] = HAPPY_CANIVAL_REWARD_TYPE.YLQ

			if self.old_fetch_reward_flag and self.old_fetch_reward_flag[32 - v.bonus_id] and 0 == self.old_fetch_reward_flag[32 - v.bonus_id] then
				self:GetItemListHint(v)
			end
		elseif v.bonus_points <= self.all_bonus_points and self.fetch_reward_flag[32 - v.bonus_id] and 0 == self.fetch_reward_flag[32 - v.bonus_id] then
			cfg["status"] = HAPPY_CANIVAL_REWARD_TYPE.KLQ
			self.remind_hint = true
		else
			cfg["status"] = HAPPY_CANIVAL_REWARD_TYPE.WDC
		end

		if 0 == v.if_show then
			table.insert(self.get_reward_info_0, cfg)
		else
			table.insert(get_reward_info_1, cfg)
		end
		
	end

	if not IsEmptyTable(self.get_reward_info_0) then
		if not IsEmptyTable(get_reward_info_1) then
			table.sort(get_reward_info_1, SortTools.KeyLowerSorter("status", "bonus_id"))
			get_reward_info_1[1]["special_bg"] = true
			self.get_reward_info_1[1] = get_reward_info_1[1]  --只取一组数据就可以

			if #get_reward_info_1 >= 2 then
				for i=2,#get_reward_info_1 do
					get_reward_info_1[i]["special_bg"] = false
					table.insert(self.get_reward_info_0, get_reward_info_1[i])
				end
			end
		end

		table.sort(self.get_reward_info_0, SortTools.KeyLowerSorter("status", "bonus_id"))
	end


	--为了记录可领取打开恭喜获得
	self.old_fetch_reward_flag = self.fetch_reward_flag
end

--是否开启
function OperationHappyKuangHuanWGData:GetActCanOpen()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local rewardlist, tasklist, open_level = self:GetInterfaceByServerDay()
	if vo.level >= open_level then
		return true
	end
	return false
end

--获取红点展示
function OperationHappyKuangHuanWGData:GetRemindHintValue()
	return self.remind_hint
end

--获取当前累计的欢乐值
function OperationHappyKuangHuanWGData:GetAllBonusPoints()
	return self.all_bonus_points or 0
end

--根据服务器开服天数获得界面相关参数
function OperationHappyKuangHuanWGData:GetInterfaceByServerDay()
    local open_day =  OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN)
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几
	for k,v in pairs(self.operation_kuanghuan_param_cfg) do
		if v.start_server_day <= open_day and open_day < v.end_server_day and week == v.week_index then
			return v.rewardlist, v.tasklist, v.open_level
		end
	end
	
	return 1, 1, 9999
end

--根据服务器开服天数获得界面配置
function OperationHappyKuangHuanWGData:GetInterfacecCfg()
	local open_day =  OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN)
	for k,v in pairs(self.operation_kuanghuan_param_cfg) do
		if v.start_server_day <= open_day and open_day <= v.end_server_day then
			return v
		end
	end
end

--根据界面配置获取界面信息
function OperationHappyKuangHuanWGData:GetViewCfgByInterface()
	local rewardlist = self:GetInterfaceByServerDay()
	return self.operation_kuanghuan_interface_cfg[rewardlist]
end

--获取任务配置
function OperationHappyKuangHuanWGData:GetTaskCfg()
	local rewardlist, tasklist = self:GetInterfaceByServerDay()
	local task_cfg = {}
	for k,v in pairs(self.operation_kuanghuan_task_cfg) do
		if tasklist == v.tasklist and 1 == v.if_open then
			table.insert(task_cfg, v)
		end
	end

	return task_cfg
end

--获取任务信息
function OperationHappyKuangHuanWGData:GetTaskInfo()
	return self.get_task_info or {}
end

--获取奖励配置
function OperationHappyKuangHuanWGData:GetRewardCfg()
	local rewardlist, tasklist = self:GetInterfaceByServerDay()
	local reward_cfg = {}
	for k,v in pairs(self.operation_kuanghuan_reward_cfg) do
		if rewardlist == v.rewardlist then
			table.insert(reward_cfg, v)
		end
	end

	return reward_cfg
end

--获取奖励信息
function OperationHappyKuangHuanWGData:GetRewardInfo()
	return self.get_reward_info_0 or {}, self.get_reward_info_1 or {}
end

--获取按钮配置
function OperationHappyKuangHuanWGData:GetTBtnCfg()
	local rewardlist, tasklist = self:GetInterfaceByServerDay()
	local btn_cfg = {}

	for k,v in pairs(self.operation_kuanghuan_btn_cfg) do
		if rewardlist == v.rewardlist then
			table.insert(btn_cfg, v)
		end
	end

	return btn_cfg
end


--幸福狂欢红点
function OperationHappyKuangHuanWGData:IsShowKuangHuanRedPoint()
	local is_open = OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_HAPPY_KUANGHUAN)
	if not is_open then
		return 0
	end

	if self.remind_hint then
		return 1
	end

	return 0
end

function OperationHappyKuangHuanWGData:GetItemListHint(item)
	local item_list = {}
	if item and item.reward_item then
		for k,v in pairs(item.reward_item) do
			table.insert(item_list, v)
		end
	end

	if not IsEmptyTable(item.reward_item) then
		TipWGCtrl.Instance:ShowGetReward(nil, item_list, false)
	end
end

function OperationHappyKuangHuanWGData:UpdataConfig()
	self.operation_kuanghuan_cfg = ConfigManager.Instance:GetAutoConfig("operation_activity_happy_carnival_auto")
	self.operation_kuanghuan_param_cfg = self.operation_kuanghuan_cfg.config_param
	self.operation_kuanghuan_interface_cfg = ListToMap(self.operation_kuanghuan_cfg.interface, "rewardlist")
	self.operation_kuanghuan_reward_cfg = self.operation_kuanghuan_cfg.reward
	self.operation_kuanghuan_task_cfg = self.operation_kuanghuan_cfg.task
	self.operation_kuanghuan_btn_cfg = self.operation_kuanghuan_cfg.btn_config
end




