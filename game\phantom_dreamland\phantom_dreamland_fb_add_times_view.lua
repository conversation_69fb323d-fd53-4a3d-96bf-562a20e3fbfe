--幻梦秘境购买时长
PhantomDreamlandFBAddTimesView = PhantomDreamlandFBAddTimesView or BaseClass(SafeBaseView)

function PhantomDreamlandFBAddTimesView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ vector2 = Vector2(0, 21), sizeDelta = Vector2(602, 428) })
	self:AddViewResource(0, "uis/view/phantom_dreamland_ui_prefab", "layout_phantom_dreamland_fb_add_times")
end

function PhantomDreamlandFBAddTimesView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ViewName.AddTimes

	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind(self.OnClickBuyTimes, self))
	XUI.AddClickEventListener(self.node_list.all_buy_check, BindTool.Bind(self.OnClickAllBuyCheck, self))
end

function PhantomDreamlandFBAddTimesView:ReleaseCallBack()
end

function PhantomDreamlandFBAddTimesView:OnFlush(param_t)
	self:FlushShowInfo()
end

function PhantomDreamlandFBAddTimesView:FlushShowInfo()
	local scene_info = PhantomDreamlandWGData.Instance:GetDreamlandSceneInfo()
	local other_cfg = PhantomDreamlandWGData.Instance:GetBaseCfg()

	local remain_buy_count = other_cfg.max_buy_time_count - scene_info.buy_time_count
	local str = string.format(Language.PhantomDreamland.AddTimesCount, remain_buy_count, other_cfg.max_buy_time_count)
	self.node_list["txt_buy_times"].text.text = str
	self.node_list["txt_buy_desc"].text.text = string.format(Language.PhantomDreamland.AddTimesTip, other_cfg.add_time)

	local all_buy_check_state = PhantomDreamlandWGData.Instance:GetBuyTimesCheckState()
	self.node_list["all_buy_yes"]:CustomSetActive(all_buy_check_state)
	--local count = all_buy_check_state and math.max(remain_buy_count, 1) or 1
	
	local price = 0
	if all_buy_check_state then
		for index = scene_info.buy_time_count + 1, other_cfg.max_buy_time_count do
			price = price + index * other_cfg.add_price
		end
	else
		price = remain_buy_count > 0 and (scene_info.buy_time_count + 1) * other_cfg.add_price or 0
	end
	self.node_list["txt_price"].text.text = price
end

-- 合并购买
function PhantomDreamlandFBAddTimesView:OnClickAllBuyCheck()
	local cur_check_state = PhantomDreamlandWGData.Instance:GetBuyTimesCheckState()
	PhantomDreamlandWGData.Instance:SetBuyTimesCheckState(not cur_check_state)
	self:FlushShowInfo()
end

-- 点击购买
function PhantomDreamlandFBAddTimesView:OnClickBuyTimes()
	local scene_info = PhantomDreamlandWGData.Instance:GetDreamlandSceneInfo()
	local other_cfg = PhantomDreamlandWGData.Instance:GetBaseCfg()

	local remain_buy_count = other_cfg.max_buy_time_count - scene_info.buy_time_count
	if remain_buy_count <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.PhantomDreamland.BuyTimesLimited)
		return
	end
	local cur_check_state = PhantomDreamlandWGData.Instance:GetBuyTimesCheckState()
	local buy_type = cur_check_state and 1 or 0
    PhantomDreamlandWGCtrl.Instance:RequestDreamlandBuyTimes(buy_type)
	self:Close()
end
