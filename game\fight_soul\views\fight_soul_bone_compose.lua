FightSoulBoneComposeView = FightSoulBoneComposeView or BaseClass(SafeBaseView)

function FightSoulBoneComposeView:__init()
	self.view_name = "FightSoulBoneComposeView"
	self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(966, 670)})
    self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_fight_soul_bone_compose")
end

function FightSoulBoneComposeView:ReleaseCallBack()
    if nil ~= self.compose_bag_grid then
        self.compose_bag_grid:DeleteMe()
        self.compose_bag_grid = nil
    end

    if nil ~= self.cur_item then
        self.cur_item:DeleteMe()
        self.cur_item = nil
    end

    if nil ~= self.next_item then
        self.next_item:DeleteMe()
        self.next_item = nil
    end

    if nil ~= self.attr_list then
        for k,v in pairs(self.attr_list) do
            v:DeleteMe()
        end
        self.attr_list = nil
    end

    if nil ~= self.stuff_item_list then
        for k,v in pairs(self.stuff_item_list) do
            v:DeleteMe()
        end
        self.stuff_item_list = nil
    end

	-- self:CleanUpDownTween()
	self.fs_type = nil
    self.bag_list = nil
	self.model_asset = nil
	self.part_type_toggle_list = nil
end

function FightSoulBoneComposeView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FightSoul.ComposeBoneTitle

    if nil == self.compose_bag_grid then
        self.compose_bag_grid = AsyncBaseGrid.New()
        self.compose_bag_grid:SetStartZeroIndex(false)
        self.compose_bag_grid:CreateCells({
            col = 5,
            cell_count = 40,
            list_view = self.node_list["bag_list"],
            itemRender = FightSoulBoneBagItem,
            assetBundle = "uis/view/fight_soul_ui_prefab",
            assetName = "fight_soul_bone_bag_item",
            change_cells_num = 2,
        })
        self.compose_bag_grid:SetSelectCallBack(BindTool.Bind1(self.OnComposeBagSelectCB, self))
    end

    if nil == self.cur_item then
        self.cur_item = FSBoneEquipBaseItem.New(self.node_list.cur_item)
		self.cur_item:SetIsShowTips(true)
    end

    if nil == self.next_item then
        self.next_item = FSBoneEquipBaseItem.New(self.node_list.next_item)
		self.next_item:SetIsShowTips(true)
    end

    if nil == self.attr_list then
        self.attr_list = {}
        for i = 1, 5 do
            self.attr_list[i] = FSBoneStrengthAttrRender.New(self.node_list.attrs_list:FindObj("attr_" .. i))
        end
    end

    if nil == self.stuff_item_list then
        self.stuff_item_list = {}
        local stuff_num = self.node_list.stuff_part.transform.childCount
        for i = 1, stuff_num do
            local cell = FightSoulBoneComposeStuffItem.New(self.node_list.stuff_part:FindObj("stuff_" .. i))
            cell:SetClickCallBack(BindTool.Bind(self.OnClickComposeStuff, self))
            cell:SetIndex(i)
            self.stuff_item_list[i] = cell
        end
    end

	self:DoUpDownTween()
    XUI.AddClickEventListener(self.node_list.btn_compose, BindTool.Bind(self.OnClickCompose, self))
	XUI.AddClickEventListener(self.node_list.btn_batch, BindTool.Bind(self.OnClickBatch, self))

	self:InitPartTypeToggleList()
end

function FightSoulBoneComposeView:InitPartTypeToggleList()
	local toggle_list = {}
	for i=1,4 do
		local temp = {m_toggle = nil, hl_img = nil}
		temp.m_toggle = self.node_list["part_type_toggle_" .. i]
		temp.m_toggle.toggle:AddClickListener(BindTool.Bind(self.OnClickToggle, self, i))

		local name_table = temp.m_toggle:GetComponent(typeof(UINameTable))
		temp.node_list = U3DNodeList(name_table, self)
		toggle_list[i] = temp
	end
	self.part_type_toggle_list = toggle_list
	self.select_toggle_index = -1
	self:SelectDefaultToggle()
end

function FightSoulBoneComposeView:CleanUpDownTween()
	if self.model_tweener then
        self.model_tweener:Kill()
        self.model_tweener = nil
    end
end

function FightSoulBoneComposeView:DoUpDownTween()
	self:CleanUpDownTween()

	local tween_time = 1.6
    local node = self.node_list["display"]
	if node then
        RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -10)
        self.model_tweener = node.rect:DOAnchorPosY(10, tween_time)
        self.model_tweener:SetEase(DG.Tweening.Ease.InOutSine)
        self.model_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function FightSoulBoneComposeView:SetDataAndOpen(fs_type, bag_list)
	self.fs_type = fs_type
    self.bag_list = bag_list
    self.select_toggle_index = -1
    self:Open()
end

function FightSoulBoneComposeView:OnFlush(param_t, index)
	self:SelectDefaultToggle()

	for k,v in pairs(param_t) do
		if k == "all" then
			self:FlushComposeBag()
			self:FlushBatchRemind()
			self:FlushToggleRemind()
		elseif k == "bag_change" then
			self.bag_list = FightSoulWGData.Instance:GetFSTypeBoneSortBagListByType(self.fs_type)
			self:FlushComposeBag()
			self:FlushBatchRemind()
			self:FlushToggleRemind()
		elseif k == "select_stuff_cb" then
			self:SetComposeStuffSelect()
			self:FlushComposeView()
		end
	end
end

function FightSoulBoneComposeView:FlushBagListData()

end

function FightSoulBoneComposeView:SelectDefaultToggle()
	if self.select_toggle_index < 0 then
		local select_index = self.fs_type
		local toggle_list = self.part_type_toggle_list
		for i=1,#toggle_list do
			toggle_list[i].m_toggle.toggle.isOn = i == select_index
		end
	end
end

function FightSoulBoneComposeView:OnClickToggle(index)
	if self.select_toggle_index == index then
		return
	end

	local bag_list = FightSoulWGData.Instance:GetFSTypeBoneSortBagListByType(index)
	if IsEmptyTable(bag_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.BagNoBoneLimitDesc)
		return
	end

	self.bag_list = bag_list
	self.select_toggle_index = index
	self.fs_type = index

	local function set_select(node_list, is_select)
		node_list.df_img:SetActive(not is_select)
		node_list.hl_img:SetActive(is_select)
	end

	local toggle_list = self.part_type_toggle_list
	for i=1,#toggle_list do
		set_select(toggle_list[i].node_list, i == index)
	end

	self:FlushComposeBag()
	self:FlushBatchRemind()
end

function FightSoulBoneComposeView:FlushBatchRemind()
	if self.node_list.btn_batch_remind then
		local remind = FightSoulWGData.Instance:GetFSTypeBoneComposeRemind(self.fs_type)
		self.node_list.btn_batch_remind:SetActive(remind)
	end
end

function FightSoulBoneComposeView:FlushToggleRemind()
	local toggle_list = self.part_type_toggle_list
	if toggle_list then
		for i=1,#toggle_list do
			local is_remind = FightSoulWGData.Instance:GetFSTypeBoneComposeRemind(i)
			toggle_list[i].node_list.red_point:SetActive(is_remind)
		end
	end
end

-- 刷新背包
function FightSoulBoneComposeView:FlushComposeBag()
	local bag_list = self.bag_list

    if self.compose_bag_grid then
    	self.compose_bag_grid:CancleAllSelectCell()
        self.compose_bag_grid:SetDataList(bag_list)

        if not IsEmptyTable(bag_list) then
            -- 沙雕 AsyncBaseGrid 只有点击回调，没有选择回调
			local sort_data
			local select_cell_index = 1
			if self.select_compose_index_inbag ~= nil then
				for k,v in ipairs(bag_list) do
					if v.item_data and v.item_data.bag_index == self.select_compose_index_inbag then
						select_cell_index = k
						break
					end
				end
			end

            self.compose_bag_grid:SetSelectCellIndex(select_cell_index)
            local sort_data = bag_list[select_cell_index].item_data
            self.select_compose_index_inbag = sort_data.bag_index
            self:SetComposeStuffSelect(sort_data, true)
        else
            -- 列表默认操作
            self.select_compose_index_inbag = -1
            self:FlushComposeStuffAndBtn()
        end
    end

    self:FlushComposeView()
end

-- 获取当前选择的数据
function FightSoulBoneComposeView:GetComposeSelectData()
    return FightSoulWGData.Instance:GetBoneDataByBagIndex(self.select_compose_index_inbag)
end

function FightSoulBoneComposeView:SetComposeStuffSelect(item_data, auto_select)
    item_data = item_data or self:GetComposeSelectData()
    self.compose_meet_list = FightSoulWGData.Instance:GetMeetBoneComposeStuffSortList(item_data, auto_select)
    self:UpdateComposeGridSelect()
end

function FightSoulBoneComposeView:UpdateComposeGridSelect()
	local bag_list = self.bag_list
    if IsEmptyTable(bag_list) then
    	return
    end

    if self.compose_bag_grid then
        local cell_list = self.compose_bag_grid:GetAllCell()
        for index, cell in pairs(cell_list) do
            cell:FlushCheckState()
        end
    end

    self:FlushComposeStuffAndBtn()
end

function FightSoulBoneComposeView:FlushComposeView()
    local select_data = self:GetComposeSelectData()
    -- 无数据
    if IsEmptyTable(select_data) then
        self.cur_item:ClearData()
        self.node_list.arrow:SetActive(false)
        self.node_list.next_item:SetActive(false)
        self.node_list.attrs_list:SetActive(false)
        return
    end

    -- 目标
    local cur_show_data, next_show_data = FightSoulWGData.Instance:GetBoneComposeShowItemData(select_data)
    local have_next = next_show_data.item_id > 0
    self.cur_item:SetData({bone_data = cur_show_data})
    self.next_item:SetData({bone_data = next_show_data})
    self.node_list.arrow:SetActive(have_next)
    self.node_list.next_item:SetActive(have_next)

    -- 属性
    local attr_list = FightSoulWGData.Instance:GetBoneComposeAttr(cur_show_data, next_show_data)
    for k,v in pairs(self.attr_list) do
        v:SetData(attr_list[k])
    end
    self.node_list.attrs_list:SetActive(true)
end

-- 刷新合成拆料 和 按钮
function FightSoulBoneComposeView:FlushComposeStuffAndBtn()
	if self.select_compose_index_inbag < 0 then
		self.node_list.max_star_flag:SetActive(false)
		self.node_list.btn_compose_remind:SetActive(false)
		local stuff_item_list = self.stuff_item_list
		if stuff_item_list then
			for i=1,#stuff_item_list do
				stuff_item_list[i]:SetData(nil)
			end
		end
		return
	end

	local stuff_list, is_meet_list, is_max = FightSoulWGData.Instance:GetBoneComposeShowStuffList(
								self.select_compose_index_inbag, self.compose_meet_list)

	if self.stuff_item_list then
		for k,v in ipairs(self.stuff_item_list) do
			v:SetData(stuff_list[k])
		end
	end

	local is_all_meet = true
	if IsEmptyTable(is_meet_list) then
		is_all_meet = false
	else
		for k,v in pairs(is_meet_list) do
			if not v then
				is_all_meet = false
				break
			end
		end
	end

	self.node_list.btn_compose:SetActive(not is_max)
	self.node_list.max_star_flag:SetActive(is_max)
	self.node_list.btn_compose_remind:SetActive(is_all_meet and not is_max)
end

-- 背包点击回调
function FightSoulBoneComposeView:OnComposeBagSelectCB(cell)
    if cell == nil or cell.data == nil or cell.data.item_data == nil then
        return
    end

    if self.select_compose_index_inbag == cell.data.item_data.bag_index then
		self:OpenItemTip(cell.data.item_data)
        return
    end

    self.select_compose_index_inbag = cell.data.item_data.bag_index
	FightSoulWGData.Instance:ClearFSTypeBoneSortBagSelectState()
	self:SetComposeStuffSelect(cell.data.item_data, true)
	self:FlushComposeView()
end

function FightSoulBoneComposeView:OpenItemTip(item_data)
	if item_data == nil then
		return
	end

	local bone_is_wear = FightSoulWGData.Instance:GetBoneBagIndexIsWear(item_data.bag_index)
	if bone_is_wear then
		TipWGCtrl.Instance:OpenItem(item_data, ItemTip.FROM_NORMAL)
		return
	end

	local show_contrast = true
	local fight_soul_type, bone_part, suit_type = FightSoulWGData.GetBoneParamByItemId(item_data.item_id)
	local fs_is_wear, slot = FightSoulWGData.Instance:GetTypeIsWear(fight_soul_type)
	if not fs_is_wear then
		show_contrast = false
	end

	if show_contrast then
		local wear_bone_data = FightSoulWGData.Instance:GetWearBoneDataBySlotPart(slot, bone_part)
		if IsEmptyTable(wear_bone_data) then
			show_contrast = false
		end
	end

	local btn_callback_event = {}
	local form_view
	-- 打开对比
	if show_contrast then
		form_view = ItemTip.FROM_FIGHT_SOUL_BONE_CONTRAST
		btn_callback_event[1] = {btn_text = Language.FightSoul.TipsBtnStr[2], callback = function()
			FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.BONE_WEAR, slot,
												bone_part, item_data.bag_index)
		end}

	-- 打开单个
	else
		form_view = ItemTip.FROM_FIGHT_SOUL_BONE
		btn_callback_event[1] = {btn_text = Language.FightSoul.TipsBtnStr[1], callback = function()
			FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.BONE_WEAR, slot,
												bone_part, item_data.bag_index)
		end}
	end

	TipWGCtrl.Instance:OpenItem(item_data, form_view, nil, nil, btn_callback_event)
end

-- 点击合成材料
function FightSoulBoneComposeView:OnClickComposeStuff(cell)
	if cell == nil or cell.data == nil then
		return
	end

	self:OpenSelectStuff(cell.data.stuff_type)
end

function FightSoulBoneComposeView:OpenSelectStuff(stuff_type)
	if self.compose_meet_list == nil then
		return
	end

	FightSoulWGCtrl.Instance:OpenBoneStuffSelectView(stuff_type, self:GetComposeSelectData(),
								self.compose_meet_list)
end

-- 点击合成
function FightSoulBoneComposeView:OnClickCompose()
	local fs_data = FightSoulWGData.Instance
	local stuff_list, is_meet_list, is_max = fs_data:GetBoneComposeShowStuffList(
								self.select_compose_index_inbag, self.compose_meet_list)

	if is_max or IsEmptyTable(is_meet_list) then
		return
	end

	for k,v in pairs(is_meet_list) do
		if not v then
			self:OpenSelectStuff(k)
			return
		end
	end

	local height_flag = fs_data:GetBoneHeightSelectCache()
	if height_flag then
		local select_data = self:GetComposeSelectData()
		local sure_callback = BindTool.Bind(self.SendBoneCompose, self)
		FightSoulWGCtrl.Instance:OpenFightSoulBoneHeightTips(select_data, sure_callback)
		return
	end

	self:SendBoneCompose()
end

function FightSoulBoneComposeView:SendBoneCompose()
	local bag_index_list = {}
	local cache_list = FightSoulWGData.Instance:GetBoneComposeSelectCache()
	for k,v in pairs(cache_list) do
		bag_index_list[#bag_index_list + 1] = k
	end

	FightSoulWGCtrl.Instance:ReqFightSoulComposeOp(FIGHT_SOUL_COMPOSE_TYPE.BONE,
												self.select_compose_index_inbag,
												bag_index_list)
end

function FightSoulBoneComposeView:OnClickBatch()
	FightSoulWGCtrl.Instance:OpenBoneBatchView(self.fs_type)
end
