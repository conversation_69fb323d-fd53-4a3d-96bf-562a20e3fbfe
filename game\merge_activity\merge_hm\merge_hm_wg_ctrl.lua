require("game/merge_activity/merge_hm/merge_hm_wg_data")
require("game/merge_activity/merge_hm/merge_hm_view")

MergeHMWGCtrl = MergeHMWGCtrl or BaseClass(BaseWGCtrl)

function MergeHMWGCtrl:__init()
	if MergeHMWGCtrl.Instance then
		Error<PERSON><PERSON>("[MergeHMWGCtrl] Attemp to create a singleton twice !")
	end
	MergeHMWGCtrl.Instance = self
	self:RegisterAllProtocols()

	self.data = MergeHMWGData.New()
end

function MergeHMWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

	MergeHMWGCtrl.Instance = nil
end

function MergeHMWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCCombineServerActivityExpBuyInfo,'OnSCCombineServerActivityExpBuyInfo')
end

function MergeHMWGCtrl:OnSCCombineServerActivityExpBuyInfo(protocol)
	--print_error(protocol)
	self.data:SetExpBuyInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.MergeActivityView, TabIndex.merge_activity_2118)
	MergeActivityWGData.Instance:GetActivityIsEvent(ACTIVITY_TYPE.MERGE_ACT_HONGMENG_WUDAO)
end

function MergeHMWGCtrl:SendReq(opera_type)
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.MERGE_ACT_HONGMENG_WUDAO,
		opera_type = opera_type,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end
