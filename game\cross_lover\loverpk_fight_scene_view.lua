LoverPkFightSceneView = LoverPkFightSceneView or BaseClass(SafeBaseView)

function LoverPkFightSceneView:__init()
    self:AddViewResource(0, "uis/view/cross_lover_prefab", "layout_loverpk_fight_scene_view")
end

function LoverPkFightSceneView:LoadCallBack()
    if not self.red_menber_list then
        self.red_menber_list = {}

        for i = 1, 2 do
            self.red_menber_list[i] = LoverPkFightSceneMenberRender.New(self.node_list["player_item" .. i])
        end
    end

    if not self.blue_menber_list then
        self.blue_menber_list = {}
        
        for i = 1, 2 do
            self.blue_menber_list[i] = LoverPkFightSceneMenberRender.New(self.node_list["player_item" .. (i + 2)])
        end
    end
end

function LoverPkFightSceneView:ReleaseCallBack()
    if self.red_menber_list then
        for k, v in pairs(self.red_menber_list) do
            v:DeleteMe()
        end

        self.red_menber_list = nil
    end

    if self.blue_menber_list then
        for k, v in pairs(self.blue_menber_list) do
            v:DeleteMe()
        end

        self.blue_menber_list = nil
    end

    if CountDownManager.Instance:HasCountDown("LoverPkFightSceneView") then
		CountDownManager.Instance:RemoveCountDown("LoverPkFightSceneView")
	end
end

function LoverPkFightSceneView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushView()
        elseif k == "show_count_down" then
            self:StartFBOutCountDown()
        end
    end
end

function LoverPkFightSceneView:FlushView()
    local menber_info_list = LoverPkWGData.Instance:GetLoverPKFightMenberInfo()
        
    if not IsEmptyTable(menber_info_list) then
        for i = 1, 2 do
            self.red_menber_list[i]:SetData((menber_info_list[1] or {})[i] or {})
            self.blue_menber_list[i]:SetData((menber_info_list[2] or {})[i] or {})
        end
    end
end

function LoverPkFightSceneView:StartFBOutCountDown()
	if CountDownManager.Instance:HasCountDown("LoverPkFightSceneView") then
		CountDownManager.Instance:RemoveCountDown("LoverPkFightSceneView")
	end

    local pk_state, next_state_time = LoverPkWGData.Instance:GetMatchPKStateAndNextStateTime()
    local server_time = TimeWGCtrl.Instance:GetServerTime()

    if pk_state == 2 and server_time < next_state_time then
        CountDownManager.Instance:AddCountDown("LoverPkFightSceneView",
        function(elapse_time, total_time)
            if self.node_list.top_pk_info_root then
                self.node_list.level_time.text.text = TimeUtil.FormatSecondDHM9(total_time - elapse_time)
                self.node_list.top_pk_info_root:CustomSetActive(true)
            end
        end,
        function()
            if self.node_list.top_pk_info_root then
                self.node_list.top_pk_info_root:CustomSetActive(false)
            end
        end,
        next_state_time, nil, 1)
    end
end

----------------------------------LoverPkFightSceneMenberRender--------------------------------
LoverPkFightSceneMenberRender = LoverPkFightSceneMenberRender or BaseClass(BaseRender)

function LoverPkFightSceneMenberRender:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local cur_hp = tonumber(self.data.cur_hp)
    local max_hp = tonumber(self.data.max_hp)
    self.node_list.slider_blood.slider.value = max_hp > 0 and (cur_hp >= max_hp and 1 or cur_hp / max_hp) or 1
    self.node_list.name.text.text = self.data.name or ""
    self.node_list.death_img:CustomSetActive(self.data.dead)
    self.node_list.offline_img:SetActive(self.data.is_online == 0)
end