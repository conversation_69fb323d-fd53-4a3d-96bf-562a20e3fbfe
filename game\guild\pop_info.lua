GuildPopInfo = GuildPopInfo or BaseClass(SafeBaseView)

function GuildPopInfo:__init()
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_guild_info")
	self.guild_vo = nil

	self.ui_guild_notice = nil
end

function GuildPopInfo:__delete()
	
end

function GuildPopInfo:LoadCallBack()
	self:RegisterAllEvent()
end

-- 注册事件
function GuildPopInfo:RegisterAllEvent()
	self.node_list.btn_connect_mengzhu.button:AddClickListener(BindTool.Bind(self.OnConnectMengZhuHandler, self))
end

-- 联系盟主事件
function GuildPopInfo:OnConnectMengZhuHandler()
	
end
function GuildPopInfo:OpenCallBack()

end

function GuildPopInfo:ShowIndexCallBack()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if nil == role_vo then
		return
	end
	if 0 ~= role_vo.guild_id then
		local post_authority = GuildDataConst.GUILD_POST_AUTHORITY_LIST[role_vo.guild_post]
		local is_connect_mengzhu = true
		if nil ~= post_authority then
			if role_vo.guild_id == self.guild_vo.guild_id and post_authority.post == Language.Guild.MengZhu then
				is_connect_mengzhu = false
			end
		end
	end
end

-- 初始化
function GuildPopInfo:OnFlush()
	if nil == self.guild_vo then
		return
	end
	self.node_list.lbl_guild_name.text.text = self.guild_vo.guild_name
	self.node_list.lbl_level.text.text = self.guild_vo.guild_level
	self.node_list.lbl_mengzhu_name.text.text = self.guild_vo.tuanzhang_name
	local curr_member = self.guild_vo.cur_member_count
	local max_member = self.guild_vo.max_member_count
	self.node_list.lbl_member.text.text = curr_member .. "/" .. max_member
	self.node_list.lbl_notice_des.text.text = self.guild_vo.guild_notice
end

-- 重写打开方法
function GuildPopInfo:Open(guild_vo)
	if nil == guild_vo then
		return
	end
	self.guild_vo = guild_vo
end