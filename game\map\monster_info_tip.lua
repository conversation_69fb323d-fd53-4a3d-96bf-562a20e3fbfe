MonsterInfoTip = MonsterInfoTip or BaseClass(SafeBaseView)

function MonsterInfoTip:__init()
	self.view_layer = UiLayer.PopWhite
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Zero)
	self:AddViewResource(0, "uis/view/map_ui_prefab", "Monster_info_tip")
end

function MonsterInfoTip:LoadCallBack()
	self.load_callback = true
	if self.need_flush then
		self:Flush()
	end
end

function MonsterInfoTip:ShowIndexCallBack()
	-- self:Flush()
end

function MonsterInfoTip:ReleaseCallBack()
	self.load_callback = nil
	self.need_flush = nil
end

function MonsterInfoTip:OnFlush()
	if not self.load_callback then
		self.need_flush = true
		return
	end
	local info = MapWGData.Instance:GetSetCurSelectInfo()
	if info == nil then
		return
	end
	self:FlushPosition(info.id)
	local cfg = BossWGData.Instance:GetMonsterInfo(info.id)
	self.node_list["level"].text.text = ToColorStr(Language.Map.TipLevel .. cfg.reasonable_level, COLOR3B.GREEN)
	self.node_list["fangyu"].text.text = ToColorStr(Language.Map.TipFangyu .. cfg.reasonable_fangyu, COLOR3B.GREEN)

	-- self.node_list["lan"].text.text = string.format(ToColorStr(Language.Map.TipEquipLan, COLOR3B.D_BLUE), NumberToChinaNumber(cfg.reasonable_lan), cfg.reasonable_lan_num)
	self.node_list["lan"].text.text = string.format(Language.Map.TipEquipLan, NumberToChinaNumber(cfg.reasonable_lan), cfg.reasonable_lan_num)
	-- self.node_list["zi"].text.text = string.format(ToColorStr(Language.Map.TipEquipZi, COLOR3B.PURPLE), NumberToChinaNumber(cfg.reasonable_zi), cfg.reasonable_zi_num)
	
	-- self.node_list["zi"].text.text = string.format(Language.Map.TipEquipZi, NumberToChinaNumber(cfg.reasonable_zi), cfg.reasonable_zi_num)
	
	self.node_list["exp"].text.text = string.format(Language.Map.TipExp, COLOR3B.L_GREEN,CommonDataManager.ConverMoneyByThousand(cfg.reasonable_jingyan))
end

function MonsterInfoTip:FlushPosition(id)
	local t_obj = MapWGCtrl.Instance:GetMonsterIconObj(id)
	if t_obj and t_obj.obj then
		local content = self.node_list["content"]
		local v2 = t_obj.obj.transform.anchoredPosition
		if v2.y < -400 then
			v2.y = v2.y + content.rect.sizeDelta.y
		end
		if v2.x < 300 then
			v2.x = v2.x + content.rect.sizeDelta.x
		end
		content.rect:DOAnchorPos(v2,0.3)
	end
end