local TabType =
{
	Tab_1 = 1, --排行榜前三奖励（结算奖励）.
	Tab_2 = 2, --冲榜奖励.
	--Tab_3 = 3, --挑战次数奖励.
	--Tab_4 = 4, --挑战奖励.
}

TianTiRankRewardTip = TianTiRankRewardTip or BaseClass(SafeBaseView)

function TianTiRankRewardTip:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
		{ sizeDelta = Vector2(812, 574) })
	self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "tianti_rank_reward")
end

function TianTiRankRewardTip:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
	if self.list_reward_view then
		self.list_reward_view:DeleteMe()
		self.list_reward_view = nil
	end

	if self.reward_rank_list then
		self.reward_rank_list:DeleteMe()
		self.reward_rank_list = nil
	end

	self.btn_index = nil
end

function TianTiRankRewardTip:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.ZhuZaiShenDian.ViewNameLianSheng
	self.list_reward_view = AsyncListView.New(TianTiRankRewardRender, self.node_list["ph_item_list"])
	self.list_reward_view:SetStartZeroIndex(true)

	for i = 1, 2 do
		XUI.AddClickEventListener(self.node_list["tab_toggle_" .. i],
			BindTool.Bind(self.SelectBtnType, self, TabType["Tab_" .. i]))

		self.node_list["tab_text_" .. i].text.text = Language.Field1v1.TianTiRewardTab[i]
		self.node_list["tab_select_text_" .. i].text.text = Language.Field1v1.TianTiRewardTab[i]
	end

	self.reward_rank_list = AsyncListView.New(ItemCell, self.node_list.ph_reward_rank_list)
	self.reward_rank_list:SetStartZeroIndex(true)

	self.node_list.tab_toggle_1.toggle.isOn = true
	self.btn_index = 1
end

function TianTiRankRewardTip:SelectBtnType(index, is_on)
	if is_on then
		self.btn_index = index
		self:Flush()
	end
end

function TianTiRankRewardTip:OnFlush()
	if nil == self.btn_index then
		return
	end

	local info = ArenaTianTiWGData.Instance:GetUserinfo()
	local init_btn_index_2, pos_2 = ArenaTianTiWGData.Instance:GetChallengeFieldRankRewardRed()
	self.node_list.tab_btn_remind_2:SetActive(init_btn_index_2)

	local GetRewardData = function (reward_list)
		for k_1, v_1 in pairs(reward_list) do
			if info.rank >= v_1.min_rank_pos + 1 and info.rank <= v_1.max_rank_pos + 1 then
				return v_1.reward_item or v_1.rank_end_reward or {}, v_1.title_item
			end
		end

		return {}
	end

	local str = ""
	local reward_data = {}
	local jump_pos = 0
	if self.btn_index == TabType.Tab_1 then
		reward_data = ArenaTianTiWGData.Instance:GetGradeRankRewardCfb()
		self.list_reward_view:SetStartZeroIndex(false)
	elseif self.btn_index == TabType.Tab_2 then
		reward_data = ArenaTianTiWGData.Instance:GetChallengeFieldRankEndRewardCfg()
		jump_pos = pos_2
	end

	self.list_reward_view:SetDataList(reward_data)
	self.list_reward_view:JumpToIndex(jump_pos, 2)
	self.node_list.tip_desc.text.text = Language.Field1v1["TianTiRewardTitle" .. self.btn_index]

	self.node_list.text_my_rank.text.text = string.format(Language.Field1v1.RankPosDes, info.rank)

	local list_data, title_item = GetRewardData(reward_data)
	if not IsEmptyTable(title_item) then
		local show_data = {}
		for i = 0, GetTableLen(list_data) - 1, 1 do
			show_data[i] = list_data[i]
		end
		table.insert(show_data, 0, title_item[0])
		list_data = show_data
	end
	self.reward_rank_list:SetDataList(list_data)

	self.node_list.text_my_score.text.text = info.new_score
	self.node_list.my_rank_desc.text.text = str
end

function TianTiRankRewardTip:GetField1v1RewardTabType()
	return self.btn_index
end

-----------------------------------------------------
-- TianTiRankRewardRender
-----------------------------------------------------
TianTiRankRewardRender = TianTiRankRewardRender or BaseClass(BaseRender)
function TianTiRankRewardRender:__init()
	self:CreateChild()
end

function TianTiRankRewardRender:__delete()
	for i, v in ipairs(self.item_list) do
		v:DeleteMe()
	end

	self.item_list = {}
	if self.reward_rank_list then
		self.reward_rank_list:DeleteMe()
		self.reward_rank_list = nil
	end
end

function TianTiRankRewardRender:CreateChild()
	self.item_list = {}
	self.reward_rank_list = AsyncListView.New(ItemCell, self.node_list.ph_reward_rank_list)
end

function TianTiRankRewardRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.get_reward, BindTool.Bind(self.ClickGetBtn, self))
	self.type = 1
end

function TianTiRankRewardRender:ClickGetBtn()
	if self.type == TabType.Tab_2 then
		ArenaTiantiWGCtrl.Instance:SendChallengeField(CROSS_CHALLENGE_RANK_LIST_TYPE.CHALLENGE_RANK_LIST_TYPE_RANK_REWARD,
			self.data.index)
	end
end

function TianTiRankRewardRender:OnFlush()
	if not self.data then
		return
	end

	self.node_list.yi_lingqu_image:SetActive(false)
	--self.node_list.yi_lingqu_image_weidacheng:SetActive(false)

	self.type = ArenaTiantiWGCtrl.Instance:GetField1v1RewardTabType()

	local reward_data = {}
	local function InsertReward(data)
		for i = 0, #data do
			if data[i] then
				table.insert(reward_data, data[i])
			end
		end
	end

	if self.data.reward_coin and self.data.reward_coin > 0 then
		table.insert(reward_data,
			{ item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN, num = self.data.reward_coin, is_bind = 1 })
	end

	if self.data.reward_shengwang and self.data.reward_shengwang > 0 then
		local item_shengwang = { item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR, num = self.data.reward_shengwang }
		table.insert(reward_data, item_shengwang)
	end

	local str = ""
	if self.type == TabType.Tab_1 then
		if self.data.min_rank_pos == self.data.max_rank_pos then
			str = string.format(Language.Field1v1.RankPosDes, self.data.min_rank_pos + 1)
		--elseif self.data.min_rank_pos >= 100 then
			--str = string.format(Language.Field1v1.RankLastPosDes, self.data.min_rank_pos + 1)
		else
			str = string.format(Language.Field1v1.RankRandPosDes, self.data.min_rank_pos + 1, self.data.max_rank_pos + 1)
		end

		InsertReward(self.data.reward_item)
	elseif self.type == TabType.Tab_2 then
		--str = string.format(Language.Field1v1.RankPosDes, self.data.rank_pos + 1)

		if self.data.min_rank_pos == self.data.max_rank_pos then
			str = string.format(Language.Field1v1.RankPosDes, self.data.min_rank_pos + 1)
		else
			str = string.format(Language.Field1v1.RankRandPosDes, self.data.min_rank_pos + 1, self.data.max_rank_pos + 1)
		end

		InsertReward(self.data.rank_end_reward)
		if not IsEmptyTable(self.data.title_item) then
			table.insert(reward_data, 1, self.data.title_item[0])
		end
		table.insert(reward_data,{item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR, num = self.data.reputation})
	end

	self.node_list.text_need_score.text.text = self.data.limit_score
	self.node_list.lbl_rank.text.text = str
	self.reward_rank_list:SetDataList(reward_data, 0)
end

-----------------------------------------------------
-- RewardPreviousCellNew
-- -----------------------------------------------------
-- RewardPreviousCellNew = RewardPreviousCellNew or BaseClass(BaseRender)

-- function RewardPreviousCellNew:__init()

-- end

-- function RewardPreviousCellNew:LoadCallBack()
-- 	self.base_cell = ItemCell.New(self.node_list["pos"])
-- end

-- function RewardPreviousCellNew:ReleaseCallBack()
-- 	if self.base_cell then
-- 		self.base_cell:DeleteMe()
-- 		self.base_cell = nil
-- 	end
-- end

-- function RewardPreviousCellNew:OnFlush()
-- 	if nil == self.data then return end
-- 	self.base_cell:SetData(self.data)
-- 	local txt1
-- 	if self.data.num < 10000 then
-- 		txt1 = self.data.num
-- 	else
-- 		txt1 = string.format("%.1f", tonumber(self.data.num) / 10000) .. Language.Common.Wan
-- 	end
-- 	self.base_cell:SetRightBottomText(txt1)
-- end
