ControlBeastsDrawView = ControlBeastsDrawView or BaseClass(SafeBaseView)
local OPERATE_SELECT_STATUS = {
	OPERATE_NOT_SELECT = 0,
	OPERATE_SELECT = 1,
}

local OPERATE_DRAW_MODE = {
	ONE_DRAW_MODE = 1,  -- 单抽
	TEN_DRAW_MODE = 2,	-- 10抽
	FIFTY_DRAW_MODE = 3,	-- 50抽
}

local SP_SHOW_NUM = 3				-- 特殊展示个数

function ControlBeastsDrawView:__init()
	self:SetMaskBg(false, true)
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	
	local common_bundle = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/control_beasts_ui_prefab", "control_beasts_draw_view")
	self:AddViewResource(0, common_bundle, "layout_a3_common_top_panel")
end

function ControlBeastsDrawView:ReleaseCallBack()
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.ControlBeastsPrizeDrawWGView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.contract_bag_type_list then
		for k, v in pairs(self.contract_bag_type_list) do
			v:DeleteMe()
		end
		self.contract_bag_type_list = nil
	end

	if self.oa_act_draw_type_item then
		self.oa_act_draw_type_item:DeleteMe()
		self.oa_act_draw_type_item = nil
	end

	if self.contract_model_list then
		for k, v in pairs(self.contract_model_list) do
			v:DeleteMe()
		end
		self.contract_model_list = nil
	end

	self.contract_select_type = nil
	self.old_contract_select_type = nil
	
	self.operate_mode_id = nil
	self.operate_type = nil
	
	self.show_baodi_list = nil
	self.last_time = nil
	self.day_draw_data = nil

	self.is_select_oa_draw_type_old = nil

	self:ReleaseTeamInfo()

	self:CleanTimeDown()
	self:StopDiscountTime()
end

function ControlBeastsDrawView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.ContralBeasts.TitleName5

	self.skeleton_effect = self.node_list["show_baodi_root"].gameObject:GetComponent(typeof(SkeletonEffect))
	self.bg_raw = self.node_list["RawImage_tongyong"].gameObject:GetComponent(typeof(UnityEngine.UI.RawImage))

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
		show_gold = true, show_bind_gold = true,
		show_cangjin_score = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	-- 抽奖类型Item
	if not self.contract_bag_type_list then
		self.contract_bag_type_list = {}
		for i = 0, 2 do
			local incubate_obj = self.node_list.contract_type_root:FindObj(string.format("contract_type_%d", i))
			if incubate_obj then
				local cell = BeastsPrizeDrawTypeItemRender.New(incubate_obj)
				cell:SetIndex(i)
				cell:SetIsActType(false)
				cell:SetClickCallBack(BindTool.Bind(self.SelectBeastsBagTypeCallBack, self))
				self.contract_bag_type_list[i] = cell
			end
		end
	end

	self.is_select_oa_draw_type = false
	self.oa_draw_type = 0

	-- 运营活动- 幻兽抽奖
	local incubate_obj = self.node_list.contract_type_root:FindObj("contract_type_3")
	if incubate_obj then
		local cur_draw_type_cfg = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()

		local cell = BeastsPrizeDrawTypeItemRender.New(incubate_obj)
		cell:SetIsActType(true)
		cell:SetIndex(cur_draw_type_cfg.type + 1000)
		cell:SetClickCallBack(BindTool.Bind(self.SelectOABeastsBagTypeCallBack, self))
		self.oa_act_draw_type_item = cell
	end

	-- 中间三个展示
	if not self.contract_model_list then
		self.contract_model_list = {}
		for i = 1, SP_SHOW_NUM do
			local model_obj = self.node_list["show_baodi_root"]:FindObj(string.format("contract_model_render_%d", i))
			if model_obj then
				local cell = BeastsPrizeDrawModelRender.New(model_obj)
				cell:SetIndex(i)
				self.contract_model_list[i] = cell
			end
		end
	end

	self:IniTeamInfo()

	XUI.AddClickEventListener(self.node_list.auto_skip_anim, BindTool.Bind1(self.ClickAutoSkillAnim, self))
	XUI.AddClickEventListener(self.node_list.auto_descompose, BindTool.Bind1(self.ClickAutoDescompose, self))
	XUI.AddClickEventListener(self.node_list.operate_one_btn, BindTool.Bind2(self.OperateContractBtn, self, OPERATE_DRAW_MODE.ONE_DRAW_MODE))                 -- 结缘1次
	XUI.AddClickEventListener(self.node_list.operate_ten_btn, BindTool.Bind2(self.OperateContractBtn, self, OPERATE_DRAW_MODE.TEN_DRAW_MODE))                 -- 结缘10次
	XUI.AddClickEventListener(self.node_list.operate_fifty_btn, BindTool.Bind2(self.OperateContractBtn, self, OPERATE_DRAW_MODE.FIFTY_DRAW_MODE))             -- 结缘50次
	XUI.AddClickEventListener(self.node_list.contract_hand_book_btn, BindTool.Bind(self.ClickContractHandBook, self))                 	-- 图鉴
	XUI.AddClickEventListener(self.node_list.contract_draw_record_btn, BindTool.Bind(self.ClickContractDrawRecord, self))            	-- 召唤记录
	XUI.AddClickEventListener(self.node_list.contract_draw_pro_btn, BindTool.Bind(self.ClickContractDrawPro, self))            	-- 召唤记录
	XUI.AddClickEventListener(self.node_list.reward_chanage, BindTool.Bind(self.ClickContractShowReward, self))                 -- 展示保底物品
	XUI.AddClickEventListener(self.node_list.operate_one_const_root, BindTool.Bind(self.ShowOperateItemBtn, self))          -- 结缘物品展示
	XUI.AddClickEventListener(self.node_list.operate_ten_const_root, BindTool.Bind(self.ShowOperateItemBtn, self))          -- 结缘物品展示

	XUI.AddClickEventListener(self.node_list.btn_show_discount_act, BindTool.Bind(self.OnClickShowDiscountBtn, self))
	self.is_show_discount_act_root = false

	for i = 1, 3 do
		XUI.AddClickEventListener(self.node_list["discount_act_icon" .. i], BindTool.Bind(self.OnClickDiscountActBtn, self, i))
	end

	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.ControlBeastsPrizeDrawWGView, self.get_guide_ui_event)
end

function ControlBeastsDrawView:OpenCallBack()
	TaskGuide.Instance:SideTOStopTask(true)
end

function ControlBeastsDrawView:CloseCallBack()
	if self.contract_select_type then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, GuideModuleName.ControlBeastsDrawPrepareView, 0, BURIED_EVENT_PARAM.closeView, self.contract_select_type)
	end
	
	TaskGuide.Instance:SideTOStopTask(false)
	ControlBeastsWGCtrl.Instance:FlushSingleBeastBagInfo(true)
end

-- 抽奖类型选中
function ControlBeastsDrawView:SelectBeastsBagTypeCallBack(beast_bag_type_cell)
	if self.contract_select_type == beast_bag_type_cell.index then
		return
	end

	self.contract_select_type = beast_bag_type_cell.index
	self.is_select_oa_draw_type = false
	
	self:ChangeCurrSelectDrawType()
	self:FlushDrawMessage()
end

function ControlBeastsDrawView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.ControlBeastsPrizeDrawWGView, ACTIVITY_TYPE.RAND_ACTIVITY_OPERA_REQ_CS)
end
-- 刷新
function ControlBeastsDrawView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "all" then
			if v.open_param == "oa_act_draw_type_item" then
				if self.oa_act_draw_type_item then
					self:SelectOABeastsBagTypeCallBack(self.oa_act_draw_type_item)
				end
			else
				self:FlushSelectDrawType()
				self:ChangeCurrSelectDrawType()
				self:FlushDrawMessage()
			end
		elseif k == "oa_draw_result" then
			self:ShowOADrawResult(v)
		elseif k == "oa_act_draw_type_item" then
			if self.oa_act_draw_type_item then
				self:SelectOABeastsBagTypeCallBack(self.oa_act_draw_type_item)
			end
		elseif k == "team_info" then
			self:FlushTeamAward()
		end
    end
end

-- 当没有选中时候 选中红点  默认 只有一个的时候关闭选择栏
function ControlBeastsDrawView:FlushSelectDrawType()
	local show_type_num = 0
	local act_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_OPERA_REQ_CS)

	if act_open and ControlBeastsOADrawWGData.Instance:IsCanShowCurOADrawType() then
		show_type_num = show_type_num + 1
	end

	local default_data_list = ControlBeastsPrizeDrawWGData.Instance:GetDrawTypeCfglist()
	local show_data_list = {}
	if not IsEmptyTable(default_data_list) then
		for k, v in pairs(default_data_list) do
			if ControlBeastsPrizeDrawWGData.Instance:IsCanShowDrawType(v.type) then
				show_type_num = show_type_num + 1

				table.insert(show_data_list, v)
			end
		end
	end

	self.node_list.contract_reward_root:CustomSetActive(show_type_num > 1)
	if self.is_select_oa_draw_type then
		if act_open then
			return
		end
	end

	if self.contract_select_type == nil or (self.is_select_oa_draw_type and not act_open) then
		self.is_select_oa_draw_type = false
		local default_select = -1

		if not IsEmptyTable(show_data_list) then
			for k, v in pairs(show_data_list) do
				if ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRedByType(v.type) then
					self.contract_select_type = v.type
					return
				end

				if default_select < 0 then
					default_select = v.type
				end
			end
		end

		self.contract_select_type = default_select >= 0 and default_select or 0
	end
end

-- 切换界面样式
function ControlBeastsDrawView:ChangeViewStyle()
	if self.is_select_oa_draw_type_old == self.is_select_oa_draw_type then
		return
	end
	self.is_select_oa_draw_type_old = self.is_select_oa_draw_type


	local bg_res, contract_type_bg, reward_chanage_bg, btn_bg, content_res = self:GetDefaultResName()
	local oa_draw_ssr_tips = ""
	if self.is_select_oa_draw_type then
		local cfg = self:GetResCfg()
		if cfg then
			oa_draw_ssr_tips = cfg.oa_draw_ssr_tips or oa_draw_ssr_tips
			bg_res = cfg.RawImage_tongyong or bg_res
			contract_type_bg = cfg.contract_type_bg or contract_type_bg
			reward_chanage_bg = cfg.reward_chanage_bg or reward_chanage_bg
			btn_bg = cfg.contract_draw_record_btn or btn_bg
			content_res = cfg.content or content_res
		end
	end

	-- 提示语
	-- local is_show_oa_draw_ssr_tips = oa_draw_ssr_tips ~= ""
	-- self.node_list.draw_ssr_tips_root:CustomSetActive(not self.is_select_oa_draw_type or not is_show_oa_draw_ssr_tips)
	-- self.node_list.oa_draw_ssr_tips_root:CustomSetActive(self.is_select_oa_draw_type and is_show_oa_draw_ssr_tips)
	self.node_list.image_1:CustomSetActive(not self.is_select_oa_draw_type)
	self.node_list.image_2:CustomSetActive(self.is_select_oa_draw_type)
	if self.is_select_oa_draw_type then
		local bundle, asset = ResPath.GetControlBeastsImg(oa_draw_ssr_tips)
		self.node_list["image_2"].image:LoadSprite(bundle, asset, function()
			self.node_list["image_2"].image:SetNativeSize()
		end)
	end

	local bundle, asset = ResPath.GetRawImagesPNG(bg_res)
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()

			-- skeleton淡出效果
			if not IsNil(self.skeleton_effect) then
				self.skeleton_effect.backGroundImage = self.bg_raw
				self.skeleton_effect.FadeEnabled = true
			end
		end)
	end

	bundle, asset = ResPath.GetRawImagesPNG(contract_type_bg)
	if self.node_list.contract_type_bg then
		self.node_list["contract_type_bg"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["contract_type_bg"].raw_image:SetNativeSize()
		end)
	end

	bundle, asset = ResPath.GetControlBeastsImg(reward_chanage_bg)
	if self.node_list.reward_chanage_bg then
		self.node_list["reward_chanage_bg"].image:LoadSprite(bundle, asset, function()
			self.node_list["reward_chanage_bg"].image:SetNativeSize()
		end)
	end

	bundle, asset = ResPath.GetControlBeastsImg(content_res)
	if self.node_list.content then
		self.node_list["content"].image:LoadSprite(bundle, asset, function()
			self.node_list["content"].image:SetNativeSize()
		end)
	end

	bundle, asset = ResPath.GetControlBeastsImg(btn_bg)
	if self.node_list.contract_draw_record_btn then
		self.node_list["contract_draw_record_btn"].image:LoadSprite(bundle, asset, function()
			self.node_list["contract_draw_record_btn"].image:SetNativeSize()
		end)
	end

	bundle, asset = ResPath.GetControlBeastsImg(btn_bg)
	if self.node_list.contract_draw_pro_btn then
		self.node_list["contract_draw_pro_btn"].image:LoadSprite(bundle, asset, function()
			self.node_list["contract_draw_pro_btn"].image:SetNativeSize()
		end)
	end
end

function ControlBeastsDrawView:GetDefaultResName()
	return "a3_hs_cj_bj", "a3_hs_cj_yqt", "a3_hs_cj_tbt", "a3_hs_cj_dk1", "a3_hs_cj_zt"
end

function ControlBeastsDrawView:GetResCfg()
	local day_draw_data, _ = ControlBeastsOADrawWGData.Instance:GetDrawGradeByType(self.contract_select_type - 1000)
	local color_type = day_draw_data.color_type
	local color_cfg = ControlBeastsOADrawWGData.Instance:GetViewColor(color_type)

	return color_cfg
end

-- 切换当前的选中抽奖类型
function ControlBeastsDrawView:ChangeCurrSelectDrawType()
	-- if self.contract_select_type == nil then
	-- 	self.contract_select_type = 0
	-- end
	self:ChangeViewStyle()

	self:ShowTeamAward()

	local list = ControlBeastsPrizeDrawWGData.Instance:GetDrawTypeCfglist()
	for index = 0, 2 do
		if self.contract_bag_type_list[index] then
			self.contract_bag_type_list[index]:SetData(list[index])
			self.contract_bag_type_list[index]:FlushSelectHl(index == self.contract_select_type and not self.is_select_oa_draw_type)
		end
	end

	local oa_draw_data = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()
	self.oa_act_draw_type_item:SetData(oa_draw_data)
	self.oa_act_draw_type_item:FlushSelectHl(self.is_select_oa_draw_type)
end

-- 切换当前的选中抽奖类型
function ControlBeastsDrawView:FlushDrawMessage()
	self:FlushGradeMessage()
	self:FlushTimeCountDown(self.last_time)

	-- 刷新中间展示模型
	local list = self.show_baodi_list
	if list then
		for i, model_cell in ipairs(self.contract_model_list) do
			model_cell:SetVisible(list[i] ~= nil)
			local spine_id = list[i] and list[i].spine_id or 0
			local show_data

			if self.is_select_oa_draw_type then
				show_data = ControlBeastsOADrawWGData.Instance:GetBeastsDrawSpineShowCfg(spine_id)
			else
				show_data = ControlBeastsWGData.Instance:GetBeastsDrawSpineShowCfg(spine_id)
			end

			local pos_x, pos_y, pos_z = 0, 0, 0
			if show_data then
				pos_x, pos_y, pos_z = show_data.show_pos_x, show_data.show_pos_y, show_data.show_pos_z
			end
			local pos = Vector3(pos_x, pos_y, pos_z)
			local sca = show_data and show_data.show_sca or 1
			local reverse_x = show_data and show_data.reverse_x == 1
			model_cell:ModelVisible(list[i] ~= nil, pos, sca, reverse_x)
			model_cell:SetData(list[i])
		end
	end

	self:FlushDrawModeMessage()
	self:FlushOperateStatus()
	self:FlushDiscountAct()
	
	-- 幸运值
	local grade = self.day_draw_data and self.day_draw_data.grade or 1
	local recycle_times = self.day_draw_data and self.day_draw_data.recycle_times or 1000
	local cfg, draw_data
	if self.is_select_oa_draw_type then
		local draw_type_data = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()
		cfg = ControlBeastsOADrawWGData.Instance:GetDrawGradeConvertByTypeGrade(draw_type_data.type, grade)
		draw_data = ControlBeastsOADrawWGData.Instance:GetOABeastDrawItemInfoByType(draw_type_data.type)
		recycle_times = self.day_draw_data.recycle_show_times or 100
	else
		cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawGradeConvertByTypeGrade(self.contract_select_type, grade)
		draw_data = ControlBeastsPrizeDrawWGData.Instance:GetDrawItemByType(self.contract_select_type)
	end

	-- local cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawGradeConvertByTypeGrade(self.contract_select_type, grade)
	-- local draw_data = ControlBeastsPrizeDrawWGData.Instance:GetDrawItemByType(self.contract_select_type)

	if cfg and draw_data then
		local need_lucky = cfg.need_lucky or 1
		self.node_list.contract_operate_red:CustomSetActive(draw_data.lucky >= need_lucky)
		self.node_list.contract_operate_num.text.text = string.format("%d/%d", draw_data.lucky, need_lucky)

		local arrived_num = draw_data.draw_times % recycle_times
		local last_times = recycle_times - arrived_num
		self.node_list.draw_ssr_tips.text.text = last_times
	end

	-- 自动分解标记
	local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
	local auto_despose_color = base_cfg and base_cfg.auto_despose_color or 1
	local color = ITEM_COLOR[auto_despose_color]
	local color_str = Language.ContralBeasts.PrizeDrawAutoDesposeColorNmae[auto_despose_color]
	local str = ToColorStr(string.format("%s%s", color_str, Language.ContralBeasts.TitleName), color)
	-- local item_data = ControlBeastsPrizeDrawWGData.Instance:GetDrawGradeConvertByTypeGrade(self.contract_select_type, grade)
	self.node_list.auto_descompose_txt.text.text = string.format(Language.ContralBeasts.PrizeDrawAutoDesposeDesc, str)
	-- self.node_list.reward_chanage:CustomSetActive(item_data ~= nil)
	self.node_list.reward_chanage:CustomSetActive(cfg ~= nil)
end

-- 获取当前的展示的数据
function ControlBeastsDrawView:FlushGradeMessage()
	if not self.contract_select_type then
		return
	end

	if self.old_contract_select_type ~= self.contract_select_type then
		local day_draw_data, last_time
		local show_baodi_list
		if self.is_select_oa_draw_type then
			day_draw_data, last_time = ControlBeastsOADrawWGData.Instance:GetDrawGradeByType(self.contract_select_type - 1000)
			show_baodi_list = ControlBeastsOADrawWGData.Instance:GetDrawShowListByCfg(day_draw_data)

			local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_OPERA_REQ_CS)
			if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then 
				last_time = activity_info.end_time - TimeWGCtrl.Instance:GetServerTime()
			end
		else
			day_draw_data, last_time = ControlBeastsPrizeDrawWGData.Instance:GetDrawGradeByType(self.contract_select_type)
			show_baodi_list = ControlBeastsPrizeDrawWGData.Instance:GetDrawShowListByCfg(day_draw_data)
		end

		self.show_baodi_list = show_baodi_list
		self.last_time = last_time
		self.day_draw_data = day_draw_data

		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.viewClick, GuideModuleName.ControlBeastsDrawPrepareView, 0, BURIED_EVENT_PARAM.openView, self.contract_select_type)
	end

	self.old_contract_select_type = self.contract_select_type
end

-- 设置抽奖数据
function ControlBeastsDrawView:FlushDrawModeMessage()
	if not self.contract_select_type then
		return
	end
	local base_cfg
	local single_mode_cfg, ten_mode_cfg, fifty_mode_cfg
	local discount_cfg = ControlBeastsPrizeDrawWGData.Instance:GetCurDrawDiscountCfg()
	local show_discount = not self.is_select_oa_draw_type and not IsEmptyTable(discount_cfg)

	self.node_list.draw_discount_mid_root:CustomSetActive(show_discount)

	if show_discount then
		self.node_list.desc_discount_num.text.text = discount_cfg.discount or ""
		self:ShowDiscountTime()
	else
		self:StopDiscountTime()
	end

	if self.is_select_oa_draw_type then
		base_cfg = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()
		single_mode_cfg = ControlBeastsOADrawWGData.Instance:GetDrawModeCfgByType(base_cfg.type, OPERATE_DRAW_MODE.ONE_DRAW_MODE)
		ten_mode_cfg = ControlBeastsOADrawWGData.Instance:GetDrawModeCfgByType(base_cfg.type, OPERATE_DRAW_MODE.TEN_DRAW_MODE)
		fifty_mode_cfg = ControlBeastsOADrawWGData.Instance:GetDrawModeCfgByType(base_cfg.type, OPERATE_DRAW_MODE.FIFTY_DRAW_MODE)
	else
		base_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawTypeCfgByType(self.contract_select_type)
		single_mode_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawModeCfgByType(base_cfg.type, OPERATE_DRAW_MODE.ONE_DRAW_MODE)
		ten_mode_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawModeCfgByType(base_cfg.type, OPERATE_DRAW_MODE.TEN_DRAW_MODE)
		fifty_mode_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawModeCfgByType(base_cfg.type, OPERATE_DRAW_MODE.FIFTY_DRAW_MODE)
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()

	-- 物品图标
	if base_cfg then
		local item_icon = ItemWGData.Instance:GetItemIconByItemId(base_cfg.cost_item_id) --拥有的数量
		local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id)
		
		if item_icon then
			local bundle, asset = ResPath.GetItem(item_icon)
			self.node_list["operate_one_const_icon"].image:LoadSpriteAsync(bundle, asset, function()
				self.node_list["operate_one_const_icon"].image:SetNativeSize()
			end)
			self.node_list["operate_ten_const_icon"].image:LoadSpriteAsync(bundle, asset, function()
				self.node_list["operate_ten_const_icon"].image:SetNativeSize()
			end)
			self.node_list["operate_fifty_const_icon"].image:LoadSpriteAsync(bundle, asset, function()
				self.node_list["operate_fifty_const_icon"].image:SetNativeSize()
			end)
		end

		-- 单抽
		if single_mode_cfg then
			self.node_list["operate_one"]:SetActive(role_level >= single_mode_cfg.show_level)

			if show_discount then
				local cost_item_num = show_discount and math.ceil(single_mode_cfg.cost_item_num * discount_cfg.discount / 10) or single_mode_cfg.cost_item_num

				if cost_item_num == single_mode_cfg.cost_item_num then
					self.node_list.operate_one_const_text.text.text = string.format("<color=#FFFFFF>%s/%s</color>", item_num, single_mode_cfg.cost_item_num) 
				else
					self.node_list.operate_one_const_text.text.text = string.format("<color=#FFFFFF>%s/</color><s><color=#FFFFFF>(%s)</color></s><color=#FFFFFF>%s</color>", item_num, single_mode_cfg.cost_item_num, cost_item_num) 
				end
			else
				self.node_list.operate_one_const_text.text.text = string.format("<color=#FFFFFF>%s/%s</color>", item_num, single_mode_cfg.cost_item_num) 
			end
	
			local is_one_red = false
			if self.is_select_oa_draw_type then
				is_one_red = ControlBeastsOADrawWGData.Instance:IsCanOADrawHasEnoughCost(base_cfg.type, OPERATE_DRAW_MODE.ONE_DRAW_MODE)
			else
				is_one_red = ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRedByMode(self.contract_select_type, OPERATE_DRAW_MODE.ONE_DRAW_MODE)
			end

			self.node_list.operate_one_red:CustomSetActive(is_one_red)
			UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.operate_one_const_root.rect)
		end

		-- 十抽
		if ten_mode_cfg then
			self.node_list["operate_ten"]:SetActive(role_level >= ten_mode_cfg.show_level)
			self.node_list.operate_ten_discount:CustomSetActive(show_discount)

			if show_discount then
				local cost_item_num = show_discount and math.ceil(ten_mode_cfg.cost_item_num * discount_cfg.discount / 10) or ten_mode_cfg.cost_item_num
				if cost_item_num == ten_mode_cfg.cost_item_num then
					self.node_list.operate_ten_const_text.text.text = string.format("<color=#FFFFFF>%s/%s</color>", item_num, ten_mode_cfg.cost_item_num)
				else
					self.node_list.operate_ten_const_text.text.text = string.format("<color=#FFFFFF>%s/</color><s><color=#FFFFFF>(%s)</color></s><color=#FFFFFF>%s</color>", item_num, ten_mode_cfg.cost_item_num, cost_item_num) 
				end

				self.node_list.desc_operate_ten_discount.text.text = string.format(Language.ContralBeasts.ControlDrawDrawDiscountStr, discount_cfg.discount)
			else
				self.node_list.operate_ten_const_text.text.text = string.format("<color=#FFFFFF>%s/%s</color>", item_num, ten_mode_cfg.cost_item_num)
			end

			local is_ten_red = false
			if self.is_select_oa_draw_type then
				is_ten_red = ControlBeastsOADrawWGData.Instance:IsCanOADrawHasEnoughCost(base_cfg.type, OPERATE_DRAW_MODE.TEN_DRAW_MODE)
			else
				is_ten_red = ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRedByMode(self.contract_select_type, OPERATE_DRAW_MODE.TEN_DRAW_MODE)
			end

			self.node_list.operate_ten_red:CustomSetActive(is_ten_red)
			UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.operate_ten_const_root.rect)
		end

		-- 五十抽
		if fifty_mode_cfg then
			self.node_list["operate_fifty"]:SetActive(role_level >= fifty_mode_cfg.show_level)
			self.node_list.operate_fifty_discount:CustomSetActive(show_discount)
			
			if show_discount then
				local cost_item_num = show_discount and math.ceil(fifty_mode_cfg.cost_item_num * discount_cfg.discount / 10) or fifty_mode_cfg.cost_item_num
				if cost_item_num == fifty_mode_cfg.cost_item_num then
					self.node_list.operate_fifty_const_text.text.text = string.format("<color=#FFFFFF>%s/%s</color>", item_num, fifty_mode_cfg.cost_item_num)
				else
					self.node_list.operate_fifty_const_text.text.text = string.format("<color=#FFFFFF>%s/</color><s><color=#FFFFFF>(%s)</color></s><color=#FFFFFF>%s</color>", item_num, fifty_mode_cfg.cost_item_num, cost_item_num) 
				end

				self.node_list.desc_operate_fifty_discount.text.text = string.format(Language.ContralBeasts.ControlDrawDrawDiscountStr, discount_cfg.discount)
			else
				self.node_list.operate_fifty_const_text.text.text = string.format("<color=#FFFFFF>%s/%s</color>", item_num, fifty_mode_cfg.cost_item_num)
			end

			local is_fifty_red = false
			if self.is_select_oa_draw_type then
				is_fifty_red = ControlBeastsOADrawWGData.Instance:IsCanOADrawHasEnoughCost(base_cfg.type, OPERATE_DRAW_MODE.FIFTY_DRAW_MODE)
			else
				is_fifty_red = ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRedByMode(self.contract_select_type, OPERATE_DRAW_MODE.FIFTY_DRAW_MODE)
			end

			self.node_list.operate_fifty_red:CustomSetActive(is_fifty_red)
			UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.operate_fifty_const_root.rect)
		end
	end

	-- local base_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawTypeCfgByType(self.contract_select_type)
	-- if base_cfg then
	-- 	local item_icon = ItemWGData.Instance:GetItemIconByItemId(base_cfg.cost_item_id) --拥有的数量
	-- 	local item_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id)
	-- 	if item_icon then
	-- 		local bundle, asset = ResPath.GetItem(item_icon)
	-- 		self.node_list["operate_one_const_icon"].image:LoadSpriteAsync(bundle, asset, function()
	-- 			self.node_list["operate_one_const_icon"].image:SetNativeSize()
	-- 		end)
	-- 		self.node_list["operate_ten_const_icon"].image:LoadSpriteAsync(bundle, asset, function()
	-- 			self.node_list["operate_ten_const_icon"].image:SetNativeSize()
	-- 		end)
	-- 	end

	-- 	local mode_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawModeCfgByType(self.contract_select_type, OPERATE_DRAW_MODE.ONE_DRAW_MODE)
	-- 	if mode_cfg then
	-- 		self.node_list.operate_one_const_text.text.text = string.format("%s/%s", item_num, mode_cfg.cost_item_num) 
	-- 		local is_one_red = ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRedByMode(self.contract_select_type, OPERATE_DRAW_MODE.ONE_DRAW_MODE)
	-- 		self.node_list.operate_one_red:CustomSetActive(is_one_red)
	-- 		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.operate_one_const_root.rect)
	-- 	end

	-- 	mode_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawModeCfgByType(self.contract_select_type, OPERATE_DRAW_MODE.TEN_DRAW_MODE)
	-- 	if mode_cfg then
	-- 		self.node_list.operate_ten_const_text.text.text = string.format("%s/%s", item_num, mode_cfg.cost_item_num) 
	-- 		local is_ten_red = ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRedByMode(self.contract_select_type, OPERATE_DRAW_MODE.TEN_DRAW_MODE)
	-- 		self.node_list.operate_ten_red:CustomSetActive(is_ten_red)
	-- 		UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.operate_ten_const_root.rect)
	-- 	end
	-- end
end

function ControlBeastsDrawView:ShowDiscountTime()
	local cur_discouny_cfg = ControlBeastsPrizeDrawWGData.Instance:GetCurDrawDiscountCfg()
	
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local day_diff = cur_discouny_cfg.end_day - open_day
	local end_time = TimeWGCtrl:NowDayTimeStart(server_time) + day_diff + 24 * 3600 

	self:StopDiscountTime()
	if end_time > server_time then
		if self.node_list.desc_discount_end_time then
			self.node_list.desc_discount_end_time.text.text = string.format(Language.ContralBeasts.ControlDrawDrawEndTimeStr, TimeUtil.FormatSecondDHM9( end_time - server_time )) 
		end
		CountDownManager.Instance:AddCountDown("ControlBeastsDrawViewDiscount",
            function(elapse_time, total_time)
				if self.node_list.desc_discount_end_time then
					self.node_list.desc_discount_end_time.text.text = string.format(Language.ContralBeasts.ControlDrawDrawEndTimeStr, TimeUtil.FormatSecondDHM9( total_time - elapse_time ))
				end
            end,
            function()
				if self.node_list.desc_discount_end_time then
					self.node_list.desc_discount_end_time.text.text = ""
				end
            end,
		end_time, nil, 1)
	end
end

function ControlBeastsDrawView:StopDiscountTime()
	if CountDownManager.Instance:HasCountDown("ControlBeastsDrawViewDiscount") then
		CountDownManager.Instance:RemoveCountDown("ControlBeastsDrawViewDiscount")
	end
end

function ControlBeastsDrawView:FlushDiscountAct()
	local discount_cfg = ControlBeastsPrizeDrawWGData.Instance:GetCurDrawDiscountCfg()
	local show_discount = not self.is_select_oa_draw_type and not IsEmptyTable(discount_cfg)

	self.node_list.draw_discount_act_root:CustomSetActive(show_discount)

	if show_discount then
		for i = 1, 3 do
			local icon_bundle, icon_asset = discount_cfg["icon_bundle" .. i], discount_cfg["icon_asset" .. i]

			if nil ~= icon_bundle and "" ~= icon_bundle and nil ~= icon_asset and "" ~= icon_asset then
				self.node_list["discount_act_icon" .. i].image:LoadSprite(icon_bundle, icon_asset, function ()
					self.node_list["discount_act_icon" .. i].image:SetNativeSize()
				end)
				self.node_list["discount_act" .. i]:CustomSetActive(true)
			else
				self.node_list["discount_act" .. i]:CustomSetActive(false)
			end
		end
	end
end

function ControlBeastsDrawView:OnClickShowDiscountBtn()
	self.is_show_discount_act_root = not self.is_show_discount_act_root
	self:DoShowDiscountBtnAct()
end

function ControlBeastsDrawView:DoShowDiscountBtnAct()
	if self.node_list.discount_act_root then
		local width = self.is_show_discount_act_root and 412 or 0
		self.node_list.discount_act_root.rect:DOSizeDelta(Vector2(width, 96), 0.5)
		local from_alpha = self.is_show_discount_act_root and 0 or 1
		local to_alpha = self.is_show_discount_act_root and 1 or 0
		self.node_list.discount_act_root.canvas_group:DoAlpha(from_alpha, to_alpha, 0.5):OnComplete(function ()
			self.node_list.discount_act_root.canvas_group.interactable = self.is_show_discount_act_root
			self.node_list.discount_act_root.canvas_group.blocksRaycasts = self.is_show_discount_act_root
		end)

		local scale = self.is_show_discount_act_root and Vector3(-1, 1, 1) or Vector3(1, 1, 1)
		self.node_list.discount_act_root_arrow.transform.localScale = scale

		local pos_x = self.is_show_discount_act_root and 311 or 65
		self.node_list.discount_act_root_arrow.rect:DOAnchorPosX(pos_x, 0.4)
	end
end

function ControlBeastsDrawView:OnClickDiscountActBtn(act_index)
	local discount_cfg = ControlBeastsPrizeDrawWGData.Instance:GetCurDrawDiscountCfg()
	local jump_path = discount_cfg["path" .. act_index]

	if jump_path and "" ~= jump_path then 
		FunOpen.Instance:OpenViewNameByCfg(jump_path)
	end
end

-- 设置操作数据
function ControlBeastsDrawView:FlushOperateStatus()
	-- local curr_draw_anim, curr_draw_despose = self:GetOperateStatus()
	local curr_draw_anim, curr_draw_despose = ControlBeastsPrizeDrawWGData.Instance:GetOperateStatus(self.contract_select_type)
	self.node_list.auto_skip_anim_select:CustomSetActive(curr_draw_anim == OPERATE_SELECT_STATUS.OPERATE_SELECT)
	self.node_list.auto_descompose_select:CustomSetActive(curr_draw_despose == OPERATE_SELECT_STATUS.OPERATE_SELECT)
end

-- -- 获取操作保存数据
-- function ControlBeastsDrawView:GetOperateStatus()
-- 	local role_id = RoleWGData.Instance:GetOriginUid()
-- 	local curr_draw_anim = OPERATE_SELECT_STATUS.OPERATE_NOT_SELECT
-- 	local draw_skip_anim_str = string.format("beast_prize_draw_skip_anim_%s_%s", self.contract_select_type, role_id)
-- 	curr_draw_anim = PlayerPrefsUtil.GetInt(draw_skip_anim_str, curr_draw_anim)

-- 	local curr_draw_despose = OPERATE_SELECT_STATUS.OPERATE_NOT_SELECT
-- 	local draw_draw_despose_str = string.format("beast_prize_draw_despose_%s_%s", self.contract_select_type, role_id)
-- 	curr_draw_despose = PlayerPrefsUtil.GetInt(draw_draw_despose_str, curr_draw_despose)

-- 	return curr_draw_anim, curr_draw_despose
-- end

-- -- 获取操作保存数据
-- function ControlBeastsDrawView:SaveOperateStatus(draw_anim, draw_despose)
-- 	local role_id = RoleWGData.Instance:GetOriginUid()

-- 	if draw_anim ~= nil then
-- 		local draw_skip_anim_str = string.format("beast_prize_draw_skip_anim_%s_%s", self.contract_select_type, role_id)
-- 		PlayerPrefsUtil.SetInt(draw_skip_anim_str, draw_anim)
-- 	end

-- 	if draw_despose ~= nil then
-- 		local draw_draw_despose_str = string.format("beast_prize_draw_despose_%s_%s", self.contract_select_type, role_id)
-- 		PlayerPrefsUtil.SetInt(draw_draw_despose_str, draw_despose)
-- 	end
-- end

-- 展示抽奖结果
function ControlBeastsDrawView:ShowDrawResult(protocol)
	if self.operate_mode_id == nil or self.operate_type == nil then
		return
	end

	local again_func = function ()
		self:OperateContractBtn(self.operate_mode_id)
	end

	local other_info = {}
	local type_index = self.operate_mode_id
	if type_index > 3 then
		type_index = 3
	end

	other_info.again_text = Language.TreasureHunt.BtnText[type_index]
	local base_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawTypeCfgByType(self.operate_type)
	if base_cfg then
		other_info.stuff_id = base_cfg.cost_item_id
		local mode_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawModeCfgByType(self.operate_type, self.operate_mode_id)
		local cur_discount_cfg = ControlBeastsPrizeDrawWGData.Instance:GetCurDrawDiscountCfg()
		local cfg_need_num = mode_cfg and mode_cfg.times or 0
		local need_num = not IsEmptyTable(cur_discount_cfg) and math.ceil(cfg_need_num * cur_discount_cfg.discount / 10) or cfg_need_num

		other_info.times = need_num
		other_info.spend = base_cfg.cost_gold
	end

	-------------------------------通用奖励展示------------------------------
	-- other_info.total_price_desc1 = Language.ContralBeasts.TotalPriceDesc
	-- local item_list = {}      -- 去除大奖得物品列表
	-- local best_data = {} 
	-- local best_item_list = {}  -- 大奖列表 

	-- for k, v in pairs(protocol.result_item_list) do
	-- 	if ControlBeastsPrizeDrawWGData.Instance:IsBigRewardItem(self.operate_type, v.item_id) then
	-- 		table.insert(best_item_list, v)
	-- 	else
	-- 		table.insert(item_list, v)
	-- 	end
	-- end

	-- if not IsEmptyTable(best_item_list) then
	-- 	if #best_item_list == 1 then
	-- 		best_data.item_id = best_item_list[1].item_id
	-- 	else
	-- 		local item_ids = {}

	-- 		for k, v in pairs(best_item_list) do
	-- 			item_ids[k] = v.item_id
	-- 		end

	-- 		best_data.item_ids = item_ids
	-- 	end

	-- 	other_info.best_data = best_data
	-- end

	-- local show_reward_func = function()
	-- 	ControlBeastsPrizeDrawWGData.Instance:GetSetIsPlayingDraw(0)
	-- 	ControlBeastsWGCtrl.Instance:ClosePrizeDrawPrepareView()
	-- 	self:CheckDesposeDrawResult(protocol)	-- 抽奖结果自动分解
	-- 	TipWGCtrl.Instance:ShowGetValueReward(item_list, again_func, other_info, false)
	-- end
	-----------------------------------------------------


	-- 播放动画
	-- local curr_draw_anim, curr_draw_despose = self:GetOperateStatus()
	local curr_draw_anim, curr_draw_despose = ControlBeastsPrizeDrawWGData.Instance:GetOperateStatus(self.contract_select_type)
	other_info.can_auto_decompose = curr_draw_despose

	local show_reward_func = function()
		ControlBeastsPrizeDrawWGData.Instance:GetSetIsPlayingDraw(0)
		ControlBeastsWGCtrl.Instance:ClosePrizeDrawPrepareView()
		self:CheckDesposeDrawResult(protocol)	-- 抽奖结果自动分解

		ControlBeastsWGCtrl.Instance:ShowControlBeastsDrawResultView(self.contract_select_type, protocol.result_item_list, again_func, other_info, false, nil)
		--展示结果时再显示Tips
		ControlBeastsWGCtrl.Instance:SetBeastAddItemShow(true)
		ItemWGData.Instance:HandleDelayNoticeNow()
	end

	if curr_draw_anim == OPERATE_SELECT_STATUS.OPERATE_SELECT then-- or ControlBeastsOADrawWGData.Instance:IsDrawSkipTween() then
		show_reward_func()
	else
		local prepare_draw_data = { callback = show_reward_func, result_item_list = protocol.result_item_list }
		ControlBeastsWGCtrl.Instance:OpenPrizeDrawPrepareView(prepare_draw_data)
	end
end

-- 自动分解
function ControlBeastsDrawView:CheckDesposeDrawResult(protocol)
	-- local curr_draw_anim, curr_draw_despose = self:GetOperateStatus()
	local curr_draw_anim, curr_draw_despose = ControlBeastsPrizeDrawWGData.Instance:GetOperateStatus(self.contract_select_type)

	if curr_draw_despose == OPERATE_SELECT_STATUS.OPERATE_NOT_SELECT then
		return
	end

	local bag_id_list = {}
	local base_cfg = ControlBeastsWGData.Instance:GetBaseCfg()
	local auto_despose_color = base_cfg and base_cfg.auto_despose_color or 1
	for k, v in pairs(protocol.result_item_list) do
		local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(v.item_id)

		if beast_cfg and beast_cfg.beast_color <= auto_despose_color then
			table.insert(bag_id_list, v.bag_index)
		end
	end

	if bag_id_list and #bag_id_list > 0 then
		ControlBeastsWGCtrl.Instance:SendCSBeastBreakListReq(bag_id_list)
	end
end

-- oa抽奖结果
function ControlBeastsDrawView:ShowOADrawResult(protocol)
	if self.operate_mode_id == nil or self.operate_type == nil then
		return
	end

	local again_func = function ()
		self:OperateContractBtn(self.operate_mode_id)
	end

	-------------------------------通用奖励展示------------------------------
	-- local other_info = {}
	-- local draw_type_cfg = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()
	-- local draw_mode_cfg = ControlBeastsOADrawWGData.Instance:GetDrawModeCfgByType(draw_type_cfg.type, self.operate_mode_id)

	-- local other_info = {
	-- 	again_text = Language.TreasureHunt.BtnText[self.operate_mode_id] or Language.TreasureHunt.BtnText[0],

	-- 	stuff_id = draw_type_cfg.cost_item_id,
	-- 	times = draw_mode_cfg.times,
	-- 	spend = draw_type_cfg.cost_gold,
	-- }
	-- other_info.total_price_desc1 = Language.ContralBeasts.TotalPriceDesc
	-- local item_list = {}      -- 去除大奖得物品列表
	-- local best_data = {} 
	-- local best_item_list = {}  -- 大奖列表 

	-- for k, v in pairs(protocol.result_item_list) do
	-- 	if ControlBeastsOADrawWGData.Instance:IsBigRewardItem(self.operate_type, v.item_id) then
	-- 		table.insert(best_item_list, v)
	-- 	else
	-- 		table.insert(item_list, v)
	-- 	end
	-- end

	-- if not IsEmptyTable(best_item_list) then
	-- 	if #best_item_list == 1 then
	-- 		best_data.item_id = best_item_list[1].item_id
	-- 	else
	-- 		local item_ids = {}

	-- 		for k, v in pairs(best_item_list) do
	-- 			item_ids[k] = v.item_id
	-- 		end

	-- 		best_data.item_ids = item_ids
	-- 	end

	-- 	other_info.best_data = best_data
	-- end
	
	-- local show_reward_func = function()
	-- 	ControlBeastsPrizeDrawWGData.Instance:GetSetIsPlayingDraw(0)
	-- 	ControlBeastsWGCtrl.Instance:ClosePrizeDrawPrepareView()
	-- 	self:CheckDesposeDrawResult(protocol)	-- 抽奖结果自动分解
	-- 	TipWGCtrl.Instance:ShowGetValueReward(item_list, again_func, other_info, false)
	-- end
	------------------------------------------------------------------------

	-- 播放动画
	-- local curr_draw_anim, curr_draw_despose = self:GetOperateStatus()
	local curr_draw_anim, curr_draw_despose = ControlBeastsPrizeDrawWGData.Instance:GetOperateStatus(self.contract_select_type)

	local show_reward_func = function()
		ControlBeastsPrizeDrawWGData.Instance:GetSetIsPlayingDraw(0)
		ControlBeastsWGCtrl.Instance:ClosePrizeDrawPrepareView()
		self:CheckDesposeDrawResult(protocol)	-- 抽奖结果自动分解

		local draw_type_cfg = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()
		local draw_mode_cfg = ControlBeastsOADrawWGData.Instance:GetDrawModeCfgByType(draw_type_cfg.type, self.operate_mode_id)

		local other_info = {
			again_text = Language.TreasureHunt.BtnText[self.operate_mode_id] or Language.TreasureHunt.BtnText[0],
			stuff_id = draw_type_cfg.cost_item_id,
			times = draw_mode_cfg.times,
			spend = draw_type_cfg.cost_gold,
			can_auto_decompose = curr_draw_despose,
			extra_cost_type = draw_type_cfg.cost_type,
		}

		ControlBeastsWGCtrl.Instance:ShowControlBeastsDrawResultView(self.contract_select_type, protocol.result_item_list, again_func, other_info, false, nil)
		--展示结果时再显示Tips
		ControlBeastsWGCtrl.Instance:SetBeastAddItemShow(true)
		ItemWGData.Instance:HandleDelayNoticeNow()
	end

	if curr_draw_anim == OPERATE_SELECT_STATUS.OPERATE_SELECT then --or ControlBeastsOADrawWGData.Instance:IsDrawSkipTween() then
		show_reward_func()
	else
		local prepare_draw_data = { callback = show_reward_func, result_item_list = protocol.result_item_list }
		ControlBeastsWGCtrl.Instance:OpenPrizeDrawPrepareView(prepare_draw_data)
	end
end

-----------------活动时间倒计时-------------------
function ControlBeastsDrawView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("control_beasts_prize_draw_down") then
		CountDownManager.Instance:RemoveCountDown("control_beasts_prize_draw_down")
	end
end

function ControlBeastsDrawView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > 0 then
		self:CleanTimeDown()
		self.node_list["refresh_time_txt"].text.text = Language.FuBen.UpdateCountDown..TimeUtil.FormatTimeLanguage2(invalid_time)
		CountDownManager.Instance:AddCountDown("control_beasts_prize_draw_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), nil, invalid_time, 1)
	end
end

function ControlBeastsDrawView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		if self.is_select_oa_draw_type then
			self.node_list["refresh_time_txt"].text.text = string.format(Language.Common.ActTimeEnd, TimeUtil.FormatTimeLanguage2(valid_time))
		else
			self.node_list["refresh_time_txt"].text.text = Language.FuBen.UpdateCountDown..TimeUtil.FormatTimeLanguage2(valid_time)
		end
	end
end

function ControlBeastsDrawView:OnComplete()
    self.node_list["refresh_time_txt"].text.text = ""
	self:Flush()
end
-----------------------------------------------------------------------------------------------------
-- 跳过动画点击
function ControlBeastsDrawView:ClickAutoSkillAnim()
	-- local curr_draw_anim, _ = self:GetOperateStatus()
	local curr_draw_anim, _ = ControlBeastsPrizeDrawWGData.Instance:GetOperateStatus(self.contract_select_type)
	if curr_draw_anim == OPERATE_SELECT_STATUS.OPERATE_NOT_SELECT then
		curr_draw_anim = OPERATE_SELECT_STATUS.OPERATE_SELECT
	else
		curr_draw_anim = OPERATE_SELECT_STATUS.OPERATE_NOT_SELECT
	end

	-- self:SaveOperateStatus(curr_draw_anim)
	ControlBeastsPrizeDrawWGData.Instance:SaveOperateStatus(curr_draw_anim, nil, self.contract_select_type)
	self:FlushOperateStatus()
end

-- 自动分解点击
function ControlBeastsDrawView:ClickAutoDescompose()
	-- local _, curr_draw_despose = self:GetOperateStatus()
	local _, curr_draw_despose = ControlBeastsPrizeDrawWGData.Instance:GetOperateStatus(self.contract_select_type)
	if curr_draw_despose == OPERATE_SELECT_STATUS.OPERATE_NOT_SELECT then
		curr_draw_despose = OPERATE_SELECT_STATUS.OPERATE_SELECT
	else
		curr_draw_despose = OPERATE_SELECT_STATUS.OPERATE_NOT_SELECT
	end

	-- self:SaveOperateStatus(nil, curr_draw_despose)
	ControlBeastsPrizeDrawWGData.Instance:SaveOperateStatus(nil, curr_draw_despose, self.contract_select_type)
	self:FlushOperateStatus()
end

-- 抽奖
function ControlBeastsDrawView:OperateContractBtn(mode_id, no_need_tip)
	if not self.contract_select_type then
		return
	end

	if self.is_select_oa_draw_type then
		local oa_draw_type_cfg = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()
		
		if oa_draw_type_cfg then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(oa_draw_type_cfg.cost_item_id) --拥有的数量
			local mode_cfg = ControlBeastsOADrawWGData.Instance:GetDrawModeCfgByType(oa_draw_type_cfg.type, mode_id)
			local need_num = mode_cfg and mode_cfg.cost_item_num or 0

			if not ControlBeastsWGData.Instance:CheckCanBeastDraw(need_num) then
				-- SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip22)
				RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
				RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.BAG_WAY_BEAST)
				return
			end

			local is_enough = has_num >= need_num
			local score = YanYuGeWGData.Instance:GetCurScore()

			-- if not is_enough and (oa_draw_type_cfg.cost_type == 2 and (score < oa_draw_type_cfg.cost_gold * (need_num - has_num))) then
			-- 	TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = oa_draw_type_cfg.cost_item_id})
			-- else
			if no_need_tip then
				self:SendOADrawOperateContractReq(oa_draw_type_cfg.type, mode_id)
			else
				-- local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
				-- local item_name = ItemWGData.Instance:GetItemName(show_item) or ""

				local tips_data = {
					item_id = oa_draw_type_cfg.cost_item_id,
					price = oa_draw_type_cfg.cost_gold,
					draw_count = need_num,
					has_checkbox = true,
					checkbox_str = string.format("oa_beasts_contract_draw%d%d", oa_draw_type_cfg.type, mode_id),
					-- gold_str = item_name,
					cost_type = oa_draw_type_cfg.cost_type,
				}
				TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind3(self.SendOADrawOperateContractReq, self, oa_draw_type_cfg.type, mode_id), nil)
			end
			-- end

			-- if oa_draw_type_cfg.cost_gold <= 0 and not is_enough then
			-- 	TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = oa_draw_type_cfg.cost_item_id})
			-- else
			-- 	if no_need_tip then
			-- 		self:SendOADrawOperateContractReq(oa_draw_type_cfg.type, mode_id)
			-- 	else
			-- 		local tips_data = {
			-- 			item_id = oa_draw_type_cfg.cost_item_id,
			-- 			price = oa_draw_type_cfg.cost_gold,
			-- 			draw_count = need_num,
			-- 			has_checkbox = true,
			-- 			checkbox_str = string.format("oa_beasts_contract_draw%d%d", oa_draw_type_cfg.type, mode_id)
			-- 		}
			-- 		TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind3(self.SendOADrawOperateContractReq, self, oa_draw_type_cfg.type, mode_id), nil)
			-- 	end
			-- end

			LimitTimeGiftWGCtrl.Instance:CheckNeedDrawRewardPopupGift(LIMIT_TIME_GIFT_POPUP_DRAW_TYPE.POPUP_DRAW_BEASTS)
		end
	else
		local base_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawTypeCfgByType(self.contract_select_type)

		if base_cfg then
			local has_num = ItemWGData.Instance:GetItemNumInBagById(base_cfg.cost_item_id) --拥有的数量
			local mode_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawModeCfgByType(self.contract_select_type, mode_id)

			local discount_cfg = ControlBeastsPrizeDrawWGData.Instance:GetCurDrawDiscountCfg()	
			local cfg_need_num = mode_cfg and mode_cfg.cost_item_num or 0

			local need_num = not IsEmptyTable(discount_cfg) and math.ceil(cfg_need_num * discount_cfg.discount / 10) or cfg_need_num
	
			if not ControlBeastsWGData.Instance:CheckCanBeastDraw(need_num) then
				RoleBagWGData.Instance:SetRoleBagCleanFlag(true)
				RoleBagWGCtrl.Instance:OpenRoleBagCleanTips(KNAPSACK_TYPE.BAG_WAY_BEAST)
				-- SysMsgWGCtrl.Instance:ErrorRemind(Language.ContralBeasts.ErrorTip22)
				return
			end
	
			local tips_data = {}
			tips_data.item_id = base_cfg.cost_item_id
			tips_data.price = base_cfg.cost_gold
			tips_data.draw_count = need_num
			tips_data.has_checkbox = true
			tips_data.checkbox_str = string.format("beasts_contract_draw%d%d", self.contract_select_type, mode_id)
	
			local is_enough = has_num >= need_num
			if tips_data.price <= 0 and not is_enough then
				TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = tips_data.item_id})
			else
				if no_need_tip then -- 引导时不需要提示框
					self:SendOperateContractReq(self.contract_select_type, mode_id)
				else
					TipWGCtrl.Instance:OpenTipsDrawRewardView(tips_data, BindTool.Bind3(self.SendOperateContractReq, self, self.contract_select_type, mode_id), nil)
				end
			end
	
			LimitTimeGiftWGCtrl.Instance:CheckNeedDrawRewardPopupGift(LIMIT_TIME_GIFT_POPUP_DRAW_TYPE.POPUP_DRAW_BEASTS)
		end
	end
end

function ControlBeastsDrawView:SendOperateContractReq(select_type, mode_id)
	ControlBeastsWGCtrl.Instance:SetBeastAddItemShow(false)
	ControlBeastsPrizeDrawWGData.Instance:GetSetIsPlayingDraw(1)
	ControlBeastsWGCtrl.Instance:SendOperateTypeBeastDraw(select_type, mode_id)
	self.operate_mode_id = mode_id
	self.operate_type = select_type
end

function ControlBeastsDrawView:SendOADrawOperateContractReq(type, mode_id)
	ControlBeastsWGCtrl.Instance:SetBeastAddItemShow(false)
	ControlBeastsPrizeDrawWGData.Instance:GetSetIsPlayingDraw(1)
	ControlBeastsOADrawWGCtrl.Instance:SendOABeastDrawOpera(OA_BEAST_DRAW_OPERATE_TYPE.DRAW, type, mode_id)
	self.operate_mode_id = mode_id
	self.operate_type = type
end

function ControlBeastsDrawView:ClickContractHandBook()
	ViewManager.Instance:Open(GuideModuleName.ControlBeastsView, TabIndex.beasts_book)
	self:Close()
end

function ControlBeastsDrawView:ClickContractDrawRecord()
	if not self.contract_select_type then
		return
	end

	local record_data = {}

	if self.is_select_oa_draw_type then
		local draw_type_data = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()
		record_data = ControlBeastsOADrawWGData.Instance:GetDrawRecordByType(draw_type_data.type)
	else
		record_data = ControlBeastsPrizeDrawWGData.Instance:GetDrawRecordByType(self.contract_select_type)
	end

	TipWGCtrl.Instance:OpenTipsRewardRecordView(record_data)
end

-- 召唤概率
function ControlBeastsDrawView:ClickContractDrawPro()
	local pro_table
	if self.is_select_oa_draw_type then
		pro_table = ControlBeastsOADrawWGData.Instance:GetDrawProListByType(self.contract_select_type - 1000)
	else
		pro_table = ControlBeastsPrizeDrawWGData.Instance:GetDrawProListByType(self.contract_select_type)
	end
	TipWGCtrl.Instance:OpenTipsRewardProView(pro_table)
end

function ControlBeastsDrawView:ClickContractShowReward()
	if not self.contract_select_type then
		return
	end

	local grade = self.day_draw_data and self.day_draw_data.grade or 1
	local cfg, draw_data
	local draw_type_data = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()

	if self.is_select_oa_draw_type then
		cfg = ControlBeastsOADrawWGData.Instance:GetDrawGradeConvertByTypeGrade(draw_type_data.type, grade)
		draw_data = ControlBeastsOADrawWGData.Instance:GetOABeastDrawItemInfoByType(draw_type_data.type)
	else
		cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawGradeConvertByTypeGrade(self.contract_select_type, grade)
		draw_data = ControlBeastsPrizeDrawWGData.Instance:GetDrawItemByType(self.contract_select_type)
	end

	-- local cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawGradeConvertByTypeGrade(self.contract_select_type, grade)
	-- local draw_data = ControlBeastsPrizeDrawWGData.Instance:GetDrawItemByType(self.contract_select_type)
	if cfg and draw_data then
		local need_lucky = cfg.need_lucky or 1
		if draw_data.lucky >= need_lucky then
			if self.is_select_oa_draw_type then
				ControlBeastsOADrawWGCtrl.Instance:SendOABeastDrawOpera(OA_BEAST_DRAW_OPERATE_TYPE.DRAW_CONVERT, draw_type_data.type)
			else
				-- 请求领奖
				ControlBeastsWGCtrl.Instance:SendOperateTypeBeastDrawConvert(self.contract_select_type)
			end
		else
			local show_id = cfg and cfg.item_id
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = show_id})
		end
	end
end

-- 展示消耗道具
function ControlBeastsDrawView:ShowOperateItemBtn()
	if not self.contract_select_type then
		return
	end

	local base_cfg
	if self.is_select_oa_draw_type then
		base_cfg = ControlBeastsOADrawWGData.Instance:GetCurDrawTypeCfg()
	else
		base_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawTypeCfgByType(self.contract_select_type)
	end

	-- local base_cfg = ControlBeastsPrizeDrawWGData.Instance:GetDrawTypeCfgByType(self.contract_select_type)
	if base_cfg then
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = base_cfg.cost_item_id})
	end
end

-- 任务引导
function ControlBeastsDrawView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.BeastsDraw then
		return self.node_list["operate_ten_btn"], BindTool.Bind(self.OperateContractBtn, self, OPERATE_DRAW_MODE.TEN_DRAW_MODE, true)
	end

	return self.node_list[ui_name]
end

function ControlBeastsDrawView:SelectOABeastsBagTypeCallBack(beast_bag_type_cell)
	if self.contract_select_type == beast_bag_type_cell.index then
		return
	end

	self.is_select_oa_draw_type = true
	self.contract_select_type = beast_bag_type_cell.index

	self:ChangeCurrSelectDrawType()
	self:FlushDrawMessage()
end

------------------------ 通天展示组队奖励 -------------------------------
function ControlBeastsDrawView:IniTeamInfo()
	self.show_team_task = nil

	if not self.player_item_list then
		self.player_item_list = {}
		for i = 1, 3 do
			self.player_item_list[i] = TogetherPlayerItem.New(self.node_list["player_item_" .. i])
		end
	end

	if not self.team_reward_list then
        self.team_reward_list = AsyncListView.New(ItemCell, self.node_list["team_award_list"])
		self.team_reward_list:SetStartZeroIndex(true)
	end

	XUI.AddClickEventListener(self.node_list.goto_active_btn, BindTool.Bind(self.GotoActivity, self))
end

function ControlBeastsDrawView:ReleaseTeamInfo()
	self.show_team_task = nil

	if self.player_item_list then
		for k, v in pairs(self.player_item_list) do
			v:DeleteMe()
		end
		self.player_item_list = nil
	end

	if self.team_reward_list then
		self.team_reward_list:DeleteMe()
		self.team_reward_list = nil
	end
end

function ControlBeastsDrawView:ShowTeamAward()
	local is_show_team_award = self.is_select_oa_draw_type
	if is_show_team_award then
		local is_act_open = WorldTreasureWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLD_TREASURE)
		local is_shop_open = WorldTreasureWGData.Instance:GetGradeIndexIsOpen(TabIndex.tcdb_together)
		is_show_team_award = is_act_open and is_shop_open
	end
	
	if is_show_team_award then
		local task_list = WorldTreasureWGData.Instance:GetTogetherTaskDataList()
		local task = task_list[1]
		if task and task.reward_flag == 0 then
			self.show_team_task = task
		else
			-- 已领完
			is_show_team_award = false
		end
	end

	self.node_list.team_award:CustomSetActive(is_show_team_award)
	self:FlushTeamAward()
end

function ControlBeastsDrawView:FlushTeamAward()
	if not self.show_team_task then
		return
	end

	local consume_num = WorldTreasureWGData.Instance:GetTogetherUseItemCount()
	local target_num = tonumber(self.show_team_task.cfg.target)

	local str = Language.ContralBeasts.TeamAwardTip1
	if consume_num < target_num then
		str = string.format(Language.ContralBeasts.TeamAwardTip2, target_num - consume_num)
	end

	self.node_list.team_award_tip.text.text = str

	local member_data_list = WorldTreasureWGData.Instance:GetTogetherMemberList()
	for i = 1, #self.player_item_list do
		self.player_item_list[i]:SetData(member_data_list[i])
	end

	local award_list = self.show_team_task.cfg.reward_item
	self.team_reward_list:SetDataList(award_list)
end

-- 跳转到通天降临组队活动
function ControlBeastsDrawView:GotoActivity()
	ViewManager.Instance:Open(GuideModuleName.WorldTreasureView, TabIndex.tcdb_together)
end
------------------------ 通天展示组队奖励end -------------------------------

------------------------------------------------------
--背包幻兽抽奖类型item
------------------------------------------------------
BeastsPrizeDrawTypeItemRender = BeastsPrizeDrawTypeItemRender or BaseClass(BaseRender)

function BeastsPrizeDrawTypeItemRender:__init()
	self.is_act_type = false
end

function BeastsPrizeDrawTypeItemRender:__delete()
	self.is_act_type = nil
end

function BeastsPrizeDrawTypeItemRender:SetIsActType(status)
	self.is_act_type = status
end

function BeastsPrizeDrawTypeItemRender:OnFlush()
	if not self.data then
		self:SetVisible(false)
		return
	end
	
	local show_icon_id = self.data.show_icon

	if self.is_act_type then
		local draw_grade_cfg = ControlBeastsOADrawWGData.Instance:GetDrawGradeByType(self.data.type)
		show_icon_id = draw_grade_cfg and draw_grade_cfg.show_icon or nil

		self.node_list.contract_type_txt.text.text = draw_grade_cfg and draw_grade_cfg.type_name or self.data.type_name
	else
		self.node_list.contract_type_txt.text.text = self.data.type_name
	end

	if show_icon_id and "" ~= show_icon_id then
		-- local bundle, asset = ResPath.GetItem(ItemWGData.Instance:GetItemIconByItemId(show_icon_id))
		local bundle, asset = ResPath.GetControlBeastsImg(show_icon_id)
		
		self.node_list.icon.image:LoadSprite(bundle, asset, function ()
			self.node_list.icon.image:SetNativeSize()
		end)
	end

	local can_show = false

	if self.is_act_type then
		local open_time = ControlBeastsOADrawWGData.Instance:IsCanShowDrawType(self.data.type)
		local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_OPERA_REQ_CS)
		can_show = open_time and act_is_open
	else
		can_show = ControlBeastsPrizeDrawWGData.Instance:IsCanShowDrawType(self.data.type)
	end

	self:SetVisible(can_show)

	if can_show then
		local is_red = false
		if self.is_act_type then
			is_red = ControlBeastsOADrawWGData.Instance:GetOABeastDrawModeRed() > 0
		else
			is_red = ControlBeastsPrizeDrawWGData.Instance:GetCurBeastDrawModeRedByType(self.data.type)
		end

		self.node_list.red:CustomSetActive(is_red)
	end
end

-- 刷新选中状态
function BeastsPrizeDrawTypeItemRender:FlushSelectHl(is_select)
	self.node_list.select:CustomSetActive(is_select)
end

------------------------------------------------------
--幻兽抽奖模型展示item
------------------------------------------------------
BeastsPrizeDrawModelRender = BeastsPrizeDrawModelRender or BaseClass(BaseRender)

function BeastsPrizeDrawModelRender:LoadCallBack()
	if not self.star_list then
		self.star_list = {}
		for i = 1, 5 do
			self.star_list[i] = self.node_list["star" .. i]
		end
	end

	-- 模型展示
	if not self.show_model then
		self.show_model = OperationActRender.New(self.node_list["model_display"])
		self.show_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)

		--self.show_model = RoleModel.New()
		--self.show_model:SetUISceneModel(self.node_list["model_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
	end
end

function BeastsPrizeDrawModelRender:ReleaseCallBack()
	if self.show_model then
		self.show_model:DeleteMe()
		self.show_model = nil
	end

	self.star_list = nil
	self.model_res_id = nil
	self.show_model_status = nil
	self.default_pos = nil
	self.default_sca = nil
end

function BeastsPrizeDrawModelRender:OnFlush()
	if not self.data then 
		return 
	end

	-- local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(self.data.item_id)
	-- if self.model_res_id ~= res_id then
	-- 	self.model_res_id = res_id
	-- 	local bundle, asset = ResPath.GetBeastsModel(res_id)
	-- 	self.show_model:SetMainAsset(bundle, asset)
	-- end

	if self.model_res_id ~= self.data.spine_id then
		self.model_res_id = self.data.spine_id
		local bundle, asset = ResPath.GetBeastsSpine(self.data.spine_id)
		local display_data = {}
		display_data.bundle_name = bundle
		display_data.asset_name = asset
		display_data.render_type = OARenderType.Prefab
		self.show_model:SetData(display_data)
	end

	local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.data.item_id)
	if beast_cfg then
		local bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
		self.node_list.color_img.image:LoadSprite(bundle, asset, function()
			self.node_list.color_img.image:SetNativeSize()
		end)

		if BEAST_EFFECT_COLOR[beast_cfg.beast_color] then
			bundle, asset = ResPath.GetUIEffect(BEAST_EFFECT_COLOR[beast_cfg.beast_color])
			self.node_list.color_img:ChangeAsset(bundle, asset)
		end

		local raw_str = string.format("a3_hs_cj_zt%d", beast_cfg.beast_color)
		self.node_list["color_bg_img"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(raw_str))

		local star_res_list = GetSpecialStarImgResByStar3(beast_cfg.beast_star)
		local no_have_star = "a3_ty_xx_zc0"
		for k,v in pairs(self.star_list) do
			v:CustomSetActive(star_res_list[k] ~= no_have_star)
			v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
		end

		local bundle, asset = ResPath.GetControlBeastsImg(string.format("a3_hs_bq_%d", beast_cfg.beast_element))
		self.node_list.element_img.image:LoadSprite(bundle, asset, function()
			self.node_list.element_img.image:SetNativeSize()
		end)

		self.node_list.name.text.text = beast_cfg.beast_name
	end

	self:SetModelPos()
end

function BeastsPrizeDrawModelRender:ModelVisible(state, default_pos, default_sca, reverse_x)
	self.show_model_status = state
	self.default_pos = default_pos
	self.default_sca = default_sca
	self.reverse_x = reverse_x
end

function BeastsPrizeDrawModelRender:FlushModel()

end

function BeastsPrizeDrawModelRender:SetModelPos()
	if self.show_model then
		self.show_model:SetVisible(self.show_model_status)
		--self.show_model:SetUSAdjustmentNodeLocalScale(self.default_sca or 1)
		local pos_x = self.default_pos and self.default_pos.x or 0
		local pos_y = self.default_pos and self.default_pos.y or 0
		local pos_z = self.default_pos and self.default_pos.z or 0
		--self.show_model:SetUSAdjustmentNodeLocalPosition(pos_x, pos_y, pos_z)
		RectTransform.SetAnchoredPositionXY(self.node_list["model_display"].rect, pos_x, pos_y)
		local scal_x = self.reverse_x and self.default_sca * -1 or self.default_sca
		Transform.SetLocalScaleXYZ(self.node_list["model_display"].transform, scal_x, self.default_sca, self.default_sca)
	end
end