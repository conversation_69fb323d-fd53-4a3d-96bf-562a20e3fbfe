require("game/fight_soul/fight_soul_enum")
require("game/fight_soul/fight_soul_slot")
require("game/fight_soul/fight_soul_wg_data")
require("game/fight_soul/fight_soul_cfg_wg_data")
require("game/fight_soul/fight_soul_bone_wg_data")
require("game/fight_soul/fight_soul_cuizhuo_wg_data")

-- views
require("game/fight_soul/views/fight_soul_render")
require("game/fight_soul/views/fight_soul_view")
require("game/fight_soul/views/fight_soul_train_view")
require("game/fight_soul/views/fight_soul_compose_view")
require("game/fight_soul/views/fight_soul_bone_view")
require("game/fight_soul/views/fight_soul_select_list_view")
require("game/fight_soul/views/fight_soul_stuff_select_view")
require("game/fight_soul/views/fight_soul_bone_stuff_select_view")
require("game/fight_soul/views/fight_soul_bone_select_view")
require("game/fight_soul/views/fight_soul_exp_pool_view")
require("game/fight_soul/views/fight_soul_suit_tips")
require("game/fight_soul/views/fight_soul_bone_strength")
require("game/fight_soul/views/fight_soul_bone_compose")
require("game/fight_soul/views/fight_soul_unlock_tips")
require("game/fight_soul/views/fight_soul_getway_tips")
require("game/fight_soul/views/fight_soul_bone_height_tips")
require("game/fight_soul/views/fight_soul_batch_view")
require("game/fight_soul/views/fight_soul_cuizhuo_view")
require("game/fight_soul/views/fight_soul_zhuotie_view")


FightSoulWGCtrl = FightSoulWGCtrl or BaseClass(BaseWGCtrl)

function FightSoulWGCtrl:__init()
	if FightSoulWGCtrl.Instance then
		error("[FightSoulWGCtrl]:Attempt to create singleton twice!")
	end
	FightSoulWGCtrl.Instance = self

    self.data = FightSoulWGData.New()
    self.view = FightSoulView.New(GuideModuleName.FightSoulView)
	self.select_list_view = FightSoulSelectListView.New()
	self.exp_pool_view = FightSoulExpPoolView.New(GuideModuleName.FightSoulExpPoolView)
	self.select_stuff_view = FightSoulStuffSelectView.New()
	self.select_bone_view = FightSoulBoneSelectView.New()
	self.suit_tips = FightSoulSuitTips.New()
	self.bone_strength_view = FightSoulBoneStrengthView.New()
	self.bone_compose_view = FightSoulBoneComposeView.New()
	self.select_bone_stuff_view = FightSoulBoneStuffSelectView.New()
	self.unlock_tips = FightSoulUnlockTips.New()
	self.get_way_tips = FightSoulGetWayTips.New()
	self.bone_height_tips = FightSoulBoneHeightTips.New()
	self.bone_batch_view = FightSoulBoneBatchView.New()
	self.zhuotie_view = FightSoulZhuoTieView.New()

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function FightSoulWGCtrl:__delete()
    self.data:DeleteMe()
	self.data = nil

	self.view:DeleteMe()
	self.view = nil

	self.select_list_view:DeleteMe()
	self.select_list_view = nil

	self.exp_pool_view:DeleteMe()
	self.exp_pool_view = nil

	self.select_stuff_view:DeleteMe()
	self.select_stuff_view = nil

	self.select_bone_view:DeleteMe()
	self.select_bone_view = nil

	self.suit_tips:DeleteMe()
	self.suit_tips = nil

	self.bone_strength_view:DeleteMe()
	self.bone_strength_view = nil

	self.bone_compose_view:DeleteMe()
	self.bone_compose_view = nil

	self.select_bone_stuff_view:DeleteMe()
	self.select_bone_stuff_view = nil

	self.unlock_tips:DeleteMe()
	self.unlock_tips = nil

	self.get_way_tips:DeleteMe()
	self.get_way_tips = nil

	self.bone_height_tips:DeleteMe()
	self.bone_height_tips = nil

	self.bone_batch_view:DeleteMe()
	self.bone_batch_view = nil

	if self.zhuotie_view then
		self.zhuotie_view:DeleteMe()
		self.zhuotie_view = nil
	end

	if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end

	if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

	FightSoulWGCtrl.Instance = nil
end

function FightSoulWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSSiXiangSystemOpera)
	self:RegisterProtocol(CSFightSoulCompose)
	self:RegisterProtocol(CSSiXiangBoneBatchReq)
	self:RegisterProtocol(SCSiXiangSlotInfo, "OnSCSiXiangSlotInfo")
	self:RegisterProtocol(SCSiXiangBagInfo, "OnSCSiXiangBagInfo")
	self:RegisterProtocol(SCFightSoulBoneSlotInfo, "OnSCFightSoulBoneSlotInfo")
	self:RegisterProtocol(SCFightSoulBoneBagInfo, "OnSCFightSoulBoneBagInfo")

	--淬琢
	self:RegisterProtocol(CSSiXiangQuenchOperate)
	self:RegisterProtocol(SCSiXiangQuenchInfo, "OnSCSiXiangQuenchInfo")
	self:RegisterProtocol(SCSiXiangQuenchItemUpdate, "OnSCSiXiangQuenchItemUpdate")
	self:RegisterProtocol(SCSiXiangQuenchCutIronUpdate, "OnSCSiXiangQuenchCutIronUpdate")
end

function FightSoulWGCtrl:RegisterAllEvents()
	self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)

	self.role_data_change = BindTool.Bind1(self.OnRoleLevelChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
end

function FightSoulWGCtrl:GetViewShowIndex()
	if self.view:IsOpen() then
		return self.view.show_index
	end

	return 0
end

-- 战魂操作请求
function FightSoulWGCtrl:ReqFightSoulOp(op_type, param1, param2, param3)
    -- print_error("【-----战魂操作请求----】：", op_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSiXiangSystemOpera)
	protocol.op_type = op_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

-- 战魂元神、魂骨升品升星请求
-- @param bag_index: 升品升星的元神或魂骨
-- @param bag_index_list: 升品升星消耗材料
function FightSoulWGCtrl:ReqFightSoulComposeOp(op_type, bag_index, bag_index_list)
	-- print_error("【----战魂融合请求-----】：", op_type, bag_index, bag_index_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSFightSoulCompose)
	protocol.op_type = op_type or 0
	protocol.bag_index = bag_index or -1
	protocol.bag_index_list = bag_index_list or{}
	protocol:EncodeAndSend()
end

local reason_1 = {
	[0] = "下发原因-登录",
	"下发原因-主动请求",
	"下发原因-使用经验",
	"下发原因-添加经验",
	"下发原因-穿戴",
	"下发原因-经验池升级",
	"下发原因-出战",
	"下发原因-分解",
	"下发原因-替换",
	"下发原因-升级成功",
	"下发原因-突破成功",
	"下发原因-槽位解锁",
}
-- 战魂位更新
function FightSoulWGCtrl:OnSCSiXiangSlotInfo(protocol)
    -- print_error("【----战魂位更新-----】：", reason_1[protocol.send_reason])
	local old_exp = self.data:GetExp()
	local old_out_fight_index = self.data:GetOutFightIndex()
	local old_slot_list = self.data:GetAllFightSoulSlotList() or {}
	-- 第一次上阵，自动出战
	local is_first_put = true
	for k,v in pairs(old_slot_list) do
		if v:GetIsWear() then
			is_first_put = false
			break
		end
	end

    self.data:UpdateFightSoulSlotInfo(protocol)

	local need_flush_big_view = true
	local need_fire_remind = true

	--登录
	if protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.LOGIN then
		self.data:ClearSingleComposeRemindCache()
		RemindManager.Instance:Fire(RemindName.FightSoul_Compose)
		RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
    -- 经验池升级
    elseif protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.EXP_POOL_UPLEVEL then
		if self.exp_pool_view:IsOpen() then
			self.exp_pool_view:Flush(nil, "play_effect")
		end
    -- 战魂exp增加
	elseif protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.ADD_EXP then
		if self.exp_pool_view:IsOpen() then
			self.exp_pool_view:Flush()
		end

		local add_exp = self.data:GetExp() - old_exp
		if add_exp > 0 then
			local item_id = FightSoulWGData.Instance:GetFightSoulExpItemId()
			local item_cfg, item_type = ItemWGData.Instance:GetItemConfig(item_id)
			if item_cfg then
				local str = string.format(Language.Chat.GetItemMsg, item_id, add_exp)
				ChatWGCtrl.Instance:AddSystemMsg(str)
			end
		end
	-- 战魂位数据变换
	elseif protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.PUT_ON
	or protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.REPLACE then
		local show_index = self.view:GetShowIndex()

		if self.view:IsOpen() and (show_index == TabIndex.fight_soul_bone or show_index == TabIndex.fight_soul_cuizhuo) then
			self.view:Flush()
		end

		SysMsgWGCtrl.Instance:ErrorRemind(Language.FightSoul.WearSuccessDesc)
		RemindManager.Instance:Fire(RemindName.FightSoul_Bone)

		if is_first_put then
			local new_slot_list = self.data:GetAllFightSoulSlotList() or {}
			for k,v in pairs(new_slot_list) do
				if v:GetIsWear() then
					self:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.SIXIANG_CALL, v:GetSlotIndex())
					break
				end
			end
		end

	-- 使用经验（这个下发后 会接着下发升级成功）
	elseif protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.USE_EXP then
		need_flush_big_view = false
		need_fire_remind = false
	-- 升级成功
	elseif protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.UPLEVEL then
		need_flush_big_view = false
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.fight_soul_train, "uplevel_success")
		end
	-- 突破成功
	elseif protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.UPGRADE then
		need_flush_big_view = false
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.fight_soul_train, "break_success")
		end
	-- 出战
	elseif protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.OUT_FIGHT then
		need_flush_big_view = false
		local is_out_fight = false
		local new_out_fight_index = self.data:GetOutFightIndex()
		if old_out_fight_index ~= -1 and new_out_fight_index == -1 then
			local fight_soul = self.data:GetFightSoulSlot(old_out_fight_index)
			if fight_soul then
				local item_cfg = ItemWGData.Instance:GetItemConfig(fight_soul:GetItemId())
				if item_cfg then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.RestSuccessDesc,
													ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])))
				end
			end
		else
			local fight_soul = self.data:GetFightSoulSlot(new_out_fight_index)
			if fight_soul then
				local item_cfg = ItemWGData.Instance:GetItemConfig(fight_soul:GetItemId())
				if item_cfg then
					local name_str = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
					local skill_name = fight_soul:GetSkillName()
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FightSoul.FightSuccessDesc,
												name_str, ITEM_COLOR[item_cfg.color], skill_name))
				end
			end
			is_out_fight = true
		end

		if self.view:IsOpen() then
			self.view:Flush(nil, "out_fight", {is_out_fight = is_out_fight})
		end

		RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
	-- 槽位解锁
	elseif protocol.send_reason == FIGHT_SOUL_SLOT_SEND_REASON.SLOT_UNLOCK then
		need_flush_big_view = false
		if self.view:IsOpen() then
			self.view:Flush()
		end
		RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
    end

	if need_flush_big_view then
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.fight_soul_train)
		end
	end

	if need_fire_remind then
		RemindManager.Instance:Fire(RemindName.FightSoul_Train)
	end
end

local reason_2 = {
	[0] = "下发原因-全部",
	"下发原因-获得",
	"下发原因-移除",
	"下发原因-升级or突破",
	"下发原因-融合成功",
	"下发原因-四象召唤获得",
}
function FightSoulWGCtrl:OnSCSiXiangBagInfo(protocol)
	-- print_error("【----战魂背包更新-----】：", reason_2[protocol.send_reason])
	self.data:UpdateFightSoulBagInfo(protocol)

	local item_cfg
	if protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.ADD_ITEM					--获得
	 	or protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.SIXIANG_CALL_GET then		--四象召唤获得
		if protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.ADD_ITEM then
			for k,v in pairs(protocol.bag_change_list) do
				item_cfg = ItemWGData.Instance:GetItemConfig(v.item_data.item_id)
				if item_cfg then
					local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, 1))
				end
			end
		end
		if self.view:IsOpen() then
			self.view:SetTabVisible()
		end

		self.data:ClearSingleComposeRemindCache()
		RemindManager.Instance:Fire(RemindName.FightSoul_Train)
		RemindManager.Instance:Fire(RemindName.FightSoul_Compose)
		RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
	elseif protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.UPLEVEL_OR_UPGRADE then	--升级or突破
		RemindManager.Instance:Fire(RemindName.FightSoul_Train)

	elseif protocol.send_reason == FIGHT_SOUL_BAG_SEND_REASON.UP_COLOR_STAR then		--融合成功
		local item_list = {}
		for k,v in pairs(protocol.bag_change_list) do
			local item_data = self.data:GetFightSoulItemByBagIndex(v.index)
			local temp_data = FightSoulWGData.FightSoulBagItemData()
			for i,j in pairs(temp_data) do
				if item_data[i] then
					temp_data[i] = item_data[i]
				end
			end
			table.insert(item_list, temp_data)
		end

		if not IsEmptyTable(item_list) then
			TipWGCtrl.Instance:ShowGetReward(nil, item_list, nil, nil, nil)
		end

		self.data:ClearSingleComposeRemindCache()
		RemindManager.Instance:Fire(RemindName.FightSoul_Train)
		RemindManager.Instance:Fire(RemindName.FightSoul_Compose)
		RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
	end

	if self.view:IsOpen() then
		self.view:Flush()
	end
end

local reason_3 = {
	[0] = "下发原因-登录",
	"下发原因-主动请求",
	"下发原因-穿戴、替换",
	"下发原因-升级成功",
	"下发原因-卸下",
}
function FightSoulWGCtrl:OnSCFightSoulBoneSlotInfo(protocol)
	-- print_error("【----魂骨位更新-----】：", reason_3[protocol.send_reason], protocol)
	self.data:UpdateBoneSlotInfo(protocol)
	if protocol.send_reason == FIGHT_SOUL_BONE_SLOT_SEND_REASON.PUT_ON then			-- 穿戴、替换
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.fight_soul_bone)
		end

		if self.bone_compose_view:IsOpen() then
			self.bone_compose_view:Flush(nil, "bag_change")
		end
	elseif protocol.send_reason == FIGHT_SOUL_BONE_SLOT_SEND_REASON.PUT_DOWN then	-- 卸下
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.fight_soul_bone)
		end
	elseif protocol.send_reason == FIGHT_SOUL_BONE_SLOT_SEND_REASON.UPLEVEL then	-- -升级成功
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.fight_soul_bone, "bone_strength")
		end

		if self.bone_strength_view:IsOpen() then
			self.bone_strength_view:Flush()
			self.bone_strength_view:PlayStengthEffect()
		end
	else
		if self.view:IsOpen() then
			self.view:Flush(TabIndex.fight_soul_bone)
		end
	end

	RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
end

local reason_4 = {
	[0] = "下发原因-全部",
	"下发原因-获得",
	"下发原因-移除",
	"下发原因-融合成功",
	"下发原因-四象召唤获得",
}
function FightSoulWGCtrl:OnSCFightSoulBoneBagInfo(protocol)
	-- print_error("【----魂骨背包更新-----】：", reason_4[protocol.send_reason])
	local is_wear

	self.data:UpdateBoneBagInfo(protocol)
	if protocol.send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.ADD_ITEM then
		for k,v in pairs(protocol.bag_change_list) do
			local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
			if item_cfg then
				local item_name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
				SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.SysRemind.AddItem, item_name, 1))
			end
		end
	-- 融合成功
	elseif protocol.send_reason == FIGHT_SOUL_BONE_BAG_SEND_REASON.UP_COLOR_STAR then
		local item_list = {}
		local max_part_num = FIGHT_SOUL_BONE_TYPE.MAX
		for k,v in pairs(protocol.bag_change_list) do
			local item_data = self.data:GetBoneDataByBagIndex(v.bag_index)
			local temp_data = FightSoulWGData.FightSoulBoneBagItemData()
			for i,j in pairs(temp_data) do
				if item_data[i] then
					temp_data[i] = item_data[i]
				end
			end

			temp_data.sort_index = item_data.star * 100 + (item_data.suit_type + 1) * 10 + (max_part_num - item_data.bone_part)
			table.insert(item_list, temp_data)
			if not is_wear then
				is_wear = self.data:GetBoneBagIndexIsWear(v.bag_index)
			end
		end

		self:FlushBoneBatchView("from_protocol")
		if not IsEmptyTable(item_list) then
			table.sort(item_list, SortTools.KeyUpperSorter("sort_index"))
			TipWGCtrl.Instance:ShowGetReward(nil, item_list, nil, nil, nil, true)
		end
	end

	if self.view:IsOpen() then
		local flush_key = is_wear and "all" or "bone_list_compose"
		self.view:Flush(TabIndex.fight_soul_bone, flush_key)
	end

	if self.bone_compose_view:IsOpen() then
		self.bone_compose_view:Flush(nil, "bag_change")
	end

	RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
end

function FightSoulWGCtrl:FlushComposeBagCheckState()
	if self.view:IsOpen() then
		self.view:Flush(TabIndex.fight_soul_compose, "select_stuff_cb")
	end
end

function FightSoulWGCtrl:FlushBoneComposeBagCheckState()
	if self.bone_compose_view:IsOpen() then
		self.bone_compose_view:Flush(nil, "select_stuff_cb")
	end
end

function FightSoulWGCtrl:OpenSelectViewBySlot(slot)
	self.select_list_view:SetDataAndOpen(slot)
end

-- 经验池界面
function FightSoulWGCtrl:OpenExpPoolView()
	self.exp_pool_view:Open()
end

function FightSoulWGCtrl:OpenStuffSelectView(stuff_type, select_data, meet_all_list)
	self.select_stuff_view:SetDataAndOpen(stuff_type, select_data, meet_all_list)
end

function FightSoulWGCtrl:OpenBoneStuffSelectView(stuff_type, select_data, meet_all_list)
	self.select_bone_stuff_view:SetDataAndOpen(stuff_type, select_data, meet_all_list)
end

function FightSoulWGCtrl:OpenBoneSelectView(meet_list, slot, part)
	self.select_bone_view:SetDataAndOpen(meet_list, slot, part)
end

function FightSoulWGCtrl:OpenSuitTips(suit_data)
	self.suit_tips:SetDataAndOpen(suit_data)
end

function FightSoulWGCtrl:OpenFightSoulUnlockTips(slot)
	self.unlock_tips:SetDataAndOpen(slot)
end

function FightSoulWGCtrl:OpenFightSoulGetWayTips(get_way_list)
	self.get_way_tips:SetDataAndOpen(get_way_list)
end

function FightSoulWGCtrl:CloseFightSoulGetWayTips()
	self.get_way_tips:Close()
end

function FightSoulWGCtrl:OpenFightSoulBoneHeightTips(select_data, sure_callback, cur_show_data, next_show_data)
	self.bone_height_tips:SetDataAndOpen(select_data, sure_callback, cur_show_data, next_show_data)
end

function FightSoulWGCtrl:OpenBoneStrengthView(slot)
	self.bone_strength_view:SetDataAndOpen(slot)
end

function FightSoulWGCtrl:OpenBoneComposeView(fs_type, bag_list)
	self.bone_compose_view:SetDataAndOpen(fs_type, bag_list)
end

function FightSoulWGCtrl:OpenBoneBatchView(fs_type)
	self.bone_batch_view:SetDataAndOpen(fs_type)
end

function FightSoulWGCtrl:FlushBoneBatchView(key)
	if self.bone_batch_view:IsOpen() then
		self.bone_batch_view:Flush(nil, key)
	end
end

-- 技能弹窗
function FightSoulWGCtrl:OpenSkillTipsByItemId(item_id, slot)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	local fs_item_cfg = self.data:GetFightSoulItemCfg(item_id)
	if IsEmptyTable(item_cfg) or IsEmptyTable(fs_item_cfg) then
		return
	end

	local skill_id = fs_item_cfg.active_skill
	local skill_level = fs_item_cfg.skill_level
	local skill_cfg = SkillWGData.Instance:GetSiXiangSkillById(skill_id, skill_level)
	local skill_client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level)
	local next_skill_client_cfg = SkillWGData.Instance:GetSkillClientConfig(skill_id, skill_level + 1)
	local skill_des = SkillWGData.Instance:GetSiXiangSkillDes(skill_id, skill_level)
	local next_skill_des = SkillWGData.Instance:GetSiXiangSkillDes(skill_id, skill_level + 1)

	if IsEmptyTable(skill_cfg) or IsEmptyTable(skill_client_cfg) or IsEmptyTable(next_skill_client_cfg) then
		return
	end

	local limit_text
	local real_limit_text = ""
	local capability = self.data:GetSkillCapabilityByItemId(item_id)
	local max_level = self.data:GetSkillMaxLevelBySkillId(skill_id)
	local is_max = skill_level >= max_level
	if not is_max then
		local level_need = self.data:GetSkillLevelNeedBySkillId(skill_id, skill_level + 1)
		if not IsEmptyTable(level_need) then
			local quality_str = ToColorStr(Language.Common.ColorName[level_need.color], ITEM_COLOR[level_need.color])
			real_limit_text = string.format(Language.FightSoul.SkillNextNeedDesc4, quality_str)--, level_need.star)
			limit_text = ""
		end
	end

	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(skill_id)
	local show_data = {
		icon = skill_client_cfg.icon_resource,
		top_text = skill_cfg.skill_name,	-- 技能名
		top_text_color = ITEM_COLOR[item_cfg.color],
		skill_level = skill_level,
		body_text = skill_des or "",							-- 当前等级技能描述
		limit_text = limit_text ~= "" and limit_text or real_limit_text,
		capability = capability,
		x = 0,
		y = 0,
		set_pos2 = true,
	}

	NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end

-- 被动技能tips
function FightSoulWGCtrl:OpenPassiveSkillTips(fs_type, suit_type, color, same_num, index, is_view)
	local cfg_list = FightSoulWGData.Instance:GetTipSkillCfgList(fs_type)
	local cfg = CheckList(cfg_list, same_num, index)
	if IsEmptyTable(cfg) then
		return
	end

	local is_act = FightSoulWGData.Instance:GetBoneSuitIsAct(fs_type, suit_type, color, same_num)

	-- 6件套的主动技能的附加效果特殊处理
	if same_num == 6 then
		if index > suit_type then
			is_act = FightSoulWGData.Instance:GetBoneSuitIsAct(fs_type, index, color, same_num)
		end
		suit_type = index
	end

	local now_suit_cfg = FightSoulWGData.Instance:GetBoneSuitNumCfg(fs_type, suit_type, color, same_num)
	local nex_suit_cfg = FightSoulWGData.Instance:GetBoneSuitNumCfg(fs_type, suit_type, color + 1, same_num)
	local now_data = FightSoulWGData.Instance:GetPassiveSkillTipsData(now_suit_cfg, index)
	local nex_data = FightSoulWGData.Instance:GetPassiveSkillTipsData(nex_suit_cfg, index)
	if IsEmptyTable(now_data) then
		return
	end

	local cur_skill_desc = table.concat(now_data.desc_list, "\n")
    local next_skill_desc = nex_data and table.concat(nex_data.desc_list, "\n") or ""

    local is_max = IsEmptyTable(nex_suit_cfg)
    local skill_level = color - 2

    local limit_text = nil
    local real_limit_text = ""
    if not is_max or not is_act then
    	limit_text = ""
		local quality_str = ToColorStr(Language.Common.ColorName[color + 1], ITEM_COLOR[color + 1])
		if is_act then
			real_limit_text = string.format(Language.FightSoul.SkillNextNeedDesc3, quality_str)
		elseif is_view then
			local str = Language.FightSoul.SuitShortStr[suit_type]
			limit_text = string.format(Language.FightSoul.SkillNextNeedDesc5, same_num, str)
		else
			limit_text = string.format(Language.FightSoul.SkillNextNeedDesc6, same_num)
		end
    end

    local show_data = {
        icon = cfg.skill_icon,
        top_text = cfg.skill_name or "",                    -- 技能名
        top_text_color = ITEM_COLOR[color],
        skill_level = is_act and skill_level or Language.MultiMount.HasNotActive,
        body_text = cur_skill_desc,                         -- 当前等级技能描述
        limit_text = limit_text ~= "" and limit_text or real_limit_text,
        real_limit_text = real_limit_text,                  -- （底部）限制描述
        capability = now_data.score,
        x = 0,
        y = 0,
        set_pos2 = true,
    }

    NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
end



-- 物品变化
function FightSoulWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		-- 战魂突破材料变化
		if self.data:IsFightSoulBreakStuff(change_item_id) then
			if self.view:IsOpen() then
				self.view:Flush(TabIndex.fight_soul_train, "break_stuff_change")
			end
		end

		if self.data:GetIsBoneStrengthStuffId(change_item_id) then
			if self.view:IsOpen() then
				self.view:Flush(TabIndex.fight_soul_bone, "bone_strength")
			end

			if self.bone_strength_view:IsOpen() then
				self.bone_strength_view:Flush()
			end

			RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
		end

		if self.data:GetIsUnlockStuff(change_item_id) then
			RemindManager.Instance:Fire(RemindName.FightSoul_Train)
			RemindManager.Instance:Fire(RemindName.FightSoul_Bone)

			if self.view:IsOpen() then
				self.view:Flush(nil, "soul_list")
			end

			if self.unlock_tips:IsOpen() then
				self.unlock_tips:Flush()
			end
		end

		if self.data:CheckIsZhuoTieItem(change_item_id) then
			RemindManager.Instance:Fire(RemindName.FightSoul_CuiZhuo)

			if self.view:IsOpen() then
				if self.view:GetShowIndex() == TabIndex.fight_soul_cuizhuo then
					self.view:Flush(TabIndex.fight_soul_cuizhuo, "flush_zhuotie_remind")
				end
			end
			
			if self.zhuotie_view and self.zhuotie_view:IsOpen() then
				self.zhuotie_view:Flush()
			end
		end

		if self.data:CheckIsZhuoTieUpGradeItem(change_item_id) then
			RemindManager.Instance:Fire(RemindName.FightSoul_CuiZhuo)
			if self.view:IsOpen() then
				if self.view:GetShowIndex() == TabIndex.fight_soul_cuizhuo then
					self.view:Flush(TabIndex.fight_soul_cuizhuo)
				end
			end
		end
	end
end

-- 等级变化
function FightSoulWGCtrl:OnRoleLevelChange(attr_name, value, old_value)
	if attr_name == "level" and self.data:IsSlotLimitLevel(value) then
		if self.view:IsOpen() then
			self.view:Flush()
		end

		RemindManager.Instance:Fire(RemindName.FightSoul_Train)
		RemindManager.Instance:Fire(RemindName.FightSoul_Bone)
	end
end

-- 打开战魂界面
function FightSoulWGCtrl:OpenFightSoulView(tab_index)
	local is_open, tip
	if tab_index == TabIndex.fight_soul_bone then
		is_open, tip = FunOpen.Instance:GetFunIsOpenedByTabName("fight_soul_bone", true)
	else
		is_open, tip = FunOpen.Instance:GetFunIsOpened(FunName.FightSoulView, true)
	end

	if not is_open then
		if tip and tip ~= "" then
			SysMsgWGCtrl.Instance:ErrorRemind(tip)
		end
		return
	end

	ViewManager.Instance:Open(GuideModuleName.FightSoulView, tab_index)
end

-- 战魂批量融合请求
function FightSoulWGCtrl:ReqFightSoulBoneBatchComposeOp(batch_list)
	-- print_error("【----战魂批量融合请求-----】：", batch_list)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSiXiangBoneBatchReq)
	protocol.batch_list = batch_list or {}
	protocol:EncodeAndSend()
end

------------------------------------------------淬啄----------------------------------------------------
function FightSoulWGCtrl:SendFightSoulCuiZhuoOperate(operate_type, param1, param2, param3)
    -- print_error("【-----淬琢操作请求----】：", operate_type, param1, param2, param3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSSiXiangQuenchOperate)
	protocol.operate_type = operate_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol.param3 = param3 or 0
	protocol:EncodeAndSend()
end

function FightSoulWGCtrl:OnSCSiXiangQuenchInfo(protocol)
	-- print_error("【-----淬琢操作所有信息----】：", protocol)
	self.data:SetSiXiangQuenchInfo(protocol)
	RemindManager.Instance:Fire(RemindName.FightSoul_CuiZhuo)

	if self.view:IsOpen() and self.view:GetShowIndex() == TabIndex.fight_soul_cuizhuo then
		self.view:Flush(TabIndex.fight_soul_cuizhuo)
	end
end

function FightSoulWGCtrl:OnSCSiXiangQuenchItemUpdate(protocol)
	-- print_error("【-----淬琢信息更新----】：", protocol)
	local old_data = FightSoulWGData.Instance:GetSiXiangQuenchInfoBySolt(protocol.solt)
	self.data:UpdateSiXiangQuenchItemInfo(protocol)
	local new_data = protocol.item

	if new_data.grade > old_data.grade then
		if self.view:IsOpen() and self.view:GetShowIndex() == TabIndex.fight_soul_cuizhuo then
			self.view:PlayCuiZhuoEffect(UIEffectName.s_shengji)
		end
	end

	ViewManager.Instance:FlushView(GuideModuleName.MountLingChongDisplayBox)
	RemindManager.Instance:Fire(RemindName.FightSoul_CuiZhuo)

	if self.view:IsOpen() and self.view:GetShowIndex() == TabIndex.fight_soul_cuizhuo then
		self.view:Flush(TabIndex.fight_soul_cuizhuo)
	end
end

function FightSoulWGCtrl:OnSCSiXiangQuenchCutIronUpdate(protocol)
	-- print_error("【-----琢铁信息更新----】：", protocol)
	self.data:UpdateSiXiangQuenchCutIronInfo(protocol)
	RemindManager.Instance:Fire(RemindName.FightSoul_CuiZhuo)

	if self.view:IsOpen() then
		if self.view:GetShowIndex() == TabIndex.fight_soul_cuizhuo then
			self.view:Flush(TabIndex.fight_soul_cuizhuo, "flush_zhuotie_remind")
		end
	end
			
	if self.zhuotie_view and self.zhuotie_view:IsOpen() then
		self.zhuotie_view:Flush()
	end
end

function FightSoulWGCtrl:OpenZhuoTieView()
	if self.zhuotie_view and not self.zhuotie_view:IsOpen() then
		self.zhuotie_view:Open()
	end
end

function FightSoulWGCtrl:FlushBone()
	if self.view:IsOpen() then
		self.view:FlushBoneView()
	end
end
