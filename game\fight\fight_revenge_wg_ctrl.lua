require("game/fight/revenge_wg_data") 
FightRevengeWGCtrl = FightRevengeWGCtrl or BaseClass(BaseWGCtrl)

local WORLD_BOSS_ROOKIE_LAYER_ID = 9674  --世界魔王新手层

function FightRevengeWGCtrl:__init()
	if FightRevengeWGCtrl.Instance ~= nil then
		print_error("[FightRevengeWGCtrl] Attemp to create a singleton twice !")
	end
	FightRevengeWGCtrl.Instance = self

	self.data = RevengeWGData.New()

	self.lase_update_time = 0
	self.safe_run = true

	Runner.Instance:AddRunObj(self)
	self.role_info_callback = BindTool.Bind(self.InfoCallBack, self)
	-- self:BindGlobalEvent(SceneEventType.SCENE_LOADING_STATE_ENTER, BindTool.Bind(self.ClearRevengeList, self))
	self:BindGlobalEvent(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.OnObjDead, self))
end

function FightRevengeWGCtrl:__delete()
	Runner.Instance:RemoveRunObj(self)

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
	FightRevengeWGCtrl.Instance = nil
end

function FightRevengeWGCtrl:OnObjChangeBlood(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	local scene_config = MainuiWGCtrl.Instance:GetFbSceneCfg(scene_type)
	if scene_config.is_quickrevenge == 0 then
		return
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic and scene_logic:IsNowHideRevengePanel() then
		return
	end

	--世界魔王新手层屏蔽反杀列表
	if Scene.Instance:GetSceneId() == WORLD_BOSS_ROOKIE_LAYER_ID then
		return
	end

	local obj = Scene.Instance:GetObj(protocol.obj_id)
	if nil == obj or obj:IsDeleted() or not obj:IsMainRole() then
		return
	end

	if obj:IsRealDead() then  --玩家角色死亡，不在更新列表，重置反击中状态
		self.data:SetCurRevengeRoleId(nil)
		return
	end

	local deliverer = Scene.Instance:GetObj(protocol.deliverer)
	if deliverer == nil or not deliverer:IsRole() or deliverer:IsRealDead() or deliverer:IsInvisible() then
		return
	end
	-- print_error("===========OnObjChangeBlood=========", deliverer:GetRoleId(), protocol.real_blood)

	--判断打我的人，是不是来自别的服
	local is_same_server = not RoleWGData.Instance:IsSameServer(deliverer.vo)
	local deliverer_key_id = 0
	--当伤害来源是自己 加血 机器人 的时候不显示
	if IS_ON_CROSSSERVER or is_same_server then
		local origin_uid = RoleWGData.Instance:GetOriginUid()
		--当在跨服时origin_id 不会为0 
		deliverer_key_id = deliverer:GetOriginId()
		if deliverer:GetOriginId() == 0 or origin_uid == deliverer:GetOriginId() or protocol.real_blood > 0 or deliverer:IsRobot() then
			return 
		end
	else
		local role_uid = RoleWGData.Instance:GetRoleVo().role_id
		deliverer_key_id = deliverer:GetRoleId()
		if role_uid == deliverer:GetRoleId() or protocol.real_blood > 0 or deliverer:IsRobot() then
			return 
		end
	end

    local x, y = deliverer:GetLogicPos()
    local role_id
    if IS_ON_CROSSSERVER or is_same_server then
        role_id = deliverer:GetOriginId()
    else
        role_id = deliverer:GetRoleId()
    end

	local flag = self.data:InsertRevengeList(x, y, role_id, deliverer_key_id)
	-- print_error("------------flag----------", flag)
	if flag then
		if is_same_server then
			local plat_type = deliverer.vo.plat_type
			BrowseWGCtrl.Instance:SendGlobalQueryRoleInfo(plat_type or RoleWGData.Instance.role_vo.plat_type, deliverer_key_id,nil, self.role_info_callback)
		else
			BrowseWGCtrl.Instance:BrowRoelInfo(deliverer_key_id, self.role_info_callback)
		end
		
	end

	self:CheckIsNeedAutoRevenge(role_id)
end

function FightRevengeWGCtrl:CheckIsNeedAutoRevenge(role_id)
	local scene_logic = Scene.Instance:GetSceneLogic()
	if scene_logic == nil or role_id == nil then
		if CLIENT_DEBUG_LOG_STATE then
			print_error("CheckIsNeedAutoRevenge", role_id, scene_logic == nil)
		end
		return
	end

	if not scene_logic:IsNeedAutoRevenge() then
		if CLIENT_DEBUG_LOG_STATE then
			print_error("IsNeedAutoRevenge")
		end
		return
	end

	local cur_role = self.data:GetCurRevengeRoleId()
	local is_in = self.data:GetIsInRevengeList(role_id)
	local main_role = Scene.Instance:GetMainRole()
	local cur_obj = GuajiCache.target_obj
	local obj_vaild = true
	if cur_obj == nil or cur_obj:IsDeleted() or not cur_obj:IsRole() or not Scene.Instance:IsEnemy(cur_obj) then
		obj_vaild = false
	end

	if CLIENT_DEBUG_LOG_STATE then
		print_error("#######", cur_obj == nil, cur_obj == nil or cur_obj:IsDeleted(), cur_obj == nil or cur_obj:IsDeleted() or not cur_obj:IsRole(), cur_obj == nil or cur_obj:IsDeleted() or not cur_obj:IsRole() or not Scene.Instance:IsEnemy(cur_obj))
		print_error("~~~~~~CheckIsNeedAutoRevenge~~~~~~", obj_vaild, self.data:GetHaveListFlag(), cur_role, is_in, obj_vaild, main_role:IsAutoMove(), GuajiCache.guaji_type == GuajiType.Auto)
	end

	if self.data:GetHaveListFlag() and cur_role == nil and is_in and not obj_vaild then
		if main_role ~= nil and not main_role:IsDeleted() and (main_role:IsAutoMove() or GuajiCache.guaji_type == GuajiType.Auto) then
			MainuiWGCtrl.Instance:TryRevengeRole(role_id)
		end
	end
end

function FightRevengeWGCtrl:Update()
	if not self.data:GetHaveListFlag() then
		return
	end

	-- 同屏人数多的时候降低请求频率
	local role_list = Scene.Instance:GetRoleList()
	local time_span = #role_list * 0.1 + 2
	time_span = math.min(time_span, 6)

	if Status.NowTime - self.lase_update_time < time_span then
		return
	end

	self.lase_update_time = Status.NowTime
	self:GetRevengeInfoByServer()
end

function FightRevengeWGCtrl:InfoCallBack(protocol)
	local role = self.data:SaveData(protocol)
	if role then
		MainuiWGCtrl.Instance:AddRevenge(role)
		GlobalTimerQuest:AddDelayTimer(function ()
			MainuiWGCtrl.Instance:RevengeChange()
		end, 1)
	end
end

function FightRevengeWGCtrl:OnObjDead(obj)
	if not obj then
		return
	end

	if obj:IsMainRole() then
		MainuiWGCtrl.Instance:OnMainRoleDead(obj)
	else
		local flag, role_id = self.data:OnObjDead(obj)
		if flag then
			MainuiWGCtrl.Instance:RevengeChange()
			MainuiWGCtrl.Instance:OnRoleObjDead(role_id)
		end
	end
end

function FightRevengeWGCtrl:ClearRevengeList()
	self.data:ClearList()
	MainuiWGCtrl.Instance:RevengeChange()
end

function FightRevengeWGCtrl:GetRevengeInfoByServer()
	local list = self.data:GetRevengeList()
	local len = #list

	for i = 1, len do
		if list[i] ~= nil then
			Scene.Instance:SendGetOneMovePos(OBJ_TYPE.OBJ_TYPE_ROLE, list[i].deliverer_role_id)
		end
	end
end

function FightRevengeWGCtrl:OnRevengeMoveInfo(protocol)
	local flag = self.data:CheckList(protocol)
	if flag then
		MainuiWGCtrl.Instance:RevengeChange()
	end

	local check_role_id = RevengeWGData.Instance:GetCurRevengeRoleId()
	if check_role_id ~= nil and (protocol.obj_type == OBJ_TYPE.OBJ_TYPE_ROLE or protocol.obj_type == OBJ_TYPE.OBJ_TYPE_INVALID)
		and check_role_id == protocol.param then
		MainuiWGCtrl.Instance:FlushFindRole()
	end
end