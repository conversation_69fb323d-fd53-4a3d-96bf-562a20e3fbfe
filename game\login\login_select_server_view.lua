-- 服务器特效
local select_server_effect = {
	[1] = "UI_fuwuqi1",
	[2] = "UI_fuwuqi2",
	[3] = "UI_fuwuqi3",
}
-- 服务器背景图
local select_server_bg = {
	[0] = "a2_xf_weixuan0",
	[1] = "a2_xf_weixuan1",
	[2] = "a2_xf_weixuan2",
	[3] = "a2_xf_weixuan3",
	[4] = "a2_xf_weixuan2",
}

LoginSelectServerView = LoginSelectServerView or BaseClass(SafeBaseView)
local ServerItemType = {
	None = 0,
	Recommend = 1,		-- 推荐
	MyServer = 2,		-- 我的服务器
	Select = 3,			-- 选服
}

function LoginSelectServerView:__init()
	self.view_style = ViewStyle.Window
	self:ClearViewTween()
	self:AddViewResource(0, "uis/view/login_ui_prefab", "layout_login_select_server")
	self:SetMaskBg()
end

function LoginSelectServerView:ReleaseCallBack()
    if self.server_list_view then
		self.server_list_view:DeleteMe()
		self.server_list_view = nil
	end

	if self.group_list_view then
		self.group_list_view:DeleteMe()
		self.group_list_view = nil
	end
end

function LoginSelectServerView:LoadCallBack()
	-- self.node_list.title_view_name.text.text = Language.Login.Select_Login
	self.server_list, self.recommend_list = LoginWGData.Instance:GetViewShowShowServerList()
	self.area_server_list = LoginWGData.Instance:GetViewShowAreaServerList()						-- 服区间

    -- 服务器列表
	if not self.server_list_view then
        self.server_list_view = AsyncBaseGrid.New()
        self.server_list_view:SetStartZeroIndex(false)
        self.server_list_view:CreateCells({
            col = 2,
            cell_count = 10,
            list_view = self.node_list["select_server_list"],
            itemRender = ServerItem,
            assetBundle = "uis/view/login_ui_prefab",
            assetName = "layout_server_item",
            change_cells_num = 1,
        })
    end

    -- 左侧服务器区间列表
	if self.group_list_view == nil then
		self.group_list_view = AsyncListView.New(ServerGroupItem, self.node_list["select_server_group"])
		self.group_list_view:SetSelectCallBack(BindTool.Bind(self.OnSelectServerGroupCB, self))
		self.group_list_view:SetDefaultSelectIndex(nil)
		self.group_list_view:SetDataList(self.area_server_list)
	end

	self.node_list["btn_my_server"].button:AddClickListener(BindTool.Bind(self.OnClickMyServer, self))
	self.node_list["btn_tuijian"].button:AddClickListener(BindTool.Bind(self.OnClickTuijian, self))
end

function LoginSelectServerView:CloseCallBack()
	LoginWGCtrl.Instance:SelectServerViewToLoginView()
end

-- 左侧服务器区间列表 回调
function LoginSelectServerView:OnSelectServerGroupCB(cell)
	if cell == nil then
		return
	end

	local index = cell:GetIndex()
	LoginWGData.Instance:ViewSelectServerType(ServerItemType.Select)

	local data_list = {}
	local server_list = self.area_server_list[index] and self.area_server_list[index].server_list or {}
	for k,v in pairs(server_list) do
		local data = {}
		data.data = v
		data.server_type = ServerItemType.Select
		table.insert(data_list, data)
	end

	self.server_list_view:SetDataList(data_list)
	self:OnClickOtherServer()
end

function LoginSelectServerView:OnClickOtherServer()
	self:SetTuiJianStatus(false)
	self:SetMyServerStatus(false)
	self.node_list["no_server"]:SetActive(false)
end

function LoginSelectServerView:OnFlush()
	self.show_my_last_server_list = LoginWGData.Instance:GetPlayerLoginServerList()
	if 0 == #self.show_my_last_server_list then
		self:OnClickTuijian()
	else
		self:OnClickMyServer()
	end
end

--点击我的服务器按钮
function LoginSelectServerView:OnClickMyServer()
	LoginWGData.Instance:ViewSelectServerType(ServerItemType.MyServer)
	self.group_list_view:CancelSelect()

	self.node_list["no_server"]:SetActive(0 == #self.show_my_last_server_list)
	self:SetTuiJianStatus(false)
	self:SetMyServerStatus(true)

	local data_list = self:GetMyServerData()
	self.server_list_view:SetDataList(data_list)
end

--点击推荐按钮
function LoginSelectServerView:OnClickTuijian()
	LoginWGData.Instance:ViewSelectServerType(ServerItemType.Recommend)
	self.group_list_view:CancelSelect()

	self.node_list["no_server"]:SetActive(0 == #self.recommend_list)
	self:SetMyServerStatus(false)
	self:SetTuiJianStatus(true)

	local data_list = self:GetRecommendServerData()
	self.server_list_view:SetDataList(data_list)
end

function LoginSelectServerView:SetMyServerStatus(is_open)
	self.node_list["btn_my_server_normal"]:SetActive(not is_open)
	self.node_list["btn_my_server_hl"]:SetActive(is_open)
end

function LoginSelectServerView:SetTuiJianStatus(is_open)
	self.node_list["btn_tuijian_nor"]:SetActive(not is_open)
	self.node_list["btn_tuijian_light"]:SetActive(is_open)
end

function LoginSelectServerView:GetMyServerData()
	local data_list = {}
	for k,v in ipairs(self.show_my_last_server_list) do
		local data = {}
		data.data = LoginWGData.Instance:GetServerVoById(v)
		data.server_type = ServerItemType.MyServer
		table.insert(data_list, data)
	end

	return data_list
end

function LoginSelectServerView:GetRecommendServerData()
	local data_list = {}
	local min_num = nil
	local recommend_index = nil
	local recommend_type = GLOBAL_CONFIG.param_list.recommend_type or 0
	if recommend_type ~= "" and recommend_type == 1 then
		for k,v in pairs(self.recommend_list) do
			if v.role_num ~= nil and (min_num == nil or min_num > v.role_num) then
				min_num = v.role_num
				recommend_index = k
			end
		end
	end


	if recommend_index == nil then
		local index = #self.recommend_list
		local data1 = {}
		data1.data = self.recommend_list[index]
		data1.server_type = ServerItemType.Recommend
		table.insert(data_list, data1)

		index = #self.recommend_list - 1
		local data2 = {}
		data2.data = self.recommend_list[index]
		data2.server_type = ServerItemType.Recommend
		table.insert(data_list, data2)
	else
		local data = {}
		data.data = self.recommend_list[recommend_index]
		data.server_type = ServerItemType.Recommend
		table.insert(data_list, data)
	end

	return data_list
end


----------------------------------------------------------------------------
ServerItem = ServerItem or BaseClass(BaseRender)
function ServerItem:LoadCallBack()
	self.server_type = ServerItemType.None
	self.server_id = 0
	self.server_status = self.node_list["server_status"]
	self.node_list["layout_server_item"].toggle:AddClickListener(BindTool.Bind(self.OnClickItemlist, self))
	-- self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnValueChange,self))
end

function ServerItem:ServerType(value)
	if nil == value then
		return self.server_type
	end

	self.server_type = value
end

function ServerItem:SetData(data)
	if not data then
		return
	end

	self.data = data.data
	self.server_type = data.server_type
	self:Flush()
end

function ServerItem:SetToggleGroup(toggle_group)
	if self.view and not self.view.toggle.group then
		self.view.toggle.group = toggle_group
	end
end

function ServerItem:OnFlush()
	self.view:CustomSetActive(self.data ~= nil)
	if self.data == nil then
		return
	end
	
	local head_level_color = COLOR3B.BLUE_TITLE

	if self.data.id == LoginWGData.Instance:ViewSelectServerID() and LoginWGData.Instance:ViewSelectServerType() == self.server_type then
		self.node_list.nor_img:CustomSetActive(false)
		self.node_list.select_img:CustomSetActive(true)
		head_level_color = COLOR3B.C6
		-- self.view.toggle.isOn = true
	else
		self.node_list.nor_img:CustomSetActive(true)
		self.node_list.select_img:CustomSetActive(false)
		-- self.view.toggle.isOn = false
	end

	--先去掉冠名的这个
	self.node_list.effect_style:SetActive(self.data.style ~= nil and self.data.style ~= 0)
	--self.node_list.effect_style_img:CustomSetActive(self.data.style ~= nil and self.data.style ~= 0)
	if self.data.style ~= nil and self.data.style ~= 0 then
		-- self.node_list.nor_img:CustomSetActive(false)
		-- self.node_list.select_img:CustomSetActive(false)

		local effect_asset, effect_bundle = ResPath.GetA2Effect(select_server_effect[self.data.style])
		if effect_asset and effect_bundle then
			self.node_list.effect_style:ChangeAsset(effect_asset, effect_bundle)
		end

		-- local bundle, asset = ResPath.GetLoginUiImg(select_server_bg[self.data.style])
		-- self.node_list.effect_style_img.image:LoadSprite(bundle, asset)
	end

	self.node_list["recommend_server_flag"]:SetActive(self.data.flag == 1)
    self.node_list["new_server_flag"]:SetActive(self.data.flag == 2)

	local bundle_bundle, bundle_asset = LoginWGData.GetServerStateAsset(self.data.flag)
	self.server_status.image:LoadSprite(bundle_bundle, bundle_asset)

	local name = LoginWGData.Instance:GetShowServerName(self.data)
	self.node_list["server_name"].text.text = name
	self.node_list["server_name_hl"].text.text = name

	self:FlushHeadShow(head_level_color)
end

function ServerItem:FlushHeadShow(head_level_color)
	if self.data == nil then
		return
	end

    local head_list = LoginWGData.Instance:GetServerListHead(self.data.id)
	if not head_list then
		self.node_list["go_head"]:SetActive(false)
		return
	else
		self.node_list["go_head"]:SetActive(true)
	end

	local head_data1 = head_list[1]
	local head_data2 = head_list[2]
	self.node_list["head1"]:SetActive(nil ~= head_data1)
	self.node_list["head2"]:SetActive(nil ~= head_data2)

	-- 修改选择角色的头像
	if head_data1 then
		XUI.UpdateRoleHead(self.node_list["default_head_icon1"], self.node_list["custom_head_icon1"],
		 tonumber(head_data1.role_id), tonumber(head_data1.sex), tonumber(head_data1.prof), nil, false, true)

		local str = RoleWGData.GetLevelStringImg3(head_data1.level)
		self.node_list.level1.text.text = ToColorStr(str, head_level_color) 
	end

	if head_data2 then
		XUI.UpdateRoleHead(self.node_list["default_head_icon2"], self.node_list["custom_head_icon2"],
		 tonumber(head_data2.role_id), tonumber(head_data2.sex), tonumber(head_data2.prof), nil, false, true)
		 local str = RoleWGData.GetLevelStringImg3(head_data2.level)
		 self.node_list.level2.text.text = ToColorStr(str, head_level_color) 
	end
end

function ServerItem:OnClickItemlist(isOn)
	if self.data == nil then
		return
	end
	
	if isOn then
		LoginWGData.Instance:ViewSelectServerID(self.data.id)
		GameNet.Instance:SetLoginServerInfo(self.data.ip, self.data.port)
		GameVoManager.Instance:GetUserVo().plat_server_id = self.data.id
		GameVoManager.Instance:GetUserVo().plat_server_name = LoginWGData.Instance:GetServerName(self.data.id)
		-- ReportManager:Step(Report.STEP_SELECT_SERVER_END, self.data.id)

		LoginWGCtrl.Instance.select_server_view:Close()
	end
end


---------------------------------
-- 服区间
ServerGroupItem = ServerGroupItem or BaseClass(BaseRender)
function ServerGroupItem:OnFlush()
	local server_name = self.data.name

	self.node_list["server_name"].text.text = server_name
	self.node_list["server_name_hl"].text.text = server_name
end

function ServerGroupItem:OnSelectChange(is_select)
	self.node_list.select:SetActive(is_select)
	self.node_list.normal:SetActive(not is_select)
end

