-- Y-幽冥聚魂.xls

return {
grade={
{},
{grade=1,need_exp=100,attr_value1=35,attr_value2=353,attr_value3=18,attr_value4=12,},
{grade=2,need_exp=150,attr_value1=99,attr_value2=988,attr_value3=49,attr_value4=33,},
{grade=3,need_exp=200,attr_value1=190,attr_value2=1906,attr_value3=95,attr_value4=64,},
{grade=4,need_exp=300,attr_value1=310,attr_value2=3106,attr_value3=155,attr_value4=104,},
{grade=5,need_exp=400,attr_value1=459,attr_value2=4588,attr_value3=229,attr_value4=153,},
{grade=6,need_exp=550,attr_value1=635,attr_value2=6353,attr_value3=318,attr_value4=212,},
{grade=7,need_exp=700,attr_value1=840,attr_value2=8400,attr_value3=420,attr_value4=280,},
{grade=8,need_exp=850,attr_value1=1073,attr_value2=10729,attr_value3=536,attr_value4=358,},
{grade=9,need_exp=1000,attr_value1=1334,attr_value2=13341,attr_value3=667,attr_value4=445,},
{grade=10,need_exp=1150,attr_value1=1624,attr_value2=16235,attr_value3=812,attr_value4=541,},
{grade=11,need_exp=1300,attr_value1=1941,attr_value2=19412,attr_value3=971,attr_value4=647,},
{grade=12,need_exp=1450,attr_value1=2287,attr_value2=22871,attr_value3=1144,attr_value4=762,},
{grade=13,need_exp=1600,attr_value1=2661,attr_value2=26612,attr_value3=1331,attr_value4=887,},
{grade=14,need_exp=1750,attr_value1=3064,attr_value2=30635,attr_value3=1532,attr_value4=1021,},
{grade=15,need_exp=1900,attr_value1=3494,attr_value2=34941,attr_value3=1747,attr_value4=1165,},
{grade=16,need_exp=2100,attr_value1=3953,attr_value2=39529,attr_value3=1976,attr_value4=1318,},
{grade=17,need_exp=2300,attr_value1=4440,attr_value2=44400,attr_value3=2220,attr_value4=1480,},
{grade=18,need_exp=2500,attr_value1=4955,attr_value2=49553,attr_value3=2478,attr_value4=1652,},
{grade=19,need_exp=2700,attr_value1=5499,attr_value2=54988,attr_value3=2749,attr_value4=1833,},
{grade=20,need_exp=2900,attr_value1=6070,attr_value2=60706,attr_value3=3035,attr_value4=2024,},
{grade=21,need_exp=3100,attr_value1=6670,attr_value2=66706,attr_value3=3335,attr_value4=2224,},
{grade=22,need_exp=3300,attr_value1=7299,attr_value2=72988,attr_value3=3649,attr_value4=2433,},
{grade=23,need_exp=3500,attr_value1=7955,attr_value2=79553,attr_value3=3978,attr_value4=2652,},
{grade=24,need_exp=3700,attr_value1=8640,attr_value2=86400,attr_value3=4320,attr_value4=2880,},
{grade=25,need_exp=3900,attr_value1=9353,attr_value2=93529,attr_value3=4676,attr_value4=3118,},
{grade=26,need_exp=4150,attr_value1=10094,attr_value2=100941,attr_value3=5047,attr_value4=3365,},
{grade=27,need_exp=4400,attr_value1=10864,attr_value2=108635,attr_value3=5432,attr_value4=3621,},
{grade=28,need_exp=4650,attr_value1=11661,attr_value2=116612,attr_value3=5831,attr_value4=3887,},
{grade=29,need_exp=4900,attr_value1=12487,attr_value2=124871,attr_value3=6244,attr_value4=4162,},
{grade=30,need_exp=5150,attr_value1=13341,attr_value2=133412,attr_value3=6671,attr_value4=4447,},
{grade=31,need_exp=5400,attr_value1=14224,attr_value2=142235,attr_value3=7112,attr_value4=4741,},
{grade=32,need_exp=5650,attr_value1=15134,attr_value2=151341,attr_value3=7567,attr_value4=5045,},
{grade=33,need_exp=5900,attr_value1=16073,attr_value2=160729,attr_value3=8036,attr_value4=5358,},
{grade=34,need_exp=6150,attr_value1=17040,attr_value2=170400,attr_value3=8520,attr_value4=5680,},
{grade=35,need_exp=6400,attr_value1=18035,attr_value2=180353,attr_value3=9018,attr_value4=6012,},
{grade=36,need_exp=6700,attr_value1=19059,attr_value2=190588,attr_value3=9529,attr_value4=6353,},
{grade=37,need_exp=7000,attr_value1=20110,attr_value2=201106,attr_value3=10055,attr_value4=6704,},
{grade=38,need_exp=7300,attr_value1=21190,attr_value2=211906,attr_value3=10595,attr_value4=7064,},
{grade=39,need_exp=7600,attr_value1=22299,attr_value2=222988,attr_value3=11149,attr_value4=7433,},
{grade=40,need_exp=7900,attr_value1=23435,attr_value2=234353,attr_value3=11718,attr_value4=7812,},
{grade=41,need_exp=8200,attr_value1=24600,attr_value2=246000,attr_value3=12300,attr_value4=8200,},
{grade=42,need_exp=8500,attr_value1=25793,attr_value2=257929,attr_value3=12896,attr_value4=8598,},
{grade=43,need_exp=8800,attr_value1=27014,attr_value2=270141,attr_value3=13507,attr_value4=9005,},
{grade=44,need_exp=9100,attr_value1=28264,attr_value2=282635,attr_value3=14132,attr_value4=9421,},
{grade=45,need_exp=9400,attr_value1=29541,attr_value2=295412,attr_value3=14771,attr_value4=9847,},
{grade=46,need_exp=9800,attr_value1=30847,attr_value2=308471,attr_value3=15424,attr_value4=10282,},
{grade=47,need_exp=10200,attr_value1=32181,attr_value2=321812,attr_value3=16091,attr_value4=10727,},
{grade=48,need_exp=10600,attr_value1=33544,attr_value2=335435,attr_value3=16772,attr_value4=11181,},
{grade=49,need_exp=11000,attr_value1=34934,attr_value2=349341,attr_value3=17467,attr_value4=11645,},
{grade=50,need_exp=11400,attr_value1=36353,attr_value2=363529,attr_value3=18176,attr_value4=12118,},
{grade=51,need_exp=11800,attr_value1=37800,attr_value2=378000,attr_value3=18900,attr_value4=12600,},
{grade=52,need_exp=12200,attr_value1=39275,attr_value2=392753,attr_value3=19638,attr_value4=13092,},
{grade=53,need_exp=12600,attr_value1=40779,attr_value2=407788,attr_value3=20389,attr_value4=13593,},
{grade=54,need_exp=13000,attr_value1=42310,attr_value2=423106,attr_value3=21155,attr_value4=14104,},
{grade=55,need_exp=13400,attr_value1=43870,attr_value2=438706,attr_value3=21935,attr_value4=14624,},
{grade=56,need_exp=13900,attr_value1=45459,attr_value2=454588,attr_value3=22729,attr_value4=15153,},
{grade=57,need_exp=14400,attr_value1=47075,attr_value2=470753,attr_value3=23538,attr_value4=15692,},
{grade=58,need_exp=14900,attr_value1=48720,attr_value2=487200,attr_value3=24360,attr_value4=16240,},
{grade=59,need_exp=15400,attr_value1=50393,attr_value2=503929,attr_value3=25196,attr_value4=16798,},
{grade=60,need_exp=15900,attr_value1=52094,attr_value2=520941,attr_value3=26047,attr_value4=17365,},
{grade=61,need_exp=16400,attr_value1=53824,attr_value2=538235,attr_value3=26912,attr_value4=17941,},
{grade=62,need_exp=16900,attr_value1=55581,attr_value2=555812,attr_value3=27791,attr_value4=18527,},
{grade=63,need_exp=17400,attr_value1=57367,attr_value2=573671,attr_value3=28684,attr_value4=19122,},
{grade=64,need_exp=17900,attr_value1=59181,attr_value2=591812,attr_value3=29591,attr_value4=19727,},
{grade=65,need_exp=18400,attr_value1=61024,attr_value2=610235,attr_value3=30512,attr_value4=20341,},
{grade=66,need_exp=19000,attr_value1=62894,attr_value2=628941,attr_value3=31447,attr_value4=20965,},
{grade=67,need_exp=19600,attr_value1=64793,attr_value2=647929,attr_value3=32396,attr_value4=21598,},
{grade=68,need_exp=20200,attr_value1=66720,attr_value2=667200,attr_value3=33360,attr_value4=22240,},
{grade=69,need_exp=20800,attr_value1=68675,attr_value2=686753,attr_value3=34338,attr_value4=22892,},
{grade=70,need_exp=21400,attr_value1=70659,attr_value2=706588,attr_value3=35329,attr_value4=23553,},
{grade=71,need_exp=22000,attr_value1=72670,attr_value2=726706,attr_value3=36335,attr_value4=24224,},
{grade=72,need_exp=22600,attr_value1=74710,attr_value2=747106,attr_value3=37355,attr_value4=24904,},
{grade=73,need_exp=23200,attr_value1=76779,attr_value2=767788,attr_value3=38389,attr_value4=25593,},
{grade=74,need_exp=23800,attr_value1=78875,attr_value2=788753,attr_value3=39438,attr_value4=26292,},
{grade=75,need_exp=24400,attr_value1=81000,attr_value2=810000,attr_value3=40500,attr_value4=27000,},
{grade=76,need_exp=25100,attr_value1=83153,attr_value2=831529,attr_value3=41576,attr_value4=27718,},
{grade=77,need_exp=25800,attr_value1=85334,attr_value2=853341,attr_value3=42667,attr_value4=28445,},
{grade=78,need_exp=26500,attr_value1=87544,attr_value2=875435,attr_value3=43772,attr_value4=29181,},
{grade=79,need_exp=27200,attr_value1=89781,attr_value2=897812,attr_value3=44891,attr_value4=29927,},
{grade=80,need_exp=27900,attr_value1=92047,attr_value2=920471,attr_value3=46024,attr_value4=30682,},
{grade=81,need_exp=28600,attr_value1=94341,attr_value2=943412,attr_value3=47171,attr_value4=31447,},
{grade=82,need_exp=29300,attr_value1=96664,attr_value2=966635,attr_value3=48332,attr_value4=32221,},
{grade=83,need_exp=30000,attr_value1=99014,attr_value2=990141,attr_value3=49507,attr_value4=33005,},
{grade=84,need_exp=30700,attr_value1=101393,attr_value2=1013929,attr_value3=50696,attr_value4=33798,},
{grade=85,need_exp=31400,attr_value1=103800,attr_value2=1038000,attr_value3=51900,attr_value4=34600,},
{grade=86,need_exp=32200,attr_value1=106235,attr_value2=1062353,attr_value3=53118,attr_value4=35412,},
{grade=87,need_exp=33000,attr_value1=108699,attr_value2=1086988,attr_value3=54349,attr_value4=36233,},
{grade=88,need_exp=33800,attr_value1=111190,attr_value2=1111906,attr_value3=55595,attr_value4=37064,},
{grade=89,need_exp=34600,attr_value1=113710,attr_value2=1137106,attr_value3=56855,attr_value4=37904,},
{grade=90,need_exp=35400,attr_value1=116259,attr_value2=1162588,attr_value3=58129,attr_value4=38753,},
{grade=91,need_exp=36200,attr_value1=118835,attr_value2=1188353,attr_value3=59418,attr_value4=39612,},
{grade=92,need_exp=37000,attr_value1=121440,attr_value2=1214400,attr_value3=60720,attr_value4=40480,},
{grade=93,need_exp=37800,attr_value1=124073,attr_value2=1240729,attr_value3=62036,attr_value4=41358,},
{grade=94,need_exp=38600,attr_value1=126734,attr_value2=1267341,attr_value3=63367,attr_value4=42245,},
{grade=95,need_exp=39400,attr_value1=129424,attr_value2=1294235,attr_value3=64712,attr_value4=43141,},
{grade=96,need_exp=40300,attr_value1=132141,attr_value2=1321412,attr_value3=66071,attr_value4=44047,},
{grade=97,need_exp=41200,attr_value1=134887,attr_value2=1348871,attr_value3=67444,attr_value4=44962,},
{grade=98,need_exp=42100,attr_value1=137661,attr_value2=1376612,attr_value3=68831,attr_value4=45887,},
{grade=99,need_exp=43000,attr_value1=140464,attr_value2=1404635,attr_value3=70232,attr_value4=46821,},
{grade=100,need_exp=43900,attr_value1=143294,attr_value2=1432941,attr_value3=71647,attr_value4=47765,},
{grade=101,need_exp=44900,attr_value1=146153,attr_value2=1461529,attr_value3=73076,attr_value4=48718,},
{grade=102,need_exp=45900,attr_value1=149040,attr_value2=1490400,attr_value3=74520,attr_value4=49680,},
{grade=103,need_exp=46900,attr_value1=151955,attr_value2=1519553,attr_value3=75978,attr_value4=50652,},
{grade=104,need_exp=47900,attr_value1=154899,attr_value2=1548988,attr_value3=77449,attr_value4=51633,},
{grade=105,need_exp=48900,attr_value1=157870,attr_value2=1578706,attr_value3=78935,attr_value4=52624,},
{grade=106,need_exp=50000,attr_value1=160870,attr_value2=1608706,attr_value3=80435,attr_value4=53624,},
{grade=107,need_exp=51100,attr_value1=163899,attr_value2=1638988,attr_value3=81949,attr_value4=54633,},
{grade=108,need_exp=52200,attr_value1=166955,attr_value2=1669553,attr_value3=83478,attr_value4=55652,},
{grade=109,need_exp=53300,attr_value1=170040,attr_value2=1700400,attr_value3=85020,attr_value4=56680,},
{grade=110,need_exp=54400,attr_value1=173153,attr_value2=1731529,attr_value3=86576,attr_value4=57718,},
{grade=111,need_exp=55600,attr_value1=176294,attr_value2=1762941,attr_value3=88147,attr_value4=58765,},
{grade=112,need_exp=56800,attr_value1=179464,attr_value2=1794635,attr_value3=89732,attr_value4=59821,},
{grade=113,need_exp=58000,attr_value1=182661,attr_value2=1826612,attr_value3=91331,attr_value4=60887,},
{grade=114,need_exp=59200,attr_value1=185887,attr_value2=1858871,attr_value3=92944,attr_value4=61962,},
{grade=115,need_exp=60400,attr_value1=189141,attr_value2=1891412,attr_value3=94571,attr_value4=63047,},
{grade=116,need_exp=61700,attr_value1=192424,attr_value2=1924235,attr_value3=96212,attr_value4=64141,},
{grade=117,need_exp=63000,attr_value1=195734,attr_value2=1957341,attr_value3=97867,attr_value4=65245,},
{grade=118,need_exp=64300,attr_value1=199073,attr_value2=1990729,attr_value3=99536,attr_value4=66358,},
{grade=119,need_exp=65600,attr_value1=202440,attr_value2=2024400,attr_value3=101220,attr_value4=67480,},
{grade=120,need_exp=66900,attr_value1=205835,attr_value2=2058353,attr_value3=102918,attr_value4=68612,},
{grade=121,need_exp=68300,attr_value1=209259,attr_value2=2092588,attr_value3=104629,attr_value4=69753,},
{grade=122,need_exp=69700,attr_value1=212710,attr_value2=2127106,attr_value3=106355,attr_value4=70904,},
{grade=123,need_exp=71100,attr_value1=216190,attr_value2=2161906,attr_value3=108095,attr_value4=72064,},
{grade=124,need_exp=72500,attr_value1=219699,attr_value2=2196988,attr_value3=109849,attr_value4=73233,},
{grade=125,need_exp=73900,attr_value1=223235,attr_value2=2232353,attr_value3=111618,attr_value4=74412,},
{grade=126,need_exp=75400,attr_value1=226800,attr_value2=2268000,attr_value3=113400,attr_value4=75600,},
{grade=127,need_exp=76900,attr_value1=230393,attr_value2=2303929,attr_value3=115196,attr_value4=76798,},
{grade=128,need_exp=78400,attr_value1=234014,attr_value2=2340141,attr_value3=117007,attr_value4=78005,},
{grade=129,need_exp=79900,attr_value1=237664,attr_value2=2376635,attr_value3=118832,attr_value4=79221,},
{grade=130,need_exp=81400,attr_value1=241341,attr_value2=2413412,attr_value3=120671,attr_value4=80447,},
{grade=131,need_exp=83000,attr_value1=245047,attr_value2=2450471,attr_value3=122524,attr_value4=81682,},
{grade=132,need_exp=84600,attr_value1=248781,attr_value2=2487812,attr_value3=124391,attr_value4=82927,},
{grade=133,need_exp=86200,attr_value1=252544,attr_value2=2525435,attr_value3=126272,attr_value4=84181,},
{grade=134,need_exp=87800,attr_value1=256334,attr_value2=2563341,attr_value3=128167,attr_value4=85445,},
{grade=135,need_exp=89400,attr_value1=260153,attr_value2=2601529,attr_value3=130076,attr_value4=86718,},
{grade=136,need_exp=91100,attr_value1=264000,attr_value2=2640000,attr_value3=132000,attr_value4=88000,},
{grade=137,need_exp=92800,attr_value1=267875,attr_value2=2678753,attr_value3=133938,attr_value4=89292,},
{grade=138,need_exp=94500,attr_value1=271779,attr_value2=2717788,attr_value3=135889,attr_value4=90593,},
{grade=139,need_exp=96200,attr_value1=275710,attr_value2=2757106,attr_value3=137855,attr_value4=91904,},
{grade=140,need_exp=97900,attr_value1=279670,attr_value2=2796706,attr_value3=139835,attr_value4=93224,},
{grade=141,need_exp=99700,attr_value1=283659,attr_value2=2836588,attr_value3=141829,attr_value4=94553,},
{grade=142,need_exp=101500,attr_value1=287675,attr_value2=2876753,attr_value3=143838,attr_value4=95892,},
{grade=143,need_exp=103300,attr_value1=291720,attr_value2=2917200,attr_value3=145860,attr_value4=97240,},
{grade=144,need_exp=105100,attr_value1=295793,attr_value2=2957929,attr_value3=147896,attr_value4=98598,},
{grade=145,need_exp=106900,attr_value1=299894,attr_value2=2998941,attr_value3=149947,attr_value4=99965,},
{grade=146,need_exp=108800,attr_value1=304024,attr_value2=3040235,attr_value3=152012,attr_value4=101341,},
{grade=147,need_exp=110700,attr_value1=308181,attr_value2=3081812,attr_value3=154091,attr_value4=102727,},
{grade=148,need_exp=112600,attr_value1=312367,attr_value2=3123671,attr_value3=156184,attr_value4=104122,},
{grade=149,need_exp=114500,attr_value1=316581,attr_value2=3165812,attr_value3=158291,attr_value4=105527,},
{grade=150,need_exp=116400,attr_value1=320824,attr_value2=3208235,attr_value3=160412,attr_value4=106941,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=1,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=2,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,},
{solt=3,}
},

grade_meta_table_map={
[458]=5,	-- depth:1
[457]=4,	-- depth:1
[456]=3,	-- depth:1
[459]=6,	-- depth:1
[455]=2,	-- depth:1
[460]=7,	-- depth:1
[453]=151,	-- depth:1
[452]=150,	-- depth:1
[451]=149,	-- depth:1
[445]=143,	-- depth:1
[449]=147,	-- depth:1
[448]=146,	-- depth:1
[447]=145,	-- depth:1
[446]=144,	-- depth:1
[444]=142,	-- depth:1
[443]=141,	-- depth:1
[442]=140,	-- depth:1
[441]=139,	-- depth:1
[440]=138,	-- depth:1
[461]=8,	-- depth:1
[439]=137,	-- depth:1
[438]=136,	-- depth:1
[450]=148,	-- depth:1
[462]=9,	-- depth:1
[478]=25,	-- depth:1
[464]=11,	-- depth:1
[488]=35,	-- depth:1
[437]=135,	-- depth:1
[487]=34,	-- depth:1
[486]=33,	-- depth:1
[485]=32,	-- depth:1
[484]=31,	-- depth:1
[483]=30,	-- depth:1
[482]=29,	-- depth:1
[481]=28,	-- depth:1
[480]=27,	-- depth:1
[479]=26,	-- depth:1
[463]=10,	-- depth:1
[477]=24,	-- depth:1
[475]=22,	-- depth:1
[474]=21,	-- depth:1
[473]=20,	-- depth:1
[472]=19,	-- depth:1
[471]=18,	-- depth:1
[470]=17,	-- depth:1
[469]=16,	-- depth:1
[468]=15,	-- depth:1
[467]=14,	-- depth:1
[466]=13,	-- depth:1
[465]=12,	-- depth:1
[476]=23,	-- depth:1
[436]=134,	-- depth:1
[399]=97,	-- depth:1
[434]=132,	-- depth:1
[404]=102,	-- depth:1
[403]=101,	-- depth:1
[402]=100,	-- depth:1
[401]=99,	-- depth:1
[400]=98,	-- depth:1
[398]=96,	-- depth:1
[397]=95,	-- depth:1
[396]=94,	-- depth:1
[395]=93,	-- depth:1
[394]=92,	-- depth:1
[393]=91,	-- depth:1
[405]=103,	-- depth:1
[392]=90,	-- depth:1
[390]=88,	-- depth:1
[389]=87,	-- depth:1
[388]=86,	-- depth:1
[387]=85,	-- depth:1
[386]=84,	-- depth:1
[385]=83,	-- depth:1
[384]=82,	-- depth:1
[383]=81,	-- depth:1
[382]=80,	-- depth:1
[381]=79,	-- depth:1
[489]=36,	-- depth:1
[391]=89,	-- depth:1
[406]=104,	-- depth:1
[407]=105,	-- depth:1
[408]=106,	-- depth:1
[433]=131,	-- depth:1
[432]=130,	-- depth:1
[431]=129,	-- depth:1
[430]=128,	-- depth:1
[429]=127,	-- depth:1
[428]=126,	-- depth:1
[427]=125,	-- depth:1
[426]=124,	-- depth:1
[425]=123,	-- depth:1
[424]=122,	-- depth:1
[423]=121,	-- depth:1
[422]=120,	-- depth:1
[421]=119,	-- depth:1
[420]=118,	-- depth:1
[419]=117,	-- depth:1
[418]=116,	-- depth:1
[417]=115,	-- depth:1
[416]=114,	-- depth:1
[415]=113,	-- depth:1
[414]=112,	-- depth:1
[413]=111,	-- depth:1
[412]=110,	-- depth:1
[411]=109,	-- depth:1
[410]=108,	-- depth:1
[409]=107,	-- depth:1
[435]=133,	-- depth:1
[490]=37,	-- depth:1
[566]=415,	-- depth:2
[492]=39,	-- depth:1
[573]=422,	-- depth:2
[572]=421,	-- depth:2
[571]=420,	-- depth:2
[570]=419,	-- depth:2
[569]=418,	-- depth:2
[568]=417,	-- depth:2
[567]=416,	-- depth:2
[380]=78,	-- depth:1
[565]=414,	-- depth:2
[564]=413,	-- depth:2
[563]=412,	-- depth:2
[574]=423,	-- depth:2
[562]=411,	-- depth:2
[560]=409,	-- depth:2
[559]=408,	-- depth:2
[558]=407,	-- depth:2
[557]=406,	-- depth:2
[556]=405,	-- depth:2
[555]=404,	-- depth:2
[554]=403,	-- depth:2
[553]=402,	-- depth:2
[552]=401,	-- depth:2
[551]=400,	-- depth:2
[550]=399,	-- depth:2
[561]=410,	-- depth:2
[575]=424,	-- depth:2
[576]=425,	-- depth:2
[577]=426,	-- depth:2
[602]=451,	-- depth:2
[601]=450,	-- depth:2
[600]=449,	-- depth:2
[599]=448,	-- depth:2
[598]=447,	-- depth:2
[597]=446,	-- depth:2
[596]=445,	-- depth:2
[595]=444,	-- depth:2
[594]=443,	-- depth:2
[593]=442,	-- depth:2
[592]=441,	-- depth:2
[591]=440,	-- depth:2
[590]=439,	-- depth:2
[589]=438,	-- depth:2
[588]=437,	-- depth:2
[587]=436,	-- depth:2
[586]=435,	-- depth:2
[585]=434,	-- depth:2
[584]=433,	-- depth:2
[583]=432,	-- depth:2
[582]=431,	-- depth:2
[581]=430,	-- depth:2
[580]=429,	-- depth:2
[579]=428,	-- depth:2
[578]=427,	-- depth:2
[549]=398,	-- depth:2
[491]=38,	-- depth:1
[548]=397,	-- depth:2
[546]=395,	-- depth:2
[516]=63,	-- depth:1
[515]=62,	-- depth:1
[514]=61,	-- depth:1
[513]=60,	-- depth:1
[512]=59,	-- depth:1
[511]=58,	-- depth:1
[510]=57,	-- depth:1
[509]=56,	-- depth:1
[508]=55,	-- depth:1
[507]=54,	-- depth:1
[506]=53,	-- depth:1
[517]=64,	-- depth:1
[505]=52,	-- depth:1
[503]=50,	-- depth:1
[502]=49,	-- depth:1
[501]=48,	-- depth:1
[500]=47,	-- depth:1
[499]=46,	-- depth:1
[498]=45,	-- depth:1
[497]=44,	-- depth:1
[496]=43,	-- depth:1
[495]=42,	-- depth:1
[494]=41,	-- depth:1
[493]=40,	-- depth:1
[504]=51,	-- depth:1
[518]=65,	-- depth:1
[519]=66,	-- depth:1
[520]=67,	-- depth:1
[545]=394,	-- depth:2
[544]=393,	-- depth:2
[543]=392,	-- depth:2
[542]=391,	-- depth:2
[541]=390,	-- depth:2
[540]=389,	-- depth:2
[539]=388,	-- depth:2
[538]=387,	-- depth:2
[537]=386,	-- depth:2
[536]=385,	-- depth:2
[535]=384,	-- depth:2
[534]=383,	-- depth:2
[533]=382,	-- depth:2
[532]=381,	-- depth:2
[531]=380,	-- depth:2
[530]=77,	-- depth:1
[529]=76,	-- depth:1
[528]=75,	-- depth:1
[527]=74,	-- depth:1
[526]=73,	-- depth:1
[525]=72,	-- depth:1
[524]=71,	-- depth:1
[523]=70,	-- depth:1
[522]=69,	-- depth:1
[521]=68,	-- depth:1
[547]=396,	-- depth:2
[379]=530,	-- depth:2
[302]=453,	-- depth:2
[377]=528,	-- depth:2
[233]=535,	-- depth:3
[232]=534,	-- depth:3
[231]=533,	-- depth:3
[230]=532,	-- depth:3
[229]=531,	-- depth:3
[228]=379,	-- depth:3
[227]=529,	-- depth:2
[226]=377,	-- depth:3
[225]=527,	-- depth:2
[224]=526,	-- depth:2
[223]=525,	-- depth:2
[234]=536,	-- depth:3
[222]=524,	-- depth:2
[220]=522,	-- depth:2
[219]=521,	-- depth:2
[218]=520,	-- depth:2
[217]=519,	-- depth:2
[216]=518,	-- depth:2
[215]=517,	-- depth:2
[214]=516,	-- depth:2
[213]=515,	-- depth:2
[212]=514,	-- depth:2
[211]=513,	-- depth:2
[210]=512,	-- depth:2
[221]=523,	-- depth:2
[209]=511,	-- depth:2
[235]=537,	-- depth:3
[237]=539,	-- depth:3
[261]=563,	-- depth:3
[260]=562,	-- depth:3
[259]=561,	-- depth:3
[258]=560,	-- depth:3
[257]=559,	-- depth:3
[256]=558,	-- depth:3
[255]=557,	-- depth:3
[254]=556,	-- depth:3
[253]=555,	-- depth:3
[252]=554,	-- depth:3
[251]=553,	-- depth:3
[236]=538,	-- depth:3
[250]=552,	-- depth:3
[248]=550,	-- depth:3
[247]=549,	-- depth:3
[246]=548,	-- depth:3
[245]=547,	-- depth:3
[244]=546,	-- depth:3
[243]=545,	-- depth:3
[242]=544,	-- depth:3
[241]=543,	-- depth:3
[240]=542,	-- depth:3
[239]=541,	-- depth:3
[238]=540,	-- depth:3
[249]=551,	-- depth:3
[208]=510,	-- depth:2
[207]=509,	-- depth:2
[206]=508,	-- depth:2
[176]=478,	-- depth:2
[175]=477,	-- depth:2
[174]=476,	-- depth:2
[173]=475,	-- depth:2
[172]=474,	-- depth:2
[171]=473,	-- depth:2
[170]=472,	-- depth:2
[169]=471,	-- depth:2
[168]=470,	-- depth:2
[167]=469,	-- depth:2
[166]=468,	-- depth:2
[177]=479,	-- depth:2
[165]=467,	-- depth:2
[163]=465,	-- depth:2
[162]=464,	-- depth:2
[161]=463,	-- depth:2
[160]=462,	-- depth:2
[159]=461,	-- depth:2
[158]=460,	-- depth:2
[157]=459,	-- depth:2
[156]=458,	-- depth:2
[155]=457,	-- depth:2
[154]=456,	-- depth:2
[153]=455,	-- depth:2
[164]=466,	-- depth:2
[178]=480,	-- depth:2
[179]=481,	-- depth:2
[180]=482,	-- depth:2
[205]=507,	-- depth:2
[204]=506,	-- depth:2
[203]=505,	-- depth:2
[202]=504,	-- depth:2
[201]=503,	-- depth:2
[200]=502,	-- depth:2
[199]=501,	-- depth:2
[198]=500,	-- depth:2
[197]=499,	-- depth:2
[196]=498,	-- depth:2
[195]=497,	-- depth:2
[194]=496,	-- depth:2
[193]=495,	-- depth:2
[192]=494,	-- depth:2
[191]=493,	-- depth:2
[190]=492,	-- depth:2
[189]=491,	-- depth:2
[188]=490,	-- depth:2
[187]=489,	-- depth:2
[186]=488,	-- depth:2
[185]=487,	-- depth:2
[184]=486,	-- depth:2
[183]=485,	-- depth:2
[182]=484,	-- depth:2
[181]=483,	-- depth:2
[262]=564,	-- depth:3
[263]=565,	-- depth:3
[264]=566,	-- depth:3
[265]=567,	-- depth:3
[347]=196,	-- depth:3
[346]=195,	-- depth:3
[345]=194,	-- depth:3
[344]=193,	-- depth:3
[343]=192,	-- depth:3
[342]=191,	-- depth:3
[341]=190,	-- depth:3
[340]=189,	-- depth:3
[339]=188,	-- depth:3
[338]=187,	-- depth:3
[337]=186,	-- depth:3
[348]=197,	-- depth:3
[336]=185,	-- depth:3
[334]=183,	-- depth:3
[333]=182,	-- depth:3
[332]=181,	-- depth:3
[331]=180,	-- depth:3
[330]=179,	-- depth:3
[329]=178,	-- depth:3
[328]=177,	-- depth:3
[327]=176,	-- depth:3
[326]=175,	-- depth:3
[325]=174,	-- depth:3
[324]=173,	-- depth:3
[335]=184,	-- depth:3
[349]=198,	-- depth:3
[350]=199,	-- depth:3
[351]=200,	-- depth:3
[376]=225,	-- depth:3
[375]=224,	-- depth:3
[374]=223,	-- depth:3
[373]=222,	-- depth:3
[372]=221,	-- depth:3
[371]=220,	-- depth:3
[370]=219,	-- depth:3
[369]=218,	-- depth:3
[368]=217,	-- depth:3
[367]=216,	-- depth:3
[366]=215,	-- depth:3
[365]=214,	-- depth:3
[364]=213,	-- depth:3
[363]=212,	-- depth:3
[362]=211,	-- depth:3
[361]=210,	-- depth:3
[360]=209,	-- depth:3
[359]=208,	-- depth:3
[358]=207,	-- depth:3
[357]=206,	-- depth:3
[356]=205,	-- depth:3
[355]=204,	-- depth:3
[354]=203,	-- depth:3
[353]=202,	-- depth:3
[352]=201,	-- depth:3
[323]=172,	-- depth:3
[378]=227,	-- depth:3
[322]=171,	-- depth:3
[320]=169,	-- depth:3
[289]=591,	-- depth:3
[288]=590,	-- depth:3
[287]=589,	-- depth:3
[286]=588,	-- depth:3
[285]=587,	-- depth:3
[284]=586,	-- depth:3
[283]=585,	-- depth:3
[282]=584,	-- depth:3
[281]=583,	-- depth:3
[280]=582,	-- depth:3
[279]=581,	-- depth:3
[290]=592,	-- depth:3
[278]=580,	-- depth:3
[276]=578,	-- depth:3
[275]=577,	-- depth:3
[274]=576,	-- depth:3
[273]=575,	-- depth:3
[272]=574,	-- depth:3
[271]=573,	-- depth:3
[270]=572,	-- depth:3
[269]=571,	-- depth:3
[268]=570,	-- depth:3
[267]=569,	-- depth:3
[266]=568,	-- depth:3
[277]=579,	-- depth:3
[291]=593,	-- depth:3
[292]=594,	-- depth:3
[293]=595,	-- depth:3
[319]=168,	-- depth:3
[318]=167,	-- depth:3
[317]=166,	-- depth:3
[316]=165,	-- depth:3
[315]=164,	-- depth:3
[314]=163,	-- depth:3
[313]=162,	-- depth:3
[312]=161,	-- depth:3
[311]=160,	-- depth:3
[310]=159,	-- depth:3
[309]=158,	-- depth:3
[308]=157,	-- depth:3
[307]=156,	-- depth:3
[306]=155,	-- depth:3
[305]=154,	-- depth:3
[304]=153,	-- depth:3
[603]=452,	-- depth:2
[301]=603,	-- depth:3
[300]=602,	-- depth:3
[299]=601,	-- depth:3
[298]=600,	-- depth:3
[297]=599,	-- depth:3
[296]=598,	-- depth:3
[295]=597,	-- depth:3
[294]=596,	-- depth:3
[321]=170,	-- depth:3
[604]=302,	-- depth:3
},
stuff={
{},
{item_id=56402,exp=60,}
},

stuff_meta_table_map={
},
skill_level={
{cost_item_num=0,},
{level=1,skill_desc="道法伤害  <color=#6fbb6f>   +117</color>\n道法护甲  <color=#6fbb6f>   +117</color>\n防    御  <color=#6fbb6f>   +701</color>\n破    甲  <color=#6fbb6f>   +467</color>",attr_value1=117,attr_value2=117,attr_value3=701,attr_value4=467,},
{level=2,skill_desc="道法伤害  <color=#6fbb6f>   +305</color>\n道法护甲  <color=#6fbb6f>   +305</color>\n防    御  <color=#6fbb6f>   +1830</color>\n破    甲  <color=#6fbb6f>   +1220</color>",attr_value1=305,attr_value2=305,attr_value3=1830,attr_value4=1220,},
{level=3,skill_desc="道法伤害  <color=#6fbb6f>   +564</color>\n道法护甲  <color=#6fbb6f>   +565</color>\n防    御  <color=#6fbb6f>   +3388</color>\n破    甲  <color=#6fbb6f>   +2259</color>",attr_value1=564,attr_value2=565,attr_value3=3388,attr_value4=2259,},
{seq=1,need_grade=20,skill_icon=550,skill_name="魂飞魄散",},
{seq=1,level=1,need_grade=20,skill_icon=550,skill_name="魂飞魄散",skill_desc="道法伤害  <color=#6fbb6f>   +557</color>\n道法护甲  <color=#6fbb6f>   +556</color>\n防    御  <color=#6fbb6f>   +3336</color>\n破    甲  <color=#6fbb6f>   +2224</color>",attr_value1=557,attr_value2=556,attr_value3=3336,attr_value4=2224,},
{level=2,skill_desc="道法伤害  <color=#6fbb6f>   +627</color>\n道法护甲  <color=#6fbb6f>   +628</color>\n防    御  <color=#6fbb6f>   +3765</color>\n破    甲  <color=#6fbb6f>   +2510</color>",attr_value1=627,attr_value2=628,attr_value3=3765,attr_value4=2510,},
{level=3,skill_desc="道法伤害  <color=#6fbb6f>   +1255</color>\n道法护甲  <color=#6fbb6f>   +1255</color>\n防    御  <color=#6fbb6f>   +7530</color>\n破    甲  <color=#6fbb6f>   +5020</color>",attr_value1=1255,attr_value2=1255,attr_value3=7530,attr_value4=5020,},
{seq=2,need_grade=40,skill_icon=556,skill_name="鬼火来袭",},
{seq=2,level=1,need_grade=40,skill_icon=556,skill_name="鬼火来袭",skill_desc="道法伤害  <color=#6fbb6f>   +1422</color>\n道法护甲  <color=#6fbb6f>   +1422</color>\n防    御  <color=#6fbb6f>   +8531</color>\n破    甲  <color=#6fbb6f>   +5687</color>\n格挡几率  <color=#6fbb6f>   +1%</color>",attr_value1=1422,attr_value2=1422,attr_value3=8531,attr_value4=5687,attr_id5=149,attr_value5=100,},
{level=2,skill_desc="道法伤害  <color=#6fbb6f>   +1494</color>\n道法护甲  <color=#6fbb6f>   +1493</color>\n防    御  <color=#6fbb6f>   +8959</color>\n破    甲  <color=#6fbb6f>   +5973</color>\n格挡几率  <color=#6fbb6f>   +1%</color>",attr_value1=1494,attr_value2=1493,attr_value3=8959,attr_value4=5973,},
{level=3,skill_desc="道法伤害  <color=#6fbb6f>   +2987</color>\n道法护甲  <color=#6fbb6f>   +2986</color>\n防    御  <color=#6fbb6f>   +17919</color>\n破    甲  <color=#6fbb6f>   +11946</color>\n格挡几率  <color=#6fbb6f>   +1%</color>",attr_value1=2987,attr_value2=2986,attr_value3=17919,attr_value4=11946,},
{seq=3,need_grade=60,skill_icon=551,skill_name="鬼气森森",skill_desc="道法伤害  <color=#6fbb6f>   +0</color>\n道法护甲  <color=#6fbb6f>   +0</color>\n防    御  <color=#6fbb6f>   +0</color>\n破    甲  <color=#6fbb6f>   +0</color>\n",cost_item_num=0,},
{level=1,skill_desc="道法伤害  <color=#6fbb6f>   +2623</color>\n道法护甲  <color=#6fbb6f>   +2623</color>\n防    御  <color=#6fbb6f>   +15737</color>\n破    甲  <color=#6fbb6f>   +10491</color>\n破档几率  <color=#6fbb6f>   +1%</color>",attr_value1=2623,attr_value2=2623,attr_value3=15737,attr_value4=10491,},
{level=2,skill_desc="道法伤害  <color=#6fbb6f>   +2695</color>\n道法护甲  <color=#6fbb6f>   +2694</color>\n防    御  <color=#6fbb6f>   +16166</color>\n破    甲  <color=#6fbb6f>   +10777</color>\n破档几率  <color=#6fbb6f>   +1%</color>",attr_value1=2695,attr_value2=2694,attr_value3=16166,attr_value4=10777,},
{seq=3,level=3,need_grade=60,skill_icon=551,skill_name="鬼气森森",skill_desc="道法伤害  <color=#6fbb6f>   +5389</color>\n道法护甲  <color=#6fbb6f>   +5389</color>\n防    御  <color=#6fbb6f>   +32331</color>\n破    甲  <color=#6fbb6f>   +21554</color>\n破档几率  <color=#6fbb6f>   +1%</color>",attr_value1=5389,attr_value2=5389,attr_value3=32331,attr_value4=21554,attr_id5=150,attr_value5=100,},
{solt=1,skill_icon=2051,skill_name="三幽鬼火",cost_item_num=0,},
{solt=1,skill_icon=2051,skill_name="三幽鬼火",},
{solt=1,skill_icon=2051,skill_name="三幽鬼火",},
{solt=1,skill_icon=2051,skill_name="三幽鬼火",},
{solt=1,skill_icon=2052,skill_name="灵魂超度",},
{solt=1,skill_icon=2052,skill_name="灵魂超度",},
{solt=1,skill_icon=2052,skill_name="灵魂超度",},
{solt=1,skill_icon=2052,skill_name="灵魂超度",},
{solt=1,skill_icon=2053,skill_name="怨魂漩涡",},
{solt=1,skill_icon=2053,skill_name="怨魂漩涡",skill_desc="道法伤害  <color=#6fbb6f>   +1422</color>\n道法护甲  <color=#6fbb6f>   +1422</color>\n防    御  <color=#6fbb6f>   +8531</color>\n破    甲  <color=#6fbb6f>   +5687</color>\n闪避几率  <color=#6fbb6f>   +1%</color>",attr_id5=107,},
{solt=1,skill_icon=2053,skill_name="怨魂漩涡",skill_desc="道法伤害  <color=#6fbb6f>   +1494</color>\n道法护甲  <color=#6fbb6f>   +1493</color>\n防    御  <color=#6fbb6f>   +8959</color>\n破    甲  <color=#6fbb6f>   +5973</color>\n闪避几率  <color=#6fbb6f>   +1%</color>",attr_id5=107,},
{solt=1,skill_icon=2053,skill_name="怨魂漩涡",skill_desc="道法伤害  <color=#6fbb6f>   +2987</color>\n道法护甲  <color=#6fbb6f>   +2986</color>\n防    御  <color=#6fbb6f>   +17919</color>\n破    甲  <color=#6fbb6f>   +11946</color>\n闪避几率  <color=#6fbb6f>   +1%</color>",attr_id5=107,},
{solt=1,skill_icon=2054,skill_name="迷失诡术",},
{solt=1,skill_icon=2054,skill_name="迷失诡术",skill_desc="道法伤害  <color=#6fbb6f>   +2623</color>\n道法护甲  <color=#6fbb6f>   +2623</color>\n防    御  <color=#6fbb6f>   +15737</color>\n破    甲  <color=#6fbb6f>   +10491</color>\n命中几率  <color=#6fbb6f>   +1%</color>",attr_id5=108,},
{solt=1,skill_icon=2054,skill_name="迷失诡术",skill_desc="道法伤害  <color=#6fbb6f>   +2695</color>\n道法护甲  <color=#6fbb6f>   +2694</color>\n防    御  <color=#6fbb6f>   +16166</color>\n破    甲  <color=#6fbb6f>   +10777</color>\n命中几率  <color=#6fbb6f>   +1%</color>",attr_id5=108,},
{solt=1,skill_icon=2054,skill_name="迷失诡术",skill_desc="道法伤害  <color=#6fbb6f>   +5389</color>\n道法护甲  <color=#6fbb6f>   +5389</color>\n防    御  <color=#6fbb6f>   +32331</color>\n破    甲  <color=#6fbb6f>   +21554</color>\n命中几率  <color=#6fbb6f>   +1%</color>",attr_id5=108,},
{solt=2,skill_icon=2039,skill_name="万吸之点",cost_item_num=0,},
{solt=2,skill_icon=2039,skill_name="万吸之点",},
{solt=2,skill_icon=2039,skill_name="万吸之点",},
{solt=2,skill_icon=2039,skill_name="万吸之点",},
{solt=2,skill_icon=2040,skill_name="摄魂夺魄",},
{solt=2,skill_icon=2040,skill_name="摄魂夺魄",},
{solt=2,skill_icon=2040,skill_name="摄魂夺魄",},
{solt=2,skill_icon=2040,skill_name="摄魂夺魄",},
{solt=2,skill_icon=2041,skill_name="心碎鬼怨",},
{solt=2,skill_icon=2041,skill_name="心碎鬼怨",skill_desc="道法伤害  <color=#6fbb6f>   +1422</color>\n道法护甲  <color=#6fbb6f>   +1422</color>\n防    御  <color=#6fbb6f>   +8531</color>\n破    甲  <color=#6fbb6f>   +5687</color>\n高血增伤  <color=#6fbb6f>   +1%</color>",attr_id5=158,},
{solt=2,skill_icon=2041,skill_name="心碎鬼怨",skill_desc="道法伤害  <color=#6fbb6f>   +1494</color>\n道法护甲  <color=#6fbb6f>   +1493</color>\n防    御  <color=#6fbb6f>   +8959</color>\n破    甲  <color=#6fbb6f>   +5973</color>\n高血增伤  <color=#6fbb6f>   +1%</color>",attr_id5=158,},
{solt=2,skill_icon=2041,skill_name="心碎鬼怨",skill_desc="道法伤害  <color=#6fbb6f>   +2987</color>\n道法护甲  <color=#6fbb6f>   +2986</color>\n防    御  <color=#6fbb6f>   +17919</color>\n破    甲  <color=#6fbb6f>   +11946</color>\n高血增伤  <color=#6fbb6f>   +1%</color>",attr_id5=158,},
{solt=2,skill_icon=2042,skill_name="元恶一击",},
{solt=2,skill_icon=2042,skill_name="元恶一击",skill_desc="道法伤害  <color=#6fbb6f>   +2623</color>\n道法护甲  <color=#6fbb6f>   +2623</color>\n防    御  <color=#6fbb6f>   +15737</color>\n破    甲  <color=#6fbb6f>   +10491</color>\n虚弱增伤  <color=#6fbb6f>   +1%</color>",attr_id5=159,},
{solt=2,skill_icon=2042,skill_name="元恶一击",skill_desc="道法伤害  <color=#6fbb6f>   +2695</color>\n道法护甲  <color=#6fbb6f>   +2694</color>\n防    御  <color=#6fbb6f>   +16166</color>\n破    甲  <color=#6fbb6f>   +10777</color>\n虚弱增伤  <color=#6fbb6f>   +1%</color>",attr_id5=159,},
{solt=2,skill_icon=2042,skill_name="元恶一击",skill_desc="道法伤害  <color=#6fbb6f>   +5389</color>\n道法护甲  <color=#6fbb6f>   +5389</color>\n防    御  <color=#6fbb6f>   +32331</color>\n破    甲  <color=#6fbb6f>   +21554</color>\n虚弱增伤  <color=#6fbb6f>   +1%</color>",attr_id5=159,},
{solt=3,skill_icon=2047,skill_name="恶鸦空袭",cost_item_num=0,},
{solt=3,skill_icon=2047,skill_name="恶鸦空袭",},
{solt=3,skill_icon=2047,skill_name="恶鸦空袭",},
{solt=3,skill_icon=2047,skill_name="恶鸦空袭",},
{solt=3,skill_icon=2048,skill_name="燃烧大地",},
{solt=3,skill_icon=2048,skill_name="燃烧大地",},
{solt=3,skill_icon=2048,skill_name="燃烧大地",},
{solt=3,skill_icon=2048,skill_name="燃烧大地",},
{solt=3,skill_icon=2049,skill_name="猛鬼火焰",},
{solt=3,skill_icon=2049,skill_name="猛鬼火焰",skill_desc="道法伤害  <color=#6fbb6f>   +1422</color>\n道法护甲  <color=#6fbb6f>   +1422</color>\n防    御  <color=#6fbb6f>   +8531</color>\n破    甲  <color=#6fbb6f>   +5687</color>\n技能增伤  <color=#6fbb6f>   +1%</color>",attr_id5=168,},
{solt=3,skill_icon=2049,skill_name="猛鬼火焰",skill_desc="道法伤害  <color=#6fbb6f>   +1494</color>\n道法护甲  <color=#6fbb6f>   +1493</color>\n防    御  <color=#6fbb6f>   +8959</color>\n破    甲  <color=#6fbb6f>   +5973</color>\n技能增伤  <color=#6fbb6f>   +1%</color>",attr_id5=168,},
{solt=3,skill_icon=2049,skill_name="猛鬼火焰",skill_desc="道法伤害  <color=#6fbb6f>   +2987</color>\n道法护甲  <color=#6fbb6f>   +2986</color>\n防    御  <color=#6fbb6f>   +17919</color>\n破    甲  <color=#6fbb6f>   +11946</color>\n技能增伤  <color=#6fbb6f>   +1%</color>",attr_id5=168,},
{solt=3,skill_icon=2050,skill_name="鬼哭脸影",},
{solt=3,skill_icon=2050,skill_name="鬼哭脸影",skill_desc="道法伤害  <color=#6fbb6f>   +2623</color>\n道法护甲  <color=#6fbb6f>   +2623</color>\n防    御  <color=#6fbb6f>   +15737</color>\n破    甲  <color=#6fbb6f>   +10491</color>\n技能免伤  <color=#6fbb6f>   +1%</color>",attr_id5=169,},
{solt=3,skill_icon=2050,skill_name="鬼哭脸影",skill_desc="道法伤害  <color=#6fbb6f>   +2695</color>\n道法护甲  <color=#6fbb6f>   +2694</color>\n防    御  <color=#6fbb6f>   +16166</color>\n破    甲  <color=#6fbb6f>   +10777</color>\n技能免伤  <color=#6fbb6f>   +1%</color>",attr_id5=169,},
{solt=3,skill_icon=2050,skill_name="鬼哭脸影",skill_desc="道法伤害  <color=#6fbb6f>   +5389</color>\n道法护甲  <color=#6fbb6f>   +5389</color>\n防    御  <color=#6fbb6f>   +32331</color>\n破    甲  <color=#6fbb6f>   +21554</color>\n技能免伤  <color=#6fbb6f>   +1%</color>",attr_id5=169,}
},

skill_level_meta_table_map={
[5]=1,	-- depth:1
[37]=5,	-- depth:2
[9]=13,	-- depth:1
[53]=5,	-- depth:2
[21]=5,	-- depth:2
[45]=13,	-- depth:1
[57]=9,	-- depth:2
[29]=13,	-- depth:1
[61]=13,	-- depth:1
[41]=9,	-- depth:2
[25]=9,	-- depth:2
[20]=4,	-- depth:1
[51]=3,	-- depth:1
[52]=20,	-- depth:2
[36]=20,	-- depth:2
[19]=51,	-- depth:2
[34]=2,	-- depth:1
[35]=51,	-- depth:2
[50]=34,	-- depth:2
[18]=34,	-- depth:2
[7]=6,	-- depth:1
[8]=6,	-- depth:1
[22]=6,	-- depth:1
[54]=6,	-- depth:1
[55]=7,	-- depth:2
[56]=8,	-- depth:2
[40]=8,	-- depth:2
[39]=7,	-- depth:2
[38]=6,	-- depth:1
[23]=7,	-- depth:2
[24]=8,	-- depth:2
[11]=10,	-- depth:1
[12]=10,	-- depth:1
[14]=16,	-- depth:1
[15]=16,	-- depth:1
[58]=10,	-- depth:1
[59]=11,	-- depth:2
[60]=12,	-- depth:2
[62]=14,	-- depth:2
[32]=16,	-- depth:1
[47]=15,	-- depth:2
[46]=14,	-- depth:2
[44]=12,	-- depth:2
[43]=11,	-- depth:2
[42]=10,	-- depth:1
[63]=15,	-- depth:2
[31]=15,	-- depth:2
[30]=14,	-- depth:2
[28]=12,	-- depth:2
[27]=11,	-- depth:2
[26]=10,	-- depth:1
[48]=16,	-- depth:1
[64]=16,	-- depth:1
},
cut_iron={
{attr_id2=101,attr_id3=104,},
{seq=1,cost_item_id=56320,attr_value1=1200,attr_id2=103,attr_value2=600,attr_id3=105,attr_value3=100,},
{seq=2,cost_item_id=56321,attr_id1=103,attr_value1=800,attr_value2=800,attr_value3=200,},
{seq=3,cost_item_id=56322,attr_value1=3333,attr_value2=1111,attr_value3=278,attr_id4=124,attr_value4=100,},
{seq=4,cost_item_id=56323,attr_id1=101,attr_value1=66660,attr_value2=2222,attr_value3=556,attr_id4=123,attr_value4=200,},
{seq=5,cost_item_id=56324,attr_value1=60000,attr_id2=103,attr_value2=3000,attr_id3=105,attr_value3=500,attr_id4=124,}
},

cut_iron_meta_table_map={
[6]=5,	-- depth:1
},
grade_default_table={solt=0,grade=0,need_exp=1,attr_id1=102,attr_value1=0,attr_id2=101,attr_value2=0,attr_id3=103,attr_value3=0,attr_id4=104,attr_value4=0,attr_id5=0,attr_value5=0,},

stuff_default_table={item_id=56401,exp=20,},

skill_level_default_table={solt=0,seq=0,level=0,need_grade=10,skill_icon=555,skill_name="群魂悲渡",skill_desc="道法伤害  <color=#6fbb6f>   +0</color>\n道法护甲  <color=#6fbb6f>   +0</color>\n防    御  <color=#6fbb6f>   +0</color>\n破    甲  <color=#6fbb6f>   +0</color>",cost_item_id=56400,cost_item_num=1,attr_id1=105,attr_value1=0,attr_id2=106,attr_value2=0,attr_id3=103,attr_value3=0,attr_id4=104,attr_value4=0,attr_id5=0,attr_value5=0,},

cut_iron_default_table={seq=0,max_level=9999,cost_item_id=56319,cost_item_num=1,attr_id1=102,attr_value1=428,attr_id2=104,attr_value2=4286,attr_id3=106,attr_value3=143,attr_id4=0,attr_value4=0,stage_level=100,}

}

