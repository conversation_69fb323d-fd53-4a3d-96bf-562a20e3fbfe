GameAssistantWGData = GameAssistantWGData or BaseClass()

local ASSISTANT_TYPE = {
	OWNER = 0,
}

-- 终身特惠id
local life_indulgence_ids = {
	[8] = true, [9] = true, [10] = true, [11] = true, [12] = true, [13] = true, [14] = true
}

-- 天渊臻品id
local twin_direct_purchase_ids = {
	[15] = true, [16] = true, [17] = true, [18] = true, [19] = true, [20] = true, [21] = true
}


function GameAssistantWGData:__init()
	if GameAssistantWGData.Instance then
		error("[GameAssistantWGData] Attempt to create singleton twice!")
		return
	end

	GameAssistantWGData.Instance = self
	self:InitConfig()

	-- 终身特惠活动状态
	self.life_indulgence_status = false
end

function GameAssistantWGData:__delete()
    GameAssistantWGData.Instance = nil
end

function GameAssistantWGData:InitConfig()
	local auto_cfg = ConfigManager.Instance:GetAutoConfig("game_assistant_auto")
	self.assistant_type_cfg = auto_cfg.assistant_type			-- 所有类型
	--今日玩法
	self.assistant_today_cfg1 = auto_cfg.assistant_today_1
	self.assistant_today_cfg2 = auto_cfg.assistant_today_2
	--灵玉必买
	self.lingyubimai_cfg1 = auto_cfg.lingyubimai_1
	self.lingyubimai_cfg2 = auto_cfg.lingyubimai_2
	--热门直购
	self.remengzhigou_cfg1 = auto_cfg.remengzhigou_1
	self.remengzhigou_cfg2 = auto_cfg.remengzhigou_2
	--上架外观
	self.shangjiawaiguan_cfg1 = auto_cfg.shangjiawaiguan_1
	self.shangjiawaiguan_cfg2 = auto_cfg.shangjiawaiguan_2
	--我要仙玉
	self.assistant_xianyu_cfg1 = auto_cfg.assistant_xianyu_1
	self.assistant_xianyu_cfg2 = auto_cfg.assistant_xianyu_2
	--我要经验
	self.assistant_jingyan_cfg1 = auto_cfg.assistant_jingyan_1
	self.assistant_jingyan_cfg2 = auto_cfg.assistant_jingyan_2
	--我要装备
	self.assistant_zhaugnbei_cfg1 = auto_cfg.assistant_zhaugnbei_1
	self.assistant_zhaugnbei_cfg2 = auto_cfg.assistant_zhaugnbei_2
end

-- 检测是否开启
function GameAssistantWGData:CheckOpen(id)
	local is_show = true
	if life_indulgence_ids[id] then
		is_show = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.LIFE_INDULGENCE_ACTIVITY)
	end

	if twin_direct_purchase_ids[id] then
		is_show = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_Twin_Direct_Purchase)
	end

	return is_show
end

-- 拆分大类型
function GameAssistantWGData:SplitAllAssistantList()
	self.type_list = {}

	if not self.assistant_type_cfg then
		return
	end

	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for _, data in pairs(self.assistant_type_cfg) do
		if cur_day <= data.close_day then
			if not self.type_list[data.assistant_big_type] then
				self.type_list[data.assistant_big_type] = {}
				self.type_list[data.assistant_big_type].sub_list = {}
			end
	
			-- 本身数据
			if data.assistant_sub_type == ASSISTANT_TYPE.OWNER then
				self.type_list[data.assistant_big_type].main_data = data
			else
				self.type_list[data.assistant_big_type].sub_list[data.assistant_sub_type] = data
			end
		end
	end
end

function GameAssistantWGData:getAllType()
	return self.assistant_type_cfg or {}
end

function GameAssistantWGData:getTypeState(tab_index)
	local index = math.floor(tab_index / 10)
	local cfg = self.assistant_type_cfg and self.assistant_type_cfg[index]
	local close_day = cfg and cfg.close_day or 9999
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	return close_day > cur_day
end

-- 获取当前的列表数据
function GameAssistantWGData:GetAssistantList()
	self:SplitAllAssistantList()
	return self.type_list
end

-- 获取对应配置的对应id的数据
function GameAssistantWGData:GetCurrCfgShowData(show_id, cur_cfg)
	return cur_cfg and cur_cfg[show_id]
end

--获取当前的天数对应的列表
function GameAssistantWGData:GetCurrCfgListByDay(now_day, cur_cfg1, cur_cfg2)
	if (not cur_cfg1) or (not cur_cfg2) then
		return nil
	end

	local show_id_list = nil
	local show_id_data_list = {}

	for _, cfg_data in pairs(cur_cfg1) do
		if now_day >= cfg_data.min_day and now_day <= cfg_data.max_day then
			show_id_list = cfg_data.show_id_list
		end
	end

	if show_id_list ~= nil and show_id_list ~= "" then
		local id_list = Split(show_id_list, "|")

		for _, show_id in ipairs(id_list) do
			local show_id_num = tonumber(show_id) or 0
			local data = self:GetCurrCfgShowData(show_id_num, cur_cfg2)
			table.insert(show_id_data_list, data)
		end
	end

	return show_id_data_list
end

--获取当前符合角色等级的列表
function GameAssistantWGData:GetLimitLevelList(list)
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local back_list = {}

	for _, v in ipairs(list) do
		if self:CheckOpen(v.get_way_id) then
			if v.show_level == nil or v.show_level == "" then
				table.insert(back_list, v)
			else
				if role_level >= v.show_level then
					table.insert(back_list, v)
				end
			end
		end
	end

	return back_list
end

-- 不留缓存了就切割了一次字符串
-- 依据当前天数获取今日玩法
function GameAssistantWGData:GetTodayCfgListByDay(now_day)
	local list = self:GetCurrCfgListByDay(now_day, self.assistant_today_cfg1, self.assistant_today_cfg2)
	return self:GetLimitLevelList(list)
end

-- 依据当前天数获取灵玉必买
function GameAssistantWGData:GetBiMaiCfgListByDay(now_day)
	local list = self:GetCurrCfgListByDay(now_day, self.lingyubimai_cfg1, self.lingyubimai_cfg2)
	return self:GetLimitLevelList(list)
end

-- 依据当前天数获取热门直购
function GameAssistantWGData:GetZhiGouCfgListByDay(now_day)
	local list = self:GetCurrCfgListByDay(now_day, self.remengzhigou_cfg1, self.remengzhigou_cfg2)
	return self:GetLimitLevelList(list)
end

-- 依据当前天数获取上架外观
function GameAssistantWGData:GetWaiGuanCfgListByDay(now_day)
	local list = self:GetCurrCfgListByDay(now_day, self.shangjiawaiguan_cfg1, self.shangjiawaiguan_cfg2)
	return self:GetLimitLevelList(list)
end

-- 依据当前天数获取我要仙玉
function GameAssistantWGData:GetXianYuCfgListByDay(now_day)
	local list = self:GetCurrCfgListByDay(now_day, self.assistant_xianyu_cfg1, self.assistant_xianyu_cfg2)
	return self:GetLimitLevelList(list)
end

-- 依据当前天数获取我要经验
function GameAssistantWGData:GetExpCfgListByDay(now_day)
	local list = self:GetCurrCfgListByDay(now_day, self.assistant_jingyan_cfg1, self.assistant_jingyan_cfg2)
	return self:GetLimitLevelList(list)
end

-- 依据当前天数获取我要装备
function GameAssistantWGData:GetEquipCfgListByDay(now_day)
	local list = self:GetCurrCfgListByDay(now_day, self.assistant_zhaugnbei_cfg1, self.assistant_zhaugnbei_cfg2)
	return self:GetLimitLevelList(list)
end