YaoShouPlazaSceneLogic = YaoShouPlazaSceneLogic or BaseClass(CommonFbLogic)

function YaoShouPlazaSceneLogic:__init()
	
end

function YaoShouPlazaSceneLogic:__delete()
	
end

function YaoShouPlazaSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	FuBenWGCtrl.Instance:OpenTaskFollow()
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Activity.OutFbTips)
	self:SetGuaiJi(GUAI_JI_TYPE.MONSTER)
end

function YaoShouPlazaSceneLogic:OpenFbSceneCd()
	local data = ActivityWGData.Instance:GetDailyActStatus(Day_Act_Type.YaoShouPlaza)
	local out_time = data and data.next_stop_time or 0
	if out_time > TimeWGCtrl.Instance:GetServerTime() then
		MainuiWGCtrl.Instance:SetFbIconEndCountDown(out_time)
	end
end

function YaoShouPlazaSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
end

-- 角色是否是敌人
function YaoShouPlazaSceneLogic:IsRoleEnemy(target_obj, main_role)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end
-- 是否是挂机打怪的敌人
function YaoShouPlazaSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() == SceneObjType.Role then
		return false
	end
	return true
end
function YaoShouPlazaSceneLogic:Out()
	CommonFbLogic.Out(self)
	FuBenWGCtrl.Instance:CloseTaskFollow()
	ActivityWGData.Instance:InitYaoShouPlazaInfo()
	FuBenWGCtrl.Instance:SetLevaeFbContent(Language.Activity.ConfirmLevelFB)
end