-- S-随机活动1.xls
local item_table={
[1]={item_id=0,num=1,is_bind=1},
[2]={item_id=28779,num=1,is_bind=1},
[3]={item_id=28780,num=1,is_bind=1},
[4]={item_id=28781,num=1,is_bind=1},
[5]={item_id=28782,num=1,is_bind=1},
[6]={item_id=28783,num=1,is_bind=1},
[7]={item_id=28784,num=1,is_bind=1},
[8]={item_id=28785,num=1,is_bind=1},
[9]={item_id=28786,num=1,is_bind=1},
[10]={item_id=28860,num=1,is_bind=1},
[11]={item_id=28789,num=1,is_bind=1},
[12]={item_id=28791,num=1,is_bind=1},
[13]={item_id=28792,num=1,is_bind=1},
[14]={item_id=28793,num=1,is_bind=1},
[15]={item_id=28794,num=1,is_bind=1},
[16]={item_id=28795,num=1,is_bind=1},
[17]={item_id=28796,num=1,is_bind=1},
[18]={item_id=28797,num=1,is_bind=1},
[19]={item_id=28798,num=1,is_bind=1},
[20]={item_id=28799,num=1,is_bind=1},
[21]={item_id=28790,num=1,is_bind=1},
[22]={item_id=28803,num=1,is_bind=1},
[23]={item_id=28804,num=1,is_bind=1},
[24]={item_id=28861,num=1,is_bind=1},
[25]={item_id=28862,num=1,is_bind=1},
[26]={item_id=26200,num=30,is_bind=1},
[27]={item_id=22618,num=1,is_bind=1},
[28]={item_id=26149,num=15,is_bind=1},
[29]={item_id=26148,num=10,is_bind=1},
[30]={item_id=26120,num=10,is_bind=1},
[31]={item_id=26149,num=20,is_bind=1},
[32]={item_id=26148,num=15,is_bind=1},
[33]={item_id=26120,num=15,is_bind=1},
[34]={item_id=26149,num=25,is_bind=1},
[35]={item_id=26148,num=20,is_bind=1},
[36]={item_id=26120,num=20,is_bind=1},
[37]={item_id=26149,num=30,is_bind=1},
[38]={item_id=26148,num=25,is_bind=1},
[39]={item_id=26120,num=25,is_bind=1},
[40]={item_id=26191,num=1,is_bind=1},
[41]={item_id=26357,num=1,is_bind=1},
[42]={item_id=26123,num=1,is_bind=1},
[43]={item_id=26149,num=100,is_bind=1},
[44]={item_id=26351,num=2,is_bind=1},
[45]={item_id=26122,num=1,is_bind=1},
[46]={item_id=26149,num=80,is_bind=1},
[47]={item_id=26351,num=1,is_bind=1},
[48]={item_id=26149,num=60,is_bind=1},
[49]={item_id=26148,num=40,is_bind=1},
[50]={item_id=44072,num=3,is_bind=1},
[51]={item_id=26353,num=5,is_bind=1},
[52]={item_id=26363,num=1,is_bind=1},
[53]={item_id=27613,num=1,is_bind=1},
[54]={item_id=26354,num=1,is_bind=1},
[55]={item_id=27026,num=1,is_bind=1},
[56]={item_id=48089,num=1,is_bind=1},
[57]={item_id=26502,num=1,is_bind=1},
[58]={item_id=27027,num=1,is_bind=1},
[59]={item_id=39105,num=500,is_bind=1},
[60]={item_id=45003,num=1,is_bind=1},
[61]={item_id=27028,num=1,is_bind=1},
[62]={item_id=48073,num=1,is_bind=1},
[63]={item_id=26193,num=1,is_bind=1},
[64]={item_id=26503,num=1,is_bind=1},
[65]={item_id=57357,num=1,is_bind=1},
[66]={item_id=48177,num=1,is_bind=1},
[67]={item_id=27029,num=1,is_bind=1},
[68]={item_id=45004,num=1,is_bind=1},
[69]={item_id=27030,num=1,is_bind=1},
[70]={item_id=27031,num=1,is_bind=1},
[71]={item_id=27032,num=1,is_bind=1},
[72]={item_id=27838,num=1,is_bind=1},
[73]={item_id=27033,num=1,is_bind=1},
[74]={item_id=57358,num=1,is_bind=1},
[75]={item_id=44072,num=1,is_bind=1},
[76]={item_id=39144,num=500,is_bind=1},
[77]={item_id=28845,num=1,is_bind=1},
[78]={item_id=39142,num=500,is_bind=1},
[79]={item_id=26409,num=5,is_bind=1},
[80]={item_id=46513,num=5,is_bind=1},
[81]={item_id=44084,num=1,is_bind=1},
[82]={item_id=27612,num=10,is_bind=1},
[83]={item_id=27612,num=20,is_bind=1},
[84]={item_id=26409,num=20,is_bind=1},
[85]={item_id=48090,num=1,is_bind=1},
[86]={item_id=48095,num=1,is_bind=1},
[87]={item_id=57356,num=1,is_bind=1},
[88]={item_id=44072,num=2,is_bind=1},
[89]={item_id=22012,num=1,is_bind=1},
[90]={item_id=50019,num=2,is_bind=1},
[91]={item_id=26410,num=10,is_bind=1},
[92]={item_id=27036,num=1,is_bind=1},
[93]={item_id=39152,num=3,is_bind=1},
[94]={item_id=27034,num=1,is_bind=1},
[95]={item_id=27659,num=2,is_bind=1},
[96]={item_id=45017,num=1,is_bind=1},
[97]={item_id=27035,num=1,is_bind=1},
[98]={item_id=48182,num=4,is_bind=1},
[99]={item_id=37219,num=1,is_bind=1},
[100]={item_id=27037,num=1,is_bind=1},
[101]={item_id=29615,num=2,is_bind=1},
[102]={item_id=26353,num=10,is_bind=1},
[103]={item_id=27038,num=1,is_bind=1},
[104]={item_id=26193,num=5,is_bind=1},
[105]={item_id=27039,num=1,is_bind=1},
[106]={item_id=39105,num=800,is_bind=1},
[107]={item_id=37051,num=1,is_bind=1},
[108]={item_id=27040,num=1,is_bind=1},
[109]={item_id=48071,num=5,is_bind=1},
[110]={item_id=27041,num=1,is_bind=1},
[111]={item_id=37217,num=1,is_bind=1},
[112]={item_id=27042,num=1,is_bind=1},
[113]={item_id=32285,num=2,is_bind=1},
[114]={item_id=27043,num=1,is_bind=1},
[115]={item_id=29621,num=150,is_bind=1},
[116]={item_id=27044,num=1,is_bind=1},
[117]={item_id=27045,num=1,is_bind=1},
[118]={item_id=37111,num=1,is_bind=1},
[119]={item_id=27046,num=1,is_bind=1},
[120]={item_id=27047,num=1,is_bind=1},
[121]={item_id=43805,num=10,is_bind=1},
[122]={item_id=27048,num=1,is_bind=1},
[123]={item_id=48071,num=10,is_bind=1},
[124]={item_id=48092,num=1,is_bind=1},
[125]={item_id=29621,num=500,is_bind=1},
[126]={item_id=27049,num=1,is_bind=1},
[127]={item_id=48093,num=1,is_bind=1},
[128]={item_id=37411,num=1,is_bind=1},
[129]={item_id=48133,num=1,is_bind=1},
[130]={item_id=26353,num=1,is_bind=1},
[131]={item_id=48184,num=1,is_bind=1},
[132]={item_id=48183,num=1,is_bind=1},
[133]={item_id=39152,num=1,is_bind=1},
[134]={item_id=45005,num=1,is_bind=1},
[135]={item_id=45006,num=1,is_bind=1},
[136]={item_id=29265,num=1,is_bind=1},
[137]={item_id=44073,num=1,is_bind=1},
[138]={item_id=43805,num=5,is_bind=1},
[139]={item_id=27612,num=2,is_bind=1},
[140]={item_id=44070,num=1,is_bind=1},
[141]={item_id=27836,num=1,is_bind=1},
[142]={item_id=27837,num=1,is_bind=1},
[143]={item_id=29264,num=1,is_bind=1},
[144]={item_id=29621,num=40,is_bind=1},
[145]={item_id=29622,num=1,is_bind=1},
[146]={item_id=26410,num=1,is_bind=1},
[147]={item_id=48090,num=2,is_bind=1},
[148]={item_id=48071,num=2,is_bind=1},
[149]={item_id=39144,num=2000,is_bind=1},
[150]={item_id=27613,num=2,is_bind=1},
[151]={item_id=27612,num=6,is_bind=1},
[152]={item_id=48088,num=1,is_bind=1},
[153]={item_id=48071,num=3,is_bind=1},
[154]={item_id=39144,num=3000,is_bind=1},
[155]={item_id=27613,num=3,is_bind=1},
[156]={item_id=27612,num=9,is_bind=1},
[157]={item_id=48088,num=2,is_bind=1},
[158]={item_id=39144,num=4000,is_bind=1},
[159]={item_id=27613,num=4,is_bind=1},
[160]={item_id=27612,num=12,is_bind=1},
[161]={item_id=26191,num=2,is_bind=1},
[162]={item_id=39144,num=5000,is_bind=1},
[163]={item_id=27613,num=5,is_bind=1},
[164]={item_id=27612,num=15,is_bind=1},
[165]={item_id=38928,num=1,is_bind=1},
[166]={item_id=39144,num=10000,is_bind=1},
[167]={item_id=27613,num=7,is_bind=1},
[168]={item_id=27612,num=21,is_bind=1},
[169]={item_id=27805,num=1,is_bind=1},
[170]={item_id=26193,num=2,is_bind=1},
[171]={item_id=39144,num=20000,is_bind=1},
[172]={item_id=27613,num=10,is_bind=1},
[173]={item_id=27612,num=30,is_bind=1},
[174]={item_id=46393,num=1,is_bind=1},
[175]={item_id=26194,num=1,is_bind=1},
[176]={item_id=39144,num=30000,is_bind=1},
[177]={item_id=27613,num=15,is_bind=1},
[178]={item_id=27612,num=45,is_bind=1},
[179]={item_id=38119,num=1,is_bind=1},
[180]={item_id=26194,num=2,is_bind=1},
[181]={item_id=39144,num=50000,is_bind=1},
[182]={item_id=27613,num=20,is_bind=1},
[183]={item_id=27612,num=60,is_bind=1},
[184]={item_id=91418,num=3,is_bind=1},
[185]={item_id=39142,num=10000,is_bind=1},
[186]={item_id=36366,num=3,is_bind=1},
[187]={item_id=46041,num=2,is_bind=1},
[188]={item_id=46039,num=2,is_bind=1},
[189]={item_id=39144,num=15000,is_bind=1},
[190]={item_id=27612,num=4,is_bind=1},
[191]={item_id=27611,num=20,is_bind=1},
[192]={item_id=28033,num=4,is_bind=1},
[193]={item_id=27611,num=30,is_bind=1},
[194]={item_id=28033,num=6,is_bind=1},
[195]={item_id=37919,num=1,is_bind=1},
[196]={item_id=27611,num=50,is_bind=1},
[197]={item_id=28033,num=10,is_bind=1},
[198]={item_id=39144,num=1000,is_bind=1},
[199]={item_id=27612,num=5,is_bind=1},
[200]={item_id=27611,num=10,is_bind=1},
[201]={item_id=27838,num=3,is_bind=1},
[202]={item_id=39144,num=8000,is_bind=1},
[203]={item_id=27613,num=8,is_bind=1},
[204]={item_id=65535,num=1,is_bind=1},
[205]={item_id=65533,num=1,is_bind=1},
[206]={item_id=46039,num=1,is_bind=1},
[207]={item_id=26409,num=2,is_bind=1},
[208]={item_id=22615,num=3,is_bind=1},
[209]={item_id=22753,num=1,is_bind=1},
[210]={item_id=28826,num=1,is_bind=1},
[211]={item_id=26409,num=4,is_bind=1},
[212]={item_id=26372,num=1,is_bind=1},
[213]={item_id=39468,num=1,is_bind=1},
[214]={item_id=26410,num=4,is_bind=1},
[215]={item_id=48144,num=1,is_bind=1},
[216]={item_id=26409,num=6,is_bind=1},
[217]={item_id=26354,num=3,is_bind=1},
[218]={item_id=39469,num=1,is_bind=1},
[219]={item_id=48146,num=1,is_bind=1},
[220]={item_id=26410,num=6,is_bind=1},
[221]={item_id=48147,num=1,is_bind=1},
[222]={item_id=26505,num=1,is_bind=1},
[223]={item_id=48072,num=1,is_bind=1},
[224]={item_id=26409,num=8,is_bind=1},
[225]={item_id=26372,num=5,is_bind=1},
[226]={item_id=39470,num=1,is_bind=1},
[227]={item_id=48106,num=1,is_bind=1},
[228]={item_id=26410,num=8,is_bind=1},
[229]={item_id=48107,num=1,is_bind=1},
[230]={item_id=26409,num=10,is_bind=1},
[231]={item_id=26415,num=10,is_bind=1},
[232]={item_id=26354,num=7,is_bind=1},
[233]={item_id=39471,num=1,is_bind=1},
[234]={item_id=48145,num=1,is_bind=1},
[235]={item_id=26409,num=12,is_bind=1},
[236]={item_id=26415,num=20,is_bind=1},
[237]={item_id=26372,num=9,is_bind=1},
[238]={item_id=39472,num=1,is_bind=1},
[239]={item_id=26410,num=12,is_bind=1},
[240]={item_id=48149,num=1,is_bind=1},
[241]={item_id=48118,num=1,is_bind=1},
[242]={item_id=26409,num=14,is_bind=1},
[243]={item_id=26415,num=30,is_bind=1},
[244]={item_id=26354,num=11,is_bind=1},
[245]={item_id=39473,num=1,is_bind=1},
[246]={item_id=26410,num=14,is_bind=1},
[247]={item_id=48096,num=1,is_bind=1},
[248]={item_id=26409,num=16,is_bind=1},
[249]={item_id=26372,num=13,is_bind=1},
[250]={item_id=39474,num=1,is_bind=1},
[251]={item_id=26410,num=16,is_bind=1},
[252]={item_id=48185,num=1,is_bind=1},
[253]={item_id=26409,num=18,is_bind=1},
[254]={item_id=48417,num=1,is_bind=1},
[255]={item_id=26354,num=15,is_bind=1},
[256]={item_id=39464,num=1,is_bind=1},
[257]={item_id=26410,num=18,is_bind=1},
[258]={item_id=48117,num=1,is_bind=1},
[259]={item_id=48438,num=1,is_bind=1},
[260]={item_id=26372,num=17,is_bind=1},
[261]={item_id=39466,num=1,is_bind=1},
[262]={item_id=26410,num=20,is_bind=1},
[263]={item_id=48120,num=1,is_bind=1},
[264]={item_id=48454,num=1,is_bind=1},
[265]={item_id=48438,num=2,is_bind=1},
[266]={item_id=48418,num=1,is_bind=1},
[267]={item_id=26409,num=22,is_bind=1},
[268]={item_id=28745,num=1,is_bind=1},
[269]={item_id=26354,num=19,is_bind=1},
[270]={item_id=39475,num=1,is_bind=1},
[271]={item_id=26410,num=22,is_bind=1},
[272]={item_id=39476,num=1,is_bind=1},
[273]={item_id=48189,num=2,is_bind=1},
[274]={item_id=48450,num=1,is_bind=1},
[275]={item_id=48439,num=2,is_bind=1},
[276]={item_id=48419,num=1,is_bind=1},
[277]={item_id=48117,num=2,is_bind=1},
[278]={item_id=26409,num=24,is_bind=1},
[279]={item_id=26372,num=21,is_bind=1},
[280]={item_id=48117,num=5,is_bind=1},
[281]={item_id=26410,num=24,is_bind=1},
[282]={item_id=48120,num=5,is_bind=1},
[283]={item_id=39477,num=1,is_bind=1},
[284]={item_id=48442,num=3,is_bind=1},
[285]={item_id=48444,num=1,is_bind=1},
[286]={item_id=48451,num=1,is_bind=1},
[287]={item_id=48439,num=4,is_bind=1},
[288]={item_id=48120,num=2,is_bind=1},
[289]={item_id=26409,num=26,is_bind=1},
[290]={item_id=28745,num=2,is_bind=1},
[291]={item_id=26354,num=23,is_bind=1},
[292]={item_id=26410,num=26,is_bind=1},
[293]={item_id=39489,num=1,is_bind=1},
[294]={item_id=48442,num=5,is_bind=1},
[295]={item_id=48445,num=1,is_bind=1},
[296]={item_id=48452,num=1,is_bind=1},
[297]={item_id=48439,num=6,is_bind=1},
[298]={item_id=55647,num=1,is_bind=1},
[299]={item_id=55655,num=1,is_bind=1},
[300]={item_id=55678,num=1,is_bind=1},
[301]={item_id=55686,num=1,is_bind=1},
[302]={item_id=55694,num=1,is_bind=1},
[303]={item_id=55702,num=1,is_bind=1},
[304]={item_id=55677,num=1,is_bind=1},
[305]={item_id=55685,num=1,is_bind=1},
[306]={item_id=55692,num=1,is_bind=1},
[307]={item_id=55700,num=1,is_bind=1},
[308]={item_id=55711,num=1,is_bind=1},
[309]={item_id=55719,num=1,is_bind=1},
[310]={item_id=55727,num=1,is_bind=1},
[311]={item_id=55735,num=1,is_bind=1},
[312]={item_id=55743,num=1,is_bind=1},
[313]={item_id=55751,num=1,is_bind=1},
[314]={item_id=55759,num=1,is_bind=1},
[315]={item_id=55767,num=1,is_bind=1},
[316]={item_id=55742,num=1,is_bind=1},
[317]={item_id=55750,num=1,is_bind=1},
[318]={item_id=55758,num=1,is_bind=1},
[319]={item_id=55766,num=1,is_bind=1},
[320]={item_id=55741,num=1,is_bind=1},
[321]={item_id=55749,num=1,is_bind=1},
[322]={item_id=55756,num=1,is_bind=1},
[323]={item_id=55764,num=1,is_bind=1},
[324]={item_id=55775,num=1,is_bind=1},
[325]={item_id=55783,num=1,is_bind=1},
[326]={item_id=55791,num=1,is_bind=1},
[327]={item_id=55799,num=1,is_bind=1},
[328]={item_id=55807,num=1,is_bind=1},
[329]={item_id=55815,num=1,is_bind=1},
[330]={item_id=55823,num=1,is_bind=1},
[331]={item_id=55831,num=1,is_bind=1},
[332]={item_id=55806,num=1,is_bind=1},
[333]={item_id=55814,num=1,is_bind=1},
[334]={item_id=55822,num=1,is_bind=1},
[335]={item_id=55830,num=1,is_bind=1},
[336]={item_id=55805,num=1,is_bind=1},
[337]={item_id=55813,num=1,is_bind=1},
[338]={item_id=55820,num=1,is_bind=1},
[339]={item_id=55828,num=1,is_bind=1},
[340]={item_id=55839,num=1,is_bind=1},
[341]={item_id=55847,num=1,is_bind=1},
[342]={item_id=55855,num=1,is_bind=1},
[343]={item_id=55863,num=1,is_bind=1},
[344]={item_id=55871,num=1,is_bind=1},
[345]={item_id=55879,num=1,is_bind=1},
[346]={item_id=55887,num=1,is_bind=1},
[347]={item_id=55895,num=1,is_bind=1},
[348]={item_id=55870,num=1,is_bind=1},
[349]={item_id=55878,num=1,is_bind=1},
[350]={item_id=55886,num=1,is_bind=1},
[351]={item_id=55894,num=1,is_bind=1},
[352]={item_id=55869,num=1,is_bind=1},
[353]={item_id=55877,num=1,is_bind=1},
[354]={item_id=55884,num=1,is_bind=1},
[355]={item_id=55892,num=1,is_bind=1},
[356]={item_id=55903,num=1,is_bind=1},
[357]={item_id=55911,num=1,is_bind=1},
[358]={item_id=55919,num=1,is_bind=1},
[359]={item_id=55927,num=1,is_bind=1},
[360]={item_id=55935,num=1,is_bind=1},
[361]={item_id=55943,num=1,is_bind=1},
[362]={item_id=55951,num=1,is_bind=1},
[363]={item_id=55959,num=1,is_bind=1},
[364]={item_id=55934,num=1,is_bind=1},
[365]={item_id=55942,num=1,is_bind=1},
[366]={item_id=55950,num=1,is_bind=1},
[367]={item_id=55958,num=1,is_bind=1},
[368]={item_id=55933,num=1,is_bind=1},
[369]={item_id=55941,num=1,is_bind=1},
[370]={item_id=55948,num=1,is_bind=1},
[371]={item_id=55956,num=1,is_bind=1},
[372]={item_id=55967,num=1,is_bind=1},
[373]={item_id=55975,num=1,is_bind=1},
[374]={item_id=55983,num=1,is_bind=1},
[375]={item_id=55991,num=1,is_bind=1},
[376]={item_id=55999,num=1,is_bind=1},
[377]={item_id=56007,num=1,is_bind=1},
[378]={item_id=56015,num=1,is_bind=1},
[379]={item_id=56023,num=1,is_bind=1},
[380]={item_id=55998,num=1,is_bind=1},
[381]={item_id=56006,num=1,is_bind=1},
[382]={item_id=56014,num=1,is_bind=1},
[383]={item_id=56022,num=1,is_bind=1},
[384]={item_id=55997,num=1,is_bind=1},
[385]={item_id=56005,num=1,is_bind=1},
[386]={item_id=56012,num=1,is_bind=1},
[387]={item_id=56020,num=1,is_bind=1},
[388]={item_id=56031,num=1,is_bind=1},
[389]={item_id=56039,num=1,is_bind=1},
[390]={item_id=56047,num=1,is_bind=1},
[391]={item_id=56055,num=1,is_bind=1},
[392]={item_id=56063,num=1,is_bind=1},
[393]={item_id=56071,num=1,is_bind=1},
[394]={item_id=56079,num=1,is_bind=1},
[395]={item_id=56087,num=1,is_bind=1},
[396]={item_id=56062,num=1,is_bind=1},
[397]={item_id=56070,num=1,is_bind=1},
[398]={item_id=56078,num=1,is_bind=1},
[399]={item_id=56086,num=1,is_bind=1},
[400]={item_id=56061,num=1,is_bind=1},
[401]={item_id=56069,num=1,is_bind=1},
[402]={item_id=56076,num=1,is_bind=1},
[403]={item_id=56084,num=1,is_bind=1},
[404]={item_id=56095,num=1,is_bind=1},
[405]={item_id=56103,num=1,is_bind=1},
[406]={item_id=56111,num=1,is_bind=1},
[407]={item_id=56119,num=1,is_bind=1},
[408]={item_id=56127,num=1,is_bind=1},
[409]={item_id=56135,num=1,is_bind=1},
[410]={item_id=56143,num=1,is_bind=1},
[411]={item_id=56151,num=1,is_bind=1},
[412]={item_id=56126,num=1,is_bind=1},
[413]={item_id=56134,num=1,is_bind=1},
[414]={item_id=56142,num=1,is_bind=1},
[415]={item_id=56150,num=1,is_bind=1},
[416]={item_id=56125,num=1,is_bind=1},
[417]={item_id=56133,num=1,is_bind=1},
[418]={item_id=56140,num=1,is_bind=1},
[419]={item_id=56148,num=1,is_bind=1},
[420]={item_id=56159,num=1,is_bind=1},
[421]={item_id=56167,num=1,is_bind=1},
[422]={item_id=56175,num=1,is_bind=1},
[423]={item_id=56183,num=1,is_bind=1},
[424]={item_id=56191,num=1,is_bind=1},
[425]={item_id=56199,num=1,is_bind=1},
[426]={item_id=56207,num=1,is_bind=1},
[427]={item_id=56215,num=1,is_bind=1},
[428]={item_id=56190,num=1,is_bind=1},
[429]={item_id=56198,num=1,is_bind=1},
[430]={item_id=56206,num=1,is_bind=1},
[431]={item_id=56214,num=1,is_bind=1},
[432]={item_id=56189,num=1,is_bind=1},
[433]={item_id=56197,num=1,is_bind=1},
[434]={item_id=56204,num=1,is_bind=1},
[435]={item_id=56212,num=1,is_bind=1},
[436]={item_id=56223,num=1,is_bind=1},
[437]={item_id=56231,num=1,is_bind=1},
[438]={item_id=56239,num=1,is_bind=1},
[439]={item_id=56247,num=1,is_bind=1},
[440]={item_id=56255,num=1,is_bind=1},
[441]={item_id=56263,num=1,is_bind=1},
[442]={item_id=56271,num=1,is_bind=1},
[443]={item_id=56279,num=1,is_bind=1},
[444]={item_id=56254,num=1,is_bind=1},
[445]={item_id=56262,num=1,is_bind=1},
[446]={item_id=56270,num=1,is_bind=1},
[447]={item_id=56278,num=1,is_bind=1},
[448]={item_id=56253,num=1,is_bind=1},
[449]={item_id=56261,num=1,is_bind=1},
[450]={item_id=56268,num=1,is_bind=1},
[451]={item_id=56276,num=1,is_bind=1},
[452]={item_id=26464,num=1,is_bind=1},
[453]={item_id=26462,num=1,is_bind=1},
[454]={item_id=26463,num=1,is_bind=1},
[455]={item_id=26459,num=1,is_bind=1},
[456]={item_id=26460,num=1,is_bind=1},
[457]={item_id=26455,num=1,is_bind=1},
[458]={item_id=26450,num=1,is_bind=1},
[459]={item_id=26444,num=1,is_bind=1},
[460]={item_id=26461,num=1,is_bind=1},
[461]={item_id=29188,num=1,is_bind=1},
[462]={item_id=29196,num=1,is_bind=1},
[463]={item_id=22872,num=1,is_bind=1},
[464]={item_id=38652,num=1,is_bind=1},
[465]={item_id=26000,num=1,is_bind=1},
[466]={item_id=26000,num=5,is_bind=1},
[467]={item_id=22007,num=1,is_bind=1},
[468]={item_id=28788,num=1,is_bind=1},
[469]={item_id=22638,num=1,is_bind=1},
[470]={item_id=26149,num=10,is_bind=1},
[471]={item_id=26148,num=5,is_bind=1},
[472]={item_id=26120,num=5,is_bind=1},
[473]={item_id=26504,num=1,is_bind=1},
[474]={item_id=26149,num=150,is_bind=1},
[475]={item_id=26344,num=5,is_bind=1},
[476]={item_id=22011,num=1,is_bind=1},
[477]={item_id=22611,num=1,is_bind=1},
[478]={item_id=37220,num=1,is_bind=1},
[479]={item_id=38411,num=1,is_bind=1},
[480]={item_id=37725,num=1,is_bind=1},
[481]={item_id=37626,num=1,is_bind=1},
[482]={item_id=46513,num=30,is_bind=1},
[483]={item_id=31056,num=10,is_bind=1},
[484]={item_id=37807,num=1,is_bind=1},
[485]={item_id=31056,num=100,is_bind=1},
[486]={item_id=28033,num=1,is_bind=1},
[487]={item_id=48071,num=1,is_bind=1},
[488]={item_id=27612,num=3,is_bind=1},
[489]={item_id=91418,num=2,is_bind=1},
[490]={item_id=36366,num=2,is_bind=1},
[491]={item_id=28033,num=2,is_bind=1},
[492]={item_id=38927,num=1,is_bind=1},
[493]={item_id=27838,num=5,is_bind=1},
[494]={item_id=48090,num=3,is_bind=1},
[495]={item_id=46398,num=1,is_bind=1},
[496]={item_id=46390,num=1,is_bind=1},
[497]={item_id=37027,num=1,is_bind=1},
[498]={item_id=26410,num=2,is_bind=1},
[499]={item_id=55663,num=1,is_bind=1},
[500]={item_id=55671,num=1,is_bind=1},
[501]={item_id=55679,num=1,is_bind=1},
[502]={item_id=55687,num=1,is_bind=1},
[503]={item_id=55695,num=1,is_bind=1},
[504]={item_id=55703,num=1,is_bind=1},
}

return {
other={
{}
},

other_meta_table_map={
},
fierce_fighting={
{}
},

fierce_fighting_meta_table_map={
},
active_turntable_reward={
{is_last=1,},
{reward_seq=1,},
{reward_seq=2,},
{reward_seq=3,},
{reward_seq=4,},
{reward_seq=5,},
{reward_seq=6,},
{reward_seq=7,reward_item=item_table[1],is_gold=1,gold_num=200,},
{activity_day=2,},
{activity_day=2,},
{reward_seq=2,},
{reward_seq=3,},
{reward_seq=4,},
{reward_seq=5,},
{reward_seq=6,},
{reward_seq=7,},
{activity_day=3,},
{activity_day=3,},
{activity_day=3,},
{reward_seq=3,},
{reward_seq=4,},
{reward_seq=5,},
{reward_seq=6,},
{activity_day=3,},
{activity_day=4,},
{activity_day=4,},
{reward_seq=2,},
{reward_seq=3,},
{reward_seq=4,},
{activity_day=4,},
{reward_seq=6,},
{activity_day=4,reward_seq=7,},
{activity_day=5,},
{reward_seq=1,},
{reward_seq=2,},
{reward_seq=3,},
{reward_seq=4,},
{reward_seq=5,},
{activity_day=5,},
{activity_day=5,},
{activity_day=6,},
{activity_day=6,},
{reward_seq=2,},
{activity_day=6,},
{reward_seq=4,},
{reward_seq=5,},
{reward_seq=6,},
{activity_day=6,},
{activity_day=7,},
{activity_day=7,},
{reward_seq=2,},
{reward_seq=3,},
{reward_seq=4,},
{reward_seq=5,},
{activity_day=7,},
{activity_day=7,}
},

active_turntable_reward_meta_table_map={
[41]=1,	-- depth:1
[39]=7,	-- depth:1
[38]=39,	-- depth:2
[37]=38,	-- depth:3
[34]=37,	-- depth:4
[35]=34,	-- depth:5
[42]=34,	-- depth:5
[33]=41,	-- depth:2
[36]=35,	-- depth:6
[43]=42,	-- depth:6
[47]=43,	-- depth:7
[45]=47,	-- depth:8
[46]=45,	-- depth:9
[48]=32,	-- depth:1
[49]=33,	-- depth:3
[50]=42,	-- depth:6
[51]=50,	-- depth:7
[52]=51,	-- depth:8
[53]=52,	-- depth:9
[54]=53,	-- depth:10
[44]=52,	-- depth:9
[31]=32,	-- depth:1
[28]=31,	-- depth:2
[29]=28,	-- depth:3
[9]=49,	-- depth:4
[10]=50,	-- depth:7
[11]=10,	-- depth:8
[12]=11,	-- depth:9
[13]=12,	-- depth:10
[14]=13,	-- depth:11
[15]=14,	-- depth:12
[16]=15,	-- depth:13
[17]=9,	-- depth:5
[30]=14,	-- depth:12
[18]=10,	-- depth:8
[20]=18,	-- depth:9
[21]=20,	-- depth:10
[22]=21,	-- depth:11
[23]=22,	-- depth:12
[25]=17,	-- depth:6
[26]=18,	-- depth:9
[27]=26,	-- depth:10
[55]=23,	-- depth:13
[19]=27,	-- depth:11
[40]=8,	-- depth:1
[24]=40,	-- depth:2
[56]=24,	-- depth:3
},
active_turntable_task={
{},
{task_id=1,task_name="日常活跃度达20",},
{task_id=2,task_name="在线10分钟",},
{task_id=3,task_name="日常活跃度达50",},
{task_id=4,task_name="在线30分钟",},
{task_id=5,task_name="日常活跃度达100",},
{task_id=6,task_name="在线60分钟",},
{task_id=7,task_name="日常活跃度达200",}
},

active_turntable_task_meta_table_map={
},
worth_buy={
{buy_item=item_table[2],buy_cost=176,origin_cost=740,pre_num=0,item_name="灵剑提升礼包",},
{seq=1,buy_item=item_table[3],origin_cost=850,rank_seq=36,item_name="灵剑养成礼包",},
{seq=2,buy_item=item_table[4],buy_cost=576,pre_seq=1,rank_seq=35,item_name="灵剑飙升礼包",},
{seq=3,buy_item=item_table[5],buy_cost=176,origin_cost=650,pre_num=0,rank_seq=34,item_name="神灵养成礼包",},
{seq=4,buy_item=item_table[6],origin_cost=880,pre_seq=3,rank_seq=33,item_name="神灵提升礼包",},
{seq=5,buy_item=item_table[7],buy_cost=316,origin_cost=1200,pre_seq=4,rank_seq=32,item_name="神灵飙升礼包",},
{seq=6,buy_item=item_table[8],buy_cost=176,origin_cost=740,pre_num=0,role_level=30,rank_seq=31,item_name="灵骑提升礼包",},
{seq=7,buy_item=item_table[9],buy_cost=196,origin_cost=800,pre_seq=6,role_level=30,rank_seq=30,item_name="灵骑养成礼包",},
{seq=8,buy_item=item_table[10],buy_cost=576,pre_seq=7,role_level=30,rank_seq=29,item_name="灵骑飙升礼包",},
{seq=9,buy_cost=176,origin_cost=740,pre_num=0,role_level=35,rank_seq=28,},
{seq=10,buy_item=item_table[11],buy_cost=196,origin_cost=880,pre_seq=9,role_level=35,rank_seq=27,item_name="背饰养成礼包",},
{seq=11,pre_seq=10,role_level=35,rank_seq=26,},
{seq=12,buy_item=item_table[12],origin_cost=860,pre_num=0,role_level=50,rank_seq=25,item_name="灵宝提升礼包",},
{seq=13,buy_item=item_table[13],buy_cost=196,origin_cost=880,pre_seq=12,role_level=50,rank_seq=24,item_name="灵宝养成礼包",},
{seq=14,buy_item=item_table[14],buy_cost=576,pre_seq=13,role_level=50,rank_seq=23,item_name="灵宝飙升礼包",},
{seq=15,buy_item=item_table[15],buy_cost=136,origin_cost=360,pre_num=0,role_level=120,rank_seq=22,item_name="玉魄大礼包",},
{seq=16,buy_item=item_table[16],origin_cost=1080,pre_seq=15,role_level=120,rank_seq=21,item_name="玉魄飙升礼包",},
{seq=17,buy_item=item_table[17],origin_cost=1000,pre_num=0,role_level=350,rank_seq=20,item_name="玉魄附魂包",},
{seq=18,buy_item=item_table[18],buy_cost=176,origin_cost=740,pre_num=0,role_level=150,rank_seq=19,item_name="天武提升礼包",},
{seq=19,buy_item=item_table[19],origin_cost=850,pre_seq=18,role_level=150,rank_seq=18,item_name="天武养成礼包",},
{seq=20,buy_item=item_table[20],buy_cost=576,pre_seq=19,role_level=150,rank_seq=17,item_name="天武飙升礼包",},
{seq=21,role_level=200,rank_seq=16,},
{seq=22,buy_item=item_table[11],origin_cost=850,pre_seq=21,role_level=200,rank_seq=15,item_name="背饰养成礼包",},
{seq=23,buy_item=item_table[21],buy_cost=576,pre_seq=22,role_level=200,rank_seq=14,item_name="背饰飙升礼包",},
{seq=24,buy_item=item_table[22],origin_cost=900,pre_num=0,role_level=300,rank_seq=13,item_name="神兽飙升礼包",},
{seq=25,buy_item=item_table[23],buy_cost=576,pre_seq=24,role_level=300,rank_seq=12,item_name="神兽飙升礼包",},
{seq=26,buy_item=item_table[24],origin_cost=1050,pre_num=0,role_level=320,rank_seq=11,item_name="天魂法器强化礼包",},
{seq=27,buy_item=item_table[25],buy_cost=396,origin_cost=1550,pre_seq=26,role_level=320,rank_seq=10,item_name="天魂法器飙升礼包",},
{seq=28,buy_item=item_table[26],money_type=2,buy_cost=60,origin_cost=300,role_level=80,rank_seq=7,item_name="强化水晶",},
{seq=29,buy_item=item_table[27],buy_cost=36,origin_cost=100,discount=2,role_level=140,rank_seq=6,item_name="五百万铜钱",}
},

worth_buy_meta_table_map={
[22]=10,	-- depth:1
[12]=24,	-- depth:1
[29]=16,	-- depth:1
[30]=16,	-- depth:1
},
loving_city_task={
{task_type=13,task_name="我结婚啦",},
{task_id=1,task_type=6,param_1=0,task_name="我有连理枝",task_desc="解锁连理枝",open_panel="marry#marry_hunjie",},
{task_id=2,param_1=26122,param_2=1,task_name="赠人倒仙花",task_desc="赠送别人%s/1次倒仙花",open_panel="Flower",},
{task_id=3,task_type=2,task_name="参加婚宴",task_desc="参加%s/1次婚宴",},
{task_id=4,param_1=3,task_desc="体验%s/3档结婚",task_process=3,},
{task_id=5,task_type=7,param_1=2,task_desc="连理枝达到%s/2阶",task_process=2,open_panel="marry#marry_hunjie",},
{task_id=6,param_1=26123,task_name="赠人凤眼花",task_desc="赠送别人%s/1次凤眼花",},
{task_id=7,param_1=5,task_desc="参加%s/5次婚宴",task_process=5,},
{task_id=8,param_1=3,task_desc="连理枝达到%s/3阶",task_process=3,},
{task_id=9,param_1=4,task_desc="连理枝达到%s/4阶",task_process=4,},
{task_id=10,param_2=3,task_desc="赠送别人%s/3次倒仙花",task_process=3,},
{task_id=11,param_1=26123,task_name="赠人凤眼花",task_desc="赠送别人%s/3次凤眼花",}
},

loving_city_task_meta_table_map={
[5]=1,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
[9]=6,	-- depth:1
[10]=6,	-- depth:1
[11]=3,	-- depth:1
[12]=11,	-- depth:2
},
loving_city_process_reward={
{},
{seq=2,need_process=50,reward_item={[0]=item_table[28],[1]=item_table[29],[2]=item_table[30]},dec="进度二",},
{seq=3,need_process=70,reward_item={[0]=item_table[31],[1]=item_table[32],[2]=item_table[33]},dec="进度三",},
{seq=4,need_process=100,reward_item={[0]=item_table[34],[1]=item_table[35],[2]=item_table[36]},dec="进度四",},
{seq=5,need_process=120,reward_item={[0]=item_table[37],[1]=item_table[38],[2]=item_table[39]},dec="进度五",}
},

loving_city_process_reward_meta_table_map={
},
profess_for_love_rank_reward={
{rank_val_limit=2000,rank_val_min_client=2000,},
{seq=1,rank_max=10,rank_val_limit=1500,reward_item={[0]=item_table[40],[1]=item_table[41],[2]=item_table[42],[3]=item_table[43]},rank_max_client=4,rank_min_client=10,rank_val_min_client=1500,},
{seq=2,rank_max=30,reward_item={[0]=item_table[44],[1]=item_table[45],[2]=item_table[46]},rank_max_client=21,rank_min_client=30,},
{seq=3,rank_max=50,reward_item={[0]=item_table[47],[1]=item_table[45],[2]=item_table[48]},rank_max_client=30,rank_min_client=50,}
},

profess_for_love_rank_reward_meta_table_map={
},
profess_for_love_score_reward={
{},
{id=1,score_value=1040,reward_item={[0]=item_table[47],[1]=item_table[45],[2]=item_table[29]},},
{id=2,score_value=1990,reward_item={[0]=item_table[47],[1]=item_table[45],[2]=item_table[35]},},
{id=3,score_value=3990,reward_item={[0]=item_table[47],[1]=item_table[45],[2]=item_table[49]},}
},

profess_for_love_score_reward_meta_table_map={
},
profess_for_love_end={
{}
},

profess_for_love_end_meta_table_map={
},
sweet_talk_fake_notice={
{profess_type=1,professor="紫萱",be_professor="长卿",content="只缘感君一回顾，使我思君朝与暮。",},
{profess_type=1,professor="青云掌门",be_professor="酒剑仙",content="醉不成欢惨将别，别时茫茫江浸月。",},
{content="我希望你活着，可以对我哭，对我笑，对我生气。我只有这样一个愿望而已。",},
{profess_type=1,content="在这九重天上，你是我的唯一。",},
{profess_type=1,content="这世间最难放下的是执念，有人执着于正邪，有人执着于爱恨。",},
{profess_type=1,professor="公仪斐",be_professor="卿酒酒",content="在下，柸中公仪斐，敢问姑娘芳名？",},
{profess_type=1,content="永安，卿酒酒。",},
{profess_type=1,content="红颜为谁舞一曲，谁为红颜倾天下。",},
{profess_type=1,content="你喜欢田野，而我愚笨​​，只能植荒十年，换得一时春生。",},
{profess_type=1,content="余生还长，请多多指教",},
{profess_type=1,content="尘缘从来都如水，罕须泪，何尽一生情？莫多情，情伤己。",},
{profess_type=1,content="我还是很喜欢你，像风走了八千里，不问归期",},
{content="剑未佩妥，出门已是江湖，酒尚余温，入口不识乾坤，远离尽千帆，归来仍是少年",},
{content="我为你翻山越岭，却无心看风景",},
{content="我将于茫茫人海中，我唯一灵魂之伴侣，得之，我幸；失之，我命",},
{content="原是今生今世已惘然，山河岁月空惆怅， 而我终将是要等着你的",},
{content="乱世繁华,只为你倾尽天下;苍水蒹葭,只为你归田卸甲。",},
{profess_type=1,content="玲珑骰子安红豆,入骨相思知不知?",},
{content="还钱！还钱！还钱！",},
{profess_type=2,content="花一世界,一叶一追寻,一曲一场叹,一生为一人。",},
{profess_type=2,content="自向来缘浅,奈何情深。",},
{profess_type=2,content="自愿我如星君如月,夜夜流光相皎洁。",},
{profess_type=2,content="我愿执笔弃花间,从此以后,离经易道,只为你。",},
{profess_type=2,content="任人世间三千轮回，我的心只为你而跳动。",},
{profess_type=2,content="阳光温热，岁月静好，你还未来，我怎敢老去。",},
{profess_type=2,content="错过了青梅竹马，避开了情窦初开，到如今人生的意义，就只剩下和你两鬓斑白。",},
{profess_type=2,content="这万里江山歌舞亭台 怎么敌你眉间朱砂，袖中风华。",},
{profess_type=2,content="即便是山雨欲来风满楼也要撑伞立于雨中等那暮归之人。",},
{profess_type=2,professor="雨馨",be_professor="辰南",content="可爱不是长久之计，可爱你是长久之计。",},
{profess_type=2,professor="姜泥",be_professor="凤年",content="众生皆无味，唯你是青山。",},
{profess_type=2,professor="雨师妾",be_professor="拓拔野",content="青浅流年，唯爱相依，唯你相依。",},
{profess_type=2,professor="柳莺莺",be_professor="梁萧",content="黄泉路上，忘川河中，三生石旁，奈何桥上，我可能喜欢着你。",},
{profess_type=2,professor="梨衣",be_professor="路明非",content="在这样的季节里我喜欢了你。",},
{profess_type=2,professor="碧瑶",be_professor="张小凡",content="一朵花开，一梦今生。一朵花谢，一念随风。云在天上，你在我的心上。",},
{profess_type=2,professor="张起灵",be_professor="吴邪",content="一笔一划诉春秋，一撇一捺绣温柔，一动一静情无限，一生一世牵你手。",},
{profess_type=2,professor="桑桑",be_professor="宁缺",content="青浅流年，唯爱相依，唯你相依。褪尽风华，我依然在红尘深处守望你。",},
{profess_type=2,professor="小舞",be_professor="唐三",content="若你能许我一个未来。我定会为你。长袖翩翩，舞尽锦瑟年华。",},
{professor="辰南",be_professor="雨馨",},
{professor="凤年",be_professor="姜泥",},
{professor="拓拔野",be_professor="雨师妾",},
{professor="梁萧",be_professor="柳莺莺",},
{professor="路明非",be_professor="梨衣",},
{professor="张小凡",be_professor="碧瑶",},
{professor="吴邪",be_professor="张起灵",},
{professor="宁缺",be_professor="桑桑",},
{professor="唐三",be_professor="小舞",},
{professor="林动",be_professor="应欢欢",},
{professor="卿酒酒",be_professor="公仪斐",},
{professor="曲红颜",be_professor="古飞扬",},
{},
{professor="薄暮流云",be_professor="拂晓浅霞",},
{professor="浮生",be_professor="蒹葭",},
{professor="长卿",be_professor="紫萱",},
{},
{professor="白浅",be_professor="夜华",},
{professor="花千骨",be_professor="白子画",},
{professor="离殇",be_professor="霓裳",}
},

sweet_talk_fake_notice_meta_table_map={
[3]=12,	-- depth:1
[50]=57,	-- depth:1
[28]=47,	-- depth:1
[4]=55,	-- depth:1
[5]=56,	-- depth:1
[7]=48,	-- depth:1
[8]=49,	-- depth:1
[9]=51,	-- depth:1
[10]=52,	-- depth:1
[11]=53,	-- depth:1
[13]=4,	-- depth:2
[14]=5,	-- depth:2
[15]=6,	-- depth:1
[17]=8,	-- depth:2
[18]=50,	-- depth:2
[19]=9,	-- depth:2
[20]=38,	-- depth:1
[21]=39,	-- depth:1
[22]=41,	-- depth:1
[23]=42,	-- depth:1
[24]=43,	-- depth:1
[25]=44,	-- depth:1
[26]=45,	-- depth:1
[27]=46,	-- depth:1
[16]=7,	-- depth:2
},
tianshenxunbao_layer={
{get_exchange_value=0,},
{layer=1,name="寰宇星盘",round_continue_time_h="96|0|0",reward_pool_id="3|4|5",icon_id=46379,openday=3,},
{layer=2,name="命运星盘",round_continue_time_h="48|0|0",reward_pool_id="6|7|8",icon_id=46380,},
{layer=3,name="轮回星盘",round_count=2,round_continue_time_h="120|0",reward_pool_id="9|10",icon_id=50040,openday=4,}
},

tianshenxunbao_layer_meta_table_map={
},
tianshenxunbao_reward_pool={
{draw_consume_item_count="1|2|2|4|4|6|6|10|10|16|16|25|25|35|35|45",one_key=0,},
{reward_pool_id=1,draw_consume_item_count="1|2|4|6|8|15|20|25|30|40|50|60|70|80|90|100",},
{reward_pool_id=2,one_key=0,},
{reward_pool_id=3,draw_consume_item_count="1|2|4|6|8|15|20|25|30|40|50|60|70|80|90|100",reward_id_list="77,78,79|77,78,79,80|77,78,79,80,81|77,78,79,80,81,82|77,78,79,80,81,82,83|77,78,79,80,81,82,83,84|77,78,79,80,81,82,83,84,85|77,78,79,80,81,82,83,84,85,86|77,78,79,80,81,82,83,84,85,86,87|77,78,79,80,81,82,83,84,85,86,87,88|77,78,79,80,81,82,83,84,85,86,87,88,89|77,78,79,80,81,82,83,84,85,86,87,88,89,90|77,78,79,80,81,82,83,84,85,86,87,88,89,90,91|77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92|77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92|77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92",},
{reward_pool_id=4,draw_consume_item_count="1|2|4|8|16|20|25|30|40|50|65|80|90|100|110|120",reward_id_list="93,94,95|93,94,95,96|93,94,95,96,97|93,94,95,96,97,98|93,94,95,96,97,98,99|93,94,95,96,97,98,99,100|93,94,95,96,97,98,99,100,101|93,94,95,96,97,98,99,100,101,102|93,94,95,96,97,98,99,100,101,102,103|93,94,95,96,97,98,99,100,101,102,103,104|93,94,95,96,97,98,99,100,101,102,103,104,105|93,94,95,96,97,98,99,100,101,102,103,104,105,106|93,94,95,96,97,98,99,100,101,102,103,104,105,106,107|93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108|93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108|93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108",},
{reward_pool_id=5,draw_consume_item_id=46379,reward_id_list="109,110,111|109,110,111,112|109,110,111,112,113|109,110,111,112,113,114|109,110,111,112,113,114,115|109,110,111,112,113,114,115,116|109,110,111,112,113,114,115,116,117|109,110,111,112,113,114,115,116,117,118|109,110,111,112,113,114,115,116,117,118,119|109,110,111,112,113,114,115,116,117,118,119,120|109,110,111,112,113,114,115,116,117,118,119,120,121|109,110,111,112,113,114,115,116,117,118,119,120,121,122|109,110,111,112,113,114,115,116,117,118,119,120,121,122,123|109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124|109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124|109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124",shop_seq=11048,},
{reward_pool_id=6,draw_consume_item_id=46380,draw_consume_item_count="1|2|4|8|16|20|25|30|40|50|65|80|95|110|125|140|155|170|185|200",need_draw_times="1,1|2,2|3,3|4,4|5,5|6,6|7,7|8,8|9,9|10,10|11,11|12,12|13,13|14,14|15,15|16,16|17,17|18,18|19,19|20,20",reward_id_list="1,2,3|1,2,3,4|1,2,3,4,5|1,2,3,4,5,6|1,2,3,4,5,6,7|1,2,3,4,5,6,7,8|1,2,3,4,5,6,7,8,9|1,2,3,4,5,6,7,8,9,10|1,2,3,4,5,6,7,8,9,10,11|1,2,3,4,5,6,7,8,9,10,11,12|1,2,3,4,5,6,7,8,9,10,11,12,13|1,2,3,4,5,6,7,8,9,10,11,12,13,14|1,2,3,4,5,6,7,8,9,10,11,12,13,14,15|1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16|1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17|1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18|1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19|1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20|1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20|1,2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20",reward_weight_list="60,39,5|80,60,20,5|80,80,60,20,5|80,80,80,60,20,2|80,80,80,80,60,10,5|80,80,80,80,80,100,20,5|80,80,80,80,80,80,60,20,2|80,80,80,80,80,80,80,60,10,5|80,80,80,80,80,80,80,80,100,20,2|80,80,80,80,80,80,80,80,80,60,10,5|80,80,80,80,80,80,80,80,80,80,100,20,5|80,80,80,80,80,80,80,80,80,80,80,60,20,2|80,80,80,80,80,80,80,80,80,80,80,80,60,10,5|80,80,80,80,80,80,80,80,80,80,80,80,80,100,20,2|80,80,80,80,80,80,80,80,80,80,80,80,80,80,60,10,5|80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,20,5|80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,60,20,5|80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,60,20,5|80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,60,20|80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,60",shop_seq=11047,},
{reward_pool_id=7,reward_id_list="21,22,23|21,22,23,24|21,22,23,24,25|21,22,23,24,25,26|21,22,23,24,25,26,27|21,22,23,24,25,26,27,28|21,22,23,24,25,26,27,28,29|21,22,23,24,25,26,27,28,29,30|21,22,23,24,25,26,27,28,29,30,31|21,22,23,24,25,26,27,28,29,30,31,32|21,22,23,24,25,26,27,28,29,30,31,32,33|21,22,23,24,25,26,27,28,29,30,31,32,33,34|21,22,23,24,25,26,27,28,29,30,31,32,33,34,35|21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36|21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37|21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38|21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39|21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40|21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40|21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40",},
{reward_pool_id=8,reward_id_list="41,42,43|41,42,43,44|41,42,43,44,45|41,42,43,44,45,46|41,42,43,44,45,46,47|41,42,43,44,45,46,47,48|41,42,43,44,45,46,47,48,49|41,42,43,44,45,46,47,48,49,50|41,42,43,44,45,46,47,48,49,50,51|41,42,43,44,45,46,47,48,49,50,51,52|41,42,43,44,45,46,47,48,49,50,51,52,53|41,42,43,44,45,46,47,48,49,50,51,52,53,54|41,42,43,44,45,46,47,48,49,50,51,52,53,54,55|41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56|41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57|41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58|41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59|41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60|41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60|41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60",},
{reward_pool_id=9,draw_consume_item_count="1|2|4|8|16|20|25|30|40|50|65|80|90|100|110|120",reward_id_list="125,126,127|125,126,127,128|125,126,127,128,129|125,126,127,128,129,130|125,126,127,128,129,130,131|125,126,127,128,129,130,131,132|125,126,127,128,129,130,131,132,133|125,126,127,128,129,130,131,132,133,134|125,126,127,128,129,130,131,132,133,134,135|125,126,127,128,129,130,131,132,133,134,135,136|125,126,127,128,129,130,131,132,133,134,135,136,137|125,126,127,128,129,130,131,132,133,134,135,136,137,138|125,126,127,128,129,130,131,132,133,134,135,136,137,138,139|125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140|125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140|125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140",},
{reward_pool_id=10,draw_consume_item_id=50040,reward_id_list="141,142,143|141,142,143,144|141,142,143,144,145|141,142,143,144,145,146|141,142,143,144,145,146,147|141,142,143,144,145,146,147,148|141,142,143,144,145,146,147,148,149|141,142,143,144,145,146,147,148,149,150|141,142,143,144,145,146,147,148,149,150,151|141,142,143,144,145,146,147,148,149,150,151,152|141,142,143,144,145,146,147,148,149,150,151,152,153|141,142,143,144,145,146,147,148,149,150,151,152,153,154|141,142,143,144,145,146,147,148,149,150,151,152,153,154,155|141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,141|141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,141|141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156",shop_seq=11058,}
},

tianshenxunbao_reward_pool_meta_table_map={
[2]=3,	-- depth:1
[4]=6,	-- depth:1
[5]=6,	-- depth:1
[10]=11,	-- depth:1
[8]=7,	-- depth:1
[9]=7,	-- depth:1
},
tianshenxunbao_reward={
{reward_item=item_table[50],x_pos=326,is_l_kite=0,},
{reward_id=2,reward_item=item_table[51],},
{reward_id=3,reward_item=item_table[52],},
{reward_id=4,x_pos=538,y_pos=252,is_l_kite=0,},
{reward_id=5,reward_item=item_table[53],},
{reward_id=6,reward_item=item_table[54],},
{reward_id=7,reward_item=item_table[55],},
{reward_id=8,reward_item=item_table[56],},
{reward_id=9,reward_item=item_table[57],},
{reward_id=10,reward_item=item_table[58],},
{reward_id=11,reward_item=item_table[59],},
{reward_id=12,reward_item=item_table[60],},
{reward_id=13,reward_item=item_table[61],},
{reward_id=14,},
{reward_id=15,reward_item=item_table[59],},
{reward_id=16,reward_item=item_table[62],x_pos=83,y_pos=87,},
{reward_id=17,reward_item=item_table[63],},
{reward_id=18,reward_item=item_table[62],is_best=1,},
{reward_id=19,reward_item=item_table[64],},
{reward_id=20,reward_item=item_table[65],is_best=1,},
{reward_id=21,reward_item=item_table[50],},
{reward_id=22,reward_item=item_table[51],},
{reward_id=23,reward_item=item_table[52],},
{reward_id=24,x_pos=256,},
{reward_id=25,reward_item=item_table[53],},
{reward_id=26,reward_item=item_table[54],},
{reward_id=27,reward_item=item_table[66],},
{reward_id=28,reward_item=item_table[56],},
{reward_id=29,reward_item=item_table[67],},
{reward_id=30,},
{reward_id=31,},
{reward_id=32,reward_item=item_table[60],},
{reward_id=33,x_pos=256,},
{reward_id=34,reward_item=item_table[68],},
{reward_id=35,x_pos=538,},
{reward_id=36,reward_item=item_table[69],},
{reward_id=37,reward_item=item_table[63],x_pos=-304,},
{reward_id=38,x_pos=-375,},
{reward_id=39,reward_item=item_table[70],x_pos=81,y_pos=252,},
{reward_id=40,reward_item=item_table[65],x_pos=608,},
{reward_id=41,},
{reward_id=42,reward_item=item_table[51],},
{reward_id=43,reward_item=item_table[52],},
{reward_id=44,x_pos=-445,},
{reward_id=45,reward_item=item_table[53],},
{reward_id=46,reward_item=item_table[54],},
{reward_id=47,reward_item=item_table[66],},
{reward_id=48,reward_item=item_table[56],},
{reward_id=49,reward_item=item_table[71],},
{reward_id=50,reward_item=item_table[72],},
{reward_id=51,reward_item=item_table[59],},
{reward_id=52,},
{reward_id=53,reward_item=item_table[59],},
{reward_id=54,reward_item=item_table[68],},
{reward_id=55,},
{reward_id=56,reward_item=item_table[62],},
{reward_id=57,reward_item=item_table[63],},
{reward_id=58,reward_item=item_table[62],},
{reward_id=59,reward_item=item_table[73],},
{reward_id=60,reward_item=item_table[74],},
{reward_id=61,reward_item=item_table[75],},
{reward_id=62,reward_item=item_table[76],},
{reward_id=63,reward_item=item_table[77],},
{reward_id=64,reward_item=item_table[78],},
{reward_id=65,reward_item=item_table[79],},
{reward_id=66,reward_item=item_table[80],},
{reward_id=67,reward_item=item_table[81],},
{reward_id=68,reward_item=item_table[82],},
{reward_id=69,reward_item=item_table[80],},
{reward_id=70,reward_item=item_table[83],},
{reward_id=71,reward_item=item_table[84],},
{reward_id=72,reward_item=item_table[80],},
{reward_id=73,x_pos=-375,y_pos=30,},
{reward_id=74,reward_item=item_table[85],},
{reward_id=75,reward_item=item_table[86],},
{reward_id=76,reward_item=item_table[87],},
{reward_id=77,reward_item=item_table[88],x_pos=-234,},
{reward_id=78,reward_item=item_table[89],},
{reward_id=79,reward_item=item_table[90],},
{reward_id=80,x_pos=256,},
{reward_id=81,reward_item=item_table[91],},
{reward_id=82,x_pos=397,y_pos=252,},
{reward_id=83,reward_item=item_table[92],},
{reward_id=84,reward_item=item_table[93],},
{reward_id=85,reward_item=item_table[94],},
{reward_id=86,reward_item=item_table[95],x_pos=-93,y_pos=30,},
{reward_id=87,reward_item=item_table[93],},
{reward_id=88,},
{reward_id=89,reward_item=item_table[96],},
{reward_id=90,reward_item=item_table[97],},
{reward_id=91,reward_item=item_table[98],is_l_kite=0,is_best=1,},
{reward_id=92,reward_item=item_table[99],is_l_kite=-1,is_best=1,},
{reward_id=93,reward_item=item_table[100],},
{reward_id=94,reward_item=item_table[89],},
{reward_id=95,reward_item=item_table[90],},
{reward_id=96,reward_item=item_table[101],},
{reward_id=97,},
{reward_id=98,x_pos=397,y_pos=30,is_l_kite=0,},
{reward_id=99,reward_item=item_table[102],},
{reward_id=100,reward_item=item_table[93],},
{reward_id=101,reward_item=item_table[103],x_pos=-93,y_pos=252,},
{reward_id=102,reward_item=item_table[95],x_pos=-445,is_l_kite=0,},
{reward_id=103,x_pos=256,},
{reward_id=104,reward_item=item_table[104],x_pos=397,y_pos=30,},
{reward_id=105,reward_item=item_table[105],},
{reward_id=106,reward_item=item_table[106],},
{reward_id=107,reward_item=item_table[98],is_best=1,},
{reward_id=108,reward_item=item_table[107],is_best=1,},
{reward_id=109,reward_item=item_table[88],},
{reward_id=110,reward_item=item_table[108],},
{reward_id=111,reward_item=item_table[90],},
{reward_id=112,reward_item=item_table[101],},
{reward_id=113,reward_item=item_table[91],},
{reward_id=114,is_l_kite=0,},
{reward_id=115,reward_item=item_table[102],is_l_kite=-1,},
{reward_id=116,reward_item=item_table[93],},
{reward_id=117,reward_item=item_table[109],},
{reward_id=118,x_pos=83,y_pos=87,},
{reward_id=119,},
{reward_id=120,reward_item=item_table[104],},
{reward_id=121,reward_item=item_table[110],},
{reward_id=122,reward_item=item_table[106],},
{reward_id=123,reward_item=item_table[98],},
{reward_id=124,reward_item=item_table[111],},
{reward_id=125,reward_item=item_table[88],},
{reward_id=126,reward_item=item_table[112],},
{reward_id=127,reward_item=item_table[113],y_pos=30,},
{reward_id=128,reward_item=item_table[101],},
{reward_id=129,reward_item=item_table[91],},
{reward_id=130,x_pos=-163,},
{reward_id=131,reward_item=item_table[114],},
{reward_id=132,reward_item=item_table[115],},
{reward_id=133,reward_item=item_table[116],},
{reward_id=134,},
{reward_id=135,},
{reward_id=136,reward_item=item_table[96],},
{reward_id=137,},
{reward_id=138,reward_item=item_table[117],},
{reward_id=139,},
{reward_id=140,reward_item=item_table[118],is_best=1,},
{reward_id=141,reward_item=item_table[119],},
{reward_id=142,reward_item=item_table[89],x_pos=-234,y_pos=252,},
{reward_id=143,},
{reward_id=144,},
{reward_id=145,reward_item=item_table[120],},
{reward_id=146,},
{reward_id=147,reward_item=item_table[121],x_pos=-234,y_pos=30,},
{reward_id=148,reward_item=item_table[122],},
{reward_id=149,reward_item=item_table[123],x_pos=-375,y_pos=252,},
{reward_id=150,reward_item=item_table[115],},
{reward_id=151,reward_item=item_table[124],x_pos=397,},
{reward_id=152,},
{reward_id=153,reward_item=item_table[125],},
{reward_id=154,reward_item=item_table[126],},
{reward_id=155,reward_item=item_table[127],is_best=1,},
{reward_id=156,reward_item=item_table[128],}
},

tianshenxunbao_reward_meta_table_map={
[146]=130,	-- depth:1
[119]=116,	-- depth:1
[88]=120,	-- depth:1
[83]=130,	-- depth:1
[113]=114,	-- depth:1
[74]=114,	-- depth:1
[106]=130,	-- depth:1
[48]=130,	-- depth:1
[72]=37,	-- depth:1
[50]=114,	-- depth:1
[53]=114,	-- depth:1
[54]=114,	-- depth:1
[56]=114,	-- depth:1
[57]=114,	-- depth:1
[59]=114,	-- depth:1
[97]=113,	-- depth:2
[95]=37,	-- depth:1
[65]=44,	-- depth:1
[66]=130,	-- depth:1
[90]=114,	-- depth:1
[55]=53,	-- depth:2
[121]=114,	-- depth:1
[12]=37,	-- depth:1
[138]=114,	-- depth:1
[145]=44,	-- depth:1
[136]=37,	-- depth:1
[129]=44,	-- depth:1
[5]=44,	-- depth:1
[25]=37,	-- depth:1
[6]=130,	-- depth:1
[27]=44,	-- depth:1
[29]=130,	-- depth:1
[30]=50,	-- depth:2
[154]=114,	-- depth:1
[122]=114,	-- depth:1
[152]=136,	-- depth:2
[14]=54,	-- depth:2
[153]=73,	-- depth:1
[150]=86,	-- depth:1
[93]=86,	-- depth:1
[123]=92,	-- depth:1
[124]=92,	-- depth:1
[125]=1,	-- depth:1
[109]=86,	-- depth:1
[126]=142,	-- depth:1
[105]=82,	-- depth:1
[96]=73,	-- depth:1
[131]=147,	-- depth:1
[133]=149,	-- depth:1
[134]=150,	-- depth:2
[137]=153,	-- depth:2
[117]=86,	-- depth:1
[141]=1,	-- depth:1
[81]=104,	-- depth:1
[78]=101,	-- depth:1
[79]=102,	-- depth:1
[24]=98,	-- depth:1
[34]=149,	-- depth:1
[23]=86,	-- depth:1
[36]=1,	-- depth:1
[22]=1,	-- depth:1
[21]=142,	-- depth:1
[41]=21,	-- depth:2
[42]=147,	-- depth:1
[43]=101,	-- depth:1
[51]=86,	-- depth:1
[58]=91,	-- depth:1
[13]=73,	-- depth:1
[60]=91,	-- depth:1
[61]=1,	-- depth:1
[28]=73,	-- depth:1
[9]=149,	-- depth:1
[2]=142,	-- depth:1
[10]=86,	-- depth:1
[67]=147,	-- depth:1
[7]=147,	-- depth:1
[70]=86,	-- depth:1
[62]=142,	-- depth:1
[69]=149,	-- depth:1
[127]=4,	-- depth:1
[3]=127,	-- depth:2
[128]=4,	-- depth:1
[151]=4,	-- depth:1
[8]=24,	-- depth:2
[148]=24,	-- depth:2
[144]=128,	-- depth:2
[20]=149,	-- depth:1
[19]=127,	-- depth:2
[135]=151,	-- depth:2
[18]=147,	-- depth:1
[17]=4,	-- depth:1
[16]=115,	-- depth:1
[26]=151,	-- depth:2
[11]=151,	-- depth:2
[143]=127,	-- depth:2
[132]=24,	-- depth:2
[15]=98,	-- depth:1
[118]=102,	-- depth:1
[32]=16,	-- depth:2
[77]=98,	-- depth:1
[84]=4,	-- depth:1
[75]=98,	-- depth:1
[87]=118,	-- depth:2
[71]=151,	-- depth:2
[68]=24,	-- depth:2
[64]=4,	-- depth:1
[94]=151,	-- depth:2
[63]=127,	-- depth:2
[99]=16,	-- depth:2
[100]=77,	-- depth:2
[31]=15,	-- depth:2
[52]=32,	-- depth:3
[40]=91,	-- depth:1
[103]=84,	-- depth:2
[49]=4,	-- depth:1
[47]=151,	-- depth:2
[46]=98,	-- depth:1
[45]=103,	-- depth:3
[110]=118,	-- depth:2
[33]=15,	-- depth:2
[38]=18,	-- depth:2
[80]=128,	-- depth:2
[35]=15,	-- depth:2
[155]=98,	-- depth:1
[140]=16,	-- depth:2
[139]=155,	-- depth:2
[107]=4,	-- depth:1
[76]=140,	-- depth:3
[156]=140,	-- depth:3
},
tianshenxunbao_exchange={
{layer=1,exchange_item=item_table[129],exchange_limit=16,},
{exchange_item=item_table[130],exchange_limit=6,},
{exchange_item=item_table[131],exchange_consume=240,},
{layer=1,exchange_item=item_table[132],exchange_consume=120,exchange_limit=4,},
{exchange_item=item_table[133],exchange_limit=20,},
{layer=1,exchange_consume=30,},
{exchange_item=item_table[134],exchange_consume=35,},
{exchange_item=item_table[135],},
{layer=1,exchange_consume=15,exchange_limit=10,},
{layer=1,exchange_item=item_table[136],},
{exchange_item=item_table[78],exchange_consume=4,},
{exchange_item=item_table[137],exchange_consume=75,},
{exchange_item=item_table[138],exchange_consume=4,exchange_limit=30,},
{exchange_item=item_table[139],exchange_consume=8,exchange_limit=20,},
{exchange_item=item_table[53],exchange_consume=20,exchange_limit=10,},
{exchange_item=item_table[140],exchange_limit=5,},
{exchange_consume=12,exchange_limit=2,},
{exchange_item=item_table[56],exchange_consume=35,exchange_limit=6,},
{exchange_item=item_table[141],},
{exchange_item=item_table[142],exchange_consume=32,exchange_limit=5,},
{exchange_item=item_table[143],exchange_consume=99,exchange_limit=1,},
{exchange_item=item_table[60],},
{exchange_item=item_table[68],exchange_consume=40,},
{layer=3,exchange_item=item_table[144],exchange_limit=11,},
{layer=3,exchange_item=item_table[121],exchange_consume=5,exchange_limit=6,},
{exchange_item=item_table[62],exchange_consume=71,},
{layer=3,exchange_item=item_table[124],exchange_consume=30,exchange_limit=5,},
{layer=3,exchange_item=item_table[145],exchange_consume=65,exchange_limit=4,},
{layer=3,exchange_item=item_table[40],exchange_consume=36,},
{layer=3,exchange_item=item_table[146],exchange_consume=3,exchange_limit=45,},
{layer=3,exchange_consume=6,exchange_limit=20,}
},

tianshenxunbao_exchange_meta_table_map={
[26]=29,	-- depth:1
[23]=17,	-- depth:1
[22]=23,	-- depth:2
[19]=20,	-- depth:1
[16]=18,	-- depth:1
[2]=1,	-- depth:1
[6]=29,	-- depth:1
[7]=6,	-- depth:2
[5]=1,	-- depth:1
[11]=14,	-- depth:1
[8]=7,	-- depth:3
[10]=21,	-- depth:1
[3]=10,	-- depth:2
},
tianshenxunbao_bubble={
{bubble_content="你如果送我一朵莲花，我可以帮你把风筝射下来哦~",},
{bubble_content="你看那个大风筝，一定很吸引人！",},
{bubble_content="风筝可不会自己掉下来哦……",},
{bubble_content="每天准时参与神灵降临，可以获得莲花哦~",},
{bubble_content="你如果送我一朵银莲，我可以帮你把风筝射下来哦~",},
{layer=1,},
{bubble_content="听说有人打BOSS，拿到了银莲呢！",},
{bubble_content="你如果送我一朵金莲，我可以帮你把风筝射下来哦~",},
{layer=2,},
{bubble_content="听说有人打BOSS，拿到了金莲呢！",}
},

tianshenxunbao_bubble_meta_table_map={
[5]=6,	-- depth:1
[7]=5,	-- depth:2
[8]=9,	-- depth:1
[10]=8,	-- depth:2
},
tianshen_theme_desc={
{rule_tip="登录就能领奖哦~",},
{active_name="首充送礼",real_id=20,rank_id=2,rule_tip="最~划算的奖励！可不要错过哦~",rule_desc="1.开服<color=#6FBB6F>第8日</color>，开启活动\n2.活动期间，每日充值<color=#6FBB6F>任意</color>金额，即可领取超值好礼\n3.活动结束后，未领取的奖励将通过邮件发放\n",tab_name="首充送礼",},
{active_name="累计充值",real_id=30,rank_id=3,rule_tip="累充得稀有时装，大幅提升战力！",rule_desc="1.开服<color=#6FBB6F>第8日</color>，开启<color=#6FBB6F>累充豪礼</color>活动\n2.活动期间，累计充值灵玉数量达到<color=#6FBB6F>360灵玉</color>、<color=#6FBB6F>1280灵玉</color>、<color=#6FBB6F>3280灵玉</color>、<color=#6FBB6F>6480灵玉</color>、<color=#6FBB6F>10000灵玉</color>，即可分别领取相应豪礼\n3.活动结束后，未主动领取的奖励将通过<color=#6FBB6F>邮件</color>发放",tab_name="累计充值",},
{active_name="拼图领奖",real_id=40,rank_id=5,rule_tip="达成目标得法器，超值礼包等你买！",rule_desc="1.开服<color=#6FBB6F>第8日</color>，开启<color=#6FBB6F>我要法器</color>活动\n2.活动期间，达成<color=#6FBB6F>指定目标</color>，可分别领取相应奖励\n3.达成目标获得<color=#6FBB6F>寻宝券</color>，可前往<color=#6FBB6F>丰收夺宝</color>活动参与夺宝，更能获得<color=#6FBB6F>稀有神灵</color>\n4.达成各目标同时，还可获得相应<color=#6FBB6F>积分</color>，积分达到指定数量可获得<color=#6FBB6F>如意金箍棒</color>，提高神灵战力\n5.活动期间，使用灵玉购买礼包，可获得<color=#6FBB6F>珍稀道具</color>\n6.活动结束后，未主动领取的奖励将通过<color=#6FBB6F>邮件</color>发放",tab_name="拼图领奖",},
{active_name="诸神排行",real_id=50,rank_id=6,rule_tip="活动结束时结算排名奖励，通过邮件发放。",rule_desc="1.开服<color=#6FBB6F>第8日</color>，开启<color=#6FBB6F>神灵冲榜</color>活动\n2.活动期间，神灵总战力达到指定目标，且神灵总战力本服排名达到指定名次，即可获得<color=#6FBB6F>开天灵斧</color>等相应豪礼\n3.统计各玩家神灵总战力及相应名次，奖励通过<color=#6FBB6F>邮件</color>发放",tab_name="神灵排行",},
{active_name="试炼副本",real_id=60,rank_id=7,rule_desc="1.活动期间，每日<color=#6FBB6F>18:00</color>开启试炼副本，全体玩家可前往参与\n2.活动中对BOSS造成伤害，即可获得<color=#6FBB6F>参与奖励</color>，奖励通过<color=#6FBB6F>邮件</color>发放\n3.成功击杀BOSS，可获得掉落奖励！",tab_name="试炼副本",},
{active_name="掉落加持",real_id=70,rank_id=8,rule_tip="活动期间击杀BOSS，得铜钱，夺大奖哦！",rule_desc="活动期间，每日参与<color=#95d12b>伏魔战场</color>、<color=#95d12b>灵妖奇脉</color>、<color=#95d12b>荒天炎窟</color>、<color=#95d12b>仙遗洞天</color>，即有机会获得抽奖券，前往活动寻宝抽大礼！每日寻宝券掉落设有上限，达到上限不再掉落，每日重置！",tab_name="掉落加持",},
{active_name="限时抢购",real_id=80,rank_id=4,rule_desc="1.开服<color=#6FBB6F>第8日</color>，开启<color=#6FBB6F>限时秒杀</color>活动\n2.活动期间，秒杀花费达到指定条件，可领取<color=#6FBB6F>额外奖励</color>\n",tab_name="限时抢购",}
},

tianshen_theme_desc_meta_table_map={
},
tianshen_theme_other={
{}
},

tianshen_theme_other_meta_table_map={
},
new_login_reward={
{},
{day_index=2,},
{day_index=3,}
},

new_login_reward_meta_table_map={
},
shouchong_reward={
{},
{day_index=2,},
{day_index=3,}
},

shouchong_reward_meta_table_map={
},
leichonghaoli_reward={
{},
{ID=2,stage_value=25600,reward_item={[0]=item_table[147],[1]=item_table[148],[2]=item_table[149],[3]=item_table[150],[4]=item_table[151]},lc_reward_item={[0]=item_table[147]},},
{ID=3,stage_value=65600,reward_item={[0]=item_table[152],[1]=item_table[153],[2]=item_table[154],[3]=item_table[155],[4]=item_table[156]},lc_reward_item={[0]=item_table[152]},},
{ID=4,stage_value=129600,reward_item={[0]=item_table[157],[1]=item_table[40],[2]=item_table[158],[3]=item_table[159],[4]=item_table[160]},lc_reward_item={[0]=item_table[157]},},
{ID=5,stage_value=200000,reward_item={[0]=item_table[72],[1]=item_table[161],[2]=item_table[162],[3]=item_table[163],[4]=item_table[164]},lc_reward_item={[0]=item_table[72]},},
{ID=6,stage_value=400000,reward_item={[0]=item_table[165],[1]=item_table[63],[2]=item_table[166],[3]=item_table[167],[4]=item_table[168]},lc_reward_item={[0]=item_table[165]},},
{ID=7,stage_value=600000,reward_item={[0]=item_table[169],[1]=item_table[170],[2]=item_table[171],[3]=item_table[172],[4]=item_table[173]},lc_reward_item={[0]=item_table[169]},},
{ID=8,stage_value=800000,reward_item={[0]=item_table[174],[1]=item_table[175],[2]=item_table[176],[3]=item_table[177],[4]=item_table[178]},lc_reward_item={[0]=item_table[174]},},
{ID=9,stage_value=1000000,reward_item={[0]=item_table[179],[1]=item_table[180],[2]=item_table[181],[3]=item_table[182],[4]=item_table[183]},lc_reward_item={[0]=item_table[179]},}
},

leichonghaoli_reward_meta_table_map={
},
woyaoshenqi_task={
{task_param2=127,task_param3=1,reward_item={[0]=item_table[184]},jigsaw_num=3,panel="GodOfWealthView",},
{ID=1,task_name="挑战神灵仙岛X次",task_condition_id=16,task_desc="挑战神灵仙岛%s/1次",panel="fubenpanel#fubenpanel_bagua",},
{ID=2,task_name="活跃度达到X",task_condition_id=17,task_param1=100,task_desc="活跃度达到%s/100",panel="bizuo",},
{ID=3,task_name="活动期间登录1次",task_condition_id=26,task_desc="活动期间登录%s/1次",panel="LongYunZhanLingTaskView",},
{ID=4,task_name="神灵神格抽奖N次",task_condition_id=19,task_param1=10,task_desc="神灵神格抽奖%s/10次",panel="TianShenBaoXia",},
{ID=5,task_name="拥有N个神灵",task_condition_id=20,task_param1=2,task_desc="拥有%s/2个神灵",},
{ID=6,task_name="任意神灵等级达到N级",task_condition_id=21,task_param1=20,task_desc="任意神灵等级达到%s/20级",},
{ID=7,task_name="拥有X个Y品质神灵（神格提升）",task_condition_id=22,task_param1=2,task_param2=3,task_desc="拥有%s/2个至高天神品质神灵",reward_item={[0]=item_table[184]},jigsaw_num=3,},
{ID=8,task_name="活动期间累计消费X积分",task_condition_id=23,task_param1=30,task_desc="活动期间累计消费%s/30积分",panel="YanYuGeExchangeShopView",},
{ID=9,task_name="上架N个物品",task_condition_id=24,task_param1=3,task_desc="上架%s/3个物品",panel="market#market_sell",},
{ID=10,task_name="发N个仙盟红包",task_condition_id=25,task_desc="发%s/1个仙盟红包",panel="guild#guild_redpacket",}
},

woyaoshenqi_task_meta_table_map={
},
woyaoshenqi_gift={
{},
{ID=2,reward_item={[0]=item_table[185],[1]=item_table[166],[2]=item_table[186],[3]=item_table[187],[4]=item_table[188]},price=588,old_price=4580,gift_name="<color=#ec0228>神灵超值礼包</color>",},
{ID=3,reward_item={[0]=item_table[185],[1]=item_table[189],[2]=item_table[186],[3]=item_table[187],[4]=item_table[188]},price=888,old_price=5880,discount=2,gift_name="<color=#ec0228>神灵终极礼包</color>",}
},

woyaoshenqi_gift_meta_table_map={
},
woyaoshenqi_reward={
{},
{ID=1,jigsaw_num=10,},
{ID=2,jigsaw_num=15,reward_item={[0]=item_table[150],[1]=item_table[190],[2]=item_table[191],[3]=item_table[192]},},
{ID=3,jigsaw_num=20,},
{ID=4,jigsaw_num=21,reward_item={[0]=item_table[155],[1]=item_table[151],[2]=item_table[193],[3]=item_table[194]},},
{ID=5,jigsaw_num=24,reward_item={[0]=item_table[195],[1]=item_table[163],[2]=item_table[82],[3]=item_table[196],[4]=item_table[197]},}
},

woyaoshenqi_reward_meta_table_map={
[4]=3,	-- depth:1
},
tianshencongbang_reward={
{},
{ID=2,zhanli_cond=50000,condition_desc="神灵总战力达到<color=#006a25>50000</color>",reward_item={[0]=item_table[198],[1]=item_table[150],[2]=item_table[199],[3]=item_table[200]},}
},

tianshencongbang_reward_meta_table_map={
},
tianshencongbang_rank_cond={
{chaozhi="1:0:0:0:0",},
{rank=10,min_zhanli=400000,reward_item={[0]=item_table[201],[1]=item_table[152],[2]=item_table[147],[3]=item_table[202],[4]=item_table[203]},condition_desc="神灵冲榜<color=#ed6127>4~10名</color>  总战力达到<color=#006a25>400000</color>",},
{rank=20,min_zhanli=200000,reward_item={[0]=item_table[72],[1]=item_table[152],[2]=item_table[85],[3]=item_table[162],[4]=item_table[163]},condition_desc="神灵冲榜<color=#ed6127>11~20名</color>  总战力达到<color=#006a25>200000</color>",}
},

tianshencongbang_rank_cond_meta_table_map={
},
tianshencongbang_tishen={
{},
{ID=2,icon="btn_ts_shenshi",desc="神灵神纹",panel="TianShenView#tianshen_shenshi",}
},

tianshencongbang_tishen_meta_table_map={
},
mowangyouli_task={
{},
{ID=2,task_name="灵妖奇脉",task_num_limit=2,panel="boss#boss_personal",icon="btn_39",boss_type=2,},
{ID=3,task_name="荒天炎窟",task_num_limit=2,panel="WorldServer#worserv_boss_mh",icon="btn_53",boss_type=5,},
{ID=4,task_name="仙遗洞天",panel="boss#boss_vip",icon="btn_38",boss_type=4,}
},

mowangyouli_task_meta_table_map={
},
duobeijiangli={
{},
{day=2,task_type=5,wanfa_name="熔火之心",reward_item={[0]=item_table[204],[1]=item_table[205]},multi_itms="22636:22008",multi_drop_device="",panel="fubenpanel#fubenpanel_copper",module_name="fubenpanel_copper",icon="a3_zjm_icon_rhzx",},
{day=3,task_type=4,wanfa_name="暗翼之巢",multi_drop_device="30001:30002:30003:30004:30005:30006:30007:30008:30009:30010:30101:30102:30103:30104:30105:30106:30107:30108:30109:30110:30111:30112:30113:30114:30115:30116:30117:30118:30119:30120:30121:30122:30123:30124:30125:30126:30127:30128:30129:30130:30131:30132:30133:30134:30135:30136:30137:30138:30139:30140",panel="fubenpanel#fubenpanel_pet",module_name="fubenpanel_pet",icon="a3_zjm_icon_ayzc",}
},

duobeijiangli_meta_table_map={
},
tianshen_dalian={
{}
},

tianshen_dalian_meta_table_map={
},
chongzhi_reset={
{}
},

chongzhi_reset_meta_table_map={
},
single_charge={
{reward_item={[0]=item_table[89],[1]=item_table[206],[2]=item_table[207],[3]=item_table[208],[4]=item_table[54]},},
{opengame_day=7,},
{opengame_day=14,},
{opengame_day=21,},
{seq=1,charge_value=300,reward_item={[0]=item_table[209],[1]=item_table[210],[2]=item_table[211],[3]=item_table[208],[4]=item_table[212]},},
{opengame_day=7,reward_item={[0]=item_table[213],[1]=item_table[210],[2]=item_table[214],[3]=item_table[208],[4]=item_table[212]},},
{opengame_day=14,},
{opengame_day=21,},
{seq=2,charge_value=680,reward_item={[0]=item_table[86],[1]=item_table[215],[2]=item_table[216],[3]=item_table[208],[4]=item_table[217]},},
{opengame_day=7,reward_item={[0]=item_table[218],[1]=item_table[219],[2]=item_table[220],[3]=item_table[208],[4]=item_table[217]},},
{opengame_day=14,reward_item={[0]=item_table[218],[1]=item_table[221],[2]=item_table[220],[3]=item_table[208],[4]=item_table[217]},},
{opengame_day=21,},
{seq=3,charge_value=980,reward_item={[0]=item_table[222],[1]=item_table[223],[2]=item_table[224],[3]=item_table[208],[4]=item_table[225]},},
{opengame_day=7,reward_item={[0]=item_table[226],[1]=item_table[227],[2]=item_table[228],[3]=item_table[208],[4]=item_table[225]},},
{opengame_day=14,reward_item={[0]=item_table[226],[1]=item_table[229],[2]=item_table[228],[3]=item_table[208],[4]=item_table[225]},},
{opengame_day=21,},
{seq=4,charge_value=1280,reward_item={[0]=item_table[40],[1]=item_table[96],[2]=item_table[230],[3]=item_table[231],[4]=item_table[232]},},
{opengame_day=7,},
{opengame_day=14,reward_item={[0]=item_table[233],[1]=item_table[96],[2]=item_table[91],[3]=item_table[231],[4]=item_table[232]},},
{opengame_day=21,},
{seq=5,charge_value=1980,reward_item={[0]=item_table[62],[1]=item_table[234],[2]=item_table[235],[3]=item_table[236],[4]=item_table[237]},},
{opengame_day=7,reward_item={[0]=item_table[238],[1]=item_table[221],[2]=item_table[239],[3]=item_table[236],[4]=item_table[237]},},
{opengame_day=14,reward_item={[0]=item_table[238],[1]=item_table[240],[2]=item_table[239],[3]=item_table[236],[4]=item_table[237]},},
{opengame_day=21,},
{seq=6,charge_value=3280,reward_item={[0]=item_table[63],[1]=item_table[241],[2]=item_table[242],[3]=item_table[243],[4]=item_table[244]},},
{opengame_day=7,reward_item={[0]=item_table[245],[1]=item_table[241],[2]=item_table[246],[3]=item_table[243],[4]=item_table[244]},},
{opengame_day=14,},
{opengame_day=21,},
{seq=7,charge_value=6480,reward_item={[0]=item_table[247],[1]=item_table[223],[2]=item_table[248],[3]=item_table[101],[4]=item_table[249]},},
{opengame_day=7,reward_item={[0]=item_table[250],[1]=item_table[227],[2]=item_table[251],[3]=item_table[252],[4]=item_table[249]},},
{opengame_day=14,reward_item={[0]=item_table[250],[1]=item_table[229],[2]=item_table[251],[3]=item_table[252],[4]=item_table[249]},},
{opengame_day=21,reward_item={[0]=item_table[250],[1]=item_table[229],[2]=item_table[251],[3]=item_table[132],[4]=item_table[249]},},
{seq=8,charge_value=10000,reward_item={[0]=item_table[175],[1]=item_table[241],[2]=item_table[253],[3]=item_table[254],[4]=item_table[255]},},
{opengame_day=7,reward_item={[0]=item_table[256],[1]=item_table[241],[2]=item_table[257],[3]=item_table[252],[4]=item_table[255]},},
{opengame_day=14,reward_item={[0]=item_table[256],[1]=item_table[241],[2]=item_table[257],[3]=item_table[132],[4]=item_table[255]},},
{opengame_day=21,reward_item={[0]=item_table[256],[1]=item_table[241],[2]=item_table[258],[3]=item_table[132],[4]=item_table[259]},},
{seq=9,charge_value=20000,reward_item={[0]=item_table[258],[1]=item_table[241],[2]=item_table[84],[3]=item_table[254],[4]=item_table[260]},},
{opengame_day=7,reward_item={[0]=item_table[261],[1]=item_table[241],[2]=item_table[262],[3]=item_table[132],[4]=item_table[260]},},
{opengame_day=14,},
{opengame_day=21,reward_item={[0]=item_table[261],[1]=item_table[241],[2]=item_table[263],[3]=item_table[264],[4]=item_table[265]},},
{seq=10,charge_value=50000,reward_item={[0]=item_table[266],[1]=item_table[175],[2]=item_table[267],[3]=item_table[268],[4]=item_table[269]},},
{opengame_day=7,reward_item={[0]=item_table[270],[1]=item_table[175],[2]=item_table[271],[3]=item_table[132],[4]=item_table[269]},},
{opengame_day=14,reward_item={[0]=item_table[270],[1]=item_table[175],[2]=item_table[271],[3]=item_table[131],[4]=item_table[269]},},
{opengame_day=21,reward_item={[0]=item_table[272],[1]=item_table[175],[2]=item_table[273],[3]=item_table[274],[4]=item_table[275]},},
{seq=11,charge_value=100000,reward_item={[0]=item_table[276],[1]=item_table[277],[2]=item_table[278],[3]=item_table[268],[4]=item_table[279]},},
{opengame_day=7,reward_item={[0]=item_table[272],[1]=item_table[280],[2]=item_table[281],[3]=item_table[131],[4]=item_table[279]},},
{opengame_day=14,reward_item={[0]=item_table[272],[1]=item_table[282],[2]=item_table[281],[3]=item_table[274],[4]=item_table[279]},},
{opengame_day=21,reward_item={[0]=item_table[283],[1]=item_table[284],[2]=item_table[285],[3]=item_table[286],[4]=item_table[287]},},
{seq=12,charge_value=200000,reward_item={[0]=item_table[276],[1]=item_table[288],[2]=item_table[289],[3]=item_table[290],[4]=item_table[291]},},
{opengame_day=7,reward_item={[0]=item_table[283],[1]=item_table[282],[2]=item_table[292],[3]=item_table[274],[4]=item_table[291]},},
{opengame_day=14,reward_item={[0]=item_table[283],[1]=item_table[284],[2]=item_table[292],[3]=item_table[286],[4]=item_table[291]},},
{opengame_day=21,reward_item={[0]=item_table[293],[1]=item_table[294],[2]=item_table[295],[3]=item_table[296],[4]=item_table[297]},}
},

single_charge_meta_table_map={
[38]=37,	-- depth:1
[36]=33,	-- depth:1
[34]=33,	-- depth:1
[35]=33,	-- depth:1
[43]=41,	-- depth:1
[40]=37,	-- depth:1
[42]=41,	-- depth:1
[44]=41,	-- depth:1
[46]=45,	-- depth:1
[47]=45,	-- depth:1
[48]=45,	-- depth:1
[50]=49,	-- depth:1
[39]=38,	-- depth:2
[32]=29,	-- depth:1
[26]=25,	-- depth:1
[30]=29,	-- depth:1
[6]=5,	-- depth:1
[7]=6,	-- depth:2
[8]=7,	-- depth:3
[10]=9,	-- depth:1
[11]=9,	-- depth:1
[12]=11,	-- depth:2
[14]=13,	-- depth:1
[15]=13,	-- depth:1
[31]=29,	-- depth:1
[16]=15,	-- depth:2
[19]=17,	-- depth:1
[20]=19,	-- depth:2
[22]=21,	-- depth:1
[23]=21,	-- depth:1
[24]=23,	-- depth:2
[51]=49,	-- depth:1
[27]=26,	-- depth:2
[28]=27,	-- depth:3
[18]=20,	-- depth:3
[52]=49,	-- depth:1
},
marry_rebate={
{},
{marry_type=1,need_gold=2688,show_discount=8,},
{marry_type=2,need_gold=5388,show_discount=6,}
},

marry_rebate_meta_table_map={
},
shake_money={
{},
{seq=2,section_start=8,section_end=14,},
{seq=3,section_start=15,section_end=21,},
{seq=4,section_start=22,section_end=28,},
{seq=5,section_start=29,section_end=35,},
{seq=6,section_start=36,section_end=0,}
},

shake_money_meta_table_map={
},
crazy_rebate={
{reward_precent=15,},
{gold_low_limit=1000,gold_high_limit=5000,reward_precent=20,},
{gold_low_limit=5000,gold_high_limit=20000,reward_precent=30,},
{gold_low_limit=20000,gold_high_limit=50000,},
{gold_low_limit=50000,gold_high_limit=80000,},
{gold_low_limit=80000,gold_high_limit=110000,},
{gold_low_limit=110000,gold_high_limit=140000,},
{gold_low_limit=140000,gold_high_limit=170000,},
{gold_low_limit=170000,gold_high_limit=200000,},
{gold_low_limit=200000,gold_high_limit=9999999,}
},

crazy_rebate_meta_table_map={
},
daily_love_reward_percent={
{},
{opengame_day=14,},
{opengame_day=30,},
{opengame_day=60,},
{opengame_day=90,},
{opengame_day=9999,}
},

daily_love_reward_percent_meta_table_map={
},
chongzhi_rank_2={
{},
{min_rank=2,max_rank=3,reward_item={[0]=item_table[298],[1]=item_table[299],[2]=item_table[300],[3]=item_table[301],[4]=item_table[302],[5]=item_table[303]},limit_chongzhi=200000,},
{min_rank=4,max_rank=10,reward_item={[0]=item_table[300],[1]=item_table[301],[2]=item_table[302],[3]=item_table[303]},limit_chongzhi=80000,},
{min_rank=11,max_rank=20,reward_item={[0]=item_table[304],[1]=item_table[305],[2]=item_table[306],[3]=item_table[307]},limit_chongzhi=20000,},
{start_day=21,end_day=27,reward_item={[0]=item_table[308],[1]=item_table[309],[2]=item_table[310],[3]=item_table[311],[4]=item_table[312],[5]=item_table[313],[6]=item_table[314],[7]=item_table[315]},},
{start_day=21,end_day=27,reward_item={[0]=item_table[308],[1]=item_table[309],[2]=item_table[316],[3]=item_table[317],[4]=item_table[318],[5]=item_table[319]},},
{start_day=21,end_day=27,reward_item={[0]=item_table[316],[1]=item_table[317],[2]=item_table[318],[3]=item_table[319]},},
{start_day=21,end_day=27,reward_item={[0]=item_table[320],[1]=item_table[321],[2]=item_table[322],[3]=item_table[323]},},
{start_day=28,end_day=34,reward_item={[0]=item_table[324],[1]=item_table[325],[2]=item_table[326],[3]=item_table[327],[4]=item_table[328],[5]=item_table[329],[6]=item_table[330],[7]=item_table[331]},},
{start_day=28,end_day=34,reward_item={[0]=item_table[324],[1]=item_table[325],[2]=item_table[332],[3]=item_table[333],[4]=item_table[334],[5]=item_table[335]},},
{start_day=28,end_day=34,reward_item={[0]=item_table[332],[1]=item_table[333],[2]=item_table[334],[3]=item_table[335]},},
{start_day=28,end_day=34,reward_item={[0]=item_table[336],[1]=item_table[337],[2]=item_table[338],[3]=item_table[339]},},
{start_day=35,end_day=41,reward_item={[0]=item_table[340],[1]=item_table[341],[2]=item_table[342],[3]=item_table[343],[4]=item_table[344],[5]=item_table[345],[6]=item_table[346],[7]=item_table[347]},},
{start_day=35,end_day=41,reward_item={[0]=item_table[340],[1]=item_table[341],[2]=item_table[348],[3]=item_table[349],[4]=item_table[350],[5]=item_table[351]},},
{start_day=35,end_day=41,reward_item={[0]=item_table[348],[1]=item_table[349],[2]=item_table[350],[3]=item_table[351]},},
{start_day=35,end_day=41,reward_item={[0]=item_table[352],[1]=item_table[353],[2]=item_table[354],[3]=item_table[355]},},
{start_day=42,end_day=48,reward_item={[0]=item_table[356],[1]=item_table[357],[2]=item_table[358],[3]=item_table[359],[4]=item_table[360],[5]=item_table[361],[6]=item_table[362],[7]=item_table[363]},},
{start_day=42,end_day=48,reward_item={[0]=item_table[356],[1]=item_table[357],[2]=item_table[364],[3]=item_table[365],[4]=item_table[366],[5]=item_table[367]},},
{start_day=42,end_day=48,reward_item={[0]=item_table[364],[1]=item_table[365],[2]=item_table[366],[3]=item_table[367]},},
{start_day=42,end_day=48,reward_item={[0]=item_table[368],[1]=item_table[369],[2]=item_table[370],[3]=item_table[371]},},
{start_day=49,end_day=55,reward_item={[0]=item_table[372],[1]=item_table[373],[2]=item_table[374],[3]=item_table[375],[4]=item_table[376],[5]=item_table[377],[6]=item_table[378],[7]=item_table[379]},},
{start_day=49,end_day=55,reward_item={[0]=item_table[372],[1]=item_table[373],[2]=item_table[380],[3]=item_table[381],[4]=item_table[382],[5]=item_table[383]},},
{start_day=49,end_day=55,reward_item={[0]=item_table[380],[1]=item_table[381],[2]=item_table[382],[3]=item_table[383]},},
{start_day=49,end_day=55,reward_item={[0]=item_table[384],[1]=item_table[385],[2]=item_table[386],[3]=item_table[387]},},
{start_day=56,end_day=62,reward_item={[0]=item_table[388],[1]=item_table[389],[2]=item_table[390],[3]=item_table[391],[4]=item_table[392],[5]=item_table[393],[6]=item_table[394],[7]=item_table[395]},},
{start_day=56,end_day=62,reward_item={[0]=item_table[388],[1]=item_table[389],[2]=item_table[396],[3]=item_table[397],[4]=item_table[398],[5]=item_table[399]},},
{start_day=56,end_day=62,reward_item={[0]=item_table[396],[1]=item_table[397],[2]=item_table[398],[3]=item_table[399]},},
{start_day=56,end_day=62,reward_item={[0]=item_table[400],[1]=item_table[401],[2]=item_table[402],[3]=item_table[403]},},
{start_day=63,end_day=69,reward_item={[0]=item_table[404],[1]=item_table[405],[2]=item_table[406],[3]=item_table[407],[4]=item_table[408],[5]=item_table[409],[6]=item_table[410],[7]=item_table[411]},},
{start_day=63,end_day=69,reward_item={[0]=item_table[404],[1]=item_table[405],[2]=item_table[412],[3]=item_table[413],[4]=item_table[414],[5]=item_table[415]},},
{start_day=63,end_day=69,reward_item={[0]=item_table[412],[1]=item_table[413],[2]=item_table[414],[3]=item_table[415]},},
{start_day=63,end_day=69,reward_item={[0]=item_table[416],[1]=item_table[417],[2]=item_table[418],[3]=item_table[419]},},
{start_day=70,end_day=76,reward_item={[0]=item_table[420],[1]=item_table[421],[2]=item_table[422],[3]=item_table[423],[4]=item_table[424],[5]=item_table[425],[6]=item_table[426],[7]=item_table[427]},},
{start_day=70,end_day=76,reward_item={[0]=item_table[420],[1]=item_table[421],[2]=item_table[428],[3]=item_table[429],[4]=item_table[430],[5]=item_table[431]},},
{start_day=70,end_day=76,reward_item={[0]=item_table[428],[1]=item_table[429],[2]=item_table[430],[3]=item_table[431]},},
{start_day=70,end_day=76,reward_item={[0]=item_table[432],[1]=item_table[433],[2]=item_table[434],[3]=item_table[435]},},
{start_day=77,end_day=83,reward_item={[0]=item_table[436],[1]=item_table[437],[2]=item_table[438],[3]=item_table[439],[4]=item_table[440],[5]=item_table[441],[6]=item_table[442],[7]=item_table[443]},},
{start_day=77,end_day=83,reward_item={[0]=item_table[436],[1]=item_table[437],[2]=item_table[444],[3]=item_table[445],[4]=item_table[446],[5]=item_table[447]},},
{start_day=77,end_day=83,reward_item={[0]=item_table[444],[1]=item_table[445],[2]=item_table[446],[3]=item_table[447]},},
{start_day=77,end_day=83,reward_item={[0]=item_table[448],[1]=item_table[449],[2]=item_table[450],[3]=item_table[451]},},
{start_day=84,end_day=9999,reward_item={[0]=item_table[452],[1]=item_table[453],[2]=item_table[454],[3]=item_table[455],[4]=item_table[456],[5]=item_table[457],[6]=item_table[458],[7]=item_table[459]},},
{start_day=84,end_day=9999,reward_item={[0]=item_table[453],[1]=item_table[454],[2]=item_table[455],[3]=item_table[456],[4]=item_table[457],[5]=item_table[458]},},
{start_day=84,end_day=9999,reward_item={[0]=item_table[454],[1]=item_table[455],[2]=item_table[456],[3]=item_table[457]},},
{start_day=84,end_day=9999,reward_item={[0]=item_table[460],[1]=item_table[456],[2]=item_table[457],[3]=item_table[458]},}
},

chongzhi_rank_2_meta_table_map={
[28]=4,	-- depth:1
[30]=2,	-- depth:1
[31]=3,	-- depth:1
[32]=4,	-- depth:1
[39]=3,	-- depth:1
[34]=2,	-- depth:1
[35]=3,	-- depth:1
[36]=4,	-- depth:1
[42]=2,	-- depth:1
[38]=2,	-- depth:1
[40]=4,	-- depth:1
[27]=3,	-- depth:1
[22]=2,	-- depth:1
[24]=4,	-- depth:1
[6]=2,	-- depth:1
[7]=3,	-- depth:1
[8]=4,	-- depth:1
[10]=2,	-- depth:1
[11]=3,	-- depth:1
[12]=4,	-- depth:1
[26]=2,	-- depth:1
[14]=2,	-- depth:1
[16]=4,	-- depth:1
[18]=2,	-- depth:1
[19]=3,	-- depth:1
[20]=4,	-- depth:1
[43]=3,	-- depth:1
[23]=3,	-- depth:1
[15]=3,	-- depth:1
[44]=4,	-- depth:1
},
activity_model={
{model_bundle_name="",model_asset_name="",model_show_itemid=27805,model_pos="-153|78|0",model_scale=1.3,},
{activity_id=2216,},
{activity_id=2217,}
},

activity_model_meta_table_map={
},
other_default_table={opengame_day=7,lucky_roll_extra_reward_need_times=10,lucky_roll_unit_roll_consume_gold=30,lucky_roll_unit_enter_gold_pool_num=15,fanli_rate=10,chestshop_discount=90,quanmin_qifu_discount=90,need_kill_boss_count=3,login_gift_accumulate_reward_need_days=5,tomorrow_reward_need_active_degree=10,niu_egg_need_charge=200,zhenbaoge_free_refresh_times=2,zhenbaoge_cd=7200,zhenbaoge_refresh_gold=10,money_tree_init_gold=1000,tianming_free_chou_times=0,mine_refresh_gold=38,mine_refresh_num=4,mine_free_times=10,mine_server_reward_level=35,continue_chongzhi_fetch_extra_reward_need_days=5,continue_chongzhi_extra_reward=item_table[461],funny_shoot_every_day_free_shoot_times=5,funny_shoot_one_times_need_gold=20,funny_shoot_ten_times_need_gold=180,funny_shoot_fifty_times_need_gold=900,daily_love_reward_precent=100,planting_tree_tree_living_time=3,planting_tree_max_watering_times=10,planting_tree_gather_id=210,continue_consume_fetch_extra_reward_need_days=5,continue_consume_extra_reward=item_table[462],fanfan_free_fan_times_per_day=5,fanfan_once_need_gold=20,fanfan_refresh_need_gold=28,fanfan_auto_refresh_interval=4,fanfan_word_increase_rate=100,promoting_position_free_play_interval=8,promoting_position_extra_time_per_chongzhi_gold=100,promoting_position_play_once_gold=100,promoting_position_10_times_gold=900,runningman_treasure_livetime=30,rush_buying_duration=10,qitian_fali_percent=10,exp_double_need_min_role_level=80,weekend_boss_id=50,level_lottery_free_cd_time=25200,level_lottery_online_max_free_times=0,national_celebration_gift=29417,national_celebration_need_gold=10,national_celebration_server_reward_item=item_table[463],wishpool_gift=28658,wishpool_buy_need_gold=30,wishpool_process_max=35,wishpool_precious_process=30,collect_blessing_gift=28656,collect_blessing_need_gold=80,national_treasure_gift=28659,national_treasure_need_gold=50,national_treasure_process_max=60,national_treasure_precious_process=55,percentage_time=20,cloud_buy_total_times=300,cloud_buy_limit_time=1400,cloud_buy_gift_id=29420,cloud_buy_need_gold=30,national_celebration2_gift=29417,national_celebration2_need_gold=10,national_celebration2_server_reward_item=item_table[463],yanhua_celebration_gift=28657,yanhua_celebration_need_gold=80,vip_zhuli_shenqing_min_vip_level=1,vip_zhuli_shenqing_max_vip_level=3,vip_zhuli_zhumeng_min_vip_level=4,vip_zhuli_shenqing_need_login_daycount=5,vip_zhuli_get_vip_level=4,vip_zhuli_get_duration_day=180,vip_zhuli_shengqing_info="VIP1~VIP3级可申请\n申请后，可寻找助梦人进行绑定\n每人仅能绑定一次\n绑定后，累计登陆5天可直升V4，且增加180天VIP时间\n绑定期间，若自行升到V4，完成登陆后可额外获得180天VIP时间\n活动结束后，奖励仍然可继续领取，领取后活动面板关闭",vip_zhuli_activity_info="VIP4及其以上的玩家可以成为助梦者\n助梦者可在申请人列表中选择助梦对象，发出助梦邀请后需要申请者同意后才能算助梦成功\n助梦所需的元宝在助梦成功后才会扣除\n每人仅能成功助梦1名玩家\n助梦成功后每日登陆游戏即可领取丰厚大奖",kaifujizhan_rolelevel=40,worth_buy_open_level=70,worth_buy_tips_time=5,worth_buy_countdown=360,show_bubble_time=60,mutex_view="SelectCameraModeView|AppearanceGetNew|GuradInvalidTimeView|VipTip|TipsShowChapterView|RechargeVipTyView",active_turntable_num=3,active_turntable_time=5,active_roll_reset_time=0,loving_city_convert_image_progress_value=120,loving_city_image_item=item_table[464],loving_city_resource_id=20102,love_profess_resource_id=20102,perfect_lover_open_level=180,barrage_time="20|18|15",barrage_space="200|200|150",perfect_lover_item_id=38651,mw_drop_item="27613|28033",mw_drop_item_limilt="40|4",},

fierce_fighting_default_table={id=1500,first_reward={[0]=item_table[465]},kill_reward={[0]=item_table[466],[1]=item_table[467]},jump="",money_type=3,hongbao=100,scale=1,position="",},

active_turntable_reward_default_table={activity_day=1,reward_seq=0,is_last=0,reward_item=item_table[465],is_gold=0,gold_num=0,},

active_turntable_task_default_table={task_id=0,reward_times=1,task_name="每天首次登陆",},

worth_buy_default_table={seq=0,buy_item=item_table[468],buy_limit=1,money_type=1,buy_cost=256,origin_cost=2000,discount=1,opengame_day=0,pre_seq=0,pre_num=1,task_id=0,role_level=20,vip_level=0,sale_time=0,rank_seq=37,is_popup=1,description="超扣超低！过时不候！",item_name="背饰提升礼包",},

loving_city_task_default_table={task_id=0,task_type=14,param_1=1,param_2=0,param_3=0,reward_item={[0]=item_table[469]},reward_coin=0,reward_bind_gold=0,reward_silver=0,reward_process=10,task_name="连理枝升级了",task_desc="完成%s/1次结婚",task_process=1,reward_show={[0]=item_table[469]},is_close_view=0,open_panel="marry#marry_jiehun",},

loving_city_process_reward_default_table={seq=1,need_process=30,reward_item={[0]=item_table[470],[1]=item_table[471],[2]=item_table[472]},dec="进度一",},

profess_for_love_rank_reward_default_table={seq=0,rank_max=3,rank_val_limit=1000,reward_item={[0]=item_table[161],[1]=item_table[473],[2]=item_table[42],[3]=item_table[474]},rank_max_client=1,rank_min_client=3,rank_val_max_client=999999999,rank_val_min_client=1000,},

profess_for_love_score_reward_default_table={id=0,score_value=710,reward_item={[0]=item_table[47],[1]=item_table[45],[2]=item_table[471]},},

profess_for_love_end_default_table={end_day=5,end_time=2300,during_time_s=300,champagne_icon=26125,rocket_icon=26126,cruise_icon=26127,},

sweet_talk_fake_notice_default_table={profess_type=3,professor="君拂",be_professor="慕言",content="",},

tianshenxunbao_layer_default_table={layer=0,name="瀚海星盘",round_count=3,round_continue_time_h="168|0|0",reward_pool_id="0|1|2",icon_id=46378,get_exchange_value=1,openday=1,},

tianshenxunbao_reward_pool_default_table={reward_pool_id=0,draw_consume_item_id=46378,draw_consume_item_count="1|2|4|8|16|20|25|30|40|50|65|80|100|120|140|160",need_draw_times="1,1|2,2|3,3|4,4|5,5|6,6|7,7|8,8|9,9|10,10|11,11|12,12|13,13|14,14|15,15|16,16",reward_id_list="61,62,63|61,62,63,64|61,62,63,64,65|61,62,63,64,65,66|61,62,63,64,65,66,67|61,62,63,64,65,66,67,68|61,62,63,64,65,66,67,68,69|61,62,63,64,65,66,67,68,69,70|61,62,63,64,65,66,67,68,69,70,71|61,62,63,64,65,66,67,68,69,70,71,72|61,62,63,64,65,66,67,68,69,70,71,72,73|61,62,63,64,65,66,67,68,69,70,71,72,73,74|61,62,63,64,65,66,67,68,69,70,71,72,73,74,75|61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76|61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76|61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76",reward_weight_list="60,39,5|80,60,20,5|80,80,60,20,5|80,80,80,60,20,2|80,80,80,80,60,10,5|80,80,80,80,80,100,20,5|80,80,80,80,80,80,60,20,2|80,80,80,80,80,80,80,60,10,5|80,80,80,80,80,80,80,80,100,20,2|80,80,80,80,80,80,80,80,80,60,10,5|80,80,80,80,80,80,80,80,80,80,100,20,5|80,80,80,80,80,80,80,80,80,80,80,60,20,2|80,80,80,80,80,80,80,80,80,80,80,80,60,10,5|80,80,80,80,80,80,80,80,80,80,80,80,80,100,20,2|80,80,80,80,80,80,80,80,80,80,80,80,80,80,60,10|80,80,80,80,80,80,80,80,80,80,80,80,80,80,80,80",shop_seq=11075,one_key=1,},

tianshenxunbao_reward_default_table={reward_id=1,reward_item=item_table[40],x_pos=467,y_pos=141,is_l_kite=1,is_best=0,},

tianshenxunbao_exchange_default_table={layer=2,exchange_item=item_table[109],exchange_consume=10,exchange_limit=3,},

tianshenxunbao_bubble_default_table={layer=0,bubble_content="你看那个大风筝，一定是个好东西！",},

tianshen_theme_desc_default_table={active_name="登录有礼",open_level=100,real_id=10,rank_id=1,xuanchuanbiaoyu="AAA",xuanchuantu="BBB",rule_tip="机不可失，一定要准时参加哦！",rule_desc="1.活动期间，每日登录游戏，即可领取超值好礼\n2.登录获得<color=#6FBB6F>抽奖券</color>，可前往<color=#6FBB6F>寻宝</color>活动参与夺宝，更能获得<color=#6FBB6F>稀有神灵</color>\n3.活动结束后，未领取的奖励将通过邮件发放",tab_name="登录有礼",},

tianshen_theme_other_default_table={zhichongbeishu=10,chongbang_max_rank=50,shenqi_id=4,sq_frag_comp_seq=0,sq_model_index=7,mw_boss_id=1632,mw_scale=110,mw_position="-1.63|-0.35|0",mw_cap=52386,mw_kill_condition="击杀BOSS即有机会掉落寻宝券！",mw_reward_item={[0]=item_table[475],[1]=item_table[476],[2]=item_table[477]},lc_reward_item={[0]=item_table[478],[1]=item_table[479],[2]=item_table[480],[3]=item_table[481]},cb_model_index=9,tips_delay=2.5,role_anim_time=0.5,angle_speed=200,arrow_speed=1500,kite_time=1,woyaoshenqi_product=46388,woyaosq_item=item_table[482],},

new_login_reward_default_table={day_index=1,reward_item={[0]=item_table[53],[1]=item_table[85],[2]=item_table[483],[3]=item_table[198],[4]=item_table[199]},vip_lv=0,special_reward_item={[0]=item_table[53],[1]=item_table[85],[2]=item_table[483],[3]=item_table[198],[4]=item_table[199]},},

shouchong_reward_default_table={day_index=1,reward_item={[0]=item_table[484],[1]=item_table[163],[2]=item_table[485],[3]=item_table[166],[4]=item_table[486]},chaozhi="0:0:0:0",daily_recharge_price=10888,},

leichonghaoli_reward_default_table={ID=1,stage_value=13600,reward_item={[0]=item_table[85],[1]=item_table[487],[2]=item_table[198],[3]=item_table[53],[4]=item_table[488]},lc_reward_item={[0]=item_table[85]},},

woyaoshenqi_task_default_table={ID=0,task_name="招财进宝",task_condition_id=14,task_param1=1,task_param2=0,task_param3=0,task_desc="购买招财进宝1元礼包",reward_item={[0]=item_table[489]},jigsaw_num=2,panel="TianShenView",},

woyaoshenqi_gift_default_table={ID=1,reward_item={[0]=item_table[185],[1]=item_table[162],[2]=item_table[490],[3]=item_table[187],[4]=item_table[206]},price=388,old_price=3880,discount=1,gift_name="<color=#ec0228>神灵特惠礼包</color>",},

woyaoshenqi_reward_default_table={ID=0,jigsaw_num=5,reward_item={[0]=item_table[53],[1]=item_table[139],[2]=item_table[200],[3]=item_table[491]},},

tianshencongbang_reward_default_table={ID=1,zhanli_cond=100000,condition_desc="神灵总战力达到<color=#006a25>100000</color>",reward_item={[0]=item_table[149],[1]=item_table[150],[2]=item_table[199],[3]=item_table[200]},},

tianshencongbang_rank_cond_default_table={rank=3,min_zhanli=800000,reward_item={[0]=item_table[492],[1]=item_table[493],[2]=item_table[157],[3]=item_table[494],[4]=item_table[166],[5]=item_table[172]},condition_desc="神灵冲榜<color=#ed6127>1~3名</color>  总战力达到<color=#006a25>800000</color>",chaozhi="0:0:0:0",},

tianshencongbang_tishen_default_table={ID=1,icon="btn_tianshen",desc="神灵系统",panel="TianShenView",},

mowangyouli_task_default_table={ID=1,task_name="伏魔战场",task_num_limit=5,mowang_level=330,panel="boss#boss_world",icon="btn_30",boss_type=1,},

duobeijiangli_default_table={day=1,task_type=6,wanfa_name="天峰夺宝",reward_mult=2,task_name="AAA",day_limit=99,coin=2,exp=2,shengwang=2,reward_item={},multi_itms="",multi_drop_device="20805:20806:20807:20808:20809:20810:20811:20812:20813:20814:20815:20905:20906:20907:20908:20909:20910:20911:20912:20913:20914:20915:21005:21006:21007:21008:21009:21010:21011:21012:21013:21014:21015:21105:21106:21107:21108:21109:21110:21111:21112:21113:21114:21115",panel="fubenpanel#fubenpanel_equip_high",module_name="fubenpanel_equip_high",icon="a3_zjm_icon_tfdb",},

tianshen_dalian_default_table={res_id=10028,boos_img_name="开天盘古",tip_img="tip1",reward_item={[0]=item_table[495],[1]=item_table[496],[4]=item_table[118],[5]=item_table[497]},scale=180,position="0.1|-1.82|0",act_time="活动时间：开服第8天-开服第11天",},

chongzhi_reset_default_table={},

single_charge_default_table={opengame_day=1,seq=0,charge_value=60,reward_item={[0]=item_table[213],[1]=item_table[206],[2]=item_table[498],[3]=item_table[208],[4]=item_table[54]},},

marry_rebate_default_table={marry_type=0,need_coin=0,need_lngot=0,need_gold_bind=0,need_gold=1188,show_discount=9,},

shake_money_default_table={seq=1,section_start=1,section_end=7,chongzhi_return=50,return_max=200000,},

crazy_rebate_default_table={gold_low_limit=0,gold_high_limit=1000,reward_precent=100,},

daily_love_reward_percent_default_table={opengame_day=1,gold_percent=150,},

chongzhi_rank_2_default_table={start_day=1,end_day=20,min_rank=1,max_rank=1,reward_item={[0]=item_table[298],[1]=item_table[299],[2]=item_table[499],[3]=item_table[500],[4]=item_table[501],[5]=item_table[502],[6]=item_table[503],[7]=item_table[504]},limit_chongzhi=500000,},

activity_model_default_table={activity_id=2213,model_show_type=1,model_bundle_name="model/boss/8042_prefab",model_asset_name=8042,model_show_itemid="",model_pos="-84|-24|0",model_rot="0|0|0",model_scale=1,}

}

