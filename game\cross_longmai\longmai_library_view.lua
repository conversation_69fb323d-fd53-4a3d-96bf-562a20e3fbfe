LongMaiLibraryView = LongMaiLibraryView or BaseClass(SafeBaseView)

function LongMaiLibraryView:__init()
	self:SetMaskBg(true, true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {vector2 = Vector2(0, 6), sizeDelta = Vector2(912, 554)})
	self:AddViewResource(0, "uis/view/longmai_ui_prefab", "layout_longmai_library")
end

function LongMaiLibraryView:__delete()

end

function LongMaiLibraryView:ReleaseCallBack()
  	if self.longmai_bag_list then
        self.longmai_bag_list:DeleteMe()
        self.longmai_bag_list = nil
    end
end

function LongMaiLibraryView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.CrossLongMai.LongMaiShopTitle
	 if not self.longmai_bag_list then
        self.longmai_bag_list = AsyncBaseGrid.New()
        self.longmai_bag_list:SetStartZeroIndex(false)
        self.longmai_bag_list:CreateCells({
            col = 2,
           	change_cells_num = 1,
            list_view = self.node_list["longmai_bag_list"],
            itemRender = LongMaiLibraryCell,
            assetBundle = "uis/view/longmai_ui_prefab",
            assetName = "longmai_library_cell",
        })
    end

end

function LongMaiLibraryView:OnFlush(param_t)
	local shop_list = CrossLongMaiWGData.Instance:GetShopInfoCfg()
	self.longmai_bag_list:SetDataList(shop_list)
end

--------------------LongMaiLibraryCell-----------------------
LongMaiLibraryCell = LongMaiLibraryCell or BaseClass(BaseRender)

function LongMaiLibraryCell:__init()
	
end

function LongMaiLibraryCell:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item_cell"])
	for i = 1, 5 do
	 	self.node_list["icon_" .. i].button:AddClickListener(BindTool.Bind(self.ShowItemTips, self, i))
	end
end

function LongMaiLibraryCell:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function LongMaiLibraryCell:OnFlush()
	if IsEmptyTable(self.data) then
		return 
	end

	local shop_item_cfg
	local item_cfg 
	local stuff_cfg = {}
	shop_item_cfg = CrossLongMaiWGData.Instance:GetShopByIdInfo(self.data.id)
	if not IsEmptyTable(shop_item_cfg) then
		self.item_cell:SetData({item_id = shop_item_cfg.product_id, num = 1})
		item_cfg = ItemWGData.Instance:GetItemConfig(shop_item_cfg.product_id)
		if item_cfg and item_cfg.name then
			self.node_list["name"].text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
		end

		for i = 1, 5 do
			if shop_item_cfg["stuff_id_" .. i] > 0 then
				table.insert(stuff_cfg, {stuff_id = shop_item_cfg["stuff_id_" .. i], stuff_count = shop_item_cfg["stuff_count_" .. i]})
			end
			self.node_list["price_bg_" .. i]:SetActive(false)
		end

		self.node_list["group_1"]:SetActive(#stuff_cfg > 0)
		self.node_list["group_2"]:SetActive(#stuff_cfg > 3)

		local stuff_item_cfg
		for t, v in ipairs(stuff_cfg) do
			self.node_list["price_bg_" .. t]:SetActive(true)
			self.node_list["stuff_count_" .. t].text.text = v.stuff_count
			stuff_item_cfg = ItemWGData.Instance:GetItemConfig(v.stuff_id)
			self.node_list["icon_" .. t].image:LoadSprite(ResPath.GetItem(stuff_item_cfg.icon_id))
		end
	end
end

function LongMaiLibraryCell:ShowItemTips(index)
	local stuff_cfg = {}
	local shop_item_cfg = {}

	if self.data then
		shop_item_cfg = CrossLongMaiWGData.Instance:GetShopByIdInfo(self.data.id)
		for i = 1, 5 do
			if shop_item_cfg["stuff_id_" .. i] > 0 then
				table.insert(stuff_cfg, {stuff_id = shop_item_cfg["stuff_id_" .. i], stuff_count = shop_item_cfg["stuff_count_" .. i]})
			end
		end
		if stuff_cfg[index] then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = stuff_cfg[index].stuff_id})
		end
	end
end