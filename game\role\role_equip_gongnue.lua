RoleEquipGongNue = RoleEquipGongNue or BaseClass(SafeBaseView)

ATTR_TYPE_ICON = {
	[1] = {icon ="icon_max_hp"},
	[2] = {icon ="icon_gong_ji"},
	[3] = {icon ="icon_fang_yu"},
	[4] = {icon ="icon_po_jia"},
	[5] = {icon ="icon_per_mianshang"},
	[6] = {icon ="icon_fujia_shanghai"},
	[7] = {icon ="icon_bao_ji"},
}

EQUIP_STAR = {
	[1] = {star = 0},
	[2] = {star = 1},
	[3] = {star = 2},
	[4] = {star = 1},
	[5] = {star = 2},
	[6] = {star = 3},
}

function RoleEquipGongNue:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
	self:AddViewResource(0, "uis/view/role_ui_prefab", "layout_gongnue")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function RoleEquipGongNue:__delete()

end

function RoleEquipGongNue:ReleaseCallBack()
	if self.equip_cells then
		for k,v in pairs(self.equip_cells) do
			v:DeleteMe()
		end
		self.equip_cells = nil
	end

	if nil ~= self.gonglve_attr_list then
		self.gonglve_attr_list:DeleteMe()
		self.gonglve_attr_list = nil
	end

	if nil ~= self.roll_timer then
		GlobalTimerQuest:CancelQuest(self.roll_timer)
		self.roll_timer = nil
	end
	self.list_view_delegate = nil
end

function RoleEquipGongNue:LoadCallBack()
	self.automove_param = 0
	self:CreateEquipCells()
	self.cell_list = {}
	self.list_view_delegate = self.node_list["list_attr"].list_simple_delegate
    self.list_view_delegate.NumberOfCellsDel = BindTool.Bind(self.GetNumberOfCells, self)
    self.list_view_delegate.CellRefreshDel = BindTool.Bind(self.CreateGongLveAttrList, self)
	self.roll_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.ListMoveHandler,self), 0.1)
end

function RoleEquipGongNue:OpenCallBack()
	self.node_list.title_view_name.text.text = Language.GuideModuleName.equip_gongnue
end

function RoleEquipGongNue:ShowIndexCallBack()
	self:Flush()
end

function RoleEquipGongNue:OnFlush()
	local role_gonglve_cfg = RoleWGData.Instance:GetEquipGongLveCfg()
	if role_gonglve_cfg then
		for i = 1, 6 do
			if role_gonglve_cfg["equip_" .. i] then
				self.equip_cells[i]:SetData({item_id = role_gonglve_cfg["equip_" .. i]})
			else
				self.equip_cells[i]:SetData()
			end
		end
		self.equip_cells[2]:SetLeftTopImg(1)
		self.equip_cells[3]:SetLeftTopImg(2)
		self.equip_cells[4]:SetLeftTopImg(1)
		self.equip_cells[5]:SetLeftTopImg(2)
		self.equip_cells[6]:SetLeftTopImg(3)
	end
end

function RoleEquipGongNue:CreateEquipCells()
	self.equip_cells = {}
	for i = 1, 6 do
		local cell = ItemCell.New(self.node_list["ph_cell_" .. i])
		self.equip_cells[i] = cell
	end
end

function RoleEquipGongNue:GetNumberOfCells()
    return #RoleWGData.Instance:GetGongLveAttrList()
end

function RoleEquipGongNue:CreateGongLveAttrList(cell,data_index)
	data_index = data_index +1
    local attr_cell = self.cell_list[cell]
    if nil == attr_cell then
        attr_cell = GongLveAttrListRender.New(cell.gameObject)
        self.cell_list[cell] = attr_cell
    end

    local gonglve_attr_cfg = RoleWGData.Instance:GetGongLveAttrList()
    attr_cell:SetData(gonglve_attr_cfg[data_index])
end

-- 按照给出的方向滚动
function RoleEquipGongNue:ListMoveHandler()
	if self.automove_param == 100 then
		GlobalTimerQuest:CancelQuest(self.roll_timer)
		self.roll_timer = nil
	end
	self.automove_param = self.automove_param + 2
	local percent = self.automove_param / 100 * 100
	if self.gonglve_attr_list then
		self.gonglve_attr_list:GetView():scrollToPercentVertical(percent, 0.5, true)
	end
end

function RoleEquipGongNue:OnClickEquipCell(id)
	local data = self.equip_cells[id]:GetData()
	if data then
		data.star = EQUIP_STAR[id]
		RoleWGCtrl.Instance:SetGonglveTipsData(data)
	end
end

-- -------------------------------------------------------------------
GongLveAttrListRender = GongLveAttrListRender or BaseClass(BaseRender)
function GongLveAttrListRender:__init()

end

function GongLveAttrListRender:__delete()
end

function GongLveAttrListRender:CreateChild()
	BaseRender.CreateChild(self)
end

function GongLveAttrListRender:OnFlush()
	if not self.data then return end
	self.node_list["lbl_att"].text.text = self.data.describe .. "         " .. self.data.value .. "%"
end

function GongLveAttrListRender:CreateSelectEffect()

end