-- 入队申请
--------------
NewTeamApplyView = NewTeamApplyView or BaseClass(SafeBaseView)
function NewTeamApplyView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, -2), sizeDelta = Vector2(814, 580)})
	self:AddViewResource(0, "uis/view/new_team_ui_prefab", "layout_apply")
	self.select_index = 0
end

function NewTeamApplyView:__delete()
end

function NewTeamApplyView:ReleaseCallBack()
	if self.apply_list then
		self.apply_list:DeleteMe()
		self.apply_list = nil
	end
	self.pt_has_change_btn_gray = nil

	if self.world_talk_timecount then
		GlobalEventSystem:UnBind(self.world_talk_timecount)
		self.world_talk_timecount = nil
	end

	if self.word_talk_cd_over then
		GlobalEventSystem:UnBind(self.word_talk_cd_over)
		self.word_talk_cd_over = nil
	end
end

function NewTeamApplyView:LoadCallBack()
	self:SetSecondView(nil, self.node_list["size"])
    self.node_list.title_view_name.text.text = Language.NewTeam.TitleApply
	self.apply_list = AsyncListView.New(NewTeamApplyListItem, self.node_list["ph_apply_list"])

	XUI.AddClickEventListener(self.node_list["btn_checkbg1"], BindTool.Bind1(self.OnClickCheck, self))
	XUI.AddClickEventListener(self.node_list["btn_today_refuse_check"], BindTool.Bind1(self.OnClickTodayCheck, self))
	XUI.AddClickEventListener(self.node_list["btn_auto_refuse"], BindTool.Bind1(self.OnClickAutoRefuse, self)) -- 全部拒绝
	XUI.AddClickEventListener(self.node_list["btn_apply_all"],BindTool.Bind1(self.OnClickApplyAll, self)) -- 全部同意
	XUI.AddClickEventListener(self.node_list["btn_invite"], BindTool.Bind1(self.OnClickInvite, self))
	XUI.AddClickEventListener(self.node_list["btn_speak"],BindTool.Bind1(self.OnClickSpeak, self))
	

    -- self.world_talk_timecount = GlobalEventSystem:Bind(TeamWorldTalk.NEW_TEAM_WORLD_TALK,BindTool.Bind(self.UpdateTeamWordTalkBtn,self))
    -- self.word_talk_cd_over = GlobalEventSystem:Bind(TeamWorldTalk.COMPLETE_CALL_BACK,BindTool.Bind(self.ComleteTeamWoldTalkCD,self))
end

function NewTeamApplyView:OnClickInvite()
	NewTeamWGCtrl.Instance:OpenInviteView()
	self:Close()
end

function NewTeamApplyView:OnClickSpeak()
	self:Close()
	NewTeamWGCtrl.Instance:OpenRecruitView()
	--NewTeamWGCtrl.Instance:ShowTalkView()
end

function NewTeamApplyView:UpdateTeamWordTalkBtn(time)
	if self.node_list.txt_btn_word_talk then
		self.node_list.txt_btn_word_talk.text.text = string.format(Language.NewTeam.WorldTalk6, time)
	end
	if not self.pt_has_change_btn_gray then
		self.pt_has_change_btn_gray = true
		if self.node_list["btn_speak"] then
			XUI.SetGraphicGrey(self.node_list["btn_speak"], true)
		end
	end
end

function NewTeamApplyView:ComleteTeamWoldTalkCD()
	if CountDownManager.Instance:HasCountDown("team_world_talk") then
		CountDownManager.Instance:RemoveCountDown("team_world_talk")
	end
	if self.node_list.txt_btn_word_talk then
		self.node_list.txt_btn_word_talk.text.text = ""
	end
	self.pt_has_change_btn_gray = nil
	if self.node_list.btn_speak then
		XUI.SetGraphicGrey(self.node_list["btn_speak"],false)
	end
end

function NewTeamApplyView:OnClickCheck()
	local must_check = SocietyWGData.Instance:GetTeamMustCheck()
	must_check = must_check == 0 and 1 or 0
	self.node_list["img_select1"]:SetActive(must_check == 0)

	local send_protocol = ProtocolPool.Instance:GetProtocol(CSChangeMustCheck)
	send_protocol.must_check = must_check
	send_protocol:EncodeAndSend()
end

function NewTeamApplyView:GetTodayCheckActive()
	if not self.node_list["btn_today_refuse_select"] then
		return false
	end
	return self.node_list["btn_today_refuse_select"].gameObject.activeSelf
end

function NewTeamApplyView:OnClickTodayCheck()
	local is_select = self.node_list["btn_today_refuse_select"].gameObject.activeSelf
	self.node_list["btn_today_refuse_select"]:SetActive(not is_select)

	--local must_check = SocietyWGData.Instance:GetTeamMustCheck()
	--must_check = must_check == 0 and 1 or 0
	--self.node_list["img_select1"]:SetActive(must_check == 0)
	--
	--local send_protocol = ProtocolPool.Instance:GetProtocol(CSChangeMustCheck)
	--send_protocol.must_check = must_check
	--send_protocol:EncodeAndSend()
end

-- 全部拒绝
function NewTeamApplyView:OnClickAutoRefuse()
	local allJoinReq = SocietyWGData.Instance:GetReqTeamList()
	if nil == allJoinReq or #allJoinReq <= 0 then
		return
	end

	SocietyWGData.Instance:TeamJoinReqClear()
	self:Flush()
	--关闭当前界面
	self:Close()
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView)

end

-- 全部同意
function NewTeamApplyView:OnClickApplyAll()
	local allJoinReq = SocietyWGData.Instance:GetReqTeamList()
	if nil == allJoinReq or #allJoinReq <= 0 then
		return
	end
	for k,v in pairs(allJoinReq) do
		local send_protocol = ProtocolPool.Instance:GetProtocol(CSReqJoinTeamRet)
		send_protocol.req_role_id = v.req_role_id
		send_protocol.result = 0
		send_protocol:EncodeAndSend()
	end
	SocietyWGData.Instance:TeamJoinReqClear()
	self:Flush()
	--关闭当前界面
	self:Close()
	ViewManager.Instance:FlushView(GuideModuleName.NewTeamView)

end

function NewTeamApplyView:ShowIndexCallBack()
	self:Flush()
	self.node_list["btn_today_refuse_select"]:SetActive(false)
end

function NewTeamApplyView:OnFlush()
	local apply_list = SocietyWGData.Instance:GetReqTeamList()
	if apply_list then
		self.apply_list:SetDataList(apply_list, 0)
	end
	self.node_list.bottom_btn_group1:SetActive(#apply_list > 0)
	self.node_list.layout_blank_tip2:SetActive(#apply_list <= 0)
	self.node_list.bottom_btn_group2:SetActive(#apply_list <= 0)

	local must_check = SocietyWGData.Instance:GetTeamMustCheck()
	self.node_list["img_select1"]:SetActive(must_check == 0)
end

function NewTeamApplyView:DeleteReq(role_id)
	if nil == role_id then
		return
	end
	SocietyWGData.Instance:RemoveTeamJoinReq(role_id)
	self:Flush()

	if SocietyWGData.Instance:GetReqTeamListSize() <= 0 then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TEAM_APPLY, 0)
		ViewManager.Instance:FlushView(GuideModuleName.NewTeamView)
		MainuiWGCtrl.Instance:TeamCellFlush()
		return--self:Close()
	end
end


------------------itemRender-----------------
NewTeamApplyListItem = NewTeamApplyListItem or BaseClass(BaseRender)

function NewTeamApplyListItem:__init()
	self:CreateChild()
	XUI.AddClickEventListener(self.node_list["head_click"], BindTool.Bind1(self.OnClickHead, self))
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function NewTeamApplyListItem:__delete()
	if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function NewTeamApplyListItem:CreateChild()
	self.node_list["btn_refuse"].button:AddClickListener(function()
		local role_id = self.data.req_role_id
		local send_protocol = ProtocolPool.Instance:GetProtocol(CSReqJoinTeamRet)
		send_protocol.req_role_id = role_id
		send_protocol.result = 1
		send_protocol:EncodeAndSend()

		if NewTeamWGCtrl.Instance:GetTodayCheckActive() then
			NewTeamWGCtrl.Instance:SendNoLongerOperateReq(LOGIN_NO_LONGER_TYPE.LOGIN_NO_LONGER_TYPE_JOIN_MY_TEAM, role_id, 0, NO_LONGER_RECORD_TYPE.LOGIN)
		end

		NewTeamWGCtrl.Instance:RemoveTeamJoinReq(role_id)
	end)

	self.node_list["btn_agree"].button:AddClickListener(function()
		local role_id = self.data.req_role_id
		if SocietyWGData.Instance:GetTargetIsTeamMember(role_id) then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.TargetAlreadyInTeam)
			return
		end
		local send_protocol = ProtocolPool.Instance:GetProtocol(CSReqJoinTeamRet)
		send_protocol.req_role_id = role_id
		send_protocol.result = 0
		send_protocol:EncodeAndSend()

		NewTeamWGCtrl.Instance:RemoveTeamJoinReq(role_id)
	end)
end

function NewTeamApplyListItem:OnFlush()
	-- local str = string.format(Language.NewTeam.ApplyViewRoleName, self.data.req_role_name, self.data.req_role_level)
	-- EmojiTextUtil.ParseRichText(self.node_list["lbl_role_name"].emoji_text, str, 20, COLOR3B.DEFAULT)

	self.node_list.lbl_role_name.text.text = self.data.req_role_name
	local level_str = string.format(Language.NewTeam.PTLevel3, self.data.req_role_level)
	EmojiTextUtil.ParseRichText(self.node_list["lbl_req_role_level"].emoji_text, level_str, 22, COLOR3B.WHITE)

	self.node_list.power_right.text.text = "" --self.data.req_role_capability

	--头像框
	local data = {}
	data.role_id = self.data.req_role_id
	data.prof = self.data.req_role_prof
	data.sex = self.data.req_role_sex
	data.fashion_photoframe = self.data.req_role_photoframe
	self.head_cell:SetImgBg(false)
	self.head_cell:SetData(data)
	-- 新增等级和关系
	local relation_flag = bit:d2b_two(self.data.req_role_relation_flag or 0)
	local index = -1
	for k,v in pairs(relation_flag) do
		if v == 1 then
			index = k
			break
		end
	end
	local relation_str = Language.NewTeam.TeamMateRelation[index + 2]
	if index + 2 == 1 then
		relation_str = ToColorStr(relation_str, COLOR3B.DEFAULT)
	end
	self.node_list.lbl_relationship.text.text = relation_str

	-- if self.data.req_role_vip_level and self.data.req_role_vip_level > 0 then
	-- 	self.node_list["vip_level"]:SetActive(true)
	-- 	local a,b = ResPath.GetVipIcon("vip"..self.data.req_role_vip_level)
	-- 	self.node_list["vip_level"].image:LoadSprite(a,b,function ()
	-- 		self.node_list.vip_level.image:SetNativeSize()
	-- 	end)
	-- else
	-- 	self.node_list["vip_level"]:SetActive(false)
 --    end
end

function NewTeamApplyListItem:OnClickInvite()

end

function NewTeamApplyListItem:OnClickHead()
	local role_id = self.data.req_role_id
	BrowseWGCtrl.Instance:BrowRoelInfo(role_id, function(param_protocol)
		if self.view and self.view.gameObject and self.view.gameObject.activeInHierarchy == true then
			NewTeamWGCtrl.Instance:CreateInviteRoleHeadCell(param_protocol.role_id, self.data.req_role_name,param_protocol.prof,param_protocol.is_online, self.node_list.head_click, param_protocol.plat_type, param_protocol.server_id, param_protocol.plat_name)
		end
	end)
end
