require("game/lucky_gift_local_bag/lucky_gift_bag_local_view")
require("game/lucky_gift_local_bag/lucky_gift_bag_local_record_view")
require("game/lucky_gift_local_bag/lucky_gift_bag_local_getreward_view")
require("game/lucky_gift_local_bag/lucky_gift_bag_local_comfirm_view")
require("game/lucky_gift_local_bag/lucky_gift_bag_local_rank_view")
require("game/lucky_gift_local_bag/send_lucky_local_value_view")
require("game/lucky_gift_local_bag/lucky_gift_bag_local_superdouble_view")
require("game/lucky_gift_local_bag/lucky_gift_bag_local_wg_data")
require("game/lucky_gift_local_bag/lucky_gift_bag_local_throughtrain_view")
require("game/lucky_gift_local_bag/lucky_gift_bag_spine")


LuckyGiftBagLoaclWgCtrl = LuckyGiftBagLoaclWgCtrl or BaseClass(BaseWGCtrl)

function LuckyGiftBagLoaclWgCtrl:__init()
	if LuckyGiftBagLoaclWgCtrl.Instance then
		error("[LuckyGiftBagLoaclWgCtrl]:Attempt to create singleton twice!")
	end
	LuckyGiftBagLoaclWgCtrl.Instance = self

	self.data = LuckyGiftBagLocalWgData.New()
	self.view = LuckyGiftBagLocalView.New(GuideModuleName.LuckyGiftBagLocalView)
	self.get_reward_view = LuckyGiftBagLocalGetRewardView.New()
	self.comfirm_view = LuckyGiftBagLocalComfirmView.New()
	self.record_view = LuckyGiftBagLocalRecordView.New()
	self.rank_view = LuckyGiftBagLocalRankView.New()
	self.send_lucky_value_view = SendLuckyLocalValueView.New()
	self.through_train_view = LuckyGiftBagLocalThroughTrainView.New(GuideModuleName.LuckyGiftBagLocalThroughTrainView)
	self.superdouble_view = LuckyGiftBagLocalSuperDoubleView.New()
	self.spine_view = LuckyGiftBagSpine.New()

	self:RegisterAllProtocals()
end

function LuckyGiftBagLoaclWgCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil	
	end

	LuckyGiftBagLoaclWgCtrl.Instance = nil

	if self.record_view then
		self.record_view:DeleteMe()
		self.record_view = nil
	end

	if self.get_reward_view then
		self.get_reward_view:DeleteMe()
		self.get_reward_view = nil
	end

	if self.comfirm_view then
		self.comfirm_view:DeleteMe()
		self.comfirm_view = nil
	end

	if self.rank_view then
		self.rank_view:DeleteMe()
		self.rank_view = nil
	end

	if self.send_lucky_value_view then
		self.send_lucky_value_view:DeleteMe()
		self.send_lucky_value_view = nil
	end

	if self.through_train_view then
		self.through_train_view:DeleteMe()
		self.through_train_view = nil
	end

	if self.superdouble_view then
		self.superdouble_view:DeleteMe()
		self.superdouble_view = nil
	end

	if self.spine_view then
		self.spine_view:DeleteMe()
		self.spine_view = nil
	end
end

function LuckyGiftBagLoaclWgCtrl:RegisterAllProtocals()
	self:RegisterProtocol(SCCrossLuckyGiftLocalBaseInfo, "OnSCCrossLuckyGiftLocalBaseInfo")
	-- self:RegisterProtocol(SCCrossLuckyGiftRateRecordAdd, "OnSCCrossLuckyGiftRateRecordAdd")
	self:RegisterProtocol(SCCrossLuckyGiftLocalBuyResult, "OnSCCrossLuckyGiftLocalBuyResult")
	self:RegisterProtocol(SCCrossLuckyGiftLocalInfo, "OnSCCrossLuckyGiftLocalInfo")
	self:RegisterProtocol(SCCrossLuckyGiftLocalRateRecordInfo, "OnSCCrossLuckyGiftLocalRateRecordInfo")
	self:RegisterProtocol(SCCrossLuckyGiftLocalRankInfo, "OnSCCrossLuckyGiftLocalRankInfo")
end

-- 跨服幸运大礼包请求
function LuckyGiftBagLoaclWgCtrl:SendLuckyGiftBagReq(opera_type,param_1,param_2,param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
	protocol.rand_activity_type = ACTIVITY_TYPE.CROSS_ACTIVITY_LUCKYGIFTBAGLOCAL
	protocol.opera_type = opera_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

-- 活动信息
function LuckyGiftBagLoaclWgCtrl:OnSCCrossLuckyGiftLocalBaseInfo(protocol)
	self.data:SetLuckyGiftBaseInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

-- 单个大奖增加
function LuckyGiftBagLoaclWgCtrl:OnSCCrossLuckyGiftRateRecordAdd(protocol)
	LuckyGiftBagLocalWgData.Instance:AddRateRecordListItem(protocol)
	if self.record_view:IsOpen() then
		self.record_view:Flush()
	end
end

-- 购买结果返回
function LuckyGiftBagLoaclWgCtrl:OnSCCrossLuckyGiftLocalBuyResult(protocol)
	LuckyGiftBagLocalWgData.Instance:SetResultInfo(protocol)
	if self.comfirm_view:IsOpen() then
		self.comfirm_view:Close()
	end

	local data = LuckyGiftBagLocalWgData.Instance:GetBuyResultInfo()
	local again_func = function ()
		self:BugLuckyGiftBagReq(LuckyGiftBagLocalWgData.Instance:GetBeforePrchaseMode())
	end

	local grade, seq = LuckyGiftBagLocalWgData.Instance:GetCurrentSelectInfo()
	local data_list = LuckyGiftBagLocalWgData.Instance:GetGradeCfgInfo(grade, seq)
	local prchase_mode = LuckyGiftBagLocalWgData.Instance:GetBeforePrchaseMode()

	local other_info = {}
	other_info.again_text = string.format(Language.LuckyGiftBag.BugAgainBtn ,data_list[prchase_mode][1].times)
	other_info.stuff_id = COMMON_CONSTS.VIRTUAL_ITEM_GOLD
	other_info.times = data_list[prchase_mode][1].times
	other_info.spend = data_list[prchase_mode][1].need_cost / data_list[prchase_mode][1].times
	other_info.total_price_desc1 = Language.LuckyGiftBag.TotalPriceDesc

	TipWGCtrl.Instance:ShowGetValueReward(data.item_list, again_func, other_info, true)
end

-- 幸运值
function LuckyGiftBagLoaclWgCtrl:OnSCCrossLuckyGiftLocalInfo(protocol)
	local old_daily_reward_flag = LuckyGiftBagLocalWgData.Instance:GetDailyRewardFlag()
	self:ShowGetLuckyValue(protocol)
	self.data:SetLuckyGiftInfo(protocol)

	--每日奖励内容后端不下发，当状态由可领取编成已领取时候打开展示界面
	if old_daily_reward_flag == 0 and protocol.daily_reward_flag == 1 then
		LuckyGiftBagLocalWgData.Instance:SetDailyRewardInfo()
		local data = LuckyGiftBagLocalWgData.Instance:GetBuyResultInfo()
		TipWGCtrl.Instance:ShowGetReward(nil, data.item_list, false, nil, nil, false)
	end

	if self.view:IsOpen() then
		self.view:Flush()
		-- self.view:Flush(0,"LuckyValue")
	end

	if self.through_train_view:IsOpen() then
		self.through_train_view:Flush()
	end

	if self.superdouble_view:IsOpen() then
		self.superdouble_view:Flush()
	end

	if self.send_lucky_value_view:IsOpen() then
		self.send_lucky_value_view:Flush()
	end

	RemindManager.Instance:Fire(RemindName.LuckyGiftBagLocalDailyReward)
end

function LuckyGiftBagLoaclWgCtrl:ShowGetLuckyValue(protocol)
	local new_value = protocol.lucky_value or 0

	if new_value > 0 then
		local old_value = self.data:GetLickyValue()

		if old_value ~= new_value then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.LuckyGiftBag.LuckyValueAdd, new_value - old_value))
		end
	end
end

-- 大奖记录
function LuckyGiftBagLoaclWgCtrl:OnSCCrossLuckyGiftLocalRateRecordInfo(protocol)
	LuckyGiftBagLocalWgData.Instance:SetRateRecordListItem(protocol)
	if self.record_view:IsOpen() then
		self.record_view:Flush()
	end
end

function LuckyGiftBagLoaclWgCtrl:OpenComfirmView(grade, seq, gift_name)
	LuckyGiftBagLocalWgData.Instance:SetCurrentSelectInfo(grade, seq, gift_name)
	if not self.comfirm_view:IsOpen() then
		self.comfirm_view:Open()
	else
		self.comfirm_view:Flush()
	end
end

function LuckyGiftBagLoaclWgCtrl:OpenLuckyGiftBagRecordView()
	if not self.record_view:IsOpen() then
		self.record_view:Open()
	end
end

function LuckyGiftBagLoaclWgCtrl:BugLuckyGiftBagReq(prchase_mode)
	local grade, seq = LuckyGiftBagLocalWgData.Instance:GetCurrentSelectInfo()
    local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
    local grade_cfg = LuckyGiftBagLocalWgData.Instance:GetGradeCfgInfo(grade, seq)

    if not IsEmptyTable(grade_cfg) and grade_cfg[prchase_mode] and grade_cfg[prchase_mode][1] then
        local grade_data = grade_cfg[prchase_mode][1]
        local cost_value = grade_data and grade_data.need_cost or 0
        if role_gold >= cost_value then
			LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_BUY, seq, prchase_mode)
        else
            VipWGCtrl.Instance:OpenTipNoGold()
        end
    end	
end

function LuckyGiftBagLoaclWgCtrl:BugLuckyGiftBagReq2(prchase_mode)
	local grade, seq = LuckyGiftBagLocalWgData.Instance:GetCurrentSelectInfo()
    local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
    local grade_cfg = LuckyGiftBagLocalWgData.Instance:GetGradeCfgInfo(grade, seq)

	if self.comfirm_view:IsOpen() then
		self.comfirm_view:Close()
	end

    if not IsEmptyTable(grade_cfg) and grade_cfg[prchase_mode] and grade_cfg[prchase_mode][1] then
        local grade_data = grade_cfg[prchase_mode][1]
        local cost_value = grade_data and grade_data.need_cost or 0
        if role_gold >= cost_value then
			local is_skip = LuckyGiftBagLocalWgData.Instance:GetSkipSpineStatus()
			if is_skip then
				LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_BUY, seq, prchase_mode)
			else
				self.view:SetEffectEndCallBack(function()
					LuckyGiftBagLoaclWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_BUY, seq, prchase_mode)
				end)
				self.view:PlayDrawSpineEffect(prchase_mode)
			end
        else
            VipWGCtrl.Instance:OpenTipNoGold()
        end
    end	
end

function LuckyGiftBagLoaclWgCtrl:OnSCCrossLuckyGiftLocalRankInfo(protocol)
	LuckyGiftBagLocalWgData.Instance:SetRankData(protocol)
	if self.rank_view:IsOpen() then
		self.rank_view:Flush()
	end
end

function LuckyGiftBagLoaclWgCtrl:OpenRankView()
	if not self.rank_view:IsOpen() then
		self.rank_view:Open()
	end
end

function LuckyGiftBagLoaclWgCtrl:OpenSendLuckyValueView()
	if not self.send_lucky_value_view:IsOpen() then
		self.send_lucky_value_view:Open()
	end
end

function LuckyGiftBagLoaclWgCtrl:OpenThroughTrainView()
	if not self.through_train_view:IsOpen() then
		self.through_train_view:Open()
	end
end

function LuckyGiftBagLoaclWgCtrl:OpenSuperDoubleView()
	if not self.superdouble_view:IsOpen() then
		self.superdouble_view:Open()
	end
end