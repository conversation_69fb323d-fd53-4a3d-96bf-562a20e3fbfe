UltimateBattleTalentSelectView = UltimateBattleTalentSelectView or BaseClass(SafeBaseView)

function UltimateBattleTalentSelectView:__init(view_name)
	self:AddViewResource(0, "uis/view/country_map_ui/ultimate_battlefield_prefab", "layout_ultimate_talent_select")
	self:SetMaskBg(false, true)
end

function UltimateBattleTalentSelectView:LoadCallBack()
	if not self.list_view then
        self.list_view = AsyncListView.New(TalentSelectRender, self.node_list.list_view)
		self.list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectTalentCB, self))
    end
end

function UltimateBattleTalentSelectView:ReleaseCallBack()
    if self.list_view then
        self.list_view:DeleteMe()
        self.list_view = nil
    end

	self.data_list = nil
	self:CleanTimeDown()
end

function UltimateBattleTalentSelectView:CloseCallBack()
	self:CleanTimeDown()
end

-- 选择回调
function UltimateBattleTalentSelectView:OnSelectTalentCB(cell, cell_index, is_default, is_click)
	if nil == cell or nil == cell.data then
		return
	end

	if self.talent_select == nil and is_click then
		self.talent_select = cell.data
		UltimateBattlefieldWGCtrl.Instance:RequestChooseTalent(self.talent_select)	-- 服务器bit位从0开始
		self:Close()
	end
end

function UltimateBattleTalentSelectView:OpenCallBack()
	self.talent_select = nil
end

function UltimateBattleTalentSelectView:OnFlush()
	-- 刷洗天赋
	local temp_data_list = UltimateBattlefieldWGData.Instance:GetPlayerRandomTalentList()

	if not self.data_list then
		self.data_list = temp_data_list
	else
		if temp_data_list then
			-- 先取到不同的下标
			local not_same_index = 0			
			for index, data in ipairs(self.data_list) do
				if self:CheckSameSeqGetSeq(data, temp_data_list) == 0 then
					not_same_index = index
				end
			end

			-- 再取到新的不同的下标
			local new_not_same_index = 0
			for index, data in ipairs(temp_data_list) do
				if self:CheckSameSeqGetSeq(data, self.data_list) == 0 then
					new_not_same_index = index
				end
			end

			if self.data_list[not_same_index] and temp_data_list[new_not_same_index] then
				self.data_list[not_same_index] = temp_data_list[new_not_same_index]
			end
		end
	end

	self.list_view:SetDataList(self.data_list)
	
	-- 刷新倒计时
	local act = ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE or nil
	local act_status = ActivityWGData.Instance:GetActivityStatuByType(act)
	local end_time = act_status.end_time or 0
	self:FlushTimeCountDown(end_time)
end

-- 列表更新
function UltimateBattleTalentSelectView:CheckSameSeqGetSeq(data, list)
	if not list then
		return 0
	end

	for index, value in ipairs(list) do
		if value == data then
			return index
		end
	end

	return 0
end


-----------------活动时间倒计时-------------------
function UltimateBattleTalentSelectView:CleanTimeDown()
	if CountDownManager.Instance:HasCountDown("uitimate_battle_talent_select_down") then
		CountDownManager.Instance:RemoveCountDown("uitimate_battle_talent_select_down")
	end
end

function UltimateBattleTalentSelectView:FlushTimeCountDown(end_time)
	local invalid_time = end_time
	if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
		self:CleanTimeDown()
		local str = TimeUtil.FormatSecondDHM6(invalid_time - TimeWGCtrl.Instance:GetServerTime())
		str = ToColorStr(str, COLOR3B.GREEN)
		self.node_list.time_down_txt.text.text = string.format(Language.UltimateBattlefield.RefreshTalentSelectTime, str) 
		CountDownManager.Instance:AddCountDown("uitimate_battle_talent_select_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
	else
		self:CleanTimeDown()
	end
end

function UltimateBattleTalentSelectView:UpdateCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 then
		local str = TimeUtil.FormatSecondDHM6(valid_time)
		str = ToColorStr(str, COLOR3B.GREEN)
		self.node_list.time_down_txt.text.text = string.format(Language.UltimateBattlefield.RefreshTalentSelectTime, str) 
	end
end

function UltimateBattleTalentSelectView:OnComplete()
    self.node_list.time_down_txt.text.text = ""
	self:Close()
end

----------------------------------------------------------------
TalentSelectRender = TalentSelectRender or BaseClass(BaseRender)

function TalentSelectRender:LoadCallBack()
	if self.node_list["talent_refresh_btn"] then
		XUI.AddClickEventListener(self.node_list["talent_refresh_btn"], BindTool.Bind(self.RefreshOnClick, self))
	end

	if self.node_list["talent_skill"] then
		XUI.AddClickEventListener(self.node_list["talent_skill"], BindTool.Bind(self.SkillOnClick, self))
	end
end

-- 刷新天赋
function TalentSelectRender:RefreshOnClick()
    if self.data == nil then 
		return 
	end

	local seq = -1
	if type(self.data) == "number" then
		seq = self.data
	else
		seq = self.data.seq
	end

	if seq == -1 then
		return
	end

	-- 发送协议
	local cur_refresh_times = UltimateBattlefieldWGData.Instance:GetPlayerTalentRefreshTimes()
	local right_number = UltimateBattlefieldWGData.Instance:GetQuestionRightNumber()
	local cfg = UltimateBattlefieldWGData.Instance:GetTalentRefreshCfgBySeq(right_number)
    local refresh_times = cfg and cfg.refresh_times or 0
	local last_refresh_time = refresh_times - cur_refresh_times

	if last_refresh_time > 0 then
		UltimateBattlefieldWGCtrl.Instance:RequestRefreshTalent(seq)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.UltimateBattlefield.RefreshTalentTimeNotEnough)
	end
end

-- 展示技能
function TalentSelectRender:SkillOnClick()
    if self.data == nil then 
		return 
	end

	local seq = -1
	if type(self.data) == "number" then
		seq = self.data
	else
		seq = self.data.seq
	end

	if seq == -1 then
		return
	end
	


end

function TalentSelectRender:OnFlush()
    if self.data == nil then 
		return 
	end

	local seq = -1
	if type(self.data) == "number" then
		seq = self.data
	else
		seq = self.data.seq
	end

	if seq == -1 then
		return
	end

	local talent_cfg = UltimateBattlefieldWGData.Instance:GetTalentCfgBySeq(seq)

    if not talent_cfg then 
		return 
	end

	self.node_list["talent_name"].text.text = talent_cfg.name
	local bundle, asset = ResPath.GetCountryUltimateImg(string.format("a2_zjzc_ucon%d", seq))
	self.node_list.talent_icon.image:LoadSprite(bundle, asset)
	self.node_list["desc_text"].text.text = talent_cfg.desc
    
	local cur_refresh_times = UltimateBattlefieldWGData.Instance:GetPlayerTalentRefreshTimes()
	local right_number = UltimateBattlefieldWGData.Instance:GetQuestionRightNumber()
	local cfg = UltimateBattlefieldWGData.Instance:GetTalentRefreshCfgBySeq(right_number)
    local refresh_times = cfg and cfg.refresh_times or 0
	local last_refresh_time = refresh_times - cur_refresh_times
	local color = last_refresh_time > 0 and COLOR3B.GREEN or COLOR3B.RED

	local str = string.format("%d/%d", last_refresh_time, 1)
	if self.node_list["talent_refresh_times"] then
		self.node_list["talent_refresh_times"].text.text = string.format(Language.UltimateBattlefield.LastHaveRefreshTimes, ToColorStr(str, color))
	end

	self.node_list["bg"].raw_image:LoadSprite(ResPath.GetRawImagesPNG(string.format("a2_zjzc_tfxz_bg%d", talent_cfg.talent_type)))
	self.node_list["talent_skill"]:CustomSetActive(talent_cfg.skill_id ~= 0)--(self.data.skill_id and self.data.skill_id ~= 0)

	if talent_cfg.skill_id ~= 0 then
		local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(talent_cfg.skill_id, 1)
		if skill_cfg and skill_cfg.icon_resource then
			self.node_list.talent_skill_icon.image:LoadSprite(ResPath.GetSkillIconById(skill_cfg.icon_resource))
		end
	end
end