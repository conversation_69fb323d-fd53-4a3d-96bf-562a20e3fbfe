--DIY1抽奖概率展示面板[已废弃]
DIYOneProbabilityView = DIYOneProbabilityView or BaseClass(SafeBaseView)

function DIYOneProbabilityView:__init()
    self.view_layer = UiLayer.Normal
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(866, 516)})
    self:AddViewResource(0, "uis/view/diy_draw_ui_prefab", "diydraw_probability")
    self:SetMaskBg(true, true)
end

function DIYOneProbabilityView:ReleaseCallBack()
    if self.probability_list then
        self.probability_list:DeleteMe()
        self.probability_list = nil
    end
end

function DIYOneProbabilityView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.RebateGiftAct.ProbabilityTitle
     if not self.probability_list then
        self.probability_list = AsyncListView.New(DIYOneGailvItemRender, self.node_list.ph_pro_list) 
    end
end

function DIYOneProbabilityView:OnFlush()
    local info = DIYDrawWGData.Instance:GetDrawOneProbabilityInfo()
    if info then
        self.probability_list:SetDataList(info)
    end
end

----------------------------------------------------------------------------------
DIYOneGailvItemRender = DIYOneGailvItemRender or BaseClass(BaseRender)
function DIYOneGailvItemRender:__delete()
    
end

function DIYOneGailvItemRender:LoadCallBack()
    
end

function DIYOneGailvItemRender:OnFlush()
    if not self.data then
        return
    end
    self.node_list.bg:SetActive(self.index % 2 == 1)
    self.node_list.index_text.text.text = self.data.number
    local color = ItemWGData.Instance:GetItemColor(self.data.item_id)
    --local item_name = ItemWGData.Instance:GetItemName(self.data.item_id)
    local item_name = self.data.item_name
    self.node_list.name_text.text.text = ToColorStr(item_name, color) 
    self.node_list.probability_text.text.text = self.data.random_count .. "%"
end
