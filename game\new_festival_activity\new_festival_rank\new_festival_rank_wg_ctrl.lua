require("game/new_festival_activity/new_festival_rank/new_festival_rank_view")
require("game/new_festival_activity/new_festival_rank/new_festival_rank_wg_data")

NewFestivalRankWGCtrl = NewFestivalRankWGCtrl or BaseClass(BaseWGCtrl)
function NewFestivalRankWGCtrl:__init()
	if NewFestivalRankWGCtrl.Instance then
		ErrorLog("[NewFestivalRankWGCtrl] Attemp to create a singleton twice !")
	end
	NewFestivalRankWGCtrl.Instance = self

	self.data = NewFestivalRankWGData.New()
	self:RegisterAllProtocols()
end

function NewFestivalRankWGCtrl:__delete()
	NewFestivalRankWGCtrl.Instance = nil

	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end
end

function NewFestivalRankWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCCrossNewFestivalRankInfo, "OnSCCrossNewFestivalRankInfo")
end

function NewFestivalRankWGCtrl:OnSCCrossNewFestivalRankInfo(protocol)
	self.data:SetNewFesRankData(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.NewFestivalActivityView, TabIndex.new_festival_activity_5009)
end

function NewFestivalRankWGCtrl:CSReqNewFesRankInfo(opera_type, param_1, param_2, param_3)
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.NEW_JRHD_JRPH
	param_t.opera_type = opera_type or 0
	param_t.param_1 = param_1 or 0
	param_t.param_2 = param_2 or 0
	param_t.param_3 = param_3 or 0
	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end
