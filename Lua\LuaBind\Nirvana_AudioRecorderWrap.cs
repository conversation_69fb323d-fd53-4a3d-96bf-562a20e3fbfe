﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class Nirvana_AudioRecorderWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("AudioRecorder");
		<PERSON><PERSON>Function("Start", Start);
		<PERSON><PERSON>Function("Stop", Stop);
		<PERSON><PERSON>unction("StartRecorder", StartRecorder);
		<PERSON><PERSON>unction("StopRecorder", StopRecorder);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Start(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				bool o = Nirvana.AudioRecorder.Start();
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 1)
			{
				System.Action<bool,int,string> arg0 = (System.Action<bool,int,string>)ToLua.CheckDelegate<System.Action<bool,int,string>>(L, 1);
				bool o = Nirvana.AudioRecorder.Start(arg0);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.AudioRecorder.Start");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Stop(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			string o = Nirvana.AudioRecorder.Stop();
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StartRecorder(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				bool o = Nirvana.AudioRecorder.StartRecorder();
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 1)
			{
				System.Action<bool,int,string> arg0 = (System.Action<bool,int,string>)ToLua.CheckDelegate<System.Action<bool,int,string>>(L, 1);
				bool o = Nirvana.AudioRecorder.StartRecorder(arg0);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: Nirvana.AudioRecorder.StartRecorder");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopRecorder(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			Nirvana.AudioRecorder.StopRecorder();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

