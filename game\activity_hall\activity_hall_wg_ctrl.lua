require("game/activity_hall/activity_hall_wg_data")
require("game/activity_hall/activity_hall_min_panel")

-- 活动大厅控制器
ActIvityHallWGCtrl = ActIvityHallWGCtrl or BaseClass(BaseWGCtrl)

function ActIvityHallWGCtrl:__init()
	if nil ~= ActIvityHallWGCtrl.Instance then
		print("[ActIvityHallWGCtrl] attempt to create singleton twice!")
		return
	end

	ActIvityHallWGCtrl.Instance = self
	self.data = ActIvityHallWGData.New()
	self.min_panel = ActIvityHallMinPanel.New()
end

function ActIvityHallWGCtrl:__delete()

	if nil ~= self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	if self.min_panel then
		self.min_panel:DeleteMe()
		self.min_panel = nil
	end

	ActIvityHallWGCtrl.Instance = nil
end

function ActIvityHallWGCtrl:Open()

end

function ActIvityHallWGCtrl:OpenMinPanel(type)
	self.min_panel:Open()
	self.min_panel:SetPanelData(type)

end

function ActIvityHallWGCtrl:CloseMinPanel()
	self.min_panel:Close()
end

function ActIvityHallWGCtrl:OpenActivityHall()

end

function ActIvityHallWGCtrl:OpenActivity(activity_type)
	local data = {}
	local hall_cfg = ActIvityHallWGData.Instance:GetActivityHallCfg()
	for k,v in pairs(hall_cfg) do
		if v.act_type == activity_type then
			data = v
			break
		end
	end

	local hall_cfg_two = BiZuoWGData.Instance:GetActivityHallCfgTwo()
	for k,v in pairs(hall_cfg_two) do
		if v.act_type == activity_type then
			data = v
			break
		end
	end
	self:EnterActivity(data)
end

function ActIvityHallWGCtrl:EnterActivity(data)
	if nil == data then
		return
	end

	local act_type = data.act_type
	local role_level = RoleWGData.Instance.role_vo.level
	local cfg = ActIvityHallWGData.Instance:GetActivityCfgByType(act_type) or {}
	local level = cfg.level or 0
	if role_level < level then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Activity.KuFuLevelTis, level))
		return
	end

	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
	if not ActivityWGData.Instance:GetActivityIsOpen(act_type) and act_cfg and act_cfg.allow_enter ~= 1
		and act_type ~= ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_GUILD_BOSS then --仙盟神兽不考虑活动状态直接打开界面
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)
		return
	end

	if data.open_type == 1 then
		FunOpen.Instance:OpenViewNameByCfg(data.open_panel_name)

	elseif data.open_type == 2 then
		if data.fun_name then
			-- 103#155#179||104#15#179||105#155#17
			-- 地图ID X坐标 Y坐标
			local list = Split(data.fun_name, "||")		--多个随机坐标
			local random = math.random(1, #list)
			local cfg = Split(list[1], "#")
			if #list > 1 then
				cfg = Split(list[random], "#")
			end
			if  Scene.Instance:GetSceneId() ~= 103 or  act_type == ACTIVITY_TYPE.MAMMONBLESS then
				local pos_cfg = ActIvityHallWGData.Instance:GetCaiShenCiFuPos()
				Scene.Instance:GetSceneLogic():FlyToPos(pos_cfg.man_pos_x , pos_cfg.man_pos_y, tonumber(cfg[1]), SceneObjType.Common, false)
			else
				Scene.Instance:GetSceneLogic():SetGuaiJi(GUAI_JI_TYPE.MONSTER)
			end
		end
	elseif data.open_type == 3 then
		-- 由于进入副本写判断的话太长了 改用函数去调用。
		self:JoinFuBenHandler(data)

	elseif data.open_type == 4 then
		if data.fun_name and data.fun_name > 0 then
			if data.act_type == GameEnum.TASK_TYPE_HU then -- 护送
				self:DoHuSong()
			end
		end
	elseif data.open_type == 5 then
		local guaji_cfg = TaskWGData.Instance:GetVirtualGuajiTask()
		if guaji_cfg then
			Scene.Instance:GetSceneLogic():FlyToPos(guaji_cfg.x, guaji_cfg.y, guaji_cfg.scene_id, SceneObjType.Monster, false)
		end
	end
end

function ActIvityHallWGCtrl:CheckDoHuSong()
	if self.need_do_husong then
		ViewManager.Instance:CloseAll()
		self:DoHuSong()
	end
end

function ActIvityHallWGCtrl:DoHuSong()
	if YunbiaoWGData.Instance:GetSurplusTimes() <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.CiShuBuZhu)
		return
	end

	if YunbiaoWGData.Instance:GetIsHuShong() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.HuSongIng)
		return
	end

	--local task_id = ConfigManager.Instance:GetAutoConfig("husongcfg_auto").other[1].task_id
	local data = YunbiaoWGData.Instance:GetYunbiaoScene()--TaskWGData.Instance:GetTaskConfig(task_id).accept_npc
	if data then
		local scene_id = data.scene
		local curr_sence_id = Scene.Instance:GetSceneId()
		local call_back = function ()
			-- YunbiaoWGCtrl.Instance:SetIsClickHuSong(true)
			-- YunbiaoWGCtrl.Instance:OpenWindow()
		end
		local function move_to_pos()
        	MoveCache.SetEndType(MoveEndType.NpcTask)
        	MoveCache.param1 = data.id
			local range = TaskWGData.Instance:GetNPCRange(data.id)
        	GuajiWGCtrl.Instance:MoveToPos(scene_id, data.x, data.y, range,nil,nil,nil,call_back)
        	--GuajiWGCtrl.Instance:SetMoveToPosCallBack(call_back)
   		end

		if curr_sence_id == scene_id then
			move_to_pos()
    	else
        	TaskWGCtrl.Instance:SendFlyByShoe(scene_id, data.x, data.y)
	       	TaskWGCtrl.Instance:AddFlyUpList(move_to_pos)
    	end
	end
end

function ActIvityHallWGCtrl:JoinFuBenHandler(data)
	if nil == data then
		return
	end

	local act_type = data.act_type
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(act_type)
	if ActivityWGData.Instance:GetActivityIsOpen(act_type) or act_cfg.allow_enter == 1 then
		-- ActivityWGData.Instance:OnEnterRoom(act_type)
		if act_type == ACTIVITY_TYPE.ZHUXIE then
			ActivityWGData.Instance:OnEnterRoom(ACTIVITY_TYPE.ZHUXIE)
			-- self:OpenMinPanel(act_type)
		elseif act_type == ACTIVITY_TYPE.KF_ZHUXIE then
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_ZHUXIE)
		elseif act_type == ACTIVITY_TYPE.KF_HOTSPRING then
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_HOTSPRING)
		elseif act_type == ACTIVITY_TYPE.KF_ANSWER then
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_ANSWER)
		elseif act_type == ACTIVITY_TYPE.KF_SHUIJING then
			CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.KF_SHUIJING)
		elseif act_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT then
			EternalNightWGCtrl.Instance:SendCSEternalNightEnter(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT)
		elseif act_type == ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF then
			EternalNightWGCtrl.Instance:SendCSEternalNightEnter(ACTIVITY_TYPE.ACTIVITY_TYPE_ETERNAL_NIGHT_KF)
		else
			ActivityWGCtrl.Instance:SendActivityEnterReq(act_type, 0)
		end
		-- ActivityWGCtrl.Instance:SendActivityEnterReq(act_type, 0)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.HuoDongWeiKaiQi)

	end
end
