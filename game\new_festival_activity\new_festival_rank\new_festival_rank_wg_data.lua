NewFestivalRankWGData = NewFestivalRankWGData or BaseClass()
function NewFestivalRankWGData:__init()
	if NewFestivalRankWGData.Instance then
		ErrorLog("[NewFestivalRankWGData] Attemp to create a singleton twice !")
	end

	self.rank_list = {}
	self.rank_value = 0
	NewFestivalRankWGData.Instance = self
	self:InitCfg()
end

function NewFestivalRankWGData:InitCfg()
	local cfg = ConfigManager.Instance:GetAutoConfig("spring_festival_score_rank_auto")
	self.grade_cfg = cfg.open_day
	self.reward_cfg = ListToMapList(cfg.rank_reward, "grade")
	self.model_cfg = ListToMap(cfg.model_display, "grade")
	self.cur_grade_cfg = ListToMap(cfg.open_day, "grade")
end

function NewFestivalRankWGData:__delete()
	NewFestivalRankWGData.Instance = nil
end

function NewFestivalRankWGData:GetRewardCfg(grade)
	return self.reward_cfg[grade]
end

function NewFestivalRankWGData:GetModelCfg(grade)
	return self.model_cfg[grade]
end

function NewFestivalRankWGData:GetCurGradeCfg(grade)
	return self.cur_grade_cfg[grade]
end

function NewFestivalRankWGData:GetCurGrade()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	for k, v in pairs(self.grade_cfg) do
		if open_day >= v.start_day and open_day <= v.end_day then
			return v.grade
		end
	end

	return 0
end

function NewFestivalRankWGData:SetNewFesRankData(protocol)
	self.rank_value = protocol.rank_value					-- 中国结数量
	self.grade = protocol.grade								-- 当前挡位
	self:SetRankList(protocol.rank_item_list)				-- 排行榜信息
end

function NewFestivalRankWGData:GetMyRankValue()
	return self.rank_value
end

function NewFestivalRankWGData:GetRankList()
	return self.rank_list
end

function NewFestivalRankWGData:SetRankList(protocol_rank_list)
	local cfg = self:GetRewardCfg(self.grade)
	if not cfg then
		return
	end

	local rank_cfg = cfg[#cfg]
	local max_rank = rank_cfg.max_rank
	for i = 1, max_rank do
		local rank_item = {}
		if protocol_rank_list[i] then
			rank_item.no_true_rank = false
			rank_item.index = i
			rank_item.rank_data = protocol_rank_list[i]
		else
			rank_item = self:ExpandRankData(i)
		end

		self.rank_list[i] = rank_item
	end
end

function NewFestivalRankWGData:ExpandRankData(index)
	local rank_item = {}
	rank_item.no_true_rank = true
	rank_item.index = index
	rank_item.rank_data = self:CreatNoRankItemData(index)
	return rank_item
end

function NewFestivalRankWGData:CreatNoRankItemData(nSectionIndex)
	return {rank = nSectionIndex, rank_value = 0}
end