SocietyAddReqView = SocietyAddReqView or BaseClass(SafeBaseView)

function SocietyAddReqView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true,true)
	self.name = "societyAddReqView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(694, 480)})
	self:AddViewResource(0, "uis/view/society_ui_prefab", "layout_trade_req")
end

function SocietyAddReqView:__delete()

end

function SocietyAddReqView:ReleaseCallBack()
	if nil ~= self.friend_list then
		self.friend_list:DeleteMe()
		self.friend_list = nil
	end

	self.list_reqlist = nil
end

function SocietyAddReqView:LoadCallBack()
	--self:SetSecondView(nil, self.node_list["size"])
	self.node_list["title_view_name"].text.text = Language.Society.ApplyFirendsList
	self.list_reqlist = self.node_list["list_reqfriendlist"]
	XUI.AddClickEventListener(self.node_list["btn_refuseall"], BindTool.Bind1(self.OnRefuseAllReq, self))
	XUI.AddClickEventListener(self.node_list["btn_agreeall"], BindTool.Bind1(self.OnAgreeAllReq, self))
	XUI.AddClickEventListener(self.node_list["btn_not_receive"], BindTool.Bind1(self.OnClickNotReceiveBtn, self))
	self:CreateReqFriendList()
end

function SocietyAddReqView:CloseCallBack()
	-- local icon = MainuiWGCtrl.Instance:GetTipIcon(MAINUI_TIP_TYPE.FRIEND)
	-- if nil == icon or not icon:IsVisible() or SocietyWGData.Instance:GetReqFriendListSize() == 0 then
	-- 	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.FRIEND, SocietyWGData.Instance:GetReqFriendListSize(), function ()
	-- 		self:Open()
	-- 	end)
	-- end

	--沒有任何提醒的時候才移除
	if SocietyWGData.Instance:GetReqFriendListSize() <= 0 then
		MainuiWGCtrl.Instance:RemoveTipIconByIconObj(MAINUI_TIP_TYPE.FRIEND)
	end
end

function SocietyAddReqView:OpenCallBack()
end

--btn单击事件绑定
-- function SocietyAddReqView:BtnBindEven(btn_name, OnClickHandler)
-- 	if nil == OnClickHandler then
-- 		Log("绑定单击事件为空")
-- 		return
-- 	end
-- 	if nil ~= btn_name and nil ~= self.node_t_list[btn_name] then
-- 		self.node_t_list[btn_name].node:addClickEventListener(BindTool.Bind1(OnClickHandler, self))
-- 	end
-- end

function SocietyAddReqView:CreateReqFriendList()
	self.friend_list = AsyncListView.New(SocietyReqFriendRender, self.list_reqlist)
	self:Flush()
end

function SocietyAddReqView:ShowIndexCallBack()
	self:FlushNotReceiveBtnState()
end

function SocietyAddReqView:OnFlush()
	local data = SocietyWGData.Instance:GetReqFriendList()
	self.node_list["no_friend_tips"]:SetActive(data == nil or #data <= 0)
	if nil ~= data and nil ~= self.friend_list then
		self.friend_list:SetDataList(data, 0)
	end
end

--全部拒绝
function SocietyAddReqView:OnRefuseAllReq()
	self:OnReplyAllReq(0)
end

--全部同意
function SocietyAddReqView:OnAgreeAllReq()
	self:OnReplyAllReq(1)
end

function SocietyAddReqView:OnReplyAllReq(flag)
	if nil == flag then
		return
	end
	local req_list = SocietyWGData.Instance:GetReqFriendList()
	if nil == req_list then
		return
	end
	for k,v in pairs(req_list) do
		self:OnReplyReq(flag, false, v)
	end
	-- SocietyWGData.Instance:ClearnReqList()
	-- self:Flush()
	self:Close()
end

function SocietyAddReqView:OnReplyReq(flag, is_flush, user_info)
	if nil == user_info or nil == flag or nil == is_flush then
		ErrorLog("OnReplyReq:user_info为空")
		return
	end

	if nil == user_info.user_id or nil == user_info.req_gamename or nil == user_info.req_sex or nil == user_info.req_prof then
		ErrorLog("OnReplyReq:user_info数据缺失")
		return
	end

	SocietyWGCtrl.Instance:SendAddFriendRet(user_info, flag)

	if true == is_flush then
		-- SocietyWGData.Instance:RemoveFdFormReqList(user_info.user_id)
		-- self:Flush()

		if SocietyWGData.Instance:GetReqFriendListSize() - 1 <= 0 then
			self:Close()
		end
	end
end

function SocietyAddReqView:OnClickNotReceiveBtn()
	local flag = SocietyWGData.Instance:GetFriendNotReceiveFlag()
	local change_flag = flag == 1 and 0 or 1
	SocietyWGData.Instance:SetFriendNotReceiveFlag(change_flag)
	--告知服务端变更
	SocietyWGCtrl.Instance:SendFriendNotReceive(change_flag)
	--刷新勾选
	self:FlushNotReceiveBtnState()
end

function SocietyAddReqView:FlushNotReceiveBtnState()
	local flag = SocietyWGData.Instance:GetFriendNotReceiveFlag()
	self.node_list["img_not_receive"]:SetActive(flag == 1)
end