WorldTreasureReceiveEggView = WorldTreasureReceiveEggView or BaseClass(SafeBaseView)

function WorldTreasureReceiveEggView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(0, 0), sizeDelta = Vector2(694, 542)})
	self:AddViewResource(0, "uis/view/world_treasure_ui_prefab", "layout_world_treasure_receive_egg")
end

function WorldTreasureReceiveEggView:LoadCallBack()
    self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
    self.reward_list:SetStartZeroIndex(true)
    self.max_reward_list = AsyncListView.New(ItemCell, self.node_list.max_reward_list)
    self.max_reward_list:SetStartZeroIndex(true)

    XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind(self.Close, self))
    XUI.AddClickEventListener(self.node_list.btn_OK, BindTool.Bind(self.OnClickBtnOk, self))
end

function WorldTreasureReceiveEggView:ReleaseCallBack()
    if self.reward_list then
        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.max_reward_list then
        self.max_reward_list:DeleteMe()
        self.max_reward_list = nil
    end
end

function WorldTreasureReceiveEggView:ShowIndexCallBack()
end

function WorldTreasureReceiveEggView:OnFlush()
    local cur_cfg = WorldTreasureWGData.Instance:GetDragonEggCurLevelCfg()
    local max_cfg = WorldTreasureWGData.Instance:GetDragonEggMaxLevelCfg()
    local quality_text = Language.WorldTreasure.QualityList[cur_cfg.level]
    local name = WorldTreasureWGData.Instance:GetCurGradeName()
    self.reward_list:SetDataList(cur_cfg.reward_item)
    self.max_reward_list:SetDataList(max_cfg.reward_item)

    self.node_list.desc1.text.text = string.format(Language.WorldTreasure.LoginTxt12, name, quality_text)
    self.node_list.desc2.text.text = string.format(Language.WorldTreasure.LoginTxt14)
end

function WorldTreasureReceiveEggView:OnClickBtnOk()
    WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_FETCH_DRAGON_EGG_REWARD)
    self:Close()
end