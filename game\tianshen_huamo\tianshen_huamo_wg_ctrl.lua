require("game/tianshen_huamo/tianshen_huamo_wg_data")
require("game/tianshen_huamo/tianshen_huamo_view")
require("game/tianshen_huamo/tianshen_huamoskill_view")
require("game/tianshen_huamo/tianshen_huamo_tips_view")



TianShenHuamoWGCtrl = TianShenHuamoWGCtrl or BaseClass(BaseWGCtrl)

function TianShenHuamoWGCtrl:__init()
	if TianShenHuamoWGCtrl.Instance then
		print_error("[TianShenHuamoWGCtrl]:Attempt to create singleton twice!")
	end

	TianShenHuamoWGCtrl.Instance = self
    self.data = TianShenHuamoWGData.New()
    self.view = TianShenHuamoView.New(GuideModuleName.TianShenHuamoView)
	self.skill_view = TianShenHuamoSkillView.New()
	self.huamo_tips_view = TianShenHuamoTipsView.New()

    self:RegisterAllProtocols()
    self:RegisterAllEvents()
end

function TianShenHuamoWGCtrl:__delete()
    self:UnRegisterAllEvents()

    if self.data then
        self.data:DeleteMe()
	    self.data = nil
    end

    if self.view then
        self.view:DeleteMe()
	    self.view = nil
    end

    TianShenHuamoWGCtrl.Instance = nil
end

function TianShenHuamoWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(CSTianShenRuMoOpera)
	self:RegisterProtocol(SCTianShenRuMoInfo, "OnSCTianShenRuMoInfo")
end

function TianShenHuamoWGCtrl:RegisterAllEvents()
    self.item_data_change = BindTool.Bind(self.OnItemDataChange, self)
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change, false)
end

function TianShenHuamoWGCtrl:UnRegisterAllEvents()
    if self.item_data_change then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
		self.item_data_change = nil
	end
end

function TianShenHuamoWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE ) then
        self:FlushView()
		self:FlushResetSkill()
		--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_Info, "flush_display")
	end
end

function TianShenHuamoWGCtrl:SendOperateReq(request, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenRuMoOpera)
	protocol.request = request or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or protocol.param_3
	protocol:EncodeAndSend()
end

function TianShenHuamoWGCtrl:SendOperateSkillReq(request, tianshen_id, demon_level, skills)
	local protocol = ProtocolPool.Instance:GetProtocol(CSTianShenRuMoSkillOpera)
	protocol.request = request or 0
	protocol.tianshen_id = tianshen_id or 0
	protocol.demon_level = demon_level or 0
	protocol.skills = skills or {0, 0, 0, 0, 0}
	protocol:EncodeAndSend()
end

---操作结果
function TianShenHuamoWGCtrl:OnOperateResult(result, param_1, param_2, param_3)
	if result == 1 then
		self:FlushView()
		---刷新一下天神界面
		RemindManager.Instance:Fire(RemindName.TianShenHuaMo)
		ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_activation, "flush_display")
		--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_Info, "flush_display")
	end
end

---返回天神化魔的所有信息
function TianShenHuamoWGCtrl:OnSCTianShenRuMoInfo(protocol)
	TianShenHuamoWGData.Instance:SetAllRuMoInfo(protocol)
end

function TianShenHuamoWGCtrl:CSTianShenRuMoAllInfo()
	self:SendOperateReq(TIANSHEN_RUMO_OPERATE_TYPE.TSRM_INFO)
end

function TianShenHuamoWGCtrl:CSTianShenRuMoInfo(tianshen_id)
	self:SendOperateReq(TIANSHEN_RUMO_OPERATE_TYPE.TSRM_INFO, tianshen_id)
end

function TianShenHuamoWGCtrl:CSTianShenRuMoActive(tianshen_id, rumo_id)
	self:SendOperateReq(TIANSHEN_RUMO_OPERATE_TYPE.TSRM_ACTIVE, tianshen_id, rumo_id)
end

function TianShenHuamoWGCtrl:CSTianShenRuMoUpStar(tianshen_id, rumo_id)
	self:SendOperateReq(TIANSHEN_RUMO_OPERATE_TYPE.TSRM_UPGRADE, tianshen_id, rumo_id)	-- id减一对应服务器的0下标位置lua从1开始的减一
end

function TianShenHuamoWGCtrl:CSTianShenRuMoHuanHua(tianshen_id, rumo_id)
	self:SendOperateReq(TIANSHEN_RUMO_OPERATE_TYPE.TSRM_LLLUSION, tianshen_id, rumo_id)
end

function TianShenHuamoWGCtrl:CSTianShenRuMoSkillUp(tianshen_id, demon_level, skill_slot)
	self:SendOperateReq(TIANSHEN_RUMO_OPERATE_TYPE.TSRM_RABBET_UPGRADE, tianshen_id, demon_level, skill_slot)
end

---操作结果
function TianShenHuamoWGCtrl:OnOperateSkillResult(result, param_1, param_2, param_3)
	if result == 1 then
		self:FlushView()
		RemindManager.Instance:Fire(RemindName.TianShenHuaMo)
		--ViewManager.Instance:FlushView(GuideModuleName.TianShenView, TabIndex.tianshen_Info, "flush_display")
	end
end

function TianShenHuamoWGCtrl:CSTianShenRuMoSkillReset(tianshen_id, demon_level)
	self:SendOperateSkillReq(TIANSHEN_RUMO_SKILL_OPERATE_TYPE.TSRM_RESET_SKILL, tianshen_id, demon_level)
end

function TianShenHuamoWGCtrl:CSTianShenRuMoSkillChoose(tianshen_id, demon_level, skills)
	self:SendOperateSkillReq(TIANSHEN_RUMO_SKILL_OPERATE_TYPE.TSRM_CHOESS_SKILL, tianshen_id, demon_level, skills)
end

-- 刷新界面
function TianShenHuamoWGCtrl:FlushView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

-- 打开界面
function TianShenHuamoWGCtrl:OpenView()
	if self.view:IsOpen() then
		self.view:Flush()
	end
    self.view:Open()
end

-- 打开技能选择界面
function TianShenHuamoWGCtrl:OpenSelectSkill(tianshen_id, demon_level)
	self.skill_view:SetTianshenHuaMoData(tianshen_id, demon_level)
	if self.skill_view:IsOpen() then
		self.skill_view:Flush()
	end
	self.skill_view:Open()
end

-- 打开技能重置界面
function TianShenHuamoWGCtrl:OpenResetSkill(reset_data)
	self.huamo_tips_view:SetData(reset_data)
end

-- 打开技能重置界面
function TianShenHuamoWGCtrl:FlushResetSkill()
	if self.huamo_tips_view:IsOpen() then
		self.huamo_tips_view:Flush()
	end
end

--打开技能提示窗口
function TianShenHuamoWGCtrl:OpenSkillLevelUp(skill_data, data, solt, tianshen_id, demon_level)
	local uplevel_data = TianShenHuamoWGData.Instance:GetSkillLevelCfgById(tianshen_id, data.skill_lv + 1)
	local show_data = {
		icon = skill_data.skill_icon,
		top_text = skill_data.skill_name,
		skill_lv = data.skill_lv,
		body_text = "",
		tianshen_index = demon_level,
		skill_id = skill_data.skill_type,
		eff_url = UIEffectName.s_shengji,
		x = 0,
		y = 0,
		set_pos2 = true,
		skill_box_type = SKILL_BOX_TYPE.TIANSHEN_MO,
		is_tianshen_select_view = false,
		solt = solt,
		tianshen_id = tianshen_id,
	}
	local skill_view = NewAppearanceWGCtrl.Instance:GetDisplayBox()
	
	if skill_view:IsOpen() then
		local display_box = NewAppearanceWGCtrl.Instance:GetDisplayBox()
		if display_box then
			display_box:LevelUpCallback(show_data)
		end
	else
		NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
	end
end

--检测技能提示窗口是否开启
function TianShenHuamoWGCtrl:CheckOpenSkillLevelUp()
	local skill_view = NewAppearanceWGCtrl.Instance:GetDisplayBox()
	return skill_view:IsOpen()
end