
--返利活动--连续充值

function RebateActivityView:ReleaseLXRechargeView()
    self.lxcz_slider_reward_cfg = nil
    self.lxcz_recharge_reward_cfg = nil
    if self.lxcz_slider_item_list then
        self.lxcz_slider_item_list:DeleteMe()
        self.lxcz_slider_item_list = nil
    end
    if self.lxcz_reward_item_list then
        self.lxcz_reward_item_list:DeleteMe()
        self.lxcz_reward_item_list = nil
    end

    --模型相关参数
    if self.lxcz_role_model then
        self.lxcz_role_model:DeleteMe()
        self.lxcz_role_model = nil
    end

    if self.lxcz_lingchong_model then
        self.lxcz_lingchong_model:DeleteMe()
        self.lxcz_lingchong_model = nil
    end

    if self.lxcz_xianwa_model then
        self.lxcz_xianwa_model:DeleteMe()
        self.lxcz_xianwa_model = nil
    end
    self.lxcz_cur_btn_type = nil
    self.lxcz_select_suit_seq = nil
    -- self.lxcz_is_foot_view = nil
    -- self.lxcz_foot_effect_id = nil
    -- self.lxcz_use_update = nil
    -- self.lxcz_next_create_footprint_time = nil
    self.lxcz_footprint_eff_t = nil
    self.lxcz_body_res_id = nil
    self.lxcz_mount_res_id = nil
    self.lxcz_mount_action = nil
    self.lxcz_have_foot_print = nil
    self.lxcz_show_role_idel_ani = nil

    self.lxcz_show_model_item_list = nil
    if RebateLianXuRechargeWGData.Instance then
        RebateLianXuRechargeWGData.Instance:CheckNeedCloseTab()
    end

    self.leichong_limit_jump = false

    self.lxcz_list_container = nil
end

function RebateActivityView:InitLXRechargeView()
    self:LXRCInitParam()

    for i = 1, 2 do
        self.node_list["lxcz_recharge_btn_"..i].toggle:AddClickListener(BindTool.Bind(self.LXRCOnClickToggle, self, i))
    end
    self.lxcz_slider_item_list = AsyncListView.New(RebateLXRechargeSliderItem,self.node_list["lxcz_slider_item_list"])
    self.lxcz_reward_item_list = AsyncListView.New(RebateLXRechargeRewardItem,self.node_list["lxcz_reward_item_list"])

    XUI.AddClickEventListener(self.node_list["lxcz_yulan_btn"], BindTool.Bind1(self.LXRCOnClickYuLanBtn, self))
    XUI.AddClickEventListener(self.node_list["lxcz_wenhao_btn"], BindTool.Bind1(self.LXRCOnClickTipBtn, self))

    if nil == self.lxcz_role_model then
        self.lxcz_role_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["lxcz_ph_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        
        self.lxcz_role_model:SetRenderTexUI3DModel(display_data)
        -- self.lxcz_role_model:SetUI3DModel(self.node_list["lxcz_ph_display"].transform,
        --                             self.node_list["lxcz_EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.lxcz_role_model)
    end

    self.leichong_limit_jump = false
end

function RebateActivityView:LXRCInitParam()
    self.lxcz_slider_reward_cfg = {}
    self.lxcz_recharge_reward_cfg = {}
    self.lxcz_cur_btn_type = self.lxcz_cur_btn_type or 1
    self.lxcz_group_index = 1
    RebateLianXuRechargeWGData.Instance:SetCacheBtnType(1)
    self.lxcz_tianshen_index = nil

    self.lxcz_show_model_item_list = {}

    self.lxcz_list_container = nil
end

function RebateActivityView:ShowIndexLXRechargeView()
    self.lxcz_old_group_index = nil
    self.lxcz_old_btn_type = nil
    self.leichong_limit_jump = true
    -- self.node_list["lxcz_recharge_btn_"..self.lxcz_cur_btn_type].toggle.isOn = true
    self:LXRCPlayPanelAnim()
end

function RebateActivityView:LXRCOnClickToggle(btn_type)
    self.lxcz_cur_btn_type = btn_type
    RebateLianXuRechargeWGData.Instance:SetCacheBtnType(btn_type)
    self:FlushLXRechargeView(false)
end

--大标签动画
function RebateActivityView:LXRCPlayPanelAnim()
    self:LXRCDoAnimation()
    self:LXRCDoCellListAnimation()
end

--小标签动画
function RebateActivityView:LXRCPlayToggleAnim()
    UITween.DoUpDownCrashTween(self.node_list["lxcz_title_root"])
end

function RebateActivityView:FlushLXRechargeView(force_flush)
    -- print_error("FFF==== force_flush", force_flush)
    self.lxcz_group_index = RebateLianXuRechargeWGData.Instance:GetCurGroupByRechargeType(self.lxcz_cur_btn_type)
    
    self:LXRCFlushToggleRed()
    self:LXRCResetModelItemList()--刷新前清空一下模型item列表
    self:LXRCTodayRechargeText()

    if self.lxcz_group_index ~= self.lxcz_old_group_index or self.lxcz_cur_btn_type ~= self.lxcz_old_btn_type then
        self.lxcz_old_group_index = self.lxcz_group_index
        self.lxcz_old_btn_type = self.lxcz_cur_btn_type
        self:LXRCFlushRewardList()
        self:LXRCFlushModel()
        self:LXRCPlayToggleAnim()
        --设置完模型再设置战力显示
        self:LXRCSetCapabilityShow()
    elseif force_flush then
        self:LXRCFlushRewardList()
    end
end

function RebateActivityView:LXRCFlushRewardList()
    if self.lxcz_reward_item_list == nil then
        return
    end

    local slider_cfg = RebateLianXuRechargeWGData.Instance:GetSliderRewardCfg(self.lxcz_group_index, self.lxcz_cur_btn_type)
    local all_cfg = RebateLianXuRechargeWGData.Instance:GetRewardCfg(self.lxcz_group_index, self.lxcz_cur_btn_type)
    -- print_error("FFFF========= self.lxcz_group_index", self.lxcz_group_index, slider_cfg, all_cfg)

    --刷新跳转
    if not IsEmptyTable(slider_cfg) then
        self.lxcz_slider_item_list:SetDataList(slider_cfg)
        local slider_jump_index = RebateLianXuRechargeWGData.Instance:TryGetFirstStageRewardDay(self.lxcz_cur_btn_type)
        -- print_error("FFFF======== slider_jump_index", slider_jump_index)
        if slider_jump_index > 0 then
            self.lxcz_slider_item_list:JumpToIndex(slider_jump_index)
        end
    end
    if not IsEmptyTable(all_cfg) then
        self.lxcz_reward_item_list:SetDataList(all_cfg)
        local jump_index = RebateLianXuRechargeWGData.Instance:TryGetFirstNormalRewardDay(self.lxcz_cur_btn_type)
        if jump_index and jump_index > 0 and not self.leichong_limit_jump then
            if not self.lxcz_list_container then
                self.lxcz_list_container = self.node_list.lxcz_reward_item_list:FindObj("Container")
            end
            if self.lxcz_list_container then
                if jump_index > 1 then
                    local container_width = 133 * #all_cfg + 4 * (#all_cfg - 1)
                    local move_x = (133 + 4) * (jump_index - 1)
                    if container_width - move_x > self.node_list.lxcz_reward_item_list.rect.sizeDelta.x then
                        self.lxcz_list_container.rect:DOAnchorPosX(-move_x, 0.5)
                    else
                        self.lxcz_reward_item_list:JumptToPrecent(1)
                    end
                else
                    self.lxcz_reward_item_list:JumptToPrecent(0)
                end
            end
        end
    end
end

function RebateActivityView:LXRCTodayRechargeText()
    local recharge_num = RebateLianXuRechargeWGData.Instance:GetTodayRechargeNum()
    local cur_recharge_day = RebateLianXuRechargeWGData.Instance:GetTotalDayByRechargeType(self.lxcz_cur_btn_type)

    self.node_list["lxcz_text_today_rechage"].text.text = string.format(Language.RebateRecharge.TodayRechargeNum, recharge_num)
    self.node_list["lxcz_text_rechage_day"].text.text = string.format(Language.RebateRecharge.TotalRechargeDay_3, cur_recharge_day)
    self.node_list["lxcz_txt_title_yuan"].text.text = self.lxcz_cur_btn_type == REBATE_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_1 and 6 or 30
    self.node_list["lxcz_txt_title_day"].text.text = string.format(Language.Common.ShowTime2, self.lxcz_group_index * 30)
end

function RebateActivityView:LXRCFlushToggleRed()
    local red_type_1 = RebateLianXuRechargeWGData.Instance:IsShowLianXuRechargeRedByBtnType(REBATE_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_1)
    local red_type_2 = RebateLianXuRechargeWGData.Instance:IsShowLianXuRechargeRedByBtnType(REBATE_LIANXU_RECHARGE_DAY_TYPE.DAY_TYPE_2)
    self.node_list["lxcz_toggle_red_1"]:SetActive(red_type_1 == 1)
    self.node_list["lxcz_toggle_red_2"]:SetActive(red_type_2 == 1)
end

---------------------------------------------------模型设置相关 start

function RebateActivityView:LXRCFlushModel()
    if not self.lxcz_role_model then
        return
    end

    --获取配置数据
    local show_model_param = RebateLianXuRechargeWGData.Instance:GetShowModelParam(self.lxcz_group_index, self.lxcz_cur_btn_type)
    if IsEmptyTable(show_model_param) or not show_model_param.seq then
        return
    end

    --设置标题展示
    if show_model_param.img_res_name and show_model_param.img_res_name ~= "" then
        self.node_list["lxcz_img_item_name"].image:LoadSprite(ResPath.GetRebateActImage(show_model_param.img_res_name))
        self.node_list["lxcz_img_item_name"].image:SetNativeSize()
    end

    local show_list = RebateLianXuRechargeWGData.Instance:GetShowModelList(show_model_param.seq)
    if IsEmptyTable(show_list) then
        return
    end

    --清理掉回调
    -- self:LXRCClearFootEff()
    self.lxcz_role_model:RemoveAllModel()

    self.node_list["lxcz_lc_root"]:SetActive(false)
    self.node_list["lxcz_xw_root"]:SetActive(false)

    -- self.lxcz_is_foot_view = false
    self.lxcz_body_res_id = AppearanceWGData.Instance:GetRoleResId()
    self.lxcz_mount_res_id = 0
    self.lxcz_mount_action = ""
    self.lxcz_have_foot_print = false

    local res_id, fashion_cfg
    for k, data in pairs(show_list) do
        if data.type == WARDROBE_PART_TYPE.FASHION and data.param1 == SHIZHUANG_TYPE.BODY then      -- 时装大类
            fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
            if fashion_cfg then                                                                     -- 时装
                local prof = GameVoManager.Instance:GetMainRoleVo().prof
                self.lxcz_body_res_id = ResPath.GetFashionModelId(prof, fashion_cfg.resouce)
            end
        elseif data.param1 == SHIZHUANG_TYPE.FOOT then                                              -- 足迹
            self.lxcz_have_foot_print = true
        elseif data.type == WARDROBE_PART_TYPE.MOUNT then                                           -- 坐骑
            fashion_cfg = NewAppearanceWGData.Instance:GetMountActCfgByImageId(data.param1)
            if fashion_cfg then
                self.lxcz_mount_res_id = fashion_cfg.appe_image_id
                self.lxcz_mount_action = MOUNT_RIDING_TYPE[1]
                local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.lxcz_mount_res_id)
                if not IsEmptyTable(action_cfg) then
                    self.lxcz_mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
                end
                self:LXRCSetModelItemList(fashion_cfg.active_item_id)
            end
        elseif data.type == WARDROBE_PART_TYPE.HUA_KUN then                                         -- 化鲲
            fashion_cfg = NewAppearanceWGData.Instance:GetKunActCfgById(data.param1)
            if fashion_cfg then
                self.lxcz_mount_res_id = fashion_cfg.active_id
                self.lxcz_mount_action = MOUNT_RIDING_TYPE[1]
                local action_cfg = NewAppearanceWGData.Instance:GetMountActionCfg(self.lxcz_mount_res_id)
                if not IsEmptyTable(action_cfg) then
                    self.lxcz_mount_action = MOUNT_RIDING_TYPE[action_cfg.action]
                end
                self:LXRCSetModelItemList(fashion_cfg.active_need_item_id)
            end
        end
    end

    local extra_role_model_data = {
        no_need_do_anim = true,
    }
    if self.lxcz_mount_res_id > 0 then
        self.lxcz_role_model:SetRoleResid(self.lxcz_body_res_id, nil, extra_role_model_data)
    else
        self.lxcz_role_model:SetRoleResid(self.lxcz_body_res_id, function()
            if self.lxcz_mount_res_id == 0 then
                if self.lxcz_have_foot_print then
                self.lxcz_role_model:PlayRoleAction(SceneObjAnimator.Move)
                else
                    if self.lxcz_show_role_idel_ani then
                        self.lxcz_role_model:PlayRoleShowAction()
                        self.lxcz_show_role_idel_ani = false
                    else
                        self.lxcz_role_model:PlayIdleAni()
                    end
                end
            end
        end, extra_role_model_data)
    end


    if self.lxcz_mount_res_id > 0 then
        self.lxcz_role_model:SetMountResid(self.lxcz_mount_res_id)
        self.lxcz_role_model:PlayStartAction(self.lxcz_mount_action)
    end

    for k, v in pairs(show_list) do
        self:LXRCShowModelByData(v)
    end

    self:LXRCChangeModelShowScale()

end

function RebateActivityView:LXRCShowModelByData(data)
    if IsEmptyTable(data) then
        return
    end

    local res_id, fashion_cfg
    if data.type == WARDROBE_PART_TYPE.FASHION then             -- 时装大类
        fashion_cfg = NewAppearanceWGData.Instance:GetFashionCfgByIndex(data.param1, data.param2)
        if fashion_cfg then
            res_id = fashion_cfg.resouce
            local item_id = NewAppearanceWGData.Instance:GetFashionItemId(data.param1, data.param2)
            self:LXRCSetModelItemList(item_id)
            -- if data.param1 == SHIZHUANG_TYPE.BODY then           -- 时装
            --  local prof = GameVoManager.Instance:GetMainRoleVo().prof
            --  res_id = ResPath.GetFashionModelId(prof, res_id)
            --  self.lxcz_role_model:SetRoleResid(res_id)
            if data.param1 == SHIZHUANG_TYPE.MASK then          -- 脸饰
                self.lxcz_role_model:SetMaskResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.BELT then      -- 腰饰
                self.lxcz_role_model:SetWaistResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.WEIBA then     -- 尾巴
                self.lxcz_role_model:SetTailResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.SHOUHUAN then  -- 手环
                self.lxcz_role_model:SetShouHuanResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.HALO then -- 光环
                self.lxcz_role_model:SetHaloResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.WING then      -- 羽翼
                self.lxcz_role_model:SetWingResid(res_id, true)
            elseif data.param1 == SHIZHUANG_TYPE.FABAO then     -- 法宝
                self.lxcz_role_model:SetBaoJuResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.JIANZHEN then  -- 剑阵
                self.lxcz_role_model:SetJianZhenResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.SHENBING then  -- 武器
                res_id = AppearanceWGData.GetFashionWeaponIdByResViewId(res_id)
                self.lxcz_role_model:SetWeaponResid(res_id)
            elseif data.param1 == SHIZHUANG_TYPE.FOOT then      -- 足迹
                self.lxcz_role_model:SetFootTrailModel(res_id)
                self.lxcz_role_model:SetRotation(MODEL_ROTATION_TYPE.FOOT)
                self.lxcz_role_model:PlayRoleAction(SceneObjAnimator.Move)

                -- self.lxcz_is_foot_view = true
                -- self.lxcz_foot_effect_id = res_id
                -- if not self.lxcz_use_update then
                --     Runner.Instance:AddRunObj(self, 8)
                --     self.lxcz_use_update = true
                -- end
            end
        end
    elseif data.type == WARDROBE_PART_TYPE.LING_CHONG then      -- 灵宠
        fashion_cfg = NewAppearanceWGData.Instance:GetLingChongActCfgByImageId(data.param1)
        if fashion_cfg then
            self:LXRCSetLingChongModelData(WARDROBE_PART_TYPE.LING_CHONG, fashion_cfg.appe_image_id)
            self:LXRCSetModelItemList(fashion_cfg.active_item_id)
        end

    elseif data.type == WARDROBE_PART_TYPE.XIAN_WA then         -- 仙娃
        fashion_cfg = MarryWGData.Instance:GetActiveCfgByTypeAndId(data.param1, data.param2)
        if fashion_cfg then
            self:LXRCSetXianWaModelData(fashion_cfg.appe_image_id)
            self:LXRCSetModelItemList(fashion_cfg.item_id)
        end
    end
end

--设置灵宠模型
function RebateActivityView:LXRCSetLingChongModelData(type, res_id)
    self.node_list["lxcz_lc_root"]:SetActive(true)
    if nil == self.lxcz_lingchong_model then
        self.lxcz_lingchong_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["lxcz_lc_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.lxcz_lingchong_model:SetRenderTexUI3DModel(display_data)
        -- self.lxcz_lingchong_model:SetUI3DModel(self.node_list["lxcz_lc_display"].transform,
        --                             self.node_list["lxcz_lc_EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.lxcz_lingchong_model)
    else
        if self.lxcz_lingchong_model then
            self.lxcz_lingchong_model:ClearModel()
        end
    end

    local bundle, asset
    if type == WARDROBE_PART_TYPE.LING_CHONG then
        bundle, asset = ResPath.GetPetModel(res_id)
    elseif type == WARDROBE_PART_TYPE.XIAN_WA then
        bundle, asset = ResPath.GetHaiZiModel(res_id)
    end

    self.lxcz_lingchong_model:SetMainAsset(bundle, asset, function()
        self.lxcz_lingchong_model:PlaySoulAction()
    end)
    self.lxcz_lingchong_model:FixToOrthographic(self.root_node_transform)
end

--设置仙娃模型
function RebateActivityView:LXRCSetXianWaModelData(res_id)
    self.node_list["lxcz_xw_root"]:SetActive(true)
    if nil == self.lxcz_xianwa_model then
        self.lxcz_xianwa_model = RoleModel.New()
        local display_data = {
            parent_node = self.node_list["lxcz_xw_display"],
            camera_type = MODEL_CAMERA_TYPE.BASE,
            -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
            rt_scale_type = ModelRTSCaleType.S,
            can_drag = false,
        }
        
        self.lxcz_xianwa_model:SetRenderTexUI3DModel(display_data)
        -- self.lxcz_xianwa_model:SetUI3DModel(self.node_list["lxcz_xw_display"].transform,
        --                             self.node_list["lxcz_xw_EventTriggerListener"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
        self:AddUiRoleModel(self.lxcz_xianwa_model)
    else
        if self.lxcz_xianwa_model then
            self.lxcz_xianwa_model:ClearModel()
        end
    end

    local bundle, asset = ResPath.GetHaiZiModel(res_id)
    self.lxcz_xianwa_model:SetMainAsset(bundle, asset, function()
        self.lxcz_xianwa_model:PlaySoulAction()
    end)
    self.lxcz_xianwa_model:FixToOrthographic(self.root_node_transform)
end

-- function RebateActivityView:LXRCUpdate(now_time, elapse_time)
--     if not self.lxcz_is_foot_view then
--         return
--     end

--     if self.lxcz_next_create_footprint_time == 0 then
--         self:LXRCCreateFootPrint()
--         self.lxcz_next_create_footprint_time = Status.NowTime + COMMON_CONSTS.FOOTPRINT_CREATE_GAP_TIME
--     end

--     if self.lxcz_next_create_footprint_time == nil then --初生时也是位置改变，不播
--         self.lxcz_next_create_footprint_time = 0
--     end

--     if self.lxcz_next_create_footprint_time > 0 and now_time >= self.lxcz_next_create_footprint_time then
--         self.lxcz_next_create_footprint_time = 0
--     end

--     self:LXRCUpdateFootprintPos()
-- end

-- function RebateActivityView:LXRCCreateFootPrint()
--     if nil == self.lxcz_foot_effect_id then
--         return
--     end

--     if nil == self.lxcz_footprint_eff_t then
--         self.lxcz_footprint_eff_t = {}
--     end

--     local pos = self.lxcz_role_model.draw_obj:GetRoot().transform
--     local bundle, asset = ResPath.GetUIFootEffect(self.lxcz_foot_effect_id)
--     EffectManager.Instance:PlayControlEffect(self, bundle, asset, Vector3(pos.position.x , pos.position.y, pos.position.z), nil, pos, nil, function(obj)
--         if obj then
--             if nil ~= obj then
--                 if self.lxcz_role_model then
--                     obj.transform.localPosition = Vector3.zero
--                     obj:SetActive(false)
--                     obj:SetActive(true)
--                     table.insert(self.lxcz_footprint_eff_t, {obj = obj, lxcz_role_model = self.lxcz_role_model})
--                     self.lxcz_role_model:OnAddGameobject(obj)
--                 else
--                     ResPoolMgr:Release(obj)
--                 end
--             end
--         end
--     end)

--     if #self.lxcz_footprint_eff_t > 2 then
--         local obj = table.remove(self.lxcz_footprint_eff_t, 1)
--         obj.lxcz_role_model:OnRemoveGameObject(obj.obj)
--         if not IsNil(obj.obj) then
--             obj.obj:SetActive(false)
--         end
--     end
-- end

-- function RebateActivityView:LXRCUpdateFootprintPos()
--     if nil == self.lxcz_footprint_eff_t then
--         return
--     end

--     for k,v in pairs(self.lxcz_footprint_eff_t) do
--         if not IsNil(v.obj) then
--             local pos = v.obj.transform.localPosition
--             v.obj.transform.localPosition = Vector3(pos.x, pos.y, pos.z - 0.16)
--         end
--     end
-- end

-- function RebateActivityView:LXRCClearFootEff()
--     if self.lxcz_footprint_eff_t ~= nil then
--         for k,v in pairs(self.lxcz_footprint_eff_t) do
--             if v.obj ~= nil and not IsNil(v.obj) and v.lxcz_role_model ~= nil then
--                 v.lxcz_role_model:OnRemoveGameObject(v.obj)
--                 v.obj:SetActive(false)
--             end
--         end
--     end

--     self.lxcz_footprint_eff_t = {}
-- end

----设置模型显示参数
function RebateActivityView:LXRCChangeModelShowScale()
    local scale_cfg = RebateLianXuRechargeWGData.Instance:GetShowModelParam(self.lxcz_group_index, self.lxcz_cur_btn_type)
    if IsEmptyTable(scale_cfg) then
        return
    end

    local scale, rotation_y, pos_x, pos_y = 0, 0, 0, 0

    scale = (scale_cfg.main_scale and scale_cfg.main_scale ~= "") and scale_cfg.main_scale or 1
    rotation_y = (scale_cfg.main_rotation_y and scale_cfg.main_rotation_y ~= "") and scale_cfg.main_rotation_y or 180
    pos_x = (scale_cfg.main_pos_x and scale_cfg.main_pos_x ~= "") and scale_cfg.main_pos_x or 0
    pos_y = (scale_cfg.main_pos_y and scale_cfg.main_pos_y ~= "") and scale_cfg.main_pos_y or 0

    RectTransform.SetAnchoredPositionXY(self.node_list.lxcz_ph_display.rect, pos_x, pos_y)
    RectTransform.SetLocalScale(self.node_list.lxcz_ph_display.rect, scale)

    if self.lxcz_role_model then
        self.lxcz_role_model:SetRotation(u3dpool.vec3(0, rotation_y, 0))
    end

    if self.node_list["lxcz_lc_root"]:GetActive() then
        scale = (scale_cfg.lc_scale and scale_cfg.lc_scale ~= "") and scale_cfg.lc_scale or 1
        rotation_y = (scale_cfg.lc_rotation_y and scale_cfg.lc_rotation_y ~= "") and scale_cfg.lc_rotation_y or 180
        pos_x = (scale_cfg.lc_pos_x and scale_cfg.lc_pos_x ~= "") and scale_cfg.lc_pos_x or 0
        pos_y = (scale_cfg.lc_pos_y and scale_cfg.lc_pos_y ~= "") and scale_cfg.lc_pos_y or 0

        RectTransform.SetAnchoredPositionXY(self.node_list.lxcz_lc_display.rect, pos_x, pos_y)
        RectTransform.SetLocalScale(self.node_list.lxcz_lc_display.rect, scale)

        if self.lxcz_lingchong_model then
            self.lxcz_lingchong_model:SetRotation(u3dpool.vec3(0, rotation_y, 0))
        end
    end

    if self.node_list["lxcz_xw_root"]:GetActive() then
        scale = (scale_cfg.xw_scale and scale_cfg.xw_scale ~= "") and scale_cfg.xw_scale or 1
        rotation_y = (scale_cfg.xw_rotation_y and scale_cfg.xw_rotation_y ~= "") and scale_cfg.xw_rotation_y or 180
        pos_x = (scale_cfg.xw_pos_x and scale_cfg.xw_pos_x ~= "") and scale_cfg.xw_pos_x or 0
        pos_y = (scale_cfg.xw_pos_y and scale_cfg.xw_pos_y ~= "") and scale_cfg.xw_pos_y or 0

        RectTransform.SetAnchoredPositionXY(self.node_list.lxcz_xw_display.rect, pos_x, pos_y)
        RectTransform.SetLocalScale(self.node_list.lxcz_xw_display.rect, scale)
        if self.lxcz_xianwa_model then
            self.lxcz_xianwa_model:SetRotation(u3dpool.vec3(0, rotation_y, 0))
        end
    end
end


---------------------------------------------------模型设置相关 end





--设置天神
function RebateActivityView:LXRCSetTianShenModel(item_id)
    -- local ts_active_cfg = TianShenWGData.Instance:GetTianShenActItemCfgByActId(item_id)
    -- if not ts_active_cfg then
    --     return
    -- end
    -- self.lxcz_tianshen_index = ts_active_cfg.index
    -- local ts_appe_cfg = TianShenWGData.Instance:GetTianShenCfg(ts_active_cfg.index)--形象配置
    -- if ts_appe_cfg then
    --     self.lxcz_show_model:SetTianShenModel(ts_appe_cfg.appe_image_id, ts_appe_cfg.index, false, nil, SceneObjAnimator.Rest)
    -- end
end
--天神预览按钮
function RebateActivityView:LXRCOnClickYuLanBtn()
    if not self.lxcz_tianshen_index then
        return
    end

    CommonSkillShowCtrl.Instance:SetTianShenSkillViewDataAndOpen({tianshen_index = self.lxcz_tianshen_index})
end

function RebateActivityView:LXRCOnClickTipBtn()
    RuleTip.Instance:SetContent(Language.RebateRecharge.LXCZTipDescent, Language.RebateRecharge.LXCZTipTitle)
end

function RebateActivityView:LXRCResetModelItemList()
    self.lxcz_show_model_item_list = {}
end

function RebateActivityView:LXRCSetModelItemList(item_id)
    -- print_error("FFFFF======= item_id", item_id)
    table.insert(self.lxcz_show_model_item_list, item_id)
end

--设置战力显示
function RebateActivityView:LXRCSetCapabilityShow()
    local power_num = 0
    -- print_error("FFF==== self.lxcz_show_model_item_list", #self.lxcz_show_model_item_list, self.lxcz_show_model_item_list)
    if not IsEmptyTable(self.lxcz_show_model_item_list) then
        for k, item_id in pairs(self.lxcz_show_model_item_list) do
            -- print_error("FFFFF====== 战力", item_id, ItemShowWGData.CalculateCapability(item_id))
            power_num = power_num + ItemShowWGData.CalculateCapability(item_id)
        end
    end
    if self.node_list["lxcz_txt_zhanli"] then
        self.node_list["lxcz_txt_zhanli"].text.text = power_num
    end
end

function RebateActivityView:LXRCDoAnimation()
    local tween_info = UITween_CONSTS.RebateActivityView
    UITween.FakeHideShow(self.node_list["lxcz_box_container"])
    local tween = UITween.AlphaShow(GuideModuleName.RebateActivityView, self.node_list["lxcz_box_container"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

function RebateActivityView:LXRCDoCellListAnimation()
    local tween_info = UITween_CONSTS.RebateActivityView.ListCellRender
    self.node_list["lxcz_reward_item_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["lxcz_reward_item_list"]:SetActive(true)
        local list = self.lxcz_reward_item_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        local max_count = #sort_list
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end

            v.item:PalyLXRCItemAnim(count, count == max_count, BindTool.Bind(self.AllCellAnimationComplete, self))
        end
    end, tween_info.DelayDoTime, "LXRC_Cell_Tween")
end


function RebateActivityView:AllCellAnimationComplete()
    if not self:IsOpen() then
        return
    end

    self.leichong_limit_jump = false
    self:LXRCFlushRewardList()
end
--------------------------RebateLXRechargeSliderItem start--------------------------------------------
--进度条
RebateLXRechargeSliderItem = RebateLXRechargeSliderItem or BaseClass(BaseRender)
function RebateLXRechargeSliderItem:__init()
    self.reward_item_cell = ItemCell.New(self.node_list["item_pos"])
    self.node_list["item_eff_pos"].button:AddClickListener(BindTool.Bind(self.OnGetSliderReward, self))
end

function RebateLXRechargeSliderItem:__delete()
    if self.reward_item_cell then
        self.reward_item_cell:DeleteMe()
        self.reward_item_cell = nil
    end
end

function RebateLXRechargeSliderItem:OnFlush()
    if not self.data and not self.data.rare_reward_item and not self.data.need_chongzhi_day then
        return
    end

    --奖励物品设置
    local rewrad_item_cfg = self.data.rare_reward_item[0] or {}
    local item_id = rewrad_item_cfg.item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    if item_cfg then
        self.reward_item_cell:SetData(rewrad_item_cfg)
        self.node_list["item_name"].text.text = item_cfg.name or ""
    end
    
    local btn_type = RebateLianXuRechargeWGData.Instance:GetCacheBtnType()
    -- local reward_day = RebateLianXuRechargeWGData.Instance:TransitionRewardDay(self.data.reward_index)
    local reward_type = REBATE_LIANXU_RECHARGE_REWARD_TYPE.Stage
    local reward_state = REBATE_LIANXU_RECHARGE_REWARD_STATE.NotFinish

    if self.data.reward_index then
        reward_state = RebateLianXuRechargeWGData.Instance:GetRewardState(btn_type, self.data.reward_index + 1, reward_type)
    end
    self.node_list["item_eff_pos"]:SetActive(reward_state == REBATE_LIANXU_RECHARGE_REWARD_STATE.CanGet)--领取
    self.node_list["item_is_get"]:SetActive(reward_state == REBATE_LIANXU_RECHARGE_REWARD_STATE.HasGet)--已领取
    self.node_list["text_recharge"].text.text = string.format(Language.RebateRecharge.TotalRechargeDay_1, self.data.need_chongzhi_day)

    --进度条设置
    local group = RebateLianXuRechargeWGData.Instance:GetCurGroupByRechargeType(btn_type)
    local next_cfg = RebateLianXuRechargeWGData.Instance:GetSliderRewardCfgByIndex(group, btn_type, self:GetIndex() + 1)
    local is_last_one = IsEmptyTable(next_cfg)
    local total_day = RebateLianXuRechargeWGData.Instance:GetTotalDayByRechargeType(btn_type)
    local show_day = total_day - self.data.need_chongzhi_day
    local slider_value = 0
    if not is_last_one and show_day > 0 then
        slider_value = show_day / (next_cfg.need_chongzhi_day - self.data.need_chongzhi_day)
    end

    self.node_list["slider_bg"]:SetActive(not is_last_one)
    self.node_list["slider_fill"].slider.value = slider_value > 0 and slider_value or 0
end

function RebateLXRechargeSliderItem:OnGetSliderReward()
    if not self.data then
        return
    end
    local btn_type = RebateLianXuRechargeWGData.Instance:GetCacheBtnType()
    RebateActivityWGCtrl.Instance:SendLianXuChongZhiReq(btn_type, REBATE_LIANXU_RECHARGE_REWARD_TYPE.Stage, self.data.reward_index)
    -- print_error("点击领取进度条奖励 btn_type", btn_type, self.data.need_chongzhi_day)
end

--------------------------RebateLXRechargeSliderItem end----------------------------------------------

--------------------------RebateLXRechargeRewardItem start--------------------------------------------
--下方奖励
RebateLXRechargeRewardItem = RebateLXRechargeRewardItem or BaseClass(BaseRender)
function RebateLXRechargeRewardItem:__init()
    self.reward_item_cell = ItemCell.New(self.node_list["item_pos"])
    self.node_list["get_btn"].button:AddClickListener(BindTool.Bind(self.OnGetReward, self))
end

function RebateLXRechargeRewardItem:__delete()
    if self.reward_item_cell then
        self.reward_item_cell:DeleteMe()
        self.reward_item_cell = nil
    end
end

function RebateLXRechargeRewardItem:OnFlush()
    if not self.data and not self.data.reward_item then
        return
    end
    local rewrad_item_cfg = self.data.reward_item[0] or {}
    local btn_type = RebateLianXuRechargeWGData.Instance:GetCacheBtnType()
    -- local reward_day = RebateLianXuRechargeWGData.Instance:TransitionRewardDay(self.data.reward_index)
    local reward_type = REBATE_LIANXU_RECHARGE_REWARD_TYPE.Normal
    local reward_state = REBATE_LIANXU_RECHARGE_REWARD_STATE.NotFinish

    if self.data.reward_index then
        reward_state = RebateLianXuRechargeWGData.Instance:GetRewardState(btn_type, self.data.reward_index + 1, reward_type)
    end

    --按钮判断
    self.node_list["get_btn"]:SetActive(reward_state == REBATE_LIANXU_RECHARGE_REWARD_STATE.CanGet)--领取
    self.node_list["effect"]:SetActive(reward_state == REBATE_LIANXU_RECHARGE_REWARD_STATE.CanGet)--领取
    self.node_list["not_finish"]:SetActive(reward_state == REBATE_LIANXU_RECHARGE_REWARD_STATE.NotFinish)--未达成
    self.node_list["is_get"]:SetActive(reward_state == REBATE_LIANXU_RECHARGE_REWARD_STATE.HasGet)--已领取

    self.reward_item_cell:SetData(rewrad_item_cfg)
    self.node_list["text_recharge"].text.text = string.format(Language.RebateRecharge.TotalRechargeDay_2, self.data.need_chongzhi_day)
end

function RebateLXRechargeRewardItem:OnGetReward()
    if not self.data then
        return
    end
    local btn_type = RebateLianXuRechargeWGData.Instance:GetCacheBtnType()
    RebateActivityWGCtrl.Instance:SendLianXuChongZhiReq(btn_type, REBATE_LIANXU_RECHARGE_REWARD_TYPE.Normal, self.data.reward_index)
    -- print_error("点击领取普通奖励 btn_type", btn_type, self.data.need_chongzhi_day)
end

function RebateLXRechargeRewardItem:PalyLXRCItemAnim(item_index, is_max, call_back)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.RebateActivityView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            if is_max then
                UITween.MoveAlphaShow(GuideModuleName.RebateActivityView, self.node_list["tween_root"], tween_info, call_back)
            else
                UITween.MoveAlphaShow(GuideModuleName.RebateActivityView, self.node_list["tween_root"], tween_info)
            end
        end

    end, tween_info.NextDoDelay * wait_index, "RebateLXRechargeRewardItem_Anim" .. wait_index)
end

--------------------------RebateLXRechargeRewardItem end----------------------------------------------