 ------------------------------------------------------------
--羁绊界面
------------------------------------------------------------
<PERSON><PERSON><PERSON>JingZuHeView = ShanHaiJingZuHeView or BaseClass(SafeBaseView)

function ShanHaiJingZuHeView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Normal
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(-4, 8), sizeDelta = Vector2(1078, 610)})
	self:AddViewResource(0, "uis/view/shj_ui_prefab", "HorizontalTabbar")
	self:AddViewResource(0, "uis/view/shj_ui_prefab", "layout_zuhe")
	self.cur_select_group_type = -1
end

function ShanHaiJingZuHeView:__delete()
end

function ShanHaiJingZuHeView:LoadCallBack()
	--self.node_list["layout_commmon_second_root"].rect.sizeDelta = self.node_list.size.rect.sizeDelta
    --self.node_list["layout_commmon_second_root"].rect.anchoredPosition = self.node_list.size.rect.anchoredPosition
	self.node_list.title_view_name.text.text = Language.ShanHaiJing.Title_Name
	if self.zuhe_item_list == nil then
		self.zuhe_item_list = AsyncListView.New(ZuHeItemRender,self.node_list.ph_zuhe_list)
	end

	self.group_type_tab = AsyncListView.New(ZuHeTypeTabRender, self.node_list.ph_zuhe_group_type_list)
	self.group_type_tab:SetSelectCallBack(BindTool.Bind1(self.TopBarChangeToIndex, self))
	self.group_type_tab:SetRefreshCallback(BindTool.Bind1(self.RefreshCallback, self))
	self:RefreshTypeTab()
end

function ShanHaiJingZuHeView:CloseCallBack()
	self.cur_select_index = nil
end

function ShanHaiJingZuHeView:ReleaseCallBack()
	if self.zuhe_item_list then
		self.zuhe_item_list:DeleteMe()
		self.zuhe_item_list = nil
	end
	if ShanHaiJingWGData.Instance then
		ShanHaiJingWGData.Instance:ResetCurSelectTuJian()
	end
	if self.group_type_tab then
		self.group_type_tab:DeleteMe()
		self.group_type_tab = nil
	end

	self.cur_select_group_type = -1
end

function ShanHaiJingZuHeView:RefreshTypeTab()
	local select_card_type = ShanHaiJingWGData.Instance:GetCardType()
	local cfg = ShanHaiJingWGData.Instance:GetGroupTujianCfg(select_card_type)
	local tab_group_name_list = {}

	local jump_index = 999
	for k,v in pairs(cfg) do
		tab_group_name_list[k] = {}
		tab_group_name_list[k].index = k
		tab_group_name_list[k].name = v[1].group_type_name
		local remind = ShanHaiJingWGData.Instance:GetAllZuHeRemindByGroupType(k, select_card_type)
		tab_group_name_list[k].redmind = remind
		if remind > 0 and jump_index > k then
			jump_index = k 
		end
	end

	self.group_type_tab:SetDataList(tab_group_name_list)
	if jump_index < 999 then
		self.group_type_tab:JumpToIndex(jump_index)
	else
		self.group_type_tab:JumpToIndex(self.cur_select_index or 1)
	end
end

function ShanHaiJingZuHeView:RefreshCallback(item_cell, cell_index)
	item_cell:RefreshRedmind()
end

function ShanHaiJingZuHeView:TopBarChangeToIndex(cell)
	if (not cell) or (not cell.data) then
		return
	end

	local cell_index = cell:GetData().index
	if self.cur_select_group_type == cell_index then
		return
	end
	self.cur_select_group_type = cell_index
	self.cur_select_index = cell_index
	
	local select_card_type = ShanHaiJingWGData.Instance:GetCardType()
	local zuhe_data_list = ShanHaiJingWGData.Instance:GetZuHeInfoList(self.cur_select_group_type, select_card_type)
	if self.zuhe_item_list ~= nil then
		self.zuhe_item_list:SetDataList(zuhe_data_list)
	end
end

function ShanHaiJingZuHeView:ShowIndexCallBack(index)
	self:Flush()
end

function ShanHaiJingZuHeView:OnFlush(param_t, index)
	if self.cur_select_group_type == -1 then
		return
	end

	local select_card_type = ShanHaiJingWGData.Instance:GetCardType()
	local zuhe_data_list = ShanHaiJingWGData.Instance:GetZuHeInfoList(self.cur_select_group_type, select_card_type)
	if self.zuhe_item_list ~= nil then
		self.zuhe_item_list:SetDataList(zuhe_data_list)
	end

	if self.group_type_tab then
		--self.group_type_tab:RefreshActiveCellViews()
		self:RefreshTypeTab()
	end
end


----------------------------------------------------------------------------------------------------
-- ZuHeTypeTabItem
----------------------------------------------------------------------------------------------------
ZuHeTypeTabRender = ZuHeTypeTabRender or BaseClass(BaseRender)

function ZuHeTypeTabRender:OnFlush()
	if self.data == nil then
		return
	end

	self.node_list.Text.text.text = self.data.name
	self.node_list.Text_hl.text.text = self.data.name

	self.node_list.RedPoint:SetActive(self.data.redmind == 1)
end

function ZuHeTypeTabRender:OnClick()
	if self.click_callback then
		self.click_callback(self.data.index)
	end
	self:SetSelectIndex(self.data.index)
end

function ZuHeTypeTabRender:OnSelectChange(is_select)
	self.node_list.group_tab.toggle.isOn = is_select
end

function ZuHeTypeTabRender:RefreshRedmind()
	if self.data == nil then
		return
	end

	local select_card_type = ShanHaiJingWGData.Instance:GetCardType()
	self.data.redmind = ShanHaiJingWGData.Instance:GetAllZuHeRemindByGroupType(self.data.index, select_card_type)
	self.node_list.RedPoint:SetActive(self.data.redmind == 1)
end


----------------------------------------------------------------------------------------------------
-- 组合item
----------------------------------------------------------------------------------------------------
ZuHeItemRender = ZuHeItemRender or BaseClass(BaseRender)
function ZuHeItemRender:LoadCallBack()
	--self.node_list.not_active.button:AddClickListener(BindTool.Bind(self.OnClickActive, self))
	self.node_list.btn_jihuo.button:AddClickListener(BindTool.Bind(self.OnClickActive, self))

	if self.node_list.ph_card_list then
		self.card_list = AsyncListView.New(TuJianItemCellRender, self.node_list.ph_card_list)
	end
end

function ZuHeItemRender:__delete()
	if self.card_list then
		self.card_list:DeleteMe()
		self.card_list = nil
	end
end

function ZuHeItemRender:OnClickActive()
	ShanHaiJingWGCtrl.Instance:CSTujianOperaReq(TUJIAN_OP_TYPE.TUJIAN_OP_TYPE_ACTIVE_GROUP,self.data.cfg_list.seq)
	self:PlayEffect()
	-- if not self.node_list["effect_jijuo"]:GetActive() then --避免多次产生特效
	-- end
end

function ZuHeItemRender:PlayEffect(  )
	self.node_list["effect_jijuo"]:SetActive(true)
	TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_jihuo, is_success = true})
end

function ZuHeItemRender:OnClickZuHeItemRenderCalllBack(cell)
	if cell.has_active then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShanHaiJing.Btn_Tip_3)
	else
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = cell.stuff_item_cfg.id})
	end
	--ShanHaiJingWGData.Instance:SignCurSelectTuJian(tonumber(cell.data))
	--ViewManager.Instance:FlushView(GuideModuleName.ShanHaiJingZuHeView)
end

function ZuHeItemRender:OnFlush()
	
	if self.data == nil then return end
	local data = self.data

	--生成图鉴表
	--if self.card_item_list == nil then
	--	self.card_item_list = AsyncListView.New(CardItemRender,self.node_list.ph_card_list)
		--self.card_item_list:SetSelectCallBack(BindTool.Bind1(self.OnClickZuHeItemRenderCalllBack, self))
	--end
	--self.card_item_list:SetDataList(data.list_info_data,2)


	local card_list = {}
	local seq = 0
	local tujian_data = {}
	local active_item_id = 0
	for k, v in pairs(data.list_info_data) do
		seq = data.list_info_data[k]
		tujian_data = ShanHaiJingWGData.Instance:GetTJCfgBySeq(tonumber(seq))
		active_item_id = tujian_data.active_item_id
		local has_active = ShanHaiJingWGData.Instance:GetTJIsActBySeq(tujian_data.seq)
		card_list[k] = {item_id = active_item_id, is_active = has_active}
	end
	self.card_list:SetDataList(card_list)
	--self.card_list:JumptToPrecent(1)

	local special_add_attr = 0
	if data.cfg_list.attr_added_modulus and data.cfg_list.attr_added_modulus ~= 0 then
		special_add_attr = data.cfg_list.attr_added_modulus / 100
	end

	local attr_list = AttributeMgr.GetAttributteByClass(data.cfg_list)
	local attr_name = Language.Common.AttrNameList2
	local sort_list = AttributeMgr.SortAttribute()
	local is_per
	local attr_data = {}
	
	for k,v in pairs(sort_list) do
		if attr_list[v] ~= 0 then
			is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v)
		    local str = ToColorStr(attr_name[v], COLOR3B.BLUE_TITLE) .. " " .. ToColorStr((is_per and (attr_list[v]/100 .. '%') or attr_list[v]), COLOR3B.DEFAULT_NUM)
			table.insert(attr_data, str)
		end
	end

	if special_add_attr ~= 0 then
		local str = ToColorStr(Language.ShanHaiJing.AddPerTuJianAttr, COLOR3B.BLUE_TITLE).." "..ToColorStr(special_add_attr.."%", COLOR3B.DEFAULT_NUM)
		table.insert(attr_data, str)
	end

	for i = 1, 5 do
		if nil ~= attr_data[i] then 
			self.node_list["attr_list"..i]:SetActive(true)
			self.node_list["attr_list"..i].text.text = attr_data[i]
		else
			self.node_list["attr_list"..i]:SetActive(false)
		end
	end

	self.node_list.group_name.text.text = ToColorStr(ShanHaiJingWGData.Instance:GetZuHeNameBySeq(data.cfg_list.seq), ITEM_COLOR[data.cfg_list.group_color + 1])
	--ShenShouWGCtrl.ChangeShenShouTextColor(self.node_list.group_name.text, data.cfg_list.group_color+1)
	--ChangeToQualityText(self.node_list.group_name, data.cfg_list.group_color+1)

	local flag = ShanHaiJingWGData.Instance:GetZuHeRemindBySeq(data.cfg_list.seq)
	self.node_list.remind:SetActive(flag)
	self.node_list.btn_jihuo:SetActive(flag)

	local is_active = data.has_active == 1
	self.node_list.has_active:SetActive(is_active)
	self.node_list.not_active:SetActive(not (is_active or flag))
end


----------------------------------------------------------------------------------------------------
-- TuJianItemRender
----------------------------------------------------------------------------------------------------
TuJianItemCellRender = TuJianItemCellRender or BaseClass(BaseRender)
function TuJianItemCellRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.cell_node)
	end
end

function TuJianItemCellRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
	end
end

function TuJianItemCellRender:OnFlush()
	if self.data == nil then
		return
	end
	self.item_cell:SetData(self.data)
	self.node_list.locked_obj:SetActive(not self.data.is_active)
end

----------------------------------------------------------------------------------------------------
-- CardItemRender
----------------------------------------------------------------------------------------------------
-- CardItemRender = CardItemRender or BaseClass(BaseRender)
-- function CardItemRender:LoadCallBack()
-- 	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnClick, self))
-- end

-- function CardItemRender:OnFlush()
-- 	if nil == self.data then return end
-- 	local data = tonumber(self.data)
-- 	local card_cfg = ShanHaiJingWGData.Instance:GetTJCfgBySeq(data)
-- 	--self.node_list.select_effect:SetActive(ShanHaiJingWGData.Instance:GetCurSelectTuJian() == data)
-- 	local item_cfg = ItemWGData.Instance:GetItemConfig(card_cfg.active_item_id)
-- 	self.stuff_item_cfg = item_cfg
-- 	self.node_list.lbl_name.text.text = item_cfg.name
-- 	self.node_list.img_picture.raw_image:LoadSprite(ResPath.GetNoPackPNG("a1_img_card_bg_" .. card_cfg.card_ID))
-- 	self.node_list.select_effect.image:LoadSprite(ResPath.GetF2SHJImgPath("kapai_" .. item_cfg.color))
-- 	self.node_list.bg.image:LoadSprite(ResPath.GetF2CommonImages(
-- 	("kuang_shj_" .. item_cfg.color)))

-- 	-- 是否激活
-- 	local tujian_active_flag = ShanHaiJingWGData.Instance:GetTJAllServerInfo()
-- 	self.has_active = tujian_active_flag[data].level > 0
-- 	self.node_list.jihuo:SetActive(not self.has_active)
-- 	self.node_list.star_bg:SetActive(self.has_active)
-- 	self:FulshStar(tujian_active_flag[data])
-- 	XUI.SetGraphicGrey(self.node_list.layout_gray, not self.has_active)
-- 	XUI.SetGraphicGrey(self.node_list.jihuo, true)
-- end

-- function CardItemRender:OnClick()
-- 	local data = tonumber(self.data)
-- 	local card_cfg = ShanHaiJingWGData.Instance:GetTJCfgBySeq(data)
-- 	if self.has_active then
-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.ShanHaiJing.Btn_Tip_3)
-- 	else
-- 		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = card_cfg.active_item_id})
-- 	end
-- end

-- function CardItemRender:FulshStar(data)
-- 	local star_num = data.star
-- 	if star_num <= 5 then
-- 		for i=1,5 do
-- 			self.node_list['star' .. i]:SetActive(star_num >= i)
-- 			self.node_list['star' .. i].image:LoadSprite(ResPath.GetF2CommonImages("star_s_2"))
-- 		end
-- 	else
-- 		for i=1,5 do
-- 			self.node_list['star' .. i]:SetActive(true)
-- 			self.node_list['star' .. i].image:LoadSprite(ResPath.GetF2CommonImages("star_s_2"))
-- 		end

-- 		for i=6,10 do
-- 			if star_num >= i then
-- 				self.node_list['star' .. i-5].image:LoadSprite(ResPath.GetF2CommonImages("star_s_1"))
-- 			end
-- 		end
-- 	end
-- end
