QiongQiLaiXiObj = QiongQiLaiXiObj or BaseClass(SceneObj)

function QiongQiLaiXiObj:__init(vo)
	self.obj_type = SceneObjType.QiongQiLaiXi
end

function QiongQiLaiXiObj:__delete()
	if nil ~= self.model_effect then
		self.model_effect:DeleteMe()
		self.model_effect = nil
	end
end

function QiongQiLaiXiObj:InitAppearance()
	local root = self.draw_obj:GetRoot()
	local trans = root.transform
	if self.vo.offset ~= nil then
		local offset = self.vo.offset
		self.draw_obj:SetOffset(Vector3(offset[1], offset[2], offset[3]))
	end

	if self.vo.rotation ~= nil then
		local rotation = self.vo.rotation
		trans.localEulerAngles = Vector3(rotation[1], rotation[2], rotation[3])
	end

	local effect_name = "chuansongmen_huang"
	if self.vo.effect_name then
		effect_name = self.vo.effect_name
	end

	local bundle_name, prefab_name = ResPath.GetEnvironmentEffect(effect_name)
	self:ChangeModel(SceneObjPart.Main, bundle_name, prefab_name)
end

function QiongQiLaiXiObj:OnEnterScene()
	--SceneObj.OnEnterScene(self)
	self:InitAppearance()
	if self.vo.target_name then
		local ui = self:GetFollowUi()
		ui:SetName(self.vo.target_name)
		ui:ForceSetVisible(true)
	end
end

function QiongQiLaiXiObj:AddMoreName(str)
	if self.vo.target_name then
		local ui = self:GetFollowUi()
		ui:SetName( str .. "\n" .. self.vo.target_name)
	end
end

function QiongQiLaiXiObj:SetDoorName(str)
	if self.vo.target_name then
		local ui = self:GetFollowUi()
		ui:SetName(str)
	end
end

function QiongQiLaiXiObj:SetFollowUIState(enabled)
	local ui = self:GetFollowUi()
	if ui then
		if enabled then
			ui:ForceSetVisible(true)
		else
			ui:ForceSetVisible(false)
		end
	end
end

function QiongQiLaiXiObj:GetObjKey()
	return self.vo.door_id
end

function QiongQiLaiXiObj:GetDoorId()
	return self.vo.door_id
end
