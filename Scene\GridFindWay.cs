﻿using System;
using System.Collections.Generic;
using System.IO;
using UnityEngine.Assertions;
using SevenZip.Compression.LZMA;
using UnityEngine;
using System.Linq;

/// <summary>
/// 网格寻路核心类 - 基于A*算法实现二维网格路径搜索
/// 
/// 主要功能特性：
/// • 支持静态阻挡（来自地图配置数据）和动态阻挡（运行时动态设置）
/// 
/// 使用流程：
/// 1. 通过构造函数创建指定尺寸的网格
/// 2. 调用LoadData加载地图阻挡数据
/// 3. 使用FindWay执行路径搜索
/// 4. 通过GenerateInflexPoints生成优化路径拐点
/// </summary>
public sealed class GridFindWay
{
    private int width;              // 网格横向单元格数量
    private int height;             // 网格纵向单元格数量
    private byte[] blocks;          // 静态阻挡数据（来自地图配置）
    private int blockSize;
    private byte[] dynamicBlocks;   // 运行时动态阻挡标记数组
    private int dynamicBlockSize;

    private GridCell[] cells;       // 所有网格单元数据池
    private int cellSize;

    private Decoder decoder;

    private Queue<int> openList = new Queue<int>();

    private int endX;
    private int endY;
    private List<GridPoint> inflexPoints = new List<GridPoint>();

    /// <summary>
    /// 获取网格的宽度（横向单元格数量）
    /// </summary>
    public int Width
    {
        get { return this.width; }
    }

    /// <summary>
    /// 获取网格的高度（纵向单元格数量）
    /// </summary>
    public int Height
    {
        get { return this.height; }
    }

    /// <summary>
    /// 构造函数 - 初始化指定尺寸的网格寻路器
    /// </summary>
    /// <param name="width">网格宽度（横向单元格数量），必须大于0</param>
    /// <param name="height">网格高度（纵向单元格数量），必须大于0</param>
    /// <remarks>
    /// 构造函数仅分配内存空间，实际地图数据需要通过LoadData方法加载
    /// </remarks>
    public GridFindWay(int width, int height)
    {
        var size = width * height;

        this.cellSize = size;
        this.cells = new GridCell[this.cellSize];

        this.blockSize = size;
        this.blocks = new byte[this.blockSize];

        this.dynamicBlockSize = size;
        this.dynamicBlocks = new byte[this.dynamicBlockSize];
    }

    /// <summary>
    /// 加载并初始化网格地图数据
    /// </summary>
    /// <param name="width">地图宽度（单元格数量）</param>
    /// <param name="height">地图高度（单元格数量）</param>
    /// <param name="mask">Base64编码的LZMA压缩地图阻挡数据</param>
    /// <remarks>
    /// 处理流程：
    /// 1. 解压LZMA压缩的地图阻挡数据
    /// 2. 验证解压后数据长度与网格尺寸的一致性
    /// 3. 初始化每个网格单元的坐标和索引信息
    /// 4. 重新分配动态阻挡和单元格数据数组
    /// </remarks>
    /// <exception cref="Exception">当输入的LZMA数据格式错误时抛出异常</exception>
    public void LoadData(int width, int height, string mask)
    {
        // Decompress the mask.
        var compress = Convert.FromBase64String(mask);
        using (var compressStream = new MemoryStream())
        using (var decompressStream = new MemoryStream())
        {
            compressStream.Write(compress, 0, compress.Length);
            compressStream.Position = 0;
            if (this.decoder == null)
            {
                this.decoder = new Decoder();
            }

            var properties = new byte[5];
            if (compressStream.Read(properties, 0, 5) != 5)
            {
                throw (new Exception("input .lzma is too short"));
            }

            this.decoder.SetDecoderProperties(properties);
            this.decoder.Code(
                compressStream,
                decompressStream,
                compressStream.Length,
                -1,
                null);
            decompressStream.Flush();
            Assert.AreEqual(width * height, decompressStream.Length);

            this.blockSize = (int)decompressStream.Length;
            if (this.blocks == null ||
                this.blockSize > this.blocks.Length)
            {
                this.blocks = new byte[decompressStream.Length];
            }

            decompressStream.Seek(0, SeekOrigin.Begin);
            decompressStream.Read(this.blocks, 0, this.blockSize);

            // Record the data.
            this.width = width;
            this.height = height;

            this.dynamicBlockSize = width * height;
            if (this.dynamicBlocks == null ||
                this.dynamicBlockSize > this.dynamicBlocks.Length)
            {
                this.dynamicBlocks = new byte[this.dynamicBlockSize];
            }

            this.cellSize = width * height;
            if (this.cells == null ||
                this.cellSize > this.cells.Length)
            {
                this.cells = new GridCell[width * height];
            }

            // Setup the cell table.
            for (int x = 0; x < width; ++x)
            {
                for (int y = 0; y < height; ++y)
                {
                    int index = x + y * width;
                    this.cells[index].Index = index;
                    this.cells[index].X = x;
                    this.cells[index].Y = y;
                }
            }
        }
    }

    /// <summary>
    /// 执行A*寻路算法寻找从起点到终点的最优路径
    /// </summary>
    /// <param name="startX">起点X坐标（网格坐标系）</param>
    /// <param name="startY">起点Y坐标（网格坐标系）</param>
    /// <param name="endX">终点X坐标（网格坐标系）</param>
    /// <param name="endY">终点Y坐标（网格坐标系）</param>
    /// <param name="ignoreHighArea">是否忽略高地地形的阻挡效果，默认为false</param>
    /// <param name="ignoreBlock">是否忽略所有阻挡进行寻路（主要用于调试），默认为false</param>
    /// <returns>如果找到可行路径返回true，否则返回false</returns>
    /// <remarks>
    /// 算法特点：
    /// • 使用A*算法，支持8方向移动
    /// • 不同地形类型具有不同的移动成本（道路=10000，其他=15000）
    /// • 使用曼哈顿距离作为启发函数
    /// • 包含完整的边界检查和起终点有效性验证
    /// • 最大迭代次数限制为1000000次，防止无限循环
    /// 
    /// 注意事项：
    /// • 起点和终点不能相同
    /// • 起点和终点都必须是可通行的格子
    /// • 寻路成功后需调用GenerateInflexPoints生成路径拐点
    /// </remarks>
    public bool FindWay(int startX, int startY, int endX, int endY, bool ignoreHighArea = false, bool ignoreBlock = false)
    {
        if (startX < 0 || startX >= this.width ||
            startY < 0 || startY >= this.height)
        {
            return false;
        }

        if (endX < 0 || endX >= this.width ||
            endY < 0 || endY >= this.height)
        {
            return false;
        }

        if(startX == endX && startY == endY)
        {
            return false;
        }

        if (this.IsBlock(startX, startY, ignoreHighArea, ignoreBlock) || this.IsBlock(endX, endY, ignoreHighArea, ignoreBlock))
        {
            return false;
        }

        this.endX = endX;
        this.endY = endY;
        this.ResetAStar();

        var cellIndex = startX + startY * this.width;
        for (int i = 0; i < 1000000; ++i)
        {
            this.cells[cellIndex].Block = true;
            var cell = this.cells[cellIndex];

            if (this.DoAStar(cell, endX, endY, 1, 0, 0, ignoreHighArea, ignoreBlock))
            {
                return true;
            }

            if (this.DoAStar(cell, endX, endY, 0, 1, 1, ignoreHighArea, ignoreBlock))
            {
                return true;
            }

            if (this.DoAStar(cell, endX, endY, -1, 0, 2, ignoreHighArea, ignoreBlock))
            {
                return true;
            }

            if (this.DoAStar(cell, endX, endY, 0, -1, 3, ignoreHighArea, ignoreBlock))
            {
                return true;
            }

            if (this.DoAStar(cell, endX, endY, -1, 1, 4, ignoreHighArea, ignoreBlock))
            {
                return true;
            }

            if (this.DoAStar(cell, endX, endY, 1, 1, 5, ignoreHighArea, ignoreBlock))
            {
                return true;
            }

            if (this.DoAStar(cell, endX, endY, 1, -1, 6, ignoreHighArea, ignoreBlock))
            {
                return true;
            }

            if (this.DoAStar(cell, endX, endY, -1, -1, 7, ignoreHighArea, ignoreBlock))
            {
                return true;
            }

            bool cantFind = true;
            for (int j = 0; j < 10000; ++j)
            {
                if (this.openList.Count <= 0)
                {
                    break;
                }

                var nextOpen = this.openList.Dequeue();
                var nextCell = this.cells[nextOpen];
                if (!nextCell.Block && !ignoreBlock)
                {
                    cellIndex = nextOpen;
                    cantFind = false;
                    break;
                }
                else
                {
                    if (ignoreBlock)
                    {
                        cellIndex = nextOpen;
                        cantFind = false;
                        break;
                    }
                }
            }

            if (cantFind)
            {
                return false;
            }
        }

        return false;
    }

    /// <summary>
    /// 基于寻路结果生成优化的路径拐点序列
    /// </summary>
    /// <param name="range">终点附近的优化范围，当大于0时会跳过终点附近指定范围内的路径点</param>
    /// <remarks>
    /// 优化流程：
    /// 1. 从终点开始反向回溯完整路径
    /// 2. 根据移动方向变化识别关键拐点
    /// 3. 使用直线通行检测消除冗余中间点
    /// 4. 清理标记为无效的路径点(-1, -1)
    /// 
    /// 算法说明：
    /// • 只保留方向发生变化的关键点作为拐点
    /// • 对相邻三个拐点进行直线检测，如果可直达则移除中间点
    /// • 生成的拐点序列按照从起点到终点的顺序存储
    /// 
    /// 使用场景：
    /// • 平滑角色移动轨迹，减少不必要的方向变换
    /// • 优化网络传输，减少路径点数量
    /// </remarks>
    public void GenerateInflexPoints(int range)
    {
        this.inflexPoints.Clear();

        var curIndex = this.endX + this.endY * this.width;
        if (range > 0)
        {
            var lastIndex = curIndex;
            for (int i = 0; i < 10000; ++i)
            {
                if (curIndex < 0)
                {
                    break;
                }

                var curCell = this.cells[curIndex];
                var disX = curCell.X - this.endX;
                var disY = curCell.Y - this.endY;
                if (range * range < disX * disX + disY * disY)
                {
                    break;
                }

                lastIndex = curIndex;
                curIndex = curCell.Parent;
            }

            curIndex = lastIndex;
        }

        var curDir = -1;
        for (int i = 1; i < 10000; ++i)
        {
            if (curIndex < 0)
            {
                break;
            }

            var curCell = this.cells[curIndex];
            if (curCell.Dir != curDir)
            {
                this.inflexPoints.Add(new GridPoint(curCell.X, curCell.Y));
                curDir = curCell.Dir;
            }

            curIndex = curCell.Parent;
        }

        for (int i = 0; i < this.inflexPoints.Count - 2;)
        {
            var p1 = this.inflexPoints[i];
            var p3 = this.inflexPoints[i + 2];
            if (this.IsWayLine(p1.X, p1.Y, p3.X, p3.Y))
            {
                this.inflexPoints[i + 1] = new GridPoint(-1, -1);
                i += 2;
            }
            else
            {
                ++i;
            }
        }

        this.inflexPoints.RemoveAll(p => p.X < 0 || p.Y < 0);
    }

    /// <summary>
    /// 获取当前路径拐点的数量
    /// </summary>
    /// <returns>路径拐点总数，如果无有效路径则返回0</returns>
    /// <remarks>
    /// 使用前提条件：
    /// • 必须先调用FindWay方法且返回true（找到路径）
    /// • 必须调用GenerateInflexPoints方法生成路径拐点
    /// 
    /// 注意事项：
    /// • 返回的数量包括起点和终点
    /// • 如果路径被优化，数量可能小于原始A*路径的节点数
    /// • 配合GetPathPoint方法使用可遍历完整路径
    /// </remarks>
    public int GetPathLength()
    {
        return this.inflexPoints?.Count ?? 0;
    }


    /// <summary>
    /// 获取指定索引位置的路径拐点坐标
    /// </summary>
    /// <param name="index">拐点索引，0表示起点，按移动顺序递增</param>
    /// <param name="x">输出拐点的X坐标</param>
    /// <param name="y">输出拐点的Y坐标</param>
    /// <returns>如果索引有效返回true，否则返回false</returns>
    /// <remarks>
    /// 路径点按照从起点到终点的正常顺序存储和获取
    /// 调用前请确保已经调用FindWay和GenerateInflexPoints方法
    /// </remarks>
    public bool GetPathPoint(int index, out int x, out int y)
    {
        x = 0;
        y = 0;
        
        if (index < 0 || index >= this.inflexPoints.Count)
        {
            return false;
        }
        
        // 修复：直接按正序获取，因为inflexPoints在生成时已经是倒序的
        // 这样index=0就是起点，符合直觉
        var p = this.inflexPoints[this.inflexPoints.Count - 1 - index];
        x = p.X;
        y = p.Y;
        return true;
    }

    /// <summary>
    /// 检查指定位置是否为水域地形
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <returns>如果是水域地形返回true，否则返回false</returns>
    /// <remarks>
    /// 超出网格边界的位置被视为非水域地形
    /// </remarks>
    public bool IsWaterWay(int x, int y)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return false;
        }

        var index = x + y * this.width;
        var cell = this.blocks[index];
        if (cell == (int)GridCellType.Water)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 检查指定位置是否被阻挡（综合静态地形和动态阻挡）
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <param name="ignoreHighArea">是否忽略高地地形的阻挡效果，默认为false</param>
    /// <param name="ignoreBlock">是否忽略所有阻挡检查，默认为false</param>
    /// <returns>如果位置被阻挡返回true，否则返回false</returns>
    /// <remarks>
    /// 阻挡判定优先级：
    /// 1. 边界检查：超出网格范围视为阻挡
    /// 2. ignoreBlock参数：如果为true则直接返回false（无阻挡）
    /// 3. 动态阻挡：运行时设置的临时阻挡，优先级最高
    /// 4. 静态地形阻挡：障碍物、客户端阻挡、障碍路径、边界
    /// 5. 高地地形：仅在ignoreHighArea为false时生效
    /// </remarks>
    public bool IsBlock(int x, int y, bool ignoreHighArea = false, bool ignoreBlock = false)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return true;
        }

        if (ignoreBlock)
        {
            return false;
        }

        var index = x + y * this.width;
        if (this.dynamicBlocks[index] > 0)
        {
            return true;
        }

        var cell = this.blocks[index];

        return cell == (int)GridCellType.Obstacle || cell == (int)GridCellType.ClientBlock ||
            cell == (int)GridCellType.ObstacleWay ||
            cell == (int)GridCellType.Border ||
            (!ignoreHighArea && cell == (int)GridCellType.HighArea);
    }

    /// <summary>
    /// 检查指定位置是否为安全区域
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <returns>如果是安全区域返回true，否则返回false</returns>
    /// <remarks>
    /// 超出网格边界的位置被视为安全区域
    /// </remarks>
    public bool IsInSafeArea(int x, int y)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return true;
        }

        var index = x + y * this.width;
        return this.blocks[index] == (int)GridCellType.Safe;
    }

    /// <summary>
    /// 检查指定位置是否为隧道区域
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <returns>如果是隧道区域返回true，否则返回false</returns>
    /// <remarks>
    /// 超出网格边界的位置被视为非隧道区域
    /// </remarks>
    public bool IsTunnelArea(int x, int y)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return false;
        }

        var index = x + y * this.width;
        return this.blocks[index] == (int)GridCellType.Tunnel;
    }

    /// <summary>
    /// 检查指定位置是否为边界区域
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <returns>如果是边界区域返回true，否则返回false</returns>
    /// <remarks>
    /// 超出网格边界的位置被视为非边界区域（可以飞出边界）
    /// </remarks>
    public bool IsBorder(int x, int y)
    {
        // 可以飞出边界
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return false;
        }

        var index = x + y * this.width;
        return this.blocks[index] == (int)GridCellType.Border;
    }

    /// <summary>
    /// 检查指定位置是否为高地地形
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <returns>如果是高地地形返回true，否则返回false</returns>
    /// <remarks>
    /// 超出网格边界的位置被视为高地地形
    /// </remarks>
    public bool IsHighArea(int x, int y)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return true;
        }

        var index = x + y * this.width;
        return this.blocks[index] == (int)GridCellType.HighArea;
    }

    /// <summary>
    /// 检查指定坐标是否超出网格范围
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <returns>如果超出网格范围返回true，否则返回false</returns>
    public bool IsNotInGrid(int x, int y)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return true;
        }

        return false;
    }

    /// <summary>
    /// 获取目标点周围最近的有效站立位置
    /// </summary>
    /// <param name="x">起始点X坐标</param>
    /// <param name="y">起始点Y坐标</param>
    /// <param name="endX">目标点X坐标</param>
    /// <param name="endY">目标点Y坐标</param>
    /// <param name="range">最大检测范围（格数）</param>
    /// <param name="targetX">输出找到的有效点X坐标</param>
    /// <param name="targetY">输出找到的有效点Y坐标</param>
    /// <param name="targetRange">输出从有效点到目标点的剩余距离</param>
    /// <remarks>
    /// 算法原理：
    /// 1. 如果起始点距离目标点在范围内，直接返回起始点
    /// 2. 从目标点向起始点方向逐步检测可通行点
    /// 3. 返回最接近目标点且可通行的位置
    /// 
    /// 典型应用场景：
    /// • 技能施法目标位置确定
    /// • 攻击范围内最佳位置选择
    /// • 近战攻击距离计算
    /// </remarks>
    public void GetTargetXY(
        int x,
        int y,
        int endX,
        int endY,
        int range,
        out int targetX,
        out int targetY,
        out int targetRange)
    {
        targetX = endX;
        targetY = endY;
        targetRange = range;
        
        if (range <= 0)
        {
            return;
        }
        
        // 计算起始点到目标点的距离
        var deltaPosX = x - endX;
        var deltaPosY = y - endY;
        var distanceSqr = deltaPosX * deltaPosX + deltaPosY * deltaPosY;
        
        // 如果起始点已经在范围内，直接返回起始点
        if (distanceSqr <= range * range)
        {
            targetX = x;
            targetY = y;
            targetRange = 0;
            return;
        }

        targetX = endX;
        targetY = endY;
        targetRange = range;
        // 沿方向向量逐步检测
        var distance = Math.Sqrt(distanceSqr);
        var normalizeX = deltaPosX / distance;
        var normalizeY = deltaPosY / distance;
        
        // 从目标点向起始点方向逐步检测
        for (int i = 1; i <= range; ++i)
        {
            var tx = (int)Math.Round(endX + normalizeX * i);
            var ty = (int)Math.Round(endY + normalizeY * i);
            
            // 检查边界
            if (tx < 0 || tx >= this.width || ty < 0 || ty >= this.height)
            {
                break;
            }
            
            if (!this.IsBlock(tx, ty))
            {
                targetX = tx;
                targetY = ty;
                targetRange = range - i;
                return;
            }
        }
    }

    /// <summary>
    /// 获取从起点向终点方向的最远可达位置
    /// </summary>
    /// <param name="x">起点X坐标</param>
    /// <param name="y">起点Y坐标</param>
    /// <param name="endX">目标终点X坐标</param>
    /// <param name="endY">目标终点Y坐标</param>
    /// <param name="targetX">输出实际可达位置的X坐标</param>
    /// <param name="targetY">输出实际可达位置的Y坐标</param>
    /// <param name="ignoreHighArea">是否忽略高地地形阻挡，默认为false</param>
    /// <param name="ignoreBlock">是否忽略所有阻挡检查，默认为false</param>
    /// <remarks>
    /// 算法特点：
    /// • 从起点向终点方向逐步推进检测
    /// • 遇到第一个阻挡点立即停止
    /// • 返回最后一个可通行的坐标位置
    /// • 如果起点被阻挡，直接返回起点坐标
    /// 
    /// 典型应用场景：
    /// • 远程攻击弹道终点计算
    /// • 射线检测的可视终点
    /// • 直线移动的实际终点确定
    /// • 技能释放的有效作用点
    /// </remarks>
    public void GetLineEndXY(
        int x,
        int y,
        int endX,
        int endY,
        out int targetX,
        out int targetY,
        bool ignoreHighArea = false,
        bool ignoreBlock = false)
    {
        targetX = x;
        targetY = y;
        
        // 计算方向向量
        var deltaPosX = endX - x;
        var deltaPosY = endY - y;
        var distanceSqr = deltaPosX * deltaPosX + deltaPosY * deltaPosY;
        
        // 起点等于终点的情况
        if (distanceSqr <= 0)
        {
            return;
        }
        
        // 起点被阻挡的情况
        if (this.IsBlock(x, y, ignoreHighArea, ignoreBlock))
        {
            return;
        }
        
        var distance = Math.Sqrt(distanceSqr);
        var normalizeX = deltaPosX / distance;
        var normalizeY = deltaPosY / distance;
        
        // 沿直线方向逐步检测
        for (int i = 1; i <= distance; ++i)
        {
            var tx = (int)Math.Round(x + normalizeX * i);
            var ty = (int)Math.Round(y + normalizeY * i);
            
            // 边界检查
            if (tx < 0 || tx >= this.width || ty < 0 || ty >= this.height)
            {
                break;
            }
            
            // 阻挡检测
            if (this.IsBlock(tx, ty, ignoreHighArea, ignoreBlock))
            {
                break;
            }
            
            targetX = tx;
            targetY = ty;
            
            // 到达终点
            if (tx == endX && ty == endY)
            {
                break;
            }
        }
    }

    /// <summary>
    /// 从终点向起点方向检测首个可通行位置
    /// </summary>
    /// <param name="x">起点X坐标</param>
    /// <param name="y">起点Y坐标</param>
    /// <param name="endX">终点X坐标</param>
    /// <param name="endY">终点Y坐标</param>
    /// <param name="targetX">输出找到的可通行位置X坐标</param>
    /// <param name="targetY">输出找到的可通行位置Y坐标</param>
    /// <param name="ignoreHighArea">是否忽略高地地形阻挡，默认为false</param>
    /// <param name="ignoreBlock">是否忽略所有阻挡检查，默认为false</param>
    /// <remarks>
    /// 算法特点：
    /// • 从终点向起点方向逐步检测
    /// • 找到第一个可通行点立即返回
    /// • 如果终点本身可通行，直接返回终点坐标
    /// • 检测方向与GetLineEndXY完全相反
    /// 
    /// 典型应用场景：
    /// • 寻找撤退路径的安全点
    /// • 从阻挡区域寻找最近出口
    /// • 反向路径探测
    /// • 安全位置回退计算
    /// </remarks>
    public void GetLineEndXY2(
        int x,
        int y,
        int endX,
        int endY,
        out int targetX,
        out int targetY,
        bool ignoreHighArea = false, 
        bool ignoreBlock = false)
    {
        targetX = endX;
        targetY = endY;
        
        // 计算反向向量
        var deltaPosX = x - endX;
        var deltaPosY = y - endY;
        var distanceSqr = deltaPosX * deltaPosX + deltaPosY * deltaPosY;
        
        // 起点等于终点的情况
        if (distanceSqr <= 0)
        {
            return;
        }
        
        // 终点本身可通行的情况
        if (!this.IsBlock(endX, endY, ignoreHighArea, ignoreBlock))
        {
            return;
        }
        
        var distance = Math.Sqrt(distanceSqr);
        var normalizeX = deltaPosX / distance;
        var normalizeY = deltaPosY / distance;
        
        // 从终点向起点方向检测
        for (int i = 1; i <= distance; ++i)
        {
            var tx = (int)Math.Round(endX + normalizeX * i);
            var ty = (int)Math.Round(endY + normalizeY * i);
            
            // 边界检查
            if (tx < 0 || tx >= this.width || ty < 0 || ty >= this.height)
            {
                continue;
            }
            
            // 找到首个可行点立即返回
            if (!this.IsBlock(tx, ty, ignoreHighArea, ignoreBlock))
            {
                targetX = tx;
                targetY = ty;
                break;
            }
        }
    }

    /// <summary>
    /// 检测两点之间是否可以直线通行
    /// </summary>
    /// <param name="x">起点X坐标</param>
    /// <param name="y">起点Y坐标</param>
    /// <param name="endX">终点X坐标</param>
    /// <param name="endY">终点Y坐标</param>
    /// <param name="ignoreHighArea">是否忽略高地地形阻挡，默认为false</param>
    /// <param name="ignoreBlock">是否忽略所有阻挡检查，默认为false</param>
    /// <returns>如果可以直线通行返回true，否则返回false</returns>
    /// <remarks>
    /// 算法原理：
    /// 1. 计算两点间的方向向量和距离
    /// 2. 沿直线方向逐步采样检测阻挡状态
    /// 3. 任何中间点被阻挡都会返回false
    /// 
    /// 主要用途：
    /// • 路径后处理优化，消除冗余拐点
    /// • 直线攻击范围检测
    /// • 视线检测系统
    /// </remarks>
    public bool IsWayLine(int x, int y, int endX, int endY, bool ignoreHighArea = false, bool ignoreBlock = false)
    {
        if (this.IsBlock(endX, endY, ignoreHighArea, ignoreBlock))
        {
            return false;
        }

        var deltaPosX = endX - x;
        var deltaPosY = endY - y;

        var distanceSqr = deltaPosX * deltaPosX + deltaPosY * deltaPosY;
        if (distanceSqr <= 0)
        {
            return true;
        }

        var distance = Math.Sqrt(distanceSqr);
        var normalizeX = deltaPosX / distance;
        var normalizeY = deltaPosY / distance;

        for (int i = 1; i < distance; ++i)
        {
            var tx = (int)Math.Round(x + normalizeX * i);
            var ty = (int)Math.Round(y + normalizeY * i);
            if (this.IsBlock(tx, ty, ignoreHighArea, ignoreBlock))
            {
                return false;
            }
        }

        return true;
    }

    /// <summary>
    /// 设置指定位置的动态阻挡
    /// </summary>
    /// <param name="x">目标位置的X坐标</param>
    /// <param name="y">目标位置的Y坐标</param>
    /// <remarks>
    /// 动态阻挡用于运行时临时设置阻挡区域，优先级高于静态地形
    /// 超出网格范围或数组未初始化时操作会被忽略
    /// </remarks>
    public void SetBlock(int x, int y)
    {
        var cellIndex = x + y * this.width;
        if (this.dynamicBlocks == null ||
            cellIndex < 0 ||
            cellIndex >= this.dynamicBlockSize)
        {
            return;
        }

        this.dynamicBlocks[cellIndex] = 1;
    }

    /// <summary>
    /// 清除指定位置的动态阻挡
    /// </summary>
    /// <param name="x">目标位置的X坐标</param>
    /// <param name="y">目标位置的Y坐标</param>
    /// <remarks>
    /// 清除动态阻挡后，该位置将恢复为静态地形的阻挡状态
    /// 超出网格范围或数组未初始化时操作会被忽略
    /// </remarks>
    public void RevertBlock(int x, int y)
    {
        var cellIndex = x + y * this.width;
        if (this.dynamicBlocks == null ||
            cellIndex < 0 ||
            cellIndex >= this.dynamicBlockSize)
        {
            return;
        }

        this.dynamicBlocks[cellIndex] = 0;
    }

    /// <summary>
    /// 重置A*算法的相关数据结构，为新的寻路准备
    /// </summary>
    /// <remarks>
    /// 重置内容包括：
    /// • 清空开放列表队列
    /// • 重置所有网格单元的G值（实际成本）为0
    /// • 重置所有网格单元的H值（启发估值）为0  
    /// • 清除所有网格单元的关闭列表标记
    /// • 重置所有网格单元的父节点引用为-1
    /// • 重置所有网格单元的移动方向为-1
    /// </remarks>
    private void ResetAStar()
    {
        this.openList.Clear();
        for (int i = 0; i < this.cellSize; ++i)
        {
            this.cells[i].G = 0;
            this.cells[i].H = 0;
            this.cells[i].Block = false;
            this.cells[i].Parent = -1;
            this.cells[i].Dir = -1;
        }
    }

    /// <summary>
    /// A*算法的单方向节点扩展处理
    /// </summary>
    /// <param name="cell">当前处理的网格单元</param>
    /// <param name="endX">目标点X坐标</param>
    /// <param name="endY">目标点Y坐标</param>
    /// <param name="offsetX">X方向的移动偏移量</param>
    /// <param name="offsetY">Y方向的移动偏移量</param>
    /// <param name="nextDir">移动方向标识(0-7对应8个方向)</param>
    /// <param name="ignoreHighArea">是否忽略高地地形阻挡</param>
    /// <param name="ignoreBlock">是否忽略所有阻挡检查</param>
    /// <returns>如果找到目标点返回true，否则返回false</returns>
    /// <remarks>
    /// 处理流程：
    /// 1. 计算相邻节点的实际坐标
    /// 2. 执行边界检查和地形阻挡检测
    /// 3. 检查是否到达目标点
    /// 4. 计算并更新节点的G值（实际移动成本）
    /// 5. 计算H值（启发函数值）并设置父节点关系
    /// 6. 将符合条件的节点加入开放列表
    /// 
    /// 移动成本规则：
    /// • 道路地形：G值增加10000
    /// • 其他地形：G值增加15000
    /// </remarks>
    private bool DoAStar(
        GridCell cell,
        int endX,
        int endY,
        int offsetX,
        int offsetY,
        int nextDir,
        bool ignoreHighArea,
        bool ignoreBlock)
    {
        int x = cell.X + offsetX;
        int y = cell.Y + offsetY;

        // Check the new (x,y) is not beyond the board.
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return false;
        }

        // Check not the blocked, and not in the close list.
        var cellType = this.GetCellType(x, y);
        if (cellType == GridCellType.Obstacle || cellType == GridCellType.ClientBlock || (!ignoreHighArea && cellType == GridCellType.HighArea))
        {
            return false;
        }

        int nextIndex = x + y * this.width;
        var nextCell = this.cells[nextIndex];
        if (nextCell.Block && !ignoreBlock)
        {
            return false;
        }

        if (x == endX && y == endY)
        {
            nextCell.Parent = cell.Index;
            nextCell.Dir = nextDir;
            this.cells[nextIndex] = nextCell;
            return true;
        }

        // Calculate the G: cost from start point to thie cell.
        var g = cell.G;
        if (cellType == GridCellType.Road)
        {
            g += 10000;
        }
        else
        {
            g += 15000;
        }

        if (nextCell.G == 0 || nextCell.G > g)
        {
            // Update the g and parent.
            nextCell.G = g;
            nextCell.Dir = nextDir;
            nextCell.Parent = cell.Index;
            if (nextCell.H == 0)
            {
                nextCell.H = 10000 * Manhattan(x, y, endX, endY);
            }

            this.cells[nextIndex] = nextCell;
            this.openList.Enqueue(nextIndex);
        }

        return false;
    }


    /// <summary>
    /// 计算曼哈顿距离（A*算法的启发函数）
    /// </summary>
    /// <param name="x">当前点X坐标</param>
    /// <param name="y">当前点Y坐标</param>
    /// <param name="endX">目标点X坐标</param>
    /// <param name="endY">目标点Y坐标</param>
    /// <returns>曼哈顿距离值</returns>
    /// <remarks>
    /// 计算公式：|endX - x| + |endY - y|
    /// 曼哈顿距离是A*算法中常用的启发函数，适合网格寻路
    /// 相比欧几里得距离计算更快，且保证启发函数的可容许性
    /// </remarks>
    private static int Manhattan(int x, int y, int endX, int endY)
    {
        return (int)(Math.Abs(endX - x) + Math.Abs(endY - y));
    }

    /// <summary>
    /// 获取指定位置的地形类型
    /// </summary>
    /// <param name="x">目标位置的X坐标</param>
    /// <param name="y">目标位置的Y坐标</param>
    /// <returns>地形类型枚举值</returns>
    /// <remarks>
    /// 优先级检查顺序：
    /// 1. 边界检查：超出范围返回障碍物类型
    /// 2. 动态阻挡：如果设置了动态阻挡，返回障碍物类型
    /// 3. 静态地形：返回地图配置的实际地形类型
    /// </remarks>
    private GridCellType GetCellType(int x, int y)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return GridCellType.Obstacle;
        }

        var index = x + y * this.width;
        if (this.dynamicBlocks[index] > 0)
        {
            return GridCellType.Obstacle;
        }

        return (GridCellType)this.blocks[index];
    }

    /// <summary>
    /// 检查指定位置是否被阻挡（寻路专用版本）
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <returns>如果位置被阻挡返回true，否则返回false</returns>
    /// <remarks>
    /// 与IsBlock方法的区别：
    /// • 仅检查障碍物和客户端阻挡类型
    /// • 不检查高地地形阻挡
    /// • 专门用于寻路算法的阻挡判定
    /// </remarks>
    public bool IsBlockFindWay(int x, int y)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return true;
        }

        var index = x + y * this.width;
        if (this.dynamicBlocks[index] > 0)
        {
            return true;
        }

        return this.blocks[index] == (int)GridCellType.Obstacle || this.blocks[index] == (int)GridCellType.ClientBlock;
    }

    /// <summary>
    /// 检查指定位置是否为水波纹区域
    /// </summary>
    /// <param name="x">检查位置的X坐标</param>
    /// <param name="y">检查位置的Y坐标</param>
    /// <returns>如果是水波纹区域返回true，否则返回false</returns>
    /// <remarks>
    /// 超出网格边界的位置被视为非水波纹区域
    /// </remarks>
    public bool IsWaterRipple(int x, int y)
    {
        if (x < 0 || x >= this.width || y < 0 || y >= this.height)
        {
            return false;
        }

        var index = x + y * this.width;
        return this.blocks[index] == (int)GridCellType.WaterRipple;
    }

    /// <summary>
    /// 使用螺旋扩散算法搜索距离指定点最近的有效通行点
    /// </summary>
    /// <param name="x">搜索起始点的X坐标</param>
    /// <param name="y">搜索起始点的Y坐标</param>
    /// <param name="range">最大搜索范围（格数）</param>
    /// <param name="out_x">输出找到的有效点X坐标</param>
    /// <param name="out_y">输出找到的有效点Y坐标</param>
    /// <remarks>
    /// 搜索策略：
    /// • 如果起始点本身可通行，直接返回起始点
    /// • 按距离从近到远分层搜索（1格、2格、3格...）
    /// • 每层按8个方向顺序检测：右→右下→下→左下→左→左上→上→右上
    /// • 找到第一个有效点立即返回，保证距离最近
    /// 
    /// 典型应用场景：
    /// • 角色出生点位置修正
    /// • 传送点阻挡时的候选位置查找
    /// • NPC刷新点的安全位置确定
    /// </remarks>
    public void FindNearestValidPoint(int x, int y, int range, out int out_x, out int out_y)
    {
        if (!IsBlock(x, y))
        {
            out_x = x;
            out_y = y;
            return;
        }

        // 8方向检测顺序优化
        for (int i = 1; i <= range; ++i)
        {
            if (IsValidPos(x, y, 1, 0, i, out out_x, out out_y))    // 右
                return;
            if (IsValidPos(x, y, 1, 1, i, out out_x, out out_y))    // 右下
                return;
            if (IsValidPos(x, y, 0, 1, i, out out_x, out out_y))    // 下
                return;
            if (IsValidPos(x, y, -1, 0, i, out out_x, out out_y))   // 左
                return;
            if (IsValidPos(x, y, -1, -1, i, out out_x, out out_y))  // 左上
                return;
            if (IsValidPos(x, y, 0, -1, i, out out_x, out out_y))   // 上
                return;
            if (IsValidPos(x, y, 1, -1, i, out out_x, out out_y))   // 右上
                return;
            if (IsValidPos(x, y, -1, 1, i, out out_x, out out_y))   // 左下
                return;
        }

        out_x = x;
        out_y = y;
    }

    /// <summary>
    /// 检测指定方向和距离的位置是否有效（FindNearestValidPoint的辅助方法）
    /// </summary>
    /// <param name="x">起始点X坐标</param>
    /// <param name="y">起始点Y坐标</param>
    /// <param name="offsetX">X方向的单位偏移量</param>
    /// <param name="offsetY">Y方向的单位偏移量</param>
    /// <param name="index">距离倍数（实际偏移 = offset * index）</param>
    /// <param name="out_x">输出计算后的X坐标</param>
    /// <param name="out_y">输出计算后的Y坐标</param>
    /// <returns>如果目标位置可通行返回true，否则返回false</returns>
    private bool IsValidPos(int x, int y, int offsetX, int offsetY, int index, out int out_x, out int out_y)
    {
        out_x = offsetX * index + x;
        out_y = offsetY * index + y;
        return !IsBlock(out_x, out_y);
    }

    #region 在target点的range范围内，找到距离A点最近的有效点
    /// <summary>
    /// 在目标点的指定范围内，找到距离起始点最近的有效可通行位置
    /// </summary>
    /// <param name="aX">起始点A的X坐标</param>
    /// <param name="aY">起始点A的Y坐标</param>
    /// <param name="targetX">目标点的X坐标</param>
    /// <param name="targetY">目标点的Y坐标</param>
    /// <param name="range">搜索范围（格数）</param>
    /// <param name="optimalX">输出最优位置的X坐标</param>
    /// <param name="optimalY">输出最优位置的Y坐标</param>
    /// <remarks>
    /// 搜索策略与优先级：
    /// 1. 主方向搜索：从目标点向起始点方向（优先级最高）
    /// 2. 反方向搜索：从目标点背离起始点方向
    /// 3. 目标点本身：如果可通行且在网格内
    /// 
    /// 搜索范围与方向示意图（以A为起始点，B为目标点）：
    /// ```
    ///    2 3 4
    /// A  1 B 5
    ///    2 3 4
    /// ```
    /// 数值越小优先级越高
    /// 
    /// 典型应用场景：
    /// • 技能释放时寻找最佳施法位置
    /// • NPC交互时的最优站立点
    /// • 攻击时的理想位置选择
    /// • 建筑放置的邻近可用点
    /// 
    /// 算法特点：
    /// • 优先考虑接近起始点的方向
    /// • 支持多层距离的智能搜索
    /// • 包含边界和阻挡检测
    /// </remarks>
    public void FindOptimalPointNearTarget(int aX, int aY, int targetX, int targetY, int range, out int optimalX, out int optimalY)
    {
        optimalX = targetX;
        optimalY = targetY;

        if (range <= 0) return;

        // 计算方向向量
        int dx = aX - targetX;
        int dy = aY - targetY;
        if (dx == 0 && dy == 0) return;

        // 获取主方向及反方向
        Vector2 direction = new Vector2(aX - targetX, aY - targetY).normalized;
        int mainDir = GetDirectionFromVector(direction);
        int reverseDir = (mainDir + 4) % 8;

        // 主方向搜索
        if (CheckDirection(targetX, targetY, range, mainDir, out int tmpX, out int tmpY))
        {
            optimalX = tmpX;
            optimalY = tmpY;
            return;
        }

        // 反方向搜索
        if (CheckDirection(targetX, targetY, range, reverseDir, out tmpX, out tmpY))
        {
            optimalX = tmpX;
            optimalY = tmpY;
            return;
        }

        if (!IsBlock(targetX, targetY) && !IsNotInGrid(targetX, targetY))
        {
            optimalX = targetX;
            optimalY = targetY;
            return;
        }
    }

    private static readonly int[,] DirectionOffsets = new int[8, 2]
    {
            {1, 0},   // 右 (0)
            {1, 1},   // 右下 (1)
            {0, 1},   // 下 (2)
            {-1, 1},  // 左下 (3)
            {-1, 0},  // 左 (4)
            {-1, -1}, // 左上 (5)
            {0, -1},  // 上 (6)
            {1, -1}   // 右上 (7)
    };
    private bool CheckDirection(int baseX, int baseY, int range, int mainDir, out int foundX, out int foundY)
    {
        var dirOrder = GetDirectionOrder(mainDir);

        // 精确步长检测（优先远距离）
        for (int distance = range; distance >= 1; distance--)
        {
            foreach (int dir in dirOrder)
            {
                int x = baseX + DirectionOffsets[dir, 0] * distance;
                int y = baseY + DirectionOffsets[dir, 1] * distance;

                int dx = x - baseX;
                int dy = y - baseY;
                float distanceToBPow2 = dx * dx + dy * dy;
                if (distanceToBPow2 > range * range)
                    continue;

                if (!IsBlock(x, y) && !IsNotInGrid(x, y))
                {
                    foundX = x;
                    foundY = y;
                    return true;
                }
            }
        }

        // 全范围扫描（确保不漏点）
        var candidates = GenerateValidCandidates(baseX, baseY, range, dirOrder);
        foreach (var point in candidates)
        {
            if (!IsBlock(point.x, point.y))
            {
                foundX = point.x;
                foundY = point.y;
                return true;
            }
        }

        foundX = baseX;
        foundY = baseY;
        return false;
    }

    /// <summary>
    /// 根据方向向量计算对应的8方向索引
    /// </summary>
    /// <param name="dir">标准化的方向向量</param>
    /// <returns>方向索引（0-7），对应右、右下、下、左下、左、左上、上、右上</returns>
    /// <remarks>
    /// 方向映射：
    /// • 0: 右 (→)
    /// • 1: 右下 (↘)  
    /// • 2: 下 (↓)
    /// • 3: 左下 (↙)
    /// • 4: 左 (←)
    /// • 5: 左上 (↖)
    /// • 6: 上 (↑)
    /// • 7: 右上 (↗)
    /// 
    /// 算法原理：
    /// 1. 计算向量与右方向的夹角
    /// 2. 将负角度转换为正角度
    /// 3. 按45度间隔划分为8个方向扇区
    /// </remarks>
    private int GetDirectionFromVector(Vector2 dir)
    {
        float angle = Vector2.SignedAngle(Vector2.right, dir);
        if (angle < 0) angle += 360;
        return Mathf.RoundToInt(angle / 45) % 8;
    }

    /// <summary>
    /// 获取以主方向为中心的优先级搜索方向序列
    /// </summary>
    /// <param name="mainDir">主要搜索方向（0-7）</param>
    /// <returns>按优先级排序的方向索引列表</returns>
    /// <remarks>
    /// 搜索优先级策略：
    /// 1. 主方向（优先级最高）
    /// 2. 主方向左右各±1的相邻方向
    /// 3. 主方向左右各±2的次相邻方向  
    /// 4. 主方向左右各±3的远相邻方向
    /// 5. 主方向正对面方向（优先级最低）
    /// 
    /// 例如主方向为0（右）时的搜索顺序：
    /// • 0: 右
    /// • 7: 右上, 1: 右下
    /// • 6: 上, 2: 下
    /// • 5: 左上, 3: 左下  
    /// • 4: 左
    /// 
    /// 用于FindOptimalPointNearTarget方法的方向搜索优化
    /// </remarks>
    private List<int> GetDirectionOrder(int mainDir)
    {
        return new List<int>
        {
            mainDir,
            (mainDir - 1 + 8) % 8,
            (mainDir + 1) % 8,
            (mainDir - 2 + 8) % 8,
            (mainDir + 2) % 8,
            (mainDir - 3 + 8) % 8,
            (mainDir + 3) % 8,
            (mainDir - 4 + 8) % 8
        };
    }

    /// <summary>
    /// 生成指定范围内的所有候选点并按优先级排序
    /// </summary>
    /// <param name="baseX">基准点X坐标</param>
    /// <param name="baseY">基准点Y坐标</param>
    /// <param name="range">搜索范围（格数）</param>
    /// <param name="dirOrder">方向优先级序列</param>
    /// <returns>按优先级和距离排序的候选点列表</returns>
    /// <remarks>
    /// 生成算法：
    /// 1. 遍历以baseX,baseY为中心的range×range正方形区域
    /// 2. 过滤超出圆形范围的点（距离²>range²）
    /// 3. 排除网格边界外的无效点
    /// 4. 根据方向优先级和距离进行排序
    /// 
    /// 排序规则：
    /// • 主要排序：方向优先级（dirOrder中的索引）
    /// • 次要排序：到基准点的欧几里得距离
    /// 
    /// 用于FindOptimalPointNearTarget的候选点生成
    /// </remarks>
    private List<Vector2Int> GenerateValidCandidates(int baseX, int baseY, int range, List<int> dirOrder)
    {
        Dictionary<Vector2Int, int> candidateDict = new Dictionary<Vector2Int, int>();

        for (int dx = -range; dx <= range; dx++)
        {
            for (int dy = -range; dy <= range; dy++)
            {
                if (dx == 0 && dy == 0) continue;

                float distancePow2 = dx * dx + dy * dy;
                if (distancePow2 > range * range) continue;

                Vector2Int point = new Vector2Int(baseX + dx, baseY + dy);
                if (IsNotInGrid(point.x, point.y)) continue;

                // 优先级
                int dir = GetDirectionFromVector(new Vector2(dx, dy));
                int priority = dirOrder.IndexOf(dir);
                if (priority == -1) priority = int.MaxValue;

                candidateDict[point] = priority;
            }
        }

        // 按优先级和距离排序
        return candidateDict.Keys
            .OrderBy(p => candidateDict[p])
            .ThenBy(p => GetDistance(baseX, baseY, p.x, p.y))
            .ToList();
    }
    #endregion

    /// <summary>
    /// 计算两点之间的欧几里得距离
    /// </summary>
    /// <param name="x1">第一个点的X坐标</param>
    /// <param name="y1">第一个点的Y坐标</param>
    /// <param name="x2">第二个点的X坐标</param>
    /// <param name="y2">第二个点的Y坐标</param>
    /// <returns>两点间的欧几里得距离</returns>
    private float GetDistance(int x1, int y1, int x2, int y2)
    {
        var dx = x2 - x1;
        var dy = y2 - y1;
        return (float)Math.Sqrt(dx * dx + dy * dy);
    }
}
