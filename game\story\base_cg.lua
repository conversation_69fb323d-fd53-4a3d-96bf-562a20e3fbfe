BaseCg = BaseCg or BaseClass()

local HideMianRolePart = {
	SceneObjPart.Wing,
	SceneObjPart.Mount,
	SceneObjPart.FightMount,
	SceneObjPart.Halo,
	SceneObjPart.BaoJu,
	SceneObjPart.Foot,
	SceneObjPart.Waist,
	SceneObjPart.Mask,
	SceneObjPart.Tail,
	SceneObjPart.ShouHuan,
	SceneObjPart.Jianling,
	SceneObjPart.SkillHalo,
	SceneObjPart.GodOrDemonHalo,
	SceneObjPart.FoorTrail,
}
function BaseCg:__init(bundle_name, asset_name, data)
	self.bundle_name = bundle_name
	self.asset_name = asset_name
	self.data = data
	self.end_callback = nil
	self.start_callback = nil
	self.skip_callback = nil
	self.reset_callback = {}
	self.cg_ctrl = nil
	self.cg_obj = nil
	self.timer_quest = nil
	self.is_deleted = false
	self.is_main_role_join = false

	self.is_sheild_all_monster_infb = false
	self.is_sheild_all_npc_infb = false
	self.is_sheild_all_other_role_infb = false
	self.is_shield_mainrole = false
	self.is_show_beauty = false

	self.old_position = nil
	self.old_rotation = nil
	self.old_scale = nil

	self.old_shield_others = false
	self.old_shield_monster = false

	self.shield_rule = SimpleRule.New(ShieldObjType.SceneObj, ShieldRuleWeight.Max, BindTool.Bind(self.ShieldSceneObj, self))

	-- 是否是跳跃cg
	self.is_jump_cg = false

	self.need_check_occlusion = false

	-- 隐藏主角的CG场景名字
	self.is_main_role_hide_cg = {

	}

	-- 隐藏主角武器
	self.is_role_weapon_hide_cg = {

	}

	-- 显示怪物的CG场景名字
	self.is_monster_show_cg = {
	}

	-- 不重置位置的CG场景名字
	self.is_not_reset_transform_cg = {
		["A3_CG_XinShouTiYan_M2"] = 1,
		["A3_CG_XinShouTiYan_F2"] = 1,
		["A3_CG_XinShouTiYan_M"] = 1,
		["A3_CG_XinShouTiYan_F"] = 1,
	}

	-- 显示NPC的CG场景名字
	self.is_npc_show_cg = {
	}

	-- 不隐藏其它角色cg
	self.is_not_sheild_all_other_role_cg = {
	}

	-- 渐变
	self.is_fade_end_cg = {
	}

	-- 可移动
	self.can_move_cg = {
	}

	-- 显示护送
	self.can_show_beauty_cg = {
	}

	Runner.Instance:AddRunObj(self)
end

function BaseCg:__delete()
	Runner.Instance:RemoveRunObj(self)
	self.end_callback = nil
	self.start_callback = nil
	self.skip_callback = nil
	self.reset_callback = {}
	self.cg_ctrl = nil
	self.is_deleted = true
	self:StopTimerQuest()
	self:StopSetPosTimerQuest()
	self:DestoryCg()
	CgManager.Instance:DelCacheCg(self.bundle_name)
	if self.shield_rule then
		self.shield_rule:DeleteMe()
		self.shield_rule = nil
	end
end

function BaseCg:DestoryCg()
	if nil ~= self.cg_obj then
		ResMgr:Destroy(self.cg_obj, ResPoolReleasePolicy.DestroyQuick)
		self.cg_obj = nil
	end
end

function BaseCg:StopTimerQuest()
	if nil ~= self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end

function BaseCg:StopSetPosTimerQuest()
	if nil ~= self.timer_pos_quest then
		GlobalTimerQuest:CancelQuest(self.timer_pos_quest)
		self.timer_pos_quest = nil
	end
end

function BaseCg:Play(end_callback, start_callback, is_jump_cg, skip_callback,show_ui,not_auto_end, change_callback)
	self.end_callback = end_callback
	self.start_callback = start_callback
	self.change_callback = change_callback
	self.is_jump_cg = is_jump_cg or false
	self.skip_callback = skip_callback or nil
	self.show_ui = show_ui or false
	self.not_auto_end = not_auto_end or false

	--[[
	-- 第一次运行游戏时加载慢导致屏蔽不及时所以提前设置
	local task_id = TaskWGData.Instance:GetCurrTaskId()
	local task_name = ""
	if task_id ~= nil and task_id ~= "" and task_id > 0 then
		local cfg = TaskWGData.Instance:GetTaskConfig(task_id)
		if cfg ~= nil then
			task_name = cfg.task_name
		end
	end
	self.start_time = os.clock()
	--]]

	if not self.show_ui then
		SafeBaseView.SetAllUICameraEnable(false)
		ViewManager.Instance:CloseUiSceneView()
	end


	local excute_asset_name = self.asset_name
	if self.asset_name == "A3_CG_XinShouTiYan_M2" or self.asset_name == "A3_CG_XinShouTiYan_M" then
		local sex, prof = RoleWGData.Instance:GetRoleSexProf()
		if sex == 0 then
			if self.asset_name == "A3_CG_XinShouTiYan_M2" then
				excute_asset_name = "A3_CG_XinShouTiYan_F2"
			else
				excute_asset_name = "A3_CG_XinShouTiYan_F"
			end
		end
	end

	ResMgr:LoadGameobjSync(self.bundle_name, excute_asset_name, function(obj)
		if self.is_deleted then
			if nil ~= obj then
				ResMgr:Destroy(obj, ResPoolReleasePolicy.DestroyQuick)
			end

			SafeBaseView.SetAllUICameraEnable(true)
			return
		end

		if nil == obj then
			print_error("CgManager Play obj is nil", self.bundle_name, self.asset_name)
			self:OnPlayEnd()
			SafeBaseView.SetAllUICameraEnable(true)
			return
		end

		--隐藏场景所有跟随宠物
		local main_role = Scene.Instance:GetMainRole()
		local pet_list = main_role:GetPetObjList()
		if pet_list then
			for k, v in pairs(pet_list) do
				v:ForceSetVisible(false)
			end
		end

		local beast_obj = main_role:GetBeastObjList()
		if beast_obj then
			beast_obj:ForceSetVisible(false)
		end

		-- 隐藏羽翼
		local wing_part = main_role:GetDrawObj():GetPart(SceneObjPart.Wing)
		if wing_part and wing_part:GetVisible() then
			wing_part:SetVisible(false)

			table.insert(self.reset_callback, function()
				if wing_part then
					wing_part:SetVisible(true)
				end
			end)
		end

		self.cg_obj = obj
		self.cg_obj.transform:SetParent(SceneObjLayer.transform)
		self.cg_ctrl = obj:GetComponent(typeof(CGController))
		if self.cg_ctrl == nil then
			print_error("CgManager Play not exist CGController")
			self:DestoryCg()
			self:OnPlayEnd()
			SafeBaseView.SetAllUICameraEnable(true)
			return
		end

		--添加CG跳过按钮
		local skip_btn_trans = self.cg_obj.transform:Find("UI_Root/UI_Canvas/Skin_Btn")
		if not IsNil(skip_btn_trans) then
			local skip_btn_obj = U3DObject(skip_btn_trans.gameObject, skip_btn_trans)
			if not IsNil(skip_btn_obj.button) then
				skip_btn_obj.button:AddClickListener(BindTool.Bind(self.OnSkipBtnClick, self))
			end
		end

		self:HandleSpecialCgOnPlay()
		self:StopTimerQuest()
		self.timer_quest = GlobalTimerQuest:AddRunQuest(function()
			self:CheckPlay()
		end, 0)
	end)
end

function BaseCg:Stop()
	if nil ~= self.cg_ctrl then
		self.cg_ctrl:Stop()
		self:DestoryCg()
		self.cg_ctrl = nil
	end

	MountWGCtrl.Instance:CheckMountUpOrDownInCg()
	-- 摄象机直接同步到角色，不缓动
	if not IsNil(MainCameraFollow) then
		MainCameraFollow:SyncImmediate()
	end
	
	self:ResumeVisible()
end

function BaseCg:CheckPlay()
	local main_role = Scene.Instance:GetMainRole()
	if not self.is_shield_mainrole and (nil == main_role or not main_role:IsVaildObj()) then
		SafeBaseView.SetAllUICameraEnable(true)
		return
	end

	self:StopTimerQuest()
	if not self.not_auto_end then
		self.cg_ctrl:SetPlayEndCallback(BindTool.Bind(self.OnPlayEnd, self))
	end
	self:OnPlayStart()
	self.cg_ctrl:Play()
end

function BaseCg:Update(now_time, elapse_time)

end

function BaseCg:RemoveOcclusion()
	self.need_check_occlusion = true
	local main_role = Scene.Instance:GetMainRole()
	if main_role == nil then
		return
	end

	local draw_obj = main_role:GetDrawObj()
	if draw_obj == nil then
		return
	end

	local main_part = draw_obj:GetPart(SceneObjPart.Main)
	if main_part ~= nil then
		main_part:RemoveOcclusion()
	end
end

function BaseCg:ResetOcclusion()
	local state = self.need_check_occlusion
	self.need_check_occlusion = false

	if state then
		local main_role = Scene.Instance:GetMainRole()
		if main_role == nil then
			return
		end

		local draw_obj = main_role:GetDrawObj()
		if draw_obj == nil then
			return
		end

		local main_part = draw_obj:GetPart(SceneObjPart.Main)
		if main_part ~= nil then
			main_part:AddOcclusion()
		end
	end
end

function BaseCg:OnPlayStart()
	local main_role = Scene.Instance:GetMainRole()
	if nil == main_role then
		return
	end

	local scene_type = Scene.Instance:GetSceneType()
	-- 婚宴场景不设置主角形象，播放默认的cg形象
	if scene_type ~= SceneType.HunYanFb then
		self:RemoveOcclusion()
		self:ModifyTrack()
	end

	if self.data then
		if self.data.obj_type == SceneObjType.Role and self.data.role_id then
			self:ModifyRoleTrack(self.data.role_id)
		end
	end

	-- 有主角参与的cg将下马参与
	if self.is_main_role_join and not self.is_jump_cg then
		MountWGCtrl.Instance:CheckMountUpOrDownInCg()
	end

	if main_role.vo.appearance_param > 0 then		--变身中强制取消变身形象
		main_role:SetAttr("special_appearance", main_role.vo.special_appearance)
	end

	if self.start_callback then
		self.start_callback(self.cg_obj)
	end

	if not self.can_move_cg[self.asset_name] then
		main_role:StopMove()
	end

	if not IsNil(MainCamera) then
		MainCamera.gameObject:SetActive(false)
	end

	-- if self.asset_name == "F2_CG_zhucheng" then
	-- 	self.fog_end_distance = UnityEngine.RenderSettings.fogEndDistance
	-- 	UnityEngine.RenderSettings.fogEndDistance = 600
	-- end

	StoryWGCtrl.Instance:SendCSNewPlayerFbReqWudi(0) 	-- 请求无敌buff

	Scene.Instance:UpdateSceneCullingFactor()
end

function BaseCg:OnSkipBtnClick()
	if self.skip_callback then
		self.skip_callback()
	end
end

function BaseCg:HandleSpecialCgOnPlay()
	-- 隐藏主角
	self.is_shield_mainrole = false
	if self.is_main_role_hide_cg[self.asset_name] then
		self.is_shield_mainrole = true
	end

	self.is_shield_role_weapon = false
	if self.is_role_weapon_hide_cg[self.asset_name] then
		self.is_shield_role_weapon = true
	end

	-- 隐藏怪物
	self.is_sheild_all_monster_infb = true
	if self.is_monster_show_cg[self.asset_name] then
		self.is_sheild_all_monster_infb = false
	end

	-- 隐藏NPC
	self.is_sheild_all_npc_infb = true
	if self.is_npc_show_cg[self.asset_name] then
		self.is_sheild_all_npc_infb = false
	end

	-- 屏蔽其他玩家
	self.is_sheild_all_other_role_infb = true
	if self.is_not_sheild_all_other_role_cg[self.asset_name] then
		self.is_sheild_all_other_role_infb = false
	end

	-- 屏蔽其他玩家
	self.is_show_beauty = false
	if self.can_show_beauty_cg[self.asset_name] then
		self.is_show_beauty = true
	end

	self.shield_rule:Register()
end

function BaseCg:OnPlayEnd()
	StoryWGCtrl.Instance:SendCSNewPlayerFbReqWudi(1) 	-- 撤销无敌buff

	-- 播放完CG防止玩家误点，导致自动任务被停掉，因为每次问策划都说没点过，所以直接拦了
	if CgManager ~= nil and CgManager.Instance ~= nil then
		CgManager.Instance:ResetSceneClickTime()
	end

	-- if self.old_position == nil then
	-- 	if nil ~= self.end_callback then
	-- 		self:CallResetCallabck()
	-- 		self.end_callback()

	-- 		local main_role = Scene.Instance:GetMainRole()
	-- 		local pet_list = main_role:GetPetObjList()
	-- 		if pet_list then
	-- 			for k, v in pairs(pet_list) do
	-- 				v:ForceSetVisible(true)
	-- 			end
	-- 		end

	-- 		local beast_obj = main_role:GetBeastObjList()
	-- 		if beast_obj then
	-- 			beast_obj:ForceSetVisible(true)
	-- 		end
	-- 	end

	-- 	SafeBaseView.SetAllUICameraEnable(true)
	-- end

	local main_role = Scene.Instance:GetMainRole()
	if not self.can_move_cg[self.asset_name] then
		main_role:StopMove()
		main_role:ChangeToCommonState()
	end

	if self.is_main_role_join then
		-- 还原节点
		local transform = main_role:GetRoot().gameObject.transform
		transform:SetParent(SceneObjLayer.transform)
		
		if self.old_position ~= nil then
			transform.localPosition = self.old_position
			transform.localRotation = self.old_rotation
			transform.localScale = self.old_scale
		end
	end

	local draw_obj = main_role:GetDrawObj()
	if self.is_main_role_join and draw_obj then 
		--(not self.is_not_reset_transform_cg[self.asset_name]) then --or not self.can_move_cg[self.asset_name]
		for k, v in pairs(HideMianRolePart) do
			local part = draw_obj:GetPart(v)
			if part then
				part:SetVisible(true)
			end
		end

		if self.is_shield_role_weapon then
			local weapon = draw_obj:GetPart(SceneObjPart.Weapon)
			weapon:SetVisible(true)
		end
	end

	main_role:UpdateCameraFollowTarget(true)
	main_role:ForceSetLOD(nil, 0)

	GlobalEventSystem:Fire(ObjectEventType.CG_EVENT_END, self.bundle_name, self.asset_name)
	self.cg_ctrl = nil
	SafeBaseView.SetAllUICameraEnable(true)

	if self.sky_box then
		self.sky_box.gameObject:SetActive(true)
		self.sky_box = nil
	end

	self:ResumeVisible()
	-- CG 结束是否自动任务在回调中做
	-- if TaskGuide.Instance:NoviceCheckTask() and Scene.Instance:GetSceneType() == SceneType.Common then
	 --        TaskGuide.Instance:CanAutoAllTask(true)
 	--    end

 	if self.shield_rule then
		self.shield_rule:DeleteMe()
		self.shield_rule = nil
	end

	if main_role:IsRiding() then
		main_role:OnMountUpEnd()
	end

	if nil ~= self.end_callback then
		self.end_callback()
	end

	-- 有主角参与的cg将下马参与
	if self.is_main_role_join then
		MountWGCtrl.Instance:CheckMountUpOrDownInCg()
	end

	if self.is_fade_end_cg[self.asset_name] then
		FunctionGuide.Instance:OpenFadeView()
	end

	if main_role.vo.appearance_param > 0 then		--变身中强制取消变身形象
		main_role:SetAttr("special_appearance", main_role.vo.special_appearance)
	end

	self:ResetOcclusion()
end

function BaseCg:ResumeVisible()
	-- 摄象机直接同步到角色，不缓动
	if not IsNil(MainCamera) then
		MainCamera.gameObject:SetActive(true)
		if not IsNil(MainCameraFollow) then
			MainCameraFollow:SyncImmediate()
		end
	end

	self:CallResetCallabck()

	local main_role = Scene.Instance:GetMainRole()
	local pet_list = main_role:GetPetObjList()
	if pet_list then
		for k, v in pairs(pet_list) do
			v:ForceSetVisible(true)
		end
	end

	local beast_obj = main_role:GetBeastObjList()
	if beast_obj then
		beast_obj:ForceSetVisible(true)
	end
end

function BaseCg:ShieldSceneObj(obj)
	if not self.is_sheild_all_monster_infb and obj:IsMonster() then
		return false
	end

	if not self.is_sheild_all_npc_infb and obj:IsNpc() then
		return false
	end

	if not self.is_sheild_all_other_role_infb and obj:IsRole() then
		return false
	end

	if not self.is_shield_mainrole and obj:IsMainRole() then
		return false
	end

	--隐藏跟随物
	if obj:IsPet() or obj:IsGuard() or obj:IsSoulBoy() or obj:IsBaby() then
		return true
	end

	if self.is_show_beauty and obj:IsBeauty() then
		return false
	end

	return true
end

local RoleTrackName = {
	[0] = { [1] = 3101, [2] = 3102, [3] = 3103, },
	[1] = { [1] = 1101, [3] = 1103, },
}

local RoleWeaponName = {
	[0] = { [1] = 9001, [2] = 9002, [3] = 9003, },
	[1] = { [1] = 9101, [3] = 9103, },
}

function BaseCg:ModifyTrack()
	local main_role = Scene.Instance:GetMainRole()
	if nil == main_role then
		return
	end

	local main_obj = main_role:GetDrawObj():GetPart(SceneObjPart.Main):GetObj()
	local main_draw_obj = main_role:GetDrawObj()
	if nil == main_obj or IsNil(main_obj.gameObject) or not self.cg_ctrl then
		return
	end

	local vo = main_role:GetVo()
	-- 把主角obj替换到cg里
	local succ1 = self.cg_ctrl:AddActor(main_obj.gameObject, "MainRoleActTrack")
	local succ2 = self.cg_ctrl:AddActor(main_role:GetRoot().gameObject, "MainRoleTrack")

	if main_draw_obj ~= nil and main_draw_obj:GetPart(SceneObjPart.Weapon) ~= nil and main_draw_obj:GetPart(SceneObjPart.Weapon):GetObj() ~= nil then
		local succ_weapon = self.cg_ctrl:AddActor(main_draw_obj:GetPart(SceneObjPart.Weapon):GetObj().gameObject, "MainRoleWeaponActTrack")
	end

	self.is_main_role_join = succ1 or succ2
	if self.is_main_role_join then
		if self.cg_obj and not IsNil(self.cg_obj.gameObject) then
			-- self.cg_obj.transform:SetParent(main_role:GetRoot().gameObject.transform, false)
			self.cg_obj.gameObject.transform.position = Vector3.New(0, 0, 0)
			self.cg_obj.gameObject.transform.localRotation = Quaternion.Euler(0, 0, 0)
			local transform = main_role:GetRoot().gameObject.transform
			transform.localRotation = Quaternion.Euler(0, 0, 0)
		end

		for k, v in pairs(HideMianRolePart) do
			local part = main_draw_obj:GetPart(v)
			if part then
				part:SetVisible(false)
			end
		end

		--[[
		--转职cg特殊处理（有特效的AttachTransform组件要attach到角色武器上，而角色模型是在这里动态赋到CG动画轨迹上）
		--后面如果有其他CG也需要处理，则需要完善代码，改成通用的方式实现
		if self.asset_name == "A1_CG_ZhuanZhi_NvQiang" 
			or self.asset_name == "A1_CG_ZhuanZhi_NvJian"
			or self.asset_name == "A1_CG_ZhuanZhi_NanQiang"
			or self.asset_name == "A1_CG_ZhuanZhi_NanJian" then
			--策划需求，实现背景全黑效果
			self.cg_ctrl.gameObject.transform.localPosition = Vector3.New(3000, 3000, 0)
			main_obj.gameObject.transform.localPosition = Vector3.New(3000, 3000, 0)
			self.sky_box = GameObject.Find("Main/SkyBox")
			if self.sky_box then
				self.sky_box.gameObject:SetActive(false)
			end

			-- if self.asset_name == "A1_CG_ZhuanZhi_NvJian" then
			-- 	local director = self.cg_ctrl:GetPlayableDirector()
			-- 	if director then
			-- 		local attach = director.gameObject:GetComponentInChildren(typeof(AttachTransform))
			-- 		local main_part = main_draw_obj:GetPart(SceneObjPart.Main)
			-- 		local weapon_point = main_part and main_part:GetAttachPoint(AttachPoint.Weapon) or nil
			-- 		if weapon_point then
			-- 			attach.target = weapon_point.transform
			-- 		end
			-- 	end
			-- end
		end
		]]

		if self.is_shield_role_weapon then
			local weapon = main_draw_obj:GetPart(SceneObjPart.Weapon)
			weapon:SetVisible(false)
		end

		local transform = main_role:GetRoot().gameObject.transform
		-- 尝试同步一下对象位置
		if self.cg_obj then
			local stage_root = self.cg_obj.transform:Find("stage")
			if stage_root then
				-- 配合设置传送位置导致位置更新出问题
				self:StopSetPosTimerQuest()
				self.timer_pos_quest = GlobalTimerQuest:AddRunQuest(function()
					if self.old_position == nil then
						self.old_position = transform.localPosition
						self.old_rotation = transform.localRotation
						self.old_scale = transform.localScale
					end

					transform:SetParent(stage_root)
					transform.localRotation = Quaternion.Euler(0, 0, 0)
					transform.localPosition = Vector3.zero
				end, 0.2)
			end
		end
	end

	local sex, prof = RoleWGData.Instance:GetRoleSexProf()
	local act_track_name = RoleTrackName[sex][prof]
	-- 开启主角的动作(默认全部静默中)
	for sex, v1 in pairs(RoleTrackName) do
		for prof, v2 in pairs(v1) do
			local track_name = v2
			self.cg_ctrl:SetTrackMute(track_name, act_track_name ~= track_name)
		end
	end

	-- 开启武器的动作
	local act_weapon_track_name = RoleWeaponName[sex][prof]
	for sex, v1 in pairs(RoleWeaponName) do
		for prof, v2 in pairs(v1) do
			local track_name = v2
			self.cg_ctrl:SetTrackMute(track_name, act_weapon_track_name ~= track_name)
		end
	end
end

function BaseCg:ModifyRoleTrack(role_id)
	local role = Scene.Instance:GetRoleByRoleId(role_id)
	if not role then
		return
	end

	local vo = role:GetVo()
	-- 把obj替换到cg里
	local succ1 = self.cg_ctrl:AddActor(role:GetDrawObj():GetPart(SceneObjPart.Main):GetObj().gameObject, "RoleActTrack")
	local succ2 = self.cg_ctrl:AddActor(role:GetRoot().gameObject, "RoleTrack")

	local sex, prof = role:GetVo().sex, role:GetVo().prof % 10
	local act_track_name = RoleTrackName[sex][prof]
	-- 开启模型的动作(默认全部静默中)
	for sex = 0, 1  do
		for prof = 1, 3 do
			local track_name = RoleTrackName[sex][prof]
			self.cg_ctrl:SetTrackMute(track_name, act_track_name ~= track_name)
		end
	end

	-- 开启武器的动作
	local act_weapon_track_name = RoleWeaponName[sex][prof]
	for sex, v1 in pairs(RoleWeaponName) do
		for prof, v2 in pairs(v1) do
			local track_name = v2
			self.cg_ctrl:SetTrackMute(track_name, act_weapon_track_name ~= track_name)
		end
	end
end

function BaseCg:IsCanMoveCG()
	return 1 == self.can_move_cg[self.asset_name]
end

function BaseCg:SetOperaBtnCallback(btn_index, callback)
	if self.cg_ctrl and btn_index and callback then
		self.cg_ctrl:SetOperaBtnCallback(btn_index, callback)
	end
end

function BaseCg:SetCurTimePoint(time)
	if self.cg_ctrl then
		local director = self.cg_ctrl:GetPlayableDirector()
		if director and not IsNil(director) then
			director.time = director.duration >= time and time or director.duration
		end
	end
end

function BaseCg:CallResetCallabck()
	for i,v in ipairs(self.reset_callback) do
		v()
	end
	self.reset_callback = {}
end