------------------------------------------------------------
--七天累充
------------------------------------------------------------
local recharge_all_num = 0
local recharge_reward_fetch_flags = {}

function ServerActivityTabView:SDRLoadCallBack()
	self.seven_day_recharge_item_list = AsyncListView.New(SevenDayRechargeItemRender, self.node_list.sdr_ph_list)
	self.item_cell_list = AsyncListView.New(ItemCell, self.node_list.sdr_item_cell_root)
	-- self.seven_day_recharge_item_list:SetStartZeroIndex(true)
	self:SDRFlushCountDownTime()
	XUI.AddClickEventListener(self.node_list.btn_go_recharge, BindTool.Bind(self.RechrgeOn<PERSON><PERSON><PERSON>enGo, self))
end

function ServerActivityTabView:RechrgeOnClickBenGo()
	RechargeWGCtrl.Instance:Open(TabIndex.recharge_cz)
end

function ServerActivityTabView:SDRReleaseCallBack()
	if self.seven_day_recharge_item_list then
		self.seven_day_recharge_item_list:DeleteMe()
		self.seven_day_recharge_item_list = nil
	end
	if self.item_cell_list then
		self.item_cell_list:DeleteMe()
		self.item_cell_list = nil
	end

	if CountDownManager.Instance:HasCountDown("seven_day_recharge") then
        CountDownManager.Instance:RemoveCountDown("seven_day_recharge")
    end
end

function ServerActivityTabView:SDRShowIndexCallBack()
	self:SDRFlushView()
	self:DoSDRAnim()
	self:SDRFlushShowReward()
end

function ServerActivityTabView:SDROnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "act_info" and v.protocol_id == 2718 then
			self:SDRFlushView()
		end
	end
end

function ServerActivityTabView:SDRFlushShowReward()
	local reward_item = SortTableKey(ServerActivityWGData.Instance:GetOpenSerActOtherCfg("recharge_reward_item"))
	self.item_cell_list:SetDataList(reward_item)
end

function ServerActivityTabView:SDRFlushView()
	local sever_info = ServerActivityWGData.Instance:GetOpenServerData()
	local reward_fetch_flag = sever_info.oga_total_chongzhi_reward_fetch_flag
	recharge_all_num = sever_info.oga_total_chongzhi_num
	recharge_reward_fetch_flags = bit:d2b_two(reward_fetch_flag)
	
	self.node_list.text_recharge.text.text = recharge_all_num

	local consume_cfg = ServerActivityWGData.Instance:GetOpenGameActivityConfig()
	local recharge_cfg = consume_cfg.total_chongzhi
	if not IsEmptyTable(recharge_cfg) then
		-- 如果需要把已领取的放在后面就取消注释 把JumpToIndex去掉
		-- local data_list = {}
		-- for i,data in ipairs(recharge_cfg) do
		-- 	if recharge_reward_fetch_flags[data.seq] == 1 then
		-- 		data_list[100 + i] = data
		-- 	else
		-- 		data_list[i] = data
		-- 	end
		-- end
		-- data_list = SortTableKey(data_list)
		self.seven_day_recharge_item_list:SetDataList(recharge_cfg)

		for i,data in ipairs(recharge_cfg) do
			if recharge_reward_fetch_flags[data.seq] == 0 then
				self.seven_day_recharge_item_list:JumpToIndex(i+1)
				return
			end
		end
	end
	self.seven_day_recharge_item_list:JumpToIndex(0)
end


function ServerActivityTabView:SDRFlushCountDownTime()
	if CountDownManager.Instance:HasCountDown("seven_day_recharge") then
        CountDownManager.Instance:RemoveCountDown("seven_day_recharge")
    end

	local count_down_time = ServerActivityWGData.Instance:GetOpenServerToSevenDayTime(ACTIVITY_TYPE.SEVENDAY_RECHARGE)
	if count_down_time > 0 then
		self.node_list.sdr_version_act_time.text.text = string.format(Language.OpenServer.ActRemainTime2, TimeUtil.FormatSecondDHM8(count_down_time))
		CountDownManager.Instance:AddCountDown(
			"seven_day_recharge",
			BindTool.Bind(self.SDRUpdateCountDown, self),
			BindTool.Bind(self.SDROnComplete, self),
			nil,
			count_down_time,
			1
		)
	else
		self:SDROnComplete()
	end
end

function ServerActivityTabView:SDRUpdateCountDown(elapse_time, total_time)
	self.node_list.sdr_version_act_time.text.text = string.format(Language.OpenServer.ActRemainTime2, TimeUtil.FormatSecondDHM8(total_time - elapse_time))
end

function ServerActivityTabView:SDROnComplete()
	self.node_list.sdr_version_act_time.text.text = Language.OpenServer.KaiFuJiZiEndTimeTex
	self.node_list.sdr_version_act_time.text.color = Str2C3b(COLOR3B.RED)
end

function ServerActivityTabView:DoSDRAnim()
	local tween_info = UITween_CONSTS.KfActivityView
    UITween.FakeHideShow(self.node_list["seven_day_recharge_root"])
    UITween.AlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["seven_day_recharge_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)

	self:DoSDRCellsAnim()
end

function ServerActivityTabView:DoSDRCellsAnim()
    local tween_info = UITween_CONSTS.KfActivityView.ListCellRender
    self.node_list["sdr_ph_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["sdr_ph_list"]:SetActive(true)
        local list =  self.seven_day_recharge_item_list:GetAllItems()
        local sort_list = ServerActivityWGData.Instance:GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalySDRItemAnim(count)
        end
    end, tween_info.DelayDoTime, "SDR_Cell_Tween")
end


------------------------------------------------------------------------------------------------------------

SevenDayRechargeItemRender = SevenDayRechargeItemRender or BaseClass(BaseRender)

function SevenDayRechargeItemRender:__init()
	self.can_get_reward = false
end

function SevenDayRechargeItemRender:__delete()
	if self.item_cell_list then
		self.item_cell_list:DeleteMe()
		self.item_cell_list = nil
	end
end

function SevenDayRechargeItemRender:LoadCallBack()
	self.item_cell_list = AsyncListView.New(ItemCell, self.node_list.item_cell_root)
	self.node_list.btn_lingqu.button:AddClickListener(BindTool.Bind(self.OnClickGetRewardBtn, self))
	self:FlushReward()
end

function SevenDayRechargeItemRender:OnFlush()
	local data = self:GetData()

	local is_get_reward = recharge_reward_fetch_flags[data.seq] == 1
	local can_get_reward = recharge_all_num >= data.chongzhi_gold and not is_get_reward

	self.can_get_reward = can_get_reward

	-- self.node_list.img_lock:SetActive(not is_get_reward and not can_get_reward)

	self.node_list.btn_lingqu:SetActive(can_get_reward)
	self.node_list.img_mask:SetActive(is_get_reward)
	
	-- self.node_list.red_point:SetActive(can_get_reward)
	self.node_list.img_select:SetActive(can_get_reward)
	self.node_list.img_select2:SetActive(can_get_reward)

	-- self.node_list.img_bg.rect.sizeDelta = is_get_reward and Vector2(144,464) or Vector2(144,488)

	-- local show_num = recharge_all_num >= data.chongzhi_gold and data.chongzhi_gold or recharge_all_num
	-- local color = recharge_all_num >= data.chongzhi_gold and COLOR3B.L_GREEN or COLOR3B.L_RED
	--策划需求超过100000改为xx万
	local chongzhi_str = CommonDataManager.ConverGoldByThousand(data.chongzhi_gold)
	self.node_list.target_label.text.text = chongzhi_str

	self:FlushReward()
end

function SevenDayRechargeItemRender:FlushReward()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end

	local is_get_reward = recharge_reward_fetch_flags[data.seq] == 1

	local reward_item = SortTableKey(data.reward_item)

	self.item_cell_list:SetDataList(reward_item)
	self.item_cell_list:SetRefreshCallback(function(item_cell, cell_index)
		if item_cell then
			item_cell:SetLingQuVisible(is_get_reward)
		end
	end)
end

function SevenDayRechargeItemRender:OnClickGetRewardBtn()
	if self.can_get_reward then
		ServerActivityWGCtrl.Instance:SendOpenGameActivityFetchReward(OPEN_SERVER_REWARD_TYPE.REWARD_TYPE_RECHANGER, self.data.seq)
	else
		RechargeWGCtrl.Instance:Open(TabIndex.recharge_cz)
	end
end

function SevenDayRechargeItemRender:PalySDRItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.KfActivityView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.MoveAlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["tween_root"], tween_info)
        end

    end, tween_info.NextDoDelay * wait_index, "sdr_item_" .. wait_index)
end
