PlayerPrefsUtil = {}
local data_dic = {}

function PlayerPrefsUtil.SetInt(key, value)
	if data_dic[key] ~= value then
		data_dic[key] = value
		UnityEngine.PlayerPrefs.SetInt(key, value)
	end
end

function PlayerPrefsUtil.GetInt(key, default)
	if nil ~= data_dic[key] then
		return data_dic[key]
	end

	local default = default or 0
	local value = UnityEngine.PlayerPrefs.GetInt(key, default)
	data_dic[key] = value
	return value
end

function PlayerPrefsUtil.SetFloat(key, value)
	if data_dic[key] ~= value then
		data_dic[key] = value
		UnityEngine.PlayerPrefs.SetFloat(key, value)
	end
end

function PlayerPrefsUtil.GetFloat(key, default)
	if nil ~= data_dic[key] then
		return data_dic[key]
	end

	local default = default or 0
	local value = UnityEngine.PlayerPrefs.GetFloat(key, default)
	data_dic[key] = value
	return value
end

function PlayerPrefsUtil.SetString(key, value)
	if data_dic[key] ~= value then
		data_dic[key] = value
		UnityEngine.PlayerPrefs.SetString(key, value)
	end
end

function PlayerPrefsUtil.GetString(key)
	if nil ~= data_dic[key] then
		return data_dic[key]
	end

	local value = UnityEngine.PlayerPrefs.GetString(key)
	data_dic[key] = value
	return value
end

function PlayerPrefsUtil.HasKey(key)
	if nil ~= data_dic[key] then
		return true
	end

	return UnityEngine.PlayerPrefs.HasKey(key)
end

function PlayerPrefsUtil.DeleteKey(key)
	data_dic[key] = nil
	UnityEngine.PlayerPrefs.DeleteKey(key)
end

function PlayerPrefsUtil.Save()
	UnityEngine.PlayerPrefs.Save()
end