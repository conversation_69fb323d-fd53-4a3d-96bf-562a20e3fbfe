﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UnityEngine_ThreadPriorityWrap
{
	public static void Register(LuaState L)
	{
		L<PERSON>gin<PERSON>num(typeof(UnityEngine.ThreadPriority));
		<PERSON><PERSON>("Low", get_Low, null);
		<PERSON><PERSON>("BelowNormal", get_BelowNormal, null);
		<PERSON><PERSON>("Normal", get_Normal, null);
		<PERSON><PERSON>("High", get_High, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON><PERSON>();
		TypeTraits<UnityEngine.ThreadPriority>.Check = CheckType;
		StackTraits<UnityEngine.ThreadPriority>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.ThreadPriority arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.ThreadPriority), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Low(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.ThreadPriority.Low);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BelowNormal(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.ThreadPriority.BelowNormal);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Normal(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.ThreadPriority.Normal);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_High(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.ThreadPriority.High);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.ThreadPriority o = (UnityEngine.ThreadPriority)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

