BeastObj = BeastObj or BaseClass(FollowObj)

function BeastObj:__init(vo)
	self.obj_type = SceneObjType.BeastObj
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.BeastObj
	self.shield_effect_type = ShieldObjType.BeastEffect

	self.owner_obj_id = vo.owner_objid
	self.battle_index = 1	-- 固定一个了
    self.beast_id = vo.beast_id
	self.beast_skin = vo.beast_skin
	self.bundle_name = ""
	self.asset_name = ""
	self.model_res_id = nil
	self.unit_distance = 2 * 50		-- 单位距离速度
	-- 最大速度
	self.max_speed = 12
	self.default_speed = 9

	-- 移动至目标所用（战斗移动）
	self.move_skill_id = nil
	self.target_x = nil
	self.target_y = nil
	self.target_obj_id = nil

	-- 是否随机漫步
	self.is_wander = false
	self.direction_owner = true
	self.wander_distance = 4
	self.is_find_use_skill_pos = false
	self.fight_continue_time = COMMON_CONSTS.FIGHT_STATE_TIME_SP

	self:InitInfo()
	self:InitAppearance()
end

function BeastObj:__delete()
	local owner_name = ""
	-- if nil ~= self.owner_obj and self.owner_obj:IsRole() then
	-- 	self.owner_obj:RemoveBeast(self)
	-- 	owner_name = self.owner_obj.vo and self.owner_obj.vo.name or "nil"
	-- end
	self.owner_obj = nil
	self.bundle_name = ""
	self.asset_name = ""
	self.model_res_id = nil

	self:ResetFightData()
	self.delete_traceback = owner_name .. "  " .. debug.traceback()
end

-- 重置战斗移动参数
function BeastObj:ResetFightData()
	-- 移动至目标所用（战斗移动）
	self.move_skill_id = nil
	self.target_x = nil
	self.target_y = nil
	self.target_obj_id = nil
end

function BeastObj:InitInfo()
	FollowObj.InitInfo(self)
	self:GetFollowUi()
	self:ReloadUIName()
end

function BeastObj:InitAppearance()
	FollowObj.InitAppearance(self)

	local res_id
	if self.beast_skin and self.beast_skin >= 0 then
		-- beast_skin = skin_seq * 1000 + skin_level
		local skin_seq = math.floor(self.beast_skin / 1000)
		res_id = ControlBeastsWGData.Instance:GetBeastModelSkinResId(skin_seq)
	else
		res_id = ControlBeastsWGData.Instance:GetBeastModelResId(self.beast_id)
	end
	if res_id then
		self:SetActorConfigPrefabData(ConfigManager.Instance:GetPrefabDataAutoConfig("Yushou", res_id))
	end

	if self.model_res_id ~= res_id then
		self.model_res_id = res_id

		local bundle, asset = ResPath.GetBeastsModel(res_id)
		self:ChangeModel(SceneObjPart.Main, bundle, asset)
	end

	local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.beast_id)
	if aim_cfg then
		self.draw_obj:SetName(aim_cfg.beast_name)
		self.radius = aim_cfg.radius or 4
	end
end

function BeastObj:ReloadUIName()
	if self.follow_ui ~= nil then
		local aim_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(self.beast_id)
		local name = aim_cfg and aim_cfg.beast_name or ""
		local beast_element = aim_cfg and aim_cfg.beast_element or 1
		self.follow_ui:SetName(name, self)
		self.follow_ui:SetBeastElementImg(beast_element)
	end
end

function BeastObj:TryFlushAppearance(beast_id, beast_skin)
	if self.beast_id == beast_id and self.beast_skin == beast_skin then
		return
	end

    self.beast_id = beast_id
	self.beast_skin = beast_skin
	self:InitAppearance()
	self:ReloadUIName()
end

function BeastObj:OnEnterScene()
	FollowObj.OnEnterScene(self)
	-- self:CreateShadow()
    -- local fb_scene_cfg = Scene.Instance:GetCurFbSceneCfg()
    -- self.can_bubble = fb_scene_cfg.is_bubble and fb_scene_cfg.is_bubble == 1 or false
end


function BeastObj:EnterStateAttack(anim_name)
	local anim_name = SceneObjAnimator.Atk1
	local skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.attack_skill_id)
	if nil ~= skill_cfg then
		anim_name = "attack" .. skill_cfg.action
	end

	FollowObj.EnterStateAttack(self, anim_name)
end

function BeastObj:GetActionTimeRecord(anim_name)
	local skin_seq = -1
	if self.beast_skin and self.beast_skin >= 0 then
		skin_seq = math.floor(self.beast_skin / 1000)
	end
	local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(self.beast_id, skin_seq)
	if res_id then
		local atr = YushouActionConfig[res_id]
		if atr ~= nil then
			return atr[anim_name]
		end
	end

	return nil
end

function BeastObj:GetCurrAnimTime()
	local skin_seq = -1
	if self.beast_skin and self.beast_skin >= 0 then
		skin_seq = math.floor(self.beast_skin / 1000)
	end
	local res_id = ControlBeastsWGData.Instance:GetBeastModelResId(self.beast_id, skin_seq)
	if res_id and self.anim_name ~= "" then
		local atr = YushouActionConfig[res_id]
		if atr ~= nil then
			return atr[self.anim_name] and atr[self.anim_name].time
		end
	end

	return 0
end

-- 战斗屏蔽随机移动
function BeastObj:ShieldWadnerForce(target)
	return self:IsFightState() or not self:GetVisiable()
end

function BeastObj:IsBeast()
	return true
end

--是否是自己的宠物
function BeastObj:IsOnwerBeast()
	return self.owner_obj_id == GameVoManager.Instance:GetMainRoleVo().obj_id
end

function BeastObj:CreateFollowUi()
    self.follow_ui = BeastObjFollow.New(self.vo)
    self.follow_ui:OnEnterScene(self.is_enter_scene)
    self.follow_ui:Create(SceneObjType.BeastObj)
end

function BeastObj:SetOwnerObj(owner_obj)
	self.owner_obj = owner_obj
end

function BeastObj:EnterStateMove()
	if not self.is_ui3d_model then
		local main_part = self.draw_obj:GetPart(SceneObjPart.Main)

		if main_part then
			main_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Run)
		end
	
		self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Move)
	else
		Character.EnterStateMove(self)
	end
end

function BeastObj:EnterStateStand()
	if not self.is_ui3d_model then
		self.is_move_over_pos = false
		local main_part = self.draw_obj:GetPart(SceneObjPart.Main)
	
		if main_part then
			main_part:SetInteger(ANIMATOR_PARAM.STATUS, ActionStatus.Idle)
		end
	
		self:CrossAction(SceneObjPart.Main, SceneObjAnimator.Idle)
	else
		FollowObj.EnterStateStand(self)
	end
end

function BeastObj:PartEffectVisibleChanged(part, visible)
	if self:IsDeleted() then
		return
	end
	
    self.draw_obj:SetPartIsDisableAttachEffect(part, not visible)
end

function BeastObj:UpdateQualityLevel()
	if self:IsDeleted() then
		return
	end

	local base_offset = -1
	local owner_obj = self:GetOwnerObj()
	if owner_obj and owner_obj.IsMainRole and owner_obj:IsMainRole() then
		base_offset = 1
	end

	local model_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     model_offset = model_offset + QualityWGData.Instance:GetModelQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetQualityLevelOffset(model_offset)

	local effect_offset = base_offset
	-- if self.bundle_name and "" ~= self.bundle_name and self.asset_name and "" ~= self.asset_name then
	--     effect_offset = effect_offset + QualityWGData.Instance:GetModelEffectQualityOffsetConfig(self.bundle_name, self.asset_name)
	-- end
	self:SetEffectQualityLevelOffset(effect_offset)

	-- 模型材质球品质偏移值。
    -- 偏移值越小则材质球品质越高

    if self.draw_obj == nil then
    	return
    end
end

function BeastObj:CreateShadow()
	FollowObj.CreateShadow(self)
	
	if self.shadow then
		self.shadow:AddShieldRule(ShieldRuleWeight.Max, function()
			return not self:GetVisiable()
		end)
	end
end

function BeastObj:TryUseBeastSkill(skill_id, target_x, target_y, target_obj_id,	is_click)
	if self:IsDeleted() then
		return
	end

	if not target_x or not target_y then
		return
	end

	-- local target_obj = GuajiCache.target_obj
	-- if not target_obj or target_obj:IsDeleted() then
	-- 	return
	-- end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local skill_cfg = SkillWGData.Instance:GetBeastsSkillById(skill_id)
	if not skill_cfg then
		return
	end

	local logic = u3d.vec2(target_x, target_y)
	local target_dis = self:GetLogicDistance(logic, true)
	local m_pos_x, m_pos_y = self:GetLogicPos()
	-- local target_movement = u3dpool.v2Sub(u3dpool.vec2(m_pos_x, m_pos_y), u3dpool.vec2(target_x, target_y))
	-- local target_dis = u3dpool.v2Length(target_movement, false)
	local skill_dis = skill_cfg.distance or 1
	if target_dis <= skill_dis then
		self.is_find_use_skill_pos = false
		SceneObj.SetDirectionByXY(self, target_x, target_y)
		self:StopMove()
		self:DoAttack(skill_id, target_x, target_y, target_obj_id)

		if is_click then	-- 主动点击并且为主角的幻兽
			ControlBeastsWGCtrl.Instance:OpenBeastsSkillShowTips(self.beast_id)
			ControlBeastsWGCtrl.Instance:SendCSBeastUseSkill(skill_id, target_obj_id, target_x, target_y)	
		end
	else
		self:StopMove()
		-- 移动至目标所用（战斗移动）
		self.move_skill_id = skill_id
		self.target_x = target_x
		self.target_y = target_y
		self.target_obj_id = target_obj_id
		self.is_main_beast = is_click
		self.is_find_use_skill_pos = true

		self.speed = ((target_dis - skill_dis) / self.sqrt_max_distance) * self.unit_distance

		if self.speed < self.default_speed then
			self.speed = self.default_speed
		end

		if self.speed > self.max_speed then
			self.speed = self.max_speed
		end

		-- self:DoMove(u3dpool.vec2(target_x, target_y))
		Character.DoMove(self, target_x, target_y, OBJ_MOVE_REASON.BEAST_FIGHT)
	end

	self:EnterFight()
end

function BeastObj:GetUpdateNumber()
	self.update_num = 1
end

-- 检测战斗移动
function BeastObj:Update(now_time, elapse_time)
	FollowObj.Update(self, now_time, elapse_time)
	--and self.target_obj_id ~= nil self:IsFightState() and 
	if self:IsFightState() and self.move_skill_id then
		local skill_cfg = SkillWGData.Instance:GetBeastsSkillById(self.move_skill_id)
		if not skill_cfg then
			return
		end

		local logic = u3d.vec2(self.target_x, self.target_y)
		local target_dis = self:GetLogicDistance(logic, true)
		local m_pos_x, m_pos_y = self:GetLogicPos()
		-- local target_movement = u3dpool.v2Sub(u3dpool.vec2(m_pos_x, m_pos_y), u3dpool.vec2(self.target_x, self.target_y))
		-- local target_dis = u3dpool.v2Length(target_movement, false)
		local skill_dis = skill_cfg.distance or 1
		if target_dis <= skill_dis then
			SceneObj.SetDirectionByXY(self, self.target_x, self.target_y)
			self:StopMove()
			self:DoAttack(self.move_skill_id, self.target_x, self.target_y, self.target_obj_id)
			self.is_find_use_skill_pos = false

			if self.is_main_beast and self:IsOnwerBeast() then	-- 主动点击并且为主角的幻兽
				ControlBeastsWGCtrl.Instance:OpenBeastsSkillShowTips(self.beast_id)
				ControlBeastsWGCtrl.Instance:SendCSBeastUseSkill(self.move_skill_id, self.target_obj_id, self.target_x, self.target_y)	
			end

			self:ResetFightData()
		end
	end
end

function BeastObj:GetCurFollowTargetRealPos()
	local target = self.owner_obj

	if target then
		local beast_battle_index = self.battle_index
		local real_pos = target.real_pos
	
		if (not beast_battle_index) then
			return real_pos
		end
	
		local deliverer_draw_obj = target:GetDrawObj()
		if not deliverer_draw_obj then
			return real_pos
		end
	
		local role_transform = deliverer_draw_obj:GetTransfrom()
	
		if not role_transform or IsNil(role_transform) then
			return real_pos
		end
	
		local pos = nil
		local left_back = -role_transform.forward + (-role_transform.right - role_transform.forward) * (self.radius / 2)
		local center_back = -role_transform.forward * self.radius
		local right_back = -role_transform.forward + (role_transform.right - role_transform.forward) * (self.radius / 2)
		if beast_battle_index == 1 then
			pos = role_transform.position + left_back       ---左后
		elseif beast_battle_index == 2 then
			pos = role_transform.position + center_back   ---正后
		elseif beast_battle_index == 3 then
			pos = role_transform.position + right_back   ---右后
		end
	
		if not pos then
			return real_pos
		end
	
		local beast_real_pos = u3d.vec2(0, 0)
		beast_real_pos.x = pos.x
		beast_real_pos.y = pos.z
	
		return beast_real_pos
	end
end

-- 是否不是使用技能移动中
function BeastObj:IsNotMoveUseSkill()
	return not self.is_find_use_skill_pos
end

-- 是否忽略离开战斗时间
function BeastObj:IgnoreFightContinueTime()
	return self.is_find_use_skill_pos
end