local FRIEND_LIST_POS_LIST = {[1] = Vector3(800,-21, 0), [2] = Vector3(480, -21, 0)}

HefuJulingFriendView = HefuJulingFriendView or BaseClass(SafeBaseView)

function HefuJulingFriendView:__init()
    self.view_name = "HefuJulingFriendView"
    --self:SetMaskBg(false, true)
    self.is_use_mask = false
	self:AddViewResource(0, "uis/view/merge_activity_ui/hefu_juling_ui_prefab", "juling_friend_obj")
end

function HefuJulingFriendView:__delete()

end

function HefuJulingFriendView:ReleaseCallBack()
	if self.friend_list_view then
		self.friend_list_view:DeleteMe()
		self.friend_list_view = nil
    end
    if CountDownManager.Instance:HasCountDown("juling_all_invite_delaytime") then
        CountDownManager.Instance:RemoveCountDown("juling_all_invite_delaytime")
    end 
    self.cur_select_toggle = nil
end

function HefuJulingFriendView:ShowIndexCallBack()
    self:OnClickJuLingFriendToggle(1, true)
	self:PlayMoveInTween()
end

function HefuJulingFriendView:PlayMoveInTween()
    if  self.node_list and self.node_list["friend_obj_root"] then
        local do_pos = self.node_list["friend_obj_root"].transform:DOAnchorPos(FRIEND_LIST_POS_LIST[2], 0.2)
        UITween.AlpahShowPanel(self.node_list["friend_obj_root"].gameObject , true , 0.2, nil,function ()
            self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).alpha = 1
            self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).blocksRaycasts = true
        end)
        do_pos:SetEase(DG.Tweening.Ease.OutBack)
        self.node_list.btn_shrink:SetActive(true)
    end
end

function HefuJulingFriendView:LoadCallBack()
    self.node_list["friend_obj_root"].transform.anchoredPosition = FRIEND_LIST_POS_LIST[1]
    self.node_list.all_invite_btn.button:AddClickListener(BindTool.Bind(self.OnClickAllInviteBtn, self))
	for i=1,2 do
		self.node_list["juling_toggle_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickJuLingFriendToggle, self, i))
    end
    self.cur_select_toggle = nil
    --self.node_list.juling_back_btn.button:AddClickListener(BindTool.Bind(self.OnClickJuLingBackBtn, self))
    self.node_list.btn_shrink.button:AddClickListener(BindTool.Bind(self.OnClickJuLingBackBtn, self))
    
    if not self.friend_list_view then
		self.friend_list_view = AsyncListView.New(JuLingFriendRender, self.node_list.juling_list_view)
	end
    --self.node_list.juling_back_block:SetActive(false)
    self.is_all_invited = false
    self.node_list.btn_shrink:SetActive(false)
end

function HefuJulingFriendView:OnClickJuLingBackBtn()
	local do_pos = self.node_list["friend_obj_root"].transform:DOAnchorPos(FRIEND_LIST_POS_LIST[1], 0.2)
	UITween.AlpahShowPanel(self.node_list["friend_obj_root"].gameObject , false , 0.2, nil,function ()
		self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).alpha = 0
		self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).blocksRaycasts = false
	end)
	do_pos:SetEase(DG.Tweening.Ease.OutBack)

    --self.node_list.juling_back_block:SetActive(false)
    self.node_list.btn_shrink:SetActive(false)
end

-- 右边好友协助按钮
function HefuJulingFriendView:OnClickFriendBtn()
	--帮派id不为0得时候请求一下成员列表信息
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	if 0 ~= role_vo.guild_id then
		GuildWGCtrl.Instance:SendGetGuildInfoReq(GuildDataConst.GUILD_INFO_TYPE.GUILD_MEMBER_LIST, role_vo.guild_id)
	end
	HeFuJuLingWGCtrl.Instance:SendAllFirendAndMemberLingBaoInfo()

	local do_pos = self.node_list["friend_obj_root"].transform:DOAnchorPos(FRIEND_LIST_POS_LIST[2], 0.2)
	UITween.AlpahShowPanel(self.node_list["friend_obj_root"].gameObject , true , 0.2, nil,function ()
		self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).alpha = 1
		self.node_list["friend_obj_root"].transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup)).blocksRaycasts = true
    end)
	do_pos:SetEase(DG.Tweening.Ease.OutBack)
    --self.node_list.juling_back_block:SetActive(true)
	if self.lingbao_root then
		self.lingbao_root:SetKettleActive(false)
    end
    -- if self.node_list and self.node_list.friend_obj then
    --     self.node_list.friend_obj.gameObject:GetComponent(typeof(UIOverrideOrder))
    --     if overried_order then
    --         overried_order:ResetRootCanvas()
    --     end
    -- end
end

function HefuJulingFriendView:OnClickJuLingFriendToggle(index, is_on)
	if self.cur_select_toggle ~= index and is_on then
		self.cur_select_toggle = index
		self:FlushFriendListView()
	end
end
function HefuJulingFriendView:OnFlush()
    if self.cur_select_toggle then
        self:FlushFriendListView()
    end
end

--刷新好友列表
function HefuJulingFriendView:FlushFriendListView()
    local is_be_invited_lsit = {}
	for i = 1, 2 do
		is_be_invited_lsit[i] = HeFuJuLingWGData.Instance:GetIsBeInveiteByType(i)
		self.node_list["toggle_remind_" .. i]:SetActive(is_be_invited_lsit[i] == true)
    end
    
	local juling_info = HeFuJuLingWGData.Instance:GetJuLingInfo()
    local count = 0
    if not self.is_all_invited then
        local status = juling_info.lingbao_list[1] and juling_info.lingbao_list[1].status or 0
		XUI.SetButtonEnabled(self.node_list.all_invite_btn, status == LINGBAO_STATUS.LINGBAO_STATUS_GROWING)
	end

	local data_list

	if self.cur_select_toggle == 1 then
        data_list = HeFuJuLingWGData.Instance:GetJuLingFriendList()
        self.node_list.no_friend_bg:SetActive(IsEmptyTable(data_list))
        self.node_list.juling_no_guild_bg:SetActive(false)
        self.node_list.no_friend_desc.text.text = Language.JuLingZhuZhen.JuLingDesc7[1]
	else
        data_list = HeFuJuLingWGData.Instance:GetJuLingMemberList()
        self.node_list.no_friend_bg:SetActive(false)
        self.node_list.juling_no_guild_bg:SetActive(IsEmptyTable(data_list))
        if IsEmptyTable(data_list) then
            local type_str = RoleWGData.Instance.role_vo.guild_id == 0 and 2 or 3
            self.node_list.no_guild_desc.text.text = Language.JuLingZhuZhen.JuLingDesc7[type_str]
        end
	end
	self.friend_list_view:SetDataList(data_list)
end

function HefuJulingFriendView:FlushTextInvite()
    if not IsEmptyTable(self.friend_list_view.cell_list) then
        for k, v in pairs(self.friend_list_view.cell_list) do
            v:FlushTextInvite()
        end
    end
end

function HefuJulingFriendView:OnClickAllInviteBtn()
	local data_list1 = HeFuJuLingWGData.Instance:GetJuLingFriendList()
	local data_list2 = HeFuJuLingWGData.Instance:GetJuLingMemberList()

	--好友列表和仙盟列表都为空
	if IsEmptyTable(data_list1) and IsEmptyTable(data_list2) then
		TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc1)
		return
	end
    local uid_list = {}
    local count = 0
    local other_cfg = HeFuJuLingWGData.Instance:GetOtherCfg()
	if not other_cfg then
		return
	end
    local min_cd = other_cfg.cd
	local friend_list = data_list1
	if friend_list and not IsEmptyTable(friend_list) then
        for k,v in pairs(friend_list) do
            local cur_cd = HeFuJuLingWGData.Instance:GetCacheCDByRoleid(v.uid)
            if v.uid > 0 then
                if cur_cd <= 0 then
                    count = count + 1
                    HeFuJuLingWGData.Instance:AddCacheCDList(v.uid, other_cfg.cd)
                    table.insert(uid_list, v.uid)
                else
                    min_cd = min_cd >= cur_cd and cur_cd or min_cd
                end
			end
		end
	end

	local member_list = data_list2
	if member_list and not IsEmptyTable(member_list) then
		for k,v in pairs(member_list) do
            if not IsEmptyTable(v) then
                local cur_cd = HeFuJuLingWGData.Instance:GetCacheCDByRoleid(v.uid)
                if v.uid > 0 then
                    if cur_cd <= 0 then
                        count = count + 1
                        HeFuJuLingWGData.Instance:AddCacheCDList(v.uid, other_cfg.cd)
                        table.insert(uid_list, v.uid)
                    else
                        min_cd = min_cd >= cur_cd and cur_cd or min_cd
                    end
                end
			end
		end
	end
    if count > 0 then
        HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenHelpFriendReqInfo(MERGE_JULINGZHUZHEN_QUERY_TYPE.MERGE_JULINGZHUZHEN_QUERY_TYPE_INVITE, count, uid_list)
    end

    if CountDownManager.Instance:HasCountDown("juling_all_invite_delaytime") then
        CountDownManager.Instance:RemoveCountDown("juling_all_invite_delaytime")
    end
    if min_cd > 0 then
        CountDownManager.Instance:AddCountDown("juling_all_invite_delaytime", BindTool.Bind1(self.AllInviteDelayTime, self), 
        BindTool.Bind1(self.CompleteAllInviteDelayTime, self), nil, min_cd, 1)
        self:AllInviteDelayTime(0, min_cd)
        self.is_all_invited = true
    else
        self:CompleteAllInviteDelayTime()
    end
end

function HefuJulingFriendView:AllInviteDelayTime(elapse_time, total_time)
	local time = math.ceil(total_time - elapse_time)

	if time >= 0 then
		XUI.SetButtonEnabled(self.node_list.all_invite_btn, false)
		self.node_list.all_invite_btn_text.text.text = Language.JuLingZhuZhen.JuLingDesc4 .. string.format("(%s)", time)
	end
end

function HefuJulingFriendView:CompleteAllInviteDelayTime()
	XUI.SetButtonEnabled(self.node_list.all_invite_btn, true)
	self.node_list.all_invite_btn_text.text.text = Language.JuLingZhuZhen.JuLingDesc4
	self.is_all_invited = false
end


function HefuJulingFriendView:OnClinkCloseHandler()
	self:Close()
end

--===========================================================JuLingFriendRender(好友列表)=============================================================--
JuLingFriendRender = JuLingFriendRender or BaseClass(BaseRender)

function JuLingFriendRender:LoadCallBack()
	--self.head_icon = BaseHeadCell.New(self.node_list["head_icon"])
	self.node_list["help_btn"].button:AddClickListener(BindTool.Bind(self.OnClickHelpBtn, self))
	self.node_list["invite_btn"].button:AddClickListener(BindTool.Bind(self.OnClickInviteBtn, self))
end

function JuLingFriendRender:__delete()
	-- if self.head_icon then
	-- 	self.head_icon:DeleteMe()
	-- 	self.head_icon = nil
	-- end

	if self.invite_delaytime then
		GlobalTimerQuest:CancelQuest(self.invite_delaytime)
		self.invite_delaytime = nil
	end

	if self.node_list.invite_btn then
		XUI.SetButtonEnabled(self.node_list.invite_btn, true)
	end
end

function JuLingFriendRender:FlushTextInvite()
    local role_id = self.data.uid
    if HeFuJuLingWGData.Instance:GetCacheCDByRoleid(role_id) > 0 then
        XUI.SetButtonEnabled(self.node_list["invite_btn"], false)
        return
    end
    XUI.SetButtonEnabled(self.node_list["invite_btn"], true)
end

function JuLingFriendRender:OnFlush()
	if not self.data then
		return
	end

	--self.head_icon:SetData({role_id = self.data.uid, prof = self.data.prof, fashion_photoframe = 0, sex = self.data.sex})
	--self.node_list.bg:SetActive(self.data.is_lover == 1)

	local is_can_zhuling, type = HeFuJuLingWGData.Instance:GetIsCanHelpZhuLing(self.data)
	XUI.SetGraphicGrey(self.node_list.help_btn, not is_can_zhuling)

	self.node_list.help_flag:SetActive(self.data.is_be_invited == 1 and is_can_zhuling)

	local status = ""
	--3是跨服在线的状态
	if 1 == self.data.is_online or 3 == self.data.is_online then
		status = Language.Common.OnLine
	else
		local time_format = TimeWGCtrl.Instance:GetServerTime() - self.data.offline_time
		status = GuildWGData.FormatTime(time_format)
	end

	local color1 = (1 == self.data.is_online or 3 == self.data.is_online) and COLOR3B.L_ORANGE or COLOR3B.GRAY
	local color2 = (1 == self.data.is_online or 3 == self.data.is_online) and COLOR3B.GREEN or COLOR3B.GRAY

	self.node_list.role_level.text.text = ToColorStr(status, color2)
	self.node_list.role_name.text.text = self.data.name
    self:FlushTextInvite()
end

--前往花园浇水按钮
function JuLingFriendRender:OnClickHelpBtn()
	if not self.data then
		return
	end
	local is_can_zhuling, log_type = HeFuJuLingWGData.Instance:GetIsCanHelpZhuLing(self.data)
	if not is_can_zhuling then
		if log_type == 1 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc14)
		elseif log_type == 2 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc6)
		elseif log_type == 3 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc11)
		end
		return
	end

	HeFuJuLingWGData.Instance:SetGardenFlag(1)
	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_CONFIRM_INVITE, self.data.uid)
	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_OTER_INFO_REQ, self.data.uid)
	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_ZHULING_LOG_REQ, self.data.uid)

end

--邀请按钮
function JuLingFriendRender:OnClickInviteBtn()
    local is_can_invite, log_type = HeFuJuLingWGData.Instance:GetIsCanInvite(self.data)
    if not is_can_invite then
        if log_type == 1 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc16)
		elseif log_type == 2 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc18)
		elseif log_type == 3 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc17)
		end
		return
    end
    
	local other_cfg = HeFuJuLingWGData.Instance:GetOtherCfg()
	if not other_cfg then
		return
	end
    HeFuJuLingWGData.Instance:AddCacheCDList(self.data.uid, other_cfg.cd)
	HeFuJuLingWGCtrl.Instance:CSCSAJuLingZhuZhenInfo(MERGE_JULINGZHUZHEN_OPERA_TYPE.MERGE_JULINGZHUZHEN_OPERA_TYPE_INVITE_HELP_ME, self.data.uid, MERGE_JULINGZHUZHEN_OPERA_INVITE_TYPE.FRIEND)
	TipWGCtrl.Instance:ShowSystemMsg(Language.JuLingZhuZhen.TipsDesc12)
end
