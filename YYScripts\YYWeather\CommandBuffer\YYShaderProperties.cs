﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class YYShaderProperties
{
    public static readonly YYShaderProperties Global = new YYShaderProperties();

    private System.Action<int, Vector4> setVector;
    private readonly System.Action<int, Vector4> setVectorGlobal;

    private System.Action<int, Vector4[]> setVectorArray;
    private readonly System.Action<int, Vector4[]> setVectorArrayGlobal;

    public void SetVector(int name, Vector4 value)
    {
        setVector(name, value);
    }

   

    public YYShaderProperties(object material = null)
    {
        setVectorGlobal = SetVectorGlobal;
        setVectorArrayGlobal = SetVectorArrayGlobal;
        Update(material);
    }

    public void SetVectorArray(int name, Vector4[] value)
    {
        if (value != null)
        {
            setVectorArray(name, value);
        }
    }

    public void Update(object material)
    {
        //setInt = setIntGlobal;
        //setFloat = setFloatGlobal;
        //setFloatArray = setFloatArrayGlobal;
        setVector = setVectorGlobal;
        setVectorArray = setVectorArrayGlobal;
        //setColor = setColorGlobal;
        //setColorArray = setColorArrayGlobal;
        //setTexture = setTextureGlobal;
    }


    private void SetVectorGlobal(int id, Vector4 value)
    {
        Shader.SetGlobalVector(id, value);
    }

    private void SetVectorArrayGlobal(int id, Vector4[] value)
    {
        Shader.SetGlobalVectorArray(id, value);
    }


}
