ShiTianSuitStrengthenWGData = ShiTianSuitStrengthenWGData or BaseClass()

STSUIT_MAX_PART_COUNT = 12
SHOW_TYPE = {
    Strengthen = TabIndex.shitian_suit_strengthen,
    Gemstone = TabIndex.shitian_suit_gemstone,
    InfuseSoul = TabIndex.shitian_suit_infuse_soul,
}

function ShiTianSuitStrengthenWGData:__init()
    if ShiTianSuitStrengthenWGData.Instance then
		error("[ShiTianSuitStrengthenWGData] Attempt to create singleton twice!")
		return
	end

    ShiTianSuitStrengthenWGData.Instance = self

    self:InitParam()
    self:InitConfig()

    RemindManager.Instance:Register(RemindName.ShiTianSuitAllStrengthen, BindTool.Bind(self.GetRemind, self))
end

function ShiTianSuitStrengthenWGData:__delete()
    self.cur_up_is_level = nil
    self.infuse_soul_is_success = nil
    self.all_suit_info = nil
    self.strengthen_total_level_list = nil
    self.gemstone_total_level_list = nil
    self.infusesoul_total_level_list = nil
    self.is_compose = nil

    RemindManager.Instance:UnRegister(RemindName.ShiTianSuitAllStrengthen)
    ShiTianSuitStrengthenWGData.Instance = nil
end

function ShiTianSuitStrengthenWGData:InitParam()
    self.is_compose = 0
    self.cur_up_is_level = false
    self.infuse_soul_is_success = false
    self.all_suit_info = {}
    self.strengthen_total_level_list = {}
    self.gemstone_total_level_list = {}
    self.infusesoul_total_level_list = {}
end

function ShiTianSuitStrengthenWGData:InitConfig()
    local cfg = ConfigManager.Instance:GetAutoConfig("shitianadvanceconfig_auto")
    self.equip_gem_type_cfg = cfg.equip_part
    self.stone_cfg = ListToMap(cfg.shitianstone, "item_id")
    self.stone_level_cfg = ListToMap(cfg.shitianstone, "shitianstone_type", "level")
    self.stone_compose_cfg = ListToMap(cfg.shitianstone_level_up, "old_shitianstone_item_id")
    self.stone_newstone_cfg = ListToMap(cfg.shitianstone_level_up, "new_shitianstone_item_id")
    self.stone_resonance_cfg = ListToMapList(cfg.total_shitianstone, "suit_seq")
    self.strength_level_cfg = ListToMap(cfg.part_strength_level_up, "suit_seq", "part", "level")
    self.total_strength_cfg = ListToMapList(cfg.total_strength, "suit_seq")
    self.infusesoul_level_cfg = ListToMap(cfg.part_fumo_level_up, "suit_seq", "part", "quality", "level")
    self.total_infusesoul_level_cfg = ListToMap(cfg.total_fumo, "suit_seq", "total_quality")
    self.strength_stuff_cfg = ListToMap(cfg.part_strength_level_up, "cost_item_id")
    self.fumo_stuff_cfg = cfg.fumo_stuff
    self.default_stone_cfg = ListToMap(cfg.first_stone, "suit_seq", "stone_type")

    self.first_lev_stone_price = cfg.shitianstone_level_up[1].price
    self:CtreBaoShiConversionTable()
end

------------ 红点相关start ------------
function ShiTianSuitStrengthenWGData:GetRemind()
    local all_suit_cfg = ShiTianSuitWGData.Instance:GetSuitTypeCfg()
    for suit_seq = 0, #all_suit_cfg do
        if self:GetShiTianSuitRemind(suit_seq) then
                return 1
        end
    end

    return 0
end

function ShiTianSuitStrengthenWGData:GetShiTianSuitRemind(suit_seq)
    if ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpRemind(suit_seq, SHOW_TYPE.Strengthen) or
        ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpRemind(suit_seq, SHOW_TYPE.Gemstone) or
        ShiTianSuitStrengthenWGData.Instance:GetShiTianSuitEpRemind(suit_seq, SHOW_TYPE.InfuseSoul) then
            return true
        end

    return false
end

function ShiTianSuitStrengthenWGData:GetShiTianSuitEpRemind(suit_seq, show_type)
    if self.all_suit_info[suit_seq] then
        if self:GetCurTotalCanActiveOrIsMax(suit_seq, show_type) then
            return true
        end

        local func_key
        if show_type == SHOW_TYPE.Strengthen then
            func_key = "GetShiTianSuitEpCanStrengthen"
        elseif show_type == SHOW_TYPE.Gemstone then
            func_key = "GetShiTianSuitEpStoneRemind"
        elseif show_type == SHOW_TYPE.InfuseSoul then
            func_key = "GetShiTianSuitEpCanInfuseSoul"
        end

        for part = 0, STSUIT_MAX_PART_COUNT - 1 do
            if self[func_key](self, suit_seq, part) then
                return true
            end
        end
    end

    return false
end
------------ 红点相关end ------------


------------ 协议相关start ------------
function ShiTianSuitStrengthenWGData:SetAllInfo(protocol)
    self.all_suit_info = protocol.all_list
end

function ShiTianSuitStrengthenWGData:SetStengthenInfo(protocol)
    local suit_data = self.all_suit_info[protocol.suit_seq]
    suit_data.shitian_strength_total_level = protocol.shitian_strength_total_level
    suit_data.shitian_equip_part_list[protocol.part].strengthen_level = protocol.strengthen_level

    self:UpdateStrengthenTotalLevel(protocol.suit_seq)
end

function ShiTianSuitStrengthenWGData:SetGemStoneInfo(protocol)
    local suit_data = self.all_suit_info[protocol.suit_seq]
    suit_data.shitian_stone_total_level = protocol.shitian_stone_total_level
    suit_data.shitian_equip_part_list[protocol.part].stone_data = protocol.stone_data
    self.is_compose = protocol.is_compose
    self:UpdateStonethenTotalLevel(protocol.suit_seq)
end

function ShiTianSuitStrengthenWGData:SetInfuseSoulInfo(protocol)
    local suit_data = self.all_suit_info[protocol.suit_seq]
    suit_data.shitian_fumo_total_quality = protocol.shitian_fumo_total_quality

    local part_data = suit_data.shitian_equip_part_list[protocol.part]
    local update_data = {
        suit_seq = protocol.suit_seq,
        part = protocol.part,
        old_level = part_data.fumo_level,
        new_level = protocol.fumo_level,
        old_quality = part_data.fumo_quality,
        new_quality = protocol.fumo_quality,
    }
    self:GetInfuseSoulIsLevelUp(update_data)

    part_data.fumo_quality = protocol.fumo_quality
    part_data.fumo_level = protocol.fumo_level
    self:UpdateInfuseSoulTotalLevel(protocol.suit_seq)
end



function ShiTianSuitStrengthenWGData:SetTotalLevelInfo(protocol)
    local suit_data = self.all_suit_info[protocol.suit_seq]
    suit_data.shitian_strength_total_level = protocol.shitian_strength_total_level
    suit_data.shitian_stone_total_level = protocol.shitian_stone_total_level
    suit_data.shitian_fumo_total_quality = protocol.shitian_fumo_total_quality

    self:UpdateStrengthenTotalLevel(protocol.suit_seq)
    self:UpdateStonethenTotalLevel(protocol.suit_seq)
    self:UpdateInfuseSoulTotalLevel(protocol.suit_seq)
end
------------ 协议相关end ------------


------------ 宝石相关strat ------------

-- 获得部位能镶嵌的宝石类型
function ShiTianSuitStrengthenWGData:GetEquipGemTypeBySlot(part)
    return self.equip_gem_type_cfg[part] or {}
end

-- 宝石id获得宝石配置
function ShiTianSuitStrengthenWGData:GetStoneCfg(stone_id)
    return self.stone_cfg[stone_id]
end

-- 是否是宝石
function ShiTianSuitStrengthenWGData:IsShiTianStone(item_id)
    return self.stone_cfg[item_id] ~= nil
end

-- 各种类型的一级宝石
function ShiTianSuitStrengthenWGData:GetDefaultStone(suit_seq, stone_type)
    return (self.default_stone_cfg[suit_seq] or {})[stone_type] or {}
end

-- 获取宝石总等级
function ShiTianSuitStrengthenWGData:GetStoneTotalLevelBySeq(suit_seq)
    if self.gemstone_total_level_list[suit_seq] then
        return self.gemstone_total_level_list[suit_seq]
    end

    self:UpdateStonethenTotalLevel(suit_seq)
    return self.gemstone_total_level_list[suit_seq] or 0
end

-- 更新宝石总等级
function ShiTianSuitStrengthenWGData:UpdateStonethenTotalLevel(suit_seq)
    local suit_data = self.all_suit_info[suit_seq]
    local total_level = 0
    if suit_data then
        for _, part_data in pairs(suit_data.shitian_equip_part_list) do
            for part, stone_id in pairs(part_data.stone_data) do
                local level = self.stone_cfg[stone_id] and self.stone_cfg[stone_id].level or 0
                total_level = total_level + level
            end
        end
    end

    self.gemstone_total_level_list[suit_seq] = total_level
end

-- 获得部位的所有宝石
function ShiTianSuitStrengthenWGData:GetStoneDataList(suit_seq, part)
    local suit_data = self.all_suit_info[suit_seq]
    if suit_data and suit_data.shitian_equip_part_list[part] then
        return suit_data.shitian_equip_part_list[part].stone_data
    end

    return {}
end

-- 宝石是否满级
function ShiTianSuitStrengthenWGData:GetStoneIsMax(stone_type, cur_stone_id)
    local stone_level_cfg = self.stone_level_cfg[stone_type]
    local cur_stone_cfg = self:GetStoneCfg(cur_stone_id)
    if stone_level_cfg and cur_stone_cfg then
        return cur_stone_cfg.level == stone_level_cfg[#stone_level_cfg].level
    end

    return false
end

-- 是否有更好的宝石
function ShiTianSuitStrengthenWGData:GetCurHasBetterStone(suit_seq, stone_type, cur_stone_id)
    local stone_list = ItemWGData.Instance:GetShiTianStoneItemList(suit_seq, stone_type)
    if not IsEmptyTable(stone_list) then
        local cur_stone_cfg = self:GetStoneCfg(cur_stone_id)
        if not cur_stone_cfg then
            return true
        end

        for k, v in pairs(stone_list) do
            local stone_cfg = self:GetStoneCfg(v.item_id)
            if stone_cfg and stone_cfg.level > cur_stone_cfg.level then
                return true
            end
        end
    end

    return false
end

-- 获得宝石单条描述
function ShiTianSuitStrengthenWGData.GetStoneDescForCfg(attr_id, attr_value)
    local str = ""
	if (not attr_id) or (not attr_value) then
		return str
	end

	if attr_id == 0 or attr_value == 0 then
		return str
	end

	local is_per = EquipmentWGData.Instance:GetAttrIsPer(attr_id)
	local per_desc = is_per and "%" or ""
	local value_str = is_per and attr_value / 100 or attr_value
	local attr_name = EquipmentWGData.Instance:GetAttrName(attr_id, false, false)
	str = string.format(Language.ShiTianSuit.StoneAttr, attr_name, value_str, per_desc)

	return str
end

-- 创建宝石转化价值列表
function ShiTianSuitStrengthenWGData:CtreBaoShiConversionTable()
	self.baoshi_table = {}
	for i,v in pairs(self.stone_cfg) do
		local need_num = 0
		local item_id = v.item_id
		self.baoshi_table[item_id] = {}
		local baoshi_level_cfg = self:GetNewStoneCfg(item_id)
        local price = self.first_lev_stone_price

		if baoshi_level_cfg then
			if v.level <= 1 then
				price = need_num * self.first_lev_stone_price
			else
				need_num = self:BaoShiConversion(item_id)
				price = need_num * self.first_lev_stone_price
			end

			self.baoshi_table[item_id].num = need_num
			self.baoshi_table[item_id].price = price
		else
			if v.level <= 1 then
				self.baoshi_table[item_id].num = 0
				self.baoshi_table[item_id].price = price
			end
		end
	end
end

--宝石等级，每级需要多少个
--根据等级换算成1级宝石
function ShiTianSuitStrengthenWGData:BaoShiConversion(item_id)
    local need_num = 1
    local function calc_num(stone_id)
        local new_cfg = self:GetNewStoneCfg(stone_id)
        if new_cfg then
            need_num = need_num * new_cfg.need_num
            calc_num(new_cfg.old_shitianstone_item_id)
        end
    end

    calc_num(item_id)
    return need_num > 1 and need_num or 0
end

-- 获得合成前宝石配置
function ShiTianSuitStrengthenWGData:GetStoneComposeCfg(stone_id)
    return self.stone_compose_cfg[stone_id]
end

-- 获得合成后宝石配置
function ShiTianSuitStrengthenWGData:GetNewStoneCfg(stone_id)
    return self.stone_newstone_cfg[stone_id]
end

-- 部位是否有可装或更强
function ShiTianSuitStrengthenWGData:GetShiTianSuitEpStoneRemind(suit_seq, part)
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
    local stone_list = self:GetStoneDataList(suit_seq, part)
    local stone_type = self:GetEquipGemTypeBySlot(part).shitianstone_type
    if is_act and not IsEmptyTable(stone_list) then
        for k, v in pairs(stone_list) do
            if self:GetCurHasBetterStone(suit_seq, stone_type, v) then
                return true
            end

            local can_compose = self:ComputBaoShiUpgradePrice(v) <= 0
            if can_compose then
                return true
            end
        end
    end

    return false
end

-- 获得对应宝石价值信息
function ShiTianSuitStrengthenWGData:GetBaoShiConversionTableByid(item_id)
    return self.baoshi_table[item_id] or {}
end

--计算升级宝石花费的价格
function ShiTianSuitStrengthenWGData:ComputBaoShiUpgradePrice(old_stone_item_id)
	if not old_stone_item_id or old_stone_item_id <= 0 then
		return 999999999
	end

	local cfg = self:GetStoneComposeCfg(old_stone_item_id)
	--已经满级
	if cfg == nil then
		return 999999999
	end

	--目标需要的换算数量
	local new_stone_item_id = cfg.new_shitianstone_item_id 			          --新宝石id
	local new_conversion_list = self:GetBaoShiConversionTableByid(new_stone_item_id)
	if IsEmptyTable(new_conversion_list) then
		return 999999999
	end

	--当前宝石孔的换算数量
	local cur_conversion_list = self:GetBaoShiConversionTableByid(old_stone_item_id)
	local cur_stone_cfg = self:GetStoneCfg(old_stone_item_id)
	local cur_num, cur_price = 0, 0

	cur_num = cur_conversion_list.num
	cur_price = cur_conversion_list.price

	--背包所有宝石的换算数量
	local bag_conversion_list = ItemWGData.Instance:GetShiTianStoneNumAndValueByType(cur_stone_cfg.stone_blong_suit, cur_stone_cfg.shitianstone_type)
    local bag_price = bag_conversion_list.price or 0
	local new_stone_item_price = 0
	local have_price = cur_price + bag_price

	if new_conversion_list.price > have_price then
		new_stone_item_price = new_conversion_list.price - have_price
	end

	return new_stone_item_price
end

-- 获取升级宝石需要低级宝石描述
function ShiTianSuitStrengthenWGData:CalcUpgradeNeedStoneStr(old_stone_item_id)
    if not old_stone_item_id then
		return nil
	end

	local cfg = self:GetStoneComposeCfg(old_stone_item_id)
    if IsEmptyTable(cfg) then
        return nil
    end

    local new_stone_item_price = (cfg.price * cfg.need_num) - cfg.price		--新宝石价格
    local expend_tab = {}
    while cfg ~= nil do
        local have_count = ItemWGData.Instance:GetItemNumInBagById(old_stone_item_id)

        if have_count > 0 then
            local need_count = new_stone_item_price / cfg.price
            local offest_count = have_count - need_count
            local add_count = offest_count > 0 and need_count or have_count
            if add_count > 0 then
                table.insert(expend_tab, {item_id = old_stone_item_id, count = add_count})
            end

            if offest_count > 0 then
                break
            end
            new_stone_item_price = new_stone_item_price - cfg.price * have_count
        end

        local temp_cfg = self:GetNewStoneCfg(old_stone_item_id)
        if temp_cfg == nil then
            break
        end

        old_stone_item_id = temp_cfg.old_shitianstone_item_id
        cfg = self:GetStoneComposeCfg(old_stone_item_id)
    end

    if IsEmptyTable(expend_tab) then
        return nil
    else
        local str = ""
        for i, v in ipairs(expend_tab) do
            local name = ItemWGData.Instance:GetItemName(v.item_id, nil, true)
            if i < #expend_tab then
                str = str .. name .. "*" .. v.count .. "，"
            else
                str = str .. name .. "*" .. v.count
            end
        end

        return str
    end
end

-- 在背包内的宝石转换成一级的总数 和 总价值
function ShiTianSuitStrengthenWGData:GetBaoShiConversionTableInBag(suit_seq, stone_type)
	local conversion_list = {}
	local need_num = 0
	local price = self.first_lev_stone_price

	local bag_list = ItemWGData.Instance:GetShiTianStoneItemList(suit_seq, stone_type)
	for k,v in pairs(bag_list) do
		local baoshi_cfg = self:GetStoneCfg(v.item_id)
        local cfg = self:GetBaoShiConversionTableByid(v.item_id)
        need_num = need_num + (cfg.num * v.num)
        if baoshi_cfg.level <= 1 then
            need_num = (need_num + 1 * v.num)
        end
	end

	conversion_list.num = need_num
	conversion_list.price = price * need_num
	return conversion_list
end

-- 一键镶嵌
function ShiTianSuitStrengthenWGData:GetBaoShiOneKeyInlay(suit_seq, part)
    local one_key_list = {}
    local stone_type = self:GetEquipGemTypeBySlot(part).shitianstone_type
    local bag_stone_list = ItemWGData.Instance:GetShiTianStoneItemList(suit_seq, stone_type)
    local stone_data = self:GetStoneDataList(suit_seq, part)
    if IsEmptyTable(bag_stone_list) or IsEmptyTable(stone_data) then
        return one_key_list
    end

    local had_inlay_itemid_list = {}
    local temp_inlay_list = {}
    local temp_item_data = {item_id = 0, index = -1}
    local temp_used_num = 0
    for slot_index = 0, GameEnum.MAX_STONE_COUNT - 1 do
        local stone_id = stone_data[slot_index]
        if stone_id > 0 then
            table.insert(had_inlay_itemid_list, {slot_index = slot_index, item_id = stone_id})
        else
            temp_item_data = {item_id = 0, index = -1}
            for k,v in pairs(bag_stone_list) do
                temp_used_num = temp_inlay_list[v.index] or 0
                if v.num > temp_used_num and v.item_id > temp_item_data.item_id then
                    temp_item_data = {item_id = v.item_id, index = v.index}
                end
            end

            if temp_item_data.item_id > 0 then
                local index = temp_item_data.index
                temp_used_num = temp_inlay_list[index] or 0
                temp_inlay_list[index] = temp_used_num + 1
                table.insert(one_key_list, {slot_index = slot_index, bag_index = index})
            end
        end
    end

    -- 排序
    SortTools.SortAsc(had_inlay_itemid_list, "item_id", "slot_index")

    local item_id = 0
    -- 替换上更高级
    for k,v in ipairs(had_inlay_itemid_list) do
        item_id = v.item_id
        temp_item_data = {item_id = 0, index = -1}
        for item_k, item_data in pairs(bag_stone_list) do
            temp_used_num = temp_inlay_list[item_data.index] or 0
            if item_data.num > temp_used_num and item_data.item_id > item_id and item_data.item_id > temp_item_data.item_id then
                temp_item_data = {item_id = item_data.item_id, index = item_data.index}
            end
        end

        if temp_item_data.item_id > 0 then
            local index = temp_item_data.index
            temp_used_num = temp_inlay_list[index] or 0
            temp_inlay_list[index] = temp_used_num + 1
            table.insert(one_key_list, {slot_index = v.slot_index, bag_index = index})
        end
    end

    return one_key_list
end

-- 能否合成
function ShiTianSuitStrengthenWGData:GetSlotStoneCanUpGrade(suit_seq, part, stone_id)
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
    if not is_act then
        return false
    end

    if stone_id == 0 then
        return false
    end

    local need_price = self:ComputBaoShiUpgradePrice(stone_id)

    return need_price <= 0
end

-- 最新下发的宝石数据是镶嵌或是合成
function ShiTianSuitStrengthenWGData:GetIsCompose()
    return self.is_compose or 0
end
------------ 宝石相关end ------------

------------ 强化相关strat ------------

-- 获得强化总等级
function ShiTianSuitStrengthenWGData:GetStrengthenTotalLevelBySeq(suit_seq)
    if self.strengthen_total_level_list[suit_seq] then
        return self.strengthen_total_level_list[suit_seq]
    end

    self:UpdateStrengthenTotalLevel(suit_seq)
    return self.strengthen_total_level_list[suit_seq] or 0
end

-- 更新强化总等级
function ShiTianSuitStrengthenWGData:UpdateStrengthenTotalLevel(suit_seq)
    local suit_data = self.all_suit_info[suit_seq]
    local total_level = 0
    if suit_data then
        for k, v in pairs(suit_data.shitian_equip_part_list) do
            total_level = total_level + v.strengthen_level
        end
    end

    self.strengthen_total_level_list[suit_seq] = total_level
end

-- 获得强化等级配置，传level获特定等级配置
function ShiTianSuitStrengthenWGData:GetStrengthenLevelCfg(suit_seq, part, level)
    if self.strength_level_cfg[suit_seq] and self.strength_level_cfg[suit_seq][part] then
        local ep_level = level and level or self:GetEpStrengthenCurLevel(suit_seq, part)
        return self.strength_level_cfg[suit_seq][part][ep_level] or {}
    end

    return {}
end

-- 获得部位强化等级
function ShiTianSuitStrengthenWGData:GetEpStrengthenCurLevel(suit_seq, part)
    local suit_data = self.all_suit_info[suit_seq]
    if suit_data and suit_data.shitian_equip_part_list[part] then
        return suit_data.shitian_equip_part_list[part].strengthen_level
    end

    return 0
end

-- 当前等级是否满级
function ShiTianSuitStrengthenWGData:GetEpStrengthenCurLevelIsMax(suit_seq, part)
    local cur_level = self:GetEpStrengthenCurLevel(suit_seq, part)
    local strengthen_cfg = self.strength_level_cfg[suit_seq][part]
    if strengthen_cfg then
        return cur_level == strengthen_cfg[#strengthen_cfg].level
    end

    return false
end

-- 是否是强化材料
function ShiTianSuitStrengthenWGData:IsShiTianStrengthenItem(item_id)
    return self.strength_stuff_cfg[item_id] ~= nil
end

-- 部位能否强化
function ShiTianSuitStrengthenWGData:GetShiTianSuitEpCanStrengthen(suit_seq, part)
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
    local cur_level = self:GetEpStrengthenCurLevel(suit_seq, part)
    local suit_cfg = self:GetStrengthenLevelCfg(suit_seq, part, cur_level + 1)
    if is_act and not IsEmptyTable(suit_cfg) then
        local coin = RoleWGData.Instance.role_info.coin or 0
        local coin_is_enough = coin >= suit_cfg.need_num_tongqian
        local stuff_num = ItemWGData.Instance:GetItemNumInBagById(suit_cfg.cost_item_id)
        local stuff_is_enough = stuff_num >= suit_cfg.cost_item_num
        return coin_is_enough and stuff_is_enough
    end

    return false
end
------------ 强化相关end ------------

------------ 入灵相关strat ------------

-- 获得入灵总等级
function ShiTianSuitStrengthenWGData:GetInfuseSoulTotalLevelBySeq(suit_seq)
    if self.infusesoul_total_level_list[suit_seq] then
        return self.infusesoul_total_level_list[suit_seq]
    end

    self:UpdateInfuseSoulTotalLevel(suit_seq)
    return self.infusesoul_total_level_list[suit_seq] or 0
end

-- 获得入灵共鸣激活阶段
function ShiTianSuitStrengthenWGData:GetInfuseSoulTotalQuality(suit_seq)
    local suit_data = self.all_suit_info[suit_seq]
    if suit_data then
        return suit_data.shitian_fumo_total_quality
    end

    return 0
end

-- 获得入灵等级配置，传入level quality获得部位的特定配置
function ShiTianSuitStrengthenWGData:GetInfuseSoulLevelCfg(suit_seq, part, level, quality)
    if self.infusesoul_level_cfg[suit_seq] and self.infusesoul_level_cfg[suit_seq][part] then
        local ep_level, ep_quality = self:GetEpInfuseSoulCurLevelAndQuality(suit_seq, part)
        ep_level = level and level or ep_level
        ep_quality = quality and quality or ep_quality

        return (self.infusesoul_level_cfg[suit_seq][part][ep_quality] or {})[ep_level] or {}
    end

    return {}
end

-- 获得部位的入灵等级和品阶
function ShiTianSuitStrengthenWGData:GetEpInfuseSoulCurLevelAndQuality(suit_seq, part)
    local suit_data = self.all_suit_info[suit_seq]
    if suit_data and suit_data.shitian_equip_part_list[part] then
        local infuse_soul_data = suit_data.shitian_equip_part_list[part]
        return infuse_soul_data.fumo_level or 0, infuse_soul_data.fumo_quality or 0
    end

    return 0, 0
end

-- 更新入灵总等级
function ShiTianSuitStrengthenWGData:UpdateInfuseSoulTotalLevel(suit_seq)
    local suit_data = self.all_suit_info[suit_seq]
    local total_level = 0
    if suit_data then
        local total_cfg, next_cfg = self:GetCurAndNextTotalLevelCfg(suit_seq, SHOW_TYPE.InfuseSoul)
        if not IsEmptyTable(total_cfg) or not IsEmptyTable(next_cfg) then
            local target_quality = next_cfg and next_cfg.total_quality or total_cfg.total_quality or 0
            for k, v in pairs(suit_data.shitian_equip_part_list) do
                if v.fumo_quality >= target_quality then
                    total_level = total_level + 1
                end
            end
        end
    end

    self.infusesoul_total_level_list[suit_seq] = total_level
end

-- 部位的入灵等级和品阶是否满级
function ShiTianSuitStrengthenWGData:GetInfuseSoulCurLevelAndQualityIsMax(suit_seq, part)
    local cur_level, cur_quality = self:GetEpInfuseSoulCurLevelAndQuality(suit_seq, part)
    local ep_fumo_cfg = self.infusesoul_level_cfg[suit_seq]
    if ep_fumo_cfg and ep_fumo_cfg[part] and ep_fumo_cfg[part][cur_quality] then
        return not ep_fumo_cfg[part][cur_quality][cur_level + 1], not ep_fumo_cfg[part][cur_quality + 1]
    end

    return false, false
end

-- 是否是入灵材料
function ShiTianSuitStrengthenWGData:IsShiTianInfuseSoulItem(item_id)
    return self.fumo_stuff_cfg[item_id] ~= nil
end

-- 部位能否入灵
function ShiTianSuitStrengthenWGData:GetShiTianSuitEpCanInfuseSoul(suit_seq, part)
    local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
    local suit_cfg = self:GetInfuseSoulLevelCfg(suit_seq, part)
    local level_max, quality_max = self:GetInfuseSoulCurLevelAndQualityIsMax(suit_seq, part)
    if is_act and not IsEmptyTable(suit_cfg) and (not quality_max or not level_max) then
        local stuff_num = ItemWGData.Instance:GetItemNumInBagById(suit_cfg.cost_fumo_id)
        local stuff_is_enough = stuff_num >= suit_cfg.cost_fumo_num

        return stuff_is_enough
    end

    return false
end

-- infuse_soul_is_success结果是否成功, cur_up_is_level当前提升是等级还是品阶
function ShiTianSuitStrengthenWGData:GetInfuseSoulIsLevelUp(update_data)
    if update_data then
        self.cur_up_is_level = not self:GetInfuseSoulCurLevelAndQualityIsMax(update_data.suit_seq, update_data.part)
        self.infuse_soul_is_success = update_data.new_level > update_data.old_level or update_data.new_quality > update_data.old_quality
    end

    return self.infuse_soul_is_success, self.cur_up_is_level
end
------------ 入灵相关end ------------

-- suit_seq套装索引，show_type显示的功能页签，获取当前和下级共鸣配置
function ShiTianSuitStrengthenWGData:GetCurAndNextTotalLevelCfg(suit_seq, show_type)
    local suit_data = self.all_suit_info[suit_seq]
    local total_cfg = self:GetTotalLevelCfg(suit_seq, show_type)
    local total_key

    if not total_cfg or not suit_data then
        return {}, {}
    end

    if show_type == SHOW_TYPE.Strengthen then
        total_key = "shitian_strength_total_level"
    elseif show_type == SHOW_TYPE.Gemstone then
        total_key = "shitian_stone_total_level"
    elseif show_type == SHOW_TYPE.InfuseSoul then
        total_key = "shitian_fumo_total_quality"
    end

    for i = 0, #total_cfg do
        if suit_data[total_key] == i then
            return total_cfg[i] or {}, total_cfg[i + 1] or {}
        end
    end

    return {}, {}
end

-- need_is_max=true则判断是否满级，获取当前某功能共鸣是否能激活或是满级
function ShiTianSuitStrengthenWGData:GetCurTotalCanActiveOrIsMax(suit_seq, show_type, need_is_max)
    local suit_data = self.all_suit_info[suit_seq]
    local total_cfg = self:GetTotalLevelCfg(suit_seq, show_type)
    local total_level_key
    local total_level
    local func_key

    if not suit_data or not total_cfg then
        return false
    end

    if show_type == SHOW_TYPE.Strengthen then
        total_level = suit_data.shitian_strength_total_level
        func_key = "GetStrengthenTotalLevelBySeq"
        total_level_key = "total_level"
    elseif show_type == SHOW_TYPE.Gemstone then
        total_level = suit_data.shitian_stone_total_level
        func_key = "GetStoneTotalLevelBySeq"
        total_level_key = "total_shitianstone"
    elseif show_type == SHOW_TYPE.InfuseSoul then
        total_level = suit_data.shitian_fumo_total_quality
        func_key = "GetInfuseSoulTotalLevelBySeq"
        total_level_key = "total_quality"
    end

    if need_is_max then
        if not total_cfg[total_level + 1] then
            return true
        end
    else
        if total_cfg[total_level + 1] then
            local target_level = show_type == SHOW_TYPE.InfuseSoul and STSUIT_MAX_PART_COUNT or total_cfg[total_level + 1][total_level_key] or 0
            return self[func_key](self, suit_seq) >= target_level
        end
    end

    return false
end

-- 获取共鸣配置
function ShiTianSuitStrengthenWGData:GetTotalLevelCfg(suit_seq, show_type)
    local total_cfg
    if show_type == SHOW_TYPE.Strengthen then
        total_cfg = self.total_strength_cfg[suit_seq]
    elseif show_type == SHOW_TYPE.Gemstone then
        total_cfg = self.stone_resonance_cfg[suit_seq]
    elseif show_type == SHOW_TYPE.InfuseSoul then
        total_cfg = self.total_infusesoul_level_cfg[suit_seq]
    end

    return total_cfg
end

-- 获取tips内容
function ShiTianSuitStrengthenWGData:GetTipsInfo(suit_seq, part)
    local tips_info = {}

    tips_info.stone_attr_list = {}
    local stone_data = self:GetStoneDataList(suit_seq, part)
    for k, v in pairs(stone_data) do
        local stone_cfg = self:GetStoneCfg(v)
        if stone_cfg then
            local attr_list = EquipWGData.GetSortAttrListByTypeCfg(stone_cfg, "attr_id", "attr_val", 1, 2)
            tips_info.stone_attr_list[k] = {
                [1] = ShiTianSuitStrengthenWGData.GetStoneDescForCfg(attr_list[1].attr_str, attr_list[1].attr_value),
                [2] = ShiTianSuitStrengthenWGData.GetStoneDescForCfg(attr_list[2].attr_str, attr_list[2].attr_value),
            }
        end
    end

    local strengthen_cfg = self:GetStrengthenLevelCfg(suit_seq, part)
    tips_info.strengthen_attr_list = EquipWGData.GetSortAttrListByTypeCfg(strengthen_cfg, "attr_id", "attr_value", 1, 2)
    local infusesoul_cfg = self:GetInfuseSoulLevelCfg(suit_seq, part)
    tips_info.infusesoul_attr_list = EquipWGData.GetSortAttrListByTypeCfg(infusesoul_cfg, "attr_id", "attr_value", 1, 2)

    return tips_info
end

function ShiTianSuitStrengthenWGData:GetOneRemindTabIndex(suit_seq, cur_index)
    if self:GetShiTianSuitEpRemind(suit_seq, SHOW_TYPE.Strengthen) then
        return SHOW_TYPE.Strengthen
    elseif self:GetShiTianSuitEpRemind(suit_seq, SHOW_TYPE.Gemstone) then
        return SHOW_TYPE.Gemstone
    elseif self:GetShiTianSuitEpRemind(suit_seq, SHOW_TYPE.InfuseSoul) then
        return SHOW_TYPE.InfuseSoul
    end

    return SHOW_TYPE.Strengthen
end

function ShiTianSuitStrengthenWGData:GetOneRemindEpIndex(suit_seq, cur_part, show_type)
    local first_act_part
    local remind_part
    local is_remind_jump = false
    if self.all_suit_info[suit_seq] then
        local func_key
        if show_type == SHOW_TYPE.Strengthen then
            func_key = "GetShiTianSuitEpCanStrengthen"
        elseif show_type == SHOW_TYPE.Gemstone then
            func_key = "GetShiTianSuitEpStoneRemind"
        elseif show_type == SHOW_TYPE.InfuseSoul then
            func_key = "GetShiTianSuitEpCanInfuseSoul"
        end

        for part = 0, STSUIT_MAX_PART_COUNT - 1 do
            local is_act = ShiTianSuitWGData.Instance:GetHoleIsAct(suit_seq, part)
            if self[func_key](self, suit_seq, part) then
                if part == cur_part then
                    return cur_part, true
                else
                    if not remind_part then
                        is_remind_jump = true
                        remind_part = part
                    end
                end
            end

            if is_act and not first_act_part then
                first_act_part = part
            end
        end
    end

    return remind_part or first_act_part or 0, is_remind_jump
end

-- 获取tips所需属性列表
function ShiTianSuitStrengthenWGData:GetShiTianEquipAttrList(suit_seq, part)
    local strengthen_cfg = self:GetStrengthenLevelCfg(suit_seq, part)
    local strengthen_attr_list = EquipWGData.GetSortAttrListByTypeCfg(strengthen_cfg, "attr_id", "attr_value", 1, 2)

    local stone_attr_list = {}
    local stone_list = self:GetStoneDataList(suit_seq, part)
    for k, v in pairs(stone_list) do
        if v > 0 then
            local stone_cfg = self:GetStoneCfg(v)
            local attr_list = EquipWGData.GetSortAttrListByTypeCfg(stone_cfg, "attr_id", "attr_val", 1, 2)
            for index, attr in pairs(attr_list) do
                if stone_attr_list[attr.attr_str] then
                    stone_attr_list[attr.attr_str] = stone_attr_list[attr.attr_str] + attr.attr_value
                else
                    stone_attr_list[attr.attr_str] = attr.attr_value
                end
            end
        end
    end

    local infuse_soul_cfg = self:GetInfuseSoulLevelCfg(suit_seq, part)
    local infuse_soul_attr_list = EquipWGData.GetSortAttrListByTypeCfg(infuse_soul_cfg, "attr_id", "attr_value", 1, 2)

    return strengthen_attr_list, stone_attr_list, infuse_soul_attr_list
end