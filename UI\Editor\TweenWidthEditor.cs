﻿using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UGUITweenWidth))]
public class TweenWidthEditor : UITweenerEditor 
{

	public override void OnInspectorGUI ()
	{
		GUILayout.Space(6f);
		UGUITweenEditorTools.SetLabelWidth(120f);

		UGUITweenWidth tw = target as UGUITweenWidth;
		GUI.changed = false;

		float from = EditorGUILayout.Slider("From", tw.from, 0f, 4000f);
		float to = EditorGUILayout.Slider("To", tw.to, 0f, 4000f);

		if (GUI.changed)
		{
			UGUITweenEditorTools.RegisterUndo("Tween Change", tw);
			tw.from = from;
			tw.to = to;
			UGUITweenEditorTools.SetDirty(tw);
		}

		DrawCommonProperties();
	}
}
