﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class UnityEngine_LightShadowsWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>num(typeof(UnityEngine.LightShadows));
		<PERSON><PERSON>("None", get_None, null);
		<PERSON><PERSON>("Hard", get_Hard, null);
		<PERSON><PERSON>("Soft", get_Soft, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		L.<PERSON>();
		TypeTraits<UnityEngine.LightShadows>.Check = CheckType;
		StackTraits<UnityEngine.LightShadows>.Push = Push;
	}

	static void Push(IntPtr L, UnityEngine.LightShadows arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(UnityEngine.LightShadows), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_None(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightShadows.None);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Hard(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightShadows.Hard);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Soft(IntPtr L)
	{
		ToLua.Push(L, UnityEngine.LightShadows.Soft);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		UnityEngine.LightShadows o = (UnityEngine.LightShadows)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

