local SkillAnchoredPos_4 = {
	Vector3(-154, 155, 0),	-- 普攻
	Vector3(-76, -130, 0),
	Vector3(-89, 12, 0),
	Vector3(23, 117, 0),
	Vector3(172, 93, 0),
}

local SkillAnchoredPos = {
	[1] = SkillAnchoredPos_4,
	[2] = SkillAnchoredPos_4,
	[3] = SkillAnchoredPos_4,
	[4] = SkillAnchoredPos_4,
	[5] = 
	{
		Vector3(-154, 155, 0),	-- 普攻
		Vector3(-76, -130, 0),
		Vector3(-97, -19, 0),
		Vector3(-43, 81, 0),
		Vector3(63, 125, 0),
		Vector3(172, 93, 0),
	}
}

MainUICommonSkillRender = MainUICommonSkillRender or BaseClass(BaseRender)
function MainUICommonSkillRender:__init()
	self.data = self:CreateSkillVo()

	if self.view.event_trigger_listener then
		self.view.event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnPointerUp, self))
		self.view.event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDrag, self))
		self.view.event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnPointerDown, self))
		self.view.event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.OnPointerExit, self))
	else
		self.view.button:AddClickListener(BindTool.Bind(self.OnClickSkill, self))
	end

	self.img_star_list = {}
	if self.node_list.img_star_1 then
		for i = 1, 5 do
			self.img_star_list[i] = self.node_list["img_star_" .. i]
		end
	end

	self.img_segment_mask_list = {}
	if self.node_list.root_segment then
		for i = 1, 10 do
			self.img_segment_mask_list[i] = self.node_list.root_segment:FindObj("img_segment_mask" .. i)
		end
	end

	if self.node_list.jiban_lock_btn then
		XUI.AddClickEventListener(self.node_list.jiban_lock_btn, BindTool.Bind(self.OnClickLockSkill, self))
	end

	self.play_baodian_effect_flag = false
end

function MainUICommonSkillRender:__delete()
	self:RemoveCountDownTimer()
	self:RemoveXiuWeiSeqDelayTimer()
	self:RemoveBuffCD()
	self:RemoveNuQiPowerEvent()
	self.img_star_list = nil
	self.img_segment_mask_list = nil
end

function MainUICommonSkillRender:CreateSkillVo()
	return {
		is_lock = true,
		skill_id = 0,
		duan_atk = 0,
		skill_index = 0,
		cell_index = 0,
		is_temp_show = false,
	}
end

function MainUICommonSkillRender:SetParent(parent)
	self.parent = parent
end

function MainUICommonSkillRender:SetData(key, value)
	self.data[key] = value
end

function MainUICommonSkillRender:GetBuffCDKey()
	if self.data and self.buff_cd_key == nil then
		self.buff_cd_key = "mianui_skill_buff_countdown" .. self.data.cell_index
	end

	return self.buff_cd_key
end

function MainUICommonSkillRender:SetPointerUpCallBack(call_back)
	self.pointer_up_cb = call_back
end

function MainUICommonSkillRender:SetDragCallBack(call_back)
	self.drag_cb = call_back
end

function MainUICommonSkillRender:SetPointerDownCallBack(call_back)
	self.pointer_down_cb = call_back
end

function MainUICommonSkillRender:SetPointerExitCallBack(call_back)
	self.pointer_exit_cb = call_back
end

function MainUICommonSkillRender:SetClickSkillCallBack(call_back)
	self.click_skill_cb = call_back
end

function MainUICommonSkillRender:OnPointerUp()
	if self.pointer_up_cb then
		self.pointer_up_cb(self.data)
	end
end

function MainUICommonSkillRender:OnDrag(eventData)
	if self.drag_cb then
		self.drag_cb(self.data, eventData)
	end
end

function MainUICommonSkillRender:OnPointerDown()
	self.play_baodian_effect_flag = false
	if self.pointer_down_cb then
		self.pointer_down_cb(self.data)
	end
end

function MainUICommonSkillRender:OnPointerExit()
	if self.pointer_exit_cb then
		self.pointer_exit_cb(self.data)
	end
end

function MainUICommonSkillRender:OnClickSkill()
	if IsEmptyTable(self.data) then
		return
	end

	-- local cell_index = self.data.cell_index
	-- if self.data.is_nuqi_skill then
	-- 	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	-- 	local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance
	-- 	local power_bianshen_flag = CultivationWGData.Instance:GetRoleBianshenFlag()
	-- 	local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()
	-- 	local curr_nuqi = CultivationWGData.Instance:GetRoleCurrNuqi()
	-- 	local curr_slider_value = curr_nuqi / 100

	-- 	if curr_slider_value >= 1 then
	-- 		if (not is_system_bianshen) and power_bianshen_flag == 0 and curr_nuqi_type ~= -1 then
	-- 			-- 请求怒气变身
	-- 			local main_role = Scene.Instance:GetMainRole()
	-- 			if main_role ~= nil and (not main_role:IsDeleted()) then
	-- 				main_role:SetXiuWeiBianShenSeq(true)
	-- 				MountWGCtrl.Instance:SendMountGoonReq(0)
	-- 				CultivationWGCtrl.Instance:AngerBecome()
	-- 				self:RemoveXiuWeiSeqDelayTimer()
	-- 				self.clear_main_role_xiuweiseq_timer = GlobalTimerQuest:AddDelayTimer(function ()
	-- 					self:RemoveXiuWeiSeqDelayTimer()
	-- 					main_role:SetXiuWeiBianShenSeq(false)
	-- 				end, 3)
	-- 			end
	-- 			return
	-- 		end
	-- 	else
	-- 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Cultivation.AngerSkillCD)
	-- 		return
	-- 	end
	-- end

	if self.click_skill_cb then
		self.click_skill_cb(self.data)
	end
end

function MainUICommonSkillRender:FlushForbidStatus(is_show)
	if self.node_list.forbid then
		self.node_list.forbid:SetActive(is_show)
	end
end

function MainUICommonSkillRender:FlushCell(skill_data, is_force)
	if IsEmptyTable(self.data) then
		return
	end

	if IsEmptyTable(skill_data) then
		self:SetCdBySkillIndex(0)
		self.view:SetActive(false)
		return
	end

	local cell_index = self.data.cell_index
	local skill_info = skill_data.skill_info
	local lock_skill_info = skill_data.lock_skill_info

	self.data.is_lock = true
	if not IsEmptyTable(skill_info) then
		local skill_id = skill_info.skill_id
		self.data.skill_index = skill_info.index
		self.data.is_lock = false
		self.data.is_temp_show = false
		self:UpdateCellData(skill_id, 1, is_force)
		if self.parent then
			self.parent:CheckRangIsVaild(cell_index, skill_id)
		end

		--判断是否在cd中
		local skill_end_time, ignore_global_cd = SkillWGData.Instance:GetSkillCDEndTime(skill_info.index)
		if skill_end_time > Status.NowTime * 1000 then -- 还在cd中
			self:SetCdBySkillIndex(skill_end_time - Status.NowTime * 1000)
		end
	elseif not IsEmptyTable(lock_skill_info) then
		self.data.skill_index = lock_skill_info.index
		self.data.is_lock = false
		self.data.is_temp_show = lock_skill_info.is_temp_show
		self:UpdateCellData(lock_skill_info.skill_id, 1, true)
	end

	if self.data.is_lock then
		self.data.skill_id = 0
		self.data.is_lock = false
		self.view:SetActive(false)
	else
		self.view:SetActive(true)
	end
end

function MainUICommonSkillRender:UpdateCellData(skill_id, duan_atk, is_force)
	if not is_force and self.data.skill_id == skill_id and self.data.duan_atk == duan_atk then
		return
	end

	local is_funtion_open = SkillWGData.Instance:GetSkillisFunctionOpen()
	if is_funtion_open then
		return
	end

	self.data.skill_id = skill_id
	self.data.duan_atk = duan_atk

	local cell_index = self.data.cell_index
	local is_temp_show = self.data.is_temp_show
	local icon_id = SkillWGData.Instance:GetSkillIconId(skill_id, duan_atk)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	local is_system_bianshen = SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM == main_role_vo.special_appearance

	if cell_index == 1 then
		self:AddNuQiPowerEvent()
		XUI.SetGraphicGrey(self.node_list.icon, is_temp_show)
		self.node_list.icon.gameObject:SetActive(true)
		local is_xmz_car = Scene.Instance:GetMainRole():IsXMZCar()
		if is_xmz_car then
			--仙盟战 变身采集车时  如果有采集到灵石 按钮图片就是 上交  没有灵石时就是 采集
			local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
			local gather_id = role_info_list.gather_id
			local xmz_other_cfg = GuildBattleRankedWGData.Instance:GetCfgOther()
			if gather_id and gather_id > 0 then
				icon_id = xmz_other_cfg.tijiao_skill_icon
			else
				icon_id = xmz_other_cfg.caiji_skill_icon
			end
		else
			local power_icon_id = self:UpdateNuQiPowerEvent()
			if power_icon_id ~= nil then
				icon_id = power_icon_id
			end
		end

		local bundle, name = ResPath.GetSkillIconById(icon_id)
		self.node_list.icon.image:LoadSprite(bundle, name, function ()
			self.node_list.icon.image:SetNativeSize()
		end)
		
		return
	end

	XUI.SetGraphicGrey(self.node_list.icon, is_temp_show)
	self.node_list.jiban_lock_btn:SetActive(is_temp_show)

	-- 特殊图标
	self.node_list.icon.gameObject:SetActive(not self.data.is_lock)
	local show_cfg = SkillWGData.Instance:GetSkillSpecialShowCfg(skill_id)
	if show_cfg and (show_cfg.show_type == SKILL_SPECIAL_SHOW.WUXING_ICON or show_cfg.show_type == SKILL_SPECIAL_SHOW.WUXING_ICON_AND_TIP) then
		local use_wuxing = TianShenWGData.Instance:GetMultiSkillUseIndex() or 1
		icon_id = icon_id .. "_" .. use_wuxing
	end

	local bundle, name = ResPath.GetSkillIconById(icon_id)
	self.node_list.icon.image:LoadSprite(bundle, name)

	-- 技能类型
	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(skill_id)
	if self.node_list.skill_type ~= nil then	-- 加个容错
		if skill_type_tab and skill_type_tab[1] and tonumber(skill_type_tab[1]) ~= 0 then
			self.node_list.skill_type:SetActive(true)
			self.node_list.skill_type.text.text = Language.Skill.CommonSkillType[skill_type_tab[1]] or ""
		else
			self.node_list.skill_type:SetActive(false)
		end
	end

	local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
	if not skill_cfg then
		return
	end

	-- 变身需要显示技能开启等级
	local is_gundam = SPECIAL_APPEARANCE_TYPE.GUNDAM == main_role_vo.special_appearance
	if is_system_bianshen then
		local skill_info = SkillWGData.Instance:GetSkillInfoById(skill_id)
		if not skill_info then
			self.node_list.lock:SetActive(true)
			XUI.SetGraphicGrey(self.node_list.icon, true)
		else
			self.node_list.lock:SetActive(false)
		end
	else
		self.node_list.lock:SetActive(false)
	end
	
	---设置武魂绑定信息(武魂绑定从1开始的这里减一)
	local main_role = Scene.Instance:GetMainRole()
	local is_riding_fight_mount = main_role:IsRidingFightMount()
	local is_bind, wuhun_skill = WuHunWGData.Instance:GetCurrWuHunBindSkill(cell_index - 1)
	if self.node_list and self.node_list.wuhun_root then
		self.node_list.wuhun_root:SetActive(is_bind and (not is_system_bianshen) and (not is_riding_fight_mount) and not is_gundam)
		
		if is_bind and (not is_system_bianshen) and (not is_riding_fight_mount) and not is_gundam then
			self.node_list.wuhun_icon.image:LoadSprite(ResPath.GetSkillIconById(wuhun_skill))
		end
	end
end

-- 添加怒气变化事件
function MainUICommonSkillRender:AddNuQiPowerEvent()
	if not self.update_nuqi_event then
		self.update_nuqi_event = GlobalEventSystem:Bind(OtherEventType.ROLE_SKILL_POWER_CHANGE, BindTool.Bind(self.UpdateNuQiPowerEvent, self))
	end
end

-- 移除怒气变化事件
function MainUICommonSkillRender:RemoveNuQiPowerEvent()
	if self.update_nuqi_event then
        GlobalEventSystem:UnBind(self.update_nuqi_event)
        self.update_nuqi_event = nil
    end
end

--怒气变化事件
function MainUICommonSkillRender:UpdateNuQiPowerEvent()
	-- 是否展示怒气
	local curr_nuqi_type = CultivationWGData.Instance:GetRoleCurrNuqiType()

	if self.node_list.power_slider then
		local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
		local is_show_slider = SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN == main_role_vo.special_appearance or main_role_vo.special_appearance == 0
		self.node_list.power_slider:CustomSetActive(is_show_slider and curr_nuqi_type ~= -1)		-- 没变身才展示怒气进度
		local curr_nuqi = CultivationWGData.Instance:GetRoleCurrNuqi()
		local curr_slider_value = curr_nuqi / 100
		self.node_list.power_slider.slider.value = curr_slider_value * 0.8

		-- if self.node_list.power_effect then
		-- 	self.node_list.power_effect:CustomSetActive((curr_nuqi_type ~= -1 and curr_slider_value >= 1 and main_role_vo.special_appearance == 0) 
		-- 		or SPECIAL_APPEARANCE_TYPE.XIUWEIBIANSHEN == main_role_vo.special_appearance
		-- 	)
		-- end

		-- 怒气大于100 且是普通形象才会改变为怒气图标
		-- if curr_slider_value >= 1 and main_role_vo.special_appearance == 0 and curr_nuqi_type ~= -1 then
		-- 	local cfg = CultivationWGData.Instance:GetActiveCfgByType(curr_nuqi_type)

		-- 	if cfg then
		-- 		local bundle, name = ResPath.GetSkillIconById(cfg.skill_icon)
		-- 		self.node_list.icon.image:LoadSprite(bundle, name, function ()
		-- 			self.node_list.icon.image:SetNativeSize()
		-- 		end)
		-- 		return cfg.skill_icon
		-- 	end
		-- end
	end
	return nil
end

function MainUICommonSkillRender:UpdateSkillBtnPos(skill_num)
	if IsEmptyTable(self.data) then
		return
	end

	local show_pos_list = SkillAnchoredPos[skill_num]
	if not show_pos_list then
		return
	end

	local cell_index = self.data.cell_index
	local skill_pos = show_pos_list[cell_index]
	self:SetAnchoredPosition(skill_pos.x, skill_pos.y)
end

--设置技能CD byindex
--@is_specialskill 特殊技能:变身技能
function MainUICommonSkillRender:SetCdBySkillIndex(cd)
	if IsEmptyTable(self.data) then
		return
	end

	local cell_index = self.data.cell_index
	local skill_id = self.data.skill_id
	if cell_index == 1 then
		return
	end

	if self.count_down and
		(self.count_down.total_time - self.count_down.elapse_time * 1000) ~= cd then
		self:RemoveCountDown(true)
	end

	if not self.count_down then
		self.node_list.cd_mark:SetActive(true)
		local is_skill_cd = cd > COMMON_CONSTS.SKILL_GLOBAL_CD
		local fenduan_time = self:JustShowBlackMark(skill_id, cd)

		-- 爆点特效
		if self.play_baodian_effect_flag and is_skill_cd then
			self.play_baodian_effect_flag = false
			local bundle_name1, asset_name1 = ResPath.GetEffectUi(Ui_Effect.UI_click)
			EffectManager.Instance:PlayControlEffect(self, bundle_name1, asset_name1, self.node_list.baodian_eff.transform.position, nil, self.node_list.baodian_eff.transform)
		end

		local function timer_func(elapse_time, total_time)
			local hao_miao = 1000
			elapse_time = elapse_time * hao_miao
			if 0 ~= fenduan_time and elapse_time <= fenduan_time then
				self.node_list.cd_mark.image.fillAmount = 1
				self.node_list.cd_text:SetActive(false)
				return
			end

			self.node_list.cd_text:SetActive(true)

			elapse_time = elapse_time - fenduan_time
			total_time = total_time - fenduan_time

			if total_time - elapse_time > hao_miao then
				local fill_amount = 1 - (elapse_time / hao_miao) / (total_time / hao_miao)
				self.node_list.cd_mark.image.fillAmount = fill_amount
				self.node_list.cd_text.text.text = (math.ceil((total_time - elapse_time) / hao_miao))
			else
				local fill_amount = 1 - (elapse_time / hao_miao) / (total_time / hao_miao)
				self.node_list.cd_mark.image.fillAmount = fill_amount
				self.node_list.cd_text.text.text = string.format("%.1f", (total_time - elapse_time) / hao_miao)
			end

			if elapse_time >= total_time then
				self:RemoveCountDown()
				return
			end
		end

		self.count_down = CountDown.Instance:AddCountDown(cd, 0.05, timer_func)
	end
end

-- 特殊需求，多段伤害技能前期，只制灰，不跑CD
function MainUICommonSkillRender:JustShowBlackMark(skill_id, cd)
	local fenduan_skill_cfg = SkillWGData.Instance:GetFenDuanSkill(skill_id)
	local skill_cfg = SkillWGData.Instance:GetSkillConfigByIdLevel(skill_id, 1)
	if not fenduan_skill_cfg or not skill_cfg then
		return 0
	end

	if cd < skill_cfg.cd_s then
    	return 0
    end

    -- if cd < skill_cfg.cd_s or skill_cfg.cd_s - cd < time then return 0 end
    return fenduan_skill_cfg.total_time
end

function MainUICommonSkillRender:PlayBaoDianEffect()
	self.play_baodian_effect_flag = true
end


----------- 角色buff改变 -----------------
function MainUICommonSkillRender:FlushBuffLayer(data, is_init)
	if IsEmptyTable(self.data) or self.data.cell_index == 1 then
		return
	end

	local skill_id = data and data.skill_id
	local cfg = SkillWGData.Instance:GetBuffLayerCfg(skill_id)
	if data == nil or cfg == nil then
		self.node_list.root_segment:SetActive(false)
		self.node_list.root_buff_eff:SetActive(false)
		self.node_list.root_time:SetActive(false)
		self.node_list.root_star:SetActive(false)
		XUI.SetGraphicGrey(self.node_list.icon, false)
		return
	end

	if cfg.buff_type and cfg.buff_type > 0 then
		if self.parent then
			self.parent:SetBuffLayerFlag(cfg.buff_type)
		end
	end

	local effect_buff_type = cfg.effect_buff_type
	if is_init and effect_buff_type == SKILL_BUFF_SHOW.LAYER then
		local max_layer = cfg.max_layer
		local angle = 360 / max_layer
		for k,v in ipairs(self.img_segment_mask_list) do
			if k <= max_layer then
				v:SetActive(true)
				local x = math.cos(math.rad(angle * k)) * 42.2
				local y = math.sin(math.rad(angle * k)) * 42.2
				Transform.SetLocalPositionXYZ(v.transform, x, y, 0)
				v.transform.localRotation = Quaternion.Euler(0, 0, angle * k)
			else
				v:SetActive(false)
			end
		end

		self.node_list.img_segment_pro.transform.localRotation = Quaternion.Euler(0, 0, angle + 90)
	end

	self.node_list.root_segment:SetActive(effect_buff_type == SKILL_BUFF_SHOW.LAYER)
	self.node_list.root_star:SetActive(effect_buff_type == SKILL_BUFF_SHOW.STAR)

	local pro = 0
	local buff_show = false
	local has_time = false
	local time = 0
	local show_star_num = 0
	local is_gray = false
    local buff_list = FightWGData.Instance:GetMainRoleEffectList()
	if buff_list ~= nil and effect_buff_type ~= SKILL_BUFF_SHOW.EFFECT then
		for k = 1, #buff_list do
			if buff_list[k].buff_type == cfg.buff_type then
				if effect_buff_type == SKILL_BUFF_SHOW.LAYER then
					pro = buff_list[k].merge_layer / cfg.max_layer
				else
					if effect_buff_type == SKILL_BUFF_SHOW.EFFECT_MY then
						buff_show = true
					elseif effect_buff_type == SKILL_BUFF_SHOW.TIMER then
						has_time = true
						time = buff_list[k].cd_time
					elseif effect_buff_type == SKILL_BUFF_SHOW.STAR then
						buff_show = buff_list[k].merge_layer == cfg.max_layer
						show_star_num = buff_list[k].merge_layer
					end
				end

				break
			end
		end
	end

	local target_buff_list = nil
    if nil ~= GuajiCache.target_obj and not GuajiCache.target_obj:IsDeleted() then
    	local target_obj_id = GuajiCache.target_obj:GetObjId()
    	target_buff_list = FightWGData.Instance:GetOtherRoleEffectList(target_obj_id)
    end

	if target_buff_list ~= nil and effect_buff_type == SKILL_BUFF_SHOW.EFFECT then
		for k = 1, #target_buff_list do
			if target_buff_list[k].buff_type == cfg.buff_type then
				buff_show = true
				break
			end
		end
	end

	if effect_buff_type == SKILL_BUFF_SHOW.LAYER then
		self.node_list.img_segment_pro.image.fillAmount = pro
	end
	self.node_list.root_buff_eff:SetActive(buff_show or pro == 1)

	if effect_buff_type == SKILL_BUFF_SHOW.STAR then
		local skill_value = SkillWGData.Instance:GetSkillInfoById(skill_id)
		if skill_value == nil then
			show_star_num = 0
		end

		for i = 1, 5 do
			local star = self.img_star_list[i]
			if star then
				star:SetActive(i <= show_star_num and i <= cfg.max_layer)
			end
		end

		local is_limit = SkillWGData.Instance:CheckIsLimitUseSkill(skill_id)
		if not skill_value then
			is_limit = true
		end

		is_gray = is_limit
	end

	XUI.SetGraphicGrey(self.node_list.icon, is_gray)
	if has_time then
		self:FlushSkillBuffTimer(time)
	else
		self.node_list.root_time:SetActive(false)
	end
end

function MainUICommonSkillRender:RemoveBuffCD()
	local key = self:GetBuffCDKey()
	if CountDownManager.Instance:HasCountDown(key) then
		CountDownManager.Instance:RemoveCountDown(key)
	end
end

-- 刷新倒计时   end_time 下次可用时间戳   bianshen_end_time  变身结束时间戳
function MainUICommonSkillRender:FlushSkillBuffTimer(end_time)
	local key = self:GetBuffCDKey()
	self:RemoveBuffCD()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	self:FlushBuffEffTime(0, end_time)

	if end_time > 0 then
		self.node_list.root_time:SetActive(true)
		CountDownManager.Instance:AddCountDown(key, BindTool.Bind(self.FlushBuffEffTime, self),
											BindTool.Bind(self.FlushBuffEffTimeCom, self), nil, end_time, 0.1)
	else
		self.node_list.root_time:SetActive(false)
	end
end

function MainUICommonSkillRender:FlushBuffEffTime(elapse_time, total_time)
	if total_time > 0 then
		local value = elapse_time / total_time
		if self.node_list.root_time_pro ~= nil then
			self.node_list.root_time_pro:SetActive(true)
			self.node_list.root_time_pro.slider.value = value
		end

		if self.node_list.time_hl_point ~= nil then
			self.node_list.time_hl_point:SetActive(true)
			self.node_list.time_hl_point.transform.localRotation = Quaternion.Euler(0, 0, -360 * (1 - value))
		end
	else
		if self.node_list.root_time_pro ~= nil then
			self.node_list.root_time_pro:SetActive(false)
		end

		if self.node_list.time_hl_point ~= nil then
			self.node_list.time_hl_point:SetActive(false)
		end
	end
end

function MainUICommonSkillRender:FlushBuffEffTimeCom()
	if self.node_list.root_time then
		self.node_list.root_time:SetActive(false)
	end
end
----------- 角色buff改变 end -----------------


function MainUICommonSkillRender:RemoveCountDownTimer()
	if self.count_down then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function MainUICommonSkillRender:RemoveCountDown(is_update)
	if not is_update then
		if self.node_list.cd_mark then
			self.node_list.cd_mark:SetActive(false)
			self.node_list.cd_mark.image.fillAmount = 0
		end

		if self.node_list.cd_text then
			self.node_list.cd_text:SetActive(false)
		end
	end

	self:RemoveCountDownTimer()
end

-- 点击羁绊上锁按钮
function MainUICommonSkillRender:OnClickLockSkill()
	local index = self.data and self.data.cell_index or 1
	if self.data.cell_index == 1 then
		return
	end
	
	local lock_jiban_data = SkillWGData.Instance:GetJiBanLockInfoByIndex(index)
	if not IsEmptyTable(lock_jiban_data) then
		local tianshen_cfg =  TianShenWGData.Instance:GetJiBanTianShenBySkillID(lock_jiban_data.skill_id)
		if not IsEmptyTable(tianshen_cfg) then
			local need_tianshen = TianShenWGData.Instance:GetTianShenCfg(tianshen_cfg.jiban_index)
			local act_cfg = TianShenWGData.Instance:GetTianshenItemCfgByIndex(tianshen_cfg.jiban_index)
			local item_cfg = {}
			if act_cfg and act_cfg.act_item_id then
				item_cfg = ItemWGData.Instance:GetItemConfig(act_cfg.act_item_id)
			end

			local color = item_cfg.color or need_tianshen.series + 2
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.TianShen.TianShenJiBanActive,ToColorStr(need_tianshen.bianshen_name, ITEM_COLOR[color])))
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShen.TianShenJiBanActive2)
		end
	end
end

--移除回调
function MainUICommonSkillRender:RemoveXiuWeiSeqDelayTimer()
    if self.clear_main_role_xiuweiseq_timer then
        GlobalTimerQuest:CancelQuest(self.clear_main_role_xiuweiseq_timer)
        self.clear_main_role_xiuweiseq_timer = nil
    end
end

