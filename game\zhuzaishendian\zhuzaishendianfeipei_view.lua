ZhuZaiFenPeiRewardView = ZhuZaiFenPeiRewardView or BaseClass(SafeBaseView)

function ZhuZaiFenPeiRewardView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/zhuzaishendian_ui_prefab", "layout_guildlist")
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self.open_type = 0
end

function ZhuZaiFenPeiRewardView:__delete()
	self.open_type = nil
end

function ZhuZaiFenPeiRewardView:ReleaseCallBack()
		if nil ~= self.reward_distribution_list then
		self.reward_distribution_list:DeleteMe()
		self.reward_distribution_list = nil
	end

end

function ZhuZaiFenPeiRewardView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.anchoredPosition = Vector2(-3.29,-24.62)
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(678.68,489.32)
	self.node_list.title_view_name.text.text = Language.GuildBattleRanked.FenpeiTitle
	self:InitZhuZaiFenPeiRewardView()
end

function ZhuZaiFenPeiRewardView:CloseCallBack()
	self.open_type = 0
end

function ZhuZaiFenPeiRewardView:InitZhuZaiFenPeiRewardView()
	if nil == self.reward_distribution_list then
		self.reward_distribution_list = AsyncListView.New(DistributionItemRender, self.node_list["ph_member_list"])
		self.reward_distribution_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectMemberListItemHandler, self))
	end
	self.node_list.btn_distribution.button:AddClickListener(BindTool.Bind(self.OnClickExamine, self))
	self.node_list.btn_min_close.button:AddClickListener(BindTool.Bind(self.OnClickCloseBtnHandler, self))
end

function ZhuZaiFenPeiRewardView:OnClickExamine()
	local  firstguild_id = {}
	 firstguild_id = ZhuZaiShenDianWGData.Instance:GetGuildId()

	if self.uid and firstguild_id then
 		ZhuZaiShenDianWGCtrl.Instance:SendCSGuildAppointKeepWinReward(firstguild_id[1].guild_id,self.uid)
 	end
 	self:Close()
end

function ZhuZaiFenPeiRewardView:GoToJoinAct()

end

function ZhuZaiFenPeiRewardView:OnSelectMemberListItemHandler(item)
	if nil == item then
		return
	end
	if nil == item.data then
		return
	end
	self.uid = item.data.uid
end

function ZhuZaiFenPeiRewardView:OnFlush(param_t, index)
	local m_list = GuildDataConst.GUILD_MEMBER_LIST
	self.member_datasource = {}
	for i = 1, m_list.count do
		local item = m_list.list[i]
		local datasource = {uid = item.uid, role_name = item.role_name, level = item.level, sex = item.sex, prof = item.prof,
			post = item.post, vip_type = item.vip_type, vip_level = item.vip_level, is_online = item.is_online, join_time = item.join_time,
			last_login_time = item.last_login_time, gongxian = item.gongxian, total_gongxian = item.total_gongxian, capability = item.capability}
		table.insert(self.member_datasource, datasource)
	end
	if nil ~= self.reward_distribution_list then
		table.sort(self.member_datasource, BindTool.Bind1(self.SortMemberList, self))
		self.reward_distribution_list:SetDataList(self.member_datasource,0)
		self.reward_distribution_list:SelectIndex(1)
	end

end

-- 排序成员列表
function ZhuZaiFenPeiRewardView:SortMemberList(a, b)
	local a_post = GuildDataConst.GUILD_POST_AUTHORITY_LIST[a.post]
	local b_post = GuildDataConst.GUILD_POST_AUTHORITY_LIST[b.post]


	if nil == a_post or nil == b_post then
		return false
	end

	if a.is_online == b.is_online then
		if a.is_online == 0 then
			return a.last_login_time > b.last_login_time
		else
			if a_post.post_index == b_post.post_index then
				return a.capability > b.capability
			else
				return a_post.post_index < b_post.post_index
			end
		end
	else
		return a.is_online > b.is_online
	end

end
function ZhuZaiFenPeiRewardView:OnClickCloseBtnHandler()
	self:Close()
end


DistributionItemRender = DistributionItemRender or BaseClass(BaseRender)
function DistributionItemRender:__init()
	self:AddClickEventListener()
end

function DistributionItemRender:__delete()
	if nil ~= self.lbl_vip then
		self.lbl_vip:DeleteMe()
		self.lbl_vip = nil
	end
end

function DistributionItemRender:CreateChild()
	BaseRender.CreateChild(self)
end

function DistributionItemRender:OnFlush()
	if nil == self.data then
		return
	end
	XUI.UpdateRoleHead(self.node_list.img_head, self.data.uid, self.data.sex,self.data.prof,nil,true)
	self.node_list.lbl_name.text.text = self.data.role_name
	local level_str = RoleWGData.GetLevelString(self.data.level)
	local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(self.data.level)
	self.node_list.img_feixian:SetActive(is_vis)
	if is_vis then
		self.node_list.lbl_level.text.text = level_str
	else
		self.node_list.lbl_level.text.text = "Lv."..level_str
	end
	local post_list = GuildDataConst.GUILD_POST_AUTHORITY_LIST
	if nil ~= post_list[self.data.post] then
		self.node_list.lbl_post.text.text = post_list[self.data.post].post
	end
end

function DistributionItemRender:OnBtnTips()

end

function DistributionItemRender:OpenFenPEI()
	ZhuZaiShenDianWGCtrl.Instance:OpenZhuZaiFenPei()
end

function DistributionItemRender:JoinFuBenHandler()

end
-- 选择状态改变
function DistributionItemRender:OnSelectChange(is_select)
	if self.node_list.hight then
		self.node_list.hight:SetActive(is_select)
	end
end