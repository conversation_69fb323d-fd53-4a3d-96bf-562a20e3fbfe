------------------------------------------------------------
--山海经相关主View
------------------------------------------------------------
LinZhiSkillView = LinZhiSkillView or BaseClass(SafeBaseView)

function LinZhiSkillView:__init()
	self.view_style = ViewStyle.Full
	self:SetMaskBg()
	self:LoadConfig()
	self.default_index = TabIndex.lingzhi_ql_skill
	self.tab_sub = {}
	self.lingzhi_view_type = LINGZHI_SKILL_TYPE.WING

	self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
end

function LinZhiSkillView:__delete()

end

function LinZhiSkillView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "VerticalTabbar")
	self:AddViewResource(TabIndex.lingzhi_ql_skill, "uis/view/lingzhi_prefab", "layout_lingzhi_sj")
	self:AddViewResource(TabIndex.lingzhi_ql_up, "uis/view/lingzhi_prefab", "layout_lingzhi_xl")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a1_commmon_panel_adorn")
end

function LinZhiSkillView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:XLReleaseCallBack()
	self:SJReleaseCallBack()
end

function LinZhiSkillView:SetViewType(lingzhi_view_type)
	self.lingzhi_view_type = lingzhi_view_type

	self.remind_tab = {}
	self.GuideModuleName = GuideModuleName.WingLinZhiSkillView
	if self.lingzhi_view_type == LINGZHI_SKILL_TYPE.WING then
		self.remind_tab = {
			{ RemindName.LingZhi_Wing_SJ },
			{ RemindName.LingZhi_Wing_XL }, }
		self.GuideModuleName = GuideModuleName.WingLinZhiSkillView
	elseif self.lingzhi_view_type == LINGZHI_SKILL_TYPE.FABAO then
		self.remind_tab = {
			{ RemindName.LingZhi_FaBao_SJ },
			{ RemindName.LingZhi_FaBao_XL }, }
		self.GuideModuleName = GuideModuleName.FaBaoLinZhiSkillView
	elseif self.lingzhi_view_type == LINGZHI_SKILL_TYPE.JIANZHEN then
		self.remind_tab = {
			{ RemindName.LingZhi_JianZhen_SJ },
			{ RemindName.LingZhi_JianZhen_XL }, }
		self.GuideModuleName = GuideModuleName.JianZhenLinZhiSkillView
	elseif self.lingzhi_view_type == LINGZHI_SKILL_TYPE.SHENBING then
		self.remind_tab = {
			{ RemindName.LingZhi_ShenBing_SJ },
			{ RemindName.LingZhi_ShenBing_XL }, }
		self.GuideModuleName = GuideModuleName.ShenBingLinZhiSkillView
	end
end

function LinZhiSkillView:OpenCallBack()
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback, false)
end

function LinZhiSkillView:CloseCallBack()
	TipWGCtrl.Instance:ForceHideEffect()
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
end

function LinZhiSkillView:LoadCallBack()
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("lingzhi")
		self.tabbar:Init(Language.LingZhi.TabGrop, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.node_list.title_view_name.text.text = Language.LingZhi.LingZhiName
		FunOpen.Instance:RegsiterTabFunUi(self.GuideModuleName, self.tabbar)
	end
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function LinZhiSkillView:LoadIndexCallBack(index)
	if index == TabIndex.lingzhi_ql_skill then
		self:SJLoadCallBack()
	elseif index == TabIndex.lingzhi_ql_up then
		self:XLLoadCallBack()
	end
end

function LinZhiSkillView:ShowIndexCallBack(index)
	if index == TabIndex.lingzhi_ql_skill then
		self:SJShowIndexCallBack()
	elseif index == TabIndex.lingzhi_ql_up then
		self:XLShowIndexCallBack()
	end
end

function LinZhiSkillView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.lingzhi_ql_skill then
				self:SJFlush(param_t)
			elseif index == TabIndex.lingzhi_ql_up then
				self:XLFlush(param_t)
			end
		end
	end
end

--物品变化
function LinZhiSkillView:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	ViewManager.Instance:FlushView(GuideModuleName.WingLinZhiSkillView, TabIndex.lingzhi_ql_up)
	ViewManager.Instance:FlushView(GuideModuleName.FaBaoLinZhiSkillView, TabIndex.lingzhi_ql_up)
	ViewManager.Instance:FlushView(GuideModuleName.JianZhenLinZhiSkillView, TabIndex.lingzhi_ql_up)
	ViewManager.Instance:FlushView(GuideModuleName.ShenBingLinZhiSkillView, TabIndex.lingzhi_ql_up)
end
