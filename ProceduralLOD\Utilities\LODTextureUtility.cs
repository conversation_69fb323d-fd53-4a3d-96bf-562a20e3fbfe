#if UNITY_EDITOR

using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.RegularExpressions;
using UnityEditor;
using UnityEngine;
using UnityEngine.Experimental.Rendering;
using Object = UnityEngine.Object;

namespace ProceduralLOD
{
    public static class LODTextureUtility
    {
        public static readonly int MinSize = 128;
        public static readonly string[] AssetLabels = new[] { "IgnoreImportRule" };

        private static Dictionary<string, Texture2D> guid2Texture;

        public delegate Texture2D MergeFunction(Texture2D[] textures);

        public class TextureReadableScope : IDisposable
        {
            List<string> readed = new List<string>();

            public TextureReadableScope(GameObject obj)
            {
                SetupTextures(GetTextures(obj));
            }
            
            public TextureReadableScope(Texture[] textures)
            {
                SetupTextures(textures);
            }
            
            void SetupTextures(Texture[] textures)
            {
                for (int i = 0; i < textures.Length; i++)
                {
                    Texture texture = textures[i];
                    string file = AssetDatabase.GetAssetPath(texture);
                    if (file != "")
                    {
                        AssetDatabase.SetLabels(texture, AssetLabels);
                        TextureImporter importer = AssetImporter.GetAtPath(file) as TextureImporter;
                        importer.isReadable = true;
                        importer.SaveAndReimport();
                        readed.Add(file);
                    }
                }
            }

            public void Dispose()
            {
                foreach (var file in readed)
                {
                    Texture texture = AssetDatabase.LoadAssetAtPath<Texture>(file);
                    AssetDatabase.SetLabels(texture, new string[0]);
                    TextureImporter importer = AssetImporter.GetAtPath(file) as TextureImporter;
                    importer.isReadable = false;
                    importer.SaveAndReimport();
                }
            }
        }
        
        public class MaterialReadableScope : IDisposable
        {
            private Dictionary<Material, Dictionary<string, Texture2D>> sources = new Dictionary<Material, Dictionary<string, Texture2D>>();

            public MaterialReadableScope(GameObject obj)
            {
                List<Material> materials = new List<Material>();
                foreach (var renderer in obj.GetComponentsInChildren<Renderer>())
                {
                    materials.AddRange(renderer.sharedMaterials);
                }
                
                SetupTextures(materials.ToArray());
            }
            
            public MaterialReadableScope(Material[] materials)
            {
                SetupTextures(materials);
            }
            
            void SetupTextures(Material[] materials)
            {
                for (int i = 0; i < materials.Length; i++)
                {
                    Material material = materials[i];
                    foreach (var prop in material.GetTexturePropertyNames())
                    {
                        Texture2D tex = material.GetTexture(prop) as Texture2D;
                        if (tex != null && string.IsNullOrEmpty(AssetDatabase.GetAssetPath(tex)))
                        {
                            Texture2D source = tex;
                            Resize(ref tex, tex.width, tex.height);
                            if (!sources.TryGetValue(material, out var dict))
                            {
                                sources.Add(material, new Dictionary<string, Texture2D>());
                            }
                            
                            dict.TryAdd(prop, source);
                        }
                    }
                }
            }

            public void Dispose()
            {
                foreach (var pairs1 in sources)
                {
                    foreach (var pairs2 in pairs1.Value)
                    {
                        pairs1.Key.SetTexture(pairs2.Key, pairs2.Value);
                    }
                }
            }
        }

        public static Texture2D MakeMipTexture(MergeFunction merge, params Texture2D[] textures)
        {
            Texture mainTex = textures[0];
            if (mainTex == null)
            {
                return null;
            }

            string path = AssetDatabase.GetAssetPath(mainTex);
            string guid = AssetDatabase.AssetPathToGUID(path);
            if (guid2Texture.TryGetValue(guid, out Texture2D cache))
            {
                return cache;
            }

            Texture2D mipTex = null; 
            
            using (new TextureReadableScope(textures))
            {
                try
                {
                    mipTex = merge(textures);

                    Debug.Assert(mipTex != null);

                    int size = Mathf.Min(mainTex.width, mainTex.height);
                    int shitbit = Regex.IsMatch(textures[0].name, @"_LOD\d+") ? 1 : 2;
                    int shift = (int)Mathf.Min(Mathf.Log(size, 2) - Mathf.Log(MinSize, 2), shitbit);
                    shift = Mathf.Min(mipTex.mipmapCount - 1, shift);
                    Color[] colors = mipTex.GetPixels(shift);
                    mipTex = new Texture2D(mipTex.width >> shift, mipTex.height >> shift, mipTex.format, false, false);
                    mipTex.SetPixels(colors);
                    mipTex.Apply();

                    guid2Texture.Add(guid, mipTex);
                }
                catch (Exception e)
                {
                    Debug.LogError(e);
                }
            }

            return mipTex;
        }

        public static Texture[] GetTextures(GameObject target)
        {
            List<Texture> textures = new List<Texture>();
            Renderer[] renderers = target.GetComponentsInChildren<Renderer>();
            foreach (var renderer in renderers)
            {
                Material[] materials = renderer.sharedMaterials;
                foreach (var material in materials)
                {
                    foreach (var name in  material.GetTexturePropertyNames())
                    {
                        Texture texture = material.GetTexture(name);
                        if (texture != null)
                            textures.Add(texture);
                    }
                }
            }

            return textures.ToArray();
        }

        public static void Resize(ref Texture2D texture, int width, int height)
        {
            Texture2D newTexture = new Texture2D(width, height, texture.format, texture.mipmapCount > 1, false);
            var oldRT = RenderTexture.active;
            var rt = RenderTexture.GetTemporary(width, height, 0, RenderTextureFormat.ARGB32);
            Graphics.Blit(texture, rt);
            RenderTexture.active = rt;
            newTexture.ReadPixels(new Rect(0, 0, width, height), 0, 0);
            newTexture.Apply();
            RenderTexture.ReleaseTemporary(rt);
            RenderTexture.active = oldRT;
            texture = newTexture;
        }
        
        public static void ClearCache()
        {
            guid2Texture = new Dictionary<string, Texture2D>();
        }
        
        public static string GetSourceID(Texture2D texture)
        {
            foreach (var pair in guid2Texture)
            {
                if (pair.Value == texture)
                {
                    return pair.Key;
                }
            }

            return null;
        }
        
        public static void SetCache(string key, Object texture)
        {
            if (!string.IsNullOrEmpty(key))
            {
                guid2Texture[key] = texture as Texture2D;
            }
        }
    }
}

#endif