ActSliderNumView = ActSliderNumView or BaseClass(SafeBaseView)

function ActSliderNumView:__init(view_name)
	self.view_name = "ActSliderNumView"
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(730,464)})
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "act_slider_num_panel")

	self:InitParam()
end

function ActSliderNumView:LoadCallBack()
	self:InitPanel()
	self:InitListener()
end

function ActSliderNumView:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function ActSliderNumView:OnFlush(param_t)
	for k,v in pairs(param_t) do
		if k == "info_list" then
			self.sure_call_back = v.sure_call_back
			self.limit_max_num = v.max_num or 1
			self.limit_min_num = v.min_num or 0
			self.item_data = v.item_data
			self.view_title_str = v.title_str or ""
			self:RefreshView(v.cur_num)
		end
	end
end

function ActSliderNumView:InitParam()
	self.sure_call_back = nil
	self.limit_max_num = 1
	self.limit_min_num = 0
	self.item_data = nil
	self.view_title_str = ""
	self.save_slider_value = 1
end

function ActSliderNumView:InitPanel()
	self.item_cell = ItemCell.New(self.node_list.cell_root)
end

function ActSliderNumView:InitListener()
	XUI.AddClickEventListener(self.node_list.btn_add, BindTool.Bind(self.OnClickNumChange, self, 1))
	XUI.AddClickEventListener(self.node_list.btn_sub, BindTool.Bind(self.OnClickNumChange, self, -1))
	XUI.AddClickEventListener(self.node_list.btn_cancel, BindTool.Bind(self.OnClickCanCelBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_sure, BindTool.Bind(self.OnClickSureBtn, self))
	self.node_list.buy_slider.slider.onValueChanged:AddListener(BindTool.Bind(self.OnSliderValueChange, self))
end

function ActSliderNumView:RefreshView(cur_num)
	self.item_cell:SetData(self.item_data)
	self.node_list.title_view_name.text.text = self.view_title_str
	self.node_list.buy_slider.slider.maxValue = self.limit_max_num
	self.node_list.buy_slider.slider.minValue = self.limit_min_num
	self.node_list.buy_slider.slider.value = cur_num or 1
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.item_data.item_id)
	self.node_list.cell_name.text.text = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
end

function ActSliderNumView:OnClickNumChange(num)
	local value = self.save_slider_value + num
	if value >= self.limit_min_num and value <= self.limit_max_num then
		self.node_list.buy_slider.slider.value = value
	end
end

function ActSliderNumView:OnSliderValueChange(value)
	self.save_slider_value = value
	self.node_list.lbl_num.text.text = value
end

function ActSliderNumView:OnClickCanCelBtn()
	self:Close()
end

function ActSliderNumView:OnClickSureBtn()
	if self.sure_call_back then
		self.sure_call_back(self.save_slider_value)
	end
	self:Close()
end