function ZhanLingView:InitTaskPanel()
    self.old_pregress_slider_value = nil
    self.select_task_index = 1
    self.zl_task_list = AsyncListView.New(ZhanLingTaskRender, self.node_list["task_list"])

    self.old_task_show_level = 0
    if not self.t_model_display then
        local item_bundle = "uis/view/zhanling_ui_prefab"
        local item_asset = "zhanling_display_item"
        self.t_model_display = OperationActRender.New(self.node_list.t_model_root, item_bundle, item_asset)
        local offset_setting = MODEL_OFFSET_TYPE.NORMALIZE
        self.t_model_display:SetModelType(MODEL_CAMERA_TYPE.TIPS, offset_setting)
    end
    
    --XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuyHighZhanling, self))
    XUI.AddClickEventListener(self.node_list["t_btn_m_buy_exp"], BindTool.Bind(self.OnClickBuyExpBtn, self))
    self.node_list["task_toggle_1"].toggle:AddClickListener(BindTool.Bind(self.OnClickTaskToggle, self, ZhanLingTaskClass.Daily))
    self.node_list["task_toggle_2"].toggle:AddClickListener(BindTool.Bind(self.OnClickTaskToggle, self, ZhanLingTaskClass.Week))
    self:FlushTaskToggleTime()
end

function ZhanLingView:ReleaseTaskPanel()
    if CountDownManager.Instance:HasCountDown("zhanling_task_week_time") then
        CountDownManager.Instance:RemoveCountDown("zhanling_task_week_time")
    end

    if self.zl_task_list then
        self.zl_task_list:DeleteMe()
        self.zl_task_list = nil
    end

    if self.t_model_display then
        self.t_model_display:DeleteMe()
        self.t_model_display = nil
    end

    if self.play_effect_timer then
        GlobalTimerQuest:CancelQuest(self.play_effect_timer)
        self.play_effect_timer = nil
    end
    self.select_task_index = nil
    self.t_old_cycle_title_img = nil
    ZhanLingWGCtrl.Instance:ClearFlushTaskAlert()
end

function ZhanLingView:DefaultSelectTaskToggle()
    self.node_list["task_toggle_1"].toggle.isOn = true
    self:SetPregressSlider()
end

function ZhanLingView:FlushTaskPanel()
    local data = ZhanLingWGData.Instance
    local zhanling_info = data:GetZhanLingInfo()
    if IsEmptyTable(zhanling_info) then
        return
    end

    local need_act_value = data:GetUpNextLevelNeedActValue()

    self.node_list.t_tips_label.text.text = Language.ZhanLing.ActValueDesc
    -- 升级特效
    if ZhanLingWGData.Instance:GetZhanLingOldLevel() ~= zhanling_info.level then
        -- local node_root = {
        --     num_root = self.node_list.t_level_num,
        --     effect_kuoda = self.node_list.level_effect,
        --     effect_saoguang = self.node_list.level_effect1,
        --     effect_boom = self.node_list.level_effect5,
        -- }
        --self:PlayUpLevelTween(node_root)
        ZhanLingWGData.Instance:SetZhanLingOldLevel()
    else
        local new_str = zhanling_info.level >= 100 and string.format(Language.ZhanLing.LevelStr2, zhanling_info.level) or zhanling_info.level -- 优化显示
        self.node_list.t_level_num.text.text = string.format(Language.ZhanLing.LevelStr2, new_str)
    end

    -- toggle 红点
    self.node_list.task_d_remind:SetActive(data:GetZhanLingTaskRemind(ZhanLingTaskClass.Daily))
    self.node_list.task_w_remind:SetActive(data:GetZhanLingTaskRemind(ZhanLingTaskClass.Week))

    self:FlushTaskList()
    -- 模型显示的文本状态
    self:FlushTaskModel()
    if self.t_model_display then
		self.t_model_display:ActModelPlayLastAction()
	end
    -- local zhanling_info = ZhanLingWGData.Instance:GetZhanLingInfo()
    -- if IsEmptyTable(zhanling_info) then
    --     return
    -- end
    -- self.node_list["board_tips"]:SetActive(not zhanling_info.act_high)
    -- if zhanling_info.act_high then
    --     RectTransform.SetAnchoredPositionXY(self.node_list.task_list.rect, 0, -46)
    --     RectTransform.SetSizeDeltaXY(self.node_list.task_list.rect, 929, 482)
    -- else
    --     RectTransform.SetAnchoredPositionXY(self.node_list.task_list.rect, 0, -5)
    --     RectTransform.SetSizeDeltaXY(self.node_list.task_list.rect, 929, 400)
    -- end
end

function ZhanLingView:SetPregressSlider()
    local data = ZhanLingWGData.Instance
    if data == nil then
        return
    end

    local zhanling_info = data:GetZhanLingInfo()

    if IsEmptyTable(zhanling_info) then
        return
    end

    local need_act_value = data:GetUpNextLevelNeedActValue()
    if self.node_list.t_pregress_slider then
        self:FlushHuoYueDuPregressSlider(zhanling_info.act_value / need_act_value)
    end

    if self.node_list.t_pregress_label then
        local color = zhanling_info.act_value < need_act_value and COLOR3B.C8 or COLOR3B.C7
        self.node_list.t_pregress_label.text.text = string.format(Language.ZhanLing.ActValueDesc3, color, zhanling_info.act_value, need_act_value)
    end
end

function ZhanLingView:FlushHuoYueDuPregressSlider(new_value)
    if self.old_pregress_slider_value and self.old_pregress_slider_value ~= new_value then
        local pregress_slider = self.node_list.t_pregress_slider.slider
        local old_value = pregress_slider.value
        if new_value > old_value then
            local time = 0.8 * (new_value - old_value)
            pregress_slider:DOValue(new_value, time)
        else
            local time_1 = 0.8 * (1 - old_value)
            local time_2 = 0.8 * new_value
            local tween_1 = pregress_slider:DOValue(1, time_1)
            tween_1:OnComplete(function ()
                pregress_slider.value = 0
                pregress_slider:DOValue(new_value, time_2)
            end)
        end
    else
        self.node_list.t_pregress_slider.slider.value = new_value
    end

    self.old_pregress_slider_value = new_value
end
--[[
function ZhanLingView:PlaySecondTaskEffect()
    if self.play_effect_timer then
        GlobalTimerQuest:CancelQuest(self.play_effect_timer)
        self.play_effect_timer = nil
    end

    self.play_effect_timer = GlobalTimerQuest:AddDelayTimer(function ()
        local bundle_name, asset_name = ResPath.GetEffectUi("UI_zhanling_01")
        EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.level_effect.transform, 1)
    end, 1)

end

function ZhanLingView:PlayFirstTaskEffect()
    local old_level = ZhanLingWGData.Instance:GetZhanLingOldLevel()
    local new_level = ZhanLingWGData.Instance:GetZhanLingInfo().level
    if old_level ~= new_level then
        local bundle_name, asset_name = ResPath.GetEffectUi("UI_zhanling_02")
        EffectManager.Instance:PlaySingleAtTransform(bundle_name, asset_name, self.node_list.level_effect1.transform, 1)--, nil, nil, nil, BindTool.Bind(self.PlaySecondTaskEffect, self))
        self:PlaySecondTaskEffect()
        ZhanLingWGData.Instance:SetZhanLingOldLevel()
    end
end
--]]
function ZhanLingView:TaskFinishEffect()
    -- local pos = ZhanLingWGData.Instance:GetZhanLingItemPos()
    -- if pos ~= nil then
    --     local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_daoju_biankuang_liziguang)
    --     TipWGCtrl.Instance:ShowFlyEffectManager(GuideModuleName.ZhanLingView, bundle_name, asset_name, pos, self.node_list.handle,
    --     DG.Tweening.Ease.OutCubic, 0.7, BindTool.Bind(self.SetPregressSlider, self), nil, 12, 100, nil, true, true)
    -- end
    self:SetPregressSlider()
end

function ZhanLingView:FlushTaskList()
    local task_list = ZhanLingWGData.Instance:GetZhanLingTaskInfo(self.select_task_index)
    self.zl_task_list:SetDataList(task_list)
end

function ZhanLingView:FlushTaskModel()
    local zhanling_info = ZhanLingWGData.Instance:GetZhanLingInfo()
    if IsEmptyTable(zhanling_info) then
        return
    end

    local max_level = ZhanLingWGData.Instance:GetCurRewardMaxLevel()
    local level_cfg = ZhanLingWGData.Instance:GetRewardShowInfoByLevel(max_level)

    if self.t_model_display and level_cfg then
        if self.old_task_show_level ~= level_cfg.model_item_id then
            self.old_task_show_level = level_cfg.model_item_id

            local data = {}
            data.should_ani = true
            data.render_type = level_cfg.guding_mark - 1
            data.item_id = level_cfg.model_item_id
            data.model_click_func = function ()
                TipWGCtrl.Instance:OpenItem({item_id = level_cfg.model_item_id})
            end
            data.model_rt_type = ModelRTSCaleType.XS
            self.t_model_display:SetData(data)
            -- 模型战力
            local cap_value = ItemShowWGData.CalculateCapability(level_cfg.model_item_id, true)
            self.node_list.t_model_cap_value.text.text = cap_value
        end
    end

    -- 模型名称图片
    local act_view_show_cfg = ZhanLingWGData.Instance:GetActViewShowModelCfg()
    if act_view_show_cfg and act_view_show_cfg.cycle_title_img then
        if self.t_old_cycle_title_img ~= act_view_show_cfg.cycle_title_img then
            local bundle, asset = ResPath.GetRawImagesPNG(act_view_show_cfg.cycle_title_img)
            self.node_list.t_model_desc.raw_image:LoadSprite(bundle, asset) --.. model_cfg_id
            self.t_old_cycle_title_img = act_view_show_cfg.cycle_title_img
        end
    end
end

function ZhanLingView:OnClickTaskToggle(type, is_on)
    if self.select_task_index == type then
        return
    end
    self.select_task_index = type
    self:FlushTaskList()
end

function ZhanLingView:FlushTaskToggleTime()
    if CountDownManager.Instance:HasCountDown("zhanling_task_week_time") then
        CountDownManager.Instance:RemoveCountDown("zhanling_task_week_time")
    end

    local rest_time = TimeUtil.GetRestTimeInWeek()
    self:FlushTaskWeekTime(TimeUtil.FormatTimeLanguage2(rest_time))
    if rest_time > 0 then
        CountDownManager.Instance:AddCountDown("zhanling_task_week_time",
                                                BindTool.Bind(self.UpdateTaskWeekTime, self),
                                                nil, nil, rest_time, 1)
    end
end

function ZhanLingView:UpdateTaskWeekTime(elapse_time, total_time)
    local valid_time = total_time - elapse_time
	if valid_time > 0 then
        self:FlushTaskWeekTime(TimeUtil.FormatTimeLanguage2(valid_time))
	end
end

function ZhanLingView:FlushTaskWeekTime(time_str)
    if self.node_list.week_rest_n_time then
		self.node_list.week_rest_n_time.text.text = time_str
	end

    if self.node_list.week_rest_h_time then
        self.node_list.week_rest_h_time.text.text = time_str
    end
end

