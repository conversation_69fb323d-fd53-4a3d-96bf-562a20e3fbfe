-- J-极品装备.xls
local item_table={
[1]={item_id=10100,num=1,is_bind=1},
[2]={item_id=26200,num=1,is_bind=1},
[3]={item_id=90629,num=1,is_bind=1},
[4]={item_id=122,num=1,is_bind=1},
[5]={item_id=1122,num=1,is_bind=1},
[6]={item_id=2122,num=1,is_bind=1},
[7]={item_id=3122,num=1,is_bind=1},
[8]={item_id=4122,num=1,is_bind=1},
[9]={item_id=5122,num=1,is_bind=1},
[10]={item_id=7122,num=1,is_bind=1},
[11]={item_id=6126,num=1,is_bind=1},
}

return {
best_equipment_task_type={
{}
},

best_equipment_task_type_meta_table_map={
},
best_necklace={
{param=10100,show_prop={[0]=item_table[1]},},
{task_type=4,reward_silver=488,task_process=30,show_prop={[0]=item_table[2]},skip_panel="equipment#equipment_strength",task_desc="普通装备强化总等级 <per>(0/30)</per> 级",},
{task_type=2,reward_silver=588,task_process=10,show_prop={[0]=item_table[3]},skip_panel="jingjie",task_desc="修真达到【筑基】<per>(0/10)</per>",},
{task_type=3,reward_silver=688,task_process=7,show_prop={[0]=item_table[4],[1]=item_table[5],[2]=item_table[6],[3]=item_table[7],[4]=item_table[8],[5]=item_table[9],[6]=item_table[10]},skip_panel="boss#boss_world",task_desc="获得1套4阶以上红色装备 <per>(0/7)</per>",},
{task_type=6,reward_silver=788,skip_icon=6,skip_name="仙侣",skip_panel="marry#marry_jiehun",task_desc="完成结婚 <per>(0/1)</per>",},
{task_type=5,reward_silver=888,skip_icon=5,skip_name="贵族记忆",skip_panel="recharge#recharge_card",task_desc="激活贵族记忆 <per>(0/1)</per>",}
},

best_necklace_meta_table_map={
},
other={
{}
},

other_meta_table_map={
},
equip_show={
{},
{item_id=1122,},
{item_id=2122,},
{item_id=3122,},
{item_id=4122,}
},

equip_show_meta_table_map={
},
best_equipment_task_type_default_table={type=1,type_name="极品项链",final_reward=item_table[11],},

best_necklace_default_table={type=1,task_type=1,reward_silver=388,param=0,task_process=1,show_prop={},skip_icon="",skip_name="",skip_panel="shop#Tab_Shop11#uip=10100",task_desc="穿戴 <per>(0/1)</per> 只猪小戒（永久）",},

other_default_table={open_level=90,max_type=1,},

equip_show_default_table={item_id=122,star_level=1,}

}

