------------每日宝箱----------------

function GuildView:InitBaoXiangView()
	XUI.AddClickEventListener(self.node_list["reward_yulan_btn"], BindTool.Bind(self.ClickRewardYuLnagBtn,self))
	XUI.AddClickEventListener(self.node_list["record_btn"], BindTool.Bind(self.ClickRewardRecordBtn,self))
	XUI.AddClickEventListener(self.node_list["send_flush_btn"], BindTool.Bind(self.ClickSendFlushBtn,self))
	XUI.AddClickEventListener(self.node_list["open_baoxiang_btn"], BindTool.Bind(self.ClickOpenBaoXiangBtn,self))
	--XUI.AddClickEventListener(self.node_list["open_code_btn"], BindTool.Bind(self.ClickOpenCodeBtn,self))
	XUI.AddClickEventListener(self.node_list["baoxiang_tip_btn"], BindTool.Bind(self.ClickBaoXiangTipBtn,self))
	XUI.AddClickEventListener(self.node_list["btn_yijianchatbtn"], BindTool.Bind(self.ClickChatBtn,self))
	XUI.AddClickEventListener(self.node_list["mask_btn"], BindTool.Bind(self.ClickMask,self))
	XUI.AddClickEventListener(self.node_list["tujie_btn"], BindTool.Bind(self.ClickTuJie,self))

	self.guild_role_list = AsyncListView.New(GuildRoleItem,self.node_list["guild_role_list"])

	-- self.flush_msg_list = AsyncListView.New(FlushMsgItem,self.node_list["flush_msg_list"])
	-- self.flush_msg_list:SetCellSizeDel(BindTool.Bind(self.CellSizeDel, self))

	
	self.open_reward_list = AsyncListView.New(ItemCell, self.node_list["open_reward_list"])
	

	local other_cfg = GuildBaoXiangWGData.Instance:GetOtherCfg()

	local cfg_list = GuildBaoXiangWGData.Instance:GetTreasureCfg()

	local model_res_name = "3_1_lan"--模型加载名字配置
	local m_bundle, m_asset = ResPath.GetOtherUIModelByName(model_res_name)
	self.play_bx_animing = false
	-- 拖拽球列表
	self.qiu_btn_list = {}
	local qiu_list_data = GuildBaoXiangWGData.Instance:GetQiuItemData()
	for i = 1, 4 do
		self.qiu_btn_list[i] = BaoXiangQiuItem.New(self.node_list["qiu_" .. i])
		self.qiu_btn_list[i]:SetIndex(i)
		self.qiu_btn_list[i]:SetData(qiu_list_data[i])
		self.node_list["qiu_" .. i].event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnChangeOrderUp, self, i))
		self.node_list["qiu_" .. i].event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDragChangeOrder, self, i))
		self.node_list["qiu_" .. i].event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnClickChangeOrder, self, i))
		self.node_list["qiu_" .. i].event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.CheckChangeOrderState, self, i))
	end
end

function GuildView:BaoXiangShowIndex()
	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_ALLY_ITEM_INFO)
	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_REQ_RECORD)
	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_REQ_INFO)
	-- GuildBaoXiangWGData.Instance:SetBaoXiangRemind(1)
end

function GuildView:DeleteBaoXiangView()

	if self.guild_role_list then
		self.guild_role_list:DeleteMe()
		self.guild_role_list = nil
	end

	-- if self.flush_msg_list then
	-- 	self.flush_msg_list:DeleteMe()
	-- 	self.flush_msg_list = nil
	-- end

	if self.open_reward_list then
		self.open_reward_list:DeleteMe()
		self.open_reward_list = nil
	end

	if self.baoxiang_sequence then
		self.baoxiang_sequence:Kill()
		self.baoxiang_sequence = nil
	end

	if CountDownManager.Instance:HasCountDown("start_send_flush_count_down") then
		CountDownManager.Instance:RemoveCountDown("start_send_flush_count_down")
	end

	if CountDownManager.Instance:HasCountDown("start_send_chat_flush_count_down") then
		CountDownManager.Instance:RemoveCountDown("start_send_chat_flush_count_down")
	end

	self.play_bx_animing = false

	if self.qiu_btn_list then
		for k, v in pairs(self.qiu_btn_list) do
			v:DeleteMe()
		end
		self.qiu_btn_list = nil
	end

	-- if self.circle_tween_list then
	-- 	for k, v in pairs(self.circle_tween_list) do
	-- 		v:Kill(true)
	-- 		v = nil
	-- 	end
	-- 	self.circle_tween_list = nil
	-- end
end

function GuildView:ShowIndexBaoXiang()

end

function GuildView:FlushBaoXiangView()
	local is_open = GuildBaoXiangWGData.Instance:GetHasOpenTreasure()
	self.node_list["open_baoxiang"]:SetActive(true)
	self:FlushOpenBaoXiang()
	self:StartSendFlushCountDown()
	self:StartSendFlushChatCountDown()

end

function GuildView:FlushOpenBaoXiang()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local quality = info.quality
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
	if IsEmptyTable(treasure_cfg) then
		return
	end

    local bundle, asset = ResPath.GetGuildSystemImage("a2_xm_icon_" .. info.quality)
    self.node_list.top_item_icon.image:LoadSprite(bundle, asset)
    local bg_index = 0
	if info.quality == 4 then
		bg_index = 7
	else
		bg_index = info.quality
	end
	local bundle2, asset2 = ResPath.GetCommon("a3_ty_wpk_" .. bg_index)
    self.node_list.top_item_bg.image:LoadSprite(bundle2, asset2, function()
        self.node_list.top_item_bg.image:SetNativeSize()
    end)

	

	local code_num = treasure_cfg.password_bit
	local password_bit = info.password_bit
	local password_list = info.password_list
	local cur_password = password_list[password_bit]
	local eff_index = quality - 1
	local code_str = ""
	local open_tag = info.open_tag
	if info.open_tag == 1 then
		for i,v in ipairs(password_list) do
			code_str = code_str .. v 
		end
	else
		local input_code = GuildBaoXiangWGData.Instance:GetInputCode()
		local code_str_list = {}
		if input_code == nil then
			for i = 1,code_num do
				if i == password_bit then
					code_str = code_str .. cur_password 
					code_str_list[i] = cur_password
				else
					code_str = code_str .. "?"
					code_str_list[i] = "?"
				end
			end
		else
			code_str = input_code
		end
	end
	local code_list = {}

	for i = 1, code_num do 
		code_list[i] = string.sub(code_str, i, i)
	end

	for i = 1, code_num do 
		if self.node_list["code_icon_" .. i] then
			if code_list[i] == "?" then
				self.node_list["code_icon_" .. i]:SetActive(false)
			else
				self.node_list["code_icon_" .. i]:SetActive(true)
				self.node_list["code_icon_" .. i].image:LoadSprite(ResPath.GetGuildSystemImage("a2_icon_qiu_".. code_list[i]))
			end
		end
	end

	local item_data = {}
	for i = 0, #treasure_cfg.reward_item do
		table.insert(item_data,treasure_cfg.reward_item[i])
	end
	self.open_reward_list:SetDataList(item_data)
	-- local tab = #item_data
	-- local index = 0
	-- for i = 1, tab do
	-- 	if self.open_reward_list[i] == nil then
	-- 		self.open_reward_list[i] = ItemCell.New(self.node_list.open_reward_list)
	-- 	end
	-- 	self.open_reward_list[i]:SetActive(true)
	-- 	self.open_reward_list[i]:SetData(item_data[i])
	-- 	index = index + 1
	-- end

	-- for i = index + 1, #self.open_reward_list do
	-- 	if self.open_reward_list[i] then
	-- 		self.open_reward_list[i]:SetActive(false)
	-- 	end
	-- end
	local konw_str = string.format(Language.BiZuoBaoXiang.KonwCodeStr,password_bit,cur_password)
	self.node_list["cur_konw_code"].text.text = konw_str

	local role_vo = RoleWGData.Instance:GetRoleVo()
	local guild_id = role_vo.guild_id
	if guild_id > 0 then
		local all_item_list = GuildBaoXiangWGData.Instance:GetShowDailyAllItemInfoList()
		self.guild_role_list:SetDataList(all_item_list)
		self.node_list["not_guild_tip"]:SetActive(IsEmptyTable(all_item_list))
		self.node_list["btn_yijianchatbtn"]:SetActive( not IsEmptyTable(all_item_list))
	end
	--local record_info_list = GuildBaoXiangWGData.Instance:GetDailyTreasureRecordInfoList()
	--self.flush_msg_list:SetDataList(record_info_list)
	--self.node_list["not_flush_msg_tip"]:SetActive(IsEmptyTable(record_info_list))

	self.node_list.mask_btn:SetActive(open_tag == 1)
	if open_tag == 1 then
		XUI.SetButtonEnabled(self.node_list["open_baoxiang_btn"], false)
		self.node_list.baoxiang_btn_text.text.text = Language.BiZuoBaoXiang.BaoXiangBtnStr2
		-- self.circle_tween_list = {}
		-- for i = 1, code_num do 
		-- 	--self.node_list["code_hl_" .. i]:SetActive(true)
		-- 	-- if not self.circle_tween_list[i] then
		-- 	-- 	--self.circle_tween_list[i] = self.node_list["code_hl_" .. i].transform:DORotate(u3dpool.vec3(0, 0, 360), 5, DG.Tweening.RotateMode.FastBeyond360)		
		-- 	-- 	self.circle_tween_list[i]:SetEase(DG.Tweening.Ease.Linear)
	    -- 	-- 	self.circle_tween_list[i]:SetLoops(-1)
		-- 	-- end
		-- end
	else
		XUI.SetButtonEnabled(self.node_list["open_baoxiang_btn"], true)
		self.node_list.baoxiang_btn_text.text.text = Language.BiZuoBaoXiang.BaoXiangBtnStr1
		-- for i = 1, code_num do 
		-- 	--self.node_list["code_hl_" .. i]:SetActive(password_bit == i)
		-- end
	end
end

function GuildView:PlayOpenBXEffect()
	TryDelayCall(self, function ()
		ViewManager.Instance:Open(GuideModuleName.BaoXiangGetRewardView)
	end, 0, "open_baoxiang_get_reward_delay")
end

function GuildView:ClickRewardYuLnagBtn()
	ViewManager.Instance:Open(GuideModuleName.BaoXiangRewardYuLianView)
end

function GuildView:ClickRewardRecordBtn()
	GuildWGCtrl.Instance:OpenLingMaiRewardView()
end


function GuildView:ClickTuJie()
	GuildWGCtrl.Instance:OpenBaoXiangTuJieView()
end

function GuildView:ClickSendFlushBtn()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local password = info.password
	local quality = info.quality
	local open_tag = info.open_tag
	local password_bit = info.password_bit
	local password_list = info.password_list
	local bx_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
	local common_color = bx_cfg and bx_cfg.common_color or 1
	local name = bx_cfg and bx_cfg.name or ""
	local virtual_id = bx_cfg and bx_cfg.virtual_id or 91146
	local item_cfg = ItemWGData.Instance:GetItemConfig(virtual_id)
	local str = ""
	if open_tag == 1 then
		local rand_num = math.random(1,4)
		local code_str = ""
		for i,v in ipairs(password_list) do
			code_str = code_str .. Language.BiZuoBaoXiang.CodeStr[v] .. " "
		end
		local tall_code_str = Language.BiZuoBaoXiang.BaoXiangOpenChatMsgs[rand_num]
		str = string.format(tall_code_str,code_str)
		if ChatWGData.ExamineEditText(str, 2) then
			ViewManager.Instance:Open(GuideModuleName.ChatView, 60, nil ,{open_param = str})
		end
	else
		local role_id = RoleWGData.Instance:GetMainRoleId()
		local code_str = Language.BiZuoBaoXiang.CodeStr[password_list[password_bit]]
		str = string.format(Language.BiZuoBaoXiang.BaoXiangChatMsg2, Language.BiZuoBaoXiang.NormalStr[password_bit], code_str, CHAT_LINK_TYPE.BAOXIANG_CODE)
		if ChatWGData.ExamineEditText(str, 2) then
			ViewManager.Instance:Open(GuideModuleName.ChatView, 60, nil ,{guild_baoxiang_param = {text = str,item_id = virtual_id,openLink = CHAT_LINK_TYPE.BAOXIANG_CODE,quality = quality,role_id = role_id}})
		end
	end
	-- ViewManager.Instance:Open(GuideModuleName.ChatView, 60)
	-- ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.GUILD, str, CHAT_CONTENT_TYPE.TEXT)
	self:SetSendFlushCountDown()
	
end

function GuildView:SetSendFlushCountDown()
	--仙盟求助按钮玩家操作过后 给一个30s的cd
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	GuildBaoXiangWGData.Instance:SetSendFlushBtnTime(server_time + 30)
	self:StartSendFlushCountDown()
end

function GuildView:StartSendFlushCountDown()
	local time = GuildBaoXiangWGData.Instance:GetSendFlushBtnTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if time < server_time then
		XUI.SetButtonEnabled(self.node_list["send_flush_btn"], true)
		local btn_text = Language.BiZuoBaoXiang.SendFlushBtnText
		local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
		if not IsEmptyTable(info) then
			local unlock_tag = info.unlock_tag
			if unlock_tag == 1 then
				btn_text = Language.BiZuoBaoXiang.SendSecretCodeShareText
			end
		end
		self.node_list["send_flush_btn_text"].text.text = btn_text
		return
	end
	if CountDownManager.Instance:HasCountDown("start_send_flush_count_down") then
		CountDownManager.Instance:RemoveCountDown("start_send_flush_count_down")
	end
	XUI.SetButtonEnabled(self.node_list["send_flush_btn"], false)
	self:SendFlushRefreshTime(server_time, time)
	if time > 0 then
		CountDownManager.Instance:AddCountDown("start_send_flush_count_down", BindTool.Bind1(self.SendFlushRefreshTime, self), BindTool.Bind1(self.SendFlushCompleteCallBack, self), time, nil,1)
	end
end

function GuildView:SendFlushRefreshTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	self.node_list["send_flush_btn_text"].text.text = string.format(Language.BiZuoBaoXiang.BuZhenSecond, time)
end

function GuildView:SendFlushCompleteCallBack()
	XUI.SetButtonEnabled(self.node_list["send_flush_btn"], true)
	local btn_text = Language.BiZuoBaoXiang.SendFlushBtnText
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if not IsEmptyTable(info) then
		local unlock_tag = info.unlock_tag
		if unlock_tag == 1 then
			btn_text = Language.BiZuoBaoXiang.SendSecretCodeShareText
		end
	end
	self.node_list["send_flush_btn_text"].text.text = btn_text
end

function GuildView:ClickOpenBaoXiangBtn()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end 
	local password = info.password
	local unlock_tag = info.unlock_tag
	local quality = info.quality
	local input_code = GuildBaoXiangWGData.Instance:GetInputCode()
	if input_code then
		input_code = tonumber(input_code)
		if input_code == password then
			local ok_fun = function ()
				GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_ENTER_PASSWARD,input_code)
				GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_OPEN_TREASURE)
				self:PlayOpenBXEffect()
			end
			
			local max_quality = GuildBaoXiangWGData.Instance:GetMaxQuality()
			if quality < max_quality then
				TipWGCtrl.Instance:OpenAlertTips(Language.BiZuoBaoXiang.OpenBaoXiangTips,ok_fun)
			else
				ok_fun()
			end
			return
		end
	end
	if unlock_tag == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.CodeError)
		return
	end
	if unlock_tag == 1 then
		local ok_fun = function ()
			self:PlayOpenBXEffect()
			GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_OPEN_TREASURE)
		end
		
		local max_quality = GuildBaoXiangWGData.Instance:GetMaxQuality()
		if quality < max_quality then
			TipWGCtrl.Instance:OpenAlertTips(Language.BiZuoBaoXiang.OpenBaoXiangTips,ok_fun)
		else
			ok_fun()
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.CodeError)
	end
end

function GuildView:ClickOpenCodeBtn()
	local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	if IsEmptyTable(info) then return end
	local unlock_tag = info.unlock_tag
	if unlock_tag == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.IsOpenBaoXiang)
	else
		ViewManager.Instance:Open(GuideModuleName.BaoXiangCodeView)
	end
end


function GuildView:ClickBaoXiangTipBtn()
	RuleTip.Instance:SetContent(Language.BiZuoBaoXiang.BaoXiangTipsRule, Language.BiZuoBaoXiang.BaoXiangTips, nil, nil, true)
end

function GuildView:ClickChatBtn()
	local all_item_list = GuildBaoXiangWGData.Instance:GetShowDailyAllItemInfoList()
	local sendsuc = false
	if not all_item_list and IsEmptyTable(all_item_list) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.NoPlayer)
		return 
	end

	local self_info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	local quality = self_info and self_info.quality or 0
	local max = GuildBaoXiangWGData.Instance:GetMaxQuality()
	if quality >= max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.MaxQuality)
		return
	end
	local open_tag = self_info.open_tag
	if open_tag == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.BaoXiangHasOpen)
		return
	end
	for i,v in ipairs(all_item_list) do
		local info = v.info
		 -- 宝箱已开或者已经请求或者已经帮忙刷新宝箱或者刷新次数不够都不再求助
		local has_id = GuildBaoXiangWGData.Instance:GetReqHelpRefreshUidListId(info.uid)
		if has_id then
			break
		end

		local other_cfg = GuildBaoXiangWGData.Instance:GetOtherCfg()
		local help_refresh_times = info.help_refresh_times
		local has_num = other_cfg.help_refresh_times - help_refresh_times
		if has_num <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.NoPlayer)
			break 
		end
		sendsuc = true
		GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH_REQ,info.uid)
	end
	if sendsuc then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.YiQingQiu)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.NoPlayer)
	end
	self:SetSendFlushChatCountDown()
end

function GuildView:ClickMask()
	SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.IsOpenBaoXiang)
end

function GuildView:SetSendFlushChatCountDown()
	--仙盟求助按钮玩家操作过后 给一个10的cd
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	GuildBaoXiangWGData.Instance:SetSendFlushChatBtnTime(server_time + 10)
	self:StartSendFlushChatCountDown()
end

function GuildView:StartSendFlushChatCountDown()
	local time = GuildBaoXiangWGData.Instance:GetSendFlushChatBtnTime()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if time < server_time then
		XUI.SetButtonEnabled(self.node_list["btn_yijianchatbtn"], true)
		-- self.node_list.yijian_text.text.text = Language.BiZuoBaoXiang.SendFlushChatText
		return
	end
	if CountDownManager.Instance:HasCountDown("start_send_chat_flush_count_down") then
		CountDownManager.Instance:RemoveCountDown("start_send_chat_flush_count_down")
	end
	XUI.SetButtonEnabled(self.node_list["btn_yijianchatbtn"], false)
	self:SendFlushChatTime(server_time,time)
	CountDownManager.Instance:AddCountDown("start_send_chat_flush_count_down", BindTool.Bind1(self.SendFlushChatTime, self), BindTool.Bind1(self.SendFlushChatCompleteCallBack, self), time, nil,1)
end

function GuildView:SendFlushChatTime(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	-- if self.node_list["yijian_text"] then
	-- 	self.node_list["yijian_text"].text.text = time .. "s"
	-- end
end

function GuildView:SendFlushChatCompleteCallBack()
	XUI.SetButtonEnabled(self.node_list["btn_yijianchatbtn"], true)
	--self.node_list.yijian_text.text.text = Language.BiZuoBaoXiang.SendFlushChatText
end


-- function GuildView:CellSizeDel(data_index)
-- 	local hight = 40
-- 	local spece = 18
-- 	data_index = data_index + 1
-- 	--local list = self.flush_msg_list:GetDataList()
-- 	local data = list[data_index]
-- 	if not data then 
-- 		return hight
-- 	end
-- 	local time_str = ""
-- 	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(data.new_quality)
-- 	local msg_str = ""
-- 	if data.is_refuse == 1 then
-- 		msg_str = string.format(Language.BiZuoBaoXiang.RoleRefuseMsg,data.name)
-- 	else
-- 		if data.new_quality == data.old_quality then
-- 			msg_str = string.format(Language.BiZuoBaoXiang.FlushRecordStrNotChange,time_str,data.name)
-- 		else
-- 			local baoxiang_name = treasure_cfg and treasure_cfg.name or ""
-- 			local color = treasure_cfg and treasure_cfg.common_color or 2
-- 			baoxiang_name = ToColorStr(baoxiang_name,ITEM_COLOR[color])
-- 			msg_str = string.format(Language.BiZuoBaoXiang.FlushRecordStr,time_str,data.name,baoxiang_name)
-- 		end
-- 	end
--     self.node_list.TestText.text.text = msg_str
--     UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list["TestText"].rect)
-- 	hight = math.ceil(self.node_list["TestText"].rect.rect.height) <= 40 and 40 or math.ceil(self.node_list["TestText"].rect.rect.height) + spece
-- 	return hight
-- end

-- 当按下图标
function GuildView:OnClickChangeOrder(index)
	self.click_qiu_order_index = index
	self.qiu_drag_finally_pos = nil
	self.node_list.change_order_qiu:SetActive(false)
	self.node_list.change_order_icon:SetActive(false)
end

-- 当拖拽图标
function GuildView:OnDragChangeOrder(index, eventData)  --持续更新
	if self.doing_order_change then
		local x = eventData.position.x
		local y = eventData.position.y
		local _, position = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list.qiu_list.transform,
															eventData.position, UICamera, Vector2(0, 0))
		self.node_list.change_order_qiu.transform.anchoredPosition = position
		self.qiu_drag_finally_pos = position
	end
end

-- 当松开图标
function GuildView:OnChangeOrderUp(index) --结束拖动
	if self.doing_order_change then
		if self.click_qiu_order_index and self.qiu_drag_finally_pos then
			local code_index = self:CacularDragCodeIndex(self.qiu_drag_finally_pos)
			local info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
			local quality = info.quality
			local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(quality)
			local code_num = treasure_cfg.password_bit
			local password_bit = info.password_bit
			local password_list = info.password_list
			local cur_password = password_list[password_bit]
			if code_index ~= -1 and  password_bit ~= code_index then
				local input_code = GuildBaoXiangWGData.Instance:GetInputCode()
				local code_str = ""
				if input_code == nil then
					for i = 1, code_num do
						if i == password_bit then
							code_str = code_str .. cur_password 
						elseif i == code_index then
							code_str = code_str .. self.click_qiu_order_index
						else
							code_str = code_str .. "?"
						end
					end
				else
					local code_list  = {}
					for i = 1, code_num do 
						if i == code_index then
							code_list[i] = self.click_qiu_order_index
						else
							code_list[i] = string.sub(input_code, i, i)
						end
					end
					code_str = table.concat(code_list)
				end
				GuildBaoXiangWGData.Instance:SetInputCode(code_str)
				self:FlushBaoXiangView()
			end
		end
	end

	self.node_list.change_order_qiu:SetActive(false)
	self.node_list.change_order_icon:SetActive(false)
	self.qiu_drag_finally_pos = nil
	self.click_qiu_order_index = nil
	self.doing_order_change = false
end

-- 拖动离开，开始显示拖动icon
function GuildView:CheckChangeOrderState(index)
	if self.click_qiu_order_index and self.click_qiu_order_index == index then 
		local qiu_list_data = GuildBaoXiangWGData.Instance:GetQiuItemData()
		if qiu_list_data[index] then
			self.node_list.change_order_qiu:SetActive(true)
			self.node_list.change_order_icon:SetActive(true)
			local bundle, asset = ResPath.GetGuildSystemImage("a2_icon_qiu_" .. qiu_list_data[index].num)
		    self.node_list.change_order_icon.image:LoadSprite(bundle, asset, function()
		        self.node_list.change_order_icon.image:SetNativeSize()
		    end) 
		    self.doing_order_change = true
		else
			self.node_list.change_order_qiu:SetActive(false)
			self.node_list.change_order_icon:SetActive(false)
			self.doing_order_change = false
		end
	end
end

function GuildView:CacularDragCodeIndex(position)
	local range = 80
	local x_value = 0
	local y_value = 0
	for i=1, 5 do
		x_value = self.node_list["code" .. i].rect.anchoredPosition.x
		y_value = self.node_list["code" .. i].rect.anchoredPosition.y
		if math.abs(x_value - position.x) <= range and math.abs(y_value - position.y) <= range then
			return i
		end
	end
	return -1
end

------------------BaoXiangRewardItem----------------------

BaoXiangRewardItem = BaoXiangRewardItem or BaseClass(BaseRender)

function BaoXiangRewardItem:__init()
	self.reward_list = AsyncListView.New(ItemCell,self.node_list["reward_list"])
end

function BaoXiangRewardItem:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function BaoXiangRewardItem:OnFlush()
	if not self.data then return end
	local bundle, asset = ResPath.GetGuildSystemImage("a2_xm_icon_" .. self.data.quality)
    self.node_list.quality_icon.image:LoadSprite(bundle, asset)
	if self.data.quality == 1 then
		self.node_list["quality_bg"]:SetActive(false)
	else
		self.node_list["quality_bg"]:SetActive(true)
		local bundle2, asset2 = ResPath.GetCommonImages("a2_xm_di_dj" .. self.index)
		self.node_list["quality_bg"].image:LoadSprite(bundle2, asset2)
	end
	self.node_list["bg"]:SetActive(self.index % 2 == 1)
	local item_data = {}
	for i = 0, #self.data.reward_item do
		table.insert(item_data,self.data.reward_item[i])
	end
	self.reward_list:SetDataList(item_data)
end

------------------GuildRoleItem----------------------

GuildRoleItem = GuildRoleItem or BaseClass(BaseRender)

function GuildRoleItem:__init()
	XUI.AddClickEventListener(self.node_list["chat_btn"],BindTool.Bind(self.ClickChatBtn,self))
	XUI.AddClickEventListener(self.node_list["role_btn"],BindTool.Bind(self.ClickRoleBtn,self))
end

function GuildRoleItem:__delete()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
end

function GuildRoleItem:OnFlush()
	if not self.data then return end
	local info = self.data.info
	self.node_list["name_text"].text.text = info.name
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(info.quality)
	local fortune_cfg = QifuYunShiWGData.Instance:GetFortuneTypeCfg(info.fortune_type)
	local color = fortune_cfg and fortune_cfg.common_color or 2
	-- local fortune_name = fortune_cfg and fortune_cfg.name or ""
	-- fortune_name = ToColorStr(fortune_name,ITEM_COLOR[color])
	-- self.node_list["baoxiang_text"].text.text = fortune_name
	local other_cfg = GuildBaoXiangWGData.Instance:GetOtherCfg()
	local help_refresh_times = info.help_refresh_times
	local has_num = other_cfg.help_refresh_times - help_refresh_times
	local color = has_num > 0 and COLOR3B.WHITE or COLOR3B.RED
	self.node_list["num_text"].text.text = ToColorStr(has_num .. "/" .. other_cfg.help_refresh_times,color)
	-- self.node_list["code_text"].text.text = string.format(Language.BiZuoBaoXiang.CodeNumStr,info.password_bit)	

	local has_id = GuildBaoXiangWGData.Instance:GetReqHelpRefreshUidListId(info.uid)

	local has_help = GuildBaoXiangWGData.Instance:GetMyBeHelpUidListByUid(info.uid)
	self.node_list["yiqiuzhu"]:SetActive(has_id ~= nil and has_num > 0 and not has_help)
	self.node_list["chat_btn"]:SetActive(has_id == nil and has_num > 0 and not has_help)
	self.node_list["has_refresh"]:SetActive(has_help)
	self.node_list["not_num"]:SetActive(has_num <= 0 and not has_help)
end

function GuildRoleItem:ClickChatBtn()
	if not self.data then return end
	local info = self.data.info
	local self_info = GuildBaoXiangWGData.Instance:GetDailyTreasureInfo()
	local quality = self_info and self_info.quality or 0
	local max = GuildBaoXiangWGData.Instance:GetMaxQuality()
	if quality >= max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.MaxQuality)
		return
	end

	local open_tag = self_info.open_tag
	if open_tag == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.BaoXiangHasOpen)
		return
	end

	GuildWGCtrl.Instance:SendCSDailyTreasureOperate(DAILY_TREASURE_OPERATE_TYPE.DAILY_TREASURE_OPERATE_TYPE_HELP_REFRESH_REQ,info.uid)
	SysMsgWGCtrl.Instance:ErrorRemind(Language.BiZuoBaoXiang.YiQingQiu)
	self.node_list["yiqiuzhu"]:SetActive(true)
	self.node_list["chat_btn"]:SetActive(false)

end

function GuildRoleItem:ClickRoleBtn()
	if not self.data then return end
	local info = self.data.info
	BrowseWGCtrl.Instance:ShowOtherRoleInfo(info.uid)
end

------------------FlushMsgItem----------------------

FlushMsgItem = FlushMsgItem or BaseClass(BaseRender)

function FlushMsgItem:__init()
end

function FlushMsgItem:__delete()

end

function FlushMsgItem:OnFlush()
	if not self.data then return end
	local time_str = ""
	local treasure_cfg = GuildBaoXiangWGData.Instance:GetTreasureCfgByQuality(self.data.new_quality)
	local msg_str = ""
	if self.data.is_refuse == 1 then
		msg_str = string.format(Language.BiZuoBaoXiang.RoleRefuseMsg,self.data.name)
	else
		if self.data.new_quality == self.data.old_quality then
			msg_str = string.format(Language.BiZuoBaoXiang.FlushRecordStrNotChange,time_str,self.data.name)
		else
			local baoxiang_name = treasure_cfg and treasure_cfg.name or ""
			local color = treasure_cfg and treasure_cfg.common_color or 2
			baoxiang_name = ToColorStr(baoxiang_name,ITEM_COLOR[color])
			msg_str = string.format(Language.BiZuoBaoXiang.FlushRecordStr,time_str,self.data.name,baoxiang_name)
		end
	end
	
	self.node_list["text"].text.text = msg_str
	self.node_list["bg"]:SetActive(self.index % 2 == 1)
end


-------------------BaoXiangShowCodeList-------------------
local NumData = {[1] = 0,[2] = 1,[3] = 2,[4] = 3,[5] = 4,[6] = 5,[7] = 6,[8] = 7,[9] = 8,[10] = 9}

BaoXiangShowCodeList = BaoXiangShowCodeList or BaseClass(BaseRender)

function BaoXiangShowCodeList:__init()
	self.list_view = AsyncListView.New(RandomCode,self.node_list["list_view"])
end

function BaoXiangShowCodeList:__delete()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
end

function BaoXiangShowCodeList:OnFlush()
	if self.data then
		local num_data = self.data.num_data
		local percent = self.data.percent
		if not IsEmptyTable(num_data) then
			self.list_view:SetDataList(num_data)
		else
			self.list_view:SetDataList(NumData)
		end
		if percent then
			self.list_view:JumptToPrecent(percent)
		end
	else
		self.list_view:SetDataList(NumData)
	end

end

-------------BaoXiangQiuItem------------  

BaoXiangQiuItem = BaoXiangQiuItem or BaseClass(BaseRender)

function BaoXiangQiuItem:__init()
	
end

function BaoXiangQiuItem:__delete()

end

function BaoXiangQiuItem:OnFlush()
	if self.data then	
		local bundle, asset = ResPath.GetGuildSystemImage("a2_icon_qiu_" .. self.data.num)
	    self.node_list.icon.image:LoadSprite(bundle, asset, function()
	        self.node_list.icon.image:SetNativeSize()
	    end) 
	end
end