MarryXunYouView = MarryXunYouView or BaseClass(SafeBaseView)

local BUY_REDPACK = 1 	--购买红包
local BUY_FLOSER = 2 	--购买鲜花

function MarryXunYouView:__init()
	
	self.view_layer = UiLayer.FloatText
	self:LoadConfig()

	self.is_concise_state = true
end

function MarryXunYouView:__delete()
	
end

function MarryXunYouView:ReleaseCallBack()
	if self.alert_flower then
		self.alert_flower:DeleteMe()
		self.alert_flower = nil
	end

	if self.alert_red_pack then
		self.alert_red_pack:DeleteMe()
		self.alert_red_pack = nil
	end
	self.hide_view = true
	if self.tween then
		self.tween:Kill()
		self.tween = nil
	end
end

function MarryXunYouView:LoadConfig()
	self:AddViewResource(0, "uis/view/wedding_ui_prefab", "layout_xun_you_view")
end

function MarryXunYouView:LoadCallBack()
	self.is_concise_state = true
	self.hide_view = true
	XUI.AddClickEventListener(self.node_list["btn_wedding"], BindTool.Bind2(self.OnBtnSprinkleRedPack, self))   --撒红包
	XUI.AddClickEventListener(self.node_list["btn_guests_msg"], BindTool.Bind2(self.OnBtnSprinkleFlower, self)) --送花
	XUI.AddClickEventListener(self.node_list["btn_hide"], BindTool.Bind(self.OnBtnHideView, self)) --隐藏/显示
	XUI.AddClickEventListener(self.node_list["btn_concise"], BindTool.Bind2(self.OnBtnConcise, self)) --极简模式

	self.tween = self.node_list["btn_hide"].transform:DORotate(
			Vector3(0, 0, 90),
			0.15,
			DG.Tweening.RotateMode.FastBeyond360)
	self.tween:SetAutoKill(false)
	self.tween:Pause()

	self:UpdateConciseBtn(self.is_concise_state)
end

function MarryXunYouView:CloseCallBack()
	-- 避免因为某些原因这个界面意外被关闭，而导致界面无法恢复
	self.is_concise_state = false
	MarryWGCtrl.Instance:SetXunYouMainUiState(self.is_concise_state)
end

function MarryXunYouView:OnBtnHideView()
	self.hide_view = not self.hide_view
	self.node_list["image_bg"]:SetActive(self.hide_view)
	self.node_list["hide_content"]:SetActive(self.hide_view)
	if self.hide_view then
		self.tween:PlayBackwards()
	else
		self.tween:PlayForward()
	end
end

function MarryXunYouView:OnFlush()
local red_pack_count, flower_count = MarryWGData.Instance:GetSprinkleRedPackCount()
	self:SetCurRedPackCount(red_pack_count)		--剩余红包数量
	self:SetCurFlowerCount(flower_count)			--剩余鲜花数量
end

--撒红包
function MarryXunYouView:OnBtnSprinkleRedPack()
	local red_pack_count, flower_count = MarryWGData.Instance:GetSprinkleRedPackCount()
	if red_pack_count <= 0 then
		self:OpenBuyAlert(BUY_REDPACK)
	else
		self:SendRedPackSeq(0)
	end
end

--送花
function MarryXunYouView:OnBtnSprinkleFlower()
	-- local red_pack_count, flower_count = MarryWGData.Instance:GetSprinkleRedPackCount()
	-- if flower_count <= 0 then
	-- 	self:OpenBuyAlert(BUY_FLOSER)
	-- else
		local main_vo = GameVoManager.Instance:GetMainRoleVo()
		local role_sex = RoleWGData.Instance:GetRoleSex()
		local lover_sex = role_sex == GameEnum.MALE and  GameEnum.FEMALE or GameEnum.MALE
        -- FlowerWGCtrl.Instance:SendGiveFlower(0, 0, main_vo.lover_uid, 1, 1)
        if main_vo.lover_uid > 0 then
            BrowseWGCtrl.Instance:SendQueryRoleInfoReq(main_vo.lover_uid, BindTool.Bind(self.QueryRoleLoverInfoCallBack, self))      
        else
            FlowerWGCtrl.Instance:Open()
        end
		-- self:SendFlowerSeq(0)
	-- end
end

function MarryXunYouView:QueryRoleLoverInfoCallBack(info)
    FlowerWGCtrl.Instance:OpenSendFlowerView(info.role_id, info.role_name, info.sex, info.prof)
end

--不支持配置，可无限次购买
function MarryXunYouView:OpenBuyAlert( index )
	if BUY_REDPACK == index then
		if nil == self.alert_red_pack then
			local red_pack_price, _ = MarryWGData.Instance:GetXunYouRedPackAndFlowerPrice()
			local str = string.format(Language.Marry.BuyRedPackTips, red_pack_price)
            self.alert_red_pack = Alert.New(str, BindTool.Bind(self.SendRedPackSeq, self, 1), nil, nil, true)
            self.alert_red_pack:SetCheckBoxDefaultSelect(false)
		end
		self.alert_red_pack:Open()
	elseif BUY_FLOSER == index then
		if nil == self.alert_flower then
			local _, flower_price = MarryWGData.Instance:GetXunYouRedPackAndFlowerPrice()
			local str = string.format(Language.Marry.BuyFlowerTips, flower_price)
			self.alert_flower = Alert.New(str, BindTool.Bind(self.SendFlowerSeq, self, 1))
		end
		self.alert_flower:Open()
	end
end

function MarryXunYouView:SendRedPackSeq(num)
	-- print_error("撒红包")
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_XUNYOU_SA_HONGBAO, num)
end

--购买鲜花
function MarryXunYouView:SendFlowerSeq(num)
	-- print_error("撒花")
	-- MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_XUNYOU_GIVE_FLOWER, num)
end

---------------------------------------------------------------------------------
--#region
function MarryXunYouView:SetCurRedPackCount( num )
	self.red_pack_count = num
	self.node_list["sprinkle_red_pack_count"].text.text = num
end

function MarryXunYouView:SetCurFlowerCount( num )
	-- self.flower_count = num
	-- self.node_list["sprinkle_flower_count"].text.text = num
end
--#endregion
---------------------------------------------------------------------------------

function MarryXunYouView:OnBtnConcise()
	self.is_concise_state = not self.is_concise_state
	MarryWGCtrl.Instance:SetXunYouMainUiState(self.is_concise_state)
	self:UpdateConciseBtn(self.is_concise_state)
end

function MarryXunYouView:UpdateConciseBtn(value)
	local str = value and "j_jian" or "j_jian1"
	local bundle, asset = ResPath.GetXunyouImg(str)
	self.node_list["btn_concise"].image:LoadSprite(bundle, asset)
end