MapGlobalView = MapGlobalView or BaseClass(BaseRender)

local MAP_COUNT = 10

local RectTransUtility = UnityEngine.RectTransformUtility

local ARTIFACT_PANEL_POS = {
	[0] = {350, -58},
	[1] = {100, -120},
	[2] = {330, 47},
	[3] = {-174, -18},
	[4] = {328, 160},
}

function MapGlobalView:LoadCallBack()
	self.map_item = {}
	self.label = {}
	self.map_name = {}

	self.level_limit = {}

	self.map_img = {}
	self.map_select_img = {}
	self.map_effect = {}
	self.scene_index = {}

	self.world_to_local_timestemp = 0

	if nil == self.change_head_icon then
		self.change_head_icon = GlobalEventSystem:Bind(OtherEventType.CHANGE_HEAD_ICON, BindTool.Bind(self.ChangeHeadIcon, self))
	end

	local cur_role_level = RoleWGData.Instance:GetRoleLevel()
	local scene_logic = Scene.Instance:GetSceneLogic()
	for i = 1, #MapWGData.WORLDCFG do
		local scene_id = MapWGData.WORLDCFG[i]
		self.map_item[scene_id] = self.node_list["ImageIcon" .. i]
		self.map_select_img[scene_id] = self.node_list["map_select_" .. i]
		self.map_img[scene_id] = self.node_list["map_" .. i]
		self.map_select_img[scene_id]:SetActive(false)
		

		local name_table = self.map_item[scene_id]:GetComponent(typeof(UINameTable))
		local node_list = U3DNodeList(name_table)
		self.scene_index[scene_id] = i
		self.label[scene_id] = node_list["InvisiblePos"]
		self.map_name[scene_id] = node_list["map_name"]
		self.map_effect[scene_id] = node_list["effect"]

		self.map_effect[scene_id]:SetActive(false)
		--self.level_limit[scene_id] = node_list["level_limit"]
		--self.shadow_t[scene_id] = node_list["shadow"]

		self.map_item[scene_id].button:AddClickListener(BindTool.Bind(self.OnClickButton, self, scene_id, i))

		if i == 6 then
			self.map_name[scene_id].text.text = Language.Map.MapName6
		elseif i == 7 then
			self.map_name[scene_id].text.text = Language.Map.MapName7
		end

		local map_config = MapWGData.Instance:GetMapConfig(scene_id)
		
		if map_config then
			local name = map_config.name or ""

			self.map_name[scene_id].text.text = name

			
			
			local level = map_config.levellimit or 0
			-- 巅峰 等级
			local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(level) 
			local str = ""
			str = role_level

			if cur_role_level >= role_level then
				local max_lev = 0
				local monsters_list, boo = scene_logic:GetFbSceneMonsterListCfg(map_config.monsters)
				if false == boo then
					monsters_list = MapWGData.Instance:GetSceneMonsterSort(map_config.monsters)
				end
				if monsters_list then
					for k,v in ipairs(monsters_list) do
						local boss_config = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[v.id]
						local lev_num = 0
						if boss_config then
							if Scene.Instance:GetSceneType() == SceneType.CROSS_LIEKUN then
								local cross_lieKun_info = GuildWGData.Instance:GetCrossLieKunFBSceneInfo()
								lev_num = boss_config.level + cross_lieKun_info.boss_extra_level
							else
								lev_num = boss_config.level
							end
						end
						if max_lev < lev_num then
							max_lev = lev_num
						end
					end
					-- local is_vis_maxlev, vis_maxlev = RoleWGData.Instance:GetDianFengLevel(max_lev)
					-- local minlev_str = is_vis and str or map_config.levellimit
				end
			end
		end
	end

	if not self.travel_tx_item_list then
        self.travel_tx_item_list = {}
        for i = 1, 5 do
            self.travel_tx_item_list[i] = ArtifactTravelTXItem.New(self.node_list["btn_travel_item_" .. i])
			self.travel_tx_item_list[i]:SetIndex(i)
			self.travel_tx_item_list[i]:SetClickCallBack(BindTool.Bind(self.OnClickTravelItemBtn, self))
        end
    end

	if not self.travel_info_list then
        self.travel_info_list = AsyncListView.New(ArtifactTravelInfoItem, self.node_list["travel_info_list"])
    end

	XUI.AddClickEventListener(self.node_list["btn_hide_travel_mask"],BindTool.Bind(self.OnClickHideTravelPanel, self))
	XUI.AddClickEventListener(self.node_list["btn_goto_travel"],BindTool.Bind(self.OnClickGoToTravelBtn, self))

	local size_delta = self.node_list["Map"].rect.sizeDelta
	self.map_width = size_delta.x / 2
	self.map_height = size_delta.y / 2

	self.is_can_click = true
	self.eh_load_quit = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind1(self.OnSceneLoadingQuite, self))
	XUI.AddClickEventListener(self.node_list["btn_world_to_local"],BindTool.Bind(self.OnWorldToLocal, self))

	local pass_time = TimeUtil.GetTodayPassTime(TimeWGCtrl.Instance:GetServerTime())
	-- 白天 6:00-18:30
	-- 黑夜 18:31-5:59

	--黑夜
	if pass_time > 3600*18.5 or pass_time < 3600*6 then
		self.node_list.img_map.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_dt_dat_yw_bj_1"))

		for i = 1, #MapWGData.WORLDCFG do
			local scene_id = MapWGData.WORLDCFG[i]
			self.map_img[scene_id].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_dt_jz_yw_map_" .. i))
			self.map_select_img[scene_id].raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_dt_jz_yw_map_select_" .. i))
		end
	end

	self:Flush()

	self:ChangeHeadIcon()


	--self:DoOpenTween()
end

function MapGlobalView:DoOpenTween()
	--local parent = self.node_list["layout_global_root"].transform.parent
	--self.node_list["Map"].canvas_group.alpha = 0
	--self.node_list["Content"].canvas_group.alpha = 0
	--UITween.ScaleShowPanel(parent, Vector3(0,1,0), 0.15, DG.Tweening.Ease.Linear, function ()
		--self.node_list["Map"].canvas_group:DoAlpha(0, 1, 0.15)
		if self.alpha_delay_timer then
			GlobalTimerQuest:CancelQuest(self.alpha_delay_timer)
		end
		self.alpha_delay_timer = GlobalTimerQuest:AddDelayTimer(function ()
			--self.node_list["Content"].canvas_group:DoAlpha(0, 1, 0.1)
			local scene_id = Scene.Instance:GetSceneId()
			local is_in_scene, cur_scene_id = self:IsInScene(scene_id)
			if not is_in_scene then
				self.node_list["MainroleIcon"]:SetActive(false)
			else
				self.node_list["MainroleIcon"]:SetActive(true)
				local screen_pos_tbl = RectTransUtility.WorldToScreenPoint(UICamera, self.label[cur_scene_id].rect.position)
				local rect = self.node_list["Content"].rect
				local _, local_position_tbl = RectTransUtility.ScreenPointToLocalPointInRectangle(rect, screen_pos_tbl, UICamera, Vector2(0, 0))
				self.node_list["MainroleIcon"].rect:SetLocalPosition(local_position_tbl.x, local_position_tbl.y, 0)
				--self:JumpToMap(scene_id)
			end

			GlobalTimerQuest:CancelQuest(self.alpha_delay_timer)
			self.alpha_delay_timer = nil
		end, 0.15)
	--end)
end

function MapGlobalView:ChangeHeadIcon()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self:GetRoleHeadCell()
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true

	self.role_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.role_head_cell:SetData(data)
	self.role_head_cell:SetBgActive(false)
	self.role_head_cell:SetHeadCellScale(0.8)
end

function MapGlobalView:GetRoleHeadCell()
	if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["icon"])
	end
end

function MapGlobalView:OnWorldToLocal()
	if Status.NowTime - self.world_to_local_timestemp >= 2.0 then
		self.world_to_local_timestemp = Status.NowTime
		GlobalEventSystem:Fire(OtherEventType.MAP_VIEW_SWITCH, 10)
	end
end

function MapGlobalView:ShowIndexCallBack()
	-- self.node_list["yun_content"].animator:SetTrigger("play")
end

function MapGlobalView:ReleaseCallBack()
	if nil ~= self.eh_load_quit then
		GlobalEventSystem:UnBind(self.eh_load_quit)
		self.eh_load_quit = nil
	end
	self.label = {}
	self.map_item = {}

	self.level_limit = {}
	self.map_img = {}
	self.map_select_img = {}
	self.scene_index = {}
	self.map_effect = {}

	if self.role_head_cell then
		self.role_head_cell:SetHeadCellScale(1)
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	if self.change_head_icon then
		GlobalEventSystem:UnBind(self.change_head_icon)
		self.change_head_icon = nil
	end

	if self.alpha_delay_timer then
		GlobalTimerQuest:CancelQuest(self.alpha_delay_timer)
		self.alpha_delay_timer = nil
	end

	if self.travel_tx_item_list then
		for k, v in pairs(self.travel_tx_item_list) do
			v:DeleteMe()
		end
		self.travel_tx_item_list = nil
	end

	if self.travel_info_list then
		self.travel_info_list:DeleteMe()
		self.travel_info_list = nil
	end
end

-- 锁定 遮罩显示 策划说后面出特效
function MapGlobalView:SetShowLock(scene_id, is_show)
	self.map_effect[scene_id]:SetActive(is_show)
end

function MapGlobalView:OnSceneLoadingQuite()
	self:Flush()
end

function MapGlobalView:OnClickButton(target_scene_id, index)
	
	if index == 6 then
		-- 魔界纵横
		ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_vip)
		return 
	end
	if index == 7 then
		-- 猎魔深渊
		ViewManager.Instance:Open(GuideModuleName.Boss, TabIndex.boss_world)
		return 
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove(true) or TaskWGCtrl.Instance:IsFly() then
		return
	end
	local flag, str = Scene.Instance:CheckCanChangeScene()
    if not flag then
        if str then
            TipWGCtrl.Instance:ShowSystemMsg(str)
        end
        return
    end

	local scene_id = Scene.Instance:GetSceneId()
	if target_scene_id == scene_id then
		return
	end

	if self:GetIsCanGoToScene(target_scene_id, true) then
		if self.is_can_click and target_scene_id ~= scene_id then
			GuajiWGCtrl.Instance:ResetMoveCache()
			TaskGuide.Instance:CanAutoAllTask(false)
			GuajiWGCtrl.Instance:SetMoveToPosCallBack(nil)

			self.is_can_click = false
			self.target_scene_id = target_scene_id
			local screen_pos_tbl = RectTransUtility.WorldToScreenPoint(UICamera, self.label[target_scene_id].rect.position)
			local _, local_position_tbl =
				RectTransUtility.ScreenPointToLocalPointInRectangle(self.node_list["Content"].rect, screen_pos_tbl, UICamera, Vector2(0, 0))
			--这里为了防止旧场景导致的报错，暂时性判空下
			if nil ~= self.map_select_img[scene_id] then
				self.map_select_img[scene_id]:SetActive(false)

			end
			self.map_select_img[target_scene_id]:SetActive(true)


			local tweener = self.node_list["MainroleIcon"].rect:DOAnchorPos(local_position_tbl, 1, false)
			tweener:OnComplete(function ()
				self:OnMoveEnd(target_scene_id)
			end)
		end
	end
end

-- 跳到对应小地图的点上 
function MapGlobalView:JumpToMap(scene_id)
	local scene_id_x = 1 / (MAP_COUNT - 4)
	self.node_list["scroll_view"].scroll_rect.horizontalNormalizedPosition = (self.scene_index[scene_id] - 4) * scene_id_x
end

function MapGlobalView:OnFlush()
	--等级限制
	local main_role = GameVoManager.Instance:GetMainRoleVo()
	local level = main_role.level
	for _, v in ipairs(MapWGData.WORLDCFG) do
		if type(v) == "number" then
			local scene_config = ConfigManager.Instance:GetSceneConfig(v)
			if not scene_config then
				return
			end

			local levellimit = scene_config.levellimit
			self:SetShowLock(v, level < levellimit)
		else
			if v == "vip_boss" then
				local is_opened = FunOpen.Instance:GetFunIsOpened("boss_vip")
				self:SetShowLock(v, not is_opened)
			elseif v == "world_boss" then
				local is_opened = FunOpen.Instance:GetFunIsOpened("worldboss")
				self:SetShowLock(v, not is_opened)
			end
			
		end
	end

	local scene_id = Scene.Instance:GetSceneId()
	local is_in_scene, cur_scene_id = self:IsInScene(scene_id)
	if is_in_scene then
		self.map_select_img[cur_scene_id]:SetActive(true)
	end

	self:FlushArtifactTravelInfo()
end

-- 判断是否在世界地图显示的地图范围内 
function MapGlobalView:IsInScene(scene_id)
	if self.map_select_img  then
		-- 普通地图
		if self.map_select_img[scene_id] then
			return true, scene_id
		else
			-- boss地图
			local cfg, scene_id = MapWGData.Instance:GetBossCfg(scene_id)
			if cfg then
				return true, scene_id
			end
		end
	end
	return false, ""
end

function MapGlobalView:OnMoveEnd(target_scene_id)
	self.is_can_click = true
	local scene_id = Scene.Instance:GetSceneId()
	if target_scene_id ~= scene_id then
		GuajiWGCtrl.Instance:ClearTaskOperate()
		-- if Scene.Instance:GetMainRole():IsFightState() then
			-- GuajiWGCtrl.Instance:MoveToScene(target_scene_id)
		-- else
			GuajiWGCtrl.Instance:FlyToScene(target_scene_id, true, 1)
		-- end
		ViewManager.Instance:Close(GuideModuleName.Map)
	end
	self.target_scene_id = nil
end

function MapGlobalView:GetIsCanGoToScene(target_scene_id, is_tip)
	local tip = ""
	local is_can_go = true

	local scene = ConfigManager.Instance:GetSceneConfig(target_scene_id)
	if scene ~= nil then
		local level = scene.levellimit or 0
		if level > RoleWGData.Instance.role_vo.level then
			tip = string.format(Language.Map.level_limit_tip, RoleWGData.GetLevelString(level))
			is_can_go = false

		end
	end

	if is_can_go and (Scene.Instance:GetSceneType() ~= 0 or Scene.Instance:GetSceneId() == 9502) then --蓬莱仙岛不可传送
		is_can_go = false
		tip = Language.Map.TransmitLimitTip
	end

	--策划需求：去掉小飞鞋判断
	-- if is_can_go then
	    -- local can_free_fly = VipPower.Instance:GetHasPower(VipPowerId.scene_fly)
	    -- local item_index = ItemWGData.Instance:GetItemIndex(COMMON_CONSTS.FLY_PROP_ID)
	    -- if not can_free_fly and item_index < 0 then
	  --   	local need_gold = ShopWGData.GetItemPrice(COMMON_CONSTS.FLY_PROP_ID).gold or 0
	  --   	local bind_gold = RoleWGData.Instance:GetRoleInfo().bind_gold or 0
			-- local gold = RoleWGData.Instance:GetRoleInfo().gold or 0
			-- if gold + bind_gold < need_gold then
		    	-- is_can_go = false
		    	-- tip = Language.Common.FlyShoesLimit3
		    -- end
	    -- end
	-- end

	if not is_can_go and is_tip and tip ~= "" then
		SysMsgWGCtrl.Instance:ErrorRemind(tip)
	end

	return is_can_go
end

function MapGlobalView:FlushArtifactTravelInfo()
	-- 同游信息
	local travel_cfg_list = ArtifactWGData.Instance:GetTravelCfgList()
	for i, v in ipairs(self.travel_tx_item_list) do
		v:SetData(travel_cfg_list[i])
	end
end

function MapGlobalView:OnClickTravelItemBtn(item)
	local data = item:GetData()
	if not data then
		return
	end

	self.node_list["artifact_travel_panel"]:SetActive(true)
	local pos = ARTIFACT_PANEL_POS[data.scene_seq]
	self.node_list["artifact_travel_panel"].rect.anchoredPosition = Vector2(pos[1], pos[2])
	local travel_list = ArtifactWGData.Instance:GetArtifactTravelListByScene(data.scene_seq)
	self.travel_info_list:SetDataList(travel_list)
end

function MapGlobalView:OnClickHideTravelPanel()
	self.node_list["artifact_travel_panel"]:SetActive(false)
end

function MapGlobalView:OnClickGoToTravelBtn()
	ViewManager.Instance:Open(GuideModuleName.ArtifactTravelView)
	self:OnClickHideTravelPanel()
end

---------------------------------------
-- 同游头像Item
---------------------------------------
ArtifactTravelTXItem = ArtifactTravelTXItem or BaseClass(BaseRender)
function ArtifactTravelTXItem:__init()
end

function ArtifactTravelTXItem:LoadCallBack()

end

function ArtifactTravelTXItem:ReleaseCallBack()

end

function ArtifactTravelTXItem:OnFlush()
	if not self.data then
		return
	end

	local travel_list = ArtifactWGData.Instance:GetArtifactTravelListByScene(self.data.scene_seq)

	if IsEmptyTable(travel_list) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)
	
	self.node_list["tag_num"]:SetActive(#travel_list > 1)
	self.node_list["txt_travel_num"].text.text = "x" .. #travel_list

	local first_end_data = travel_list[1]
	for i, v in ipairs(travel_list) do
		if v.end_time < first_end_data.end_time then
			first_end_data = v
		end
	end

	local bundle, asset = ResPath.GetStrangeCatalogImg("a3_sxhg_sx_tx_" .. first_end_data.artifact_seq)
	self.node_list["img_artifact_tx"].image:LoadSprite(bundle, asset)
end

---------------------------------------
-- 同游列表Item
---------------------------------------
ArtifactTravelInfoItem = ArtifactTravelInfoItem or BaseClass(BaseRender)
function ArtifactTravelInfoItem:__init()
end

function ArtifactTravelInfoItem:LoadCallBack()
end

function ArtifactTravelInfoItem:ReleaseCallBack()
	self:CleanTimer()
end

function ArtifactTravelInfoItem:OnFlush()
	if not self.data then
		return
	end

	local artifact_seq = self.data.artifact_seq
	local bundle, asset = ResPath.GetStrangeCatalogImg("a3_sxhg_sx_tx_" .. artifact_seq)
	self.node_list["img_icon_tx"].image:LoadSprite(bundle, asset)

	local artifact_data = ArtifactWGData.Instance:GetArtifactItemDataBySeq(artifact_seq)
	local awake_name = ArtifactWGData.Instance:GetArtifactAwakeName(artifact_seq, artifact_data.awake_level)
	self.node_list["txt_name"].text.text = awake_name

	self:TravelCountDown()
end

function ArtifactTravelInfoItem:TravelCountDown()
    self:CleanTimer()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.data.end_time > server_time then
		self:UpdateGradeCountDown(server_time, self.data.end_time)
		self.timer = CountDown.Instance:AddCountDown(self.data.end_time - server_time, 1,
        	BindTool.Bind(self.UpdateGradeCountDown, self),        -- 回调方法
        	function()-- 倒计时完成回调方法
				self:Flush()
			end
		)
	else
		self.node_list["txt_travel_time"].text.text = Language.Artifact.TravelFinished
	end
end

function ArtifactTravelInfoItem:UpdateGradeCountDown(elapse_time, total_time)
	local str = string.format(Language.Artifact.LimitTravelTime2, TimeUtil.FormatSecondDHM9(total_time - elapse_time))
	self.node_list["txt_travel_time"].text.text = str
end

function ArtifactTravelInfoItem:CleanTimer()
    if self.timer and CountDown.Instance:HasCountDown(self.timer) then
        CountDown.Instance:RemoveCountDown(self.timer)
        self.timer = nil
    end
end