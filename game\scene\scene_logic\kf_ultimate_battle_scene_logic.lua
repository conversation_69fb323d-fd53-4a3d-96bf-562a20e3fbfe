KfUltimateBattleSceneLogic = KfUltimateBattleSceneLogic or BaseClass(CrossServerSceneLogic)

function KfUltimateBattleSceneLogic:__init()
	self.change_special_param_func = BindTool.Bind(self.ChangeSpecialParam, self)
	self.change_special = GlobalEventSystem:Bind(SceneEventType.ROLE_ENTER_ROLE, self.change_special_param_func)
	self.change_rob_special = GlobalEventSystem:Bind(SceneEventType.OBJ_ENTER_SHADOW, self.change_special_param_func)
	self.obj_die_handler = GlobalEventSystem:Bind(ObjectEventType.OBJ_DEAD, BindTool.Bind(self.PlayerIsDie, self))
end

function KfUltimateBattleSceneLogic:__delete()
	if self.change_special then
		GlobalEventSystem:UnBind(self.change_special)
		self.change_special = nil
	end

	if self.change_rob_special then
		GlobalEventSystem:UnBind(self.change_rob_special)
		self.change_rob_special = nil
	end

	if self.shield_pet_rule then
		self.shield_pet_rule:DeleteMe()
		self.shield_pet_rule = nil
	end

	if self.obj_die_handler then
		GlobalEventSystem:UnBind(self.obj_die_handler)
		self.obj_die_handler = nil
	end

	self.obj_list = nil
	self.is_guess_status = false
	self.fb_name = string.format(Language.UltimateBattlefield.RoundStr, NumberToChinaNumber(1))
end

function KfUltimateBattleSceneLogic:Update(now_time, elapse_time)
	CrossServerSceneLogic.Update(self, now_time, elapse_time)
end

function KfUltimateBattleSceneLogic:CanGetMoveObj()
	return UltimateBattlefieldWGData.Instance:CheckActIsBattleByScene()
end

-- 进入准备场景
function KfUltimateBattleSceneLogic:Enter(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Enter(self, old_scene_type, new_scene_type)
	if old_scene_type == new_scene_type then
		return
	end

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
        MainuiWGCtrl.Instance:SetTaskContents(false)
        MainuiWGCtrl.Instance:SetOtherContents(true)
		-- MainuiWGCtrl.Instance:SetBtnTianxianPavilionStatus(false)
		MainuiWGCtrl.Instance:SetFBNameState(true, self.fb_name)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetMianUITargetPos(nil, -40)
	end)

	self.shield_pet_rule = SimpleRule.New(ShieldObjType.Pet, ShieldRuleWeight.Middle, function (obj)
		return not obj:IsOnwerPet()
	end)
	self.shield_pet_rule:Register()



	local my_room_info = UltimateBattlefieldWGData.Instance:GetMyRankInfo()
	local main_role = Scene.Instance:GetMainRole()
	if my_room_info and main_role then
		self:SetFollowUI(main_role, my_room_info.camp)
	end

	local battle_scene_info = UltimateBattlefieldWGData.Instance:GetSceneInfo()

	if not battle_scene_info then
		return 
	end

	self.fb_name = string.format(Language.UltimateBattlefield.RoundStr, NumberToChinaNumber(battle_scene_info.stage + 1))
	local show_data = self:GetBattleShowData(battle_scene_info)
	-- 打开倒计时
	UltimateBattlefieldWGCtrl.Instance:OpenFollowView(show_data)
	UltimateBattlefieldWGCtrl.Instance:FlushFollowTalentSkillMessage()
	self.is_guess_status = false

	-- 存在竞猜，打开竞猜
	if UltimateBattlefieldWGData.Instance:CheckActIsGuessTime() then
		UltimateBattlefieldWGCtrl.Instance:OpenGuessView(battle_scene_info.stage)
	else
		TaskWGCtrl.Instance:AddFlyUpList(function ()
			if GuajiCache.guaji_type ~= GuajiType.Auto then
				GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			end
		end)
	end
end

-- 设置任务栏标题
function KfUltimateBattleSceneLogic:SetFBNameStr(fb_name)
	self.fb_name = fb_name
	MainuiWGCtrl.Instance:SetFBNameState(true, self.fb_name)
end

--获取战斗倒计时
function KfUltimateBattleSceneLogic:GetBattleShowData(battle_scene_info)
	local show_data = {}
	show_data.end_time = battle_scene_info.next_stage_time
	show_data.end_str = string.format(Language.UltimateBattlefield.WaitBattleRoundTime, NumberToChinaNumber(battle_scene_info.stage + 1)) 
	show_data.show_type = CROSS_1VN_TIME_DOWN_TYPE.CROSS_1VN_TIME_DOWN_BATTLE
	return show_data
end

-- 设置followUI
function KfUltimateBattleSceneLogic:SetFollowUI(role, camp)
	if not role then
		return
	end

	local follow_ui = role:GetFollowUi()
	local ultimate_data = {}
	ultimate_data.server_id = role.vo.origin_server_id
	local cfg = UltimateBattlefieldWGData.Instance:GetCampCfgBySeq(camp)
	ultimate_data.camp_name = cfg and cfg.camp_name or ""
	ultimate_data.camp = camp

	if follow_ui then
		follow_ui:SetUltimateState(ultimate_data)
	end
end

-- 离开场景
function KfUltimateBattleSceneLogic:Out(old_scene_type, new_scene_type)
	CrossServerSceneLogic.Out(self)
	if old_scene_type == new_scene_type then
		return
	end

	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
    MainuiWGCtrl.Instance:SetTaskContents(true)
    MainuiWGCtrl.Instance:SetOtherContents(false)
	-- MainuiWGCtrl.Instance:SetBtnTianxianPavilionStatus(true)
	MainuiWGCtrl.Instance:SetMianUITargetPos(nil, 0)
	UltimateBattlefieldWGCtrl.Instance:ClsoeFollowView()
end

-- 当自身没有匹配到房间阵营保存一下玩家信息
function KfUltimateBattleSceneLogic:SeveOtherRoleId(obj_id)
	self.obj_list = {}
	table.insert(self.obj_list, obj_id)
end

-- 当自身没有匹配到房间阵营保存一下玩家信息
function KfUltimateBattleSceneLogic:ChangeSpecialOtherRoleId()
	if not self.obj_list then
		return
	end

	local temp_obj_list = {}
	-- 这一步转存是为了防止第二次没找到阵营的情况
	for _, obj_id in ipairs(self.obj_list) do
		table.insert(temp_obj_list, obj_id)
	end
	self.obj_list = nil

	for _, obj_id in ipairs(temp_obj_list) do
		self:ChangeSpecialParam(obj_id)
	end
end

-- 角色阵营发生变化
function KfUltimateBattleSceneLogic:ChangeSpecialParam(obj_id)
	local main_role = Scene.Instance:GetMainRole()
	if (not main_role) or main_role.vo.special_param == -1 then
		self:SeveOtherRoleId(obj_id)
		return
	end

	local role = Scene.Instance:GetObj(obj_id)
	if (not role) or (not role.vo) then
		return
	end

	local camp = role.vo.special_param
	self:SetFollowUI(role, camp)

	if (camp ~= - 1) and camp == main_role.vo.special_param then
		local follow_ui = role and role:GetFollowUi()
		local follow_hp_bar = follow_ui and follow_ui:GetHpBar()
		if follow_hp_bar then
			follow_hp_bar:AddShieldRule(ShieldRuleWeight.Max, function()
				return true
			end)
		end
	end

	role:ReloadUIName()
	self:CanSelectObj(role)
end

-- 角色死亡
function KfUltimateBattleSceneLogic:PlayerIsDie(obj_dead)
	if not IS_ON_CROSSSERVER or obj_dead:GetType() == SceneObjType.Monster then return end
	local vo = GameVoManager.Instance:GetMainRoleVo()
	if vo.obj_id == obj_dead:GetObjId() then -- 主角不做操作
	else
		local obj = Scene.Instance:GetRoleByObjId(obj_dead:GetObjId())
		if obj then
			obj:GetFollowUi():ForceSetVisible(false)
			obj:HideOrShowRole(false)
			obj:ForceShadowVisible(false)
		end
	end	
end



function KfUltimateBattleSceneLogic:CanSelectObj(obj)
	-- IsEnemy会回调到 IsRoleEnemy
	if GuajiWGCtrl.Instance:GetCurSelectTargetObj() == nil and self:IsEnemy(obj) then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SCENE)
	end
end

-- 角色是否是敌人--活动结束也会在里面打
function KfUltimateBattleSceneLogic:IsRoleEnemy(target_obj, main_role)
	if target_obj == nil then 
		return false 
	end

	if not UltimateBattlefieldWGData.Instance:CheckActIsBattleByScene() then
		return false
	end

	local main_role = main_role or Scene.Instance:GetMainRole()
	local target_vo = target_obj:GetVo()

	if main_role:IsRealDead() then												-- 自己死亡
		return false
	end

	if target_obj:IsRealDead() then	
		return false
	end

	if target_vo then
		local main_vo = main_role:GetVo()

		if main_vo.special_param == -1 then
			return false
		end


		local target_camp = target_vo.special_param
		if target_camp == main_vo.special_param then
			target_obj:SetHpVisiable(false)
			return false
		else
			return true
		end
	end

	local is_enemy = BaseSceneLogic.IsRoleEnemy(self, target_obj, main_role)
	if not is_enemy then
		target_obj:SetHpVisiable(false)
	end

	return is_enemy
end

function KfUltimateBattleSceneLogic:IsSetAutoGuaji()
	return true
end

function KfUltimateBattleSceneLogic:GetSceneQualityPolicy()
	return QualityPolicy.UnitCount
end

-- 此场景优先显示敌人(false则优先显示队员)
function KfUltimateBattleSceneLogic:IsEnemyVisiblePriortiy()
	return true
end

function KfUltimateBattleSceneLogic:GetRoleNameBoardText(role_vo)
	local main_role = Scene.Instance:GetMainRole()
	local main_vo = main_role and main_role:GetVo()
	local my_comp = main_vo and main_vo.special_param or -1
	local target_is_red_side = role_vo.special_param

    local color = COLOR3B.WHITE
    local role_id = RoleWGData.Instance.role_vo.role_id
    if role_id ~= role_vo.role_id then
    	color = target_is_red_side ~= my_comp and COLOR3B.RED or COLOR3B.WHITE
    end
	local t = {}
	t.color = color
	t.text =  role_vo.role_id == role_id and role_vo.name or Language.Common.MysteryMen
	return t
end

function KfUltimateBattleSceneLogic:SetActStatus()
	self:CheckSceneMapBlock(SceneType.CROSS_ULTIMATE_BATTLE)
end

function KfUltimateBattleSceneLogic:CheckSceneMapBlock(scene_id)
	scene_id = scene_id or Scene.Instance:GetSceneId()
	local act_scene = SceneWGData.Instance:GetMapBlockOtherCfg(scene_id)
	if nil == act_scene then
		return
	end

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_ULTIMATE_BATTLE)
	if activity_info and UltimateBattlefieldWGData.Instance:CheckActIsGuessTime() then
		self:SetSceneBlock()

		local map_block_bubble_cfg = SceneWGData.Instance:GetMapBlockBubbleCfg()
		if nil == self.role_bubble_timer and map_block_bubble_cfg then
			self.role_bubble_timer = GlobalTimerQuest:AddRunQuest(
				function()
					self:SetRoleBubble(scene_id)
				end, map_block_bubble_cfg.show_time)
		end
	else
		self:RemoveSceneBlock()
	end
end

function KfUltimateBattleSceneLogic:SetStopGuaji()
	self.is_guess_status = true
	if GuajiCache.guaji_type == GuajiType.Auto then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	end
end

function KfUltimateBattleSceneLogic:SetGuaji()
	self.is_guess_status = false
	if GuajiCache.guaji_type ~= GuajiType.Auto then
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end
end

function KfUltimateBattleSceneLogic:GetGuajiPos()
	if self.is_guess_status then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	if not main_role then
		return
	end

	local pos = UltimateBattlefieldWGData.Instance:GetFindPostion()
	if not pos then
		return
	end

	return pos.x, pos.y
end