local EFFECT_NAME =
{
	[0] = "UI_VIP_zhouka_lv",
	[1] = "UI_VIP_zhouka_zi",
	[2] = "UI_VIP_zhouka_zhizun",
}
-- 特权周卡
function RechargeView:InitWeekCardPanel()
	self.select_week_type = 1
	XUI.AddClickEventListener(self.node_list.week_buy_btn, BindTool.Bind(self.OnClickWeekBuybtn, self))
	XUI.AddClickEventListener(self.node_list.reward_get_btn, BindTool.Bind(self.OnClickWeekRewardbtn, self))
	XUI.AddClickEventListener(self.node_list.quan_btn, BindTool.Bind(self.OnClickChongZhiQuanbtn, self))
	XUI.AddClickEventListener(self.node_list.shop_btn, BindTool.Bind(self.OnClickShopbtn, self))


	if not self.week_item_cell_list then
		self.week_item_cell_list = {}

		for i = 1, 3 do
			self.week_item_cell_list[i] = ItemCell.New(self.node_list["week_item_cell" .. i])
			-- self.week_item_cell_list[i]:SetData(week_item_show[i - 1])
		end
	end

	if not self.week_card_type_list then
		self.week_card_type_list = AsyncListView.New(WeekCardTypeRender, self.node_list.week_card_type_list)
		self.week_card_type_list:SetSelectCallBack(BindTool.Bind(self.OnChangeCardType, self))
	end

	if not self.week_card_attr_add_list then
		self.week_card_attr_add_list = AsyncListView.New(WeekCardAdditionRender, self.node_list.week_card_attr_add_list)
	end
end

function RechargeView:ShowWeekCardIndexCallBack()
	if RechargeWGData.Instance:GetWeekCardToDayEnterNeedRemind() then
		RechargeWGData.Instance:SetWeekCardToDayEnterNeedRemind()
	end
end

function RechargeView:FlushWeekCardItemList()
	local week_item_show = RechargeWGData.Instance:GetWeekCardItemShow(self.select_week_type)
	if IsEmptyTable(week_item_show) then
		return
	end

	local addition_data = RechargeWGData.Instance:GetWeekCardAddition(self.select_week_type)
	if IsEmptyTable(addition_data) then
		return
	end

	local other_cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(self.select_week_type)
	if not other_cfg then
		return
	end

	local day_num = other_cfg.week_card_day
	local cur_day = RechargeWGData.Instance:GetWeekCardCurrentDay()
	local cur_grade = RechargeWGData.Instance:GetWeekCardGrade()
	local has_week_card = RechargeWGData.Instance:IsHasWeekCard()
	local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(cur_day)
	local can_get_reward = has_week_card and cur_day <= day_num and not get_flag

	for k, v in pairs(self.week_item_cell_list) do
		v:SetData(week_item_show[k - 1])
		v:SetLingQuVisible(cur_grade == self.select_week_type - 1 and not can_get_reward)
	end

	table.insert(addition_data, 1, { show_str = true })
	self.week_card_attr_add_list:SetDataList(addition_data)
end

function RechargeView:DeleteWeekCardPanel()
	if self.week_item_cell_list then
		for k,v in pairs(self.week_item_cell_list) do
			v:DeleteMe()
		end
		self.week_item_cell_list = nil
	end

	if self.week_card_type_list then
		self.week_card_type_list:DeleteMe()
		self.week_card_type_list = nil
	end

	if self.week_card_attr_add_list then
		self.week_card_attr_add_list:DeleteMe()
		self.week_card_attr_add_list = nil
	end

	self:RemoveWeekCardCountDown()
end

function RechargeView:FlushWeekCardPanel()
	local type_cfg = RechargeWGData.Instance:GetWeekCardOpenOtherCfg()
	self.week_card_type_list:SetDataList(type_cfg)

	local week_card_day = ""
	local cur_grade = RechargeWGData.Instance:GetWeekCardGrade()
	local is_has_week_card = RechargeWGData.Instance:IsHasWeekCard()

	local cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(self.select_week_type)
	if cfg then
		week_card_day = cfg.week_card_day
		local week_card_price_str = RoleWGData.GetPayMoneyStr(cfg.week_card_price, cfg.rmb_type, cfg.rmb_seq)

		local is_show = (self.select_week_type - 1 > cur_grade and is_has_week_card) or not is_has_week_card
		self.node_list.lifan:SetActive(is_show)
		self.node_list.week_buy_label:SetActive(is_show)
		self.node_list.week_buy_desc:SetActive(is_show and self.select_week_type - 1 > 0)
		self.node_list.week_buy_btn:SetActive(is_show)
		self.node_list.reward_get_btn:SetActive(not is_show)

		self.node_list.week_buy_label.text.text = week_card_price_str
		self.node_list.week_buy_desc.text.text = string.format(Language.Recharge.WeekCardBuyDesc, Language.Recharge.WeekCardName[self.select_week_type - 2] or "")
		self.node_list.title_day_text.text.text = week_card_day >= 999 and Language.Vip.VipTips_9 or string.format(Language.Recharge.BuyWeekCard2,  week_card_day)

		local bg_bundle, bg_asset = ResPath.GetVipImage("a3_vip_zktq_k" .. cfg.rmb_seq)
		self.node_list.vip_week_card_img.image:LoadSprite(bg_bundle, bg_asset)
		self.node_list.vip_week_card_img:ChangeAsset(ResPath.GetEffectUi(EFFECT_NAME[cfg.rmb_seq]))

		local title_bundle, title_asset = ResPath.GetNoPackPNG("a3_vip_zktq_txt" .. cfg.rmb_seq)
		self.node_list.week_card_title_img.image:LoadSprite(title_bundle, title_asset, function()
			self.node_list.week_card_title_img.image:SetNativeSize()
		end)
	end

	local time_str = ""
	local week_day_str = ""
	local is_infinite = RechargeWGData.Instance:GetIsInfinite(self.select_week_type)
	local remain_time = RechargeWGData.Instance:GetWeekCardRemainTime()
	if is_infinite then
		time_str = Language.Vip.VipTips_9
	else
		time_str = TimeUtil.FormatSecondDHM5(remain_time)
	end

	if remain_time > 0 then
		if cur_grade >= 0 then
			self:RemoveWeekCardCountDown()
			if cur_grade > self.select_week_type - 1 then
				week_day_str = string.format(Language.Recharge.WeekCardUpText, Language.Recharge.WeekCardName[cur_grade])
			elseif cur_grade < self.select_week_type - 1 then
				local day_str = is_infinite and Language.Vip.VipTips_9 or string.format(Language.Recharge.BuyWeekCard2, week_card_day)
				week_day_str = string.format(Language.Recharge.BuyWeekCard, day_str)
			elseif cur_grade == self.select_week_type - 1 then
				week_day_str = string.format(Language.Recharge.WeekCardTime, time_str)
				CountDownManager.Instance:AddCountDown("week_card_count_down",
					BindTool.Bind(self.UpdateWeekCardCountDown, self),
					BindTool.Bind(self.OnWeekCardComPlete, self),
					nil, remain_time, 1)
			end
		end
	else
		week_day_str =	Language.Recharge.NoWeekCard
	end

	self.node_list.week_day.text.text = week_day_str

	self:GetRewardBtnFlush()
	self:FlushWeekCardItemList()
end

function RechargeView:UpdateWeekCardCountDown(elapse_time, total_time)
	local valid_time = total_time - elapse_time
	if valid_time > 0 and self.node_list and self.node_list.week_day then
		local time_str = ""
		if RechargeWGData.Instance:GetIsInfinite(self.select_week_type) then
			time_str = Language.Vip.VipTips_9
		else
			time_str = TimeUtil.FormatSecondDHM5(valid_time)
		end

		self.node_list.week_day.text.text = string.format(Language.Recharge.WeekCardTime, time_str)
	end
end

function RechargeView:OnWeekCardComPlete()
	if self.node_list and self.node_list.week_day then
		self.node_list.week_day.text.text =	Language.Recharge.NoWeekCard
	end
end

function RechargeView:RemoveWeekCardCountDown()
    if CountDownManager.Instance:HasCountDown("week_card_count_down") then
        CountDownManager.Instance:RemoveCountDown("week_card_count_down")
    end
end

function RechargeView:OnClickWeekRewardbtn()
	local other_cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(self.select_week_type)
	if not other_cfg then
		return
	end

	local day_num = other_cfg.week_card_day
	local cur_grade = RechargeWGData.Instance:GetWeekCardGrade()
	local cur_day = RechargeWGData.Instance:GetWeekCardCurrentDay()
	local has_week_card = RechargeWGData.Instance:IsHasWeekCard()
	local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(cur_day)
	local can_get_reward = self.select_week_type - 1 == cur_grade and has_week_card and cur_day <= day_num and not get_flag
	local has_get_reward = self.select_week_type - 1 == cur_grade and has_week_card and cur_day <= day_num and get_flag

	if not has_week_card then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.WeekCardTips[1])
	elseif has_get_reward then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.WeekCardTips[0])
	elseif can_get_reward then
		RechargeWGCtrl.Instance:SendWeekCardOperate(WEEKCARD_OPERATE_TYPE.WEEKCARD_OPERATE_TYPE_FETCH_DAILY_REWARD,cur_day)
	end
end

function RechargeView:GetRewardBtnFlush()
	local other_cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(self.select_week_type)
	if not other_cfg then
		return
	end

	local day_num = other_cfg.week_card_day
	local cur_day = RechargeWGData.Instance:GetWeekCardCurrentDay()
	local cur_grade = RechargeWGData.Instance:GetWeekCardGrade()
	local has_week_card = RechargeWGData.Instance:IsHasWeekCard()
	local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(cur_day)
	local can_get_reward = cur_grade == self.select_week_type - 1 and has_week_card and cur_day <= day_num and not get_flag

	self.node_list.week_remind:SetActive(can_get_reward)

	local is_can_click = cur_grade < self.select_week_type - 1 or can_get_reward
	self.node_list.week_card_flag:SetActive(cur_grade == self.select_week_type - 1 and not can_get_reward)
	if not is_can_click then
		self.node_list.reward_get_btn:SetActive(false)
	end
end

function RechargeView:OnClickWeekBuybtn()
	local cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(self.select_week_type)
	if not cfg then
		return
	end

	local cur_grade = RechargeWGData.Instance:GetWeekCardGrade()
	local is_can_click = cur_grade < 0 and self.select_week_type - 1 <= 0 or self.select_week_type - 1 == cur_grade + 1
	if not is_can_click then
		local msg = string.format(Language.Recharge.WeekCardBuyTips, Language.Recharge.WeekCardName[self.select_week_type - 2] or "")
		TipWGCtrl.Instance:ShowSystemMsg(msg)
		return
	end

	RechargeWGCtrl.Instance:Recharge(cfg.week_card_price, cfg.rmb_type, cfg.rmb_seq)
end

function RechargeView:OnClickChongZhiQuanbtn()
	ViewManager.Instance:Open(GuideModuleName.RechargeVolumeView)
end

function RechargeView:OnClickShopbtn()
	ViewManager.Instance:Open(GuideModuleName.PrivilegeShop)
end

function RechargeView:OnChangeCardType(cell)
	local data = cell:GetData()
	local index = data.rmb_seq + 1
	if not data or self.select_week_type == index then
		return
	end

	self.select_week_type = index
	self:FlushWeekCardPanel()
end


WeekCardItemRender = WeekCardItemRender or BaseClass(BaseRender)
function WeekCardItemRender:__init()
	self.can_get_reward = false
end

function WeekCardItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function WeekCardItemRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell_root)
	end

	self.node_list.get_reward_btn.button:AddClickListener(BindTool.Bind(self.OnClickGetReward, self))
end

function WeekCardItemRender:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end

	local item_data = data.item_list[0]
	self.item_cell:SetData(item_data)

	self.node_list.name_label.text.text = string.format(Language.Welfare.diXtian, data.day)
	self.node_list.xianyu_label.text.text = data.item_list[1].num
	self.node_list.tongbi_label.text.text = data.item_list[2].num
	
	local day = RechargeWGData.Instance:GetWeekCardCurrentDay()
	local has_week_card = RechargeWGData.Instance:IsHasWeekCard()
	local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(data.day)
	local can_get_reward = has_week_card and data.day <= day and not get_flag
	local has_get_reward = has_week_card and data.day <= day and get_flag
	self.node_list.get_reward_img:SetActive(has_get_reward)
	self.node_list.get_reward_btn:SetActive(not has_get_reward)
	self.node_list.remind:SetActive(can_get_reward)
	XUI.SetGraphicGrey(self.node_list.get_reward_btn, not can_get_reward)
end

function WeekCardItemRender:OnClickGetReward()
	local data = self:GetData()
	if IsEmptyTable(data) then
		return
	end

	local day = RechargeWGData.Instance:GetWeekCardCurrentDay()
	local has_week_card = RechargeWGData.Instance:IsHasWeekCard()
	local get_flag = RechargeWGData.Instance:GetWeekCardRewardFlag(data.day)
	local can_get_reward = has_week_card and data.day <= day and not get_flag
	local has_get_reward = has_week_card and data.day <= day and get_flag

	if not has_week_card then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.WeekCardTips[1])
	elseif has_get_reward then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.WeekCardTips[0])
	elseif data.day > day then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Recharge.WeekCardTips[2])
	elseif can_get_reward then
		RechargeWGCtrl.Instance:SendWeekCardOperate(WEEKCARD_OPERATE_TYPE.WEEKCARD_OPERATE_TYPE_FETCH_DAILY_REWARD, data.day)
	end
end

------------WeekCardTypeRender周卡类型toggle---------
WeekCardTypeRender = WeekCardTypeRender or BaseClass(BaseRender)
function WeekCardTypeRender:OnFlush()
	if not self.data then
		return
	end

	local is_has_week_card = RechargeWGData.Instance:IsHasWeekCard()
	local cur_grade = RechargeWGData.Instance:GetWeekCardGrade()
	local have_card = cur_grade >= self.data.rmb_seq

	self.node_list.name.text.text = Language.Recharge.WeekCardName[self.data.rmb_seq]
	self.node_list.desc.text.text = Language.Recharge.WeekCardDesc[self.data.rmb_seq]

	self.node_list.state:SetActive((cur_grade <= self.data.rmb_seq and is_has_week_card) or not is_has_week_card)
	self.node_list.state2:SetActive(is_has_week_card and cur_grade > -1 and cur_grade > self.data.rmb_seq)

	if is_has_week_card and cur_grade > -1 and cur_grade > self.data.rmb_seq then
		self.node_list.state_text2.text.text = string.format(Language.Recharge.WeekCardUpText, Language.Recharge.WeekCardName[cur_grade])
	end

	local can_get_reward = RechargeWGData.Instance:IsShowWeekCardRemind() == 1
	self.node_list.RedPoint:SetActive(can_get_reward and cur_grade == self.data.rmb_seq)

	local week_card_price_str = Language.Recharge.WeekCardStateDesc

	if not have_card then
		local cfg = RechargeWGData.Instance:GetWeekCardOtherCfg(self.data.rmb_seq + 1)
		if cfg then
			week_card_price_str = RoleWGData.GetPayMoneyStr(cfg.week_card_price, cfg.rmb_type, cfg.rmb_seq)
		end
	end

	self.node_list.state_text.text.text = week_card_price_str

	local b1, a1 = ResPath.GetVipImage(have_card and "a3_vip_card_l" or "a3_vip_card_h")
	self.node_list.state.image:LoadSprite(b1, a1, function()
		self.node_list.state.image:SetNativeSize()
	end)

	-- local bg_bundle, bg_asset = ResPath.GetVipImage("a3_vip_zktq_k" .. self.data.rmb_seq)
	-- self.node_list.card_img.image:LoadSprite(bg_bundle, bg_asset, function()
	-- 	self.node_list.card_img.image:SetNativeSize()
	-- end)

	local b2, a2 = ResPath.GetVipImage("a3_vip_card_di" .. self.data.rmb_seq)
	self.node_list.normal_bg.image:LoadSprite(b2, a2, function()
		self.node_list.normal_bg.image:SetNativeSize()
	end)
end

function WeekCardTypeRender:OnSelectChange(is_select)
	self.node_list.hl_bg:SetActive(is_select)
	local scale = is_select and 1.05 or 1
	RectTransform.SetLocalScaleXYZ(self.node_list.root.rect, scale, scale , scale)
end

------------WeekCardAdditionRender---------
WeekCardAdditionRender = WeekCardAdditionRender or BaseClass(BaseRender)
function WeekCardAdditionRender:OnFlush()
	if not self.data then
		return
	end

	if self.data.show_str then
		self.node_list.desc.text.text = Language.Recharge.WeekCardAdditionDesc2
		return
	end

	local addition_data = Split(self.data, ",")
	local addition_idx = tonumber(addition_data[1])
	local addition_per = addition_data[2] / 100 --万分比.

	self.node_list.desc.text.text = string.format(Language.Recharge.WeekCardAdditionDesc1,
		Language.Recharge.WeekCardAdditionDesc[addition_idx], addition_per)
end