EternalNightMapView = EternalNightMapView or BaseClass(SafeBaseView)

function EternalNightMapView:__init()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/eternal_night_ui_prefab", "layout_eternal_night_map")
   	self:SetMaskBg()
end

function EternalNightMapView:__delete()
end

function EternalNightMapView:LoadCallBack()
    self:SetSecondView(nil, self.node_list["size"])
    self.map_list = {}
    local map_obj = self.node_list["maps"].transform
    for i=1,4 do
    	local str = "map"..i
    	local obj = U3DObject(map_obj:Find(str).gameObject, map_obj:Find(str), self)
    	self.map_list[i] = EternalNightMapItem.New(obj)
    	self.map_list[i]:SetIndex(i)
    end
end

function EternalNightMapView:ReleaseCallBack()
	if not IsEmptyTable(self.map_list) then
		for k,v in pairs(self.map_list) do
			v:DeleteMe()
		end
		self.map_list = {}
	end
end

function EternalNightMapView:OnFlush()
	for k,v in pairs(self.map_list) do
		v:Flush()
	end
end


-----------------------EternalNightMapItem------------------------------
EternalNightMapItem = EternalNightMapItem or BaseClass(BaseRender)

function EternalNightMapItem:__init()
	XUI.AddClickEventListener(self.node_list["btn"],BindTool.Bind(self.ClickMapBtn,self))
	local info = RoleWGData.Instance:GetRoleInfo()
	self.head_cell = BaseHeadCell.New(self.node_list["head_cell"])
	self.head_cell:SetData({role_id = info.role_id, sex = info.sex, prof = info.prof, fashion_photoframe = info.fashion_photoframe})
end

function EternalNightMapItem:__delete()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function EternalNightMapItem:OnFlush()
	local cfg = EternalNightWGData.Instance:GetMapCfgByIndex(self.index)
	if IsEmptyTable(cfg) then return end
	local room_info = EternalNightWGData.Instance:GetSceneRoomInfo(cfg.scene_id)
	if room_info then
		local room_key = room_info.sub_room_key
		self.node_list["map_name"].text.text = cfg and cfg.name or ""
		self.node_list["map_img"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("en_map"..self.index))
		self.node_list["map_img"].raw_image:SetNativeSize()
		local scene_key = RoleWGData.Instance:GetAttr("scene_key") or 0
		self.node_list["head"]:SetActive(scene_key == room_key)
	end

end

function EternalNightMapItem:ClickMapBtn()
	local cfg = EternalNightWGData.Instance:GetMapCfgByIndex(self.index)
	if IsEmptyTable(cfg) then return end
	local room_info = EternalNightWGData.Instance:GetSceneRoomInfo(cfg.scene_id)
	if room_info then
		local room_key = room_info.sub_room_key
		EternalNightWGCtrl.Instance:SendCSEternalNightEnterSubRoom(room_key)
		ViewManager.Instance:Close(GuideModuleName.EternalNightMapView)
	end
	
end
