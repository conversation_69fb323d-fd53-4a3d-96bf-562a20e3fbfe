QunXiongZhuLuWGData = QunXiongZhuLuWGData or BaseClass()

function QunXiongZhuLuWGData:__init()
    if QunXiongZhuLuWGData.Instance then
        error("[QunXiongZhuLuWGData]:Attempt to create singleton twice!")
        return
    end
    QunXiongZhuLuWGData.Instance = self
    self:InitData()
    self.kill_boss_times = {}
    self.rewatd_tag = {}
    self.list = {}
    self.act_is_over = false
    RemindManager.Instance:Register(RemindName.QunXiongZhuLu, BindTool.Bind(self.GetQXZLRemind, self))
end

function QunXiongZhuLuWGData:__delete()
    QunXiongZhuLuWGData.Instance = nil
    RemindManager.Instance:UnRegister(RemindName.QunXiongZhuLu)
end

function QunXiongZhuLuWGData:InitData()
    self.qunxiongzhulu_cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").herd_grade
    self.tips_cfg = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").herd
    self.herd_time = ConfigManager.Instance:GetAutoConfig("opengameactivity_auto").herd_time
    self.meng_zhan_cfg = ConfigManager.Instance:GetAutoConfig("guild_battle_auto").other[1]
    self.cfg = ListToMapList(self.qunxiongzhulu_cfg, "id")
end

function QunXiongZhuLuWGData:SetQXZLInfo(protocol)
    self.kill_boss_times = protocol.kill_boss_times
    self.rewatd_tag = bit:d2b_two(protocol.rewatd_tag)
end

function QunXiongZhuLuWGData:SetQXZLGuildBattleMatchInfo(protocol)
    self.role_name = protocol.role_name
    self.final_role_name = protocol.final_role_name
    self.win_role = protocol.win_role
end

function QunXiongZhuLuWGData:GetQXZLGuildBattleMatchInfo()
    return self.role_name, self.final_role_name, self.win_role
end


function QunXiongZhuLuWGData:SetQXZLKillTimesUpdate(protocol)
    local id = protocol.id
    local times  = protocol.times
    local data_list = {}
    data_list.times = times
    self.list[id] = data_list
end

function QunXiongZhuLuWGData:GetQXZLRemindByID(id)
    if self.rewatd_tag[id] and self.rewatd_tag[id] == 0 then
        return 1
    end      

    return 0
end

function QunXiongZhuLuWGData:GetMengZhanTime()
    local round_1 = self.meng_zhan_cfg.first_round
    local round_2 = self.meng_zhan_cfg.second_round
    local table_1 = Split(round_1,"-")
    local table_2 = Split(round_2,"-")
    return round_1, round_2, table_1, table_2
end

function QunXiongZhuLuWGData:GetQXZLIsCanByID(id)
    if self.kill_boss_times[id] and self.kill_boss_times[id] >= self.cfg[id][1].times then
        return 1
    end      

    return 0
end

function QunXiongZhuLuWGData:GetQXZLRemind()
    if self.act_is_over then
         return 0
    end
    
    for i = 1, #self.kill_boss_times do
        if self.rewatd_tag[i] and self.rewatd_tag[i] == 0 and self.cfg[i] and self.kill_boss_times[i] >= self.cfg[i][1].times then
            return 1
        end

        if self.rewatd_tag[i] and self.rewatd_tag[i] == 0 and self.cfg[i] and self.list[i] and self.list[i].times >= self.cfg[i][1].times then 
            return 1 
        end    
    end

    if ServerActivityWGData.Instance:GetGuildContentRemind1() == 1 then
        return 1 
    end

    return 0
end

function QunXiongZhuLuWGData:GetQXZLBQRemind(act_is_over)
    self.act_is_over = act_is_over
    if act_is_over then 
        return 0
    end

    for i = 1, #self.kill_boss_times do
        if self.rewatd_tag[i] and self.rewatd_tag[i] == 0 and self.cfg[i] and self.kill_boss_times[i] >= self.cfg[i][1].times then
            return 1
        end

        if self.rewatd_tag[i] and self.rewatd_tag[i] == 0 and self.cfg[i] and self.list[i] and self.list[i].times >= self.cfg[i][1].times then 
            return 1 
        end    
    end

    return 0
end

function QunXiongZhuLuWGData:GetQXZLKillCountByID(id)
    if self.kill_boss_times[id] then
        return self.kill_boss_times[id]
    end
    return 0
end

function QunXiongZhuLuWGData:GetPJInfo()
    return self.qunxiongzhulu_cfg
end

function QunXiongZhuLuWGData:GetPJReward(id)
    return self.cfg[id][1].reward, self.cfg[id][1].join_reward
end

function QunXiongZhuLuWGData:GetTips(index)
    return self.tips_cfg[1]["tipdes"..index]
end

function QunXiongZhuLuWGData:GetQXZLConfig()
    return self.herd_time[1]
end