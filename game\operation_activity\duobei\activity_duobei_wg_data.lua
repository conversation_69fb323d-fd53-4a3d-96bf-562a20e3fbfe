OperationActDuoBeiWGData = OperationActDuoBeiWGData or BaseClass()

function OperationActDuoBeiWGData:__init()
	if OperationActDuoBeiWGData.Instance then
		ErrorLog("[OperationActDuoBeiWGData] Attemp to create a singleton twice !")
	end
	OperationActDuoBeiWGData.Instance = self
	self:LoadConfig()

	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_DUOBEI_YOULI, {[1] = OPERATION_EVENT_TYPE.LEVEL},
		BindTool.Bind(self.GetActCanOpen, self))
end

function OperationActDuoBeiWGData:__delete()
	OperationActDuoBeiWGData.Instance = nil
end

function OperationActDuoBeiWGData:GetActivityOtherCfg()
	local activity_cfg = self.other_cfg
	local grade = self:GetInterfaceByServerDay()
	for k,v in pairs(activity_cfg) do
		if grade == v.grade then
			return v
		end 
	end
	return nil
end

function OperationActDuoBeiWGData:GetDuoBeiCfg()
	return self.duobei_cfg
end

function OperationActDuoBeiWGData:LoadConfig()
	self.other_cfg = ConfigManager.Instance:GetAutoConfig("operaact_duobei_cfg_auto").other
	self.duobei_cfg = ConfigManager.Instance:GetAutoConfig("operaact_duobei_cfg_auto").duobei
	self.param_cfg = ConfigManager.Instance:GetAutoConfig("operaact_duobei_cfg_auto").config_param
end

--是否开启
function OperationActDuoBeiWGData:GetActCanOpen()
	local vo = GameVoManager.Instance:GetMainRoleVo()
	local _, open_level = self:GetInterfaceByServerDay()
	if vo.level >= open_level then
		return true
	end
	return false
end

--根据服务器开服天数获得界面配置
function OperationActDuoBeiWGData:GetInterfaceByServerDay()
    local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_DUOBEI_YOULI)
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_DUOBEI_YOULI)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取是周几
	for k,v in pairs(self.param_cfg) do
		if open_day >= v.start_server_day and open_day < v.end_server_day and week == v.week_index then
			return v.grade, v.open_level
		end
	end
	
	return 0, 9999
end

function OperationActDuoBeiWGData:SetDuoBeiInfo(protocol)
	self.duobei_data = protocol
	MainuiWGCtrl.Instance:FlushDuoBei()
end

function OperationActDuoBeiWGData:GetDuoBeiInfo()
	if not self.duobei_data then
		return nil
	end

	local grade = self:GetInterfaceByServerDay()
	local cfg = self:GetDuoBeiCfg()
	local list = {}

	for k,v in pairs(cfg) do
		if v.grade == grade and v.day == self.duobei_data.day then
			local info = {}
			info.cfg = v
			info.cur_finish_num = 0
			if self.duobei_data.task_info[v.task_type] then
				info.cur_finish_num = self.duobei_data.task_info[v.task_type].cur_finish_num
			end
			table.insert(list, info)
		end
	end
	return list
end

--获取活动的结束时间
function OperationActDuoBeiWGData:GetActivityInValidTime()
	local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_DUOBEI_YOULI)
	if activity_data ~= nil then
		return activity_data.end_time, activity_data.start_time
	end
	return  0
end

--获取副本是否有多倍活动开启
function OperationActDuoBeiWGData:GetHasDuoBeiInCopy()
	for i=RATSDUOBEI_TASK.FANRENXIUXIAN, RATSDUOBEI_TASK.HAIDIFEIXU do
		if self:GetDuoBeiTimes(i) > 0 then
			return true
		end
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.WABAO) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANGUDIAN) > 0 then
		return true
	end
	
	return false
end

--获取魔王是否有boss开启
function OperationActDuoBeiWGData:GetHasDuoBeiInBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.SHIJIEMOWANG) > 0 then
		return true
	end

	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.MOWANGCHAOXUE) > 0 then
		return true
	end 

	return false 
end

--获取世界服是否有boss开启
function OperationActDuoBeiWGData:GetHasDuoBeiInWorldBoss()
	if self:GetDuoBeiTimes(RATSDUOBEI_TASK.HONGMENGSHENYU) > 0 or self:GetDuoBeiTimes(RATSDUOBEI_TASK.MANHUANSHENSHOU) > 0 then
		return true
	end
	return false 
end

function OperationActDuoBeiWGData:GetDuoBeiTimes(task_type)
	if self.duobei_data ~= nil and self:GetActivityState() then
		local cfg = self:GetDuoBeiCfg()
		if not cfg then
			return 0
		end

		local grade = self:GetInterfaceByServerDay()
		for k,v in pairs(cfg) do
			if v.grade == grade and v.day == self.duobei_data.day and v.task_type == task_type then
				if FunOpen.Instance:GetFunIsOpened(v.module_name) then
					return v.reward_mult
				end
			end
		end
	end
	return 0
end

function OperationActDuoBeiWGData:GetItemDuoBeiTimes(task_type , item_id)
	if self.duobei_data ~= nil and self:GetActivityState() then
		local cfg = self:GetDuoBeiCfg()
		if not cfg then
			return 0
		end
		
		for k,v in pairs(cfg) do
			if v.day == self.duobei_data.day and v.task_type == task_type then
				for k2,v2 in pairs(v.reward_item) do
					if v2.item_id == item_id then
						return v.reward_mult
					end
				end
			end
		end
	end
	return 0
end

--获取活动是否开启
function OperationActDuoBeiWGData:GetActivityState()
	return ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.OPERA_ACT_DUOBEI_YOULI)
end