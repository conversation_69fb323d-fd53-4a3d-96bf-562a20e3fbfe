MingWenBagView = MingWenBagView or BaseClass(SafeBaseView)

function MingWenBagView:__init()
	self:SetMaskBg(true, true)
    self.view_layer = UiLayer.Pop
    self.view_name = "MingWenBagView"
	self:LoadConfig()
	self.special_open_index = false
end

function MingWenBagView:__delete()

end

function MingWenBagView:ReleaseCallBack()
	if self.posy_bag_list then
		self.posy_bag_list:DeleteMe()
		self.posy_bag_list = nil
	end
end

-- 加载配置
function MingWenBagView:LoadConfig()
	local bundle_name = "uis/view/ming_wen_ui_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_commmon_second_panel", {sizeDelta = Vector2(814, 580)})
	self:AddViewResource(0,bundle_name,"layout_mingwen_bag")
end

function MingWenBagView:LoadCallBack(index, loaded_times)
	self.node_list.title_view_name.text.text = Language.ViewName[GuideModuleName.MingWenBagView]
	self.node_list.desc.text.text = Language.MingWenView.Desc
	self.posy_bag_list = AsyncListView.New(MingWenBagBigCell, self.node_list.posy_bag_list)
    self.node_list["posy_bag_get"].button:AddClickListener(BindTool.Bind(self.BagOpenGetRune, self))
    self.node_list["btn_xiangqian"].button:AddClickListener(BindTool.Bind(self.OpenMingwenXiangqian, self))
end

function MingWenBagView:ShowIndexCallBack(index)

end

function MingWenBagView:OpenMingwenXiangqian()
	ViewManager.Instance:Close(GuideModuleName.TreasureHunt)
    ViewManager.Instance:Open(GuideModuleName.MingWenView, TabIndex.ming_wen_xiang_qian)
    self:Close()
end

function MingWenBagView:BagOpenGetRune()
	local is_open, tip = FunOpen.Instance:GetFunIsOpenByViewNameAndNumberIndex(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_fuwen)
	if not is_open then
		SysMsgWGCtrl.Instance:ErrorRemind(tip)
	else
		FunOpen.Instance:OpenViewByName(GuideModuleName.TreasureHunt, TabIndex.treasurehunt_fuwen)
		self:Close()
	end
end

function MingWenBagView:OnFlush(param_t)
    local is_show_xiangqian = false
	local is_show_all = false
    for k, v in pairs(param_t) do
        if k == "all" then
            for k1, v1 in pairs(v) do
                if k1 == "is_from_xunbao" then
                    is_show_xiangqian = v1
					is_show_all = true
                end
            end
        end
    end

    self.node_list.btn_xiangqian:SetActive(is_show_xiangqian)
	self.now_slot_index = MingWenWGData.Instance:GetSelectPosyIndex()
	is_show_all = self.now_slot_index == -1 and true or is_show_all
	local data_list = MingWenWGData.Instance:GetPosyBagListData(self.now_slot_index, is_show_all)
	--self.special_open_index = false
	--if self.now_slot_index ~= 0 and IsEmptyTable(data_list) then
	--	data_list = MingWenWGData.Instance:GetPosyBagListData(0)
		--self.special_open_index = true
	--end
	local bag_num = MingWenWGData.Instance:GetMingWenBagNum()
	local is_empty = IsEmptyTable(data_list)
	self.node_list["posy_bag_blank"]:SetActive(is_empty)
	for k,v in pairs(data_list) do
		v.select_callback = BindTool.Bind(self.OnClickPosyCellCalllBack, self)
		--v.special_open_index = self.special_open_index
	end

	self.posy_bag_list:SetDataList(data_list)
	local bag_num,bag_max = MingWenWGData.Instance:GetMingWenBagNum()
	local color = bag_num < bag_max and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED
	--背包容量显示
	self.node_list["posy_bag_num"].text.text = Language.MingWenView.BagNum..ToColorStr(bag_num.."/"..bag_max,color)
end

function MingWenBagView:OnClickPosyCellCalllBack(cell,index)
	if self.now_slot_index >= 0 then
		local send_index = self.now_slot_index
		MingWenWGCtrl.Instance:SendMingWenOperaReq(PPSY_OPERA_TYPE.POSY_OPERATOR_TYPE_PUT_SLOT, send_index, index, MingWenWGData.Instance:GetIsSpecialBySlotIndex(self.now_slot_index))
		self:Close()
	end
end

MingWenBagBigCell = MingWenBagBigCell or BaseClass(BaseRender)

function MingWenBagBigCell:__init()

end

function MingWenBagBigCell:ReleaseCallBack()
	if self.rune_list then
		for k,v in pairs(self.rune_list) do
			v.item_cell:DeleteMe()
			v = nil
		end
		self.rune_list = nil
	end
end

function MingWenBagBigCell:LoadCallBack()
	self.rune_list = {}
	for i= 1,2 do
		self.rune_list[i] = {}
		self.rune_list[i].cell = self.node_list["rune_cell"..i]
		self.rune_list[i].cell.button:AddClickListener(BindTool.Bind(self.OnClickCell, self,i))
		self.rune_list[i].item_cell = ItemCell.New(self.node_list["item_pos"..i])
		self.rune_list[i].item_cell:SetIsShowTips(false)
        self.rune_list[i].item_cell:SetTipClickCallBack(BindTool.Bind(self.OnClickItem, self, i))
		--self.node_list["item_pos"..i].button:AddClickListener(BindTool.Bind(self.OnClickItem, self,i))
	end

	self:KillArrowTween()
	local tween_time = 0.8
    local node1 = self.node_list.arrow1
	if node1 then
        RectTransform.SetAnchoredPositionXY(node1.rect, node1.rect.anchoredPosition.x, -3)
        self.arrow_tweener1 = node1.rect:DOAnchorPosY(3, tween_time)
        self.arrow_tweener1:SetEase(DG.Tweening.Ease.InOutSine)
        self.arrow_tweener1:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end

	local node2 = self.node_list.arrow2
	if node2 then
        RectTransform.SetAnchoredPositionXY(node2.rect, node2.rect.anchoredPosition.x, -3)
        self.arrow_tweener2 = node2.rect:DOAnchorPosY(3, tween_time)
        self.arrow_tweener2:SetEase(DG.Tweening.Ease.InOutSine)
        self.arrow_tweener2:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
end

function MingWenBagBigCell:__delete()
    self:KillArrowTween()
end

function MingWenBagBigCell:KillArrowTween()
    if self.arrow_tweener1 then
        self.arrow_tweener1:Kill()
        self.arrow_tweener1 = nil
    end

	if self.arrow_tweener2 then
        self.arrow_tweener2:Kill()
        self.arrow_tweener2 = nil
    end
end

function MingWenBagBigCell:OnFlush()
	self.limite_type = {}
	for i = 1 , 2 do
		local item_id = self.data[i] and self.data[i].item_id or -1
		local base_cfg = MingWenWGData.Instance:GetMingWenBaseCfgByID(item_id)
		self.node_list["arrow"..i]:SetActive(false)

		if base_cfg then
			if base_cfg.type == 0 then
				self.node_list["locked"..i]:SetActive(false)
				self.node_list["rune_has_same"..i]:SetActive(false)
				self.node_list["typeconfit"..i]:SetActive(false)
				self.limite_type[i] = 0
			else
				local is_limite = MingWenWGData.Instance:GetPosyIsLimite(item_id)
				if is_limite then
					self.node_list["locked"..i]:SetActive(true)
					self.node_list["typeconfit"..i]:SetActive(false)
					self.node_list["rune_has_same"..i]:SetActive(false)
					self.limite_type[i] = 1
				else
					local now_slot_index = MingWenWGData.Instance:GetSelectPosyIndex()
					if now_slot_index >= 0 then
						local is_better = false
						is_better = MingWenWGData.Instance:GetItemIsBetterBySlotIndex(now_slot_index, item_id)
						self.node_list["arrow"..i]:SetActive(is_better)

						local is_open_slot = MingWenWGData.Instance:GetSlotIndexIsOpen(now_slot_index)
						if not is_open_slot then
							self.node_list["locked"..i]:SetActive(true)
							self.node_list["rune_has_same"..i]:SetActive(false)
							self.node_list["typeconfit"..i]:SetActive(false)
							self.limite_type[i] = 2
						else
							self.node_list["locked"..i]:SetActive(false)
							self.node_list["rune_has_same"..i]:SetActive(self.data[i].special_open_index or false)
							self.node_list["typeconfit"..i]:SetActive(false)
						end
					else
						self.node_list["locked"..i]:SetActive(false)
						self.node_list["rune_has_same"..i]:SetActive(false)
						self.node_list["typeconfit"..i]:SetActive(false)
					end
				end
			end

			self.rune_list[i].item_cell:SetData({item_id = item_id})

			self.rune_list[i].cell:SetActive(true)

			if base_cfg.type > 0 then
				if base_cfg.cost_type > 0 then
					self.node_list["rune_bag_name"..i].text.text = ToColorStr(base_cfg.name, ITEM_COLOR[base_cfg.quality])
				else
					self.node_list["rune_bag_name"..i].text.text = ToColorStr(base_cfg.name, ITEM_COLOR[base_cfg.quality])
				end
				local attr_type, attr_value, add_value = MingWenWGData.Instance:GetEquipPosyAttrByItemId(self.data[i].item_id)
				local name_list, is_pre = MingWenWGData.Instance:GetAttributeNameListByType(attr_type)
				local attr_str = ""

				for k,v in pairs(name_list) do
					if is_pre[k] then
						attr_str = attr_str .. name_list[k]..ToColorStr("  "..((tonumber(attr_value[k]) + (tonumber(self.data[i].level) -1) * tonumber(add_value[k])) / 100).."%",COLOR3B.DEFAULT_NUM).."\n"
					else
						attr_str = attr_str .. name_list[k]..ToColorStr("  "..tonumber(attr_value[k]) + (tonumber(self.data[i].level) -1) * tonumber(add_value[k]),COLOR3B.DEFAULT_NUM).."\n"
					end
				end
				self.node_list["rune_bag_attr"..i].text.text = attr_str
			else
				self.node_list["rune_bag_name"..i].text.text = ToColorStr(base_cfg.name, ITEM_COLOR[base_cfg.quality])
				self.node_list["rune_bag_attr"..i].text.text = ""
			end
		else
			self.rune_list[i].cell:SetActive(false)
		end
	end
end

function MingWenBagBigCell:OnClickCell(index)
	if self.data and self.data[index] then
		if self.limite_type[index] == 0 then
			return
		elseif self.limite_type[index] == 1 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.MingWenView.LockPosy)
		elseif self.limite_type[index] == 2 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.MingWenView.LockSlot)
		elseif self.limite_type[index] == 4 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.MingWenView.TypeConflict)
		elseif self.data[index].special_open_index then
			TipWGCtrl.Instance:ShowSystemMsg(Language.MingWenView.HaveSame)
		else
			self.data.select_callback(self,self.data[index].index)
		end
	end
end

function MingWenBagBigCell:OnClickItem(index)
	if self.data and self.data[index] then
		if MingWenWGData.Instance:CheckMingWenIsFromHeCheng(self.data[index].item_id) then
			local btn_callback_event = {}
			btn_callback_event[1] = {}
			btn_callback_event[1].btn_text = Language.MingWenView.ChaiJieText
			btn_callback_event[1].callback = function ()
				MingWenWGCtrl.Instance:SendMingWenOperaReq(PPSY_OPERA_TYPE.POSY_OPERATOR_TYPE_DE_COMPOSE_INDEX, self.data[index].index)
			end
			TipWGCtrl.Instance:OpenItem(self.data[index],ItemTip.FROM_MINGWEN_BAG,nil,nil,btn_callback_event)
		else
			TipWGCtrl.Instance:OpenItem(self.data[index],ItemTip.FROM_MINGWEN_BAG)
		end
	end
end