GuaJiSkillListView = GuaJiSkillListView or BaseClass(SafeBaseView)

function GuaJiSkillListView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(false,false)
	self.is_modal = true

	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/setting_ui_prefab", "layout_guajiskill")
end

function GuaJiSkillListView:ReleaseCallBack()
	if nil ~= self.guaji_skill_list_view then
		self.guaji_skill_list_view:DeleteMe()
		self.guaji_skill_list_view = nil
	end
end

function GuaJiSkillListView:LoadCallBack()
	self.guaji_skill_list_view = AsyncListView.New(SkillListItemRender, self.node_list["ph_skill_list"])
		self.node_list.layout_commmon_second_root.rect.sizeDelta = Vector2(916.8,615.5)
		self.node_list.title_view_name.text.text = Language.Setting.skill_title_view_name
	local data = SkillWGData.Instance:GetSkillListByType(1)
	self.guaji_skill_list_view:SetDataList(data)
	self.guaji_skill_list_view:JumpToTop()
end

function GuaJiSkillListView:ShowIndexCallBack(index)
	self:Flush()
end

function GuaJiSkillListView:OnFlush(param_t, index)
	local data = SkillWGData.Instance:GetSkillListByType(1)
	self.guaji_skill_list_view:SetDataList(data, 3)
end

function GuaJiSkillListView:CloseCallBack()
-- 	local guaji_set_flag = SettingWGCtrl.Instance.setting_view.guaji_set_flag
-- 	if 0 ~= #guaji_set_flag then
-- 		local guaji_data = bit:b2d(guaji_set_flag)
-- 		SettingWGCtrl.Instance:SendChangeHotkeyReq(HOT_KEY.GUAJI_SETTING, guaji_data)
-- 	end
	SettingWGCtrl.Instance:FlushSkill()
end


SkillListItemRender = SkillListItemRender or BaseClass(BaseRender)
function SkillListItemRender:__init()
end

function SkillListItemRender:__delete()

end

function SkillListItemRender:LoadCallBack()

	self.node_list["ph_skill_icon"]:SetActive(false)

	self.node_list["ph_skill_index"]:SetActive(false)
	XUI.AddClickEventListener(self.node_list["btn_nohint_checkbox"], BindTool.Bind1(self.ClickHookHandler, self))
end

function SkillListItemRender:ClickHookHandler()
	local img_hook = self.node_list["img_nohint_hook"]
	local flag = not img_hook:GetActive()
	img_hook:SetActive(flag)
	local guaji_set_flag = SettingWGCtrl.Instance.setting_view.guaji_set_flag
	guaji_set_flag[33 - self.index] = flag and 1 or 0
	local data = bit:b2d(guaji_set_flag)
	SettingWGData.Instance:SetSettingData1(SettingPanel3[self.index], flag, true)
	SettingWGData.Instance:SetDataByIndex(HOT_KEY.GUAJI_SETTING, data)
	GlobalEventSystem:Fire(SettingEventType.GUAJI_SETTING_CHANGE, SettingPanel3[self.index], flag)
end

function SkillListItemRender:OnFlush()
	if self.data == nil then return end
	-- local path = ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.skill_id))
	-- self.skill_icon:loadTexture(path, XUI.IS_PLIST)
	self.node_list["ph_skill_icon"].image:LoadSprite(ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.skill_id)))
	--	print("SkillListItemRender:OnFlush--- 222 self.data.skill_id : ",self.data.skill_id," path : ",ResPath.GetSkillIconById(SkillWGData.Instance:GetSkillIconId(self.data.skill_id)))
	self.node_list["ph_skill_icon"]:SetActive(true)
	self.node_list["text_inx"].text.text = self.index
	--self.node_list["ph_skill_index"].image:LoadSprite(ResPath.GetSetting("skill_index_" .. self.index))
	self.node_list["ph_skill_index"]:SetActive(true)
	-- self.skill_index:loadTexture(path_index, XUI.IS_PLIST)
	--local cfg = SkillWGData.Instance:Instance:GetSkillClientConfig(self.data.skill_id)
	local cfg = SkillWGData.Instance:GetSkillClientConfig(self.data.skill_id)
	local skill_info = SkillWGData.Instance:GetSkillInfoById(self.data.skill_id)

	local now_level = skill_info ~= nil and skill_info.level or 0
	local next_skillvo = SkillWGData.Instance:GetSkillConfigByIdLevel(self.data.skill_id, now_level + 1)	--下一个等级的技能

	local now_skillvo = SkillWGData.Instance:GetSkillConfigByIdLevel(self.data.skill_id, now_level)
	local desc = SkillWGData.Instance:RepleCfgContent(cfg.description, now_skillvo)

	local skill_name = self.data.skill_name .. "(" .. Language.Common.ZhuDong .. ")"
	local cur_skill_level = now_skillvo and now_skillvo.skill_level or 0

	local str = string.format(Language.Common.SkillTips, skill_name, cur_skill_level)

	self.node_list["rich_skill_name"].text.text = ToColorStr(str, COLOR3B.L_ORANGE)
	self.node_list["rich_cur_content"].text.text = ToColorStr(desc, COLOR3B.GLOD)

	local flag = SettingWGData.Instance:GetSettingData(SettingPanel3[self.index])
	self.node_list["img_nohint_hook"]:SetActive(flag)
end

function SkillListItemRender:CreateSelectEffect()

end
