HolyHeavenlyDomainCampView = HolyHeavenlyDomainCampView or BaseClass(SafeBaseView)

function HolyHeavenlyDomainCampView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true
	local bundle = "uis/view/holy_heavenly_domain_ui_prefab"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a2_common_panel")
	self:AddViewResource(0, bundle, "layout_holy_heavenly_domain_camp")
	self:AddViewResource(0, bundle, "layout_holy_heavenly_domain_top_panel")
end

function HolyHeavenlyDomainCampView:LoadCallBack()
	if not self.country_map_list then
		self.country_map_list = {}

		for i = 1, 16 do
			self.country_map_list[i] = HHDCampItemRender.New(self.node_list["country_" .. i])
		end
	end

	self.node_list.title_view_name.text.text = Language.HolyHeavenlyDomain.CampDetailViewName
end

function HolyHeavenlyDomainCampView:ReleaseCallBack()
	if self.country_map_list then
		for k, v in pairs(self.country_map_list) do
			v:DeleteMe()
		end

		self.country_map_list = nil
	end
end

function HolyHeavenlyDomainCampView:OnFlush()
	local data_list = HolyHeavenlyDomainWGData.Instance:GetCampCountryDataList()

	for i = 1, 16 do
		local server_id = data_list[i - 1]

		if server_id then
			local plat, server = LLStrToInt(server_id)
			if plat == 0 and server == 0 then
				self.node_list["country_" .. i]:CustomSetActive(false)
			else
				self.country_map_list[i]:SetData(server_id)
				self.node_list["country_" .. i]:CustomSetActive(true)
			end
		else
			self.node_list["country_" .. i]:CustomSetActive(false)
		end
	end
end

HHDCampItemRender = HHDCampItemRender or BaseClass(BaseRender)

function HHDCampItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.view, BindTool.Bind1(self.ClickHandler, self))
end

function HHDCampItemRender:OnFlush()
	if nil == self.data then
		return
	end

	local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByServerId(self.data)
	self.node_list.btn_text.text.text = camp_cfg and camp_cfg.camp_name or ""
	local my_camp_seq = HolyHeavenlyDomainWGData.Instance:GetMyCampSeq()
	self.node_list.flag_my_camp:CustomSetActive(my_camp_seq == camp_cfg.seq)
end

function HHDCampItemRender:ClickHandler()
	if nil == self.data then
		return
	end

	local camp_cfg = HolyHeavenlyDomainWGData.Instance:GetCampCfgByServerId(self.data)
	if not IsEmptyTable(camp_cfg) then
		HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.CAMP_INFO, camp_cfg.seq)
		HolyHeavenlyDomainWGCtrl.Instance:OpenCampDetailsView(camp_cfg)
	end
end
