require("game/festival_activity/festival_fireworks/festival_fireworks_wg_data")
require("game/festival_activity/festival_fireworks/festival_fireworks_view")
require("game/festival_activity/festival_fireworks/festival_fireworks_record_view")
require("game/festival_activity/festival_fireworks/festival_fireworks_reward")

FestivalFireworksWGCtrl = FestivalFireworksWGCtrl or BaseClass(BaseWGCtrl)

function FestivalFireworksWGCtrl:__init()
	if FestivalFireworksWGCtrl.Instance then
		ErrorLog("[FestivalFireworksWGCtrl] Attemp to create a singleton twice !")
	end
	FestivalFireworksWGCtrl.Instance = self
	self:RegisterAllProtocols()

	self.data = FestivalFireworksWGData.New()
	self.record_view = FestivalFireworksRecord.New()
    self.reward_view = FestivalFireworksReward.New()

	self.act_change = BindTool.Bind(self.ActChange, self)
    ActivityWGData.Instance:NotifyActChangeCallback(self.act_change)
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.MainuiOpenCreate, self))
end

function FestivalFireworksWGCtrl:__delete()
	self.data:DeleteMe()
	self.data = nil

    self.record_view:DeleteMe()
	self.record_view = nil

    self.reward_view:DeleteMe()
	self.reward_view = nil

	ActivityWGData.Instance:UnNotifyActChangeCallback(self.act_change)
	self.act_change = nil

    if self.alert then
        self.alert:DeleteMe()
        self.alert = nil
    end

    self.main_ui_load = nil
	FestivalFireworksWGCtrl.Instance = nil
end

function FestivalFireworksWGCtrl:RegisterAllProtocols()
    self:RegisterProtocol(SCFAYanHuaShengDianInfo,'OnSCFAYanHuaShengDianInfo')
    self:RegisterProtocol(SCFAYanHuaShengDianRecordListInfo,'OnSCFAYanHuaShengDianRecordListInfo')
    self:RegisterProtocol(SCFAYanHuaShengDianDrawRewardInfo,'OnSCFAYanHuaShengDianDrawRewardInfo')
    self:RegisterProtocol(SCRAYanHuaShengDian2BaoDiRewardDrawInfo,'OnSCRAYanHuaShengDian2BaoDiRewardDrawInfo')
end

function FestivalFireworksWGCtrl:OnSCRAYanHuaShengDian2BaoDiRewardDrawInfo(protocol)
    self.data:SaveDrawInfo(protocol)
    ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2270)
end

function FestivalFireworksWGCtrl:MainuiOpenCreate()
    self.main_ui_load = true
    if self.data:GetActIsOpen() then
        FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
    end
end

--活动信息
function FestivalFireworksWGCtrl:OnSCFAYanHuaShengDianInfo(protocol)
	-- print_error("info",protocol)
	self.data:SetInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2270)
    self.reward_view:Flush("ignore")
    RemindManager.Instance:Fire(RemindName.Festival_Fireworks)
end

--日志
function FestivalFireworksWGCtrl:OnSCFAYanHuaShengDianRecordListInfo(protocol)
	-- print_error("record", protocol.record_count, protocol)
	self.data:SetRecord(protocol.record_list)
    ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2270, "all", {[2] = "record"})
	self.record_view:Flush()
end

--奖励
function FestivalFireworksWGCtrl:OnSCFAYanHuaShengDianDrawRewardInfo(protocol)
    -- print_error("reward", protocol.reward_count, protocol)
    ViewManager.Instance:FlushView(GuideModuleName.FestivalActivityView, TabIndex.festival_activity_2270, "all", {[2] = "play_ani"})
    local delay_time = self.data:GetDelayTime()
    GlobalTimerQuest:AddDelayTimer(function ()
        local data_list = self.data:CalDrawRewardList(protocol)
        self:OpenRewardView(data_list)
    end, delay_time)
end

--请求
function FestivalFireworksWGCtrl:SendReq(opera_type, param1, param2)
	local param_t = {
		rand_activity_type = ACTIVITY_TYPE.FESTIVAL_ACT_YANHUA_SHENGDIAN_2,
		opera_type = opera_type,
        param_1 = param1,
        param_2 = param2,
	}
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq(param_t)
end

--奖励弹窗
function FestivalFireworksWGCtrl:OpenRewardView(data)
    self.reward_view:SetData(data)
    self.reward_view:Open()
end

--监听活动状态改变，清除缓存标记，添加道具监听，
function FestivalFireworksWGCtrl:ActChange(act_type, status)
    if act_type ~= ACTIVITY_TYPE.FESTIVAL_ACT_YANHUA_SHENGDIAN_2 then
        return
    end

	self.data:ClearCache()
    if status == ACTIVITY_STATUS.OPEN then
        if self.main_ui_load then
            FestivalFireworksWGCtrl.Instance:SendReq(CSA_YANHUA_OP.INFO)
        end
		--self:ListeneItem(true)--添加道具监听
    elseif status == ACTIVITY_STATUS.CLOSE then
		--self:ListeneItem(false)
        self.is_fire_map = false
    end
end

--物品监听
function FestivalFireworksWGCtrl:ListeneItem(status)
	if status then
		if not self.item_data_change then
			self.item_data_change = BindTool.Bind(self.OnFwsItemChange, self)
		end
    	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)
	else
		if self.item_data_change then
			ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change)
			self.item_data_change = nil
		end
	end
end

--物品改变返回
function FestivalFireworksWGCtrl:OnFwsItemChange(item_id)
	local check_list = FestivalFireworksWGData.Instance:GetItemDataChangeList()
    for i, v in pairs(check_list) do
        if v == item_id then
            RemindManager.Instance:Fire(RemindName.Festival_Fireworks)
            return
        end
    end
end

--去放烟花的地方
function FestivalFireworksWGCtrl:GotoFire(callback, range)
	callback()
end

--打开记录
function FestivalFireworksWGCtrl:OpenRecord()
    self.record_view:Open()
end

--使用道具并弹窗
function FestivalFireworksWGCtrl:ClickUse(index, func)
     --数量检测
    local cfg = FestivalFireworksWGData.Instance:GetConsumeCfg()
    local num = ItemWGData.Instance:GetItemNumInBagById(cfg[index].yanhua_item.item_id)

    --不足弹窗
    if num < cfg[index].yanhua_item.num then
        if not self.alert then
            self.alert = Alert.New()
        end
        self.alert:ClearCheckHook()
        self.alert:SetShowCheckBox(true, "festival_fireworks")
        self.alert:SetCheckBoxDefaultSelect(false)
        local item_cfg = ItemWGData.Instance:GetItemConfig(cfg[index].yanhua_item.item_id)
        local name = ToColorStr(item_cfg.name, ITEM_COLOR[item_cfg.color])
        local cost = cfg[1].consume_count * (cfg[index].yanhua_item.num - num)
        local str = string.format(Language.MergeFireworks.CostStr, name, cost)

        self.alert:SetLableString(str)
        self.alert:SetOkFunc(func)
        self.alert:Open()
    else
        --使用
        func()
    end
end


function FestivalFireworksWGCtrl:GetIsFireMap()
    return self.is_fire_map
end