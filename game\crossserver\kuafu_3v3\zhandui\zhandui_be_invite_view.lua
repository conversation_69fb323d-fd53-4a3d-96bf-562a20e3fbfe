--战队被邀请界面
ZhanDuiBeInviteView = ZhanDuiBeInviteView or BaseClass(SafeBaseView)

function ZhanDuiBeInviteView:__init()
    self:SetMaskBg()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel",
        { vector2 = Vector2(10, 3), sizeDelta = Vector2(854, 486) })
    self:AddViewResource(0, "uis/view/zhandui_ui_prefab", "layout_zhandui_be_invite")
end

function ZhanDuiBeInviteView:ReleaseCallBack()
    if self.be_invite_list then
        self.be_invite_list:DeleteMe()
        self.be_invite_list = nil
    end
    if self.zhandui_new_beinvite_event then
        GlobalEventSystem:UnBind(self.zhandui_new_beinvite_event)
        self.zhandui_new_beinvite_event = nil
    end
    if self.zhan_dui_info_change_event then
        GlobalEventSystem:UnBind(self.zhan_dui_info_change_event)
        self.zhan_dui_info_change_event = nil
    end
end

function ZhanDuiBeInviteView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_oncerefuse, BindTool.Bind(self.OnClickOnceRefuse, self))
    XUI.AddClickEventListener(self.node_list.btn_today_refuse_check, BindTool.Bind1(self.OnClickTodayCheck, self))
    self.be_invite_list = AsyncListView.New(ZhanDuiBeInviteRender, self.node_list.be_invite_list)
    --self.be_invite_list:SetSelectCallBack(BindTool.Bind(self.OnClickBeInviteRender, self))
    self.be_invite_list:SetDefaultSelectIndex(1)

    self.zhandui_new_beinvite_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Add_BeInvite,
        BindTool.Bind(self.OnNewBeInviteCallBack, self))
    self.zhan_dui_info_change_event = GlobalEventSystem:Bind(OtherEventType.ZhanDui_Info_Change,
        BindTool.Bind(self.OnZhanDuiInfoChange, self)) --战队信息改变
end

function ZhanDuiBeInviteView:ShowIndexCallBack()
    if self.node_list.btn_today_refuse_select then
        self.node_list.btn_today_refuse_select:SetActive(false)
    end
    self:Flush()
end

function ZhanDuiBeInviteView:OnNewBeInviteCallBack()
    local data_list = ZhanDuiWGData.Instance:GetBeInviteList()
    self.be_invite_list:SetDataList(data_list)
    self.old_click_index = nil
end

function ZhanDuiBeInviteView:OnFlush()
    local data_list = ZhanDuiWGData.Instance:GetBeInviteList()
    self.be_invite_list:SetDataList(data_list)
    self.old_click_index = nil
end

function ZhanDuiBeInviteView:GetBeInviteTodayCheckActive()
    if not self.node_list.btn_today_refuse_select then
        return false
    end
    return self.node_list.btn_today_refuse_select.gameObject.activeSelf
end

-- function ZhanDuiBeInviteView:OnClickBeInviteRender(item, index)
--     local data = item:GetData()
--     if not data then
--         --print_error("物品数据为空 >>>>>> ", index)
--         return
--     end
--     if self.old_click_index and self.old_click_index == index then
--         --print_error("点击相同格子 ::", index)
--         return
--     end
--     self.select_data = data
--     self.old_click_index = index
--     if self.node_list.btn_today_refuse_select then
--         self.node_list.btn_today_refuse_select:SetActive(false)
--     end
--     --self.node_list.top_invite_text.text.text = string.format(Language.KuafuPVP.ZhanDuiBeInviteTopTips, data.captain_name, data.name) --队长名、战队名
--     local grade_cfg, next_score = KF3V3WGData.Instance:GetDuanWeiCfg(data.score)
-- 	self.node_list.duanwei_value.text.text = grade_cfg.name
-- 	ChangeToQualityText(self.node_list.duanwei_value.text, RankGradeEnum[grade_cfg.rank_id])
--     local b,a = ResPath.GetKF3V3("duanwei_icon".. grade_cfg.grade)
--     self.node_list.duanwei_icon.image:LoadSprite(b, a)

--     self.node_list.leader_name.text.text = data.captain_name
--     self.node_list.capability_value.text.text = data.capability
-- end

--一键拒绝
function ZhanDuiBeInviteView:OnClickOnceRefuse()
    local data_list = ZhanDuiWGData.Instance:GetBeInviteList()
    if data_list then
        local today_refuse_flag = ZhanDuiWGCtrl.Instance:GetBeInviteTodayCheckActive() and 1 or 0
        for i, v in ipairs(data_list) do
            ZhanDuiWGCtrl.Instance:SendBeInviteAckZhanDui(v.zhandui_id, 0, today_refuse_flag)
        end
    end
end

--今日拒绝
function ZhanDuiBeInviteView:OnClickTodayCheck()
    local is_select = self.node_list.btn_today_refuse_select.gameObject.activeSelf
    self.node_list.btn_today_refuse_select:SetActive(not is_select)
end

function ZhanDuiBeInviteView:OnZhanDuiInfoChange(notify_reason)
    --print_error("战队信息改变", notify_reason)
    if notify_reason == NotifyZhanduiInfoReason.JoinZhandui then
        self:Close()
        KF3V3WGCtrl.Instance:Open3V3View(TabIndex.kf_pvp_zhandui_info)
    end
end

------------------------------------------------------------------
ZhanDuiBeInviteRender = ZhanDuiBeInviteRender or BaseClass(BaseRender)

function ZhanDuiBeInviteRender:__init()
    XUI.AddClickEventListener(self.node_list.btn_agree, BindTool.Bind(self.OnClickAgree, self))
    XUI.AddClickEventListener(self.node_list.btn_refuse, BindTool.Bind(self.OnClickRefuse, self))
    self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function ZhanDuiBeInviteRender:__delete()
    if self.head_cell then
        self.head_cell:DeleteMe()
        self.head_cell = nil
    end
end

function ZhanDuiBeInviteRender:OnFlush()
    if not self.data then return end
    --self.node_list.name.text.text = self.data.name
    self.node_list.capability_value.text.text = self.data.capability
    self.node_list.leader_name.text.text = self.data.captain_name
    local grade_cfg, next_score = KF3V3WGData.Instance:GetDuanWeiCfg(self.data.score)
    self.node_list.duanwei_value.text.text = grade_cfg.tier_name
    self.node_list.duanwei_icon.image:LoadSprite(ResPath.GetCommon("a3_jjc_hz" .. grade_cfg.grade))
    --ChangeToQualityText(self.node_list.duanwei_value.text, RankGradeEnum[grade_cfg.rank_id])
    local flush_fun = function(protocol)
        if not protocol or not self.node_list or not self.node_list.vip_level or not self.head_cell then
            return
        end
        if protocol.vip_level then -- 设置VIP等级
            self.node_list.vip_level:SetActive(protocol.vip_level >= 1)
            local is_hide_vip = SettingWGData.Instance:IsHideRoleVipLv(protocol.shield_vip_flag)
            self.node_list.vip_level.text.text = is_hide_vip and "V" or "V" .. protocol.vip_level
        end

        local data = { role_id = protocol.role_id, prof = protocol.prof, sex = protocol.sex, fashion_photoframe = 0 }
        self.head_cell:SetData(data)
    end
    BrowseWGCtrl.Instance:BrowRoelInfo(self.data.captain_uid, flush_fun, RoleWGData.Instance.role_vo.plat_type)
end

function ZhanDuiBeInviteRender:OnSelectChange(is_select)
    -- self.node_list.normal:SetActive(not is_select)
    -- self.node_list.hl:SetActive(true)
end

--同意
function ZhanDuiBeInviteRender:OnClickAgree()
    if not self.data then return end
    ZhanDuiWGCtrl.Instance:SendBeInviteAckZhanDui(self.data.zhandui_id, 1)
end

--拒绝
function ZhanDuiBeInviteRender:OnClickRefuse()
    if not self.data then return end
    local today_refuse_flag = ZhanDuiWGCtrl.Instance:GetBeInviteTodayCheckActive() and 1 or 0
    -- print_error("拒绝该玩家邀请入队，今日拒绝:", today_refuse_flag)
    ZhanDuiWGCtrl.Instance:SendBeInviteAckZhanDui(self.data.zhandui_id, 0, today_refuse_flag)
end
