
XianLingGuZhenRecord = XianLingGuZhenRecord or BaseClass(SafeBaseView)
function XianLingGuZhenRecord:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/xianling_guzhen_prefab", "layout_xianling_record")
end

function XianLingGuZhenRecord:__delete()

end

function XianLingGuZhenRecord:ReleaseCallBack()

	-- if self.record_list then
	-- 	self.record_list:DeleteMe()
	-- 	self.record_list = nil
	-- end

end

function XianLingGuZhenRecord:OpenCallBack()
	--XianLingGuZhenWGCtrl.Instance:SendXianLingRollReq(NEW_CS_RAXIANLINGZHEN_OPERA_TYPE.CS_RA_XIANLINGZHEN_OPERA_TYPE_RECORD)
end

function XianLingGuZhenRecord:LoadCallBack()
	self:SetSecondView(Vector2(833,480))
	self.node_list.title_view_name.text.text = Language.XianLingGuZhen.RecordPanelName
	--self.record_list = AsyncListView.New(XianLingRecordCell, self.node_list.record_list)
end



function XianLingGuZhenRecord:OnClickClose()
	self:Close()
end



function XianLingGuZhenRecord:OnFlush()
	-- local all_data = XianLingGuZhenWGData.Instance:GetWorldRewardInfoData()

	-- self.record_list:SetDataList(all_data)
	-- if IsEmptyTable(all_data) then
	-- 	self.node_list["empty_tip"]:SetActive(true)
	-- else
	-- 	self.node_list["empty_tip"]:SetActive(false)
	-- end
	-- if not IsEmptyTable(all_data) then
	-- 	local sort_data = {}
	-- 	local is_rare_sort, cfg
	-- 	--策划需求:珍稀大奖置顶
	-- 	for i, v in ipairs(all_data) do
	-- 		sort_data[i] = v
	-- 		cfg = XianLingGuZhenWGData.Instance:GetCfgByItemId(v.item_id)
	-- 		is_rare_sort = 0
	-- 		if cfg and cfg.is_rare_sort == 1 then
	-- 			is_rare_sort = 1
	-- 		end
	-- 		sort_data[i].is_rare_sort = is_rare_sort
	-- 	end
	-- 	table.sort(sort_data, SortTools.KeyUpperSorters("is_rare_sort", "timestamp"))
	-- 	self.record_list:SetDataList(sort_data)
	-- end
end
