LingYuActiveView = LingYuActiveView or BaseClass(SafeBaseView)
function LingYuActiveView:__init(...)
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/lingyu_active_ui_prefab", "layout_lingyu_active")
	self.is_playing = false
	self.nowGoldValue = 0
	self.nextGoldValue = 0
end

function LingYuActiveView:OpenCallBack()
	LingYuActiveWGCtrl.Instance:CSReqLingYuPoolInfo()
end

function LingYuActiveView:CloseCallBack()

end

function LingYuActiveView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_goToRecharge, BindTool.Bind(self.OnClickGoToRecharge, self))
end

function LingYuActiveView:ReleaseCallBack()
	self:CleanTimer1()
end

function LingYuActiveView:OnFlush(param_t, index)
	self:FlushView()
end

function LingYuActiveView:FlushView()
	self.node_list.text_desc.text.text = string.format(Language.LingYuActiveText.ActiveDesc)
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local endTime = LingYuActiveWGData.Instance:GetActiveEndTime()
	local remainTime = math.ceil(endTime - server_time)
	self:SetRemainTimeShow(remainTime)
	if remainTime > 0 then
		self:CleanTimer1()
		CountDownManager.Instance:AddCountDown("LingYuActiveTimer",
			-- 回调方法
			BindTool.Bind(self.FinalUpdateTimeCallBack, self),
			-- 倒计时完成回调方法
			BindTool.Bind(self.OnCompleteFunc, self),
			nil,
			remainTime,
			1
		)
	else
		self:OnCompleteFunc()
	end

	local playerRechargeValue = LingYuActiveWGData.Instance:GetPlayerRechargeValue()
	self.node_list.text_nowRecharge.text.text = string.format(Language.LingYuActiveText.PlayerRechargeStr,
		playerRechargeValue)

	self.nextGoldValue = LingYuActiveWGData.Instance:GetTotalGoldValue()
	if not self.is_playing then
		self:PlayTxtEffect()
	end
end

function LingYuActiveView:FinalUpdateTimeCallBack(now_time, total_time)
	local timeOff = math.ceil(total_time - now_time)
	self:SetRemainTimeShow(timeOff)
end

function LingYuActiveView:OnCompleteFunc()
	self:SetRemainTimeShow(0)
end

function LingYuActiveView:SetRemainTimeShow(timeValue)
	local timeStr = Language.LingYuActiveText.ActiveRemainEndTime
	if timeValue > 0 then
		timeStr = string.format(Language.LingYuActiveText.ActiveRemainTime, TimeUtil.FormatSecondDHM8(timeValue))
	end
	self.node_list.text_remainTime.text.text = timeStr
end

function LingYuActiveView:PlayTxtEffect()
	self.is_playing = true
	self.nowGoldValue = tonumber(self.node_list.text_totalGold.text.text)
	self.node_list.text_totalGold.text:DoNumberTo(self.nowGoldValue, self.nextGoldValue, 0.3, function()
		self.is_playing = false
		if self.nowGoldValue ~= self.nextGoldValue then
			self:PlayTxtEffect()
		end
	end)
end

-- 清除倒计时器1
function LingYuActiveView:CleanTimer1()
	if CountDownManager.Instance:HasCountDown("LingYuActiveTimer") then
		CountDownManager.Instance:RemoveCountDown("LingYuActiveTimer")
	end
end

function LingYuActiveView:OnClickGoToRecharge()
	RechargeWGCtrl.Instance:Open()
end
