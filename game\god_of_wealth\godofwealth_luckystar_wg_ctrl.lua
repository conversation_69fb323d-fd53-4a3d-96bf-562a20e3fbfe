GodOfWealthWGCtrl = GodOfWealthWGCtrl or BaseClass(BaseWGCtrl)

function GodOfWealthWGCtrl:InitLuckyDrawCtrl()

	self:RegisterProtocol(CSRoleFuXingGaoZhaoReq)
    self:RegisterProtocol(SCRoleFuXingGaoZhaoDrawResult, "OnSCRoleFuXingGaoZhaoDrawResult")
    self:RegisterProtocol(SCRoleFuXingGaoZhaoInfo, "OnSCRoleFuXingGaoZhaoInfo")
	self:RegisterProtocol(SCRoleFuXingGaoZhaoWorldRecord, "OnSCRoleFuXingGaoZhaoWorldRecord")

    self.item_data_change_callback = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change_callback)
end

function GodOfWealthWGCtrl:DeleteLuckyDrawCtrl()
	if self.item_data_change_callback then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_change_callback)
		self.item_data_change_callback = nil
	end
end

function GodOfWealthWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD or
	(change_reason == GameEnum.DATALIST_CHANGE_REASON_UPDATE and old_num ~= nil and new_num ~= nil and new_num > old_num) then
		self:OnLuckystarItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	end
end

function GodOfWealthWGCtrl:OnLuckystarItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
	if self.data:IsLuckyStarItem(change_item_id) then
		RemindManager.Instance:Fire(RemindName.Wealth_god_lucky_wealth)
        if self.view:IsOpen() then
            self.view:Flush()
        end
        
        --self:FlushLuckyStarView()
	 end
end

function GodOfWealthWGCtrl:SendRoleFuXingGaoZhaoReq(operate_type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleFuXingGaoZhaoReq)
  	protocol.operate_type = operate_type or 0
  	protocol.param1 = param1 or 0
	protocol:EncodeAndSend()
end

function GodOfWealthWGCtrl:OnSCRoleFuXingGaoZhaoDrawResult(protocol)
	self.data:SetRoleFuXingGaoZhaoDrawResult(protocol)

    --TipWGCtrl.Instance:ShowGetReward(nil, protocol.result_item_list, nil, nil, nil)
	local best_ids = {}
	local normal_ids = {}
	for i, v in ipairs(protocol.result_item_list) do
		if self.data:GetIsShowItem(v.item_id) then
			table.insert(best_ids, v.item_id)
		else
			table.insert(normal_ids, v)
		end
	end
	local other_info = {}
	other_info.best_data = { item_ids = best_ids }
	TipWGCtrl.Instance:ShowGetValueReward(normal_ids, nil, other_info)

	self.data:SetLuckyDrawState(true)

	-- ReDelayCall(self, function ()
	-- 	TipWGCtrl.Instance:ShowGetReward(nil, protocol.result_item_list, nil, nil, nil)
	-- 	self.data:SetLuckyDrawState(true)
	-- end, 1.5, "RoleFuXingGaoZhaoDrawResult")
end

function GodOfWealthWGCtrl:OnSCRoleFuXingGaoZhaoInfo(protocol)
	self.data:SetRoleFuXingGaoZhaoInfo(protocol)
	RemindManager.Instance:Fire(RemindName.Wealth_god_lucky_wealth)
	--self:FlushLuckyStarView()

	if self.view:IsOpen() then
        self.view:Flush()
    end
end

function GodOfWealthWGCtrl:OnSCRoleFuXingGaoZhaoWorldRecord(protocol)
    self.data:SetRoleFuXingGaoZhaoWorldRecord(protocol)
	if self.view:IsOpen() then
        self.view:Flush()
    end
	--self:FlushLuckyStarView()
end