﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class RuntimeEnvironmentParamModifierWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(RuntimeEnvironmentParamModifier), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("ChangeEnvironmentParam", ChangeEnvironmentParam);
		<PERSON><PERSON>Function("RecoverEnvironmentParam", RecoverEnvironmentParam);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("IsChangeLightmapTexture", get_IsChangeLightmapTexture, set_IsChangeLightmapTexture);
		<PERSON><PERSON>("ChangeLightmapTexture", get_ChangeLightmapTexture, set_ChangeLightmapTexture);
		<PERSON><PERSON>("IsChangeFogParam", get_IsChangeFogParam, set_IsChangeFogParam);
		<PERSON><PERSON>("ChangeFogColor", get_ChangeFogColor, set_ChangeFogColor);
		<PERSON><PERSON>("ChangeFogStartDis", get_ChangeFogStartDis, set_ChangeFogStartDis);
		L.RegVar("ChangeFogEndDis", get_ChangeFogEndDis, set_ChangeFogEndDis);
		L.RegVar("IsChangeHeroLight", get_IsChangeHeroLight, set_IsChangeHeroLight);
		L.RegVar("heroLight", get_heroLight, set_heroLight);
		L.RegVar("heroLightColor", get_heroLightColor, set_heroLightColor);
		L.RegVar("IsChangeSkyMaterial", get_IsChangeSkyMaterial, set_IsChangeSkyMaterial);
		L.RegVar("ChangeSkyMaterial", get_ChangeSkyMaterial, set_ChangeSkyMaterial);
		L.RegVar("IsChangeReflectionProbe", get_IsChangeReflectionProbe, set_IsChangeReflectionProbe);
		L.RegVar("ChangeReflectionProbe", get_ChangeReflectionProbe, set_ChangeReflectionProbe);
		L.RegVar("ChangeCubeMap", get_ChangeCubeMap, set_ChangeCubeMap);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ChangeEnvironmentParam(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)ToLua.CheckObject<RuntimeEnvironmentParamModifier>(L, 1);
			obj.ChangeEnvironmentParam();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RecoverEnvironmentParam(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)ToLua.CheckObject<RuntimeEnvironmentParamModifier>(L, 1);
			obj.RecoverEnvironmentParam();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsChangeLightmapTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool ret = obj.IsChangeLightmapTexture;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeLightmapTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ChangeLightmapTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Texture2D[] ret = obj.ChangeLightmapTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeLightmapTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsChangeFogParam(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool ret = obj.IsChangeFogParam;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeFogParam on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ChangeFogColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Color ret = obj.ChangeFogColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeFogColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ChangeFogStartDis(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			float ret = obj.ChangeFogStartDis;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeFogStartDis on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ChangeFogEndDis(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			float ret = obj.ChangeFogEndDis;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeFogEndDis on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsChangeHeroLight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool ret = obj.IsChangeHeroLight;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeHeroLight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_heroLight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Light ret = obj.heroLight;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index heroLight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_heroLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Color ret = obj.heroLightColor;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index heroLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsChangeSkyMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool ret = obj.IsChangeSkyMaterial;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeSkyMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ChangeSkyMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Material ret = obj.ChangeSkyMaterial;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeSkyMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsChangeReflectionProbe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool ret = obj.IsChangeReflectionProbe;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeReflectionProbe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ChangeReflectionProbe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.ReflectionProbe ret = obj.ChangeReflectionProbe;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeReflectionProbe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ChangeCubeMap(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Texture ret = obj.ChangeCubeMap;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeCubeMap on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsChangeLightmapTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsChangeLightmapTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeLightmapTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ChangeLightmapTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Texture2D[] arg0 = ToLua.CheckObjectArray<UnityEngine.Texture2D>(L, 2);
			obj.ChangeLightmapTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeLightmapTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsChangeFogParam(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsChangeFogParam = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeFogParam on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ChangeFogColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.ChangeFogColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeFogColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ChangeFogStartDis(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ChangeFogStartDis = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeFogStartDis on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ChangeFogEndDis(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.ChangeFogEndDis = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeFogEndDis on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsChangeHeroLight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsChangeHeroLight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeHeroLight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_heroLight(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Light arg0 = (UnityEngine.Light)ToLua.CheckObject(L, 2, typeof(UnityEngine.Light));
			obj.heroLight = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index heroLight on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_heroLightColor(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Color arg0 = ToLua.ToColor(L, 2);
			obj.heroLightColor = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index heroLightColor on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsChangeSkyMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsChangeSkyMaterial = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeSkyMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ChangeSkyMaterial(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Material arg0 = (UnityEngine.Material)ToLua.CheckObject<UnityEngine.Material>(L, 2);
			obj.ChangeSkyMaterial = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeSkyMaterial on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsChangeReflectionProbe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsChangeReflectionProbe = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsChangeReflectionProbe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ChangeReflectionProbe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.ReflectionProbe arg0 = (UnityEngine.ReflectionProbe)ToLua.CheckObject(L, 2, typeof(UnityEngine.ReflectionProbe));
			obj.ChangeReflectionProbe = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeReflectionProbe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ChangeCubeMap(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			RuntimeEnvironmentParamModifier obj = (RuntimeEnvironmentParamModifier)o;
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			obj.ChangeCubeMap = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ChangeCubeMap on a nil value");
		}
	}
}

