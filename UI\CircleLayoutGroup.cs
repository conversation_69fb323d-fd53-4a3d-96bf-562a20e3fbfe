﻿using Sirenix.OdinInspector;
using UnityEngine;
using UnityEngine.UI;


[AddComponentMenu("Layout/Circle Layout Group", 150)]
public class CircleLayoutGroup : LayoutGroup
{
    public enum LayoutMode
    {
        [LabelText("平均分布")]
        Average,
        [LabelText("扇形分布")]
        Sector,
    }

    [LabelText("分组编号")]
    public LayoutMode TypeMode = LayoutMode.Average;

    [LabelText("半径")]
    public float Radius = 0;

    [LabelText("起始角度")]
    [Range(-360, 360)]
    public float InitAngle = 0;

    [LabelText("是否固定角度")]
    [ShowIf("@this.TypeMode == LayoutMode.Sector")]
    public bool KeepAngle = false;

    [LabelText("固定角度值")]
    [ShowIf("@this.KeepAngle == true && this.TypeMode == LayoutMode.Sector")]
    [Range(0, 360)]
    public float KeepKeepAngleVal = 0f;

    [LabelText("扇形分布范围")]
    [ShowIf("@this.KeepAngle == false && this.TypeMode == LayoutMode.Sector")]
    [Range(0, 360)]
    public float sectorAngle = 0;

    [LabelText("扇形反转")]
    [ShowIf("@this.TypeMode == LayoutMode.Sector")]
    public bool SectorClockwise = true;

    [LabelText("锁定子物体角度")]
    public bool LockSubAngle = true;

    [LabelText("子物体初始角度")]
    [Range(0, 360)]
    public float SubAngle = 0f;

    [LabelText("内部不旋转")]
    public bool NotRotationInAll = true;


    protected override void OnEnable()
    {
        base.OnEnable();
        CalculateRadial();
    }

    public override void CalculateLayoutInputVertical()
    {
        CalculateRadial();
    }

    public override void SetLayoutHorizontal()
    {
        CalculateRadial();
    }

    public override void SetLayoutVertical()
    {
        CalculateRadial();
    }

#if UNITY_EDITOR
    protected override void OnValidate()
    {
        base.OnValidate();
        CalculateRadial();
    }
#endif
    protected void CalculateRadial()
    {
        this.m_Tracker.Clear();
        if (transform.childCount == 0 || this.rectChildren.Count <= 0)
            return;
        if (TypeMode == LayoutMode.Average)
        {
            AverageDispersion();
        }
        else
        {
            SectorDispersion();
        }
    }

    protected void AverageDispersion()
    {
        float perRad = 2 * Mathf.PI / rectChildren.Count;
        float initRad = InitAngle * Mathf.Deg2Rad;
        UpdateLayout(initRad, perRad);
    }

    protected void SectorDispersion()
    {
        float perRad = 0;
        float initRad = this.InitAngle * Mathf.Deg2Rad;
        // 扇形弧度
        var sectorRad = this.sectorAngle * Mathf.Deg2Rad;

        if (this.KeepAngle)
        {
            // 弧长公式为: L = (angle * PI / 180)
            perRad = KeepKeepAngleVal * Mathf.PI / 180;
        }
        else
        {
            perRad = rectChildren.Count == 1 ? 0 : sectorRad / (rectChildren.Count - 1);
        }
        if (!SectorClockwise)
        {
            perRad *= -1;
        }
        UpdateLayout(initRad, perRad);
    }

    protected void UpdateLayout(float initRad, float perRad)
    {
        float totalFlexible = 0;

        float minX = float.MaxValue;
        float maxX = float.MinValue;
        float minY = float.MaxValue;
        float maxY = float.MinValue;
        for (int i = 0; i < rectChildren.Count; i++)
        {
            var child = rectChildren[i];

            //禁用子节点recttransform相关属性
            m_Tracker.Add(this, child,
            DrivenTransformProperties.Anchors |
            DrivenTransformProperties.AnchoredPosition |
            DrivenTransformProperties.Pivot);

            var size = child.rect.size;
            child.pivot = new Vector2(0.5f, 0.5f);
            child.anchorMin = new Vector2(0.5f, 0.5f);
            child.anchorMax = new Vector2(0.5f, 0.5f);
            child.SetSizeWithCurrentAnchors(RectTransform.Axis.Horizontal, size.x);
            child.SetSizeWithCurrentAnchors(RectTransform.Axis.Vertical, size.y);

            size *= 0.5f * child.localScale;
            Vector3 vPos = child.localPosition;
            var rad = initRad + perRad * i;
            vPos.x = this.Radius * Mathf.Cos(rad);
            vPos.y = this.Radius * Mathf.Sin(rad);
            child.localPosition = vPos;
            if (LockSubAngle)
            {
                child.localEulerAngles = new Vector3(0, 0, SubAngle);
            }
            else
            {
                child.localEulerAngles = new Vector3(0, 0, rad * 180 / Mathf.PI + SubAngle);
            }
            if (NotRotationInAll)
            {
                for (int j = 0; j < child.childCount; j++)
                {
                    var sub = child.GetChild(j);
                    sub.localEulerAngles = new Vector3(0, 0, -child.localEulerAngles.z);
                }
            }

            var left = vPos.x - size.x;
            if (left < minX)
                minX = left;
            var right = vPos.x + size.x;
            if (right > maxX)
                maxX = right;

            var bottom = vPos.y - size.y;
            if (bottom < minY)
                minY = bottom;
            var top = vPos.y + size.y;
            if (top > maxY)
                maxY = top;
        }
        // 此处宽高计算并不是很精确
        var w = Mathf.Abs(maxX - minX);
        var h = Mathf.Abs(maxY - minY);
        SetLayoutInputForAxis(Radius, w, totalFlexible, 0);
        SetLayoutInputForAxis(Radius, h, totalFlexible, 1);
    }

}

