FestivalMiaoshaWGData = FestivalMiaoshaWGData or BaseClass()

function FestivalMiaoshaWGData:__init()
	if FestivalMiaoshaWGData.Instance then
		ErrorLog("[FestivalMiaoshaWGData] Attemp to create a singleton twice !")
	end

	FestivalMiaoshaWGData.Instance = self
	self.hefu_miaosha_cfg = ConfigManager.Instance:GetAutoConfig("festival_activity_timed_spike_cfg_auto")
	self:CreateHeFuMiaoShaInfoSturt()

	FestivalActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2, {[1] = MERGE_EVENT_TYPE.LEVEL},
									BindTool.Bind(self.GetAvtivityIsOpen, self), BindTool.Bind(self.ShowHeFuMiaoshaRemind, self))

	FestivalActivityWGData.Instance:SetNewFalgCallBack(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2, BindTool.Bind(self.IsShowNewFlag, self))

	RemindManager.Instance:Register(RemindName.Festival_MiaoSha, BindTool.Bind(self.ShowHeFuMiaoshaRemind, self))
	self:InitBuyTimesList()
end

function FestivalMiaoshaWGData:__delete()
	FestivalMiaoshaWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.Festival_MiaoSha)
end

-----------------------------------------限时秒杀-----------------------------------------

function FestivalMiaoshaWGData:CreateHeFuMiaoShaInfoSturt()
	self.hefu_miaosha_data = {}

	self.hefu_miaosha_select_type = 0

	self:CreateFictitiousDataList()

	self.item_lib_cfg = {}

	self.refresh_time_list = {}

	for i=1,3 do

		self.hefu_miaosha_data[i] = {}
		self.hefu_miaosha_data[i]["shop_id"] = 0 				--商品库id
		self.hefu_miaosha_data[i]["type"] = 0 					--专属类型
		self.hefu_miaosha_data[i]["refresh_time"] = 0 			--刷新时间
		self.hefu_miaosha_data[i]["total_quota"] = 0 			--累计值
		self.hefu_miaosha_data[i]["quota_reward_tag"] = {}		--累计奖励领取标记
		self.hefu_miaosha_data[i]["item_list"] = {}				--商品列表
	end

	self.new_flag_list = {}
end

--创建一个虚拟的数据表（无出售物品时使用）
function FestivalMiaoshaWGData:CreateFictitiousDataList()
	local other_cfg = self.hefu_miaosha_cfg.other[1]
	self.hefu_miaosha_fictitious_list = {}
	for i=1,4 do
		self.hefu_miaosha_fictitious_list[i] = {}
		for j=1,3 do
			self.hefu_miaosha_fictitious_list[i][j] = {}
			self.hefu_miaosha_fictitious_list[i][j].item_id = other_cfg.show_id
			self.hefu_miaosha_fictitious_list[i][j].is_fictitious = true
		end
	end
end

function FestivalMiaoshaWGData:GetFictitiousDataList()
	return self.hefu_miaosha_fictitious_list
end

function FestivalMiaoshaWGData:InitBuyTimesList()
	self.buy_times_list = {}

	for k,v in pairs(self.hefu_miaosha_cfg.item_lib) do
		self.buy_times_list[v.id] = {}
		self.item_lib_cfg[v.id] = v
		self.buy_times_list[v.id].id = v.id
		self.buy_times_list[v.id].times = 0
		self.buy_times_list[v.id].hidded_flag = 0
	end
end

----8966// 运营活动-限时秒杀信息
function FestivalMiaoshaWGData:SetSCHeFuTimedSpikeInfo(protocol)
	self.hefu_miaosha_data[protocol.type].shop_id = protocol.shop_id
	self.hefu_miaosha_data[protocol.type].type = protocol.type
	self.hefu_miaosha_data[protocol.type].refresh_time = protocol.refresh_time
	self.hefu_miaosha_data[protocol.type].total_quota = protocol.total_quota
	self.hefu_miaosha_data[protocol.type].quota_reward_tag = protocol.quota_reward_tag
	self.hefu_miaosha_data[protocol.type].item_count = protocol.item_count
	self.hefu_miaosha_data[protocol.type].item_list = protocol.item_list
	self.refresh_time_list[protocol.type] = protocol.refresh_time - TimeWGCtrl.Instance:GetServerTime()
end

--8967// 运营活动-限时秒杀商品更新
function FestivalMiaoshaWGData:SetSCHeFuTimedSpikeItemUpdate(protocol)
	self.hefu_miaosha_data[protocol.type].shop_id = protocol.shop_id
	self.hefu_miaosha_data[protocol.type].type = protocol.type
	self.hefu_miaosha_data[protocol.type].refresh_time = protocol.refresh_time
	self.hefu_miaosha_data[protocol.type].item_count = protocol.item_count
	self.hefu_miaosha_data[protocol.type].item_list = protocol.item_list
	self.refresh_time_list[protocol.type] = protocol.refresh_time - TimeWGCtrl.Instance:GetServerTime()
end

--8968// 运营活动-限时秒杀额度奖励更新
function FestivalMiaoshaWGData:SetSCHeFuTimedSpikeQuotaUpdate(protocol)
	self.hefu_miaosha_data[protocol.type].type = protocol.type
	self.hefu_miaosha_data[protocol.type].total_quota = protocol.total_quota
    self.hefu_miaosha_data[protocol.type].quota_reward_tag = protocol.quota_reward_tag
end

--8969,// 运营活动-限时秒杀购买次数更新
function FestivalMiaoshaWGData:SetSCHeFuTimedSpikeBuyTimesInfo(protocol)
	for k,v in pairs(protocol.buy_times_list) do
		if self.buy_times_list[v.id] then
			if v.id == self.buy_times_list[v.id].id then
				self.buy_times_list[v.id].times = v.times
				self.buy_times_list[v.id].hidded_flag = v.hidded_flag
			end
		end
	end
end

function FestivalMiaoshaWGData:SetCTimedSpikeNewTagInfo(protocol)
	for k,v in pairs(protocol.shop_list) do
		if v.shop_id > 0 then
			self.new_flag_list[v.shop_id] = v.shop_status
		end
	end
end

function FestivalMiaoshaWGData:GetSCOATimedSpikeBuyTimesInfoById(id)
	if self.buy_times_list[id] then
		return self.buy_times_list[id]
	end
end

function FestivalMiaoshaWGData:GetNewFlagByShopId(shop_id)
	if self.new_flag_list[shop_id] then
		return self.new_flag_list[shop_id]
	end
end

--根据专属类型拿数据
function FestivalMiaoshaWGData:GetHeFuMiaoShaInfoByType(type)
	return self.hefu_miaosha_data[type]
end

function FestivalMiaoshaWGData:GetRefreshTimeList()
	return self.refresh_time_list
end

-- type:专属类型
-- is_need_slider：是否需要进度条
function FestivalMiaoshaWGData:GetHeFuMiaoShaListData(type)
	local data_list = {}
	if not self.hefu_miaosha_data[type] then
		return data_list
	end
   
    local temp_data_list = self.hefu_miaosha_data[type].item_list
	if not temp_data_list or IsEmptyTable(temp_data_list) then
		return data_list
	end
	local data_list = self:GetSortDataList(temp_data_list)

	return data_list
end

function FestivalMiaoshaWGData:GetSortDataList(data)
	local sort_list = {}
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local vip_level = VipWGData.Instance:IsVip() and RoleWGData.Instance.role_vo.vip_level or 0

	local index = 0
	for k,v in pairs(data) do

 		local item_lib_cfg = self:GetItemLibConfigById(v.id)
 		local buy_info = self:GetSCOATimedSpikeBuyTimesInfoById(v.id)
		 -- if item_lib_cfg and buy_info and buy_info.hidded_flag ~= 1 then
		 if item_lib_cfg and buy_info then
			index = index + 1
 			sort_list[index] = {}
			sort_list[index].id = v.id
			sort_list[index].last_num = v.last_num
			sort_list[index].num = v.num
			sort_list[index].sort_index = 0
 			sort_list[index].sort_priority = item_lib_cfg.sort_priority
 			sort_list[index].bought_times = buy_info.times 					--已购次数

 			--有个字段还未订号
			if v.last_num <= 0 and buy_info.times < item_lib_cfg.buy_limit_value then
				sort_list[index].sort_index = 500
			elseif v.last_num <= 0 and buy_info.times >= item_lib_cfg.buy_limit_value then
				sort_list[index].sort_index = 500
			elseif v.last_num > 0 and buy_info.times >= item_lib_cfg.buy_limit_value then
				sort_list[index].sort_index = 400
			elseif role_level < item_lib_cfg.level_limit then
				sort_list[index].sort_index = 300
			elseif vip_level < item_lib_cfg.vip_level_limit then
				sort_list[index].sort_index = 200
			else
				sort_list[index].sort_index = 100
			end
		end
	end
	table.sort(sort_list, SortTools.KeyLowerSorters("sort_index", "sort_priority"))
	return sort_list
end

--根据专属类型拿配置
function FestivalMiaoshaWGData:GetShopLibConfigByType(type, shop_id)
	local cfg = self.hefu_miaosha_cfg.shop_lib

	for k,v in pairs(cfg) do
		if v.id == shop_id and type == v.type then
			return v
		end
	end
end

function FestivalMiaoshaWGData:GetItemLibConfigById(id)
	if self.item_lib_cfg[id] then
		return self.item_lib_cfg[id]
	end
end

function FestivalMiaoshaWGData:SetMiaoShaSelectType(select_type)
	self.hefu_miaosha_select_type = select_type
end

function FestivalMiaoshaWGData:GetMiaoShaSelectType()
	return self.hefu_miaosha_select_type
end

function FestivalMiaoshaWGData:ShowHeFuMiaoshaRemind()
	local is_enough_level = self:GetAvtivityIsOpen()
	local state = FestivalActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.FESTIVAL_ACT_OA_TIMED_SPIKE_2)

	if not is_enough_level or not state then
		return 0
	end

	for i=1,3 do
		if self:GetMiaoShaRemindByType(i) == 1 then
			return 1
		end
	end

	return 0
end

function FestivalMiaoshaWGData:GetMiaoShaRemindByType(type)
	local data_list = self:GetHeFuMiaoShaListData(type)
	-- 备货期间 data_list 为空表
	local data = self.hefu_miaosha_data[type]
	if data and not IsEmptyTable(data_list) then
		local shop_lib_cfg = self:GetShopLibConfigByType(type, data.shop_id)
		if shop_lib_cfg and shop_lib_cfg.gift_quota ~= "" then
			local gift_quota_list = Split(shop_lib_cfg.gift_quota, ",")
			for j=1,#gift_quota_list do
				if gift_quota_list[j] and data.total_quota >= tonumber(gift_quota_list[j]) and data.quota_reward_tag[j] == 0 then
					return 1
				end
			end
		end
	end

	return 0
end

function FestivalMiaoshaWGData:GetAvtivityIsOpen()
	local is_open = false

	local other_cfg = self.hefu_miaosha_cfg.other[1]

	if not other_cfg then
		return is_open
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	local need_level = other_cfg.open_role_level
	return role_level >= need_level
end

function FestivalMiaoshaWGData:GetOtherCfg()
	return self.hefu_miaosha_cfg.other[1]
end

function FestivalMiaoshaWGData:GetIsShowNewFalgByType(type)
	local data_list = self:GetHeFuMiaoShaListData(type)

	for k,v in pairs(data_list) do
		local new_flag = self.new_flag_list[v.id] or 0

		if new_flag == 1 then
			return 1
		end
	end

	return 0
end

function FestivalMiaoshaWGData:IsShowNewFlag()
	for i=1,3 do
		local is_show_new = self:GetIsShowNewFalgByType(i)
		if is_show_new == 1 then
			return true
		end
	end

	return false
end
-------------------------------------------------------------------------------------------
