HolyDarkWeaponWGData = HolyDarkWeaponWGData or BaseClass()
HolyDarkWeaponWGData.Weapon_type = {
	HolyType = 0,					--圣器类型
	DarkType = 1,					--暗器类型
}

HolyDarkWeaponWGData.DecompsEquip = 50     --每次消耗装备最大上限

function HolyDarkWeaponWGData:__init()
	if HolyDarkWeaponWGData.Instance then
		error("[HolyDarkWeaponWGData] Attempt to create singleton twice!")
		return
	end
	HolyDarkWeaponWGData.Instance = self

	-- 【配置】
    local cfg = ConfigManager.Instance:GetAutoConfig("relic_cfg_auto")
    self.other_cfg = cfg.other[1]
    self.relic_type_cfg = ListToMapList(cfg.relic, "type")
    self.relic_seq_cfg = ListToMap(cfg.relic, "seq")
    self.seal_relic_cfg = ListToMap(cfg.seal, "relic_seq", "seal_index")
    self.seal_cost_cfg = ListToMap(cfg.seal, "cost_item_id")
    self.equip_cfg = ListToMap(cfg.equip, "item_id")
    self.empty_slot_cfg = ListToMap(cfg.empty_slot_seq, "relic_seq", "slot")
    self.equip_suit_cfg = ListToMap(cfg.suit, "relic_type", "suit_level")
    self.relic_skill_level_cfg = ListToMap(cfg.relic_skill_level, "relic_type", "level")
    self.slot_level_cfg = ListToMap(cfg.slot_level, "relic_type", "slot", "level")
    self.slot_level_cost_cfg = ListToMap(cfg.slot_level, "cost_item_id")
    self.slot_star_cfg = ListToMap(cfg.slot_star, "relic_seq", "slot", "level")
    self.slot_star_cost_cfg = ListToMap(cfg.slot_star, "cost_item_id")
    self.relic_month_cfg = ListToMap(cfg.relic_type, "type")
    self.power_level_cfg = ListToMap(cfg.power_level, "type", "level")
    self.slot_grade_cfg = ListToMap(cfg.slot_grade, "relic_type", "slot", "grade")

	self.cur_weapon_type = 0
	self.relic_item_list = {}
	self.relic_holy_equip_bag_list = {}
	self.relic_dark_equip_bag_list = {}
	self.power_item_list = {}

	RemindManager.Instance:Register(RemindName.HolyWeapon, BindTool.Bind(self.GetRelicRemind, self, 0))
	RemindManager.Instance:Register(RemindName.DarkWeapon, BindTool.Bind(self.GetRelicRemind, self, 1))
end

function HolyDarkWeaponWGData:__delete()
	HolyDarkWeaponWGData.Instance = nil

	self.cur_weapon_type = nil
	self.relic_item_list = nil
	self.relic_holy_equip_bag_list = nil
	self.relic_dark_equip_bag_list = nil
	self.power_item_list = nil

	RemindManager.Instance:UnRegister(RemindName.HolyWeapon)
	RemindManager.Instance:UnRegister(RemindName.DarkWeapon)
end

------------------------------协议数据
-- 圣装全部信息
function HolyDarkWeaponWGData:SetRelicItemAllInfo(protocol)
	self.relic_item_list = protocol.relic_item_list
end

--单个圣装信息变化
function HolyDarkWeaponWGData:RelicItemUpdateInfo(protocol)
	local relic_seq = protocol.relic_seq
	local relic_item = protocol.relic_item

	self.relic_item_list[relic_seq] = relic_item
end

--light背包全部信息
function HolyDarkWeaponWGData:SetRelicLightBagInfo(protocol)
	self.relic_holy_equip_bag_list = protocol.relic_light_grid_list
end

-- 单个light背包信息变化
function HolyDarkWeaponWGData:SetRelicLightBagChangeInfo(protocol)
	local new_data = protocol.change_info
	local index = new_data.index

	self.relic_holy_equip_bag_list[index] = new_data
end

--单个light背包信息
function HolyDarkWeaponWGData:GetRelicLightBagInfoByIndex(index)
	return self.relic_holy_equip_bag_list[index]
end


--dark背包全部信息
function HolyDarkWeaponWGData:SetRelicDarkBagInfo(protocol)
	self.relic_dark_equip_bag_list = protocol.relic_dark_grid_list
end

-- 单个dark背包信息变化
function HolyDarkWeaponWGData:SetRelicDarkBagChangeInfo(protocol)
	local new_data = protocol.change_info
	local index = new_data.index

	self.relic_dark_equip_bag_list[index] = new_data
end

--单个dark背包信息
function HolyDarkWeaponWGData:GetRelicDarkBagInfoByIndex(index)
	return self.relic_dark_equip_bag_list[index]
end

-- 能量信息
function HolyDarkWeaponWGData:SetRelicPowerItemInfo(protocol)
	self.power_item_list = protocol.power_item_list
end

--单个圣装能量信息变化
function HolyDarkWeaponWGData:RelicPowerItemUpdateInfo(protocol)
	local relic_type = protocol.type
	local power_item = protocol.power_item

	self.power_item_list[relic_type] = power_item
end

-- 保存当前打开view类型（圣器/暗器）
function HolyDarkWeaponWGData:SetCurWeaponType(weapon_type)
	self.cur_weapon_type = weapon_type
end

function HolyDarkWeaponWGData:GetCurWeaponType()
	return self.cur_weapon_type
end

--获取圣装数据byseq
function HolyDarkWeaponWGData:GetRelicItemDataBySeq(relic_seq)
	return self.relic_item_list[relic_seq]
end

--获取装备配置byitem_id
function HolyDarkWeaponWGData:GetRelicEquipItemData(item_id)
	return self.equip_cfg[item_id]
end

--没有装备弹tip数据
function HolyDarkWeaponWGData:GetRelicEquipEmptyItemData(relic_seq, slot)
	return self.empty_slot_cfg[relic_seq] and self.empty_slot_cfg[relic_seq][slot]
end

-- 背包
function FiveElementsWGData:GetFiveElementsBagListInfo()
	local data_list = SortDataByItemColor(self.bag_grid_list)
	return data_list
end

--根据seq选择符合装备的背包列表
function HolyDarkWeaponWGData:GetRelicEquipBagListBySeq(relic_seq)
	local bag_list = {}
	local data_info = {}
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if not IsEmptyTable(relic_cfg) then
		if relic_cfg.type == HolyDarkWeaponWGData.Weapon_type.HolyType then
			data_info = self.relic_holy_equip_bag_list
		else
			data_info = self.relic_dark_equip_bag_list
		end

		for index, data in pairs(data_info) do
			for k, v in pairs(data.relic_seq_list) do
				if v == relic_seq then
					table.insert(bag_list, data)
				end
			end
		end
	end

	bag_list = SortDataByItemColor(bag_list)
	return bag_list
end

-- 获取圣器穿戴列表
function HolyDarkWeaponWGData:GetHolyDarkEquipWearList(relic_seq)
	local data_info = self:GetRelicItemDataBySeq(relic_seq)
	return data_info and data_info.relic_slot_list
end

-- 是否是更好的穿戴
function HolyDarkWeaponWGData:RelicEquipIsBetterWear(relic_seq, item_data)
	if item_data == nil or relic_seq and relic_seq < 0 then
		return false
	end
	
	local slot = item_data.slot
	local color = item_data.color
	-- 选择的圣器装备数据
	local wear_all_data = self:GetHolyDarkEquipWearList(relic_seq)
	local wear_slot_data = wear_all_data and wear_all_data[slot]
	if IsEmptyTable(wear_slot_data) or wear_slot_data.item_id <= 0 then
		return true
	end

	-- 品质比较
	if color > wear_slot_data.color then
		return true
	end

	return false
end

-- 圣器是否穿戴了装备
function HolyDarkWeaponWGData:GetRelicEquipIsWear(relic_seq)
	local wear_all_data = self:GetHolyDarkEquipWearList(relic_seq)
	local is_wear = false
	if wear_all_data then
		for k, v in pairs(wear_all_data) do
			if v.item_id > 0 then
				is_wear = true
				break
			end
		end
	end

	return is_wear
end

-- 获取圣器4个封印某个目前状态是否激活By Seal_index
function HolyDarkWeaponWGData:GetSealDataBySealIndex(relic_seq, seal_index)
	local cur_relic_data = self.relic_item_list[relic_seq] or {}
	local seal_data = cur_relic_data and cur_relic_data.seal_flag or {}
	return seal_data[seal_index] and seal_data[seal_index] ~= 0 or false
end


-- 获取圣器4个封印是否有激活
function HolyDarkWeaponWGData:GetHaveSealActBySeq(relic_seq)
	local seal_data = self:GetSealCfgBySeq(relic_seq)
    if not IsEmptyTable(seal_data) then
    	for k, v in pairs(seal_data) do
    		if self:GetSealDataBySealIndex(relic_seq, v.seal_index) then
    			return true
    		end
    	end
    end

    return false
end
-- 当前圣器是否激活
function HolyDarkWeaponWGData:GetRelicSateBySeq(relic_seq)
	local cur_relic_data = self.relic_item_list[relic_seq]
	return cur_relic_data and cur_relic_data.active_flag and cur_relic_data.active_flag ~= 0 or false
end

-- 根据武器类型获取展示圣器信息(等级判断 和天数 是否显示 或者有个封印被激活)
function HolyDarkWeaponWGData:GetShowWeaponInfo()
	local show_list = {}
	show_list = self:GetShowWeaponList(self.cur_weapon_type)

	return show_list
end

function HolyDarkWeaponWGData:GetShowWeaponList(relic_type)
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local cur_weapon_type_cfg = self.relic_type_cfg[relic_type] or {}
	local show_list = {}
	local server_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if not IsEmptyTable(cur_weapon_type_cfg) then
		for k, v in ipairs(cur_weapon_type_cfg) do
			if (role_level >= v.show_level and server_day >= v.open_day) or self:GetHaveSealActBySeq(v.seq) then
				table.insert(show_list, v)
			end
		end
	end

	return show_list
end

--获取圣/暗器配置根据seq
function HolyDarkWeaponWGData:GetWeaponCfgBySeq(relic_seq)
	return self.relic_seq_cfg[relic_seq] or {}
end

--获取圣/暗器封印配置根据relic_seq
function HolyDarkWeaponWGData:GetSealCfgBySeq(relic_seq)
	return self.seal_relic_cfg[relic_seq] or {}
end

--检测是否是封印消耗物品
function HolyDarkWeaponWGData:CheckIsSealStuff(change_item_id, change_reason)
	return self.seal_cost_cfg[change_item_id] ~= nil
end

--检测是否是槽位升级消耗物品
function HolyDarkWeaponWGData:CheckIsSlotLevelStuff(change_item_id, change_reason)
	return self.slot_level_cost_cfg[change_item_id] ~= nil
end

--检测是否是槽位升星消耗物品
function HolyDarkWeaponWGData:CheckIsSlotStarStuff(change_item_id, change_reason)
	return self.slot_star_cost_cfg[change_item_id] ~= nil
end

--获取套装等级
function HolyDarkWeaponWGData:GetRelicSuitLevel(relic_seq)
	local data_info = self:GetRelicItemDataBySeq(relic_seq)
	return data_info and data_info.suit_level or 0
end

--获取套装配置
function HolyDarkWeaponWGData:GetRelicSuitCfg(relic_seq, suit_level)
    local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if not IsEmptyTable(relic_cfg) then
		--套装只配有圣器/暗器两种配置
		return (self.equip_suit_cfg[relic_cfg.type] or {})[suit_level]
	end

	return nil
end

-- 满足套装 对应条件品质
function HolyDarkWeaponWGData:GetRelicSuitEnoughColorNum(relic_seq, need_color)
	local enough_num = 0
	local wear_all_data = self:GetHolyDarkEquipWearList(relic_seq)
	if not IsEmptyTable(wear_all_data) then
		for k, v in pairs(wear_all_data) do
			if v.color >= need_color then
				enough_num = enough_num + 1
			end
		end
	end

	return enough_num
end

-- 获取圣器技能等级
function HolyDarkWeaponWGData:GetRelicSkillLevel(relic_seq)
	local cur_relic_data = self.relic_item_list[relic_seq] or {}
	local skill_level = cur_relic_data and cur_relic_data.skill_level or 0
	local skill_exp = cur_relic_data and cur_relic_data.skill_exp or 0
	return skill_level, skill_exp
end

-- 获取圣器技能等级配置
function HolyDarkWeaponWGData:GetRelicSkillLevelCfg(relic_seq, skill_level)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if not IsEmptyTable(relic_cfg) then
		return (self.relic_skill_level_cfg[relic_cfg.type] or {})[skill_level]
	end

	return nil
end
-- 获取多余装备用于技能升级
function HolyDarkWeaponWGData:GetUpSkillEquipBag(relic_seq)
	local skill_equip_bag = {}
	local equip_bag_info = self:GetRelicEquipBagListBySeq(relic_seq)
	if not IsEmptyTable(equip_bag_info) then
		for k, v in pairs(equip_bag_info) do
			local is_better = self:RelicEquipIsBetterWear(relic_seq, v)
			if not is_better then
				table.insert(skill_equip_bag, v)
			end
		end
	end

	return skill_equip_bag
end

--选择背包的数据参数 bag_list 选中的背包格子 add_per 选中格子经验
function HolyDarkWeaponWGData:SetSkillSelectBagParam(bag_list, add_per, count)
	self.skill_bag_list = bag_list
	self.skill_add_per = add_per
	self.skill_select_count = count
end

function HolyDarkWeaponWGData:GetSkillSelectBagParam()
	return self.skill_bag_list , self.skill_add_per or 0 , self.skill_select_count or 0
end

function HolyDarkWeaponWGData:ClearSkillSelectBagParam()
	self.skill_bag_list = nil
	self.skill_add_per = 0
	self.skill_select_count = 0
end

function HolyDarkWeaponWGData:SkillPopEquipIsSelect(index)
	if not IsEmptyTable(self.skill_bag_list) then
		for k,v in pairs(self.skill_bag_list) do
			if v.index == index then
				return true
			end
		end
	end
	return false
end

function HolyDarkWeaponWGData:GetRelicSlotLevelCfg(relic_seq, slot, level)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if not IsEmptyTable(relic_cfg) then
		return ((self.slot_level_cfg[relic_cfg.type] or {})[slot] or {})[level]
	end

	return nil
end

--判断穿戴的装备能否升级
function HolyDarkWeaponWGData:GetRelicSlotCanUpLevel(wear_equip_data)
	local cur_level_cfg = self:GetRelicSlotLevelCfg(wear_equip_data.relic_seq, wear_equip_data.slot_index, wear_equip_data.level)
	local next_level_cfg = self:GetRelicSlotLevelCfg(wear_equip_data.relic_seq, wear_equip_data.slot_index, wear_equip_data.level + 1)
	local is_max = IsEmptyTable(next_level_cfg)
	if is_max then
		return
	end

	if not IsEmptyTable(cur_level_cfg) then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_level_cfg.cost_item_id)
		return item_num >= cur_level_cfg.cost_item_num
	end

	return false
end

function HolyDarkWeaponWGData:GetRelicSlotStarCfg(relic_seq, slot, star_level)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if not IsEmptyTable(relic_cfg) then
		return ((self.slot_star_cfg[relic_seq] or {})[slot] or {})[star_level]
	end

	return nil
end

--判断穿戴的装备能否升星
function HolyDarkWeaponWGData:GetRelicSlotCanUpStar(wear_equip_data)
	local cur_star_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotStarCfg(wear_equip_data.relic_seq, wear_equip_data.slot_index, wear_equip_data.star_level)
	local next_star_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotStarCfg(wear_equip_data.relic_seq, wear_equip_data.slot_index, wear_equip_data.star_level + 1)
	local is_max = IsEmptyTable(next_star_cfg)
	if is_max then
		return false
	end

	if not IsEmptyTable(cur_star_cfg) then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_star_cfg.cost_item_id)
		return item_num >= cur_star_cfg.cost_item_num
	end

	return false
end

--获取圣装能量信息byseq
function HolyDarkWeaponWGData:GetRelicPowerItemInfoBySeq(relic_seq)
	return self.power_item_list[relic_seq] or {}
end

--月卡直购
function HolyDarkWeaponWGData:GetRelicMonthBuyInfo(relic_seq)
	return self.relic_month_cfg[relic_seq] or {}
end

--能量配置
function HolyDarkWeaponWGData:GetRelicPowerLevelCfg(relic_type, level)
	return (self.power_level_cfg[relic_type] or {})[level]
end

--是否有能量可以获取
function HolyDarkWeaponWGData:GetRelicPowerCanGet(relic_type)
	local info = HolyDarkWeaponWGData.Instance:GetRelicPowerItemInfoBySeq(relic_type)
	if not IsEmptyTable(info) then
		for k, v in pairs(info.power_list) do
			if v.power_value > 0 then
				return true
			end
		end
	end
	return false
end

--槽位阶数配置
function HolyDarkWeaponWGData:GetRelicSlotGradeCfg(relic_seq, slot, grade)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if not IsEmptyTable(relic_cfg) then
		return ((self.slot_grade_cfg[relic_cfg.type] or {})[slot] or {})[grade]
	end
	
	return nil
end

--判断穿戴的装备能否升阶
function HolyDarkWeaponWGData:GetRelicSlotCanUpGrade(wear_equip_data)
	local cur_grade_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeCfg(wear_equip_data.relic_seq, wear_equip_data.slot_index, wear_equip_data.grade)
	local next_grade_cfg = HolyDarkWeaponWGData.Instance:GetRelicSlotGradeCfg(wear_equip_data.relic_seq, wear_equip_data.slot_index, wear_equip_data.grade + 1)
	local is_max = IsEmptyTable(next_grade_cfg)
	if is_max then
		return false
	end

	if not IsEmptyTable(cur_grade_cfg) then
		local relic_cfg = self:GetWeaponCfgBySeq(wear_equip_data.relic_seq)
		local info = self:GetRelicPowerItemInfoBySeq(relic_cfg.type)
		if not IsEmptyTable(relic_cfg) and (not IsEmptyTable(info)) then
			return info.total_power > 0
		end
	end

	return false
end


function HolyDarkWeaponWGData:GetRelicCapabilityBySeq(relic_seq) --圣器战力
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if IsEmptyTable(relic_cfg) then
		return 0
	end

	local capability = 0
	local seal_cap = self:GetRelicSealCapabilityBySeq(relic_seq)
	local equip_cap = self:GetRelicEquipCapabilityBySeq(relic_seq)
	capability = capability + seal_cap + equip_cap
	return capability
end

function HolyDarkWeaponWGData:GetRelicSealCapabilityBySeq(relic_seq, ignore) --封印战力
	local capability = 0
	local sys_attr_per = 0
	local seal_attribute = AttributePool.AllocAttribute()
	local seal_data = self:GetSealCfgBySeq(relic_seq)
	local max_attr_num = 1
    local attr_id, attr_value = 0, 0
    local em_data = EquipmentWGData.Instance
    for k, v in pairs(seal_data) do
        if ignore or self:GetSealDataBySealIndex(relic_seq, v.seal_index) then
            for i = 1, max_attr_num do
                attr_id = v["attr_id" .. i]
                attr_value = v["attr_value" .. i]
                if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
                    local attr_str = em_data:GetAttrStrByAttrId(attr_id)
                    seal_attribute[attr_str] = seal_attribute[attr_str] + attr_value
                end
            end
        end
    end

    if ignore or self:GetRelicSateBySeq(relic_seq) then
    	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
    	if not IsEmptyTable(relic_cfg) then
    		sys_attr_per = relic_cfg.seal_attr_per
    	end
    end

    local add_per = 1 + sys_attr_per * 0.0001
   	for k, v in pairs(seal_attribute) do
        if v > 0 then
            seal_attribute[k] = seal_attribute[k] * add_per
        end
    end

    return AttributeMgr.GetCapability(seal_attribute)
end

function HolyDarkWeaponWGData:GetRelicEquipCapabilityBySeq(relic_seq) --装备战力
	local attribute = AttributePool.AllocAttribute()
	local cap = 0
	local equip_attr_per = 0 --技能加成

	local skill_level = self:GetRelicSkillLevel(relic_seq) --技能加成战力
	local cur_skill_level_cfg = self:GetRelicSkillLevelCfg(relic_seq, skill_level)

	if cur_skill_level_cfg then
		equip_attr_per = cur_skill_level_cfg.equip_attr_per
	end

	local wear_all_data = self:GetHolyDarkEquipWearList(relic_seq)--所有穿戴的装备基础属性战力
	if not IsEmptyTable(wear_all_data) then
		for k, v in pairs(wear_all_data) do
			if v.item_id > 0 then
				--基础属性 +强化属性 +技能属性
				local equip_attr_list = self:GetRelicEquipBaseAttrList(v.item_id, true)
				local qianghua_attr_per = 0       ---槽位强化等级加成
				local cur_level_cfg = self:GetRelicSlotLevelCfg(v.relic_seq, v.slot_index, v.level)
				if not IsEmptyTable(cur_level_cfg) then
					qianghua_attr_per = cur_level_cfg.attr_per
				end

				local add_per = (equip_attr_per + qianghua_attr_per) * 0.0001
				for k,v in pairs(equip_attr_list) do
					local is_per = EquipmentWGData.Instance:GetAttrIsPerByAttrStr(v.attr_str) --百分比属性不叠加
					if is_per then
						attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
					else
						attribute[v.attr_str] = attribute[v.attr_str] + (v.attr_value * (1 + add_per))
					end
				end
				--星级属性
				local star_attr_list = self:GetRelicSlotStarAttrList(v.relic_seq, v.slot_index, v.star_level, true)
				for k,v in pairs(star_attr_list) do
					attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
				end

				-- --阶数属性
				-- local grade_attr_list = self:GetRelicSlotGradeAttrList(v.relic_seq, v.slot_index, v.grade, true)
				-- for k,v in pairs(grade_attr_list) do
				-- 	attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
				-- end
			end
		end
	end

	-- 套装战力属性
	local suit_level = self:GetRelicSuitLevel(relic_seq)
	local suit_attr_list = self:GetRelicSuitAttrList(relic_seq, suit_level, true)
	for k,v in pairs(suit_attr_list) do
		attribute[v.attr_str] = attribute[v.attr_str] + v.attr_value
	end

	cap = AttributeMgr.GetCapability(attribute)
	return cap
end

--列表选择index
function HolyDarkWeaponWGData:GetSelectIndexList()
	local show_list = self:GetShowWeaponInfo()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local index = 0
	if not IsEmptyTable(show_list) then
		--没红点默认最后一个
		for i, v in ipairs(show_list) do
			if role_level > v.open_level and index < i then
				index = i
			end
		end

		for i, v in ipairs(show_list) do
			if self:ShowRelicAllRemind(v.seq) then
				index = i
				break
			end
		end
	end

	return index
end

--界面索引跳转
function HolyDarkWeaponWGData:GetOpenViewJump(relic_seq)
	local default_tab_index = TabIndex.holy_dark_seal
	if self:ShowRelicSealRemind(relic_seq) then
		default_tab_index = TabIndex.holy_dark_seal
	elseif self:ShowRelicEquipBagRemind(relic_seq) then
		default_tab_index = TabIndex.holy_dark_equip_bag
	elseif self:GetRelicSkillRemind(relic_seq) then
		default_tab_index = TabIndex.holy_dark_equip_skill
	elseif self:GetRelicQiangHuaRemind(relic_seq) then
		default_tab_index = TabIndex.holy_dark_qianghua
	elseif self:GetRelicStarRemind(relic_seq) then
		default_tab_index = TabIndex.holy_dark_upstar
	elseif self:GetRelicGradeRemind(relic_seq) then
		default_tab_index = TabIndex.holy_dark_jinjie
	end

	return default_tab_index
end

----------------属性-------------
--装备基础属性
function HolyDarkWeaponWGData:GetRelicEquipBaseAttrList(item_id, no_sort)
	local attr_list = {}
	local cfg = self:GetRelicEquipItemData(item_id)
	if IsEmptyTable(cfg) then
        return attr_list
    end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
        attr_id = cfg["attr_id" .. i]
        attr_value = cfg["attr_value" .. i]
        if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
            local attr_str = em_data:GetAttrStrByAttrId(attr_id)
            local data = {
				attr_id = attr_id,
                attr_str = attr_str,
                attr_value = attr_value,
                add_value = 0,
            }

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

            table.insert(attr_list, data)
        end
    end

	if not no_sort and not IsEmptyTable(attr_list) then
        table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
    end

	return attr_list
end

-- 套装属性
function HolyDarkWeaponWGData:GetRelicSuitAttrList(relic_seq, suit_level, no_sort)
	local attr_list = {}
	local level_suit_cfg = self:GetRelicSuitCfg(relic_seq, suit_level)
	if not level_suit_cfg then
		return attr_list
	end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
		attr_id = level_suit_cfg["attr_id" .. i]
		attr_value = level_suit_cfg["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = em_data:GetAttrStrByAttrId(attr_id)
			local data = {
				attr_str = attr_str,
                attr_value = attr_value,
			}

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

			table.insert(attr_list, data)
		end
	end

	if not no_sort and not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end

--星级属性
function HolyDarkWeaponWGData:GetRelicSlotStarAttrList(relic_seq, slot, star_level, no_sort)
	local attr_list = {}
	local cfg = self:GetRelicSlotStarCfg(relic_seq, slot, star_level)
	if not cfg then
		return attr_list
	end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
		attr_id = cfg["attr_id" .. i]
		attr_value = cfg["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = em_data:GetAttrStrByAttrId(attr_id)
			local data = {
				attr_str = attr_str,
                attr_value = attr_value,
			}

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

			table.insert(attr_list, data)
		end
	end

	if not no_sort and not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end

--阶数属性
function HolyDarkWeaponWGData:GetRelicSlotGradeAttrList(relic_seq, slot, grade, no_sort)
	local attr_list = {}
	local cfg = self:GetRelicSlotGradeCfg(relic_seq, slot, grade)
	if not cfg then
		return attr_list
	end

	local em_data = EquipmentWGData.Instance
	local attr_id, attr_value = 0, 0
	local max_attr_num = 5
	for i = 1, max_attr_num do
		attr_id = cfg["attr_id" .. i]
		attr_value = cfg["attr_value" .. i]
		if attr_id and attr_id > 0 and attr_value and attr_value > 0 then
			local attr_str = em_data:GetAttrStrByAttrId(attr_id)
			local data = {
				attr_str = attr_str,
                attr_value = attr_value,
			}

			if not no_sort then
				data.attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str)
			end

			table.insert(attr_list, data)
		end
	end

	if not no_sort and not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

	return attr_list
end



-----------------------------红点-----------------
function HolyDarkWeaponWGData:GetRelicRemind(weapon_type)
	local weapon_name = weapon_type == 0 and FunName.HolyWeapon or FunName.DarkWeapon
	local is_open = FunOpen.Instance:GetFunIsOpened(weapon_name)
	if not is_open then
		return 0
	end

	local show_list = self:GetShowWeaponList(weapon_type)
	if not IsEmptyTable(show_list) then
		for i, v in ipairs(show_list) do
			if self:ShowRelicAllRemind(v.seq) then
				return 1
			end
		end
	end

	return 0
end

function HolyDarkWeaponWGData:ShowRelicAllRemind(relic_seq) --一个圣器所有红点
	if self:ShowRelicSealRemind(relic_seq) then
		return true
	elseif self:ShowRelicEquipBagRemind(relic_seq) then
		return true
	elseif self:GetRelicSkillRemind(relic_seq) then
		return true
	elseif self:GetRelicQiangHuaRemind(relic_seq) then
		return true
	elseif self:GetRelicStarRemind(relic_seq) then
		return true
	elseif self:GetRelicGradeRemind(relic_seq) then
		return true
	end

	return false
end

function HolyDarkWeaponWGData:ShowRelicSealRemind(relic_seq) -- 圣器封印红点
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if IsEmptyTable(relic_cfg) then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < relic_cfg.open_level then -- 没有达到开放等级
		return false
	end

	local is_remind = false
	local active_state = HolyDarkWeaponWGData.Instance:GetRelicSateBySeq(relic_seq) --圣器已激活
	if active_state then
		return is_remind
	end

	local all_seal_act = true
	local seal_data = self:GetSealCfgBySeq(relic_seq)
    if not IsEmptyTable(seal_data) then
    	for k, v in pairs(seal_data) do
    		if not self:GetSealDataBySealIndex(relic_seq, v.seal_index) then
    			local item_num = ItemWGData.Instance:GetItemNumInBagById(v.cost_item_id)
			    if item_num >= v.cost_item_num then  --封印没激活  激活物品足够
			        is_remind = true
			        return is_remind
			    end

			    all_seal_act = false
    		end
    	end
    end

    if all_seal_act and (not active_state) then --4个封印激活  圣器还未激活
    	is_remind = true
    end

	return is_remind
end

--装备背包红点
function HolyDarkWeaponWGData:ShowRelicEquipBagRemind(relic_seq)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if IsEmptyTable(relic_cfg) then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < relic_cfg.open_level then -- 没有达到开放等级
		return false
	end

	--是否是更好的穿戴
	local equip_bag_info = self:GetRelicEquipBagListBySeq(relic_seq)
	if not IsEmptyTable(equip_bag_info) then
		for k,v in pairs(equip_bag_info) do
			local is_better = self:RelicEquipIsBetterWear(relic_seq, v)
			if is_better then
				return true
			end
		end
	end

	if self:GetRelicSuitRemind(relic_seq) then
		return true
	end

	return false
end

-- 套装红点
function HolyDarkWeaponWGData:GetRelicSuitRemind(relic_seq)
	local suit_level = HolyDarkWeaponWGData.Instance:GetRelicSuitLevel(relic_seq)
	local next_cfg = self:GetRelicSuitCfg(relic_seq, suit_level + 1)
	if not next_cfg then
		return false
	end

	local enough_num = self:GetRelicSuitEnoughColorNum(relic_seq, next_cfg.color)
	return enough_num >= next_cfg.need_num
end

-- 技能红点
function HolyDarkWeaponWGData:GetRelicSkillRemind(relic_seq)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if IsEmptyTable(relic_cfg) then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < relic_cfg.open_level then -- 没有达到开放等级
		return false
	end

	local skill_level = self:GetRelicSkillLevel(relic_seq)
	local next_skill_level_cfg = self:GetRelicSkillLevelCfg(relic_seq, skill_level + 1)
	if not next_skill_level_cfg then
		return false
	end

	local skill_equip_bag = self:GetUpSkillEquipBag(relic_seq)
	if #skill_equip_bag > 0 then
		return true
	end
end

-- 强化红点
function HolyDarkWeaponWGData:GetRelicQiangHuaRemind(relic_seq)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if IsEmptyTable(relic_cfg) then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < relic_cfg.open_level then -- 没有达到开放等级
		return false
	end

	local wear_all_data = self:GetHolyDarkEquipWearList(relic_seq)
	if not IsEmptyTable(wear_all_data) then
		for k, v in pairs(wear_all_data) do
			if v.item_id > 0 then
				local is_up = self:GetRelicSlotCanUpLevel(v)
				if is_up then 
					return true
				end
			end
		end
	end

	return false
end

-- 升星红点
function HolyDarkWeaponWGData:GetRelicStarRemind(relic_seq)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if IsEmptyTable(relic_cfg) then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < relic_cfg.open_level then -- 没有达到开放等级
		return false
	end

	local wear_all_data = self:GetHolyDarkEquipWearList(relic_seq)
	if not IsEmptyTable(wear_all_data) then
		for k, v in pairs(wear_all_data) do
			if v.item_id > 0 then
				local is_up = self:GetRelicSlotCanUpStar(v)
				if is_up then 
					return true
				end
			end
		end
	end

	return false
end

-- 升阶红点
function HolyDarkWeaponWGData:GetRelicGradeRemind(relic_seq)
	local relic_cfg = self:GetWeaponCfgBySeq(relic_seq)
	if IsEmptyTable(relic_cfg) then
		return false
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < relic_cfg.open_level then -- 没有达到开放等级
		return false
	end

	--先屏蔽
	-- local wear_all_data = self:GetHolyDarkEquipWearList(relic_seq)
	-- if not IsEmptyTable(wear_all_data) then
	-- 	for k, v in pairs(wear_all_data) do
	-- 		if v.item_id > 0 then
	-- 			local is_up = self:GetRelicSlotCanUpGrade(v)
	-- 			if is_up then 
	-- 				return true
	-- 			end
	-- 		end
	-- 	end
	-- end

	-- if self:GetRelicEquipIsWear() then --能量
	-- 	local is_can_get = self:GetRelicPowerCanGet(relic_cfg.type)
	-- 	if is_can_get then
	-- 		return true
	-- 	end
	-- end

	return false
end

