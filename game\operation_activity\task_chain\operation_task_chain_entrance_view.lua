OperationTaskChainEntranceView = OperationTaskChainEntranceView or BaseClass(SafeBaseView)

function OperationTaskChainEntranceView:__init()
	self.view_style = ViewStyle.Full
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true, true)

	self:AddViewResource(0, "uis/view/operation_task_chain_ui_prefab", "layout_task_chain_entrance_view")
end

function OperationTaskChainEntranceView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("task_chain_entrance_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_entrance_timer")
	end

	if CountDownManager.Instance:HasCountDown("task_chain_entrance_act_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_entrance_act_timer")
	end


	if self.task_chain_task_list ~= nil then
		for k, v in pairs(self.task_chain_task_list) do
			if v then
				v:DeleteMe()
			end
		end

		self.task_chain_task_list = nil
	end

	if self.reward_grid then
		self.reward_grid:DeleteMe()
		self.reward_grid = nil
	end

	if self.display_model ~= nil then
		self.display_model:DeleteMe()
		self.display_model = nil
	end
end

function OperationTaskChainEntranceView:CloseCallBack()
	if CountDownManager.Instance:HasCountDown("task_chain_entrance_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_entrance_timer")
	end
end

function OperationTaskChainEntranceView:LoadCallBack()
	self.reward_grid = AsyncBaseGrid.New()
	self.reward_grid:SetStartZeroIndex(false)
	self.reward_grid:CreateCells({ col = 3, change_cells_num = 1, list_view = self.node_list["list_reward"] })

	self.node_list.btn_go.button:AddClickListener(BindTool.Bind1(self.OnClickTaskChainGo, self))

	self.display_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["root_obj"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = false,
	}
	
	self.display_model:SetRenderTexUI3DModel(display_data)
	-- self.display_model:SetUI3DModel(self.node_list.root_obj.transform, nil, 1, false, MODEL_CAMERA_TYPE.BASE,
	-- 	MODEL_OFFSET_TYPE.NORMALIZE)
	self:AddUiRoleModel(self.display_model)

	-- self:FlushPictureAndTextContent()
end

function OperationTaskChainEntranceView:FlushPictureAndTextContent()
	local task_chain_interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
	if not task_chain_interface_cfg then
		return
	end

	self.node_list.RawImage_tongyong.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG(task_chain_interface_cfg.pic2_1))
	self.node_list.RawImage_tongyong.raw_image:SetNativeSize()
	self.node_list.zhangshi_2.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic2_2))
	self.node_list.zhangshi_2.image:SetNativeSize()
	self.node_list.zhangshi_1.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic2_2))
	self.node_list.zhangshi_1.image:SetNativeSize()
	self.node_list.think_gift_flag.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic_11))
	self.node_list.think_gift_flag.image:SetNativeSize()
	self.node_list.reward_list_bg.image:LoadSprite(ResPath.GetOperationTaskChainF2(task_chain_interface_cfg.pic_12))
	self.node_list.reward_list_bg.image:SetNativeSize()
	self.node_list.btn_go.image:LoadSprite(ResPath.GetCommonButtonToggle_atlas(task_chain_interface_cfg.pic_10))
	self.node_list.btn_go.image:SetNativeSize()
end

function OperationTaskChainEntranceView:OnClickTaskChainGo()
	CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
	self:Close()
end

function OperationTaskChainEntranceView:OnFlush(param_t)
	self:RefreshView()
	self:FlushAutoEnterCountDown()
end

function OperationTaskChainEntranceView:RefreshView()
	if CountDownManager.Instance:HasCountDown("task_chain_entrance_act_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_entrance_act_timer")
	end

	local is_open = false
	local act_info = OperationTaskChainWGData.Instance:GetTaskChainActStatusInfo()
	local is_done = OperationTaskChainWGData.Instance:GetTaskChainIsDone()
	if act_info ~= nil and not is_done then
		if not is_done then
			local count_time = act_info.next_time - TimeWGCtrl.Instance:GetServerTime()
			if count_time > 0 then
				self.node_list.str_time.text.text = TimeUtil.FormatSecondDHM2(count_time)
				CountDownManager.Instance:AddCountDown("task_chain_entrance_act_timer",
					BindTool.Bind1(self.FlushTaskChainActTime, self), nil, act_info.next_time, nil, 1)
			end
			is_open = act_info.status ~= ACTIVITY_STATUS.CLOSE
		end
	end

	self.node_list.root_time:SetActive(is_open)
	if not is_open then
		self.node_list.str_time.text.text = ""
	end

	local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
	if day_index_cfg ~= nil then
		local open_str = Language.OpertionAcitvity.TaskChain.TaskChainOpenStr
		local open_tab = Split(day_index_cfg.task_chain_start, "|")
		if open_tab ~= nil and next(open_tab) ~= nil then
			for i = 1, #open_tab do
				open_str = open_str .. string.sub(open_tab[i], 1, 2) .. ":" .. string.sub(open_tab[i], 3, 4)
				if i < #open_tab then
					open_str = open_str .. ","
				end
			end
		end

		local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
		if task_chain_cfg ~= nil then
			self.btn_str = task_chain_cfg.btn_str
			self.node_list.str_task_name.text.text = task_chain_cfg.task_chain_name
			self.node_list.str_task_desc.text.text = task_chain_cfg.desc
			self.node_list.str_btn_info.text.text = task_chain_cfg.btn_str
			self:FlushDisplayModel(task_chain_cfg)
		end

		self.node_list.title_img.image:LoadSprite(ResPath.GetOperationTaskChainF2("xx_task_title_" ..
		day_index_cfg.task_chain_id))
	end

	self.node_list.btn_go:SetActive(is_open)

	self:FlushTaskChainTaskInfo()
	self:FlushTaskChainShowReward(is_done)
end

function OperationTaskChainEntranceView:FlushAutoEnterCountDown()
	if CountDownManager.Instance:HasCountDown("task_chain_entrance_timer") then
		CountDownManager.Instance:RemoveCountDown("task_chain_entrance_timer")
	end

	local is_open = OperationTaskChainWGData.Instance:GetTaskChainIsOpen()
	if is_open then
		local count_time = 5
		self.node_list.str_btn_info.text.text = string.format(
		Language.OpertionAcitvity.TaskChain.TaskChainBtnCountDownStr, self.btn_str, count_time)
		CountDownManager.Instance:AddCountDown("task_chain_entrance_timer", BindTool.Bind1(self.FlushAutoClick, self),
			BindTool.Bind(self.CompleteAutoClick, self), nil, count_time, 1)
	end
end

function OperationTaskChainEntranceView:FlushTaskChainShowReward(is_done)
	local data_list = {}
	if is_done then
		local task_chain_interface_cfg = OperationTaskChainWGData.Instance:GetTaskChainInterfaceCfg()
		if task_chain_interface_cfg ~= nil then
			data_list = { { item_id = task_chain_interface_cfg.item_id } }
		end
	else
		data_list = OperationTaskChainWGData.Instance:GetShowRewardList()
	end

	self.reward_grid:SetDataList(data_list)
end

function OperationTaskChainEntranceView:FlushTaskChainTaskInfo()
	local is_need_load = false
	if self.task_chain_task_list == nil then
		is_need_load = true
	end

	local show_list = OperationTaskChainWGData.Instance:GetShowTaskList()
	if show_list == nil or #show_list == 0 then
		return
	end

	local show_num = #show_list

	if not self.task_chain_task_list or show_num > #self.task_chain_task_list then
		local asset, bundle = ResPath.GetOperationTaskChainPrefabF2("render_task_chain_icon")
		local res_async_loader = AllocResAsyncLoader(self, "render_task_chain_icon")
		res_async_loader:Load(asset, bundle, nil,
			function(new_obj)
				if nil == new_obj then
					return
				end

				local task_list = self.task_chain_task_list or {}
				local cell_root = nil
				for i = #task_list + 1, show_num do
					cell_root = self.node_list["task_root_" .. i]
					if cell_root then
						local obj = ResMgr:Instantiate(new_obj)
						obj.transform:SetParent(cell_root.transform, false)
						task_list[#task_list + 1] = TaskChainTaskInfoRender.New(obj)
					end
				end
				self.task_chain_task_list = task_list

				for k, v in pairs(task_list) do
					v:SetData(show_list[k])
				end
			end)
	else
		for i = 1, show_num do
			if self.task_chain_task_list[i] ~= nil then
				self.task_chain_task_list[i]:SetActive(true)
				self.task_chain_task_list[i]:SetData(show_list[i])
			end
		end

		if show_num < #self.task_chain_task_list then
			for i = show_num, #self.task_chain_task_list do
				self.task_chain_task_list[i]:SetActive(false)
			end
		end
	end
end

function OperationTaskChainEntranceView:FlushDisplayModel(task_chain_cfg)
	if not self.display_model or not task_chain_cfg then
		return
	end

	if task_chain_cfg.npc_view_pos then
		local model_pos = Split(task_chain_cfg.npc_view_pos, "|")
		if model_pos then
			RectTransform.SetAnchoredPositionXY(self.node_list.root_obj.rect, model_pos[1] or 0, model_pos[2] or 0)
		end
	end

	if task_chain_cfg.npc_view_scale then
		local scale = tonumber(task_chain_cfg.npc_view_scale) or 1
		Transform.SetLocalScaleXYZ(self.node_list.root_obj.transform, scale, scale, scale)
	end

	local ts_appe_image_id = task_chain_cfg.ts_appe_image_id
	if ts_appe_image_id and ts_appe_image_id > 0 then
		local ts_cfg = TianShenWGData.Instance:GetImageModelByAppeId(ts_appe_image_id)
		if ts_cfg then
			self.display_model:SetTianShenModel(ts_cfg.appe_image_id, ts_cfg.index, true, ts_cfg.show_audio,
				SceneObjAnimator.Rest)
		end
		return
	end

	if task_chain_cfg.res_id > 0 then
		local bundle, asset = ResPath.GetNpcModel(task_chain_cfg.res_id)
		self.display_model:SetMainAsset(bundle, asset)
		self.display_model:PlayMonsterAction()
	end
end

function OperationTaskChainEntranceView:FlushTaskChainActTime(elapse_time, total_time)
	if self.node_list.str_time ~= nil then
		local value = math.floor(total_time - elapse_time)
		self.node_list.str_time.text.text = TimeUtil.FormatSecondDHM2(value)
	end
end

function OperationTaskChainEntranceView:FlushAutoClick(elapse_time, total_time)
	local value = math.ceil(total_time - elapse_time)
	self.node_list.str_btn_info.text.text = string.format(Language.OpertionAcitvity.TaskChain.TaskChainBtnCountDownStr,
		self.btn_str, value)
end

function OperationTaskChainEntranceView:CompleteAutoClick()
	local is_open = OperationTaskChainWGData.Instance:GetTaskChainIsOpen()
	if is_open then
		CrossServerWGCtrl.SendCrossStartReq(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN)
	end

	self:Close()
end
