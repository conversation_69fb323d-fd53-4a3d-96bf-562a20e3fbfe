TianShuWGData = TianShuWGData or BaseClass()

function TianShuWGData:__init()
	if TianShuWGData.Instance ~= nil then
		print("[TianShuWGData] attempt to create singleton twice!")
		return
	end
	TianShuWGData.Instance = self

	local config = ConfigManager.Instance:GetAutoConfig("tianshuxunzhu_cfg_auto")
	self.tianshu_config = config.runetime_use
	self.tumo = config.tumo
	self.goal_fetch_flag_list_t = {}
	self.goal_can_fetch_flag_list_t = {}
	self.kill_boss_id_list = {}
end

function TianShuWGData:__delete()
	TianShuWGData.Instance = nil
end

function TianShuWGData:SetTianshuXunzhuInfo(protocol)
	self.tianshu_xunzhu_goal_fetch_flag_list = protocol.tianshu_xunzhu_goal_fetch_flag_list
	self.tianshu_xunzhu_goal_can_fetch_flag_list = protocol.tianshu_xunzhu_goal_can_fetch_flag_list

	for i = 1, GameEnum.TIANSHU_MAX_TYPE do
		self.goal_fetch_flag_list_t[i] = bit:d2b(self.tianshu_xunzhu_goal_fetch_flag_list[i])
		self.goal_can_fetch_flag_list_t[i] = bit:d2b(self.tianshu_xunzhu_goal_can_fetch_flag_list[i])
	end
	RemindManager.Instance:Fire(RemindName.OpenServer)
end

function TianShuWGData:SetRoleKillBossIDList(protocol)
	self.count = protocol.count
	self.kill_boss_id_list = protocol.kill_boss_id_list
end

function TianShuWGData:GetFetchFlagList()
	return self.goal_fetch_flag_list_t, self.goal_can_fetch_flag_list_t
end
--获取每个卷轴的数据配置(除了成长卷轴)
function TianShuWGData:GetTisnShuDataListByIndex(index)
	if nil == next(self.goal_fetch_flag_list_t) or nil == next(self.goal_can_fetch_flag_list_t) or nil == index
		or index < 1 or index > GameEnum.TIANSHU_MAX_TYPE then return nil end
	local prof = RoleWGData.Instance:GetRoleProf()
	local config = self.tianshu_config[index]
	local goal_count = config.goal_count or 0
	local param1_list = Split(config.param1_list, "|")
	local param2_list = Split(config.param2_list, "|")
	local reward_param_list = Split(config.reward_param_list, "|")
	local recommend = Split(config["recommend" .. prof], "|")
	local seq_list = Split(config.seq, "|")
	local recommend_list = {}

	for i = 1, goal_count do
		recommend_list[i] = Split(recommend[i], ",")
	end
	local data_list = {}
	for i = 1, goal_count do
		local data = {}
		data.index = index
		data.param1 = tonumber(param1_list[i])
		data.param2 = tonumber(param2_list[i])
		data.reward = tonumber(reward_param_list[i])
		data.seq = tonumber(seq_list[i])
		data.desc = config.describe
		data.fetch_flag = self.goal_fetch_flag_list_t[index][33 - i]
		data.final_fetch_flag = self.goal_fetch_flag_list_t[index][1]
		data.can_fetch_flag = self.goal_can_fetch_flag_list_t[index][33 - i]
		data.recommend_t = {}
		for j = 1, #recommend_list[i] do
			data.recommend_t[j] = tonumber(recommend_list[i][j])
			data["is_boss_killed_index_" .. j] = (nil ~= self.kill_boss_id_list[data.recommend_t[j]] )
		end
		data_list[i] = data
	end
	if index~=3 then
		return self:SortList(data_list,index)
	end
	table.sort(data_list, function(a, b)
		if a.fetch_flag ~= b.fetch_flag then
			return a.fetch_flag < b.fetch_flag
		else
			return a.can_fetch_flag > b.can_fetch_flag
		end
	end )
	return data_list
end

--获取屠魔跳转界面的相关索引
function TianShuWGData:GetTuMoViewIndex(monster_id,task_index)
	-- print_error(monster1_id,monster2_id)
	if monster_id ~= nil and task_index ~= nil then
		for k,v in pairs(self.tumo) do
			if monster_id == v.monster1_id or monster_id == v.monster2_id then
				local str = string.split(v.location, "|")
				return v.boss_type, v.tier, tonumber(str[task_index])
			end
		end
	end
	return 11,0,1
end

function TianShuWGData:SortList(date_list,index)
	local data_list = date_list
	local index = index
	local templist = {}
	local numlist = {}
	if index == 2 then
		table.sort( data_list, SortTools.KeyTianShuSorters("can_fetch_flag","fetch_flag","param1")) 
	else
		table.sort( data_list, SortTools.KeyTianShuSorters("can_fetch_flag","fetch_flag","reward"))
	end
	for k,v in pairs(data_list) do
		if v.can_fetch_flag==1 and v.fetch_flag == 1 then
			table.insert(templist,v)
			table.insert(numlist,k)
		end
	end
	for k,v in pairs(numlist) do
		table.remove(data_list,v-k+1)
	end
	for k,v in pairs(templist) do
		table.insert( data_list, v)
	end
	return data_list
end





function TianShuWGData:GetFinalRewardByIndex(index)
	if nil == next(self.goal_fetch_flag_list_t) or nil == next(self.goal_can_fetch_flag_list_t) or nil == index
		or index < 1 or index > GameEnum.TIANSHU_MAX_TYPE then return nil, false, false end

	local config = self.tianshu_config[index]
	local goal_count = config.goal_count or 0
	local final_reward_item = config.final_reward_item
	local fetch_flag = 1 == self.goal_fetch_flag_list_t[index][1]
	local can_fetch_flag = 1 == self.goal_can_fetch_flag_list_t[index][1]
	return final_reward_item, fetch_flag, can_fetch_flag
end

function TianShuWGData:GetAllFetchFlag()
	if nil == next(self.goal_fetch_flag_list_t) then return nil end

	local all_fetch_flag_t = {}
	for i = 1, GameEnum.TIANSHU_MAX_TYPE do
		all_fetch_flag_t[i] = (1 == self.goal_fetch_flag_list_t[i][1])
	end

	return all_fetch_flag_t
end

function TianShuWGData:GetRemindByIndex(index)
	if nil == next(self.goal_fetch_flag_list_t) or nil == next(self.goal_can_fetch_flag_list_t) or nil == index
		or index < 1 or index > GameEnum.TIANSHU_MAX_TYPE then return 0 end

	local config = self.tianshu_config[index]
	local goal_count = config.goal_count or 0
	local remind_num = 0

	if index > 1 then
		local all_flag = self:GetAllFetchFlag()
		local fetch_flag = all_flag[index - 1]
		if not fetch_flag then
			return 0
		end
	end

	for i = 1, goal_count do
		remind_num = remind_num + (self.goal_fetch_flag_list_t[index][33 - i] < self.goal_can_fetch_flag_list_t[index][33 - i] and 1 or 0)
	end
	remind_num = remind_num + (self.goal_fetch_flag_list_t[index][1] < self.goal_can_fetch_flag_list_t[index][1] and 1 or 0)

	return remind_num
end

function TianShuWGData:GetAllRemind()
	if not TaskWGData.Instance:GetTaskIsCompleted(TianShuWGData.Instance:GetTianshuPerTaskId()) then
		return 0
	end
	local remind_num = 0
	for index = 1, GameEnum.TIANSHU_MAX_TYPE do
		remind_num = remind_num + self:GetRemindByIndex(index)
	end
	return remind_num
end

function TianShuWGData:IsGetAllReward()
	local grow_up_book = false
	local other_book = true
	if self.chengzhang_flag_list ~= nil and self.chengzhang_flag_list[31] ~= nil then 
		grow_up_book = self.chengzhang_flag_list[31].fetch_flag > 0
	end
	if self.goal_fetch_flag_list_t ~= nil and self.goal_fetch_flag_list_t[GameEnum.TIANSHU_MAX_TYPE] ~= nil and self.goal_fetch_flag_list_t[GameEnum.TIANSHU_MAX_TYPE][1] ~= nil then
		other_book = self.goal_fetch_flag_list_t[GameEnum.TIANSHU_MAX_TYPE][1] > 0
	end
	return (other_book and grow_up_book) and true or false
end
--用于七天登录模块关闭开服按钮的时候做判断
function TianShuWGData:GetTianShuIsClose()
	if TaskWGData.Instance:GetTaskIsCompleted(self:GetTianshuPerTaskId()) and not self:IsGetAllReward() then
		return false
	end
	return true
end

function TianShuWGData:GetCurIndex()
	if nil == self.goal_fetch_flag_list_t then return 1 end

	for i = 1, GameEnum.TIANSHU_MAX_TYPE do
		if 0 == self.goal_fetch_flag_list_t[i][1] then
			return i
		end
	end
	return 1
end

function TianShuWGData:GetTabIndexAndIndexByBossId(boss_id)
	local boss_list = BossWGData.Instance:GetWorldBossList()
	for k, v in pairs(boss_list) do
		if boss_id == v.boss_id then
			return {tab_index = 1, page_index = math.ceil((k + 1) / 8), select_index = k}
		end
	end

	return nil
end

function TianShuWGData:GetBossInfoById(boss_id)
	local boss_info = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[boss_id]
	return boss_info
end

function TianShuWGData:GetTianshuPerTaskId()
	local other_cfg = ConfigManager.Instance:GetAutoConfig("tianshuxunzhu_cfg_auto").other[1]
	return other_cfg.tianshu_open_task_id
end

function TianShuWGData:GetTianShuIsFetch(value)
	if value + 1 == GameEnum.TIANSHU_CHENGZHANG_TYPE then
		if self.chengzhang_flag_list == nil or self.chengzhang_flag_list[31] == nil then return false end
		return self.chengzhang_flag_list[31].fetch_flag == 1
	else
		if self.goal_fetch_flag_list_t[value + 1] == nil then return false end
		return self.goal_fetch_flag_list_t[value + 1][1] == 1
	end
end


function TianShuWGData:SetChengZhangTianShuInfo(protocol)
	local fetch_flag_list = bit:d2b(protocol.fetch_flag_list)
	local act_flag_list = bit:d2b(protocol.act_flag_list)

	self.chengzhang_flag_list = {}
	for k, v in ipairs(fetch_flag_list)do
		self.chengzhang_flag_list[k - 1] = {
			fetch_flag = fetch_flag_list[33 - k],
			act_flag = act_flag_list[33 - k],
		}
	end
	RemindManager.Instance:Fire(RemindName.OpenServer)

end


function TianShuWGData:GetChengZhangFinalRewardByIndex()
	if self.chengzhang_flag_list == nil then return "" , false, false end
	local fetch_flag = 1 == self.chengzhang_flag_list[31].fetch_flag
	local can_fetch_flag = 1 == self.chengzhang_flag_list[31].act_flag

	return Language.TianShuXunZhu.ChengZhangFinalText, fetch_flag, can_fetch_flag
end

function TianShuWGData:GetChengZhangTisnShuDataListByIndex()
	if self.chengzhang_flag_list == nil then return {} end
	local chengzhang_cfg = ConfigManager.Instance:GetAutoConfig("tianshuxunzhu_cfg_auto").chengzhang
	local chengzhang_list = {}
	for k,v in ipairs(chengzhang_cfg)do
		--成长k 6个条件.v:条件内容
		if self.chengzhang_flag_list[v.seq] ~= nil then
			local item_data = __TableCopy(v)
			item_data.fetch_flag = self.chengzhang_flag_list[v.seq].fetch_flag --已完成
			item_data.can_fetch_flag = self.chengzhang_flag_list[v.seq].act_flag--可完成
			item_data.index = GameEnum.TIANSHU_CHENGZHANG_TYPE
			chengzhang_list[#chengzhang_list + 1] = item_data

		end
	end

	--table.sort 排序,完成的靠后
	table.sort(chengzhang_list, function(a, b)
		if a.fetch_flag ~= b.fetch_flag then
			return a.fetch_flag < b.fetch_flag
		else
			return a.can_fetch_flag > b.can_fetch_flag
		end
	end)
	return chengzhang_list
end

function TianShuWGData:GetChengZhangTisnShuRemindByIndex()
	if self.chengzhang_flag_list == nil then return 0 end

	for k = #self.chengzhang_flag_list, 0, -1 do
		if self.chengzhang_flag_list[k].fetch_flag ~= self.chengzhang_flag_list[k].act_flag then
			return 1
		end
	end
	return 0
end

function TianShuWGData:GetSkillId(view_index)
	local skill_info_cfg = ConfigManager.Instance:GetAutoConfig("tianshuxunzhu_cfg_auto").skill_info
	for k,v in pairs(skill_info_cfg) do
		if v.view_index == view_index then
			return v.skill_id
		end
	end
	return 0
end