local SCORE_SLIDER = {
    [0] = 0.22,
    [1] = 0.44,
    [2] = 0.67,
    [3] = 0.89,
    [4] = 1,
}
function LoverPkView:LoadQCDJCallBack()
    self.qcdj_left_model = RoleModel.New()
    local qcdj_left_display_data = {
        parent_node = self.node_list["qcdj_left_model"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }

    self.qcdj_left_model:SetRenderTexUI3DModel(qcdj_left_display_data)
    -- self.qcdj_left_model:SetUI3DModel(self.node_list.qcdj_left_model.transform, self.node_list.qcdj_left_model.event_trigger_listener,
    --     1, false, MODEL_CAMERA_TYPE.BASE)
    self:AddUiRoleModel(self.qcdj_left_model)

    self.qcdj_right_model = RoleModel.New()
    local qcdj_right_display_data = {
        parent_node = self.node_list["qcdj_right_model"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }
    self.qcdj_right_model:SetRenderTexUI3DModel(qcdj_right_display_data)
    -- self.qcdj_right_model:SetUI3DModel(self.node_list.qcdj_right_model.transform, self.node_list.qcdj_right_model.event_trigger_listener,
    --     1, false, MODEL_CAMERA_TYPE.BASE)
    self:AddUiRoleModel(self.qcdj_right_model)

    XUI.AddClickEventListener(self.node_list["tip_btn"], BindTool.Bind(self.ClickTipsBtn,self))
    XUI.AddClickEventListener(self.node_list["look_kf_btn"], BindTool.Bind(self.ClickLookKF,self))
    XUI.AddClickEventListener(self.node_list["btn_reward"], BindTool.Bind(self.ClickRewardBtn,self))
    XUI.AddClickEventListener(self.node_list["btn_qcdj_matching"], BindTool.Bind(self.ClickQCDJMatchIngBtn,self))

    self.score_slider_rewards = {}
    for i = 1, 5 do
        self.score_slider_rewards[i] = QCDJScoreRewardItem.New(self.node_list["ph_reward_" .. i])
    end

    self.rank_list = AsyncListView.New(QCDJRankItem, self.node_list["rank_list"])
    
    self.node_list.qcdj_time_tips.text.text = Language.LoverPK.QCDJOpenTimeDesc
    self.qcdj_left_model_cache = -1
    self.qcdj_right_model_cache = -1

    self.node_list.qcdj_no_model_left:CustomSetActive(true)
    self.node_list.qcdj_no_model_right:CustomSetActive(true)
    self.node_list.qcdj_left_name:CustomSetActive(false)
    self.node_list.qcdj_right_name:CustomSetActive(false)
end

function LoverPkView:ReleaseQCDJCallBack()
    if not IsEmptyTable(self.score_slider_rewards) then
        for k,v in pairs(self.score_slider_rewards) do
            if v then
                v:DeleteMe()
            end
        end
        self.score_slider_rewards = {}
    end

    if self.rank_list then
        self.rank_list:DeleteMe()
        self.rank_list = nil
    end

    if self.qcdj_left_model then
        self.qcdj_left_model:DeleteMe()
        self.qcdj_left_model = nil
    end

    if self.qcdj_right_model then
        self.qcdj_right_model:DeleteMe()
        self.qcdj_right_model = nil
    end

    self.qcdj_left_model_cache = nil
    self.qcdj_right_model_cache = nil
end

function LoverPkView:FlushQCDJCallBack()
    local rank_list, my_rank_data = LoverPkWGData.Instance:GetRegularSeasonRankDataListAndMyInfo()
    self.rank_list:SetDataList(rank_list)
    self.node_list.common_no_data_panel:SetActive(IsEmptyTable(rank_list))
    self:FlushQCDJMyRankInfo(my_rank_data)
    self:FlushQCDJModelInfo()

    for i = 1, 5 do
        local data = LoverPkWGData.Instance:GetMatchCountRewardCfgBySeq(i - 1)
        self.score_slider_rewards[i]:SetData(data)
    end

    self.node_list["reward_progress"].slider.value = self:GetQCDJSliderValue()
end

function LoverPkView:GetQCDJSliderValue()
    local value = 0
    local match_count = LoverPkWGData.Instance:GetCrossCoupleMatchCount()
    local mecha_count_reward_cfg = LoverPkWGData.Instance:GetMatchCountRewardCfg()
    local last_cfg = {}

    if not IsEmptyTable(mecha_count_reward_cfg) then
        for k, v in pairs(mecha_count_reward_cfg) do
            if match_count >= v.match_count then
                value = SCORE_SLIDER[v.seq] > value and SCORE_SLIDER[v.seq] or value
                last_cfg = v
            else
                local last_seq = last_cfg and last_cfg.seq or -1
                local last_value = SCORE_SLIDER[last_seq] or 0
                local last_need_count = last_cfg and last_cfg.match_count or 0
                local diff = SCORE_SLIDER[v.seq] - last_value

                value = last_value + diff * (match_count - last_need_count) / (v.match_count - last_need_count)
                break
            end
        end
    end

    return value
end

function LoverPkView:FlushQCDJMyRankInfo(my_rank_info)
    local info_data = my_rank_info.info
    local has_data = not IsEmptyTable(info_data)
    local rank_id = has_data and info_data.rank_id or -1
    local rank_str = has_data and string.format(Language.LoverPK.RankValueStr, rank_id) or Language.LoverPK.NotInRank
    self.node_list.my_rank_text.text.text = string.format(Language.LoverPK.RankStr, rank_str)
    self.node_list.my_rank_score.text.text = has_data and info_data.score or "-"
end

function LoverPkView:ClickTipsBtn()
    RuleTip.Instance:SetContent(Language.LoverPK.QCDJTipContent, Language.LoverPK.QCDJTipTitle)
end

function LoverPkView:ClickRewardBtn()
    LoverPkWGCtrl.Instance:OpenLoverPKMsgView(TabIndex.lover_pk_msg_jbjl)
end

function LoverPkView:ClickQCDJMatchIngBtn()
    local act_isopen = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
    if not act_isopen then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.ActNotOpenCannotEnter)
        return
    end

    -- local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
    -- if lover_id <= 0 then
    --     SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.NoLoverCannotEnter)
    --     return
    -- end

    local cur_scene_type = Scene.Instance:GetSceneType()
    if cur_scene_type == SceneType.CROSS_PK_LOVER_READY then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.LoverPK.RoleIsInThePreparScene)
        return
    end
 
    CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.CROSS_ACTIVITY_TYPE_LOVERPK)
    self:Close()
end

function LoverPkView:ClickLookKF()
end

function LoverPkView:FlushQCDJModelInfo()
    local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true}
    local sex = RoleWGData.Instance:GetRoleSex()
    local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
    local my_model_render = (sex == GameEnum.FEMALE and self.qcdj_right_model or self.qcdj_left_model)
    local other_model_render = (sex == GameEnum.FEMALE and self.qcdj_left_model or self.qcdj_right_model)

    local set_model_data = function (role_vo, model_render, is_main_role)
        local my_score, other_score = LoverPkWGData.Instance:GetLoverPKQCDJScore()
        local role_name = role_vo.role_name
        local is_dianfeng, show_level = RoleWGData.Instance:GetDianFengLevel(role_vo.level)
        local role_level_str = show_level .. Language.Tip.Ji .. string.format(Language.LoverPK.QCDJRoleScore, is_main_role and my_score or other_score)

        if model_render == self.qcdj_right_model then
            self.node_list.qcdj_no_model_right:CustomSetActive(false)
            self.node_list.qcdj_right_name:CustomSetActive(true)
            self.node_list.qcdj_right_name_text.text.text = role_name
            self.node_list.qcdj_right_dianfeng_img:CustomSetActive(is_dianfeng)
            self.node_list.qcdj_right_level.text.text = role_level_str
        else
            self.node_list.qcdj_no_model_left:CustomSetActive(false)
            self.node_list.qcdj_left_name:CustomSetActive(true)
            self.node_list.qcdj_left_name_text.text.text = role_name
            self.node_list.qcdj_left_dianfeng_img:CustomSetActive(is_dianfeng)
            self.node_list.qcdj_left_level.text.text = role_level_str
        end

        if (model_render == self.qcdj_left_model and self.qcdj_left_model_cache ~= role_vo.role_id) or (model_render == self.qcdj_right_model and self.qcdj_right_model_cache ~= role_vo.role_id) then
            if model_render == self.qcdj_left_model then
                self.qcdj_left_model_cache = role_vo.role_id
            elseif model_render == self.qcdj_right_model then
                self.qcdj_right_model_cache = role_vo.role_id
            end

            model_render:SetModelResInfo(role_vo, ignore_table, function()
            end)
            model_render:FixToOrthographic(self.root_node_transform)
            model_render:PlayRoleAction(SceneObjAnimator.UiIdle)
        else
            model_render:PlayLastAction()
        end
    end

    local role_vo = RoleWGData.Instance:GetRoleVo()
    set_model_data(role_vo, my_model_render, true)

    if lover_id > 0 then
        BrowseWGCtrl.Instance:BrowRoelInfo(lover_id, function (protocol)
            set_model_data(protocol, other_model_render, false)
        end)
    else
        if sex == GameEnum.FEMALE then
            self.node_list.qcdj_no_model_left:CustomSetActive(true)
            self.node_list.qcdj_left_name:CustomSetActive(false)
        else
            self.node_list.qcdj_no_model_right:CustomSetActive(true)
            self.node_list.qcdj_right_name:CustomSetActive(false)
        end
    end
end

--------------------------------QCDJRankItem--------------------------------
QCDJRankItem = QCDJRankItem or BaseClass(BaseRender)

function QCDJRankItem:OnFlush()
    if IsEmptyTable(self.data) then return end
    local rank_num = self.data.rank_id
    
    if rank_num <= 3 then
        self.node_list.bg.image:LoadSprite(ResPath.GetLoverPkImg("a2_xlpk_shq_yeqian" .. rank_num))
    else
        self.node_list.bg.image:LoadSprite(ResPath.GetLoverPkImg("a2_xlpk_shq_yeqian4"))
    end

    self.node_list["rank_icon"]:SetActive(rank_num <= 3)
    self.node_list.rank_index:SetActive(rank_num > 3)
    self.node_list.bg:SetActive(rank_num <= 3 or rank_num % 2 == 0)
    self.node_list.rank_index_top.text.text = rank_num

    if rank_num <= 3 then
        self.node_list["rank_icon"].image:LoadSprite(ResPath.GetCommonIcon("a2_pm_"..rank_num))
        self.node_list["rank_icon"].image:SetNativeSize()
    else
        self.node_list.rank_index.text.text = rank_num
    end

    local info = self.data.info
    local has_data = not IsEmptyTable(info)

    self.node_list["score"].text.text = has_data and info.score or "-"
    self.node_list["name2"].text.text = has_data and info.name1 or Language.LoverPK.XuWeiYiDai
    self.node_list["name1"].text.text = has_data and info.name2 or Language.LoverPK.XuWeiYiDai
end

---------------------------------QCDJScoreRewardItem-----------------------------------
QCDJScoreRewardItem = QCDJScoreRewardItem or BaseClass(BaseRender)

function QCDJScoreRewardItem:LoadCallBack()
    XUI.AddClickEventListener(self.node_list["ph_reward"], BindTool.Bind(self.OnClickCellCallBack, self))
end

function QCDJScoreRewardItem:OnFlush()
    if IsEmptyTable(self.data) then
        return
    end

    local reward_item = self.data.reward_list[0]
    local item_cfg = ItemWGData.Instance:GetItemConfig(reward_item.item_id)
    self.node_list.icon_img.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    self.node_list["count"].text.text = self.data.match_count

    local can_get , is_get = LoverPkWGData.Instance:IsCanGetMatchCountRewardFlag(self.data.seq)
    self.node_list["redpoint"]:SetActive(can_get)
    self.node_list["get_flag"]:SetActive(is_get)
end

function QCDJScoreRewardItem:OnClickCellCallBack()
    if IsEmptyTable(self.data) then
        return
    end

    local can_get = LoverPkWGData.Instance:IsCanGetMatchCountRewardFlag(self.data.seq)
    if can_get then
        LoverPkWGCtrl.Instance:SendCSCrossCouple2V2Operate(CROSS_COUPLE_2V2_OPERATE_TYPE.ACQUIRE_MATCH_COUNT_REWARDS, self.data.seq)
    else
        TipWGCtrl.Instance:OpenItem(self.data.reward_list[0], ItemTip.FROM_NORMAL)
    end
end