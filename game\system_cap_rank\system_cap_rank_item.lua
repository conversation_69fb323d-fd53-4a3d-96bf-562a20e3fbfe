SystemCapRankItem = SystemCapRankItem or BaseClass(BaseRender)
function SystemCapRankItem:LoadCallBack()
	self.item_cell_list = {}
end

function SystemCapRankItem:__delete()
	for _, v in pairs(self.item_cell_list) do
		v:DeleteMe()
	end
	self.item_cell_list = nil
end

function SystemCapRankItem:OnFlush()
	if not self.data then
		return
	end
	
	local desc = ""
	if self.data.min_rank == 1 then
		desc = string.format(Language.OperationActivity.RechargeRankTitle2, self.data.min_rank)
	else
		desc = string.format(Language.OperationActivity.RechargeRankTitle3, self.data.min_rank, self.data.max_rank)
	end

	self.node_list["rank_desc"].text.text = string.format(Language.SystemCapRank.SystemCapRankTitle, desc, self.data.reach_value)

	local item_list = self.item_cell_list
	if #self.data.reward_item > #item_list then
		local cell_parent = self.node_list["reward_group"]
		for i = 0, #self.data.reward_item do
			item_list[i] = item_list[i] or ItemCell.New(cell_parent)
		end
		self.item_list = item_list
	end

	for i = 0, #item_list do
		if self.data.reward_item[i] then
			item_list[i]:SetData(self.data.reward_item[i])
			item_list[i]:SetActive(true)
		else
			item_list[i]:SetActive(false)
		end
	end
end