require("game/operation_activity/ctn_recharge/operation_ctn_recharge_wg_data")

-- 运营活动-连续充值
OperationCtnRechargeWGCtrl = OperationCtnRechargeWGCtrl or BaseClass(BaseWGCtrl)

function OperationCtnRechargeWGCtrl:__init()
	if OperationCtnRechargeWGCtrl.Instance then
		ErrorLog("[OperationCtnRechargeWGCtrl] Attemp to create a singleton twice !")
	end
	OperationCtnRechargeWGCtrl.Instance = self

	self.data = OperationCtnRechargeWGData.New()
	self:RegisterAllProtocols()

	OperationActivityWGCtrl.Instance:ListenHotUpdate(OperationCtnRechargeWGData.ConfigPath, BindTool.Bind(self.OnHotUpdate, self))
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function OperationCtnRechargeWGCtrl:__delete()
	if self.delay_timer then
		GlobalTimerQuest:CancelQuest(self.delay_timer)
		self.delay_timer = nil
	end
	OperationCtnRechargeWGCtrl.Instance = nil
	self.data:DeleteMe()
	self.data = nil
end

function OperationCtnRechargeWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOACtnRechargeInfo, "OnSCOACtnRechargeInfo")
	self:RegisterProtocol(SCOACtnRechargeFetchResult, "OnSCOACtnRechargeFetchResult")
	self:RegisterProtocol(CSOACtnRechargeOpera)
end

-- 运营活动-连续充值-信息 8200
function OperationCtnRechargeWGCtrl:OnSCOACtnRechargeInfo(protocol)
	self.data:SetCtnRechargeData(protocol)
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_ctn_recharge)
	--print_error("连续充值-信息 : " , protocol)
	RemindManager.Instance:Fire(RemindName.OperationCtnRecharge)
end

-- 运营活动-连续充值-奖励展示 8202
function OperationCtnRechargeWGCtrl:OnSCOACtnRechargeFetchResult(protocol)
	if not IsEmptyTable(protocol.reward_list) then
		TipWGCtrl.Instance:ShowGetReward(nil, protocol.reward_list, false)
	end
end

-- 运营活动-连续充值-操作 8201
function OperationCtnRechargeWGCtrl:OperaActCtnRechargeOpera(type, param1)
	local protocol = ProtocolPool.Instance:GetProtocol(CSOACtnRechargeOpera)
	protocol.type = type
	protocol.param1 = param1
	protocol:EncodeAndSend()
end

function OperationCtnRechargeWGCtrl:OnPassDay()
	--做个容错，跨天的时候，服务器的数据可能还没同步过来
	self.delay_timer = GlobalTimerQuest:AddDelayTimer(function()
		OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_ctn_recharge, "RefreshDayIndex", {RefreshDayIndex = true})
		self.delay_timer = nil
	end, 1)
end

function OperationCtnRechargeWGCtrl:OnHotUpdate()
	self.data:InitCtnRechargeConfig()
	OperationActivityWGCtrl.Instance:FlushView(TabIndex.operation_act_ctn_recharge)
end