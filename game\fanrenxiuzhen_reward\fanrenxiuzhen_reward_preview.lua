-- 凡人修真奖励总览
FanrenxiuzhenRewardPreview = FanrenxiuzhenRewardPreview or BaseClass(SafeBaseView)

function FanrenxiuzhenRewardPreview:__init()
	self.view_style = ViewStyle.Full
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)
    self.is_safe_area_adapter = true


    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_panel")
    self:AddViewResource(0, "uis/view/fanrenxiuzhen_reward_ui_prefab", "fanrenxiuzhen_reward_preview")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function FanrenxiuzhenRewardPreview:__delete()
    self.cur_data = nil
    self.cur_index = nil
end

function FanrenxiuzhenRewardPreview:LoadCallBack()
    local bundle, asset = ResPath.GetRawImagesJPG("a3_fb_pt_bj1")
	if self.node_list.RawImage_tongyong then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

	-- XUI.AddClickEventListener(self.node_list["btn_close"], BindTool.Bind(self.Close, self))

	-- 奖励预览列表
	self.reward_list = AsyncListView.New(FanrenxiuzhenRewardPreItem, self.node_list["reward_list"])
    -- self.reward_list:SetStartZeroIndex(true)

	self.chapter_group_list = AsyncListView.New(FanrenxiuzhenRewardGroupItem, self.node_list["chapter_group_list"])
    self.chapter_group_list:SetSelectCallBack(BindTool.Bind1(self.OnSelectGroupCallBack, self))

end

function FanrenxiuzhenRewardPreview:ReleaseCallBack()

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

    if self.chapter_group_list then
        self.chapter_group_list:DeleteMe()
        self.chapter_group_list = nil
    end

end



function FanrenxiuzhenRewardPreview:OnFlush(param_t, index)
	for k,v in pairs(param_t) do
		if "all" == k then
            self:FlushGroupList()
            self:FlushRewardList()
		end
	end
end

function FanrenxiuzhenRewardPreview:FlushGroupList()
    local group_list = FuBenPanelWGData.Instance:GetFanrenxiuzhenRewardPreview()
    self.chapter_group_list:SetDataList(group_list)
    self.chapter_group_list:JumpToIndex(self.cur_index or 1)

    if self.cur_index then
        self.reward_list:JumpToIndex(self.cur_index,0)

    end
end

function FanrenxiuzhenRewardPreview:SetCurIndex(index)
    self.cur_index = index
end

function FanrenxiuzhenRewardPreview:OnSelectGroupCallBack(item)
    local data = item:GetData()
    self.cur_data = data
    self.cur_index = data.seq
    self:FlushRewardList()
end

function FanrenxiuzhenRewardPreview:FlushRewardList()
    local data = self.cur_data
    if data then
        local data_list = FuBenPanelWGData.Instance:GetLevelRewardCfgList(data.level_origin, data.level_end)
        self.reward_list:SetDataList(data_list)


        local pass_level = FuBenPanelWGData.Instance:GetPassLevel()

        -- 计算红点位置
        local remind_count ,index = FuBenPanelWGData.Instance:CountFanrenxiuzhenRewardRemind(data.seq)
        if remind_count > 0 then
            self.reward_list:JumpToIndex(index,3)
            return
        end
        for i = #data_list, 1, -1 do
            if pass_level < data_list[i].level then

                self.reward_list:JumpToIndex(i,3)
                return
            end
        end
    end
end

-----------------------------------------------

FanrenxiuzhenRewardGroupItem = FanrenxiuzhenRewardGroupItem or BaseClass(BaseRender)

function FanrenxiuzhenRewardGroupItem:LoadCallBack()

end

function FanrenxiuzhenRewardGroupItem:__delete()

end

function FanrenxiuzhenRewardGroupItem:OnFlush()
    self.node_list.Text.text.text = self.data.title
    self.node_list.TextHL.text.text = self.data.title

    local remind_count ,index = FuBenPanelWGData.Instance:CountFanrenxiuzhenRewardRemind(self.data.seq)
    self.node_list.RedPoint:SetActive(remind_count > 0) 

    self.node_list.HLImage:SetActive(false)
end


function FanrenxiuzhenRewardGroupItem:OnSelectChange(is_select)
    -- self.node_list.normal:SetActive(not is_select)
    self.node_list.HLImage:SetActive(is_select)
end

-----------------------------------------------

FanrenxiuzhenRewardPreItem = FanrenxiuzhenRewardPreItem or BaseClass(BaseRender)

function FanrenxiuzhenRewardPreItem:LoadCallBack()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_scroll"])
    -- self.reward_list:SetStartZeroIndex(true)

    self.hunka_item_cell = ItemCell.New(self.node_list.item_cell)

	XUI.AddClickEventListener(self.node_list["btn_receive"], BindTool.Bind(self.OnCliekReceive, self))

end

function FanrenxiuzhenRewardPreItem:__delete()
    if self.reward_list then

        self.reward_list:DeleteMe()
        self.reward_list = nil
    end

    if self.hunka_item_cell then
        self.hunka_item_cell:DeleteMe()
        self.hunka_item_cell = nil
    end
end

function FanrenxiuzhenRewardPreItem:OnFlush()
    local chapter_cfg = FuBenPanelWGData.Instance:GetFanrenxiuzhenChapterCfgByLevel(self.data.level)

    local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
    local is_receive = FuBenPanelWGData.Instance:GetLevelRewardFlag(self.data.seq)
    local is_pass = pass_level >= self.data.level
    local last_level_reward = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdDataBySeq(self.data.seq - 1)
    local is_lock = last_level_reward and pass_level < last_level_reward.level

    
    self.node_list.img_bg_1:SetActive(self.index ~= 1)
    self.node_list.img_bg_2:SetActive(self.index == 1)
    self.node_list.img_pass:SetActive(is_pass)
    self.node_list.remind:SetActive(is_pass and not is_receive)
    self.node_list.btn_receive:SetActive(is_pass and not is_receive)
    self.node_list.img_battle:SetActive(not is_pass and not is_lock)
    self.node_list.img_lock:SetActive(is_lock)


    if self.data.seq % 2 ==0 then
        self.node_list.reward_bg.rect.anchoredPosition = Vector2(-384,34)
        self.node_list.img_line_right:SetActive(false)
        self.node_list.img_line_left:SetActive(true)
    else
        self.node_list.reward_bg.rect.anchoredPosition = Vector2(379,34)
        self.node_list.img_line_right:SetActive(true)
        self.node_list.img_line_left:SetActive(false)
    end

    self.node_list.text_level.text.text = self.data.level
    self.node_list.text_title.text.text = self.data.chapter_title

    -- 阶段奖励

    local reawrd_data_list = {}
    for i,v in pairs(self.data.reward_item) do
        local item_data = {}
        item_data.is_ylq = is_receive
        item_data.item_id = v.item_id
        item_data.num = v.num
        item_data.bind = v.bind
        table.insert(reawrd_data_list, item_data)
    end
    self.reward_list:SetDataList(reawrd_data_list)

    if chapter_cfg and chapter_cfg.client_chapter_item[0] then
        -- 章节奖励
        local item_data = {}
        -- item_data.is_ylq = is_receive
        item_data.item_id = chapter_cfg.client_chapter_item[0].item_id
        item_data.num = chapter_cfg.client_chapter_item[0].num
        item_data.bind = chapter_cfg.client_chapter_item[0].bind
        self.hunka_item_cell:SetData(item_data)
        self.node_list.img_unlock:SetActive(is_receive)

        self.node_list.item_cell:SetActive(true)
        self.node_list.img_add:SetActive(true)

        self.node_list.reward_scroll.rect.sizeDelta = Vector2(266, 90)

    else
        self.node_list.item_cell:SetActive(false)
        self.node_list.img_add:SetActive(false)

        self.node_list.reward_scroll.rect.sizeDelta = Vector2(382, 90)
        self.node_list.img_unlock:SetActive(false)

    end



    local bundle, asset
    if self.index == 1 then
       
        if (is_pass and is_receive) or is_lock then
            bundle, asset = ResPath.GetRawImagesPNG("a3_fb_pt_ta6")
        else
            bundle, asset = ResPath.GetRawImagesPNG("a3_fb_pt_ta4")
        end

        if self.node_list.img_bg_1 then
            self.node_list["img_bg_2"].raw_image:LoadSprite(bundle, asset, function()
                self.node_list["img_bg_2"].raw_image:SetNativeSize()
            end)
        end
    else
        if (is_pass and is_receive) or is_lock then
            bundle, asset = ResPath.GetRawImagesPNG("a3_fb_pt_ta3")
        else
            bundle, asset = ResPath.GetRawImagesPNG("a3_fb_pt_ta1")
        end

        if self.node_list.img_bg_1 then
            self.node_list["img_bg_1"].raw_image:LoadSprite(bundle, asset, function()
                self.node_list["img_bg_1"].raw_image:SetNativeSize()
            end)
        end
    end

end



function FanrenxiuzhenRewardPreItem:OnCliekReceive()
    
    if self.data then
        local level_reward_cfg = FuBenPanelWGData.Instance:GetFanrenxiuzhenLevelRwawrdDataByLevel(self.data.level)
        FuBenPanelWGCtrl.Instance:SendPataFbRewrad(level_reward_cfg.seq)
    end

end




