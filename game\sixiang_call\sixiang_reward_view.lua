SiXiangRewardView = SiXiangRewardView or BaseClass(SafeBaseView)

function SiXiangRewardView:__init(view_name)
	self.view_name = "SiXiangRewardView"
	self.view_layer = UiLayer.Pop
	self.mask_alpha = MASK_BG_ALPHA_TYPE.Normal
	self:AddViewResource(0, "uis/view/sixiang_call_prefab", "layout_sixiang_reward")
	self:SetMaskBg(true, true)
end

function SiXiangRewardView:LoadCallBack()
	self:InitParam()
	self:InitPanel()
	self:InitListener()
	self:InitRewardList()
end

function SiXiangRewardView:ReleaseCallBack()
	if self.reward_item_list then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end
	if self.once_summon_item then
		self.once_summon_item:DeleteMe()
		self.once_summon_item = nil
	end
end

function SiXiangRewardView:CloseCallBack()
	self:StopTween()
end

function SiXiangRewardView:OnFlush(param_t)
	if param_t.summon_reward then
		self:RefreshView()
		if self.is_shilian then
			self:PlayTenRewardTween(true)
		else
			self:PlayOneRewardTween(true)
		end
	end
end

function SiXiangRewardView:InitParam()
	self.need_ready_tween = true
	self.reward_item_list = nil
	self.play_tween_index = 0
	self.is_shilian = false
	self.is_play_ten_tween = false
	-- self.is_play_one_tween = false
end

function SiXiangRewardView:InitPanel()
	self.once_summon_item = SiXiangRewardItem.New()
	self.once_summon_item:DoLoad(self.node_list.once_item_root)
end

function SiXiangRewardView:InitListener()
	XUI.AddClickEventListener(self.node_list.sure_btn, BindTool.Bind1(self.OnClickSureBtn, self))
	XUI.AddClickEventListener(self.node_list.draw_btn, BindTool.Bind1(self.OnClickDrawBtn, self))
end

function SiXiangRewardView:InitRewardList()
	local res_async_loader = AllocResAsyncLoader(self, "sixiang_reward_item")
	res_async_loader:Load("uis/view/sixiang_call_prefab", "sixiang_reward_item", nil,
		function(new_obj)
			if IsNil(new_obj) then
				return
			end

			local item_root = self.node_list.reward_root.transform
			local layout_group = self.node_list.reward_root.grid_layout_group
			local cell_x = layout_group.cellSize.x
			local cell_y = layout_group.cellSize.y
			local spacing_x = layout_group.spacing.x
			local spacing_y = layout_group.spacing.y

			local item_list = {}
			for i=1,10 do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(item_root, false)
				item_list[i] = SiXiangRewardItem.New(obj)
				item_list[i]:SetIndex(i)
				item_list[i]:CalculateCenterPos(cell_x, cell_y, spacing_x, spacing_y)
			end

			self.reward_item_list = item_list
			self:RefreshView()
		end)
end

function SiXiangRewardView:OnClickSureBtn()
	if self.is_play_ten_tween or self.is_play_one_tween then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.SiXiangCall.WaitAnimStop)
		return
	end
	self:Close()
end

function SiXiangRewardView:OnClickDrawBtn()
	if self.is_play_ten_tween or self.is_play_one_tween then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.SiXiangCall.WaitAnimStop)
		return
	end

	local summon_info = SiXiangCallWGData.Instance:GetCacheSummonType()
	if summon_info then
		SiXiangCallWGCtrl.Instance:SiXiangSummon(summon_info.item_id, summon_info.need_num, summon_info.draw_type)
		self:StopTween()
	end

	self:Close()
end

function SiXiangRewardView:RefreshView()
	local reward_list = SiXiangCallWGData.Instance:GetSiXiangSummonReward()
	if #reward_list > 1 then
		self:ShowTenReward(reward_list)
		self:PlayTenRewardTween()
	elseif #reward_list == 1 then
		self:ShowOneReward(reward_list[1])
		self:PlayOneRewardTween()
	end

	self.is_shilian = #reward_list > 1
	self.node_list.once_content:SetActive(#reward_list == 1)
	self.node_list.reward_root:SetActive(#reward_list > 1)
	self.node_list.draw_btn_lbl.text.text = Language.SiXiangCall.RefineSure

	self:FlushCostItem()
end

-- 再抽一次消耗显示
function SiXiangRewardView:FlushCostItem()
	local summon_info = SiXiangCallWGData.Instance:GetCacheSummonType()
	if summon_info then
		-- 货币数量
		local has_num = ItemWGData.Instance:GetItemNumInBagById(summon_info.item_id)
        local need_num = summon_info.need_num or 1
        local str = string.format("%d/%d", has_num, need_num)
        local str_color = has_num >= need_num and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.node_list.const_lbl.text.text = ToColorStr(str, str_color)

		if has_num < need_num and summon_info.draw_type ~= OP_YSZH_DRAW_TYPE.MULTI 
			and summon_info.draw_type ~= OP_YSZH_DRAW_TYPE.SINGLE then
			self.node_list.draw_btn:SetActive(false)
			self.node_list.const_img:SetActive(false)
			return
		end

		-- 按钮文字
		if SiXiangCallWGData.Instance:IsTenSummon(summon_info.draw_type) then
			self.node_list.draw_btn_lbl.text.text = string.format(Language.SiXiangCall.RefineAgain, CommonDataManager.GetDaXie(10))
		else
			self.node_list.draw_btn_lbl.text.text = string.format(Language.SiXiangCall.RefineAgain, CommonDataManager.GetDaXie(1))
		end

		-- 货币icon
		local item_cfg = ItemWGData.Instance:GetItemConfig(summon_info.item_id)
		if item_cfg then
	        local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
			self.node_list.const_img.image:LoadSprite(bundle, asset)
			self.node_list.const_img:SetActive(true)
		else
			self.node_list.const_img:SetActive(false)
		end

		-- 折扣显示
		if need_num > 1 then
			local multi_lotto = SiXiangCallWGData.Instance:GetSiXiangSummonOtherCfg("multi_lotto")
	        if multi_lotto then
	            local discount = math.floor(need_num / multi_lotto * 10)
	            self.node_list.discount_bg:SetActive(discount < 10)
	            self.node_list.discount_lbl.text.text = string.format(Language.SiXiangCall.DiscountText, discount)
	        end
		else
			self.node_list.discount_bg:SetActive(false)
		end

		self.node_list.draw_btn:SetActive(true)
	else
		self.node_list.draw_btn:SetActive(false)
		self.node_list.const_img:SetActive(false)
	end
end

-- 单抽奖励展示
function SiXiangRewardView:ShowOneReward(reward_data)
	self.once_summon_item:SetData(reward_data)
end

function SiXiangRewardView:PlayOneRewardTween(is_play)
	local is_skip = SiXiangCallWGCtrl.Instance:IsSkipSummonAnim()
	if is_skip then
		self.once_summon_item:RestItem()
	elseif is_play then
		self.once_summon_item:ReadyPlayTween()
		self.once_summon_item:PlayFlyTween()
		-- self.is_play_one_tween = true
	end
end

---[[ 十连抽动画播放
function SiXiangRewardView:ShowTenReward(reward_list)
	if not IsEmptyTable(self.reward_item_list) then
		local item_list = self.reward_item_list
		for i=1,#item_list do
			item_list[i]:SetData(reward_list[i])
		end
	end
end

function SiXiangRewardView:PlayTenRewardTween(is_play)
	local is_skip = SiXiangCallWGCtrl.Instance:IsSkipSummonAnim()
	local item_list = self.reward_item_list
	if is_skip then
		for i=1,#item_list do
			item_list[i]:RestItem()
		end
	elseif is_play and self.need_ready_tween then
		for i=1,#item_list do
			item_list[i]:ReadyPlayTween()
		end
		self.reward_item_list[1]:PlayFlyTween(true)
		self.play_tween_index = 1
		self.need_ready_tween = false
		self.is_play_ten_tween = true
	end
end

function SiXiangRewardView:NextGetRewardTween()
	if not self.need_ready_tween then
		local index = self.play_tween_index + 1
		self.play_tween_index = index
		if self.reward_item_list[index] then
			self.reward_item_list[index]:PlayFlyTween(true)
		else
			self.is_play_ten_tween = false
		end
	end
end

function SiXiangRewardView:StopTween()
	if not IsEmptyTable(self.reward_item_list) then
		local item_list = self.reward_item_list
		for i=1,#item_list do
			item_list[i]:RestItem()
		end
	end
	self.need_ready_tween = true
	self.is_play_ten_tween = false
end
--]]

--------------------------------------------------------------------------------------
SiXiangRewardItem = SiXiangRewardItem or BaseClass(BaseRender)
function SiXiangRewardItem:__delete()
	self:StopItemTween()

	if self.reward_item ~= nil then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end
end

function SiXiangRewardItem:DoLoad(parent)
	self:LoadAsset("uis/view/sixiang_call_prefab", "sixiang_reward_item", parent.transform)
end

function SiXiangRewardItem:LoadCallBack()
	self.is_best_reward = false
	self.model_pos_x, self.model_pos_y = 0, 0

	if self.reward_item == nil then
		self.reward_item = SiXiangSummonItem.New()
		self.reward_item:DoLoad(self.node_list.reward_item)
	end
end

function SiXiangRewardItem:OnFlush()
	local data = self.data and self.data.item_data
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)

	self.reward_item:SetData(self.data)
	local _, _, suit_type = FightSoulWGData.GetBoneParamByItemId(data.item_id)
	local is_best_reward = suit_type > 0
	self.is_best_reward = is_best_reward
	self.node_list.best_img:SetActive(false)--is_best_reward)

	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if item_cfg then
		self:FlushItemEffect(item_cfg.color)
	end
end

function SiXiangRewardItem:FlushItemEffect(color)
	local eff_id = TIAN_SHEN_SHEN_SHI_MODEL_EFFECT[color]
	local bundle, asset = ResPath.GetEffectUi(eff_id)
	self.node_list.effect_root:ChangeAsset(bundle, asset)

	if self.is_best_reward then
		local bundle, asset = ResPath.GetEffectUi(Ui_Effect.UI_zhuanpanka_gj)
		self.node_list.best_effect_root:ChangeAsset(bundle, asset)
	end
end

-- 十连时播
function SiXiangRewardItem:PlayFlyTween(need_move)
	if self.m_tween then
		return
	end
	
	self:StopItemTween()
	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_move = need_move and self.node_list.tween_root.rect:DOAnchorPos(u3dpool.vec3(0, 0, 0), 0.2)
	local tween_alpha = self.node_list.tween_root.canvas_group:DoAlpha(0, 1, 0.2)

	if self.is_best_reward then
		local tween_scale_big = self.node_list.tween_root.transform:DOScale(2, 0.5)
		tween_scale_big:SetEase(DG.Tweening.Ease.OutCubic)
		local tween_scale_min = self.node_list.tween_root.transform:DOScale(1, 0.2)

		tween_sequence:Append(tween_scale_big)
		tween_sequence:Join(tween_alpha)
		tween_sequence:InsertCallback(0.2, function ()
			self.node_list.best_effect_root:SetActive(true)
		end)
		tween_sequence:AppendInterval(1)
		tween_sequence:Append(tween_scale_min)
		if need_move then
			tween_sequence:Append(tween_move)
		end
		tween_sequence:AppendCallback(function ()
			self.node_list.effect_root:SetActive(true)
		end)
	else
		local tween_scale_big = self.node_list.tween_root.transform:DOScale(0.7, 0.1)
		local tween_scale_min = self.node_list.tween_root.transform:DOScale(1, 0.1)

		tween_sequence:Append(tween_alpha)
		if need_move then
			tween_sequence:Join(tween_move)
		end
		tween_sequence:Append(tween_scale_big)
		tween_sequence:Append(tween_scale_min)
		tween_sequence:InsertCallback(0.2, function ()
			self.node_list.effect_root:SetActive(true)
		end)
	end

	tween_sequence:OnComplete(function ()
		self.m_tween:Kill()
		self.m_tween = nil
		SiXiangCallWGCtrl.Instance:PlayNextRewardTween()
	end)

	self.m_tween = tween_sequence
end

-- 算下在布局组件中间的偏移点(先只考虑10个两行的情况)
function SiXiangRewardItem:CalculateCenterPos(cell_x, cell_y, spacing_x, spacing_y)
	local index = self:GetIndex()
	local row = 5											-- 多少个一行
	local row_num = math.ceil(index / row)					-- 行数
	local center_index = row / 2 + row * (row_num - 1) 		-- 这一行的中间数
	local center_row_num = 1
	local center_x = (cell_x + spacing_x) * (center_index - index + 0.5)
	local center_y = -(cell_y + spacing_y) * (center_row_num - row_num + 0.5)
	self.center_pos_x = center_x
	self.center_pos_y = center_y
end

function SiXiangRewardItem:ReadyPlayTween()
	self:StopItemTween()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, self.center_pos_x, self.center_pos_y)
	self.node_list.tween_root.canvas_group.alpha = 0
	self.node_list.effect_root:SetActive(false)
	self.node_list.best_effect_root:SetActive(false)
end

function SiXiangRewardItem:RestItem()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, 0, 0)
	self.node_list.tween_root.canvas_group.alpha = 1
	self.node_list.tween_root.transform:SetLocalScale(1, 1, 1)
	self.node_list.effect_root:SetActive(false)
	self.node_list.best_effect_root:SetActive(false)
	self:StopItemTween()
end

function SiXiangRewardItem:StopItemTween()
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end
end