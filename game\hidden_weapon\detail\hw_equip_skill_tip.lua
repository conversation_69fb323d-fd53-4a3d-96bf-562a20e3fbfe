 ------------------------------------------------------------
--宠物相关主View
------------------------------------------------------------
HWEquipSkillTip = HWEquipSkillTip or BaseClass(SafeBaseView)

function HWEquipSkillTip:__init()
    self:SetMaskBg(true,true)
    self.view_layer = UiLayer.Pop
    self:AddViewResource(0, "uis/view/shenji_anqiruanjia_ui_prefab", "layout_equip_skill_tip")

end

function HWEquipSkillTip:ReleaseCallBack()

end

function HWEquipSkillTip:SetData(data)
    if not data then return end
    self.data = data
    self:Open()
end

function HWEquipSkillTip:LoadCallBack(index, loaded_times)
end


function HWEquipSkillTip:ShowIndexCallBack(index)
    self:Flush()
end

function HWEquipSkillTip:OnFlush(param_t, index)
    if not self.data then return end

    -- 技能配置
    local data = self.data
    local is_active = data.is_active
    local max_cfg = data.max_cfg
    local cur_cfg = data.cfg
    local next_cfg = HiddenWeaponWGData.Instance:GetSkillInfoByLevel(cur_cfg.skill_id, data.skill_type, cur_cfg.skill_level + 1)
    local is_max_level = max_cfg.skill_level <= cur_cfg.skill_level and is_active
    local attr_data = AttributeMgr.GetAttributteByClass(cur_cfg)
    local cap = AttributeMgr.GetCapability(attr_data, cur_cfg)
    self.node_list["cap_value"].text.text = cap
    self.node_list.layout_2:SetActive(false)
    self.node_list.layout_3:SetActive(false)

    -- 满级       
    if is_max_level then
        self.node_list.cur_title.text.text = "【" .. Language.Pet.SkillTitle_4 .. "】"
        self.node_list.cur_info.text.text = cur_cfg.skill_des or ""
        
    -- 已激活
    elseif is_active then
        self.node_list.layout_2:SetActive(true)
        self.node_list.layout_3:SetActive(true)
        self.node_list.cur_title.text.text = "【" .. Language.Pet.SkillTitle_4 .. "】"
        self.node_list.next_title.text.text = "【" .. Language.Pet.SkillTitle_3 .. "】"
        self.node_list.cur_info.text.text = cur_cfg.skill_des or ""
        -- 下级配置
        self.node_list.next_info.text.text = next_cfg.skill_des or ""
        
        -- 升级条件
        self.node_list.condition_title.text.text = "【" .. Language.Pet.SkillTitle_6 .. "】"
        local star_str = CommonDataManager.GetDaXie(next_cfg.need_star)
        self.node_list.condition_info.text.text = string.format(Language.HiddenWeapon.SkillCondition1,
         Language.Common.ColorName4[next_cfg.need_color], star_str)
    -- 未激活
    elseif not is_active then
        self.node_list.layout_3:SetActive(true)
        self.node_list.cur_title.text.text = "【" .. Language.Pet.SkillTitle_1 .. "】"
        self.node_list.cur_info.text.text = max_cfg.skill_des or ""
        self.node_list.condition_title.text.text = "【" .. Language.Pet.SkillTitle_5 .. "】"
        local star_str = CommonDataManager.GetDaXie(cur_cfg.need_star)
        self.node_list.condition_info.text.text = string.format(Language.HiddenWeapon.SkillCondition2,
        Language.Common.ColorName4[cur_cfg.need_color], star_str)
    end

    self.node_list.skill_level.text.text = is_active and cur_cfg.skill_level or Language.Pet.SkillLevel_1
    self.node_list.skill_name.text.text = ToColorStr(cur_cfg.skill_name, ITEM_COLOR[cur_cfg.color or 1])
    self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(cur_cfg.skill_icon))
    self.node_list.skill_icon.image:SetNativeSize()
    -- self.node_list.img_skill_icon_normal.image:LoadSprite(ResPath.GetCommonBackGround("bg_cell_circle3_" .. (cur_cfg.color or 1)))

    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.cur_info.rect)
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.next_info.rect)

end
