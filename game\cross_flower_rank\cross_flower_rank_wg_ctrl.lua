require("game/cross_flower_rank/cross_flower_rank_wg_data")
require("game/cross_flower_rank/cross_flower_rank_view")

CrossFlowerRankWGCtrl = CrossFlowerRankWGCtrl or BaseClass(BaseWGCtrl)

function CrossFlowerRankWGCtrl:__init()
    if CrossFlowerRankWGCtrl.Instance then
		error("[CrossFlowerRankWGCtrl]:Attempt to create singleton twice!")
	end
    CrossFlowerRankWGCtrl.Instance = self

    self.data = CrossFlowerRankWGData.New()
    self.view = CrossFlowerRankView.New(GuideModuleName.CrossFlowerRankView)

    self:RegisterProtocol(SCCrossFlowerRank,'OnSCCrossFlowerRank')
end

function CrossFlowerRankWGCtrl:__delete()
    self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil

    CrossFlowerRankWGCtrl.Instance = nil
end

function CrossFlowerRankWGCtrl:Send<PERSON>rossFlowerReq(opera_type,param_1,param_2,param_3)
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.CROSS_CHANNEL_ACTIVITY_TYPE_CHARM_RANK
	param_t.opera_type = opera_type or 0
	param_t.param_1 = param_1 or 0
	param_t.param_2 = param_2 or 0
	param_t.param_3 = param_3 or 0
	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end

function CrossFlowerRankWGCtrl:OnSCCrossFlowerRank(protocol)
	self.data:SetCrossFlowerRankInfo(protocol)
	if self.view:IsOpen() then
		self.view:Flush(0, "rank_list")
	end
end
