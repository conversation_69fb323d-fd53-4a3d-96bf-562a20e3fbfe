------------------------------------------------------------------------
--	F2开服活动
------------------------------------------------------------------------
ServerActivityTabView = ServerActivityTabView or BaseClass(SafeBaseView)

function ServerActivityTabView:__init()
	self:SetMaskBg()
	self.view_style = ViewStyle.Half

	local bundle_name = "uis/view/open_server_activity_ui_prefab"
	local common_path = "uis/view/common_panel_prefab"

	-- self:AddViewResource(0, common_path, "layout_a3_common_activity_panel")
	self:AddViewResource(TabIndex.act_xianmeng_fengbang, bundle_name, "layout_guild_rank")
	self:AddViewResource(TabIndex.act_kaizonglipai, bundle_name, "layout_open_guild")
	self:AddViewResource(TabIndex.act_boss_lieren, bundle_name, "layout_boss_hunter")
	self:AddViewResource(TabIndex.act_kf_jizi, bundle_name, "layout_open_server_reward")

	-- self:AddViewResource(TabIndex.act_lovers, bundle_name, "layout_open_server_lovers")
	--self:AddViewResource(TabIndex.profess_love, bundle_name, "lover_propose_container") 表白已屏蔽
	-- self:AddViewResource(TabIndex.city_love, bundle_name, "city_love_container")
	self:AddViewResource(TabIndex.act_kf_bysf, bundle_name, "layout_fly_wing_to_wing")
	self:AddViewResource(TabIndex.guild_assign, bundle_name, "layout_fly_wing_to_wing")
	self:AddViewResource(TabIndex.guild_my_assign, bundle_name, "layout_fly_wing_to_wing")
	self:AddViewResource(TabIndex.act_kf_gift, bundle_name, "layout_kf_gift_view")

	self:AddViewResource(TabIndex.act_highpoint, bundle_name, "layout_high_point")
	self:AddViewResource(TabIndex.act_sevenday_recharge, bundle_name, "layout_seven_day_recharge")
	self:AddViewResource(TabIndex.server_collect_card, bundle_name, "layout_open_server_collect_card")
	-- self:AddViewResource(0, bundle_name, "layout_tab_activity_adorn")

	self:AddViewResource(0, common_path, "VerticalTabbar_Activity")
	self:AddViewResource(0, bundle_name, "HorizontalTabbar")
	self:AddViewResource(0, common_path, "layout_a3_light_common_top_panel")
end

function ServerActivityTabView:ReleaseCallBack()
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	self:XMFBReleaseCallBack()
	self:KZLPReleaseCallBack()
	self:BossHunterReleaseCallBack()
	self:KFJZReleaseCallBack()
	self:LoversReleaseCallBack()
	self:ProfessLoveReleaseCallBack()
	self:ReleaseCityLoveCallBack()
	self:HiPoReleaseCallBack()
	self:KFGiftReleaseCallBack()
	self:KFBiYiShuangFeiReleaseCallBack()
	self:SDRReleaseCallBack()
	self:CollectCardReleaseCallBack()
end

function ServerActivityTabView:CloseCallBack()
	self.is_play_left_btn_anim = nil
end

function ServerActivityTabView:LoadCallBack()
	self.is_play_left_btn_anim = nil
	self:InitMoneyBar()
	self:InitTabbar()
end

function ServerActivityTabView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function ServerActivityTabView:PlayMoneyBarEffct(money_type, rect, view_name)
	self.money_bar:PlayEffectByRecharge(money_type, rect, view_name)
end

function ServerActivityTabView:InitTabbar()
	local remind_tab = {}
	local toggle_name_list = {}
	for _, v in ipairs(ServerActivityTabCfg) do
		if v.key == "open_server" then
			if v.table_index % 10 == 0 then --代表没有横标签
				toggle_name_list[#toggle_name_list + 1] = v.name
				remind_tab[v.table_index / 10] = v.remind_name ~= "" and { [1] = v.remind_name } or {}
			else --代表有横标签
				local index = math.floor(v.table_index / 10)
				if remind_tab[index] then
					remind_tab[index][#remind_tab[index] + 1] = v.remind_name ~= "" and v.remind_name or ""
				else
					toggle_name_list[#toggle_name_list + 1] = v.name
					remind_tab[index] = v.remind_name ~= "" and { [1] = v.remind_name } or {}
				end
			end
		end
	end

	local bundle_name = "uis/view/open_server_activity_ui_prefab"
	local common_path = "uis/view/common_panel_prefab"
	self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
	self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	self.tabbar:SetCreateVerCallBack(BindTool.Bind1(self.SetTabVisible, self))
	self.tab_sub = { nil, nil, nil, nil, nil, Language.OpenServer.QingyuanActivityName, nil }
	self.tabbar:Init(toggle_name_list, self.tab_sub, common_path,
		bundle_name, remind_tab)
	-- self.tabbar:JumpToVerPrecent(1)
end

function ServerActivityTabView:SetTabVisible()
	if nil == self.tabbar then
		return
	end
	for _, v in pairs(ServerActivityTabCfg) do
		if v.key == "open_server" then
			self.tabbar:SetToggleVisible(v.table_index, v.can_open_func())
		end
	end
end

function ServerActivityTabView:LoadIndexCallBack(index)

	if index == TabIndex.act_xianmeng_fengbang then
		self:XMFBLoadCallBack()
	elseif index == TabIndex.act_kaizonglipai then
		self:KZLPLoadCallBack()
	elseif index == TabIndex.act_boss_lieren then
		self:BossHunterLoadCallBack()

	elseif index == TabIndex.act_kf_jizi then
		self:KFJZLoadCallBack()
	-- elseif index == TabIndex.act_kf_bysf then
	-- 	self:KFBiYiShuangLoadCallBack()
	elseif index == TabIndex.act_lovers then
		self:KFBiYiShuangLoadCallBack()
	elseif index == TabIndex.city_love then
		self:KFBiYiShuangLoadCallBack2()
	elseif index == TabIndex.act_highpoint then
		self:HiPoLoadCallBack()
	elseif index == TabIndex.act_kf_gift then
		self:KFGiftLoadCallBack()
	elseif index == TabIndex.act_sevenday_recharge then
		self:SDRLoadCallBack()
	elseif index == TabIndex.server_collect_card then
		self:CollectCardLoadCallBack()
	end
end

function ServerActivityTabView:ShowIndexCallBack(index)
	UITween.CleanAllTween(GuideModuleName.ServerActivityTabView)
	if not self.is_play_left_btn_anim then
		self.is_play_left_btn_anim = true
		self:PlaySATVOpenAnim()
	end

	if index ~= 0 then
		ServerActivityWGCtrl.Instance:SendOpenGameActivityInfoReq()
	end

	-- local bg_res = "a3_hdty_bg_1"
    -- local bundle, asset

	if index == TabIndex.act_xianmeng_fengbang then
		self:XMFBShowCallBack()
	elseif index == TabIndex.act_boss_lieren then
		self:BossHunterShowCallBack()
	elseif index == TabIndex.act_kaizonglipai then --开宗立派
		self:KZLPShowIndexCallBack()
	elseif index == TabIndex.act_kf_jizi then --开服集字
		self:KFJZShowIndexCallBack()
	elseif index == TabIndex.guild_assign then --比翼双飞 结婚
		self:KFBiYiShuangShowIndexCallBack()
	elseif index == TabIndex.city_love then --比翼双飞 任务
		self:KFBiYiShuangShowIndexCallBack2()
		self:HighTaskPointShowIndexCallBack()

	elseif index == TabIndex.act_highpoint then      --开服嗨点
		self:HighPointShowIndexCallBack()
	elseif index == TabIndex.act_sevenday_recharge then --七日累充
		self:SDRShowIndexCallBack()
	elseif index == TabIndex.act_kf_gift then
		self:GiftShowIndexCallBack()
	elseif index == TabIndex.server_collect_card then
		self:CollectCardShowIndexCallBack()
	end

	local act_type = 0
	if index == TabIndex.act_lovers then				--完美情人
		act_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_LOVING_CITY
	end

	if act_type > 0 then
		ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.ServerActivityTabView, act_type)
	end

	-- elseif index == TabIndex.act_kf_bysf then  
	-- self:KFBiYiShuangShowIndexCallBack()
	-- elseif index == TabIndex.act_kf_gift then
	-- 	self:KFGiftModelPlayLastAction()
	-- elseif index == TabIndex.act_xianmeng_zhengba then		--仙盟争霸
	-- 	self:XMZBShowIndexCallBack()
	
	-- 	self:WMQRShowIndexCallBack()
	-- elseif index == TabIndex.profess_love then				--爱的表白
	--   	self:ShowPerofessIndexCallBack()

	-- bundle, asset = ResPath.GetRawImagesPNG(bg_res)

	-- self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- end)

	self:SetTabVisible()
end

--播放首次打开界面动画
function ServerActivityTabView:PlaySATVOpenAnim()

	local tween_info = UITween_CONSTS.ServerActivityTab
	UITween.FakeHideShow(self.node_list["VerticalTabbar_Mask"])

	UITween.AlphaShow(GuideModuleName.ServerActivityTabView, self.node_list["VerticalTabbar_Mask"], 0, tween_info
	.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

function ServerActivityTabView:OnFlush(param_t, index)
	if index == TabIndex.act_xianmeng_fengbang then
		self:XMFBOnFlush(param_t)
	elseif index == TabIndex.act_kaizonglipai then
		self:KZLPOnFlush(param_t)
	elseif index == TabIndex.act_boss_lieren then
		self:BossHunterOnFlush(param_t)
		-- elseif index == TabIndex.act_xianmeng_zhengba then
		-- 	self:XMZBOnFlush(param_t)
	elseif index == TabIndex.act_kf_jizi then
		self:KFJZOnFlush(param_t)
	-- elseif index == TabIndex.act_kf_bysf then
	-- 	self:KFBiYiShuangFeiOnFlush(param_t)
	elseif index == TabIndex.act_lovers then
		self:KFBiYiShuangFeiOnFlush(param_t)
	elseif index == TabIndex.city_love then
		self:KFBiYiShuangFeiOnFlush2(param_t)
		-- elseif index == TabIndex.act_lovers then
		--     self:LoversOnFlush(param_t)
		-- elseif index == TabIndex.profess_love then
		--     self:FlushProfessLove(param_t)
		-- elseif index == TabIndex.city_love then
		-- 	self:FlushCityLoveInfo()
	elseif index == TabIndex.act_highpoint then
		self:HiPoOnFlush(param_t)
	elseif index == TabIndex.act_kf_gift then
		self:KFGiftOnFlush(param_t)
	elseif index == TabIndex.act_sevenday_recharge then
		self:SDROnFlush(param_t)
	elseif index == TabIndex.server_collect_card then
		self:CollectCardOnFlush(param_t)
	end
end

function ServerActivityTabView:OpenHasRemindTab()
	local index = 0
	for _, v in ipairs(ServerActivityTabCfg) do
		if v.key == "open_server" then
			if v:show_redpoint_func() == 1 then
				index = v.table_index
				break
			elseif v:can_open_func() and (v.table_index < index or index == 0) then
				index = v.table_index
			end
		end
	end

	if self:IsLoaded() then
		self:SetTabVisible()
	end

	if index > 0 then
		self:Open(index)
	end
end

function ServerActivityTabView:OpenByKey(index, param_t)
	if index then
		for _, v in pairs(ServerActivityTabCfg) do
			if v.key == "open_server" and v.table_index == index and v:can_open_func() then
				self:Open(index)
				self:Flush(index, nil, param_t)
				return
			end
		end
	end
	self:OpenHasRemindTab()
end
