﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class GInstancing : MonoBehaviour {

    public Material material;
    public Texture2D lightmapTexture;

    MeshFilter meshfilter;

    List<Matrix4x4> materix = new List<Matrix4x4>();
    List<Vector4> lightmapOffset = new List<Vector4>();
    MaterialPropertyBlock block;




    //Use this for initialization
    public void Init(List<MeshRenderer> listMeshRenderer,Dictionary<MeshRenderer,bool> dicMeshListStatus)
    {


        materix.Clear();
        lightmapOffset.Clear();
        meshfilter = null;

        //获取节点下的所有MeshRenderer
        //foreach (var item in GetComponentsInChildren<MeshRenderer>())
        foreach (var item in listMeshRenderer)
        {
            if (dicMeshListStatus[item] == false)
                continue;
            //item.gameObject.SetActive(false);
            if (meshfilter == null)
            {

                //取出第一个mesh，因为我们是做例子可以肯定保证下面的mesh都是一样的
                meshfilter = item.GetComponent<MeshFilter>();
            }
            //保存每个物体的矩阵以及lightmapScaleOffset
            materix.Add(item.localToWorldMatrix);
            lightmapOffset.Add(item.lightmapScaleOffset);
        }

        if (meshfilter == null)
            return;

        //启动LIGHTMAP_ON宏
        material.EnableKeyword("LIGHTMAP_ON");
        //为了避免GC所以只new一次MaterialPropertyBlock
        block = new MaterialPropertyBlock();
        //设置具体用哪张烘焙贴图
        block.SetTexture("unity_Lightmap", lightmapTexture);
        //将每个物体的lightmapScaleOffset传入shader中
        block.SetVectorArray("_LightmapST", lightmapOffset.ToArray());
        material.EnableKeyword("_LightmapST_ON");

    }




    private void OnEnable()
    {
        //启动LIGHTMAP_ON宏
        
    }

    private void OnDisable()
    {
        //启动LIGHTMAP_ON宏
        material.DisableKeyword("_LightmapST_ON");
    }

    // Update is called once per frame
    void Update () {

        if (meshfilter == null)
        {
           
            //Init();
            return;
        }
        //一次绘制
        Graphics.DrawMeshInstanced(meshfilter.sharedMesh, 0, material, materix, block);
    }
}
