local DIANCHUN_CUTDOWN = "marry_dianchun_cd_timer" 				--点唇
local DIANCHUN_CUTDOWN_CHANGELIFE = "marry_dianchun_changelife_timer" 	--逆天改命

DIANCHUN_START = 1 				-- 开始点唇
DIANCHUN_GAIMING = 2 			-- 逆天改命
DIANCHUN_LINGQU = 3 			-- 领取奖励

function MarryView:InitDianChunView()
	self.show_dian_chun_img_flag = true
	if not self.init_dian_chun_view then
		self.init_dian_chun_view = true
		self.cur_btn_status = DIANCHUN_START
		self.nitiangaiming_timestamp = 0 					-- 模拟逆天改命时间戳
		self.self_chunnum = 0 								-- 自己的唇数
		self.fere_chunnum = 0								-- 伴侣的唇数

		self.self_chun_list = {} 							-- 自己所有色子是否旋转完毕
		self.lover_chun_list = {} 							-- 伴侣点唇图片是否播放完毕

		self.chun_num_list = {}				--台子上的色子
		for i = 1, COMMON_CONSTS.COUPLE_DIANCUN_DICE_MAX do
			local ph_dice = self.node_list["ph_dice_" .. i]
			local chun_num = ChunNumRender.New(ph_dice)
			chun_num:SetIndex(i)
			chun_num:AddClickEventListener(BindTool.Bind(self.OnClickSelfChun, self, i))
			self.chun_num_list[i] = chun_num
			self.self_chun_list[i] = 0
		end

		self.fere_chun_num_list = {} 		--伴侣唇数
		for i = 1, COMMON_CONSTS.COUPLE_DIANCUN_DICE_MAX do
			local chun_num = FereChunNumRender.New(self.node_list["item_cell_"..i])
			chun_num:SetIndex(i)
			self.fere_chun_num_list[i] = chun_num
			self.lover_chun_list[i] = 0
		end

		
		XUI.AddClickEventListener(self.node_list["btn_chun_invite"], BindTool.Bind(self.OnClickBtnChunInvite, self))	--邀请伴侣点唇
		XUI.AddClickEventListener(self.node_list["btn_chun_start"], BindTool.Bind(self.OnClickBtnChunStart, self))		--开始点唇
		XUI.AddClickEventListener(self.node_list["btn_chun_tips"], BindTool.Bind(self.OnClickBtnChunTips, self))		--帮助
		XUI.AddClickEventListener(self.node_list["btn_img_flag"], BindTool.Bind(self.OnClickJiangLiShow, self))		--显示/隐藏个人奖励

		self:FlushDianChunFereStatus()	--刷新伴侣点唇状态
		self:InitRewardShow()			--初始化伴侣

		self:ChangeSelfDianChunNum(nil, 0)
		self:ChangeLoverDianChunNum(nil,0)
		self.global_event_self = GlobalEventSystem:Bind(MarryDianChunType.SELF,BindTool.Bind(self.ChangeSelfDianChunNum,self))
		self.global_event_lover = GlobalEventSystem:Bind(MarryDianChunType.COMPANION,BindTool.Bind(self.ChangeLoverDianChunNum,self))
	end
	self.node_list["img_double"]:SetActive(false)
end

function MarryView:DeleteDianChunView()
	self.init_dian_chun_view = false
	if nil ~= self.chun_num_list then
		for k,v in pairs(self.chun_num_list) do
			v:DeleteMe()
		end
		self.chun_num_list = nil
	end
	if nil ~= self.fere_chun_num_list then
		for k,v in pairs(self.fere_chun_num_list) do
			v:DeleteMe()
		end
		self.fere_chun_num_list = nil
	end

	if nil ~= self.dianchun_alert then
		self.dianchun_alert:DeleteMe()
		self.dianchun_alert = nil
	end

	if nil ~= self.chun_reward_list then
		self.chun_reward_list:DeleteMe()
		self.chun_reward_list = nil
	end

	self.spi_effect = nil

	if CountDownManager.Instance:HasCountDown(DIANCHUN_CUTDOWN_CHANGELIFE) then
		CountDownManager.Instance:RemoveCountDown(DIANCHUN_CUTDOWN_CHANGELIFE)
	end
	if CountDownManager.Instance:HasCountDown(DIANCHUN_CUTDOWN) then
		CountDownManager.Instance:RemoveCountDown(DIANCHUN_CUTDOWN)
	end

	if self.global_event_lover then
		GlobalEventSystem:UnBind(self.global_event_lover)
		self.global_event_lover = nil
	end

	if self.global_event_self then
		GlobalEventSystem:UnBind(self.global_event_self)
		self.global_event_self = nil
	end

	self.self_chunnum = 0
	self.fere_chunnum = 0
	self.cur_btn_status = DIANCHUN_START
	self.nitiangaiming_timestamp = 0 					-- 模拟逆天改命时间戳
	self.show_dian_chun_img_flag = true
end

--初始化个人奖励列表
function MarryView:InitRewardShow()
	self.chun_reward_list = AsyncListView.New(ChunRewardRender,self.node_list["ph_chun_reward_list"])
	self.chun_reward_list:SetDataList(MarryWGData.Instance:GetChunRewardList(),0)
end

function MarryView:OpenDianChunView()
	self:FlushSelfDiceData(false)
	self:FlushFereDiceData(false)
end

function MarryView:FlushDianChunView()
	local couple_dianchun_info = MarryWGData.Instance:GetCoupleDianChunInfo()

	if nil == couple_dianchun_info or nil == IsEmptyTable(couple_dianchun_info) then
		self.node_list["text_chun_start_desc"]:SetActive(false)
		self.node_list["text_chun_free_num"]:SetActive(false)
		self.node_list["text_num"]:SetActive(false)
		self:SetDianChunRemindState(false)
		return
	end

	self.node_list["text_chun_start_desc"]:SetActive(true)
	self.node_list["text_chun_free_num"]:SetActive(true)
	self.node_list["text_num"]:SetActive(true)

	if CountDownManager.Instance:HasCountDown(DIANCHUN_CUTDOWN_CHANGELIFE) then
		CountDownManager.Instance:RemoveCountDown(DIANCHUN_CUTDOWN_CHANGELIFE)
	end
	if CountDownManager.Instance:HasCountDown(DIANCHUN_CUTDOWN) then
		CountDownManager.Instance:RemoveCountDown(DIANCHUN_CUTDOWN)
	end

	if self.spi_effect then 
		self.spi_effect:setVisible(false)
	end

	local change_life_time_stamp = couple_dianchun_info.couple_dianchun_changelife_time_stamp
	if change_life_time_stamp - TimeWGCtrl.Instance:GetServerTime() > 0 then
		self:SetDianChunBtnStr(nil, DIANCHUN_GAIMING)
		self:UpdataDianChunTime(TimeWGCtrl.Instance:GetServerTime(), change_life_time_stamp)
		CountDownManager.Instance:AddCountDown(DIANCHUN_CUTDOWN_CHANGELIFE, 
							BindTool.Bind1(self.UpdataDianChunTime, self), 
							BindTool.Bind1(self.CompleteDianChunTime, self), 
							change_life_time_stamp, 
							nil, 1)

	elseif 0 == MarryWGData.Instance:GetCoupleDianChunGetRewardFlage() then
		self:SetDianChunBtnStr(Language.Common.LingQuJiangLi, DIANCHUN_LINGQU)
		if self:IsPlayLoverAni() or self:IsPlaySelfAni() then --正在播放动画
			self:SetDianChunBtnEnable(false)
		end
		if self.spi_effect then 
			local path, name = ResPath.GetEffectAnimPath(3042)
			self.spi_effect:setAnimate(path, name, COMMON_CONSTS.MAX_LOOPS, 0.15, false)
			self.spi_effect:setVisible(true)
		end
	else
		local dianchun_cd_time = couple_dianchun_info.couple_dianchun_cd_time_stamp
		if dianchun_cd_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
			self:SetDianChunBtnStr(nil, DIANCHUN_START)
			self:UpdataDianChunCDTime(TimeWGCtrl.Instance:GetServerTime(), dianchun_cd_time)
			CountDownManager.Instance:AddCountDown(DIANCHUN_CUTDOWN, 
								BindTool.Bind1(self.UpdataDianChunCDTime, self), 
								BindTool.Bind1(self.CompleteDianChunCDTime, self), 
								dianchun_cd_time, 
								nil, 1)
		else
			self:SetDianChunBtnStr(Language.Marry.DianChunStart, DIANCHUN_START)
		end		
	end
	-- self.node_list["text_chun_start_desc"].text.text = string.format(Language.Marry.VipCanBuyNum, MarryWGData.Instance:GetVipBuyRemainNum())
	self.node_list["text_chun_start_desc"].text.text = ""        --舍弃该功能
	local num = MarryWGData.Instance:GetDianChunRemainNum()
	self.node_list["text_num"].text.text = num

	local boo = num > 0
	if boo == false then
		boo = self.cur_btn_status ~= DIANCHUN_START
	end

	self:SetDianChunRemindState(boo)

	local dianchun_reward_info = MarryWGData.Instance:GetCoupleDianChunRewardInfo()
	if nil ~= dianchun_reward_info then
		self.node_list["img_double"]:SetActive(2 == dianchun_reward_info.double_reward)
	end
end

function MarryView:UpdataDianChunTime(elapse_time, total_time)
	local time = string.format("(%d)", total_time - elapse_time)
	self:SetDianChunBtnStr(Language.Marry.NiTianGaiMing .. time, DIANCHUN_GAIMING)
end

function MarryView:SetDianChunRemindState( enable )
	self.node_list["dianchun_remind"]:SetActive(enable)
end

function MarryView:CompleteDianChunTime()
	self:FlushDianChunView()
end

function MarryView:UpdataDianChunCDTime(elapse_time, total_time)
	local time = string.format("(%d)", total_time - elapse_time)
	self:SetDianChunBtnStr(Language.Marry.DianChunStart .. time, DIANCHUN_START)
end

function MarryView:SetDianChunBtnStr( str, type )
	if nil ~= str then
		self.node_list["lbl_btn_str"].text.text = str
	end
	self.cur_btn_status = type
	MarryWGData.Instance:DianChunBtnState(type)
end

function MarryView:CompleteDianChunCDTime()
	self:FlushDianChunView()
end

--刷新自己的点唇数据：色子
function MarryView:FlushSelfDiceData(is_action)
	self.self_chunnum = 0
	is_action = nil == is_action and true or is_action
	local couple_dianchun_info = MarryWGData.Instance:GetCoupleDianChunInfo()
	if nil == couple_dianchun_info then
		return
	end
	self:IsPlaySelfAni(is_action)
	for i,v in ipairs(couple_dianchun_info.self_dice_data) do
	 	self.chun_num_list[i]:SetIsAction(is_action)
	 	self.chun_num_list[i]:SetData(v)
	 	if 6 == v then
	 		self.self_chunnum = self.self_chunnum + 1
	 	end
	end
end

--刷新伴侣的点唇数据
function MarryView:FlushFereDiceData(is_action)
	self.fere_chunnum = 0
	is_action = nil == is_action and true or is_action
	local couple_dianchun_info = MarryWGData.Instance:GetCoupleDianChunInfo()
	if nil == couple_dianchun_info then
		return
	end
	self:IsPlayLoverAni(is_action)
	if is_action and self.cur_btn_status == DIANCHUN_LINGQU then --领取奖励状态
		self:SetDianChunBtnEnable(false)
	end
	for i, v in ipairs(couple_dianchun_info.qingyuan_dice_data) do
		self.fere_chun_num_list[i]:SetIsAction(is_action)
	 	self.fere_chun_num_list[i]:SetData(v)
	 	if 6 == v then
	 		self.fere_chunnum = self.fere_chunnum + 1
	 	end
	end
end


function MarryView:ChangeSelfDianChunNum(index, num)
	num = num or self.self_chunnum

	if index ~= nil then
		self.self_chun_list[index] = 1

		for i = 1, COMMON_CONSTS.COUPLE_DIANCUN_DICE_MAX do
			if self.self_chun_list[i] == 0 then
				return
			end
		end
	end

	self.node_list["text_self_chunshu"].text.text = string.format(Language.Marry.SelfChunShu, num)

	for i = 1, COMMON_CONSTS.COUPLE_DIANCUN_DICE_MAX do
		self.self_chun_list[i] = 0
	end

	self:IsPlaySelfAni(false)
	if not self.dianchun_btn_is_enable and false == self:IsPlayLoverAni() then
		self:SetDianChunBtnEnable(true)
	end
end

function MarryView:SetDianChunBtnEnable(enable)
	self.dianchun_btn_is_enable = enable
	XUI.SetButtonEnabled(self.node_list["btn_chun_start"], enable)
end


function MarryView:ChangeLoverDianChunNum(index, num)
	num = num or self.fere_chunnum

	if index ~= nil then
		self.lover_chun_list[index] = 1

		for i = 1, COMMON_CONSTS.COUPLE_DIANCUN_DICE_MAX do
			if self.lover_chun_list[i] == 0 then
				return
			end
		end
	end
	self.node_list["text_fere_chunshu"].text.text = (string.format(Language.Marry.FereChunShu, num))

	for i = 1, COMMON_CONSTS.COUPLE_DIANCUN_DICE_MAX do
		self.lover_chun_list[i] = 0
	end
	self:IsPlayLoverAni(false)
	if not self.dianchun_btn_is_enable and false == self:IsPlaySelfAni() then
		self:SetDianChunBtnEnable(true)
	end
end

function MarryView:IsPlayLoverAni( state )
	if nil == state then
		return self.is_play_lover_ani
	end
	self.is_play_lover_ani = state
end

function MarryView:IsPlaySelfAni( state )
	if nil == state then
		return self.is_play_self_ani
	end
	self.is_play_self_ani = state
end

-- 
function MarryView:FlushDianChunFereStatus()
	local status_str = ""
	if 1 == MarryWGData.Instance:GetFereDianChunStatus() then
		local couple_dianchun_info = MarryWGData.Instance:GetCoupleDianChunInfo()
		if nil ~= couple_dianchun_info and 1 == couple_dianchun_info.qingyuan_dianchun_flage then
			status_str = Language.Marry.FereClick
		else
			status_str = Language.Marry.HaveFere
		end
	else
		status_str = Language.Marry.NoFere
	end

	self.node_list["text_fere_status"].text.text = status_str
end

function MarryView:OnClickBtnChunInvite()
	if 1 == MarryWGData.Instance:GetFereDianChunStatus() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.HaveFere)
		return
	end

	local function callback()
		MarryWGCtrl.Instance:SendInviteLoverReq(0)
	end
	OperateFrequency.Operate(callback, "dianchun_invite", 1)
end

function MarryView:OnClickBtnChunStart()
	if DIANCHUN_START == self.cur_btn_status then
		local couple_dianchun_info = MarryWGData.Instance:GetCoupleDianChunInfo()
		if nil == couple_dianchun_info then
			return
		end
		local dianchun_cd_time = couple_dianchun_info.couple_dianchun_cd_time_stamp
		if dianchun_cd_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
			if nil == self.dianchun_alert then
				self.dianchun_alert = Alert.New()
			end
			self.dianchun_alert:SetLableString(string.format(Language.Marry.RemoveCDTips, MarryWGData.Instance:GetRemoveCDGold()))
			self.dianchun_alert:SetOkFunc(BindTool.Bind(self.OnClickRemoveCDTime, self))
			self.dianchun_alert:Open()

		elseif MarryWGData.Instance:GetDianChunRemainNum() < 1 and MarryWGData.Instance:GetVipBuyRemainNum() > 0 then
			if nil == self.dianchun_alert then
				self.dianchun_alert = Alert.New()
			end
			self.dianchun_alert:SetLableString(string.format(Language.Marry.BuyNumTips, MarryWGData.Instance:GetBuyNumGold()))
			self.dianchun_alert:SetOkFunc(BindTool.Bind(self.OnClickBuyNum, self))
			self.dianchun_alert:Open()
		elseif MarryWGData.Instance:GetDianChunRemainNum() < 1 then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Marry.CanNotFere)
		else
			if self.nitiangaiming_timestamp < TimeWGCtrl.Instance:GetServerTime() then
				self.nitiangaiming_timestamp = TimeWGCtrl.Instance:GetServerTime() + 15
			end
			MarryWGCtrl.Instance:SendDianChunReq(0, 1)
		end
		MarryWGCtrl.Instance:OnClickDianChun()
	elseif DIANCHUN_GAIMING == self.cur_btn_status then
		if nil == self.dianchun_alert then
			self.dianchun_alert = Alert.New()
		end
		local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
		self.dianchun_alert:SetLableString(string.format(Language.Marry.NiTianGaiMingTips, other_cfg.change_life_use_gold))
		self.dianchun_alert:SetOkFunc(BindTool.Bind(self.OnClickNiTianGaiMing, self))
		self.dianchun_alert:Open()

	elseif DIANCHUN_LINGQU == self.cur_btn_status then
		MarryWGCtrl.Instance:SendCoupleDianChunGetRewardReq()
	end
end

function MarryView:OnClickRemoveCDTime()
	local couple_dianchun_info = MarryWGData.Instance:GetCoupleDianChunInfo()
	if nil ~= couple_dianchun_info then
		local dianchun_cd_time = couple_dianchun_info.couple_dianchun_cd_time_stamp
		if dianchun_cd_time - TimeWGCtrl.Instance:GetServerTime() > 0 then
			MarryWGCtrl.Instance:SendDianChunReq(2)
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.RemoveCDTimeTips)
		end
	end
end

function MarryView:OnClickBuyNum()
	MarryWGCtrl.Instance:SendDianChunReq(0, 0)
end

function MarryView:OnClickNiTianGaiMing()
	if DIANCHUN_GAIMING == self.cur_btn_status then
		MarryWGCtrl.Instance:OnClickDianChun()
		MarryWGCtrl.Instance:SendDianChunReq(1)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.NiTianGaiMingTimeTips)
	end
end

function MarryView:OnClickBtnChunTips()
	MarryView.OpenTips(Language.Marry.CoupleDianChunDesc, Language.Marry.CoupleDianChunTitle)
end

function MarryView:OnClickSelfChun(index)
	if MarryWGData.Instance:GetCoupleDianChunInfo() ~= nil then
		if nil == self.dianchun_alert then
			self.dianchun_alert = Alert.New()
		end
		self.dianchun_alert:SetLableString(string.format(Language.Marry.SingleDianChunPrice, MarryWGData.Instance:GetDianChunSingleGold()))
		self.dianchun_alert:SetOkFunc(BindTool.Bind(self.OnClickSingleDianChun, self, index))
		self.dianchun_alert:Open()
	end
end

function MarryView:OnClickSingleDianChun(index)
	MarryWGCtrl.Instance:SendDianChunReq(1, index)
end

--按钮点击
function MarryView:OnClickJiangLiShow()
	self.show_dian_chun_img_flag = not self.show_dian_chun_img_flag
	self.node_list["layout_chun_reward"]:SetActive(self.show_dian_chun_img_flag)
	if self.show_dian_chun_img_flag then
		self.node_list["img_flag"].rect.localScale = Vector3(1,1,1)
	else
		self.node_list["img_flag"].rect.localScale = Vector3(1,-1,1)
	end
end

MarryView.DianChunUpdateDeltaTime = 0.03 
-----------------------------------------------------------
ChunNumRender = ChunNumRender or BaseClass(BaseRender)
ChunNumRender.Time = 3 	--旋转时间
function ChunNumRender:__init()
end

function ChunNumRender:__delete()
	self.old_data = -1
	if nil ~= self.dianchun_roll_timer then
		GlobalTimerQuest:CancelQuest(self.dianchun_roll_timer)
		self.dianchun_roll_timer = nil
	end
end

function ChunNumRender:LoadCallBack()
	self.dianchun_roll_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateRoll, self), MarryView.DianChunUpdateDeltaTime)
	self.old_data = -1
	self.timer = 0
	self.view.image:LoadSprite(ResPath.GetMarryResPath2("dice_" .. math.random(1, 6)))
end

--点唇 dice_1
function ChunNumRender:OnFlush()
	self.view:SetActive(self.data > 0)

	if self.data > 0 then
		if self.is_action == false then
			self.view.image:LoadSprite(ResPath.GetMarryResPath2("dice_" .. self.data))
		else
			if not self.dianchun_roll_timer then
				self.dianchun_roll_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateRoll, self), MarryView.DianChunUpdateDeltaTime)
			end
			self.is_start = true
			self.timer = 0
		end
	end
end

--改变状态：旋转还是停止旋转
function ChunNumRender:ChangeState()
	self.view:SetActive(self.data > 0)
	if self.is_start then
		self.view.rect.localRotation = Quaternion.Euler(0, 0, 270)
		self.view.rect.localScale = Vector3(1.3,1.3,1)
		self.view.animator.enabled = true
	else
		self.view.animator.enabled = false
		self.view.image:LoadSprite(ResPath.GetMarryResPath2("dice_" .. self.data))
		self.view.rect.localRotation = Quaternion.Euler(0, 0, 0)
		self.view.rect.localScale = Vector3(1.1,1.1,1)
		GlobalEventSystem:Fire(MarryDianChunType.SELF, self.index)
		if nil ~= self.dianchun_roll_timer then
			GlobalTimerQuest:CancelQuest(self.dianchun_roll_timer)
			self.dianchun_roll_timer = nil
		end
	end
end

--计时器控制
function ChunNumRender:UpdateRoll()
	if self.is_start then
		if self.timer >= (self.index * 0.2) then
			if not self.is_change_state then
				self:ChangeState()
				self.is_change_state = true
			end
		end

		if self.timer >= ChunNumRender.Time + (self.index * 0.4) then
			self.is_start = false
			if self.is_change_state then
				self:ChangeState()
				self.is_change_state = false
			end
			self.timer = 0
		end
		self.timer = self.timer + MarryView.DianChunUpdateDeltaTime
	end
end

function ChunNumRender:SetIsAction(is_action)
	self.is_action = is_action
end

----------------------------------------------------------------------
--伴侣点唇
FereChunNumRender = FereChunNumRender or BaseClass(BaseRender)
FereChunNumRender.Height = 74
function FereChunNumRender:__init()
	self.is_action = false
	self.is_start = false
	self.default_roll_speed = 10
	self.min_roll_speed = 2
	self.atten_speed = 0
	self.cur_roll_speed = 0
	self.speed_cut_time = 0
	self.is_can_end = false
	self.old_data = -1
end

function FereChunNumRender:__delete()
	if nil ~= self.dianchun_roll_timer then
		GlobalTimerQuest:CancelQuest(self.dianchun_roll_timer)
		self.dianchun_roll_timer = nil
	end

	self.old_data = -1
end

function FereChunNumRender:LoadCallBack()
	if self.dianchun_roll_timer then
		GlobalTimerQuest:CancelQuest(self.dianchun_roll_timer)
	end
	self.max_height = self:getPositionY1() + FereChunNumRender.Height
	self.view.image:LoadSprite(ResPath.GetMarryResPath2("marry_end_" .. math.random(1, 6)))
	self.node_list["item_cell"].image:LoadSprite(ResPath.GetMarryResPath2("marry_end_" .. math.random(1, 6)))

end

function FereChunNumRender:OnFlush()
	self.view:SetActive(self.data > 0)
	self.node_list["item_cell"]:SetActive(self.data > 0)
	if self.data > 0 then
		if self.is_action then
			self.cur_roll_speed = self.default_roll_speed + math.random(2, 10)
			self.speed_cut_time = Status.NowTime + 1 + self.index * 0.5
			if not self.dianchun_roll_timer then
				self.dianchun_roll_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateRoll, self), MarryView.DianChunUpdateDeltaTime)
			end

			self.is_start = true
		else
			self.view.image:LoadSprite(ResPath.GetMarryResPath2("marry_end_" .. self.data))
		end
	end
end

function FereChunNumRender:UpdateRoll()
	if self.is_start then
		if 0 == self.atten_speed and 0 ~= self.speed_cut_time and Status.NowTime > self.speed_cut_time then
			self.atten_speed = 0.1
		end

		local is_min_speed = self.cur_roll_speed <= self.min_roll_speed
		if not is_min_speed then
			self.cur_roll_speed = self.cur_roll_speed - self.atten_speed
		end

		self:setPositionY1(self:getPositionY1() + self.cur_roll_speed)
		self:setPositionY2(self:getPositionY2() + self.cur_roll_speed)

		if self:getPositionY1() > self.max_height then
			if is_min_speed then
				self.is_can_end = true
				self.view.image:LoadSprite(ResPath.GetMarryResPath2("marry_end_" .. math.random(1, 6)))
			end
			self:setPositionY1(self:getPositionY1() - FereChunNumRender.Height * 2)
		end

		if self:getPositionY2() > self.max_height then
			if self.is_can_end then
				self.node_list["item_cell"].image:LoadSprite(ResPath.GetMarryResPath2("marry_end_" .. self.data))
			else
				self.node_list["item_cell"].image:LoadSprite(ResPath.GetMarryResPath2("marry_end_" .. math.random(1, 6)))
			end
			self:setPositionY2(self:getPositionY2() - FereChunNumRender.Height * 2)
		end

		if self.is_can_end then
			if math.abs(self:getPositionY1() - FereChunNumRender.Height / 2) <= self.min_roll_speed then
				GlobalEventSystem:Fire(MarryDianChunType.COMPANION, self.index)
				self:ClearRoll()
			end
		end
	end
end

function FereChunNumRender:setPositionY1(posY)
	self.view.rect.anchoredPosition = Vector2(self:getPositionX1(),posY)
end

function FereChunNumRender:getPositionX1()
	return self.view.rect.anchoredPosition.x
end

function FereChunNumRender:getPositionY1()
	return self.view.rect.anchoredPosition.y
end

function FereChunNumRender:setPositionY2(posY)
	self.node_list["item_cell"].rect.anchoredPosition = Vector2(self:getPositionX2(),posY)
end

function FereChunNumRender:getPositionX2()
	return self.node_list["item_cell"].rect.anchoredPosition.x
end

function FereChunNumRender:getPositionY2()
	return self.node_list["item_cell"].rect.anchoredPosition.y
end

function FereChunNumRender:ClearRoll()
	self.is_start = false
	self.atten_speed = 0
	self.cur_roll_speed = 0
	self.speed_cut_time = 0
	self.is_can_end = false
	if self.dianchun_roll_timer then
		GlobalTimerQuest:CancelQuest(self.dianchun_roll_timer)
		self.dianchun_roll_timer = nil
	end
end

function FereChunNumRender:SetIsAction(is_action)
	self.is_action = is_action
end

------------------------------------------------------------------------
------------------------------- 点唇奖励 -------------------------------
------------------------------------------------------------------------
ChunRewardRender = ChunRewardRender or BaseClass(BaseRender)

function ChunRewardRender:__init()

end

function ChunRewardRender:__delete()
	for k,v in pairs(self.reward_list) do
		v:DeleteMe()
	end
	self.reward_list = nil
end

function ChunRewardRender:LoadCallBack()
	self.reward_list = {}
	for i = 1, 2 do
		local cell = ItemCell.New(self.node_list["item_cell_"..i])
		-- cell:GetView():setScale(0.7)
		self.reward_list[i] = cell
	end
end

function ChunRewardRender:OnFlush()
	for k,v in pairs(self.data.reward_item) do
		if nil ~= self.reward_list[k + 1] then
			self.reward_list[k + 1]:SetData(v)
		end
	end
	self.node_list["num"].text.text = (string.format(Language.Marry.ChunRewardDesc, self.data.dianchun_count))
end