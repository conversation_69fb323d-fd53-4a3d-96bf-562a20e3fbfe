﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System.IO;

public sealed class ResourceRecorderRuntimeGUI
{
    private static bool isRecording = false;
    private static Dictionary<string, Info> recordings = new Dictionary<string, Info>();
    private bool autoRecording = false;
    private bool showDetaile = false;
    private static Queue<Info> lastResources = new Queue<Info>();
    private bool isShow;
   
    public void ShowGUI()
    {
        isShow = true;
    }

    public void OnGUI()
    {
        if (!isShow) return;

        if (autoRecording)
        {
            autoRecording = false;
            Start();
        }
        GUILayout.BeginHorizontal();
        if (isRecording)
        {
            if (GUILayout.Button("Stop"))
            {
                Stop();
            }
        }
        else
        {
            if (GUILayout.Button("Start"))
            {
                Start();
            }
        }

        if (GUILayout.Button("Clean"))
        {
            recordings.Clear();
        }

        GUILayout.EndHorizontal();

        if (GUILayout.Button("Save"))
        {
            Save();
        }

        if (isRecording)
        {
            GUILayout.Space(10);
            GUILayout.Label("游戏运行时间： " + (int)Time.realtimeSinceStartup);
            GUILayout.Space(10);

            showDetaile = GUILayout.Toggle(showDetaile, "Detail");
            if (!showDetaile)
            {
                return;
            }

            if (lastResources.Count > 0)
            {
                GUILayout.BeginVertical();

                GUILayout.BeginHorizontal();
                GUILayout.Label("BundleName");
                GUILayout.Label("AssetName");
                GUILayout.Label("UseCount");
                GUILayout.EndHorizontal();

                Info[] infos = lastResources.ToArray();
                for (int i = infos.Length - 1; i >= 0; --i)
                {
                    Info info = infos[i];
                    GUILayout.BeginHorizontal();
                    GUILayout.Label(info.bundleName);
                    GUILayout.Label(info.assetName);
                    GUILayout.Label(info.useCount.ToString());
                    GUILayout.EndHorizontal();
                }

                GUILayout.EndVertical();
            }
        }
    }

    private static void Start()
    {
        if (isRecording)
            return;

        if (null == GameRoot.Instance)
            return;

        isRecording = true;
        RuntimeGUIMgr.Instance.SetLoadObjectCallback(LoadAction);
     
    }

    private static void Stop()
    {
        isRecording = false;
        RuntimeGUIMgr.Instance.SetLoadObjectCallback(null);
    }

    private static void LoadAction(string bundleName, string assetName, Object asset)
    {
        if (string.IsNullOrEmpty(bundleName) || string.IsNullOrEmpty(assetName))
            return;

        Info info;
        if (!recordings.TryGetValue(bundleName, out info))
        {
            int level = GameRoot.Instance.GetMainRoleLevel();
            info = new Info();
            info.bundleName = bundleName;
            info.level = level;
            info.firstUseTimeStamp = (int)Time.realtimeSinceStartup;
            info.useCount = 1;
            info.assetName = assetName;
            recordings.Add(bundleName, info);
        }
        else
        {
            info.useCount = info.useCount + 1;
            info.assetName = assetName;
            recordings[bundleName] = info;
        }

        lastResources.Enqueue(info);
        if (lastResources.Count > 15)
        {
            lastResources.Dequeue();
        }
    }

    private static void Save()
    {
        string path = string.Format("{0}/Resource Recordings.csv", RuntimeGUIMgr.Instance.GetDataPath());
        using (var file = File.Open(path, FileMode.Create, FileAccess.Write))
        using (var writer = new StreamWriter(file))
        {
            file.WriteByte(239); // 0xEF
            file.WriteByte(187); // 0xBB
            file.WriteByte(191); // 0xBF

            writer.WriteLine(
                "BundleName,Level,FirstUseTimeStamp,UseCount");
            foreach (var kv in recordings)
            {
                var bundleName = kv.Key;
                var info = kv.Value;

                writer.WriteLine(
                    bundleName + ',' +
                    info.level + ',' +
                    info.firstUseTimeStamp + ',' +
                    info.useCount + ',');
            }
        }
    }

    private struct Info
    {
        public string bundleName;
        public int level;
        public int firstUseTimeStamp;
        public int useCount;
        public string assetName;
    }
}
