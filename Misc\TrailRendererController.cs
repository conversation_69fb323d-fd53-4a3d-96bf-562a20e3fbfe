﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

public class TrailRendererController : MonoBehaviour
{
    private List<TrailRendererItem> trailRendererList = null;

//     private void Update ()
//     {
//         if (this.trailRendererList.Count > 0)
//         {
//             this.RefreshTrailRender();
//         }
//     }

    private void OnEnable()
    {
        if (null == this.trailRendererList)
        {
            this.trailRendererList = new List<TrailRendererItem>();
            var renderers = ListPool<TrailRenderer>.Get();

            this.GetComponentsInChildren<TrailRenderer>(renderers);
            for (int i = 0; i < renderers.Count; i++)
            {
                TrailRendererItem item = new TrailRendererItem();
                item.renderer = renderers[i];
                item.renderer.Clear();
                this.trailRendererList.Add(item);
            }

            ListPool<TrailRenderer>.Release(renderers);
        }
        else
        {
            for (int i = 0; i < this.trailRendererList.Count; i++)
            {
                var item = this.trailRendererList[i];
                if (null != item.renderer)
                {
                    item.renderer.Clear();
                }
                item.lastPosition = Vector3.zero;
            }
        }
    }

    public void RefreshTrailRender()
    {
        if (null == this.trailRendererList) return;

        for (int i = this.trailRendererList.Count - 1; i >= 0; --i)
        {
            TrailRendererItem item = this.trailRendererList[i];
            if (null == item.renderer)
            {
                this.trailRendererList.RemoveAt(i);
                continue;
            }

            Vector3 now_pos = item.renderer.transform.position;
            if ((now_pos - item.lastPosition).sqrMagnitude >= 4 * 4)
            {
                item.renderer.Clear();
            }
            item.lastPosition = now_pos;
        }
    }
}

class TrailRendererItem
{
    public TrailRenderer renderer;
    public Vector3 lastPosition;
}

