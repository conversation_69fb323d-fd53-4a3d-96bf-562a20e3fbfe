TehuiShopWGData = TehuiShopWGData or BaseClass()

function TehuiShopWGData:__init()
	if TehuiShopWGData.Instance ~= nil then
		print("[TehuiShopWGData] attempt to create singleton twice!")
		return		
	end
	TehuiShopWGData.Instance = self

	self.ra_panic_buy_server_num = {}
	self.batch = 0
	self.level = 0
end

function TehuiShopWGData:__delete()
	TehuiShopWGData.Instance = nil
end

function TehuiShopWGData:SetRAPanicBuyInfo(protocol)
	self.ra_panic_buy_server_num = protocol.ra_panic_buy_server_num or {}
	self.batch = protocol.batch or 0
	self.ra_panic_buy_num = protocol.ra_panic_buy_num
	self.level = protocol.level or 0
end

function TehuiShopWGData:GetRAPanicBuyBatch()
	return self.batch
end

function TehuiShopWGData:GetCanBuyTimes()
	return self.ra_panic_buy_server_num
end

function TehuiShopWGData:GetHaveBoughtTimes()
	return self.ra_panic_buy_num
end

function TehuiShopWGData:GetTeHuiShopCfg()
	local data_list = {}
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	for k,v in pairs(cfg.panic_buy_item) do
		if self.batch == v.batch and self.level >= v.role_level and self.level <= v.level_max then
			table.insert(data_list, v)
		end
	end
	return data_list
end

function TehuiShopWGData:GetNextTeHuiShopCfg()
	local data_list = {}
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	for k,v in pairs(cfg.panic_buy_item) do
		if self.batch ~= v.batch and self.level >= v.role_level and self.level <= v.level_max then
			table.insert(data_list, v)
		end
	end
	return data_list
end

-- 下次刷新时间
function TehuiShopWGData:GetNextTeHuiShopFreshTime(index)
	local fresh_total_time = 0
	local flush_time = 0
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local curr_time_list = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	for k,v in pairs(cfg.panic_buy_flush) do
		if index == v.batch then
			flush_time = (v.flush_time / 100) * 60 * 60
		end
	end
	fresh_total_time = flush_time - (curr_time_list.hour * 60 * 60 + curr_time_list.min * 60 + curr_time_list.sec)
	return fresh_total_time
end