ActIvityHallWGData = ActIvityHallWGData or BaseClass()

function ActIvityHallWGData:__init()
	if ActIvityHallWGData.Instance then
		ErrorLog("[ActIvityHallWGData] Attemp to create a singleton twice !")
	end
	ActIvityHallWGData.Instance = self
end

function ActIvityHallWGData:__delete()
	ActIvityHallWGData.Instance = nil
end

function ActIvityHallWGData:GetActivityHallCfg()
	return BiZuoWGData.Instance:GetActivityHallAllInfo()
end

--根据类型获得活动配置
function ActIvityHallWGData:GetActivityCfgByType(act_type)
	local daily_cfg = ConfigManager.Instance:GetAutoConfig("dailywork_auto").activity_hall
	for k,v in pairs(daily_cfg) do
		if v.act_type == act_type then
			return TableCopy(v)
		end
	end
	return nil
end

function ActIvityHallWGData:GetCaiShenCiFuPos()
	local point = math.random(1,9)
	local pos_cfg = ConfigManager.Instance:GetAutoConfig("caishencifuconfig_auto").flush_pos
	for k,v in pairs(pos_cfg) do
		if v.pos == point then
			return v
		end
	end
end

function ActIvityHallWGData:GetLevelRank(type,list)
	local len_num = #list
	if len_num <= 0 then return end
	if type == 1 and #list >= 10 then
		self.level = list[10].level
	elseif type == 1 and #list <10 then
		self.level = list[#list].level
	end
end

function ActIvityHallWGData:GetBossInfo()
	local boss_cfg = ConfigManager.Instance:GetAutoConfig("zhuxieconfig_auto").show_reward
	for i = 1,#boss_cfg do
		if boss_cfg[i].world_max_level >= self.level and boss_cfg[i].world_min_level then
			return boss_cfg[i].item
		elseif boss_cfg[i].world_max_level <self.level then
			return boss_cfg[#boss_cfg].item
		elseif boss_cfg[i].world_min_level > self.level then
			return boss_cfg[1].item
		end
	end
end
