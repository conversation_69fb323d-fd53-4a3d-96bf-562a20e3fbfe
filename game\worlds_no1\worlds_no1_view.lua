-- 天下第一
WorldsNO1ViewTAB = {
	INFO = 1,  -- 信息
	AUDITION = 2, -- 海选
	KNOCKOUT = 3, -- 淘汰
}
WorldsNO1View = WorldsNO1View or BaseClass(SafeBaseView)
function WorldsNO1View:__init()
	self.view_style = ViewStyle.Full
	self.view_layer = UiLayer.Normal
	self:SetMaskBg(true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)

	self:AddViewResource(0, "uis/view/worlds_no1_ui_prefab", "layout_worlds_no1")
end

function WorldsNO1View:__delete()

end

function WorldsNO1View:OpenCallBack()
	-- 请求获得上届冠军信息
	WorldsNO1WGCtrl.Instance:SendRequestChampionInfo()
	-- 请求获取点赞信息
	WorldsNO1WGCtrl.Instance:SendRequestLikeInfo()
	-- 请求获取轮次信息
	WorldsNO1WGCtrl.Instance:SendRequestRoundInfo()
end

function WorldsNO1View:CloseCallBack()
end

function WorldsNO1View:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["reward_btn"], BindTool.Bind(self.OnClickRewardBtn, self))                        -- 奖励按钮
	XUI.AddClickEventListener(self.node_list["timetable_btn"], BindTool.Bind(self.OnClickTimetableBtn, self))                  -- 对战场次按钮
	XUI.AddClickEventListener(self.node_list["rule_btn"], BindTool.Bind(self.OnClickRuleBtn, self))                            -- 规则按钮
	XUI.AddClickEventListener(self.node_list["info_btn"], BindTool.Bind(self.OnClickTabBtn, self, WorldsNO1ViewTAB.INFO))      -- 信息按钮
	XUI.AddClickEventListener(self.node_list["audition_btn"],
		BindTool.Bind(self.OnClickTabBtn, self, WorldsNO1ViewTAB.AUDITION))                                                    -- 海选信息按钮
	XUI.AddClickEventListener(self.node_list["knockout_btn"],
		BindTool.Bind(self.OnClickTabBtn, self, WorldsNO1ViewTAB.KNOCKOUT))                                                    -- 淘汰信息按钮
	XUI.AddClickEventListener(self.node_list["enter_btn"], BindTool.Bind(self.OnClickEnter, self))                             -- 进入活动按钮
	XUI.AddClickEventListener(self.node_list["no1_player_like_btn"], BindTool.Bind(self.OnClickLike, self))                    -- 冠军点赞按钮

	-- 淘汰赛子标签按钮列表
	self.knockout_tab_btn_list = {}
	local knockout_cfg = WorldsNO1WGData.Instance:GetKnockoutCfg()
	local prefab = self.node_list["knockout_sub_btn_prefab"].gameObject
	prefab:SetActive(false)
	for index, cfg in ipairs(knockout_cfg) do
		local obj = ResMgr:Instantiate(prefab)
		obj.transform:SetParent(self.node_list["knockout_tab_list"].gameObject.transform, false)
		obj:SetActive(true)
		self.knockout_tab_btn_list[index] = WorldsNO1KnockoutTabBtn.New(obj)
		self.knockout_tab_btn_list[index]:SetData(cfg)
		self.knockout_tab_btn_list[index]:SetClickCallback(BindTool.Bind(self.OnClickKnockoutTabBtn, self))
	end

	self.knockout_rank_list = AsyncListView.New(WorldsNO1KnockoutRankItem, self.node_list["knockout_list"]) -- 淘汰赛排名列表
	self.main_role_knockout_item = WorldsNO1KnockoutRankItem.New(self.node_list["knockout_my_rank_item"]) -- 主角淘汰赛信息
	self.main_role_knockout_item:SetIsBottomInfo(true)
	self.audition_rank_list = AsyncListView.New(WorldsNO1AuditionRankItem, self.node_list["audition_list"]) -- 海选排名列表
	self.main_role_audition_item = WorldsNO1AuditionRankItem.New(self.node_list["audition_my_rank_item"]) -- 主角海选赛信息
	self.main_role_audition_item:SetIsBottomInfo(true)

	-- 模型
	self.role_model = RoleModel.New()
	local display_data = {
		parent_node = self.node_list["role_model"],
		camera_type = MODEL_CAMERA_TYPE.BASE,
		-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		rt_scale_type = ModelRTSCaleType.M,
		can_drag = true,
	}
	
	self.role_model:SetRenderTexUI3DModel(display_data)
	-- self.role_model:SetUI3DModel(self.node_list["role_model"].transform,
	-- 	self.node_list["role_model"].event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
	self:AddUiRoleModel(self.role_model)

	-- 头像
	self.no1_player_head = BaseHeadCell.New(self.node_list["no1_player_head"])
	XUI.AddClickEventListener(self.node_list["no1_player_head"], BindTool.Bind(self.OnClickNO1PlayerHead, self))

	self:SetCurSelectTab(WorldsNO1ViewTAB.INFO)
	self:SetKnockoutTab(1)

	-- 刷新时间轴计时器
	self.flush_time_slider_timer = GlobalTimerQuest:AddRunQuest(function()
		self:FlushTimeSlider()
	end, 60)

	UITween.ScaleShowPanel(self.node_list["bg"], Vector3(0.75, 0.75, 0.75), 0.8, DG.Tweening.Ease.OutQuart)
	UITween.ScaleShowPanel(self.node_list["model"], Vector3(0.75, 0.75, 0.75), 0.8, DG.Tweening.Ease.OutQuart)

	self.remind_callback = BindTool.Bind(self.OnRemindChange, self)
	RemindManager.Instance:Bind(self.remind_callback, RemindName.WorldsNO1EnterRemind)
	RemindManager.Instance:Bind(self.remind_callback, RemindName.WorldsNO1LikeRemind)
end

function WorldsNO1View:ReleaseCallBack()
	RemindManager.Instance:UnBind(self.remind_callback)

	if self.flush_time_slider_timer then
		GlobalTimerQuest:CancelQuest(self.flush_time_slider_timer)
		self.flush_time_slider_timer = nil
	end

	for i, v in ipairs(self.knockout_tab_btn_list) do
		v:DeleteMe()
	end

	if self.knockout_rank_list then
		self.knockout_rank_list:DeleteMe()
		self.knockout_rank_list = nil
	end

	if self.audition_rank_list then
		self.audition_rank_list:DeleteMe()
		self.audition_rank_list = nil
	end

	if self.main_role_knockout_item then
		self.main_role_knockout_item:DeleteMe()
		self.main_role_knockout_item = nil
	end

	if self.main_role_audition_item then
		self.main_role_audition_item:DeleteMe()
		self.main_role_audition_item = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end

	if self.no1_player_head then
		self.no1_player_head:DeleteMe()
		self.no1_player_head = nil
	end

	self.select_tab = nil
	self.select_knockout_index = nil
end

function WorldsNO1View:OnFlush(param_t)
	for k, v in pairs(param_t) do
		if k == "all" then
			-- 配合C-传闻配置
			if v.link_cfg_key then
				local keys = Split(v.link_cfg_key, ":")
				local tab_index = keys[1]
				local subround_index = keys[2]
				if subround_index then
					self:SetCurSelectTab(tonumber(tab_index), tonumber(subround_index))
				else
					self:SetCurSelectTab(tonumber(tab_index))
				end
			end
			self:FlushAllView()
		elseif k == "change_select_tab" then
			self:SetCurSelectTab(v.select_tab, v.subround_index)
			self:FlushAllView()
		elseif k == "no1_palyer_info" then
			self:FlushNO1PlayerInfo()
		elseif k == "knockout_list" then
			self:FlushKnockoutList()
		elseif k == "others" then
			self:FlushOthers()
		end
	end
end

function WorldsNO1View:FlushAllView()
	self:FlushTabBtnActive()
	self:FlushPanelActive()
	self:FlushAuditionList()
	self:FlushKnockoutList()
	self:FlushOthers()
	self:FlushSliderPanel()
	self:FlushNO1PlayerInfo()
	self:FlushRemind()
end

-- 刷新标签按钮显隐藏
function WorldsNO1View:FlushTabBtnActive()
	local first_knockout_cfg = WorldsNO1WGData.Instance:GetKnockoutCfgByIndex(1)                         -- 淘汰赛第一子轮的配置
	local last_round_audition_cfg_list = WorldsNO1WGData.Instance:GetRoundCfg(first_knockout_cfg.round - 1) -- 海选最后一轮配置列表
	local last_audition_subround_cfg = last_round_audition_cfg_list[#last_round_audition_cfg_list]       -- 海选最后一轮最后一子轮的配置
	local show_knockout = true                                                                           --WorldsNO1WGData.Instance:GetSubroundStatus(last_audition_subround_cfg.round, last_audition_subround_cfg.subround) == WORLDS_NO1_SUBROUND_STATUS.OVER or WorldsNO1WGData.Instance:GetCurRound() == 0
	self.node_list["knockout_btn"]:SetActive(show_knockout)
	self.node_list["knockout_tab_list"]:SetActive(show_knockout)
end

function WorldsNO1View:FlushPanelActive()
	self.node_list["info_panel"]:SetActive(self.select_tab == WorldsNO1ViewTAB.INFO)
	self.node_list["rank_panel"]:SetActive(self.select_tab ~= WorldsNO1ViewTAB.INFO)
	self.node_list["audition_panel"]:SetActive(self.select_tab == WorldsNO1ViewTAB.AUDITION)
	self.node_list["knockout_panel"]:SetActive(self.select_tab == WorldsNO1ViewTAB.KNOCKOUT)

	self.node_list["info_btn_hl"]:SetActive(self.select_tab == WorldsNO1ViewTAB.INFO)
	self.node_list["audition_btn_hl"]:SetActive(self.select_tab == WorldsNO1ViewTAB.AUDITION)
	self.node_list["knockout_btn_hl"]:SetActive(self.select_tab == WorldsNO1ViewTAB.KNOCKOUT)

	-- 背景图
	self.node_list["bg"]:SetActive(self.select_tab == WorldsNO1ViewTAB.INFO)
	self.node_list["bg_2"]:SetActive(self.select_tab ~= WorldsNO1ViewTAB.INFO)
end

-- 刷新海选赛列表
function WorldsNO1View:FlushAuditionList()
	local audition_info_list = WorldsNO1WGData.Instance:GetAuditionRankInfo()
	self.audition_rank_list:SetDataList(audition_info_list)
	self.node_list["audition_empty_tips"]:SetActive(IsEmptyTable(audition_info_list))

	-- 主角海选赛排名信息
	self.main_role_audition_item:SetData(WorldsNO1WGData.Instance:GetMainRoleAudtionRankInfo()) -- self.select_knockout_index
end

-- 刷新淘汰赛列表
function WorldsNO1View:FlushKnockoutList()
	self.node_list["operator_text"]:SetActive(ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1))
	local knockout_info_list = WorldsNO1WGData.Instance:GetKnockoutRankInfo(self.select_knockout_index)
	self.knockout_rank_list:SetDataList(knockout_info_list)
	self.node_list["knockout_empty_tips"]:SetActive(IsEmptyTable(knockout_info_list))

	-- 主角淘汰赛排名信息
	self.main_role_knockout_item:SetData(WorldsNO1WGData.Instance:GetMianRoleKnockoutRankInfo(self.select_knockout_index))
end

-- 刷新赛季进度面板
function WorldsNO1View:FlushSliderPanel()
	local round_cfg_list = WorldsNO1WGData.Instance:GetRoundCfg()
	local has_round_openning = false
	for i, v in ipairs(round_cfg_list) do
		if self.node_list["round_" .. i .. "_desc"] and self.node_list["round_" .. i .. "_state_label"] then
			local time_info = WorldsNO1WGData.Instance:GetRoundTimeInfo(v[1].round)
			local open_time_tab = time_info.open_time_tab
			local close_time_tab = time_info.close_time_tab
			local time_str = string.format(Language.WorldsNO1.RoundTime, open_time_tab.month, open_time_tab.day) -- 开启日期
			local cur_subround_state_str = ""                                                           -- 子轮状态
			local subround_status = WorldsNO1WGData.Instance:GetSubroundStatus(v[1].round,
				WorldsNO1WGData.Instance:GetCurSubround())
			-- 是否子轮开启中
			if subround_status == WORLDS_NO1_SUBROUND_STATUS.OPENNING then
				local round_cfg = WorldsNO1WGData.Instance:GetRoundCfg(v[1].round,
					WorldsNO1WGData.Instance:GetCurSubround())
				local subround_stage = WorldsNO1WGData.Instance:GetCurSubroundStage()
				-- 是否处于准备阶段
				if subround_stage == WORLDS_NO1_SUBROUND_STAGE.PREPARE then
					local next_timestamp = WorldsNO1WGData.Instance:GetNextChangeStatusTimestamp()
					local time_str = TimeUtil.FormatHM(math.floor(next_timestamp))
					cur_subround_state_str = string.format(Language.WorldsNO1.SubroundPrepare, time_str,
						round_cfg.sub_round_name)
					-- 是否处于战斗阶段
				elseif subround_stage == WORLDS_NO1_SUBROUND_STAGE.PK then
					cur_subround_state_str = string.format(Language.WorldsNO1.SubroundIsOpenning,
						round_cfg.sub_round_name)
				end
			end

			self.node_list["round_" .. i .. "_desc"].text.text = v[1].round_name ..
			ToColorStr(time_str, COLOR3B.D_GREEN) .. cur_subround_state_str
			local round_state = WorldsNO1WGData.Instance:GetRoundState(i)
			local will_open = WorldsNO1WGData.Instance:GetRoundTodayWillOpen(i)
			if not will_open and round_state == WORLDS_NO1_ROUND_STATUS.UNOPENED then
				self.node_list["round_" .. i .. "_state_label"].image.enabled = false
			else
				local bundle, asset
				if will_open then
					bundle, asset = ResPath.GetWorldsNO1Img("round_state_will_open")
				else
					bundle, asset = ResPath.GetWorldsNO1Img("round_state_" .. round_state)
				end
				self.node_list["round_" .. i .. "_state_label"].image:LoadSpriteAsync(bundle, asset, function()
					if self.node_list["round_" .. i .. "_state_label"] then
						self.node_list["round_" .. i .. "_state_label"].image:SetNativeSize()
					end
				end)
			end
			if round_state == WORLDS_NO1_ROUND_STATUS.OPENNING then
				has_round_openning = true
			end
		end
	end

	-- 进度条特效
	self.node_list["slider_effect"]:SetActive(has_round_openning)

	-- 活动时间
	self.node_list["act_time"].text.text = WorldsNO1WGData.Instance:GetWorldsNO1ActTimeDesc()

	self:FlushTimeSlider()
end

-- 刷新时间轴
function WorldsNO1View:FlushTimeSlider()
	if self.node_list["time_slider"] then
		self.node_list["time_slider"].slider.value = WorldsNO1WGData.Instance:GetTimeSliderValue()
	end
end

-- 刷新第一名玩家信息
function WorldsNO1View:FlushNO1PlayerInfo()
	local champion_info = WorldsNO1WGData.Instance:GetChampionInfo()
	self.node_list["no1_player_panel"]:SetActive(champion_info ~= nil)
	if champion_info then
		self.node_list["no1_player_kill_amount"].text.text = string.format(Language.WorldsNO1.KillAmount,
			champion_info.kill_count)                                                                                                      -- 累计杀敌数
		self.node_list["no1_player_champion_amount"].text.text = string.format(Language.WorldsNO1.ChampionAmount,
			champion_info.champion_count)                                                                                                  -- 累计获得冠军次数
		self.node_list["no1_player_final_amount"].text.text = string.format(Language.WorldsNO1.FinalAmount,
			champion_info.top_10_count)                                                                                                    -- 累计进入前十次数
		self.node_list["no1_player_like_btn"]:SetActive(not WorldsNO1WGData.Instance:GetIsLiked())
	end

	-- 上次第一名玩家模型
	self:FlushModel()
end

-- 刷新模型
function WorldsNO1View:FlushModel()
	local champion_info = WorldsNO1WGData.Instance:GetChampionInfo()
	self.node_list["xuwei_bg"]:SetActive(champion_info == nil)
	self.node_list["role_model"]:SetActive(champion_info ~= nil)
	if champion_info then
		BrowseWGCtrl.Instance:SendGlobalQueryRoleInfo(champion_info.plat_type, champion_info.uid, nil,
			function(protocol_vo)
				if not self.node_list or not self.role_model then
					return
				end
				-- 头像
				local data = {}
				data.role_id = protocol_vo.role_id
				data.prof = protocol_vo.prof
				data.sex = protocol_vo.sex
				data.server_id = protocol_vo.server_id
				self.no1_player_head:SetData(data)
				if not IsSameTable(self.role_model:GetModelResInfo(), protocol_vo) then
					self.role_model:SetModelResInfo(protocol_vo)
					-- 播出场动画
					self.role_model:PlayStartAction("chuchang_jump1")
					GlobalTimerQuest:AddDelayTimer(function()
						if self.role_model then
							self.role_model:PlayRoleShowAction()
						end
					end, 2.8)
				end
				-- self.node_list["title_" .. i].rect.anchoredPosition = Vector3(0, 330, 0)
				-- self.node_list["title_effect_" .. i].rect.anchoredPosition = Vector3(0, 330, 0)
			end)

		-- 角色名
		self.node_list["no1_player_name"].text.text = champion_info.role_name
	end
end

function WorldsNO1View:FlushOthers()
	-- 轮次描述
	local cfg = nil
	if self.select_tab == WorldsNO1ViewTAB.KNOCKOUT then
		cfg = WorldsNO1WGData.Instance:GetTypeKeyRoundCfg(WORLDS_NO1_MATCH_TYPE.KNOCKOUT, self.select_knockout_index)
	else
		cfg = WorldsNO1WGData.Instance:GetTypeKeyRoundCfg(WORLDS_NO1_MATCH_TYPE.AUDITION)[1]
	end
	self.node_list["round_desc"].text.text = cfg.desc

	-- 参与按钮文字
	local act_status = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.WORLDS_NO1)
	local is_open = act_status and act_status.status == ACTIVITY_STATUS.OPEN
	self.node_list["enter_btn_effect"]:SetActive(is_open)
	if is_open then
		self.node_list["enter_btn_name"].image:LoadSprite(ResPath.GetWorldsNO1Img("to_join_text")) -- 前往参与
	else
		self.node_list["enter_btn_name"].image:LoadSprite(ResPath.GetWorldsNO1Img("act_closed_text")) -- 活动未开启
	end
	self.node_list["enter_btn_name"].image:SetNativeSize()

	-- 场次时间
	local day_str = ""
	local time_str = ""
	if self.select_tab == WorldsNO1ViewTAB.KNOCKOUT then
		day_str, time_str = WorldsNO1WGData.Instance:GetKnockoutTime(self.select_knockout_index)
	else
		day_str, time_str = WorldsNO1WGData.Instance:GetAuditionTime()
	end
	self.node_list["round_time"].text.text = string.format(Language.WorldsNO1.RoundTime2, day_str, time_str)

	-- 淘汰赛子节点高亮
	for i, v in ipairs(self.knockout_tab_btn_list) do
		v:SetSelectTab(self.select_tab)
		v:SetSelectKnockoutIndex(self.select_knockout_index)
	end

	-- 投票次数
	self.node_list["bet_times"].text.text = ""
	local knockout_rank_info_list = WorldsNO1WGData.Instance:GetKnockoutRankInfo(self.select_knockout_index)
	if self.select_tab == WorldsNO1ViewTAB.KNOCKOUT and not IsEmptyTable(knockout_rank_info_list) then
		if self.select_knockout_index == WorldsNO1WGData.Instance:GetCurSubround() then
			local remain_bet_times = WorldsNO1WGData.Instance:GetRemainBetCount() -- 剩余投票次数
			local bet_cfg = WorldsNO1WGData.Instance:GetBetCfgBySubround(self.select_knockout_index)
			local max_times = bet_cfg.guess_count
			local color = COLOR3B.D_GREEN
			if remain_bet_times == 0 then
				color = COLOR3B.D_RED
			end
			local str = string.format(Language.WorldsNO1.BetTimes,
				ToColorStr(remain_bet_times .. "/" .. max_times, color))
			self.node_list["bet_times"].text.text = str
		end
	end
end

-- 点击奖励按钮
function WorldsNO1View:OnClickRewardBtn()
	ViewManager.Instance:Open(GuideModuleName.WorldsNO1RewardView)
end

-- 点击对战场次按钮
function WorldsNO1View:OnClickTimetableBtn()
	ViewManager.Instance:Open(GuideModuleName.WorldsNO1TimetableView)
end

-- 点击规则按钮
function WorldsNO1View:OnClickRuleBtn()
	RuleTip.Instance:SetContent(Language.WorldsNO1.RuleTips, Language.WorldsNO1.RuleTipsTitle)
end

-- 点击页签按钮
function WorldsNO1View:OnClickTabBtn(tab)
	self:SetCurSelectTab(tab)
end

-- 点击进入活动场景
function WorldsNO1View:OnClickEnter(tab)
	if Scene.Instance:GetSceneType() == SceneType.WorldsNO1Prepare then
		self:Close()
		ViewManager.Instance:Close(GuideModuleName.ActJjc)
	else
		if ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1) then
			CrossServerWGCtrl.Instance.SendCrossStartReq(ACTIVITY_TYPE.WORLDS_NO1)
		else
			local cur_round = WorldsNO1WGData.Instance:GetCurRound()
			local tip = Language.WorldsNO1.ActIsOver
			local next_round_time_info, next_round = WorldsNO1WGData.Instance:GetNextRoundTimeInfo(cur_round)
			local next_round_cfg = WorldsNO1WGData.Instance:GetRoundCfg(next_round, 1)
			local tab = next_round_time_info.open_time_tab
			tip = string.format(Language.WorldsNO1.NextRoundOpenTips, next_round_cfg.round_name, tab.month, tab.day,
				tab.hour, tab.min)
			TipWGCtrl.Instance:ShowSystemMsg(tip)
		end
	end
end

-- 点赞按钮
function WorldsNO1View:OnClickLike()
	WorldsNO1WGCtrl.Instance:SendLike()
end

-- 点击上届冠军头像
function WorldsNO1View:OnClickNO1PlayerHead()
	self.no1_player_head:OpenMenu(self.node_list["no1_player_head"], nil, Vector2(-150, -150))
end

-- 点击淘汰赛子页签按钮
function WorldsNO1View:OnClickKnockoutTabBtn(knockout_cfg)
	self:SetCurSelectTab(WorldsNO1ViewTAB.KNOCKOUT, knockout_cfg.subround)
end

-- 设置当前选中的tab
function WorldsNO1View:SetCurSelectTab(tab, select_knockout_index)
	self.select_tab = tab
	if self.select_tab == WorldsNO1ViewTAB.AUDITION then
		WorldsNO1WGCtrl.Instance:SendRequestRankInfo(WORLDS_RANK_INFO_TYPE.WORLDS_RANK_INFO_TYPE_0)
	else
		local round_index = WorldsNO1WGData.Instance:GetCurRound()
		local subround_index = WorldsNO1WGData.Instance:GetCurSubround()
		local round_cfg = WorldsNO1WGData.Instance:GetRoundCfg(round_index, subround_index)
		if select_knockout_index and select_knockout_index > 0 then
			self:SetKnockoutTab(select_knockout_index)
		elseif round_cfg and round_cfg.type == WORLDS_NO1_MATCH_TYPE.KNOCKOUT then
			self:SetKnockoutTab(subround_index)
		else
			self:SetKnockoutTab(1) -- 选中第一个淘汰赛子页签
		end
	end
	self:FlushPanelActive()
	self:FlushOthers()
end

-- 设置当前选中的淘汰赛子页签
function WorldsNO1View:SetKnockoutTab(index)
	self.select_knockout_index = index
	if self.select_tab == WorldsNO1ViewTAB.KNOCKOUT then
		WorldsNO1WGCtrl.Instance:SendRequestRankInfo(KNOCKOUT_SUBROUND_TO_WORLDS_RANK_INFO_TYPE
		[self.select_knockout_index])
	end

	self:FlushOthers()
end

-- 红点回调
function WorldsNO1View:OnRemindChange()
	self:FlushRemind()
end

-- 刷新红点
function WorldsNO1View:FlushRemind()
	self.node_list["enter_remind"]:SetActive(RemindManager.Instance:GetRemind(RemindName.WorldsNO1EnterRemind) > 0)
	self.node_list["like_remind"]:SetActive(RemindManager.Instance:GetRemind(RemindName.WorldsNO1LikeRemind) > 0)
	self.node_list["like_remind_2"]:SetActive(RemindManager.Instance:GetRemind(RemindName.WorldsNO1LikeRemind) > 0)
end

------------------ 淘汰赛信息按钮 ----------------------
WorldsNO1KnockoutTabBtn = WorldsNO1KnockoutTabBtn or BaseClass(BaseRender)
function WorldsNO1KnockoutTabBtn:__init()
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind(self.OnClick, self))
end

function WorldsNO1KnockoutTabBtn:__delete()

end

function WorldsNO1KnockoutTabBtn:OnFlush()
	if self.data then
		self.node_list["btn_name"].text.text = self.data.btn_name
		self.node_list["hl_btn_name"].text.text = self.data.btn_name
		self.node_list["hl"]:SetActive(self.select_subround == self.data.subround and
		self.select_tab == WorldsNO1ViewTAB.KNOCKOUT)
		self.node_list["btn_name"]:SetActive(not (self.select_subround == self.data.subround and self.select_tab == WorldsNO1ViewTAB.KNOCKOUT))
		local last_subround_cfg = WorldsNO1WGData.Instance:GetRoundCfg(self.data.round, self.data.subround - 1)
		-- -- 结束了就直接显示全部
		-- if WorldsNO1WGData.Instance:GetCurRound() == 0 then
		-- 	self:SetVisible(true)
		-- else
		-- 	if last_subround_cfg then
		-- 		-- 上一子轮打完才显示
		-- 		self:SetVisible(WorldsNO1WGData.Instance:GetSubroundStatus(last_subround_cfg.round, last_subround_cfg.subround) == WORLDS_NO1_SUBROUND_STATUS.OVER)
		-- 	else
		-- 		-- 没拿到配置表明是淘汰赛第一子轮, 直接显示
		-- 		self:SetVisible(true)
		-- 	end
		-- end
	end
end

function WorldsNO1KnockoutTabBtn:SetSelectKnockoutIndex(select_subround)
	self.select_subround = select_subround
	self:Flush()
end

function WorldsNO1KnockoutTabBtn:SetSelectTab(select_tab)
	self.select_tab = select_tab
	self:Flush()
end

function WorldsNO1KnockoutTabBtn:SetClickCallback(click_callback)
	self.click_callback = click_callback
end

function WorldsNO1KnockoutTabBtn:OnClick()
	if self.click_callback then
		self.click_callback(self.data)
	end
end

------------------ 淘汰赛排行榜item ----------------------
WorldsNO1KnockoutRankItem = WorldsNO1KnockoutRankItem or BaseClass(BaseRender)
function WorldsNO1KnockoutRankItem:__init()
	XUI.AddClickEventListener(self.node_list["bet_btn"],
		BindTool.Bind(self.OnClickBetBtn, self, WorldsNO1ViewTAB.KNOCKOUT))                                               -- 竞猜按钮
	XUI.AddClickEventListener(self.node_list["enter_observation_btn"],
		BindTool.Bind(self.OnClickEnterObservationBtn, self))                                                             -- 观战按钮

	-- 头像
	self.head = BaseHeadCell.New(self.node_list["role_head"])
	self.head:SetNeedMenuBtn(true)
	XUI.AddClickEventListener(self.node_list["role_head"], BindTool.Bind(self.OnClickHead, self))
end

function WorldsNO1KnockoutRankItem:__delete()
	if self.head then
		self.head:DeleteMe()
		self.head = nil
	end
end

function WorldsNO1KnockoutRankItem:OnFlush()
	if self.data then
		-- 角色名
		self.node_list["role_name"].text.text = self:ChangeColor(self.data.role_name)

		-- 区服名
		local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id) --内网无法请求PHP，先设置默认名字
		self.node_list["server_name"].text.text = self:ChangeColor(temp_name)

		-- kd
		self.node_list["kd"].text.text = self:ChangeColor(self.data.kill_count) -- .. "/" .. self.data.dead_count

		-- 淘汰赛积分
		self.node_list["score"].text.text = self:ChangeColor(self.data.score_count)

		-- 排名
		self.node_list["rank_txt"]:SetActive(self.data.rank <= 0 or self.data.rank > 3)
		self.node_list["rank_img"]:SetActive(self.data.rank > 0 and self.data.rank <= 3)
		if self.data.rank > 0 then
			if self.data.rank <= 3 then
				local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.data.rank)
				self.node_list["rank_img"].image:LoadSpriteAsync(bundle, asset, function()
					self.node_list["rank_img"].image:SetNativeSize()
				end)
			else
				self.node_list["rank_txt"].text.text = self.is_bottom_info and self.data.rank or
				self:ChangeColor(self.data.rank)
			end
		else
			self.node_list["rank_txt"].text.text = Language.WorldsNO1.OutOfRankList -- 未上榜
		end

		-- 操作		
		local act_is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.WORLDS_NO1)
		self.node_list["operate"]:SetActive(act_is_open)
		local subround_stage = WorldsNO1WGData.Instance:GetCurSubroundStage()
		local subround = WORLDS_RANK_INFO_TYPE_TO_KNOCKOUT_SUBROUND[self.data.rank_info_type]                     -- 子轮
		local round_cfg = WorldsNO1WGData.Instance:GetKnockoutCfgByIndex(subround)                                -- 轮次配置
		local knockout_subround_state = WorldsNO1WGData.Instance:GetSubroundStatus(round_cfg.round, round_cfg.subround) -- 子轮状态
		local openning = knockout_subround_state == WORLDS_NO1_SUBROUND_STATUS.OPENNING                           -- 子轮是否进行中
		local over = knockout_subround_state == WORLDS_NO1_SUBROUND_STATUS.OVER                                   -- 子轮是否已结束
		local room_is_over = (self.data.room_key == 0) and openning and
		subround_stage == WORLDS_NO1_SUBROUND_STAGE.PK                                                            -- 是否没进房间
		local unopened = knockout_subround_state == WORLDS_NO1_SUBROUND_STATUS.UNOPENED                           -- 子轮是否未开启
		local has_rank = self.data.rank > 0
		self.node_list["operate_empty"]:SetActive(self.data.rank <= 0)
		self.node_list["bet_btn"]:SetActive(has_rank and not WorldsNO1WGData.Instance:GetIsGuess(self.data.uuid_str) and
		openning and subround_stage == WORLDS_NO1_SUBROUND_STAGE.PREPARE)                                                                                                            -- 竞猜按钮
		self.node_list["bet_label"]:SetActive(has_rank and WorldsNO1WGData.Instance:GetIsGuess(self.data.uuid_str) and
		openning and subround_stage == WORLDS_NO1_SUBROUND_STAGE.PREPARE)                                                                                                            -- 已竞猜标签
		self.node_list["small_bet_label"]:SetActive(has_rank and WorldsNO1WGData.Instance:GetIsGuess(self.data.uuid_str) and
		openning)                                                                                                                                                                    -- 已竞猜角标
		self.node_list["over_label"]:SetActive(has_rank and (over or room_is_over))                                                                                                  -- 已结束标签
		self.node_list["unopened_label"]:SetActive(has_rank and unopened)
		self.node_list["enter_observation_btn"]:SetActive(has_rank and openning and not room_is_over and
		subround_stage == WORLDS_NO1_SUBROUND_STAGE.PK)                                                                                                                              -- 观战按钮


		if not self.is_bottom_info then
			self.node_list["bg"]:SetActive(self.data.rank % 2 == 0)
		end

		-- 头像
		local data = {}
		data.role_id = self.data.uid
		data.prof = self.data.prof
		data.sex = self.data.sex
		data.server_id = self.data.server_id
		self.head:SetData(data)
	end
end

-- 点击竞猜
function WorldsNO1KnockoutRankItem:OnClickBetBtn()
	if WorldsNO1WGData.Instance:GetRemainBetCount() <= 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WorldsNO1.NoBetTimesTips)
		return
	end

	local subround = WORLDS_RANK_INFO_TYPE_TO_KNOCKOUT_SUBROUND[self.data.rank_info_type] -- 子轮
	local bet_cfg = WorldsNO1WGData.Instance:GetBetCfgBySubround(subround)
	local type = bet_cfg.cost_type
	local need_num = bet_cfg.cost_count
	local has_num = math.huge
	local role_info = RoleWGData.Instance:GetRoleInfo()
	if type == MoneyType.XianYu then
		has_num = role_info.gold
	elseif type == MoneyType.BangYu then
		has_num = role_info.bind_gold
	elseif type == MoneyType.YuanBao then
		has_num = role_info.silver_ticket
	elseif type == MoneyType.ShengWang then
		has_num = role_info.shengwang
	elseif type == MoneyType.JinBi then
		has_num = role_info.coin
	end

	local emoji_index = MoneyTypeEmoji[type] or 0
	local tips_str = string.format(Language.WorldsNO1.BetTips, emoji_index, need_num, emoji_index, need_num * 2)
	TipWGCtrl.Instance:OpenCheckAlertTips(tips_str, function()
		if need_num > has_num then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Shop["MoneyType" .. type] .. Language.Common.NotEnough)
		end
		WorldsNO1WGCtrl.Instance:SendGuess(subround, self.data.uuid_str)
	end)
end

-- 点击观战按钮
function WorldsNO1KnockoutRankItem:OnClickEnterObservationBtn()
	-- 战斗场景非观战状态不能观战他人
	if Scene.Instance:GetSceneType() == SceneType.WorldsNO1 and not WorldsNO1WGData.Instance:IsObservationStatus() then
		TipWGCtrl.Instance:ShowSystemMsg(Language.WorldsNO1.FightingCantObservation)
		return
	end
	WorldsNO1WGCtrl.Instance:SendObservate(self.data.uuid_str)

	-- 请求刷新一下排名信息
	WorldsNO1WGCtrl.Instance:SendRequestRankInfo(self.data.rank_info_type)
end

function WorldsNO1KnockoutRankItem:OnClickHead()
	self.head:OpenMenu()
end

function WorldsNO1KnockoutRankItem:SetIsBottomInfo(is_bottom_info)
	self.is_bottom_info = is_bottom_info
end

function WorldsNO1KnockoutRankItem:ChangeColor(str)
	if self.is_bottom_info then
		return str
	end
	local color = "D8C8BDFF"
	if self.data and self.data.is_main_role then
		color = COLOR3B.D_GREEN
	end
	return ToColorStr(str, color)
end

------------------ 海选排行榜item ----------------------
WorldsNO1AuditionRankItem = WorldsNO1AuditionRankItem or BaseClass(BaseRender)
function WorldsNO1AuditionRankItem:__init()
	-- 头像
	self.head = BaseHeadCell.New(self.node_list["role_head"])
	self.head:SetNeedMenuBtn(true)
	XUI.AddClickEventListener(self.node_list["role_head"], BindTool.Bind(self.OnClickHead, self))
end

function WorldsNO1AuditionRankItem:__delete()
	if self.head then
		self.head:DeleteMe()
		self.head = nil
	end
end

function WorldsNO1AuditionRankItem:OnFlush()
	if self.data then
		-- 角色名
		self.node_list["role_name"].text.text = self:ChangeColor(self.data.role_name)

		-- 区服名
		local temp_name = string.format(Language.WorldServer.ServerDefName, self.data.server_id) --内网无法请求PHP，先设置默认名字		
		self.node_list["server_name"].text.text = self:ChangeColor(temp_name)

		-- kd
		self.node_list["kd"].text.text = self:ChangeColor(self.data.kill_count)

		-- 淘汰赛积分
		self.node_list["score"].text.text = self:ChangeColor(self.data.score_count)

		-- 排名
		self.node_list["rank_txt"]:SetActive(self.data.rank <= 0 or self.data.rank > 3)
		self.node_list["rank_img"]:SetActive(self.data.rank > 0 and self.data.rank <= 3)
		if self.data.rank > 0 then
			if self.data.rank <= 3 then
				local bundle, asset = ResPath.GetF2CommonIcon("icon_paiming_big_" .. self.data.rank)
				self.node_list["rank_img"].image:LoadSpriteAsync(bundle, asset, function()
					self.node_list["rank_img"].image:SetNativeSize()
				end)
			else
				self.node_list["rank_txt"].text.text = self:ChangeColor(self.data.rank)
			end
		else
			self.node_list["rank_txt"].text.text = Language.WorldsNO1.OutOfRankList -- 未上榜
		end

		-- 头像
		local data = {}
		data.role_id = self.data.uid
		data.prof = self.data.prof
		data.sex = self.data.sex
		data.server_id = self.data.server_id
		self.head:SetData(data)

		-- 背景
		if not self.is_bottom_info then
			self.node_list["bg"]:SetActive(self.data.rank % 2 == 0)
		end
	end
end

function WorldsNO1AuditionRankItem:OnClickHead()
	self.head:OpenMenu()
end

function WorldsNO1AuditionRankItem:SetIsBottomInfo(is_bottom_info)
	self.is_bottom_info = is_bottom_info
end

function WorldsNO1AuditionRankItem:ChangeColor(str)
	if self.is_bottom_info then
		return str
	end
	local color = "D8C8BDFF"
	if self.data and self.data.is_main_role then
		color = COLOR3B.D_GREEN
	end
	return ToColorStr(str, color)
end
