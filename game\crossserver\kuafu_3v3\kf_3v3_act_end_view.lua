KF3V3ActEndView = KF3V3ActEndView or BaseClass(SafeBaseView)

function KF3V3ActEndView:__init()
    self:AddViewResource(0, "uis/view/field1v1_ui_prefab", "3v3_act_end_panel")
    self.active_close = false
    self.open_tween = nil
    self.close_tween = nil
end

function KF3V3ActEndView:ReleaseCallBack()
    self.data = nil

    if self.on_load_end then
        GlobalEventSystem:UnBind(self.on_load_end)
        self.on_load_end = nil
    end

    if self.close_anim then
        self.close_anim:Kill()
        self.close_anim = nil
    end
    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end
    if self.start_tween then
        self.start_tween:Kill()
        self.start_tween = nil
    end
   
end

--- data = {  act_type = XXX, }
function KF3V3ActEndView:SetData(data)
    if not data then
        return
    end
    self.data = data
    self:Open()
end

function KF3V3ActEndView:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.btn_go, BindTool.Bind(self.OnClickGo, self))
    XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind(self.CloseAnim, self))
    self.on_load_end = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW, BindTool.Bind(self.OnShowMainView, self))
end

function KF3V3ActEndView:OnFlush()
    if not self.data then
        return
    end

    if self.data.act_type == ACTIVITY_TYPE.KF_PVP then
        self.node_list.mid_text.text.text = Language.ActEndTip.KF_3V3_MID
        self.node_list.bottom_text.text.text = Language.ActEndTip.KF_3V3_BOTTOM
    elseif self.data.act_type == ACTIVITY_TYPE.KF_ONEVONE then
        self.node_list.mid_text.text.text = Language.ActEndTip.KF_1V1_MID
        self.node_list.bottom_text.text.text = Language.ActEndTip.KF_1V1_BOTTOM
    end
end

function KF3V3ActEndView:OnClickGo()
    if not self.data then
        return
    end

    if self.data.act_type == ACTIVITY_TYPE.KF_PVP then
        KF3V3WGCtrl.Instance:Open3V3View(TabIndex.kf_pvp_3v3info)
    elseif self.data.act_type == ACTIVITY_TYPE.KF_ONEVONE then
        Field1v1WGCtrl.Instance:OpenPerson()
    end
    self:Close()
end

function KF3V3ActEndView:OnShowMainView()
    if not self.start_tween then
        self.start_tween = DG.Tweening.DOTween.Sequence()
        self.start_tween:Append(self.node_list["big_bg"].canvas_group:DoAlpha(0,1,0.3))
        self.start_tween:AppendInterval(0.3)

        self.start_tween:OnComplete(function()
            self.start_tween:Kill()
            self.start_tween = nil
        end)
    end
    if self.delay_timer then
        return
    end
    self.delay_timer = GlobalTimerQuest:AddDelayTimer(function()
        self:CloseAnim()
    end, 8)
end

function KF3V3ActEndView:CloseAnim()
    if self.close_anim or not self.node_list["big_bg"] then
        return
    end
    if self.delay_timer then
        GlobalTimerQuest:CancelQuest(self.delay_timer)
        self.delay_timer = nil
    end
    local cfg = MustBuyWGData.Instance:GetOtherCfg()
    local alpha_time = cfg.alpha_time or 0.3
    local sizeDelta_time = cfg.sizeDelta_time or 0.5

    self.close_anim = DG.Tweening.DOTween.Sequence()

    self.close_anim:Append(self.node_list["big_bg"].canvas_group:DoAlpha(1,0,alpha_time))
    self.close_anim:AppendInterval(alpha_time)

    self.close_anim:OnComplete(function()
        self.close_anim:Kill()
        self.close_anim = nil
        self:Close()
    end)
end