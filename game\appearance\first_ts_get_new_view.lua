--------------------------------------------------
--第一次获得天神的界面
--------------------------------------------------
FirstTSGetNewView = FirstTSGetNewView or BaseClass(SafeBaseView)

function FirstTSGetNewView:__init()
    self.view_layer = UiLayer.Pop

    self:AddViewResource(0, "uis/view/appearance_ui_prefab", "layout_ts_get_new_view")
    self:SetMaskBg()
end

function FirstTSGetNewView:__delete()
end

function FirstTSGetNewView:ReleaseCallBack()
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if CountDownManager.Instance:HasCountDown("first_ts_get_new_close") then
        CountDownManager.Instance:RemoveCountDown("first_ts_get_new_close")
    end
end

function FirstTSGetNewView:LoadCallBack()
    self.model_display = RoleModel.New()
    local display_data = {
        parent_node = self.node_list["ph_display"],
        camera_type = MODEL_CAMERA_TYPE.BASE,
        -- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
        rt_scale_type = ModelRTSCaleType.M,
        can_drag = true,
    }

    self.model_display:SetRenderTexUI3DModel(display_data)   
    -- self.model_display:SetUI3DModel(self.node_list["ph_display"].transform,
    --     self.node_list["ph_display"].event_trigger_listener,
    --     1, false, MODEL_CAMERA_TYPE.BASE)
    AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.FirstGetTS, nil, true))
end

function FirstTSGetNewView:ShowIndexCallBack()
    MainuiWGCtrl.Instance:UpdateBianShenBtnState(false)
    if CountDownManager.Instance:HasCountDown("first_ts_get_new_close") then
        CountDownManager.Instance:RemoveCountDown("first_ts_get_new_close")
    end
    CountDownManager.Instance:AddCountDown("first_ts_get_new_close", BindTool.Bind(self.UpdataTime, self),
        BindTool.Bind(self.TimeCompleteCallBack, self), nil, 5, 1)
end

function FirstTSGetNewView:UpdataTime(elapse_time, total_time)
    local time = math.floor(total_time - elapse_time)
    self.node_list["time_text"].text.text = string.format(Language.Common.AutoCloseTimerTxt, time)
end

function FirstTSGetNewView:TimeCompleteCallBack()
    self:ClickClose()
end

function FirstTSGetNewView:OnFlush()
    local default_cfg = TianShenWGData.Instance:GetTianShenCfg(0)
    if default_cfg then
        self.model_display:SetTianShenModel(default_cfg.appe_image_id, 0, true, nil, nil)
        self.node_list.icon.image:LoadSprite(ResPath.GetSkillIconById(default_cfg.bianshen_icon))
        -- local tianshen_upgrade_cfg = TianShenWGData.Instance:GetTianShenUpgrade(default_cfg.index, 1)
        -- local attr_list = AttributeMgr.GetAttributteByClass(tianshen_upgrade_cfg)
        -- local cap = AttributeMgr.GetCapability(attr_list)
    end
end

function FirstTSGetNewView:CloseCallBack()
end

function FirstTSGetNewView:ClickClose()
    if CountDownManager.Instance:HasCountDown("first_ts_get_new_close") then
        CountDownManager.Instance:RemoveCountDown("first_ts_get_new_close")
    end
    self.node_list["view_panel"]:SetActive(false)
    UITween.AlpahShowPanel(self.node_list["view_panel"], false, 0.2, nil, function()
        local ts_skill_btn = MainuiWGCtrl.Instance:GetBtnTSSkillBtn()
        local btn_obj = self.node_list["btn"]
        if ts_skill_btn then
            local start_pos = btn_obj.transform.anchoredPosition
            local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
            local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera,
                ts_skill_btn.rect.position)
            local parent_node_rect = self.node_list["root"].transform
            local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
            parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
            local end_pos = Vector2(local_bullet_start_pos_tbl.x, local_bullet_start_pos_tbl.y)
            UITween.MoveToScaleAndShowPanel(btn_obj, start_pos, end_pos, 1, 1, nil, function()
                MainuiWGCtrl.Instance:UpdateBianShenBtnState(true)
                self:Close()
                GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
            end)
        end
    end)
end
