require("game/lord_every_day_shop/lord_every_day_shop_wg_data")
require("game/lord_every_day_shop/lord_every_day_shop_view")
require("game/lord_every_day_shop/lord_every_day_shop_convert_tips")

LordEveryDayShopWGCtrl = LordEveryDayShopWGCtrl or BaseClass(BaseWGCtrl)
function LordEveryDayShopWGCtrl:__init()
    if LordEveryDayShopWGCtrl.Instance ~= nil then
		print_error("[LordEveryDayShopWGCtrl] attempt to create singleton twice!")
		return
	end
	LordEveryDayShopWGCtrl.Instance = self

    self.view = LordEveryDayShopView.New(GuideModuleName.LordEveryDayShopView)
    self.data = LordEveryDayShopWGData.New()
	self.shop_convert_tips = LordEveryDayShopConvertTips.New()

    self:RegisterAllProtocals()

	self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
    self.item_data_change = BindTool.Bind1(self.OnItemDataChange, self)
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_change)

end

function LordEveryDayShopWGCtrl:__delete()
    LordEveryDayShopWGCtrl.Instance = nil

    if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

    if self.data then
        self.data:DeleteMe()
        self.data = nil
    end

	if self.shop_convert_tips then
		self.shop_convert_tips:DeleteMe()
        self.shop_convert_tips = nil
	end

	GlobalEventSystem:UnBind(self.open_fun_change)
end

function LordEveryDayShopWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSEveryDayLingzhuShopOperate)
	self:RegisterProtocol(SCEveryDayLingzhuShopInfo, "OnSCEveryDayLingzhuShopInfo")
end

-- 请求操作
function LordEveryDayShopWGCtrl:SendCSLordShopOperateRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSEveryDayLingzhuShopOperate)
	protocol.opera_type = opera_type or 0
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function LordEveryDayShopWGCtrl:OnSCEveryDayLingzhuShopInfo(protocol)

	self.data:SetAllInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.LordEveryDayShopView)
    RemindManager.Instance:Fire(RemindName.LoadEveryDayShop)
    MainuiWGCtrl.Instance:FlushView(0, "load_everyday_shop")
end

function LordEveryDayShopWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all then
        is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.LordEveryDayShopView)
    end

    if check_all or fun_name == FunName.LordEveryDayShopView then
        local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.LORD_EVERY_DAY_SHOP, state)
    end
end

function LordEveryDayShopWGCtrl:LordEveryDayShopFlush(result)
    if result == 1 then
        if self.view:IsOpen() then
            self.view:Flush(0, "flush_cell_anim")
        end
    end
end

function LordEveryDayShopWGCtrl:OnItemDataChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local other_cfg = self.data:GetOtherCfg()
    if change_item_id == other_cfg.exchange_item_id then
        ViewManager.Instance:FlushView(GuideModuleName.LordEveryDayShopView, nil, "flush_exchange")
    end
end

function LordEveryDayShopWGCtrl:OpenShopConvertTipsView(data)
	self.shop_convert_tips:SetDataAndOpen(data)
end
