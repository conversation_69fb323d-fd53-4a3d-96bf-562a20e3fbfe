﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class HedgehogTeam_EasyTouch_EasyTouchWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(HedgehogTeam.EasyTouch.EasyTouch), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>ction("IsFingerOverUIElement", IsFingerOverUIElement);
		<PERSON><PERSON>Function("GetCurrentPickedUIElement", GetCurrentPickedUIElement);
		L<PERSON>RegFunction("GetCurrentPickedObject", GetCurrentPickedObject);
		L.RegFunction("GetGameObjectAt", GetGameObjectAt);
		<PERSON><PERSON>Function("GetTouchCount", GetTouchCount);
		<PERSON><PERSON>Function("ResetTouch", ResetTouch);
		<PERSON><PERSON>RegFunction("SetEnabled", SetEnabled);
		<PERSON><PERSON>RegFunction("GetEnabled", GetEnabled);
		<PERSON><PERSON>RegFunction("SetEnableUIDetection", SetEnableUIDetection);
		<PERSON><PERSON>("GetEnableUIDetection", GetEnableUIDetection);
		<PERSON><PERSON>("SetUICompatibily", SetUICompatibily);
		L.RegFunction("GetUIComptability", GetUIComptability);
		L.RegFunction("SetAutoUpdateUI", SetAutoUpdateUI);
		L.RegFunction("GetAutoUpdateUI", GetAutoUpdateUI);
		L.RegFunction("SetNGUICompatibility", SetNGUICompatibility);
		L.RegFunction("GetNGUICompatibility", GetNGUICompatibility);
		L.RegFunction("SetEnableAutoSelect", SetEnableAutoSelect);
		L.RegFunction("GetEnableAutoSelect", GetEnableAutoSelect);
		L.RegFunction("SetAutoUpdatePickedObject", SetAutoUpdatePickedObject);
		L.RegFunction("GetAutoUpdatePickedObject", GetAutoUpdatePickedObject);
		L.RegFunction("Set3DPickableLayer", Set3DPickableLayer);
		L.RegFunction("Get3DPickableLayer", Get3DPickableLayer);
		L.RegFunction("AddCamera", AddCamera);
		L.RegFunction("RemoveCamera", RemoveCamera);
		L.RegFunction("GetCamera", GetCamera);
		L.RegFunction("SetEnable2DCollider", SetEnable2DCollider);
		L.RegFunction("GetEnable2DCollider", GetEnable2DCollider);
		L.RegFunction("Set2DPickableLayer", Set2DPickableLayer);
		L.RegFunction("Get2DPickableLayer", Get2DPickableLayer);
		L.RegFunction("SetGesturePriority", SetGesturePriority);
		L.RegFunction("GetGesturePriority", GetGesturePriority);
		L.RegFunction("SetStationaryTolerance", SetStationaryTolerance);
		L.RegFunction("GetStationaryTolerance", GetStationaryTolerance);
		L.RegFunction("SetLongTapTime", SetLongTapTime);
		L.RegFunction("GetlongTapTime", GetlongTapTime);
		L.RegFunction("SetDoubleTapTime", SetDoubleTapTime);
		L.RegFunction("GetDoubleTapTime", GetDoubleTapTime);
		L.RegFunction("SetDoubleTapMethod", SetDoubleTapMethod);
		L.RegFunction("GetDoubleTapMethod", GetDoubleTapMethod);
		L.RegFunction("SetSwipeTolerance", SetSwipeTolerance);
		L.RegFunction("GetSwipeTolerance", GetSwipeTolerance);
		L.RegFunction("SetEnable2FingersGesture", SetEnable2FingersGesture);
		L.RegFunction("GetEnable2FingersGesture", GetEnable2FingersGesture);
		L.RegFunction("SetTwoFingerPickMethod", SetTwoFingerPickMethod);
		L.RegFunction("GetTwoFingerPickMethod", GetTwoFingerPickMethod);
		L.RegFunction("SetEnablePinch", SetEnablePinch);
		L.RegFunction("GetEnablePinch", GetEnablePinch);
		L.RegFunction("SetMinPinchLength", SetMinPinchLength);
		L.RegFunction("GetMinPinchLength", GetMinPinchLength);
		L.RegFunction("SetEnableTwist", SetEnableTwist);
		L.RegFunction("GetEnableTwist", GetEnableTwist);
		L.RegFunction("SetMinTwistAngle", SetMinTwistAngle);
		L.RegFunction("GetMinTwistAngle", GetMinTwistAngle);
		L.RegFunction("GetSecondeFingerSimulation", GetSecondeFingerSimulation);
		L.RegFunction("SetSecondFingerSimulation", SetSecondFingerSimulation);
		L.RegFunction("__eq", op_Equality);
		L.RegFunction("__tostring", ToLua.op_ToString);
		L.RegVar("enable", get_enable, set_enable);
		L.RegVar("enableRemote", get_enableRemote, set_enableRemote);
		L.RegVar("gesturePriority", get_gesturePriority, set_gesturePriority);
		L.RegVar("StationaryTolerance", get_StationaryTolerance, set_StationaryTolerance);
		L.RegVar("longTapTime", get_longTapTime, set_longTapTime);
		L.RegVar("swipeTolerance", get_swipeTolerance, set_swipeTolerance);
		L.RegVar("minPinchLength", get_minPinchLength, set_minPinchLength);
		L.RegVar("minTwistAngle", get_minTwistAngle, set_minTwistAngle);
		L.RegVar("doubleTapDetection", get_doubleTapDetection, set_doubleTapDetection);
		L.RegVar("doubleTapTime", get_doubleTapTime, set_doubleTapTime);
		L.RegVar("alwaysSendSwipe", get_alwaysSendSwipe, set_alwaysSendSwipe);
		L.RegVar("enable2FingersGesture", get_enable2FingersGesture, set_enable2FingersGesture);
		L.RegVar("enableTwist", get_enableTwist, set_enableTwist);
		L.RegVar("enablePinch", get_enablePinch, set_enablePinch);
		L.RegVar("enable2FingersSwipe", get_enable2FingersSwipe, set_enable2FingersSwipe);
		L.RegVar("twoFingerPickMethod", get_twoFingerPickMethod, set_twoFingerPickMethod);
		L.RegVar("touchCameras", get_touchCameras, set_touchCameras);
		L.RegVar("autoSelect", get_autoSelect, set_autoSelect);
		L.RegVar("pickableLayers3D", get_pickableLayers3D, set_pickableLayers3D);
		L.RegVar("enable2D", get_enable2D, set_enable2D);
		L.RegVar("pickableLayers2D", get_pickableLayers2D, set_pickableLayers2D);
		L.RegVar("autoUpdatePickedObject", get_autoUpdatePickedObject, set_autoUpdatePickedObject);
		L.RegVar("allowUIDetection", get_allowUIDetection, set_allowUIDetection);
		L.RegVar("enableUIMode", get_enableUIMode, set_enableUIMode);
		L.RegVar("autoUpdatePickedUI", get_autoUpdatePickedUI, set_autoUpdatePickedUI);
		L.RegVar("enabledNGuiMode", get_enabledNGuiMode, set_enabledNGuiMode);
		L.RegVar("nGUILayers", get_nGUILayers, set_nGUILayers);
		L.RegVar("nGUICameras", get_nGUICameras, set_nGUICameras);
		L.RegVar("enableSimulation", get_enableSimulation, set_enableSimulation);
		L.RegVar("twistKey", get_twistKey, set_twistKey);
		L.RegVar("swipeKey", get_swipeKey, set_swipeKey);
		L.RegVar("showGuiInspector", get_showGuiInspector, set_showGuiInspector);
		L.RegVar("showSelectInspector", get_showSelectInspector, set_showSelectInspector);
		L.RegVar("showGestureInspector", get_showGestureInspector, set_showGestureInspector);
		L.RegVar("showTwoFingerInspector", get_showTwoFingerInspector, set_showTwoFingerInspector);
		L.RegVar("showSecondFingerInspector", get_showSecondFingerInspector, set_showSecondFingerInspector);
		L.RegVar("secondFingerTexture", get_secondFingerTexture, set_secondFingerTexture);
		L.RegVar("instance", get_instance, null);
		L.RegVar("current", get_current, null);
		L.RegVar("On_Cancel", get_On_Cancel, set_On_Cancel);
		L.RegVar("On_Cancel2Fingers", get_On_Cancel2Fingers, set_On_Cancel2Fingers);
		L.RegVar("On_TouchStart", get_On_TouchStart, set_On_TouchStart);
		L.RegVar("On_TouchDown", get_On_TouchDown, set_On_TouchDown);
		L.RegVar("On_TouchUp", get_On_TouchUp, set_On_TouchUp);
		L.RegVar("On_SimpleTap", get_On_SimpleTap, set_On_SimpleTap);
		L.RegVar("On_DoubleTap", get_On_DoubleTap, set_On_DoubleTap);
		L.RegVar("On_LongTapStart", get_On_LongTapStart, set_On_LongTapStart);
		L.RegVar("On_LongTap", get_On_LongTap, set_On_LongTap);
		L.RegVar("On_LongTapEnd", get_On_LongTapEnd, set_On_LongTapEnd);
		L.RegVar("On_DragStart", get_On_DragStart, set_On_DragStart);
		L.RegVar("On_Drag", get_On_Drag, set_On_Drag);
		L.RegVar("On_DragEnd", get_On_DragEnd, set_On_DragEnd);
		L.RegVar("On_SwipeStart", get_On_SwipeStart, set_On_SwipeStart);
		L.RegVar("On_Swipe", get_On_Swipe, set_On_Swipe);
		L.RegVar("On_SwipeEnd", get_On_SwipeEnd, set_On_SwipeEnd);
		L.RegVar("On_TouchStart2Fingers", get_On_TouchStart2Fingers, set_On_TouchStart2Fingers);
		L.RegVar("On_TouchDown2Fingers", get_On_TouchDown2Fingers, set_On_TouchDown2Fingers);
		L.RegVar("On_TouchUp2Fingers", get_On_TouchUp2Fingers, set_On_TouchUp2Fingers);
		L.RegVar("On_SimpleTap2Fingers", get_On_SimpleTap2Fingers, set_On_SimpleTap2Fingers);
		L.RegVar("On_DoubleTap2Fingers", get_On_DoubleTap2Fingers, set_On_DoubleTap2Fingers);
		L.RegVar("On_LongTapStart2Fingers", get_On_LongTapStart2Fingers, set_On_LongTapStart2Fingers);
		L.RegVar("On_LongTap2Fingers", get_On_LongTap2Fingers, set_On_LongTap2Fingers);
		L.RegVar("On_LongTapEnd2Fingers", get_On_LongTapEnd2Fingers, set_On_LongTapEnd2Fingers);
		L.RegVar("On_Twist", get_On_Twist, set_On_Twist);
		L.RegVar("On_TwistEnd", get_On_TwistEnd, set_On_TwistEnd);
		L.RegVar("On_Pinch", get_On_Pinch, set_On_Pinch);
		L.RegVar("On_PinchIn", get_On_PinchIn, set_On_PinchIn);
		L.RegVar("On_PinchOut", get_On_PinchOut, set_On_PinchOut);
		L.RegVar("On_PinchEnd", get_On_PinchEnd, set_On_PinchEnd);
		L.RegVar("On_DragStart2Fingers", get_On_DragStart2Fingers, set_On_DragStart2Fingers);
		L.RegVar("On_Drag2Fingers", get_On_Drag2Fingers, set_On_Drag2Fingers);
		L.RegVar("On_DragEnd2Fingers", get_On_DragEnd2Fingers, set_On_DragEnd2Fingers);
		L.RegVar("On_SwipeStart2Fingers", get_On_SwipeStart2Fingers, set_On_SwipeStart2Fingers);
		L.RegVar("On_Swipe2Fingers", get_On_Swipe2Fingers, set_On_Swipe2Fingers);
		L.RegVar("On_SwipeEnd2Fingers", get_On_SwipeEnd2Fingers, set_On_SwipeEnd2Fingers);
		L.RegVar("On_EasyTouchIsReady", get_On_EasyTouchIsReady, set_On_EasyTouchIsReady);
		L.RegVar("On_OverUIElement", get_On_OverUIElement, set_On_OverUIElement);
		L.RegVar("On_UIElementTouchUp", get_On_UIElementTouchUp, set_On_UIElementTouchUp);
		L.RegFunction("UIElementTouchUpHandler", HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler);
		L.RegFunction("OverUIElementHandler", HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler);
		L.RegFunction("EasyTouchIsReadyHandler", HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler);
		L.RegFunction("SwipeEnd2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler);
		L.RegFunction("Swipe2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler);
		L.RegFunction("SwipeStart2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler);
		L.RegFunction("DragEnd2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler);
		L.RegFunction("Drag2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler);
		L.RegFunction("DragStart2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler);
		L.RegFunction("PinchEndHandler", HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler);
		L.RegFunction("PinchOutHandler", HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler);
		L.RegFunction("PinchInHandler", HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler);
		L.RegFunction("PinchHandler", HedgehogTeam_EasyTouch_EasyTouch_PinchHandler);
		L.RegFunction("TwistEndHandler", HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler);
		L.RegFunction("TwistHandler", HedgehogTeam_EasyTouch_EasyTouch_TwistHandler);
		L.RegFunction("LongTapEnd2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler);
		L.RegFunction("LongTap2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler);
		L.RegFunction("LongTapStart2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler);
		L.RegFunction("DoubleTap2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler);
		L.RegFunction("SimpleTap2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler);
		L.RegFunction("TouchUp2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler);
		L.RegFunction("TouchDown2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler);
		L.RegFunction("TouchStart2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler);
		L.RegFunction("SwipeEndHandler", HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler);
		L.RegFunction("SwipeHandler", HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler);
		L.RegFunction("SwipeStartHandler", HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler);
		L.RegFunction("DragEndHandler", HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler);
		L.RegFunction("DragHandler", HedgehogTeam_EasyTouch_EasyTouch_DragHandler);
		L.RegFunction("DragStartHandler", HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler);
		L.RegFunction("LongTapEndHandler", HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler);
		L.RegFunction("LongTapHandler", HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler);
		L.RegFunction("LongTapStartHandler", HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler);
		L.RegFunction("DoubleTapHandler", HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler);
		L.RegFunction("SimpleTapHandler", HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler);
		L.RegFunction("TouchUpHandler", HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler);
		L.RegFunction("TouchDownHandler", HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler);
		L.RegFunction("TouchStartHandler", HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler);
		L.RegFunction("Cancel2FingersHandler", HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler);
		L.RegFunction("TouchCancelHandler", HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsFingerOverUIElement(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.IsFingerOverUIElement(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurrentPickedUIElement(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.GameObject o = HedgehogTeam.EasyTouch.EasyTouch.GetCurrentPickedUIElement(arg0, arg1);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCurrentPickedObject(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
			UnityEngine.GameObject o = HedgehogTeam.EasyTouch.EasyTouch.GetCurrentPickedObject(arg0, arg1);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGameObjectAt(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 1);
				UnityEngine.GameObject o = HedgehogTeam.EasyTouch.EasyTouch.GetGameObjectAt(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 2)
			{
				UnityEngine.Vector2 arg0 = ToLua.ToVector2(L, 1);
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				UnityEngine.GameObject o = HedgehogTeam.EasyTouch.EasyTouch.GetGameObjectAt(arg0, arg1);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: HedgehogTeam.EasyTouch.EasyTouch.GetGameObjectAt");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTouchCount(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			int o = HedgehogTeam.EasyTouch.EasyTouch.GetTouchCount();
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ResetTouch(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.ResetTouch(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetEnabled(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEnabled(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetEnabled();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnableUIDetection(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetEnableUIDetection(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEnableUIDetection(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetEnableUIDetection();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetUICompatibily(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetUICompatibily(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetUIComptability(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetUIComptability();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetAutoUpdateUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetAutoUpdateUI(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAutoUpdateUI(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetAutoUpdateUI();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetNGUICompatibility(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetNGUICompatibility(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNGUICompatibility(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetNGUICompatibility();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnableAutoSelect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetEnableAutoSelect(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEnableAutoSelect(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetEnableAutoSelect();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetAutoUpdatePickedObject(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetAutoUpdatePickedObject(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetAutoUpdatePickedObject(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetAutoUpdatePickedObject();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Set3DPickableLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.LayerMask arg0 = ToLua.ToLayerMask(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.Set3DPickableLayer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Get3DPickableLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			UnityEngine.LayerMask o = HedgehogTeam.EasyTouch.EasyTouch.Get3DPickableLayer();
			ToLua.PushLayerMask(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AddCamera(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 1, typeof(UnityEngine.Camera));
				HedgehogTeam.EasyTouch.EasyTouch.AddCamera(arg0);
				return 0;
			}
			else if (count == 2)
			{
				UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 1, typeof(UnityEngine.Camera));
				bool arg1 = LuaDLL.luaL_checkboolean(L, 2);
				HedgehogTeam.EasyTouch.EasyTouch.AddCamera(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: HedgehogTeam.EasyTouch.EasyTouch.AddCamera");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RemoveCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Camera arg0 = (UnityEngine.Camera)ToLua.CheckObject(L, 1, typeof(UnityEngine.Camera));
			HedgehogTeam.EasyTouch.EasyTouch.RemoveCamera(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCamera(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				UnityEngine.Camera o = HedgehogTeam.EasyTouch.EasyTouch.GetCamera();
				ToLua.PushSealed(L, o);
				return 1;
			}
			else if (count == 1)
			{
				int arg0 = (int)LuaDLL.luaL_checknumber(L, 1);
				UnityEngine.Camera o = HedgehogTeam.EasyTouch.EasyTouch.GetCamera(arg0);
				ToLua.PushSealed(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: HedgehogTeam.EasyTouch.EasyTouch.GetCamera");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnable2DCollider(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetEnable2DCollider(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEnable2DCollider(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetEnable2DCollider();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Set2DPickableLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.LayerMask arg0 = ToLua.ToLayerMask(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.Set2DPickableLayer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Get2DPickableLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			UnityEngine.LayerMask o = HedgehogTeam.EasyTouch.EasyTouch.Get2DPickableLayer();
			ToLua.PushLayerMask(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetGesturePriority(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.GesturePriority arg0 = (HedgehogTeam.EasyTouch.EasyTouch.GesturePriority)ToLua.CheckObject(L, 1, typeof(HedgehogTeam.EasyTouch.EasyTouch.GesturePriority));
			HedgehogTeam.EasyTouch.EasyTouch.SetGesturePriority(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGesturePriority(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			HedgehogTeam.EasyTouch.EasyTouch.GesturePriority o = HedgehogTeam.EasyTouch.EasyTouch.GetGesturePriority();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetStationaryTolerance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetStationaryTolerance(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetStationaryTolerance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = HedgehogTeam.EasyTouch.EasyTouch.GetStationaryTolerance();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLongTapTime(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetLongTapTime(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetlongTapTime(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = HedgehogTeam.EasyTouch.EasyTouch.GetlongTapTime();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDoubleTapTime(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetDoubleTapTime(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDoubleTapTime(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = HedgehogTeam.EasyTouch.EasyTouch.GetDoubleTapTime();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetDoubleTapMethod(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTapDetection arg0 = (HedgehogTeam.EasyTouch.EasyTouch.DoubleTapDetection)ToLua.CheckObject(L, 1, typeof(HedgehogTeam.EasyTouch.EasyTouch.DoubleTapDetection));
			HedgehogTeam.EasyTouch.EasyTouch.SetDoubleTapMethod(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetDoubleTapMethod(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTapDetection o = HedgehogTeam.EasyTouch.EasyTouch.GetDoubleTapMethod();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSwipeTolerance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetSwipeTolerance(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSwipeTolerance(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = HedgehogTeam.EasyTouch.EasyTouch.GetSwipeTolerance();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnable2FingersGesture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetEnable2FingersGesture(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEnable2FingersGesture(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetEnable2FingersGesture();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetTwoFingerPickMethod(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.TwoFingerPickMethod arg0 = (HedgehogTeam.EasyTouch.EasyTouch.TwoFingerPickMethod)ToLua.CheckObject(L, 1, typeof(HedgehogTeam.EasyTouch.EasyTouch.TwoFingerPickMethod));
			HedgehogTeam.EasyTouch.EasyTouch.SetTwoFingerPickMethod(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetTwoFingerPickMethod(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			HedgehogTeam.EasyTouch.EasyTouch.TwoFingerPickMethod o = HedgehogTeam.EasyTouch.EasyTouch.GetTwoFingerPickMethod();
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnablePinch(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetEnablePinch(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEnablePinch(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetEnablePinch();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMinPinchLength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetMinPinchLength(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMinPinchLength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = HedgehogTeam.EasyTouch.EasyTouch.GetMinPinchLength();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetEnableTwist(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetEnableTwist(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetEnableTwist(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetEnableTwist();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetMinTwistAngle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetMinTwistAngle(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetMinTwistAngle(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			float o = HedgehogTeam.EasyTouch.EasyTouch.GetMinTwistAngle();
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetSecondeFingerSimulation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			bool o = HedgehogTeam.EasyTouch.EasyTouch.GetSecondeFingerSimulation();
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetSecondFingerSimulation(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			bool arg0 = LuaDLL.luaL_checkboolean(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch.SetSecondFingerSimulation(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enable;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableRemote(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enableRemote;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableRemote on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_gesturePriority(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			HedgehogTeam.EasyTouch.EasyTouch.GesturePriority ret = obj.gesturePriority;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index gesturePriority on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_StationaryTolerance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float ret = obj.StationaryTolerance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index StationaryTolerance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_longTapTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float ret = obj.longTapTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index longTapTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_swipeTolerance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float ret = obj.swipeTolerance;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipeTolerance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_minPinchLength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float ret = obj.minPinchLength;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index minPinchLength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_minTwistAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float ret = obj.minTwistAngle;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index minTwistAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_doubleTapDetection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTapDetection ret = obj.doubleTapDetection;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index doubleTapDetection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_doubleTapTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float ret = obj.doubleTapTime;
			LuaDLL.lua_pushnumber(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index doubleTapTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_alwaysSendSwipe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.alwaysSendSwipe;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index alwaysSendSwipe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enable2FingersGesture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enable2FingersGesture;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enable2FingersGesture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableTwist(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enableTwist;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableTwist on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enablePinch(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enablePinch;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enablePinch on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enable2FingersSwipe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enable2FingersSwipe;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enable2FingersSwipe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_twoFingerPickMethod(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			HedgehogTeam.EasyTouch.EasyTouch.TwoFingerPickMethod ret = obj.twoFingerPickMethod;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index twoFingerPickMethod on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_touchCameras(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			System.Collections.Generic.List<HedgehogTeam.EasyTouch.ECamera> ret = obj.touchCameras;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index touchCameras on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoSelect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.autoSelect;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoSelect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pickableLayers3D(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.LayerMask ret = obj.pickableLayers3D;
			ToLua.PushLayerMask(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickableLayers3D on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enable2D(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enable2D;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enable2D on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_pickableLayers2D(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.LayerMask ret = obj.pickableLayers2D;
			ToLua.PushLayerMask(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickableLayers2D on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoUpdatePickedObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.autoUpdatePickedObject;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoUpdatePickedObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_allowUIDetection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.allowUIDetection;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index allowUIDetection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableUIMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enableUIMode;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableUIMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_autoUpdatePickedUI(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.autoUpdatePickedUI;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoUpdatePickedUI on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enabledNGuiMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enabledNGuiMode;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enabledNGuiMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_nGUILayers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.LayerMask ret = obj.nGUILayers;
			ToLua.PushLayerMask(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index nGUILayers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_nGUICameras(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			System.Collections.Generic.List<UnityEngine.Camera> ret = obj.nGUICameras;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index nGUICameras on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_enableSimulation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.enableSimulation;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableSimulation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_twistKey(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.KeyCode ret = obj.twistKey;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index twistKey on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_swipeKey(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.KeyCode ret = obj.swipeKey;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipeKey on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_showGuiInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.showGuiInspector;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showGuiInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_showSelectInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.showSelectInspector;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showSelectInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_showGestureInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.showGestureInspector;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showGestureInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_showTwoFingerInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.showTwoFingerInspector;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showTwoFingerInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_showSecondFingerInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool ret = obj.showSecondFingerInspector;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showSecondFingerInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_secondFingerTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.Texture ret = obj.secondFingerTexture;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index secondFingerTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_instance(IntPtr L)
	{
		try
		{
			ToLua.Push(L, HedgehogTeam.EasyTouch.EasyTouch.instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_current(IntPtr L)
	{
		try
		{
			ToLua.PushObject(L, HedgehogTeam.EasyTouch.EasyTouch.current);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_Cancel(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_Cancel2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_TouchStart(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_TouchDown(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_TouchUp(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_SimpleTap(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_DoubleTap(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_LongTapStart(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_LongTap(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_LongTapEnd(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_DragStart(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_Drag(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_DragEnd(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_SwipeStart(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_Swipe(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_SwipeEnd(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_TouchStart2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_TouchDown2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_TouchUp2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_SimpleTap2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_DoubleTap2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_LongTapStart2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_LongTap2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_LongTapEnd2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_Twist(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TwistHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_TwistEnd(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_Pinch(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_PinchIn(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_PinchOut(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_PinchEnd(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_DragStart2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_Drag2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_DragEnd2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_SwipeStart2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_Swipe2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_SwipeEnd2Fingers(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_EasyTouchIsReady(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_OverUIElement(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_On_UIElementTouchUp(IntPtr L)
	{
		ToLua.Push(L, new EventObject(typeof(HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler)));
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enable = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableRemote(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableRemote = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableRemote on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_gesturePriority(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			HedgehogTeam.EasyTouch.EasyTouch.GesturePriority arg0 = (HedgehogTeam.EasyTouch.EasyTouch.GesturePriority)ToLua.CheckObject(L, 2, typeof(HedgehogTeam.EasyTouch.EasyTouch.GesturePriority));
			obj.gesturePriority = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index gesturePriority on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_StationaryTolerance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.StationaryTolerance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index StationaryTolerance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_longTapTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.longTapTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index longTapTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_swipeTolerance(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.swipeTolerance = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipeTolerance on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_minPinchLength(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.minPinchLength = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index minPinchLength on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_minTwistAngle(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.minTwistAngle = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index minTwistAngle on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_doubleTapDetection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			HedgehogTeam.EasyTouch.EasyTouch.DoubleTapDetection arg0 = (HedgehogTeam.EasyTouch.EasyTouch.DoubleTapDetection)ToLua.CheckObject(L, 2, typeof(HedgehogTeam.EasyTouch.EasyTouch.DoubleTapDetection));
			obj.doubleTapDetection = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index doubleTapDetection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_doubleTapTime(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			float arg0 = (float)LuaDLL.luaL_checknumber(L, 2);
			obj.doubleTapTime = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index doubleTapTime on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_alwaysSendSwipe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.alwaysSendSwipe = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index alwaysSendSwipe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enable2FingersGesture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enable2FingersGesture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enable2FingersGesture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableTwist(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableTwist = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableTwist on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enablePinch(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enablePinch = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enablePinch on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enable2FingersSwipe(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enable2FingersSwipe = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enable2FingersSwipe on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_twoFingerPickMethod(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			HedgehogTeam.EasyTouch.EasyTouch.TwoFingerPickMethod arg0 = (HedgehogTeam.EasyTouch.EasyTouch.TwoFingerPickMethod)ToLua.CheckObject(L, 2, typeof(HedgehogTeam.EasyTouch.EasyTouch.TwoFingerPickMethod));
			obj.twoFingerPickMethod = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index twoFingerPickMethod on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_touchCameras(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			System.Collections.Generic.List<HedgehogTeam.EasyTouch.ECamera> arg0 = (System.Collections.Generic.List<HedgehogTeam.EasyTouch.ECamera>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<HedgehogTeam.EasyTouch.ECamera>));
			obj.touchCameras = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index touchCameras on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoSelect(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.autoSelect = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoSelect on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pickableLayers3D(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.LayerMask arg0 = ToLua.ToLayerMask(L, 2);
			obj.pickableLayers3D = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickableLayers3D on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enable2D(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enable2D = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enable2D on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_pickableLayers2D(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.LayerMask arg0 = ToLua.ToLayerMask(L, 2);
			obj.pickableLayers2D = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index pickableLayers2D on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoUpdatePickedObject(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.autoUpdatePickedObject = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoUpdatePickedObject on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_allowUIDetection(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.allowUIDetection = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index allowUIDetection on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableUIMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableUIMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableUIMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_autoUpdatePickedUI(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.autoUpdatePickedUI = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index autoUpdatePickedUI on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enabledNGuiMode(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enabledNGuiMode = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enabledNGuiMode on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_nGUILayers(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.LayerMask arg0 = ToLua.ToLayerMask(L, 2);
			obj.nGUILayers = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index nGUILayers on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_nGUICameras(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			System.Collections.Generic.List<UnityEngine.Camera> arg0 = (System.Collections.Generic.List<UnityEngine.Camera>)ToLua.CheckObject(L, 2, typeof(System.Collections.Generic.List<UnityEngine.Camera>));
			obj.nGUICameras = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index nGUICameras on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_enableSimulation(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.enableSimulation = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index enableSimulation on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_twistKey(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.KeyCode arg0 = (UnityEngine.KeyCode)ToLua.CheckObject(L, 2, typeof(UnityEngine.KeyCode));
			obj.twistKey = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index twistKey on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_swipeKey(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.KeyCode arg0 = (UnityEngine.KeyCode)ToLua.CheckObject(L, 2, typeof(UnityEngine.KeyCode));
			obj.swipeKey = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index swipeKey on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_showGuiInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.showGuiInspector = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showGuiInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_showSelectInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.showSelectInspector = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showSelectInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_showGestureInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.showGestureInspector = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showGestureInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_showTwoFingerInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.showTwoFingerInspector = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showTwoFingerInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_showSecondFingerInspector(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.showSecondFingerInspector = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index showSecondFingerInspector on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_secondFingerTexture(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			HedgehogTeam.EasyTouch.EasyTouch obj = (HedgehogTeam.EasyTouch.EasyTouch)o;
			UnityEngine.Texture arg0 = (UnityEngine.Texture)ToLua.CheckObject<UnityEngine.Texture>(L, 2);
			obj.secondFingerTexture = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index secondFingerTexture on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_Cancel(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_Cancel' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Cancel += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Cancel -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_Cancel2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_Cancel2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Cancel2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Cancel2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_TouchStart(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_TouchStart' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchStart += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchStart -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_TouchDown(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_TouchDown' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchDown += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchDown -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_TouchUp(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_TouchUp' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchUp += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchUp -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_SimpleTap(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_SimpleTap' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SimpleTap += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SimpleTap -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_DoubleTap(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_DoubleTap' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DoubleTap += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DoubleTap -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_LongTapStart(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_LongTapStart' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTapStart += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTapStart -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_LongTap(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_LongTap' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTap += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTap -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_LongTapEnd(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_LongTapEnd' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTapEnd += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTapEnd -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_DragStart(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_DragStart' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DragStart += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DragStart -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_Drag(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_Drag' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Drag += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Drag -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_DragEnd(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_DragEnd' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DragEnd += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DragEnd -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_SwipeStart(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_SwipeStart' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SwipeStart += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SwipeStart -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_Swipe(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_Swipe' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Swipe += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Swipe -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_SwipeEnd(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_SwipeEnd' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SwipeEnd += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SwipeEnd -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_TouchStart2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_TouchStart2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchStart2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchStart2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_TouchDown2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_TouchDown2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchDown2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchDown2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_TouchUp2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_TouchUp2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchUp2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TouchUp2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_SimpleTap2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_SimpleTap2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SimpleTap2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SimpleTap2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_DoubleTap2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_DoubleTap2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DoubleTap2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DoubleTap2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_LongTapStart2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_LongTapStart2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTapStart2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTapStart2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_LongTap2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_LongTap2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTap2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTap2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_LongTapEnd2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_LongTapEnd2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTapEnd2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_LongTapEnd2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_Twist(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_Twist' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TwistHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TwistHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Twist += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TwistHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TwistHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Twist -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_TwistEnd(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_TwistEnd' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TwistEnd += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_TwistEnd -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_Pinch(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_Pinch' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.PinchHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.PinchHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Pinch += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.PinchHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.PinchHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Pinch -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_PinchIn(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_PinchIn' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_PinchIn += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_PinchIn -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_PinchOut(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_PinchOut' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_PinchOut += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_PinchOut -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_PinchEnd(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_PinchEnd' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_PinchEnd += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_PinchEnd -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_DragStart2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_DragStart2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DragStart2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DragStart2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_Drag2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_Drag2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Drag2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Drag2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_DragEnd2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_DragEnd2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DragEnd2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_DragEnd2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_SwipeStart2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_SwipeStart2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SwipeStart2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SwipeStart2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_Swipe2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_Swipe2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Swipe2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_Swipe2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_SwipeEnd2Fingers(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_SwipeEnd2Fingers' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SwipeEnd2Fingers += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_SwipeEnd2Fingers -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_EasyTouchIsReady(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_EasyTouchIsReady' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_EasyTouchIsReady += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_EasyTouchIsReady -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_OverUIElement(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_OverUIElement' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_OverUIElement += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_OverUIElement -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_On_UIElementTouchUp(IntPtr L)
	{
		try
		{
			EventObject arg0 = null;

			if (LuaDLL.lua_isuserdata(L, 2) != 0)
			{
				arg0 = (EventObject)ToLua.ToObject(L, 2);
			}
			else
			{
				return LuaDLL.luaL_throw(L, "The event 'HedgehogTeam.EasyTouch.EasyTouch.On_UIElementTouchUp' can only appear on the left hand side of += or -= when used outside of the type 'HedgehogTeam.EasyTouch.EasyTouch'");
			}

			if (arg0.op == EventOp.Add)
			{
				HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_UIElementTouchUp += ev;
			}
			else if (arg0.op == EventOp.Sub)
			{
				HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler ev = (HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler)arg0.func;
				HedgehogTeam.EasyTouch.EasyTouch.On_UIElementTouchUp -= ev;
			}

			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_UIElementTouchUpHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.UIElementTouchUpHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_OverUIElementHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.OverUIElementHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_EasyTouchIsReadyHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.EasyTouchIsReadyHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_SwipeEnd2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEnd2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_Swipe2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Swipe2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_SwipeStart2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStart2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_DragEnd2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEnd2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_Drag2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Drag2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_DragStart2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStart2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_PinchEndHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchEndHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_PinchOutHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchOutHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_PinchInHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchInHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_PinchHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.PinchHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TwistEndHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistEndHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TwistHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TwistHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_LongTapEnd2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEnd2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_LongTap2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTap2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_LongTapStart2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStart2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_DoubleTap2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTap2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_SimpleTap2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTap2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TouchUp2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUp2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TouchDown2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDown2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TouchStart2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStart2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_SwipeEndHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeEndHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_SwipeHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_SwipeStartHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SwipeStartHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_DragEndHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragEndHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_DragHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_DragStartHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DragStartHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_LongTapEndHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapEndHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_LongTapHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_LongTapStartHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.LongTapStartHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_DoubleTapHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.DoubleTapHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_SimpleTapHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.SimpleTapHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TouchUpHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchUpHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TouchDownHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchDownHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TouchStartHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchStartHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_Cancel2FingersHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.Cancel2FingersHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int HedgehogTeam_EasyTouch_EasyTouch_TouchCancelHandler(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<HedgehogTeam.EasyTouch.EasyTouch.TouchCancelHandler>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

