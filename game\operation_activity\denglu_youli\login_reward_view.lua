local LoginColorList1 = {"#3f6092", "#973b27"}
local LoginColorList2 = {"#a3c5dd", "#ddc1a3"}
local LoginColorList3 = {"#4A3908", "#FFF8BB"}

function OperationActivityView:LoadIndexCallBackLoginRewarView()
	self.is_login_day_show = false 

	--[[
		9303协议活动数据下来之后，要是开服天数还没下来，活动数据会延迟刷新
		此时8244协议下来，取不到活动数据，计算活动开启时间为0天，获取不到配置数据
		此处增加容错，开界面的时候再请求一次数据
	]]
	LoginRewardWGCtrl.Instance:LOGINREWARDInfoReq()

	self.rember_up_index = 0
	self:ShowLoginDayList()
	self.role_data_change_callback_lr = BindTool.Bind1(self.RoleDataChangeCallback, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback_lr, {"vip_level"})

	--刷新界面ui
	self:ShowLoginRewardViewCfg()
end

--刷新界面状态
function OperationActivityView:OperationFlushLoginView()
    if not self:IsLoadedIndex(TabIndex.operation_act_login_reward) then
        return
    end
	--问号样式和文本框
	local view_cfg = LoginRewardWGData.Instance:GetViewCfg()
	if view_cfg then
		self:SetOutsideRuleTips(view_cfg.wh_word)
		self:SetRuleInfo(view_cfg.rule_desc, view_cfg.btn_rule_title)
		self.node_list.login_title_desc.text.text = view_cfg.rule_desc
    end
    self:FluashLoginDayData()

	local year, month, day = TimeUtil.FormatSecond5MYHM1(TimeWGCtrl.Instance:GetServerTime())
	self.node_list.year_text.text.text = year
	local month_desc1 = month < 10 and "0" .. month or month
	self.node_list.month_text1.text.text = month_desc1
	self.node_list.month_text2.text.text = Language.OpertionAcitvity.MonthDesc2[month]
	local day_str = NumberToChinaNumber(day)
	if string.len(day_str) == 9 then
		self.node_list.day_text.text.fontSize = 90
	else
		self.node_list.day_text.text.fontSize = 104
	end
	self.node_list.day_text.text.text = day_str

	local language_cfg = LoginRewardWGData.Instance:GetLanguageCfg()
	if not IsEmptyTable(language_cfg) then
		local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
		local index = PlayerPrefsUtil.GetInt("OperationActivityView" .. main_role_id .. year .. month .. day)
		if index <= 0 then
			local default_text_index = math.random(1, #language_cfg)
			local old_index = PlayerPrefsUtil.GetInt("OperationActivityView" .. main_role_id .. year .. month .. day - 1)
			if old_index > 0 then
				while default_text_index == old_index do
					default_text_index = math.random(1, #language_cfg)
				end
			end
			self.node_list["calendar_desc"].text.text = language_cfg[default_text_index].conect
			PlayerPrefsUtil.SetInt("OperationActivityView" .. main_role_id .. year .. month .. day, default_text_index)
		else
			self.node_list["calendar_desc"].text.text = language_cfg[index].conect
		end
	end

	self:FlushLoginDBReward()
end

function OperationActivityView:FlushLoginDBReward()
	local cfg = OperationActDuoBeiWGData.Instance:GetDuoBeiInfo()
	if cfg ~= nil then
		if not self.lo_db_reward_list then
			self.lo_db_reward_list = AsyncListView.New(OperationActDuoBeiItemRender, self.node_list.time_act_db_list)
		end
		self.lo_db_reward_list:SetDataList(cfg)
	end
end

function OperationActivityView:ReleaseLoginRewardView()
	self.login_act_date = nil
	self.is_login_day_show = nil
	if self.login_day_list then
		self.login_day_list:DeleteMe()
        self.login_day_list = nil
	end

	if self.lo_db_reward_list then
		self.lo_db_reward_list:DeleteMe()
		self.lo_db_reward_list = nil
	end

	if self.role_data_change_callback_lr then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback_lr)
		self.role_data_change_callback_lr = nil
	end
end

function OperationActivityView:RoleDataChangeCallback(attr_name, value, old_value)
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if attr_name == "vip_level" then
		self:FluashLoginDayData()
		RemindManager.Instance:Fire(RemindName.OperationLoginRward)
	end
end

function OperationActivityView:ShowLoginRewardViewCfg()
	-- local view_cfg = LoginRewardWGData.Instance:GetViewCfg()
	-- if nil == view_cfg or IsEmptyTable(view_cfg) then
	-- 	return
	-- end

	local raw_top_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_di_9")
	local bundle, asset = ResPath.GetRawImagesPNG(raw_top_bg_name)
	self.node_list["raw_top_bg"].raw_image:LoadSprite(bundle, asset, function()
        self.node_list["raw_top_bg"].raw_image:SetNativeSize()
    end)

	local title_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_di_10")
	local bundle2, asset2 = ResPath.GetOperationActivityImagePath(title_bg_name)
	self.node_list["title_box_bg"].image:LoadSprite(bundle2, asset2, function()
        self.node_list["title_box_bg"].image:SetNativeSize()
    end)

	local index = OperationActivityWGData.Instance:GetCurTypeId()
	self.node_list.day_text.text.color = StrToColor(LoginColorList1[index])
	self.node_list.month_text1.text.color = StrToColor(LoginColorList1[index])
	self.node_list.year_text.text.color = StrToColor(LoginColorList2[index])
	self.node_list.month_text2.text.color = StrToColor(LoginColorList2[index])
	self.node_list.login_left_desc1.text.color = StrToColor(LoginColorList1[index])
	self.node_list.login_left_desc2.text.color = StrToColor(LoginColorList1[index])
	self.node_list.login_right_desc1.text.color = StrToColor(LoginColorList1[index])
	self.node_list.login_right_desc2.text.color = StrToColor(LoginColorList1[index])
	self.node_list.calendar_desc.text.color = StrToColor(LoginColorList1[index])
	self.node_list.title_box_bg_text.text.color = StrToColor(LoginColorList3[index])
end


--显示天数
function OperationActivityView:ShowLoginDayList()
    self.login_day_list = AsyncListView.New(LoginRewardItem, self.node_list["day_group"])
	local cfg_days = LoginRewardWGData.Instance:GetOpenDays()
	local cfg_day = #cfg_days
	local houtai_day = OperationActivityWGData.Instance:GetActCanOpenDay(ACTIVITY_TYPE.OPERA_ACT_LOGIN_REWARD)
	local day = math.min(houtai_day, cfg_day)
	local login_day_data = LoginRewardWGData.Instance:GetLoginDayData()

    if login_day_data then
        self.login_day_list:SetDataList(login_day_data.day_list)
        self.login_day_list:SetDefaultSelectIndex(LoginRewardWGData.Instance:GetLoginDyaIndex())
	end
end

--刷新登录天数据
function OperationActivityView:FluashLoginDayData()
	local login_day_data = LoginRewardWGData.Instance:GetLoginDayData()
	if login_day_data and login_day_data.day_list then
		if self.login_day_list ~= nil then
            self.login_day_list:SetDataList(login_day_data.day_list)
        end
	end
end

OperationActDuoBeiItemRender = OperationActDuoBeiItemRender or BaseClass(BaseRender)
function OperationActDuoBeiItemRender:__init()
	self.db_render_info_cfg = {}
end
--设置界面的所有显示信息包括底图(只需要赋值一次的)
function OperationActDuoBeiItemRender:LoadDuoBeiRenderView()
	-- local other_cfg = OperationActDuoBeiWGData.Instance:GetActivityOtherCfg()
	-- if nil == other_cfg or IsEmptyTable(other_cfg) then
	-- 	return
	-- end
	-- local asset_name1,bundle_name1 = ResPath.GetF2RawImagesPNG(other_cfg.duobei_item)
	-- 		self.node_list["duobei_item"].raw_image:LoadSprite(asset_name1,bundle_name1,function ()
	-- 		self.node_list["duobei_item"].raw_image:SetNativeSize()
	-- 	end)
	local item_bg_name = OperationActivityWGData.Instance:GetCurTypeImgName("a3_xskh_di_11")
	local asset_name1, bundle_name1 = ResPath.GetOperationActivityImagePath(item_bg_name)
	self.node_list["item_bg"].image:LoadSprite(asset_name1, bundle_name1, function ()
		self.node_list["item_bg"].image:SetNativeSize()
	end)
	-- local asset_name1,bundle_name1 = ResPath.GetF2RawImagesPNG(other_cfg.icon_bg)
	-- 		self.node_list["icon_bg"].raw_image:LoadSprite(asset_name1,bundle_name1,function ()
	-- 		self.node_list["icon_bg"].raw_image:SetNativeSize()
	-- 	end)

end
function OperationActDuoBeiItemRender:LoadCallBack()
	--self.db_reward_item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
	self.node_list.btn.button:AddClickListener(BindTool.Bind(self.OnBtnClickDuoBei, self))
end

function OperationActDuoBeiItemRender:__delete()
	-- if self.db_reward_item_list then
	-- 	self.db_reward_item_list:DeleteMe()
	-- 	self.db_reward_item_list = nil
	-- end
end

function OperationActDuoBeiItemRender:SetItemData(data)
	self.data = data
	self:OnFlush()
end

function OperationActDuoBeiItemRender:OnFlush()
	local data = self:GetData()
	if not data then
		return
	end

	self:LoadDuoBeiRenderView()
	local bundle, asset = ResPath.GetCommonImages(data.cfg.icon)
	self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function ()
		self.node_list.icon.image:SetNativeSize()
	end)

	local beishu = CommonDataManager.GetDaXie(data.cfg.reward_mult)
	beishu = beishu == Language.TianShenRoad.twoconversion[1] and Language.TianShenRoad.twoconversion[2] or beishu
	self.node_list.beishu.text.text = string.format(Language.TianShenRoad.beishudown,beishu)
	
	self.node_list.name.text.text = data.cfg.wanfa_name

	-- local list = {}
	-- if IsEmptyTable(data.cfg.reward_item) then
	-- 	list = TianshenRoadWGData.Instance:GetDuoBeiRewardListByTaskType(data.cfg.task_type)
	-- else
	-- 	list = SortTableKey(data.cfg.reward_item)
	-- end
	
	--self.db_reward_item_list:SetDataList(list)
end

function OperationActDuoBeiItemRender:OnBtnClickDuoBei()
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")  
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end