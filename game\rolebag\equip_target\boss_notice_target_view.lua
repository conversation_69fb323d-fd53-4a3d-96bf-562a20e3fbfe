BossNoticeTargetTipsView = BossNoticeTargetTipsView or BaseClass(SafeBaseView)

function BossNoticeTargetTipsView:__init()
    --self.view_name = "EquipTargetTipiew"
    self:AddViewResource(0, "uis/view/rolebag_ui/equip_target_prefab", "layout_boss_notice_tip")
    self.index = 1
    self.view_cache_time = 0
    self.cur_need_prof_level = 0
    self.active_close = false
end

function BossNoticeTargetTipsView:__delete()

end

function BossNoticeTargetTipsView:ReleaseCallBack()
    if nil ~= self.equip_obj_list then
        for k,v in pairs(self.equip_obj_list) do
            v:DeleteMe()
        end
        self.equip_obj_list = nil
    end

    if nil ~= self.equip_fly_icon then
        for k,v in pairs(self.equip_fly_icon) do
            v:DeleteMe()
        end
        self.equip_fly_icon = nil
    end

    if self.equip_data_change then
        EquipWGData.Instance:UnNotifyDataChangeCallBack(self.equip_data_change)
        self.equip_data_change = nil
    end

    if self.role_data_change then
        RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
        self.role_data_change = nil
    end

end

function BossNoticeTargetTipsView:CloseCallBack()
    
end

function BossNoticeTargetTipsView:LoadCallBack()
    self.equip_data_change = BindTool.Bind(self.OnEquipDataChange, self)
    self.cur_suit_index,self.cur_need_prof_level = BossNoticeTargetTipsWGData.Instance:GetCurGradeStatus2()
    EquipWGData.Instance:NotifyDataChangeCallBack(self.equip_data_change)

    if not self.level_change then
        self.role_data_change = BindTool.Bind1(self.OnEquipDataChange, self)
        RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})
    end
    if self.cur_suit_index ~= -1 then
        self.node_list["skill_six"].raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("mb_notice"..self.cur_suit_index))
        self.node_list.skill_six.raw_image:SetNativeSize()
    end
    self:InitEquipList()
end

function BossNoticeTargetTipsView:OnEquipDataChange()
    self:Flush()
end

function BossNoticeTargetTipsView:OnFlush()

    -- local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
    -- if prof_level < self.cur_need_prof_level then
    --     self.node_list["equip_content"]:SetActive(false)
    --     self.node_list["prof_limite"]:SetActive(true)
    -- else
    --     self.node_list["equip_content"]:SetActive(true)
    --     self.node_list["prof_limite"]:SetActive(false)
    -- end
    for k,v in pairs(self.equip_obj_list) do
        v:Flush()
    end
    local have_num,need_num = BossNoticeTargetTipsWGData.Instance:GetCurHaveNumAndNeedNum()
    local color = COLOR3B.D_RED
    if have_num < need_num then

    else
        color = COLOR3B.D_GREEN
    end
    self.node_list["progress_text"].text.text = ToColorStr(have_num,color) .."/"..need_num
end

function BossNoticeTargetTipsView:InitEquipList()
    self.equip_obj_list = {}
    self.equip_fly_icon = {}
    local equip_list = BossNoticeTargetTipsWGData.Instance:GetCurEquipNeedList()
    for i=1,6 do
        self.equip_obj_list[i] = BossNoticeListRender.New(self.node_list['equip_' .. i])
        self.equip_obj_list[i]:SetIndex(i)
        self.equip_obj_list[i]:SetData(equip_list[i] or {})
        self.equip_fly_icon[i] = BossNoticeTargetFlyIcon.New(self.node_list["fly_item"..i])
        self.equip_fly_icon[i]:SetIndex(i)
        self.equip_fly_icon[i]:SetParntNode(self.node_list.root_obj.rect)
        self.node_list["fly_item"..i]:SetActive(false)
    end
end

function BossNoticeTargetTipsView:UpdataTime(elapse_time, total_time)
end

function BossNoticeTargetTipsView:PlayBossTargetAnim(cell_index)
    if self.equip_fly_icon and self.equip_fly_icon[cell_index] then
        self.equip_fly_icon[cell_index]:ReadToPlayFly()
        self.equip_obj_list[cell_index]:SetStopFlush(true)
    end
end

function BossNoticeTargetTipsView:CompleteBossTargetAnim(cell_index)
    if self.equip_obj_list and self.equip_obj_list[cell_index] then
        self.equip_obj_list[cell_index]:SetStopFlush(false)
        self.equip_obj_list[cell_index]:Flush()
    end
    if not EquipTargetWGCtrl.Instance:CheckNeedBossNoticeTip() then
        EquipTargetWGCtrl.Instance:CompleteBossNoticeTask(self.cur_suit_index)
        GlobalTimerQuest:AddDelayTimer(function ()
            EquipTargetWGCtrl.Instance:CloseBossNoticeTip()
        end, 1)
    end
end

function BossNoticeTargetTipsView:GetBossTargetCellNode(index)
    if index and not IsEmptyTable(self.equip_obj_list) and self.equip_obj_list[index] then
        local cell = self.equip_obj_list[index]
        local node_obj = cell and cell.root_node
        return node_obj
    end
end



------------------------------------装备列表------------------------
BossNoticeListRender = BossNoticeListRender or BaseClass(BaseRender)

function BossNoticeListRender:__init()
    self.root_node = self.node_list["ph_icon"]
    self.old_statue = -1
end

function BossNoticeListRender:__delete()
   if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
    self.old_statue = nil
    self.root_node = nil
end

function BossNoticeListRender:LoadCallBack()
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list.ph_icon)
        self.item_cell:SetRightTopImageTextActive(false)
    end
end

function BossNoticeListRender:SetStopFlush(status)
    self.stop_flush = status
end

function BossNoticeListRender:OnFlush()
    local data = self:GetData()
    if not data then
        return
    end
    local state, star_level = BossNoticeTargetTipsWGData.Instance:GetBossNoticeEquipIndexIsEnough(self.index)
    if self.old_statue == 0 and state then
        EquipTargetWGCtrl.Instance:PlayBossTargetAnim(self.index)
        self.stop_flush = true
    end
    self.old_statue = state and 1 or 0
    if self.stop_flush then
        return
    end
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg then
        self.node_list["left_bottom_text"].text.text = string.format(Language.Role.XXJie, item_cfg.order)
    end
    self.item_cell:SetData(self.data)
    self.item_cell:SetRightTopImageTextActive(false)
    self.item_cell:SetGraphicGreyCualityBg(not state)
    self.item_cell:SetDefaultEff(state)

end


---------------------------------------------------------------------------
BossNoticeTargetFlyIcon = BossNoticeTargetFlyIcon or BaseClass(BaseRender)
function BossNoticeTargetFlyIcon:__init()
    self.playing_ani = false
end

function BossNoticeTargetFlyIcon:__delete()
    self.playing_ani = nil
    self.item_id = nil
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end
    self.parent_node_rect = nil
end

function BossNoticeTargetFlyIcon:ResetCanvasGroup()
    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end

    if self.canvas_group then
        self.canvas_group.alpha = 1
        self.canvas_group.interactable = true
        self.canvas_group.blocksRaycasts = true
    end

    if self.node_list and self.node_list.view then
        self.node_list.view:SetActive(false)
    end
    self.playing_ani = false
end

function BossNoticeTargetFlyIcon:LoadCallBack()
    self.canvas_group = self.node_list.view.transform:GetOrAddComponent(typeof(UnityEngine.CanvasGroup))
    self.item_cell = ItemCell.New(self.node_list.view)
    self.item_cell:SetRightTopImageTextActive(false)
end

function BossNoticeTargetFlyIcon:ReadToPlayFly()
    if self.playing_ani then
        return
    end
    local item_data,equip_data = BossNoticeTargetTipsWGData.Instance:GetBossNoticeEquipData(self.index)
    self:SetData(item_data)
end

function BossNoticeTargetFlyIcon:SetData(item_data)
    self.item_data = item_data
    if not item_data or not self.node_list or not self.node_list.view then
        return
    end

    self.pos = {555.7,61.4}
    local btn_node = EquipTargetWGCtrl.Instance:GetBossTargetCellNode(self.index)
    --EquipTargetWGCtrl.Instance:GetEquipBtnNode()
    if btn_node then
    -- --获取指引按钮的屏幕坐标
        local uicamera = GameObject.Find("GameRoot/UICamera"):GetComponent(typeof(UnityEngine.Camera))
        local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(uicamera, btn_node.rect.position)
        local _, local_bullet_start_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.parent_node_rect, screen_pos_tbl, uicamera, Vector2(0, 0))
        self.pos = {local_bullet_start_pos_tbl.x, local_bullet_start_pos_tbl.y}
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(item_data.item_id)
    if item_cfg and item_cfg.icon_id and item_cfg.icon_id > 0 then
        self.node_list.view:SetActive(true)
        self.item_cell:SetActive(true)
        self.item_cell:SetFlushCallBack(function ()
            self.item_cell:SetRightBottomText('')
            self.item_cell:SetRightBottomTextVisible(false)
        end)
        self.item_cell:SetData(item_data)
        self.node_list.view.rect.anchoredPosition = Vector2(150, -200)
        self.node_list.view.rect.localScale = Vector3(0.2, 0.2, 0.2)
        self.playing_ani = true
        self:PlayAni()
    else
        self:ResetCanvasGroup()
    end
end

function BossNoticeTargetFlyIcon:PlayAni()
    self.canvas_group.alpha = 1
    self.canvas_group.interactable = false
    self.canvas_group.blocksRaycasts = false

    local move_tween_1 = self.node_list.view.rect:DOAnchorPos(Vector2(150, -50), 0.5)
    local move_tween_2 = self.node_list.view.rect:DOAnchorPos(Vector2(self.pos[1], self.pos[2]), 1)

    local scale_tween_1 = self.node_list.view.rect:DOScale(Vector3(1, 1, 1), 0.5)
    local scale_tween_2 = self.node_list.view.rect:DOScale(Vector3(0, 0, 0), 0.5)

    if self.sequence then
        self.sequence:Kill()
        self.sequence = nil
    end
    
    self.sequence = DG.Tweening.DOTween.Sequence()

    self.sequence:Append(move_tween_1)
    self.sequence:Join(scale_tween_1)

    self.sequence:AppendInterval(0.2)
    self.sequence:AppendCallback(function ()
        self.canvas_group:DoAlpha(1, 0.5, 0.5)
    end)

    self.sequence:Append(move_tween_2)
    self.sequence:Append(scale_tween_2)

    self.sequence:OnComplete(function ()
        self.canvas_group.interactable = true
        self.canvas_group.blocksRaycasts = true
        self.node_list.view:SetActive(false)
        self.playing_ani = false
        --if self.item_data and self.item_data.anim_complete_fun then
           -- self.item_data.anim_complete_fun()
        --end
        EquipTargetWGCtrl.Instance:CompleteBossTargetAnim(self.index)
    end)
end

function BossNoticeTargetFlyIcon:SetParntNode(node)
    self.parent_node_rect = node
end