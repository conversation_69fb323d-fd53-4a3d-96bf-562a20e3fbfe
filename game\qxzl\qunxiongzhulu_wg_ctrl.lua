require("game/qxzl/qunxiongzhulu_wg_data")
require("game/qxzl/layout_qxzl_view")

QunXiongZhuLuWGCtrl = QunXiongZhuLuWGCtrl or BaseClass(BaseWGCtrl)

GUILD_OGA_HERD_GRADE_MAX_NUM = 20

function QunXiongZhuLuWGCtrl:__init()
    if QunXiongZhuLuWGCtrl.Instance then
        error("[QunXiongZhuLuWGCtrl]:Attempt to create singleton twice!")
        return
    end
    QunXiongZhuLuWGCtrl.Instance = self

    self.data = QunXiongZhuLuWGData.New()
    self.view = QunXiongZhuLuView.New(GuideModuleName.QunXiongZhuLu)
    self:RegisterAllProtocals()
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
    self:BindGlobalEvent(OtherEventType.PASS_DAY2, BindTool.Bind1(self.DayChangeOrOpen, self))
end

function QunXiongZhuLuWGCtrl:__delete()
    self.view:DeleteMe()
    self.view = nil
    self.data:DeleteMe()
    self.data = nil
    QunXiongZhuLuWGCtrl.Instance = nil
end

function QunXiongZhuLuWGCtrl:MainuiOpenCreateCallBack()
    self:CheckActIsOpen()
end

function QunXiongZhuLuWGCtrl:DayChangeOrOpen()
    self:CheckActIsOpen()
end

function QunXiongZhuLuWGCtrl:RegisterAllProtocals()
    self:RegisterProtocol(CSOpenGameHerdOpera)
    self:RegisterProtocol(SCOpenGameHerdInfo, "ReceiveSCActivityInfo")
    self:RegisterProtocol(SCOGMHerdGuildBattleInfo, "ReceiveSCXMInfo")
    self:RegisterProtocol(SCOpenGameHerdKillTimesUpdate, "ReceiveSCKillTimesUpdateInfo")
end

function QunXiongZhuLuWGCtrl:SendReq(index, param1, param2)
    local protocol = ProtocolPool.Instance:GetProtocol(CSOpenGameHerdOpera)
    protocol.operate = index
    protocol.param1 = param1 or 0
    protocol.param2 = param2 or 0
    protocol:EncodeAndSend()
    RankWGCtrl.Instance:SendRankListReq(RankClientType.XIANMENG_ZHANLI, BindTool.Bind1(self.GetGuildRankList, self))
end

function QunXiongZhuLuWGCtrl:ReceiveSCXMInfo(protocol)
    self.data:SetQXZLGuildBattleMatchInfo(protocol)
    --print_error("protocol", protocol.xmz_match_info_list)
    --print_error(protocol.win_role)
    if ViewManager.Instance:IsOpen(GuideModuleName.QunXiongZhuLu) then
        ViewManager.Instance:FlushView(GuideModuleName.QunXiongZhuLu)
    end
end

function QunXiongZhuLuWGCtrl:CheckActIsOpen()
    local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.QUN_XIONG_ZHU_LU)
    local opengame_cfg = QunXiongZhuLuWGData.Instance:GetQXZLConfig()
    local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
    local now_time = TimeWGCtrl.Instance:GetServerTime()
    local close_day = opengame_cfg.show_close_day
    if MainuiWGData.Instance:CheckMainUIActIsLimit(act_cfg) or open_day > close_day then
        MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.QUN_XIONG_ZHU_LU, false)
    else
        local status 
        local close_rest_day = close_day - open_day + 1
        local clear_time = TimeUtil.GetEarlyTime(now_time)
        local one_day_s = 24 * 3600
        local zlend_time = (one_day_s * close_rest_day + clear_time)
        local zbend_time = 0
        local end_time = 0
        if zlend_time > 0 and not IS_AUDIT_VERSION then
            MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.QUN_XIONG_ZHU_LU, zlend_time, act_cfg)
            --end_time = zlend_time
            --status = ACTIVITY_STATUS.STANDY
        else
            MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.QUN_XIONG_ZHU_LU, false)
            --status = ACTIVITY_STATUS.CLOSE
        end

        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.QUN_XIONG_ZHU_LU, status, end_time, nil, nil)
    end
end

function QunXiongZhuLuWGCtrl:ReceiveSCKillTimesUpdateInfo(protocol)
    --print_error("ReceiveSCKillTimesUpdateInfo", protocol)
    self.data:SetQXZLKillTimesUpdate(protocol)
    if ViewManager.Instance:IsOpen(GuideModuleName.QunXiongZhuLu) then
        ViewManager.Instance:FlushView(GuideModuleName.QunXiongZhuLu)
    end
    RemindManager.Instance:Fire(RemindName.QunXiongZhuLu)
end

function QunXiongZhuLuWGCtrl:ReceiveSCActivityInfo(protocol)
    --print_error("ReceiveSCActivityInfo", protocol)
    self.data:SetQXZLInfo(protocol)
    if ViewManager.Instance:IsOpen(GuideModuleName.QunXiongZhuLu) then
        ViewManager.Instance:FlushView(GuideModuleName.QunXiongZhuLu)
    end
    RemindManager.Instance:Fire(RemindName.QunXiongZhuLu)
end

function QunXiongZhuLuWGCtrl:GetGuildRankList(rank_type, rank_list)
    self.my_guild_rank = nil
    for k,v in pairs(rank_list) do
        if v.guild_id == RoleWGData.Instance.role_vo.guild_id then
            self.my_guild_rank = k
        end
    end
end

function QunXiongZhuLuWGCtrl:MyGuildRank()
    if self.my_guild_rank ~= nil then
        return self.my_guild_rank
    end
    return 0
end