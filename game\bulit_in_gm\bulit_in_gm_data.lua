BulitInGMData = BulitInGMData or BaseClass()
function BulitInGMData:__init()
	if BulitInGMData.Instance then
		error("[BulitInGMData] Attempt to create singleton twice!")
		return
	end

	BulitInGMData.Instance = self

    self:InitNeedPrintLogList()
    local cfg = ConfigManager.Instance:GetAutoConfig("bulit_in_gm_auto")
    self.big_type_cfg = cfg.big_type
    self.sub_type_map_cfg = ListToMap(cfg.sub_type, "big_type", "seq")
end

function BulitInGMData:__delete()
    BulitInGMData.Instance = nil
end

function BulitInGMData:GetBigTypeCfg()
    return self.big_type_cfg
end

function BulitInGMData:GetSubTypeCfgList(big_type)
    return self.sub_type_map_cfg[big_type] or {}
end

function BulitInGMData:GetPrintLogData()
    return {
        is_print = true,
        print_str = "",
    }
end

function BulitInGMData:InitNeedPrintLogList()
    self.need_print_log_list = {}
    for i = 1, 5 do
        table.insert(self.need_print_log_list, self:GetPrintLogData())
    end
end

function BulitInGMData:GetNeedPrintLogList()
    return self.need_print_log_list
end
