require("game/super_purchase/super_purchase_wg_data")
require("game/super_purchase/super_purchase_view")

SuperPurchaseWGCtrl = SuperPurchaseWGCtrl or BaseClass(BaseWGCtrl)
function SuperPurchaseWGCtrl:__init()
	if SuperPurchaseWGCtrl.Instance then
		ErrorLog("[SuperPurchaseWGCtrl] attempt to create singleton twice!")
		return
	end

	SuperPurchaseWGCtrl.Instance = self
	self.data = SuperPurchaseWGData.New()
    self.view = SuperPurchaseView.New(GuideModuleName.SuperPurchaseView)
  
    self:RegisterAllProtocols()
    self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind(self.OnPassDay, self))
end

function SuperPurchaseWGCtrl:__delete()
	SuperPurchaseWGCtrl.Instance = nil

	self.view:DeleteMe()
	self.view = nil

    self.data:DeleteMe()
	self.data = nil
end

function SuperPurchaseWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCOALimitRmbBuy3Info,"OnSCOALimitRmbBuy3Info")
end

function SuperPurchaseWGCtrl:OnSCOALimitRmbBuy3Info(protocol)
	--print_error("===========数据============", protocol)
	self.data:SetAllBuyInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.SuperPurchaseView)
end

function SuperPurchaseWGCtrl:ReqActivityLimitBuy3Info(opera_type, param_1, param_2, param_3)
    local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_LIMIT_RMB_BUY3
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param_1 or 0
 	protocol.param_2 = param_2 or 0
 	protocol.param_3 = param_3 or 0
 	protocol:EncodeAndSend()
end

function SuperPurchaseWGCtrl:OnPassDay()
    GlobalTimerQuest:AddDelayTimer(function()
		self:ReqActivityLimitBuy3Info(OA_LIMIT_RMB_BUY3_OPERATE_TYPE.INFO)
	end, 2)
end
