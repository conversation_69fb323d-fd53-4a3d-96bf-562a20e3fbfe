FightSoulSelectListView = FightSoulSelectListView or BaseClass(SafeBaseView)
function FightSoulSelectListView:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function FightSoulSelectListView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(966, 492)})
	self:AddViewResource(0, "uis/view/fight_soul_ui_prefab", "layout_select_fight_soul")
end

function FightSoulSelectListView:__delete()

end

function FightSoulSelectListView:ReleaseCallBack()
	if self.fight_soul_list_view then
		self.fight_soul_list_view:DeleteMe()
		self.fight_soul_list_view = nil
	end

	self.select_equip_list = nil
    self.select_solt = nil
    self.data_list = nil
end

function FightSoulSelectListView:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.FightSoul.SelectTitle
    self.fight_soul_list_view = AsyncListView.New(SelectFightSoulRender, self.node_list["list_view"])
    self.fight_soul_list_view:SetSelectCallBack(BindTool.Bind1(self.OnSelectItemCB, self))
end

function FightSoulSelectListView:SetDataAndOpen(slot)
    self.select_solt = slot
    local fight_soul_slot = FightSoulWGData.Instance:GetFightSoulSlot(self.select_solt)
    if fight_soul_slot == nil then
        return
    end

    self.data_list = FightSoulWGData.Instance:GetFightSoulSelectListByType(fight_soul_slot:GetType())
    if IsEmptyTable(self.data_list) then
        TipWGCtrl.Instance:ShowSystemMsg(Language.FightSoul.SelectLimit)
		local get_way_list = TipWGData.Instance:GetGetWayList(17105)
		if not IsEmptyTable(get_way_list) then
			FightSoulWGCtrl.Instance:OpenFightSoulGetWayTips(get_way_list)
		end
		return
    else
        self:Open()
    end
end

function FightSoulSelectListView:OnFlush()
    if nil ~= self.fight_soul_list_view then
        self.fight_soul_list_view:SetDataList(self.data_list)
        self.fight_soul_list_view:CancelSelect()
    end
end

-- 选择列表项回调
function FightSoulSelectListView:OnSelectItemCB(item, cell_index, is_default, is_click)
	if not is_click then
		return
	end

	if nil == item or nil == item.data or nil == item.data.item_data then
		return
	end

	FightSoulWGCtrl.Instance:ReqFightSoulOp(FIGHT_SOUL_OP_TYPE.FIGHT_SOUL_WEAR, self.select_solt, item.data.item_data.index)
	self:Close()
end

-----------------------------------------------------------------------------
