SupremeFieldsView = SupremeFieldsView or BaseClass(SafeBaseView)

function SupremeFieldsView:__init()
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
	self:AddViewResource(0, "uis/view/supreme_fields_ui_prefab", "layout_supreme_fields")
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_top_panel")
	self.type_index = -1;
	self.slot_index = 1;
	self.skill_id = nil
	self.sf_list_jump_index = nil

	self:SetTabShowUIScene(0, {type = UI_SCENE_TYPE.DEFAULT, config_index = UI_SCENE_CONFIG_INDEX.LINGYU})
end

function SupremeFieldsView:LoadCallBack()
	self.sf_flush_wait_flag = true

	if not self.slot_obj_list then
		self.slot_obj_list = {}
		local slot_num = self.node_list.content.transform.childCount
		for i = 1, slot_num do
			self.slot_obj_list[i] = FieldsSlotRender.New(self.node_list.content:FindObj("slot_" .. i))
			self.slot_obj_list[i]:SetIndex(i)
			self.slot_obj_list[i]:SetSelectCallBack(BindTool.Bind(self.SlotSelectCallBack, self))
		end
	end

	if nil == self.attr_obj_list then
		self.attr_obj_list = {}
		-- local attr_num = self.node_list.attr_list.transform.childCount
		for i = 1, 5 do
			local cell = CommonAddAttrRender.New(self.node_list["attr_" .. i])
			cell:SetAttrNameNeedSpace(true)
			cell:SetIndex(i)
			self.attr_obj_list[i] = cell
		end
	end

	if nil == self.attr_obj_list1 then
		self.attr_obj_list1 = {}
		-- local attr_num = self.node_list.attr_list1.transform.childCount
		for i = 1, 3 do
			local cell = SAddAttrRender.New(self.node_list["sattr_" .. i])
			self.attr_obj_list1[i] = cell
		end
	end

	if self.item_cell == nil then
		self.item_cell = ItemCell.New(self.node_list.item_cell)
		-- self.item_cell:SetNeedItemGetWay(true)
	end

	if not self.model_display then
		self.model_display = RoleModel.New()
		self.model_display:SetUISceneModel(self.node_list["display"].event_trigger_listener,
								MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.model_display)

		-- self.model_display = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["display"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.M,
		-- 	can_drag = false,
		-- }
		
		-- self.model_display:SetRenderTexUI3DModel(display_data)
		-- -- self.model_display:SetRTAdjustmentRootLocalScale(0.6)
		-- -- self.model_display:SetUI3DModel(self.node_list["display"].transform,
		-- -- 	self.node_list.EventTriggerListener.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		-- self:AddUiRoleModel(self.model_display)
	end

	if not self.fz_display then
		self.fz_display = RoleModel.New()
		self.fz_display:SetUISceneModel(self.node_list["fzdisplay"].event_trigger_listener,
								MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.fz_display)



		-- self.fz_display = RoleModel.New()
		-- local display_data = {
		-- 	parent_node = self.node_list["fzdisplay"],
		-- 	camera_type = MODEL_CAMERA_TYPE.BASE,
		-- 	-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
		-- 	rt_scale_type = ModelRTSCaleType.L,
		-- 	can_drag = false,
		-- }
		
		-- self.fz_display:SetRenderTexUI3DModel(display_data)
		-- -- self.fz_display:SetUI3DModel(self.node_list["fzdisplay"].transform,
		-- -- 	self.node_list.EventTriggerListener.event_trigger_listener, 1, false, MODEL_CAMERA_TYPE.BASE)
		-- self:AddUiRoleModel(self.fz_display)
	end

	-- local bundle, asset = ResPath.GetRawImagesPNG(TONGYONG_RAWIMAGE_ENUM.HAVE_ROLEMODEL)
	-- self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	-- end)

	XUI.AddClickEventListener(self.node_list["skill_bg"], BindTool.Bind(self.OnBtnSkillIcon, self))    --技能预览
	XUI.AddClickEventListener(self.node_list["btn"], BindTool.Bind(self.OnActOrUpgradeClick, self))    --孔位激活升级
	XUI.AddClickEventListener(self.node_list["battle_btn"], BindTool.Bind(self.OnBtnBatClick, self))   --上阵
	XUI.AddClickEventListener(self.node_list["hh_btn"], BindTool.Bind(self.OnBtnHHClick, self))        --幻化
	XUI.AddClickEventListener(self.node_list["act_btn"], BindTool.Bind(self.OnBtnActClick, self))      --活动按钮
	XUI.AddClickEventListener(self.node_list["wuxing_btn"], BindTool.Bind(self.OnBtnOpenWuXing, self)) --五行通天.
	XUI.AddClickEventListener(self.node_list["all_attr_btn"], BindTool.Bind(self.OnBtnOpenAllAttr, self)) --总属性.
	XUI.AddClickEventListener(self.node_list["btn_open_fields"], BindTool.Bind(self.OnCliclOpenFieldsBtn, self)) --总属性.

	-- self.message_root_tween = self.node_list.content:GetComponent(typeof(UGUITweenPosition))
	-- XUI.AddClickEventListener(self.node_list.forward_button, BindTool.Bind2(self.PlaySupremeFieldsCententTween, self, false))
	-- XUI.AddClickEventListener(self.node_list.reverse_button, BindTool.Bind2(self.PlaySupremeFieldsCententTween, self, true))

	SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.GET_STAR_ATTR)
	SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.GET_TOTAL_STAR_ATTR)
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.SupremeFieldsWGView, self.get_guide_ui_event)

	SupremeFieldsWGData.Instance:SetFieldToggleList()
	self:CreateToggleList()

	self.is_show_fields_tog = false
	self:FlushFieldsSelectRoot()
	self.select_big_type_data = {}
end

function SupremeFieldsView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "OnFootLightUse" then --幻化刷新

		elseif k == "OnFootLightItem" then --孔位操作刷新
			self:OnFlushSlot()
			self:OnFlushAttrView(self.type_index, self.slot_index)
		elseif k == "OnAllFootLight" then
			self:OnFlushSlot()
			self:OnFlushAttrView(self.type_index, self.slot_index)
		elseif k == "OnShowRed" then
			self:FlushConsume()
			self:OnFlushSlot()
		elseif k == "jump" then
			self.sf_list_jump_index = self:GetJumpToIndex(v.type)
		end
	end

	self:OnFlushSkillView()
	self:FlushWuXingBtnRemind()
	self:OnFlushTitleInfo()
	SupremeFieldsWGData.Instance:SetFieldToggleList()
	self:FlushSFToggleAllData()
end

function SupremeFieldsView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("zhi_zun_view_time") then
		CountDownManager.Instance:RemoveCountDown("zhi_zun_view_time")
	end

	if nil ~= self.slot_obj_list then
		for k, v in pairs(self.slot_obj_list) do
			v:DeleteMe()
		end
		self.slot_obj_list = nil
	end

	if nil ~= self.attr_obj_list then
		for k, v in pairs(self.attr_obj_list) do
			v:DeleteMe()
		end
		self.attr_obj_list = nil
	end

	if nil ~= self.attr_obj_list1 then
		for k, v in pairs(self.attr_obj_list1) do
			v:DeleteMe()
		end
		self.attr_obj_list1 = nil
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.fz_display then
		self.fz_display:DeleteMe()
		self.fz_display = nil
	end

	self.sf_accordion_list = nil
	self.load_sf_cell_complete = nil
	self.sf_force_big_type = nil
	self.sf_force_small_type = nil
	self.sf_select_big_type = nil
	self.sf_select_small_type = nil
	self.sf_stop_cur = nil

	if self.sf_big_type_toggle_list ~= nil then
		for k, v in ipairs(self.sf_big_type_toggle_list) do
			v:DeleteMe()
		end
		self.sf_big_type_toggle_list = nil
	end

	if self.sf_small_type_list then
		for k, v in pairs(self.sf_small_type_list) do
			for k1, v1 in pairs(v) do
				v1:DeleteMe()
			end
			self.sf_small_type_list[k] = nil
		end
		self.sf_small_type_list = nil
	end

	-- if not IsNil(self.message_root_tween) then
	-- 	self.message_root_tween = nil
	-- end

	self.sf_list_jump_index = nil
	self.skill_id = nil
	self.slot_index = 1
	self.type_index = -1
	self.selected_cell_data = nil
	self.select_big_type_data = nil
	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.SupremeFieldsWGView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function SupremeFieldsView:ShowIndexCallBack()
	self:FlushTimeCount()
end

-- function SupremeFieldsView:PlaySupremeFieldsCententTween(is_forward)
-- 	self.node_list.forward_button:CustomSetActive(is_forward)
-- 	self.node_list.reverse_button:CustomSetActive(not is_forward)

-- 	if not IsNil(self.message_root_tween) then
-- 		if is_forward then
-- 			self.message_root_tween:PlayForward()
-- 		else
-- 			self.message_root_tween:PlayReverse()
-- 		end
-- 	end
-- end

-- 创建扩展列表
function SupremeFieldsView:CreateToggleList()
	if nil == self.sf_accordion_list then
		self.sf_big_type_toggle_list = {}
		self.sf_small_type_list = {}
		self.sf_accordion_list = {}
		self.load_sf_cell_complete = false
		local toggle_list = SupremeFieldsWGData.Instance:GetFieldToggleList()

		local toggle_length = 0
		for k, v in pairs(toggle_list) do
			toggle_length = toggle_length + 1
		end

		local idx = 0
		for i, value in pairs(toggle_list) do
			idx = idx + 1
			self.sf_accordion_list[i] = self.node_list["List" .. i]
			local select_btn = self.node_list["SelectBtn" .. i]

			local big_btn_cell = SupremeFieldsBigTypeToggleRender.New(select_btn)
			big_btn_cell:SetIndex(i)
			big_btn_cell:SetOnlyClickCallBack(BindTool.Bind(self.OnClickSFBigTypeToggle, self))
			self.sf_big_type_toggle_list[i] = big_btn_cell

			local small_num = value.child_list and #value.child_list or 0
			self:LoadsfSmallCellList(idx, i, toggle_length, small_num)
		end
	end
end

-- 加载子标签
function SupremeFieldsView:LoadsfSmallCellList(idx, index, big_type_num, small_num)
	-- print_error("---加载子标签---", index, big_type_num, small_num)
	local res_async_loader = AllocResAsyncLoader(self, "sf_accordion_item" .. index)
	res_async_loader:Load("uis/view/supreme_fields_ui_prefab", "sf_accordion_item", nil, function(new_obj)
		local item_vo_list = {}
		for i = 1, small_num do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			obj_transform:SetParent(self.sf_accordion_list[index].transform, false)

			local item_render = SupremeFieldsListItemRender.New(obj)
			item_render:SetIndex(i)
			item_render:SetClickCallBack(BindTool.Bind(self.OnClickSFSmallType, self))
			item_vo_list[i] = item_render

			if idx == big_type_num and i == small_num then
				self.load_sf_cell_complete = true
			end
		end

		self.sf_small_type_list[index] = item_vo_list
		if self.load_sf_cell_complete then
			-- 设置数据
			self:FlushSFToggleAllData()

			-- 加载完 是否要选择标签
			if self.sf_flush_wait_flag then
				self:SFSelectToggle()
			end
		end
	end)
end

-- 刷新标签数据
function SupremeFieldsView:FlushSFToggleAllData()
	-- print_error("---刷新标签数据---")
	if IsEmptyTable(self.sf_big_type_toggle_list) then
		return
	end

	if IsEmptyTable(self.sf_small_type_list) then
		return
	end

	local toggle_list = SupremeFieldsWGData.Instance:GetFieldToggleList()
	for k, v in pairs(toggle_list) do
		self.sf_big_type_toggle_list[k]:SetData(v)
		local small_list = self.sf_small_type_list[k]
		local suit_list_data = v.child_list
		if not IsEmptyTable(small_list) then
			for k1, v1 in pairs(small_list) do
				v1:SetData(suit_list_data[k1])
			end
		end
	end
end

-- 标签选择
function SupremeFieldsView:SFSelectToggle(force_big_type, force_small_type, stop_cur)
	-- print_error("---标签选择---", force_big_type, force_small_type, stop_cur)
	-- if force_big_type then
	-- 	self.sf_force_big_type = force_big_type
	-- end

	-- if force_small_type then
	-- 	self.sf_force_small_type = force_small_type
	-- end

	-- if not self.load_sf_cell_complete then
	-- 	return
	-- else
	-- 	self.sf_flush_wait_flag = false
	-- end

	-- local jump_index
	-- local toggle_list = SupremeFieldsWGData.Instance:GetFieldToggleList()

	-- if self.sf_force_big_type then
	-- 	jump_index = self.sf_force_big_type
	-- 	self.sf_force_big_type = nil
	-- elseif stop_cur then
	-- 	self.sf_stop_cur = true
	-- 	jump_index = self.sf_select_big_type
	-- else
	-- 	for k, v in ipairs(toggle_list) do
	-- 		if v.is_remind then -- 跳红点
	-- 			jump_index = k
	-- 			break
	-- 		end
	-- 	end

	-- 	jump_index = jump_index or self.sf_select_big_type
	-- end

	-- if not jump_index then
	-- 	for key, value in pairs(toggle_list) do
	-- 		jump_index = key
	-- 		break
	-- 	end
	-- end

	--策划要求选中All
	local toggle_list = SupremeFieldsWGData.Instance:GetFieldToggleList()
	local jump_index
	local default_index

	for k, v in pairs(toggle_list) do
		if v.is_remind then -- 跳红点
			jump_index = k
			-- break
		end

		default_index = k
	end

	jump_index = nil ~= jump_index and jump_index or default_index

	if jump_index then
		if self.sf_select_big_type ~= jump_index then
			self.sf_big_type_toggle_list[jump_index]:SetAccordionElementState(true)
		else
			self:OnClickSFBigTypeToggle(self.sf_big_type_toggle_list[jump_index])
		end
	end
end

-- 大标签回调
function SupremeFieldsView:OnClickSFBigTypeToggle(cell, isOn)
	-- print_error("【----点击 大 回调-----】：", cell:GetIndex(), isOn, self.sf_force_small_type, self.sf_select_small_type, self.sf_stop_cur)

	local index = cell:GetIndex()
	local data = cell:GetData()
	if data == nil then
		return
	end

	self.select_big_type_data = data
	self.sf_select_big_type = index
	local jump_small_index

	self.is_show_fields_tog = false
	self:FlushFieldsSelectRoot()

	if self.sf_force_small_type then
		jump_small_index = self.sf_force_small_type
		self.sf_force_small_type = nil
	elseif self.sf_stop_cur then
		jump_small_index = self.sf_select_small_type
		self.sf_stop_cur = nil
	else
		for k, v in ipairs(data.child_list) do
			if v.is_remind then -- 跳红点
				jump_small_index = k
				break
			end
		end

		jump_small_index = jump_small_index or self.sf_select_small_type
	end

	jump_small_index = jump_small_index or 1

	if IsEmptyTable(self.sf_small_type_list) then
		return
	end

	local small_type_cell = ((self.sf_small_type_list or {})[index] or {})[jump_small_index]
	if not small_type_cell then
		jump_small_index = 1	--数量不匹配的时候给个默认值.
		small_type_cell = ((self.sf_small_type_list or {})[index] or {})[jump_small_index]
	end

	if small_type_cell then
		small_type_cell:OnClick()
	end
end

-- 子标签回调
function SupremeFieldsView:OnClickSFSmallType(cell)
	-- print_error("【----点击 子 回调-----】：", cell.index, cell:GetData().type, cell:GetData().name, self.sf_select_small_type)
	if cell == nil then
		return
	end

	local data = cell:GetData()
	if data == nil then
		return
	end
	self.selected_cell_data = data

	local index = cell.index

	if self.sf_select_small_type ~= index then
		self.ui_scene_change_config_index = data.cfg.ui_scene_config_index
		Scene.Instance:ChangeUISceneController(UI_SCENE_TYPE.DEFAULT, UI_SCENE_CONFIG_INDEX.LINGYU, data.cfg.ui_scene_config_index)
	end

	self.sf_select_small_type = index

	local list = self.sf_small_type_list[self.sf_select_big_type]
	if list then
		for k, v in pairs(list) do
			v:OnSelectSuitChange(index)
		end
	end

	self:FlushSPView(data.type)
end

function SupremeFieldsView:FlushSPView(type)
	if type == self.type_index then
		return
	end

	--self.slot_index = 1
	self.type_index = type
	self:OnFlushSlot()
	self:OnFlushSkillView()
	self:OnFlushAttrView(self.type_index, self.slot_index)
	self:FlushModel()
	self:OnFlushTitleInfo()
	self:FlushWuXingBtnRemind()
end

function SupremeFieldsView:SlotSelectCallBack(slot_index, slot_data)
	local _, is_open = SupremeFieldsWGData.Instance:TestFootLightSlotOpen(self.type_index, slot_index)
	if self.slot_index == slot_index then
		TipWGCtrl.Instance:OpenItem({ item_id = slot_data.item_id })
	end
	self.slot_index = slot_index
	self:OnFlushAttrView(self.type_index, slot_index)
end

-- 消耗
function SupremeFieldsView:FlushConsume()
	local slot_cfg = SupremeFieldsWGData.Instance:GetFootLightSlotCfgByIndex(self.type_index, self.slot_index)
	local num = ItemWGData.Instance:GetItemNumInBagById(slot_cfg.item_id)
	local is_have_item = false
	is_have_item = num >= slot_cfg.cost_item_num
	self.item_cell:SetData({ item_id = slot_cfg.item_id })
	self.item_cell:SetRightBottomColorText(num .. "/" .. slot_cfg.cost_item_num,
		is_have_item and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH)
	self.item_cell:SetRightBottomTextVisible(true)

	local lv, is_open = SupremeFieldsWGData.Instance:TestFootLightSlotOpen(self.type_index, self.slot_index)
	lv = is_open and lv or 0

	for i = 1, 10 do
		local asset_name = i <= lv and "a3_ty_xx_zc" or "a3_ty_xx_zh" 
		self.node_list["supreme_fields_star" .. i].image:LoadSprite(ResPath.GetCommonImages(asset_name))
	end


	-- for i = 1, 5 do
	-- 	local cur_star_res_list = GetStarImgResByStar(lv)
	-- 	for i = 1, 5 do
	-- 		self.node_list["supreme_fields_star" .. i].image:LoadSprite(ResPath.GetCommonImages(cur_star_res_list[i]))
	-- 	end
	-- end
end

function SupremeFieldsView:GetJumpToIndex(supreme_fields_type)
	local data_list = SupremeFieldsWGData.Instance:GetFootLightList()

	for k, v in pairs(data_list) do
		if v.type == tonumber(supreme_fields_type) then
			return k
		end
	end

	return 1
end

function SupremeFieldsView:OnFlushSlot()
	local data_list = SupremeFieldsWGData.Instance:GetFootLightSlotCfg(self.type_index)
	for i, v in ipairs(self.slot_obj_list) do
		if data_list[i] then
			v:SetData(data_list[i])
		end
	end

	local is_red = SupremeFieldsWGData.Instance:TestFootLightSlotRedPoint(self.type_index, self.slot_index)
	if not is_red then
		for k, v in pairs(self.slot_obj_list) do
			is_red = SupremeFieldsWGData.Instance:TestFootLightSlotRedPoint(self.type_index, v.index)
			if is_red then
				v:SetToggleSelect()
				break
			end
		end
	end
end

-- 属性
function SupremeFieldsView:OnFlushAttrView(type_index, slot_index)
	local slot_cfg = SupremeFieldsWGData.Instance:GetFootLightSlotCfgByIndex(type_index, slot_index)
	local name = ItemWGData.Instance:GetItemConfig(slot_cfg.item_id).name
	local attr_list, attr_list_1 = SupremeFieldsWGData.Instance:GetFootLightSlotAttrList(type_index, slot_index)
	local lv, is_open = SupremeFieldsWGData.Instance:TestFootLightSlotOpen(type_index, slot_index)
	for k, v in ipairs(self.attr_obj_list) do
		v:SetData(attr_list[k])
	end

	-- self.node_list.attr_list1:SetActive(not IsEmptyTable(attr_list_1))
	for i, v in ipairs(self.attr_obj_list1) do
		v:SetData(attr_list_1[i])
	end

	local is_red = SupremeFieldsWGData.Instance:TestFootLightSlotRedPoint(type_index, slot_index)
	self.node_list.btn_act_red:SetActive(is_red)
	self.node_list.slot_name.text.text = name
	self.node_list.btn_txt.text.text = is_open and Language.SupremeFields.BtnText1 or Language.SupremeFields.BtnText2
	self.node_list.img_max:SetActive(slot_cfg.max_lv <= lv)
	self.node_list.btn:SetActive(slot_cfg.max_lv > lv)
	self.node_list.item_cell:SetActive(slot_cfg.max_lv > lv)
	self:FlushConsume()
end

-- 刷新技能信息
function SupremeFieldsView:OnFlushSkillView()
	local is_use = SupremeFieldsWGData.Instance:TestFootLightUse(self.type_index)
	local cfg = SupremeFieldsWGData.Instance:GetFootLightCfg(self.type_index)
	if not cfg then return end
	local lv, is_open = SupremeFieldsWGData.Instance:TestFootLightOpen(self.type_index)
	local title = is_open and string.format(Language.SupremeFields.JieShu, cfg.name, lv) or cfg.name
	-- local skill_lv = self.selected_cell_data.lv
	local skill_lv = SupremeFieldsWGData.Instance:GetStarAttrInfoByType(self.selected_cell_data.cfg.type)

	local title_des = skill_lv and skill_lv > 0 and string.format(Language.SupremeFields.StarNumText2, skill_lv) or Language.SupremeFields.NoActive
	local cap = SupremeFieldsWGData.Instance:GetAllSlotCap(self.type_index)
	local index = is_open and lv or 1
	local skill_id = SupremeFieldsWGData.Instance:GetSkillIDList(self.type_index, index)[1] or 0
	local slot_cfg = SupremeFieldsWGData.Instance:GetFootLightSlotCfgByIndex(self.type_index, self.slot_index)
	if is_use then
		self.node_list.hh_txt.text.text = Language.SupremeFields.hh_txt
	else
		self.node_list.hh_txt.text.text = Language.SupremeFields.wei_hh_txt
	end
	local grade, cur_shop_cfg, _ = SupremeFieldsWGData.Instance:GetCurShopCfg()
	local buy_tmb_count, is_free = SupremeFieldsWGData.Instance:GetShopIsBuyFlag()
	self.node_list.red:SetActive(not is_free)
	self.node_list["act_btn"]:SetActive(self.type_index == grade and buy_tmb_count <= cur_shop_cfg.buy_count_limit)
	self.node_list.btn_bat_red:SetActive(SupremeFieldsWGData.Instance:TestSkillCanBattle() and
		SupremeFieldsWGData.Instance:TestSkillSlotHasSurplus())
	self.node_list.hh_btn:SetActive(is_open)
	self.node_list.title.text.text = title
	self.node_list.title_des.text.text = title_des
	self.node_list["cap_value"].text.text = cap
	local skill_cfg = SupremeFieldsWGData.Instance:GetFootLightSkillCfg(skill_id)
	local bundle, asset = ResPath.GetSkillIconById(skill_cfg.icon)
end

function SupremeFieldsView:FlushModel()
	self.model_display:RemoveAllModel()
	self.fz_display:RemoveAllModel()

	local cfg = SupremeFieldsWGData.Instance:GetFootLightCfg(self.type_index)
	if not cfg then
		return 
	end

	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self.model_display:SetModelResInfo(role_vo, nil, function()
		self.model_display:PlayRoleShowAction()
	end)

	if cfg.role_model_pos and cfg.role_model_pos ~= "" then
		local pos = Split(cfg.role_model_pos, "|")
		self.model_display:SetUSAdjustmentNodeLocalPosition(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	if cfg.role_model_rot and cfg.role_model_rot ~= "" then
		local pos = Split(cfg.role_model_rot, "|")
		self.model_display:SetUSAdjustmentNodeLocalRotation(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	if cfg.role_model_scale and cfg.role_model_scale ~= "" then
		self.model_display:SetUSAdjustmentNodeLocalScale(tonumber(cfg.role_model_scale) or 0)
	end

	self.model_display:FixToOrthographic(self.root_node_transform)
	-- self.model_display:SetRTAdjustmentRootTransform({model_pos = cfg.role_model_pos, model_rot = cfg.role_model_rot, model_scale = cfg.role_model_scale})

	local bundle, asset = ResPath.GetSkillFaZhenModel(self.type_index)
	self.fz_display:SetMainAsset(bundle, asset)

	if cfg.lingyu_model_pos and cfg.lingyu_model_pos ~= "" then
		local pos = Split(cfg.lingyu_model_pos, "|")
		self.fz_display:SetUSAdjustmentNodeLocalPosition(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	if cfg.lingyu_model_rot and cfg.lingyu_model_rot ~= "" then
		local pos = Split(cfg.lingyu_model_rot, "|")
		self.fz_display:SetUSAdjustmentNodeLocalRotation(tonumber(pos[1]) or 0, tonumber(pos[2]) or 0, tonumber(pos[3]) or 0)
	end

	if cfg.lingyu_model_scale and cfg.lingyu_model_scale ~= "" then
		self.fz_display:SetUSAdjustmentNodeLocalScale(tonumber(cfg.lingyu_model_scale) or 0)
	end

	-- self.fz_display:SetRTAdjustmentRootTransform({model_pos = cfg.lingyu_model_pos, model_rot = cfg.lingyu_model_rot, model_scale = cfg.lingyu_model_scale})
end

-- 技能及星级红点
function SupremeFieldsView:FlushWuXingBtnRemind()
	local is_red = SupremeFieldsWGData.Instance:GetWuXingLateAttrIsCanActivate(self.type_index)
	self.node_list.wuxing_red:SetActive(is_red)

	local is_red2 = SupremeFieldsWGData.Instance:GetWuXingTotalAttrIsCanActivate()
	self.node_list.wuxing_attr_red:SetActive(is_red2)
end

function SupremeFieldsView:OnBtnSkillIcon()
	local wuxing_star_list = SupremeFieldsWGData.Instance:GetAllStarAttrInfo()
	if IsEmptyTable(wuxing_star_list) or not wuxing_star_list[self.type_index] then
		return
	end

	local lv = wuxing_star_list[self.type_index]
	local is_open = wuxing_star_list[self.type_index] > 0

	lv = is_open and lv or 1
	local data = SupremeFieldsWGData.Instance:SkillShowCfgList(self.type_index, lv)

	--local data = SupremeFieldsWGData.Instance:SkillShowCfgList(self.type_index)
	CommonSkillShowCtrl.Instance:SetSupremeFieldsSkillViewDataAndOpen(data)
end

-- 技能图标
function SupremeFieldsView:OnFlushTitleInfo()
	if self.type_index < 0 then
		return
	end

	local bundle, asset = ResPath.GetSupremeFieldsIcon(self.type_index)
	self.node_list.field_icon.image:LoadSpriteAsync(bundle, asset, function()
		self.node_list.field_icon.image:SetNativeSize()
	end)
end

function SupremeFieldsView:OnActOrUpgradeClick()
	local slot_cfg = SupremeFieldsWGData.Instance:GetFootLightSlotCfgByIndex(self.type_index, self.slot_index)
	local num = ItemWGData.Instance:GetItemNumInBagById(slot_cfg.item_id)
	if num < slot_cfg.cost_item_num then
		TipWGCtrl.Instance:OpenItem({ item_id = slot_cfg.item_id })
		SysMsgWGCtrl.Instance:ErrorRemind(Language.SupremeFields.Tips3)
		return
	end

	local level, is_open = SupremeFieldsWGData.Instance:TestFootLightSlotOpen(self.type_index, self.slot_index)
	local max_level = slot_cfg.max_lv
	if level >= max_level then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.SupremeFields.Tips11)
		return
	end

	if is_open then
		--五行孔位升级操作
		SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.SLOT_UPGRADE, self.type_index, self
			.slot_index)
	else
		--五行孔位激活操作
		local bag_index = ItemWGData.Instance:GetItemIndex(slot_cfg.item_id)
		SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.SLOT_ACT, self.type_index, self.slot_index,
			bag_index)
	end
end

function SupremeFieldsView:OnBtnBatClick()
	SupremeFieldsWGCtrl.Instance:OpenFieldsBattleView()
end

function SupremeFieldsView:OnBtnHHClick()
	local cfg = SupremeFieldsWGData.Instance:GetAllSFootLightById(self.type_index)
	if cfg.is_use then
		SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.CANCEL_SUPREME_TRANSFIG, self.type_index)
		return
	end

	SupremeFieldsWGCtrl.Instance:SendOperation(Supreme_Operation_Index.SUPREME_TRANSFIG, self.type_index)
end

function SupremeFieldsView:OnBtnActClick()
	SupremeFieldsWGCtrl.Instance:OpenFieldsActivityView()
end

function SupremeFieldsView:OnBtnOpenWuXing()
	SupremeFieldsWGCtrl.Instance:OpenFieldsWuxingView(self.type_index)
end

function SupremeFieldsView:OnBtnOpenAllAttr()
	SupremeFieldsWGCtrl.Instance:OpenFieldsWuxingAttrTips()
end

function SupremeFieldsView:FlushTimeCount()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	local time
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	else
		time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
	end

	if time > 0 then
		if CountDownManager.Instance:HasCountDown("zhi_zun_view_time") then
			CountDownManager.Instance:RemoveCountDown("zhi_zun_view_time")
		end

		CountDownManager.Instance:AddCountDown("zhi_zun_view_time",
			BindTool.Bind(self.FinalUpdateTimeCallBack, self),
			BindTool.Bind(self.OnComplete, self),
			nil, time, 1)
	else
		self:OnComplete()
	end
end

function SupremeFieldsView:FinalUpdateTimeCallBack(now_time, total_time)
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_ZHI_ZUN)
	local time = math.ceil(total_time - now_time)
	local time_str = TimeUtil.FormatSecondDHM8(time)
	if activity_info and activity_info.status == ACTIVITY_STATUS.OPEN then
		self.node_list["time_str"].text.text = string.format(Language.SupremeFields.ActivityViewTime, time_str)
	else
		self.node_list["time_str"].text.text = string.format(Language.SupremeFields.ActivityViewTime, time_str)
	end
end

function SupremeFieldsView:OnComplete()
	self.node_list.time_str.text.text = ""
end

function SupremeFieldsView:OnCliclOpenFieldsBtn()
	self.is_show_fields_tog = not self.is_show_fields_tog
	self:FlushFieldsSelectRoot()
end

function SupremeFieldsView:FlushFieldsSelectRoot()
	if self.sf_select_big_type then
		local tab_type_data = SupremeFieldsWGData.Instance:GetFootLightTabTypeCfg(self.sf_select_big_type)
		if tab_type_data and tab_type_data.sub_type_icon then
			local bundle, asset = ResPath.GetCommonImages(tab_type_data.sub_type_icon)
			self.node_list.btn_open_fields.image:LoadSpriteAsync(bundle, asset, function()
				self.node_list.btn_open_fields.image:SetNativeSize()
			end)
		end

		if tab_type_data and tab_type_data.effect_res_id then
			local attach_obj = self.node_list.btn_open_fields_effect.gameObject:GetComponent(typeof(Game.GameObjectAttach))
			local bundle, asset = ResPath.GetUIEffect(tab_type_data.effect_res_id)
			attach_obj.BundleName = bundle
			attach_obj.AssetName = asset
			self.node_list.btn_open_fields_effect:SetActive(false)
			self.node_list.btn_open_fields_effect:SetActive(true)
		end
	end

	self.node_list.flag_fields_close:CustomSetActive(not self.is_show_fields_tog)
	self.node_list.supreme_fields_select_root.canvas_group.alpha = self.is_show_fields_tog and 1 or 0
	self.node_list.supreme_fields_select_root.canvas_group.blocksRaycasts = self.is_show_fields_tog

	local show_remind = false
	-- 收起时候显示总红点
	if not self.is_show_fields_tog then
		local toggle_list = SupremeFieldsWGData.Instance:GetFieldToggleList()
		if not IsEmptyTable(toggle_list) then
			for k, v in pairs(toggle_list) do
				if v.is_remind then
					show_remind = true
					break
				end
			end
		end
	else
		show_remind = (self.select_big_type_data or {}).is_remind or false
	end

	self.node_list.btn_open_fields_remind:CustomSetActive(show_remind)
end


function SupremeFieldsView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	return self.node_list[ui_name]
end

----------------------------------------------------------------------------
-- 大类型toggle
--SupremeFieldsBigTypeToggleRender
----------------------------------------------------------------------------
SupremeFieldsBigTypeToggleRender = SupremeFieldsBigTypeToggleRender or BaseClass(BaseRender)
function SupremeFieldsBigTypeToggleRender:__init()
	self.view.accordion_element:AddClickListener(BindTool.Bind(self.OnClickAccordion, self))
end

function SupremeFieldsBigTypeToggleRender:SetOnlyClickCallBack(callback)
	self.click_callback = callback
end

function SupremeFieldsBigTypeToggleRender:OnClickAccordion(isOn)
	if nil ~= self.click_callback then
		self.click_callback(self, isOn)
	end
end

function SupremeFieldsBigTypeToggleRender:OnFlush()
	if self.data == nil or not self.data.is_show then
		self.view:SetActive(false)
		return
	end

	local tab_type_data = SupremeFieldsWGData.Instance:GetFootLightTabTypeCfg(self.data.type)
	local bundle, asset = ResPath.GetCommonImages(tab_type_data.sub_type_icon)
	self.node_list.noraml.image:LoadSpriteAsync(bundle, asset, function()
		self.node_list.noraml.image:SetNativeSize()
	end)

	if tab_type_data and tab_type_data.effect_res_id then
		local attach_obj = self.node_list.effect.gameObject:GetComponent(typeof(Game.GameObjectAttach))
		local bundle, asset = ResPath.GetUIEffect(tab_type_data.effect_res_id)
		attach_obj.BundleName = bundle
		attach_obj.AssetName = asset
		self.node_list.effect:SetActive(false)
		self.node_list.effect:SetActive(true)
	end

	-- self.node_list.normal_text.text.text = self.data.name
	-- self.node_list.select_text.text.text = self.data.name
	self.node_list["remind"]:SetActive(self.data.is_remind)

	self.view:SetActive(true)
end

function SupremeFieldsBigTypeToggleRender:SetAccordionElementState(is_on)
	self.view.accordion_element.isOn = is_on
end

----------------------------------------------------------------------------
-- 小类型toggle
-- SupremeFieldsListItemRender
----------------------------------------------------------------------------
SupremeFieldsListItemRender = SupremeFieldsListItemRender or BaseClass(BaseRender)
function SupremeFieldsListItemRender:__init()
end

function SupremeFieldsListItemRender:__delete()

end

function SupremeFieldsListItemRender:OnFlush()
	if self.data == nil then
		self.view:SetActive(false)
		return
	end

	local bundle, asset = ResPath.GetSupremeFields(self.data.cfg.item_bg)
	self.node_list.normal.image:LoadSpriteAsync(bundle, asset, function()
		self.node_list.normal.image:SetNativeSize()
	end)

	-- local bundle, asset = ResPath.GetSupremeFieldsIcon(self.data.type)
	-- self.node_list.icon.image:LoadSpriteAsync(bundle, asset, function()
	-- 	self.node_list.icon.image:SetNativeSize()
	-- end)

	self.node_list["normal_text"].text.text = self.data.name
	-- self.node_list["select_text"].text.text = self.data.name
	local level_str = self.data.lv > 0 and string.format(Language.SupremeFields.StarNumText2, self.data.lv) or Language.SupremeFields.NoActive
	self.node_list["normal_text2"].text.text = level_str
	-- self.node_list["select_text2"].text.text = level_str
	self.node_list["remind"]:SetActive(self.data.is_remind)

	local is_use = SupremeFieldsWGData.Instance:TestFootLightUse(self.data.type)
	self.node_list.put_on:SetActive(is_use) --幻化标识
	self.view:SetActive(true)
end

function SupremeFieldsListItemRender:OnSelectSuitChange(index)
	if self.data == nil then
		return
	end

	local is_select = index == self.index
	-- self.node_list["normal"]:SetActive(not is_select)
	self.node_list["select"]:SetActive(is_select)
end

------------------------------------孔位item列表------------------------
FieldsSlotRender = FieldsSlotRender or BaseClass(BaseRender)

function FieldsSlotRender:LoadCallBack()
	self.node_list.slot.toggle:AddClickListener(BindTool.Bind(self.OnClickCell, self))
end

function FieldsSlotRender:OnFlush()
	if nil == self.data then return end
	local is_red = SupremeFieldsWGData.Instance:TestFootLightSlotRedPoint(self.data.type, self.data.slot)
	local _, is_open = SupremeFieldsWGData.Instance:TestFootLightSlotOpen(self.data.type, self.data.slot)

	self.node_list.btn_act_red:SetActive(is_red)
	-- self.node_list.add:SetActive(not is_open)
	self.node_list.star_root:SetActive(is_open)
	self.node_list.name_bg:SetActive(is_open)
	-- self.node_list.ph_icon:SetActive(is_open)

	-- local bundle, asset = ItemWGData.Instance:GetTipsItemIcon(self.data.item_id)
	local bundle, asset = ResPath.GetSupremeFields(self.data.show_icon)
	self.node_list.ph_icon.image:LoadSprite(bundle, asset, function()
		self.node_list.ph_icon.image:SetNativeSize()
	end)

	local name_str = ""
	
	if not is_open then
		name_str = Language.Common.NoActivate
	else
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		name_str = item_cfg and item_cfg.name or ""
	end

	self.node_list.name.text.text = name_str

	local name_bg_asset = "a3_xkzj_mc_0"
	if is_open then
		name_bg_asset = self.data.name_bg
	end

	local name_bundle, name_asset = ResPath.GetSupremeFields(name_bg_asset)
	self.node_list.name_bg.image:LoadSprite(name_bundle, name_asset, function()
		self.node_list.name_bg.image:SetNativeSize()
	end)

	-- local color = item_cfg.color
	-- local bundle, asset = ResPath.GetCommonImages("a3_sp_wpk_bg_" .. color)
	-- self.node_list.icon_bg.image:LoadSprite(bundle, asset, function()
	-- 	self.node_list.icon_bg.image:SetNativeSize()
	-- end)

	if is_open then
		self:FlushStar()
	end
end

function FieldsSlotRender:OnClickCell()
	if nil == self.data then
		return
	end

	self.select_call_back(self.index, self.data)
end

function FieldsSlotRender:SetToggleSelect()
	self.node_list.slot.toggle.isOn = true
end

function FieldsSlotRender:FlushStar()
	local lv, is_open = SupremeFieldsWGData.Instance:TestFootLightSlotOpen(self.data.type, self.data.slot)
	lv = is_open and lv or 0
	self.node_list.star_num.text.text = string.format(Language.SupremeFields.StarNumText, lv)

	for i = 1, 5 do
		local cur_star_res_list = GetStarImgResByStar(lv)
		for i = 1, 5 do
			self.node_list["supreme_fields_star" .. i].image:LoadSprite(ResPath.GetCommonImages(cur_star_res_list[i]))
		end
	end
end

SAddAttrRender = SAddAttrRender or BaseClass(BaseRender)
function SAddAttrRender:__init()

end

function SAddAttrRender:__delete()
	self:KillArrowTween()
end

function SAddAttrRender:KillArrowTween()
	if self.arrow_tweener then
		self.arrow_tweener:Kill()
		self.arrow_tweener = nil
	end
end

function SAddAttrRender:LoadCallBack()
	self:KillArrowTween()
	local tween_time = 0.8
	local node = self.node_list.arrow
	if node then
		RectTransform.SetAnchoredPositionXY(node.rect, node.rect.anchoredPosition.x, -18)
		self.arrow_tweener = node.rect:DOAnchorPosY(-12, tween_time)
		self.arrow_tweener:SetEase(DG.Tweening.Ease.InOutSine)
		self.arrow_tweener:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
	end
	self.view:SetActive(false)
end

function SAddAttrRender:OnFlush()
	if IsEmptyTable(self.data) then
		self.view:SetActive(false)
		return
	end

	self.node_list.arrow:SetActive(self.data.add_value > 0)
	self.node_list.add_value:SetActive(self.data.add_value > 0)
	self.node_list.attr_name.text.text = self.data.attr_str
	self.node_list.attr_value.text.text = self.data.attr_value .. "%"
	self.node_list.add_value.text.text = self.data.add_value .. "%"
	self.view:SetActive(true)
end
