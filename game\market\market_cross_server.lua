local CrossServerMarketTips = {
	Guild_Battle = 1,		--仙盟战跨服拍卖
}

function MarketView:CrossServerMarketReleaseCallBack()
	if self.cross_server_aiction_item_list then
		self.cross_server_aiction_item_list:DeleteMe()
		self.cross_server_aiction_item_list = nil
	end

	self.cs_search_name = nil
	self.cur_cs_big_type = nil
	self.cur_cs_sub_type = nil
	self.cur_cs_data_list = nil
	self.old_cs_show_info = nil

	if self.cross_server_tab_list ~= nil then
		self.cross_server_tab_list:DeleteMe()
		self.cross_server_tab_list = nil
	end
end

function MarketView:InitCSParam()
	self.cs_search_name = ""
	self.cur_cs_big_type = nil
	self.cur_cs_sub_type = nil
	self.cur_cs_data_list = nil
	self.old_cs_show_info = nil--用作保存上一次刷新数据的位置信息
end

function MarketView:CrossServerMarketLoadCallBack()
	self:InitCSParam()

	self.cross_server_aiction_item_list = AsyncListView.New(MarketAuctionCommonRender, self.node_list.cs_item_list)

	self.node_list.cs_all_select_btn.button:AddClickListener(BindTool.Bind(self.OnClickCSAllTypeBtn, self))
	self.node_list.cs_search_btn.button:AddClickListener(BindTool.Bind(self.OnClickCSSearchBtn, self))
	self.node_list.cs_search_input.input_field.onSelect:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, true, MarketViewIndex.KFPM))
	self.node_list.cs_search_input.input_field.onEndEdit:AddListener(BindTool.Bind(self.OnInputFieldClickOrEndEdit, self, false, MarketViewIndex.KFPM))

	--tab页签的箭头显示.
	self.node_list.market_cross_server_list.scroller.scrollerEndScrolled = BindTool.Bind(self.CrossServerScrollerEndScrolled, self)

	self.cross_server_tab_list = AsyncListView.New(MarketAuctionTabRender, self.node_list["market_cross_server_list"])
	self.cross_server_tab_list:SetSelectCallBack(BindTool.Bind1(self.OnClickCrossServerTabCallBack, self))
	self.cross_server_tab_list:SetDefaultSelectIndex(nil)
	self.cross_server_tab_list:SetDataList(MarketWGData.Instance:GetAuctionTabList(AUCTION_INFO_BIG_TYPE.CrossServer))

	self:OnClickCSAllTypeBtn()
end

--tab页签的箭头显示.
function MarketView:CrossServerScrollerEndScrolled()
	local val = self.node_list.market_cross_server_list.scroll_rect.horizontalNormalizedPosition
	self.node_list.market_cross_server_l_img:SetActive(val ~= 0 and val > 0.1)
	self.node_list.market_cross_server_r_img:SetActive(val ~= 0 and val < 0.9)
end

function MarketView:OnClickCrossServerTabCallBack(item)
	if IsEmptyTable(item.data) then
		return
	end

	self.node_list.cs_all_hl:SetActive(false)
	self:OnClickCrossServerLeftBtnCallBack(item.data.type, item.data.sub_type)
end

function MarketView:CrossServerMarketShowIndexCallBack()

end

--设置首次打开的跳转
function MarketView:CrossServerMarketJumpIndexShow(big_type, sub_type)

end

function MarketView:CrossServerMarketOnFlush(param_t)
	if self.cur_cs_big_type and self.cur_cs_sub_type then
		self:OnClickCrossServerLeftBtnCallBack(self.cur_cs_big_type, self.cur_cs_sub_type, true)
	else
		self:OnClickCSAllTypeBtn(true)
	end
end

function MarketView:OnClickCrossServerLeftBtnCallBack(big_type, sub_type, force_flush)
	if self.cs_search_name == "" and not force_flush and self.cur_cs_big_type and self.cur_cs_sub_type then
		if self.cur_cs_big_type == big_type and self.cur_cs_sub_type == sub_type then
			return
		end
	end

	self.cur_cs_big_type = big_type
	self.cur_cs_sub_type = sub_type

	local show_acution_list_type = AUCTION_TYPE.Guild_Battle
	--根据类型取得竞拍物品展示配置
	self.cur_cs_data_list = MarketWGData.Instance:GetNewAuctionItemInfoByType(show_acution_list_type, sub_type)
	if not force_flush then-- 普通点击页签,清空搜索状态以及搜索栏
		self.node_list["cs_search_input"].input_field.text = ""
		self.cs_search_name = ""
	end

	if self.cs_search_name ~= "" then-- 还在搜索列表中
		self:OnClickCSSearchBtn(true)
	else
		self:CSOnFlushGoodsList(force_flush)
	end
end

-- 点击全部按钮
function MarketView:OnClickCSAllTypeBtn(force_flush)
	if self.cs_search_name == "" and not force_flush and self.node_list.cs_all_hl.gameObject.activeSelf then
		return
	end

	self.cur_cs_big_type = nil
	self.cur_cs_sub_type = nil

	self.node_list.cs_all_hl:SetActive(true)
	self.cur_cs_data_list = MarketWGData.Instance:GetAllAuctionByType(AUCTION_TYPE.Guild_Battle)

	if not force_flush then-- 普通点击页签,清空搜索状态以及搜索栏
		self.node_list["cs_search_input"].input_field.text = ""
		self.cs_search_name = ""
	end

	if self.cs_search_name ~= "" then-- 还在搜索列表中
		self:OnClickCSSearchBtn(true)
	else
		self:CSOnFlushGoodsList(force_flush)
	end

	self.node_list.cs_buttom_tips.text.text = Language.Market.Auction_Buttom_Tips_3

	self.cross_server_tab_list:CancelSelect()
end

-- 搜索按钮
--flush_flag:刷新标志,用于搜索后列表信息变更刷新
function MarketView:OnClickCSSearchBtn(flush_flag)
	if IsEmptyTable(self.cur_cs_data_list) then
		return
	end

	if not flush_flag then
		local new_search_name = self.node_list["cs_search_input"].input_field.text
		if self.cs_search_name == new_search_name then
			return
		else--二次搜索:根据当前所在的 左页签类型 进行搜索
			if self.cur_cs_big_type and self.cur_cs_sub_type then
				--根据类型取得竞拍物品展示配置
				self.cur_cs_data_list = MarketWGData.Instance:GetNewAuctionItemInfoByType(AUCTION_TYPE.Guild_Battle, self.cur_cs_sub_type)
			else
				self.cur_cs_data_list = MarketWGData.Instance:GetAllAuctionByType(AUCTION_TYPE.Guild_Battle)
			end
		end

		self.cs_search_name = new_search_name
		if self.cs_search_name == "" then
			self:CrossServerMarketOnFlush()
			return
		end
	end

	local temp_list = {}
	for k, v in pairs(self.cur_cs_data_list) do
		if self:CSSearchGoodsInfo(v.item_id) then
			table.insert(temp_list, v)
		end
	end

	self.cur_cs_data_list = temp_list
	self:CSOnFlushGoodsList(flush_flag)
end

function MarketView:CSSearchGoodsInfo(item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	if item_cfg == nil then
		return false
	end

	if self.cs_search_name and self.cs_search_name ~= "" then
		if not string.find(item_cfg.name, self.cs_search_name) then
			return false
		end
	end
	return true
end

--刷新跨国拍卖商品列表
--specil_flag 特殊需求:不是点击页签刷新时,列表数据有变化,按照原来的列表顺序刷新
function MarketView:CSOnFlushGoodsList(specil_flag)
	if self.cur_cs_data_list and not IsEmptyTable(self.cur_cs_data_list) then
		self.node_list.cs_empty_tips:SetActive(false)
		self.node_list.cs_item_list:SetActive(true)

		if specil_flag and not IsEmptyTable(self.old_cs_show_info) then
			self.cur_cs_data_list = MarketWGData.Instance:SortAuctionInfoListByOld(self.cur_cs_data_list, self.old_cs_show_info)
		else--点击页签切换时,才按时间重新排序
			-- table.sort(self.cur_cs_data_list, SortTools.KeyLowerSorters("end_time", "index"))
			table.sort(self.cur_cs_data_list, MarketWGData.Instance:AuctionDataListSort())
		end

		self.old_cs_show_info = self.cur_cs_data_list

		self.cross_server_aiction_item_list:SetDataList(self.cur_cs_data_list)
		if not specil_flag then
			self.cross_server_aiction_item_list:JumpToTop()
		end
	else
		self.node_list.cs_empty_tips:SetActive(true)
		self.node_list.cs_item_list:SetActive(false)
	end
end
