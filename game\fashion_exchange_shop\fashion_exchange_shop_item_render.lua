FashionExchangeShopItemRender = FashionExchangeShopItemRender or BaseClass(BaseRender)

function FashionExchangeShopItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function FashionExchangeShopItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list["item"])
	-- self.item_cell:SetIsShowTips(false)
end

function FashionExchangeShopItemRender:OnFlush()
	if nil == self.data then
		return
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	if nil == item_cfg then
		print_error("没有配置 ", self.data.item_id)
		return
	end

	self.item_cell:SetData({ item_id = self.data.item_id, is_bind = self.data.is_bind })
	self.item_cell:SetDefaultEff(true)
	self.item_cell:SetBindIconVisible(false)
	self.node_list.item_name.text.text = ToColorStr(item_cfg.name, self.data.rare_type == 1 and COLOR3B.C13 or COLOR3B.C7)

	self.node_list["pz_img"]:SetActive(self.data.jiaobiao_icon > 0)
	if self.data.jiaobiao_icon > 0 then
		local bundle, asset = ResPath.GetCommonImages("a3_ty_pz" .. self.data.jiaobiao_icon)
		self.node_list.pz_img.image:LoadSprite(bundle, asset)
	end

	local bg_bundle, bg_asset = ResPath.GetFashionExchangeShopImg("a3_szdh_bg_" .. self.data.rare_type)
	self.node_list["bg"].image:LoadSprite(bg_bundle, bg_asset, function()
		self.node_list["bg"].image:SetNativeSize()
	end)

	self.item_cell:SetRightBottomTextVisible(false)

	local list = FashionExchangeShopWGData.Instance:ExplainComposeStr(self.data.seq)
	local is_can_cell, sell_str = FashionExchangeShopWGData.Instance:IsCanCell(self.data.seq)
	self.node_list["buy_limit_text"]:SetActive(is_can_cell)

	if IsEmptyTable(list) then
		self.node_list["buy_limit_text"].text.text = Language.FashionExchangeShop.NoLimitBuy
	else
		self.node_list["buy_limit_text"].text.text = list.str
	end

	local is_vip_limit, vip_limit_str, is_can_buy = FashionExchangeShopWGData.Instance:IsVipLimit(self.data.seq)
	self.node_list["lock"]:SetActive(not is_can_buy)
	self.node_list["limit_text"]:SetActive(not is_can_buy)
	self.node_list["gold"]:SetActive(is_can_buy)
	if not is_can_buy and is_vip_limit and vip_limit_str then
		self.node_list["limit_text"].text.text = vip_limit_str
	else
		local is_limit = FashionExchangeShopWGData.Instance:IsLimit(self.data.seq)
		is_limit = is_limit and IsEmptyTable(list)
		self.node_list["gold"]:SetActive(is_can_cell or not is_limit)
		self.node_list["lock"]:SetActive(not is_can_cell or is_limit)
		self.node_list["limit_text"]:SetActive(not is_can_cell or is_limit)
		if not is_can_cell or is_limit then
			self.node_list["limit_text"].text.text = sell_str
		end
	end

	self:FlushPrice()
end

function FashionExchangeShopItemRender:FlushPrice()
	local money_cfg = FashionExchangeShopWGData.Instance:GetMoneyCfg()
	local item_id = money_cfg[self.data.price_type] and money_cfg[self.data.price_type].item_id or 0
	local bundel, asset = ItemWGData.Instance:GetTipsItemIcon(item_id)
	local scale = 0.5
	local vector3 = Vector3(scale, scale, scale)
	local price = self.data.price
	if bundel and asset then
		self.node_list["price_icon"].image:LoadSprite(bundel, asset, function()
			self.node_list["price_icon"].image:SetNativeSize()
			self.node_list["price_icon"].transform.localScale = vector3
		end)
	end

	self.node_list["price_text"].text.text = ToColorStr(price, self.data.rare_type == 1 and COLOR3B.C21 or COLOR3B.C1)

	self.node_list["old_price_text"]:SetActive(0 ~= self.data.old_price)
	if 0 ~= self.data.old_price then
		self.node_list["old_price_text"].text.text = ToColorStr(self.data.old_price, self.data.rare_type == 1 and COLOR3B.C21 or COLOR3B.C1)
	end
end

function FashionExchangeShopItemRender:OnSelectChange(is_select)
	self.node_list.select_img:SetActive(is_select)
end
