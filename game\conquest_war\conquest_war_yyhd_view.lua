-- 永夜幻都 (原锁妖塔)
function ConquestWarView:LoadIndexCallBackYYHDView()
	self:FlushFGBEnterBtnState()

	if not self.yyhd_reward_list then
		self.yyhd_reward_list = AsyncListView.New(ItemCell, self.node_list.yyhd_reward_list)
		self.yyhd_reward_list:SetStartZeroIndex(true)
	end

	--local reward_cfg = KuafuHonorhallWGData.Instance:GetFGBWinRewardList()
	--self.yyhd_reward_list:SetDataList(reward_cfg)
	XUI.AddClickEventListener(self.node_list.go_yyhd_btn, BindTool.Bind1(self.OnClickGoToYYHD, self))
	XUI.AddClickEventListener(self.node_list.btn_yyhd_reward_show, BindTool.Bind(self.OnClickYYHDRewardShow, self))
	XUI.AddClickEventListener(self.node_list.btn_yyhd_shop_show, BindTool.Bind(self.OnClickYYHDShopShow, self))

	self.yyhd_activity_change_callback = BindTool.Bind(self.OnYYHDActChange, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.yyhd_activity_change_callback)
end

function ConquestWarView:ReleaseYYHDView()
	if self.yyhd_reward_list then
		self.yyhd_reward_list:DeleteMe()
		self.yyhd_reward_list = nil
	end
end

function ConquestWarView:YYHDShowIndexCallBack()

end

function ConquestWarView:OnClickYYHDRewardShow()
	KuafuHonorhallWGCtrl.Instance:OpenRewardView()
end

function ConquestWarView:OnClickYYHDShopShow()
	KuafuHonorhallWGCtrl.Instance:OpenBuyPreview()
end

function ConquestWarView:OnFlushYYHDView(param_t, index)
	local data = ConquestWarWGData.Instance:GetConquestWarActivityInfoById(ACTIVITY_TYPE.KF_HONORHALLS)
	if IsEmptyTable(data) then
		return
	end

	self.yyhd_reward_list:SetDataList(data.activity_item)
	self.node_list["txt_yyhd_title"].text.text = data.activity_title
	self.node_list["txt_yyhd_open_time"].text.text = string.format(Language.FlagGrabbingBattlefield.ActivityTime1, data.time_1, data.time_2)
	self.node_list["txt_yyhd_desc"].text.text = data.activity_illustrate

	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if not activity_info then return end

	self:FlushYYHDBtnState(activity_info.status)
end

function ConquestWarView:OnClickGoToYYHD()
	local info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
	if not info then return end

	if info.status ~= ACTIVITY_STATUS.OPEN and info.status ~= ACTIVITY_STATUS.STANDY then
		SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Common.NotOpenAct))
		return
	end

	-- local open_level = KuafuYeZhanWangChengWGData.Instance:GetLimitLevel()
	-- local role_level = RoleWGData.Instance:GetRoleLevel()
	-- if role_level < open_level then
	-- 	SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.FlagGrabbingBattlefield.LevelLimitJoin, RoleWGData.GetLevelString2(open_level)))
	-- 	return
	-- end

	ActivityWGCtrl.Instance:AutoQuickJoinActByType(ACTIVITY_TYPE.KF_HONORHALLS)
end

function ConquestWarView:OnYYHDActChange(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KF_HONORHALLS and self:IsOpen() then
		self:FlushYYHDBtnState(status)
	end
end

function ConquestWarView:FlushYYHDBtnState(status)
	if status == ACTIVITY_STATUS.STANDY then
		self.node_list["go_yyhd_btn"]:CustomSetActive(true)
		self.node_list["go_yyhd_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Standy
	elseif status == ACTIVITY_STATUS.OPEN then
		self.node_list["go_yyhd_btn"]:CustomSetActive(true)
		self.node_list["go_yyhd_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.Doing
	else
		self.node_list["go_yyhd_btn"]:CustomSetActive(false)
		self.node_list["go_yyhd_wait_flag_text"].text.text = Language.FlagGrabbingBattlefield.NotOpenDesc
	end
end
