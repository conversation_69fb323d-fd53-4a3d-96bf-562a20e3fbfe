
XianQiRankLogView = XianQiRankLogView or BaseClass(SafeBaseView)

function XianQiRankLogView:__init()
	self:SetMaskBg(true)
	self.view_layer = UiLayer.Pop
	self.view_name = "XianQiRankLogView"
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_commmon_second_panel", {sizeDelta = Vector2(692, 514)})
	local assetbundle = "uis/view/act_xianqi_jiefeng_ui_prefab"
	self:AddViewResource(0, assetbundle, "layout_longhun_rank_log")
end

function XianQiRankLogView:ReleaseCallBack()
	if self.rank_log_list then
		self.rank_log_list:DeleteMe()
		self.rank_log_list = nil
	end
end

function XianQiRankLogView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.XianQiJieFengAct.LongHunRankLog_Title
	-- local end_time = ActXianQiJieFengWGData.Instance:GetActivityInValidTime(TabIndex.xianqi_jiefeng_longhun_rank)
	-- self.node_list["rank_time"].text.text = string.format(Language.XianQiJieFengAct.LongHunRankLog_End_Time, TimeUtil.FormatMDHMS(end_time))
	self.rank_log_list = AsyncListView.New(XianQiRankLogItemRender, self.node_list["ph_item_list"])

	self:FlushView()
end

function XianQiRankLogView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
			self:FlushView()
		end
	end
end

function XianQiRankLogView:FlushView()
	local data_list = ActXianQiJieFengWGData.Instance:GetXianQiRankLogInfo()
	if not IsEmptyTable(data_list) then
		self.node_list["ph_item_list"]:SetActive(true)
		self.node_list["not_rank_info"]:SetActive(false)
		self.rank_log_list:SetDataList(data_list)
	else
		self.node_list["ph_item_list"]:SetActive(false)
		self.node_list["not_rank_info"]:SetActive(true)
	end

	local my_rank_info = ActXianQiJieFengWGData.Instance:GetXianQiRankRoleInfo()
	local my_rank_num = my_rank_info.rank or 0
	local my_longhun_power = my_rank_info.zhanli or 0

	if my_rank_num <= 0 then
		self.node_list["my_rank"].text.text = Language.XianQiJieFengAct.LongHunRank_No_Rank
	else
		self.node_list["my_rank"].text.text = string.format(Language.XianQiJieFengAct.LongHunRankStr, my_rank_num)
	end

	--我的战力
	self.node_list["tscao_value"].text.text = string.format(Language.XianQiJieFengAct.LongHunRankDesc, my_longhun_power)
end

----------------------------------------------------------------------------------------------------

XianQiRankLogItemRender = XianQiRankLogItemRender or BaseClass(BaseRender)
function XianQiRankLogItemRender:OnFlush()
	if not self.data then
		return
	end

	local is_top_3 = self.data.rand_id <= 3

	--排名名次信息(4-+++)
	if self.data.rand_id > 3 then
		self.node_list["rank"].text.text = string.format(Language.XianQiJieFengAct.RankStr, self.data.rand_id)
	else
		self.node_list["rank"].text.text = ""
	end

	--玩家名称
	self.node_list["player_name"].text.text = self.data.name
	self.node_list["player_name"].text.enabled = not is_top_3

	self.node_list["player_name2"].text.text = self.data.name
	self.node_list["player_name2"].text.enabled = is_top_3
	--玩家战力
	self.node_list["player_power"].text.text = self.data.zhanli
	self.node_list["player_power"].text.enabled = not is_top_3

	self.node_list["player_power2"].text.text = self.data.zhanli
	self.node_list["player_power2"].text.enabled = is_top_3
	self.node_list["img_rank"].image.enabled = is_top_3

	if not is_top_3 then
		-- if self.data.rand_id % 2 == 0 then
		-- 	self.node_list.fengge_bg.image:LoadSprite(ResPath.GetCommonImages("a1_ty_pmd4"))
		-- else
		-- 	self.node_list.fengge_bg.image:LoadSprite(ResPath.GetCommonImages("a1_ty_pmd5"))
		-- end
		self.node_list.rank.text.text = self.data.rand_id
	else
		self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. self.data.rand_id))
		-- self.node_list.fengge_bg.image:LoadSprite(ResPath.GetCommonImages("a1_ty_pmd" .. self.data.rand_id))
		self.node_list.rank.text.text = ""
	end

end