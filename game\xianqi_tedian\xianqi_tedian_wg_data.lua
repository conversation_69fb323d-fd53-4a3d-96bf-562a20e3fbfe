XianQiTeDianWGData = XianQiTeDianWGData or BaseClass()

MAX_XIAN_QI_ZHAO_HUAN_SUBACT_ID = 6   --仙器特典子活动最大id
MAX_XIAN_QI_ZHAO_HUAN_SUBACT_SALE_PRODUCT_NUM = 64 --仙器特典每次开启每个子活动最大售卖商品个数

function XianQiTeDianWGData:__init()
    if XianQiTeDianWGData.Instance then
        error("[XianQiTeDianWGData]:Attempt to create singleton twice!")
        return
    end
    XianQiTeDianWGData.Instance = self
    self:InitParam()
    self:InitCfgData()
end

function XianQiTeDianWGData:__delete()
    
    XianQiTeDianWGData.Instance = nil
end

function XianQiTeDianWGData:InitParam()
	self.te_dian_status = 0				-- 特典是否开启中 0-未开启 1-开启中
	self.te_dian_open_times = 0			-- 开启的次数 （对特典实际开启次数做了循环步长 取余操作得到的）
	self.te_dian_end_time = 0			-- 本次特典的结束时间戳or下一次特典开启时间戳
	self.te_dian_subact_info = {}		-- 子活动售卖状态
	self.shilian_show_id_list = nil
end

function XianQiTeDianWGData:InitCfgData()
	local zhenlian_config = ConfigManager.Instance:GetAutoConfig("shenjibailian_tedian_auto")

	self.other_cfg = zhenlian_config.other and zhenlian_config.other[1]

	self.client_sub_act_map = ListToMap(zhenlian_config.client_sub_act, "cycle", "act_id")

	self.probability_up_show = ListToMapList(zhenlian_config.probability_up_show, "cycle")
	self.subactivity_relation_cfg = zhenlian_config.subactivity_relation
	self.item_random_desc_cfg = zhenlian_config.item_random_desc
	-- 构造周期子活动数据
	self:MakeDataCycleSubActivity(zhenlian_config.cycle_subactivity)
	-- 构造子活动售卖数据
	self.sub_act_sale_map = ListToMapList(zhenlian_config.subactivity_sale, "cycle", "subactivity_id")

	local shenji_system_open_yugao_cfg = ConfigManager.Instance:GetAutoConfig("shenjisystemopenyugaocfg_auto")
	self.btn_resource_cfg = ListToMap(shenji_system_open_yugao_cfg.btn_resource_cfg, "special_sale_id")
	self.special_sale_cfg = ListToMap(shenji_system_open_yugao_cfg.special_sale, "special_sale_id")
	self.special_sale_other_cfg = shenji_system_open_yugao_cfg.other[1]

end

function XianQiTeDianWGData:MakeDataCycleSubActivity(cfg_list)
	local map_list = {}
	for i=1,#cfg_list do
		local split_tab = Split(cfg_list[i].start_subactivitys, "|")
		local temp_list = {}
		for j=1,#split_tab do
			temp_list[j] = tonumber(split_tab[j])
		end
		map_list[cfg_list[i].cycle] = temp_list
	end
    self.cycle_subactivity_map = map_list
end

---[[ 后端发的数据
function XianQiTeDianWGData:SetXianQiTeDianInfo(protocol)
	self.te_dian_status = protocol.te_dian_status
    self.te_dian_open_times = protocol.open_times
    self.te_dian_end_time = protocol.end_time
    self.te_dian_subact_info = protocol.te_dian_subact_info
	--print_error("售卖状态", self.te_dian_subact_info)
end

-- 获取是否开启特典
function XianQiTeDianWGData:GetIsOpenTeDian()
	return self.te_dian_status == 1
end

-- 获取开启次数
function XianQiTeDianWGData:GetCycle()
	return self.te_dian_open_times
end

-- 获取本次特典的结束时间戳or下一次特典开启时间戳
function XianQiTeDianWGData:GetTeDianEndTimeStamp()
    return self.te_dian_end_time
end

-- 获取子活动售卖状态
function XianQiTeDianWGData:GetSubActSaleInfo(act_id, product_id)
	if self.te_dian_subact_info[act_id] then
		return self.te_dian_subact_info[act_id][product_id]
	end
end
--]]

-- 获取其它配置
function XianQiTeDianWGData:GetOtherCfg(key)
	if self.other_cfg and key then
		return self.other_cfg[key]
	end
end

-- 获取十连展示物品id列表
function XianQiTeDianWGData:GetShilianShowIdList()
	if not self.shilian_show_id_list then
		local show_cfg = XianQiTeDianWGData.Instance:GetOtherCfg("show")
		if not show_cfg then
			return
		end
		local id_list = Split(show_cfg, ",")
		local temp_list = {}
		for i=1,#id_list do
			temp_list[i] = tonumber(id_list[i])
		end
		self.shilian_show_id_list = temp_list
	end
	return self.shilian_show_id_list
end

-- 获取特典概率提升cfg
function XianQiTeDianWGData:GetProUpCfgList()
	local cycle = self:GetCycle()
	if cycle and self.probability_up_show and self.probability_up_show[cycle] then
		return self.probability_up_show[cycle]
	end
	return {}
end

-- 获取子活动售卖cfg_list
function XianQiTeDianWGData:GetActSaleCfg(act_id)
	--local cycle = self:GetCycle() 需求变动 不要次数了 直接拿第一个
	if self.sub_act_sale_map[1] then
		return self.sub_act_sale_map[1][act_id]
	end
end

--获取子活动关系list
function XianQiTeDianWGData:GetActRelationCfg(act_id)
	return self.subactivity_relation_cfg[act_id]
end

-- 获取可展示的售卖cfg_list
function XianQiTeDianWGData:GetActSaleItemListCfg()
	local cfg_list = self.sub_act_sale_map[1]
	local show_list = self:GetShowActIdList()
	if show_list == nil then return end
	
	local index_list = {}
	local data_list = {}
	for k, v in pairs(show_list) do
		index_list[v] = v
	end
	--print_error(index_list)
	for k, v in pairs(cfg_list) do
		if k == index_list[k] then
			table.insert(data_list, v)
		end
	end
	return data_list
end

--获取客户端显示活动配置
function XianQiTeDianWGData:GetClientShowCfg(act_id)
    local cycle = self:GetCycle()
    if self.client_sub_act_map[cycle] then
    	return self.client_sub_act_map[cycle][act_id]
    end
end

-- 获取显示的活动列表(买完领完后就移除)
function XianQiTeDianWGData:GetShowActIdList()
	local cycle = self:GetCycle()
	local act_list = self.cycle_subactivity_map[cycle]
	local show_list = {}
	if act_list then
		local is_show = true
		for i,act_id in ipairs(act_list) do
			if act_id == XianQiTeDianSubActId.ProbabilityUp then
				--is_show = self:GetIsOpenTeDian() --不要第一个了
				is_show = false
			elseif act_id == XianQiTeDianSubActId.ShiLian2 then
				if self:IsAllBuyAndGetReward(XianQiTeDianSubActId.ShiLian1) then
					is_show = true
				else
					is_show = false
				end
			elseif act_id == XianQiTeDianSubActId.ShiLian1 then
				is_show = not self:IsAllBuyAndGetReward(act_id)
			else
				is_show = true
			end
			if is_show then
				show_list[#show_list + 1] = act_id
			end
		end
	end

	return show_list
end

-- 该活动的所有商品都买完了且领取了奖励
function XianQiTeDianWGData:IsAllBuyAndGetReward(act_id)
	local cfg_list = self:GetActSaleCfg(act_id)
	if cfg_list then
		local act_info = nil
		for i=1,#cfg_list do
			act_info = self:GetSubActSaleInfo(act_id, cfg_list[i].product_id)
			if act_info and (act_info.status ~= YuanShenSaleSubActSaleStatus.HasBuyAndFetched or act_info.buy_num < cfg_list[i].limit_buy_times) then --没买完的
				return false
			end
		end
		return true
	end
end

-- 检测某个特典活动是否开启(买完领完后就关闭)
function XianQiTeDianWGData:CheckSubActIsOpen(act_id)
	local cycle = self:GetCycle()
	local act_list = self.cycle_subactivity_map[cycle]
	if IsEmptyTable(act_list) then
		return false
	end
	for i,id in ipairs(act_list) do
		if id == act_id and not self:IsAllBuyAndGetReward(act_id) then
			return true
		end
	end
end

-- 获取当前十连
function XianQiTeDianWGData:GetCurShiLian()
	if self:CheckSubActIsOpen(XianQiTeDianSubActId.ShiLian1) then
		return XianQiTeDianSubActId.ShiLian1
	else
		return XianQiTeDianSubActId.ShiLian2
	end
end

-- 获取当前甄选
function XianQiTeDianWGData:GetCurZhenXuan()
	if self:CheckSubActIsOpen(XianQiTeDianSubActId.ZhenXuan1) then
		return XianQiTeDianSubActId.ZhenXuan1
	else
		return XianQiTeDianSubActId.ZhenXuan2
	end
end

function XianQiTeDianWGData:GetLingJiaBtnShowCfg(spc_id)
	return self.btn_resource_cfg and self.btn_resource_cfg[spc_id]
end

function XianQiTeDianWGData:GetLingJiaSaleCfg(spc_id)
	return self.special_sale_cfg and self.special_sale_cfg[spc_id]
end

function XianQiTeDianWGData:GetLingJiaSaleOtherCfg()
	return self.special_sale_other_cfg
end


--仙器特典十连红点判断
function XianQiTeDianWGData:GetXianQITeDianShiLianRemind()
	local cur_shilian_act_id = self:GetCurShiLian()
	local sale_info = self:GetSubActSaleInfo(cur_shilian_act_id, 1)
    if sale_info and sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch then
    	return 1
    end
    return 0
end

--仙器特典珍选红点判断
function XianQiTeDianWGData:GetXianQITeDianZhenXuanRemind()
	local cur_zhenxuan_act_id = self:GetCurZhenXuan()
	local sale_info = self:GetSubActSaleInfo(cur_zhenxuan_act_id, 1)
    if sale_info and sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch then
    	return 1
    end
    return 0
end

function XianQiTeDianWGData:GetRandomGaiLvinfo()
	return self.item_random_desc_cfg
end