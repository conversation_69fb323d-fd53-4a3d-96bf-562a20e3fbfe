CangJinShopView = CangJinShopView or BaseClass(SafeBaseView)

function CangJinShopView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Half

    self:SetMaskBg()
    -- self:AddViewResource(0, "uis/view/cangjin_shop_prefab", "layout_shop_exchange_bg")
    self:AddViewResource(0, "uis/view/cangjin_shop_prefab", "layout_cangjin_shop_view")
end

function CangJinShopView:LoadCallBack()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["model_display"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)

        self:FlushModel()
    end

    if not self.remind_change then
        self.remind_change = BindTool.Bind(self.RemindChangeCallBack, self)
        RemindManager.Instance:Bind(self.remind_change, RemindName.CangJinExchange)
    end

    local item_cfg = ItemWGData.Instance:GetItemConfig(CangJinShopWGData.Instance:GetVirtualItemRechargeScore())
    if item_cfg then
        self.node_list.score_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    XUI.AddClickEventListener(self.node_list["btn_open_shop"], BindTool.Bind(self.OnClickOpenShop, self))
    XUI.AddClickEventListener(self.node_list["recharge_btn"], BindTool.Bind(self.OnClickRechargeBtn, self))
    XUI.AddClickEventListener(self.node_list.score_icon, BindTool.Bind(self.OnClickScoreIcon, self))
end

function CangJinShopView:ReleaseCallBack()
    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

    if self.remind_change then
        RemindManager.Instance:UnBind(self.remind_change)
        self.remind_change = nil
    end
end

function CangJinShopView:ShowIndexCallBack()

end

function CangJinShopView:OnFlush()
    -- local cur_score = CangJinShopWGData.Instance:GetCurScore()
    -- self.node_list["cur_score"].text.text = string.format(Language.CangJinShopView.CurScore, cur_score)
    self.node_list.desc_text.text.text = Language.CangJinShopView.DescStr

    local cur_score = CangJinShopWGData.Instance:GetCurScore()
    self.node_list["cur_score"].text.text = CommonDataManager.ConverExpByThousand(cur_score)
end

function CangJinShopView:OnClickOpenShop()
    ViewManager.Instance:Open(GuideModuleName.CangJinExchangeView)
    self:Close()
end

function CangJinShopView:OnClickRechargeBtn()
    ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_cz)
end

function CangJinShopView:FlushModel()
    local init_cfg = CangJinShopWGData.Instance:GetModelCfgByIndex(1)
    if not init_cfg then
        return
    end

    local display_data = {}
    display_data.should_ani = true
    if init_cfg.model_show_itemid ~= 0 and init_cfg.model_show_itemid ~= "" then
        local split_list = string.split(init_cfg.model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
            display_data.item_id = init_cfg.model_show_itemid
        end
    end

    display_data.bundle_name = init_cfg["model_bundle_name"]
    display_data.asset_name = init_cfg["model_asset_name"]
    local model_show_type = tonumber(init_cfg["model_show_type"]) or 1
    display_data.render_type = model_show_type - 1
    display_data.event_trigger_listener_node = self.node_list["EventTriggerListener"]

    self.model_display:SetData(display_data)

    if init_cfg.display_scale and init_cfg.display_scale ~= "" then
        local scale = init_cfg.display_scale
        Transform.SetLocalScaleXYZ(self.node_list["model_display"].transform, scale, scale, scale)
    end

    local pos_x, pos_y = 0, 0
    if init_cfg.display_pos and init_cfg.display_pos ~= "" then
        local pos_list = string.split(init_cfg.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    RectTransform.SetAnchoredPositionXY(self.node_list.model_display.rect, pos_x, pos_y)

    if init_cfg.rotation and init_cfg.rotation ~= "" then
        local rotation_tab = string.split(init_cfg.rotation, "|")
        self.node_list["model_display"].transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2],
            rotation_tab[3])
    end
end

function CangJinShopView:RemindChangeCallBack(remind_name, num)
    if remind_name == RemindName.CangJinExchange then
        self.node_list["exchange_shop_red"]:SetActive(num > 0)
    end
end

function CangJinShopView:OnClickScoreIcon()
    local item_id = CangJinShopWGData.Instance:GetVirtualItemRechargeScore()

    if item_id and item_id > 0 then
        TipWGCtrl.Instance:OpenItem({ item_id = item_id })
    end
end
