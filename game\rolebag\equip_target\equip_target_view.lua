TARGET_TYPE = {
    WUQI = 0, --武器外观
    TIANSHEN = 1,--天神
    ROLE = 2, --人物模型
}

TARGET_SUIT_ACTIVE_REWARD_TYPE = {
    CONSUMABLES = 0,
    SKILL =  1,
}

--8件装备时的位置
local NormalCellPosTb = {
    [1] = Vector2(-291, 22),
    [2] = Vector2(-327, -101),
    [3] = Vector2(209, -43),
    [4] = Vector2(-313, -242),
    [5] = Vector2(184, -176),
    [6] = Vector2(-180, 114),
    [7] = Vector2(99, -280),
    [8] = Vector2(175, 83),
}
local NormalGetWayPosTb = {
    [1] = Vector2(-314, 108),
    [2] = Vector2(-354, -24),
    [3] = Vector2(363, 32),
    [4] = Vector2(-348, -171),
    [5] = Vector2(338, -109),
    [6] = Vector2(-207, 191),
    [7] = Vector2(257, -212),
    [8] = Vector2(332, 161),
}

--6件装备时的位置
local SpecialCellPosTb = {
    [1] = Vector2(-316, -36),
    [2] = Vector2(-327, -179),
    [3] = Vector2(185, -104),
    [4] = Vector2(0, 0), --不显示, 无用
    [5] = Vector2(139, -235),
    [6] = Vector2(-209, 76),
    [7] = Vector2(0, 0), --不显示, 无用
    [8] = Vector2(179, 36),
}
local SpecialGetWayPosTb = {
    [1] = Vector2(-345, 35),
    [2] = Vector2(-356, -109),
    [3] = Vector2(345, -28),
    [4] = Vector2(0, 0), --不显示, 无用
    [5] = Vector2(295, -164),
    [6] = Vector2(-239, 151),
    [7] = Vector2(0, 0), --不显示, 无用
    [8] = Vector2(338, 116),
}

local PROGRESS_WIDTH = 468

EquipTargetView = EquipTargetView or BaseClass(SafeBaseView)

function EquipTargetView:__init()
	self.is_big_view = true
	self.is_safe_area_adapter = true
    self:SetMaskBg(false, true)
    -- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_panel")
    self:AddViewResource(0, "uis/view/rolebag_ui/equip_target_prefab", "layout_equiptarget")
    --self:AddViewResource(0, ResPath.CommonBundleName, "layout_a2_common_top_panel")
end

function EquipTargetView:ReleaseCallBack()
    if nil ~= self.et_equip_type_list then
        self.et_equip_type_list:DeleteMe()
        self.et_equip_type_list = nil
    end

    if nil ~= self.et_equip_obj_list then
        for k,v in pairs(self.et_equip_obj_list) do
            v:DeleteMe()
        end
        self.et_equip_obj_list = nil
    end

    if nil ~= self.et_equip_obj_list1 then
        for k,v in pairs(self.et_equip_obj_list1) do
            v:DeleteMe()
        end
        self.et_equip_obj_list1 = nil
    end

    if self.et_suit_attr_item_list then
        for k,v in pairs(self.et_suit_attr_item_list) do
            v:DeleteMe()
        end
        self.et_suit_attr_item_list = nil
    end

    if self.et_role_model then
        self.et_role_model:DeleteMe()
        self.et_role_model = nil
    end

    if self.et_item_cell then
        self.et_item_cell:DeleteMe()
        self.et_item_cell = nil
    end

    self:CancelWeaponTween()
    self.et_origin_display_pos = nil
    self.et_suit_index = 0
    self.et_display_type = 0
    self.et_show_model = 0
    self.equip_index = nil
    self.task_line_tabs = nil
end

function EquipTargetView:LoadCallBack()
    self:InitEquipList()

    self.et_equip_type_list = AsyncListView.New(EquipTypeRender, self.node_list.et_ph_btn_listview)
    self.et_equip_type_list:SetSelectCallBack(BindTool.Bind1(self.OnClickEquipType, self))
    local type_data = EquipTargetWGData.Instance:GetEquipBigType()
    self.et_equip_type_list:SetDataList(type_data)

    self.et_item_cell = ItemCell.New(self.node_list.et_ph_item_cell)

    self.node_list.et_btn_act.button:AddClickListener(BindTool.Bind(self.OnClickAct, self))
    self.node_list.et_skill_icon.button:AddClickListener(BindTool.Bind(self.OnClickSkillIcon, self))
    self.node_list.btn_go_way1.button:AddClickListener(BindTool.Bind(self.OnClickWayGoTo, self, 1))
    self.node_list.btn_go_way2.button:AddClickListener(BindTool.Bind(self.OnClickWayGoTo, self, 2))
    self.node_list.btn_go_way3.button:AddClickListener(BindTool.Bind(self.OnClickWayGoTo, self, 3))
    self.node_list.btn_go_way4.button:AddClickListener(BindTool.Bind(self.OnClickWayGoTo, self, 4))
    self.node_list.btn_goto.button:AddClickListener(BindTool.Bind(self.OnClickGoTo, self))
    self.node_list.btn_compose.button:AddClickListener(BindTool.Bind(self.OnClickBtnCompose, self))
    
    if self.node_list.et_display and not self.et_origin_display_pos then
        self.et_origin_display_pos = self.node_list.et_display.rect.anchoredPosition
    end

    self.et_suit_index = 0
    self.et_display_type = 0
    self.et_show_model = 0
    self.et_suit_attr_item_list = {}
    self.task_line_tabs = {}
end

function EquipTargetView:OpenCallBack()
    self.is_first = true
end

function EquipTargetView:CloseCallBack()
    self.is_first = nil
end

function EquipTargetView:ShowIndexCallBack()
    self:PlayEquipTargeModelAction()
    self:InitEquipTargeModel()
end

function EquipTargetView:GetInitIndex()
    local type_data = EquipTargetWGData.Instance:GetEquipBigType()
    local active_count = 0
    for k_1, v_1 in ipairs(type_data) do
        local list = EquipTargetWGData.Instance:GetEquipList(v_1.suit_index)
        for k_2, v_2 in ipairs(list) do
            if v_2 and tonumber(v_2) > 0 then
                local state = EquipTargetWGData.Instance:GetEquipSortStateByIndex(v_1.suit_index, k_2, true)
                if state then
                    active_count = active_count + 1
                    return k_1
                end

            end
        end

        if k_1 == #type_data then
            return k_1
        end
    end

    return 1
end

function EquipTargetView:JumpFlushETView(to_ui_param)
    local type_data = EquipTargetWGData.Instance:GetEquipBigType()
    self.et_equip_type_list:SetDataList(type_data)
    if to_ui_param then
        self:JumpToTab(to_ui_param)
    else
        if self.is_first then
            self.is_first = false
            local init_index = self:GetInitIndex()
            self.et_equip_type_list:JumpToIndex(init_index)
        else
            self:FlushETView()
        end
    end
end

function EquipTargetView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if "all" == k then
            self:JumpFlushETView(v.to_ui_param)
		end
	end
end

function EquipTargetView:FlushETView()
    local capability = EquipTargetWGData.Instance:GetEquipAttrCap(self.et_suit_index)
    local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    local info = EquipTargetWGData.Instance:GetSuitInfo(self.et_suit_index)
    local active_num = EquipTargetWGData.Instance:GetEquipStateNumByIndex(self.et_suit_index)
    local remind = EquipTargetWGData.Instance:GetEquipTargetRemind(self.et_suit_index)
    local str, skill_icon, skill_name = EquipTargetWGData.Instance:GetSuitSkillDes(cfg.suit_active_reward, self.et_display_type, self.et_suit_index)

    local is_has_skill_cfg = EquipTargetWGData.Instance:GetSuitSkillActive(cfg.suit_active_reward)
    local name = ""
    if self.et_item_cell then
        if TARGET_TYPE.TIANSHEN == self.et_display_type then
            local item_cfg = ItemWGData.Instance:GetItemConfig(cfg.suit_active_reward)
            self.et_item_cell:SetData({item_id = cfg.suit_active_reward})
            name = item_cfg and item_cfg.name or ""
        elseif TARGET_TYPE.WUQI == self.et_display_type and is_has_skill_cfg then
                local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(cfg.suit_active_reward)
                name = skill_data.name
                XUI.SetSkillIcon(self.node_list.et_skill_bg, self.node_list.et_skill_icon, skill_icon)
        elseif TARGET_TYPE.ROLE == self.et_display_type or (TARGET_TYPE.WUQI == self.et_display_type and not is_has_skill_cfg) then  
            local item_data = ItemWGData.Instance:GetItemConfig(cfg.suit_active_reward)
            name = item_data.name
            self.et_item_cell:SetData({item_id = cfg.suit_active_reward})
        end
    end

    self.node_list.et_skill_desc.text.text = str
    self.node_list.et_name.text.text = name
    self:FlushSuitAttrListView()
    self:FlushEquipListView()
    self.node_list.et_btn_act:SetActive(info.suit_active_reward == 0)
    self.node_list.et_img_max:SetActive(info.suit_active_reward == 1)
    self.node_list.et_btn_act_red:SetActive(remind)
    self.node_list.et_cap_value.text.text = capability

    local max_count = 0
    local list = EquipTargetWGData.Instance:GetEquipList(self.et_suit_index)
    for k_1, v_1 in pairs(list) do
        if tonumber(v_1) > 0 then
            max_count = max_count + 1
        end
    end

    self.node_list.eight_line:SetActive(tonumber(max_count) > 6)
    self.node_list.six_line:SetActive(not (tonumber(max_count) > 6))
    self.node_list.et_equipment_colect_value.image:DOFillAmount(tonumber(active_num)/tonumber(max_count),UITween_CONSTS.EquipTargetSys.ToTargetProgress)
    self.node_list.et_jindu_text.text.text = string.format("%s/%s", active_num, max_count)

    local attr_list_data = EquipTargetWGData.Instance:GetEquipmenSuitAttr(self.et_suit_index)

    if IsEmptyTable(attr_list_data) then
        return 
    end

    local is_open_attr_num = 0
    local can_open_attr_num = 0
    local max_attr_num = #attr_list_data

    for i, v in ipairs(attr_list_data) do
        if active_num >= v.equip_num then
            can_open_attr_num = can_open_attr_num + 1
            is_open_attr_num = v.is_open ~= 0 and (is_open_attr_num + 1) or is_open_attr_num
        end
    end

    local spe_show_num = (can_open_attr_num > is_open_attr_num) and (is_open_attr_num + 1) or is_open_attr_num
    self.node_list.slider_progress.slider.value = spe_show_num / max_attr_num
    self.node_list.slider_progress_1.slider.value = is_open_attr_num / max_attr_num

    for index = 1, max_attr_num do
        self.node_list["img_line_"..index]:CustomSetActive(index <= max_attr_num - 1)
        local pos = (PROGRESS_WIDTH / max_attr_num) * index
        RectTransform.SetAnchoredPositionXY(self.node_list["img_line_"..index].rect, pos, -10)
    end

    local btn_name = remind and Language.EquipTarget.ActiveStr2 or Language.EquipTarget.ActiveStr1
    self.node_list.txt_act.text.text = btn_name

    self.node_list.text_goto.text.text = cfg.skip_name
    local open_param = cfg.is_open_skip or 1
    local is_open_skip = open_param == 1
    self.node_list.btn_goto:SetActive(is_open_skip)
end

function EquipTargetView:InitqETTweenState()
    local tween_root_1 = self.node_list.et_right_tween
    local tween_root_2 = self.node_list.et_tween
    local tween_root_3 = self.node_list.et_left_tween
    local tween_root_4 = self.node_list.et_btm_tween
    RectTransform.SetAnchoredPositionXY(tween_root_1.rect, 313, 0)
    RectTransform.SetAnchoredPositionXY(tween_root_4.rect, 0, -300)
    UITween.CleanAllTween(GuideModuleName.Achievement)

    UITween.FakeHideShow(tween_root_3)
    UITween.FakeHideShow(tween_root_2)
    UITween.FakeHideShow(self.node_list.et_equip_cap)
    self.is_do_cj_tween = true
end

function EquipTargetView:DoEquipTargetTween()

end

function EquipTargetView:EquipSequenceAni(obj)
    local scale = 1
    if self.display_type == TARGET_TYPE.WUQI then
        scale = 1.1
    else
        scale = 1.1
    end

    obj.transform.localRotation = Quaternion.Euler(0, 0, 40)
    obj.transform.localScale = Vector3(0.4, 0.4, 0.4)
    --self.node_list.ts_way.canvas_group.alpha = 0
    --self.node_list.equip_way.canvas_group.alpha = 0
    --self:ClearModelData()
    self:CancelWeaponTween()
    obj.canvas_group.alpha = 0

    local tween = obj.transform:DOScale(Vector3(scale, scale, scale), 0.3)
    tween:OnComplete(function ()
        obj.transform:DOScale(Vector3(1, 1, 1), 0.4)
    end)

    obj.canvas_group:DoAlpha(0, 1, 1)

    local tween2 = obj.transform:DOLocalRotate(Vector3(0, 0, 0), 0.7)
    tween2:OnComplete(function ()
        --self.node_list.ts_way.canvas_group:DoAlpha(0, 1, 0.6)
        --self.node_list.equip_way.canvas_group:DoAlpha(0, 1, 0.6)
        --self:PlayEffect()
        --self:InitEquipTargeModel()
    end)
    tween2:SetEase(DG.Tweening.Ease.InOutQuint)
end

function EquipTargetView:CancelEquipSequenceAni(obj)
    obj.transform.localRotation = Quaternion.Euler(0, 0, 0)
    obj.transform.localScale = Vector3(1, 1, 1)
end

-- function EquipTargetView:PlayEffect()
--     if self.et_display_type == TARGET_TYPE.WUQI and self.et_equip_obj_list then
--         for i,v in pairs(self.et_equip_obj_list) do
--             v:PlayEffect()
--         end
--     elseif self.et_display_type == TARGET_TYPE.TIANSHEN and self.et_equip_obj_list1 then
--         for i,v in pairs(self.et_equip_obj_list1) do
--             v:PlayEffect()
--         end
--     end
-- end    

function EquipTargetView:InitEquipList()
    self.et_equip_obj_list = {}
    local item
    for i=1, GameEnum.MAX_EQUIP_TARGET_NUM do
        item = EquipListRender.New(self.node_list['et_equip_' .. i])
        item:SetIndex(i)
        table.insert(self.et_equip_obj_list, item)
    end

    self.et_equip_obj_list1 = {}
    -- local item1
    -- for i=1, GameEnum.MAX_EQUIP_TARGET_NUM do
    --     item1 = EquipListRender.New(self.node_list['et_sequip_' .. i])
    --     item1:SetIndex(i)
    --     table.insert(self.et_equip_obj_list1, item1)
    -- end
end

function EquipTargetView:OnClickEquipType(cell)
    if cell == nil or cell:GetIndex() == self.equip_index then
        return
    end
    
    local data = cell:GetData()
    self.skill_index = data.suit_active_reward
    self.et_display_type = TARGET_TYPE.WUQI--data.display_type
    self.et_suit_index = data.suit_index
    self.equip_index = cell:GetIndex()
    -- local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    -- local is_show_itencell = TARGET_TYPE.WUQI == self.et_display_type and not EquipTargetWGData.Instance:GetSuitSkillActive(cfg.suit_active_reward)
    --self.node_list.et_tianshen:SetActive(TARGET_TYPE.TIANSHEN == self.et_display_type or TARGET_TYPE.ROLE == self.et_display_type or is_show_itencell)
    self.node_list.et_tianshen:SetActive(data.suit_active_reward_type == TARGET_SUIT_ACTIVE_REWARD_TYPE.CONSUMABLES)
    self.node_list.et_equip_content:SetActive(true)--TARGET_TYPE.ROLE ~= self.et_display_type)
    --self.node_list.et_ts_content:SetActive(TARGET_TYPE.ROLE == self.et_display_type)
    --self.node_list.et_skill:SetActive(TARGET_TYPE.WUQI == self.et_display_type)
    self.node_list.et_skill:SetActive(data.suit_active_reward_type == TARGET_SUIT_ACTIVE_REWARD_TYPE.SKILL)
    self.node_list.et_role_fishon:SetActive(TARGET_TYPE.ROLE == self.et_display_type)
    EquipTargetWGCtrl.Instance:SendEquipTarget(0, data.suit_index)
    self:SetCellsPos()
    --local node = TARGET_TYPE.ROLE == self.et_display_type and self.node_list.et_ts_content or self.node_list.et_equip_content
    local node = self.node_list.et_equip_content
    self:CancelEquipSequenceAni(node)
    self:EquipSequenceAni(node)
    self:FlushETView()
end

--设置位置
function EquipTargetView:SetCellsPos()
    local is_special = self.et_suit_index > GameEnum.EQUIP_TARGET_SPECIAL_SUIT_START
    local cell_pos_tab = is_special and SpecialCellPosTb or NormalCellPosTb
    local getway_pos_tab = is_special and SpecialGetWayPosTb or NormalGetWayPosTb
    for i,v in ipairs(self.et_equip_obj_list) do
        local is_inactive = is_special and (i == EquipTargetWGData.SuitPartNecklace or i == EquipTargetWGData.SuitPartDrop)
        v:SetViewParams(is_inactive, cell_pos_tab[i], getway_pos_tab[i])
    end
end

--刷新装备栏
function EquipTargetView:FlushEquipListView()
    local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    local list = EquipTargetWGData.Instance:GetEquipList(self.et_suit_index)
    local info = EquipTargetWGData.Instance:GetSuitInfo(self.et_suit_index)
    self.et_display_type = cfg.display_type
    self.et_show_model = cfg.show_model
    -- local add_list = {}
    -- for i = 8, 1, -1 do
    --     if tonumber(list[i]) and tonumber(list[i]) > 0 then
    --         table.insert(add_list, tonumber(list[i]))
    --     end
    -- end

    local count = 0
    self.now_way_tab = {}
    --if self.et_display_type == TARGET_TYPE.WUQI then
        for i, v in ipairs(self.et_equip_obj_list) do
            if list[i] and tonumber(list[i]) > 0 then
                local item_id = tonumber(list[i])
                v:SetData({item_id = item_id, suit_index = self.et_suit_index})

                local way_group = EquipTargetWGData.Instance:GetEquipGetawayById(item_id)
                if not IsEmptyTable(way_group) then
                    for key, way in pairs(way_group) do
                        if way and not self.now_way_tab[way.acquisition_path] and count < 4 then
                            self.now_way_tab[way.acquisition_path] = {}
                            count = count + 1
                            self.now_way_tab[way.acquisition_path].name = way.acquisition_path
                            self.now_way_tab[way.acquisition_path].item_id = item_id
                            self.now_way_tab[way.acquisition_path].index = count
                            self.now_way_tab[way.acquisition_path].show_index = i
                            self.now_way_tab[way.acquisition_path].get_type = way.get_type
                            local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.et_suit_index, i, true)
                            self.now_way_tab[way.acquisition_path].star_level = star_level
                        end
                    end
                end
            else
                --v:SetData({})
            end
        end
    -- else
    --     for i,v in ipairs(self.et_equip_obj_list1) do
    --         if list[i] then
    --             local item_id = tonumber(list[i])
    --             v:SetData({item_id = item_id, suit_index = self.et_suit_index})
    --         else
    --             v:SetData({})
    --         end
    --     end
    -- end

    for i = 1, 4, 1 do
        self.node_list["btn_go_way" .. i]:SetActive(false)
    end

    for k_1, v_1 in pairs(self.now_way_tab) do
        self.node_list["btn_go_way" .. v_1.index]:SetActive(true)
        self.node_list["text_go_way" .. v_1.index].text.text = v_1.name

        local can_to = false
        if v_1.get_type == 1 then
            -- 可合成红点
            local cur_num, max_num = EquipTargetWGData.Instance:GetEquipStateNumBySuitIndex(self.et_suit_index)
            -- if cur_num < max_num then
            --     can_to = EquipmentWGData.Instance:GetCESingleRemindByData(v_1.item_id, v_1.star_level)
            -- end

            if cur_num < max_num then
                local list = EquipTargetWGData.Instance:GetEquipList(self.et_suit_index)
                for k_1, v_1 in pairs(list) do
                    local item_id = tonumber(v_1)
                    if item_id > 0 then
                        local is_show, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.et_suit_index, k_1, true)
                        if is_show and EquipmentWGData.Instance:GetCESingleRemindByData(item_id, star_level) then
                            can_to = true
                        end
                    end
                end
            end
        end
        self.node_list["image_can_hecheng_" .. v_1.index]:SetActive(can_to)
        self.node_list["hecheng_remind_".. v_1.index]:SetActive(can_to)
    end
end

function EquipTargetView:OnClickWayGoTo(index)
    local item_id = nil
    local show_index = 0
    for k_1, v_1 in pairs(self.now_way_tab) do
        if v_1.index == index then
            item_id = v_1.item_id
            show_index = v_1.show_index
            break
        end
    end
    if not item_id then
        return
    end

    local way_group = EquipTargetWGData.Instance:GetEquipGetawayById(item_id)
    local way = way_group[index]
    if not IsEmptyTable(way) then
        if way and way.jump_panel_spc and way.jump_panel_spc ~= ""  then
            local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.et_suit_index, show_index, true)
            local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
            local tab_index = flag and TabIndex.other_compose_eq_hecheng_one or TabIndex.other_compose_eq_hecheng_two
            local cur_type = flag and EquipmentWGData.COMPOSE_EQUIP_TYPE.MALE or EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE
            local can_compose, open_param, to_ui_param = EquipmentWGData.Instance:GetEquipComposeCfgByProductId(item_id, cur_type,star_level)
            if can_compose then
                FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_index, {to_ui_name = 0, open_param = open_param, to_ui_param = to_ui_param})
            else
                FunOpen.Instance:OpenViewByName(way.jump_panel_spc, tab_index)
            end
            return
        end

        local boss_id = way.jump_aim
        if not boss_id then
            return 
        end
        BossOfferWGCtrl.Instance:JumToByID(boss_id)
        --FunOpen.Instance:OpenViewNameByCfg(way)
    else
        print_error("物品没有获取路径item_id = ", self.item_id)
    end
end

-- 刷新属性列表
function EquipTargetView:FlushSuitAttrListView()
    local list_data = EquipTargetWGData.Instance:GetEquipmenSuitAttr(self.et_suit_index)
    if IsEmptyTable(list_data) then
        return
    end

    for i,v in ipairs(list_data) do
        if self.et_suit_attr_item_list[i] then
            self.et_suit_attr_item_list[i]:SetData(v)
        else
            local item_vo = {}
            local obj = ResMgr:Instantiate(self.node_list["et_attri_num_render"].gameObject)--AllocAsyncLoader(self, "equiptarget_view" .. i)
            obj:SetActive(true)
            local obj_transform = obj.transform
            obj_transform:SetParent(self.node_list.et_suit_attr_list.transform, false)
            local item_render = EquipRichRenderNew.New(obj)
            item_render:SetData(v)
            self.et_suit_attr_item_list[i] = item_render
        end
    end
end

function EquipTargetView:ClearModelData()
    -- if nil ~= self.et_role_model then
    --     self.et_role_model:ClearModel()
    -- end
end

--跳转红点按钮
function EquipTargetView:JumpToTab(force_index)
    local index = 0
    local _, is_show = EquipTargetWGData.Instance:EqTargetIsShow()
    local num = EquipTargetWGData.Instance:JumpToRemind()
    local _, indexs = EquipTargetWGData.Instance:GetEquipTargetAllRemind()
    local type_data = EquipTargetWGData.Instance:GetEquipBigType()

    if indexs > 0 then
        index = indexs
    else
        index = num
    end
    
    index = is_show and 6 or index

    force_index = tonumber(force_index)
    if force_index then
        if force_index <= #type_data then
            index = force_index
        end
    end

    --添加容错
    if index > 0 and not IsEmptyTable(type_data) and index > #type_data then
        index = 1
    end

    if index > 0 then
        if self.et_equip_type_list then
            self.et_equip_type_list:JumpToIndex(index, 7)
        end
    else
        self:FlushETView()
    end
end

---模型动画
function EquipTargetView:PlayWeaponTween()
    if not self.tween_weapon then
        local tween_root = self.node_list["et_display"].rect
        self.tween_weapon = tween_root:DOAnchorPosY(tween_root.anchoredPosition.y + 50, 1)
        self.tween_weapon:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
    else
        self.tween_weapon:Restart()
    end
end

function EquipTargetView:CancelWeaponTween()
    if self.tween_weapon then
        self.tween_weapon:Kill()
        self.tween_weapon = nil
        local tween_root = self.node_list["et_display"]
        if tween_root then
            tween_root.rect.anchoredPosition = self.et_origin_display_pos
        end
    end
end

--模型初始化
function EquipTargetView:InitEquipTargeModel()
    if not self.et_role_model then
        self.et_role_model = RoleModel.New()
        self.et_role_model:SetPositionAndRotation(nil, Vector3(0, 180, 0), nil)
        local display_data = {
            parent_node = self.node_list["et_display"],
            CAMERA_TYPE = MODEL_CAMERA_TYPE.BASE,
            rt_scale_type = ModelRTSCaleType.M,
            can_drag = false,
        }
        self.et_role_model:SetRenderTexUI3DModel(display_data)

    end
    local role_vo = GameVoManager.Instance:GetMainRoleVo()
    local special_status_table = {ignore_wing  = true, ignore_halo  = true, ignore_fazhen  = true, ignore_mantle  = true, ignore_god_or_demon = true,
        ignore_waist = true, ignore_mask = true, ignore_shouhuan = true, ignore_tail = true, ignore_jianzhen = true, is_preview = true, ignore_weapon = true}
    self.et_role_model:SetModelResInfo(role_vo, special_status_table, nil, SceneObjAnimator.Sit_Idle)
    self.et_role_model:SetWeaponModelFakeRemove()



end

function EquipTargetView:PlayEquipTargeModelAction()
    if not self.et_role_model then
        return
    end
end

--技能弹窗
function EquipTargetView:OnClickSkillIcon()
    if self.et_display_type ~= TARGET_TYPE.WUQI then
        return
    end

    local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    local str, skill_icon = EquipTargetWGData.Instance:GetSuitSkillDes(cfg.suit_active_reward, self.et_display_type, self.et_suit_index)

    local skill_data = SkillWGData.Instance:GetPassiveSkillByIndex(self.skill_index)
    local capability = EquipTargetWGData.Instance:GetSkillAttrCap(self.skill_index)

    if skill_data then
        local show_data = {
            icon = skill_icon,
            top_text = skill_data.name,
            body_text = str,

            x = -49,
            y = -90,
            set_pos = true,
            capability = capability,
        }

        NewAppearanceWGCtrl.Instance:DisplayBoxOpen(show_data)
    end
end

--激活套装
function EquipTargetView:OnClickAct()
    local remind = EquipTargetWGData.Instance:GetEquipTargetRemind(self.et_suit_index)
    local gaer_list, cur_gaer = EquipTargetWGData.Instance:GetEquipJinDu(self.et_suit_index)
    if not remind then
        --SysMsgWGCtrl.Instance:ErrorRemind(Language.EquipTarget.Tips)
        FunOpen.Instance:OpenViewByName(FunName.Boss, TabIndex.boss_vip)
        return
    end

    for i = 1, cur_gaer do
        if gaer_list[i-1] <= 0 then
            EquipTargetWGCtrl.Instance:SendEquipTarget(1, self.et_suit_index)
            return
        end
    end
end

function EquipTargetView:OnClickGoTo()
    local cfg = EquipTargetWGData.Instance:GetEquipinfo(self.et_suit_index)
    FunOpen.Instance:OpenViewNameByCfg(cfg.skip_panel)
end

function EquipTargetView:OnClickBtnCompose()
    EquipTargetWGCtrl.Instance:OpenEquipTargetCompose()
end


------------------------------------装备标签------------------------
EquipTypeRender = EquipTypeRender or BaseClass(BaseRender)

function EquipTypeRender:__init()

end

function EquipTypeRender:__delete()

end

function EquipTypeRender:LoadCallBack()

end

function EquipTypeRender:OnFlush()
    if nil == self.data then
        return
    end

    -- local remind = EquipTargetWGData.Instance:GetEquipTargetRemind(self.data.suit_index)

    -- -- 可合成红点
    -- if not remind then
    --     local cur_num, max_num = EquipTargetWGData.Instance:GetEquipStateNumBySuitIndex(self.data.suit_index)
    --     if cur_num < max_num then
    --         local list = EquipTargetWGData.Instance:GetEquipList(self.data.suit_index)
    --         for k_1, v_1 in pairs(list) do
    --             local item_id = tonumber(v_1)
    --             if item_id > 0 then
    --                 local is_show, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.data.suit_index, k_1, true)
    --                 if is_show and EquipmentWGData.Instance:GetCESingleRemindByData(item_id, star_level) then
    --                     remind = true
    --                 end
    --             end
    --         end
    --     end
    -- end

    local state = EquipTargetWGData.Instance:GetSuitInfo(self.data.suit_index).suit_active_reward
    self.node_list.finish:SetActive(state == 1)
    --self.node_list.text_normal.text.text = self.data.suit_name
    --self.node_list.text_hl.text.text = self.data.suit_name

    self.node_list.text_normal_name_1.text.text = self.data.suit_name1
    self.node_list.text_normal_name_2.text.text = self.data.suit_name2
    self.node_list.text_select_name_1.text.text = self.data.suit_name1
    self.node_list.text_select_name_2.text.text = self.data.suit_name2

    self:FlushRemind()
    -- self.node_list.remind:SetActive(remind)
    self:OnSelectChange(self:IsSelectIndex())
end

function EquipTypeRender:OnSelectChange(is_select)
    --self.node_list.text_normal:SetActive(not is_select)
    --self.node_list.text_hl:SetActive(is_select)
    self.node_list.bg_hl:SetActive(is_select)
    self.node_list.bg_normal:SetActive(not is_select)

    self.node_list.text_normal_name_1:SetActive(not is_select)
    self.node_list.text_normal_name_2:SetActive(not is_select)
    self.node_list.text_select_name_1:SetActive(is_select)
    self.node_list.text_select_name_2:SetActive(is_select)
end

function EquipTypeRender:PalyItemAnimator(item_index)

end

function EquipTypeRender:FlushRemind()
    
    local remind = EquipTargetWGData.Instance:GetEquipTargetRemind(self.data.suit_index, true)
    self.node_list.remind:SetActive(remind)

end

------------------------------------装备列表------------------------
EquipListRender = EquipListRender or BaseClass(BaseRender)

function EquipListRender:__init()

end

function EquipListRender:SetViewParams(is_inactive, pos, getway_pos)
    if not is_inactive then
        if self.view then
            self.view:SetActive(true)
            --self.view.rect.localPosition = pos
        end
        -- if self.node_list and self.node_list.get_way then
        --     self.node_list.get_way:SetActive(true)
        --     --self.node_list.get_way.rect.localPosition = getway_pos
        -- end
    else
        if self.view then
            self.view:SetActive(false)
        end
        -- if self.node_list and self.node_list.get_way then
        --     self.node_list.get_way:SetActive(false)
        -- end
    end
end


function EquipListRender:__delete()
   if self.et_item_cell then
        self.et_item_cell:DeleteMe()
        self.et_item_cell = nil
    end

    XUI.SetGraphicGrey(self.node_list.et_equip, false)
end

function EquipListRender:LoadCallBack()
    --if not self.et_item_cell then
        --self.et_item_cell = ItemCell.New(self.node_list.ph_icon)
    --end
    self.node_list.image_icon.button:AddClickListener(BindTool.Bind1(self.OnClickItemCell, self))

    --self.node_list.get_way.button:AddClickListener(BindTool.Bind1(self.OnClickTo, self))
end

function EquipListRender:OnFlush()
    local data = self:GetData()
    if not data and not IsEmptyTable(data) then
        return
    end

    local state = EquipTargetWGData.Instance:GetEquipSortStateByIndex(data.suit_index, self.index, true)
    local star_level = EquipTargetWGData.Instance:GetEquipShowStarLevel(data.suit_index, self.index)
    local way = EquipTargetWGData.Instance:GetEquipGetawayById(data.item_id)
    if way then
        --self.node_list.get_way.text.text = way.acquisition_path.."————"
    end

    for i = 1, 5, 1 do
        --self.node_list["image_star" .. i]:SetActive(star_level >= i)
        self.node_list["node_star_" .. i]:SetActive(star_level == i)
    end
    self.item_id = self.data.item_id
    --self.et_item_cell:SetData({item_id = data.item_id})
    --self.et_item_cell:SetGraphicGreyCualityBg(state)
    --self.et_item_cell:SetCellBgEnabled(false)
    --self.et_item_cell:TrySetActive("quality_icon", nil, false)
    --self.et_item_cell:SetDefaultEff(not state)
    local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
    if item_cfg == nil then
        print_error("装备目标 道具不存在 item_id:",self.data.item_id)
        return
    end
    local bundle, asset = ResPath.GetItem(item_cfg.icon_id)
    self.node_list.image_icon.image:LoadSprite(bundle, asset, function ()
        self.node_list.image_icon.image:SetNativeSize()
    end)
    self.node_list.is_has:SetActive(not state)

    local bundle1, asset1 =  ResPath.GetCommonImages("a3_sc_btn_bg_" .. item_cfg.color)
    self.node_list.image_bg.image:LoadSprite(bundle1, asset1)

    XUI.SetGraphicGrey(self.node_list.et_equip, state)
end

function EquipListRender:PlayEffect()
    local data = self:GetData()
    local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(data.suit_index, self.index, true)
    if not state then
        -- 屏蔽掉特效
        -- local bundle_name, asset_name = ResPath.GetEffectUi(Ui_Effect.UI_zhuangbeihuo)
        -- EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.effect.transform, 2)
    end
end

function EquipListRender:OnClickTo()
    local way_group = EquipTargetWGData.Instance:GetEquipGetawayById(self.item_id)
    local way = way_group[1]
    if not IsEmptyTable(way) then
        if way and way.jump_panel_spc and way.jump_panel_spc ~= ""  then
            local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.data.suit_index, self.index, true)
            local flag = RoleWGData.Instance:GetRoleSex() == GameEnum.MALE
            local tab_index = flag and TabIndex.other_compose_eq_hecheng_one or TabIndex.other_compose_eq_hecheng_two
            local cur_type = flag and EquipmentWGData.COMPOSE_EQUIP_TYPE.MALE or EquipmentWGData.COMPOSE_EQUIP_TYPE.FEMALE
            local can_compose, open_param, to_ui_param = EquipmentWGData.Instance:GetEquipComposeCfgByProductId(self.item_id, cur_type,star_level)
            if can_compose then
                FunOpen.Instance:OpenViewByName(GuideModuleName.Compose, tab_index, {to_ui_name = 0, open_param = open_param, to_ui_param = to_ui_param})
            else
                FunOpen.Instance:OpenViewByName(way.jump_panel_spc, tab_index)
            end
            return
        end

        local boss_id = way.jump_aim
        if not boss_id then
            return 
        end
        BossOfferWGCtrl.Instance:JumToByID(boss_id)
        --FunOpen.Instance:OpenViewNameByCfg(way)
    else
        print_error("物品没有获取路径item_id = ", self.item_id)
    end
end

function EquipListRender:OnClickItemCell()
    local item_d = {}
    item_d.item_id = self.data.item_id
    local state, star_level = EquipTargetWGData.Instance:GetEquipSortStateByIndex(self.data.suit_index, self.index, true)
    item_d.param = {star_level = star_level}
    TipWGCtrl.Instance:OpenItem(item_d)
end

-----------------------------套装属性-------------------------------
EquipRichRenderNew = EquipRichRenderNew or BaseClass(BaseRender)

function EquipRichRenderNew:__init()
    self.attr_list = {}
    local attr_num = self.node_list.attr_list.transform.childCount
    for i = 1, attr_num do
        self.attr_list[i] = self.node_list.attr_list:FindObj("attr_" .. i)
    end
end

function EquipRichRenderNew:__delete()
    self.attr_list = nil
end

function EquipRichRenderNew:LoadCallBack()
    
end

function EquipRichRenderNew:OnFlush()
    if nil == self.data then
        return
    end

    local attri_color = COLOR3B.WHITE
    if self.data.is_open == 1 then
        attri_color = COLOR3B.DEFAULT_NUM
    -- else
    --     attri_color = COLOR3B.D_GRAY
    end

    local need_str = self.data.equip_num .. Language.Equip.SuitNumCompany1--"【" .. self.data.equip_num .. Language.Equip.SuitNumCompany1 .. "】"
    self.node_list.need_num.text.text = ToColorStr(need_str, attri_color)
    local list = self.data.attr_list

    for k, v in ipairs(self.attr_list) do
        if list[k] then
            local is_per = not EquipmentWGData.Instance:GetAttrIsPerByAttrStr(list[k].attr_type)
            local name = Language.Common.AttrNameList2[list[k].attr_type]
            local value = is_per and list[k].value or list[k].value / 100 .. "%"
            local attr_str = string.format("%s +%s", name, value)
            v.text.text = ToColorStr(attr_str, attri_color)
            v:SetActive(true)
        else
            v:SetActive(false)
        end
    end
end
