﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class ListViewSimpleDelegateWrap
{
	public static void Register(LuaState L)
	{
		L.<PERSON>ginClass(typeof(ListViewSimpleDelegate), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("GetNumberOfCells", GetNumberOfCells);
		<PERSON><PERSON>ction("GetCellViewSize", GetCellViewSize);
		<PERSON><PERSON>ction("GetCellView", GetCellView);
		<PERSON><PERSON>RegFunction("CreateCell", CreateCell);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("CellPrefab", get_CellPrefab, set_CellPrefab);
		<PERSON>.<PERSON>ar("NumberOfCellsDel", get_NumberOfCellsDel, set_NumberOfCellsDel);
		<PERSON><PERSON>("CellSizeDel", get_CellSizeDel, set_CellSizeDel);
		<PERSON><PERSON>("CellRefreshDel", get_CellRefreshDel, set_CellRefreshDel);
		<PERSON>.Reg<PERSON>unction("CellRefreshDelegate", ListViewSimpleDelegate_CellRefreshDelegate);
		L.RegFunction("CellSizeDelegate", ListViewSimpleDelegate_CellSizeDelegate);
		L.RegFunction("NumberOfCellsDelegate", ListViewSimpleDelegate_NumberOfCellsDelegate);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetNumberOfCells(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)ToLua.CheckObject(L, 1, typeof(ListViewSimpleDelegate));
			EnhancedUI.EnhancedScroller.EnhancedScroller arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 2);
			int o = obj.GetNumberOfCells(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCellViewSize(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)ToLua.CheckObject(L, 1, typeof(ListViewSimpleDelegate));
			EnhancedUI.EnhancedScroller.EnhancedScroller arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			float o = obj.GetCellViewSize(arg0, arg1);
			LuaDLL.lua_pushnumber(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCellView(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 4);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)ToLua.CheckObject(L, 1, typeof(ListViewSimpleDelegate));
			EnhancedUI.EnhancedScroller.EnhancedScroller arg0 = (EnhancedUI.EnhancedScroller.EnhancedScroller)ToLua.CheckObject<EnhancedUI.EnhancedScroller.EnhancedScroller>(L, 2);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 3);
			int arg2 = (int)LuaDLL.luaL_checknumber(L, 4);
			EnhancedUI.EnhancedScroller.EnhancedScrollerCellView o = obj.GetCellView(arg0, arg1, arg2);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CreateCell(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)ToLua.CheckObject(L, 1, typeof(ListViewSimpleDelegate));
			ListViewCell o = obj.CreateCell();
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CellPrefab(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)o;
			ListViewCell ret = obj.CellPrefab;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellPrefab on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_NumberOfCellsDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)o;
			ListViewSimpleDelegate.NumberOfCellsDelegate ret = obj.NumberOfCellsDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index NumberOfCellsDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CellSizeDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)o;
			ListViewSimpleDelegate.CellSizeDelegate ret = obj.CellSizeDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellSizeDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_CellRefreshDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)o;
			ListViewSimpleDelegate.CellRefreshDelegate ret = obj.CellRefreshDel;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellRefreshDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CellPrefab(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)o;
			ListViewCell arg0 = (ListViewCell)ToLua.CheckObject(L, 2, typeof(ListViewCell));
			obj.CellPrefab = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellPrefab on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_NumberOfCellsDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)o;
			ListViewSimpleDelegate.NumberOfCellsDelegate arg0 = (ListViewSimpleDelegate.NumberOfCellsDelegate)ToLua.CheckDelegate<ListViewSimpleDelegate.NumberOfCellsDelegate>(L, 2);
			obj.NumberOfCellsDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index NumberOfCellsDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CellSizeDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)o;
			ListViewSimpleDelegate.CellSizeDelegate arg0 = (ListViewSimpleDelegate.CellSizeDelegate)ToLua.CheckDelegate<ListViewSimpleDelegate.CellSizeDelegate>(L, 2);
			obj.CellSizeDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellSizeDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_CellRefreshDel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			ListViewSimpleDelegate obj = (ListViewSimpleDelegate)o;
			ListViewSimpleDelegate.CellRefreshDelegate arg0 = (ListViewSimpleDelegate.CellRefreshDelegate)ToLua.CheckDelegate<ListViewSimpleDelegate.CellRefreshDelegate>(L, 2);
			obj.CellRefreshDel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index CellRefreshDel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListViewSimpleDelegate_CellRefreshDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<ListViewSimpleDelegate.CellRefreshDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<ListViewSimpleDelegate.CellRefreshDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListViewSimpleDelegate_CellSizeDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<ListViewSimpleDelegate.CellSizeDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<ListViewSimpleDelegate.CellSizeDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ListViewSimpleDelegate_NumberOfCellsDelegate(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);
			LuaFunction func = ToLua.CheckLuaFunction(L, 1);

			if (count == 1)
			{
				Delegate arg1 = DelegateTraits<ListViewSimpleDelegate.NumberOfCellsDelegate>.Create(func);
				ToLua.Push(L, arg1);
			}
			else
			{
				LuaTable self = ToLua.CheckLuaTable(L, 2);
				Delegate arg1 = DelegateTraits<ListViewSimpleDelegate.NumberOfCellsDelegate>.Create(func, self);
				ToLua.Push(L, arg1);
			}
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

