-- F2 VIP投资

function RechargeView:InitStorehousePanel()
	self.is_first_open_storehousepanel = true
	self.node_list.storehouse_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickStorehouseBuyBtn, self))
	self.node_list.get_reward_btn.button:AddClickListener(BindTool.Bind(self.OnClickStorehouseGetRewardBtn, self))

	local storehouse_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.StorehouseCard)
	self.storehouse_cost = storehouse_cfg and storehouse_cfg.need_rmb or 0
	self.storehouse_cost_type = storehouse_cfg and storehouse_cfg.rmb_type or 0
	local price_str = RoleWGData.GetPayMoneyStr(self.storehouse_cost, self.storehouse_cost_type, INVEST_CARD_TYPE.StorehouseCard)

	if storehouse_cfg.invest_need_vip_level > 0 then
		--self.node_list.storehouse_buy_cost.text.fontSize = 22
		self.node_list.storehouse_buy_cost.text.text = string.format(Language.Vip.MonthCardBuyBtnLbl, storehouse_cfg.invest_need_vip_level, price_str)
	else
		--self.node_list.storehouse_buy_cost.text.fontSize = 30
		self.node_list.storehouse_buy_cost.text.text = price_str --string.format(Language.Recharge.MonthCardBuy, self.storehouse_cost)
	end
	self.invest_need_vip_level = storehouse_cfg.invest_need_vip_level
	self.reward_item_root = AsyncListView.New(ItemCell, self.node_list.reward_item_root)
	self.reward_item_root:SetStartZeroIndex(true)
end

function RechargeView:DeleteStorehousePanel()
	if self.reward_item_root then
		self.reward_item_root:DeleteMe()
		self.reward_item_root = nil
	end

	self.storehouse_cost = nil
	self.storehouse_cost_type = nil
	self.invest_need_vip_level = nil
end

function RechargeView:OnClickStorehouseBuyBtn()
	if self.storehouse_cost > 0 and RoleWGData.Instance:GetRoleVo().vip_level >= self.invest_need_vip_level then
		RechargeWGCtrl.Instance:Recharge(self.storehouse_cost, self.storehouse_cost_type, INVEST_CARD_TYPE.StorehouseCard)
	end
end

function RechargeView:OnClickStorehouseGetRewardBtn()
	local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)
	RechargeWGCtrl.Instance:SendFetchInvestCardReward(INVEST_CARD_TYPE.StorehouseCard, card_info.cur_day)
end

function RechargeView:FlushStorehousePanel()
	local mc_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.MonthCard)
	local mc_can_get_reward, mc_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.MonthCard, mc_info.cur_day)
	local is_show = mc_state == INVEST_CARD_TIPS.NoInvest
	self.node_list.storehouse_panel:SetActive(is_show)
	if not is_show then
		return
	end

	local storehouse_cfg_list = RechargeWGData.Instance:GetTZCardRewardCfg(INVEST_CARD_TYPE.StorehouseCard)
	local card_info = RechargeWGData.Instance:GetInvestCardInfo(INVEST_CARD_TYPE.StorehouseCard)
	local can_active = RechargeWGData.Instance:CanActiveTZCard(INVEST_CARD_TYPE.StorehouseCard)
	local can_get_reward, sc_state = RechargeWGData.Instance:CanGetInvestCardReward(INVEST_CARD_TYPE.StorehouseCard, card_info.cur_day)

	local data_list = {}
	local index = 1
	local card_max_day = RechargeWGData.Instance:GetTZCardMaxDay(INVEST_CARD_TYPE.StorehouseCard)
	---[[只显示当前周期
	local cur_day = 1
	if storehouse_cfg_list and card_info then
		cur_day = card_info.cur_day + 1
		cur_day = cur_day <= card_max_day and cur_day or 1
		local zhouqi = math.ceil(cur_day / 7)
		local max_day = zhouqi * 7
		local min_day = (zhouqi - 1) * 7
		index = (can_active and 1) or cur_day - (zhouqi - 1) * 7
	end

	if can_get_reward then
		self.node_list.get_reward_text.text.text = Language.Marry.LingQu
	else
		self.node_list.get_reward_text.text.text = Language.Common.YiLingQu
	end

    local storehouse_cfg = RechargeWGData.Instance:GetTZCardCfg(INVEST_CARD_TYPE.StorehouseCard)
	local time_txt = Language.Vip.CardNoBuyTimer
	if sc_state ~= INVEST_CARD_TIPS.NoInvest then
		local duration = storehouse_cfg and storehouse_cfg.duration or 0
		local limit_time = duration - cur_day
		time_txt = string.format(Language.Vip.CardTimer, limit_time)
	end

	self.node_list.sc_time.text.text = time_txt
	self.node_list.vip_storehouse_title_text.text.text = Language.Recharge.VIPTeQuanFanLi2[3]
	self.node_list.vip_storehouse_text.text.text = Language.Recharge.VIPTeQuanFanLi2[1]
	local return_gold = storehouse_cfg and storehouse_cfg.return_gold or 0
	self.node_list.vip_storehouse_text_2.text.text = string.format(Language.Recharge.VIPTeQuanFanLi2[2], return_gold)
    self.node_list.storehouse_fanli_num.text.text = return_gold

	self.node_list.storehouse_buy_cost:SetActive(can_active)
	self.node_list.lifan_bg:SetActive(can_active)
	self.node_list.storehouse_buy_btn:SetActive(can_active)
	self.node_list.reward_remind:SetActive(can_get_reward)

	if not can_active then
		self.node_list.get_reward_btn:SetActive(can_get_reward)
		self.node_list.get_reward_flag:SetActive(not can_get_reward)
	else
		self.node_list.get_reward_btn:SetActive(false)
		self.node_list.get_reward_flag:SetActive(false)
	end

    local storehouse_list = RechargeWGData.Instance:GetTZCardRewardCfg(INVEST_CARD_TYPE.StorehouseCard,card_info.cur_day)

    self.reward_item_root:SetDataList(storehouse_list.reward_item)
	self.reward_item_root:SetRefreshCallback(function(item_cell, cell_index)
		if item_cell then
			item_cell:SetLingQuVisible(sc_state ~= INVEST_CARD_TIPS.NoInvest and not can_get_reward)
		end
	end)
end


--------------------------------------------------StorehouseItemRender--------------------------------------------------------------------------------
