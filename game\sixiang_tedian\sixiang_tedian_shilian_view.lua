SiXiangTeDianShiLianView = SiXiangTeDianShiLianView or BaseClass(SafeBaseView)

function SiXiangTeDianShiLianView:__init(view_name)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/sixiang_call_prefab", "sixiang_shilian_view")
	self:SetMaskBg(true, true)
end

function SiXiangTeDianShiLianView:LoadCallBack()
	self:InitParam()
	--self:InitPanel()
	self:InitListener()
	
	if not self.item_root then	
		self.item_root = ItemCell.New(self.node_list.center_item_root)
	end
end

function SiXiangTeDianShiLianView:ReleaseCallBack()
	-- if self.sixiang_item_list then
	-- 	for k,v in pairs(self.sixiang_item_list) do
	-- 		v:DeleteMe()
	-- 	end
	-- 	self.sixiang_item_list = nil
	-- end
	if self.item_root then
		self.item_root:DeleteMe()
		self.item_root = nil
	end
	CountDownManager.Instance:RemoveCountDown("sixiang_tedian_shilian")
end

function SiXiangTeDianShiLianView:CloseCallBack()
	-- if self.title_tween then
	-- 	self.title_tween:Kill()
	-- 	self.title_tween = nil
	-- end
	if self.center_item_tween then
		self.center_item_tween:Kill()
		self.center_item_tween = nil
	end

	-- if self.tween_yoyo then
	-- 	self.tween_yoyo:Kill()
	-- 	self.tween_yoyo = nil
	-- end
	-- if self.sixiang_item_list then
	-- 	for k,v in pairs(self.sixiang_item_list) do
	-- 		v:RestTween()
	-- 	end
	-- end
end

function SiXiangTeDianShiLianView:OnFlush(param_t)
	self:RefreshView()
end

function SiXiangTeDianShiLianView:InitParam()
	self.cur_act_id = 0
	--self.title_tween = nil
	self.center_item_tween = nil
	--self.tween_yoyo = nil
end

function SiXiangTeDianShiLianView:InitPanel()
	local show_id_list = SiXiangCallWGData.Instance:GetSiXiangSummonItemList()
	if IsEmptyTable(show_id_list) then
		return
	end
	local center_num = #show_id_list / 2
	local item_parent = self.node_list.item_list
	local sixiang_item_list = {}

	for i = #show_id_list, 1, -1 do
		local item = SiXiangTeDianShiLianItem.New()
		item:SetIndex(i)
		if i <= center_num then
			item:SavePos(-194 - 120 * (i - 1), 0)
		else
			item:SavePos(194 + 120 * (i - center_num - 1), 0)
		end
		item:DoLoad(item_parent)
		item:SetData(show_id_list[i].item_data)
		sixiang_item_list[i] = item
	end

	self.sixiang_item_list = sixiang_item_list
end

function SiXiangTeDianShiLianView:InitListener()
	XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind1(self.OnClickBuyBtn, self))
	XUI.AddClickEventListener(self.node_list.tip_btn, BindTool.Bind1(self.OnClickTipBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_close_window, BindTool.Bind1(self.Close, self))
end

function SiXiangTeDianShiLianView:OnClickTipBtn()
	RuleTip.Instance:SetContent(Language.SiXiangCall.ShiLianRuleContent, Language.SiXiangCall.ShiLianRuleTitle)
end

function SiXiangTeDianShiLianView:OnClickBuyBtn()
	SiXiangTeDianWGCtrl.Instance:BuySiXiangTeDian(self.cur_act_id)
end

function SiXiangTeDianShiLianView:ShowIndexCallBack()
	--self:PlayShowViewTween()
end

function SiXiangTeDianShiLianView:RefreshView()
	self.cur_act_id = SiXiangTeDianWGData.Instance:GetCurShiLian()
	if self.item_root then
		self.item_root:SetData({item_id = 50020})
	end
	self:FlushDiscount()
	self:FlushActTime()
	self:FlushBuyBtnState()
	--self:FlushShowItemList()
end

-- function SiXiangTeDianShiLianView:FlushShowItemList()
-- 	local item_list = self.sixiang_item_list
-- 	local show_id_list = SiXiangCallWGData.Instance:GetSiXiangSummonItemList()
-- 	if IsEmptyTable(item_list) or IsEmptyTable(show_id_list) then
-- 		return
-- 	end
-- 	for i = #show_id_list, 1, -1 do
-- 		item_list[i]:SetData(show_id_list[i].item_data)
-- 	end
-- end

-- 刷新折扣
function SiXiangTeDianShiLianView:FlushDiscount()
	local discount = 0
	if self.cur_act_id == YuanShenZhaoHuanSubActId.ShiLian1 then
		discount = 3
	elseif self.cur_act_id == YuanShenZhaoHuanSubActId.ShiLian2 then
		discount = 5
	end
	
	self.node_list.discount_lbl.text.text = discount .. "折"
end

-- 刷新购买按钮
function SiXiangTeDianShiLianView:FlushBuyBtnState()
	local sale_info = SiXiangTeDianWGData.Instance:GetSubActSaleInfo(self.cur_act_id, 1)
	local cfg_list = SiXiangTeDianWGData.Instance:GetActSaleCfg(self.cur_act_id)
	if not sale_info or IsEmptyTable(cfg_list) then
		return
	end

	local btn_text = ""
    local cfg_data = cfg_list[1]
    if sale_info.status == YuanShenSaleSubActSaleStatus.NotBuy then
        --未购买
        if cfg_data.price_type == SiXiangBuyType.RMB then
        	
			--btn_text = string.format(Language.SiXiangCall.BuyText, cfg_data.special_sale_price)
			local act_id =  self.cur_act_id
			local product_id = 1
			local cfg_list = SiXiangTeDianWGData.Instance:GetActSaleCfg(act_id)
			local cfg = cfg_list and cfg_list[product_id]
			local flag = cfg.product_id * 100 + act_id
			btn_text = RoleWGData.GetPayMoneyStr(cfg_data.special_sale_price, GET_GOLD_REASON.GET_GOLD_REASON_YUAN_SHEN_ZHAO_HUAN, flag)
        elseif cfg_data.price_type == SiXiangBuyType.XY then
        	btn_text = string.format(Language.SiXiangCall.BuyText2, cfg_data.special_sale_price)
        end
    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch then
        --已购买未领取（rmb商品会用到）
        btn_text = Language.SiXiangCall.CurCanFetch
    elseif sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyAndFetched then
        --已购买已领取
        btn_text = Language.SiXiangCall.HasFetch
    end
    self.node_list.buy_btn_label.text.text = btn_text

    self.node_list.red_point:SetActive(sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyNotFetch)
    self.node_list.finish_img:SetActive(sale_info.status == YuanShenSaleSubActSaleStatus.HasBuyAndFetched)
    self.node_list.buy_btn:SetActive(sale_info.status ~= YuanShenSaleSubActSaleStatus.HasBuyAndFetched)
end

---[[ 活动结束倒计时
function SiXiangTeDianShiLianView:FlushActTime()
	CountDownManager.Instance:RemoveCountDown("sixiang_tedian_shilian")
    local sever_time = TimeWGCtrl.Instance:GetServerTime()
    local end_time = SiXiangTeDianWGData.Instance:GetTeDianEndTimeStamp()
    if end_time <= sever_time then
        self.node_list.time_label.text.text = ""
        return
    end

    self:UpdateCountDown(sever_time, end_time)

    CountDownManager.Instance:AddCountDown(
        "sixiang_tedian_shilian",
        BindTool.Bind(self.UpdateCountDown, self),
        BindTool.Bind(self.FlushActTime, self),
        end_time,
        nil,
        1
    )
end

function SiXiangTeDianShiLianView:UpdateCountDown(elapse_time, total_time)
	local time_str = TimeUtil.FormatSecondDHM8(math.floor(total_time - elapse_time))
	self.node_list.time_label.text.text = string.format(Language.SiXiangCall.CloseTimeDesc1, time_str)
end
--]]

---[[ 动画相关
function SiXiangTeDianShiLianView:PlayShowViewTween()
	if self.sixiang_item_list then
		local item_list = self.sixiang_item_list
		local center_num = #item_list / 2
		for i=1,#item_list do
			item_list[i]:PlayShowItemTween(center_num)
		end
	end
	self:PlayTitleImgTween()
	self:PlayCenterItemTween()
end

function SiXiangTeDianShiLianView:PlayTitleImgTween()
	if self.title_tween then
		return
	end
	self.node_list.title_img.transform:SetLocalScale(1.5, 1.5, 1.5)
	self.title_tween = self.node_list.title_img.transform:DOScale(1, 0.4)
	self.title_tween:SetEase(DG.Tweening.Ease.InQuad)
	self.title_tween:OnComplete(function ()
		self.title_tween = nil
	end)
end

function SiXiangTeDianShiLianView:PlayCenterItemTween()
	if self.center_item_tween then
		return
	end

	RectTransform.SetAnchoredPositionXY(self.node_list.center_item_root.rect, 0, -40)
	self.node_list.center_item_root.canvas_group.alpha = 0

	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_move = self.node_list.center_item_root.rect:DOAnchorPosY(0, 0.5)
	local tween_alpha = self.node_list.center_item_root.canvas_group:DoAlpha(0, 1, 0.5)
	
	tween_sequence:Append(tween_move)
	tween_sequence:Join(tween_alpha)
	tween_sequence:OnComplete(function ()
		if not self.tween_yoyo then
			self.tween_yoyo = self.node_list.center_item_root.rect:DOAnchorPosY(20, 1.5)
			self.tween_yoyo:SetLoops(-1, DG.Tweening.LoopType.Yoyo)
		end

		self.center_item_tween:Kill()
		self.center_item_tween = nil
	end)

	self.center_item_tween = tween_sequence
end
--]]

--------------------------------------------------------------------------------

SiXiangTeDianShiLianItem = SiXiangTeDianShiLianItem or BaseClass(SiXiangSummonItem)

function SiXiangRewardItem:__delete()
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end
end

function SiXiangTeDianShiLianItem:PlayShowItemTween(center_num)
	if self.m_tween or not self.node_list then
		return
	end

	local pos_x, pos_y = self:GetPos()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, -pos_x, pos_y)
	self.node_list.tween_root.canvas_group.alpha = 0

	local index = self:GetIndex()
	if index > center_num then
		index = index - center_num
	end
	local time = 0.15 * index

	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_move = self.node_list.tween_root.rect:DOAnchorPosX(0, time)
	local tween_alpha = self.node_list.tween_root.canvas_group:DoAlpha(0, 1, time + 0.3)

	tween_sequence:Append(tween_move)
	tween_sequence:Join(tween_alpha)
	tween_sequence:OnComplete(function ()
		self.m_tween:Kill()
		self.m_tween = nil
	end)
	tween_sequence:SetEase(DG.Tweening.Ease.Linear)

	self.m_tween = tween_sequence
end

function SiXiangTeDianShiLianItem:RestTween()
	RectTransform.SetAnchoredPositionXY(self.node_list.tween_root.rect, 0, 0)
	self.node_list.tween_root.canvas_group.alpha = 1
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end
end