LieKunBossListView = LieKunBossListView or BaseClass(SafeBaseView)
function LieKunBossListView:__init()
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "layout_boss_list_view")
	self.view_layer = UiLayer.Pop

	self.reward_show_cell = {}
	self.box_reward_show_cell = {}
end

function LieKunBossListView:__delete()
end

function LieKunBossListView:ReleaseCallBack()
	if self.boss_list then
		self.boss_list:DeleteMe()
		self.boss_list = nil
	end

	if self.reward_show_cell then
		for k, v in pairs(self.reward_show_cell) do
			v:DeleteMe()
		end
		self.reward_show_cell = {}
	end
	if self.box_reward_show_cell then
		for k, v in pairs(self.box_reward_show_cell) do
			v:DeleteMe()
		end
		self.box_reward_show_cell = {}
	end
	self.lkboss_info_list = nil
	self.lk_boss_anim = nil
end

function LieKunBossListView:OpenCallBack()
end

function LieKunBossListView:CloseCallBack()
end

function LieKunBossListView:LoadCallBack()
	self.node_list["layout_commmon_second_root"].rect.sizeDelta = Vector2(1037, 609)
	self.node_list.title_view_name.text.text = Language.Guild.GuaiWuLieBiao

	self.boss_list = PerfectLoversListView.New(LieKunBossItemRender, self.node_list.ph_world_boss_list)
	self.boss_list.select_index = 1
	self.boss_list:SetSelectCallBack(BindTool.Bind1(self.BossLsitSelectCallBack, self))
	self.boss_list:SetDefaultSelectIndex(1)
	for i = 1, 2 do
		local ph = self.node_list["ph_rareitem_" .. i]
		local show_cell = ItemCell.New(ph)
		self.reward_show_cell[i] = show_cell
	end

	for i = 1, 2 do
		local ph = self.node_list["box_reword_" .. i]
		local show_cell = ItemCell.New(ph)
		self.box_reward_show_cell[i] = show_cell
	end

	self.lkboss_info_list = {}
	for i = 1, 4 do
		self.lkboss_info_list[i] = self.node_list['attr_' .. i].text
	end
end

-- boss属性
function LieKunBossListView:refreshPerBossInfo()
	if self.lkboss_info_list == nil then return end
	local list_data = GuildWGData.Instance:GetLieKunBossListCfg()
	local boss_info = list_data[self.boss_list:GetSelectIndex()]
	if boss_info == nil then return end

	local boss_data = BossWGData.Instance:GetMonsterInfo(boss_info.boss_id)
	local key = "boss_wu_atk"
	if boss_data.damage_type == 1 then
		key = "boss_fa_atk"
	end
	local add_value = GuildWGData.Instance:GetLieKunMonsterAttrCfg()
	local info_list = {
		{ "boss_hp",     math.floor(boss_data.hp * (1 + add_value / 10000)) },
		{ key,           math.floor(boss_data.gongji * (1 + add_value / 10000)) },
		{ "boss_defen",  math.floor(boss_data.fangyu * (1 + add_value / 10000)) },
		{ "boss_magdef", math.floor(boss_data.fa_fangyu * (1 + add_value / 10000)) },
	}
	for i, v in ipairs(self.lkboss_info_list) do
		v.text = Language.Boss.BossAttrName[info_list[i][1]] .. ToColorStr(info_list[i][2], COLOR3B.WHITE)
	end
end

function LieKunBossListView:refreshPerRareFall()
	if self.reward_show_cell == nil or self.reward_show_cell == nil then return end
	local list_data = GuildWGData.Instance:GetLieKunBossListCfg()

	local boss_info = list_data[self.boss_list:GetSelectIndex()]
	if boss_info == nil then return end
	local item_list = Split(boss_info.treasure_item_list, "|")

	for i = 1, #self.box_reward_show_cell do
		self.box_reward_show_cell[i]:SetData({ item_id = tonumber(item_list[i]) })
	end

	item_list = Split(boss_info.drop_item_list, "|")
	for i = 1, #item_list do
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(tonumber(item_list[i]))
		local gift_item = ItemWGData.Instance:GetGiftConfig(tonumber(item_list[i]))
		if gift_item and next(gift_item) ~= nil then
			self.reward_show_cell[i]:SetData({ item_id = gift_item.item_data[1].id, num = gift_item.item_data[1].num,
				is_bind = gift_item.item_data[1].isbind, param = gift_item.item_data[1].param })
		else
			self.reward_show_cell[i]:SetData({ item_id = tonumber(item_list[i]) })
		end
	end
end

function LieKunBossListView:BossLsitSelectCallBack(cell)
	if cell == nil then
		return
	end
	self:refreshPerBossInfo()
	self:refreshPerRareFall()
end

function LieKunBossListView:ShowIndexCallBack()
	self:Flush()
end

-- 刷新boss列表
function LieKunBossListView:refreshPerBossList()
	local list_data = GuildWGData.Instance:GetLieKunBossListCfg()
	if next(list_data) == nil then return end
	if self.boss_list then
		self.boss_list:SetDataList(list_data, 3)
	end
end

function LieKunBossListView:OnFlush()
	self:refreshPerBossList()
end

LieKunBossItemRender = LieKunBossItemRender or BaseClass(BaseRender)
function LieKunBossItemRender:__init()
end

function LieKunBossItemRender:__delete()

end

function LieKunBossItemRender:OnFlush()
	if nil == self.data then
		return
	end
	local liekunfb_player_info = GuildWGData.Instance:GetCrossLieKunFBPlayerInfo()
	local str = ""
	local cfg = ConfigManager.Instance:GetAutoConfig("monster_auto").monster_list[self.data.boss_id]
	local level = cfg.level
	level = level + liekunfb_player_info.boss_extra_level
	str = "Lv." .. level
	self.node_list.text_boss_level.text.text = str
	self.node_list.text_boss_name.text.text = cfg.name
	self.node_list.text_boss_name_hl.text.text = cfg.name

	self.node_list.select_image:SetActive(self:IsSelectIndex())
	self.node_list["img_boss"].image:LoadSprite(ResPath.GetBossIcon("wrod_boss_" .. self.data.big_icon))
end
