--预约
MarryYuYueView = MarryYuYueView or BaseClass(SafeBaseView)

function MarryYuYueView:__init()
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/marry_ui_prefab", "layout_marry_yuyue")
end

function MarryYuYueView:__delete()

end

function MarryYuYueView:ReleaseCallBack()
	if nil ~= self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
    end
    
    if nil ~= self.timer_quest1 then
		GlobalTimerQuest:CancelQuest(self.timer_quest1)
		self.timer_quest1 = nil
	end

	if self.yuyue_item then
		for k,v in pairs(self.yuyue_item) do
			v:DeleteMe()
		end
		self.yuyue_item = nil 
	end

	if self.marry_yuyue_list then
		self.marry_yuyue_list:DeleteMe()
		self.marry_yuyue_list = nil
	end

	if self.yuyue_handler_alert then
		self.yuyue_handler_alert:DeleteMe()
		self.yuyue_handler_alert = nil
	end

	if MarryWGData.Instance then
		MarryWGData.Instance:SetMarryTimeSeq(nil)
	end

	-- if self.my_head_cell then
	-- 	self.my_head_cell:DeleteMe()
	-- 	self.my_head_cell = nil
	-- end

	-- if self.love_head_cell then
	-- 	self.love_head_cell:DeleteMe()
	-- 	self.love_head_cell = nil
	-- end

    self.is_first_enter = false
    self.flag = nil
end

function MarryYuYueView:CloseCallBack()
	if MarryWGData.Instance then
		MarryWGData.Instance:SetMarryTimeSeq(nil)
    end
    
    if nil ~= self.timer_quest1 then
		GlobalTimerQuest:CancelQuest(self.timer_quest1)
		self.timer_quest1 = nil
    end
end

function MarryYuYueView:LoadCallBack()
	local asset_name,bundle_name = ResPath.GetMarryResPath2("new_marry_title_yuyue")
	-- self.node_list["title_name"].image:LoadSprite(asset_name,bundle_name,function ()
	-- 	self.node_list["title_name"].image:SetNativeSize()
	-- end)
	local role_name = GameVoManager.Instance:GetMainRoleVo().name
	local lover_name = RoleWGData.Instance.role_vo.lover_name
	self.node_list["lbl_role_name"].text.text = role_name
	self.node_list["lbl_lover_name"].text.text = lover_name
	self:CreateYuYueList()
	self:CreateYuYueCell()

	if self.timer_quest == nil then
 		self.timer_quest = GlobalTimerQuest:AddRunQuest(function()
			MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_FLAG)
			end, 60)
  	end

	self.node_list["btn_go_yuyue"].button:AddClickListener(BindTool.Bind(self.SelectYuYueHandler, self))
	--self.node_list["window_close"].button:AddClickListener(BindTool.Bind(self.Close, self))
end

function MarryYuYueView:ShowIndexCallBack()
    if self.timer_quest1 == nil then
        self.timer_quest1 = GlobalTimerQuest:AddRunQuest(function()
        local flag = MarryWGData.Instance:GetMarryTodayCanOrderTomorrow()
        if self.flag ~= nil and self.flag ~= flag then
            MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_FLAG)
        end
        self.flag = flag and 1 or 0
        end, 1)
    end
	local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
	if main_role_vo.lover_uid > 0 then
		BrowseWGCtrl.Instance:BrowRoelInfo(main_role_vo.lover_uid, BindTool.Bind1(self.SetRoleHeadImg, self))
	end
	self:Flush()
end

function MarryYuYueView:OnFlush()
	self:SetRoleHeadImg() --设置双方头像
	local role_msg_info = MarryWGData.Instance:GetYuYueRoleInfo()
	if nil == role_msg_info then
		return
	end

	local str = string.format(Language.Marry.WeddingCount, role_msg_info.marry_count or 0)
	self.node_list["rich_wedding_count"].text.text = str
	local yuyue_data, seq_fornt = MarryWGData.Instance:GetMarryYuYueInfo()
    
	if self.marry_yuyue_list then
        self.marry_yuyue_list:SetDataList(yuyue_data)
        self.marry_yuyue_list:RefreshActiveCellViews()
		--self.node_list["ph_yuyue_list"].scroller:RefreshAndReloadActiveCellViews(true)
	end

	--初始进入跳转一次即可(yuyue_data不会为nil会是{})10是当前可见的格子数
    if not self.is_first_enter and self.marry_yuyue_list and seq_fornt >= 10 then
        self.is_first_enter = true
        self.default_select_index = seq_fornt
        local all_num = #yuyue_data
        self.node_list["ph_yuyue_list"].scroller:ReloadData(seq_fornt/all_num)
        self.marry_yuyue_list:RefreshActiveCellViews()
        self.marry_yuyue_list:JumpToIndex(seq_fornt+1)
    end

    self.node_list["marry_yuyue_red"]:SetActive(MarryWGData.Instance:GetMarryYuyueRemind() == 1)
end

function MarryYuYueView:SetRoleHeadImg(protocol)

	if not protocol then return end
	MarryWGData.Instance:SetLoverInfo2(protocol)
	--自己的头像数据
	-- if not self.my_head_cell then
	-- 	self.my_head_cell = BaseHeadCell.New(self.node_list["my_head"])
	-- end
	--local role_vo = GameVoManager.Instance:GetMainRoleVo()
	-- local appearance = role_vo and role_vo.appearance
	-- local data = {fashion_photoframe = appearance.fashion_photoframe}
	-- data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	-- data.prof = role_vo.prof
	-- data.sex = role_vo.sex
	-- data.is_show_main = true
	-- self.my_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	-- self.my_head_cell:SetData(data)

	--对象的头像数据
	-- if not self.love_head_cell then
	-- 	self.love_head_cell = BaseHeadCell.New(self.node_list["love_head"])
    -- end
	--local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	--local role_base_info_ack = MarryWGData.Instance:GetLoverInfo2()
	
    -- if role_base_info_ack and lover_id > 0 then
	-- 	local role_vo = role_base_info_ack
	-- 	local appearance = role_vo and role_vo.appearance
	-- 	local data = {fashion_photoframe = appearance.fashion_photoframe}
	-- 	data.role_id = role_vo.role_id
	-- 	data.prof = role_vo.prof
	-- 	data.sex = role_vo.sex
	-- 	self.love_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	-- 	self.love_head_cell:SetData(data)
	-- end
end

--初始化奖励格子
function MarryYuYueView:CreateYuYueCell()
	local reward_item_list = MarryWGData.Instance:GetRewardItemData()
	if nil == self.yuyue_item then
		self.yuyue_item = {}
	end
	for k,v in pairs(reward_item_list) do
		if nil == self.yuyue_item[k] then
			self.yuyue_item[k] = ItemCell.New()
			self.yuyue_item[k]:SetInstanceParent(self.node_list["ph_cell"])
		end
		self.yuyue_item[k]:SetData(v)
	end
end

--初始化预约列表
function MarryYuYueView:CreateYuYueList()
	--防止重复生成
	if self.marry_yuyue_list then
		return
	end
	self.marry_yuyue_list = YuYueListView.New(MarryYuYueListItemRender, self.node_list["ph_yuyue_list"])
   	self.marry_yuyue_list:SetSelectCallBack(BindTool.Bind1(self.SelectYuYueListItemCallBack, self))
end

function MarryYuYueView:SelectYuYueListItemCallBack(item)
    if not item or not item:GetData() then return end
    self.selected_data = item:GetData()
    local role_msg_info = MarryWGData.Instance:GetYuYueRoleInfo()
    if self.selected_data.is_yuyue == 1 and 
    ((self.selected_data.seq == role_msg_info.param_ch4) or (self.selected_data.seq == role_msg_info.param_ch8))then
        self.node_list.yuyue_btn_text.text.text = Language.Marry.MarryBtn2  --邀请宾客
    else
        self.node_list.yuyue_btn_text.text.text = Language.Marry.MarryBtn5  --马上预约
    end
    MarryWGData.Instance:SetMarryTimeSeq(item:GetData().seq)
	self.marry_yuyue_list:RefreshActiveCellViews()
end

function MarryYuYueView:SelectYuYueHandler()
    local role_msg_info = MarryWGData.Instance:GetYuYueRoleInfo()
	if self.selected_data ~= nil then
		if self.selected_data.is_yuyue == 1 and ((self.selected_data.seq == role_msg_info.param_ch4) or (self.selected_data.seq == role_msg_info.param_ch8))then
        	MarryWGCtrl.Instance:OpenInviteView(self.selected_data.seq)
        	self:Close()
    	elseif 1 == self.selected_data.yuyue_time then
    		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YuYueTips)
    	else
        	if role_msg_info.marry_count < 1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.WeddingCountTips)
        	else
            	local seq = MarryWGData.Instance:GetMarryTimeSeq()     
            	if nil == seq or seq < 0 then
                	SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.YuYueTime)
            	else
                	self:OpenYuYueHandlerTips()
            	end
         	end
    	end
	end
    
end

--弹出提示：是否是否在xxx-xxx开启婚宴
function MarryYuYueView:OpenYuYueHandlerTips()
	local _, begin_time, end_time = MarryWGData.Instance:GetShowYuYueTime(MarryWGData.Instance:GetMarryTimeSeq())
	-- local yuyue_tips_cfg = MarryWGData.Instance:GetYuYueTime(MarryWGData.Instance:GetMarryTimeSeq())
	-- local time_table = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	-- local begin1, begin2 = math.modf(yuyue_tips_cfg.xunyou_end_time / 100)
	-- local end1, end2 = math.modf(yuyue_tips_cfg.end_time / 100)
	-- local begin_time = begin1 .. ":" .. begin2 * 100
	-- local end_time = end1 .. ":" .. end2 * 100
	local time = begin_time .. "-" .. end_time
	if not self.yuyue_handler_alert then
		self.yuyue_handler_alert = Alert.New()
		self.yuyue_handler_alert:SetOkString(Language.Common.Confirm)
		self.yuyue_handler_alert:SetCancelString(Language.Common.Cancel)
		self.yuyue_handler_alert:SetOkFunc(function ()
			MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_YUYUE, MarryWGData.Instance:GetMarryTimeSeq())
			MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_YUYUE_FLAG)
		end)
	end
	local str = string.format(Language.Marry.YuYueConf2, time)
	self.yuyue_handler_alert:SetLableString(str)
	self.yuyue_handler_alert:Open()
end

----------------------------------------------------
---YuYueListView
YuYueListView = YuYueListView or BaseClass(AsyncListView)

function YuYueListView:__init()
end

function YuYueListView:__delete()
end

function YuYueListView:SelectIndex(index)
	local data = self:GetItemAt(index):GetData()
	local time_table = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	local hour , min = math.modf(data.apply_time / 100)
	min = min * 100
	local yuyue_time = os.time({year=time_table.year, month=time_table.month, day=time_table.day, hour=hour, min=min, sec=0})
	if data.is_yuyue == 1 or TimeWGCtrl.Instance:GetServerTime() > yuyue_time then
		return 
	end

	self:SetSelectItem(self:GetItemAt(index), index)
end

---MarryYuYueListItemRender
MarryYuYueListItemRender = MarryYuYueListItemRender or BaseClass(BaseRender)
function MarryYuYueListItemRender:__init()	
	self.is_select = false
	self.interactable = nil
end

function MarryYuYueListItemRender:__delete()
	
end

function MarryYuYueListItemRender:LoadCallBack()
end

function MarryYuYueListItemRender:OnFlush()
	if not self.data then 
		return
	end
	-- local rich_wedding_count = "#0F9C1CFF"
	-- local color_orange = "#ED6127FF"
	-- local color_pink = "#9C160FFF"
	-- local color_gray = "#272727FF"
	--XUI.SetGraphicGrey(self.node_list["img_bg"],false)
	--self.node_list["img_heart"]:SetActive(false)
	self.node_list["ph_yuyue_item"].button.interactable = false				--按钮不可以使用
	self.interactable = false
	-- local begin1, begin2 = math.modf(self.data.xunyou_end_time / 100)
	-- local end1, end2 = math.modf(self.data.end_time / 100)
	-- local begin_time = begin1 .. ":" .. begin2 * 100
	-- local end_time = end1 .. ":" .. end2 * 100
	local _, begin_time, end_time = MarryWGData.Instance:GetShowYuYueTime(self.data.seq)
	local role_msg_info = MarryWGData.Instance:GetYuYueRoleInfo()
	local is_time_over = 1 == self.data.yuyue_time --时间是否错过 

    local flag = MarryWGData.Instance:GetMarryTodayCanOrderTomorrow()
    local str = flag and Language.Marry.Tomorrow or ""
	local bundel, asset = ResPath.GetJieHunImg("a3_qy_yyhl_d2b")
	if self.data.is_yuyue == 1 then
		if (self.data.seq == role_msg_info.param_ch4) or (self.data.seq == role_msg_info.param_ch8) then
			self.node_list["lbl_time_tips"].text.text = Language.Marry.MyYuYueTips  	--我们的预约
			self.node_list["lbl_time"].text.text = str .. begin_time .. "-" .. end_time
            --self.node_list["img_heart"]:SetActive(true)
            self.node_list["ph_yuyue_item"].button.interactable = true				--按钮可以使用
			self.interactable = true
			bundel, asset = ResPath.GetJieHunImg("a3_qy_yyhl_d3c")
		else
			self.node_list["lbl_time_tips"].text.text = Language.Marry.YiYuYue 	--已经被预约
			self.node_list["lbl_time"].text.text = str ..  begin_time .. "-" .. end_time
			bundel, asset = ResPath.GetJieHunImg("a3_qy_yyhl_d1a")
		end
	else
		if is_time_over then
			self.node_list["lbl_time_tips"].text.text = Language.Marry.YuYueTips  	--时间已过
			self.node_list["lbl_time"].text.text = str .. begin_time .. "-" .. end_time
			bundel, asset = ResPath.GetJieHunImg("a3_qy_yyhl_d3")
		else
			self.node_list["lbl_time_tips"].text.text = Language.Marry.CanYuYue  	--可预约
			self.node_list["lbl_time"].text.text =  str ..begin_time .. "-" .. end_time
			self.node_list["ph_yuyue_item"].button.interactable = true				--按钮可以使用
			self.interactable = true
		end
	end

	self.node_list.img_bg.image:LoadSprite(bundel, asset)

	local bg_name = "new_marry_yuyue_text_bottom"
	if is_time_over then
		bg_name = "new_marry_yuyue_text_bottom_1"
	end
	self:SetHL()
end

function MarryYuYueListItemRender:SetHL()
	local seq = MarryWGData.Instance:GetMarryTimeSeq()
	if seq == nil then
		if self.data.first_flag == self.data.seq then
			MarryWGData.Instance:SetMarryTimeSeq(self.data.first_flag)
			self.node_list["img_hl"]:SetActive(true)
		else
			self.node_list["img_hl"]:SetActive(false)
		end
	else
		self.node_list["img_hl"]:SetActive(self.data.seq == MarryWGData.Instance:GetMarryTimeSeq())
	end
end