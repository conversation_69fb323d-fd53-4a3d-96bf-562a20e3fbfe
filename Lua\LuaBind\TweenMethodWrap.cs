﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class TweenMethodWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>(typeof(TweenMethod));
		<PERSON><PERSON>("Linear", get_Linear, null);
		<PERSON><PERSON>("EaseIn", get_EaseIn, null);
		<PERSON><PERSON>("EaseOut", get_EaseOut, null);
		<PERSON><PERSON>("EaseInOut", get_EaseInOut, null);
		<PERSON><PERSON>("BounceIn", get_BounceIn, null);
		<PERSON><PERSON>("BounceOut", get_BounceOut, null);
		<PERSON><PERSON>("IntToEnum", IntToEnum);
		<PERSON>.<PERSON>();
		TypeTraits<TweenMethod>.Check = CheckType;
		StackTraits<TweenMethod>.Push = Push;
	}

	static void Push(IntPtr L, TweenMethod arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(TweenMethod), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Linear(IntPtr L)
	{
		ToLua.Push(L, TweenMethod.Linear);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EaseIn(IntPtr L)
	{
		ToLua.Push(L, TweenMethod.EaseIn);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EaseOut(IntPtr L)
	{
		ToLua.Push(L, TweenMethod.EaseOut);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_EaseInOut(IntPtr L)
	{
		ToLua.Push(L, TweenMethod.EaseInOut);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BounceIn(IntPtr L)
	{
		ToLua.Push(L, TweenMethod.BounceIn);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_BounceOut(IntPtr L)
	{
		ToLua.Push(L, TweenMethod.BounceOut);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		TweenMethod o = (TweenMethod)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

