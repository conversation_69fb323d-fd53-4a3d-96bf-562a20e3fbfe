TianShenBaoXiaRewardPanel = TianShenBaoXiaRewardPanel or BaseClass(SafeBaseView)

function TianShenBaoXiaRewardPanel:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true, true)
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	local bundle_name = "uis/view/tianshen_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_result_panel")
	self:AddViewResource(0, bundle_name, "layout_ts_baoxia_reward")

	self.view_name = "TianShenBaoXiaRewardPanel"
end

function TianShenBaoXiaRewardPanel:__delete()

end

function TianShenBaoXiaRewardPanel:ReleaseCallBack()
	if self.reward_item_list then
		for k,v in pairs(self.reward_item_list) do
			v:DeleteMe()
		end
		self.reward_item_list = nil
	end

	if self.one_award_item_cell then
		self.one_award_item_cell:DeleteMe()
		self.one_award_item_cell = nil
	end 
	if self.tween_root_canvas_group then
		self.tween_root_canvas_group = nil
	end

	if self.one_tween_sequence then
		self.one_tween_sequence:Kill()
		self.one_tween_sequence = nil
	end

	self.is_reward_anim_playing = nil
end

function TianShenBaoXiaRewardPanel:CloseCallBack()
	self:StopTween()
	self:ReadyItemsPlayTween()
	TianShenWGCtrl.Instance:TryToResetTSBoxModel()
end

function TianShenBaoXiaRewardPanel:SetInfo(info)
	self.data = info
end

function TianShenBaoXiaRewardPanel:LoadCallBack()
	self.is_reward_anim_playing = false
	-- self.node_list["btn_one_key_open"]:SetActive(false)

	self.reward_item_list = nil
	self.play_tween_index = 0

	if self.node_list.top_title_bg then
		self.node_list.top_title_bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_gxhd_djhd_gxhd"))
 	end

	self.node_list["btn_storage"].button:AddClickListener(BindTool.Bind(self.OnClickSendToGetAward, self, BAOXIA_SEND_GET_AWARD_TYPE.One))
	self.node_list["btn_one_more"].button:AddClickListener(BindTool.Bind(self.OnClickSendToGetAward, self, BAOXIA_SEND_GET_AWARD_TYPE.N_Max))
	self.node_list["btn_one_key_open"].button:AddClickListener(BindTool.Bind(self.StopTween, self))
	XUI.AddClickEventListener(self.node_list["bx_skip_anim_btn"], BindTool.Bind(self.OnClickTSBoxSkipAnimBtn, self))
	XUI.AddClickEventListener(self.node_list.btn_up_level, BindTool.Bind(self.OnClickTSBoxSkipGoToBtn, self))
	self.node_list["bx_skip_gou_img"]:SetActive(TianShenWGData.Instance:GetTSBoxIsSkipAnim())

    local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
	local mat_item_id = other_cfg.consume_item or 39144

	for i = 1, 2 do
		self.node_list["cost_money_" .. i].button:AddClickListener(function()--材料物品Tips
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = mat_item_id})
		end)
	end

	self:InitRewardList()
end

function TianShenBaoXiaRewardPanel:ShowIndexCallBack()
	self:ResetRewardPosRoot()
end

function TianShenBaoXiaRewardPanel:OnFlush(param_t)
	if not self.data then return end
	-- print_error("TianShenBaoXiaRewardPanel_OnFLush")
	self:SetPanelInfoShow()
	self:ResetRewardPosRoot()
	self.node_list["bx_skip_gou_img"]:SetActive(TianShenWGData.Instance:GetTSBoxIsSkipAnim())
	if #self.data > 1 then
		self:StartPlayMultiItemAnim()
	else
		self:StartPlayOneItemAnim()
	end
end

function TianShenBaoXiaRewardPanel:SetPanelInfoShow()
	-- 设置材料
	local cur_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
	local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
	local my_mat_count = TianShenWGData.Instance:GetGodHoodDrawLeftMat()
	local is_can_uplevel = TianShenWGData.Instance:GetGodHoodDrawIsCanUpLevel()

	local is_one_mat_enough = my_mat_count >= other_cfg.draw_cost
	local show_str_1 = CommonDataManager.ConverExpByThousand(my_mat_count) .. "/" .. (other_cfg.draw_cost * BAOXIA_SEND_GET_AWARD_TYPE.One)
	show_str_1 = ToColorStr(show_str_1, is_one_mat_enough and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED)
	local btn_str_1 = is_one_mat_enough and string.format(Language.TianShenBaoXia.BaoXia_Open_Btn, 1) or Language.Common.Confirm1
	self.node_list["text_cost_money_1"].text.text = show_str_1
	self.node_list["text_btn_open_1"].text.text = btn_str_1

	--设置开启N次按钮显示:次数满足一次性开启2个才显示多开按钮
	local is_n_mat_enough = my_mat_count >= (BAOXIA_SEND_GET_AWARD_TYPE.Two * other_cfg.draw_cost)
	self.node_list["btn_one_more"]:SetActive(is_n_mat_enough and not is_can_uplevel)
	self.node_list["btn_storage"]:SetActive(not is_can_uplevel)
	self.node_list["btn_up_level"]:SetActive(is_can_uplevel)

	if is_n_mat_enough then
		local can_open_num = math.floor(my_mat_count / other_cfg.draw_cost)
		can_open_num = can_open_num > BAOXIA_SEND_GET_AWARD_TYPE.N_Max and BAOXIA_SEND_GET_AWARD_TYPE.N_Max or can_open_num
		local next_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade + 1)
		if next_cfg then
			--策划需求:宝匣可抽取次数若超过升级次数时，只会显示升级前的次数
			local cur_time = TianShenWGData.Instance:GetGodHoodDrawTimes()
			local need_up_level_time = cur_cfg.max_draw_count - cur_time - 1
			can_open_num = can_open_num > need_up_level_time and need_up_level_time or can_open_num
			self.node_list["btn_one_more"]:SetActive(can_open_num > 1 and not is_can_uplevel)
		end
		local show_str_2 = CommonDataManager.ConverExpByThousand(my_mat_count) .. "/" .. (other_cfg.draw_cost * can_open_num)
		--
		show_str_2 = ToColorStr(show_str_2, is_n_mat_enough and COLOR3B.DEFAULT_NUM or COLOR3B.D_RED)
		self.node_list["text_cost_money_2"].text.text = show_str_2
		self.node_list["text_btn_open_2"].text.text = string.format(Language.TianShenBaoXia.BaoXia_Open_Btn, can_open_num)
	end
end

function TianShenBaoXiaRewardPanel:ResetRewardPosRoot()
	self.node_list["one_cell_pos"]:SetActive(false)
    self.node_list["one_award_pos"]:SetActive(false)
    self.node_list["multi_reward_root"]:SetActive(false)
end

--单抽动画播放
function TianShenBaoXiaRewardPanel:StartPlayOneItemAnim()
	--print_error("单抽动画播放 StartPlayOneItemAnim",self.data)
	if self.one_tween_sequence then
		return
	end

	if not self.data[1] or not self.data[1].item_id then
		print_error("self.data[1] is", self.data[1])
		return
	end

	-- local shenshi_base_cfg = TianShenWGData.Instance:GetTianshenItemInfo(self.data[1].item_id)
	-- if IsEmptyTable(shenshi_base_cfg) then
	-- 	return
	-- end
	
	self.node_list["one_award_pos"]:SetActive(true)
	self.node_list["multi_reward_root"]:SetActive(false)
	self.node_list["one_cell_eff_pos"]:SetActive(false)
	self.node_list["one_cell_mask_tween"].rect.sizeDelta = u3dpool.vec2(0, 40)
	--名字
	local item_name = ItemWGData.Instance:GetItemName(self.data[1].item_id)
	local color, item_color = ItemWGData.Instance:GetItemColor(self.data[1].item_id)
	self.node_list["one_cell_name_label"].text.text = ToColorStr(item_name, color)
	if not self.one_award_item_cell then
		self.one_award_item_cell = ItemCell.New(self.node_list["one_cell_pos"])
	end
	if not IsEmptyTable(self.data) then
		self.one_award_item_cell:SetData(self.data[1])
		self.node_list["one_cell_pos"]:SetActive(true)
		self.node_list["one_cell_pos"].transform.localScale = u3dpool.vec3(1.5, 1.5, 1.5)
	end

	if TianShenWGData.Instance:GetTSBoxIsSkipAnim() then--跳过动画
		self:StopOneRewardTween()
	else
		--单抽动画播放
		self.is_reward_anim_playing = true
		local is_show_spc_anim = item_color >= item_color
		local one_tween_sequence = DG.Tweening.DOTween.Sequence()
		if is_show_spc_anim then
			local tween_scale_big = self.node_list["one_cell_pos"].transform:DOScale(1.5, 0.4)
			local tween_scale_min = self.node_list["one_cell_pos"].transform:DOScale(1, 0.3)
			one_tween_sequence:Append(tween_scale_big)
			one_tween_sequence:Append(tween_scale_min)
			--特写特效
			--[[
			GlobalTimerQuest:AddDelayTimer(function()
				local eff_bundle, eff_asset = ResPath.GetEffectUi(Ui_Effect.UI_zhuanpanka_gj)
				local eff_trf = self.node_list["one_cell_pos"].transform
				EffectManager.Instance:PlayAtTransform(eff_bundle, eff_asset, eff_trf, 1, u3dpool.vec3(0, 0, 0), nil, u3dpool.vec3(0.3, 0.3, 0.3))
			end, 0.2)
			]]
		else
			local nor_tween = self.node_list["one_cell_pos"].transform:DOScale(Vector3(1, 1, 1), 0.3)
			one_tween_sequence:Append(nor_tween)
		end
	    one_tween_sequence:OnComplete(function()
	    	self.one_tween_sequence = nil
	    	self.node_list["one_cell_mask_tween"].transform:DOSizeDelta(u3dpool.vec2(166, 40), 0.4)

	    	--普通特效
			--[[
		    local eff_id = TIAN_SHEN_SHEN_SHI_MODEL_EFFECT[item_color]
			if eff_id then
				self.node_list["one_cell_eff_pos"]:SetActive(true)
				local eff_bundle, eff_asset = ResPath.GetEffectUi(eff_id)
		    	local eff_trf = self.node_list["one_cell_eff_pos"].transform
		    	EffectManager.Instance:PlayAtTransform(eff_bundle, eff_asset, eff_trf, 1, u3dpool.vec3(0, 0, 0))
			end
			]]
			self.is_reward_anim_playing = false
	    end)
	    self.one_tween_sequence = one_tween_sequence
	end

	TianShenWGData.Instance:ShowTSBoxRewardTip()
end

function TianShenBaoXiaRewardPanel:StopOneRewardTween()
	if self.one_tween_sequence then
		self.one_tween_sequence:Kill()
		self.one_tween_sequence = nil
	end
	if self.node_list["one_cell_pos"].gameObject.activeSelf then
		self.node_list["one_cell_mask_tween"].transform:DOSizeDelta(u3dpool.vec2(166, 40), 0.4)
		self.node_list["one_cell_pos"].transform.localScale = Vector3(1, 1, 1)
	end
end

--多抽动画播放
function TianShenBaoXiaRewardPanel:StartPlayMultiItemAnim()
	-- print_error("多抽动画播放 StartPlayMultiItemAnim")
	self.node_list["one_award_pos"]:SetActive(false)
	self.node_list["multi_reward_root"]:SetActive(true)
	self:RefreshView()
end

--点击抽奖
function TianShenBaoXiaRewardPanel:OnClickSendToGetAward(s_type)
	--动画播放中 弹提示
	if self.is_reward_anim_playing then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenBaoXia.BaoXia_Anim_Is_Playing)
		return
	end

	local cur_cfg = TianShenWGData.Instance:GetGodHoodDrawGradeCfg()
	local other_cfg = TianShenWGData.Instance:GetGodHoodDrawOtherCfg()
	local my_mat_count = TianShenWGData.Instance:GetGodHoodDrawLeftMat()
	local is_mat_enough = false
	local can_open_num = 1

	if s_type == BAOXIA_SEND_GET_AWARD_TYPE.One then--单次抽 一次
		is_mat_enough = my_mat_count >= other_cfg.draw_cost
	else--单次抽 N次,最高50
		can_open_num = math.floor(my_mat_count / other_cfg.draw_cost)
		can_open_num = can_open_num > BAOXIA_SEND_GET_AWARD_TYPE.N_Max and BAOXIA_SEND_GET_AWARD_TYPE.N_Max or can_open_num
		local next_cfg = TianShenWGData.Instance:GetGodHoodDrawShowRewardCfgByGrade(cur_cfg.grade + 1)
		if next_cfg then
			--策划需求:宝匣可抽取次数若超过升级次数时，只会显示升级前的次数
			local cur_time = TianShenWGData.Instance:GetGodHoodDrawTimes()
			local need_up_level_time = cur_cfg.max_draw_count - cur_time
			can_open_num = can_open_num > need_up_level_time and need_up_level_time or can_open_num
		end
		is_mat_enough = can_open_num > BAOXIA_SEND_GET_AWARD_TYPE.One
	end

	--判断材料是否足够
	if is_mat_enough then
		--请求类型  s_type
		self:ResetRewardPosRoot()
		TianShenWGCtrl.Instance:OnCSGodhoodDrawOperate(GODHOOD_DRAW_OPERATE_TYPE.GODHOOD_DRAW_OPERATE_TYPE_DRAW, can_open_num)
		if can_open_num > 10 then
			TianShenWGData.Instance:DelayFlushShenShiRed(1)
		end
	else
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.TianShenBaoXia.Mat_Not_Enough)
		self:Close()
	end
end

--初始化奖励格子
function TianShenBaoXiaRewardPanel:InitRewardList()
	local res_async_loader = AllocResAsyncLoader(self, "tsbox_shenshi_reward_item")
	res_async_loader:Load("uis/view/tianshen_prefab", "tsbox_shenshi_reward_item", nil,
		function(new_obj)
			if IsNil(new_obj) then
				print_error("TianShenBaoXiaRewardPanel new_obj is nil")
				return
			end

			local item_root = self.node_list["multi_reward_root"].transform
			local layout_group = self.node_list["multi_reward_root"].grid_layout_group
			local cell_x = layout_group.cellSize.x
			local cell_y = layout_group.cellSize.y
			local spacing_x = layout_group.spacing.x
			local spacing_y = layout_group.spacing.y

			local item_list = {}
			for i = 1, BAOXIA_SEND_GET_AWARD_TYPE.N_Max do
				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(item_root, false)
				item_list[i] = TSBoxShenShiRewardItem.New(obj)
				item_list[i]:SetIndex(i)
				item_list[i]:CalculateCenterPos(cell_x, cell_y, spacing_x, spacing_y)
			end

			self.reward_item_list = item_list
			self:RefreshView()
		end)
end

function TianShenBaoXiaRewardPanel:RefreshView()
	if not IsEmptyTable(self.reward_item_list) and not IsEmptyTable(self.data) then
		local reward_list = self.data
		local item_list = self.reward_item_list
		for i=1,#item_list do
			item_list[i]:SetData(reward_list[i])
		end
		
		if TianShenWGData.Instance:GetTSBoxIsSkipAnim() then--跳过动画
			self:StopTween(true)
		else
			if not self.is_reward_anim_playing then
				self:ReadyItemsPlayTween()
				self.reward_item_list[1]:PlayFlyTween()
				self.play_tween_index = 1
				self.is_reward_anim_playing = true
				-- self.node_list["btn_one_key_open"]:SetActive(true)
			end
		end
	end
end

function TianShenBaoXiaRewardPanel:ReadyItemsPlayTween()
	if not IsEmptyTable(self.reward_item_list) then
		for i = 1, #self.reward_item_list do
			self.reward_item_list[i]:ReadyPlayTween()
		end
	end
end

function TianShenBaoXiaRewardPanel:NextGetShenShiRewardTween()
	if self.is_reward_anim_playing then
		local index = self.play_tween_index + 1
		self.play_tween_index = index

		local cur_reward_list_count = #self.data
		if self.play_tween_index > cur_reward_list_count then
			self.is_reward_anim_playing = false
			-- self.node_list["btn_one_key_open"]:SetActive(false)
			TianShenWGData.Instance:ShowTSBoxRewardTip()
		end

		if self.reward_item_list[index] then
			self.reward_item_list[index]:PlayFlyTween()
		end
	end
end

function TianShenBaoXiaRewardPanel:StopTween(force_stop)
	if self.is_reward_anim_playing or force_stop then
		if not IsEmptyTable(self.reward_item_list) then
			local item_list = self.reward_item_list
			for i=1,#item_list do
				item_list[i]:RestItem()
			end
		end
		self:StopOneRewardTween()
		self.is_reward_anim_playing = false
		-- self.node_list["btn_one_key_open"]:SetActive(false)
		TianShenWGData.Instance:ShowTSBoxRewardTip()
	end
end

function TianShenBaoXiaRewardPanel:OnClickTSBoxSkipAnimBtn()
	local last_state = TianShenWGData.Instance:GetTSBoxIsSkipAnim()
	self.node_list["bx_skip_gou_img"]:SetActive(not last_state)
	TianShenWGData.Instance:SetTSBoxSkipAnimFalg(not last_state)
	TianShenWGCtrl.Instance:FlushTianShenBaoXiaView()
end

function TianShenBaoXiaRewardPanel:OnClickTSBoxSkipGoToBtn()
	ViewManager.Instance:Open(GuideModuleName.TianShenBaoXia)
	self:Close()
end

--------------------------------------------------------------------------------------

TSBoxShenShiRewardItem = TSBoxShenShiRewardItem or BaseClass(BaseRender)

function TSBoxShenShiRewardItem:__init()
	self.center_pos_x = 0
	self.center_pos_y = 0
end

function TSBoxShenShiRewardItem:__delete()
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end 
end

function TSBoxShenShiRewardItem:LoadCallBack()
	
end

function TSBoxShenShiRewardItem:OnFlush()
	local data = self:GetData()
	if IsEmptyTable(data) then
		self:SetVisible(false)
		return
	end
	self:SetVisible(true)
	self:OnFlushShenShiModel()
end

function TSBoxShenShiRewardItem:OnClickItem()
	
end

--设置神饰模型
function TSBoxShenShiRewardItem:OnFlushShenShiModel()
	local data = self:GetData()
	-- local shenshi_base_cfg = TianShenWGData.Instance:GetTianshenItemInfo(data.item_id)
	-- if IsEmptyTable(shenshi_base_cfg) then
	-- 	return
	-- end
	--名字
	local item_name = ItemWGData.Instance:GetItemName(data.item_id)
	local item_color = ItemWGData.Instance:GetItemColor(data.item_id)
	self.node_list["name_label"].text.text = ToColorStr(item_name, item_color)
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list["model_pos"])
	end
	self.item_cell:SetData(data)
	--self:ChangeItemEffect(shenshi_base_cfg.color_limit)
end

function TSBoxShenShiRewardItem:ChangeItemEffect(color)
	local eff_id = TIAN_SHEN_SHEN_SHI_MODEL_EFFECT[color]
	if eff_id then
		local eff_bundle, eff_asset = ResPath.GetEffectUi(eff_id)
		self.node_list["effect_pos"]:ChangeAsset(eff_bundle, eff_asset)
	end
end

function TSBoxShenShiRewardItem:PlayFlyTween()
	if self.m_tween then
		return
	end
	self.node_list["model_pos"]:SetActive(true)
	local tween_sequence = DG.Tweening.DOTween.Sequence()
	local tween_alpha = self.node_list["tween_root"].canvas_group:DoAlpha(0, 1, 0.2)
	self.node_list["name_mask_tween"].rect.sizeDelta = u3dpool.vec2(0, 40)

	local data = self:GetData()
	local is_show_spc_anim = false
	local shenshi_base_cfg = TianShenWGData.Instance:GetTianshenItemInfo(data.item_id)
	local color, item_color = ItemWGData.Instance:GetItemColor(data.item_id)
	-- if not IsEmptyTable(shenshi_base_cfg) and shenshi_base_cfg.color_limit then
	-- 	is_show_spc_anim = shenshi_base_cfg.color_limit >= 4
	-- end

	if item_color ~= nil then
		is_show_spc_anim = item_color >= 4
	end

	if is_show_spc_anim then
		local tween_move = self.node_list["tween_root"].rect:DOAnchorPos(u3dpool.vec3(0, 0, 0), 0.2)
		local tween_scale_big = self.node_list["tween_root"].transform:DOScale(1.5, 0.4)
		local tween_scale_min = self.node_list["tween_root"].transform:DOScale(1, 0.2)
		tween_sequence:Append(tween_scale_big)
		tween_sequence:Join(tween_alpha)
		tween_sequence:Append(tween_scale_min)
		tween_sequence:Append(tween_move)
		--特写特效
		GlobalTimerQuest:AddDelayTimer(function()
			self.node_list["best_effect_pos"]:SetActive(true)
			--[[
			local eff_bundle, eff_asset = ResPath.GetEffectUi(Ui_Effect.UI_zhuanpanka_gj)
    		local eff_trf = self.node_list["effect_pos"].transform
    		EffectManager.Instance:PlayAtTransform(eff_bundle, eff_asset, eff_trf, 1, u3dpool.vec3(0, 40, 0), nil, u3dpool.vec3(0.3, 0.3, 0.3))
    		--]]
		end, 0.2)
	else
		local tween_move = self.node_list["tween_root"].rect:DOAnchorPos(u3dpool.vec3(0, 0, 0), 0.2)
		local tween_scale_big = self.node_list["tween_root"].transform:DOScale(0.7, 0.1)
		local tween_scale_min = self.node_list["tween_root"].transform:DOScale(1, 0.1)
		tween_sequence:Append(tween_move)
		tween_sequence:Join(tween_alpha)
		tween_sequence:Append(tween_scale_big)
		tween_sequence:Append(tween_scale_min)
	end

	tween_sequence:OnComplete(function ()
		self.m_tween:Kill()
		self.m_tween = nil
		--普通特效
		self.node_list["effect_pos"]:SetActive(true)
	    --名字底动画
	    self.node_list["name_mask_tween"].transform:DOSizeDelta(u3dpool.vec2(166, 40), 0.4)

		TianShenWGCtrl.Instance:PlayNextShenShiRewardTween()
	end)

	self.m_tween = tween_sequence
end

-- 算下在布局组件中间的偏移点(先只考虑10个两行的情况)
function TSBoxShenShiRewardItem:CalculateCenterPos(cell_x, cell_y, spacing_x, spacing_y)
	local index = self:GetIndex()
	local row = 5											-- 多少个一行
	local row_num = math.ceil(index / row)					-- 行数
	local center_index = row / 2 + row * (row_num - 1) 		-- 这一行的中间数
	local center_row_num = 1
	local center_x = (cell_x + spacing_x) * (center_index - index + 0.5)
	local center_y = -(cell_y + spacing_y) * (center_row_num - row_num + 0.5)
	self.center_pos_x = center_x
	self.center_pos_y = center_y
end

function TSBoxShenShiRewardItem:ReadyPlayTween()
	self.node_list["model_pos"]:SetActive(false)
	RectTransform.SetAnchoredPositionXY(self.node_list["tween_root"].rect, self.center_pos_x, self.center_pos_y)
	self.node_list["tween_root"].canvas_group.alpha = 0

	self.node_list["best_effect_pos"]:SetActive(false)
	self.node_list["effect_pos"]:SetActive(false)
end

function TSBoxShenShiRewardItem:RestItem()
	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end
	RectTransform.SetAnchoredPositionXY(self.node_list["tween_root"].rect, 0, 0)
	self.node_list["tween_root"].canvas_group.alpha = 1
	self.node_list["tween_root"].transform.localScale = u3dpool.vec3(1, 1, 1)
	self.node_list["name_mask_tween"].transform:DOSizeDelta(u3dpool.vec2(166, 40), 0.4)
	self.node_list["model_pos"]:SetActive(true)

	self.node_list["best_effect_pos"]:SetActive(false)
	self.node_list["effect_pos"]:SetActive(false)
end