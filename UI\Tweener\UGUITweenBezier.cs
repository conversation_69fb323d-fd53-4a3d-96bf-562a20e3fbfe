﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;

[AddComponentMenu("UGUI/Tween/UGUI Tween Bezider")]
public class UGUITweenBezier : UGUITweener {

	[Header("Start pos")]
	public Transform from;
	[Header("End Pos")]
	public Transform to;
	[Header("Bezier center pos")]
	public Transform center;

	Transform mTrans;
	Vector3 mPos;
	Quaternion mRot;
	Vector3 mScale;

	protected override void OnUpdate (float factor, bool isFinished)
	{
		if (to != null)
		{
			if (mTrans == null)
			{
				mTrans = transform;
				mPos = mTrans.position;
				mRot = mTrans.rotation;
				mScale = mTrans.localScale;
			}

			if (from != null)
			{
				mTrans.position = GetBezierPoint (factor, from.position, center.position, to.position);
				mTrans.localScale = from.localScale * (1f - factor) + to.localScale * factor;
				mTrans.rotation = Quaternion.Slerp(from.rotation, to.rotation, factor);
			}
			else
			{
				mTrans.position = GetBezierPoint (factor, mPos, center.position, to.position);
				mTrans.localScale = mScale * (1f - factor) + to.localScale * factor;
				mTrans.rotation = Quaternion.Slerp(mRot, to.rotation, factor);
			}
		}
	}


	private Vector3 GetBezierPoint(float factor, Vector3 start, Vector3 center, Vector3 end)
	{
		return (1 - factor) * (1 - factor) * start + 2 * factor * (1 - factor) * center + factor * factor * end;
	}

	static public UGUITweenBezier Begin (GameObject go, float duration, Transform center, Transform to) { return Begin(go, duration, null, center, to); }

	static public UGUITweenBezier Begin (GameObject go, float duration, Transform from, Transform center, Transform to)
	{
		UGUITweenBezier comp = UGUITweener.Begin<UGUITweenBezier>(go, duration);
		comp.from = from;
		comp.to = to;
		comp.center = center;

		if (duration <= 0f)
		{
			comp.Sample(1f, true);
			comp.enabled = false;
		}
		return comp;
	}
}
