ExpFubenAlertView = ExpFubenAlertView or BaseClass(DayCountChangeView)

function ExpFubenAlertView:__init()
    self.view_layer = UiLayer.Pop
    self.view_name = "ExpFubenAlertView"
    self:SetMaskBg(true, true, nil, BindTool.Bind1(self.Close, self))
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(740, 384)})
    self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fubenexp_confirm_dialog")
end

function ExpFubenAlertView:ReleaseCallBack()
    DayCountChangeView.ReleaseCallBack(self)
    if self.vip_change_event then
        GlobalEventSystem:UnBind(self.vip_change_event)
        self.vip_change_event = nil
    end
    self.rich_dialog = nil
end

function ExpFubenAlertView:OnVipInfoChange()
    self:Flush()
end

function ExpFubenAlertView:LoadCallBack()
    DayCountChangeView.LoadCallBack(self)
    self.node_list["title_view_name"].text.text = Language.Common.AlertTitile
    self.rich_dialog = self.node_list["rich_dialog"]
    self:SetLableString(self.content_str)
    self.node_list["btn_OK"].button:AddClickListener(BindTool.Bind1(self.OnClickOK, self))
    self.node_list["btn_cancel"].button:AddClickListener(BindTool.Bind1(self.OnClickCancel, self))
    self.node_list["btn_vip"].button:AddClickListener(BindTool.Bind1(self.OnClickVip, self))
     --Vip信息改变
    self.vip_change_event = GlobalEventSystem:Bind(OtherEventType.VIP_INFO_CHANGE, BindTool.Bind(self.OnVipInfoChange, self))
end

function ExpFubenAlertView:OnFlush()
    self.node_list.des_text.text.text = Language.FuBenPanel.ToBeVip6
    local up_level = VipWGData.Instance:GetVIPZeroBuyCfg("arrive_level") or 0
    self.node_list.des_text:SetActive(VipWGData.Instance:GetVipLevel() < 6 and up_level > 0)
end

-- 设置内容
function ExpFubenAlertView:SetLableString(str)
	if nil ~= str and "" ~= str then
		self.content_str = str
		if self.rich_dialog then
			EmojiTextUtil.ParseRichText(self.rich_dialog.emoji_text, self.content_str, 20, COLOR3B.DEFAULT)
		end
	end
end

function ExpFubenAlertView:OnClickOK()
	if nil ~= self.ok_func then
		self.ok_func()
    end
    self:Close()
end

-- 设置确定回调
function ExpFubenAlertView:SetOkFunc(ok_func)
	self.ok_func = ok_func
end

-- 设置取消回调
function ExpFubenAlertView:SetCancelFunc(cancel_func)
	self.cancel_func = cancel_func
end

function ExpFubenAlertView:OnClickCancel()
	if nil ~= self.cancel_func then
		self.cancel_func()
    end
    self:Close()
end

function ExpFubenAlertView:OnClickVip()
    local tab_index = VipWGData.Instance:GetVIPZeorBuyIsOpenJump()
    ViewManager.Instance:Open(GuideModuleName.Vip, tab_index)
    self:Close()
end