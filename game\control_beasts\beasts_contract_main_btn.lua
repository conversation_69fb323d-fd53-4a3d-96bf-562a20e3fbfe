BeastsContractMainBtn = BeastsContractMainBtn or BaseClass(BaseRender)

function BeastsContractMainBtn:ReleaseCallBack()

end

function BeastsContractMainBtn:OnFlush()
    local is_open = ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_OPERA_REQ_CS)
    local can_show = ControlBeastsOADrawWGData.Instance:IsCanShowCurOADrawType()
    self.node_list.jxz:CustomSetActive(is_open and can_show)
end