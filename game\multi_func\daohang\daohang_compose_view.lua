DaoHangComposeView = DaoHangComposeView or BaseClass(SafeBaseView)

function DaoHangComposeView:__init()
	self:SetMaskBg(false, true)
	self:AddViewResource(0, "uis/view/multi_function_ui_prefab", "layout_muti_func_compose_bg")
	self:AddViewResource(0, "uis/view/multi_function_ui/daohang_prefab", "layout_daohang_compose")
end

function DaoHangComposeView:LoadCallBack()
	if not self.compose_item_list then
        self.compose_item_list = {}
		
        for i = 1, 2 do
            local cell = ItemCell.New(self.node_list["daohang_item_pos" .. i])
            self.compose_item_list[i] = cell
        end
    end

	if not self.target_item then
		self.target_item = ItemCell.New(self.node_list["daohang_target_item_pos"])
	end

	self.cell_list = {}
	self.big_type = -1
	self.small_type = -1
	self.load_bar_cell_complete = false
	self.node_list.title_view_name.text.text = Language.Charm.CharmComposeTitle
	XUI.AddClickEventListener(self.node_list.btn_daohang_compose, BindTool.Bind(BindTool.Bind(self.OnClickCompose, self)))
	XUI.AddClickEventListener(self.node_list.btn_charm_onekey_compose, BindTool.Bind(BindTool.Bind(self.OnClickOneKeyCompose, self)))

	self:LoadNavBar()
end

function DaoHangComposeView:ReleaseCallBack()
    if self.cell_list ~= nil then
        for k, v in pairs(self.cell_list) do
            for i, j in pairs(v) do
				j:DeleteMe()
			end
        end

        self.cell_list = nil
    end

	if self.compose_item_list ~= nil then
        for k, v in pairs(self.compose_item_list) do
            v:DeleteMe()
        end
		
        self.compose_item_list = nil
    end

	if self.target_item then
		self.target_item:DeleteMe()
		self.target_item = nil
	end

	self.load_bar_cell_complete = nil
	self.big_type = nil
	self.small_type = nil
	self.can_compose = false
end

function DaoHangComposeView:LoadNavBar()
	local data_list = MultiFunctionWGData.Instance:GetDaoHangComposeTypeCfg()

	for i = 1, #data_list do
		if not IsEmptyTable(data_list[i]) then
			if self.node_list['text_' .. i] then
				self.node_list['text_' .. i].text.text = data_list[i][0].name
			end

			if self.node_list['text_' .. i .. "_hl"] then
				self.node_list['text_' .. i.. "_hl"].text.text = data_list[i][0].name
			end
			self.node_list["SelectBtnTj" .. i]:SetActive(true)
			self.node_list["SelectBtnTj" .. i].accordion_element:AddValueChangedListener(BindTool.Bind(self.OnClickComposeBarHandler, self, i))
			self:LoadNavBarItemCell(i, i == #data_list)
		else
			self.node_list["SelectBtnTj" .. i]:SetActive(false)
		end
	end
end

function DaoHangComposeView:LoadNavBarItemCell(index, load_end)
	local res_async_loader = AllocResAsyncLoader(self, "daohang_compose_list_cell" .. index)
	local data_list = MultiFunctionWGData.Instance:GetDaoHangComposeSmallItemInfo(index)
	local item_count = #data_list

	res_async_loader:Load("uis/view/multi_function_ui_prefab", "muti_func_compose_list_cell", nil,
		function(new_obj)
			local item_vo = {}

			for i = 1, item_count do
				local obj = ResMgr:Instantiate(new_obj)
				local obj_transform = obj.transform
				obj_transform:SetParent(self.node_list["ListTj"..index].transform, false)
				obj:GetComponent("Toggle").group = self.node_list["ListTj" .. index].toggle_group
				local item_render = DaoHangComposeSmallTypeRender.New(obj)
				item_render.parent_view = self
				item_render:SetIndex(i)
				item_render:SetValueChangedCallBack(BindTool.Bind(self.OnClickAccordionChild, self))
				item_vo[i] = item_render

				if i == item_count and load_end then
					self.load_bar_cell_complete = true
				end
				obj:SetActive(true)
			end

			self.cell_list[index] = item_vo

			if self.load_bar_cell_complete then
				self:Flush()
			end
	end)
end

function DaoHangComposeView:OnClickComposeBarHandler(index, is_on)
	if nil == index or not is_on or self.big_type == index then
		return
	end

	self.big_type = index
	self.small_type = MultiFunctionWGData.Instance:GetDaoHangComposeSmallTypeSelect(index)
	self.cell_list[self.big_type][self.small_type]:OnSelectChange(true)
	self:FlushMidComposePanel()
end

function DaoHangComposeView:OnClickAccordionChild(item)
	if nil == item or nil == item.data then
		return 
	end

	self.small_type = item.index
	self:FlushMidComposePanel()
end

function DaoHangComposeView:OnClickCompose()
	self:OnCompose(false)
end

function DaoHangComposeView:OnClickOneKeyCompose()
	self:OnCompose(true)
end

function DaoHangComposeView:OnCompose(is_one_key)
	local can_compose = MultiFunctionWGData.Instance:GetDaoHangComposeSmallTypeRedmind(self.big_type, self.small_type)
	local data_cfg = MultiFunctionWGData.Instance:GetDaoHangComposeItemCfg(self.big_type, self.small_type)

	if not IsEmptyTable(data_cfg) then
		local item_num1 = 0
		local item_num2 = 0

		if data_cfg.equip_id1 > 0 then
			item_num1 = MultiFunctionWGData.Instance:GetDaoHangComposeStuffNum(data_cfg.equip_id1)
		end

		if data_cfg.stuff_id2 > 0 then
			item_num2 = ItemWGData.Instance:GetItemNumInBagById(data_cfg.stuff_id2)
		end

		if can_compose then
			local num = 1

			if is_one_key then
				local need_item_num1 = data_cfg.equip_num1
				local need_item_num2 = data_cfg.stuff_num2
				local num1 = (data_cfg.equip_id1 > 0 and need_item_num1 > 0) and math.floor(item_num1 / need_item_num1) or -1
				local num2 = (data_cfg.stuff_id2 > 0 and need_item_num2 > 0) and math.floor(item_num2 / need_item_num2) or -1
				num = (num1 > 0 and num2 > 0) and math.min(num1, num2) or math.max(num1, num2)
			end

			TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_hecheng, is_success = true, pos = Vector2(0, 0)})
			MultiFunctionWGCtrl.Instance:OnCSTaoistOperate(TAOIST_OPERATE_TYPE.COMP, data_cfg.equip_id, num)
		else
			if data_cfg.equip_id1 > 0 and item_num1 < data_cfg.equip_num1 then
				TipWGCtrl.Instance:OpenItem({item_id = data_cfg.equip_id1})
			elseif data_cfg.stuff_id2 > 0 and item_num2 < data_cfg.stuff_num2 then
				TipWGCtrl.Instance:OpenItem({item_id = data_cfg.stuff_id2})
			end
		end
	end
end

function DaoHangComposeView:FlushMidComposePanel()
	local data_cfg = MultiFunctionWGData.Instance:GetDaoHangComposeItemCfg(self.big_type, self.small_type)

	if not IsEmptyTable(data_cfg) then
		local remind = false
		local item_num1 = MultiFunctionWGData.Instance:GetDaoHangComposeStuffNum(data_cfg.equip_id1)
		local equip_enough = item_num1 >= data_cfg.equip_num1

		self.target_item:SetData({item_id = data_cfg.equip_id})
		self.compose_item_list[1]:SetFlushCallBack(function ()
			local right_text = ToColorStr(item_num1 .. "/" .. data_cfg.equip_num1, equip_enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
			self.compose_item_list[1]:SetRightBottomColorText(right_text)
			self.compose_item_list[1]:SetRightBottomTextVisible(true)
		end)

		self.compose_item_list[1]:SetData({item_id = data_cfg.equip_id1})

		if data_cfg.stuff_id2 > 0 then
			local item_num2 = ItemWGData.Instance:GetItemNumInBagById(data_cfg.stuff_id2)
			local stuff_enough = item_num2 >= data_cfg.stuff_num2

			self.compose_item_list[2]:SetFlushCallBack(function ()
				local right_text = ToColorStr(item_num2 .. "/" .. data_cfg.stuff_num2, stuff_enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
				self.compose_item_list[2]:SetRightBottomColorText(right_text)
				self.compose_item_list[2]:SetRightBottomTextVisible(true)
			end)

			self.compose_item_list[2]:SetData({item_id = data_cfg.stuff_id2})
			remind = equip_enough and stuff_enough
		else
			self.compose_item_list[2]:ClearAllParts()
			remind = equip_enough
		end

		self.node_list.btn_daohang_compose_remind:SetActive(remind)
		self.node_list.btn_charm_onekey_compose_remind:SetActive(remind)
	end
end

function DaoHangComposeView:OnFlush()
	if not self.load_bar_cell_complete then
		return
	end

	local big_type_data_list = MultiFunctionWGData.Instance:GetDaoHangComposeTypeCfg()
	for i = 1, #big_type_data_list do
		local data_list = MultiFunctionWGData.Instance:GetDaoHangComposeSmallItemInfo(i)

		for k, v in pairs(self.cell_list[i]) do
			v:SetData(data_list[k])
		end

		local remind = MultiFunctionWGData.Instance:GetDaoHangComposeBigTypeRedmind(i)
		self.node_list["root_remind" .. i]:SetActive(remind) 
	end

	self:SelectToggle()
	self:FlushMidComposePanel()
end

function DaoHangComposeView:SelectToggle()
	local select_bigtype, select_smalltype = MultiFunctionWGData.Instance:GetDaoHangComposeDefaultSelect(self.big_type, self.small_type)
	self.node_list["SelectBtnTj" .. select_bigtype].accordion_element.isOn = true
	self.cell_list[select_bigtype][select_smalltype]:OnSelectChange(true)
	self.big_type = select_bigtype
	self.small_type = select_smalltype
end

----------------------------------------------render---------------------------------------------------
DaoHangComposeSmallTypeRender = DaoHangComposeSmallTypeRender or BaseClass(BaseRender)
function DaoHangComposeSmallTypeRender:__init()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function DaoHangComposeSmallTypeRender:__delete()
	self.parent_view = nil
end

function DaoHangComposeSmallTypeRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local data = self.data
	self.node_list.normal_text.text.text = data.name or ""
	self.node_list.select_text.text.text = data.name or ""
	local remind = MultiFunctionWGData.Instance:GetDaoHangComposeSmallTypeRedmind(self.data.big_type, self.data.small_type)
	self.node_list.remind:SetActive(remind)
end

function DaoHangComposeSmallTypeRender:SetValueChangedCallBack(call_back)
	self.value_change_call_back = call_back
end

function DaoHangComposeSmallTypeRender:OnClickItem(is_on)
	if is_on then
		self.value_change_call_back(self)
	end	
end

function DaoHangComposeSmallTypeRender:OnSelectChange(is_select)
	self.node_list.select:SetActive(is_select)
	self.node_list.normal:SetActive(not is_select)
	self.view.toggle.isOn = is_select
end