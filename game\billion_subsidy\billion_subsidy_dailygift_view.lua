function BillionSubsidyView:InitDailyGiftRechargeView()
    self.libao_item_list = {}
	for i = 1 , 4 do
		if not self.libao_item_list[i] then
        	self.libao_item_list[i] = BillionDailyGiftItemRander.New(self.node_list["libao_item_"..i])
    	end
	end
    XUI.AddClickEventListener(self.node_list.baoxiang_btn, BindTool.Bind(self.BaoXiangOnClick, self))
end

function BillionSubsidyView:DailyGiftDoAnimation()
    local tween_info = UITween_CONSTS.EveryDayRechargeSys
    UITween.FakeHideShow(self.node_list["dailygift_root"])
    local tween = UITween.AlphaShow(GuideModuleName.BillionSubsidy, self.node_list["dailygift_root"], 0, tween_info.ToAlpha, tween_info.AlphaTime, tween_info.AlphaShowType)
end

function BillionSubsidyView:ShowIndexDailyGiftView()
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().origin_uid--存储用uid
	PlayerPrefsUtil.SetInt("everyday_recharge_dailygift" .. main_role_id .. open_day, 1)
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.EVERYDAY_RECHARGE_DAILYGIFT, 0)

    self:DailyGiftDoAnimation()
end

function BillionSubsidyView:ReleseDailyGiftRechargeView()

	if self.libao_item_list then
        for i,v in ipairs(self.libao_item_list) do
            v:DeleteMe()
        end
        self.libao_item_list = {}
    end

    if self.libao_daily_gift_box_tween then
		self.libao_daily_gift_box_tween:Kill()
		self.libao_daily_gift_box_tween = nil
	end
end

function BillionSubsidyView:FlushDailyGiftRechargeView()
    if not self.libao_item_list then
		return
	end

    local free_libao_can_lq = ServerActivityWGData.Instance:GetCanEveryDayGiftIsYlq()
    self.node_list.red_point:SetActive(free_libao_can_lq)  --免费礼包领取红点显示
    self.node_list.baoxiang_btn:SetActive(free_libao_can_lq)
    self.node_list.baoxiang_btn_bg:SetActive(free_libao_can_lq)

    if free_libao_can_lq then
		if self.libao_daily_gift_box_tween then
			self.libao_daily_gift_box_tween:Restart()
		else
            if self.libao_daily_gift_box_tween then
                self.libao_daily_gift_box_tween:Kill()
                self.libao_daily_gift_box_tween = nil
            end

			self.libao_daily_gift_box_tween = DG.Tweening.DOTween.Sequence()
			UITween.ShakeAnimi(self.node_list.libao_box_icon.transform, self.libao_daily_gift_box_tween, 1)
		end
	elseif self.libao_daily_gift_box_tween then
		self.libao_daily_gift_box_tween:Pause()
		self.node_list.libao_box_icon.transform.localRotation = Quaternion.identity
	end

    local daily_gift_info_list = ServerActivityWGData.Instance:GetDailyGiftInfo()

    if IsEmptyTable(daily_gift_info_list) then
        return
    end

    for i = 1 , 4 do
        self.node_list["libao_item_"..i]:SetActive(nil ~= daily_gift_info_list[i])

        if daily_gift_info_list[i] then
            self.libao_item_list[i]:SetData(daily_gift_info_list[i])
        end
	end
end

function BillionSubsidyView:BaoXiangOnClick()
    ServerActivityWGCtrl.Instance:SendEveryDayRecharge(EVERYDAY_RECHARGE_TYPR.OPER_TYPE_GET_LEIJI_CHONGZHI_FREE_REWARD ,2)
end


------------------------ 每日礼包ItemRander ---------------------------------------
BillionDailyGiftItemRander = BillionDailyGiftItemRander or BaseClass(BaseRender)
function BillionDailyGiftItemRander:LoadCallBack()
    if not self.show_cell then
        self.show_cell = ItemCell.New(self.node_list["item_node"])
    end

	if self.item_list_node == nil then
		self.item_list_node = AsyncBaseGrid.New()
		self.item_list_node:CreateCells({col = 3,
                        change_cells_num = 1,
                        list_view = self.node_list["item_list_node"],
				        itemRender = ItemCell})
		self.item_list_node:SetStartZeroIndex(true)
	end

    -- 按钮事件绑定
    XUI.AddClickEventListener(self.node_list.buy_btn, BindTool.Bind(self.BuyOnClick, self))
end

function BillionDailyGiftItemRander:ReleaseCallBack()
    if not self.show_cell then
        self.show_cell:DeleteMe()
        self.show_cell = nil
    end

	if nil ~= self.item_list_node then
        self.item_list_node:DeleteMe()
        self.item_list_node = nil
    end
end

function BillionDailyGiftItemRander:OnFlush()
	if not self.data then return end
    local daily_gift_list = ServerActivityWGData.Instance:GetEveryDayGiftList()
    if IsEmptyTable(daily_gift_list) then
        return
    end
    local cur_buy_nums = daily_gift_list[self.data.id+1]
    local is_can_buy = self.data.buy_limit_count > cur_buy_nums
    self.node_list.old_spend_text.text.text = string.format(Language.EverydayRecharge.OldSpend, self.data.dummy_price)
    self.node_list.spend_count_yuan.text.text = string.format(Language.EverydayRecharge.NowSpend, self.data.price)
    local limt_buy_str = is_can_buy and string.format(Language.EverydayRecharge.LimitBuyNumsGreen, cur_buy_nums, self.data.buy_limit_count)
                                    or string.format(Language.EverydayRecharge.LimitBuyNumsRed, cur_buy_nums, self.data.buy_limit_count)
    self.node_list.limt_buy_text.text.text = limt_buy_str
    self.node_list.fan_count_text.text.text = string.format(Language.EverydayRecharge.Discount, self.data.discount)
    self.node_list.cost_text.text.text = self.data.cost
    self.node_list.old_spend_text:SetActive(is_can_buy)
    self.node_list.fanli_bg:SetActive(is_can_buy)
    self.node_list.buy_btn:SetActive(is_can_buy)
    self.node_list.yishouqin_img:SetActive(not is_can_buy)
    self.node_list.double_img:SetActive(self.data.double_title == 1)
    self.item_list_node:SetDataList(self.data.reward_item)
    self.show_cell:SetData({is_bind = 1, item_id = self.data.gold_id, num = self.data.gold_num}) --展示显示的item物品
    --XUI.SetButtonEnabled(self.node_list.buy_btn, self.data.buy_limit_count > cur_buy_nums)

end

function BillionDailyGiftItemRander:BuyOnClick()
    local money = self.data.price
    local rmb_type = self.data.rmb_type
    local rmb_seq = self.data.rmb_seq
    RechargeWGCtrl.Instance:Recharge(money, rmb_type, rmb_seq)
end