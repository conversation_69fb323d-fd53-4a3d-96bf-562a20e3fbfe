GuradInvalidTimeView = GuradInvalidTimeView or BaseClass(SafeBaseView)

function GuradInvalidTimeView:__init()
	self.calc_active_close_ui_volume = false
	self:SetMaskBg(true,true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(700, 446)})
	self:AddViewResource(0, "uis/view/gurad_invalid_time_prefab", "layout_gurad_invalid_time")
	self.view_layer = UiLayer.Normal
end

function GuradInvalidTimeView:ReleaseCallBack()
	if self.xiaogui_item then
        self.xiaogui_item:DeleteMe()
        self.xiaogui_item = nil
    end

    if self.count_down_timer then
		GlobalTimerQuest:CancelQuest(self.count_down_timer)	
		self.count_down_timer = nil
	end

end

function GuradInvalidTimeView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.Equip.XiaoGuiGuoQi
	self.guard_id = EquipmentWGData.Instance:GetGuardFirstCfg().item_id
	XUI.AddClickEventListener(self.node_list["btn_renew"], BindTool.Bind1(self.ClickRenewHandler, self))
	self.xiaogui_item = ItemCell.New(self.node_list["ph_item"])
end

function GuradInvalidTimeView:CloseCallBack()
	MainuiWGCtrl.Instance:OpenNextView(NEED_OPEN_TIPS_TYPE.GURAD_INVAILD_TYPE)
end

function GuradInvalidTimeView:OnFlush(param_t)
	local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.guard_id)
	if xiaogui_cfg == nil then
		return
	end
	local show_youshi,youshi_time = XiaoGuiWGData.Instance:GetIsShowYouShiTime()
	self.xiaogui_item:SetData({item_id = self.guard_id})
	self.node_list.desc.text.text = Language.Equip.XiaoGuiXuFei5
	local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(self.guard_id)
	if not shop_cfg then
		return
	end
	self.node_list.money_img.image:LoadSprite(ResPath.GetCommonIcon(ResPath.GetMoneyIcon(2)))
	--self.node_list.btn_text.text.text = Language.Equip.RenewText_5
	self.node_list.need.text.text = shop_cfg.price
	self.node_list["title_img2"]:SetActive(show_youshi)
	self.node_list.youshi_time:SetActive(show_youshi)
	if show_youshi then
		local cur_time = TimeWGCtrl.Instance:GetServerTime()
		if cur_time < youshi_time then

			if self.count_down_timer then
				GlobalTimerQuest:CancelQuest(self.count_down_timer)
				self.count_down_timer = nil
			end
			self:CountDownYouShiTime()
			self.count_down_timer =  GlobalTimerQuest:AddRunQuest(function()
				self:CountDownYouShiTime()
			end,1)
		end
	end
end

function GuradInvalidTimeView:CountDownYouShiTime()
	local show_youshi,youshi_time = XiaoGuiWGData.Instance:GetIsShowYouShiTime()
	if show_youshi and self.node_list and self.node_list.youshi_time then
		local cur_time = TimeWGCtrl.Instance:GetServerTime()
		self.node_list.youshi_time.text.text = Language.Equip.ShouHuYouShi..TimeUtil.FormatSecond(youshi_time - cur_time)
	else
		if self.count_down_timer then
			GlobalTimerQuest:CancelQuest(self.count_down_timer)	
			self.count_down_timer = nil
		end
		if self.node_list and self.node_list["title_img"] then
			self.node_list["title_img"]:SetActive(not show_youshi)
			self.node_list["title_img2"]:SetActive(show_youshi)
			self.node_list.youshi_time:SetActive(false)
		end
	end
end


function GuradInvalidTimeView:ClickRenewHandler()
	local xiaogui_cfg = EquipmentWGData.GetXiaoGuiCfg(self.guard_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.guard_id)
	local btn_click = BindTool.Bind1(self.BtnCompleteFun, self)
	if xiaogui_cfg == nil then
		return
	end

	local function ok_func()
		ShopWGCtrl.Instance:SendShopBuy(item_cfg.id, 1, 0, 0, xiaogui_cfg.seq)
		self:Close()
	end



	if not ShopWGData.Instance:CheckMoneyToBuy(xiaogui_cfg.seq, 1, false, false) then
		local shop_cfg = ShopWGData.Instance:GetItemCfgBySeq(xiaogui_cfg.seq)
		if shop_cfg then
			local itemid = COIN_ITEM_ID[2]
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = itemid})
		end
		return
	end

	--local num = ShopWGData.Instance:GetMaxBuyNum(xiaogui_cfg.seq)
	local shop_cfg = ShopWGData.Instance:GetShopCfgItemId(self.guard_id)
	if not shop_cfg then
		return
	end
	-- if not ShopWGData.Instance:CheckBangYuToBuy(shop_cfg.seq, 1) then
	-- 	TipWGCtrl.Instance:OpenAlertTips(Language.Guild.BindGoldNo, ok_func)
	-- 	return
	-- end
	--ShopTip.Instance:SetData(item_cfg, nil, GameEnum.SHOP, nil, xiaogui_cfg.seq, nil, num, btn_click)
    --ViewManager.Instance:Open(GuideModuleName.Shop, shop_tab, "select_item_id", {item_id = self.guard_id})
    --ShopWGCtrl.Instance:SendShopBuy(item_cfg.id, 1, 0, 0, xiaogui_cfg.seq)
    --self:Close()
    local have_bind_num = RoleWGData.Instance:GetRoleInfo().bind_gold
    if have_bind_num < shop_cfg.price then
   		-- local str = Language.Guild.BindGoldNo--,shop_cfg.price)
    	-- TipWGCtrl.Instance:OpenAlertTips(str,ok_func)
    	TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
    else
    	ok_func()
    end
end

function GuradInvalidTimeView:BtnCompleteFun()
	self:Close()
end
