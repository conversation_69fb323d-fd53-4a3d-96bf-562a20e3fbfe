TabRuleTip = TabRuleTip or BaseClass(SafeBaseView)

local TitleHeight = 21 --标题高度
local TitleContentSpace = 8 --标题和文本的间距

function TabRuleTip:__init()
	self.view_layer = UiLayer.Pop
	self:SetMaskBg(true,true)
	self.text_height = 0
	self:LoadConfig()
	self.view_cache_time = 0
	self.default_index = 10
end

function TabRuleTip:__delete()
	
end

function TabRuleTip:ReleaseCallBack()
	if self.rule_list then
		self.rule_list:DeleteMe()
		self.rule_list = nil
	end
	if self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end
	self.data = nil
end

function TabRuleTip:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", 
	{vector2 = Vector2(0, 0), sizeDelta = Vector2(700, 474)})
	self:AddViewResource(0, "uis/view/tips/ruletip_prefab", "VerticalTabbar")
	self:AddViewResource(0, "uis/view/tips/ruletip_prefab", "tab_rule_tip")
	self.is_modal = false
end

function TabRuleTip:LoadCallBack()
	if not self.data then
		--print_error("必须设置数据打开界面")
		return
	end
	self.node_list.title_view_name.text.text = self.data.title
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:Init(self.data.tab_group,nil,"uis/view/tips/ruletip_prefab")
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		self.tabbar:SetInstanceParent(self.node_list.TabbarContainer)
		self.tabbar:SetLocalPosition(0,0,0)
	end
	self.rule_list = AsyncListView.New(TabRuleTipItem, self.node_list.ScrollView)
	self.rule_list:SetCellSizeDel(BindTool.Bind(self.GetCellSizeDel, self))
end

function TabRuleTip:ShowIndexCallBack(index)
	self:Flush(index)
end

function TabRuleTip:OnFlush()
	local index = self:GetShowIndex() / 10
	self.rule_list:SetDataList(self.data.content_list[index], 0)
	--self.node_list.title_text.text.text = self.data.title
end

--data:
--1、tab_group，tabber的竖按钮列表
--2、content_list, content_list = {
--						[1] = {{title = xxx1, content = xxx1},{title = xxx2, content = xxx2}},
--						[2] = {{title = xxx1, content = xxx1},{title = xxx2, content = xxx2}},
--					}
--3、title, 标题
function TabRuleTip:SetData(data, open_index)
	self.data = data
	self:Open(open_index or self.de_index)
end

function TabRuleTip:GetCellSizeDel(data_index)
	local content_index = self:GetShowIndex() / 10
    local data = (self.data.content_list[content_index] or {})[data_index + 1]
    self.node_list.TestText.text.text = data and data.content or ""
    UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.TestText.rect)
    local hight = math.ceil(self.node_list.TestText.rect.rect.height)
    return hight and hight + TitleHeight or 60
end

--------------------------TabRuleTipItem-------------------------------------
TabRuleTipItem = TabRuleTipItem or BaseClass(BaseRender)
function TabRuleTipItem:OnFlush()
	self:SetTitle()
	self:SetContent()
end
function TabRuleTipItem:SetTitle()
	self.node_list.rule_title.text.text = self.data.title
end
function TabRuleTipItem:SetContent()
	self.node_list.rule_text.text.text = self.data.content
end