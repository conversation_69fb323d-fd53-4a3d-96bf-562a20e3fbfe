require("game/lingyu_active/lingyu_active_wg_data")
require("game/lingyu_active/lingyu_active_view")

LingYuActiveWGCtrl = LingYuActiveWGCtrl or BaseClass(BaseWGCtrl)
function LingYuActiveWGCtrl:__init()
	if LingYuActiveWGCtrl.Instance then
		error("[LingYuActiveWGCtrl]:Attempt to create singleton twice!")
	end
	-- 单例
	LingYuActiveWGCtrl.Instance = self

	-- 创建data
	self.data = LingYuActiveWGData.New()
	-- 创建view
	self.view = LingYuActiveView.New(GuideModuleName.LingYuActiveView)

	-- 注册协议
	self:RegisterAllProtocols()
	-- 注册事件监听
end

function LingYuActiveWGCtrl:__delete()
	-- 销毁data
	self.data:DeleteMe()
	self.data = nil
	-- 销毁view
	self.view:DeleteMe()
	self.view = nil

	LingYuActiveWGCtrl.Instance = nil
end

-- 注册协议
function LingYuActiveWGCtrl:RegisterAllProtocols()
	-- 接收协议 与对应方法名绑定
	self:RegisterProtocol(SCLingYuPoolActiveAllInfo, "OnSCLingYuActiveInfo")
end

function LingYuActiveWGCtrl:OnSCLingYuActiveInfo(param)
	self.data:SetActiveInfo(param)
	if self.view:IsOpen() then
		self.view:Flush()
	end
end

function LingYuActiveWGCtrl:Open()
	self.view:Open()
end

--请求灵玉奖池的数据
function LingYuActiveWGCtrl:CSReqLingYuPoolInfo()
	local param_t = {}
	param_t.activity_type = ACTIVITY_TYPE.CROSS_LINGYUPOOL_ACTIVE
	param_t.opera_type = 1
	param_t.param_1 = 0
	param_t.param_2 = 0
	param_t.param_3 = 0
	ActivityWGCtrl.Instance:SendCSCrossChannelActivityRequest(param_t)
end

--界面如果打开就请求数据
function LingYuActiveWGCtrl:FlushLingYuView()
	if self.view:IsOpen() then
		self:CSReqLingYuPoolInfo()
	end
end
