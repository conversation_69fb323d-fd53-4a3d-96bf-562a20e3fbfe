GuideBossSceneLogic = GuideBossSceneLogic or BaseClass(CommonFbLogic)

function GuideBossSceneLogic:__init()

end

function GuideBossSceneLogic:__delete()
	if self.story then
		self.story:DeleteMe()
		self.story = nil
	end
end

function GuideBossSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	if old_scene_type == new_scene_type then return end
	self.story_view = StoryView.New(GuideModuleName.StoryView)
    self.story_view:SetOpenCallBack(function ()
        local step_list_cfg = ConfigManager.Instance:GetAutoConfig("story_auto").world_boss_story
        if nil ~= step_list_cfg then
            RobertManager.Instance:Start()
            self.story = Story.New(step_list_cfg, self.story_view)
            self.story:SetTrigger(S_STEP_TRIGGER.ENTER_SCENE)
        end
    end)
    self.story_view:Open()
	ViewManager.Instance:CloseAll()
	-- MainuiWGCtrl.Instance:SetOtherContents(true)
	MainuiWGCtrl.Instance:SetTaskPanel(false)
	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.Boss_text)
	FuBenPanelWGCtrl.Instance:OpenGuideBossView()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	Scene.SendGetAllObjMoveInfoReq()
end


function GuideBossSceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self, old_scene_type, new_scene_type)
	if old_scene_type == new_scene_type then return end
	FuBenPanelWGCtrl.Instance:CloseGuideBossView()
	MainuiWGCtrl.Instance:SetTaskPanel(true)
	-- MainuiWGCtrl.Instance:SetOtherContents(false)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	-- MainuiWGCtrl.Instance:ChangeTaskBtnName(Language.Task.task_text)
	ViewManager.Instance:CloseAll()
	 if nil ~= self.story_view then
        self.story_view:Close()
        self.story_view:DeleteMe()
        self.story_view = nil
    end
end

function GuideBossSceneLogic:IsEnemy(target_obj, main_role)
	return target_obj ~= nil and target_obj:IsMonster()
end

function GuideBossSceneLogic:RoleCanShowHp(role)
	return not role:IsRobot()
end

function GuideBossSceneLogic:GetGuajiCharacter()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		return Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end

	return nil
end