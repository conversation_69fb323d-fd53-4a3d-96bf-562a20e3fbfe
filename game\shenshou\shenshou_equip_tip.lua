ShenShouEquipTip = ShenShouEquipTip or BaseClass()

ShenShouEquipTip.FROM_NORMAL = 0                        
ShenShouEquipTip.FROM_SHENSHOU = 1 						-- 在神兽界面




ShenShouEquipTip.FROM_SHENSHOUBAG = 3 					-- 在神兽背包界面
ShenShouEquipTip.FROM_SHENSHOUBAGGRID = 2 				-- 在神兽背包装备格子界面

ShenShouEquipTip.FROM_QIANGHUA = 4 						-- 在强化界面
ShenShouEquipTip.FROM_EQUIMENT_HECHENG = 5 				-- 在合成界面
ShenShouEquipTip.FROM_LINK = 6 							-- 在传闻界面
ShenShouEquipTip.FROM_MARKET = 7 						-- 在集市界面
ShenShouEquipTip.FROM_MARKET_JISHOU = 8					-- 打开市场界面时，来自寄售
ShenShouEquipTip.FROM_MARKET_GOUMAI = 9 				-- 从市场界面打开，来自购买界面
ShenShouEquipTip.FROM_MARKET_SHANGJIA = 43             	-- 上架
ShenShouEquipTip.FROM_MARKET_SHANGJIA_CLICK = 100       -- 市场点击显示
ShenShouEquipTip.FROM_CONTRAST = 1000					-- 在比对界面

local add_per_t = {
	[1] = "%", 
	[2] = "%", 
	[3] = "%", 
	[4] = "%", 
	[5] = "%", 
	[6] = "%", 
	[13] = "%", 
	[14] = "%", 
	[15] = "%", 
	[16] = "%", 
	[17] = "%", 
	[18] = "%", 
	[20] = "%", 
	[22] = "%", 
	[23] = "%",
}

function ShenShouEquipTip:__init(instance)
	self.ignore_btn = false
	self.base_tips = BaseTip.New(instance)
	self.cell = self.base_tips:GetShenshouEquipCell()
end

function ShenShouEquipTip:__delete()
	if self.base_tips then
		self.base_tips:DeleteMe()
		self.base_tips = nil
	end
	self.ignore_btn = nil

	if self.take_off_alert then
		self.take_off_alert:DeleteMe()
		self.take_off_alert = nil
	end

	if self.take_on_alert then
		self.take_on_alert:DeleteMe()
		self.take_on_alert = nil
	end

	self.cell = nil
end

function ShenShouEquipTip:SetData(data, fromView, param_t)
	if not data then
		return
	end
	self.base_tips:Reset()
	self.data = data
	self.fromView = fromView or ShenShouEquipTip.FROM_NORMAL
	self.handle_param_t = param_t or {}
	self:OnFlush()
end

function ShenShouEquipTip:OnFlush()
	self:ShowTipContent()
	self:ShowOperationState()
end

function ShenShouEquipTip:ShowTipContent()
	if not self.cell then
		return
	end
	
	local shenshou_equip_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(self.data.item_id)
	self.shenshou_equip_cfg = shenshou_equip_cfg
	if nil == shenshou_equip_cfg then
		return
	end
	self.cell:SetItemTipFrom(ShenShouEquipTip.FROM_MARKET_SHANGJIA)
	self.cell:SetData(self.data)

	local name  = shenshou_equip_cfg.name or ""
	local strength_level = self.data.strength_level or 0
	if strength_level > 0 then
		name = string.format("%s+%d", name, strength_level)
	end

	local color = shenshou_equip_cfg.quality
	self.base_tips:SetItemName(ToColorStr(name, ITEM_COLOR[color]))

	local part = Language.ShenShou.ZhuangBeiLeiXing[shenshou_equip_cfg.slot_index] or ""
	local normal_info = {}
	normal_info[1] = {name = Language.F2Tip.TypeStr, label = part}
	self.base_tips:SetNormalAttribute(normal_info)
	self.base_tips:SetTopColorBg(shenshou_equip_cfg.quality)
	-- self.base_tips:SetOrnamentImage(0, 0)

	self:ParseEquip(shenshou_equip_cfg)

	if color >= GameEnum.ITEM_COLOR_SHINING_GOLD then
		local bundle_name, asset_name = ResPath.GetWuPinKuangEffectUi(TIPS_KUANG_QUALITY_EFFECT[color])
		self.base_tips:SetTipsPanelEffectShow(bundle_name, asset_name)
	end
end

function ShenShouEquipTip:ShowOperationState()
	if self.ignore_btn then
		return
	end

	local btn_info_list = {}
	local function set_btn_info(index, btn_name)
		local btn_info = {btn_name = btn_name or Language.ShenShou.ButtonLabel[index], btn_click = BindTool.Bind(self.OperationClickHandler, self, index)}
		btn_info_list[#btn_info_list + 1] = btn_info
	end

	if self.fromView == ShenShouEquipTip.FROM_SHENSHOUBAG then	     -- 神兽背包
		-- 穿戴按钮
		local unlock = ShenShouWGData.Instance:IsViewSelectSoulRingUnlock()
		if unlock then
			set_btn_info(0)
		end

		-- 拆解
		local data_list = ShenShouWGData.Instance:GetEquipDecomposeDataList(self.data)
		if not IsEmptyTable(data_list) then
			set_btn_info(1, Language.ShenShou.ButtonLabel[4])
		end

		-- 上架按钮
		if MarketWGData.Instance:CheckIsCanMarket(self.data) then       --判断是否可以上架
			set_btn_info(3, Language.ShenShou.ButtonLabel[5])
		end

		-- 销毁
		set_btn_info(8)
	elseif self.fromView == ShenShouEquipTip.FROM_MARKET_GOUMAI then    -- 市场购买
		-- 购买
		set_btn_info(0, Language.Market.GoumaiStr)
	elseif self.fromView == ShenShouEquipTip.FROM_MARKET_JISHOU then    -- 市场寄售
		set_btn_info(0, Language.Market.WorldSellName_1)
	elseif self.fromView == ShenShouEquipTip.FROM_MARKET_SHANGJIA then  --上架
		set_btn_info(0, Language.Market.WorldSellName)
	elseif self.fromView == ShenShouEquipTip.FROM_SHENSHOUBAGGRID then  -- 神兽背包装备格子
		set_btn_info(0, Language.ShenShou.ButtonLabel[2])   --卸下
	elseif self.fromView == ShenShouEquipTip.FROM_SHENSHOU then         -- 神兽界面
		set_btn_info(0, Language.ShenShou.ButtonLabel[2]) -- 卸下
		set_btn_info(1, Language.ShenShou.ButtonLabel[3]) -- 强化

		-- 有更好的装备 显示替换
		if ShenShouWGData.Instance:IsHasBetterShenShouEquip(self.data) then
			set_btn_info(2, Language.ShenShou.ButtonLabelEx)
		end
	end

	if QUICK_ADDITEM then
		set_btn_info(4, Language.ShenShou.ButtonLabelGM)
	end

	if #btn_info_list > 0 then
		self.base_tips:SetBtnsClick(btn_info_list)
	end
end

function ShenShouEquipTip:OperationClickHandler(index)
	if self.fromView == ShenShouEquipTip.FROM_MARKET_SHANGJIA then
		-- 上架
		MarketWGCtrl.Instance:OpenMarketTipItemView(self.data)
	elseif self.fromView == ShenShouEquipTip.FROM_MARKET_JISHOU then
		-- 撤回
		MarketWGCtrl.Instance:SendRemoveGoods(self.data.auction_index)
	elseif self.fromView == ShenShouEquipTip.FROM_MARKET_GOUMAI then
		-- 购买
		MarketWGCtrl.Instance:OpenBuyMarketGoodsAlert(self.data.auction_index, self.data.has_password, nil, self.data.item_id)
	elseif self.fromView == ShenShouEquipTip.FROM_SHENSHOUBAG then
		-- 神兽背包  穿戴 拆解 上架 销毁
		
		-- 穿戴
		if index == 0 then
			local view_select_soul_ring_seq = ShenShouWGData.Instance:GetViewSelectSoulRingSeq()
			local cur_equi_info = ShenShouWGData.Instance:GetShenShouEquipInfo(view_select_soul_ring_seq, self.shenshou_equip_cfg.slot_index)

			if cur_equi_info.item_id > 0 then
				TipWGCtrl.Instance:OpenCheckTodayAlertTips(Language.ShenShou.TakeOnTip, function ()
					ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_PUT_ON, view_select_soul_ring_seq, self.data.index,  self.shenshou_equip_cfg.slot_index)
				end, "ShenShouBagEquipTipChangeEquip", Language.ShenShou.CheckBoxText)
			else
				ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_PUT_ON, view_select_soul_ring_seq, self.data.index,  self.shenshou_equip_cfg.slot_index)
			end
		elseif index == 3 then
			-- 上架
			local is_market = MarketWGData.Instance:CheckIsCanMarket(self.data)
			local btn_callback_event = {}
			local is_open = FunOpen.Instance:GetFunIsOpened(FunName.OtherMarket)
			if 0 == self.data.is_bind and is_market and is_open then
				self.data.knapsack_type = KNAPSACK_TYPE.SHENSHOU
				ViewManager.Instance:CloseAll()
        		ShenShouWGCtrl.Instance:OpenShenShouEquipTip(self.data, ShenShouEquipTip.FROM_MARKET_SHANGJIA)
        		ViewManager.Instance:Open(GuideModuleName.Market, TabIndex.market_sell)
			end
		elseif index == 8 then
			ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.DESTROY, self.data.index)
		elseif index == 1 then
			-- 拆解
			local data_list = ShenShouWGData.Instance:GetEquipDecomposeDataList(self.data)
			local label_str1 = Language.Common.EquipDecompose .. "\n"
			local label_str2 = ""
			local num = data_list[2] or 0
			for k,v in pairs(data_list) do
				if k % 2 ~= 0 then
					local item_cfg
					local color
					if k >= 3 then
						item_cfg = ItemWGData.Instance:GetItemConfig(v)
						color = ITEM_COLOR[item_cfg.color]
					else
						item_cfg = ShenShouWGData.Instance:GetShenShouEqCfg(v)
						color = ITEM_TIP_COLOR[item_cfg.quality]
					end
					if label_str2 == "" then
						label_str2 = string.format("<color=%s>%s</color>", color, item_cfg.name)
					else
						local s = string.format("<color=%s>%s</color>", color, item_cfg.name)
						label_str2 = string.format("%s%s", label_str2, s)
					end
				else
					if data_list[k + 1] ~= nil then
						label_str2 = label_str2 .. " * " .. v .. "、"
					else
						label_str2 = label_str2 .. " * " .. v
					end
				end
			end
			label_str1 = label_str1 .. "\n" .. Language.Common.EquipDecomposeCanGet .. label_str2
			local ok_func = function()
				local bag_data_list = ShenShouWGData.Instance:GetShenShouBagDataList()
				if #bag_data_list <= 200 - num then--200-3个格子
					ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_DECOMPOSE, self.data.index)
				else
					TipWGCtrl.Instance:OpenConfirmAlertTips(Language.ShenShou.EnoughTips, function ()
						if ShenShouWGData.Instance:IsSoulRingWearEquip() then
							ShenShouWGCtrl.Instance:OpenShenShouQiangHua()--打开强化面板
						else
							SysMsgWGCtrl.Instance:ErrorRemind(Language.ShenShou.SoulRingStrengthTip)
						end
					end)
				end
			end

			TipWGCtrl.Instance:OpenAlertTips(label_str1, ok_func)
		end
	elseif self.fromView == ShenShouEquipTip.FROM_SHENSHOUBAGGRID then
		if index == 0 then
			-- self:GetShenShouTakeOffAlert(cur_shou_id)
			local view_select_soul_ring_seq = ShenShouWGData.Instance:GetViewSelectSoulRingSeq()
			ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_TAKE_OFF, view_select_soul_ring_seq, self.shenshou_equip_cfg.slot_index)
		end
	elseif self.fromView == ShenShouEquipTip.FROM_SHENSHOU then 
		-- 神兽界面   0卸下  1强化  2 替换打开背包界面   
		if index == 0 then
			local view_select_soul_ring_seq = ShenShouWGData.Instance:GetViewSelectSoulRingSeq()
			ShenShouWGCtrl.Instance:SendShenshouOperaReq(NEW_SHENSHOU_REQ_TYPE.SHENSHOU_REQ_TYPE_TAKE_OFF, view_select_soul_ring_seq, self.shenshou_equip_cfg.slot_index)
		elseif index == 1 then
			ShenShouWGCtrl.Instance:OpenShenShouQiangHua()--打开强化面板
		elseif index == 2 then
			ShenShouWGCtrl.Instance:OpenShenShouBagView()
		end
	elseif index == 4 then --gm获得
		local cur_data_conf = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		local gm_additem_num = 10
		
		if cur_data_conf and cur_data_conf.pile_limit and cur_data_conf.pile_limit == 999 then
			gm_additem_num = 999
		end
		SysMsgWGCtrl.SendGmCommand("additem", self.data.item_id .." ".. gm_additem_num .. " " .. 0)
	end

	ShenShouWGCtrl.Instance:CloseShenShouEquipTip()
end

function ShenShouEquipTip:ParseEquip(shenshou_equip_cfg)
	if nil == self.data then
		return
	end
	local base_shenshou_cfg = ShenShouWGData.Instance:GetShenshouBaseList(shenshou_equip_cfg.slot_index, shenshou_equip_cfg.quality)
	local qh_shenshou_cfg = ShenShouWGData.Instance:GetShenshouLevelList(shenshou_equip_cfg.slot_index, self.data.strength_level)

	--基础属性
	local base_attr_struct = AttributeMgr.GetAttributteByClass(base_shenshou_cfg)
	local qh_attr_struct = AttributeMgr.GetAttributteByClass(qh_shenshou_cfg)
	local attr_keys = AttributeMgr.SortAttribute()
	local common_pingfen_num = 0
	local comp_pingfen_num = 0
	local base_num = 0
	local base_attr_list = {}
	for k,v in pairs(attr_keys) do
		if base_attr_struct[v] and qh_attr_struct[v] then
			local base_value = math.floor(base_attr_struct[v])
			local add_value = math.floor(qh_attr_struct[v])
			if base_value > 0 then
				local temp = {attr_name = "", attr_value = ""}
				temp.attr_name = EquipmentWGData.Instance:GetAttrNameByAttrStr(v)
				base_num = TipWGData.Instance:GetCommonPingFenCfgByIndex(v)
				common_pingfen_num = common_pingfen_num + base_num * base_value
				comp_pingfen_num = comp_pingfen_num + base_num * base_value
				if add_value > 0 then
					comp_pingfen_num = comp_pingfen_num + base_num * add_value
					temp.attr_value = base_value
					temp.add_str = string.format("+%s", add_value)
				else
					temp.attr_value = base_value
					temp.add_str = ""
				end
				base_attr_list[#base_attr_list + 1] = temp
			end
		end
	end
	self.base_tips:SetBaseAttribute(base_attr_list)

	--卓越属性
	local zuoyue_attr_list = {}
	local title_text = Language.ShenShou.ZhuoYueShuXing
	if self.data.attr_list then
		for _,v in pairs(self.data.attr_list) do
			if v.attr_type > 0 and v.attr_value then
				local temp = {}
				base_num = TipWGData.Instance:GetSpecialPingFenCfgByOrder(0, v.attr_type, v.attr_value, 0, 2)
				common_pingfen_num = common_pingfen_num + base_num
				comp_pingfen_num = comp_pingfen_num + base_num
				local add_value = add_per_t[v.attr_type] and v.attr_value/100 .. "%" or v.attr_value
				local random_cfg = ShenShouWGData.Instance:GetRandomAttrCfg(shenshou_equip_cfg.quality, v.attr_type)
				if not IsEmptyTable(random_cfg) then
					if random_cfg.is_star_attr == 1 then
						temp.icon = "tips_xingxing1"
						temp.value = ToColorStr(add_value, COLOR3B.D_PURPLE)
						temp.label = ToColorStr(random_cfg.attr_show, COLOR3B.D_PURPLE)
					else
						temp.value = ToColorStr(add_value, COLOR3B.D_BLUE)
						temp.label = ToColorStr(random_cfg.attr_show, COLOR3B.D_BLUE)
					end
				end
				zuoyue_attr_list[#zuoyue_attr_list + 1] = temp
			end
		end
	else
		local legend_attr_list = ShenShouWGData.Instance:GetRecommendAttr(self.shenshou_equip_cfg.quality)
		if #legend_attr_list > 0 then
			title_text = Language.ShenShou.ZhuoYueShuXing .. string.format(Language.ShenShou.SuiJiZhuoYueShuXing, #legend_attr_list)
			for _,v in pairs(legend_attr_list) do
				local temp = {icon = "tips_xingxing1", label = ""}
				local add_value = add_per_t[v.attr_type] and v.attr_value/100 .. "%" or v.attr_value
				temp.value = ToColorStr(add_value, COLOR3B.D_PURPLE)
				temp.label = ToColorStr(Language.Role.EquipDesc1, COLOR3B.D_BLUE) .. "  " .. ToColorStr(v.attr_show, COLOR3B.D_PURPLE)
				zuoyue_attr_list[#zuoyue_attr_list + 1] = temp
			end
		end
	end

	-- 是否为预览状态  取随机属性做显示
	if self.fromView == ShenShouEquipTip.FROM_EQUIMENT_HECHENG then
		local legend_attr_list = ShenShouWGData.Instance:GeRandomAttrByHeCheng(self.data)
		title_text = Language.ShenShou.ZhuoYueShuXing .. string.format(Language.ShenShou.SuiJiZhuoYueShuXing, self.data.star_count or 0)
		for _,v in pairs(legend_attr_list) do
			local temp = {label = "", value = ""}
			local add_value = add_per_t[v.attr_type] and v.attr_value/100 .. "%" or v.attr_value
			temp.label = ToColorStr(Language.Role.EquipDesc1, COLOR3B.D_BLUE) .. "  " .. ToColorStr(v.attr_show, COLOR3B.D_PURPLE)
			temp.value = ToColorStr(add_value, COLOR3B.D_PURPLE)
			zuoyue_attr_list[#zuoyue_attr_list + 1] = temp
		end
	end

	if #zuoyue_attr_list > 0 then
		local info_list = {title_name = title_text, attr_list = zuoyue_attr_list}
		self.base_tips:SetXianpinAttribute(info_list)
	end

	self.base_tips:SetEquipSocre(string.format(Language.Tip.PingFen, TIPS_COLOR.SOCRE, math.floor(common_pingfen_num)))
	self.base_tips:SetSyntheticalSocre(string.format(Language.Tip.CompPingFen, TIPS_COLOR.SOCRE, math.floor(comp_pingfen_num)))
end

function ShenShouEquipTip:SetIndex(index)
	self.base_tips:SetIndex(index)
end

function ShenShouEquipTip:SetIgnoreBtn(_bool)
	self.ignore_btn = _bool
end

function ShenShouEquipTip:SetTopLeftIcon(name)
	self.base_tips:SetTopLeftIcon()
end
---------------------------------------------------------------------------------