NormalAreaGuideView = NormalAreaGuideView or BaseClass(SafeBaseView)

function NormalAreaGuideView:__init()
    self.view_layer = UiLayer.Guide
    self.reference_resolution = REFERENCE_RESOLUTION_TYPE.FHD
	self:AddViewResource(0, "uis/view/guide_ui_prefab", "NormalAreaGuideView")

    self.target_height = 0
	self.target_width = 0
end

function NormalAreaGuideView:SetDataAndOpen(module_name, click_obj)
    self.module_name = module_name
    self.click_obj = click_obj
    self.click_rect = self.click_obj:GetComponent(typeof(UnityEngine.RectTransform))

    if not self:IsOpen() then
        self:Open()
    else
        self:Flush()
    end
end

function NormalAreaGuideView:OpenCallBack()
    if self.module_name and "" ~= self.module_name then
        ViewManager.Instance:CloseNormalViewExceptViewName(self.module_name)
    end
end

function NormalAreaGuideView:LoadCallBack()
	self.strong_mask_1 = self.node_list["StrongMask"]						-- 强引导黑幕1（圆形）
	self.strong_mask_2 = self.node_list["StrongMask2"]						-- 强引导黑幕2（长方形）

	self.left = self.node_list["Left"]
	self.right = self.node_list["Right"]
	self.top = self.node_list["Top"]
	self.bottom = self.node_list["Bottom"]
	self.strong_guide = self.node_list["StrongGuide"]
	self.week_block = self.node_list["WeekBlock"]							--弱指引遮罩

	self.node_list.WeekBlock.button:AddClickListener(BindTool.Bind(self.WeekBlockClick, self))
	self.node_list.WeekBtn.button:AddClickListener(BindTool.Bind(self.StrongBlockClick, self))
	self.left.button:AddClickListener(BindTool.Bind(self.OtherClick, self))
	self.right.button:AddClickListener(BindTool.Bind(self.OtherClick, self))
	self.top.button:AddClickListener(BindTool.Bind(self.OtherClick, self))
	self.bottom.button:AddClickListener(BindTool.Bind(self.OtherClick, self))
	self.node_list.StrongBlock.button:AddClickListener(BindTool.Bind(self.StrongBlockClick, self))
end

function NormalAreaGuideView:ReleaseCallBack()
    self:CancelDelayClickStrongBtnTimer()
    self.module_name = nil
    self.click_obj = nil
    self.click_rect = nil
end

function NormalAreaGuideView:OtherClick()
	self:StrongBlockClick()
end

function NormalAreaGuideView:StrongBlockClick()
	self:CancelDelayClickStrongBtnTimer()

    if self.click_obj.button then
        self.click_obj.button.onClick:Invoke()
    elseif self.click_obj.toggle then
        self.click_obj.toggle.onValueChanged:Invoke(true)
    end

	self.node_list.Block:SetActive(true)
	self:Close()
end

function NormalAreaGuideView:ShowIndexCallBack(index)
	self.node_list.Block:CustomSetActive(false)
    self.node_list.eff:ChangeAsset("effects/prefab/ui/ui_yindao_prefab", "UI_yindao")

    self.node_list.GuideTextArrow:CustomSetActive(true)
    self.strong_mask_1:CustomSetActive(true)
    self.strong_mask_2:CustomSetActive(false)

    self.left:CustomSetActive(self.click_rect ~= nil)
    self.right:CustomSetActive(self.click_rect ~= nil)
    self.top:CustomSetActive(self.click_rect ~= nil)
    self.bottom:CustomSetActive(self.click_rect ~= nil)
    self.node_list.eff:CustomSetActive(true)
end

function NormalAreaGuideView:OnFlush()
    self:FlushStrong()
end

function NormalAreaGuideView:FlushStrong()
    if not self.click_rect then
        return
    end

    --获取指引按钮的屏幕坐标
	local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, self.click_rect.position)

	--转换屏幕坐标为本地坐标
	local rect = self.root_node:GetComponent(typeof(UnityEngine.RectTransform))
	local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(rect, screen_pos_tbl, UICamera, Vector2(0, 0))

    --计算高亮框的位置
	local height = self.left.rect.rect.height
	local width = self.left.rect.rect.width

	local click_real_rect = self.click_rect.rect
	local pivot = self.click_rect.pivot
	local btn_height = click_real_rect.height
	local btn_width = click_real_rect.width
	local pos_x = local_pos_tbl.x + (0.5 - pivot.x) * btn_width
	local pos_y = local_pos_tbl.y + (0.5 - pivot.y) * btn_height

	self.node_list.eff.rect.localPosition = Vector2(pos_x, pos_y)
	self.strong_mask_1.rect.localPosition = Vector2(pos_x, pos_y)
	self.strong_mask_2.rect.localPosition = Vector2(pos_x, pos_y)

    local kuang_height = btn_height
	local kuang_width = btn_width
	if not self.is_end_ani then
		if self.target_height == 0 then
			--记录宽高
			self.target_width = kuang_width + 400
			self.target_height = kuang_height + 400
		end

		if self.target_width <= kuang_width then
			self.target_height = 0
			self.target_width = 0
			self.is_end_ani = true
		else
			self.target_width = self.target_width - 40
			self.target_height = self.target_height - 40
			if self.target_height > kuang_height then
				kuang_height = self.target_height
				kuang_width = self.target_width
			end
		end
	else
		local max_width = kuang_width + 13.5
		local min_width = kuang_width
		--循环变化选中框大小
		if self.target_height == 0 then
			--记录宽高
			self.target_width = kuang_width
			self.target_height = kuang_height
		end
		if self.change_value == 0 then
			self.change_value = 1.5
		end
		if self.target_width > max_width then
			self.change_value = -1.5
		elseif self.target_width < min_width then
			self.change_value = 1.5
		end
		self.target_width = self.target_width + self.change_value
		self.target_height = self.target_height + self.change_value
		kuang_height = self.target_height
		kuang_width = self.target_width
	end

	--设置框
	self.node_list.WeekBtn.rect.localPosition = Vector2(pos_x, pos_y)
	self.node_list.WeekBtn.rect.sizeDelta = Vector2(kuang_width + 10, kuang_height + 10)

    self:AddDelayClickStrongBtnTimer()
end

function NormalAreaGuideView:ReSetStrongGuide()
	local rect = self.root_node:GetComponent(typeof(UnityEngine.RectTransform))
	local width = rect.rect.width
	local height = rect.rect.height

	self.left.rect:SetInsetAndSizeFromParentEdge(UnityEngine.RectTransform.Edge.Right, 0, width)
	self.right.rect:SetInsetAndSizeFromParentEdge(UnityEngine.RectTransform.Edge.Left, 0, width)

	self.top.rect:SetInsetAndSizeFromParentEdge(UnityEngine.RectTransform.Edge.Bottom, 0, height)
	self.top.rect:SetInsetAndSizeFromParentEdge(UnityEngine.RectTransform.Edge.Left, 0, width)

	self.bottom.rect:SetInsetAndSizeFromParentEdge(UnityEngine.RectTransform.Edge.Top, 0, height)
	self.bottom.rect:SetInsetAndSizeFromParentEdge(UnityEngine.RectTransform.Edge.Left, 0, width)
end

function NormalAreaGuideView:AddDelayClickStrongBtnTimer()
	self:CancelDelayClickStrongBtnTimer()

	if not self.delay_click_strong_timer then
		local auto_time = 5
		
		if self.cur_strong_guide then
			local img_line = nil
			local time_line = self.cur_strong_guide.gameObject.Find("time_line")

			if time_line ~= nil then
				img_line = time_line:GetComponent(typeof(UnityEngine.UI.Image))
			end

			self.delay_click_strong_timer = CountDown.Instance:AddCountDown(auto_time, 0.02,
				-- 回调方法
				function(elapse_time, total_time)
					if img_line == nil then
						local time_line = self.cur_strong_guide.gameObject.Find("time_line")
						if time_line then
							img_line = time_line:GetComponent(typeof(UnityEngine.UI.Image))
						end
					end
					if img_line then
						img_line.fillAmount = elapse_time/total_time
					end
				end,
				-- 倒计时完成回调方法
				function()
					self:StrongBlockClick()
				end
			)
		end
	end
end

-- 取消延迟点击强引导按钮的计时器
function NormalAreaGuideView:CancelDelayClickStrongBtnTimer()
	if self.delay_click_strong_timer and CountDown.Instance:HasCountDown(self.delay_click_strong_timer) then
        CountDown.Instance:RemoveCountDown(self.delay_click_strong_timer)
    end
	self.delay_click_strong_timer = nil
end

function NormalAreaGuideView:WeekBlockClick()
	self:Close()
end