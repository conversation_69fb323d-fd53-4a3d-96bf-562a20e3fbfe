﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_UIVariableBindWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.UIVariableBind), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>RegFunction("FindVariable", FindVariable);
		<PERSON><PERSON>RegFunction("BindVariables", BindVariables);
		L.RegFunction("UnbindVariables", UnbindVariables);
		L.RegFunction("RefreshVariableTable", RefreshVariableTable);
		L.RegFunction("__eq", op_Equality);
		<PERSON><PERSON>RegFunction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>ar("binded", get_binded, set_binded);
		L.RegVar("VariableTable", get_VariableTable, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int FindVariable(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			Nirvana.UIVariableBind obj = (Nirvana.UIVariableBind)ToLua.CheckObject<Nirvana.UIVariableBind>(L, 1);
			string arg0 = ToLua.CheckString(L, 2);
			Nirvana.UIVariable o = obj.FindVariable(arg0);
			ToLua.PushSealed(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int BindVariables(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariableBind obj = (Nirvana.UIVariableBind)ToLua.CheckObject<Nirvana.UIVariableBind>(L, 1);
			obj.BindVariables();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int UnbindVariables(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariableBind obj = (Nirvana.UIVariableBind)ToLua.CheckObject<Nirvana.UIVariableBind>(L, 1);
			obj.UnbindVariables();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RefreshVariableTable(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.UIVariableBind obj = (Nirvana.UIVariableBind)ToLua.CheckObject<Nirvana.UIVariableBind>(L, 1);
			obj.RefreshVariableTable();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_binded(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBind obj = (Nirvana.UIVariableBind)o;
			bool ret = obj.binded;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index binded on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_VariableTable(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBind obj = (Nirvana.UIVariableBind)o;
			Nirvana.UIVariableTable ret = obj.VariableTable;
			ToLua.PushSealed(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index VariableTable on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_binded(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			Nirvana.UIVariableBind obj = (Nirvana.UIVariableBind)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.binded = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index binded on a nil value");
		}
	}
}

