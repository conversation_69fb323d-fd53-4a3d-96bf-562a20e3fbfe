QunXianLuanDouSceneLogic = QunXianLuanDouSceneLogic or BaseClass(CommonActivityLogic)

function QunXianLuanDouSceneLogic:__init()

end

function QunXianLuanDouSceneLogic:__delete()

end

function QunXianLuanDouSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonActivityLogic.Enter(self, old_scene_type, new_scene_type)
	self:OpenActivitySceneCd(ACTIVITY_TYPE.QUNXIANLUANDOU)

	-- XuiBaseView.CloseAllView()

	-- 所有下坐骑(普通和战斗坐骑)
	MountWGCtrl.Instance:AllDownMount()

	-- QunXianLuanDouWGCtrl.Instance:OpenFollow()
	-- QunXianLuanDouWGCtrl.Instance:UpdataLuanDouFollow()
end

function QunXianLuanDouSceneLogic:Update(now_time, elapse_time)
	CommonActivityLogic.Update(self, now_time, elapse_time)
end

function QunXianLuanDouSceneLogic:GetRoleNameBoardText(role_vo)
	-- local role_kill = QunXianLuanDouData.GetSpecialToKill(role_vo.special_param)
	-- local role_side = QunXianLuanDouData.GetSpecialToSide(role_vo.special_param)
	-- local main_side = QunXianLuanDouData.GetSpecialToSide(GameVoManager.Instance:GetMainRoleVo().special_param)

	-- local t = {}
	-- t.color = CAMP_COLOR3B[role_side + 1]
	-- t.text = Language.QunXianLuanDouSideName[role_side] .. COMMON_CONSTS.POINT

	-- local is_camp = (main_side == role_side)
	-- local color = is_camp and COLOR3B.WHITE or COLOR3B.RED
	-- t.text = t.text .. "<color=" .. color .. ">" .. role_vo.name .. "</color>"

	-- if role_kill >= 5 then
	-- 	t.text = t.text .. "<color=" .. COLOR3B.YELLOW .. ">" .. string.format(Language.Dungeon.KillCount, role_kill) .. "</color>"
	-- end
	-- return t
end

function QunXianLuanDouSceneLogic:Out()
	CommonActivityLogic.Out(self)
	--QunXianLuanDouWGCtrl.Instance:CloseFollow()
end

function QunXianLuanDouSceneLogic:IsMonsterEnemy(target_obj, main_role)
	return false
end

-- 获取挂机打怪的敌人
function QunXianLuanDouSceneLogic:GetGuiJiMonsterEnemy()
	local x, y = Scene.Instance:GetMainRole():GetLogicPos()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	return Scene.Instance:SelectObjHelper(Scene.Instance:GetRoleList(), x, y, distance_limit, SelectType.Enemy)
end

-- 是否是挂机打怪的敌人
function QunXianLuanDouSceneLogic:IsGuiJiMonsterEnemy(target_obj)
	if nil == target_obj or target_obj:GetType() ~= SceneObjType.Role
		or target_obj:IsRealDead() or not Scene.Instance:IsEnemy(target_obj) then
		return false
	end
	return true
end

function QunXianLuanDouSceneLogic:IsRoleEnemy(target_obj, main_role)
	-- if QunXianLuanDouData.GetSpecialToSide(main_role:GetVo().special_param) ==
	-- QunXianLuanDouData.GetSpecialToSide(target_obj:GetVo().special_param) then			-- 同一边
	-- 	return false, Language.Fight.Side
	-- end
	return true
end