-------------------------------- 注意事项 --------------------------------
--修改此代码必须经过主程同意
-------------------------------------------------------------------------

-- startup
-- 确定要接着要load哪些lua文件
local UnityApplication = UnityEngine.Application
local SysFile = System.IO.File

local _smatch = string.match
local _sformat = string.format

IS_LOCLA_WINDOWS_DEBUG_EXE = false					--是否是本地的windows包调试
if RuntimeGUIMgr then
	IS_LOCLA_WINDOWS_DEBUG_EXE = RuntimeGUIMgr.Instance:IsGUIOpening()
end

IS_CHECK_GRAPHIC_MEMORY = true
IS_CHECK_CLASS_OBJ_COUNT = false

local flag = UnityEngine.PlayerPrefs.GetInt("COLLECT_GRAPHIC_MEM", 0)
if 1 == flag then
	IS_CHECK_GRAPHIC_MEMORY = true
	IS_CHECK_CLASS_OBJ_COUNT = true
end

local function Setup()
	ResUtil = require("lib/resmanager/res_util")
	require("utils/util")

	local develop_mode = require("editor/develop_mode")

	ResUtil.memory_debug = develop_mode:IsDeveloper() or IS_CHECK_GRAPHIC_MEMORY

	local base_cache_path_t = _sformat("%s/%s", UnityApplication.persistentDataPath, ResUtil.GetFileEncryptPath("BundleCache"))

	if GAME_ASSETBUNDLE then
		ResUtil.InitEncryptKey()
		if ResUtil.is_ios_encrypt_asset or ResUtil.is_android_encrypt_asset then
			local base_cache_path = _sformat("%s/%s", UnityApplication.persistentDataPath, ResUtil.GetFileEncryptPath("BundleCache"))
			ResUtil.SetBaseCachePath(base_cache_path)
		else
			ResUtil.SetBaseCachePath(_sformat("%s/%s", UnityApplication.persistentDataPath, "BundleCache"))
		end

		ResUtil:InitStreamingFilesInfo()
		ResMgr = require("lib/resmanager/bundle_loader"):new()
	else
		ResUtil.SetBaseCachePath(_sformat("%s/%s", UnityApplication.persistentDataPath, "BundleCache"))
		ResMgr = require("lib/resmanager/simulation_loader"):new()
	end

	AssetBundleMgr = require("lib/resmanager/assetbundle_mgr"):new()

	BundleCache = require("lib/resmanager/bundle_cache")
	DownloaderMgr = require("lib/resmanager/download_mgr"):new()
	AudioManager = require "lib/resmanager/audio_mgr"
	AudioManager.init()

	require("lib/resmanager/gameobjattach_event_handle")
	require("lib/resmanager/loadrawimage_event_handle")
	require("lib/resmanager/effect_event_handl")

	BundleCache:Init()

	ResPoolMgr = require("lib/resmanager/resource_pool_mgr"):new()
	ResMgr:LoadLocalLuaManifest("LuaAssetBundle/LuaAssetBundle.lua")
	ResMgr:LoadLocalManifest("AssetBundle.lua")
end

Setup()

require "main"
