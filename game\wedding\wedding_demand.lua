WeddingDeMandView = WeddingDeMandView or BaseClass(SafeBaseView)

function WeddingDeMandView:__init()
	self.view_layer = UiLayer.Pop
    self:SetMaskBg(true)

    self.view_name = "WeddingDeMandView"
    self:AddViewResource(0, "uis/view/wedding_ui_prefab", "layout_demand_wedding")
	self.btn_state_is_xunyou = false
end

function WeddingDeMandView:ReleaseCallBack()
	self.btn_state_is_xunyou = false

	if CountDownManager.Instance:HasCountDown("auto_enter_hunyan_endtime") then
		CountDownManager.Instance:RemoveCountDown("auto_enter_hunyan_endtime")
    end

	--[[
	if self.my_head_cell then
		self.my_head_cell:DeleteMe()
		self.my_head_cell = nil
	end

	if self.love_head_cell then
		self.love_head_cell:DeleteMe()
		self.love_head_cell = nil
	end
	]]

    self.cur_res_id = nil
end

function WeddingDeMandView:LoadCallBack()
	--需要倒计时
	self.is_need_auto_time = false

	WeddingWGCtrl.Instance:SendCSMarryHunyanOpera(HUNYAN_OPERA_TYPE.HUNYAN_GET_WEDDING_INFO)
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_GET_YUYUE_INFO) -- 获取预约列表信息
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_WEDDING_GET_ROLE_INFO)  -- 玩家信息
	MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.HUNYAN_GET_APPLICANT_INFO, 0, 0)

	XUI.AddClickEventListener(self.node_list["btn_demand"], BindTool.Bind1(self.OnBtnDemand, self)) 			--索要请帖
    XUI.AddClickEventListener(self.node_list["btn_goto_wendding"], BindTool.Bind1(self.OnBtnWedding, self))	    --参加巡游/参加婚礼
end

function WeddingDeMandView:ShowIndexCallBack()
	self:Flush()
end

function WeddingDeMandView:OnFlush(param_t)
	local marry_info = MarryWGData.Instance:GetCurWeddingInfo()
	if not marry_info or IsEmptyTable(marry_info) then
		return
	end

	if param_t then
		for k,v in pairs(param_t) do
			if k == "is_need_time" then
				self.is_need_auto_time = v.is_need_time
			end
		end
    end
   
	self:SetHunYanInfo(marry_info)
	self:SetRoleHeadInfo(marry_info)

	--这个按钮初次打开界面信息也许不完整，所以要放在刷新里边做控制
	local is_show = MarryWGData.Instance:IsShowXunYouBtn()
    self.node_list["btn_goto_wendding"]:SetActive(is_show)
end


function WeddingDeMandView:SetHunYanInfo( marry_info )
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
	local str = "" 	--显示时间
	local _, begin_time, end_time = MarryWGData.Instance:GetShowYuYueTime(marry_info.seq)
	--local wedding_time_cfg = MarryWGData.Instance:GetYuYueTime(marry_info.seq)
	local time_table = os.date("*t", TimeWGCtrl.Instance:GetServerTime())
	str = begin_time .. "-" .. end_time--string.format(Language.Marry.MarryDemand2, wedding_time_cfg.xunyou_end_time / 100, wedding_time_cfg.xunyou_end_time % 100, 
	--wedding_time_cfg.end_time / 100, wedding_time_cfg.end_time % 100)
	self.node_list["img_remind"]:SetActive(MarryWGData.Instance:GetWeddingApplicantRemind() > 0)

	if activity_info.status == ACTIVITY_STATUS.STANDY and marry_info.wedding_type == 2 or activity_info.status == ACTIVITY_STATUS.XUNYOU then
		--str = MarryWGData.Instance:GetShowXuyouTime(marry_info.seq)
		--暂时先显示为婚宴状态 巡游先屏蔽
		-- self.node_list["text_wedding"].text.text = Language.Marry.MarryBtn3  --参加巡游
		-- self.node_list["wedding_type"].text.text = Language.Marry.XunYouTime
		self.node_list["wedding_type"].text.text = Language.Marry.WeddingTime
		self.btn_state_is_xunyou = true
	else
		if self.is_need_auto_time then
			self:SetAutoEnterHunYanCd()
		else
			self.node_list["text_wedding"].text.text = Language.Marry.MarryBtn4  --参加婚宴
		end
		
		self.node_list["wedding_type"].text.text = Language.Marry.WeddingTime
		self.btn_state_is_xunyou = false
	end

	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if role_id == marry_info.role_id or role_id == marry_info.lover_role_id then
		self.node_list["text_demand"].text.text = Language.Marry.MarryBtn2  --邀请宾客
	else
		self.node_list["text_demand"].text.text = Language.Marry.MarryBtn1  --索要请柬
	end

	self.node_list["lbl_wedding_time"].text.text = str
end

function WeddingDeMandView:SetAutoEnterHunYanCd()
	if CountDownManager.Instance:HasCountDown("auto_enter_hunyan_endtime") then
		return
	end

	local end_time = MarryWGData.Instance:AuToEnterHunYanTime()
	if end_time > 0 then
		self:UpdatesyAutoEnterDownTime(1, end_time)
		CountDownManager.Instance:AddCountDown("auto_enter_hunyan_endtime", BindTool.Bind1(self.UpdatesyAutoEnterDownTime, self), BindTool.Bind1(self.CompletesyDownTime, self), nil, end_time, 1)
	else
		self.node_list["text_wedding"].text.text = Language.Marry.MarryBtn4  --参加婚宴
	end
end

function WeddingDeMandView:CompletesyDownTime()
	self.node_list["text_wedding"].text.text = Language.Marry.MarryBtn4  --参加婚宴
	self:OnBtnWedding()
end

function WeddingDeMandView:UpdatesyAutoEnterDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		local format_time = TimeUtil.Format2TableDHMS(total_time - elapse_time)
		self.node_list["text_wedding"].text.text = string.format(Language.Marry.MarryBtn4_1, format_time.s)  --自动参加婚宴
	end
end

--加载结婚双方信息
function WeddingDeMandView:SetRoleHeadInfo(marry_info)
	if marry_info == nil then
		print_error("加载结婚双方信息失败!")
		return
	end

	--[[
	--自己的头像数据
	if not self.my_head_cell then
		self.my_head_cell = BaseHeadCell.New(self.node_list["my_head"])
	end

	local my_data = {fashion_photoframe = marry_info.role_fashion_photforame}
	my_data.role_id = marry_info.role_id
	my_data.prof = marry_info.role_prof
	my_data.sex = marry_info.role_sex

	if my_data ~= nil then
		self.my_head_cell:SetImgBg(marry_info.role_fashion_photforame > 0)
		self.my_head_cell:SetData(my_data)
	end

	--对象的头像数据
	if not self.love_head_cell then
		self.love_head_cell = BaseHeadCell.New(self.node_list["love_head"])
    end

	local love_data = {fashion_photoframe = marry_info.lover_fashion_photforame}
	love_data.role_id = marry_info.lover_role_id
	love_data.prof = marry_info.lover_role_prof
	love_data.sex = marry_info.lover_role_sex

	if love_data ~= nil then
		self.love_head_cell:SetImgBg(marry_info.lover_fashion_photforame > 0)
		self.love_head_cell:SetData(love_data)
	end
	--]]

    self.node_list["lbl_name_1"].text.text = marry_info.role_name
    self.node_list["lbl_name_2"].text.text = marry_info.lover_role_name
    self.node_list["role_name"].text.text = string.format(Language.Marry.WeddingDemandDes1, marry_info.role_name)
	self.node_list["lover_role_name"].text.text = string.format(Language.Marry.WeddingDemandDes2, marry_info.lover_role_name)
end

function WeddingDeMandView:OnBtnDemand()
	local marry_info = MarryWGData.Instance:GetCurWeddingInfo() or {}
	local role_id = RoleWGData.Instance:InCrossGetOriginUid()
	if role_id == marry_info.role_id or role_id == marry_info.lover_role_id then
		MarryWGCtrl.Instance:OpenInviteView()
	else
		local seq = marry_info.seq
		MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.HUNYAN_OPERA_TYPE_APPLY, seq)
	end
end

function WeddingDeMandView:OnBtnWedding()
	local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.HUNYAN)
	if self.btn_state_is_xunyou then
		if activity_info.status == ACTIVITY_STATUS.XUNYOU then
			if MarryWGData.Instance:GetOwnIsXunyou() then
				self:Close()
				return
			end
			ViewManager.Instance:CloseAll()
			MarryWGCtrl.Instance:SetMoveXuyou(true)
			local flag = 2
			MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_XUNYOU_OBJ_POS, flag)
		else
			TipWGCtrl.Instance:ShowSystemMsg(Language.Marry.TipsXunYou)
		end
	else
		local id = WeddingWGData.Instance:GetScenceId()
		WeddingWGCtrl.Instance:SendJoinHunyan(id)
		-- TipWGCtrl.Instance:ShowSystemMsg(Language.Guild.GUILDJIUHUINOOPEN)
	end
end