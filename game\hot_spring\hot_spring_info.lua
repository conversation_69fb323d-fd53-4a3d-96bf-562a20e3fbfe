HotSpringInfo = HotSpringInfo or BaseClass(SafeBaseView)

local MAX_REFLUSH_OFFSET = 9 	--刷新延时

local HAS_RECEIVE = {-96,-60,-20}

function HotSpringInfo:__init()
	self:AddViewResource(0, "uis/view/hot_spring_ui_prefab", "layout_left_panel")
	
	self.active_close = false
	self.item_cell_list = {}
end

function HotSpringInfo:__delete()
end

function HotSpringInfo:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end
	self.complete_func = nil
	RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change_callback)

	ActivityWGData.Instance:UnNotifyActChangeCallback(self.activity_change_callback)
	self.activity_change_callback = nil
	self:CancelEnterDuckRaceTimer()
end

function HotSpringInfo:LoadCallBack()
	self:CreateCell()
	self.role_data_change_callback =  BindTool.Bind1(self.OnRoleDataChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change_callback, {"vip_level"})
	XUI.AddClickEventListener(self.node_list.btn_vip, BindTool.Bind1(self.OnClickVip, self))

	self.activity_change_callback = BindTool.Bind(self.InfoActivityChangeCallBack, self)
	ActivityWGData.Instance:NotifyActChangeCallback(self.activity_change_callback)

	self.node_list["coundown_panel"]:SetActive(false)
end

function HotSpringInfo:OnRoleDataChange(attr_name, value, old_value)
	if attr_name == "vip_level" then
		self:FlushVipAdd()
	end
end

function HotSpringInfo:FlushVipAdd()
	self.node_list.lbl_vip_score.text.text = HotSpringWGData.Instance:GetbVipExePercent()
end

function HotSpringInfo:ShowIndexCallBack()
	--放这里事防止界面还没卸载的时候重现打开了界面没有进行设置
	local callback = function ()
		local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
		self.node_list.layout_left_panel_root.transform:SetParent(parent.transform,false)
		self.node_list.layout_left_panel_root.transform.localScale = Vector3.one
		--self.node_list.layout_left_panel_root.transform.localPosition = Vector3(24, 0, 0)
		self.load_finish = true

		--Vip信息改变
		MainuiWGCtrl.Instance:AddBtnToFbIconGroup2Line(self.node_list.btn_vip)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,callback)
	--初始化数据
	self:Flush()
end

function HotSpringInfo:OpenCallBack()
	self.complete_func = BindTool.Bind(self.SetCompontentCountDown,self)
	-- self:OldExp(0)
end

function HotSpringInfo:CloseCallBack()
	if self.node_list.layout_left_panel_root then
		self.node_list.layout_left_panel_root:SetActive(false)
		self.node_list.layout_left_panel_root.transform:SetParent(self.root_node_transform,false)
	end

	if self.node_list.btn_vip then
		self.node_list.btn_vip:SetActive(false)
		self.node_list.btn_vip.transform:SetParent(self.root_node_transform,false)
	end

	-- self:OnOutWenQuanType()
	-- self:OldExp(0)
	UiInstanceMgr.Instance:ColseFBStartDown()
	CountDownManager.Instance:RemoveCountDown("kf_hot_spring_count_down")
end

--get set
function HotSpringInfo:OldExp(old_exp)
	if old_exp == nil then
		return self.old_exp
	end
	self.old_exp = old_exp
end

function HotSpringInfo:CreateCell()
	self.reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
	local reward_cfg = HotSpringWGData.Instance:GetHotSpringRewardCfg()
	self.Rewarditem_cells = {}
	if reward_cfg then
		local data_list = {}
		for i = 0, 10 do
			if reward_cfg[i] then
				table.insert(data_list, reward_cfg[i])
			else
				break
			end
		end
		self.reward_list:SetDataList(data_list, 3)
	end

	--self.node_list["img_yilingwan"].rect.anchoredPosition = Vector2(HAS_RECEIVE[#reward_cfg + 1],-26.6)
	if nil ~= self.delay_remove_snow_eff_target then
        GlobalTimerQuest:CancelQuest(self.delay_remove_snow_eff_target)
        self.delay_remove_snow_eff_target = nil
    end
end

-- SurplusNum
function HotSpringInfo:OnFlush()
	local other_cfg = HotSpringWGData.Instance:GetHotSpringOtherCfg()
	local anmo_times, intimidation_times = HotSpringWGData.Instance:GetHotspringPlayerInfo()

	if other_cfg.day_action_reward_max_count then
		local total_num = anmo_times + intimidation_times
		local is_visible = false
		if total_num >= other_cfg.day_action_reward_max_count then
			is_visible = true
		end
		self.node_list.img_yilingwan:SetActive(is_visible)
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role and main_role.vo.shuangxiu_state == 1 then
		self.node_list.lbl_shuangxiu_txt.text.text = Language.HotSpring.ShuangXiuJiaCheng
		self.node_list.lbl_shuangxiu_score.text.text = "50%"
	else
		self.node_list.lbl_shuangxiu_txt.text.text = Language.HotSpring.WeiShuangXiu
		self.node_list.lbl_shuangxiu_score.text.text = "0%"
	end

	local total_exp = HotSpringWGData.Instance:GetHotspringTotalExp()
	self.node_list.lbl_exp.text.text = CommonDataManager.ConverExp(total_exp)

	-- 采集次数
	local gather_times = HotSpringWGData.Instance:GetGatherInfo()
	local remain_count = other_cfg.gather_role_count - gather_times

	-- 按摩次数
	local anmo_times_num = other_cfg.game_day_max_times - anmo_times

	-- 泼热水次数
	local intimidation_times_num = other_cfg.game_day_max_times - intimidation_times

	local now_hudong_count = remain_count + anmo_times_num + intimidation_times_num
	local max_hudong_count = other_cfg.game_day_max_times * 2 + other_cfg.gather_role_count

	local color2 = now_hudong_count > 0 and COLOR3B.D_GREEN or COLOR3B.D_RED
	local str = string.format("%d/%d", now_hudong_count, max_hudong_count)
	str = ToColorStr(str, color2)
	self.node_list.rich_gather_reward.text.text = str

	self:FlushVipIcon()
	self:FlushVipAdd()
end

--倒计时结束提示
--@结束后显示提示：xx秒后刷新采集物
function HotSpringInfo:CompontentCountDown()
	local seconds = HotSpringWGData.Instance:GatherRefreshRemainTime() 	--采集物刷新秒数
	if seconds - TimeWGCtrl.Instance:GetServerTime() >= MAX_REFLUSH_OFFSET then
		seconds = seconds - MAX_REFLUSH_OFFSET
		if not CountDownManager.Instance:HasCountDown("kf_hot_spring_count_down") then
			CountDownManager.Instance:AddCountDown("kf_hot_spring_count_down",nil,self.complete_func,seconds,nil,1)
		end
		return
	else
		self:SetCompontentCountDown()
	end
end

function HotSpringInfo:SetCompontentCountDown()
	CountDownManager.Instance:RemoveCountDown("kf_hot_spring_count_down")
	local seconds = HotSpringWGData.Instance:GatherRefreshRemainTime() 	--采集物刷新秒数
	UiInstanceMgr.Instance:ShowFBStartDown3(2,seconds)
	if FuBenPanelWGCtrl.Instance:GetIsCountDowning() then
		UiInstanceMgr.Instance:ColseFBStartDown()
	end
end

function HotSpringInfo:OnBtnTipsClickHandler()
	RuleTip.Instance:SetContent(Language.Activity.DailyActAfterTips17, "玩法攻略")
end

function HotSpringInfo:HideFace()
	 if nil ~= self.delay_remove_snow_eff_target then
            GlobalTimerQuest:CancelQuest(self.delay_remove_snow_eff_target)
            self.delay_remove_snow_eff_target = nil
      end

    self.delay_remove_snow_eff_target = GlobalTimerQuest:AddDelayTimer(function()
    	 											HotSpringWGCtrl.Instance:HideFace()
    											end, 5)
end

function HotSpringInfo:OnClickVip()
	local max = HotSpringWGData.Instance:GetVipExpMaxAdd()
	local cur = VipWGData.Instance:GetVipSpecPermissionsValue(8) or 0
	local is_vip = VipWGData.Instance:IsVip()
	if is_vip and cur >= max then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.ExpAddition.VipMaxAddTip)
		return
	else
		local tab_index = RechargeWGData.Instance:GetVIPDefultIndex()
		ViewManager.Instance:Open(GuideModuleName.Vip, tab_index)
	end
end

function HotSpringInfo:FlushVipIcon()
	local vip_level = GameVoManager.Instance:GetMainRoleVo().vip_level
	-- XUI.SetGraphicGrey(self.node_list.vip_icon, vip_level == 0)
	local vip_add = VipWGData.Instance:GetVipSpecPermissionsValue(8)
	local is_vip = VipWGData.Instance:IsVip()
	if is_vip then
		self.node_list.vip_text.text.text = string.format(Language.ExpAddition.VipAdd, vip_add / 100)
	end
	local max_add = HotSpringWGData.Instance:GetVipExpMaxAdd()
	self.node_list.vip_bg:SetActive(is_vip and vip_add > 0)
	self.node_list.vip_arrow:SetActive(vip_add < max_add)
end

function HotSpringInfo:OnVipInfoChange()
	if not self:IsLoadedIndex(0) then
		return
	end
    self:FlushVipIcon()
end

function HotSpringInfo:InfoActivityChangeCallBack(activity_type, status, next_time, open_type)
	if activity_type == ACTIVITY_TYPE.KF_DUCK_RACE and ACTIVITY_STATUS.STANDY == status then
		local seconds =  next_time - TimeWGCtrl.Instance:GetServerTime()
		self:CancelEnterDuckRaceTimer()
		self:FlushEnterDuckRaceTime()
		self.enter_duck_race_count_down = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushEnterDuckRaceTime, self), 1)
	end
end

-- 刷新进入小鸭疾跑倒计时
function HotSpringInfo:FlushEnterDuckRaceTime()
	if not self.node_list["time_text"] or not self.node_list["coundown_panel"] then
		return
	end
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_DUCK_RACE)
	if act_info then
		if act_info.status == ACTIVITY_STATUS.STANDY then
			local seconds =  act_info.next_time - TimeWGCtrl.Instance:GetServerTime()
			if seconds < 0 then
				self:CancelEnterDuckRaceTimer()
			end

			if seconds < 6 then
				self.node_list["time_text"].text.text = math.floor(seconds)
				self.node_list["coundown_panel"]:SetActive(true)
			end
		else
			self:CancelEnterDuckRaceTimer()
		end
	else
		self:CancelEnterDuckRaceTimer()
	end
end

function HotSpringInfo:CancelEnterDuckRaceTimer()
	if self.enter_duck_race_count_down then
		GlobalTimerQuest:CancelQuest(self.enter_duck_race_count_down)
		self.enter_duck_race_count_down = nil
	end
	if self.node_list["coundown_panel"] then
		self.node_list["coundown_panel"]:SetActive(false)
	end
end