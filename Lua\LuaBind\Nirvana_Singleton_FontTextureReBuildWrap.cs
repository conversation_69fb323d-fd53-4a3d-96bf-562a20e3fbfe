﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class Nirvana_Singleton_FontTextureReBuildWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(Nirvana.Singleton<FontTextureReBuild>), typeof(System.Object), "Singleton_FontTextureReBuild");
		<PERSON><PERSON>un<PERSON>("New", _CreateNirvana_Singleton_FontTextureReBuild);
		L<PERSON>unction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("Instance", get_Instance, null);
		L.EndClass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int _CreateNirvana_Singleton_FontTextureReBuild(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 0)
			{
				Nirvana.Singleton<FontTextureReBuild> obj = new Nirvana.Singleton<FontTextureReBuild>();
				ToLua.PushObject(L, obj);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to ctor method: Nirvana.Singleton<FontTextureReBuild>.New");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Instance(IntPtr L)
	{
		try
		{
			ToLua.PushObject(L, Nirvana.Singleton<FontTextureReBuild>.Instance);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

