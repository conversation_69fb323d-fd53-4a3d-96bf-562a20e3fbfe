BaseLingHeCell = BaseLingHeCell or BaseClass(BaseRender)

function BaseLingHeCell:__init(instance, real_instance)
    local bundle = "uis/view/tianshen_linghe_ui_prefab"
    local asset = "base_linghe_cell"
	self:LoadAsset(bundle, asset, real_instance.transform)

    self.is_show_tip = true
    self.show_item_id = nil
end

function BaseLingHeCell:LoadCallBack()
    self.view_block = self.view:GetComponent("UIBlock")
    self.view.button:AddClickListener(BindTool.Bind(self.OnClick, self))
    if not self.item_cell then
        self.item_cell = ItemCell.New(self.node_list["icon"])
        self.item_cell:SetUseButton(false)
        self.item_cell:SetCellBgEnabled(false)
    end
end

function BaseLingHeCell:__delete()
    self.view_block = nil
    if self.item_cell then
        self.item_cell:DeleteMe()
        self.item_cell = nil
    end

    self.show_item_id = nil
end

function BaseLingHeCell:SetIsShowTips(is_show)
	self.is_show_tip = is_show
end

function BaseLingHeCell:SetUseButton(is_use)
    if self.view_block then
        self.view_block.enabled = is_use
    end
end

function BaseLingHeCell:SetQualityBgShow(is_show_quality_bg, is_need_eff)
    self.item_cell:SetShowCualityBg(is_show_quality_bg)
    self.item_cell:NeedDefaultEff(is_need_eff)
end


function BaseLingHeCell:OnFlush()
    local item_id = self.data and (self.data.item_id > 0 and self.data.item_id or self.data.default_show_item_id) or 0
    self.show_item_id = item_id
    local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    self.node_list["icon"]:SetActive(item_cfg ~= nil)
	if item_cfg == nil then
        return
	end

    self.item_cell:SetData({item_id = item_id})
    self.item_cell:MakeGray(self.data.is_need_gray)
end

function BaseLingHeCell:OnClick()
    if self.data == nil or self.show_item_id <= 0 then
        return
    end
    if self.is_show_tip then
        TipWGCtrl.Instance:OpenItem({item_id = self.show_item_id})
    end

    BaseRender.OnClick(self)
end