function MarryView:InitFbView()
	if not self.init_fb_view then
		self.init_fb_view = true
		-- 呼唤仙伴
		XUI.AddClickEventListener(self.node_list["btn_call_fb"], BindTool.Bind1(self.CallTeam, self))

		-- 进入副本
		XUI.AddClickEventListener(self.node_list["btn_ingo_fb"], BindTool.Bind1(self.QingyuanFbHandler, self))
		XUI.AddClickEventListener(self.node_list["btn_fb_tips"], BindTool.Bind1(self.OnMarryFbTips, self))
		XUI.AddClickEventListener(self.node_list["btn_call_friend"], BindTool.Bind1(self.OnClickCallFriend, self))
		XUI.AddClickEventListener(self.node_list["btn_role_buy"], BindTool.Bind1(self.OnClick<PERSON>uy<PERSON><PERSON><PERSON>, self))
		XUI.AddClickEventListener(self.node_list["btn_lover_buy"], BindTool.Bind1(self.On<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, self))
        XUI.AddClickEventListener(self.node_list["btn_apply_jiehun"], BindTool.Bind1(self.OnClickApplyJieHun, self))
        XUI.AddClickEventListener(self.node_list["fb_img_plus"], BindTool.Bind1(self.OnClickApplyJieHun, self))
		XUI.AddClickEventListener(self.node_list["btn_baoming"], BindTool.Bind1(self.OnClickBaoMing, self))
	end
	self:FbEquip()
	local marry_other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	--self.node_list["lbl_fb_tips5"].text.text = marry_other_cfg.fb_buy_times_gold_cost 		--购买副本花费钱数
	self.marry_fb_match_event = GlobalEventSystem:Bind(OtherEventType.MatchChangeEvent, BindTool.Bind(self.OnMarryFbMatchInfoChange, self))
	self.alert_invite = MarryAlert.New(Language.Marry.InviteLover)

	self.node_list.btn_ingo_fb_text.text.text = Language.NewTeam.SingleEnterFb
end

function MarryView:DeleteFbView()
	-- if nil ~= self.reward_cell_list then
	-- 	for k,v in pairs(self.reward_cell_list) do
	-- 		v:DeleteMe()
	-- 	end
	-- 	self.reward_cell_list = nil
	-- end
	if self.marry_fb_match_event then
		GlobalEventSystem:UnBind(self.marry_fb_match_event)
		self.marry_fb_match_event = nil
	end

	if self.reward_cell_list then
		self.reward_cell_list:DeleteMe()
		self.reward_cell_list = nil
	end

	if self.exp_cancel_match_alert then
		self.exp_cancel_match_alert:DeleteMe()
		self.exp_cancel_match_alert = nil
	end

	if self.single_enter_alert then
		self.single_enter_alert:DeleteMe()
		self.single_enter_alert = nil
	end

	if self.alert_invite then
		self.alert_invite:DeleteMe()
		self.alert_invite = nil
	end
	self.init_fb_view = false
end

function MarryView:OnMarryFbMatchInfoChange()
	if self.show_index == TabIndex.marry_fb then
		self:Flush(self.show_index)
	end
end

--副本描述
function MarryView:OnFlushFb()
	local fb_info = MarryWGData.Instance:GetQyFbInfo()
	if fb_info ~= nil then
		local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
		if other_cfg ~= nil then
			-- local can_buy_num = other_cfg.fb_buy_times_limit - fb_info.self_buy_jion_fb_times
			-- local fb_num = other_cfg.fb_free_times_limit + fb_info.self_buy_jion_fb_times + fb_info.lover_buy_fb_times
			local fb_num = other_cfg.fb_free_times_limit + fb_info.self_buy_jion_fb_times + fb_info.total_lover_buy_fb_times
			local fb_shenyu_num = fb_num - fb_info.join_fb_times
			-- self.node_list["lbl_fb_tips6"].text.text = string.format(Language.Shop.Times, can_buy_num)
			local str = fb_shenyu_num.."/"..fb_num
			local color = fb_shenyu_num > 0 and COLOR3B.DEFAULT_NUM or COLOR3B.RED
			str = ToColorStr(str,color)
			self.node_list["lbl_fb_tips7"].text.text = str
			-- self:SetFBRemindState(fb_num > 0)
			-- local free_num = other_cfg.fb_free_times_limit - fb_info.join_fb_times
			-- if free_num <= 0 then
			-- 	--local fb_buy_times_limit = other_cfg.fb_buy_times_limit or 0
			-- 	--local shengyu_num = fb_buy_times_limit - fb_info.self_buy_jion_fb_times
			-- 	--self.node_list["effect"]:SetActive(shengyu_num > 0)
			-- else
			-- 	--self.node_list["effect"]:SetActive(false)
			-- end
		end	
 	end
 	self:SetFunBtnState()
 	-- local is_match = NewTeamWGData.Instance:GetIsMatching()
	-- self.node_list.btn_ingo_fb_text.text.text =  is_match and Language.NewTeam.IsMatching or Language.NewTeam.ViewNamePT
	--self.node_list.marry_pingtai_effect:SetActive(is_match)
	MarryWGData.Instance:SetMarryFBViewOpened()
end

-- 呼唤仙伴按钮处理函数
function MarryView:CallTeam()
	local join_num = self:GetJoinCount()

	--如果没有进入的次数，则提示
	if join_num == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryFbLimitTips)
		return
	end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	local lover_id = vo.lover_uid

	if 0 == lover_id or nil == lover_id then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.FbCallPartner)
	else
		local friend = SocietyWGData.Instance:FindFriend(lover_id)
		if nil == friend or friend.is_online == 0 then
			local lan = nil == friend and Language.Marry.FbNotFriends or Language.Marry.FbFriendsOnline
			SysMsgWGCtrl.Instance:ErrorRemind(lan)
			return
		end

		local team_cfg = SocietyWGData.Instance:GetTeamMemberList() or {}

		if nil ~= team_cfg and nil ~= #team_cfg and #team_cfg >= 2 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.FbCallBeyond)
			return
		end
		local team_type = GoalTeamType.QingYuanFb
		local info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, 0)
		if not info then
			return
		end

		NewTeamWGCtrl.Instance:QueryTeamInfo(lover_id, function(protocol)
			if self:IsOpen() and self:IsLoadedIndex(TabIndex.marry_fb) then
				if protocol.team_index > 0 then
					SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Marry.FbCallHasTeam, vo.lover_name))
					return
				end

                if 1 == SocietyWGData.Instance:GetIsInTeam() then
                    local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
                    local top_lv = info.role_max_level or top_user_level
                    local now_team_type, now_fb_mode = NewTeamWGData.Instance:GetTeamTypeAndMode()
                    if (now_team_type ~= info.team_type or now_fb_mode ~= info.fb_mode) and SocietyWGData.Instance:GetIsTeamLeader() == 1 then
                        NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
                        NewTeamWGCtrl.Instance:SendAutoMatchTeam(1, now_team_type, now_fb_mode)
                        NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, top_lv)
                    end
                    NewTeamWGCtrl.Instance:SendInviteUser(lover_id, TEAM_INVITE_TYPE.MARRY, 1)
                else
                    SocietyWGCtrl.Instance:ITeamInvite(lover_id, TEAM_INVITE_TYPE.MARRY, team_type, info.fb_mode)
                end
			end
		end)
	end
end

-- 进入副本处理函数
function MarryView:QingyuanFbHandler()
	---[[现逻辑--单人进入
	local join_num, buy_num = self:GetJoinCount()

	-- 如果进入次数为0
	if join_num == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryFbLimitTips) --今日挑战次数已经用完
	else

		local is_in_team = SocietyWGData.Instance:GetIsInTeam() ~= 0
		local is_match = NewTeamWGData.Instance:GetIsMatching()
		if is_match then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.SingleEnterFbMatching)
			return
		end

		if is_in_team then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.SingleEnterFbInTeam)
			return
		end

		if not self.single_enter_alert then
			self.single_enter_alert = MarryAlert.New()
		end
		self.single_enter_alert:SetLableString(Language.Marry.SingleEnterFbTip)
		self.single_enter_alert:SetOkString(Language.Marry.SingleEnterFbEnter)
		self.single_enter_alert:SetCancelString(Language.Marry.SingleEnterFbCancel)
		self.single_enter_alert:SetOkFunc(function()
			-- print_error("FFFFF===== 情缘副本--单人进入")
			local operate = 0
			local teamfb_mode = 0
			NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, GoalTeamType.QingYuanFb, teamfb_mode, 2)
		end)
		self.single_enter_alert:Open()
	end
	--]]

	--[[--原逻辑,跳转组队平台
	local is_match = NewTeamWGData.Instance:GetIsMatching()
	local operate = is_match and 1 or 0
	local is_linghunguangchang_fb = WuJinJiTanWGData.Instance:IsLingHunGuangChang()
	if is_match then
		if not self.exp_cancel_match_alert then
			self.exp_cancel_match_alert = MarryAlert.New()
		end
		self.exp_cancel_match_alert:SetLableString(Language.FuBenPanel.AlertCancelMatch)
		self.exp_cancel_match_alert:SetOkFunc(function()
			NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, GoalTeamType.QingYuanFb, 0)
			-- self.node_list.btn_ingo_fb_text.text.text =  Language.NewTeam.ViewNamePT
		end)
		self.exp_cancel_match_alert:Open()
		return
	end
	local team_member_count = SocietyWGData.Instance:GetTeamMemberCount()
	if team_member_count > QingYuanLimitCount then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryFbLimitCount)
	else
		NewTeamWGData.Instance:SetTeamTypeAndMode(GoalTeamType.QingYuanFb, 0)
		ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
	end
	--]]
end

function MarryView:OnMarryFbTips()
	MarryView.OpenTips(Language.Marry.MarryFbAfterTips, Language.Marry.MarryFbTitle)
end

--邀请异性好友
function MarryView:OnClickCallFriend()
	local join_num = self:GetJoinCount()

	if join_num == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.MarryFbLimitTips)
	else
		SocietyWGCtrl.Instance:SendFriendInfoReq()
		MarryWGCtrl.Instance:OpenFriendView()
	end
end

--购买副本次数
function MarryView:OnClickBuyHandler()
	local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	local fb_info = MarryWGData.Instance:GetQyFbInfo()
	if not other_cfg or not fb_info then
		print_error("other_cfg is a nil value!!!")
		return
	end
	local bind_gold = RoleWGData.Instance.role_info.bind_gold
	local gold = RoleWGData.Instance.role_info.gold

	if other_cfg.fb_buy_times_limit > fb_info.self_buy_jion_fb_times then
		if gold + bind_gold >= other_cfg.fb_buy_times_gold_cost then
			MarryWGCtrl.Instance:OpenMarryFbBuyView("role_buy")		
		else
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoEnoughGold)
		end
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.YunBiao.GouMaiTips2)
	end
end

--请求仙侣购买
function MarryView:OnClickLoverBuyHandler()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid
	if lover_id > 0 then
		MarryWGCtrl.Instance:SendQingYuanOperaReq(QINGYUAN_OPERA_TYPE.QINGYUAN_OPERA_TYPE_BUY_QINGYUAN_FB_REQ)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Marry.NoLover)
	end
end

-- 情缘副本掉落装备展示
function MarryView:FbEquip()
	-- local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	-- if not self.reward_cell_list then
	-- 	self.reward_cell_list = {}
	-- 	for i = 0, 2 do
	-- 		local reward_cell = ItemCell.New()
	-- 		local ph = self.node_list["ph_fb_item_"..i]
	-- 		reward_cell:SetInstanceParent(self.node_list["ph_fb_item_"..i])
	-- 		self.reward_cell_list[i] = reward_cell
	-- 	end
	-- end

	-- if other_cfg ~= nil then
	-- 	self.reward_cell_list[0]:SetData({item_id = other_cfg.drop_show1, is_bind = 0})
	-- 	self.reward_cell_list[1]:SetData({item_id = other_cfg.drop_show2, is_bind = 0})
	-- 	self.reward_cell_list[2]:SetData({item_id = other_cfg.drop_show3, is_bind = 0})
	-- end
	if nil == self.reward_cell_list then
		self.reward_cell_list = AsyncListView.New(MarryFBRewardItemRender,self.node_list["ph_reward_list"])
	end
	local qingyuanfb_reward = MarryWGData.Instance:GetQingYuanFbViewReward()
	if nil ~= qingyuanfb_reward then
		local list = {}
		for i,v in ipairs(qingyuanfb_reward) do
			local item = {}
			item.reward = v
			item.show_duobei = true
			item.task_type = RATSDUOBEI_TASK.QINYUANFUBEN
			table.insert(list, item)
		end
		self.reward_cell_list:SetDataList(list,0)
	end
end


--获取进入副本剩余次数及总共购买次数（自己的购买次数+伴侣的购买次数）
function MarryView:GetJoinCount()
	local friend = SocietyWGData.Instance:FindFriend(RoleWGData.Instance.role_vo.lover_uid)

	local fb_info = MarryWGData.Instance:GetQyFbInfo() or {}
	local join_num = 0
	local buy_num = 0

	-- 计算次数
	if fb_info ~= nil then
		local join_fb_times = fb_info.join_fb_times or 0
		local self_buy_jion_fb_times = fb_info.self_buy_jion_fb_times or 0
		local total_lover_buy_fb_times = fb_info.total_lover_buy_fb_times or 0
		local buy_fb_join_times = self_buy_jion_fb_times + total_lover_buy_fb_times

		local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
		if other_cfg ~= nil then
			local fb_free_times_limit = other_cfg.fb_free_times_limit or 0
			local fb_buy_times_limit = other_cfg.fb_buy_times_limit or 0

			join_num = buy_fb_join_times + fb_free_times_limit - join_fb_times
			buy_num = fb_buy_times_limit - buy_fb_join_times
		end	
	end

	return join_num, buy_num
end

--副本红点
function MarryView:SetFBRemindState( enable )
	self.node_list.marry_fb_remind:SetActive(enable)
end

-----------------------------情缘副本修改部分----------------------------------
function MarryView:OnClickApplyJieHun()
	MarryWGCtrl.Instance:OpenSelectLoverView(1) --1结婚2离婚
end

function MarryView:OnClickBaoMing()
	if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end
	local team_type = GoalTeamType.QingYuanFb
	local info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(team_type, 0)
	if not info then
		return
	end

	local match_callback = function ()
		local is_match = NewTeamWGData.Instance:GetIsMatching()
		local operate = is_match and 1 or 0
		--如果没在匹配中，才进行一些操作
		if not is_match then
	         --如果选中的是全部队伍，自动创建无目标
	        if info.team_type == -1 and info.fb_mode == -1 then
	            info = NewTeamWGData.Instance:GetTeamTargetInfoByTypeAndMode(0, 1)
	        end
	        local is_not_in_team = SocietyWGData.Instance:GetIsInTeam() == 0
	        if is_not_in_team then
                local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()  --获取服务器最高世界等级    
                local top_lv = info.role_max_level or top_user_level
	            NewTeamWGCtrl.Instance:SendCreateTeam(info.team_type, info.fb_mode, info.role_min_level, top_lv)
	            SocietyWGCtrl.Instance:SendTeamListReq(info.team_type, info.fb_mode) --创建完，立即请求队伍列表
	        end
	        --组队的最大等级改为等级排行的第一名
            local top_user_level = NewTeamWGData.Instance:GetTopLevelByTeamType()	--获取服务器最高世界等级
            local top_lv = info.role_max_level or top_user_level
			NewTeamWGData.Instance:SetTeamTypeAndMode(info.team_type, info.fb_mode)
			NewTeamWGData.Instance:SetTeamLimitLevel(info.role_min_level, top_lv)
			NewTeamWGCtrl.Instance:SendChangeTeamLimit(info.team_type, info.fb_mode, info.role_min_level, top_lv)
			--请求进入副本
			local room_menber_list = SocietyWGData.Instance:GetTeamMemberList()
			local num = #room_menber_list
			if num ~= 2 then-- 1.人数不够 2.队伍为同性
				TipWGCtrl.Instance:ShowSystemMsg(Language.Society.QingYuanPiPei)
				ViewManager.Instance:Open(GuideModuleName.NewTeamView, TabIndex.team_pingtai)
			else
				NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
			end

			-- NewTeamWGCtrl.Instance:SendAutoMatchTeam(operate, info.team_type , info.fb_mode)
			-- self.node_list.btn_ingo_fb_text.text.text = Language.NewTeam.IsMatching
			MarryWGCtrl.Instance:SendQingyuanReqInfo()
		else
			-- self.node_list.btn_ingo_fb_text.text.text = Language.NewTeam.IsMatching
			MarryWGCtrl.Instance:SendQingyuanReqInfo()
			NewTeamWGCtrl.Instance:OpenBaoMingEnterView()
		end
	end

	local team_cfg = SocietyWGData.Instance:GetTeamMemberList() or {}
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0
	local lover_info = lover_id > 0 and MarryWGData.Instance:GetLoverInfo2()
	local state = lover_info and lover_info.is_online == 1 and #team_cfg < 2
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local main_role_id = GameVoManager.Instance:GetMainRoleVo().role_id
	if PlayerPrefsUtil.GetInt("marry_fb" .. main_role_id) ~= open_day then
		if state then
			self.alert_invite:SetOkFunc( function ()
				self:CallTeam()
			end)
			self.alert_invite:SetCancelFunc(function ()
				match_callback()
			end)
			self.alert_invite:SetCloseFunc(function ()
				match_callback()
			end)
			--self.alert_invite:SetShowCheckBox(true, "marry_fb")
			--self.alert_invite:SetCheckBoxDefaultSelect(false)
			self.alert_invite:Open()
			return
		end
	end
	match_callback()
	
end

function MarryView:SendLoverInfo()
    if self:IsOpen() then
        local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
        self.node_list["lover_info"]:SetActive(lover_id > 0)
        self.node_list["no_lover"]:SetActive(lover_id <= 0)
        if lover_id > 0 then
            self.node_list["fb_img_plus"]:SetActive(false)
            BrowseWGCtrl.Instance:BrowRoelInfo(lover_id, BindTool.Bind1(self.SetLoverShowInfo, self))
            --self.node_list["rightup_bg"].rect.sizeDelta = Vector2(286,301)
        else
            self.node_list["fb_img_plus"]:SetActive(true)
            self.node_list["img_head"]:SetActive(false) 
            self.node_list["custom_img_head"]:SetActive(false) 
            --self.node_list["rightup_bg"].rect.sizeDelta = Vector2(286,235)
            XUI.SetGraphicGrey(self.node_list["head_bg"], false)
        end
    end
end

function MarryView:SetLoverShowInfo(protocol)
	if not protocol then
		return
	end
	MarryWGData.Instance:SetLoverInfo2(protocol)
	self:SetLoverShow()
end

function MarryView:SetLoverShow()
	if self:IsOpen() and self:IsLoadedIndex(TabIndex.marry_fb) then
		local lover_info = MarryWGData.Instance:GetLoverInfo2()
		if lover_info == nil then
			return
		end
		XUI.UpdateRoleHead(self.node_list["img_head"], self.node_list["custom_img_head"],lover_info.role_id,lover_info.sex,lover_info.prof,lover_info.is_online == 0,nil,true)
		self.node_list["lover_name"].text.text = lover_info.role_name
		local state = lover_info.is_online == 1 and true or false
		self.node_list["online"]:SetActive(state)
		self.node_list["not_online"]:SetActive(not state)
		self:SetFunBtnState()
	end
end
--设置按钮的装状态
function MarryView:SetFunBtnState()
	local lover_info = MarryWGData.Instance:GetLoverInfo2()
	local fb_info = MarryWGData.Instance:GetQyFbInfo()
	local lover_id = RoleWGData.Instance.role_vo.lover_uid or 0	--伴侣ID
	-- self.node_list["desc_text"]:SetActive(lover_id > 0)
	if nil == lover_info or nil == fb_info or lover_id == 0 then
		return
	end
	local other_cfg = MarryWGData.Instance:GetMarryOtherCfg()
	local fb_buy_times_limit = other_cfg.fb_buy_times_limit or 0
	local lover_buy_fb_times = fb_info.lover_buy_fb_times or 0
	local shengyu_num = fb_buy_times_limit - fb_info.lover_buy_fb_times
	-- XUI.SetButtonEnabled(self.node_list["btn_lover_buy"], lover_info.is_online == 1 and shengyu_num > 0)
	-- XUI.SetButtonEnabled(self.node_list["btn_call_fb"], lover_info.is_online == 1)
	-- self.node_list["desc_text"]:SetActive(shengyu_num > 0)
	if lover_info.is_online == 1 and shengyu_num > 0 then
		XUI.SetButtonEnabled(self.node_list["btn_lover_buy"], true)
	else
		XUI.SetButtonEnabled(self.node_list["btn_lover_buy"], false)
	end

	if lover_info.is_online == 1 then
		XUI.SetButtonEnabled(self.node_list["btn_call_fb"], true)
	else
		XUI.SetButtonEnabled(self.node_list["btn_call_fb"], false)
	end	
	local is_online = lover_info.is_online == 1
	XUI.SetGraphicGrey(self.node_list["btn_lover_buy_text"], not is_online or shengyu_num <= 0)
	XUI.SetGraphicGrey(self.node_list["btn_call_fb_text"], not is_online)
	XUI.SetGraphicGrey(self.node_list["head_bg"], not is_online)
	XUI.SetGraphicGrey(self.node_list["lover_name"], not is_online)
end


---MarryFBRewardItemRender
MarryFBRewardItemRender = MarryFBRewardItemRender or BaseClass(BaseRender)
function MarryFBRewardItemRender:__init()	

end

function MarryFBRewardItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function MarryFBRewardItemRender:LoadCallBack()
end

function MarryFBRewardItemRender:OnFlush()
	if not self.data then 
		return
	end
	self.node_list["bg"]:SetActive(1 == self.data.reward.is_eyetoeye)
	self.node_list["double"]:SetActive(false)
	
	if nil == self.cell then
		self.cell = ItemCell.New(self.node_list["ph_cell"])
	end
	local data = {}
	if 0 == self.data.reward.is_eyetoeye then
		data.item_id = self.data.reward.item_id or 0
		data.num = self.data.reward.num or 0
		data.is_bind = self.data.reward.is_bind or 0
	else
		data.item_id = self.data.reward.item_id or 0
		data.is_bind = self.data.reward.is_bind or 0
	end
	data.show_duobei = self.data.show_duobei
	data.task_type = self.data.task_type
	self.cell:SetData(data)
end
--胜利界面要用这个
MarryFBWinRewardItemRender = MarryFBWinRewardItemRender or BaseClass(BaseRender)
function MarryFBWinRewardItemRender:__init()	

end

function MarryFBWinRewardItemRender:__delete()
	if self.cell then
		self.cell:DeleteMe()
		self.cell = nil
	end
end

function MarryFBWinRewardItemRender:LoadCallBack()
end

function MarryFBWinRewardItemRender:OnFlush()
	if not self.data then 
		return
	end
	self.node_list["bg"]:SetActive(false)
	self.node_list["double"]:SetActive(1 == self.data.is_eyetoeye)

	if nil == self.cell then
		self.cell = ItemCell.New(self.node_list["ph_cell"])
	end
	
	--之前要的区分又不要了 服
	self.cell:SetData(self.data)
end

function MarryFBWinRewardItemRender:OnClick()
    if self.data then
		local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
		if nil == item_cfg then return end
		if GLOBAL_SHOW_ITEM_ID_SWITCH then
			print_log("物品id： ", self.data.item_id) --方便查看勿删
		end

		if self.need_item_get_way then
			TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.data.item_id})
		else
			TipWGCtrl.Instance:OpenItem(self.data, self.item_tip_from or ItemTip.FROM_NORMAL, nil)
		end
	end
end
