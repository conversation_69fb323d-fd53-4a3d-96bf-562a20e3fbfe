function GuildView:InitGuildRentView()
    if self.rent_grid == nil then
        local bundle = "uis/view/guild_ui_prefab"
        local asset = "assign_rent_cell"
		self.rent_grid = AsyncBaseGrid.New()
		self.rent_grid:CreateCells({
			col = 4, 
			change_cells_num = 1, 
			list_view = self.node_list["rent_grid"],
			assetBundle = bundle, 
			assetName = asset, 
			itemRender = GuildRentGridItemRender})
		self.rent_grid:SetStartZeroIndex(false)
	end

    XUI.AddClickEventListener(self.node_list.rent_btn, BindTool.Bind(self.OnRentBtnClick, self))
end

function GuildView:DeleteGuildRentView()
    if self.rent_grid then
		self.rent_grid:DeleteMe()
		self.rent_grid = nil
	end
end

function GuildView:ShowGuildRentCallBack()
	AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.INFO)
	AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.GUILD_RECORD)
	AssignmentWGCtrl.Instance:SendRentReq(ASSIGN_RENT_OPERATE_TASK.SELF_RENT)
end

function GuildView:FlushGuidRentView(param)
    for k, v in pairs(param) do
        if k == "all" then
			self:FlushRentAllFashionData()
			self:FlushGuildRentLogList()
			self:FlushRentLimit()	
		elseif k == "flush_log" then
			self:FlushGuildRentLogList()
		elseif k == "flush_Rent" then
			self:FlushRentAllFashionData()
			self:FlushRentLimit()
        end
    end
end

function GuildView:FlushRentLimit()
	local guild_all_fashion = AssignmentWGData.Instance:GetAllRentFashionDataList()
    local guild_max = AssignmentWGData.Instance:GetGuildMaxBorrowNum() 

	local self_max = AssignmentWGData.Instance:GetMaxBorrowNum()
    local my_data_list = AssignmentWGData.Instance:GetMyRentList()
    local my_is_rent_list = AssignmentWGData.Instance:GetMyIsRentList()
	local my_rent_num = #my_data_list + #my_is_rent_list

	local str = string.format(Language.Assignment.GuildRentTips, my_rent_num, self_max)
	local str2 = string.format(Language.Assignment.GuildRentTips2, #guild_all_fashion, guild_max)
	self.node_list.text_my_rent_num.text.text = str
	self.node_list.text_rent_num.text.text = str2
	

end

function GuildView:FlushRentAllFashionData()
    local data_list = AssignmentWGData.Instance:GetAllRentFashionDataList()
    self.rent_grid:SetDataList(data_list)

	self.node_list.rent_no_data:CustomSetActive(IsEmptyTable(data_list))
end

function GuildView:FlushGuildRentLogList()
	local str = AssignmentWGData.Instance:GetGuildRentLogText()
    self.node_list.rent_log_text.text.text = str
	self.node_list.log_list.scroll_rect.verticalNormalizedPosition = 0
	self.node_list.rent_log_no_data:CustomSetActive(str == "")
end

function GuildView:OnRentBtnClick()
    AssignmentWGCtrl.Instance:OpenGuildShelvesView()
end

-------------------------------------------GuildRentGridItemRender  start----------------------------------
GuildRentGridItemRender = GuildRentGridItemRender or BaseClass(BaseGridRender)

function GuildRentGridItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
        self.item_cell = nil
	end
end

function GuildRentGridItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_root)
    self.item_cell:SetNeedItemGetWay(false)
end

function GuildRentGridItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	self.item_cell:SetData({item_id = self.data.cfg.item_id})
    self.node_list.fashion_name_text.text.text = self.data.cfg.name
    self.node_list.name_text.text.text = self.data.owner_name

	self.node_list.rent_img:CustomSetActive(self.data.status == AssignmentWGData.GUILD_ASSIGN_STATUS.RENT_ASSIGN)
end
-------------------------------------------GuildRentGridItemRender  end----------------------------------