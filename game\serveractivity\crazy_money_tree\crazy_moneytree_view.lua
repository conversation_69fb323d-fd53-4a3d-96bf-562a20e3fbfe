CrazyMoneyTreeView = CrazyMoneyTreeView or BaseClass(SafeBaseView)
function CrazyMoneyTreeView:__init()
	self:SetMaskBg(true)
	self:AddViewResource(0, "uis/view/act_crazymoneytree_prefab", "CrazyMoneyTree")
end

function CrazyMoneyTreeView:__delete()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function CrazyMoneyTreeView:ReleaseCallBack()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function CrazyMoneyTreeView:CloseCallBack()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
	-- local data = CrazyMoneyTreeWGData.Instance
	-- local gold = data:GetMoney() or 0
	-- local max_chongzhi_num = data:GetMaxChongZhiNum() or 0
	-- if gold == max_chongzhi_num then
	-- 	ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY, ACTIVITY_STATUS.CLOSE)
	-- end
end

function CrazyMoneyTreeView:LoadCallBack()
	ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY, opera_type = RA_SHAKEMONEY_OPERA_TYPE.RA_SHAKEMONEY_OPERA_TYPE_QUERY_INFO})
	self.node_list["BtnRecharge"].button:AddClickListener(BindTool.Bind(self.OnClickShake, self))
	self.can_get = 0
end

--显示界面回调
function CrazyMoneyTreeView:ShowIndexCallBack()
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end
end

function CrazyMoneyTreeView:UpdataRollerTime(elapse_time, next_time)
	local time = next_time - elapse_time
	if self.node_list["TxtTime"] ~= nil then
		if time > 0 then
			self.node_list["TxtTime"].text.text = TimeUtil.FormatSecondDHM6(time)
		else
			self.node_list["TxtTime"].text.text = "00:00:00"
		end
	end
end

--点击摇一摇按钮回调
function CrazyMoneyTreeView:OnClickShake()
	local data = CrazyMoneyTreeWGData.Instance
	local chongzhi = data:GetTotalGold() or 0
	local gold = data:GetMoney() or 0
	local has_return_recive = data:GetReturnChongzhi() or 0
	local max_chongzhi_num = data:GetMaxChongZhiNum() or 0
	if chongzhi == 0 or math.ceil(chongzhi * has_return_recive / 100) == gold then
		FunOpen.Instance:OpenViewByName(GuideModuleName.Recharge)
	elseif gold >= max_chongzhi_num then
		-- SysMsgWGCtrl.Instance:ErrorRemind(Language.CrazyMoneyTree.TipsBroughtOut)
	else
		ServerActivityWGCtrl.Instance:SendRandActivityOperaReq({rand_activity_type = ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY, opera_type = RA_SHAKEMONEY_OPERA_TYPE.RA_SHAKEMONEY_OPERA_TYPE_FETCH_GOLD})
		AudioService.Instance:PlayRewardAudio()
	end
end

function CrazyMoneyTreeView:FlushButtonText()
	local data = CrazyMoneyTreeWGData.Instance
	local chongzhi = data:GetTotalGold() or 0
	local gold = data:GetMoney() or 0
	local has_return_recive = data:GetReturnChongzhi() or 0
	local max_chongzhi_num = data:GetMaxChongZhiNum() or 0
	XUI.SetButtonEnabled(self.node_list["BtnRecharge"], max_chongzhi_num > 0 and gold ~= max_chongzhi_num)
	if chongzhi == 0 then
		self.node_list["btn_text"].text.text = Language.Recharge.GoReCharge
	elseif gold == max_chongzhi_num then
		self.node_list["btn_text"].text.text = Language.CrazyMoneyTree.IsAllGet
	elseif math.ceil(chongzhi * has_return_recive / 100) == gold then
		self.node_list["btn_text"].text.text = Language.Recharge.GoReCharge
	else
		self.node_list["btn_text"].text.text = Language.CrazyMoneyTree.GetGold
	end
end

--刷新
function CrazyMoneyTreeView:OnFlush(param_t, index)
	local show_point = CrazyMoneyTreeWGData.Instance:GetCanCrazy()
	self.node_list["ImgRedPoint"]:SetActive(show_point)
	local chongzhi = CrazyMoneyTreeWGData.Instance:GetTotalGold() or 0
	self.node_list["recharge_has_text"].text.text = chongzhi
	local gold = CrazyMoneyTreeWGData.Instance:GetMoney() or 0
	local max_chongzhi_num = CrazyMoneyTreeWGData.Instance:GetMaxChongZhiNum()
	local surplus = max_chongzhi_num - gold
	if surplus >= 0 then
		self.node_list["res_return_num"].text.text = surplus
	else
		self.node_list["res_return_num"].text.text = 0
	end
	local return_echarge = CrazyMoneyTreeWGData.Instance:GetReturnChongzhi()
	self.node_list["return_text"].text.text = string.format(Language.CrazyMoneyTree.Recharge, return_echarge)
	local has_return_recive = CrazyMoneyTreeWGData.Instance:GetReturnChongzhi() or 0
	
	if math.ceil(chongzhi * has_return_recive / 100) <= max_chongzhi_num then
		self.can_get = math.ceil(chongzhi * has_return_recive / 100) - gold
		self.node_list["can_get_num"].text.text = self.can_get
	else
		self.can_get = max_chongzhi_num - gold
		self.node_list["can_get_num"].text.text = self.can_get
	end
	-- MainUIWGCtrl.Instance:FlushTipsIcon(MainUIViewChat.IconList.CRAZY_TREE, self.can_get > 0)

	if self.time_quest == nil then
		self.time_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushNextTime, self), 1)
		self:FlushNextTime()
	end

	self:FlushButtonText()
end

function CrazyMoneyTreeView:FlushNextTime()
	local time = ActivityWGData.Instance:GetActivityResidueTime(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_SHAKE_MONEY)
	if time <= 0 then
		if self.time_quest then
			GlobalTimerQuest:CancelQuest(self.time_quest)
			self.time_quest = nil
		end
	end
	
	self.node_list["TxtTime"].text.text = TimeUtil.FormatSecondDHM6(time)
end





