JiangShanRuHuaView = JiangShanRuHuaView or BaseClass(SafeBaseView)

local MAX_LEIJI_ITEM_NUM = 7

function JiangShanRuHuaView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Half
    self.default_index = 10
    self:SetMaskBg()

    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, "uis/view/jiangshan_ruhua_ui_prefab", "jiangshan_ruhua_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function JiangShanRuHuaView:LoadCallBack()
	self:InitCommonUI()

	for i = 1, 3 do
        self.node_list["btn_select_" .. i].button:AddClickListener(BindTool.Bind2(self.OnClickSelectTimesBtn, self, i))
    end

    self.node_list["all_reward_btn"].button:AddClickListener(BindTool.Bind(self.ShowRewardGaiLV, self))
    self:LoginTimeCountDown() --活动倒计时

    if not self.xy_all_reward_list then
        self.xy_all_reward_list = AsyncListView.New(JiangShanRuHuaAllRewardShowRender, self.node_list.show_reward_cell_list)
    end

	if not self.xy_leiji_list then
		self.xy_leiji_list = {}
		for i = 1, MAX_LEIJI_ITEM_NUM do 
			self.xy_leiji_list[i] = JiangShanRuHuaRender.New(self.node_list.xy_leiji_item_list:FindObj("xy_leiji_item_" .. i))
		end
	end

	if not self.xy_leiji_item_cur then
		self.xy_leiji_item_cur = JiangShanRuHuaRender.New(self.node_list.xy_leiji_item_cur)
	end

    local cur_grade_cfg = JiangShanRuHuaWGData.Instance:GetGradeCfg()
    if not cur_grade_cfg then
        return
    end
    local item_id = cur_grade_cfg.consume_item
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
    self.node_list["item_icon"].image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
	local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_grade_cfg.consume_item)
    local need_set_time = 1 -- 默认值
    if item_num >= 1 and item_num < 50 then
        need_set_time = 1
    elseif item_num >= 50 then
        need_set_time = 50
    end

    JiangShanRuHuaWGData.Instance:SetDrawTimes(need_set_time)
    local cur_sel_time = JiangShanRuHuaWGData.Instance:GetDrawTimes()
    self:SetSelectTimes(cur_sel_time)

    XUI.AddClickEventListener(self.node_list["btn_cur_lingqu"], BindTool.Bind(self.OnClickLeijiBtn, self))
	XUI.AddClickEventListener(self.node_list["btn_shrink_arrow"], BindTool.Bind(self.SwitchLeijiRewardContent, self, false))
    XUI.AddClickEventListener(self.node_list["btn_select_times"], BindTool.Bind(self.SwitchSelectContent, self))
    XUI.AddClickEventListener(self.node_list["btn_draw"], BindTool.Bind(self.OnClickDraw, self))
	XUI.AddClickEventListener(self.node_list["btn_draw_record"], BindTool.Bind(self.OnClickShowRecord, self)) --抽奖记录
    XUI.AddClickEventListener(self.node_list["btn_item_click_1"], BindTool.Bind(self.OnClickShowItem, self, 1))
	XUI.AddClickEventListener(self.node_list["btn_item_click_2"], BindTool.Bind(self.OnClickShowItem, self, 2))
	XUI.AddClickEventListener(self.node_list["add_item"], BindTool.Bind(self.OnClickAddItem, self))

	self.node_list["skip_spine_check"].button:AddClickListener(BindTool.Bind(self.OnClickSkipSpine, self))

    self:ShowModelDisplay()
end

function JiangShanRuHuaView:InitCommonUI()
	local bundle, assert = ResPath.GetRawImagesPNG("a3_jsrh_bg")
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function ()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)

	self.node_list.title_view_name.text.text = Language.JiangShanRuHua.TitleViewName
	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list, "VerticalTabbar_Activity")
		self.tabbar:SetVerTabbarCellName("VerticalTabbarCell_Activity")
		self.tabbar:SetSelectCallback(BindTool.Bind(self.ChangeToIndex, self))
		self.tabbar:Init(Language.JiangShanRuHua.TabGroup, nil, ResPath.CommonBundleName)
	end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local money_bundle, money_asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true, show_bind_gold = true,
			show_coin = true, show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(money_bundle, money_asset, self.node_list["money_tabar_pos"].transform)
	end
end

function JiangShanRuHuaView:CloseCallBack()
    if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end
end

function JiangShanRuHuaView:ReleaseCallBack()
    if CountDownManager.Instance:HasCountDown("xuyuan_freshpool_down") then
        CountDownManager.Instance:RemoveCountDown("xuyuan_freshpool_down")
    end

	if self.delay_play_draw_idle then
		GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
		self.delay_play_draw_idle = nil
	end

    if self.tabbar then
        self.tabbar:DeleteMe()
        self.tabbar = nil
    end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.xy_all_reward_list then
		self.xy_all_reward_list:DeleteMe()
		self.xy_all_reward_list = nil
	end

	if self.xy_leiji_list then
		for k,v in pairs(self.xy_leiji_list) do
			v:DeleteMe()
		end
		self.xy_leiji_list = nil
	end

	if self.xy_leiji_item_cur then
		self.xy_leiji_item_cur:DeleteMe()
		self.xy_leiji_item_cur = nil
	end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

	self.sel_content_expand = nil
    self:ClearLeiJiTween()
	self:ClearSelectTween()
end

--更新日志和协议信息
function JiangShanRuHuaView:OpenCallBack()
    JiangShanRuHuaWGCtrl.Instance:SendReq(RA_JIANGSHAN_RUHUA_OP_TYPE.INFO)
    JiangShanRuHuaWGCtrl.Instance:SendReq(RA_JIANGSHAN_RUHUA_OP_TYPE.RECORD)
end

function JiangShanRuHuaView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.JiangShanRuHuaView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_JIANG_SHAN_RU_HUA)
end

--参数刷新
function JiangShanRuHuaView:OnFlush(param)
    for k, v in pairs(param) do
        if "all" == k then
            self:FlushItemInfo()
            self:FlushViewShow()
        elseif k == "flush_model" then
            self:ShowModelDisplay()
        end
    end
end

function JiangShanRuHuaView:FlushViewShow()
    self:FlushShowCell()
    self:FlushSkipSpine()
end

function JiangShanRuHuaView:FlushItemInfo()
    local cur_grade_cfg = JiangShanRuHuaWGData.Instance:GetGradeCfg()
    if not cur_grade_cfg then
        return
    end

	local item_id = cur_grade_cfg.consume_item
	local item_num = ItemWGData.Instance:GetItemNumInBagById(item_id)
	self.node_list["item_num"].text.text = item_num
end

function JiangShanRuHuaView:ShowModelDisplay()
    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list.model_pos)
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
    end

    -- 形象展示
    local display_data = {}
	local cur_model_id = 0
    local model_cfg = JiangShanRuHuaWGData.Instance:GetModelShowInfo()
    if model_cfg.model_show_itemid ~= 0 and model_cfg.model_show_itemid ~= "" then
        local split_list = string.split(model_cfg.model_show_itemid, "|")
        if #split_list > 1 then
            local list = {}
            for k, v in pairs(split_list) do
				if cur_model_id == 0 then cur_model_id = k end
                list[tonumber(v)] = true
            end
            display_data.model_item_id_list = list
        else
			cur_model_id = model_cfg.model_show_itemid
            display_data.item_id = model_cfg.model_show_itemid
        end
    end

    display_data.bundle_name = model_cfg["model_bundle_name"]
    display_data.asset_name = model_cfg["model_asset_name"]
    display_data.image_effect_bundle = model_cfg.image_effect_bundle
    display_data.image_effect_asset = model_cfg.image_effect_asset
    local model_show_type = tonumber(model_cfg["show_type"]) or 1
    display_data.render_type = model_show_type - 1

    display_data.need_wp_tween = true
    display_data.hide_model_block = false
    display_data.model_click_func = function ()
        TipWGCtrl.Instance:OpenItem({item_id = cur_model_id})
    end
    self.model_display:SetData(display_data)

    local scale = tonumber(model_cfg.display_scale) or 1
    local pos_x, pos_y = 0, 0
    if model_cfg.display_pos and model_cfg.display_pos ~= "" then
        local pos_list = string.split(model_cfg.display_pos, "|")
        pos_x = tonumber(pos_list[1]) or pos_x
        pos_y = tonumber(pos_list[2]) or pos_y
    end

    if model_cfg.rotation and model_cfg.rotation ~= "" then
        local rotation_tab = string.split(model_cfg.rotation,"|")
        self.node_list.model_pos.transform.rotation = Quaternion.Euler(rotation_tab[1], rotation_tab[2], rotation_tab[3])
    end

    self.node_list.model_pos.transform:SetLocalScale(scale, scale, scale)
    RectTransform.SetAnchoredPositionXY(self.node_list.model_pos.rect, pos_x, pos_y)

    local model_name = ItemWGData.Instance:GetItemName(cur_model_id)
	self.node_list["model_show_name"].text.text = model_name

	-- 物品图片展示
	for i = 1, 2 do
		local bundle, asset = ResPath.GetNoPackPNG(model_cfg["image_name_" .. i])
		self.node_list["show_raw_image_" .. i].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["show_raw_image_" .. i].raw_image:SetNativeSize()
        end)
		local item_name = ItemWGData.Instance:GetItemName(model_cfg["item_id_" .. i])
		self.node_list["txt_show_name_" .. i].text.text = item_name
	end
end

function JiangShanRuHuaView:FlushShowCell()
    local show_list = JiangShanRuHuaWGData.Instance:GetShowCellList()
    if show_list then
		self.xy_all_reward_list:SetDataList(show_list)
    end

	local leiji_list = JiangShanRuHuaWGData.Instance:GetFanliList1()
	if leiji_list then
		for i, v in ipairs(self.xy_leiji_list) do
			if leiji_list[i] then
				v:SetData(leiji_list[i])
			end
		end
    end

    local draw_times = JiangShanRuHuaWGData.Instance:GetCurDrawTimes()
    local cur_data = leiji_list[#leiji_list]
    for i, v in ipairs(leiji_list) do
        if draw_times < v.data.lotto_num or (draw_times >= v.data.lotto_num and v.has_get ~= 1) then
            cur_data = v
            break
        end
    end
    self.xy_leiji_item_cur:SetData(cur_data)

	-- 进度
	local index = 0
    for i, v in ipairs(leiji_list) do
        if draw_times < v.data.lotto_num then
            index = i
			break
        end
    end
	index = index >= 1 and (index - 1) or index
	self.node_list["jd"].image.fillAmount = index / (MAX_LEIJI_ITEM_NUM - 1)
end

function JiangShanRuHuaView:ShowRewardGaiLV(item_type)
    local info = JiangShanRuHuaWGData.Instance:GetProbabilityInfo()
    TipWGCtrl.Instance:OpenTipsRewardProView(info)
end

function JiangShanRuHuaView:ShowItemTips(item_type)
    local cfg = JiangShanRuHuaWGData.Instance:GetConsumeCfg()
    local item_id = cfg[item_type].yanhua_item.item_id
    TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_id}) 
end

function JiangShanRuHuaView:OnItemChange(change_item_id, change_item_index, change_reason, put_reason, old_num, new_num)
    local check_list = JiangShanRuHuaWGData.Instance:GetItemDataChangeList()
    for i, v in pairs(check_list) do
        if v == change_item_id then
            self:FlushBtnShow(true)
			self.node_list["remind_draw"]:SetActive(new_num > 0)
            return
        end
    end
end

function JiangShanRuHuaView:OnClickShowItem(index)
    local model_cfg = JiangShanRuHuaWGData.Instance:GetModelShowInfo()
	if index == 1 then
        TipWGCtrl.Instance:OpenItem({ item_id = model_cfg.item_id_1 })
	elseif index == 2 then
		TipWGCtrl.Instance:OpenItem({ item_id = model_cfg.item_id_2 })
	end
end

function JiangShanRuHuaView:OnClickShowRecord()
    local record_list = JiangShanRuHuaWGData.Instance:GetRecordInfo()
	TipWGCtrl.Instance:OpenTipsRewardRecordView(record_list)
end

function JiangShanRuHuaView:OnClickDraw()
	local draw_times = JiangShanRuHuaWGData.Instance:GetDrawTimes()
	JiangShanRuHuaWGCtrl.Instance:ClickUse(draw_times, function()
		self:OnClickBuy()
	end)
end

function JiangShanRuHuaView:OnClickSelectTimesBtn(select_type)
	if select_type == 1 then
        self:SetSelectTimes(1)
		self:SwitchSelectContent(false)
	elseif select_type == 2 then
        self:SetSelectTimes(50)
		self:SwitchSelectContent(false)
	else
		local callback = function(sel_times)
            self:SetSelectTimes(sel_times)
			self:SwitchSelectContent(false)
		end
		JiangShanRuHuaWGCtrl.Instance:OpenSelectTimesView({ ok_func = callback })
	end
end

---切换 笔画选择 展开状态
function JiangShanRuHuaView:SwitchSelectContent(is_expand)
    if not self.select_content_tween then
        local width = self.node_list["select_part"].rect.rect.width
        self.select_content_tween = self.node_list["select_part"].rect:DOSizeDelta(Vector2(width, 152), 0.25)
        self.select_content_tween:SetEase(DG.Tweening.Ease.InOutSine)
        self.select_content_tween:SetAutoKill(false)
        self.select_content_tween:Pause()
    end

    if is_expand ~= nil then
		self.sel_content_expand = not is_expand
    end
	if not self.sel_content_expand then
		self.node_list["select_part"].rect:DOPlayForward()
		self.sel_content_expand = true
	else
		self.node_list["select_part"].rect:DOPlayBackwards()
		self.sel_content_expand = false
	end
end

---设置次数
---@param times integer
function JiangShanRuHuaView:SetSelectTimes(times)
	JiangShanRuHuaWGData.Instance:SetDrawTimes(times)
	self.node_list["txt_cur_select_times"].text.text = string.format(Language.JiangShanRuHua.Times, times)

    local cur_grade_cfg = JiangShanRuHuaWGData.Instance:GetGradeCfg()
    if not cur_grade_cfg then
        return
    end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_grade_cfg.consume_item)
    self.node_list.remind_draw:CustomSetActive(item_num >= times)
end

function JiangShanRuHuaView:OnClickBuy()
    local cur_cfg = JiangShanRuHuaWGData.Instance:GetConsumeCfg()
    local num = ItemWGData.Instance:GetItemNumInBagById(cur_cfg.cost_item_id)
	local draw_times = JiangShanRuHuaWGData.Instance:GetDrawTimes()
    local consume = cur_cfg.consume_count * (draw_times - num)
    --检查仙玉
    local enough = RoleWGData.Instance:GetIsEnoughUseGold(consume)
    --足够购买，不足弹窗
    if enough then
		local is_skip = JiangShanRuHuaWGData.Instance:GetSkipSpineStatus()
		if is_skip then
            JiangShanRuHuaWGCtrl.Instance:SendReq(RA_JIANGSHAN_RUHUA_OP_TYPE.BUY, draw_times)
		else
			if self.delay_play_draw_idle then
				GlobalTimerQuest:CancelQuest(self.delay_play_draw_idle)
				self.delay_play_draw_idle = nil
			end

			self.node_list["effect_draw_root"]:SetActive(true)
			self.delay_play_draw_idle = GlobalTimerQuest:AddDelayTimer(function()
                JiangShanRuHuaWGCtrl.Instance:SendReq(RA_JIANGSHAN_RUHUA_OP_TYPE.BUY, draw_times)
				TryDelayCall(self, function()
                    self.node_list["effect_draw_root"]:SetActive(false)
                end, 0.1, "hide_jiangshangruhua_effect")
			end, 2.3)
		end
    else
        VipWGCtrl.Instance:OpenTipNoGold()
    end
end

function JiangShanRuHuaView:OnClickLeijiBtn()
    local leiji_list = JiangShanRuHuaWGData.Instance:GetFanliList1()
    local draw_times = JiangShanRuHuaWGData.Instance:GetCurDrawTimes()
    local cur_data = leiji_list[#leiji_list]

    local can_get = false
    for i, v in ipairs(leiji_list) do
        if draw_times < v.data.lotto_num then
            cur_data = v
        elseif draw_times >= v.data.lotto_num and v.has_get ~= 1 then
            can_get = true
            cur_data = v
            break
        end
    end

    if can_get then
        JiangShanRuHuaWGCtrl.Instance:SendReq(RA_JIANGSHAN_RUHUA_OP_TYPE.FETCH, cur_data.data.index)
    else
        -- 展开
		self:SwitchLeijiRewardContent(true)
    end
end

---切换 累计奖励 展开状态
---@param is_expand boolean 是否展开
function JiangShanRuHuaView:SwitchLeijiRewardContent(is_expand)
	self:ClearLeiJiTween()
	if is_expand then
		self.node_list["btn_shrink_arrow"]:SetActive(true)
		self.node_list["btn_cur_lingqu"]:SetActive(false)
		self.node_list["xy_leiji_item_list"]:SetActive(true)
		local height = self.node_list["xy_leiji_item_list"].rect.rect.height
		RectTransform.SetSizeDeltaXY(self.node_list["xy_leiji_item_list"].rect, 0, height)
        self.leiji_content_tween = self.node_list["xy_leiji_item_list"].rect:DOSizeDelta(Vector2(690, height), 0.3)
		self.leiji_content_tween:SetEase(DG.Tweening.Ease.OutBack)
    else
		self.node_list["btn_shrink_arrow"]:SetActive(false)
		local height = self.node_list["xy_leiji_item_list"].rect.rect.height
		RectTransform.SetSizeDeltaXY(self.node_list["xy_leiji_item_list"].rect, 690, height)
		self.leiji_content_tween = self.node_list["xy_leiji_item_list"].rect:DOSizeDelta(Vector2(0, height), 0.3)
		self.leiji_content_tween:SetEase(DG.Tweening.Ease.InOutQuart)
		self.leiji_content_tween:OnComplete(function()
			self.node_list["btn_cur_lingqu"]:SetActive(true)
			self.node_list["xy_leiji_item_list"]:SetActive(false)
		end)
	end
end

function JiangShanRuHuaView:ClearLeiJiTween()
    if self.leiji_content_tween then
        self.leiji_content_tween:Kill()
        self.leiji_content_tween = nil
    end
end

function JiangShanRuHuaView:ClearSelectTween()
	if self.select_content_tween then
        self.select_content_tween:Kill()
        self.select_content_tween = nil
    end
end

--活动时间倒计时
function JiangShanRuHuaView:LoginTimeCountDown()
    local activity_data = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_JIANG_SHAN_RU_HUA)
    if activity_data ~= nil then
        local invalid_time = activity_data.end_time
        if invalid_time > TimeWGCtrl.Instance:GetServerTime() then
            self.node_list.xy_act_time.text.text = TimeUtil.FormatSecondDHM2(invalid_time - TimeWGCtrl.Instance:GetServerTime())
            CountDownManager.Instance:AddCountDown("xuyuan_freshpool_down", BindTool.Bind1(self.UpdateCountDown, self), BindTool.Bind1(self.OnComplete, self), invalid_time, nil, 1)
        end
    end
end

function JiangShanRuHuaView:UpdateCountDown(elapse_time, total_time)
    local valid_time = total_time - elapse_time
    if valid_time > 0 then
        self.login_act_date = TimeUtil.FormatUnixTime2Date()
        if self.node_list and self.node_list.xy_act_time then
            self.node_list.xy_act_time.text.text = TimeUtil.FormatSecondDHM2(valid_time)
        end
    end
end

function JiangShanRuHuaView:OnComplete()
    if self.node_list and self.node_list.xy_act_time then
        self.node_list.xy_act_time.text.text = Language.Activity.TianshenRoadLoginTime
    end

    self:Close()
end

function JiangShanRuHuaView:OnClickSkipSpine()
    local is_skip = JiangShanRuHuaWGData.Instance:GetSkipSpineStatus()
    is_skip = not is_skip
    JiangShanRuHuaWGData.Instance:SetSkipSpineStatus(is_skip)
    self:FlushSkipSpine()
end

function JiangShanRuHuaView:FlushSkipSpine()
	local is_skip = JiangShanRuHuaWGData.Instance:GetSkipSpineStatus()
	self.node_list.skip_spine_yes:SetActive(is_skip)
end

function JiangShanRuHuaView:OnClickAddItem()
    local cur_grade_cfg = JiangShanRuHuaWGData.Instance:GetGradeCfg()
    if not cur_grade_cfg then
        return
    end

	local item_id = cur_grade_cfg.consume_item
	local item_cfg = ItemWGData.Instance:GetItemConfig(item_id)
	TipWGCtrl.Instance:OpenItem({item_id = item_cfg.id})
end


--#region
----------------------------------------------------
-- JiangShanRuHuaRender
----------------------------------------------------
JiangShanRuHuaRender = JiangShanRuHuaRender or BaseClass(BaseRender)
function JiangShanRuHuaRender:LoadCallBack()
    self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickGet, self))
    self.show_item = ItemCell.New(self.node_list.cell)
    self.show_item:SetShowCualityBg(false)
	self.show_item:SetCellBgEnabled(false)
end

function JiangShanRuHuaRender:ReleaseCallBack()
    if self.show_item then
        self.show_item:DeleteMe()
		self.show_item = nil
    end
end

function JiangShanRuHuaRender:OnFlush()
    if not self.data then
        return
    end

    local data = self.data.data
    local draw_times = JiangShanRuHuaWGData.Instance:GetCurDrawTimes()
    local color = draw_times >= data.lotto_num and COLOR3B.D_GREEN or COLOR3B.D_RED
    local str = draw_times .. "/" .. data.lotto_num
    local desc = ToColorStr(str, color)
    self.node_list["txt_times"].text.text = desc
    local is_show = data.lotto_num <= draw_times and self.data.has_get ~= 1
    self.node_list["red"]:SetActive(is_show)
    self.node_list["is_get"]:SetActive(self.data.has_get == 1)
    self.node_list["btn_lingqu"]:SetActive(is_show)
    -- XUI.SetGraphicGrey(self.node_list["root"], self.data.has_get == 1)
    if not IsEmptyTable(data.reward_item) then
        self.show_item:SetData(data.reward_item[0])
        self.show_item:SetRightBottomTextVisible(false)
        self.show_item:SetBindIconVisible(false)
        self.show_item:SetEffectRootEnable(false)
    end
end

function JiangShanRuHuaRender:OnClickGet()
    if not self.data then
        return
    end

    local draw_times = JiangShanRuHuaWGData.Instance:GetCurDrawTimes()
    local data = self.data.data
    if draw_times >= data.lotto_num and self.data.has_get == 0 then
        JiangShanRuHuaWGCtrl.Instance:SendReq(RA_JIANGSHAN_RUHUA_OP_TYPE.FETCH, data.index)
    end
end

----------------------------------------------------
-- JiangShanRuHuaAllRewardShowRender
----------------------------------------------------
JiangShanRuHuaAllRewardShowRender = JiangShanRuHuaAllRewardShowRender or BaseClass(BaseRender) 

function JiangShanRuHuaAllRewardShowRender:LoadCallBack()
    self.show_item = ItemCell.New(self.node_list.cell)
end

function JiangShanRuHuaAllRewardShowRender:__delete()
    if self.show_item then
        self.show_item:DeleteMe()
    end
    self.show_item = nil
end

function JiangShanRuHuaAllRewardShowRender:SetData(data)
    self.data = data

    if not IsEmptyTable(self.data) and not IsEmptyTable(self.data.reward_item) then
        if self.item_id ~= self.data.reward_item.item_id then
            self.show_item:SetData(self.data.reward_item)
        end
    end
end

----------------------------------------------------
-- JiangShanRuHuaRecordCell
----------------------------------------------------
JiangShanRuHuaRecordCell = JiangShanRuHuaRecordCell or BaseClass(BaseRender)
function JiangShanRuHuaRecordCell:OnFlush()
    if not IsEmptyTable(self.data) then
        local item_data = ItemWGData.Instance:GetItemConfig(self.data.item_id)
        local item_name = ToColorStr(item_data.name, ITEM_COLOR[item_data.color])
        self.node_list["name_text"].text.text = string.format(Language.JiangShanRuHua.RecordName, self.data.role_name, item_name)
    else
        self.node_list["name_text"].text.text = ""
    end
end
--#endregion