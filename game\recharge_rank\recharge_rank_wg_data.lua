RechargeRankWGData = RechargeRankWGData or BaseClass()
function RechargeRankWGData:__init()
    if RechargeRankWGData.Instance then
        ErrorLog("[RechargeRankWGData] attempt to create singleton twice!")
        return
    end
    RechargeRankWGData.Instance =self

    local cfg = ConfigManager.Instance:GetAutoConfig("oa_recharge_rank_config_auto")
    self.recharge_rank_raward_cfg = ListToMapList(cfg.reward, "grade")
	self.grade_cfg = cfg.grade
	self.jump_cfg = ListToMapList(cfg.jump, "grade")
    self.recharge_rank_list = {}

	self.rank_show_list = {}
	RemindManager.Instance:Register(RemindName.LocalRechargeRank, BindTool.Bind(self.IsShowRechargeRankRedPoint, self))
end

function RechargeRankWGData:__delete()
	self.rank_show_list = nil
    RechargeRankWGData.Instance = nil
	RemindManager.Instance:UnRegister(RemindName.LocalRechargeRank)
end

-- 获取当前档位排行配置
function RechargeRankWGData:GetCurGradeRewardCfg()
	local grade = self:GetCurGrade()
	return self.recharge_rank_raward_cfg[grade]
end

-- 获取跳转配置
function RechargeRankWGData:GetCurJumpCfg()
	local grade = self:GetCurGrade()
	return self.jump_cfg[grade]
end

--设置充值榜信息
function RechargeRankWGData:SetRechargeRankInfo(rank_list)
	self.recharge_rank_list = rank_list
end

--设置活动信息
function RechargeRankWGData:SetRechargeRankBaseInfo(protocol)
	self.recharge_num = protocol.recharge_num						-- 充值数
	self.daily_reward_flag = protocol.daily_reward_flag				-- 每日奖励标记 0: 未领取 1: 已领取
end

-- 获取自己的充值数
function RechargeRankWGData:GetSelfRechargeNum()
	return self.recharge_num or 0
end

-- 获取每日奖励标记
function RechargeRankWGData:GetDailyGiftRewardFlag()
	return self.daily_reward_flag or 0
end

-- 获取排行榜信息
function RechargeRankWGData:GetRankList()
	return self.recharge_rank_list
end

-- 获取排行榜信息
function RechargeRankWGData:GetRankInfoByRank(rank)
	return (self.recharge_rank_list or {})[rank]
end

-- 获取自己的排行信息
function RechargeRankWGData:GetSelfRankInfo()
	local self_data = RankWGData.Instance:GetSelfValueData(RankKind.Person, PROFESS_RANK_TYPE.PERSON_RANK_TYPE_OA_RECHARGE_RANK)
	return self_data
end

--获取自己的排名
function RechargeRankWGData:GetSelfRankNum()
	return self:GetSelfRankInfo().self_rank
end

-- 获取当前档位
function RechargeRankWGData:GetCurGrade()
	local open_day = ActivityWGData.Instance:GetActivityOpenInServerDay(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_RECHARGE_RANK)
	local grade = 1
	for k, v in pairs(self.grade_cfg) do
		if open_day >= v.min_open_day and open_day <= v.max_open_day then
			grade = v.grade
			break
		end
	end
	return grade
end

--获取排行列表配置
function RechargeRankWGData:GetRankShowList()
	if not ActivityWGData.Instance:GetActivityIsOpen(ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_RECHARGE_RANK) then
		self.rank_show_list = {}
		return self.rank_show_list
	end

	local rank_show_list = {}
	rank_show_list = self.rank_show_list

	if IsEmptyTable(rank_show_list) then
		local cfg = self:GetCurGradeRewardCfg()
		if cfg then
			local min_rank = cfg[1].min_rank or 1
			local max_rank = cfg[#cfg].max_rank or 0
			for i = min_rank, max_rank do
				for k, v in ipairs(cfg) do
					if v.min_rank <= i and i <= v.max_rank then
						local data = {}
						data.rank = i
						data.reward_item = v.reward_item
						data.reach_value = v.reach_value
						table.insert(rank_show_list, data)
					end
				end
			end
		end
	end

	return rank_show_list
end

--获取上升排行的差值
function RechargeRankWGData:GetUpRankCondition()
	local my_rank_data = self:GetSelfRankInfo()
	if my_rank_data.self_rank == 1 then -- 已是第一
		return 0
	end

	local rank_list = self:GetRankList()
	local cfg = self:GetCurGradeRewardCfg()

	local target_rank = my_rank_data.self_rank == 0 and cfg[#cfg].max_rank or my_rank_data.self_rank - 1
	local target_value = (rank_list[target_rank] or {}).rank_value
	if not target_value or target_value == 0 then
		for i, v in ipairs(cfg) do
			if target_rank >= v.min_rank and target_rank <= v.max_rank then
				target_value = v.reach_value
				break
			end
		end
		target_value = target_value or cfg[#cfg].reach_value --兼容
	else
		target_value = target_value + 1 -- 高1才能顶下去
	end
	return target_value - my_rank_data.self_value
end

--主界面显示气泡
function RechargeRankWGData:GetMainUiIsShowRechargeRank()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "MainUiIsShowRechargeRank")
	if not PlayerPrefsUtil.HasKey(key) then -- 默认显示
		--开服冲榜的气泡，玩家等级200级后默认勾选，如果玩家手动点掉了那就不显示，200级前默认不勾选
		local level = RoleWGData.Instance:GetRoleLevel()
		if level >= 200 then
			PlayerPrefsUtil.SetInt(key, 1)
			return true
		else
			PlayerPrefsUtil.SetInt(key, 0)
			return false
		end
		
	end
	local flag = PlayerPrefsUtil.GetInt(key)
	return flag == 1
end

--设置主界面气泡显示
function RechargeRankWGData:SetMainUiIsShowRechargeRank(flag)
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "MainUiIsShowRechargeRank")
	PlayerPrefsUtil.SetInt(key, flag)
	RechargeRankWGCtrl.Instance:RuningRequesFlushRechargeRankInfo()
end

--跨天
function RechargeRankWGData:OnDayChange()
	self.rank_show_list = {}
end

-- 红点
function RechargeRankWGData:IsShowRechargeRankRedPoint()
	-- 每日礼包
	if self:GetDailyGiftRewardFlag() == 0 then
		return 1
	end

	return 0
end