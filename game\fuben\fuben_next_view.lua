FuBenNextView = FuBenNextView or BaseClass(SafeBaseView)

local QUICK_FINISH_ROLE_LEVEL = 90
local QUICK_FINISH_FUBEN_LEVEL = 3

function FuBenNextView:__init()
	self.rich_num = nil
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg()
	self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_a3_commmon_tiaozhanjiesuan_panel")
	self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_jiesuan_win")
    self.end_time = 0
    self.view_name = "FuBenNextView"
	self.view_layer = UiLayer.Pop
	self.playing_index = 0
	self.star_num = 0
	self.downtime_str = ""
end

function FuBenNextView:ReleaseCallBack()
	if self.rich_num then
		self.rich_num = nil
	end

	if self.act_star_reward_cell then
		for k,v in pairs(self.act_star_reward_cell) do
			v:DeleteMe()
		end
		self.act_star_reward_cell = nil
	end

	if self.rect_list then
		self.rect_list:DeleteMe()
		self.rect_list = nil
	end

	if self.m_tween then
		self.m_tween:Kill()
		self.m_tween = nil
	end

	self.end_time = 0
	if CountDownManager.Instance:HasCountDown("txg_close_timer") then
		CountDownManager.Instance:RemoveCountDown("txg_close_timer")
	end
	self.star_img_list = nil
	if FunctionGuide.Instance then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.FuBenNextView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

	if self.delay_play_star_quest then
		GlobalTimerQuest:CancelQuest(self.delay_play_star_quest)
        self.delay_play_star_quest = nil
	end
end

function FuBenNextView:LoadCallBack()
	self.star_img_list = {}
	self.act_star_reward_cell = {}
	for i = 1, 8 do
		local ph = self.node_list["ph_cell_" .. i]
		self.act_star_reward_cell[i] = ItemCell.New(ph)
	end

	if not self.rect_list then
		self.rect_list = AsyncListView.New(ItemCell, self.node_list.ph_zhanshii_cell)
	end

	self.star_high_img_list = {}
	for i=1,3 do
		self.star_high_img_list[i] = {}
		self.star_high_img_list[i].obj = self.node_list['img_star_' .. i]:FindObj("img_high_star_" .. i)
		self.star_high_img_list[i].pos = self.star_high_img_list[i].obj.rect.anchoredPosition
		self.star_high_img_list[i].effect = self.node_list["Effect"..i]
		self.star_high_img_list[i].obj:SetActive(false)
	end

	self.node_list["btn_win_tuichu"].button:AddClickListener(BindTool.Bind(self.CloseTianXianGeViewFuBen, self))
	self.node_list["btn_win_jixu"].button:AddClickListener(BindTool.Bind(self.OnChallengeNextMission, self))
	-- self.node_list["img_jinghua"]:SetActive(false)
	-- self.node_list["img_hunjing"]:SetActive(false)
	-- self.node_list["lbl_hunjing"]:SetActive(false)

	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.FuBenNextView, self.get_guide_ui_event)

 	local is_txg = Scene.Instance:GetSceneType() == SceneType.Fb_Welkin
	local is_exp_west_fb = Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_EXP_WEST_FB
	local is_dreamland_fb = Scene.Instance:GetSceneType() == SceneType.PHANTOM_DREAMLAND_FB
	self.node_list["layout_star"]:SetActive(not is_txg and (not is_exp_west_fb))
	-- self.node_list["title_img"]:SetActive(is_txg)
	-- self.node_list["txg_kill_text"]:SetActive( is_txg)
	self.node_list["lbl_fb_itemtips"]:SetActive(not is_txg and (not is_dreamland_fb))

	-- --self.node_list.success_bg:SetActive(false)
	-- self.node_list.reward_container:SetActive(false)
	-- self.node_list.btn_container:SetActive(false)
	self.node_list.mScroll:SetActive(false)
	--self.node_list.jiesuan_bg.rect.sizeDelta = Vector2(0,276)
	GlobalTimerQuest:AddDelayTimer(function()
		-- self.node_list.success_bg:SetActive(true)
		-- self.node_list.reward_container:SetActive(true)
		-- self.node_list.btn_container:SetActive(true)
		self.node_list.mScroll:SetActive(true)
		-- self.node_list.success_bg.transform.localScale = Vector3(3,3,3)
		-- self.node_list.success_bg.rect:DOScale(1,0.2)
		-- UITween.AlpahShowPanel(self.node_list.reward_container.gameObject, true, 1.5)
		-- UITween.AlpahShowPanel(self.node_list.btn_container.gameObject, true, 1.5)
		UITween.MoveShowPanel(self.node_list.mScroll.gameObject, Vector3(1000,0), 0.5)
	end,0.3)
end

function FuBenNextView:SortDataList(data_list)
	if data_list and not IsEmptyTable(data_list) then
		for k,v in pairs(data_list) do
			if v.item_id and v.item_id > 0 then
                local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)       
                v.color = item_cfg and item_cfg.color or 1
			else
                v.color = 0
            end
		end
		table.sort(data_list, SortTools.KeyUpperSorters("color"))
    end
    return data_list
end

function FuBenNextView:ShowIndexCallBack(index)
	if nil == self.end_data then
		return
	end

	-- if self.end_data.hunjing > 0 then
	-- 	-- self.node_list["img_hunjing"]:SetActive(true)
	-- 	-- self.node_list["lbl_hunjing"]:SetActive(true)
	-- 	-- self.node_list["lbl_hunjing_num"].text.text = self.end_data.hunjing
	-- end

	local function tween_end_func()
		XUI.SetButtonEnabled(self.node_list["btn_win_jixu"], 1 == self.end_data.is_pass)
		self:Flush()

		if CountDownManager.Instance:HasCountDown("txg_close_timer") then
			CountDownManager.Instance:RemoveCountDown("txg_close_timer")
		end

		self.node_list.exp_fb_special:SetActive(Scene.Instance:GetSceneType() == SceneType.Fb_Welkin)
		local is_exp_west_fb = Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_EXP_WEST_FB
		local is_dreamland_fb = Scene.Instance:GetSceneType() == SceneType.PHANTOM_DREAMLAND_FB
		self.node_list.fb_special_desc_root:SetActive(is_exp_west_fb or is_dreamland_fb)
		self.node_list.special_desc_1.text.text = self.end_data.tip1
		self.node_list.special_desc_2.text.text = self.end_data.tip2
		self.node_list["new_record"]:CustomSetActive(self.end_data.is_show_new_record)

		if self.end_time > 0 then
			if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
				-- self.node_list["lbl_hunjing_num"].text.text = ""
				local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
				local role_lv = RoleWGData.Instance:GetRoleLevel()
				local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(pass_level + 1)
				local max_level = #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").reward_show
				local limit_lv = cfg and cfg.open_level or 1
				local _, auto_str = self:GetWelkinAutoCloseIsExit()
				self.downtime_str = auto_str
				self:DoExpMoveTween()
				-- self.node_list["txg_kill_text"]:SetActive(false)
				--self.node_list["txg_kill_text"].text.text = FuBenPanelWGData.Instance:GetKillMonsterStr(pass_level)
				--self.node_list["txg_kill_text"].text.text = string.format(Language.FanRenXiuZhen.SuccessLevelPassed, pass_level)
				self.node_list.btn_win_jixu:SetActive(max_level > pass_level and role_lv >= limit_lv)
				CountDownManager.Instance:AddCountDown("txg_close_timer",
				BindTool.Bind1(self.UpdateCloseCountDownTime, self), BindTool.Bind1(self.CloseViewWelkinFuBen, self), nil, self.end_time , 1)
	
				-- 这里检测登神长阶一键直达
				if role_lv >= QUICK_FINISH_ROLE_LEVEL and pass_level > QUICK_FINISH_FUBEN_LEVEL and cfg then
					local can_arrived_lv, is_level_limit = FuBenPanelWGData.Instance:GetCurCanQuickFinishLevel(pass_level)
	
					-- 不能一键直达，不做任何操作
					self.node_list["lbl_fb_itemtips"]:SetActive(true)
					if can_arrived_lv == pass_level then	-- 相等表示下一关不可继续， 倒计时退出
						self.node_list["btn_win_jixu_text"].text.text = Language.FuBenPanel.ContinueFight
						if is_level_limit ~= 0 then
							self.node_list["lbl_fb_itemtips"].text.text = string.format(Language.FuBen.FuBenSpecialTips3, ToColorStr(is_level_limit, COLOR3B.D_RED)) 
						else
							self.node_list["lbl_fb_itemtips"].text.text = string.format(Language.FuBen.FuBenSpecialTips2, ToColorStr(cfg.capability, COLOR3B.D_RED)) 
							self.node_list["btn_win_jixu_text"].text.text = Language.FuBenPanel.ContinueFight
						end
					elseif can_arrived_lv - pass_level == 1 then -- 相差1个推荐继续
						self.node_list["lbl_fb_itemtips"].text.text = string.format(Language.FuBen.FuBenSpecialTips4, ToColorStr(cfg.capability, COLOR3B.GREEN)) 
						self.node_list["btn_win_jixu_text"].text.text = Language.FuBenPanel.ContinueFight
					elseif can_arrived_lv - pass_level > 1 then	-- 相差很多 一键直达
						self.node_list["lbl_fb_itemtips"].text.text = string.format(Language.FuBen.FuBenSpecialTips1, ToColorStr(can_arrived_lv - 1, COLOR3B.GREEN)) 
						self.node_list["btn_win_jixu_text"].text.text = Language.FuBenPanel.QuickFight
						self.downtime_str = Language.GuildBattleRanked.BattleQuickTime
					end
				end
			elseif Scene.Instance:GetSceneType() == SceneType.ZHUSHENTA_FB then
				CountDownManager.Instance:AddCountDown("txg_close_timer",
				BindTool.Bind1(self.UpdateCloseCountDownTime, self), BindTool.Bind1(self.CloseViewZhuShenTa, self), nil, self.end_time, 1)
			else
				CountDownManager.Instance:AddCountDown("txg_close_timer",
				BindTool.Bind1(self.UpdateCloseCountDownTime, self), BindTool.Bind1(self.CloseViewZhuShenTa, self), nil, self.end_time, 1)
			end
			self:UpdateCloseCountDownTime(1, self.end_time)
		end
	end


	UITween.ShowCommonTiaoZhanJieSuanPanelTween(self, true, self.node_list.tween_info_root, tween_end_func)
end

function FuBenNextView:DoExpMoveTween()
	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
    local old_cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(pass_level)
    local now_level = GameVoManager.Instance:GetMainRoleVo().level
    local old_level = FuBenPanelWGData.Instance:GetEnterXZLLevel() or now_level
    self.node_list.exp_fb_exp.text.text = old_cfg and old_cfg.roleexp or ""

    if now_level == old_level then
        self.node_list.level_change_group:SetActive(false)
    else
        self.node_list.level_change_group:SetActive(true)
    end

    local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(old_level)
    local new_is_vis,new_role_level = RoleWGData.Instance:GetDianFengLevel(now_level)
    self.node_list.exp_fb_old_level.text.text = role_level
    self.node_list.dianfeng1:SetActive(is_vis)
    self.node_list.exp_fb_new_level.text.text = new_role_level
	self.node_list.exp_fb_new_level:SetActive(now_level > old_level)
    self.node_list.dianfeng2:SetActive(now_level > old_level and new_is_vis)

	local get_exp = old_cfg.roleexp or 0
    local text_obj = self.node_list.exp_fb_exp:GetComponent(typeof(TMPro.TextMeshProUGUI))
    local update_fun = function(num)
        local value, postfix_name = CommonDataManager.ConverExpFBNum(num)
        if postfix_name == "" then
            text_obj.text = (string.format("%.0f", value)) .. postfix_name
        else
            text_obj.text = (value) .. postfix_name
        end
    end
	UITween.DONumberTo(text_obj, 0, get_exp, 1.5, update_fun,nil)

    -- local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
    -- local old_cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(pass_level)
    -- local now_level = GameVoManager.Instance:GetMainRoleVo().level
    -- local old_level = FuBenPanelWGData.Instance:GetEnterXZLLevel() or now_level
    -- if now_level == old_level then
    --     self.node_list.level_change_group:SetActive(false)
    -- else
    --     self.node_list.level_change_group:SetActive(true)
    -- end
    -- self.node_list.exp_fb_exp.text.text = old_cfg and old_cfg.roleexp or ""
    -- -- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.exp_fb_exp.rect)
    -- -- self.node_list.exp_bg.rect.sizeDelta = Vector2(96 + self.node_list.exp_fb_exp.rect.sizeDelta.x, 50)
    -- -- if self.node_list.exp_bg.rect.sizeDelta.x > 338 then
    -- --     self.node_list.level_change_group.sizeDelta = Vector2(self.node_list.exp_bg.rect.sizeDelta.x, 50)
    -- -- end
   
    -- local is_vis, role_level = RoleWGData.Instance:GetDianFengLevel(old_level)
    -- local new_is_vis,new_role_level = RoleWGData.Instance:GetDianFengLevel(now_level)

	-- --self.node_list.exp_fb_old_level_bg.text.text = role_level
    -- self.node_list.exp_fb_old_level.text.text = role_level
	-- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.node_list.old_group.rect)
    -- self.node_list.dianfeng1:SetActive(is_vis)
    -- self.node_list.exp_fb_new_level.text.text = new_role_level

    -- -- if role_level < 10 then
    -- --     self.node_list.level_arrow.rect.localPosition = Vector3(-45,0,0)
    -- -- elseif role_level < 100 then
    -- --     self.node_list.level_arrow.rect.localPosition = Vector3(-25,0,0)
    -- -- else
    -- --     self.node_list.level_arrow.rect.localPosition = Vector3(-9,0,0)
    -- -- end
    -- UITween.CleanAllTween(self.view_name)
    -- UITween.FakeHideShow(self.node_list.level_change_group)

    -- self.node_list.exp_fb_new_level:SetActive(false)
    -- self.node_list.dianfeng2:SetActive(false)
    -- self.node_list.exp_fb_arrow_slider.slider.value = 0
    -- self.node_list.exp_fb_new_level:SetActive(false)

    -- self.node_list.exp_fb_exp:SetActive(false)
    -- self.node_list.exp_fb_exp.text.text = ""

    -- local drop_time = 0.3  --降落时间
    -- local start_delay = 0.4 --开始落下延迟
    -- local start_delay2 = 0.9 --第二行落下延迟
    -- local slider_time = 0.7 -- 箭头出现延迟 start_delay2+slider_time
    -- local update_fun3 =  function ()
    --     if not self.node_list.exp_fb_new_level then
    --         return
    --     end
    --     self.node_list.exp_fb_new_level:SetActive(true)
    --     self.node_list.dianfeng2:SetActive(new_is_vis)
    --     if now_level > old_level then
    --         self.node_list.exp_fb_new_level.text.text = new_role_level
    --         local end_x = new_is_vis and 90 or 63
    --         local end_pos3 = Vector3(end_x,0,0)
    --         local end_scale3 = Vector3(1,1,1)
    --         self.node_list.exp_fb_new_level:SetActive(true)
    --         --self.node_list.exp_fb_new_level.rect.anchoredPosition = Vector2(end_pos3, 50)
    --         --self.node_list.dianfeng2.rect.anchoredPosition = Vector2(-8, 3)
    --         self.node_list.exp_fb_new_level.rect.localScale = Vector3(3,3,1)
    --         -- self.node_list.exp_fb_new_level.rect:DOAnchorPos(end_pos3,drop_time):OnComplete(
    --         --     function()
    --         --     end)
    --         self.node_list.exp_fb_new_level.rect:DOScale(end_scale3,drop_time)
    --         -- self.node_list.dianfeng2.rect:DOAnchorPos(Vector3(33,0,0),drop_time):OnComplete(
    --         --     function()
    --         --     end)
    --         self.node_list.dianfeng2.rect:DOScale(Vector3(1,1,1),drop_time)
    --     end
    -- end

    -- local exp_fun2 = function ()
    --     if not self.node_list.level_change_group then
    --         return
    --     end
    --     if now_level == old_level then
    --         self.node_list.level_change_group:SetActive(false)
    --         UITween.AlphaShow(self.view_name, self.node_list.level_change_group, 0, 1, 0.05)
    --         return
    --     end
    --     UITween.AlphaShow(self.view_name, self.node_list.level_change_group, 0, 1, 0.05)
    --     local end_pos2 = Vector3(408,-25,0)
    --     local end_scale2 = Vector3(1,1,1)
    --     self.node_list.level_change_group:SetActive(true)
    --     self.node_list.level_change_group.rect.anchoredPosition = Vector2(1119, 25)
    --     self.node_list.level_change_group.rect.localScale = Vector3(3,3,1)
    --     self.node_list.level_change_group.rect:DOAnchorPos(end_pos2, drop_time):OnComplete(
    --         function()
    --             self.node_list.exp_fb_arrow_slider.slider:DOValue(1, slider_time)
    --             GlobalTimerQuest:AddDelayTimer(function ()
    --                 update_fun3()
    --             end,slider_time)
    --         end)
    --     self.node_list.level_change_group.rect:DOScale(end_scale2, drop_time)
    -- end

    -- GlobalTimerQuest:AddDelayTimer(function ()
    --     exp_fun2()
    -- end,start_delay2)


    -- local get_exp = old_cfg.roleexp or 0
    -- local text_obj = self.node_list.exp_fb_exp:GetComponent(typeof(TMPro.TextMeshProUGUI))
    -- local update_fun = function(num)
    --     local value, postfix_name = CommonDataManager.ConverExpFBNum(num)
    --     if postfix_name == "" then
    --         text_obj.text = (string.format("%.0f", value)) .. postfix_name
    --     else
    --         text_obj.text = (value) .. postfix_name
    --     end
    -- end

    -- local exp_fun1 = function ()
    --     if not self.node_list.exp_fb_exp then
    --         return
    --     end
    --     local end_pos = Vector3(0,2,0)
    --     local end_scale = Vector3(1,1,1)
    --     self.node_list.exp_fb_exp.rect.anchoredPosition = Vector2(0, 50)
    --     self.node_list.exp_fb_exp.rect.localScale = Vector3(3,3,1)
    --     self.node_list.exp_fb_exp.rect:DOAnchorPos(end_pos,drop_time):OnComplete(
    --         function()
    --             UITween.DONumberTo(text_obj, 0, get_exp, 1.5, update_fun,nil)
    --         end)
    --     self.node_list.exp_fb_exp.rect:DOScale(end_scale,drop_time)
    -- end

    -- GlobalTimerQuest:AddDelayTimer(function ()
    --     self.node_list.exp_fb_exp:SetActive(true)
    --     exp_fun1()
    -- end,start_delay)
end

function FuBenNextView:Flush()
	self.star_num = 3
	self:PlayStarAction(self.star_num)
	SafeBaseView.Flush(self)
end

function FuBenNextView:FlushRewardList()
	if nil == self.end_data then
		return
	end

	local reward_list = {}
	-- local temp_list = {}
	-- 整合叠加一下
	-- for k,v in pairs(self.end_data.reward_list) do
	-- 	local item_id = v.item_id
	-- 	if not temp_list[item_id] then
	-- 		local data = {}
	-- 		data.item_id = v.item_id
	-- 		data.num = v.count or v.num
	-- 		data.is_bind = v.bind or v.is_bind
	-- 		temp_list[item_id] = data
	-- 	else
	-- 		temp_list[item_id].num = temp_list[item_id].num + (v.count or v.num)
	-- 	end
	-- end
	

	for k,v in pairs(self.end_data.reward_list) do
		local data = {}
		data.item_id = v.item_id
		data.num = v.count or v.num
		data.is_bind = v.bind or v.is_bind
		table.insert(reward_list, data)
	end

	reward_list = self:SortDataList(reward_list)

	self.node_list.zhanshi_root:SetActive(#reward_list > 8)
	self.node_list.Bound:SetActive(#reward_list <= 8 and #reward_list > 0)
	if self.end_data.scene_type == SceneType.PHANTOM_DREAMLAND_FB then
		self.node_list.not_have_reward:SetActive(false)
	else
		self.node_list.not_have_reward:SetActive(#reward_list <= 0)
	end

	local count = 0
	if #reward_list <= 8 then
		for i,v in ipairs(self.act_star_reward_cell) do
			if reward_list[i] and reward_list[i].item_id ~= 0 then
				v:SetData(reward_list[i])
				count = count + 1
			else
				v.root_node.transform.parent.gameObject:SetActive(false)
			end
		end

		self.node_list.Content.rect.sizeDelta = Vector2(100 * count + (count - 1) * 10 , self.node_list.Content.rect.sizeDelta.y)
	else
		self.rect_list:SetDataList(reward_list)
		self.rect_list:JumpToTop()

		local time = 0.25 * (#reward_list - 8)
		self.m_tween = self.node_list.ph_zhanshii_cell.scroll_rect:DoHorizontalPosition(0, 1, time, nil)
		self.m_tween:SetEase(DG.Tweening.Ease.Linear)
		self.m_tween:OnComplete(function ()
			self.m_tween:Kill()
			self.m_tween = nil
		end)
	end
end

function FuBenNextView:OnFlush()
	if not self.end_data then
		return
	end

	if self:IsOnlyShowJixu() then
		self.node_list["btn_win_tuichu"]:SetActive(false)
		self.node_list["btn_win_jixu"]:SetActive(true)
	elseif self:IsOnlyShowTuichu() then
		self.node_list["btn_win_tuichu"]:SetActive(true)
		self.node_list["btn_win_jixu"]:SetActive(false)
	else
		self.node_list["btn_win_tuichu"]:SetActive(true)
		self.node_list["btn_win_jixu"]:SetActive(true)
	end

	-- b不知道这个界面的刷新在ShowIndex上，这里加个条件修改
	if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
		local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
		local role_lv = RoleWGData.Instance:GetRoleLevel()

		-- 这里检测登神长阶一键直达
		if role_lv >= QUICK_FINISH_ROLE_LEVEL and pass_level > QUICK_FINISH_FUBEN_LEVEL then
			local can_arrived_lv, is_level_limit = FuBenPanelWGData.Instance:GetCurCanQuickFinishLevel(pass_level)
			if can_arrived_lv == pass_level then
				if is_level_limit ~= 0 then
					self.node_list["btn_win_jixu"]:SetActive(false)
				end
			end
		end
	end


	self:FlushRewardList()
end

--递归调用
function FuBenNextView:PlayStarAction(star_num)
	local index = 1
	local end_pos = Vector2(0,0)
	local scale = Vector3(1,1,1)
	local fun = function ()
		local eff_obj = self.star_high_img_list[index].effect
		local obj = self.star_high_img_list[index].obj
		
		if obj == nil or IsNil(obj.gameObject) then
			return
		end
		
		obj:SetActive(true)
		obj.rect.localScale = Vector3(3,3,1)
		obj.rect.anchoredPosition = Vector2(35, 60)
		obj.rect:DOAnchorPos(end_pos,0.5):OnComplete(
                function()
                    if eff_obj then
                        eff_obj:SetActive(true)
                    end
                end)
		obj.rect:DOScale(scale,0.5)
		obj.rect:DORotate(Vector3(0, 0, -360 * 3), 0.3, DG.Tweening.RotateMode.FastBeyond360)
		index = index + 1
		if index > 3 then
			GlobalTimerQuest:CancelQuest(self.StartAction)
		end
	end

	--self.StartAction = GlobalTimerQuest:AddTimesTimer(fun,0.2,star_num)
	self.delay_play_star_quest = GlobalTimerQuest:AddDelayTimer(function()
        self.StartAction = GlobalTimerQuest:AddTimesTimer(fun,0.2,star_num)
        GlobalTimerQuest:CancelQuest(self.delay_play_star_quest)
        self.delay_play_star_quest = nil
    end,0.03)

 --    GlobalTimerQuest:AddDelayTimer(function()
	-- -- 	self.node_list.success_bg:SetActive(true)
	-- 	self.node_list.layout_exp_money_tips:SetActive(true)
	-- 	self.node_list.bottom_parent:SetActive(true)
	-- 	self.node_list.Bound:SetActive(true)
	-- -- 	self.node_list.success_bg.transform.localScale = Vector3(2,2,2)
	-- -- 	self.node_list.success_bg.rect:DOScale(1,0.5)
	-- 	UITween.AlpahShowPanel(self.node_list.layout_exp_money_tips.gameObject, true, 1.5)
	-- 	UITween.AlpahShowPanel(self.node_list.bottom_parent.gameObject, true, 1.5)
	-- 	UITween.MoveShowPanel(self.node_list.Bound.gameObject, Vector3(1000,0), 0.5)
	-- end,0.5)
end

function FuBenNextView:OpenCallBack()
	if nil == self.end_data then
		return
	end
	-- AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(1 == self.end_data.is_pass and AudioUrl.ShengLi or AudioUrl.MissionFailed, nil, true))
	if 1 == self.end_data.is_pass then
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.ShengLi, nil, true))
	end
end

function FuBenNextView:CloseCallBack()
	self.end_data = nil

	if CountDownManager.Instance:HasCountDown("next_level") then
		CountDownManager.Instance:RemoveCountDown("next_level")
	end
	if self.star_high_img_list then
		for i=1,3 do
			if self.star_high_img_list[i] and self.star_high_img_list[i].obj then
				self.star_high_img_list[i].obj.anchoredPosition = self.star_high_img_list[i].pos
			end
		end
	end
end
-- 设置面板数据
function FuBenNextView:SetEndData(data)
	self.end_data = data
end
-- 设置倒计时关闭时间
function FuBenNextView:FbCloseTime(time)
	self.end_time = time
end

function FuBenNextView:UpdateCloseCountDownTime(elapse_time, total_time)
	if FunctionGuide.Instance:GetIsGuide() then
		if CountDownManager.Instance:HasCountDown("txg_close_timer") then
			CountDownManager.Instance:RemoveCountDown("txg_close_timer")
		end
		self.node_list["lbl_end_time"].text.text = ""
		return
	end

	if total_time - elapse_time > 0 then
		if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
			self.node_list["lbl_end_time"].text.text = string.format(self.downtime_str, math.floor(total_time - elapse_time))
		else
			self.node_list["lbl_end_time"].text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, math.floor(total_time - elapse_time))
		end
	end
end

function FuBenNextView:CloseViewZhuShenTa()
	self:Close()
end

-- 修真路关闭当前面板
function FuBenNextView:CloseViewWelkinFuBen()
	MainuiWGCtrl.Instance:SetShowTimeTextState( false )
	local need_exit = self:GetWelkinAutoCloseIsExit()
	if need_exit then
		FuBenWGCtrl.Instance:SendLeaveFB()
	else
		if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
			local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
            local role_lv = RoleWGData.Instance:GetRoleLevel()
			-- 这里检测登神长阶一键直达
			if role_lv >= QUICK_FINISH_ROLE_LEVEL and pass_level > QUICK_FINISH_FUBEN_LEVEL then
				local can_arrived_lv, is_level_limit = FuBenPanelWGData.Instance:GetCurCanQuickFinishLevel(pass_level)
				if can_arrived_lv - pass_level == 1 then -- 相差1个推荐继续
					FuBenWGCtrl.Instance:SendFBReqNextLevel()
				elseif can_arrived_lv - pass_level > 1 then	-- 相差很多 一键直达
					FuBenWGCtrl.Instance:SendFBReqQucikFinish()
				else -- 不能直达也不能继续 直接退出 免得卡副本里
					FuBenWGCtrl.Instance:SendLeaveFB()
				end
			else
				FuBenWGCtrl.Instance:SendFBReqNextLevel()
			end
		else
			FuBenWGCtrl.Instance:SendFBReqNextLevel()
		end
	end
	self:Close()
	Scene.Instance:SimulationSceneLoad()
end

--  点击退出按钮退出当前副本
function FuBenNextView:CloseTianXianGeViewFuBen()
	if CountDownManager.Instance:HasCountDown("txg_close_timer") then
		CountDownManager.Instance:RemoveCountDown("txg_close_timer")
	end
	if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin or Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_EXP_WEST_FB then   -- 当场景类型为天仙阁时，发送离开副本协议
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
	self:Close()
end

-- 获得修真路结算面板自动关闭的时候是否应该退出
function FuBenNextView:GetWelkinAutoCloseIsExit()
	if self:IsOnlyShowTuichu() then
		return true, Language.GuildBattleRanked.AutoQuitFb
	end

	if self:IsOnlyShowJixu() then
		return false, Language.GuildBattleRanked.BattleRankedNextTime
	end

	local pass_level = FuBenPanelWGData.Instance:GetPassLevel()
	local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(pass_level + 1)
    local limit_lv = cfg and cfg.open_level or 1
	local max_level = #ConfigManager.Instance:GetAutoConfig("tianxiangefbcfg_auto").reward_show
	if cfg and GameVoManager.Instance:GetMainRoleVo().capability < cfg.capability then
		return true, Language.GuildBattleRanked.AutoQuitFb
	end 

	if RoleWGData.Instance:GetRoleLevel() < limit_lv then
        local str = RoleWGData.GetLevelString(limit_lv)
		return true, Language.GuildBattleRanked.LevelLimitNextFb1 .. str .. Language.GuildBattleRanked.LevelLimitNextFb2
	end
	return false, Language.GuildBattleRanked.BattleRankedNextTime
end

function FuBenNextView:OnChallengeNextMission()
	if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
		local level = FuBenPanelWGData.Instance:GetPassLevel()
		local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(level + 1)

		if cfg and GameVoManager.Instance:GetMainRoleVo().capability < cfg.capability then
			local data = {}
			data.level = level
			data.capability = cfg.capability
			data.enter_func = BindTool.Bind(self.RealEnterNext, self)
			FuBenPanelWGCtrl.Instance:OpenChallengeTip(data)
		else
			local role_lv = RoleWGData.Instance:GetRoleLevel()
			-- 这里检测登神长阶一键直达
			if role_lv >= QUICK_FINISH_ROLE_LEVEL and level > QUICK_FINISH_FUBEN_LEVEL and cfg then
				local can_arrived_lv, is_level_limit = FuBenPanelWGData.Instance:GetCurCanQuickFinishLevel(level)
				if can_arrived_lv - level == 1 then -- 相差1个推荐继续
					self:RealEnterNext()
				elseif can_arrived_lv - level > 1 then	-- 相差很多 一键直达
					self:RealEnterNext(true)
				end
			else
				self:RealEnterNext()
			end
		end
	else
		self:RealEnterNext()
	end
end

function FuBenNextView:RealEnterNext(is_quick_fight)
	-- FuBenWGCtrl.Instance:SendEnterWelkinFb()
	if is_quick_fight then
		FuBenWGCtrl.Instance:SendFBReqQucikFinish()
	elseif Scene.Instance:GetSceneType() == SceneType.SCENE_TYPE_EXP_WEST_FB then
		local level = self.end_data.level
		ExperienceFbWgCtrl.Instance:RequestExpWestDare(level + 1)
	else
		FuBenWGCtrl.Instance:SendFBReqNextLevel()
	end

	--print_error("点击了按钮")
	MainuiWGCtrl.Instance:SetShowTimeTextState( false )
	if CountDownManager.Instance:HasCountDown("txg_close_timer") then
		CountDownManager.Instance:RemoveCountDown("txg_close_timer")
	end
	--UiInstanceMgr.Instance:ShowFBStartDown(5)
	self:Close()
	Scene.Instance:SimulationSceneLoad()
end

function FuBenNextView:GetCloseBtn()
	return nil
end

function FuBenNextView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.CloseBtn and nil ~= self.node_list.btn_win_tuichu then -- 当关闭按钮不为空
		if Scene.Instance:GetSceneType() == SceneType.SG_BOSS then
		else
			return self.node_list.btn_win_tuichu, BindTool.Bind(self.CloseTianXianGeViewFuBen, self)
		end
	elseif ui_name == GuideUIName.EndPanelGoOn then
		return self.node_list["btn_win_jixu"], BindTool.Bind(self.OnChallengeNextMission, self)
	end
	return nil, nil
end

-- 是否只显示继续按钮
function FuBenNextView:IsOnlyShowJixu()
	local role_lv = RoleWGData.Instance:GetRoleLevel()
	if self.end_data and self.end_data.scene_type == SceneType.Fb_Welkin and role_lv < QUICK_FINISH_ROLE_LEVEL then
		local pass_level = FuBenPanelWGData.Instance:GetPassLevel()		
		if self.end_data.is_guide and pass_level <= WELKIN_GUIDE_MAX_TIMES and FuBenPanelWGData.Instance:GetGuideFinishTimes() < WELKIN_GUIDE_MAX_TIMES then
			return true
		end
		-- 写死新手时期第四关只显示“继续”按钮
		if TaskGuide.Instance:IsNoviceStage() and pass_level == 4 then
			return true
		end
	end
	return false
end

-- 是否只显示退出按钮
function FuBenNextView:IsOnlyShowTuichu()
	local role_lv = RoleWGData.Instance:GetRoleLevel()

	if self.end_data and self.end_data.scene_type == SceneType.Fb_Welkin and role_lv < QUICK_FINISH_ROLE_LEVEL then
		local pass_level = FuBenPanelWGData.Instance:GetPassLevel()		
		if self.end_data.is_guide and pass_level <= WELKIN_GUIDE_MAX_TIMES and FuBenPanelWGData.Instance:GetGuideFinishTimes() >= WELKIN_GUIDE_MAX_TIMES then
			return true
		end
		-- 写死新手时期第五关只显示“退出”按钮
		return TaskGuide.Instance:IsNoviceStage() and pass_level == 5
	end

	-- 幻梦秘境只显示退出
	if self.end_data and self.end_data.scene_type == SceneType.PHANTOM_DREAMLAND_FB  then
		return true
	end

	if not self:CheckHaveContinue() then
		return true
	end

	return false
end

-- 检测是否展示下一关继续按钮
function FuBenNextView:CheckHaveContinue()
	-- -- 天山修炼没有下一关继续
	if self.end_data and self.end_data.scene_type == FUBEN_TYPE.FB_WUHUN_EXP_WEST then
		local cfg = ExperienceFbWGData.Instance:GetLevelCfgByLevel(self.end_data.level + 1)
		if not cfg then
			return false
		end
	
		local role_lv = RoleWGData.Instance:GetRoleLevel()
		local is_can_challenge = role_lv >= cfg.need_level 
	
		if not is_can_challenge then
			return false
		end

		return true
	end

	return true
end