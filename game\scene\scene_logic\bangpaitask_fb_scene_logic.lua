BangPaiTaskFbFbSceneLogic = BangPaiTaskFbFbSceneLogic or BaseClass(CommonFbLogic)

function BangPaiTaskFbFbSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function BangPaiTaskFbFbSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function BangPaiTaskFbFbSceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)
	-- FuBenWGCtrl.Instance:OpenTaskFollow()
	-- XuiBaseView.CloseAllView()
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	FuBenPanelWGCtrl.Instance:OpenBountyTaskView()
end

function BangPaiTaskFbFbSceneLogic:Update(now_time, elapse_time)
	CommonFbLogic.Update(self, now_time, elapse_time)
	FuBenWGCtrl.Instance:UpdataTaskFollow()
end

function BangPaiTaskFbFbSceneLogic:Out()
	CommonFbLogic.Out(self)
	-- FuBenWGCtrl.Instance:CloseTaskFollow()
	FuBenPanelWGCtrl.Instance:CloseBountyTaskView()
end