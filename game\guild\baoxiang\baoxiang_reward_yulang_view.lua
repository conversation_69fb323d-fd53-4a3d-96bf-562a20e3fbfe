BaoXiangRewardYuLianView = BaoXiangRewardYuLianView or BaseClass(SafeBaseView)

function BaoXiangRewardYuLianView:__init()
	self:SetMaskBg(true)
	self:LoadConfig()
end

-- 加载配置
function BaoXiangRewardYuLianView:LoadConfig()
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {vector2 = Vector2(2, 13), sizeDelta = Vector2(866, 527)})
	self:AddViewResource(0, "uis/view/guild_ui_prefab", "baoxiang_reward_yulang")
end

function BaoXiangRewardYuLianView:__delete()

end

function BaoXiangRewardYuLianView:ReleaseCallBack()
	if self.reward_yulang_list then
		self.reward_yulang_list:DeleteMe()
		self.reward_yulang_list = nil
	end
end

function BaoXiangRewardYuLianView:LoadCallBack()
	self.node_list["title_view_name"].text.text = Language.BiZuoBaoXiang.RewardYuLangTitle
	self.reward_yulang_list = AsyncListView.New(BaoXiangRewardItem,self.node_list["reward_yulang_list"])
	local cfg_list = GuildBaoXiangWGData.Instance:GetTreasureCfg()
	local data_list = {}
	for k,v in pairs(cfg_list) do
		table.insert(data_list,v)
	end
	table.sort(data_list,SortTools.KeyUpperSorters("quality"))
	self.reward_yulang_list:SetDataList(data_list)
end

function BaoXiangRewardYuLianView:OnFlush()

end

