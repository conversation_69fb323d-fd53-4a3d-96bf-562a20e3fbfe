MainUiLongZhuSkill = MainUiLongZhuSkill or BaseClass(BaseRender)

function MainUiLongZhuSkill:__init()
	self.m_skill_level = -1
	self.m_skill_cd = 0
	self.m_is_activate = nil
	local skill_id = LongZhuWGData.Instance:GetOtherCfg("skill_id")
	self.m_skill_data = {skill_index = 8, skill_id = skill_id or 6000, cell_index = 0, is_temp_show = false}
end

function MainUiLongZhuSkill:__delete()
	if self.cd_mask_tween then
		self.cd_mask_tween:Kill()
		self.cd_mask_tween = nil
	end

	CountDownManager.Instance:RemoveCountDown("mainui_long_zhu_count_down")
end

function MainUiLongZhuSkill:LoadCallBack()
	-- self.node_list.btn_root.button:AddClickListener(BindTool.Bind(self.OnClickLongZhuSkillBtn, self))
	self.node_list.lock_btn.button:AddClickListener(BindTool.Bind(self.OnClickLockLongZhuSkill, self))
	local longzhu_cell = self.node_list.btn_root
	if longzhu_cell.event_trigger_listener then
		longzhu_cell.event_trigger_listener:AddPointerUpListener(BindTool.Bind(self.OnSkillUp, self, self.m_skill_data))
		longzhu_cell.event_trigger_listener:AddDragListener(BindTool.Bind(self.OnDragSkill, self, self.m_skill_data))
		longzhu_cell.event_trigger_listener:AddPointerDownListener(BindTool.Bind(self.OnClickRangSkill, self, self.m_skill_data))
		longzhu_cell.event_trigger_listener:AddPointerExitListener(BindTool.Bind(self.CheckRangState, self, self.m_skill_data))
	end
end

function MainUiLongZhuSkill:OnSkillUp(data)
	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:OnSkillUp(self.m_skill_data)
	end
end

function MainUiLongZhuSkill:OnDragSkill(data, eventData)
	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:OnDragSkill(self.m_skill_data, eventData)
	end
end

function MainUiLongZhuSkill:OnClickRangSkill(data)
	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:OnClickRangSkill(self.m_skill_data)
	end
end

function MainUiLongZhuSkill:CheckRangState(data)
	local view = MainuiWGCtrl.Instance:GetView()
	if view then
		view:CheckRangState(self.m_skill_data)
	end
end


function MainUiLongZhuSkill:OnClickLongZhuSkillBtn()
	if not self.m_is_activate then
		ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.longzhu_cell)
		return
	end
	-- local view = MainuiWGCtrl.Instance:GetView()
	-- if view then
	-- 	view:OnClickSkill(self.m_skill_data)
	-- end
end

function MainUiLongZhuSkill:OnClickLockLongZhuSkill()
	if not self.m_is_activate then
		ViewManager.Instance:Open(GuideModuleName.Bag, TabIndex.rolebag_longzhu)
		return
	end
end

function MainUiLongZhuSkill:OnFlush()
	self:FlushLongZhuLevel()
	self:FlushActive()
	self:FlushCD()
end

function MainUiLongZhuSkill:FlushLongZhuLevel()
	local skill_level = LongZhuWGData.Instance:GetLongZhuSkillLevel()
	if skill_level == self.m_skill_level then
		return
	end

	local skill_data
	local skill_info_list = SkillWGData.Instance:GetCurrSkillList()
	for k,v in pairs(skill_info_list) do
		if SkillWGData.Instance:GetIsLongZhuSkill(v.skill_id) then
			skill_data = v
			break
		end
	end

	if skill_data then
		self.m_skill_data.skill_index = skill_data.index
	end

	skill_level = math.max(skill_level, 1)
	self.m_skill_level = skill_level

	local skill_cfg = SkillWGData.Instance:GetOtherSkillCfg(self.m_skill_data.skill_id, skill_level)
	if skill_cfg then
		self.m_skill_cd = skill_cfg.cd_s and skill_cfg.cd_s / 1000 or 0
	end

	local clien_skill_cfg = SkillWGData.Instance:GetSkillClientConfig(self.m_skill_data.skill_id, skill_level)
	if clien_skill_cfg then
		self.node_list.skill_icon.image:LoadSprite(ResPath.GetSkillIconById(clien_skill_cfg.icon_resource))
	end

	-- 技能类型
	local skill_type_tab = SkillWGData.Instance:GetSkillAttackType(self.m_skill_data.skill_id)
	if self.node_list.skill_type ~= nil then	-- 加个容错
		if skill_type_tab and skill_type_tab[1] and tonumber(skill_type_tab[1]) ~= 0 then
			self.node_list.skill_type:SetActive(true)
			self.node_list.skill_type.text.text = Language.Skill.CommonSkillType[skill_type_tab[1]] or ""
		else
			self.node_list.skill_type:SetActive(false)
		end
	end
end

function MainUiLongZhuSkill:FlushActive()
	local is_activate = LongZhuWGData.Instance:HasLongZhuActive()
	if self.m_is_activate ~= is_activate then
		self.node_list.lock_btn:SetActive(not is_activate)
		if not self.cd_mask_tween then
			self.node_list.CDMask.image.fillAmount = is_activate and 0 or 1
		end
		self.m_is_activate = is_activate
	end

	local is_xmz = Scene.Instance:GetMainRole():IsXMZCar()
	local is_show = not is_xmz
	-- local main_role = Scene.Instance:GetMainRole()
	-- local is_tianshen = main_role:IsTianShenAppearance()
	-- self.node_list.btn_root:SetActive(not is_tianshen)
	self.node_list.btn_root:SetActive(is_show)
end

function MainUiLongZhuSkill:FlushCD()
	-- if CountDownManager.Instance:HasCountDown("mainui_long_zhu_count_down") then
	-- 	return
	-- end
	CountDownManager.Instance:RemoveCountDown("mainui_long_zhu_count_down")

	if not self.m_is_activate then
		return
	end

	local in_cd = SkillWGData.Instance:IsInSkillCD(self.m_skill_data.skill_index,self.m_skill_data.skill_id)
	self.node_list["long_zhu_skill_effect"]:SetActive(not in_cd)

	local skill_end_time = SkillWGData.Instance:GetSkillCDEndTime(self.m_skill_data.skill_index,self.m_skill_data.skill_id)
	local count_down_time = skill_end_time - Status.NowTime * 1000
	local complete_func = function ()
		self.node_list.CDText.text.text = ""
		self.node_list.CDMask.image.fillAmount = 0
		self.node_list["long_zhu_skill_effect"]:SetActive(true)
	end

	local ceil = math.ceil
	local cd_label = self.node_list.CDText.text
	local update_func = function (elapse_time, total_time)
		elapse_time = elapse_time * 1000
		if total_time - elapse_time > 1000 then
			cd_label.text = ceil((total_time - elapse_time) / 1000)
		elseif total_time - elapse_time > 0 then
			cd_label.text = string.format("%.1f", (total_time - elapse_time) / 1000)
		else
			CountDownManager.Instance:RemoveCountDown("mainui_long_zhu_count_down")
			complete_func()
		end
	end

	if count_down_time > 0 then
		update_func(0, count_down_time)
		CountDownManager.Instance:AddCountDown(
			"mainui_long_zhu_count_down",
			update_func,
			complete_func,
			nil,
			count_down_time > 1 and count_down_time or 1,
			0.1
		)
	else
		complete_func()
	end

	self:PlayCDMaskTween(count_down_time / 1000)
end

function MainUiLongZhuSkill:PlayCDMaskTween(count_down_time)
	if self.cd_mask_tween then
		self.cd_mask_tween:Kill()
		self.cd_mask_tween = nil
	end
	if count_down_time > 0 then
		if self.m_skill_cd > 0 and count_down_time > 1 then
			self.node_list.CDMask.image.fillAmount = count_down_time / self.m_skill_cd
		else
			self.node_list.CDMask.image.fillAmount = 1
		end
		self.cd_mask_tween = self.node_list.CDMask.image:DOFillAmount(0, count_down_time)
		self.cd_mask_tween:SetEase(DG.Tweening.Ease.Linear)
		self.cd_mask_tween:OnComplete(function ()
			self.cd_mask_tween = nil
		end)
	end
end