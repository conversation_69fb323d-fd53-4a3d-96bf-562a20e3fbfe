ZhuXieRankRewardView = ZhuXieRankRewardView or BaseClass(SafeBaseView)

function ZhuXieRankRewardView:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true)
    local bundle_name = "uis/view/zhuxie_ui_prefab"
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel")
    self:AddViewResource(0, bundle_name, "layout_rank_reward_view")
end

function ZhuXieRankRewardView:ReleaseCallBack()
    if nil ~= self.reward_item_list then
        self.reward_item_list:DeleteMe()
        self.reward_item_list = nil
    end
end

--是否跨服，boss_id
function ZhuXieRankRewardView:SetBossId(is_kf, boss_id)
    self.is_kf = is_kf
    local cur_boss_id = ActivityWGData.Instance:GetBossId(is_kf)
    self.boss_id = boss_id or cur_boss_id
    if nil == self.boss_id then
        return
    end

    self.reward_list = {}
    if self.is_kf then
        self.reward_list = ActivityWGData.Instance:GetKfZhuXieRewardShow(self.boss_id)
    else
        self.reward_list = ActivityWGData.Instance:GetZhuXieRewardShow(self.boss_id)
    end
    if self.reward_list and not IsEmptyTable(self.reward_list) then
        self:Open()
    end
end

function ZhuXieRankRewardView:LoadCallBack()
    self:SetSecondView(nil, self.node_list["size"])
    self.node_list["title_view_name"].text.text = Language.ZhuXie.RankRewardViewName

    if nil == self.reward_item_list then
        self.reward_item_list = AsyncListView.New(ZhuXieRewardRenderItem, self.node_list["reward_list"])
        self.reward_item_list:SetCreateCellCallBack(BindTool.Bind(self.OnCreateCell, self))
    end

    self.reward_item_list:SetDataList(self.reward_list)
end

function ZhuXieRankRewardView:OnCreateCell(cell)
    cell:SetParentScrollRect(self.node_list.reward_list.scroll_rect)
end



-------------------------------------------------------------------
ZhuXieRewardRenderItem = ZhuXieRewardRenderItem or BaseClass(BaseRender)
function ZhuXieRewardRenderItem:LoadCallBack()
    self.item_list = AsyncListView.New(ItemCell, self.node_list.item_list)
    self.is_load_complete = true
    self.nested_scroll_rect = SNestedScrollRect.New(self.node_list.item_list)
    if self.parent_scroll_rect then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end
end

function ZhuXieRewardRenderItem:__delete()
    self.parent_scroll_rect = nil
    if self.nested_scroll_rect then
        self.nested_scroll_rect:DeleteMe()
        self.nested_scroll_rect = nil
    end
    if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end
end

function ZhuXieRewardRenderItem:OnFlush()
    if nil == self.data then
        return
    end

    local data_list = {}
    for k = 0, #self.data.reward do
        table.insert(data_list, self.data.reward[k])
    end

    self.item_list:SetDataList(data_list)

    if self.index == 1 and self.data.mingci_1 == self.data.mingci_2 then
        self.node_list["rank_text"].text.text = string.format(Language.ZhuXie.SeasonRewardDesc[1], self.data.mingci_1)
    elseif self.data.mingci_1 < self.data.mingci_2 then
        self.node_list["rank_text"].text.text = string.format(Language.ZhuXie.SeasonRewardDesc[2], self.data.mingci_1, self.data.mingci_2)
    elseif self.index > 1 and self.data.mingci_1 == self.data.mingci_2 then
        self.node_list["rank_text"].text.text = string.format(Language.ZhuXie.SeasonRewardDesc[3], self.data.mingci_1)
    end
end


function ZhuXieRewardRenderItem:SetParentScrollRect(scroll_rect)
    self.parent_scroll_rect = scroll_rect

    if self.is_load_complete then
        self.nested_scroll_rect:SetParentScrollRect(self.parent_scroll_rect)
    end
end
