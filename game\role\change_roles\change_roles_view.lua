local CHANGE_ROLES_STATE_TYPE =
{
	NotAct = 1,
	Change = 2,
	CD = 3,
}

function RoleView:InitChangeRolesView()
	self.node_list.title_view_name.text.text = Language.ChangeRoleTitleName.ChangeRoleName
	self.select_sex = nil
	self.select_prof = nil
	for sex = GameEnum.FEMALE, GameEnum.MALE do
		for prof = GameEnum.ROLE_PROF_1, GameEnum.ROLE_PROF_4 do
			local node = string.format("toggle_%s_%s", sex, prof)
			if self.node_list[node] then
				XUI.AddClickEventListener(self.node_list[node],
					BindTool.Bind(self.ChangeRolesOnClickSelectRole, self, sex, prof))
			end
		end
	end

	if nil == self.model_display then
		self.model_display = RoleModel.New()
		self.model_display:SetUISceneModel(self.node_list["role_display"].event_trigger_listener, MODEL_CAMERA_TYPE.BASE, nil, UI_SCENE_TYPE.DEFAULT)
		self:AddUiRoleModel(self.model_display, TabIndex.role_change)
	end

	XUI.AddClickEventListener(self.node_list.btn_change, BindTool.Bind(self.ChangeRolesOnClickChangeRole, self))
end

function RoleView:ReleaseChangeRolesView()
	self:CleanChangeCD()
	self.select_sex = nil
	self.select_prof = nil

	if self.model_display then
		self.model_display:DeleteMe()
		self.model_display = nil
	end

	if self.alert_tips then
		self.alert_tips:DeleteMe()
		self.alert_tips = nil
	end
end

function RoleView:OnFlushChangeRoles()
	local sex, prof = RoleWGData.Instance:GetRoleSexProf()

	self:ChangeRolesFlushChangeBtn()
	if self.select_sex == nil or self.select_prof == nil then
		local node = string.format("toggle_%s_%s", sex, prof)
		if self.node_list[node] then
			self.node_list[node].toggle.isOn = true
			self:ChangeRolesOnClickSelectRole(sex, prof, true)
		end
	else
		self:ChangeRolesFlushModel()
	end
end

function RoleView:ChangeRolesOnClickSelectRole(sex, prof, isOn)
	if not isOn or (self.select_sex == sex and self.select_prof == prof) then
		return
	end

	self.select_sex = sex
	self.select_prof = prof

	self:ChangeRolesFlushChangeBtn()
	self:ChangeRolesFlushModel()
end

function RoleView:CleanChangeCD()
	if self.change_cd and CountDown.Instance:HasCountDown(self.change_cd) then
		CountDown.Instance:RemoveCountDown(self.change_cd)
		self.change_cd = nil
	end
end

function RoleView:ChangeRolesFlushChangeBtn()
	if self.select_sex == nil or self.select_prof == nil then
		return
	end

	local cur_sex, cur_prof = RoleWGData.Instance:GetRoleSexProf()
	local is_cur = self.select_sex == cur_sex and self.select_prof == cur_prof
	local is_act = ChangeRolesWGData.Instance:GetRoleIsAct(self.select_sex, self.select_prof)
	self.node_list["btn_change"]:SetActive(not is_cur)

	self:CleanChangeCD()
	if is_act then
		local next_time = ChangeRolesWGData.Instance:GetNextChangeCD()
		if next_time > 0 then
			self:ChangeRolesSetBtnImg(CHANGE_ROLES_STATE_TYPE.CD, next_time)
			self:ChangeRolesSetBtnCDStr(next_time)
			self.change_cd = CountDown.Instance:AddCountDown(next_time, 0.5,
				function(elapse_time, total_time)
					local time = total_time - elapse_time
					self:ChangeRolesSetBtnCDStr(time)
				end,
				function()
					self:ChangeRolesSetBtnCDStr(0)
				end
			)
		else
			self:ChangeRolesSetBtnCDStr(0)
		end
	else
		local rmb_buy_cfg = ChangeRolesWGData.Instance:GetRMBBuyCfg(self.select_sex, self.select_prof)
		if rmb_buy_cfg then
			local money_str = RoleWGData.GetPayMoneyStr(rmb_buy_cfg.price, rmb_buy_cfg.rmb_type, rmb_buy_cfg.rmb_seq)
			self:ChangeRolesSetBtnImg(CHANGE_ROLES_STATE_TYPE.NotAct, money_str)
		end
	end
end

function RoleView:ChangeRolesSetBtnCDStr(time)
	time = math.floor(time)
	if time > 0 then
		self:ChangeRolesSetBtnImg(CHANGE_ROLES_STATE_TYPE.CD, time)
	else
		self:ChangeRolesSetBtnImg(CHANGE_ROLES_STATE_TYPE.Change)
	end
end

function RoleView:ChangeRolesSetBtnImg(type, param)
	self.node_list["btn_change_img"]:SetActive(type == CHANGE_ROLES_STATE_TYPE.Change)
	self.node_list["btn_change_act_img"]:SetActive(type == CHANGE_ROLES_STATE_TYPE.NotAct)
	self.node_list["btn_change_time_img"]:SetActive(type == CHANGE_ROLES_STATE_TYPE.CD)

	if type == CHANGE_ROLES_STATE_TYPE.NotAct then
		self.node_list["btn_change_act_text"].text.text = param
	elseif type == CHANGE_ROLES_STATE_TYPE.CD then
		self.node_list["btn_change_time_text"].text.text = string.format(Language.Role.ChangeRoleBtnCD, param)
	end
end

function RoleView:ChangeRolesFlushModel()
	if self.select_sex == nil or self.select_prof == nil then
		return
	end

	local role_res_id = RoleWGData.GetJobModelId(self.select_sex, self.select_prof)
	local img_asset, img_name = ResPath.GetProfRawImagesPNG(self.select_sex, self.select_prof)
	self.node_list["display_role_img"].raw_image:LoadSprite(img_asset, img_name, function()
		self.node_list["display_role_img"].raw_image:SetNativeSize()
	end)

	local bundle, asset = RoleWGData.Instance:GetJobProfEffect(self.select_sex, self.select_prof)
    if bundle and asset then
        self.node_list["display_role_img"]:ChangeAsset(bundle, asset)
    end

	local desc = RoleWGData.Instance:GetJobProfDesc(self.select_sex, self.select_prof)
	self.node_list.desc_name_1.text.text = desc and desc[1] or ""
	self.node_list.desc_name_2.text.text = desc and desc[2] or ""

	local extra_role_model_data = {}
	local weapon_res_id = RoleWGData.GetJobWeaponId(self.select_sex, self.select_prof)


	local diy_list_info_list = DressingRoleDiyWGData.Instance:GetDiyAppearanceInfoBySexProf(self.select_sex, self.select_prof)
	local diy_list_info = diy_list_info_list and diy_list_info_list["project_list"][0]

	if diy_list_info and diy_list_info.is_set == 1 then

		local role_diy_appearance_info = diy_list_info.role_diy_appearance
		extra_role_model_data = {
			prof = self.select_prof,
			sex = self.select_sex,
			
			d_face_res = diy_list_info.new_face,
			d_hair_res = diy_list_info.new_hair,
			d_body_res = diy_list_info.new_body,
			weapon_res_id = weapon_res_id,
	
			eye_size = role_diy_appearance_info.eye_size,
			eye_shadow_color = role_diy_appearance_info.eye_shadow_color,
			eye_position = role_diy_appearance_info.eye_position,
	
			left_pupil_type = role_diy_appearance_info.left_pupil_type,
			left_pupil_size = role_diy_appearance_info.left_pupil_size,
			left_pupil_color = role_diy_appearance_info.left_pupil_color,
	
			right_pupil_type = role_diy_appearance_info.right_pupil_type,
			right_pupil_size = role_diy_appearance_info.right_pupil_size,
			right_pupil_color = role_diy_appearance_info.right_pupil_color,
	
			mouth_size =  role_diy_appearance_info.mouth_size,
			mouth_position =  role_diy_appearance_info.mouth_position,
			mouth_color = role_diy_appearance_info.mouth_color,
	
			face_decal_id = role_diy_appearance_info.face_decal_id,
			preset_seq = role_diy_appearance_info.preset_seq,
			hair_color = role_diy_appearance_info.hair_color,
		}
	else
		local face_res, hair_res, body_res = ChangeRolesWGData.Instance:GetRoleSetDIYInfo(self.select_sex, self.select_prof)
		local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(self.select_sex, self.select_prof)
		extra_role_model_data = {
			prof = self.select_prof,
			sex = self.select_sex,
			d_face_res = face_res,
			d_hair_res = hair_res,
			d_body_res = body_res,
			weapon_res_id = weapon_res_id,

			eye_size = default_diy_data.eye_size,
			eye_shadow_color = default_diy_data.eye_shadow_color,
			eye_position = default_diy_data.eye_position,
	
			left_pupil_type = default_diy_data.left_pupil_type,
			left_pupil_size = default_diy_data.left_pupil_size,
			left_pupil_color = default_diy_data.left_pupil_color,
	
			right_pupil_type = default_diy_data.right_pupil_type,
			right_pupil_size = default_diy_data.right_pupil_size,
			right_pupil_color = default_diy_data.right_pupil_color,
	
			mouth_size =  default_diy_data.mouth_size,
			mouth_position =  default_diy_data.mouth_position,
			mouth_color = default_diy_data.mouth_color,
	
			face_decal_id = default_diy_data.face_decal_id,
			preset_seq = default_diy_data.preset_seq,
			hair_color = default_diy_data.hair_color,
		}
	end

	self.model_display:SetRoleResid(0, nil, extra_role_model_data)
	self.model_display:FixToOrthographicOnUIScene()
end

function RoleView:ChangeRolesOnClickChangeRole()
	local cur_sex, cur_prof = RoleWGData.Instance:GetRoleSexProf()
	if self.select_sex == nil or self.select_prof == nil
		or (self.select_sex == cur_sex and self.select_prof == cur_prof) then
		return
	end

	local is_act = ChangeRolesWGData.Instance:GetRoleIsAct(self.select_sex, self.select_prof)
	if is_act then
		local next_time = ChangeRolesWGData.Instance:GetNextChangeCD()
		if next_time > 0 then
			return
		end

		local had_lover = RoleWGData.Instance.role_vo.lover_uid > 0
		if cur_sex ~= self.select_sex and had_lover then
			TipWGCtrl.Instance:ShowSystemMsg(Language.Common.DonNotChangeProf)
			return
		end

		RoleWGCtrl.Instance:ChangeRolesSendChangeProf(self.select_prof, self.select_sex)
		return
	end

	if nil == self.alert_tips then
		self.alert_tips = Alert.New()
	end

	local cfg = RoleWGData.Instance:GetJobConfig(self.select_sex, self.select_prof)
	local role_str = cfg and cfg.name1 or ""
	local str = string.format(Language.Role.ChangeRoleTips, role_str, role_str)
	self.alert_tips:SetLableString(str)
	self.alert_tips:SetOkFunc(function()
		local rmb_buy_cfg = ChangeRolesWGData.Instance:GetRMBBuyCfg(self.select_sex, self.select_prof)
		if rmb_buy_cfg then
			local default_face, default_hair, default_body = RoleWGData.Instance:GetAllDefaultPartID(self.select_sex,
				self.select_prof)

			local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(self.select_sex, self.select_prof)
			RoleWGCtrl.Instance:ChangeRolesSendDIYSelect(rmb_buy_cfg.seq, default_face, default_hair, default_body, default_diy_data)
			RechargeWGCtrl.Instance:Recharge(rmb_buy_cfg.price, rmb_buy_cfg.rmb_type, rmb_buy_cfg.rmb_seq)
		end
	end)

	self.alert_tips:Open()
end
