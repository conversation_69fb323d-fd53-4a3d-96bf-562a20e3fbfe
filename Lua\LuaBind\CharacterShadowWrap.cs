﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class CharacterShadowWrap
{
	public static void Register(LuaState L)
	{
		L.BeginClass(typeof(CharacterShadow), typeof(UnityEngine.MonoBehaviour));
		<PERSON><PERSON>Function("OnDrawShadow", OnDrawShadow);
		<PERSON><PERSON>("OnGeneratedCaster", OnGeneratedCaster);
		<PERSON><PERSON>unction("__eq", op_Equality);
		<PERSON><PERSON>ction("__tostring", ToLua.op_ToString);
		<PERSON><PERSON>("ShadowLevel", get_ShadowLevel, set_ShadowLevel);
		<PERSON><PERSON>("ShadowCaster", get_ShadowCaster, set_ShadowCaster);
		<PERSON><PERSON>("Bias", get_Bias, set_Bias);
		<PERSON><PERSON>("FixedBounds", get_FixedBounds, set_FixedBounds);
		<PERSON><PERSON>("IsFixedBounds", get_IsFixedBounds, set_IsFixedBounds);
		<PERSON><PERSON>("LocalBounds", get_LocalBounds, null);
		<PERSON><PERSON>lass();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnDrawShadow(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			CharacterShadow obj = (CharacterShadow)ToLua.CheckObject<CharacterShadow>(L, 1);
			UnityEngine.Rendering.CommandBuffer arg0 = (UnityEngine.Rendering.CommandBuffer)ToLua.CheckObject<UnityEngine.Rendering.CommandBuffer>(L, 2);
			System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer> arg1 = (System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>)ToLua.CheckDelegate<System.Action<UnityEngine.Rendering.CommandBuffer,UnityEngine.Renderer>>(L, 3);
			obj.OnDrawShadow(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int OnGeneratedCaster(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			CharacterShadow obj = (CharacterShadow)ToLua.CheckObject<CharacterShadow>(L, 1);
			CharacterShadows.ShadowCaster arg0 = (CharacterShadows.ShadowCaster)ToLua.CheckObject<CharacterShadows.ShadowCaster>(L, 2);
			obj.OnGeneratedCaster(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int op_Equality(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.Object arg0 = (UnityEngine.Object)ToLua.ToObject(L, 1);
			UnityEngine.Object arg1 = (UnityEngine.Object)ToLua.ToObject(L, 2);
			bool o = arg0 == arg1;
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ShadowLevel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			int ret = obj.ShadowLevel;
			LuaDLL.lua_pushinteger(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ShadowLevel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_ShadowCaster(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			CharacterShadows.ShadowCaster ret = obj.ShadowCaster;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ShadowCaster on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Bias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			UnityEngine.Vector3 ret = obj.Bias;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Bias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FixedBounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			UnityEngine.Bounds ret = obj.FixedBounds;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FixedBounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_IsFixedBounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			bool ret = obj.IsFixedBounds;
			LuaDLL.lua_pushboolean(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsFixedBounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LocalBounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			UnityEngine.Bounds ret = obj.LocalBounds;
			ToLua.Push(L, ret);
			return 1;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index LocalBounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ShadowLevel(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			int arg0 = (int)LuaDLL.luaL_checknumber(L, 2);
			obj.ShadowLevel = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ShadowLevel on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_ShadowCaster(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			CharacterShadows.ShadowCaster arg0 = (CharacterShadows.ShadowCaster)ToLua.CheckObject<CharacterShadows.ShadowCaster>(L, 2);
			obj.ShadowCaster = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index ShadowCaster on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_Bias(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 2);
			obj.Bias = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index Bias on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_FixedBounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			UnityEngine.Bounds arg0 = ToLua.ToBounds(L, 2);
			obj.FixedBounds = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index FixedBounds on a nil value");
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int set_IsFixedBounds(IntPtr L)
	{
		object o = null;

		try
		{
			o = ToLua.ToObject(L, 1);
			CharacterShadow obj = (CharacterShadow)o;
			bool arg0 = LuaDLL.luaL_checkboolean(L, 2);
			obj.IsFixedBounds = arg0;
			return 0;
		}
		catch(Exception e)
		{
			return LuaDLL.toluaL_exception(L, e, o, "attempt to index IsFixedBounds on a nil value");
		}
	}
}

