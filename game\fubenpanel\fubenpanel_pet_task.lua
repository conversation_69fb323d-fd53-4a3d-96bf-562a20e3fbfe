PetTaskView = PetTaskView or BaseClass(SafeBaseView)

function PetTaskView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fubenpet_info1")
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_fubenpet_info2")
	
	self.active_close = false
	self.view_cache_time = 0
	self.view_name = "PetTaskView"

	self.fb_star = -1
	self.curr_wave_index = -1
	self.main_ui_is_init = false
end

function PetTaskView:__delete()
	self.is_load_complete = nil

end

function PetTaskView:ResetTaskPanel()
	if self.task_panel_change then
		MainuiWGCtrl.Instance:ResetTaskPanel()
		self.task_panel_change = false
	end
end

function PetTaskView:ReleaseCallBack()
	self.is_load_complete = nil
	self.data = nil
	if self.delay_hide_quest then
		GlobalTimerQuest:CancelQuest(self.delay_hide_quest)
		self.delay_hide_quest = nil
	end



	if self.pet_reward_list then
        self.pet_reward_list:DeleteMe()
        self.pet_reward_list = nil
    end

	if self.info then
		self.info:DeleteMe()
		self.info = nil
	end


	self.star = -1
	self.wave = -1

	if self.star_effect then
		self.star_effect = nil
	end

	GlobalTimerQuest:CancelQuest(self.release_timer)
	self.release_timer = nil
	MainuiWGCtrl.Instance:SetAutoGuaJi(false)

	self.is_fisrt = nil

	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end

	if self.star_view then
		self.star_view:DeleteMe()
		self.star_view = nil
	end

end

function PetTaskView:OnLoadingComplete2()
	local parent = MainuiWGCtrl.Instance:GetTaskOtherContent()
	self.info = PetTaskList.New(self.node_list.root_fuben_info)
	self.info:SetInstanceParent(parent)
	self.info:AnchorPosition(0,-63)
	self:Flush()
end


function PetTaskView:LoadCallBack()
	FuBenPanelWGCtrl.Instance:GetFuBenPanemView():SetActive(false)

	if MainuiWGCtrl.Instance:IsLoadMainUiView() then
		self:InitCallBack()
		self:CreatePetFbCells()
	else
		self.mainui_open_complete_handle = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind(self.InitCallBack, self))
	end
	local level = FuBenPanelWGData.Instance:GetPetLevel()
	self.max_hp = FuBenPanelWGData.Instance:GetPetGoddessMaxHpByLevel(level)
	if Scene.Instance:GetSceneType() == SceneType.FakePetFb then
		local cfg = FuBenPanelWGData.Instance:GetFakePetFbXianNvCfg()
		if cfg then
			self.max_hp = cfg.shengming_max
		end
	end

	self.old_hp = self.max_hp
	MainuiWGCtrl.Instance:SetAutoGuaJi(true)

	Runner.Instance:AddRunObj(self,8)

	-- XUI.AddClickEventListener(self.node_list["circle_mask"], BindTool.Bind1(self.OnClickXianNvHead, self))

	self.record_time = 0
	self.is_can_show_tip = false
	self.node_list["hit_tip"]:SetActive(false)
	self.node_list["hit_tip_2"]:SetActive(false)
	self.node_list["hit_tip_group"]:SetActive(false)
	self.is_show_two = false
	self.is_show_three = false

	self.is_fisrt = true
	self.is_load_complete = true
	self.node_list["layout_guaiwu"]:SetActive(false)
end

function PetTaskView:OnClickXianNvHead()
	local x, y = FuBenPanelWGData.Instance:GetXianNvPos()
	if x == nil or y == nil then
		return
	end

	GuajiWGCtrl.Instance:SetMoveToPosCallBack(function()
		GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
	end)
	GuajiWGCtrl.Instance:MoveToPos(Scene.Instance:GetSceneId(), x, y, 1)
end

--物品奖励格子
function PetTaskView:CreatePetFbCells()
	--self.pet_fb_cells = {}
	--for i = 0, 2 do
	--	if not self.pet_fb_cells[i] then
	--		local ph = self.node_list["item_cell" .. i]
	--		local pet_cell = ItemCell.New(ph)
	--		self.pet_fb_cells[i] = pet_cell
	--	end
	--end

	self.pet_reward_list = AsyncListView.New(PetFbRewardItemRender, self.node_list["pet_reward_list"])
end

function PetTaskView:OpenCallBack()
	if self.is_load_complete and self.info then
        self.info:SetActive(true)
    end
end

function PetTaskView:InitCallBack()
	local mainui_ctrl = MainuiWGCtrl.Instance
	local parent = mainui_ctrl:GetTaskOtherContent()
	if nil ==self.node_list.root_fuben_info or nil == parent then return end
	self.info = PetTaskList.New(self.node_list.root_fuben_info)
	self.info:SetInstanceParent(parent)
	--self.info:AnchorPosition(151,-135)
	self.info:AnchorPosition(0,0)
	mainui_ctrl:SetAutoGuaJi(true)
	mainui_ctrl:SetTaskPanel(false,152,-181.6)
	self.main_ui_is_init = true
	self:CreatePetFbCells()
	self:Flush()
end

function PetTaskView:CloseCallBack()
	Runner.Instance:RemoveRunObj(self)
	self.fb_star = -1
	self.is_show_two = false
	self.is_show_three = false
	self:ResetTaskPanel()

    if self.info then
        self.info:SetInstanceParent(self.node_list.panel)
        self.info:SetActive(false)
     end
end

function PetTaskView:Update(now_time, elapse_time)
	self.now_time = now_time
	if self.now_time >  self.record_time + 2 then
		self.is_can_show_tip = true
		self.record_time = self.record_time + 2
	end
end

function PetTaskView:ShowIndexCallBack()
	self.task_panel_change = true
	self:Flush()
	self.node_list["lbl_boshu"].text.text = ""
	self:InitCallBack()
end

function PetTaskView:OnFlush()
	if not self.main_ui_is_init  then return end
	local pet_scence_info = FuBenPanelWGData.Instance:GetPetScenceAllInfo()
	if pet_scence_info and not IsEmptyTable(pet_scence_info) then
		local pet_fb_cfg = FuBenPanelWGData.Instance:GetPetItemByLayer(pet_scence_info.level)
		if pet_fb_cfg then
			if self.is_fisrt then
		        local drop_data_list = FuBenPanelWGData.Instance:GetPetDropList(pet_scence_info.level)
		        if Scene.Instance:GetSceneType() == SceneType.FakePetFb then
					drop_data_list = FuBenPanelWGData.Instance:GetFakePetDropList()
				end
				self.pet_reward_list:SetDataList(drop_data_list)
		        self.is_fisrt = false
		    end
		end

		self:SetGoddessBlood(pet_scence_info.goddess_hp,self.max_hp)

		--波数
		if pet_scence_info.curr_wave_index then
			if self.fb_star == pet_scence_info.cur_star_num and self.curr_wave_index == pet_scence_info.curr_wave_index then
				return
			end
			self.fb_star = pet_scence_info.cur_star_num
			self.curr_wave_index = pet_scence_info.curr_wave_index

			self.node_list["lbl_boshu"].text.text = pet_scence_info.curr_wave_index .. "/" .. pet_scence_info.max_wave_count
			if self.info then
				self.info:SetBoCount(string.format("[%d/%d]",pet_scence_info.curr_wave_index, pet_scence_info.max_wave_count))
			end
		end

		--波数来袭提示
		local should_do_anim = false
		if pet_scence_info.next_wave_refresh_timestamp - TimeWGCtrl.Instance:GetServerTime() > 0 and pet_scence_info.curr_wave_index > 0 then
			local level = FuBenPanelWGData.Instance:GetPetLevel()
			local cfg  = FuBenPanelWGData.Instance:GetPetDifficulty(level)
			local is_false = FuBenPanelWGData.Instance:GetPetBossId(cfg,pet_scence_info.curr_wave_index)
			self.node_list["img_guaiwu_num"].text.text = pet_scence_info.curr_wave_index

			if is_false == 1 then
				should_do_anim = true
			else
				should_do_anim = true
			end
		else
			should_do_anim = false
		end
		if should_do_anim then
			-- 策划大佬说屏蔽 第几波开始
			 --self.node_list["layout_guaiwu"]:SetActive(true)
			 --self:DoAnimHide(self.node_list["layout_guaiwu"])
		end
 	end
 	self:OnFlushStar()

	self.node_list.fb_desc_tips.text.text = Language.FuBenPanel.FubenPerBossTips1
end

function PetTaskView:SetGoddessBlood(value, max_value)
	if nil == value or nil == max_value or max_value == 0 then
		return
	end

	if self.is_can_show_tip == true and (self.is_can_show_hit_tip == nil or self.is_can_show_hit_tip == true) then
		if self.old_hp ~= value then
			self:CalcShowImage(value, max_value)
			--self:DoAnim(self.node_list["hit_tip_group"])
			self.is_can_show_tip = false
		end
	end
	--self.node_list["HurtWaring"]:SetActive(self.old_hp ~= value and value > 0)
	if self.old_hp - value > 0 then
		local float_blood = self.old_hp - value
		float_blood = float_blood * (-1)
		GlobalEventSystem:Fire(OtherEventType.GODDESS_HURT_FLOAT_TEXT, float_blood)
	end

	self.old_hp = value
	--slider
	if self.node_list["goddess_blood"] then
		if value >= max_value then
			self.node_list["precent_num_text"].text.text = "100%"
		else
			self.node_list["precent_num_text"].text.text = math.ceil(value / max_value * 100) .."%"
		end
		self.node_list["goddess_blood"].slider.value = value / max_value
	end
end

function PetTaskView:CalcShowImage(value, max_value)
	if nil == value or nil == max_value or max_value == 0 then
		return
	end

	self.node_list["hit_tip"]:SetActive(false)
	self.node_list["hit_tip_2"]:SetActive(false)


	local per = value / max_value
	if per > 0.2 and value < max_value then
		self.node_list["hit_tip"]:SetActive(true)
	elseif per < 0.2 and per > 0.1 and self.is_show_two == false then
	self.node_list["hit_tip_2"]:SetActive(true)
	self.node_list["hit_per"].text.text = 20
		self.is_show_two = true
	elseif per < 0.1 and per > 0 and self.is_show_three == false then
	self.node_list["hit_tip_2"]:SetActive(true)
	self.node_list["hit_per"].text.text = 10
		self.is_show_three = true
	end
end

function PetTaskView:DoAnim(node)
	node:SetActive(true)
	--node.rect.anchoredPosition = Vector2(0,-300)

	--node.rect:DOAnchorPosY(-150, 2):OnComplete(function()
	--	node:SetActive(false)
    --
	--end):SetEase(DG.Tweening.Ease.Linear)
	if self.delay_hide_quest then
		GlobalTimerQuest:CancelQuest(self.delay_hide_quest)
	end
	self.delay_hide_quest = GlobalTimerQuest:AddDelayTimer(function()
		if node and not IsNil(node.gameObject) then
			node:SetActive(false)
		end
	end,3)
end

function PetTaskView:DoAnimHide(node)
	node:SetActive(true)
	self.is_can_show_hit_tip = false
	GlobalTimerQuest:AddDelayTimer(function()
		if node and not IsNil(node.gameObject) then
			node:SetActive(false)
		end
		self.is_can_show_hit_tip = true
	end,3)
end

function PetTaskView:OnFlushStar()
    if nil == self.star_view then
		self.star_view =  FuBenStarClock.New(self.node_list.star_view)
    end
    local cfg = FuBenPanelWGData.Instance:GetPetLevelCfg()
	local data = {}
	if cfg and not IsEmptyTable(cfg) then
		data = {time3 = cfg.three_star_time, time2 = cfg.two_star_time, time1 = cfg.one_star_time, time0 = cfg.zero_star_time,
	 		per0 = cfg.star0, per1 =cfg.star1, per2 = cfg.star2, per3 = cfg.star3, str = Language.Boss.StarAniStr,}
	end
	self.star_view:SetData(data)
	self.star_view:Flush()
end

PetTaskList = PetTaskList or BaseClass(BaseRender)
function PetTaskList:__init()
	-- body
end

function PetTaskList:__delete()
	-- body
end

function PetTaskList:AnchorPosition( x,y )
	self.view.rect.anchoredPosition = Vector2(x,y)
end

function PetTaskList:SetBoCount( value )
	self.node_list.lbl_boshu.text.text = value
end

function PetTaskList:SetDesc( str )
	--self.node_list.rich_txt.text.text = str
end

function PetTaskList:LoadCallBack()
	self:SetDesc(Language.FuBenPanel.PetTask)
end
