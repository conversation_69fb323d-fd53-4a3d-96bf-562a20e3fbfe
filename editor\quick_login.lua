require("utils/playerprefs_util")
local QuickLogin= {}
local is_init = false
local module_list = {}
QuickLogin._class_type = true
-- local reconnectview = nil

local UnityApplication = UnityEngine.Application
local UnitySysInfo = UnityEngine.SystemInfo

function QuickLogin:IsOpenQuick()
	return "1" == PlayerPrefsUtil.GetString("a3_fanli_is_quick_login")
end

local TypeUnityMaterial = typeof(UnityEngine.Material)
local TypeRuntimeAnimatorController = typeof(UnityEngine.RuntimeAnimatorController)
local TypeVideoClip = typeof(UnityEngine.Video.VideoClip)
local TypeRenderTexture = typeof(UnityEngine.RenderTexture)

local LoadingCamera = GameObject.Find("Loading/UICameraLoading")
function QuickLogin:Start()
	GlobalUnityTime = UnityEngine.Time.unscaledTime

	LoadingCamera:GetComponent(typeof(UnityEngine.Camera)).enabled = false
	ResMgr:LoadLocalLuaManifest("LuaAssetBundle/LuaAssetBundle.lua")
	ResMgr:LoadLocalManifest("AssetBundle.lua")
	self:ReqireLua()
	self:InitModule()
	self:RegisterProtocols()
	self:ReqInitHttp()
	PlayerPrefsUtil.DeleteKey("a3_fanli_is_quick_login")
	-- PlayerPrefsUtil.DeleteKey("a2_fanli_is_local_windows_debug_exe_quick_login")

	collectgarbage("setpause", 100)
	collectgarbage("setstepmul", 5000)
end

function QuickLogin:Stop()
	if Runner ~= nil and Runner.Instance ~= nil and Runner.Instance:IsExistRunObj(self) then
		Runner.Instance:RemoveRunObj(self)
	end

	-- if reconnectview then
	-- 	reconnectview:DeleteMe()
	-- end
	for i=#module_list, 1, -1 do
		module_list[i]:DeleteMe()
	end
	module_list = {}
end

function QuickLogin:ReqireLua()
	local agentTable = {
		["pev"] = "agent/dev/agent_adapter",
		["dev"] = "agent/dev/agent_adapter",
	}

	local agentPath = agentTable[ChannelAgent.GetChannelID()]
	require(agentPath)
	require("init/global_config")

	require("init/init_wg_ctrl")
	require("manager/report_manager")

	local list = require("game/common/require_list")
	for _, path in ipairs(list) do
		if string.match(path, "^config/auto_new/.*") then
			CheckLuaConfig(path, require(path))
		else
			require(path)
		end
	end

	collectgarbage("setpause", 100)
	collectgarbage("setstepmul", 5000)

	-- 加载品质控制器
	QualityConfig.ClearInstance()
	local loader = AllocResAsyncLoader(self, "QualityConfig")
	loader:Load(
		"misc/quality",
		"QualityConfig",
		typeof(QualityConfig),
		function(config)
			if IsLowMemSystem then -- 低内存系统不开启实时阴影
				QualityConfig.SetOverrideShadowQuality(0, 0)
			end

			if config ~= nil then
				print_log("Load the QualityConfig.")
				if PlayerPrefsUtil.HasKey("a3_custom_quality_level") then
					local quality_level = PlayerPrefsUtil.GetInt("a3_custom_quality_level")
					QualityConfig.QualityLevel = quality_level
				end
			else
				print_error("Can not load the QualityConfig")
			end
		end)

	if (G_IsDeveloper or GLOBAL_DELETE_ME_CHECK_SWITCH) and CheckMem then
		CheckMem:InitGlobal()
	end
end

function QuickLogin:InitModule()
	require("play")

	module_list = {}
	table.insert(module_list, Runner.New())

	GlobalEventSystem = EventSystem.New()					-- 全局事件系统
	table.insert(module_list, GlobalEventSystem)

	GlobalTimerQuest = TimerQuest.New()						-- 定时器
	table.insert(module_list, GlobalTimerQuest)

	Runner.Instance:AddRunObj(GlobalEventSystem, 3)

	table.insert(module_list, CountDown.New())
	table.insert(module_list, CountDownManager.New())
	table.insert(module_list, GameNet.New())
	table.insert(module_list, ConfigManager.New())
	-- table.insert(module_list, StepPool.New())
	table.insert(module_list, GameMapHelper.New())
	table.insert(module_list, PreloadManager.New())
	table.insert(module_list, EffectManager.New())
	table.insert(module_list, RoadFindWay.New())			-- 寻路
	table.insert(module_list, ModulesWGCtrl.New(true))
	table.insert(module_list, TipsSystemManager.New())
	table.insert(module_list, ZCTipsSystemManager.New())
	-- reconnectview = ReconnectView.New()

	table.insert(module_list, TipsFloatingManager.New())
	-- table.insert(module_list, TipsFloatingName.New())
	-- table.insert(module_list, TipsActivityNoticeManager.New())
	-- table.insert(module_list, RareItemTipManager.New())

	COMMON_CONSTS.SCENE_LOADING_MIN_TIME = 0.2
	is_init = true

  	-- Runner.Instance:AddRunObj(ResMgr, 1)
    -- 资源TimerQuest
    -- ResPoolMgr:Startup()
	LoginWGCtrl.CreateClickEffectCanvas(QuickLogin)
end

function QuickLogin:Update(now_time, elapse_time)
	if is_init then
		Runner.Instance:Update(now_time, elapse_time)
	end
end

function QuickLogin:RegisterProtocols()
	local list = {
		{SCLoginAck, "OnLoginAck"},
		{SCAccountKeyError, "OnAccountKeyError"},
		{SCRoleListAck, "OnRoleListAck"},
		{SCMergeRoleListAck, "OnMergeRoleListAck"},
		{SCCreateRoleAck, "OnCreateRoleAck"},
		{SCUserEnterGSAck, "OnUserEnterGSAck"},
		{SCDisconnectNotice, "OnDisconnectNotice"},
	}

	for _, v in ipairs(list) do
		local msg_type = ProtocolPool.Instance:Register(v[1])
		GameNet.Instance:RegisterMsgOperate(msg_type, BindTool.Bind1(self[v[2]], self))
	end
end

function QuickLogin:OnAccountKeyError(protocol)
	if protocol.result == ACCOUNT_KEY_ERROR.MD5 then
		TipWGCtrl.Instance:ShowSystemMsg("MD5 Error !!!")
	end
end

function QuickLogin:ReqInitHttp()
	InitWGCtrl:Init()
	local init_url = GLOBAL_CONFIG.local_package_info.config.init_urls[1]
	local data = {
		plat_id = GLOBAL_CONFIG.local_package_info.config.agent_id,
		pkg = GLOBAL_CONFIG.local_package_info.version,
		device = DeviceTool.GetDeviceID()
	}

	local link_data_str = PhpHandle.GetParamsToStr(data)
	local sign_str = PhpHandle.GetParamsToSignStr(data)
	local md5_sign = MD52.GetMD5(sign_str)
	local url = string.format("%s?%s&sign=%s", init_url, link_data_str, md5_sign)
	print("[QuickLogin]ReqInitHttp start request", url)

	HttpClient:Request(url, function(p_url, is_succ, data)
		print("[QuickLogin]ReqInitHttp request back", url, is_succ)
		if not is_succ then
			print_error("[QuickLogin]ReqInitHttp Fail", p_url)
			return
		end

		local init_info = cjson.decode(data)
		local php_data = init_info ~= nil and init_info.data or nil
		if type(init_info) == "table" and type(php_data) ~= "table" and Base64Decode ~= nil then
			local b64 = Base64Decode(init_info.data, GlobalPHPInfoDecodeKey)
			php_data = cjson.decode(b64)
		end
		
		if init_info == nil or php_data == nil then
			print_error("[QuickLogin]ReqInitHttp Fail", p_url)
			return
		end

		GLOBAL_CONFIG.param_list = php_data.param_list
		GLOBAL_CONFIG.param_list.shield_chat_voice = 1 -- 强制屏蔽语音聊天
		GLOBAL_CONFIG.api_urls = php_data.api_urls
		GLOBAL_CONFIG.chat_reward_word_list = php_data.chat_reward_word_list
		GlobalUrl = php_data.param_list.global_url or DefaultGlobalUrl

		GLOBAL_CONFIG.server_info = php_data.server_info
		local version_info = php_data.version_info
		GLOBAL_CONFIG.version_info = {}
		GLOBAL_CONFIG.version_info.php_package_info = version_info.package_info

		if nil ~= version_info.assets_info then
			GLOBAL_CONFIG.version_info.php_assets_info = version_info.assets_info
			ResMgr:SetAssetVersion(version_info.assets_info.version)
			ResMgr:SetAssetLuaVersion(version_info.assets_info.version)

			-- AssetManager.AssetVersion = version_info.assets_info.version
		end

		print("[QuickLogin]SetDownloadingURL", GLOBAL_CONFIG.param_list.cdn_url)
		ResMgr:SetDownloadingURL(GLOBAL_CONFIG.param_list.cdn_url)
		self:Preload()
	end)
end

function QuickLogin:Preload()
	local preload_list_cfg = PreloadManager.Instance:GetLoadListCfg()
	local total_count = #preload_list_cfg
	local loaded_count = 0
	local loaded_list = {}

	local call_back = function ()
		loaded_count = loaded_count + 1
		if loaded_count >= total_count then
			-- self:ReqInitHttp()
			self:ReqConnectLoginServer()
		end
	end

	for _, v in ipairs(preload_list_cfg) do
		if v[3] == TypeUnityMaterial then
			ResPoolMgr:GetMaterial(v[1], v[2], call_back, false)
		elseif v[3] == TypeRuntimeAnimatorController then
			ResPoolMgr:GetAnimatorController(v[1], v[2], call_back, false)
		elseif v[3] == TypeVideoClip then
			ResPoolMgr:GetVideoClip(v[1], v[2], call_back, false)
		elseif v[3] == TypeRenderTexture then
			ResPoolMgr:GetRenderTexture(v[1], v[2], call_back, false)
		else
			ResPoolMgr:GetPrefab(v[1], v[2], call_back, false)
		end
	end
end

function QuickLogin:ReqConnectLoginServer()
	local server_id = PlayerPrefsUtil.GetString("a3_fanli_quick_login_server_id")
	if nil ~= server_id then
		server_id = tonumber(server_id)
	end

	local cfg = nil
	for _, v in pairs(GLOBAL_CONFIG.server_info.server_list) do
		if v.id == server_id then
			cfg = v
			break
		end
	end

	if nil == cfg then
		print(string.format("[QuickLogin] server %s is not exist", server_id or ""))
		return
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	user_vo.plat_server_id = cfg.id
	GameNet.Instance:SetLoginServerInfo(cfg.ip, cfg.port)

	GameNet.Instance:AsyncConnectLoginServer(5)
	GlobalEventSystem:Bind(LoginEventType.LOGIN_SERVER_CONNECTED, BindTool.Bind(self.OnConnectLoginServer, self))

	GlobalEventSystem:Bind(LoginEventType.LOGIN_SERVER_DISCONNECTED, BindTool.Bind(self.OnDisconnectLoginServer, self))
end

function QuickLogin:OnDisconnectLoginServer(custom_disconnect_reason, custom_disconnect_notice_type, real_disconnect_reason, disconnect_detail)
	print("[QuickLogin] login server disconnect")
	if self.login_server_heartbeat_timer then
		GlobalTimerQuest:CancelQuest(self.login_server_heartbeat_timer)
		self.login_server_heartbeat_timer = nil
	end
	
	-- 非手动断线，显示断线提示
	if real_disconnect_reason ~= GameNet.DisconnectReason.Manual then
		if TipWGCtrl.Instance ~= nil then
			TipWGCtrl.Instance:ShowDisconnected(custom_disconnect_notice_type)
		end
	end
end

function QuickLogin:OnConnectLoginServer(is_suc)
	print("[QuickLogin] connect login server success")

	if is_suc then
		-- 请求php login
		local account_name = PlayerPrefsUtil.GetString("a3_fanli_quick_login_user_name")
		if nil == account_name then
			account_name = math.floor(math.random(1, ********))
		end

		local plat_id = CHANNEL_AGENT_ID
		local partner_login_data = cjson.encode({uid = account_name})

		local loginData = {
			plat_id = plat_id,
			partner_login_data = partner_login_data,
		}

		local login_url = GLOBAL_CONFIG.api_urls.client.login
		PhpHandle.HandlePhpJsonPostRequest(login_url, loginData, loginData,
		function (cb_data)
				local data = cb_data.data
				local uservo = GameVoManager.Instance:GetUserVo()
				uservo.account_user_id = data.uid
				uservo.login_time = data.login_time
				uservo.access_token = data.access_token
				uservo.is_white = data.is_white
				uservo.plat_session_key = data.sign

				PlayerPrefsUtil.SetString("cahce_account_user_id", data.uid)
				PlayerPrefsUtil.SetString("account_name", account_name)
				PlayerPrefsUtil.SetString("account_token_" .. (data.uid or ""), data.access_token)
				PlayerPrefsUtil.SetString("plat_session_key_" .. (data.uid or ""), data.sign)
				PlayerPrefsUtil.SetString("is_white_" .. (data.uid or ""), data.is_white)

				if LoginWGData.Instance then
					LoginWGData.Instance:SetPHPAccountRoleList(data.role_list)
				end

				self:ReqLogin()
		end,

		function (cb_data)
				print_error("---login 失败--", cb_data)
		end)
	else
		TipWGCtrl.Instance:ShowDisconnected(DISCONNECT_NOTICE_TYPE.CONNECT_LOGIN_SERVER_ERROR)
	end
end

function QuickLogin:ReqLogin()
	print("[QuickLogin] ReqLogin")

	local user_vo = GameVoManager.Instance:GetUserVo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSLoginReq)
	protocol.rand_1 = math.floor(math.random(1000000, ********))
	protocol.login_time = os.time()
	protocol.key = user_vo.plat_session_key
	protocol.rand_2 = math.floor(math.random(1000000, ********))
	protocol.plat_name = user_vo.account_user_id
	protocol.plat_server_id = user_vo.plat_server_id
	protocol.access_token = user_vo.access_token

	local server_id_str = PlayerPrefsUtil.GetString("PRVE_SRVER_ID")
	local pre_server_id = 0
	-- 清空旧缓存
	if type(server_id_str) == "number" or tonumber(server_id_str) then
		PlayerPrefsUtil.SetString("PRVE_SRVER_ID", "")
	else
		local server_id_list = StrToTable(server_id_str)
		local account_name = PlayerPrefsUtil.GetString("cahce_account_user_id")
		if server_id_list and not IsEmptyTable(server_id_list) and
			server_id_list[account_name] and #server_id_list[account_name] then
			pre_server_id = tonumber(server_id_list[account_name][1]) or 0
		end
	end

	if user_vo.account_user_id and string.find(user_vo.account_user_id, "_") then
		user_vo.plat_server_id = pre_server_id or 0
		user_vo.account_user_id = PlayerPrefsUtil.GetString("cahce_account_user_id")
		protocol.plat_name = user_vo.account_user_id
		protocol.plat_server_id = user_vo.plat_server_id
	end

	protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())

	if not self.login_server_heartbeat_timer then
		self.login_server_heartbeat_timer = GlobalTimerQuest:AddRunQuest(function()
				local cmd = ProtocolPool.Instance:GetProtocol(CSLHeartBeat)
				cmd:EncodeAndSend(GameNet.Instance:GetLoginNet())
			end, 10)
	end
end

function QuickLogin:OnRoleListAck(protocol)
	print("[QuickLogin] OnRoleListAck", protocol.result)

	if nil ~= self.login_data then
		self.login_data:DeleteMe()
	end
	self.login_data = LoginWGData.New()
	self.login_data:SetRoleListAck(protocol)
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
	local user_vo = GameVoManager.Instance:GetUserVo()

	if 0 == protocol.result and protocol.count > 0 then
		local role_seq = 1
		local cache_seq = PlayerPrefsUtil.GetInt("a3_fanli_is_quick_login_role_seq")
		if cache_seq > 0 then
			if cache_seq > protocol.count then
				self:ReqCreateRole()
				return
			else
				role_seq = cache_seq
			end
		end

		local info = protocol.role_list[role_seq]
		user_vo:SetNowRole(info.role_id)
		mainrole_vo.name = info.role_name
		self.SendRoleReq()

	elseif -6 == protocol.result then -- 没有角色
		if 0 == #protocol.role_list then
			CacheManager.Instance:ClearAllCache()
		end
		self:ReqCreateRole()
	end
end

function QuickLogin:OnMergeRoleListAck(protocol)
	print("[QuickLogin] OnMergeRoleListAck", protocol.count)
	if nil ~= self.login_data then
		self.login_data:DeleteMe()
	end
	self.login_data = LoginWGData.New()
	self.login_data:SetRoleListAck(protocol)
	if protocol.count == 0 then
		self:ReqCreateRole()
	else
		local user_vo = GameVoManager.Instance:GetUserVo()
		user_vo:ClearRoleList()
		for i = 1, protocol.count do
			user_vo:AddRole(
				protocol.role_list[i].role_id,
				protocol.role_list[i].role_name,
				protocol.role_list[i].avatar,
				protocol.role_list[i].sex,
				protocol.role_list[i].prof,
				protocol.role_list[i].country,
				protocol.role_list[i].level,
				protocol.role_list[i].create_time,
				protocol.role_list[i].online_time,
				protocol.role_list[i].last_logout_time,
				protocol.role_list[i].capability,
				protocol.role_list[i].avatar_key_big,
				protocol.role_list[i].avatar_key_small
				)
		end

		local role_seq = 1
		local cache_seq = PlayerPrefsUtil.GetInt("a3_fanli_is_quick_login_role_seq")
		if cache_seq > 0 then
			if cache_seq > protocol.count then
				self:ReqCreateRole()
				return
			else
				role_seq = cache_seq
			end
		end

		user_vo:SetNowRole(protocol.role_list[role_seq].role_id)
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		mainrole_vo.name = protocol.role_list[role_seq].role_name
		self.SendRoleReq()
	end
end


local rand_name = {"a","c","d","e","f","g","h","i","j","k","l","m","n","o","p","q","r","s","t","u","v","w","x","y","z"}
-- 【职业改动修改】
local prof_list = {
	{sex = 0, prof = 2},
	{sex = 1, prof = 1},
	{sex = 0, prof = 1},
	{sex = 1, prof = 3},
	{sex = 0, prof = 3},
	{sex = 1, prof = 4},
	{sex = 0, prof = 4},
}

function QuickLogin.ReqCreateRole()
	print("[QuickLogin] ReqCreateRole")

	local sex = PlayerPrefsUtil.GetInt("a3_fanli_is_quick_login_sex")
	local prof = PlayerPrefsUtil.GetInt("a3_fanli_is_quick_login_prof")
	-- 随机
	if sex < 0 or prof <= 0 then
		-- 【职业改动修改】
		local random_index = 1
		local function random_fun()
			random_index = math.floor(math.random(1, #prof_list))
			local data = prof_list[random_index]
			if RoleWGData.Instance:GetIsShieldSexAndProf(data.sex, data.prof) then
				random_fun()
			end
		end
		random_fun()

		sex = prof_list[random_index].sex
		prof = prof_list[random_index].prof
	end

	local user_vo = GameVoManager.Instance:GetUserVo()
	local protocol = ProtocolPool.Instance:GetProtocol(CSCreateRoleReq)
	protocol.plat_name = user_vo.account_user_id
	protocol.role_name = ""
	for i=1, 6 do
		protocol.role_name = protocol.role_name .. (rand_name[math.random(52)] or math.random(1,2))
	end
	protocol.login_time = os.time()
	protocol.key = user_vo.plat_session_key
	protocol.plat_server_id = user_vo.plat_server_id
	protocol.avatar = 1
	protocol.prof = prof
	protocol.sex = sex
	protocol.had_create_role_num = 1
	protocol.plat_spid = tostring(GLOBAL_CONFIG.local_package_info.config.agent_id)
	protocol.pay_money = LoginWGData.Instance:GetUserMoney()
	local default_face_res_id, default_hair_res_id, default_body_res_id = RoleWGData.Instance:GetAllDefaultPartID(protocol.sex, protocol.prof)
	protocol.default_face_res_id = default_face_res_id
	protocol.default_hair_res_id = default_hair_res_id
	protocol.default_body_res_id = default_body_res_id


	local default_diy_data = RoleDiyAppearanceWGData.Instance:GetDefaultPresetDiyCfgBySexAndProf(protocol.sex, protocol.prof)
	if not default_diy_data then
		return
	end

	protocol.hair_color = default_diy_data.hair_color or {}
	protocol.eye_size = default_diy_data.eye_size or 0
	protocol.eye_position = default_diy_data.eye_position or 0
	protocol.eye_shadow_color = default_diy_data.eye_shadow_color or {}
	protocol.left_pupil_type = default_diy_data.left_pupil_type or 0
	protocol.left_pupil_size = default_diy_data.left_pupil_size or 0
	protocol.left_pupil_color = default_diy_data.left_pupil_color or {}
	protocol.right_pupil_type = default_diy_data.right_pupil_type or 0
	protocol.right_pupil_size = default_diy_data.right_pupil_size or 0
	protocol.right_pupil_color = default_diy_data.right_pupil_color or {}
	protocol.mouth_size = default_diy_data.mouth_size or 0
	protocol.mouth_position = default_diy_data.mouth_position or 0
	protocol.mouth_color = default_diy_data.mouth_color or {}
	protocol.face_decal_id = default_diy_data.face_decal_id or 0
	protocol.preset_seq = default_diy_data.preset_seq or 1

	protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())
end

function QuickLogin:OnCreateRoleAck(protocol)
	print("[QuickLogin] OnCreateRoleAck", protocol.result)
	if 0 == protocol.result then
		local user_vo = GameVoManager.Instance:GetUserVo()
		user_vo:ClearRoleList()
		user_vo:AddRole(
			protocol.role_id,
			protocol.role_name,
			protocol.avatar,
			protocol.sex,
			protocol.prof,
			0,
			protocol.level,
			protocol.create_time)

		user_vo:SetNowRole(protocol.role_id)
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		mainrole_vo.name = protocol.role_name

		self.SendRoleReq()
	else
		local show_msg = "创建角色失败"
		if -1 == protocol.result then
			show_msg = "创角色已满，请更换账号或者移步新服"
		elseif -2 == protocol.result then
			show_msg = "该昵称已存在, 请修改昵称"
		elseif -3 == protocol.result then
			show_msg = "名字不合法"
		elseif -4 == protocol.result then
			show_msg = "服务器爆满，请移步新服吧"
		elseif -5 == protocol.result then
			show_msg = "角色昵称长度非法"
		end

		TipWGCtrl.Instance:ShowSystemMsg(show_msg)
	end
end

function QuickLogin.SendRoleReq()
	print("[QuickLogin] SendRoleReq")
	local user_vo = GameVoManager.Instance:GetUserVo()
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()

	local protocol = ProtocolPool.Instance:GetProtocol(CSRoleReq)
	protocol.rand_1 = math.floor(math.random(1, ********))
	protocol.login_time = os.time()
	protocol.key = user_vo.plat_session_key
	protocol.rand_2 = math.floor(math.random(1000000, ********))
	protocol.role_id = mainrole_vo.role_id
	protocol.plat_name = user_vo.account_user_id
	protocol.plat_server_id = user_vo.plat_server_id
	protocol:EncodeAndSend(GameNet.Instance:GetLoginNet())
end

function QuickLogin:OnLoginAck(protocol)
	TimeWGCtrl.Instance:SetServerTime(protocol.server_time, protocol.pi_time)

	local error_code = protocol.result
	if 0 ~= error_code then
		TipWGCtrl.Instance:ShowSystemMsg(string.format("登陆认证失败: %d.", error_code))

		if LoginAckResult.LOGIN_LOGIN_FORBID == error_code then
			local str = Language.Login.CountBeBand
			local ok_func = function ()
				GlobalEventSystem:Fire(LoginEventType.LOGOUT)
			end
			TipWGCtrl.Instance:OpenConfirmAlertTips(str, ok_func)
		else
			local disconnect_notice_type = LOGIN_ACK_RESULT_TO_DISCONNECT_NOTICE_TYPE[error_code]
										or DISCONNECT_NOTICE_TYPE.CONNECT_LOGIN_SERVER_ERROR

			TipWGCtrl.Instance:ShowDisconnected(disconnect_notice_type)
		end
		return
	end

	print("[QuickLogin] OnLoginAck hostname:" .. protocol.gs_hostname .. "  prot:" .. protocol.gs_port)
	GameNet.Instance:SetGameServerInfo(protocol.gs_hostname, protocol.gs_port)
	local user_vo = GameVoManager.Instance:GetUserVo()
	user_vo:SetNowRole(protocol.role_id)
	user_vo.login_time = protocol.time
	user_vo.session_key = protocol.key
	user_vo.anti_wallow = protocol.anti_wallow
	user_vo.scene_id = protocol.scene_id
	user_vo.last_scene_id = protocol.last_scene_id
	GameNet.Instance:DisconnectLoginServer()

	self:ReqConnectGameServer()
end

function QuickLogin:ReqConnectGameServer()
	GameNet.Instance:AsyncConnectGameServer(5)

	if nil == self.send_userenter then
		self.send_userenter = GlobalEventSystem:Bind(LoginEventType.GAME_SERVER_CONNECTED, function(is_succ)
			print("[QuickLogin] connect game server success")
			TipWGCtrl.Instance:CloseDisconnected()
			self:SendUserEnterGSReq()
		end)

		GlobalEventSystem:Bind(LoginEventType.GAME_SERVER_DISCONNECTED, function(custom_disconnect_reason, custom_disconnect_notice_type, real_disconnect_reason, disconnect_detail)
			print("[QuickLogin] game server disconnect")
			if real_disconnect_reason == GameNet.DisconnectReason.Manual then
				return
			end
		
			if TipWGCtrl.Instance then
				TipWGCtrl.Instance:ShowDisconnected(custom_disconnect_notice_type)
			end
		end)
	end
end

function QuickLogin.SendUserEnterGSReq()
	local user_vo = GameVoManager.Instance:GetUserVo()
	local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()

	local protocol = ProtocolPool.Instance:GetProtocol(CSUserEnterGSReq)
	protocol.scene_id = user_vo.scene_id
	protocol.scene_key = 0
	protocol.last_scene_id = user_vo.last_scene_id
	protocol.role_id = mainrole_vo.role_id
	protocol.role_name = mainrole_vo.role_name
	protocol.time = user_vo.login_time
	protocol.is_login = 1
	protocol.server_id = mainrole_vo.server_id
	protocol.key = user_vo.session_key
	protocol.plat_name = user_vo.account_user_id
	protocol.is_micro_pc = 0
	protocol.plat_spid = tostring(GLOBAL_CONFIG.local_package_info.config.agent_id)
	protocol:EncodeAndSend(GameNet.Instance:GetGameServerNet())
	if GlobalLocalRoleId == nil then
		GlobalLocalRoleId = protocol.role_id
	end
	print("[QuickLogin] SendUserEnterGSReq name=" .. mainrole_vo.role_name.."server_id="..mainrole_vo.server_id)
end

function QuickLogin:OnUserEnterGSAck(protocol)
	print("[QuickLogin] OnUserEnterGSAck result=" .. protocol.result)
	if 0 == protocol.result then
		GlobalEventSystem:Fire(LoginEventType.ENTER_GAME_SERVER_SUCC)
		self.enter_gs_count = 0
	elseif -1 == protocol.result then
		self.enter_gs_count = (self.enter_gs_count or 0) + 1
		if self.enter_gs_count >= 5 then
			self.enter_gs_count = 0
		else
			self.enter_gs_timer = GlobalTimerQuest:AddDelayTimer(
				function() self.SendUserEnterGSReq() end, 0.1)
		end
	elseif -3 == protocol.result then
		-- 玩家在跨服，请求重连
		-- ReportManager:Step(Report.STEP_ENTER_GS_ACK_FAILED)
		self.enter_gs_count = (self.enter_gs_count or 0) + 1
		if self.enter_gs_count >= 5 then
			self.enter_gs_count = 0
		else
			self.enter_gs_timer = GlobalTimerQuest:AddDelayTimer(
				function() self.SendUserEnterGSReq() end, 3)
		end
	else
		self.enter_gs_count = 0
		print_log("LoginWGCtrl:OnUserEnterGSAck", protocol.result)
	end
	if 0 == protocol.result and not IS_ON_CROSSSERVER then
		local mainrole_vo = GameVoManager.Instance:GetMainRoleVo()
		local data_list = self.login_data:GetRoleListAck()
		local index = nil
		for k,v in pairs(data_list.role_list) do
			if mainrole_vo.role_id == v.role_id then
				index = k
			end
		end
		index = index or #data_list.role_list + 1
		PlayerPrefsUtil.SetString("SAVE_UP_LOGIN", index)

		--开启冒险的时候保存用户名，之前是在AgentView:OnLoginClick()储存的但村的过早
		local uservo = GameVoManager.Instance:GetUserVo()
		PlayerPrefsUtil.SetString("login_account_name", uservo.account_user_id)
		PlayerPrefsUtil.SetInt("login_account_uid", mainrole_vo.role_id)
	end
end

function QuickLogin:OnDisconnectNotice(protocol)
	GameNet.Instance.custom_disconnect_notice_type = protocol.reason
end

function QuickLogin:ExecuteGm(text)
	local len = string.len(text)
	if len >= 6 and string.sub(text, 1, 6) == "/jy_gm" then
		local blank_begin, blank_end = string.find(text, " ")
		local colon_begin, colon_end = string.find(text, ":")
		if blank_begin and blank_end and colon_begin and colon_end then
			local cmd_type = string.sub(text, blank_end + 1, colon_begin - 1)
			local command = string.sub(text, colon_end + 1, -1)
			SysMsgWGCtrl.SendGmCommand(cmd_type, command)
		end
	elseif len >= 11 and string.sub(text, 1, 11) == "/jy_crossgm" then
		local blank_begin, blank_end = string.find(text, " ")
		local colon_begin, colon_end = string.find(text, ":")
		if blank_begin and blank_end and colon_begin and colon_end then
			local cmd_type = string.sub(text, blank_end + 1, colon_begin - 1)
			local command = string.sub(text, colon_end + 1, -1)
			SysMsgWGCtrl.SendCrossGmCommand(cmd_type, command)
		end
	elseif len >= 7 and string.sub(text, 1, 7) == "/jy_cmd" then
		local blank_begin, blank_end = string.find(text, " ")
		if blank_begin and blank_end then
			ClientCmdWGCtrl.Instance:Cmd(string.sub(text, blank_end + 1, len))
		end
	elseif len > 6 and string.sub(text,1,12) == "wg_shaderadd" then
    	QuickLogin:ShaderOpt(text,"add")
    elseif len > 6 and string.sub(text,1,12) == "wg_shadersub" then
    	QuickLogin:ShaderOpt(text,"sub")
	else
		ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD, text, CHAT_CONTENT_TYPE.TEXT)
	end
end


function QuickLogin:ShaderOpt(text,opt)
	local blank_begin,blank_end = string.find(text," ")
	local colon_begin,colon_end = string.find(text,":")

	if blank_end and blank_begin and colon_begin and colon_end then
		local cmd_type = string.sub(text,blank_end+1,colon_begin - 1)
		local command = string.sub(text,colon_end + 1,-1)

		if opt == "add" then
			QuickLogin:ShaderKeyword(cmd_type,command,true)
		else
            QuickLogin:ShaderKeyword(cmd_type,command,false)
        end
	end
end

function QuickLogin:ShaderKeyword(gameObj,keyword,enable)
	--print("3333 is "..gameObj)
	--print("4444 is "..keyword)
	--local GameObject = UnityEngine.GameObject
    --local go = GameObject('go')
	local obj = UnityEngine.GameObject.Find(gameObj)
	if not obj then
		print("obj is null")
		return
	end

    local renderer = obj:GetComponent(typeof(UnityEngine.Renderer))
    local material = renderer.sharedMaterial

    --material:EnableKeyword("ENABLE_CLIP_RECT")
    if enable then
       material:EnableKeyword(keyword)
    else
    	material:DisableKeyword(keyword)
    end

end


return QuickLogin
