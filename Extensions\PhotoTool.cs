﻿using UnityEngine;
using System;

public static class PhotoTool
{
    // 给照片添加相框
    public static Texture2D PhotoAddFrame(Texture2D source, Texture2D frame)
    {   if (frame == null)
            return source;

        if (frame.width < 200 || frame.height < 200)
            return source;

        int width = source.width + 20;
        int height = source.height + 20;

        Texture2D newTexture = new Texture2D(width, height);

        newTexture.SetPixels32(10, 10, source.width, source.height, source.GetPixels32());

        for (int x = 0; x < 100; ++x)
        {
            for (int y = 0; y < 100; ++y)
            {
                var pixel = frame.GetPixel(x, y);
                var sourcePixel = newTexture.GetPixel(x, y);
                newTexture.SetPixel(x, y, pixel.a * pixel + (1 - pixel.a) * sourcePixel);
            }
        }

        for (int x = 0; x < 100; ++x)
        {
            for (int y = 0; y < 100; ++y)
            {
                var pixel = frame.GetPixel(frame.width - 100 + x, y);
                var sourcePixel = newTexture.GetPixel(width - 100 + x, y);
                newTexture.SetPixel(width - 100 + x, y, pixel.a * pixel + (1 - pixel.a) * sourcePixel);
            }
        }

        for (int x = 0; x < 100; ++x)
        {
            for (int y = 0; y < 100; ++y)
            {
                var pixel = frame.GetPixel(frame.width - 100 + x, frame.height - 100 + y);
                var sourcePixel = newTexture.GetPixel(width - 100 + x, height - 100 + y);
                newTexture.SetPixel(width - 100 + x, height - 100 + y, pixel.a * pixel + (1 - pixel.a) * sourcePixel);
            }
        }

        for (int x = 0; x < 100; ++x)
        {
            for (int y = 0; y < 100; ++y)
            {
                var pixel = frame.GetPixel(x, frame.height - 100 + y);
                var sourcePixel = newTexture.GetPixel(x, height - 100 + y);
                newTexture.SetPixel(x, height - 100 + y, pixel.a * pixel + (1 - pixel.a) * sourcePixel);
            }
        }

        for (int x = 0; x < 100; ++x)
        {
            for (int y = 100; y < height - 100; ++y)
            {
                var pixel = frame.GetPixel(x, 101);
                var sourcePixel = newTexture.GetPixel(x, y);
                newTexture.SetPixel(x, y, pixel.a * pixel + (1 - pixel.a) * sourcePixel);
            }
        }

        for (int x = width - 100; x < width; ++x)
        {
            for (int y = 100; y < height - 100; ++y)
            {
                var pixel = frame.GetPixel(frame.width + x - width, 101);
                var sourcePixel = newTexture.GetPixel(x, y);
                newTexture.SetPixel(x, y, pixel.a * pixel + (1 - pixel.a) * sourcePixel);
            }
        }

        for (int x = 100; x < width - 100; ++x)
        {
            for (int y = 0; y < 100; ++y)
            {
                var pixel = frame.GetPixel(101, y);
                var sourcePixel = newTexture.GetPixel(x, y);
                newTexture.SetPixel(x, y, pixel.a * pixel + (1 - pixel.a) * sourcePixel);
            }
        }

        for (int x = 100; x < width - 100; ++x)
        {
            for (int y = height - 100; y < height; ++y)
            {
                var pixel = frame.GetPixel(101, frame.height + y - height);
                var sourcePixel = newTexture.GetPixel(x, y);
                newTexture.SetPixel(x, y, pixel.a * pixel + (1 - pixel.a) * sourcePixel);
            }
        }

        return newTexture;
    }

    public static Texture2D PhotoAddText(Texture2D source, string text)
    {

        return source;
    }
}
