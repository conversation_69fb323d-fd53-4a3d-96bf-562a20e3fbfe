﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;


[CreateAssetMenu(menuName = "MySubMenue/Create YYFullScreenFogProfile ")]
[System.Serializable]
public class YYFullScreenFogProfile : YYFogProfile
{

    // 高度雾 
    [Header("Full screen fog - thresholds")]
    [Tooltip("Fog height. Set to 0 for unlimited height.")]
    [Range(0.0f, 5000.0f)]
    public float FogHeight = 0.0f;


    /// <summary>The number of shaft samples. Set to 0 to disable sun shafts.</summary>
    [Header("Full screen fog - screen space shafts (all dir lights).")]
    [Tooltip("The number of shaft samples. Set to 0 to disable sun shafts.")]
    [Range(0, 100)]
    public int SunShaftSampleCount = 0;

    /// <summary>Sun shaft spread (0 - 1).</summary>
    [Tooltip("Sun shaft spread (0 - 1).")]
    [Range(0.0f, 1.0f)]
    public float SunShaftSpread = 0.65f;

    /// <summary>Increases the sun shaft brightness</summary>
    [Tooltip("Increases the sun shaft brightness")]
    [Range(0.0f, 1.0f)]
    public float SunShaftBrightness = 0.075f;

    /// <summary>Combined with each ray march, this determines how much light is accumulated each step.</summary>
    [Tooltip("Combined with each ray march, this determines how much light is accumulated each step.")]
    [Range(0.0f, 100.0f)]
    public float SunShaftStepMultiplier = 21.0f;

    /// <summary>Determines light fall-off from start of sun shaft. Set to 1 for no fall-off.</summary>
    [Tooltip("Determines light fall-off from start of sun shaft. Set to 1 for no fall-off.")]
    [Range(0.5f, 1.0f)]
    public float SunShaftDecay = 0.97f;

    /// <summary>Controls dithering intensity of sun shafts.</summary>
    [Tooltip("Controls dithering intensity of sun shafts.")]
    [Range(-1.0f, 1.0f)]
    public float SunShaftDither = 0.4f;

    /// <summary>Sun shaft tint color. Alpha value determines tint intensity.</summary>
    [Tooltip("Sun shaft tint color. Alpha value determines tint intensity.")]
    public Color SunShaftTintColor = Color.white;





    public override void UpdateMaterialProperties(Material material, Camera camera, bool global)
    {
        if (YYLightManager.Instance == null)
        {
            return;
        }

        base.UpdateMaterialProperties(material, camera, global);



        bool gamma = (QualitySettings.activeColorSpace == ColorSpace.Gamma);

        float scatterCover = 0.0f;// (YYFullScreenClouds.Instance != null && YYFullScreenClouds.Instance.enabled && YYFullScreenClouds.Instance.CloudProfile != null ? YYFullScreenClouds.Instance.CloudProfile.CloudCoverTotal : 0.0f);

        float brightness = SunShaftBrightness * (gamma ? 1.0f : 0.33f) * Mathf.Max(0.0f, 1.0f - (scatterCover * 1.5f));

       
        material.SetVector(WMS._WeatherMakerFogSunShaftsParam1,
            new Vector4(SunShaftSpread / (float)SunShaftSampleCount, (float)SunShaftSampleCount, brightness, 1.0f / (float)SunShaftSampleCount));

        material.SetVector(WMS._WeatherMakerFogSunShaftsParam2,
            new Vector4(SunShaftStepMultiplier, SunShaftDecay, SunShaftDither, 0.0f));

        material.SetVector(WMS._WeatherMakerFogSunShaftsTintColor, new Vector4(SunShaftTintColor.r * SunShaftTintColor.a, SunShaftTintColor.g * SunShaftTintColor.a,
                  SunShaftTintColor.b * SunShaftTintColor.a, SunShaftTintColor.a));

    }

}
