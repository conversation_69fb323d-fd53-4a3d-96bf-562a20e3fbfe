-- 龙王宝藏【铜币本】
-- 原 [A2] 荒古神冢
-- [A3] 熔火之心

FuBenPanelView = FuBenPanelView or BaseClass(SafeBaseView)

function FuBenPanelView:InittongbiView()
	-- self.is_des_expand = false

	XUI.AddClickEventListener(self.node_list["btn_tongbi_enter"],BindTool.Bind(self.GoToCopperFb, self)) 		--进入副本
	--XUI.AddClickEventListener(self.node_list["btn_tongbi_rule"],BindTool.Bind(self.OnClickCopperTips, self))  	--玩法按钮
	XUI.AddClickEventListener(self.node_list["btn_tongbi_add_count"],BindTool.Bind(self.OnClinkCopperFbBuyHandler, self))  	--购买次数
	XUI.AddClickEventListener(self.node_list["layout_tongbi_combine_mark"],BindTool.Bind(self.OnClickCopperCombine, self))  	--合并
	XUI.AddClickEventListener(self.node_list["btn_tongbi_saodang"], BindTool.Bind(self.SaoDangTongBiFB, self))  --扫荡
	--XUI.AddClickEventListener(self.node_list["tb_close"], BindTool.Bind(self.OnClickTBClose, self)) 
	-- XUI.AddClickEventListener(self.node_list["btn_tb_show_desc"], BindTool.Bind(self.OnClickTongBiShowDescBtn, self))  --描述文本展开
	self.copper_reward_list = AsyncListView.New(CopperRewardCell, self.node_list["tongbi_reward_list"])

	local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
	if other_cfg.fb_des then
		self.node_list["desc_tongbi_fb"].text.text = other_cfg.fb_des
	end

	self.node_list.desc_tongbi_ads.text.text = Language.FuBenPanel.TongBiFuBenAds

	for i = 1, 9 do
		if Language.FuBenPanel.TongBiFuBenShuoming[i] then
			self.node_list["jbb_fb_info_sign"..i]:SetActive(true)
			self.node_list["jbb_fb_info"..i].text.text = Language.FuBenPanel.TongBiFuBenShuoming[i]
		else
			self.node_list["jbb_fb_info_sign"..i]:SetActive(false)
		end
	end


	-- self:ShowBriefIntraduction()
	--self.node_list.tb_name.text.text = Language.FuBenPanel.RuleTitle[TabIndex.fubenpanel_copper]
end

-- function  FuBenPanelView:ShowBriefIntraduction()
-- 	-- for i = 1, 9 do
-- 	-- 	if Language.FuBenPanel.TongBiFuBenShuoming[i] then
-- 	-- 		self.node_list["jbb_fb_info"..i]:SetActive(true)
-- 	-- 		self.node_list["jbb_fb_info"..i].text.text = Language.FuBenPanel.TongBiFuBenShuoming[i]
-- 	-- 	else
-- 	-- 		self.node_list["jbb_fb_info"..i]:SetActive(false)
-- 	-- 	end
-- 	-- end

-- 	local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
-- 	if other_cfg.fb_des then
-- 		self.node_list["jbb_desc"].text.text = other_cfg.fb_des
-- 	end

--     -- self.node_list.jbb_desc_bg.rect.sizeDelta = u3dpool.vec2(570, 28)
-- end

function FuBenPanelView:DeleteTongBiPanelView()
	if self.copper_reward_list then
		self.copper_reward_list:DeleteMe()
		self.copper_reward_list = nil
	end
end

-- function FuBenPanelView:OnClickTongBiShowDescBtn()
-- 	local is_expand = not self.is_des_expand
-- 	local height = self.node_list.jbb_desc.rect.sizeDelta.y + 8
-- 	self.node_list.jbb_desc_bg.rect.sizeDelta = is_expand and u3dpool.vec2(570, height) or u3dpool.vec2(570, 28)
-- 	self.node_list.img_btn_arrow.rect.rotation = is_expand and Quaternion.Euler(0, 0, 180) or Quaternion.identity
-- 	self.is_des_expand = is_expand
-- end

function FuBenPanelView:DoTongBiTweenStart()
	if self.node_list.tongbi_tween_root then
		UITween.CanvasGroup(self.node_list.tongbi_tween_root.gameObject).alpha = 0
	end
end

function FuBenPanelView:DoTongBiTween()
	--local tween_info = UITween_CONSTS.FuBen
	-- if self.node_list.tongbi_fb_text_img then
	-- 	UITween.ImgFillDoValue(self.node_list.tongbi_fb_text_img, 0, 1, tween_info.TipsTweenTime)
	-- end

	-- if self.node_list.tongbi_tween_root then
	-- 	UITween.CanvasGroup(self.node_list.tongbi_tween_root.gameObject).alpha = 0
	-- 	UITween.AlphaShow(GuideModuleName.FuBenPanel,self.node_list.tongbi_tween_root.gameObject, 0, 1, tween_info.TipsTweenTime * 0.5)
	-- end

	-- if self.node_list.tongbi_shuoming then
	-- 	local start_pos = Vector3(250, self.node_list.tongbi_shuoming.transform.anchoredPosition.y, 0)
	-- 	UITween.MoveShowPanel(self.node_list.tongbi_shuoming, start_pos, tween_info.TipsTweenTime)
	-- end
end

function FuBenPanelView:OnFlushTBView(index)
	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	if not copper_info then return end
	--for i = 1, 3 do
	--	self.node_list["tongbi_star"..i]:SetActive(i <= copper_info.day_star_num)
	--end
	local data_list = CopperFbWGData.Instance:GetRewardItemList(3)
	self.copper_reward_list:SetDataList(data_list)
	self:FlushTBHaveCount()
	self:FlushCopperEffect()
	local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local pre_show_level = combine_cfg[FB_COMBINE_TYPE.COPPER_FB +1].pre_show_level
	local role_level = RoleWGData.Instance:GetRoleLevel()
	self.node_list.layout_tongbi_combine_mark_root:SetActive(role_level >= pre_show_level)
	-- for i=1,5 do
	-- 	self.node_list["shuoming"..i].text.text = Language.FuBenPanel.TongBiFuBenShuoming[i]
	-- end

	local hook_type = FuBenWGData.Instance:GetCombineStatus(FB_COMBINE_TYPE.COPPER_FB)
 	self.node_list.layout_tongbi_combine_hook:SetActive(hook_type == 1)

end

function FuBenPanelView:FlushTBHaveCount()
	if nil == self.node_list["lbl_tongbi_count"] then return end
	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
	local remain_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times

	self.node_list["lbl_tongbi_count"].text.text = string.format(Language.FuBenPanel.FuBenEnterTime, remain_times, other_cfg.fb_day_free_times + copper_info.day_buy_times)
	--if remain_times > 0 then
	--	self.node_list["lbl_tongbi_count"].text.text = remain_times .. "/" .. other_cfg.fb_day_free_times + copper_info.day_buy_times
	--else
	--	self.node_list["lbl_tongbi_count"].text.text = string.format(Language.FuBenPanel.FuBenEnterCount, remain_times, other_cfg.fb_day_free_times + copper_info.day_buy_times)
	--end
end

function FuBenPanelView:FlushCopperEffect()
	local role_vip = VipWGData.Instance:GetRoleVipLevel()
	local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyCountCfg()
	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	local have_count = vip_buy_cfg["param_" .. role_vip] - copper_info.day_buy_times
	local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
	local enter_tb_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
	--self.node_list.tongbi_effect:SetActive(have_count > 0 and enter_tb_times <= 0 and FuBenPanelWGData.Instance:CheckVipCondition())
end

function FuBenPanelView:OnClickCopperTips()
    local role_tip = RuleTip.Instance
    role_tip:SetTitle(Language.FuBenPanel.RuleTitle[12])
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local need_level = combine_cfg[FB_COMBINE_TYPE.COPPER_FB + 1].level_limit
    role_tip:SetContent(string.format(Language.FuBen.Copper_Des, need_level))
end

function FuBenPanelView:OnClinkCopperFbBuyHandler()
	--FuBenPanelWGCtrl.Instance:OpenCopperBuy()
	FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_TONGBIBEN)
end

function FuBenPanelView:OnClickCopperCombine()
    local combine_cfg = FuBenWGData.Instance:GetCombineCfg()
    local role_level = GameVoManager.Instance:GetMainRoleVo().level
    local need_level = combine_cfg[FB_COMBINE_TYPE.COPPER_FB + 1].level_limit
    if need_level > role_level then
        local level_des = RoleWGData.GetLevelString(need_level)
        local str = string.format(Language.FuBenPanel.CombineLimitTips,level_des)
        SysMsgWGCtrl.Instance:ErrorRemind(str)
        return
    end

	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	if not copper_info then
		return
	end
	local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
	local total_times = other_cfg.fb_day_free_times + copper_info.day_buy_times
    local remain_times = total_times - copper_info.day_has_enter_times
    if remain_times < 2 then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.CountTooLess)
        return
    end
    local vas = self.node_list.layout_tongbi_combine_hook:GetActive()

    local is_combine = vas == true and 0 or 1
    if vas then
        FuBenWGCtrl.Instance:SendFBUseCombine(is_combine,FB_COMBINE_TYPE.COPPER_FB)
         --self.node_list.layout_tongbi_combine_hook:SetActive(not vas)
    else
        local callback_func = function()
           --self.node_list.layout_tongbi_combine_hook:SetActive(true)
        end
        FuBenWGCtrl.Instance:ShowCombinePanel(FB_COMBINE_TYPE.COPPER_FB,callback_func)
    end
end

function FuBenPanelView:CheckCopperCount() -- 判断铜币本有无进入次数
	local copper_info = FuBenPanelWGData.Instance:GetTongBiInfo()
	local other_cfg = FuBenPanelWGData.Instance:GetTongBiBenOtherCfg()
	local enter_tb_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times

	local copper_level_cfg = FuBenPanelWGData.Instance:GetCopperTitleCfg()
	if enter_tb_times > 0 and copper_level_cfg[1].level_limit <= RoleWGData.Instance.role_vo.level then
		return true
	else
		return false
	end
end

function FuBenPanelView:GoToCopperFb()
	if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end

	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
	local remain_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
	if remain_times > 0 then
		--if self.is_can_enter_copper_fb then  return end
		self.is_can_enter_copper_fb = true
		FuBenWGData.Instance:SetTXGEnter(true)
		FuBenWGCtrl.Instance:SendTongBiFb(0)
		self:Close()
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
		-- local role_vip = VipWGData.Instance:GetRoleVipLevel()
		-- local vip_buy_cfg = FuBenPanelWGData.Instance:GetVipBuyCountCfg()
		-- local copper_info = FuBenPanelWGData.Instance:GetTongBiInfo()
		-- local time = vip_buy_cfg["param_" .. role_vip] - copper_info.day_buy_times
		-- if time > 0 then
			-- FuBenPanelWGCtrl.Instance:OpenCopperBuy()
		--end
		FuBenPanelWGCtrl.Instance:OpenBuyView(FUBEN_TYPE.FBCT_TONGBIBEN)
	end
end

function FuBenPanelView:SaoDangTongBiFB()
	if not FuBenWGCtrl.Instance:GetCanEnterFuBen() then
		return
	end

	local copper_info = CopperFbWGData.Instance:GetTongBiInfo()
	local other_cfg = CopperFbWGData.Instance:GetOtherCfg()
	local remain_times = other_cfg.fb_day_free_times + copper_info.day_buy_times - copper_info.day_has_enter_times
	if remain_times > 0 then
		FuBenWGCtrl.Instance:ShowSaoDangPanel(FUBEN_TYPE.FBCT_TONGBIBEN)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.HaveNotEnterTimes)
		FuBenPanelWGCtrl.Instance:OpenCopperBuy()
	end
end

-- function FuBenPanelView:OnClickTBClose()
-- 	self.fb_index = -1
-- 	self.node_list.tongbi_left_msg.rect:DOAnchorPos(Vector2(500, 0), 0.3)
-- 	ReDelayCall(self, function()
-- 		self:OnTabChangeHandler(0)
-- 	end, 0.3, "copper_tween")
-- end

---------------------CopperRewardCell--------------------------------
CopperRewardCell = CopperRewardCell or BaseClass(BaseRender)
function CopperRewardCell:__init()
	self.base_cell = ItemCell.New(self.node_list["pos"])
    self.base_cell:SetIsShowTips(true)
end
function CopperRewardCell:__delete()
	if self.base_cell then
		self.base_cell:DeleteMe()
		self.base_cell = nil
	end
end
function CopperRewardCell:OnFlush()
	self.base_cell:SetFlushCallBack(function()
		self.base_cell:SetRightBottomTextVisible(false)
		if self.data.is_three_must_drop and self.data.is_three_must_drop == true then
			self.base_cell:SetLeftTopText(self.data.num)
			self.base_cell:SetLeftTopTextVisible(true)
		end
	end)
	self.base_cell:SetData(self.data)
    self.node_list.three_flag:SetActive(self.data.is_three_must_drop and self.data.is_three_must_drop == true)
	--if self.data.is_three_must_drop then
	--	self.node_list.three_flag_text.text.text = Language.FuBenPanel.ThreeEWai
	--end
    --self.node_list.new_flag:SetActive(false)
end
