require("game/yanyuge/yanyuge_entrance_view")
require("game/yanyuge/yanyuge_exchange_shop_view")
require("game/yanyuge/yanyuge_privilege_view")
require("game/yanyuge/yanyuge_nwsd_view")
require("game/yanyuge/yanyuge_nztq_view")
require("game/yanyuge/yanyuge_tzsd_view")
require("game/yanyuge/yanyuge_wltz_view")
require("game/yanyuge/yanyuge_yytq_view")
require("game/yanyuge/yanyuge_wg_data")
require("game/yanyuge/yanyuge_wltz_tip")
require("game/yanyuge/yanyuge_wltz_face_slap_view")
require("game/yanyuge/yanyuge_zztq_view")
require("game/yanyuge/yanyuge_noble_view")
require("game/yanyuge/yanyuge_wltz_task_view")
require("game/yanyuge/yanyuge_privilege_level_up_tip")

YanYuGeWGCtrl = YanYuGeWGCtrl or BaseClass(BaseWGCtrl)
function YanYuGeWGCtrl:__init()
	if YanYuGeWGCtrl.Instance then
		ErrorLog("[YanYuGeWGCtrl] Attemp to create a singleton twice !")
	end
	YanYuGeWGCtrl.Instance = self

    self.entrance_view = YanYuGeEntranceView.New(GuideModuleName.YanYuGeEntranceView)
    self.privilege_view = YanYuGePrivilegeView.New(GuideModuleName.YanYuGePrivilegeView)
    self.exchange_shop_view = YanYuGeExchangeShopView.New(GuideModuleName.YanYuGeExchangeShopView)
    self.yanyuge_wltz_tip = YanYuGeWLTZTip.New()
    self.noble_privilege_view = YanYuGeNobleView.New(GuideModuleName.YanYuGeNobleView)
    self.data = YanYuGeWGData.New()
    self.wltz_face_slap_view = YanYuGeWLTZFaceSlapView.New()
    self.level_up_tip = YanYuGePrivilegeLevelUpTip.New()
    self.wltz_task_view = YanYuGeWLTZTaskView.New(GuideModuleName.YanYuGeWLTZTaskView)

    self.open_fun_change = GlobalEventSystem:Bind(OpenFunEventType.OPEN_TRIGGER, BindTool.Bind(self.OpenFunEventChange, self))
    self.mainui_create_complete = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self))

    self.role_data_change = BindTool.Bind(self.OnRoleAttrChange, self)
	RoleWGData.Instance:NotifyAttrChange(self.role_data_change, {"level"})

    if not self.view_open_event then
        self.view_open_event = GlobalEventSystem:Bind(OtherEventType.VIEW_OPEN, BindTool.Bind(self.ViewOpenHandler, self))
    end

    self:RegisterProtocol(CSCangJinShangPuClientReq)
	self:RegisterProtocol(SCCangJingShangPuInvestInfo, "OnSCCangJingShangPuInvestInfo")
	self:RegisterProtocol(SCCangJinShangPuAllInfo, "OnSCCangJinShangPuAllInfo")
	self:RegisterProtocol(SCCangJinShangPuSuitShopInfo, "OnSCCangJinShangPuSuitShopInfo")
	self:RegisterProtocol(SCCangJinShangPuLimitShopInfo, "OnSCCangJinShangPuLimitShopInfo")
    self:RegisterProtocol(SCCangJinShangPuTeQuanInfo, "OnSCCangJinShangPuTeQuanInfo")
    self:RegisterProtocol(SCCangJinShangPuScoreInfo, "OnSCCangJinShangPuScoreInfo")
    self:RegisterProtocol(SSCANGJINSHANGPUConvertInfo, "OnSSCANGJINSHANGPUConvertInfo")
    self:RegisterProtocol(SCCangJingShangPuNobilityInfo, "OnSCCangJingShangPuNobilityInfo")
    self:RegisterProtocol(SCCangJingShangPuNobilityTaskUpdate, "OnSCCangJingShangPuNobilityTaskUpdate")
end

function YanYuGeWGCtrl:__delete()
	YanYuGeWGCtrl.Instance = nil

    if self.entrance_view then 
        self.entrance_view:DeleteMe()
        self.entrance_view = nil
    end

    if self.privilege_view then 
        self.privilege_view:DeleteMe()
        self.privilege_view = nil
    end

    if self.exchange_shop_view then 
        self.exchange_shop_view:DeleteMe()
        self.exchange_shop_view = nil
    end

    if self.data then 
        self.data:DeleteMe()
        self.data = nil
    end

    if self.yanyuge_wltz_tip then
        self.yanyuge_wltz_tip:DeleteMe()
        self.yanyuge_wltz_tip = nil
    end

    if self.noble_privilege_view then
        self.noble_privilege_view:DeleteMe()
        self.noble_privilege_view = nil
    end

    if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

    if self.open_fun_change then
        GlobalEventSystem:UnBind(self.open_fun_change)
        self.open_fun_change = nil
    end

    if self.level_up_tip then
        self.level_up_tip:DeleteMe()
        self.level_up_tip = nil
    end

    if self.wltz_face_slap_view then
        self.wltz_face_slap_view:DeleteMe()
        self.wltz_face_slap_view = nil
    end

    if self.wltz_task_view then
        self.wltz_task_view:DeleteMe()
        self.wltz_task_view = nil
    end

    if self.role_data_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.role_data_change)
		self.role_data_change = nil
	end

    if self.view_open_event then
		GlobalEventSystem:UnBind(self.view_open_event)
		self.view_open_event = nil
	end
end

-------------------------------------------PROTOCOL_START---------------------------------------------
function YanYuGeWGCtrl:SendOperateRequest(opera_type, param_1, param_2, param_3)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCangJinShangPuClientReq)
	protocol.opera_type = opera_type
	protocol.param_1 = param_1 or 0
	protocol.param_2 = param_2 or 0
	protocol.param_3 = param_3 or 0
	protocol:EncodeAndSend()
end

function YanYuGeWGCtrl:OnSCCangJingShangPuInvestInfo(protocol)
    -- print_error("-------------OnSCCangJingShangPuInvestInfo--------------", protocol)
    self.data:SetCangJingShangPuInvestInfo(protocol)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_WLTZ)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_YYTQ)
    -- RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_ZZTQ)
    RemindManager.Instance:Fire(RemindName.PrivilegeCollection_ZZTQ)

    RemindManager.Instance:Fire(RemindName.RechargeVolumeCanUse)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeLimitView)

    ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_wltz)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_yytq)
    -- ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_zztq)

    ViewManager.Instance:FlushView(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_zztq)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView)
end

function YanYuGeWGCtrl:OnSCCangJinShangPuAllInfo(protocol)
    -- print_error("=======全部信息======", protocol)
    self.data:SetCangJinShopAllInfo(protocol)

    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_WLTZ)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_YYTQ)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_NZTQ)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Shop_NWSD)
    -- RemindManager.Instance:Fire(RemindName.YanYuGe_Shop_TZSD)
    RemindManager.Instance:Fire(RemindName.PrivilegeCollection_ZJTQ)

    -- RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_ZZTQ)
    RemindManager.Instance:Fire(RemindName.PrivilegeCollection_ZZTQ)
    RemindManager.Instance:Fire(RemindName.RechargeVolumeCanUse)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeLimitView)

    ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_wltz)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_yytq)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_nztq)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGeExchangeShopView, TabIndex.yanyuge_shop_nwsd)
    -- ViewManager.Instance:FlushView(GuideModuleName.YanYuGeExchangeShopView, TabIndex.yanyuge_shop_tzsd)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGeEntranceView)
    -- ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_zztq)
    ViewManager.Instance:FlushView(GuideModuleName.PrivilegeCollectionView)
    ViewManager.Instance:FlushView( GuideModuleName.YanYuGeNobleView)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView)
end

function YanYuGeWGCtrl:OnSCCangJinShangPuSuitShopInfo(protocol)
    -- print_error("=======套装商店======", protocol)
    self.data:SuitShopChange(protocol)
    -- RemindManager.Instance:Fire(RemindName.YanYuGe_Shop_TZSD)
    RemindManager.Instance:Fire(RemindName.PrivilegeCollection_ZJTQ)
    -- ViewManager.Instance:FlushView(GuideModuleName.YanYuGeExchangeShopView, TabIndex.yanyuge_shop_tzsd)
    ViewManager.Instance:FlushView(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_zjtq)
end

function YanYuGeWGCtrl:OnSCCangJinShangPuLimitShopInfo(protocol)
    -- print_error("=======限购商店======", protocol)
    self.data:LimitShopInfoChange(protocol)

    RemindManager.Instance:Fire(RemindName.YanYuGe_Shop_NWSD)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGeExchangeShopView, TabIndex.yanyuge_shop_nwsd)
end

function YanYuGeWGCtrl:OnSCCangJinShangPuTeQuanInfo(protocol)
    -- print_error("=======特权======", protocol)
    self.data:TeQuanInfoChange(protocol)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_YYTQ)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_NZTQ)
    -- RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_ZZTQ)
    RemindManager.Instance:Fire(RemindName.PrivilegeCollection_ZZTQ)
    RemindManager.Instance:Fire(RemindName.RechargeVolumeCanUse)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeLimitView)

    ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_yytq)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_nztq)
    -- ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView, TabIndex.yanyuge_privilege_zztq)
    ViewManager.Instance:FlushView(GuideModuleName.PrivilegeCollectionView, TabIndex.pri_col_zztq)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView)
end

function YanYuGeWGCtrl:OnSCCangJinShangPuScoreInfo(protocol)
    -- print_error("=======积分======", protocol)
    local old_score = self.data:GetCurScore()
    local diff = protocol.score - old_score

    if diff > 0 then
        if protocol.reason_type ~= COST_REASON_TYPE.GOLD_ADD_FORTUNE_CAT then
            SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.YanYuGe.GetScore, diff))
        end
    end

    self.data:ScoreInfoChange(protocol)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_YYTQ)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_NZTQ)
    -- RemindManager.Instance:Fire(RemindName.YanYuGe_Privilege_ZZTQ)

    RemindManager.Instance:Fire(RemindName.PrivilegeCollection_ZZTQ)
    RemindManager.Instance:Fire(RemindName.RechargeVolumeCanUse)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeLimitView)
    
	GlobalEventSystem:Fire(OtherEventType.CANG_JIN_SCORE_CHANGE)

    ViewManager.Instance:FlushView(GuideModuleName.YanYuGeEntranceView)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGePrivilegeView)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGeExchangeShopView)
    ViewManager.Instance:FlushView(GuideModuleName.PrivilegeCollectionView)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGeNobleView)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView)
    ViewManager.Instance:FlushView(GuideModuleName.FortuneCatView)
end

function YanYuGeWGCtrl:OnSSCANGJINSHANGPUConvertInfo(protocol)
    -- print_error("=======兑换信息======", protocol)
    self.data:SetConvertInfo(protocol)
end

function YanYuGeWGCtrl:OnSCCangJingShangPuNobilityInfo(protocol)
    -- print_error("=======OnSCCangJingShangPuNobilityInfo======", protocol)
    local old_privilege_level = self.data:GetNobilityLevel()
    self.data:SetCangJingShangPuNobilityInfo(protocol)

    if protocol.nobility_level > old_privilege_level and old_privilege_level>= 0 then
        if self.level_up_tip then
            if self.level_up_tip:IsOpen() then
                self.level_up_tip:Flush()
            else
                self.level_up_tip:Open()
            end
        end
    end

    RemindManager.Instance:Fire(RemindName.YanYuGe_Noble_Privilege)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGeNobleView)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView)
end

function YanYuGeWGCtrl:OnSCCangJingShangPuNobilityTaskUpdate(protocol)
    -- print_error("=======OnSCCangJingShangPuNobilityTaskUpdate======", protocol)
    self.data:CangJingShangPuNobilityTaskUpdate(protocol)
    RemindManager.Instance:Fire(RemindName.YanYuGe_Noble_Privilege)
    ViewManager.Instance:FlushView(GuideModuleName.YanYuGeNobleView)
    ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView)
end

function YanYuGeWGCtrl:OpenFunEventChange(check_all, fun_name, is_open)
    if check_all or fun_name == FunName.YanYuGeEntranceView then 
        local is_open = FunOpen.Instance:GetFunIsOpened(FunName.YanYuGeEntranceView)
        local state = is_open and ACTIVITY_STATUS.OPEN or ACTIVITY_STATUS.CLOSE
        ActivityWGData.Instance:SetActivityStatus(ACTIVITY_TYPE.YanYuGe, state)
    end
end

function YanYuGeWGCtrl:OnRoleAttrChange(attr_name, value, old_value)
    if attr_name == "level" then
        ViewManager.Instance:FlushView(GuideModuleName.YanYuGe_Shop_NWSD)
        ViewManager.Instance:FlushView(GuideModuleName.YanYuGeExchangeShopView, TabIndex.yanyuge_shop_nwsd)
        local close_level = YanYuGeWGData.Instance:GetOtherCfgAttrValue("open_server_task_close_level")
        if close_level <= value and close_level > old_value then
            RemindManager.Instance:Fire(RemindName.YanYuGe_Noble_Privilege)
            ViewManager.Instance:FlushView(GuideModuleName.YanYuGeNobleView)
            ViewManager.Instance:FlushView(GuideModuleName.RechargeVolumeView)
        end
    end
end

function YanYuGeWGCtrl:ViewOpenHandler(view)
	local is_open = FunOpen.Instance:GetFunIsOpened(FunName.YanYuGeNobleView)

	if is_open then
		local is_has_open_task, open_task_list = YanYuGeWGData.Instance:GetYYGOpenTaskData()
		if is_has_open_task then
			for k, v in pairs(open_task_list) do
				if view and view.view_name and view.view_name == tostring(v.panel) then
					self:SendOperateRequest(YANYUGE_OPERA_TYPE.NOBILITY_TASK, v.ID)
					break
				end
			end
		end
	end
end
-------------------------------------------PROTOCOL_END---------------------------------------------

function YanYuGeWGCtrl:OpenYanYuGeWLTZTip(max_buy_count)
    if self.yanyuge_wltz_tip then
        self.yanyuge_wltz_tip:SetDataAndOpen(max_buy_count)
    end
end

function YanYuGeWGCtrl:MainuiOpenCreateCallBack()
    -- 延迟10s 等待活动 和 自定义活动信息全同步
	TryDelayCall(self, function () 
        if FunOpen.Instance:GetFunIsOpened(FunName.YanYuGePrivilegeWLTZ) then
            if self.data:IsCanShowWLTZFaceSlapView() then
                if self.wltz_face_slap_view and not self.wltz_face_slap_view:IsOpen() then
                    self.wltz_face_slap_view:Open()
                end
            end
        end
	end, 10, "activity_notice_on_login")
end