HolyHeavenlyDomainView = HolyHeavenlyDomainView or BaseClass(SafeBaseView)

local BG_CFG = {
	[TabIndex.holy_heavenly_domain_details] = "a2_stsy_bg2",
	[TabIndex.holy_heavenly_domain_rank] = "a2_stsy_bg1",
}

function HolyHeavenlyDomainView:__init()
	self.view_layer = UiLayer.Normal
	self.view_style = ViewStyle.Full

	self.is_safe_area_adapter = true
	self.default_index = TabIndex.holy_heavenly_domain_details

	local bundle = "uis/view/holy_heavenly_domain_ui_prefab"
	self:AddViewResource(0, bundle, "layout_holy_heavenly_domain_bg")
	self:AddViewResource(TabIndex.holy_heavenly_domain_details, bundle, "layout_holy_heavenly_domain_details") --圣天神域详情
	self:AddViewResource(TabIndex.holy_heavenly_domain_rank, bundle, "layout_holy_heavenly_domain_hof")     --圣天神域名人堂
	self:AddViewResource(0, bundle, "layout_holy_heavenly_domain_top_panel")
	self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar")

	self.remind_tab = {
		{ RemindName.HolyHeavenlyDomainDetails },
		{ RemindName.HolyHeavenlyDomainRank },
	}
end

function HolyHeavenlyDomainView:OpenCallBack()
	HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.PLAYER_RANK,
		CROSS_DIVINE_DOMAIN_PERSON_RANK_TYPE.SCORE_RANK)
	HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainOperate(CROSS_DIVINE_DOMAIN_OPERATE_TYPE.SERVER_RANK)

	-- 逼策划，进游戏要求有没有领取的城池奖励时候弹战况 数据是主动请求的 需要补个请求
	local city_table = {}
	local city_data = HolyHeavenlyDomainWGData.Instance:GetMapCitiDataList()
	for k, v in pairs(city_data) do
		if v.type ~= 4 then
			city_table[v.seq] = 1
		end
	end

	HolyHeavenlyDomainWGCtrl.Instance:OnDivineDomainGetCitiInfoOperate(city_table)
	HolyHeavenlyDomainWGCtrl.Instance:SetAutoOpenWarSituationState(true)
end

function HolyHeavenlyDomainView:LoadCallBack()
	self.last_show_index_cache = -1
	self.node_list.title_view_name.text.text = Language.HolyHeavenlyDomain.TitleViewName

	if not self.tabbar then
		self.tabbar = Tabbar.New(self.node_list)
		self.tabbar:SetVerTabbarIconStr("a2_stsy_tab")
		self.tabbar:SetVerTabbarIconPath(ResPath.GetHolyHeavenlyDomainImg)
		self.tabbar:Init(Language.HolyHeavenlyDomain.TabGrop, nil, nil, nil, self.remind_tab)
		self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
		FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.HolyHeavenlyDomainView, self.tabbar)
	end
end

function HolyHeavenlyDomainView:LoadIndexCallBack(index)
	if index == TabIndex.holy_heavenly_domain_details then
		self:LoadIndexCallBackDetails()
	elseif index == TabIndex.holy_heavenly_domain_rank then
		self:LoadIndexCallBackRank()
	end
end

function HolyHeavenlyDomainView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self:ReleaseCallBackDetails()
	self:ReleaseCallBackRank()
end

function HolyHeavenlyDomainView:ShowIndexCallBack(index)
	if self.last_show_index_cache ~= index then
		self:ChangeIndex(self.last_show_index_cache)
	end

	self:ChangeBgImage(index)

	if index == TabIndex.holy_heavenly_domain_details then
		self:ShowIndexCallBackDetails()
	elseif index == TabIndex.holy_heavenly_domain_rank then
		self:ShowIndexCallBackRank()
	end

	self.last_show_index_cache = index
end

function HolyHeavenlyDomainView:ChangeIndex(old_index)
	if old_index == TabIndex.holy_heavenly_domain_details then

	elseif old_index == TabIndex.holy_heavenly_domain_rank then
		self:ChangeIndexCallBackRank()
	end
end

function HolyHeavenlyDomainView:OnFlush(param_t, index)
	for k, v in pairs(param_t) do
		if k == "all" then
			if index == TabIndex.holy_heavenly_domain_details then
				self:OnFlushDetails(param_t)
			elseif index == TabIndex.holy_heavenly_domain_rank then
				self:OnFlushRank(param_t)
			end
		end
	end
end

function HolyHeavenlyDomainView:ChangeBgImage(index)
	if self.node_list.raw_bg then
		local bg_name = BG_CFG[index]
		local bundle, asset = ResPath.GetRawImagesPNG(bg_name)
		self.node_list.raw_bg.raw_image:LoadSprite(bundle, asset)
	end
end
