require("game/role_model_test/role_model_test_view")
require("game/role_model_test/gundam_model_test_view")
require("game/role_model_test/screen_shot_model_data")
require("game/role_model_test/screen_shot_model_view")

RoleModelTestWGCtrl = RoleModelTestWGCtrl or BaseClass(BaseWGCtrl)
function RoleModelTestWGCtrl:__init()
	if RoleModelTestWGCtrl.Instance ~= nil then
		<PERSON>rrorLog("[RoleModelTestWGCtrl] attempt to create singleton twice!")
		return
	end
	RoleModelTestWGCtrl.Instance = self

	self.view = RoleModelTestView.New()
	self.gundam_view = GundamModelTestView.New()
	self.screen_shot_model_data = ScreenShotModelData.New()
	self.screen_shot_model_view = ScreenShotModelView.New()
end

function RoleModelTestWGCtrl:__delete()
	RoleModelTestWGCtrl.Instance = nil
	self.view:DeleteMe()
	self.view = nil

	self.gundam_view:DeleteMe()
	self.gundam_view = nil

	self.screen_shot_model_data:DeleteMe()
	self.screen_shot_model_data = nil

	self.screen_shot_model_view:DeleteMe()
	self.screen_shot_model_view = nil
end

function RoleModelTestWGCtrl:OpenRoleModelTestView()
	self.view:Open()
end

function RoleModelTestWGCtrl:OpenGundamModelTestView()
	self.gundam_view:Open()
end

function RoleModelTestWGCtrl:OpenScreenShotModelView()
	self.screen_shot_model_view:Open()
end