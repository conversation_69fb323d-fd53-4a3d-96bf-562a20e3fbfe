FuBenLoseView = FuBenLoseView or BaseClass(SafeBaseView)

--个人boss失败面板
function FuBenLoseView:__init()
	--self:SetModal(true)
	self.default_index = 0
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(false)
	self.rich_num = nil
	self.view_layer = UiLayer.Pop
	--self.texture_path_list[1] = "res/xui/fuben.png"
	self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_a3_commmon_tiaozhanjiesuan_panel")
	self:AddViewResource(0, "uis/view/common_jiesuan_prefab", "layout_jiesuan_lose")

	self.active_close = false
	self.end_time = 0
	self.is_need_playani = false
	self.load_complete = false

end

function FuBenLoseView:ReleaseCallBack()
	self.is_need_playani = nil
	self.load_complete = nil
	self.comefrom_scene_type = nil

	if self.play_star_event then
		GlobalEventSystem:UnBind(self.play_star_event)
		self.play_star_event = nil
	end

	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end

	if FuBenWGData.Instance then
		FuBenWGData.Instance:SetLoseSceneTypeChche(nil)
	end
end

function FuBenLoseView:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.btn_lose_tuichu, BindTool.Bind(self.CloseJieMianView, self))

	XUI.AddClickEventListener(self.node_list.layout_hs, BindTool.Bind(self.OpenJump, self, 1))      --幻兽提升
	XUI.AddClickEventListener(self.node_list.layout_hszy, BindTool.Bind(self.OpenJump, self, 2))    --幻兽奇遇
	XUI.AddClickEventListener(self.node_list.layout_sz, BindTool.Bind(self.OpenJump, self, 3))      --气衍万劫
	XUI.AddClickEventListener(self.node_list.layout_mbjl, BindTool.Bind(self.OpenJump, self, 4))    --每日礼包
	XUI.AddClickEventListener(self.node_list.layout_first_recharge, BindTool.Bind(self.OpenJump, self, 5))  --首冲
	XUI.AddClickEventListener(self.node_list.btn_lose_tuichu_next, BindTool.Bind(self.EnterNextFb, self))
	
	self.load_complete = true
	self.node_list.btn_lose_tuichu_next:SetActive(false)
	if ViewManager.Instance:IsOpen(GuideModuleName.SceneLoading) then
		if not self.play_star_event then
			self.play_star_event = GlobalEventSystem:Bind(SceneEventType.CLOSE_LOADING_VIEW,BindTool.Bind(self.SceneLoadComplete,self))
		end
	else
		-- self:PlayAction()
	end
	-- GuajiWGCtrl.Instance:StopGuaji()
end

function FuBenLoseView:SceneLoadComplete()
	-- self:PlayAction()
end

-- function FuBenLoseView:PlayAction()
-- 	if not self.load_complete then
-- 		self.is_need_playani = true
-- 		return
-- 	end
-- 	--for i = 1, 3 do
-- 		--self.node_list["T"..i]:SetActive(false)
-- 	--end
-- 	self.node_list.bottom:SetActive(false)
-- 	--self:SetRootNodeActive(true)
-- 	local is_first = false
-- 	local index = 1
-- 	if self.node_list and self.node_list.lose_bg then
-- 		self.node_list.lose_bg.transform.localScale = Vector3(2,2,2)
-- 	end
-- 	self.action_quest = GlobalTimerQuest:AddTimesTimer(function()
-- 		if not is_first then
-- 			self.node_list.lose_bg:SetActive(true)
-- 			self.node_list.lose_bg.transform.localScale = Vector3(2,2,2)
-- 			self.node_list.lose_bg.rect:DOScale(1,0.5)
-- 			is_first = true
-- 		end
-- 		--local node = self.node_list["T"..index]
-- 		--node:SetActive(true)
-- 		--local end_pos = Vector2(node.rect.anchoredPosition.x,node.rect.anchoredPosition.y)
-- 		--node.rect.anchoredPosition = Vector2(200, node.rect.anchoredPosition.y)
-- 		--UITween.AlpahShowPanel(node.gameObject, true, 1)
-- 		--node.rect:DOAnchorPos(end_pos,0.5)
-- 		index = index + 1
-- 		if index > 3 then
-- 			GlobalTimerQuest:CancelQuest(self.action_quest)
-- 			local node = self.node_list.bottom
-- 			node:SetActive(true)
-- 			local end_pos = Vector2(node.rect.anchoredPosition.x,node.rect.anchoredPosition.y)
-- 			node.rect.anchoredPosition = Vector2(200, node.rect.anchoredPosition.y)
-- 			UITween.AlpahShowPanel(node.gameObject, true, 1)
-- 			node.rect:DOAnchorPos(end_pos,0.5)
-- 		end
-- 	end,0.2,3)
-- end

function FuBenLoseView:OpenJump(click_type)
	FuBenWGCtrl.Instance:LoseViewCilckLeaveFB(click_type)
	self:Close()
end

function FuBenLoseView:EnterNextFb()
	if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
		local level = FuBenPanelWGData.Instance:GetPassLevel()
		local cfg = FuBenPanelWGData.Instance:GetCurXiuXianCfg(level + 1)
		if not IsEmptyTable(cfg) and GameVoManager.Instance:GetMainRoleVo().capability < cfg.capability then
			local data = {}
			data.level = level
			data.capability = cfg.capability
			data.enter_func = BindTool.Bind(self.RealEnterNext, self)
			FuBenPanelWGCtrl.Instance:OpenChallengeTip(data)
		else
			self:RealEnterNext()
		end
	end
end

function FuBenLoseView:RealEnterNext()
	self:Close()
	FuBenWGCtrl.Instance:SendFBReqNextLevel()
	--FuBenWGCtrl.Instance:SendEnterWelkinFb()
	MainuiWGCtrl.Instance:SetShowTimeTextState( false )
	Scene.Instance:SimulationSceneLoad()
end

function FuBenLoseView:ShowIndexCallBack(index)
	UITween.ShowCommonTiaoZhanJieSuanPanelTween(self, false, self.node_list.tween_info_root)

	if self.end_time > 0 then
		self:UpdateCloseCountDownTime(1, self.end_time)
		if CountDownManager.Instance:HasCountDown("txgjs_close_timer") then
			CountDownManager.Instance:RemoveCountDown("txgjs_close_timer")
		end
		CountDownManager.Instance:AddCountDown("txgjs_close_timer", BindTool.Bind1(self.UpdateCloseCountDownTime, self), BindTool.Bind1(self.CloseJieMianView, self), nil, self.end_time , 1)
	end

	local scene_type = Scene.Instance:GetSceneType()
	if scene_type ~= SceneType.Fb_Welkin then
		self.node_list.btn_lose_tuichu_next:SetActive(false)
		-- self.node_list.text_des:SetActive(false)
	else
		GuajiWGCtrl.Instance:StopGuaji()
		-- self.node_list.text_des:SetActive(false)
		self.node_list.btn_lose_tuichu_next:SetActive(true)

	end

	self:CheckRewardShow()
	-- local seconds=0.3
	-- local count_down_func = function(elapse_time, total_time)
	-- 	local last_time = math.ceil(total_time - elapse_time)
	-- end

	-- local complete_func = function()
	-- 	CountDownManager.Instance:RemoveCountDown("next_level")
	-- 	self:CloseViewHandler()
	-- end
	-- if seconds > 0 and (not CountDownManager.Instance:HasCountDown("next_level")) then
	-- 	CountDownManager.Instance:AddCountDown(
	-- 		"next_level",
	-- 		count_down_func,
	-- 		complete_func,
	-- 		nil,
	-- 		seconds,
	-- 		1)
	-- 	count_down_func(0, seconds)
	-- elseif seconds <= 0 then
	-- end
end

function FuBenLoseView:OpenCallBack()
	FuBenWGCtrl.Instance:TryCloseCurFuHuoView() --结算界面打开，立即关闭复活界面
	-- 清理缓存，不然还是会自动任务
	SceneWGData.Instance:ClearCommonSceneGuajiTypeAndAutoTask()
	TaskGuide.Instance:CanAutoAllTask(false)
	-- AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.MissionFailed, nil, true))
end

function FuBenLoseView:CloseCallBack()
	self.end_data = nil
	self.comefrom_scene_type = nil
	-- AudioManager.Instance:PlayOpenCloseUiEffect()
	-- if CountDownManager.Instance:HasCountDown("fb_lose_timer") then
	-- 	CountDownManager.Instance:RemoveCountDown("fb_lose_timer")
	-- end
	self.end_time = 0
	if CountDownManager.Instance:HasCountDown("txgjs_close_timer") then
		CountDownManager.Instance:RemoveCountDown("txgjs_close_timer")
	end
	if self.action_quest then
		GlobalTimerQuest:CancelQuest(self.action_quest)
		self.action_quest = nil
	end
end

function FuBenLoseView:SetEndData(data)
	self.end_data = data
end

function FuBenLoseView:CloseViewHandler()
	if Scene.Instance:GetSceneType() == SceneType.Fb_Welkin then
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

function FuBenLoseView:CloseJieMianView()
	FuBenWGCtrl.Instance:SendLeaveFB()
	if self.comefrom_scene_type == SceneType.ZHUANZHI_FB then
		LimitTimeGiftWGCtrl.Instance:CheckZhuanZhiPopupGift()
	end
	self:Close()
end

function FuBenLoseView:OnChallengeNextMission()
	FuBenWGCtrl.Instance:SendEnterWelkinFb()
	MainuiWGCtrl.Instance:SetShowTimeTextState( false )
	-- Scene.Instance:LoadSameScene(Scene.Instance:GetSceneType())
	self:Close()
end

function FuBenLoseView:FbCloseTime(time)
	self.end_time = time or 0
end

function FuBenLoseView:UpdateCloseCountDownTime(elapse_time, total_time)
	if total_time - elapse_time > 0 then
		self.node_list.lbl_end_time.text.text = string.format(Language.GuildBattleRanked.BattleRankedEndTime, math.floor(total_time - elapse_time))
	end
end

function FuBenLoseView:OnClinkCloseHandler()
	self:Close()
end

function FuBenLoseView:CheckRewardShow()
	local scene_type = FuBenWGData.Instance:GetLoseSceneTypeChche()
	--print_error("FFF====== SceneType.FengShenBang", SceneType.FengShenBang, scene_type)
	local big_bg_width = nil
	local big_bg_height = 282
	if scene_type == SceneType.FengShenBang then
		big_bg_height = 345
		self:FengShenBangShowItemReward()
	end

	-- if self:GetRewardItemListActive() then
	-- 	-- self:SetLoseBigBgSize(big_bg_width, big_bg_height)
	-- end
end

-- function FuBenLoseView:SetLoseBigBgSize(width, height)
-- 	if self.node_list["lose_jiesuan_bg"] then
-- 		width = width or self.node_list["lose_jiesuan_bg"].transform.sizeDelta.x
-- 		height = height or self.node_list["lose_jiesuan_bg"].transform.sizeDelta.y
-- 		self.node_list["lose_jiesuan_bg"].transform.sizeDelta = Vector2(width, height)
-- 	end
-- end

function FuBenLoseView:SetRightBottomText(text_str)
	if self.node_list["right_bottom_lbl"] then
		self.node_list["right_bottom_lbl"].text.text = text_str or ""
	end
end

function FuBenLoseView:SetRewardItemList(data_list)
	if not self.node_list["reward_item_list"] then
		return
	end
	--print_error("FFF==== data_list", data_list)
	if not IsEmptyTable(data_list) then
		if not self.reward_item_list then
			self.reward_item_list = AsyncListView.New(ItemCell, self.node_list["reward_item_list"])
		end
		self.reward_item_list:SetDataList(data_list)
	end
	
	self.node_list.reward_root:CustomSetActive(not IsEmptyTable(data_list))
	-- self.node_list["reward_item_title"]:SetActive(not IsEmptyTable(data_list))
	-- self.node_list["reward_item_list"]:SetActive(not IsEmptyTable(data_list))
end

-- function FuBenLoseView:GetRewardItemListActive()
-- 	return self.node_list["reward_item_list"] and self.node_list["reward_item_list"].gameObject.activeSelf
-- end

function FuBenLoseView:FengShenBangShowItemReward()
	local tip_show_des = ""
	if FengShenBangWGData.Instance:GetJoinRewardShowState() then
		local other_cfg = FengShenBangWGData.Instance:GetActCfgList("other")
		local need_time = other_cfg.join_num or 5
		local scene_id = FengShenBangWGData.Instance:GetFSBSceneId()
		--print_error("FFF==== scene_id", scene_id)
		local scene_cfg = FengShenBangWGData.Instance:GetSceneCfgDataBySceneID(scene_id)
		if scene_cfg and scene_cfg.fail_reward_item then
			local temp_data = {}
			for k, v in pairs(scene_cfg.fail_reward_item) do
				table.insert(temp_data, v)
			end
			self:SetRewardItemList(temp_data)
		end
		
		tip_show_des = string.format(Language.FengShenBang.JoinJieSuanDes, Language.Common.UpNum[need_time])
	end
	self:SetRightBottomText(tip_show_des)
end

function FuBenLoseView:SetSceneType(scene_type)
	self.comefrom_scene_type = scene_type
end