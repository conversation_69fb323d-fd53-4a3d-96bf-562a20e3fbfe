local empty = ""

HURT_TYPE = {
	NOR_BAI = 1,			--模板（白）
	NOR_CHENG = 2,			--模板（橙）
	NOR_HONG = 3,			--模板（红）
	NOR_HUANG = 4,			--模板（黄）
	NOR_LAN = 5,			--模板（蓝）
	NOR_LV = 6,				--模板（绿）
	NOR_QING = 7,			--模板（青）
	NOR_ZI = 8,				--模板（紫）
	NOR_FEN = 9,			--模板（粉）
	NOR_JIN = 10,			--模板（金）
	NOR_HEI = 11,			--模板（黑）
	NOR_HUI = 12,			--模板（灰）
	NOR_HONGBAI = 25,       --模板（红白）
	NOR_HONGCHENG = 26,     --模板（红橙）
	ROLE_NOR_BAI = 13,		--模板（白）（角色）
	ROLE_NOR_CHENG = 14,	--模板（橙）（角色）
	ROLE_NOR_HONG = 15,		--模板（红）（角色）
	ROLE_NOR_HUANG = 16,	--模板（黄）（角色）
	ROLE_NOR_LAN = 17,		--模板（蓝）（角色）
	ROLE_NOR_LV = 18,		--模板（绿）（角色）
	ROLE_NOR_QING = 19,		--模板（青）（角色）
	ROLE_NOR_ZI = 20,		--模板（紫）（角色）
	ROLE_NOR_FEN = 21,		--模板（粉）（角色）
	ROLE_NOR_JIN = 22,		--模板（金）（角色）
	ROLE_NOR_HEI = 23,		--模板（黑）（角色）
	ROLE_NOR_HUI = 24,		--模板（灰）（角色）

	----上面按颜色形成的基础模板，如有颜色一样动画不同请新建拓展
}

-- 配置（目前全是模板）
HUDAnimShowType = 
{	
	[HURT_TYPE.NOR_BAI] = "hurt_nor_bai_anim",
	[HURT_TYPE.NOR_CHENG] = "hurt_nor_cheng_anim",
	[HURT_TYPE.NOR_HONG] = "hurt_nor_hong_anim",
	[HURT_TYPE.NOR_HUANG] = "hurt_nor_huang_anim",
	[HURT_TYPE.NOR_LAN] = "hurt_nor_lan_anim",
	[HURT_TYPE.NOR_LV] = "hurt_nor_lv_anim",
	[HURT_TYPE.NOR_QING] = "hurt_nor_qing_anim",
	[HURT_TYPE.NOR_ZI] = "hurt_nor_zi_anim",
	[HURT_TYPE.NOR_FEN] = "hurt_nor_fen_anim",
	[HURT_TYPE.NOR_JIN] = "hurt_nor_jin_anim",
	[HURT_TYPE.NOR_HEI] = "hurt_nor_hei_anim",
	[HURT_TYPE.NOR_HUI] = "hurt_nor_hui_anim",
	[HURT_TYPE.NOR_HONGBAI] = "hurt_nor_hongbai_anim",
	[HURT_TYPE.NOR_HONGCHENG] = "hurt_role_nor_ch_anim",
	[HURT_TYPE.ROLE_NOR_BAI] = "hurt_role_nor_bai_anim",
	[HURT_TYPE.ROLE_NOR_CHENG] = "hurt_role_nor_cheng_anim",
	[HURT_TYPE.ROLE_NOR_HONG] = "hurt_role_nor_hong_anim",
	[HURT_TYPE.ROLE_NOR_HUANG] = "hurt_role_nor_huang_anim",
	[HURT_TYPE.ROLE_NOR_LAN] = "hurt_role_nor_lan_anim",
	[HURT_TYPE.ROLE_NOR_LV] = "hurt_role_nor_lv_anim",
	[HURT_TYPE.ROLE_NOR_QING] = "hurt_role_nor_qing_anim",
	[HURT_TYPE.ROLE_NOR_ZI] = "hurt_role_nor_zi_anim",
	[HURT_TYPE.ROLE_NOR_FEN] = "hurt_role_nor_fen_anim",
	[HURT_TYPE.ROLE_NOR_JIN] = "hurt_role_nor_jin_anim",
	[HURT_TYPE.ROLE_NOR_HEI] = "hurt_role_nor_hei_anim",
	[HURT_TYPE.ROLE_NOR_HUI] = "hurt_role_nor_hui_anim",
}

-- 特殊类型
SpecialAnimShowType = {
	[WUXING_HURT_TYPE.WUXING_JIN] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],		--五行 --金
	[WUXING_HURT_TYPE.WUXING_MU] = HUDAnimShowType[HURT_TYPE.NOR_LV],			--五行 --木
	[WUXING_HURT_TYPE.WUXING_SHUI] = HUDAnimShowType[HURT_TYPE.NOR_LAN],		--五行 --水
	[WUXING_HURT_TYPE.WUXING_HUO] = HUDAnimShowType[HURT_TYPE.NOR_HONG],		--五行 --火
	[WUXING_HURT_TYPE.WUXING_TU] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],		--五行 --土
}

SpecialAnimHeadString = {
	[WUXING_HURT_TYPE.WUXING_JIN] = empty,										--五行 --金
	[WUXING_HURT_TYPE.WUXING_MU] = empty,										--五行 --木
	[WUXING_HURT_TYPE.WUXING_SHUI] = empty,										--五行 --水
	[WUXING_HURT_TYPE.WUXING_HUO] = empty,										--五行 --火
	[WUXING_HURT_TYPE.WUXING_TU] = empty,										--五行 --土
}

SpecialAnimHeadStringTB = {
	[WUXING_HURT_TYPE.WUXING_JIN] = empty,										--五行 --金
	[WUXING_HURT_TYPE.WUXING_MU] = empty,										--五行 --木
	[WUXING_HURT_TYPE.WUXING_SHUI] = empty,										--五行 --水
	[WUXING_HURT_TYPE.WUXING_HUO] = empty,										--五行 --火
	[WUXING_HURT_TYPE.WUXING_TU] = empty,										--五行 --土
}

-- 其他特殊类型
OtherAnimShowType = {
	[FIGHT_TEXT_TYPE.BAOJU] = HUDAnimShowType[HURT_TYPE.NOR_LAN],				--宝具
	[FIGHT_TEXT_TYPE.NVSHEN] = HUDAnimShowType[HURT_TYPE.NOR_ZI],				--女神
}

OtherAnimHeadString = {
	[FIGHT_TEXT_TYPE.BAOJU] = empty,											--宝具
	[FIGHT_TEXT_TYPE.NVSHEN] = empty,											--女神
}

OtherAnimHeadStringTB = {
	[FIGHT_TEXT_TYPE.BAOJU] = empty,											--宝具
	[FIGHT_TEXT_TYPE.NVSHEN] = empty,											--女神
}

-- 飘字前缀
AnimHeadString = {
	-- 白色字前缀（模板）
	BAI_SH = "bai_sh",							-- 白 伤害
	BAI_YS = "bai_ys",							-- 白 伤害
	-- 橙色字前缀（模板）
	CHENG_HG = "cheng_hg",						-- 橙 火攻
	CHENG_JC = "cheng_jc",						-- 橙 击穿
	CHENG_LX = "cheng_lx",						-- 橙 流血
	CHENG_PJ = "cheng_pj",						-- 橙 破击
	CHENG_WH = "cheng_wh",						-- 橙 武魂
	-- 粉色字前缀（模板）
	FEN_XIAN = "fen_xian",						-- 粉 仙修
	-- 黑色字前缀（模板）
	HEI_PO = "hei_po",							-- 黑 破盾
	HEI_ZS = "hei_zs",							-- 黑 斩杀
	-- 红色字前缀（模板）
	HONG_FS = "hong_fs",						-- 红 反伤
	HONG_YS = "hong_ys",						-- 红 友伤
	-- 黄色字前缀（模板）
	HUANG_BJ = "huang_bj",						-- 黄 暴击
	HUANG_HX = "huang_hx",						-- 黄 会心
	HUANG_XY = "huang_xy",						-- 黄 星陨
	-- 蓝色字前缀（模板）
	LAN_LJ = "lan_lj",							-- 蓝 法宝
	LAN_HS = "lan_hs",							-- 蓝 幻兽
	LAN_BOSS_MS = "lan_boss_miaosha",			-- 蓝 Boss秒杀
	-- 绿色字前缀（模板）
	LV_HX = "lv_hx",							-- 绿 回血
	LV_ZD = "lv_sx",							-- 绿 嗜血
	LV_ZD_TB = "lv_zd_tb",						-- 绿 中毒图标
	-- 青色字前缀（模板）
	QING_BS = "qing_bs",						-- 青 不死
	QING_DK = "qing_dk",						-- 青 抵抗
	QING_GD = "qing_gd",						-- 青 格挡
	QING_MY = "qing_my",						-- 青 免疫
	QING_SB = "qing_sb",						-- 青 闪避
	QING_XS = "qing_xs",						-- 青 吸收
	QING_ZJ = "qing_zj",						-- 青 吸收
	-- 紫色字前缀（模板）
	ZI_BC = "zi_bc",							-- 紫 背刺
	ZI_FQ = "zi_fq",							-- 紫 法器
	ZI_JX = "zi_jx",							-- 紫 剑心
	ZI_SJ = "zi_sj",							-- 紫 神机
	ZI_TW = "zi_tw",							-- 紫 天武
	ZI_YG = "zi_yg",							-- 紫 幽冥
	ZI_ZD = "zi_zd",							-- 紫 中毒
	-- 金色字前缀（模板）
	JIN_ZS = "jin_zs",							-- 金 真伤
	JIN_TS = "jin_ts",							-- 金 天神
	-- 紫色字前缀（模板）
	HUI_WMZ = "hui_wmz",						-- 灰 未命中
	HUI_HD = "hui_hd",							-- 灰 护盾

	HONGBAI_PO = "hongbai_po",                  -- 红白-破
	-- 红橙字前缀（模板）
	HONGCHENG_PO = "a3_pz_ch_po",				-- 黑 破盾
	HONGCHENG_ZS = "a3_pz_ch_zhan",				-- 黑 斩杀
}

-- 匹配战斗类型（特殊请新建）
FIGHT_ANIM_TYPE = {
			[FIGHT_TYPE.SHANBI] = HUDAnimShowType[HURT_TYPE.NOR_HUI],    				-- 闪避
			[FIGHT_TYPE.NORMAL] = HUDAnimShowType[HURT_TYPE.NOR_BAI],    				-- 正常攻击
			[FIGHT_TYPE.DIDANG] = HUDAnimShowType[HURT_TYPE.NOR_QING],   				-- 抵挡
			[FIGHT_TYPE.BAOJI] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],   				-- 暴击
			[FIGHT_TYPE.MIANYI] = HUDAnimShowType[HURT_TYPE.NOR_QING],   				-- 免疫
			[FIGHT_TYPE.DIKANG] = HUDAnimShowType[HURT_TYPE.NOR_QING],   				-- 抵抗
			[FIGHT_TYPE.POJI] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],    				-- 破击
			[FIGHT_TYPE.BAOPO] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   				-- 破击加暴击
			[FIGHT_TYPE.LINGTONG] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   				-- 灵童攻击
			[FIGHT_TYPE.FABAO] = HUDAnimShowType[HURT_TYPE.NOR_ZI],      				-- 法宝伤害
			[FIGHT_TYPE.HPSTORE] = HUDAnimShowType[HURT_TYPE.NOR_HUI],   				-- 护盾
			[FIGHT_TYPE.MOUNT] = HUDAnimShowType[HURT_TYPE.NOR_ZI],  					-- 坐骑伤害
			[FIGHT_TYPE.SHEHUN_DUOPO] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   			-- 摄魂夺魄
			[FIGHT_TYPE.FUWEN] = HUDAnimShowType[HURT_TYPE.NOR_ZI],  					-- 符文伤害
			[FIGHT_TYPE.HUIXINYIJI] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],   			-- 会心一击
			[FIGHT_TYPE.GEDANG] = HUDAnimShowType[HURT_TYPE.NOR_QING], 					-- 格挡
			[FIGHT_TYPE.LIANJI] = HUDAnimShowType[HURT_TYPE.NOR_LAN],   				-- 连击
			[FIGHT_TYPE.JICHUAN] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   				-- 击穿
			[FIGHT_TYPE.PET] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   					-- 宠物攻击伤害
			[FIGHT_TYPE.XiaoTianQuan] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   			-- 哮天犬攻击
			[FIGHT_TYPE.ERCIGONGJI] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   				-- 二次攻击
			[FIGHT_TYPE.LIUXUE] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   				-- 流血
			[FIGHT_TYPE.HUIXUE] = HUDAnimShowType[HURT_TYPE.NOR_LV],   					-- 回血
			[FIGHT_TYPE.ZHUOSHAO] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   			-- 灼烧
			[FIGHT_TYPE.FANGSHANG] = HUDAnimShowType[HURT_TYPE.NOR_HONG],   			-- 反伤
			[FIGHT_TYPE.TS_YOUJUN] = HUDAnimShowType[HURT_TYPE.NOR_HONG],   			-- 太深—友军
			[FIGHT_TYPE.ZHONGDU] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   				-- 中毒
			[FIGHT_TYPE.CHONGJIBO] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],   -- 额外一次伤害，冲击波形式
			[FIGHT_TYPE.FIGHT_TYPE_XIXUE] = HUDAnimShowType[HURT_TYPE.NOR_LV],   -- 吸血
			[FIGHT_TYPE.FIGHT_TYPE_CRIT_2] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],   -- 2倍暴击
			[FIGHT_TYPE.FIGHT_TYPE_CRIT_3] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],   -- 3倍暴击
			[FIGHT_TYPE.FIGHT_TYPE_CRIT_4] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],   -- 4倍暴击
			[FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_2] = HUDAnimShowType[HURT_TYPE.NOR_LV],   -- 2倍治疗暴击
			[FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_3] = HUDAnimShowType[HURT_TYPE.NOR_LV],   -- 3倍治疗暴击
			[FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_4] = HUDAnimShowType[HURT_TYPE.NOR_LV],   -- 4倍治疗暴击
			[FIGHT_TYPE.FIGHT_TYPE_BEHEADED] = HUDAnimShowType[HURT_TYPE.NOR_HEI],   -- 斩杀
			[FIGHT_TYPE.FIGHT_TYPE_LEIJI] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   -- 雷击
			[FIGHT_TYPE.FIGHT_TYPE_KNOCK_FLY] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   -- 齐天大圣击飞
			[FIGHT_TYPE.EQUIP_XIANJIE_SKILL] = HUDAnimShowType[HURT_TYPE.NOR_LV],   -- 仙戒回血
			[FIGHT_TYPE.EQUIP_XIANZHOU_SKILL] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 仙镯伤害
			[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_1] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 青龙技能
			[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_2] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 白虎技能
			[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_3] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 朱雀技能
			[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_4] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 玄武技能
			[FIGHT_TYPE.FIGHT_TYPE_SHENJI_SKILL] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 神机技能
			[FIGHT_TYPE.FIGHT_TYPE_SHENJI_RUODIANJIPO] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 神机技能弱点击破
			[FIGHT_TYPE.FIGHT_TYPE_ANQI_WEAPON] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 暗器兵器
			[FIGHT_TYPE.FIGHT_TYPE_ANQI_ARMOR] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 暗器护甲
			[FIGHT_TYPE.FIGHT_TYPE_MIANSI] = HUDAnimShowType[HURT_TYPE.NOR_QING],   -- 免死
			[FIGHT_TYPE.FIGHT_TYPE_BACK] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 背刺
			[FIGHT_TYPE.FIGHT_TYPE_INVISIBLE_ATTACK] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],   -- 破隐一击
			[FIGHT_TYPE.FIGHT_TYPE_DRUNK_FIRE] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   -- 醉酒燃烧
			[FIGHT_TYPE.FIGHT_TYPE_DRUNK_REBOUND] = HUDAnimShowType[HURT_TYPE.NOR_BAI],   -- 熊猫反伤
			[FIGHT_TYPE.FIGHT_TYPE_LONGZHU_INJURE] = HUDAnimShowType[HURT_TYPE.NOR_HUANG],   -- 龙珠伤害
			[FIGHT_TYPE.FIGHT_TYPE_FA_BAO] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 法宝
			[FIGHT_TYPE.FIGHT_TYPE_CLOAK_ZUZHOU] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 神兵诅咒伤害
			[FIGHT_TYPE.FIGHT_TYPE_DIDNAG_WING] = HUDAnimShowType[HURT_TYPE.NOR_QING],   -- 器魂 羽翼抵挡
			[FIGHT_TYPE.FIGHT_TYPE_DIDNAG_JIANLING] = HUDAnimShowType[HURT_TYPE.NOR_QING],   -- 器魂 剑灵抵挡
			[FIGHT_TYPE.FIGHT_TYPE_WING_HUIXUE] = HUDAnimShowType[HURT_TYPE.NOR_LV],   -- 器魂 羽翼回血
			[FIGHT_TYPE.FIGHT_TYPE_JIANLING_HUIXUE] = HUDAnimShowType[HURT_TYPE.NOR_LV],   -- 器魂 剑灵回血
			[FIGHT_TYPE.FIGHT_TYPE_DELAY_INJURE] = HUDAnimShowType[HURT_TYPE.NOR_BAI],   -- 延迟飘血
			[FIGHT_TYPE.BOSS_ZHENSHANG] = HUDAnimShowType[HURT_TYPE.NOR_JIN],   -- boss真伤
			[FIGHT_TYPE.BOSS_SECKILL] = HUDAnimShowType[HURT_TYPE.NOR_HEI],   -- boss秒杀
			[FIGHT_TYPE.WUHUN_ZHENSHANG] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   -- 武魂真伤
			[FIGHT_TYPE.BEAST] = HUDAnimShowType[HURT_TYPE.NOR_LAN],   -- 驭兽伤害
			[FIGHT_TYPE.BEAST_DRAGON] = HUDAnimShowType[HURT_TYPE.NOR_LAN],   -- 驭兽--龙
			[FIGHT_TYPE.FIGHT_TYPE_SHUANGSHENG_TIANSHEN] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 双生神灵
			[FIGHT_TYPE.FIGHT_TYPE_JIJIA] = HUDAnimShowType[HURT_TYPE.NOR_FEN],   -- 机甲伤害
			[FIGHT_TYPE.FIGHT_TYPE_JINGJIE] = HUDAnimShowType[HURT_TYPE.NOR_BAI],   -- 境界压制伤害
			[FIGHT_TYPE.FIGHT_TYPE_LONGSHEN_HUANG] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   -- 龙神伤害
			[FIGHT_TYPE.FIGHT_TYPE_LONGSHEN_LV] = HUDAnimShowType[HURT_TYPE.NOR_CHENG],   -- 创世套龙神伤害
			[FIGHT_TYPE.FIGHT_TYPE_TIANSHEN] = HUDAnimShowType[HURT_TYPE.NOR_JIN],   -- 所有的天神伤害
			[FIGHT_TYPE.FIGHT_TYPE_ESOTERICA] = HUDAnimShowType[HURT_TYPE.NOR_FEN],   -- 秘籍伤害
			[FIGHT_TYPE.FIGHT_TYPE_TIANSHEN_HEJI] = HUDAnimShowType[HURT_TYPE.NOR_FEN],   -- 天神合击伤害
			[FIGHT_TYPE.FIGHT_TYPE_WUXING_ACTIVE] = HUDAnimShowType[HURT_TYPE.NOR_FEN],   -- 天道五行伤害
			[FIGHT_TYPE.FIGHT_TYPE_WAIST_LIGHT] = HUDAnimShowType[HURT_TYPE.NOR_FEN],   -- 五行沧溟伤害
			[FIGHT_TYPE.FIGHT_TYPE_WUSHANGZHIJING] = HUDAnimShowType[HURT_TYPE.NOR_FEN],   -- 无上之境伤害
			[FIGHT_TYPE.FIGHT_TYPE_EXP_WEST_CARD_SKILL] = HUDAnimShowType[HURT_TYPE.NOR_ZI],   -- 副本-历练西行卡牌技能伤害
			[FIGHT_TYPE.FIGHT_TYPE_XIUWEI] = HUDAnimShowType[HURT_TYPE.NOR_FEN],   -- 修为变身技能伤害
			[FIGHT_TYPE.FIGHT_TYPE_MONSTER_SHIELD_BROKEN] = HUDAnimShowType[HURT_TYPE.NOR_HONGCHENG],   -- 怪物破盾伤害
			[FIGHT_TYPE.FIGHT_TYPE_ELEMENTAL] = HUDAnimShowType[HURT_TYPE.NOR_BAI],   -- 元素伤害
			[FIGHT_TYPE.FIGHT_TYPE_BOSS_SECKILL_GL] = HUDAnimShowType[HURT_TYPE.NOR_HEI],   -- boss秒杀(概率)
			[FIGHT_TYPE.FIGHT_TYPE_BREAK_SHIELD] = HUDAnimShowType[HURT_TYPE.NOR_HONGBAI],   -- 新破盾
			[FIGHT_TYPE.FIGHT_TYPE_MAX] = empty,							-- 服务端这个类型只为了同步血量，不飘字

}

-- 匹配战斗类型（特殊请新建）（作用在主角）
FIGHT_ANIM_TYPE_MAIN_ROLE = {
	[FIGHT_TYPE.SHANBI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],									-- 闪避
	[FIGHT_TYPE.NORMAL] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],									-- 正常攻击
	[FIGHT_TYPE.DIDANG] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],									-- 抵挡
	[FIGHT_TYPE.BAOJI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],									-- 暴击
	[FIGHT_TYPE.MIANYI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],									-- 免疫
	[FIGHT_TYPE.DIKANG] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],									-- 抵抗
	[FIGHT_TYPE.POJI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],									-- 破击
	[FIGHT_TYPE.BAOPO] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],									-- 破击加暴击
	[FIGHT_TYPE.LINGTONG] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],								-- 灵童攻击
	[FIGHT_TYPE.FABAO] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],									-- 法宝伤害
	[FIGHT_TYPE.HPSTORE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_BAI],									-- 护盾
	[FIGHT_TYPE.MOUNT] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],									-- 坐骑伤害
	[FIGHT_TYPE.SHEHUN_DUOPO] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI], 							-- 摄魂夺魄
	[FIGHT_TYPE.FUWEN] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI],									-- 符文伤害
	[FIGHT_TYPE.HUIXINYIJI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],								-- 会心一击
	[FIGHT_TYPE.GEDANG] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],									-- 格挡
	[FIGHT_TYPE.LIANJI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],                    				-- 连击
	[FIGHT_TYPE.JICHUAN] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],                    			-- 击穿
	[FIGHT_TYPE.PET] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN], 									-- 宠物攻击伤害
	[FIGHT_TYPE.XiaoTianQuan] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],							-- 哮天犬攻击
	[FIGHT_TYPE.ERCIGONGJI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_BAI],								-- 二次攻击
	[FIGHT_TYPE.LIUXUE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_CHENG],								-- 流血
	[FIGHT_TYPE.HUIXUE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],									-- 回血
	[FIGHT_TYPE.ZHUOSHAO] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_CHENG],								-- 灼烧
	[FIGHT_TYPE.FANGSHANG] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_BAI],								-- 反伤
	[FIGHT_TYPE.TS_YOUJUN] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI],								-- 太深—友军
	[FIGHT_TYPE.ZHONGDU] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],									-- 中毒
	[FIGHT_TYPE.CHONGJIBO] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_BAI],                				-- 额外一次伤害，冲击波形式
	[FIGHT_TYPE.FIGHT_TYPE_XIXUE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],							-- 吸血
    [FIGHT_TYPE.FIGHT_TYPE_CRIT_2] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],                     	-- 2倍暴击
    [FIGHT_TYPE.FIGHT_TYPE_CRIT_3] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],                     	-- 3倍暴击
    [FIGHT_TYPE.FIGHT_TYPE_CRIT_4] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],                     	-- 4倍暴击
    [FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_2] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],                -- 2倍治疗暴击
    [FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_3] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],                -- 3倍治疗暴击
    [FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_4] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],                -- 4倍治疗暴击
    [FIGHT_TYPE.FIGHT_TYPE_BEHEADED] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],					-- 斩杀
    [FIGHT_TYPE.FIGHT_TYPE_LEIJI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],                       -- 雷击
    [FIGHT_TYPE.FIGHT_TYPE_KNOCK_FLY] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],					-- 齐天大圣击飞
	[FIGHT_TYPE.EQUIP_XIANJIE_SKILL] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],						-- 仙戒回血
	[FIGHT_TYPE.EQUIP_XIANZHOU_SKILL] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI],						-- 仙镯伤害
	[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_1] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],					-- 青龙技能
	[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_2] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],					-- 白虎技能
	[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_3] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],					-- 朱雀技能
	[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_4] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HUANG],					-- 玄武技能
	[FIGHT_TYPE.FIGHT_TYPE_SHENJI_SKILL] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI],					-- 神机技能
	[FIGHT_TYPE.FIGHT_TYPE_SHENJI_RUODIANJIPO] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI],			-- 神机技能弱点击破
	[FIGHT_TYPE.FIGHT_TYPE_ANQI_WEAPON] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HUANG],				-- 暗器兵器
	[FIGHT_TYPE.FIGHT_TYPE_ANQI_ARMOR] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HUANG],					-- 暗器护甲
	[FIGHT_TYPE.FIGHT_TYPE_MIANSI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],						-- 免死
	[FIGHT_TYPE.FIGHT_TYPE_BACK] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI],							-- 背刺
	[FIGHT_TYPE.FIGHT_TYPE_INVISIBLE_ATTACK] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HUANG],			-- 破隐一击
	[FIGHT_TYPE.FIGHT_TYPE_DRUNK_FIRE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HONG],					-- 醉酒燃烧
	[FIGHT_TYPE.FIGHT_TYPE_DRUNK_REBOUND] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_BAI],				-- 熊猫反伤
	[FIGHT_TYPE.FIGHT_TYPE_LONGZHU_INJURE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI],				-- 龙珠伤害
	[FIGHT_TYPE.FIGHT_TYPE_FA_BAO] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],						-- 法宝
	[FIGHT_TYPE.FIGHT_TYPE_CLOAK_ZUZHOU] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_ZI],					-- 神兵诅咒伤害
	[FIGHT_TYPE.FIGHT_TYPE_DIDNAG_WING] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],					-- 器魂 羽翼抵挡
	[FIGHT_TYPE.FIGHT_TYPE_DIDNAG_JIANLING] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_QING],				-- 器魂 剑灵抵挡
	[FIGHT_TYPE.FIGHT_TYPE_WING_HUIXUE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],					-- 器魂 羽翼回血
	[FIGHT_TYPE.FIGHT_TYPE_JIANLING_HUIXUE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LV],				-- 器魂 剑灵回血
	[FIGHT_TYPE.FIGHT_TYPE_DELAY_INJURE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_BAI],					-- 延迟飘血
	[FIGHT_TYPE.BOSS_ZHENSHANG] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_JIN],							-- boss真伤
	[FIGHT_TYPE.BOSS_SECKILL] = HUDAnimShowType[HURT_TYPE.NOR_LAN],									-- boss秒杀
	[FIGHT_TYPE.WUHUN_ZHENSHANG] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_JIN],							-- 武魂真伤
	[FIGHT_TYPE.BEAST] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],									-- 驭兽伤害
	[FIGHT_TYPE.BEAST_DRAGON] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_JIN],							-- 驭兽--龙
	[FIGHT_TYPE.FIGHT_TYPE_SHUANGSHENG_TIANSHEN] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_JIN],			-- 双生神灵
	[FIGHT_TYPE.FIGHT_TYPE_JIJIA] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_FEN],						-- 机甲伤害
	[FIGHT_TYPE.FIGHT_TYPE_JINGJIE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_HEI],						-- 境界压制伤害
	[FIGHT_TYPE.FIGHT_TYPE_LONGSHEN_HUANG] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_JIN],				-- 龙神伤害
	[FIGHT_TYPE.FIGHT_TYPE_LONGSHEN_LV] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_JIN],					-- 创世套龙神伤害
	[FIGHT_TYPE.FIGHT_TYPE_TIANSHEN] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_JIN],						-- 所有的天神伤害
	[FIGHT_TYPE.FIGHT_TYPE_ESOTERICA] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_FEN],					-- 秘籍伤害
	[FIGHT_TYPE.FIGHT_TYPE_TIANSHEN_HEJI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_FEN],				-- 天神合击伤害
	[FIGHT_TYPE.FIGHT_TYPE_WUXING_ACTIVE] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_FEN],				-- 天道五行伤害
	[FIGHT_TYPE.FIGHT_TYPE_WAIST_LIGHT] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_FEN],					-- 五行沧溟伤害
	[FIGHT_TYPE.FIGHT_TYPE_WUSHANGZHIJING] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_FEN],				-- 无上之境伤害
	[FIGHT_TYPE.FIGHT_TYPE_EXP_WEST_CARD_SKILL] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_LAN],			-- 副本-历练西行卡牌技能伤害
	[FIGHT_TYPE.FIGHT_TYPE_XIUWEI] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_BAI],						-- 修为变身技能伤害
	[FIGHT_TYPE.FIGHT_TYPE_MONSTER_SHIELD_BROKEN] = HUDAnimShowType[HURT_TYPE.ROLE_NOR_BAI],		-- 怪物破盾伤害

	[FIGHT_TYPE.FIGHT_TYPE_MAX] = empty,							-- 服务端这个类型只为了同步血量，不飘字
}

-- 匹配战斗类型（特殊请新建）
-- {td_str				--飘字图标
--	text_str			--飘字艺术字
--	add					--是否显示加号
--  sub					--是否显示减号
--  num					--是否显示数字
--}

function BuildOneSetting(td_str, text_str, add, sub, num)
	local data = {}
	data.td_str = td_str
	data.text_str = text_str
	data.add = add
	data.sub = sub
	data.num = num

	return data
end

-- 默认只有伤害加数字的配置
local DEFAULT_SETTING = {
	td_str = empty,
	text_str = empty,
	add = false,
	sub = true,
	num = true,
}

FIGHT_ANIM_TYPE_SETTING = {
	[FIGHT_TYPE.SHANBI] = BuildOneSetting(empty, AnimHeadString.HUI_WMZ, false, false, false),									-- 未命中
	[FIGHT_TYPE.NORMAL] = DEFAULT_SETTING,																						-- 正常攻击
	[FIGHT_TYPE.DIDANG] = BuildOneSetting(empty, AnimHeadString.QING_DK, false, true, true),									-- 抵挡
	[FIGHT_TYPE.BAOJI] = BuildOneSetting(empty, AnimHeadString.HUANG_BJ, false, true, true),									-- 暴击
	[FIGHT_TYPE.MIANYI] = BuildOneSetting(empty, AnimHeadString.QING_MY, false, false, false),									-- 免疫
	[FIGHT_TYPE.DIKANG] = BuildOneSetting(empty, AnimHeadString.QING_DK, false, true, true),									-- 抵抗
	[FIGHT_TYPE.POJI] = BuildOneSetting(empty, AnimHeadString.CHENG_PJ, false, true, true),										-- 破击
	[FIGHT_TYPE.BAOPO] = BuildOneSetting(empty, AnimHeadString.CHENG_PJ, false, true, true),									-- 破击加暴击
	[FIGHT_TYPE.LINGTONG] = DEFAULT_SETTING,																					-- 灵童攻击
	[FIGHT_TYPE.FABAO] = BuildOneSetting(empty, AnimHeadString.ZI_FQ, false, true, true),										-- 法宝伤害
	[FIGHT_TYPE.HPSTORE] = BuildOneSetting(empty, AnimHeadString.HUI_hd, false, false, false),									-- 护盾
	[FIGHT_TYPE.MOUNT] = DEFAULT_SETTING,																						-- 坐骑伤害
	[FIGHT_TYPE.SHEHUN_DUOPO] = DEFAULT_SETTING, 																				-- 摄魂夺魄
	[FIGHT_TYPE.FUWEN] = DEFAULT_SETTING,																						-- 符文伤害
	[FIGHT_TYPE.HUIXINYIJI] = BuildOneSetting(empty, AnimHeadString.HUANG_HX, false, true, true),								-- 会心一击
	[FIGHT_TYPE.GEDANG] = BuildOneSetting(empty, AnimHeadString.QING_GD, false, true, true),									-- 格挡
	[FIGHT_TYPE.LIANJI] = BuildOneSetting(empty, AnimHeadString.LAN_LJ, false, true, true),                    					-- 连击
	[FIGHT_TYPE.JICHUAN] = BuildOneSetting(empty, AnimHeadString.CHENG_JC, false, true, true),	                    			-- 击穿
	[FIGHT_TYPE.PET] = BuildOneSetting(empty, AnimHeadString.ZI_JX, false, true, true), 										-- 宠物攻击伤害
	[FIGHT_TYPE.XiaoTianQuan] = BuildOneSetting(empty, empty, false, true, true),												-- 哮天犬攻击
	[FIGHT_TYPE.ERCIGONGJI] = BuildOneSetting(empty, AnimHeadString.LAN_LJ, false, true, true), 								-- 二次攻击
	[FIGHT_TYPE.LIUXUE] = BuildOneSetting(empty, AnimHeadString.CHENG_LX, false, true, true),									-- 流血
	[FIGHT_TYPE.HUIXUE] = BuildOneSetting(empty, AnimHeadString.LV_HX, true, false, true),										-- 回血
	[FIGHT_TYPE.ZHUOSHAO] = BuildOneSetting(empty, AnimHeadString.CHENG_HG, false, true, true),									-- 灼烧
	[FIGHT_TYPE.FANGSHANG] = BuildOneSetting(empty, AnimHeadString.HONG_FS, false, true, true),									-- 反伤
	[FIGHT_TYPE.TS_YOUJUN] = BuildOneSetting(empty, AnimHeadString.HONG_YS, false, true, true),									-- 太深—友军
	[FIGHT_TYPE.ZHONGDU] = BuildOneSetting(empty, AnimHeadString.ZI_ZD, false, true, true),										-- 中毒
	[FIGHT_TYPE.CHONGJIBO] = BuildOneSetting(empty, AnimHeadString.CHENG_PJ, false, true, true),								-- 额外一次伤害，冲击波形式
	[FIGHT_TYPE.FIGHT_TYPE_XIXUE] = BuildOneSetting(empty, AnimHeadString.LV_SX, true, false, true),							-- 吸血
    [FIGHT_TYPE.FIGHT_TYPE_CRIT_2] = BuildOneSetting(empty, AnimHeadString.HUANG_BJ, false, true, true),                 		-- 2倍暴击
    [FIGHT_TYPE.FIGHT_TYPE_CRIT_3] = BuildOneSetting(empty, AnimHeadString.HUANG_BJ, false, true, true),                 		-- 3倍暴击
    [FIGHT_TYPE.FIGHT_TYPE_CRIT_4] = BuildOneSetting(empty, AnimHeadString.HUANG_BJ, false, true, true),              			-- 4倍暴击
    [FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_2] = BuildOneSetting(empty, AnimHeadString.LV_HX, true, false, true),                	-- 2倍治疗暴击
    [FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_3] = BuildOneSetting(empty, AnimHeadString.LV_HX, true, false, true),                	-- 3倍治疗暴击
    [FIGHT_TYPE.FIGHT_TYPE_RECOVER_CRIT_4] = BuildOneSetting(empty, AnimHeadString.LV_HX, true, false, true),                	-- 4倍治疗暴击
    [FIGHT_TYPE.FIGHT_TYPE_BEHEADED] = BuildOneSetting(empty, AnimHeadString.HEI_ZS, false, true, true),  						-- 斩杀
    [FIGHT_TYPE.FIGHT_TYPE_LEIJI] = DEFAULT_SETTING,                         													-- 雷击
    [FIGHT_TYPE.FIGHT_TYPE_KNOCK_FLY] = DEFAULT_SETTING,																		-- 齐天大圣击飞
	[FIGHT_TYPE.EQUIP_XIANJIE_SKILL] = BuildOneSetting(empty, AnimHeadString.LV_HX, true, false, true),							-- 仙戒回血
	[FIGHT_TYPE.EQUIP_XIANZHOU_SKILL] = DEFAULT_SETTING,																		-- 仙镯伤害
	[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_1] = BuildOneSetting(empty, AnimHeadString.ZI_YG, false, true, true),  						-- 青龙技能
	[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_2] = BuildOneSetting(empty, AnimHeadString.ZI_YG, false, true, true),						-- 白虎技能
	[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_3] = BuildOneSetting(empty, AnimHeadString.ZI_YG, false, true, true),						-- 朱雀技能
	[FIGHT_TYPE.FIGHT_TYPE_SIXIANG_4] = BuildOneSetting(empty, AnimHeadString.ZI_YG, false, true, true),						-- 玄武技能
	[FIGHT_TYPE.FIGHT_TYPE_SHENJI_SKILL] = BuildOneSetting(empty, AnimHeadString.ZI_SJ, false, true, true),					-- 神机技能
	[FIGHT_TYPE.FIGHT_TYPE_SHENJI_RUODIANJIPO] = BuildOneSetting(empty, AnimHeadString.ZI_SJ, false, true, true),				-- 神机技能弱点击破
	[FIGHT_TYPE.FIGHT_TYPE_ANQI_WEAPON] = DEFAULT_SETTING,																		-- 暗器兵器
	[FIGHT_TYPE.FIGHT_TYPE_ANQI_ARMOR] = DEFAULT_SETTING,																		-- 暗器护甲
	[FIGHT_TYPE.FIGHT_TYPE_MIANSI] = BuildOneSetting(empty, AnimHeadString.QING_BS, false, false, false),						-- 免死
	[FIGHT_TYPE.FIGHT_TYPE_BACK] = BuildOneSetting(empty, AnimHeadString.ZI_BC, false, true, true),								-- 背刺
	[FIGHT_TYPE.FIGHT_TYPE_INVISIBLE_ATTACK] = DEFAULT_SETTING,																	-- 破隐一击
	[FIGHT_TYPE.FIGHT_TYPE_DRUNK_FIRE] = DEFAULT_SETTING,																		-- 醉酒燃烧
	[FIGHT_TYPE.FIGHT_TYPE_DRUNK_REBOUND] = DEFAULT_SETTING,																	-- 熊猫反伤
	[FIGHT_TYPE.FIGHT_TYPE_LONGZHU_INJURE] = DEFAULT_SETTING,																	-- 龙珠伤害
	[FIGHT_TYPE.FIGHT_TYPE_FA_BAO] = BuildOneSetting(empty, AnimHeadString.ZI_FQ, false, true, true),							-- 法宝
	[FIGHT_TYPE.FIGHT_TYPE_CLOAK_ZUZHOU] = BuildOneSetting(empty, AnimHeadString.ZI_TW, false, true, true),						-- 神兵诅咒伤害
	[FIGHT_TYPE.FIGHT_TYPE_DIDNAG_WING] = BuildOneSetting(empty, AnimHeadString.QING_DK, false, true, true),					-- 器魂 羽翼抵挡
	[FIGHT_TYPE.FIGHT_TYPE_DIDNAG_JIANLING] = BuildOneSetting(empty, AnimHeadString.QING_DK, false, true, true),				-- 器魂 剑灵抵挡
	[FIGHT_TYPE.FIGHT_TYPE_WING_HUIXUE] = BuildOneSetting(empty, AnimHeadString.LV_HX, true, false, true),						-- 器魂 羽翼回血
	[FIGHT_TYPE.FIGHT_TYPE_JIANLING_HUIXUE] = BuildOneSetting(empty, AnimHeadString.LV_HX, true, false, true),					-- 器魂 剑灵回血
	[FIGHT_TYPE.FIGHT_TYPE_DELAY_INJURE] = DEFAULT_SETTING,																		-- 延迟飘血
	[FIGHT_TYPE.BOSS_ZHENSHANG] = BuildOneSetting(empty, AnimHeadString.JIN_ZS, false, true, true),								-- boss真伤
	[FIGHT_TYPE.BOSS_SECKILL] = BuildOneSetting(empty, AnimHeadString.HEI_ZS, false, false, false),								-- boss秒杀
	[FIGHT_TYPE.WUHUN_ZHENSHANG] = BuildOneSetting(empty, AnimHeadString.CHENG_WH, false, true, true),							-- 武魂真伤
	[FIGHT_TYPE.BEAST] = BuildOneSetting(empty, AnimHeadString.LAN_HS, false, true, true),										-- 驭兽伤害
	[FIGHT_TYPE.BEAST_DRAGON] = BuildOneSetting(empty, AnimHeadString.LAN_HS, false, true, true),								-- 驭兽--龙
	[FIGHT_TYPE.FIGHT_TYPE_SHUANGSHENG_TIANSHEN] = BuildOneSetting(empty, AnimHeadString.JIN_SS, false, true, true),			-- 双生神灵
	[FIGHT_TYPE.FIGHT_TYPE_JIJIA] = DEFAULT_SETTING,																			-- 机甲伤害
	[FIGHT_TYPE.FIGHT_TYPE_JINGJIE] = BuildOneSetting(empty, AnimHeadString.FEN_XIAN, false, true, true),						-- 境界压制伤害
	[FIGHT_TYPE.FIGHT_TYPE_LONGSHEN_HUANG] = DEFAULT_SETTING,																	-- 龙神伤害
	[FIGHT_TYPE.FIGHT_TYPE_LONGSHEN_LV] = DEFAULT_SETTING,																		-- 创世套龙神伤害
	[FIGHT_TYPE.FIGHT_TYPE_TIANSHEN] = BuildOneSetting(empty, AnimHeadString.JIN_TS, false, true, true),						-- 所有的天神伤害
	[FIGHT_TYPE.FIGHT_TYPE_ESOTERICA] = BuildOneSetting(empty, AnimHeadString.FEN_XIAN, false, true, true),						-- 秘籍伤害
	[FIGHT_TYPE.FIGHT_TYPE_TIANSHEN_HEJI] = DEFAULT_SETTING,																	-- 天神合击伤害
	[FIGHT_TYPE.FIGHT_TYPE_WUXING_ACTIVE] = DEFAULT_SETTING,																	-- 天道五行伤害
	[FIGHT_TYPE.FIGHT_TYPE_WAIST_LIGHT] = DEFAULT_SETTING,																		-- 五行沧溟伤害
	[FIGHT_TYPE.FIGHT_TYPE_WUSHANGZHIJING] = DEFAULT_SETTING,																	-- 无上之境伤害
	[FIGHT_TYPE.FIGHT_TYPE_EXP_WEST_CARD_SKILL] = DEFAULT_SETTING,																-- 副本-历练西行卡牌技能伤害
	[FIGHT_TYPE.FIGHT_TYPE_XIUWEI] = BuildOneSetting(empty, AnimHeadString.FEN_XIAN, false, true, true),						-- 修为变身技能伤害
	[FIGHT_TYPE.FIGHT_TYPE_MONSTER_SHIELD_BROKEN] = BuildOneSetting(empty, AnimHeadString.HONGCHENG_PO, false, true, true),		-- 怪物破盾伤害
	[FIGHT_TYPE.FIGHT_TYPE_ELEMENTAL] = BuildOneSetting(empty, AnimHeadString.BAI_YS, false, true, true),						-- 元素伤害
	[FIGHT_TYPE.FIGHT_TYPE_BOSS_SECKILL_GL] = BuildOneSetting(empty, AnimHeadString.HEI_ZS, false, true, true),					-- BOSS秒杀(概率)
	[FIGHT_TYPE.FIGHT_TYPE_BREAK_SHIELD] = BuildOneSetting(empty, AnimHeadString.HONGBAI_PO, false, true, true),                -- 新破盾

	[FIGHT_TYPE.FIGHT_TYPE_MAX] = DEFAULT_SETTING,				-- 服务端这个类型只为了同步血量，不飘字
}

-- 特殊类型
FIGHT_SPEC_ANIM_TYPE_SETTING = {
	[FIGHT_TYPE.SHANBI] = BuildOneSetting(empty, AnimHeadString.QING_SB, false, false, false),									-- 闪避
	[FIGHT_TYPE.BAOJI] = BuildOneSetting(empty, AnimHeadString.HONG_BJ, false, true, true),										-- 暴击
	[FIGHT_TYPE.FIGHT_TYPE_CRIT_2] = BuildOneSetting(empty, AnimHeadString.HONG_BJ, false, true, true),                 		-- 2倍暴击
    [FIGHT_TYPE.FIGHT_TYPE_CRIT_3] = BuildOneSetting(empty, AnimHeadString.HONG_BJ, false, true, true),                 		-- 3倍暴击
    [FIGHT_TYPE.FIGHT_TYPE_CRIT_4] = BuildOneSetting(empty, AnimHeadString.HONG_BJ, false, true, true),              			-- 4倍暴击
}