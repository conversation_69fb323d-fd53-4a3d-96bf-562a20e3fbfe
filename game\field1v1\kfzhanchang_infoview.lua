--------------------------------------------------
-- 1v1  3v3  仙盟争霸 开启信息提示
--------------------------------------------------
KFZhanChangInfoView = KFZhanChangInfoView or BaseClass(SafeBaseView)

function KFZhanChangInfoView:__init()
	self.is_modal = true
	self.is_any_click_close = true
	self.view_layer = UiLayer.Pop

	self.view_name = "KFZhanChangInfoView"
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_third2_panel")
	self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_boss_forenotice")
end
function KFZhanChangInfoView:__delete()
end
function KFZhanChangInfoView:ReleaseCallBack()
	self.panel_type = nil
end
function KFZhanChangInfoView:LoadCallBack()
	self.node_list["btn_go_kill"].button:AddClickListener(BindTool.Bind1(self.ClickHandler, self))

end
function KFZhanChangInfoView:ShowIndexCallBack()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.Kf_OneVOne_Prepare or scene_type == SceneType.Kf_PvP_Prepare then
		self:Close()
	end
	self:Flush()
end

function KFZhanChangInfoView:OnFlush()
	if nil == self.node_list.lbl_title then return end

	local desc = Language.Field1v1.ActivityOpenDes
	if self.panel_type == ACTIVITY_TYPE.KF_ONEVONE then
		desc = Language.Field1v1.ActivityOpenDes
	elseif self.panel_type == ACTIVITY_TYPE.KF_PVP then
		desc = Language.Field1v1.ActivityOpenDes_1
	elseif self.panel_type == ACTIVITY_TYPE.XIANMENGZHAN then
		desc = Language.Field1v1.ActivityOpenDes_2
	end
	self.node_list["boss_name_txt"].text.text = desc


	local name = Language.Field1v1.KF_1V1
	if self.panel_type == ACTIVITY_TYPE.KF_ONEVONE then
		name = Language.Field1v1.KF_1V1
	elseif self.panel_type == ACTIVITY_TYPE.KF_PVP then
		name = Language.Field1v1.KF_PVP
	elseif self.panel_type == ACTIVITY_TYPE.XIANMENGZHAN then
		name = Language.Field1v1.KF_GuildWar
	end
	self.node_list["lbl_title"].text.text = name

	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(self.panel_type)
	-- 图标
	if act_cfg and act_cfg.res_name and act_cfg.res_name ~= "" then
		self.node_list["icon_bg"]:SetActive(true)
		local bundle, asset = ResPath.GetF2MainUIImage(act_cfg.res_name)
		self.node_list["icon"].image:LoadSprite(bundle, asset, function()
			self.node_list["icon"].image:SetNativeSize()
		end)
	else
		self.node_list["icon_bg"]:SetActive(false)
	end
end

function KFZhanChangInfoView:ClickHandler()
	if self.panel_type == ACTIVITY_TYPE.XIANMENGZHAN then
		local list_data = GuildWGData.Instance:GetGuildBattleMatchInfoList()
		local is_guild_war = false
		local guild_id = RoleWGData.Instance.role_vo.guild_id
		if guild_id <= 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.RoleNotGuild)
			self:Close()
			return
		end

		for k,v in pairs(list_data) do
			for i=1,2 do
				if v[i].match_info[1].guild_id == guild_id or v[i].match_info[2].guild_id == guild_id then
					is_guild_war = true
				end
			end
		end

		if not is_guild_war then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Guild.CanNotWar)
			self:Close()
			return
		end

		ActivityWGCtrl.Instance:SendActivityEnterReq(ACTIVITY_TYPE.XIANMENGZHAN, 0)
		self:Close()
	else
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.Kf_OneVOne_Prepare then
			return
		end
		if self.panel_type == ACTIVITY_TYPE.KF_PVP then
			KF3V3WGCtrl.Instance:EnterPrepareScene()
			-- KF3V3WGCtrl.Instance:GoToNpc()
			self:Close()
			return
		end
		local send_protocol = ProtocolPool.Instance:GetProtocol(CSCrossStartReq)
		send_protocol.cross_activity_type = self.panel_type
		send_protocol:EncodeAndSend()
	end
	self:Close()
end

function KFZhanChangInfoView:SetShowData(panel_type)
	self.panel_type = panel_type
	if self:IsOpen() then
		self:Flush()
	else
		self:Open()
	end
end
