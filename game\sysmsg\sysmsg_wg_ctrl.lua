-- require("config/errorcode")

-- 系统消息
SysMsgWGCtrl = SysMsgWGCtrl or BaseClass(BaseWGCtrl)

function SysMsgWGCtrl:__init()
	if SysMsgWGCtrl.Instance then
		ErrorLog("[SysMsgWGCtrl] Attempt to create singleton twice!")
		return
	end
	SysMsgWGCtrl.Instance = self

	self:registerAllProtocols()

	self.tips_list = {}
	self.label_list = {}
	self.is_main_ui_create = false
	-- self.system_hint = SystemHint.New()
	self.act_timing_news = ActTimingNews.New()
	self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreate, self))
end

function SysMsgWGCtrl:__delete()
	-- self.system_hint:DeleteMe()
	-- self.system_hint = nil
	SysMsgWGCtrl.Instance = nil
	self.act_timing_news:DeleteMe()
	self.act_timing_news = nil
end

function SysMsgWGCtrl:registerAllProtocols()
	self:RegisterProtocol(SCGMCommand, "OnGMCommand")
	self:RegisterProtocol(CSGMCommand)
	self:RegisterProtocol(CSCrossGMCommand)
	self:RegisterProtocol(SCNoticeNumAck, "OnNoticeNumAck")
	self:RegisterProtocol(SCNoticeNumStr, "OnNoticeNumStr")

	self:RegisterProtocol(SCQTEInfo, "OnQTEInfo")
	self:RegisterProtocol(CSQTEReq)
end

function SysMsgWGCtrl:MainuiOpenCreate()
	self.is_main_ui_create = true
end

--QTE信息
function SysMsgWGCtrl:OnQTEInfo(protocol)

end

function SysMsgWGCtrl:RegisterErrNumCallback(err_num, callback_func)

end

function SysMsgWGCtrl:OnSysMsgCommon(protocol)

end

function SysMsgWGCtrl:OnGMCommand(protocol)
	Log("OnGMCommand type:" .. protocol.type .. "  result:" .. protocol.result)
end

function SysMsgWGCtrl.SendGmCommand(type, command)
	local protocol = ProtocolPool.Instance:GetProtocol(CSGMCommand)
	protocol.type = type
	protocol.command = command
	protocol:EncodeAndSend()
	Log("type:" .. type .. "  command:" .. command)
end

function SysMsgWGCtrl.SendCrossGmCommand(type, command)
	local protocol = ProtocolPool.Instance:GetProtocol(CSCrossGMCommand)
	protocol.type = type
	protocol.command = command
	protocol:EncodeAndSend()
	Log("type:" .. type .. "  command:" .. command)
end

function SysMsgWGCtrl:OnNoticeNumAck(protocol)
	if protocol.result == FIX_ERROR_CODE.EN_COIN_NOT_ENOUGH then
		self:ErrorRemind(Language.Common.NoCoin)
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = COMMON_CONSTS.VIRTUAL_ITEM_BIND_COIN})
		return
	end

	if protocol.result == FIX_ERROR_CODE.EN_GOLD_NOT_ENOUGH then			 --元宝不足
		UiInstanceMgr.Instance:ShowChongZhiView()
		return
	end

	local str = ErrorInfo[protocol.result]
	if nil == str then
		str = tostring(protocol.result)
	end
	Log("-----------系统提示：", str, protocol.result)
	self:ErrorRemind(str)
end

function SysMsgWGCtrl:OnNoticeNumStr(protocol)
	local result_code = tonumber(protocol.notice_numstr)
	if nil ~= result_code then
		local item_id = self:GetErrorTipItemId(result_code)
		if item_id ~= 0 then
			GlobalEventSystem:Fire(KnapsackEventType.KNAPSACK_LECK_ITEM, item_id)
			return
		end

		if result_code == FIX_ERROR_CODE.EN_GOLD_NOT_ENOUGH then			 --元宝不足
			UiInstanceMgr.Instance:ShowChongZhiView()
			return
		end
	end

	self:ErrorRemind(protocol.notice_numstr)
	Log("-----------lua系统提示：", protocol.notice_numstr)
end

function SysMsgWGCtrl:GetErrorTipItemId(code)
	local item_id = 0
	-- if code == FIX_ERROR_CODE.EN_COIN_NOT_ENOUGH then 			 				--铜币不足
	-- 	item_id = COMMON_CONSTS.VIRTUAL_ITEM_COIN
	if code == FIX_ERROR_CODE.EN_ROLE_ZHENQI_NOT_ENOUGH then 	 			--仙魂不足
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_XIANHUN
	-- elseif code == FIX_ERROR_CODE.EN_BIND_GOLD_NOT_ENOUGH then 					--绑定元宝不足
	-- 	item_id = COMMON_CONSTS.VIRTUAL_ITEM_BINDGOL
	elseif code == FIX_ERROR_CODE.EN_CONVERTSHOP_BATTLE_FIELD_HONOR_LESS then	--战场荣誉不足
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR
	elseif code == FIX_ERROR_CODE.EN_SHENGWANG_SHENGWANG_NOT_ENOUGH then	--竞技场声望不足
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_HORNOR
	elseif code == FIX_ERROR_CODE.EN_ROLE_HUNLI_NOT_ENOUGH then				-- 魂力不足
		item_id = COMMON_CONSTS.VIRTUAL_ITEM_HUNLI
	end
	return item_id
end

--提升飘字
function SysMsgWGCtrl:FloatingText(parent, str, x, y, scale, is_txt)
	-- self.system_hint:FloatingText(parent, str, x, y, scale, is_txt)
end

--右下角系统飘字
function SysMsgWGCtrl:FloatingLabel(str)
	-- self.system_hint:FloatingLabel(str)
end

--系统中上部飘字
function SysMsgWGCtrl:ErrorRemind(str)
	-- 用于给投放录视频用的，拦截飘字
	if MIANUI_VIEW_EDITOR_FLAG then
		return
	end

	if TipWGCtrl.Instance ~= nil then
		TipWGCtrl.Instance:ShowSystemMsg(str)
	end
end

--系统右边往左飘字
function SysMsgWGCtrl:RightNoticeRemind(str, role_level, direction, direction_x, direction_y)
	if str == nil or str == ""
		or role_level == nil or role_level == nil
		or direction == "" or direction == nil
		or direction_x == "" or direction_x == nil
		or direction_y == "" or direction_y == nil then
		return
	end

	if not self.is_main_ui_create then
		return
	end

	-- self.system_hint:RightNoticeFloatingLayoutText(str, role_level, direction, direction_x, direction_y)
end

--系统中上部滚动字 从右往左移(内容,优先级,类型)
function SysMsgWGCtrl:RollingEffect(str, priority, msg_type)
	-- 根据设置是否屏蔽传闻
	local role_level = RoleWGData.Instance.role_vo.level or 100 						--20级前不出现传闻
	if SettingWGCtrl.Instance:GetSystemSetting(SETTING_TYPE.ROLLING_EFFECT) or role_level <= 20 then
		return
	end

	if str == nil or nil == priority then
		return
	end
	-- self.system_hint:RollingEffect(str, priority, msg_type)
end

function SysMsgWGCtrl:FloatingCoustom(num, icon, num_path, icon_path)
	-- self.system_hint:FloatingRichText(num, icon, num_path, icon_path)
end
