PersonMaBiBossDisplaySceneLogic = PersonMaBiBossDisplaySceneLogic or BaseClass(CommonFbLogic)

function PersonMaBiBossDisplaySceneLogic:__init()

end

function PersonMaBiBossDisplaySceneLogic:Enter(old_scene_type, new_scene_type)
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskContents(false)
		MainuiWGCtrl.Instance:SetOtherContents(true)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.PersonMabiBoss.FBNane)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		PersonMaBiBossWGCtrl.Instance:OpenSceneView()
        GuajiWGCtrl.Instance:SetGuajiType(GuajiType.None)
	end)

	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
end

function PersonMaBiBossDisplaySceneLogic:Out(old_scene_type, new_scene_type)
	CommonFbLogic.Out(self)

    PersonMaBiBossWGCtrl.Instance:CloseSceneView()
    MainuiWGCtrl.Instance:SetTaskContents(true)
	-- MainuiWGCtrl.Instance:SetTaskTabButtonsNode(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetOtherContents(false)
	GuajiWGCtrl.Instance:ClearCurGuaJiInfo(true, false, true)
end

-- 