Npc = Npc or BaseClass(SceneObj)

Npc.Count = 0

function Npc:__init(vo)
	Npc.Count = Npc.Count + 1
	self.update_num = 0
	self.elapse_time = 0

	self.obj_type = SceneObjType.Npc
	self.draw_obj:SetObjType(self.obj_type)
	self.shield_obj_type = ShieldObjType.Npc
	self.need_calculate_priortiy = true

	self.last_task_index = -1
	self.select_effect = nil
	self.init_rotation = nil
	self.rotation_range = 10
	self.is_auto_rotation = false
	self.rotation_update_time = 0.3
	self.now_time = 0
	self.role_res = 0
	self.is_weapon_anim = false
	self.shadow_level = 2

	--冒泡显示时间
	self.max_bubble_time = 4
	self.bubble_time = 0
	self.bubble_interval = 0
	self.show_bubble = false

	self:GetUpdateNumber()
end

function Npc:__delete()
	Npc.Count = Npc.Count - 1

	if nil ~= self.select_effect then
		self.select_effect:DeleteMe()
		self.select_effect = nil
	end
	if self.time_quest then
		GlobalTimerQuest:CancelQuest(self.time_quest)
		self.time_quest = nil
	end

	if self.task_change then
		GlobalEventSystem:UnBind(self.task_change)
		self.task_change = nil
	end
	if self.task_effect then
		ResPoolMgr:Release(self.task_effect)
		self.task_effect = nil
	end

	if CountDownManager.Instance:HasCountDown("xmz_npc_task_icon_countdown") then
		CountDownManager.Instance:RemoveCountDown("xmz_npc_task_icon_countdown")
	end

	self:ClearTaskEvent()
	if self.check_wangqi_status_fun then
		GlobalEventSystem:UnBind(SceneEventType.CHANGE_ROLE_WANGQI_MODE_STATUS, self.check_wangqi_status_fun)
		self.check_wangqi_status_fun = nil
	end
	self:SetSceneWangqiStatus()
end

function Npc:InitInfo()
	SceneObj.InitInfo(self)

	local npc_config = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.vo.npc_id]
	if nil == npc_config then
		print_log("npc_config not find npc_id:" .. self.vo.npc_id)
		return
	end

	self.vo.name = npc_config.show_name
	self.res_id = npc_config.resid
	self.head_id = npc_config.headid
	self.obj_scale = npc_config.scale
	self.weapen_res = npc_config.weapen_res or 0
	self.mount_res = npc_config.mount_res or 0
	self.wing_res = npc_config.wing_res or 0
	self.halo_res = npc_config.halo_res or 0
	self.npc_icon_id = npc_config.npc_icon_id
	self.npc_title = npc_config.npc_title
	self.is_building = npc_config.is_building
	self.is_weapon_anim = npc_config.is_weapon_anim == 1
	self.sync_index = tonumber(npc_config.is_sync_action) or 0
	self.need_action = npc_config.need_action or 0

	if not self.task_change then
		self.task_change = GlobalEventSystem:Bind(OtherEventType.TASK_CHANGE,
			BindTool.Bind(self.OnTaskChange, self))
	end

	if TaskWGData.INJURED_NPC[self.vo.npc_id] and not self.task_change_handle then
		self.is_need_check_injueed = true
		self:CheckInjueed()
		self.task_change_handle = GlobalEventSystem:Bind(OtherEventType.TASK_INFO_CHANGE, BindTool.Bind(self.CheckInjueed, self))
	end

	self.check_wangqi_status_fun = BindTool.Bind(self.CheckSceneWangqiStatus, self)
	GlobalEventSystem:Bind(SceneEventType.CHANGE_ROLE_WANGQI_MODE_STATUS, self.check_wangqi_status_fun)
end

function Npc:IsWeaponOwnAnim()
    return self.is_weapon_anim
end

function Npc:InitAppearance()
	self:CheckSpecialStatus()

	self.load_priority = 10

	if self.obj_scale ~= nil then
		local transform = self.draw_obj:GetRoot().transform
		transform.localScale = Vector3(self.obj_scale, self.obj_scale, self.obj_scale)
	end

	if self.res_id ~= "" and self.res_id ~= 0 then
		local bundle, asset = ResPath.GetNpcModel(self.res_id)
		local sync_anim_type = self:IsWeaponOwnAnim() and SENCE_OBJ_WAIT_ANIM_SYNC_TYPE.TIAN_SHEN or nil
		self:ChangeObjWaitSyncAnimType(sync_anim_type)
		self:InitModel(bundle, asset, function()
			if HideEffectNPCList[self.vo.npc_id] then
				local ui_effect_obj = self.draw_obj:GetPart(SceneObjPart.Main).obj.gameObject.transform:FindHard("UIEffectObject")
				if ui_effect_obj and not IsNil(ui_effect_obj) then
					ui_effect_obj.gameObject:SetActive(false)
				else
					print_log("该模型在场景不显示特效, 找不到 UIEffectObject" , bundle, asset)
				end
			end
		end)

		if self:IsWeaponOwnAnim() then
			local weapon_bundle, weapon_asset = ResPath.GetNpcWeaponModel(self.res_id)
			self:ChangeModel(SceneObjPart.Weapon, weapon_bundle, weapon_asset)
		end
	end
	self.draw_obj:Rotate(0, self.vo.rotation_y or 0, 0)
end

function Npc:InitModel(bundle, asset, cb)
	self:ChangeModel(SceneObjPart.Main, bundle, asset, cb)
end

function Npc:OnEnterScene()
	SceneObj.OnEnterScene(self)
	self:GetFollowUi()
	if not self:IsBuilding() and self.res_id ~= "" and self.res_id ~= 0 then
	 	self:CreateShadow()
	end

	self:PlayAction()
	self:UpdateTitle()
	-- self:UpdateTaskEffect()
	self:UpdateNpcIcon()
	self:UpdateNpcTitle()
end

function Npc:GetUpdateNumber()
	self.update_num = Npc.Count
end

function Npc:Update(now_time, elapse_time)
	self.update_num = self.update_num - 1
	self.elapse_time = self.elapse_time + elapse_time
	if self.update_num > 0 then
		return
	end

	elapse_time = self.elapse_time
	self.elapse_time = 0
	self:GetUpdateNumber()

	SceneObj.Update(self, now_time, elapse_time)
	if self.now_time > now_time then
		return
	end

	self.now_time = self.now_time + self.rotation_update_time
	self:RangeToBubble(now_time, elapse_time)
	self:UpdataBubble(now_time, elapse_time)

	if not self.is_auto_rotation then
		return
	end

	local main_role = Scene.Instance:GetMainRole()
	local sub_pos = u3d.v2Sub(u3d.vec2(main_role:GetLogicPos()), u3d.vec2(self:GetLogicPos()))
	local distance = u3d.v2Length(sub_pos, true)
	if distance <= self.rotation_range then
	 	self:SetDirectionByXY(main_role:GetLogicPos())
	else
		self:SetRotation(self.init_rotation)
		self:AutoRotation(false)
	end
end

function Npc:IsNpc()
	return true
end

function Npc:GetObjKey()
	return self.vo.npc_id
end

function Npc:GetNpcId()
	return self.vo.npc_id
end

function Npc:GetNpcHead()
	return self.head_id
end

function Npc:CancelSelect()
	if SceneObj.select_obj == self then
		SceneObj.select_obj = nil
	end
	self.is_select = false
	-- SceneObj.CancelSelect(self)
	self.is_select = false
	-- if nil ~= self.select_effect then
	-- 	self.select_effect:SetActive(false)
	-- end
end

function Npc:IsSyncAction()
	return self.sync_index or 0
end


function Npc:PlayAction()
	if self.vo == nil then
		return
	end

	if 0 == self.need_action then
		return
	end

	local draw_obj = self:GetDrawObj()
	if draw_obj then
		if self.time_quest then
			GlobalTimerQuest:CancelQuest(self.time_quest)
			self.time_quest = nil
		end

		local sync_index = self:IsSyncAction()
		if sync_index ~= 0 then
			local async_npc_normalized = Scene.Instance:GetSyncAcitonNormalized(sync_index)
			local animator = self:GetAnimator()
			if animator and async_npc_normalized >= 0 then
				animator:Play(SceneObjAnimator.Rest, 0, async_npc_normalized)
			end
		end

		self.time_quest = GlobalTimerQuest:AddDelayTimer(function() self:PlayAction() end, 10)
	end

end

-- 获取Npc的Animator的播放进度
function Npc:GetAnimatorNormalized()
	local animtor = self:GetAnimator()
	local normalized = -1
	if animtor then
		local curAnimatorState = animtor:GetCurrentAnimatorStateInfo(0);
		normalized = curAnimatorState.normalizedTime;
	end
	return normalized
end

-- 获取Npc的Animtor
function Npc:GetAnimator()
	local draw_obj = self:GetDrawObj()
	local a_part = draw_obj:GetPart(SceneObjPart.Main)
	local part_obj = a_part:GetObj()
	if part_obj == nil then
		return nil
	end
	return part_obj.animator
end

function Npc:FlushTaskEffect(enable, bundle, asset)
	if self.task_effect then
		ResPoolMgr:Release(self.task_effect)
		self.task_effect = nil
	end

	if enable then
		ResPoolMgr:GetDynamicObjAsync(bundle, asset, function(obj)
			if not obj then
				return
			end

			local draw_obj = self:GetDrawObj()
			if not draw_obj then
				ResPoolMgr:Release(obj)
				return
			end

			local parent_transform = draw_obj:GetAttachPoint(AttachPoint.UI)
			if not parent_transform then
				ResPoolMgr:Release(obj)
				return
			end

			obj.transform:SetParent(parent_transform, false)
			self.task_effect = obj
		end)
	end
end

function Npc:ChangeTaskEffect(index)
	if self.last_task_index ~= index then
		self.last_task_index = index
		if index >= 0 then
			local bubble, asset = ResPath.GetTaskNpcEffect(index)
			self:FlushTaskEffect(true, bubble, asset)
		else
			self:FlushTaskEffect(false)
		end
	end
end

function Npc:UpdateTaskEffect()
	local task_cfg = TaskWGData.Instance:GetNpcOneExitsTask(self:GetNpcId())
	if task_cfg then
		local status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
		if status == GameEnum.TASK_STATUS_CAN_ACCEPT then
			local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
			if main_role_vo then
				local level = main_role_vo.level
				if task_cfg.min_level > level then
					status = 0
				end
			end
		end
		self:ChangeTaskEffect(status)
	else
		self:ChangeTaskEffect(-1)
	end
end

-- 设置NPC头顶特效
function Npc:ChangeSpecailTitle(index)
	if self.last_task_index ~= index then
		self.last_task_index = index
		if index >= 0 then
			-- local str = "task_" .. index
			-- local bubble, asset = ResPath.GetTitleModel(str)
			-- self:GetFollowUi():ChangeTitle(bubble, asset, 0, 60)
		else
			self:GetFollowUi():ChangeTitle(nil)
		end
	end
end

-- 设置任务状态
function Npc:SetTaskState(state)
	self.task_state = state
	if nil ~= self.follow_ui then
		local bundle, sp
		if state == GameEnum.TASK_STATUS_CAN_ACCEPT then
			bundle, sp = ResPath.GetEffectUi("UI_gantanhao")
		elseif state == GameEnum.TASK_STATUS_COMMIT then
			bundle, sp = ResPath.GetEffectUi("UI_wenhao")
		end
		self.follow_ui:SetTaskIcon(bundle, sp)
	end
end


function Npc:SetXMZNpcTaskIcon()
	local scene_type = Scene.Instance:GetSceneType()
	local first_enter_scene = GuildBattleRankedWGData.Instance:GetFirstEnterScene()
	if scene_type == SceneType.XianMengzhan and first_enter_scene then
		local role_info_list = GuildBattleRankedWGData.Instance:GetGuildBattleSceneUserInfo()
		local side = role_info_list.side
		local npc_id = GuildBattleRankedWGData.Instance:GetGuildNpcId(side)
		if npc_id == self:GetNpcId() then
			local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_XIANMENGZHAN)
			local server_time = TimeWGCtrl.Instance:GetServerTime()
			local next_time = act_info and act_info.next_time or 0
			-- local is_standy = act_info and act_info.status == ACTIVITY_STATUS.STANDY
			-- local xiusai_state = GuildWGData.Instance:GetIsPrepareRestTime()
			-- local is_show = is_standy or xiusai_state
			local is_show = true
			if is_show then
				GuildBattleRankedWGData.Instance:SetFirstEnterScene(false)
				local bundle, sp = ResPath.GetEffectUi("UI_JianTou")
				if self.follow_ui then
					self.follow_ui:SetTaskIcon(bundle, sp)
				end
				if CountDownManager.Instance:HasCountDown("xmz_npc_task_icon_countdown") then
					CountDownManager.Instance:RemoveCountDown("xmz_npc_task_icon_countdown")
				end
				CountDownManager.Instance:AddCountDown("xmz_npc_task_icon_countdown", BindTool.Bind(self.UpdateXMZNpcTaskIconTimer,self), BindTool.Bind(self.XMZNpcTaskIconTimerCallBack,self), nil, 30, 1)
			else
				if CountDownManager.Instance:HasCountDown("xmz_npc_task_icon_countdown") then
					CountDownManager.Instance:RemoveCountDown("xmz_npc_task_icon_countdown")
				end
				self:ClearTaskIcon()
			end
		end
	end
end

function Npc:UpdateXMZNpcTaskIconTimer(now_time, elapse_time)

end

function Npc:XMZNpcTaskIconTimerCallBack()
	self:ClearTaskIcon()
end

function Npc:ClearTaskIcon()
	if self.follow_ui then
		self.follow_ui:SetTaskIcon(nil,nil)
	end
end

function Npc:CreateFollowUi()
	self.follow_ui = NpcFollow.New(self.vo)
	self.follow_ui:SetOwnerObj(self)
	self.follow_ui:OnEnterScene(self.is_enter_scene)
	self.follow_ui:Create(SceneObjType.Npc)
	if self.draw_obj then
		self.follow_ui:SetFollowTarget(self.draw_obj.root.transform, self.draw_obj:GetName())
	end
	local status = TaskWGData.Instance:GetNpcTaskStatus(self:GetNpcId())
	self:SetTaskState(status)
	--仙盟战 npc头顶任务特效
	self:SetXMZNpcTaskIcon()
end

function Npc:UpdateTitle()
	local task_cfg = TaskWGData.Instance:GetNpcOneExitsTask(self:GetNpcId())
	if task_cfg then
		local status = TaskWGData.Instance:GetTaskStatus(task_cfg.task_id)
		if status == GameEnum.TASK_STATUS_CAN_ACCEPT then
			if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
				-- status = SPECIAL_TASK_STATUS.DALIY_TASK_CAN_ACCEPT
			end
			local main_role_vo = GameVoManager.Instance:GetMainRoleVo()
			if main_role_vo then
				local level = main_role_vo.level
				if task_cfg.min_level > level then
					status = 0
				end
			end
		end

		if status == GameEnum.TASK_STATUS_COMMIT or status == GameEnum.TASK_STATUS_ACCEPT_PROCESS then
			if task_cfg.task_type == GameEnum.TASK_TYPE_RI then
				-- status = SPECIAL_TASK_STATUS.DALIY_TASK_COMMIT
			end
		end

		-- if task_cfg.task_aim then
		-- 	if task_cfg.task_aim == 1 then
		-- 		if task_cfg.task_type == TASK_TYPE.CITAN or task_cfg.task_type == TASK_TYPE.BANZHUAN then
		-- 			status = SPECIAL_TASK_STATUS.CAMP_WAR_COMMIT
		-- 		end
		-- 	elseif task_cfg.task_aim == 2 then
		-- 		if task_cfg.task_type == TASK_TYPE.CITAN or task_cfg.task_type == TASK_TYPE.BANZHUAN then
		-- 			status = SPECIAL_TASK_STATUS.CAMP_WAR_CAN_ACCEPT
		-- 		end
		-- 	end
		-- end

		self:ChangeSpecailTitle(status)
	else
		self:ChangeSpecailTitle(-1)
	end
end

function Npc:OnTaskChange()
	self:UpdateTitle()
	-- self:UpdateTaskEffect()
end

local npc_bubble_list = {}
function Npc:RangeToBubble(now_time, elapse_time)
	if 0 ~= self.bubble_time then
		return
	end

	local npc_id = self:GetNpcId()
	local bubble_list = RoleWGData.Instance:GetBubbleNpcList()
	local cfg = bubble_list[npc_id]
	if cfg == nil then
		return
	end

	local talk_list = nil
	for k,v in pairs(cfg) do
		local flag = self:GetRangeBubbleFlag(v)
		if flag then
			local main_role = Scene.Instance:GetMainRole()
			local px, py = main_role:GetLogicPos()
			local nx, ny = self:GetLogicPos()
			local dis = GameMath.GetDistance(px, py, nx, ny, true)
			local is_move_in = dis <= v.param3
			if is_move_in ~= self.is_move_in then
				self.is_move_in = is_move_in
				if is_move_in then
					npc_bubble_list[npc_id] = npc_bubble_list[npc_id] and npc_bubble_list[npc_id] + 1 or 1
					if npc_bubble_list[npc_id] > v.max_num then
						return
					end
					talk_list = talk_list or {}
					table.insert(talk_list, v)
				end
			end
		end
	end

	self:SetBubbleText(talk_list, now_time)
end

function Npc:GetRangeBubbleFlag(cfg)
	if cfg.bubble_type == TaskBubbleType.Type2 then
		return true
	end

	if cfg.bubble_type ~= TaskBubbleType.Type3 then
		return false
	end

	local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_ZHU)
	local task_id = task_list[1]
	if not task_id then
		local task_cfg = TaskWGData.Instance:GetNextZhuTaskConfig()
		task_id = task_cfg and task_cfg.task_id
	end
	local status_type = TaskWGData.Instance:GetTaskStatusType(task_id)
	if task_id then
		if task_id > cfg.param1 then
			return true
		elseif cfg.param1 == task_id and cfg.param2 <= status_type then
			return true
		end
	end

	return false
end

function Npc:TaskToBubble(task_id, status_type)
	-- body
	if 0 ~= self.bubble_time then return end
	local talk_list = {}
	local bubble_list = RoleWGData.Instance:GetBubbleNpcList()
	local cfg = bubble_list[self:GetNpcId()]
	if not cfg then
		return
	end
	for k,v in pairs(cfg) do
		if v.bubble_type == TaskBubbleType.Type1 and v.param1 == task_id and v.param2 == status_type then
			table.insert(talk_list, v)
		end
	end

	self:SetBubbleText(talk_list, self.now_time)
end

function Npc:SetBubbleText(list, now_time)
	if nil == list or IsEmptyTable(list) then
		return
	end

	SortTools.SortAsc(list, "sort")
	local cfg = list[1]
	local text_index_list = string.split(cfg.bubble_npc_text, "|")
	local index = math.random(1, #text_index_list)
	index = tonumber(text_index_list[index])

	self:GetFollowUi():ChangeBubble(RoleWGData.Instance.bubble_talk_list[index].bubble_npc_text)
	self.bubble_time = now_time + self.max_bubble_time
	self.show_bubble = true
end

function Npc:UpdataBubble(now_time, elapse_time)
	if 0 == self.bubble_time then return end
	if now_time < self.bubble_time then return end
	if self.show_bubble then
		self:GetFollowUi():HideBubble()
		self.bubble_time = now_time + self.bubble_interval
		self.show_bubble = false
	else
		self.bubble_time = 0
	end
end

-- 关闭NPC对话面板时，一段距离内需要面朝玩家
function Npc:AutoRotation(value)
	-- body
	if nil == value then
		return self.is_auto_rotation
	end

	if self:IsBuilding() then return end
	self.is_auto_rotation = value
end

function Npc:OnModelLoaded(part, obj)
	SceneObj.OnModelLoaded(self, part, obj)
	if SceneObjPart.Main == part then
		self:PlayAction()
	end

	-- 设置望气
	self:CheckSceneWangqiStatus()
	-- 之前就屏蔽了的
	-- if SceneObjPart.Main == part then
	-- 	self.init_rotation = self:GetRotation()
	-- 	obj.gameObject.transform:SetLocalScale(self.obj_scale, self.obj_scale, self.obj_scale)
	-- elseif SceneObjPart.Weapon == part and self:IsWeaponOwnAnim() then
	-- 	obj.gameObject.transform:SetLocalScale(self.obj_scale, self.obj_scale, self.obj_scale)
	-- end
end

function Npc:CheckInjueed(task_event_type, task_id)
	if not self.is_need_check_injueed then
		return
	end

	local draw_obj = self:GetDrawObj()
	if draw_obj then
		local part = draw_obj:GetPart(SceneObjPart.Main)
		if part then
			local behurt = not TaskWGData.Instance:GetTaskIsCanCommint(TaskWGData.INJURED_NPC[self:GetNpcId()]) and not TaskWGData.Instance:GetTaskIsCompleted(TaskWGData.INJURED_NPC[self:GetNpcId()])
			part:SetBool("hurt", behurt)
			if TaskWGData.Instance:GetTaskIsCompleted(TaskWGData.INJURED_NPC[self:GetNpcId()]) then
				self.is_need_check_injueed = false
			end

			if behurt and self.res_id == TaskWGData.BIND_NPC then
				if not self.bind_loader then
					local bundle, asset = ResPath.GetEffect("Effect_kunbang")
					self.bind_loader = AllocAsyncLoader(self, "bind_loader")
					self.bind_loader:SetParent(draw_obj:GetRoot().transform)
					self.bind_loader:Load(bundle, asset, function(obj)
					end)
				end
			elseif self.bind_loader then
				self.bind_loader:Destroy()
				self.bind_loader = nil
			end
		end
	end
end

function Npc:ClearTaskEvent()
	if self.task_change_handle then
		GlobalEventSystem:UnBind(self.task_change_handle)
		self.task_change_handle = nil
	end
end

function Npc:IsWalkNpc()
	return false
end

function Npc:UpdateNpcIcon()
	local show_npc_icon_id = self.npc_icon_id or 0
	local status = show_npc_icon_id > 0
	self:GetFollowUi():SetNPCIcon(show_npc_icon_id, status)
end

function Npc:UpdateNpcTitle()
	local is_show = self.npc_title and self.npc_title ~= ""
	self:GetFollowUi():SetNPCTitle(self.npc_title, is_show)
end

function Npc:IsBuilding()
	return 1 == self.is_building
end

local temp_vector = u3d.vec3(0, 0, 0)
function Npc:CalculatePriortiy()
	if SceneObj.select_obj == self then
		return SceneAppearPriority.High
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		if u3dpool.v2Length(u3dpool.v2Sub(main_role.logic_pos, self.logic_pos, temp_vector), false) <= 15 * 15 then
			return SceneAppearPriority.High
		else
			return SceneAppearPriority.Middle
		end
	end

	return SceneAppearPriority.Middle
end

function Npc:CheckSpecialStatus()
	if self.vo == nil then
		return
	end

	if OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_TASK_CHAIN) then
		local day_index_cfg = OperationTaskChainWGData.Instance:GetCurDayIndexCfg()
		if day_index_cfg ~= nil then
			local task_chain_cfg = OperationTaskChainWGData.Instance:GetTaskChainCfg(day_index_cfg.task_chain_id)
	    	if task_chain_cfg ~= nil then
	    		if self.vo.npc_id == task_chain_cfg.npc_id then
	    			self.res_id = task_chain_cfg.res_id
	    			self.vo.name = task_chain_cfg.npc_name

	    			local follow_ui = self:GetFollowUi()
	    			if follow_ui ~= nil then
	    				follow_ui:SetName(task_chain_cfg.npc_name)
	    			end
	    		end
	    	end
		end
	end
end

function Npc:CheckModeAndName(res_id, name, is_reset)
	if self:IsDeleted() then
		return
	end

	if is_reset then
		local npc_config = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.vo.npc_id]
		if npc_config == nil then
			return
		end

		res_id = npc_config.resid
		name = npc_config.show_name
	end

	if res_id == nil or res_id == "" or name == nil then
		return
	end

	if self.res_id ~= res_id then
		self.res_id = res_id

		local bundle, asset = ResPath.GetNpcModel(self.res_id)
		self:InitModel(bundle, asset, function()
			if HideEffectNPCList[self.vo.npc_id] then
				local ui_effect_obj = self.draw_obj:GetPart(SceneObjPart.Main).obj.gameObject.transform:FindHard("UIEffectObject")
				if ui_effect_obj and not IsNil(ui_effect_obj) then
					ui_effect_obj.gameObject:SetActive(false)
				else
					print_log("该模型在场景不显示特效, 找不到 UIEffectObject" , bundle, asset)
				end
			end
		end)
	end

	self.vo.name = name
	local follow_ui = self:GetFollowUi()
	if follow_ui ~= nil then
		follow_ui:SetName(name)
	end
end

-- 望气展示效果
function Npc:CheckSceneWangqiStatus(is_wangqi)
	-- 是否望气高亮
	if self.vo == nil then
		return
	end

	local npc_config = ConfigManager.Instance:GetAutoConfig("npc_auto").npc_list[self.vo.npc_id]
	if npc_config == nil then
		return
	end

	local is_now_wangqi = is_wangqi or Scene.Instance:IsEnterWangQiStatus()
	-- 是否望气高亮
	if npc_config.is_wangqi_effect == 1 then
		self:SetSceneWangqiStatus(nil, is_now_wangqi)
	end
end

-- 重置物体的layer层
local GameFeatureLayer_Layer = 6
function Npc:SetSceneWangqiStatus(is_reset, is_wangqi)
	local draw_obj = self:GetDrawObj()
	local part = draw_obj:GetPart(SceneObjPart.Main)
	if part then
		if is_reset or (not is_wangqi) then
			part:TrySetMainDefaultGameObjectLayer()
		else
			-- local InteractableLayer = UnityEngine.LayerMask.NameToLayer("GameFeatureLayer")
			part:TrySetMainGameObjectLayer(GameFeatureLayer_Layer)
		end
	end
end