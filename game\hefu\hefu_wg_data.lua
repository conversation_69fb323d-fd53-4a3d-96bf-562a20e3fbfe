
HeFuWGData = HeFuWGData or BaseClass()
function HeFuWGData:__init()
    if nil ~= HeFuWGData.Instance then
        ErrorLog("[HeFuWGData]:Attempt to create singleton twice!")
    end
    HeFuWGData.Instance = self

    self.hefu_info = {
        merge_server_time = -1,
        server_id1 = 0,
        server_id2 = 0,
        reward_status = 0,
    }

    RemindManager.Instance:Register(RemindName.HeFuNotice, BindTool.Bind(self.IsShowHeFuRemind, self))

    self.level_limit = ConfigManager.Instance:GetAutoConfig("combineserver_notice_auto").reward[1].level_limit
end

function HeFuWGData:__delete()
    RemindManager.Instance:UnRegister(RemindName.HeFuNotice)

    HeFuWGData.Instance = nil
end

function HeFuWGData:SetHeFuInfo(protocol)
    self.hefu_info.merge_server_time = protocol.merge_server_time
    self.hefu_info.server_id1 = protocol.server_id1
    self.hefu_info.server_id2 = protocol.server_id2
    self.hefu_info.reward_status = protocol.reward_status
end

function HeFuWGData:GetHeFuInfo()
    return self.hefu_info
end

function HeFuWGData:IsShowHeFuRemind()
    --今日没提醒过
    if not self:GetTodayIsReminded() then
        return 1
    end

    --奖励还未领取
    if self:GetIsCanFetchReward() then
        return 1
    end

    return 0
end

function HeFuWGData:HasFetchReward()
    return self.hefu_info.reward_status == 1
end

function HeFuWGData:GetIsCanFetchReward()
    if self:HasFetchReward() then
        return false
    end

    --还没有合服，不可领取奖励
    if not self:GetIsCombineServer() then
        return false
    end

    return true
end

--本地保存今天提醒状态
function HeFuWGData:CacheTodayIsReminded()
    local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local name = uid .."HeFuNotice"
	PlayerPrefsUtil.SetString(name, cur_day)
end

--今日是否提醒过
function HeFuWGData:GetTodayIsReminded()
	local cur_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	local uid = RoleWGData.Instance:InCrossGetOriginUid()
	local name = uid.."HeFuNotice"
	local remind_day = PlayerPrefsUtil.GetString(name) or 0
	return tonumber(remind_day) == cur_day
end

--获取奖励列表
function HeFuWGData:GetRewardList()
    local reward_cfg = ConfigManager.Instance:GetAutoConfig("combineserver_notice_auto").reward[1].reward_item
    local data_list = {}
    for i = 0, #reward_cfg do
        table.insert(data_list, reward_cfg[i])
    end
    return data_list
end

--是否已经合服
function HeFuWGData:GetIsCombineServer()
    local server_real_combine_time = TimeWGCtrl.Instance:GetServerRealCombineTime()
    return TimeWGCtrl.Instance:GetServerTime() > server_real_combine_time
end

--默认值说明不合服。
function HeFuWGData:GetIsNotOpenHeFu()
    return self.hefu_info.merge_server_time <= 0
end

function HeFuWGData:IsLevelConditionOK()
    --print_error(self.level_limit)
    return self.level_limit <= GameVoManager.Instance:GetMainRoleVo().level
end