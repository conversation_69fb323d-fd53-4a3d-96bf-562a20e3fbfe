require("game/bootybay/bootybay_wg_data")
require("game/bootybay/bootybay_scene_follow_view")
require("game/bootybay/bootybay_fb_view")
require("game/bootybay/team_bootybay_fb_view")
require("game/bootybay/bootybay_fb_ready_view")
require("game/bootybay/bootybay_reward_tips")
require("game/bootybay/bootybay_team_baoming_view")
require("game/bootybay/bootybay_pre_view")

BootyBayWGCtrl = BootyBayWGCtrl or BaseClass(BaseWGCtrl)
local SPECIAL_EVENT_ID_1 = 1
local SPECIAL_EVENT_ID_2 = 5
local SPECIAL_EVENT_ID_3 = 7
local SPECIAL_EVENT_ID_6 = 6

local SPECIAL_EVENT_ID = {
		[SPECIAL_EVENT_ID_1] = true,
		[SPECIAL_EVENT_ID_2] = true,
		[SPECIAL_EVENT_ID_3] = true,
	}

function BootyBayWGCtrl:__init()
	if BootyBayWGCtrl.Instance then
		print_error("BootyBayWGCtrl:Attempt to create singleton twice!")
	end
	BootyBayWGCtrl.Instance = self

	self.bootybay_scene_follow_view = BootyBaySceneFollowView.New(GuideModuleName.BootyBaySceneFollowView)						  --藏宝湾场景信息界面
	self.bootybay_fb_view = BootyBayFBView.New(GuideModuleName.BootyBayFBView)													  --藏宝湾单人副本内的信息界面
	self.team_bootybay_fb_view = TeamBootyBayFBView.New(GuideModuleName.TeamBootyBayFBView)										  --藏宝湾组队副本内的信息界面
	self.bootybay_fb_ready_view = BootyBayFBReadyView.New(GuideModuleName.BootyBayFBReadyView)								  	  --藏宝湾组队准备界面
	self.bootybay_team_baoming_view = BootybayTeamBaoMingView.New(GuideModuleName.BootybayTeamBaoMingView)						  --藏宝湾组队平台界面
	self.bootybay_reward_tips = BootyBayRewardTips.New(GuideModuleName.BootyBayRewardTips)										  --藏宝湾任务奖励界面
	self.bootybay_pre_view = BootyBayPreView.New(GuideModuleName.BootyBayPreView)												  --藏宝湾
	self.data = BootyBayWGData.New()

	self.frist_wabao = true
	self:RegisterAllProtocals()
	self.is_from_bootybay = false
	--掉落物拾取回调
	self.pick_item_callback = GlobalEventSystem:Bind(OtherEventType.PICK_ITEM_EVENT, BindTool.Bind(self.OnPickItemEvent,self))
	self.touch_callback = GlobalEventSystem:Bind(LayerEventType.TOUCH_MOVED, BindTool.Bind(self.SetAutoWaBaoFlag,self,false))

	self.auto_wabao_flag = false
end

function BootyBayWGCtrl:__delete()
	if self.data then
		self.data:DeleteMe()
		self.data = nil
	end

	self.bootybay_reward_tips:DeleteMe()
	self.bootybay_team_baoming_view:DeleteMe()
	self.bootybay_fb_ready_view:DeleteMe()
	self.team_bootybay_fb_view:DeleteMe()
	self.bootybay_fb_view:DeleteMe()
	self.bootybay_scene_follow_view:DeleteMe()

	self.bootybay_pre_view:DeleteMe()
	self.bootybay_pre_view = nil

	if self.pick_item_callback then
		GlobalEventSystem:UnBind(self.pick_item_callback)
		self.pick_item_callback = nil
	end

	if self.touch_callback then
		GlobalEventSystem:UnBind(self.touch_callback)
		self.touch_callback = nil
	end

	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end
	if self.view then
		self.view:DeleteMe()
		self.view = nil
	end

	if self.delay_goto_wabao then
		GlobalTimerQuest:CancelQuest(self.delay_goto_wabao)
		self.delay_goto_wabao = nil
	end

	BootyBayWGCtrl.Instance = nil
end

-- 注册协议
function BootyBayWGCtrl:RegisterAllProtocals()
	self:RegisterProtocol(CSWabaoOpera)											--5270
	self:RegisterProtocol(SCWabaoPos, "OnSCWabaoPos")							--5271
	self:RegisterProtocol(SCWabaoEvent, "OnSCWabaoEvent")						--5272
	self:RegisterProtocol(SCWabaoInfo, "OnSCWabaoInfo")							--5273
	self:RegisterProtocol(SCWabaoPersonFBInfo, "OnSCWabaoPersonFBInfo")			--5274
	self:RegisterProtocol(SCWabaoTeamFBInfo, "OnSCWabaoTeamFBInfo")				--5275
	self:RegisterProtocol(SCWabaoFinishAllTask, "OnSCWabaoFinishAllTask")		--5276

	self:RegisterProtocol(SCWabaoTeamFBOpen, "OnSCWabaoTeamFBOpen")				--5277
	self:RegisterProtocol(SCWaBaoMonsterDieNotice, "OnSCWaBaoMonsterDieNotice")	--5278

end

function BootyBayWGCtrl:SetAutoWaBaoFlag(auto_wabao_flag)
	self.auto_wabao_flag = auto_wabao_flag
end

function BootyBayWGCtrl:GetAutoWaBaoFlag()
	return self.auto_wabao_flag
end

function BootyBayWGCtrl:SendCSWabaoOpera(opera_type, param1, param2)
	local protocol = ProtocolPool.Instance:GetProtocol(CSWabaoOpera)
	protocol.opera_type = opera_type or 0
	protocol.param1 = param1 or 0
	protocol.param2 = param2 or 0
	protocol:EncodeAndSend()
end

function BootyBayWGCtrl:OnSCWabaoPos(protocol)
	--print_error("onscwabaopos", protocol)
	self.data:SetWaBaoPosInfo(protocol)
	local treasure_item_list = protocol.treasure_item_list
	if not self.frist_wabao then
		self:MoveToWaBaoPos(treasure_item_list[self.data.wabao_type])
	end
end

--移动到挖宝的地点
function BootyBayWGCtrl:MoveToWaBaoPos(treasure_info)
	local scene_id = Scene.Instance:GetSceneId()
	self:SetAutoWaBaoFlag(true)
	--到达宝藏位置的回调
	local gather_time = 2.5
	local move_to_pos_call_back = function ()
		local is_wabao = true
		GatherBar.Instance:SetGatherTime(gather_time, is_wabao) 															--界面采集动画时长
		GatherBar.Instance:Open()
		GlobalEventSystem:Fire(ObjectEventType.START_GATHER) 								--播放界面采集动画
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)		--更改寻路状态
		local main_vo = Scene.Instance:GetMainRole()
		if main_vo then
			main_vo:SetIsGatherState(true, 0) --播放模型采集动画
		end
		GlobalTimerQuest:AddDelayTimer(function ()
			if self.data and not main_vo:IsDeleted() then
				self:SendCSWabaoOpera(WABAO_OP.WABAO_OP_DO_WABAO, self.data.wabao_type)
				main_vo:SetIsGatherState(false, 0) --播放模型采集动画
			end
		end, gather_time)
	end

	if scene_id == treasure_info.scene_id and treasure_info.has_treasure == 1 then
		GuajiWGCtrl.Instance:SetMoveToPosCallBack(move_to_pos_call_back )
		GuajiWGCtrl.Instance:MoveToPos(treasure_info.scene_id, treasure_info.x, treasure_info.y, 1, nil, treasure_info.scene_key,nil,move_to_pos_call_back)
		GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.AutoWaBao)

	end
end

function BootyBayWGCtrl:OnSCWabaoEvent(protocol)
	local main_vo = Scene.Instance:GetMainRole()
	if main_vo then
		main_vo:ShowBubble(protocol.event_id)
	end
	GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)

	if protocol.wabao_type == WABAO_TYPE.WABAO_TYPE_BAOTU then 						--宝图挖宝
		-- 7:组队副本-藏宝湾 5:出现小怪 1:单人副本-藏宝湾	--这三种情况不再继续自动使用宝图
		if SPECIAL_EVENT_ID_6 == protocol.event_id then
			local use_baotu_info = self.data:GetUseBaoTuInfo()
			if not IsEmptyTable(use_baotu_info) then
				local stuff_num = ItemWGData.Instance:GetItemNumInBagById(use_baotu_info.item_id)
				if stuff_num > 0 then
					--继续使用宝图挖宝
					-- GlobalTimerQuest:AddDelayTimer(function ()
						BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_USE_BAOTU,use_baotu_info.quality)		--请求生成宝藏点
					-- end, 2)
				end
			end
		else
			GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)		--更改寻路状态
			if protocol.event_id == SPECIAL_EVENT_ID_3 then 		--7:组队副本-藏宝湾
				local is_in_team = SocietyWGData.Instance:GetIsInTeam()
				local team_count = SocietyWGData.Instance:GetTeamMemberCount()
				if is_in_team ~= 1 then						--不在队伍中创建队伍
					local min_level, max_level = NewTeamWGData.Instance:GetTeamLimitLevel()
    				NewTeamWGCtrl.Instance:SendCreateTeam(0, 0, min_level, max_level)
    				--不在队伍中需要自己请求进入队伍
					--FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_WABAO_TEAM_FB)
				end
				--ViewManager.Instance:Open(GuideModuleName.BootybayTeamBaoMingView)
				self:OnClickEnter()
			end
		end
	elseif protocol.wabao_type == WABAO_TYPE.WABAO_TYPE_TASK then 					--任务挖宝
		local wabao_times = BootyBayWGData.Instance:GettDailyWaBaoRemainCount()
		local is_accepted = BootyBayWGData.Instance:GetIsAcceptWaBaoTask()
		local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
		if SPECIAL_EVENT_ID_6 == protocol.event_id 
			or WABAO_EVENT_TYPE.WABAO_EVENT_ENTET_REWARD_MONEY == protocol.event_type
			 or WABAO_EVENT_TYPE.WABAO_EVENT_ENTET_NOTHING == protocol.event_type then
			-- 已接取过任务，还有剩余次数
			if is_accepted and other_cfg.task_wabao_times - wabao_times > 0 then
				--继续请求任务挖宝
				-- GlobalTimerQuest:AddDelayTimer(function ()
					--请求生成任务宝藏点
					BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_DO_TASK)
				-- end, 2)
			end
		elseif WABAO_EVENT_TYPE.WABAO_EVENT_ENTET_REWARD_ITEM == protocol.event_type then
			local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem) or {}
			local main_role = Scene.Instance:GetMainRole()
			local fall_count = 0
			for k, v in pairs(fall_item_list) do
				if v:GetVo().owner_role_id > 0 and v:GetVo().owner_role_id == main_role:GetRoleId() then
					fall_count = fall_count + 1
				end
			end
			if fall_count <= 1 then
				if is_accepted and other_cfg.task_wabao_times - wabao_times > 0 then
					self.delay_goto_wabao = GlobalTimerQuest:AddDelayTimer(function ()
						if self.auto_wabao_flag then
							BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_DO_TASK)
						end
						GlobalTimerQuest:CancelQuest(self.delay_goto_wabao)
						self.delay_goto_wabao = nil
					end, 3.5)
				end
			end
		else
			GlobalEventSystem:Fire(ObjectEventType.MAIN_ROLE_AUTO_XUNLU, XunLuStatus.None)		--更改寻路状态
		end
	end
end

function BootyBayWGCtrl:OnClickEnter()
	--单人给提示
	--多人弹准备界面
	local member_count = SocietyWGData.Instance:GetTeamMemberCount()
	local enter_teamfb_times = BootyBayWGData.Instance:GetEnterTeamFBTimes()
	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	local role_id = RoleWGData.Instance:GetRoleVo().role_id
	if (enter_teamfb_times - other_cfg.team_fb_enter_times_limit) == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.NewTeam.NotTimesEnter)
		return
	end
	if member_count <= 1 then
		--local ok_func = function ()
			FuBenWGCtrl.Instance:SendEnterFB(FUBEN_TYPE.FBCT_WABAO_TEAM_FB)
		--end
		--TipWGCtrl.Instance:OpenAlertTips(Language.BootyBay.TeamDesc,ok_func)
		return
	end

	if member_count > 1 and member_count < 3 then
		--local ok_func = function ()
			BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_QUERY_TEAM_FB_OPEN_INFO, role_id, 0)
		--end
		--TipWGCtrl.Instance:OpenAlertTips(Language.BootyBay.TeamDesc,ok_func)
		return
	end

	BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_QUERY_TEAM_FB_OPEN_INFO, role_id, 0)
end

--请求宝图挖宝
function BootyBayWGCtrl:SendBaoTuWaBao(item_data, quick_use)
    if item_data and item_data.item_id then
        local stuff_num = ItemWGData.Instance:GetItemNumInBagById(item_data.item_id)
        --判断消耗品够不够
        if stuff_num > 0 then
            if quick_use and item_data.quality == BootyBaySceneFollowView.MaxColor then
                BootyBayWGData.Instance:SetNowWabaoInfo(item_data)
                ViewManager.Instance:Open(GuideModuleName.BootyBayPreView)
                return
            end


            local wabao_pos_info = BootyBayWGData.Instance:GetWaBaoPosInfo()[WABAO_TYPE.WABAO_TYPE_BAOTU]
            BootyBayWGCtrl.Instance.frist_wabao = false
            --设置这次的挖宝类型
            BootyBayWGData.Instance:SetWaBaoType(WABAO_TYPE.WABAO_TYPE_BAOTU)
            --获取上次使用的消耗品信息
            local use_baotu_info = self.data:GetUseBaoTuInfo()
            if use_baotu_info and use_baotu_info.quality == item_data.quality then
                --判断是否已经生成宝藏点
                if wabao_pos_info.has_treasure == 1 then
                    BootyBayWGCtrl.Instance:MoveToWaBaoPos(wabao_pos_info)
                    BootyBayWGData.Instance:SetUseBaoTuInfo(item_data)
                    self:FlushBootybaySceneFollowView()
                    return
                end
            end
            BootyBayWGData.Instance:SetUseBaoTuInfo(item_data)
            self:FlushBootybaySceneFollowView()
            --请求生成宝藏点
            BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_USE_BAOTU,item_data.quality)

        else
            TipWGCtrl.Instance:OpenItemTipGetWay({item_id = item_data.item_id})
        end
	end
end

function BootyBayWGCtrl:SendAgainFunc()
    if self.data:GetIsShowAgainBtn() then
        self:SendCSWabaoOpera(WABAO_OP.WABAO_OP_CONTINUE_CHALLENGE)
        Scene.Instance:SimulationSceneLoad()
    end
end

--请求任务挖宝
function BootyBayWGCtrl:SendTaskWaBao()
	local wabao_times = BootyBayWGData.Instance:GettDailyWaBaoRemainCount()
	local is_accepted = BootyBayWGData.Instance:GetIsAcceptWaBaoTask()
	local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
	local bootybay_scene_id = BootyBayWGData.Instance:GetBootyBaySceneId()
	local scene_id = Scene.Instance:GetSceneId()
	if not is_accepted then
		local scene_key = RoleWGData.Instance.role_vo.scene_key
		local scene_cfg = ConfigManager.Instance:GetSceneConfig(bootybay_scene_id)
		local scene_npc_cfg = {}
        if scene_cfg ~= nil and scene_cfg.npcs ~= nil then
            for i, j in pairs(scene_cfg.npcs) do
                if j.id == other_cfg.npcid then
                    scene_npc_cfg = j
                    break
                end
            end
        end

        local call_back = function ()
        	GuajiWGCtrl.Instance:MoveToNpc(other_cfg.npcid, nil, bootybay_scene_id, true, nil, false)
        end

        if scene_npc_cfg and scene_id ~= bootybay_scene_id then
        	MoveCache.SetEndType(MoveEndType.Normal)
        	TaskWGCtrl.Instance:AddFlyUpList(call_back)
        	local scene_logic = Scene.Instance:GetSceneLogic()
        	local x, y = scene_logic:GetTargetScenePos(bootybay_scene_id)
        	x = x or 0
        	y = y or 0
        	--TaskWGCtrl.Instance:SendFlyByShoe(bootybay_scene_id,x, y)
        	TaskWGCtrl.Instance:JumpFly(bootybay_scene_id,x, y)

        	
		else
			call_back()
		end
		return
	end
	--当前挖宝次数小于最大挖宝次数
	if other_cfg.task_wabao_times - wabao_times <= 0 then
	else
		local wabao_pos_info = BootyBayWGData.Instance:GetWaBaoPosInfo()[WABAO_TYPE.WABAO_TYPE_TASK]
		BootyBayWGData.Instance:SetWaBaoType(WABAO_TYPE.WABAO_TYPE_TASK)

		BootyBayWGCtrl.Instance.frist_wabao = false
		if wabao_pos_info and wabao_pos_info.has_treasure == 1 then

			local call_back = function ()
				BootyBayWGCtrl.Instance:MoveToWaBaoPos(wabao_pos_info)
			end

			if scene_id ~= bootybay_scene_id then
				MoveCache.SetEndType(MoveEndType.Normal)
				TaskWGCtrl.Instance:AddFlyUpList(call_back)
				local scene_logic = Scene.Instance:GetSceneLogic()
				local x, y = scene_logic:GetTargetScenePos(bootybay_scene_id)
				x = x or 0
				y = y or 0
         		TaskWGCtrl.Instance:JumpFly(bootybay_scene_id,x, y)
				return
			else
				call_back()
				return
			end
		end
		BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_DO_TASK)

	end
end

function BootyBayWGCtrl:OnSCWabaoInfo(protocol)
	self.data:SetSCWabaoInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BootyBaySceneFollowView)
	RemindManager.Instance:Fire(RemindName.Bootybay)
	ViewManager.Instance:FlushView(GuideModuleName.FuBenPanel,TabIndex.fubenpanel_bootybay)
end

--单人副本信息
function BootyBayWGCtrl:OnSCWabaoPersonFBInfo(protocol)
	self.data:SetPersonFBInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.BootyBayFBView)

	local scene_type = Scene.Instance:GetSceneType()
	if protocol.is_finish == 1 then
		if protocol.kill_boss_num >= 1 and scene_type == SceneType.BOOTYBAY_FB then
			local reward_list = self:SotrRewardList(protocol.reward_item_list)

			FuBenWGCtrl.Instance:SetCommonWinData(scene_type, reward_list, 0, 0, 3, 10, nil ,nil,nil)
			--self:PassCallBack()
			return
		end
		FuBenWGCtrl.Instance:OpenLose(SceneType.BOOTYBAY_FB)
	else
		FuBenWGCtrl.Instance:SetOutFbTime(protocol.fb_end_timestamp, true, true)
	end
end

--组队副本信息
function BootyBayWGCtrl:OnSCWabaoTeamFBInfo(protocol)
	self.data:SetTeamFBInfo(protocol)
	ViewManager.Instance:FlushView(GuideModuleName.TeamBootyBayFBView)
	local scene_type = Scene.Instance:GetSceneType()
	if protocol.is_finish == 1 then
		if protocol.kill_boss_num >= 1 and scene_type == SceneType.TEAM_BOOTYBAY_FB then
			local reward_list = self:SotrRewardList(protocol.reward_item_list)
			FuBenWGCtrl.Instance:SetCommonWinData(scene_type, reward_list, 0, 0, 3, 10, nil ,nil,nil)
			self:PassCallBack()
			return
		end
		FuBenWGCtrl.Instance:OpenLose(SceneType.TEAM_BOOTYBAY_FB)
	else
		FuBenWGCtrl.Instance:SetOutFbTime(protocol.fb_end_timestamp, true, true)
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
	if SceneType.TEAM_BOOTYBAY_FB then
		Scene.Instance:CheckClientObj()
		scene_logic:CreateCustomBlockPoint()
	end
end

function BootyBayWGCtrl:SotrRewardList(reward_list)
	if not reward_list or IsEmptyTable(reward_list) then return {} end
	local list = {}
	for k,v in pairs(reward_list) do
		local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		list[k] = v
		list[k].color = item_cfg.color
	end
	SortTools.SortDesc(list,"color")
	return list
end

function BootyBayWGCtrl:PassCallBack()
	local time = TimeWGCtrl.Instance:GetServerTime() + 10
	FuBenPanelCountDown.Instance:SetTimerInfo(time,function()
		FuBenWGCtrl.Instance:SendLeaveFB()
			end, {x = 0, y = -50})
end


--挖宝完成所有任务次数
function BootyBayWGCtrl:OnSCWabaoFinishAllTask()
	local other_cfg = self.data:GetOtherConfig()
	self.bootybay_reward_tips:SetData(other_cfg.task_finish_reward_item)
	self.bootybay_reward_tips:SetDesc(Language.BootyBay.TaskFinishDesc)
	self.bootybay_reward_tips:Open()
end


--藏宝湾组队协议
function BootyBayWGCtrl:OnSCWabaoTeamFBOpen(protocol)
	self.data:SetBootybayTeamInfo(protocol)
	if protocol.notify_reason == WabaoTeamFBNotifyReason.Query then
		ViewManager.Instance:Open(GuideModuleName.BootyBayFBReadyView)
	else
		ViewManager.Instance:FlushView(GuideModuleName.BootyBayFBReadyView)
	end
	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:StopMove()
		GuajiWGCtrl.Instance:ClearAllOperate()
	end
end

--物品拾取回调
function BootyBayWGCtrl:OnPickItemEvent()
	if self.delay_goto_wabao then
		GlobalTimerQuest:CancelQuest(self.delay_goto_wabao)
		self.delay_goto_wabao = nil
	end
	local bootybay_scene_id = self.data:GetBootyBaySceneId()
	local scene_id = Scene.Instance:GetSceneId()
	if bootybay_scene_id ~= scene_id then return end
	--掉落物列表
	local fall_item_list = Scene.Instance:GetObjListByType(SceneObjType.FallItem) or {}
	local main_role = Scene.Instance:GetMainRole()
	local fall_count = 0
	for k, v in pairs(fall_item_list) do
		if v:GetVo().owner_role_id > 0 and v:GetVo().owner_role_id == main_role:GetRoleId() then
			fall_count = fall_count + 1
		end
	end
	if fall_count <= 1 then
		if self.data.wabao_type == WABAO_TYPE.WABAO_TYPE_BAOTU then
			--道具挖宝
			local use_baotu_info = self.data:GetUseBaoTuInfo()
			if not IsEmptyTable(use_baotu_info) then
				local stuff_num = ItemWGData.Instance:GetItemNumInBagById(use_baotu_info.item_id)
				if stuff_num > 0 then
					--继续使用宝图挖宝
					BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_USE_BAOTU,use_baotu_info.quality)		--请求生成宝藏点
				else
					self:SetAutoWaBaoFlag(false)
				end
			end
		elseif self.data.wabao_type == WABAO_TYPE.WABAO_TYPE_TASK then
			--任务挖宝
			local wabao_times = BootyBayWGData.Instance:GettDailyWaBaoRemainCount()
			local is_accepted = BootyBayWGData.Instance:GetIsAcceptWaBaoTask()
			local other_cfg = BootyBayWGData.Instance:GetOtherConfig()
			--还有剩余次数
			if is_accepted and other_cfg.task_wabao_times - wabao_times > 0 then
				--继续请求任务挖宝
				--请求生成任务宝藏点
				BootyBayWGCtrl.Instance:SendCSWabaoOpera(WABAO_OP.WABAO_OP_DO_TASK)
			else
				self:SetAutoWaBaoFlag(false)
			end
		end
	end
end

-- 挖宝挖出来的怪死亡通知
function BootyBayWGCtrl:OnSCWaBaoMonsterDieNotice(protocol)

end

--打开藏宝湾场景信息界面
function BootyBayWGCtrl:OpenBootybayFollowView()
	ViewManager.Instance:Open(GuideModuleName.BootyBaySceneFollowView)
end

--关闭藏宝湾场景信息界面
function BootyBayWGCtrl:CloseBootybayFollowView()
	ViewManager.Instance:Close(GuideModuleName.BootyBaySceneFollowView)
end

--刷新藏宝湾场景信息界面
function BootyBayWGCtrl:FlushBootybaySceneFollowView()
	if self.bootybay_scene_follow_view:IsOpen() then
		self.bootybay_scene_follow_view:Flush()
	end
end

--打开藏宝湾单人副本内的信息界面
function BootyBayWGCtrl:OpenBootyBayFBView()
	ViewManager.Instance:Open(GuideModuleName.BootyBayFBView)
end

--关闭藏宝湾单人副本内的信息界面
function BootyBayWGCtrl:CloseBootyBayFBView()
	ViewManager.Instance:Close(GuideModuleName.BootyBayFBView)
end

--刷新藏宝湾单人副本内的信息界面
function BootyBayWGCtrl:FlushBootyBayFBView()
	ViewManager.Instance:FlushView(GuideModuleName.BootyBayFBView)
end

--打开藏宝湾组队副本内的信息界面
function BootyBayWGCtrl:OpenTeamBootyBayFBView()
	ViewManager.Instance:Open(GuideModuleName.TeamBootyBayFBView)
end

--关闭藏宝湾组队副本内的信息界面
function BootyBayWGCtrl:CloseTeamBootyBayFBView()
	ViewManager.Instance:Close(GuideModuleName.TeamBootyBayFBView)
end

--刷新藏宝湾组队副本内的信息界面
function BootyBayWGCtrl:FlushTeamBootyBayFBView()
	ViewManager.Instance:FlushView(GuideModuleName.TeamBootyBayFBView)
end

function BootyBayWGCtrl:GetEventFromBootyBay()
	return self.is_from_bootybay
end

-- 设置组队准备事件是否是来自藏宝湾
function BootyBayWGCtrl:SetEventFromBootyBay(is_from_bootybay)
	self.is_from_bootybay = is_from_bootybay
end

function BootyBayWGCtrl:OpenBootybayFBReadyView()
	if self.bootybay_fb_ready_view:IsOpen() then
		ViewManager.Instance:FlushView(GuideModuleName.BootyBayFBReadyView)
	else
		ViewManager.Instance:Open(GuideModuleName.BootyBayFBReadyView)
	end
end

function BootyBayWGCtrl:ReqGoToZangBaoPos(is_req)
	if 1 == is_req then
		local data = self.data:GetTempWaBaoInfo()
		if data then
			self:SendBaoTuWaBao(data)
		end

		self.data:SetTempWaBaoInfo()
		return
	end

	self.data:SetTempWaBaoInfo()

end