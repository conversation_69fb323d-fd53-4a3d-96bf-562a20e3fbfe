local DEFAULT_BAG_CELL_NUM = 280

function MengLingView:InitMengLingBagPanel()
    if not self.mengling_bag_list then
		self.mengling_bag_list = AsyncBaseGrid.New()
		self.mengling_bag_list:CreateCells({col = 4, itemRender = MengLingBagCellRender, cell_count = DEFAULT_BAG_CELL_NUM, list_view = self.node_list.mengling_bag_list})
		self.mengling_bag_list:SetStartZeroIndex(false)
		self.mengling_bag_list:SetIsMultiSelect(false)
	end

    XUI.AddClickEventListener(self.node_list.btn_wear, BindTool.Bind(self.OnClickWearBtn, self))
    XUI.AddClickEventListener(self.node_list.btn_go_to, BindTool.Bind(self.OnClickGoToBtn, self))
end

function MengLingView:DeleteMengLingBagPanel()
    if self.mengling_bag_list then
        self.mengling_bag_list:DeleteMe()
        self.mengling_bag_list = nil
    end
end

function MengLingView:FlushMengLingBagPanel()
    -- self.node_list.mengling_no_wearequip:CustomSetActive(false)
    -- local data_list = MengLingWGData.Instance:GetMengLingBagDataList()
    local data_list = MengLingWGData.Instance:GetMengLingBagDataListBySeq(self.select_equip_suit_seq)

    self.mengling_bag_list:SetDataList(data_list)
    self.node_list.desc_bag_size.text.text = string.format(Language.MengLing.BagNumDesc, MengLingWGData.Instance:GetBagCurNum(), DEFAULT_BAG_CELL_NUM)
    self:FlushMengLingBagWearRemind()
end

function MengLingView:FlushMengLingBagWearRemind()
    local wear_remind = false

    if self.select_equip_suit_seq and self.select_equip_suit_seq >= 0 then
        local wear_data = MengLingWGData.Instance:GetMengLingOneKeyWearDataList(self.select_equip_suit_seq)
        wear_remind = not IsEmptyTable(wear_data)
    end

    self.node_list.btn_wear_remind:CustomSetActive(wear_remind)
end

function MengLingView:OnClickWearBtn()
    if self.select_equip_suit_seq and self.select_equip_suit_seq >= 0 then
        local wear_data = MengLingWGData.Instance:GetMengLingOneKeyWearDataList(self.select_equip_suit_seq)
        
        if IsEmptyTable(wear_data) then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.MengLing.MengLingOneKeyWearErrorTips)
        else
            MengLingWGCtrl.Instance:OnCSDreamSpiritEquip(self.select_equip_suit_seq, #wear_data, wear_data)
        end
    end
end

MengLingBagCellRender = MengLingBagCellRender or BaseClass(ItemCell)

function MengLingBagCellRender:OnFlush()
    ItemCell.OnFlush(self)

    local up_flag = false
    if not IsEmptyTable(self.data) then
       local equip_cfg = MengLingWGData.Instance:GetMengLingEquipCfgByItemId(self.data.item_id)

        if not IsEmptyTable(equip_cfg) then
            up_flag = MengLingWGData.Instance:GetMengLingItemUpFlag(equip_cfg.seq, equip_cfg.slot, equip_cfg.item_id)
        end
    end

    self:SetUpFlagIconVisible(up_flag)
end