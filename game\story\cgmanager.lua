local TypeUnityGameObject = typeof(UnityEngine.GameObject)

CgManager = CgManager or BaseClass()
function CgManager:__init()
	if CgManager.Instance ~= nil then
		ErrorLog("[CgManager] attempt to create singleton twice!")
		return
	end
	CgManager.Instance = self

	self.cg = nil
	self.cache_prefab_list = {}
	self.scene_click_limit_time = 0
	self.cg_end_call_back = nil
end

function CgManager:__delete()
	if nil ~= self.cg then
		self.cg:DeleteMe()
		self.cg = nil
	end
	if self.role_info_event then
		GlobalEventSystem:UnBind(self.role_info_event)
	end

	self.cg_end_call_back = nil
	CgManager.Instance = nil
end

function CgManager:Play(cg, end_callback, start_callback, is_jump_cg, skip_callback, show_ui, not_auto_end,change_callback)
	if nil == cg or cg == self.cg then
		return
	end

	if not RoleWGCtrl.Instance:GetIsHasRoleData() then
		if  not self.role_info_event then
			self.role_info_event = GlobalEventSystem:Bind(LoginEventType.RECV_MAIN_ROLE_INFO, function ()
				GlobalEventSystem:UnBind(self.role_info_event)
				self:Play(cg, end_callback, start_callback, is_jump_cg, skip_callback, show_ui, not_auto_end, change_callback)
			end)
		end
		return
	end

	if nil ~= self.cg then
		self.cg:Stop()
		self.cg:DeleteMe()
	end

	self.cg = cg

	--防止一些界面模型出现在CG镜头里面
	-- if not self.cg.is_ui_cg then
	-- 	ViewManager.Instance:CloseAll()
	-- end

	local main_role = Scene.Instance:GetMainRole()
	if main_role then
		main_role:StopMove()	-- 玩家停止移动
		main_role:QingGongEnable(false)
	end
	GuajiWGCtrl.Instance:ClearAllOperate()	-- 停止所有操作
	-- TalkCache.StopCurIndexAudio()

	self.cg:Play(function ()
			self.cg:DeleteMe()
			self.cg = nil
			Scene.Instance:UpdateSceneCullingFactor()
			-- Scene.Instance:SetWeatherState(true)
			Scene.Instance:UpdateSceneQuality(SCENE_QUALITY_TYPE.WEATHER, true)
			if end_callback then
				end_callback()
			end

			if self.cg_end_call_back then
				self.cg_end_call_back()
				self.cg_end_call_back = nil
			end
		end, function ()
			-- Scene.Instance:SetWeatherState(false)
			Scene.Instance:UpdateSceneQuality(SCENE_QUALITY_TYPE.WEATHER, false)
			if start_callback then
				start_callback()
			end
		end, is_jump_cg, skip_callback, show_ui, not_auto_end,change_callback)
end

function CgManager:Stop()
	if nil ~= self.cg then
		self.cg:Stop()
		self.cg:DeleteMe()
		self.cg = nil
	end
end

function CgManager:OnPlayEnd()
	if nil ~= self.cg then
		self.cg:OnPlayEnd()
	end
end

function CgManager:IsCgIng()
	return nil ~= self.cg
end

function CgManager:GetCurCg()
	return self.cg
end

-- 增加一个回调，用于CG结束后做某些操作
function CgManager:SetCgEndCallBack(call_back)
	self.cg_end_call_back = call_back
end

function CgManager:IsCanMoveCG()
	return self.cg and self.cg:IsCanMoveCG()
end

-- 预加载cg prefab到缓存，引用计数会+1，用完后记得DelCacheCg
function CgManager:PreloadCacheCg(bundle_name, asset_name, callback)
	ResPoolMgr:GetPrefab(bundle_name, asset_name, function(prefab)
		table.insert(self.cache_prefab_list, {prefab = prefab, bundle_name = bundle_name, asset_name = asset_name})
		if nil ~= callback then
			callback()
		end
	end, true)
end

function CgManager:DelCacheCgs()
	for k,v in pairs(self.cache_prefab_list) do
		ResPoolMgr:Release(v.prefab, ResPoolReleasePolicy.DestroyQuick)
	end
	self.cache_prefab_list = {}
end

function CgManager:DelCacheCg(bundle_name, asset_name)
	for i=#self.cache_prefab_list, 1, -1 do
		local t = self.cache_prefab_list[i]
		if t.bundle_name == bundle_name and t.asset_name == asset_name then
			table.remove(self.cache_prefab_list, i)
			ResPoolMgr:Release(t.prefab, ResPoolReleasePolicy.DestroyQuick)
		end
	end
end

function CgManager:ModifyTrack()
	if self.cg and self.cg.ModifyTrack then
		self.cg:ModifyTrack()
	end
end

function CgManager:SetOperaBtnCallback(btn_index, callback)
	if nil ~= self.cg then
		self.cg:SetOperaBtnCallback(btn_index, callback)
	end
end

function CgManager:SetCurTimePoint(time)
	if nil ~= self.cg then
		self.cg:SetCurTimePoint(time)
	end
end

function CgManager:ResetSceneClickTime()
	self.scene_click_limit_time = Status.NowTime + 0.5
end

-- CG播放完一秒内不许玩家点地面移动，防止他误操作导致自动任务停了
function CgManager:IsLimit()
	local is_limit = false
	if self.scene_click_limit_time ~= nil and self.scene_click_limit_time >= Status.NowTime then
		is_limit = true
	end

	return is_limit
end