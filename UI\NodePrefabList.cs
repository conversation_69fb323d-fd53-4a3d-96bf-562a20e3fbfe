﻿using System.Collections;
using System.Collections.Generic;
using UnityEngine;
using System;

#if UNITY_EDITOR
using UnityEditorInternal;
using UnityEditor;
#endif

/// <summary>
/// 子节点预制体列表
/// </summary>
public class NodePrefabList : MonoBehaviour
{
    public List<NodePrefabElement> nodeList = new List<NodePrefabElement>();

    private GameObject nodeRoot = null;

    public void InstantiateAllNodeInEditor()
    {
#if UNITY_EDITOR
        DestroyAllNodeInEditor();
        if (nodeRoot == null)
        {
            nodeRoot = new GameObject("nodes", typeof(RectTransform));
            nodeRoot.transform.SetParent(transform, false);
            nodeRoot.transform.localPosition = Vector3.zero;
            nodeRoot.GetComponent<RectTransform>().sizeDelta = GetComponent<RectTransform>().sizeDelta;
        }
        for (int i = 0; i < nodeList.Count; i++)
        {
            GameObject prefab = nodeList[i].prefab;
            if (prefab != null)
            {
                GameObject go = Instantiate(prefab);
                go.name = prefab.name;
                go.transform.SetParent(nodeRoot.transform, false);
            }
        }
#endif
    }

    public void DestroyAllNodeInEditor()
    {
#if UNITY_EDITOR
        if (nodeRoot != null)
        {
            DestroyImmediate(nodeRoot);
            nodeRoot = null;
        }

#endif
    }

#if UNITY_EDITOR
    [CustomEditor(typeof(NodePrefabList))]
    public class NodePrefabListEditor : Editor
    {
        ReorderableList reorderableList;
        Color? elementDefaultColor = null;  // 用于储存默认GUI颜色
        private void OnEnable()
        {
            // 创建可排序列表
            reorderableList = new ReorderableList(serializedObject,
                serializedObject.FindProperty("nodeList"),
                true, true, true, true);
            if (elementDefaultColor == null)
            {
                elementDefaultColor = GUI.color;
            }
        }

        public override void OnInspectorGUI()
        {
            serializedObject.Update();
            NodePrefabList script = target as NodePrefabList;

            // GameObject列表
            SerializedProperty prop = serializedObject.FindProperty("nodeList");
            // 绘制子物体回调
            reorderableList.drawElementCallback = (rect, index, isActive, isFocused) => {
                EditorGUI.PropertyField(rect, prop.GetArrayElementAtIndex(index));
            };

            // 绘制列表标题
            reorderableList.drawHeaderCallback = (Rect rect) =>
            {
                GUI.Label(rect, "子节点预制体， 列表顺序会影响实例化时候子节点的层级顺序");
            };

            // 加号回调
            reorderableList.onAddCallback = (ReorderableList list) =>
            {
                script.nodeList.Add(new NodePrefabElement());
                list.index = script.nodeList.Count - 1;         // 选中最后一个
            };

            // 减号回调
            reorderableList.onRemoveCallback = (ReorderableList list) =>
            {
                if(script.nodeList[list.index].prefab != null)
                {
                    if (EditorUtility.DisplayDialog("警告", "是否真的要删除" + script.nodeList[list.index].prefab.name + "？", "是", "否"))
                    {
                        ReorderableList.defaultBehaviours.DoRemoveButton(list);
                    }
                }
                else
                {
                    ReorderableList.defaultBehaviours.DoRemoveButton(list);
                }
            };

            // 绘制列表背景
            reorderableList.drawElementBackgroundCallback = (Rect rect, int index, bool isActive, bool isFocused) =>
            {
                if (index == -1)
                {
                    return;
                }
                bool repeat = false;                                                        // 属性key值是否重复
                GameObject prefab = script.nodeList[index].prefab;
                if (prefab != null)
                {
                    string key = prefab.name;
                    for (int i = 0; i < script.nodeList.Count; i++)
                    {
                        if (script.nodeList[i].prefab != null && i != index && key.Equals(script.nodeList[i].prefab.name))
                        {
                            repeat = true;
                        }
                    }
                }

                if (repeat)
                {
                    GUI.color = new Color(1f, 0f, 0f, 0.8f);                                // 修改GUI颜色
                }
                else
                {
                    GUI.color = (Color)elementDefaultColor;                                 // 还原GUI颜色
                }
                ReorderableList.defaultBehaviours.DrawElementBackground(rect, index, isActive, isFocused, true);
            };

            reorderableList.DoLayoutList();
            serializedObject.ApplyModifiedProperties();
            
            if (GUILayout.Button("临时实例化所有节点"))
            {
                script.InstantiateAllNodeInEditor();
            }

            if (GUILayout.Button("清除临时节点"))
            {
                script.DestroyAllNodeInEditor();
            }

            if (GUI.changed)
            {
                EditorUtility.SetDirty(target);
            }
        }
    }
#endif
}