﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class DG_Tweening_PathModeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(DG.Tweening.PathMode));
		<PERSON><PERSON>("Ignore", get_Ignore, null);
		<PERSON><PERSON>("Full3D", get_Full3D, null);
		<PERSON><PERSON>("TopDown2D", get_TopDown2D, null);
		<PERSON><PERSON>("Sidescroller2D", get_Sidescroller2D, null);
		<PERSON><PERSON>RegFunction("IntToEnum", IntToEnum);
		L.EndEnum();
		TypeTraits<DG.Tweening.PathMode>.Check = CheckType;
		StackTraits<DG.Tweening.PathMode>.Push = Push;
	}

	static void Push(IntPtr L, DG.Tweening.PathMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(DG.Tweening.PathMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Ignore(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.PathMode.Ignore);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Full3D(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.PathMode.Full3D);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_TopDown2D(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.PathMode.TopDown2D);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Sidescroller2D(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.PathMode.Sidescroller2D);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		DG.Tweening.PathMode o = (DG.Tweening.PathMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

