TianShenShenUpGradeTips = TianShenShenUpGradeTips or BaseClass(SafeBaseView)

function TianShenShenUpGradeTips:__init()
	self.view_layer = UiLayer.Pop
	self.view_style = ViewStyle.Half
	self:SetMaskBg(true)
	-- self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_result_panel")
	self:AddViewResource(0, "uis/view/tianshen_prefab", "layout_shenshi_upgrade_tip")
	self.played_open_anim = false
	self.view_cache_time = 0
end

function TianShenShenUpGradeTips:__delete()
end

function TianShenShenUpGradeTips:LoadCallBack()
	-- if self.node_list.top_title_bg then
	-- 	self.node_list.top_title_bg.raw_image:LoadSprite(ResPath.GetRawImagesPNG("a3_gxhd_djhd_jscg"))
 	-- end

	if not self.shenshi_upgrade_role_model then
		self.shenshi_upgrade_role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["role_display"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = true,
		}
		
		self.shenshi_upgrade_role_model:SetRenderTexUI3DModel(display_data)
		-- self.shenshi_upgrade_role_model:SetUI3DModel(self.node_list["role_display"].transform,
		-- 	self.node_list.Block.event_trigger_listener, 1, true, MODEL_CAMERA_TYPE.BASE)
		self:AddUiRoleModel(self.shenshi_upgrade_role_model)
	end

	if self.tianshen_attr_list == nil then
		self.tianshen_attr_list = {}
		local node_num = self.node_list.tianshen_attr_list.transform.childCount
		for i = 1, node_num do
			self.tianshen_attr_list[i] = CommonAttrRender.New(self.node_list.tianshen_attr_list:FindObj("attr_" .. i))
		end
	end

	self.node_list["close_btn"].button:AddClickListener(BindTool.Bind(self.DoCloseTween, self))
	self.node_list["view_panel"].button:AddClickListener(BindTool.Bind(self.DoCloseTween, self))
end

function TianShenShenUpGradeTips:ShowIndexCallBack()

end

function TianShenShenUpGradeTips:CloseCallBack()
	if self.close_tweener then
		self.close_tweener:Kill()
	end
	if self.show_tweener then
		self.show_tweener:Kill()
	end
end

function TianShenShenUpGradeTips:ReleaseCallBack()
	if self.shenshi_upgrade_role_model then
		self.shenshi_upgrade_role_model:DeleteMe()
		self.shenshi_upgrade_role_model = nil
	end
	self.played_open_anim = false
	if self.close_tweener then
		self.close_tweener:Kill()
	end
	self.close_tweener = nil
	if self.show_tweener then
		self.show_tweener:Kill()
	end
	self.show_tweener = nil

	if self.tianshen_attr_list then
		for k, v in pairs(self.tianshen_attr_list) do
			v:DeleteMe()
		end
		self.tianshen_attr_list = nil
	end
end

function TianShenShenUpGradeTips:DoOpenTween()
	if self.show_tweener then
		self.show_tweener:Kill()
	end

	self.show_tweener = DG.Tweening.DOTween.Sequence()
	local panel = self.node_list.view_panel
	panel.transform.localScale = Vector3(0.6, 0.8, 0.8)
	self.show_tweener:Append(panel.canvas_group:DoAlpha(0.6, 1, 0.3))
	self.show_tweener:Join(panel.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.25))
	self.show_tweener:Append(panel.rect:DOScale(Vector3(1, 1, 1), 0.05))
	self.show_tweener:SetEase(DG.Tweening.Ease.InOutQuad)
	self.show_tweener:OnComplete(
		function()
			self.played_open_anim = true
			self:Flush()
			self:PlayInfoAnim()
		end
	)
end

function TianShenShenUpGradeTips:PlayInfoAnim()
	GlobalTimerQuest:AddDelayTimer(function()
		if self.node_list and self.node_list.old_rank then
			self.node_list.old_rank.canvas_group:DoAlpha(0.2, 1, 0.2)
		end
	end, 0.2)


	GlobalTimerQuest:AddDelayTimer(function()
		if self.node_list and self.node_list.arrow then
			self.node_list.arrow.canvas_group:DoAlpha(0.2, 1, 0.2)
		end
	end, 0.4)

	GlobalTimerQuest:AddDelayTimer(function()
		if self.node_list and self.node_list.left_panel then
			self.node_list.left_panel.canvas_group:DoAlpha(0.2, 1, 0.2)
		end
	end, 0.9)
end

function TianShenShenUpGradeTips:DoCloseTween()
	-- if self.close_tweener then
	-- 	self.close_tweener:Kill()
	-- end
	-- self.close_tweener = DG.Tweening.DOTween.Sequence()
	-- local panel = self.node_list.view_panel
	-- panel.transform.localScale = Vector3(1, 1, 1)
	-- self.close_tweener:Append(panel.rect:DOScale(Vector3(1.2, 1.2, 1.2), 0.25))
	-- self.close_tweener:Join(panel.canvas_group:DoAlpha(1, 0.1, 0.2))
	-- self.close_tweener:Append(panel.rect:DOScale(Vector3(1.1, 1.1, 1.1), 0.05))
	-- self.close_tweener:SetEase(DG.Tweening.Ease.InOutQuad)
	-- self.close_tweener:OnComplete(function()
	self:Close()
	-- end)
end

function TianShenShenUpGradeTips:OnFlush()
	if not self.tianshen_index then
		return
	end

	-- if not self.played_open_anim then
	-- 	self:DoOpenTween()
	-- 	return
	-- end

	self:FlushModel()

	local tianshen_image_cfg = TianShenWGData.Instance:GetTianShenCfg(self.tianshen_index)
	local tianshen_equip_data = TianShenWGData.Instance:GetShenShiEquipInfo(self.tianshen_index)
	local tianshen_rank = tianshen_equip_data.jingshen
	-- local is_max = TianShenWGData.Instance:ShenShiIsMaxRank(self.tianshen_index, tianshen_equip_data.jingshen)
	-- local bundle, asset = ResPath.GetRawImagesPNG(tianshen_image_cfg.bianshen_name_res)
	-- self.node_list.name_success_upgrade.raw_image:LoadSprite(bundle, asset, function()
	-- 	self.node_list.name_success_upgrade.raw_image:SetNativeSize()
	-- end)

	self.node_list.obj_name.text.text = tianshen_image_cfg.bianshen_name

	local normal_str = string.format("a3_ts_succinct_0%d", tianshen_rank)
	-- local bundle1, asset1 = ResPath.GetCommon("a3_ty_pz" .. tianshen_rank + 1)
	self.node_list["new_rank"].image:LoadSprite(ResPath.GetF2TianShenImage(normal_str))
	-- self.node_list["img_rank"].image:LoadSprite(ResPath.GetF2TianShenImage(normal_str))
	normal_str = string.format("a3_ts_succinct_0%d", tianshen_rank - 1)
	self.node_list.old_rank.image:LoadSprite(ResPath.GetF2TianShenImage(normal_str))

	-- local bundle2, asset2 = ResPath.GetCommon("a3_ty_pz" .. (tianshen_rank))
	-- self.node_list["old_rank"].image:LoadSprite(bundle2, asset2, function()
	-- 	self.node_list["old_rank"].image:SetNativeSize()
	-- end)

	local attr_tab = TianShenWGData.Instance:GetSingleLimitAttrTab(tianshen_rank)
	local attr_list = EquipWGData.GetSortAttrListByCfg(attr_tab)
	for k, v in pairs(self.tianshen_attr_list) do
		v:SetData(attr_list[k])
	end

	local js_old_data = TianShenWGData.Instance:GetShenShiJinShengDataByLevel(self.tianshen_index, tianshen_rank - 1)
	local js_new_data = TianShenWGData.Instance:GetShenShiJinShengDataByLevel(self.tianshen_index, tianshen_rank)
	local js_old_name = js_old_data and js_old_data.lv_name
	local js_new_name = js_new_data and js_new_data.lv_name
	self.node_list.old_rank_name.text.text = ToColorStr(js_old_name, ITEM_COLOR[tianshen_rank - 1])
	self.node_list.new_rank_name.text.text = ToColorStr(js_new_name, ITEM_COLOR[tianshen_rank])
	
	local list = TianShenWGData.Instance:GetTianShenShenShiRefineShowList(self.tianshen_index)
	if list and list[tianshen_rank] then
		local max_value = 0
		local min_value = 0

		for i, aim_data in ipairs(list[tianshen_rank]) do
			if aim_data then
				local base_data, baodi_data = TianShenWGData.Instance:GetTianShenShenShiRefineInterval(aim_data)

				local baodi_max_value = (baodi_data.max_value or 0) / 100
				max_value = max_value + baodi_max_value
	
				local base_min_value = (base_data.min_value or 0) / 100
				min_value = min_value + base_min_value
			end
		end

		local num_str = string.format(Language.TianShen.TSShenShiSuccinctTips2, #list[tianshen_rank])
		local value_str =  string.format(Language.TianShen.TSShenShiSuccinctTips4, min_value, max_value)
		local final_num_str = string.format("%s\n%s", num_str, value_str)
		self.node_list.succinct_desc.text.text = final_num_str
	end

	-- self.node_list.now_arrt.text.text = string.format(Language.TianShen.OpenLimit26, (tonumber(js_old_data.attr_per) or 0) / 100 .. "%")
	-- self.node_list.next_arrt.text.text = string.format(Language.TianShen.OpenLimit26, (tonumber(js_new_data.attr_per) or 0) / 100 .. "%")
end

function TianShenShenUpGradeTips:SetTianShenData(index)
	self.tianshen_index = index
end

function TianShenShenUpGradeTips:FlushModel()
	local data = TianShenWGData.Instance:GetTianShenCfg(self.tianshen_index)
	if data then
		---添加化魔展示
		local appe_image_id = TianShenHuamoWGData.Instance:GetHuaMoAppeImageById(data.index) or data.appe_image_id
		self.shenshi_upgrade_role_model:SetTianShenModel(appe_image_id, data.index, true, nil, SceneObjAnimator.Rest)
	end
	AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Advanced, false, true))
end
