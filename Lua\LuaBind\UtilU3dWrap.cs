﻿//this source code was auto-generated by to<PERSON><PERSON>#, do not modify it
using System;
using LuaInterface;

public class UtilU3dWrap
{
	public static void Register(LuaState L)
	{
		L.BeginStaticLibs("UtilU3d");
		<PERSON><PERSON>RegFunction("CacheData", CacheData);
		<PERSON><PERSON>RegFunction("DelCacheData", DelCacheData);
		<PERSON><PERSON>Function("GetCacheData", GetCacheData);
		<PERSON><PERSON>RegFunction("SetFilterLogType", SetFilterLogType);
		L.RegFunction("RequestGet", RequestGet);
		<PERSON><PERSON>RegFunction("RequestPost", RequestPost);
		<PERSON><PERSON>RegFunction("RequestWWWFormPost", RequestWWWFormPost);
		<PERSON><PERSON>unction("RequestJsonPost", RequestJsonPost);
		<PERSON><PERSON>RegFunction("Download", Download);
		L.RegFunction("Upload", Upload);
		<PERSON>.RegFunction("WatchFunRunTime", WatchFunRunTime);
		<PERSON><PERSON>Function("AudioPlayerIsPlaying", AudioPlayerIsPlaying);
		<PERSON><PERSON>("StopAudioPlayer", StopAudioPlayer);
		<PERSON><PERSON>("Screenshot", Screenshot);
		<PERSON>.RegFunction("ScreenshotByJpg", ScreenshotByJpg);
		L.RegFunction("ScreenshotByPng", ScreenshotByPng);
		L.RegFunction("SaveScreenshot", SaveScreenshot);
		L.RegFunction("DeleteFile", DeleteFile);
		L.RegFunction("ForceReSetCamera", ForceReSetCamera);
		L.RegFunction("IsFileInfoLimit", IsFileInfoLimit);
		L.RegFunction("GetFileInfoLength", GetFileInfoLength);
		L.RegFunction("GetGameObjectFullPath", GetGameObjectFullPath);
		L.RegFunction("CopyToClipboard", CopyToClipboard);
		L.RegFunction("SetRendererLayer", SetRendererLayer);
		L.RegFunction("SetLayer", SetLayer);
		L.RegFunction("ConvertColorToHex", ConvertColorToHex);
		L.RegFunction("ConvertColorToHSV", ConvertColorToHSV);
		L.RegFunction("ConvertHexToColor", ConvertHexToColor);
		L.RegFunction("ConvertHSVToColor", ConvertHSVToColor);
		L.EndStaticLibs();
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CacheData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			object arg1 = ToLua.ToVarObject(L, 2);
			UtilU3d.CacheData(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DelCacheData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			object arg1 = ToLua.ToVarObject(L, 2);
			UtilU3d.DelCacheData(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetCacheData(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			object o = UtilU3d.GetCacheData(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetFilterLogType(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.LogType arg0 = (UnityEngine.LogType)ToLua.CheckObject(L, 1, typeof(UnityEngine.LogType));
			UtilU3d.SetFilterLogType(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RequestGet(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				string arg0 = ToLua.CheckString(L, 1);
				UtilU3d.RequestGet(arg0);
				return 0;
			}
			else if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				System.Action<bool,string> arg1 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 2);
				UtilU3d.RequestGet(arg0, arg1);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UtilU3d.RequestGet");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RequestPost(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				UtilU3d.RequestPost(arg0, arg1);
				return 0;
			}
			else if (count == 3)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				System.Action<bool,string> arg2 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 3);
				UtilU3d.RequestPost(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UtilU3d.RequestPost");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RequestWWWFormPost(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				UtilU3d.RequestWWWFormPost(arg0, arg1);
				return 0;
			}
			else if (count == 3)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				System.Action<bool,string> arg2 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 3);
				UtilU3d.RequestWWWFormPost(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UtilU3d.RequestWWWFormPost");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int RequestJsonPost(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				UtilU3d.RequestJsonPost(arg0, arg1);
				return 0;
			}
			else if (count == 3)
			{
				string arg0 = ToLua.CheckString(L, 1);
				string arg1 = ToLua.CheckString(L, 2);
				System.Action<bool,string> arg2 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 3);
				UtilU3d.RequestJsonPost(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UtilU3d.RequestJsonPost");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Download(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			System.Action<bool,string> arg2 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 3);
			UtilU3d.Download(arg0, arg1, arg2);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Upload(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 3);
			string arg0 = ToLua.CheckString(L, 1);
			string arg1 = ToLua.CheckString(L, 2);
			System.Action<bool,string> arg2 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 3);
			bool o = UtilU3d.Upload(arg0, arg1, arg2);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int WatchFunRunTime(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 1)
			{
				System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 1);
				float o = UtilU3d.WatchFunRunTime(arg0);
				LuaDLL.lua_pushnumber(L, o);
				return 1;
			}
			else if (count == 2)
			{
				System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				float o = UtilU3d.WatchFunRunTime(arg0, arg1);
				LuaDLL.lua_pushnumber(L, o);
				return 1;
			}
			else if (count == 3)
			{
				System.Action arg0 = (System.Action)ToLua.CheckDelegate<System.Action>(L, 1);
				float arg1 = (float)LuaDLL.luaL_checknumber(L, 2);
				string arg2 = ToLua.CheckString(L, 3);
				float o = UtilU3d.WatchFunRunTime(arg0, arg1, arg2);
				LuaDLL.lua_pushnumber(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UtilU3d.WatchFunRunTime");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int AudioPlayerIsPlaying(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.IAudioPlayer arg0 = (Nirvana.IAudioPlayer)ToLua.CheckObject<Nirvana.IAudioPlayer>(L, 1);
			bool o = UtilU3d.AudioPlayerIsPlaying(arg0);
			LuaDLL.lua_pushboolean(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int StopAudioPlayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			Nirvana.IAudioPlayer arg0 = (Nirvana.IAudioPlayer)ToLua.CheckObject<Nirvana.IAudioPlayer>(L, 1);
			UtilU3d.StopAudioPlayer(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int Screenshot(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				string arg0 = ToLua.CheckString(L, 1);
				System.Action<bool,string> arg1 = (System.Action<bool,string>)ToLua.CheckDelegate<System.Action<bool,string>>(L, 2);
				UtilU3d.Screenshot(arg0, arg1);
				return 0;
			}
			else if (count == 3 && TypeChecker.CheckTypes<System.Action<UnityEngine.Texture2D>, int, bool>(L, 1))
			{
				System.Action<UnityEngine.Texture2D> arg0 = (System.Action<UnityEngine.Texture2D>)ToLua.ToObject(L, 1);
				int arg1 = (int)LuaDLL.lua_tonumber(L, 2);
				bool arg2 = LuaDLL.lua_toboolean(L, 3);
				UtilU3d.Screenshot(arg0, arg1, arg2);
				return 0;
			}
			else if (count == 3 && TypeChecker.CheckTypes<string, System.Action<bool,string>, int>(L, 1))
			{
				string arg0 = ToLua.ToString(L, 1);
				System.Action<bool,string> arg1 = (System.Action<bool,string>)ToLua.ToObject(L, 2);
				int arg2 = (int)LuaDLL.lua_tonumber(L, 3);
				UtilU3d.Screenshot(arg0, arg1, arg2);
				return 0;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UtilU3d.Screenshot");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ScreenshotByJpg(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Action<UnityEngine.Texture2D> arg0 = (System.Action<UnityEngine.Texture2D>)ToLua.CheckDelegate<System.Action<UnityEngine.Texture2D>>(L, 1);
			UtilU3d.ScreenshotByJpg(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ScreenshotByPng(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			System.Action<UnityEngine.Texture2D> arg0 = (System.Action<UnityEngine.Texture2D>)ToLua.CheckDelegate<System.Action<UnityEngine.Texture2D>>(L, 1);
			UtilU3d.ScreenshotByPng(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SaveScreenshot(IntPtr L)
	{
		try
		{
			int count = LuaDLL.lua_gettop(L);

			if (count == 2)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				string arg1 = ToLua.CheckString(L, 2);
				bool o = UtilU3d.SaveScreenshot(arg0, arg1);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 3)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				string arg1 = ToLua.CheckString(L, 2);
				UnityEngine.Texture2D arg2 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 3, typeof(UnityEngine.Texture2D));
				bool o = UtilU3d.SaveScreenshot(arg0, arg1, arg2);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else if (count == 4)
			{
				UnityEngine.Texture2D arg0 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 1, typeof(UnityEngine.Texture2D));
				string arg1 = ToLua.CheckString(L, 2);
				UnityEngine.Texture2D arg2 = (UnityEngine.Texture2D)ToLua.CheckObject(L, 3, typeof(UnityEngine.Texture2D));
				bool arg3 = LuaDLL.luaL_checkboolean(L, 4);
				bool o = UtilU3d.SaveScreenshot(arg0, arg1, arg2, arg3);
				LuaDLL.lua_pushboolean(L, o);
				return 1;
			}
			else
			{
				return LuaDLL.luaL_throw(L, "invalid arguments to method: UtilU3d.SaveScreenshot");
			}
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int DeleteFile(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			UtilU3d.DeleteFile(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ForceReSetCamera(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 0);
			UtilU3d.ForceReSetCamera();
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IsFileInfoLimit(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			string arg0 = ToLua.CheckString(L, 1);
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			int o = UtilU3d.IsFileInfoLimit(arg0, arg1);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetFileInfoLength(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			int o = UtilU3d.GetFileInfoLength(arg0);
			LuaDLL.lua_pushinteger(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int GetGameObjectFullPath(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			string o = UtilU3d.GetGameObjectFullPath(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int CopyToClipboard(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			UtilU3d.CopyToClipboard(arg0);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetRendererLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			UtilU3d.SetRendererLayer(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int SetLayer(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 2);
			UnityEngine.GameObject arg0 = (UnityEngine.GameObject)ToLua.CheckObject(L, 1, typeof(UnityEngine.GameObject));
			int arg1 = (int)LuaDLL.luaL_checknumber(L, 2);
			UtilU3d.SetLayer(arg0, arg1);
			return 0;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ConvertColorToHex(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Color arg0 = ToLua.ToColor(L, 1);
			string o = UtilU3d.ConvertColorToHex(arg0);
			LuaDLL.lua_pushstring(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ConvertColorToHSV(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Color arg0 = ToLua.ToColor(L, 1);
			UnityEngine.Vector3 o = UtilU3d.ConvertColorToHSV(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ConvertHexToColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			string arg0 = ToLua.CheckString(L, 1);
			UnityEngine.Color o = UtilU3d.ConvertHexToColor(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int ConvertHSVToColor(IntPtr L)
	{
		try
		{
			ToLua.CheckArgsCount(L, 1);
			UnityEngine.Vector3 arg0 = ToLua.ToVector3(L, 1);
			UnityEngine.Color o = UtilU3d.ConvertHSVToColor(arg0);
			ToLua.Push(L, o);
			return 1;
		}
		catch (Exception e)
		{
			return LuaDLL.toluaL_exception(L, e);
		}
	}
}

