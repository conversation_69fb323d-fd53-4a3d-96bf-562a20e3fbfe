YanYuGeExchangeShopView = YanYuGeExchangeShopView or BaseClass(SafeBaseView)

function YanYuGeExchangeShopView:__init()
    self.view_layer = UiLayer.Normal
    self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true
    
    self.default_index = TabIndex.yanyuge_shop_nwsd
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_a3_common_panel")
    self:AddViewResource(TabIndex.yanyuge_shop_nwsd, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_nwsd_view")
    -- self:AddViewResource(TabIndex.yanyuge_shop_tzsd, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_tzsd_view")
    self:AddViewResource(0, "uis/view/yanyuge_ui_prefab", "layout_yanyuge_sd_common_view")
    self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function YanYuGeExchangeShopView:LoadCallBack()
    -- XUI.AddClickEventListener(self.node_list.btn_tzsd, BindTool.Bind(self.OnClickTZSDBtn, self))
    -- XUI.AddClickEventListener(self.node_list.btn_nwsd, BindTool.Bind(self.OnClickNWSDBtn, self))
    XUI.AddClickEventListener(self.node_list.score_bg, BindTool.Bind(self.OnClickScoreBtn, self))

    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")
    local item_cfg = ItemWGData.Instance:GetItemConfig(show_item)
    if item_cfg then
        self.node_list.score_icon.image:LoadSprite(ResPath.GetItem(item_cfg.icon_id))
    end

    local other_remind_list = {RemindName.YanYuGe_Shop_NWSD}--, RemindName.YanYuGe_Shop_TZSD}
    self.remind_callback = BindTool.Bind(self.OtherRemindCallBack, self)
    for k,v in pairs(other_remind_list) do
        RemindManager.Instance:Bind(self.remind_callback, v)
    end

    -- FunOpen.Instance:RegisterFunUi(FunName.YanYuGeShopTZSD, self.node_list["btn_tzsd"])
    -- FunOpen.Instance:RegisterFunUi(FunName.YanYuGeShopNWSD, self.node_list["btn_nwsd"])
end

function YanYuGeExchangeShopView:LoadIndexCallBack(index)
    if index == TabIndex.yanyuge_shop_nwsd then
        self:NWSDLoadCallBack()
    -- elseif index == TabIndex.yanyuge_shop_tzsd then
    --     self:TZSDLoadCallBack()
    end
end

function YanYuGeExchangeShopView:ShowIndexCallBack(index)
    local bundle, asset 
    if index == TabIndex.yanyuge_shop_nwsd then
        bundle, asset = ResPath.GetRawImagesJPG("a3_yyg_bj2")
        self:NWSDShowIndexCallBack()
    -- elseif index == TabIndex.yanyuge_shop_tzsd then
    --     bundle, asset = ResPath.GetRawImagesJPG("a3_yyg_bj4")
    --     self:TZSDShowIndexCallBack()
    end

    if self.node_list.RawImage_tongyong and nil~= bundle and nil ~= asset then
		self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
		end)
	end

    -- self.node_list.btn_nwsd_select:CustomSetActive(index == TabIndex.yanyuge_shop_nwsd)
    -- self.node_list.btn_tzsd_select:CustomSetActive(index == TabIndex.yanyuge_shop_tzsd)
end

function YanYuGeExchangeShopView:ReleaseCallBack()
    self:NWSDReleaseCallBack()
    -- self:TZSDReleaseCallBack()

    if self.remind_callback then
        RemindManager.Instance:UnBind(self.remind_callback)
        self.remind_callback = nil
    end
end

function YanYuGeExchangeShopView:OnFlush(param_t, index)
    for k,v in pairs(param_t) do
		if k == "all" then
            if index == TabIndex.yanyuge_shop_nwsd then
                self:NWSDOnFlush(param_t)
            elseif index == TabIndex.yanyuge_shop_tzsd then
                self:TZSDOnFlush(param_t)
            end
        end
    end

    local score = YanYuGeWGData.Instance:GetCurScore()
    self.node_list.cur_score.tmp.text = score
end

-- function YanYuGeExchangeShopView:OnClickTZSDBtn()
--     self:ChangeToIndex(TabIndex.yanyuge_shop_tzsd)
-- end

function YanYuGeExchangeShopView:OnClickNWSDBtn()
    self:ChangeToIndex(TabIndex.yanyuge_shop_nwsd)
end

function YanYuGeExchangeShopView:OtherRemindCallBack(remind_name, num)
    if remind_name == RemindName.YanYuGe_Shop_NWSD then
        self.node_list.btn_nwsd_remind:SetActive(num > 0)
    -- elseif remind_name == RemindName.YanYuGe_Shop_TZSD then
    --     self.node_list.btn_tzsd_remind:SetActive(num > 0)
    end
end

function YanYuGeExchangeShopView:OnClickScoreBtn()
    local show_item = YanYuGeWGData.Instance:GetOtherCfgAttrValue("show_item")

    if show_item and show_item > 0 then
        TipWGCtrl.Instance:OpenItem({ item_id = show_item })
    end
end

local TAB_INDEX_CFG = {
    [TabIndex.yanyuge_shop_nwsd] = {FunName = FunName.YanYuGeShopNWSD, RemindName = RemindName.YanYuGe_Shop_NWSD},
    -- [TabIndex.yanyuge_shop_tzsd] = {FunName = FunName.YanYuGeShopTZSD, RemindName = RemindName.YanYuGe_Shop_TZSD},
}

function YanYuGeExchangeShopView:CalcShowIndex()
    local default_index = 0

    for k, v in pairs(TAB_INDEX_CFG) do
        if FunOpen.Instance:GetFunIsOpened(v.FunName) then
            if default_index <= 0 then
                default_index = k
            end

            if RemindManager.Instance:GetRemind(v.RemindName) > 0 then
                return k
            end
        end
    end

	return default_index
end