﻿//this source code was auto-generated by to<PERSON><PERSON><PERSON>, do not modify it
using System;
using LuaInterface;

public class DG_Tweening_RotateModeWrap
{
	public static void Register(LuaState L)
	{
		<PERSON><PERSON>gin<PERSON>num(typeof(DG.Tweening.RotateMode));
		<PERSON><PERSON>("Fast", get_Fast, null);
		<PERSON><PERSON>("FastBeyond360", get_FastBeyond360, null);
		<PERSON><PERSON>("WorldAxisAdd", get_WorldAxisAdd, null);
		<PERSON><PERSON>("LocalAxisAdd", get_LocalAxisAdd, null);
		<PERSON><PERSON>un<PERSON>("IntToEnum", IntToEnum);
		<PERSON><PERSON>();
		TypeTraits<DG.Tweening.RotateMode>.Check = CheckType;
		StackTraits<DG.Tweening.RotateMode>.Push = Push;
	}

	static void Push(IntPtr L, DG.Tweening.RotateMode arg)
	{
		ToLua.Push(L, arg);
	}

	static bool CheckType(IntPtr L, int pos)
	{
		return TypeChecker.CheckEnumType(typeof(DG.Tweening.RotateMode), L, pos);
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_Fast(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.RotateMode.Fast);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_FastBeyond360(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.RotateMode.FastBeyond360);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_WorldAxisAdd(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.RotateMode.WorldAxisAdd);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int get_LocalAxisAdd(IntPtr L)
	{
		ToLua.Push(L, DG.Tweening.RotateMode.LocalAxisAdd);
		return 1;
	}

	[MonoPInvokeCallbackAttribute(typeof(LuaCSFunction))]
	static int IntToEnum(IntPtr L)
	{
		int arg0 = (int)LuaDLL.lua_tonumber(L, 1);
		DG.Tweening.RotateMode o = (DG.Tweening.RotateMode)arg0;
		ToLua.Push(L, o);
		return 1;
	}
}

