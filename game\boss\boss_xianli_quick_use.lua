BossXianliQuickUse = BossXianliQuickUse or BaseClass(SafeBaseView)

function BossXianliQuickUse:__init()
    self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(704, 488)})
    self:AddViewResource(0, "uis/view/boss_ui_prefab", "layout_xianli_quick_use")
    self:SetMaskBg(true, true)
    self.item_data_event = BindTool.Bind1(self.ItemDataChangeCallback, self)
end

function BossXianliQuickUse:ReleaseCallBack()
    self.list = nil
    if ItemWGData.Instance ~= nil then
		ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
	end
    if self.cell then
        self.cell:DeleteMe()
        self.cell = nil
    end
    if self.cell1 then
        self.cell1:DeleteMe()
        self.cell1 = nil
    end
end

function BossXianliQuickUse:ItemDataChangeCallback(item_id, index, reason, put_reason, old_num, new_num)
    if (self.list and self.list.consume_item and item_id == self.list.consume_item)
    or (self.list1 and self.list1.consume_item1 and item_id == self.list1.consume_item1)then
        self:Flush()
    end
end

function BossXianliQuickUse:LoadCallBack()
    self.node_list.title_view_name.text.text = Language.Boss.XianliAdd
    self.node_list.xianli_text.text.text = Language.Boss.XianLiNumText
    self.cell = ItemCell.New(self.node_list["cell"])
    self.cell1 = ItemCell.New(self.node_list["cell1"])
    self.node_list["Button"].button:AddClickListener(BindTool.Bind(self.OnClickUse, self))
    self.node_list["Button1"].button:AddClickListener(BindTool.Bind(self.OnClickUse1, self))
    ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
end

function BossXianliQuickUse:ShowIndexCallBack()
    self:Flush()
end

function BossXianliQuickUse:SetData(list, list1)
    self.list = list
    self.list1 = list1
    self:Open()
end

function BossXianliQuickUse:OnFlush()
    local item = self.list
    local num = ItemWGData.Instance:GetItemNumInBagById(item.consume_item)
    self.cell:SetData({item_id = item.consume_item})
    local color = num > 0 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    self.cell:SetRightBottomColorText(ToColorStr(num .. "/" .. 1, color))
    self.cell:SetRightBottomTextVisible(true)
    local cfg = ItemWGData.Instance:GetItemConfig(item.consume_item)
    self.node_list["name"].text.text = ToColorStr(cfg.name ,ITEM_COLOR[cfg.color])
    local desc = Language.Boss.XianLiItemDesc[item.consume_item]
    self.node_list["desc"].text.text = desc


    local item1 = self.list1
    local num1 = ItemWGData.Instance:GetItemNumInBagById(item1.consume_item1)
    self.cell1:SetData({item_id = item1.consume_item1})
    local color1 = num1 > 0 and ITEM_NUM_COLOR.ENOUGH or ITEM_NUM_COLOR.NOT_ENOUGH
    self.cell1:SetRightBottomColorText(ToColorStr(num1 .. "/" .. 1, color1))
    self.cell1:SetRightBottomTextVisible(true)
    local cfg1 = ItemWGData.Instance:GetItemConfig(item1.consume_item1)
    self.node_list["name1"].text.text = ToColorStr(cfg1.name ,ITEM_COLOR[cfg.color])
    local desc1 = Language.Boss.XianLiItemDesc[item1.consume_item1]
    self.node_list["desc1"].text.text = desc1

    local total_xianli = BossAssistWGData.Instance:GetBossXianli()
    local color = total_xianli > 0 and COLOR3B.C8 or COLOR3B.C10
    local max_xianli = BossWGData.Instance:GetBossXianliMax()
    local num_str = ToColorStr((total_xianli .. "/" .. max_xianli), color)
    self.node_list.xianli_num_text.text.text = string.format(Language.Boss.XianliDes2, num_str)
end

function BossXianliQuickUse:OnClickUse()
    local num = ItemWGData.Instance:GetItemNumInBagById(self.list.consume_item)
    if num > 0 then
        local item_index = ItemWGData.Instance:GetItemIndex(self.list.consume_item)
        if item_index == -1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.NotItemTop)
            return
        end
        BagWGCtrl.Instance:SendUseItem(item_index, 1)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.list.consume_item})
    end
end

function BossXianliQuickUse:OnClickUse1()
    local num = ItemWGData.Instance:GetItemNumInBagById(self.list1.consume_item1)
    if num > 0 then
        local item_index = ItemWGData.Instance:GetItemIndex(self.list1.consume_item1)
        if item_index == -1 then
            SysMsgWGCtrl.Instance:ErrorRemind(Language.NewAppearance.NotItemTop)
            return
        end
        BagWGCtrl.Instance:SendUseItem(item_index, 1)
    else
        TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.list1.consume_item1})
    end
end