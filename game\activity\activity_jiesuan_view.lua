ActivityJiesuanView = ActivityJiesuanView or BaseClass(SafeBaseView)

function ActivityJiesuanView:__init(view_name)
	self.view_style = ViewStyle.Half
	self.view_name = "ActivityJiesuanView"
	self.view_layer = UiLayer.Pop
	self:SetMaskBgAlpha(MASK_BG_ALPHA_TYPE.Normal)
	self:SetMaskBg(true, true)
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_jiesuan_panel")
	self:AddViewResource(0, "uis/view/activity_ui_prefab", "layout_act_jiesuan_win")

	self.act_type = nil
end

function ActivityJiesuanView:LoadCallBack()
	self:InitPanel()
	self:InitListener()
end

function ActivityJiesuanView:ReleaseCallBack()
	self.act_type = nil

	if self.fall_reward_list then
		self.fall_reward_list:DeleteMe()
		self.fall_reward_list = nil
	end

	if self.partipate_reward_list then
		self.partipate_reward_list:DeleteMe()
		self.partipate_reward_list = nil
	end
end

function ActivityJiesuanView:CloseCallBack()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.TIANSHEN_JIANLIN or scene_type == SceneType.XINGTIANLAIXI_FB or scene_type == SceneType.MOWU_JIANLIN then
		FuBenWGCtrl.Instance:SendLeaveFB()
	end
end

function ActivityJiesuanView:SetActType(act_type)
	self.act_type = act_type
end

function ActivityJiesuanView:OnFlush(param_t)
	self:RefreshView()
	self:AutoCloseView()
end

function ActivityJiesuanView:InitPanel()
	self.fall_reward_list = AsyncListView.New(ItemCell, self.node_list.fall_reward_list)
	self.partipate_reward_list = AsyncListView.New(ItemCell, self.node_list.partipate_reward_list)
end

function ActivityJiesuanView:InitListener()
	XUI.AddClickEventListener(self.node_list.sure_btn, BindTool.Bind1(self.Close, self))
end

function ActivityJiesuanView:RefreshView()
	self.node_list.victory:CustomSetActive(true)
	local jiesuan_info = nil
	--local res_name = nil
	if self.act_type == ACTIVITY_TYPE.TIANSHENJIANLIN then
		--res_name = "ts_tianshenjianglin"
		jiesuan_info = TianshenRoadWGData.Instance:GetTSJLFinishInfo()
	elseif self.act_type == ACTIVITY_TYPE.XINGTIANLAIXI then
		--res_name = "zi_mowan"
		jiesuan_info = QuanMinBeiZhanWGData.Instance:GetXTLXFinishInfo()
	elseif self.act_type == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_XINGTIANLAIXI_CHILD then
		--res_name = "zi_mowan"
		jiesuan_info = ActXianQiJieFengWGData.Instance:GetXTLXFinishInfo()
	elseif self.act_type == ACTIVITY_TYPE.CLIENT_MOWUJINGLIN then
		--res_name = "zi_mowan"
		jiesuan_info = MoWuJiangLinWGData.Instance:GetMWJLFinishInfo()
	elseif self.act_type == ACTIVITY_TYPE.WORLD_TREASURE then
		jiesuan_info = WorldTreasureWGData.Instance:GetTSJLFinishInfo()
	end
	if not jiesuan_info then
		return
	end

	-- if res_name then
	-- 	local bundle, asset = ResPath.GetF2RawImagesPNG(res_name)
	-- 	self.node_list.win_title_img.raw_image:LoadSprite(bundle, asset, function ()
	-- 		self.node_list.win_title_img.raw_image:SetNativeSize()
	-- 	end)
	-- end

	if IsEmptyTable(jiesuan_info.fall_item_list) then
		self.node_list.fall_info_panel:SetActive(false)
	else
		self.fall_reward_list:SetDataList(jiesuan_info.fall_item_list)
		self.node_list.fall_info_panel:SetActive(true)
	end
	
	if IsEmptyTable(jiesuan_info.partipate_list) then
		self.node_list.partipate_panel:SetActive(false)
	else
		self.partipate_reward_list:SetDataList(jiesuan_info.partipate_list)
		self.node_list.partipate_panel:SetActive(true)
	end
end

function ActivityJiesuanView:AutoCloseView()
	CountDownManager.Instance:RemoveCountDown("activity_jiesuan_close_count_down")
	self.node_list.close_tip.text.text = string.format(Language.TianShenRoad.DaLianStr3, 10)
	CountDownManager.Instance:AddCountDown("activity_jiesuan_close_count_down", 
        function(elapse_time, total_time)
            if self.node_list.close_tip then
                self.node_list.close_tip.text.text = string.format(Language.TianShenRoad.DaLianStr3, math.ceil(total_time - elapse_time))
            end
		end, BindTool.Bind1(self.CountDownComplete, self), nil, 10, 1)
end

function ActivityJiesuanView:CountDownComplete()
	self:Close()
end