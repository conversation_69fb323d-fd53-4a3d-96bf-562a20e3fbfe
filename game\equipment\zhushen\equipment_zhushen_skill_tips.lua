EquipmentZhuShenSkillTips = EquipmentZhuShenSkillTips or BaseClass(SafeBaseView)
function EquipmentZhuShenSkillTips:__init()
    self.view_layer = UiLayer.Pop
    self:SetMaskBg(true, true)
    self:AddViewResource(0, "uis/view/equipment_ui_prefab", "zhushen_equip_skill_tip")

    self.show_data = {}
end

function EquipmentZhuShenSkillTips:LoadCallBack()
    XUI.AddClickEventListener(self.node_list.skill_levelup_btn, BindTool.Bind1(self.OnClickUpSkillLevel, self))
end

function EquipmentZhuShenSkillTips:ReleaseCallBack()
    self.show_data = {}
end

function EquipmentZhuShenSkillTips:SetShowData(show_data)
    self.show_data = show_data
end

function EquipmentZhuShenSkillTips:OnClickUpSkillLevel()
    if IsEmptyTable(self.show_data) then
        return
    end

    EquipmentWGCtrl.Instance:SendEquipZhuShenOperate(CAST_SOUL_OPERATE_TYPE.SUIT_LEVEL_UP, self.show_data.skill_seq)
end

function EquipmentZhuShenSkillTips:OnFlush()
    if IsEmptyTable(self.show_data) then
        return
    end

    local is_tips = false
    local skill_dsc, skill_next_dsc, skill_name, limit_desc --技能信息
    local skill_level = EquipmentWGData.Instance:GetZhuShenEquipSkillLevel(self.show_data.skill_seq)
    local cur_level = skill_level < 1 and skill_level + 1 or skill_level

    if self.show_data.is_tips then --从技能详情点进来
        local suit_level = EquipmentWGData.Instance:GetZhuShenAllEquipNextLevel()
        local show_level = EquipmentWGData.Instance:GetZhuShenSkillSuitLevelCfg(suit_level)
        cur_level = show_level
        is_tips = true

        skill_name = self.show_data.skill_name .. string.format(Language.EquipmentZhuShen.ZhuShenTaiHoleLevel, cur_level)
    else
        skill_name = skill_level < 1 and self.show_data.skill_name or self.show_data.skill_name .. string.format(Language.EquipmentZhuShen.ZhuShenTaiHoleLevel, cur_level)
    end

    local max_level = EquipmentWGData.Instance:GetZhuShenSkillMaxLevelCfg(self.show_data.skill_seq)
    local is_skill_up = EquipmentWGData.Instance:GetEquipZhuShenSkillRedmind(self.show_data.skill_seq) --是否有红点
    local skill_cfg = EquipmentWGData.Instance:GetZhuShenSkillLevelCfg(self.show_data.skill_seq, cur_level)
    local is_next_cfg = cur_level < max_level --是否有下一级属性
    local is_next_show = skill_level > 0 and is_next_cfg
    local next_skill_cfg
    local btn_up_text


    if is_next_cfg then
        next_skill_cfg = EquipmentWGData.Instance:GetZhuShenSkillLevelCfg(self.show_data.skill_seq, cur_level + 1)
        skill_next_dsc = next_skill_cfg.skill_describe
    end

    skill_dsc = skill_cfg.skill_describe
    btn_up_text = skill_level < 1 and Language.EquipmentZhuShen.ZhuHunUpBtnTxt1 or Language.EquipmentZhuShen.ZhuHunUpBtnTxt2

    if not is_skill_up and skill_level < 1 then --未解锁
        limit_desc = string.format(Language.EquipmentZhuShen.ZhuHunSkillTipsLimit, skill_cfg.need_level)
    elseif not is_skill_up and skill_level > 0 and is_next_cfg then --已解锁未升级
        local skill_name_desc = self.show_data.skill_name .. string.format(Language.EquipmentZhuShen.ZhuShenTaiHoleLevel, cur_level + 1)
        limit_desc = string.format(Language.EquipmentZhuShen.ZhuHunSkillTipsNextLimit, next_skill_cfg.need_level, skill_name_desc)
    elseif not is_next_cfg then --技能已升级
        limit_desc = Language.EquipmentZhuShen.MaxSkillDesc
    end

    if is_skill_up then
        limit_desc = ""
    end

    if self.show_data.icon ~= nil then
		XUI.SetSkillIcon(self.node_list.ph_ml_skill_bg, self.node_list.ph_ml_skill_item, self.show_data.icon)
	end

    -- local item_cfg = ItemWGData.Instance:GetItemConfig(self.show_data.icon)
    -- local name_str
    -- if item_cfg then
    --     name_str = ToColorStr(self.show_data.skill_name, ITEM_COLOR[item_cfg.color])
    -- end

    self.node_list["skill_name"].text.text = skill_name
    self.node_list["skill_dsc"].text.text = skill_dsc

    self.node_list["next_desc"]:SetActive(is_next_show and (not is_tips))
    self.node_list["skill_dsc_nextLevel"].text.text = is_next_show and skill_next_dsc or ""

    self.node_list["skill_levelup_btn"]:SetActive(is_skill_up and (not is_tips))
    self.node_list["limit_text"]:SetActive(not is_skill_up and (not is_tips))
    self.node_list["limit_text"].text.text = limit_desc

    self.node_list["text_btn"].text.text = btn_up_text
end