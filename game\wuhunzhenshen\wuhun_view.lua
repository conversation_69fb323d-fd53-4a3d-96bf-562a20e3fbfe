WuHunView = WuHunView or BaseClass(SafeBaseView)

function WuHunView:__init()
	self:SetMaskBg(false)
	self.view_style = ViewStyle.Full
	self.is_safe_area_adapter = true

	self.default_index = TabIndex.wuhun_details
	local bundle_name = "uis/view/wuhunzhenshen_prefab"
	local common_bundle_name = "uis/view/common_panel_prefab"
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_panel")
	self:AddViewResource(TabIndex.wuhun_details, bundle_name, "layout_tianshen_wuhun")
	self:AddViewResource(TabIndex.wuhun_tower, bundle_name, "layout_wuhun_tower")
	self:AddViewResource(TabIndex.wuhun_front, bundle_name, "layout_wuhun_front")
	self:AddViewResource(0, common_bundle_name, "VerticalTabbar")
	self:AddViewResource(0, common_bundle_name, "layout_a3_common_top_panel")

	self.remind_tab =
	{
		{ RemindName.WuHunDetails },
		{ RemindName.WuHunTower },
		{ RemindName.WuHunFront },
	}

	self.jump_open = false
	self:WuHunDetailsInit()
	self:WuHunFrontInit()
end

function WuHunView:__delete()

end

function WuHunView:ReleaseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:DeleteMe()
		self.tabbar = nil
	end

	self.jump_open = false
	self.is_open = false

	if MainuiWGCtrl.Instance then
		MainuiWGCtrl.Instance:CacheTableClearData()
	end

	self:WuHunDetailsReleseCallBack()
	self:WuHunTowerReleseCallBack()
	self:WuHunFrontReleseCallBack()

	FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.WuHunView, self.get_guide_ui_event)
	self.get_guide_ui_event = nil
end

function WuHunView:LoadCallBack()
	self.tabbar = Tabbar.New(self.node_list)
	self.tabbar:SetVerTabbarIconStr("wuhun_view")
	self.tabbar:Init(Language.WuHunZhenShen.TabGrop, nil, nil, nil, self.remind_tab)
	self.tabbar:SetSelectCallback(BindTool.Bind1(self.ChangeToIndex, self))
	FunOpen.Instance:RegsiterTabFunUi(GuideModuleName.WuHunView, self.tabbar)

	--退出按钮
	self.node_list["btn_close_window"].button:AddClickListener(BindTool.Bind(self.Close, self))
	self.node_list.btn_rule_tips:CustomSetActive(true)
	XUI.AddClickEventListener(self.node_list.btn_rule_tips, BindTool.Bind(self.OnClickBtnWuHunTip, self))
	self.get_guide_ui_event = BindTool.Bind1(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.WuHunView, self.get_guide_ui_event)
end

function WuHunView:LoadIndexCallBack(index)
	if index == TabIndex.wuhun_details then
		self:WuHunDetailsLoadCallBack()
	elseif index == TabIndex.wuhun_tower then
		self:WuHunTowerLoadCallBack()
	elseif index == TabIndex.wuhun_front then
		self:WuHunFrontLoadCallBack()
	end
end

function WuHunView:OpenCallBack()
	if nil ~= self.tabbar then
		self.tabbar:AddAllListen()
	end
end

function WuHunView:CloseCallBack()
	if nil ~= self.tabbar then
		self.tabbar:UnAllListen()
	end
end

function WuHunView:ShowIndexCallBack(index)
	self:SetTabVisible()

	self.is_oneKey = false
	local bundle, assert = ResPath.GetF2RawImagesPNG("a3_wh_bj")
	local title = Language.WuHunZhenShen.TitleName

	if index == TabIndex.wuhun_details then
		bundle, assert = ResPath.GetF2RawImagesPNG("a3_ftxd_bjt1")
		title = Language.WuHunZhenShen.TitleName
	elseif index == TabIndex.wuhun_tower then
		self:WuHunTowerShowIndexCallBack()
		title = Language.WuHunZhenShen.TitleName1
	elseif index == TabIndex.wuhun_front then
		self:WuHunFrontShowIndexCallBack()
		title = Language.WuHunZhenShen.TitleName2
		bundle, assert = ResPath.GetF2RawImagesPNG("a2_wh_sxd_bj")
	end

	self.node_list.title_view_name.text.text = title
	self.node_list["RawImage_tongyong"].raw_image:LoadSprite(bundle, assert, function()
		self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
	end)
end

function WuHunView:SetTabVisible()
	if nil == self.tabbar then
		return
	end

	self.tabbar:SetVerBGVisble(false)
	self.tabbar:SetVerToggleVisble(TabIndex.wuhun_details, false) --屏蔽掉
end

function WuHunView:SpecialAppearanceRemindChange(name, num)

end

function WuHunView:OnFlush(param_t, index)
	if index ~= TabIndex.wuhun_front then
		self.is_front_auto_strenge = false --魂阵界面
	end

	if index == TabIndex.wuhun_details then
		self:FlushWuHunJump()
	elseif index == TabIndex.wuhun_tower then
		self:FlushWuHunTowerJump(param_t)
	elseif index == TabIndex.wuhun_front then
		self:FlushWuHunFrontJump(param_t)
	end
end

--物品变化
function WuHunView:ItemChangeFlush()
	if not self:IsLoadedIndex(self.show_index) then
		return
	end

	if self.show_index == TabIndex.wuhun_details then
		self:WuHunSpendFlush()
		self:SetWuHunPropertyInfo()
	end
end

function WuHunView:OnClickBtnWuHunTip()
	local rule_tip = RuleTip.Instance
	local rule_title = ""
	local rule_content = ""

	if self.show_index == TabIndex.wuhun_details then
		rule_title = Language.WuHunZhenShen.TitleName
		rule_content = Language.WuHunZhenShen.WuhunRuleTips
	elseif self.show_index == TabIndex.wuhun_tower then
		rule_title = Language.WuHunZhenShen.TitleName1
		rule_content = Language.WuHunZhenShen.WuhunTowerRuletips
	elseif self.show_index == TabIndex.wuhun_front then
		rule_title = Language.WuHunZhenShen.TitleName2
		rule_content = Language.WuHunZhenShen.WuhunFrontRuletips
	end

	rule_tip:SetTitle(rule_title)
	rule_tip:SetContent(rule_content, nil, nil, nil, true)
end

function WuHunView:GetGuideUiCallBack(ui_name, ui_param, try_times, guide_cfg)
	return self.node_list[ui_name]
end
