ExchangeShopWGData = ExchangeShopWGData or BaseClass()
local this = ExchangeShopWGData
function this:__init()
	ExchangeShopWGData.Instance = self
	self.data = {}
	self.period = 1
	self:LoadConfig()
	RemindManager.Instance:Register(RemindName.ExchangeShop, BindTool.Bind(self.IsShowRemind, self))
	OperationActivityWGData.Instance:SetActivityOpenCallBack(ACTIVITY_TYPE.OPERA_ACT_EXCHANGE,{[1] = OPERATION_EVENT_TYPE.LEVEL},
			BindTool.Bind(self.GetActCanOpen, self),
			BindTool.Bind(self.IsShowRemind, self))

    self.config_param = ConfigManager.Instance:GetAutoConfig("act_common_convert_auto").period
	self:RegisterRemindInBag(RemindName.ExchangeShop)
end

function this:__delete()
	RemindManager.Instance:UnRegister(RemindName.ExchangeShop)
end

function this:RegisterRemindInBag(remind_name)
    local map = {}

    local cfg = ConfigManager.Instance:GetAutoConfig("act_common_convert_auto").item
    for k,v in pairs(cfg) do
    	map[v.reward_item.item_id] = true
    end

    local item_id_list = {}
    for k,v in pairs(map) do
        table.insert(item_id_list, k)
    end

    BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

-- 获取数据对象
function this:GetData()
	return self.data
end

function this:LoadConfig()
	local config = ConfigManager.Instance:GetAutoConfig("act_common_convert_auto")
	if config then
		self.item_cfg = ListToMap(config.item, "period", "sort")
		self.show_cfg = config.show
		self.interface_cfg = config.interface
	end
	self:InitData()
end

-- 初始化数据
function this:InitData()
	self.data = {}
	self.data.show_remind = 0
	local cur_cfg = self.show_cfg[self.period] or self.show_cfg[1]
	self.data.subtab_id = cur_cfg.subtab_id
	self.data.head_picture_icon = cur_cfg.head_picture_icon
	self.data.picture_id = cur_cfg.show_picture
	self.data.picture_bundle = cur_cfg.show_picture_bundle
	self.data.model_id = cur_cfg.show_model
	self.data.show_item_id = cur_cfg.show_item_id
	self.data.show_model = cur_cfg.show_model ~= "" and self.data.show_item_id == ""
	self.data.show_model_bundle = cur_cfg.show_model_bundle
	local position = Split(cur_cfg.position, "|")
	self.data.position = Vector3.New(position[1], position[2], position[3])
	local rotation = Split(cur_cfg.rotation, "|")
	self.data.rotation = Vector3(rotation[1], rotation[2], rotation[3])
	self.data.scale = cur_cfg.scale
	self.data.rule_desc = cur_cfg.rule_desc
	self.data.btn_rule_title = cur_cfg.btn_rule_title
	self.data.out_side_rule = cur_cfg.out_side_rule

	local interface_cfg = ExchangeShopWGData.Instance:GetInterfaceCfg()
	local list = CheckList(self.item_cfg, self.period) or {}
	local exchange_shop_list = {}
	for i = 1, #list do
		local cfg = CheckList(self.item_cfg, self.period, i)
		local data = {}
		data.item = cfg.reward_item
		local item_cfg = ItemWGData.Instance:GetItemConfig(data.item.item_id)
		data.name = item_cfg and item_cfg.name or ""
		data.max_exchange_times = cfg.limit_num
		data.can_exchange_times = cfg.limit_num
		data.cost = cfg.stuff_count
		data.stuff_id = cfg.stuff_id
		data.stuff_icon = cfg.stuff_icon
		data.seq = cfg.seq
		data.item_bg = interface_cfg and interface_cfg.item_bg
		exchange_shop_list[i] = data
	end
	self.exchange_shop_list = exchange_shop_list

	self.shop_list_num = #list
end

function this:SaveData(protocol)
	if protocol.cur_act_period ~= self.period then
		self.period = protocol.cur_act_period
		self:InitData()
	end

	--触发一下开启。。
	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.OPERA_ACT_EXCHANGE)
	if act_info then
		OperationActivityWGData.Instance:SetAleardayOpenActivity(ACTIVITY_TYPE.OPERA_ACT_EXCHANGE, act_info.status)
	end

	for k, data in ipairs(self.exchange_shop_list) do
		data.can_exchange_times = data.max_exchange_times - protocol.record_list[data.seq]
	end

	self.exchange_shop_list = TableSortByCondition(self.exchange_shop_list, function (value)
		return value.can_exchange_times ~= 0
	end)
end

-- 处理数据
function this:DisposeData(cur_data)

end

function this:GetInterfaceCfg()
	local cur_zhuti = OperationActivityWGData.Instance:GetCurThemeType()
	return self.interface_cfg[cur_zhuti]
end

-- 红点判断
function this:IsShowRemind()
	if not OperationActivityWGData.Instance:GetActivityState(ACTIVITY_TYPE.OPERA_ACT_EXCHANGE) then
		return 0
	end
	for k, data in pairs(self.exchange_shop_list) do
		if data.can_exchange_times > 0 then
			local num = ItemWGData.Instance:GetItemNumInBagById(data.stuff_id)
			if num >= data.cost then
				return 1
			end
		end
	end
	return 0
end

-- WGCtrl层物品数据改变调用
function this:CheckItemNum()

end

function this:GetShopListNum()
	return self.shop_list_num
end

function this:GetExchangeShopList()
	return self.exchange_shop_list
end

function this:CheckPeriod()

end

function this:GetActivityCanOpenDay()
	local is_open = false
    local open_day = OperationActivityWGData.Instance:GetOpenDayByAct(ACTIVITY_TYPE.OPERA_ACT_EXCHANGE)
    local open_time = OperationActivityWGData.Instance:GetOpenTimeByAct(ACTIVITY_TYPE.OPERA_ACT_EXCHANGE)
    local week = TimeUtil.FormatSecond3MYHM1(open_time)--获取当前是周几
	for k,v in pairs(self.config_param) do
		if v.begin_server_day <= open_day and open_day <= v.server_day and week == v.week_index then
			is_open = true
			break
		end
	end
	return is_open
end

function this:GetActCanOpen()
    if not self:GetActivityCanOpenDay() then
        return false
    end

	local vo = GameVoManager.Instance:GetMainRoleVo()
	local cur_cfg = self.show_cfg[self.period] or self.show_cfg[1]
	if vo.level >= cur_cfg.open_level then
		return true
	end
	return false
end