{"actorController": {"projectiles": [], "hurts": [], "beHurtEffecct": {"BundleName": "", "AssetName": "", "AssetGUID": "", "IsEmpty": true}, "hurtEffectName": "", "beHurtNodeName": "", "beHurtAttach": false}, "actorTriggers": {"effects": [{"triggerEventName": "attack1/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8053_attack", "effectAsset": {"BundleName": "effects/prefab/model/boss/8053/boss8053_attack_prefab", "AssetName": "boss8053_attack", "AssetGUID": "b27bdc9cd0eddcd4fb84415ceb71723e", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack1", "playerAtPos": false, "ignoreParentScale": true}, {"triggerEventName": "attack2/begin", "triggerDelay": 0.0, "triggerFreeDelay": 0.0, "effectGoName": "boss8053_skill", "effectAsset": {"BundleName": "effects/prefab/model/boss/8053/boss8053_skill_prefab", "AssetName": "boss8053_skill", "AssetGUID": "ca942b1eec7b24848a5386e11800e65e", "IsEmpty": false}, "playerAtTarget": false, "referenceNodeHierarchyPath": "", "isAttach": false, "isRotation": true, "isUseCustomTransform": false, "offsetPosX": 0.0, "offsetPosY": 0.0, "offsetPosZ": 0.0, "triggerStopEvent": "[none]", "effectBtnName": "attack2", "playerAtPos": false, "ignoreParentScale": true}], "sounds": [], "cameraShakes": [], "radialBlurs": []}}