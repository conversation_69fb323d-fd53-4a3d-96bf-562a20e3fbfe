ActwishingWGData = ActwishingWGData or BaseClass()

function ActwishingWGData:__init()
	if ActwishingWGData.Instance ~= nil then
		print("[ActwishingWGData] attempt to create singleton twice!")
		return
	end
	ActwishingWGData.Instance = self
	self.wish_pool_process =0 
	self.open_act_day = 1
	self.act_wish_info = {}
end

function ActwishingWGData:__delete()
	ActwishingWGData.Instance = nil
end

-- 读许愿池表
function ActwishingWGData:GetWishPoolData()

	self.act_cfg = {}
	local cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local pool_t = cfg.wish_pool
	-- local act_day = self:GetWishPoolDay() --取消天数
	for k,v in pairs(pool_t) do
		if (v.show_icon and v.show_icon == 1) then
			table.insert(self.act_cfg, v)
		end
	end
	return self.act_cfg
end
--读其他表
function ActwishingWGData:GetWishPoolOtherData()
	local other_cfg = ServerActivityWGData.Instance:GetCurrentRandActivityConfig()
	local pool_t = other_cfg.other
    return pool_t
end
function ActwishingWGData:GetWishPoolPeizhiData()
	local cfg = ConfigManager.Instance:GetAutoConfig("randactivityopencfg_auto")
	local pool_t = cfg.open_cfg
    return pool_t
end

function ActwishingWGData:GetCirculationWishInfo()
	return self.act_wish_info or {}
end

function ActwishingWGData:UpdataInfoData(protocol)
	self.wish_pool_process = protocol.wish_pool_process
	self.open_act_day = protocol.open_act_day
	self.is_finish = protocol.is_finish
end

function ActwishingWGData:GetWishPoolProcess()
	return self.wish_pool_process
end
function ActwishingWGData:GetWishPoolDay()
	return self.open_act_day
end
function ActwishingWGData:GetIsFinish()
	return self.is_finish
end