OperationActivityView = OperationActivityView or BaseClass(SafeBaseView)

SHOW_EFFECT_LEVEL = 3

 function OperationActivityView:LoadIndexCallBackChuShen()
 	XUI.AddClickEventListener(self.node_list["chushen_select_btn_1"], BindTool.Bind(self.ChuShenSelecType, self, 1))
 	XUI.AddClickEventListener(self.node_list["chushen_select_btn_2"], BindTool.Bind(self.ChuShenSelecType, self, 2))
 	XUI.AddClickEventListener(self.node_list["chushen_rank_panel_btn"], BindTool.Bind(self.ChuShenRankInfo, self))
 	XUI.AddClickEventListener(self.node_list["chushen_cook_btn"], BindTool.Bind(self.ChuShenCookSend, self))
 	XUI.AddClickEventListener(self.node_list["chushen_remind_btn"], BindTool.Bind(self.ChuShenRemindSend, self))
 	XUI.AddClickEventListener(self.node_list["chushen_unlockreward_btn"], BindTool.Bind(self.OpenUnlockRewardPanel, self))
 	XUI.AddClickEventListener(self.node_list["chushen_ani_btn"], BindTool.Bind(self.SendAniMark, self))
 	XUI.AddClickEventListener(self.node_list["chushen_unlockreward_tips_btn"], BindTool.Bind(self.SendTodayMark, self))
 	UITween.MoveLoop(self.node_list["chushen_unlockreward_tips_btn"], Vector2(-378, -195), Vector2(-378, -207), 0.9)

 	local name_text = self.node_list["chushen_unlockreward_tips_btn"]:FindObj("Text")
 	name_text.text.text = Language.Activity.ChuShenMaterialQiPao
 	for i=1, 3 do
 		XUI.AddClickEventListener(self.node_list["chushen_add_"..i], BindTool.Bind(self.PanelAddClick, self, i))
 	end

 	 if not self.chushen_rank_list then
		self.chushen_rank_list = AsyncListView.New(ChuShenRankRender, self.node_list.chushen_rank_list)
	end

	 if not self.chushen_reward_list then
		self.chushen_reward_list = AsyncListView.New(ChuShenRewardRender, self.node_list.chushen_reward_list)
	end

	 if not self.chushen_material_list then
		self.chushen_material_list = AsyncListView.New(ChuShenMaterialRender, self.node_list.chushen_material_list)
		self.chushen_material_list:SetSelectCallBack(BindTool.Bind1(self.OnClickMaterialHandler, self))
	end

	if not self.chushen_material_list_1 then
		self.chushen_material_list_1 = AsyncListView.New(ChuShenMaterialRenderShiCai, self.node_list.chushen_material_list_1)
		self.chushen_material_list_1:SetSelectCallBack(BindTool.Bind1(self.OnClickMaterialHandler, self))
	end
	self.node_list.fire_parti:SetActive(false)
	self.gaizi_origin_pos = self.node_list["chushen_gaizi_image"].rect.anchoredPosition
	self.chushen_add_list = {} --锅炉放置的材料
	self.chushen_add_cell_list = {}
	self.cur_menu_list = {} --当前选中的菜谱
	self.move_cell_list = {}

 	self:ChuShenSelecType(1)
 	ChuShenWGCtrl.Instance:ChuShenInfoReq()
 	self.send_cook_ani = false
 	self.is_full_count = false

 	self:InitPictureAndDescText()
 end 

--SetRawImageMoWang: node_name, res_path, asset_name, is_async, not_set_native_size
--SetImageMoWang: node_name, res_path, asset_name, is_async
 function OperationActivityView:InitPictureAndDescText()
 	local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
	if not interface_cfg or not self.node_list.fire_parti then
		return 
	end

	self:SetImageMoWang("chushen_rank_image", ResPath.GetChuShenImg, interface_cfg.pic_2)
	self:SetImageMoWang("chushen_rank_panel_btn", ResPath.GetChuShenImg, interface_cfg.pic_1)
	self:SetImageMoWang("chushen_rank_bg", ResPath.GetChuShenImg, interface_cfg.pic_3, nil, true)
	--self:SetImageMoWang("cook_center_special", ResPath.GetChuShenImg, interface_cfg.pic_6)
	self:SetImageMoWang("cook_center_desc_image", ResPath.GetChuShenImg, interface_cfg.pic_7)
	self:SetImageMoWang("chushen_unlockreward_tips_btn", ResPath.GetChuShenImg, interface_cfg.pic_18)
	-- self:SetImageMoWang("chushen_material_select", ResPath.GetChuShenImg, interface_cfg.pic_11, nil, true)
	-- self:SetImageMoWang("chushen_remind_arrow", ResPath.GetChuShenImg, interface_cfg.pic_12)
	self:SetImageMoWang("chushen_select_image_1", ResPath.GetChuShenImg, interface_cfg.pic_8)
	self:SetImageMoWang("chushen_select_image_2", ResPath.GetChuShenImg, interface_cfg.pic_44)
	self:SetImageMoWang("chushen_select_btn_1", ResPath.GetChuShenImg, interface_cfg.pic_9)
	self:SetImageMoWang("chushen_select_btn_2", ResPath.GetChuShenImg, interface_cfg.pic_45)
	self:SetImageMoWang("chushen_cook_btn", ResPath.GetChuShenImg, interface_cfg.pic_13)
	-- self:SetImageMoWang("shicai_name_image", ResPath.GetChuShenImg, interface_cfg.pic_15)
	-- self:SetImageMoWang("menu_name_image", ResPath.GetChuShenImg, interface_cfg.pic_16)
	self:SetImageMoWang("chushen_peishi_1", ResPath.GetChuShenImg, interface_cfg.pic_17)
	self:SetImageMoWang("chushen_peishi_2", ResPath.GetChuShenImg, interface_cfg.pic_17)
	self:SetImageMoWang("chushen_unlockreward_btn", ResPath.GetChuShenImg, interface_cfg.pic_19)
	self:SetImageMoWang("chushen_num_text_bg_1", ResPath.GetChuShenImg, interface_cfg.pic_25)
	self:SetImageMoWang("chushen_num_text_bg_2", ResPath.GetChuShenImg, interface_cfg.pic_25)
	self:SetImageMoWang("chushen_num_text_bg_3", ResPath.GetChuShenImg, interface_cfg.pic_25)
	for i=1, 3 do
		self:SetImageMoWang("chushen_add_"..i, ResPath.GetChuShenImg, interface_cfg.pic_27)
 	end

	self:SetRawImageMoWang("chushen_gaizi_image", ResPath.GetF2RawImagesPNG, interface_cfg.pic_5)
	self:SetRawImageMoWang("luzi_bg", ResPath.GetF2RawImagesPNG, interface_cfg.pic_99)
	self:SetRawImageMoWang("huochai_bg", ResPath.GetF2RawImagesPNG, interface_cfg.pic_20)
	self:SetRawImageMoWang("chushen_reward_list_bg", ResPath.GetF2RawImagesPNG, interface_cfg.pic_14)
	self:SetRawImageMoWang("chushen_list_big_bg", ResPath.GetF2RawImagesPNG, interface_cfg.pic_10)
	self:SetRawImageMoWang("big_raw_bg", ResPath.GetF2RawImagesPNG, interface_cfg.pic_4)
	local b,a = ResPath.GetEffectUi(interface_cfg.pic_22)
	self.node_list.fire_parti:ChangeAsset(b, a)

end

function OperationActivityView:DeleteChuShen()
	if self.chushen_rank_list then
		self.chushen_rank_list:DeleteMe()
		self.chushen_rank_list = nil
	end

	self.send_cook_ani = nil
	self.reward_tween = nil
	self.is_full_count = nil
	self.gaizi_origin_pos = nil
 	
	if self.chushen_reward_list then
		self.chushen_reward_list:DeleteMe()
		self.chushen_reward_list = nil
	end

	if self.chushen_material_list then
		self.chushen_material_list:DeleteMe()
		self.chushen_material_list = nil
	end

	if self.center_cook_cell then
		self.center_cook_cell:DeleteMe()
		self.center_cook_cell = nil
	end

	if self.chushen_material_list_1 then
		self.chushen_material_list_1:DeleteMe()
		self.chushen_material_list_1 = nil
	end

	self.chushen_select_type = nil
 end

 function OperationActivityView:OnClickMaterialHandler(item)
 	local data = item:GetData()
 	if nil == data then 
 		return
 	end

 	local real_num = 0
 	self.chushen_add_list, real_num = ChuShenWGData.Instance:GetChuShenSelectData()
 	if self.chushen_select_type == 1 then
 		self.node_list.chushen_material_select:SetActive(false)
	 	if real_num >= 3 then
	 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenMaterialEnoughNum)
	 		return 
	 	end

	 	self.is_full_count = false
		local tem_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
		local num = 0
		for k,v in pairs(self.chushen_add_list) do
			if v.item_id == data.item_id then
				num = num + 1
			end
		end
		
		if tem_num <= num then
			TipWGCtrl.Instance:OpenItem(data, ItemTip.FROM_NORMAL, nil, nil, nil)
			return
		end

		local has_add_num = ChuShenWGData.Instance:GetLastNum(data.item_id)
		if data.is_special == 1 and has_add_num > 0 then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenMaterialNumLimit)
			return 
		end

		local move_cell_index = 1
		for i=1, 3 do
			if nil == self.chushen_add_list[i] then
				self.chushen_add_list[i] = data
				move_cell_index = i
				break
			end
		end
		self.node_list["chushen_move_image_"..move_cell_index]:SetActive(true)
		local bundle, assest = ResPath.GetCommonImages("a2_sc_btn_bg_"..data.quality_color)
		self.node_list["move_cell_bg_"..move_cell_index].image:LoadSprite(bundle, assest, function()
				XUI.ImageSetNativeSize(self.node_list["move_cell_bg_"..move_cell_index])
			end) 
		local parti_b, parti_a = self:GetPartiAsesst(data.quality_color)
		self.node_list["move_cell_parti_"..move_cell_index]:ChangeAsset(parti_b, parti_a)
		self.node_list["move_cell_parti_"..move_cell_index]:SetActive(data.quality_color > SHOW_EFFECT_LEVEL)
		local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(data.item_id)
		local b,a = ResPath.GetItem(item_cfg.icon_id)
		self.node_list["move_cell_item_image_"..move_cell_index].image:LoadSprite(b, a, function()
			XUI.ImageSetNativeSize(self.node_list["move_cell_item_image_"..move_cell_index])
		end) 
		local start_image = item:GetPosNode()
		local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(UICamera, start_image.rect.position)
		local _, local_pos_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(self.node_list.material_group.rect, screen_pos_tbl, UICamera, Vector2(0, 0))
		local end_pos_x, end_pos_y = self.node_list["chushen_add_"..move_cell_index].rect.anchoredPosition.x,  self.node_list["chushen_add_"..move_cell_index].rect.anchoredPosition.y
		self.node_list["chushen_move_image_"..move_cell_index].rect.anchoredPosition = Vector2(local_pos_tbl.x, local_pos_tbl.y)
		
		local tween = self.node_list["chushen_move_image_"..move_cell_index].rect:DOLocalMove(Vector2(end_pos_x, end_pos_y), 0.3):OnComplete(function()
	        self.node_list["chushen_move_image_"..move_cell_index]:SetActive(false)
	        local ann_count = 0
			for k,v in pairs(self.chushen_add_list) do
				if v then
					ann_count = ann_count + 1
				end
			end
			
	 		ChuShenWGData.Instance:SetPanelAddMark(false)
	 		ChuShenWGData.Instance:SetChuShenSelectData(self.chushen_add_list)
	 		item:SetAddArrowShow()
	 		self:FlushChuShen()
	    end)
	else
		self.is_full_count = item:IsFullCount()
		if self.is_full_count then
			SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenCookMaxNum)
			return
		end
		local cfg = data.cfg
		if IsEmptyTable(self.chushen_add_list) then

			self.chushen_add_list = cfg
			local color = ITEM_COLOR[cfg.menu_quality]
			local menu_name = ToColorStr(cfg.menu_name, color)
			self.node_list.cook_center_desc.text.text = menu_name
			self.node_list.chushen_remind_btn:SetActive(true)
		else
			if self.chushen_add_list.id == cfg.id then   --取消选中
				self.chushen_add_list = {}
				self.node_list.chushen_remind_btn:SetActive(false)
				self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialAddEnough 
				self:ChuShenPanelResert()
				self.is_full_count = false
			else
				self.chushen_add_list = cfg
				local color = ITEM_COLOR[cfg.menu_quality]
				local menu_name = ToColorStr(cfg.menu_name, color)
				self.node_list.cook_center_desc.text.text = menu_name
				self.node_list.chushen_remind_btn:SetActive(true)
			end
		end
		ChuShenWGData.Instance:SetChuShenSelectData(self.chushen_add_list)
		self:ChuShenPreviousReward()
		self:FlushChuShen()
	end

 end


 function OperationActivityView:GetPartiAsesst(color)
 	local parti_name = BaseCell_Ui_Circle_Effect[color]
 	if parti_name then
 		return ResPath.GetWuPinKuangEffectUi(parti_name)
 	end
 end

function OperationActivityView:IsShowSelectImage()
	-- local cfg_1, cfg_2 = ChuShenWGData.Instance:GetMenuListCfg()
	-- for k,v in pairs(cfg_1) do
	-- 	local has_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
	-- 	local list_has_num = ChuShenWGData.Instance:GetLastNum(v.item_id)
	-- 	local is_num_add = ChuShenWGData.Instance:GetLastNum(v.item_id)
	-- 	if (has_num - list_has_num) > 0 and v.put_time > is_num_add then
			self.node_list.chushen_material_select:SetActive(true)
			UITween.MoveLoop(self.node_list["chushen_remind_arrow"], Vector2(0, 115), Vector2(0, 107.4), 0.9)
	-- 		return UI_pzk_zi_yuan
	-- 	end
	-- end
end

function OperationActivityView:PanelAddClick(index)
	if self.chushen_select_type == 1 then
		if self.chushen_add_list[index] then
			self.chushen_add_list[index] = nil
			ChuShenWGData.Instance:SetPanelAddMark(false)
			self:FlushChuShen()
		else
			ChuShenWGData.Instance:SetPanelAddMark(true)
			self:IsShowSelectImage()
		end
		self.node_list.chushen_material_list_1.scroller:RefreshActiveCellViews()
	else
		local data_list = self:OperateChuShenMenuList()
		local is_unlock = false
		if self.chushen_add_list and self.chushen_add_list.id then
			is_unlock = ChuShenWGData.Instance:CheckMenuIsUnlock(self.chushen_add_list.id)
		end

		if is_unlock and data_list[index] then
			--提示获取途径
			TipWGCtrl.Instance:OpenItem(data_list[index], ItemTip.FROM_NORMAL, nil, nil, nil)
		end
	end

end

 function OperationActivityView:RightRewardAni()
 	if self.reward_tween then
 		self.reward_tween:Kill()
 	end
 	self.node_list["chushen_reward_list_bg"].rect.anchoredPosition = Vector3(0, 701, 0)
	self.reward_tween = self.node_list["chushen_reward_list_bg"].rect:DOAnchorPosY(254, 0.5)
	self.reward_tween:OnComplete(function()
		self.reward_tween = nil
	end)
 end

function OperationActivityView:ShowIndexCallChuShen()
	self:RightRewardAni()
	self.node_list.chushen_material_select:SetActive(false)
	local other_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
	if other_cfg then
		self:SetOutsideRuleTips(other_cfg.tips_outside)
		self:SetRuleInfo(other_cfg.tips_inside, other_cfg.tips_name)
	end
end

 function OperationActivityView:FlushChuShen()

 	if self.send_cook_ani then
 		return 
 	end
 	local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
 	self.chushen_add_list = ChuShenWGData.Instance:GetChuShenSelectData()
 	local other_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
	self.node_list.cook_center_special:SetActive(false)
	for i=1,3 do
		XUI.SetGraphicGrey(self.node_list["chushen_material_cell_image_"..i], false)
		XUI.SetGraphicGrey(self.node_list["chushen_material_cell_bg_"..i], false)
		self.node_list["chushen_num_text_"..i].text.text = ""
		self.node_list["chushen_num_text_bg_"..i]:SetActive(false)
	end


 	if self.chushen_select_type == 1 then
 		self.node_list.chushen_cook_remind_point:SetActive(false)
 		local add_num = 0
	 	for k = 1, 3 do
	 		if self.node_list["chushen_material_"..k] then
	 			local v = self.chushen_add_list[k]
	 			if v and v.item_id and  v.item_id > 0 then
	 				self.node_list["chushen_material_cell_image_"..k]:SetActive(true)

	 				local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(v.item_id)
	 				local bundle, assest = ResPath.GetItem(item_cfg.icon_id)
					self.node_list["chushen_material_cell_image_"..k].image:LoadSprite(bundle, assest, function ()
						XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_"..k])
					end)

					local b, a = ResPath.GetCommonImages("a2_sc_btn_bg_"..v.quality_color)
					local parti_b, parti_a = self:GetPartiAsesst(v.quality_color)
					self.node_list["chushen_material_cell_parti_"..k]:ChangeAsset(parti_b, parti_a)
					self.node_list["chushen_material_cell_parti_"..k]:SetActive(v.quality_color > SHOW_EFFECT_LEVEL)
					self.node_list["chushen_material_cell_bg_"..k].image:LoadSprite(b, a, function ()
						XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_bg_"..k])
					end)

	 				local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
	 				local num_des = string.format(Language.Activity.ChuShenMaterialNumberShowType1, num)
	 				self.node_list["chushen_num_text_"..k].text.text = num_des
	 				self.node_list["chushen_num_text_bg_"..k]:SetActive(true)
		 			self.node_list["chushen_add_"..k].canvas_group.alpha = 0 
		 			add_num = add_num + 1
		 		else
					local b, a =  ResPath.GetChuShenImg("special_circle_bg")
					if interface_cfg then
						b, a =  ResPath.GetChuShenImg(interface_cfg.pic_28)
					end
					self.node_list["chushen_material_cell_parti_"..k]:SetActive(false)
					self.node_list["chushen_material_cell_bg_"..k].image:LoadSprite(b, a, function ()
						XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_bg_"..k])
					end)
					self.node_list["chushen_material_cell_image_"..k]:SetActive(false)
		 			self.node_list["chushen_add_"..k].canvas_group.alpha = 1
		 		end
	 		end
	 	end

	 	XUI.SetGraphicGrey(self.node_list.chushen_cook_btn, add_num < 3)
	 	self.node_list.chushen_cook_remind_point:SetActive(add_num >= 3)
	 	self.node_list.fire_parti:SetActive(add_num >= 3)

	 	if add_num < 3 then
			local b, a =  ResPath.GetChuShenImg("question_mark")
			if interface_cfg then
				b, a =  ResPath.GetChuShenImg(interface_cfg.pic_29)
			end
			self.node_list["chushen_material_cell_image_4"].image:LoadSprite(b, a, function ()
				XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
			end)
			self.node_list["chushen_material_cell_bg_4"]:SetActive(false)
	 		self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialAddEnough
	 	else
	 		local is_has_menu, menu_data = self:ChuShenMineCreatMenu(self.chushen_add_list)
	 		if is_has_menu == 1 then  --有菜谱
	 			local temp_info = ChuShenWGData.Instance:GetChuShenFoodInfoById(menu_data.id)
	 			if not IsEmptyTable(temp_info) then
	 				self.is_full_count = temp_info.use_count >= menu_data.limit_count
	 				XUI.SetGraphicGrey(self.node_list.chushen_cook_btn, temp_info.use_count >= menu_data.limit_count)
	 				self.node_list.chushen_cook_remind_point:SetActive(temp_info.use_count < menu_data.limit_count)
					self.node_list.fire_parti:SetActive(temp_info.use_count < menu_data.limit_count)
	 			end

	 			self.node_list["chushen_material_cell_bg_4"]:SetActive(true)
	 			local is_unlock = ChuShenWGData.Instance:CheckMenuIsUnlock(menu_data.id)
	 			
	 			if is_unlock then
	 				local color = ITEM_COLOR[menu_data.menu_quality]
		 			local menu_name = ToColorStr(menu_data.menu_name, color)
		 			self.node_list.cook_center_desc.text.text = menu_name
		 			ResPath.GetF2CommonImages("yuan_menu_quality_"..menu_data.menu_quality)
	 				local b, a =  ResPath.GetF2CommonImages("yuan_menu_quality_"..menu_data.menu_quality)
					self.node_list["chushen_material_cell_bg_4"].image:LoadSprite(b, a, function ()
						XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_bg_4"])
					end)
					local parti_b, parti_a = self:GetPartiAsesst(menu_data.menu_quality)
					self.node_list.chushen_material_cell_parti_4:ChangeAsset(parti_b, parti_a)
					self.node_list.chushen_material_cell_parti_4:SetActive(menu_data.menu_quality > SHOW_EFFECT_LEVEL)
					local item_cfg = ItemWGData.Instance:GetItemConfig(menu_data.id)
	 				local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
	 				
					self.node_list["chushen_material_cell_image_4"].image:LoadSprite(center_image_b, center_image_a, function ()
						XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
					end)
					self.node_list.chushen_material_cell_image_4:SetActive(true)
	 			else
	 				local center_image_b, center_image_a =  ResPath.GetChuShenImg("question_mark")
	 				if interface_cfg then
						center_image_b, center_image_a =  ResPath.GetChuShenImg(interface_cfg.pic_29)
					end
	 				self.node_list["chushen_material_cell_image_4"].image:LoadSprite(center_image_b, center_image_a, function ()
						XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
					end)
					self.node_list["chushen_material_cell_bg_4"]:SetActive(false)
	 				self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialMystic
	 			end
	 			return
	 		elseif is_has_menu == 2 then --高级自由组合
	 			local fufei_id = self:ChuShenGetFuFeiId()
	 			for k,v in pairs(self.chushen_add_list) do
	 				if v.item_id == fufei_id then  --牛杂处理
	 					local niuza_id = other_cfg.special_menu_id
	 					local temp_info = ChuShenWGData.Instance:GetChuShenFoodInfoById(niuza_id)
			 			if not IsEmptyTable(temp_info) then
			 				local temp_menu_data = ChuShenWGData.Instance:GetOneMenuCfg(niuza_id)
			 				if temp_menu_data then
			 					self.is_full_count = temp_info.use_count >= temp_menu_data.limit_count
			 					XUI.SetGraphicGrey(self.node_list.chushen_cook_btn, temp_info.use_count >= temp_menu_data.limit_count)
			 					self.node_list.chushen_cook_remind_point:SetActive(temp_info.use_count < temp_menu_data.limit_count)
			 					self.node_list.fire_parti:SetActive( temp_info.use_count < temp_menu_data.limit_count)
			 				end
			 			end
	 					
	 					local is_unlock =  ChuShenWGData.Instance:CheckMenuIsUnlock(niuza_id)
	 					self.node_list.chushen_material_cell_bg_4:SetActive(is_unlock)
	 					if is_unlock then
	 						local cfg = ChuShenWGData.Instance:GetOneMenuCfg(niuza_id)
	 						if cfg then
	 							local color = ITEM_COLOR[cfg.menu_quality]
					 			local menu_name = ToColorStr(cfg.menu_name, color)
					 			self.node_list.cook_center_desc.text.text = menu_name

					 			local b, a =  ResPath.GetF2CommonImages("yuan_menu_quality_"..cfg.menu_quality)
								self.node_list["chushen_material_cell_bg_4"].image:LoadSprite(b, a, function ()
									XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_bg_4"])
								end)
								local parti_b, parti_a = self:GetPartiAsesst(cfg.menu_quality)
								-- self.node_list.chushen_material_cell_bg_4:ChangeAsset(parti_b, parti_a)
								self.node_list.chushen_material_cell_parti_4:ChangeAsset(parti_b, parti_a)
								self.node_list.chushen_material_cell_parti_4:SetActive(cfg.menu_quality > SHOW_EFFECT_LEVEL)
								local item_cfg = ItemWGData.Instance:GetItemConfig(niuza_id)
				 				local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
								self.node_list["chushen_material_cell_image_4"].image:LoadSprite(center_image_b, center_image_a, function ()
									XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
								end)
								self.node_list["chushen_material_cell_image_4"]:SetActive(true)
					 		else
					 			self.node_list.cook_center_desc.text.text = ""
	 						end
	 						return
	 					end
	 				end
	 			end
	 		elseif is_has_menu == 3 then  --中级自由组合
	 			local _, mid_fufei_id = self:ChuShenGetFuFeiId()
	 			for k,v in pairs(self.chushen_add_list) do
		 			if v.item_id == mid_fufei_id then  --牛杂处理
	 					local mid_menu_id = other_cfg.mid_cuisine_icon
	 					local temp_info = ChuShenWGData.Instance:GetChuShenFoodInfoById(mid_menu_id)
	 					if not IsEmptyTable(temp_info) then
	 						local temp_menu_data = ChuShenWGData.Instance:GetOneMenuCfg(mid_menu_id)
			 				if temp_menu_data then
			 					self.is_full_count = temp_info.use_count >= temp_menu_data.limit_count
			 					XUI.SetGraphicGrey(self.node_list.chushen_cook_btn, temp_info.use_count >= temp_menu_data.limit_count)
			 					self.node_list.chushen_cook_remind_point:SetActive(temp_info.use_count < temp_menu_data.limit_count)
			 					self.node_list.fire_parti:SetActive( temp_info.use_count < temp_menu_data.limit_count)
			 				end
	 					end
	 					
		 				local is_unlock =  ChuShenWGData.Instance:CheckMenuIsUnlock(mid_menu_id)
		 				self.node_list.chushen_material_cell_bg_4:SetActive(is_unlock)
						if is_unlock then
							local cfg = ChuShenWGData.Instance:GetOneMenuCfg(mid_menu_id)
							if cfg then
								local color = ITEM_COLOR[cfg.menu_quality]
					 			local menu_name = ToColorStr(cfg.menu_name, color)
					 			self.node_list.cook_center_desc.text.text = menu_name

					 			local b, a =  ResPath.GetF2CommonImages("yuan_menu_quality_"..cfg.menu_quality)
								self.node_list["chushen_material_cell_bg_4"].image:LoadSprite(b, a, function ()
									XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_bg_4"])
								end)
								local parti_b, parti_a = self:GetPartiAsesst(cfg.menu_quality)
								--self.node_list.chushen_material_cell_bg_4:ChangeAsset(parti_b, parti_a)
								self.node_list.chushen_material_cell_parti_4:ChangeAsset(parti_b, parti_a)
								self.node_list.chushen_material_cell_parti_4:SetActive(cfg.menu_quality > SHOW_EFFECT_LEVEL)
								local item_cfg = ItemWGData.Instance:GetItemConfig(mid_menu_id)
				 				local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
								self.node_list["chushen_material_cell_image_4"].image:LoadSprite(center_image_b, center_image_a, function ()
									XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
								end)
								self.node_list["chushen_material_cell_image_4"]:SetActive(true)
					 		else
					 			self.node_list.cook_center_desc.text.text = ""
							end
							return
						end
		 			end
		 		end
	 		end
	 			-------神秘食材处理-------
	 			self.node_list["chushen_material_cell_image_4"]:SetActive(true)
	 			local center_image_b, center_image_a =  ResPath.GetChuShenImg("question_mark")
	 				 if interface_cfg then
						center_image_b, center_image_a =  ResPath.GetChuShenImg(interface_cfg.pic_29)
					end
 				self.node_list["chushen_material_cell_image_4"].image:LoadSprite(center_image_b, center_image_a, function ()
					XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
				end)
	 			self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialMystic
	 		
	 	end
	 	self.node_list.chushen_material_list_1.scroller:RefreshActiveCellViews()
	else
		local is_vas, is_have_get = ChuShenWGData.Instance:GetPreviousRedPoint()
		self.node_list.chushen_reward_remind_point:SetActive(is_vas)
		XUI.SetGraphicGrey(self.node_list.chushen_unlockreward_btn, is_have_get)
		self.node_list["chushen_unlockreward_yes"]:SetActive(is_have_get)

		local flag = ChuShenWGData.Instance:ChuShenRemindTodayBtn()
 		self.node_list.chushen_unlockreward_tips_btn:SetActive(flag)
 		local is_unlock, info = ChuShenWGData.Instance:CheckMenuIsUnlock(self.chushen_add_list.id)
		local data_list, is_special, special_menu_type = self:OperateChuShenMenuList()
		if self.chushen_add_list and self.chushen_add_list.menu_name then
			local color = ITEM_COLOR[self.chushen_add_list.menu_quality]
 			local menu_name = ToColorStr(self.chushen_add_list.menu_name, color)
 			self.node_list.cook_center_desc.text.text = menu_name
			self.node_list["chushen_material_cell_bg_4"]:SetActive(is_unlock)

			local b, a =  ResPath.GetF2CommonImages("yuan_menu_quality_"..self.chushen_add_list.menu_quality)
			self.node_list["chushen_material_cell_bg_4"]:SetActive(true)
			self.node_list["chushen_material_cell_bg_4"].image:LoadSprite(b, a, function ()
				XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_bg_4"])
			end)
			local parti_b, parti_a = self:GetPartiAsesst(self.chushen_add_list.menu_quality)
			-- self.node_list.chushen_material_cell_bg_4:ChangeAsset(parti_b, parti_a)
			self.node_list.chushen_material_cell_parti_4:ChangeAsset(parti_b, parti_a)
			self.node_list.chushen_material_cell_parti_4:SetActive(self.chushen_add_list.menu_quality > SHOW_EFFECT_LEVEL)
			local item_cfg = ItemWGData.Instance:GetItemConfig(self.chushen_add_list.id)
			local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
			self.node_list["chushen_material_cell_image_4"].image:LoadSprite(center_image_b, center_image_a, function ()
				XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
			end)
		else
			self.node_list["chushen_material_cell_bg_4"]:SetActive(false)
			local center_image_b, center_image_a =  ResPath.GetChuShenImg("question_mark")
		 	if interface_cfg then
				center_image_b, center_image_a =  ResPath.GetChuShenImg(interface_cfg.pic_29)
			end
			self.node_list["chushen_material_cell_image_4"].image:LoadSprite(center_image_b, center_image_a, function ()
					XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
				end)
		end

		if is_unlock then
			if IsEmptyTable(data_list) then
			else
				local have_real_num = 0
				for k,v in pairs(data_list) do
			 		if self.node_list["chushen_material_"..k] then
		 				local num = v.num
		 				local num_des = string.format(Language.Activity.ChuShenMaterialNumberShowType1, num)
		 				self.node_list["chushen_num_text_"..k].text.text = num_des
		 				self.node_list["chushen_num_text_bg_"..k]:SetActive(true)
		 				local base_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)

 						local b, a =  ResPath.GetCommonImages("a2_sc_btn_bg_"..base_cfg.color)
 						local parti_b, parti_a = self:GetPartiAsesst(base_cfg.color)
						self.node_list["chushen_material_cell_parti_"..k]:ChangeAsset(parti_b, parti_a)
						self.node_list["chushen_material_cell_parti_"..k]:SetActive(base_cfg.color > SHOW_EFFECT_LEVEL)
						self.node_list["chushen_material_cell_bg_"..k].image:LoadSprite(b, a, function ()
							XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_bg_"..k])
						end)

						local item_cfg = ItemWGData.Instance:GetItemConfig(v.item_id)
		 				local center_image_b, center_image_a =  ResPath.GetItem(item_cfg.icon_id)
						self.node_list["chushen_material_cell_image_"..k].image:LoadSprite(center_image_b, center_image_a, function ()
							XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_"..k])
						end)
						self.node_list["chushen_material_cell_image_"..k]:SetActive(true)

						XUI.SetGraphicGrey(self.node_list["chushen_material_cell_image_"..k], v.num <= 0)
						XUI.SetGraphicGrey(self.node_list["chushen_material_cell_bg_"..k], v.num <= 0)
		 				if v.num > 0 then
			 				self.node_list["chushen_add_"..k].canvas_group.alpha = 0 
			 				have_real_num = have_real_num + 1
				 		else
				 			self.node_list["chushen_add_"..k].canvas_group.alpha = 1
				 		end
			 		end
			 	end

			 	local have_count = self.chushen_add_list.limit_count - info.use_count 
			 	XUI.SetGraphicGrey(self.node_list.chushen_cook_btn, have_real_num < 3 or have_count <= 0)
			 	self.node_list.fire_parti:SetActive(have_real_num >= 3 and have_count > 0)
			 	self.node_list.chushen_cook_remind_point:SetActive(have_real_num >= 3 and have_count > 0 and info.is_remind == 1)
			end
			self.node_list.cook_center_special:SetActive(is_special) --data_list
			if is_special then
				local menu_type_index = other_cfg.tips_image_show
				local b, a 
				if special_menu_type == 0 then  --高级
					b, a = ResPath.GetChuShenImg("special_text_image_"..menu_type_index)
				else
					b, a = ResPath.GetChuShenImg("mid_special_text_image_"..menu_type_index)
				end
				self.node_list["cook_center_special"].image:LoadSprite(b, a)
			end
		else
			 ----显示问号
			 for i=1, 3 do
			 	self.node_list["chushen_add_"..i].canvas_group.alpha = 0
			 	self.node_list["chushen_material_cell_image_"..i]:SetActive(true)
			 	self.node_list["chushen_material_cell_bg_"..i]:SetActive(true)
				self.node_list["chushen_material_cell_parti_"..i]:SetActive(false)
			 	local center_image_b, center_image_a =  ResPath.GetChuShenImg("special_circle_bg")
			 	if interface_cfg then
					center_image_b, center_image_a =  ResPath.GetChuShenImg(interface_cfg.pic_28)
				end
 				self.node_list["chushen_material_cell_bg_"..i].image:LoadSprite(center_image_b, center_image_a, function ()
					XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_bg_"..i])
				end)

				local center_image_b, center_image_a =  ResPath.GetChuShenImg("question_mark")
				if interface_cfg then
					center_image_b, center_image_a =  ResPath.GetChuShenImg(interface_cfg.pic_29)
				end
				self.node_list["chushen_material_cell_image_"..i].image:LoadSprite(center_image_b, center_image_a, function ()
						XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_"..i])
					end)
			 end

			self.node_list.chushen_remind_btn:SetActive(false)
			XUI.SetGraphicGrey(self.node_list.chushen_cook_btn, true)
			self.node_list.fire_parti:SetActive(false)
			self.node_list.chushen_cook_remind_point:SetActive(false)
			
		end
		self.node_list.chushen_material_list.scroller:RefreshActiveCellViews()
	end
	local _, _, ani_mark = ChuShenWGData.Instance:GetChuShenActInfoData()
	self.node_list.chushen_ani_mark:SetActive(ani_mark == 1)
	local is_remind_select_btn = ChuShenWGData.Instance:IsShowChuShenRedPoint()
	self.node_list.chushen_select_btn_remind_2:SetActive(is_remind_select_btn == 1)
 	
	self:ChuShenSetRankList()
 end

 function OperationActivityView:ChuShenSetRankList()
	local data_list = ChuShenWGData.Instance:GetChuShenRankData()
	self.node_list["chushen_not_rank"]:SetActive(IsEmptyTable(data_list))
 	self.chushen_rank_list:SetDataList(data_list, 0)
 end

 function OperationActivityView:OperateChuShenMenuList()
 	self.chushen_add_list = ChuShenWGData.Instance:GetChuShenSelectData()
 	local data_list = self.chushen_add_list.consume_item
 	if not data_list then
 		return {}, false
 	end

	local temp_info ChuShenWGData.Instance:GetChuShenFoodInfoById(self.chushen_add_list.id)

	if nil == temp_info or (temp_info and temp_info.state and temp_info.state == 0) then
		self.node_list.chushen_remind_btn:SetActive(false)
		self.node_list.chushen_remind_mark:SetActive(false)
	else
		self.node_list.chushen_remind_btn:SetActive(true)
		self.node_list.chushen_remind_mark:SetActive(temp_info.is_remind == 1)
	end

	local temp_list, menu_level = {}, nil
 	if #data_list <= 1 then
 		temp_list, menu_level = self:ChuShenAutoMenu(self.chushen_add_list)
 		self.niu_za_menu = temp_list
 		return temp_list, true, menu_level
 	end
 	
 	for i=1,3 do
 		local num = ItemWGData.Instance:GetItemNumInBagById(data_list[i -1].item_id)
 		temp_list[i] = {}
	 	temp_list[i].item_id = data_list[i -1].item_id

 		if i == 1 then
	 		temp_list[i].num = num >= data_list[0].num and num or 0
	 	elseif i == 2 then
			if data_list[1].item_id == data_list[0].item_id then
				local all_num = data_list[1].num + data_list[0].num
				temp_list[i].num = num >= all_num and num or 0
			else
				temp_list[i].num = num >= data_list[1].num and num or 0
			end

	 	else
	 		if data_list[2].item_id == data_list[0].item_id and data_list[2].item_id ~= data_list[1].item_id 
	 			or  data_list[2].item_id == data_list[1].item_id and data_list[2].item_id ~= data_list[0].item_id  then
	 			local all_num = data_list[1].num + data_list[0].num
				temp_list[i].num = num >= all_num and num or 0
			elseif data_list[2].item_id == data_list[0].item_id and data_list[2].item_id == data_list[1].item_id then
				local all_num = data_list[1].num + data_list[0].num + data_list[2].num 
				temp_list[i].num = num >= all_num and num or 0
			else
				temp_list[i].num = num >= data_list[0].num and num or 0
			end
	 	end
 	end

 	return temp_list, false
 end

 function OperationActivityView:ChuShenGetFuFeiId()
 	local fufei_id_cfg, menu_list = ChuShenWGData.Instance:GetMenuListCfg()
 	local fufei_id, mind_fufei_id = 0, 0
	for k,v in pairs(fufei_id_cfg) do
		if v.is_special == 1 then
			fufei_id = v.item_id
		end

		if v.is_special == 2 then
			mind_fufei_id = v.item_id
		end
	end

	return fufei_id, mind_fufei_id
 end

--牛杂配方要自己选出来放进去
function OperationActivityView:ChuShenAutoMenu(menu_cfg)
	local fufei_id_cfg, menu_list = ChuShenWGData.Instance:GetMenuListCfg()
	local fufei_id = 0
	local other_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
	local special_menu_id, middle_special_menu_id = other_cfg.special_menu_id, other_cfg.mid_cuisine_icon
	local auto_shicai = {}
	local is_special, is_mid_special = special_menu_id == menu_cfg.id, middle_special_menu_id == menu_cfg.id
	for k,v in pairs(fufei_id_cfg) do
		if is_special then
			if v.is_special == 1 then
				fufei_id = v.item_id
			else
				local data = {}
				data.item_id = v.item_id
				local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
				data.num = num
				data.food_sort = v.food_sort * -1
				table.insert(auto_shicai, data)
			end
		else
			if v.is_special == 2 then
				fufei_id = v.item_id
				local data = {}
				data.item_id = v.item_id
				local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
				data.num = num > 1 and (num - 1) or 0
				data.food_sort = v.food_sort * -1
				if data.num > 0 then
					table.insert(auto_shicai, data)
				end
			elseif v.is_special == 0 then
				local data = {}
				data.item_id = v.item_id
				local num = ItemWGData.Instance:GetItemNumInBagById(v.item_id)
				data.num = num
				data.food_sort = v.food_sort * -1
				table.insert(auto_shicai, data)
			end
		end
		
	end
		table.sort(auto_shicai, SortTools.KeyUpperSorters("num", "food_sort"))

		local menu_list = {}
		menu_list[1] = {}
		menu_list[1].item_id = fufei_id
		menu_list[1].num = ItemWGData.Instance:GetItemNumInBagById(fufei_id)
		menu_list[2] = auto_shicai[1]

	if auto_shicai[1].num > 1 then
		menu_list[2].num = ItemWGData.Instance:GetItemNumInBagById(auto_shicai[1].item_id)
		menu_list[3] = auto_shicai[1]
		return menu_list, is_mid_special and 1 or 0 --1中级菜  0 高级菜
	else
		menu_list[2].num = ItemWGData.Instance:GetItemNumInBagById(auto_shicai[1].item_id)
		menu_list[3] = auto_shicai[2]
		menu_list[3].num = ItemWGData.Instance:GetItemNumInBagById(auto_shicai[2].item_id)
		local is_can_menu = self:IsHaveMenu(fufei_id, menu_list)
		if is_can_menu then
			return menu_list, is_mid_special and 1 or 0 --1中级菜  0 高级菜
		end
	end
	
end

function OperationActivityView:IsHaveMenu(fufei_id, menu_table)
	local cfg, cfg_1 = ChuShenWGData.Instance:GetMenuListCfg()
	local table_fufei = {}
	for k,v in pairs(cfg_1) do
		if #v.consume_item > 1 then
			table.insert(table_fufei, v.consume_item)
			break
		end
	end

	local menu_table_copy = {}
	for k,v in pairs(table_fufei) do
		local count = 0
		for m,n in pairs(menu_table) do
			menu_table_copy[m] = n.item_id
		end
		for i=1,3 do
			for m,n in pairs(menu_table_copy) do
				if n == v[i - 1].item_id then
					menu_table_copy[m] = 0
					count = count + 1
				end
			end
		end
		if count >= 3 then
			return false
		end
	end
	return true
end

--自研菜谱放入文字显示
function OperationActivityView:ChuShenMineCreatMenu(menu_table)
	local cfg, cfg_1 = ChuShenWGData.Instance:GetMenuListCfg()
	--local other_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
	local special_id, middle_special_id = self:ChuShenGetFuFeiId()--other_cfg.special_menu_id, other_cfg.mid_cuisine_icon
	local is_special, is_mid_special = false, false
	local table_fufei = {}
	for k,v in pairs(cfg_1) do
		if #v.consume_item > 1 then
			table.insert(table_fufei, v) --.consume_item
		end
	end

	local menu_table_copy = {}
	for k,v in pairs(table_fufei) do
		local count = 0
		for m,n in pairs(menu_table) do
			menu_table_copy[m] = n.item_id
			if special_id == n.item_id then
				is_special = true
			end

			if middle_special_id == n.item_id then
				is_mid_special = true
			end
		end

		for i=1,3 do
			for m,n in pairs(menu_table_copy) do
				if n == v.consume_item[i - 1].item_id then
					menu_table_copy[m] = 0
					count = count + 1
					break
				end
			end
		end

		if count >= 3 then
			return 1, v  --1代表有菜谱
		end
	end


	if is_special then
		return 2     --2代表是高级自由组合
	end 

	if not is_special and is_mid_special then
		return 3     --3代表是高级自由组合
	end 

	return 4      --4 代表黑暗料理了
end

function OperationActivityView:ChuShenPanelResert()
	local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
	for i=1,3 do
		self.node_list["chushen_material_"..i]:SetActive(true)
		self.node_list["chushen_material_cell_bg_"..i]:SetActive(true)
		self.node_list["chushen_material_cell_parti_"..i]:SetActive(false)
		self.node_list["chushen_material_cell_image_"..i]:SetActive(false)
		self.node_list["chushen_add_"..i].canvas_group.alpha = 1
		self.node_list["chushen_num_text_"..i].text.text = ""
		self.node_list["chushen_num_text_bg_"..i]:SetActive(false)

		local b, a =  ResPath.GetChuShenImg("special_circle_bg")
		if interface_cfg then
			b, a =  ResPath.GetChuShenImg(interface_cfg.pic_28)
		end
		self.node_list["chushen_material_cell_bg_"..i].image:LoadSprite(b, a, function ()
			XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_"..i])
		end)
	end

	self.node_list["chushen_material_cell_bg_4"]:SetActive(false)
	self.node_list["chushen_material_cell_image_4"]:SetActive(true)

	local b, a =  ResPath.GetChuShenImg("question_mark")
	if interface_cfg then
		b, a =  ResPath.GetChuShenImg(interface_cfg.pic_29)
	end
	self.node_list["chushen_material_cell_image_4"].image:LoadSprite(b, a, function ()
		XUI.ImageSetNativeSize(self.node_list["chushen_material_cell_image_4"])
	end)
end

function OperationActivityView:ChuShenSelecType(btn_index)
 	if self.chushen_select_type == btn_index then
 		return
 	end

 	self:ChuShenPanelResert()
 	self.node_list.chushen_material_select:SetActive(false)
 	self.node_list.chushen_material_list_1:SetActive(btn_index == 1)
 	self.node_list.chushen_material_list:SetActive(btn_index == 2)
 	self.chushen_select_type = btn_index
 	self.node_list.chushen_remind_btn:SetActive(false)

 	for i=1, 2 do
 		self.node_list["chushen_select_image_"..i]:SetActive(i == btn_index)
 	end

 	self.node_list.chushen_cook_remind_point:SetActive(false)
 	self.node_list["chushen_unlockreward_btn"]:SetActive(btn_index == 2)
 	self.node_list["chushen_unlockreward_yes"]:SetActive(btn_index == 2)
 	

 	local cfg_1, _ = ChuShenWGData.Instance:GetMenuListCfg()
 	self.chushen_material_list:SetDefaultSelectIndex(0)
 	self.chushen_material_list_1:SetDefaultSelectIndex(0)
 	if self.chushen_select_type == 1 then
 		self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialAddEnough
 		self.chushen_material_list_1:SetDataList(cfg_1, 0)
 		self:ChuShenPreviousReward()
 		self.node_list.chushen_unlockreward_tips_btn:SetActive(false)
 	else
 		self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialAddEnough
 		 local cfg_2 = ChuShenWGData.Instance:GetCurFoodMenuList()
 		self.chushen_material_list:SetDataList(cfg_2, 0)
 	end
 	ChuShenWGData.Instance:SetChuShenSelectData({})
 	self:FlushChuShen()
 end

function OperationActivityView:ChuShenPreviousReward()
	local data_list = ChuShenWGData.Instance:GetChuShenSelectData()
	if not IsEmptyTable(data_list) then
		self:RightRewardAni()
	end
	self:ChuShenPreviousRewardData()
end

function OperationActivityView:ChuShenPreviousRewardData()
	local data = {}
	self.node_list.menu_name_image:SetActive(self.chushen_select_type == 2)
	self.node_list.shicai_name_image:SetActive(self.chushen_select_type == 1)
	
	local oa_index = ChuShenWGData.Instance:GetChuShenActInfoData()
	local cfg_data = ChuShenWGData.Instance:GetChuShenParamCfg()
	for k,v in pairs(cfg_data) do
		if v.oa_index == oa_index then
			data = v.reward_preview_item
			break
		end
	end

	if self.chushen_select_type == 2 then
		data = self.chushen_add_list.reward_dropid_list_client or data
	end

	local end_data = {}
	if not IsEmptyTable(data) then
		for i=0, #data do
			table.insert(end_data, data[i])
		end
	end
	
	table.sort(end_data,function (a,b)
		if a and b and a.item_id and b.item_id then
			local item_cfg_a = ItemWGData.Instance:GetItemConfig(a.item_id)
			local item_cfg_b = ItemWGData.Instance:GetItemConfig(b.item_id)
			return item_cfg_a.color > item_cfg_b.color
		end
	end )
	self.chushen_reward_list:SetDataList(end_data, 0)
end



 function OperationActivityView:ChuShenRankInfo()
 	ChuShenWGCtrl.Instance:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_1)
 	ChuShenWGCtrl.Instance:OpenChuShenSecondPanel()
 end

 function OperationActivityView:FlushChuShenHot()
 	local cfg_1, _ = ChuShenWGData.Instance:GetMenuListCfg()
 	local cfg_2 = ChuShenWGData.Instance:GetCurFoodMenuList()
	self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialAddEnough
	self.chushen_material_list_1:SetDataList(cfg_1, 2)
	self.chushen_material_list:SetDataList(cfg_2, 2)
 	self:InitPictureAndDescText()
 	self:FlushChuShen()
 end

 function OperationActivityView:ChuShenCookSend()
 	if self.send_cook_ani then
 		return
	end

 	local param1, param2, param3
 	local num = 0
 	if self.chushen_select_type == 1 then
 		for k,v in pairs(self.chushen_add_list) do
	 		if v and v.item_id and v.item_id > 0 then
	 			num = num + 1
	 		end
	 	end

	 	if num < 3 then
	 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenMaterialNumTips)
	 		return
	 	end
	 	param1, param2, param3 = self.chushen_add_list[1].item_id, self.chushen_add_list[2].item_id, self.chushen_add_list[3].item_id
 	else
 		local data_list = self:OperateChuShenMenuList()
 		if IsEmptyTable(data_list) then
	 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenMenuSelect)
 			return
 		end

 		local is_unlock, info = ChuShenWGData.Instance:CheckMenuIsUnlock(self.chushen_add_list.id)
	 	if not is_unlock then
	 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenMenuNotActive)
	 		return
	 	end

 		for k,v in pairs(data_list) do
	 		if v and v.num > 0 then
	 			num = num + 1
	 		end
	 	end

	 	if num < 3 then
	 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenMaterialNumTips)
	 		return
	 	end

	 	self.is_full_count = info.use_count >= self.chushen_add_list.limit_count
	 	param1, param2, param3 = data_list[1].item_id, data_list[2].item_id, data_list[3].item_id
 	end

 	if self.is_full_count then
 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenCookMaxNum)
	 	return
 	end
 	self.send_cook_ani = true
 	self:ChuShenPlayAniDetail(param1, param2, param3)
 end

 function OperationActivityView:ChuShenRemindSend()
 	local is_active = self.node_list.chushen_remind_mark:GetActive()
 	local is_mark = is_active and 0 or 1
 	ChuShenWGCtrl.Instance:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_8, self.chushen_add_list.id, is_mark)
 end

 function OperationActivityView:OpenUnlockRewardPanel()
 	 local _, unlock_all = ChuShenWGData.Instance:GetChuShenActInfoData()
	if unlock_all == 2 then
 		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.ChuShenGetReward1)
 		return
 	end
 	ChuShenWGCtrl.Instance:OpenRewardPanel()
 end

 function OperationActivityView:SendAniMark()
 	local is_vas = self.node_list.chushen_ani_mark:GetActive()
 	local is_mark = is_vas and 0 or 1
 	ChuShenWGCtrl.Instance:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_5, is_mark)
 end

 function OperationActivityView:SendTodayMark()
 	ChuShenWGData.Instance:ChuShenUnlockRemindMark()
 	self.node_list["chushen_unlockreward_tips_btn"]:SetActive(false)
 end

function OperationActivityView:ChuShenPlayAni()
	self.send_cook_ani = false
	for i=1,3 do
		self.node_list["chushen_material_"..i].rect.anchoredPosition = self.node_list["chushen_add_"..i].rect.anchoredPosition
		self.node_list["chushen_material_"..i]:SetActive(true)
	end

	if self.gaizi_origin_pos then
		self.node_list["chushen_gaizi_image"].rect.anchoredPosition = self.gaizi_origin_pos
	end
	self.node_list.chushen_material_cell_image_4:SetActive(true)
	self:FlushChuShen()
end

 function OperationActivityView:ChuShenPlayAniDetail(param1, param2, param3)
 	local _, _, ani_mark = ChuShenWGData.Instance:GetChuShenActInfoData()
 	self.is_full_count = false

 	if self.chushen_select_type == 1 then
 		ChuShenWGData.Instance:SetChuShenSelectData({})
 	end

 	if ani_mark == 1 then
 		ChuShenWGCtrl.Instance:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_4, param1, param2, param3)
 		self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialAddEnough
 	else
 		for i=1,3 do
	 		self.node_list["chushen_material_"..i].rect.anchoredPosition = self.node_list["chushen_add_"..i].rect.anchoredPosition
	 		self.node_list["chushen_material_"..i]:SetActive(true)
	 	end

	 	
	 	local gaizi_tween = self.node_list["chushen_gaizi_image"].rect:DOAnchorPosY(102, 0.5):OnComplete(function()
	           -- self.node_list["chushen_gaizi_image"].rect.anchoredPosition = self.node_list["chushen_add_1"].rect.anchoredPosition
	        end)

	 	local end_pos_x, end_pos_y =  self.node_list["chushen_material_4"].rect.anchoredPosition.x,  self.node_list["chushen_material_4"].rect.anchoredPosition.y
	 	local tween1 = self.node_list["chushen_material_1"].rect:DOLocalMove(Vector2(end_pos_x, end_pos_y), 0.5):OnComplete(function()
	            self.node_list["chushen_material_1"]:SetActive(false)
	           -- self.node_list["chushen_material_1"].rect.anchoredPosition = self.node_list["chushen_add_1"].rect.anchoredPosition
	        end)

	 	local tween2 = self.node_list["chushen_material_2"].rect:DOLocalMove(Vector2(end_pos_x, end_pos_y), 0.5):OnComplete(function()
	            self.node_list["chushen_material_2"]:SetActive(false)
	           -- self.node_list["chushen_material_2"].rect.anchoredPosition = self.node_list["chushen_add_2"].rect.anchoredPosition
	        end)

	 	 local tween3 = self.node_list["chushen_material_3"].rect:DOLocalMove(Vector2(end_pos_x, end_pos_y), 0.5):OnComplete(function()
	            self.node_list["chushen_material_3"]:SetActive(false)
	            self.node_list.chushen_material_cell_bg_4:SetActive(false)
	            self.node_list.chushen_material_cell_image_4:SetActive(false)
	            local fun_callback = function ()
		           -- ChuShenWGData.Instance:SetChuShenSelectData({})
		            self.node_list.cook_center_desc.text.text = Language.Activity.ChuShenMaterialAddEnough
		            ChuShenWGCtrl.Instance:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_4, param1, param2, param3)
		            

	            end
	            local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
	            local bundle_name, asset_name = ResPath.GetEffectUi(interface_cfg.pic_21)
	            local load_callback = function ()
	            	if self.gaizi_origin_pos then
						self.node_list["chushen_gaizi_image"].rect:DOAnchorPosY(self.gaizi_origin_pos.y, 0.5)
					end
	            end
	            EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, self.node_list.chushen_ronghe_pos.transform, 1.2, nil, nil, nil, load_callback, fun_callback)
 				local bundle_name1, asset_name1 = ResPath.GetEffectUi(interface_cfg.pic_23)
	          	EffectManager.Instance:PlaySingleAtTransform(bundle_name1, asset_name1, self.node_list.gaizi_light_pos.transform, 1.2, nil, nil, nil, nil)
	        end)
 	end
 end



ChuShenRankRender = ChuShenRankRender or BaseClass(BaseRender)

function ChuShenRankRender:LoadCallBack()
	
end

function ChuShenRankRender:__delete()
	
end

function ChuShenRankRender:OnFlush()
	if nil == self.data then
		return
	end

	local info = ChuShenWGData.Instance:GetOneMenuCfg(self.data.menu_id)
	local color = ITEM_COLOR[info.menu_quality]
	local menu_name = ToColorStr(info.menu_name, color)
	self.node_list.content_text.text.text = string.format(Language.Activity.ChuShenRankDes, self.data.role_name, menu_name)
end

ChuShenRewardRender = ChuShenRewardRender or BaseClass(BaseRender)

function ChuShenRewardRender:LoadCallBack()
	if not self.reward_cell then
		self.reward_cell = ItemCell.New(self.node_list.material_pos)
	end
end


function ChuShenRewardRender:__delete()
	if self.reward_cell then
		self.reward_cell:DeleteMe()
		self.reward_cell = nil
	end

end

function ChuShenRewardRender:OnFlush()
	if nil == self.data then
		return 
	end
	self.reward_cell:SetData(self.data)
end 


ChuShenMaterialRender = ChuShenMaterialRender or BaseClass(BaseRender)

function ChuShenMaterialRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list["chushen_apply_btn"], BindTool.Bind(self.ChuShenCDSend, self))
	self.is_full_count = false
end

function ChuShenMaterialRender:ChuShenCDSend()
	local cfg = self.data.cfg
	local color = ITEM_COLOR[cfg.menu_quality]
	local sex = GameVoManager.Instance:GetMainRoleVo().sex or GameEnum.MALE
	local content = string.format(Language.Activity.ChuShenHelp, color, cfg.menu_name, cfg.id, sex) 
	ChatWGCtrl.Instance:SendChannelChat(CHANNEL_TYPE.WORLD, content, CHAT_CONTENT_TYPE.TEXT, nil, 1, true)
	ChuShenWGCtrl.Instance:CSOATianCaiChuShenOpera(TIANCAICHUSHEN_OPERA_TYPE.TIANCAICHUSHEN_OPERA_TYPE_7, cfg.id)
end

function ChuShenMaterialRender:__delete()
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end

function ChuShenMaterialRender:OnFlush()
	if nil == self.data then
		return 
	end

	local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
 	if interface_cfg then
 		self.node_list["chushen_apply_btn"].image:LoadSprite(ResPath.GetChuShenImg(interface_cfg.pic_26))
 	end
	
	self.node_list.SelectEffect2:SetActive(false)
	local cfg = self.data.cfg
	self.temp_info = ChuShenWGData.Instance:GetChuShenFoodInfoById(cfg.id)

	self.node_list.num_text.text.text = ""
	self.node_list.num_text_bg:SetActive(false)

	local is_select_menu = ChuShenWGData.Instance:GetChuShenSelectData()
	if IsEmptyTable(is_select_menu) then
		self.node_list.SelectEffect2:SetActive(false)
	else
		if cfg.id == is_select_menu.id then
			self.node_list.SelectEffect2:SetActive(true)
		end
	end
	local item_cfg, big_type = ItemWGData.Instance:GetItemConfig(cfg.id)
	local bundle, assest = ResPath.GetItem(item_cfg.icon_id)
	self.node_list.item_image.image:LoadSprite(bundle, assest)
	self.node_list.material_pos.image:LoadSprite(ResPath.GetF2CommonImages("yuan_menu_quality_"..cfg.menu_quality))
	local parti_b, parti_a = self:GetPartiAsesst(item_cfg.color)
	self.node_list["material_parti"]:ChangeAsset(parti_b, parti_a)
	self.node_list["material_parti"]:SetActive(false)
	if not IsEmptyTable(self.temp_info) then
		XUI.SetGraphicGrey(self.node_list.material_pos, self.temp_info.state ~= 1) 
		XUI.SetGraphicGrey(self.node_list.item_image, self.temp_info.state ~= 1) 
		self.node_list.lock_text:SetActive(self.temp_info.state ~= 1)
		self.node_list.num_text_bg:SetActive(self.temp_info.state == 1)
		self.node_list["material_parti"]:SetActive(self.temp_info.state == 1 and item_cfg.color > SHOW_EFFECT_LEVEL)
		if self.temp_info.state == 1 then
			XUI.SetGraphicGrey(self.node_list.material_pos, self.temp_info.use_count >= cfg.limit_count) 
			XUI.SetGraphicGrey(self.node_list.item_image, self.temp_info.use_count >= cfg.limit_count) 
			local is_material_enough = ChuShenWGData.Instance:OperateChuShenMenuList(cfg)
			self.node_list.remind_arrow:SetActive(is_material_enough and self.temp_info.use_count < cfg.limit_count and self.temp_info.is_remind == 1) 
			--XUI.SetGraphicGrey(self.node_list.num_text, self.temp_info.use_count >= cfg.limit_count)
			if self.temp_info.use_count >= cfg.limit_count then
				self.node_list.num_text.text.text = string.format(Language.Activity.ChuShenMaterialNumberShowType1_2, 0, cfg.limit_count)

			else
				self.node_list.num_text.text.text = string.format(Language.Activity.ChuShenMaterialNumberShowType1_1, cfg.limit_count - self.temp_info.use_count, cfg.limit_count)
			end
		else
			self.node_list.remind_arrow:SetActive(false) 
		end
		self.is_full_count = self.temp_info.use_count >= cfg.limit_count

		local server_time = TimeWGCtrl.Instance:GetServerTime()
		local last_time = server_time - self.temp_info.help_timestamp - cfg.cd
		self.is_other_role_unlock = ChuShenWGData.Instance:IsOtherRoleCreat(cfg.id)
		self.node_list.chushen_apply_btn:SetActive(self.temp_info.state == 0 and last_time > 0 and self.is_other_role_unlock)

		if nil == self.timer_quest then
			if self.temp_info.state == 0 and last_time < 0 and self.is_other_role_unlock then
				self:UpdateTime()
				self.timer_quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateTime, self), 1)
			end
		end
		
	else
		self.node_list.remind_arrow:SetActive(false)
		local lock_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
		XUI.SetGraphicGrey(self.node_list.material_pos, true) 
		XUI.SetGraphicGrey(self.node_list.item_image, true) 
		self.node_list.lock_text:SetActive(true)
		self.node_list.chushen_apply_btn:SetActive(false)
		self.is_other_role_unlock = ChuShenWGData.Instance:IsOtherRoleCreat(cfg.id)
		self.node_list.chushen_apply_btn:SetActive(self.is_other_role_unlock)
	end


end 

function ChuShenMaterialRender:GetPartiAsesst(color)
 	local parti_name = BaseCell_Ui_Circle_Effect[color]
 	if parti_name then
 		return ResPath.GetWuPinKuangEffectUi(parti_name)
 	end
end

function ChuShenMaterialRender:SetAddArrowShow(is_show)
	self.node_list.remind_arrow:SetActive(false)
end

function ChuShenMaterialRender:UpdateTime()
	if nil == self.temp_info or IsEmptyTable(self.temp_info) then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local cd = self.data and self.data.cfg and self.data.cfg.cd or 0
	local last_time = server_time - self.temp_info.help_timestamp - cd
	self.node_list.chushen_apply_btn:SetActive(self.temp_info.state == 0 and last_time > 0 and self.is_other_role_unlock)
end

function ChuShenMaterialRender:GetPosNode()
	return self.node_list.material_pos
end

function ChuShenMaterialRender:IsFullCount()
	return self.is_full_count or false
end


ChuShenMaterialRenderShiCai = ChuShenMaterialRenderShiCai or BaseClass(BaseRender)


function ChuShenMaterialRenderShiCai:LoadCallBack()

end

function ChuShenMaterialRenderShiCai:__delete()
	if self.timer_quest then
		GlobalTimerQuest:CancelQuest(self.timer_quest)
		self.timer_quest = nil
	end
end

function ChuShenMaterialRenderShiCai:OnFlush()
	local data = self:GetData()
	if not data then
		return 
	end

	local interface_cfg = ChuShenWGData.Instance:GetChuShenOtherCfg()
 	if interface_cfg then
 		self.node_list["item_image_dengzi"].image:LoadSprite(ResPath.GetChuShenImg(interface_cfg.pic_30))
 	end

	self.node_list.SelectEffect2:SetActive(false)
	
	local item_cfg = ItemWGData.Instance:GetItemConfig(data.item_id)
	if not item_cfg then
		return
	end

	local has_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id)
	local list_has_num = ChuShenWGData.Instance:GetLastNum(data.item_id)

	local bundle, assest = ResPath.GetItem(item_cfg.icon_id)
	self.node_list.item_image.image:LoadSprite(bundle, assest)

	local b, a = ResPath.GetCommonImages("a2_sc_btn_bg_" .. item_cfg.color)
	self.node_list["material_pos"].image:LoadSprite(b, a, function ()
			XUI.ImageSetNativeSize(self.node_list["material_pos"])
		end)

	local parti_b, parti_a = self:GetPartiAsesst(item_cfg.color)
	self.node_list["item_parti"]:ChangeAsset(parti_b, parti_a)
	self.node_list["item_parti"]:SetActive(item_cfg.color > SHOW_EFFECT_LEVEL)
	-- XUI.SetGraphicGrey(self.node_list.material_pos, has_num - list_has_num <= 0) 
	-- XUI.SetGraphicGrey(self.node_list.item_image, has_num - list_has_num <= 0) 
	self.node_list.num_text.text.text = ""
	self:SetRenderMaterialNum(has_num - list_has_num)

	if data.is_special == 1 then
		self.node_list.SelectEffect2:SetActive(list_has_num > 0)
	else
		self.node_list.SelectEffect2:SetActive(false)
	end
end

function ChuShenMaterialRenderShiCai:GetPartiAsesst(color)
 	local parti_name = BaseCell_Ui_Circle_Effect[color]
 	if parti_name then
 		return ResPath.GetWuPinKuangEffectUi(parti_name)
 	end
 end

function ChuShenMaterialRenderShiCai:SetRenderMaterialNum( num )
	local is_show_arrow = ChuShenWGData.Instance:GetPanelAddMark()
	if num <= 0 then
		self.node_list.num_text.text.text = ToColorStr(num, COLOR3B.D_RED)
	else
		self.node_list.num_text.text.text = num
	end
	local is_num_add = ChuShenWGData.Instance:GetLastNum(self.data.item_id)
	-- UITween.MoveLoop(self.node_list["remind_arrow"], Vector2(0, 55), Vector2(0, 48), 0.9)
	-- self.node_list.remind_arrow:SetActive(is_show_arrow and num > 0 and self.data.put_time > is_num_add)
	self.node_list.remind_arrow:SetActive(false)

	if self.data.is_special == 1 and is_num_add > 0 then
	else
		if is_show_arrow and num > 0 and self.data.put_time > is_num_add then
			ChuShenWGData.Instance:SetPanelAddMark(false)
		end
	end
end

function ChuShenMaterialRenderShiCai:GetPosNode()
	return self.node_list.material_pos
end

function ChuShenMaterialRenderShiCai:SetAddArrowShow(is_show)
	self.node_list.remind_arrow:SetActive(false)
end