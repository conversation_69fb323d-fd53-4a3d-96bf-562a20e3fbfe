--vip主界面按钮 做成列表形式方便后期添加
local ViewVipButtonButton = {
	ButtonVIP = {GuideModuleName.Recharge, nil, ["remind"] = RemindName.Vip},
	btn_recharge_volume = {GuideModuleName.RechargeVolumeView, nil, ["remind"] = RemindName.RechargeVolume},
	ButtonYiNianMagic = {GuideModuleName.YinianMagicView, nil, ["remind"] = RemindName.YinianMagic},
	btn_billion_subsidy = {GuideModuleName.BillionSubsidy, nil, ["remind"] = RemindName.BillionSubsidy},
}

function MainUIView:__initPlayer()
	self.change_buff_list = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.FlushPlayerBuffer, self))
	self.exp_info = GlobalEventSystem:Bind(MainUIEventType.EXP_EFFICIENCY_INFO, BindTool.Bind(self.FlushPlayerBuffer, self))
end

function MainUIView:__deletePlayer()
	if self.change_buff_list then
		GlobalEventSystem:UnBind(self.change_buff_list)
		self.change_buff_list = nil
	end

	if self.exp_info then
		GlobalEventSystem:UnBind(self.exp_info)
		self.exp_info = nil
	end
end

function MainUIView:Player__ReleaseCallBack()
	self.can_change_mode = nil
	-- if self.change_head_icon then
	-- 	GlobalEventSystem:UnBind(self.change_head_icon)
	-- 	self.change_head_icon = nil
	-- end

	if self.vip_button_render then
		self.vip_button_render:DeleteMe()
		self.vip_button_render = nil
	end

	if self.vip_free_render then
		self.vip_free_render:DeleteMe()
		self.vip_free_render = nil
	end

	if self.vip_zero_buy_render then
		self.vip_zero_buy_render:DeleteMe()
		self.vip_zero_buy_render = nil
	end

	if self.role_head_cell then
		self.role_head_cell:DeleteMe()
		self.role_head_cell = nil
	end

	if self.player_info_buff_list then
		self.player_info_buff_list:DeleteMe()
		self.player_info_buff_list = nil
	end

	if self.stop_gather_event then
		GlobalEventSystem:UnBind(self.stop_gather_event)
		self.stop_gather_event = nil
	end

	if self.star_gather_event then
		GlobalEventSystem:UnBind(self.star_gather_event)
		self.star_gather_event = nil
	end

	if self.cultivation_exp_event then
		GlobalEventSystem:UnBind(self.cultivation_exp_event)
		self.cultivation_exp_event = nil
	end

	if self.show_buff_list then
		for k, v in pairs(self.show_buff_list) do
			v:DeleteMe()
		end

		self.show_buff_list = nil
	end

	self:RemoveHpCD()
	self:RemoveCultivationTimer()
end

function MainUIView:PlayerLoadCallBack()
	self.node_list["BtnPortraitLabel"].button:AddClickListener(BindTool.Bind(self.OnClickMainRoleHead, self))
	self.node_list["ButtonMode"].button:AddClickListener(BindTool.Bind(self.OnClickChangeMode, self))
	XUI.AddClickEventListener(self.node_list.BuffAddBt, BindTool.Bind(self.OnClickShowBuffer, self))
	XUI.AddClickEventListener(self.node_list.btn_cash_point, BindTool.Bind(self.OnClickCasnPoint, self))
	XUI.AddClickEventListener(self.node_list.btn_recharge_volume, BindTool.Bind(self.OnClickRechargeVolumeBtn, self))
	XUI.AddClickEventListener(self.node_list.ButtonYiNianMagic, BindTool.Bind(self.OnClickYinnianMagicBtn, self))
	XUI.AddClickEventListener(self.node_list["CameraMode"], BindTool.Bind(self.OnClickCameraMode, self)) 				-- 相机模式
	XUI.AddClickEventListener(self.node_list.BtnExpPool, BindTool.Bind(self.OnClickBtnExpPool, self))
	XUI.AddClickEventListener(self.node_list.btn_billion_subsidy, BindTool.Bind(self.OnClickBillionSubsidyBtn, self))

	self.cur_show_buff_index = 1
	self.show_buff_wait_list = {}
	if not self.show_buff_list then
		self.show_buff_list = {}
		for i = 1, 3 do
			local cell = PlayerInfoShowBuffRender.New(self.node_list["show_buff_render" .. i])
			cell:SetAniCompleteCallBack(BindTool.Bind(self.PlayGetNewBuffAni, self))
			self.show_buff_list[i] = cell
		end
	end

	self.vip_button_render = VipButtonRender.New(self.node_list.ButtonVIP)
	self.node_list["ButtonVIP"].button:AddClickListener(function()
		local tab_index = RechargeWGData.Instance:GetVIPDefultIndex()
		ViewManager.Instance:Open(GuideModuleName.Vip, tab_index)
	end)

	if VipWGData.Instance:CheckHasFreeCard() then
		self.vip_free_render = VipFreeRender.New(self.node_list.task_btn_trans)
	end

	if RechargeWGData.Instance:HasVipZeroBuy() then
		self.vip_zero_buy_render = VipZeroBuyRender.New(self.node_list.ButtonGroup1)
	end

   	-- self.change_head_icon = GlobalEventSystem:Bind(OtherEventType.CHANGE_HEAD_ICON, BindTool.Bind(self.ChangeHeadIcon, self))

	self.can_change_mode = true
	self:PlayerChangeHeadIcon()
	self:SetVipRedEvent()

	if not self.player_info_buff_list then
		self.player_info_buff_list = AsyncListView.New(PlayerInfoBuffCellItemRender, self.node_list.player_info_buff_list)
	end

	FunOpen.Instance:RegisterFunUi(FunName.CashPointView, self.node_list["btn_cash_point"])
	FunOpen.Instance:RegisterFunUi(FunName.RechargeVolume, self.node_list["btn_recharge_volume"])

	self.stop_gather_event = GlobalEventSystem:Bind(ObjectEventType.STOP_GATHER, BindTool.Bind(self.OnStopGather, self))
	self.star_gather_event = GlobalEventSystem:Bind(ObjectEventType.START_GATHER, BindTool.Bind(self.OnStarGather, self))
	-- 主界面屏蔽显示
	-- self.cultivation_exp_event = GlobalEventSystem:Bind(CULTIVATION_CHANGE.ADD_EXP_CHANGE, BindTool.Bind(self.OnCultivationShow, self))

	self:InitGuaJiState()
	self:InitCameraModeText()

	XUI.AddClickEventListener(self.node_list["send_location_btn"], function()
		self.node_list["send_location_panel"]:SetActive(not self.node_list["send_location_panel"].gameObject.activeSelf)
	end)

	-- 发送坐标面板
	self.node_list["send_location_panel"]:SetActive(false)
	XUI.AddClickEventListener(self.node_list["location_btn_panel_mask"], function()
		self.node_list["send_location_panel"]:SetActive(false)
	end)

	XUI.AddClickEventListener(self.node_list["world_location_btn"], BindTool.Bind(self.ChatSendLocation, self, CHANNEL_TYPE.WORLD))
	XUI.AddClickEventListener(self.node_list["guild_location_btn"], BindTool.Bind(self.ChatSendLocation, self, CHANNEL_TYPE.GUILD))
	XUI.AddClickEventListener(self.node_list["team_location_btn"], BindTool.Bind(self.ChatSendLocation, self, CHANNEL_TYPE.TEAM))
    XUI.AddClickEventListener(self.node_list["cross_location_btn"], BindTool.Bind(self.ChatSendLocation, self, CHANNEL_TYPE.CROSS))

	self:FlushLocationPanel()
end

function MainUIView:PlayerCloseCallBack()
	self:RemoveHpCD()
	self:RemoveCultivationTimer()
end

function MainUIView:PlayerChangeSceneBack(old_scene_type, new_scene_type)
    if self.vip_button_render then
        self.vip_button_render:OnSceneChangeComplete(old_scene_type, new_scene_type)
    end
end

-- 设置vip红点监听
function MainUIView:SetVipRedEvent()
	local icon_node_list = {}
	for k,v in pairs(ViewVipButtonButton) do
		icon_node_list[k] = self.node_list[k]
		if icon_node_list[k] then
			self:SetMainIconNodeListObj(k, icon_node_list[k])
			icon_node_list[k].remind = icon_node_list[k].transform:FindByName("RedPoint")
			if v["remind"] and not self.icon_remind_list[v["remind"]] then
				self.vip_remind_list[v["remind"]] = k
				self.vip_remind_list_num[v["remind"]] = 0
				RemindManager.Instance:Bind(self.remind_change, v["remind"])
			end
		end
	end
	
	FunOpen.Instance:RegisterFunOpenUi(ViewVipButtonButton, icon_node_list)
end

-- 设置vip按钮显示
function MainUIView:SetVIPBtnState(enable)
	if IS_AUDIT_VERSION then
		enable = false
	end

	if VipWGData.Instance:CheckHasFreeCard() then
		if self.vip_free_render then
			self.vip_free_render:SetVisible(enable)
		end

		-- self.node_list.ButtonVIP:SetActive(false)
	else
		-- print_error("vip开启",FunOpen.Instance:GetFunIsOpened(FunName.RechargeVip))
		self.node_list.ButtonVIP:SetActive(enable and FunOpen.Instance:GetFunIsOpened(FunName.RechargeVip))
	end
end

--设置主角Vip
function MainUIView:SetMainRoleVip()
	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		self.vip_button_render:Flush()

		if self.vip_free_render then
			self.vip_free_render:Flush()
		elseif VipWGData.Instance:CheckHasFreeCard() then
			self.vip_free_render = VipFreeRender.New(self.node_list.task_btn_trans)
		end
		
		if self.vip_zero_buy_render then
			self.vip_zero_buy_render:Flush()
		end
		-- self:CheckAndPlayExpAdd()
	end)
end

function MainUIView:CheckAndPlayExpAdd()
	local info = VipWGData.Instance:GetMainUITweenInfo()
	if not info or not info.need_effect then
		return
	end

	local star_obj = self.node_list.vip_exp_effect_pos
	local end_obj = self.node_list.vip_exp_effect_end
	VipWGData.Instance:ClearEffectData()
	self:PlayVIPEXPEffectByRecharge(star_obj, end_obj)
end

function MainUIView:PlayVIPEXPEffectByRecharge(star_obj, end_obj)
	local bundle_name, asset_name = ResPath.GetEffectUi("UI_vip_jinyan")
	EffectManager.Instance:PlayAtTransform(bundle_name, asset_name, star_obj.transform, 2)
	GlobalTimerQuest:AddDelayTimer(function ()
        self:PlayVIPEXPEffectByRechargeSecond(star_obj,end_obj)
    end, 0.5)
end

function MainUIView:PlayVIPEXPEffectByRechargeSecond(star_obj, end_obj)
	TipWGCtrl.Instance:DestroyFlyEffectByViewName(GuideModuleName.Bag)
    TipWGCtrl.Instance:ShowFlyEffectManager("MainTaskGoldGetParticle", "effects2/prefab/ui/ui_jinseguangqiu_prefab",
       "UI_jinseguangqiu", star_obj, end_obj, DG.Tweening.Ease.OutCubic, 0.5, nil, nil, 18, 150, nil, false, true)
end

-- 点击主角头像
function MainUIView:OnClickMainRoleHead()
	RoleWGCtrl.Instance:OpenView()
end

-- 设置是否可切换模式
function MainUIView:SetButtonModeClick(enable)
	self.can_change_mode = enable
end

-- 设置切换模式
function MainUIView:SetAttackMode(atk_mode)
	-- print_error("---------------------------切换模式", atk_mode)
	-- if not self.node_list or not self.node_list.lbl_peacemode then
	-- 	return
	-- end

	if not self.node_list or not self.node_list.lbl_peacemode_text then
		return
	end

	self.atk_mode = atk_mode

	local color = ATTACK_MODE_COLOR[atk_mode] or ATTACK_MODE_COLOR[0]
	self.node_list.lbl_peacemode_text.text.text = Language.Common.AttackMode[atk_mode] -- ToColorStr(Language.Common.AttackMode[atk_mode] or "", color)
end

-- 点击切换模式
function MainUIView:OnClickChangeMode()
    local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	if fb_cfg and fb_cfg.cant_change_mode == 1 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.CannotChangeMode_1)
		return
	end

	if IS_ON_CROSSSERVER and not Scene.Instance:IsShowSwitchServerModeScene() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.CannotChangeMode)
		return
	end

	local scene_id = Scene.Instance:GetSceneId()
	--策划说主城默认国家模式 不给切换
	if scene_id == 1003 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.CannotChangeMode)
		return
	end

	local scene_logic = Scene.Instance:GetSceneLogic()
    if not scene_logic:CanChangeAttackMode() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Activity.CannotChangeMode)
		return
	end

	if not self.can_change_mode then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Boss.BtnChangeTips)
		return
	end

	local scene_type = Scene.Instance:GetSceneType()

	-- 养龙寺限制切换模式， 如后续有新增 世界等级限制切换模式的添加场景配置字段控制
	if scene_type == SceneType.SCENE_TYPE_CROSS_YANGLONG then
		local limit_world_level = YangLongSiaWGData.Instance:GetOtherCfgData("force_peace_cross_world_level") or 0
		local world_level = CrossServerWGData.Instance:GetServerMeanWorldLevel()
		if world_level < limit_world_level then
			SysMsgWGCtrl.Instance:ErrorRemind(string.format(Language.Activity.CannotChangeMode_2, RoleWGData.GetLevelString2(limit_world_level)))
			return
		end
	end

	local main_role = Scene.Instance:GetMainRole()
	if main_role:CantPlayerDoMove(true) then
		return
	end

	MainuiWGCtrl.Instance:OpenAttackModeView()
end

-- 设置主角战力
function MainUIView:SetMainRoleCapability(value)
	self.last_capability = value
	self.node_list["TextZhanLi"].text.text = string.format(Language.Mainui.CapStr, CommonDataManager.ConverExpByThousand(value))
	RoleWGData.Instance:SetMainRoleCap(value)
end

-- 点击BUFF状态
function MainUIView:OnClickShowBuffer()
	BuffTip.Instance:SetContent()
end

function MainUIView:FlushPlayerBuffer()
	local flush_buff = function()
		local main_role_all_effect_list = FightWGData.Instance:GetMainRoleShowEffect()
		local len =  GetTableLen(main_role_all_effect_list)
		self.node_list.BufferNum.text.text = string.format(Language.Common.StateBuff, len)

		-- local buff_list = FightWGData.Instance:GetMainRoleShowEffect()
		local target_buff_data_list = {}
		if not IsEmptyTable(main_role_all_effect_list) then
			local target_buff_icon_cache = {}

			for k, v in pairs(main_role_all_effect_list) do
				local client_effect_type = (v.info or {}).client_effect_type or -1

				if client_effect_type > 0 then
					local cfg = FightWGData.Instance:GetBuffDescCfgByType(client_effect_type)

					if cfg and cfg.icon and nil == target_buff_icon_cache[cfg.icon] then
						target_buff_icon_cache[cfg.icon] = cfg.icon
						table.insert(target_buff_data_list, {client_effect_type = client_effect_type})
					end
				end
			end

			target_buff_icon_cache = nil
		end

		self.player_info_buff_list:SetDataList(target_buff_data_list)
	end
	MainuiWGCtrl.Instance:AddInitCallBack(nil,flush_buff)
end

function MainUIView:UpdateMainRoleHp()
	local main_vo = GameVoManager.Instance:GetMainRoleVo()
	self:SetMainRoleHp(main_vo.hp, main_vo.max_hp)
end

--设置主角血量
function MainUIView:SetMainRoleHp(hp, max_hp)
	max_hp = max_hp <= 0 and 1 or max_hp
	local cur_hp = hp
	local percent = (cur_hp / max_hp)
	if percent >= 0.1 then
		self:StopLowHpTipEffect()
	else
		self:PlayLowHpTipEffect()
	end
	--local main_role = Scene.Instance:GetMainRole()
	--if main_role:IsGhost() then
	--	cur_hp = 0
	--	percent = 0
	--end
	-- self.node_list["SliderHPTop"].slider.value = percent
	self.node_list["SliderHPTopImg"].image.fillAmount = percent
	self.node_list.slider_role_hp.slider.value = percent
	self.node_list["TextRoleHP"].text.text = CommonDataManager.ConverNumber(cur_hp, nil, false) .. "/" .. CommonDataManager.ConverNumber(max_hp, nil, false)
	-- self:FlushPlayerBuffer()
end

function MainUIView:RemoveHpCD()
	 if self.hp_cd then
		GlobalTimerQuest:CancelQuest(self.hp_cd)
		self.hp_cd = nil
	end
end

function MainUIView:RemoveCultivationTimer()
	if self.cultivation_timer then
	   GlobalTimerQuest:CancelQuest(self.cultivation_timer)
	   self.cultivation_timer = nil
   end
   	if self.cultivation_tween then
		self.cultivation_tween:Kill()
		self.cultivation_tween = nil
	end
   self.node_list.root_cultivation.canvas_group.alpha = 0
   self.node_list.root_cultivation:CustomSetActive(false)
   
end

local hp_flag = true
function MainUIView:UpdateHpCD()
	local alpha = self.node_list["LowHpEffectRoot"].canvas_group.alpha
	if hp_flag then
		alpha = alpha + 0.1
		if alpha >= 1 then
			hp_flag = false
		end
	else
		alpha = alpha - 0.1
		if alpha <= 0 then
			hp_flag = true
		end
	end

	self.node_list["LowHpEffectRoot"].canvas_group.alpha = alpha
end

function MainUIView:PlayLowHpTipEffect()
	if not self.node_list["LowHpEffectRoot"] then
		return
	end

	self.node_list["LowHpEffectRoot"]:SetActive(true)
	self:RemoveHpCD()
	self.hp_cd = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.UpdateHpCD, self), 0.05)
end

function MainUIView:StopLowHpTipEffect()
	if not self.node_list["LowHpEffectRoot"] then return end
	self.node_list["LowHpEffectRoot"]:SetActive(false)
	self:RemoveHpCD()
end

--设置主角等级
function MainUIView:SetMainRoleLevel(value)
	local is_vis, level = RoleWGData.Instance:GetDianFengLevel(value)
	self.node_list.dianfen_img:SetActive(is_vis)
	self.node_list["TextLevel"].text.text = string.format(Language.Common.Level1, level) 
end

-- 设置头像信息面板显示
function MainUIView:SetMianUIPlayerState(state)
	self.node_list["PlayerInfo_Root"].gameObject:SetActive(state)
end

-- 头像改变
function MainUIView:PlayerChangeHeadIcon()
	local role_vo = GameVoManager.Instance:GetMainRoleVo()
	self:GetRoleHeadCell()
	local appearance = role_vo and role_vo.appearance
	local data = {fashion_photoframe = appearance.fashion_photoframe}
	data.role_id = RoleWGData.Instance:InCrossGetOriginUid()
	data.prof = role_vo.prof
	data.sex = role_vo.sex
	data.is_show_main = true

	self.role_head_cell:SetImgBg(appearance and appearance.fashion_photoframe > 0)
	self.role_head_cell:SetData(data)
	self.role_head_cell:SetFrameScale(0.9)
	self.role_head_cell:SetSpineFrameScale(1)
end

function MainUIView:GetRoleHeadCell()
	if not self.role_head_cell then
		self.role_head_cell = BaseHeadCell.New(self.node_list["default_head_icon"])
	end
end

function MainUIView:FlushVipZeroBuy()
	if self.vip_zero_buy_render then
		self.vip_zero_buy_render:Flush()
	elseif RechargeWGData.Instance:HasVipZeroBuy() then
		self.vip_zero_buy_render = VipZeroBuyRender.New(self.node_list.ButtonGroup1)
	end
end

function MainUIView:OnClickCasnPoint()
	FunOpen.Instance:OpenViewByName(GuideModuleName.CashPointView)
end

function MainUIView:OnClickRechargeVolumeBtn()
	ViewManager.Instance:Open(GuideModuleName.RechargeVolumeView)
end

function MainUIView:OnClickBillionSubsidyBtn()
	ViewManager.Instance:Open(GuideModuleName.BillionSubsidy)
end

function MainUIView:OnClickYinnianMagicBtn()
	ViewManager.Instance:Open(GuideModuleName.YinianMagicView)
end

function MainUIView:FlushCashPoint()
	local fun_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.CashPointView)
	if fun_open and self.node_list.casn_point_text then
		local cash_point_num = RoleWGData.Instance:GetAttr("cash_point")
		self.node_list.casn_point_text.text.text = string.format(Language.CashPoint.CashPointBlance, cash_point_num)
	end
end

function MainUIView:FlushRechargeVolume()
	--local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.RechargeVolume)
	if self.node_list.recharge_volume_text then
		-- 计算每日使用额度
		-- local type = VIRTUAL_GOLD_TYPE.RECHARGE_VOLUME
		-- local max_num = RechargeWGData.Instance:GetAllVirtualGoldMaxUseNum(type)
		-- local use_num = RechargeWGData.Instance:GetVirtualGoldUseNum(type)
		-- local day_total_num = max_num
		-- local can_use_num = day_total_num - use_num
		local recharge_volume_num = RoleWGData.Instance:GetAttr("recharge_volume") or 0
		self.node_list.recharge_volume_text.text.text = string.format(Language.CashPoint.CashPointBlance, recharge_volume_num)
	end
end

-----------------------------------------仙修模块---------------------------------------------
function MainUIView:OnCultivationShow()
	if not FunOpen.Instance:GetFunIsOpened(FunName.CultivationView) then
		return
	end
	self.node_list.root_cultivation:CustomSetActive(true)
	self.cultivation_tween = self.node_list.root_cultivation.canvas_group:DoAlpha(0, 1, 0.5):SetEase(DG.Tweening.Ease.OutQuart)
	local number,rate = CultivationWGData.Instance:GetTotalExpAdd()
	self.node_list.text_add_exp.text.text = number
	self.node_list.text_add_rate.text.text = string.format(Language.Cultivation.ExpAddTotalStr3, rate)

	-- 当前等级的经验和上限
	local cur_xiuwei_level_cfg = CultivationWGData.Instance:GetCurXiuWeiLevelCfg()
    local cur_exp = CultivationWGData.Instance:GetXiuWeiExp()
	local old_exp = CultivationWGData.Instance:GetXiuWeiOldExp()
	
    self.node_list.text_cultivation_value.text.text = string.format(Language.Cultivation.XiuWeiValueAdd, old_exp, cur_exp-old_exp ,cur_xiuwei_level_cfg.need_exp)
	self.node_list.slider_cultivation.slider.value = old_exp / cur_xiuwei_level_cfg.need_exp
	self.node_list.img_add_cultivation.image.fillAmount = cur_exp / cur_xiuwei_level_cfg.need_exp

	self.cultivation_timer = GlobalTimerQuest:AddDelayTimer(function ()
		if self.cultivation_tween then
			self.cultivation_tween:Kill()
			self.cultivation_tween = nil
		end
		self.node_list.root_cultivation.canvas_group.alpha = 0
		self.node_list.root_cultivation:CustomSetActive(false)
    end, 3.5)
end

-----------------------------------------打坐模块---------------------------------------------
function MainUIView:OnStopGather()
	self:FlushSitShow(false)
	self:FlushXunLuStates()
end

function MainUIView:OnStarGather()
	self:FlushSitShow(true)
	self:FlushXunLuStates()
end

function MainUIView:FlushSitShow(is_gather)
	local is_in_sit = false
	local is_in_sit_area = false
	local main_role = Scene.Instance:GetMainRole()
	local is_xunyou = false
	local is_caiji = false
	local is_husong = false
	local is_bianshen = false
	local is_fight_mount = false
	local is_open_sit = FunOpen.Instance:GetFunIsOpened(FunName.RoleSit)
	if main_role ~= nil and main_role:GetVo() ~= nil then
		is_in_sit = main_role:GetIsInSit()
		is_in_sit_area = main_role:IsInSitArea()
		is_xunyou = main_role:IsInXunYou()
		is_caiji = main_role:GetIsGatherState()
		is_husong = YunbiaoWGData.Instance:GetIsHuShong()
		local special_appearance = RoleWGData.Instance:GetAttr("special_appearance")
		if special_appearance == SPECIAL_APPEARANCE_TYPE.BIANSHEN_SYSTEM or special_appearance == SPECIAL_APPEARANCE_TYPE.GUNDAM then
			is_bianshen = true
		end

		is_fight_mount = main_role:IsRidingFightMount()
	end

	local show_root_sit = false
	if not is_in_sit and is_in_sit_area and not is_xunyou and is_open_sit and not is_caiji and not is_bianshen and not is_fight_mount and not is_husong then
		self.node_list.btn_sit:SetActive(true)
		self.node_list.root_sitinfo:SetActive(false)
		show_root_sit = true
		-- MainuiWGCtrl.Instance:PlayAutoSitAni(false)
	elseif is_in_sit and is_in_sit_area and not is_xunyou and not is_husong then
		self.node_list.btn_sit:SetActive(false)
		self.node_list.root_sitinfo:SetActive(true and not IS_AUDIT_VERSION)
		show_root_sit = not IS_AUDIT_VERSION
		--  MainuiWGCtrl.Instance:PlayAutoSitAni(true)
		self:FlushSitInfo()
	else
		-- MainuiWGCtrl.Instance:PlayAutoSitAni(false)
		self.node_list.btn_sit:SetActive(false)
		self.node_list.root_sitinfo:SetActive(false)
		show_root_sit = false
	end

	self.node_list.root_sit:CustomSetActive(show_root_sit)

	self:CheckGuaJiBtnShow(not is_in_sit)

	-- local has_red = OfflineRestWGData.Instance:GetLiLianItemCount() > 0
	-- if not is_sit and has_red then
	-- 	self:InvateTip(MAINUI_TIP_TYPE.SIT_REARD, 1, BindTool.Bind(self.OnClickSitBox, self))
	-- else
	-- 	self:InvateTip(MAINUI_TIP_TYPE.SIT_REARD, 0)
	-- end
end

function MainUIView:SetXunLuStatus(value)
    local main_role = Scene.Instance:GetMainRole()

    if main_role ~= nil and not main_role:IsDeleted() and main_role:IsTeamFollowState() then
    	self.node_list.auto_team_follow:SetActive(true)
    	self.node_list.root_normal_auto:SetActive(true)
    	return
    else
    	self.node_list.auto_team_follow:SetActive(false)
    end

	local show_auto_team_follow = false

	if self.node_list["auto_fight"] or self.node_list["auto_path_find"] or self.node_list["auto_wabao"] or self.node_list["auto_sit"] then
		local main_role = Scene.Instance:GetMainRole()
		local is_qinggong = main_role:IsQingGong()
		local is_mitsurugi = main_role:IsMitsurugi()
		local scene_type = Scene.Instance:GetSceneType()

		local show_auto_path_find = false
		local show_auto_fight = false
		local show_auto_follow = false
		local show_auto_answer = false
		local show_auto_wabao = value == XunLuStatus.AutoWaBao and not is_qinggong and not is_mitsurugi and not is_multi_mount_take
		local show_auto_multi_mount = MultiMountWGData.Instance:IsMyMultiMountTake()

		if scene_type == SceneType.KF_DUCK_RACE then
			-- show_auto_path_find = false
			-- show_auto_fight = false
			show_auto_follow = ((value == XunLuStatus.XunLu or value == XunLuStatus.AutoFight) and not is_qinggong and not is_mitsurugi)
			-- show_auto_answer = false
		elseif scene_type == SceneType.TianShen3v3 then
			local x, y = Scene.Instance:GetMainRole():GetLogicPos()
			local in_point = TianShen3v3WGData.Instance:InPoint(x, y)

			show_auto_path_find = (value == XunLuStatus.XunLu and not is_qinggong and not main_role:GetIsGatherState() and not in_point and not is_mitsurugi)
			show_auto_fight = (value == XunLuStatus.AutoFight and not is_qinggong and not main_role:GetIsGatherState() and not in_point and not is_mitsurugi)
			-- show_auto_follow = false
			-- show_auto_answer = false
		elseif scene_type == SceneType.BOSS_INVASION then
			local status = BOSSInvasionWGData.Instance:GetCurActStatus()
			local is_get_privilege = BOSSInvasionWGData.Instance:IsGetPrivilege()
			show_auto_fight = ((value == XunLuStatus.XunLu or value == XunLuStatus.AutoFight) and status == CROSS_BOSS_STRIKE_STATUS.STATUS_BOSS)
			-- show_auto_follow = false
			show_auto_answer = (is_get_privilege and (value == XunLuStatus.XunLu or value == XunLuStatus.AutoFight) and status == CROSS_BOSS_STRIKE_STATUS.STATUS_QUESTION)
		else
			show_auto_path_find = (value == XunLuStatus.XunLu and not is_qinggong and not main_role:GetIsGatherState() and not is_mitsurugi and not is_multi_mount_take)
			show_auto_fight = (value == XunLuStatus.AutoFight and not is_qinggong and not main_role:GetIsGatherState() and not is_mitsurugi and not is_multi_mount_take)
			-- show_auto_follow = false
			-- show_auto_answer = false
		end

		self.node_list["auto_wabao"]:CustomSetActive(show_auto_wabao)
		self.node_list["auto_path_find"]:CustomSetActive(show_auto_path_find)
		self.node_list["auto_fight"]:CustomSetActive(show_auto_fight)
		self.node_list["auto_follow"]:CustomSetActive(show_auto_follow)
		self.node_list["auto_answer"]:CustomSetActive(show_auto_answer)
		self.node_list["auto_multi_mount"]:CustomSetActive(show_auto_multi_mount)

		show_auto_team_follow = show_auto_wabao or show_auto_path_find or show_auto_fight or show_auto_follow or show_auto_answer or show_auto_multi_mount
	-- if XunLuStatus.None == value then
  --       	GlobalEventSystem:Fire(OtherEventType.GUAJI_TYPE_CHANGE, GuajiType.None) --取消自动寻路时挂机 挂机标记也要去掉
  --       end
	end

	self.node_list.root_normal_auto:SetActive(show_auto_team_follow)
end

-- function MainUIView:SetSitStatus(value)
-- 	--暂时不用这个提示了
-- 	--self.node_list["auto_sit"]:SetActive(value)
-- end

-----------------------------------------相机----------------------------------------------
function MainUIView:InitCameraModeText()
	local guide_flag_list = SettingWGData.Instance:GetSettingDataListByKey(HOT_KEY.CAMERA_KEY_FLAG)
	local flag = guide_flag_list.item_id or 1
	self.node_list["CameraModeText3D"]:CustomSetActive(flag == 1)
	self.node_list["CameraModeTextLock"]:CustomSetActive(flag ~= 1)
end

--显示隐藏 相机锁定按钮
function MainUIView:SetCameraModeBtnState( enable )
	self.node_list.CameraMode:SetActive(enable)
end

function MainUIView:OnClickCameraMode(is_ignore_scene, is_click)
	if not Scene.Instance:GetIsCanChangeCameraMode() then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NotCanChangeCameraMode)
		return
	end

	self:SelectCameraModeViewClose()
	if RoleWGData.Instance:IsHoldAngle() then
		return
	end

	local guide_flag_list = SettingWGData.Instance:GetSettingDataListByKey(HOT_KEY.CAMERA_KEY_FLAG)
	local flag = guide_flag_list.item_id or CameraType.Free
	if flag == CameraType.Free then
		flag = CameraType.Fixed
	else
		flag = CameraType.Free
	end

	self.node_list["CameraModeText3D"]:CustomSetActive(flag == CameraType.Free)
	self.node_list["CameraModeTextLock"]:CustomSetActive(flag ~= CameraType.Free)

	Scene.Instance:SetCameraMode(flag, is_click)
	if flag == CameraType.Free then
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CamerModeFree)
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.Common.CamerModeLock)
	end

	SettingWGCtrl.Instance:SendChangeHotkeyReq({[1] = {HOT_KEY.CAMERA_KEY_FLAG, flag}})
	SettingWGData.Instance:SetSettingDataListByKey(HOT_KEY.CAMERA_KEY_FLAG, flag)
end

-- 修改相机锁定按钮位置
function MainUIView:SetCameraModeBtnPos(pos_x, pos_y)
	if pos_x and pos_y then
		self.cache_camera_mode_btn_pos = self.node_list.CameraMode.rect.anchoredPosition
		self.node_list.CameraMode.rect.anchoredPosition = Vector2(pos_x, pos_y)
	elseif self.cache_camera_mode_btn_pos then
		self.node_list.CameraMode.rect.anchoredPosition = self.cache_camera_mode_btn_pos
		self.cache_camera_mode_btn_pos = nil
	end
end

-------------------------------------------挂机按钮----------------------------------------
--初始化挂机
function MainUIView:InitGuaJiState()
	self.guaji_change_event = GlobalEventSystem:Bind(OtherEventType.GUAJI_TYPE_CHANGE, BindTool.Bind(self.BindGuaJiFireEvent,self))
	XUI.AddClickEventListener(self.node_list["btn_guaji"],BindTool.Bind(self.OnClickGuaji, self))				--挂机

	self:CheckGuaJiBtnShow()
end

function MainUIView:CheckGuaJiBtnShow(value)
	local is_show = true
	if value == nil then
		local main_role = Scene.Instance:GetMainRole()
		if main_role ~= nil then
			is_show = not main_role:GetIsInSit() and self:GetCanShowGuajiBtn()
		end
	else
		is_show = value and self:GetCanShowGuajiBtn()
	end
    self.node_list["btn_guaji"]:SetActive(is_show)
end

--挂机按钮点击事件
function MainUIView:OnClickGuaji()
	GuajiWGCtrl.Instance:ResetRecoveryFollowCache()
	local main_role = Scene.Instance:GetMainRole()

	local function click_fun()
		local scene_type = Scene.Instance:GetSceneType()
		if scene_type == SceneType.XianMengzhan and GuildBattleRankedWGCtrl.Instance:GetIsEnd() then
			return
		end

		if scene_type == SceneType.Kf_Honorhalls then
			local activity_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.KF_HONORHALLS)
			if activity_info.status ~= ACTIVITY_STATUS.OPEN then
				local is_act = self.node_list["guaji_normal"]:GetActive()
				self.node_list["guaji_normal"]:SetActive(not is_act)
				self.node_list["guaji_select"]:SetActive(is_act)
				return
			end
		end

		--飞升挂机提示
		-- if LevelFeiShengData.Instance:GetIsLevelFeiShengWinSceneType(scene_type) then
		-- 	SysMsgWGCtrl.Instance:ErrorRemind(Language.LevelFeiSheng.GuaJiTips)
		-- 	return
		-- end

		local main_role = Scene.Instance:GetMainRole()
		if main_role == nil then
			return
		end

		if main_role:IsGhost() then
			return
		end

		if main_role:CantPlayerDoMove(true) then
			return
		end

		MainuiWGCtrl.Instance:StopAutoRevenge()
		main_role:SetIsFollowState(false)
		GuajiWGCtrl.Instance:ResetRecoveryFollowCache()

		local scene_logic = Scene.Instance:GetSceneLogic()
		if scene_logic ~= nil then
			scene_logic:ClearGuaJiInfo()
		end

		GuajiWGCtrl.Instance:ClearTemporary()
		local is_auto = false
		if GuajiCache.guaji_type ~= GuajiType.Auto and GuajiCache.guaji_type ~= GuajiType.Temporary then
			GuajiWGCtrl.Instance:SetGuajiType(GuajiType.Auto)
			is_auto = true
		else
			GuajiWGCtrl.Instance:StopGuaji(true, true)
			is_auto = false
		end
		
		GlobalEventSystem:Fire(OtherEventType.HAND_CLICK_GUAJI_BTN, is_auto)
	end

    if main_role ~= nil and 0 < main_role:GetTaskToBianShen() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Task.task_bianshen03)
        return
    end

	if MultiMountWGData.Instance:IsMyMultiMountTake() then
		local ok_func = function()
			MultiMountWGCtrl.Instance:SendDoubleMountOperate(DOUBLE_MOUNT_OPERATE_TYPE.CANCLE_RIDE)
		end

		TipWGCtrl.Instance:OpenAlertTips(Language.MultiMount.CancelMultiMountTake, ok_func)
		return
	end

	if main_role ~= nil and not main_role:IsDeleted() and main_role:IsTeamFollowState() then
		local function follow_ok_fun()
			if main_role ~= nil and not main_role:IsDeleted() then
				main_role:SetIsFollowState(false)
				MainuiWGCtrl.Instance:FlushXunLuStates()
			end

			click_fun()
		end

		TipWGCtrl.Instance:OpenAlertTips(Language.FollowState.RelieveTip, follow_ok_fun)
		return
	else
		click_fun()
	end
end

--绑定的事件
function MainUIView:BindGuaJiFireEvent( guaji_type )
	if not guaji_type then return end
	self.node_list["guaji_normal"]:SetActive(false)
	self.node_list["guaji_select"]:SetActive(false)
	self.node_list["guaji_hand"]:SetActive(false)
	if guaji_type == GuajiType.Temporary then
		self.node_list["guaji_hand"]:SetActive(true)
	elseif guaji_type == GuajiType.None then
		self.node_list["guaji_normal"]:SetActive(true)
	else
		self.node_list["guaji_select"]:SetActive(true)
	end
end

function MainUIView:OnClickHandle()
	self:OnClickGuaji()
	TaskGuide.Instance:CanAutoAllTask(false)
end

--显示隐藏 挂机按钮
function MainUIView:SetGuajiBtnState( enable )
	self.node_list.guaji:SetActive(enable)
end

-- 分享坐标
function MainUIView:ChatSendLocation(channel)
    local is_spc_show = channel == CHANNEL_TYPE.CROSS
    if is_spc_show and not ChatWGData.Instance:CanSharePosInCrossChannel() then
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.CorssChannelLimitPos)
        return
    end

	if Scene.Instance:GetSceneType() == SceneType.GHOST_FB_GLOBAL then
        TipWGCtrl.Instance:ShowSystemMsg(Language.ZhuoGuiFuBen.LimitPosSend)
		return
	end

	ChatWGCtrl.Instance:SendMainRolePos(channel)
	self.node_list["send_location_panel"]:SetActive(false)
end

function MainUIView:FlushLocationPanel()
	if not self.is_load_chat then
		return
	end
	self.node_list["send_location_btn"]:SetActive(GetMainRoleLevel() >= COMMON_CONSTS.CHAT_LEVEL_LIMIT)
	self.node_list["world_location_btn"]:SetActive(true)
	self.node_list["guild_location_btn"]:SetActive(GuildWGData.Instance:HasGuild())
    self.node_list["team_location_btn"]:SetActive(SocietyWGData.Instance:GetIsInTeam() ~= 0)
    self.node_list["cross_location_btn"]:SetActive(ChatWGData.Instance:IsShowCrossSharePosBtn())
end

function MainUIView:GetNewBuffAddToAniWaitList(effect_info)
	table.insert(self.show_buff_wait_list, effect_info)
	self:PlayGetNewBuffAni()
end

function MainUIView:PlayGetNewBuffAni()


	local cell = self.show_buff_list[self.cur_show_buff_index]
	if cell and not cell:GetIsPlayAni() and #self.show_buff_wait_list > 0 then
		local effect_info = table.remove(self.show_buff_wait_list, 1)
		cell:AddNewBuff(effect_info, self.cur_show_buff_index)
		self.cur_show_buff_index = self.cur_show_buff_index + 1 > 3 and 1 or self.cur_show_buff_index + 1
	elseif #self.show_buff_wait_list == 0 then
		self.cur_show_buff_index = 1
	end
end

function MainUIView:GetPlayerInfoWidgetRoot()
    return self.node_list.player_info_widget_root
end

--------------------------------------经验池----------------------------------------
function MainUIView:OnClickBtnExpPool()
	ViewManager.Instance:Open(GuideModuleName.ExpPoolView)
end

function MainUIView:FlushBtnExpPoolViewState()
	if self.node_list.BtnExpPool then
		local is_get_extra_exp = ExpPoolWGData.Instance:IsGetVipExtraRoleExp()
	
		if is_get_extra_exp then
			self.node_list.BtnExpPool:CustomSetActive(false)
		else
			local fun_open = FunOpen.Instance:GetFunIsOpened(FunName.ExpPoolView)
			self.node_list.BtnExpPool:CustomSetActive(fun_open)

			if fun_open then
				local can_get = ExpPoolWGData.Instance:IsCanGetVipExtraRoleExp() > 0
				self.node_list.btn_exp_pool_icon1:CustomSetActive(not can_get)
				self.node_list.btn_exp_pool_icon2:CustomSetActive(can_get)
				self.node_list.btn_exp_pool_remind:CustomSetActive(can_get)
			end
		end
	end
end

-----------------------------------VipButtonRender-----------------------------------------
VipButtonRender = VipButtonRender or BaseClass(BaseRender)
function VipButtonRender:OnFlush()
	self.node_list.cur_vip_text.text.text = VipWGData.Instance:GetRoleVipLevel()
end

function VipButtonRender:OnSceneChangeComplete(old_scene_type, new_scene_type)
	local is_show = new_scene_type ~= SceneType.XianMengzhan
	MainuiWGCtrl.Instance:ChangeVipBtnShow(is_show)
end

----------------------------------PlayerInfoBuffCellItemRender---------------------------------
PlayerInfoBuffCellItemRender = PlayerInfoBuffCellItemRender or BaseClass(BaseRender)

function PlayerInfoBuffCellItemRender:LoadCallBack()
	XUI.AddClickEventListener(self.node_list.icon, BindTool.Bind(self.OnClickShowBuffer, self))
end

function PlayerInfoBuffCellItemRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	-- self.node_list.icon.image:LoadSprite(ResPath.GetBuff(self.data.info.client_effect_type))
	self.node_list.icon.image:LoadSprite(ResPath.GetBuff(self.data.client_effect_type))
end

function PlayerInfoBuffCellItemRender:OnClickShowBuffer()
	BuffTip.Instance:SetContent()
end

-----------------------------------PlayerInfoShowBuffRender-----------------------------------------
local ShowBuffPos = {
	[1] = {first_x = 100, first_y = 0, second_x = -240, second_y = 0},
	[2] = {first_x = 100, first_y = -30, second_x = -240, second_y = -30},
	[3] = {first_x = 100, first_y = -60, second_x = -240, second_y = -60},
}

local ShowBuffNormalPos = {
	[1] = {x = 100, y = -200},
	[2] = {x = 100, y = -200},
	[3] = {x = 100, y = -200},
}

PlayerInfoShowBuffRender = PlayerInfoShowBuffRender or BaseClass(BaseRender)
function PlayerInfoShowBuffRender:LoadCallBack()
	self.is_play_ani = false
end

function PlayerInfoShowBuffRender:__delete()
	if self.show_tweener then
		self.show_tweener:Kill()
		self.show_tweener = nil
	end
end

function PlayerInfoShowBuffRender:AddNewBuff(effect_info, show_index)
	local buff_desc_cfg = FightWGData.Instance:GetBuffDescCfgByInfo(effect_info.product_id, effect_info.buff_type, effect_info.client_effect_type)
	if not buff_desc_cfg or buff_desc_cfg.buff_name_is_show == 0 then
		return
	end

	self.node_list.show_buff_name.text.text = buff_desc_cfg.buff_name
	local color = buff_desc_cfg.buff_color == "" or COLOR3B.WHITE and buff_desc_cfg.buff_color
	self.node_list.show_buff_name.text.color = Str2C3b(color)
	if ShowBuffNormalPos[show_index] then
		self:SetAnchoredPosition(ShowBuffNormalPos[show_index].x, ShowBuffNormalPos[show_index].y)
	end

	if ShowBuffPos[show_index] then
		self.is_play_ani = true
		if self.show_tweener then
			self.show_tweener:Kill()
			self.show_tweener = nil
		end

		self.show_tweener = DG.Tweening.DOTween.Sequence()
		self.show_tweener:SetEase(DG.Tweening.Ease.OutQuad)
		self.show_tweener:Append(self.view.transform:DOAnchorPosY(ShowBuffPos[show_index].first_y, 0.3))
		self.show_tweener:Join(self.view.canvas_group:DoAlpha(0, 1, 0.3))
		self.show_tweener:AppendInterval(0.7)
		self.show_tweener:Append(self.view.transform:DOAnchorPosX(ShowBuffPos[show_index].second_x, 0.5))
		self.show_tweener:Join(self.view.canvas_group:DoAlpha(1, 0, 0.5))
		self.show_tweener:OnComplete(function()
			self.is_play_ani = false
			if self.complete_cb then
				self.complete_cb()
			end
		end)
	end
end

function PlayerInfoShowBuffRender:SetAniCompleteCallBack(complete_cb)
	self.complete_cb = complete_cb
end

function PlayerInfoShowBuffRender:GetIsPlayAni()
	return self.is_play_ani
end