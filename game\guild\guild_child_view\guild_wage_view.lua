-- 仙盟工资
function GuildView:InitGuildWageView()
	if not self.wage_task_list then
		self.wage_task_list = AsyncListView.New(GuildWageItemRender, self.node_list["ph_task_list"])
	end
	self.node_list["btn_guild_wage_states"].button:AddClickListener(BindTool.Bind1(self.OnClickState, self))
	self.node_list["reward_hint"].text.text = Language.GuildWage.FlushHint
end

function GuildView:DeleteGuildWageView()
	if self.wage_task_list then
		self.wage_task_list:DeleteMe()
		self.wage_task_list = nil
	end
end


function GuildView:OnFlushGuildWageView()
	local task_info = GuildWGData.Instance:GetGuildWageTaskInfo()
	if self.wage_task_list and not IsEmptyTable(task_info) then
		self.wage_task_list:SetDataList(task_info, 0)
	end
end

function GuildView:OnClickState()
	local role_tip = RuleTip.Instance
	if role_tip then
		role_tip:SetTitle(Language.GuildWage.GuildBuildWageTips)
		role_tip:SetContent(Language.GuildWage.GuildWageStateTips)
	end
end


----------------------------------------------------------------------------------------------------------------
GuildWageItemRender = GuildWageItemRender or BaseClass(BaseRender)
function GuildWageItemRender:__init()

end

function GuildWageItemRender:LoadCallBack()
	self.btn_lingqu = self.node_list["btn_lingqu"]
	self.btn_lingqu.button:AddClickListener(BindTool.Bind1(self.OnClickRewardHnadler, self))
	if not self.cell_list then
		self.cell_list = {}
	end
end

function GuildWageItemRender:__delete()
	self.btn_lingqu = nil

	if self.cell_list then
		for k,v in pairs(self.cell_list) do
			v:DeleteMe()
		end
		self.cell_list = nil
	end
end

function GuildWageItemRender:OnClickRewardHnadler()
	if self.data.states == GUILD_WAGE_TASK_STATES.WEIWANCHENG then
		if "" ~= self.data.old_info.activity_type then
			local activity_open = ActivityWGData.Instance:GetActivityIsOpen(self.data.old_info.activity_type)
			if not activity_open then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildWage.ActivityNoOpen)
				return
			end
		elseif self.data.old_info.type == GUILD_WAGE_OTHER_TYPE.TASK_TYPE1 then
			local activity_open = GuildBossWGData.Instance:IsGuildBossOpen()
			if not activity_open then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildWage.ActivityNoOpen)
				return
			end
		elseif self.data.old_info.type == GUILD_WAGE_OTHER_TYPE.TASK_TYPE11 then
			local mainrolevo = GameVoManager.Instance:GetMainRoleVo()
			local task_list = TaskWGData.Instance:GetTaskListIdByType(GameEnum.TASK_TYPE_MENG)
			local state = (0 ~= mainrolevo.guild_id and #task_list > 0)
			if not state then
				SysMsgWGCtrl.Instance:ErrorRemind(Language.GuildWage.GuildTaskFinish)
				return
			end
			local task_id = task_list and task_list[1]
			if task_id and task_id ~= 0 then
				MainuiWGCtrl.Instance:DoTask(task_id,TaskWGData.Instance:GetTaskStatus(task_id), true)
				if self.data.old_info.is_close_view == 1 then
					GuildWGCtrl.Instance:Close()
				end
			end
			return
		end
		FunOpen.Instance:OpenViewNameByCfg(self.data.old_info.open_panel)
		if self.data.old_info.is_close_view == 1 then
			GuildWGCtrl.Instance:Close()
		end
	elseif self.data.states == GUILD_WAGE_TASK_STATES.KELINGQU then
		GuildWGCtrl.Instance:SendGetGuildWage(self.data.old_info.type)
	end
end

function GuildWageItemRender:OnFlush()
	if not self.data then
		return
	end

	-- self.node_list["task_name"].text.text = self.data.old_info.task_name
	self.node_list["task_desc"].text.text = string.format(Language.GuildWage.TaseDec,self.data.old_info.time,self.data.old_info.task_name)
	local money_str = ResPath.GetF2MoneyIcon(self.data.old_info.money_type)
	local str = self.data.states == GUILD_WAGE_TASK_STATES.KELINGQU and Language.GuildWage.BtnName2 or Language.GuildWage.BtnName1
	self.node_list["lbl_btn_text"].text.text = str
	-- local process = self.data.cur_time.."/"..self.data.old_info.time
	-- local color = self.data.states ~= GUILD_WAGE_TASK_STATES.YILINGQU and COLOR3B.PINK or COLOR3B.DEFAULT_NUM
	local color = self.data.cur_time >= self.data.old_info.time and COLOR3B.GREEN or COLOR3B.RED
	local str = ToColorStr(string.format(Language.GuildWage.TaskProcess, self.data.cur_time,self.data.old_info.time), color)
	self.node_list["task_process"].text.text = str
	--self.node_list["task_process"]:SetActive(self.data.states ~= GUILD_WAGE_TASK_STATES.YILINGQU)
    -- self.node_list["task_slider"].slider.value = self.data.cur_time / self.data.old_info.time
	
	self.node_list["img_ylq"]:SetActive(self.data.states == GUILD_WAGE_TASK_STATES.YILINGQU)

	local remind_show = self.data.states == GUILD_WAGE_TASK_STATES.KELINGQU
	if self.data.old_info.type == GUILD_WAGE_OTHER_TYPE.TASK_TYPE11 then--仙盟周任务
		if GuildWGCtrl.Instance:GetWeekTaskRemindState() == 1 then
			remind_show = true
		end
	end
	self.node_list["image_redmind"]:SetActive(remind_show)
	local btn_icon_name = self.data.states == GUILD_WAGE_TASK_STATES.KELINGQU and "a3_ty_btn_9" or "a3_ty_btn_8"
	self.node_list["btn_lingqu"].image:LoadSprite(ResPath.GetCommonButton(btn_icon_name))
	local task_name = self.data.old_info.task_name
	self.node_list.task_name.text.text = task_name
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local task_open_level = self.data.old_info.task_open_level
	--self.node_list["lock_panel"]:SetActive(task_open_level > role_level)
	--self.node_list["bottom_panel"]:SetActive(task_open_level <= role_level)
	self.node_list["mask"]:SetActive(task_open_level > role_level)
	self.node_list.txt_open_level.text.text = string.format(Language.Common.LimitLevel,RoleWGData.GetLevelStringImg(task_open_level))
	self.btn_lingqu:SetActive(self.data.states ~= GUILD_WAGE_TASK_STATES.YILINGQU and task_open_level <= role_level)
	--XUI.SetGraphicGrey(self.node_list["ph_item"], task_open_level > role_level)
	self.node_list.score.text.text = string.format(Language.Guild.TaskScore, self.data.old_info.score)
	-- local bundle, asset = ResPath.GetGuildSystemImage("a2_xs" .. self.data.old_info.tag)
	-- self.node_list.img_title_bg.image:LoadSprite(bundle, asset, function()
    --     self.node_list.img_title_bg.image:SetNativeSize()
	-- end)
	local reward_item_list = {}
	for k,v in pairs(self.data.old_info.reward_item) do
		table.insert(reward_item_list, v)
	end
	local tab = #reward_item_list
	local index = 0
	for i = 1, tab do
		if self.cell_list[i] == nil then
			self.cell_list[i] = ItemCell.New(self.node_list.content)
		end
		self.cell_list[i]:SetActive(true)
		self.cell_list[i]:SetData(reward_item_list[i])
		index = index + 1
	end

	for i = index + 1, #self.cell_list do
		if self.cell_list[i] then
			self.cell_list[i]:SetActive(false)
		end
	end
	-- if task_open_level > role_level then
	-- 	self.node_list["task_process"].text.text = string.format(Language.Guild.GuildWageLock,task_open_level)
	-- 	for i = 1, #self.cell_list do
	-- 		if self.cell_list[i] then
	-- 			self.cell_list[i]:SetGraphicGreyCualityBg(true)
	-- 			self.cell_list[i]:SetEffectRootEnable(false)
	-- 		end
	-- 	end
	-- end

	local bundle, asset = ResPath.GetGuildSystemImage(self.data.old_info.task_type_icon)
	self.node_list.icon.image:LoadSprite(bundle, asset, function()
		self.node_list.icon.image:SetNativeSize()
	end)
end

function GuildWageItemRender:OnClickSliver()
	local item_id = GuildWGData.Instance:GetGuildWageSilverId()
	TipWGCtrl.Instance:OpenItem({item_id = item_id},nil)
end

-- function GuildWageItemRender:OnClickTaskInfoShow()
-- 	local data = nil
-- 	if self.data.old_info.task_info_from and self.data.old_info.task_info_from == 0 then
-- 		data = BiZuoWGData.Instance:GetBiZuoShowTipData(self.data.old_info.task_type)
-- 	elseif self.data.old_info.task_info_from and self.data.old_info.task_info_from == 1 then
-- 		data = BiZuoWGData.Instance:GetAllActivityHallCfgByActType(self.data.old_info.task_type)
-- 	elseif self.data.old_info.task_info_from and self.data.old_info.task_info_from == 2 then
-- 		data = GuildWGData.Instance:GetAllActivityHallCfg(self.data.old_info.task_type)
-- 	end
-- 	if data then
-- 		BiZuoWGCtrl.Instance:TaskInfoViewShow(data)
-- 	end
-- end
