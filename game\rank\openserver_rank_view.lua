OpenServerRankView = OpenServerRankView or BaseClass(SafeBaseView)

local My_Rush_Type = nil
local First_Recharge_Stage_Limit = 3 --充值榜第一档位最低名次

function OpenServerRankView:__init()
	self:SetMaskBg(true,true)
	self.view_layer = UiLayer.Pop
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "layout_second_panel", {sizeDelta = Vector2(786, 540)})
	self:AddViewResource(0, "uis/view/open_server_activity_ui_prefab", "openserver_act_rank")
	self.my_rank_type = nil
end

function OpenServerRankView:ReleaseCallBack()
	if self.list_view then
		self.list_view:DeleteMe()
		self.list_view = nil
	end
end

function OpenServerRankView:LoadCallBack()
	self.list_view = AsyncListView.New(OpenServerRankItem, self.node_list.ph_rankitem_role)
end

function OpenServerRankView:ShowIndexCallBack()
	if self.my_rank_type then
		RankWGCtrl.Instance:SendActRankListReq(self.my_rank_type)
	elseif My_Rush_Type then
		local opengame_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(My_Rush_Type)
		if opengame_cfg then
			self.my_rank_type = opengame_cfg.rank_type
			RankWGCtrl.Instance:SendActRankListReq(opengame_cfg.rank_type)
		end
	end
end

function OpenServerRankView:SetRankTypeday(rush_type, rank_type)
	My_Rush_Type = rush_type
	self.my_rank_type = rank_type
end

function OpenServerRankView:OnFlush(param_t)
	for i,v in pairs(param_t) do
		if i == "all" and v.open_param then
			local rank_type = RankClientType[v.open_param]
			local data = ServerActivityWGData.Instance:GetOpenServerActivityRushConfigByRankType(rank_type)
			if data then
				RankWGCtrl.Instance:SendActRankListReq(rank_type)
				self:SetRankTypeday(data.rush_type, rank_type)
			end
		end
	end
end

function OpenServerRankView:FlushView(rank_type, data, protocol)
	if self.my_rank_type and self.my_rank_type ~= protocol.rank_type then
		return
	end

	if not self:IsLoaded() then
		return
	end

	self:FlushRankList(data)
	self:FlushMayRank(protocol)
	self:FlushRankInfo(protocol)
	-- self:SetRankDesc(data, protocol.self_rank, protocol.self_value)
end

function OpenServerRankView:FlushRankList(data)
	if not self.list_view then
		return
	end
	local data_list = {}
	for i=1,100 do
		data_list[i] = data[i] 
		if data_list[i] then
			data_list[i].real_rank = i
		else
			data_list[i] = {real_rank = i, rank_value = 0} 
		end
	end
	self.list_view:SetDataList(data_list)
end

function OpenServerRankView:FlushMayRank(protocol)
	if protocol.self_rank > 0 then
		local rank_str = ToColorStr(protocol.self_rank, COLOR3B.C2)
		self.node_list.rich_my_rank.text.text = string.format(Language.OpenServer.MyRank, rank_str)
	else
		self.node_list.rich_my_rank.text.text = string.format(Language.OpenServer.MyRank, Language.Rank.NoRank)
	end
	local rush_cfg = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(My_Rush_Type)
	local reward_cfg_list = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(My_Rush_Type)
	if reward_cfg_list and rush_cfg and rush_cfg.rank_condition ~= "" then
		local reward_cfg = reward_cfg_list[#reward_cfg_list]
		local rank_str = ""
		if ServerActivityWGData.Instance:IsSpecialRank(My_Rush_Type) then
			local body_show_type = My_Rush_Type == 2 and MOUNT_LINGCHONG_APPE_TYPE.MOUNT or MOUNT_LINGCHONG_APPE_TYPE.LINGCHONG
			local cfg = NewAppearanceWGData.Instance:GetQiChongBaseUpStarCfg(body_show_type, reward_cfg.reach_value) or {}
			rank_str = string.format(rush_cfg.rank_condition, cfg.grade_num or 0, cfg.star_num or 0)
		else
			rank_str = string.format(rush_cfg.rank_condition, reward_cfg.reach_value)
		end
		
		rank_str = ToColorStr(rank_str, COLOR3B.C2)
		self.node_list.rich_my_value.text.text = string.format(Language.OpenServer.RankCondition, rank_str)
	else
		self.node_list.rich_my_value.text.text = ""
	end
end

function OpenServerRankView:FlushRankInfo(protocol)
	local data = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(My_Rush_Type)
	local value_str = ""
	if data and data.ranking_title and data.ranking_name then
		if Language.Common.GradeText == data.ranking_title then  -- 如果是阶数
			if protocol.self_value <= 0 then 
				value_str = string.format(Language.Rank.Jie, math.floor(protocol.self_value / 10), 0) 
			else
				local _,temp = math.modf(protocol.self_value / 10)
				local value = protocol.self_value % 10
				if value == 0 then
					value = 10 
				end
				if temp > 0 then
					value_str = string.format(Language.Rank.Jie, math.floor(protocol.self_value / 10) + 1, value) 
				else
					value_str = string.format(Language.Rank.Jie, math.floor(protocol.self_value / 10), value) 
				end
			end
		else
			value_str = protocol.self_value
		end
		value_str = ToColorStr(value_str, COLOR3B.C2)
		self.node_list.rich_my_level.text.text = string.format(Language.Rank.My, data.ranking_title, value_str)
		self.node_list.title_view_name.text.text = data.ranking_name
		self.node_list.label_listname.text.text = data.ranking_title
	end
end

--设置排行榜提示
function OpenServerRankView:SetRankDesc(rank_info, my_rank, my_value)
	--根节点显示:充值榜才显示,且榜一不显示
	my_rank = my_rank <= 0 and 9999 or my_rank
	my_value = my_value <= 0 and 0 or my_value
	local is_show = My_Rush_Type == RUSH_TYPE.RUSH_TYPE_RECHARGE and my_rank > First_Recharge_Stage_Limit
	--self.node_list["bg_desc"]:SetActive(is_show)
	if not is_show then
		return
	end

	local min_limit_recharge = 1000 --最低上榜充值数
	local show_next_rank = 40 --最低上榜领取奖励位
	local reward_cfg_list = ServerActivityWGData.Instance:GetOpenServerActivityRewardConfig(My_Rush_Type)
	local reward_cfg = nil
	if not IsEmptyTable(reward_cfg_list) then
		for i, v in ipairs(reward_cfg_list) do
			if my_rank >= v.min_rank and my_rank <= v.max_rank then
				reward_cfg = reward_cfg_list[i - 1]
				break
			end
		end

		if not reward_cfg then
			reward_cfg = reward_cfg_list[#reward_cfg_list]
		end
	end

	if reward_cfg and reward_cfg.max_rank then
		local cur_info = rank_info and rank_info[reward_cfg.max_rank]
		show_next_rank = reward_cfg.max_rank
		if cur_info and cur_info.rank_value and cur_info.rank_value > 0 then--榜上有数据
			min_limit_recharge = cur_info.rank_value + 1
		else
			min_limit_recharge = reward_cfg.reach_value or min_limit_recharge
		end
	end
	-- print_error("FFF===show_next_rank== ", min_limit_recharge, my_value)
	local show_num = min_limit_recharge - my_value
	show_num = show_num < 0 and 1 or show_num
	self.node_list["rank_desc"].text.text = string.format(Language.OpenServer.RechargeRankDesTop, show_num, show_next_rank)
end

-------------------------------------------------------------------------------------------------------------------------
OpenServerRankItem = OpenServerRankItem or BaseClass(BaseRender)

function OpenServerRankItem:OnFlush()
	if not IsEmptyTable(self.data) then 
		local rush_type = ServerActivityWGData.Instance:BiPinGetSelectRushType()
		local data = ServerActivityWGData.Instance:GetOpenServerActivityRushConfig(rush_type)

		if rush_type == RUSH_TYPE.RUSH_TYPE_RECHARGE then
			self.node_list.label_level.text.text = Language.OpenServer.RechargeRankDesBaoMi
		else
			if data and Language.Common.GradeText == data.ranking_title then  -- 如果是阶数
				if self.data.rank_value <= 0 then 
					self.node_list.label_level.text.text = string.format(Language.Rank.Jie,1,0 )
				else
					local _,temp = math.modf(self.data.rank_value/10)
					local value = self.data.rank_value%10
					if value == 0 then value = 10 end
					if  temp > 0 then
						self.node_list.label_level.text.text = string.format(Language.Rank.Jie,math.floor(self.data.rank_value/10) + 1,value ) 
					else

						self.node_list.label_level.text.text = string.format(Language.Rank.Jie,math.floor(self.data.rank_value/10),value) 
					end
				end
			else
				self.node_list.label_level.text.text = self.data.rank_value
			end
		end

		local user_name = self.data.user_name;
		local role_id = GameVoManager.Instance:GetMainRoleVo().role_id
		if (user_name == "" or user_name == nil) then
			user_name = Language.OpenServer.XuWeiYiDai
		end
		
		local rank = self.data.real_rank
		local is_top_3 = rank <= 3
		--[[
		if self.data.user_id == role_id then
			user_name = ToColorStr(user_name, is_top_3 and COLOR3B.L_GREEN or COLOR3B.L_GREEN)
		end
		]]
		self.node_list.label_name.text.text = user_name
		self.node_list.rank_3_name.text.text = user_name
		self.node_list.label_rank.text.text = rank
		self.node_list.rank_3_level.text.text = self.node_list.label_level.text.text

		self.node_list.label_name.text.enabled = not is_top_3
		self.node_list.rank_3_name.text.enabled = is_top_3
		self.node_list.label_level.text.enabled = not is_top_3
		self.node_list.rank_3_level.text.enabled = is_top_3
		self.node_list.label_rank.text.enabled = not is_top_3
		self.node_list.img_rank.image.enabled = is_top_3

		self.node_list.bg:SetActive(is_top_3)
		if is_top_3 then
			self.node_list.img_rank.image:LoadSprite(ResPath.GetCommonIcon("a3_tb_jp" .. rank))
			self.node_list.bg.image:LoadSprite(ResPath.GetNoPackPNG("a3_zqxz_di_" .. rank))
		end

		if self.index%2 == 0 then
			self.node_list.bg_normal.image:LoadSprite(ResPath.GetCommon("a3_kfcb_di_phxx2"))
		else
			self.node_list.bg_normal.image:LoadSprite(ResPath.GetCommon("a3_kfcb_di_phxx3"))
		end
		
	end
end

-------------------------------------------------------OpenServerRankItemRender-------------------------------------------------------
OpenServerRankItemRender = OpenServerRankItemRender or BaseClass(BaseRender)

function OpenServerRankItemRender:LoadCallBack()
	self.head_cell = BaseHeadCell.New(self.node_list.head_cell)
end

function OpenServerRankItemRender:ReleaseCallBack()
	if self.head_cell then
		self.head_cell:DeleteMe()
		self.head_cell = nil
	end
end

function OpenServerRankItemRender:OnFlush()
	if not self.data then
		return
	end

	local user_name = self.data.user_name;
	if (user_name == "" or user_name == nil) then
		user_name = Language.OpenServer.XuWeiYiDai
	end
	self.node_list.role_name.text.text = user_name

	self.node_list.not_head:SetActive(self.data.user_id <= 0)
	self.node_list.head_cell:SetActive(self.data.user_id > 0)

	local head_data =
	{
		role_id = self.data.user_id,
		sex = self.data.sex,
		prof = self.data.prof,
		fashion_photoframe = self.data.fashion_photoframe
	}
	self.head_cell:SetData(head_data)
end