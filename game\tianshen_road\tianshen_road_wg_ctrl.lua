require("game/tianshen_road/tianshen_road_view")
require("game/tianshen_road/tianshen_road_wg_data")
require("game/tianshen_road/tianshen_road_login")
require("game/tianshen_road/Tianshen_road_shenqi")
require("game/tianshen_road/tianshen_road_jianglin")
require("game/tianshen_road/tianshen_road_duobei")
require("game/tianshen_road/tianshen_road_chongbang")
require("game/tianshen_road/tianshen_road_mw")
require("game/tianshen_road/tianshenroad_recharge")
require("game/tianshen_road/tianshenload_item")
require("game/tianshen_road/tianshen_road_reward_tip")
require("game/tianshen_road/tianshen_road_rank")
require("game/tianshen_road/tianshen_road_dalian_view")
require("game/tianshen_road/tianshen_road_tsjl_fb_view")
require("game/tianshen_road/tianshenroad_miaosha_view/tianshenroad_miaosha_view")--天神秒杀
require("game/tianshen_road/tianshen_road_jigsaw_task") -- 拼图任务

TianshenRoadWGCtrl = TianshenRoadWGCtrl or BaseClass(BaseWGCtrl)
function TianshenRoadWGCtrl:__init()
	if TianshenRoadWGCtrl.Instance then
		ErrorLog("[TianshenRoadWGCtrl] Attemp to create a singleton twice !")
	end
	TianshenRoadWGCtrl.Instance = self

	self.tianshen_road_data = TianshenRoadWGData.New()

	self:SetTabIndex()
	
	self.tianshen_road_view = TianshenRoadView.New(GuideModuleName.TianShenRoadPanel)
	self.sq_reward_tips = TianShenRoadRewardTips.New()
	self.cb_rank_view = TianShenRoadCBRankView.New()
	self.ts_dalian_view = TianShenDaLianView.New()
	self.ts_jianglin_fb_view = TianShenRoadTSJLFBView.New()
	self.ts_jigsaw_task = TianshenRoadJigsawTask.New(GuideModuleName.TianshenRoadJigsawTask)

	self.mainui_create_complete = GlobalEventSystem:Bind(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self))

	self:RegisterAllProtocols()
end

function TianshenRoadWGCtrl:__delete()
	TianshenRoadWGCtrl.Instance = nil
	self.ts_dalian_view:DeleteMe()

	if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

	if self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end

	if self.tianshen_road_data ~= nil then
		self.tianshen_road_data:DeleteMe()
		self.tianshen_road_data = nil
	end
	if self.tianshen_road_view ~= nil then
		self.tianshen_road_view:DeleteMe()
		self.tianshen_road_view = nil
	end

	if self.sq_reward_tips ~= nil then
		self.sq_reward_tips:DeleteMe()
		self.sq_reward_tips = nil
	end

	if self.cb_rank_view ~= nil then
		self.cb_rank_view:DeleteMe()
		self.cb_rank_view = nil
	end

	if self.ts_jigsaw_task then 
		self.ts_jigsaw_task:DeleteMe()
		self.ts_jigsaw_task = nil
	end
end

function TianshenRoadWGCtrl:RegisterAllProtocols()
	self:RegisterProtocol(SCNewLoginGiftRet,'SCNewLoginGiftRet')
	self:RegisterProtocol(SCActTSJLInfo,'SCActTSJLInfo')
	self:RegisterProtocol(SCWoYaoShenQi,'SCWoYaoShenQi')
	self:RegisterProtocol(SCMoWangYouLi,'SCMoWangYouLi')

	self:RegisterProtocol(SCShouChongRet,'SCShouChongRet')
	self:RegisterProtocol(SCLeiChongHaoLi,'SCLeiChongHaoLi')

	self:RegisterProtocol(SCTianShenChongBang,'SCTianShenChongBang')
	self:RegisterProtocol(SCTianShenZhanLiRank,'SCTianShenZhanLiRank')
	self:RegisterProtocol(SCDuoBeiJiangLi,'SCDuoBeiJiangLi')
	self:RegisterProtocol(SCRATianShenJiangLinFinishInfo,'OnSCRATianShenJiangLinFinishInfo')

	self:RegisterProtocol(CSRandActTSJLReq)
end

function TianshenRoadWGCtrl:Open(tab_index, param_t)
	tab_index = tab_index or self:GetFirstOpenActivity()
	self.tianshen_road_view:Open(tab_index)
end

function TianshenRoadWGCtrl:MainuiOpenCreateCallBack()
	--self:CheckNeedOpenDaLian()
end

function TianshenRoadWGCtrl:RoleAttrChange()
	--self:CheckNeedOpenDaLian()
end

function TianshenRoadWGCtrl:CheckNeedOpenDaLian()
	local uuid = RoleWGData.Instance:GetUUid()
	local key = string.format("%s%s%s", uuid.temp_low, uuid.temp_high, "tianshen_dalian_flag")
	local flag = PlayerPrefsUtil.GetInt(key)
	if flag == 1 then
		if self.mainui_create_complete then
			GlobalEventSystem:UnBind(self.mainui_create_complete)
			self.mainui_create_complete = nil
		end
		return false
	end

	local act_info = ActivityWGData.Instance:GetActivityStatuByType(ACTIVITY_TYPE.GOD_TIANSHENROAD)
	if not act_info or act_info.status ~= ACTIVITY_STATUS.OPEN then
		return false
	end

	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.GOD_TIANSHENROAD)
	if not act_cfg then
		return false
	end

	local role_level = RoleWGData.Instance:GetAttr("level")
	if role_level < act_cfg.level or role_level > act_cfg.level_max then
		if not self.attr_change then
			self.attr_change = BindTool.Bind(self.RoleAttrChange, self)
			RoleWGData.Instance:NotifyAttrChange(self.attr_change, {"level"})
		end
		return false
	elseif self.attr_change then
		RoleWGData.Instance:UnNotifyAttrChange(self.attr_change)
		self.attr_change = nil
	end

	if self.mainui_create_complete then
		GlobalEventSystem:UnBind(self.mainui_create_complete)
		self.mainui_create_complete = nil
	end

	return true
	-- ViewManager.Instance:OpenByQueue(self.ts_dalian_view)
end

function TianshenRoadWGCtrl:OpenShenQiRewardTips()
	self.sq_reward_tips:Open()
end

function TianshenRoadWGCtrl:FlushShenQiRewardTips()
	if self.sq_reward_tips:IsOpen() then
		self.sq_reward_tips:Flush()
	end
end

function TianshenRoadWGCtrl:OpenTianShenCapRankView()
	self.cb_rank_view:Open()
end

function TianshenRoadWGCtrl:FlushTianShenCapRankView()
	if self.cb_rank_view:IsOpen() then
		self.cb_rank_view:FlushView()
	end
end

function TianshenRoadWGCtrl:OpenTSJLFbView()
	self.ts_jianglin_fb_view:Open()
end

function TianshenRoadWGCtrl:SetTabIndex()
	local TianshenRoad_Tab_Index = {
		tianshenroad_login	= 10,
		tianshenroad_first_recharge = 20,
		tianshenroad_total_recharge = 30,
		tianshenroad_shenqi = 40,
		tianshenroad_chongbang = 50,
		tianshenroad_jianglin = 60,
		tianshenroad_mowang	= 70,	
		-- tianshenroad_duobei	= 80,	
		tianshenroad_xianshi_miaosha = 80,	
	}

	self.tab_index_list = {}
	local tb = ConfigManager.Instance:GetAutoConfig("randactivityconfig_1_auto").tianshen_theme_desc
	table.sort(tb, SortTools.KeyLowerSorter("rank_id"))
	for k,v in ipairs(tb) do
		local tab_index = v.rank_id * 10
		if v.real_id == TianshenRoad_Tab_Index.tianshenroad_login then
			TabIndex.tianshenroad_login = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_login, v)
		elseif v.real_id == TianshenRoad_Tab_Index.tianshenroad_first_recharge then
			TabIndex.tianshenroad_first_recharge = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_first_recharge, v)
		elseif v.real_id == TianshenRoad_Tab_Index.tianshenroad_total_recharge then
			TabIndex.tianshenroad_total_recharge = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_total_recharge, v)
		elseif v.real_id == TianshenRoad_Tab_Index.tianshenroad_shenqi then
			TabIndex.tianshenroad_shenqi = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_shenqi, v)
		elseif v.real_id == TianshenRoad_Tab_Index.tianshenroad_chongbang then
			TabIndex.tianshenroad_chongbang = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_chongbang, v)
		elseif v.real_id == TianshenRoad_Tab_Index.tianshenroad_jianglin then
			TabIndex.tianshenroad_jianglin = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_jianglin, v)
		elseif v.real_id == TianshenRoad_Tab_Index.tianshenroad_mowang then
			TabIndex.tianshenroad_mowang = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_mowang, v)
		elseif v.real_id == TianshenRoad_Tab_Index.tianshenroad_duobei then
			TabIndex.tianshenroad_duobei = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_duobei, v)
		elseif v.real_id == TianshenRoad_Tab_Index.tianshenroad_xianshi_miaosha then
			TabIndex.tianshenroad_xianshi_miaosha = tab_index
			self.tianshen_road_data:SetThemeCfgByTabIndex(TabIndex.tianshenroad_xianshi_miaosha, v)
		end
		table.insert(self.tab_index_list, tab_index)
	end
end

-- 获取排序后第一个开启的活动
function TianshenRoadWGCtrl:GetFirstOpenActivity()
	local first_tab_index = nil
	if self.tab_index_list ~= nil then
		for i,v in ipairs(self.tab_index_list) do
			if self.tianshen_road_data:GetActivityState(v) then
				if self.tianshen_road_data:GetActivityRewardState(v) > 0 then
					return v
				end
				if first_tab_index == nil then
					first_tab_index = v
				end
			end
		end
	end
	return first_tab_index
end

function TianshenRoadWGCtrl:FlushView(...)
	if self.tianshen_road_view and self.tianshen_road_view:IsOpen() then
		self.tianshen_road_view:Flush(...)
	end
end

function TianshenRoadWGCtrl:ShowLoginDayReward(day_index)
	if self.tianshen_road_view ~= nil then
		if self.tianshen_road_view:IsOpen() then
			self.tianshen_road_view:Flush(TabIndex.tianshenroad_login, "login_reward", {day_index})
		end
	end
end

--登录奖励信息
function TianshenRoadWGCtrl:SCNewLoginGiftRet(protocol)
	--print_error("FFFFFF===== 登录奖励信息", protocol.activity_id, protocol)
	if protocol.activity_id ~= nil and protocol.activity_id == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DENGLUYOULI then
		QuanMinBeiZhanWGCtrl.Instance:SCNewLoginGiftRet(protocol)
	elseif protocol.activity_id ~= nil and protocol.activity_id == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DENGLUYOULI then
		ActXianQiJieFengWGCtrl.Instance:SCNewLoginGiftRet(protocol)
	else
		self.tianshen_road_data:SetLoginRewardInfo(protocol)
		if self.tianshen_road_view:IsOpen() then
			self.tianshen_road_view:Flush(TabIndex.tianshenroad_login, "login_view")
		end
	end
end

--天神降临返回
function TianshenRoadWGCtrl:SCActTSJLInfo(protocol)
	self.tianshen_road_data:SetTSJLInfo(protocol)
	if self.tianshen_road_view:IsOpen() then
		self.tianshen_road_view:Flush(TabIndex.tianshenroad_jianglin, "jianglin_view")
	end
end

--我要神器返回
function TianshenRoadWGCtrl:SCWoYaoShenQi(protocol)
	self.tianshen_road_data:SetSQInfo(protocol)
	if self.tianshen_road_view:IsOpen() then
		self.tianshen_road_view:Flush(TabIndex.tianshenroad_shenqi, "shenqi_view")
	end

	if self.sq_reward_tips:IsOpen() and self.sq_reward_tips:IsLoaded() then
		self.sq_reward_tips:FlushView()
	end

	if self.ts_jigsaw_task and self.ts_jigsaw_task:IsOpen() then
		self.ts_jigsaw_task:Flush()
	end
end

--魔王有礼返回
function TianshenRoadWGCtrl:SCMoWangYouLi(protocol)
	self.tianshen_road_data:SetMoWangYouLiInfo(protocol)
	if self.tianshen_road_view:IsOpen() then
		self.tianshen_road_view:Flush(TabIndex.tianshenroad_mowang, "mowang_view")
	end
end

--天神冲榜返回
function TianshenRoadWGCtrl:SCTianShenChongBang(protocol)
	self.tianshen_road_data:SetChongBangInfo(protocol)
	if self.tianshen_road_view:IsOpen() then
		self.tianshen_road_view:Flush(TabIndex.tianshenroad_chongbang, "chongbang_view")
	end
end

--天神排行版返回
function TianshenRoadWGCtrl:SCTianShenZhanLiRank(protocol)
	self.tianshen_road_data:SetChongBangRankInfo(protocol)
	if self.tianshen_road_view:IsOpen() then
		self.tianshen_road_view:Flush(TabIndex.tianshenroad_chongbang, "chongbang_view")
	end
end

--多倍来袭返回
function TianshenRoadWGCtrl:SCDuoBeiJiangLi(protocol)
	local data = protocol.data
	--print_error("FFFF====== 多倍来袭返回", data.activity_id, data)
	if data.activity_id ~= nil and data.activity_id == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_DUOBEILAIXI then
		QuanMinBeiZhanWGCtrl.Instance:SCDuoBeiJiangLi(data)
	elseif data.activity_id ~= nil and data.activity_id == ACTIVITY_TYPE.OPERA_ACT_DUOBEI_YOULI then
		OperationActDuoBeiWGCtrl.Instance:SCDuoBeiJiangLi(data)
	elseif data.activity_id ~= nil and data.activity_id == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_DUOBEILAIXI then
		ActXianQiJieFengWGCtrl.Instance:SCDuoBeiJiangLi(data)
	else
		self.tianshen_road_data:SetDuoBeiInfo(data)
		if self.tianshen_road_view:IsOpen() then
			self.tianshen_road_view:Flush(TabIndex.tianshenroad_login, "login_view")
		end
	end
end

--首充返回
function TianshenRoadWGCtrl:SCShouChongRet(protocol)
	--print_error("FFFF======= 首充返回", protocol.activity_id, protocol)
	if protocol.activity_id ~= nil and protocol.activity_id == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_MEIRISHOUCHONG then
		QuanMinBeiZhanWGCtrl.Instance:SCShouChongRet(protocol)
	elseif protocol.activity_id ~= nil and protocol.activity_id == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_MEIRISHOUCHONG then
		ActXianQiJieFengWGCtrl.Instance:SCShouChongRet(protocol)
	else
		self.tianshen_road_data:SetShouChongInfo(protocol)
		if self.tianshen_road_view:IsOpen() then
			self.tianshen_road_view:Flush(TabIndex.tianshenroad_first_recharge, "first_recharge_view")
			-- self.tianshen_road_view:Flush(TabIndex.tianshenroad_first_recharge + 3, "first_recharge_view")
		end
	end
end

--累计充值返回
function TianshenRoadWGCtrl:SCLeiChongHaoLi(protocol)
	if protocol.activity_id ~= nil and protocol.activity_id == ACTIVITY_TYPE.RAND_ACT_BEIZHAN_LEICHONGHAOLI then
		QuanMinBeiZhanWGCtrl.Instance:SCLeiChongHaoLi(protocol)
	elseif protocol.activity_id ~= nil and protocol.activity_id == ACTIVITY_TYPE.RAND_ACT_XIANQIJIEFENG_LEICHONGHAOLI then
		ActXianQiJieFengWGCtrl.Instance:SCLeiChongHaoLi(protocol)
	else
		self.tianshen_road_data:SetLeiChongInfo(protocol)
		if self.tianshen_road_view:IsOpen() then
			self.tianshen_road_view:Flush(TabIndex.tianshenroad_total_recharge, "total_recharge_view")
		end
	end
end

function TianshenRoadWGCtrl:SendActivityRewardOp(activity_type, opera_type,param1, param2, param3)
 	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActivityOperaReq)
 	protocol.rand_activity_type = activity_type
 	protocol.opera_type = opera_type or 0
 	protocol.param_1 = param1 or 0
 	protocol.param_2 = param2 or 0
 	protocol.param_3 = param3 or 0
 	protocol:EncodeAndSend()
end

---[[ 天神降临
function TianshenRoadWGCtrl:RankActTSJLReq()
 	local protocol = ProtocolPool.Instance:GetProtocol(CSRandActTSJLReq)
 	protocol:EncodeAndSend()
end

-- 天神降临结算
function TianshenRoadWGCtrl:OnSCRATianShenJiangLinFinishInfo(protocol)
	self.tianshen_road_data:SetTSJLFinishInfo(protocol)
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.TIANSHEN_JIANLIN then
		ActivityWGCtrl.Instance:OpenActJiseSuanView(ACTIVITY_TYPE.TIANSHENJIANLIN)
	end
end

-- 最后10s特殊倒计时
function TianshenRoadWGCtrl:TianShenJianLinCountDown()
	local act_info = self.tianshen_road_data:GetTianShenJianLinInfo()
	if act_info and (act_info.activity_state == ACTIVITY_STATUS.STANDY or act_info.activity_state == ACTIVITY_STATUS.OPEN) then
		if ActivityWGCtrl.Instance:CheckNowCountDownActType(ACTIVITY_TYPE.TIANSHENJIANLIN) then
			return
		end

		local scene_id = Scene.Instance:GetSceneId()
		if act_info.activity_state == ACTIVITY_STATUS.STANDY then
			local npc_scene = self.tianshen_road_data:GetJiangLinOtherCfg("npc_scene")
			if scene_id ~= npc_scene then
				return
			end
		elseif act_info.activity_state == ACTIVITY_STATUS.OPEN then
			local boss_id, cfg_data = self.tianshen_road_data:GetBossId()
			if scene_id ~= cfg_data.scene_id then
				return
			end
		else
			return
		end

		local info_list = {}
		info_list.act_type = ACTIVITY_TYPE.TIANSHENJIANLIN
		info_list.calculate_time = 10
		info_list.timestamp = act_info.next_time
		-- info_list.image_name = act_info.activity_state == ACTIVITY_STATUS.STANDY and "tianshenjianglin"
		info_list.is_auto_close = act_info.activity_state == ACTIVITY_STATUS.OPEN
		ActivityWGCtrl.Instance:OpenActCountDown(info_list)
	else
		ActivityWGCtrl.Instance:CloseActCountDown(ACTIVITY_TYPE.TIANSHENJIANLIN)
	end
end

-- boss出生震屏效果
function TianshenRoadWGCtrl:BossBornTween()
	local boss_id, cfg_data = self.tianshen_road_data:GetBossId()
	local scene_id = Scene.Instance:GetSceneId()
	if cfg_data and scene_id == cfg_data.scene_id then
		Scene.Instance:ShakeMainCamera(1, 0.5, 30, 40)
	end
end

-- 活动icon控制
function TianshenRoadWGCtrl:CheckTianShenJianLinActIcon()
	local act_cfg = ActivityWGData.Instance:GetActivityCfgByType(ACTIVITY_TYPE.TIANSHENJIANLIN)
	if not act_cfg then
		return
	end

	local role_level = RoleWGData.Instance:GetRoleLevel()
	if role_level < act_cfg.level or role_level > act_cfg.level_max then
		return
	end

	local act_info = self.tianshen_road_data:GetTianShenJianLinInfo()
	if act_info and (act_info.activity_state == ACTIVITY_STATUS.STANDY or act_info.activity_state == ACTIVITY_STATUS.OPEN) then
		local time_cfg = TianshenRoadWGData.Instance:GetJiangLinFlushTimeCfg()
		local ser_time = TimeWGCtrl.Instance:GetServerTime()
	    local time_tab = os.date("*t", ser_time)
	    local hour = 0
	    local min = 0
	    for i=1,#time_cfg do
	        hour = math.floor(time_cfg[i].refresh_time / 100)
	        min = time_cfg[i].refresh_time - hour * 100
	        if time_tab.hour < hour and time_tab.min < min then
	            break
	        end
	    end
	    local open_time_str = string.format("%d:%02d%s", hour, min, Language.Activity.KaiQi)
		MainuiWGCtrl.Instance:AddTempActIcon(ACTIVITY_TYPE.TIANSHENJIANLIN, act_info.next_time, act_cfg, COLOR3B.GREEN, nil, open_time_str)
	else
		MainuiWGCtrl.Instance:ActivitySetButtonVisible(ACTIVITY_TYPE.TIANSHENJIANLIN, false)
	end
end

-- npc对话修改
function TianshenRoadWGCtrl:CheckTianShenJianLinNpc(npc_id)
	local npcid = self.tianshen_road_data:GetJiangLinOtherCfg("npcid")
	if npcid ~= npc_id then
		return false
	end

	local act_info = self.tianshen_road_data:GetTianShenJianLinInfo()
	if act_info then
		if act_info.activity_state == ACTIVITY_STATUS.STANDY then
			return true, Language.TianShenRoad.NPCTalkStr_1
		elseif act_info.activity_state == ACTIVITY_STATUS.OPEN then
			return true, Language.TianShenRoad.NPCTalkStr_2, BindTool.Bind(self.RankActTSJLReq, self)
		end
	end
end

-- 前往NPC
function TianshenRoadWGCtrl:GotoShiLian()
	local scene_type = Scene.Instance:GetSceneType()
	if scene_type == SceneType.TIANSHEN_JIANLIN then
		return
	end

	local other_cfg = self.tianshen_road_data:GetJiangLinOtherCfg()
	other_cfg = other_cfg and other_cfg[1]
	if not other_cfg then
		return
	end
	
	RoleWGCtrl.Instance:SetJumpAlertCheck(other_cfg.npc_scene, function()
		GuajiWGCtrl.Instance:MoveToNpc(other_cfg.npcid, nil, other_cfg.npc_scene)
	end, true)
end
--]]