TianShenLingHeWGData = TianShenLingHeWGData or BaseClass()
TianShenLingHeWGData.MAX_SLOT = 8               -- 灵核镶嵌槽位数
TianShenLingHeWGData.MAX_BAG = 400              -- 灵核背包容量
TianShenLingHeWGData.MAX_RESLOVE = 50           -- 最多分解数量
TianShenLingHeWGData.BAG_CHANGE_TYPE = {
    ALL_INFO = 0,
    CHANGE_PART = 1,
}

TianShenLingHeWGData.OPERA_TYPE = {
    BAG_INFO = 0,
    SLOT_INFO = 1,
    ACT_CHANGE = 2,             -- param 1:天神索引 2:槽位索引 3：背包位置
    UPLEVEL = 3,                -- param 1:天神索引 2:槽位索引
    COMPOSE = 4,                -- param 1:天神索引 2:槽位索引
    QUICKCOMPOSE = 5,           -- param 1:一键合成
}

function TianShenLingHeWGData:__init()
	if TianShenLingHeWGData.Instance then
		error("[TianShenLingHeWGData] Attempt to create singleton twice!")
		return
	end
	TianShenLingHeWGData.Instance = self

    self.bag_list = {}
    self.bag_total_num = 0
    -- self.cache_slot_item_list = {}
    self.cache_slot_item_color_num = {}
    self.item_id_num = {}
    self.ul_show_list = {}
    self:InItLingHeDrawData() --初始化灵核抽奖data
    local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_runes_auto")
    self.item_map_cfg = cfg.base
    self.default_ul_show_cfg = cfg.default_show
    self.slot_map_cfg = {}
    self.show_attr_item_list = {}               --- 槽位对应的任意展示灵核
    local slot
    for k,v in pairs(cfg.base) do
        slot = v.slot
        if self.slot_map_cfg[slot] == nil then
            self.slot_map_cfg[slot] = {}
        end

        self.slot_map_cfg[slot][k] = v

        if self.show_attr_item_list[slot] == nil then
            self.show_attr_item_list[slot] = v.runes_id
        end
    end

    self.uplevel_map_cfg = ListToMap(cfg.uplevel, "slot", "level")
    self.compose_map_cfg = cfg.compose
    self.compose_map_cost_cfg = ListToMapByDisorder(cfg.compose, "stuff_id")
    self.compose_toggle_map_cfg = {}
    local big_type, small_type
    for k,v in pairs(cfg.compose) do
        big_type = v.big_type
        small_type = v.small_type
        if self.compose_toggle_map_cfg[big_type] == nil then
            self.compose_toggle_map_cfg[big_type] = {}
        end

        self.compose_toggle_map_cfg[big_type][small_type] = v
    end

    RemindManager.Instance:Register(RemindName.TianShenLingHeUpLevel, BindTool.Bind(self.GetAllUpLevelRemind, self))
	--RemindManager.Instance:Register(RemindName.TianShenLingHeReslove, BindTool.Bind(self.GetResloveRemind, self))
	-- RemindManager.Instance:Register(RemindName.TianShenLingHeCompose, BindTool.Bind(self.GetComposeRemind, self))
    RemindManager.Instance:Register(RemindName.TianShenLingHeDraw, BindTool.Bind(self.GetLingHeDrawRed, self))
end

function TianShenLingHeWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.TianShenLingHeUpLevel)
	--RemindManager.Instance:UnRegister(RemindName.TianShenLingHeReslove)
	-- RemindManager.Instance:UnRegister(RemindName.TianShenLingHeCompose)
    RemindManager.Instance:UnRegister(RemindName.TianShenLingHeDraw)

	TianShenLingHeWGData.Instance = nil
end

-------------------------------- 分割 -----------------------------------
-- 配置部分
-------------------------------------------------------------------------
-- 获取槽位升级配置
function TianShenLingHeWGData:GetSlotUplevelCfg(slot, level)
    return (self.uplevel_map_cfg[slot] or {})[level]
end

-- 获取灵核配置 by item_id
function TianShenLingHeWGData:GetLingGHeCfgByItemId(item_id)
    return self.item_map_cfg[item_id]
end

function TianShenLingHeWGData:GetLingHeDefaultShowItem(slot)
    return self.default_ul_show_cfg[slot].show_item_id
end

function TianShenLingHeWGData:GetUpLevelShowAttr(slot, level, item_id)
    local is_preview = item_id == nil or item_id <= 0
    if is_preview then
        item_id = self.show_attr_item_list[slot] or 0
    end

    local attr_list = {}
    local lh_cfg = self:GetLingGHeCfgByItemId(item_id)
    if lh_cfg == nil then
        return attr_list
    end

    local cur_cfg = self:GetSlotUplevelCfg(slot, level)
    local next_cfg = self:GetSlotUplevelCfg(slot, level + 1)
    local linghe_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(lh_cfg)
    local _, uplevel_attr_map_cfg_list = AttributeMgr.GetUsefulAttributteByClass(cur_cfg)

    local base_value = 0
    for k,v in pairs(lh_cfg) do
        local attr_str = linghe_cfg_map_attr_list[k]
        if attr_str and v > 0 then
            base_value = is_preview and 0 or v
            local data = {
                attr_str = attr_str,
                attr_value = base_value,
                attr_next_value = 0,
                add_value = 0,
                attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str),
            }

            local cfg_attr_key = uplevel_attr_map_cfg_list[attr_str]
            if cfg_attr_key then
                if cur_cfg and cur_cfg[cfg_attr_key] then
                    data.attr_value = base_value + cur_cfg[cfg_attr_key]
                end

                if next_cfg and next_cfg[cfg_attr_key] then
                    data.attr_next_value = base_value + next_cfg[cfg_attr_key]
                    data.add_value = data.attr_next_value - data.attr_value
                end
            end

            table.insert(attr_list, data)
        end
    end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

    return attr_list
end

-- 合成显示属性
function TianShenLingHeWGData:GetComposeShowAttr(item_id)
    local attr_list = {}
    local cur_cfg = self:GetLingGHeCfgByItemId(item_id)
    if IsEmptyTable(cur_cfg) then
        return attr_list
    end
    
    local compose_cfg = self:GetItemComposeCfgByStuff(item_id)
    local product_id = compose_cfg and compose_cfg.product_id or 0
    local next_cfg = self:GetLingGHeCfgByItemId(product_id)

    local linghe_cfg_map_attr_list = AttributeMgr.GetUsefulAttributteByClass(cur_cfg)
    local _, compose_attr_map_cfg_list = AttributeMgr.GetUsefulAttributteByClass(next_cfg)

    for k,v in pairs(cur_cfg) do
        local attr_str = linghe_cfg_map_attr_list[k]
        if attr_str and v > 0 then
            local data = {
                attr_str = attr_str,
                attr_value = v,
                attr_next_value = 0,
                add_value = 0,
                attr_sort = AttributeMgr.GetSortAttributeIndex(attr_str),
            }
            local cfg_attr_key = compose_attr_map_cfg_list[attr_str] --配置表里的属性字段名
            if not IsEmptyTable(next_cfg) and next_cfg[cfg_attr_key] then
                data.attr_next_value = next_cfg[cfg_attr_key]
                data.add_value = data.attr_next_value - data.attr_value
            end
            table.insert(attr_list, data)
        end
    end

	if not IsEmptyTable(attr_list) then
		table.sort(attr_list, SortTools.KeyLowerSorter("attr_sort"))
	end

    return attr_list
end

-- 获取合成配置 by item_id
function TianShenLingHeWGData:GetItemComposeCfg(item_id)
    return self.compose_map_cfg[item_id]
end

-- 获取合成配置 by stuff_id
function TianShenLingHeWGData:GetItemComposeCfgByStuff(stuff_id)
    return self.compose_map_cost_cfg[stuff_id]
end

-- 获取合成配置 by 类型
function TianShenLingHeWGData:GetItemComposeCfgByType(big_type, small_type)
    return (self.compose_toggle_map_cfg[big_type] or {})[small_type]
end

-------------------------------- 分割 -----------------------------------
-- 灵核背包部分
-------------------------------------------------------------------------
-- 设置灵核背包
function TianShenLingHeWGData:SetAllBagInfo(protocol)
    if protocol.change_reason == TianShenLingHeWGData.BAG_CHANGE_TYPE.ALL_INFO then
        self.bag_list = protocol.bag_list
        self.bag_total_num = protocol.bag_count
        -- print_error("-----设置全部背包-----", self.bag_total_num, self.bag_list)
        -- self.cache_slot_item_list = {}
        self.cache_slot_item_color_num = {}
        self.item_id_num = {}
        for k,v in pairs(self.bag_list) do
            self:CacheSlotItemList(GameEnum.DATALIST_CHANGE_REASON_ADD, v, v.num)
        end

        self:SetComposeToggleList()
    else
        -- print_error("----背包部分改变----", protocol.bag_count, protocol.bag_list)
        for k,v in pairs(protocol.bag_list) do
            self:SetSingleItemData(v)
        end
    end
end

-- 获取背包列表
function TianShenLingHeWGData:GetAllBagInfo()
    return self.bag_list
end

function TianShenLingHeWGData:GetBagItem(index)
    return self.bag_list[index]
end

-- 改变灵核背包数据
function TianShenLingHeWGData:SetSingleItemInfo(protocol)
    self:SetSingleItemData(protocol.change_data)
end

-- 设置单个物品改变
function TianShenLingHeWGData:SetSingleItemData(new_data)
	local index = new_data.index
	local old_data = self:GetBagItem(index)
	local old_num = old_data and old_data.num or 0
	local new_num = new_data.num
    if new_num > 0 then
        self.bag_list[index] = new_data
    else
        self.bag_list[index] = nil
    end

    -- print_error("-----单个物品改变-----", new_data)
    -- print_error("-----单个物品改变后背包-----", self.bag_list)

	local change_reason = GameEnum.DATALIST_CHANGE_REASON_UPDATE
	if (old_data == nil or old_data.item_id == nil or old_data.item_id <= 0) and new_num > old_num then
		change_reason = GameEnum.DATALIST_CHANGE_REASON_ADD
        self.bag_total_num = self.bag_total_num + 1
        self:CacheSlotItemList(change_reason, new_data, new_num - old_num)
	elseif old_data and old_data.item_id > 0 and new_num <= 0 then
        self.bag_total_num = self.bag_total_num - 1
		change_reason = GameEnum.DATALIST_CHANGE_REASON_REMOVE
        self:CacheSlotItemList(change_reason, new_data, old_num - new_num)
    else
        self:CacheSlotItemList(change_reason, new_data, new_num - old_num)
	end

    if new_num > old_num then
        self:ChangeUpLevelShowList()
    end

    self:SetComposeToggleList()
    TianShenLingHeWGCtrl.Instance:OnLingHeItemDataChange(new_data.item_id, index, change_reason, old_num, new_num)
end

-- 改变缓存数据
function TianShenLingHeWGData:CacheSlotItemList(change_reason, data, change_num)
	if data == nil then
		return
	end

    local item_id = data.item_id
	local slot = data.used_slot
	local color = data.color

    --[[ 获取 槽位 颜色缓存列表
    self.cache_slot_item_list[slot] = self.cache_slot_item_list[slot] or {}
    local color_list = self.cache_slot_item_list[slot][color] or {}
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_ADD then
        table.insert(color_list, data)
	elseif change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
		for i = #color_list, 1, -1 do
            local cahce_data = color_list[i]
            if cahce_data.index == data.index then
                table.remove(color_list, i)
                return
            end
        end
	end
    ]]

    local item_num = self.item_id_num[item_id] or 0
    self.cache_slot_item_color_num[slot] = self.cache_slot_item_color_num[slot] or {}
	local color_num = self.cache_slot_item_color_num[slot][color] or 0
	if change_reason == GameEnum.DATALIST_CHANGE_REASON_REMOVE then
        item_num = item_num - change_num
        item_num = item_num > 0 and item_num or 0
		color_num = color_num - change_num
		color_num = color_num > 0 and color_num or 0
    else
        item_num = item_num + change_num
        color_num = color_num + change_num
	end

    self.item_id_num[item_id] = item_num
	self.cache_slot_item_color_num[slot][color] = color_num
end

-- 获取 缓存
-- function TianShenLingHeWGData:GetCacheSlotItemList(slot, color)
--     return (self.cache_slot_item_list[slot] or {})[color] or {}
-- end

-- 获取 缓存的品质数量
function TianShenLingHeWGData:GetCacheSlotItemColorNum(slot, color)
    return (self.cache_slot_item_color_num[slot] or {})[color] or 0
end

-- 获取 道具数量
function TianShenLingHeWGData:GetItemNum(item_id)
    return self.item_id_num[item_id] or 0
end

-- 获取 获取背包格子已使用数量
function TianShenLingHeWGData:GetBagItemUsedNum()
    return self.bag_total_num
end

-- 获取背包里该槽位的所有物品
function TianShenLingHeWGData:GetSlotBagList(slot)
    local slot_bag_list = {}
    local bag_list = self:GetAllBagInfo()
    for k,v in pairs(bag_list) do
        if v.used_slot == slot and v.color > 0 and v.num > 0 then
            table.insert(slot_bag_list, v)
        end
    end

    if not IsEmptyTable(slot_bag_list) then
        SortTools.SortDesc(slot_bag_list, "color", "index")
    end

    return slot_bag_list
end

-- 获取背包里所有物品中各个槽位最高的物品
function TianShenLingHeWGData:GetBestBagList()
    local best_bag_list = {}
    for i = 0, TianShenLingHeWGData.MAX_SLOT - 1 do
        local slot_bag_list = self:GetSlotBagList(i)
        if not IsEmptyTable(slot_bag_list) then
            best_bag_list[i] = slot_bag_list[1]
        end
    end
    
    return best_bag_list
end

-- 获取 分解排序列表
function TianShenLingHeWGData:GetResloveBagList(color_select_list)
    self.reslove_list = {}
    local bag_list = self:GetAllBagInfo()
    for k,v in pairs(bag_list) do
        if v.can_reslove and v.color > 0 and v.num > 0 then
            local data = {
                item_id = v.item_id,
                index = v.index,
                is_bind = v.is_bind,
                color = v.color,
                sort = ((color_select_list and color_select_list[v.color]) and 10000 or 0) + v.color * 1000 + v.index,
            }
            table.insert(self.reslove_list, data)
        end
    end

    if not IsEmptyTable(self.reslove_list) then
        SortTools.SortDesc(self.reslove_list, "sort")
    end

    return self.reslove_list
end

-- 设置合成展示列表
function TianShenLingHeWGData:SetComposeToggleList()
    self.cp_toggle_list = {}
    local cfg = ConfigManager.Instance:GetAutoConfig("tianshen_runes_auto").compose_show
    local big_type, small_type = 0, 0
    for k,v in ipairs(cfg) do
        big_type = v.big_type
        small_type = v.small_type
        if small_type == 0 then
            self.cp_toggle_list[big_type] = {name = v.name, type = big_type, child_list = {}, is_show = false, is_remind = false}
        else
            if self.cp_toggle_list[big_type] then
                local cfg_data = self:GetItemComposeCfgByType(big_type, small_type)
                if cfg_data then
                    self.cp_toggle_list[big_type].is_show = true
                    local is_remind = self:GetItemComposeRemind(cfg_data.product_id)
                    if is_remind then
                        self.cp_toggle_list[big_type].is_remind = true
                    end

                    local data = {name = v.name, type = small_type, is_remind = is_remind}
                    table.insert(self.cp_toggle_list[big_type].child_list, data)
                end
            end
        end
    end


    for i = #self.cp_toggle_list, 1, -1 do
        if not self.cp_toggle_list[i].is_show then
            self.cp_toggle_list[i] = nil
        end
    end
end

function TianShenLingHeWGData:GetComposeToggleList()
    return self.cp_toggle_list
end

-------------------------------- 分割 -----------------------------------
-- 灵核槽位部分
-------------------------------------------------------------------------
-- 设置灵核全部槽位
function TianShenLingHeWGData:SetAllSlotInfo(protocol)
    self.slot_list = protocol.slot_list
    -- print_error("----设置全部槽位----", self.slot_list)
    self:ChangeUpLevelShowList()
end

-- 设置灵核单个槽位
function TianShenLingHeWGData:SetSingleSlotInfo(protocol)
    local ts_index = protocol.ts_index
    local slot = protocol.slot
    -- print_error("----单个槽位改变----", ts_index, slot, protocol.change_data)
    if self.slot_list[ts_index] and self.slot_list[ts_index][slot] then
        self.slot_list[ts_index][slot] = protocol.change_data
    end
    self:ChangeUpLevelShowList()
end

-- 获取槽位列表数据
function TianShenLingHeWGData:GetSlotListData(ts_index)
    return self.slot_list[ts_index] or {}
end

-- 获取槽位数据
function TianShenLingHeWGData:GetSlotData(ts_index, slot)
    return (self.slot_list[ts_index] or {})[slot]
end

-- 获取槽位镶嵌颜色列表
function TianShenLingHeWGData:GetSlotColorListData(ts_index)
    local color_list = {}
    local list = self:GetSlotListData(ts_index)
    if IsEmptyTable(list) then
        return color_list
    end

    for i = 0, #list do
        local data = list[i]
        if data.color > 0 then
            table.insert(color_list, data.color)
        end
    end

    return color_list
end

-- 更新天神，全部槽位展示
function TianShenLingHeWGData:ChangeUpLevelShowList()
    self.ul_show_list = {}
    local ts_data = TianShenWGData.Instance
    local magic_cfg = ts_data:GetTianShenImageCfg()
    for i = 0, magic_cfg[#magic_cfg].index do
        local cfg_data = magic_cfg[i]
        if cfg_data then
            local ts_index = cfg_data.index
            local is_act = ts_data:IsActivation(ts_index)
            if is_act then
                local act_cfg = ts_data:GetTianshenItemCfgByIndex(ts_index)
                local slot_list, is_remind = self:GetULSlotList(ts_index)
                local data = {
                    index = ts_index,
                    ts_index = ts_index,
                    series = cfg_data.series,
                    bianshen_name = cfg_data.bianshen_name,
                    active_status = is_act and 1 or 0,
                    act_item_id = act_cfg.act_item_id,
                    slot_list = slot_list,
                    is_remind = is_remind }
                table.insert(self.ul_show_list, data)
            end
        end
    end
end

-- 获取 全部槽位展示
function TianShenLingHeWGData:GetUpLevelShowList()
    return self.ul_show_list
end

-- 获取 某天神槽位数据
function TianShenLingHeWGData:GetULSlotList(ts_index)
    local slot_list = {}
    local ts_remind = false
    for i = 0, TianShenLingHeWGData.MAX_SLOT - 1 do
        local data = {slot = i, level = 0, item_id = 0, color = 0, is_remind = false}
        local slot_info = self:GetSlotData(ts_index, i)
        if slot_info then
            data.level = slot_info.level
            data.item_id = slot_info.item_id
            data.color = slot_info.color
            data.is_remind = self:GetSlotRemind(ts_index, i)
            if data.is_remind then
                ts_remind = true
            end
        end

        slot_list[i] = data
    end

    return slot_list, ts_remind
end

-- 获取天神灵核战力
function TianShenLingHeWGData:GetULCapability(ts_index)
    local list = self:GetSlotListData(ts_index)
    if IsEmptyTable(list) then
        return 0
    end

    local linghe_attr_key = AttributeMgr.GetUsefulAttributteByClass(self:GetLingGHeCfgByItemId(31000))
    local uplevel_attr_key = AttributeMgr.GetUsefulAttributteByClass(self:GetSlotUplevelCfg(0, 1))
    if IsEmptyTable(linghe_attr_key) or IsEmptyTable(uplevel_attr_key) then
        return 0
    end

    local attribute = AttributePool.AllocAttribute()
    for k,v in pairs(list) do
        if v.item_id > 0 then
            local lh_cfg = self:GetLingGHeCfgByItemId(v.item_id)
            if lh_cfg then
                for cfg_key, attr_str in pairs(linghe_attr_key) do
                    attribute[attr_str] = attribute[attr_str] + lh_cfg[cfg_key]
                end
            end

            local ul_cfg = self:GetSlotUplevelCfg(v.used_slot, v.level)
            if ul_cfg then
                for cfg_key, attr_str in pairs(uplevel_attr_key) do
                    attribute[attr_str] = attribute[attr_str] + ul_cfg[cfg_key]
                end
            end
        end
    end

    return AttributeMgr.GetCapability(attribute)
end

-- 获取单个神饰评分(战力)
function TianShenLingHeWGData:GetShenShiScore(ts_index, slot)
    local slot_data = self:GetSlotData(ts_index, slot)
    if IsEmptyTable(slot_data) then
        return 0
    end

    local linghe_attr_key = AttributeMgr.GetUsefulAttributteByClass(self:GetLingGHeCfgByItemId(31000))
    local uplevel_attr_key = AttributeMgr.GetUsefulAttributteByClass(self:GetSlotUplevelCfg(0, 1))
    if IsEmptyTable(linghe_attr_key) or IsEmptyTable(uplevel_attr_key) then
        return 0
    end

    local attribute = AttributePool.AllocAttribute()
    if slot_data.item_id > 0 then
        local lh_cfg = self:GetLingGHeCfgByItemId(slot_data.item_id)
        if lh_cfg then
            for cfg_key, attr_str in pairs(linghe_attr_key) do
                attribute[attr_str] = attribute[attr_str] + lh_cfg[cfg_key]
            end
        end

        local ul_cfg = self:GetSlotUplevelCfg(slot_data.used_slot, slot_data.level)
        if ul_cfg then
            for cfg_key, attr_str in pairs(uplevel_attr_key) do
                attribute[attr_str] = attribute[attr_str] + ul_cfg[cfg_key]
            end
        end
    end

    return AttributeMgr.GetCapability(attribute)
end

-- 获取该装备所有可合成的材料(包括下级)
function TianShenLingHeWGData:GetItemComposeCostList(item_id)
    local cose_item_list = {}

    local function get_compose_items(target_item_id, require_num)
        local item_compose_cfg = self.compose_map_cfg[target_item_id]
        if not item_compose_cfg then
            return false
        end

        local need_stuff_count = require_num * item_compose_cfg.stuff_count
        local have_num = self:GetItemNum(item_compose_cfg.stuff_id)
        if target_item_id == item_id then --这里默认加上身上穿的一件，特殊处理
            have_num = have_num + 1
        end

        if have_num >= need_stuff_count then
            table.insert(cose_item_list, {item_id = item_compose_cfg.stuff_id, num = need_stuff_count})
            return true
        else
            table.insert(cose_item_list, {item_id = item_compose_cfg.stuff_id, num = have_num})
            return get_compose_items(item_compose_cfg.stuff_id, need_stuff_count - have_num)
        end
    end

    local is_enough = get_compose_items(item_id, 1)
    return is_enough, cose_item_list
end

-------------------------------- 分割 -----------------------------------
-- 红点部分
-------------------------------------------------------------------------
-- 有更好的灵核
function TianShenLingHeWGData:GetHaveBetterLingHe(ts_index, slot)
    local slot_info = self:GetSlotData(ts_index, slot)
    if slot_info == nil then
        return false
    end

    -- 有更好的灵核
    local color = 0
    local item_cfg = ItemWGData.Instance:GetItemConfig(slot_info.item_id)
    if item_cfg then
        color = item_cfg.color
    end
    
    for i = color + 1, GameEnum.ITEM_COLOR_COLOR_FUL do
        if self:GetCacheSlotItemColorNum(slot, i) > 0 then
            return true
        end
    end

    return false
end

-- 槽位升级红点
function TianShenLingHeWGData:GetUpLevelRemind(ts_index, slot)
    local slot_info = self:GetSlotData(ts_index, slot)
    if slot_info == nil or slot_info.item_id <= 0 then
        return false
    end
    
    local ul_cfg = self:GetSlotUplevelCfg(slot, slot_info.level + 1)
    if ul_cfg == nil then
        return false
    end

    local had_num = self:GetItemNum(ul_cfg.stuff_id)
    return had_num >= ul_cfg.stuff_num
end

-- 槽位装备可合成红点
function TianShenLingHeWGData:GetSlotComposeRemind(ts_index, slot)
    local slot_info = self:GetSlotData(ts_index, slot)
    if slot_info == nil or slot_info.item_id <= 0 then
        return false
    end

    local compose_target_id = (self.compose_map_cost_cfg[slot_info.item_id] or {}).product_id
    if not compose_target_id then
        return false
    end

    local is_enough = self:GetItemComposeCostList(compose_target_id)
    return is_enough
end

-- 灵核槽位红点
function TianShenLingHeWGData:GetSlotRemind(ts_index, slot)
    -- 更好灵核
    if self:GetHaveBetterLingHe(ts_index, slot) then
        return true
    end

    -- 升级满足
    if self:GetUpLevelRemind(ts_index, slot) then
        return true
    end

    -- 合成满足
    if self:GetSlotComposeRemind(ts_index, slot) then
        return true
    end

    return false
end

-- 神饰红点
function TianShenLingHeWGData:GetAllUpLevelRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.TianShenView) then
        return 0
    end

    if self:GetResloveRemind() == 1 then
        return 1
    end

    for i, data in pairs(self:GetUpLevelShowList()) do
        if data.is_remind then
            MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LINGHE_UPLEVEL, 1, function ()
                ViewManager.Instance:Open(GuideModuleName.TianShenView, TabIndex.tianshen_linghe_uplevel)
                return true
            end)
            return 1
        end
    end
    MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.LINGHE_UPLEVEL, 0)
    return 0
end

-- 分解红点
function TianShenLingHeWGData:GetResloveRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.TianShenView) then
        return 0
    end

    -- 背包满了提醒分解
    local used_num = self:GetBagItemUsedNum()
    return used_num >= TianShenLingHeWGData.MAX_BAG and 1 or 0
end

-- 单个物品合成红点(只提醒灵核部分)
function TianShenLingHeWGData:GetItemComposeRemind(item_id)
    local compose_cfg = self:GetItemComposeCfg(item_id)
    if compose_cfg then
    local had_num = self:GetItemNum(compose_cfg.stuff_id)
        return had_num >= compose_cfg.stuff_count
    end

    return false
end

-- 融合红点(弃用)
function TianShenLingHeWGData:GetComposeRemind()
    if not FunOpen.Instance:GetFunIsOpened(FunName.TianShenView) then
        return 0
    end
    
    local toggle_list = self:GetComposeToggleList()
    for k,v in pairs(toggle_list) do
        if v.is_remind then
            return 1
        end
    end

    return 0
end