--塔防副本 左上角按钮
DefenseFbSceneBtnView = DefenseFbSceneBtnView or BaseClass(SafeBaseView)

function DefenseFbSceneBtnView:__init()
	self:AddViewResource(0, "uis/view/fubenpanel_prefab", "layout_defense_btn_view")
	
end

function DefenseFbSceneBtnView:__delete()
end

function DefenseFbSceneBtnView:LoadCallBack()
	local other_cfg = FuBenWGCtrl.Instance.defense_fb_data:GetDefenseTowerOtherCfg()
	local boss_str = string.format(Language.DefenseFb.DefenseExtraBoss, other_cfg.extra_call_gold or 0, other_cfg.extra_call_gold or 0)
	self.defense_boss_alert = Alert.New(boss_str, function()
		FuBenWGCtrl.Instance:SendBuildTowerReq(BUILD_TOWER_OPERA_TYPE.BUILD_TOWER_OPERA_TYPE_CALL)
		FuBenWGCtrl.Instance.defense_fb_data:SetIsAutoCallBoss(self.defense_boss_alert:GetIsNolongerTips())
		self.defense_boss_alert:ClearCheckHook()
		local is_mark = self.defense_boss_alert:GetClickCheckBoxIsShow()
		FuBenWGCtrl.Instance:SetTaFangZhaoHuanMark(is_mark)
	end, nil)
	self.defense_boss_alert:SetShowCheckBox(true)
	local var_mark_show = FuBenWGCtrl.Instance:GetTaFangZhaoHuanMark()
	self.defense_boss_alert:SetCheckBoxDefaultSelect(var_mark_show)
	self.defense_boss_alert:SetCheckBoxText(Language.DefenseFb.AutoDefenseBoss)
	self.alert_window = Alert.New(Language.FuBenPanel.DefenseStartTip)
	self.alert_window:SetLableString(Language.FuBenPanel.DefenseStartTip)
	self.alert_window:SetOkFunc(BindTool.Bind1(self.HandleStart, self))
	self.node_list.boss_defense_tip.text.text = Language.DefenseFb.DefenseBossTip
	self.layout_item_show = self.node_list.attr_list

	self.show_num_lbl_list = {}
	for k = 1, 6 do
		self.show_num_lbl_list[k] = self.layout_item_show:FindObj('lbl_' .. k)
		self.show_num_lbl_list[k]:SetActive(false)
	end
	self.layout_item_show:SetActive(false)

	self.node_list["layout_defense_btn_start"].button:AddClickListener(BindTool.Bind(self.OnClickDefenseStart, self))
	self.node_list["layout_defense_btn_boss"].button:AddClickListener(BindTool.Bind(self.OnClickDefenseBoss, self))
	self.node_list["layout_defense_btn_tips"].button:AddClickListener(BindTool.Bind(self.OnClickHelp, self))

	self.event_listener = self.node_list["layout_defense_btn_award"].event_trigger_listener
	if self.event_listener then 
		self.event_listener:AddPointerDownListener(function ()
			self.layout_item_show:SetActive(true)
		end)
		self.event_listener:AddPointerUpListener(function ()
			self.layout_item_show:SetActive(false)
		end)
	end

	-- self.node_list["layout_defense_btn_award"].button:AddClickListener(BindTool.Bind(self.OnClickDefenseAward, self))
	GlobalEventSystem:Bind(MainUIEventType.MAIN_TOP_ARROW_CLICK, BindTool.Bind1(self.TopPanelAni, self))
	self.is_load_complete = true

	if self.is_should_call == true then
		self.is_should_call = false
		self:ShowIdxCall()
	end

	if self.is_should_showbuild == true then
		self:ShowBuildTip(self.should_build_value)
		self.is_should_showbuild = false
	end

	local mianui_ctrl = MainuiWGCtrl.Instance
	mianui_ctrl:AddInitCallBack(nil, function ( )
		mianui_ctrl:AddBtnToFbIconGroup2Line(self.node_list.layout_defense_btn_award)
		mianui_ctrl:AddBtnToFbIconGroup2Line(self.node_list.layout_defense_btn_boss)
		mianui_ctrl:AddBtnToFbIconGroup2Line(self.node_list.layout_defense_btn_start)
	end)
end

function DefenseFbSceneBtnView:Close()
	FuBenWGCtrl.Instance:SetTaFangZhaoHuanMark(false)
	SafeBaseView.Close(self)
	self.init_btn = false
	self.node_list.layout_defense_btn_start.transform:SetParent(self.root_node_transform,false)
	self.node_list.layout_defense_btn_boss.transform:SetParent(self.root_node_transform,false)
	self.node_list.layout_defense_btn_award.transform:SetParent(self.root_node_transform,false)		
end

function DefenseFbSceneBtnView:GetTowerBtnNode()
	if self.node_list.layout_defense_btn_award then
		return self.node_list.layout_defense_btn_award
	end
end

function DefenseFbSceneBtnView:TopPanelAni( ison )
	--local move_dis = ison and -1 or 1
	local max_move ,min_move = 161 , -91.6

	--self.node_list.root_layout_inspire:SetActive(not is_on)
	local move_vaule = ison == true and max_move or min_move
	if nil == self.node_list.move_pos then return end
	local tween = self.node_list.move_pos.rect:DOAnchorPosY(move_vaule, 1)
	tween:SetEase(DG.Tweening.Ease.OutBack)
	--tween:SetDelay(delay_time)
	tween:OnUpdate(function()
		self.node_list.move_pos.canvas_group.alpha = ((max_move - min_move) - (self.node_list.move_pos.rect.anchoredPosition.y - min_move)) / (max_move - min_move)
	end)
end

function DefenseFbSceneBtnView:OnClickHelp()
	local fb_cfg = Scene.Instance:GetCurFbSceneCfg()
	RuleTip.Instance:SetContent(fb_cfg.fb_desc, Language.Dungeon.HowToPlay)
end

function DefenseFbSceneBtnView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown("defense_fb_time_tip_start") then
		CountDownManager.Instance:RemoveCountDown("defense_fb_time_tip_start")
	end
	if self.defense_boss_alert then
		self.defense_boss_alert:DeleteMe()
		self.defense_boss_alert = nil
	end
	self.layout_item_show = nil
	self.show_num_lbl_list = nil
	self.event_listener = nil
	self.is_load_complete = nil
	self.is_should_showbuild = nil
	self.is_should_call = nil
	if self.alert_window then
		self.alert_window:DeleteMe()
		self.alert_window = nil
	end
end

function DefenseFbSceneBtnView:ShowIdxCall()
	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	local server_time = TimeWGCtrl.Instance:GetServerTime()
	local interval_time = defense_data.next_wave_timestamp - server_time
	if defense_data.cur_wave == -1 then
		-- self.node_list.img_defense_btn_start:SetActive(true)
		--self.node_list.effect:SetActive(false)
		self.node_list.layout_defense_btn_start:SetActive(true)
	else
		self.node_list["img_defense_btn_start"]:SetActive(false)
		--self.node_list.effect:SetActive(true)
		self.node_list.layout_defense_btn_start:SetActive(false)	
	end
end

function DefenseFbSceneBtnView:ShowIndexCallBack()
	if not self.is_load_complete then
		self.is_should_call = true
		return 
	end
	self:ShowIdxCall()
end

function DefenseFbSceneBtnView:OnFlush()
	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	local show_num_list = {}
	local btn_num = 0
	for k,v in ipairs(defense_data.linghun_num_list)do
		if v > 0 then
			show_num_list[#show_num_list + 1] = {key = k - 2, num = v} --客户端是从1开始读 颜色从0开始 减一 key 又减1
			btn_num = btn_num + v
		end
	end

	if defense_data.reward_xianhunshi_num > 0 then
		show_num_list[#show_num_list + 1] = {key = 100, num = defense_data.reward_xianhunshi_num}
		btn_num = btn_num + defense_data.reward_xianhunshi_num
	end

	if defense_data.reward_hun_suipian_num > 0 then
		show_num_list[#show_num_list + 1] = {key = 101, num = defense_data.reward_hun_suipian_num}
		btn_num = btn_num + defense_data.reward_hun_suipian_num
	end

	for k,v in ipairs(self.show_num_lbl_list)do
		if show_num_list[k] ~= nil then
			local num_str = ""
			local color = COLOR3B.WHITE
			if show_num_list[k].key == 100 then
				num_str = string.format(Language.DefenseFb.DefenseFbBtnStone, show_num_list[k].num)
				-- color = COLOR3B.RED
			elseif show_num_list[k].key == 101 then
				num_str = string.format(Language.DefenseFb.DefenseFbBtnSuipian, show_num_list[k].num)
			else
				num_str = string.format(Language.DefenseFb.DefenseFbBtnSoul, Language.Common.ItemQualityColor[show_num_list[k].key], show_num_list[k].num)
				color = ITEM_COLOR[show_num_list[k].key]
			end
			v.text.text = ToColorStr(num_str,color)
			v:SetActive(true)
		else
			v:SetActive(false)
		end
	end
	self.node_list.defense_btn_number.text.text =btn_num



	if CountDownManager.Instance:HasCountDown("defense_fb_time_tip_start") then
		CountDownManager.Instance:RemoveCountDown("defense_fb_time_tip_start")
	end
	local defense_data = FuBenWGCtrl.Instance.defense_fb_data:GetBuildTowerFBSceneLogicInfo()
	local interval_time = defense_data.next_wave_timestamp - TimeWGCtrl.Instance:GetServerTime()
--	print_error(interval_time,defense_data.can_call_extra_monster)
	if self.node_list.layout_defense_btn_start then
		self.node_list.yizhaohuan:SetActive(false)
		self.node_list.effect:SetActive(true)
		local vas = self.node_list.layout_defense_btn_start:GetActive()
		if not vas then
			self.node_list.yizhaohuan:SetActive(defense_data.can_call_extra_monster == 0)
			self.node_list.effect:SetActive(defense_data.can_call_extra_monster ~= 0)
		
		end
	end
	if interval_time > 0 then
		CountDownManager.Instance:AddCountDown("defense_fb_time_tip_start", function(elapse_time, total_time)
			--if total_time - elapse_time >= 1 then
			if self.node_list.shuaguai then
				self.node_list.shuaguai.text.text = math.floor(total_time - elapse_time)
			end
				
			-- elseif total_time - elapse_time < 1 and  total_time - elapse_time >= 0 then
			-- 	self:DoTweenScaleContentFightStart()
			-- end
		end,BindTool.Bind1(self.StarSecondComplete, self), defense_data.next_wave_timestamp, nil, 1)
	end
	local var_mark_show = FuBenWGCtrl.Instance:GetTaFangZhaoHuanMark()
	--self.node_list.effect:SetActive(not var_mark_show)
	-- self.img_bg1:setContentWH(150, #show_num_list * 30 + 50)
end
function DefenseFbSceneBtnView:StarSecondComplete()
	self.node_list.shuaguai.text.text = "00"
end

function DefenseFbSceneBtnView:OnClickDefenseStart()

	--local flag = FuBenWGCtrl.Instance:GetIsNoTowerBuild()
	local is_show_alert = FuBenWGCtrl.Instance:GetTowerNotBuildNum()
	if is_show_alert then
		-- self.alert_window:SetLableString(string.format(Language.FuBenPanel.DefenseStartTipNum, num))
		self.alert_window:SetLableString(Language.FuBenPanel.DefenseStartTip1)
		self.alert_window:Open()
	else
		self:HandleStart()
	end
	
end

function DefenseFbSceneBtnView:HandleStart( )
	FuBenWGCtrl.Instance:ShowBuildTowerTips(false)
	self.node_list["img_defense_btn_start"]:SetActive(false)
	--self.node_list.effect:SetActive(true)
	self.node_list.layout_defense_btn_start:SetActive(false)
	FuBenWGCtrl.Instance:SendBuildTowerReq(BUILD_TOWER_OPERA_TYPE.BUILD_TOWER_OPERA_TYPE_FLUSH)
end

function DefenseFbSceneBtnView:OnClickDefenseBoss()
	self.node_list["img_boss_btn_start"]:SetActive(false)

	self.defense_boss_alert:Open()
	local var_mark_show = FuBenWGCtrl.Instance:GetTaFangZhaoHuanMark()
	self.defense_boss_alert:SetCheckBoxDefaultSelect(var_mark_show)
end

function DefenseFbSceneBtnView:ShowBuildTip(is_active)
	if not self.is_load_complete then
		self.is_should_showbuild = true
		self.should_build_value = is_active
		return 
	end
	if self.node_list["Build_Tip"] then
		self.node_list["Build_Tip"]:SetActive(is_active)
	end
end

function DefenseFbSceneBtnView:OnClickDefenseAward(sender, event_type, touch)
	if event_type == XuiTouchEventType.Began then
		self.layout_item_show:SetActive(true)
	elseif event_type == XuiTouchEventType.Ended or event_type == XuiTouchEventType.Canceled then
		self.layout_item_show:SetActive(false)
	end
end

function DefenseFbSceneBtnView:OnChangeFbSceneBtnView(state)

end

function DefenseFbSceneBtnView:ShowBossBtnAction(state)
	if not self.node_list.img_boss then
		return
	end
	if state then
		self:ShowBossBtnStartImg()
	end
end

function DefenseFbSceneBtnView:ShowBossBtnStartImg()
	self.node_list.img_defense_btn_start:SetActive(false)
	--self.node_list.effect:SetActive(true)
	self.node_list.layout_defense_btn_start:SetActive(false)
end

