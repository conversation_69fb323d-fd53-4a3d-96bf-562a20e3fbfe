-- Y-运营活动-每日累充.xls
local item_table={
[1]={item_id=26437,num=1,is_bind=1},
[2]={item_id=26191,num=1,is_bind=1},
[3]={item_id=48071,num=5,is_bind=1},
[4]={item_id=26367,num=5,is_bind=1},
[5]={item_id=26368,num=5,is_bind=1},
[6]={item_id=28719,num=3,is_bind=1},
[7]={item_id=26444,num=1,is_bind=1},
[8]={item_id=48071,num=10,is_bind=1},
[9]={item_id=26367,num=10,is_bind=1},
[10]={item_id=26368,num=10,is_bind=1},
[11]={item_id=48078,num=4,is_bind=1},
[12]={item_id=26450,num=1,is_bind=1},
[13]={item_id=26193,num=1,is_bind=1},
[14]={item_id=48071,num=15,is_bind=1},
[15]={item_id=26367,num=15,is_bind=1},
[16]={item_id=26368,num=15,is_bind=1},
[17]={item_id=26455,num=1,is_bind=1},
[18]={item_id=48071,num=20,is_bind=1},
[19]={item_id=26367,num=20,is_bind=1},
[20]={item_id=26369,num=2,is_bind=1},
[21]={item_id=26459,num=1,is_bind=1},
[22]={item_id=45017,num=2,is_bind=1},
[23]={item_id=26367,num=25,is_bind=1},
[24]={item_id=26369,num=5,is_bind=1},
[25]={item_id=48079,num=4,is_bind=1},
[26]={item_id=26462,num=1,is_bind=1},
[27]={item_id=45017,num=4,is_bind=1},
[28]={item_id=26367,num=30,is_bind=1},
[29]={item_id=26369,num=8,is_bind=1},
[30]={item_id=37028,num=1,is_bind=1},
[31]={item_id=45017,num=6,is_bind=1},
[32]={item_id=26367,num=50,is_bind=1},
[33]={item_id=26369,num=10,is_bind=1},
[34]={item_id=48080,num=5,is_bind=1},
[35]={item_id=48071,num=2,is_bind=1},
[36]={item_id=26345,num=2,is_bind=1},
[37]={item_id=26368,num=2,is_bind=1},
[38]={item_id=28719,num=1,is_bind=1},
[39]={item_id=26345,num=5,is_bind=1},
[40]={item_id=26345,num=10,is_bind=1},
[41]={item_id=26345,num=15,is_bind=1},
[42]={item_id=26345,num=20,is_bind=1},
[43]={item_id=26345,num=25,is_bind=1},
[44]={item_id=26345,num=30,is_bind=1},
[45]={item_id=38128,num=1,is_bind=1},
[46]={item_id=26345,num=35,is_bind=1},
[47]={item_id=26367,num=2,is_bind=1},
}

return {
open_day={
{},
{start_day=8,end_day=9999,grade=2,display_model=38128,}
},

open_day_meta_table_map={
},
reward={
{},
{seq=1,need_chongzhi=3960,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[4],[4]=item_table[5],[5]=item_table[6]},},
{seq=2,need_chongzhi=6560,reward_item={[0]=item_table[7],[1]=item_table[2],[2]=item_table[8],[3]=item_table[9],[4]=item_table[10],[5]=item_table[11]},},
{seq=3,need_chongzhi=12960,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[15],[4]=item_table[16],[5]=item_table[11]},},
{seq=4,need_chongzhi=20000,reward_item={[0]=item_table[17],[1]=item_table[13],[2]=item_table[18],[3]=item_table[19],[4]=item_table[20],[5]=item_table[11]},},
{seq=5,need_chongzhi=60000,reward_item={[0]=item_table[21],[1]=item_table[13],[2]=item_table[22],[3]=item_table[23],[4]=item_table[24],[5]=item_table[25]},},
{seq=6,need_chongzhi=120000,reward_item={[0]=item_table[26],[1]=item_table[13],[2]=item_table[27],[3]=item_table[28],[4]=item_table[29],[5]=item_table[25]},},
{seq=7,need_chongzhi=200000,reward_item={[0]=item_table[30],[1]=item_table[13],[2]=item_table[31],[3]=item_table[32],[4]=item_table[33],[5]=item_table[34]},},
{grade=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[35],[3]=item_table[36],[4]=item_table[37],[5]=item_table[38]},},
{grade=2,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[3],[3]=item_table[39],[4]=item_table[5],[5]=item_table[6]},},
{grade=2,reward_item={[0]=item_table[7],[1]=item_table[2],[2]=item_table[8],[3]=item_table[40],[4]=item_table[10],[5]=item_table[11]},},
{grade=2,reward_item={[0]=item_table[12],[1]=item_table[13],[2]=item_table[14],[3]=item_table[41],[4]=item_table[16],[5]=item_table[11]},},
{grade=2,reward_item={[0]=item_table[17],[1]=item_table[13],[2]=item_table[18],[3]=item_table[42],[4]=item_table[20],[5]=item_table[11]},},
{grade=2,reward_item={[0]=item_table[21],[1]=item_table[13],[2]=item_table[22],[3]=item_table[43],[4]=item_table[24],[5]=item_table[25]},},
{grade=2,reward_item={[0]=item_table[26],[1]=item_table[13],[2]=item_table[27],[3]=item_table[44],[4]=item_table[29],[5]=item_table[25]},},
{grade=2,reward_item={[0]=item_table[45],[1]=item_table[13],[2]=item_table[31],[3]=item_table[46],[4]=item_table[33],[5]=item_table[34]},}
},

reward_meta_table_map={
[10]=2,	-- depth:1
[11]=3,	-- depth:1
[12]=4,	-- depth:1
[13]=5,	-- depth:1
[14]=6,	-- depth:1
[15]=7,	-- depth:1
[16]=8,	-- depth:1
},
open_day_default_table={start_day=1,end_day=7,grade=1,display_model=37028,},

reward_default_table={grade=1,seq=0,need_chongzhi=720,reward_item={[0]=item_table[1],[1]=item_table[2],[2]=item_table[35],[3]=item_table[47],[4]=item_table[37],[5]=item_table[38]},}

}

