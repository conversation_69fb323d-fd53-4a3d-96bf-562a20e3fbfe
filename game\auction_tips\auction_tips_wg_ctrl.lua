require("game/auction_tips/auction_tips_view")
require("game/auction_tips/auction_tips_wg_data")

AuctionTipsWGCtrl = AuctionTipsWGCtrl or BaseClass(BaseWGCtrl)
function AuctionTipsWGCtrl:__init()
	if AuctionTipsWGCtrl.Instance then
		error("[AuctionTipsWGCtrl]:Attempt to create singleton twice!")
	end
    -- 单例
	AuctionTipsWGCtrl.Instance = self

    self.data = AuctionTipsWGData.New()
    self.view = AuctionTipsView.New(GuideModuleName.AuctionTipsView)

    -- 注册事件监听
    self:RegisterAllEvents()
end

function AuctionTipsWGCtrl:__delete()

    if self.show_timer then
		GlobalTimerQuest:CancelQuest(self.show_timer)
		self.show_timer = nil
	end

    self:UnRegisterAllEvents()
    -- 销毁data
    self.data:DeleteMe()
	self.data = nil
    -- 销毁view
	self.view:DeleteMe()
	self.view = nil

    AuctionTipsWGCtrl.Instance = nil
end

function AuctionTipsWGCtrl:RegisterAllEvents()
    -- 天数改变
	self:BindGlobalEvent(OtherEventType.PASS_DAY, BindTool.Bind1(self.OnDayChange, self))
    self:BindGlobalEvent(MainUIEventType.MAINUI_OPEN_COMLETE, BindTool.Bind1(self.MainuiOpenCreateCallBack, self)) --主界面加载完成
end

-- 注销事件监听
function AuctionTipsWGCtrl:UnRegisterAllEvents()

end

function AuctionTipsWGCtrl:OnDayChange()
    self.data:SetPeriodTime()
    self:SetTimer()
end

function AuctionTipsWGCtrl:MainuiOpenCreateCallBack()
    self.data:SetPeriodTime()
    self:SetTimer()
end

function AuctionTipsWGCtrl:SetTimer()
    if self.show_timer then
		GlobalTimerQuest:CancelQuest(self.show_timer)
		self.show_timer = nil
	end

    local server_time = TimeWGCtrl.Instance:GetServerTime()
    local round, next_start_timestemp = AuctionTipsWGData.Instance:CheckInPeriodBuType(2) -- 世界拍卖
    MainuiWGCtrl.Instance:FlushView(0,"set_auction_tips")
    if round ~= 0 then
        self.show_timer = GlobalTimerQuest:AddDelayTimer(function ()
            self:SetTimer()
        end, next_start_timestemp.end_timestemp - server_time + 2 )

    elseif round == 0 and next_start_timestemp and next_start_timestemp ~= 0 then
        self.show_timer = GlobalTimerQuest:AddDelayTimer(function ()
            self:SetTimer()
        end, next_start_timestemp - server_time + 2)
    end


end

