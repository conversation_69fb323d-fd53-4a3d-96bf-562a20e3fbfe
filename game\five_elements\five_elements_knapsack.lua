local DEFAULT_BAG_CELL_NUM = 200
local RIGHT_PANEL = {
	BAG = 1,
	QIANGHUA = 2,
	SHENGHUA = 3,
}

function FiveElementsView:InitFiveElementKnapsackView()
	self.select_part_index = 0

	if not self.knapsack_list then
		self.knapsack_list = AsyncBaseGrid.New()
		self.knapsack_list:CreateCells({col = 4, cell_count = DEFAULT_BAG_CELL_NUM, list_view = self.node_list["knapsack_list"],
		assetBundle = "uis/view/five_elements_ui_prefab", assetName = "fe_bag_cell", itemRender = FEBagCell, change_cells_num = 2})
		self.knapsack_list:SetStartZeroIndex(false)
		self.knapsack_list:SetIsMultiSelect(false)
	end

	if not self.shenghua_cost_item then
		self.shenghua_cost_item = ItemCell.New(self.node_list.shenghua_item_pos)
	end

	if not self.cost_item then
		self.cost_item = ItemCell.New(self.node_list.cost_item_pos)	
	end

	if self.qianghua_attr_list == nil then
        self.qianghua_attr_list = {}
        local node_num = self.node_list["qianghua_attr_list"].transform.childCount
        for i = 1, node_num do
            self.qianghua_attr_list[i] = CommonAddAttrRender.New(self.node_list["qianghua_attr_list"]:FindObj("attr_" .. i))
			self.qianghua_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

	if self.shenghua_attr_list == nil then
        self.shenghua_attr_list = {}
        local node_num = self.node_list["shenghua_attr_list"].transform.childCount
        for i = 1, node_num do
            self.shenghua_attr_list[i] = CommonAddAttrRender.New(self.node_list["shenghua_attr_list"]:FindObj("attr_" .. i))
			self.shenghua_attr_list[i]:SetAttrNameNeedSpace(true)
        end
    end

	self.knapsack_default_select_part_cell = {}
	for i = 0, 4 do
		self.node_list["tog_item" .. (i + 1)].toggle:AddClickListener(BindTool.Bind(self.OnClickPartSelect, self, i))
		self.knapsack_default_select_part_cell[i] = -1
	end

	self.node_list.tog_knapsack_bag.toggle:AddValueChangedListener(BindTool.Bind(self.OnBeiBaoToggleChange,self))
	self.node_list.tog_qianghua.toggle:AddValueChangedListener(BindTool.Bind(self.OnQiangHuaToggleChange,self))
	self.node_list.tog_shenghua.toggle:AddValueChangedListener(BindTool.Bind(self.OnShengHuaToggleChange,self))

	self.can_qianghua_flag = false
	self.can_shenghua_flag = false
	self.node_list.qianghua_nodata_text.text.text = Language.FiveElements.KnapsackQIangHuaNoData
	self.knapsack_rightpanel_showid = RIGHT_PANEL.BAG

	self:LoadMidPartCell()
	XUI.AddClickEventListener(self.node_list.btn_suit, BindTool.Bind(self.OnSuitBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_hecheng, BindTool.Bind(self.OnHeChengBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_fenjie, BindTool.Bind(self.OnFenJieBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_qianghua, BindTool.Bind(self.OnQianghuaBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind(self.OnTipsBtnClick, self))
	XUI.AddClickEventListener(self.node_list.qianghua_item_bg, BindTool.Bind(self.OnQiangHuaItemIconClick, self))
	XUI.AddClickEventListener(self.node_list.btn_do_draw, BindTool.Bind(self.OnGoDrawClick, self))
	XUI.AddClickEventListener(self.node_list.btn_shenghua, BindTool.Bind(self.OnShengHuaBtnClick ,self))
end

function FiveElementsView:KnapsackViewReleaseCallBack()
	self.show_part_id = nil
	self.knapsack_rightpanel_showid = nil
	self.select_part_cell_id = nil

	if self.knapsack_list then
		self.knapsack_list:DeleteMe()
		self.knapsack_list = nil
	end
	
	if self.cost_item then
		self.cost_item:DeleteMe()
		self.cost_item = nil
	end

	if self.qianghua_attr_list then
        for k, v in pairs(self.qianghua_attr_list) do
            v:DeleteMe()
        end
        self.qianghua_attr_list = nil
    end

	if self.shenghua_attr_list then
        for k, v in pairs(self.shenghua_attr_list) do
            v:DeleteMe()
        end
        self.shenghua_attr_list = nil
    end

	if self.shenghua_cost_item then
		self.shenghua_cost_item:DeleteMe()
		self.shenghua_cost_item = nil
	end

	if self.five_elements_part_cell_list then
		for k, v in pairs(self.five_elements_part_cell_list) do
			for i, j in pairs(v) do
				j:DeleteMe()
			end
		end

		self.five_elements_part_cell_list = nil
	end	
end

function FiveElementsView:LoadMidPartCell()
	if not self.five_elements_part_cell_list then
		self.five_elements_part_cell_list = {}
		for i = 0, 4 do
			local part_cell_list = {}
			for j = 0, 7 do
				local itemRender = FiveElementsPartCell.New(self.node_list["five_item_list" .. (i + 1)]:FindObj("five_item_pos" .. (j + 1) .."/five_item_render"))
				itemRender:SetIndex(j)
				itemRender:SetClickCallBack(BindTool.Bind(self.OnSelectPartCell, self))
				part_cell_list[j] = itemRender
			end

			self.five_elements_part_cell_list[i] = part_cell_list
		end
	end
end

function FiveElementsView:ShowFiveElementKnapsackView()
	self.node_list["title_view_name"].text.text = Language.FiveElements.TabName[2]
	self:ChangeFiveElementsViewBg("a2_wx_bg2")

	local tween_info = UITween_CONSTS.FiveElements
	self.node_list.mid_root.canvas_group.alpha = 0
	self.node_list.btn_suit.canvas_group.alpha = 0
	RectTransform.SetAnchoredPositionXY(self.node_list.bag_right_root.rect, 600, 0)

	for i = 1, 5 do
		local x, y = RectTransform.GetAnchoredPositionXY(self.node_list["tog_item"..i].rect)
		RectTransform.SetAnchoredPositionXY(self.node_list["tog_item"..i].rect, -200, y)
	end

	self.node_list.mid_root.canvas_group:DoAlpha(0, 1, tween_info.mid_show)
	local right_tween = self.node_list.bag_right_root.rect:DOAnchorPos(Vector2(60, 0), tween_info.movetime)
	right_tween:OnComplete(function()
		self.node_list.btn_suit.canvas_group:DoAlpha(0, 1, tween_info.canvas_group_show)
	end)

	for i = 1, 5 do
		ReDelayCall(self, function()
			if self.node_list and self.node_list["tog_item" .. i] then
				local x, y = RectTransform.GetAnchoredPositionXY(self.node_list["tog_item"..i].rect)
				self.node_list["tog_item"..i].rect:DOAnchorPosX(-6, tween_info.item_movetime)
			end
		end, tween_info.nextdodelay * i, "five_elements_knapsack" .. i)
	end
end

function FiveElementsView:OnFlushFiveElementKnapsackView(param_t, index)
	local force_flush_index = -1
	for k, v in pairs(param_t) do
		if k == "all" then
			if v.jump_index then
				force_flush_index = v.jump_index
				self.select_part_index = v.jump_index
			end
		end
	end

	if force_flush_index < 0 then
		self.select_part_index = FiveElementsWGData.Instance:GetKnapsackDefaultSelectTogIndex(self.select_part_index)
	end

	local tog_index = self.select_part_index + 1
	if self.node_list["tog_item" .. tog_index].toggle.isOn == true then
		self:OnClickPartSelect(self.select_part_index)
	else
		self.node_list["tog_item" .. tog_index].toggle.isOn = true
	end

	self:SetKnapscakLeftTogRemind()

	local suit_remind = FiveElementsWGData.Instance:GetSuitMainRemind()
	self.node_list.btn_suit_remind:SetActive(suit_remind)
end

-- 大类行选择
function FiveElementsView:OnClickPartSelect(index)
	self.select_part_index = index
	FiveElementsWGData.Instance:SetPartDefaultSelectId(index)  -- 保留用于装备镶嵌
	local select_part_id = self.knapsack_default_select_part_cell[index] or -1
	local cal_new_index = FiveElementsWGData.Instance:GetKnapsackDefaultSelectPartCellId(index, select_part_id)

	for k, v in pairs(self.five_elements_part_cell_list[index]) do
		local data = FiveElementsWGData.Instance:GetPartItemCellData(index, k)
		v:SetData(data)
		v:SetSelectState(k == cal_new_index)
	end

	local bundle, asset = ResPath.GetRawImagesPNG("a2_wx_tm_" .. self.select_part_index + 1)
	if self.node_list.text_img then
		self.node_list["text_img"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["text_img"].raw_image:SetNativeSize()
		end)
	end

	local render = (self.five_elements_part_cell_list[index] or {})[cal_new_index]
	if render then
		self:OnSelectPartCell(render, true)
	else
		self:SetKnapscakRightPanelanelRemind()
	end

	local state = FiveElementsWGData.Instance:GetKnapsackBagRemind(index)
	self.node_list["beibao_toggle_remind"]:SetActive(state)

	-- 处理右侧面板选中
	local qianghua_remind = FiveElementsWGData.Instance:GetKnapsackQiangHuaFlag(index, cal_new_index)
	local shenghua_remind = FiveElementsWGData.Instance:GetKnapsackShengHuaFlag(index)

	if self.knapsack_rightpanel_showid == RIGHT_PANEL.BAG then
		if state then
			self.knapsack_rightpanel_showid = RIGHT_PANEL.BAG
			self:FlushKnapsackRightPanel()
			return
		end
	elseif self.knapsack_rightpanel_showid == RIGHT_PANEL.QIANGHUA then
		if qianghua_remind then
			self.knapsack_rightpanel_showid = RIGHT_PANEL.QIANGHUA
			self:FlushKnapsackRightPanel()
			return
		end
	elseif self.knapsack_rightpanel_showid == RIGHT_PANEL.SHENGHUA then
		if shenghua_remind then
			self.knapsack_rightpanel_showid = RIGHT_PANEL.SHENGHUA
			self:FlushKnapsackRightPanel()
			return
		end
	end

	-- 优先背包 强化  升华
	if state then
		self.knapsack_rightpanel_showid = RIGHT_PANEL.BAG
		self:FlushKnapsackRightPanel()
		return
	end

	if qianghua_remind then
		self.knapsack_rightpanel_showid = RIGHT_PANEL.QIANGHUA
		self:FlushKnapsackRightPanel()
		return
	end

	if shenghua_remind then
		self.knapsack_rightpanel_showid = RIGHT_PANEL.SHENGHUA
		self:FlushKnapsackRightPanel()
		return
	end
	
	self:FlushKnapsackRightPanel()
end

-- 小珠子选择
function FiveElementsView:OnSelectPartCell(cell, ignore_tips)
	if nil == cell or nil == cell.data then
		return
	end

	local data = cell.data

	if data.item_id > 0 then
		local cur_part_cell_id = self.knapsack_default_select_part_cell[self.select_part_index] or -1

		if not ignore_tips then
			if cur_part_cell_id == data.part_cell_id then
				TipWGCtrl.Instance:OpenItem(data)
			end
		end

		self.knapsack_default_select_part_cell[self.select_part_index] = data.part_cell_id

		for k, v in pairs(self.five_elements_part_cell_list[self.select_part_index]) do
			v:SetSelectState(k == data.part_cell_id)
		end

		if not ignore_tips then
			self:FlushKnapsackRightPanel()
		end
	else
		TipWGCtrl.Instance:ShowSystemMsg(Language.FiveElements.NotActive)
	end

	self:SetKnapscakRightPanelanelRemind()
end

function FiveElementsView:SetKnapscakRightPanelanelRemind()
	local part_cell_index = self.knapsack_default_select_part_cell[self.select_part_index] or -1
	local qianghua_flag = FiveElementsWGData.Instance:GetKnapsackQiangHuaFlag(self.select_part_index, part_cell_index)
	local shegnhua_remind = FiveElementsWGData.Instance:GetOverviewPartRemind(self.select_part_index)

	self.node_list.qianghua_toggle_remind:SetActive(qianghua_flag)
	self.node_list.btn_qianghua_remind:SetActive(qianghua_flag)
	self.node_list.shenghua_toggle_remind:SetActive(shegnhua_remind)
	self.node_list.shenghua_btn_remind:SetActive(shegnhua_remind)
end

function FiveElementsView:SetKnapscakLeftTogRemind()
	for i = 1, 5 do
		local flag = FiveElementsWGData.Instance:GetKnapsackTogUpFlag(i - 1)
		self.node_list["tog_item_remind" .. i]:SetActive(flag)
	end
end

function FiveElementsView:FlushKnapsackRightPanel()
	if self.knapsack_rightpanel_showid == RIGHT_PANEL.BAG then
		if self.node_list.tog_knapsack_bag.toggle.isOn == false then
			self.node_list.tog_knapsack_bag.toggle.isOn = true
		end

		self:FlushKnapsackRightBagPanel()
	elseif self.knapsack_rightpanel_showid == RIGHT_PANEL.QIANGHUA then
		if self.node_list.tog_qianghua.toggle.isOn == false then
			self.node_list.tog_qianghua.toggle.isOn = true
		end

		self:FlushKnapsackRightQianghuaPanel()
	elseif self.knapsack_rightpanel_showid == RIGHT_PANEL.SHENGHUA then
		if self.node_list.tog_shenghua.toggle.isOn == false then
			self.node_list.tog_shenghua.toggle.isOn = true
		end

		self:FlushOverviewShengHuaPanel()
	end
end

function FiveElementsView:KnapsackItemChangeFlush()
	self:SetKnapscakRightPanelanelRemind()
	self:SetKnapscakLeftTogRemind()
	self:FlushKnapsackRightPanel()
	self:FlushToggleList()
end

function FiveElementsView:FlushToggleList()
	for k, v in pairs(self.five_elements_part_cell_list[self.select_part_index]) do
		v:Flush()
	end
end

function FiveElementsView:FlushKnapsackRightBagPanel()
	local hecheng_remind = FiveElementsWGData.Instance:GetComposeRemind().compose_remind or false
	self.node_list.heceng_remind:SetActive(hecheng_remind)

	if self.knapsack_list then
		local data_list = FiveElementsWGData.Instance:GetFiveElementsBagListInfo()
		self.knapsack_list:SetDataList(data_list)
	end
end

function FiveElementsView:FlushKnapsackRightQianghuaPanel()
	self.can_qianghua_flag = false
	local select_id = self.knapsack_default_select_part_cell[self.select_part_index] or -1
	local has_select = select_id >= 0
	
	self.node_list.qianghua_has_data:SetActive(has_select)
	self.node_list.qianghua_nodata:SetActive(not has_select)

	if has_select then
		local data = FiveElementsWGData.Instance:GetPartItemCellData(self.select_part_index, select_id)
	    local bundle, asset = ResPath.GetFiveElementsImg("a2_wx_color_" .. data.color)
		self.node_list.qianghua_item_bg.image:LoadSprite(bundle, asset, function()
			self.node_list.qianghua_item_bg.image:SetNativeSize()
		end)

		local bundle1, asset1 = ResPath.GetFiveElementsImg("a2_wx_cq_z_" .. select_id)
		self.node_list.qianghua_item_icon.image:LoadSprite(bundle1, asset1, function()
			self.node_list.qianghua_item_icon.image:SetNativeSize()
		end)

		local up_data, next_data = FiveElementsWGData.Instance:GetPartCellUpLevelInfo(self.select_part_index, select_id, data.level)
		if IsEmptyTable(up_data) then
			return
		end

		local attr_list = FiveElementsWGData.Instance:GetQiangHuaAttrData(up_data, next_data)
		local need_show_effect = false

		if nil ~= self.select_part_index_cache and nil ~= self.select_select_id_cache and nil ~= self.select_part_level_cache then
			if (self.select_part_index_cache == self.select_part_index) and (self.select_select_id_cache == select_id) and (data.level - self.select_part_level_cache == 1) then
				need_show_effect = true
			end
		end

		self.select_part_index_cache = self.select_part_index
		self.select_select_id_cache = select_id
		self.select_part_level_cache = data.level

		for k, v in pairs(self.qianghua_attr_list) do
			v:SetData(attr_list[k])

			if need_show_effect then
				v:PlayAttrValueUpEffect()
			end
		end

		--self.bag_attr_item_list1:SetDataList(attr_list)
		--self.node_list.bag_attr_item_name1.text.text = string.format(Language.FiveElements.LevelCha, data.level)
		local can_update = not IsEmptyTable(next_data)

		if can_update then
			--local attr_list_next = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_data, "attr_id", "attr_value")
			--self.bag_attr_item_list2:SetDataList(attr_list_next)
			self.cost_item_id = up_data.cost_item_id
			local item_num = ItemWGData.Instance:GetItemNumInBagById(self.cost_item_id)
			local cost_item_num = up_data.cost_item_num
			local enough = item_num >= cost_item_num

			self.cost_item:SetFlushCallBack(function ()
				local right_text = ToColorStr(item_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
				self.cost_item:SetRightBottomColorText(right_text)
				self.cost_item:SetRightBottomTextVisible(true)
			end)

			self.can_qianghua_flag = enough
			self.cost_item:SetData({item_id = self.cost_item_id, num = cost_item_num, is_bind = 0})
		end

		--self.node_list.bag_attr_item_name2.text.text = can_update and string.format(Language.FiveElements.LevelCha, data.level + 1) or ""
		self.node_list.level.text.text = data.level
		--self.node_list.bag_arrow:SetActive(can_update)
		self.node_list.cost_item_pos:SetActive(can_update)
		self.node_list.btn_qianghua:SetActive(can_update)
		self.node_list.full_level:SetActive(not can_update)
		XUI.SetButtonEnabled(self.node_list.btn_qianghua, can_update)
		XUI.SetButtonEnabled(self.node_list.btn_yijianqianghua, can_update)
		--self.node_list.bag_attr_item2:SetActive(can_update)
	else
		self.node_list.btn_qianghua:SetActive(false)
		self.node_list.full_level:SetActive(false)
		self.node_list.cost_item_pos:SetActive(false)
		XUI.SetButtonEnabled(self.node_list.btn_qianghua, false)
		XUI.SetButtonEnabled(self.node_list.btn_yijianqianghua, false)
	end
end

function FiveElementsView:FlushOverviewShengHuaPanel()
	self.can_shenghua_flag = false
	local have_data = FiveElementsWGData.Instance:GetPartItemData(self.select_part_index)
	local current_data, next_data = FiveElementsWGData.Instance:GetOverViewTiShengData(self.select_part_index)
	local has_select = self.select_part_index >= 0
	local has_data = have_data.color > GameEnum.ITEM_COLOR_WHITE
	local is_max_level = has_select and IsEmptyTable(next_data)

	self.node_list.shenghua_hasdata:SetActive(has_data)
	self.node_list.shenghua_nodata:SetActive(not has_data)
	self.node_list.shenghua_item_pos:SetActive(has_data and not is_max_level)
	self.node_list.shenghua_flag:SetActive(is_max_level)
	self.node_list.btn_shenghua:SetActive(not is_max_level)
	XUI.SetButtonEnabled(self.node_list.btn_shenghua, has_data and not is_max_level)

	if has_data then
		local part_name = FiveElementsWGData.Instance:GetPartCfg()[self.select_part_index + 1].name
		--self.node_list.attr_item_name1.text.text = string.format(Language.FiveElements.NameAndLevel, part_name, current_data.level)
		self.node_list.shenghua_title.text.text = string.format(Language.FiveElements.TitleAndLevel, part_name, current_data.level)
		-- local attr_data = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(current_data, "attr_id", "attr_value")
		--self.attr_item_list1:SetDataList(attr_data)

		if not is_max_level then
			--self.node_list.attr_item_name2.text.text = string.format(Language.FiveElements.NameAndLevel, part_name, next_data.level)
			-- local next_attr_data = ItemShowWGData.Instance:OutStrAttrListAndCapalityByAttrId(next_data, "attr_id", "attr_value")
			--self.attr_item_list2:SetDataList(next_attr_data)
			self.shenghua_item_id = current_data.cost_item_id
			local item_num = ItemWGData.Instance:GetItemNumInBagById(self.shenghua_item_id)
			local cost_item_num = current_data.cost_item_num
			local enough = item_num >= cost_item_num

			self.shenghua_cost_item:SetFlushCallBack(function ()
				local right_text = ToColorStr(item_num .. "/" .. cost_item_num, enough and COLOR3B.D_GREEN or COLOR3B.D_RED)
				self.shenghua_cost_item:SetRightBottomColorText(right_text)
				self.shenghua_cost_item:SetRightBottomTextVisible(true)
			end)

			self.can_shenghua_flag = enough
			self.shenghua_cost_item:SetData({item_id = self.shenghua_item_id, num = cost_item_num, is_bind = 0})
		end
		local attr_list = FiveElementsWGData.Instance:GetQiangHuaAttrData(current_data, next_data)

		local need_show_effect = false
		if nil ~= self.shenghua_select_part_cache and nil ~= self.shenghua_part_level_cache then
			if (self.shenghua_select_part_cache == self.select_part_index) and (current_data.level - self.shenghua_part_level_cache == 1 ) then
				need_show_effect = true
			end
		end

		self.shenghua_select_part_cache = self.select_part_index
		self.shenghua_part_level_cache = current_data.level

		for k, v in pairs(self.shenghua_attr_list) do
			v:SetData(attr_list[k])

			if need_show_effect then
				v:PlayAttrValueUpEffect()
			end
		end
		local desc_cfg = FiveElementsWGData.Instance:GetPartCfg()
		self.node_list.shenghua_desc.text.text = desc_cfg[self.select_part_index + 1].part_txt
	end
end

function FiveElementsView:OnBeiBaoToggleChange(is_on)
	if is_on then
		self.knapsack_rightpanel_showid = RIGHT_PANEL.BAG
		self:FlushKnapsackRightBagPanel()
	end
end

function FiveElementsView:OnQiangHuaToggleChange(is_on)
	if is_on then
		self.knapsack_rightpanel_showid = RIGHT_PANEL.QIANGHUA
		self:FlushKnapsackRightQianghuaPanel()
	end
end

function FiveElementsView:OnShengHuaToggleChange(is_on)
	if is_on then
		self.knapsack_rightpanel_showid = RIGHT_PANEL.SHENGHUA
		self:FlushOverviewShengHuaPanel()
	end
end

function FiveElementsView:OnTipsBtnClick()
	RuleTip.Instance:SetContent(Language.FiveElements.Knapsack_Tips_Content, Language.FiveElements.Knapsack_Tips_Title)
end

function FiveElementsView:OnSuitBtnClick()
	FiveElementsWGCtrl.Instance:OpenSuitView()
end

function FiveElementsView:OnHeChengBtnClick()
	FiveElementsWGCtrl.Instance:OpenComposeView()
end

function FiveElementsView:OnFenJieBtnClick()
	FiveElementsWGCtrl.Instance:OpenResolveView()
end

function FiveElementsView:OnQianghuaBtnClick()
	if self.can_qianghua_flag then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_qianghua,
	    is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["knapsack_effect_root"]})
		local select_id = self.knapsack_default_select_part_cell[self.select_part_index]
		FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_HOLE_LEVEL_UP, self.select_part_index, select_id)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.cost_item_id})
	end
end

function FiveElementsView:OnShengHuaBtnClick()
	if self.can_shenghua_flag then
		TipWGCtrl.Instance:ShowEffect({effect_type = UIEffectName.s_shengji,
	    is_success = true, pos = Vector2(0, 0), parent_node = self.node_list["knapsack_effect_root"]})
		FiveElementsWGCtrl.Instance:SendSFiveElementsRequest(FIVE_ELEMENTS_OPERATE_TYPE.FIVE_ELEMENTS_OPERATE_TYPE_PART_LEVEL_UP, self.select_part_index)
	else
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.ItemNotEnough)
		TipWGCtrl.Instance:OpenItemTipGetWay({item_id = self.shenghua_item_id})
	end
end

function FiveElementsView:OnQiangHuaItemIconClick()
	local select_id = self.knapsack_default_select_part_cell[self.select_part_index]
	local has_select = select_id >= 0

	if has_select then
		local data = FiveElementsWGData.Instance:GetPartItemCellData(self.select_part_index, select_id)

		if not IsEmptyTable(data) and data.item_id > 0 then
			TipWGCtrl.Instance:OpenItem(data)
		end
	end
end

function FiveElementsView:OnGoDrawClick()
	FiveElementsWGCtrl.Instance:OpenFiveELementsTreasuryView()
end

----------------------------------------嵌入式小珠子-------------------------------------
FiveElementsPartCell = FiveElementsPartCell or BaseClass(BaseRender)

function FiveElementsPartCell:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end

	local data = self.data
	self.node_list.level_bg:SetActive(data.color > 0)
	self.node_list.level.text.text = data.level
	local color = "a2_wx_color_" .. data.color
	local bundle, asset = ResPath.GetFiveElementsImg(color)
	self.node_list.icon.image:LoadSprite(bundle, asset)
	self:FlushRemind()
end

function FiveElementsPartCell:FlushRemind()
	if self.node_list.remind then
		local remind = FiveElementsWGData.Instance:GetFiveElementsPartCellRemind(self.data.part_id, self.data.part_cell_id)
		self.node_list.remind:SetActive(remind)
	end
end

function FiveElementsPartCell:SetSelectState(state)
	self.node_list.select:SetActive(state)
end