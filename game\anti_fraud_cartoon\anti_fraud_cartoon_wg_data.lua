-- 防骗漫画
AntiFraudCartoonWGData = AntiFraudCartoonWGData or BaseClass()
function AntiFraudCartoonWGData:__init()
	if AntiFraudCartoonWGData.Instance ~= nil then
		<PERSON>rrorLog("[AntiFraudCartoonWGData] attempt to create singleton twice!")
		return
	end
	AntiFraudCartoonWGData.Instance = self

	self.is_fetched = false

	RemindManager.Instance:Register(RemindName.AntiFraudCartoonRemind, BindTool.Bind(self.GetAntiCartoonRemind, self)) 	-- 可领取红点
end

function AntiFraudCartoonWGData:__delete()
	RemindManager.Instance:UnRegister(RemindName.AntiFraudCartoonRemind)

	AntiFraudCartoonWGData.Instance = nil
end

-- 设置是否已领取
function AntiFraudCartoonWGData:SetIsFetched(protocol)
	self.is_fetched = protocol.fetched
end

-- 是否已领取奖励
function AntiFraudCartoonWGData:GetIsFetched()
	return self.is_fetched
end

-- 领取红点
function AntiFraudCartoonWGData:GetAntiCartoonRemind()
	return self:GetIsFetched() and 0 or 1
end