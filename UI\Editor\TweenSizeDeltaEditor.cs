﻿using UnityEngine;
using UnityEditor;

[CustomEditor(typeof(UGUITweenSizeDelta))]
public class TweenSizeDeltaEditor : UITweenerEditor 
{

	public override void OnInspectorGUI ()
	{
		GUILayout.Space(6f);
		UGUITweenEditorTools.SetLabelWidth(120f);

		UGUITweenSizeDelta tw = target as UGUITweenSizeDelta;
		GUI.changed = false;

		Vector2 from = EditorGUILayout.Vector2Field("From", tw.from);
		Vector2 to = EditorGUILayout.Vector2Field("To", tw.to);

		if (GUI.changed)
		{
			UGUITweenEditorTools.RegisterUndo("Tween Change", tw);
			tw.from = from;
			tw.to = to;
			UGUITweenEditorTools.SetDirty(tw);
		}

		DrawCommonProperties();
	}
}
