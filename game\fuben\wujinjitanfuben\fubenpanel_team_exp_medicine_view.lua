---------------------
TeamExpFbMedicineView = TeamExpFbMedicineView or BaseClass(SafeBaseView)
-- 多人经验本药水
function TeamExpFbMedicineView:__init()
	--self.texture_path_list[1] = 'res/xui/fubenpanel.png'
	self:AddViewResource(0, "uis/view/common_panel_prefab", "layout_commmon_second_panel", {sizeDelta = Vector2(812, 574)})
	self:AddViewResource(0, "uis/view/team_exp_fb_prefab", "layout_exp_medicine")
	self.view_layer = UiLayer.Normal
	self.item_data_event = BindTool.Bind(self.ItemDataChangeCallback, self)
	self:SetMaskBg(true)
	self.timer_type = "TeamExpFbMedicineViewCountDown"
end

function TeamExpFbMedicineView:LoadCallBack()
	self.node_list.title_view_name.text.text = Language.FuBenPanel.Xiaolv 		--鼓舞
	self.medicine_list_view = AsyncListView.New(MedicineItemRender, self.node_list["medicine_list"])
	ItemWGData.Instance:NotifyDataChangeCallBack(self.item_data_event)
	self.effect_change = GlobalEventSystem:Bind(ObjectEventType.FIGHT_EFFECT_CHANGE, BindTool.Bind(self.OnEffectChange, self))
end

function TeamExpFbMedicineView:ItemDataChangeCallback()
	self:Flush()
end

function TeamExpFbMedicineView:OnEffectChange()
	self:Flush()
end

function TeamExpFbMedicineView:ReleaseCallBack()
	if CountDownManager.Instance:HasCountDown(self.timer_type) then
		CountDownManager.Instance:RemoveCountDown(self.timer_type)
	end
	if self.effect_change then
		GlobalEventSystem:UnBind(self.effect_change)
		self.effect_change = nil
	end
	if self.medicine_list_view then
		self.medicine_list_view:DeleteMe()
		self.medicine_list_view = nil
	end
	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.item_data_event)
end

function TeamExpFbMedicineView:CloseCallBack()

end

function TeamExpFbMedicineView:OpenCallBack()

end

function TeamExpFbMedicineView:ShowIndexCallBack(index)
	self:Flush()
end

function TeamExpFbMedicineView:OnFlush(param_list, index)
	local data_list = {}
	local list = WuJinJiTanWGData.Instance:GetMedicineItemList()
	local seq_list = WuJinJiTanWGData.Instance:GetMedicineItemSeqList()
	for i, v in ipairs(list) do
		table.insert(data_list, {item_id = v, img_index = i, seq = seq_list[i]})
	end
	self.medicine_list_view:SetDataList(data_list)

	local exp_buff = FightWGData.Instance:GetExpBuff()
	if exp_buff then
		local cur_value = exp_buff.param_list[3] / 100
		local cur_time = TimeUtil.FormatSecondDHM2(math.ceil(exp_buff.param_list[1] / 1000))
		self.node_list.cur_exp_add_text.text.text = string.format(Language.ExpAddition.CurEffect, cur_value)
		self.node_list.text_time.text.text = string.format(Language.ExpAddition.CurEffect2, cur_time)
		if CountDownManager.Instance:HasCountDown(self.timer_type) then
			CountDownManager.Instance:RemoveCountDown(self.timer_type)
		end
		local remain_time = math.ceil(exp_buff.param_list[1])
		remain_time = math.max(remain_time / 1000 - (Status.NowTime - exp_buff.recv_time), 0)
		self:UpdataNextTime(0,remain_time)
		CountDownManager.Instance:AddCountDown(self.timer_type, BindTool.Bind1(self.UpdataNextTime, self), BindTool.Bind(self.CompleteNextTime, self), nil, remain_time, 0)
	else
		self.node_list.cur_exp_add_text.text.text = Language.ExpAddition.CurNotEffect
		self.node_list.text_time.text.text = Language.ExpAddition.CurNotEffect2
	end
end

function TeamExpFbMedicineView:UpdataNextTime(elapse_time, total_time)
	local on_time = math.ceil(total_time - elapse_time)
	on_time = TimeUtil.FormatSecondDHM3(on_time)
	if self.node_list and self.node_list["cur_exp_add_text"] ~= nil and self.node_list["text_time"] ~= nil then
		local exp_buff = FightWGData.Instance:GetExpBuff()
		if exp_buff then
			local cur_value = exp_buff.param_list[3] / 100
			self.node_list["cur_exp_add_text"].text.text = string.format(Language.ExpAddition.CurEffect, cur_value)
			self.node_list["text_time"].text.text = string.format(Language.ExpAddition.CurEffect2, on_time)
		end

	end
end

function TeamExpFbMedicineView:CompleteNextTime()
	if self.node_list and self.node_list["cur_exp_add_text"] ~= nil and self.node_list["text_time"] ~= nil then
		local exp_buff = FightWGData.Instance:GetExpBuff()
		if exp_buff then
			local cur_value = exp_buff.param_list[3] / 100
			self.node_list["cur_exp_add_text"].text.text = string.format(Language.ExpAddition.CurEffect, cur_value)
			self.node_list["text_time"].text.text = string.format(Language.ExpAddition.CurEffect2, 0)
		end
	end
	CountDownManager.Instance:RemoveCountDown(self.timer_type)
end



--MedicineItemRender
----------------------------------------------------------------------------
MedicineItemRender = MedicineItemRender or BaseClass(BaseRender)
function MedicineItemRender:__init()
	XUI.AddClickEventListener(self.node_list.btn_buy, BindTool.Bind1(self.OnClickBuy, self))
	XUI.AddClickEventListener(self.node_list.btn_use, BindTool.Bind1(self.OnClickUse, self))
end
function MedicineItemRender:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function MedicineItemRender:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.ph_render_item)
end

function MedicineItemRender:OnClickBuy()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	if 0 == item_num then
		local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)
		local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
		local gold_num = RoleWGData.Instance.role_info.gold
		--sq说去掉绑玉
		if bind_gold_num >= shop_cfg.price then
			ShopWGCtrl.Instance:SendShopBuy(self.data.item_id, 1, 0, 1, self.data.seq)

		elseif gold_num >= shop_cfg.price then
			local ok_func = function()
				ShopWGCtrl.Instance:SendShopBuy(self.data.item_id, 1, 0, 0, self.data.seq)
			end
			-- TipWGCtrl.Instance:OpenAlertTips(Language.Guild.BindGoldNo,ok_func)
			TipWGCtrl.Instance:OpenAlertByNotBindYu(ok_func)
		else
			--SysMsgWGCtrl.Instance:ErrorRemind(Language.Common.NoEnoughGoldAndBindGold)
			VipWGCtrl.Instance:OpenTipNoGold()
		end
	--else
	--	local item_index = ItemWGData.Instance:GetItemIndex(self.data.item_id)
	--	if item_index >= 0 then
	--		BagWGCtrl.Instance:SendUseItem(item_index, 1)
	--	end
	end
end

function MedicineItemRender:OnClickUse()
	local enum = {
            [EFFECT_CLIENT_TYPE.ECT_ITEM_EXP1] = 22011,
            [EFFECT_CLIENT_TYPE.ECT_ITEM_EXP2] = 22012,
            [EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3] = 22013
            --[EFFECT_CLIENT_TYPE.ECT_ITEM_EXP3] = 
        }

    local str 
	local main_role_effect_list = FightWGData.Instance:GetMainRoleEffectList()

	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	local exp_buff = FightWGData.Instance:GetExpBuff()
	local cur_value = 0
	if exp_buff then
		cur_value = exp_buff.param_list[3] / 100
	end

	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)

	if item_num == 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.NoExpItem)
	elseif item_cfg and cur_value >= (item_cfg.param3 / 100) then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.FuBenPanel.NotUseExpItem)
	elseif item_num == 1 then
		local item_index = ItemWGData.Instance:GetItemIndex(self.data.item_id)
		if item_index >= 0 then
			BagWGCtrl.Instance:SendUseItem(item_index, 1)
		end
	else
		for k, v in pairs(main_role_effect_list) do
			if enum[v.client_effect_type] and enum[v.client_effect_type] < self.data.item_id then
				str = Language.FuBenPanel.YouXianShiYong
			end
		end
		OfflineRestWGCtrl.Instance:OpenUserOfflineView(self.data.item_id,1,1, str)
	end
end

function MedicineItemRender:OnFlush()
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id)
	local item_cfg = ItemWGData.Instance:GetItemConfig(self.data.item_id)
	local role_vo  = GameVoManager.Instance:GetMainRoleVo()
	local shop_cfg = ShopWGData.Instance:GetShopCfgSeq(self.data.seq)

	local exp_buff = FightWGData.Instance:GetExpBuff()
	local cur_value = 0
	if exp_buff then
		cur_value = exp_buff.param_list[3] / 100
	end
	local add_num = Language.FuBenPanel.ExpAddItemNum[self.data.img_index]

	local data = {}
	data.item_id = self.data.item_id
	data.num = 1
	self.item_cell:SetData(data)
	self.node_list.btn_use:SetActive(item_num > 0 or self.data.img_index <= 2)


	self.node_list.RedPoint:SetActive(item_num > 0 and cur_value < add_num)

	self.node_list.btn_buy:SetActive(item_num <= 0 and self.data.img_index > 2)
	self.node_list.cur_num_text:SetActive(item_num > 0 or self.data.img_index <= 2)
	self.node_list.gold_container:SetActive(item_num <= 0 and self.data.img_index > 2)
	local bundle, asset = ResPath.GetCommonButton("a3_ty_btn_6")
	local bundle1, asset1 = ResPath.GetCommonButton("a3_ty_btn_7")
	if item_num <= 0 then
		self.node_list.btn_use.image:LoadSprite(bundle, asset, function()
			self.node_list.btn_use.image:SetNativeSize()
		end)
		self.node_list.btn_use_text.text.text = ToColorStr(Language.FuBenPanel.Use,COLOR3B.C21)
	else
		self.node_list.btn_use.image:LoadSprite(bundle1, asset1, function()
			self.node_list.btn_use.image:SetNativeSize()
		end)
		self.node_list.btn_use_text.text.text = ToColorStr(Language.FuBenPanel.Use,COLOR3B.C5)
	end
	if item_num > 0 or self.data.img_index <= 2 then
		self.node_list.cur_num_text.text.text = string.format(Language.FuBenPanel.ExpMedicineItemNumStr, item_num)
	end
	if shop_cfg and item_num <= 0 then
		-- local other_cfg = WuJinJiTanWGData.GetOtherCfg()
		local bind_gold_num = RoleWGData.Instance.role_info.bind_gold
		local gold_num = RoleWGData.Instance.role_info.gold

		local color = (bind_gold_num >= shop_cfg.price or gold_num >= shop_cfg.price) and IS_CAN_COLOR.ENOUGH or IS_CAN_COLOR.NOT_ENOUGH
		local str = string.format(Language.FuBenPanel.ExpMedicineItemGoldStr, shop_cfg.price)
		self.node_list.gold_text.text.text = ToColorStr(str, color)
	end
	

	self.node_list["item_desc"].text.text = string.format(Language.FuBenPanel.ExpAddItemType[self.data.img_index],add_num.."%")
	--self.node_list.img_ml_item_hight_bg.raw_image:LoadSprite(ResPath.GetF2RawImagesPNG("exp_guwu_".. self.data.img_index))
	--self.node_list.exp_eff_img.image:LoadSprite(ResPath.GetTeamExpRes("fb_yishuzi".. self.data.img_index))
	--self.node_list.item_bg.image:LoadSprite(ResPath.GetTeamExpRes("fb_item_bg".. self.data.img_index))
end