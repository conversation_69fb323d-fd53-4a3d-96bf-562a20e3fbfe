------------------------------------------------------------
--人物称号View
------------------------------------------------------------
local TOGGLE_MAX = 7
local ATTR_COUNT = 7
local DELAY_TIME = 0.8
function RoleBranchView:LoadTitleView()
	self.is_peidai = true             --是否佩戴
	self.title_background_loader = nil
	self.old_have_num = 0             --上一次拥有的称号数量
	self.item_id_title_map = {}       --可以用item_id激活的称号, 1、防止物品变化，疯狂GetNum。 2、也可以用来当跳转
	self.title_id_index_map = {}      --用来跳转
	self.has_get_title_id_index_map = {} --以获得的称号
	self.has_load_list = {}
	self.title_sub_index = 1
	self.title_btn_index = 1
	self.change_title_id = 0
	self.title_item_change_call_back = BindTool.Bind(self.OnItemTitleChangeCallBack, self)
	self:InitTitleView()
	self:InitAccordionListView()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	ItemWGData.Instance:NotifyDataChangeCallBack(self.title_item_change_call_back)

	self.title_info_change = GlobalEventSystem:Bind(OtherEventType.TitleInfoChange,
		BindTool.Bind(self.OnTitleInfoChange, self))
end

function RoleBranchView:CloseTitleCallBack()
	if nil ~= self.accor_list then
		for k, v in pairs(self.accor_list) do
			v.btn.accordion_element.isOn = false
		end
	end

	RoleWGCtrl.Instance:FlushView(TabIndex.role_intro)
	self.change_title_id = 0
	self.jump_title_id = nil
end

function RoleBranchView:ShowTitleIndexCallBack()
	if not self.is_load_complete then
		return
	end

	self:SetHL()
end

function RoleBranchView:CalcScroll(cell)
	local screen_pos_tbl = UnityEngine.RectTransformUtility.WorldToScreenPoint(self.canvas.worldCamera,
		cell.view.transform.position)
	local _, local_position_tbl = UnityEngine.RectTransformUtility.ScreenPointToLocalPointInRectangle(
	self.node_list.Content.rect, screen_pos_tbl, UICamera, Vector2(0, 0))
	local per
	local content_size_y = self.node_list.Content.rect.sizeDelta.y
	local togglesv_size_y = self.node_list.ToggleSV.rect.sizeDelta.y
	if content_size_y + local_position_tbl.y < togglesv_size_y then
		per = 0
	else
		local rect = content_size_y - self.node_list.ToggleSV.rect.sizeDelta.y
		per = (content_size_y + local_position_tbl.y - togglesv_size_y + 26) / rect
	end

	per = math.min(per, 1)
	self.node_list.ToggleSV.scroll_rect.verticalNormalizedPosition = per
end

--设置跳转的title_id
function RoleBranchView:SetJumpParam(title_id)
	self.jump_title_id = title_id
	if self.is_load_complete then
		self:SetHL()
	end
end

function RoleBranchView:SetPutOnHL(new_list)
	if not self:IsLoadedIndex(0) then
		return
	end

	if not self.is_load_complete then
		return
	end

	local cell_data = TitleWGData.Instance:GetTitleListDataByType(1)
	if #cell_data < 1 then
		return
	end
	--local vo = GameVoManager.Instance:GetMainRoleVo()
	local used_title_list = new_list
	if used_title_list then
		local put_on_title_id = self.change_title_id or used_title_list[1]
		if put_on_title_id and put_on_title_id > 0 then
			local value_tab = self.has_get_title_id_index_map[put_on_title_id]
			if value_tab then
				self.accor_list[value_tab.parent_index].btn.accordion_element.isOn = true
				local tab = self.title_list_view[value_tab.parent_index]
				if tab then
					for k, v in pairs(tab) do
						v:SetToggleIsOn(false)
					end
				end
				local cell = value_tab.item_cell
				cell:SetToggleIsOn(true)

				local old_list_pos = self.title_id_index_map[put_on_title_id]
				if old_list_pos and old_list_pos.parent_index then
					self:FlushCurCellList(old_list_pos.parent_index) --激活新称号时,刷新其所在列表
				end

				self.delay_calc_scroll_quest = GlobalTimerQuest:AddDelayTimer(function()
					self:CalcScroll(cell)
				end, DELAY_TIME)
				return
			end
		end
	end
end

function RoleBranchView:SetHL()
	if self.delay_calc_scroll_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_calc_scroll_quest)
		self.delay_calc_scroll_quest = nil
	end


	--快速跳转
	--1、在快捷使用界面点击，或者在背包点击称号使用，跳转到该称号对应使用界面
	if self.jump_title_id then
		local title_cfg = TitleWGData.Instance:GetConfig(self.jump_title_id)
		if not title_cfg then return end
		self:FlushCurCellList(title_cfg.title_classify)

		local value_tab = self.title_id_index_map[self.jump_title_id]
		if value_tab then
			self.accor_list[value_tab.parent_index].btn.accordion_element.isOn = true
			local tab = self.title_list_view[value_tab.parent_index]
			if tab then
				for k, v in pairs(tab) do
					v:SetToggleIsOn(false)
				end
			end
			local cell = tab[value_tab.child_index]
			cell:SetToggleIsOn(true)
			self.delay_calc_scroll_quest = GlobalTimerQuest:AddDelayTimer(function()
				self:CalcScroll(cell)
			end, DELAY_TIME)
			return
		end
	end

	--升级红点
	local up_level_red, up_level_index = TitleWGData.Instance:RoleTitleUpLevelRemind()
	if up_level_red > 0 and up_level_index then
		self.accor_list[1].btn.accordion_element.isOn = true
		local tab = self.title_list_view[1]
		if tab then
			for k, v in pairs(tab) do
				v:SetToggleIsOn(false)
			end
		end

		local cell = tab[up_level_index]
		cell:SetToggleIsOn(true)
		self.delay_calc_scroll_quest = GlobalTimerQuest:AddDelayTimer(function()
			self:CalcScroll(cell)
		end, DELAY_TIME)
		return
	end
	--有激活红点时
	--1、打开称号界面， 默认选中列表中最上面那个有红点的页签以及对应红点的分页签
	local remind_count, title_id = TitleWGData.Instance:RoleTitleActRemind()
	if remind_count > 0 and title_id then
		local title_cfg = TitleWGData.Instance:GetConfig(title_id)
		if not title_cfg then return end
		self:FlushCurCellList(title_cfg.title_classify)

		local value_tab = self.title_id_index_map[title_id]
		if value_tab then
			self.accor_list[value_tab.parent_index].btn.accordion_element.isOn = true
			local tab = self.title_list_view[value_tab.parent_index]
			if tab then
				for k, v in pairs(tab) do
					v:SetToggleIsOn(false)
				end
			end
			local cell = tab[value_tab.child_index]
			cell:SetToggleIsOn(true)
			self.delay_calc_scroll_quest = GlobalTimerQuest:AddDelayTimer(function()
				self:CalcScroll(cell)
			end, DELAY_TIME)
			return
		end
	end

	--无红点时
	--1、打开称号界面，默认选中已拥有的第一个称号
	--2、若无已拥有称号， 则默认选中除已拥有页签外第一个页签的第一个称号
	local cell_data = TitleWGData.Instance:GetTitleListDataByType(1)
	if #cell_data < 1 then
		self.accor_list[2].btn.accordion_element.isOn = true
	else
		local vo = GameVoManager.Instance:GetMainRoleVo()
		if vo then
			self.cur_child_index = 1
			local put_on_title_id = vo.used_title_list[1]
			for i, v in ipairs(cell_data) do
				if v.title_id == put_on_title_id then
					self.cur_child_index = i
				end
			end
		end
		--self.cur_child_index = index
		--设置已有称号的index
		self.accor_list[1].btn.accordion_element.isOn = true
	end
end

function RoleBranchView:InitTitleView() --实例化视图
	------ 属性列表 ------------
	self.title_list_view = {}
	self.title_expiredtime = 0

	self.tilte_item_cell = ItemCell.New(self.node_list.title_item)
	if not self.title_attr_list then
		self.title_attr_list = {}
		local parent_node = self.node_list["attr_title_list"]
        local attr_num = parent_node.transform.childCount
        for i = 1, attr_num do
            local cell = CommonAddAttrRender.New(parent_node:FindObj("attr_" .. i))
            cell:SetIndex(i)
            cell:SetAttrNameNeedSpace(true)
            self.title_attr_list[i] = cell
        end
	end

	self.node_list["btn_title_wear"].button:AddClickListener(BindTool.Bind(self.OnClickSaveTitle, self))
	self.node_list["btn_title_active"].button:AddClickListener(BindTool.Bind(self.OnClickTitleActiveBtn, self))
	self.node_list["btn_title_up_level"].button:AddClickListener(BindTool.Bind(self.OnClickTitleUpLevelBtn, self))
	self.node_list["btn_chenghao_attr"].button:AddClickListener(BindTool.Bind(self.OnClickTitleAddition, self))
	-- self.node_list["btn_addition_close"].button:AddClickListener(BindTool.Bind(self.OnClickCloseAdditionTips, self))
	--self.node_list["btn_close"].button:AddClickListener(BindTool.Bind(self.Close, self))


	self.expiredtime_countdown_timer = GlobalTimerQuest:AddRunQuest(BindTool.Bind(self.FlushTitleExpiredtime, self), 1)
	-- XUI.SetButtonEnabled(self.node_list.btn_no_active,false)
	self.node_list.btn_no_active:SetActive(false)

	if self.fs_title_star_list == nil then
		self.fs_title_star_list = {}
		for i = 1, 5 do
			self.fs_title_star_list[i] = self.node_list["fs_title_star_" .. i]
		end
	end
	--self:FlushBackground()
end

function RoleBranchView:FlushBackground()
	local background_id = BackgroundWGData.Instance.curr_use_back_id
	local asset, bundle = nil, nil
	local back_data = BackgroundWGData.Instance:GetBigDataByID(background_id)

	self.node_list["background_root"]:SetActive(background_id ~= 0 and back_data)

	if background_id ~= 0 and back_data then
		if not self.title_background_loader then
			local background_loader = AllocAsyncLoader(self, "base_tip_title_back_cell")
			background_loader:SetIsUseObjPool(true)
			background_loader:SetParent(self.node_list["background_root"].transform)
			self.title_background_loader = background_loader
		end
		self.title_background_loader:Load(asset, bundle)
	end

	if self.role_model then
		self.role_model:PlayLastAction()
	end
end

function RoleBranchView:InitAccordionListView() ------实例化左边列表
	self.accor_list = {}
	for i = 1, TOGGLE_MAX do
		self.accor_list[i] = {}
		self.accor_list[i].text_name = self.node_list["BtnText" .. i]
		self.accor_list[i].high_text_name = self.node_list["text_high_btn" .. i]
		self.accor_list[i].list = self.node_list["List" .. i]
		self.accor_list[i].btn = self.node_list["SelectBtn" .. i]
		self.accor_list[i].remind = self.node_list["remind_" .. i]
		self.accor_list[i].list_item_cell = {}
		self:LoadCell(i)
		self.accor_list[i].select_btn = self.node_list["SelectBtn" .. i].gameObject:GetComponent(typeof(UnityEngine.UI
		.Toggle))

		self.accor_list[i].select_btn:AddValueChangedListener(BindTool.Bind(self.AccorBtnClickCallback, self, i))
	end
	self.accor_list[1].text_name.text.text = Language.Role.AlreadyHave
	self.accor_list[1].high_text_name.text.text = Language.Role.AlreadyHave
	self.accor_list[1].remind:SetActive(false)

	for i = 2, TOGGLE_MAX do
		self.accor_list[i].text_name.text.text = TitleWGData.Instance:GetTitleBtnNameByIndex(i)
		self.accor_list[i].high_text_name.text.text = TitleWGData.Instance:GetTitleBtnNameByIndex(i)
	end
end

function RoleBranchView:LoadCell(index) -- 加载左边list中的 cell
	local cell_data = TitleWGData.Instance:GetTitleListDataByType(index)
	if index == 1 then
		self.old_have_num = #cell_data
	end

	self.title_list_view[index] = {}
	local res_async_loader = AllocResAsyncLoader(self, "item_type" .. index)
	res_async_loader:Load("uis/view/chenghao_prefab", "item_type", nil, function(new_obj)
		for i = 1, #cell_data do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			if self.accor_list[index].list then
				obj_transform:SetParent(self.accor_list[index].list.transform, false)
				obj:GetComponent("Toggle").group = self.accor_list[index].list.toggle_group
				self.accor_list[index].list_item_cell[i] = obj
			end

			local item_cell = TitleListItem.New(obj)
			item_cell.parent_view = self
			item_cell:SetData(cell_data[i])
			self.title_list_view[index][i] = item_cell
			if cell_data[i].item_id ~= "" and cell_data[i].item_id > 0 and index ~= 1 then
				self.item_id_title_map[cell_data[i].item_id] = { item_cell = item_cell, parent_index = index,
					child_index = i }
			end

			if index ~= 1 then
				self.title_id_index_map[cell_data[i].title_id] = { parent_index = index, child_index = i }
			else
				self.has_get_title_id_index_map[cell_data[i].title_id] = { item_cell = item_cell, parent_index = index,
					child_index = i }
			end
		end

		--新改的
		table.insert(self.has_load_list, index)
		if #self.has_load_list >= TOGGLE_MAX then
			self.is_load_complete = true
			self:AccorBtnClickCallback(self.m_load_index or 1)
			self:FlushTitleModel()
			self:SetHL()
			self:InitFlushRemind()
		end
	end)
end

function RoleBranchView:ReleaseTitleCallBack()
	self.is_load_complete = nil
	self.accor_list = {}
	self.has_load_list = {}
	self.item_id_title_map = {}
	self.title_id_index_map = {}
	self.has_get_title_id_index_map = {}

	if self.title_attr_list then
		for k, v in pairs(self.title_attr_list) do
			v:DeleteMe()
		end
		self.title_attr_list = nil
	end

	if self.role_model then
		self.role_model:DeleteMe()
		self.role_model = nil
	end
	self.cur_child_index = nil

	if self.delay_calc_scroll_quest ~= nil then
		GlobalTimerQuest:CancelQuest(self.delay_calc_scroll_quest)
		self.delay_calc_scroll_quest = nil
	end
	if self.expiredtime_countdown_timer ~= nil then
		GlobalTimerQuest:CancelQuest(self.expiredtime_countdown_timer)
		self.expiredtime_countdown_timer = nil
	end

	if self.title_info_change then
		GlobalEventSystem:UnBind(self.title_info_change)
		self.title_info_change = nil
	end

	if nil ~= self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.tilte_item_cell then
		self.tilte_item_cell:DeleteMe()
		self.tilte_item_cell = nil
	end

	if self.title_list_view then
		for index = 1, #self.title_list_view do
			local tab = self.title_list_view[index]
			if tab then
				for k, v in pairs(tab) do
					v:DeleteMe()
				end
			end
		end
		self.title_list_view = {}
	end

	ItemWGData.Instance:UnNotifyDataChangeCallBack(self.title_item_change_call_back)
	self.title_sub_index = 1
	self.title_btn_index = 1
	self.change_title_id = 0

	self.fs_title_star_list = nil
	self.title_background_loader = nil
end

function RoleBranchView:OnItemTitleChangeCallBack(change_item_id, change_item_index, change_reason, put_reason, old_num,
												  new_num)
	if self.item_id_title_map[change_item_id] ~= nil and new_num and old_num and new_num > old_num then
		-- 	local map_value = self.item_id_title_map[change_item_id]
		-- 	map_value.item_cell:Flush()
		--        self:InitFlushRemind()

		self:InitFlushRemind()
		self:FlushTitleInfo()
	end
end

function RoleBranchView:InitFlushRemind()
	for i = 1, TOGGLE_MAX do
		local cell_data_list = TitleWGData.Instance:GetTitleListDataByType(i)
		if self.accor_list[i] then
			self.accor_list[i].remind:SetActive(false)
			for k, v in pairs(cell_data_list) do
				local item_num = ItemWGData.Instance:GetItemNumInBagById(v.item_id == "" and 0 or v.item_id) or 0
				local flag = TitleWGData.Instance:IsThisTitleActive(v.title_id)
				if flag then
					local is_can_up_level = TitleWGData.Instance:RoleTitleIsCanUpLevel(v.title_id)
					if is_can_up_level then
						self.accor_list[i].remind:SetActive(true)
						self:FlushCurCellList(i)
						break
					end
				end

				if not flag and item_num > 0 then
					self.accor_list[i].remind:SetActive(true)
					self:FlushCurCellList(i)
					break
				end
			end
		end
	end
end

function RoleBranchView:OnTitleInfoChange(new_list)
	if self.is_load_complete then
		if new_list then
			if #new_list ~= self.old_have_num then
				--重新加载 [可使用] 列表
				--隐藏 所有子物体
				local tab = self.title_list_view[1]
				if tab then
					for k, v in pairs(tab) do
						v.view.gameObject:SetActive(false)
					end
				end
				--是否需要实例化出来
				local num = #new_list > self.old_have_num and #new_list - self.old_have_num or 0
				if num > 0 then
					self:CreateChangeCell(self.old_have_num, #new_list, new_list)

					-- UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.accor_list[1].list.rect)
				else
					local cell_data_list = TitleWGData.Instance:GetTitleListDataByType(1)

					for k, v in pairs(new_list) do
						self.title_list_view[1][k].view.gameObject:SetActive(true)
						self.title_list_view[1][k]:SetData(cell_data_list[k])
					end
					local spacing_num = #new_list - 1
					spacing_num = spacing_num > 0 and spacing_num or 0
					-- self.accor_list[1].list.layout_element.preferredHeight = ListCellHeight * #new_list + Spacing * spacing_num + PaddingBottom
					self:SetPutOnHL(new_list)
				end
				self.old_have_num = #new_list

				--if tab then
				--	for k, v in pairs(tab) do
				--		v:SetToggleIsOn(false)
				--	end
				--	if tab[1] then
				--		tab[1]:SetToggleIsOn(true)
				--		self.accor_list[1].btn.accordion_element.isOn = true
				--	end
				--end
			end
		end
	end
end

function RoleBranchView:CreateChangeCell(old_num, new_num, new_list)
	local res_async_loader = AllocResAsyncLoader(self, "item_type")
	res_async_loader:Load("uis/view/chenghao_prefab", "item_type", nil, function(new_obj)
		for i = old_num + 1, new_num do
			local obj = ResMgr:Instantiate(new_obj)
			local obj_transform = obj.transform
			if self.accor_list[1].list then
				obj_transform:SetParent(self.accor_list[1].list.transform, false)
				obj:GetComponent("Toggle").group = self.accor_list[1].list.toggle_group
				self.accor_list[1].list_item_cell[i] = obj
			end
			local item_cell = TitleListItem.New(obj)
			item_cell.parent_view = self
			self.title_list_view[1][i] = item_cell
			local spacing_num = i - 1 > 0 and i - 1 or 0
			-- self.accor_list[1].list.layout_element.preferredHeight = self.accor_list[1].list.layout_element.preferredHeight + ListCellHeight + Spacing * spacing_num
			if i == new_num then
				self.change_cell_complete = true
				local cell_data = TitleWGData.Instance:GetTitleListDataByType(1)
				if cell_data then
					local tab = self.title_list_view[1]
					if tab then
						for k, v in pairs(tab) do
							v.view.gameObject:SetActive(true)
							v:SetData(cell_data[k])
							if cell_data[k] and cell_data[k].title_id and not self.has_get_title_id_index_map[cell_data[k].title_id] then
								self.has_get_title_id_index_map[cell_data[k].title_id] = { item_cell = v,
									parent_index = 1, child_index = k }
								self.change_title_id = cell_data[k].title_id
							end
						end
					end
				end

				-- self.accor_list[1].list.layout_element.preferredHeight = self.accor_list[1].list.layout_element.preferredHeight + PaddingBottom
				UnityEngine.UI.LayoutRebuilder.ForceRebuildLayoutImmediate(self.accor_list[1].list.rect)

				self:SetPutOnHL(new_list)
			end
		end
	end)
end

function RoleBranchView:FlushTitleModel()
	if nil == self.role_model then
		self.role_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["RoleTitleDisplay"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			-- offset_type = MODEL_OFFSET_TYPE.NORMALIZE,
			rt_scale_type = ModelRTSCaleType.M,
			can_drag = false,
		}
		
		self.role_model:SetRenderTexUI3DModel(display_data)
		self:AddUiRoleModel(self.role_model)
	end

	if self.role_model then
		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local ignore_table = {ignore_wing = true, ignore_jianzhen = true, ignore_halo = true}
		self.role_model:SetModelResInfo(role_vo, ignore_table)
		self.role_model:SetRTAdjustmentRootLocalPosition(0, -0.8, 0)
		self.role_model:SetRTAdjustmentRootLocalScale(0.8)
	end
end

function RoleBranchView:AccorBtnClickCallback(index)
	if not self.is_load_complete then
		self.m_load_index = index
		return
	end

	if self.accor_list[index].btn and self.accor_list[index].btn.accordion_element.isOn then --刷新
		local child_index = self.cur_child_index
		self.cur_child_index = nil
		-- 若有红点，优先选中红点
		local remind_count, title_id = TitleWGData.Instance:RoleTitleActRemindByType(index)
		if remind_count > 0 and title_id then
			local value_tab = self.title_id_index_map[title_id]
			if value_tab.parent_index == index then
				self:SetJumpParam(title_id)
				return
			end
		end

		local obj = self.accor_list[index].list_item_cell[child_index or 1]
		if obj ~= nil then
			if obj:GetComponent("Toggle").isOn == true then
				self.title_list_view[index][child_index or 1]:OnClickItem(true)
				return
			end

			obj:GetComponent("Toggle").isOn = true
		end
	end
end

function RoleBranchView:FlushCurCellList(index)
	local cell_data = TitleWGData.Instance:GetTitleListDataByType(index)
	if not cell_data then return end
	for i = 1, #cell_data do
		if self.title_list_view[index] and self.title_list_view[index][i] and cell_data[i] then
			self.title_list_view[index][i]:SetData(cell_data[i])
			self.title_id_index_map[cell_data[i].title_id] = { parent_index = index, child_index = i }
		end
	end
end

function RoleBranchView:OnFlushTitle()
	self:FlushLeftBar()
	self:InitFlushRemind()
	self:FlushTitleInfo()
end

function RoleBranchView:FlushLeftBar()
	if self.title_list_view then
		for index = 1, #self.title_list_view do
			local tab = self.title_list_view[index]
			if tab then
				for k, v in pairs(tab) do
					v:Flush()
				end
			end
		end
	end
end

--     刷新称号信息
function RoleBranchView:FlushTitleInfo()
	if not self.is_load_complete then
		return
	end

	local data = TitleWGData.Instance:GetCurClickData() -- 当前选中的数据
	if data == nil then
		return
	end

	self:FlushTitlePart()
	self:FlushTitleAttr()

	------是否激活-------
	local flag = TitleWGData.Instance:IsThisTitleActive(data.title_id)
	self.node_list.btn_title_wear:SetActive(flag)

	local time_txt = ""
	local save_info = TitleWGData.Instance:GetTitleSaveDataById(data.title_id)
	if save_info ~= nil then --以拥有的时限称号
		local re_time = save_info.expired_time - TimeWGCtrl.Instance:GetServerTime()
		time_txt = string.format(Language.Title.RemainTime,
			TimeUtil.FormatSecondDHM(re_time))
	else
		time_txt = string.format(Language.Title.RemainTime, data.time_show)
	end

	self.node_list.label_title_get_chengjiu.text.text = string.format("%s\n%s", data.desc, time_txt)

	if flag then
		local txt = ""
		local is_put_on = GameVoManager.Instance:GetMainRoleVo().used_title_list[1]
		if is_put_on == data.title_id then
			self.is_peidai = false
			txt = Language.Title.XieXia
		else
			self.is_peidai = true
			txt = Language.Title.PeiDai
		end

		self.node_list.ch_txt.text.text = txt
	end

	local item_num = ItemWGData.Instance:GetItemNumInBagById(data.item_id == "" and 0 or data.item_id) or 0
	self.node_list.btn_no_active:SetActive(not flag and item_num <= 0)
	self.node_list.btn_title_active:SetActive(not flag and item_num > 0)

	--激活后判断能不能升级
	local cur_title_level_cfg
	local next_title_level_cfg
	local title_item_id = 0
	local title_level = 0
	local capability = 0
	if flag then
		title_level = TitleWGData.Instance:GetTitleLevelByTitleId(data.title_id)
		cur_title_level_cfg = TitleWGData.Instance:GetTitleLevelCfg(data.title_id, title_level)
		next_title_level_cfg = TitleWGData.Instance:GetTitleLevelCfg(data.title_id, title_level + 1)
		if cur_title_level_cfg then
			title_item_id = cur_title_level_cfg.stuff_id
			capability = TitleWGData.GetTitleCapability(cur_title_level_cfg)
		else
			title_item_id = data.item_id
			capability = TitleWGData.GetTitleCapabilityByID(data.title_id)
		end
	else
		title_item_id = data.item_id
		capability = TitleWGData.GetTitleCapabilityByID(data.title_id)
	end

	self.node_list["power_text"].text.text = capability

	self.tilte_item_cell:SetData({ item_id = title_item_id })
	local is_can_up_level = not IsEmptyTable(cur_title_level_cfg)
	local is_max_level = IsEmptyTable(next_title_level_cfg)

	self.node_list.is_title_active:SetActive(flag and not is_can_up_level)
	self.node_list.btn_title_up_level:SetActive(flag and is_can_up_level and (not is_max_level))
	self.node_list.is_title_max_level:SetActive(flag and is_can_up_level and is_max_level)
	self.node_list.fs_title_stars_list:SetActive(flag and is_can_up_level)
	local up_level_red = TitleWGData.Instance:RoleTitleIsCanUpLevel(data.title_id)
	self.node_list.up_level_remind:SetActive(flag and up_level_red)

	-- 升级
	if flag and is_can_up_level then
		local star_res_list = GetStarImgResByStar(title_level)
		for k, v in pairs(self.fs_title_star_list) do
			v.image:LoadSprite(ResPath.GetCommonImages(star_res_list[k]))
		end
	end

	if flag and is_can_up_level and not is_max_level then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(title_item_id)
		local stuff_count = cur_title_level_cfg.stuff_num
		local color = item_num >= stuff_count and COLOR3B.D_GREEN or COLOR3B.D_RED
		self.tilte_item_cell:SetRightBottomTextVisible(true)
		self.tilte_item_cell:SetRightBottomColorText(item_num .. '/' .. stuff_count, color)
	else
		self.tilte_item_cell:SetRightBottomTextVisible(false)
	end
end

function RoleBranchView:FlushTitleAttr()
	local info = TitleWGData.Instance:GetCurClickData() -- 当前选中的数据
	if not info then return end

	local cur_title_level_cfg
	local next_title_level_cfg
	local title_level = TitleWGData.Instance:GetTitleLevelByTitleId(info.title_id) or 0
	cur_title_level_cfg = TitleWGData.Instance:GetTitleLevelCfg(info.title_id, title_level)
	next_title_level_cfg = TitleWGData.Instance:GetTitleLevelCfg(info.title_id, title_level + 1)

	local level_attr_list = {}
	if cur_title_level_cfg then
		level_attr_list = EquipWGData.GetSortAttrListHaveNextByCfg(cur_title_level_cfg, next_title_level_cfg)
	end

	-- 属性
	for k, v in ipairs(self.title_attr_list) do
		v:SetData(level_attr_list[k])
		v:SetRealHideNext(true)
	end
end

------------ 点击产品回调----------------------
function RoleBranchView:OnClickProductHandler(data)
	TitleWGData.Instance:SetCurClickData(data)
	--执行刷新逻辑
	self:FlushTitleInfo()
end

function RoleBranchView:FlushTitlePart()
	local data = TitleWGData.Instance:GetCurClickData() -- 当前选中的数据
	if data == nil then
		return
	end

	local title_id = data.title_id
	self:FlushTitleExpiredtime()

	local b, a = ResPath.GetTitleModel(title_id)
	self.node_list["image_title"]:ChangeAsset(b, a, false, BindTool.Bind(self.SetDiyTitleNameText, self))
end

function RoleBranchView:SetDiyTitleNameText(obj)
	if IsNil(obj) then
		return
	end

	local data = TitleWGData.Instance:GetCurClickData() -- 当前选中的数据
	if data == nil then
		return
	end

	local title_id = data.title_id
	local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(title_id)
	if not diy_cfg then
		return
	end

	local text_obj = obj.gameObject.transform:Find("Text")
	if text_obj == nil then
		return
	end

	local title_text = text_obj.gameObject:GetComponent(typeof(TMPro.TextMeshProUGUI))
	local title_name = TitleWGData.Instance:GetDiyTitleName(title_id)
	if title_name and title_name ~= "" then
		title_text.text = title_name
	else
		title_text.text = data.name
	end
end

--   刷新倒计时
function RoleBranchView:FlushTitleExpiredtime()
	if self.node_list["lbl_title_expiredtime"] == nil then
		return
	end

	local server_time = TimeWGCtrl.Instance:GetServerTime()
	if self.title_expiredtime ~= nil and self.title_expiredtime ~= 0 and self.title_expiredtime >= server_time then
		self.node_list["lbl_title_expiredtime"].text.text = string.format(Language.Role.TitleExpiredTime,
			TimeUtil.FormatSecond(self.title_expiredtime - server_time, 1))
	else
		self.node_list["lbl_title_expiredtime"].text.text = ""
	end
end

--点击保存称号
function RoleBranchView:OnClickSaveTitle()
	local cur_data = TitleWGData.Instance:GetCurClickData() -- 获取当前选中的称号数据
	if cur_data == nil then
		cur_data = TitleWGData.Instance:GetShowTitleCfgList()[1]
	end
	local flag = TitleWGData.Instance:IsThisTitleActive(cur_data.title_id)
	if not flag then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.TitleNotActivate)
		return
	end

	if self.is_peidai == true then
		TitleWGCtrl.Instance:SendUseTitleReq({ cur_data.title_id })
	else
		-- print_error("RoleBranchView -> 卸下按钮点击",cur_data.title_id)
		TitleWGCtrl.Instance:SendUnUseTitleReq({ cur_data.title_id })
	end
end

function RoleBranchView:OnClickTitleActiveBtn()
	local cur_data = TitleWGData.Instance:GetCurClickData() -- 获取当前选中的称号数据
	if cur_data == nil then
		cur_data = TitleWGData.Instance:GetShowTitleCfgList()[1]
	end

	local flag = TitleWGData.Instance:IsThisTitleActive(cur_data.title_id)
	if flag and cur_data.item_id == "" then
		return
	end

	local diy_cfg = TitleWGData.Instance:GetDiyTitleCfg(cur_data.title_id)
	if diy_cfg then
		TitleWGCtrl.Instance:OpenDiyTitleNameView(cur_data.title_id)
	else
		local bag_index = ItemWGData.Instance:GetItemIndex(cur_data.item_id)
		BagWGCtrl.Instance:SendUseItem(bag_index, 1, 0, 0)
	end
end

function RoleBranchView:OnClickTitleUpLevelBtn()
	local cur_data = TitleWGData.Instance:GetCurClickData() -- 获取当前选中的称号数据
	if cur_data == nil then
		cur_data = TitleWGData.Instance:GetShowTitleCfgList()[1]
	end

	local flag = TitleWGData.Instance:IsThisTitleActive(cur_data.title_id)
	if not flag and cur_data.item_id == "" then
		return
	end

	local title_level = TitleWGData.Instance:GetTitleLevelByTitleId(cur_data.title_id)
	local cur_title_level_cfg = TitleWGData.Instance:GetTitleLevelCfg(cur_data.title_id, title_level)
	local next_title_level_cfg = TitleWGData.Instance:GetTitleLevelCfg(cur_data.title_id, title_level + 1)
	if cur_title_level_cfg and next_title_level_cfg then
		local item_num = ItemWGData.Instance:GetItemNumInBagById(cur_title_level_cfg.stuff_id)
		local stuff_count = cur_title_level_cfg.stuff_num
		if item_num < stuff_count then
			TipWGCtrl.Instance:OpenItemTipGetWay({ item_id = cur_title_level_cfg.stuff_id })
		else
			TitleWGCtrl.Instance:SendUpLevelTitleReq({ cur_data.title_id })
		end
	end
end

function RoleBranchView:PlayUpLevelEffect()
	if self.node_list["level_effct"] then
		TipWGCtrl.Instance:ShowEffect({
			effect_type = UIEffectName.s_shengxing,
			is_success = true,
			pos = Vector2(0, 0),
			parent_node = self.node_list["level_effct"]
		})
	end
end

--点击称号总属性
function RoleBranchView:OnClickTitleAddition()
	local data_id_list = TitleWGData.Instance:GetTitleIdList() -- 激活列表
	if #data_id_list <= 0 then
		SysMsgWGCtrl.Instance:ErrorRemind(Language.Role.TitleErrorInfo)
		return
	end
	TitleWGCtrl.Instance:OnOpenTitleTips()
end

TitleListItem = TitleListItem or BaseClass(BaseRender)
function TitleListItem:LoadCallBack()
	self.view.toggle:AddValueChangedListener(BindTool.Bind(self.OnClickItem, self))
end

function TitleListItem:__delete()
	self.parent_view = nil
end

function TitleListItem:SetToggleIsOn(value)
	self.view.toggle.isOn = value
end

function TitleListItem:OnClickItem(is_click)
	if is_click then
		self.parent_view:OnClickProductHandler(self.data)
	end
end

function TitleListItem:OnFlush()
	if not self.data then
		return
	end

	local title_id = self.data.title_id
	local diy_name = TitleWGData.Instance:GetDiyTitleName(title_id)
	local name = diy_name or self.data.name

	self.node_list.normal_text.text.text = name
	self.node_list.select_text.text.text = name
	local item_num = ItemWGData.Instance:GetItemNumInBagById(self.data.item_id == "" and 0 or self.data.item_id) or 0
	local is_put_on = GameVoManager.Instance:GetMainRoleVo().used_title_list[1]
	local up_level_red = TitleWGData.Instance:RoleTitleIsCanUpLevel(title_id)
	local flag = TitleWGData.Instance:IsThisTitleActive(title_id)
	self.node_list["remind"]:SetActive((not flag and item_num > 0) or (flag and up_level_red))
	self.node_list["put_on_flag"]:SetActive(is_put_on == title_id)
	self.node_list["act_flag"]:SetActive(flag and is_put_on ~= title_id)
	self.node_list.lock:SetActive(not flag)
end
