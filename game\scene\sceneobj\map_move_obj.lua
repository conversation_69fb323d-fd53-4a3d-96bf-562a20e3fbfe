
MapMoveObj = MapMoveObj or BaseClass()

-- 地图移动对象，包括视野外的对象
function MapMoveObj:__init(vo)
	self.vo = vo
end

function MapMoveObj:__delete()
	GameVoManager.Instance:DeleteVo(self.vo)
	self.vo = nil
end

function MapMoveObj:GetVo()
	return self.vo
end

function MapMoveObj:GetLogicPos()
	return self.vo.pos_x, self.vo.pos_y
end

local MapMoveObjList = {}
function MapMoveObj.CreateMapMoveObj(vo)
	local map_obj = table.remove(MapMoveObjList)
	if nil == map_obj then
		map_obj = MapMoveObj.New(vo)
	else
		map_obj.vo = vo
	end

	return map_obj
end

function MapMoveObj.Release(map_obj)
	if #MapMoveObjList <= 1000 then
		local vo = map_obj.vo
		map_obj.vo = nil
		GameVoManager.Instance:DeleteVo(vo)
		table.insert(MapMoveObjList, map_obj)
	else
		map_obj:DeleteMe()
	end
end