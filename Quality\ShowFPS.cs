﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using UnityEngine;
using UnityEngine.Profiling;
using UnityEngine.UI;

public class ShowFPS : MonoBehaviour
{
	public float fpsMeasuringDelta = 1f;
	private static bool swich = false;
	private float timePassed;
	private int m_FrameCount = 0;
	private float m_FPS = 0.0f;
	private Rect rect1 = new Rect((Screen.width / 2) - 300, 0, 100, 100);
	private Rect rect2 = new Rect((Screen.width / 2) - 170, 0, 100, 100);
	private Rect rect3 = new Rect((Screen.width / 2) + 20, 0, 100, 100);
	GUIStyle lua;
	GUIStyle memory;
	GUIStyle bb;
	public Text text;
	string s;
	string format = "fps:{0} AllReserved:{1} Allocated:{2} Mono:{3} MonoReserved:{4} lua: {5} dc:{6}";
	//string format = "lua:{0} dc:{1}";
	long totalReserver = 0;
    long totalAllocated = 0;
    long totalUnused = 0;
    long luamemory = 0;
    long mono = 0;
    long monoReserver = 0;
    int drawcall = 0;

    private void Start()
	{
		timePassed = 0.0f;
	}

	private void Update()
	{
        if (!RuntimeGUIMgr.Instance.IsAndroidGM() && (RuntimeGUIMgr.Instance.IsGUIOpening() || Debug.isDebugBuild  || swich))
		{
			m_FrameCount = m_FrameCount + 1;
			timePassed = timePassed + Time.deltaTime;

			if (timePassed > fpsMeasuringDelta)
			{
				m_FPS = (int)Math.Floor((m_FrameCount / timePassed));
				timePassed = 0.0f;
				m_FrameCount = 0;
                totalReserver = Profiler.GetTotalReservedMemoryLong() / 1024 / 1024;
                totalAllocated = Profiler.GetTotalAllocatedMemoryLong() / 1024 / 1024;
                totalUnused = Profiler.GetTotalUnusedReservedMemoryLong() / 1024 / 1024;
                luamemory = (int)(GameRoot.Instance.Collectgarbage("count") / 1024);
                mono = Profiler.GetMonoUsedSizeLong() / 1024 / 1024;
                monoReserver = Profiler.GetMonoHeapSizeLong() / 1024 / 1024;
#if UNITY_EDITOR
                drawcall = UnityEditor.UnityStats.batches;
                s = string.Format(format, m_FPS, totalReserver, totalAllocated, mono, monoReserver, luamemory, drawcall);
#else
                s = string.Format(format, m_FPS, totalReserver, totalAllocated, mono, monoReserver, luamemory, "无法获取");
#endif
                text.text = s;
			}
		}
		else
		{
            text.text = string.Empty;
            return;
		}
	}

	private void OnGUI()
	{
		
	}

	public static void SetSwich(string on)
	{
		Debug.Log(on);
		if(on == "true")
		{
			swich = true;
		}
		else
		{
			swich = false;
		}
	}
}

