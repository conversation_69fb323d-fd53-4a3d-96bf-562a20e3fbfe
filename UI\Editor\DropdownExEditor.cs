﻿using UnityEditor;
using UnityEditor.UI;


/// <summary>
/// 此编辑器脚本：用于在层次面板中添加一个可控属性AlwaysCallback
/// </summary>
[CustomEditor(typeof(DropdownEx), true)]
[CanEditMultipleObjects]
public class DropdownExEditor : DropdownEditor
{
    SerializedProperty activeGameObject;
    SerializedProperty inactiveGameObject;


    protected override void OnEnable()
    {
        base.OnEnable();
        activeGameObject = serializedObject.FindProperty("activeGameObject");
        inactiveGameObject = serializedObject.FindProperty("inactiveGameObject");
    }


    public override void OnInspectorGUI()
    {
        base.OnInspectorGUI();
        EditorGUILayout.PropertyField(activeGameObject, true);
        EditorGUILayout.PropertyField(inactiveGameObject, true);
        serializedObject.ApplyModifiedProperties();
    }
}