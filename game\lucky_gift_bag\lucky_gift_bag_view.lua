LuckyGiftBagView = LuckyGiftBagView or BaseClass(SafeBaseView)

local grade_reward_item_width = 110

function LuckyGiftBagView:__init()
	self.view_style = ViewStyle.Half
	self:SetMaskBg(false)
	self:AddViewResource(0, "uis/view/lucky_gift_bag_ui_prefab", "lucky_gift_bag_view")
end

function LuckyGiftBagView:ReleaseCallBack()
	self:CleanRefreshTime()

	if self.lucky_gift_bag_list then
		self.lucky_gift_bag_list:DeleteMe()
		self.lucky_gift_bag_list = nil 
	end

	if self.reward_item then
		self.reward_item:DeleteMe()
		self.reward_item = nil
	end

	if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
    end

	if self.lucky_grade_reward_list then
		self.lucky_grade_reward_list:DeleteMe()
		self.lucky_grade_reward_list = nil
	end

	if self.final_reward_item then
		self.final_reward_item:DeleteMe()
		self.final_reward_item = nil 
	end
end

function LuckyGiftBagView:OpenCallBack()
	LuckyGiftBagWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_INFO)
	LuckyGiftBagWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_BASE_INFO)
end

function LuckyGiftBagView:ShowIndexCallBack()
	self:InitTweenRoot()
	self:DoViewTween()
end

function LuckyGiftBagView:InitTweenRoot()
    RectTransform.SetAnchoredPositionXY(self.node_list.right_tween_root.rect, 1000, 0)
    RectTransform.SetAnchoredPositionXY(self.node_list.reward_tween_root.rect, 0, -300)
	self.node_list.btn_canvas_group.canvas_group.alpha = 0
	self.node_list.capability.canvas_group.alpha = 0
end

function LuckyGiftBagView:DoViewTween()
	local tween_info = UITween_CONSTS.LuckyGiftBag

	local delay_func = function()
		self.node_list.btn_canvas_group.canvas_group:DoAlpha(0, 1, tween_info.canvas_group_show)
		self.node_list.capability.canvas_group:DoAlpha(0, 1, tween_info.canvas_group_show)
	end

	self.node_list.reward_tween_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.MoveTime)
    local right_tween =  self.node_list.right_tween_root.rect:DOAnchorPos(Vector2(0, 0), tween_info.MoveTime)
	right_tween:OnComplete(delay_func)
end

function LuckyGiftBagView:LoadCallBack()
	if nil == self.model_display then
		self.model_display = OperationActRender.New(self.node_list.model)
		self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if nil == self.lucky_gift_bag_list then
		self.lucky_gift_bag_list = AsyncListView.New(LuckyGiftBagItemRender, self.node_list.lucky_gift_bag_list)
	end

	if nil == self.lucky_grade_reward_list then
		self.lucky_grade_reward_list = AsyncListView.New(LuckyGradeRewardRender, self.node_list.reward_item_list)
		self.node_list.reward_item_list.scroll_rect.onValueChanged:AddListener(BindTool.Bind(self.OnScrollRectValueChange, self))
	end

	if nil == self.reward_item then
		self.reward_item = ItemCell.New(self.node_list.reward_item_pos)
	end

	self:FlushActiveRefreshTime()
	XUI.AddClickEventListener(self.node_list.btn_record, BindTool.Bind(self.OnRecordBtnClick, self))
	XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind(self.OnClickTipsBtn, self))
	XUI.AddClickEventListener(self.node_list.daily_reward_btn, BindTool.Bind(self.OnDailyRewardBtnClick, self))
	-- 2021.12.22 策划觉得这个功能影响大佬留存，暂时屏蔽入口，功能保留
 	-- XUI.AddClickEventListener(self.node_list.btn_luckyvlaue_rank, BindTool.Bind(self.OnLuckyValueRankBtnClick, self))
	 XUI.AddClickEventListener(self.node_list.btn_send_lucky_value, BindTool.Bind(self.OnSendLuckyValueBtnClick, self))
	 XUI.AddClickEventListener(self.node_list.btn_through_train, BindTool.Bind(self.OnThroughTrainBtnClick, self))
	 XUI.AddClickEventListener(self.node_list.btn_super_double, BindTool.Bind(self.OnSuperDoubleBtnClick, self))
	 XUI.AddClickEventListener(self.node_list.btn_hatch_fight_mount, BindTool.Bind(self.OnHatchFightMount, self))

	 local super_double_state = LuckyGiftBagWgData.Instance:GetOtherCfg().is_show_lucky_double == 1
	 self.node_list.btn_super_double:SetActive(super_double_state)
end

function LuckyGiftBagView:OnFlush(param_t)
	local model_data = LuckyGiftBagWgData.Instance:GetCurrentRateShow()
	if not IsEmptyTable(model_data) and model_data.model_show_itemid then
		local capability = ItemShowWGData.CalculateCapability(model_data.model_show_itemid, true)
		self.node_list.cap_value.text.text = capability
		if self.reward_item then
			self.reward_item:SetData({item_id = model_data.model_show_itemid, num = 1, is_bind = 1})
		end
	end

	local get_rate_count, rate_count =  LuckyGiftBagWgData.Instance:GetRewardProgress()
	if get_rate_count and rate_count then
		self.node_list.reward_item_flag:SetActive(rate_count <= get_rate_count)
		self.node_list.reward_progress_text.text.text = string.format(Language.LuckyGiftBag.LuckyGiftBagRewardProgress, rate_count - get_rate_count, rate_count)
	end

	local reward_pool_data = LuckyGiftBagWgData.Instance:GetRewardPoolInfo()
	if not IsEmptyTable(reward_pool_data) then
		self.lucky_gift_bag_list:SetDataList(reward_pool_data)
	end

	self:FlushModel()

	local lucky_value = LuckyGiftBagWgData.Instance:GetLickyValue()
	self.node_list.lucky_value.text.text = lucky_value --string.format(Language.LuckyGiftBag.MyLuckyValue, ToColorStr(lucky_value, COLOR3B.GREEN))
	self.node_list.daily_reward_remind:SetActive(LuckyGiftBagWgData.Instance:GetDailyRewardRemind() ~= 0)
	self.node_list.btn_hatch_fight_mount:SetActive(model_data.show_fight_icon ~= 0)

	local data_list, start_reward, final_reward_data, slider_value = LuckyGiftBagWgData.Instance:GetLuckyGradeRewardInfo()
	self.lucky_grade_reward_list:SetDataList(data_list)
	self:FlushFinalLuckyGradeReward(final_reward_data)
	self.node_list.slider.slider.value = slider_value
end

function LuckyGiftBagView:FlushActiveRefreshTime()
	local sever_time = TimeWGCtrl.Instance:GetServerTime()
	local refresh_time = TimeWGCtrl.Instance:NowDayTimeEnd(sever_time)- sever_time
	local interval = 1
	self:CleanRefreshTime()
	self.refresh_time = CountDown.Instance:AddCountDown(refresh_time, interval,
	function(elapse_time, total_time)
		local refresh_time = total_time - elapse_time
		if self.node_list.lucky_value_reset_time then
			self.node_list.lucky_value_reset_time.text.text = Language.LuckyGiftBag.LuckyValueRefreshTime .. ToColorStr(TimeUtil.FormatSecondDHM2(refresh_time), COLOR3B.D_GREEN)
		end
	end,
	function()
	end
)
end

function LuckyGiftBagView:CleanRefreshTime()
    if self.refresh_time and CountDown.Instance:HasCountDown(self.refresh_time) then
        CountDown.Instance:RemoveCountDown(self.refresh_time)
        self.refresh_time = nil
    end
end

function LuckyGiftBagView:FlushModel()
	--local model_data = LuckyGiftBagWgData.Instance:GetOtherCfg()
	local model_data = LuckyGiftBagWgData.Instance:GetCurrentRateShow()
	if not IsEmptyTable(model_data) then
		local display_data = {}
		display_data.should_ani = true
		display_data.bundle_name = model_data.model_bundle_name
		display_data.asset_name = model_data.model_asset_name
		display_data.render_type = model_data.model_show_type - 1
		display_data.item_id = model_data.model_show_itemid
		self.model_display:SetData(display_data)

		--self.node_list.img_skill:SetActive(model_data.show_icon == 1)
	end
end

-- 刷新最终奖励
function LuckyGiftBagView:FlushFinalLuckyGradeReward(data_info)
	if IsEmptyTable(data_info) then
		return
	end

	-- 配置表中未标识那个是大奖，策划说拿最后一个档次
	data_info.is_final_reward = true
	if not self.final_reward_item then
		local bundle = "uis/view/lucky_gift_bag_ui_prefab"
		local asset = "reward_item"
		local final_reward_async_loader = AllocResAsyncLoader(self, "final_reward_async_loader")
		final_reward_async_loader:Load(bundle, asset, nil, function (new_obj)
			local obj = ResMgr:Instantiate(new_obj)
			obj.transform:SetParent(self.node_list.final_reward_item_pos.transform, false)
			obj.transform.localPosition = Vector3.zero
			self.final_reward_item = LuckyGradeRewardRender.New(obj)
			-- 会出现没加载完赋值没刷新情况
			self.final_reward_item:SetData(data_info)
		end)
	else
		self.final_reward_item:SetData(data_info)
	end
end

function LuckyGiftBagView:OnScrollRectValueChange()
	local rect = self.node_list.reward_item_list.scroll_rect.content
	self.node_list.slider.rect.sizeDelta = Vector2(rect.rect.size.x + grade_reward_item_width / 2, 10)
	local pos_x =  RectTransform.GetAnchoredPositionXY(self.node_list.reward_item_list.scroll_rect.content)
	RectTransform.SetAnchoredPositionXY(self.node_list.slider.rect, pos_x, 11)
end

function LuckyGiftBagView:OnRecordBtnClick()
	-- LuckyGiftBagWgCtrl.Instance:OpenLuckyGiftBagRecordView()
    local data_list = LuckyGiftBagWgData.Instance:GetRateRecordList()
	local tips_data_list = {}
    if not IsEmptyTable(data_list) then
		for i, v in ipairs(data_list) do
			local temp_data = {}
			temp_data.item_data = {}
			temp_data.item_data.item_id = v.item_id
			temp_data.item_data.num = v.num
			temp_data.item_data.is_bind = v.is_bind
			temp_data.consume_time = v.timestamp
			temp_data.role_name = v.name
			table.insert(tips_data_list, temp_data)
		end
    end

	TipWGCtrl.Instance:OpenTipsRewardRecordView(tips_data_list)
end

function LuckyGiftBagView:OnDailyRewardBtnClick()
	if LuckyGiftBagWgData.Instance:GetDailyRewardRemind() == 0 then
		TipWGCtrl.Instance:ShowSystemMsg(Language.LuckyGiftBag.DailyRewardHasGet)
	else
		LuckyGiftBagWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_RECEIVE_DAILY_REWARD)
	end
end

function LuckyGiftBagView:OnSendLuckyValueBtnClick()
	LuckyGiftBagWgCtrl.Instance:OpenSendLuckyValueView()
end

function LuckyGiftBagView:OnThroughTrainBtnClick()
	LuckyGiftBagWgCtrl.Instance:OpenThroughTrainView()
end

function LuckyGiftBagView:OnClickTipsBtn()
	local role_tip = RuleTip.Instance
	role_tip:SetTitle(Language.LuckyGiftBag.RuleTitle)
	role_tip:SetContent(Language.LuckyGiftBag.RuleDesc)
end

function LuckyGiftBagView:OnLuckyValueRankBtnClick()
	-- 策划说打开时请求下就好，没必要实时显示
	LuckyGiftBagWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_RANK_INFO)
	LuckyGiftBagWgCtrl.Instance:OpenRankView()
end

function LuckyGiftBagView:OnSuperDoubleBtnClick()
	LuckyGiftBagWgCtrl.Instance:OpenSuperDoubleView()
end

function LuckyGiftBagView:OnHatchFightMount()
	ViewManager.Instance:Open(GuideModuleName.NewFightMountView)
end
--------------------------------------------幸运值购买挡位信息-----------------------------------------------
LuckyGiftBagItemRender = LuckyGiftBagItemRender or BaseClass(BaseRender)
function LuckyGiftBagItemRender:__init()
end

function LuckyGiftBagItemRender:__delete()
	if self.reward_item_list then
		self.reward_item_list:DeleteMe()
		self.reward_item_list = nil
	end
end

function LuckyGiftBagItemRender:LoadCallBack()
	if nil == self.reward_item_list then
		self.reward_item_list = AsyncListView.New(ItemCell, self.node_list.reward_item_list)
	end

	self.node_list.reward_explain.text.text = Language.LuckyGiftBag.LuckyGiftBagItemExplain
	XUI.AddClickEventListener(self.node_list.bug_reward_btn, BindTool.Bind(self.OnBugRewardBtnClick, self))
end

function LuckyGiftBagItemRender:OnFlush()
	if not IsEmptyTable(self.data) then
		self.node_list.reward_name.text.text = self.data[1].gift_name
		local item_data = LuckyGiftBagWgData.Instance:GetRewardPoolItemDataList(self.data[1].grade, self.data[1].seq)
		self.reward_item_list:SetDataList(item_data)
		local info_data = LuckyGiftBagWgData.Instance:GetGradeCfgInfo(self.data[1].grade, self.data[1].seq)
		--默认拿第一挡位购买信息
		local prchase_mode = GIFT_PRCHASE_OPERATE_TYPE.PRCHASE_MODE_ONE
		local add_lucky_value = info_data[prchase_mode][1].add_lucky * LuckyGiftBagWgData.Instance:GetCurrentLuckyDoubleAddPer()
		self.node_list.add_lucky.text.text = string.format(Language.LuckyGiftBag.LuckyValueAdd, add_lucky_value)
		self.node_list.bug_reward_btn_text.text.text = info_data[prchase_mode][1].need_cost
	end
end

function LuckyGiftBagItemRender:OnBugRewardBtnClick()
	local grade, seq, gift_name = self.data[1].grade, self.data[1].seq, self.data[1].gift_name
	LuckyGiftBagWgCtrl.Instance:OpenComfirmView(grade, seq, gift_name)
end

-----------------------------------------------进度条奖励信息------------------------------------------------------
LuckyGradeRewardRender = LuckyGradeRewardRender or BaseClass(BaseRender)
function LuckyGradeRewardRender:__init()
end

function LuckyGradeRewardRender:__delete()
end

function LuckyGradeRewardRender:LoadCallBack()
	if not self.item_cell then
		self.item_cell = ItemCell.New(self.node_list.item_cell_pos)
		self.item_cell:SetIsShowTips(false)
	end
	self.item_cell:AddClickEventListener(BindTool.Bind1(self.OnClick, self))
end

function LuckyGradeRewardRender:ReleaseCallBack()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil 
	end
end

function LuckyGradeRewardRender:OnFlush()
	if IsEmptyTable(self.data) then
		return
	end
	local reward_info = self.data.reward_info
	self.node_list.value.text.text = reward_info.need_lucky
	self.item_cell:SetData({item_id = reward_info.item.item_id})

	local state = self:GetGradeRewardState()
	self.node_list.lingqu_flag:SetActive(state == REWARD_STATE_TYPE.FINISH)
	self.node_list.kuang:SetActive(self.data.is_final_reward)
	self.node_list.can_get_flag:SetActive(state == REWARD_STATE_TYPE.CAN_FETCH)
end

function LuckyGradeRewardRender:GetGradeRewardState()
	local lucky_value = LuckyGiftBagWgData.Instance:GetLickyValue()
	local need_lucky_value = self.data.reward_info.need_lucky

	if self.data.reward_flag == 0 and lucky_value >= need_lucky_value then
		return REWARD_STATE_TYPE.CAN_FETCH
	elseif self.data.reward_flag == 1 then
		return REWARD_STATE_TYPE.FINISH
	else
		return REWARD_STATE_TYPE.UNDONE
	end
end

function LuckyGradeRewardRender:OnClick(cell)
		if not IsEmptyTable(self.data) then
			local lucky_value = LuckyGiftBagWgData.Instance:GetLickyValue()
			local reward_info = self.data.reward_info
			local need_lucky_value = reward_info.need_lucky
			if self:GetGradeRewardState() == REWARD_STATE_TYPE.CAN_FETCH then
				LuckyGiftBagWgCtrl.Instance:SendLuckyGiftBagReq(GIFT_OPERATE_TYPE.CROSS_LUCKY_GIFT_OPERATE_TYPE_LUCKY_GRADE_REWARD, reward_info.seq)
			else
				TipWGCtrl.Instance:OpenItem({item_id = reward_info.item.item_id}, ItemTip.FROM_NORMAL)
			end
		end
end