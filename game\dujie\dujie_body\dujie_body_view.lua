DujieBodyView = DujieBodyView or BaseClass(SafeBaseView)

local TALENT_SIZE_WIDTH = 108 + 59
local TALENT_SIZE_HEIGHT = 108 + 28
local WIDTH_SPACE = 59
local HEIGHT_SPACE = 28
local OFFSET_WIDTH = 192
local OFFSET_HEIGHT = 135
local ITEM_HALF_WIDTH = 54
local ITEM_HALF_HEIGHT = 54

function DujieBodyView:__init()
	self.view_style = ViewStyle.Full
    self.is_safe_area_adapter = true

    self:SetMaskBg(false, true)
    local common_bundle = "uis/view/common_panel_prefab"
    local view_bundle = "uis/view/dujie_body_ui_prefab"
    self:AddViewResource(0, common_bundle, "layout_a3_common_panel")
    self:AddViewResource(0, view_bundle, "layout_dujie_body_view")
    self:AddViewResource(0, common_bundle, "layout_a3_light_common_top_panel")
end

function DujieBodyView:LoadCallBack()

    local bundle, asset = ResPath.GetRawImagesJPG("a3_dz_b2")
    self.node_list.RawImage_tongyong.raw_image:LoadSprite(bundle, asset, function ()
        self.node_list["RawImage_tongyong"].raw_image:SetNativeSize()
    end)

    
    self.cur_body_seq = -1

    -- 创建货币栏
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
            show_custom_1 = true,
		    custom_item_id_1 = DujieWGData.Instance:GetOtherCfg("body_item_1"),
		    custom_num_1 = DujieWGData.Instance:GetMoneyNum(1),
            show_custom_2 = true,
		    custom_item_id_2 = DujieWGData.Instance:GetOtherCfg("body_item_2"),
		    custom_num_2 = DujieWGData.Instance:GetMoneyNum(2),
            show_custom_3 = true,
		    custom_item_id_3 = DujieWGData.Instance:GetOtherCfg("body_item_3"),
		    custom_num_3 = DujieWGData.Instance:GetMoneyNum(3),
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
       
    self:CreateTalentCellList()

    -- 灵根列表
    if not self.body_list then
		self.body_list = AsyncListView.New(DujieBodyItemRender, self.node_list.body_list)
        self.body_list:SetStartZeroIndex(true)
        self.body_list:SetSelectCallBack(BindTool.Bind(self.OnSelectBodyList, self))
	end

    -- 技能列表
    if not self.talent_skill_list then
        self.talent_skill_list = AsyncFancyGridView.New(BodyTalentSkillItemRender, self.node_list.talent_skill_list)
        -- 设置回调
        self.talent_skill_list:SetSelectCallBack(BindTool.Bind(self.OnClckTalentSkill, self))
	end

    -- 灵根总属性
    if not self.body_attr_list then
		self.body_attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.body_attr_list)
	end
    -- 天赋属性
    if not self.talent_attr_list then
		self.talent_attr_list = AsyncListView.New(CommonAddAttrRender, self.node_list.talent_attr_list)
	end
    -- 灵根激活消耗
    if self.active_body_cost_cell == nil then
		self.active_body_cost_cell = ItemCell.New(self.node_list.active_body_cell_node)
        self.active_body_cost_cell:SetShowCualityBg(false)
		self.active_body_cost_cell:NeedDefaultEff(false)
        self.active_body_cost_cell:SetCellBgEnabled(false)
	end

    -- 底部天赋图标
    if self.bottom_talent_item == nil then
        self.bottom_talent_item = DujieTalentItemRender.New(self.node_list.bottom_talent_item)
    end

    -- 天赋注灵消耗 
    if self.active_talent_cost_cell == nil then
		self.active_talent_cost_cell = ItemCell.New(self.node_list.item_node)
        self.active_talent_cost_cell:SetShowCualityBg(false)
		self.active_talent_cost_cell:NeedDefaultEff(false)
        self.active_talent_cost_cell:SetCellBgEnabled(false)
	end

    
    XUI.AddClickEventListener(self.node_list.btn_active_body, BindTool.Bind(self.OnClickBtnActiveBody, self))
    XUI.AddClickEventListener(self.node_list.btn_talent_active, BindTool.Bind(self.OnClickBtnActiveTalent, self))
    XUI.AddClickEventListener(self.node_list.btn_reset, BindTool.Bind(self.OnClickBtnReset, self))
    XUI.AddClickEventListener(self.node_list.btn_tips, BindTool.Bind(self.OnClickBtnTips, self))
    XUI.AddClickEventListener(self.node_list.element_group, BindTool.Bind(self.OnClickBtnTips, self))
    
end

function DujieBodyView:OpenCallBack()
    -- 渡劫界面把BGM停止了 要重新播放
    self:RePlayMusic()
end

function DujieBodyView:CloseCallBack()
    -- 渡劫cg有BGM 停止通用的
    if ViewManager.Instance:IsOpen(GuideModuleName.DujieView) then
        AudioService.Instance:StopBgm()
    end
end

function DujieBodyView:ShowIndexCallBack()

end

function DujieBodyView:ReleaseCallBack()

    self.is_frist_load = false
    self.is_frist_line_load = false

    if self.money_bar then
        self.money_bar:DeleteMe()
        self.money_bar = nil
    end

    if self.bottom_talent_item then
        self.bottom_talent_item:DeleteMe()
        self.bottom_talent_item = nil
    end

    if self.active_body_cost_cell then
        self.active_body_cost_cell:DeleteMe()
        self.active_body_cost_cell = nil
    end

    if self.active_talent_cost_cell then
        self.active_talent_cost_cell:DeleteMe()
        self.active_talent_cost_cell = nil
    end

    if self.body_list then
		self.body_list:DeleteMe()
		self.body_list = nil
	end

    if self.talent_skill_list then
        self.talent_skill_list:DeleteMe()
        self.talent_skill_list = nil
    end

    if self.body_attr_list then
		self.body_attr_list:DeleteMe()
		self.body_attr_list = nil
    end

    if self.talent_attr_list then
		self.talent_attr_list:DeleteMe()
		self.talent_attr_list = nil
    end

    local bounder = DujieWGData.Instance:GetBounderNum()
    for row = 1, bounder do
        for col = 1, bounder do
            if self.talent_list[row] and self.talent_list[row][col] then
                self.talent_list[row][col].parent_view = nil
                self.talent_list[row][col]:DeleteMe()
                self.talent_list[row][col] = nil
            end
        end
        self.talent_list[row] = nil
    end
    self.talent_list = nil

    local line_max_bounder = bounder - 1  
    for row = 1, line_max_bounder do
        for col = 1, line_max_bounder do
            if self.talent_line_map[row] and self.talent_line_map[row][col] then
                self.talent_line_map[row][col].parent_view = nil
                self.talent_line_map[row][col]:DeleteMe()
                self.talent_line_map[row][col] = nil
            end
        end
        self.talent_line_map[row] = nil
    end
    self.talent_line_map = nil
end

-- 创建天赋和线条
function DujieBodyView:CreateTalentCellList()
	if not self.talent_list then
        self.talent_list = {}
		self.talent_line_map = {}

        local bounder = DujieWGData.Instance:GetBounderNum()
        RectTransform.SetSizeDeltaXY(self.node_list.node_parent.transform, TALENT_SIZE_WIDTH*(bounder-1)+OFFSET_WIDTH*4, TALENT_SIZE_HEIGHT*(bounder-1)+OFFSET_HEIGHT*4)
		local obj_name = "talent_item"
		local res_async_loader = AllocResAsyncLoader(self, obj_name)
		res_async_loader:Load("uis/view/dujie_body_ui_prefab", obj_name, nil, function(new_obj)

            if bounder == 0 then
                return
            end
            local count = bounder* bounder
            local talent_node_group = self.node_list.talent_node_group.rect

            local talent_list = {}
            local row,col = 1,1
            talent_list[row] = {}
            for i=1, count do

				local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(talent_node_group, false)
                local talent_item = DujieTalentItemRender.New(obj)
                talent_item.parent_view = self
                talent_item:SetIndex(i)
                talent_item:SetClickCallBack(BindTool.Bind(self.OnSelectTalentCell, self, row,col, true))
                local pos_x = (col-1)*TALENT_SIZE_WIDTH  + OFFSET_WIDTH + ITEM_HALF_WIDTH*2 - WIDTH_SPACE + 88
                local pos_y = (row-1)*TALENT_SIZE_HEIGHT  + OFFSET_HEIGHT + ITEM_HALF_HEIGHT*2 - HEIGHT_SPACE
                talent_item:SetPos(pos_x, pos_y)
                talent_list[row][col] = talent_item

                if col%bounder == 0 then
                    col = 1
                    row = row + 1
                    talent_list[row] = {}
                else
                    col = col + 1
                end
            end
            self.talent_list = talent_list

            self.is_frist_load = true
            self:SetAllTalentShow()
            self:FlushBodyActiveState(true)
		end)

        local obj_name2 = "talent_line_item"
		local res_async_loader2 = AllocResAsyncLoader(self, obj_name2)
		res_async_loader2:Load("uis/view/dujie_body_ui_prefab", obj_name2, nil, function(new_obj)

            if bounder == 0 then
                return
            end
            local line_node_group = self.node_list.line_node_group.rect

            local row,col = 1,1
            local talent_line_map = {}
            local count = (bounder-1)* (bounder-1)
            talent_line_map[row] = {}
            for i=1, count do
                local obj = ResMgr:Instantiate(new_obj)
				obj.transform:SetParent(line_node_group, false)
                local talent_item = DujieTalentLineItemRender.New(obj)
                talent_item.parent_view = self
                talent_item:SetIndex(i)
                local pos_x = (col-1)*TALENT_SIZE_WIDTH  + OFFSET_WIDTH + ITEM_HALF_WIDTH*2 - WIDTH_SPACE + 88
                local pos_y = (row-1)*TALENT_SIZE_HEIGHT  + OFFSET_HEIGHT + ITEM_HALF_HEIGHT*3 - HEIGHT_SPACE + 19
                talent_item:SetPos(pos_x, pos_y)
                talent_line_map[row][col] = talent_item

                if col%(bounder-1) == 0 then
                    col = 1
                    row = row + 1
                    talent_line_map[row] = {}
                else
                    col = col + 1
                end
            end
            self.talent_line_map = talent_line_map

            self.is_frist_line_load = true
            self:SetAllTalentLineShow()
		end)
	end
end

-- 设置天赋线条显示
function DujieBodyView:SetAllTalentLineShow()
    if not self.is_frist_line_load then
        return
    end

    local about_list = DujieWGData.Instance:GetTalnetLineAboutList(self.cur_body_seq)
    if IsEmptyTable(about_list) then
        return 
    end

    local bounder = DujieWGData.Instance:GetBounderNum()
    local max_bounder = bounder - 1
    -- 先判断表里有没有配置这个数据，没有直接隐藏
    for pos_x = 1, max_bounder do
        for pos_y = 1, max_bounder do
            local data = about_list[pos_x] and about_list[pos_x][pos_y]
            if data then
                local is_show = DujieWGData.Instance:IsShowTalent(self.cur_body_seq, data.seq)
                self.talent_line_map[pos_y][pos_x]:SetCellVisable(is_show)
                if is_show then
                    local is_up_show = DujieWGData.Instance:IsShowTalent(self.cur_body_seq, data.up_seq)
                    self.talent_line_map[pos_y][pos_x]:SetVerticalVisable(is_up_show)

                    local is_right_show = DujieWGData.Instance:IsShowTalent(self.cur_body_seq, data.right_seq)
                    self.talent_line_map[pos_y][pos_x]:SetHorizontalVisable(is_right_show)
                end
            else
                self.talent_line_map[pos_y][pos_x]:SetCellVisable(false)
            end
        end
    end
    
end

-- 设置天赋显示
function DujieBodyView:SetAllTalentShow(is_flush,is_set_visable)
    if not self.is_frist_load then
        return
    end
    if is_set_visable == nil then
        is_set_visable = true
    end
    local bounder = DujieWGData.Instance:GetBounderNum()

    local active_talent_list = {}
    -- 先判断表里有没有配置这个数据，没有直接隐藏
    for x = 1, bounder do
        for y = 1, bounder do
            local talent_cfg = DujieWGData.Instance:GetTalentCfgByPos(self.cur_body_seq, x, y)
            local item_cell = self.talent_list[y][x]
            -- 有的话 根据对应配置的seq，判断后端给的数据
            if talent_cfg then
                local talent_info = DujieWGData.Instance:GetTalentInfo(self.cur_body_seq,talent_cfg.seq)

                local data = {}
                data.info = talent_info
                data.cfg = talent_cfg
                item_cell:SetData(data)
                table.insert(active_talent_list,data)

                if is_set_visable  then
                    local is_show = DujieWGData.Instance:IsShowTalent(talent_cfg.body_seq, talent_cfg.seq)
                    item_cell:SetCellVisable(is_show,is_flush)
                end

            else
                item_cell:SetCellVisable(false)
            end
        end
    end

end

function DujieBodyView:OnFlush(param_t)
    

    for k,v in pairs(param_t) do
		if k == "all" then
            self:FlushEsotericaList()
            self:FlushBodyActiveState()
            
        elseif k == "money_bar" then
            self:FlushMoney()
        elseif k == "flush_talent" then
            self.select_talent_data.info = DujieWGData.Instance:GetTalentInfo(self.select_talent_data.cfg.body_seq, self.select_talent_data.cfg.seq)
            self:FlushTalent()
            self:SetAllTalentShow(false,false)
            -- self:SetAllTalentLineShow()
            self:FlushRightLayer()
            self:FlushActiveGroup()
            self:FlushBodyActiveState()
        elseif k == "flush_body" then
            -- 灵根刷新
            self.select_talent_data.info = DujieWGData.Instance:GetTalentInfo(self.select_talent_data.cfg.body_seq, self.select_talent_data.cfg.seq)
            self:FlushBodyActiveState()
            self:FlushEsotericaList()
            self:SetAllTalentShow()
            self:SetAllTalentLineShow()

        elseif k == "reset_body" then
            -- 重置灵根刷新
            self:FlushBodyActiveState()
            self:SetAllTalentShow()
            self:SetAllTalentLineShow()
            self:FlushRightLayer()
            self:FlushEsotericaList()
        elseif k == "auto_select" then
            self:SetAllTalentShow(true)
            self:SetAllTalentLineShow()
            -- 灵根自动选择
            local about_talent_list = DujieWGData.Instance:GetAboutTalentList(self.cur_body_seq, v.seq)
            for k, v in pairs(about_talent_list) do
                if not DujieWGData.Instance:IsActiveTalent(self.cur_body_seq, v.seq) then
                    self:OnSelectTalentCell(v.pos_y, v.pos_x, false)
                    
                end
            end

        elseif k == "dianliang_effect" then
            local talent_cfg = DujieWGData.Instance:GetTalentCfgBySeq(self.cur_body_seq, v.seq)
            local talent_info = DujieWGData.Instance:GetTalentInfo(self.cur_body_seq, v.seq)
            local cell = self.talent_list[talent_cfg.pos_y][talent_cfg.pos_x]
            if cell then
                local grandParent = cell.view.transform.parent.parent
                local pos = grandParent:InverseTransformPoint(cell.view.transform.position)
                self.node_list.effect_dianliang.rect.anchoredPosition = Vector2(pos.x +54, pos.y +54)
                self.node_list.effect_dianliang:SetActive(false)
                self.node_list.effect_dianliang:SetActive(true)

                for i = 1, 5 do
                    self.node_list["effect_color_"..i]:SetActive(false)
                end
                if talent_info and self.node_list["effect_color_"..talent_info.color] then
                    self.node_list["effect_color_"..talent_info.color]:SetActive(true)
                end
            end
        end
    end
end

function DujieBodyView:FlushMoney()
    if self.money_bar then
        local show_params = {
            show_custom_1 = true,
            custom_item_id_1 = DujieWGData.Instance:GetOtherCfg("body_item_1"),
            custom_num_1 = DujieWGData.Instance:GetMoneyNum(1),
            show_custom_2 = true,
            custom_item_id_2 = DujieWGData.Instance:GetOtherCfg("body_item_2"),
            custom_num_2 = DujieWGData.Instance:GetMoneyNum(2),
            show_custom_3 = true,
            custom_item_id_3 = DujieWGData.Instance:GetOtherCfg("body_item_3"),
            custom_num_3 = DujieWGData.Instance:GetMoneyNum(3),
        }
        self.money_bar:SetMoneyShowInfo(0, 0, show_params)
        self.money_bar:Flush()
    end

end

function DujieBodyView:OnSelectBodyList(cell)
    if (not cell) or (not cell.data) then
		return
	end

    self.cur_body_data = cell.data
    self.cur_body_seq = cell.data.seq

    local first_talent_cfg = DujieWGData.Instance:GetTalentCfgBySeq(self.cur_body_seq,0)
    self:SetGridPos(nil,first_talent_cfg.pos_y,first_talent_cfg.pos_x)
    
    self:FlushGrid()

    self:FlushBodyActiveState(true)
    
end

function DujieBodyView:OnClckTalentSkill(cell)
    if (not cell) or (not cell.data) then
		return
	end
    if cell.data.is_special then
        DujieWGCtrl.Instance:OpenBodyTalentSkillTipsView(cell.data)
    else
        if cell.data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
            DujieWGCtrl.Instance:OpenBodyTalentSkillView(cell.data)
        else
            DujieWGCtrl.Instance:OpenBodyTalentSkillTipsView(cell.data)
        end
        
    end
end

function DujieBodyView:FlushBodyActiveState(move_first)
    if not self.is_frist_load then
        return
    end
    if self.cur_body_seq < 0 then
        return
    end
    if move_first then
        local first_talent_cfg = DujieWGData.Instance:GetTalentCfgBySeq(self.cur_body_seq,0)
        self:OnSelectTalentCell(first_talent_cfg.pos_y, first_talent_cfg.pos_x, false)
    end


    local is_active_body = DujieWGData.Instance:IsActiveBody(self.cur_body_seq)
    self.node_list.bottom_layer:SetActive(is_active_body)
    self.node_list.active_group:SetActive(is_active_body)
    self.node_list.body_active_layer:SetActive(not is_active_body)
    self.node_list.body_active_group:SetActive(not is_active_body)

    if not is_active_body then
        self:FlushActiveLayer()
        local first_talent_cfg = DujieWGData.Instance:GetTalentCfgBySeq(self.cur_body_seq,0)
        local cell = self.talent_list[first_talent_cfg.pos_y][first_talent_cfg.pos_x]
        local grandParent = cell.view.transform.parent.parent
        local pos = grandParent:InverseTransformPoint(cell.view.transform.position)
        local pos_x = pos.x + 54
        local pos_y = pos.y - 30
        self.node_list.body_active_group.rect.anchoredPosition = Vector2(pos_x,pos_y)
    end
end

function DujieBodyView:FlushActiveLayer()
    if self.cur_body_data then
        local is_can_active = DujieWGData.Instance:IsCanActiveBody(self.cur_body_seq)
        -- Layer部分
        self.node_list.text_cur_body_desc.tmp.text = self.cur_body_data.body_desc
        self.node_list.text_cur_body_name.tmp.text = self.cur_body_data.body_name

        

        -- Group部分
        local level_cfg = DujieWGData.Instance:GetLevelCfg(self.cur_body_data.unlock_ordeal_level)
        if level_cfg then
            local type_str = DujieWGData.Instance:GetTypeStr(level_cfg.type)

            if self.cur_body_data.unlock_ordeal_level == 0 then
                self.node_list.text_body_active_limit:SetActive(false)
            else
                if DujieWGData.Instance:IsCanActiveBody(self.cur_body_seq) then

                    self.node_list.text_body_active_limit.tmp.text = string.format(Language.Dujie.BodyActiveStr, self.cur_body_data.body_name)
                else
                    self.node_list.text_body_active_limit:SetActive(true)
                    local limit_str = type_str..DujieWGData.Instance:GetLevelIndexStr(self.cur_body_data.unlock_ordeal_level)
                    self.node_list.text_body_active_limit.tmp.text = string.format(Language.Dujie.BodyActiveLimitStr, limit_str)
                end

            end

        else
            print_error("灵根配置的解锁等级不对")
        end
        -- 消耗显示
        if self.cur_body_data.unlock_item_id ~= 0  then
            self.node_list.body_active_cost_group:SetActive(true)

            self.active_body_cost_cell:SetData({item_id = self.cur_body_data.unlock_item_id})

            self.node_list.text_cost_active_body.tmp_text = "X"..self.cur_body_data.unlock_item_num

            self.node_list.text_body_active_limit:SetActive(false)
        else
            self.node_list.body_active_cost_group:SetActive(false)
        end
        -- 可激活红点
        self.node_list.remind_active_body:SetActive(DujieWGData.Instance:IsCanActiveBody(self.cur_body_seq))
        
        
    end
end

function DujieBodyView:FlushGrid()
    self:SetAllTalentLineShow()
    self:SetAllTalentShow()
    self:FlushRightLayer()
end

function DujieBodyView:OnClickOpenDetailViewBtn()
    CultivationWGCtrl.Instance:SetDataAndOpenEsotericaDetailView(self.select_index)
end

function DujieBodyView:FlushEsotericaList()
    local data_list = DujieWGData.Instance:GetBodyCfg()
    self.body_list:SetDataList(data_list)
end

-- 刷新右侧面板
function DujieBodyView:FlushRightLayer()
    if self.cur_body_seq < 0 then
        return
    end

    local body_info = DujieWGData.Instance:GetBodyInfo(self.cur_body_seq)
    
    -- 品质 灵根名字+完美度+品质
    local body_cfg = DujieWGData.Instance:GetBodyCfgBySeq(self.cur_body_seq)
    local body_score_cfg, next_body_score_cfg = DujieWGData.Instance:GetBodyScoreCfg(self.cur_body_seq, body_info.score)

    self.node_list.text_body_quality.tmp.text = string.format(Language.Dujie.BodyQualityStr, body_cfg.body_name, body_score_cfg.level_txt)
    -- 进度
    if next_body_score_cfg then
        self.node_list.img_scorce_progress.image.fillAmount = body_info.score/next_body_score_cfg.need_score
    else
        self.node_list.img_scorce_progress.image.fillAmount = 1
    end
    -- 评分
    if next_body_score_cfg then
        self.node_list.text_body_score.tmp.text = body_info.score .. "/" .. next_body_score_cfg.need_score
    else
        self.node_list.text_body_score.tmp.text = body_info.score .. "/" .. body_score_cfg.need_score
    end
    -- 属性
    local attr_info = DujieWGData.Instance:GetordealBodyAttrInfo(self.cur_body_seq)
    if IsEmptyTable(attr_info) or attr_info.count == 0 or IsEmptyTable(attr_info.attr_list) then
        self.node_list.common_tips_text:SetActive(true)
        self.node_list.body_attr_list:SetActive(false)

        -- 战力
        self.node_list.cap_value.tmp.text = 0

    else
        self.node_list.common_tips_text:SetActive(false)
        self.node_list.body_attr_list:SetActive(true)
        local attr_list, capability = EquipmentWGData.Instance:OutStrAttrListAndCapalityByAttrId(attr_info.attr_list, "attr_id", "attr_value","attr_name","attr_value",1,attr_info.count)
        self.body_attr_list:SetDataList(attr_list)

        
        self.node_list.cap_value.tmp.text = capability
    end
    

    -- 元素
    for i = 1, 5 do
        if body_info.element_count_list[i - 1] then
            self.node_list["text_element_count_"..i].tmp.text = body_info.element_count_list[i - 1]
        else
            self.node_list["text_element_count_"..i].tmp.text = 0
        end
    end

    -- 技能
    local skill_list = DujieWGData.Instance:GetTalentSkillList(self.cur_body_seq)
    self.talent_skill_list:SetDataList(skill_list)


end

function DujieBodyView:OnClickBtnActiveBody()
    if DujieWGData.Instance:IsCanActiveBody(self.cur_body_seq) then
        DujieWGCtrl.Instance:ActiveBody(self.cur_body_seq)
    else
        self.active_body_cost_cell:OnClick()
        SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.BodyActiveLimitTips)
    end

end

function DujieBodyView:OnClickBtnActiveTalent()
    if self.select_talent_data then
        -- 特殊处理
        if self.select_talent_data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
            DujieWGCtrl.Instance:OpenBodyTalentSkillView(self.select_talent_data)
        else
            -- 前置条件是否满足
            if DujieWGData.Instance:IsActivePreTalent(self.select_talent_data.cfg.body_seq,self.select_talent_data.cfg.seq) then
                if DujieWGData.Instance:IsActiveReach(self.select_talent_data.cfg.body_seq,self.select_talent_data.cfg.seq) then
                    DujieWGCtrl.Instance:ActiveTalent(self.select_talent_data.cfg.body_seq,self.select_talent_data.cfg.seq)
                else
                    self.active_talent_cost_cell:OnClick()
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.ActiveCostNotReach)
                end
            else
                SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.ActivePre)
            end

        end

    end
end

function DujieBodyView:OnClickBtnReset()
    -- Language.Dujie.ResetTips
    local tips_data = {}
    tips_data.title_view_name = "重置灵根"
    tips_data.desc = Language.Dujie.ResetTips
    tips_data.bodu_seq = self.cur_body_seq
    tips_data.cost_item = {}

    local reset_item_id = DujieWGData.Instance:GetOtherCfg("reset_item")
    local item_data = {}
    item_data.item_id = tonumber(reset_item_id)
    item_data.num = 1
    tips_data.cost_item = item_data


    -- TipWGCtrl.Instance:OpenBuyItemTipsView(Language.Dujie.ResetTips, function ()
    --     DujieWGCtrl.Instance:TalentReset(self.cur_body_seq)
    -- end)

    DujieWGCtrl.Instance:OpenBodyResetTipsView(tips_data)
end



-- 设置Grid位置
function DujieBodyView:SetGridPos(cell, row ,col)
    local bounder = DujieWGData.Instance:GetBounderNum()

    if bounder <= 3 then
        self.node_list.node_parent.rect:DOAnchorPos(Vector2(0, 0), 0.5)
    else
        -- print_error("col:",col)
        -- print_error("row:",row)
        local min = bounder/2

        local pos_x = -(col-min)*TALENT_SIZE_WIDTH
        local pos_y = -(row-min)*TALENT_SIZE_HEIGHT

        -- 锚点在左下角 要做个偏移
        if col ~= min then
            pos_x = pos_x + TALENT_SIZE_WIDTH/2
        end
        if row ~= min then
            pos_y = pos_y + TALENT_SIZE_HEIGHT/2
        end

        self.node_list.node_parent.rect:DOAnchorPos(Vector2(pos_x, pos_y) , 0.5)

    end
end


-- 刷新天赋信息
function DujieBodyView:FlushTalent()
    if not self.select_talent_data then
        return
    end

    self.bottom_talent_item:SetData(self.select_talent_data)

    local info = self.select_talent_data.info
    local cfg = self.select_talent_data.cfg
    
    local title_str = DujieWGData.Instance:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.TalentType, cfg.type)
    if info and info.is_active > 0 then
        local element_str = ""
        if cfg.type == DUJIE_BODY_TALENT_TYPE.ATTR then
            element_str = DujieWGData.Instance:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.Element, info.element)

            local attr_cfg = DujieWGData.Instance:GetTalentAttrCfg(cfg.type, info.element, info.color)
            -- 属性显示
            local attr_list,cap = DujieWGData.Instance:GetTalentAttrDataAndCap(attr_cfg)
            self.node_list.talent_attr_list:SetActive(true)
            self.talent_attr_list:SetDataList(attr_list)

            self.node_list.text_cur_talent_desc.tmp.text = ""

            
            -- 天赋名字
            local str = string.format(Language.Dujie.TalentNameStr, title_str, element_str,info.color)
            self.node_list.text_cur_talent_name.tmp.text = str

        elseif cfg.type == DUJIE_BODY_TALENT_TYPE.EFFECT then
            element_str = DujieWGData.Instance:GetDescByTypeAndSeq(DUJIE_DESC_TYPE.Element, info.element)
            self.node_list.talent_attr_list:SetActive(false)

            local skill_desc = DujieWGData.Instance:GetTalentSkillDesc(info.skill_seq, self.cur_body_seq)
            -- 描述
            self.node_list.text_cur_talent_desc.tmp.text = skill_desc

            
            -- 天赋名字
            local str = string.format(Language.Dujie.TalentNameStr, title_str, element_str,info.color)
            self.node_list.text_cur_talent_name.tmp.text = str
        elseif cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
            self.node_list.talent_attr_list:SetActive(false)

            local skill_desc,skill_name = DujieWGData.Instance:GetTalentSkillDesc(info.skill_seq, self.cur_body_seq)
            -- 描述
            self.node_list.text_cur_talent_desc.tmp.text = skill_desc
            
            -- 天赋名字
            local str = string.format(Language.Dujie.TalentNameStr2, title_str,skill_name)
            self.node_list.text_cur_talent_name.tmp.text = str
        end

        -- 元素和数量
        local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_0_"..info.element + 1)
        self.node_list.img_cur_element.image:LoadSprite(bundle, asset)
        self.node_list.text_cur_element_count.tmp.text = "X"..info.element_count




    else
        self.node_list.talent_attr_list:SetActive(false)

        self.node_list.text_cur_talent_name.tmp.text = string.format(Language.Dujie.TalentNameNotActiveStr,title_str)

        local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_0_0")
        self.node_list.img_cur_element.image:LoadSprite(bundle, asset)
        self.node_list.text_cur_element_count.tmp.text = "X0"

        -- 描述
        self.node_list.text_cur_talent_desc.tmp.text = Language.Dujie.TalentDescNotActive[cfg.type]
    end
    
end

-- 天赋选中
function DujieBodyView:OnSelectTalentCell(row ,col , is_click)
    if not self.talent_list or not self.talent_list[row] or not self.talent_list[row][col] then
        return
    end

    local cell = self.talent_list[row][col]
    local cell_data = cell:GetData()
    if not cell_data then
        return
    end

    self.select_talent_data = cell_data

    -- 设置Grid坐标
    self:SetGridPos(cell, row ,col)
    -- 设置激活按钮坐标
    local grandParent = cell.view.transform.parent.parent
    local pos = grandParent:InverseTransformPoint(cell.view.transform.position)
    local pos_x = pos.x + 54
    local pos_y = pos.y - 30
    self.node_list.active_group.rect.anchoredPosition = Vector2(pos_x, pos_y)

    -- self.node_list.effect_dianliang.rect.anchoredPosition = Vector2(pos.x +54, pos.y +54)

    if DujieWGData.Instance:IsActiveBody(self.cur_body_seq) then
        self.node_list.bottom_layer:SetActive(true)

        -- 无前置限制的直接显示
        if self.select_talent_data.cfg.pre_seq == -1 then
            self.node_list.active_group:SetActive(true)
        else
            local pre_list = Split(self.select_talent_data.cfg.pre_seq,"|")
            local is_active = false
            for k, v in pairs(pre_list) do
                if DujieWGData.Instance:IsActiveTalent(self.cur_body_seq,tonumber(v)) then
                    is_active = true
                end
            end
            -- 有前置的，任意前置满足显示
            self.node_list.active_group:SetActive(is_active)

            if self.select_talent_data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL and is_click then
                if not is_active then
                    SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.NotCanAcitveTalentStr)
                    DujieWGCtrl.Instance:OpenBodyTalentSkillView(self.select_talent_data)
                elseif self.select_talent_data.info.skill_seq == -1 then
                    DujieWGCtrl.Instance:OpenBodyTalentSkillView(self.select_talent_data)
                end
            end
        end

        self:FlushActiveGroup()

        self:FlushTalent()
    else
        if self.select_talent_data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
            -- SysMsgWGCtrl.Instance:ErrorRemind(Language.Dujie.NotCanAcitveTalentStr)
            DujieWGCtrl.Instance:OpenBodyTalentSkillView(self.select_talent_data)
        end
    end
    
    
    
end

-- 刷新注灵group
function DujieBodyView:FlushActiveGroup()
    if self.select_talent_data then
        if self.select_talent_data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
            self.node_list.active_cost_group:SetActive(false)
        else
            self.node_list.active_cost_group:SetActive(true)
            local type, num = nil,nil
            if self.select_talent_data.info.is_active > 0 then
                type, num = DujieWGData.Instance:GetRefineCost(self.select_talent_data.cfg.body_seq, self.select_talent_data.cfg.seq)
            else
                type, num = DujieWGData.Instance:GetActiveCost(self.select_talent_data.cfg.body_seq, self.select_talent_data.cfg.seq)
            end
            
            local item_id = DujieWGData.Instance:GetOtherCfg("body_item_"..type)
    
            self.active_talent_cost_cell:SetData({item_id = item_id})
            self.node_list.text_cost.tmp.text = "X"..num
    
            self.node_list.item_node:SetActive(true)
            self.node_list.text_cost:SetActive(true)
        end
        self.node_list.btn_text_talent_active.tmp.text = self.select_talent_data.info.is_active > 0 and Language.Dujie.ActiveBtnStr2 or Language.Dujie.ActiveBtnStr1
    end


    
end

function DujieBodyView:OnClickBtnTips()
    RuleTip.Instance:SetContent(Language.Dujie.ElementRuleTitle, Language.Dujie.ElementRuleContent)
end
---------------------------- 音频 ---------------------
function DujieBodyView:RePlayMusic()
    local sceneLogic = Scene.Instance:GetSceneLogic()
    sceneLogic:PlayBGM()
end

---------------------------------------------------------
DujieBodyItemRender = DujieBodyItemRender or BaseClass(BaseRender)
function DujieBodyItemRender:__init()

end

function DujieBodyItemRender:LoadCallBack()

end

function DujieBodyItemRender:ReleaseCallBack()

end

function DujieBodyItemRender:OnFlush()
    if not self.data then return end

    
    local bundle_1, asset_1 = ResPath.GetDujieBodyImg("a3_lg_"..self.data.seq)
    self.node_list.img_normal.image:LoadSprite(bundle_1, asset_1, function ()
        self.node_list.img_normal.image:SetNativeSize()
    end)

    local bundle_2, asset_2 = ResPath.GetDujieBodyImg("a3_lg_hl_"..self.data.seq)
    self.node_list.img_hl.image:LoadSprite(bundle_2, asset_2, function ()
        self.node_list.img_hl.image:SetNativeSize()
    end)

    self.node_list.text_name.tmp.text = self.data.body_name

    self.node_list.text_count.tmp.text = DujieWGData.Instance:GetActiveCountByBody(self.data.seq)

    self.node_list.remind:SetActive(DujieWGData.Instance:GetBodyRemindBySeq(self.data.seq))
end

function DujieBodyItemRender:OnSelectChange(is_on)
    self.node_list.hl:SetActive(is_on)
    self.node_list.normal:SetActive(not is_on)
end

---------------------------------------------------------
DujieTalentLineItemRender = DujieTalentLineItemRender or BaseClass(BaseRender)
function DujieTalentLineItemRender:__init()

end

function DujieTalentLineItemRender:SetPos(pos_x, pos_y)
    self.m_pos = {x = pos_x, y = pos_y}
    RectTransform.SetAnchoredPositionXY(self.view.rect, pos_x, pos_y)
end

-- 设置显隐
function DujieTalentLineItemRender:SetCellVisable(enabled)
    if self.view then
        self.view:SetActive(enabled)
    end
end

-- 设置横线显隐
function DujieTalentLineItemRender:SetHorizontalVisable(enabled)
    self.node_list.img_line_horizontal:SetActive(enabled)
end

-- 设置竖线显隐
function DujieTalentLineItemRender:SetVerticalVisable(enabled)
    self.node_list.img_line_vertical:SetActive(enabled)
end
---------------------------------------------------------
DujieTalentItemRender = DujieTalentItemRender or BaseClass(BaseRender)
function DujieTalentItemRender:__init()
    self.is_active = true
end

function DujieTalentItemRender:OnFlush()
    if not self.data then return end

    self:SetTalentType()
    self.node_list.img_element:SetActive(false)
    self.node_list.img_skill_icon:SetActive(false)

    
    -- 已激活
    if self.data.info and self.data.info.is_active > 0 then
        self:SetTalentActiveImage()

    else
        self:SetTalentNotActiveImage()
    end
    
end

function DujieTalentItemRender:SetTalentType()
    self.node_list.img_bg:SetActive(self.data.cfg.type ~= DUJIE_BODY_TALENT_TYPE.SKILL)
    self.node_list.img_bg2:SetActive(self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL)
    
    self.node_list.img_element:SetActive(self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.ATTR)
    self.node_list.img_skill_icon:SetActive(self.data.cfg.type ~= DUJIE_BODY_TALENT_TYPE.ATTR)
end

function DujieTalentItemRender:SetTalentImage()
    self.node_list.img_bg:SetActive(self.data.cfg.type ~= DUJIE_BODY_TALENT_TYPE.SKILL)
    self.node_list.img_bg2:SetActive(self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL)
    
    self.node_list.img_element:SetActive(self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.ATTR)
    self.node_list.img_skill_icon:SetActive(self.data.cfg.type ~= DUJIE_BODY_TALENT_TYPE.ATTR)
end

function DujieTalentItemRender:SetTalentNotActiveImage()
    if self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.ATTR then
        self:SetElementImage(0)
    elseif self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.EFFECT then
        self:SetBigElementImage(0)
    elseif self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
        self:SetSkillIconImage(0)
    end

end

function DujieTalentItemRender:SetTalentActiveImage()


    if self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.ATTR then
        self:SetElementImage(self.data.info.element + 1)
    elseif self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.EFFECT then
        self:SetBigElementImage(self.data.info.element + 1)
    elseif self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
        local talent_skill_cfg = DujieWGData.Instance:GetTalentSkillCfg(self.data.info.skill_seq)
        if talent_skill_cfg then
            self:SetSkillIconImage(talent_skill_cfg.skill_icon)
        else
            self:SetSkillIconImage(0)
        end
        
    end
end

-- 设置元素图片
function DujieTalentItemRender:SetElementImage(index)
    local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_0_"..index)
    self.node_list.img_element.image:LoadSprite(bundle, asset)
    self.node_list.img_element:SetActive(true)

end

-- 设置特殊元素图片
function DujieTalentItemRender:SetBigElementImage(index)
    local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_1_"..index)
    self.node_list.img_element.image:LoadSprite(bundle, asset)
    self.node_list.img_element:SetActive(true)
end

-- 设置技能图片
function DujieTalentItemRender:SetSkillIconImage(index)
    local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_2_"..index)
    self.node_list.img_skill_icon.image:LoadSprite(bundle, asset)
    self.node_list.img_skill_icon:SetActive(true)
end

function DujieTalentItemRender:OnSelectChange(is_on)

end

function DujieTalentItemRender:SetPos(pos_x, pos_y)
    self.m_pos = {x = pos_x, y = pos_y}
    RectTransform.SetAnchoredPositionXY(self.view.rect, pos_x, pos_y)
end

function DujieTalentItemRender:GetPos()
    return self.m_pos
end

function DujieTalentItemRender:GetV2Pos()
    return RectTransform.GetAnchoredPosition(self.view.rect)
end

function DujieTalentItemRender:GetSizeDelta()
    return RectTransform.GetSizeDelta(self.view.rect)
end

-- 设置显隐
function DujieTalentItemRender:SetCellVisable(enabled,is_flush)
    if self.view then
        self.view:SetActive(enabled)
        if self.node_list.effect_chuxian then
            self.node_list.effect_chuxian:SetActive(false)
        end
        if self.is_enabled ~= enabled then
            if is_flush and enabled then
                if self.node_list.effect_chuxian then
                    self.node_list.effect_chuxian:SetActive(true)
                end
            end

        end
        self.is_enabled = enabled
    end
end

---------------------------------------------------------
BodyTalentSkillItemRender = BodyTalentSkillItemRender or BaseClass(BaseRender)
function BodyTalentSkillItemRender:__init()

end


function BodyTalentSkillItemRender:ReleaseCallBack()

end

function BodyTalentSkillItemRender:OnFlush()
    if not self.data then return end

    self.node_list.img_bg3:SetActive(self.data.is_special)
    self.node_list.img_bg:SetActive(not self.data.is_special and self.data.cfg.type ~= DUJIE_BODY_TALENT_TYPE.SKILL)
    self.node_list.img_bg2:SetActive(not self.data.is_special and self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL)

    if self.data.is_special then

        local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_hl_"..self.data.body_seq)
        self.node_list.img_skill_icon.image:LoadSprite(bundle, asset)
    else
        -- 技能天赋特殊处理
        if self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.SKILL then
            if self.data.info.skill_seq >= 0 then
                local skill_cfg = DujieWGData.Instance:GetTalentSkillCfg(self.data.info.skill_seq)
                local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_2_"..skill_cfg.skill_icon)
                self.node_list.img_skill_icon.image:LoadSprite(bundle, asset)
            else
                local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_2_0")
                self.node_list.img_skill_icon.image:LoadSprite(bundle, asset)
            end

        elseif self.data.cfg.type == DUJIE_BODY_TALENT_TYPE.EFFECT then
            local bundle, asset = ResPath.GetDujieBodyImg("a3_lg_element_1_"..self.data.info.element + 1)
            self.node_list.img_skill_icon.image:LoadSprite(bundle, asset)
        end

    end
end

function BodyTalentSkillItemRender:OnSelectChange(is_on)

end