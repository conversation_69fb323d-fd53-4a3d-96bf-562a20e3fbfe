RevengeWGData = RevengeWGData or BaseClass()
local this = RevengeWGData

function this:__init()
	RevengeWGData.Instance = self
	self.data = {}


	self.cur_revenge_role_id = nil  --当前正在反击的玩家id
	self.revenge_list = {}
	self.revenge_role_list = {}
	self.revenge_time_list = {}
	self.want_revenge_role = nil
	self.check_revenge_safe_time = 0
	self.scene_loading_state_quit = GlobalEventSystem:Bind(SceneEventType.SCENE_LOADING_STATE_QUIT, BindTool.Bind(self.OnSceneChangeComplete, self))
end

function this:__delete()
	self.want_revenge_role = nil
	RevengeWGData.Instance = nil	

	if self.scene_loading_state_quit then
		GlobalEventSystem:UnBind(self.scene_loading_state_quit)
		self.scene_loading_state_quit = nil
	end
end

function this:OnSceneChangeComplete()
	self:ClearList()
	MainuiWGCtrl.Instance:RevengeChange()
end

function this:SetCurRevengeRoleId(role_id)
	self.cur_revenge_role_id = role_id
end

function this:GetCurRevengeRoleId()
	return self.cur_revenge_role_id 
end

function this:GetRevengeIsVaild(role_id)
	local is_vaild = false
	local check_role_id = role_id or self.cur_revenge_role_id
	if check_role_id == nil or check_role_id == nil then
		return is_vaild
	end

	if self.revenge_role_list[check_role_id] ~= nil then
		is_vaild = true
	end

	return is_vaild
end

--revenge_list key 在跨服为 origin_id 在本服为 role_id
--revenge_role_list key 为 role_id 用于和场景里的物体做对比

function this:InsertRevengeList(x, y, deliverer_role_id,deliverer_key_id)
	self.revenge_time_list[deliverer_role_id] = Status.NowTime
	if self.revenge_role_list[deliverer_role_id] then
		self.revenge_role_list[deliverer_role_id] = {x = x, y = y}
		-- 已加入列表
		return
	end

	-- 如果反击列表太长，则不再增加
	if #self.revenge_list >= 2 then
		return
	end

	table.insert(self.revenge_list, {deliverer_key_id = deliverer_key_id,deliverer_role_id = deliverer_role_id})
	self.revenge_role_list[deliverer_role_id] = {x = x, y = y}
	self.have_list = true
	return true
end

function this:GetHaveListFlag()
	return self.have_list == true
end

function this:CheckList(protocol)
	local change_flag = false
	if protocol == nil or (protocol.obj_type ~= OBJ_TYPE.OBJ_TYPE_ROLE and protocol.obj_type ~= OBJ_TYPE.OBJ_TYPE_INVALID) then
		return false
	end

	if protocol.obj_type ~= OBJ_TYPE.OBJ_TYPE_INVALID and self.revenge_role_list[protocol.param] then
		self.revenge_role_list[protocol.param] = {x = protocol.pos_x, y = protocol.pos_y}
	else
		change_flag = true
		self.revenge_role_list[protocol.param] = nil
	end

	if change_flag then
		self.revenge_list = TableSplitByCondition(self.revenge_list, function (revenge_role)
			return self.revenge_role_list[revenge_role.deliverer_role_id] ~= nil
		end)
		self.have_list = #self.revenge_list ~= 0
	end

	return change_flag
end

function this:SaveData(protocol)
	local show_role_name = self:IsShowRoleName()
	for k, v in pairs(self.revenge_list) do
		if tonumber(protocol.role_id) == tonumber(v.deliverer_key_id) then
			self.revenge_list[k].deliverer_prof = protocol.prof
            self.revenge_list[k].role_name = show_role_name and protocol.role_name or Language.Common.MysteryMen
            self.revenge_list[k].sex = protocol.sex
            self.revenge_list[k].tianshen_3v3_tianshen_index = protocol.tianshen_3v3_tianshen_index
			self.revenge_list[k].is_data_init = true
			self.revenge_list[k].merge_server_id = -1
			self.revenge_list[k].origin_plat_type = -1
			local obj = Scene.Instance:GetObj(protocol.obj_id)
			if not (nil == obj or obj:IsDeleted() or not obj:IsMainRole()) then
				local vo = obj:GetVo()
				self.revenge_list[k].merge_server_id = vo.merge_server_id
				self.revenge_list[k].origin_plat_type = vo.origin_plat_type
			end
			return self.revenge_list[k]
		end
	end

	return nil
end

function this:IsShowRoleName()
	return Scene.Instance:GetSceneType() ~= SceneType.YEZHANWANGCHENGFUBEN
end

function this:GetRevengeNum()
	return #self.revenge_list
end

function this:GetRevengeList()
	return self.revenge_list
end

function this:IsOnAttack(deliverer_role_id)
	if self.revenge_time_list[deliverer_role_id] then
		return Status.NowTime - self.revenge_time_list[deliverer_role_id] < 4
	else
		return false
	end
end

function this:IsNeedClearOnAttackList(deliverer_role_id) --30秒清空
	if self.revenge_time_list[deliverer_role_id] and self.revenge_time_list[deliverer_role_id] ~= 0 then
		return Status.NowTime - self.revenge_time_list[deliverer_role_id] >= 30
	else
		return false
	end
end


function this:GetOnAttackRoleId()
	local role_id = nil
	if self.revenge_time_list ~= nil and next(self.revenge_time_list) ~= nil then
		for k,v in pairs(self.revenge_time_list) do
			if Status.NowTime - v < 4 then
				role_id = k
				break
			end
		end
	end

	return role_id
end

function this:OnObjDead(obj)
	if not obj:IsRole() then
		return false
	end

	local role_id = obj:GetOriginId()
	for k, v in pairs(self.revenge_role_list) do
		if role_id == k then
			self.revenge_role_list[k] = nil

			self.revenge_list = TableSplitByCondition(self.revenge_list, function (revenge_role)
				return self.revenge_role_list[revenge_role.deliverer_role_id] ~= nil
			end)
			self.have_list = #self.revenge_list ~= 0

			return true, k
		end
	end

	return false, nil
end

function this:ClearList()
	self.revenge_time_list = {}
	self.revenge_list = {}
	self.have_list = false
	self.revenge_role_list = {}
end

function this:ClearRoleInList(role_id)
	if role_id == nil then
		return
	end

	local list = {}
	if self.revenge_list ~= nil then
		for k,v in pairs(self.revenge_list) do
			if v.deliverer_role_id == role_id then
				if self.cur_revenge_role_id ~= nil and self.cur_revenge_role_id == role_id then
					self:SetCurRevengeRoleId(nil)
				end
			else
				local data = {}
				data.deliverer_key_id = v.deliverer_key_id
				data.deliverer_role_id = v.deliverer_role_id
                data.deliverer_prof = v.deliverer_prof
                data.sex = v.sex
				data.role_name = v.role_name
				data.is_data_init = v.is_data_init
				data.tianshen_3v3_tianshen_index = v.tianshen_3v3_tianshen_index
				data.merge_server_id = v.merge_server_id
				data.origin_plat_type = v.origin_plat_type
				table.insert(list, data)
			end
		end
	end
	self.revenge_list = list
	self.revenge_time_list[role_id] = 0
	self.revenge_role_list[role_id] = nil
end

function this:SetWantRevengeRole(role_id)
	self.want_revenge_role = role_id
end

function this:GetWantRevengeRole()
	return self.want_revenge_role
end

function this:GetIsInRevengeList(role_id)
	local is_in = false
	if role_id == nil then
		return
	end

	if self.revenge_role_list ~= nil and self.revenge_role_list[role_id] ~= nil then
		is_in = true
	end

	return is_in
end

function this:IsCurRevengeObj(obj)
	local is_revenge = false
	if obj ~= nil and not obj:IsDeleted() and obj:IsRole() then
		local revenge_role_id = self:GetCurRevengeRoleId()
		if revenge_role_id == obj:GetRoleId() then
			is_revenge = true
		end
	end

	return is_revenge
end

function this:GetCheckTargetSafeTime()
	return self.check_revenge_safe_time
end

function this:SetCheckTargetSafeTime(time)
	self.check_revenge_safe_time = time or 0
end


function this:GetRevengeRolePos(role_id)
	local x = nil
	local y = nil

	if role_id == nil then
		return x, y
	end

	if self.revenge_role_list == nil then
		return x, y
	end

	local data = self.revenge_role_list[role_id]
	if data then
		x = data.x
		y = data.y
	end

	return x, y
end