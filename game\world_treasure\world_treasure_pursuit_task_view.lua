-- 天财地宝-迷影寻踪任务

WorldTreasurePursuitTaskView = WorldTreasurePursuitTaskView or BaseClass(SafeBaseView)

function WorldTreasurePursuitTaskView:__init()
	self:SetMaskBg(false)
    
    self:AddViewResource(0, "uis/view/world_treasure_ui_prefab", "pursuit_task_panel")
    
    self.is_loaded = false
end

function WorldTreasurePursuitTaskView:ReleaseCallBack()
	if self.pursuit_task_list then
		self.pursuit_task_list:DeleteMe()
		self.pursuit_task_list = nil
	end
	
	if self.get_guide_ui_event then
		FunctionGuide.Instance:UnRegiseGetGuideUiByFun(GuideModuleName.WorldTreasurePursuitTaskView, self.get_guide_ui_event)
		self.get_guide_ui_event = nil
	end

    self.is_loaded = false
end

function WorldTreasurePursuitTaskView:LoadCallBack()
    self.pursuit_task_list = AsyncListView.New(PursuitTaskItemRender, self.node_list["pursuit_task_list"])
    self.is_loaded = true
	self:DoCellsAnim()
	
	--功能引导注册
	self.get_guide_ui_event = BindTool.Bind(self.GetGuideUiCallBack, self)
	FunctionGuide.Instance:RegisteGetGuideUi(GuideModuleName.WorldTreasurePursuitTaskView, self.get_guide_ui_event)
end

function WorldTreasurePursuitTaskView:OpenCallBack()
    if self.is_loaded then
        self:DoCellsAnim()
    end
end

function WorldTreasurePursuitTaskView:OnFlush()
    local task_list = WorldTreasureWGData.Instance:GetPursuitTaskList()
	if task_list then
		self.pursuit_task_list:SetDataList(task_list)
	end
end

function WorldTreasurePursuitTaskView:DoCellsAnim()
	local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender
	self.node_list["pursuit_task_list"]:SetActive(false)
    ReDelayCall(self, function()
        self.node_list["pursuit_task_list"]:SetActive(true)
        local list =  self.pursuit_task_list:GetAllItems()
        local sort_list = GetSortListView(list)
        local count = 0
        for k,v in ipairs(sort_list) do
            if 0 ~= v.index then
                count = count + 1
            end
            v.item:PalyItemAnim(count)
        end
    end, tween_info.DelayDoTime, "pursuit_cell_tween")
end

function WorldTreasurePursuitTaskView:GetGuideUiCallBack(ui_name, ui_param)
	if ui_name == GuideUIName.BtnLingQu then
		local obj = self.pursuit_task_list and self.pursuit_task_list:GetItemAt(1)
		if obj then
			return obj.node_list.btn_lingqu, BindTool.Bind(obj.OnClickRewardHandler, obj)
		end
	elseif ui_name == GuideUIName.BtnCloseWindow then
		return self.node_list[ui_name], BindTool.Bind(self.Close, self)
	end
	return SafeBaseView.GetGuideUiCallBack(self, ui_name, ui_param)
end

----------------------------------------------------------------------------------------------------

PursuitTaskItemRender = PursuitTaskItemRender or BaseClass(BaseRender)

function PursuitTaskItemRender:LoadCallBack()
	self.item_list = {}
	self.node_list["btn_lingqu"].button:AddClickListener(BindTool.Bind1(self.OnClickRewardHandler, self))
	self.node_list["btn_go"].button:AddClickListener(BindTool.Bind1(self.OnClickGoHandler, self))
	self.task_reward_list = AsyncListView.New(ItemCell, self.node_list["reward_list"])
end

function PursuitTaskItemRender:__delete()
	if self.task_reward_list then
		self.task_reward_list:DeleteMe()
		self.task_reward_list = nil
	end
end

function PursuitTaskItemRender:OnFlush()
	if not self.data then
		return
	end 

	--local is_received = self.data.fetch_flag == 1
	--local is_complete = self.data.process >= self.data.cfg.task_param1
	--local receive_state = TianshenRoadWGData.Instance:GetSQTaskState(self.data.cfg.ID)
	self.node_list["task_name"].text.text = self.data.cfg.task_name
	self.node_list["condition"].text.text = self:GetTaskDesc()

	self.node_list["btn_lingqu"]:SetActive(self.data.can_receive)
	self.node_list["btn_yilingqu"]:SetActive(self.data.is_received)
	self.node_list["btn_go"]:SetActive(not self.data.can_receive and not self.data.is_received)

	self.task_reward_list:SetDataList(SortTableKey(self.data.cfg.reward_item))
end

function PursuitTaskItemRender:OnClickRewardHandler(sender)
	WorldTreasureWGCtrl.Instance:SendWorldTreasureOp(TREASURE_OPERATE_TYPE.TREASURE_OPERATE_TYPE_PURSUIT_TASK_REWARD, self.data.seq)
end

function PursuitTaskItemRender:OnClickGoHandler(sender)
	if self.data and self.data.cfg then
		local param = string.split(self.data.cfg.panel,"#")  
		FunOpen.Instance:OpenViewByName(param[1], param[2])
	end
end

function PursuitTaskItemRender:GetTaskDesc()
	local str = self.data.cfg.task_desc
	local param = self.data.process
	local pos = string.find(str, "%%s")
	if pos ~= nil then 
		local color = param >= self.data.cfg.task_param1 and "<color=#3c8652>%d</color>" or "<color=#ff0000>%d</color>"
		param = math.min(param, self.data.cfg.task_param1)
		str = string.format(str, string.format(color, param))
	end
	return str
end

function PursuitTaskItemRender:PalyItemAnim(item_index)
    if not self.node_list["tween_root"] then return end
    local wait_index = item_index - 1
    wait_index = wait_index < 0 and 0 or wait_index
    local tween_info = UITween_CONSTS.WorldTreasureView.ListCellRender

    UITween.FakeHideShow(self.node_list["tween_root"])
    ReDelayCall(self, function()
        if self.node_list and self.node_list["tween_root"] then
            UITween.RotateAlphaShow(GuideModuleName.WorldTreasureView, self.node_list["tween_root"], tween_info)
        end
    end, tween_info.NextDoDelay * wait_index, "pursuit_task_item_" .. wait_index)
end
