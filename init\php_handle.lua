PhpHandle = PhpHandle or {}
-- PHP请求结果码
PHP_RESULT_CODE = {
	SUCCESS = 200,					-- 成功
}
PHP_HANDLE_TOKEN_REFRESH_COUNT = 0


local equal_str = "%s=%s"
local key_str = "%s[%s]"
local function IsArray(t)
    if type(t) ~= "table" then
        return false
    end
    
    local len = #t
    if len == 0 then
        return false
    end
    
    for k in pairs(t) do
        if type(k) ~= "number" or k < 1 or k > len then
            return false
        end
    end
    
    return true
end

-- 数据字符串平接
-- 数据按照键名进行升序排序
local function ProcessTable(prefix, t, result)
    if IsArray(t) then
        for i = 1, #t do
            local elem = t[i]
            local new_prefix = string.format(key_str, prefix, (i - 1))
            if type(elem) == "table" then
                ProcessTable(new_prefix, elem, result)
            else
                table.insert(result, string.format(equal_str, new_prefix, tostring(elem)))
            end
        end
    else
        local keys = {}
        for k in pairs(t) do
            table.insert(keys, k)
        end

        table.sort(keys, function(a, b)
            return tostring(a) < tostring(b)
        end)

        for _, k in ipairs(keys) do
            local v = t[k]
            local new_prefix
            if prefix == "" then
                new_prefix = tostring(k)
            else
                new_prefix = string.format(key_str, prefix, tostring(k))
            end

            if type(v) == "table" then
                ProcessTable(new_prefix, v, result)
            else
                table.insert(result, string.format(equal_str, new_prefix, tostring(v)))
            end
        end
    end
end

-- 数据字符串平接
function PhpHandle.GetParamsToStr(tab_data)
    local result = {}
    ProcessTable("", tab_data, result)
    return table.concat(result, "&")
end

-- 获取执行MD5前的签名str
function PhpHandle.GetParamsToSignStr(tab_data, sign_key)
    local result = {}
    ProcessTable("", tab_data, result)

    sign_key = sign_key or GlobalUrlSignKey
    table.insert(result, sign_key)
    return table.concat(result, "&")
end

-- 通用 - 请求php，使用get
-- 目前链接参数 和 签名参数是一致，不排除后续出现不一致的情况，预留2个传参，一致的传参相同值即可
function PhpHandle.HandlePhpGetRequest(url, link_data, sign_data, onSuccess, onFailure)
    if not url then return end

    local sign_key
    if string.find(url, "pay/notify") then
        sign_key = WhitePkgPayKey
    end

    local link_data_str = PhpHandle.GetParamsToStr(link_data)
    local sign_data_str = PhpHandle.GetParamsToSignStr(sign_data, sign_key)
    local md5_sign = MD52.GetMD5(sign_data_str)

    local req_url = string.format("%s?%s&sign=%s", url, link_data_str, md5_sign)
    HttpClient:Request(req_url, function(req_url, isSuccess, responseData)
        -- print_error("---通用 GET----", req_url, link_data, isSuccess, responseData == nil)
        if PHP_HANDLE_TOKEN_REFRESH_COUNT > 5 then
            PHP_HANDLE_TOKEN_REFRESH_COUNT = 0
            PhpHandle.PerformRelogin()
            return
        end

        if not isSuccess then
            if onFailure then onFailure() end
            return
        end

        local decodedData = cjson.decode(responseData)
        -- print_error("---通用 GET   decodedData----", req_url, decodedData)
		
        if not decodedData then
            if onFailure then onFailure(nil) end
            print_error("PHP返回结果数据解析异常  url =", url)
            return
        end

        if decodedData.code ~= PHP_RESULT_CODE.SUCCESS then
            if decodedData.info == PHP_RESULT_INFO.TOKEN_EXPIRED then
                if link_data and link_data.access_token == nil then
                    print_error("数据异常。1、请求没带access_token。2、不需要access_token，但PHP返回结果异常  url =", url)
                    return
                end

                PhpHandle.RefreshTokenAndRetry(false, url, link_data, sign_data, onSuccess, onFailure)
            elseif decodedData.info == PHP_RESULT_INFO.TOKEN_INVALID then
                PhpHandle.PerformRelogin()
            else
                -- PHP数据错误处理函数
                -- 通用
                PhpHandle.HandlePhpDataError(decodedData)
                -- 功能单独 
                if onFailure then onFailure(decodedData) end
            end
        else
            if onSuccess then onSuccess(decodedData) end
        end
    end)
end

-- 通用 - 请求php，使用post， json格式
-- 目前post参数 和 签名参数是一致，不排除后续出现不一致的情况，预留2个传参，一致的传参相同值即可
function PhpHandle.HandlePhpJsonPostRequest(url, post_data, sign_data, onSuccess, onFailure)
    if not url then return end

    local sign_key
    -- 因为后台pay notify会暴露通用的签名key，所以这个key设置为单独
    if string.find(url, "pay/notify") then
        sign_key = WhitePkgPayKey
    end

    -- print_error("---HandlePhpJsonPostRequest  signData", sign_data)
    local sign_data_str = PhpHandle.GetParamsToSignStr(sign_data, sign_key)
    -- print_error("---HandlePhpJsonPostRequest  sign_data_str", sign_data_str)
    local md5_sign = MD52.GetMD5(sign_data_str)
    post_data.sign = md5_sign
    -- print_error("---HandlePhpJsonPostRequest  postData", post_data)
    local jsonData = cjson.encode(post_data)
    -- print_error("---HandlePhpJsonPostRequest  jsonData", url, jsonData)
    HttpClient:RequestJsonPost(url, jsonData, function(isSuccess, responseData)
        -- print_error("---通用 Post----", url, post_data, isSuccess, responseData == nil, type(responseData))
        if not isSuccess then
            if onFailure then onFailure(nil) end
            return
        end

        local decodedData = cjson.decode(responseData)
        -- print_error("---通用 Post  decodedData----", decodedData)
        if not decodedData then
            if onFailure then onFailure(nil) end
            print_error("PHP返回结果数据解析异常  url =", url)
            return
        end

        if decodedData.code ~= PHP_RESULT_CODE.SUCCESS then
            if decodedData.info == PHP_RESULT_INFO.TOKEN_EXPIRED then
                if post_data and post_data.access_token == nil then
                    print_error("数据异常。1、请求没带access_token。2、不需要access_token，但PHP返回结果异常  url =", url)
                    return
                end
                post_data.sign = nil
                sign_data.sign = nil
                PhpHandle.RefreshTokenAndRetry(true, url, post_data, sign_data, onSuccess, onFailure)
            elseif decodedData.info == PHP_RESULT_INFO.TOKEN_INVALID then
                PhpHandle.PerformRelogin()
            else
                -- PHP数据错误处理函数
                -- 通用
                PhpHandle.HandlePhpDataError(decodedData)
                -- 功能单独
                if onFailure then onFailure(decodedData) end
            end
        else
            if onSuccess then onSuccess(decodedData) end
        end
    end)
end

-- Token刷新
function PhpHandle.RefreshTokenAndRetry(is_post, originalUrl, originalData, originalSignData, onSuccess, onFailure)
    PHP_HANDLE_TOKEN_REFRESH_COUNT = PHP_HANDLE_TOKEN_REFRESH_COUNT + 1
    if GameVoManager.Instance == nil then
        return
    end

    local uservo = GameVoManager.Instance:GetUserVo()
    local refreshData = {
        plat_id = CHANNEL_AGENT_ID,
        access_token = uservo.access_token,
    }

    PhpHandle.HandlePhpGetRequest(GLOBAL_CONFIG.api_urls.client.refresh, refreshData, refreshData, function(refreshData)
        if GameVoManager.Instance == nil then
            return
        end
        
        local refreshResult = refreshData.data
        local n_uservo = GameVoManager.Instance:GetUserVo()
        n_uservo.access_token = refreshResult.access_token

        local uid = PlayerPrefsUtil.GetString("cahce_account_user_id")
        PlayerPrefsUtil.SetString("account_token_" .. (uid or ""), refreshResult.access_token)

        -- 使用新的 token 重试原始请求
        originalData.access_token = refreshResult.access_token
        if originalSignData then
            originalSignData.access_token = refreshResult.access_token
        end

        if is_post then
            PhpHandle.HandlePhpJsonPostRequest(originalUrl, originalData, originalSignData, onSuccess, onFailure)
        else
            PhpHandle.HandlePhpGetRequest(originalUrl, originalData, originalSignData, onSuccess, onFailure)
        end
        
    end, BindTool.Bind(PhpHandle.PerformRelogin))
end

-- 实现重新登录逻辑
function PhpHandle.PerformRelogin()
	GlobalEventSystem:Fire(LoginEventType.LOGOUT)
end

-- 通用 - PHP数据错误处理函数
function PhpHandle.HandlePhpDataError(data)
end

