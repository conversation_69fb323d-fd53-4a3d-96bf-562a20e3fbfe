TransFerWGData = TransFerWGData or BaseClass()

--槽位格子的类型
TransFerWGData.SlotType = {
	Skill = 1,
	Equip = 2,
	Item = 3,
}

--槽位格子的类型
TransFerWGData.StageType = {
	Task = 1,
	Star = 2,
	Fb = 3,
}

function TransFerWGData:__init()
	if TransFerWGData.Instance ~= nil then
		ErrorLog("[TransFerWGData] attempt to create singleton twice!")
		return
	end

	TransFerWGData.Instance = self
	--红点显示
	RemindManager.Instance:Register(RemindName.TransFer, BindTool.Bind(self.GetTransFerRemind, self))

	local cfg = ConfigManager.Instance:GetAutoConfig("zhuanzhicfg_auto")
	self.zz_task_cfg = ListToMap(cfg.zhuanzhi_desc,"task_id")
	self.zz_desc_cfg = ListToMapList(cfg.zhuanzhi_desc,"zhuan_num","jieduan")

	self.zhuanzhi_reward_cfg = cfg.zhuanzhi_reward
	self.zhuanzhi_reward_list = ListToMap(self.zhuanzhi_reward_cfg,"zhuanzhi_level")

	self.zhuanzhi_interface_cfg = ListToMapList(cfg.zhuanzhi_interface,"zhuan_num","prof")

	self.zhuanzhi_attr_cfg = ListToMapList(cfg.zhuanzhi_attr,"zhuan_num","jieduan")

	self.stuff_price_cfg = ListToMap(cfg.stuff_price,"itemid")

	
	self.other_cfg	= cfg.other[1]

	self.task_cfg = ListToMapList(cfg.zhuanzhi_task, "zhuanzhi_level", "tab_type")
	self.task_seq_cfg = ListToMap(cfg.zhuanzhi_task, "task_seq")
	self.stage_show_cfg = ListToMap(cfg.stage_show, "prof_level", "stage")
	self.zhuanzhi_light_cfg = ListToMap(cfg.zhuanzhi_light, "zhuanzhi_level", "level")
	self.zhuanzhi_fb_cfg = ListToMap(cfg.zhuanzhi_fb, "prof_level")
	self:InitEquipCollectCfg(cfg)
	
	self.equip_collect_info = {}
	self:RegisterTransferRemindInBag(RemindName.TransFer)
end

function TransFerWGData:__delete()
		--红点显示
	RemindManager.Instance:UnRegister(RemindName.TransFer)

	TransFerWGData.Instance = nil
end

function TransFerWGData:GetOtherCfgByKey(key)
	return self.other_cfg[key]
end

function TransFerWGData:RegisterTransferRemindInBag(remind_name)
	local map = {}

	local light_cfg = ConfigManager.Instance:GetAutoConfig("zhuanzhicfg_auto").zhuanzhi_light
	for k,v in pairs(light_cfg) do
		map[v.need_stuff_id] = true
	end

	local item_id_list = {}
	for k,v in pairs(map) do
		table.insert(item_id_list, k)
	end
	self.item_id_list = map
	BagWGCtrl.Instance:RegisterRemindByItemChange(remind_name, item_id_list, nil)
end

function TransFerWGData:GetItemIdList()
	return self.item_id_list or {}
end

-----------------表数据---------------------------

--- task_cfg

-- 获取当前转职等级需要显示的任务阶段数量
function TransFerWGData:GetTaskCfgTypeNumByLevel(prof_level)
	if self.task_cfg[prof_level] then
		return #self.task_cfg[prof_level]
	end
	return 0
end

-- 获取需要显示的任务列表
function TransFerWGData:GetTaskCfgByLevelAndType(prof_level, type)
	if self.task_cfg[prof_level] and self.task_cfg[prof_level][type] then
		return self.task_cfg[prof_level][type]
	end
	return {}
end

--- stage_show_cfg

-- 获取转职等级的阶段数量
function TransFerWGData:GetStageNumByLevel(prof_level)
	if self.stage_show_cfg[prof_level] then
		return #self.stage_show_cfg[prof_level]
	end
	return 0
end

-- 获取转职等级的阶段数据
function TransFerWGData:GetStageCfgByLevel(prof_level)
	return self.stage_show_cfg[prof_level] or {}
end

-- 获取转职等级的阶段数据
function TransFerWGData:GetStageCfgByLevelAndStage(prof_level, stage)
	if self.stage_show_cfg[prof_level] then
		return self.stage_show_cfg[prof_level][stage] or {}
	end
	return {}
end

--- zhuanzhi_light_cfg
-- 获取转职等级的点亮数据
function TransFerWGData:GetStarCfgByLevel(prof_level)
	return self.zhuanzhi_light_cfg[prof_level] or {}
end

-- 获取转职等级的点亮数据长度
function TransFerWGData:GetStarCfgNumByLevel(prof_level)
	return self.zhuanzhi_light_cfg[prof_level] and #self.zhuanzhi_light_cfg[prof_level] or 0
end

-- 获取转职等级的点亮数据
function TransFerWGData:GetStarCfgByLevelAndStar(prof_level,star_level)
	if self.zhuanzhi_light_cfg[prof_level] then
		return self.zhuanzhi_light_cfg[prof_level][star_level] or {}
	end
	return {}
end

--- zhuanzhi_fb_cfg
-- 获取转职副本
function TransFerWGData:GetProfFbCfg(prof_level)
	if self.zhuanzhi_fb_cfg[prof_level] then
		return self.zhuanzhi_fb_cfg[prof_level]
	end
	print_error("无法获取转职副本配置 传入转职等级",prof_level)
	return nil
end

-------------------------------协议数据-----------------------------
function TransFerWGData:SetTaskInfo(protocol)
	self.task_info_list = protocol.task_info_list
end
-- 获取任务信息
function TransFerWGData:GetTaskInfoBySeq(task_seq)
	if self.task_info_list then
		return self.task_info_list[task_seq] or {}
	end
	return {}
end

function TransFerWGData:SetStarInfo(protocol)
	self.light_level = protocol.light_level
	self.god_and_demons_type = protocol.god_and_demons_type
	-- print_error("self.god_and_demons_type:",self.god_and_demons_type)
end

-- 获取当前点亮等级
function TransFerWGData:GetStarLevel()
	return self.light_level or 0
end

-------------------------------协议数据-----------------------------

-- 判断当前等级是否全部点亮
function TransFerWGData:IsAllLight()
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local cfg_list = self:GetStarCfgByLevel(prof_level+1)
	return self:GetStarLevel() >= #cfg_list
end

-- 判断当前等级是否可点亮
function TransFerWGData:IsCanLight()
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local cfg_list = self:GetStarCfgByLevel(prof_level+1)
	local cur_level = self:GetStarLevel()
	if cur_level < #cfg_list then
		local cfg = cfg_list[cur_level + 1] 
		if cfg then
			local have_num = ItemWGData.Instance:GetItemNumInBagById(cfg.need_stuff_id)
			if have_num >= cfg.need_stuff_num then
				return true
			end
		end
	end
	return false
end

-- 判断当前等级 指定类型任务是否已全部领取
function TransFerWGData:IsAllReceiveByType(type)
	if not self.task_info_list then
		return false
	end
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local task_cfg = self:GetTaskCfgByLevelAndType(prof_level+1, type)
	if task_cfg then
		for key, value in pairs(task_cfg) do
			if not self.task_info_list[value.task_seq] or not self.task_info_list[value.task_seq].has_fetched_reward then
				return false
			end
		end
	end
	return true
end

-- 判断当前等级任务是否已全部领取
function TransFerWGData:IsAllReceive()
	if self.task_info_list then
		for key, value in pairs(self.task_info_list) do
			if not value.has_fetched_reward then
				return false
			end
		end
	end
	return true
end

-- 获取当前未完成的任务
function TransFerWGData:GetNotCompleteTask()
	local role_prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local open_day_limit = self:GetLevelOpenDayLimit(role_prof_level)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
	if open_day_limit > open_day then
		return nil, nil
	end
	if self.task_info_list then
		for key, value in pairs(self.task_info_list) do
			if not value.is_complete and self.task_seq_cfg[value.task_seq] then
				return self.task_seq_cfg[value.task_seq], value
			end
		end
	end

	return nil, nil
end

-- 判断当前等级任务是否可领取
function TransFerWGData:IsCanReceive(stage)
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	if self.task_info_list then
		for key, value in pairs(self.task_info_list) do
			if value.is_complete and not value.has_fetched_reward then --可领取
				if stage then --是需要阶段判断
					local task_cfg = self:GetTaskCfgByLevelAndType(prof_level+1, stage)
					for seq, task in pairs(task_cfg) do
						if task.task_seq == key then
							return true
						end
					end
				else
					return true
				end
			end
		end
	end
	return false
end

-- 判断当前等级副本任务是否完成
function TransFerWGData:IsFinishFb()
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local fb_cfg = self:GetProfFbCfg(prof_level+1)
	if fb_cfg and fb_cfg.task_id_list then
		local task_id_list = Split(fb_cfg.task_id_list, ",", true)
		for index, value in ipairs(task_id_list) do
			local task_id = tonumber(value)
			if not TaskWGData.Instance:GetTaskIsCompleted(task_id) then
				return false
			end
		end
	end

	return true
end

function TransFerWGData:GetFbTask()
	if not self:IsFinishFb() then
		local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
		local role_level = GameVoManager.Instance:GetMainRoleVo().level
		local fb_cfg = self:GetProfFbCfg(prof_level + 1)

		local open_day_limit = self:GetLevelOpenDayLimitEnd(prof_level +1 )
		local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		if open_day_limit > open_day then
			return 0
		end
		
		if fb_cfg and role_level >= fb_cfg.level_limit and self:IsAllLight() and self:IsAllReceive() then
			return self:GetTaskTransferFbCfgTaskId()
		end
	end

	return 0
end

-- 获取阶段完成情况
function TransFerWGData:IsStageFinish(stage_show_cfg)
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	if prof_level >= stage_show_cfg.prof_level then
		return true
	end
	if stage_show_cfg.type == TransFerWGData.StageType.Task then
		return self:IsAllReceiveByType(stage_show_cfg.stage)
		
	elseif stage_show_cfg.type == TransFerWGData.StageType.Star then
		return self:IsAllLight()
	elseif stage_show_cfg.type == TransFerWGData.StageType.Fb then
		return false
	end
end

-- 是否完成 进度 最大阶段
function TransFerWGData:IsAllFinish()
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local stage_cfg =  self:GetStageCfgByLevel(prof_level + 1)
	for key, value in ipairs(stage_cfg) do
		if not self:IsStageFinish(value) then
			return false, key - 1, #stage_cfg
		end
	end
	return true, #stage_cfg, #stage_cfg
end

-- 获取当前转职 副本阶段待做的任务
function TransFerWGData:GetTaskTransferFbCfgTaskId()
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local fb_cfg = TransFerWGData.Instance:GetProfFbCfg(prof_level + 1)
	if not fb_cfg then
		return 0
	end

	local task_id_list = Split(fb_cfg.task_id_list, ",", true)
	for index, value in ipairs(task_id_list) do
		local task_id = tonumber(value)
		if not TaskWGData.Instance:GetTaskIsCompleted(task_id) then
			return task_id
		end
	end
	return 0
end

-- 获取一键点亮需要消耗的灵玉
function TransFerWGData:GetAllLightNeedGold()
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local cur_star_level = self:GetStarLevel()
	local star_cfg = TransFerWGData.Instance:GetStarCfgByLevel(prof_level + 1)
	local gold = 0
	if star_cfg then
		local need_item = {}
		for index, value in pairs(star_cfg) do
			if index~=#star_cfg and cur_star_level <= value.level then
				if need_item[value.need_stuff_id] then
					need_item[value.need_stuff_id] = need_item[value.need_stuff_id] + value.need_stuff_num
				else
					need_item[value.need_stuff_id] = value.need_stuff_num
				end
			end
		end
	
		for key, value in pairs(need_item) do
			local have_num = ItemWGData.Instance:GetItemNumInBagById(key)
			local price_cfg = TransFerWGData.Instance:GetStuffPriceCfg(key)
			local need_cost_gold = price_cfg and price_cfg.gold or 0
			if value > have_num then
				gold = gold + need_cost_gold * (value - have_num)
			end
		end
	end
	return gold
end

-------------------------------红点-----------------------------
function TransFerWGData:GetTransFerRemind()
	-- 是否开启
	local is_open = FunOpen.Instance:GetFunIsOpened(GuideModuleName.ZhuanSheng)
	if not is_open then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TRANSFER, 0)
		return 0
	end

	-- 是否满转职
	local prof_level = RoleWGData.Instance:GetZhuanZhiNumber()
	local max_level = self:GetTransFerRewardCfgMax()
	if prof_level >= max_level then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TRANSFER, 0)
		return 0
	end

	-- 是否达到下个转职的开启等级
	local role_level = GameVoManager.Instance:GetMainRoleVo().level
	local level_cfg = self:GetTransFerSingleRewardCfg(prof_level + 1)
	if role_level < level_cfg.open_level then
		MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TRANSFER, 0)
		return 0
	end

	-- 需要同步修改 ZhuanShengStageItemRender的红点显示
	local data_list = self:GetStageCfgByLevel(prof_level + 1)
	local open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()

	for index, value in ipairs(data_list) do
		if role_level >= value.open_level and open_day >= value.open_day then
			if value.type == TransFerWGData.StageType.Task then
				-- 是否可领取
				if self:IsCanReceive(value.stage) then
					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TRANSFER, 1,function()
						ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
					end)
					
					return 1
				end
			elseif value.type == TransFerWGData.StageType.Star then
				-- 是否可点亮
				if self:IsCanLight() then
					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TRANSFER, 1,function()
						ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
					end)
					return 1
				end
			elseif value.type == TransFerWGData.StageType.Fb then
				-- 是否可转职
				if self:IsFinishFb() then
					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TRANSFER, 1,function()
						ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
					end)
					return 1
				end
				local fb_cfg = self:GetProfFbCfg(prof_level + 1)
				local role_zhanli = RoleWGData.Instance:GetRoleVo().capability
				if fb_cfg and role_level >= fb_cfg.level_limit  and  self:IsAllLight() and self:IsAllReceive() and DujieWGData.Instance:IsCanDujie() then
					MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TRANSFER, 1,function()
						ViewManager.Instance:Open(GuideModuleName.ZhuanSheng)
					end)
					return 1
				end
			end
		end
		
	end
	MainuiWGCtrl.Instance:InvateTip(MAINUI_TIP_TYPE.TRANSFER, 0)
	return 0
end

function TransFerWGData:GetLevelOpenDayLimit(level)
	if self.stage_show_cfg[level] and self.stage_show_cfg[level][1] then
		return self.stage_show_cfg[level][1].open_day

	end
	return 0
end

function TransFerWGData:GetLevelOpenDayLimitEnd(level)
	if self.stage_show_cfg[level]  then
		local max = self:GetStageNumByLevel(level)
		if self.stage_show_cfg[level][max] then
			return self.stage_show_cfg[level][max].open_day
		end
	end
	return 0
end


-------------------------------红点-----------------------------


function TransFerWGData:GetZhuanzhiReward(prof)
	for k,v in pairs(self.zhuanzhi_reward_cfg) do
		if v.zhuanzhi_level == prof then
			return v.reward_item
		end
	end
	return {}
end


--获取转职阶段配置
function TransFerWGData:GetZhunaZhiDescCfg(zhuan_num,jieduan)
	return self.zz_desc_cfg and self.zz_desc_cfg[zhuan_num] and self.zz_desc_cfg[zhuan_num][jieduan]
end

function TransFerWGData:GetZhunaZhiDescCfgBuZhuanNum(zhuan_num)
	return self.zz_desc_cfg and self.zz_desc_cfg[zhuan_num]
end

--功能解锁数据
function TransFerWGData:GetFaceCfgByZhuanNum(zhuan_num)
	local role_prof = RoleWGData.Instance:GetRoleProf()
	local cfg = self.zhuanzhi_interface_cfg[zhuan_num] and self.zhuanzhi_interface_cfg[zhuan_num][role_prof]
	local normal_cfg = {}
	local normal_child = {}
	if cfg then
		-- return cfg
		-- 合并显示  不判断slot_subtype
		for k, v in pairs(cfg) do
			if v.slot_subtype == 1 then
				normal_child[v] = v
				table.insert(normal_cfg, normal_child[v])
			end
		end
	end
	-- return {}
	return normal_cfg
end

--技能解锁数据
function TransFerWGData:GetSkillCfgByZhuanNum(zhuan_num)
	local role_prof = RoleWGData.Instance:GetRoleProf()
	local cfg = self.zhuanzhi_interface_cfg[zhuan_num] and self.zhuanzhi_interface_cfg[zhuan_num][role_prof]
	local normal_cfg = {}
	local normal_child = {}
	if cfg then
		for k, v in pairs(cfg) do
			if v.slot_subtype == 2 then
				normal_child[v] = v
				table.insert(normal_cfg, normal_child[v])
			end
		end
	end
	
	return normal_cfg
end

--转职奖励数据
function TransFerWGData:GetFaceCfgByZhuanFunction(zhuan_num)
	local role_prof = RoleWGData.Instance:GetRoleProf()
	local cfg = self.zhuanzhi_interface_cfg[zhuan_num] and self.zhuanzhi_interface_cfg[zhuan_num][role_prof]
	local function_cfg = {}
	local function_cfg_child = {}
	if cfg then
		for k, v in pairs(cfg) do
			if v.slot_subtype == 2 and v.slot_type == 3 or v.slot_type == 1 then
				function_cfg_child[v] = v
				table.insert(function_cfg, function_cfg_child[v])
			end
		end
	end
	
	return function_cfg
end

function TransFerWGData:GetSkillCfgByZhuanFunction(zhuan_num)
	local role_prof = RoleWGData.Instance:GetRoleProf()
	local cfg = self.zhuanzhi_interface_cfg[zhuan_num] and self.zhuanzhi_interface_cfg[zhuan_num][role_prof]
	local function_cfg = {}
	local function_cfg_child = {}
	if cfg then
		for k, v in pairs(cfg) do
			if v.slot_subtype == 2 and v.slot_type == 1 then
				function_cfg_child[v] = v
				table.insert(function_cfg, function_cfg_child[v])
			end
		end
	end
	
	return function_cfg
end

function TransFerWGData:GetAttrCfgByZhuanNum(zhuan_num,stage)
	local attr_data = {}
	local cfg_list = self.zhuanzhi_attr_cfg[zhuan_num] and self.zhuanzhi_attr_cfg[zhuan_num][stage] or {}
	if not IsEmptyTable(cfg_list) then
		attr_data = AttributeMgr.GetAttributteByClass(cfg_list[#cfg_list])
	end

	return attr_data
end


-- 获取转职相关物品单价
function TransFerWGData:GetStuffPriceCfg(item_id)
	return self.stuff_price_cfg[item_id]
end


function TransFerWGData:GetRoleProfNameCfg(zhuan_num, role_prof)
	local sex = RoleWGData.Instance.role_vo.sex
	local prof_name_list = ConfigManager.Instance:GetAutoConfig("zhuanzhicfg_auto").prof_name
	for k,v in pairs(prof_name_list) do
		if v.prof == role_prof and zhuan_num == v.zhuan_num and v.sex == sex then
			return v.prof_name
		end
	end
	return ""
end

function TransFerWGData:GetRoleProfLevelName(level)
	local cfg = self:GetTransFerSingleRewardCfg(level)
	if cfg and cfg.level_name then
		return cfg.level_name
	end
	return ""
end

function TransFerWGData:GetZhuanZhiLastTaskId(task_id)
	if not self.zhuanzhi_end_task_id_list then
		self.zhuanzhi_end_task_id_list = {}
		local task_list = TaskWGData.Instance:GetZhuanzhiCfg()
		for k,v in pairs(task_list) do
			table.insert(self.zhuanzhi_end_task_id_list, v.end_task)
		end
	end
	for k,v in pairs(self.zhuanzhi_end_task_id_list) do
		if v == task_id then
			return true
		end
	end
	return false
end

function TransFerWGData:GetZhuanZhiEndTaskIsComplete()
	local zhuan_num = RoleWGData.Instance:GetZhuanZhiNumber()
	local task_list = TaskWGData.Instance:GetZhuanzhiCfg()
	local task_cfg = task_list[zhuan_num]
	if task_cfg then
		return TaskWGData.Instance:GetTaskIsCompleted(task_cfg.end_task)
	end
	return false
end

function TransFerWGData:GetCurTaskCanCommit()
	local prof_zhuan = RoleWGData.Instance:GetZhuanZhiNumber()
	prof_zhuan = prof_zhuan >= self:GetTransFerRewardCfgMax() and prof_zhuan or prof_zhuan + 1
    local desc_cfg_list = self:GetZhunaZhiDescCfg(prof_zhuan, 1)
    if not IsEmptyTable(desc_cfg_list) then
	    local level = RoleWGData.Instance:GetAttr('level')
	    for k,v in pairs(desc_cfg_list) do
			local is_complete = TaskWGData.Instance:GetTaskIsCompleted(v.task_id)
			local task_cfg = TaskWGData.Instance:GetTaskConfig(v.task_id)
			if task_cfg then
				local task_info = TaskWGData.Instance:GetTaskInfo(task_cfg.task_id)
	    		local progress_num = 0
				if task_info then
					progress_num = task_info.progress_num
				end
	    		local can_commit = not is_complete and progress_num >= task_cfg.c_param2 and level >= task_cfg.min_level
				if can_commit then
		    		return true
		    	end
			end
		end
    end

    return false
end

function TransFerWGData:GetTransFerRewardCfg()
	return self.zhuanzhi_reward_list or {}
end

-- 获取转职最大等级
function TransFerWGData:GetTransFerRewardCfgMax()
	return self.zhuanzhi_reward_list and #self.zhuanzhi_reward_list or 0
end

function TransFerWGData:GetTransFerSingleRewardCfg(index)
	return self.zhuanzhi_reward_list[index]
end

-- 获取转职奖励(判断神魔)
function TransFerWGData:GetTransFerSingleRewardList(index, is_star_one)
	local cfg = self.zhuanzhi_reward_list[index]
	if IsEmptyTable(cfg) then
		return {}
	end
	if is_star_one then
		local reward_list =  (self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.GOD and cfg.reward_item0 or cfg.reward_item1) or {}
		local new_reward_list = {}
		for k, v in pairs(reward_list) do
			table.insert(new_reward_list,v)
		end
		return new_reward_list
	else
		return (self.god_and_demons_type == ZHUANZHI_GOD_AND_DEMONS_TYPE.GOD and cfg.reward_item0 or cfg.reward_item1) or {}
	end
	
end

-- 获取神魔状态
function TransFerWGData:GetGodAndDemonsType()
	return self.god_and_demons_type or -1
end




------------------------------装备收集---------------------
local VIP_BOSS_SCENE_ID_0 = 1360
local VIP_BOSS_SCENE_ID_1 = 1361
local MAX_EQUIP_SUIT_NUM = 4

local equip_id_type = {
    [0] = {
        [1] = 4,
        [2] = 8,
        [3] = 5,
        [4] = 7,
    },

    [1] = {
        [1] = 1,
        [2] = 2,
        [3] = 3,
        [4] = 6,
    }
}

local equip_type_name = {
    [1] = "equip_1_1_id",
    [2] = "equip_1_2_id",
    [3] = "equip_1_3_id",
    [4] = "equip_0_1_id",
    [5] = "equip_0_3_id",
    [6] = "equip_1_4_id",
    [7] = "equip_0_4_id",
    [8] = "equip_0_2_id",
}

function TransFerWGData:SetAllEquipCollectInfo(protocol)
	self.equip_collect_info = protocol.equip_collect_info
end

function TransFerWGData:UpdateEquipCollectInfo(protocol)
	local suit_index = protocol.suit_index
	local change_data = protocol.change_data

	if self.equip_collect_info[suit_index] then
		self.equip_collect_info[suit_index] = change_data
	end
end

function TransFerWGData:GetEquipCollectInfoByIndex(suit_index, part_index)
	if part_index then
		return (self.equip_collect_info[suit_index] or {})[part_index]
	else
		return self.equip_collect_info[suit_index]
	end
end

function TransFerWGData:GetActiveNumAndMax(suit_index)
	local max_count = 0
	local active_count = 0
	local item_list = self:GetEquipItemListBySuitIndex(suit_index)
	local start_list = self:GetEquipStarListBySuitIndex(suit_index)
	for i, v in ipairs(item_list) do
		local item_id = tonumber(v) or 0
		if item_id > 0 then
			max_count = max_count + 1

			local equip_collect_info = TransFerWGData.Instance:GetEquipCollectInfoByIndex(suit_index, i)
			local cur_star = equip_collect_info and equip_collect_info.item_star or -1
			local cur_item_id = equip_collect_info and equip_collect_info.item_id or 0

			local need_star = tonumber(start_list[i])
			if cur_star >= need_star then
				active_count = active_count + 1
			end
		end
	end
	return active_count,max_count
end

function TransFerWGData:InitEquipCollectCfg(all_cfg)
	self.equip_collect_cfg = ListToMap(all_cfg.equip_collect, "suit_index")
	self.equip_star_list = {}
	self.equip_item_list = {}
	self.max_show_level = 0
    for i = 0, MAX_EQUIP_SUIT_NUM - 1 do
		local suit_cfg = self.equip_collect_cfg[i]
		if suit_cfg then
			self.equip_star_list[i] = Split(suit_cfg.equip_star, "|")
			self.max_show_level = math.max(self.max_show_level, suit_cfg.open_level or 0)
		end
    end

	for sex = GameEnum.FEMALE, GameEnum.MALE do
        self.equip_item_list[sex] = {}
		for prof = GameEnum.ROLE_PROF_1, GameEnum.ROLE_PROF_4 do
			self.equip_item_list[sex][prof] = {}
			local equip_id_type = equip_id_type[sex] and equip_id_type[sex][prof]
            local equip_type_name = equip_type_name[equip_id_type]
			if equip_type_name then
				for k,v in pairs(self.equip_collect_cfg) do
                    self.equip_item_list[sex][prof][v.suit_index] = Split(v[equip_type_name], "|")
                end
			end
		end
	end
end

function TransFerWGData:GetAllEquipCollectCfg()
	return self.equip_collect_cfg
end

function TransFerWGData:GetEquipCollectCfgBySuit(suit)
	return self.equip_collect_cfg[suit]
end

function TransFerWGData:GetEquipCollectSuitShowMaxLevel()
	return self.max_show_level
end

function TransFerWGData:GetCurShowSuitIndex()
	local role_level = RoleWGData.Instance:GetRoleLevel()
	local show_suit_index = -1

	for i = 0, MAX_EQUIP_SUIT_NUM - 1 do
		local suit_cfg = self.equip_collect_cfg[i]
		if suit_cfg and role_level >= suit_cfg.open_level then
			local suit_part_info = self:GetEquipCollectInfoByIndex(i)
			local suit_star_list = self.equip_star_list[i]
			for k, v in ipairs(suit_star_list) do
				local part_item_star = suit_part_info and suit_part_info[k] and suit_part_info[k].item_star or -1
				local need_star = tonumber(v)
				local is_need_task = false
				if suit_cfg.task_id and suit_cfg.task_id ~=0 and suit_cfg.task_id~= "" and not TaskWGData.Instance:GetTaskIsCompleted(suit_cfg.task_id) then
					is_need_task = true
				end
				if (part_item_star < need_star and need_star > 0) or is_need_task then
					show_suit_index = i
					return show_suit_index
				end
			end
		end
    end

	return show_suit_index
end

function TransFerWGData:GetSuitCollectProgress(suit)
	local all_num = 0
	local progress_num = 0
	local suit_part_info = self:GetEquipCollectInfoByIndex(suit)
	local suit_star_list = self.equip_star_list[suit]
	for k, v in ipairs(suit_star_list) do
		local part_item_star = suit_part_info[k] and suit_part_info[k].item_star or -1
		local need_star = tonumber(v)
		if need_star > 0 then
			all_num = all_num + 1
			if part_item_star >= need_star then
				progress_num = progress_num + 1
			end
		end
	end

	return all_num, progress_num
end

function TransFerWGData:GetEquipItemListBySuitIndex(suit_index)
    local sex = RoleWGData.Instance:GetRoleSex()
    local prof = RoleWGData.Instance:GetRoleProf()
    
	return ((self.equip_item_list[sex] or {})[prof] or {})[suit_index]
end

function TransFerWGData:GetEquipStarListBySuitIndex(suit_index)
	return self.equip_star_list[suit_index]
end

function TransFerWGData:IsVIPBossScene()
    local cur_scene_id = Scene.Instance:GetSceneId() or 1-- 当前场景id
	if cur_scene_id == VIP_BOSS_SCENE_ID_0 or cur_scene_id == VIP_BOSS_SCENE_ID_1 then
		return true
	end

	return false
end