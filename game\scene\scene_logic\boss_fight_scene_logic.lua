BossFightSceneLogic = BossFightSceneLogic or BaseClass(CommonFbLogic)

function BossFightSceneLogic:__init()
	self.old_scene = nil
	self.new_scene = nil
end

function BossFightSceneLogic:__delete()
	self.old_scene = nil
	self.new_scene = nil
end

function BossFightSceneLogic:Enter( old_scene_type, new_scene_type )
	CommonFbLogic.Enter(self, old_scene_type, new_scene_type)

	MainuiWGCtrl.Instance:AddInitCallBack(nil, function()
		MainuiWGCtrl.Instance:SetTaskPanel(false)
		MainuiWGCtrl.Instance:SetTeamBtnState(false)
		MainuiWGCtrl.Instance:SetFBNameState(true, Language.ActBossFight.BossFightTitle)
	end)

	ViewManager.Instance:Open(GuideModuleName.BossFightSceneView)

	local main_role = Scene.Instance:GetMainRole()
	-- self.before_act_mode = main_role:GetVo().attack_mode
	-- MainuiWGCtrl.Instance:SendSetAttackMode(ATTACK_MODE.NAMECOLOR)
end

function BossFightSceneLogic:OnObjCreate(obj)
	if obj and not SceneObj.select_obj and self:IsEnemy(obj) then
		GlobalEventSystem:Fire(ObjectEventType.BE_SELECT, obj, SceneTargetSelectType.SELECT)
	end
end

function BossFightSceneLogic:Out()
	CommonFbLogic.Out(self)

	MainuiWGCtrl.Instance:ResetTaskPanel()
	MainuiWGCtrl.Instance:SetTeamBtnState(true)
	MainuiWGCtrl.Instance:SetFBNameState(false)

	ViewManager.Instance:Close(GuideModuleName.BossFightSceneView)
	-- if self.before_act_mode ~= nil then
	-- 	MainuiWGCtrl.Instance:SendSetAttackMode(self.before_act_mode)
	-- end

	if self.create_obj_event then
		GlobalEventSystem:UnBind(self.create_obj_event)
		self.create_obj_event = nil
	end
end


-- 获取挂机打怪的敌人(优先级： 优先打角色，如果点击前往击杀则优先打BOSS)
function BossFightSceneLogic:GetGuajiCharacter()
	local target_obj
	target_obj = self:GetNormalRole()
	if target_obj ~= nil then
		return target_obj
	end
	if target_obj == nil then
		return self:GetMonster()
	end
end

function BossFightSceneLogic:GetNormalRole()
	local role_list = Scene.Instance:GetRoleList()
	for k,v in pairs(role_list) do
		if self:IsEnemy(v) then
			GuajiCache.target_obj = v
			return v
		end
	end
end

function BossFightSceneLogic:GetMonster()
	local distance_limit = COMMON_CONSTS.SELECT_OBJ_DISTANCE * COMMON_CONSTS.SELECT_OBJ_DISTANCE
	local main_role = Scene.Instance:GetMainRole()
	local obj = nil
	if main_role ~= nil then
		local x, y = main_role:GetLogicPos()
		obj = Scene.Instance:SelectObjHelper(SceneObjType.Monster, x, y, distance_limit, SelectType.Enemy)
	end
	
	return obj
end

function BossFightSceneLogic:IsMonsterEnemy( target_obj, main_role )
	-- local info = BossWGData.Instance:GetSecretBossInfoByBossId(target_obj:GetVo().monster_id)
	-- local physical = BossWGData.Instance:GetNowOwnPower()
	-- if physical < info.kill_consume_physical then
	-- 	return false, Language.Boss.NoEnoughPower
	-- end
	return true
end